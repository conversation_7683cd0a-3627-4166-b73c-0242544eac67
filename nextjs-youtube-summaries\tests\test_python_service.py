"""
Test script for the Python service embedding endpoint
"""
import requests
import json

def test_python_service():
    """Test the /embeddings endpoint on the Python service"""
    url = "http://localhost:9000/embeddings"
    
    test_data = {
        "text": "This is a test sentence for embedding generation"
    }
    
    try:
        print("Testing Python service at http://localhost:9000/embeddings")
        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer dev-key-123'
        }
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Status: {response.status_code}")
            print(f"Embedding dimension: {len(result.get('embedding', []))}")
            print(f"Text processed: {result.get('text', 'N/A')}")
            return True
        else:
            print(f"❌ Error! Status: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure the Python service is running on port 9000")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_python_service()
