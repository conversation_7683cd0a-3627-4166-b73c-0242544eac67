WEBVTT
Kind: captions
Language: en

00:00:00.320 --> 00:00:02.510 align:start position:0%
 
hi<00:00:00.520><c> this</c><00:00:00.640><c> is</c><00:00:00.719><c> floran</c><00:00:01.160><c> bers</c><00:00:01.520><c> with</c><00:00:01.719><c> GitHub</c><00:00:02.360><c> and</c>

00:00:02.510 --> 00:00:02.520 align:start position:0%
hi this is floran bers with GitHub and
 

00:00:02.520 --> 00:00:04.190 align:start position:0%
hi this is floran bers with Git<PERSON><PERSON> and
I'm<00:00:02.639><c> going</c><00:00:02.800><c> to</c><00:00:02.960><c> show</c><00:00:03.199><c> you</c><00:00:03.439><c> how</c><00:00:03.600><c> to</c><00:00:03.879><c> access</c>

00:00:04.190 --> 00:00:04.200 align:start position:0%
I'm going to show you how to access
 

00:00:04.200 --> 00:00:06.389 align:start position:0%
I'm going to show you how to access
GitHub<00:00:04.600><c> co-pilot</c><00:00:05.160><c> using</c><00:00:05.440><c> the</c><00:00:05.759><c> vs</c><00:00:06.160><c> code</c>

00:00:06.389 --> 00:00:06.399 align:start position:0%
GitHub co-pilot using the vs code
 

00:00:06.399 --> 00:00:08.870 align:start position:0%
GitHub co-pilot using the vs code
command<00:00:06.879><c> pallet</c><00:00:07.640><c> I</c><00:00:07.720><c> will</c><00:00:07.839><c> be</c><00:00:08.000><c> using</c><00:00:08.200><c> mvda</c><00:00:08.639><c> 202</c>

00:00:08.870 --> 00:00:08.880 align:start position:0%
command pallet I will be using mvda 202
 

00:00:08.880 --> 00:00:11.669 align:start position:0%
command pallet I will be using mvda 202
24.1<00:00:09.639><c> on</c><00:00:09.800><c> Windows</c><00:00:10.120><c> 11</c><00:00:10.719><c> using</c><00:00:11.040><c> the</c><00:00:11.240><c> GitHub</c>

00:00:11.669 --> 00:00:11.679 align:start position:0%
24.1 on Windows 11 using the GitHub
 

00:00:11.679 --> 00:00:14.190 align:start position:0%
24.1 on Windows 11 using the GitHub
co-pilot<00:00:12.280><c> extension</c><00:00:13.000><c> with</c><00:00:13.120><c> nvs</c><00:00:13.599><c> code</c><00:00:13.880><c> version</c>

00:00:14.190 --> 00:00:14.200 align:start position:0%
co-pilot extension with nvs code version
 

00:00:14.200 --> 00:00:17.630 align:start position:0%
co-pilot extension with nvs code version
1.88<00:00:15.080><c> which</c><00:00:15.200><c> is</c><00:00:15.320><c> the</c><00:00:15.440><c> March</c><00:00:16.080><c> 2024</c><00:00:16.840><c> release</c><00:00:17.520><c> in</c>

00:00:17.630 --> 00:00:17.640 align:start position:0%
1.88 which is the March 2024 release in
 

00:00:17.640 --> 00:00:19.510 align:start position:0%
1.88 which is the March 2024 release in
the<00:00:17.800><c> command</c><00:00:18.119><c> pallet</c><00:00:18.640><c> we</c><00:00:18.760><c> have</c><00:00:19.000><c> convenient</c>

00:00:19.510 --> 00:00:19.520 align:start position:0%
the command pallet we have convenient
 

00:00:19.520 --> 00:00:21.710 align:start position:0%
the command pallet we have convenient
filterable<00:00:20.160><c> access</c><00:00:20.480><c> to</c><00:00:20.720><c> every</c><00:00:21.000><c> command</c><00:00:21.359><c> vs</c>

00:00:21.710 --> 00:00:21.720 align:start position:0%
filterable access to every command vs
 

00:00:21.720 --> 00:00:23.710 align:start position:0%
filterable access to every command vs
code<00:00:22.039><c> is</c><00:00:22.240><c> able</c><00:00:22.519><c> to</c><00:00:22.680><c> run</c><00:00:23.000><c> in</c><00:00:23.160><c> the</c><00:00:23.320><c> current</c>

00:00:23.710 --> 00:00:23.720 align:start position:0%
code is able to run in the current
 

00:00:23.720 --> 00:00:25.910 align:start position:0%
code is able to run in the current
context<00:00:24.560><c> this</c><00:00:24.720><c> list</c><00:00:24.960><c> will</c><00:00:25.160><c> show</c><00:00:25.439><c> off</c><00:00:25.720><c> the</c>

00:00:25.910 --> 00:00:25.920 align:start position:0%
context this list will show off the
 

00:00:25.920 --> 00:00:27.790 align:start position:0%
context this list will show off the
hotkeys<00:00:26.679><c> configured</c><00:00:27.160><c> to</c><00:00:27.279><c> trigger</c><00:00:27.640><c> these</c>

00:00:27.790 --> 00:00:27.800 align:start position:0%
hotkeys configured to trigger these
 

00:00:27.800 --> 00:00:30.029 align:start position:0%
hotkeys configured to trigger these
commands<00:00:28.320><c> if</c><00:00:28.480><c> available</c><00:00:29.160><c> and</c><00:00:29.320><c> allow</c><00:00:29.560><c> us</c><00:00:29.679><c> to</c>

00:00:30.029 --> 00:00:30.039 align:start position:0%
commands if available and allow us to
 

00:00:30.039 --> 00:00:31.990 align:start position:0%
commands if available and allow us to
activate<00:00:30.480><c> the</c><00:00:30.599><c> command</c><00:00:31.000><c> in</c><00:00:31.240><c> question</c><00:00:31.840><c> right</c>

00:00:31.990 --> 00:00:32.000 align:start position:0%
activate the command in question right
 

00:00:32.000 --> 00:00:33.869 align:start position:0%
activate the command in question right
from<00:00:32.239><c> the</c><00:00:32.480><c> command</c><00:00:32.920><c> pallet</c><00:00:33.320><c> there's</c><00:00:33.559><c> a</c><00:00:33.680><c> few</c>

00:00:33.869 --> 00:00:33.879 align:start position:0%
from the command pallet there's a few
 

00:00:33.879 --> 00:00:35.670 align:start position:0%
from the command pallet there's a few
ways<00:00:34.120><c> to</c><00:00:34.280><c> bring</c><00:00:34.520><c> up</c><00:00:34.640><c> the</c><00:00:34.800><c> command</c><00:00:35.160><c> pallet</c><00:00:35.559><c> but</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
ways to bring up the command pallet but
 

00:00:35.680 --> 00:00:38.030 align:start position:0%
ways to bring up the command pallet but
the<00:00:35.840><c> easiest</c><00:00:36.280><c> way</c><00:00:36.440><c> is</c><00:00:36.520><c> to</c><00:00:36.719><c> Simply</c><00:00:37.079><c> press</c><00:00:37.320><c> F1</c><00:00:37.840><c> on</c>

00:00:38.030 --> 00:00:38.040 align:start position:0%
the easiest way is to Simply press F1 on
 

00:00:38.040 --> 00:00:40.310 align:start position:0%
the easiest way is to Simply press F1 on
your<00:00:38.239><c> keyboard</c><00:00:39.040><c> next</c><00:00:39.360><c> we</c><00:00:39.480><c> can</c><00:00:39.640><c> search</c><00:00:39.920><c> for</c><00:00:40.120><c> a</c>

00:00:40.310 --> 00:00:40.320 align:start position:0%
your keyboard next we can search for a
 

00:00:40.320 --> 00:00:42.350 align:start position:0%
your keyboard next we can search for a
command<00:00:40.760><c> by</c><00:00:40.920><c> typing</c><00:00:41.200><c> a</c><00:00:41.320><c> search</c><00:00:41.640><c> term</c><00:00:42.160><c> let's</c>

00:00:42.350 --> 00:00:42.360 align:start position:0%
command by typing a search term let's
 

00:00:42.360 --> 00:00:44.510 align:start position:0%
command by typing a search term let's
show<00:00:42.640><c> this</c><00:00:42.800><c> off</c><00:00:43.160><c> by</c><00:00:43.280><c> using</c><00:00:43.600><c> the</c><00:00:43.760><c> search</c><00:00:44.079><c> term</c>

00:00:44.510 --> 00:00:44.520 align:start position:0%
show this off by using the search term
 

00:00:44.520 --> 00:00:47.069 align:start position:0%
show this off by using the search term
co-pilot<00:00:45.440><c> providing</c><00:00:45.840><c> the</c><00:00:46.039><c> GitHub</c><00:00:46.440><c> co-pilot</c>

00:00:47.069 --> 00:00:47.079 align:start position:0%
co-pilot providing the GitHub co-pilot
 

00:00:47.079 --> 00:00:49.189 align:start position:0%
co-pilot providing the GitHub co-pilot
and<00:00:47.239><c> GitHub</c><00:00:47.600><c> co-pilot</c><00:00:48.120><c> chat</c><00:00:48.480><c> extensions</c><00:00:48.960><c> for</c>

00:00:49.189 --> 00:00:49.199 align:start position:0%
and GitHub co-pilot chat extensions for
 

00:00:49.199 --> 00:00:51.510 align:start position:0%
and GitHub co-pilot chat extensions for
vs<00:00:49.559><c> code</c><00:00:49.840><c> are</c><00:00:50.000><c> installed</c><00:00:50.399><c> and</c><00:00:50.600><c> enabled</c><00:00:51.360><c> this</c>

00:00:51.510 --> 00:00:51.520 align:start position:0%
vs code are installed and enabled this
 

00:00:51.520 --> 00:00:53.549 align:start position:0%
vs code are installed and enabled this
will<00:00:51.719><c> list</c><00:00:51.960><c> a</c><00:00:52.160><c> number</c><00:00:52.440><c> of</c><00:00:52.680><c> commands</c><00:00:53.239><c> we</c><00:00:53.399><c> can</c>

00:00:53.549 --> 00:00:53.559 align:start position:0%
will list a number of commands we can
 

00:00:53.559 --> 00:00:55.590 align:start position:0%
will list a number of commands we can
run<00:00:54.239><c> features</c><00:00:54.680><c> include</c><00:00:55.000><c> bringing</c><00:00:55.320><c> up</c><00:00:55.480><c> the</c>

00:00:55.590 --> 00:00:55.600 align:start position:0%
run features include bringing up the
 

00:00:55.600 --> 00:00:58.150 align:start position:0%
run features include bringing up the
inline<00:00:56.039><c> chat</c><00:00:56.399><c> panel</c><00:00:56.800><c> or</c><00:00:57.000><c> the</c><00:00:57.199><c> GitHub</c><00:00:57.600><c> co-pilot</c>

00:00:58.150 --> 00:00:58.160 align:start position:0%
inline chat panel or the GitHub co-pilot
 

00:00:58.160 --> 00:01:00.430 align:start position:0%
inline chat panel or the GitHub co-pilot
chat<00:00:58.440><c> view</c><00:00:59.079><c> but</c><00:00:59.239><c> also</c><00:00:59.600><c> pre</c><00:01:00.000><c> packag</c>

00:01:00.430 --> 00:01:00.440 align:start position:0%
chat view but also pre packag
 

00:01:00.440 --> 00:01:02.790 align:start position:0%
chat view but also pre packag
instructions<00:01:01.079><c> like</c><00:01:01.480><c> explain</c><00:01:01.920><c> this</c><00:01:02.120><c> code</c><00:01:02.640><c> and</c>

00:01:02.790 --> 00:01:02.800 align:start position:0%
instructions like explain this code and
 

00:01:02.800 --> 00:01:04.910 align:start position:0%
instructions like explain this code and
generate<00:01:03.280><c> documentation</c><00:01:04.239><c> these</c><00:01:04.519><c> take</c><00:01:04.760><c> the</c>

00:01:04.910 --> 00:01:04.920 align:start position:0%
generate documentation these take the
 

00:01:04.920 --> 00:01:06.910 align:start position:0%
generate documentation these take the
currently<00:01:05.400><c> highlighted</c><00:01:05.920><c> code</c><00:01:06.400><c> and</c><00:01:06.560><c> do</c>

00:01:06.910 --> 00:01:06.920 align:start position:0%
currently highlighted code and do
 

00:01:06.920 --> 00:01:08.670 align:start position:0%
currently highlighted code and do
exactly<00:01:07.360><c> what</c><00:01:07.479><c> it</c><00:01:07.600><c> says</c><00:01:07.759><c> on</c><00:01:07.960><c> the</c><00:01:08.080><c> tin</c><00:01:08.479><c> for</c>

00:01:08.670 --> 00:01:08.680 align:start position:0%
exactly what it says on the tin for
 

00:01:08.680 --> 00:01:11.429 align:start position:0%
exactly what it says on the tin for
example<00:01:09.280><c> document</c><00:01:09.759><c> code</c><00:01:10.200><c> generate</c><00:01:10.600><c> tests</c><00:01:11.240><c> or</c>

00:01:11.429 --> 00:01:11.439 align:start position:0%
example document code generate tests or
 

00:01:11.439 --> 00:01:14.270 align:start position:0%
example document code generate tests or
even<00:01:11.680><c> fix</c><00:01:11.920><c> a</c><00:01:12.080><c> bug</c><00:01:12.479><c> that</c><00:01:12.720><c> co-pilot</c><00:01:13.280><c> has</c><00:01:13.439><c> found</c><00:01:14.200><c> I</c>

00:01:14.270 --> 00:01:14.280 align:start position:0%
even fix a bug that co-pilot has found I
 

00:01:14.280 --> 00:01:15.830 align:start position:0%
even fix a bug that co-pilot has found I
will<00:01:14.439><c> show</c><00:01:14.720><c> off</c><00:01:15.000><c> some</c><00:01:15.119><c> of</c><00:01:15.240><c> the</c><00:01:15.360><c> commands</c><00:01:15.720><c> we</c>

00:01:15.830 --> 00:01:15.840 align:start position:0%
will show off some of the commands we
 

00:01:15.840 --> 00:01:17.510 align:start position:0%
will show off some of the commands we
have<00:01:16.040><c> available</c><00:01:16.640><c> by</c><00:01:16.840><c> going</c><00:01:17.119><c> through</c><00:01:17.360><c> the</c>

00:01:17.510 --> 00:01:17.520 align:start position:0%
have available by going through the
 

00:01:17.520 --> 00:01:19.590 align:start position:0%
have available by going through the
results<00:01:17.920><c> we</c><00:01:18.040><c> have</c><00:01:18.240><c> found</c><00:01:18.840><c> after</c><00:01:19.159><c> typing</c>

00:01:19.590 --> 00:01:19.600 align:start position:0%
results we have found after typing
 

00:01:19.600 --> 00:01:21.910 align:start position:0%
results we have found after typing
co-pilot<00:01:20.280><c> in</c><00:01:20.439><c> the</c><00:01:20.640><c> command</c><00:01:21.040><c> pallet</c><00:01:21.640><c> with</c><00:01:21.759><c> the</c>

00:01:21.910 --> 00:01:21.920 align:start position:0%
co-pilot in the command pallet with the
 

00:01:21.920 --> 00:01:24.590 align:start position:0%
co-pilot in the command pallet with the
up<00:01:22.079><c> and</c><00:01:22.280><c> down</c><00:01:22.439><c> arrow</c><00:01:22.799><c> keys</c><00:01:23.600><c> co-pilot</c><00:01:24.119><c> Pest</c><00:01:24.520><c> of</c>

00:01:24.590 --> 00:01:24.600 align:start position:0%
up and down arrow keys co-pilot Pest of
 

00:01:24.600 --> 00:01:26.429 align:start position:0%
up and down arrow keys co-pilot Pest of
the<00:01:24.640><c> cursor</c><00:01:24.840><c> Control</c><00:01:25.079><c> Plus</c><00:01:25.960><c> co-</c><00:01:26.079><c> pilot</c><00:01:26.280><c> apply</c>

00:01:26.429 --> 00:01:26.439 align:start position:0%
the cursor Control Plus co- pilot apply
 

00:01:26.439 --> 00:01:28.310 align:start position:0%
the cursor Control Plus co- pilot apply
suest<00:01:26.720><c> with</c><00:01:26.799><c> co-</c><00:01:26.880><c> pilot</c><00:01:27.200><c> of4</c><00:01:27.840><c> co-</c><00:01:27.960><c> pilot</c>

00:01:28.310 --> 00:01:28.320 align:start position:0%
suest with co- pilot of4 co- pilot
 

00:01:28.320 --> 00:01:30.830 align:start position:0%
suest with co- pilot of4 co- pilot
diagnostic<00:01:28.640><c> 7</c><00:01:28.799><c> of</c><00:01:29.200><c> co-</c><00:01:29.320><c> pilot</c><00:01:29.960><c> able</c><00:01:30.479><c> copilot</c>

00:01:30.830 --> 00:01:30.840 align:start position:0%
diagnostic 7 of co- pilot able copilot
 

00:01:30.840 --> 00:01:32.469 align:start position:0%
diagnostic 7 of co- pilot able copilot
explain<00:01:31.040><c> this</c><00:01:31.159><c> 9</c><00:01:31.280><c> of</c><00:01:31.759><c> co-</c><00:01:31.880><c> pilot</c><00:01:32.119><c> explain</c><00:01:32.360><c> this</c>

00:01:32.469 --> 00:01:32.479 align:start position:0%
explain this 9 of co- pilot explain this
 

00:01:32.479 --> 00:01:34.710 align:start position:0%
explain this 9 of co- pilot explain this
terminal<00:01:32.759><c> 10</c><00:01:33.240><c> co-</c><00:01:33.360><c> pilot</c><00:01:33.560><c> fix</c><00:01:33.759><c> 11</c><00:01:34.399><c> co-</c><00:01:34.479><c> pilot</c>

00:01:34.710 --> 00:01:34.720 align:start position:0%
terminal 10 co- pilot fix 11 co- pilot
 

00:01:34.720 --> 00:01:36.429 align:start position:0%
terminal 10 co- pilot fix 11 co- pilot
generate<00:01:35.040><c> 12</c><00:01:35.560><c> co-pilot</c><00:01:35.880><c> generate</c><00:01:36.119><c> test</c><00:01:36.240><c> 13</c>

00:01:36.429 --> 00:01:36.439 align:start position:0%
generate 12 co-pilot generate test 13
 

00:01:36.439 --> 00:01:38.590 align:start position:0%
generate 12 co-pilot generate test 13
of4<00:01:37.159><c> now</c><00:01:37.320><c> please</c><00:01:37.520><c> note</c><00:01:37.720><c> that</c><00:01:37.920><c> for</c><00:01:38.079><c> some</c><00:01:38.240><c> of</c><00:01:38.399><c> the</c>

00:01:38.590 --> 00:01:38.600 align:start position:0%
of4 now please note that for some of the
 

00:01:38.600 --> 00:01:40.270 align:start position:0%
of4 now please note that for some of the
commands<00:01:39.040><c> in</c><00:01:39.240><c> this</c><00:01:39.439><c> list</c><00:01:39.720><c> we</c><00:01:39.840><c> have</c><00:01:40.000><c> already</c>

00:01:40.270 --> 00:01:40.280 align:start position:0%
commands in this list we have already
 

00:01:40.280 --> 00:01:42.469 align:start position:0%
commands in this list we have already
created<00:01:40.680><c> some</c><00:01:40.920><c> videos</c><00:01:41.479><c> to</c><00:01:41.799><c> show</c><00:01:42.040><c> off</c><00:01:42.280><c> how</c>

00:01:42.469 --> 00:01:42.479 align:start position:0%
created some videos to show off how
 

00:01:42.479 --> 00:01:43.670 align:start position:0%
created some videos to show off how
these<00:01:42.640><c> commands</c><00:01:43.000><c> work</c><00:01:43.200><c> with</c><00:01:43.320><c> the</c><00:01:43.399><c> screen</c>

00:01:43.670 --> 00:01:43.680 align:start position:0%
these commands work with the screen
 

00:01:43.680 --> 00:01:46.069 align:start position:0%
these commands work with the screen
meter<00:01:44.200><c> for</c><00:01:44.399><c> others</c><00:01:44.920><c> other</c><00:01:45.159><c> videos</c><00:01:45.479><c> may</c><00:01:45.640><c> follow</c>

00:01:46.069 --> 00:01:46.079 align:start position:0%
meter for others other videos may follow
 

00:01:46.079 --> 00:01:47.950 align:start position:0%
meter for others other videos may follow
soon<00:01:46.719><c> this</c><00:01:46.799><c> is</c><00:01:46.960><c> the</c><00:01:47.040><c> end</c><00:01:47.240><c> of</c><00:01:47.320><c> the</c><00:01:47.479><c> video</c><00:01:47.799><c> about</c>

00:01:47.950 --> 00:01:47.960 align:start position:0%
soon this is the end of the video about
 

00:01:47.960 --> 00:01:49.630 align:start position:0%
soon this is the end of the video about
the<00:01:48.079><c> command</c><00:01:48.479><c> pallet</c><00:01:48.840><c> as</c><00:01:48.960><c> it</c><00:01:49.119><c> pertains</c><00:01:49.439><c> to</c>

00:01:49.630 --> 00:01:49.640 align:start position:0%
the command pallet as it pertains to
 

00:01:49.640 --> 00:01:51.749 align:start position:0%
the command pallet as it pertains to
GitHub<00:01:50.040><c> co-pilot</c><00:01:50.680><c> please</c><00:01:51.040><c> be</c><00:01:51.159><c> on</c><00:01:51.280><c> the</c><00:01:51.399><c> lookout</c>

00:01:51.749 --> 00:01:51.759 align:start position:0%
GitHub co-pilot please be on the lookout
 

00:01:51.759 --> 00:01:53.630 align:start position:0%
GitHub co-pilot please be on the lookout
for<00:01:51.960><c> more</c><00:01:52.200><c> videos</c><00:01:52.560><c> to</c><00:01:52.719><c> come</c><00:01:52.960><c> about</c><00:01:53.200><c> this</c><00:01:53.399><c> topic</c>

00:01:53.630 --> 00:01:53.640 align:start position:0%
for more videos to come about this topic
 

00:01:53.640 --> 00:01:57.560 align:start position:0%
for more videos to come about this topic
soon<00:01:54.159><c> thanks</c><00:01:54.360><c> for</c><00:01:54.560><c> watching</c>

