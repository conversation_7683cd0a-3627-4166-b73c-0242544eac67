import { NextRequest, NextResponse } from 'next/server'
import { DatabaseService } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const topicsParam = searchParams.get('topics')
    const channelsParam = searchParams.get('channels')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    
    const topics = topicsParam ? topicsParam.split(',').filter(t => t.trim()) : undefined
    const channels = channelsParam ? channelsParam.split(',').filter(c => c.trim()) : undefined
    
    const videos = await DatabaseService.getFilteredVideos(
      topics, 
      channels, 
      limit, 
      offset,
      startDate || undefined,
      endDate || undefined
    )
    
    return NextResponse.json({ 
      videos,
      count: videos.length,
      filters: {
        topics: topics || [],
        channels: channels || []
      }
    })
  } catch (error) {
    console.error('Error in filtered videos API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch filtered videos' },
      { status: 500 }
    )
  }
}
