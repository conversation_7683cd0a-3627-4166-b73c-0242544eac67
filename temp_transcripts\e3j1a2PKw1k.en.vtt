WEBVTT
Kind: captions
Language: en

00:00:00.520 --> 00:00:02.950 align:start position:0%
 
welcome<00:00:00.960><c> back</c><00:00:01.680><c> you're</c><00:00:01.920><c> watching</c><00:00:02.280><c> the</c><00:00:02.560><c> second</c>

00:00:02.950 --> 00:00:02.960 align:start position:0%
welcome back you're watching the second
 

00:00:02.960 --> 00:00:05.150 align:start position:0%
welcome back you're watching the second
video<00:00:03.480><c> of</c><00:00:03.600><c> a</c><00:00:03.760><c> free</c><00:00:04.120><c> course</c><00:00:04.680><c> that</c><00:00:04.759><c> will</c><00:00:04.920><c> teach</c>

00:00:05.150 --> 00:00:05.160 align:start position:0%
video of a free course that will teach
 

00:00:05.160 --> 00:00:07.230 align:start position:0%
video of a free course that will teach
you<00:00:05.480><c> everything</c><00:00:05.960><c> you</c><00:00:06.080><c> need</c><00:00:06.279><c> to</c><00:00:06.440><c> know</c><00:00:07.040><c> about</c>

00:00:07.230 --> 00:00:07.240 align:start position:0%
you everything you need to know about
 

00:00:07.240 --> 00:00:10.350 align:start position:0%
you everything you need to know about
using<00:00:07.560><c> AMA</c><00:00:08.559><c> and</c><00:00:08.679><c> will</c><00:00:08.880><c> help</c><00:00:09.040><c> you</c><00:00:09.240><c> become</c><00:00:09.679><c> a</c><00:00:09.960><c> pro</c>

00:00:10.350 --> 00:00:10.360 align:start position:0%
using AMA and will help you become a pro
 

00:00:10.360 --> 00:00:12.709 align:start position:0%
using AMA and will help you become a pro
with<00:00:10.480><c> the</c><00:00:10.880><c> technology</c><00:00:11.880><c> I'm</c><00:00:12.000><c> releasing</c><00:00:12.400><c> a</c><00:00:12.519><c> new</c>

00:00:12.709 --> 00:00:12.719 align:start position:0%
with the technology I'm releasing a new
 

00:00:12.719 --> 00:00:14.910 align:start position:0%
with the technology I'm releasing a new
video<00:00:12.960><c> in</c><00:00:13.080><c> the</c><00:00:13.200><c> course</c><00:00:13.639><c> each</c><00:00:13.839><c> week</c><00:00:14.360><c> so</c><00:00:14.639><c> keep</c>

00:00:14.910 --> 00:00:14.920 align:start position:0%
video in the course each week so keep
 

00:00:14.920 --> 00:00:17.429 align:start position:0%
video in the course each week so keep
coming<00:00:15.240><c> back</c><00:00:15.400><c> to</c><00:00:15.519><c> learn</c><00:00:15.759><c> more</c><00:00:16.400><c> and</c><00:00:16.600><c> more</c><00:00:17.160><c> about</c>

00:00:17.429 --> 00:00:17.439 align:start position:0%
coming back to learn more and more about
 

00:00:17.439 --> 00:00:20.150 align:start position:0%
coming back to learn more and more about
it<00:00:18.240><c> in</c><00:00:18.359><c> the</c><00:00:18.560><c> previous</c><00:00:18.960><c> video</c><00:00:19.279><c> we</c><00:00:19.400><c> did</c><00:00:19.680><c> a</c><00:00:19.880><c> quick</c>

00:00:20.150 --> 00:00:20.160 align:start position:0%
it in the previous video we did a quick
 

00:00:20.160 --> 00:00:22.070 align:start position:0%
it in the previous video we did a quick
overview<00:00:20.680><c> of</c><00:00:20.840><c> how</c><00:00:20.960><c> to</c><00:00:21.160><c> get</c><00:00:21.279><c> started</c><00:00:21.680><c> with</c>

00:00:22.070 --> 00:00:22.080 align:start position:0%
overview of how to get started with
 

00:00:22.080 --> 00:00:24.470 align:start position:0%
overview of how to get started with
olama<00:00:23.080><c> in</c><00:00:23.240><c> this</c><00:00:23.400><c> video</c><00:00:23.640><c> we're</c><00:00:23.800><c> going</c><00:00:24.119><c> to</c><00:00:24.320><c> go</c>

00:00:24.470 --> 00:00:24.480 align:start position:0%
olama in this video we're going to go
 

00:00:24.480 --> 00:00:27.550 align:start position:0%
olama in this video we're going to go
into<00:00:24.760><c> more</c><00:00:25.160><c> detail</c><00:00:25.680><c> on</c><00:00:26.160><c> installation</c><00:00:27.160><c> there's</c>

00:00:27.550 --> 00:00:27.560 align:start position:0%
into more detail on installation there's
 

00:00:27.560 --> 00:00:30.029 align:start position:0%
into more detail on installation there's
no<00:00:27.880><c> way</c><00:00:28.080><c> for</c><00:00:28.240><c> me</c><00:00:28.359><c> to</c><00:00:28.560><c> gauge</c><00:00:29.000><c> how</c><00:00:29.199><c> much</c><00:00:29.439><c> time</c><00:00:29.679><c> you</c>

00:00:30.029 --> 00:00:30.039 align:start position:0%
no way for me to gauge how much time you
 

00:00:30.039 --> 00:00:32.589 align:start position:0%
no way for me to gauge how much time you
need<00:00:30.240><c> to</c><00:00:30.400><c> do</c><00:00:30.840><c> any</c><00:00:31.199><c> step</c><00:00:31.960><c> so</c><00:00:32.119><c> it's</c><00:00:32.360><c> probably</c>

00:00:32.589 --> 00:00:32.599 align:start position:0%
need to do any step so it's probably
 

00:00:32.599 --> 00:00:34.670 align:start position:0%
need to do any step so it's probably
worth<00:00:32.920><c> watching</c><00:00:33.200><c> the</c><00:00:33.360><c> video</c><00:00:33.879><c> all</c><00:00:34.280><c> the</c><00:00:34.440><c> way</c>

00:00:34.670 --> 00:00:34.680 align:start position:0%
worth watching the video all the way
 

00:00:34.680 --> 00:00:37.950 align:start position:0%
worth watching the video all the way
through<00:00:35.360><c> without</c><00:00:35.760><c> actually</c><00:00:36.040><c> following</c><00:00:36.960><c> along</c>

00:00:37.950 --> 00:00:37.960 align:start position:0%
through without actually following along
 

00:00:37.960 --> 00:00:39.950 align:start position:0%
through without actually following along
then<00:00:38.079><c> try</c><00:00:38.280><c> it</c><00:00:38.399><c> out</c><00:00:39.000><c> if</c><00:00:39.079><c> you</c><00:00:39.200><c> run</c><00:00:39.440><c> into</c><00:00:39.719><c> any</c>

00:00:39.950 --> 00:00:39.960 align:start position:0%
then try it out if you run into any
 

00:00:39.960 --> 00:00:42.350 align:start position:0%
then try it out if you run into any
issues<00:00:40.480><c> come</c><00:00:40.760><c> back</c><00:00:40.960><c> to</c><00:00:41.160><c> the</c><00:00:41.320><c> video</c><00:00:41.800><c> to</c><00:00:42.039><c> see</c>

00:00:42.350 --> 00:00:42.360 align:start position:0%
issues come back to the video to see
 

00:00:42.360 --> 00:00:45.029 align:start position:0%
issues come back to the video to see
what<00:00:42.480><c> you</c><00:00:42.640><c> missed</c><00:00:43.600><c> pause</c><00:00:44.160><c> go</c><00:00:44.399><c> back</c><00:00:44.680><c> speed</c>

00:00:45.029 --> 00:00:45.039 align:start position:0%
what you missed pause go back speed
 

00:00:45.039 --> 00:00:47.709 align:start position:0%
what you missed pause go back speed
forward<00:00:45.680><c> are</c><00:00:45.960><c> all</c><00:00:46.239><c> options</c><00:00:46.719><c> in</c><00:00:46.840><c> the</c><00:00:47.000><c> YouTube</c>

00:00:47.709 --> 00:00:47.719 align:start position:0%
forward are all options in the YouTube
 

00:00:47.719 --> 00:00:49.910 align:start position:0%
forward are all options in the YouTube
interface<00:00:48.719><c> in</c><00:00:48.840><c> the</c><00:00:48.960><c> getting</c><00:00:49.199><c> started</c><00:00:49.559><c> video</c>

00:00:49.910 --> 00:00:49.920 align:start position:0%
interface in the getting started video
 

00:00:49.920 --> 00:00:52.950 align:start position:0%
interface in the getting started video
we<00:00:50.039><c> saw</c><00:00:50.480><c> that</c><00:00:50.640><c> you</c><00:00:50.760><c> had</c><00:00:50.879><c> to</c><00:00:51.079><c> go</c><00:00:51.280><c> to</c><00:00:51.559><c> ama.com</c><00:00:52.320><c> and</c>

00:00:52.950 --> 00:00:52.960 align:start position:0%
we saw that you had to go to ama.com and
 

00:00:52.960 --> 00:00:54.869 align:start position:0%
we saw that you had to go to ama.com and
in<00:00:53.079><c> the</c><00:00:53.199><c> middle</c><00:00:53.480><c> of</c><00:00:53.600><c> the</c><00:00:53.719><c> page</c><00:00:54.199><c> there's</c><00:00:54.359><c> a</c><00:00:54.559><c> link</c>

00:00:54.869 --> 00:00:54.879 align:start position:0%
in the middle of the page there's a link
 

00:00:54.879 --> 00:00:57.029 align:start position:0%
in the middle of the page there's a link
to<00:00:55.120><c> download</c><00:00:55.520><c> it</c><00:00:56.160><c> so</c><00:00:56.359><c> let's</c><00:00:56.600><c> look</c><00:00:56.760><c> at</c><00:00:56.879><c> the</c>

00:00:57.029 --> 00:00:57.039 align:start position:0%
to download it so let's look at the
 

00:00:57.039 --> 00:00:59.990 align:start position:0%
to download it so let's look at the
options<00:00:57.680><c> there</c><00:00:57.800><c> are</c><00:00:58.039><c> three</c><00:00:58.320><c> options</c><00:00:58.920><c> Mac</c><00:00:59.199><c> OS</c>

00:00:59.990 --> 00:01:00.000 align:start position:0%
options there are three options Mac OS
 

00:01:00.000 --> 00:01:02.349 align:start position:0%
options there are three options Mac OS
Linux<00:01:00.719><c> and</c><00:01:00.879><c> windows</c><00:01:01.840><c> let's</c><00:01:02.000><c> start</c><00:01:02.199><c> with</c>

00:01:02.349 --> 00:01:02.359 align:start position:0%
Linux and windows let's start with
 

00:01:02.359 --> 00:01:04.429 align:start position:0%
Linux and windows let's start with
windows<00:01:03.199><c> I</c><00:01:03.280><c> don't</c><00:01:03.559><c> actually</c><00:01:03.800><c> have</c><00:01:03.960><c> a</c><00:01:04.080><c> Windows</c>

00:01:04.429 --> 00:01:04.439 align:start position:0%
windows I don't actually have a Windows
 

00:01:04.439 --> 00:01:06.510 align:start position:0%
windows I don't actually have a Windows
system<00:01:04.720><c> to</c><00:01:04.920><c> use</c><00:01:05.280><c> as</c><00:01:05.519><c> all</c><00:01:05.760><c> the</c><00:01:05.880><c> computers</c><00:01:06.280><c> in</c>

00:01:06.510 --> 00:01:06.520 align:start position:0%
system to use as all the computers in
 

00:01:06.520 --> 00:01:09.390 align:start position:0%
system to use as all the computers in
this<00:01:06.720><c> house</c><00:01:07.479><c> are</c><00:01:07.680><c> Macs</c><00:01:08.479><c> so</c><00:01:08.680><c> I'll</c><00:01:08.799><c> be</c><00:01:08.960><c> using</c><00:01:09.200><c> an</c>

00:01:09.390 --> 00:01:09.400 align:start position:0%
this house are Macs so I'll be using an
 

00:01:09.400 --> 00:01:11.950 align:start position:0%
this house are Macs so I'll be using an
instance<00:01:09.840><c> on</c><00:01:10.240><c> paperspace</c><00:01:11.240><c> paperspace</c><00:01:11.799><c> is</c>

00:01:11.950 --> 00:01:11.960 align:start position:0%
instance on paperspace paperspace is
 

00:01:11.960 --> 00:01:13.910 align:start position:0%
instance on paperspace paperspace is
part<00:01:12.080><c> of</c><00:01:12.240><c> digital</c><00:01:12.560><c> ocean</c><00:01:12.960><c> and</c><00:01:13.400><c> after</c><00:01:13.600><c> all</c><00:01:13.759><c> my</c>

00:01:13.910 --> 00:01:13.920 align:start position:0%
part of digital ocean and after all my
 

00:01:13.920 --> 00:01:16.230 align:start position:0%
part of digital ocean and after all my
searching<00:01:14.479><c> is</c><00:01:14.680><c> the</c><00:01:14.880><c> one</c><00:01:15.159><c> reliable</c><00:01:15.640><c> source</c><00:01:16.080><c> of</c>

00:01:16.230 --> 00:01:16.240 align:start position:0%
searching is the one reliable source of
 

00:01:16.240 --> 00:01:19.270 align:start position:0%
searching is the one reliable source of
Windows<00:01:16.600><c> machines</c><00:01:17.080><c> in</c><00:01:17.240><c> the</c><00:01:17.400><c> cloud</c><00:01:18.159><c> with</c><00:01:18.759><c> named</c>

00:01:19.270 --> 00:01:19.280 align:start position:0%
Windows machines in the cloud with named
 

00:01:19.280 --> 00:01:22.230 align:start position:0%
Windows machines in the cloud with named
gpus<00:01:19.960><c> meaning</c><00:01:20.360><c> not</c><00:01:20.600><c> just</c><00:01:20.759><c> a</c><00:01:21.040><c> fake</c><00:01:21.400><c> name</c><00:01:22.040><c> that</c>

00:01:22.230 --> 00:01:22.240 align:start position:0%
gpus meaning not just a fake name that
 

00:01:22.240 --> 00:01:24.789 align:start position:0%
gpus meaning not just a fake name that
Azure<00:01:22.680><c> uses</c><00:01:23.640><c> this</c><00:01:23.759><c> is</c><00:01:23.960><c> a</c><00:01:24.040><c> Windows</c><00:01:24.400><c> instance</c>

00:01:24.789 --> 00:01:24.799 align:start position:0%
Azure uses this is a Windows instance
 

00:01:24.799 --> 00:01:28.550 align:start position:0%
Azure uses this is a Windows instance
with<00:01:24.960><c> a</c><00:01:25.079><c> p6000</c><00:01:25.920><c> GPU</c><00:01:26.759><c> that</c><00:01:26.920><c> has</c><00:01:27.079><c> 24</c><00:01:27.560><c> GB</c><00:01:27.960><c> of</c><00:01:28.079><c> vram</c>

00:01:28.550 --> 00:01:28.560 align:start position:0%
with a p6000 GPU that has 24 GB of vram
 

00:01:28.560 --> 00:01:31.830 align:start position:0%
with a p6000 GPU that has 24 GB of vram
and<00:01:28.720><c> 32</c><00:01:29.200><c> GB</c><00:01:29.600><c> of</c><00:01:29.759><c> r</c><00:01:29.960><c> Ram</c><00:01:30.920><c> so</c><00:01:31.159><c> once</c><00:01:31.360><c> it</c><00:01:31.560><c> finally</c>

00:01:31.830 --> 00:01:31.840 align:start position:0%
and 32 GB of r Ram so once it finally
 

00:01:31.840 --> 00:01:34.230 align:start position:0%
and 32 GB of r Ram so once it finally
starts<00:01:32.159><c> up</c><00:01:32.560><c> you</c><00:01:32.640><c> can</c><00:01:32.799><c> go</c><00:01:32.880><c> to</c><00:01:33.079><c> al.com</c><00:01:33.840><c> and</c><00:01:34.000><c> start</c>

00:01:34.230 --> 00:01:34.240 align:start position:0%
starts up you can go to al.com and start
 

00:01:34.240 --> 00:01:36.030 align:start position:0%
starts up you can go to al.com and start
the<00:01:34.399><c> download</c><00:01:34.759><c> for</c><00:01:34.920><c> the</c>

00:01:36.030 --> 00:01:36.040 align:start position:0%
the download for the
 

00:01:36.040 --> 00:01:39.670 align:start position:0%
the download for the
installer<00:01:37.040><c> then</c><00:01:37.240><c> run</c><00:01:37.479><c> the</c>

00:01:39.670 --> 00:01:39.680 align:start position:0%
 
 

00:01:39.680 --> 00:01:41.870 align:start position:0%
 
installer<00:01:40.680><c> click</c><00:01:40.960><c> through</c><00:01:41.119><c> the</c><00:01:41.240><c> buttons</c><00:01:41.680><c> and</c>

00:01:41.870 --> 00:01:41.880 align:start position:0%
installer click through the buttons and
 

00:01:41.880 --> 00:01:44.270 align:start position:0%
installer click through the buttons and
it's<00:01:42.119><c> pretty</c>

00:01:44.270 --> 00:01:44.280 align:start position:0%
it's pretty
 

00:01:44.280 --> 00:01:46.590 align:start position:0%
it's pretty
easy<00:01:45.280><c> then</c><00:01:45.399><c> you</c><00:01:45.520><c> get</c><00:01:45.680><c> this</c><00:01:45.880><c> notification</c><00:01:46.439><c> that</c>

00:01:46.590 --> 00:01:46.600 align:start position:0%
easy then you get this notification that
 

00:01:46.600 --> 00:01:49.270 align:start position:0%
easy then you get this notification that
ama<00:01:47.119><c> is</c>

00:01:49.270 --> 00:01:49.280 align:start position:0%
 
 

00:01:49.280 --> 00:01:52.030 align:start position:0%
 
started<00:01:50.280><c> and</c><00:01:50.520><c> then</c><00:01:50.880><c> is</c><00:01:51.040><c> set</c><00:01:51.240><c> to</c><00:01:51.439><c> run</c><00:01:51.799><c> when</c><00:01:51.920><c> you</c>

00:01:52.030 --> 00:01:52.040 align:start position:0%
started and then is set to run when you
 

00:01:52.040 --> 00:01:54.870 align:start position:0%
started and then is set to run when you
log<00:01:52.320><c> in</c><00:01:53.119><c> now</c><00:01:53.360><c> you</c><00:01:53.520><c> may</c><00:01:53.880><c> notice</c><00:01:54.240><c> that</c><00:01:54.439><c> I</c><00:01:54.600><c> didn't</c>

00:01:54.870 --> 00:01:54.880 align:start position:0%
log in now you may notice that I didn't
 

00:01:54.880 --> 00:01:58.630 align:start position:0%
log in now you may notice that I didn't
say<00:01:55.119><c> anything</c><00:01:55.479><c> about</c><00:01:55.799><c> Nvidia</c><00:01:56.759><c> or</c><00:01:56.960><c> AMD</c><00:01:57.640><c> drivers</c>

00:01:58.630 --> 00:01:58.640 align:start position:0%
say anything about Nvidia or AMD drivers
 

00:01:58.640 --> 00:02:00.830 align:start position:0%
say anything about Nvidia or AMD drivers
if<00:01:58.799><c> the</c><00:01:59.000><c> drivers</c><00:01:59.520><c> are</c><00:01:59.920><c> are</c><00:02:00.079><c> all</c><00:02:00.360><c> configured</c>

00:02:00.830 --> 00:02:00.840 align:start position:0%
if the drivers are are all configured
 

00:02:00.840 --> 00:02:02.230 align:start position:0%
if the drivers are are all configured
for<00:02:01.000><c> your</c><00:02:01.159><c> machine</c><00:02:01.520><c> then</c><00:02:01.680><c> there's</c><00:02:01.960><c> nothing</c>

00:02:02.230 --> 00:02:02.240 align:start position:0%
for your machine then there's nothing
 

00:02:02.240 --> 00:02:05.590 align:start position:0%
for your machine then there's nothing
else<00:02:02.479><c> to</c><00:02:02.680><c> do</c><00:02:03.439><c> olama</c><00:02:03.960><c> will</c><00:02:04.119><c> use</c><00:02:04.360><c> the</c><00:02:04.560><c> GPU</c><00:02:05.439><c> if</c>

00:02:05.590 --> 00:02:05.600 align:start position:0%
else to do olama will use the GPU if
 

00:02:05.600 --> 00:02:08.070 align:start position:0%
else to do olama will use the GPU if
it's<00:02:05.719><c> a</c><00:02:05.920><c> supported</c><00:02:06.439><c> GPU</c><00:02:07.439><c> you</c><00:02:07.520><c> can</c><00:02:07.680><c> find</c><00:02:08.000><c> the</c>

00:02:08.070 --> 00:02:08.080 align:start position:0%
it's a supported GPU you can find the
 

00:02:08.080 --> 00:02:10.190 align:start position:0%
it's a supported GPU you can find the
gpus<00:02:08.520><c> that</c><00:02:08.599><c> are</c><00:02:08.759><c> supported</c><00:02:09.200><c> by</c><00:02:09.360><c> going</c><00:02:09.560><c> to</c><00:02:09.800><c> this</c>

00:02:10.190 --> 00:02:10.200 align:start position:0%
gpus that are supported by going to this
 

00:02:10.200 --> 00:02:13.110 align:start position:0%
gpus that are supported by going to this
URL<00:02:11.200><c> if</c><00:02:11.319><c> your</c><00:02:11.520><c> GPU</c><00:02:12.000><c> is</c><00:02:12.120><c> on</c><00:02:12.440><c> there</c><00:02:12.879><c> but</c><00:02:13.000><c> you</c>

00:02:13.110 --> 00:02:13.120 align:start position:0%
URL if your GPU is on there but you
 

00:02:13.120 --> 00:02:15.750 align:start position:0%
URL if your GPU is on there but you
aren't<00:02:13.360><c> seeing</c><00:02:13.680><c> AMA</c><00:02:14.200><c> use</c><00:02:14.480><c> the</c><00:02:14.640><c> GPU</c><00:02:15.480><c> you</c><00:02:15.599><c> can</c>

00:02:15.750 --> 00:02:15.760 align:start position:0%
aren't seeing AMA use the GPU you can
 

00:02:15.760 --> 00:02:18.110 align:start position:0%
aren't seeing AMA use the GPU you can
try<00:02:16.160><c> either</c><00:02:16.400><c> the</c><00:02:16.640><c> course</c><00:02:17.040><c> Discord</c><00:02:17.760><c> or</c><00:02:18.000><c> the</c>

00:02:18.110 --> 00:02:18.120 align:start position:0%
try either the course Discord or the
 

00:02:18.120 --> 00:02:21.190 align:start position:0%
try either the course Discord or the
olama<00:02:18.800><c> Discord</c><00:02:19.800><c> the</c><00:02:19.920><c> links</c><00:02:20.200><c> to</c><00:02:20.480><c> both</c><00:02:20.680><c> of</c><00:02:20.879><c> these</c>

00:02:21.190 --> 00:02:21.200 align:start position:0%
olama Discord the links to both of these
 

00:02:21.200 --> 00:02:24.070 align:start position:0%
olama Discord the links to both of these
are<00:02:21.440><c> in</c><00:02:21.560><c> the</c><00:02:21.800><c> description</c><00:02:22.680><c> below</c><00:02:23.680><c> at</c><00:02:23.920><c> this</c>

00:02:24.070 --> 00:02:24.080 align:start position:0%
are in the description below at this
 

00:02:24.080 --> 00:02:26.070 align:start position:0%
are in the description below at this
point<00:02:24.239><c> you</c><00:02:24.360><c> might</c><00:02:24.519><c> be</c><00:02:24.720><c> confused</c><00:02:25.519><c> some</c><00:02:25.720><c> folks</c>

00:02:26.070 --> 00:02:26.080 align:start position:0%
point you might be confused some folks
 

00:02:26.080 --> 00:02:29.229 align:start position:0%
point you might be confused some folks
expect<00:02:26.440><c> a</c><00:02:26.720><c> graphical</c><00:02:27.239><c> UI</c><00:02:27.640><c> to</c><00:02:27.879><c> pop</c><00:02:28.120><c> up</c><00:02:28.959><c> but</c><00:02:29.080><c> the</c>

00:02:29.229 --> 00:02:29.239 align:start position:0%
expect a graphical UI to pop up but the
 

00:02:29.239 --> 00:02:31.589 align:start position:0%
expect a graphical UI to pop up but the
UI<00:02:29.560><c> with</c><00:02:29.720><c> a</c><00:02:30.040><c> Lama</c><00:02:30.720><c> without</c><00:02:31.040><c> anything</c><00:02:31.319><c> else</c>

00:02:31.589 --> 00:02:31.599 align:start position:0%
UI with a Lama without anything else
 

00:02:31.599 --> 00:02:34.150 align:start position:0%
UI with a Lama without anything else
installed<00:02:32.560><c> is</c><00:02:32.760><c> at</c><00:02:32.920><c> the</c><00:02:33.040><c> command</c><00:02:33.440><c> line</c><00:02:33.920><c> so</c><00:02:34.040><c> we</c>

00:02:34.150 --> 00:02:34.160 align:start position:0%
installed is at the command line so we
 

00:02:34.160 --> 00:02:35.949 align:start position:0%
installed is at the command line so we
need<00:02:34.319><c> to</c><00:02:34.480><c> start</c><00:02:34.760><c> by</c><00:02:34.879><c> opening</c><00:02:35.239><c> up</c><00:02:35.519><c> either</c><00:02:35.760><c> the</c>

00:02:35.949 --> 00:02:35.959 align:start position:0%
need to start by opening up either the
 

00:02:35.959 --> 00:02:38.830 align:start position:0%
need to start by opening up either the
terminal<00:02:36.720><c> or</c><00:02:37.200><c> Powell</c><00:02:38.200><c> then</c><00:02:38.319><c> you</c><00:02:38.400><c> can</c><00:02:38.560><c> run</c>

00:02:38.830 --> 00:02:38.840 align:start position:0%
terminal or Powell then you can run
 

00:02:38.840 --> 00:02:41.670 align:start position:0%
terminal or Powell then you can run
olama<00:02:39.400><c> run</c><00:02:39.920><c> and</c><00:02:40.080><c> the</c><00:02:40.200><c> name</c><00:02:40.360><c> of</c><00:02:40.480><c> the</c><00:02:40.599><c> model</c><00:02:41.599><c> I</c>

00:02:41.670 --> 00:02:41.680 align:start position:0%
olama run and the name of the model I
 

00:02:41.680 --> 00:02:43.869 align:start position:0%
olama run and the name of the model I
used<00:02:42.080><c> buy</c><00:02:42.480><c> three</c><00:02:42.920><c> in</c><00:02:43.040><c> the</c><00:02:43.239><c> last</c><00:02:43.440><c> video</c><00:02:43.720><c> because</c>

00:02:43.869 --> 00:02:43.879 align:start position:0%
used buy three in the last video because
 

00:02:43.879 --> 00:02:47.550 align:start position:0%
used buy three in the last video because
it's<00:02:44.080><c> nice</c><00:02:44.280><c> and</c><00:02:44.480><c> small</c><00:02:45.280><c> so</c><00:02:45.519><c> ol</c><00:02:46.080><c> run</c><00:02:46.360><c> 53</c><00:02:47.360><c> and</c><00:02:47.480><c> you</c>

00:02:47.550 --> 00:02:47.560 align:start position:0%
it's nice and small so ol run 53 and you
 

00:02:47.560 --> 00:02:49.790 align:start position:0%
it's nice and small so ol run 53 and you
should<00:02:47.760><c> be</c><00:02:48.000><c> plopped</c><00:02:48.400><c> into</c><00:02:48.640><c> the</c><00:02:48.800><c> repple</c>

00:02:49.790 --> 00:02:49.800 align:start position:0%
should be plopped into the repple
 

00:02:49.800 --> 00:02:51.470 align:start position:0%
should be plopped into the repple
remember<00:02:50.280><c> last</c><00:02:50.560><c> time</c><00:02:50.840><c> I</c><00:02:50.959><c> talked</c><00:02:51.200><c> about</c><00:02:51.360><c> the</c>

00:02:51.470 --> 00:02:51.480 align:start position:0%
remember last time I talked about the
 

00:02:51.480 --> 00:02:53.509 align:start position:0%
remember last time I talked about the
reppel<00:02:51.879><c> being</c><00:02:52.080><c> a</c><00:02:52.280><c> place</c><00:02:52.440><c> you</c><00:02:52.560><c> can</c><00:02:52.720><c> go</c><00:02:52.920><c> to</c><00:02:53.120><c> ask</c><00:02:53.319><c> a</c>

00:02:53.509 --> 00:02:53.519 align:start position:0%
reppel being a place you can go to ask a
 

00:02:53.519 --> 00:02:55.830 align:start position:0%
reppel being a place you can go to ask a
question<00:02:54.319><c> and</c><00:02:54.560><c> interactively</c><00:02:55.360><c> get</c><00:02:55.560><c> the</c>

00:02:55.830 --> 00:02:55.840 align:start position:0%
question and interactively get the
 

00:02:55.840 --> 00:02:58.390 align:start position:0%
question and interactively get the
answer<00:02:56.840><c> now</c><00:02:57.000><c> you</c><00:02:57.080><c> can</c><00:02:57.319><c> ask</c><00:02:57.640><c> any</c><00:02:57.879><c> questions</c><00:02:58.239><c> you</c>

00:02:58.390 --> 00:02:58.400 align:start position:0%
answer now you can ask any questions you
 

00:02:58.400 --> 00:03:00.390 align:start position:0%
answer now you can ask any questions you
like<00:02:58.959><c> just</c><00:02:59.120><c> like</c><00:02:59.280><c> you</c><00:02:59.400><c> saw</c><00:02:59.840><c> in</c><00:02:59.959><c> the</c><00:03:00.120><c> getting</c>

00:03:00.390 --> 00:03:00.400 align:start position:0%
like just like you saw in the getting
 

00:03:00.400 --> 00:03:03.190 align:start position:0%
like just like you saw in the getting
started<00:03:00.920><c> video</c><00:03:01.920><c> of</c><00:03:02.080><c> course</c><00:03:02.440><c> there</c><00:03:02.560><c> are</c><00:03:02.879><c> always</c>

00:03:03.190 --> 00:03:03.200 align:start position:0%
started video of course there are always
 

00:03:03.200 --> 00:03:05.070 align:start position:0%
started video of course there are always
edge<00:03:03.599><c> cases</c><00:03:04.080><c> where</c><00:03:04.239><c> the</c><00:03:04.360><c> install</c><00:03:04.760><c> doesn't</c>

00:03:05.070 --> 00:03:05.080 align:start position:0%
edge cases where the install doesn't
 

00:03:05.080 --> 00:03:07.910 align:start position:0%
edge cases where the install doesn't
work<00:03:06.040><c> as</c><00:03:06.200><c> I</c><00:03:06.319><c> said</c><00:03:06.560><c> before</c><00:03:06.959><c> try</c><00:03:07.200><c> signing</c><00:03:07.560><c> up</c><00:03:07.799><c> to</c>

00:03:07.910 --> 00:03:07.920 align:start position:0%
work as I said before try signing up to
 

00:03:07.920 --> 00:03:10.070 align:start position:0%
work as I said before try signing up to
the<00:03:08.040><c> two</c><00:03:08.280><c> discords</c><00:03:08.920><c> and</c><00:03:09.159><c> you</c><00:03:09.280><c> should</c><00:03:09.760><c> be</c><00:03:09.879><c> able</c>

00:03:10.070 --> 00:03:10.080 align:start position:0%
the two discords and you should be able
 

00:03:10.080 --> 00:03:12.509 align:start position:0%
the two discords and you should be able
to<00:03:10.200><c> get</c><00:03:10.319><c> an</c><00:03:10.519><c> answer</c><00:03:10.879><c> pretty</c><00:03:11.360><c> quickly</c><00:03:12.360><c> let's</c>

00:03:12.509 --> 00:03:12.519 align:start position:0%
to get an answer pretty quickly let's
 

00:03:12.519 --> 00:03:15.309 align:start position:0%
to get an answer pretty quickly let's
move<00:03:12.720><c> on</c><00:03:12.840><c> to</c><00:03:13.040><c> installing</c><00:03:13.519><c> on</c><00:03:14.000><c> Linux</c><00:03:14.799><c> for</c><00:03:15.040><c> this</c>

00:03:15.309 --> 00:03:15.319 align:start position:0%
move on to installing on Linux for this
 

00:03:15.319 --> 00:03:17.589 align:start position:0%
move on to installing on Linux for this
I'm<00:03:15.480><c> using</c><00:03:15.840><c> an</c><00:03:16.040><c> instance</c><00:03:16.480><c> I</c><00:03:16.680><c> just</c><00:03:16.920><c> created</c><00:03:17.400><c> on</c>

00:03:17.589 --> 00:03:17.599 align:start position:0%
I'm using an instance I just created on
 

00:03:17.599 --> 00:03:20.390 align:start position:0%
I'm using an instance I just created on
brev<00:03:18.080><c> dodev</c><00:03:19.080><c> I</c><00:03:19.239><c> love</c><00:03:19.480><c> these</c><00:03:19.680><c> guys</c><00:03:20.080><c> they</c><00:03:20.239><c> make</c>

00:03:20.390 --> 00:03:20.400 align:start position:0%
brev dodev I love these guys they make
 

00:03:20.400 --> 00:03:22.589 align:start position:0%
brev dodev I love these guys they make
it<00:03:20.640><c> so</c><00:03:20.879><c> easy</c><00:03:21.120><c> to</c><00:03:21.319><c> create</c><00:03:21.599><c> a</c><00:03:21.760><c> Linux</c><00:03:22.159><c> instance</c>

00:03:22.589 --> 00:03:22.599 align:start position:0%
it so easy to create a Linux instance
 

00:03:22.599 --> 00:03:25.630 align:start position:0%
it so easy to create a Linux instance
with<00:03:22.879><c> any</c><00:03:23.080><c> GPU</c><00:03:23.560><c> you</c><00:03:23.680><c> want</c><00:03:24.680><c> and</c><00:03:24.920><c> no</c><00:03:25.319><c> they</c><00:03:25.440><c> are</c>

00:03:25.630 --> 00:03:25.640 align:start position:0%
with any GPU you want and no they are
 

00:03:25.640 --> 00:03:28.229 align:start position:0%
with any GPU you want and no they are
not<00:03:25.840><c> paying</c><00:03:26.120><c> me</c><00:03:26.280><c> to</c><00:03:26.440><c> say</c><00:03:26.720><c> that</c><00:03:27.360><c> they're</c><00:03:27.720><c> just</c>

00:03:28.229 --> 00:03:28.239 align:start position:0%
not paying me to say that they're just
 

00:03:28.239 --> 00:03:30.789 align:start position:0%
not paying me to say that they're just
so<00:03:28.599><c> many</c><00:03:28.920><c> sketchy</c><00:03:29.319><c> vendors</c><00:03:29.879><c> out</c><00:03:30.080><c> there</c><00:03:30.519><c> so</c>

00:03:30.789 --> 00:03:30.799 align:start position:0%
so many sketchy vendors out there so
 

00:03:30.799 --> 00:03:33.910 align:start position:0%
so many sketchy vendors out there so
brev<00:03:31.280><c> is</c><00:03:31.560><c> kind</c><00:03:31.720><c> of</c><00:03:32.360><c> refreshing</c><00:03:33.360><c> with</c><00:03:33.519><c> Linux</c>

00:03:33.910 --> 00:03:33.920 align:start position:0%
brev is kind of refreshing with Linux
 

00:03:33.920 --> 00:03:36.509 align:start position:0%
brev is kind of refreshing with Linux
you<00:03:34.120><c> download</c><00:03:34.560><c> and</c><00:03:34.720><c> run</c><00:03:35.040><c> a</c><00:03:35.239><c> script</c><00:03:35.959><c> some</c><00:03:36.159><c> folks</c>

00:03:36.509 --> 00:03:36.519 align:start position:0%
you download and run a script some folks
 

00:03:36.519 --> 00:03:39.229 align:start position:0%
you download and run a script some folks
find<00:03:36.840><c> that</c><00:03:37.080><c> a</c><00:03:37.200><c> bit</c><00:03:37.439><c> scary</c><00:03:38.000><c> to</c><00:03:38.200><c> do</c><00:03:38.840><c> especially</c>

00:03:39.229 --> 00:03:39.239 align:start position:0%
find that a bit scary to do especially
 

00:03:39.239 --> 00:03:41.309 align:start position:0%
find that a bit scary to do especially
if<00:03:39.360><c> they</c><00:03:39.519><c> don't</c><00:03:39.720><c> trust</c><00:03:40.040><c> the</c><00:03:40.239><c> source</c><00:03:41.040><c> so</c><00:03:41.200><c> you</c>

00:03:41.309 --> 00:03:41.319 align:start position:0%
if they don't trust the source so you
 

00:03:41.319 --> 00:03:43.270 align:start position:0%
if they don't trust the source so you
can<00:03:41.480><c> also</c><00:03:41.760><c> review</c><00:03:42.120><c> The</c><00:03:42.239><c> Script</c><00:03:42.560><c> first</c><00:03:42.879><c> or</c><00:03:43.120><c> go</c>

00:03:43.270 --> 00:03:43.280 align:start position:0%
can also review The Script first or go
 

00:03:43.280 --> 00:03:45.550 align:start position:0%
can also review The Script first or go
to<00:03:43.400><c> the</c><00:03:43.519><c> manual</c><00:03:43.840><c> andall</c><00:03:44.400><c> instructions</c><00:03:45.400><c> most</c>

00:03:45.550 --> 00:03:45.560 align:start position:0%
to the manual andall instructions most
 

00:03:45.560 --> 00:03:47.229 align:start position:0%
to the manual andall instructions most
of<00:03:45.760><c> the</c><00:03:45.920><c> script</c><00:03:46.200><c> just</c><00:03:46.319><c> deals</c><00:03:46.640><c> with</c><00:03:46.840><c> drivers</c>

00:03:47.229 --> 00:03:47.239 align:start position:0%
of the script just deals with drivers
 

00:03:47.239 --> 00:03:49.990 align:start position:0%
of the script just deals with drivers
for<00:03:47.519><c> video</c><00:03:47.799><c> cards</c><00:03:48.640><c> the</c><00:03:48.799><c> parts</c><00:03:49.040><c> that</c><00:03:49.200><c> are</c><00:03:49.439><c> AMA</c>

00:03:49.990 --> 00:03:50.000 align:start position:0%
for video cards the parts that are AMA
 

00:03:50.000 --> 00:03:52.429 align:start position:0%
for video cards the parts that are AMA
specific<00:03:50.760><c> copy</c><00:03:51.040><c> the</c><00:03:51.239><c> executable</c><00:03:52.079><c> create</c><00:03:52.319><c> a</c>

00:03:52.429 --> 00:03:52.439 align:start position:0%
specific copy the executable create a
 

00:03:52.439 --> 00:03:54.830 align:start position:0%
specific copy the executable create a
user<00:03:52.760><c> called</c><00:03:53.000><c> AMA</c><00:03:53.480><c> to</c><00:03:53.599><c> run</c><00:03:53.879><c> the</c><00:03:54.000><c> executable</c>

00:03:54.830 --> 00:03:54.840 align:start position:0%
user called AMA to run the executable
 

00:03:54.840 --> 00:03:56.429 align:start position:0%
user called AMA to run the executable
and<00:03:55.000><c> create</c><00:03:55.239><c> the</c><00:03:55.360><c> service</c><00:03:55.720><c> to</c><00:03:55.879><c> run</c><00:03:56.079><c> in</c><00:03:56.200><c> the</c>

00:03:56.429 --> 00:03:56.439 align:start position:0%
and create the service to run in the
 

00:03:56.439 --> 00:03:59.110 align:start position:0%
and create the service to run in the
background<00:03:57.439><c> so</c><00:03:57.799><c> run</c><00:03:58.040><c> the</c><00:03:58.200><c> script</c><00:03:58.799><c> and</c><00:03:58.959><c> just</c>

00:03:59.110 --> 00:03:59.120 align:start position:0%
background so run the script and just
 

00:03:59.120 --> 00:04:00.509 align:start position:0%
background so run the script and just
like<00:03:59.280><c> your</c><00:03:59.400><c> windows</c><00:03:59.920><c> version</c><00:04:00.239><c> if</c><00:04:00.360><c> your</c>

00:04:00.509 --> 00:04:00.519 align:start position:0%
like your windows version if your
 

00:04:00.519 --> 00:04:03.270 align:start position:0%
like your windows version if your
drivers<00:04:00.920><c> for</c><00:04:01.079><c> your</c><00:04:01.239><c> GPU</c><00:04:02.040><c> are</c><00:04:02.239><c> set</c><00:04:03.000><c> then</c><00:04:03.120><c> the</c>

00:04:03.270 --> 00:04:03.280 align:start position:0%
drivers for your GPU are set then the
 

00:04:03.280 --> 00:04:07.069 align:start position:0%
drivers for your GPU are set then the
install<00:04:03.720><c> takes</c><00:04:04.360><c> very</c><00:04:04.640><c> little</c>

00:04:07.069 --> 00:04:07.079 align:start position:0%
 
 

00:04:07.079 --> 00:04:09.910 align:start position:0%
 
time<00:04:08.079><c> and</c><00:04:08.239><c> then</c><00:04:08.720><c> just</c><00:04:08.959><c> like</c><00:04:09.200><c> Windows</c><00:04:09.680><c> you</c><00:04:09.799><c> can</c>

00:04:09.910 --> 00:04:09.920 align:start position:0%
time and then just like Windows you can
 

00:04:09.920 --> 00:04:13.589 align:start position:0%
time and then just like Windows you can
run<00:04:10.159><c> AMA</c><00:04:10.879><c> run</c><00:04:11.400><c> and</c><00:04:11.519><c> the</c><00:04:11.640><c> model</c>

00:04:13.589 --> 00:04:13.599 align:start position:0%
run AMA run and the model
 

00:04:13.599 --> 00:04:16.749 align:start position:0%
run AMA run and the model
name<00:04:14.599><c> now</c><00:04:14.799><c> ask</c><00:04:15.079><c> any</c><00:04:15.360><c> question</c><00:04:16.199><c> there</c><00:04:16.320><c> are</c><00:04:16.519><c> some</c>

00:04:16.749 --> 00:04:16.759 align:start position:0%
name now ask any question there are some
 

00:04:16.759 --> 00:04:18.590 align:start position:0%
name now ask any question there are some
distributions<00:04:17.320><c> of</c><00:04:17.479><c> Linux</c><00:04:17.840><c> that</c><00:04:18.040><c> make</c><00:04:18.280><c> things</c>

00:04:18.590 --> 00:04:18.600 align:start position:0%
distributions of Linux that make things
 

00:04:18.600 --> 00:04:21.509 align:start position:0%
distributions of Linux that make things
more<00:04:19.359><c> difficult</c><00:04:20.000><c> but</c><00:04:20.280><c> if</c><00:04:20.359><c> you're</c><00:04:20.600><c> using</c><00:04:21.079><c> them</c>

00:04:21.509 --> 00:04:21.519 align:start position:0%
more difficult but if you're using them
 

00:04:21.519 --> 00:04:24.270 align:start position:0%
more difficult but if you're using them
you<00:04:22.040><c> already</c><00:04:22.400><c> know</c><00:04:22.880><c> about</c><00:04:23.199><c> this</c><00:04:23.960><c> again</c><00:04:24.160><c> the</c>

00:04:24.270 --> 00:04:24.280 align:start position:0%
you already know about this again the
 

00:04:24.280 --> 00:04:26.710 align:start position:0%
you already know about this again the
discords<00:04:24.759><c> are</c><00:04:24.960><c> the</c><00:04:25.240><c> best</c><00:04:25.520><c> place</c><00:04:25.680><c> to</c><00:04:25.919><c> go</c><00:04:26.280><c> to</c><00:04:26.440><c> get</c>

00:04:26.710 --> 00:04:26.720 align:start position:0%
discords are the best place to go to get
 

00:04:26.720 --> 00:04:29.110 align:start position:0%
discords are the best place to go to get
any<00:04:26.960><c> help</c><00:04:27.160><c> you</c><00:04:27.320><c> need</c><00:04:27.600><c> if</c><00:04:27.720><c> you</c><00:04:27.880><c> run</c><00:04:28.520><c> into</c><00:04:28.880><c> any</c>

00:04:29.110 --> 00:04:29.120 align:start position:0%
any help you need if you run into any
 

00:04:29.120 --> 00:04:31.469 align:start position:0%
any help you need if you run into any
issues<00:04:30.199><c> with</c><00:04:30.360><c> that</c><00:04:30.520><c> done</c><00:04:30.800><c> let's</c><00:04:30.960><c> move</c><00:04:31.160><c> to</c><00:04:31.320><c> the</c>

00:04:31.469 --> 00:04:31.479 align:start position:0%
issues with that done let's move to the
 

00:04:31.479 --> 00:04:33.909 align:start position:0%
issues with that done let's move to the
third<00:04:31.759><c> platform</c><00:04:32.199><c> which</c><00:04:32.320><c> is</c><00:04:32.479><c> Mac</c><00:04:32.759><c> OS</c><00:04:33.639><c> it's</c><00:04:33.759><c> a</c>

00:04:33.909 --> 00:04:33.919 align:start position:0%
third platform which is Mac OS it's a
 

00:04:33.919 --> 00:04:35.990 align:start position:0%
third platform which is Mac OS it's a
universal<00:04:34.560><c> app</c><00:04:34.800><c> so</c><00:04:34.960><c> it'll</c><00:04:35.199><c> run</c><00:04:35.400><c> on</c><00:04:35.680><c> Apple</c>

00:04:35.990 --> 00:04:36.000 align:start position:0%
universal app so it'll run on Apple
 

00:04:36.000 --> 00:04:39.350 align:start position:0%
universal app so it'll run on Apple
silicon<00:04:36.639><c> or</c><00:04:36.919><c> Intel</c><00:04:37.280><c> Max</c><00:04:38.199><c> but</c><00:04:38.320><c> there's</c><00:04:38.479><c> a</c><00:04:38.680><c> catch</c>

00:04:39.350 --> 00:04:39.360 align:start position:0%
silicon or Intel Max but there's a catch
 

00:04:39.360 --> 00:04:43.909 align:start position:0%
silicon or Intel Max but there's a catch
it<00:04:39.479><c> runs</c><00:04:40.039><c> great</c><00:04:40.400><c> on</c><00:04:40.680><c> Apple</c><00:04:41.360><c> silicon</c><00:04:42.360><c> but</c><00:04:42.919><c> Intel</c>

00:04:43.909 --> 00:04:43.919 align:start position:0%
it runs great on Apple silicon but Intel
 

00:04:43.919 --> 00:04:47.150 align:start position:0%
it runs great on Apple silicon but Intel
is<00:04:44.360><c> more</c><00:04:44.680><c> of</c><00:04:44.800><c> a</c><00:04:45.120><c> challenge</c><00:04:46.120><c> GPU</c><00:04:46.600><c> support</c><00:04:46.960><c> on</c>

00:04:47.150 --> 00:04:47.160 align:start position:0%
is more of a challenge GPU support on
 

00:04:47.160 --> 00:04:49.070 align:start position:0%
is more of a challenge GPU support on
those<00:04:47.360><c> older</c><00:04:47.680><c> Macs</c><00:04:48.120><c> is</c>

00:04:49.070 --> 00:04:49.080 align:start position:0%
those older Macs is
 

00:04:49.080 --> 00:04:51.749 align:start position:0%
those older Macs is
nonexistent<00:04:50.080><c> the</c><00:04:50.199><c> M1</c><00:04:50.720><c> came</c><00:04:50.880><c> out</c><00:04:51.199><c> about</c><00:04:51.479><c> 4</c>

00:04:51.749 --> 00:04:51.759 align:start position:0%
nonexistent the M1 came out about 4
 

00:04:51.759 --> 00:04:54.029 align:start position:0%
nonexistent the M1 came out about 4
years<00:04:51.960><c> ago</c><00:04:52.240><c> and</c><00:04:52.400><c> is</c><00:04:52.639><c> so</c><00:04:53.240><c> superior</c><00:04:53.800><c> to</c><00:04:53.960><c> the</c>

00:04:54.029 --> 00:04:54.039 align:start position:0%
years ago and is so superior to the
 

00:04:54.039 --> 00:04:57.110 align:start position:0%
years ago and is so superior to the
Intel<00:04:54.720><c> versions</c><00:04:55.720><c> I</c><00:04:55.919><c> really</c><00:04:56.280><c> doubt</c><00:04:56.639><c> support</c>

00:04:57.110 --> 00:04:57.120 align:start position:0%
Intel versions I really doubt support
 

00:04:57.120 --> 00:04:59.710 align:start position:0%
Intel versions I really doubt support
will<00:04:57.360><c> come</c><00:04:57.600><c> for</c><00:04:57.840><c> those</c><00:04:58.039><c> older</c><00:04:58.479><c> versions</c><00:04:59.400><c> but</c>

00:04:59.710 --> 00:04:59.720 align:start position:0%
will come for those older versions but
 

00:04:59.720 --> 00:05:01.950 align:start position:0%
will come for those older versions but
you<00:04:59.880><c> can</c><00:05:00.240><c> easily</c><00:05:00.560><c> get</c><00:05:00.720><c> a</c><00:05:00.919><c> new</c><00:05:01.160><c> Mac</c><00:05:01.400><c> Mini</c><00:05:01.759><c> with</c>

00:05:01.950 --> 00:05:01.960 align:start position:0%
you can easily get a new Mac Mini with
 

00:05:01.960 --> 00:05:05.070 align:start position:0%
you can easily get a new Mac Mini with
16<00:05:02.280><c> gigs</c><00:05:02.479><c> of</c><00:05:02.639><c> RAM</c><00:05:02.960><c> for</c><00:05:03.440><c> $800</c><00:05:04.320><c> or</c><00:05:04.440><c> so</c><00:05:04.759><c> which</c><00:05:04.880><c> is</c>

00:05:05.070 --> 00:05:05.080 align:start position:0%
16 gigs of RAM for $800 or so which is
 

00:05:05.080 --> 00:05:08.070 align:start position:0%
16 gigs of RAM for $800 or so which is
pretty<00:05:05.400><c> great</c><00:05:06.280><c> installing</c><00:05:06.720><c> aama</c><00:05:07.240><c> on</c><00:05:07.320><c> a</c><00:05:07.479><c> Mac</c>

00:05:08.070 --> 00:05:08.080 align:start position:0%
pretty great installing aama on a Mac
 

00:05:08.080 --> 00:05:10.110 align:start position:0%
pretty great installing aama on a Mac
uses<00:05:08.520><c> an</c><00:05:08.720><c> installer</c><00:05:09.199><c> that</c><00:05:09.320><c> you</c><00:05:09.479><c> download</c><00:05:09.919><c> and</c>

00:05:10.110 --> 00:05:10.120 align:start position:0%
uses an installer that you download and
 

00:05:10.120 --> 00:05:12.990 align:start position:0%
uses an installer that you download and
run<00:05:10.919><c> but</c><00:05:11.120><c> like</c><00:05:11.320><c> the</c><00:05:11.440><c> others</c><00:05:11.800><c> it's</c><00:05:12.120><c> super</c><00:05:12.479><c> quick</c>

00:05:12.990 --> 00:05:13.000 align:start position:0%
run but like the others it's super quick
 

00:05:13.000 --> 00:05:15.510 align:start position:0%
run but like the others it's super quick
and<00:05:13.120><c> you're</c><00:05:13.320><c> done</c><00:05:14.320><c> then</c><00:05:14.520><c> open</c><00:05:14.720><c> a</c><00:05:14.880><c> terminal</c><00:05:15.400><c> and</c>

00:05:15.510 --> 00:05:15.520 align:start position:0%
and you're done then open a terminal and
 

00:05:15.520 --> 00:05:18.390 align:start position:0%
and you're done then open a terminal and
run<00:05:15.800><c> AMA</c><00:05:16.320><c> run</c><00:05:16.720><c> and</c><00:05:16.840><c> a</c><00:05:16.960><c> model</c><00:05:17.280><c> name</c><00:05:17.639><c> and</c><00:05:17.840><c> boom</c>

00:05:18.390 --> 00:05:18.400 align:start position:0%
run AMA run and a model name and boom
 

00:05:18.400 --> 00:05:20.909 align:start position:0%
run AMA run and a model name and boom
ask<00:05:18.600><c> your</c><00:05:19.000><c> question</c><00:05:20.000><c> there</c><00:05:20.120><c> are</c><00:05:20.280><c> a</c><00:05:20.400><c> number</c><00:05:20.639><c> of</c>

00:05:20.909 --> 00:05:20.919 align:start position:0%
ask your question there are a number of
 

00:05:20.919 --> 00:05:22.710 align:start position:0%
ask your question there are a number of
common<00:05:21.319><c> next</c><00:05:21.639><c> steps</c><00:05:22.000><c> that</c><00:05:22.120><c> folks</c><00:05:22.400><c> want</c><00:05:22.560><c> to</c>

00:05:22.710 --> 00:05:22.720 align:start position:0%
common next steps that folks want to
 

00:05:22.720 --> 00:05:25.710 align:start position:0%
common next steps that folks want to
deal<00:05:23.000><c> with</c><00:05:23.400><c> one</c><00:05:23.560><c> is</c><00:05:23.800><c> installing</c><00:05:24.240><c> a</c><00:05:24.400><c> web</c><00:05:24.720><c> UI</c>

00:05:25.710 --> 00:05:25.720 align:start position:0%
deal with one is installing a web UI
 

00:05:25.720 --> 00:05:27.629 align:start position:0%
deal with one is installing a web UI
I'll<00:05:25.960><c> have</c><00:05:26.160><c> a</c><00:05:26.319><c> number</c><00:05:26.600><c> of</c><00:05:26.720><c> videos</c><00:05:27.080><c> on</c><00:05:27.280><c> that</c><00:05:27.520><c> in</c>

00:05:27.629 --> 00:05:27.639 align:start position:0%
I'll have a number of videos on that in
 

00:05:27.639 --> 00:05:29.749 align:start position:0%
I'll have a number of videos on that in
the<00:05:27.759><c> future</c><00:05:28.080><c> as</c><00:05:28.240><c> part</c><00:05:28.400><c> of</c><00:05:28.520><c> this</c><00:05:28.680><c> course</c>

00:05:29.749 --> 00:05:29.759 align:start position:0%
the future as part of this course
 

00:05:29.759 --> 00:05:31.710 align:start position:0%
the future as part of this course
another<00:05:30.039><c> common</c><00:05:30.319><c> need</c><00:05:30.639><c> is</c><00:05:30.759><c> to</c><00:05:30.919><c> put</c><00:05:31.120><c> models</c><00:05:31.600><c> in</c>

00:05:31.710 --> 00:05:31.720 align:start position:0%
another common need is to put models in
 

00:05:31.720 --> 00:05:33.510 align:start position:0%
another common need is to put models in
a<00:05:31.880><c> different</c><00:05:32.199><c> directory</c><00:05:33.000><c> from</c><00:05:33.199><c> where</c><00:05:33.360><c> it</c>

00:05:33.510 --> 00:05:33.520 align:start position:0%
a different directory from where it
 

00:05:33.520 --> 00:05:35.790 align:start position:0%
a different directory from where it
defaults<00:05:33.960><c> to</c><00:05:34.759><c> I'll</c><00:05:34.919><c> have</c><00:05:35.039><c> a</c><00:05:35.199><c> video</c><00:05:35.520><c> coming</c>

00:05:35.790 --> 00:05:35.800 align:start position:0%
defaults to I'll have a video coming
 

00:05:35.800 --> 00:05:38.710 align:start position:0%
defaults to I'll have a video coming
soon<00:05:36.120><c> on</c><00:05:36.319><c> where</c><00:05:36.560><c> files</c><00:05:36.919><c> Go</c><00:05:37.240><c> in</c><00:05:37.440><c> ol</c><00:05:38.400><c> but</c><00:05:38.520><c> if</c><00:05:38.639><c> you</c>

00:05:38.710 --> 00:05:38.720 align:start position:0%
soon on where files Go in ol but if you
 

00:05:38.720 --> 00:05:40.469 align:start position:0%
soon on where files Go in ol but if you
want<00:05:38.840><c> to</c><00:05:39.039><c> figure</c><00:05:39.240><c> out</c><00:05:39.479><c> how</c><00:05:39.600><c> to</c><00:05:39.960><c> redirect</c>

00:05:40.469 --> 00:05:40.479 align:start position:0%
want to figure out how to redirect
 

00:05:40.479 --> 00:05:41.950 align:start position:0%
want to figure out how to redirect
models<00:05:40.800><c> to</c><00:05:40.960><c> a</c><00:05:41.120><c> different</c><00:05:41.360><c> directory</c><00:05:41.720><c> on</c><00:05:41.840><c> your</c>

00:05:41.950 --> 00:05:41.960 align:start position:0%
models to a different directory on your
 

00:05:41.960 --> 00:05:44.309 align:start position:0%
models to a different directory on your
machine<00:05:42.919><c> look</c><00:05:43.120><c> into</c><00:05:43.400><c> using</c><00:05:43.800><c> environment</c>

00:05:44.309 --> 00:05:44.319 align:start position:0%
machine look into using environment
 

00:05:44.319 --> 00:05:47.029 align:start position:0%
machine look into using environment
variables<00:05:44.800><c> in</c><00:05:44.919><c> the</c><00:05:45.000><c> AMA</c><00:05:45.520><c> docs</c><00:05:46.520><c> it's</c><00:05:46.680><c> not</c><00:05:46.880><c> as</c>

00:05:47.029 --> 00:05:47.039 align:start position:0%
variables in the AMA docs it's not as
 

00:05:47.039 --> 00:05:48.830 align:start position:0%
variables in the AMA docs it's not as
easy<00:05:47.319><c> as</c><00:05:47.520><c> just</c><00:05:47.680><c> setting</c><00:05:48.000><c> up</c><00:05:48.160><c> an</c><00:05:48.360><c> environment</c>

00:05:48.830 --> 00:05:48.840 align:start position:0%
easy as just setting up an environment
 

00:05:48.840 --> 00:05:51.270 align:start position:0%
easy as just setting up an environment
variable<00:05:49.199><c> in</c><00:05:49.319><c> your</c><00:05:49.560><c> login</c><00:05:49.960><c> shell</c><00:05:50.840><c> some</c><00:05:51.039><c> folks</c>

00:05:51.270 --> 00:05:51.280 align:start position:0%
variable in your login shell some folks
 

00:05:51.280 --> 00:05:53.189 align:start position:0%
variable in your login shell some folks
will<00:05:51.479><c> try</c><00:05:51.639><c> to</c><00:05:51.759><c> solve</c><00:05:52.080><c> this</c><00:05:52.240><c> by</c><00:05:52.400><c> using</c><00:05:52.759><c> symbolic</c>

00:05:53.189 --> 00:05:53.199 align:start position:0%
will try to solve this by using symbolic
 

00:05:53.199 --> 00:05:55.550 align:start position:0%
will try to solve this by using symbolic
links<00:05:54.199><c> but</c><00:05:54.360><c> there</c><00:05:54.479><c> are</c><00:05:54.759><c> other</c><00:05:55.039><c> issues</c><00:05:55.400><c> that</c>

00:05:55.550 --> 00:05:55.560 align:start position:0%
links but there are other issues that
 

00:05:55.560 --> 00:05:57.749 align:start position:0%
links but there are other issues that
come<00:05:55.759><c> up</c><00:05:56.160><c> using</c><00:05:56.520><c> that</c><00:05:56.680><c> approach</c><00:05:57.600><c> the</c>

00:05:57.749 --> 00:05:57.759 align:start position:0%
come up using that approach the
 

00:05:57.759 --> 00:05:59.670 align:start position:0%
come up using that approach the
environment<00:05:58.280><c> variables</c><00:05:58.680><c> are</c><00:05:58.880><c> the</c><00:05:59.160><c> right</c><00:05:59.360><c> way</c>

00:05:59.670 --> 00:05:59.680 align:start position:0%
environment variables are the right way
 

00:05:59.680 --> 00:06:01.909 align:start position:0%
environment variables are the right way
to<00:05:59.880><c> go</c><00:06:00.880><c> and</c><00:06:01.000><c> that's</c><00:06:01.160><c> really</c><00:06:01.319><c> all</c><00:06:01.479><c> there</c><00:06:01.600><c> is</c><00:06:01.759><c> to</c>

00:06:01.909 --> 00:06:01.919 align:start position:0%
to go and that's really all there is to
 

00:06:01.919 --> 00:06:03.830 align:start position:0%
to go and that's really all there is to
it<00:06:02.080><c> getting</c><00:06:02.319><c> AMA</c><00:06:02.919><c> installed</c><00:06:03.400><c> and</c><00:06:03.560><c> up</c><00:06:03.680><c> and</c>

00:06:03.830 --> 00:06:03.840 align:start position:0%
it getting AMA installed and up and
 

00:06:03.840 --> 00:06:05.790 align:start position:0%
it getting AMA installed and up and
running<00:06:04.680><c> watch</c><00:06:04.840><c> out</c><00:06:05.000><c> for</c><00:06:05.120><c> the</c><00:06:05.240><c> next</c><00:06:05.440><c> video</c><00:06:05.680><c> in</c>

00:06:05.790 --> 00:06:05.800 align:start position:0%
running watch out for the next video in
 

00:06:05.800 --> 00:06:08.029 align:start position:0%
running watch out for the next video in
this<00:06:05.960><c> course</c><00:06:06.479><c> coming</c><00:06:06.720><c> soon</c><00:06:07.599><c> thanks</c><00:06:07.800><c> so</c><00:06:07.919><c> much</c>

00:06:08.029 --> 00:06:08.039 align:start position:0%
this course coming soon thanks so much
 

00:06:08.039 --> 00:06:12.199 align:start position:0%
this course coming soon thanks so much
for<00:06:08.240><c> watching</c><00:06:09.199><c> goodbye</c>

