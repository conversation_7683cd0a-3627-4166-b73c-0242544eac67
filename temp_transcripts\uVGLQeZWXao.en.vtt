WEBVTT
Kind: captions
Language: en

00:00:00.320 --> 00:00:02.110 align:start position:0%
 
hi<00:00:00.480><c> this</c><00:00:00.599><c> is</c><00:00:00.680><c> <PERSON>lorian</c><00:00:01.120><c> <PERSON><PERSON></c><00:00:01.480><c> with</c><00:00:01.680><c> GitHub</c>

00:00:02.110 --> 00:00:02.120 align:start position:0%
hi this is <PERSON><PERSON><PERSON> with GitHub
 

00:00:02.120 --> 00:00:04.070 align:start position:0%
hi this is <PERSON>lor<PERSON> with GitHub
and<00:00:02.240><c> I'm</c><00:00:02.399><c> going</c><00:00:02.600><c> to</c><00:00:02.800><c> show</c><00:00:03.000><c> you</c><00:00:03.360><c> how</c><00:00:03.480><c> to</c><00:00:03.600><c> use</c><00:00:03.840><c> the</c>

00:00:04.070 --> 00:00:04.080 align:start position:0%
and I'm going to show you how to use the
 

00:00:04.080 --> 00:00:06.789 align:start position:0%
and I'm going to show you how to use the
GitHub<00:00:04.680><c> co-pilot</c><00:00:05.400><c> chat</c><00:00:05.759><c> View</c><00:00:06.080><c> using</c><00:00:06.319><c> a</c><00:00:06.440><c> screen</c>

00:00:06.789 --> 00:00:06.799 align:start position:0%
GitHub co-pilot chat View using a screen
 

00:00:06.799 --> 00:00:09.629 align:start position:0%
GitHub co-pilot chat View using a screen
reader<00:00:07.319><c> I'm</c><00:00:07.439><c> using</c><00:00:07.680><c> mvid</c><00:00:08.160><c> 202</c><00:00:08.440><c> 24.1</c><00:00:09.440><c> on</c>

00:00:09.629 --> 00:00:09.639 align:start position:0%
reader I'm using mvid 202 24.1 on
 

00:00:09.639 --> 00:00:12.870 align:start position:0%
reader I'm using mvid 202 24.1 on
Windows<00:00:10.000><c> 11</c><00:00:10.360><c> 23</c><00:00:10.840><c> H2</c><00:00:11.480><c> and</c><00:00:11.639><c> the</c><00:00:11.880><c> GitHub</c><00:00:12.280><c> co-pilot</c>

00:00:12.870 --> 00:00:12.880 align:start position:0%
Windows 11 23 H2 and the GitHub co-pilot
 

00:00:12.880 --> 00:00:14.669 align:start position:0%
Windows 11 23 H2 and the GitHub co-pilot
chat<00:00:13.320><c> extension</c><00:00:14.040><c> which</c><00:00:14.160><c> is</c><00:00:14.280><c> different</c><00:00:14.559><c> from</c>

00:00:14.669 --> 00:00:14.679 align:start position:0%
chat extension which is different from
 

00:00:14.679 --> 00:00:16.470 align:start position:0%
chat extension which is different from
the<00:00:14.799><c> GitHub</c><00:00:15.120><c> co-pilot</c><00:00:15.599><c> extension</c><00:00:16.160><c> within</c>

00:00:16.470 --> 00:00:16.480 align:start position:0%
the GitHub co-pilot extension within
 

00:00:16.480 --> 00:00:18.910 align:start position:0%
the GitHub co-pilot extension within
Visual<00:00:16.720><c> Studio</c><00:00:17.080><c> code</c><00:00:17.680><c> 1.88</c><00:00:18.600><c> which</c><00:00:18.720><c> is</c><00:00:18.800><c> the</c>

00:00:18.910 --> 00:00:18.920 align:start position:0%
Visual Studio code 1.88 which is the
 

00:00:18.920 --> 00:00:21.590 align:start position:0%
Visual Studio code 1.88 which is the
March<00:00:19.199><c> 2024</c><00:00:19.880><c> release</c><00:00:20.640><c> first</c><00:00:20.960><c> we</c><00:00:21.080><c> need</c><00:00:21.240><c> to</c><00:00:21.400><c> make</c>

00:00:21.590 --> 00:00:21.600 align:start position:0%
March 2024 release first we need to make
 

00:00:21.600 --> 00:00:24.029 align:start position:0%
March 2024 release first we need to make
sure<00:00:21.920><c> we</c><00:00:22.119><c> have</c><00:00:22.359><c> the</c><00:00:22.640><c> separate</c><00:00:23.160><c> GitHub</c><00:00:23.519><c> copilot</c>

00:00:24.029 --> 00:00:24.039 align:start position:0%
sure we have the separate GitHub copilot
 

00:00:24.039 --> 00:00:25.910 align:start position:0%
sure we have the separate GitHub copilot
chat<00:00:24.279><c> extension</c><00:00:24.760><c> installed</c><00:00:25.439><c> once</c><00:00:25.640><c> this</c><00:00:25.760><c> is</c>

00:00:25.910 --> 00:00:25.920 align:start position:0%
chat extension installed once this is
 

00:00:25.920 --> 00:00:28.669 align:start position:0%
chat extension installed once this is
the<00:00:26.119><c> case</c><00:00:26.439><c> we</c><00:00:26.560><c> can</c><00:00:26.800><c> at</c><00:00:27.240><c> any</c><00:00:27.519><c> time</c><00:00:27.960><c> use</c><00:00:28.240><c> control</c>

00:00:28.669 --> 00:00:28.679 align:start position:0%
the case we can at any time use control
 

00:00:28.679 --> 00:00:31.230 align:start position:0%
the case we can at any time use control
shift<00:00:29.039><c> I</c><00:00:29.320><c> to</c><00:00:29.480><c> bring</c><00:00:29.679><c> up</c><00:00:29.840><c> the</c><00:00:30.320><c> dedicated</c><00:00:30.880><c> GitHub</c>

00:00:31.230 --> 00:00:31.240 align:start position:0%
shift I to bring up the dedicated GitHub
 

00:00:31.240 --> 00:00:33.670 align:start position:0%
shift I to bring up the dedicated GitHub
co-pilot<00:00:31.759><c> chat</c><00:00:32.079><c> view</c><00:00:32.680><c> okay</c><00:00:32.840><c> let's</c><00:00:33.079><c> ask</c><00:00:33.480><c> let's</c>

00:00:33.670 --> 00:00:33.680 align:start position:0%
co-pilot chat view okay let's ask let's
 

00:00:33.680 --> 00:00:35.150 align:start position:0%
co-pilot chat view okay let's ask let's
let's<00:00:33.800><c> show</c><00:00:34.079><c> that</c><00:00:34.239><c> off</c><00:00:34.480><c> by</c><00:00:34.640><c> actually</c><00:00:34.840><c> opening</c>

00:00:35.150 --> 00:00:35.160 align:start position:0%
let's show that off by actually opening
 

00:00:35.160 --> 00:00:36.990 align:start position:0%
let's show that off by actually opening
it<00:00:35.280><c> up</c><00:00:35.399><c> I'm</c><00:00:35.520><c> just</c><00:00:35.719><c> asking</c><00:00:36.079><c> GitHub</c><00:00:36.520><c> co-pilot</c>

00:00:36.990 --> 00:00:37.000 align:start position:0%
it up I'm just asking GitHub co-pilot
 

00:00:37.000 --> 00:00:42.389 align:start position:0%
it up I'm just asking GitHub co-pilot
chat<00:00:37.200><c> a</c><00:00:37.320><c> general</c><00:00:37.680><c> coding</c><00:00:38.360><c> question</c><00:00:39.360><c> question</c>

00:00:42.389 --> 00:00:42.399 align:start position:0%
 
 

00:00:42.399 --> 00:00:45.029 align:start position:0%
 
topicity<00:00:43.399><c> we</c><00:00:43.480><c> are</c><00:00:43.640><c> now</c><00:00:43.840><c> in</c><00:00:43.960><c> the</c><00:00:44.200><c> GitHub</c><00:00:44.559><c> c-al</c>

00:00:45.029 --> 00:00:45.039 align:start position:0%
topicity we are now in the GitHub c-al
 

00:00:45.039 --> 00:00:47.470 align:start position:0%
topicity we are now in the GitHub c-al
chat<00:00:45.320><c> view</c><00:00:45.719><c> let</c><00:00:45.840><c> me</c><00:00:46.079><c> ask</c><00:00:46.360><c> it</c><00:00:46.800><c> what</c><00:00:47.000><c> programming</c>

00:00:47.470 --> 00:00:47.480 align:start position:0%
chat view let me ask it what programming
 

00:00:47.480 --> 00:00:49.150 align:start position:0%
chat view let me ask it what programming
language<00:00:47.840><c> is</c><00:00:48.000><c> the</c><00:00:48.160><c> best</c><00:00:48.399><c> for</c><00:00:48.600><c> a</c><00:00:48.760><c> certain</c>

00:00:49.150 --> 00:00:49.160 align:start position:0%
language is the best for a certain
 

00:00:49.160 --> 00:00:51.270 align:start position:0%
language is the best for a certain
problem<00:00:49.559><c> what</c><00:00:49.680><c> program</c><00:00:50.079><c> language</c><00:00:50.640><c> for</c>

00:00:51.270 --> 00:00:51.280 align:start position:0%
problem what program language for
 

00:00:51.280 --> 00:00:54.950 align:start position:0%
problem what program language for
writing<00:00:51.920><c> a</c><00:00:52.280><c> mobile</c><00:00:53.280><c> application</c><00:00:54.280><c> that</c><00:00:54.520><c> talks</c>

00:00:54.950 --> 00:00:54.960 align:start position:0%
writing a mobile application that talks
 

00:00:54.960 --> 00:00:59.349 align:start position:0%
writing a mobile application that talks
to<00:00:55.399><c> a</c><00:00:55.960><c> backend</c><00:00:56.840><c> API</c><00:00:57.480><c> on</c><00:00:57.680><c> the</c><00:00:57.879><c> web</c><00:00:58.600><c> chat</c><00:00:58.760><c> request</c>

00:00:59.349 --> 00:00:59.359 align:start position:0%
to a backend API on the web chat request
 

00:00:59.359 --> 00:01:00.509 align:start position:0%
to a backend API on the web chat request
chat<00:00:59.519><c> respon</c><00:01:00.120><c> there</c><00:01:00.239><c> are</c><00:01:00.320><c> several</c>

00:01:00.509 --> 00:01:00.519 align:start position:0%
chat respon there are several
 

00:01:00.519 --> 00:01:01.430 align:start position:0%
chat respon there are several
programming<00:01:00.800><c> languages</c><00:01:01.039><c> that</c><00:01:01.120><c> are</c><00:01:01.160><c> welled</c>

00:01:01.430 --> 00:01:01.440 align:start position:0%
programming languages that are welled
 

00:01:01.440 --> 00:01:02.310 align:start position:0%
programming languages that are welled
for<00:01:01.480><c> writing</c><00:01:01.640><c> a</c><00:01:01.680><c> mobile</c><00:01:01.879><c> application</c><00:01:02.239><c> that</c>

00:01:02.310 --> 00:01:02.320 align:start position:0%
for writing a mobile application that
 

00:01:02.320 --> 00:01:03.470 align:start position:0%
for writing a mobile application that
communicates<00:01:02.640><c> with</c><00:01:02.719><c> a</c><00:01:02.800><c> back</c><00:01:03.000><c> API</c><00:01:03.239><c> on</c><00:01:03.320><c> the</c><00:01:03.359><c> web</c>

00:01:03.470 --> 00:01:03.480 align:start position:0%
communicates with a back API on the web
 

00:01:03.480 --> 00:01:04.670 align:start position:0%
communicates with a back API on the web
here<00:01:03.559><c> are</c><00:01:03.640><c> a</c><00:01:03.680><c> few</c><00:01:03.840><c> one</c><00:01:04.000><c> star</c><00:01:04.159><c> star</c><00:01:04.280><c> JavaScript</c>

00:01:04.670 --> 00:01:04.680 align:start position:0%
here are a few one star star JavaScript
 

00:01:04.680 --> 00:01:05.750 align:start position:0%
here are a few one star star JavaScript
react<00:01:04.839><c> native</c><00:01:05.040><c> star</c><00:01:05.199><c> star</c><00:01:05.320><c> react</c><00:01:05.479><c> native</c><00:01:05.680><c> is</c><00:01:05.720><c> a</c>

00:01:05.750 --> 00:01:05.760 align:start position:0%
react native star star react native is a
 

00:01:05.760 --> 00:01:06.590 align:start position:0%
react native star star react native is a
popular<00:01:05.960><c> framework</c><00:01:06.200><c> for</c><00:01:06.240><c> building</c><00:01:06.439><c> mobile</c>

00:01:06.590 --> 00:01:06.600 align:start position:0%
popular framework for building mobile
 

00:01:06.600 --> 00:01:07.590 align:start position:0%
popular framework for building mobile
applications<00:01:07.000><c> that</c><00:01:07.080><c> can</c><00:01:07.159><c> communicate</c><00:01:07.479><c> with</c><00:01:07.520><c> a</c>

00:01:07.590 --> 00:01:07.600 align:start position:0%
applications that can communicate with a
 

00:01:07.600 --> 00:01:08.789 align:start position:0%
applications that can communicate with a
back<00:01:07.799><c> AP</c><00:01:08.040><c> it</c><00:01:08.080><c> allows</c><00:01:08.240><c> you</c><00:01:08.320><c> to</c><00:01:08.400><c> write</c><00:01:08.479><c> code</c><00:01:08.600><c> on</c>

00:01:08.789 --> 00:01:08.799 align:start position:0%
back AP it allows you to write code on
 

00:01:08.799 --> 00:01:09.910 align:start position:0%
back AP it allows you to write code on
and<00:01:08.880><c> deployed</c><00:01:09.119><c> on</c><00:01:09.200><c> both</c><00:01:09.280><c> Android</c><00:01:09.479><c> and</c><00:01:09.560><c> iOS</c>

00:01:09.910 --> 00:01:09.920 align:start position:0%
and deployed on both Android and iOS
 

00:01:09.920 --> 00:01:11.230 align:start position:0%
and deployed on both Android and iOS
star<00:01:10.080><c> star</c><00:01:10.200><c> Java</c><00:01:10.720><c> for</c><00:01:10.759><c> Android</c><00:01:11.000><c> star</c><00:01:11.119><c> star</c>

00:01:11.230 --> 00:01:11.240 align:start position:0%
star star Java for Android star star
 

00:01:11.240 --> 00:01:12.030 align:start position:0%
star star Java for Android star star
Java<00:01:11.400><c> is</c><00:01:11.439><c> a</c><00:01:11.520><c> traditional</c><00:01:11.759><c> language</c><00:01:11.960><c> for</c>

00:01:12.030 --> 00:01:12.040 align:start position:0%
Java is a traditional language for
 

00:01:12.040 --> 00:01:13.070 align:start position:0%
Java is a traditional language for
Android<00:01:12.240><c> development</c><00:01:12.560><c> but</c><00:01:12.640><c> Cog</c><00:01:12.880><c> is</c><00:01:12.920><c> now</c><00:01:13.000><c> the</c>

00:01:13.070 --> 00:01:13.080 align:start position:0%
Android development but Cog is now the
 

00:01:13.080 --> 00:01:13.830 align:start position:0%
Android development but Cog is now the
preferred<00:01:13.280><c> language</c><00:01:13.479><c> for</c><00:01:13.560><c> new</c><00:01:13.640><c> Android</c>

00:01:13.830 --> 00:01:13.840 align:start position:0%
preferred language for new Android
 

00:01:13.840 --> 00:01:15.030 align:start position:0%
preferred language for new Android
development<00:01:14.159><c> free</c><00:01:14.320><c> star</c><00:01:14.479><c> star</c><00:01:14.600><c> s</c><00:01:14.759><c> for</c><00:01:14.840><c> iOS</c>

00:01:15.030 --> 00:01:15.040 align:start position:0%
development free star star s for iOS
 

00:01:15.040 --> 00:01:15.950 align:start position:0%
development free star star s for iOS
star<00:01:15.159><c> star</c><00:01:15.280><c> s</c><00:01:15.439><c> is</c><00:01:15.520><c> the</c><00:01:15.560><c> preferred</c><00:01:15.759><c> language</c>

00:01:15.950 --> 00:01:15.960 align:start position:0%
star star s is the preferred language
 

00:01:15.960 --> 00:01:17.149 align:start position:0%
star star s is the preferred language
for<00:01:16.040><c> iOS</c><00:01:16.280><c> development</c><00:01:16.560><c> for</c><00:01:16.720><c> star</c><00:01:16.880><c> star</c><00:01:17.040><c> dark</c>

00:01:17.149 --> 00:01:17.159 align:start position:0%
for iOS development for star star dark
 

00:01:17.159 --> 00:01:18.070 align:start position:0%
for iOS development for star star dark
flutter<00:01:17.439><c> star</c><00:01:17.560><c> star</c><00:01:17.680><c> flutter</c><00:01:17.880><c> is</c><00:01:17.920><c> a</c><00:01:17.960><c> new</c>

00:01:18.070 --> 00:01:18.080 align:start position:0%
flutter star star flutter is a new
 

00:01:18.080 --> 00:01:18.990 align:start position:0%
flutter star star flutter is a new
framework<00:01:18.360><c> that</c><00:01:18.400><c> allows</c><00:01:18.600><c> you</c><00:01:18.640><c> to</c><00:01:18.720><c> write</c><00:01:18.840><c> code</c>

00:01:18.990 --> 00:01:19.000 align:start position:0%
framework that allows you to write code
 

00:01:19.000 --> 00:01:20.109 align:start position:0%
framework that allows you to write code
once<00:01:19.159><c> and</c><00:01:19.240><c> Deploy</c><00:01:19.439><c> on</c><00:01:19.520><c> both</c><00:01:19.640><c> Android</c><00:01:19.840><c> and</c><00:01:19.920><c> iOS</c>

00:01:20.109 --> 00:01:20.119 align:start position:0%
once and Deploy on both Android and iOS
 

00:01:20.119 --> 00:01:20.910 align:start position:0%
once and Deploy on both Android and iOS
that<00:01:20.200><c> uses</c><00:01:20.360><c> a</c><00:01:20.400><c> dark</c><00:01:20.520><c> language</c><00:01:20.759><c> the</c><00:01:20.840><c> best</c>

00:01:20.910 --> 00:01:20.920 align:start position:0%
that uses a dark language the best
 

00:01:20.920 --> 00:01:21.870 align:start position:0%
that uses a dark language the best
choice<00:01:21.079><c> depends</c><00:01:21.240><c> on</c><00:01:21.320><c> your</c><00:01:21.400><c> specific</c><00:01:21.640><c> need</c><00:01:21.799><c> the</c>

00:01:21.870 --> 00:01:21.880 align:start position:0%
choice depends on your specific need the
 

00:01:21.880 --> 00:01:22.749 align:start position:0%
choice depends on your specific need the
skills<00:01:22.040><c> of</c><00:01:22.079><c> your</c><00:01:22.159><c> development</c><00:01:22.439><c> team</c><00:01:22.600><c> and</c><00:01:22.680><c> the</c>

00:01:22.749 --> 00:01:22.759 align:start position:0%
skills of your development team and the
 

00:01:22.759 --> 00:01:24.230 align:start position:0%
skills of your development team and the
platform<00:01:23.000><c> you</c><00:01:23.079><c> want</c><00:01:23.200><c> to</c><00:01:23.280><c> Target</c><00:01:23.759><c> once</c><00:01:23.960><c> in</c><00:01:24.079><c> this</c>

00:01:24.230 --> 00:01:24.240 align:start position:0%
platform you want to Target once in this
 

00:01:24.240 --> 00:01:26.510 align:start position:0%
platform you want to Target once in this
view<00:01:24.640><c> we</c><00:01:24.759><c> can</c><00:01:24.960><c> provide</c><00:01:25.280><c> GitHub</c><00:01:25.640><c> co-pilot</c><00:01:26.119><c> chat</c>

00:01:26.510 --> 00:01:26.520 align:start position:0%
view we can provide GitHub co-pilot chat
 

00:01:26.520 --> 00:01:28.670 align:start position:0%
view we can provide GitHub co-pilot chat
context<00:01:27.040><c> by</c><00:01:27.200><c> providing</c><00:01:27.520><c> the</c><00:01:27.720><c> at</c><00:01:28.079><c> workspace</c>

00:01:28.670 --> 00:01:28.680 align:start position:0%
context by providing the at workspace
 

00:01:28.680 --> 00:01:30.510 align:start position:0%
context by providing the at workspace
tag<00:01:29.240><c> to</c><00:01:29.400><c> indicate</c><00:01:29.759><c> our</c><00:01:29.920><c> our</c><00:01:30.079><c> question</c><00:01:30.360><c> is</c>

00:01:30.510 --> 00:01:30.520 align:start position:0%
tag to indicate our our question is
 

00:01:30.520 --> 00:01:32.510 align:start position:0%
tag to indicate our our question is
about<00:01:30.759><c> the</c><00:01:30.920><c> currently</c><00:01:31.360><c> opened</c><00:01:31.759><c> workspace</c>

00:01:32.510 --> 00:01:32.520 align:start position:0%
about the currently opened workspace
 

00:01:32.520 --> 00:01:34.230 align:start position:0%
about the currently opened workspace
when<00:01:32.720><c> asked</c><00:01:32.960><c> a</c><00:01:33.159><c> question</c><00:01:33.640><c> with</c><00:01:33.799><c> the</c><00:01:33.920><c> newly</c>

00:01:34.230 --> 00:01:34.240 align:start position:0%
when asked a question with the newly
 

00:01:34.240 --> 00:01:36.950 align:start position:0%
when asked a question with the newly
added<00:01:34.600><c> at</c><00:01:34.920><c> workspace</c><00:01:35.520><c> tag</c><00:01:36.079><c> getup</c><00:01:36.439><c> co-pilot</c>

00:01:36.950 --> 00:01:36.960 align:start position:0%
added at workspace tag getup co-pilot
 

00:01:36.960 --> 00:01:38.789 align:start position:0%
added at workspace tag getup co-pilot
chat<00:01:37.280><c> will</c><00:01:37.439><c> determine</c><00:01:38.119><c> what</c><00:01:38.320><c> part</c><00:01:38.520><c> of</c><00:01:38.640><c> your</c>

00:01:38.789 --> 00:01:38.799 align:start position:0%
chat will determine what part of your
 

00:01:38.799 --> 00:01:40.389 align:start position:0%
chat will determine what part of your
workspace<00:01:39.320><c> is</c><00:01:39.520><c> relevant</c><00:01:39.840><c> to</c><00:01:40.000><c> the</c><00:01:40.159><c> question</c>

00:01:40.389 --> 00:01:40.399 align:start position:0%
workspace is relevant to the question
 

00:01:40.399 --> 00:01:43.429 align:start position:0%
workspace is relevant to the question
you<00:01:40.640><c> asked</c><00:01:41.119><c> and</c><00:01:41.280><c> answer</c><00:01:41.680><c> appropriately</c><00:01:42.680><c> at</c>

00:01:43.429 --> 00:01:43.439 align:start position:0%
you asked and answer appropriately at
 

00:01:43.439 --> 00:01:47.030 align:start position:0%
you asked and answer appropriately at
workspace<00:01:44.439><c> how</c><00:01:44.719><c> would</c><00:01:44.960><c> we</c><00:01:45.240><c> call</c><00:01:45.719><c> the</c><00:01:46.040><c> function</c>

00:01:47.030 --> 00:01:47.040 align:start position:0%
workspace how would we call the function
 

00:01:47.040 --> 00:01:50.550 align:start position:0%
workspace how would we call the function
that<00:01:47.240><c> is</c><00:01:47.520><c> in</c><00:01:48.280><c> this</c><00:01:49.040><c> workspace</c><00:01:50.040><c> chat</c><00:01:50.200><c> request</c>

00:01:50.550 --> 00:01:50.560 align:start position:0%
that is in this workspace chat request
 

00:01:50.560 --> 00:01:51.910 align:start position:0%
that is in this workspace chat request
deed<00:01:50.880><c> workpace</c><00:01:51.159><c> information</c><00:01:51.399><c> to</c><00:01:51.479><c> collect</c><00:01:51.719><c> add</c>

00:01:51.910 --> 00:01:51.920 align:start position:0%
deed workpace information to collect add
 

00:01:51.920 --> 00:01:53.149 align:start position:0%
deed workpace information to collect add
workpace<00:01:52.200><c> info</c><00:01:52.479><c> chat</c><00:01:52.680><c> respon</c><00:01:52.920><c> pending</c>

00:01:53.149 --> 00:01:53.159 align:start position:0%
workpace info chat respon pending
 

00:01:53.159 --> 00:01:54.389 align:start position:0%
workpace info chat respon pending
determined<00:01:53.479><c> workpace</c><00:01:53.719><c> structure</c><00:01:53.960><c> the</c><00:01:54.280><c> work</c>

00:01:54.389 --> 00:01:54.399 align:start position:0%
determined workpace structure the work
 

00:01:54.399 --> 00:01:55.630 align:start position:0%
determined workpace structure the work
space<00:01:54.560><c> information</c><00:01:54.799><c> that</c><00:01:55.240><c> work</c><00:01:55.360><c> space</c><00:01:55.520><c> INF</c>

00:01:55.630 --> 00:01:55.640 align:start position:0%
space information that work space INF
 

00:01:55.640 --> 00:01:56.749 align:start position:0%
space information that work space INF
you<00:01:55.719><c> can</c><00:01:55.799><c> call</c><00:01:55.920><c> to</c><00:01:55.960><c> calculate</c><00:01:56.360><c> with</c><00:01:56.640><c> dat</c>

00:01:56.749 --> 00:01:56.759 align:start position:0%
you can call to calculate with dat
 

00:01:56.759 --> 00:01:58.069 align:start position:0%
you can call to calculate with dat
function<00:01:56.960><c> from</c>

00:01:58.069 --> 00:01:58.079 align:start position:0%
function from
 

00:01:58.079 --> 00:01:59.910 align:start position:0%
function from
us3s<00:01:59.079><c> like</c><00:01:59.200><c> this</c><00:01:59.280><c> JavaScript</c><00:01:59.640><c> let's</c><00:01:59.719><c> start</c>

00:01:59.910 --> 00:01:59.920 align:start position:0%
us3s like this JavaScript let's start
 

00:01:59.920 --> 00:02:02.109 align:start position:0%
us3s like this JavaScript let's start
equal<00:02:00.119><c> new</c><00:02:00.200><c> date</c><00:02:00.360><c> 2022</c><00:02:00.880><c> 0101</c><00:02:01.640><c> let</c><00:02:01.840><c> dat</c><00:02:01.960><c> equals</c>

00:02:02.109 --> 00:02:02.119 align:start position:0%
equal new date 2022 0101 let dat equals
 

00:02:02.119 --> 00:02:03.950 align:start position:0%
equal new date 2022 0101 let dat equals
new<00:02:02.240><c> date</c><00:02:02.360><c> 2022</c><00:02:02.920><c> 1231</c><00:02:03.560><c> let</c><00:02:03.640><c> dat</c><00:02:03.799><c> equals</c>

00:02:03.950 --> 00:02:03.960 align:start position:0%
new date 2022 1231 let dat equals
 

00:02:03.960 --> 00:02:04.990 align:start position:0%
new date 2022 1231 let dat equals
calculate<00:02:04.240><c> days</c><00:02:04.360><c> between</c><00:02:04.520><c> two</c><00:02:04.640><c> dates</c><00:02:04.840><c> start</c>

00:02:04.990 --> 00:02:05.000 align:start position:0%
calculate days between two dates start
 

00:02:05.000 --> 00:02:05.950 align:start position:0%
calculate days between two dates start
date<00:02:05.159><c> and</c><00:02:05.280><c> date</c><00:02:05.439><c> this</c><00:02:05.560><c> will</c><00:02:05.640><c> calculate</c><00:02:05.920><c> the</c>

00:02:05.950 --> 00:02:05.960 align:start position:0%
date and date this will calculate the
 

00:02:05.960 --> 00:02:07.389 align:start position:0%
date and date this will calculate the
number<00:02:06.079><c> of</c><00:02:06.159><c> days</c><00:02:06.320><c> between</c><00:02:06.479><c> January</c><00:02:06.759><c> 1st</c><00:02:06.960><c> 2022</c>

00:02:07.389 --> 00:02:07.399 align:start position:0%
number of days between January 1st 2022
 

00:02:07.399 --> 00:02:08.869 align:start position:0%
number of days between January 1st 2022
and<00:02:07.479><c> December</c><00:02:07.719><c> 31st</c><00:02:08.080><c> 2022</c><00:02:08.560><c> make</c><00:02:08.640><c> sure</c><00:02:08.759><c> to</c>

00:02:08.869 --> 00:02:08.879 align:start position:0%
and December 31st 2022 make sure to
 

00:02:08.879 --> 00:02:09.910 align:start position:0%
and December 31st 2022 make sure to
replace<00:02:09.080><c> the</c><00:02:09.119><c> DAT</c><00:02:09.239><c> with</c><00:02:09.319><c> your</c><00:02:09.360><c> desired</c><00:02:09.599><c> start</c>

00:02:09.910 --> 00:02:09.920 align:start position:0%
replace the DAT with your desired start
 

00:02:09.920 --> 00:02:11.910 align:start position:0%
replace the DAT with your desired start
base<00:02:10.399><c> okay</c><00:02:10.679><c> that's</c><00:02:10.879><c> great</c><00:02:11.239><c> we</c><00:02:11.319><c> can</c><00:02:11.480><c> look</c><00:02:11.640><c> into</c>

00:02:11.910 --> 00:02:11.920 align:start position:0%
base okay that's great we can look into
 

00:02:11.920 --> 00:02:14.430 align:start position:0%
base okay that's great we can look into
this<00:02:12.160><c> in</c><00:02:12.360><c> detail</c><00:02:12.760><c> by</c><00:02:12.879><c> using</c><00:02:13.120><c> the</c><00:02:13.239><c> alt</c><00:02:13.520><c> F2</c><00:02:14.200><c> key</c>

00:02:14.430 --> 00:02:14.440 align:start position:0%
this in detail by using the alt F2 key
 

00:02:14.440 --> 00:02:15.869 align:start position:0%
this in detail by using the alt F2 key
to<00:02:14.599><c> bring</c><00:02:14.760><c> up</c><00:02:14.920><c> the</c><00:02:15.040><c> accessible</c><00:02:15.400><c> view</c><00:02:15.560><c> for</c><00:02:15.680><c> this</c>

00:02:15.869 --> 00:02:15.879 align:start position:0%
to bring up the accessible view for this
 

00:02:15.879 --> 00:02:17.550 align:start position:0%
to bring up the accessible view for this
message<00:02:16.640><c> explor</c><00:02:16.959><c> the</c><00:02:17.040><c> ter</c><00:02:17.280><c> workpace</c>

00:02:17.550 --> 00:02:17.560 align:start position:0%
message explor the ter workpace
 

00:02:17.560 --> 00:02:18.990 align:start position:0%
message explor the ter workpace
structure<00:02:17.959><c> land</c><00:02:18.319><c> theed</c><00:02:18.599><c> with</c><00:02:18.680><c> work</c><00:02:18.840><c> space</c>

00:02:18.990 --> 00:02:19.000 align:start position:0%
structure land theed with work space
 

00:02:19.000 --> 00:02:20.830 align:start position:0%
structure land theed with work space
information<00:02:19.239><c> to</c><00:02:19.319><c> collect</c><00:02:20.200><c> work</c><00:02:20.360><c> space</c><00:02:20.480><c> info</c>

00:02:20.830 --> 00:02:20.840 align:start position:0%
information to collect work space info
 

00:02:20.840 --> 00:02:21.949 align:start position:0%
information to collect work space info
as<00:02:20.920><c> you</c><00:02:21.000><c> can</c><00:02:21.160><c> see</c><00:02:21.400><c> this</c><00:02:21.480><c> is</c><00:02:21.640><c> all</c><00:02:21.840><c> the</c>

00:02:21.949 --> 00:02:21.959 align:start position:0%
as you can see this is all the
 

00:02:21.959 --> 00:02:23.350 align:start position:0%
as you can see this is all the
information<00:02:22.360><c> we</c><00:02:22.480><c> were</c><00:02:22.640><c> told</c><00:02:22.879><c> about</c>

00:02:23.350 --> 00:02:23.360 align:start position:0%
information we were told about
 

00:02:23.360 --> 00:02:24.949 align:start position:0%
information we were told about
automatically<00:02:23.959><c> before</c><00:02:24.319><c> we</c><00:02:24.440><c> can</c><00:02:24.560><c> just</c><00:02:24.720><c> look</c><00:02:24.840><c> at</c>

00:02:24.949 --> 00:02:24.959 align:start position:0%
automatically before we can just look at
 

00:02:24.959 --> 00:02:28.070 align:start position:0%
automatically before we can just look at
it<00:02:25.120><c> in</c><00:02:25.280><c> detail</c><00:02:25.680><c> now</c><00:02:26.599><c> ask</c><00:02:26.760><c> questions</c><00:02:27.400><c> topics</c>

00:02:28.070 --> 00:02:28.080 align:start position:0%
it in detail now ask questions topics
 

00:02:28.080 --> 00:02:30.030 align:start position:0%
it in detail now ask questions topics
this<00:02:28.200><c> is</c><00:02:28.280><c> the</c><00:02:28.400><c> end</c><00:02:28.560><c> of</c><00:02:28.680><c> the</c><00:02:28.840><c> video</c><00:02:29.200><c> on</c><00:02:29.400><c> GitHub</c>

00:02:30.030 --> 00:02:30.040 align:start position:0%
this is the end of the video on GitHub
 

00:02:30.040 --> 00:02:32.030 align:start position:0%
this is the end of the video on GitHub
pilot<00:02:30.280><c> chat's</c><00:02:30.680><c> dedicated</c><00:02:31.120><c> view</c><00:02:31.360><c> with</c><00:02:31.519><c> inv</c><00:02:31.680><c> vs</c>

00:02:32.030 --> 00:02:32.040 align:start position:0%
pilot chat's dedicated view with inv vs
 

00:02:32.040 --> 00:02:33.750 align:start position:0%
pilot chat's dedicated view with inv vs
code<00:02:32.519><c> thank</c><00:02:32.680><c> you</c><00:02:32.760><c> for</c><00:02:32.959><c> watching</c><00:02:33.360><c> and</c><00:02:33.560><c> please</c>

00:02:33.750 --> 00:02:33.760 align:start position:0%
code thank you for watching and please
 

00:02:33.760 --> 00:02:35.190 align:start position:0%
code thank you for watching and please
be<00:02:33.879><c> on</c><00:02:34.000><c> the</c><00:02:34.120><c> lookout</c><00:02:34.440><c> for</c><00:02:34.599><c> more</c><00:02:34.760><c> videos</c><00:02:35.080><c> on</c>

00:02:35.190 --> 00:02:35.200 align:start position:0%
be on the lookout for more videos on
 

00:02:35.200 --> 00:02:38.680 align:start position:0%
be on the lookout for more videos on
this<00:02:35.400><c> topic</c><00:02:35.680><c> soon</c>

