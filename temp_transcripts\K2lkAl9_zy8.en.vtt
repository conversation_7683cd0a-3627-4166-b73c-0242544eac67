WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:01.230 align:start position:0%
 
today<00:00:00.280><c> I'm</c><00:00:00.399><c> going</c><00:00:00.480><c> to</c><00:00:00.560><c> show</c><00:00:00.719><c> you</c><00:00:00.799><c> how</c><00:00:00.919><c> we</c><00:00:01.040><c> can</c>

00:00:01.230 --> 00:00:01.240 align:start position:0%
today I'm going to show you how we can
 

00:00:01.240 --> 00:00:03.110 align:start position:0%
today I'm going to show you how we can
generate<00:00:01.640><c> images</c><00:00:01.959><c> using</c><00:00:02.200><c> stable</c><00:00:02.520><c> diffusion</c>

00:00:03.110 --> 00:00:03.120 align:start position:0%
generate images using stable diffusion
 

00:00:03.120 --> 00:00:05.470 align:start position:0%
generate images using stable diffusion
locally<00:00:03.719><c> with</c><00:00:04.000><c> AI</c><00:00:04.440><c> agents</c><00:00:05.120><c> and</c><00:00:05.240><c> how</c><00:00:05.359><c> we're</c>

00:00:05.470 --> 00:00:05.480 align:start position:0%
locally with AI agents and how we're
 

00:00:05.480 --> 00:00:06.710 align:start position:0%
locally with AI agents and how we're
going<00:00:05.560><c> to</c><00:00:05.720><c> do</c><00:00:05.920><c> this</c><00:00:06.040><c> is</c><00:00:06.160><c> we're</c><00:00:06.279><c> going</c><00:00:06.359><c> to</c><00:00:06.480><c> use</c>

00:00:06.710 --> 00:00:06.720 align:start position:0%
going to do this is we're going to use
 

00:00:06.720 --> 00:00:08.549 align:start position:0%
going to do this is we're going to use
hugging<00:00:07.120><c> face</c><00:00:07.399><c> to</c><00:00:07.640><c> get</c><00:00:07.799><c> the</c><00:00:08.000><c> model</c><00:00:08.400><c> if</c><00:00:08.480><c> you're</c>

00:00:08.549 --> 00:00:08.559 align:start position:0%
hugging face to get the model if you're
 

00:00:08.559 --> 00:00:10.230 align:start position:0%
hugging face to get the model if you're
not<00:00:08.679><c> used</c><00:00:08.840><c> to</c><00:00:08.920><c> hugging</c><00:00:09.240><c> face</c><00:00:09.519><c> they</c><00:00:09.679><c> have</c><00:00:09.920><c> over</c>

00:00:10.230 --> 00:00:10.240 align:start position:0%
not used to hugging face they have over
 

00:00:10.240 --> 00:00:12.950 align:start position:0%
not used to hugging face they have over
500<00:00:10.960><c> that</c><00:00:11.160><c> you</c><00:00:11.280><c> can</c><00:00:11.480><c> use</c><00:00:11.840><c> locally</c><00:00:12.320><c> to</c><00:00:12.440><c> do</c><00:00:12.679><c> many</c>

00:00:12.950 --> 00:00:12.960 align:start position:0%
500 that you can use locally to do many
 

00:00:12.960 --> 00:00:14.070 align:start position:0%
500 that you can use locally to do many
different<00:00:13.240><c> things</c><00:00:13.599><c> but</c><00:00:13.679><c> what</c><00:00:13.799><c> we're</c><00:00:13.880><c> going</c><00:00:13.960><c> to</c>

00:00:14.070 --> 00:00:14.080 align:start position:0%
different things but what we're going to
 

00:00:14.080 --> 00:00:16.189 align:start position:0%
different things but what we're going to
go<00:00:14.200><c> over</c><00:00:14.440><c> today</c><00:00:14.839><c> is</c><00:00:15.000><c> the</c><00:00:15.200><c> text</c><00:00:15.480><c> to</c><00:00:15.679><c> image</c><00:00:16.080><c> and</c>

00:00:16.189 --> 00:00:16.199 align:start position:0%
go over today is the text to image and
 

00:00:16.199 --> 00:00:17.310 align:start position:0%
go over today is the text to image and
I'm<00:00:16.279><c> going</c><00:00:16.359><c> to</c><00:00:16.480><c> show</c><00:00:16.640><c> you</c><00:00:16.840><c> the</c><00:00:17.000><c> stable</c>

00:00:17.310 --> 00:00:17.320 align:start position:0%
I'm going to show you the stable
 

00:00:17.320 --> 00:00:18.870 align:start position:0%
I'm going to show you the stable
diffusion<00:00:18.000><c> and</c><00:00:18.119><c> how</c><00:00:18.199><c> we're</c><00:00:18.320><c> going</c><00:00:18.400><c> to</c><00:00:18.560><c> do</c><00:00:18.720><c> this</c>

00:00:18.870 --> 00:00:18.880 align:start position:0%
diffusion and how we're going to do this
 

00:00:18.880 --> 00:00:20.470 align:start position:0%
diffusion and how we're going to do this
is<00:00:19.000><c> we're</c><00:00:19.080><c> going</c><00:00:19.199><c> to</c><00:00:19.279><c> use</c><00:00:19.560><c> hugging</c><00:00:19.960><c> face</c><00:00:20.240><c> to</c>

00:00:20.470 --> 00:00:20.480 align:start position:0%
is we're going to use hugging face to
 

00:00:20.480 --> 00:00:21.750 align:start position:0%
is we're going to use hugging face to
get<00:00:20.640><c> the</c><00:00:20.760><c> model</c><00:00:21.199><c> if</c><00:00:21.279><c> you're</c><00:00:21.400><c> not</c><00:00:21.480><c> used</c><00:00:21.640><c> to</c>

00:00:21.750 --> 00:00:21.760 align:start position:0%
get the model if you're not used to
 

00:00:21.760 --> 00:00:24.070 align:start position:0%
get the model if you're not used to
hugging<00:00:22.080><c> face</c><00:00:22.320><c> they</c><00:00:22.480><c> have</c><00:00:22.720><c> over</c><00:00:23.080><c> 500,000</c>

00:00:24.070 --> 00:00:24.080 align:start position:0%
hugging face they have over 500,000
 

00:00:24.080 --> 00:00:25.750 align:start position:0%
hugging face they have over 500,000
models<00:00:24.359><c> that</c><00:00:24.519><c> you</c><00:00:24.599><c> can</c><00:00:24.760><c> choose</c><00:00:25.039><c> from</c><00:00:25.400><c> popular</c>

00:00:25.750 --> 00:00:25.760 align:start position:0%
models that you can choose from popular
 

00:00:25.760 --> 00:00:27.429 align:start position:0%
models that you can choose from popular
this<00:00:25.880><c> trending</c><00:00:26.199><c> model</c><00:00:26.439><c> is</c><00:00:26.560><c> becoming</c><00:00:27.199><c> uh</c><00:00:27.320><c> I</c>

00:00:27.429 --> 00:00:27.439 align:start position:0%
this trending model is becoming uh I
 

00:00:27.439 --> 00:00:29.310 align:start position:0%
this trending model is becoming uh I
gave<00:00:27.560><c> it</c><00:00:27.679><c> a</c><00:00:27.840><c> prompt</c><00:00:28.119><c> to</c><00:00:28.480><c> give</c><00:00:28.599><c> me</c><00:00:28.720><c> an</c><00:00:28.920><c> astronaut</c>

00:00:29.310 --> 00:00:29.320 align:start position:0%
gave it a prompt to give me an astronaut
 

00:00:29.320 --> 00:00:31.390 align:start position:0%
gave it a prompt to give me an astronaut
riding<00:00:29.599><c> a</c><00:00:29.720><c> horse</c><00:00:30.080><c> in</c><00:00:30.279><c> space</c><00:00:30.800><c> and</c><00:00:30.880><c> it</c><00:00:31.039><c> came</c><00:00:31.199><c> up</c>

00:00:31.390 --> 00:00:31.400 align:start position:0%
riding a horse in space and it came up
 

00:00:31.400 --> 00:00:32.950 align:start position:0%
riding a horse in space and it came up
with<00:00:31.599><c> this</c><00:00:31.920><c> and</c><00:00:32.040><c> it</c><00:00:32.160><c> also</c><00:00:32.320><c> had</c><00:00:32.439><c> to</c><00:00:32.559><c> create</c><00:00:32.800><c> a</c>

00:00:32.950 --> 00:00:32.960 align:start position:0%
with this and it also had to create a
 

00:00:32.960 --> 00:00:34.790 align:start position:0%
with this and it also had to create a
cat<00:00:33.120><c> riding</c><00:00:33.399><c> a</c><00:00:33.520><c> rocket</c><00:00:33.920><c> around</c><00:00:34.160><c> a</c><00:00:34.360><c> big</c><00:00:34.520><c> city</c>

00:00:34.790 --> 00:00:34.800 align:start position:0%
cat riding a rocket around a big city
 

00:00:34.800 --> 00:00:36.869 align:start position:0%
cat riding a rocket around a big city
with<00:00:34.879><c> a</c><00:00:35.000><c> mouse</c><00:00:35.360><c> sidekick</c><00:00:35.920><c> the</c><00:00:36.040><c> mouse</c><00:00:36.320><c> might</c><00:00:36.480><c> be</c>

00:00:36.869 --> 00:00:36.879 align:start position:0%
with a mouse sidekick the mouse might be
 

00:00:36.879 --> 00:00:38.270 align:start position:0%
with a mouse sidekick the mouse might be
in<00:00:37.000><c> the</c><00:00:37.160><c> rocket</c><00:00:37.440><c> in</c><00:00:37.520><c> the</c><00:00:37.640><c> very</c><00:00:37.800><c> back</c><00:00:38.120><c> I'm</c><00:00:38.200><c> going</c>

00:00:38.270 --> 00:00:38.280 align:start position:0%
in the rocket in the very back I'm going
 

00:00:38.280 --> 00:00:39.830 align:start position:0%
in the rocket in the very back I'm going
to<00:00:38.399><c> have</c><00:00:38.520><c> the</c><00:00:38.719><c> user</c><00:00:39.000><c> agent</c><00:00:39.320><c> give</c><00:00:39.480><c> a</c><00:00:39.600><c> system</c>

00:00:39.830 --> 00:00:39.840 align:start position:0%
to have the user agent give a system
 

00:00:39.840 --> 00:00:41.310 align:start position:0%
to have the user agent give a system
message<00:00:40.079><c> to</c><00:00:40.200><c> the</c><00:00:40.399><c> assistant</c><00:00:40.800><c> agent</c><00:00:41.120><c> which</c><00:00:41.200><c> is</c>

00:00:41.310 --> 00:00:41.320 align:start position:0%
message to the assistant agent which is
 

00:00:41.320 --> 00:00:43.110 align:start position:0%
message to the assistant agent which is
basically<00:00:41.600><c> saying</c><00:00:42.039><c> come</c><00:00:42.239><c> up</c><00:00:42.440><c> with</c><00:00:42.640><c> some</c>

00:00:43.110 --> 00:00:43.120 align:start position:0%
basically saying come up with some
 

00:00:43.120 --> 00:00:44.830 align:start position:0%
basically saying come up with some
random<00:00:43.520><c> image</c><00:00:43.879><c> prompt</c><00:00:44.320><c> then</c><00:00:44.440><c> it's</c><00:00:44.559><c> going</c><00:00:44.640><c> to</c>

00:00:44.830 --> 00:00:44.840 align:start position:0%
random image prompt then it's going to
 

00:00:44.840 --> 00:00:46.670 align:start position:0%
random image prompt then it's going to
create<00:00:45.120><c> that</c><00:00:45.280><c> prompt</c><00:00:45.680><c> send</c><00:00:45.960><c> it</c><00:00:46.039><c> to</c><00:00:46.199><c> a</c><00:00:46.320><c> function</c>

00:00:46.670 --> 00:00:46.680 align:start position:0%
create that prompt send it to a function
 

00:00:46.680 --> 00:00:48.270 align:start position:0%
create that prompt send it to a function
that's<00:00:46.800><c> going</c><00:00:46.920><c> to</c><00:00:47.239><c> create</c><00:00:47.640><c> the</c><00:00:47.840><c> image</c><00:00:48.199><c> and</c>

00:00:48.270 --> 00:00:48.280 align:start position:0%
that's going to create the image and
 

00:00:48.280 --> 00:00:49.310 align:start position:0%
that's going to create the image and
then<00:00:48.360><c> we're</c><00:00:48.440><c> going</c><00:00:48.559><c> to</c><00:00:48.640><c> get</c><00:00:48.760><c> that</c><00:00:48.879><c> image</c><00:00:49.160><c> back</c>

00:00:49.310 --> 00:00:49.320 align:start position:0%
then we're going to get that image back
 

00:00:49.320 --> 00:00:50.750 align:start position:0%
then we're going to get that image back
and<00:00:49.480><c> output</c><00:00:49.800><c> it</c><00:00:50.000><c> you</c><00:00:50.079><c> can</c><00:00:50.199><c> use</c><00:00:50.360><c> something</c><00:00:50.600><c> like</c>

00:00:50.750 --> 00:00:50.760 align:start position:0%
and output it you can use something like
 

00:00:50.760 --> 00:00:53.430 align:start position:0%
and output it you can use something like
Ama<00:00:51.320><c> or</c><00:00:51.440><c> LM</c><00:00:51.760><c> Studio</c><00:00:52.079><c> to</c><00:00:52.239><c> have</c><00:00:52.359><c> a</c><00:00:52.520><c> local</c><00:00:52.840><c> server</c>

00:00:53.430 --> 00:00:53.440 align:start position:0%
Ama or LM Studio to have a local server
 

00:00:53.440 --> 00:00:55.590 align:start position:0%
Ama or LM Studio to have a local server
to<00:00:53.559><c> load</c><00:00:53.840><c> some</c><00:00:54.120><c> texttext</c><00:00:54.680><c> generation</c><00:00:55.160><c> model</c>

00:00:55.590 --> 00:00:55.600 align:start position:0%
to load some texttext generation model
 

00:00:55.600 --> 00:00:57.630 align:start position:0%
to load some texttext generation model
I'm<00:00:55.680><c> just</c><00:00:55.800><c> going</c><00:00:55.920><c> to</c><00:00:56.039><c> show</c><00:00:56.199><c> you</c><00:00:56.399><c> chat</c><00:00:56.640><c> GPT</c><00:00:57.079><c> 3.5</c>

00:00:57.630 --> 00:00:57.640 align:start position:0%
I'm just going to show you chat GPT 3.5
 

00:00:57.640 --> 00:00:59.470 align:start position:0%
I'm just going to show you chat GPT 3.5
turbo<00:00:58.039><c> which</c><00:00:58.160><c> is</c><00:00:58.399><c> super</c><00:00:58.680><c> cheap</c><00:00:59.079><c> just</c><00:00:59.199><c> so</c><00:00:59.320><c> it's</c>

00:00:59.470 --> 00:00:59.480 align:start position:0%
turbo which is super cheap just so it's
 

00:00:59.480 --> 00:01:01.069 align:start position:0%
turbo which is super cheap just so it's
quicker<00:00:59.680><c> for</c><00:00:59.760><c> me</c><00:00:59.960><c> me</c><00:01:00.079><c> locally</c><00:01:00.719><c> okay</c><00:01:00.840><c> so</c><00:01:00.960><c> now</c>

00:01:01.069 --> 00:01:01.079 align:start position:0%
quicker for me me locally okay so now
 

00:01:01.079 --> 00:01:02.430 align:start position:0%
quicker for me me locally okay so now
for<00:01:01.199><c> the</c><00:01:01.320><c> coding</c><00:01:01.680><c> part</c><00:01:02.039><c> the</c><00:01:02.120><c> first</c><00:01:02.280><c> thing</c><00:01:02.359><c> you</c>

00:01:02.430 --> 00:01:02.440 align:start position:0%
for the coding part the first thing you
 

00:01:02.440 --> 00:01:03.389 align:start position:0%
for the coding part the first thing you
need<00:01:02.559><c> to</c><00:01:02.680><c> do</c><00:01:02.800><c> is</c><00:01:02.920><c> install</c><00:01:03.199><c> all</c><00:01:03.320><c> the</c>

00:01:03.389 --> 00:01:03.399 align:start position:0%
need to do is install all the
 

00:01:03.399 --> 00:01:04.630 align:start position:0%
need to do is install all the
requirements<00:01:04.000><c> I'm</c><00:01:04.119><c> going</c><00:01:04.239><c> to</c><00:01:04.360><c> have</c><00:01:04.479><c> a</c>

00:01:04.630 --> 00:01:04.640 align:start position:0%
requirements I'm going to have a
 

00:01:04.640 --> 00:01:06.710 align:start position:0%
requirements I'm going to have a
requirements.<00:01:05.479><c> text</c><00:01:06.080><c> file</c><00:01:06.320><c> so</c><00:01:06.479><c> that</c><00:01:06.560><c> all</c><00:01:06.640><c> you</c>

00:01:06.710 --> 00:01:06.720 align:start position:0%
requirements. text file so that all you
 

00:01:06.720 --> 00:01:08.429 align:start position:0%
requirements. text file so that all you
have<00:01:06.799><c> to</c><00:01:06.920><c> type</c><00:01:07.080><c> in</c><00:01:07.280><c> in</c><00:01:07.360><c> your</c><00:01:07.520><c> terminal</c><00:01:08.040><c> is</c><00:01:08.240><c> PIP</c>

00:01:08.429 --> 00:01:08.439 align:start position:0%
have to type in in your terminal is PIP
 

00:01:08.439 --> 00:01:10.550 align:start position:0%
have to type in in your terminal is PIP
install<00:01:08.840><c> -</c><00:01:09.119><c> R</c><00:01:09.360><c> requirements.</c><00:01:10.040><c> text</c><00:01:10.479><c> and</c>

00:01:10.550 --> 00:01:10.560 align:start position:0%
install - R requirements. text and
 

00:01:10.560 --> 00:01:11.910 align:start position:0%
install - R requirements. text and
you'll<00:01:10.720><c> be</c><00:01:10.880><c> good</c><00:01:11.000><c> to</c><00:01:11.159><c> go</c><00:01:11.439><c> once</c><00:01:11.560><c> you</c><00:01:11.640><c> have</c><00:01:11.759><c> all</c>

00:01:11.910 --> 00:01:11.920 align:start position:0%
you'll be good to go once you have all
 

00:01:11.920 --> 00:01:13.749 align:start position:0%
you'll be good to go once you have all
the<00:01:12.040><c> Imports</c><00:01:12.600><c> here</c><00:01:13.080><c> the</c><00:01:13.200><c> next</c><00:01:13.360><c> thing</c><00:01:13.520><c> is</c><00:01:13.640><c> we</c>

00:01:13.749 --> 00:01:13.759 align:start position:0%
the Imports here the next thing is we
 

00:01:13.759 --> 00:01:16.630 align:start position:0%
the Imports here the next thing is we
need<00:01:13.880><c> the</c><00:01:14.040><c> API</c><00:01:14.520><c> URL</c><00:01:15.200><c> and</c><00:01:15.400><c> the</c><00:01:15.560><c> headers</c><00:01:16.360><c> which</c><00:01:16.520><c> I</c>

00:01:16.630 --> 00:01:16.640 align:start position:0%
need the API URL and the headers which I
 

00:01:16.640 --> 00:01:18.350 align:start position:0%
need the API URL and the headers which I
got<00:01:16.920><c> directly</c><00:01:17.320><c> from</c><00:01:17.479><c> that</c><00:01:17.680><c> hugging</c><00:01:18.040><c> face</c>

00:01:18.350 --> 00:01:18.360 align:start position:0%
got directly from that hugging face
 

00:01:18.360 --> 00:01:19.830 align:start position:0%
got directly from that hugging face
deploy<00:01:19.000><c> and</c><00:01:19.119><c> because</c><00:01:19.320><c> we</c><00:01:19.400><c> are</c><00:01:19.520><c> going</c><00:01:19.640><c> to</c><00:01:19.720><c> be</c>

00:01:19.830 --> 00:01:19.840 align:start position:0%
deploy and because we are going to be
 

00:01:19.840 --> 00:01:21.670 align:start position:0%
deploy and because we are going to be
using<00:01:20.079><c> autogen</c><00:01:21.000><c> and</c><00:01:21.159><c> we're</c><00:01:21.280><c> going</c><00:01:21.360><c> to</c><00:01:21.439><c> have</c><00:01:21.560><c> an</c>

00:01:21.670 --> 00:01:21.680 align:start position:0%
using autogen and we're going to have an
 

00:01:21.680 --> 00:01:24.310 align:start position:0%
using autogen and we're going to have an
AI<00:01:22.000><c> agent</c><00:01:22.400><c> we</c><00:01:22.520><c> need</c><00:01:22.720><c> an</c><00:01:22.880><c> llm</c><00:01:23.439><c> configuration</c><00:01:24.200><c> so</c>

00:01:24.310 --> 00:01:24.320 align:start position:0%
AI agent we need an llm configuration so
 

00:01:24.320 --> 00:01:26.310 align:start position:0%
AI agent we need an llm configuration so
we're<00:01:24.439><c> going</c><00:01:24.520><c> to</c><00:01:24.600><c> say</c><00:01:24.759><c> LL</c><00:01:25.119><c> config</c><00:01:25.520><c> is</c><00:01:25.680><c> equal</c><00:01:25.920><c> to</c>

00:01:26.310 --> 00:01:26.320 align:start position:0%
we're going to say LL config is equal to
 

00:01:26.320 --> 00:01:27.390 align:start position:0%
we're going to say LL config is equal to
and<00:01:26.439><c> then</c><00:01:26.560><c> we're</c><00:01:26.680><c> going</c><00:01:26.759><c> to</c><00:01:26.840><c> have</c><00:01:26.960><c> the</c><00:01:27.040><c> config</c>

00:01:27.390 --> 00:01:27.400 align:start position:0%
and then we're going to have the config
 

00:01:27.400 --> 00:01:29.390 align:start position:0%
and then we're going to have the config
list<00:01:27.640><c> property</c><00:01:28.360><c> which</c><00:01:28.479><c> is</c><00:01:28.640><c> where</c><00:01:28.799><c> we</c><00:01:28.960><c> retrieve</c>

00:01:29.390 --> 00:01:29.400 align:start position:0%
list property which is where we retrieve
 

00:01:29.400 --> 00:01:31.910 align:start position:0%
list property which is where we retrieve
the<00:01:29.560><c> config</c><00:01:30.079><c> list</c><00:01:30.360><c> from</c><00:01:30.600><c> Json</c><00:01:31.320><c> which</c><00:01:31.439><c> is</c><00:01:31.640><c> over</c>

00:01:31.910 --> 00:01:31.920 align:start position:0%
the config list from Json which is over
 

00:01:31.920 --> 00:01:34.469 align:start position:0%
the config list from Json which is over
here<00:01:32.159><c> we</c><00:01:32.280><c> have</c><00:01:32.399><c> an</c><00:01:32.560><c> oi</c><00:01:33.040><c> config</c><00:01:33.439><c> list.</c><00:01:34.079><c> Json</c>

00:01:34.469 --> 00:01:34.479 align:start position:0%
here we have an oi config list. Json
 

00:01:34.479 --> 00:01:36.310 align:start position:0%
here we have an oi config list. Json
file<00:01:34.759><c> and</c><00:01:34.920><c> all</c><00:01:35.079><c> this</c><00:01:35.360><c> has</c><00:01:35.640><c> is</c><00:01:35.759><c> the</c><00:01:35.880><c> model</c><00:01:36.200><c> the</c>

00:01:36.310 --> 00:01:36.320 align:start position:0%
file and all this has is the model the
 

00:01:36.320 --> 00:01:37.630 align:start position:0%
file and all this has is the model the
API<00:01:36.799><c> key</c><00:01:37.119><c> now</c><00:01:37.200><c> if</c><00:01:37.240><c> you're</c><00:01:37.360><c> going</c><00:01:37.439><c> to</c><00:01:37.479><c> use</c>

00:01:37.630 --> 00:01:37.640 align:start position:0%
API key now if you're going to use
 

00:01:37.640 --> 00:01:39.389 align:start position:0%
API key now if you're going to use
something<00:01:37.799><c> like</c><00:01:37.960><c> LM</c><00:01:38.280><c> Studio</c><00:01:38.600><c> or</c><00:01:38.720><c> oama</c><00:01:39.320><c> you</c>

00:01:39.389 --> 00:01:39.399 align:start position:0%
something like LM Studio or oama you
 

00:01:39.399 --> 00:01:41.670 align:start position:0%
something like LM Studio or oama you
want<00:01:39.520><c> to</c><00:01:39.640><c> put</c><00:01:39.759><c> the</c><00:01:39.920><c> base</c><00:01:40.159><c> URL</c><00:01:40.720><c> in</c><00:01:40.960><c> here</c><00:01:41.280><c> as</c><00:01:41.399><c> well</c>

00:01:41.670 --> 00:01:41.680 align:start position:0%
want to put the base URL in here as well
 

00:01:41.680 --> 00:01:43.789 align:start position:0%
want to put the base URL in here as well
and<00:01:41.799><c> with</c><00:01:41.960><c> oama</c><00:01:42.520><c> you</c><00:01:42.640><c> need</c><00:01:42.880><c> the</c><00:01:43.079><c> exact</c><00:01:43.399><c> model</c>

00:01:43.789 --> 00:01:43.799 align:start position:0%
and with oama you need the exact model
 

00:01:43.799 --> 00:01:45.910 align:start position:0%
and with oama you need the exact model
then<00:01:43.880><c> you</c><00:01:44.040><c> set</c><00:01:44.200><c> the</c><00:01:44.280><c> temperature</c><00:01:44.640><c> to</c><00:01:44.719><c> 0.5</c><00:01:45.719><c> and</c>

00:01:45.910 --> 00:01:45.920 align:start position:0%
then you set the temperature to 0.5 and
 

00:01:45.920 --> 00:01:47.109 align:start position:0%
then you set the temperature to 0.5 and
I<00:01:46.040><c> just</c><00:01:46.119><c> have</c><00:01:46.240><c> a</c><00:01:46.320><c> seed</c><00:01:46.560><c> number</c><00:01:46.719><c> so</c><00:01:46.960><c> I</c><00:01:47.040><c> can</c>

00:01:47.109 --> 00:01:47.119 align:start position:0%
I just have a seed number so I can
 

00:01:47.119 --> 00:01:48.910 align:start position:0%
I just have a seed number so I can
change<00:01:47.320><c> it</c><00:01:47.479><c> easier</c><00:01:47.960><c> and</c><00:01:48.079><c> the</c><00:01:48.159><c> workflow</c><00:01:48.640><c> here</c>

00:01:48.910 --> 00:01:48.920 align:start position:0%
change it easier and the workflow here
 

00:01:48.920 --> 00:01:50.789 align:start position:0%
change it easier and the workflow here
is<00:01:49.159><c> pretty</c><00:01:49.399><c> simple</c><00:01:49.840><c> we</c><00:01:49.960><c> have</c><00:01:50.159><c> two</c><00:01:50.360><c> agents</c><00:01:50.680><c> we</c>

00:01:50.789 --> 00:01:50.799 align:start position:0%
is pretty simple we have two agents we
 

00:01:50.799 --> 00:01:52.069 align:start position:0%
is pretty simple we have two agents we
have<00:01:50.880><c> an</c><00:01:51.000><c> assistant</c><00:01:51.320><c> agent</c><00:01:51.640><c> with</c><00:01:51.759><c> just</c><00:01:51.920><c> the</c>

00:01:52.069 --> 00:01:52.079 align:start position:0%
have an assistant agent with just the
 

00:01:52.079 --> 00:01:54.030 align:start position:0%
have an assistant agent with just the
name<00:01:52.439><c> and</c><00:01:52.520><c> then</c><00:01:52.640><c> the</c><00:01:52.759><c> llm</c><00:01:53.240><c> config</c><00:01:53.880><c> if</c><00:01:53.960><c> you</c>

00:01:54.030 --> 00:01:54.040 align:start position:0%
name and then the llm config if you
 

00:01:54.040 --> 00:01:55.310 align:start position:0%
name and then the llm config if you
didn't<00:01:54.200><c> know</c><00:01:54.399><c> this</c><00:01:54.560><c> if</c><00:01:54.640><c> you</c><00:01:54.759><c> don't</c><00:01:55.040><c> have</c><00:01:55.159><c> a</c>

00:01:55.310 --> 00:01:55.320 align:start position:0%
didn't know this if you don't have a
 

00:01:55.320 --> 00:01:56.910 align:start position:0%
didn't know this if you don't have a
system<00:01:55.600><c> message</c><00:01:55.880><c> for</c><00:01:56.000><c> an</c><00:01:56.119><c> assistant</c><00:01:56.439><c> agent</c>

00:01:56.910 --> 00:01:56.920 align:start position:0%
system message for an assistant agent
 

00:01:56.920 --> 00:01:58.550 align:start position:0%
system message for an assistant agent
that's<00:01:57.119><c> perfectly</c><00:01:57.600><c> okay</c><00:01:57.799><c> because</c><00:01:58.000><c> it</c><00:01:58.119><c> has</c><00:01:58.280><c> one</c>

00:01:58.550 --> 00:01:58.560 align:start position:0%
that's perfectly okay because it has one
 

00:01:58.560 --> 00:02:00.389 align:start position:0%
that's perfectly okay because it has one
by<00:01:58.799><c> default</c><00:01:59.320><c> if</c><00:01:59.399><c> you</c><00:01:59.520><c> go</c><00:01:59.640><c> in</c><00:01:59.920><c> here</c><00:02:00.079><c> and</c><00:02:00.240><c> click</c>

00:02:00.389 --> 00:02:00.399 align:start position:0%
by default if you go in here and click
 

00:02:00.399 --> 00:02:02.389 align:start position:0%
by default if you go in here and click
on<00:02:00.560><c> assistant</c><00:02:00.960><c> agent</c><00:02:01.479><c> here</c><00:02:01.680><c> is</c><00:02:01.799><c> the</c><00:02:02.039><c> default</c>

00:02:02.389 --> 00:02:02.399 align:start position:0%
on assistant agent here is the default
 

00:02:02.399 --> 00:02:03.709 align:start position:0%
on assistant agent here is the default
system<00:02:02.719><c> message</c><00:02:03.039><c> and</c><00:02:03.159><c> the</c><00:02:03.320><c> default</c>

00:02:03.709 --> 00:02:03.719 align:start position:0%
system message and the default
 

00:02:03.719 --> 00:02:05.510 align:start position:0%
system message and the default
description<00:02:04.280><c> and</c><00:02:04.360><c> then</c><00:02:04.479><c> for</c><00:02:04.600><c> the</c><00:02:04.680><c> user</c><00:02:04.960><c> agents</c>

00:02:05.510 --> 00:02:05.520 align:start position:0%
description and then for the user agents
 

00:02:05.520 --> 00:02:06.950 align:start position:0%
description and then for the user agents
uh<00:02:05.640><c> I</c><00:02:05.759><c> just</c><00:02:05.840><c> had</c><00:02:06.000><c> the</c><00:02:06.119><c> name</c><00:02:06.479><c> we</c><00:02:06.600><c> are</c><00:02:06.759><c> not</c>

00:02:06.950 --> 00:02:06.960 align:start position:0%
uh I just had the name we are not
 

00:02:06.960 --> 00:02:08.949 align:start position:0%
uh I just had the name we are not
actually<00:02:07.240><c> executing</c><00:02:07.799><c> any</c><00:02:08.080><c> code</c><00:02:08.479><c> and</c><00:02:08.599><c> I</c><00:02:08.759><c> never</c>

00:02:08.949 --> 00:02:08.959 align:start position:0%
actually executing any code and I never
 

00:02:08.959 --> 00:02:10.749 align:start position:0%
actually executing any code and I never
actually<00:02:09.200><c> need</c><00:02:09.319><c> to</c><00:02:09.520><c> respond</c><00:02:10.000><c> back</c><00:02:10.160><c> to</c><00:02:10.280><c> the</c><00:02:10.440><c> AI</c>

00:02:10.749 --> 00:02:10.759 align:start position:0%
actually need to respond back to the AI
 

00:02:10.759 --> 00:02:12.510 align:start position:0%
actually need to respond back to the AI
agent<00:02:11.160><c> and</c><00:02:11.280><c> now</c><00:02:11.440><c> for</c><00:02:11.640><c> the</c><00:02:11.840><c> function</c><00:02:12.400><c> that's</c>

00:02:12.510 --> 00:02:12.520 align:start position:0%
agent and now for the function that's
 

00:02:12.520 --> 00:02:13.910 align:start position:0%
agent and now for the function that's
going<00:02:12.640><c> to</c><00:02:12.760><c> do</c><00:02:13.120><c> most</c><00:02:13.280><c> of</c><00:02:13.400><c> the</c><00:02:13.480><c> work</c><00:02:13.720><c> for</c>

00:02:13.910 --> 00:02:13.920 align:start position:0%
going to do most of the work for
 

00:02:13.920 --> 00:02:16.110 align:start position:0%
going to do most of the work for
function<00:02:14.239><c> calling</c><00:02:14.640><c> in</c><00:02:14.879><c> autogen</c><00:02:15.800><c> we</c><00:02:15.920><c> first</c>

00:02:16.110 --> 00:02:16.120 align:start position:0%
function calling in autogen we first
 

00:02:16.120 --> 00:02:17.670 align:start position:0%
function calling in autogen we first
need<00:02:16.280><c> to</c><00:02:16.440><c> register</c><00:02:16.840><c> the</c><00:02:17.000><c> function</c><00:02:17.319><c> to</c><00:02:17.519><c> the</c>

00:02:17.670 --> 00:02:17.680 align:start position:0%
need to register the function to the
 

00:02:17.680 --> 00:02:18.990 align:start position:0%
need to register the function to the
user<00:02:18.040><c> because</c><00:02:18.200><c> the</c><00:02:18.319><c> user</c><00:02:18.519><c> is</c><00:02:18.640><c> going</c><00:02:18.720><c> to</c><00:02:18.800><c> be</c><00:02:18.920><c> the</c>

00:02:18.990 --> 00:02:19.000 align:start position:0%
user because the user is going to be the
 

00:02:19.000 --> 00:02:20.750 align:start position:0%
user because the user is going to be the
one<00:02:19.080><c> that</c><00:02:19.239><c> actually</c><00:02:19.640><c> executes</c><00:02:20.200><c> the</c><00:02:20.360><c> function</c>

00:02:20.750 --> 00:02:20.760 align:start position:0%
one that actually executes the function
 

00:02:20.760 --> 00:02:22.430 align:start position:0%
one that actually executes the function
and<00:02:20.840><c> then</c><00:02:20.959><c> we</c><00:02:21.040><c> need</c><00:02:21.239><c> assistant.</c><00:02:21.840><c> register</c><00:02:22.160><c> for</c>

00:02:22.430 --> 00:02:22.440 align:start position:0%
and then we need assistant. register for
 

00:02:22.440 --> 00:02:25.110 align:start position:0%
and then we need assistant. register for
llm<00:02:23.200><c> because</c><00:02:23.440><c> the</c><00:02:23.560><c> assistant</c><00:02:24.120><c> AI</c><00:02:24.480><c> agent</c><00:02:24.959><c> is</c>

00:02:25.110 --> 00:02:25.120 align:start position:0%
llm because the assistant AI agent is
 

00:02:25.120 --> 00:02:26.190 align:start position:0%
llm because the assistant AI agent is
going<00:02:25.200><c> to</c><00:02:25.280><c> be</c><00:02:25.360><c> the</c><00:02:25.440><c> one</c><00:02:25.560><c> that</c><00:02:25.680><c> is</c><00:02:25.879><c> actually</c>

00:02:26.190 --> 00:02:26.200 align:start position:0%
going to be the one that is actually
 

00:02:26.200 --> 00:02:28.229 align:start position:0%
going to be the one that is actually
talking<00:02:26.560><c> to</c><00:02:26.959><c> the</c><00:02:27.120><c> llm</c><00:02:27.800><c> and</c><00:02:27.920><c> now</c><00:02:28.040><c> we're</c><00:02:28.160><c> going</c>

00:02:28.229 --> 00:02:28.239 align:start position:0%
talking to the llm and now we're going
 

00:02:28.239 --> 00:02:29.910 align:start position:0%
talking to the llm and now we're going
to<00:02:28.400><c> define</c><00:02:28.720><c> a</c><00:02:28.840><c> function</c><00:02:29.160><c> so</c><00:02:29.400><c> we</c><00:02:29.519><c> call</c><00:02:29.640><c> it</c>

00:02:29.910 --> 00:02:29.920 align:start position:0%
to define a function so we call it
 

00:02:29.920 --> 00:02:31.830 align:start position:0%
to define a function so we call it
create<00:02:30.200><c> image</c><00:02:30.840><c> we</c><00:02:30.959><c> have</c><00:02:31.080><c> a</c><00:02:31.239><c> message</c><00:02:31.519><c> coming</c><00:02:31.720><c> in</c>

00:02:31.830 --> 00:02:31.840 align:start position:0%
create image we have a message coming in
 

00:02:31.840 --> 00:02:33.350 align:start position:0%
create image we have a message coming in
here<00:02:31.959><c> and</c><00:02:32.120><c> this</c><00:02:32.319><c> message</c><00:02:32.599><c> is</c><00:02:32.720><c> going</c><00:02:32.840><c> to</c><00:02:32.920><c> be</c><00:02:33.120><c> the</c>

00:02:33.350 --> 00:02:33.360 align:start position:0%
here and this message is going to be the
 

00:02:33.360 --> 00:02:35.670 align:start position:0%
here and this message is going to be the
prompt<00:02:34.080><c> once</c><00:02:34.400><c> the</c><00:02:34.560><c> assistant</c><00:02:34.920><c> agent</c><00:02:35.239><c> gets</c><00:02:35.440><c> it</c>

00:02:35.670 --> 00:02:35.680 align:start position:0%
prompt once the assistant agent gets it
 

00:02:35.680 --> 00:02:37.470 align:start position:0%
prompt once the assistant agent gets it
from<00:02:35.840><c> the</c><00:02:36.000><c> llm</c><00:02:36.640><c> you</c><00:02:36.760><c> need</c><00:02:36.879><c> to</c><00:02:37.040><c> have</c><00:02:37.200><c> this</c>

00:02:37.470 --> 00:02:37.480 align:start position:0%
from the llm you need to have this
 

00:02:37.480 --> 00:02:40.350 align:start position:0%
from the llm you need to have this
annotated<00:02:38.319><c> object</c><00:02:38.680><c> here</c><00:02:38.840><c> so</c><00:02:39.000><c> that</c><00:02:39.200><c> we</c><00:02:39.480><c> Define</c>

00:02:40.350 --> 00:02:40.360 align:start position:0%
annotated object here so that we Define
 

00:02:40.360 --> 00:02:42.589 align:start position:0%
annotated object here so that we Define
the<00:02:40.599><c> response</c><00:02:41.159><c> or</c><00:02:41.640><c> the</c><00:02:41.879><c> type</c><00:02:42.080><c> of</c><00:02:42.239><c> message</c>

00:02:42.589 --> 00:02:42.599 align:start position:0%
the response or the type of message
 

00:02:42.599 --> 00:02:44.270 align:start position:0%
the response or the type of message
coming<00:02:42.920><c> in</c><00:02:43.120><c> so</c><00:02:43.280><c> it's</c><00:02:43.400><c> going</c><00:02:43.480><c> to</c><00:02:43.599><c> be</c><00:02:43.680><c> a</c><00:02:43.800><c> string</c>

00:02:44.270 --> 00:02:44.280 align:start position:0%
coming in so it's going to be a string
 

00:02:44.280 --> 00:02:45.830 align:start position:0%
coming in so it's going to be a string
and<00:02:44.519><c> just</c><00:02:44.640><c> going</c><00:02:44.760><c> to</c><00:02:45.000><c> return</c><00:02:45.319><c> a</c><00:02:45.440><c> string</c>

00:02:45.830 --> 00:02:45.840 align:start position:0%
and just going to return a string
 

00:02:45.840 --> 00:02:48.430 align:start position:0%
and just going to return a string
autogen<00:02:46.440><c> kind</c><00:02:46.599><c> of</c><00:02:47.159><c> makes</c><00:02:47.360><c> it</c><00:02:47.560><c> necessary</c><00:02:48.000><c> to</c><00:02:48.159><c> do</c>

00:02:48.430 --> 00:02:48.440 align:start position:0%
autogen kind of makes it necessary to do
 

00:02:48.440 --> 00:02:50.350 align:start position:0%
autogen kind of makes it necessary to do
this<00:02:48.879><c> that's</c><00:02:49.120><c> why</c><00:02:49.640><c> it</c><00:02:49.760><c> even</c><00:02:49.959><c> though</c><00:02:50.080><c> we're</c><00:02:50.200><c> not</c>

00:02:50.350 --> 00:02:50.360 align:start position:0%
this that's why it even though we're not
 

00:02:50.360 --> 00:02:51.710 align:start position:0%
this that's why it even though we're not
really<00:02:50.560><c> need</c><00:02:50.720><c> to</c><00:02:50.959><c> needing</c><00:02:51.200><c> to</c><00:02:51.480><c> return</c>

00:02:51.710 --> 00:02:51.720 align:start position:0%
really need to needing to return
 

00:02:51.720 --> 00:02:53.990 align:start position:0%
really need to needing to return
anything<00:02:52.599><c> it's</c><00:02:52.800><c> just</c><00:02:53.280><c> there</c><00:02:53.440><c> so</c><00:02:53.640><c> it</c><00:02:53.800><c> doesn't</c>

00:02:53.990 --> 00:02:54.000 align:start position:0%
anything it's just there so it doesn't
 

00:02:54.000 --> 00:02:55.990 align:start position:0%
anything it's just there so it doesn't
complain<00:02:54.480><c> and</c><00:02:54.599><c> now</c><00:02:54.760><c> we</c><00:02:54.840><c> have</c><00:02:54.959><c> our</c><00:02:55.159><c> API</c><00:02:55.599><c> call</c><00:02:55.879><c> so</c>

00:02:55.990 --> 00:02:56.000 align:start position:0%
complain and now we have our API call so
 

00:02:56.000 --> 00:02:58.589 align:start position:0%
complain and now we have our API call so
we<00:02:56.080><c> say</c><00:02:56.319><c> response</c><00:02:56.680><c> equals</c><00:02:57.120><c> request.</c><00:02:58.040><c> poost</c>

00:02:58.589 --> 00:02:58.599 align:start position:0%
we say response equals request. poost
 

00:02:58.599 --> 00:03:00.910 align:start position:0%
we say response equals request. poost
give<00:02:58.720><c> it</c><00:02:58.840><c> the</c><00:02:58.959><c> API</c><00:02:59.360><c> URL</c><00:03:00.080><c> the</c><00:03:00.200><c> headers</c><00:03:00.640><c> and</c><00:03:00.760><c> the</c>

00:03:00.910 --> 00:03:00.920 align:start position:0%
give it the API URL the headers and the
 

00:03:00.920 --> 00:03:03.149 align:start position:0%
give it the API URL the headers and the
message<00:03:01.480><c> the</c><00:03:01.640><c> response.</c><00:03:02.480><c> content</c><00:03:02.959><c> is</c><00:03:03.080><c> going</c>

00:03:03.149 --> 00:03:03.159 align:start position:0%
message the response. content is going
 

00:03:03.159 --> 00:03:05.190 align:start position:0%
message the response. content is going
to<00:03:03.280><c> be</c><00:03:03.480><c> the</c><00:03:03.680><c> image</c><00:03:04.040><c> that</c><00:03:04.120><c> we're</c><00:03:04.360><c> getting</c><00:03:04.799><c> back</c>

00:03:05.190 --> 00:03:05.200 align:start position:0%
to be the image that we're getting back
 

00:03:05.200 --> 00:03:06.869 align:start position:0%
to be the image that we're getting back
this<00:03:05.400><c> random</c><00:03:05.920><c> number</c><00:03:06.159><c> in</c><00:03:06.319><c> this</c><00:03:06.480><c> file</c><00:03:06.680><c> name</c>

00:03:06.869 --> 00:03:06.879 align:start position:0%
this random number in this file name
 

00:03:06.879 --> 00:03:08.589 align:start position:0%
this random number in this file name
variable<00:03:07.280><c> here</c><00:03:07.560><c> basically</c><00:03:07.920><c> what</c><00:03:08.000><c> this</c><00:03:08.159><c> doing</c>

00:03:08.589 --> 00:03:08.599 align:start position:0%
variable here basically what this doing
 

00:03:08.599 --> 00:03:10.589 align:start position:0%
variable here basically what this doing
is<00:03:08.799><c> just</c><00:03:09.000><c> going</c><00:03:09.120><c> to</c><00:03:09.319><c> give</c><00:03:09.519><c> us</c><00:03:09.720><c> a</c><00:03:09.920><c> random</c><00:03:10.319><c> file</c>

00:03:10.589 --> 00:03:10.599 align:start position:0%
is just going to give us a random file
 

00:03:10.599 --> 00:03:11.789 align:start position:0%
is just going to give us a random file
name<00:03:11.000><c> so</c><00:03:11.200><c> you</c><00:03:11.280><c> don't</c><00:03:11.400><c> always</c><00:03:11.599><c> have</c><00:03:11.680><c> to</c>

00:03:11.789 --> 00:03:11.799 align:start position:0%
name so you don't always have to
 

00:03:11.799 --> 00:03:14.789 align:start position:0%
name so you don't always have to
overwrite<00:03:12.280><c> it</c><00:03:12.560><c> then</c><00:03:12.680><c> we</c><00:03:12.840><c> use</c><00:03:13.120><c> the</c><00:03:13.519><c> pil</c><00:03:14.239><c> Library</c>

00:03:14.789 --> 00:03:14.799 align:start position:0%
overwrite it then we use the pil Library
 

00:03:14.799 --> 00:03:16.550 align:start position:0%
overwrite it then we use the pil Library
so<00:03:14.959><c> that</c><00:03:15.120><c> we</c><00:03:15.200><c> can</c><00:03:15.440><c> take</c><00:03:15.640><c> that</c><00:03:15.840><c> image</c><00:03:16.239><c> and</c><00:03:16.360><c> then</c>

00:03:16.550 --> 00:03:16.560 align:start position:0%
so that we can take that image and then
 

00:03:16.560 --> 00:03:18.149 align:start position:0%
so that we can take that image and then
save<00:03:16.799><c> it</c><00:03:16.959><c> to</c><00:03:17.120><c> a</c><00:03:17.319><c> file</c><00:03:17.640><c> and</c><00:03:17.760><c> then</c><00:03:17.840><c> we</c><00:03:17.920><c> just</c>

00:03:18.149 --> 00:03:18.159 align:start position:0%
save it to a file and then we just
 

00:03:18.159 --> 00:03:19.509 align:start position:0%
save it to a file and then we just
return<00:03:18.360><c> the</c><00:03:18.560><c> prompt</c><00:03:18.879><c> that</c><00:03:19.000><c> the</c><00:03:19.120><c> assistant</c>

00:03:19.509 --> 00:03:19.519 align:start position:0%
return the prompt that the assistant
 

00:03:19.519 --> 00:03:22.229 align:start position:0%
return the prompt that the assistant
agent<00:03:19.959><c> got</c><00:03:20.319><c> from</c><00:03:20.519><c> the</c><00:03:20.680><c> llm</c><00:03:21.400><c> and</c><00:03:21.599><c> lastly</c><00:03:22.080><c> we</c>

00:03:22.229 --> 00:03:22.239 align:start position:0%
agent got from the llm and lastly we
 

00:03:22.239 --> 00:03:23.710 align:start position:0%
agent got from the llm and lastly we
just<00:03:22.440><c> initiate</c><00:03:22.840><c> the</c><00:03:22.920><c> chat</c><00:03:23.280><c> all</c><00:03:23.400><c> I'm</c><00:03:23.480><c> saying</c>

00:03:23.710 --> 00:03:23.720 align:start position:0%
just initiate the chat all I'm saying
 

00:03:23.720 --> 00:03:24.869 align:start position:0%
just initiate the chat all I'm saying
here<00:03:23.840><c> is</c><00:03:23.920><c> I</c><00:03:24.000><c> want</c><00:03:24.080><c> to</c><00:03:24.200><c> create</c><00:03:24.360><c> a</c><00:03:24.480><c> prompt</c><00:03:24.760><c> for</c>

00:03:24.869 --> 00:03:24.879 align:start position:0%
here is I want to create a prompt for
 

00:03:24.879 --> 00:03:26.789 align:start position:0%
here is I want to create a prompt for
image<00:03:25.080><c> generation</c><00:03:25.599><c> AI</c><00:03:26.040><c> with</c><00:03:26.159><c> Mario</c><00:03:26.560><c> tell</c><00:03:26.720><c> it</c>

00:03:26.789 --> 00:03:26.799 align:start position:0%
image generation AI with Mario tell it
 

00:03:26.799 --> 00:03:28.429 align:start position:0%
image generation AI with Mario tell it
to<00:03:26.879><c> be</c><00:03:27.040><c> creative</c><00:03:27.680><c> and</c><00:03:27.799><c> the</c><00:03:27.920><c> style</c><00:03:28.159><c> should</c><00:03:28.280><c> be</c>

00:03:28.429 --> 00:03:28.439 align:start position:0%
to be creative and the style should be
 

00:03:28.439 --> 00:03:30.390 align:start position:0%
to be creative and the style should be
dreamy<00:03:29.120><c> and</c><00:03:29.239><c> just</c><00:03:29.360><c> make</c><00:03:29.480><c> sure</c><00:03:29.879><c> where</c><00:03:30.000><c> I</c><00:03:30.159><c> had</c>

00:03:30.390 --> 00:03:30.400 align:start position:0%
dreamy and just make sure where I had
 

00:03:30.400 --> 00:03:32.429 align:start position:0%
dreamy and just make sure where I had
your<00:03:30.640><c> token</c><00:03:31.120><c> this</c><00:03:31.200><c> is</c><00:03:31.360><c> where</c><00:03:31.599><c> the</c><00:03:31.799><c> bear</c><00:03:32.120><c> all</c>

00:03:32.429 --> 00:03:32.439 align:start position:0%
your token this is where the bear all
 

00:03:32.439 --> 00:03:34.429 align:start position:0%
your token this is where the bear all
token<00:03:32.920><c> is</c><00:03:33.080><c> going</c><00:03:33.200><c> to</c><00:03:33.280><c> be</c><00:03:33.439><c> placed</c><00:03:33.760><c> for</c><00:03:34.000><c> you</c><00:03:34.319><c> that</c>

00:03:34.429 --> 00:03:34.439 align:start position:0%
token is going to be placed for you that
 

00:03:34.439 --> 00:03:36.429 align:start position:0%
token is going to be placed for you that
you<00:03:34.599><c> got</c><00:03:34.799><c> from</c><00:03:34.959><c> hugging</c><00:03:35.360><c> face</c><00:03:36.080><c> okay</c><00:03:36.280><c> and</c><00:03:36.360><c> then</c>

00:03:36.429 --> 00:03:36.439 align:start position:0%
you got from hugging face okay and then
 

00:03:36.439 --> 00:03:38.309 align:start position:0%
you got from hugging face okay and then
we're<00:03:36.560><c> going</c><00:03:36.640><c> to</c><00:03:36.760><c> come</c><00:03:36.879><c> over</c><00:03:37.080><c> here</c><00:03:37.239><c> to</c><00:03:37.400><c> main.py</c>

00:03:38.309 --> 00:03:38.319 align:start position:0%
we're going to come over here to main.py
 

00:03:38.319 --> 00:03:40.309 align:start position:0%
we're going to come over here to main.py
right<00:03:38.560><c> click</c><00:03:38.840><c> and</c><00:03:39.040><c> just</c><00:03:39.280><c> click</c><00:03:39.519><c> Run</c><00:03:40.080><c> Okay</c><00:03:40.200><c> so</c>

00:03:40.309 --> 00:03:40.319 align:start position:0%
right click and just click Run Okay so
 

00:03:40.319 --> 00:03:42.270 align:start position:0%
right click and just click Run Okay so
the<00:03:40.480><c> started</c><00:03:40.760><c> the</c><00:03:40.879><c> assistant</c><00:03:41.319><c> agent</c><00:03:42.000><c> uh</c><00:03:42.120><c> got</c>

00:03:42.270 --> 00:03:42.280 align:start position:0%
the started the assistant agent uh got
 

00:03:42.280 --> 00:03:44.110 align:start position:0%
the started the assistant agent uh got
the<00:03:42.480><c> suggested</c><00:03:42.920><c> tool</c><00:03:43.239><c> call</c><00:03:43.599><c> to</c><00:03:43.760><c> create</c><00:03:44.000><c> a</c>

00:03:44.110 --> 00:03:44.120 align:start position:0%
the suggested tool call to create a
 

00:03:44.120 --> 00:03:45.710 align:start position:0%
the suggested tool call to create a
dreamy<00:03:44.480><c> image</c><00:03:44.680><c> of</c><00:03:44.840><c> Mario</c><00:03:45.200><c> exploring</c><00:03:45.560><c> A</c>

00:03:45.710 --> 00:03:45.720 align:start position:0%
dreamy image of Mario exploring A
 

00:03:45.720 --> 00:03:48.030 align:start position:0%
dreamy image of Mario exploring A
Magical<00:03:46.000><c> Force</c><00:03:46.319><c> at</c><00:03:46.519><c> night</c><00:03:47.120><c> with</c><00:03:47.400><c> mushrooms</c>

00:03:48.030 --> 00:03:48.040 align:start position:0%
Magical Force at night with mushrooms
 

00:03:48.040 --> 00:03:49.710 align:start position:0%
Magical Force at night with mushrooms
and<00:03:48.280><c> all</c><00:03:48.439><c> kinds</c><00:03:48.599><c> of</c><00:03:48.720><c> other</c><00:03:48.920><c> stuff</c><00:03:49.319><c> so</c><00:03:49.480><c> now</c><00:03:49.640><c> the</c>

00:03:49.710 --> 00:03:49.720 align:start position:0%
and all kinds of other stuff so now the
 

00:03:49.720 --> 00:03:51.750 align:start position:0%
and all kinds of other stuff so now the
user<00:03:50.040><c> proxy</c><00:03:50.519><c> agent</c><00:03:50.799><c> is</c><00:03:50.920><c> going</c><00:03:51.120><c> to</c><00:03:51.360><c> execute</c>

00:03:51.750 --> 00:03:51.760 align:start position:0%
user proxy agent is going to execute
 

00:03:51.760 --> 00:03:53.990 align:start position:0%
user proxy agent is going to execute
that<00:03:51.959><c> function</c><00:03:52.720><c> with</c><00:03:53.120><c> that</c><00:03:53.360><c> message</c><00:03:53.760><c> that</c><00:03:53.920><c> the</c>

00:03:53.990 --> 00:03:54.000 align:start position:0%
that function with that message that the
 

00:03:54.000 --> 00:03:56.589 align:start position:0%
that function with that message that the
assistant<00:03:54.319><c> agent</c><00:03:54.680><c> got</c><00:03:55.079><c> from</c><00:03:55.360><c> the</c><00:03:55.480><c> llm</c><00:03:56.439><c> and</c>

00:03:56.589 --> 00:03:56.599 align:start position:0%
assistant agent got from the llm and
 

00:03:56.599 --> 00:03:58.390 align:start position:0%
assistant agent got from the llm and
then<00:03:56.760><c> we</c><00:03:56.959><c> terminated</c><00:03:57.720><c> and</c><00:03:57.799><c> then</c><00:03:57.959><c> over</c><00:03:58.120><c> here</c><00:03:58.239><c> on</c>

00:03:58.390 --> 00:03:58.400 align:start position:0%
then we terminated and then over here on
 

00:03:58.400 --> 00:04:00.270 align:start position:0%
then we terminated and then over here on
left-<00:03:58.640><c> hand</c><00:03:58.799><c> side</c><00:03:59.000><c> we</c><00:03:59.120><c> have</c><00:03:59.239><c> this</c><00:03:59.400><c> new</c><00:04:00.000><c> file</c>

00:04:00.270 --> 00:04:00.280 align:start position:0%
left- hand side we have this new file
 

00:04:00.280 --> 00:04:01.869 align:start position:0%
left- hand side we have this new file
name<00:04:00.519><c> here</c><00:04:00.640><c> with</c><00:04:00.760><c> a</c><00:04:00.840><c> PNG</c><00:04:01.319><c> let's</c><00:04:01.480><c> see</c><00:04:01.640><c> what</c><00:04:01.760><c> it</c>

00:04:01.869 --> 00:04:01.879 align:start position:0%
name here with a PNG let's see what it
 

00:04:01.879 --> 00:04:04.069 align:start position:0%
name here with a PNG let's see what it
looks<00:04:02.120><c> like</c><00:04:02.599><c> and</c><00:04:02.720><c> there</c><00:04:02.840><c> you</c><00:04:03.000><c> go</c><00:04:03.200><c> you</c><00:04:03.400><c> have</c>

00:04:04.069 --> 00:04:04.079 align:start position:0%
looks like and there you go you have
 

00:04:04.079 --> 00:04:07.309 align:start position:0%
looks like and there you go you have
Mario<00:04:04.680><c> in</c><00:04:04.920><c> some</c><00:04:05.280><c> dreamy</c><00:04:06.239><c> night</c><00:04:06.760><c> theme</c><00:04:07.159><c> with</c>

00:04:07.309 --> 00:04:07.319 align:start position:0%
Mario in some dreamy night theme with
 

00:04:07.319 --> 00:04:08.750 align:start position:0%
Mario in some dreamy night theme with
mushrooms<00:04:07.959><c> the</c><00:04:08.159><c> sky</c><00:04:08.360><c> is</c><00:04:08.439><c> filled</c><00:04:08.680><c> with</c>

00:04:08.750 --> 00:04:08.760 align:start position:0%
mushrooms the sky is filled with
 

00:04:08.760 --> 00:04:10.670 align:start position:0%
mushrooms the sky is filled with
shimmering<00:04:09.120><c> stars</c><00:04:09.439><c> and</c><00:04:09.560><c> a</c><00:04:09.680><c> large</c><00:04:09.959><c> Moon</c>

00:04:10.670 --> 00:04:10.680 align:start position:0%
shimmering stars and a large Moon
 

00:04:10.680 --> 00:04:12.990 align:start position:0%
shimmering stars and a large Moon
casting<00:04:11.040><c> overhead</c><00:04:11.760><c> okay</c><00:04:12.239><c> awesome</c><00:04:12.760><c> now</c><00:04:12.879><c> what</c>

00:04:12.990 --> 00:04:13.000 align:start position:0%
casting overhead okay awesome now what
 

00:04:13.000 --> 00:04:14.750 align:start position:0%
casting overhead okay awesome now what
you<00:04:13.079><c> can</c><00:04:13.239><c> do</c><00:04:13.480><c> is</c><00:04:13.680><c> just</c><00:04:13.879><c> change</c><00:04:14.200><c> the</c><00:04:14.319><c> seat</c><00:04:14.599><c> here</c>

00:04:14.750 --> 00:04:14.760 align:start position:0%
you can do is just change the seat here
 

00:04:14.760 --> 00:04:16.229 align:start position:0%
you can do is just change the seat here
let's<00:04:14.920><c> change</c><00:04:15.079><c> it</c><00:04:15.159><c> to</c><00:04:15.280><c> 41</c><00:04:15.879><c> and</c><00:04:16.000><c> what</c><00:04:16.120><c> you'll</c>

00:04:16.229 --> 00:04:16.239 align:start position:0%
let's change it to 41 and what you'll
 

00:04:16.239 --> 00:04:18.469 align:start position:0%
let's change it to 41 and what you'll
see<00:04:16.479><c> here</c><00:04:16.720><c> is</c><00:04:16.919><c> this</c><00:04:17.040><c> doesn't</c><00:04:17.280><c> take</c><00:04:17.519><c> very</c><00:04:17.799><c> long</c>

00:04:18.469 --> 00:04:18.479 align:start position:0%
see here is this doesn't take very long
 

00:04:18.479 --> 00:04:19.870 align:start position:0%
see here is this doesn't take very long
because<00:04:18.680><c> we're</c><00:04:18.799><c> using</c><00:04:19.040><c> an</c><00:04:19.199><c> inference</c><00:04:19.600><c> server</c>

00:04:19.870 --> 00:04:19.880 align:start position:0%
because we're using an inference server
 

00:04:19.880 --> 00:04:21.710 align:start position:0%
because we're using an inference server
to<00:04:20.040><c> generate</c><00:04:20.400><c> the</c><00:04:20.519><c> image</c><00:04:20.880><c> if</c><00:04:21.000><c> I</c><00:04:21.160><c> just</c><00:04:21.359><c> had</c><00:04:21.560><c> the</c>

00:04:21.710 --> 00:04:21.720 align:start position:0%
to generate the image if I just had the
 

00:04:21.720 --> 00:04:24.350 align:start position:0%
to generate the image if I just had the
code<00:04:22.280><c> to</c><00:04:22.560><c> directly</c><00:04:23.479><c> uh</c><00:04:23.680><c> have</c><00:04:23.919><c> everything</c><00:04:24.199><c> done</c>

00:04:24.350 --> 00:04:24.360 align:start position:0%
code to directly uh have everything done
 

00:04:24.360 --> 00:04:25.990 align:start position:0%
code to directly uh have everything done
on<00:04:24.479><c> my</c><00:04:24.600><c> local</c><00:04:24.840><c> machine</c><00:04:25.120><c> this</c><00:04:25.240><c> would</c><00:04:25.440><c> take</c><00:04:25.720><c> so</c>

00:04:25.990 --> 00:04:26.000 align:start position:0%
on my local machine this would take so
 

00:04:26.000 --> 00:04:27.830 align:start position:0%
on my local machine this would take so
long<00:04:26.680><c> while</c><00:04:26.840><c> I'm</c><00:04:26.960><c> just</c><00:04:27.080><c> talking</c><00:04:27.360><c> it</c><00:04:27.520><c> finished</c>

00:04:27.830 --> 00:04:27.840 align:start position:0%
long while I'm just talking it finished
 

00:04:27.840 --> 00:04:29.150 align:start position:0%
long while I'm just talking it finished
and<00:04:27.960><c> you</c><00:04:28.040><c> see</c><00:04:28.160><c> it</c><00:04:28.280><c> created</c><00:04:28.560><c> a</c><00:04:28.639><c> new</c><00:04:28.800><c> file</c><00:04:29.000><c> name</c>

00:04:29.150 --> 00:04:29.160 align:start position:0%
and you see it created a new file name
 

00:04:29.160 --> 00:04:31.070 align:start position:0%
and you see it created a new file name
with<00:04:29.280><c> the</c><00:04:29.360><c> r</c><00:04:29.800><c> number</c><00:04:30.280><c> between</c><00:04:30.600><c> 1</c><00:04:30.759><c> and</c><00:04:30.919><c> 1</c>

00:04:31.070 --> 00:04:31.080 align:start position:0%
with the r number between 1 and 1
 

00:04:31.080 --> 00:04:33.070 align:start position:0%
with the r number between 1 and 1
million<00:04:31.600><c> we</c><00:04:31.759><c> got</c><00:04:31.960><c> a</c><00:04:32.199><c> different</c><00:04:32.560><c> image</c><00:04:32.880><c> of</c>

00:04:33.070 --> 00:04:33.080 align:start position:0%
million we got a different image of
 

00:04:33.080 --> 00:04:34.749 align:start position:0%
million we got a different image of
Mario<00:04:33.600><c> it</c><00:04:33.680><c> looks</c><00:04:33.840><c> a</c><00:04:33.960><c> little</c><00:04:34.080><c> bit</c><00:04:34.240><c> like</c><00:04:34.400><c> Luigi</c>

00:04:34.749 --> 00:04:34.759 align:start position:0%
Mario it looks a little bit like Luigi
 

00:04:34.759 --> 00:04:36.550 align:start position:0%
Mario it looks a little bit like Luigi
he's<00:04:34.880><c> kind</c><00:04:35.000><c> of</c><00:04:35.120><c> tall</c><00:04:35.520><c> but</c><00:04:35.720><c> oh</c><00:04:36.280><c> I</c><00:04:36.360><c> don't</c><00:04:36.479><c> know</c>

00:04:36.550 --> 00:04:36.560 align:start position:0%
he's kind of tall but oh I don't know
 

00:04:36.560 --> 00:04:39.150 align:start position:0%
he's kind of tall but oh I don't know
what<00:04:36.680><c> that</c><00:04:36.800><c> is</c><00:04:36.919><c> up</c><00:04:37.120><c> there</c><00:04:37.360><c> but</c><00:04:37.960><c> okay</c><00:04:38.199><c> cool</c><00:04:38.880><c> okay</c>

00:04:39.150 --> 00:04:39.160 align:start position:0%
what that is up there but okay cool okay
 

00:04:39.160 --> 00:04:40.749 align:start position:0%
what that is up there but okay cool okay
awesome<00:04:39.479><c> we</c><00:04:39.600><c> just</c><00:04:39.720><c> did</c><00:04:39.880><c> our</c><00:04:40.080><c> first</c><00:04:40.400><c> text</c><00:04:40.639><c> to</c>

00:04:40.749 --> 00:04:40.759 align:start position:0%
awesome we just did our first text to
 

00:04:40.759 --> 00:04:42.670 align:start position:0%
awesome we just did our first text to
image<00:04:41.039><c> generation</c><00:04:41.560><c> using</c><00:04:41.840><c> a</c><00:04:41.960><c> free</c><00:04:42.199><c> model</c><00:04:42.479><c> from</c>

00:04:42.670 --> 00:04:42.680 align:start position:0%
image generation using a free model from
 

00:04:42.680 --> 00:04:44.390 align:start position:0%
image generation using a free model from
hugging<00:04:43.120><c> face</c><00:04:43.520><c> if</c><00:04:43.600><c> you</c><00:04:43.720><c> have</c><00:04:43.840><c> any</c><00:04:44.000><c> questions</c>

00:04:44.390 --> 00:04:44.400 align:start position:0%
hugging face if you have any questions
 

00:04:44.400 --> 00:04:46.350 align:start position:0%
hugging face if you have any questions
or<00:04:44.600><c> if</c><00:04:44.720><c> you've</c><00:04:44.960><c> tried</c><00:04:45.400><c> other</c><00:04:45.680><c> models</c><00:04:46.199><c> that</c>

00:04:46.350 --> 00:04:46.360 align:start position:0%
or if you've tried other models that
 

00:04:46.360 --> 00:04:47.950 align:start position:0%
or if you've tried other models that
might<00:04:46.560><c> give</c><00:04:46.720><c> you</c><00:04:46.919><c> better</c><00:04:47.199><c> image</c><00:04:47.479><c> generation</c>

00:04:47.950 --> 00:04:47.960 align:start position:0%
might give you better image generation
 

00:04:47.960 --> 00:04:50.150 align:start position:0%
might give you better image generation
locally<00:04:48.680><c> or</c><00:04:48.840><c> if</c><00:04:48.919><c> you're</c><00:04:49.080><c> interested</c><00:04:49.440><c> in</c><00:04:49.759><c> other</c>

00:04:50.150 --> 00:04:50.160 align:start position:0%
locally or if you're interested in other
 

00:04:50.160 --> 00:04:51.670 align:start position:0%
locally or if you're interested in other
models<00:04:50.520><c> that</c><00:04:50.639><c> do</c><00:04:50.880><c> different</c><00:04:51.120><c> things</c><00:04:51.400><c> such</c><00:04:51.560><c> as</c>

00:04:51.670 --> 00:04:51.680 align:start position:0%
models that do different things such as
 

00:04:51.680 --> 00:04:52.990 align:start position:0%
models that do different things such as
speech<00:04:51.919><c> recognition</c><00:04:52.479><c> please</c><00:04:52.680><c> put</c><00:04:52.800><c> them</c><00:04:52.919><c> down</c>

00:04:52.990 --> 00:04:53.000 align:start position:0%
speech recognition please put them down
 

00:04:53.000 --> 00:04:54.310 align:start position:0%
speech recognition please put them down
in<00:04:53.080><c> the</c><00:04:53.160><c> comments</c><00:04:53.400><c> and</c><00:04:53.520><c> let</c><00:04:53.680><c> me</c><00:04:53.759><c> know</c><00:04:54.039><c> this</c><00:04:54.120><c> is</c>

00:04:54.310 --> 00:04:54.320 align:start position:0%
in the comments and let me know this is
 

00:04:54.320 --> 00:04:56.590 align:start position:0%
in the comments and let me know this is
day<00:04:54.520><c> 10</c><00:04:55.199><c> and</c><00:04:55.400><c> from</c><00:04:55.680><c> this</c><00:04:55.880><c> day</c><00:04:56.199><c> I</c><00:04:56.280><c> think</c><00:04:56.360><c> for</c><00:04:56.479><c> the</c>

00:04:56.590 --> 00:04:56.600 align:start position:0%
day 10 and from this day I think for the
 

00:04:56.600 --> 00:04:58.230 align:start position:0%
day 10 and from this day I think for the
next<00:04:56.800><c> four</c><00:04:57.000><c> to</c><00:04:57.199><c> 5</c><00:04:57.400><c> days</c><00:04:57.680><c> we're</c><00:04:57.880><c> going</c><00:04:58.000><c> to</c><00:04:58.120><c> be</c>

00:04:58.230 --> 00:04:58.240 align:start position:0%
next four to 5 days we're going to be
 

00:04:58.240 --> 00:05:00.629 align:start position:0%
next four to 5 days we're going to be
using<00:04:58.600><c> different</c><00:04:58.840><c> models</c><00:04:59.160><c> from</c><00:04:59.320><c> hug</c><00:04:59.759><c> fa</c><00:05:00.280><c> doing</c>

00:05:00.629 --> 00:05:00.639 align:start position:0%
using different models from hug fa doing
 

00:05:00.639 --> 00:05:02.350 align:start position:0%
using different models from hug fa doing
different<00:05:01.000><c> things</c><00:05:01.440><c> and</c><00:05:01.560><c> the</c><00:05:01.720><c> goal</c><00:05:01.960><c> is</c><00:05:02.160><c> we</c><00:05:02.240><c> can</c>

00:05:02.350 --> 00:05:02.360 align:start position:0%
different things and the goal is we can
 

00:05:02.360 --> 00:05:04.590 align:start position:0%
different things and the goal is we can
start<00:05:02.639><c> integrating</c><00:05:03.280><c> all</c><00:05:03.400><c> of</c><00:05:03.639><c> these</c><00:05:03.840><c> things</c><00:05:04.160><c> in</c>

00:05:04.590 --> 00:05:04.600 align:start position:0%
start integrating all of these things in
 

00:05:04.600 --> 00:05:06.189 align:start position:0%
start integrating all of these things in
together<00:05:05.039><c> and</c><00:05:05.160><c> then</c><00:05:05.280><c> we</c><00:05:05.360><c> can</c><00:05:05.520><c> create</c><00:05:05.800><c> this</c><00:05:06.000><c> big</c>

00:05:06.189 --> 00:05:06.199 align:start position:0%
together and then we can create this big
 

00:05:06.199 --> 00:05:07.870 align:start position:0%
together and then we can create this big
AI<00:05:06.479><c> agent</c><00:05:06.720><c> workflow</c><00:05:07.120><c> to</c><00:05:07.240><c> do</c><00:05:07.400><c> something</c><00:05:07.759><c> that</c>

00:05:07.870 --> 00:05:07.880 align:start position:0%
AI agent workflow to do something that
 

00:05:07.880 --> 00:05:09.909 align:start position:0%
AI agent workflow to do something that
we<00:05:08.039><c> really</c><00:05:08.240><c> want</c><00:05:08.440><c> to</c><00:05:08.600><c> do</c><00:05:09.120><c> but</c><00:05:09.440><c> the</c><00:05:09.560><c> first</c><00:05:09.759><c> thing</c>

00:05:09.909 --> 00:05:09.919 align:start position:0%
we really want to do but the first thing
 

00:05:09.919 --> 00:05:11.189 align:start position:0%
we really want to do but the first thing
is<00:05:10.080><c> we</c><00:05:10.160><c> need</c><00:05:10.280><c> to</c><00:05:10.360><c> make</c><00:05:10.479><c> sure</c><00:05:10.639><c> we</c><00:05:10.720><c> have</c><00:05:10.840><c> all</c><00:05:11.000><c> the</c>

00:05:11.189 --> 00:05:11.199 align:start position:0%
is we need to make sure we have all the
 

00:05:11.199 --> 00:05:12.710 align:start position:0%
is we need to make sure we have all the
components<00:05:11.720><c> and</c><00:05:11.840><c> we</c><00:05:11.919><c> know</c><00:05:12.039><c> how</c><00:05:12.160><c> to</c><00:05:12.280><c> do</c><00:05:12.560><c> each</c>

00:05:12.710 --> 00:05:12.720 align:start position:0%
components and we know how to do each
 

00:05:12.720 --> 00:05:14.189 align:start position:0%
components and we know how to do each
one<00:05:12.960><c> so</c><00:05:13.080><c> I'm</c><00:05:13.160><c> going</c><00:05:13.240><c> to</c><00:05:13.360><c> introduce</c><00:05:13.759><c> you</c><00:05:13.880><c> to</c><00:05:14.120><c> a</c>

00:05:14.189 --> 00:05:14.199 align:start position:0%
one so I'm going to introduce you to a
 

00:05:14.199 --> 00:05:15.909 align:start position:0%
one so I'm going to introduce you to a
bunch<00:05:14.400><c> of</c><00:05:14.520><c> these</c><00:05:14.680><c> different</c><00:05:14.880><c> things</c><00:05:15.160><c> that</c><00:05:15.440><c> AI</c>

00:05:15.909 --> 00:05:15.919 align:start position:0%
bunch of these different things that AI
 

00:05:15.919 --> 00:05:17.629 align:start position:0%
bunch of these different things that AI
can<00:05:16.199><c> do</c><00:05:16.720><c> and</c><00:05:16.840><c> you</c><00:05:16.919><c> don't</c><00:05:17.080><c> always</c><00:05:17.240><c> have</c><00:05:17.360><c> to</c><00:05:17.520><c> pay</c>

00:05:17.629 --> 00:05:17.639 align:start position:0%
can do and you don't always have to pay
 

00:05:17.639 --> 00:05:19.350 align:start position:0%
can do and you don't always have to pay
for<00:05:17.800><c> it</c><00:05:17.960><c> you</c><00:05:18.080><c> can</c><00:05:18.280><c> do</c><00:05:18.479><c> things</c><00:05:18.720><c> locally</c><00:05:19.240><c> thank</c>

00:05:19.350 --> 00:05:19.360 align:start position:0%
for it you can do things locally thank
 

00:05:19.360 --> 00:05:20.629 align:start position:0%
for it you can do things locally thank
you<00:05:19.440><c> for</c><00:05:19.600><c> watching</c><00:05:19.800><c> here</c><00:05:19.960><c> are</c><00:05:20.080><c> more</c><00:05:20.280><c> videos</c><00:05:20.520><c> on</c>

00:05:20.629 --> 00:05:20.639 align:start position:0%
you for watching here are more videos on
 

00:05:20.639 --> 00:05:22.309 align:start position:0%
you for watching here are more videos on
autogen<00:05:21.199><c> please</c><00:05:21.440><c> like</c><00:05:21.560><c> And</c><00:05:21.720><c> subscribe</c><00:05:22.199><c> I'll</c>

00:05:22.309 --> 00:05:22.319 align:start position:0%
autogen please like And subscribe I'll
 

00:05:22.319 --> 00:05:25.080 align:start position:0%
autogen please like And subscribe I'll
see<00:05:22.479><c> you</c><00:05:22.639><c> next</c><00:05:22.800><c> video</c>

