WEBVTT
Kind: captions
Language: en

00:00:02.470 --> 00:00:08.860 align:start position:0%
 
[Music]

00:00:08.860 --> 00:00:08.870 align:start position:0%
[Music]
 

00:00:08.870 --> 00:00:11.690 align:start position:0%
[Music]
string<00:00:09.870><c> is</c><00:00:10.170><c> immutable</c><00:00:10.410><c> in</c><00:00:10.830><c> Java</c><00:00:11.040><c> which</c><00:00:11.460><c> means</c>

00:00:11.690 --> 00:00:11.700 align:start position:0%
string is immutable in Java which means
 

00:00:11.700 --> 00:00:13.430 align:start position:0%
string is immutable in Java which means
the<00:00:11.849><c> content</c><00:00:12.300><c> it</c><00:00:12.360><c> holds</c><00:00:12.690><c> cannot</c><00:00:13.139><c> be</c><00:00:13.230><c> changed</c>

00:00:13.430 --> 00:00:13.440 align:start position:0%
the content it holds cannot be changed
 

00:00:13.440 --> 00:00:15.379 align:start position:0%
the content it holds cannot be changed
this<00:00:14.070><c> can</c><00:00:14.219><c> be</c><00:00:14.340><c> further</c><00:00:14.580><c> referenced</c><00:00:15.150><c> seeing</c>

00:00:15.379 --> 00:00:15.389 align:start position:0%
this can be further referenced seeing
 

00:00:15.389 --> 00:00:17.500 align:start position:0%
this can be further referenced seeing
jellies<00:00:15.750><c> the</c><00:00:16.020><c> final</c><00:00:16.440><c> keyword</c><00:00:16.650><c> in</c><00:00:16.949><c> the</c><00:00:17.039><c> class</c>

00:00:17.500 --> 00:00:17.510 align:start position:0%
jellies the final keyword in the class
 

00:00:17.510 --> 00:00:19.970 align:start position:0%
jellies the final keyword in the class
this<00:00:18.510><c> is</c><00:00:18.690><c> an</c><00:00:18.779><c> intentional</c><00:00:19.170><c> design</c><00:00:19.560><c> for</c>

00:00:19.970 --> 00:00:19.980 align:start position:0%
this is an intentional design for
 

00:00:19.980 --> 00:00:21.859 align:start position:0%
this is an intentional design for
efficiency<00:00:20.460><c> and</c><00:00:20.550><c> optimization</c><00:00:20.779><c> within</c><00:00:21.779><c> the</c>

00:00:21.859 --> 00:00:21.869 align:start position:0%
efficiency and optimization within the
 

00:00:21.869 --> 00:00:24.880 align:start position:0%
efficiency and optimization within the
JVM<00:00:22.320><c> using</c><00:00:23.070><c> something</c><00:00:23.279><c> called</c><00:00:23.400><c> string</c><00:00:23.850><c> pools</c>

00:00:24.880 --> 00:00:24.890 align:start position:0%
JVM using something called string pools
 

00:00:24.890 --> 00:00:27.019 align:start position:0%
JVM using something called string pools
while<00:00:25.890><c> strings</c><00:00:26.220><c> are</c><00:00:26.369><c> represented</c><00:00:27.000><c> by</c>

00:00:27.019 --> 00:00:27.029 align:start position:0%
while strings are represented by
 

00:00:27.029 --> 00:00:28.910 align:start position:0%
while strings are represented by
sequence<00:00:27.570><c> of</c><00:00:27.720><c> zero</c><00:00:27.930><c> or</c><00:00:28.109><c> more</c><00:00:28.230><c> characters</c><00:00:28.289><c> if</c>

00:00:28.910 --> 00:00:28.920 align:start position:0%
sequence of zero or more characters if
 

00:00:28.920 --> 00:00:31.279 align:start position:0%
sequence of zero or more characters if
we<00:00:29.310><c> want</c><00:00:29.460><c> add</c><00:00:29.580><c> more</c><00:00:29.970><c> to</c><00:00:30.150><c> the</c><00:00:30.240><c> string</c><00:00:30.510><c> we</c><00:00:31.140><c> need</c>

00:00:31.279 --> 00:00:31.289 align:start position:0%
we want add more to the string we need
 

00:00:31.289 --> 00:00:33.229 align:start position:0%
we want add more to the string we need
to<00:00:31.380><c> use</c><00:00:31.500><c> concatenation</c><00:00:32.309><c> which</c><00:00:32.489><c> is</c><00:00:32.850><c> denoted</c><00:00:33.059><c> by</c>

00:00:33.229 --> 00:00:33.239 align:start position:0%
to use concatenation which is denoted by
 

00:00:33.239 --> 00:00:35.930 align:start position:0%
to use concatenation which is denoted by
the<00:00:33.390><c> plus</c><00:00:33.780><c> operator</c><00:00:34.170><c> but</c><00:00:35.100><c> what</c><00:00:35.520><c> does</c><00:00:35.670><c> this</c><00:00:35.760><c> do</c>

00:00:35.930 --> 00:00:35.940 align:start position:0%
the plus operator but what does this do
 

00:00:35.940 --> 00:00:37.940 align:start position:0%
the plus operator but what does this do
to<00:00:35.969><c> the</c><00:00:36.120><c> string</c><00:00:36.360><c> object</c><00:00:36.510><c> since</c><00:00:37.170><c> it</c><00:00:37.500><c> cannot</c><00:00:37.800><c> be</c>

00:00:37.940 --> 00:00:37.950 align:start position:0%
to the string object since it cannot be
 

00:00:37.950 --> 00:00:38.360 align:start position:0%
to the string object since it cannot be
changed

00:00:38.360 --> 00:00:38.370 align:start position:0%
changed
 

00:00:38.370 --> 00:00:40.819 align:start position:0%
changed
well<00:00:39.270><c> Java</c><00:00:39.899><c> must</c><00:00:40.200><c> create</c><00:00:40.350><c> a</c><00:00:40.410><c> new</c><00:00:40.559><c> string</c>

00:00:40.819 --> 00:00:40.829 align:start position:0%
well Java must create a new string
 

00:00:40.829 --> 00:00:42.920 align:start position:0%
well Java must create a new string
object<00:00:40.950><c> then</c><00:00:41.700><c> copied</c><00:00:42.120><c> the</c><00:00:42.180><c> characters</c><00:00:42.570><c> over</c>

00:00:42.920 --> 00:00:42.930 align:start position:0%
object then copied the characters over
 

00:00:42.930 --> 00:00:45.529 align:start position:0%
object then copied the characters over
from<00:00:43.170><c> the</c><00:00:43.260><c> original</c><00:00:43.559><c> string</c><00:00:43.800><c> and</c><00:00:44.040><c> then</c><00:00:44.879><c> append</c>

00:00:45.529 --> 00:00:45.539 align:start position:0%
from the original string and then append
 

00:00:45.539 --> 00:00:46.959 align:start position:0%
from the original string and then append
the<00:00:45.690><c> new</c><00:00:45.719><c> string</c><00:00:46.140><c> you</c><00:00:46.379><c> want</c><00:00:46.530><c> to</c><00:00:46.620><c> concatenate</c>

00:00:46.959 --> 00:00:46.969 align:start position:0%
the new string you want to concatenate
 

00:00:46.969 --> 00:00:49.310 align:start position:0%
the new string you want to concatenate
this<00:00:47.969><c> can</c><00:00:48.180><c> be</c><00:00:48.329><c> time-consuming</c><00:00:48.539><c> for</c><00:00:49.079><c> larger</c>

00:00:49.310 --> 00:00:49.320 align:start position:0%
this can be time-consuming for larger
 

00:00:49.320 --> 00:00:51.439 align:start position:0%
this can be time-consuming for larger
strings<00:00:49.620><c> though</c><00:00:49.800><c> the</c><00:00:50.730><c> string</c><00:00:51.000><c> builder</c><00:00:51.149><c> class</c>

00:00:51.439 --> 00:00:51.449 align:start position:0%
strings though the string builder class
 

00:00:51.449 --> 00:00:53.540 align:start position:0%
strings though the string builder class
doesn't<00:00:51.960><c> need</c><00:00:52.260><c> to</c><00:00:52.350><c> go</c><00:00:52.500><c> through</c><00:00:52.530><c> creation</c><00:00:53.190><c> and</c>

00:00:53.540 --> 00:00:53.550 align:start position:0%
doesn't need to go through creation and
 

00:00:53.550 --> 00:00:57.110 align:start position:0%
doesn't need to go through creation and
copying<00:00:53.940><c> over</c><00:00:54.120><c> because</c><00:00:54.690><c> it</c><00:00:54.930><c> is</c><00:00:55.170><c> mutable</c><00:00:56.120><c> with</c>

00:00:57.110 --> 00:00:57.120 align:start position:0%
copying over because it is mutable with
 

00:00:57.120 --> 00:00:59.360 align:start position:0%
copying over because it is mutable with
this<00:00:57.300><c> class</c><00:00:57.510><c> all</c><00:00:57.840><c> you</c><00:00:58.140><c> need</c><00:00:58.260><c> to</c><00:00:58.350><c> do</c><00:00:58.500><c> is</c><00:00:58.710><c> Estate</c>

00:00:59.360 --> 00:00:59.370 align:start position:0%
this class all you need to do is Estate
 

00:00:59.370 --> 00:01:01.610 align:start position:0%
this class all you need to do is Estate
an<00:00:59.460><c> object</c><00:00:59.910><c> and</c><00:01:00.059><c> add</c><00:01:00.510><c> string</c><00:01:00.840><c> literals</c><00:01:01.289><c> to</c><00:01:01.500><c> the</c>

00:01:01.610 --> 00:01:01.620 align:start position:0%
an object and add string literals to the
 

00:01:01.620 --> 00:01:04.910 align:start position:0%
an object and add string literals to the
object<00:01:02.010><c> with</c><00:01:02.340><c> a</c><00:01:02.520><c> methods</c><00:01:02.879><c> such</c><00:01:03.120><c> as</c><00:01:03.149><c> append</c><00:01:03.920><c> the</c>

00:01:04.910 --> 00:01:04.920 align:start position:0%
object with a methods such as append the
 

00:01:04.920 --> 00:01:06.469 align:start position:0%
object with a methods such as append the
string<00:01:05.159><c> class</c><00:01:05.339><c> can</c><00:01:05.580><c> be</c><00:01:05.610><c> initiated</c><00:01:06.180><c> in</c><00:01:06.240><c> two</c>

00:01:06.469 --> 00:01:06.479 align:start position:0%
string class can be initiated in two
 

00:01:06.479 --> 00:01:08.990 align:start position:0%
string class can be initiated in two
ways<00:01:06.689><c> the</c><00:01:07.350><c> first</c><00:01:07.380><c> with</c><00:01:07.890><c> double</c><00:01:08.159><c> quotes</c><00:01:08.189><c> and</c><00:01:08.610><c> if</c>

00:01:08.990 --> 00:01:09.000 align:start position:0%
ways the first with double quotes and if
 

00:01:09.000 --> 00:01:10.969 align:start position:0%
ways the first with double quotes and if
you<00:01:09.119><c> do</c><00:01:09.210><c> it</c><00:01:09.299><c> this</c><00:01:09.420><c> way</c><00:01:09.500><c> Java</c><00:01:10.500><c> will</c><00:01:10.650><c> create</c><00:01:10.860><c> the</c>

00:01:10.969 --> 00:01:10.979 align:start position:0%
you do it this way Java will create the
 

00:01:10.979 --> 00:01:12.620 align:start position:0%
you do it this way Java will create the
string<00:01:11.130><c> object</c><00:01:11.250><c> and</c><00:01:11.640><c> then</c><00:01:11.909><c> place</c><00:01:12.180><c> it</c><00:01:12.360><c> in</c><00:01:12.510><c> the</c>

00:01:12.620 --> 00:01:12.630 align:start position:0%
string object and then place it in the
 

00:01:12.630 --> 00:01:14.810 align:start position:0%
string object and then place it in the
string<00:01:12.900><c> pool</c><00:01:13.200><c> the</c><00:01:13.920><c> second</c><00:01:14.280><c> way</c><00:01:14.340><c> is</c><00:01:14.369><c> with</c><00:01:14.729><c> the</c>

00:01:14.810 --> 00:01:14.820 align:start position:0%
string pool the second way is with the
 

00:01:14.820 --> 00:01:16.670 align:start position:0%
string pool the second way is with the
new<00:01:14.970><c> keyword</c><00:01:15.330><c> if</c><00:01:15.509><c> you</c><00:01:15.990><c> use</c><00:01:16.110><c> the</c><00:01:16.259><c> new</c><00:01:16.409><c> keyword</c>

00:01:16.670 --> 00:01:16.680 align:start position:0%
new keyword if you use the new keyword
 

00:01:16.680 --> 00:01:19.010 align:start position:0%
new keyword if you use the new keyword
then<00:01:17.250><c> Java</c><00:01:17.580><c> explicitly</c><00:01:18.390><c> creates</c><00:01:18.750><c> it</c><00:01:18.840><c> in</c><00:01:18.930><c> the</c>

00:01:19.010 --> 00:01:19.020 align:start position:0%
then Java explicitly creates it in the
 

00:01:19.020 --> 00:01:22.160 align:start position:0%
then Java explicitly creates it in the
heap<00:01:19.290><c> memory</c><00:01:19.939><c> so</c><00:01:20.939><c> why</c><00:01:21.360><c> would</c><00:01:21.540><c> we</c><00:01:21.659><c> use</c><00:01:21.810><c> one</c><00:01:21.990><c> over</c>

00:01:22.160 --> 00:01:22.170 align:start position:0%
heap memory so why would we use one over
 

00:01:22.170 --> 00:01:22.640 align:start position:0%
heap memory so why would we use one over
the<00:01:22.259><c> other</c>

00:01:22.640 --> 00:01:22.650 align:start position:0%
the other
 

00:01:22.650 --> 00:01:25.310 align:start position:0%
the other
well<00:01:23.310><c> when</c><00:01:24.030><c> dealing</c><00:01:24.270><c> with</c><00:01:24.420><c> multi-threading</c>

00:01:25.310 --> 00:01:25.320 align:start position:0%
well when dealing with multi-threading
 

00:01:25.320 --> 00:01:28.310 align:start position:0%
well when dealing with multi-threading
you<00:01:25.650><c> want</c><00:01:25.890><c> immutability</c><00:01:26.670><c> so</c><00:01:27.360><c> string</c><00:01:27.840><c> should</c>

00:01:28.310 --> 00:01:28.320 align:start position:0%
you want immutability so string should
 

00:01:28.320 --> 00:01:31.370 align:start position:0%
you want immutability so string should
be<00:01:28.439><c> the</c><00:01:28.560><c> preferred</c><00:01:28.920><c> class</c><00:01:29.189><c> however</c><00:01:30.200><c> if</c><00:01:31.200><c> you</c>

00:01:31.370 --> 00:01:31.380 align:start position:0%
be the preferred class however if you
 

00:01:31.380 --> 00:01:32.600 align:start position:0%
be the preferred class however if you
are<00:01:31.439><c> worried</c><00:01:31.650><c> about</c><00:01:31.740><c> performance</c><00:01:31.920><c> overhead</c>

00:01:32.600 --> 00:01:32.610 align:start position:0%
are worried about performance overhead
 

00:01:32.610 --> 00:01:35.630 align:start position:0%
are worried about performance overhead
then<00:01:33.210><c> go</c><00:01:33.390><c> with</c><00:01:33.540><c> string</c><00:01:33.750><c> builder</c><00:01:34.250><c> but</c><00:01:35.250><c> in</c><00:01:35.460><c> many</c>

00:01:35.630 --> 00:01:35.640 align:start position:0%
then go with string builder but in many
 

00:01:35.640 --> 00:01:37.399 align:start position:0%
then go with string builder but in many
cases<00:01:36.119><c> you're</c><00:01:36.600><c> going</c><00:01:36.750><c> to</c><00:01:36.810><c> use</c><00:01:36.960><c> a</c><00:01:36.990><c> string</c><00:01:37.229><c> class</c>

00:01:37.399 --> 00:01:37.409 align:start position:0%
cases you're going to use a string class
 

00:01:37.409 --> 00:01:39.930 align:start position:0%
cases you're going to use a string class
regardless

00:01:39.930 --> 00:01:39.940 align:start position:0%
 
 

00:01:39.940 --> 00:01:46.080 align:start position:0%
 
[Music]

00:01:46.080 --> 00:01:46.090 align:start position:0%
[Music]
 

00:01:46.090 --> 00:01:48.149 align:start position:0%
[Music]
you

