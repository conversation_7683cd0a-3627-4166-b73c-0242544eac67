WEBVTT
Kind: captions
Language: en

00:00:00.160 --> 00:00:02.389 align:start position:0%
 
Hello<00:00:00.480><c> everyone.</c><00:00:00.960><c> This</c><00:00:01.120><c> is</c><00:00:01.280><c> <PERSON><PERSON></c><00:00:01.760><c> from</c><00:00:02.000><c> Llama</c>

00:00:02.389 --> 00:00:02.399 align:start position:0%
Hello everyone. This is <PERSON><PERSON> from Llama
 

00:00:02.399 --> 00:00:04.630 align:start position:0%
Hello everyone. This is <PERSON><PERSON> from Llama
Index<00:00:03.040><c> and</c><00:00:03.360><c> I've</c><00:00:03.600><c> decided</c><00:00:03.919><c> that</c><00:00:04.160><c> I'm</c><00:00:04.400><c> going</c><00:00:04.480><c> to</c>

00:00:04.630 --> 00:00:04.640 align:start position:0%
Index and I've decided that I'm going to
 

00:00:04.640 --> 00:00:06.630 align:start position:0%
Index and I've decided that I'm going to
be<00:00:04.799><c> making</c><00:00:05.200><c> two</c><00:00:05.520><c> videos</c><00:00:05.920><c> over</c><00:00:06.160><c> the</c><00:00:06.319><c> next</c><00:00:06.480><c> few</c>

00:00:06.630 --> 00:00:06.640 align:start position:0%
be making two videos over the next few
 

00:00:06.640 --> 00:00:08.470 align:start position:0%
be making two videos over the next few
weeks.<00:00:06.960><c> This</c><00:00:07.200><c> one</c><00:00:07.359><c> being</c><00:00:07.520><c> the</c><00:00:07.759><c> first</c><00:00:08.000><c> because</c>

00:00:08.470 --> 00:00:08.480 align:start position:0%
weeks. This one being the first because
 

00:00:08.480 --> 00:00:10.790 align:start position:0%
weeks. This one being the first because
Llama<00:00:08.880><c> Index</c><00:00:09.280><c> provides</c><00:00:09.599><c> a</c><00:00:09.840><c> lot</c><00:00:10.000><c> of</c><00:00:10.080><c> tools</c><00:00:10.480><c> and</c>

00:00:10.790 --> 00:00:10.800 align:start position:0%
Llama Index provides a lot of tools and
 

00:00:10.800 --> 00:00:13.589 align:start position:0%
Llama Index provides a lot of tools and
products<00:00:11.360><c> as</c><00:00:11.599><c> well</c><00:00:11.759><c> as</c><00:00:11.920><c> Llama</c><00:00:12.320><c> Cloud.</c><00:00:13.040><c> So</c><00:00:13.360><c> my</c>

00:00:13.589 --> 00:00:13.599 align:start position:0%
products as well as Llama Cloud. So my
 

00:00:13.599 --> 00:00:15.990 align:start position:0%
products as well as Llama Cloud. So my
idea<00:00:13.920><c> is</c><00:00:14.160><c> that</c><00:00:14.400><c> I'm</c><00:00:14.639><c> going</c><00:00:14.799><c> to</c><00:00:14.960><c> be</c><00:00:15.280><c> doing</c><00:00:15.519><c> this</c>

00:00:15.990 --> 00:00:16.000 align:start position:0%
idea is that I'm going to be doing this
 

00:00:16.000 --> 00:00:17.830 align:start position:0%
idea is that I'm going to be doing this
video<00:00:16.240><c> where</c><00:00:16.480><c> I'll</c><00:00:16.800><c> walk</c><00:00:16.960><c> through</c><00:00:17.199><c> the</c><00:00:17.440><c> Llama</c>

00:00:17.830 --> 00:00:17.840 align:start position:0%
video where I'll walk through the Llama
 

00:00:17.840 --> 00:00:20.390 align:start position:0%
video where I'll walk through the Llama
Cloud<00:00:18.359><c> landscape.</c><00:00:19.439><c> Um,</c><00:00:19.760><c> and</c><00:00:19.920><c> then</c><00:00:20.080><c> in</c><00:00:20.240><c> the</c>

00:00:20.390 --> 00:00:20.400 align:start position:0%
Cloud landscape. Um, and then in the
 

00:00:20.400 --> 00:00:22.630 align:start position:0%
Cloud landscape. Um, and then in the
next<00:00:20.640><c> video</c><00:00:20.960><c> I'll</c><00:00:21.359><c> actually</c><00:00:21.680><c> use</c><00:00:22.240><c> Llama</c>

00:00:22.630 --> 00:00:22.640 align:start position:0%
next video I'll actually use Llama
 

00:00:22.640 --> 00:00:24.310 align:start position:0%
next video I'll actually use Llama
Index,<00:00:22.960><c> the</c><00:00:23.199><c> open</c><00:00:23.359><c> source</c><00:00:23.680><c> framework</c><00:00:24.080><c> to</c>

00:00:24.310 --> 00:00:24.320 align:start position:0%
Index, the open source framework to
 

00:00:24.320 --> 00:00:26.790 align:start position:0%
Index, the open source framework to
build<00:00:24.480><c> an</c><00:00:24.720><c> agentic</c><00:00:25.279><c> workflow</c><00:00:25.920><c> that</c><00:00:26.240><c> makes</c><00:00:26.480><c> use</c>

00:00:26.790 --> 00:00:26.800 align:start position:0%
build an agentic workflow that makes use
 

00:00:26.800 --> 00:00:28.390 align:start position:0%
build an agentic workflow that makes use
of<00:00:27.039><c> the</c><00:00:27.279><c> tools</c><00:00:27.599><c> that</c><00:00:27.760><c> we've</c><00:00:28.000><c> built</c><00:00:28.240><c> for</c>

00:00:28.390 --> 00:00:28.400 align:start position:0%
of the tools that we've built for
 

00:00:28.400 --> 00:00:30.950 align:start position:0%
of the tools that we've built for
ourselves<00:00:28.800><c> in</c><00:00:29.039><c> Llama</c><00:00:29.439><c> Cloud.</c><00:00:30.160><c> So</c><00:00:30.480><c> all</c><00:00:30.720><c> of</c><00:00:30.800><c> the</c>

00:00:30.950 --> 00:00:30.960 align:start position:0%
ourselves in Llama Cloud. So all of the
 

00:00:30.960 --> 00:00:33.270 align:start position:0%
ourselves in Llama Cloud. So all of the
tools<00:00:31.279><c> and</c><00:00:31.519><c> products</c><00:00:31.840><c> that</c><00:00:32.480><c> Llama</c><00:00:32.880><c> Index</c>

00:00:33.270 --> 00:00:33.280 align:start position:0%
tools and products that Llama Index
 

00:00:33.280 --> 00:00:35.510 align:start position:0%
tools and products that Llama Index
provides<00:00:34.000><c> essentially</c><00:00:34.640><c> aims</c><00:00:35.040><c> to</c><00:00:35.200><c> make</c>

00:00:35.510 --> 00:00:35.520 align:start position:0%
provides essentially aims to make
 

00:00:35.520 --> 00:00:37.910 align:start position:0%
provides essentially aims to make
building<00:00:35.840><c> AI</c><00:00:36.320><c> applications</c><00:00:37.200><c> a</c><00:00:37.440><c> lot</c><00:00:37.600><c> easier</c>

00:00:37.910 --> 00:00:37.920 align:start position:0%
building AI applications a lot easier
 

00:00:37.920 --> 00:00:40.069 align:start position:0%
building AI applications a lot easier
for<00:00:38.160><c> developers.</c><00:00:38.960><c> And</c><00:00:39.200><c> the</c><00:00:39.440><c> whole</c><00:00:39.680><c> process</c>

00:00:40.069 --> 00:00:40.079 align:start position:0%
for developers. And the whole process
 

00:00:40.079 --> 00:00:42.549 align:start position:0%
for developers. And the whole process
starts<00:00:40.399><c> from</c><00:00:40.640><c> the</c><00:00:40.800><c> very</c><00:00:41.120><c> beginning</c><00:00:41.600><c> of</c><00:00:41.840><c> the</c><00:00:42.079><c> AI</c>

00:00:42.549 --> 00:00:42.559 align:start position:0%
starts from the very beginning of the AI
 

00:00:42.559 --> 00:00:45.190 align:start position:0%
starts from the very beginning of the AI
application<00:00:43.200><c> building</c><00:00:43.680><c> life</c><00:00:43.920><c> cycle</c><00:00:44.640><c> which</c><00:00:44.879><c> is</c>

00:00:45.190 --> 00:00:45.200 align:start position:0%
application building life cycle which is
 

00:00:45.200 --> 00:00:49.190 align:start position:0%
application building life cycle which is
very<00:00:45.559><c> often</c><00:00:46.559><c> preparing</c><00:00:47.360><c> data</c><00:00:48.320><c> in</c><00:00:48.559><c> a</c><00:00:48.800><c> way</c><00:00:48.879><c> that</c>

00:00:49.190 --> 00:00:49.200 align:start position:0%
very often preparing data in a way that
 

00:00:49.200 --> 00:00:52.150 align:start position:0%
very often preparing data in a way that
an<00:00:49.360><c> LLM</c><00:00:49.920><c> can</c><00:00:50.160><c> actually</c><00:00:50.559><c> understand</c><00:00:51.120><c> and</c><00:00:51.520><c> use.</c>

00:00:52.150 --> 00:00:52.160 align:start position:0%
an LLM can actually understand and use.
 

00:00:52.160 --> 00:00:55.590 align:start position:0%
an LLM can actually understand and use.
Data<00:00:52.480><c> is</c><00:00:52.719><c> messy.</c><00:00:53.520><c> It's</c><00:00:53.760><c> complex.</c><00:00:54.800><c> So,</c><00:00:55.280><c> Llama</c>

00:00:55.590 --> 00:00:55.600 align:start position:0%
Data is messy. It's complex. So, Llama
 

00:00:55.600 --> 00:00:58.229 align:start position:0%
Data is messy. It's complex. So, Llama
index<00:00:56.079><c> provides</c><00:00:56.480><c> tools</c><00:00:56.879><c> like</c><00:00:57.199><c> llama</c><00:00:57.680><c> par</c>

00:00:58.229 --> 00:00:58.239 align:start position:0%
index provides tools like llama par
 

00:00:58.239 --> 00:01:01.270 align:start position:0%
index provides tools like llama par
extract<00:00:58.640><c> and</c><00:00:58.879><c> index</c><00:00:59.359><c> to</c><00:00:59.600><c> make</c><00:00:59.760><c> that</c><00:01:00.719><c> side</c><00:01:01.039><c> of</c>

00:01:01.270 --> 00:01:01.280 align:start position:0%
extract and index to make that side of
 

00:01:01.280 --> 00:01:03.349 align:start position:0%
extract and index to make that side of
the<00:01:01.520><c> application</c><00:01:02.160><c> building</c><00:01:02.559><c> a</c><00:01:02.800><c> lot</c><00:01:02.960><c> easier</c>

00:01:03.349 --> 00:01:03.359 align:start position:0%
the application building a lot easier
 

00:01:03.359 --> 00:01:05.830 align:start position:0%
the application building a lot easier
for<00:01:03.600><c> you</c><00:01:04.159><c> while</c><00:01:04.559><c> still</c><00:01:04.799><c> making</c><00:01:05.119><c> use</c><00:01:05.439><c> of</c><00:01:05.680><c> the</c>

00:01:05.830 --> 00:01:05.840 align:start position:0%
for you while still making use of the
 

00:01:05.840 --> 00:01:08.750 align:start position:0%
for you while still making use of the
latest<00:01:06.159><c> AI</c><00:01:06.560><c> technology</c><00:01:07.200><c> and</c><00:01:07.439><c> LLMs</c><00:01:08.240><c> in</c><00:01:08.560><c> the</c>

00:01:08.750 --> 00:01:08.760 align:start position:0%
latest AI technology and LLMs in the
 

00:01:08.760 --> 00:01:11.429 align:start position:0%
latest AI technology and LLMs in the
process.<00:01:09.760><c> So,</c><00:01:10.000><c> let's</c><00:01:10.320><c> start</c><00:01:10.560><c> with</c><00:01:10.960><c> llama</c>

00:01:11.429 --> 00:01:11.439 align:start position:0%
process. So, let's start with llama
 

00:01:11.439 --> 00:01:15.750 align:start position:0%
process. So, let's start with llama
pass.<00:01:12.320><c> Llama,</c><00:01:13.280><c> the</c><00:01:13.600><c> idea</c><00:01:13.920><c> is</c><00:01:14.159><c> simple.</c><00:01:15.200><c> We</c><00:01:15.520><c> may</c>

00:01:15.750 --> 00:01:15.760 align:start position:0%
pass. Llama, the idea is simple. We may
 

00:01:15.760 --> 00:01:18.550 align:start position:0%
pass. Llama, the idea is simple. We may
have<00:01:15.920><c> data</c><00:01:16.320><c> in</c><00:01:16.640><c> quite</c><00:01:17.119><c> notoriously</c><00:01:18.000><c> difficult</c>

00:01:18.550 --> 00:01:18.560 align:start position:0%
have data in quite notoriously difficult
 

00:01:18.560 --> 00:01:21.510 align:start position:0%
have data in quite notoriously difficult
to<00:01:18.880><c> handle</c><00:01:19.360><c> file</c><00:01:19.680><c> types</c><00:01:20.400><c> for</c><00:01:20.479><c> example</c><00:01:20.960><c> PDFs</c>

00:01:21.510 --> 00:01:21.520 align:start position:0%
to handle file types for example PDFs
 

00:01:21.520 --> 00:01:24.710 align:start position:0%
to handle file types for example PDFs
with<00:01:21.759><c> lots</c><00:01:22.000><c> of</c><00:01:22.560><c> tables</c><00:01:22.960><c> and</c><00:01:23.280><c> graphics</c><00:01:23.920><c> etc.</c>

00:01:24.710 --> 00:01:24.720 align:start position:0%
with lots of tables and graphics etc.
 

00:01:24.720 --> 00:01:27.030 align:start position:0%
with lots of tables and graphics etc.
And<00:01:24.960><c> llama</c><00:01:25.360><c> piles</c><00:01:25.759><c> just</c><00:01:25.920><c> simply</c><00:01:26.240><c> the</c><00:01:26.479><c> idea</c><00:01:26.720><c> is</c>

00:01:27.030 --> 00:01:27.040 align:start position:0%
And llama piles just simply the idea is
 

00:01:27.040 --> 00:01:29.270 align:start position:0%
And llama piles just simply the idea is
let's<00:01:27.439><c> take</c><00:01:27.680><c> that</c><00:01:27.920><c> and</c><00:01:28.159><c> let's</c><00:01:28.640><c> convert</c><00:01:29.040><c> that</c>

00:01:29.270 --> 00:01:29.280 align:start position:0%
let's take that and let's convert that
 

00:01:29.280 --> 00:01:31.429 align:start position:0%
let's take that and let's convert that
format<00:01:29.759><c> into</c><00:01:30.159><c> something</c><00:01:30.640><c> that</c><00:01:31.040><c> we</c><00:01:31.280><c> can</c>

00:01:31.429 --> 00:01:31.439 align:start position:0%
format into something that we can
 

00:01:31.439 --> 00:01:32.950 align:start position:0%
format into something that we can
actually<00:01:31.680><c> effectively</c><00:01:32.159><c> use</c><00:01:32.400><c> with</c><00:01:32.560><c> a</c><00:01:32.720><c> large</c>

00:01:32.950 --> 00:01:32.960 align:start position:0%
actually effectively use with a large
 

00:01:32.960 --> 00:01:35.749 align:start position:0%
actually effectively use with a large
language<00:01:33.400><c> model.</c><00:01:34.400><c> In</c><00:01:34.720><c> this</c><00:01:34.960><c> example</c><00:01:35.439><c> you'll</c>

00:01:35.749 --> 00:01:35.759 align:start position:0%
language model. In this example you'll
 

00:01:35.759 --> 00:01:39.109 align:start position:0%
language model. In this example you'll
see<00:01:35.920><c> me</c><00:01:36.479><c> parsing</c><00:01:37.119><c> a</c><00:01:37.520><c> example</c><00:01:38.000><c> invoice.</c><00:01:38.880><c> This</c>

00:01:39.109 --> 00:01:39.119 align:start position:0%
see me parsing a example invoice. This
 

00:01:39.119 --> 00:01:42.069 align:start position:0%
see me parsing a example invoice. This
invoice<00:01:39.680><c> has</c><00:01:40.479><c> an</c><00:01:40.799><c> address,</c><00:01:41.439><c> a</c><00:01:41.680><c> billing</c>

00:01:42.069 --> 00:01:42.079 align:start position:0%
invoice has an address, a billing
 

00:01:42.079 --> 00:01:45.270 align:start position:0%
invoice has an address, a billing
address.<00:01:42.640><c> It's</c><00:01:42.799><c> got</c><00:01:43.119><c> prices.</c><00:01:43.759><c> It's</c><00:01:44.000><c> got</c><00:01:44.720><c> VAT.</c>

00:01:45.270 --> 00:01:45.280 align:start position:0%
address. It's got prices. It's got VAT.
 

00:01:45.280 --> 00:01:47.030 align:start position:0%
address. It's got prices. It's got VAT.
It's<00:01:45.439><c> got</c><00:01:45.520><c> a</c><00:01:45.680><c> bunch</c><00:01:45.840><c> of</c><00:01:46.000><c> things</c><00:01:46.159><c> in</c><00:01:46.399><c> there</c><00:01:46.799><c> and</c>

00:01:47.030 --> 00:01:47.040 align:start position:0%
It's got a bunch of things in there and
 

00:01:47.040 --> 00:01:49.670 align:start position:0%
It's got a bunch of things in there and
we<00:01:47.280><c> want</c><00:01:47.360><c> to</c><00:01:47.680><c> be</c><00:01:47.840><c> able</c><00:01:48.000><c> to</c><00:01:48.159><c> use</c><00:01:48.399><c> this</c><00:01:48.720><c> PDF</c>

00:01:49.670 --> 00:01:49.680 align:start position:0%
we want to be able to use this PDF
 

00:01:49.680 --> 00:01:52.069 align:start position:0%
we want to be able to use this PDF
within<00:01:50.159><c> an</c><00:01:50.479><c> AI</c><00:01:50.880><c> application</c><00:01:51.439><c> for</c><00:01:51.600><c> example</c>

00:01:52.069 --> 00:01:52.079 align:start position:0%
within an AI application for example
 

00:01:52.079 --> 00:01:53.749 align:start position:0%
within an AI application for example
that<00:01:52.240><c> the</c><00:01:52.479><c> first</c><00:01:52.640><c> thing</c><00:01:52.799><c> we</c><00:01:53.040><c> have</c><00:01:53.200><c> to</c><00:01:53.360><c> do</c><00:01:53.439><c> is</c>

00:01:53.749 --> 00:01:53.759 align:start position:0%
that the first thing we have to do is
 

00:01:53.759 --> 00:01:55.830 align:start position:0%
that the first thing we have to do is
actually<00:01:54.399><c> get</c><00:01:54.640><c> the</c><00:01:54.880><c> information</c><00:01:55.280><c> out</c><00:01:55.439><c> of</c><00:01:55.680><c> this</c>

00:01:55.830 --> 00:01:55.840 align:start position:0%
actually get the information out of this
 

00:01:55.840 --> 00:01:57.749 align:start position:0%
actually get the information out of this
PDF.<00:01:56.399><c> So</c><00:01:56.560><c> this</c><00:01:56.720><c> is</c><00:01:56.799><c> what</c><00:01:56.960><c> llama</c><00:01:57.360><c> pass</c><00:01:57.600><c> is</c>

00:01:57.749 --> 00:01:57.759 align:start position:0%
PDF. So this is what llama pass is
 

00:01:57.759 --> 00:02:00.310 align:start position:0%
PDF. So this is what llama pass is
allowing<00:01:58.000><c> us</c><00:01:58.159><c> to</c><00:01:58.320><c> do.</c><00:01:58.960><c> It</c><00:01:59.280><c> has</c><00:01:59.520><c> a</c><00:01:59.920><c> bunch</c><00:02:00.159><c> of</c>

00:02:00.310 --> 00:02:00.320 align:start position:0%
allowing us to do. It has a bunch of
 

00:02:00.320 --> 00:02:02.630 align:start position:0%
allowing us to do. It has a bunch of
options.<00:02:00.880><c> We</c><00:02:01.119><c> can</c><00:02:01.360><c> decide</c><00:02:01.680><c> to</c><00:02:01.920><c> use</c><00:02:02.240><c> very</c>

00:02:02.630 --> 00:02:02.640 align:start position:0%
options. We can decide to use very
 

00:02:02.640 --> 00:02:05.830 align:start position:0%
options. We can decide to use very
simple<00:02:03.200><c> pausing</c><00:02:03.640><c> techniques</c><00:02:04.640><c> as</c><00:02:04.960><c> well</c><00:02:05.119><c> as</c><00:02:05.520><c> use</c>

00:02:05.830 --> 00:02:05.840 align:start position:0%
simple pausing techniques as well as use
 

00:02:05.840 --> 00:02:08.550 align:start position:0%
simple pausing techniques as well as use
using<00:02:06.560><c> um</c><00:02:06.799><c> an</c><00:02:07.040><c> agent</c><00:02:07.360><c> or</c><00:02:07.520><c> an</c><00:02:07.680><c> LLM</c><00:02:08.239><c> for</c><00:02:08.399><c> the</c>

00:02:08.550 --> 00:02:08.560 align:start position:0%
using um an agent or an LLM for the
 

00:02:08.560 --> 00:02:11.110 align:start position:0%
using um an agent or an LLM for the
parsing<00:02:08.959><c> process.</c><00:02:09.920><c> Uh,</c><00:02:10.160><c> and</c><00:02:10.399><c> llama</c><00:02:10.800><c> pass</c>

00:02:11.110 --> 00:02:11.120 align:start position:0%
parsing process. Uh, and llama pass
 

00:02:11.120 --> 00:02:13.190 align:start position:0%
parsing process. Uh, and llama pass
basically<00:02:11.520><c> allows</c><00:02:11.840><c> you</c><00:02:12.000><c> to</c><00:02:12.239><c> get</c><00:02:12.480><c> that</c><00:02:12.720><c> data,</c>

00:02:13.190 --> 00:02:13.200 align:start position:0%
basically allows you to get that data,
 

00:02:13.200 --> 00:02:15.670 align:start position:0%
basically allows you to get that data,
get<00:02:13.360><c> that</c><00:02:13.680><c> file</c><00:02:13.920><c> into</c><00:02:14.239><c> a</c><00:02:14.400><c> format</c><00:02:14.720><c> that</c><00:02:15.040><c> an</c><00:02:15.280><c> LLM</c>

00:02:15.670 --> 00:02:15.680 align:start position:0%
get that file into a format that an LLM
 

00:02:15.680 --> 00:02:18.309 align:start position:0%
get that file into a format that an LLM
can<00:02:15.920><c> later</c><00:02:16.080><c> on</c><00:02:16.599><c> understand.</c><00:02:17.599><c> This</c><00:02:17.840><c> is</c><00:02:17.920><c> a</c><00:02:18.080><c> great</c>

00:02:18.309 --> 00:02:18.319 align:start position:0%
can later on understand. This is a great
 

00:02:18.319 --> 00:02:20.710 align:start position:0%
can later on understand. This is a great
segue<00:02:18.720><c> into</c><00:02:19.120><c> index</c><00:02:19.599><c> because</c><00:02:19.840><c> index</c><00:02:20.319><c> under</c><00:02:20.560><c> the</c>

00:02:20.710 --> 00:02:20.720 align:start position:0%
segue into index because index under the
 

00:02:20.720 --> 00:02:23.190 align:start position:0%
segue into index because index under the
hood<00:02:21.040><c> also</c><00:02:21.360><c> makes</c><00:02:21.599><c> use</c><00:02:21.760><c> of</c><00:02:22.000><c> pars.</c><00:02:22.800><c> And</c><00:02:23.040><c> the</c>

00:02:23.190 --> 00:02:23.200 align:start position:0%
hood also makes use of pars. And the
 

00:02:23.200 --> 00:02:26.949 align:start position:0%
hood also makes use of pars. And the
idea<00:02:23.440><c> behind</c><00:02:23.840><c> index</c><00:02:24.319><c> is</c><00:02:25.239><c> storing</c><00:02:26.239><c> a</c><00:02:26.560><c> bunch</c><00:02:26.800><c> of</c>

00:02:26.949 --> 00:02:26.959 align:start position:0%
idea behind index is storing a bunch of
 

00:02:26.959 --> 00:02:30.550 align:start position:0%
idea behind index is storing a bunch of
data,<00:02:27.760><c> a</c><00:02:28.000><c> lot</c><00:02:28.080><c> of</c><00:02:28.239><c> data</c><00:02:28.959><c> into</c><00:02:29.280><c> a</c><00:02:29.520><c> format</c><00:02:29.920><c> that</c>

00:02:30.550 --> 00:02:30.560 align:start position:0%
data, a lot of data into a format that
 

00:02:30.560 --> 00:02:32.790 align:start position:0%
data, a lot of data into a format that
maybe<00:02:31.120><c> even</c><00:02:31.440><c> a</c><00:02:31.680><c> simple</c><00:02:31.920><c> rag</c><00:02:32.319><c> application</c>

00:02:32.790 --> 00:02:32.800 align:start position:0%
maybe even a simple rag application
 

00:02:32.800 --> 00:02:34.869 align:start position:0%
maybe even a simple rag application
might<00:02:33.040><c> be</c><00:02:33.280><c> able</c><00:02:33.440><c> to</c><00:02:33.519><c> use.</c><00:02:33.920><c> So</c><00:02:34.319><c> a</c><00:02:34.480><c> lot</c><00:02:34.640><c> of</c><00:02:34.720><c> the</c>

00:02:34.869 --> 00:02:34.879 align:start position:0%
might be able to use. So a lot of the
 

00:02:34.879 --> 00:02:37.270 align:start position:0%
might be able to use. So a lot of the
time<00:02:35.040><c> we</c><00:02:35.360><c> may</c><00:02:35.519><c> have</c><00:02:36.000><c> thousands</c><00:02:36.400><c> and</c><00:02:36.879><c> thousands</c>

00:02:37.270 --> 00:02:37.280 align:start position:0%
time we may have thousands and thousands
 

00:02:37.280 --> 00:02:40.229 align:start position:0%
time we may have thousands and thousands
of<00:02:37.440><c> files</c><00:02:38.400><c> um</c><00:02:38.560><c> and</c><00:02:38.800><c> we</c><00:02:38.959><c> need</c><00:02:39.120><c> to</c><00:02:39.280><c> be</c><00:02:39.440><c> able</c><00:02:39.599><c> to</c>

00:02:40.229 --> 00:02:40.239 align:start position:0%
of files um and we need to be able to
 

00:02:40.239 --> 00:02:41.910 align:start position:0%
of files um and we need to be able to
like<00:02:40.480><c> first</c><00:02:40.720><c> get</c><00:02:40.959><c> them</c><00:02:41.040><c> into</c><00:02:41.280><c> a</c><00:02:41.440><c> format</c><00:02:41.680><c> that</c>

00:02:41.910 --> 00:02:41.920 align:start position:0%
like first get them into a format that
 

00:02:41.920 --> 00:02:44.790 align:start position:0%
like first get them into a format that
an<00:02:42.080><c> LLM</c><00:02:42.560><c> can</c><00:02:42.720><c> use</c><00:02:43.440><c> then</c><00:02:43.680><c> possibly</c><00:02:44.160><c> chunk</c><00:02:44.480><c> them</c>

00:02:44.790 --> 00:02:44.800 align:start position:0%
an LLM can use then possibly chunk them
 

00:02:44.800 --> 00:02:46.470 align:start position:0%
an LLM can use then possibly chunk them
then<00:02:45.040><c> possibly</c><00:02:45.360><c> embed</c><00:02:45.760><c> them</c><00:02:45.920><c> so</c><00:02:46.160><c> that</c><00:02:46.319><c> a</c>

00:02:46.470 --> 00:02:46.480 align:start position:0%
then possibly embed them so that a
 

00:02:46.480 --> 00:02:48.790 align:start position:0%
then possibly embed them so that a
retriever<00:02:46.879><c> can</c><00:02:47.040><c> retrieve</c><00:02:47.440><c> the</c><00:02:47.599><c> most</c><00:02:47.760><c> relevant</c>

00:02:48.790 --> 00:02:48.800 align:start position:0%
retriever can retrieve the most relevant
 

00:02:48.800 --> 00:02:51.750 align:start position:0%
retriever can retrieve the most relevant
uh<00:02:49.040><c> context</c><00:02:49.519><c> from</c><00:02:50.000><c> that</c><00:02:50.480><c> data</c><00:02:50.800><c> that</c><00:02:51.040><c> we</c><00:02:51.280><c> have</c>

00:02:51.750 --> 00:02:51.760 align:start position:0%
uh context from that data that we have
 

00:02:51.760 --> 00:02:55.589 align:start position:0%
uh context from that data that we have
and<00:02:52.160><c> index</c><00:02:53.120><c> handles</c><00:02:53.680><c> basically</c><00:02:54.080><c> all</c><00:02:54.239><c> of</c><00:02:54.599><c> this.</c>

00:02:55.589 --> 00:02:55.599 align:start position:0%
and index handles basically all of this.
 

00:02:55.599 --> 00:02:57.830 align:start position:0%
and index handles basically all of this.
The<00:02:55.840><c> first</c><00:02:56.000><c> thing</c><00:02:56.160><c> you</c><00:02:56.480><c> do</c><00:02:56.720><c> is</c><00:02:57.120><c> decide</c><00:02:57.440><c> on</c><00:02:57.680><c> what</c>

00:02:57.830 --> 00:02:57.840 align:start position:0%
The first thing you do is decide on what
 

00:02:57.840 --> 00:02:59.750 align:start position:0%
The first thing you do is decide on what
your<00:02:58.000><c> data</c><00:02:58.319><c> source</c><00:02:58.560><c> is.</c><00:02:58.800><c> This</c><00:02:59.040><c> can</c><00:02:59.200><c> be</c><00:02:59.440><c> as</c><00:02:59.519><c> as</c>

00:02:59.750 --> 00:02:59.760 align:start position:0%
your data source is. This can be as as
 

00:02:59.760 --> 00:03:02.149 align:start position:0%
your data source is. This can be as as
simple<00:03:00.000><c> as</c><00:03:00.239><c> dragging</c><00:03:00.560><c> and</c><00:03:00.800><c> dropping</c><00:03:01.440><c> a</c><00:03:01.680><c> PDF</c>

00:03:02.149 --> 00:03:02.159 align:start position:0%
simple as dragging and dropping a PDF
 

00:03:02.159 --> 00:03:04.869 align:start position:0%
simple as dragging and dropping a PDF
file<00:03:02.440><c> yourself.</c><00:03:03.440><c> Or</c><00:03:03.680><c> you</c><00:03:03.920><c> can,</c><00:03:04.159><c> as</c><00:03:04.400><c> I've</c><00:03:04.720><c> as</c>

00:03:04.869 --> 00:03:04.879 align:start position:0%
file yourself. Or you can, as I've as
 

00:03:04.879 --> 00:03:06.470 align:start position:0%
file yourself. Or you can, as I've as
you<00:03:05.040><c> see</c><00:03:05.200><c> I'm</c><00:03:05.440><c> doing</c><00:03:05.599><c> here,</c><00:03:05.920><c> you</c><00:03:06.080><c> can</c><00:03:06.239><c> select</c>

00:03:06.470 --> 00:03:06.480 align:start position:0%
you see I'm doing here, you can select
 

00:03:06.480 --> 00:03:09.110 align:start position:0%
you see I'm doing here, you can select
to<00:03:06.800><c> use</c><00:03:07.280><c> an</c><00:03:07.599><c> external</c><00:03:08.000><c> data</c><00:03:08.319><c> source</c><00:03:08.640><c> like</c><00:03:08.959><c> for</c>

00:03:09.110 --> 00:03:09.120 align:start position:0%
to use an external data source like for
 

00:03:09.120 --> 00:03:11.030 align:start position:0%
to use an external data source like for
example<00:03:09.599><c> here</c><00:03:09.920><c> I'm</c><00:03:10.159><c> connecting</c><00:03:10.560><c> to</c><00:03:10.800><c> a</c>

00:03:11.030 --> 00:03:11.040 align:start position:0%
example here I'm connecting to a
 

00:03:11.040 --> 00:03:13.190 align:start position:0%
example here I'm connecting to a
specific<00:03:11.440><c> Google</c><00:03:11.760><c> Drive</c><00:03:12.080><c> folder</c><00:03:12.400><c> of</c><00:03:12.560><c> mine</c>

00:03:13.190 --> 00:03:13.200 align:start position:0%
specific Google Drive folder of mine
 

00:03:13.200 --> 00:03:15.990 align:start position:0%
specific Google Drive folder of mine
that<00:03:13.519><c> for</c><00:03:13.760><c> now</c><00:03:14.000><c> has</c><00:03:14.319><c> one</c><00:03:14.560><c> document</c><00:03:14.959><c> in</c><00:03:15.200><c> it.</c>

00:03:15.990 --> 00:03:16.000 align:start position:0%
that for now has one document in it.
 

00:03:16.000 --> 00:03:18.630 align:start position:0%
that for now has one document in it.
Next,<00:03:16.400><c> you</c><00:03:16.640><c> can</c><00:03:16.879><c> select</c><00:03:17.200><c> a</c><00:03:17.440><c> sync</c><00:03:18.159><c> which</c><00:03:18.400><c> is</c>

00:03:18.630 --> 00:03:18.640 align:start position:0%
Next, you can select a sync which is
 

00:03:18.640 --> 00:03:21.350 align:start position:0%
Next, you can select a sync which is
basically<00:03:19.360><c> telling</c><00:03:19.920><c> Llama</c><00:03:20.400><c> Index</c><00:03:20.879><c> where</c><00:03:21.200><c> you</c>

00:03:21.350 --> 00:03:21.360 align:start position:0%
basically telling Llama Index where you
 

00:03:21.360 --> 00:03:23.750 align:start position:0%
basically telling Llama Index where you
want<00:03:21.519><c> the</c><00:03:21.760><c> actual</c><00:03:22.159><c> data</c><00:03:22.480><c> stored.</c><00:03:23.200><c> The</c><00:03:23.440><c> easiest</c>

00:03:23.750 --> 00:03:23.760 align:start position:0%
want the actual data stored. The easiest
 

00:03:23.760 --> 00:03:25.589 align:start position:0%
want the actual data stored. The easiest
option<00:03:24.080><c> here</c><00:03:24.239><c> is</c><00:03:24.480><c> to</c><00:03:24.640><c> go</c><00:03:24.800><c> with</c><00:03:24.959><c> the</c><00:03:25.200><c> llama</c>

00:03:25.589 --> 00:03:25.599 align:start position:0%
option here is to go with the llama
 

00:03:25.599 --> 00:03:27.750 align:start position:0%
option here is to go with the llama
index<00:03:26.000><c> managed</c><00:03:26.400><c> option,</c><00:03:26.800><c> but</c><00:03:27.120><c> you</c><00:03:27.280><c> can</c><00:03:27.440><c> also</c>

00:03:27.750 --> 00:03:27.760 align:start position:0%
index managed option, but you can also
 

00:03:27.760 --> 00:03:29.830 align:start position:0%
index managed option, but you can also
select<00:03:28.080><c> from</c><00:03:28.319><c> one</c><00:03:28.480><c> of</c><00:03:28.640><c> these</c><00:03:29.200><c> databases</c>

00:03:29.830 --> 00:03:29.840 align:start position:0%
select from one of these databases
 

00:03:29.840 --> 00:03:32.550 align:start position:0%
select from one of these databases
available<00:03:30.319><c> there</c><00:03:30.640><c> as</c><00:03:31.159><c> well.</c><00:03:32.159><c> Once</c><00:03:32.400><c> you've</c>

00:03:32.550 --> 00:03:32.560 align:start position:0%
available there as well. Once you've
 

00:03:32.560 --> 00:03:34.149 align:start position:0%
available there as well. Once you've
gone<00:03:32.799><c> through</c><00:03:32.959><c> all</c><00:03:33.120><c> of</c><00:03:33.200><c> these,</c><00:03:33.519><c> you</c><00:03:33.680><c> can</c><00:03:33.840><c> also</c>

00:03:34.149 --> 00:03:34.159 align:start position:0%
gone through all of these, you can also
 

00:03:34.159 --> 00:03:36.229 align:start position:0%
gone through all of these, you can also
again<00:03:34.560><c> choose</c><00:03:34.879><c> your</c><00:03:35.200><c> pausing</c><00:03:35.599><c> options</c><00:03:35.920><c> here,</c>

00:03:36.229 --> 00:03:36.239 align:start position:0%
again choose your pausing options here,
 

00:03:36.239 --> 00:03:38.470 align:start position:0%
again choose your pausing options here,
your<00:03:36.480><c> chunking</c><00:03:36.879><c> options</c><00:03:37.200><c> here,</c><00:03:37.760><c> etc.</c><00:03:38.319><c> And</c>

00:03:38.470 --> 00:03:38.480 align:start position:0%
your chunking options here, etc. And
 

00:03:38.480 --> 00:03:40.550 align:start position:0%
your chunking options here, etc. And
you've<00:03:38.799><c> deployed</c><00:03:39.120><c> your</c><00:03:39.360><c> index,</c><00:03:40.000><c> then</c><00:03:40.239><c> you</c><00:03:40.480><c> can</c>

00:03:40.550 --> 00:03:40.560 align:start position:0%
you've deployed your index, then you can
 

00:03:40.560 --> 00:03:42.070 align:start position:0%
you've deployed your index, then you can
start<00:03:40.879><c> querying</c><00:03:41.280><c> it</c><00:03:41.440><c> and</c><00:03:41.680><c> playing</c><00:03:41.920><c> around</c>

00:03:42.070 --> 00:03:42.080 align:start position:0%
start querying it and playing around
 

00:03:42.080 --> 00:03:44.070 align:start position:0%
start querying it and playing around
with<00:03:42.239><c> it.</c><00:03:42.480><c> Here</c><00:03:42.879><c> you</c><00:03:43.120><c> can</c><00:03:43.280><c> see</c><00:03:43.360><c> me</c><00:03:43.599><c> chatting</c>

00:03:44.070 --> 00:03:44.080 align:start position:0%
with it. Here you can see me chatting
 

00:03:44.080 --> 00:03:48.070 align:start position:0%
with it. Here you can see me chatting
with<00:03:45.040><c> uh</c><00:03:45.280><c> my</c><00:03:45.760><c> index</c><00:03:46.400><c> about</c><00:03:47.200><c> um</c><00:03:47.519><c> llama</c>

00:03:48.070 --> 00:03:48.080 align:start position:0%
with uh my index about um llama
 

00:03:48.080 --> 00:03:50.470 align:start position:0%
with uh my index about um llama
workflows,<00:03:48.640><c> llama</c><00:03:49.040><c> index</c><00:03:49.360><c> workflows</c><00:03:50.319><c> uh</c>

00:03:50.470 --> 00:03:50.480 align:start position:0%
workflows, llama index workflows uh
 

00:03:50.480 --> 00:03:53.190 align:start position:0%
workflows, llama index workflows uh
because<00:03:50.720><c> that's</c><00:03:50.959><c> the</c><00:03:51.200><c> one</c><00:03:51.760><c> documentation</c><00:03:53.040><c> uh</c>

00:03:53.190 --> 00:03:53.200 align:start position:0%
because that's the one documentation uh
 

00:03:53.200 --> 00:03:55.990 align:start position:0%
because that's the one documentation uh
page<00:03:53.440><c> I</c><00:03:53.760><c> have</c><00:03:53.920><c> in</c><00:03:54.159><c> my</c><00:03:54.400><c> Google</c><00:03:54.640><c> drive</c><00:03:55.000><c> folder.</c>

00:03:55.990 --> 00:03:56.000 align:start position:0%
page I have in my Google drive folder.
 

00:03:56.000 --> 00:03:59.110 align:start position:0%
page I have in my Google drive folder.
All<00:03:56.080><c> right,</c><00:03:56.480><c> so</c><00:03:56.720><c> we've</c><00:03:57.040><c> gone</c><00:03:57.280><c> through</c><00:03:58.159><c> uh</c><00:03:58.400><c> par</c>

00:03:59.110 --> 00:03:59.120 align:start position:0%
All right, so we've gone through uh par
 

00:03:59.120 --> 00:04:01.270 align:start position:0%
All right, so we've gone through uh par
index<00:03:59.599><c> and</c><00:03:59.840><c> now</c><00:04:00.159><c> let's</c><00:04:00.480><c> have</c><00:04:00.640><c> a</c><00:04:00.879><c> look</c><00:04:00.959><c> at</c>

00:04:01.270 --> 00:04:01.280 align:start position:0%
index and now let's have a look at
 

00:04:01.280 --> 00:04:02.869 align:start position:0%
index and now let's have a look at
extract.<00:04:01.920><c> This</c><00:04:02.080><c> is</c><00:04:02.159><c> one</c><00:04:02.400><c> of</c><00:04:02.400><c> the</c><00:04:02.640><c> latest</c>

00:04:02.869 --> 00:04:02.879 align:start position:0%
extract. This is one of the latest
 

00:04:02.879 --> 00:04:05.429 align:start position:0%
extract. This is one of the latest
products<00:04:03.200><c> we've</c><00:04:03.519><c> added</c><00:04:03.760><c> to</c><00:04:04.000><c> Llama</c><00:04:04.400><c> cloud</c><00:04:05.120><c> and</c>

00:04:05.429 --> 00:04:05.439 align:start position:0%
products we've added to Llama cloud and
 

00:04:05.439 --> 00:04:08.229 align:start position:0%
products we've added to Llama cloud and
the<00:04:05.680><c> idea</c><00:04:06.000><c> is</c><00:04:06.239><c> simple</c><00:04:06.640><c> but</c><00:04:07.120><c> very</c><00:04:07.519><c> effective.</c>

00:04:08.229 --> 00:04:08.239 align:start position:0%
the idea is simple but very effective.
 

00:04:08.239 --> 00:04:11.670 align:start position:0%
the idea is simple but very effective.
You<00:04:08.480><c> may</c><00:04:08.720><c> have</c><00:04:09.120><c> a</c><00:04:09.680><c> file</c><00:04:10.080><c> that</c><00:04:10.319><c> is</c><00:04:10.560><c> maybe</c><00:04:10.799><c> a</c><00:04:11.040><c> CV,</c>

00:04:11.670 --> 00:04:11.680 align:start position:0%
You may have a file that is maybe a CV,
 

00:04:11.680 --> 00:04:14.949 align:start position:0%
You may have a file that is maybe a CV,
an<00:04:12.000><c> invoice,</c><00:04:12.879><c> a</c><00:04:13.120><c> filing</c><00:04:13.599><c> report,</c><00:04:14.239><c> etc.</c><00:04:14.560><c> These</c>

00:04:14.949 --> 00:04:14.959 align:start position:0%
an invoice, a filing report, etc. These
 

00:04:14.959 --> 00:04:17.749 align:start position:0%
an invoice, a filing report, etc. These
types<00:04:15.280><c> of</c><00:04:15.840><c> files</c><00:04:16.320><c> and</c><00:04:16.560><c> documents</c><00:04:17.120><c> have</c><00:04:17.359><c> a</c><00:04:17.600><c> lot</c>

00:04:17.749 --> 00:04:17.759 align:start position:0%
types of files and documents have a lot
 

00:04:17.759 --> 00:04:20.069 align:start position:0%
types of files and documents have a lot
a<00:04:18.000><c> lot</c><00:04:18.079><c> of</c><00:04:18.239><c> information</c><00:04:18.720><c> in</c><00:04:18.959><c> there.</c><00:04:19.519><c> And</c><00:04:19.759><c> let's</c>

00:04:20.069 --> 00:04:20.079 align:start position:0%
a lot of information in there. And let's
 

00:04:20.079 --> 00:04:22.310 align:start position:0%
a lot of information in there. And let's
imagine<00:04:20.560><c> you</c><00:04:20.880><c> actually</c><00:04:21.199><c> want</c><00:04:21.359><c> to</c><00:04:21.519><c> focus</c><00:04:21.840><c> on</c><00:04:22.079><c> a</c>

00:04:22.310 --> 00:04:22.320 align:start position:0%
imagine you actually want to focus on a
 

00:04:22.320 --> 00:04:24.790 align:start position:0%
imagine you actually want to focus on a
select<00:04:22.720><c> few</c><00:04:23.040><c> things.</c><00:04:23.759><c> For</c><00:04:23.840><c> example,</c><00:04:24.400><c> the</c><00:04:24.639><c> name</c>

00:04:24.790 --> 00:04:24.800 align:start position:0%
select few things. For example, the name
 

00:04:24.800 --> 00:04:26.790 align:start position:0%
select few things. For example, the name
of<00:04:24.960><c> the</c><00:04:25.120><c> applicant,</c><00:04:25.840><c> the</c><00:04:26.080><c> email</c><00:04:26.400><c> address</c><00:04:26.639><c> of</c>

00:04:26.790 --> 00:04:26.800 align:start position:0%
of the applicant, the email address of
 

00:04:26.800 --> 00:04:29.510 align:start position:0%
of the applicant, the email address of
the<00:04:26.960><c> applicant,</c><00:04:27.919><c> um</c><00:04:28.080><c> the</c><00:04:28.400><c> degree</c><00:04:28.720><c> of</c><00:04:28.880><c> the</c>

00:04:29.510 --> 00:04:29.520 align:start position:0%
the applicant, um the degree of the
 

00:04:29.520 --> 00:04:32.150 align:start position:0%
the applicant, um the degree of the
applicant,<00:04:30.080><c> and</c><00:04:30.320><c> so</c><00:04:30.479><c> on.</c><00:04:30.880><c> We</c><00:04:31.120><c> make</c><00:04:31.360><c> use</c><00:04:31.600><c> of</c><00:04:31.840><c> the</c>

00:04:32.150 --> 00:04:32.160 align:start position:0%
applicant, and so on. We make use of the
 

00:04:32.160 --> 00:04:33.909 align:start position:0%
applicant, and so on. We make use of the
structured<00:04:32.639><c> outputs</c><00:04:33.120><c> functionality</c><00:04:33.680><c> that</c>

00:04:33.909 --> 00:04:33.919 align:start position:0%
structured outputs functionality that
 

00:04:33.919 --> 00:04:36.550 align:start position:0%
structured outputs functionality that
most<00:04:34.160><c> LLMs</c><00:04:34.639><c> now</c><00:04:34.880><c> support</c><00:04:35.759><c> which</c><00:04:36.080><c> allows</c><00:04:36.400><c> you</c>

00:04:36.550 --> 00:04:36.560 align:start position:0%
most LLMs now support which allows you
 

00:04:36.560 --> 00:04:38.670 align:start position:0%
most LLMs now support which allows you
to<00:04:36.960><c> either</c><00:04:37.440><c> select</c><00:04:37.919><c> from</c><00:04:38.240><c> one</c><00:04:38.400><c> of</c><00:04:38.479><c> the</c>

00:04:38.670 --> 00:04:38.680 align:start position:0%
to either select from one of the
 

00:04:38.680 --> 00:04:41.270 align:start position:0%
to either select from one of the
predefined<00:04:39.680><c> schemas</c><00:04:40.320><c> or</c><00:04:40.639><c> define</c><00:04:40.960><c> your</c><00:04:41.120><c> own</c>

00:04:41.270 --> 00:04:41.280 align:start position:0%
predefined schemas or define your own
 

00:04:41.280 --> 00:04:43.670 align:start position:0%
predefined schemas or define your own
schema<00:04:42.080><c> and</c><00:04:42.320><c> then</c><00:04:42.560><c> once</c><00:04:42.880><c> you</c><00:04:43.120><c> run</c><00:04:43.440><c> an</c>

00:04:43.670 --> 00:04:43.680 align:start position:0%
schema and then once you run an
 

00:04:43.680 --> 00:04:47.189 align:start position:0%
schema and then once you run an
extraction<00:04:44.240><c> agent</c><00:04:44.720><c> over</c><00:04:45.199><c> your</c><00:04:45.720><c> document</c><00:04:46.720><c> then</c>

00:04:47.189 --> 00:04:47.199 align:start position:0%
extraction agent over your document then
 

00:04:47.199 --> 00:04:49.990 align:start position:0%
extraction agent over your document then
this<00:04:47.520><c> extraction</c><00:04:48.080><c> agent</c><00:04:48.479><c> will</c><00:04:48.720><c> be</c><00:04:48.880><c> able</c><00:04:49.120><c> to</c>

00:04:49.990 --> 00:04:50.000 align:start position:0%
this extraction agent will be able to
 

00:04:50.000 --> 00:04:52.070 align:start position:0%
this extraction agent will be able to
fill<00:04:50.320><c> that</c><00:04:50.560><c> schema</c><00:04:50.960><c> in</c><00:04:51.199><c> with</c><00:04:51.440><c> the</c><00:04:51.600><c> relevant</c>

00:04:52.070 --> 00:04:52.080 align:start position:0%
fill that schema in with the relevant
 

00:04:52.080 --> 00:04:54.550 align:start position:0%
fill that schema in with the relevant
information<00:04:52.720><c> that</c><00:04:53.040><c> is</c><00:04:53.360><c> present</c><00:04:53.840><c> in</c><00:04:54.160><c> your</c>

00:04:54.550 --> 00:04:54.560 align:start position:0%
information that is present in your
 

00:04:54.560 --> 00:04:58.070 align:start position:0%
information that is present in your
file.<00:04:55.840><c> One</c><00:04:56.080><c> of</c><00:04:56.160><c> our</c><00:04:56.400><c> latest</c><00:04:56.880><c> upgrades</c><00:04:57.440><c> to</c><00:04:57.680><c> this</c>

00:04:58.070 --> 00:04:58.080 align:start position:0%
file. One of our latest upgrades to this
 

00:04:58.080 --> 00:05:00.710 align:start position:0%
file. One of our latest upgrades to this
feature<00:04:58.479><c> has</c><00:04:58.800><c> also</c><00:04:59.040><c> been</c><00:04:59.520><c> enabling</c><00:05:00.160><c> citations</c>

00:05:00.710 --> 00:05:00.720 align:start position:0%
feature has also been enabling citations
 

00:05:00.720 --> 00:05:02.550 align:start position:0%
feature has also been enabling citations
and<00:05:00.960><c> reasoning.</c><00:05:01.360><c> And</c><00:05:01.520><c> I</c><00:05:01.759><c> made</c><00:05:01.919><c> a</c><00:05:02.160><c> whole</c><00:05:02.320><c> other</c>

00:05:02.550 --> 00:05:02.560 align:start position:0%
and reasoning. And I made a whole other
 

00:05:02.560 --> 00:05:04.469 align:start position:0%
and reasoning. And I made a whole other
video<00:05:02.880><c> about</c><00:05:03.199><c> this</c><00:05:03.440><c> and</c><00:05:03.680><c> how</c><00:05:03.840><c> you</c><00:05:04.000><c> can</c><00:05:04.080><c> enable</c>

00:05:04.469 --> 00:05:04.479 align:start position:0%
video about this and how you can enable
 

00:05:04.479 --> 00:05:07.110 align:start position:0%
video about this and how you can enable
citations<00:05:05.040><c> and</c><00:05:05.280><c> reasoning</c><00:05:05.840><c> which</c><00:05:06.160><c> basically</c>

00:05:07.110 --> 00:05:07.120 align:start position:0%
citations and reasoning which basically
 

00:05:07.120 --> 00:05:09.110 align:start position:0%
citations and reasoning which basically
allows<00:05:07.520><c> you</c><00:05:07.759><c> to</c><00:05:08.080><c> not</c><00:05:08.320><c> only</c><00:05:08.639><c> see</c><00:05:08.880><c> the</c>

00:05:09.110 --> 00:05:09.120 align:start position:0%
allows you to not only see the
 

00:05:09.120 --> 00:05:11.670 align:start position:0%
allows you to not only see the
structured<00:05:09.600><c> output</c><00:05:10.160><c> based</c><00:05:10.479><c> on</c><00:05:10.720><c> your</c><00:05:11.440><c> u</c>

00:05:11.670 --> 00:05:11.680 align:start position:0%
structured output based on your u
 

00:05:11.680 --> 00:05:14.550 align:start position:0%
structured output based on your u
document<00:05:12.160><c> but</c><00:05:12.400><c> also</c><00:05:13.199><c> see</c><00:05:13.520><c> if</c><00:05:13.759><c> you</c><00:05:14.000><c> have</c><00:05:14.320><c> used</c>

00:05:14.550 --> 00:05:14.560 align:start position:0%
document but also see if you have used
 

00:05:14.560 --> 00:05:17.350 align:start position:0%
document but also see if you have used
an<00:05:14.720><c> LLM</c><00:05:15.120><c> or</c><00:05:15.360><c> agent</c><00:05:15.680><c> in</c><00:05:15.840><c> the</c><00:05:16.000><c> process.</c><00:05:16.880><c> you</c><00:05:17.120><c> can</c>

00:05:17.350 --> 00:05:17.360 align:start position:0%
an LLM or agent in the process. you can
 

00:05:17.360 --> 00:05:19.029 align:start position:0%
an LLM or agent in the process. you can
understand<00:05:17.759><c> what</c><00:05:18.000><c> the</c><00:05:18.160><c> reasoning</c><00:05:18.560><c> behind</c>

00:05:19.029 --> 00:05:19.039 align:start position:0%
understand what the reasoning behind
 

00:05:19.039 --> 00:05:21.590 align:start position:0%
understand what the reasoning behind
certain<00:05:19.440><c> choices</c><00:05:19.840><c> were</c><00:05:20.479><c> and</c><00:05:20.960><c> specific</c>

00:05:21.590 --> 00:05:21.600 align:start position:0%
certain choices were and specific
 

00:05:21.600 --> 00:05:23.830 align:start position:0%
certain choices were and specific
citations<00:05:22.400><c> where</c><00:05:22.720><c> the</c><00:05:22.960><c> actual</c><00:05:23.360><c> information</c>

00:05:23.830 --> 00:05:23.840 align:start position:0%
citations where the actual information
 

00:05:23.840 --> 00:05:26.070 align:start position:0%
citations where the actual information
from<00:05:24.160><c> your</c><00:05:24.400><c> document</c><00:05:24.880><c> came</c><00:05:25.120><c> from.</c><00:05:25.759><c> If</c><00:05:25.919><c> you</c>

00:05:26.070 --> 00:05:26.080 align:start position:0%
from your document came from. If you
 

00:05:26.080 --> 00:05:27.270 align:start position:0%
from your document came from. If you
want<00:05:26.160><c> to</c><00:05:26.240><c> have</c><00:05:26.320><c> a</c><00:05:26.479><c> look</c><00:05:26.560><c> at</c><00:05:26.720><c> that,</c><00:05:26.960><c> I'm</c><00:05:27.120><c> going</c>

00:05:27.270 --> 00:05:27.280 align:start position:0%
want to have a look at that, I'm going
 

00:05:27.280 --> 00:05:29.710 align:start position:0%
want to have a look at that, I'm going
to<00:05:27.440><c> link</c><00:05:27.759><c> the</c><00:05:28.080><c> video</c><00:05:28.320><c> in</c><00:05:28.560><c> the</c><00:05:28.720><c> description</c><00:05:29.360><c> as</c>

00:05:29.710 --> 00:05:29.720 align:start position:0%
to link the video in the description as
 

00:05:29.720 --> 00:05:33.029 align:start position:0%
to link the video in the description as
well.<00:05:30.720><c> And</c><00:05:31.120><c> that's</c><00:05:31.440><c> it.</c><00:05:32.160><c> In</c><00:05:32.320><c> the</c><00:05:32.479><c> next</c><00:05:32.720><c> video,</c>

00:05:33.029 --> 00:05:33.039 align:start position:0%
well. And that's it. In the next video,
 

00:05:33.039 --> 00:05:35.909 align:start position:0%
well. And that's it. In the next video,
we<00:05:33.280><c> will</c><00:05:33.520><c> build</c><00:05:33.759><c> something</c><00:05:34.320><c> with</c><00:05:34.800><c> Llama</c><00:05:35.280><c> Index</c>

00:05:35.909 --> 00:05:35.919 align:start position:0%
we will build something with Llama Index
 

00:05:35.919 --> 00:05:39.350 align:start position:0%
we will build something with Llama Index
workflows<00:05:36.800><c> making</c><00:05:37.120><c> use</c><00:05:37.440><c> of</c><00:05:38.320><c> all</c><00:05:38.639><c> three</c><00:05:38.960><c> or</c>

00:05:39.350 --> 00:05:39.360 align:start position:0%
workflows making use of all three or
 

00:05:39.360 --> 00:05:42.070 align:start position:0%
workflows making use of all three or
some<00:05:39.600><c> of</c><00:05:39.759><c> these</c><00:05:40.479><c> Llama</c><00:05:40.880><c> cloud</c><00:05:41.199><c> tools.</c><00:05:41.759><c> See</c><00:05:41.919><c> you</c>

00:05:42.070 --> 00:05:42.080 align:start position:0%
some of these Llama cloud tools. See you
 

00:05:42.080 --> 00:05:44.400 align:start position:0%
some of these Llama cloud tools. See you
then.

