WEBVTT
Kind: captions
Language: en

00:00:00.320 --> 00:00:01.670 align:start position:0%
 
sometimes<00:00:00.640><c> you</c><00:00:00.799><c> might</c><00:00:01.000><c> need</c><00:00:01.199><c> a</c><00:00:01.360><c> little</c>

00:00:01.670 --> 00:00:01.680 align:start position:0%
sometimes you might need a little
 

00:00:01.680 --> 00:00:03.990 align:start position:0%
sometimes you might need a little
inspiration<00:00:02.200><c> to</c><00:00:02.399><c> get</c><00:00:02.600><c> going</c><00:00:03.520><c> co-pilot</c>

00:00:03.990 --> 00:00:04.000 align:start position:0%
inspiration to get going co-pilot
 

00:00:04.000 --> 00:00:05.829 align:start position:0%
inspiration to get going co-pilot
workspace<00:00:04.480><c> can</c><00:00:04.640><c> help</c><00:00:04.799><c> you</c><00:00:05.040><c> get</c><00:00:05.200><c> your</c><00:00:05.359><c> creative</c>

00:00:05.829 --> 00:00:05.839 align:start position:0%
workspace can help you get your creative
 

00:00:05.839 --> 00:00:07.749 align:start position:0%
workspace can help you get your creative
process<00:00:06.200><c> started</c><00:00:06.720><c> by</c><00:00:06.839><c> building</c><00:00:07.200><c> from</c><00:00:07.399><c> another</c>

00:00:07.749 --> 00:00:07.759 align:start position:0%
process started by building from another
 

00:00:07.759 --> 00:00:10.589 align:start position:0%
process started by building from another
repository<00:00:08.519><c> as</c><00:00:08.639><c> a</c><00:00:08.880><c> template</c><00:00:09.880><c> it</c><00:00:10.040><c> starts</c><00:00:10.320><c> in</c><00:00:10.400><c> a</c>

00:00:10.589 --> 00:00:10.599 align:start position:0%
repository as a template it starts in a
 

00:00:10.599 --> 00:00:12.589 align:start position:0%
repository as a template it starts in a
repository<00:00:11.400><c> by</c><00:00:11.559><c> outlining</c><00:00:12.040><c> the</c><00:00:12.200><c> task</c><00:00:12.480><c> that</c>

00:00:12.589 --> 00:00:12.599 align:start position:0%
repository by outlining the task that
 

00:00:12.599 --> 00:00:14.950 align:start position:0%
repository by outlining the task that
you<00:00:12.719><c> want</c><00:00:13.040><c> co-pilot</c><00:00:13.559><c> workspace</c><00:00:13.960><c> to</c><00:00:14.120><c> achieve</c>

00:00:14.950 --> 00:00:14.960 align:start position:0%
you want co-pilot workspace to achieve
 

00:00:14.960 --> 00:00:17.269 align:start position:0%
you want co-pilot workspace to achieve
and<00:00:15.120><c> from</c><00:00:15.360><c> there</c><00:00:16.039><c> it</c><00:00:16.160><c> will</c><00:00:16.320><c> convert</c><00:00:16.720><c> that</c><00:00:16.920><c> task</c>

00:00:17.269 --> 00:00:17.279 align:start position:0%
and from there it will convert that task
 

00:00:17.279 --> 00:00:19.189 align:start position:0%
and from there it will convert that task
into<00:00:17.480><c> a</c><00:00:17.600><c> specification</c><00:00:18.600><c> with</c><00:00:18.720><c> a</c><00:00:18.880><c> current</c>

00:00:19.189 --> 00:00:19.199 align:start position:0%
into a specification with a current
 

00:00:19.199 --> 00:00:21.950 align:start position:0%
into a specification with a current
state<00:00:19.720><c> and</c><00:00:19.840><c> a</c><00:00:20.000><c> proposed</c><00:00:20.480><c> state</c><00:00:21.400><c> now</c><00:00:21.600><c> before</c><00:00:21.800><c> we</c>

00:00:21.950 --> 00:00:21.960 align:start position:0%
state and a proposed state now before we
 

00:00:21.960 --> 00:00:24.070 align:start position:0%
state and a proposed state now before we
make<00:00:22.160><c> any</c><00:00:22.359><c> changes</c><00:00:23.039><c> let's</c><00:00:23.279><c> open</c><00:00:23.519><c> up</c><00:00:23.640><c> the</c><00:00:23.800><c> live</c>

00:00:24.070 --> 00:00:24.080 align:start position:0%
make any changes let's open up the live
 

00:00:24.080 --> 00:00:26.630 align:start position:0%
make any changes let's open up the live
preview<00:00:24.599><c> so</c><00:00:24.800><c> we</c><00:00:24.920><c> can</c><00:00:25.039><c> see</c><00:00:25.240><c> our</c><00:00:25.480><c> progress</c><00:00:26.439><c> and</c>

00:00:26.630 --> 00:00:26.640 align:start position:0%
preview so we can see our progress and
 

00:00:26.640 --> 00:00:28.790 align:start position:0%
preview so we can see our progress and
finally<00:00:27.279><c> put</c><00:00:27.439><c> it</c><00:00:27.560><c> all</c><00:00:27.800><c> side</c><00:00:28.039><c> by</c><00:00:28.279><c> side</c><00:00:28.519><c> so</c><00:00:28.679><c> we</c>

00:00:28.790 --> 00:00:28.800 align:start position:0%
finally put it all side by side so we
 

00:00:28.800 --> 00:00:31.669 align:start position:0%
finally put it all side by side so we
can<00:00:28.960><c> stay</c><00:00:29.119><c> in</c><00:00:29.240><c> the</c><00:00:29.320><c> flow</c><00:00:29.679><c> and</c><00:00:30.000><c> keep</c><00:00:30.679><c> iterating</c>

00:00:31.669 --> 00:00:31.679 align:start position:0%
can stay in the flow and keep iterating
 

00:00:31.679 --> 00:00:33.110 align:start position:0%
can stay in the flow and keep iterating
now<00:00:31.840><c> we</c><00:00:31.960><c> can</c><00:00:32.079><c> move</c><00:00:32.279><c> from</c><00:00:32.399><c> the</c><00:00:32.559><c> specification</c>

00:00:33.110 --> 00:00:33.120 align:start position:0%
now we can move from the specification
 

00:00:33.120 --> 00:00:34.830 align:start position:0%
now we can move from the specification
to<00:00:33.320><c> the</c><00:00:33.440><c> plan</c><00:00:33.800><c> and</c><00:00:33.960><c> review</c><00:00:34.320><c> the</c><00:00:34.440><c> steps</c><00:00:34.680><c> the</c>

00:00:34.830 --> 00:00:34.840 align:start position:0%
to the plan and review the steps the
 

00:00:34.840 --> 00:00:36.750 align:start position:0%
to the plan and review the steps the
co-pilot<00:00:35.320><c> workspace</c><00:00:35.760><c> will</c><00:00:35.920><c> take</c><00:00:36.280><c> to</c><00:00:36.480><c> achieve</c>

00:00:36.750 --> 00:00:36.760 align:start position:0%
co-pilot workspace will take to achieve
 

00:00:36.760 --> 00:00:39.310 align:start position:0%
co-pilot workspace will take to achieve
our<00:00:37.000><c> desired</c><00:00:37.480><c> task</c><00:00:38.440><c> after</c><00:00:38.680><c> we've</c><00:00:38.920><c> progressed</c>

00:00:39.310 --> 00:00:39.320 align:start position:0%
our desired task after we've progressed
 

00:00:39.320 --> 00:00:41.310 align:start position:0%
our desired task after we've progressed
through<00:00:39.520><c> these</c><00:00:39.680><c> stages</c><00:00:40.399><c> the</c><00:00:40.559><c> suggestions</c><00:00:41.160><c> are</c>

00:00:41.310 --> 00:00:41.320 align:start position:0%
through these stages the suggestions are
 

00:00:41.320 --> 00:00:44.470 align:start position:0%
through these stages the suggestions are
streamed<00:00:41.920><c> to</c><00:00:42.079><c> my</c><00:00:42.800><c> environment</c><00:00:43.800><c> these</c><00:00:44.000><c> changes</c>

00:00:44.470 --> 00:00:44.480 align:start position:0%
streamed to my environment these changes
 

00:00:44.480 --> 00:00:46.510 align:start position:0%
streamed to my environment these changes
introduce<00:00:44.920><c> new</c><00:00:45.160><c> dependencies</c><00:00:45.879><c> which</c><00:00:46.120><c> aren't</c>

00:00:46.510 --> 00:00:46.520 align:start position:0%
introduce new dependencies which aren't
 

00:00:46.520 --> 00:00:48.869 align:start position:0%
introduce new dependencies which aren't
installed<00:00:47.480><c> so</c><00:00:47.680><c> let's</c><00:00:47.920><c> fix</c><00:00:48.199><c> that</c><00:00:48.399><c> by</c><00:00:48.520><c> opening</c>

00:00:48.869 --> 00:00:48.879 align:start position:0%
installed so let's fix that by opening
 

00:00:48.879 --> 00:00:50.950 align:start position:0%
installed so let's fix that by opening
the<00:00:49.039><c> terminal</c><00:00:49.800><c> stopping</c><00:00:50.199><c> the</c><00:00:50.360><c> app</c><00:00:50.800><c> and</c>

00:00:50.950 --> 00:00:50.960 align:start position:0%
the terminal stopping the app and
 

00:00:50.960 --> 00:00:53.830 align:start position:0%
the terminal stopping the app and
running<00:00:51.280><c> npm</c><00:00:51.800><c> install</c><00:00:52.320><c> to</c><00:00:52.480><c> get</c><00:00:52.640><c> the</c><00:00:52.760><c> latest</c>

00:00:53.830 --> 00:00:53.840 align:start position:0%
running npm install to get the latest
 

00:00:53.840 --> 00:00:57.110 align:start position:0%
running npm install to get the latest
packages<00:00:54.840><c> ah</c><00:00:55.399><c> that</c><00:00:55.559><c> looks</c><00:00:55.840><c> better</c><00:00:56.680><c> but</c><00:00:56.840><c> I</c><00:00:56.920><c> want</c>

00:00:57.110 --> 00:00:57.120 align:start position:0%
packages ah that looks better but I want
 

00:00:57.120 --> 00:00:59.189 align:start position:0%
packages ah that looks better but I want
to<00:00:57.320><c> customize</c><00:00:57.760><c> it</c><00:00:57.879><c> a</c><00:00:58.000><c> little</c><00:00:58.199><c> more</c><00:00:58.719><c> so</c><00:00:58.960><c> this</c><00:00:59.039><c> is</c>

00:00:59.189 --> 00:00:59.199 align:start position:0%
to customize it a little more so this is
 

00:00:59.199 --> 00:01:01.270 align:start position:0%
to customize it a little more so this is
where<00:00:59.359><c> I</c><00:00:59.480><c> started</c><00:00:59.960><c> operating</c><00:01:00.320><c> with</c><00:01:00.480><c> co-pilot</c>

00:01:01.270 --> 00:01:01.280 align:start position:0%
where I started operating with co-pilot
 

00:01:01.280 --> 00:01:03.509 align:start position:0%
where I started operating with co-pilot
workspace<00:01:02.280><c> let's</c><00:01:02.519><c> add</c><00:01:02.680><c> a</c><00:01:02.800><c> couple</c><00:01:03.000><c> of</c><00:01:03.160><c> buttons</c>

00:01:03.509 --> 00:01:03.519 align:start position:0%
workspace let's add a couple of buttons
 

00:01:03.519 --> 00:01:06.109 align:start position:0%
workspace let's add a couple of buttons
to<00:01:03.680><c> easily</c><00:01:04.040><c> Mark</c><00:01:04.320><c> all</c><00:01:04.559><c> tasks</c><00:01:05.000><c> completed</c><00:01:05.880><c> and</c>

00:01:06.109 --> 00:01:06.119 align:start position:0%
to easily Mark all tasks completed and
 

00:01:06.119 --> 00:01:09.149 align:start position:0%
to easily Mark all tasks completed and
delete<00:01:06.479><c> the</c><00:01:06.600><c> completed</c><00:01:07.400><c> tasks</c><00:01:08.400><c> that's</c><00:01:08.640><c> done</c>

00:01:09.149 --> 00:01:09.159 align:start position:0%
delete the completed tasks that's done
 

00:01:09.159 --> 00:01:12.030 align:start position:0%
delete the completed tasks that's done
and<00:01:09.320><c> let's</c><00:01:09.560><c> just</c><00:01:09.720><c> test</c><00:01:09.920><c> that</c>

00:01:12.030 --> 00:01:12.040 align:start position:0%
and let's just test that
 

00:01:12.040 --> 00:01:15.429 align:start position:0%
and let's just test that
out<00:01:13.040><c> it</c><00:01:13.159><c> looks</c><00:01:13.600><c> good</c><00:01:14.600><c> but</c><00:01:14.799><c> what</c><00:01:14.920><c> if</c><00:01:15.080><c> I</c><00:01:15.200><c> wanted</c>

00:01:15.429 --> 00:01:15.439 align:start position:0%
out it looks good but what if I wanted
 

00:01:15.439 --> 00:01:17.870 align:start position:0%
out it looks good but what if I wanted
to<00:01:15.560><c> go</c><00:01:15.759><c> back</c><00:01:15.960><c> to</c><00:01:16.119><c> an</c><00:01:16.200><c> earlier</c><00:01:16.600><c> State</c><00:01:17.600><c> I</c><00:01:17.680><c> can</c>

00:01:17.870 --> 00:01:17.880 align:start position:0%
to go back to an earlier State I can
 

00:01:17.880 --> 00:01:20.710 align:start position:0%
to go back to an earlier State I can
click<00:01:18.200><c> undo</c><00:01:18.799><c> to</c><00:01:19.000><c> go</c><00:01:19.240><c> back</c><00:01:19.960><c> and</c><00:01:20.119><c> notice</c><00:01:20.439><c> how</c><00:01:20.560><c> the</c>

00:01:20.710 --> 00:01:20.720 align:start position:0%
click undo to go back and notice how the
 

00:01:20.720 --> 00:01:22.749 align:start position:0%
click undo to go back and notice how the
live<00:01:20.960><c> app</c><00:01:21.159><c> view</c><00:01:21.479><c> updates</c><00:01:22.200><c> as</c><00:01:22.320><c> well</c><00:01:22.479><c> as</c><00:01:22.640><c> the</c>

00:01:22.749 --> 00:01:22.759 align:start position:0%
live app view updates as well as the
 

00:01:22.759 --> 00:01:25.749 align:start position:0%
live app view updates as well as the
items<00:01:23.040><c> in</c><00:01:23.200><c> my</c><00:01:23.439><c> plan</c><00:01:24.439><c> I</c><00:01:24.520><c> can</c><00:01:24.680><c> then</c><00:01:24.880><c> click</c><00:01:25.119><c> redo</c>

00:01:25.749 --> 00:01:25.759 align:start position:0%
items in my plan I can then click redo
 

00:01:25.759 --> 00:01:28.390 align:start position:0%
items in my plan I can then click redo
to<00:01:25.960><c> move</c><00:01:26.200><c> forwards</c><00:01:26.799><c> to</c><00:01:27.040><c> where</c><00:01:27.280><c> I</c><00:01:27.439><c> was</c>

00:01:28.390 --> 00:01:28.400 align:start position:0%
to move forwards to where I was
 

00:01:28.400 --> 00:01:30.670 align:start position:0%
to move forwards to where I was
originally<00:01:29.400><c> we</c><00:01:29.520><c> can</c><00:01:29.640><c> also</c><00:01:29.920><c> Al</c><00:01:30.079><c> switch</c><00:01:30.360><c> over</c>

00:01:30.670 --> 00:01:30.680 align:start position:0%
originally we can also Al switch over
 

00:01:30.680 --> 00:01:32.950 align:start position:0%
originally we can also Al switch over
and<00:01:30.920><c> update</c><00:01:31.240><c> the</c><00:01:31.400><c> code</c><00:01:31.759><c> directly</c><00:01:32.159><c> in</c><00:01:32.280><c> our</c><00:01:32.520><c> code</c>

00:01:32.950 --> 00:01:32.960 align:start position:0%
and update the code directly in our code
 

00:01:32.960 --> 00:01:35.350 align:start position:0%
and update the code directly in our code
space<00:01:33.960><c> let's</c><00:01:34.159><c> make</c><00:01:34.360><c> several</c><00:01:34.720><c> changes</c>

00:01:35.350 --> 00:01:35.360 align:start position:0%
space let's make several changes
 

00:01:35.360 --> 00:01:37.190 align:start position:0%
space let's make several changes
including<00:01:35.799><c> the</c><00:01:35.920><c> background</c><00:01:36.600><c> alignment</c><00:01:37.040><c> of</c>

00:01:37.190 --> 00:01:37.200 align:start position:0%
including the background alignment of
 

00:01:37.200 --> 00:01:39.350 align:start position:0%
including the background alignment of
all<00:01:37.399><c> elements</c><00:01:38.119><c> Tidy</c><00:01:38.439><c> Up</c><00:01:38.600><c> The</c><00:01:38.720><c> Styling</c><00:01:39.040><c> on</c><00:01:39.200><c> the</c>

00:01:39.350 --> 00:01:39.360 align:start position:0%
all elements Tidy Up The Styling on the
 

00:01:39.360 --> 00:01:41.990 align:start position:0%
all elements Tidy Up The Styling on the
task<00:01:39.680><c> list</c><00:01:40.320><c> and</c><00:01:40.720><c> the</c><00:01:40.840><c> input</c>

00:01:41.990 --> 00:01:42.000 align:start position:0%
task list and the input
 

00:01:42.000 --> 00:01:44.310 align:start position:0%
task list and the input
form<00:01:43.000><c> notice</c><00:01:43.399><c> that</c><00:01:43.560><c> the</c><00:01:43.640><c> live</c><00:01:43.880><c> view</c><00:01:44.079><c> on</c><00:01:44.159><c> the</c>

00:01:44.310 --> 00:01:44.320 align:start position:0%
form notice that the live view on the
 

00:01:44.320 --> 00:01:47.469 align:start position:0%
form notice that the live view on the
right<00:01:44.719><c> updates</c><00:01:45.479><c> as</c><00:01:45.600><c> we</c><00:01:45.759><c> make</c><00:01:46.280><c> changes</c><00:01:47.280><c> they</c>

00:01:47.469 --> 00:01:47.479 align:start position:0%
right updates as we make changes they
 

00:01:47.479 --> 00:01:50.310 align:start position:0%
right updates as we make changes they
even<00:01:47.680><c> sync</c><00:01:48.119><c> back</c><00:01:48.560><c> into</c><00:01:48.920><c> co-pilot</c>

00:01:50.310 --> 00:01:50.320 align:start position:0%
even sync back into co-pilot
 

00:01:50.320 --> 00:01:53.030 align:start position:0%
even sync back into co-pilot
workspace<00:01:51.320><c> now</c><00:01:51.520><c> I'm</c><00:01:51.680><c> no</c><00:01:51.920><c> designer</c><00:01:52.680><c> so</c><00:01:52.920><c> that</c>

00:01:53.030 --> 00:01:53.040 align:start position:0%
workspace now I'm no designer so that
 

00:01:53.040 --> 00:01:54.590 align:start position:0%
workspace now I'm no designer so that
could<00:01:53.159><c> be</c><00:01:53.280><c> a</c><00:01:53.399><c> future</c><00:01:53.759><c> opportunity</c><00:01:54.399><c> to</c>

00:01:54.590 --> 00:01:54.600 align:start position:0%
could be a future opportunity to
 

00:01:54.600 --> 00:01:55.990 align:start position:0%
could be a future opportunity to
collaborate<00:01:55.000><c> with</c><00:01:55.159><c> my</c><00:01:55.240><c> colleagues</c><00:01:55.560><c> in</c><00:01:55.640><c> a</c><00:01:55.759><c> PO</c>

00:01:55.990 --> 00:01:56.000 align:start position:0%
collaborate with my colleagues in a PO
 

00:01:56.000 --> 00:01:57.670 align:start position:0%
collaborate with my colleagues in a PO
request<00:01:56.439><c> and</c><00:01:56.600><c> improve</c><00:01:56.960><c> the</c>

00:01:57.670 --> 00:01:57.680 align:start position:0%
request and improve the
 

00:01:57.680 --> 00:02:00.709 align:start position:0%
request and improve the
UI<00:01:58.680><c> speaking</c><00:01:59.039><c> of</c><00:01:59.200><c> collaboration</c><00:02:00.280><c> now</c><00:02:00.479><c> that</c><00:02:00.600><c> we</c>

00:02:00.709 --> 00:02:00.719 align:start position:0%
UI speaking of collaboration now that we
 

00:02:00.719 --> 00:02:02.749 align:start position:0%
UI speaking of collaboration now that we
have<00:02:00.799><c> a</c><00:02:00.960><c> scaffold</c><00:02:01.360><c> for</c><00:02:01.479><c> our</c><00:02:01.640><c> to-do</c><00:02:02.000><c> app</c><00:02:02.479><c> we</c><00:02:02.600><c> can</c>

00:02:02.749 --> 00:02:02.759 align:start position:0%
have a scaffold for our to-do app we can
 

00:02:02.759 --> 00:02:04.670 align:start position:0%
have a scaffold for our to-do app we can
use<00:02:02.960><c> the</c><00:02:03.079><c> share</c><00:02:03.399><c> button</c><00:02:03.840><c> to</c><00:02:04.000><c> let</c><00:02:04.200><c> others</c><00:02:04.479><c> know</c>

00:02:04.670 --> 00:02:04.680 align:start position:0%
use the share button to let others know
 

00:02:04.680 --> 00:02:06.389 align:start position:0%
use the share button to let others know
what<00:02:04.840><c> we've</c><00:02:05.039><c> been</c><00:02:05.200><c> working</c>

00:02:06.389 --> 00:02:06.399 align:start position:0%
what we've been working
 

00:02:06.399 --> 00:02:09.749 align:start position:0%
what we've been working
on<00:02:07.399><c> okay</c><00:02:08.039><c> let's</c><00:02:08.280><c> now</c><00:02:08.479><c> go</c><00:02:08.640><c> and</c><00:02:08.840><c> create</c><00:02:09.160><c> the</c>

00:02:09.749 --> 00:02:09.759 align:start position:0%
on okay let's now go and create the
 

00:02:09.759 --> 00:02:12.030 align:start position:0%
on okay let's now go and create the
repository<00:02:10.759><c> the</c><00:02:10.920><c> template</c><00:02:11.360><c> repository</c><00:02:11.879><c> had</c>

00:02:12.030 --> 00:02:12.040 align:start position:0%
repository the template repository had
 

00:02:12.040 --> 00:02:14.550 align:start position:0%
repository the template repository had
to<00:02:12.200><c> get</c><00:02:12.560><c> actions</c><00:02:12.879><c> yaml</c><00:02:13.280><c> file</c><00:02:13.800><c> which</c><00:02:13.959><c> means</c><00:02:14.319><c> our</c>

00:02:14.550 --> 00:02:14.560 align:start position:0%
to get actions yaml file which means our
 

00:02:14.560 --> 00:02:15.869 align:start position:0%
to get actions yaml file which means our
first<00:02:14.840><c> build</c><00:02:15.120><c> and</c><00:02:15.280><c> deployment</c><00:02:15.680><c> appr</c>

00:02:15.869 --> 00:02:15.879 align:start position:0%
first build and deployment appr
 

00:02:15.879 --> 00:02:18.550 align:start position:0%
first build and deployment appr
production<00:02:16.519><c> have</c><00:02:16.680><c> already</c><00:02:17.040><c> kicked</c><00:02:17.360><c> off</c><00:02:18.319><c> let's</c>

00:02:18.550 --> 00:02:18.560 align:start position:0%
production have already kicked off let's
 

00:02:18.560 --> 00:02:20.830 align:start position:0%
production have already kicked off let's
click<00:02:18.760><c> the</c><00:02:18.840><c> link</c><00:02:19.040><c> and</c><00:02:19.200><c> check</c><00:02:19.360><c> it</c><00:02:19.599><c> out</c><00:02:20.599><c> we've</c>

00:02:20.830 --> 00:02:20.840 align:start position:0%
click the link and check it out we've
 

00:02:20.840 --> 00:02:22.350 align:start position:0%
click the link and check it out we've
been<00:02:21.000><c> able</c><00:02:21.239><c> to</c><00:02:21.400><c> stay</c><00:02:21.560><c> in</c><00:02:21.680><c> the</c><00:02:21.760><c> flow</c><00:02:22.120><c> and</c>

00:02:22.350 --> 00:02:22.360 align:start position:0%
been able to stay in the flow and
 

00:02:22.360 --> 00:02:24.910 align:start position:0%
been able to stay in the flow and
iteratively<00:02:22.959><c> solve</c><00:02:23.280><c> our</c><00:02:23.519><c> tasks</c><00:02:24.200><c> thanks</c><00:02:24.680><c> to</c>

00:02:24.910 --> 00:02:24.920 align:start position:0%
iteratively solve our tasks thanks to
 

00:02:24.920 --> 00:02:28.000 align:start position:0%
iteratively solve our tasks thanks to
co-pilot<00:02:25.480><c> workspace</c>

00:02:28.000 --> 00:02:28.010 align:start position:0%
co-pilot workspace
 

00:02:28.010 --> 00:02:31.220 align:start position:0%
co-pilot workspace
[Music]

