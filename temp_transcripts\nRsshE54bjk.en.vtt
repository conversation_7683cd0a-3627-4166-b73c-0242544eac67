WEBVTT
Kind: captions
Language: en

00:00:00.560 --> 00:00:02.710 align:start position:0%
 
hi<00:00:00.880><c> this</c><00:00:00.960><c> is</c><00:00:01.079><c> floran</c><00:00:01.520><c> <PERSON><PERSON></c><00:00:01.880><c> with</c><00:00:02.080><c> GitHub</c><00:00:02.560><c> and</c>

00:00:02.710 --> 00:00:02.720 align:start position:0%
hi this is flora<PERSON> with GitHub and
 

00:00:02.720 --> 00:00:04.510 align:start position:0%
hi this is flora<PERSON> with GitHub and
I'm<00:00:02.919><c> going</c><00:00:03.120><c> to</c><00:00:03.320><c> show</c><00:00:03.560><c> you</c><00:00:03.879><c> how</c><00:00:04.040><c> to</c><00:00:04.240><c> handle</c>

00:00:04.510 --> 00:00:04.520 align:start position:0%
I'm going to show you how to handle
 

00:00:04.520 --> 00:00:07.150 align:start position:0%
I'm going to show you how to handle
inline<00:00:04.960><c> suggestions</c><00:00:05.720><c> from</c><00:00:05.960><c> GitHub</c><00:00:06.319><c> co-pilot</c>

00:00:07.150 --> 00:00:07.160 align:start position:0%
inline suggestions from GitHub co-pilot
 

00:00:07.160 --> 00:00:09.190 align:start position:0%
inline suggestions from GitHub co-pilot
using<00:00:07.399><c> a</c><00:00:07.520><c> screen</c><00:00:07.759><c> beer</c><00:00:08.240><c> I</c><00:00:08.320><c> will</c><00:00:08.440><c> be</c><00:00:08.519><c> using</c><00:00:08.760><c> mvda</c>

00:00:09.190 --> 00:00:09.200 align:start position:0%
using a screen beer I will be using mvda
 

00:00:09.200 --> 00:00:12.230 align:start position:0%
using a screen beer I will be using mvda
202<00:00:09.440><c> 24.1</c><00:00:10.200><c> on</c><00:00:10.360><c> Windows</c><00:00:10.679><c> 11</c><00:00:11.280><c> using</c><00:00:11.599><c> the</c><00:00:11.799><c> GitHub</c>

00:00:12.230 --> 00:00:12.240 align:start position:0%
202 24.1 on Windows 11 using the GitHub
 

00:00:12.240 --> 00:00:14.749 align:start position:0%
202 24.1 on Windows 11 using the GitHub
co-pilot<00:00:12.840><c> extension</c><00:00:13.559><c> with</c><00:00:13.679><c> nvs</c><00:00:14.160><c> code</c><00:00:14.440><c> version</c>

00:00:14.749 --> 00:00:14.759 align:start position:0%
co-pilot extension with nvs code version
 

00:00:14.759 --> 00:00:18.349 align:start position:0%
co-pilot extension with nvs code version
1.88<00:00:15.639><c> which</c><00:00:15.759><c> is</c><00:00:15.839><c> the</c><00:00:16.039><c> March</c><00:00:16.600><c> 2024</c><00:00:17.400><c> release</c><00:00:18.160><c> to</c>

00:00:18.349 --> 00:00:18.359 align:start position:0%
1.88 which is the March 2024 release to
 

00:00:18.359 --> 00:00:19.910 align:start position:0%
1.88 which is the March 2024 release to
start<00:00:18.600><c> with</c><00:00:18.760><c> I'm</c><00:00:18.880><c> going</c><00:00:19.039><c> to</c><00:00:19.240><c> type</c><00:00:19.439><c> up</c><00:00:19.600><c> a</c><00:00:19.760><c> line</c>

00:00:19.910 --> 00:00:19.920 align:start position:0%
start with I'm going to type up a line
 

00:00:19.920 --> 00:00:21.830 align:start position:0%
start with I'm going to type up a line
of<00:00:20.119><c> code</c><00:00:20.880><c> just</c><00:00:21.000><c> to</c><00:00:21.160><c> give</c><00:00:21.279><c> us</c><00:00:21.439><c> something</c><00:00:21.680><c> to</c>

00:00:21.830 --> 00:00:21.840 align:start position:0%
of code just to give us something to
 

00:00:21.840 --> 00:00:22.990 align:start position:0%
of code just to give us something to
work<00:00:22.119><c> with</c>

00:00:22.990 --> 00:00:23.000 align:start position:0%
work with
 

00:00:23.000 --> 00:00:27.109 align:start position:0%
work with
here<00:00:24.000><c> apps</c><00:00:24.320><c> two</c><00:00:24.599><c> one</c><00:00:24.760><c> C</c><00:00:25.400><c> ma.</c><00:00:25.920><c> time</c><00:00:26.320><c> 1000</c><00:00:26.760><c> 60</c>

00:00:27.109 --> 00:00:27.119 align:start position:0%
here apps two one C ma. time 1000 60
 

00:00:27.119 --> 00:00:29.830 align:start position:0%
here apps two one C ma. time 1000 60
6024<00:00:27.880><c> return</c><00:00:28.160><c> days</c><00:00:28.279><c> and</c><00:00:28.720><c> accessible</c><00:00:29.000><c> VI</c><00:00:29.279><c> plus2</c>

00:00:29.830 --> 00:00:29.840 align:start position:0%
6024 return days and accessible VI plus2
 

00:00:29.840 --> 00:00:31.269 align:start position:0%
6024 return days and accessible VI plus2
this<00:00:30.080><c> audio</c><00:00:30.359><c> cue</c><00:00:30.560><c> that</c><00:00:30.679><c> we're</c><00:00:30.840><c> hearing</c><00:00:31.160><c> the</c>

00:00:31.269 --> 00:00:31.279 align:start position:0%
this audio cue that we're hearing the
 

00:00:31.279 --> 00:00:33.350 align:start position:0%
this audio cue that we're hearing the
little<00:00:31.480><c> Boop</c><00:00:31.960><c> means</c><00:00:32.200><c> that</c><00:00:32.399><c> GitHub</c><00:00:32.759><c> co-pilot</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
little Boop means that GitHub co-pilot
 

00:00:33.360 --> 00:00:35.630 align:start position:0%
little Boop means that GitHub co-pilot
has<00:00:33.480><c> a</c><00:00:33.719><c> suggestion</c><00:00:34.200><c> ready</c><00:00:34.399><c> to</c><00:00:34.600><c> review</c><00:00:35.360><c> if</c><00:00:35.480><c> we</c>

00:00:35.630 --> 00:00:35.640 align:start position:0%
has a suggestion ready to review if we
 

00:00:35.640 --> 00:00:37.750 align:start position:0%
has a suggestion ready to review if we
don't<00:00:35.879><c> want</c><00:00:36.040><c> to</c><00:00:36.200><c> see</c><00:00:36.440><c> it</c><00:00:36.600><c> in</c><00:00:36.840><c> detail</c><00:00:37.520><c> we</c><00:00:37.640><c> can</c>

00:00:37.750 --> 00:00:37.760 align:start position:0%
don't want to see it in detail we can
 

00:00:37.760 --> 00:00:39.910 align:start position:0%
don't want to see it in detail we can
use<00:00:38.040><c> the</c><00:00:38.200><c> T</c><00:00:38.480><c> key</c><00:00:38.840><c> to</c><00:00:39.079><c> Simply</c><00:00:39.480><c> accept</c><00:00:39.760><c> the</c>

00:00:39.910 --> 00:00:39.920 align:start position:0%
use the T key to Simply accept the
 

00:00:39.920 --> 00:00:42.190 align:start position:0%
use the T key to Simply accept the
suggestion<00:00:40.760><c> if</c><00:00:40.879><c> we</c><00:00:41.039><c> do</c><00:00:41.200><c> want</c><00:00:41.399><c> more</c><00:00:41.640><c> detail</c><00:00:42.120><c> we</c>

00:00:42.190 --> 00:00:42.200 align:start position:0%
suggestion if we do want more detail we
 

00:00:42.200 --> 00:00:44.709 align:start position:0%
suggestion if we do want more detail we
can<00:00:42.360><c> use</c><00:00:42.640><c> alt</c><00:00:42.960><c> F2</c><00:00:43.680><c> the</c><00:00:43.879><c> accessibility</c><00:00:44.440><c> view</c>

00:00:44.709 --> 00:00:44.719 align:start position:0%
can use alt F2 the accessibility view
 

00:00:44.719 --> 00:00:47.549 align:start position:0%
can use alt F2 the accessibility view
hotkey<00:00:45.480><c> to</c><00:00:45.640><c> look</c><00:00:45.840><c> at</c><00:00:46.000><c> it</c><00:00:46.239><c> in</c><00:00:46.399><c> more</c><00:00:46.640><c> detail</c>

00:00:47.549 --> 00:00:47.559 align:start position:0%
hotkey to look at it in more detail
 

00:00:47.559 --> 00:00:49.029 align:start position:0%
hotkey to look at it in more detail
accessible<00:00:47.879><c> VI</c><00:00:48.039><c> explore</c><00:00:48.239><c> action</c><00:00:48.399><c> such</c><00:00:48.520><c> as</c><00:00:48.600><c> dis</c>

00:00:49.029 --> 00:00:49.039 align:start position:0%
accessible VI explore action such as dis
 

00:00:49.039 --> 00:00:50.310 align:start position:0%
accessible VI explore action such as dis
shift<00:00:49.239><c> plus</c><00:00:49.399><c> tab</c><00:00:49.600><c> Ed</c><00:00:49.800><c> muline</c><00:00:50.120><c> function</c>

00:00:50.310 --> 00:00:50.320 align:start position:0%
shift plus tab Ed muline function
 

00:00:50.320 --> 00:00:51.750 align:start position:0%
shift plus tab Ed muline function
calculate<00:00:50.600><c> days</c><00:00:50.760><c> between</c><00:00:50.879><c> two</c><00:00:51.000><c> dat</c><00:00:51.280><c> one</c><00:00:51.559><c> two</c>

00:00:51.750 --> 00:00:51.760 align:start position:0%
calculate days between two dat one two
 

00:00:51.760 --> 00:00:55.150 align:start position:0%
calculate days between two dat one two
con<00:00:52.120><c> time</c><00:00:52.640><c> apps</c><00:00:52.960><c> two</c><00:00:53.239><c> one</c><00:00:53.600><c> con</c><00:00:54.239><c> maale</c><00:00:54.800><c> time/</c>

00:00:55.150 --> 00:00:55.160 align:start position:0%
con time apps two one con maale time/
 

00:00:55.160 --> 00:00:58.990 align:start position:0%
con time apps two one con maale time/
1000<00:00:55.640><c> 60</c><00:00:55.879><c> St</c><00:00:56.320><c> 604</c><00:00:57.320><c> return</c><00:00:57.680><c> days</c><00:00:58.680><c> exit</c><00:00:58.879><c> this</c>

00:00:58.990 --> 00:00:59.000 align:start position:0%
1000 60 St 604 return days exit this
 

00:00:59.000 --> 00:01:00.509 align:start position:0%
1000 60 St 604 return days exit this
dialog<00:00:59.320><c> escaped</c><00:00:59.719><c> as</c><00:01:00.000><c> you</c><00:01:00.039><c> can</c><00:01:00.160><c> see</c><00:01:00.280><c> we</c><00:01:00.359><c> can</c>

00:01:00.509 --> 00:01:00.519 align:start position:0%
dialog escaped as you can see we can
 

00:01:00.519 --> 00:01:02.310 align:start position:0%
dialog escaped as you can see we can
look<00:01:00.640><c> at</c><00:01:00.840><c> the</c><00:01:01.039><c> the</c><00:01:01.199><c> suggestion</c><00:01:01.640><c> in</c><00:01:01.800><c> detail</c><00:01:02.239><c> and</c>

00:01:02.310 --> 00:01:02.320 align:start position:0%
look at the the suggestion in detail and
 

00:01:02.320 --> 00:01:03.990 align:start position:0%
look at the the suggestion in detail and
by<00:01:02.440><c> pressing</c><00:01:02.760><c> shift</c><00:01:03.120><c> tab</c><00:01:03.440><c> we'll</c><00:01:03.640><c> go</c><00:01:03.719><c> to</c><00:01:03.840><c> the</c>

00:01:03.990 --> 00:01:04.000 align:start position:0%
by pressing shift tab we'll go to the
 

00:01:04.000 --> 00:01:06.149 align:start position:0%
by pressing shift tab we'll go to the
toolbar<00:01:04.519><c> which</c><00:01:04.640><c> lets</c><00:01:04.839><c> us</c><00:01:05.119><c> accept</c><00:01:05.519><c> this</c><00:01:05.720><c> code</c>

00:01:06.149 --> 00:01:06.159 align:start position:0%
toolbar which lets us accept this code
 

00:01:06.159 --> 00:01:08.149 align:start position:0%
toolbar which lets us accept this code
access<00:01:06.560><c> tool</c><00:01:06.840><c> accept</c><00:01:07.200><c> comption</c><00:01:07.799><c> plus</c><00:01:07.960><c> SL</c>

00:01:08.149 --> 00:01:08.159 align:start position:0%
access tool accept comption plus SL
 

00:01:08.159 --> 00:01:09.510 align:start position:0%
access tool accept comption plus SL
button<00:01:08.479><c> this</c><00:01:08.640><c> also</c><00:01:08.840><c> tells</c><00:01:09.040><c> us</c><00:01:09.159><c> that</c><00:01:09.280><c> we</c><00:01:09.360><c> can</c>

00:01:09.510 --> 00:01:09.520 align:start position:0%
button this also tells us that we can
 

00:01:09.520 --> 00:01:11.710 align:start position:0%
button this also tells us that we can
use<00:01:09.720><c> the</c><00:01:09.840><c> hotkey</c><00:01:10.320><c> control</c><00:01:10.759><c> plus</c><00:01:11.040><c> slash</c><00:01:11.479><c> to</c>

00:01:11.710 --> 00:01:11.720 align:start position:0%
use the hotkey control plus slash to
 

00:01:11.720 --> 00:01:14.870 align:start position:0%
use the hotkey control plus slash to
accept<00:01:12.119><c> this</c><00:01:12.320><c> code</c><00:01:12.600><c> suggestion</c><00:01:13.400><c> Landmark</c><00:01:13.880><c> 123</c>

00:01:14.870 --> 00:01:14.880 align:start position:0%
accept this code suggestion Landmark 123
 

00:01:14.880 --> 00:01:16.830 align:start position:0%
accept this code suggestion Landmark 123
aut<00:01:15.040><c> complete</c><00:01:15.240><c> muline</c><00:01:16.000><c> there</c><00:01:16.119><c> is</c><00:01:16.280><c> no</c><00:01:16.439><c> feedback</c>

00:01:16.830 --> 00:01:16.840 align:start position:0%
aut complete muline there is no feedback
 

00:01:16.840 --> 00:01:18.670 align:start position:0%
aut complete muline there is no feedback
for<00:01:17.080><c> this</c><00:01:17.280><c> but</c><00:01:17.400><c> the</c><00:01:17.520><c> code</c><00:01:17.840><c> suggestion</c><00:01:18.360><c> has</c><00:01:18.520><c> now</c>

00:01:18.670 --> 00:01:18.680 align:start position:0%
for this but the code suggestion has now
 

00:01:18.680 --> 00:01:21.030 align:start position:0%
for this but the code suggestion has now
been<00:01:18.920><c> inserted</c><00:01:19.799><c> if</c><00:01:19.920><c> we're</c><00:01:20.159><c> not</c><00:01:20.439><c> happy</c><00:01:20.840><c> with</c>

00:01:21.030 --> 00:01:21.040 align:start position:0%
been inserted if we're not happy with
 

00:01:21.040 --> 00:01:23.109 align:start position:0%
been inserted if we're not happy with
this<00:01:21.240><c> suggestion</c><00:01:22.079><c> we</c><00:01:22.200><c> can</c><00:01:22.320><c> use</c><00:01:22.520><c> the</c><00:01:22.680><c> hotkey</c>

00:01:23.109 --> 00:01:23.119 align:start position:0%
this suggestion we can use the hotkey
 

00:01:23.119 --> 00:01:25.429 align:start position:0%
this suggestion we can use the hotkey
control<00:01:23.600><c> enter</c><00:01:24.400><c> to</c><00:01:24.600><c> generate</c><00:01:25.040><c> several</c>

00:01:25.429 --> 00:01:25.439 align:start position:0%
control enter to generate several
 

00:01:25.439 --> 00:01:27.270 align:start position:0%
control enter to generate several
suggestions<00:01:26.040><c> for</c><00:01:26.240><c> the</c><00:01:26.360><c> given</c><00:01:26.640><c> code</c><00:01:27.119><c> the</c>

00:01:27.270 --> 00:01:27.280 align:start position:0%
suggestions for the given code the
 

00:01:27.280 --> 00:01:29.469 align:start position:0%
suggestions for the given code the
suggestions<00:01:27.720><c> are</c><00:01:27.920><c> navigable</c><00:01:28.439><c> by</c><00:01:28.600><c> heading</c><00:01:29.320><c> and</c>

00:01:29.469 --> 00:01:29.479 align:start position:0%
suggestions are navigable by heading and
 

00:01:29.479 --> 00:01:31.350 align:start position:0%
suggestions are navigable by heading and
each<00:01:29.640><c> suest</c><00:01:29.880><c> suggestion</c><00:01:30.439><c> has</c><00:01:30.560><c> a</c><00:01:30.799><c> button</c><00:01:31.119><c> to</c>

00:01:31.350 --> 00:01:31.360 align:start position:0%
each suest suggestion has a button to
 

00:01:31.360 --> 00:01:33.429 align:start position:0%
each suest suggestion has a button to
paste<00:01:31.600><c> it</c><00:01:31.920><c> right</c><00:01:32.079><c> into</c><00:01:32.320><c> the</c><00:01:32.439><c> editor</c><00:01:33.040><c> up</c><00:01:33.119><c> coil</c>

00:01:33.429 --> 00:01:33.439 align:start position:0%
paste it right into the editor up coil
 

00:01:33.439 --> 00:01:35.310 align:start position:0%
paste it right into the editor up coil
suggestion<00:01:33.680><c> for</c><00:01:33.920><c> 123s</c><00:01:34.799><c> Visual</c><00:01:35.000><c> Studio</c><00:01:35.200><c> code</c>

00:01:35.310 --> 00:01:35.320 align:start position:0%
suggestion for 123s Visual Studio code
 

00:01:35.320 --> 00:01:36.270 align:start position:0%
suggestion for 123s Visual Studio code
document<00:01:35.600><c> loading</c><00:01:35.759><c> suggestion</c><00:01:36.079><c> loading</c>

00:01:36.270 --> 00:01:36.280 align:start position:0%
document loading suggestion loading
 

00:01:36.280 --> 00:01:37.230 align:start position:0%
document loading suggestion loading
suggestion<00:01:36.600><c> loading</c><00:01:36.759><c> suggestion</c><00:01:37.079><c> loading</c>

00:01:37.230 --> 00:01:37.240 align:start position:0%
suggestion loading suggestion loading
 

00:01:37.240 --> 00:01:38.830 align:start position:0%
suggestion loading suggestion loading
suggestion<00:01:37.720><c> suggestions</c><00:01:38.280><c> Cil</c><00:01:38.600><c> suggestions</c>

00:01:38.830 --> 00:01:38.840 align:start position:0%
suggestion suggestions Cil suggestions
 

00:01:38.840 --> 00:01:40.950 align:start position:0%
suggestion suggestions Cil suggestions
for<00:01:38.960><c> test</c><00:01:39.079><c> 123s</c><00:01:39.960><c> visual</c><00:01:40.159><c> stud</c><00:01:40.360><c> code</c><00:01:40.520><c> document</c>

00:01:40.950 --> 00:01:40.960 align:start position:0%
for test 123s visual stud code document
 

00:01:40.960 --> 00:01:42.870 align:start position:0%
for test 123s visual stud code document
to<00:01:41.119><c> navigate</c><00:01:41.640><c> these</c><00:01:41.960><c> suggestions</c><00:01:42.479><c> we'll</c><00:01:42.680><c> drop</c>

00:01:42.870 --> 00:01:42.880 align:start position:0%
to navigate these suggestions we'll drop
 

00:01:42.880 --> 00:01:45.350 align:start position:0%
to navigate these suggestions we'll drop
into<00:01:43.119><c> browse</c><00:01:43.479><c> mode</c><00:01:43.759><c> so</c><00:01:43.960><c> we</c><00:01:44.079><c> can</c><00:01:44.320><c> navigate</c><00:01:44.719><c> by</c>

00:01:45.350 --> 00:01:45.360 align:start position:0%
into browse mode so we can navigate by
 

00:01:45.360 --> 00:01:48.590 align:start position:0%
into browse mode so we can navigate by
heading<00:01:46.640><c> application</c><00:01:47.640><c> clickable</c><00:01:47.960><c> frame</c><00:01:48.320><c> coil</c>

00:01:48.590 --> 00:01:48.600 align:start position:0%
heading application clickable frame coil
 

00:01:48.600 --> 00:01:50.069 align:start position:0%
heading application clickable frame coil
suggestions<00:01:48.880><c> for</c><00:01:48.960><c> test</c><00:01:49.119><c> 123.</c><00:01:49.759><c> JS</c><00:01:49.920><c> frame</c>

00:01:50.069 --> 00:01:50.079 align:start position:0%
suggestions for test 123. JS frame
 

00:01:50.079 --> 00:01:51.310 align:start position:0%
suggestions for test 123. JS frame
suggestion<00:01:50.360><c> one</c><00:01:50.600><c> level</c><00:01:50.759><c> three</c><00:01:51.079><c> function</c>

00:01:51.310 --> 00:01:51.320 align:start position:0%
suggestion one level three function
 

00:01:51.320 --> 00:01:52.389 align:start position:0%
suggestion one level three function
calculate<00:01:51.640><c> days</c><00:01:51.759><c> between</c><00:01:51.920><c> two</c><00:01:52.040><c> dat</c><00:01:52.240><c> start</c>

00:01:52.389 --> 00:01:52.399 align:start position:0%
calculate days between two dat start
 

00:01:52.399 --> 00:01:54.630 align:start position:0%
calculate days between two dat start
date<00:01:52.680><c> date</c><00:01:52.920><c> C</c><00:01:53.119><c> Oneal</c>

00:01:54.630 --> 00:01:54.640 align:start position:0%
date date C Oneal
 

00:01:54.640 --> 00:01:57.510 align:start position:0%
date date C Oneal
240<00:01:55.640><c> start</c><00:01:55.799><c> dat</c><00:01:56.039><c> dat</c><00:01:56.360><c> one</c><00:01:56.520><c> day</c><00:01:57.200><c> clickable</c>

00:01:57.510 --> 00:01:57.520 align:start position:0%
240 start dat dat one day clickable
 

00:01:57.520 --> 00:01:59.190 align:start position:0%
240 start dat dat one day clickable
button<00:01:57.960><c> button</c><00:01:58.159><c> clickable</c><00:01:58.600><c> suggestion</c><00:01:58.880><c> one</c>

00:01:59.190 --> 00:01:59.200 align:start position:0%
button button clickable suggestion one
 

00:01:59.200 --> 00:02:01.469 align:start position:0%
button button clickable suggestion one
if<00:01:59.280><c> I</c><00:01:59.439><c> like</c><00:01:59.600><c> the</c><00:01:59.920><c> suggestion</c><00:02:00.719><c> I</c><00:02:00.840><c> can</c><00:02:01.039><c> select</c>

00:02:01.469 --> 00:02:01.479 align:start position:0%
if I like the suggestion I can select
 

00:02:01.479 --> 00:02:03.429 align:start position:0%
if I like the suggestion I can select
the<00:02:01.680><c> accept</c><00:02:02.039><c> suggestion</c><00:02:02.640><c> button</c><00:02:03.320><c> but</c>

00:02:03.429 --> 00:02:03.439 align:start position:0%
the accept suggestion button but
 

00:02:03.439 --> 00:02:05.270 align:start position:0%
the accept suggestion button but
application<00:02:03.840><c> made</c><00:02:04.399><c> it</c><00:02:04.520><c> has</c><00:02:04.640><c> now</c><00:02:04.799><c> been</c><00:02:04.960><c> placed</c>

00:02:05.270 --> 00:02:05.280 align:start position:0%
application made it has now been placed
 

00:02:05.280 --> 00:02:07.310 align:start position:0%
application made it has now been placed
into<00:02:05.520><c> our</c><00:02:05.680><c> editor</c><00:02:06.439><c> this</c><00:02:06.560><c> is</c><00:02:06.680><c> the</c><00:02:06.799><c> end</c><00:02:07.000><c> of</c><00:02:07.119><c> the</c>

00:02:07.310 --> 00:02:07.320 align:start position:0%
into our editor this is the end of the
 

00:02:07.320 --> 00:02:09.350 align:start position:0%
into our editor this is the end of the
video<00:02:07.759><c> about</c><00:02:08.080><c> inline</c><00:02:08.479><c> suggestions</c><00:02:09.160><c> with</c>

00:02:09.350 --> 00:02:09.360 align:start position:0%
video about inline suggestions with
 

00:02:09.360 --> 00:02:11.309 align:start position:0%
video about inline suggestions with
GitHub<00:02:09.879><c> co-pilot</c><00:02:10.679><c> I</c><00:02:10.759><c> would</c><00:02:10.920><c> like</c><00:02:11.039><c> to</c><00:02:11.160><c> thank</c>

00:02:11.309 --> 00:02:11.319 align:start position:0%
GitHub co-pilot I would like to thank
 

00:02:11.319 --> 00:02:13.070 align:start position:0%
GitHub co-pilot I would like to thank
you<00:02:11.440><c> for</c><00:02:11.640><c> watching</c><00:02:11.920><c> this</c><00:02:12.120><c> video</c><00:02:12.599><c> and</c><00:02:12.720><c> to</c><00:02:12.840><c> be</c><00:02:12.959><c> on</c>

00:02:13.070 --> 00:02:13.080 align:start position:0%
you for watching this video and to be on
 

00:02:13.080 --> 00:02:17.519 align:start position:0%
you for watching this video and to be on
the<00:02:13.200><c> lookout</c><00:02:13.560><c> for</c><00:02:13.760><c> more</c><00:02:14.000><c> videos</c><00:02:14.319><c> to</c><00:02:14.519><c> come</c>

