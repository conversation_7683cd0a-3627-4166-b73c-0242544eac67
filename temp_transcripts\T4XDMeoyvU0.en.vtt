WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:03.190 align:start position:0%
 
Okay,<00:00:00.480><c> so</c><00:00:00.800><c> in</c><00:00:01.040><c> this</c><00:00:01.360><c> video</c><00:00:01.920><c> I</c><00:00:02.240><c> want</c><00:00:02.399><c> to</c><00:00:02.560><c> cover</c>

00:00:03.190 --> 00:00:03.200 align:start position:0%
Okay, so in this video I want to cover
 

00:00:03.200 --> 00:00:05.349 align:start position:0%
Okay, so in this video I want to cover
small<00:00:03.760><c> LM3.</c>

00:00:05.349 --> 00:00:05.359 align:start position:0%
small LM3.
 

00:00:05.359 --> 00:00:07.269 align:start position:0%
small LM3.
This<00:00:05.520><c> is</c><00:00:05.600><c> a</c><00:00:05.839><c> new</c><00:00:06.000><c> model</c><00:00:06.240><c> that</c><00:00:06.480><c> came</c><00:00:06.720><c> out</c><00:00:06.879><c> today</c>

00:00:07.269 --> 00:00:07.279 align:start position:0%
This is a new model that came out today
 

00:00:07.279 --> 00:00:09.750 align:start position:0%
This is a new model that came out today
from<00:00:07.680><c> Hugging</c><00:00:08.160><c> Face,</c><00:00:08.720><c> actually</c><00:00:09.120><c> trained</c><00:00:09.519><c> by</c>

00:00:09.750 --> 00:00:09.760 align:start position:0%
from Hugging Face, actually trained by
 

00:00:09.760 --> 00:00:12.150 align:start position:0%
from Hugging Face, actually trained by
Hugging<00:00:10.240><c> Face,</c><00:00:10.559><c> created</c><00:00:10.960><c> by</c><00:00:11.120><c> Hugging</c><00:00:11.599><c> Face.</c>

00:00:12.150 --> 00:00:12.160 align:start position:0%
Hugging Face, created by Hugging Face.
 

00:00:12.160 --> 00:00:14.150 align:start position:0%
Hugging Face, created by Hugging Face.
It<00:00:12.320><c> builds</c><00:00:12.559><c> on</c><00:00:12.719><c> the</c><00:00:12.960><c> previous</c><00:00:13.280><c> small</c><00:00:13.599><c> LMS</c><00:00:14.000><c> that</c>

00:00:14.150 --> 00:00:14.160 align:start position:0%
It builds on the previous small LMS that
 

00:00:14.160 --> 00:00:16.470 align:start position:0%
It builds on the previous small LMS that
they've<00:00:14.400><c> had</c><00:00:14.559><c> in</c><00:00:14.799><c> the</c><00:00:14.960><c> past,</c><00:00:15.679><c> slightly</c><00:00:16.160><c> bigger</c>

00:00:16.470 --> 00:00:16.480 align:start position:0%
they've had in the past, slightly bigger
 

00:00:16.480 --> 00:00:18.950 align:start position:0%
they've had in the past, slightly bigger
than<00:00:16.640><c> the</c><00:00:16.800><c> other</c><00:00:16.960><c> ones</c><00:00:17.440><c> at</c><00:00:17.680><c> 3B.</c><00:00:18.480><c> But</c><00:00:18.640><c> in</c><00:00:18.800><c> this</c>

00:00:18.950 --> 00:00:18.960 align:start position:0%
than the other ones at 3B. But in this
 

00:00:18.960 --> 00:00:20.950 align:start position:0%
than the other ones at 3B. But in this
video,<00:00:19.119><c> I</c><00:00:19.279><c> want</c><00:00:19.359><c> to</c><00:00:19.520><c> cover</c><00:00:19.760><c> not</c><00:00:20.080><c> just</c><00:00:20.400><c> the</c>

00:00:20.950 --> 00:00:20.960 align:start position:0%
video, I want to cover not just the
 

00:00:20.960 --> 00:00:22.950 align:start position:0%
video, I want to cover not just the
model<00:00:21.359><c> itself.</c><00:00:22.080><c> I</c><00:00:22.320><c> think</c><00:00:22.400><c> it's</c><00:00:22.640><c> really</c>

00:00:22.950 --> 00:00:22.960 align:start position:0%
model itself. I think it's really
 

00:00:22.960 --> 00:00:25.189 align:start position:0%
model itself. I think it's really
interesting<00:00:23.519><c> around</c><00:00:23.920><c> what</c><00:00:24.240><c> they've</c><00:00:24.640><c> released</c>

00:00:25.189 --> 00:00:25.199 align:start position:0%
interesting around what they've released
 

00:00:25.199 --> 00:00:27.670 align:start position:0%
interesting around what they've released
about<00:00:25.519><c> how</c><00:00:25.760><c> they</c><00:00:26.160><c> created</c><00:00:26.640><c> this</c><00:00:26.960><c> model</c><00:00:27.439><c> and</c>

00:00:27.670 --> 00:00:27.680 align:start position:0%
about how they created this model and
 

00:00:27.680 --> 00:00:29.109 align:start position:0%
about how they created this model and
the<00:00:27.920><c> different</c><00:00:28.160><c> kinds</c><00:00:28.480><c> of</c><00:00:28.720><c> training</c>

00:00:29.109 --> 00:00:29.119 align:start position:0%
the different kinds of training
 

00:00:29.119 --> 00:00:31.429 align:start position:0%
the different kinds of training
techniques<00:00:29.519><c> that</c><00:00:29.760><c> they've</c><00:00:30.000><c> used</c><00:00:30.720><c> and</c><00:00:31.039><c> also</c><00:00:31.279><c> at</c>

00:00:31.429 --> 00:00:31.439 align:start position:0%
techniques that they've used and also at
 

00:00:31.439 --> 00:00:33.510 align:start position:0%
techniques that they've used and also at
the<00:00:31.519><c> end</c><00:00:31.679><c> I</c><00:00:31.840><c> want</c><00:00:31.920><c> to</c><00:00:32.079><c> basically</c><00:00:32.480><c> test</c><00:00:32.719><c> it</c><00:00:33.280><c> not</c>

00:00:33.510 --> 00:00:33.520 align:start position:0%
the end I want to basically test it not
 

00:00:33.520 --> 00:00:35.910 align:start position:0%
the end I want to basically test it not
just<00:00:33.680><c> with</c><00:00:34.000><c> reasoning</c><00:00:34.719><c> and</c><00:00:35.040><c> non-reasoning</c>

00:00:35.910 --> 00:00:35.920 align:start position:0%
just with reasoning and non-reasoning
 

00:00:35.920 --> 00:00:38.389 align:start position:0%
just with reasoning and non-reasoning
but<00:00:36.239><c> also</c><00:00:36.480><c> with</c><00:00:36.719><c> agentic</c><00:00:37.360><c> uses.</c><00:00:37.840><c> So</c><00:00:38.000><c> let's</c><00:00:38.239><c> see</c>

00:00:38.389 --> 00:00:38.399 align:start position:0%
but also with agentic uses. So let's see
 

00:00:38.399 --> 00:00:40.150 align:start position:0%
but also with agentic uses. So let's see
how<00:00:38.640><c> it</c><00:00:38.800><c> does</c><00:00:39.040><c> at</c><00:00:39.280><c> things</c><00:00:39.440><c> like</c><00:00:39.680><c> function</c>

00:00:40.150 --> 00:00:40.160 align:start position:0%
how it does at things like function
 

00:00:40.160 --> 00:00:41.830 align:start position:0%
how it does at things like function
calling<00:00:40.480><c> and</c><00:00:40.719><c> stuff</c><00:00:40.879><c> like</c><00:00:41.040><c> that.</c><00:00:41.440><c> Could</c><00:00:41.600><c> this</c>

00:00:41.830 --> 00:00:41.840 align:start position:0%
calling and stuff like that. Could this
 

00:00:41.840 --> 00:00:43.830 align:start position:0%
calling and stuff like that. Could this
be<00:00:41.920><c> the</c><00:00:42.160><c> model</c><00:00:42.320><c> that</c><00:00:42.480><c> you</c><00:00:42.640><c> run</c><00:00:42.879><c> locally</c><00:00:43.440><c> to</c><00:00:43.680><c> run</c>

00:00:43.830 --> 00:00:43.840 align:start position:0%
be the model that you run locally to run
 

00:00:43.840 --> 00:00:45.750 align:start position:0%
be the model that you run locally to run
all<00:00:44.079><c> your</c><00:00:44.239><c> agents</c><00:00:44.800><c> without</c><00:00:45.200><c> having</c><00:00:45.440><c> to</c><00:00:45.600><c> use</c>

00:00:45.750 --> 00:00:45.760 align:start position:0%
all your agents without having to use
 

00:00:45.760 --> 00:00:48.470 align:start position:0%
all your agents without having to use
any<00:00:46.079><c> sort</c><00:00:46.239><c> of</c><00:00:46.480><c> proprietary</c><00:00:47.120><c> model</c><00:00:47.680><c> etc.</c><00:00:48.480><c> All</c>

00:00:48.470 --> 00:00:48.480 align:start position:0%
any sort of proprietary model etc. All
 

00:00:48.480 --> 00:00:50.950 align:start position:0%
any sort of proprietary model etc. All
right<00:00:48.719><c> let's</c><00:00:49.039><c> jump</c><00:00:49.200><c> in.</c><00:00:49.840><c> So,</c><00:00:50.079><c> HuggingFace</c><00:00:50.800><c> has</c>

00:00:50.950 --> 00:00:50.960 align:start position:0%
right let's jump in. So, HuggingFace has
 

00:00:50.960 --> 00:00:53.990 align:start position:0%
right let's jump in. So, HuggingFace has
released<00:00:51.440><c> this</c><00:00:52.000><c> new</c><00:00:52.559><c> 3B</c><00:00:53.199><c> model</c><00:00:53.520><c> and</c><00:00:53.760><c> they've</c>

00:00:53.990 --> 00:00:54.000 align:start position:0%
released this new 3B model and they've
 

00:00:54.000 --> 00:00:56.150 align:start position:0%
released this new 3B model and they've
released<00:00:54.320><c> both</c><00:00:54.559><c> the</c><00:00:55.039><c> base</c><00:00:55.440><c> model,</c><00:00:55.920><c> an</c>

00:00:56.150 --> 00:00:56.160 align:start position:0%
released both the base model, an
 

00:00:56.160 --> 00:00:58.869 align:start position:0%
released both the base model, an
instruct<00:00:56.719><c> model,</c><00:00:57.280><c> and</c><00:00:57.440><c> an</c><00:00:57.680><c> onyx</c><00:00:58.239><c> version</c><00:00:58.640><c> of</c>

00:00:58.869 --> 00:00:58.879 align:start position:0%
instruct model, and an onyx version of
 

00:00:58.879 --> 00:01:01.430 align:start position:0%
instruct model, and an onyx version of
the<00:00:59.039><c> model.</c><00:00:59.520><c> And</c><00:00:59.840><c> already</c><00:01:00.239><c> people</c><00:01:00.640><c> are</c><00:01:00.879><c> making</c>

00:01:01.430 --> 00:01:01.440 align:start position:0%
the model. And already people are making
 

00:01:01.440 --> 00:01:04.549 align:start position:0%
the model. And already people are making
GGUF<00:01:02.320><c> versions</c><00:01:02.800><c> which</c><00:01:02.960><c> you</c><00:01:03.120><c> can</c><00:01:03.280><c> use</c><00:01:03.359><c> on</c><00:01:03.600><c> Alama</c>

00:01:04.549 --> 00:01:04.559 align:start position:0%
GGUF versions which you can use on Alama
 

00:01:04.559 --> 00:01:07.270 align:start position:0%
GGUF versions which you can use on Alama
and<00:01:04.799><c> LM</c><00:01:05.360><c> Studio.</c><00:01:06.320><c> The</c><00:01:06.479><c> model</c><00:01:06.720><c> itself</c><00:01:07.040><c> is</c>

00:01:07.270 --> 00:01:07.280 align:start position:0%
and LM Studio. The model itself is
 

00:01:07.280 --> 00:01:09.590 align:start position:0%
and LM Studio. The model itself is
certainly<00:01:07.680><c> impressive.</c><00:01:08.320><c> We</c><00:01:08.560><c> can</c><00:01:08.640><c> see</c><00:01:08.799><c> that</c><00:01:09.280><c> it</c>

00:01:09.590 --> 00:01:09.600 align:start position:0%
certainly impressive. We can see that it
 

00:01:09.600 --> 00:01:12.390 align:start position:0%
certainly impressive. We can see that it
lies<00:01:09.920><c> in</c><00:01:10.159><c> this</c><00:01:10.400><c> area</c><00:01:10.880><c> between</c><00:01:11.200><c> the</c><00:01:11.360><c> Quen</c><00:01:12.080><c> 3</c>

00:01:12.390 --> 00:01:12.400 align:start position:0%
lies in this area between the Quen 3
 

00:01:12.400 --> 00:01:16.550 align:start position:0%
lies in this area between the Quen 3
1.7B<00:01:13.439><c> and</c><00:01:13.600><c> the</c><00:01:13.760><c> Quen</c><00:01:14.080><c> 3</c><00:01:14.400><c> 4B.</c><00:01:15.439><c> And</c><00:01:15.760><c> 3B</c><00:01:16.159><c> is</c><00:01:16.400><c> an</c>

00:01:16.550 --> 00:01:16.560 align:start position:0%
1.7B and the Quen 3 4B. And 3B is an
 

00:01:16.560 --> 00:01:18.550 align:start position:0%
1.7B and the Quen 3 4B. And 3B is an
interesting<00:01:16.960><c> size</c><00:01:17.360><c> in</c><00:01:17.680><c> that</c><00:01:17.840><c> it's</c><00:01:18.159><c> like</c><00:01:18.320><c> a</c>

00:01:18.550 --> 00:01:18.560 align:start position:0%
interesting size in that it's like a
 

00:01:18.560 --> 00:01:21.030 align:start position:0%
interesting size in that it's like a
nice<00:01:18.799><c> size</c><00:01:19.040><c> that</c><00:01:19.280><c> you</c><00:01:19.439><c> could</c><00:01:19.600><c> actually</c><00:01:19.920><c> run</c><00:01:20.320><c> on</c>

00:01:21.030 --> 00:01:21.040 align:start position:0%
nice size that you could actually run on
 

00:01:21.040 --> 00:01:23.350 align:start position:0%
nice size that you could actually run on
many<00:01:21.439><c> mobile</c><00:01:21.920><c> devices</c><00:01:22.479><c> that</c><00:01:22.720><c> are</c><00:01:22.960><c> out</c><00:01:23.119><c> there</c>

00:01:23.350 --> 00:01:23.360 align:start position:0%
many mobile devices that are out there
 

00:01:23.360 --> 00:01:25.670 align:start position:0%
many mobile devices that are out there
now.<00:01:24.080><c> And</c><00:01:24.240><c> sure</c><00:01:24.320><c> enough,</c><00:01:24.640><c> for</c><00:01:24.880><c> that</c><00:01:25.119><c> size,</c><00:01:25.439><c> it</c>

00:01:25.670 --> 00:01:25.680 align:start position:0%
now. And sure enough, for that size, it
 

00:01:25.680 --> 00:01:27.670 align:start position:0%
now. And sure enough, for that size, it
seems<00:01:25.840><c> to</c><00:01:26.000><c> be</c><00:01:26.159><c> beating</c><00:01:26.479><c> out</c><00:01:26.720><c> the</c><00:01:26.880><c> old</c><00:01:27.119><c> Quen</c>

00:01:27.670 --> 00:01:27.680 align:start position:0%
seems to be beating out the old Quen
 

00:01:27.680 --> 00:01:31.749 align:start position:0%
seems to be beating out the old Quen
2.53B<00:01:28.960><c> and</c><00:01:29.280><c> the</c><00:01:29.600><c> Llama</c><00:01:30.080><c> 3.2B.</c>

00:01:31.749 --> 00:01:31.759 align:start position:0%
2.53B and the Llama 3.2B.
 

00:01:31.759 --> 00:01:33.910 align:start position:0%
2.53B and the Llama 3.2B.
So,<00:01:31.920><c> if</c><00:01:32.079><c> we</c><00:01:32.240><c> look</c><00:01:32.320><c> at</c><00:01:32.479><c> some</c><00:01:32.720><c> of</c><00:01:32.880><c> the</c><00:01:33.119><c> core</c><00:01:33.439><c> stats</c>

00:01:33.910 --> 00:01:33.920 align:start position:0%
So, if we look at some of the core stats
 

00:01:33.920 --> 00:01:36.469 align:start position:0%
So, if we look at some of the core stats
for<00:01:34.240><c> it,</c><00:01:34.880><c> we</c><00:01:35.119><c> can</c><00:01:35.280><c> see</c><00:01:35.520><c> that</c><00:01:35.759><c> it's</c><00:01:36.079><c> a</c><00:01:36.240><c> model</c>

00:01:36.469 --> 00:01:36.479 align:start position:0%
for it, we can see that it's a model
 

00:01:36.479 --> 00:01:38.469 align:start position:0%
for it, we can see that it's a model
that's<00:01:36.799><c> been</c><00:01:36.960><c> trained</c><00:01:37.280><c> on</c><00:01:37.600><c> 11</c><00:01:38.000><c> trillion</c>

00:01:38.469 --> 00:01:38.479 align:start position:0%
that's been trained on 11 trillion
 

00:01:38.479 --> 00:01:41.830 align:start position:0%
that's been trained on 11 trillion
tokens,<00:01:39.119><c> which</c><00:01:39.759><c> is</c><00:01:40.240><c> a</c><00:01:40.560><c> very</c><00:01:40.880><c> impressive</c><00:01:41.439><c> feat,</c>

00:01:41.830 --> 00:01:41.840 align:start position:0%
tokens, which is a very impressive feat,
 

00:01:41.840 --> 00:01:43.590 align:start position:0%
tokens, which is a very impressive feat,
right?<00:01:42.079><c> To</c><00:01:42.400><c> actually</c><00:01:42.720><c> train</c><00:01:43.040><c> something</c><00:01:43.360><c> for</c>

00:01:43.590 --> 00:01:43.600 align:start position:0%
right? To actually train something for
 

00:01:43.600 --> 00:01:45.670 align:start position:0%
right? To actually train something for
that<00:01:43.920><c> long.</c><00:01:44.560><c> We</c><00:01:44.799><c> can</c><00:01:44.960><c> see</c><00:01:45.040><c> that</c><00:01:45.200><c> they're</c>

00:01:45.670 --> 00:01:45.680 align:start position:0%
that long. We can see that they're
 

00:01:45.680 --> 00:01:47.590 align:start position:0%
that long. We can see that they're
claiming<00:01:46.079><c> that</c><00:01:46.240><c> it's</c><00:01:46.399><c> state-of-the-art</c><00:01:47.119><c> for</c>

00:01:47.590 --> 00:01:47.600 align:start position:0%
claiming that it's state-of-the-art for
 

00:01:47.600 --> 00:01:50.149 align:start position:0%
claiming that it's state-of-the-art for
all<00:01:47.759><c> the</c><00:01:47.920><c> 3B</c><00:01:48.479><c> models</c><00:01:48.960><c> and</c><00:01:49.200><c> also</c><00:01:49.680><c> strongly</c>

00:01:50.149 --> 00:01:50.159 align:start position:0%
all the 3B models and also strongly
 

00:01:50.159 --> 00:01:52.950 align:start position:0%
all the 3B models and also strongly
competing<00:01:50.560><c> with</c><00:01:50.880><c> 4B</c><00:01:51.360><c> models.</c><00:01:52.399><c> And</c><00:01:52.560><c> it's</c><00:01:52.799><c> one</c>

00:01:52.950 --> 00:01:52.960 align:start position:0%
competing with 4B models. And it's one
 

00:01:52.960 --> 00:01:55.109 align:start position:0%
competing with 4B models. And it's one
of<00:01:53.040><c> these</c><00:01:53.280><c> new</c><00:01:53.520><c> models</c><00:01:53.920><c> that</c><00:01:54.320><c> allows</c><00:01:54.720><c> you</c><00:01:54.960><c> to</c>

00:01:55.109 --> 00:01:55.119 align:start position:0%
of these new models that allows you to
 

00:01:55.119 --> 00:01:57.830 align:start position:0%
of these new models that allows you to
basically<00:01:55.759><c> turn</c><00:01:56.159><c> on</c><00:01:56.640><c> and</c><00:01:57.040><c> turn</c><00:01:57.360><c> off</c>

00:01:57.830 --> 00:01:57.840 align:start position:0%
basically turn on and turn off
 

00:01:57.840 --> 00:01:59.429 align:start position:0%
basically turn on and turn off
reasoning.<00:01:58.479><c> So,</c><00:01:58.719><c> we've</c><00:01:58.880><c> looked</c><00:01:59.040><c> at</c><00:01:59.200><c> some</c><00:01:59.360><c> of</c>

00:01:59.429 --> 00:01:59.439 align:start position:0%
reasoning. So, we've looked at some of
 

00:01:59.439 --> 00:02:01.350 align:start position:0%
reasoning. So, we've looked at some of
these<00:01:59.680><c> before</c><00:02:00.000><c> where</c><00:02:00.240><c> you've</c><00:02:00.560><c> got</c><00:02:00.799><c> some</c><00:02:01.040><c> kind</c>

00:02:01.350 --> 00:02:01.360 align:start position:0%
these before where you've got some kind
 

00:02:01.360 --> 00:02:04.149 align:start position:0%
these before where you've got some kind
of<00:02:01.680><c> flag</c><00:02:02.079><c> that</c><00:02:02.399><c> you</c><00:02:02.560><c> can</c><00:02:02.719><c> turn</c><00:02:02.880><c> on</c><00:02:03.200><c> thinking</c><00:02:03.680><c> or</c>

00:02:04.149 --> 00:02:04.159 align:start position:0%
of flag that you can turn on thinking or
 

00:02:04.159 --> 00:02:06.230 align:start position:0%
of flag that you can turn on thinking or
non-thinking.<00:02:05.360><c> They</c><00:02:05.520><c> claiming</c><00:02:05.840><c> that</c><00:02:06.000><c> it's</c>

00:02:06.230 --> 00:02:06.240 align:start position:0%
non-thinking. They claiming that it's
 

00:02:06.240 --> 00:02:08.309 align:start position:0%
non-thinking. They claiming that it's
multilingual.<00:02:07.040><c> For</c><00:02:07.280><c> me,</c><00:02:07.600><c> there's</c><00:02:07.840><c> only</c><00:02:08.080><c> six</c>

00:02:08.309 --> 00:02:08.319 align:start position:0%
multilingual. For me, there's only six
 

00:02:08.319 --> 00:02:10.869 align:start position:0%
multilingual. For me, there's only six
languages<00:02:08.879><c> there.</c><00:02:09.280><c> They're</c><00:02:09.520><c> all</c><00:02:10.080><c> European</c>

00:02:10.869 --> 00:02:10.879 align:start position:0%
languages there. They're all European
 

00:02:10.879 --> 00:02:13.350 align:start position:0%
languages there. They're all European
languages.<00:02:12.080><c> For</c><00:02:12.319><c> me</c><00:02:12.480><c> personally,</c><00:02:12.879><c> that's</c><00:02:13.120><c> not</c>

00:02:13.350 --> 00:02:13.360 align:start position:0%
languages. For me personally, that's not
 

00:02:13.360 --> 00:02:14.949 align:start position:0%
languages. For me personally, that's not
very<00:02:13.599><c> multilingual.</c><00:02:14.480><c> But</c><00:02:14.640><c> also,</c>

00:02:14.949 --> 00:02:14.959 align:start position:0%
very multilingual. But also,
 

00:02:14.959 --> 00:02:17.350 align:start position:0%
very multilingual. But also,
interestingly,<00:02:15.599><c> this</c><00:02:15.840><c> can</c><00:02:16.319><c> actually</c><00:02:16.879><c> use</c><00:02:17.120><c> a</c>

00:02:17.350 --> 00:02:17.360 align:start position:0%
interestingly, this can actually use a
 

00:02:17.360 --> 00:02:21.830 align:start position:0%
interestingly, this can actually use a
long<00:02:17.599><c> context</c><00:02:18.239><c> of</c><00:02:18.640><c> up</c><00:02:18.800><c> to</c><00:02:19.440><c> 128K</c><00:02:20.239><c> in</c><00:02:20.560><c> here.</c><00:02:21.440><c> Now,</c>

00:02:21.830 --> 00:02:21.840 align:start position:0%
long context of up to 128K in here. Now,
 

00:02:21.840 --> 00:02:24.229 align:start position:0%
long context of up to 128K in here. Now,
the<00:02:22.080><c> thing</c><00:02:22.239><c> that</c><00:02:22.400><c> I</c><00:02:22.640><c> find</c><00:02:22.879><c> that</c><00:02:23.200><c> actually</c><00:02:23.920><c> is</c>

00:02:24.229 --> 00:02:24.239 align:start position:0%
the thing that I find that actually is
 

00:02:24.239 --> 00:02:26.150 align:start position:0%
the thing that I find that actually is
perhaps<00:02:24.640><c> more</c><00:02:24.959><c> interesting</c><00:02:25.360><c> than</c><00:02:25.599><c> the</c><00:02:25.840><c> model</c>

00:02:26.150 --> 00:02:26.160 align:start position:0%
perhaps more interesting than the model
 

00:02:26.160 --> 00:02:28.710 align:start position:0%
perhaps more interesting than the model
itself<00:02:27.040><c> is</c><00:02:27.280><c> that</c><00:02:27.440><c> they've</c><00:02:27.760><c> released</c><00:02:28.239><c> this</c>

00:02:28.710 --> 00:02:28.720 align:start position:0%
itself is that they've released this
 

00:02:28.720 --> 00:02:32.229 align:start position:0%
itself is that they've released this
blueprint.<00:02:29.840><c> And</c><00:02:30.080><c> this</c><00:02:30.400><c> blueprint</c><00:02:31.200><c> is</c><00:02:31.599><c> exactly</c>

00:02:32.229 --> 00:02:32.239 align:start position:0%
blueprint. And this blueprint is exactly
 

00:02:32.239 --> 00:02:34.869 align:start position:0%
blueprint. And this blueprint is exactly
how<00:02:32.560><c> they've</c><00:02:32.959><c> done</c><00:02:33.360><c> each</c><00:02:33.760><c> step</c><00:02:34.319><c> of</c><00:02:34.640><c> the</c>

00:02:34.869 --> 00:02:34.879 align:start position:0%
how they've done each step of the
 

00:02:34.879 --> 00:02:37.750 align:start position:0%
how they've done each step of the
training.<00:02:35.760><c> So</c><00:02:36.080><c> even</c><00:02:36.400><c> with</c><00:02:36.640><c> the</c><00:02:36.959><c> open</c><00:02:37.280><c> weights</c>

00:02:37.750 --> 00:02:37.760 align:start position:0%
training. So even with the open weights
 

00:02:37.760 --> 00:02:41.350 align:start position:0%
training. So even with the open weights
models<00:02:38.239><c> from</c><00:02:38.640><c> Deepseek</c><00:02:39.519><c> from</c><00:02:40.239><c> Quen,</c><00:02:40.959><c> usually</c>

00:02:41.350 --> 00:02:41.360 align:start position:0%
models from Deepseek from Quen, usually
 

00:02:41.360 --> 00:02:43.190 align:start position:0%
models from Deepseek from Quen, usually
they're<00:02:41.760><c> quite</c><00:02:42.080><c> good</c><00:02:42.319><c> in</c><00:02:42.640><c> telling</c><00:02:42.879><c> us</c><00:02:43.200><c> at</c>

00:02:43.190 --> 00:02:43.200 align:start position:0%
they're quite good in telling us at
 

00:02:43.200 --> 00:02:45.910 align:start position:0%
they're quite good in telling us at
least<00:02:43.599><c> roughly</c><00:02:44.080><c> what</c><00:02:44.400><c> they</c><00:02:44.720><c> did,</c><00:02:45.440><c> unlike</c><00:02:45.760><c> the</c>

00:02:45.910 --> 00:02:45.920 align:start position:0%
least roughly what they did, unlike the
 

00:02:45.920 --> 00:02:47.350 align:start position:0%
least roughly what they did, unlike the
papers<00:02:46.239><c> coming</c><00:02:46.480><c> out</c><00:02:46.640><c> of</c><00:02:46.720><c> the</c><00:02:46.879><c> proprietary</c>

00:02:47.350 --> 00:02:47.360 align:start position:0%
papers coming out of the proprietary
 

00:02:47.360 --> 00:02:49.990 align:start position:0%
papers coming out of the proprietary
labs<00:02:48.000><c> which</c><00:02:48.319><c> really</c><00:02:48.640><c> aren't</c><00:02:49.040><c> giving</c><00:02:49.280><c> you</c><00:02:49.680><c> any</c>

00:02:49.990 --> 00:02:50.000 align:start position:0%
labs which really aren't giving you any
 

00:02:50.000 --> 00:02:52.309 align:start position:0%
labs which really aren't giving you any
sort<00:02:50.239><c> of</c><00:02:50.400><c> real</c><00:02:50.800><c> facts</c><00:02:51.200><c> and</c><00:02:51.519><c> basically</c><00:02:52.000><c> just</c>

00:02:52.309 --> 00:02:52.319 align:start position:0%
sort of real facts and basically just
 

00:02:52.319 --> 00:02:54.710 align:start position:0%
sort of real facts and basically just
maybe<00:02:52.640><c> have</c><00:02:53.120><c> some</c><00:02:53.440><c> vague</c><00:02:53.840><c> clues</c><00:02:54.239><c> every</c><00:02:54.560><c> now</c>

00:02:54.710 --> 00:02:54.720 align:start position:0%
maybe have some vague clues every now
 

00:02:54.720 --> 00:02:57.190 align:start position:0%
maybe have some vague clues every now
and<00:02:54.959><c> then.</c><00:02:55.840><c> This</c><00:02:56.160><c> is</c><00:02:56.319><c> actually</c><00:02:56.640><c> a</c><00:02:56.959><c> full</c>

00:02:57.190 --> 00:02:57.200 align:start position:0%
and then. This is actually a full
 

00:02:57.200 --> 00:03:00.630 align:start position:0%
and then. This is actually a full
blueprint<00:02:57.840><c> of</c><00:02:58.560><c> exactly</c><00:02:59.120><c> what</c><00:02:59.440><c> they</c><00:02:59.680><c> did</c><00:03:00.319><c> for</c>

00:03:00.630 --> 00:03:00.640 align:start position:0%
blueprint of exactly what they did for
 

00:03:00.640 --> 00:03:02.710 align:start position:0%
blueprint of exactly what they did for
each<00:03:00.800><c> of</c><00:03:00.959><c> the</c><00:03:01.200><c> steps</c><00:03:01.680><c> from</c><00:03:01.920><c> the</c><00:03:02.080><c> pre-training</c>

00:03:02.710 --> 00:03:02.720 align:start position:0%
each of the steps from the pre-training
 

00:03:02.720 --> 00:03:04.869 align:start position:0%
each of the steps from the pre-training
recipe,<00:03:03.440><c> how</c><00:03:03.680><c> they</c><00:03:03.840><c> set</c><00:03:04.000><c> up</c><00:03:04.159><c> the</c><00:03:04.319><c> distributed</c>

00:03:04.869 --> 00:03:04.879 align:start position:0%
recipe, how they set up the distributed
 

00:03:04.879 --> 00:03:06.949 align:start position:0%
recipe, how they set up the distributed
training,<00:03:05.840><c> how</c><00:03:06.000><c> they</c><00:03:06.239><c> did</c><00:03:06.400><c> the</c><00:03:06.560><c> model</c>

00:03:06.949 --> 00:03:06.959 align:start position:0%
training, how they did the model
 

00:03:06.959 --> 00:03:09.270 align:start position:0%
training, how they did the model
architecture,<00:03:08.400><c> right</c><00:03:08.640><c> through</c><00:03:08.800><c> to</c><00:03:09.040><c> things</c>

00:03:09.270 --> 00:03:09.280 align:start position:0%
architecture, right through to things
 

00:03:09.280 --> 00:03:11.589 align:start position:0%
architecture, right through to things
like<00:03:09.519><c> the</c><00:03:09.760><c> long</c><00:03:10.080><c> context</c><00:03:10.560><c> training</c><00:03:11.120><c> and</c><00:03:11.440><c> all</c>

00:03:11.589 --> 00:03:11.599 align:start position:0%
like the long context training and all
 

00:03:11.599 --> 00:03:14.309 align:start position:0%
like the long context training and all
the<00:03:11.840><c> post-training</c><00:03:12.640><c> recipes</c><00:03:13.519><c> for</c><00:03:13.920><c> actually</c>

00:03:14.309 --> 00:03:14.319 align:start position:0%
the post-training recipes for actually
 

00:03:14.319 --> 00:03:16.869 align:start position:0%
the post-training recipes for actually
putting<00:03:14.640><c> this</c><00:03:14.959><c> together.</c><00:03:15.840><c> So,</c><00:03:16.159><c> if</c><00:03:16.319><c> we</c><00:03:16.480><c> just</c><00:03:16.720><c> go</c>

00:03:16.869 --> 00:03:16.879 align:start position:0%
putting this together. So, if we just go
 

00:03:16.879 --> 00:03:18.630 align:start position:0%
putting this together. So, if we just go
through<00:03:17.120><c> those</c><00:03:17.360><c> quickly,</c><00:03:17.920><c> they've</c><00:03:18.159><c> got</c><00:03:18.400><c> some</c>

00:03:18.630 --> 00:03:18.640 align:start position:0%
through those quickly, they've got some
 

00:03:18.640 --> 00:03:20.309 align:start position:0%
through those quickly, they've got some
of<00:03:18.720><c> these</c><00:03:18.959><c> are</c><00:03:19.120><c> already</c><00:03:19.440><c> up</c><00:03:19.599><c> on</c><00:03:19.840><c> the</c><00:03:20.080><c> blog</c>

00:03:20.309 --> 00:03:20.319 align:start position:0%
of these are already up on the blog
 

00:03:20.319 --> 00:03:22.390 align:start position:0%
of these are already up on the blog
post,<00:03:21.120><c> but</c><00:03:21.360><c> like</c><00:03:21.519><c> I</c><00:03:21.680><c> said,</c><00:03:21.840><c> you've</c><00:03:22.080><c> also</c><00:03:22.319><c> got</c>

00:03:22.390 --> 00:03:22.400 align:start position:0%
post, but like I said, you've also got
 

00:03:22.400 --> 00:03:24.070 align:start position:0%
post, but like I said, you've also got
the<00:03:22.560><c> blueprint</c><00:03:22.959><c> where</c><00:03:23.200><c> you</c><00:03:23.360><c> can</c><00:03:23.440><c> just</c><00:03:23.680><c> come</c><00:03:23.760><c> in</c>

00:03:24.070 --> 00:03:24.080 align:start position:0%
the blueprint where you can just come in
 

00:03:24.080 --> 00:03:26.070 align:start position:0%
the blueprint where you can just come in
and<00:03:24.319><c> actually</c><00:03:24.879><c> analyze</c><00:03:25.440><c> these</c><00:03:25.760><c> things</c>

00:03:26.070 --> 00:03:26.080 align:start position:0%
and actually analyze these things
 

00:03:26.080 --> 00:03:28.390 align:start position:0%
and actually analyze these things
yourself.<00:03:27.040><c> We</c><00:03:27.280><c> can</c><00:03:27.360><c> see</c><00:03:27.440><c> the</c><00:03:27.599><c> long</c><00:03:27.840><c> context</c>

00:03:28.390 --> 00:03:28.400 align:start position:0%
yourself. We can see the long context
 

00:03:28.400 --> 00:03:31.030 align:start position:0%
yourself. We can see the long context
can<00:03:28.640><c> go</c><00:03:28.879><c> up</c><00:03:29.040><c> to</c><00:03:29.280><c> 256.</c><00:03:30.080><c> I'm</c><00:03:30.239><c> not</c><00:03:30.400><c> sure</c><00:03:30.560><c> how</c><00:03:30.799><c> great</c>

00:03:31.030 --> 00:03:31.040 align:start position:0%
can go up to 256. I'm not sure how great
 

00:03:31.040 --> 00:03:33.830 align:start position:0%
can go up to 256. I'm not sure how great
it's<00:03:31.280><c> going</c><00:03:31.360><c> to</c><00:03:31.519><c> be</c><00:03:31.680><c> past</c><00:03:32.239><c> 128.</c><00:03:33.280><c> It</c><00:03:33.440><c> can</c><00:03:33.599><c> use</c>

00:03:33.830 --> 00:03:33.840 align:start position:0%
it's going to be past 128. It can use
 

00:03:33.840 --> 00:03:35.190 align:start position:0%
it's going to be past 128. It can use
tools,<00:03:34.239><c> which</c><00:03:34.480><c> I</c><00:03:34.640><c> definitely</c><00:03:34.879><c> want</c><00:03:35.040><c> to</c><00:03:35.120><c> look</c>

00:03:35.190 --> 00:03:35.200 align:start position:0%
tools, which I definitely want to look
 

00:03:35.200 --> 00:03:36.789 align:start position:0%
tools, which I definitely want to look
at<00:03:35.360><c> the</c><00:03:35.519><c> end,</c><00:03:35.680><c> and</c><00:03:35.840><c> we'll</c><00:03:36.000><c> test</c><00:03:36.239><c> that</c><00:03:36.400><c> when</c><00:03:36.640><c> we</c>

00:03:36.789 --> 00:03:36.799 align:start position:0%
at the end, and we'll test that when we
 

00:03:36.799 --> 00:03:39.110 align:start position:0%
at the end, and we'll test that when we
do<00:03:36.879><c> the</c><00:03:37.120><c> code</c><00:03:37.360><c> test</c><00:03:37.920><c> for</c><00:03:38.159><c> this.</c><00:03:38.720><c> And</c><00:03:38.879><c> we</c><00:03:39.040><c> can</c>

00:03:39.110 --> 00:03:39.120 align:start position:0%
do the code test for this. And we can
 

00:03:39.120 --> 00:03:40.789 align:start position:0%
do the code test for this. And we can
see<00:03:39.280><c> that</c><00:03:39.440><c> it's</c><00:03:39.680><c> got</c><00:03:39.840><c> this</c><00:03:40.000><c> dual</c><00:03:40.400><c> think</c>

00:03:40.789 --> 00:03:40.799 align:start position:0%
see that it's got this dual think
 

00:03:40.799 --> 00:03:42.869 align:start position:0%
see that it's got this dual think
reasoning<00:03:41.680><c> system</c><00:03:42.000><c> where</c><00:03:42.239><c> you</c><00:03:42.400><c> can</c><00:03:42.480><c> turn</c><00:03:42.640><c> the</c>

00:03:42.869 --> 00:03:42.879 align:start position:0%
reasoning system where you can turn the
 

00:03:42.879 --> 00:03:45.350 align:start position:0%
reasoning system where you can turn the
thinking<00:03:43.280><c> on</c><00:03:43.519><c> and</c><00:03:43.840><c> off.</c><00:03:44.400><c> And</c><00:03:44.640><c> if</c><00:03:44.799><c> we</c><00:03:44.959><c> jump</c><00:03:45.120><c> into</c>

00:03:45.350 --> 00:03:45.360 align:start position:0%
thinking on and off. And if we jump into
 

00:03:45.360 --> 00:03:46.949 align:start position:0%
thinking on and off. And if we jump into
the<00:03:45.519><c> blog</c><00:03:45.760><c> post,</c><00:03:46.080><c> we</c><00:03:46.239><c> can</c><00:03:46.319><c> just</c><00:03:46.480><c> look</c><00:03:46.640><c> at</c><00:03:46.799><c> some</c>

00:03:46.949 --> 00:03:46.959 align:start position:0%
the blog post, we can just look at some
 

00:03:46.959 --> 00:03:48.630 align:start position:0%
the blog post, we can just look at some
of<00:03:47.040><c> the</c><00:03:47.200><c> key</c><00:03:47.440><c> facts</c><00:03:47.760><c> in</c><00:03:48.000><c> here.</c><00:03:48.239><c> So</c><00:03:48.400><c> the</c>

00:03:48.630 --> 00:03:48.640 align:start position:0%
of the key facts in here. So the
 

00:03:48.640 --> 00:03:50.229 align:start position:0%
of the key facts in here. So the
architecture<00:03:49.040><c> that</c><00:03:49.280><c> they've</c><00:03:49.519><c> gone</c><00:03:49.680><c> for</c><00:03:50.000><c> is</c>

00:03:50.229 --> 00:03:50.239 align:start position:0%
architecture that they've gone for is
 

00:03:50.239 --> 00:03:53.110 align:start position:0%
architecture that they've gone for is
actually<00:03:50.480><c> similar</c><00:03:50.879><c> to</c><00:03:51.200><c> some</c><00:03:51.440><c> of</c><00:03:51.599><c> the</c><00:03:52.000><c> Llama</c><00:03:52.720><c> 3</c>

00:03:53.110 --> 00:03:53.120 align:start position:0%
actually similar to some of the Llama 3
 

00:03:53.120 --> 00:03:56.229 align:start position:0%
actually similar to some of the Llama 3
architectures.<00:03:54.560><c> If</c><00:03:54.799><c> we</c><00:03:54.959><c> think</c><00:03:55.200><c> way</c><00:03:55.519><c> back</c><00:03:55.760><c> now,</c>

00:03:56.229 --> 00:03:56.239 align:start position:0%
architectures. If we think way back now,
 

00:03:56.239 --> 00:03:57.990 align:start position:0%
architectures. If we think way back now,
it's<00:03:56.480><c> been</c><00:03:56.720><c> quite</c><00:03:56.879><c> a</c><00:03:57.040><c> long</c><00:03:57.200><c> time</c><00:03:57.360><c> since</c><00:03:57.599><c> Llama</c>

00:03:57.990 --> 00:03:58.000 align:start position:0%
it's been quite a long time since Llama
 

00:03:58.000 --> 00:04:00.710 align:start position:0%
it's been quite a long time since Llama
3<00:03:58.480><c> was</c><00:03:58.799><c> released.</c><00:03:59.519><c> Clearly,</c><00:03:59.920><c> the</c><00:04:00.080><c> Llama</c><00:04:00.480><c> 3</c>

00:04:00.710 --> 00:04:00.720 align:start position:0%
3 was released. Clearly, the Llama 3
 

00:04:00.720 --> 00:04:02.229 align:start position:0%
3 was released. Clearly, the Llama 3
release<00:04:01.040><c> was</c><00:04:01.280><c> a</c><00:04:01.439><c> lot</c><00:04:01.519><c> more</c><00:04:01.760><c> successful</c><00:04:02.080><c> than</c>

00:04:02.229 --> 00:04:02.239 align:start position:0%
release was a lot more successful than
 

00:04:02.239 --> 00:04:05.270 align:start position:0%
release was a lot more successful than
the<00:04:02.400><c> Llama</c><00:04:02.879><c> 4</c><00:04:03.120><c> release.</c><00:04:04.000><c> It</c><00:04:04.239><c> didn't</c><00:04:04.640><c> require</c>

00:04:05.270 --> 00:04:05.280 align:start position:0%
the Llama 4 release. It didn't require
 

00:04:05.280 --> 00:04:07.910 align:start position:0%
the Llama 4 release. It didn't require
Zuckerberg<00:04:05.920><c> going</c><00:04:06.080><c> out</c><00:04:06.319><c> and</c><00:04:06.560><c> trying</c><00:04:06.879><c> to</c><00:04:07.599><c> hire</c>

00:04:07.910 --> 00:04:07.920 align:start position:0%
Zuckerberg going out and trying to hire
 

00:04:07.920 --> 00:04:10.710 align:start position:0%
Zuckerberg going out and trying to hire
as<00:04:08.159><c> many</c><00:04:08.319><c> LLM</c><00:04:08.959><c> researchers</c><00:04:09.439><c> as</c><00:04:09.760><c> possible</c><00:04:10.480><c> for</c>

00:04:10.710 --> 00:04:10.720 align:start position:0%
as many LLM researchers as possible for
 

00:04:10.720 --> 00:04:12.630 align:start position:0%
as many LLM researchers as possible for
doing<00:04:10.959><c> that.</c><00:04:11.760><c> But</c><00:04:11.920><c> we</c><00:04:12.080><c> can</c><00:04:12.159><c> see</c><00:04:12.239><c> that</c><00:04:12.400><c> in</c><00:04:12.560><c> there</c>

00:04:12.630 --> 00:04:12.640 align:start position:0%
doing that. But we can see that in there
 

00:04:12.640 --> 00:04:14.390 align:start position:0%
doing that. But we can see that in there
they've<00:04:12.879><c> got</c><00:04:12.959><c> the</c><00:04:13.040><c> group</c><00:04:13.280><c> query</c><00:04:13.599><c> attention.</c>

00:04:14.390 --> 00:04:14.400 align:start position:0%
they've got the group query attention.
 

00:04:14.400 --> 00:04:15.750 align:start position:0%
they've got the group query attention.
Now<00:04:14.640><c> the</c><00:04:14.879><c> interesting</c><00:04:15.120><c> thing</c><00:04:15.200><c> is</c><00:04:15.360><c> they've</c><00:04:15.680><c> got</c>

00:04:15.750 --> 00:04:15.760 align:start position:0%
Now the interesting thing is they've got
 

00:04:15.760 --> 00:04:18.390 align:start position:0%
Now the interesting thing is they've got
this<00:04:16.000><c> new</c><00:04:16.239><c> thing</c><00:04:16.560><c> called</c><00:04:16.880><c> nope</c><00:04:17.759><c> and</c><00:04:18.160><c> they're</c>

00:04:18.390 --> 00:04:18.400 align:start position:0%
this new thing called nope and they're
 

00:04:18.400 --> 00:04:20.949 align:start position:0%
this new thing called nope and they're
fully<00:04:18.720><c> citing</c><00:04:19.359><c> in</c><00:04:19.680><c> here.</c><00:04:20.239><c> So</c><00:04:20.400><c> my</c><00:04:20.639><c> brief</c>

00:04:20.949 --> 00:04:20.959 align:start position:0%
fully citing in here. So my brief
 

00:04:20.959 --> 00:04:22.629 align:start position:0%
fully citing in here. So my brief
understanding<00:04:21.359><c> of</c><00:04:21.600><c> this</c><00:04:21.759><c> is</c><00:04:21.919><c> using</c><00:04:22.160><c> like</c><00:04:22.400><c> a</c>

00:04:22.629 --> 00:04:22.639 align:start position:0%
understanding of this is using like a
 

00:04:22.639 --> 00:04:24.230 align:start position:0%
understanding of this is using like a
cause<00:04:22.800><c> of</c><00:04:22.960><c> mus</c><00:04:23.280><c> rather</c><00:04:23.520><c> than</c><00:04:23.600><c> the</c><00:04:23.840><c> rotary</c>

00:04:24.230 --> 00:04:24.240 align:start position:0%
cause of mus rather than the rotary
 

00:04:24.240 --> 00:04:26.710 align:start position:0%
cause of mus rather than the rotary
embeddings<00:04:25.040><c> etc.</c><00:04:25.759><c> And</c><00:04:25.919><c> you</c><00:04:26.080><c> can</c><00:04:26.240><c> see</c><00:04:26.320><c> that</c>

00:04:26.710 --> 00:04:26.720 align:start position:0%
embeddings etc. And you can see that
 

00:04:26.720 --> 00:04:28.230 align:start position:0%
embeddings etc. And you can see that
even<00:04:26.960><c> though</c><00:04:27.120><c> they've</c><00:04:27.360><c> taken</c><00:04:27.680><c> certain</c><00:04:28.000><c> things</c>

00:04:28.230 --> 00:04:28.240 align:start position:0%
even though they've taken certain things
 

00:04:28.240 --> 00:04:30.790 align:start position:0%
even though they've taken certain things
from<00:04:28.400><c> the</c><00:04:28.639><c> llama</c><00:04:29.280><c> series</c><00:04:30.080><c> they've</c><00:04:30.400><c> also</c>

00:04:30.790 --> 00:04:30.800 align:start position:0%
from the llama series they've also
 

00:04:30.800 --> 00:04:33.189 align:start position:0%
from the llama series they've also
basically<00:04:31.199><c> looked</c><00:04:31.520><c> around</c><00:04:32.000><c> and</c><00:04:32.240><c> taken</c><00:04:32.720><c> ideas</c>

00:04:33.189 --> 00:04:33.199 align:start position:0%
basically looked around and taken ideas
 

00:04:33.199 --> 00:04:35.270 align:start position:0%
basically looked around and taken ideas
from<00:04:33.520><c> things</c><00:04:33.680><c> like</c><00:04:34.160><c> mo</c><00:04:34.479><c> 2</c><00:04:34.720><c> where</c><00:04:34.960><c> they</c><00:04:35.120><c> talk</c>

00:04:35.270 --> 00:04:35.280 align:start position:0%
from things like mo 2 where they talk
 

00:04:35.280 --> 00:04:37.270 align:start position:0%
from things like mo 2 where they talk
about<00:04:35.600><c> removing</c><00:04:36.080><c> this</c><00:04:36.400><c> weight</c><00:04:36.639><c> decay</c><00:04:37.040><c> from</c>

00:04:37.270 --> 00:04:37.280 align:start position:0%
about removing this weight decay from
 

00:04:37.280 --> 00:04:39.350 align:start position:0%
about removing this weight decay from
embedding<00:04:37.919><c> layers</c><00:04:38.160><c> to</c><00:04:38.479><c> improve</c><00:04:38.960><c> training</c>

00:04:39.350 --> 00:04:39.360 align:start position:0%
embedding layers to improve training
 

00:04:39.360 --> 00:04:40.790 align:start position:0%
embedding layers to improve training
stability.<00:04:39.840><c> Now,</c><00:04:40.080><c> that's</c><00:04:40.320><c> interesting</c><00:04:40.639><c> and</c>

00:04:40.790 --> 00:04:40.800 align:start position:0%
stability. Now, that's interesting and
 

00:04:40.800 --> 00:04:42.710 align:start position:0%
stability. Now, that's interesting and
going<00:04:40.960><c> to</c><00:04:41.040><c> be</c><00:04:41.120><c> very</c><00:04:41.440><c> useful</c><00:04:41.759><c> for</c><00:04:42.080><c> anyone</c><00:04:42.479><c> that</c>

00:04:42.710 --> 00:04:42.720 align:start position:0%
going to be very useful for anyone that
 

00:04:42.720 --> 00:04:44.710 align:start position:0%
going to be very useful for anyone that
does<00:04:42.960><c> want</c><00:04:43.120><c> to</c><00:04:43.280><c> try</c><00:04:43.440><c> and</c><00:04:43.840><c> train</c><00:04:44.160><c> one</c><00:04:44.320><c> of</c><00:04:44.400><c> these</c>

00:04:44.710 --> 00:04:44.720 align:start position:0%
does want to try and train one of these
 

00:04:44.720 --> 00:04:47.030 align:start position:0%
does want to try and train one of these
small<00:04:45.040><c> models.</c><00:04:45.759><c> And</c><00:04:45.919><c> I</c><00:04:46.080><c> got</c><00:04:46.240><c> to</c><00:04:46.400><c> say</c><00:04:46.720><c> that</c>

00:04:47.030 --> 00:04:47.040 align:start position:0%
small models. And I got to say that
 

00:04:47.040 --> 00:04:49.749 align:start position:0%
small models. And I got to say that
looking<00:04:47.360><c> at</c><00:04:47.520><c> their</c><00:04:47.759><c> GPU</c><00:04:48.400><c> budget</c><00:04:48.800><c> for</c><00:04:49.040><c> this,</c>

00:04:49.749 --> 00:04:49.759 align:start position:0%
looking at their GPU budget for this,
 

00:04:49.759 --> 00:04:53.909 align:start position:0%
looking at their GPU budget for this,
they<00:04:50.000><c> had</c><00:04:50.240><c> 384</c><00:04:51.199><c> H100s</c><00:04:52.160><c> for</c><00:04:52.479><c> 24</c><00:04:52.960><c> days.</c><00:04:53.520><c> That's</c>

00:04:53.909 --> 00:04:53.919 align:start position:0%
they had 384 H100s for 24 days. That's
 

00:04:53.919 --> 00:04:57.590 align:start position:0%
they had 384 H100s for 24 days. That's
not<00:04:54.160><c> a</c><00:04:54.479><c> crazy</c><00:04:55.120><c> amount</c><00:04:55.440><c> of</c><00:04:55.759><c> GPUs</c><00:04:56.800><c> time.</c><00:04:57.360><c> So,</c>

00:04:57.590 --> 00:04:57.600 align:start position:0%
not a crazy amount of GPUs time. So,
 

00:04:57.600 --> 00:04:59.030 align:start position:0%
not a crazy amount of GPUs time. So,
actually,<00:04:57.840><c> I</c><00:04:58.080><c> think</c><00:04:58.320><c> that</c><00:04:58.560><c> works</c><00:04:58.720><c> out</c><00:04:58.880><c> to</c>

00:04:59.030 --> 00:04:59.040 align:start position:0%
actually, I think that works out to
 

00:04:59.040 --> 00:05:03.350 align:start position:0%
actually, I think that works out to
roughly<00:04:59.600><c> 220,000</c><00:05:00.880><c> plus</c><00:05:01.600><c> GPU</c><00:05:02.160><c> hours</c><00:05:02.639><c> there.</c>

00:05:03.350 --> 00:05:03.360 align:start position:0%
roughly 220,000 plus GPU hours there.
 

00:05:03.360 --> 00:05:05.110 align:start position:0%
roughly 220,000 plus GPU hours there.
And<00:05:03.600><c> the</c><00:05:03.840><c> interesting</c><00:05:04.160><c> thing</c><00:05:04.240><c> is</c><00:05:04.560><c> you</c><00:05:04.720><c> can</c><00:05:04.880><c> get</c>

00:05:05.110 --> 00:05:05.120 align:start position:0%
And the interesting thing is you can get
 

00:05:05.120 --> 00:05:08.150 align:start position:0%
And the interesting thing is you can get
H100s<00:05:06.160><c> now.</c><00:05:06.639><c> The</c><00:05:06.800><c> price</c><00:05:07.120><c> of</c><00:05:07.440><c> being</c><00:05:07.680><c> able</c><00:05:07.919><c> to</c>

00:05:08.150 --> 00:05:08.160 align:start position:0%
H100s now. The price of being able to
 

00:05:08.160 --> 00:05:10.390 align:start position:0%
H100s now. The price of being able to
rent<00:05:08.479><c> them</c><00:05:08.639><c> out</c><00:05:08.880><c> is</c><00:05:09.199><c> going</c><00:05:09.360><c> down.</c><00:05:09.680><c> So</c><00:05:10.160><c> we're</c>

00:05:10.390 --> 00:05:10.400 align:start position:0%
rent them out is going down. So we're
 

00:05:10.400 --> 00:05:11.830 align:start position:0%
rent them out is going down. So we're
looking<00:05:10.560><c> at</c><00:05:10.800><c> something</c><00:05:11.039><c> that</c><00:05:11.280><c> probably</c><00:05:11.600><c> cost</c>

00:05:11.830 --> 00:05:11.840 align:start position:0%
looking at something that probably cost
 

00:05:11.840 --> 00:05:14.150 align:start position:0%
looking at something that probably cost
a<00:05:12.080><c> few</c><00:05:12.160><c> hundred,000</c><00:05:13.120><c> to</c><00:05:13.360><c> train.</c><00:05:14.000><c> It's</c>

00:05:14.150 --> 00:05:14.160 align:start position:0%
a few hundred,000 to train. It's
 

00:05:14.160 --> 00:05:15.990 align:start position:0%
a few hundred,000 to train. It's
certainly<00:05:14.479><c> not</c><00:05:14.639><c> in</c><00:05:14.800><c> the</c><00:05:14.960><c> large</c><00:05:15.280><c> millions</c><00:05:15.759><c> of</c>

00:05:15.990 --> 00:05:16.000 align:start position:0%
certainly not in the large millions of
 

00:05:16.000 --> 00:05:18.310 align:start position:0%
certainly not in the large millions of
dollars<00:05:16.720><c> that</c><00:05:16.960><c> we've</c><00:05:17.199><c> seen</c><00:05:17.440><c> people</c><00:05:17.680><c> do</c><00:05:17.919><c> in</c><00:05:18.160><c> the</c>

00:05:18.310 --> 00:05:18.320 align:start position:0%
dollars that we've seen people do in the
 

00:05:18.320 --> 00:05:20.790 align:start position:0%
dollars that we've seen people do in the
past.<00:05:19.360><c> All</c><00:05:19.440><c> right.</c><00:05:19.840><c> So</c><00:05:20.080><c> one</c><00:05:20.320><c> of</c><00:05:20.400><c> the</c><00:05:20.639><c> things</c>

00:05:20.790 --> 00:05:20.800 align:start position:0%
past. All right. So one of the things
 

00:05:20.800 --> 00:05:23.110 align:start position:0%
past. All right. So one of the things
that<00:05:21.120><c> is</c><00:05:21.440><c> always</c><00:05:21.840><c> the</c><00:05:22.160><c> mystery</c><00:05:22.639><c> of</c><00:05:22.880><c> the</c>

00:05:23.110 --> 00:05:23.120 align:start position:0%
that is always the mystery of the
 

00:05:23.120 --> 00:05:25.990 align:start position:0%
that is always the mystery of the
proprietary<00:05:23.759><c> models</c><00:05:24.560><c> is</c><00:05:25.280><c> how</c><00:05:25.520><c> do</c><00:05:25.680><c> they</c><00:05:25.840><c> do</c>

00:05:25.990 --> 00:05:26.000 align:start position:0%
proprietary models is how do they do
 

00:05:26.000 --> 00:05:28.310 align:start position:0%
proprietary models is how do they do
their<00:05:26.320><c> data</c><00:05:26.639><c> mixes</c><00:05:27.199><c> and</c><00:05:27.440><c> how</c><00:05:27.600><c> do</c><00:05:27.759><c> they</c><00:05:28.080><c> do</c>

00:05:28.310 --> 00:05:28.320 align:start position:0%
their data mixes and how do they do
 

00:05:28.320 --> 00:05:30.070 align:start position:0%
their data mixes and how do they do
their<00:05:28.560><c> pre-training.</c><00:05:29.360><c> So,</c><00:05:29.600><c> it's</c><00:05:29.759><c> really</c><00:05:30.000><c> nice</c>

00:05:30.070 --> 00:05:30.080 align:start position:0%
their pre-training. So, it's really nice
 

00:05:30.080 --> 00:05:31.270 align:start position:0%
their pre-training. So, it's really nice
to<00:05:30.240><c> see</c><00:05:30.400><c> here</c><00:05:30.560><c> that</c><00:05:30.720><c> they're</c><00:05:30.960><c> talking</c><00:05:31.120><c> about</c>

00:05:31.270 --> 00:05:31.280 align:start position:0%
to see here that they're talking about
 

00:05:31.280 --> 00:05:33.990 align:start position:0%
to see here that they're talking about
doing<00:05:31.440><c> a</c><00:05:31.680><c> three-phase</c><00:05:32.639><c> pre-training</c><00:05:33.360><c> with</c><00:05:33.759><c> an</c>

00:05:33.990 --> 00:05:34.000 align:start position:0%
doing a three-phase pre-training with an
 

00:05:34.000 --> 00:05:36.710 align:start position:0%
doing a three-phase pre-training with an
kneeling<00:05:34.479><c> sort</c><00:05:34.639><c> of</c><00:05:34.800><c> at</c><00:05:35.039><c> the</c><00:05:35.199><c> end</c><00:05:35.520><c> of</c><00:05:35.759><c> that,</c><00:05:36.479><c> but</c>

00:05:36.710 --> 00:05:36.720 align:start position:0%
kneeling sort of at the end of that, but
 

00:05:36.720 --> 00:05:38.950 align:start position:0%
kneeling sort of at the end of that, but
more<00:05:37.039><c> interestingly</c><00:05:37.680><c> looking</c><00:05:37.919><c> at</c><00:05:38.080><c> the</c><00:05:38.400><c> splits</c>

00:05:38.950 --> 00:05:38.960 align:start position:0%
more interestingly looking at the splits
 

00:05:38.960 --> 00:05:41.749 align:start position:0%
more interestingly looking at the splits
of<00:05:39.520><c> data</c><00:05:39.919><c> as</c><00:05:40.160><c> they</c><00:05:40.400><c> go</c><00:05:40.560><c> through</c><00:05:40.800><c> this.</c><00:05:41.360><c> So,</c><00:05:41.520><c> you</c>

00:05:41.749 --> 00:05:41.759 align:start position:0%
of data as they go through this. So, you
 

00:05:41.759 --> 00:05:43.670 align:start position:0%
of data as they go through this. So, you
can<00:05:41.840><c> see</c><00:05:42.000><c> at</c><00:05:42.160><c> the</c><00:05:42.320><c> start</c><00:05:42.560><c> it's</c><00:05:42.800><c> very</c><00:05:43.039><c> webheavy</c>

00:05:43.670 --> 00:05:43.680 align:start position:0%
can see at the start it's very webheavy
 

00:05:43.680 --> 00:05:46.629 align:start position:0%
can see at the start it's very webheavy
for<00:05:44.000><c> that</c><00:05:44.240><c> sort</c><00:05:44.400><c> of</c><00:05:44.560><c> long</c><00:05:45.039><c> phase,</c><00:05:46.080><c> but</c><00:05:46.320><c> then</c><00:05:46.479><c> in</c>

00:05:46.629 --> 00:05:46.639 align:start position:0%
for that sort of long phase, but then in
 

00:05:46.639 --> 00:05:49.029 align:start position:0%
for that sort of long phase, but then in
phase<00:05:46.960><c> two</c><00:05:47.120><c> and</c><00:05:47.360><c> phase</c><00:05:47.600><c> three,</c><00:05:48.240><c> they</c><00:05:48.560><c> actually</c>

00:05:49.029 --> 00:05:49.039 align:start position:0%
phase two and phase three, they actually
 

00:05:49.039 --> 00:05:50.870 align:start position:0%
phase two and phase three, they actually
increase<00:05:49.360><c> the</c><00:05:49.680><c> code.</c><00:05:50.000><c> They</c><00:05:50.400><c> increase</c><00:05:50.639><c> the</c>

00:05:50.870 --> 00:05:50.880 align:start position:0%
increase the code. They increase the
 

00:05:50.880 --> 00:05:54.310 align:start position:0%
increase the code. They increase the
math<00:05:51.600><c> quite</c><00:05:51.840><c> a</c><00:05:52.080><c> lot</c><00:05:52.320><c> in</c><00:05:52.639><c> there</c><00:05:53.440><c> just</c><00:05:53.759><c> for</c><00:05:54.080><c> this</c>

00:05:54.310 --> 00:05:54.320 align:start position:0%
math quite a lot in there just for this
 

00:05:54.320 --> 00:05:57.270 align:start position:0%
math quite a lot in there just for this
pre-training<00:05:55.039><c> phrase.</c><00:05:56.160><c> Now</c><00:05:56.560><c> after</c><00:05:56.800><c> that</c><00:05:57.039><c> they</c>

00:05:57.270 --> 00:05:57.280 align:start position:0%
pre-training phrase. Now after that they
 

00:05:57.280 --> 00:05:59.270 align:start position:0%
pre-training phrase. Now after that they
talk<00:05:57.440><c> about</c><00:05:57.759><c> how</c><00:05:57.919><c> they</c><00:05:58.160><c> did</c><00:05:58.240><c> the</c><00:05:58.400><c> long</c><00:05:58.720><c> context</c>

00:05:59.270 --> 00:05:59.280 align:start position:0%
talk about how they did the long context
 

00:05:59.280 --> 00:06:01.670 align:start position:0%
talk about how they did the long context
stuff<00:05:59.759><c> and</c><00:06:00.000><c> also</c><00:06:00.400><c> how</c><00:06:00.639><c> they</c><00:06:00.880><c> did</c><00:06:01.120><c> the</c><00:06:01.360><c> actual</c>

00:06:01.670 --> 00:06:01.680 align:start position:0%
stuff and also how they did the actual
 

00:06:01.680 --> 00:06:04.550 align:start position:0%
stuff and also how they did the actual
reasoning<00:06:02.240><c> stuff.</c><00:06:02.639><c> Now</c><00:06:03.440><c> for</c><00:06:03.680><c> me</c><00:06:04.160><c> looking</c><00:06:04.400><c> at</c>

00:06:04.550 --> 00:06:04.560 align:start position:0%
reasoning stuff. Now for me looking at
 

00:06:04.560 --> 00:06:06.790 align:start position:0%
reasoning stuff. Now for me looking at
this<00:06:04.880><c> sort</c><00:06:05.039><c> of</c><00:06:05.360><c> briefly</c><00:06:06.000><c> it</c><00:06:06.240><c> does</c><00:06:06.400><c> look</c><00:06:06.639><c> that</c>

00:06:06.790 --> 00:06:06.800 align:start position:0%
this sort of briefly it does look that
 

00:06:06.800 --> 00:06:09.430 align:start position:0%
this sort of briefly it does look that
like<00:06:07.039><c> they've</c><00:06:07.280><c> heavily</c><00:06:07.759><c> relied</c><00:06:08.240><c> on</c><00:06:08.639><c> both</c><00:06:09.199><c> the</c>

00:06:09.430 --> 00:06:09.440 align:start position:0%
like they've heavily relied on both the
 

00:06:09.440 --> 00:06:12.950 align:start position:0%
like they've heavily relied on both the
DeepSync<00:06:10.080><c> R1</c><00:06:11.039><c> reasoning</c><00:06:11.520><c> traces</c><00:06:12.080><c> as</c><00:06:12.400><c> well</c><00:06:12.560><c> as</c>

00:06:12.950 --> 00:06:12.960 align:start position:0%
DeepSync R1 reasoning traces as well as
 

00:06:12.960 --> 00:06:16.790 align:start position:0%
DeepSync R1 reasoning traces as well as
Quen<00:06:13.440><c> 3</c><00:06:14.160><c> for</c><00:06:14.560><c> creating</c><00:06:15.039><c> synthetic</c><00:06:15.680><c> data</c><00:06:16.240><c> to</c><00:06:16.639><c> be</c>

00:06:16.790 --> 00:06:16.800 align:start position:0%
Quen 3 for creating synthetic data to be
 

00:06:16.800 --> 00:06:19.510 align:start position:0%
Quen 3 for creating synthetic data to be
able<00:06:17.039><c> to</c><00:06:17.759><c> make</c><00:06:18.080><c> some</c><00:06:18.319><c> of</c><00:06:18.479><c> these</c><00:06:19.039><c> reasoning</c>

00:06:19.510 --> 00:06:19.520 align:start position:0%
able to make some of these reasoning
 

00:06:19.520 --> 00:06:21.670 align:start position:0%
able to make some of these reasoning
traces<00:06:20.080><c> out.</c><00:06:20.479><c> So</c><00:06:20.800><c> it</c><00:06:21.039><c> doesn't</c><00:06:21.280><c> look</c><00:06:21.440><c> like</c>

00:06:21.670 --> 00:06:21.680 align:start position:0%
traces out. So it doesn't look like
 

00:06:21.680 --> 00:06:24.629 align:start position:0%
traces out. So it doesn't look like
they've<00:06:22.000><c> done</c><00:06:22.319><c> a</c><00:06:22.560><c> large</c><00:06:22.880><c> amount</c><00:06:23.360><c> of</c><00:06:24.160><c> sort</c><00:06:24.400><c> of</c>

00:06:24.629 --> 00:06:24.639 align:start position:0%
they've done a large amount of sort of
 

00:06:24.639 --> 00:06:27.990 align:start position:0%
they've done a large amount of sort of
RLVR<00:06:25.759><c> or</c><00:06:26.080><c> anything</c><00:06:26.319><c> like</c><00:06:26.639><c> that</c><00:06:26.960><c> in</c><00:06:27.199><c> here</c><00:06:27.919><c> at</c>

00:06:27.990 --> 00:06:28.000 align:start position:0%
RLVR or anything like that in here at
 

00:06:28.000 --> 00:06:29.749 align:start position:0%
RLVR or anything like that in here at
least<00:06:28.240><c> in</c><00:06:28.479><c> comparison</c><00:06:28.960><c> to</c><00:06:29.199><c> perhaps</c><00:06:29.440><c> some</c><00:06:29.680><c> of</c>

00:06:29.749 --> 00:06:29.759 align:start position:0%
least in comparison to perhaps some of
 

00:06:29.759 --> 00:06:31.990 align:start position:0%
least in comparison to perhaps some of
the<00:06:30.000><c> Chinese</c><00:06:30.400><c> labs</c><00:06:30.880><c> that</c><00:06:31.199><c> these</c><00:06:31.600><c> people</c><00:06:31.759><c> are</c>

00:06:31.990 --> 00:06:32.000 align:start position:0%
the Chinese labs that these people are
 

00:06:32.000 --> 00:06:33.990 align:start position:0%
the Chinese labs that these people are
trying<00:06:32.080><c> to</c><00:06:32.240><c> spend</c><00:06:32.560><c> longer</c><00:06:33.120><c> doing</c><00:06:33.440><c> the</c>

00:06:33.990 --> 00:06:34.000 align:start position:0%
trying to spend longer doing the
 

00:06:34.000 --> 00:06:35.749 align:start position:0%
trying to spend longer doing the
reinforcement<00:06:34.639><c> learning</c><00:06:34.880><c> with</c><00:06:35.120><c> verifiable</c>

00:06:35.749 --> 00:06:35.759 align:start position:0%
reinforcement learning with verifiable
 

00:06:35.759 --> 00:06:38.469 align:start position:0%
reinforcement learning with verifiable
rewards<00:06:36.479><c> etc.</c><00:06:37.360><c> Interestingly,</c><00:06:38.000><c> they</c><00:06:38.240><c> use</c><00:06:38.319><c> a</c>

00:06:38.469 --> 00:06:38.479 align:start position:0%
rewards etc. Interestingly, they use a
 

00:06:38.479 --> 00:06:41.430 align:start position:0%
rewards etc. Interestingly, they use a
new<00:06:38.639><c> form</c><00:06:38.880><c> of</c><00:06:39.039><c> alignment</c><00:06:40.080><c> in</c><00:06:40.400><c> here,</c><00:06:41.039><c> which</c><00:06:41.280><c> is</c>

00:06:41.430 --> 00:06:41.440 align:start position:0%
new form of alignment in here, which is
 

00:06:41.440 --> 00:06:44.710 align:start position:0%
new form of alignment in here, which is
a<00:06:41.600><c> variant</c><00:06:42.000><c> on</c><00:06:42.240><c> DPO.</c><00:06:43.680><c> And</c><00:06:43.919><c> finally,</c><00:06:44.240><c> they</c><00:06:44.479><c> also</c>

00:06:44.710 --> 00:06:44.720 align:start position:0%
a variant on DPO. And finally, they also
 

00:06:44.720 --> 00:06:46.390 align:start position:0%
a variant on DPO. And finally, they also
do<00:06:44.960><c> things</c><00:06:45.120><c> like</c><00:06:45.360><c> model</c><00:06:45.680><c> merging</c><00:06:46.160><c> to</c>

00:06:46.390 --> 00:06:46.400 align:start position:0%
do things like model merging to
 

00:06:46.400 --> 00:06:48.950 align:start position:0%
do things like model merging to
basically<00:06:46.960><c> take</c><00:06:47.440><c> various</c><00:06:47.919><c> checkpoints</c><00:06:48.720><c> and</c>

00:06:48.950 --> 00:06:48.960 align:start position:0%
basically take various checkpoints and
 

00:06:48.960 --> 00:06:50.950 align:start position:0%
basically take various checkpoints and
try<00:06:49.120><c> and</c><00:06:49.360><c> create</c><00:06:49.680><c> a</c><00:06:49.919><c> super</c><00:06:50.240><c> checkpoint</c><00:06:50.720><c> in</c>

00:06:50.950 --> 00:06:50.960 align:start position:0%
try and create a super checkpoint in
 

00:06:50.960 --> 00:06:53.189 align:start position:0%
try and create a super checkpoint in
here.<00:06:51.520><c> Now,</c><00:06:51.759><c> as</c><00:06:51.919><c> I</c><00:06:52.160><c> mentioned,</c><00:06:52.479><c> they've</c><00:06:52.880><c> put</c>

00:06:53.189 --> 00:06:53.199 align:start position:0%
here. Now, as I mentioned, they've put
 

00:06:53.199 --> 00:06:55.510 align:start position:0%
here. Now, as I mentioned, they've put
the<00:06:53.919><c> base</c><00:06:54.319><c> model</c><00:06:54.639><c> and</c><00:06:54.880><c> the</c><00:06:55.039><c> instruction</c>

00:06:55.510 --> 00:06:55.520 align:start position:0%
the base model and the instruction
 

00:06:55.520 --> 00:06:58.710 align:start position:0%
the base model and the instruction
fine-tune<00:06:56.160><c> model</c><00:06:56.560><c> up</c><00:06:56.880><c> on</c><00:06:57.600><c> hugging</c><00:06:58.080><c> phase.</c>

00:06:58.710 --> 00:06:58.720 align:start position:0%
fine-tune model up on hugging phase.
 

00:06:58.720 --> 00:07:00.230 align:start position:0%
fine-tune model up on hugging phase.
Actually,<00:06:59.039><c> perhaps</c><00:06:59.520><c> would</c><00:06:59.680><c> have</c><00:06:59.840><c> been</c><00:07:00.000><c> nice</c>

00:07:00.230 --> 00:07:00.240 align:start position:0%
Actually, perhaps would have been nice
 

00:07:00.240 --> 00:07:01.670 align:start position:0%
Actually, perhaps would have been nice
for<00:07:00.479><c> them</c><00:07:00.639><c> to</c><00:07:00.880><c> put</c><00:07:01.039><c> up</c><00:07:01.199><c> the</c><00:07:01.440><c> different</c>

00:07:01.670 --> 00:07:01.680 align:start position:0%
for them to put up the different
 

00:07:01.680 --> 00:07:04.550 align:start position:0%
for them to put up the different
checkpoints<00:07:02.319><c> at</c><00:07:02.560><c> the</c><00:07:02.720><c> end</c><00:07:02.880><c> of</c><00:07:03.039><c> each</c><00:07:03.360><c> phase</c><00:07:04.240><c> of</c>

00:07:04.550 --> 00:07:04.560 align:start position:0%
checkpoints at the end of each phase of
 

00:07:04.560 --> 00:07:06.550 align:start position:0%
checkpoints at the end of each phase of
training<00:07:04.960><c> as</c><00:07:05.199><c> well.</c><00:07:05.599><c> I'm</c><00:07:05.840><c> not</c><00:07:06.000><c> sure</c><00:07:06.080><c> if</c><00:07:06.240><c> they</c>

00:07:06.550 --> 00:07:06.560 align:start position:0%
training as well. I'm not sure if they
 

00:07:06.560 --> 00:07:07.990 align:start position:0%
training as well. I'm not sure if they
perhaps<00:07:06.880><c> they</c><00:07:07.039><c> will</c><00:07:07.199><c> release</c><00:07:07.520><c> that</c><00:07:07.759><c> in</c><00:07:07.919><c> the</c>

00:07:07.990 --> 00:07:08.000 align:start position:0%
perhaps they will release that in the
 

00:07:08.000 --> 00:07:09.270 align:start position:0%
perhaps they will release that in the
future.<00:07:08.319><c> That</c><00:07:08.479><c> would</c><00:07:08.639><c> be</c><00:07:08.800><c> really</c><00:07:08.960><c> good</c><00:07:09.120><c> to</c>

00:07:09.270 --> 00:07:09.280 align:start position:0%
future. That would be really good to
 

00:07:09.280 --> 00:07:11.830 align:start position:0%
future. That would be really good to
see.<00:07:10.000><c> We</c><00:07:10.240><c> can</c><00:07:10.400><c> also</c><00:07:10.720><c> see</c><00:07:10.960><c> that</c><00:07:11.199><c> they've</c><00:07:11.520><c> also</c>

00:07:11.830 --> 00:07:11.840 align:start position:0%
see. We can also see that they've also
 

00:07:11.840 --> 00:07:14.390 align:start position:0%
see. We can also see that they've also
gathered<00:07:12.319><c> all</c><00:07:12.479><c> the</c><00:07:12.720><c> data</c><00:07:13.039><c> sets</c><00:07:13.360><c> here</c><00:07:13.919><c> both</c><00:07:14.160><c> for</c>

00:07:14.390 --> 00:07:14.400 align:start position:0%
gathered all the data sets here both for
 

00:07:14.400 --> 00:07:17.430 align:start position:0%
gathered all the data sets here both for
things<00:07:14.560><c> like</c><00:07:14.800><c> SFT</c><00:07:15.919><c> for</c><00:07:16.400><c> reasoning</c><00:07:16.880><c> data</c><00:07:17.199><c> sets</c>

00:07:17.430 --> 00:07:17.440 align:start position:0%
things like SFT for reasoning data sets
 

00:07:17.440 --> 00:07:19.189 align:start position:0%
things like SFT for reasoning data sets
that<00:07:17.599><c> they</c><00:07:17.759><c> were</c><00:07:17.919><c> using</c><00:07:18.160><c> in</c><00:07:18.479><c> there.</c><00:07:19.039><c> And</c>

00:07:19.189 --> 00:07:19.199 align:start position:0%
that they were using in there. And
 

00:07:19.199 --> 00:07:20.550 align:start position:0%
that they were using in there. And
generally,<00:07:19.520><c> it</c><00:07:19.680><c> seems</c><00:07:19.919><c> to</c><00:07:20.080><c> me</c><00:07:20.160><c> that</c><00:07:20.319><c> we've</c><00:07:20.479><c> got</c>

00:07:20.550 --> 00:07:20.560 align:start position:0%
generally, it seems to me that we've got
 

00:07:20.560 --> 00:07:21.909 align:start position:0%
generally, it seems to me that we've got
to<00:07:20.720><c> think</c><00:07:20.800><c> of</c><00:07:20.960><c> this</c><00:07:21.039><c> as</c><00:07:21.280><c> being</c><00:07:21.440><c> one</c><00:07:21.680><c> of</c><00:07:21.759><c> the</c>

00:07:21.909 --> 00:07:21.919 align:start position:0%
to think of this as being one of the
 

00:07:21.919 --> 00:07:24.550 align:start position:0%
to think of this as being one of the
most<00:07:22.240><c> open</c><00:07:22.800><c> releases</c><00:07:23.440><c> that</c><00:07:23.680><c> we've</c><00:07:24.000><c> seen</c><00:07:24.240><c> in</c>

00:07:24.550 --> 00:07:24.560 align:start position:0%
most open releases that we've seen in
 

00:07:24.560 --> 00:07:26.550 align:start position:0%
most open releases that we've seen in
quite<00:07:24.800><c> a</c><00:07:24.960><c> while.</c><00:07:25.520><c> Okay,</c><00:07:25.759><c> so</c><00:07:26.000><c> let's</c><00:07:26.240><c> jump</c><00:07:26.400><c> in</c>

00:07:26.550 --> 00:07:26.560 align:start position:0%
quite a while. Okay, so let's jump in
 

00:07:26.560 --> 00:07:28.390 align:start position:0%
quite a while. Okay, so let's jump in
the<00:07:26.720><c> code</c><00:07:26.880><c> and</c><00:07:27.120><c> see</c><00:07:27.280><c> how</c><00:07:27.520><c> the</c><00:07:27.680><c> model</c><00:07:28.000><c> actually</c>

00:07:28.390 --> 00:07:28.400 align:start position:0%
the code and see how the model actually
 

00:07:28.400 --> 00:07:31.270 align:start position:0%
the code and see how the model actually
performs.<00:07:29.759><c> Okay,</c><00:07:30.160><c> so</c><00:07:30.479><c> jumping</c><00:07:30.800><c> into</c><00:07:31.120><c> the</c>

00:07:31.270 --> 00:07:31.280 align:start position:0%
performs. Okay, so jumping into the
 

00:07:31.280 --> 00:07:32.710 align:start position:0%
performs. Okay, so jumping into the
code,<00:07:31.599><c> we</c><00:07:31.759><c> can</c><00:07:31.840><c> see</c><00:07:31.919><c> that</c><00:07:32.080><c> it's</c><00:07:32.319><c> really</c><00:07:32.560><c> easy</c>

00:07:32.710 --> 00:07:32.720 align:start position:0%
code, we can see that it's really easy
 

00:07:32.720 --> 00:07:35.029 align:start position:0%
code, we can see that it's really easy
to<00:07:33.039><c> actually</c><00:07:33.520><c> set</c><00:07:33.680><c> this</c><00:07:33.919><c> model</c><00:07:34.240><c> up.</c><00:07:34.720><c> No</c>

00:07:35.029 --> 00:07:35.039 align:start position:0%
to actually set this model up. No
 

00:07:35.039 --> 00:07:37.029 align:start position:0%
to actually set this model up. No
surprise.<00:07:35.680><c> It's</c><00:07:35.919><c> been</c><00:07:36.160><c> created</c><00:07:36.400><c> by</c><00:07:36.639><c> Hugging</c>

00:07:37.029 --> 00:07:37.039 align:start position:0%
surprise. It's been created by Hugging
 

00:07:37.039 --> 00:07:38.870 align:start position:0%
surprise. It's been created by Hugging
Face.<00:07:37.680><c> It's</c><00:07:38.000><c> going</c><00:07:38.080><c> to</c><00:07:38.240><c> work</c><00:07:38.400><c> with</c>

00:07:38.870 --> 00:07:38.880 align:start position:0%
Face. It's going to work with
 

00:07:38.880 --> 00:07:41.110 align:start position:0%
Face. It's going to work with
Transformers<00:07:39.520><c> out</c><00:07:39.759><c> of</c><00:07:39.840><c> the</c><00:07:40.000><c> box.</c><00:07:40.720><c> It</c><00:07:40.960><c> also</c>

00:07:41.110 --> 00:07:41.120 align:start position:0%
Transformers out of the box. It also
 

00:07:41.120 --> 00:07:44.469 align:start position:0%
Transformers out of the box. It also
works<00:07:41.280><c> with</c><00:07:41.599><c> SG</c><00:07:42.000><c> Lang</c><00:07:42.560><c> and</c><00:07:42.800><c> with</c><00:07:43.120><c> VLM.</c><00:07:44.319><c> But</c>

00:07:44.469 --> 00:07:44.479 align:start position:0%
works with SG Lang and with VLM. But
 

00:07:44.479 --> 00:07:45.749 align:start position:0%
works with SG Lang and with VLM. But
anyway,<00:07:44.800><c> in</c><00:07:44.960><c> here</c><00:07:45.120><c> you</c><00:07:45.280><c> can</c><00:07:45.360><c> see</c><00:07:45.440><c> I've</c><00:07:45.599><c> just</c>

00:07:45.749 --> 00:07:45.759 align:start position:0%
anyway, in here you can see I've just
 

00:07:45.759 --> 00:07:48.070 align:start position:0%
anyway, in here you can see I've just
loaded<00:07:46.000><c> up</c><00:07:46.160><c> the</c><00:07:46.319><c> model.</c><00:07:47.039><c> If</c><00:07:47.199><c> we</c><00:07:47.360><c> want</c><00:07:47.440><c> to</c><00:07:47.680><c> do</c>

00:07:48.070 --> 00:07:48.080 align:start position:0%
loaded up the model. If we want to do
 

00:07:48.080 --> 00:07:50.550 align:start position:0%
loaded up the model. If we want to do
reasoning<00:07:48.639><c> outputs,</c><00:07:49.599><c> we</c><00:07:49.840><c> can</c><00:07:50.000><c> just</c><00:07:50.160><c> basically</c>

00:07:50.550 --> 00:07:50.560 align:start position:0%
reasoning outputs, we can just basically
 

00:07:50.560 --> 00:07:53.670 align:start position:0%
reasoning outputs, we can just basically
pass<00:07:50.960><c> in</c><00:07:51.680><c> a</c><00:07:52.000><c> prompt</c><00:07:52.720><c> and</c><00:07:52.960><c> you'll</c><00:07:53.120><c> see</c><00:07:53.280><c> we</c><00:07:53.520><c> get</c>

00:07:53.670 --> 00:07:53.680 align:start position:0%
pass in a prompt and you'll see we get
 

00:07:53.680 --> 00:07:58.150 align:start position:0%
pass in a prompt and you'll see we get
the<00:07:54.000><c> standard</c><00:07:54.560><c> sort</c><00:07:54.879><c> of</c><00:07:55.199><c> deepseek</c><00:07:56.639><c> think</c><00:07:56.960><c> tags</c>

00:07:58.150 --> 00:07:58.160 align:start position:0%
the standard sort of deepseek think tags
 

00:07:58.160 --> 00:07:59.670 align:start position:0%
the standard sort of deepseek think tags
where<00:07:58.400><c> in</c><00:07:58.560><c> this</c><00:07:58.720><c> case</c><00:07:58.879><c> we've</c><00:07:59.120><c> got</c><00:07:59.280><c> quite</c><00:07:59.520><c> a</c>

00:07:59.670 --> 00:07:59.680 align:start position:0%
where in this case we've got quite a
 

00:07:59.680 --> 00:08:02.230 align:start position:0%
where in this case we've got quite a
long<00:07:59.919><c> think</c><00:08:00.240><c> tag</c><00:08:00.879><c> going</c><00:08:01.039><c> on</c><00:08:01.280><c> there</c><00:08:01.840><c> and</c><00:08:02.080><c> then</c>

00:08:02.230 --> 00:08:02.240 align:start position:0%
long think tag going on there and then
 

00:08:02.240 --> 00:08:04.390 align:start position:0%
long think tag going on there and then
we've<00:08:02.400><c> got</c><00:08:02.560><c> the</c><00:08:02.800><c> answer</c><00:08:03.199><c> coming</c><00:08:03.440><c> out.</c><00:08:04.000><c> If</c><00:08:04.160><c> we</c>

00:08:04.390 --> 00:08:04.400 align:start position:0%
we've got the answer coming out. If we
 

00:08:04.400 --> 00:08:07.430 align:start position:0%
we've got the answer coming out. If we
want<00:08:04.560><c> to</c><00:08:04.800><c> get</c><00:08:05.039><c> it</c><00:08:05.280><c> with</c><00:08:05.599><c> no</c><00:08:06.000><c> reasoning</c><00:08:07.039><c> in</c><00:08:07.280><c> the</c>

00:08:07.430 --> 00:08:07.440 align:start position:0%
want to get it with no reasoning in the
 

00:08:07.440 --> 00:08:08.869 align:start position:0%
want to get it with no reasoning in the
system<00:08:07.680><c> prompt,</c><00:08:07.919><c> we</c><00:08:08.080><c> just</c><00:08:08.240><c> need</c><00:08:08.319><c> to</c><00:08:08.479><c> pass</c><00:08:08.639><c> in</c>

00:08:08.869 --> 00:08:08.879 align:start position:0%
system prompt, we just need to pass in
 

00:08:08.879 --> 00:08:12.070 align:start position:0%
system prompt, we just need to pass in
this<00:08:09.120><c> slashn</c><00:08:09.680><c> nothink</c><00:08:10.800><c> in</c><00:08:11.039><c> there.</c><00:08:11.759><c> And</c><00:08:11.919><c> then</c>

00:08:12.070 --> 00:08:12.080 align:start position:0%
this slashn nothink in there. And then
 

00:08:12.080 --> 00:08:14.070 align:start position:0%
this slashn nothink in there. And then
you'll<00:08:12.319><c> see</c><00:08:12.400><c> that</c><00:08:12.639><c> we</c><00:08:12.800><c> just</c><00:08:13.039><c> get</c><00:08:13.440><c> an</c><00:08:13.759><c> answer</c>

00:08:14.070 --> 00:08:14.080 align:start position:0%
you'll see that we just get an answer
 

00:08:14.080 --> 00:08:16.550 align:start position:0%
you'll see that we just get an answer
out<00:08:14.400><c> with</c><00:08:14.639><c> no</c><00:08:14.960><c> thinking</c><00:08:15.360><c> tags,</c><00:08:16.080><c> just</c><00:08:16.319><c> the</c>

00:08:16.550 --> 00:08:16.560 align:start position:0%
out with no thinking tags, just the
 

00:08:16.560 --> 00:08:19.189 align:start position:0%
out with no thinking tags, just the
final<00:08:16.879><c> answer</c><00:08:17.280><c> out</c><00:08:17.599><c> there.</c><00:08:18.560><c> All</c><00:08:18.639><c> right.</c><00:08:19.039><c> So,</c>

00:08:19.189 --> 00:08:19.199 align:start position:0%
final answer out there. All right. So,
 

00:08:19.199 --> 00:08:21.830 align:start position:0%
final answer out there. All right. So,
you<00:08:19.520><c> can</c><00:08:19.840><c> manipulate</c><00:08:20.479><c> the</c><00:08:20.800><c> thinking</c><00:08:21.280><c> with</c><00:08:21.599><c> the</c>

00:08:21.830 --> 00:08:21.840 align:start position:0%
you can manipulate the thinking with the
 

00:08:21.840 --> 00:08:24.950 align:start position:0%
you can manipulate the thinking with the
system<00:08:22.240><c> prompt</c><00:08:22.720><c> a</c><00:08:23.039><c> bit</c><00:08:23.199><c> by</c><00:08:23.520><c> referring</c><00:08:24.080><c> to</c><00:08:24.319><c> it.</c>

00:08:24.950 --> 00:08:24.960 align:start position:0%
system prompt a bit by referring to it.
 

00:08:24.960 --> 00:08:27.189 align:start position:0%
system prompt a bit by referring to it.
And<00:08:25.120><c> the</c><00:08:25.360><c> thinking</c><00:08:25.919><c> will</c><00:08:26.240><c> often</c><00:08:26.639><c> be</c><00:08:26.879><c> very</c>

00:08:27.189 --> 00:08:27.199 align:start position:0%
And the thinking will often be very
 

00:08:27.199 --> 00:08:29.670 align:start position:0%
And the thinking will often be very
long.<00:08:27.919><c> Now,</c><00:08:28.319><c> unfortunately,</c><00:08:28.879><c> I</c><00:08:29.120><c> wasn't</c><00:08:29.440><c> able</c>

00:08:29.670 --> 00:08:29.680 align:start position:0%
long. Now, unfortunately, I wasn't able
 

00:08:29.680 --> 00:08:31.990 align:start position:0%
long. Now, unfortunately, I wasn't able
to<00:08:29.840><c> get</c><00:08:30.080><c> it</c><00:08:30.319><c> to</c><00:08:30.560><c> number</c><00:08:30.879><c> the</c><00:08:31.199><c> thinking</c><00:08:31.599><c> and</c>

00:08:31.990 --> 00:08:32.000 align:start position:0%
to get it to number the thinking and
 

00:08:32.000 --> 00:08:34.389 align:start position:0%
to get it to number the thinking and
break<00:08:32.240><c> it</c><00:08:32.399><c> down</c><00:08:32.560><c> into</c><00:08:32.959><c> sections,</c><00:08:34.080><c> which</c><00:08:34.320><c> is</c>

00:08:34.389 --> 00:08:34.399 align:start position:0%
break it down into sections, which is
 

00:08:34.399 --> 00:08:36.870 align:start position:0%
break it down into sections, which is
the<00:08:34.640><c> same</c><00:08:34.800><c> for</c><00:08:35.039><c> the</c><00:08:35.200><c> Deep</c><00:08:35.519><c> Seek</c><00:08:35.839><c> ones.</c><00:08:36.640><c> With</c>

00:08:36.870 --> 00:08:36.880 align:start position:0%
the same for the Deep Seek ones. With
 

00:08:36.880 --> 00:08:39.029 align:start position:0%
the same for the Deep Seek ones. With
the<00:08:37.039><c> way</c><00:08:37.200><c> that</c><00:08:37.440><c> they</c><00:08:37.760><c> train</c><00:08:38.159><c> that</c><00:08:38.479><c> model</c><00:08:38.880><c> and</c>

00:08:39.029 --> 00:08:39.039 align:start position:0%
the way that they train that model and
 

00:08:39.039 --> 00:08:41.829 align:start position:0%
the way that they train that model and
the<00:08:39.279><c> Quen</c><00:08:39.760><c> 3</c><00:08:40.080><c> model,</c><00:08:40.880><c> they're</c><00:08:41.200><c> really</c><00:08:41.440><c> using</c>

00:08:41.829 --> 00:08:41.839 align:start position:0%
the Quen 3 model, they're really using
 

00:08:41.839 --> 00:08:46.310 align:start position:0%
the Quen 3 model, they're really using
this<00:08:42.240><c> RL</c><00:08:43.279><c> VR</c><00:08:43.839><c> by</c><00:08:44.240><c> Verifiable</c><00:08:44.959><c> Rewards,</c><00:08:45.839><c> which</c>

00:08:46.310 --> 00:08:46.320 align:start position:0%
this RL VR by Verifiable Rewards, which
 

00:08:46.320 --> 00:08:48.630 align:start position:0%
this RL VR by Verifiable Rewards, which
doesn't<00:08:46.640><c> give</c><00:08:46.720><c> you</c><00:08:46.880><c> a</c><00:08:47.120><c> lot</c><00:08:47.200><c> of</c><00:08:47.600><c> control</c><00:08:48.240><c> over</c>

00:08:48.630 --> 00:08:48.640 align:start position:0%
doesn't give you a lot of control over
 

00:08:48.640 --> 00:08:50.949 align:start position:0%
doesn't give you a lot of control over
that<00:08:49.040><c> long</c><00:08:49.440><c> chain</c><00:08:49.600><c> of</c><00:08:49.839><c> thought</c><00:08:50.080><c> to</c><00:08:50.320><c> get</c><00:08:50.560><c> to</c><00:08:50.720><c> the</c>

00:08:50.949 --> 00:08:50.959 align:start position:0%
that long chain of thought to get to the
 

00:08:50.959 --> 00:08:53.269 align:start position:0%
that long chain of thought to get to the
right<00:08:51.279><c> answer.</c><00:08:52.160><c> You</c><00:08:52.399><c> see</c><00:08:52.640><c> this</c><00:08:52.880><c> being</c>

00:08:53.269 --> 00:08:53.279 align:start position:0%
right answer. You see this being
 

00:08:53.279 --> 00:08:55.509 align:start position:0%
right answer. You see this being
actually<00:08:53.680><c> different</c><00:08:54.000><c> in</c><00:08:54.240><c> the</c><00:08:54.399><c> Gemini</c><00:08:54.959><c> models</c>

00:08:55.509 --> 00:08:55.519 align:start position:0%
actually different in the Gemini models
 

00:08:55.519 --> 00:08:57.509 align:start position:0%
actually different in the Gemini models
and<00:08:55.760><c> some</c><00:08:56.000><c> of</c><00:08:56.080><c> the</c><00:08:56.240><c> other</c><00:08:56.399><c> proprietary</c>

00:08:57.509 --> 00:08:57.519 align:start position:0%
and some of the other proprietary
 

00:08:57.519 --> 00:08:59.829 align:start position:0%
and some of the other proprietary
reasoning<00:08:57.920><c> models</c><00:08:58.320><c> out</c><00:08:58.560><c> there</c><00:08:59.120><c> which</c><00:08:59.440><c> kind</c><00:08:59.680><c> of</c>

00:08:59.829 --> 00:08:59.839 align:start position:0%
reasoning models out there which kind of
 

00:08:59.839 --> 00:09:02.630 align:start position:0%
reasoning models out there which kind of
sectionize<00:09:00.800><c> their</c><00:09:01.120><c> thinking</c><00:09:01.760><c> tokens</c><00:09:02.160><c> as</c><00:09:02.399><c> they</c>

00:09:02.630 --> 00:09:02.640 align:start position:0%
sectionize their thinking tokens as they
 

00:09:02.640 --> 00:09:04.790 align:start position:0%
sectionize their thinking tokens as they
go<00:09:02.800><c> through.</c><00:09:03.519><c> Anyway,</c><00:09:03.920><c> here</c><00:09:04.160><c> we</c><00:09:04.399><c> can</c><00:09:04.480><c> see</c><00:09:04.640><c> that</c>

00:09:04.790 --> 00:09:04.800 align:start position:0%
go through. Anyway, here we can see that
 

00:09:04.800 --> 00:09:07.350 align:start position:0%
go through. Anyway, here we can see that
okay<00:09:05.120><c> it</c><00:09:05.440><c> did</c><00:09:05.600><c> a</c><00:09:05.760><c> nice</c><00:09:06.000><c> long</c><00:09:06.320><c> think</c><00:09:07.040><c> and</c><00:09:07.200><c> then</c>

00:09:07.350 --> 00:09:07.360 align:start position:0%
okay it did a nice long think and then
 

00:09:07.360 --> 00:09:10.550 align:start position:0%
okay it did a nice long think and then
it<00:09:07.519><c> gave</c><00:09:07.680><c> us</c><00:09:07.839><c> quite</c><00:09:08.080><c> a</c><00:09:08.240><c> detailed</c><00:09:09.040><c> response</c><00:09:09.760><c> out</c>

00:09:10.550 --> 00:09:10.560 align:start position:0%
it gave us quite a detailed response out
 

00:09:10.560 --> 00:09:14.150 align:start position:0%
it gave us quite a detailed response out
here.<00:09:11.600><c> Now</c><00:09:11.920><c> if</c><00:09:12.160><c> we</c><00:09:12.399><c> look</c><00:09:12.560><c> at</c><00:09:13.120><c> sometimes</c><00:09:13.839><c> like</c>

00:09:14.150 --> 00:09:14.160 align:start position:0%
here. Now if we look at sometimes like
 

00:09:14.160 --> 00:09:17.269 align:start position:0%
here. Now if we look at sometimes like
this<00:09:14.560><c> so</c><00:09:14.720><c> this</c><00:09:14.880><c> is</c><00:09:15.040><c> a</c><00:09:15.200><c> code</c><00:09:15.519><c> generation</c><00:09:16.160><c> one</c><00:09:17.120><c> it</c>

00:09:17.269 --> 00:09:17.279 align:start position:0%
this so this is a code generation one it
 

00:09:17.279 --> 00:09:19.990 align:start position:0%
this so this is a code generation one it
will<00:09:17.519><c> just</c><00:09:17.680><c> generate</c><00:09:18.240><c> empty</c><00:09:18.640><c> thinking.</c><00:09:19.600><c> So</c><00:09:19.760><c> I</c>

00:09:19.990 --> 00:09:20.000 align:start position:0%
will just generate empty thinking. So I
 

00:09:20.000 --> 00:09:22.870 align:start position:0%
will just generate empty thinking. So I
found<00:09:20.160><c> this</c><00:09:20.320><c> on</c><00:09:20.560><c> a</c><00:09:20.880><c> few</c><00:09:21.600><c> sort</c><00:09:21.839><c> of</c><00:09:22.000><c> examples.</c>

00:09:22.870 --> 00:09:22.880 align:start position:0%
found this on a few sort of examples.
 

00:09:22.880 --> 00:09:25.030 align:start position:0%
found this on a few sort of examples.
I'm<00:09:23.120><c> not</c><00:09:23.279><c> sure,</c><00:09:23.680><c> you</c><00:09:23.839><c> know,</c><00:09:24.000><c> what</c><00:09:24.240><c> they</c>

00:09:25.030 --> 00:09:25.040 align:start position:0%
I'm not sure, you know, what they
 

00:09:25.040 --> 00:09:28.630 align:start position:0%
I'm not sure, you know, what they
expected<00:09:25.680><c> behavior</c><00:09:26.240><c> is</c><00:09:26.959><c> for</c><00:09:27.200><c> this.</c><00:09:28.080><c> Maybe</c><00:09:28.399><c> if</c>

00:09:28.630 --> 00:09:28.640 align:start position:0%
expected behavior is for this. Maybe if
 

00:09:28.640 --> 00:09:30.949 align:start position:0%
expected behavior is for this. Maybe if
we<00:09:28.800><c> had</c><00:09:28.959><c> asked</c><00:09:29.600><c> some</c><00:09:29.839><c> things</c><00:09:30.240><c> before</c><00:09:30.640><c> we</c>

00:09:30.949 --> 00:09:30.959 align:start position:0%
we had asked some things before we
 

00:09:30.959 --> 00:09:33.350 align:start position:0%
we had asked some things before we
actually<00:09:31.360><c> sent</c><00:09:31.600><c> in</c><00:09:31.839><c> the</c><00:09:32.080><c> Python</c><00:09:32.959><c> function</c>

00:09:33.350 --> 00:09:33.360 align:start position:0%
actually sent in the Python function
 

00:09:33.360 --> 00:09:35.430 align:start position:0%
actually sent in the Python function
definition,<00:09:34.480><c> we</c><00:09:34.720><c> would</c><00:09:34.800><c> have</c><00:09:34.959><c> got</c><00:09:35.040><c> a</c><00:09:35.200><c> better</c>

00:09:35.430 --> 00:09:35.440 align:start position:0%
definition, we would have got a better
 

00:09:35.440 --> 00:09:38.230 align:start position:0%
definition, we would have got a better
response<00:09:35.920><c> out.</c><00:09:36.640><c> So</c><00:09:36.880><c> I</c><00:09:37.040><c> do</c><00:09:37.200><c> notice</c><00:09:37.519><c> this</c><00:09:37.839><c> for</c><00:09:38.000><c> a</c>

00:09:38.230 --> 00:09:38.240 align:start position:0%
response out. So I do notice this for a
 

00:09:38.240 --> 00:09:40.310 align:start position:0%
response out. So I do notice this for a
number<00:09:38.399><c> of</c><00:09:38.640><c> responses</c><00:09:39.360><c> where</c><00:09:39.680><c> you</c><00:09:39.920><c> will</c><00:09:40.080><c> get</c>

00:09:40.310 --> 00:09:40.320 align:start position:0%
number of responses where you will get
 

00:09:40.320 --> 00:09:43.190 align:start position:0%
number of responses where you will get
empty<00:09:40.720><c> thinking</c><00:09:41.279><c> out</c><00:09:41.600><c> there.</c><00:09:42.399><c> I</c><00:09:42.640><c> got</c><00:09:42.800><c> to</c><00:09:42.959><c> say</c>

00:09:43.190 --> 00:09:43.200 align:start position:0%
empty thinking out there. I got to say
 

00:09:43.200 --> 00:09:45.670 align:start position:0%
empty thinking out there. I got to say
though,<00:09:43.600><c> for</c><00:09:44.080><c> such</c><00:09:44.320><c> a</c><00:09:44.560><c> small</c><00:09:44.880><c> model,</c><00:09:45.360><c> the</c>

00:09:45.670 --> 00:09:45.680 align:start position:0%
though, for such a small model, the
 

00:09:45.680 --> 00:09:48.949 align:start position:0%
though, for such a small model, the
thinking<00:09:46.240><c> turns</c><00:09:46.480><c> out</c><00:09:46.720><c> to</c><00:09:46.880><c> be</c><00:09:47.120><c> quite</c><00:09:47.600><c> good.</c><00:09:48.480><c> I</c>

00:09:48.949 --> 00:09:48.959 align:start position:0%
thinking turns out to be quite good. I
 

00:09:48.959 --> 00:09:50.790 align:start position:0%
thinking turns out to be quite good. I
can't<00:09:49.279><c> help</c><00:09:49.440><c> but</c><00:09:49.680><c> feel</c><00:09:49.839><c> that</c><00:09:50.080><c> perhaps</c><00:09:50.480><c> it's</c>

00:09:50.790 --> 00:09:50.800 align:start position:0%
can't help but feel that perhaps it's
 

00:09:50.800 --> 00:09:53.430 align:start position:0%
can't help but feel that perhaps it's
just<00:09:51.040><c> fine-tuned</c><00:09:51.680><c> in</c><00:09:51.920><c> from</c><00:09:52.320><c> deepseek</c><00:09:53.120><c> and</c>

00:09:53.430 --> 00:09:53.440 align:start position:0%
just fine-tuned in from deepseek and
 

00:09:53.440 --> 00:09:55.990 align:start position:0%
just fine-tuned in from deepseek and
quen<00:09:53.839><c> 3</c><00:09:54.320><c> as</c><00:09:54.560><c> opposed</c><00:09:54.800><c> to</c><00:09:55.279><c> actually</c><00:09:55.680><c> getting</c>

00:09:55.990 --> 00:09:56.000 align:start position:0%
quen 3 as opposed to actually getting
 

00:09:56.000 --> 00:09:58.710 align:start position:0%
quen 3 as opposed to actually getting
the<00:09:56.240><c> model</c><00:09:56.880><c> to</c><00:09:57.120><c> do</c><00:09:57.360><c> thinking</c><00:09:58.000><c> itself.</c><00:09:58.560><c> It's</c>

00:09:58.710 --> 00:09:58.720 align:start position:0%
the model to do thinking itself. It's
 

00:09:58.720 --> 00:10:01.350 align:start position:0%
the model to do thinking itself. It's
just<00:09:58.959><c> extended</c><00:09:59.760><c> chain</c><00:10:00.000><c> of</c><00:10:00.160><c> thought.</c><00:10:00.880><c> But</c><00:10:01.120><c> that</c>

00:10:01.350 --> 00:10:01.360 align:start position:0%
just extended chain of thought. But that
 

00:10:01.360 --> 00:10:03.030 align:start position:0%
just extended chain of thought. But that
said,<00:10:01.680><c> we</c><00:10:01.839><c> can</c><00:10:01.920><c> look</c><00:10:02.080><c> at,</c><00:10:02.240><c> you</c><00:10:02.399><c> know,</c><00:10:02.560><c> so</c><00:10:02.800><c> a</c>

00:10:03.030 --> 00:10:03.040 align:start position:0%
said, we can look at, you know, so a
 

00:10:03.040 --> 00:10:04.310 align:start position:0%
said, we can look at, you know, so a
question<00:10:03.200><c> like</c><00:10:03.360><c> this,</c><00:10:03.600><c> write</c><00:10:03.760><c> a</c><00:10:03.920><c> detailed</c>

00:10:04.310 --> 00:10:04.320 align:start position:0%
question like this, write a detailed
 

00:10:04.320 --> 00:10:05.750 align:start position:0%
question like this, write a detailed
breakdown<00:10:04.640><c> of</c><00:10:04.880><c> building</c><00:10:05.120><c> a</c><00:10:05.360><c> successful</c>

00:10:05.750 --> 00:10:05.760 align:start position:0%
breakdown of building a successful
 

00:10:05.760 --> 00:10:07.829 align:start position:0%
breakdown of building a successful
lemonade<00:10:06.240><c> stand.</c><00:10:06.720><c> The</c><00:10:06.959><c> thinking</c><00:10:07.360><c> actually</c><00:10:07.600><c> is</c>

00:10:07.829 --> 00:10:07.839 align:start position:0%
lemonade stand. The thinking actually is
 

00:10:07.839 --> 00:10:09.350 align:start position:0%
lemonade stand. The thinking actually is
quite<00:10:08.000><c> good,</c><00:10:08.320><c> right?</c><00:10:08.560><c> It</c><00:10:08.720><c> goes</c><00:10:08.959><c> through</c><00:10:09.120><c> the</c>

00:10:09.350 --> 00:10:09.360 align:start position:0%
quite good, right? It goes through the
 

00:10:09.360 --> 00:10:11.910 align:start position:0%
quite good, right? It goes through the
different<00:10:10.000><c> elements</c><00:10:10.480><c> before</c><00:10:10.880><c> giving</c><00:10:11.200><c> a</c>

00:10:11.910 --> 00:10:11.920 align:start position:0%
different elements before giving a
 

00:10:11.920 --> 00:10:15.910 align:start position:0%
different elements before giving a
detailed<00:10:12.480><c> plan</c><00:10:13.519><c> out</c><00:10:14.000><c> for</c><00:10:14.240><c> this.</c><00:10:14.640><c> Now</c><00:10:15.440><c> when</c><00:10:15.680><c> we</c>

00:10:15.910 --> 00:10:15.920 align:start position:0%
detailed plan out for this. Now when we
 

00:10:15.920 --> 00:10:18.389 align:start position:0%
detailed plan out for this. Now when we
compare<00:10:16.320><c> this</c><00:10:16.800><c> to</c><00:10:17.040><c> a</c><00:10:17.279><c> version</c><00:10:17.680><c> with</c><00:10:18.000><c> no</c>

00:10:18.389 --> 00:10:18.399 align:start position:0%
compare this to a version with no
 

00:10:18.399 --> 00:10:20.710 align:start position:0%
compare this to a version with no
thinking<00:10:19.440><c> you</c><00:10:19.680><c> can</c><00:10:19.760><c> see</c><00:10:19.920><c> in</c><00:10:20.160><c> this</c><00:10:20.320><c> case</c><00:10:20.480><c> the</c>

00:10:20.710 --> 00:10:20.720 align:start position:0%
thinking you can see in this case the
 

00:10:20.720 --> 00:10:23.750 align:start position:0%
thinking you can see in this case the
plan<00:10:21.040><c> is</c><00:10:21.279><c> much</c><00:10:21.680><c> simpler</c><00:10:22.720><c> definitely</c><00:10:23.200><c> not</c><00:10:23.440><c> as</c>

00:10:23.750 --> 00:10:23.760 align:start position:0%
plan is much simpler definitely not as
 

00:10:23.760 --> 00:10:26.389 align:start position:0%
plan is much simpler definitely not as
wellought<00:10:24.399><c> out</c><00:10:24.640><c> and</c><00:10:24.959><c> stuff</c><00:10:25.120><c> like</c><00:10:25.279><c> that.</c><00:10:25.680><c> So</c><00:10:26.160><c> it</c>

00:10:26.389 --> 00:10:26.399 align:start position:0%
wellought out and stuff like that. So it
 

00:10:26.399 --> 00:10:28.069 align:start position:0%
wellought out and stuff like that. So it
does<00:10:26.560><c> show</c><00:10:26.720><c> that</c><00:10:26.880><c> the</c><00:10:27.120><c> thinking</c><00:10:27.519><c> will</c><00:10:27.839><c> help</c>

00:10:28.069 --> 00:10:28.079 align:start position:0%
does show that the thinking will help
 

00:10:28.079 --> 00:10:31.030 align:start position:0%
does show that the thinking will help
for<00:10:28.640><c> certain</c><00:10:29.040><c> kind</c><00:10:29.200><c> of</c><00:10:29.360><c> responses.</c><00:10:30.480><c> Often</c><00:10:30.800><c> you</c>

00:10:31.030 --> 00:10:31.040 align:start position:0%
for certain kind of responses. Often you
 

00:10:31.040 --> 00:10:33.750 align:start position:0%
for certain kind of responses. Often you
will<00:10:31.200><c> also</c><00:10:31.519><c> get</c><00:10:31.839><c> the</c><00:10:32.079><c> model</c><00:10:32.640><c> wanting</c><00:10:33.120><c> to</c><00:10:33.519><c> give</c>

00:10:33.750 --> 00:10:33.760 align:start position:0%
will also get the model wanting to give
 

00:10:33.760 --> 00:10:36.949 align:start position:0%
will also get the model wanting to give
some<00:10:34.079><c> justification</c><00:10:34.880><c> at</c><00:10:35.120><c> the</c><00:10:35.360><c> end</c><00:10:35.760><c> or</c><00:10:36.079><c> to</c><00:10:36.320><c> give</c>

00:10:36.949 --> 00:10:36.959 align:start position:0%
some justification at the end or to give
 

00:10:36.959 --> 00:10:38.949 align:start position:0%
some justification at the end or to give
so<00:10:37.200><c> in</c><00:10:37.360><c> this</c><00:10:37.519><c> case</c><00:10:37.839><c> additional</c><00:10:38.320><c> tips</c><00:10:38.640><c> at</c><00:10:38.800><c> the</c>

00:10:38.949 --> 00:10:38.959 align:start position:0%
so in this case additional tips at the
 

00:10:38.959 --> 00:10:41.670 align:start position:0%
so in this case additional tips at the
end.<00:10:39.760><c> Okay</c><00:10:40.160><c> another</c><00:10:40.399><c> one</c><00:10:40.800><c> old</c><00:10:41.200><c> question.</c>

00:10:41.670 --> 00:10:41.680 align:start position:0%
end. Okay another one old question.
 

00:10:41.680 --> 00:10:42.710 align:start position:0%
end. Okay another one old question.
What's<00:10:41.920><c> the</c><00:10:42.000><c> difference</c><00:10:42.160><c> between</c><00:10:42.399><c> llama</c>

00:10:42.710 --> 00:10:42.720 align:start position:0%
What's the difference between llama
 

00:10:42.720 --> 00:10:45.269 align:start position:0%
What's the difference between llama
vuna?<00:10:43.760><c> Again,</c><00:10:44.160><c> here</c><00:10:44.399><c> it</c><00:10:44.640><c> just</c><00:10:44.800><c> dropped</c><00:10:45.120><c> out</c>

00:10:45.269 --> 00:10:45.279 align:start position:0%
vuna? Again, here it just dropped out
 

00:10:45.279 --> 00:10:47.990 align:start position:0%
vuna? Again, here it just dropped out
the<00:10:45.519><c> thinking.</c><00:10:45.920><c> I'm</c><00:10:46.160><c> not</c><00:10:46.320><c> sure</c><00:10:46.880><c> why,</c><00:10:47.600><c> but</c><00:10:47.839><c> it</c>

00:10:47.990 --> 00:10:48.000 align:start position:0%
the thinking. I'm not sure why, but it
 

00:10:48.000 --> 00:10:50.230 align:start position:0%
the thinking. I'm not sure why, but it
gave<00:10:48.160><c> us</c><00:10:48.399><c> detailed</c><00:10:48.880><c> a</c><00:10:49.120><c> very</c><00:10:49.360><c> detailed</c><00:10:49.839><c> sort</c><00:10:50.079><c> of</c>

00:10:50.230 --> 00:10:50.240 align:start position:0%
gave us detailed a very detailed sort of
 

00:10:50.240 --> 00:10:52.710 align:start position:0%
gave us detailed a very detailed sort of
response,<00:10:51.360><c> probably</c><00:10:51.760><c> on</c><00:10:52.079><c> par</c><00:10:52.240><c> with</c><00:10:52.480><c> some</c><00:10:52.640><c> of</c>

00:10:52.710 --> 00:10:52.720 align:start position:0%
response, probably on par with some of
 

00:10:52.720 --> 00:10:55.030 align:start position:0%
response, probably on par with some of
the<00:10:52.880><c> models</c><00:10:53.200><c> that</c><00:10:53.360><c> we've</c><00:10:53.600><c> seen</c><00:10:53.920><c> before.</c><00:10:54.720><c> Now,</c>

00:10:55.030 --> 00:10:55.040 align:start position:0%
the models that we've seen before. Now,
 

00:10:55.040 --> 00:10:56.870 align:start position:0%
the models that we've seen before. Now,
one<00:10:55.200><c> area</c><00:10:55.440><c> where</c><00:10:55.600><c> I</c><00:10:55.760><c> thought</c><00:10:55.920><c> it</c><00:10:56.079><c> was</c><00:10:56.160><c> a</c><00:10:56.399><c> bit</c>

00:10:56.870 --> 00:10:56.880 align:start position:0%
one area where I thought it was a bit
 

00:10:56.880 --> 00:10:58.630 align:start position:0%
one area where I thought it was a bit
not<00:10:57.120><c> so</c><00:10:57.360><c> great</c><00:10:57.680><c> here</c><00:10:57.920><c> where</c><00:10:58.079><c> I've</c><00:10:58.320><c> asked</c><00:10:58.480><c> it,</c>

00:10:58.630 --> 00:10:58.640 align:start position:0%
not so great here where I've asked it,
 

00:10:58.640 --> 00:11:00.310 align:start position:0%
not so great here where I've asked it,
what<00:10:58.800><c> is</c><00:10:58.880><c> the</c><00:10:59.040><c> capital</c><00:10:59.279><c> of</c><00:10:59.440><c> England?</c><00:11:00.079><c> I've</c>

00:11:00.310 --> 00:11:00.320 align:start position:0%
what is the capital of England? I've
 

00:11:00.320 --> 00:11:02.550 align:start position:0%
what is the capital of England? I've
left<00:11:00.480><c> the</c><00:11:00.720><c> thinking</c><00:11:00.959><c> on.</c><00:11:01.760><c> We</c><00:11:02.000><c> get</c><00:11:02.160><c> a</c><00:11:02.320><c> huge</c>

00:11:02.550 --> 00:11:02.560 align:start position:0%
left the thinking on. We get a huge
 

00:11:02.560 --> 00:11:04.470 align:start position:0%
left the thinking on. We get a huge
amount<00:11:02.800><c> of</c><00:11:02.959><c> thinking</c><00:11:03.680><c> and</c><00:11:03.839><c> then</c><00:11:04.000><c> we</c><00:11:04.160><c> get</c><00:11:04.320><c> a</c>

00:11:04.470 --> 00:11:04.480 align:start position:0%
amount of thinking and then we get a
 

00:11:04.480 --> 00:11:07.750 align:start position:0%
amount of thinking and then we get a
step-by-step<00:11:05.440><c> breakdown</c><00:11:06.240><c> of</c><00:11:06.560><c> the</c><00:11:06.959><c> answer</c>

00:11:07.750 --> 00:11:07.760 align:start position:0%
step-by-step breakdown of the answer
 

00:11:07.760 --> 00:11:09.350 align:start position:0%
step-by-step breakdown of the answer
like<00:11:08.000><c> the</c><00:11:08.240><c> justification</c><00:11:08.880><c> that</c><00:11:09.040><c> I</c><00:11:09.279><c> was</c>

00:11:09.350 --> 00:11:09.360 align:start position:0%
like the justification that I was
 

00:11:09.360 --> 00:11:11.670 align:start position:0%
like the justification that I was
talking<00:11:09.600><c> about</c><00:11:09.839><c> before.</c><00:11:10.720><c> Another</c><00:11:11.279><c> example</c>

00:11:11.670 --> 00:11:11.680 align:start position:0%
talking about before. Another example
 

00:11:11.680 --> 00:11:13.590 align:start position:0%
talking about before. Another example
here<00:11:11.920><c> of</c><00:11:12.160><c> the</c><00:11:12.320><c> thinking</c><00:11:12.720><c> for</c><00:11:12.959><c> the</c><00:11:13.200><c> Jeffrey</c>

00:11:13.590 --> 00:11:13.600 align:start position:0%
here of the thinking for the Jeffrey
 

00:11:13.600 --> 00:11:15.750 align:start position:0%
here of the thinking for the Jeffrey
Hinton<00:11:14.160><c> question.</c><00:11:15.040><c> Gives</c><00:11:15.279><c> some</c><00:11:15.519><c> nice</c>

00:11:15.750 --> 00:11:15.760 align:start position:0%
Hinton question. Gives some nice
 

00:11:15.760 --> 00:11:18.150 align:start position:0%
Hinton question. Gives some nice
rationale<00:11:16.320><c> at</c><00:11:16.560><c> the</c><00:11:16.720><c> end</c><00:11:17.120><c> for</c><00:11:17.360><c> this.</c><00:11:17.839><c> Does</c>

00:11:18.150 --> 00:11:18.160 align:start position:0%
rationale at the end for this. Does
 

00:11:18.160 --> 00:11:20.790 align:start position:0%
rationale at the end for this. Does
really<00:11:18.320><c> well</c><00:11:18.480><c> on</c><00:11:18.640><c> the</c><00:11:18.800><c> GSM</c><00:11:19.360><c> AK</c><00:11:19.839><c> stuff.</c><00:11:20.320><c> Not</c>

00:11:20.790 --> 00:11:20.800 align:start position:0%
really well on the GSM AK stuff. Not
 

00:11:20.800 --> 00:11:23.350 align:start position:0%
really well on the GSM AK stuff. Not
surprisingly<00:11:21.519><c> now.</c><00:11:22.240><c> It</c><00:11:22.480><c> is</c><00:11:22.560><c> kind</c><00:11:22.720><c> of</c><00:11:22.880><c> amazing</c>

00:11:23.350 --> 00:11:23.360 align:start position:0%
surprisingly now. It is kind of amazing
 

00:11:23.360 --> 00:11:27.110 align:start position:0%
surprisingly now. It is kind of amazing
to<00:11:23.680><c> think</c><00:11:23.839><c> that</c><00:11:24.560><c> a</c><00:11:24.800><c> 3B</c><00:11:25.279><c> model</c><00:11:25.760><c> is</c><00:11:26.399><c> doing</c><00:11:26.880><c> so</c>

00:11:27.110 --> 00:11:27.120 align:start position:0%
to think that a 3B model is doing so
 

00:11:27.120 --> 00:11:29.670 align:start position:0%
to think that a 3B model is doing so
much<00:11:27.360><c> better</c><00:11:27.680><c> on</c><00:11:28.399><c> this</c><00:11:28.640><c> kind</c><00:11:28.880><c> of</c><00:11:29.040><c> stuff</c>

00:11:29.670 --> 00:11:29.680 align:start position:0%
much better on this kind of stuff
 

00:11:29.680 --> 00:11:31.990 align:start position:0%
much better on this kind of stuff
compared<00:11:30.079><c> to</c><00:11:30.240><c> going</c><00:11:30.560><c> back</c><00:11:30.800><c> a</c><00:11:31.040><c> year</c><00:11:31.360><c> 18</c><00:11:31.760><c> months</c>

00:11:31.990 --> 00:11:32.000 align:start position:0%
compared to going back a year 18 months
 

00:11:32.000 --> 00:11:34.790 align:start position:0%
compared to going back a year 18 months
ago<00:11:32.640><c> where</c><00:11:32.800><c> we</c><00:11:33.040><c> were</c><00:11:33.200><c> looking</c><00:11:33.360><c> at</c><00:11:33.680><c> 14B</c><00:11:34.399><c> models</c>

00:11:34.790 --> 00:11:34.800 align:start position:0%
ago where we were looking at 14B models
 

00:11:34.800 --> 00:11:37.350 align:start position:0%
ago where we were looking at 14B models
that<00:11:35.120><c> couldn't</c><00:11:35.519><c> do</c><00:11:35.920><c> a</c><00:11:36.160><c> lot</c><00:11:36.240><c> of</c><00:11:36.320><c> these</c><00:11:36.640><c> things.</c>

00:11:37.350 --> 00:11:37.360 align:start position:0%
that couldn't do a lot of these things.
 

00:11:37.360 --> 00:11:38.949 align:start position:0%
that couldn't do a lot of these things.
All<00:11:37.440><c> right.</c><00:11:37.760><c> Finally,</c><00:11:38.079><c> just</c><00:11:38.240><c> to</c><00:11:38.399><c> finish</c><00:11:38.560><c> up</c>

00:11:38.949 --> 00:11:38.959 align:start position:0%
All right. Finally, just to finish up
 

00:11:38.959 --> 00:11:41.269 align:start position:0%
All right. Finally, just to finish up
the<00:11:39.200><c> tool</c><00:11:39.519><c> use.</c><00:11:39.920><c> So,</c><00:11:40.320><c> one</c><00:11:40.560><c> of</c><00:11:40.720><c> the</c><00:11:40.959><c> things</c><00:11:41.120><c> that</c>

00:11:41.269 --> 00:11:41.279 align:start position:0%
the tool use. So, one of the things that
 

00:11:41.279 --> 00:11:43.269 align:start position:0%
the tool use. So, one of the things that
I<00:11:41.440><c> find</c><00:11:41.680><c> most</c><00:11:42.000><c> interesting</c><00:11:42.320><c> about</c><00:11:42.560><c> this</c><00:11:42.800><c> model</c>

00:11:43.269 --> 00:11:43.279 align:start position:0%
I find most interesting about this model
 

00:11:43.279 --> 00:11:45.990 align:start position:0%
I find most interesting about this model
is<00:11:43.600><c> that</c><00:11:43.839><c> it</c><00:11:44.160><c> can</c><00:11:44.240><c> do</c><00:11:44.480><c> function</c><00:11:44.959><c> calling</c><00:11:45.760><c> and</c>

00:11:45.990 --> 00:11:46.000 align:start position:0%
is that it can do function calling and
 

00:11:46.000 --> 00:11:48.790 align:start position:0%
is that it can do function calling and
that<00:11:46.240><c> it's</c><00:11:46.640><c> supposedly</c><00:11:47.360><c> been</c><00:11:47.920><c> primed</c><00:11:48.320><c> a</c><00:11:48.560><c> bit</c>

00:11:48.790 --> 00:11:48.800 align:start position:0%
that it's supposedly been primed a bit
 

00:11:48.800 --> 00:11:52.069 align:start position:0%
that it's supposedly been primed a bit
for<00:11:49.279><c> agentic</c><00:11:49.920><c> use.</c><00:11:51.040><c> So,</c><00:11:51.200><c> when</c><00:11:51.360><c> we</c><00:11:51.519><c> come</c><00:11:51.600><c> in</c><00:11:51.839><c> and</c>

00:11:52.069 --> 00:11:52.079 align:start position:0%
for agentic use. So, when we come in and
 

00:11:52.079 --> 00:11:53.910 align:start position:0%
for agentic use. So, when we come in and
look<00:11:52.160><c> at</c><00:11:52.240><c> the</c><00:11:52.399><c> tool</c><00:11:52.720><c> use,</c><00:11:53.279><c> we</c><00:11:53.440><c> can</c><00:11:53.600><c> basically</c>

00:11:53.910 --> 00:11:53.920 align:start position:0%
look at the tool use, we can basically
 

00:11:53.920 --> 00:11:56.150 align:start position:0%
look at the tool use, we can basically
just<00:11:54.160><c> define</c><00:11:54.560><c> a</c><00:11:54.800><c> tool.</c><00:11:55.519><c> You</c><00:11:55.680><c> can</c><00:11:55.839><c> see</c><00:11:56.000><c> here</c>

00:11:56.150 --> 00:11:56.160 align:start position:0%
just define a tool. You can see here
 

00:11:56.160 --> 00:11:58.150 align:start position:0%
just define a tool. You can see here
that<00:11:56.320><c> we've</c><00:11:56.640><c> just</c><00:11:56.800><c> defined</c><00:11:57.200><c> this</c><00:11:57.440><c> the</c><00:11:57.760><c> schema</c>

00:11:58.150 --> 00:11:58.160 align:start position:0%
that we've just defined this the schema
 

00:11:58.160 --> 00:12:00.790 align:start position:0%
that we've just defined this the schema
for<00:11:58.399><c> the</c><00:11:58.560><c> tool</c><00:11:59.279><c> in</c><00:11:59.519><c> here.</c><00:11:59.920><c> We</c><00:12:00.160><c> can</c><00:12:00.240><c> then</c><00:12:00.560><c> pass</c>

00:12:00.790 --> 00:12:00.800 align:start position:0%
for the tool in here. We can then pass
 

00:12:00.800 --> 00:12:03.910 align:start position:0%
for the tool in here. We can then pass
in<00:12:01.120><c> a</c><00:12:01.519><c> message</c><00:12:02.480><c> and</c><00:12:02.880><c> sure</c><00:12:03.120><c> enough,</c><00:12:03.519><c> what</c><00:12:03.680><c> it</c>

00:12:03.910 --> 00:12:03.920 align:start position:0%
in a message and sure enough, what it
 

00:12:03.920 --> 00:12:06.230 align:start position:0%
in a message and sure enough, what it
will<00:12:04.079><c> do</c><00:12:04.640><c> is</c><00:12:04.959><c> that</c><00:12:05.200><c> the</c><00:12:05.440><c> message</c><00:12:05.920><c> this</c><00:12:06.079><c> is</c>

00:12:06.230 --> 00:12:06.240 align:start position:0%
will do is that the message this is
 

00:12:06.240 --> 00:12:07.990 align:start position:0%
will do is that the message this is
showing<00:12:06.480><c> all</c><00:12:06.639><c> the</c><00:12:06.800><c> data</c><00:12:07.040><c> that's</c><00:12:07.360><c> coming</c><00:12:07.600><c> out.</c>

00:12:07.990 --> 00:12:08.000 align:start position:0%
showing all the data that's coming out.
 

00:12:08.000 --> 00:12:10.150 align:start position:0%
showing all the data that's coming out.
Sure<00:12:08.399><c> enough,</c><00:12:08.720><c> the</c><00:12:09.040><c> message</c><00:12:09.440><c> will</c><00:12:09.839><c> actually</c>

00:12:10.150 --> 00:12:10.160 align:start position:0%
Sure enough, the message will actually
 

00:12:10.160 --> 00:12:13.350 align:start position:0%
Sure enough, the message will actually
then<00:12:10.560><c> include</c><00:12:11.040><c> a</c><00:12:11.279><c> tool</c><00:12:11.680><c> call</c><00:12:11.920><c> in</c><00:12:12.160><c> it.</c><00:12:12.800><c> So</c><00:12:13.120><c> in</c>

00:12:13.350 --> 00:12:13.360 align:start position:0%
then include a tool call in it. So in
 

00:12:13.360 --> 00:12:14.710 align:start position:0%
then include a tool call in it. So in
this<00:12:13.440><c> case,</c><00:12:13.600><c> the</c><00:12:13.760><c> tool</c><00:12:14.079><c> call</c><00:12:14.240><c> is</c><00:12:14.399><c> the</c><00:12:14.560><c> get</c>

00:12:14.710 --> 00:12:14.720 align:start position:0%
this case, the tool call is the get
 

00:12:14.720 --> 00:12:16.389 align:start position:0%
this case, the tool call is the get
weather<00:12:15.120><c> and</c><00:12:15.279><c> it</c><00:12:15.519><c> worked</c><00:12:15.680><c> out</c><00:12:15.839><c> that</c><00:12:16.079><c> it</c><00:12:16.240><c> was</c>

00:12:16.389 --> 00:12:16.399 align:start position:0%
weather and it worked out that it was
 

00:12:16.399 --> 00:12:18.310 align:start position:0%
weather and it worked out that it was
Copenhagen,<00:12:17.200><c> which</c><00:12:17.360><c> is</c><00:12:17.600><c> what</c><00:12:17.839><c> it</c><00:12:18.079><c> should</c><00:12:18.160><c> have</c>

00:12:18.310 --> 00:12:18.320 align:start position:0%
Copenhagen, which is what it should have
 

00:12:18.320 --> 00:12:20.949 align:start position:0%
Copenhagen, which is what it should have
been<00:12:18.639><c> for</c><00:12:18.880><c> this.</c><00:12:19.600><c> So</c><00:12:19.760><c> I</c><00:12:19.920><c> tried</c><00:12:20.160><c> this</c><00:12:20.320><c> out</c><00:12:20.560><c> for</c><00:12:20.800><c> a</c>

00:12:20.949 --> 00:12:20.959 align:start position:0%
been for this. So I tried this out for a
 

00:12:20.959 --> 00:12:23.269 align:start position:0%
been for this. So I tried this out for a
number<00:12:21.120><c> of</c><00:12:21.360><c> different</c><00:12:22.079><c> functions.</c><00:12:22.720><c> So</c><00:12:23.040><c> doing</c>

00:12:23.269 --> 00:12:23.279 align:start position:0%
number of different functions. So doing
 

00:12:23.279 --> 00:12:25.750 align:start position:0%
number of different functions. So doing
like<00:12:23.519><c> a</c><00:12:23.760><c> search</c><00:12:24.320><c> thing</c><00:12:24.880><c> where</c><00:12:25.200><c> we</c><00:12:25.360><c> can</c><00:12:25.519><c> say,</c>

00:12:25.750 --> 00:12:25.760 align:start position:0%
like a search thing where we can say,
 

00:12:25.760 --> 00:12:27.509 align:start position:0%
like a search thing where we can say,
okay,<00:12:26.000><c> when</c><00:12:26.160><c> will</c><00:12:26.320><c> open</c><00:12:26.880><c> release</c><00:12:27.120><c> the</c><00:12:27.279><c> open</c>

00:12:27.509 --> 00:12:27.519 align:start position:0%
okay, when will open release the open
 

00:12:27.519 --> 00:12:29.750 align:start position:0%
okay, when will open release the open
weights<00:12:27.839><c> model?</c><00:12:28.240><c> What</c><00:12:28.399><c> are</c><00:12:28.480><c> the</c><00:12:28.639><c> rumors?</c><00:12:29.600><c> And</c>

00:12:29.750 --> 00:12:29.760 align:start position:0%
weights model? What are the rumors? And
 

00:12:29.760 --> 00:12:31.350 align:start position:0%
weights model? What are the rumors? And
we<00:12:29.920><c> can</c><00:12:30.079><c> see,</c><00:12:30.240><c> sure</c><00:12:30.399><c> enough,</c><00:12:30.720><c> we're</c><00:12:30.880><c> taking</c><00:12:31.120><c> in</c>

00:12:31.350 --> 00:12:31.360 align:start position:0%
we can see, sure enough, we're taking in
 

00:12:31.360 --> 00:12:34.470 align:start position:0%
we can see, sure enough, we're taking in
that<00:12:31.519><c> one</c><00:12:31.760><c> tool</c><00:12:32.160><c> there</c><00:12:33.279><c> and</c><00:12:33.839><c> it's</c><00:12:34.160><c> got</c><00:12:34.320><c> some</c>

00:12:34.470 --> 00:12:34.480 align:start position:0%
that one tool there and it's got some
 

00:12:34.480 --> 00:12:36.550 align:start position:0%
that one tool there and it's got some
instructions<00:12:35.040><c> of</c><00:12:35.279><c> how</c><00:12:35.440><c> to</c><00:12:35.600><c> do</c><00:12:35.760><c> it.</c><00:12:36.240><c> And</c><00:12:36.480><c> sure</c>

00:12:36.550 --> 00:12:36.560 align:start position:0%
instructions of how to do it. And sure
 

00:12:36.560 --> 00:12:38.550 align:start position:0%
instructions of how to do it. And sure
enough,<00:12:36.800><c> it</c><00:12:36.959><c> returns</c><00:12:37.279><c> back</c><00:12:37.440><c> a</c><00:12:37.600><c> tool</c><00:12:37.920><c> call.</c><00:12:38.399><c> So</c>

00:12:38.550 --> 00:12:38.560 align:start position:0%
enough, it returns back a tool call. So
 

00:12:38.560 --> 00:12:40.550 align:start position:0%
enough, it returns back a tool call. So
the<00:12:38.720><c> tool</c><00:12:39.040><c> call</c><00:12:39.279><c> in</c><00:12:39.440><c> this</c><00:12:39.600><c> case</c><00:12:39.760><c> is</c><00:12:40.079><c> the</c><00:12:40.240><c> search</c>

00:12:40.550 --> 00:12:40.560 align:start position:0%
the tool call in this case is the search
 

00:12:40.560 --> 00:12:42.470 align:start position:0%
the tool call in this case is the search
web<00:12:40.959><c> and</c><00:12:41.200><c> the</c><00:12:41.360><c> arguments</c><00:12:41.839><c> it's</c><00:12:42.079><c> come</c><00:12:42.240><c> up</c><00:12:42.320><c> with</c>

00:12:42.470 --> 00:12:42.480 align:start position:0%
web and the arguments it's come up with
 

00:12:42.480 --> 00:12:45.110 align:start position:0%
web and the arguments it's come up with
is<00:12:42.800><c> open</c><00:12:43.120><c> AI</c><00:12:43.600><c> open</c><00:12:44.000><c> weights</c><00:12:44.399><c> model</c><00:12:44.720><c> release</c>

00:12:45.110 --> 00:12:45.120 align:start position:0%
is open AI open weights model release
 

00:12:45.120 --> 00:12:48.389 align:start position:0%
is open AI open weights model release
date<00:12:45.440><c> rumors.</c><00:12:46.560><c> So</c><00:12:47.040><c> it</c><00:12:47.360><c> actually</c><00:12:47.760><c> is</c><00:12:48.079><c> working</c>

00:12:48.389 --> 00:12:48.399 align:start position:0%
date rumors. So it actually is working
 

00:12:48.399 --> 00:12:50.629 align:start position:0%
date rumors. So it actually is working
out<00:12:48.639><c> what</c><00:12:49.040><c> the</c><00:12:49.200><c> keywords.</c><00:12:49.920><c> So</c><00:12:50.079><c> I</c><00:12:50.240><c> tried</c><00:12:50.480><c> this</c>

00:12:50.629 --> 00:12:50.639 align:start position:0%
out what the keywords. So I tried this
 

00:12:50.639 --> 00:12:52.470 align:start position:0%
out what the keywords. So I tried this
for<00:12:50.800><c> a</c><00:12:51.040><c> few</c><00:12:51.200><c> different</c><00:12:51.440><c> things</c><00:12:52.079><c> for</c><00:12:52.320><c> things</c>

00:12:52.470 --> 00:12:52.480 align:start position:0%
for a few different things for things
 

00:12:52.480 --> 00:12:54.470 align:start position:0%
for a few different things for things
like<00:12:52.800><c> okay</c><00:12:53.040><c> who</c><00:12:53.279><c> won</c><00:12:53.440><c> the</c><00:12:53.600><c> Nobel</c><00:12:53.920><c> Prize</c><00:12:54.240><c> in</c>

00:12:54.470 --> 00:12:54.480 align:start position:0%
like okay who won the Nobel Prize in
 

00:12:54.480 --> 00:12:56.389 align:start position:0%
like okay who won the Nobel Prize in
chemistry.<00:12:55.279><c> Now</c><00:12:55.519><c> technically</c><00:12:56.000><c> if</c><00:12:56.160><c> it's</c>

00:12:56.389 --> 00:12:56.399 align:start position:0%
chemistry. Now technically if it's
 

00:12:56.399 --> 00:12:59.430 align:start position:0%
chemistry. Now technically if it's
cutoff<00:12:56.800><c> date</c><00:12:57.040><c> is</c><00:12:57.360><c> June</c><00:12:57.760><c> 2025</c><00:12:58.880><c> it</c><00:12:59.120><c> should</c>

00:12:59.430 --> 00:12:59.440 align:start position:0%
cutoff date is June 2025 it should
 

00:12:59.440 --> 00:13:01.670 align:start position:0%
cutoff date is June 2025 it should
actually<00:12:59.839><c> know</c><00:13:00.079><c> that.</c><00:13:00.639><c> But</c><00:13:00.880><c> in</c><00:13:01.120><c> this</c><00:13:01.200><c> case,</c><00:13:01.440><c> it</c>

00:13:01.670 --> 00:13:01.680 align:start position:0%
actually know that. But in this case, it
 

00:13:01.680 --> 00:13:04.550 align:start position:0%
actually know that. But in this case, it
decided,<00:13:02.240><c> okay,</c><00:13:02.800><c> let's</c><00:13:03.040><c> do</c><00:13:03.200><c> a</c><00:13:03.440><c> search.</c><00:13:04.079><c> 24</c>

00:13:04.550 --> 00:13:04.560 align:start position:0%
decided, okay, let's do a search. 24
 

00:13:04.560 --> 00:13:06.790 align:start position:0%
decided, okay, let's do a search. 24
Nobel<00:13:05.040><c> Prize</c><00:13:05.440><c> chemistry</c><00:13:05.839><c> winner.</c><00:13:06.399><c> I</c><00:13:06.639><c> actually</c>

00:13:06.790 --> 00:13:06.800 align:start position:0%
Nobel Prize chemistry winner. I actually
 

00:13:06.800 --> 00:13:09.030 align:start position:0%
Nobel Prize chemistry winner. I actually
think<00:13:06.959><c> that's</c><00:13:07.200><c> good,</c><00:13:07.519><c> right?</c><00:13:07.760><c> That</c><00:13:08.399><c> I</c><00:13:08.720><c> don't</c>

00:13:09.030 --> 00:13:09.040 align:start position:0%
think that's good, right? That I don't
 

00:13:09.040 --> 00:13:11.350 align:start position:0%
think that's good, right? That I don't
expect<00:13:09.360><c> a</c><00:13:09.600><c> 3B</c><00:13:10.079><c> model</c><00:13:10.399><c> to</c><00:13:10.560><c> be</c><00:13:10.720><c> really</c><00:13:10.959><c> good</c><00:13:11.120><c> with</c>

00:13:11.350 --> 00:13:11.360 align:start position:0%
expect a 3B model to be really good with
 

00:13:11.360 --> 00:13:13.430 align:start position:0%
expect a 3B model to be really good with
facts.<00:13:11.760><c> I</c><00:13:12.000><c> feel</c><00:13:12.160><c> it's</c><00:13:12.399><c> like</c><00:13:12.560><c> better</c><00:13:12.800><c> for</c><00:13:13.040><c> it</c><00:13:13.200><c> to</c>

00:13:13.430 --> 00:13:13.440 align:start position:0%
facts. I feel it's like better for it to
 

00:13:13.440 --> 00:13:15.269 align:start position:0%
facts. I feel it's like better for it to
actually<00:13:13.760><c> try</c><00:13:14.000><c> things</c><00:13:14.320><c> out</c><00:13:14.639><c> there.</c><00:13:15.279><c> All</c>

00:13:15.269 --> 00:13:15.279 align:start position:0%
actually try things out there. All
 

00:13:15.279 --> 00:13:17.110 align:start position:0%
actually try things out there. All
right.<00:13:15.519><c> And</c><00:13:15.760><c> then</c><00:13:16.000><c> finally,</c><00:13:16.639><c> if</c><00:13:16.880><c> we're</c>

00:13:17.110 --> 00:13:17.120 align:start position:0%
right. And then finally, if we're
 

00:13:17.120 --> 00:13:19.509 align:start position:0%
right. And then finally, if we're
passing<00:13:17.440><c> in</c><00:13:17.680><c> multiple</c><00:13:18.079><c> tools</c><00:13:18.959><c> here,</c><00:13:19.200><c> I've</c><00:13:19.440><c> got</c>

00:13:19.509 --> 00:13:19.519 align:start position:0%
passing in multiple tools here, I've got
 

00:13:19.519 --> 00:13:21.829 align:start position:0%
passing in multiple tools here, I've got
the<00:13:19.680><c> search</c><00:13:20.000><c> web</c><00:13:20.240><c> and</c><00:13:20.480><c> the</c><00:13:20.639><c> weather,</c><00:13:21.440><c> and</c><00:13:21.680><c> we</c>

00:13:21.829 --> 00:13:21.839 align:start position:0%
the search web and the weather, and we
 

00:13:21.839 --> 00:13:23.190 align:start position:0%
the search web and the weather, and we
ask<00:13:22.079><c> it</c><00:13:22.320><c> something</c><00:13:22.480><c> that</c><00:13:22.720><c> really</c><00:13:22.880><c> shouldn't</c>

00:13:23.190 --> 00:13:23.200 align:start position:0%
ask it something that really shouldn't
 

00:13:23.200 --> 00:13:25.910 align:start position:0%
ask it something that really shouldn't
use<00:13:23.519><c> either</c><00:13:23.839><c> tool,</c><00:13:24.399><c> we</c><00:13:24.639><c> want</c><00:13:24.800><c> it</c><00:13:25.040><c> to</c><00:13:25.200><c> not</c><00:13:25.519><c> use</c><00:13:25.680><c> a</c>

00:13:25.910 --> 00:13:25.920 align:start position:0%
use either tool, we want it to not use a
 

00:13:25.920 --> 00:13:27.350 align:start position:0%
use either tool, we want it to not use a
tool,<00:13:26.240><c> right?</c><00:13:26.399><c> We</c><00:13:26.560><c> want</c><00:13:26.639><c> it</c><00:13:26.800><c> to</c><00:13:26.959><c> know</c><00:13:27.120><c> that,</c>

00:13:27.350 --> 00:13:27.360 align:start position:0%
tool, right? We want it to know that,
 

00:13:27.360 --> 00:13:29.990 align:start position:0%
tool, right? We want it to know that,
okay,<00:13:27.600><c> it</c><00:13:27.760><c> should</c><00:13:27.920><c> just</c><00:13:28.160><c> respond</c><00:13:28.959><c> to</c><00:13:29.200><c> this.</c>

00:13:29.990 --> 00:13:30.000 align:start position:0%
okay, it should just respond to this.
 

00:13:30.000 --> 00:13:32.389 align:start position:0%
okay, it should just respond to this.
Now,<00:13:30.480><c> I</c><00:13:30.639><c> had</c><00:13:30.880><c> mixed</c><00:13:31.279><c> responses</c><00:13:31.760><c> with</c><00:13:32.000><c> this.</c>

00:13:32.389 --> 00:13:32.399 align:start position:0%
Now, I had mixed responses with this.
 

00:13:32.399 --> 00:13:34.949 align:start position:0%
Now, I had mixed responses with this.
Sometimes<00:13:33.360><c> it</c><00:13:33.600><c> works</c><00:13:33.839><c> really</c><00:13:34.160><c> well</c><00:13:34.560><c> as</c><00:13:34.800><c> you</c>

00:13:34.949 --> 00:13:34.959 align:start position:0%
Sometimes it works really well as you
 

00:13:34.959 --> 00:13:36.550 align:start position:0%
Sometimes it works really well as you
see<00:13:35.120><c> here</c><00:13:35.440><c> where,</c><00:13:35.760><c> okay,</c><00:13:36.000><c> we've</c><00:13:36.160><c> passed</c><00:13:36.399><c> in</c>

00:13:36.550 --> 00:13:36.560 align:start position:0%
see here where, okay, we've passed in
 

00:13:36.560 --> 00:13:39.269 align:start position:0%
see here where, okay, we've passed in
the<00:13:36.800><c> tools.</c><00:13:37.680><c> We've</c><00:13:37.920><c> passed</c><00:13:38.240><c> in</c><00:13:38.560><c> something.</c><00:13:39.040><c> We</c>

00:13:39.269 --> 00:13:39.279 align:start position:0%
the tools. We've passed in something. We
 

00:13:39.279 --> 00:13:40.949 align:start position:0%
the tools. We've passed in something. We
didn't<00:13:39.440><c> get</c><00:13:39.519><c> a</c><00:13:39.680><c> cool</c><00:13:39.920><c> call</c><00:13:40.160><c> back.</c><00:13:40.560><c> We</c><00:13:40.800><c> didn't</c>

00:13:40.949 --> 00:13:40.959 align:start position:0%
didn't get a cool call back. We didn't
 

00:13:40.959 --> 00:13:43.430 align:start position:0%
didn't get a cool call back. We didn't
get<00:13:41.120><c> any</c><00:13:41.279><c> thinking.</c><00:13:41.760><c> We</c><00:13:41.920><c> just</c><00:13:42.079><c> got</c><00:13:42.399><c> I'm</c><00:13:42.639><c> an</c><00:13:42.800><c> AI.</c>

00:13:43.430 --> 00:13:43.440 align:start position:0%
get any thinking. We just got I'm an AI.
 

00:13:43.440 --> 00:13:45.030 align:start position:0%
get any thinking. We just got I'm an AI.
I<00:13:43.680><c> don't</c><00:13:43.760><c> have</c><00:13:44.000><c> feelings,</c><00:13:44.399><c> but</c><00:13:44.639><c> I'm</c><00:13:44.800><c> here</c><00:13:44.959><c> to</c>

00:13:45.030 --> 00:13:45.040 align:start position:0%
I don't have feelings, but I'm here to
 

00:13:45.040 --> 00:13:47.430 align:start position:0%
I don't have feelings, but I'm here to
help.<00:13:45.360><c> We</c><00:13:45.519><c> got</c><00:13:45.600><c> a</c><00:13:45.839><c> response</c><00:13:46.240><c> back.</c><00:13:46.959><c> Often,</c><00:13:47.279><c> it</c>

00:13:47.430 --> 00:13:47.440 align:start position:0%
help. We got a response back. Often, it
 

00:13:47.440 --> 00:13:48.710 align:start position:0%
help. We got a response back. Often, it
would<00:13:47.519><c> be</c><00:13:47.680><c> like</c><00:13:47.839><c> this.</c><00:13:48.160><c> Every</c><00:13:48.320><c> now</c><00:13:48.480><c> and</c><00:13:48.560><c> then,</c>

00:13:48.710 --> 00:13:48.720 align:start position:0%
would be like this. Every now and then,
 

00:13:48.720 --> 00:13:51.509 align:start position:0%
would be like this. Every now and then,
I<00:13:48.959><c> found</c><00:13:49.120><c> that,</c><00:13:49.360><c> okay,</c><00:13:49.839><c> it</c><00:13:50.160><c> will</c><00:13:50.880><c> try</c><00:13:51.120><c> to</c><00:13:51.360><c> use</c>

00:13:51.509 --> 00:13:51.519 align:start position:0%
I found that, okay, it will try to use
 

00:13:51.519 --> 00:13:54.150 align:start position:0%
I found that, okay, it will try to use
the<00:13:51.839><c> search</c><00:13:52.160><c> tool</c><00:13:53.040><c> for</c><00:13:53.279><c> things</c><00:13:53.440><c> that</c><00:13:53.760><c> perhaps</c>

00:13:54.150 --> 00:13:54.160 align:start position:0%
the search tool for things that perhaps
 

00:13:54.160 --> 00:13:55.509 align:start position:0%
the search tool for things that perhaps
maybe<00:13:54.399><c> you</c><00:13:54.639><c> don't</c><00:13:54.880><c> want</c><00:13:54.959><c> it</c><00:13:55.120><c> to</c><00:13:55.279><c> use</c><00:13:55.360><c> the</c>

00:13:55.509 --> 00:13:55.519 align:start position:0%
maybe you don't want it to use the
 

00:13:55.519 --> 00:13:57.590 align:start position:0%
maybe you don't want it to use the
search<00:13:55.839><c> tool.</c><00:13:56.000><c> Now,</c><00:13:56.240><c> that</c><00:13:56.399><c> could</c><00:13:56.639><c> also</c><00:13:56.959><c> be</c>

00:13:57.590 --> 00:13:57.600 align:start position:0%
search tool. Now, that could also be
 

00:13:57.600 --> 00:13:59.750 align:start position:0%
search tool. Now, that could also be
down<00:13:57.839><c> to</c><00:13:58.079><c> my</c><00:13:58.320><c> description.</c><00:13:59.120><c> In</c><00:13:59.360><c> here,</c><00:13:59.519><c> I've</c>

00:13:59.750 --> 00:13:59.760 align:start position:0%
down to my description. In here, I've
 

00:13:59.760 --> 00:14:01.350 align:start position:0%
down to my description. In here, I've
got<00:13:59.920><c> allows</c><00:14:00.240><c> you</c><00:14:00.399><c> to</c><00:14:00.560><c> use</c><00:14:00.720><c> the</c><00:14:00.880><c> search</c><00:14:01.199><c> the</c>

00:14:01.350 --> 00:14:01.360 align:start position:0%
got allows you to use the search the
 

00:14:01.360 --> 00:14:02.790 align:start position:0%
got allows you to use the search the
internet,<00:14:01.680><c> find</c><00:14:01.920><c> useful</c><00:14:02.160><c> and</c><00:14:02.320><c> up-to-date</c>

00:14:02.790 --> 00:14:02.800 align:start position:0%
internet, find useful and up-to-date
 

00:14:02.800 --> 00:14:04.949 align:start position:0%
internet, find useful and up-to-date
information.<00:14:03.760><c> You</c><00:14:04.000><c> could</c><00:14:04.320><c> play</c><00:14:04.560><c> around</c><00:14:04.800><c> with</c>

00:14:04.949 --> 00:14:04.959 align:start position:0%
information. You could play around with
 

00:14:04.959 --> 00:14:06.629 align:start position:0%
information. You could play around with
that<00:14:05.199><c> and</c><00:14:05.440><c> I</c><00:14:05.600><c> think</c><00:14:05.680><c> you</c><00:14:05.920><c> certainly</c><00:14:06.240><c> want</c><00:14:06.399><c> to</c>

00:14:06.629 --> 00:14:06.639 align:start position:0%
that and I think you certainly want to
 

00:14:06.639 --> 00:14:09.110 align:start position:0%
that and I think you certainly want to
do<00:14:06.880><c> that</c><00:14:07.519><c> if</c><00:14:07.760><c> you're</c><00:14:08.000><c> planning</c><00:14:08.240><c> on</c><00:14:08.480><c> using</c><00:14:08.800><c> this</c>

00:14:09.110 --> 00:14:09.120 align:start position:0%
do that if you're planning on using this
 

00:14:09.120 --> 00:14:13.590 align:start position:0%
do that if you're planning on using this
thing.<00:14:09.760><c> So,</c><00:14:10.079><c> the</c><00:14:10.320><c> model</c><00:14:10.639><c> is</c><00:14:10.959><c> out</c><00:14:11.440><c> also</c><00:14:12.000><c> on.</c><00:14:13.360><c> I</c>

00:14:13.590 --> 00:14:13.600 align:start position:0%
thing. So, the model is out also on. I
 

00:14:13.600 --> 00:14:15.189 align:start position:0%
thing. So, the model is out also on. I
think<00:14:13.760><c> this</c><00:14:13.920><c> is</c><00:14:14.079><c> a</c><00:14:14.320><c> custom</c><00:14:14.560><c> version</c><00:14:14.880><c> someone's</c>

00:14:15.189 --> 00:14:15.199 align:start position:0%
think this is a custom version someone's
 

00:14:15.199 --> 00:14:16.550 align:start position:0%
think this is a custom version someone's
uploaded,<00:14:15.519><c> but</c><00:14:15.760><c> my</c><00:14:15.839><c> guess</c><00:14:16.000><c> is</c><00:14:16.160><c> we'll</c><00:14:16.320><c> see</c>

00:14:16.550 --> 00:14:16.560 align:start position:0%
uploaded, but my guess is we'll see
 

00:14:16.560 --> 00:14:18.710 align:start position:0%
uploaded, but my guess is we'll see
probably<00:14:16.720><c> an</c><00:14:16.959><c> official</c><00:14:17.360><c> version</c><00:14:18.000><c> up</c><00:14:18.240><c> there</c><00:14:18.480><c> as</c>

00:14:18.710 --> 00:14:18.720 align:start position:0%
probably an official version up there as
 

00:14:18.720 --> 00:14:20.550 align:start position:0%
probably an official version up there as
well.<00:14:19.440><c> And</c><00:14:19.519><c> I'm</c><00:14:19.760><c> sure</c><00:14:19.839><c> it's</c><00:14:20.000><c> just</c><00:14:20.160><c> a</c><00:14:20.320><c> matter</c><00:14:20.399><c> of</c>

00:14:20.550 --> 00:14:20.560 align:start position:0%
well. And I'm sure it's just a matter of
 

00:14:20.560 --> 00:14:22.389 align:start position:0%
well. And I'm sure it's just a matter of
time<00:14:20.639><c> before</c><00:14:20.880><c> you</c><00:14:21.040><c> see</c><00:14:21.120><c> it</c><00:14:21.199><c> on</c><00:14:21.360><c> LM</c><00:14:21.760><c> Studio</c><00:14:22.160><c> as</c>

00:14:22.389 --> 00:14:22.399 align:start position:0%
time before you see it on LM Studio as
 

00:14:22.399 --> 00:14:24.949 align:start position:0%
time before you see it on LM Studio as
well.<00:14:23.199><c> Overall,</c><00:14:24.000><c> this</c><00:14:24.160><c> is</c><00:14:24.399><c> a</c><00:14:24.720><c> really</c>

00:14:24.949 --> 00:14:24.959 align:start position:0%
well. Overall, this is a really
 

00:14:24.959 --> 00:14:27.269 align:start position:0%
well. Overall, this is a really
interesting<00:14:25.519><c> release</c><00:14:25.839><c> from</c><00:14:26.160><c> Hugging</c><00:14:26.639><c> Face,</c>

00:14:27.269 --> 00:14:27.279 align:start position:0%
interesting release from Hugging Face,
 

00:14:27.279 --> 00:14:29.110 align:start position:0%
interesting release from Hugging Face,
and<00:14:27.839><c> I</c><00:14:28.079><c> really</c><00:14:28.399><c> think</c><00:14:28.480><c> they</c><00:14:28.800><c> should</c><00:14:28.959><c> be</c>

00:14:29.110 --> 00:14:29.120 align:start position:0%
and I really think they should be
 

00:14:29.120 --> 00:14:31.350 align:start position:0%
and I really think they should be
commended<00:14:29.519><c> for</c><00:14:29.760><c> this.</c><00:14:30.160><c> They've</c><00:14:30.880><c> not</c><00:14:31.120><c> only</c>

00:14:31.350 --> 00:14:31.360 align:start position:0%
commended for this. They've not only
 

00:14:31.360 --> 00:14:33.269 align:start position:0%
commended for this. They've not only
released<00:14:31.760><c> an</c><00:14:32.000><c> interesting</c><00:14:32.399><c> model,</c><00:14:33.120><c> but</c>

00:14:33.269 --> 00:14:33.279 align:start position:0%
released an interesting model, but
 

00:14:33.279 --> 00:14:35.430 align:start position:0%
released an interesting model, but
they've<00:14:33.680><c> also</c><00:14:34.000><c> released</c><00:14:34.480><c> this</c><00:14:34.800><c> really</c><00:14:35.040><c> nice</c>

00:14:35.430 --> 00:14:35.440 align:start position:0%
they've also released this really nice
 

00:14:35.440 --> 00:14:38.550 align:start position:0%
they've also released this really nice
blueprint<00:14:36.079><c> of</c><00:14:36.399><c> how</c><00:14:37.120><c> they</c><00:14:37.600><c> created</c><00:14:37.920><c> it,</c><00:14:38.320><c> what</c>

00:14:38.550 --> 00:14:38.560 align:start position:0%
blueprint of how they created it, what
 

00:14:38.560 --> 00:14:40.870 align:start position:0%
blueprint of how they created it, what
they<00:14:38.800><c> did</c><00:14:38.959><c> at</c><00:14:39.120><c> each</c><00:14:39.440><c> steps.</c><00:14:40.160><c> My</c><00:14:40.399><c> guess</c><00:14:40.560><c> is</c><00:14:40.720><c> that</c>

00:14:40.870 --> 00:14:40.880 align:start position:0%
they did at each steps. My guess is that
 

00:14:40.880 --> 00:14:42.470 align:start position:0%
they did at each steps. My guess is that
there's<00:14:41.199><c> probably</c><00:14:41.360><c> a</c><00:14:41.600><c> full</c><00:14:41.839><c> paper</c><00:14:42.160><c> coming</c>

00:14:42.470 --> 00:14:42.480 align:start position:0%
there's probably a full paper coming
 

00:14:42.480 --> 00:14:44.470 align:start position:0%
there's probably a full paper coming
with<00:14:42.800><c> maybe</c><00:14:43.040><c> even</c><00:14:43.360><c> more</c><00:14:43.600><c> details,</c><00:14:44.079><c> which</c><00:14:44.320><c> will</c>

00:14:44.470 --> 00:14:44.480 align:start position:0%
with maybe even more details, which will
 

00:14:44.480 --> 00:14:46.949 align:start position:0%
with maybe even more details, which will
be<00:14:44.639><c> great.</c><00:14:45.600><c> Hopefully</c><00:14:46.079><c> they</c><00:14:46.320><c> will</c><00:14:46.639><c> release</c>

00:14:46.949 --> 00:14:46.959 align:start position:0%
be great. Hopefully they will release
 

00:14:46.959 --> 00:14:48.790 align:start position:0%
be great. Hopefully they will release
the<00:14:47.199><c> checkpoints</c><00:14:47.839><c> for</c><00:14:48.079><c> like</c><00:14:48.240><c> each</c><00:14:48.480><c> of</c><00:14:48.639><c> the</c>

00:14:48.790 --> 00:14:48.800 align:start position:0%
the checkpoints for like each of the
 

00:14:48.800 --> 00:14:50.470 align:start position:0%
the checkpoints for like each of the
phases<00:14:49.199><c> and</c><00:14:49.440><c> stuff</c><00:14:49.600><c> like</c><00:14:49.760><c> that.</c><00:14:50.000><c> So</c><00:14:50.240><c> people</c>

00:14:50.470 --> 00:14:50.480 align:start position:0%
phases and stuff like that. So people
 

00:14:50.480 --> 00:14:53.269 align:start position:0%
phases and stuff like that. So people
could<00:14:50.880><c> look</c><00:14:51.120><c> at</c><00:14:51.279><c> testing</c><00:14:51.839><c> different</c><00:14:52.320><c> ideas.</c>

00:14:53.269 --> 00:14:53.279 align:start position:0%
could look at testing different ideas.
 

00:14:53.279 --> 00:14:54.790 align:start position:0%
could look at testing different ideas.
For<00:14:53.360><c> example,</c><00:14:53.760><c> if</c><00:14:53.920><c> you</c><00:14:54.000><c> wanted</c><00:14:54.240><c> to</c><00:14:54.399><c> change</c>

00:14:54.790 --> 00:14:54.800 align:start position:0%
For example, if you wanted to change
 

00:14:54.800 --> 00:14:56.870 align:start position:0%
For example, if you wanted to change
something<00:14:55.199><c> in</c><00:14:55.440><c> the</c><00:14:55.680><c> phase</c><00:14:56.000><c> 2,</c><00:14:56.560><c> you</c><00:14:56.720><c> don't</c><00:14:56.800><c> want</c>

00:14:56.870 --> 00:14:56.880 align:start position:0%
something in the phase 2, you don't want
 

00:14:56.880 --> 00:14:58.550 align:start position:0%
something in the phase 2, you don't want
to<00:14:56.959><c> have</c><00:14:57.120><c> to</c><00:14:57.199><c> do</c><00:14:57.360><c> the</c><00:14:57.519><c> whole</c><00:14:57.680><c> phase</c><00:14:58.000><c> one</c><00:14:58.320><c> by</c>

00:14:58.550 --> 00:14:58.560 align:start position:0%
to have to do the whole phase one by
 

00:14:58.560 --> 00:14:59.829 align:start position:0%
to have to do the whole phase one by
yourself.<00:14:58.959><c> If</c><00:14:59.120><c> you</c><00:14:59.199><c> could</c><00:14:59.360><c> just</c><00:14:59.440><c> take</c><00:14:59.680><c> their</c>

00:14:59.829 --> 00:14:59.839 align:start position:0%
yourself. If you could just take their
 

00:14:59.839 --> 00:15:02.389 align:start position:0%
yourself. If you could just take their
checkpoint<00:15:00.320><c> for</c><00:15:00.560><c> that</c><00:15:01.040><c> and</c><00:15:01.279><c> then</c><00:15:01.600><c> try</c><00:15:01.839><c> out</c><00:15:02.160><c> new</c>

00:15:02.389 --> 00:15:02.399 align:start position:0%
checkpoint for that and then try out new
 

00:15:02.399 --> 00:15:05.110 align:start position:0%
checkpoint for that and then try out new
ideas<00:15:02.959><c> for</c><00:15:03.199><c> the</c><00:15:03.360><c> phase</c><00:15:03.600><c> 2,</c><00:15:03.920><c> phase</c><00:15:04.240><c> 3,</c><00:15:04.720><c> or</c><00:15:04.880><c> for</c>

00:15:05.110 --> 00:15:05.120 align:start position:0%
ideas for the phase 2, phase 3, or for
 

00:15:05.120 --> 00:15:06.710 align:start position:0%
ideas for the phase 2, phase 3, or for
the<00:15:05.279><c> different</c><00:15:05.600><c> sections</c><00:15:06.079><c> of</c><00:15:06.240><c> the</c><00:15:06.399><c> post</c>

00:15:06.710 --> 00:15:06.720 align:start position:0%
the different sections of the post
 

00:15:06.720 --> 00:15:08.470 align:start position:0%
the different sections of the post
training<00:15:07.120><c> stuff</c><00:15:07.440><c> as</c><00:15:07.680><c> well.</c><00:15:07.920><c> There's</c><00:15:08.160><c> quite</c><00:15:08.320><c> a</c>

00:15:08.470 --> 00:15:08.480 align:start position:0%
training stuff as well. There's quite a
 

00:15:08.480 --> 00:15:10.310 align:start position:0%
training stuff as well. There's quite a
lot<00:15:08.560><c> of</c><00:15:08.959><c> tokens</c><00:15:09.360><c> that</c><00:15:09.519><c> they</c><00:15:09.680><c> trained</c><00:15:10.000><c> on</c><00:15:10.160><c> in</c>

00:15:10.310 --> 00:15:10.320 align:start position:0%
lot of tokens that they trained on in
 

00:15:10.320 --> 00:15:12.389 align:start position:0%
lot of tokens that they trained on in
there.<00:15:10.880><c> Anyway,</c><00:15:11.360><c> check</c><00:15:11.519><c> it</c><00:15:11.680><c> out.</c><00:15:12.000><c> Let</c><00:15:12.160><c> me</c><00:15:12.240><c> know</c>

00:15:12.389 --> 00:15:12.399 align:start position:0%
there. Anyway, check it out. Let me know
 

00:15:12.399 --> 00:15:14.870 align:start position:0%
there. Anyway, check it out. Let me know
in<00:15:12.560><c> the</c><00:15:12.720><c> comments.</c><00:15:13.279><c> Are</c><00:15:13.519><c> you</c><00:15:13.839><c> actually</c><00:15:14.320><c> still</c>

00:15:14.870 --> 00:15:14.880 align:start position:0%
in the comments. Are you actually still
 

00:15:14.880 --> 00:15:18.470 align:start position:0%
in the comments. Are you actually still
looking<00:15:15.199><c> to</c><00:15:15.519><c> use</c><00:15:16.480><c> small</c><00:15:16.880><c> local</c><00:15:17.360><c> models</c><00:15:18.160><c> on</c>

00:15:18.470 --> 00:15:18.480 align:start position:0%
looking to use small local models on
 

00:15:18.480 --> 00:15:20.710 align:start position:0%
looking to use small local models on
your<00:15:18.639><c> own</c><00:15:18.880><c> machine</c><00:15:19.440><c> or</c><00:15:19.760><c> are</c><00:15:19.920><c> you</c><00:15:20.160><c> more</c><00:15:20.399><c> moving</c>

00:15:20.710 --> 00:15:20.720 align:start position:0%
your own machine or are you more moving
 

00:15:20.720 --> 00:15:23.269 align:start position:0%
your own machine or are you more moving
to<00:15:20.959><c> the</c><00:15:21.199><c> really</c><00:15:21.519><c> cheap</c><00:15:22.160><c> proprietary</c><00:15:22.880><c> models</c>

00:15:23.269 --> 00:15:23.279 align:start position:0%
to the really cheap proprietary models
 

00:15:23.279 --> 00:15:25.829 align:start position:0%
to the really cheap proprietary models
that<00:15:23.680><c> can</c><00:15:24.079><c> do</c><00:15:24.320><c> all</c><00:15:24.480><c> of</c><00:15:24.639><c> these</c><00:15:24.959><c> things?</c><00:15:25.519><c> This</c><00:15:25.760><c> is</c>

00:15:25.829 --> 00:15:25.839 align:start position:0%
that can do all of these things? This is
 

00:15:25.839 --> 00:15:27.189 align:start position:0%
that can do all of these things? This is
certainly<00:15:26.160><c> one</c><00:15:26.320><c> of</c><00:15:26.399><c> the</c><00:15:26.560><c> things</c><00:15:26.720><c> I'm</c><00:15:27.040><c> looking</c>

00:15:27.189 --> 00:15:27.199 align:start position:0%
certainly one of the things I'm looking
 

00:15:27.199 --> 00:15:29.030 align:start position:0%
certainly one of the things I'm looking
at<00:15:27.360><c> more</c><00:15:27.600><c> and</c><00:15:27.760><c> more</c><00:15:28.240><c> and</c><00:15:28.480><c> talking</c><00:15:28.639><c> to</c><00:15:28.800><c> other</c>

00:15:29.030 --> 00:15:29.040 align:start position:0%
at more and more and talking to other
 

00:15:29.040 --> 00:15:31.590 align:start position:0%
at more and more and talking to other
people,<00:15:29.199><c> I</c><00:15:29.440><c> see</c><00:15:29.760><c> use</c><00:15:30.079><c> cases</c><00:15:30.560><c> for</c><00:15:30.880><c> sort</c><00:15:31.120><c> of</c><00:15:31.199><c> both</c>

00:15:31.590 --> 00:15:31.600 align:start position:0%
people, I see use cases for sort of both
 

00:15:31.600 --> 00:15:33.030 align:start position:0%
people, I see use cases for sort of both
systems<00:15:31.920><c> and</c><00:15:32.160><c> stuff.</c><00:15:32.399><c> So,</c><00:15:32.560><c> I'd</c><00:15:32.720><c> love</c><00:15:32.800><c> to</c><00:15:32.959><c> hear</c>

00:15:33.030 --> 00:15:33.040 align:start position:0%
systems and stuff. So, I'd love to hear
 

00:15:33.040 --> 00:15:34.470 align:start position:0%
systems and stuff. So, I'd love to hear
your<00:15:33.279><c> thoughts</c><00:15:33.600><c> about</c><00:15:33.760><c> that</c><00:15:34.079><c> in</c><00:15:34.320><c> the</c>

00:15:34.470 --> 00:15:34.480 align:start position:0%
your thoughts about that in the
 

00:15:34.480 --> 00:15:36.389 align:start position:0%
your thoughts about that in the
comments.<00:15:35.279><c> Anyway,</c><00:15:35.600><c> as</c><00:15:35.839><c> always,</c><00:15:36.160><c> if</c><00:15:36.320><c> you</c>

00:15:36.389 --> 00:15:36.399 align:start position:0%
comments. Anyway, as always, if you
 

00:15:36.399 --> 00:15:37.750 align:start position:0%
comments. Anyway, as always, if you
found<00:15:36.560><c> the</c><00:15:36.720><c> video</c><00:15:36.959><c> useful,</c><00:15:37.360><c> please</c><00:15:37.519><c> click</c>

00:15:37.750 --> 00:15:37.760 align:start position:0%
found the video useful, please click
 

00:15:37.760 --> 00:15:39.350 align:start position:0%
found the video useful, please click
like<00:15:37.920><c> and</c><00:15:38.160><c> subscribe</c><00:15:38.560><c> and</c><00:15:38.800><c> I</c><00:15:39.040><c> will</c><00:15:39.120><c> talk</c><00:15:39.279><c> to</c>

00:15:39.350 --> 00:15:39.360 align:start position:0%
like and subscribe and I will talk to
 

00:15:39.360 --> 00:15:43.440 align:start position:0%
like and subscribe and I will talk to
you<00:15:39.440><c> in</c><00:15:39.600><c> the</c><00:15:39.760><c> next</c><00:15:39.920><c> video.</c><00:15:40.560><c> Bye</c><00:15:40.880><c> for</c><00:15:41.040><c> now.</c>

