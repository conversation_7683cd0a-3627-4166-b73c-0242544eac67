WEBVTT
Kind: captions
Language: en

00:00:00.730 --> 00:00:05.590 align:start position:0%
 
[Music]

00:00:05.590 --> 00:00:05.600 align:start position:0%
 
 

00:00:05.600 --> 00:00:07.630 align:start position:0%
 
now<00:00:05.799><c> that</c><00:00:05.960><c> we</c><00:00:06.080><c> know</c><00:00:06.319><c> the</c><00:00:06.520><c> issues</c><00:00:07.000><c> in</c><00:00:07.200><c> getting</c>

00:00:07.630 --> 00:00:07.640 align:start position:0%
now that we know the issues in getting
 

00:00:07.640 --> 00:00:09.830 align:start position:0%
now that we know the issues in getting
accurate<00:00:08.160><c> answers</c><00:00:08.920><c> by</c><00:00:09.040><c> simply</c><00:00:09.320><c> calling</c><00:00:09.639><c> an</c>

00:00:09.830 --> 00:00:09.840 align:start position:0%
accurate answers by simply calling an
 

00:00:09.840 --> 00:00:14.110 align:start position:0%
accurate answers by simply calling an
API<00:00:10.800><c> let's</c><00:00:11.080><c> explore</c><00:00:11.440><c> a</c><00:00:11.559><c> more</c><00:00:11.759><c> interesting</c>

00:00:14.110 --> 00:00:14.120 align:start position:0%
API let's explore a more interesting
 

00:00:14.120 --> 00:00:16.590 align:start position:0%
API let's explore a more interesting
solution<00:00:15.120><c> in</c><00:00:15.280><c> the</c><00:00:15.440><c> diagram</c><00:00:15.839><c> of</c><00:00:16.000><c> our</c><00:00:16.199><c> solution</c>

00:00:16.590 --> 00:00:16.600 align:start position:0%
solution in the diagram of our solution
 

00:00:16.600 --> 00:00:18.830 align:start position:0%
solution in the diagram of our solution
we<00:00:16.720><c> have</c><00:00:16.920><c> several</c><00:00:17.320><c> elements</c><00:00:18.320><c> let's</c><00:00:18.560><c> start</c>

00:00:18.830 --> 00:00:18.840 align:start position:0%
we have several elements let's start
 

00:00:18.840 --> 00:00:22.109 align:start position:0%
we have several elements let's start
from<00:00:19.000><c> the</c><00:00:19.160><c> right</c><00:00:19.439><c> hand</c><00:00:19.960><c> side</c><00:00:20.960><c> the</c><00:00:21.160><c> docs</c><00:00:21.840><c> or</c>

00:00:22.109 --> 00:00:22.119 align:start position:0%
from the right hand side the docs or
 

00:00:22.119 --> 00:00:24.349 align:start position:0%
from the right hand side the docs or
documentation<00:00:23.119><c> serves</c><00:00:23.519><c> as</c><00:00:23.680><c> our</c><00:00:24.000><c> knowledge</c>

00:00:24.349 --> 00:00:24.359 align:start position:0%
documentation serves as our knowledge
 

00:00:24.359 --> 00:00:26.870 align:start position:0%
documentation serves as our knowledge
base<00:00:25.359><c> it</c><00:00:25.480><c> should</c><00:00:25.680><c> be</c><00:00:25.880><c> comprehensive</c><00:00:26.679><c> and</c>

00:00:26.870 --> 00:00:26.880 align:start position:0%
base it should be comprehensive and
 

00:00:26.880 --> 00:00:30.589 align:start position:0%
base it should be comprehensive and
contain<00:00:27.560><c> most</c><00:00:27.960><c> answers</c><00:00:28.320><c> to</c><00:00:28.480><c> user</c><00:00:28.960><c> questions</c>

00:00:30.589 --> 00:00:30.599 align:start position:0%
contain most answers to user questions
 

00:00:30.599 --> 00:00:32.429 align:start position:0%
contain most answers to user questions
however<00:00:30.920><c> navigating</c><00:00:31.560><c> it</c><00:00:31.720><c> can</c><00:00:31.880><c> be</c><00:00:32.279><c> difficult</c>

00:00:32.429 --> 00:00:32.439 align:start position:0%
however navigating it can be difficult
 

00:00:32.439 --> 00:00:34.950 align:start position:0%
however navigating it can be difficult
for<00:00:32.640><c> users</c><00:00:33.600><c> and</c><00:00:33.840><c> they</c><00:00:33.960><c> may</c><00:00:34.160><c> prefer</c><00:00:34.480><c> to</c><00:00:34.600><c> Simply</c>

00:00:34.950 --> 00:00:34.960 align:start position:0%
for users and they may prefer to Simply
 

00:00:34.960 --> 00:00:37.510 align:start position:0%
for users and they may prefer to Simply
ask<00:00:35.160><c> a</c><00:00:35.399><c> question</c><00:00:36.000><c> and</c><00:00:36.200><c> expect</c><00:00:36.520><c> an</c>

00:00:37.510 --> 00:00:37.520 align:start position:0%
ask a question and expect an
 

00:00:37.520 --> 00:00:40.190 align:start position:0%
ask a question and expect an
answer<00:00:38.520><c> we</c><00:00:38.640><c> Feit</c><00:00:38.960><c> all</c><00:00:39.239><c> documents</c><00:00:39.760><c> into</c><00:00:39.960><c> a</c>

00:00:40.190 --> 00:00:40.200 align:start position:0%
answer we Feit all documents into a
 

00:00:40.200 --> 00:00:42.790 align:start position:0%
answer we Feit all documents into a
document<00:00:40.520><c> store</c><00:00:41.360><c> and</c><00:00:41.520><c> then</c><00:00:41.680><c> use</c><00:00:41.879><c> an</c><00:00:42.239><c> embedding</c>

00:00:42.790 --> 00:00:42.800 align:start position:0%
document store and then use an embedding
 

00:00:42.800 --> 00:00:45.430 align:start position:0%
document store and then use an embedding
model<00:00:43.480><c> to</c><00:00:43.680><c> find</c><00:00:44.160><c> relevant</c><00:00:44.680><c> documents</c><00:00:45.239><c> for</c>

00:00:45.430 --> 00:00:45.440 align:start position:0%
model to find relevant documents for
 

00:00:45.440 --> 00:00:47.510 align:start position:0%
model to find relevant documents for
user

00:00:47.510 --> 00:00:47.520 align:start position:0%
user
 

00:00:47.520 --> 00:00:49.910 align:start position:0%
user
questions<00:00:48.520><c> embedding</c><00:00:49.000><c> model</c><00:00:49.360><c> converts</c>

00:00:49.910 --> 00:00:49.920 align:start position:0%
questions embedding model converts
 

00:00:49.920 --> 00:00:52.590 align:start position:0%
questions embedding model converts
documents<00:00:50.559><c> into</c><00:00:50.840><c> numeric</c><00:00:51.600><c> representations</c>

00:00:52.590 --> 00:00:52.600 align:start position:0%
documents into numeric representations
 

00:00:52.600 --> 00:00:55.590 align:start position:0%
documents into numeric representations
and<00:00:52.800><c> stores</c><00:00:53.280><c> them</c><00:00:53.520><c> in</c><00:00:53.640><c> a</c><00:00:53.840><c> vector</c>

00:00:55.590 --> 00:00:55.600 align:start position:0%
and stores them in a vector
 

00:00:55.600 --> 00:00:58.470 align:start position:0%
and stores them in a vector
database<00:00:56.600><c> we</c><00:00:56.800><c> also</c><00:00:57.079><c> pass</c><00:00:57.359><c> user</c><00:00:57.760><c> questions</c><00:00:58.320><c> to</c>

00:00:58.470 --> 00:00:58.480 align:start position:0%
database we also pass user questions to
 

00:00:58.480 --> 00:01:00.590 align:start position:0%
database we also pass user questions to
the<00:00:58.600><c> embedding</c><00:00:59.039><c> model</c><00:00:59.519><c> to</c><00:00:59.680><c> find</c><00:00:59.920><c> find</c><00:01:00.160><c> similar</c>

00:01:00.590 --> 00:01:00.600 align:start position:0%
the embedding model to find find similar
 

00:01:00.600 --> 00:01:02.830 align:start position:0%
the embedding model to find find similar
documents<00:01:01.280><c> through</c><00:01:01.600><c> similarity</c>

00:01:02.830 --> 00:01:02.840 align:start position:0%
documents through similarity
 

00:01:02.840 --> 00:01:05.710 align:start position:0%
documents through similarity
search<00:01:03.840><c> a</c><00:01:04.040><c> prompt</c><00:01:04.360><c> template</c><00:01:05.000><c> helps</c><00:01:05.239><c> us</c><00:01:05.439><c> create</c>

00:01:05.710 --> 00:01:05.720 align:start position:0%
search a prompt template helps us create
 

00:01:05.720 --> 00:01:08.590 align:start position:0%
search a prompt template helps us create
a<00:01:05.880><c> dynamic</c><00:01:06.400><c> prompt</c><00:01:07.240><c> that</c><00:01:07.439><c> includes</c><00:01:07.880><c> the</c><00:01:08.000><c> user</c>

00:01:08.590 --> 00:01:08.600 align:start position:0%
a dynamic prompt that includes the user
 

00:01:08.600 --> 00:01:11.630 align:start position:0%
a dynamic prompt that includes the user
question<00:01:09.600><c> and</c><00:01:09.840><c> similar</c><00:01:10.439><c> documents</c><00:01:11.439><c> this</c>

00:01:11.630 --> 00:01:11.640 align:start position:0%
question and similar documents this
 

00:01:11.640 --> 00:01:14.510 align:start position:0%
question and similar documents this
prompt<00:01:12.000><c> is</c><00:01:12.240><c> passed</c><00:01:12.680><c> to</c><00:01:12.920><c> the</c><00:01:13.200><c> llm</c><00:01:14.200><c> for</c>

00:01:14.510 --> 00:01:14.520 align:start position:0%
prompt is passed to the llm for
 

00:01:14.520 --> 00:01:17.190 align:start position:0%
prompt is passed to the llm for
generating<00:01:15.080><c> an</c><00:01:15.320><c> answer</c><00:01:16.080><c> that</c><00:01:16.200><c> is</c><00:01:16.520><c> displayed</c>

00:01:17.190 --> 00:01:17.200 align:start position:0%
generating an answer that is displayed
 

00:01:17.200 --> 00:01:20.310 align:start position:0%
generating an answer that is displayed
to<00:01:17.400><c> the</c><00:01:17.600><c> user</c><00:01:18.680><c> embeddings</c><00:01:19.680><c> help</c><00:01:20.000><c> find</c>

00:01:20.310 --> 00:01:20.320 align:start position:0%
to the user embeddings help find
 

00:01:20.320 --> 00:01:22.830 align:start position:0%
to the user embeddings help find
relevant<00:01:20.799><c> documents</c><00:01:21.799><c> without</c><00:01:22.200><c> relying</c><00:01:22.640><c> on</c>

00:01:22.830 --> 00:01:22.840 align:start position:0%
relevant documents without relying on
 

00:01:22.840 --> 00:01:25.550 align:start position:0%
relevant documents without relying on
keyword<00:01:23.240><c> matches</c><00:01:23.920><c> alone</c><00:01:24.920><c> consider</c><00:01:25.360><c> for</c>

00:01:25.550 --> 00:01:25.560 align:start position:0%
keyword matches alone consider for
 

00:01:25.560 --> 00:01:28.109 align:start position:0%
keyword matches alone consider for
example<00:01:26.119><c> this</c><00:01:26.640><c> question</c><00:01:27.360><c> and</c><00:01:27.520><c> a</c><00:01:27.720><c> relevant</c>

00:01:28.109 --> 00:01:28.119 align:start position:0%
example this question and a relevant
 

00:01:28.119 --> 00:01:30.510 align:start position:0%
example this question and a relevant
document<00:01:28.560><c> pair</c><00:01:29.360><c> the</c><00:01:29.479><c> question</c><00:01:29.960><c> is</c><00:01:30.159><c> how</c><00:01:30.280><c> can</c><00:01:30.400><c> I</c>

00:01:30.510 --> 00:01:30.520 align:start position:0%
document pair the question is how can I
 

00:01:30.520 --> 00:01:32.870 align:start position:0%
document pair the question is how can I
show<00:01:30.759><c> my</c><00:01:30.920><c> experiments</c><00:01:31.479><c> to</c><00:01:31.640><c> my</c><00:01:31.759><c> team</c><00:01:32.000><c> members</c>

00:01:32.870 --> 00:01:32.880 align:start position:0%
show my experiments to my team members
 

00:01:32.880 --> 00:01:34.190 align:start position:0%
show my experiments to my team members
and<00:01:33.000><c> then</c><00:01:33.119><c> the</c><00:01:33.280><c> document</c><00:01:33.640><c> talks</c><00:01:33.920><c> about</c>

00:01:34.190 --> 00:01:34.200 align:start position:0%
and then the document talks about
 

00:01:34.200 --> 00:01:36.630 align:start position:0%
and then the document talks about
collaborative<00:01:34.880><c> reports</c><00:01:35.680><c> and</c><00:01:35.840><c> using</c><00:01:36.399><c> weight</c>

00:01:36.630 --> 00:01:36.640 align:start position:0%
collaborative reports and using weight
 

00:01:36.640 --> 00:01:39.069 align:start position:0%
collaborative reports and using weight
Andis<00:01:37.159><c> reports</c><00:01:37.600><c> to</c><00:01:37.880><c> organize</c><00:01:38.280><c> runs</c><00:01:38.680><c> embed</c>

00:01:39.069 --> 00:01:39.079 align:start position:0%
Andis reports to organize runs embed
 

00:01:39.079 --> 00:01:41.389 align:start position:0%
Andis reports to organize runs embed
automate<00:01:39.680><c> visualizations</c><00:01:40.680><c> share</c><00:01:41.040><c> updates</c>

00:01:41.389 --> 00:01:41.399 align:start position:0%
automate visualizations share updates
 

00:01:41.399 --> 00:01:44.670 align:start position:0%
automate visualizations share updates
with<00:01:41.880><c> collaborators</c><00:01:42.880><c> there</c><00:01:43.000><c> is</c><00:01:43.439><c> zero</c><00:01:43.960><c> lexical</c>

00:01:44.670 --> 00:01:44.680 align:start position:0%
with collaborators there is zero lexical
 

00:01:44.680 --> 00:01:46.789 align:start position:0%
with collaborators there is zero lexical
overlap<00:01:45.280><c> between</c><00:01:45.560><c> this</c><00:01:45.759><c> question</c><00:01:46.079><c> and</c><00:01:46.320><c> answer</c>

00:01:46.789 --> 00:01:46.799 align:start position:0%
overlap between this question and answer
 

00:01:46.799 --> 00:01:48.749 align:start position:0%
overlap between this question and answer
so<00:01:46.960><c> if</c><00:01:47.079><c> we</c><00:01:47.240><c> rely</c><00:01:47.520><c> on</c><00:01:47.719><c> traditional</c><00:01:48.200><c> search</c><00:01:48.640><c> we</c>

00:01:48.749 --> 00:01:48.759 align:start position:0%
so if we rely on traditional search we
 

00:01:48.759 --> 00:01:51.190 align:start position:0%
so if we rely on traditional search we
will<00:01:48.960><c> not</c><00:01:49.200><c> retrieve</c><00:01:49.600><c> this</c><00:01:49.920><c> document</c><00:01:50.920><c> however</c>

00:01:51.190 --> 00:01:51.200 align:start position:0%
will not retrieve this document however
 

00:01:51.200 --> 00:01:53.630 align:start position:0%
will not retrieve this document however
there<00:01:51.320><c> is</c><00:01:51.520><c> a</c><00:01:51.759><c> high</c><00:01:52.119><c> semantic</c><00:01:52.680><c> overlap</c><00:01:53.439><c> and</c>

00:01:53.630 --> 00:01:53.640 align:start position:0%
there is a high semantic overlap and
 

00:01:53.640 --> 00:01:55.670 align:start position:0%
there is a high semantic overlap and
this<00:01:53.880><c> document</c><00:01:54.360><c> is</c><00:01:54.600><c> actually</c><00:01:54.960><c> very</c><00:01:55.240><c> relevant</c>

00:01:55.670 --> 00:01:55.680 align:start position:0%
this document is actually very relevant
 

00:01:55.680 --> 00:01:58.230 align:start position:0%
this document is actually very relevant
to<00:01:55.840><c> the</c><00:01:56.240><c> question</c><00:01:57.240><c> and</c><00:01:57.399><c> we</c><00:01:57.520><c> use</c><00:01:57.719><c> embedding</c>

00:01:58.230 --> 00:01:58.240 align:start position:0%
to the question and we use embedding
 

00:01:58.240 --> 00:02:01.069 align:start position:0%
to the question and we use embedding
models<00:01:59.000><c> to</c><00:01:59.240><c> find</c><00:02:00.000><c> these</c><00:02:00.399><c> semantically</c>

00:02:01.069 --> 00:02:01.079 align:start position:0%
models to find these semantically
 

00:02:01.079 --> 00:02:04.230 align:start position:0%
models to find these semantically
related<00:02:01.560><c> documents</c><00:02:02.079><c> for</c><00:02:02.280><c> our</c><00:02:03.039><c> questions</c><00:02:04.039><c> by</c>

00:02:04.230 --> 00:02:04.240 align:start position:0%
related documents for our questions by
 

00:02:04.240 --> 00:02:06.910 align:start position:0%
related documents for our questions by
training<00:02:04.640><c> embedding</c><00:02:05.119><c> models</c><00:02:05.920><c> on</c><00:02:06.240><c> large</c>

00:02:06.910 --> 00:02:06.920 align:start position:0%
training embedding models on large
 

00:02:06.920 --> 00:02:09.710 align:start position:0%
training embedding models on large
question<00:02:07.560><c> answer</c><00:02:08.119><c> pair</c><00:02:08.520><c> data</c><00:02:08.800><c> sets</c><00:02:09.360><c> such</c><00:02:09.520><c> as</c>

00:02:09.710 --> 00:02:09.720 align:start position:0%
question answer pair data sets such as
 

00:02:09.720 --> 00:02:12.710 align:start position:0%
question answer pair data sets such as
stack<00:02:10.039><c> exchange</c><00:02:10.920><c> edics</c><00:02:11.520><c> can</c><00:02:11.760><c> effectively</c><00:02:12.319><c> map</c>

00:02:12.710 --> 00:02:12.720 align:start position:0%
stack exchange edics can effectively map
 

00:02:12.720 --> 00:02:15.229 align:start position:0%
stack exchange edics can effectively map
questions<00:02:13.640><c> and</c><00:02:13.920><c> documents</c><00:02:14.400><c> into</c><00:02:14.599><c> a</c><00:02:14.760><c> numeric</c>

00:02:15.229 --> 00:02:15.239 align:start position:0%
questions and documents into a numeric
 

00:02:15.239 --> 00:02:18.990 align:start position:0%
questions and documents into a numeric
space<00:02:15.879><c> where</c><00:02:16.040><c> we</c><00:02:16.160><c> can</c><00:02:16.400><c> compute</c><00:02:17.000><c> similarity</c>

00:02:18.990 --> 00:02:19.000 align:start position:0%
space where we can compute similarity
 

00:02:19.000 --> 00:02:21.750 align:start position:0%
space where we can compute similarity
scores<00:02:20.000><c> how</c><00:02:20.120><c> do</c><00:02:20.280><c> we</c><00:02:20.519><c> calculate</c><00:02:21.040><c> similarity</c>

00:02:21.750 --> 00:02:21.760 align:start position:0%
scores how do we calculate similarity
 

00:02:21.760 --> 00:02:25.509 align:start position:0%
scores how do we calculate similarity
using<00:02:22.080><c> our</c><00:02:22.319><c> emedic</c><00:02:23.200><c> model</c><00:02:24.200><c> we</c><00:02:24.319><c> can</c><00:02:24.519><c> use</c><00:02:24.840><c> ukian</c>

00:02:25.509 --> 00:02:25.519 align:start position:0%
using our emedic model we can use ukian
 

00:02:25.519 --> 00:02:28.150 align:start position:0%
using our emedic model we can use ukian
distance<00:02:26.360><c> which</c><00:02:26.480><c> is</c><00:02:26.640><c> the</c><00:02:26.879><c> length</c><00:02:27.360><c> of</c><00:02:27.480><c> a</c><00:02:27.680><c> line</c>

00:02:28.150 --> 00:02:28.160 align:start position:0%
distance which is the length of a line
 

00:02:28.160 --> 00:02:30.750 align:start position:0%
distance which is the length of a line
between<00:02:28.519><c> two</c><00:02:28.720><c> points</c><00:02:29.000><c> in</c><00:02:29.120><c> the</c><00:02:29.280><c> vector</c><00:02:29.519><c> space</c>

00:02:30.750 --> 00:02:30.760 align:start position:0%
between two points in the vector space
 

00:02:30.760 --> 00:02:32.910 align:start position:0%
between two points in the vector space
or<00:02:30.920><c> we</c><00:02:31.040><c> can</c><00:02:31.160><c> use</c><00:02:31.400><c> cosine</c><00:02:31.840><c> similarity</c><00:02:32.640><c> which</c><00:02:32.760><c> is</c>

00:02:32.910 --> 00:02:32.920 align:start position:0%
or we can use cosine similarity which is
 

00:02:32.920 --> 00:02:35.990 align:start position:0%
or we can use cosine similarity which is
the<00:02:33.160><c> angle</c><00:02:33.760><c> between</c><00:02:34.080><c> two</c><00:02:34.680><c> vectors</c><00:02:35.680><c> both</c><00:02:35.879><c> of</c>

00:02:35.990 --> 00:02:36.000 align:start position:0%
the angle between two vectors both of
 

00:02:36.000 --> 00:02:37.509 align:start position:0%
the angle between two vectors both of
these<00:02:36.160><c> metrics</c><00:02:36.519><c> are</c><00:02:36.760><c> popular</c><00:02:37.360><c> and</c>

00:02:37.509 --> 00:02:37.519 align:start position:0%
these metrics are popular and
 

00:02:37.519 --> 00:02:39.949 align:start position:0%
these metrics are popular and
demonstrate<00:02:38.080><c> similar</c><00:02:38.680><c> performance</c><00:02:39.680><c> but</c>

00:02:39.949 --> 00:02:39.959 align:start position:0%
demonstrate similar performance but
 

00:02:39.959 --> 00:02:42.309 align:start position:0%
demonstrate similar performance but
experimenting<00:02:40.680><c> with</c><00:02:41.000><c> them</c><00:02:41.680><c> uh</c><00:02:41.840><c> could</c><00:02:42.040><c> help</c>

00:02:42.309 --> 00:02:42.319 align:start position:0%
experimenting with them uh could help
 

00:02:42.319 --> 00:02:46.430 align:start position:0%
experimenting with them uh could help
find<00:02:42.519><c> the</c><00:02:42.720><c> best</c><00:02:42.920><c> one</c><00:02:43.200><c> for</c><00:02:43.400><c> your</c><00:02:43.680><c> specific</c><00:02:44.159><c> data</c>

00:02:46.430 --> 00:02:46.440 align:start position:0%
find the best one for your specific data
 

00:02:46.440 --> 00:02:49.149 align:start position:0%
find the best one for your specific data
set<00:02:47.440><c> sometimes</c><00:02:47.680><c> embeddings</c><00:02:48.200><c> don't</c><00:02:48.560><c> work</c><00:02:48.920><c> very</c>

00:02:49.149 --> 00:02:49.159 align:start position:0%
set sometimes embeddings don't work very
 

00:02:49.159 --> 00:02:52.030 align:start position:0%
set sometimes embeddings don't work very
well<00:02:50.120><c> embeddings</c><00:02:50.640><c> may</c><00:02:50.840><c> struggle</c><00:02:51.440><c> with</c><00:02:51.640><c> domain</c>

00:02:52.030 --> 00:02:52.040 align:start position:0%
well embeddings may struggle with domain
 

00:02:52.040 --> 00:02:55.710 align:start position:0%
well embeddings may struggle with domain
specific<00:02:52.560><c> data</c><00:02:53.280><c> like</c><00:02:53.640><c> biology</c><00:02:54.400><c> or</c><00:02:54.720><c> chemistry</c>

00:02:55.710 --> 00:02:55.720 align:start position:0%
specific data like biology or chemistry
 

00:02:55.720 --> 00:02:58.589 align:start position:0%
specific data like biology or chemistry
as<00:02:55.840><c> well</c><00:02:56.000><c> as</c><00:02:56.159><c> new</c><00:02:56.360><c> terms</c><00:02:56.680><c> or</c><00:02:56.879><c> proper</c><00:02:57.599><c> names</c>

00:02:58.589 --> 00:02:58.599 align:start position:0%
as well as new terms or proper names
 

00:02:58.599 --> 00:03:00.670 align:start position:0%
as well as new terms or proper names
Solutions<00:02:59.400><c> include</c><00:02:59.720><c> INE</c><00:02:59.920><c> training</c><00:03:00.239><c> your</c><00:03:00.400><c> own</c>

00:03:00.670 --> 00:03:00.680 align:start position:0%
Solutions include INE training your own
 

00:03:00.680 --> 00:03:03.110 align:start position:0%
Solutions include INE training your own
embedding<00:03:01.159><c> model</c><00:03:01.879><c> or</c><00:03:02.200><c> combining</c><00:03:02.640><c> embeddings</c>

00:03:03.110 --> 00:03:03.120 align:start position:0%
embedding model or combining embeddings
 

00:03:03.120 --> 00:03:05.470 align:start position:0%
embedding model or combining embeddings
with<00:03:03.319><c> classic</c><00:03:03.599><c> search</c><00:03:04.000><c> algorithms</c><00:03:05.000><c> such</c><00:03:05.200><c> as</c>

00:03:05.470 --> 00:03:05.480 align:start position:0%
with classic search algorithms such as
 

00:03:05.480 --> 00:03:06.990 align:start position:0%
with classic search algorithms such as
tfidf<00:03:06.280><c> or</c>

00:03:06.990 --> 00:03:07.000 align:start position:0%
tfidf or
 

00:03:07.000 --> 00:03:09.509 align:start position:0%
tfidf or
bm25<00:03:08.000><c> consider</c><00:03:08.400><c> learning</c><00:03:09.080><c> about</c><00:03:09.360><c> these</c>

00:03:09.509 --> 00:03:09.519 align:start position:0%
bm25 consider learning about these
 

00:03:09.519 --> 00:03:11.270 align:start position:0%
bm25 consider learning about these
methods<00:03:09.879><c> if</c><00:03:09.959><c> you're</c><00:03:10.120><c> dealing</c><00:03:10.440><c> with</c><00:03:10.680><c> complex</c>

00:03:11.270 --> 00:03:11.280 align:start position:0%
methods if you're dealing with complex
 

00:03:11.280 --> 00:03:13.030 align:start position:0%
methods if you're dealing with complex
domain<00:03:11.720><c> specific</c>

00:03:13.030 --> 00:03:13.040 align:start position:0%
domain specific
 

00:03:13.040 --> 00:03:15.949 align:start position:0%
domain specific
data<00:03:14.040><c> in</c><00:03:14.239><c> conclusion</c><00:03:14.920><c> using</c><00:03:15.239><c> a</c><00:03:15.440><c> powerful</c>

00:03:15.949 --> 00:03:15.959 align:start position:0%
data in conclusion using a powerful
 

00:03:15.959 --> 00:03:18.030 align:start position:0%
data in conclusion using a powerful
combination<00:03:16.480><c> of</c><00:03:16.680><c> document</c><00:03:17.040><c> store</c><00:03:17.560><c> embedding</c>

00:03:18.030 --> 00:03:18.040 align:start position:0%
combination of document store embedding
 

00:03:18.040 --> 00:03:20.270 align:start position:0%
combination of document store embedding
models<00:03:18.560><c> and</c><00:03:18.720><c> large</c><00:03:19.000><c> language</c><00:03:19.360><c> models</c><00:03:20.080><c> can</c>

00:03:20.270 --> 00:03:20.280 align:start position:0%
models and large language models can
 

00:03:20.280 --> 00:03:22.630 align:start position:0%
models and large language models can
greatly<00:03:20.640><c> improve</c><00:03:21.040><c> the</c><00:03:21.200><c> way</c><00:03:21.560><c> users</c><00:03:22.120><c> receive</c>

00:03:22.630 --> 00:03:22.640 align:start position:0%
greatly improve the way users receive
 

00:03:22.640 --> 00:03:28.670 align:start position:0%
greatly improve the way users receive
accurate<00:03:23.319><c> and</c><00:03:23.519><c> concise</c>

00:03:28.670 --> 00:03:28.680 align:start position:0%
 
 

00:03:28.680 --> 00:03:31.680 align:start position:0%
 
answers

