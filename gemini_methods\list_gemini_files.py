import asyncio
import logging
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional, List

# --- Dynamic Path Setup for IDE/Script Execution ---
# This block ensures you can run this script from any directory (e.g., pressing 'play' in Cursor/VSCode)
import os
import sys
from pathlib import Path

# Find the backend root (the directory containing 'LangGraph_TDD_v14')
current_file = Path(__file__).resolve()
for parent in current_file.parents:
    if (parent / 'LangGraph_TDD_v14').is_dir():
        backend_root = parent
        break
else:
    backend_root = current_file.parents[2]  # Fallback: up two levels

if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# --- Imports from project ---
try:
    from deprecated_LangGraph_TDD_v14.gemini_methods.file_manager import GenaiFileManager  # Absolute import for script execution
    from google.genai import types as genai_types  # Modern SDK import
    from google.api_core import exceptions as google_exceptions
except ImportError as e:
    print(f"Error importing project modules: {e}")
    print("Please ensure the script is run from the project root or PYTHONPATH includes the backend root.")
    sys.exit(1)

# --- Rich Console --- #
try:
    from rich.console import Console
    from rich.table import Table
    from rich.prompt import Prompt, Confirm
except ImportError:
    print("Error: 'rich' library not found. Please install it: pip install rich")
    sys.exit(1)

console = Console()

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Helper Functions ---
def format_size(size_bytes: Optional[int]) -> str:
    """Convert bytes to human readable format."""
    if size_bytes is None:
        return "[dim]N/A[/dim]"
    # Convert to float for calculations
    size_float = float(size_bytes)
    for unit in ["B", "KB", "MB", "GB"]:
        if size_float < 1024:
            return f"{size_float:.2f} {unit}"
        size_float /= 1024
    return f"{size_float:.2f} TB"

def format_timestamp(timestamp: Optional[datetime]) -> str:
    """Format datetime.datetime object to human readable format (string)."""
    if timestamp is None:
        return "[dim]N/A[/dim]"
    try:
        # Assume timestamp is already a datetime object
        if isinstance(timestamp, datetime):
            # Format directly, assuming it's UTC or converting if needed
            # Let's assume it's already timezone-aware or naive UTC
            return timestamp.strftime("%Y-%m-%d %H:%M:%S [dim]UTC[/dim]")
        else:
            # Fallback if it's not a datetime object (e.g., protobuf timestamp)
            dt = datetime.fromtimestamp(timestamp.seconds) # Attempt original logic
            return dt.strftime("%Y-%m-%d %H:%M:%S [dim]UTC[/dim]")
    except Exception as e:
        logger.warning(f"Could not format timestamp {timestamp}: {e}")
        return f"[yellow]{str(timestamp)}[/yellow]"

# --- Core Logic Functions ---
async def list_and_display_files(manager: GenaiFileManager):
    """Lists files using the manager and displays them in a rich table."""
    logger.info("Listing files...")
    try:
        files = await manager.list_files()

        if not files:
            console.print("[yellow]No files found in Google GenAI storage.[/yellow]")
            return

        table = Table(title="Uploaded Gemini Files", show_header=True, header_style="bold magenta")
        table.add_column("File ID", style="dim", width=25)
        table.add_column("Display Name", width=40)
        table.add_column("Size", justify="right", style="green", width=10)
        table.add_column("Created At", style="cyan", width=25)
        table.add_column("State", style="bold", width=12)

        for file_obj in files:
            # Ensure getattr results are not None before formatting
            file_id = getattr(file_obj, 'name', None) or '[dim]N/A[/dim]'
            display_name = getattr(file_obj, 'display_name', None) or '[dim]N/A[/dim]'
            size = format_size(getattr(file_obj, 'size_bytes', None))
            created_at = format_timestamp(getattr(file_obj, 'create_time', None))
            # Get the actual enum name (e.g., 'ACTIVE', 'PROCESSING')
            state_enum = getattr(file_obj, 'state', None)
            state_raw = state_enum.name if state_enum else 'UNKNOWN'
            state_color = "green" if state_raw == "ACTIVE" else "yellow"
            state = f"[{state_color}]{state_raw}[/{state_color}]"

            # Add row to table
            table.add_row(file_id, display_name, size, created_at, state)

        console.print(table)
        logger.info(f"Total files listed: {len(files)}")

    except google_exceptions.GoogleAPIError as e:
        logger.error(f"API Error during file listing: {e}")
        console.print(f"[bold red]API Error listing files:[/bold red] {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during listing: {e}", exc_info=True)
        console.print(f"[bold red]Unexpected error listing files:[/bold red] {e}")

async def delete_all_files(manager: GenaiFileManager):
    """Lists all files and prompts the user to delete them."""
    logger.info("Retrieving file list for deletion...")
    try:
        files_to_delete = await manager.list_files()

        if not files_to_delete:
            console.print("[yellow]No files found to delete.[/yellow]")
            return

        console.print(f"Found {len(files_to_delete)} files. Example File ID: {getattr(files_to_delete[0], 'name', 'N/A')}")
        if Confirm.ask(f"[bold yellow]Are you sure you want to delete all {len(files_to_delete)} files permanently?[/bold yellow]", default=False):
            deleted_count = 0
            failed_count = 0
            console.print("Proceeding with deletion...")
            # Use asyncio.gather for concurrent deletion
            delete_tasks = []
            for file_obj in files_to_delete:
                file_id = getattr(file_obj, 'name', None)
                if file_id:
                    delete_tasks.append(manager.delete_file(file_id))
                else:
                    logger.warning(f"Skipping file with missing ID: {file_obj}")
            
            results = await asyncio.gather(*delete_tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                file_id_attempted = getattr(files_to_delete[i], 'name', 'UNKNOWN_ID')
                if isinstance(result, Exception) or result is False:
                    failed_count += 1
                    error_msg = result if isinstance(result, Exception) else 'Delete operation returned False'
                    logger.error(f"Failed to delete file {file_id_attempted}: {error_msg}")
                    console.print(f"[red]Failed to delete {file_id_attempted}[/red]")
                else:
                    deleted_count += 1
                    logger.info(f"Successfully deleted file {file_id_attempted}")
                    # console.print(f"[green]Deleted {file_id_attempted}[/green]") # Optional: too verbose?

            console.print(f"\n[bold green]Deletion Complete:[/bold green] {deleted_count} succeeded, [bold red]{failed_count} failed[/bold red].")
        else:
            console.print("[cyan]Deletion cancelled.[/cyan]")

    except google_exceptions.GoogleAPIError as e:
        logger.error(f"API Error during file deletion process: {e}")
        console.print(f"[bold red]API Error during deletion process:[/bold red] {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during deletion: {e}", exc_info=True)
        console.print(f"[bold red]Unexpected error during deletion:[/bold red] {e}")

# --- Main Execution ---
async def main():
    logger.info("Initializing Gemini File Manager...")
    try:
        manager = GenaiFileManager()
    except (RuntimeError, ValueError) as e: # Catch init errors
        logger.error(f"Failed to initialize FileManager: {e}")
        console.print(f"[bold red]Error initializing manager:[/bold red] {e}")
        return

    while True:
        console.print("\n[bold cyan]Gemini File Management[/bold cyan]")
        console.print("1: List Uploaded Files")
        console.print("2: Delete ALL Uploaded Files")
        console.print("q: Quit")
        choice = Prompt.ask("Choose an option", choices=["1", "2", "q"], default="1")

        if choice == '1':
            await list_and_display_files(manager)
        elif choice == '2':
            await delete_all_files(manager)
        elif choice == 'q':
            console.print("[yellow]Exiting.[/yellow]")
            break

if __name__ == "__main__":
    logger.info(f"Project Root: {backend_root}")
    asyncio.run(main())
