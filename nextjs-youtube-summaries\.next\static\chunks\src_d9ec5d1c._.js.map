{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport function formatDuration(duration: string | number): string {\r\n  // Handle number input (duration in seconds)\r\n  if (typeof duration === 'number') {\r\n    const hours = Math.floor(duration / 3600)\r\n    const minutes = Math.floor((duration % 3600) / 60)\r\n    const seconds = duration % 60\r\n\r\n    if (hours > 0) {\r\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`\r\n    }\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`\r\n  }\r\n\r\n  // Handle string input (YouTube duration format PT4M13S)\r\n  if (typeof duration === 'string') {\r\n    const match = duration.match(/PT(\\d+H)?(\\d+M)?(\\d+S)?/)\r\n    if (!match) return duration\r\n\r\n    const hours = match[1] ? parseInt(match[1].replace('H', '')) : 0\r\n    const minutes = match[2] ? parseInt(match[2].replace('M', '')) : 0\r\n    const seconds = match[3] ? parseInt(match[3].replace('S', '')) : 0\r\n\r\n    if (hours > 0) {\r\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`\r\n    }\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`\r\n  }\r\n\r\n  // Fallback for unexpected input types\r\n  return String(duration)\r\n}\r\n\r\nexport function formatViewCount(count: number): string {\r\n  if (count >= 1000000) {\r\n    return `${(count / 1000000).toFixed(1)}M views`\r\n  } else if (count >= 1000) {\r\n    return `${(count / 1000).toFixed(1)}K views`\r\n  }\r\n  return `${count} views`\r\n}\r\n\r\n// Get the most appropriate date for a video - only use actual YouTube publication date\r\nexport function getEffectiveDate(video: { published_at?: string | null, created_at?: string | null, updated_at?: string | null }): string | null {\r\n  // Handle data quality issue: ignore fake published_at dates that match created_at\r\n  const isRealDate = video.published_at && video.created_at && video.published_at !== video.created_at;\r\n  return isRealDate ? video.published_at : null;\r\n}\r\n\r\nexport function formatDate(dateString: string | null | undefined): string {\r\n  if (!dateString || dateString === 'null' || dateString === 'undefined') {\r\n    // Handle NULL/empty dates - these are considered \"older\" videos\r\n    return \"Older video\";\r\n  }\r\n\r\n  const date = new Date(dateString);\r\n\r\n  if (isNaN(date.getTime())) {\r\n    console.warn(`formatDate: Invalid date string received: \"${dateString}\"`);\r\n    return `Invalid date: ${dateString.substring(0, 20)}${dateString.length > 20 ? '...' : ''}`;\r\n  }\r\n\r\n  // Return the actual publication date instead of relative time\r\n  // Format: \"MMM DD, YYYY\" (e.g., \"Dec 15, 2024\")\r\n  return date.toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric'\r\n  });\r\n}\r\n\r\nexport function truncateText(text: string | null | undefined, maxLength: number): string {\r\n  if (!text) return ''\r\n  if (text.length <= maxLength) return text\r\n  return text.slice(0, maxLength) + '...'\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,QAAyB;IACtD,4CAA4C;IAC5C,IAAI,OAAO,aAAa,UAAU;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,WAAW;QACpC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,WAAW,OAAQ;QAC/C,MAAM,UAAU,WAAW;QAE3B,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QACjG;QACA,OAAO,GAAG,QAAQ,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC5D;IAEA,wDAAwD;IACxD,IAAI,OAAO,aAAa,UAAU;QAChC,MAAM,QAAQ,SAAS,KAAK,CAAC;QAC7B,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,QAAQ,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,OAAO;QAC/D,MAAM,UAAU,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,OAAO;QACjE,MAAM,UAAU,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,OAAO;QAEjE,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QACjG;QACA,OAAO,GAAG,QAAQ,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC5D;IAEA,sCAAsC;IACtC,OAAO,OAAO;AAChB;AAEO,SAAS,gBAAgB,KAAa;IAC3C,IAAI,SAAS,SAAS;QACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;IACjD,OAAO,IAAI,SAAS,MAAM;QACxB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;IAC9C;IACA,OAAO,GAAG,MAAM,MAAM,CAAC;AACzB;AAGO,SAAS,iBAAiB,KAA+F;IAC9H,kFAAkF;IAClF,MAAM,aAAa,MAAM,YAAY,IAAI,MAAM,UAAU,IAAI,MAAM,YAAY,KAAK,MAAM,UAAU;IACpG,OAAO,aAAa,MAAM,YAAY,GAAG;AAC3C;AAEO,SAAS,WAAW,UAAqC;IAC9D,IAAI,CAAC,cAAc,eAAe,UAAU,eAAe,aAAa;QACtE,gEAAgE;QAChE,OAAO;IACT;IAEA,MAAM,OAAO,IAAI,KAAK;IAEtB,IAAI,MAAM,KAAK,OAAO,KAAK;QACzB,QAAQ,IAAI,CAAC,CAAC,2CAA2C,EAAE,WAAW,CAAC,CAAC;QACxE,OAAO,CAAC,cAAc,EAAE,WAAW,SAAS,CAAC,GAAG,MAAM,WAAW,MAAM,GAAG,KAAK,QAAQ,IAAI;IAC7F;IAEA,8DAA8D;IAC9D,gDAAgD;IAChD,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,aAAa,IAA+B,EAAE,SAAiB;IAC7E,IAAI,CAAC,MAAM,OAAO;IAClB,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/components/VideoCard.tsx"], "sourcesContent": ["import Link from 'next/link'\r\nimport Image from 'next/image'\r\nimport { Play, Clock, Calendar } from 'lucide-react'\r\nimport { VideoSummary } from '@/types'\r\nimport { formatDuration, formatDate, truncateText, getEffectiveDate } from '@/lib/utils'\r\nimport React, { useState } from 'react';\r\n\r\ninterface VideoCardProps {\r\n  video: VideoSummary;\r\n  showTopic?: boolean;\r\n}\r\n\r\nexport function VideoCard({ video, showTopic = false }: VideoCardProps) {\r\n  const topicPath = video.topic_category || 'general';\r\n  const [showEmbed, setShowEmbed] = useState(false);\r\n\r\n  let displayContent = 'Keywords not available yet...';\r\n  // Add null check for video.llm_response before accessing keywords\r\n  if (video.llm_response && video.llm_response.keywords) {\r\n    if (Array.isArray(video.llm_response.keywords) && video.llm_response.keywords.length > 0) {\r\n      displayContent = video.llm_response.keywords\r\n        .filter(kw => typeof kw === 'string' && kw.trim() !== '')\r\n        .join(', ');\r\n      if (displayContent.length === 0) {\r\n        displayContent = 'Keywords not available yet...';\r\n      }\r\n    } else if (typeof video.llm_response.keywords === 'string' && video.llm_response.keywords.trim() !== '') {\r\n      displayContent = video.llm_response.keywords;\r\n    }\r\n  } else if (video.llm_response && typeof video.llm_response.summary === 'string' && video.llm_response.summary.trim() !== '') {\r\n    // Fallback to summary if keywords are not available, as a simple string\r\n    // displayContent = truncateText(video.llm_response.summary, 100); // Or some other formatting\r\n  } // Further fallback logic can be added if needed\r\n\r\n\r\n  const youtubeEmbedUrl = video.video_id ? `https://www.youtube.com/embed/${video.video_id}` : null;\r\n\r\n  const handleThumbnailError = () => {\r\n    if (youtubeEmbedUrl) {\r\n      setShowEmbed(true);\r\n    }\r\n  };\r\n\r\n  const initialShowEmbed = !video.thumbnail_url && !!youtubeEmbedUrl;\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-200 flex flex-col h-full\">\r\n      <Link href={`/video/${topicPath}/${video.video_id}`} className=\"block\">\r\n        <div className=\"relative aspect-video overflow-hidden\">\r\n          {(showEmbed || initialShowEmbed) && youtubeEmbedUrl ? (\r\n            <iframe\r\n              width=\"100%\"\r\n              height=\"100%\"\r\n              src={youtubeEmbedUrl}\r\n              title={video.title ?? \"YouTube video player\"}\r\n              frameBorder=\"0\"\r\n              allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\r\n              allowFullScreen\r\n              className=\"w-full h-full\"\r\n            ></iframe>\r\n          ) : video.thumbnail_url ? (\r\n            <Image \r\n              src={video.thumbnail_url} \r\n              alt={video.title || 'Video thumbnail'} \r\n              className=\"w-full h-full object-cover\"\r\n              width={640}\r\n              height={360}\r\n              unoptimized={video.thumbnail_url.includes('youtube.com') || video.thumbnail_url.includes('ytimg.com')}\r\n              onError={handleThumbnailError}\r\n            />\r\n          ) : (\r\n            <div className=\"w-full h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center\">\r\n              <Play className=\"w-12 h-12 text-gray-400 dark:text-gray-500\" />\r\n              <span className=\"ml-2 text-gray-500 dark:text-gray-400\">Thumbnail not available</span>\r\n            </div>\r\n          )}\r\n          {!(showEmbed || initialShowEmbed) && video.duration && (\r\n            <div className=\"absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded flex items-center gap-1\">\r\n              <Clock className=\"w-3 h-3\" />\r\n              {formatDuration(video.duration)}\r\n            </div>\r\n          )}\r\n          {!(showEmbed || initialShowEmbed) && (\r\n            <div className=\"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center\">\r\n              <Play className=\"w-12 h-12 text-white opacity-0 hover:opacity-100 transition-opacity duration-200\" />\r\n            </div>\r\n          )}\r\n        </div>\r\n      </Link>\r\n      <div className=\"p-4 flex flex-col flex-grow\">\r\n        <Link href={`/video/${topicPath}/${video.video_id}`} className=\"block mb-1\">\r\n          <h3 className=\"font-semibold text-lg text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 transition-colors line-clamp-2\" title={video.title || 'Untitled Video'}>\r\n            {video.title || 'Untitled Video'}\r\n          </h3>\r\n        </Link>\r\n        <p className=\"text-gray-600 dark:text-gray-400 text-sm mt-1 line-clamp-1\" title={video.channel_name || 'Unknown Channel'}>\r\n          {video.channel_name || 'Unknown Channel'}\r\n        </p>\r\n        \r\n        <div className=\"flex items-center justify-between mt-3 text-sm text-gray-500 dark:text-gray-400\">\r\n          <div className=\"flex items-center gap-1\">\r\n            <Calendar className=\"w-4 h-4\" />\r\n            {formatDate(getEffectiveDate(video))}\r\n          </div>\r\n          {showTopic && video.topic_category && (\r\n            <span className=\"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded-full line-clamp-1\" title={video.topic_category}>\r\n              {video.topic_category}\r\n            </span>\r\n          )}\r\n        </div>\r\n        <div className=\"mt-3 flex-grow\">\r\n          <p className=\"text-gray-700 dark:text-gray-300 text-sm line-clamp-3\" title={displayContent}>\r\n            {truncateText(displayContent, 150)}\r\n          </p>\r\n        </div>\r\n        \r\n        <div className=\"mt-auto pt-3\">\r\n          <Link \r\n            href={`/video/${topicPath}/${video.video_id}`}\r\n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium\"\r\n          >\r\n            Read full summary →\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAEA;AACA;;;;;;;;AAOO,SAAS,UAAU,EAAE,KAAK,EAAE,YAAY,KAAK,EAAkB;;IACpE,MAAM,YAAY,MAAM,cAAc,IAAI;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,iBAAiB;IACrB,kEAAkE;IAClE,IAAI,MAAM,YAAY,IAAI,MAAM,YAAY,CAAC,QAAQ,EAAE;QACrD,IAAI,MAAM,OAAO,CAAC,MAAM,YAAY,CAAC,QAAQ,KAAK,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;YACxF,iBAAiB,MAAM,YAAY,CAAC,QAAQ,CACzC,MAAM,CAAC,CAAA,KAAM,OAAO,OAAO,YAAY,GAAG,IAAI,OAAO,IACrD,IAAI,CAAC;YACR,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,iBAAiB;YACnB;QACF,OAAO,IAAI,OAAO,MAAM,YAAY,CAAC,QAAQ,KAAK,YAAY,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,OAAO,IAAI;YACvG,iBAAiB,MAAM,YAAY,CAAC,QAAQ;QAC9C;IACF,OAAO,IAAI,MAAM,YAAY,IAAI,OAAO,MAAM,YAAY,CAAC,OAAO,KAAK,YAAY,MAAM,YAAY,CAAC,OAAO,CAAC,IAAI,OAAO,IAAI;IAC3H,wEAAwE;IACxE,8FAA8F;IAChG,EAAE,gDAAgD;IAGlD,MAAM,kBAAkB,MAAM,QAAQ,GAAG,CAAC,8BAA8B,EAAE,MAAM,QAAQ,EAAE,GAAG;IAE7F,MAAM,uBAAuB;QAC3B,IAAI,iBAAiB;YACnB,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,CAAC,MAAM,aAAa,IAAI,CAAC,CAAC;IAEnD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,MAAM,QAAQ,EAAE;gBAAE,WAAU;0BAC7D,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,CAAC,aAAa,gBAAgB,KAAK,gCAClC,6LAAC;4BACC,OAAM;4BACN,QAAO;4BACP,KAAK;4BACL,OAAO,MAAM,KAAK,IAAI;4BACtB,aAAY;4BACZ,OAAM;4BACN,eAAe;4BACf,WAAU;;;;;mCAEV,MAAM,aAAa,iBACrB,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,MAAM,aAAa;4BACxB,KAAK,MAAM,KAAK,IAAI;4BACpB,WAAU;4BACV,OAAO;4BACP,QAAQ;4BACR,aAAa,MAAM,aAAa,CAAC,QAAQ,CAAC,kBAAkB,MAAM,aAAa,CAAC,QAAQ,CAAC;4BACzF,SAAS;;;;;iDAGX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAwC;;;;;;;;;;;;wBAG3D,CAAC,CAAC,aAAa,gBAAgB,KAAK,MAAM,QAAQ,kBACjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAChB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ;;;;;;;wBAGjC,CAAC,CAAC,aAAa,gBAAgB,mBAC9B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAKxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,MAAM,QAAQ,EAAE;wBAAE,WAAU;kCAC7D,cAAA,6LAAC;4BAAG,WAAU;4BAAqI,OAAO,MAAM,KAAK,IAAI;sCACtK,MAAM,KAAK,IAAI;;;;;;;;;;;kCAGpB,6LAAC;wBAAE,WAAU;wBAA6D,OAAO,MAAM,YAAY,IAAI;kCACpG,MAAM,YAAY,IAAI;;;;;;kCAGzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;;;;;;;4BAE9B,aAAa,MAAM,cAAc,kBAChC,6LAAC;gCAAK,WAAU;gCAA4G,OAAO,MAAM,cAAc;0CACpJ,MAAM,cAAc;;;;;;;;;;;;kCAI3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;4BAAwD,OAAO;sCACzE,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;;;;;;;;;;;kCAIlC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,MAAM,QAAQ,EAAE;4BAC7C,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAnHgB;KAAA", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/components/TopicSelector.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { TopicTable } from '@/types'\r\nimport { Filter, ChevronDown } from 'lucide-react'\r\n\r\ninterface TopicSelectorProps {\r\n  selectedTopics: string[]\r\n  onTopicsChange: (topics: string[]) => void\r\n}\r\n\r\nexport function TopicSelector({ selectedTopics, onTopicsChange }: TopicSelectorProps) {\r\n  const [topics, setTopics] = useState<TopicTable[]>([])\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const [loading, setLoading] = useState(true)\r\n\r\n  useEffect(() => {\r\n    fetchTopics()\r\n  }, [])\r\n\r\n  const fetchTopics = async () => {\r\n    try {\r\n      const response = await fetch('/api/topics')\r\n      if (response.ok) {\r\n        const data = await response.json()\r\n        setTopics(data.topics)\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching topics:', error)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleTopicToggle = (topicName: string) => {\r\n    const newTopics = selectedTopics.includes(topicName)\r\n      ? selectedTopics.filter(t => t !== topicName)\r\n      : [...selectedTopics, topicName]\r\n    \r\n    onTopicsChange(newTopics)\r\n  }\r\n\r\n  const clearSelection = () => {\r\n    onTopicsChange([])\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"animate-pulse\">\r\n        <div className=\"h-10 bg-gray-200 rounded-lg\"></div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      <button\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"flex items-center justify-between w-full px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <Filter className=\"w-5 h-5 text-gray-500\" />\r\n          <span className=\"text-gray-700\">\r\n            {selectedTopics.length === 0 \r\n              ? 'All Topics' \r\n              : `${selectedTopics.length} topic${selectedTopics.length > 1 ? 's' : ''} selected`\r\n            }\r\n          </span>\r\n        </div>\r\n        <ChevronDown className={`w-5 h-5 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />\r\n      </button>\r\n\r\n      {isOpen && (\r\n        <div className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10 max-h-64 overflow-y-auto\">\r\n          <div className=\"p-2\">\r\n            {selectedTopics.length > 0 && (\r\n              <button\r\n                onClick={clearSelection}\r\n                className=\"w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded\"\r\n              >\r\n                Clear selection\r\n              </button>\r\n            )}\r\n            \r\n            {topics.map((topic) => (\r\n              <label\r\n                key={topic.name}\r\n                className=\"flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer rounded\"\r\n              >\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={selectedTopics.includes(topic.name)}\r\n                  onChange={() => handleTopicToggle(topic.name)}\r\n                  className=\"mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\r\n                />\r\n                <div className=\"flex-1\">\r\n                  <div className=\"text-sm font-medium text-gray-900\">\r\n                    {topic.display_name}\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-500\">\r\n                    {topic.video_count} videos\r\n                  </div>\r\n                </div>\r\n              </label>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;;;AAJA;;;AAWO,SAAS,cAAc,EAAE,cAAc,EAAE,cAAc,EAAsB;;IAClF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,UAAU,KAAK,MAAM;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,YAAY,eAAe,QAAQ,CAAC,aACtC,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM,aACjC;eAAI;YAAgB;SAAU;QAElC,eAAe;IACjB;IAEA,MAAM,iBAAiB;QACrB,eAAe,EAAE;IACnB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAK,WAAU;0CACb,eAAe,MAAM,KAAK,IACvB,eACA,GAAG,eAAe,MAAM,CAAC,MAAM,EAAE,eAAe,MAAM,GAAG,IAAI,MAAM,GAAG,SAAS,CAAC;;;;;;;;;;;;kCAIxF,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAW,CAAC,2CAA2C,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;YAGnG,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,eAAe,MAAM,GAAG,mBACvB,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;wBAKF,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCACC,MAAK;wCACL,SAAS,eAAe,QAAQ,CAAC,MAAM,IAAI;wCAC3C,UAAU,IAAM,kBAAkB,MAAM,IAAI;wCAC5C,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,MAAM,YAAY;;;;;;0DAErB,6LAAC;gDAAI,WAAU;;oDACZ,MAAM,WAAW;oDAAC;;;;;;;;;;;;;;+BAdlB,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;AAwB/B;GAnGgB;KAAA", "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/components/ChannelSelector.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect, useCallback } from 'react'\r\nimport { Users, ChevronDown } from 'lucide-react'\r\n\r\ninterface ChannelSelectorProps {\r\n  selectedChannels: string[]\r\n  onChannelsChange: (channels: string[]) => void\r\n  selectedTopics?: string[]\r\n}\r\n\r\nexport function ChannelSelector({ selectedChannels, onChannelsChange, selectedTopics }: ChannelSelectorProps) {\r\n  const [channels, setChannels] = useState<string[]>([])\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const [loading, setLoading] = useState(true)\r\n\r\n  const fetchChannels = useCallback(async () => {\r\n    try {\r\n      setLoading(true)\r\n      const params = new URLSearchParams()\r\n      if (selectedTopics && selectedTopics.length > 0) {\r\n        params.append('topics', selectedTopics.join(','))\r\n      }\r\n      \r\n      const response = await fetch(`/api/channels?${params}`)\r\n      if (response.ok) {\r\n        const data = await response.json()\r\n        setChannels(data.channels)\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching channels:', error)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }, [selectedTopics])\r\n\r\n  useEffect(() => {\r\n    fetchChannels()\r\n  }, [fetchChannels])\r\n\r\n  const handleChannelToggle = (channelName: string) => {\r\n    const newChannels = selectedChannels.includes(channelName)\r\n      ? selectedChannels.filter(c => c !== channelName)\r\n      : [...selectedChannels, channelName]\r\n    \r\n    onChannelsChange(newChannels)\r\n  }\r\n\r\n  const clearSelection = () => {\r\n    onChannelsChange([])\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"animate-pulse\">\r\n        <div className=\"h-10 bg-gray-200 rounded-lg\"></div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      <div className=\"flex items-center gap-2 mb-2\">\r\n        <Users className=\"w-4 h-4 text-gray-600\" />\r\n        <span className=\"text-sm font-medium text-gray-700\">\r\n          Filter by Channel {selectedChannels.length > 0 && `(${selectedChannels.length})`}\r\n        </span>\r\n      </div>\r\n      \r\n      <div className=\"relative\">\r\n        <button\r\n          onClick={() => setIsOpen(!isOpen)}\r\n          className=\"w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n        >\r\n          <div className=\"flex items-center justify-between\">\r\n            <span className=\"text-sm\">\r\n              {selectedChannels.length === 0\r\n                ? 'All channels'\r\n                : selectedChannels.length === 1\r\n                ? selectedChannels[0]\r\n                : `${selectedChannels.length} channels selected`\r\n              }\r\n            </span>\r\n            <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />\r\n          </div>\r\n        </button>\r\n\r\n        {isOpen && (\r\n          <div className=\"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto\">\r\n            <div className=\"p-2\">\r\n              {selectedChannels.length > 0 && (\r\n                <button\r\n                  onClick={clearSelection}\r\n                  className=\"w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 rounded\"\r\n                >\r\n                  Clear all channels\r\n                </button>\r\n              )}\r\n              \r\n              {channels.length === 0 ? (\r\n                <div className=\"px-3 py-2 text-sm text-gray-500\">\r\n                  No channels found{selectedTopics?.length ? ' for selected topics' : ''}\r\n                </div>\r\n              ) : (\r\n                channels.map((channel) => (\r\n                  <label\r\n                    key={channel}\r\n                    className=\"flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer rounded\"\r\n                  >\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={selectedChannels.includes(channel)}\r\n                      onChange={() => handleChannelToggle(channel)}\r\n                      className=\"mr-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n                    />\r\n                    <span className=\"text-sm text-gray-700 truncate\" title={channel}>\r\n                      {channel}\r\n                    </span>\r\n                  </label>\r\n                ))\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n      \r\n      {/* Click outside to close */}\r\n      {isOpen && (\r\n        <div\r\n          className=\"fixed inset-0 z-40\"\r\n          onClick={() => setIsOpen(false)}\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAWO,SAAS,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAwB;;IAC1G,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAChC,IAAI;gBACF,WAAW;gBACX,MAAM,SAAS,IAAI;gBACnB,IAAI,kBAAkB,eAAe,MAAM,GAAG,GAAG;oBAC/C,OAAO,MAAM,CAAC,UAAU,eAAe,IAAI,CAAC;gBAC9C;gBAEA,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ;gBACtD,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,YAAY,KAAK,QAAQ;gBAC3B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C,SAAU;gBACR,WAAW;YACb;QACF;qDAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG;QAAC;KAAc;IAElB,MAAM,sBAAsB,CAAC;QAC3B,MAAM,cAAc,iBAAiB,QAAQ,CAAC,eAC1C,iBAAiB,MAAM,CAAC,CAAA,IAAK,MAAM,eACnC;eAAI;YAAkB;SAAY;QAEtC,iBAAiB;IACnB;IAEA,MAAM,iBAAiB;QACrB,iBAAiB,EAAE;IACrB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAK,WAAU;;4BAAoC;4BAC/B,iBAAiB,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,iBAAiB,MAAM,CAAC,CAAC,CAAC;;;;;;;;;;;;;0BAIpF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,UAAU,CAAC;wBAC1B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,iBAAiB,MAAM,KAAK,IACzB,iBACA,iBAAiB,MAAM,KAAK,IAC5B,gBAAgB,CAAC,EAAE,GACnB,GAAG,iBAAiB,MAAM,CAAC,kBAAkB,CAAC;;;;;;8CAGpD,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAW,CAAC,6BAA6B,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;;;;;;oBAIvF,wBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;gCAKF,SAAS,MAAM,KAAK,kBACnB,6LAAC;oCAAI,WAAU;;wCAAkC;wCAC7B,gBAAgB,SAAS,yBAAyB;;;;;;2CAGtE,SAAS,GAAG,CAAC,CAAC,wBACZ,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDACC,MAAK;gDACL,SAAS,iBAAiB,QAAQ,CAAC;gDACnC,UAAU,IAAM,oBAAoB;gDACpC,WAAU;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;gDAAiC,OAAO;0DACrD;;;;;;;uCAVE;;;;;;;;;;;;;;;;;;;;;;YAqBlB,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;;;;;;;AAKnC;GA5HgB;KAAA", "debugId": null}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/lib/tokenUtils.ts"], "sourcesContent": ["// Token management utilities for Gemini API\r\nexport const GEMINI_LIMITS = {\r\n  MAX_INPUT_TOKENS: 1_000_000,  // 1M context window for gemini-2.5-flash\r\n  MAX_OUTPUT_TOKENS: 65_536,     // 64K output limit\r\n  SAFETY_MARGIN: 10_000,         // Safety margin for system prompt + overhead\r\n} as const\r\n\r\nexport const MAX_CONTEXT_TOKENS = GEMINI_LIMITS.MAX_INPUT_TOKENS - GEMINI_LIMITS.SAFETY_MARGIN\r\n\r\n/**\r\n * Rough estimation of tokens (more accurate counting would require tiktoken)\r\n * English text is roughly 4 characters per token\r\n */\r\nexport function estimateTokens(text: string): number {\r\n  return Math.ceil(text.length / 4)\r\n}\r\n\r\n/**\r\n * Truncate text to fit within token limit\r\n */\r\nexport function truncateToTokenLimit(text: string, maxTokens: number): string {\r\n  const estimatedTokens = estimateTokens(text)\r\n  if (estimatedTokens <= maxTokens) {\r\n    return text\r\n  }\r\n  \r\n  // Calculate approximate character limit\r\n  const maxChars = maxTokens * 4\r\n  return text.substring(0, maxChars) + '...[truncated]'\r\n}\r\n\r\n/**\r\n * Smart context management for chat API\r\n */\r\nexport function optimizeContextForChat(videos: Array<{\r\n  title: string\r\n  channel: string\r\n  summary: string\r\n  transcript?: string\r\n}>, userMessage: string, conversationHistory: string = ''): {\r\n  contextText: string\r\n  totalEstimatedTokens: number\r\n  truncated: boolean\r\n} {\r\n  const systemPromptTokens = 200\r\n  const userMessageTokens = estimateTokens(userMessage)\r\n  const conversationTokens = estimateTokens(conversationHistory)\r\n  \r\n  const availableTokensForContext = MAX_CONTEXT_TOKENS - systemPromptTokens - userMessageTokens - conversationTokens\r\n  \r\n  let contextText = ''\r\n  let totalTokensUsed = 0\r\n  let truncated = false\r\n  \r\n  for (const video of videos) {\r\n    const summaryText = `Title: ${video.title}\\nChannel: ${video.channel}\\nSummary: ${video.summary}`\r\n    const summaryTokens = estimateTokens(summaryText)\r\n    \r\n    // Always include at least the summary\r\n    let videoContext = summaryText\r\n    let videoTokens = summaryTokens\r\n    \r\n    // Try to include transcript if we have tokens available\r\n    if (video.transcript && video.transcript.trim()) {\r\n      const transcriptTokens = estimateTokens(video.transcript)\r\n      const remainingTokens = availableTokensForContext - totalTokensUsed - videoTokens\r\n      \r\n      if (transcriptTokens <= remainingTokens - 1000) { // Leave 1000 tokens buffer\r\n        // Include full transcript\r\n        videoContext += `\\n\\nFull Transcript:\\n${video.transcript}`\r\n        videoTokens += transcriptTokens\r\n      } else if (remainingTokens > 2000) {\r\n        // Include truncated transcript\r\n        const maxTranscriptTokens = remainingTokens - 1000\r\n        const truncatedTranscript = truncateToTokenLimit(video.transcript, maxTranscriptTokens)\r\n        videoContext += `\\n\\nTranscript (truncated):\\n${truncatedTranscript}`\r\n        videoTokens = summaryTokens + maxTranscriptTokens\r\n        truncated = true\r\n      }\r\n    }\r\n    \r\n    // Check if adding this video would exceed our limit\r\n    if (totalTokensUsed + videoTokens > availableTokensForContext) {\r\n      truncated = true\r\n      break\r\n    }\r\n    \r\n    if (contextText) {\r\n      contextText += '\\n\\n---\\n\\n'\r\n    }\r\n    contextText += videoContext\r\n    totalTokensUsed += videoTokens\r\n  }\r\n  \r\n  return {\r\n    contextText,\r\n    totalEstimatedTokens: systemPromptTokens + userMessageTokens + conversationTokens + totalTokensUsed,\r\n    truncated\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;;;;;AACrC,MAAM,gBAAgB;IAC3B,kBAAkB;IAClB,mBAAmB;IACnB,eAAe;AACjB;AAEO,MAAM,qBAAqB,cAAc,gBAAgB,GAAG,cAAc,aAAa;AAMvF,SAAS,eAAe,IAAY;IACzC,OAAO,KAAK,IAAI,CAAC,KAAK,MAAM,GAAG;AACjC;AAKO,SAAS,qBAAqB,IAAY,EAAE,SAAiB;IAClE,MAAM,kBAAkB,eAAe;IACvC,IAAI,mBAAmB,WAAW;QAChC,OAAO;IACT;IAEA,wCAAwC;IACxC,MAAM,WAAW,YAAY;IAC7B,OAAO,KAAK,SAAS,CAAC,GAAG,YAAY;AACvC;AAKO,SAAS,uBAAuB,MAKrC,EAAE,WAAmB,EAAE,sBAA8B,EAAE;IAKvD,MAAM,qBAAqB;IAC3B,MAAM,oBAAoB,eAAe;IACzC,MAAM,qBAAqB,eAAe;IAE1C,MAAM,4BAA4B,qBAAqB,qBAAqB,oBAAoB;IAEhG,IAAI,cAAc;IAClB,IAAI,kBAAkB;IACtB,IAAI,YAAY;IAEhB,KAAK,MAAM,SAAS,OAAQ;QAC1B,MAAM,cAAc,CAAC,OAAO,EAAE,MAAM,KAAK,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;QACjG,MAAM,gBAAgB,eAAe;QAErC,sCAAsC;QACtC,IAAI,eAAe;QACnB,IAAI,cAAc;QAElB,wDAAwD;QACxD,IAAI,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,IAAI,IAAI;YAC/C,MAAM,mBAAmB,eAAe,MAAM,UAAU;YACxD,MAAM,kBAAkB,4BAA4B,kBAAkB;YAEtE,IAAI,oBAAoB,kBAAkB,MAAM;gBAC9C,0BAA0B;gBAC1B,gBAAgB,CAAC,sBAAsB,EAAE,MAAM,UAAU,EAAE;gBAC3D,eAAe;YACjB,OAAO,IAAI,kBAAkB,MAAM;gBACjC,+BAA+B;gBAC/B,MAAM,sBAAsB,kBAAkB;gBAC9C,MAAM,sBAAsB,qBAAqB,MAAM,UAAU,EAAE;gBACnE,gBAAgB,CAAC,6BAA6B,EAAE,qBAAqB;gBACrE,cAAc,gBAAgB;gBAC9B,YAAY;YACd;QACF;QAEA,oDAAoD;QACpD,IAAI,kBAAkB,cAAc,2BAA2B;YAC7D,YAAY;YACZ;QACF;QAEA,IAAI,aAAa;YACf,eAAe;QACjB;QACA,eAAe;QACf,mBAAmB;IACrB;IAEA,OAAO;QACL;QACA,sBAAsB,qBAAqB,oBAAoB,qBAAqB;QACpF;IACF;AACF", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/components/ContextualChat.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { Send, MessageCircle, Bo<PERSON>, User, AlertTriangle } from 'lucide-react'\r\nimport ReactMarkdown from 'react-markdown'\r\nimport remarkGfm from 'remark-gfm'\r\nimport { VideoSummary } from '@/types' // Use same VideoSummary type as DatabaseService\r\nimport { MAX_CONTEXT_TOKENS, optimizeContextForChat } from '@/lib/tokenUtils'\r\n\r\ninterface ContextualChatProps {\r\n  selectedTopics: string[]\r\n  selectedChannels: string[]\r\n  videos: VideoSummary[]\r\n}\r\n\r\ninterface ChatMessage {\r\n  id: string\r\n  role: 'user' | 'assistant'\r\n  content: string\r\n  timestamp: Date\r\n  contextVideos?: VideoSummary[]\r\n}\r\n\r\nexport function ContextualChat({ selectedTopics, selectedChannels, videos }: ContextualChatProps) {\r\n  const [messages, setMessages] = useState<ChatMessage[]>([])\r\n  const [input, setInput] = useState('')\r\n  const [isLoading, setIsLoading] = useState(false)\r\n  const [sessionId, setSessionId] = useState<string | undefined>(undefined)\r\n\r\n  const getContextDescription = () => {\r\n    const parts = []\r\n    \r\n    if (selectedTopics.length > 0) {\r\n      parts.push(`topics: ${selectedTopics.map(t => t.replace('_videos', '').replace('_', ' ')).join(', ')}`)\r\n    }\r\n    \r\n    if (selectedChannels.length > 0) {\r\n      parts.push(`channels: ${selectedChannels.join(', ')}`)\r\n    }\r\n    \r\n    if (parts.length === 0) {\r\n      return 'all available videos'\r\n    }\r\n    \r\n    return parts.join(' and ')\r\n  }\r\n  const getTokenEstimate = () => {\r\n    if (!input.trim()) return 0\r\n    \r\n    // Prepare context for optimization\r\n    const context = videos.map(video => {\r\n      // Extract summary from llm_response if it's an object with summary, or use summary_text\r\n      let summary = 'No summary available'\r\n      if (video.llm_response && typeof video.llm_response === 'object' && 'summary' in video.llm_response) {\r\n        summary = String(video.llm_response.summary)\r\n      } else if (video.summary_text) {\r\n        summary = video.summary_text\r\n      }\r\n      \r\n      return {\r\n        title: video.title,\r\n        channel: video.channel_name || '',\r\n        summary: summary,\r\n        transcript: video.transcript || undefined\r\n      }\r\n    })\r\n    \r\n    const { totalEstimatedTokens } = optimizeContextForChat(context, input)\r\n    return totalEstimatedTokens\r\n  }\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    if (!input.trim() || isLoading) return\r\n\r\n    const userMessage: ChatMessage = {\r\n      id: Date.now().toString(),\r\n      role: 'user',\r\n      content: input.trim(),\r\n      timestamp: new Date()\r\n    }\r\n\r\n    setMessages(prev => [...prev, userMessage])\r\n    setInput('')\r\n    setIsLoading(true)\r\n\r\n    try {      // Prepare context from ALL current videos with full transcripts\r\n      const context = videos.map(video => {\r\n        // Extract summary from llm_response if it's an object with summary, or use summary_text\r\n        let summary = 'No summary available'\r\n        if (video.llm_response && typeof video.llm_response === 'object' && 'summary' in video.llm_response) {\r\n          summary = String(video.llm_response.summary)\r\n        } else if (video.summary_text) {\r\n          summary = video.summary_text\r\n        }\r\n        \r\n        return {\r\n          title: video.title,\r\n          channel: video.channel_name,\r\n          summary: summary,\r\n          transcript: video.transcript, // Include full transcript instead of just summary\r\n          published: video.published_at\r\n        }\r\n      })\r\n\r\n      const response = await fetch('/api/chat', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify({\r\n          message: input.trim(),\r\n          session_id: sessionId, // Include session_id for conversation memory\r\n          context: {\r\n            videos: context,\r\n            topics: selectedTopics,\r\n            channels: selectedChannels\r\n          }\r\n        })\r\n      })\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to get response')\r\n      }\r\n\r\n      const data = await response.json()\r\n      \r\n      // Store session_id from response for conversation continuity\r\n      if (data.session_id && !sessionId) {\r\n        setSessionId(data.session_id)\r\n      }\r\n      \r\n      const assistantMessage: ChatMessage = {\r\n        id: (Date.now() + 1).toString(),\r\n        role: 'assistant',\r\n        content: data.response || 'Sorry, I could not generate a response.',\r\n        timestamp: new Date(),\r\n        contextVideos: data.contextVideos || []\r\n      }\r\n\r\n      setMessages(prev => [...prev, assistantMessage])\r\n    } catch (error) {\r\n      console.error('Error in chat:', error)\r\n      const errorMessage: ChatMessage = {\r\n        id: (Date.now() + 1).toString(),\r\n        role: 'assistant',\r\n        content: 'Sorry, I encountered an error while processing your request. Please try again.',\r\n        timestamp: new Date()\r\n      }\r\n      setMessages(prev => [...prev, errorMessage])    } finally {\r\n      setIsLoading(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-gray-900 border border-gray-700 rounded-lg shadow-sm h-[1600px] flex flex-col\">\r\n      <div className=\"border-b border-gray-700 p-4\">\r\n        <div className=\"flex items-center gap-2 mb-2\">\r\n          <MessageCircle className=\"w-5 h-5 text-blue-400\" />\r\n          <h3 className=\"font-semibold text-gray-100\">AI Chat Assistant</h3>\r\n        </div>\r\n        <p className=\"text-sm text-gray-300\">\r\n          Ask questions about {getContextDescription()} ({videos.length} videos available)\r\n        </p>\r\n      </div>      <div className=\"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-800 min-h-[600px] max-h-[1200px]\">\r\n        {messages.length === 0 ? (\r\n          <div className=\"text-center text-gray-400 py-8\">\r\n            <Bot className=\"w-12 h-12 mx-auto mb-3 text-gray-500\" />\r\n            <p className=\"text-sm\">Start a conversation about the videos you&apos;ve selected!</p>\r\n            <p className=\"text-xs mt-1\">Try asking: &ldquo;What are the main topics covered?&rdquo;</p>\r\n          </div>\r\n        ) : (\r\n          messages.map((message) => (\r\n            <div\r\n              key={message.id}\r\n              className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\r\n            >\r\n              <div className={`flex gap-2 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : ''}`}>\r\n                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\r\n                  message.role === 'user' ? 'bg-blue-600' : 'bg-gray-600'\r\n                }`}>\r\n                  {message.role === 'user' ? (\r\n                    <User className=\"w-4 h-4 text-white\" />\r\n                  ) : (\r\n                    <Bot className=\"w-4 h-4 text-white\" />\r\n                  )}\r\n                </div>                <div className={`rounded-lg px-3 py-2 ${\r\n                  message.role === 'user'\r\n                    ? 'bg-blue-600 text-white'\r\n                    : 'bg-gray-700 text-gray-100'\r\n                }`}>\r\n                  {message.role === 'assistant' ? (\r\n                    <div className=\"text-sm prose prose-invert prose-sm max-w-none\">\r\n                      <ReactMarkdown \r\n                        remarkPlugins={[remarkGfm]}\r\n                        components={{\r\n                          // Style headers\r\n                          h1: ({ children }) => <h1 className=\"text-lg font-bold mb-2 text-gray-100\">{children}</h1>,\r\n                          h2: ({ children }) => <h2 className=\"text-base font-semibold mb-2 text-gray-100\">{children}</h2>,\r\n                          h3: ({ children }) => <h3 className=\"text-sm font-medium mb-1 text-gray-100\">{children}</h3>,\r\n                          // Style lists\r\n                          ul: ({ children }) => <ul className=\"list-disc list-inside space-y-1 mb-2\">{children}</ul>,\r\n                          ol: ({ children }) => <ol className=\"list-decimal list-inside space-y-1 mb-2\">{children}</ol>,\r\n                          li: ({ children }) => <li className=\"text-gray-100\">{children}</li>,\r\n                          // Style links\r\n                          a: ({ children, href }) => <a href={href} className=\"text-blue-400 hover:text-blue-300 underline\" target=\"_blank\" rel=\"noopener noreferrer\">{children}</a>,\r\n                          // Style code\r\n                          code: ({ children }) => <code className=\"bg-gray-600 px-1 py-0.5 rounded text-xs text-gray-200\">{children}</code>,\r\n                          pre: ({ children }) => <pre className=\"bg-gray-600 p-2 rounded mt-2 mb-2 overflow-x-auto text-xs text-gray-200\">{children}</pre>,\r\n                          // Style paragraphs\r\n                          p: ({ children }) => <p className=\"mb-2 text-gray-100 leading-relaxed\">{children}</p>,\r\n                          // Style blockquotes\r\n                          blockquote: ({ children }) => <blockquote className=\"border-l-4 border-gray-500 pl-3 ml-2 my-2 text-gray-300 italic\">{children}</blockquote>,\r\n                          // Style strong/bold\r\n                          strong: ({ children }) => <strong className=\"font-semibold text-gray-100\">{children}</strong>,\r\n                          // Style emphasis/italic\r\n                          em: ({ children }) => <em className=\"italic text-gray-200\">{children}</em>,\r\n                        }}\r\n                      >\r\n                        {message.content}\r\n                      </ReactMarkdown>\r\n                    </div>\r\n                  ) : (\r\n                    <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\r\n                  )}\r\n                  <p className={`text-xs mt-1 ${\r\n                    message.role === 'user' ? 'text-blue-100' : 'text-gray-400'\r\n                  }`}>\r\n                    {message.timestamp.toLocaleTimeString()}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))\r\n        )}\r\n          {isLoading && (\r\n          <div className=\"flex gap-3\">\r\n            <div className=\"w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center\">\r\n              <Bot className=\"w-4 h-4 text-white\" />\r\n            </div>\r\n            <div className=\"bg-gray-700 rounded-lg px-3 py-2\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]\"></div>\r\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]\"></div>\r\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\r\n              </div>\r\n            </div>\r\n          </div>        )}\r\n      </div>\r\n\r\n      {/* Template Prompts */}\r\n      <div className=\"mt-4 p-4 bg-gray-800 border-t border-gray-600\">\r\n        <h3 className=\"text-lg font-semibold text-gray-100 mb-3\">Template Prompts</h3>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2\">          <button\r\n            onClick={() => {\r\n              setInput(\"Summarize in bullet points the most important points made by these videos\")\r\n              setTimeout(() => {\r\n                const form = document.querySelector('form')\r\n                if (form) form.requestSubmit()\r\n              }, 100)\r\n            }}\r\n            className=\"p-3 text-left bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors duration-200\"\r\n          >\r\n            📝 Summarize in bullet points the most important points made by these videos\r\n          </button>\r\n          <button\r\n            onClick={() => {\r\n              setInput(\"What are the main themes and topics covered across these videos?\")\r\n              setTimeout(() => {\r\n                const form = document.querySelector('form')\r\n                if (form) form.requestSubmit()\r\n              }, 100)\r\n            }}\r\n            className=\"p-3 text-left bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors duration-200\"\r\n          >\r\n            🎯 What are the main themes and topics covered across these videos?\r\n          </button>\r\n          <button\r\n            onClick={() => {\r\n              setInput(\"Compare and contrast the different perspectives presented in these videos\")\r\n              setTimeout(() => {\r\n                const form = document.querySelector('form')\r\n                if (form) form.requestSubmit()\r\n              }, 100)\r\n            }}\r\n            className=\"p-3 text-left bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors duration-200\"\r\n          >\r\n            ⚖️ Compare and contrast the different perspectives presented in these videos\r\n          </button>\r\n          <button\r\n            onClick={() => {\r\n              setInput(\"What actionable insights or recommendations can be drawn from these videos?\")\r\n              setTimeout(() => {\r\n                const form = document.querySelector('form')\r\n                if (form) form.requestSubmit()\r\n              }, 100)\r\n            }}\r\n            className=\"p-3 text-left bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors duration-200\"\r\n          >\r\n            💡 What actionable insights or recommendations can be drawn from these videos?\r\n          </button>\r\n          <button\r\n            onClick={() => {\r\n              setInput(\"Identify any contradictions or disagreements between the videos\")\r\n              setTimeout(() => {\r\n                const form = document.querySelector('form')\r\n                if (form) form.requestSubmit()\r\n              }, 100)\r\n            }}\r\n            className=\"p-3 text-left bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors duration-200\"\r\n          >\r\n            🔍 Identify any contradictions or disagreements between the videos\r\n          </button>\r\n          <button\r\n            onClick={() => {\r\n              setInput(\"What questions do these videos raise that weren't fully answered?\")\r\n              setTimeout(() => {\r\n                const form = document.querySelector('form')\r\n                if (form) form.requestSubmit()\r\n              }, 100)\r\n            }}\r\n            className=\"p-3 text-left bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors duration-200\"\r\n          >\r\n            ❓ What questions do these videos raise that weren&apos;t fully answered?\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <form onSubmit={handleSubmit} className=\"border-t border-gray-700 p-4\">\r\n        <div className=\"flex gap-2\">\r\n          <input\r\n            type=\"text\"\r\n            value={input}\r\n            onChange={(e) => setInput(e.target.value)}\r\n            placeholder={`Ask about ${getContextDescription()}...`}\r\n            className=\"flex-1 px-3 py-2 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-800 text-gray-100 placeholder-gray-400\"\r\n            disabled={isLoading}\r\n          />\r\n          <button\r\n            type=\"submit\"\r\n            disabled={!input.trim() || isLoading}\r\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            aria-label=\"Send message\"\r\n          >\r\n            <Send className=\"w-5 h-5\" />\r\n          </button>\r\n        </div>\r\n      </form>\r\n      <div className=\"p-4 text-sm text-gray-400\">        <p>\r\n          {`Token estimate: ${getTokenEstimate()} / ${MAX_CONTEXT_TOKENS}`}\r\n        </p>\r\n        {getTokenEstimate() > MAX_CONTEXT_TOKENS && (\r\n          <p className=\"flex items-center gap-1 text-red-400\">\r\n            <AlertTriangle className=\"w-4 h-4\" />\r\n            {`Your message is too long. Please shorten it to under ${MAX_CONTEXT_TOKENS} tokens.`}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;AAPA;;;;;;AAuBO,SAAS,eAAe,EAAE,cAAc,EAAE,gBAAgB,EAAE,MAAM,EAAuB;;IAC9F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAE/D,MAAM,wBAAwB;QAC5B,MAAM,QAAQ,EAAE;QAEhB,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAI,CAAC,CAAC,QAAQ,EAAE,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,KAAK,MAAM,IAAI,CAAC,OAAO;QACxG;QAEA,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,MAAM,IAAI,CAAC,CAAC,UAAU,EAAE,iBAAiB,IAAI,CAAC,OAAO;QACvD;QAEA,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO;QACT;QAEA,OAAO,MAAM,IAAI,CAAC;IACpB;IACA,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;QAE1B,mCAAmC;QACnC,MAAM,UAAU,OAAO,GAAG,CAAC,CAAA;YACzB,wFAAwF;YACxF,IAAI,UAAU;YACd,IAAI,MAAM,YAAY,IAAI,OAAO,MAAM,YAAY,KAAK,YAAY,aAAa,MAAM,YAAY,EAAE;gBACnG,UAAU,OAAO,MAAM,YAAY,CAAC,OAAO;YAC7C,OAAO,IAAI,MAAM,YAAY,EAAE;gBAC7B,UAAU,MAAM,YAAY;YAC9B;YAEA,OAAO;gBACL,OAAO,MAAM,KAAK;gBAClB,SAAS,MAAM,YAAY,IAAI;gBAC/B,SAAS;gBACT,YAAY,MAAM,UAAU,IAAI;YAClC;QACF;QAEA,MAAM,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS;QACjE,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,MAAM,WAAW;QAEhC,MAAM,cAA2B;YAC/B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS,MAAM,IAAI;YACnB,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,SAAS;QACT,aAAa;QAEb,IAAI;YACF,MAAM,UAAU,OAAO,GAAG,CAAC,CAAA;gBACzB,wFAAwF;gBACxF,IAAI,UAAU;gBACd,IAAI,MAAM,YAAY,IAAI,OAAO,MAAM,YAAY,KAAK,YAAY,aAAa,MAAM,YAAY,EAAE;oBACnG,UAAU,OAAO,MAAM,YAAY,CAAC,OAAO;gBAC7C,OAAO,IAAI,MAAM,YAAY,EAAE;oBAC7B,UAAU,MAAM,YAAY;gBAC9B;gBAEA,OAAO;oBACL,OAAO,MAAM,KAAK;oBAClB,SAAS,MAAM,YAAY;oBAC3B,SAAS;oBACT,YAAY,MAAM,UAAU;oBAC5B,WAAW,MAAM,YAAY;gBAC/B;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,MAAM,IAAI;oBACnB,YAAY;oBACZ,SAAS;wBACP,QAAQ;wBACR,QAAQ;wBACR,UAAU;oBACZ;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,6DAA6D;YAC7D,IAAI,KAAK,UAAU,IAAI,CAAC,WAAW;gBACjC,aAAa,KAAK,UAAU;YAC9B;YAEA,MAAM,mBAAgC;gBACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,KAAK,QAAQ,IAAI;gBAC1B,WAAW,IAAI;gBACf,eAAe,KAAK,aAAa,IAAI,EAAE;YACzC;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAiB;QACjD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM,eAA4B;gBAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAAK,SAAU;YAC1D,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC;gCAAG,WAAU;0CAA8B;;;;;;;;;;;;kCAE9C,6LAAC;wBAAE,WAAU;;4BAAwB;4BACd;4BAAwB;4BAAG,OAAO,MAAM;4BAAC;;;;;;;;;;;;;YAE5D;0BAAM,6LAAC;gBAAI,WAAU;;oBACxB,SAAS,MAAM,KAAK,kBACnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;+BAG9B,SAAS,GAAG,CAAC,CAAC,wBACZ,6LAAC;4BAEC,WAAW,CAAC,WAAW,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;sCAEpF,cAAA,6LAAC;gCAAI,WAAW,CAAC,uBAAuB,EAAE,QAAQ,IAAI,KAAK,SAAS,qBAAqB,IAAI;;kDAC3F,6LAAC;wCAAI,WAAW,CAAC,sDAAsD,EACrE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,eAC1C;kDACC,QAAQ,IAAI,KAAK,uBAChB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;iEAEhB,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;oCAEb;kDAAgB,6LAAC;wCAAI,WAAW,CAAC,qBAAqB,EAC1D,QAAQ,IAAI,KAAK,SACb,2BACA,6BACJ;;4CACC,QAAQ,IAAI,KAAK,4BAChB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,2LAAA,CAAA,UAAa;oDACZ,eAAe;wDAAC,gJAAA,CAAA,UAAS;qDAAC;oDAC1B,YAAY;wDACV,gBAAgB;wDAChB,IAAI,CAAC,EAAE,QAAQ,EAAE,iBAAK,6LAAC;gEAAG,WAAU;0EAAwC;;;;;;wDAC5E,IAAI,CAAC,EAAE,QAAQ,EAAE,iBAAK,6LAAC;gEAAG,WAAU;0EAA8C;;;;;;wDAClF,IAAI,CAAC,EAAE,QAAQ,EAAE,iBAAK,6LAAC;gEAAG,WAAU;0EAA0C;;;;;;wDAC9E,cAAc;wDACd,IAAI,CAAC,EAAE,QAAQ,EAAE,iBAAK,6LAAC;gEAAG,WAAU;0EAAwC;;;;;;wDAC5E,IAAI,CAAC,EAAE,QAAQ,EAAE,iBAAK,6LAAC;gEAAG,WAAU;0EAA2C;;;;;;wDAC/E,IAAI,CAAC,EAAE,QAAQ,EAAE,iBAAK,6LAAC;gEAAG,WAAU;0EAAiB;;;;;;wDACrD,cAAc;wDACd,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,iBAAK,6LAAC;gEAAE,MAAM;gEAAM,WAAU;gEAA8C,QAAO;gEAAS,KAAI;0EAAuB;;;;;;wDAC7I,aAAa;wDACb,MAAM,CAAC,EAAE,QAAQ,EAAE,iBAAK,6LAAC;gEAAK,WAAU;0EAAyD;;;;;;wDACjG,KAAK,CAAC,EAAE,QAAQ,EAAE,iBAAK,6LAAC;gEAAI,WAAU;0EAA2E;;;;;;wDACjH,mBAAmB;wDACnB,GAAG,CAAC,EAAE,QAAQ,EAAE,iBAAK,6LAAC;gEAAE,WAAU;0EAAsC;;;;;;wDACxE,oBAAoB;wDACpB,YAAY,CAAC,EAAE,QAAQ,EAAE,iBAAK,6LAAC;gEAAW,WAAU;0EAAkE;;;;;;wDACtH,oBAAoB;wDACpB,QAAQ,CAAC,EAAE,QAAQ,EAAE,iBAAK,6LAAC;gEAAO,WAAU;0EAA+B;;;;;;wDAC3E,wBAAwB;wDACxB,IAAI,CAAC,EAAE,QAAQ,EAAE,iBAAK,6LAAC;gEAAG,WAAU;0EAAwB;;;;;;oDAC9D;8DAEC,QAAQ,OAAO;;;;;;;;;;qEAIpB,6LAAC;gDAAE,WAAU;0DAA+B,QAAQ,OAAO;;;;;;0DAE7D,6LAAC;gDAAE,WAAW,CAAC,aAAa,EAC1B,QAAQ,IAAI,KAAK,SAAS,kBAAkB,iBAC5C;0DACC,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;2BAtDtC,QAAQ,EAAE;;;;;oBA6DlB,2BACD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;4BAAwC;0CAAU,6LAAC;gCAC9D,SAAS;oCACP,SAAS;oCACT,WAAW;wCACT,MAAM,OAAO,SAAS,aAAa,CAAC;wCACpC,IAAI,MAAM,KAAK,aAAa;oCAC9B,GAAG;gCACL;gCACA,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS;oCACP,SAAS;oCACT,WAAW;wCACT,MAAM,OAAO,SAAS,aAAa,CAAC;wCACpC,IAAI,MAAM,KAAK,aAAa;oCAC9B,GAAG;gCACL;gCACA,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS;oCACP,SAAS;oCACT,WAAW;wCACT,MAAM,OAAO,SAAS,aAAa,CAAC;wCACpC,IAAI,MAAM,KAAK,aAAa;oCAC9B,GAAG;gCACL;gCACA,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS;oCACP,SAAS;oCACT,WAAW;wCACT,MAAM,OAAO,SAAS,aAAa,CAAC;wCACpC,IAAI,MAAM,KAAK,aAAa;oCAC9B,GAAG;gCACL;gCACA,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS;oCACP,SAAS;oCACT,WAAW;wCACT,MAAM,OAAO,SAAS,aAAa,CAAC;wCACpC,IAAI,MAAM,KAAK,aAAa;oCAC9B,GAAG;gCACL;gCACA,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS;oCACP,SAAS;oCACT,WAAW;wCACT,MAAM,OAAO,SAAS,aAAa,CAAC;wCACpC,IAAI,MAAM,KAAK,aAAa;oCAC9B,GAAG;gCACL;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAML,6LAAC;gBAAK,UAAU;gBAAc,WAAU;0BACtC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,aAAa,CAAC,UAAU,EAAE,wBAAwB,GAAG,CAAC;4BACtD,WAAU;4BACV,UAAU;;;;;;sCAEZ,6LAAC;4BACC,MAAK;4BACL,UAAU,CAAC,MAAM,IAAI,MAAM;4BAC3B,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAItB,6LAAC;gBAAI,WAAU;;oBAA4B;kCAAQ,6LAAC;kCAC/C,CAAC,gBAAgB,EAAE,mBAAmB,GAAG,EAAE,2HAAA,CAAA,qBAAkB,EAAE;;;;;;oBAEjE,qBAAqB,2HAAA,CAAA,qBAAkB,kBACtC,6LAAC;wBAAE,WAAU;;0CACX,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;4BACxB,CAAC,qDAAqD,EAAE,2HAAA,CAAA,qBAAkB,CAAC,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;AAMjG;GAjVgB;KAAA", "debugId": null}}, {"offset": {"line": 1579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/components/SearchComponent.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { Search, Loader2, X } from 'lucide-react'\r\nimport { SearchResult } from '@/types'\r\nimport { VideoCard } from './VideoCard'\r\n\r\ninterface SearchComponentProps {\r\n  selectedTopics?: string[]\r\n  onResultsChange?: (results: SearchResult[]) => void\r\n}\r\n\r\nexport function SearchComponent({ selectedTopics = [], onResultsChange }: SearchComponentProps) {\r\n  const [query, setQuery] = useState('')\r\n  const [results, setResults] = useState<SearchResult[]>([])\r\n  const [isLoading, setIsLoading] = useState(false)\r\n  const [hasSearched, setHasSearched] = useState(false)\r\n\r\n  const handleSearch = async (searchQuery: string = query) => {\r\n    if (!searchQuery.trim()) return\r\n\r\n    setIsLoading(true)\r\n    setHasSearched(true)\r\n\r\n    try {\r\n      const response = await fetch('/api/search', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          query: searchQuery.trim(),\r\n          topicFilters: selectedTopics,\r\n          limit: 20\r\n        })\r\n      })\r\n\r\n      if (!response.ok) throw new Error('Search failed')\r\n\r\n      const data = await response.json()\r\n      setResults(data.results || [])\r\n      onResultsChange?.(data.results || [])\r\n    } catch (error) {\r\n      console.error('Search error:', error)\r\n      setResults([])\r\n      onResultsChange?.([])\r\n    } finally {\r\n      setIsLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    handleSearch()\r\n  }\r\n\r\n  const clearSearch = () => {\r\n    setQuery('')\r\n    setResults([])\r\n    setHasSearched(false)\r\n    onResultsChange?.([])\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Search Form */}\r\n      <form onSubmit={handleSubmit} className=\"relative\">\r\n        <div className=\"relative\">\r\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\r\n          <input\r\n            type=\"text\"\r\n            value={query}\r\n            onChange={(e) => setQuery(e.target.value)}\r\n            placeholder=\"Search YouTube video summaries...\"\r\n            className=\"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n            disabled={isLoading}\r\n          />          {query && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={clearSearch}\r\n              className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\r\n              aria-label=\"Clear search\"\r\n            >\r\n              <X className=\"w-5 h-5\" />\r\n            </button>\r\n          )}\r\n        </div>\r\n        \r\n        {selectedTopics.length > 0 && (\r\n          <div className=\"mt-2 flex flex-wrap gap-1\">\r\n            <span className=\"text-sm text-gray-600\">Searching in:</span>\r\n            {selectedTopics.map((topic) => (\r\n              <span\r\n                key={topic}\r\n                className=\"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full\"\r\n              >\r\n                {topic}\r\n              </span>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </form>\r\n\r\n      {/* Loading State */}\r\n      {isLoading && (\r\n        <div className=\"flex items-center justify-center py-8\">\r\n          <Loader2 className=\"w-8 h-8 animate-spin text-blue-600\" />\r\n          <span className=\"ml-2 text-gray-600\">Searching videos...</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Search Results */}\r\n      {hasSearched && !isLoading && (\r\n        <div>\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900\">\r\n              Search Results\r\n            </h3>\r\n            <span className=\"text-sm text-gray-600\">\r\n              {results.length} video{results.length !== 1 ? 's' : ''} found\r\n            </span>\r\n          </div>\r\n\r\n          {results.length === 0 ? (\r\n            <div className=\"text-center py-8 text-gray-500\">\r\n              <Search className=\"w-12 h-12 mx-auto mb-4 text-gray-300\" />\r\n              <p>No videos found for your search.</p>\r\n              <p className=\"text-sm mt-2\">Try different keywords or adjust your topic filters.</p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\r\n              {results.map((result) => (\r\n                <div key={result.video.id} className=\"relative\">\r\n                  <VideoCard video={result.video} showTopic />\r\n                  {result.similarity_score && (\r\n                    <div className=\"absolute top-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded-full\">\r\n                      {Math.round(result.similarity_score * 100)}% match\r\n                    </div>\r\n                  )}\r\n                  {result.relevant_transcript_chunks && result.relevant_transcript_chunks.length > 0 && (\r\n                    <div className=\"mt-2 p-3 bg-gray-50 rounded-lg\">\r\n                      <p className=\"text-xs text-gray-600 mb-1\">Relevant excerpt:</p>                      <p className=\"text-sm text-gray-800 italic\">\r\n                        &ldquo;{result.relevant_transcript_chunks[0].substring(0, 150)}...&rdquo;\r\n                      </p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;;;AALA;;;;AAYO,SAAS,gBAAgB,EAAE,iBAAiB,EAAE,EAAE,eAAe,EAAwB;;IAC5F,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,OAAO,cAAsB,KAAK;QACrD,IAAI,CAAC,YAAY,IAAI,IAAI;QAEzB,aAAa;QACb,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,YAAY,IAAI;oBACvB,cAAc;oBACd,OAAO;gBACT;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,WAAW,KAAK,OAAO,IAAI,EAAE;YAC7B,kBAAkB,KAAK,OAAO,IAAI,EAAE;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,WAAW,EAAE;YACb,kBAAkB,EAAE;QACtB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,WAAW,EAAE;QACb,eAAe;QACf,kBAAkB,EAAE;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,aAAY;gCACZ,WAAU;gCACV,UAAU;;;;;;4BACV;4BAAW,uBACX,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAKlB,eAAe,MAAM,GAAG,mBACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;4BACvC,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;;;;;;;;;;;;;YAWd,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAK,WAAU;kCAAqB;;;;;;;;;;;;YAKxC,eAAe,CAAC,2BACf,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAK,WAAU;;oCACb,QAAQ,MAAM;oCAAC;oCAAO,QAAQ,MAAM,KAAK,IAAI,MAAM;oCAAG;;;;;;;;;;;;;oBAI1D,QAAQ,MAAM,KAAK,kBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;6CAG9B,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;gCAA0B,WAAU;;kDACnC,6LAAC,kIAAA,CAAA,YAAS;wCAAC,OAAO,OAAO,KAAK;wCAAE,SAAS;;;;;;oCACxC,OAAO,gBAAgB,kBACtB,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,KAAK,CAAC,OAAO,gBAAgB,GAAG;4CAAK;;;;;;;oCAG9C,OAAO,0BAA0B,IAAI,OAAO,0BAA0B,CAAC,MAAM,GAAG,mBAC/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;4CAAqB;0DAAsB,6LAAC;gDAAE,WAAU;;oDAA+B;oDACvH,OAAO,0BAA0B,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG;oDAAK;;;;;;;;;;;;;;+BAV7D,OAAO,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;AAsBzC;GA5IgB;KAAA", "debugId": null}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/components/DateFilter.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { Calendar, X } from 'lucide-react'\r\n\r\ninterface DateFilterProps {\r\n  startDate: string\r\n  endDate: string\r\n  onDateChange: (startDate: string, endDate: string) => void\r\n  onClear: () => void\r\n}\r\n\r\nexport function DateFilter({ startDate, endDate, onDateChange, onClear }: DateFilterProps) {\r\n  const [isExpanded, setIsExpanded] = useState(false)\r\n\r\n  const handleStartDateChange = (date: string) => {\r\n    onDateChange(date, endDate)\r\n  }\r\n\r\n  const handleEndDateChange = (date: string) => {\r\n    onDateChange(startDate, date)\r\n  }\r\n\r\n  const getPresetDates = (days: number) => {\r\n    const end = new Date()\r\n    const start = new Date()\r\n    start.setDate(start.getDate() - days)\r\n    \r\n    return {\r\n      start: start.toISOString().split('T')[0],\r\n      end: end.toISOString().split('T')[0]\r\n    }\r\n  }\r\n\r\n  const applyPreset = (days: number) => {\r\n    const { start, end } = getPresetDates(days)\r\n    onDateChange(start, end)\r\n  }\r\n\r\n  const hasDateFilter = startDate || endDate\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      <button\r\n        onClick={() => setIsExpanded(!isExpanded)}\r\n        className={`flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors ${\r\n          hasDateFilter \r\n            ? 'bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-900 dark:border-blue-700 dark:text-blue-300'\r\n            : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700'\r\n        }`}\r\n      >\r\n        <Calendar className=\"w-4 h-4\" />\r\n        <span className=\"text-sm font-medium\">\r\n          {hasDateFilter ? 'Date Filter Active' : 'Filter by Date'}\r\n        </span>        {hasDateFilter && (\r\n          <button\r\n            onClick={(e) => {\r\n              e.stopPropagation()\r\n              onClear()\r\n            }}\r\n            className=\"ml-1 hover:bg-blue-100 dark:hover:bg-blue-800 rounded p-1\"\r\n            title=\"Clear date filter\"\r\n            aria-label=\"Clear date filter\"\r\n          >\r\n            <X className=\"w-3 h-3\" />\r\n          </button>\r\n        )}\r\n      </button>\r\n\r\n      {isExpanded && (\r\n        <div className=\"absolute top-full left-0 mt-2 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10 min-w-80\">\r\n          <div className=\"space-y-4\">\r\n            <h3 className=\"font-medium text-gray-900 dark:text-gray-100\">Filter by Date Range</h3>\r\n            \r\n            {/* Preset buttons */}\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              <button\r\n                onClick={() => applyPreset(7)}\r\n                className=\"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded\"\r\n              >\r\n                Last 7 days\r\n              </button>\r\n              <button\r\n                onClick={() => applyPreset(30)}\r\n                className=\"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded\"\r\n              >\r\n                Last 30 days\r\n              </button>\r\n              <button\r\n                onClick={() => applyPreset(90)}\r\n                className=\"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded\"\r\n              >\r\n                Last 3 months\r\n              </button>\r\n              <button\r\n                onClick={() => applyPreset(365)}\r\n                className=\"px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded\"\r\n              >\r\n                Last year\r\n              </button>\r\n            </div>\r\n\r\n            {/* Custom date inputs */}\r\n            <div className=\"grid grid-cols-2 gap-3\">\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                  Start Date\r\n                </label>                <input\r\n                  type=\"date\"\r\n                  value={startDate}\r\n                  onChange={(e) => handleStartDateChange(e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n                  title=\"Select start date\"\r\n                  placeholder=\"Start date\"\r\n                />\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                  End Date\r\n                </label>                <input\r\n                  type=\"date\"\r\n                  value={endDate}\r\n                  onChange={(e) => handleEndDateChange(e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n                  title=\"Select end date\"\r\n                  placeholder=\"End date\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Clear and Close buttons */}\r\n            <div className=\"flex justify-between pt-2\">\r\n              <button\r\n                onClick={onClear}\r\n                className=\"text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n              >\r\n                Clear filters\r\n              </button>\r\n              <button\r\n                onClick={() => setIsExpanded(false)}\r\n                className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700\"\r\n              >\r\n                Apply\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAYO,SAAS,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAmB;;IACvF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,wBAAwB,CAAC;QAC7B,aAAa,MAAM;IACrB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,aAAa,WAAW;IAC1B;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,IAAI;QAClB,MAAM,OAAO,CAAC,MAAM,OAAO,KAAK;QAEhC,OAAO;YACL,OAAO,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACxC,KAAK,IAAI,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACtC;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,eAAe;QACtC,aAAa,OAAO;IACtB;IAEA,MAAM,gBAAgB,aAAa;IAEnC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,cAAc,CAAC;gBAC9B,WAAW,CAAC,sEAAsE,EAChF,gBACI,sGACA,2IACJ;;kCAEF,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAK,WAAU;kCACb,gBAAgB,uBAAuB;;;;;;oBACnC;oBAAS,+BACd,6LAAC;wBACC,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB;wBACF;wBACA,WAAU;wBACV,OAAM;wBACN,cAAW;kCAEX,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAKlB,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA+C;;;;;;sCAG7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;wCAE3E;sDAAgB,6LAAC;4CACvB,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;4CACrD,WAAU;4CACV,OAAM;4CACN,aAAY;;;;;;;;;;;;8CAGhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;wCAE3E;sDAAgB,6LAAC;4CACvB,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACnD,WAAU;4CACV,OAAM;4CACN,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA1IgB;KAAA", "debugId": null}}, {"offset": {"line": 2166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/components/ui/tabs.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport * as React from 'react'\r\nimport * as TabsPrimitive from '@radix-ui/react-tabs'\r\nimport { cn } from '@/lib/utils'\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      'inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback, useRef } from 'react'\nimport { VideoSummary } from '@/types'\nimport { VideoCard } from '@/components/VideoCard'\nimport { TopicSelector } from '@/components/TopicSelector'\nimport { ChannelSelector } from '@/components/ChannelSelector'\nimport { ContextualChat } from '@/components/ContextualChat'\nimport { SearchComponent } from '@/components/SearchComponent'\nimport { DateFilter } from '@/components/DateFilter'\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Play, TrendingUp, Clock, MessageCircle } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function Home() {\n  const [recentVideos, setRecentVideos] = useState<VideoSummary[]>([])\n  const [selectedTopics, setSelectedTopics] = useState<string[]>([])\n  const [selectedChannels, setSelectedChannels] = useState<string[]>([])\n  const [loading, setLoading] = useState(true)\n  const [loadingMore, setLoadingMore] = useState(false)\n  const [startDate, setStartDate] = useState('')\n  const [endDate, setEndDate] = useState('')\n  const [hasMoreVideos, setHasMoreVideos] = useState(true)\n  \n  // Use refs to avoid dependency issues in useCallback\n  const currentOffsetRef = useRef(0)\n  const loadedVideoIdsRef = useRef<Set<string>>(new Set())\n  \n  // Use refs for filter values to avoid infinite loops\n  const selectedTopicsRef = useRef<string[]>([])\n  const selectedChannelsRef = useRef<string[]>([])\n  const startDateRef = useRef('')\n  const endDateRef = useRef('')\n  const isInitialMount = useRef(true)\n  \n  // Update refs when state changes\n  selectedTopicsRef.current = selectedTopics\n  selectedChannelsRef.current = selectedChannels\n  startDateRef.current = startDate\n  endDateRef.current = endDate\n\n  const fetchRecentVideos = useCallback(async (isLoadMore = false) => {\n    try {\n      console.log('🔍 fetchRecentVideos called with isLoadMore:', isLoadMore);\n      if (!isLoadMore) {\n        setLoading(true);\n        currentOffsetRef.current = 0;\n        loadedVideoIdsRef.current = new Set(); // Clear loaded video IDs on fresh fetch\n      } else {\n        setLoadingMore(true);\n      }\n      \n      const params = new URLSearchParams();\n      \n      // Check if any filters are applied using refs for stable callback\n      const hasTopicFilters = selectedTopicsRef.current.length > 0;\n      const hasChannelFilters = selectedChannelsRef.current.length > 0;\n      const hasDateFilters = startDateRef.current || endDateRef.current;\n      const hasAnyFilters = hasTopicFilters || hasChannelFilters || hasDateFilters;\n      \n      let endpoint = '/api/videos/recent'; // Default for no filters\n      \n      if (hasAnyFilters) {\n        // Use filtered endpoint when filters are applied\n        endpoint = '/api/videos/filtered';\n        \n        if (hasTopicFilters) {\n          params.append('topics', selectedTopicsRef.current.join(','));\n        }\n        \n        if (hasChannelFilters) {\n          params.append('channels', selectedChannelsRef.current.join(','));\n        }\n\n        if (startDateRef.current) {\n          params.append('startDate', startDateRef.current);\n        }\n\n        if (endDateRef.current) {\n          params.append('endDate', endDateRef.current);\n        }\n        \n        params.append('offset', isLoadMore ? currentOffsetRef.current.toString() : '0');\n      }\n      \n      params.append('limit', '20'); // Always fetch 20 videos\n      \n      console.log('🌐 Making request to:', `${endpoint}?${params}`);\n      const response = await fetch(`${endpoint}?${params}`);\n      console.log('📨 Response status:', response.status, response.ok);\n      \n      if (response.ok) {\n        const data = await response.json();\n        console.log('📊 Response data:', data);\n        const newVideos = data.videos || [];\n        console.log('🎥 New videos count:', newVideos.length);\n        \n        // Filter out any videos we\\'ve already loaded\n        const uniqueNewVideos = newVideos.filter((video: VideoSummary) => \n          !loadedVideoIdsRef.current.has(video.id)\n        );\n        \n        // Sort videos by published_at in descending order (most recent first)\n        // Handle data quality issue: ignore fake published_at dates that match created_at\n        const sortedNewVideos = uniqueNewVideos.sort((a: VideoSummary, b: VideoSummary) => {\n          const isRealDateA = a.published_at && a.created_at && a.published_at !== a.created_at;\n          const isRealDateB = b.published_at && b.created_at && b.published_at !== b.created_at;\n\n          const dateA = isRealDateA ? new Date(a.published_at).getTime() : 0;\n          const dateB = isRealDateB ? new Date(b.published_at).getTime() : 0;\n          return dateB - dateA;\n        });\n        \n        if (isLoadMore) {\n          setRecentVideos(prev => {\n            const combined = [...prev, ...sortedNewVideos];\n            // Additional deduplication at React level\n            const seen = new Set<string>();\n            return combined.filter(video => {\n              if (seen.has(video.id)) {\n                return false;\n              }\n              seen.add(video.id);\n              return true;\n            });\n          });\n          currentOffsetRef.current = currentOffsetRef.current + 20;\n        } else {\n          console.log('🔄 Setting recentVideos to:', sortedNewVideos.length, 'videos');\n          setRecentVideos(sortedNewVideos);\n          console.log('🔄 setRecentVideos called successfully');\n          currentOffsetRef.current = 20;\n        }\n        \n        // Update loaded video IDs set\n        sortedNewVideos.forEach((video: VideoSummary) => {\n          loadedVideoIdsRef.current.add(video.id);\n        });\n        \n        // Determine if there are more videos to load\n        if (hasAnyFilters) {\n          setHasMoreVideos(sortedNewVideos.length >= 20);\n        } else {\n          // For recent videos (no filters), pagination is not supported by this logic\n          setHasMoreVideos(false);\n        }\n      } else {\n        console.error('❌ Response not OK:', response.status, response.statusText);\n        const errorText = await response.text();\n        console.error('❌ Error response body:', errorText);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching recent videos:', error);\n      console.error('❌ Error details:', {\n        message: (error as Error).message,\n        stack: (error as Error).stack,\n        name: (error as Error).name\n      });\n    } finally {\n      setLoading(false);\n      setLoadingMore(false);\n      console.log('✅ fetchRecentVideos completed');\n    }\n  }, []); // Empty dependency array is correct - we use refs to avoid stale closures\n\n  const handleLoadMore = () => {\n    // Only allow load more when filters are applied (filtered endpoint supports pagination)\n    const hasAnyFilters = selectedTopicsRef.current.length > 0 || \n                         selectedChannelsRef.current.length > 0 || \n                         startDateRef.current || \n                         endDateRef.current\n    \n    if (!loadingMore && hasMoreVideos && hasAnyFilters) {\n      fetchRecentVideos(true)\n    }\n  }\n\n  const handleDateChange = (newStartDate: string, newEndDate: string) => {\n    setStartDate(newStartDate)\n    setEndDate(newEndDate)\n  }\n\n  const handleDateClear = () => {\n    setStartDate('')\n    setEndDate('')\n  }\n\n  useEffect(() => {\n    // Initial fetch on component mount\n    console.log('🚀 Component mounted, calling fetchRecentVideos')\n    fetchRecentVideos()\n    isInitialMount.current = false // Mark that initial mount is complete\n  }, [fetchRecentVideos]) // Include fetchRecentVideos dependency\n\n  // Trigger refresh when filters change\n  useEffect(() => {\n    // Skip the initial render (which is handled by the mount effect above)\n    if (isInitialMount.current) {\n      console.log('⏭️ Skipping filter effect on initial mount')\n      return\n    }\n    console.log('🔄 Filters changed, calling fetchRecentVideos')\n    fetchRecentVideos()\n  }, [selectedTopics, selectedChannels, startDate, endDate, fetchRecentVideos])\n\n  // Debug effect to track recentVideos state changes\n  useEffect(() => {\n    console.log('🎬 recentVideos state changed:', {\n      length: recentVideos.length,\n      firstVideo: recentVideos[0]?.title,\n      loading,\n      loadingMore\n    })\n  }, [recentVideos, loading, loadingMore])\n\n  return (\n    <div className=\"w-full\">\n      {/* Hero Section */}\n      <div className=\"text-center py-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg mb-8\">\n        <h1 className=\"text-4xl font-bold mb-4\">\n          YouTube Summaries AI Explorer\n        </h1>\n        <p className=\"text-xl mb-8 max-w-3xl mx-auto\">\n          Discover, search, and chat with AI about YouTube video content. \n          Get instant insights from thousands of video summaries powered by Google Gemini.\n        </p>\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Link\n            href=\"/search\"\n            className=\"inline-flex items-center gap-2 bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\"\n          >\n            <Play className=\"w-5 h-5\" />\n            Explore Videos\n          </Link>\n          <Link\n            href=\"/chat\"\n            className=\"inline-flex items-center gap-2 bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-400 transition-colors\"\n          >\n            <MessageCircle className=\"w-5 h-5\" />\n            Chat with AI\n          </Link>\n        </div>\n      </div>\n\n      {/* Main Content Tabs */}\n      <Tabs defaultValue=\"recent\" className=\"w-full\">\n        <TabsList className=\"grid w-full grid-cols-2\">\n          <TabsTrigger value=\"recent\" className=\"flex items-center gap-2\">\n            <Clock className=\"w-4 h-4\" />\n            Recent Videos\n          </TabsTrigger>\n          <TabsTrigger value=\"search\" className=\"flex items-center gap-2\">\n            <TrendingUp className=\"w-4 h-4\" />\n            Search & Explore\n          </TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"recent\" className=\"space-y-6\">\n          {/* Date Filter */}\n          <div className=\"flex justify-center\">\n            <DateFilter\n              startDate={startDate}\n              endDate={endDate}\n              onDateChange={handleDateChange}\n              onClear={handleDateClear}\n            />\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <TopicSelector\n              selectedTopics={selectedTopics}\n              onTopicsChange={setSelectedTopics}\n            />\n            <ChannelSelector\n              selectedChannels={selectedChannels}\n              onChannelsChange={setSelectedChannels}\n              selectedTopics={selectedTopics}\n            />\n          </div>\n\n          {loading ? (\n            <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n              {[...Array(6)].map((_, i) => (\n                <div key={i} className=\"animate-pulse\">\n                  <div className=\"bg-gray-200 h-48 rounded-lg mb-4\"></div>\n                  <div className=\"bg-gray-200 h-4 rounded mb-2\"></div>\n                  <div className=\"bg-gray-200 h-4 rounded w-3/4\"></div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <>\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-2xl font-bold text-gray-900\">\n                  {selectedTopics.length > 0 || selectedChannels.length > 0 ? 'Filtered Videos' : 'Recent Videos'}\n                </h2>\n                <span className=\"text-gray-600\">\n                  {recentVideos.length} videos\n                </span>\n              </div>\n\n              {(() => {\n                console.log('🎯 Current recentVideos.length:', recentVideos.length)\n                console.log('🎯 Current recentVideos:', recentVideos)\n                return recentVideos.length === 0\n              })() ? (\n                <div className=\"text-center py-12 text-gray-500\">\n                  <Play className=\"w-16 h-16 mx-auto mb-4 text-gray-300\" />\n                  <h3 className=\"text-lg font-medium mb-2\">No videos found</h3>\n                  <p>Try adjusting your topic or channel filters or check back later for new content.</p>\n                </div>\n              ) : (\n                <div className=\"space-y-6\">\n                  <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n                    {recentVideos.map((video) => (\n                      <VideoCard key={video.id} video={video} showTopic />\n                    ))}\n                  </div>\n                  \n                  {/* Load More Button */}\n                  {hasMoreVideos && (\n                    <div className=\"flex justify-center pt-6\">\n                      <button\n                        onClick={handleLoadMore}\n                        disabled={loadingMore}\n                        className=\"px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors flex items-center gap-2\"\n                      >\n                        {loadingMore ? (\n                          <>\n                            <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent\"></div>\n                            Loading more...\n                          </>\n                        ) : (\n                          'Load More Videos'\n                        )}\n                      </button>\n                    </div>\n                  )}\n                  \n                  {/* Contextual Chat Section */}\n                  <div className=\"border-t pt-6\">\n                    <ContextualChat\n                      selectedTopics={selectedTopics}\n                      selectedChannels={selectedChannels}\n                      videos={recentVideos}\n                    />\n                  </div>\n                </div>\n              )}\n            </>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"search\" className=\"space-y-6\">\n          <SearchComponent selectedTopics={selectedTopics} />\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAZA;;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qDAAqD;IACrD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe,IAAI;IAElD,qDAAqD;IACrD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAY,EAAE;IAC7C,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAY,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,iCAAiC;IACjC,kBAAkB,OAAO,GAAG;IAC5B,oBAAoB,OAAO,GAAG;IAC9B,aAAa,OAAO,GAAG;IACvB,WAAW,OAAO,GAAG;IAErB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,OAAO,aAAa,KAAK;YAC7D,IAAI;gBACF,QAAQ,GAAG,CAAC,gDAAgD;gBAC5D,IAAI,CAAC,YAAY;oBACf,WAAW;oBACX,iBAAiB,OAAO,GAAG;oBAC3B,kBAAkB,OAAO,GAAG,IAAI,OAAO,wCAAwC;gBACjF,OAAO;oBACL,eAAe;gBACjB;gBAEA,MAAM,SAAS,IAAI;gBAEnB,kEAAkE;gBAClE,MAAM,kBAAkB,kBAAkB,OAAO,CAAC,MAAM,GAAG;gBAC3D,MAAM,oBAAoB,oBAAoB,OAAO,CAAC,MAAM,GAAG;gBAC/D,MAAM,iBAAiB,aAAa,OAAO,IAAI,WAAW,OAAO;gBACjE,MAAM,gBAAgB,mBAAmB,qBAAqB;gBAE9D,IAAI,WAAW,sBAAsB,yBAAyB;gBAE9D,IAAI,eAAe;oBACjB,iDAAiD;oBACjD,WAAW;oBAEX,IAAI,iBAAiB;wBACnB,OAAO,MAAM,CAAC,UAAU,kBAAkB,OAAO,CAAC,IAAI,CAAC;oBACzD;oBAEA,IAAI,mBAAmB;wBACrB,OAAO,MAAM,CAAC,YAAY,oBAAoB,OAAO,CAAC,IAAI,CAAC;oBAC7D;oBAEA,IAAI,aAAa,OAAO,EAAE;wBACxB,OAAO,MAAM,CAAC,aAAa,aAAa,OAAO;oBACjD;oBAEA,IAAI,WAAW,OAAO,EAAE;wBACtB,OAAO,MAAM,CAAC,WAAW,WAAW,OAAO;oBAC7C;oBAEA,OAAO,MAAM,CAAC,UAAU,aAAa,iBAAiB,OAAO,CAAC,QAAQ,KAAK;gBAC7E;gBAEA,OAAO,MAAM,CAAC,SAAS,OAAO,yBAAyB;gBAEvD,QAAQ,GAAG,CAAC,yBAAyB,GAAG,SAAS,CAAC,EAAE,QAAQ;gBAC5D,MAAM,WAAW,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE,QAAQ;gBACpD,QAAQ,GAAG,CAAC,uBAAuB,SAAS,MAAM,EAAE,SAAS,EAAE;gBAE/D,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,QAAQ,GAAG,CAAC,qBAAqB;oBACjC,MAAM,YAAY,KAAK,MAAM,IAAI,EAAE;oBACnC,QAAQ,GAAG,CAAC,wBAAwB,UAAU,MAAM;oBAEpD,8CAA8C;oBAC9C,MAAM,kBAAkB,UAAU,MAAM;+EAAC,CAAC,QACxC,CAAC,kBAAkB,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE;;oBAGzC,sEAAsE;oBACtE,kFAAkF;oBAClF,MAAM,kBAAkB,gBAAgB,IAAI;+EAAC,CAAC,GAAiB;4BAC7D,MAAM,cAAc,EAAE,YAAY,IAAI,EAAE,UAAU,IAAI,EAAE,YAAY,KAAK,EAAE,UAAU;4BACrF,MAAM,cAAc,EAAE,YAAY,IAAI,EAAE,UAAU,IAAI,EAAE,YAAY,KAAK,EAAE,UAAU;4BAErF,MAAM,QAAQ,cAAc,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;4BACjE,MAAM,QAAQ,cAAc,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;4BACjE,OAAO,QAAQ;wBACjB;;oBAEA,IAAI,YAAY;wBACd;mEAAgB,CAAA;gCACd,MAAM,WAAW;uCAAI;uCAAS;iCAAgB;gCAC9C,0CAA0C;gCAC1C,MAAM,OAAO,IAAI;gCACjB,OAAO,SAAS,MAAM;2EAAC,CAAA;wCACrB,IAAI,KAAK,GAAG,CAAC,MAAM,EAAE,GAAG;4CACtB,OAAO;wCACT;wCACA,KAAK,GAAG,CAAC,MAAM,EAAE;wCACjB,OAAO;oCACT;;4BACF;;wBACA,iBAAiB,OAAO,GAAG,iBAAiB,OAAO,GAAG;oBACxD,OAAO;wBACL,QAAQ,GAAG,CAAC,+BAA+B,gBAAgB,MAAM,EAAE;wBACnE,gBAAgB;wBAChB,QAAQ,GAAG,CAAC;wBACZ,iBAAiB,OAAO,GAAG;oBAC7B;oBAEA,8BAA8B;oBAC9B,gBAAgB,OAAO;+DAAC,CAAC;4BACvB,kBAAkB,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE;wBACxC;;oBAEA,6CAA6C;oBAC7C,IAAI,eAAe;wBACjB,iBAAiB,gBAAgB,MAAM,IAAI;oBAC7C,OAAO;wBACL,4EAA4E;wBAC5E,iBAAiB;oBACnB;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC,sBAAsB,SAAS,MAAM,EAAE,SAAS,UAAU;oBACxE,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,QAAQ,KAAK,CAAC,0BAA0B;gBAC1C;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,QAAQ,KAAK,CAAC,oBAAoB;oBAChC,SAAS,AAAC,MAAgB,OAAO;oBACjC,OAAO,AAAC,MAAgB,KAAK;oBAC7B,MAAM,AAAC,MAAgB,IAAI;gBAC7B;YACF,SAAU;gBACR,WAAW;gBACX,eAAe;gBACf,QAAQ,GAAG,CAAC;YACd;QACF;8CAAG,EAAE,GAAG,0EAA0E;IAElF,MAAM,iBAAiB;QACrB,wFAAwF;QACxF,MAAM,gBAAgB,kBAAkB,OAAO,CAAC,MAAM,GAAG,KACpC,oBAAoB,OAAO,CAAC,MAAM,GAAG,KACrC,aAAa,OAAO,IACpB,WAAW,OAAO;QAEvC,IAAI,CAAC,eAAe,iBAAiB,eAAe;YAClD,kBAAkB;QACpB;IACF;IAEA,MAAM,mBAAmB,CAAC,cAAsB;QAC9C,aAAa;QACb,WAAW;IACb;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,WAAW;IACb;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,mCAAmC;YACnC,QAAQ,GAAG,CAAC;YACZ;YACA,eAAe,OAAO,GAAG,MAAM,sCAAsC;;QACvE;yBAAG;QAAC;KAAkB,EAAE,uCAAuC;;IAE/D,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,uEAAuE;YACvE,IAAI,eAAe,OAAO,EAAE;gBAC1B,QAAQ,GAAG,CAAC;gBACZ;YACF;YACA,QAAQ,GAAG,CAAC;YACZ;QACF;yBAAG;QAAC;QAAgB;QAAkB;QAAW;QAAS;KAAkB;IAE5E,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,QAAQ,GAAG,CAAC,kCAAkC;gBAC5C,QAAQ,aAAa,MAAM;gBAC3B,YAAY,YAAY,CAAC,EAAE,EAAE;gBAC7B;gBACA;YACF;QACF;yBAAG;QAAC;QAAc;QAAS;KAAY;IAEvC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCAGxC,6LAAC;wBAAE,WAAU;kCAAiC;;;;;;kCAI9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG9B,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC,mIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAS,WAAU;;kCACpC,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;;kDACpC,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;;kDACpC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAKtC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAS,WAAU;;0CAEpC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mIAAA,CAAA,aAAU;oCACT,WAAW;oCACX,SAAS;oCACT,cAAc;oCACd,SAAS;;;;;;;;;;;0CAIb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,sIAAA,CAAA,gBAAa;wCACZ,gBAAgB;wCAChB,gBAAgB;;;;;;kDAElB,6LAAC,wIAAA,CAAA,kBAAe;wCACd,kBAAkB;wCAClB,kBAAkB;wCAClB,gBAAgB;;;;;;;;;;;;4BAInB,wBACC,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wCAAY,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;uCAHP;;;;;;;;;qDAQd;;kDACE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,eAAe,MAAM,GAAG,KAAK,iBAAiB,MAAM,GAAG,IAAI,oBAAoB;;;;;;0DAElF,6LAAC;gDAAK,WAAU;;oDACb,aAAa,MAAM;oDAAC;;;;;;;;;;;;;oCAIxB,CAAC;wCACA,QAAQ,GAAG,CAAC,mCAAmC,aAAa,MAAM;wCAClE,QAAQ,GAAG,CAAC,4BAA4B;wCACxC,OAAO,aAAa,MAAM,KAAK;oCACjC,CAAC,oBACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAG,WAAU;0DAA2B;;;;;;0DACzC,6LAAC;0DAAE;;;;;;;;;;;6DAGL,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,6LAAC,kIAAA,CAAA,YAAS;wDAAgB,OAAO;wDAAO,SAAS;uDAAjC,MAAM,EAAE;;;;;;;;;;4CAK3B,+BACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;8DAET,4BACC;;0EACE,6LAAC;gEAAI,WAAU;;;;;;4DAAqF;;uEAItG;;;;;;;;;;;0DAOR,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uIAAA,CAAA,iBAAc;oDACb,gBAAgB;oDAChB,kBAAkB;oDAClB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;kCAStB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAS,WAAU;kCACpC,cAAA,6LAAC,wIAAA,CAAA,kBAAe;4BAAC,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAK3C;GAzVwB;KAAA", "debugId": null}}]}