WEBVTT
Kind: captions
Language: en

00:00:00.399 --> 00:00:03.429 align:start position:0%
 
hey<00:00:01.199><c> uh</c><00:00:01.280><c> so</c><00:00:01.480><c> I'm</c><00:00:01.680><c> back</c><00:00:01.920><c> to</c><00:00:02.159><c> talk</c><00:00:02.520><c> through</c><00:00:03.280><c> um</c>

00:00:03.429 --> 00:00:03.439 align:start position:0%
hey uh so I'm back to talk through um
 

00:00:03.439 --> 00:00:06.789 align:start position:0%
hey uh so I'm back to talk through um
kind<00:00:03.560><c> of</c><00:00:03.679><c> some</c><00:00:04.000><c> high</c><00:00:04.240><c> Lev</c><00:00:04.879><c> Concepts</c><00:00:05.879><c> about</c><00:00:06.520><c> um</c>

00:00:06.789 --> 00:00:06.799 align:start position:0%
kind of some high Lev Concepts about um
 

00:00:06.799 --> 00:00:08.830 align:start position:0%
kind of some high Lev Concepts about um
this<00:00:07.040><c> this</c><00:00:07.200><c> feature</c><00:00:07.520><c> that</c><00:00:07.640><c> we've</c><00:00:07.840><c> referenced</c>

00:00:08.830 --> 00:00:08.840 align:start position:0%
this this feature that we've referenced
 

00:00:08.840 --> 00:00:11.749 align:start position:0%
this this feature that we've referenced
um<00:00:09.000><c> called</c><00:00:09.559><c> automations</c><00:00:10.559><c> and</c><00:00:10.840><c> as</c><00:00:11.200><c> haml</c><00:00:11.519><c> did</c><00:00:11.639><c> a</c>

00:00:11.749 --> 00:00:11.759 align:start position:0%
um called automations and as haml did a
 

00:00:11.759 --> 00:00:13.390 align:start position:0%
um called automations and as haml did a
great<00:00:11.960><c> job</c><00:00:12.160><c> of</c><00:00:12.320><c> describing</c><00:00:12.759><c> it's</c><00:00:12.920><c> really</c><00:00:13.200><c> this</c>

00:00:13.390 --> 00:00:13.400 align:start position:0%
great job of describing it's really this
 

00:00:13.400 --> 00:00:16.590 align:start position:0%
great job of describing it's really this
glue<00:00:14.200><c> that</c><00:00:14.440><c> lets</c><00:00:14.719><c> you</c><00:00:15.039><c> connect</c><00:00:16.039><c> um</c><00:00:16.279><c> you</c><00:00:16.359><c> know</c>

00:00:16.590 --> 00:00:16.600 align:start position:0%
glue that lets you connect um you know
 

00:00:16.600 --> 00:00:18.390 align:start position:0%
glue that lets you connect um you know
different<00:00:17.000><c> actions</c><00:00:17.400><c> that</c><00:00:17.520><c> you're</c><00:00:17.800><c> performing</c>

00:00:18.390 --> 00:00:18.400 align:start position:0%
different actions that you're performing
 

00:00:18.400 --> 00:00:21.070 align:start position:0%
different actions that you're performing
in<00:00:18.560><c> weights</c><00:00:18.840><c> and</c><00:00:19.240><c> biases</c><00:00:20.240><c> uh</c><00:00:20.439><c> and</c><00:00:20.640><c> hook</c><00:00:20.920><c> them</c>

00:00:21.070 --> 00:00:21.080 align:start position:0%
in weights and biases uh and hook them
 

00:00:21.080 --> 00:00:22.830 align:start position:0%
in weights and biases uh and hook them
up<00:00:21.279><c> with</c><00:00:21.480><c> Downstream</c><00:00:22.039><c> events</c><00:00:22.480><c> that</c><00:00:22.600><c> you</c><00:00:22.680><c> want</c>

00:00:22.830 --> 00:00:22.840 align:start position:0%
up with Downstream events that you want
 

00:00:22.840 --> 00:00:24.990 align:start position:0%
up with Downstream events that you want
to<00:00:23.039><c> trigger</c><00:00:23.439><c> automatically</c><00:00:24.439><c> and</c><00:00:24.599><c> that's</c><00:00:24.800><c> what</c>

00:00:24.990 --> 00:00:25.000 align:start position:0%
to trigger automatically and that's what
 

00:00:25.000 --> 00:00:26.790 align:start position:0%
to trigger automatically and that's what
this<00:00:25.439><c> this</c><00:00:25.599><c> whole</c><00:00:25.840><c> talk</c><00:00:26.359><c> topic</c><00:00:26.640><c> of</c>

00:00:26.790 --> 00:00:26.800 align:start position:0%
this this whole talk topic of
 

00:00:26.800 --> 00:00:28.589 align:start position:0%
this this whole talk topic of
automations<00:00:27.560><c> is</c><00:00:27.720><c> really</c><00:00:28.000><c> about</c><00:00:28.279><c> that</c><00:00:28.439><c> is</c>

00:00:28.589 --> 00:00:28.599 align:start position:0%
automations is really about that is
 

00:00:28.599 --> 00:00:31.669 align:start position:0%
automations is really about that is
about<00:00:28.880><c> setting</c><00:00:29.279><c> up</c><00:00:30.199><c> uh</c><00:00:30.400><c> these</c><00:00:30.720><c> event</c><00:00:31.240><c> action</c>

00:00:31.669 --> 00:00:31.679 align:start position:0%
about setting up uh these event action
 

00:00:31.679 --> 00:00:34.830 align:start position:0%
about setting up uh these event action
pairs<00:00:32.320><c> so</c><00:00:33.120><c> to</c><00:00:33.360><c> clarify</c><00:00:33.760><c> an</c><00:00:33.920><c> automation</c><00:00:34.640><c> is</c>

00:00:34.830 --> 00:00:34.840 align:start position:0%
pairs so to clarify an automation is
 

00:00:34.840 --> 00:00:37.709 align:start position:0%
pairs so to clarify an automation is
just<00:00:35.040><c> an</c><00:00:35.239><c> event</c><00:00:35.520><c> action</c><00:00:35.960><c> pair</c><00:00:36.960><c> um</c><00:00:37.360><c> where</c><00:00:37.520><c> an</c>

00:00:37.709 --> 00:00:37.719 align:start position:0%
just an event action pair um where an
 

00:00:37.719 --> 00:00:40.150 align:start position:0%
just an event action pair um where an
event<00:00:38.320><c> is</c><00:00:38.640><c> you</c><00:00:38.760><c> know</c><00:00:38.960><c> something</c><00:00:39.680><c> that</c><00:00:39.840><c> takes</c>

00:00:40.150 --> 00:00:40.160 align:start position:0%
event is you know something that takes
 

00:00:40.160 --> 00:00:42.110 align:start position:0%
event is you know something that takes
place<00:00:40.360><c> like</c><00:00:40.480><c> a</c><00:00:40.680><c> specific</c><00:00:41.120><c> change</c><00:00:41.600><c> that</c><00:00:41.800><c> takes</c>

00:00:42.110 --> 00:00:42.120 align:start position:0%
place like a specific change that takes
 

00:00:42.120 --> 00:00:45.430 align:start position:0%
place like a specific change that takes
place<00:00:42.600><c> in</c><00:00:42.879><c> the</c><00:00:43.000><c> model</c><00:00:43.360><c> registry</c><00:00:44.079><c> like</c><00:00:44.640><c> a</c><00:00:44.840><c> new</c>

00:00:45.430 --> 00:00:45.440 align:start position:0%
place in the model registry like a new
 

00:00:45.440 --> 00:00:49.229 align:start position:0%
place in the model registry like a new
model<00:00:45.760><c> version</c><00:00:46.199><c> is</c><00:00:46.600><c> added</c><00:00:47.600><c> um</c><00:00:48.239><c> and</c><00:00:48.879><c> that</c><00:00:49.079><c> is</c>

00:00:49.229 --> 00:00:49.239 align:start position:0%
model version is added um and that is
 

00:00:49.239 --> 00:00:51.549 align:start position:0%
model version is added um and that is
hooked<00:00:49.520><c> up</c><00:00:49.680><c> to</c><00:00:50.000><c> an</c><00:00:50.239><c> action</c><00:00:51.039><c> so</c><00:00:51.199><c> this</c><00:00:51.320><c> is</c><00:00:51.399><c> a</c>

00:00:51.549 --> 00:00:51.559 align:start position:0%
hooked up to an action so this is a
 

00:00:51.559 --> 00:00:53.549 align:start position:0%
hooked up to an action so this is a
downstream<00:00:52.160><c> action</c><00:00:52.520><c> that</c><00:00:52.640><c> is</c><00:00:52.800><c> triggered</c><00:00:53.280><c> in</c>

00:00:53.549 --> 00:00:53.559 align:start position:0%
downstream action that is triggered in
 

00:00:53.559 --> 00:00:56.709 align:start position:0%
downstream action that is triggered in
response<00:00:54.000><c> to</c><00:00:54.160><c> the</c><00:00:54.320><c> event</c><00:00:54.800><c> um</c><00:00:55.320><c> happening</c><00:00:56.320><c> um</c><00:00:56.480><c> so</c>

00:00:56.709 --> 00:00:56.719 align:start position:0%
response to the event um happening um so
 

00:00:56.719 --> 00:00:58.310 align:start position:0%
response to the event um happening um so
it's<00:00:56.920><c> nothing</c><00:00:57.320><c> nothing</c><00:00:57.600><c> fancy</c><00:00:58.000><c> again</c><00:00:58.160><c> it's</c>

00:00:58.310 --> 00:00:58.320 align:start position:0%
it's nothing nothing fancy again it's
 

00:00:58.320 --> 00:01:00.750 align:start position:0%
it's nothing nothing fancy again it's
this<00:00:58.480><c> event</c><00:00:58.800><c> action</c><00:00:59.160><c> pair</c><00:01:00.120><c> um</c><00:01:00.359><c> and</c><00:01:00.480><c> you</c><00:01:00.600><c> can</c>

00:01:00.750 --> 00:01:00.760 align:start position:0%
this event action pair um and you can
 

00:01:00.760 --> 00:01:03.150 align:start position:0%
this event action pair um and you can
see<00:01:01.039><c> here</c><00:01:01.239><c> in</c><00:01:01.359><c> the</c><00:01:01.559><c> diagram</c><00:01:02.239><c> that</c><00:01:02.719><c> uh</c><00:01:02.840><c> we</c><00:01:02.960><c> have</c>

00:01:03.150 --> 00:01:03.160 align:start position:0%
see here in the diagram that uh we have
 

00:01:03.160 --> 00:01:05.469 align:start position:0%
see here in the diagram that uh we have
two<00:01:03.440><c> types</c><00:01:03.680><c> of</c><00:01:03.879><c> events</c><00:01:04.600><c> um</c><00:01:04.760><c> that</c><00:01:04.879><c> you</c><00:01:05.000><c> can</c><00:01:05.199><c> set</c>

00:01:05.469 --> 00:01:05.479 align:start position:0%
two types of events um that you can set
 

00:01:05.479 --> 00:01:09.710 align:start position:0%
two types of events um that you can set
up<00:01:06.320><c> um</c><00:01:06.479><c> so</c><00:01:06.799><c> either</c><00:01:07.400><c> you</c><00:01:07.560><c> know</c><00:01:07.680><c> you</c><00:01:07.960><c> want</c><00:01:08.159><c> to</c><00:01:09.040><c> um</c>

00:01:09.710 --> 00:01:09.720 align:start position:0%
up um so either you know you want to um
 

00:01:09.720 --> 00:01:12.870 align:start position:0%
up um so either you know you want to um
add<00:01:09.960><c> a</c><00:01:10.080><c> new</c><00:01:10.280><c> version</c><00:01:10.840><c> so</c><00:01:11.759><c> the</c><00:01:12.200><c> automation</c><00:01:12.720><c> will</c>

00:01:12.870 --> 00:01:12.880 align:start position:0%
add a new version so the automation will
 

00:01:12.880 --> 00:01:15.670 align:start position:0%
add a new version so the automation will
listen<00:01:13.280><c> to</c><00:01:13.680><c> any</c><00:01:13.960><c> changes</c><00:01:14.759><c> in</c><00:01:15.320><c> um</c><00:01:15.479><c> you</c><00:01:15.560><c> know</c>

00:01:15.670 --> 00:01:15.680 align:start position:0%
listen to any changes in um you know
 

00:01:15.680 --> 00:01:17.510 align:start position:0%
listen to any changes in um you know
your<00:01:15.960><c> registered</c><00:01:16.400><c> model</c><00:01:16.799><c> that</c><00:01:16.920><c> we</c><00:01:17.080><c> looked</c><00:01:17.280><c> at</c>

00:01:17.510 --> 00:01:17.520 align:start position:0%
your registered model that we looked at
 

00:01:17.520 --> 00:01:20.510 align:start position:0%
your registered model that we looked at
earlier<00:01:18.520><c> and</c><00:01:18.840><c> you</c><00:01:18.960><c> can</c><00:01:19.119><c> set</c><00:01:19.360><c> one</c><00:01:19.520><c> up</c><00:01:19.720><c> that</c><00:01:19.840><c> says</c>

00:01:20.510 --> 00:01:20.520 align:start position:0%
earlier and you can set one up that says
 

00:01:20.520 --> 00:01:22.350 align:start position:0%
earlier and you can set one up that says
Hey<00:01:20.680><c> I</c><00:01:20.759><c> want</c><00:01:20.920><c> you</c><00:01:21.040><c> to</c><00:01:21.320><c> kick</c><00:01:21.560><c> off</c><00:01:21.840><c> a</c><00:01:22.000><c> model</c>

00:01:22.350 --> 00:01:22.360 align:start position:0%
Hey I want you to kick off a model
 

00:01:22.360 --> 00:01:25.310 align:start position:0%
Hey I want you to kick off a model
evaluation<00:01:23.079><c> pipeline</c><00:01:23.600><c> when</c><00:01:23.840><c> any</c><00:01:24.200><c> new</c><00:01:24.479><c> model</c>

00:01:25.310 --> 00:01:25.320 align:start position:0%
evaluation pipeline when any new model
 

00:01:25.320 --> 00:01:28.390 align:start position:0%
evaluation pipeline when any new model
um<00:01:25.520><c> is</c><00:01:25.720><c> added</c><00:01:26.479><c> so</c><00:01:26.720><c> the</c><00:01:26.880><c> event</c><00:01:27.280><c> there</c><00:01:27.600><c> is</c><00:01:28.240><c> uh</c>

00:01:28.390 --> 00:01:28.400 align:start position:0%
um is added so the event there is uh
 

00:01:28.400 --> 00:01:31.830 align:start position:0%
um is added so the event there is uh
linking<00:01:29.040><c> a</c><00:01:29.159><c> a</c><00:01:29.280><c> new</c><00:01:29.479><c> version</c><00:01:30.000><c> to</c><00:01:30.240><c> the</c><00:01:30.840><c> registry</c>

00:01:31.830 --> 00:01:31.840 align:start position:0%
linking a a new version to the registry
 

00:01:31.840 --> 00:01:33.990 align:start position:0%
linking a a new version to the registry
um<00:01:32.280><c> the</c><00:01:32.439><c> second</c><00:01:32.680><c> one</c><00:01:32.880><c> we</c><00:01:32.960><c> see</c><00:01:33.200><c> here</c><00:01:33.360><c> is</c><00:01:33.560><c> adding</c>

00:01:33.990 --> 00:01:34.000 align:start position:0%
um the second one we see here is adding
 

00:01:34.000 --> 00:01:36.789 align:start position:0%
um the second one we see here is adding
a<00:01:34.159><c> new</c><00:01:34.360><c> Alias</c><00:01:34.880><c> and</c><00:01:35.119><c> that</c><00:01:35.640><c> what</c><00:01:35.799><c> that</c><00:01:35.920><c> means</c><00:01:36.240><c> is</c>

00:01:36.789 --> 00:01:36.799 align:start position:0%
a new Alias and that what that means is
 

00:01:36.799 --> 00:01:39.749 align:start position:0%
a new Alias and that what that means is
you're<00:01:37.079><c> adding</c><00:01:37.720><c> a</c><00:01:38.040><c> a</c><00:01:38.159><c> model</c><00:01:38.520><c> version</c><00:01:39.479><c> um</c><00:01:39.640><c> with</c>

00:01:39.749 --> 00:01:39.759 align:start position:0%
you're adding a a model version um with
 

00:01:39.759 --> 00:01:42.710 align:start position:0%
you're adding a a model version um with
a<00:01:39.960><c> specific</c><00:01:40.560><c> Alias</c><00:01:41.560><c> so</c><00:01:42.119><c> this</c><00:01:42.320><c> might</c><00:01:42.560><c> be</c>

00:01:42.710 --> 00:01:42.720 align:start position:0%
a specific Alias so this might be
 

00:01:42.720 --> 00:01:44.830 align:start position:0%
a specific Alias so this might be
something<00:01:43.000><c> that</c><00:01:43.119><c> you</c><00:01:43.280><c> set</c><00:01:43.520><c> up</c><00:01:43.759><c> for</c><00:01:43.960><c> example</c><00:01:44.439><c> to</c>

00:01:44.830 --> 00:01:44.840 align:start position:0%
something that you set up for example to
 

00:01:44.840 --> 00:01:46.670 align:start position:0%
something that you set up for example to
kick<00:01:45.040><c> off</c><00:01:45.200><c> an</c><00:01:45.439><c> action</c><00:01:45.880><c> that</c><00:01:46.000><c> should</c><00:01:46.280><c> only</c>

00:01:46.670 --> 00:01:46.680 align:start position:0%
kick off an action that should only
 

00:01:46.680 --> 00:01:48.910 align:start position:0%
kick off an action that should only
happen<00:01:47.200><c> when</c><00:01:47.320><c> a</c><00:01:47.479><c> model</c><00:01:47.759><c> is</c><00:01:47.880><c> in</c><00:01:48.000><c> a</c><00:01:48.159><c> specific</c><00:01:48.719><c> a</c>

00:01:48.910 --> 00:01:48.920 align:start position:0%
happen when a model is in a specific a
 

00:01:48.920 --> 00:01:52.109 align:start position:0%
happen when a model is in a specific a
specific<00:01:49.439><c> stage</c><00:01:50.200><c> of</c><00:01:50.360><c> the</c><00:01:50.520><c> model</c><00:01:50.880><c> life</c><00:01:51.119><c> cycle</c>

00:01:52.109 --> 00:01:52.119 align:start position:0%
specific stage of the model life cycle
 

00:01:52.119 --> 00:01:54.789 align:start position:0%
specific stage of the model life cycle
um<00:01:52.280><c> so</c><00:01:52.759><c> quantized</c><00:01:53.560><c> adding</c><00:01:53.920><c> the</c><00:01:54.040><c> Alias</c>

00:01:54.789 --> 00:01:54.799 align:start position:0%
um so quantized adding the Alias
 

00:01:54.799 --> 00:01:57.590 align:start position:0%
um so quantized adding the Alias
quantized<00:01:55.799><c> um</c><00:01:56.079><c> might</c><00:01:56.280><c> be</c><00:01:56.520><c> great</c><00:01:57.280><c> a</c><00:01:57.399><c> great</c>

00:01:57.590 --> 00:01:57.600 align:start position:0%
quantized um might be great a great
 

00:01:57.600 --> 00:01:59.510 align:start position:0%
quantized um might be great a great
event<00:01:57.880><c> to</c><00:01:58.039><c> use</c><00:01:58.360><c> to</c><00:01:58.560><c> trigger</c><00:01:58.880><c> a</c><00:01:59.039><c> downstream</c>

00:01:59.510 --> 00:01:59.520 align:start position:0%
event to use to trigger a downstream
 

00:01:59.520 --> 00:02:02.990 align:start position:0%
event to use to trigger a downstream
process<00:02:00.159><c> of</c><00:02:00.719><c> model</c><00:02:01.439><c> quantization</c><00:02:02.439><c> um</c><00:02:02.640><c> another</c>

00:02:02.990 --> 00:02:03.000 align:start position:0%
process of model quantization um another
 

00:02:03.000 --> 00:02:05.630 align:start position:0%
process of model quantization um another
one<00:02:03.399><c> might</c><00:02:03.600><c> be</c><00:02:04.039><c> prod</c><00:02:04.840><c> um</c><00:02:04.960><c> so</c><00:02:05.119><c> if</c><00:02:05.200><c> you're</c><00:02:05.360><c> adding</c>

00:02:05.630 --> 00:02:05.640 align:start position:0%
one might be prod um so if you're adding
 

00:02:05.640 --> 00:02:07.749 align:start position:0%
one might be prod um so if you're adding
the<00:02:05.759><c> Alias</c><00:02:06.320><c> prod</c><00:02:06.880><c> that</c><00:02:07.039><c> might</c><00:02:07.240><c> kick</c><00:02:07.439><c> off</c><00:02:07.600><c> a</c>

00:02:07.749 --> 00:02:07.759 align:start position:0%
the Alias prod that might kick off a
 

00:02:07.759 --> 00:02:09.790 align:start position:0%
the Alias prod that might kick off a
downstream<00:02:08.319><c> action</c><00:02:09.000><c> um</c><00:02:09.160><c> that</c><00:02:09.280><c> deploys</c><00:02:09.720><c> the</c>

00:02:09.790 --> 00:02:09.800 align:start position:0%
downstream action um that deploys the
 

00:02:09.800 --> 00:02:11.070 align:start position:0%
downstream action um that deploys the
model<00:02:10.080><c> to</c>

00:02:11.070 --> 00:02:11.080 align:start position:0%
model to
 

00:02:11.080 --> 00:02:13.630 align:start position:0%
model to
production<00:02:12.080><c> and</c><00:02:12.239><c> on</c><00:02:12.360><c> the</c><00:02:12.520><c> right</c><00:02:13.040><c> hand</c><00:02:13.280><c> side</c><00:02:13.520><c> we</c>

00:02:13.630 --> 00:02:13.640 align:start position:0%
production and on the right hand side we
 

00:02:13.640 --> 00:02:15.670 align:start position:0%
production and on the right hand side we
see<00:02:14.040><c> the</c><00:02:14.239><c> action</c><00:02:14.879><c> so</c><00:02:15.160><c> again</c><00:02:15.360><c> this</c><00:02:15.440><c> is</c><00:02:15.560><c> the</c>

00:02:15.670 --> 00:02:15.680 align:start position:0%
see the action so again this is the
 

00:02:15.680 --> 00:02:17.229 align:start position:0%
see the action so again this is the
downstream<00:02:16.280><c> action</c><00:02:16.720><c> that's</c><00:02:16.879><c> going</c><00:02:17.000><c> to</c><00:02:17.080><c> be</c>

00:02:17.229 --> 00:02:17.239 align:start position:0%
downstream action that's going to be
 

00:02:17.239 --> 00:02:19.190 align:start position:0%
downstream action that's going to be
triggered<00:02:17.599><c> in</c><00:02:17.760><c> a</c><00:02:17.959><c> response</c><00:02:18.959><c> um</c><00:02:19.120><c> the</c>

00:02:19.190 --> 00:02:19.200 align:start position:0%
triggered in a response um the
 

00:02:19.200 --> 00:02:20.550 align:start position:0%
triggered in a response um the
automation<00:02:19.640><c> is</c><00:02:19.760><c> going</c><00:02:19.879><c> to</c><00:02:19.959><c> listen</c><00:02:20.239><c> for</c><00:02:20.440><c> the</c>

00:02:20.550 --> 00:02:20.560 align:start position:0%
automation is going to listen for the
 

00:02:20.560 --> 00:02:22.830 align:start position:0%
automation is going to listen for the
event<00:02:20.800><c> to</c><00:02:21.000><c> happen</c><00:02:21.599><c> and</c><00:02:21.800><c> then</c><00:02:22.560><c> uh</c><00:02:22.680><c> this</c>

00:02:22.830 --> 00:02:22.840 align:start position:0%
event to happen and then uh this
 

00:02:22.840 --> 00:02:25.150 align:start position:0%
event to happen and then uh this
Downstream<00:02:23.360><c> action</c><00:02:23.560><c> will</c><00:02:23.840><c> happen</c><00:02:24.840><c> um</c><00:02:25.040><c> in</c>

00:02:25.150 --> 00:02:25.160 align:start position:0%
Downstream action will happen um in
 

00:02:25.160 --> 00:02:26.990 align:start position:0%
Downstream action will happen um in
weights<00:02:25.440><c> and</c><00:02:25.560><c> biases</c><00:02:26.000><c> we</c><00:02:26.080><c> currently</c><00:02:26.440><c> have</c><00:02:26.640><c> two</c>

00:02:26.990 --> 00:02:27.000 align:start position:0%
weights and biases we currently have two
 

00:02:27.000 --> 00:02:30.270 align:start position:0%
weights and biases we currently have two
options<00:02:27.640><c> for</c><00:02:28.200><c> these</c><00:02:28.680><c> actions</c><00:02:29.599><c> the</c><00:02:29.959><c> first</c><00:02:30.120><c> one</c>

00:02:30.270 --> 00:02:30.280 align:start position:0%
options for these actions the first one
 

00:02:30.280 --> 00:02:33.470 align:start position:0%
options for these actions the first one
is<00:02:30.400><c> a</c><00:02:30.640><c> a</c><00:02:30.760><c> web</c><00:02:31.080><c> hook</c><00:02:31.680><c> um</c><00:02:32.400><c> this</c><00:02:32.560><c> might</c><00:02:32.760><c> be</c><00:02:33.080><c> a</c><00:02:33.200><c> term</c>

00:02:33.470 --> 00:02:33.480 align:start position:0%
is a a web hook um this might be a term
 

00:02:33.480 --> 00:02:35.430 align:start position:0%
is a a web hook um this might be a term
that<00:02:33.599><c> you</c><00:02:33.720><c> guys</c><00:02:33.840><c> are</c><00:02:34.200><c> familiar</c><00:02:34.680><c> with</c><00:02:35.080><c> um</c><00:02:35.200><c> from</c>

00:02:35.430 --> 00:02:35.440 align:start position:0%
that you guys are familiar with um from
 

00:02:35.440 --> 00:02:37.589 align:start position:0%
that you guys are familiar with um from
software<00:02:36.120><c> and</c><00:02:36.599><c> Hamill</c><00:02:37.000><c> is</c><00:02:37.080><c> going</c><00:02:37.200><c> to</c><00:02:37.280><c> spend</c><00:02:37.519><c> a</c>

00:02:37.589 --> 00:02:37.599 align:start position:0%
software and Hamill is going to spend a
 

00:02:37.599 --> 00:02:39.270 align:start position:0%
software and Hamill is going to spend a
bunch<00:02:37.800><c> of</c><00:02:38.040><c> time</c><00:02:38.319><c> kind</c><00:02:38.440><c> of</c><00:02:38.599><c> walking</c><00:02:38.959><c> through</c>

00:02:39.270 --> 00:02:39.280 align:start position:0%
bunch of time kind of walking through
 

00:02:39.280 --> 00:02:41.350 align:start position:0%
bunch of time kind of walking through
that<00:02:40.080><c> um</c><00:02:40.400><c> and</c><00:02:40.599><c> and</c><00:02:40.720><c> some</c><00:02:40.920><c> really</c><00:02:41.120><c> good</c>

00:02:41.350 --> 00:02:41.360 align:start position:0%
that um and and some really good
 

00:02:41.360 --> 00:02:43.350 align:start position:0%
that um and and some really good
examples<00:02:41.840><c> of</c><00:02:42.000><c> how</c><00:02:42.080><c> you</c><00:02:42.200><c> set</c><00:02:42.400><c> a</c><00:02:42.519><c> web</c><00:02:42.760><c> hook</c><00:02:43.000><c> a</c><00:02:43.120><c> web</c>

00:02:43.350 --> 00:02:43.360 align:start position:0%
examples of how you set a web hook a web
 

00:02:43.360 --> 00:02:46.350 align:start position:0%
examples of how you set a web hook a web
hook<00:02:43.560><c> up</c><00:02:44.000><c> um</c><00:02:44.200><c> locally</c><00:02:45.200><c> and</c><00:02:45.360><c> then</c><00:02:45.800><c> um</c><00:02:45.959><c> moving</c><00:02:46.239><c> it</c>

00:02:46.350 --> 00:02:46.360 align:start position:0%
hook up um locally and then um moving it
 

00:02:46.360 --> 00:02:48.630 align:start position:0%
hook up um locally and then um moving it
on<00:02:46.560><c> cloud</c><00:02:47.319><c> uh</c><00:02:47.480><c> and</c><00:02:48.000><c> kind</c><00:02:48.120><c> of</c><00:02:48.239><c> really</c><00:02:48.440><c> to</c>

00:02:48.630 --> 00:02:48.640 align:start position:0%
on cloud uh and kind of really to
 

00:02:48.640 --> 00:02:50.470 align:start position:0%
on cloud uh and kind of really to
replicate<00:02:49.480><c> what</c><00:02:49.760><c> what</c><00:02:49.879><c> do</c><00:02:50.000><c> we</c><00:02:50.080><c> mean</c><00:02:50.319><c> when</c>

00:02:50.470 --> 00:02:50.480 align:start position:0%
replicate what what do we mean when
 

00:02:50.480 --> 00:02:52.070 align:start position:0%
replicate what what do we mean when
weights<00:02:50.720><c> and</c><00:02:50.879><c> biases</c><00:02:51.319><c> is</c><00:02:51.440><c> going</c><00:02:51.560><c> to</c><00:02:51.720><c> kick</c><00:02:51.920><c> off</c>

00:02:52.070 --> 00:02:52.080 align:start position:0%
weights and biases is going to kick off
 

00:02:52.080 --> 00:02:54.670 align:start position:0%
weights and biases is going to kick off
a<00:02:52.239><c> web</c><00:02:52.480><c> hook</c><00:02:53.400><c> um</c><00:02:53.640><c> but</c><00:02:53.920><c> it</c><00:02:54.000><c> allows</c><00:02:54.239><c> you</c><00:02:54.360><c> to</c><00:02:54.480><c> kick</c>

00:02:54.670 --> 00:02:54.680 align:start position:0%
a web hook um but it allows you to kick
 

00:02:54.680 --> 00:02:57.350 align:start position:0%
a web hook um but it allows you to kick
off<00:02:54.800><c> a</c><00:02:54.920><c> downstream</c><00:02:55.560><c> process</c><00:02:56.159><c> in</c><00:02:56.319><c> an</c><00:02:56.560><c> external</c>

00:02:57.350 --> 00:02:57.360 align:start position:0%
off a downstream process in an external
 

00:02:57.360 --> 00:02:59.949 align:start position:0%
off a downstream process in an external
uh<00:02:57.560><c> application</c><00:02:58.319><c> so</c><00:02:58.560><c> maybe</c><00:02:59.239><c> kicking</c><00:02:59.560><c> off</c><00:02:59.680><c> a</c><00:02:59.840><c> a</c>

00:02:59.949 --> 00:02:59.959 align:start position:0%
uh application so maybe kicking off a a
 

00:02:59.959 --> 00:03:01.589 align:start position:0%
uh application so maybe kicking off a a
model<00:03:00.239><c> evaluation</c><00:03:00.840><c> pipeline</c><00:03:01.200><c> in</c><00:03:01.360><c> your</c>

00:03:01.589 --> 00:03:01.599 align:start position:0%
model evaluation pipeline in your
 

00:03:01.599 --> 00:03:04.110 align:start position:0%
model evaluation pipeline in your
workflow<00:03:02.599><c> orchestrator</c><00:03:03.599><c> um</c><00:03:03.920><c> it's</c>

00:03:04.110 --> 00:03:04.120 align:start position:0%
workflow orchestrator um it's
 

00:03:04.120 --> 00:03:05.550 align:start position:0%
workflow orchestrator um it's
essentially<00:03:04.480><c> just</c><00:03:04.599><c> a</c><00:03:04.760><c> post</c><00:03:05.080><c> request</c><00:03:05.440><c> that</c>

00:03:05.550 --> 00:03:05.560 align:start position:0%
essentially just a post request that
 

00:03:05.560 --> 00:03:07.910 align:start position:0%
essentially just a post request that
allows<00:03:05.879><c> you</c><00:03:06.000><c> to</c><00:03:06.239><c> hit</c><00:03:06.440><c> an</c><00:03:06.680><c> external</c><00:03:07.319><c> endpoint</c>

00:03:07.910 --> 00:03:07.920 align:start position:0%
allows you to hit an external endpoint
 

00:03:07.920 --> 00:03:10.470 align:start position:0%
allows you to hit an external endpoint
in<00:03:08.080><c> another</c><00:03:08.519><c> application</c><00:03:09.159><c> or</c><00:03:09.519><c> server</c><00:03:10.360><c> and</c>

00:03:10.470 --> 00:03:10.480 align:start position:0%
in another application or server and
 

00:03:10.480 --> 00:03:12.149 align:start position:0%
in another application or server and
lets<00:03:10.680><c> you</c><00:03:10.879><c> connect</c><00:03:11.280><c> events</c><00:03:11.599><c> to</c><00:03:11.799><c> weights</c><00:03:12.000><c> and</c>

00:03:12.149 --> 00:03:12.159 align:start position:0%
lets you connect events to weights and
 

00:03:12.159 --> 00:03:14.990 align:start position:0%
lets you connect events to weights and
biases<00:03:12.760><c> to</c><00:03:13.360><c> you</c><00:03:13.480><c> know</c><00:03:13.879><c> any</c><00:03:14.120><c> other</c><00:03:14.319><c> tooling</c><00:03:14.760><c> you</c>

00:03:14.990 --> 00:03:15.000 align:start position:0%
biases to you know any other tooling you
 

00:03:15.000 --> 00:03:16.789 align:start position:0%
biases to you know any other tooling you
have<00:03:15.280><c> for</c><00:03:15.480><c> example</c><00:03:15.840><c> tooling</c><00:03:16.200><c> that</c><00:03:16.400><c> might</c><00:03:16.599><c> be</c>

00:03:16.789 --> 00:03:16.799 align:start position:0%
have for example tooling that might be
 

00:03:16.799 --> 00:03:20.990 align:start position:0%
have for example tooling that might be
managing<00:03:17.720><c> um</c><00:03:17.879><c> your</c><00:03:18.599><c> um</c><00:03:18.799><c> you</c><00:03:18.920><c> know</c><00:03:19.200><c> model</c><00:03:19.519><c> cicd</c>

00:03:20.990 --> 00:03:21.000 align:start position:0%
managing um your um you know model cicd
 

00:03:21.000 --> 00:03:24.030 align:start position:0%
managing um your um you know model cicd
processes<00:03:22.000><c> uh</c><00:03:22.159><c> the</c><00:03:22.360><c> second</c><00:03:22.760><c> type</c><00:03:23.200><c> of</c><00:03:23.840><c> uh</c>

00:03:24.030 --> 00:03:24.040 align:start position:0%
processes uh the second type of uh
 

00:03:24.040 --> 00:03:27.350 align:start position:0%
processes uh the second type of uh
action<00:03:24.360><c> we</c><00:03:24.519><c> see</c><00:03:24.840><c> here</c><00:03:25.159><c> is</c><00:03:25.280><c> a</c><00:03:25.560><c> a</c><00:03:25.680><c> launch</c><00:03:26.120><c> job</c><00:03:27.120><c> so</c>

00:03:27.350 --> 00:03:27.360 align:start position:0%
action we see here is a a launch job so
 

00:03:27.360 --> 00:03:30.070 align:start position:0%
action we see here is a a launch job so
a<00:03:27.519><c> launch</c><00:03:27.920><c> job</c><00:03:28.200><c> is</c><00:03:28.319><c> a</c><00:03:28.840><c> containerized</c><00:03:29.799><c> and</c>

00:03:30.070 --> 00:03:30.080 align:start position:0%
a launch job is a containerized and
 

00:03:30.080 --> 00:03:34.070 align:start position:0%
a launch job is a containerized and
reusable<00:03:31.080><c> and</c><00:03:31.239><c> also</c><00:03:31.519><c> configurable</c><00:03:32.439><c> job</c><00:03:33.439><c> um</c>

00:03:34.070 --> 00:03:34.080 align:start position:0%
reusable and also configurable job um
 

00:03:34.080 --> 00:03:37.110 align:start position:0%
reusable and also configurable job um
that<00:03:34.439><c> can</c><00:03:34.640><c> be</c><00:03:35.560><c> executed</c><00:03:36.319><c> in</c><00:03:36.760><c> various</c>

00:03:37.110 --> 00:03:37.120 align:start position:0%
that can be executed in various
 

00:03:37.120 --> 00:03:39.789 align:start position:0%
that can be executed in various
different<00:03:37.439><c> compute</c><00:03:38.280><c> environments</c><00:03:39.280><c> um</c><00:03:39.480><c> and</c><00:03:39.599><c> we</c>

00:03:39.789 --> 00:03:39.799 align:start position:0%
different compute environments um and we
 

00:03:39.799 --> 00:03:42.149 align:start position:0%
different compute environments um and we
typically<00:03:40.239><c> encourage</c><00:03:41.000><c> this</c><00:03:41.720><c> uh</c><00:03:41.840><c> you</c><00:03:41.959><c> know</c>

00:03:42.149 --> 00:03:42.159 align:start position:0%
typically encourage this uh you know
 

00:03:42.159 --> 00:03:44.949 align:start position:0%
typically encourage this uh you know
when<00:03:42.519><c> you're</c><00:03:42.720><c> working</c><00:03:43.400><c> with</c><00:03:43.599><c> any</c><00:03:43.959><c> actions</c>

00:03:44.949 --> 00:03:44.959 align:start position:0%
when you're working with any actions
 

00:03:44.959 --> 00:03:46.910 align:start position:0%
when you're working with any actions
that<00:03:45.200><c> for</c><00:03:45.360><c> example</c><00:03:45.720><c> like</c><00:03:45.920><c> evaluation</c><00:03:46.720><c> or</c>

00:03:46.910 --> 00:03:46.920 align:start position:0%
that for example like evaluation or
 

00:03:46.920 --> 00:03:49.190 align:start position:0%
that for example like evaluation or
model<00:03:47.239><c> retraining</c><00:03:48.239><c> anything</c><00:03:48.640><c> with</c><00:03:48.879><c> heavy</c>

00:03:49.190 --> 00:03:49.200 align:start position:0%
model retraining anything with heavy
 

00:03:49.200 --> 00:03:51.789 align:start position:0%
model retraining anything with heavy
compute<00:03:49.799><c> requirements</c><00:03:50.799><c> versus</c><00:03:51.159><c> the</c><00:03:51.280><c> web</c><00:03:51.599><c> Hook</c>

00:03:51.789 --> 00:03:51.799 align:start position:0%
compute requirements versus the web Hook
 

00:03:51.799 --> 00:03:53.830 align:start position:0%
compute requirements versus the web Hook
is<00:03:52.200><c> typically</c><00:03:52.640><c> more</c><00:03:52.920><c> helpful</c><00:03:53.239><c> for</c><00:03:53.439><c> handing</c>

00:03:53.830 --> 00:03:53.840 align:start position:0%
is typically more helpful for handing
 

00:03:53.840 --> 00:03:57.229 align:start position:0%
is typically more helpful for handing
off<00:03:54.079><c> models</c><00:03:54.519><c> to</c><00:03:54.920><c> external</c><00:03:55.439><c> systems</c><00:03:56.040><c> like</c><00:03:56.480><c> cicd</c>

00:03:57.229 --> 00:03:57.239 align:start position:0%
off models to external systems like cicd
 

00:03:57.239 --> 00:03:59.910 align:start position:0%
off models to external systems like cicd
tools<00:03:58.040><c> like</c><00:03:58.280><c> GitHub</c><00:03:58.760><c> actions</c><00:03:59.280><c> or</c><00:03:59.439><c> any</c><00:03:59.560><c> of</c><00:03:59.799><c> the</c>

00:03:59.910 --> 00:03:59.920 align:start position:0%
tools like GitHub actions or any of the
 

00:03:59.920 --> 00:04:02.710 align:start position:0%
tools like GitHub actions or any of the
workflow<00:04:00.319><c> orchestrators</c><00:04:01.040><c> you</c><00:04:01.159><c> might</c><00:04:01.319><c> be</c><00:04:01.799><c> uh</c>

00:04:02.710 --> 00:04:02.720 align:start position:0%
workflow orchestrators you might be uh
 

00:04:02.720 --> 00:04:05.910 align:start position:0%
workflow orchestrators you might be uh
using<00:04:03.720><c> um</c><00:04:04.200><c> and</c><00:04:04.720><c> we'll</c><00:04:04.879><c> spend</c><00:04:05.120><c> some</c><00:04:05.280><c> more</c><00:04:05.560><c> time</c>

00:04:05.910 --> 00:04:05.920 align:start position:0%
using um and we'll spend some more time
 

00:04:05.920 --> 00:04:07.309 align:start position:0%
using um and we'll spend some more time
talking<00:04:06.200><c> about</c><00:04:06.439><c> web</c><00:04:06.680><c> hooks</c><00:04:06.959><c> that</c><00:04:07.040><c> will</c><00:04:07.159><c> be</c>

00:04:07.309 --> 00:04:07.319 align:start position:0%
talking about web hooks that will be
 

00:04:07.319 --> 00:04:10.550 align:start position:0%
talking about web hooks that will be
kind<00:04:07.439><c> of</c><00:04:07.560><c> the</c><00:04:07.879><c> the</c><00:04:08.000><c> main</c><00:04:08.519><c> focus</c><00:04:09.519><c> of</c><00:04:10.040><c> um</c><00:04:10.200><c> some</c><00:04:10.360><c> of</c>

00:04:10.550 --> 00:04:10.560 align:start position:0%
kind of the the main focus of um some of
 

00:04:10.560 --> 00:04:12.830 align:start position:0%
kind of the the main focus of um some of
the<00:04:10.760><c> the</c><00:04:10.879><c> demos</c><00:04:11.239><c> that</c><00:04:11.400><c> haml</c><00:04:11.720><c> will</c><00:04:11.840><c> be</c><00:04:12.000><c> giving</c>

00:04:12.830 --> 00:04:12.840 align:start position:0%
the the demos that haml will be giving
 

00:04:12.840 --> 00:04:16.110 align:start position:0%
the the demos that haml will be giving
uh<00:04:12.959><c> but</c><00:04:13.120><c> launch</c><00:04:14.120><c> uh</c><00:04:14.239><c> is</c><00:04:14.439><c> also</c><00:04:15.120><c> it's</c><00:04:15.319><c> a</c><00:04:15.799><c> a</c>

00:04:16.110 --> 00:04:16.120 align:start position:0%
uh but launch uh is also it's a a
 

00:04:16.120 --> 00:04:17.629 align:start position:0%
uh but launch uh is also it's a a
another<00:04:16.519><c> product</c><00:04:16.919><c> inside</c><00:04:17.320><c> kind</c><00:04:17.440><c> of</c><00:04:17.519><c> the</c>

00:04:17.629 --> 00:04:17.639 align:start position:0%
another product inside kind of the
 

00:04:17.639 --> 00:04:20.349 align:start position:0%
another product inside kind of the
weights<00:04:17.880><c> and</c><00:04:18.000><c> biases</c>

00:04:20.349 --> 00:04:20.359 align:start position:0%
weights and biases
 

00:04:20.359 --> 00:04:24.270 align:start position:0%
weights and biases
family<00:04:21.440><c> cool</c><00:04:22.440><c> um</c><00:04:22.680><c> so</c><00:04:23.080><c> I</c><00:04:23.240><c> put</c><00:04:23.440><c> this</c><00:04:23.680><c> diagram</c>

00:04:24.270 --> 00:04:24.280 align:start position:0%
family cool um so I put this diagram
 

00:04:24.280 --> 00:04:26.390 align:start position:0%
family cool um so I put this diagram
together<00:04:25.040><c> you</c><00:04:25.160><c> know</c><00:04:25.360><c> I</c><00:04:25.680><c> I</c><00:04:25.800><c> talked</c><00:04:26.080><c> about</c><00:04:26.280><c> kind</c>

00:04:26.390 --> 00:04:26.400 align:start position:0%
together you know I I talked about kind
 

00:04:26.400 --> 00:04:28.430 align:start position:0%
together you know I I talked about kind
of<00:04:26.520><c> the</c><00:04:26.639><c> anatomy</c><00:04:27.280><c> of</c><00:04:27.400><c> an</c><00:04:27.600><c> automation</c><00:04:28.360><c> that</c>

00:04:28.430 --> 00:04:28.440 align:start position:0%
of the anatomy of an automation that
 

00:04:28.440 --> 00:04:30.430 align:start position:0%
of the anatomy of an automation that
it's<00:04:28.639><c> this</c><00:04:28.800><c> event</c><00:04:29.120><c> action</c><00:04:29.400><c> pair</c><00:04:30.199><c> and</c><00:04:30.320><c> you</c>

00:04:30.430 --> 00:04:30.440 align:start position:0%
it's this event action pair and you
 

00:04:30.440 --> 00:04:32.390 align:start position:0%
it's this event action pair and you
might<00:04:30.600><c> be</c><00:04:30.720><c> trying</c><00:04:30.919><c> to</c><00:04:31.360><c> understand</c><00:04:31.639><c> like</c><00:04:32.120><c> okay</c>

00:04:32.390 --> 00:04:32.400 align:start position:0%
might be trying to understand like okay
 

00:04:32.400 --> 00:04:34.310 align:start position:0%
might be trying to understand like okay
like<00:04:32.560><c> why</c><00:04:32.720><c> is</c><00:04:32.840><c> this</c><00:04:33.039><c> relevant</c><00:04:33.400><c> to</c><00:04:33.560><c> my</c><00:04:33.720><c> cicd</c>

00:04:34.310 --> 00:04:34.320 align:start position:0%
like why is this relevant to my cicd
 

00:04:34.320 --> 00:04:36.270 align:start position:0%
like why is this relevant to my cicd
workflow<00:04:34.840><c> and</c><00:04:35.320><c> really</c><00:04:35.720><c> like</c><00:04:35.919><c> how</c><00:04:36.080><c> are</c>

00:04:36.270 --> 00:04:36.280 align:start position:0%
workflow and really like how are
 

00:04:36.280 --> 00:04:37.990 align:start position:0%
workflow and really like how are
automations<00:04:36.840><c> useful</c><00:04:37.240><c> to</c><00:04:37.400><c> connect</c><00:04:37.800><c> weights</c>

00:04:37.990 --> 00:04:38.000 align:start position:0%
automations useful to connect weights
 

00:04:38.000 --> 00:04:41.550 align:start position:0%
automations useful to connect weights
and<00:04:38.160><c> vises</c><00:04:38.680><c> to</c><00:04:39.160><c> your</c><00:04:39.400><c> cicd</c><00:04:40.199><c> workflow</c><00:04:41.199><c> um</c><00:04:41.320><c> so</c><00:04:41.440><c> I</c>

00:04:41.550 --> 00:04:41.560 align:start position:0%
and vises to your cicd workflow um so I
 

00:04:41.560 --> 00:04:44.790 align:start position:0%
and vises to your cicd workflow um so I
put<00:04:41.680><c> down</c><00:04:41.840><c> a</c><00:04:41.960><c> few</c><00:04:42.240><c> examples</c><00:04:42.919><c> here</c><00:04:43.919><c> and</c><00:04:44.560><c> um</c><00:04:44.680><c> you</c>

00:04:44.790 --> 00:04:44.800 align:start position:0%
put down a few examples here and um you
 

00:04:44.800 --> 00:04:46.510 align:start position:0%
put down a few examples here and um you
might<00:04:45.160><c> you</c><00:04:45.280><c> know</c><00:04:45.400><c> on</c><00:04:45.520><c> the</c><00:04:45.800><c> on</c><00:04:45.880><c> the</c><00:04:46.080><c> left</c><00:04:46.320><c> hand</c>

00:04:46.510 --> 00:04:46.520 align:start position:0%
might you know on the on the left hand
 

00:04:46.520 --> 00:04:48.270 align:start position:0%
might you know on the on the left hand
side<00:04:46.720><c> the</c><00:04:46.840><c> farthest</c><00:04:47.280><c> column</c><00:04:47.560><c> on</c><00:04:47.680><c> the</c><00:04:47.840><c> left</c>

00:04:48.270 --> 00:04:48.280 align:start position:0%
side the farthest column on the left
 

00:04:48.280 --> 00:04:50.029 align:start position:0%
side the farthest column on the left
might<00:04:48.600><c> you</c><00:04:48.720><c> know</c><00:04:48.960><c> recognize</c><00:04:49.600><c> a</c><00:04:49.720><c> bunch</c><00:04:49.880><c> of</c>

00:04:50.029 --> 00:04:50.039 align:start position:0%
might you know recognize a bunch of
 

00:04:50.039 --> 00:04:52.390 align:start position:0%
might you know recognize a bunch of
workflows<00:04:50.560><c> that</c><00:04:50.680><c> you're</c><00:04:50.880><c> trying</c><00:04:51.120><c> to</c><00:04:51.400><c> automate</c>

00:04:52.390 --> 00:04:52.400 align:start position:0%
workflows that you're trying to automate
 

00:04:52.400 --> 00:04:54.310 align:start position:0%
workflows that you're trying to automate
and<00:04:52.840><c> you</c><00:04:52.960><c> know</c><00:04:53.280><c> the</c><00:04:53.400><c> key</c><00:04:53.560><c> thing</c><00:04:53.720><c> to</c><00:04:53.880><c> point</c><00:04:54.080><c> out</c>

00:04:54.310 --> 00:04:54.320 align:start position:0%
and you know the key thing to point out
 

00:04:54.320 --> 00:04:56.270 align:start position:0%
and you know the key thing to point out
here<00:04:54.600><c> is</c><00:04:54.720><c> that</c><00:04:54.880><c> you</c><00:04:55.000><c> can</c><00:04:55.160><c> use</c><00:04:55.360><c> an</c><00:04:55.520><c> automation</c>

00:04:56.270 --> 00:04:56.280 align:start position:0%
here is that you can use an automation
 

00:04:56.280 --> 00:04:59.110 align:start position:0%
here is that you can use an automation
to<00:04:56.720><c> listen</c><00:04:57.160><c> to</c><00:04:57.440><c> to</c><00:04:57.639><c> changes</c><00:04:58.600><c> in</c><00:04:58.720><c> the</c><00:04:58.840><c> model</c>

00:04:59.110 --> 00:04:59.120 align:start position:0%
to listen to to changes in the model
 

00:04:59.120 --> 00:05:01.510 align:start position:0%
to listen to to changes in the model
registry<00:04:59.840><c> or</c><00:05:00.000><c> to</c><00:05:00.120><c> your</c><00:05:00.280><c> artifacts</c><00:05:01.280><c> to</c>

00:05:01.510 --> 00:05:01.520 align:start position:0%
registry or to your artifacts to
 

00:05:01.520 --> 00:05:04.110 align:start position:0%
registry or to your artifacts to
automate<00:05:02.039><c> these</c><00:05:02.440><c> these</c><00:05:02.720><c> processes</c><00:05:03.720><c> um</c><00:05:03.880><c> so</c><00:05:04.039><c> I</c>

00:05:04.110 --> 00:05:04.120 align:start position:0%
automate these these processes um so I
 

00:05:04.120 --> 00:05:07.430 align:start position:0%
automate these these processes um so I
think<00:05:04.520><c> a</c><00:05:04.680><c> great</c><00:05:04.919><c> one</c><00:05:05.880><c> um</c><00:05:06.560><c> that</c><00:05:06.919><c> I</c><00:05:07.039><c> have</c><00:05:07.240><c> down</c>

00:05:07.430 --> 00:05:07.440 align:start position:0%
think a great one um that I have down
 

00:05:07.440 --> 00:05:10.790 align:start position:0%
think a great one um that I have down
here<00:05:07.680><c> is</c><00:05:07.960><c> model</c><00:05:08.800><c> evaluation</c><00:05:09.800><c> um</c><00:05:10.120><c> so</c><00:05:10.600><c> you're</c>

00:05:10.790 --> 00:05:10.800 align:start position:0%
here is model evaluation um so you're
 

00:05:10.800 --> 00:05:12.870 align:start position:0%
here is model evaluation um so you're
trying<00:05:11.039><c> to</c><00:05:11.400><c> make</c><00:05:11.560><c> sure</c><00:05:11.759><c> that</c><00:05:12.000><c> anytime</c><00:05:12.479><c> a</c><00:05:12.639><c> new</c>

00:05:12.870 --> 00:05:12.880 align:start position:0%
trying to make sure that anytime a new
 

00:05:12.880 --> 00:05:15.310 align:start position:0%
trying to make sure that anytime a new
model<00:05:13.680><c> uh</c><00:05:13.800><c> is</c><00:05:13.919><c> a</c><00:05:14.160><c> candidate</c><00:05:14.520><c> for</c><00:05:14.720><c> staging</c><00:05:15.160><c> and</c>

00:05:15.310 --> 00:05:15.320 align:start position:0%
model uh is a candidate for staging and
 

00:05:15.320 --> 00:05:18.110 align:start position:0%
model uh is a candidate for staging and
it<00:05:15.520><c> is</c><00:05:15.800><c> linked</c><00:05:16.199><c> to</c><00:05:16.360><c> the</c><00:05:16.479><c> model</c><00:05:16.800><c> registry</c><00:05:17.800><c> um</c><00:05:17.960><c> it</c>

00:05:18.110 --> 00:05:18.120 align:start position:0%
it is linked to the model registry um it
 

00:05:18.120 --> 00:05:20.230 align:start position:0%
it is linked to the model registry um it
has<00:05:18.280><c> to</c><00:05:18.440><c> go</c><00:05:18.680><c> through</c><00:05:19.360><c> uh</c><00:05:19.440><c> you</c><00:05:19.560><c> know</c><00:05:19.840><c> a</c><00:05:19.960><c> system</c>

00:05:20.230 --> 00:05:20.240 align:start position:0%
has to go through uh you know a system
 

00:05:20.240 --> 00:05:22.950 align:start position:0%
has to go through uh you know a system
of<00:05:20.520><c> evaluation</c><00:05:21.199><c> tests</c><00:05:22.039><c> so</c><00:05:22.360><c> the</c><00:05:22.479><c> automation</c>

00:05:22.950 --> 00:05:22.960 align:start position:0%
of evaluation tests so the automation
 

00:05:22.960 --> 00:05:25.629 align:start position:0%
of evaluation tests so the automation
will<00:05:23.160><c> listen</c><00:05:23.440><c> to</c><00:05:23.680><c> changes</c><00:05:24.000><c> and</c><00:05:24.199><c> say</c><00:05:24.520><c> oh</c><00:05:25.120><c> a</c><00:05:25.240><c> new</c>

00:05:25.629 --> 00:05:25.639 align:start position:0%
will listen to changes and say oh a new
 

00:05:25.639 --> 00:05:27.350 align:start position:0%
will listen to changes and say oh a new
a<00:05:25.759><c> new</c><00:05:25.919><c> model</c><00:05:26.199><c> was</c><00:05:26.360><c> added</c><00:05:26.880><c> I'm</c><00:05:26.960><c> going</c><00:05:27.080><c> to</c><00:05:27.240><c> go</c>

00:05:27.350 --> 00:05:27.360 align:start position:0%
a new model was added I'm going to go
 

00:05:27.360 --> 00:05:29.150 align:start position:0%
a new model was added I'm going to go
ahead<00:05:27.560><c> and</c><00:05:27.720><c> kick</c><00:05:27.919><c> off</c><00:05:28.240><c> this</c><00:05:28.680><c> uh</c><00:05:28.800><c> model</c>

00:05:29.150 --> 00:05:29.160 align:start position:0%
ahead and kick off this uh model
 

00:05:29.160 --> 00:05:32.110 align:start position:0%
ahead and kick off this uh model
evaluation<00:05:29.960><c> process</c><00:05:30.759><c> potentially</c><00:05:31.280><c> create</c><00:05:31.960><c> an</c>

00:05:32.110 --> 00:05:32.120 align:start position:0%
evaluation process potentially create an
 

00:05:32.120 --> 00:05:34.870 align:start position:0%
evaluation process potentially create an
automated<00:05:32.720><c> report</c><00:05:33.639><c> uh</c><00:05:33.800><c> which</c><00:05:34.000><c> pulls</c><00:05:34.360><c> in</c><00:05:34.720><c> all</c>

00:05:34.870 --> 00:05:34.880 align:start position:0%
automated report uh which pulls in all
 

00:05:34.880 --> 00:05:36.950 align:start position:0%
automated report uh which pulls in all
of<00:05:35.080><c> the</c><00:05:35.319><c> evaluation</c><00:05:36.039><c> results</c><00:05:36.680><c> so</c><00:05:36.840><c> that</c>

00:05:36.950 --> 00:05:36.960 align:start position:0%
of the evaluation results so that
 

00:05:36.960 --> 00:05:39.150 align:start position:0%
of the evaluation results so that
they're<00:05:37.120><c> ready</c><00:05:37.400><c> to</c><00:05:37.520><c> be</c><00:05:37.720><c> kind</c><00:05:37.840><c> of</c><00:05:38.039><c> analyzed</c><00:05:38.919><c> um</c>

00:05:39.150 --> 00:05:39.160 align:start position:0%
they're ready to be kind of analyzed um
 

00:05:39.160 --> 00:05:42.350 align:start position:0%
they're ready to be kind of analyzed um
by<00:05:39.479><c> by</c><00:05:39.600><c> the</c><00:05:40.120><c> team</c><00:05:41.120><c> um</c><00:05:41.520><c> you</c><00:05:41.639><c> can</c><00:05:41.840><c> also</c><00:05:42.080><c> apply</c>

00:05:42.350 --> 00:05:42.360 align:start position:0%
by by the team um you can also apply
 

00:05:42.360 --> 00:05:44.510 align:start position:0%
by by the team um you can also apply
automations<00:05:43.240><c> and</c><00:05:43.360><c> we'll</c><00:05:43.520><c> spend</c><00:05:43.840><c> less</c><00:05:44.080><c> time</c><00:05:44.319><c> on</c>

00:05:44.510 --> 00:05:44.520 align:start position:0%
automations and we'll spend less time on
 

00:05:44.520 --> 00:05:47.070 align:start position:0%
automations and we'll spend less time on
this<00:05:44.759><c> but</c><00:05:44.919><c> for</c><00:05:45.240><c> data</c><00:05:45.560><c> set</c><00:05:45.800><c> artifacts</c><00:05:46.800><c> this</c><00:05:46.919><c> is</c>

00:05:47.070 --> 00:05:47.080 align:start position:0%
this but for data set artifacts this is
 

00:05:47.080 --> 00:05:49.230 align:start position:0%
this but for data set artifacts this is
another<00:05:47.479><c> great</c><00:05:47.680><c> one</c><00:05:48.000><c> where</c><00:05:48.479><c> you</c><00:05:48.759><c> set</c><00:05:48.919><c> up</c><00:05:49.080><c> an</c>

00:05:49.230 --> 00:05:49.240 align:start position:0%
another great one where you set up an
 

00:05:49.240 --> 00:05:52.670 align:start position:0%
another great one where you set up an
automation<00:05:49.840><c> to</c><00:05:50.160><c> listen</c><00:05:50.560><c> to</c><00:05:50.800><c> any</c><00:05:51.120><c> new</c><00:05:51.919><c> um</c><00:05:52.319><c> data</c>

00:05:52.670 --> 00:05:52.680 align:start position:0%
automation to listen to any new um data
 

00:05:52.680 --> 00:05:55.469 align:start position:0%
automation to listen to any new um data
set<00:05:53.160><c> uh</c><00:05:53.280><c> versions</c><00:05:53.800><c> that</c><00:05:53.919><c> are</c><00:05:54.080><c> added</c><00:05:55.039><c> um</c><00:05:55.240><c> and</c>

00:05:55.469 --> 00:05:55.479 align:start position:0%
set uh versions that are added um and
 

00:05:55.479 --> 00:05:57.950 align:start position:0%
set uh versions that are added um and
you<00:05:55.600><c> know</c><00:05:55.759><c> whenever</c><00:05:56.199><c> a</c><00:05:56.360><c> new</c><00:05:56.560><c> version</c><00:05:56.840><c> is</c><00:05:57.039><c> added</c>

00:05:57.950 --> 00:05:57.960 align:start position:0%
you know whenever a new version is added
 

00:05:57.960 --> 00:05:59.710 align:start position:0%
you know whenever a new version is added
you<00:05:58.199><c> kick</c><00:05:58.400><c> off</c><00:05:58.600><c> this</c><00:05:58.759><c> model</c><00:05:59.080><c> retraining</c>

00:05:59.710 --> 00:05:59.720 align:start position:0%
you kick off this model retraining
 

00:05:59.720 --> 00:06:02.189 align:start position:0%
you kick off this model retraining
pipeline<00:06:00.479><c> uh</c><00:06:00.600><c> this</c><00:06:00.720><c> is</c><00:06:01.199><c> a</c><00:06:01.400><c> you</c><00:06:01.520><c> know</c><00:06:01.800><c> a</c><00:06:01.880><c> use</c>

00:06:02.189 --> 00:06:02.199 align:start position:0%
pipeline uh this is a you know a use
 

00:06:02.199 --> 00:06:03.749 align:start position:0%
pipeline uh this is a you know a use
case<00:06:02.319><c> we</c><00:06:02.440><c> see</c><00:06:02.600><c> a</c><00:06:02.680><c> lot</c><00:06:02.840><c> with</c><00:06:03.039><c> customers</c><00:06:03.520><c> who</c><00:06:03.639><c> are</c>

00:06:03.749 --> 00:06:03.759 align:start position:0%
case we see a lot with customers who are
 

00:06:03.759 --> 00:06:06.350 align:start position:0%
case we see a lot with customers who are
getting<00:06:04.000><c> in</c><00:06:04.160><c> a</c><00:06:04.319><c> bunch</c><00:06:04.520><c> of</c><00:06:04.720><c> new</c><00:06:05.000><c> data</c><00:06:05.720><c> um</c><00:06:05.840><c> so</c>

00:06:06.350 --> 00:06:06.360 align:start position:0%
getting in a bunch of new data um so
 

00:06:06.360 --> 00:06:08.710 align:start position:0%
getting in a bunch of new data um so
periodically<00:06:06.960><c> once</c><00:06:07.160><c> a</c><00:06:07.319><c> month</c><00:06:08.039><c> they</c><00:06:08.280><c> have</c>

00:06:08.710 --> 00:06:08.720 align:start position:0%
periodically once a month they have
 

00:06:08.720 --> 00:06:10.870 align:start position:0%
periodically once a month they have
their<00:06:08.919><c> data</c><00:06:09.160><c> is</c><00:06:09.319><c> ingest</c><00:06:10.160><c> ingested</c><00:06:10.639><c> into</c>

00:06:10.870 --> 00:06:10.880 align:start position:0%
their data is ingest ingested into
 

00:06:10.880 --> 00:06:12.950 align:start position:0%
their data is ingest ingested into
weights<00:06:11.080><c> and</c><00:06:11.240><c> biases</c><00:06:11.960><c> and</c><00:06:12.440><c> they</c><00:06:12.599><c> want</c><00:06:12.800><c> this</c>

00:06:12.950 --> 00:06:12.960 align:start position:0%
weights and biases and they want this
 

00:06:12.960 --> 00:06:15.230 align:start position:0%
weights and biases and they want this
automation<00:06:13.440><c> to</c><00:06:13.560><c> listen</c><00:06:14.120><c> for</c><00:06:14.360><c> a</c><00:06:14.520><c> new</c><00:06:14.720><c> data</c><00:06:15.039><c> set</c>

00:06:15.230 --> 00:06:15.240 align:start position:0%
automation to listen for a new data set
 

00:06:15.240 --> 00:06:16.950 align:start position:0%
automation to listen for a new data set
so<00:06:15.400><c> that</c><00:06:15.520><c> the</c><00:06:15.680><c> retraining</c><00:06:16.280><c> process</c><00:06:16.680><c> can</c><00:06:16.800><c> be</c>

00:06:16.950 --> 00:06:16.960 align:start position:0%
so that the retraining process can be
 

00:06:16.960 --> 00:06:19.230 align:start position:0%
so that the retraining process can be
done<00:06:17.440><c> automatically</c><00:06:18.440><c> rather</c><00:06:18.759><c> than</c><00:06:18.919><c> someone</c>

00:06:19.230 --> 00:06:19.240 align:start position:0%
done automatically rather than someone
 

00:06:19.240 --> 00:06:21.749 align:start position:0%
done automatically rather than someone
saying<00:06:19.680><c> hey</c><00:06:19.919><c> we</c><00:06:20.039><c> have</c><00:06:20.160><c> a</c><00:06:20.280><c> new</c><00:06:20.479><c> batch</c><00:06:20.720><c> of</c><00:06:21.400><c> uh</c><00:06:21.639><c> you</c>

00:06:21.749 --> 00:06:21.759 align:start position:0%
saying hey we have a new batch of uh you
 

00:06:21.759 --> 00:06:23.990 align:start position:0%
saying hey we have a new batch of uh you
know<00:06:22.080><c> annotated</c><00:06:22.599><c> data</c><00:06:23.199><c> can</c><00:06:23.360><c> you</c><00:06:23.479><c> go</c><00:06:23.720><c> and</c><00:06:23.840><c> and</c>

00:06:23.990 --> 00:06:24.000 align:start position:0%
know annotated data can you go and and
 

00:06:24.000 --> 00:06:27.070 align:start position:0%
know annotated data can you go and and
retrain<00:06:24.759><c> the</c><00:06:25.000><c> the</c><00:06:25.240><c> model</c><00:06:26.240><c> um</c><00:06:26.639><c> and</c><00:06:26.759><c> the</c><00:06:26.919><c> last</c>

00:06:27.070 --> 00:06:27.080 align:start position:0%
retrain the the model um and the last
 

00:06:27.080 --> 00:06:29.629 align:start position:0%
retrain the the model um and the last
thing<00:06:27.199><c> I'll</c><00:06:27.400><c> note</c><00:06:27.680><c> here</c><00:06:28.199><c> uh</c><00:06:28.400><c> specifically</c><00:06:28.880><c> for</c>

00:06:29.629 --> 00:06:29.639 align:start position:0%
thing I'll note here uh specifically for
 

00:06:29.639 --> 00:06:31.390 align:start position:0%
thing I'll note here uh specifically for
web<00:06:29.919><c> hooks</c><00:06:30.520><c> that</c><00:06:30.680><c> again</c><00:06:30.840><c> we'll</c><00:06:31.000><c> spend</c><00:06:31.240><c> some</c>

00:06:31.390 --> 00:06:31.400 align:start position:0%
web hooks that again we'll spend some
 

00:06:31.400 --> 00:06:34.670 align:start position:0%
web hooks that again we'll spend some
more<00:06:31.639><c> time</c><00:06:31.800><c> and</c><00:06:32.000><c> demo</c><00:06:32.360><c> this</c><00:06:32.599><c> live</c><00:06:33.520><c> but</c><00:06:34.039><c> um</c><00:06:34.520><c> you</c>

00:06:34.670 --> 00:06:34.680 align:start position:0%
more time and demo this live but um you
 

00:06:34.680 --> 00:06:36.830 align:start position:0%
more time and demo this live but um you
might<00:06:34.840><c> be</c><00:06:34.960><c> seeing</c><00:06:35.440><c> hey</c><00:06:35.800><c> you</c><00:06:35.919><c> know</c><00:06:36.319><c> talking</c><00:06:36.639><c> to</c>

00:06:36.830 --> 00:06:36.840 align:start position:0%
might be seeing hey you know talking to
 

00:06:36.840 --> 00:06:38.990 align:start position:0%
might be seeing hey you know talking to
external<00:06:37.280><c> systems</c><00:06:38.199><c> what</c><00:06:38.319><c> about</c><00:06:38.639><c> any</c>

00:06:38.990 --> 00:06:39.000 align:start position:0%
external systems what about any
 

00:06:39.000 --> 00:06:41.589 align:start position:0%
external systems what about any
authentication<00:06:40.000><c> or</c><00:06:40.240><c> like</c><00:06:41.120><c> uh</c><00:06:41.360><c> you</c><00:06:41.440><c> know</c>

00:06:41.589 --> 00:06:41.599 align:start position:0%
authentication or like uh you know
 

00:06:41.599 --> 00:06:43.469 align:start position:0%
authentication or like uh you know
Secrets<00:06:42.039><c> or</c><00:06:42.199><c> access</c><00:06:42.560><c> tokens</c><00:06:43.000><c> that</c><00:06:43.120><c> I</c><00:06:43.280><c> might</c>

00:06:43.469 --> 00:06:43.479 align:start position:0%
Secrets or access tokens that I might
 

00:06:43.479 --> 00:06:45.870 align:start position:0%
Secrets or access tokens that I might
need<00:06:44.280><c> um</c><00:06:44.400><c> and</c><00:06:44.560><c> is</c><00:06:44.639><c> it</c><00:06:44.800><c> safe</c><00:06:45.039><c> to</c><00:06:45.160><c> store</c><00:06:45.479><c> those</c><00:06:45.680><c> in</c>

00:06:45.870 --> 00:06:45.880 align:start position:0%
need um and is it safe to store those in
 

00:06:45.880 --> 00:06:48.670 align:start position:0%
need um and is it safe to store those in
weights<00:06:46.120><c> and</c><00:06:46.280><c> biases</c><00:06:47.199><c> um</c><00:06:47.400><c> so</c><00:06:47.720><c> the</c><00:06:47.880><c> answer</c><00:06:48.199><c> is</c>

00:06:48.670 --> 00:06:48.680 align:start position:0%
weights and biases um so the answer is
 

00:06:48.680 --> 00:06:51.110 align:start position:0%
weights and biases um so the answer is
yes<00:06:49.280><c> uh</c><00:06:49.400><c> you</c><00:06:49.520><c> know</c><00:06:49.680><c> we</c><00:06:49.880><c> have</c><00:06:50.440><c> um</c><00:06:50.599><c> a</c><00:06:50.759><c> Secret</c>

00:06:51.110 --> 00:06:51.120 align:start position:0%
yes uh you know we have um a Secret
 

00:06:51.120 --> 00:06:53.230 align:start position:0%
yes uh you know we have um a Secret
store<00:06:51.840><c> that</c><00:06:52.039><c> safely</c><00:06:52.440><c> allows</c><00:06:52.720><c> you</c><00:06:52.800><c> to</c><00:06:52.960><c> store</c>

00:06:53.230 --> 00:06:53.240 align:start position:0%
store that safely allows you to store
 

00:06:53.240 --> 00:06:55.990 align:start position:0%
store that safely allows you to store
any<00:06:53.440><c> of</c><00:06:53.680><c> these</c><00:06:54.280><c> uh</c><00:06:54.479><c> Secrets</c><00:06:54.919><c> or</c><00:06:55.120><c> access</c><00:06:55.479><c> tokens</c>

00:06:55.990 --> 00:06:56.000 align:start position:0%
any of these uh Secrets or access tokens
 

00:06:56.000 --> 00:06:58.029 align:start position:0%
any of these uh Secrets or access tokens
that<00:06:56.199><c> you're</c><00:06:56.360><c> going</c><00:06:56.479><c> to</c><00:06:56.599><c> be</c><00:06:56.840><c> required</c><00:06:57.840><c> or</c>

00:06:58.029 --> 00:06:58.039 align:start position:0%
that you're going to be required or
 

00:06:58.039 --> 00:06:59.869 align:start position:0%
that you're going to be required or
you're<00:06:58.479><c> the</c><00:06:58.840><c> receiving</c><00:06:59.080><c> server</c><00:06:59.599><c> is</c><00:06:59.720><c> going</c><00:06:59.800><c> to</c>

00:06:59.869 --> 00:06:59.879 align:start position:0%
you're the receiving server is going to
 

00:06:59.879 --> 00:07:01.790 align:start position:0%
you're the receiving server is going to
be<00:07:00.080><c> required</c><00:07:00.479><c> to</c><00:07:00.680><c> authenticate</c><00:07:01.240><c> a</c><00:07:01.400><c> web</c><00:07:01.639><c> Hook</c>

00:07:01.790 --> 00:07:01.800 align:start position:0%
be required to authenticate a web Hook
 

00:07:01.800 --> 00:07:04.990 align:start position:0%
be required to authenticate a web Hook
from<00:07:01.960><c> weights</c><00:07:02.199><c> and</c><00:07:02.599><c> biases</c><00:07:03.599><c> um</c><00:07:03.840><c> and</c><00:07:04.520><c> Beyond</c>

00:07:04.990 --> 00:07:05.000 align:start position:0%
from weights and biases um and Beyond
 

00:07:05.000 --> 00:07:06.830 align:start position:0%
from weights and biases um and Beyond
kind<00:07:05.120><c> of</c><00:07:05.520><c> you</c><00:07:05.599><c> know</c><00:07:05.800><c> our</c><00:07:06.039><c> our</c><00:07:06.360><c> our</c><00:07:06.520><c> Cloud</c>

00:07:06.830 --> 00:07:06.840 align:start position:0%
kind of you know our our our Cloud
 

00:07:06.840 --> 00:07:09.950 align:start position:0%
kind of you know our our our Cloud
deployments<00:07:07.840><c> um</c><00:07:07.960><c> for</c><00:07:08.199><c> anyone</c><00:07:09.000><c> any</c><00:07:09.680><c> um</c>

00:07:09.950 --> 00:07:09.960 align:start position:0%
deployments um for anyone any um
 

00:07:09.960 --> 00:07:11.710 align:start position:0%
deployments um for anyone any um
customers<00:07:10.400><c> that</c><00:07:10.520><c> are</c><00:07:10.680><c> on</c><00:07:10.960><c> on</c><00:07:11.199><c> Prem</c>

00:07:11.710 --> 00:07:11.720 align:start position:0%
customers that are on on Prem
 

00:07:11.720 --> 00:07:14.589 align:start position:0%
customers that are on on Prem
deployments<00:07:12.720><c> we</c><00:07:12.919><c> offer</c><00:07:13.479><c> Integrations</c><00:07:14.479><c> uh</c>

00:07:14.589 --> 00:07:14.599 align:start position:0%
deployments we offer Integrations uh
 

00:07:14.599 --> 00:07:16.589 align:start position:0%
deployments we offer Integrations uh
with<00:07:14.720><c> secret</c><00:07:15.039><c> managers</c><00:07:15.400><c> for</c><00:07:15.639><c> all</c><00:07:15.840><c> three</c><00:07:16.080><c> major</c>

00:07:16.589 --> 00:07:16.599 align:start position:0%
with secret managers for all three major
 

00:07:16.599 --> 00:07:20.080 align:start position:0%
with secret managers for all three major
Cloud<00:07:17.080><c> providers</c>

