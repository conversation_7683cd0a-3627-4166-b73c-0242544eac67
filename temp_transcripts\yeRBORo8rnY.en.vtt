WEBVTT
Kind: captions
Language: en

00:00:01.439 --> 00:00:05.670 align:start position:0%
 
hello<00:00:01.760><c> everyone</c><00:00:02.600><c> today</c><00:00:03.240><c> is</c><00:00:03.600><c> the</c><00:00:04.040><c> 27</c><00:00:04.880><c> of</c><00:00:05.080><c> March</c>

00:00:05.670 --> 00:00:05.680 align:start position:0%
hello everyone today is the 27 of March
 

00:00:05.680 --> 00:00:10.230 align:start position:0%
hello everyone today is the 27 of March
2025<00:00:06.680><c> and</c><00:00:06.879><c> this</c><00:00:07.080><c> is</c><00:00:07.359><c> the</c><00:00:08.000><c> just</c><00:00:08.400><c> stario</c><00:00:09.240><c> daily</c>

00:00:10.230 --> 00:00:10.240 align:start position:0%
2025 and this is the just stario daily
 

00:00:10.240 --> 00:00:12.830 align:start position:0%
2025 and this is the just stario daily
the<00:00:10.440><c> title</c><00:00:10.719><c> of</c><00:00:10.880><c> today</c><00:00:11.240><c> daily</c><00:00:11.679><c> is</c><00:00:12.160><c> crypto</c><00:00:12.599><c> pump</c>

00:00:12.830 --> 00:00:12.840 align:start position:0%
the title of today daily is crypto pump
 

00:00:12.840 --> 00:00:15.709 align:start position:0%
the title of today daily is crypto pump
and<00:00:13.000><c> dumps</c><00:00:13.759><c> a</c><00:00:13.960><c> guide</c><00:00:14.280><c> on</c><00:00:14.480><c> how</c><00:00:14.639><c> to</c><00:00:14.920><c> recognize</c>

00:00:15.709 --> 00:00:15.719 align:start position:0%
and dumps a guide on how to recognize
 

00:00:15.719 --> 00:00:19.390 align:start position:0%
and dumps a guide on how to recognize
and<00:00:16.279><c> avoid</c><00:00:16.840><c> them</c><00:00:17.840><c> one</c><00:00:18.080><c> month</c><00:00:18.439><c> ago</c><00:00:19.039><c> when</c><00:00:19.240><c> the</c>

00:00:19.390 --> 00:00:19.400 align:start position:0%
and avoid them one month ago when the
 

00:00:19.400 --> 00:00:23.269 align:start position:0%
and avoid them one month ago when the
Libra<00:00:19.840><c> Scandal</c><00:00:20.800><c> broke</c><00:00:21.680><c> out</c><00:00:22.680><c> I</c><00:00:22.840><c> wrongly</c>

00:00:23.269 --> 00:00:23.279 align:start position:0%
Libra Scandal broke out I wrongly
 

00:00:23.279 --> 00:00:26.790 align:start position:0%
Libra Scandal broke out I wrongly
assumed<00:00:23.920><c> that</c><00:00:24.279><c> the</c><00:00:24.480><c> era</c><00:00:25.000><c> of</c><00:00:25.560><c> scam</c><00:00:26.000><c> coins</c><00:00:26.560><c> and</c>

00:00:26.790 --> 00:00:26.800 align:start position:0%
assumed that the era of scam coins and
 

00:00:26.800 --> 00:00:28.950 align:start position:0%
assumed that the era of scam coins and
predatory<00:00:27.279><c> pump</c><00:00:27.519><c> and</c><00:00:27.720><c> dumps</c><00:00:28.199><c> was</c><00:00:28.400><c> finally</c>

00:00:28.950 --> 00:00:28.960 align:start position:0%
predatory pump and dumps was finally
 

00:00:28.960 --> 00:00:31.910 align:start position:0%
predatory pump and dumps was finally
over<00:00:30.000><c> considering</c><00:00:30.400><c> it</c><00:00:30.519><c> was</c><00:00:30.800><c> exposed</c><00:00:31.519><c> how</c><00:00:31.800><c> the</c>

00:00:31.910 --> 00:00:31.920 align:start position:0%
over considering it was exposed how the
 

00:00:31.920 --> 00:00:34.069 align:start position:0%
over considering it was exposed how the
whole<00:00:32.160><c> meme</c><00:00:32.520><c> coins</c><00:00:32.960><c> Casino</c><00:00:33.520><c> was</c><00:00:33.800><c> actually</c>

00:00:34.069 --> 00:00:34.079 align:start position:0%
whole meme coins Casino was actually
 

00:00:34.079 --> 00:00:36.549 align:start position:0%
whole meme coins Casino was actually
rigged<00:00:34.520><c> in</c><00:00:34.680><c> favor</c><00:00:34.960><c> of</c><00:00:35.079><c> a</c><00:00:35.239><c> handful</c><00:00:35.600><c> of</c><00:00:35.800><c> players</c>

00:00:36.549 --> 00:00:36.559 align:start position:0%
rigged in favor of a handful of players
 

00:00:36.559 --> 00:00:39.630 align:start position:0%
rigged in favor of a handful of players
who<00:00:36.800><c> consistently</c><00:00:37.520><c> pocketed</c><00:00:38.040><c> big</c><00:00:38.360><c> wins</c><00:00:39.360><c> since</c>

00:00:39.630 --> 00:00:39.640 align:start position:0%
who consistently pocketed big wins since
 

00:00:39.640 --> 00:00:42.389 align:start position:0%
who consistently pocketed big wins since
it<00:00:39.879><c> appears</c><00:00:40.440><c> that</c><00:00:41.000><c> with</c><00:00:41.280><c> exceptions</c><00:00:41.800><c> of</c><00:00:42.079><c> those</c>

00:00:42.389 --> 00:00:42.399 align:start position:0%
it appears that with exceptions of those
 

00:00:42.399 --> 00:00:44.590 align:start position:0%
it appears that with exceptions of those
who<00:00:42.600><c> have</c><00:00:42.760><c> been</c><00:00:43.000><c> publicly</c><00:00:43.520><c> exposed</c><00:00:44.320><c> Bad</c>

00:00:44.590 --> 00:00:44.600 align:start position:0%
who have been publicly exposed Bad
 

00:00:44.600 --> 00:00:46.750 align:start position:0%
who have been publicly exposed Bad
actors<00:00:44.879><c> and</c><00:00:45.000><c> grifters</c><00:00:45.440><c> are</c><00:00:45.600><c> back</c><00:00:45.800><c> in</c><00:00:45.960><c> the</c><00:00:46.160><c> game</c>

00:00:46.750 --> 00:00:46.760 align:start position:0%
actors and grifters are back in the game
 

00:00:46.760 --> 00:00:48.750 align:start position:0%
actors and grifters are back in the game
I<00:00:46.920><c> believe</c><00:00:47.199><c> it</c><00:00:47.320><c> will</c><00:00:47.480><c> be</c><00:00:47.640><c> useful</c><00:00:48.039><c> for</c><00:00:48.280><c> everyone</c>

00:00:48.750 --> 00:00:48.760 align:start position:0%
I believe it will be useful for everyone
 

00:00:48.760 --> 00:00:51.549 align:start position:0%
I believe it will be useful for everyone
to<00:00:48.960><c> learn</c><00:00:49.320><c> the</c><00:00:49.520><c> rules</c><00:00:49.960><c> I</c><00:00:50.160><c> used</c><00:00:50.520><c> to</c><00:00:50.800><c> recognize</c>

00:00:51.549 --> 00:00:51.559 align:start position:0%
to learn the rules I used to recognize
 

00:00:51.559 --> 00:00:54.029 align:start position:0%
to learn the rules I used to recognize
and<00:00:51.840><c> avoid</c><00:00:52.160><c> pump</c><00:00:52.399><c> and</c><00:00:52.559><c> dumps</c><00:00:52.960><c> in</c><00:00:53.079><c> the</c><00:00:53.280><c> crypto</c>

00:00:54.029 --> 00:00:54.039 align:start position:0%
and avoid pump and dumps in the crypto
 

00:00:54.039 --> 00:00:57.229 align:start position:0%
and avoid pump and dumps in the crypto
space<00:00:55.039><c> broadly</c><00:00:55.520><c> speaking</c><00:00:56.199><c> not</c><00:00:56.399><c> only</c><00:00:56.840><c> with</c>

00:00:57.229 --> 00:00:57.239 align:start position:0%
space broadly speaking not only with
 

00:00:57.239 --> 00:01:01.830 align:start position:0%
space broadly speaking not only with
regard<00:00:57.640><c> to</c><00:00:58.039><c> memes</c>

00:01:01.830 --> 00:01:01.840 align:start position:0%
 
 

00:01:01.840 --> 00:01:04.469 align:start position:0%
 
red<00:01:02.120><c> flag</c><00:01:02.480><c> number</c><00:01:02.760><c> one</c><00:01:03.600><c> a</c><00:01:03.760><c> newly</c><00:01:04.080><c> launched</c>

00:01:04.469 --> 00:01:04.479 align:start position:0%
red flag number one a newly launched
 

00:01:04.479 --> 00:01:07.109 align:start position:0%
red flag number one a newly launched
token<00:01:04.960><c> is</c><00:01:05.239><c> immediately</c><00:01:05.840><c> trending</c><00:01:06.320><c> on</c><00:01:06.640><c> Dex</c>

00:01:07.109 --> 00:01:07.119 align:start position:0%
token is immediately trending on Dex
 

00:01:07.119 --> 00:01:08.109 align:start position:0%
token is immediately trending on Dex
trading

00:01:08.109 --> 00:01:08.119 align:start position:0%
trading
 

00:01:08.119 --> 00:01:13.149 align:start position:0%
trading
screeners<00:01:09.119><c> the</c><00:01:09.680><c> Cuban</c><00:01:10.680><c> as</c><00:01:11.040><c> an</c><00:01:11.600><c> example</c><00:01:12.600><c> which</c>

00:01:13.149 --> 00:01:13.159 align:start position:0%
screeners the Cuban as an example which
 

00:01:13.159 --> 00:01:14.710 align:start position:0%
screeners the Cuban as an example which
was<00:01:13.479><c> launched</c><00:01:13.960><c> under</c><00:01:14.200><c> the</c><00:01:14.400><c> false</c>

00:01:14.710 --> 00:01:14.720 align:start position:0%
was launched under the false
 

00:01:14.720 --> 00:01:16.950 align:start position:0%
was launched under the false
representation<00:01:15.680><c> of</c><00:01:16.000><c> being</c><00:01:16.320><c> the</c><00:01:16.479><c> official</c>

00:01:16.950 --> 00:01:16.960 align:start position:0%
representation of being the official
 

00:01:16.960 --> 00:01:19.550 align:start position:0%
representation of being the official
Mark<00:01:17.400><c> Cuban</c><00:01:17.920><c> meme</c>

00:01:19.550 --> 00:01:19.560 align:start position:0%
Mark Cuban meme
 

00:01:19.560 --> 00:01:22.310 align:start position:0%
Mark Cuban meme
token<00:01:20.560><c> in</c><00:01:20.759><c> less</c><00:01:21.000><c> than</c><00:01:21.200><c> 35</c><00:01:21.720><c> minutes</c><00:01:22.040><c> since</c>

00:01:22.310 --> 00:01:22.320 align:start position:0%
token in less than 35 minutes since
 

00:01:22.320 --> 00:01:25.590 align:start position:0%
token in less than 35 minutes since
launch<00:01:23.320><c> this</c><00:01:23.560><c> token</c><00:01:23.960><c> was</c><00:01:24.200><c> up</c>

00:01:25.590 --> 00:01:25.600 align:start position:0%
launch this token was up
 

00:01:25.600 --> 00:01:29.390 align:start position:0%
launch this token was up
18,957<00:01:26.600><c> to</c><00:01:27.560><c> a</c><00:01:27.759><c> theoretical</c><00:01:28.360><c> total</c><00:01:28.680><c> market</c><00:01:29.079><c> cap</c>

00:01:29.390 --> 00:01:29.400 align:start position:0%
18,957 to a theoretical total market cap
 

00:01:29.400 --> 00:01:31.069 align:start position:0%
18,957 to a theoretical total market cap
of<00:01:29.640><c> rough</c>

00:01:31.069 --> 00:01:31.079 align:start position:0%
of rough
 

00:01:31.079 --> 00:01:35.109 align:start position:0%
of rough
426<00:01:32.079><c> million</c><00:01:32.520><c> us</c><00:01:33.520><c> and</c><00:01:33.680><c> as</c><00:01:33.799><c> a</c><00:01:34.000><c> result</c><00:01:34.520><c> it</c><00:01:34.680><c> stood</c>

00:01:35.109 --> 00:01:35.119 align:start position:0%
426 million us and as a result it stood
 

00:01:35.119 --> 00:01:37.910 align:start position:0%
426 million us and as a result it stood
High<00:01:35.600><c> among</c><00:01:35.920><c> the</c><00:01:36.079><c> trending</c><00:01:36.560><c> tokens</c><00:01:37.560><c> being</c>

00:01:37.910 --> 00:01:37.920 align:start position:0%
High among the trending tokens being
 

00:01:37.920 --> 00:01:40.590 align:start position:0%
High among the trending tokens being
picked<00:01:38.240><c> up</c><00:01:38.439><c> by</c><00:01:38.600><c> the</c><00:01:38.759><c> various</c><00:01:39.200><c> Dex</c><00:01:39.600><c> screener</c>

00:01:40.590 --> 00:01:40.600 align:start position:0%
picked up by the various Dex screener
 

00:01:40.600 --> 00:01:42.870 align:start position:0%
picked up by the various Dex screener
platforms<00:01:41.600><c> because</c><00:01:41.920><c> these</c><00:01:42.119><c> tokens</c><00:01:42.439><c> are</c><00:01:42.640><c> never</c>

00:01:42.870 --> 00:01:42.880 align:start position:0%
platforms because these tokens are never
 

00:01:42.880 --> 00:01:51.990 align:start position:0%
platforms because these tokens are never
listed<00:01:43.360><c> right</c><00:01:43.560><c> away</c><00:01:43.920><c> on</c><00:01:44.200><c> centralized</c>

00:01:51.990 --> 00:01:52.000 align:start position:0%
 
 

00:01:52.000 --> 00:01:55.429 align:start position:0%
 
exchanges<00:01:53.000><c> red</c><00:01:53.240><c> flag</c><00:01:53.600><c> number</c><00:01:53.920><c> two</c><00:01:54.600><c> raor</c><00:01:55.159><c> fin</c>

00:01:55.429 --> 00:01:55.439 align:start position:0%
exchanges red flag number two raor fin
 

00:01:55.439 --> 00:01:57.709 align:start position:0%
exchanges red flag number two raor fin
liquidity<00:01:56.000><c> and</c><00:01:56.200><c> token</c><00:01:56.520><c> Supply</c><00:01:57.039><c> in</c><00:01:57.200><c> the</c><00:01:57.360><c> Dex</c>

00:01:57.709 --> 00:01:57.719 align:start position:0%
liquidity and token Supply in the Dex
 

00:01:57.719 --> 00:02:00.550 align:start position:0%
liquidity and token Supply in the Dex
liquidity<00:01:58.399><c> pools</c><00:01:59.399><c> how</c><00:01:59.600><c> is</c><00:01:59.680><c> it</c><00:02:00.079><c> possible</c><00:02:00.399><c> to</c>

00:02:00.550 --> 00:02:00.560 align:start position:0%
liquidity pools how is it possible to
 

00:02:00.560 --> 00:02:02.910 align:start position:0%
liquidity pools how is it possible to
send<00:02:00.920><c> a</c><00:02:01.119><c> newly</c><00:02:01.439><c> minted</c><00:02:01.840><c> token</c><00:02:02.079><c> to</c><00:02:02.320><c> hundreds</c><00:02:02.719><c> of</c>

00:02:02.910 --> 00:02:02.920 align:start position:0%
send a newly minted token to hundreds of
 

00:02:02.920 --> 00:02:04.550 align:start position:0%
send a newly minted token to hundreds of
millions<00:02:03.280><c> of</c><00:02:03.439><c> US</c><00:02:03.680><c> dollar</c><00:02:03.920><c> in</c><00:02:04.119><c> valuation</c><00:02:04.439><c> in</c>

00:02:04.550 --> 00:02:04.560 align:start position:0%
millions of US dollar in valuation in
 

00:02:04.560 --> 00:02:07.510 align:start position:0%
millions of US dollar in valuation in
the<00:02:04.680><c> blink</c><00:02:04.920><c> of</c><00:02:05.000><c> an</c><00:02:05.159><c> eye</c><00:02:05.920><c> very</c><00:02:06.200><c> simple</c><00:02:07.079><c> by</c><00:02:07.360><c> most</c>

00:02:07.510 --> 00:02:07.520 align:start position:0%
the blink of an eye very simple by most
 

00:02:07.520 --> 00:02:09.630 align:start position:0%
the blink of an eye very simple by most
of<00:02:07.680><c> the</c><00:02:07.920><c> supply</c><00:02:08.319><c> of</c><00:02:08.479><c> tokens</c><00:02:08.920><c> available</c><00:02:09.360><c> in</c><00:02:09.479><c> the</c>

00:02:09.630 --> 00:02:09.640 align:start position:0%
of the supply of tokens available in the
 

00:02:09.640 --> 00:02:12.309 align:start position:0%
of the supply of tokens available in the
decentralized<00:02:10.319><c> exchange</c><00:02:10.879><c> pool</c><00:02:11.879><c> as</c><00:02:12.000><c> you</c><00:02:12.120><c> can</c>

00:02:12.309 --> 00:02:12.319 align:start position:0%
decentralized exchange pool as you can
 

00:02:12.319 --> 00:02:17.470 align:start position:0%
decentralized exchange pool as you can
see<00:02:13.000><c> in</c><00:02:13.160><c> the</c><00:02:13.760><c> table</c><00:02:14.760><c> about</c><00:02:15.599><c> cuban1</c><00:02:16.599><c> Million</c>

00:02:17.470 --> 00:02:17.480 align:start position:0%
see in the table about cuban1 Million
 

00:02:17.480 --> 00:02:19.430 align:start position:0%
see in the table about cuban1 Million
worth<00:02:17.720><c> of</c><00:02:17.920><c> buying</c><00:02:18.239><c> volume</c><00:02:18.640><c> was</c><00:02:18.840><c> executed</c>

00:02:19.430 --> 00:02:19.440 align:start position:0%
worth of buying volume was executed
 

00:02:19.440 --> 00:02:21.630 align:start position:0%
worth of buying volume was executed
right<00:02:19.640><c> away</c><00:02:20.200><c> in</c>

00:02:21.630 --> 00:02:21.640 align:start position:0%
right away in
 

00:02:21.640 --> 00:02:25.830 align:start position:0%
right away in
9,256<00:02:22.640><c> transactions</c><00:02:23.360><c> compared</c><00:02:23.879><c> to</c>

00:02:25.830 --> 00:02:25.840 align:start position:0%
9,256 transactions compared to
 

00:02:25.840 --> 00:02:31.430 align:start position:0%
9,256 transactions compared to
8,862<00:02:26.840><c> of</c><00:02:27.160><c> selling</c><00:02:28.160><c> for</c><00:02:28.360><c> a</c><00:02:28.519><c> total</c><00:02:28.959><c> of</c><00:02:29.239><c> 26</c><00:02:30.000><c> ,000</c>

00:02:31.430 --> 00:02:31.440 align:start position:0%
8,862 of selling for a total of 26 ,000
 

00:02:31.440 --> 00:02:34.309 align:start position:0%
8,862 of selling for a total of 26 ,000
us<00:02:32.440><c> to</c><00:02:33.040><c> understand</c><00:02:33.360><c> how</c><00:02:33.599><c> this</c><00:02:33.800><c> brought</c><00:02:34.160><c> the</c>

00:02:34.309 --> 00:02:34.319 align:start position:0%
us to understand how this brought the
 

00:02:34.319 --> 00:02:36.830 align:start position:0%
us to understand how this brought the
token<00:02:34.599><c> to</c><00:02:34.760><c> a</c><00:02:34.840><c> staggering</c><00:02:35.319><c> 400</c><00:02:35.800><c> million</c><00:02:36.239><c> plus</c>

00:02:36.830 --> 00:02:36.840 align:start position:0%
token to a staggering 400 million plus
 

00:02:36.840 --> 00:02:39.589 align:start position:0%
token to a staggering 400 million plus
Us<00:02:37.160><c> doll</c><00:02:37.519><c> valuation</c><00:02:38.519><c> it</c><00:02:38.640><c> is</c><00:02:38.800><c> important</c><00:02:39.200><c> to</c>

00:02:39.589 --> 00:02:39.599 align:start position:0%
Us doll valuation it is important to
 

00:02:39.599 --> 00:02:41.750 align:start position:0%
Us doll valuation it is important to
understand<00:02:39.840><c> how</c><00:02:40.080><c> Dex</c><00:02:40.400><c> liquidity</c><00:02:40.879><c> pools</c><00:02:41.360><c> work</c>

00:02:41.750 --> 00:02:41.760 align:start position:0%
understand how Dex liquidity pools work
 

00:02:41.760 --> 00:02:43.270 align:start position:0%
understand how Dex liquidity pools work
and<00:02:41.959><c> automatically</c><00:02:42.680><c> calculate</c><00:02:43.080><c> and</c>

00:02:43.270 --> 00:02:43.280 align:start position:0%
and automatically calculate and
 

00:02:43.280 --> 00:02:45.750 align:start position:0%
and automatically calculate and
broadcast<00:02:43.879><c> the</c><00:02:44.040><c> price</c><00:02:44.400><c> of</c><00:02:44.560><c> a</c>

00:02:45.750 --> 00:02:45.760 align:start position:0%
broadcast the price of a
 

00:02:45.760 --> 00:02:48.630 align:start position:0%
broadcast the price of a
token<00:02:46.760><c> a</c><00:02:46.959><c> decentralized</c><00:02:47.720><c> exchange</c><00:02:48.159><c> liquidity</c>

00:02:48.630 --> 00:02:48.640 align:start position:0%
token a decentralized exchange liquidity
 

00:02:48.640 --> 00:02:51.070 align:start position:0%
token a decentralized exchange liquidity
pool<00:02:49.200><c> functions</c><00:02:49.680><c> through</c><00:02:49.879><c> a</c><00:02:50.080><c> mechanism</c><00:02:50.560><c> known</c>

00:02:51.070 --> 00:02:51.080 align:start position:0%
pool functions through a mechanism known
 

00:02:51.080 --> 00:02:55.030 align:start position:0%
pool functions through a mechanism known
as<00:02:51.280><c> an</c><00:02:51.480><c> automated</c><00:02:52.239><c> Market</c><00:02:52.720><c> maker</c><00:02:53.400><c> or</c><00:02:54.040><c> amm</c>

00:02:55.030 --> 00:02:55.040 align:start position:0%
as an automated Market maker or amm
 

00:02:55.040 --> 00:02:57.149 align:start position:0%
as an automated Market maker or amm
which<00:02:55.400><c> replaces</c><00:02:56.000><c> traditional</c><00:02:56.440><c> order</c><00:02:56.800><c> books</c>

00:02:57.149 --> 00:02:57.159 align:start position:0%
which replaces traditional order books
 

00:02:57.159 --> 00:02:59.149 align:start position:0%
which replaces traditional order books
with<00:02:57.319><c> a</c><00:02:57.480><c> mathematically</c><00:02:58.200><c> driven</c><00:02:58.640><c> system</c><00:02:58.959><c> of</c>

00:02:59.149 --> 00:02:59.159 align:start position:0%
with a mathematically driven system of
 

00:02:59.159 --> 00:03:01.630 align:start position:0%
with a mathematically driven system of
pulled<00:02:59.560><c> assets</c><00:02:59.840><c> assets</c><00:03:00.680><c> at</c><00:03:00.840><c> its</c><00:03:01.080><c> core</c><00:03:01.480><c> the</c>

00:03:01.630 --> 00:03:01.640 align:start position:0%
pulled assets assets at its core the
 

00:03:01.640 --> 00:03:03.990 align:start position:0%
pulled assets assets at its core the
liquidity<00:03:02.159><c> pool</c><00:03:02.680><c> is</c><00:03:02.920><c> a</c><00:03:03.040><c> shared</c><00:03:03.480><c> reserve</c><00:03:03.840><c> of</c>

00:03:03.990 --> 00:03:04.000 align:start position:0%
liquidity pool is a shared reserve of
 

00:03:04.000 --> 00:03:07.390 align:start position:0%
liquidity pool is a shared reserve of
two<00:03:04.200><c> tokens</c><00:03:04.760><c> such</c><00:03:04.959><c> as</c><00:03:05.200><c> Cuban</c><00:03:05.959><c> and</c><00:03:06.360><c> Solana</c><00:03:07.239><c> in</c>

00:03:07.390 --> 00:03:07.400 align:start position:0%
two tokens such as Cuban and Solana in
 

00:03:07.400 --> 00:03:10.270 align:start position:0%
two tokens such as Cuban and Solana in
our<00:03:07.680><c> example</c><00:03:08.680><c> deposited</c><00:03:09.239><c> by</c><00:03:09.440><c> users</c><00:03:09.879><c> called</c>

00:03:10.270 --> 00:03:10.280 align:start position:0%
our example deposited by users called
 

00:03:10.280 --> 00:03:13.670 align:start position:0%
our example deposited by users called
liquidity<00:03:10.799><c> providers</c><00:03:11.920><c> LPS</c><00:03:12.920><c> these</c><00:03:13.159><c> providers</c>

00:03:13.670 --> 00:03:13.680 align:start position:0%
liquidity providers LPS these providers
 

00:03:13.680 --> 00:03:16.270 align:start position:0%
liquidity providers LPS these providers
contribute<00:03:14.360><c> equal</c><00:03:14.720><c> values</c><00:03:15.080><c> of</c><00:03:15.280><c> both</c><00:03:15.599><c> tokens</c>

00:03:16.270 --> 00:03:16.280 align:start position:0%
contribute equal values of both tokens
 

00:03:16.280 --> 00:03:18.910 align:start position:0%
contribute equal values of both tokens
to<00:03:16.480><c> the</c><00:03:16.640><c> pool</c><00:03:17.360><c> enabling</c><00:03:18.040><c> decentralized</c>

00:03:18.910 --> 00:03:18.920 align:start position:0%
to the pool enabling decentralized
 

00:03:18.920 --> 00:03:22.270 align:start position:0%
to the pool enabling decentralized
trading<00:03:19.920><c> in</c><00:03:20.319><c> return</c><00:03:21.000><c> they</c><00:03:21.239><c> receive</c><00:03:21.760><c> liquidity</c>

00:03:22.270 --> 00:03:22.280 align:start position:0%
trading in return they receive liquidity
 

00:03:22.280 --> 00:03:23.990 align:start position:0%
trading in return they receive liquidity
pool<00:03:22.599><c> tokens</c><00:03:23.120><c> that</c><00:03:23.319><c> represent</c><00:03:23.840><c> the</c>

00:03:23.990 --> 00:03:24.000 align:start position:0%
pool tokens that represent the
 

00:03:24.000 --> 00:03:26.630 align:start position:0%
pool tokens that represent the
proportional<00:03:24.519><c> ownership</c><00:03:24.959><c> of</c><00:03:25.080><c> the</c><00:03:25.400><c> pool</c><00:03:26.400><c> this</c>

00:03:26.630 --> 00:03:26.640 align:start position:0%
proportional ownership of the pool this
 

00:03:26.640 --> 00:03:28.630 align:start position:0%
proportional ownership of the pool this
system<00:03:27.159><c> ensures</c><00:03:27.599><c> continuous</c><00:03:28.040><c> liquidity</c><00:03:28.439><c> for</c>

00:03:28.630 --> 00:03:28.640 align:start position:0%
system ensures continuous liquidity for
 

00:03:28.640 --> 00:03:30.630 align:start position:0%
system ensures continuous liquidity for
Traders<00:03:29.040><c> as</c><00:03:29.200><c> the</c><00:03:29.319><c> pool</c><00:03:29.519><c> itself</c><00:03:30.040><c> acts</c><00:03:30.360><c> as</c><00:03:30.480><c> a</c>

00:03:30.630 --> 00:03:30.640 align:start position:0%
Traders as the pool itself acts as a
 

00:03:30.640 --> 00:03:32.869 align:start position:0%
Traders as the pool itself acts as a
counterparty<00:03:31.200><c> to</c><00:03:31.360><c> every</c><00:03:31.560><c> trade</c><00:03:32.280><c> eliminating</c>

00:03:32.869 --> 00:03:32.879 align:start position:0%
counterparty to every trade eliminating
 

00:03:32.879 --> 00:03:34.869 align:start position:0%
counterparty to every trade eliminating
the<00:03:33.000><c> need</c><00:03:33.200><c> for</c><00:03:33.400><c> buyers</c><00:03:33.720><c> and</c><00:03:33.879><c> sellers</c><00:03:34.239><c> to</c><00:03:34.439><c> match</c>

00:03:34.869 --> 00:03:34.879 align:start position:0%
the need for buyers and sellers to match
 

00:03:34.879 --> 00:03:36.429 align:start position:0%
the need for buyers and sellers to match
orders

00:03:36.429 --> 00:03:36.439 align:start position:0%
orders
 

00:03:36.439 --> 00:03:38.509 align:start position:0%
orders
directly<00:03:37.439><c> the</c><00:03:37.640><c> mechanics</c><00:03:38.080><c> of</c><00:03:38.280><c> price</c>

00:03:38.509 --> 00:03:38.519 align:start position:0%
directly the mechanics of price
 

00:03:38.519 --> 00:03:40.270 align:start position:0%
directly the mechanics of price
determination<00:03:39.200><c> rely</c><00:03:39.480><c> on</c><00:03:39.560><c> a</c><00:03:39.720><c> foundational</c>

00:03:40.270 --> 00:03:40.280 align:start position:0%
determination rely on a foundational
 

00:03:40.280 --> 00:03:42.630 align:start position:0%
determination rely on a foundational
mathematical<00:03:40.920><c> formula</c><00:03:41.840><c> most</c><00:03:42.080><c> commonly</c><00:03:42.480><c> the</c>

00:03:42.630 --> 00:03:42.640 align:start position:0%
mathematical formula most commonly the
 

00:03:42.640 --> 00:03:44.110 align:start position:0%
mathematical formula most commonly the
constant<00:03:43.080><c> product</c>

00:03:44.110 --> 00:03:44.120 align:start position:0%
constant product
 

00:03:44.120 --> 00:03:48.509 align:start position:0%
constant product
formula<00:03:45.120><c> the</c><00:03:45.319><c> ratios</c><00:03:45.920><c> between</c><00:03:46.680><c> x</c><00:03:47.159><c> and</c><00:03:47.439><c> y</c><00:03:48.080><c> equal</c>

00:03:48.509 --> 00:03:48.519 align:start position:0%
formula the ratios between x and y equal
 

00:03:48.519 --> 00:03:49.869 align:start position:0%
formula the ratios between x and y equal
to

00:03:49.869 --> 00:03:49.879 align:start position:0%
to
 

00:03:49.879 --> 00:03:54.309 align:start position:0%
to
K<00:03:50.879><c> here</c><00:03:51.319><c> X</c><00:03:51.560><c> and</c><00:03:51.840><c> Y</c><00:03:52.680><c> represent</c><00:03:53.280><c> the</c><00:03:53.480><c> quantities</c>

00:03:54.309 --> 00:03:54.319 align:start position:0%
K here X and Y represent the quantities
 

00:03:54.319 --> 00:03:57.149 align:start position:0%
K here X and Y represent the quantities
of<00:03:54.480><c> the</c><00:03:54.599><c> two</c><00:03:54.799><c> tokens</c><00:03:55.120><c> in</c><00:03:55.239><c> the</c><00:03:55.360><c> pool</c><00:03:55.959><c> and</c><00:03:56.200><c> K</c><00:03:56.680><c> is</c><00:03:57.000><c> a</c>

00:03:57.149 --> 00:03:57.159 align:start position:0%
of the two tokens in the pool and K is a
 

00:03:57.159 --> 00:03:58.869 align:start position:0%
of the two tokens in the pool and K is a
constant<00:03:57.599><c> value</c><00:03:57.879><c> that</c><00:03:58.120><c> remains</c><00:03:58.360><c> unchanged</c>

00:03:58.869 --> 00:03:58.879 align:start position:0%
constant value that remains unchanged
 

00:03:58.879 --> 00:04:01.190 align:start position:0%
constant value that remains unchanged
regardless<00:03:59.280><c> of</c><00:03:59.400><c> the</c><00:03:59.480><c> trade</c><00:04:00.040><c> negativity</c><00:04:01.040><c> the</c>

00:04:01.190 --> 00:04:01.200 align:start position:0%
regardless of the trade negativity the
 

00:04:01.200 --> 00:04:03.350 align:start position:0%
regardless of the trade negativity the
price<00:04:01.439><c> of</c><00:04:01.560><c> a</c><00:04:01.720><c> token</c><00:04:02.040><c> is</c><00:04:02.239><c> derived</c><00:04:02.720><c> dynamically</c>

00:04:03.350 --> 00:04:03.360 align:start position:0%
price of a token is derived dynamically
 

00:04:03.360 --> 00:04:05.949 align:start position:0%
price of a token is derived dynamically
from<00:04:03.560><c> the</c><00:04:03.760><c> ratio</c><00:04:04.120><c> of</c><00:04:04.319><c> these</c><00:04:04.680><c> reserves</c><00:04:05.680><c> for</c>

00:04:05.949 --> 00:04:05.959 align:start position:0%
from the ratio of these reserves for
 

00:04:05.959 --> 00:04:09.149 align:start position:0%
from the ratio of these reserves for
instance<00:04:06.519><c> if</c><00:04:06.640><c> a</c><00:04:06.799><c> pool</c><00:04:07.040><c> holds</c><00:04:07.519><c> 100</c><00:04:07.920><c> so</c><00:04:08.599><c> and</c>

00:04:09.149 --> 00:04:09.159 align:start position:0%
instance if a pool holds 100 so and
 

00:04:09.159 --> 00:04:11.910 align:start position:0%
instance if a pool holds 100 so and
200,000<00:04:09.840><c> Cuban</c><00:04:10.519><c> the</c><00:04:10.640><c> initial</c><00:04:11.079><c> price</c><00:04:11.280><c> of</c><00:04:11.439><c> so</c><00:04:11.760><c> is</c>

00:04:11.910 --> 00:04:11.920 align:start position:0%
200,000 Cuban the initial price of so is
 

00:04:11.920 --> 00:04:14.509 align:start position:0%
200,000 Cuban the initial price of so is
calculated<00:04:12.560><c> as</c><00:04:12.760><c> the</c><00:04:12.920><c> Cuban</c><00:04:13.360><c> Reserve</c><00:04:13.959><c> divided</c>

00:04:14.509 --> 00:04:14.519 align:start position:0%
calculated as the Cuban Reserve divided
 

00:04:14.519 --> 00:04:17.469 align:start position:0%
calculated as the Cuban Reserve divided
by<00:04:14.760><c> the</c><00:04:14.879><c> so</c><00:04:15.239><c> Reserve</c><00:04:15.959><c> yielding</c><00:04:16.440><c> 2,000</c><00:04:17.000><c> Cuban</c>

00:04:17.469 --> 00:04:17.479 align:start position:0%
by the so Reserve yielding 2,000 Cuban
 

00:04:17.479 --> 00:04:20.710 align:start position:0%
by the so Reserve yielding 2,000 Cuban
per<00:04:17.759><c> so</c><00:04:18.720><c> this</c><00:04:18.959><c> ratio</c><00:04:19.359><c> shifts</c><00:04:19.919><c> with</c><00:04:20.199><c> every</c>

00:04:20.710 --> 00:04:20.720 align:start position:0%
per so this ratio shifts with every
 

00:04:20.720 --> 00:04:24.390 align:start position:0%
per so this ratio shifts with every
trade<00:04:21.720><c> when</c><00:04:22.120><c> a</c><00:04:22.320><c> Trader</c><00:04:22.919><c> Buys</c><00:04:23.479><c> so</c><00:04:24.040><c> from</c><00:04:24.240><c> the</c>

00:04:24.390 --> 00:04:24.400 align:start position:0%
trade when a Trader Buys so from the
 

00:04:24.400 --> 00:04:27.390 align:start position:0%
trade when a Trader Buys so from the
pool<00:04:24.960><c> the</c><00:04:25.160><c> so</c><00:04:25.800><c> Reserve</c><00:04:26.280><c> decreases</c><00:04:27.040><c> while</c><00:04:27.240><c> the</c>

00:04:27.390 --> 00:04:27.400 align:start position:0%
pool the so Reserve decreases while the
 

00:04:27.400 --> 00:04:29.870 align:start position:0%
pool the so Reserve decreases while the
Cuban<00:04:27.840><c> Reserve</c><00:04:28.240><c> increases</c><00:04:29.240><c> causing</c><00:04:29.600><c> the</c><00:04:29.759><c> the</c>

00:04:29.870 --> 00:04:29.880 align:start position:0%
Cuban Reserve increases causing the the
 

00:04:29.880 --> 00:04:32.110 align:start position:0%
Cuban Reserve increases causing the the
price<00:04:30.080><c> of</c><00:04:30.199><c> soul</c><00:04:30.479><c> to</c><00:04:30.639><c> rise</c><00:04:31.120><c> incrementally</c>

00:04:32.110 --> 00:04:32.120 align:start position:0%
price of soul to rise incrementally
 

00:04:32.120 --> 00:04:34.189 align:start position:0%
price of soul to rise incrementally
conversely<00:04:32.759><c> selling</c><00:04:33.160><c> soul</c><00:04:33.479><c> into</c><00:04:33.720><c> the</c><00:04:33.880><c> pool</c>

00:04:34.189 --> 00:04:34.199 align:start position:0%
conversely selling soul into the pool
 

00:04:34.199 --> 00:04:36.629 align:start position:0%
conversely selling soul into the pool
lowers<00:04:34.680><c> its</c><00:04:34.960><c> price</c><00:04:35.840><c> when</c><00:04:36.000><c> the</c><00:04:36.160><c> reserves</c>

00:04:36.629 --> 00:04:36.639 align:start position:0%
lowers its price when the reserves
 

00:04:36.639 --> 00:04:38.830 align:start position:0%
lowers its price when the reserves
become<00:04:36.960><c> unbalanced</c><00:04:37.560><c> a</c><00:04:37.800><c> phenomenon</c><00:04:38.400><c> called</c>

00:04:38.830 --> 00:04:38.840 align:start position:0%
become unbalanced a phenomenon called
 

00:04:38.840 --> 00:04:42.550 align:start position:0%
become unbalanced a phenomenon called
slipage<00:04:39.960><c> occurs</c><00:04:40.960><c> which</c><00:04:41.240><c> grows</c><00:04:41.800><c> with</c><00:04:42.120><c> larger</c>

00:04:42.550 --> 00:04:42.560 align:start position:0%
slipage occurs which grows with larger
 

00:04:42.560 --> 00:04:44.710 align:start position:0%
slipage occurs which grows with larger
trades<00:04:43.039><c> due</c><00:04:43.280><c> to</c><00:04:43.400><c> the</c><00:04:43.560><c> curvature</c><00:04:44.320><c> of</c><00:04:44.520><c> the</c>

00:04:44.710 --> 00:04:44.720 align:start position:0%
trades due to the curvature of the
 

00:04:44.720 --> 00:04:47.790 align:start position:0%
trades due to the curvature of the
constant<00:04:45.160><c> product</c><00:04:45.479><c> curve</c><00:04:46.479><c> as</c><00:04:46.600><c> a</c><00:04:46.840><c> result</c><00:04:47.639><c> the</c>

00:04:47.790 --> 00:04:47.800 align:start position:0%
constant product curve as a result the
 

00:04:47.800 --> 00:04:49.550 align:start position:0%
constant product curve as a result the
greater<00:04:48.160><c> the</c><00:04:48.320><c> imbalance</c><00:04:48.840><c> the</c><00:04:48.960><c> larger</c><00:04:49.360><c> the</c>

00:04:49.550 --> 00:04:49.560 align:start position:0%
greater the imbalance the larger the
 

00:04:49.560 --> 00:04:52.350 align:start position:0%
greater the imbalance the larger the
price<00:04:50.039><c> slippage</c><00:04:50.919><c> which</c><00:04:51.039><c> is</c><00:04:51.320><c> why</c><00:04:51.639><c> it</c><00:04:51.800><c> only</c><00:04:52.120><c> took</c>

00:04:52.350 --> 00:04:52.360 align:start position:0%
price slippage which is why it only took
 

00:04:52.360 --> 00:04:55.749 align:start position:0%
price slippage which is why it only took
$1<00:04:52.759><c> million</c><00:04:53.680><c> worth</c><00:04:54.160><c> of</c><00:04:54.479><c> bind</c><00:04:54.840><c> to</c><00:04:55.000><c> send</c><00:04:55.360><c> Cuban</c>

00:04:55.749 --> 00:04:55.759 align:start position:0%
$1 million worth of bind to send Cuban
 

00:04:55.759 --> 00:04:58.870 align:start position:0%
$1 million worth of bind to send Cuban
price<00:04:56.039><c> to</c><00:04:56.199><c> a</c><00:04:56.320><c> staggering</c><00:04:57.240><c> 400</c><00:04:58.080><c> million</c><00:04:58.560><c> plus</c>

00:04:58.870 --> 00:04:58.880 align:start position:0%
price to a staggering 400 million plus
 

00:04:58.880 --> 00:05:01.590 align:start position:0%
price to a staggering 400 million plus
US<00:04:59.160><c> dollar</c><00:04:59.400><c> value</c><00:04:59.880><c> ation</c><00:05:00.759><c> considering</c><00:05:01.479><c> the</c>

00:05:01.590 --> 00:05:01.600 align:start position:0%
US dollar value ation considering the
 

00:05:01.600 --> 00:05:03.749 align:start position:0%
US dollar value ation considering the
initial<00:05:02.000><c> supply</c><00:05:02.360><c> of</c><00:05:02.520><c> Cuban</c><00:05:02.880><c> and</c><00:05:03.080><c> so</c><00:05:03.479><c> in</c><00:05:03.600><c> the</c>

00:05:03.749 --> 00:05:03.759 align:start position:0%
initial supply of Cuban and so in the
 

00:05:03.759 --> 00:05:06.990 align:start position:0%
initial supply of Cuban and so in the
Dex<00:05:04.120><c> reserves</c><00:05:04.759><c> was</c><00:05:05.039><c> very</c><00:05:05.280><c> limited</c><00:05:06.280><c> in</c><00:05:06.479><c> essence</c>

00:05:06.990 --> 00:05:07.000 align:start position:0%
Dex reserves was very limited in essence
 

00:05:07.000 --> 00:05:09.189 align:start position:0%
Dex reserves was very limited in essence
liquidity<00:05:07.479><c> pool</c><00:05:07.919><c> enable</c><00:05:08.440><c> decentralized</c>

00:05:09.189 --> 00:05:09.199 align:start position:0%
liquidity pool enable decentralized
 

00:05:09.199 --> 00:05:11.950 align:start position:0%
liquidity pool enable decentralized
trading<00:05:09.720><c> by</c><00:05:10.400><c> algorithmically</c><00:05:11.400><c> balancing</c>

00:05:11.950 --> 00:05:11.960 align:start position:0%
trading by algorithmically balancing
 

00:05:11.960 --> 00:05:14.790 align:start position:0%
trading by algorithmically balancing
supply<00:05:12.360><c> and</c><00:05:12.520><c> demand</c><00:05:12.919><c> through</c><00:05:13.120><c> token</c><00:05:13.800><c> reserves</c>

00:05:14.790 --> 00:05:14.800 align:start position:0%
supply and demand through token reserves
 

00:05:14.800 --> 00:05:17.310 align:start position:0%
supply and demand through token reserves
prices<00:05:15.240><c> adjust</c><00:05:15.840><c> simly</c><00:05:16.840><c> with</c><00:05:17.039><c> each</c>

00:05:17.310 --> 00:05:17.320 align:start position:0%
prices adjust simly with each
 

00:05:17.320 --> 00:05:20.150 align:start position:0%
prices adjust simly with each
transactions<00:05:18.080><c> ensuring</c><00:05:18.840><c> liquidity</c><00:05:19.840><c> but</c>

00:05:20.150 --> 00:05:20.160 align:start position:0%
transactions ensuring liquidity but
 

00:05:20.160 --> 00:05:22.590 align:start position:0%
transactions ensuring liquidity but
introducing<00:05:20.759><c> trade-offs</c><00:05:21.440><c> like</c><00:05:21.680><c> slipage</c><00:05:22.360><c> and</c>

00:05:22.590 --> 00:05:22.600 align:start position:0%
introducing trade-offs like slipage and
 

00:05:22.600 --> 00:05:25.430 align:start position:0%
introducing trade-offs like slipage and
impairment<00:05:23.199><c> loss</c><00:05:24.160><c> which</c><00:05:24.360><c> occurs</c><00:05:24.960><c> when</c><00:05:25.280><c> the</c>

00:05:25.430 --> 00:05:25.440 align:start position:0%
impairment loss which occurs when the
 

00:05:25.440 --> 00:05:27.070 align:start position:0%
impairment loss which occurs when the
external<00:05:25.880><c> market</c><00:05:26.199><c> price</c><00:05:26.400><c> of</c><00:05:26.520><c> a</c><00:05:26.680><c> token</c>

00:05:27.070 --> 00:05:27.080 align:start position:0%
external market price of a token
 

00:05:27.080 --> 00:05:30.469 align:start position:0%
external market price of a token
diverges<00:05:27.680><c> from</c><00:05:27.880><c> the</c><00:05:28.039><c> pools</c><00:05:28.600><c> ratio</c>

00:05:30.469 --> 00:05:30.479 align:start position:0%
diverges from the pools ratio
 

00:05:30.479 --> 00:05:32.270 align:start position:0%
diverges from the pools ratio
how<00:05:30.680><c> does</c><00:05:30.840><c> a</c><00:05:31.000><c> scammer</c><00:05:31.520><c> profit</c><00:05:31.840><c> from</c><00:05:32.080><c> this</c>

00:05:32.270 --> 00:05:32.280 align:start position:0%
how does a scammer profit from this
 

00:05:32.280 --> 00:05:33.309 align:start position:0%
how does a scammer profit from this
trade

00:05:33.309 --> 00:05:33.319 align:start position:0%
trade
 

00:05:33.319 --> 00:05:36.950 align:start position:0%
trade
then<00:05:34.319><c> by</c><00:05:34.680><c> enticing</c><00:05:35.600><c> formal</c><00:05:36.240><c> Traders</c><00:05:36.720><c> or</c>

00:05:36.950 --> 00:05:36.960 align:start position:0%
then by enticing formal Traders or
 

00:05:36.960 --> 00:05:39.390 align:start position:0%
then by enticing formal Traders or
poorly<00:05:37.319><c> written</c><00:05:37.720><c> trading</c><00:05:38.039><c> algorithms</c><00:05:38.960><c> to</c><00:05:39.160><c> buy</c>

00:05:39.390 --> 00:05:39.400 align:start position:0%
poorly written trading algorithms to buy
 

00:05:39.400 --> 00:05:41.230 align:start position:0%
poorly written trading algorithms to buy
The<00:05:39.600><c> Limited</c><00:05:40.000><c> number</c><00:05:40.240><c> of</c><00:05:40.400><c> Kuban</c><00:05:40.800><c> left</c><00:05:40.960><c> in</c><00:05:41.120><c> the</c>

00:05:41.230 --> 00:05:41.240 align:start position:0%
The Limited number of Kuban left in the
 

00:05:41.240 --> 00:05:43.950 align:start position:0%
The Limited number of Kuban left in the
pool<00:05:41.479><c> for</c><00:05:41.639><c> a</c><00:05:41.800><c> hyperinflated</c><00:05:42.720><c> price</c><00:05:43.560><c> and</c>

00:05:43.950 --> 00:05:43.960 align:start position:0%
pool for a hyperinflated price and
 

00:05:43.960 --> 00:05:45.950 align:start position:0%
pool for a hyperinflated price and
supplying<00:05:44.560><c> a</c><00:05:44.680><c> large</c><00:05:44.960><c> amount</c><00:05:45.199><c> of</c><00:05:45.360><c> soul</c><00:05:45.720><c> in</c><00:05:45.800><c> the</c>

00:05:45.950 --> 00:05:45.960 align:start position:0%
supplying a large amount of soul in the
 

00:05:45.960 --> 00:05:48.870 align:start position:0%
supplying a large amount of soul in the
reserve<00:05:46.440><c> pool</c><00:05:47.440><c> at</c><00:05:47.639><c> this</c><00:05:47.880><c> point</c><00:05:48.280><c> the</c><00:05:48.440><c> trader</c>

00:05:48.870 --> 00:05:48.880 align:start position:0%
reserve pool at this point the trader
 

00:05:48.880 --> 00:05:52.990 align:start position:0%
reserve pool at this point the trader
who<00:05:49.360><c> pumped</c><00:05:49.960><c> the</c><00:05:50.160><c> token</c><00:05:50.800><c> then</c><00:05:50.960><c> sells</c><00:05:51.639><c> all</c><00:05:51.840><c> the</c>

00:05:52.990 --> 00:05:53.000 align:start position:0%
who pumped the token then sells all the
 

00:05:53.000 --> 00:05:54.830 align:start position:0%
who pumped the token then sells all the
supplied<00:05:54.000><c> he</c>

00:05:54.830 --> 00:05:54.840 align:start position:0%
supplied he
 

00:05:54.840 --> 00:05:58.629 align:start position:0%
supplied he
holds<00:05:55.840><c> extracting</c><00:05:56.680><c> as</c><00:05:56.919><c> many</c><00:05:57.520><c> Soul</c><00:05:58.360><c> as</c>

00:05:58.629 --> 00:05:58.639 align:start position:0%
holds extracting as many Soul as
 

00:05:58.639 --> 00:06:00.830 align:start position:0%
holds extracting as many Soul as
possible<00:05:59.039><c> from</c><00:05:59.199><c> the</c><00:05:59.360><c> res</c><00:06:00.080><c> and</c><00:06:00.280><c> crashing</c><00:06:00.720><c> the</c>

00:06:00.830 --> 00:06:00.840 align:start position:0%
possible from the res and crashing the
 

00:06:00.840 --> 00:06:03.189 align:start position:0%
possible from the res and crashing the
Cuban<00:06:01.240><c> price</c><00:06:01.479><c> as</c><00:06:01.600><c> a</c><00:06:01.759><c> consequence</c><00:06:02.680><c> you</c><00:06:02.800><c> can</c><00:06:02.960><c> see</c>

00:06:03.189 --> 00:06:03.199 align:start position:0%
Cuban price as a consequence you can see
 

00:06:03.199 --> 00:06:05.870 align:start position:0%
Cuban price as a consequence you can see
the<00:06:03.400><c> final</c><00:06:03.759><c> result</c><00:06:04.319><c> in</c><00:06:04.520><c> the</c><00:06:04.720><c> chart</c><00:06:05.240><c> here</c><00:06:05.680><c> with</c>

00:06:05.870 --> 00:06:05.880 align:start position:0%
the final result in the chart here with
 

00:06:05.880 --> 00:06:08.390 align:start position:0%
the final result in the chart here with
the<00:06:06.039><c> Cuban</c><00:06:06.479><c> now</c><00:06:06.639><c> worth</c><00:06:07.080><c> a</c><00:06:07.280><c> total</c><00:06:07.680><c> of</c>

00:06:08.390 --> 00:06:08.400 align:start position:0%
the Cuban now worth a total of
 

00:06:08.400 --> 00:06:12.830 align:start position:0%
the Cuban now worth a total of
$3,000<00:06:09.400><c> just</c><00:06:09.840><c> 24</c><00:06:10.479><c> hours</c><00:06:11.080><c> after</c><00:06:11.520><c> its</c><00:06:11.840><c> launch</c>

00:06:12.830 --> 00:06:12.840 align:start position:0%
$3,000 just 24 hours after its launch
 

00:06:12.840 --> 00:06:15.749 align:start position:0%
$3,000 just 24 hours after its launch
red<00:06:13.120><c> flag</c><00:06:13.479><c> number</c><00:06:13.800><c> three</c><00:06:14.599><c> no</c><00:06:14.960><c> core</c><00:06:15.240><c> team</c>

00:06:15.749 --> 00:06:15.759 align:start position:0%
red flag number three no core team
 

00:06:15.759 --> 00:06:18.390 align:start position:0%
red flag number three no core team
activity<00:06:16.319><c> in</c><00:06:16.520><c> support</c><00:06:17.000><c> of</c><00:06:17.160><c> the</c><00:06:17.319><c> token</c><00:06:18.240><c> a</c>

00:06:18.390 --> 00:06:18.400 align:start position:0%
activity in support of the token a
 

00:06:18.400 --> 00:06:20.309 align:start position:0%
activity in support of the token a
crypto<00:06:18.840><c> token</c><00:06:19.080><c> should</c><00:06:19.440><c> Express</c><00:06:19.800><c> the</c><00:06:19.960><c> value</c><00:06:20.199><c> of</c>

00:06:20.309 --> 00:06:20.319 align:start position:0%
crypto token should Express the value of
 

00:06:20.319 --> 00:06:22.870 align:start position:0%
crypto token should Express the value of
a<00:06:20.520><c> project</c><00:06:20.919><c> and</c><00:06:21.080><c> the</c><00:06:21.240><c> team</c><00:06:21.599><c> pursuing</c><00:06:22.120><c> it</c><00:06:22.720><c> at</c>

00:06:22.870 --> 00:06:22.880 align:start position:0%
a project and the team pursuing it at
 

00:06:22.880 --> 00:06:25.309 align:start position:0%
a project and the team pursuing it at
least<00:06:23.080><c> in</c><00:06:23.240><c> theory</c><00:06:24.000><c> this</c><00:06:24.199><c> can</c><00:06:24.360><c> be</c><00:06:24.560><c> very</c>

00:06:25.309 --> 00:06:25.319 align:start position:0%
least in theory this can be very
 

00:06:25.319 --> 00:06:27.950 align:start position:0%
least in theory this can be very
straightforward<00:06:26.319><c> with</c><00:06:26.520><c> the</c><00:06:26.680><c> team</c><00:06:26.960><c> identities</c>

00:06:27.950 --> 00:06:27.960 align:start position:0%
straightforward with the team identities
 

00:06:27.960 --> 00:06:31.029 align:start position:0%
straightforward with the team identities
disclosed<00:06:28.960><c> or</c><00:06:29.919><c> indirect</c><00:06:30.520><c> when</c><00:06:30.680><c> the</c><00:06:30.840><c> team</c>

00:06:31.029 --> 00:06:31.039 align:start position:0%
disclosed or indirect when the team
 

00:06:31.039 --> 00:06:33.029 align:start position:0%
disclosed or indirect when the team
members<00:06:31.400><c> use</c><00:06:31.639><c> social</c><00:06:31.919><c> media</c><00:06:32.240><c> pseudonyms</c><00:06:32.880><c> to</c>

00:06:33.029 --> 00:06:33.039 align:start position:0%
members use social media pseudonyms to
 

00:06:33.039 --> 00:06:34.589 align:start position:0%
members use social media pseudonyms to
maintain<00:06:33.400><c> their</c><00:06:33.599><c> privacy</c><00:06:34.120><c> while</c><00:06:34.280><c> still</c>

00:06:34.589 --> 00:06:34.599 align:start position:0%
maintain their privacy while still
 

00:06:34.599 --> 00:06:36.790 align:start position:0%
maintain their privacy while still
performing<00:06:35.160><c> actions</c><00:06:35.599><c> like</c><00:06:35.840><c> setting</c><00:06:36.199><c> up</c><00:06:36.400><c> and</c>

00:06:36.790 --> 00:06:36.800 align:start position:0%
performing actions like setting up and
 

00:06:36.800 --> 00:06:38.830 align:start position:0%
performing actions like setting up and
operating<00:06:37.240><c> social</c><00:06:37.599><c> media</c><00:06:37.919><c> accounts</c><00:06:38.560><c> or</c>

00:06:38.830 --> 00:06:38.840 align:start position:0%
operating social media accounts or
 

00:06:38.840 --> 00:06:40.350 align:start position:0%
operating social media accounts or
interacting<00:06:39.400><c> with</c><00:06:39.560><c> the</c><00:06:39.680><c> community</c><00:06:40.080><c> Through</c>

00:06:40.350 --> 00:06:40.360 align:start position:0%
interacting with the community Through
 

00:06:40.360 --> 00:06:41.189 align:start position:0%
interacting with the community Through
various

00:06:41.189 --> 00:06:41.199 align:start position:0%
various
 

00:06:41.199 --> 00:06:43.950 align:start position:0%
various
channels<00:06:42.199><c> when</c><00:06:42.400><c> there</c><00:06:42.560><c> is</c><00:06:42.840><c> no</c><00:06:43.160><c> sign</c><00:06:43.639><c> of</c>

00:06:43.950 --> 00:06:43.960 align:start position:0%
channels when there is no sign of
 

00:06:43.960 --> 00:06:45.990 align:start position:0%
channels when there is no sign of
proactive<00:06:44.520><c> commitment</c><00:06:45.000><c> to</c><00:06:45.160><c> build</c><00:06:45.440><c> a</c><00:06:45.680><c> project</c>

00:06:45.990 --> 00:06:46.000 align:start position:0%
proactive commitment to build a project
 

00:06:46.000 --> 00:06:48.070 align:start position:0%
proactive commitment to build a project
behind<00:06:46.240><c> a</c><00:06:46.440><c> token</c><00:06:47.199><c> that</c><00:06:47.360><c> is</c><00:06:47.520><c> assigned</c><00:06:47.919><c> the</c>

00:06:48.070 --> 00:06:48.080 align:start position:0%
behind a token that is assigned the
 

00:06:48.080 --> 00:06:49.629 align:start position:0%
behind a token that is assigned the
token<00:06:48.319><c> is</c><00:06:48.520><c> potentially</c><00:06:48.919><c> a</c><00:06:49.039><c> pump</c><00:06:49.240><c> and</c><00:06:49.400><c> dump</c>

00:06:49.629 --> 00:06:49.639 align:start position:0%
token is potentially a pump and dump
 

00:06:49.639 --> 00:06:52.390 align:start position:0%
token is potentially a pump and dump
scam<00:06:50.520><c> let's</c><00:06:50.720><c> take</c><00:06:50.919><c> our</c><00:06:51.240><c> Cuban</c><00:06:51.680><c> case</c><00:06:52.160><c> for</c>

00:06:52.390 --> 00:06:52.400 align:start position:0%
scam let's take our Cuban case for
 

00:06:52.400 --> 00:06:55.589 align:start position:0%
scam let's take our Cuban case for
example<00:06:53.360><c> if</c><00:06:53.560><c> you</c><00:06:53.759><c> go</c><00:06:54.000><c> on</c><00:06:54.160><c> the</c><00:06:54.360><c> token</c><00:06:54.919><c> page</c><00:06:55.319><c> of</c><00:06:55.440><c> a</c>

00:06:55.589 --> 00:06:55.599 align:start position:0%
example if you go on the token page of a
 

00:06:55.599 --> 00:06:58.150 align:start position:0%
example if you go on the token page of a
platform<00:06:56.599><c> like</c><00:06:56.840><c> the</c><00:06:57.120><c> screener</c><00:06:57.680><c> and</c><00:06:57.840><c> look</c><00:06:58.000><c> for</c>

00:06:58.150 --> 00:06:58.160 align:start position:0%
platform like the screener and look for
 

00:06:58.160 --> 00:07:00.350 align:start position:0%
platform like the screener and look for
a<00:06:58.360><c> social</c><00:06:58.599><c> media</c><00:06:58.919><c> counseling</c><00:06:59.400><c> to</c><00:06:59.680><c> the</c><00:06:59.800><c> token</c>

00:07:00.350 --> 00:07:00.360 align:start position:0%
a social media counseling to the token
 

00:07:00.360 --> 00:07:02.390 align:start position:0%
a social media counseling to the token
you<00:07:00.479><c> can</c><00:07:00.639><c> see</c><00:07:00.879><c> there</c><00:07:01.039><c> is</c><00:07:01.319><c> a</c><00:07:01.440><c> Twitter</c><00:07:01.800><c> X</c><00:07:02.120><c> account</c>

00:07:02.390 --> 00:07:02.400 align:start position:0%
you can see there is a Twitter X account
 

00:07:02.400 --> 00:07:05.350 align:start position:0%
you can see there is a Twitter X account
set<00:07:02.680><c> up</c><00:07:03.680><c> this</c><00:07:03.840><c> is</c><00:07:04.280><c> of</c><00:07:04.479><c> course</c><00:07:04.840><c> to</c><00:07:05.039><c> give</c><00:07:05.240><c> the</c>

00:07:05.350 --> 00:07:05.360 align:start position:0%
set up this is of course to give the
 

00:07:05.360 --> 00:07:07.110 align:start position:0%
set up this is of course to give the
impression<00:07:05.720><c> that</c><00:07:05.879><c> the</c><00:07:05.960><c> real</c><00:07:06.240><c> Mark</c><00:07:06.599><c> Cuban</c><00:07:06.919><c> is</c>

00:07:07.110 --> 00:07:07.120 align:start position:0%
impression that the real Mark Cuban is
 

00:07:07.120 --> 00:07:10.029 align:start position:0%
impression that the real Mark Cuban is
eventually<00:07:07.639><c> behind</c><00:07:07.960><c> this</c><00:07:08.160><c> token</c><00:07:08.680><c> launch</c><00:07:09.680><c> as</c>

00:07:10.029 --> 00:07:10.039 align:start position:0%
eventually behind this token launch as
 

00:07:10.039 --> 00:07:13.029 align:start position:0%
eventually behind this token launch as
the<00:07:10.199><c> reason</c><00:07:10.560><c> why</c><00:07:10.720><c> it</c><00:07:10.800><c> is</c><00:07:11.000><c> worth</c><00:07:11.199><c> $400</c><00:07:12.039><c> million</c>

00:07:13.029 --> 00:07:13.039 align:start position:0%
the reason why it is worth $400 million
 

00:07:13.039 --> 00:07:16.029 align:start position:0%
the reason why it is worth $400 million
and<00:07:13.440><c> skyrocketing</c><00:07:14.440><c> right</c><00:07:15.360><c> if</c><00:07:15.560><c> you</c><00:07:15.680><c> click</c><00:07:15.879><c> on</c>

00:07:16.029 --> 00:07:16.039 align:start position:0%
and skyrocketing right if you click on
 

00:07:16.039 --> 00:07:18.550 align:start position:0%
and skyrocketing right if you click on
that<00:07:16.240><c> link</c><00:07:16.639><c> you</c><00:07:16.759><c> can</c><00:07:16.960><c> see</c><00:07:17.319><c> it</c><00:07:17.479><c> isn't</c><00:07:17.800><c> linked</c><00:07:18.280><c> to</c>

00:07:18.550 --> 00:07:18.560 align:start position:0%
that link you can see it isn't linked to
 

00:07:18.560 --> 00:07:21.029 align:start position:0%
that link you can see it isn't linked to
Mark<00:07:18.919><c> Cuban</c><00:07:19.319><c> profile</c><00:07:19.960><c> but</c><00:07:20.160><c> to</c><00:07:20.280><c> a</c><00:07:20.400><c> single</c><00:07:20.759><c> tweet</c>

00:07:21.029 --> 00:07:21.039 align:start position:0%
Mark Cuban profile but to a single tweet
 

00:07:21.039 --> 00:07:22.629 align:start position:0%
Mark Cuban profile but to a single tweet
from<00:07:21.199><c> him</c><00:07:21.440><c> that</c><00:07:21.560><c> isn't</c><00:07:21.800><c> even</c><00:07:22.039><c> related</c><00:07:22.400><c> to</c><00:07:22.520><c> the</c>

00:07:22.629 --> 00:07:22.639 align:start position:0%
from him that isn't even related to the
 

00:07:22.639 --> 00:07:25.469 align:start position:0%
from him that isn't even related to the
launch<00:07:22.879><c> of</c><00:07:23.080><c> Cuban</c><00:07:24.080><c> clearly</c><00:07:24.560><c> Mar</c><00:07:24.879><c> cuan</c><00:07:25.280><c> had</c>

00:07:25.469 --> 00:07:25.479 align:start position:0%
launch of Cuban clearly Mar cuan had
 

00:07:25.479 --> 00:07:27.909 align:start position:0%
launch of Cuban clearly Mar cuan had
nothing<00:07:25.759><c> to</c><00:07:25.919><c> do</c><00:07:26.160><c> with</c><00:07:26.360><c> this</c><00:07:26.639><c> scam</c><00:07:27.599><c> I</c><00:07:27.680><c> would</c>

00:07:27.909 --> 00:07:27.919 align:start position:0%
nothing to do with this scam I would
 

00:07:27.919 --> 00:07:29.749 align:start position:0%
nothing to do with this scam I would
like<00:07:28.080><c> to</c><00:07:28.240><c> share</c><00:07:28.560><c> with</c><00:07:28.680><c> you</c><00:07:28.840><c> a</c><00:07:28.960><c> more</c><00:07:29.160><c> sub</c>

00:07:29.749 --> 00:07:29.759 align:start position:0%
like to share with you a more sub
 

00:07:29.759 --> 00:07:31.390 align:start position:0%
like to share with you a more sub
example<00:07:30.080><c> of</c><00:07:30.240><c> another</c><00:07:30.599><c> token</c><00:07:30.919><c> that</c><00:07:31.080><c> became</c>

00:07:31.390 --> 00:07:31.400 align:start position:0%
example of another token that became
 

00:07:31.400 --> 00:07:33.110 align:start position:0%
example of another token that became
very<00:07:31.599><c> popular</c><00:07:31.960><c> and</c><00:07:32.080><c> widely</c><00:07:32.479><c> traded</c><00:07:32.840><c> in</c><00:07:32.960><c> the</c>

00:07:33.110 --> 00:07:33.120 align:start position:0%
very popular and widely traded in the
 

00:07:33.120 --> 00:07:34.869 align:start position:0%
very popular and widely traded in the
past<00:07:33.360><c> week</c>

00:07:34.869 --> 00:07:34.879 align:start position:0%
past week
 

00:07:34.879 --> 00:07:37.670 align:start position:0%
past week
routine<00:07:35.879><c> here</c><00:07:36.360><c> there</c><00:07:36.520><c> was</c><00:07:36.840><c> a</c><00:07:36.960><c> clear</c><00:07:37.280><c> attempt</c>

00:07:37.670 --> 00:07:37.680 align:start position:0%
routine here there was a clear attempt
 

00:07:37.680 --> 00:07:39.629 align:start position:0%
routine here there was a clear attempt
to<00:07:37.800><c> launch</c><00:07:38.080><c> a</c><00:07:38.240><c> pump</c><00:07:38.479><c> and</c><00:07:38.680><c> dump</c><00:07:39.000><c> on</c><00:07:39.120><c> the</c><00:07:39.280><c> back</c><00:07:39.479><c> of</c>

00:07:39.629 --> 00:07:39.639 align:start position:0%
to launch a pump and dump on the back of
 

00:07:39.639 --> 00:07:42.469 align:start position:0%
to launch a pump and dump on the back of
a<00:07:40.080><c> viral</c><00:07:40.759><c> morning</c><00:07:41.160><c> routine</c><00:07:41.639><c> video</c><00:07:42.000><c> on</c><00:07:42.199><c> social</c>

00:07:42.469 --> 00:07:42.479 align:start position:0%
a viral morning routine video on social
 

00:07:42.479 --> 00:07:44.550 align:start position:0%
a viral morning routine video on social
media<00:07:43.240><c> giving</c><00:07:43.520><c> the</c><00:07:43.639><c> first</c><00:07:44.000><c> impression</c><00:07:44.360><c> that</c>

00:07:44.550 --> 00:07:44.560 align:start position:0%
media giving the first impression that
 

00:07:44.560 --> 00:07:47.390 align:start position:0%
media giving the first impression that
Ashton<00:07:45.000><c> Hall</c><00:07:45.280><c> was</c><00:07:45.479><c> supporting</c><00:07:45.919><c> the</c><00:07:46.400><c> launch</c>

00:07:47.390 --> 00:07:47.400 align:start position:0%
Ashton Hall was supporting the launch
 

00:07:47.400 --> 00:07:49.589 align:start position:0%
Ashton Hall was supporting the launch
however<00:07:48.000><c> if</c><00:07:48.120><c> you</c><00:07:48.400><c> click</c><00:07:48.720><c> on</c><00:07:48.879><c> the</c><00:07:49.039><c> social</c><00:07:49.280><c> media</c>

00:07:49.589 --> 00:07:49.599 align:start position:0%
however if you click on the social media
 

00:07:49.599 --> 00:07:51.629 align:start position:0%
however if you click on the social media
links<00:07:49.960><c> provided</c><00:07:50.639><c> all</c><00:07:50.800><c> you</c><00:07:51.000><c> get</c><00:07:51.240><c> is</c><00:07:51.400><c> being</c>

00:07:51.629 --> 00:07:51.639 align:start position:0%
links provided all you get is being
 

00:07:51.639 --> 00:07:54.189 align:start position:0%
links provided all you get is being
redirected<00:07:52.319><c> to</c><00:07:52.639><c> the</c><00:07:52.800><c> single</c><00:07:53.199><c> Twitter</c><00:07:53.680><c> post</c><00:07:54.039><c> of</c>

00:07:54.189 --> 00:07:54.199 align:start position:0%
redirected to the single Twitter post of
 

00:07:54.199 --> 00:07:57.749 align:start position:0%
redirected to the single Twitter post of
the<00:07:54.360><c> video</c><00:07:55.159><c> and</c><00:07:55.360><c> clicking</c><00:07:55.800><c> on</c><00:07:56.039><c> the</c><00:07:56.960><c> website</c>

00:07:57.749 --> 00:07:57.759 align:start position:0%
the video and clicking on the website
 

00:07:57.759 --> 00:07:59.869 align:start position:0%
the video and clicking on the website
link<00:07:58.240><c> you</c><00:07:58.360><c> are</c><00:07:58.560><c> just</c><00:07:58.759><c> redirected</c><00:07:59.280><c> to</c><00:07:59.560><c> the</c><00:07:59.680><c> web</c>

00:07:59.869 --> 00:07:59.879 align:start position:0%
link you are just redirected to the web
 

00:07:59.879 --> 00:08:01.950 align:start position:0%
link you are just redirected to the web
link<00:08:00.120><c> of</c><00:08:00.280><c> Ashton</c><00:08:00.639><c> Hall's</c><00:08:01.000><c> Tik</c><00:08:01.240><c> Tok</c><00:08:01.440><c> profile</c>

00:08:01.950 --> 00:08:01.960 align:start position:0%
link of Ashton Hall's Tik Tok profile
 

00:08:01.960 --> 00:08:04.110 align:start position:0%
link of Ashton Hall's Tik Tok profile
instead

00:08:04.110 --> 00:08:04.120 align:start position:0%
instead
 

00:08:04.120 --> 00:08:08.270 align:start position:0%
instead
of<00:08:05.120><c> the</c><00:08:05.360><c> direct</c><00:08:05.879><c> profile</c><00:08:06.479><c> on</c><00:08:07.000><c> Tik</c><00:08:07.280><c> Tok</c><00:08:07.800><c> behind</c>

00:08:08.270 --> 00:08:08.280 align:start position:0%
of the direct profile on Tik Tok behind
 

00:08:08.280 --> 00:08:12.469 align:start position:0%
of the direct profile on Tik Tok behind
a<00:08:08.560><c> proper</c><00:08:09.280><c> Tik</c><00:08:09.599><c> Tok</c><00:08:10.039><c> direct</c><00:08:10.879><c> button</c><00:08:11.879><c> red</c><00:08:12.120><c> flag</c>

00:08:12.469 --> 00:08:12.479 align:start position:0%
a proper Tik Tok direct button red flag
 

00:08:12.479 --> 00:08:14.869 align:start position:0%
a proper Tik Tok direct button red flag
number<00:08:12.720><c> four</c><00:08:13.440><c> immediate</c><00:08:14.000><c> offthe</c><00:08:14.440><c> chart</c>

00:08:14.869 --> 00:08:14.879 align:start position:0%
number four immediate offthe chart
 

00:08:14.879 --> 00:08:17.950 align:start position:0%
number four immediate offthe chart
social<00:08:15.199><c> media</c><00:08:15.960><c> hype</c><00:08:16.960><c> continuing</c><00:08:17.599><c> with</c><00:08:17.759><c> the</c>

00:08:17.950 --> 00:08:17.960 align:start position:0%
social media hype continuing with the
 

00:08:17.960 --> 00:08:21.189 align:start position:0%
social media hype continuing with the
routine<00:08:18.400><c> token</c><00:08:18.840><c> example</c><00:08:19.639><c> it</c><00:08:19.800><c> is</c><00:08:20.199><c> very</c><00:08:20.440><c> easy</c><00:08:20.680><c> to</c>

00:08:21.189 --> 00:08:21.199 align:start position:0%
routine token example it is very easy to
 

00:08:21.199 --> 00:08:23.469 align:start position:0%
routine token example it is very easy to
understand<00:08:21.960><c> the</c><00:08:22.240><c> significant</c><00:08:22.800><c> social</c><00:08:23.080><c> media</c>

00:08:23.469 --> 00:08:23.479 align:start position:0%
understand the significant social media
 

00:08:23.479 --> 00:08:25.990 align:start position:0%
understand the significant social media
hype<00:08:23.800><c> from</c><00:08:24.039><c> many</c><00:08:24.360><c> accounts</c><00:08:25.240><c> many</c><00:08:25.520><c> of</c><00:08:25.720><c> which</c>

00:08:25.990 --> 00:08:26.000 align:start position:0%
hype from many accounts many of which
 

00:08:26.000 --> 00:08:28.990 align:start position:0%
hype from many accounts many of which
are<00:08:26.440><c> obviously</c><00:08:27.000><c> Bots</c><00:08:28.000><c> bringing</c><00:08:28.479><c> attention</c><00:08:28.840><c> to</c>

00:08:28.990 --> 00:08:29.000 align:start position:0%
are obviously Bots bringing attention to
 

00:08:29.000 --> 00:08:30.950 align:start position:0%
are obviously Bots bringing attention to
the<00:08:29.120><c> crypto</c><00:08:29.599><c> meme</c><00:08:29.919><c> by</c><00:08:30.120><c> perpetuating</c><00:08:30.800><c> the</c>

00:08:30.950 --> 00:08:30.960 align:start position:0%
the crypto meme by perpetuating the
 

00:08:30.960 --> 00:08:32.550 align:start position:0%
the crypto meme by perpetuating the
false<00:08:31.360><c> impression</c><00:08:31.800><c> that</c><00:08:31.960><c> the</c><00:08:32.080><c> launch</c><00:08:32.399><c> was</c>

00:08:32.550 --> 00:08:32.560 align:start position:0%
false impression that the launch was
 

00:08:32.560 --> 00:08:34.709 align:start position:0%
false impression that the launch was
endorsed<00:08:33.000><c> by</c><00:08:33.159><c> Ashton</c><00:08:33.560><c> all</c><00:08:33.839><c> to</c><00:08:34.039><c> trigger</c>

00:08:34.709 --> 00:08:34.719 align:start position:0%
endorsed by Ashton all to trigger
 

00:08:34.719 --> 00:08:36.870 align:start position:0%
endorsed by Ashton all to trigger
Traders<00:08:35.479><c> fear</c><00:08:35.760><c> of</c><00:08:35.919><c> missing</c><00:08:36.240><c> out</c><00:08:36.440><c> for</c><00:08:36.719><c> the</c>

00:08:36.870 --> 00:08:36.880 align:start position:0%
Traders fear of missing out for the
 

00:08:36.880 --> 00:08:39.630 align:start position:0%
Traders fear of missing out for the
token<00:08:37.880><c> high</c><00:08:38.120><c> social</c><00:08:38.440><c> media</c><00:08:38.760><c> hype</c><00:08:39.000><c> for</c><00:08:39.159><c> a</c><00:08:39.320><c> token</c>

00:08:39.630 --> 00:08:39.640 align:start position:0%
token high social media hype for a token
 

00:08:39.640 --> 00:08:41.630 align:start position:0%
token high social media hype for a token
isn't<00:08:39.959><c> necessarily</c><00:08:40.399><c> a</c><00:08:40.519><c> red</c><00:08:40.719><c> flag</c><00:08:41.240><c> provided</c>

00:08:41.630 --> 00:08:41.640 align:start position:0%
isn't necessarily a red flag provided
 

00:08:41.640 --> 00:08:43.190 align:start position:0%
isn't necessarily a red flag provided
that<00:08:41.800><c> the</c><00:08:41.959><c> team</c><00:08:42.279><c> behind</c><00:08:42.599><c> the</c><00:08:42.760><c> project</c><00:08:43.039><c> is</c>

00:08:43.190 --> 00:08:43.200 align:start position:0%
that the team behind the project is
 

00:08:43.200 --> 00:08:45.310 align:start position:0%
that the team behind the project is
delivering<00:08:43.719><c> results</c><00:08:44.560><c> reflected</c><00:08:45.040><c> in</c><00:08:45.160><c> an</c>

00:08:45.310 --> 00:08:45.320 align:start position:0%
delivering results reflected in an
 

00:08:45.320 --> 00:08:47.470 align:start position:0%
delivering results reflected in an
increase<00:08:45.720><c> in</c><00:08:45.920><c> value</c><00:08:46.200><c> of</c><00:08:46.279><c> the</c><00:08:46.440><c> token</c><00:08:47.080><c> and</c><00:08:47.279><c> the</c>

00:08:47.470 --> 00:08:47.480 align:start position:0%
increase in value of the token and the
 

00:08:47.480 --> 00:08:50.310 align:start position:0%
increase in value of the token and the
hype<00:08:47.920><c> usually</c><00:08:48.279><c> tends</c><00:08:48.560><c> to</c><00:08:48.720><c> grow</c><00:08:49.200><c> gradually</c><00:08:49.880><c> not</c>

00:08:50.310 --> 00:08:50.320 align:start position:0%
hype usually tends to grow gradually not
 

00:08:50.320 --> 00:08:51.990 align:start position:0%
hype usually tends to grow gradually not
right

00:08:51.990 --> 00:08:52.000 align:start position:0%
right
 

00:08:52.000 --> 00:08:54.910 align:start position:0%
right
away<00:08:53.000><c> red</c><00:08:53.240><c> flag</c><00:08:53.640><c> number</c><00:08:54.000><c> five</c><00:08:54.440><c> Supply</c>

00:08:54.910 --> 00:08:54.920 align:start position:0%
away red flag number five Supply
 

00:08:54.920 --> 00:08:56.790 align:start position:0%
away red flag number five Supply
concentration<00:08:55.640><c> without</c><00:08:55.880><c> a</c><00:08:56.080><c> project</c><00:08:56.399><c> team</c>

00:08:56.790 --> 00:08:56.800 align:start position:0%
concentration without a project team
 

00:08:56.800 --> 00:08:59.230 align:start position:0%
concentration without a project team
activity<00:08:57.800><c> this</c><00:08:58.040><c> red</c><00:08:58.279><c> flag</c><00:08:58.600><c> is</c><00:08:58.720><c> more</c><00:08:59.120><c> difficult</c>

00:08:59.230 --> 00:08:59.240 align:start position:0%
activity this red flag is more difficult
 

00:08:59.240 --> 00:09:01.389 align:start position:0%
activity this red flag is more difficult
to<00:08:59.440><c> to</c><00:08:59.600><c> spot</c><00:09:00.000><c> than</c><00:09:00.160><c> in</c><00:09:00.360><c> the</c><00:09:00.560><c> previous</c><00:09:01.000><c> example</c>

00:09:01.389 --> 00:09:01.399 align:start position:0%
to to spot than in the previous example
 

00:09:01.399 --> 00:09:03.550 align:start position:0%
to to spot than in the previous example
of<00:09:01.519><c> the</c><00:09:01.680><c> Cuban</c><00:09:02.040><c> token</c><00:09:02.519><c> and</c><00:09:02.720><c> usually</c><00:09:03.120><c> tends</c><00:09:03.399><c> to</c>

00:09:03.550 --> 00:09:03.560 align:start position:0%
of the Cuban token and usually tends to
 

00:09:03.560 --> 00:09:05.430 align:start position:0%
of the Cuban token and usually tends to
occur<00:09:03.920><c> in</c><00:09:04.040><c> more</c><00:09:04.320><c> sophisticated</c><00:09:05.040><c> pump</c><00:09:05.240><c> and</c>

00:09:05.430 --> 00:09:05.440 align:start position:0%
occur in more sophisticated pump and
 

00:09:05.440 --> 00:09:08.389 align:start position:0%
occur in more sophisticated pump and
dump<00:09:05.800><c> schemes</c><00:09:06.560><c> like</c><00:09:06.839><c> routine</c><00:09:07.839><c> as</c><00:09:07.920><c> you</c><00:09:08.040><c> can</c><00:09:08.200><c> see</c>

00:09:08.389 --> 00:09:08.399 align:start position:0%
dump schemes like routine as you can see
 

00:09:08.399 --> 00:09:10.430 align:start position:0%
dump schemes like routine as you can see
from<00:09:08.560><c> the</c><00:09:08.680><c> bubble</c><00:09:09.040><c> map</c><00:09:09.360><c> here</c><00:09:09.760><c> a</c><00:09:09.920><c> cluster</c><00:09:10.279><c> of</c>

00:09:10.430 --> 00:09:10.440 align:start position:0%
from the bubble map here a cluster of
 

00:09:10.440 --> 00:09:12.829 align:start position:0%
from the bubble map here a cluster of
wallets<00:09:10.880><c> linked</c><00:09:11.160><c> to</c><00:09:11.320><c> each</c><00:09:11.480><c> other</c><00:09:12.200><c> holds</c><00:09:12.640><c> more</c>

00:09:12.829 --> 00:09:12.839 align:start position:0%
wallets linked to each other holds more
 

00:09:12.839 --> 00:09:14.670 align:start position:0%
wallets linked to each other holds more
than<00:09:13.000><c> 20%</c><00:09:13.600><c> of</c><00:09:13.720><c> the</c><00:09:13.880><c> whole</c><00:09:14.079><c> supply</c><00:09:14.440><c> of</c><00:09:14.519><c> the</c>

00:09:14.670 --> 00:09:14.680 align:start position:0%
than 20% of the whole supply of the
 

00:09:14.680 --> 00:09:17.509 align:start position:0%
than 20% of the whole supply of the
token<00:09:15.440><c> and</c><00:09:15.640><c> it</c><00:09:15.839><c> has</c><00:09:16.240><c> since</c><00:09:16.640><c> the</c><00:09:16.839><c> very</c>

00:09:17.509 --> 00:09:17.519 align:start position:0%
token and it has since the very
 

00:09:17.519 --> 00:09:20.430 align:start position:0%
token and it has since the very
beginning<00:09:18.519><c> clearly</c><00:09:19.519><c> here</c><00:09:19.720><c> the</c><00:09:19.839><c> intent</c><00:09:20.160><c> was</c><00:09:20.320><c> to</c>

00:09:20.430 --> 00:09:20.440 align:start position:0%
beginning clearly here the intent was to
 

00:09:20.440 --> 00:09:22.230 align:start position:0%
beginning clearly here the intent was to
give<00:09:20.600><c> the</c><00:09:20.720><c> impression</c><00:09:21.200><c> this</c><00:09:21.320><c> meme</c><00:09:21.640><c> was</c><00:09:21.800><c> a</c><00:09:21.920><c> fair</c>

00:09:22.230 --> 00:09:22.240 align:start position:0%
give the impression this meme was a fair
 

00:09:22.240 --> 00:09:24.230 align:start position:0%
give the impression this meme was a fair
launch<00:09:22.640><c> meaning</c><00:09:22.959><c> the</c><00:09:23.120><c> supply</c><00:09:23.519><c> was</c><00:09:23.720><c> widespread</c>

00:09:24.230 --> 00:09:24.240 align:start position:0%
launch meaning the supply was widespread
 

00:09:24.240 --> 00:09:26.550 align:start position:0%
launch meaning the supply was widespread
through<00:09:24.519><c> thousands</c><00:09:24.920><c> of</c><00:09:25.040><c> small</c><00:09:25.320><c> holders</c><00:09:26.320><c> while</c>

00:09:26.550 --> 00:09:26.560 align:start position:0%
through thousands of small holders while
 

00:09:26.560 --> 00:09:28.269 align:start position:0%
through thousands of small holders while
the<00:09:26.720><c> reality</c><00:09:27.120><c> was</c><00:09:27.320><c> very</c><00:09:27.519><c> different</c><00:09:27.959><c> and</c><00:09:28.120><c> all</c>

00:09:28.269 --> 00:09:28.279 align:start position:0%
the reality was very different and all
 

00:09:28.279 --> 00:09:29.949 align:start position:0%
the reality was very different and all
the<00:09:28.440><c> top</c><00:09:28.640><c> wallets</c><00:09:29.000><c> are</c><00:09:29.160><c> actually</c><00:09:29.560><c> controlled</c>

00:09:29.949 --> 00:09:29.959 align:start position:0%
the top wallets are actually controlled
 

00:09:29.959 --> 00:09:33.350 align:start position:0%
the top wallets are actually controlled
by<00:09:30.079><c> the</c><00:09:30.240><c> same</c><00:09:30.760><c> group</c><00:09:31.079><c> or</c><00:09:31.640><c> person</c><00:09:32.640><c> however</c>

00:09:33.350 --> 00:09:33.360 align:start position:0%
by the same group or person however
 

00:09:33.360 --> 00:09:34.949 align:start position:0%
by the same group or person however
being<00:09:33.720><c> this</c><00:09:33.839><c> is</c><00:09:33.920><c> a</c><00:09:34.079><c> more</c><00:09:34.320><c> sophisticated</c>

00:09:34.949 --> 00:09:34.959 align:start position:0%
being this is a more sophisticated
 

00:09:34.959 --> 00:09:37.430 align:start position:0%
being this is a more sophisticated
scheme<00:09:35.320><c> the</c><00:09:35.440><c> cluster</c><00:09:35.920><c> did</c><00:09:36.079><c> not</c><00:09:36.320><c> dump</c><00:09:36.680><c> all</c><00:09:37.160><c> at</c>

00:09:37.430 --> 00:09:37.440 align:start position:0%
scheme the cluster did not dump all at
 

00:09:37.440 --> 00:09:39.910 align:start position:0%
scheme the cluster did not dump all at
once<00:09:37.959><c> like</c><00:09:38.160><c> in</c><00:09:38.279><c> the</c><00:09:38.480><c> case</c><00:09:38.680><c> of</c><00:09:38.880><c> Cuban</c><00:09:39.720><c> but</c>

00:09:39.910 --> 00:09:39.920 align:start position:0%
once like in the case of Cuban but
 

00:09:39.920 --> 00:09:42.069 align:start position:0%
once like in the case of Cuban but
likely<00:09:40.320><c> was</c><00:09:40.440><c> used</c><00:09:40.680><c> to</c><00:09:40.839><c> constrain</c><00:09:41.240><c> the</c><00:09:41.399><c> supply</c>

00:09:42.069 --> 00:09:42.079 align:start position:0%
likely was used to constrain the supply
 

00:09:42.079 --> 00:09:45.350 align:start position:0%
likely was used to constrain the supply
while<00:09:42.360><c> other</c><00:09:42.959><c> quote</c><00:09:43.240><c> unquote</c><00:09:43.720><c> fresh</c><00:09:44.360><c> wallets</c>

00:09:45.350 --> 00:09:45.360 align:start position:0%
while other quote unquote fresh wallets
 

00:09:45.360 --> 00:09:47.190 align:start position:0%
while other quote unquote fresh wallets
they<00:09:45.560><c> cannot</c><00:09:45.880><c> be</c><00:09:46.040><c> linked</c><00:09:46.399><c> to</c><00:09:46.560><c> a</c><00:09:46.720><c> cluster</c>

00:09:47.190 --> 00:09:47.200 align:start position:0%
they cannot be linked to a cluster
 

00:09:47.200 --> 00:09:50.430 align:start position:0%
they cannot be linked to a cluster
because<00:09:47.480><c> no</c><00:09:47.880><c> transactions</c><00:09:48.880><c> exist</c><00:09:49.399><c> among</c><00:09:49.760><c> them</c>

00:09:50.430 --> 00:09:50.440 align:start position:0%
because no transactions exist among them
 

00:09:50.440 --> 00:09:53.269 align:start position:0%
because no transactions exist among them
were<00:09:50.760><c> created</c><00:09:51.160><c> to</c><00:09:51.600><c> snipe</c><00:09:52.480><c> the</c><00:09:52.680><c> supplyer</c><00:09:53.120><c> at</c>

00:09:53.269 --> 00:09:53.279 align:start position:0%
were created to snipe the supplyer at
 

00:09:53.279 --> 00:09:55.550 align:start position:0%
were created to snipe the supplyer at
the<00:09:53.440><c> very</c><00:09:53.640><c> beginning</c><00:09:54.480><c> similar</c><00:09:54.920><c> to</c><00:09:55.120><c> what</c><00:09:55.320><c> was</c>

00:09:55.550 --> 00:09:55.560 align:start position:0%
the very beginning similar to what was
 

00:09:55.560 --> 00:09:57.829 align:start position:0%
the very beginning similar to what was
exposed<00:09:56.040><c> by</c><00:09:56.160><c> the</c><00:09:56.279><c> Libra</c><00:09:56.640><c> Scandal</c><00:09:57.360><c> to</c><00:09:57.600><c> then</c>

00:09:57.829 --> 00:09:57.839 align:start position:0%
exposed by the Libra Scandal to then
 

00:09:57.839 --> 00:09:59.750 align:start position:0%
exposed by the Libra Scandal to then
dump<00:09:58.360><c> this</c><00:09:58.720><c> gradually</c><00:09:59.160><c> when</c><00:09:59.399><c> the</c><00:09:59.519><c> social</c>

00:09:59.750 --> 00:09:59.760 align:start position:0%
dump this gradually when the social
 

00:09:59.760 --> 00:10:02.590 align:start position:0%
dump this gradually when the social
media<00:10:00.120><c> hype</c><00:10:00.480><c> started</c><00:10:00.839><c> to</c><00:10:01.000><c> fade</c><00:10:01.839><c> the</c><00:10:02.000><c> ultimate</c>

00:10:02.590 --> 00:10:02.600 align:start position:0%
media hype started to fade the ultimate
 

00:10:02.600 --> 00:10:04.790 align:start position:0%
media hype started to fade the ultimate
result<00:10:03.000><c> isn't</c><00:10:03.320><c> that</c><00:10:03.519><c> much</c><00:10:03.839><c> different</c><00:10:04.360><c> from</c>

00:10:04.790 --> 00:10:04.800 align:start position:0%
result isn't that much different from
 

00:10:04.800 --> 00:10:07.630 align:start position:0%
result isn't that much different from
the<00:10:04.959><c> Cuban</c><00:10:05.399><c> example</c><00:10:06.040><c> as</c><00:10:06.200><c> you</c><00:10:06.360><c> can</c>

00:10:07.630 --> 00:10:07.640 align:start position:0%
the Cuban example as you can
 

00:10:07.640 --> 00:10:10.509 align:start position:0%
the Cuban example as you can
see<00:10:08.640><c> Supply</c><00:10:09.040><c> concentration</c><00:10:09.720><c> isn't</c><00:10:10.079><c> per</c><00:10:10.240><c> se</c><00:10:10.399><c> a</c>

00:10:10.509 --> 00:10:10.519 align:start position:0%
see Supply concentration isn't per se a
 

00:10:10.519 --> 00:10:12.269 align:start position:0%
see Supply concentration isn't per se a
red<00:10:10.720><c> flag</c><00:10:11.079><c> when</c><00:10:11.240><c> there</c><00:10:11.360><c> is</c><00:10:11.440><c> a</c><00:10:11.600><c> team</c><00:10:11.839><c> working</c><00:10:12.160><c> on</c>

00:10:12.269 --> 00:10:12.279 align:start position:0%
red flag when there is a team working on
 

00:10:12.279 --> 00:10:14.790 align:start position:0%
red flag when there is a team working on
the<00:10:12.440><c> project</c><00:10:12.760><c> behind</c><00:10:13.000><c> the</c><00:10:13.120><c> token</c><00:10:14.000><c> as</c><00:10:14.399><c> usually</c>

00:10:14.790 --> 00:10:14.800 align:start position:0%
the project behind the token as usually
 

00:10:14.800 --> 00:10:17.030 align:start position:0%
the project behind the token as usually
the<00:10:14.959><c> supply</c><00:10:15.480><c> split</c><00:10:15.920><c> is</c><00:10:16.160><c> publicly</c><00:10:16.560><c> disclosed</c>

00:10:17.030 --> 00:10:17.040 align:start position:0%
the supply split is publicly disclosed
 

00:10:17.040 --> 00:10:19.750 align:start position:0%
the supply split is publicly disclosed
in<00:10:17.120><c> to</c><00:10:17.399><c> economics</c><00:10:18.399><c> however</c><00:10:18.880><c> none</c><00:10:19.120><c> of</c><00:10:19.279><c> this</c><00:10:19.480><c> was</c>

00:10:19.750 --> 00:10:19.760 align:start position:0%
in to economics however none of this was
 

00:10:19.760 --> 00:10:23.509 align:start position:0%
in to economics however none of this was
available<00:10:20.440><c> in</c><00:10:20.720><c> the</c><00:10:20.880><c> routine</c><00:10:21.440><c> example</c><00:10:22.040><c> we</c><00:10:22.240><c> just</c>

00:10:23.509 --> 00:10:23.519 align:start position:0%
available in the routine example we just
 

00:10:23.519 --> 00:10:25.949 align:start position:0%
available in the routine example we just
discussed<00:10:24.519><c> as</c><00:10:24.640><c> you</c><00:10:24.760><c> already</c><00:10:25.079><c> know</c><00:10:25.720><c> I'm</c>

00:10:25.949 --> 00:10:25.959 align:start position:0%
discussed as you already know I'm
 

00:10:25.959 --> 00:10:28.670 align:start position:0%
discussed as you already know I'm
bullish<00:10:26.279><c> on</c><00:10:26.480><c> crypto</c><00:10:26.800><c> in</c><00:10:26.880><c> the</c><00:10:27.000><c> long</c><00:10:27.519><c> term</c><00:10:28.519><c> and</c>

00:10:28.670 --> 00:10:28.680 align:start position:0%
bullish on crypto in the long term and
 

00:10:28.680 --> 00:10:31.509 align:start position:0%
bullish on crypto in the long term and
as<00:10:28.880><c> a</c><00:10:29.040><c> class</c><00:10:29.519><c> that</c><00:10:30.120><c> similar</c><00:10:30.480><c> to</c><00:10:30.640><c> the</c><00:10:30.800><c> internet</c>

00:10:31.509 --> 00:10:31.519 align:start position:0%
as a class that similar to the internet
 

00:10:31.519 --> 00:10:33.310 align:start position:0%
as a class that similar to the internet
we<00:10:31.760><c> see</c><00:10:31.959><c> a</c><00:10:32.120><c> dramatic</c><00:10:32.519><c> increase</c><00:10:32.880><c> in</c><00:10:33.000><c> value</c><00:10:33.240><c> in</c>

00:10:33.310 --> 00:10:33.320 align:start position:0%
we see a dramatic increase in value in
 

00:10:33.320 --> 00:10:35.470 align:start position:0%
we see a dramatic increase in value in
the<00:10:33.440><c> future</c><00:10:33.959><c> and</c><00:10:34.160><c> as</c><00:10:34.360><c> such</c><00:10:34.760><c> I</c><00:10:34.880><c> have</c><00:10:35.079><c> already</c>

00:10:35.470 --> 00:10:35.480 align:start position:0%
the future and as such I have already
 

00:10:35.480 --> 00:10:38.710 align:start position:0%
the future and as such I have already
written<00:10:35.959><c> several</c><00:10:36.440><c> articles</c><00:10:37.320><c> trying</c><00:10:37.600><c> to</c><00:10:37.920><c> help</c>

00:10:38.710 --> 00:10:38.720 align:start position:0%
written several articles trying to help
 

00:10:38.720 --> 00:10:40.870 align:start position:0%
written several articles trying to help
spot<00:10:39.240><c> the</c><00:10:39.399><c> opportunities</c><00:10:40.079><c> mindful</c><00:10:40.560><c> of</c><00:10:40.680><c> the</c>

00:10:40.870 --> 00:10:40.880 align:start position:0%
spot the opportunities mindful of the
 

00:10:40.880 --> 00:10:42.949 align:start position:0%
spot the opportunities mindful of the
history<00:10:41.279><c> of</c><00:10:41.440><c> the</c><00:10:41.600><c> internet</c>

00:10:42.949 --> 00:10:42.959 align:start position:0%
history of the internet
 

00:10:42.959 --> 00:10:44.550 align:start position:0%
history of the internet
bubble

00:10:44.550 --> 00:10:44.560 align:start position:0%
bubble
 

00:10:44.560 --> 00:10:48.069 align:start position:0%
bubble
like<00:10:45.560><c> the</c><00:10:45.839><c> US</c><00:10:46.279><c> crypto</c><00:10:46.639><c> Reserve</c><00:10:47.040><c> plan</c><00:10:47.760><c> what</c><00:10:47.959><c> it</c>

00:10:48.069 --> 00:10:48.079 align:start position:0%
like the US crypto Reserve plan what it
 

00:10:48.079 --> 00:10:51.949 align:start position:0%
like the US crypto Reserve plan what it
means<00:10:48.399><c> for</c><00:10:48.680><c> investors</c><00:10:49.560><c> everywhere</c><00:10:50.760><c> or</c><00:10:51.760><c> a</c>

00:10:51.949 --> 00:10:51.959 align:start position:0%
means for investors everywhere or a
 

00:10:51.959 --> 00:10:54.389 align:start position:0%
means for investors everywhere or a
crypto<00:10:52.399><c> update</c><00:10:52.800><c> to</c><00:10:53.000><c> bang</c><00:10:53.320><c> GRS</c><00:10:54.079><c> the</c>

00:10:54.389 --> 00:10:54.399 align:start position:0%
crypto update to bang GRS the
 

00:10:54.399 --> 00:10:57.790 align:start position:0%
crypto update to bang GRS the
intelligent<00:10:54.959><c> investor</c><00:10:55.920><c> or</c><00:10:56.920><c> is</c><00:10:57.160><c> this</c><00:10:57.519><c> black</c>

00:10:57.790 --> 00:10:57.800 align:start position:0%
intelligent investor or is this black
 

00:10:57.800 --> 00:11:00.550 align:start position:0%
intelligent investor or is this black
rocks<00:10:58.200><c> master</c><00:10:58.560><c> plan</c><00:10:58.760><c> for</c><00:10:58.920><c> Bitcoin</c>

00:11:00.550 --> 00:11:00.560 align:start position:0%
rocks master plan for Bitcoin
 

00:11:00.560 --> 00:11:03.550 align:start position:0%
rocks master plan for Bitcoin
or<00:11:01.240><c> why</c><00:11:01.399><c> a</c><00:11:01.560><c> Bitcoin</c><00:11:02.000><c> ETF</c><00:11:02.560><c> will</c><00:11:02.880><c> remove</c><00:11:03.360><c> the</c>

00:11:03.550 --> 00:11:03.560 align:start position:0%
or why a Bitcoin ETF will remove the
 

00:11:03.560 --> 00:11:05.509 align:start position:0%
or why a Bitcoin ETF will remove the
barrier<00:11:04.120><c> between</c><00:11:04.600><c> central</c><00:11:04.959><c> bank's</c><00:11:05.279><c> money</c>

00:11:05.509 --> 00:11:05.519 align:start position:0%
barrier between central bank's money
 

00:11:05.519 --> 00:11:06.790 align:start position:0%
barrier between central bank's money
Printing<00:11:05.880><c> and</c>

00:11:06.790 --> 00:11:06.800 align:start position:0%
Printing and
 

00:11:06.800 --> 00:11:10.590 align:start position:0%
Printing and
crypto<00:11:07.800><c> and</c><00:11:08.120><c> lastly</c><00:11:09.120><c> value</c><00:11:09.560><c> investing</c><00:11:10.120><c> on</c>

00:11:10.590 --> 00:11:10.600 align:start position:0%
crypto and lastly value investing on
 

00:11:10.600 --> 00:11:13.710 align:start position:0%
crypto and lastly value investing on
crypto<00:11:11.000><c> blockchain</c><00:11:11.720><c> projects</c><00:11:12.320><c> I</c><00:11:12.519><c> wrote</c><00:11:13.279><c> in</c>

00:11:13.710 --> 00:11:13.720 align:start position:0%
crypto blockchain projects I wrote in
 

00:11:13.720 --> 00:11:15.350 align:start position:0%
crypto blockchain projects I wrote in
December

00:11:15.350 --> 00:11:15.360 align:start position:0%
December
 

00:11:15.360 --> 00:11:18.310 align:start position:0%
December
2023<00:11:16.360><c> I</c><00:11:16.560><c> hope</c><00:11:16.839><c> that</c><00:11:17.040><c> with</c><00:11:17.200><c> today's</c><00:11:17.639><c> article</c><00:11:18.200><c> I</c>

00:11:18.310 --> 00:11:18.320 align:start position:0%
2023 I hope that with today's article I
 

00:11:18.320 --> 00:11:20.710 align:start position:0%
2023 I hope that with today's article I
will<00:11:18.560><c> also</c><00:11:18.880><c> help</c><00:11:19.120><c> you</c><00:11:19.360><c> my</c><00:11:19.560><c> dear</c><00:11:19.760><c> reader</c><00:11:20.440><c> to</c>

00:11:20.710 --> 00:11:20.720 align:start position:0%
will also help you my dear reader to
 

00:11:20.720 --> 00:11:22.949 align:start position:0%
will also help you my dear reader to
avoid<00:11:21.040><c> the</c><00:11:21.200><c> traps</c><00:11:21.680><c> and</c><00:11:21.839><c> losing</c><00:11:22.240><c> money</c><00:11:22.800><c> that</c>

00:11:22.949 --> 00:11:22.959 align:start position:0%
avoid the traps and losing money that
 

00:11:22.959 --> 00:11:25.230 align:start position:0%
avoid the traps and losing money that
can<00:11:23.120><c> be</c><00:11:23.360><c> instead</c><00:11:23.720><c> deployed</c><00:11:24.279><c> more</c><00:11:24.600><c> profitably</c>

00:11:25.230 --> 00:11:25.240 align:start position:0%
can be instead deployed more profitably
 

00:11:25.240 --> 00:11:28.240 align:start position:0%
can be instead deployed more profitably
healthware

