'use client';

import React from 'react';

// For handling field data in sections like 'Key Points', 'Recommendations', etc.
type FieldData = [string, unknown][];

interface CategoryCopyButtonProps {
  category: string;
  fields: FieldData;
  className?: string;
}

export function CategoryCopyButton({ category, fields, className = "text-xs px-2 py-1 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded flex items-center" }: CategoryCopyButtonProps) {
  // Format field names nicely
  const formatFieldName = (key: string) => {
    return key
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Helper to generate copyable text for a field
  const getCopyableText = (key: string, value: unknown): string => {
    if (Array.isArray(value)) {
      return value.map(item => 
        typeof item === 'object' ? JSON.stringify(item, null, 2) : `• ${String(item)}`
      ).join('\n');
    } else if (typeof value === 'object' && value !== null) {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  const handleCopy = () => {
    const text = fields.map(([key, value]) => 
      `${formatFieldName(key)}:\n${getCopyableText(key, value)}`
    ).join('\n\n');
    navigator.clipboard.writeText(text);
    
    // Visual feedback
    const notification = document.createElement('div');
    notification.textContent = 'Copied to clipboard!';
    notification.style.cssText = 'position:fixed;bottom:20px;left:50%;transform:translateX(-50%);background:#4CAF50;color:white;padding:8px 16px;border-radius:4px;z-index:1000;';
    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 2000);
  };

  return (
    <button 
      onClick={handleCopy}
      className={className}
      title={`Copy all ${category.toLowerCase()}`}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
      </svg>
      Copy
    </button>
  );
}

interface FieldCopyButtonProps {
  fieldValue: unknown;
  className?: string;
}

export function FieldCopyButton({ fieldValue, className = "opacity-0 group-hover:opacity-100 hover:opacity-100 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300" }: FieldCopyButtonProps) {
  // Helper to generate copyable text for a field
  const getCopyableText = (value: unknown): string => {
    if (Array.isArray(value)) {
      return value.map(item => 
        typeof item === 'object' ? JSON.stringify(item, null, 2) : `• ${String(item)}`
      ).join('\n');
    } else if (typeof value === 'object' && value !== null) {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  const handleCopy = (e: React.MouseEvent) => {
    navigator.clipboard.writeText(getCopyableText(fieldValue));
    // Feedback for copy
    const btn = e.currentTarget as HTMLButtonElement;
    const originalHTML = btn.innerHTML;
    btn.innerHTML = '✓';
    setTimeout(() => { btn.innerHTML = originalHTML; }, 1000);
  };

  return (
    <button
      onClick={handleCopy}
      className={className}
      title="Copy to clipboard"
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
      </svg>
    </button>
  );
}

interface SummaryCopyButtonProps {
  text: string;
  className?: string;
}

export function SummaryCopyButton({ text, className = "text-xs px-2 py-1 bg-primary/20 hover:bg-primary/30 text-primary rounded flex items-center" }: SummaryCopyButtonProps) {
  const handleCopy = () => {
    navigator.clipboard.writeText(text);
    
    // Visual feedback
    const notification = document.createElement('div');
    notification.textContent = 'Summary copied!';
    notification.style.cssText = 'position:fixed;bottom:20px;left:50%;transform:translateX(-50%);background:#4CAF50;color:white;padding:8px 16px;border-radius:4px;z-index:1000;';
    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 2000);
  };

  return (
    <button
      onClick={handleCopy}
      className={className}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
      </svg>
      Copy
    </button>
  );
}

interface TranscriptCopyButtonProps {
  transcript: string;
  className?: string;
}

export function TranscriptCopyButton({ transcript, className = "text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 rounded flex items-center" }: TranscriptCopyButtonProps) {
  const handleCopy = () => {
    navigator.clipboard.writeText(transcript);
    
    // Visual feedback
    const notification = document.createElement('div');
    notification.textContent = 'Transcript copied!';
    notification.style.cssText = 'position:fixed;bottom:20px;left:50%;transform:translateX(-50%);background:#4CAF50;color:white;padding:8px 16px;border-radius:4px;z-index:1000;';
    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 2000);
  };

  return (
    <button
      onClick={handleCopy}
      className={className}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
      </svg>
      Copy Full Transcript
    </button>
  );
}
