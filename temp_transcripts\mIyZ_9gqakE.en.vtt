WEBVTT
Kind: captions
Language: en

00:00:00.240 --> 00:00:02.090 align:start position:0%
 
hey<00:00:00.659><c> everybody</c><00:00:00.840><c> <PERSON></c><00:00:01.260><c> here</c><00:00:01.620><c> back</c><00:00:01.920><c> with</c>

00:00:02.090 --> 00:00:02.100 align:start position:0%
hey everybody Logan here back with
 

00:00:02.100 --> 00:00:03.710 align:start position:0%
hey everybody Logan here back with
another<00:00:02.280><c> episode</c><00:00:02.639><c> of</c><00:00:03.179><c> Bottoms</c><00:00:03.600><c> Up</c>

00:00:03.710 --> 00:00:03.720 align:start position:0%
another episode of Bottoms Up
 

00:00:03.720 --> 00:00:05.809 align:start position:0%
another episode of Bottoms Up
development<00:00:04.080><c> with</c><00:00:04.259><c> lava</c><00:00:04.680><c> index</c><00:00:04.980><c> today</c><00:00:05.580><c> we're</c>

00:00:05.809 --> 00:00:05.819 align:start position:0%
development with lava index today we're
 

00:00:05.819 --> 00:00:07.610 align:start position:0%
development with lava index today we're
going<00:00:05.940><c> to</c><00:00:06.060><c> be</c><00:00:06.120><c> covering</c><00:00:06.620><c> customizing</c>

00:00:07.610 --> 00:00:07.620 align:start position:0%
going to be covering customizing
 

00:00:07.620 --> 00:00:09.650 align:start position:0%
going to be covering customizing
retrievers<00:00:08.220><c> and</c><00:00:08.400><c> node</c><00:00:08.639><c> plus</c><00:00:08.820><c> processors</c><00:00:09.360><c> in</c>

00:00:09.650 --> 00:00:09.660 align:start position:0%
retrievers and node plus processors in
 

00:00:09.660 --> 00:00:12.290 align:start position:0%
retrievers and node plus processors in
llama<00:00:09.960><c> index</c><00:00:10.380><c> but</c><00:00:11.280><c> before</c><00:00:11.519><c> we</c><00:00:11.760><c> dive</c><00:00:12.000><c> into</c><00:00:12.120><c> that</c>

00:00:12.290 --> 00:00:12.300 align:start position:0%
llama index but before we dive into that
 

00:00:12.300 --> 00:00:15.169 align:start position:0%
llama index but before we dive into that
let's<00:00:12.599><c> quickly</c><00:00:12.960><c> review</c><00:00:13.280><c> what</c><00:00:14.280><c> a</c><00:00:14.580><c> query</c><00:00:14.940><c> engine</c>

00:00:15.169 --> 00:00:15.179 align:start position:0%
let's quickly review what a query engine
 

00:00:15.179 --> 00:00:17.150 align:start position:0%
let's quickly review what a query engine
looks<00:00:15.599><c> like</c><00:00:15.719><c> in</c><00:00:15.900><c> llama</c><00:00:16.260><c> index</c>

00:00:17.150 --> 00:00:17.160 align:start position:0%
looks like in llama index
 

00:00:17.160 --> 00:00:18.470 align:start position:0%
looks like in llama index
so<00:00:17.340><c> when</c><00:00:17.580><c> you</c><00:00:17.699><c> create</c><00:00:17.820><c> a</c><00:00:18.000><c> query</c><00:00:18.240><c> engine</c>

00:00:18.470 --> 00:00:18.480 align:start position:0%
so when you create a query engine
 

00:00:18.480 --> 00:00:20.330 align:start position:0%
so when you create a query engine
typically<00:00:18.960><c> you're</c><00:00:19.199><c> creating</c><00:00:19.560><c> it</c><00:00:19.740><c> on</c><00:00:20.100><c> top</c><00:00:20.220><c> of</c>

00:00:20.330 --> 00:00:20.340 align:start position:0%
typically you're creating it on top of
 

00:00:20.340 --> 00:00:22.429 align:start position:0%
typically you're creating it on top of
your<00:00:20.520><c> index</c><00:00:20.820><c> and</c><00:00:21.660><c> you'll</c><00:00:21.840><c> send</c><00:00:22.080><c> it</c><00:00:22.199><c> like</c><00:00:22.320><c> a</c>

00:00:22.429 --> 00:00:22.439 align:start position:0%
your index and you'll send it like a
 

00:00:22.439 --> 00:00:23.689 align:start position:0%
your index and you'll send it like a
query<00:00:22.680><c> string</c><00:00:22.980><c> and</c><00:00:23.100><c> you'll</c><00:00:23.279><c> get</c><00:00:23.400><c> back</c><00:00:23.520><c> a</c>

00:00:23.689 --> 00:00:23.699 align:start position:0%
query string and you'll get back a
 

00:00:23.699 --> 00:00:25.730 align:start position:0%
query string and you'll get back a
response<00:00:24.180><c> but</c><00:00:25.019><c> under</c><00:00:25.140><c> the</c><00:00:25.380><c> hood</c><00:00:25.500><c> there's</c>

00:00:25.730 --> 00:00:25.740 align:start position:0%
response but under the hood there's
 

00:00:25.740 --> 00:00:27.290 align:start position:0%
response but under the hood there's
several<00:00:26.039><c> steps</c><00:00:26.340><c> that</c><00:00:26.460><c> are</c><00:00:26.580><c> taking</c><00:00:26.699><c> place</c><00:00:27.000><c> to</c>

00:00:27.290 --> 00:00:27.300 align:start position:0%
several steps that are taking place to
 

00:00:27.300 --> 00:00:29.630 align:start position:0%
several steps that are taking place to
make<00:00:27.539><c> this</c><00:00:27.779><c> happen</c><00:00:28.439><c> so</c><00:00:28.980><c> from</c><00:00:29.220><c> your</c><00:00:29.400><c> query</c>

00:00:29.630 --> 00:00:29.640 align:start position:0%
make this happen so from your query
 

00:00:29.640 --> 00:00:31.730 align:start position:0%
make this happen so from your query
string<00:00:30.060><c> alarm</c><00:00:30.359><c> index</c><00:00:30.779><c> retrieves</c><00:00:31.320><c> relevant</c>

00:00:31.730 --> 00:00:31.740 align:start position:0%
string alarm index retrieves relevant
 

00:00:31.740 --> 00:00:34.610 align:start position:0%
string alarm index retrieves relevant
nodes<00:00:32.480><c> optionally</c><00:00:33.480><c> post-processes</c><00:00:34.380><c> them</c>

00:00:34.610 --> 00:00:34.620 align:start position:0%
nodes optionally post-processes them
 

00:00:34.620 --> 00:00:36.650 align:start position:0%
nodes optionally post-processes them
with<00:00:35.280><c> some</c><00:00:35.520><c> kind</c><00:00:35.700><c> of</c><00:00:35.760><c> transformation</c><00:00:36.120><c> if</c>

00:00:36.650 --> 00:00:36.660 align:start position:0%
with some kind of transformation if
 

00:00:36.660 --> 00:00:39.049 align:start position:0%
with some kind of transformation if
you've<00:00:37.020><c> passed</c><00:00:37.980><c> in</c><00:00:38.100><c> a</c><00:00:38.219><c> notebook</c><00:00:38.579><c> processor</c>

00:00:39.049 --> 00:00:39.059 align:start position:0%
you've passed in a notebook processor
 

00:00:39.059 --> 00:00:41.389 align:start position:0%
you've passed in a notebook processor
and<00:00:39.780><c> then</c><00:00:39.840><c> we</c><00:00:40.020><c> take</c><00:00:40.140><c> those</c><00:00:40.379><c> list</c><00:00:40.620><c> of</c><00:00:40.860><c> nodes</c><00:00:41.100><c> and</c>

00:00:41.389 --> 00:00:41.399 align:start position:0%
and then we take those list of nodes and
 

00:00:41.399 --> 00:00:43.549 align:start position:0%
and then we take those list of nodes and
send<00:00:41.520><c> it</c><00:00:41.640><c> to</c><00:00:41.760><c> a</c><00:00:41.879><c> response</c><00:00:42.300><c> synthesizer</c><00:00:42.960><c> which</c>

00:00:43.549 --> 00:00:43.559 align:start position:0%
send it to a response synthesizer which
 

00:00:43.559 --> 00:00:45.590 align:start position:0%
send it to a response synthesizer which
is<00:00:43.739><c> basically</c><00:00:44.100><c> sending</c><00:00:45.000><c> those</c><00:00:45.059><c> nodes</c><00:00:45.300><c> to</c><00:00:45.540><c> an</c>

00:00:45.590 --> 00:00:45.600 align:start position:0%
is basically sending those nodes to an
 

00:00:45.600 --> 00:00:47.869 align:start position:0%
is basically sending those nodes to an
llm<00:00:46.020><c> like</c><00:00:46.200><c> openai</c><00:00:46.559><c> to</c><00:00:46.920><c> synthesize</c><00:00:47.399><c> that</c>

00:00:47.869 --> 00:00:47.879 align:start position:0%
llm like openai to synthesize that
 

00:00:47.879 --> 00:00:49.850 align:start position:0%
llm like openai to synthesize that
natural<00:00:48.000><c> language</c><00:00:48.360><c> response</c><00:00:49.020><c> and</c><00:00:49.800><c> then</c>

00:00:49.850 --> 00:00:49.860 align:start position:0%
natural language response and then
 

00:00:49.860 --> 00:00:51.830 align:start position:0%
natural language response and then
finally<00:00:50.160><c> you</c><00:00:50.399><c> get</c><00:00:50.520><c> your</c><00:00:50.760><c> response</c><00:00:51.180><c> object</c><00:00:51.420><c> at</c>

00:00:51.830 --> 00:00:51.840 align:start position:0%
finally you get your response object at
 

00:00:51.840 --> 00:00:52.790 align:start position:0%
finally you get your response object at
the<00:00:51.899><c> end</c>

00:00:52.790 --> 00:00:52.800 align:start position:0%
the end
 

00:00:52.800 --> 00:00:54.049 align:start position:0%
the end
so<00:00:53.219><c> in</c><00:00:53.340><c> this</c><00:00:53.460><c> video</c><00:00:53.579><c> we're</c><00:00:53.820><c> going</c><00:00:53.879><c> to</c><00:00:54.000><c> be</c>

00:00:54.049 --> 00:00:54.059 align:start position:0%
so in this video we're going to be
 

00:00:54.059 --> 00:00:56.029 align:start position:0%
so in this video we're going to be
covering<00:00:54.420><c> customizing</c><00:00:55.320><c> specifically</c><00:00:55.800><c> the</c>

00:00:56.029 --> 00:00:56.039 align:start position:0%
covering customizing specifically the
 

00:00:56.039 --> 00:00:58.549 align:start position:0%
covering customizing specifically the
retrieval<00:00:56.579><c> and</c><00:00:57.000><c> the</c><00:00:57.420><c> post-processing</c><00:00:58.140><c> steps</c>

00:00:58.549 --> 00:00:58.559 align:start position:0%
retrieval and the post-processing steps
 

00:00:58.559 --> 00:01:01.010 align:start position:0%
retrieval and the post-processing steps
in<00:00:59.100><c> a</c><00:00:59.219><c> query</c><00:00:59.460><c> engine</c>

00:01:01.010 --> 00:01:01.020 align:start position:0%
in a query engine
 

00:01:01.020 --> 00:01:03.049 align:start position:0%
in a query engine
so<00:01:01.440><c> looking</c><00:01:01.680><c> at</c><00:01:01.860><c> retrievers</c><00:01:02.340><c> and</c><00:01:02.460><c> lab</c><00:01:02.640><c> index</c>

00:01:03.049 --> 00:01:03.059 align:start position:0%
so looking at retrievers and lab index
 

00:01:03.059 --> 00:01:05.329 align:start position:0%
so looking at retrievers and lab index
every<00:01:03.780><c> index</c><00:01:04.140><c> has</c><00:01:04.379><c> its</c><00:01:04.619><c> own</c><00:01:04.680><c> retriever</c><00:01:05.100><c> that</c>

00:01:05.329 --> 00:01:05.339 align:start position:0%
every index has its own retriever that
 

00:01:05.339 --> 00:01:07.969 align:start position:0%
every index has its own retriever that
you<00:01:05.460><c> can</c><00:01:05.580><c> access</c><00:01:05.700><c> with</c><00:01:06.119><c> as</c><00:01:06.360><c> retriever</c><00:01:06.960><c> on</c><00:01:07.920><c> top</c>

00:01:07.969 --> 00:01:07.979 align:start position:0%
you can access with as retriever on top
 

00:01:07.979 --> 00:01:09.530 align:start position:0%
you can access with as retriever on top
of<00:01:08.100><c> that</c><00:01:08.220><c> we</c><00:01:08.400><c> have</c><00:01:08.520><c> a</c><00:01:08.700><c> bunch</c><00:01:08.820><c> of</c><00:01:08.939><c> specialized</c>

00:01:09.530 --> 00:01:09.540 align:start position:0%
of that we have a bunch of specialized
 

00:01:09.540 --> 00:01:12.770 align:start position:0%
of that we have a bunch of specialized
retrievers<00:01:10.140><c> for</c><00:01:11.100><c> various</c><00:01:11.460><c> purposes</c><00:01:11.880><c> a</c>

00:01:12.770 --> 00:01:12.780 align:start position:0%
retrievers for various purposes a
 

00:01:12.780 --> 00:01:14.390 align:start position:0%
retrievers for various purposes a
recursive<00:01:13.260><c> retriever</c><00:01:13.680><c> Auto</c><00:01:13.979><c> merging</c>

00:01:14.390 --> 00:01:14.400 align:start position:0%
recursive retriever Auto merging
 

00:01:14.400 --> 00:01:16.609 align:start position:0%
recursive retriever Auto merging
Retriever<00:01:14.820><c> and</c><00:01:15.060><c> more</c><00:01:15.720><c> and</c><00:01:16.200><c> each</c><00:01:16.380><c> of</c><00:01:16.500><c> these</c>

00:01:16.609 --> 00:01:16.619 align:start position:0%
Retriever and more and each of these
 

00:01:16.619 --> 00:01:18.530 align:start position:0%
Retriever and more and each of these
retrievers<00:01:17.100><c> is</c><00:01:17.340><c> super</c><00:01:17.520><c> simple</c>

00:01:18.530 --> 00:01:18.540 align:start position:0%
retrievers is super simple
 

00:01:18.540 --> 00:01:19.969 align:start position:0%
retrievers is super simple
um<00:01:18.600><c> once</c><00:01:19.020><c> you</c><00:01:19.080><c> create</c><00:01:19.260><c> it</c><00:01:19.439><c> you</c><00:01:19.619><c> just</c><00:01:19.740><c> have</c><00:01:19.860><c> to</c>

00:01:19.969 --> 00:01:19.979 align:start position:0%
um once you create it you just have to
 

00:01:19.979 --> 00:01:22.070 align:start position:0%
um once you create it you just have to
call<00:01:20.100><c> retrieve</c><00:01:20.580><c> pass</c><00:01:20.820><c> it</c><00:01:21.000><c> a</c><00:01:21.180><c> string</c><00:01:21.420><c> and</c><00:01:21.900><c> it</c>

00:01:22.070 --> 00:01:22.080 align:start position:0%
call retrieve pass it a string and it
 

00:01:22.080 --> 00:01:24.350 align:start position:0%
call retrieve pass it a string and it
will<00:01:22.200><c> give</c><00:01:22.439><c> you</c><00:01:22.560><c> the</c><00:01:22.920><c> nodes</c><00:01:23.040><c> that</c><00:01:23.400><c> are</c>

00:01:24.350 --> 00:01:24.360 align:start position:0%
will give you the nodes that are
 

00:01:24.360 --> 00:01:26.330 align:start position:0%
will give you the nodes that are
basically<00:01:25.020><c> relevant</c><00:01:25.740><c> to</c><00:01:25.860><c> that</c><00:01:26.040><c> string</c>

00:01:26.330 --> 00:01:26.340 align:start position:0%
basically relevant to that string
 

00:01:26.340 --> 00:01:28.609 align:start position:0%
basically relevant to that string
depending<00:01:26.939><c> on</c><00:01:27.119><c> the</c><00:01:27.240><c> index</c>

00:01:28.609 --> 00:01:28.619 align:start position:0%
depending on the index
 

00:01:28.619 --> 00:01:29.929 align:start position:0%
depending on the index
um<00:01:28.680><c> so</c><00:01:29.040><c> here</c><00:01:29.159><c> we</c><00:01:29.340><c> have</c><00:01:29.460><c> a</c><00:01:29.640><c> really</c><00:01:29.759><c> simple</c>

00:01:29.929 --> 00:01:29.939 align:start position:0%
um so here we have a really simple
 

00:01:29.939 --> 00:01:32.450 align:start position:0%
um so here we have a really simple
example<00:01:30.360><c> of</c><00:01:30.540><c> using</c><00:01:30.900><c> a</c><00:01:31.080><c> vector</c><00:01:31.439><c> index</c><00:01:31.740><c> with</c><00:01:32.040><c> two</c>

00:01:32.450 --> 00:01:32.460 align:start position:0%
example of using a vector index with two
 

00:01:32.460 --> 00:01:35.450 align:start position:0%
example of using a vector index with two
documents<00:01:33.299><c> in</c><00:01:33.420><c> it</c><00:01:33.600><c> we</c><00:01:34.560><c> create</c><00:01:34.860><c> the</c><00:01:35.100><c> Retriever</c>

00:01:35.450 --> 00:01:35.460 align:start position:0%
documents in it we create the Retriever
 

00:01:35.460 --> 00:01:38.210 align:start position:0%
documents in it we create the Retriever
with<00:01:35.640><c> a</c><00:01:35.820><c> top</c><00:01:35.880><c> K</c><00:01:36.119><c> of</c><00:01:36.299><c> one</c><00:01:36.479><c> we</c><00:01:37.259><c> query</c><00:01:37.619><c> for</c><00:01:37.860><c> a</c><00:01:38.040><c> cat</c>

00:01:38.210 --> 00:01:38.220 align:start position:0%
with a top K of one we query for a cat
 

00:01:38.220 --> 00:01:40.789 align:start position:0%
with a top K of one we query for a cat
and<00:01:38.520><c> indeed</c><00:01:38.820><c> we</c><00:01:39.000><c> do</c><00:01:39.119><c> get</c><00:01:39.299><c> back</c><00:01:39.479><c> a</c><00:01:39.900><c> cat</c>

00:01:40.789 --> 00:01:40.799 align:start position:0%
and indeed we do get back a cat
 

00:01:40.799 --> 00:01:43.010 align:start position:0%
and indeed we do get back a cat
now<00:01:41.460><c> on</c><00:01:41.579><c> top</c><00:01:41.759><c> of</c><00:01:41.820><c> this</c><00:01:41.939><c> we</c><00:01:42.180><c> can</c><00:01:42.360><c> also</c><00:01:42.659><c> post</c>

00:01:43.010 --> 00:01:43.020 align:start position:0%
now on top of this we can also post
 

00:01:43.020 --> 00:01:46.010 align:start position:0%
now on top of this we can also post
process<00:01:43.380><c> these</c><00:01:43.799><c> retrieve</c><00:01:44.159><c> nodes</c><00:01:44.600><c> we</c><00:01:45.600><c> have</c><00:01:45.840><c> a</c>

00:01:46.010 --> 00:01:46.020 align:start position:0%
process these retrieve nodes we have a
 

00:01:46.020 --> 00:01:47.569 align:start position:0%
process these retrieve nodes we have a
lot<00:01:46.079><c> of</c><00:01:46.200><c> post</c><00:01:46.380><c> processes</c><00:01:46.920><c> that</c><00:01:47.159><c> are</c><00:01:47.280><c> baked</c>

00:01:47.569 --> 00:01:47.579 align:start position:0%
lot of post processes that are baked
 

00:01:47.579 --> 00:01:49.370 align:start position:0%
lot of post processes that are baked
into<00:01:47.640><c> law</c><00:01:47.880><c> index</c><00:01:48.180><c> that</c><00:01:48.360><c> you</c><00:01:48.540><c> can</c><00:01:48.659><c> use</c><00:01:48.840><c> right</c>

00:01:49.370 --> 00:01:49.380 align:start position:0%
into law index that you can use right
 

00:01:49.380 --> 00:01:51.590 align:start position:0%
into law index that you can use right
now<00:01:49.560><c> on</c><00:01:50.280><c> top</c><00:01:50.399><c> of</c><00:01:50.520><c> that</c><00:01:50.640><c> it's</c><00:01:50.820><c> also</c><00:01:51.180><c> really</c><00:01:51.360><c> easy</c>

00:01:51.590 --> 00:01:51.600 align:start position:0%
now on top of that it's also really easy
 

00:01:51.600 --> 00:01:53.749 align:start position:0%
now on top of that it's also really easy
to<00:01:51.899><c> add</c><00:01:52.140><c> your</c><00:01:52.619><c> own</c><00:01:52.740><c> node</c><00:01:52.979><c> post</c><00:01:53.220><c> processors</c>

00:01:53.749 --> 00:01:53.759 align:start position:0%
to add your own node post processors
 

00:01:53.759 --> 00:01:55.850 align:start position:0%
to add your own node post processors
since<00:01:54.180><c> you</c><00:01:54.299><c> only</c><00:01:54.479><c> have</c><00:01:54.659><c> to</c><00:01:54.780><c> implement</c><00:01:55.140><c> a</c>

00:01:55.850 --> 00:01:55.860 align:start position:0%
since you only have to implement a
 

00:01:55.860 --> 00:01:58.190 align:start position:0%
since you only have to implement a
single<00:01:56.280><c> method</c><00:01:56.579><c> and</c><00:01:57.420><c> later</c><00:01:57.720><c> on</c><00:01:57.899><c> in</c><00:01:58.079><c> this</c>

00:01:58.190 --> 00:01:58.200 align:start position:0%
single method and later on in this
 

00:01:58.200 --> 00:02:00.289 align:start position:0%
single method and later on in this
tutorial<00:01:58.500><c> we'll</c><00:01:58.740><c> go</c><00:01:58.920><c> through</c><00:01:59.159><c> uh</c><00:01:59.939><c> creating</c>

00:02:00.289 --> 00:02:00.299 align:start position:0%
tutorial we'll go through uh creating
 

00:02:00.299 --> 00:02:02.690 align:start position:0%
tutorial we'll go through uh creating
your<00:02:00.479><c> own</c><00:02:00.600><c> node</c><00:02:00.840><c> post</c><00:02:01.079><c> processor</c>

00:02:02.690 --> 00:02:02.700 align:start position:0%
your own node post processor
 

00:02:02.700 --> 00:02:04.130 align:start position:0%
your own node post processor
so<00:02:03.000><c> down</c><00:02:03.180><c> here</c><00:02:03.299><c> we</c><00:02:03.479><c> could</c><00:02:03.600><c> see</c><00:02:03.780><c> a</c><00:02:04.020><c> quick</c>

00:02:04.130 --> 00:02:04.140 align:start position:0%
so down here we could see a quick
 

00:02:04.140 --> 00:02:08.029 align:start position:0%
so down here we could see a quick
example<00:02:04.860><c> of</c><00:02:05.040><c> using</c><00:02:05.399><c> one</c><00:02:05.579><c> at</c><00:02:06.540><c> a</c><00:02:06.659><c> low</c><00:02:06.780><c> level</c><00:02:07.039><c> so</c>

00:02:08.029 --> 00:02:08.039 align:start position:0%
example of using one at a low level so
 

00:02:08.039 --> 00:02:09.710 align:start position:0%
example of using one at a low level so
you<00:02:08.280><c> could</c><00:02:08.340><c> create</c><00:02:08.580><c> like</c><00:02:08.819><c> a</c><00:02:08.940><c> re-ranker</c><00:02:09.539><c> here</c>

00:02:09.710 --> 00:02:09.720 align:start position:0%
you could create like a re-ranker here
 

00:02:09.720 --> 00:02:12.949 align:start position:0%
you could create like a re-ranker here
which<00:02:10.080><c> is</c><00:02:10.200><c> going</c><00:02:10.440><c> to</c><00:02:10.560><c> reorder</c><00:02:11.280><c> our</c><00:02:11.459><c> nodes</c><00:02:11.959><c> we</c>

00:02:12.949 --> 00:02:12.959 align:start position:0%
which is going to reorder our nodes we
 

00:02:12.959 --> 00:02:14.630 align:start position:0%
which is going to reorder our nodes we
create<00:02:13.080><c> our</c><00:02:13.319><c> retriever</c><00:02:13.739><c> again</c><00:02:13.980><c> but</c><00:02:14.340><c> this</c><00:02:14.520><c> time</c>

00:02:14.630 --> 00:02:14.640 align:start position:0%
create our retriever again but this time
 

00:02:14.640 --> 00:02:17.150 align:start position:0%
create our retriever again but this time
we<00:02:14.760><c> set</c><00:02:14.940><c> the</c><00:02:15.120><c> top</c><00:02:15.239><c> K</c><00:02:15.480><c> to</c><00:02:15.720><c> two</c><00:02:16.440><c> the</c><00:02:16.920><c> call</c>

00:02:17.150 --> 00:02:17.160 align:start position:0%
we set the top K to two the call
 

00:02:17.160 --> 00:02:19.130 align:start position:0%
we set the top K to two the call
retrieve<00:02:17.700><c> and</c><00:02:18.120><c> then</c><00:02:18.239><c> we</c><00:02:18.420><c> also</c><00:02:18.660><c> call</c><00:02:18.840><c> Post</c>

00:02:19.130 --> 00:02:19.140 align:start position:0%
retrieve and then we also call Post
 

00:02:19.140 --> 00:02:20.990 align:start position:0%
retrieve and then we also call Post
process<00:02:19.560><c> and</c><00:02:19.980><c> so</c><00:02:20.099><c> what</c><00:02:20.280><c> this</c><00:02:20.400><c> is</c><00:02:20.520><c> going</c><00:02:20.700><c> to</c><00:02:20.819><c> do</c>

00:02:20.990 --> 00:02:21.000 align:start position:0%
process and so what this is going to do
 

00:02:21.000 --> 00:02:22.850 align:start position:0%
process and so what this is going to do
is<00:02:21.540><c> it's</c><00:02:21.720><c> going</c><00:02:21.900><c> to</c><00:02:22.020><c> take</c><00:02:22.140><c> our</c><00:02:22.379><c> nodes</c><00:02:22.560><c> post</c>

00:02:22.850 --> 00:02:22.860 align:start position:0%
is it's going to take our nodes post
 

00:02:22.860 --> 00:02:24.170 align:start position:0%
is it's going to take our nodes post
process<00:02:23.220><c> them</c><00:02:23.459><c> according</c><00:02:23.940><c> to</c><00:02:24.060><c> this</c>

00:02:24.170 --> 00:02:24.180 align:start position:0%
process them according to this
 

00:02:24.180 --> 00:02:26.510 align:start position:0%
process them according to this
re-rankered<00:02:24.959><c> what</c><00:02:25.680><c> this</c><00:02:25.800><c> rear</c><00:02:26.099><c> anchor</c><00:02:26.400><c> is</c>

00:02:26.510 --> 00:02:26.520 align:start position:0%
re-rankered what this rear anchor is
 

00:02:26.520 --> 00:02:28.670 align:start position:0%
re-rankered what this rear anchor is
going<00:02:26.640><c> to</c><00:02:26.760><c> do</c><00:02:26.879><c> is</c><00:02:27.180><c> reorder</c><00:02:27.720><c> the</c><00:02:27.840><c> nodes</c><00:02:28.080><c> and</c>

00:02:28.670 --> 00:02:28.680 align:start position:0%
going to do is reorder the nodes and
 

00:02:28.680 --> 00:02:31.430 align:start position:0%
going to do is reorder the nodes and
return<00:02:28.860><c> the</c><00:02:29.160><c> top</c><00:02:29.340><c> one</c><00:02:29.640><c> so</c><00:02:30.360><c> in</c><00:02:30.480><c> this</c><00:02:30.599><c> case</c><00:02:30.780><c> we're</c>

00:02:31.430 --> 00:02:31.440 align:start position:0%
return the top one so in this case we're
 

00:02:31.440 --> 00:02:33.290 align:start position:0%
return the top one so in this case we're
going<00:02:31.560><c> to</c><00:02:31.680><c> reorder</c><00:02:32.220><c> according</c><00:02:32.580><c> to</c><00:02:32.700><c> our</c><00:02:32.879><c> query</c>

00:02:33.290 --> 00:02:33.300 align:start position:0%
going to reorder according to our query
 

00:02:33.300 --> 00:02:35.510 align:start position:0%
going to reorder according to our query
which<00:02:33.540><c> is</c><00:02:33.660><c> a</c><00:02:33.840><c> black</c><00:02:33.959><c> cat</c><00:02:34.200><c> and</c><00:02:34.980><c> indeed</c><00:02:35.220><c> we</c><00:02:35.400><c> do</c>

00:02:35.510 --> 00:02:35.520 align:start position:0%
which is a black cat and indeed we do
 

00:02:35.520 --> 00:02:38.510 align:start position:0%
which is a black cat and indeed we do
get<00:02:35.700><c> back</c><00:02:35.879><c> the</c><00:02:36.060><c> note</c><00:02:36.239><c> that</c><00:02:36.480><c> says</c><00:02:36.780><c> I</c><00:02:37.020><c> can't</c>

00:02:38.510 --> 00:02:38.520 align:start position:0%
get back the note that says I can't
 

00:02:38.520 --> 00:02:40.250 align:start position:0%
get back the note that says I can't
and<00:02:39.000><c> that's</c><00:02:39.120><c> basically</c><00:02:39.420><c> it</c><00:02:39.599><c> for</c><00:02:39.720><c> retrievers</c>

00:02:40.250 --> 00:02:40.260 align:start position:0%
and that's basically it for retrievers
 

00:02:40.260 --> 00:02:42.290 align:start position:0%
and that's basically it for retrievers
and<00:02:40.319><c> node</c><00:02:40.440><c> post</c><00:02:40.739><c> processors</c><00:02:41.280><c> we</c><00:02:41.879><c> can</c><00:02:42.060><c> kind</c><00:02:42.239><c> of</c>

00:02:42.290 --> 00:02:42.300 align:start position:0%
and node post processors we can kind of
 

00:02:42.300 --> 00:02:44.330 align:start position:0%
and node post processors we can kind of
jump<00:02:42.480><c> right</c><00:02:42.720><c> into</c><00:02:43.019><c> our</c><00:02:43.500><c> example</c><00:02:43.920><c> notebook</c>

00:02:44.330 --> 00:02:44.340 align:start position:0%
jump right into our example notebook
 

00:02:44.340 --> 00:02:45.110 align:start position:0%
jump right into our example notebook
here

00:02:45.110 --> 00:02:45.120 align:start position:0%
here
 

00:02:45.120 --> 00:02:46.670 align:start position:0%
here
so<00:02:45.480><c> in</c><00:02:45.599><c> this</c><00:02:45.780><c> notebook</c><00:02:46.080><c> we're</c><00:02:46.319><c> going</c><00:02:46.500><c> to</c><00:02:46.620><c> be</c>

00:02:46.670 --> 00:02:46.680 align:start position:0%
so in this notebook we're going to be
 

00:02:46.680 --> 00:02:49.309 align:start position:0%
so in this notebook we're going to be
covering<00:02:47.099><c> a</c><00:02:47.700><c> auto</c><00:02:48.120><c> merging</c><00:02:48.599><c> retriever</c><00:02:49.019><c> as</c>

00:02:49.309 --> 00:02:49.319 align:start position:0%
covering a auto merging retriever as
 

00:02:49.319 --> 00:02:51.470 align:start position:0%
covering a auto merging retriever as
well<00:02:49.440><c> as</c><00:02:49.560><c> implementing</c><00:02:50.099><c> our</c><00:02:50.400><c> own</c><00:02:50.580><c> custom</c><00:02:51.000><c> node</c>

00:02:51.470 --> 00:02:51.480 align:start position:0%
well as implementing our own custom node
 

00:02:51.480 --> 00:02:52.970 align:start position:0%
well as implementing our own custom node
post<00:02:51.660><c> processor</c>

00:02:52.970 --> 00:02:52.980 align:start position:0%
post processor
 

00:02:52.980 --> 00:02:54.710 align:start position:0%
post processor
at<00:02:53.459><c> the</c><00:02:53.580><c> top</c><00:02:53.700><c> of</c><00:02:53.760><c> the</c><00:02:53.819><c> notebook</c><00:02:54.120><c> here</c><00:02:54.360><c> is</c><00:02:54.540><c> just</c>

00:02:54.710 --> 00:02:54.720 align:start position:0%
at the top of the notebook here is just
 

00:02:54.720 --> 00:02:57.110 align:start position:0%
at the top of the notebook here is just
some<00:02:54.900><c> setup</c><00:02:55.260><c> to</c><00:02:55.560><c> set</c><00:02:55.860><c> up</c><00:02:55.980><c> our</c><00:02:56.099><c> llms</c><00:02:56.760><c> and</c><00:02:56.940><c> our</c>

00:02:57.110 --> 00:02:57.120 align:start position:0%
some setup to set up our llms and our
 

00:02:57.120 --> 00:02:59.750 align:start position:0%
some setup to set up our llms and our
open<00:02:57.239><c> AI</c><00:02:57.660><c> keys</c><00:02:58.040><c> we're</c><00:02:59.040><c> going</c><00:02:59.220><c> to</c><00:02:59.280><c> be</c><00:02:59.340><c> using</c><00:02:59.580><c> a</c>

00:02:59.750 --> 00:02:59.760 align:start position:0%
open AI keys we're going to be using a
 

00:02:59.760 --> 00:03:01.970 align:start position:0%
open AI keys we're going to be using a
local<00:02:59.879><c> embedding</c><00:03:00.420><c> model</c><00:03:00.599><c> just</c><00:03:01.200><c> to</c><00:03:01.500><c> save</c>

00:03:01.970 --> 00:03:01.980 align:start position:0%
local embedding model just to save
 

00:03:01.980 --> 00:03:05.030 align:start position:0%
local embedding model just to save
myself<00:03:02.220><c> the</c><00:03:02.519><c> tokens</c><00:03:02.760><c> as</c><00:03:02.940><c> I'm</c><00:03:03.120><c> running</c><00:03:03.300><c> this</c>

00:03:05.030 --> 00:03:05.040 align:start position:0%
myself the tokens as I'm running this
 

00:03:05.040 --> 00:03:08.390 align:start position:0%
myself the tokens as I'm running this
so<00:03:05.700><c> first</c><00:03:06.000><c> step</c><00:03:06.260><c> is</c><00:03:07.260><c> loading</c><00:03:07.739><c> our</c><00:03:07.860><c> documents</c>

00:03:08.390 --> 00:03:08.400 align:start position:0%
so first step is loading our documents
 

00:03:08.400 --> 00:03:10.009 align:start position:0%
so first step is loading our documents
and<00:03:08.700><c> specifically</c><00:03:09.180><c> we</c><00:03:09.480><c> want</c><00:03:09.540><c> to</c><00:03:09.660><c> load</c><00:03:09.900><c> our</c>

00:03:10.009 --> 00:03:10.019 align:start position:0%
and specifically we want to load our
 

00:03:10.019 --> 00:03:11.990 align:start position:0%
and specifically we want to load our
documents<00:03:10.560><c> in</c><00:03:10.800><c> order</c><00:03:10.980><c> to</c><00:03:11.220><c> use</c><00:03:11.400><c> them</c><00:03:11.580><c> with</c><00:03:11.879><c> the</c>

00:03:11.990 --> 00:03:12.000 align:start position:0%
documents in order to use them with the
 

00:03:12.000 --> 00:03:14.089 align:start position:0%
documents in order to use them with the
auto<00:03:12.120><c> merging</c><00:03:12.599><c> retriever</c><00:03:13.019><c> now</c><00:03:13.800><c> the</c><00:03:13.920><c> auto</c>

00:03:14.089 --> 00:03:14.099 align:start position:0%
auto merging retriever now the auto
 

00:03:14.099 --> 00:03:15.830 align:start position:0%
auto merging retriever now the auto
merging<00:03:14.519><c> retriever</c><00:03:14.879><c> depends</c><00:03:15.360><c> on</c><00:03:15.540><c> this</c><00:03:15.659><c> thing</c>

00:03:15.830 --> 00:03:15.840 align:start position:0%
merging retriever depends on this thing
 

00:03:15.840 --> 00:03:18.710 align:start position:0%
merging retriever depends on this thing
called<00:03:16.019><c> a</c><00:03:16.440><c> hierarchical</c><00:03:16.920><c> node</c><00:03:17.280><c> parser</c><00:03:17.879><c> what</c>

00:03:18.710 --> 00:03:18.720 align:start position:0%
called a hierarchical node parser what
 

00:03:18.720 --> 00:03:20.149 align:start position:0%
called a hierarchical node parser what
this<00:03:18.840><c> means</c><00:03:19.080><c> is</c><00:03:19.200><c> that</c><00:03:19.319><c> it</c><00:03:19.440><c> parses</c><00:03:19.860><c> your</c><00:03:19.980><c> nodes</c>

00:03:20.149 --> 00:03:20.159 align:start position:0%
this means is that it parses your nodes
 

00:03:20.159 --> 00:03:22.610 align:start position:0%
this means is that it parses your nodes
into<00:03:20.400><c> a</c><00:03:20.760><c> hierarchy</c><00:03:21.180><c> where</c><00:03:21.480><c> we</c><00:03:21.659><c> have</c><00:03:21.900><c> a</c><00:03:22.500><c> top</c>

00:03:22.610 --> 00:03:22.620 align:start position:0%
into a hierarchy where we have a top
 

00:03:22.620 --> 00:03:24.229 align:start position:0%
into a hierarchy where we have a top
level<00:03:22.800><c> chunk</c><00:03:23.220><c> size</c><00:03:23.459><c> and</c><00:03:23.879><c> then</c><00:03:23.940><c> we</c><00:03:24.120><c> have</c>

00:03:24.229 --> 00:03:24.239 align:start position:0%
level chunk size and then we have
 

00:03:24.239 --> 00:03:26.630 align:start position:0%
level chunk size and then we have
children<00:03:24.480><c> trunks</c><00:03:25.080><c> that</c><00:03:25.260><c> are</c><00:03:25.440><c> smaller</c><00:03:25.800><c> so</c><00:03:26.580><c> in</c>

00:03:26.630 --> 00:03:26.640 align:start position:0%
children trunks that are smaller so in
 

00:03:26.640 --> 00:03:28.670 align:start position:0%
children trunks that are smaller so in
this<00:03:26.819><c> case</c><00:03:26.940><c> we</c><00:03:27.180><c> have</c><00:03:27.360><c> a</c><00:03:27.659><c> top</c><00:03:27.840><c> level</c><00:03:28.080><c> chunk</c><00:03:28.500><c> size</c>

00:03:28.670 --> 00:03:28.680 align:start position:0%
this case we have a top level chunk size
 

00:03:28.680 --> 00:03:32.149 align:start position:0%
this case we have a top level chunk size
of<00:03:28.920><c> 1536</c><00:03:29.819><c> and</c><00:03:30.599><c> then</c><00:03:30.840><c> each</c><00:03:31.260><c> top</c><00:03:31.560><c> level</c><00:03:31.739><c> chunk</c>

00:03:32.149 --> 00:03:32.159 align:start position:0%
of 1536 and then each top level chunk
 

00:03:32.159 --> 00:03:34.369 align:start position:0%
of 1536 and then each top level chunk
will<00:03:32.280><c> have</c><00:03:32.459><c> three</c><00:03:32.640><c> children</c><00:03:32.940><c> of</c><00:03:33.900><c> a</c><00:03:34.080><c> size</c><00:03:34.200><c> that</c>

00:03:34.369 --> 00:03:34.379 align:start position:0%
will have three children of a size that
 

00:03:34.379 --> 00:03:35.750 align:start position:0%
will have three children of a size that
are<00:03:34.560><c> one-third</c><00:03:34.980><c> that</c>

00:03:35.750 --> 00:03:35.760 align:start position:0%
are one-third that
 

00:03:35.760 --> 00:03:37.729 align:start position:0%
are one-third that
what<00:03:36.239><c> this</c><00:03:36.420><c> means</c><00:03:36.659><c> is</c><00:03:36.780><c> that</c><00:03:37.019><c> when</c><00:03:37.260><c> we</c><00:03:37.379><c> call</c><00:03:37.560><c> the</c>

00:03:37.729 --> 00:03:37.739 align:start position:0%
what this means is that when we call the
 

00:03:37.739 --> 00:03:40.009 align:start position:0%
what this means is that when we call the
retriever<00:03:38.099><c> later</c><00:03:38.340><c> on</c><00:03:38.580><c> if</c><00:03:39.360><c> two</c><00:03:39.599><c> out</c><00:03:39.840><c> of</c><00:03:39.959><c> the</c>

00:03:40.009 --> 00:03:40.019 align:start position:0%
retriever later on if two out of the
 

00:03:40.019 --> 00:03:42.229 align:start position:0%
retriever later on if two out of the
three<00:03:40.260><c> children</c><00:03:40.620><c> are</c><00:03:41.040><c> retrieved</c><00:03:41.519><c> we</c><00:03:42.060><c> replace</c>

00:03:42.229 --> 00:03:42.239 align:start position:0%
three children are retrieved we replace
 

00:03:42.239 --> 00:03:44.270 align:start position:0%
three children are retrieved we replace
it<00:03:42.599><c> with</c><00:03:42.840><c> the</c><00:03:43.019><c> parent</c><00:03:43.140><c> chunk</c><00:03:43.620><c> because</c><00:03:43.860><c> clearly</c>

00:03:44.270 --> 00:03:44.280 align:start position:0%
it with the parent chunk because clearly
 

00:03:44.280 --> 00:03:45.949 align:start position:0%
it with the parent chunk because clearly
it's<00:03:44.459><c> relevant</c><00:03:44.879><c> enough</c><00:03:45.060><c> that</c><00:03:45.360><c> we</c><00:03:45.540><c> should</c><00:03:45.780><c> just</c>

00:03:45.949 --> 00:03:45.959 align:start position:0%
it's relevant enough that we should just
 

00:03:45.959 --> 00:03:48.410 align:start position:0%
it's relevant enough that we should just
include<00:03:46.319><c> the</c><00:03:46.500><c> entire</c><00:03:46.799><c> parent</c><00:03:47.220><c> context</c>

00:03:48.410 --> 00:03:48.420 align:start position:0%
include the entire parent context
 

00:03:48.420 --> 00:03:50.990 align:start position:0%
include the entire parent context
in<00:03:49.379><c> order</c><00:03:49.560><c> to</c><00:03:49.680><c> do</c><00:03:49.860><c> this</c><00:03:49.980><c> we</c><00:03:50.280><c> use</c><00:03:50.459><c> our</c><00:03:50.700><c> markdown</c>

00:03:50.990 --> 00:03:51.000 align:start position:0%
in order to do this we use our markdown
 

00:03:51.000 --> 00:03:55.789 align:start position:0%
in order to do this we use our markdown
loader<00:03:51.420><c> from</c><00:03:51.720><c> previous</c><00:03:52.019><c> uh</c><00:03:52.799><c> episodes</c><00:03:53.040><c> here</c><00:03:53.519><c> we</c>

00:03:55.789 --> 00:03:55.799 align:start position:0%
loader from previous uh episodes here we
 

00:03:55.799 --> 00:03:58.309 align:start position:0%
loader from previous uh episodes here we
combine<00:03:56.519><c> our</c><00:03:56.819><c> documents</c><00:03:57.239><c> into</c><00:03:57.540><c> one</c><00:03:58.019><c> kind</c><00:03:58.200><c> of</c>

00:03:58.309 --> 00:03:58.319 align:start position:0%
combine our documents into one kind of
 

00:03:58.319 --> 00:04:00.289 align:start position:0%
combine our documents into one kind of
Mega<00:03:58.620><c> document</c><00:03:58.980><c> to</c><00:03:59.340><c> help</c><00:03:59.400><c> this</c><00:03:59.640><c> parsing</c><00:04:00.060><c> work</c>

00:04:00.289 --> 00:04:00.299 align:start position:0%
Mega document to help this parsing work
 

00:04:00.299 --> 00:04:02.750 align:start position:0%
Mega document to help this parsing work
better<00:04:00.680><c> we</c><00:04:01.680><c> set</c><00:04:01.860><c> up</c><00:04:01.980><c> the</c><00:04:02.159><c> hierarchical</c><00:04:02.519><c> node</c>

00:04:02.750 --> 00:04:02.760 align:start position:0%
better we set up the hierarchical node
 

00:04:02.760 --> 00:04:05.869 align:start position:0%
better we set up the hierarchical node
processor<00:04:03.480><c> and</c><00:04:03.720><c> we</c><00:04:03.840><c> return</c><00:04:04.019><c> our</c><00:04:04.440><c> nodes</c><00:04:04.860><c> at</c><00:04:05.760><c> the</c>

00:04:05.869 --> 00:04:05.879 align:start position:0%
processor and we return our nodes at the
 

00:04:05.879 --> 00:04:07.009 align:start position:0%
processor and we return our nodes at the
bottom<00:04:06.060><c> here</c><00:04:06.239><c> you'll</c><00:04:06.420><c> see</c><00:04:06.540><c> we're</c><00:04:06.780><c> also</c>

00:04:07.009 --> 00:04:07.019 align:start position:0%
bottom here you'll see we're also
 

00:04:07.019 --> 00:04:09.470 align:start position:0%
bottom here you'll see we're also
returning<00:04:07.379><c> our</c><00:04:07.560><c> Leaf</c><00:04:07.920><c> nodes</c><00:04:08.220><c> what</c><00:04:09.120><c> this</c><00:04:09.239><c> means</c>

00:04:09.470 --> 00:04:09.480 align:start position:0%
returning our Leaf nodes what this means
 

00:04:09.480 --> 00:04:12.589 align:start position:0%
returning our Leaf nodes what this means
is<00:04:09.599><c> nodes</c><00:04:09.959><c> that</c><00:04:10.260><c> uh</c><00:04:11.400><c> don't</c><00:04:11.819><c> have</c><00:04:12.060><c> any</c><00:04:12.360><c> children</c>

00:04:12.589 --> 00:04:12.599 align:start position:0%
is nodes that uh don't have any children
 

00:04:12.599 --> 00:04:14.509 align:start position:0%
is nodes that uh don't have any children
essentially<00:04:13.140><c> and</c><00:04:13.439><c> you'll</c><00:04:13.620><c> see</c><00:04:13.799><c> later</c><00:04:14.099><c> on</c><00:04:14.340><c> how</c>

00:04:14.509 --> 00:04:14.519 align:start position:0%
essentially and you'll see later on how
 

00:04:14.519 --> 00:04:16.550 align:start position:0%
essentially and you'll see later on how
we<00:04:14.640><c> use</c><00:04:14.819><c> those</c>

00:04:16.550 --> 00:04:16.560 align:start position:0%
we use those
 

00:04:16.560 --> 00:04:19.610 align:start position:0%
we use those
so<00:04:17.220><c> now</c><00:04:17.459><c> that</c><00:04:17.639><c> we've</c><00:04:18.120><c> loaded</c><00:04:18.660><c> our</c><00:04:18.900><c> nodes</c><00:04:19.199><c> or</c>

00:04:19.610 --> 00:04:19.620 align:start position:0%
so now that we've loaded our nodes or
 

00:04:19.620 --> 00:04:21.110 align:start position:0%
so now that we've loaded our nodes or
created<00:04:19.979><c> the</c><00:04:20.160><c> function</c><00:04:20.340><c> to</c><00:04:20.579><c> load</c><00:04:20.760><c> our</c><00:04:20.880><c> nodes</c>

00:04:21.110 --> 00:04:21.120 align:start position:0%
created the function to load our nodes
 

00:04:21.120 --> 00:04:23.930 align:start position:0%
created the function to load our nodes
we<00:04:21.359><c> can</c><00:04:21.479><c> also</c><00:04:21.840><c> set</c><00:04:22.380><c> up</c><00:04:22.560><c> our</c><00:04:23.040><c> function</c><00:04:23.400><c> to</c><00:04:23.699><c> load</c>

00:04:23.930 --> 00:04:23.940 align:start position:0%
we can also set up our function to load
 

00:04:23.940 --> 00:04:26.270 align:start position:0%
we can also set up our function to load
our<00:04:24.120><c> query</c><00:04:24.419><c> engines</c><00:04:24.840><c> if</c><00:04:25.560><c> you</c><00:04:25.680><c> remember</c><00:04:25.860><c> we're</c>

00:04:26.270 --> 00:04:26.280 align:start position:0%
our query engines if you remember we're
 

00:04:26.280 --> 00:04:29.510 align:start position:0%
our query engines if you remember we're
creating<00:04:26.639><c> a</c><00:04:27.180><c> kind</c><00:04:27.540><c> of</c><00:04:27.600><c> chat</c><00:04:28.080><c> llm</c><00:04:28.800><c> application</c>

00:04:29.510 --> 00:04:29.520 align:start position:0%
creating a kind of chat llm application
 

00:04:29.520 --> 00:04:31.870 align:start position:0%
creating a kind of chat llm application
over<00:04:29.940><c> top</c><00:04:30.120><c> of</c><00:04:30.300><c> the</c><00:04:30.419><c> Lum</c><00:04:30.660><c> index</c><00:04:31.020><c> documentation</c>

00:04:31.870 --> 00:04:31.880 align:start position:0%
over top of the Lum index documentation
 

00:04:31.880 --> 00:04:34.730 align:start position:0%
over top of the Lum index documentation
so<00:04:32.880><c> we</c><00:04:33.060><c> have</c><00:04:33.180><c> several</c><00:04:33.660><c> query</c><00:04:34.080><c> engines</c><00:04:34.440><c> for</c>

00:04:34.730 --> 00:04:34.740 align:start position:0%
so we have several query engines for
 

00:04:34.740 --> 00:04:36.650 align:start position:0%
so we have several query engines for
different<00:04:34.919><c> sections</c><00:04:35.759><c> of</c><00:04:35.940><c> the</c><00:04:36.060><c> documentation</c>

00:04:36.650 --> 00:04:36.660 align:start position:0%
different sections of the documentation
 

00:04:36.660 --> 00:04:39.170 align:start position:0%
different sections of the documentation
so<00:04:37.440><c> here</c><00:04:37.620><c> I've</c><00:04:37.740><c> set</c><00:04:37.919><c> up</c><00:04:38.100><c> a</c><00:04:38.340><c> function</c><00:04:38.759><c> that</c><00:04:39.060><c> will</c>

00:04:39.170 --> 00:04:39.180 align:start position:0%
so here I've set up a function that will
 

00:04:39.180 --> 00:04:41.510 align:start position:0%
so here I've set up a function that will
take<00:04:39.360><c> a</c><00:04:39.660><c> directory</c><00:04:40.320><c> a</c><00:04:40.800><c> description</c><00:04:41.100><c> of</c><00:04:41.340><c> that</c>

00:04:41.510 --> 00:04:41.520 align:start position:0%
take a directory a description of that
 

00:04:41.520 --> 00:04:43.370 align:start position:0%
take a directory a description of that
directory<00:04:42.000><c> and</c><00:04:42.660><c> possibly</c><00:04:42.960><c> any</c><00:04:43.139><c> post</c>

00:04:43.370 --> 00:04:43.380 align:start position:0%
directory and possibly any post
 

00:04:43.380 --> 00:04:46.310 align:start position:0%
directory and possibly any post
processors<00:04:43.979><c> to</c><00:04:44.580><c> basically</c><00:04:45.360><c> construct</c><00:04:45.840><c> each</c>

00:04:46.310 --> 00:04:46.320 align:start position:0%
processors to basically construct each
 

00:04:46.320 --> 00:04:51.469 align:start position:0%
processors to basically construct each
index<00:04:46.860><c> or</c><00:04:47.340><c> query</c><00:04:47.699><c> engine</c><00:04:48.000><c> for</c><00:04:48.720><c> each</c><00:04:49.199><c> directory</c>

00:04:51.469 --> 00:04:51.479 align:start position:0%
index or query engine for each directory
 

00:04:51.479 --> 00:04:53.030 align:start position:0%
index or query engine for each directory
so<00:04:51.960><c> it</c><00:04:52.139><c> does</c><00:04:52.259><c> a</c><00:04:52.380><c> little</c><00:04:52.440><c> bit</c><00:04:52.560><c> of</c><00:04:52.620><c> checking</c><00:04:52.919><c> here</c>

00:04:53.030 --> 00:04:53.040 align:start position:0%
so it does a little bit of checking here
 

00:04:53.040 --> 00:04:54.710 align:start position:0%
so it does a little bit of checking here
to<00:04:53.280><c> see</c><00:04:53.400><c> if</c><00:04:53.580><c> it's</c><00:04:53.759><c> already</c><00:04:54.000><c> been</c><00:04:54.180><c> created</c><00:04:54.479><c> and</c>

00:04:54.710 --> 00:04:54.720 align:start position:0%
to see if it's already been created and
 

00:04:54.720 --> 00:04:57.230 align:start position:0%
to see if it's already been created and
saved<00:04:55.080><c> to</c><00:04:55.139><c> disk</c><00:04:55.500><c> if</c><00:04:56.280><c> it's</c><00:04:56.460><c> not</c><00:04:56.639><c> been</c><00:04:56.820><c> saved</c>

00:04:57.230 --> 00:04:57.240 align:start position:0%
saved to disk if it's not been saved
 

00:04:57.240 --> 00:04:59.990 align:start position:0%
saved to disk if it's not been saved
disk<00:04:57.720><c> we</c><00:04:58.500><c> call</c><00:04:58.740><c> our</c><00:04:58.860><c> function</c><00:04:59.160><c> from</c><00:04:59.340><c> above</c><00:04:59.639><c> to</c>

00:04:59.990 --> 00:05:00.000 align:start position:0%
disk we call our function from above to
 

00:05:00.000 --> 00:05:02.590 align:start position:0%
disk we call our function from above to
load<00:05:00.360><c> the</c><00:05:00.660><c> markdown</c><00:05:01.080><c> documentation</c>

00:05:02.590 --> 00:05:02.600 align:start position:0%
load the markdown documentation
 

00:05:02.600 --> 00:05:07.570 align:start position:0%
load the markdown documentation
we<00:05:03.600><c> add</c><00:05:04.080><c> the</c><00:05:05.060><c> documents</c><00:05:06.060><c> to</c><00:05:06.180><c> a</c><00:05:06.360><c> doc</c><00:05:06.660><c> store</c>

00:05:07.570 --> 00:05:07.580 align:start position:0%
we add the documents to a doc store
 

00:05:07.580 --> 00:05:10.070 align:start position:0%
we add the documents to a doc store
and<00:05:08.580><c> then</c><00:05:08.699><c> we</c><00:05:08.880><c> also</c><00:05:09.120><c> construct</c><00:05:09.540><c> the</c><00:05:09.720><c> index</c>

00:05:10.070 --> 00:05:10.080 align:start position:0%
and then we also construct the index
 

00:05:10.080 --> 00:05:12.469 align:start position:0%
and then we also construct the index
using<00:05:10.800><c> just</c><00:05:11.160><c> the</c><00:05:11.340><c> leaf</c><00:05:11.639><c> nodes</c>

00:05:12.469 --> 00:05:12.479 align:start position:0%
using just the leaf nodes
 

00:05:12.479 --> 00:05:13.909 align:start position:0%
using just the leaf nodes
now<00:05:12.840><c> what</c><00:05:13.020><c> this</c><00:05:13.139><c> means</c><00:05:13.320><c> here</c><00:05:13.440><c> is</c><00:05:13.560><c> that</c><00:05:13.740><c> the</c>

00:05:13.909 --> 00:05:13.919 align:start position:0%
now what this means here is that the
 

00:05:13.919 --> 00:05:15.950 align:start position:0%
now what this means here is that the
index<00:05:14.280><c> is</c><00:05:14.820><c> only</c><00:05:15.000><c> going</c><00:05:15.180><c> to</c><00:05:15.300><c> retrieve</c><00:05:15.660><c> Leaf</c>

00:05:15.950 --> 00:05:15.960 align:start position:0%
index is only going to retrieve Leaf
 

00:05:15.960 --> 00:05:18.050 align:start position:0%
index is only going to retrieve Leaf
nodes<00:05:16.259><c> but</c><00:05:16.800><c> then</c><00:05:16.979><c> later</c><00:05:17.280><c> on</c><00:05:17.520><c> this</c><00:05:17.880><c> Auto</c>

00:05:18.050 --> 00:05:18.060 align:start position:0%
nodes but then later on this Auto
 

00:05:18.060 --> 00:05:20.510 align:start position:0%
nodes but then later on this Auto
merging<00:05:18.540><c> retriever</c><00:05:18.960><c> is</c><00:05:19.680><c> going</c><00:05:19.860><c> to</c><00:05:19.979><c> check</c><00:05:20.220><c> if</c>

00:05:20.510 --> 00:05:20.520 align:start position:0%
merging retriever is going to check if
 

00:05:20.520 --> 00:05:22.909 align:start position:0%
merging retriever is going to check if
we've<00:05:20.820><c> retrieved</c><00:05:21.240><c> enough</c><00:05:21.479><c> children</c><00:05:21.900><c> of</c><00:05:22.740><c> a</c>

00:05:22.909 --> 00:05:22.919 align:start position:0%
we've retrieved enough children of a
 

00:05:22.919 --> 00:05:24.890 align:start position:0%
we've retrieved enough children of a
kind<00:05:23.160><c> of</c><00:05:23.220><c> hierarchical</c><00:05:23.639><c> parent</c><00:05:24.000><c> that</c><00:05:24.660><c> we</c><00:05:24.840><c> have</c>

00:05:24.890 --> 00:05:24.900 align:start position:0%
kind of hierarchical parent that we have
 

00:05:24.900 --> 00:05:27.590 align:start position:0%
kind of hierarchical parent that we have
to<00:05:25.080><c> merge</c><00:05:25.440><c> and</c><00:05:25.680><c> replace</c><00:05:25.800><c> with</c><00:05:26.160><c> that</c><00:05:26.340><c> parent</c>

00:05:27.590 --> 00:05:27.600 align:start position:0%
to merge and replace with that parent
 

00:05:27.600 --> 00:05:29.570 align:start position:0%
to merge and replace with that parent
in<00:05:28.259><c> order</c><00:05:28.380><c> for</c><00:05:28.620><c> this</c><00:05:28.800><c> to</c><00:05:28.919><c> work</c><00:05:29.039><c> well</c><00:05:29.220><c> you</c><00:05:29.520><c> have</c>

00:05:29.570 --> 00:05:29.580 align:start position:0%
in order for this to work well you have
 

00:05:29.580 --> 00:05:31.629 align:start position:0%
in order for this to work well you have
to<00:05:29.699><c> set</c><00:05:29.880><c> the</c><00:05:30.060><c> top</c><00:05:30.180><c> k</c><00:05:30.360><c> a</c><00:05:30.660><c> little</c><00:05:30.780><c> high</c>

00:05:31.629 --> 00:05:31.639 align:start position:0%
to set the top k a little high
 

00:05:31.639 --> 00:05:33.830 align:start position:0%
to set the top k a little high
you'll<00:05:32.639><c> see</c><00:05:32.759><c> here</c><00:05:32.940><c> we're</c><00:05:33.060><c> setting</c><00:05:33.419><c> the</c><00:05:33.479><c> top</c><00:05:33.600><c> K</c>

00:05:33.830 --> 00:05:33.840 align:start position:0%
you'll see here we're setting the top K
 

00:05:33.840 --> 00:05:36.350 align:start position:0%
you'll see here we're setting the top K
to<00:05:34.139><c> 12.</c>

00:05:36.350 --> 00:05:36.360 align:start position:0%
to 12.
 

00:05:36.360 --> 00:05:37.969 align:start position:0%
to 12.
now<00:05:36.720><c> I've</c><00:05:36.840><c> also</c><00:05:37.080><c> included</c><00:05:37.380><c> some</c><00:05:37.500><c> code</c><00:05:37.620><c> in</c><00:05:37.800><c> here</c>

00:05:37.969 --> 00:05:37.979 align:start position:0%
now I've also included some code in here
 

00:05:37.979 --> 00:05:41.629 align:start position:0%
now I've also included some code in here
to<00:05:38.160><c> also</c><00:05:38.699><c> load</c><00:05:39.660><c> a</c><00:05:39.900><c> normal</c><00:05:40.080><c> index</c><00:05:40.560><c> without</c><00:05:40.979><c> the</c>

00:05:41.629 --> 00:05:41.639 align:start position:0%
to also load a normal index without the
 

00:05:41.639 --> 00:05:43.969 align:start position:0%
to also load a normal index without the
hierarchical<00:05:42.180><c> node</c><00:05:42.600><c> parsing</c><00:05:43.080><c> or</c><00:05:43.259><c> without</c><00:05:43.440><c> the</c>

00:05:43.969 --> 00:05:43.979 align:start position:0%
hierarchical node parsing or without the
 

00:05:43.979 --> 00:05:46.550 align:start position:0%
hierarchical node parsing or without the
auto<00:05:44.460><c> merging</c><00:05:44.940><c> retrieve</c><00:05:45.780><c> so</c><00:05:46.139><c> in</c><00:05:46.259><c> this</c><00:05:46.440><c> next</c>

00:05:46.550 --> 00:05:46.560 align:start position:0%
auto merging retrieve so in this next
 

00:05:46.560 --> 00:05:48.890 align:start position:0%
auto merging retrieve so in this next
section<00:05:46.800><c> here</c><00:05:47.039><c> I'm</c><00:05:47.280><c> going</c><00:05:47.460><c> to</c><00:05:47.580><c> compare</c><00:05:48.120><c> two</c>

00:05:48.890 --> 00:05:48.900 align:start position:0%
section here I'm going to compare two
 

00:05:48.900 --> 00:05:51.350 align:start position:0%
section here I'm going to compare two
retrieval<00:05:49.560><c> techniques</c><00:05:50.039><c> so</c><00:05:50.699><c> the</c><00:05:50.940><c> first</c><00:05:51.180><c> one</c>

00:05:51.350 --> 00:05:51.360 align:start position:0%
retrieval techniques so the first one
 

00:05:51.360 --> 00:05:53.930 align:start position:0%
retrieval techniques so the first one
using<00:05:51.900><c> our</c><00:05:52.139><c> hierarchical</c><00:05:52.560><c> Retriever</c><00:05:53.160><c> and</c><00:05:53.820><c> the</c>

00:05:53.930 --> 00:05:53.940 align:start position:0%
using our hierarchical Retriever and the
 

00:05:53.940 --> 00:05:55.850 align:start position:0%
using our hierarchical Retriever and the
second<00:05:54.060><c> one</c><00:05:54.240><c> without</c><00:05:54.560><c> we're</c><00:05:55.560><c> going</c><00:05:55.680><c> to</c><00:05:55.800><c> be</c>

00:05:55.850 --> 00:05:55.860 align:start position:0%
second one without we're going to be
 

00:05:55.860 --> 00:05:57.710 align:start position:0%
second one without we're going to be
retrieving<00:05:56.340><c> based</c><00:05:56.759><c> on</c><00:05:56.820><c> the</c><00:05:57.240><c> documentation</c>

00:05:57.710 --> 00:05:57.720 align:start position:0%
retrieving based on the documentation
 

00:05:57.720 --> 00:05:59.330 align:start position:0%
retrieving based on the documentation
that<00:05:58.020><c> talks</c><00:05:58.259><c> about</c><00:05:58.320><c> how</c><00:05:58.740><c> to</c><00:05:58.800><c> query</c><00:05:59.100><c> things</c>

00:05:59.330 --> 00:05:59.340 align:start position:0%
that talks about how to query things
 

00:05:59.340 --> 00:06:00.469 align:start position:0%
that talks about how to query things
essentially

00:06:00.469 --> 00:06:00.479 align:start position:0%
essentially
 

00:06:00.479 --> 00:06:01.790 align:start position:0%
essentially
so<00:06:00.840><c> here</c><00:06:01.020><c> I'm</c><00:06:01.139><c> going</c><00:06:01.259><c> to</c><00:06:01.259><c> build</c><00:06:01.440><c> my</c><00:06:01.620><c> first</c>

00:06:01.790 --> 00:06:01.800 align:start position:0%
so here I'm going to build my first
 

00:06:01.800 --> 00:06:04.010 align:start position:0%
so here I'm going to build my first
query<00:06:02.100><c> engine</c><00:06:02.340><c> I'm</c><00:06:03.300><c> going</c><00:06:03.419><c> to</c><00:06:03.539><c> remove</c><00:06:03.720><c> the</c>

00:06:04.010 --> 00:06:04.020 align:start position:0%
query engine I'm going to remove the
 

00:06:04.020 --> 00:06:06.890 align:start position:0%
query engine I'm going to remove the
save<00:06:04.320><c> indexes</c><00:06:04.919><c> just</c><00:06:05.280><c> to</c><00:06:05.520><c> avoid</c><00:06:05.940><c> any</c><00:06:06.120><c> conflicts</c>

00:06:06.890 --> 00:06:06.900 align:start position:0%
save indexes just to avoid any conflicts
 

00:06:06.900 --> 00:06:11.510 align:start position:0%
save indexes just to avoid any conflicts
between<00:06:07.520><c> saving</c><00:06:08.520><c> and</c><00:06:08.639><c> loading</c><00:06:09.000><c> here</c>

00:06:11.510 --> 00:06:11.520 align:start position:0%
 
 

00:06:11.520 --> 00:06:13.730 align:start position:0%
 
and<00:06:12.000><c> then</c><00:06:12.120><c> lastly</c><00:06:12.840><c> I'm</c><00:06:13.080><c> going</c><00:06:13.259><c> to</c><00:06:13.500><c> call</c>

00:06:13.730 --> 00:06:13.740 align:start position:0%
and then lastly I'm going to call
 

00:06:13.740 --> 00:06:16.430 align:start position:0%
and then lastly I'm going to call
retrieve<00:06:14.340><c> with</c><00:06:14.580><c> these</c><00:06:14.699><c> query</c><00:06:15.000><c> engines</c><00:06:15.440><c> with</c>

00:06:16.430 --> 00:06:16.440 align:start position:0%
retrieve with these query engines with
 

00:06:16.440 --> 00:06:18.350 align:start position:0%
retrieve with these query engines with
the<00:06:16.620><c> same</c><00:06:16.740><c> query</c><00:06:17.039><c> string</c><00:06:17.460><c> which</c><00:06:17.699><c> is</c><00:06:17.820><c> how</c><00:06:18.120><c> do</c><00:06:18.180><c> I</c>

00:06:18.350 --> 00:06:18.360 align:start position:0%
the same query string which is how do I
 

00:06:18.360 --> 00:06:20.090 align:start position:0%
the same query string which is how do I
set<00:06:18.539><c> up</c><00:06:18.660><c> a</c><00:06:18.900><c> query</c><00:06:19.080><c> engine</c>

00:06:20.090 --> 00:06:20.100 align:start position:0%
set up a query engine
 

00:06:20.100 --> 00:06:21.830 align:start position:0%
set up a query engine
pretty<00:06:20.880><c> basic</c>

00:06:21.830 --> 00:06:21.840 align:start position:0%
pretty basic
 

00:06:21.840 --> 00:06:23.629 align:start position:0%
pretty basic
then<00:06:22.319><c> the</c><00:06:22.440><c> next</c><00:06:22.560><c> step</c><00:06:22.740><c> here</c><00:06:22.919><c> is</c><00:06:23.100><c> I'm</c><00:06:23.340><c> going</c><00:06:23.520><c> to</c>

00:06:23.629 --> 00:06:23.639 align:start position:0%
then the next step here is I'm going to
 

00:06:23.639 --> 00:06:26.029 align:start position:0%
then the next step here is I'm going to
print<00:06:24.180><c> out</c><00:06:24.479><c> the</c><00:06:25.139><c> text</c><00:06:25.440><c> that</c><00:06:25.680><c> was</c><00:06:25.860><c> actually</c>

00:06:26.029 --> 00:06:26.039 align:start position:0%
print out the text that was actually
 

00:06:26.039 --> 00:06:27.950 align:start position:0%
print out the text that was actually
received<00:06:26.520><c> from</c><00:06:26.759><c> these</c><00:06:27.000><c> nodes</c>

00:06:27.950 --> 00:06:27.960 align:start position:0%
received from these nodes
 

00:06:27.960 --> 00:06:29.809 align:start position:0%
received from these nodes
and<00:06:28.440><c> you'll</c><00:06:28.919><c> see</c><00:06:29.039><c> that</c><00:06:29.160><c> it's</c><00:06:29.340><c> actually</c><00:06:29.520><c> a</c><00:06:29.759><c> lot</c>

00:06:29.809 --> 00:06:29.819 align:start position:0%
and you'll see that it's actually a lot
 

00:06:29.819 --> 00:06:31.070 align:start position:0%
and you'll see that it's actually a lot
of<00:06:29.940><c> text</c><00:06:30.120><c> that's</c><00:06:30.360><c> being</c><00:06:30.539><c> retrieved</c><00:06:30.960><c> because</c>

00:06:31.070 --> 00:06:31.080 align:start position:0%
of text that's being retrieved because
 

00:06:31.080 --> 00:06:33.050 align:start position:0%
of text that's being retrieved because
we<00:06:31.319><c> had</c><00:06:31.380><c> to</c><00:06:31.500><c> set</c><00:06:31.680><c> the</c><00:06:31.860><c> top</c><00:06:31.979><c> k</c><00:06:32.220><c> a</c><00:06:32.819><c> little</c><00:06:32.940><c> bit</c>

00:06:33.050 --> 00:06:33.060 align:start position:0%
we had to set the top k a little bit
 

00:06:33.060 --> 00:06:34.430 align:start position:0%
we had to set the top k a little bit
high<00:06:33.240><c> in</c><00:06:33.600><c> order</c><00:06:33.720><c> for</c><00:06:33.960><c> the</c><00:06:34.080><c> hierarchical</c>

00:06:34.430 --> 00:06:34.440 align:start position:0%
high in order for the hierarchical
 

00:06:34.440 --> 00:06:36.050 align:start position:0%
high in order for the hierarchical
retriever<00:06:34.919><c> to</c><00:06:35.100><c> work</c><00:06:35.280><c> well</c>

00:06:36.050 --> 00:06:36.060 align:start position:0%
retriever to work well
 

00:06:36.060 --> 00:06:37.790 align:start position:0%
retriever to work well
so<00:06:36.419><c> here</c><00:06:36.600><c> at</c><00:06:36.720><c> the</c><00:06:36.840><c> end</c><00:06:36.900><c> I</c><00:06:37.139><c> print</c><00:06:37.319><c> also</c><00:06:37.620><c> the</c>

00:06:37.790 --> 00:06:37.800 align:start position:0%
so here at the end I print also the
 

00:06:37.800 --> 00:06:40.430 align:start position:0%
so here at the end I print also the
total<00:06:38.039><c> length</c><00:06:38.340><c> of</c><00:06:38.520><c> the</c><00:06:38.639><c> text</c><00:06:38.759><c> received</c>

00:06:40.430 --> 00:06:40.440 align:start position:0%
total length of the text received
 

00:06:40.440 --> 00:06:42.710 align:start position:0%
total length of the text received
and<00:06:40.800><c> so</c><00:06:40.919><c> here</c><00:06:41.160><c> the</c><00:06:41.460><c> hypical</c><00:06:41.880><c> retriever</c>

00:06:42.710 --> 00:06:42.720 align:start position:0%
and so here the hypical retriever
 

00:06:42.720 --> 00:06:45.230 align:start position:0%
and so here the hypical retriever
retrieves<00:06:43.259><c> a</c><00:06:43.500><c> lot</c><00:06:43.560><c> of</c><00:06:43.680><c> text</c><00:06:43.880><c> that</c><00:06:44.880><c> can</c><00:06:45.000><c> help</c><00:06:45.120><c> us</c>

00:06:45.230 --> 00:06:45.240 align:start position:0%
retrieves a lot of text that can help us
 

00:06:45.240 --> 00:06:47.390 align:start position:0%
retrieves a lot of text that can help us
answer<00:06:45.419><c> that</c><00:06:45.720><c> question</c><00:06:45.960><c> but</c><00:06:46.919><c> the</c><00:06:47.160><c> total</c>

00:06:47.390 --> 00:06:47.400 align:start position:0%
answer that question but the total
 

00:06:47.400 --> 00:06:49.249 align:start position:0%
answer that question but the total
length<00:06:47.639><c> is</c><00:06:47.759><c> 6000</c><00:06:48.300><c> tokens</c><00:06:48.600><c> which</c><00:06:48.840><c> is</c><00:06:49.020><c> going</c><00:06:49.139><c> to</c>

00:06:49.249 --> 00:06:49.259 align:start position:0%
length is 6000 tokens which is going to
 

00:06:49.259 --> 00:06:51.650 align:start position:0%
length is 6000 tokens which is going to
involve<00:06:49.740><c> several</c><00:06:50.280><c> llm</c><00:06:50.759><c> calls</c><00:06:51.120><c> if</c><00:06:51.360><c> we</c><00:06:51.479><c> actually</c>

00:06:51.650 --> 00:06:51.660 align:start position:0%
involve several llm calls if we actually
 

00:06:51.660 --> 00:06:53.570 align:start position:0%
involve several llm calls if we actually
use<00:06:51.960><c> these</c><00:06:52.259><c> nodes</c>

00:06:53.570 --> 00:06:53.580 align:start position:0%
use these nodes
 

00:06:53.580 --> 00:06:56.210 align:start position:0%
use these nodes
whereas<00:06:54.180><c> the</c><00:06:54.300><c> base</c><00:06:54.419><c> retriever</c><00:06:55.220><c> also</c>

00:06:56.210 --> 00:06:56.220 align:start position:0%
whereas the base retriever also
 

00:06:56.220 --> 00:06:58.309 align:start position:0%
whereas the base retriever also
retrieves<00:06:56.580><c> a</c><00:06:56.759><c> lot</c><00:06:56.819><c> of</c><00:06:56.940><c> text</c><00:06:57.060><c> but</c><00:06:57.960><c> quite</c><00:06:58.139><c> a</c><00:06:58.199><c> bit</c>

00:06:58.309 --> 00:06:58.319 align:start position:0%
retrieves a lot of text but quite a bit
 

00:06:58.319 --> 00:07:00.890 align:start position:0%
retrieves a lot of text but quite a bit
shorter<00:06:58.620><c> this</c><00:06:58.800><c> time</c><00:06:58.919><c> only</c><00:06:59.220><c> 1400.</c>

00:07:00.890 --> 00:07:00.900 align:start position:0%
shorter this time only 1400.
 

00:07:00.900 --> 00:07:02.990 align:start position:0%
shorter this time only 1400.
and<00:07:01.319><c> this</c><00:07:01.500><c> is</c><00:07:01.560><c> because</c><00:07:01.800><c> the</c><00:07:02.759><c> kind</c><00:07:02.940><c> of</c>

00:07:02.990 --> 00:07:03.000 align:start position:0%
and this is because the kind of
 

00:07:03.000 --> 00:07:04.610 align:start position:0%
and this is because the kind of
hierarchical<00:07:03.300><c> retriever</c><00:07:03.840><c> is</c><00:07:04.199><c> you</c><00:07:04.560><c> know</c>

00:07:04.610 --> 00:07:04.620 align:start position:0%
hierarchical retriever is you know
 

00:07:04.620 --> 00:07:06.110 align:start position:0%
hierarchical retriever is you know
retrieving

00:07:06.110 --> 00:07:06.120 align:start position:0%
retrieving
 

00:07:06.120 --> 00:07:07.790 align:start position:0%
retrieving
all<00:07:06.600><c> these</c><00:07:06.780><c> children</c><00:07:06.900><c> nodes</c><00:07:07.319><c> it</c><00:07:07.560><c> might</c><00:07:07.680><c> be</c>

00:07:07.790 --> 00:07:07.800 align:start position:0%
all these children nodes it might be
 

00:07:07.800 --> 00:07:10.670 align:start position:0%
all these children nodes it might be
merging<00:07:08.100><c> up</c><00:07:08.280><c> to</c><00:07:08.520><c> parent</c><00:07:08.699><c> nodes</c><00:07:09.380><c> and</c><00:07:10.380><c> there's</c><00:07:10.500><c> a</c>

00:07:10.670 --> 00:07:10.680 align:start position:0%
merging up to parent nodes and there's a
 

00:07:10.680 --> 00:07:14.210 align:start position:0%
merging up to parent nodes and there's a
lot<00:07:10.740><c> of</c><00:07:10.860><c> text</c><00:07:10.979><c> involved</c><00:07:11.520><c> in</c><00:07:11.699><c> this</c><00:07:11.819><c> process</c>

00:07:14.210 --> 00:07:14.220 align:start position:0%
 
 

00:07:14.220 --> 00:07:16.129 align:start position:0%
 
now<00:07:14.759><c> a</c><00:07:14.940><c> problem</c><00:07:15.180><c> here</c><00:07:15.419><c> like</c><00:07:15.600><c> I</c><00:07:15.720><c> mentioned</c><00:07:15.960><c> is</c>

00:07:16.129 --> 00:07:16.139 align:start position:0%
now a problem here like I mentioned is
 

00:07:16.139 --> 00:07:18.050 align:start position:0%
now a problem here like I mentioned is
that<00:07:16.380><c> the</c><00:07:16.979><c> hierarchical</c><00:07:17.340><c> retriever</c><00:07:17.819><c> might</c>

00:07:18.050 --> 00:07:18.060 align:start position:0%
that the hierarchical retriever might
 

00:07:18.060 --> 00:07:20.150 align:start position:0%
that the hierarchical retriever might
involve<00:07:18.360><c> more</c><00:07:18.539><c> llm</c><00:07:19.020><c> calls</c><00:07:19.380><c> at</c><00:07:19.620><c> the</c><00:07:19.680><c> query</c><00:07:19.860><c> time</c>

00:07:20.150 --> 00:07:20.160 align:start position:0%
involve more llm calls at the query time
 

00:07:20.160 --> 00:07:23.629 align:start position:0%
involve more llm calls at the query time
in<00:07:20.580><c> order</c><00:07:20.699><c> to</c><00:07:21.120><c> get</c><00:07:21.840><c> the</c><00:07:22.020><c> OM</c><00:07:22.199><c> to</c><00:07:22.620><c> read</c><00:07:22.800><c> all</c><00:07:23.280><c> those</c>

00:07:23.629 --> 00:07:23.639 align:start position:0%
in order to get the OM to read all those
 

00:07:23.639 --> 00:07:25.670 align:start position:0%
in order to get the OM to read all those
nodes<00:07:24.539><c> that</c><00:07:24.780><c> are</c><00:07:24.900><c> retrieved</c><00:07:25.259><c> and</c><00:07:25.380><c> all</c><00:07:25.560><c> that</c>

00:07:25.670 --> 00:07:25.680 align:start position:0%
nodes that are retrieved and all that
 

00:07:25.680 --> 00:07:28.490 align:start position:0%
nodes that are retrieved and all that
text<00:07:25.919><c> as</c><00:07:26.639><c> we</c><00:07:26.759><c> saw</c><00:07:26.880><c> above</c><00:07:27.180><c> it</c><00:07:27.300><c> was</c><00:07:27.539><c> almost</c><00:07:27.900><c> 7000</c>

00:07:28.490 --> 00:07:28.500 align:start position:0%
text as we saw above it was almost 7000
 

00:07:28.500 --> 00:07:30.290 align:start position:0%
text as we saw above it was almost 7000
tokens<00:07:28.860><c> whereas</c><00:07:29.280><c> the</c><00:07:29.400><c> base</c><00:07:29.520><c> retriever</c><00:07:30.060><c> is</c>

00:07:30.290 --> 00:07:30.300 align:start position:0%
tokens whereas the base retriever is
 

00:07:30.300 --> 00:07:33.650 align:start position:0%
tokens whereas the base retriever is
only<00:07:30.419><c> retrieving</c><00:07:30.960><c> about</c><00:07:31.500><c> 1400</c><00:07:32.099><c> tokens</c>

00:07:33.650 --> 00:07:33.660 align:start position:0%
only retrieving about 1400 tokens
 

00:07:33.660 --> 00:07:35.749 align:start position:0%
only retrieving about 1400 tokens
so<00:07:34.080><c> in</c><00:07:34.199><c> this</c><00:07:34.319><c> section</c><00:07:34.500><c> here</c><00:07:34.800><c> we</c><00:07:35.280><c> basically</c>

00:07:35.749 --> 00:07:35.759 align:start position:0%
so in this section here we basically
 

00:07:35.759 --> 00:07:37.749 align:start position:0%
so in this section here we basically
develop<00:07:36.120><c> a</c><00:07:36.300><c> solution</c><00:07:36.599><c> to</c><00:07:36.840><c> our</c><00:07:36.960><c> token</c><00:07:37.319><c> problem</c>

00:07:37.749 --> 00:07:37.759 align:start position:0%
develop a solution to our token problem
 

00:07:37.759 --> 00:07:39.890 align:start position:0%
develop a solution to our token problem
uh<00:07:38.759><c> where</c><00:07:39.120><c> we</c><00:07:39.360><c> still</c><00:07:39.479><c> want</c><00:07:39.599><c> to</c><00:07:39.720><c> use</c><00:07:39.780><c> the</c>

00:07:39.890 --> 00:07:39.900 align:start position:0%
uh where we still want to use the
 

00:07:39.900 --> 00:07:41.390 align:start position:0%
uh where we still want to use the
hierarchical<00:07:40.259><c> retriever</c><00:07:40.740><c> but</c><00:07:40.919><c> we</c><00:07:41.099><c> also</c><00:07:41.280><c> want</c>

00:07:41.390 --> 00:07:41.400 align:start position:0%
hierarchical retriever but we also want
 

00:07:41.400 --> 00:07:43.490 align:start position:0%
hierarchical retriever but we also want
to<00:07:41.580><c> limit</c><00:07:41.819><c> how</c><00:07:42.660><c> much</c><00:07:42.780><c> tokens</c><00:07:43.139><c> are</c><00:07:43.319><c> actually</c>

00:07:43.490 --> 00:07:43.500 align:start position:0%
to limit how much tokens are actually
 

00:07:43.500 --> 00:07:46.430 align:start position:0%
to limit how much tokens are actually
being<00:07:43.800><c> used</c><00:07:44.160><c> to</c><00:07:44.460><c> synthesize</c><00:07:44.940><c> the</c><00:07:45.180><c> response</c><00:07:45.660><c> so</c>

00:07:46.430 --> 00:07:46.440 align:start position:0%
being used to synthesize the response so
 

00:07:46.440 --> 00:07:47.809 align:start position:0%
being used to synthesize the response so
for<00:07:46.560><c> this</c><00:07:46.680><c> we</c><00:07:46.860><c> can</c><00:07:46.979><c> Implement</c><00:07:47.280><c> a</c><00:07:47.460><c> custom</c><00:07:47.580><c> node</c>

00:07:47.809 --> 00:07:47.819 align:start position:0%
for this we can Implement a custom node
 

00:07:47.819 --> 00:07:50.990 align:start position:0%
for this we can Implement a custom node
post<00:07:48.060><c> processor</c><00:07:48.660><c> that</c><00:07:49.620><c> essentially</c><00:07:50.039><c> uses</c><00:07:50.819><c> a</c>

00:07:50.990 --> 00:07:51.000 align:start position:0%
post processor that essentially uses a
 

00:07:51.000 --> 00:07:52.670 align:start position:0%
post processor that essentially uses a
tokenizer<00:07:51.539><c> in</c><00:07:51.780><c> this</c><00:07:51.840><c> case</c><00:07:51.960><c> it's</c><00:07:52.199><c> using</c><00:07:52.500><c> the</c>

00:07:52.670 --> 00:07:52.680 align:start position:0%
tokenizer in this case it's using the
 

00:07:52.680 --> 00:07:54.650 align:start position:0%
tokenizer in this case it's using the
default<00:07:52.979><c> tick</c><00:07:53.400><c> token</c><00:07:53.699><c> tokenizer</c><00:07:54.240><c> and</c><00:07:54.360><c> Lum</c>

00:07:54.650 --> 00:07:54.660 align:start position:0%
default tick token tokenizer and Lum
 

00:07:54.660 --> 00:07:56.749 align:start position:0%
default tick token tokenizer and Lum
index<00:07:55.020><c> but</c><00:07:55.800><c> you</c><00:07:55.979><c> could</c><00:07:56.099><c> pass</c><00:07:56.220><c> in</c><00:07:56.340><c> whatever</c><00:07:56.520><c> you</c>

00:07:56.749 --> 00:07:56.759 align:start position:0%
index but you could pass in whatever you
 

00:07:56.759 --> 00:07:59.749 align:start position:0%
index but you could pass in whatever you
wanted<00:07:57.440><c> it</c><00:07:58.440><c> counts</c><00:07:58.680><c> the</c><00:07:58.800><c> tokens</c><00:07:59.160><c> and</c><00:07:59.520><c> every</c>

00:07:59.749 --> 00:07:59.759 align:start position:0%
wanted it counts the tokens and every
 

00:07:59.759 --> 00:08:02.210 align:start position:0%
wanted it counts the tokens and every
node<00:08:00.180><c> that</c><00:08:00.479><c> was</c><00:08:00.599><c> returned</c><00:08:00.900><c> by</c><00:08:01.080><c> the</c><00:08:01.199><c> retriever</c>

00:08:02.210 --> 00:08:02.220 align:start position:0%
node that was returned by the retriever
 

00:08:02.220 --> 00:08:04.909 align:start position:0%
node that was returned by the retriever
and<00:08:02.940><c> Returns</c><00:08:03.539><c> the</c><00:08:03.780><c> subset</c><00:08:04.139><c> of</c><00:08:04.319><c> nodes</c><00:08:04.560><c> that</c>

00:08:04.909 --> 00:08:04.919 align:start position:0%
and Returns the subset of nodes that
 

00:08:04.919 --> 00:08:07.010 align:start position:0%
and Returns the subset of nodes that
don't<00:08:05.099><c> go</c><00:08:05.340><c> over</c><00:08:05.520><c> the</c><00:08:05.819><c> limit</c><00:08:05.940><c> that</c><00:08:06.240><c> we</c><00:08:06.360><c> set</c><00:08:06.539><c> so</c>

00:08:07.010 --> 00:08:07.020 align:start position:0%
don't go over the limit that we set so
 

00:08:07.020 --> 00:08:08.689 align:start position:0%
don't go over the limit that we set so
in<00:08:07.139><c> this</c><00:08:07.259><c> case</c><00:08:07.380><c> the</c><00:08:07.680><c> default</c><00:08:07.979><c> limits</c><00:08:08.340><c> 2000</c>

00:08:08.689 --> 00:08:08.699 align:start position:0%
in this case the default limits 2000
 

00:08:08.699 --> 00:08:11.089 align:start position:0%
in this case the default limits 2000
tokens<00:08:09.120><c> we</c><00:08:09.900><c> count</c><00:08:10.080><c> the</c><00:08:10.259><c> nodes</c><00:08:10.440><c> and</c><00:08:10.800><c> we</c><00:08:10.919><c> stop</c>

00:08:11.089 --> 00:08:11.099 align:start position:0%
tokens we count the nodes and we stop
 

00:08:11.099 --> 00:08:13.670 align:start position:0%
tokens we count the nodes and we stop
adding<00:08:11.460><c> nodes</c><00:08:11.759><c> once</c><00:08:12.360><c> we</c><00:08:12.660><c> reach</c><00:08:13.139><c> or</c><00:08:13.319><c> exceed</c>

00:08:13.670 --> 00:08:13.680 align:start position:0%
adding nodes once we reach or exceed
 

00:08:13.680 --> 00:08:16.610 align:start position:0%
adding nodes once we reach or exceed
that<00:08:13.860><c> limit</c>

00:08:16.610 --> 00:08:16.620 align:start position:0%
 
 

00:08:16.620 --> 00:08:18.469 align:start position:0%
 
so<00:08:16.979><c> we</c><00:08:17.099><c> can</c><00:08:17.220><c> create</c><00:08:17.400><c> our</c><00:08:17.699><c> query</c><00:08:17.940><c> engine</c><00:08:18.180><c> again</c>

00:08:18.469 --> 00:08:18.479 align:start position:0%
so we can create our query engine again
 

00:08:18.479 --> 00:08:20.450 align:start position:0%
so we can create our query engine again
and<00:08:18.840><c> this</c><00:08:18.960><c> time</c><00:08:19.139><c> pass</c><00:08:19.379><c> in</c><00:08:19.680><c> our</c><00:08:20.039><c> post</c>

00:08:20.450 --> 00:08:20.460 align:start position:0%
and this time pass in our post
 

00:08:20.460 --> 00:08:22.730 align:start position:0%
and this time pass in our post
processors<00:08:21.060><c> here</c><00:08:21.360><c> which</c><00:08:22.199><c> is</c><00:08:22.319><c> they're</c><00:08:22.500><c> just</c>

00:08:22.730 --> 00:08:22.740 align:start position:0%
processors here which is they're just
 

00:08:22.740 --> 00:08:26.330 align:start position:0%
processors here which is they're just
single<00:08:23.160><c> limit</c><00:08:23.879><c> the</c><00:08:24.240><c> length</c><00:08:24.660><c> post</c><00:08:25.259><c> processor</c>

00:08:26.330 --> 00:08:26.340 align:start position:0%
single limit the length post processor
 

00:08:26.340 --> 00:08:28.309 align:start position:0%
single limit the length post processor
we<00:08:26.699><c> send</c><00:08:26.819><c> our</c><00:08:27.000><c> query</c><00:08:27.360><c> again</c><00:08:27.660><c> and</c><00:08:27.960><c> this</c><00:08:28.080><c> time</c><00:08:28.199><c> we</c>

00:08:28.309 --> 00:08:28.319 align:start position:0%
we send our query again and this time we
 

00:08:28.319 --> 00:08:30.050 align:start position:0%
we send our query again and this time we
will<00:08:28.440><c> see</c><00:08:28.620><c> that</c><00:08:28.740><c> the</c><00:08:29.039><c> actual</c><00:08:29.340><c> retrieved</c>

00:08:30.050 --> 00:08:30.060 align:start position:0%
will see that the actual retrieved
 

00:08:30.060 --> 00:08:32.690 align:start position:0%
will see that the actual retrieved
length<00:08:30.360><c> of</c><00:08:30.479><c> nodes</c><00:08:30.720><c> is</c><00:08:31.620><c> indeed</c><00:08:32.279><c> shorter</c><00:08:32.580><c> than</c>

00:08:32.690 --> 00:08:32.700 align:start position:0%
length of nodes is indeed shorter than
 

00:08:32.700 --> 00:08:35.570 align:start position:0%
length of nodes is indeed shorter than
three<00:08:32.940><c> thousand</c><00:08:33.300><c> so</c><00:08:33.839><c> now</c><00:08:34.140><c> we</c><00:08:34.380><c> have</c><00:08:34.500><c> 2</c><00:08:34.800><c> 300</c>

00:08:35.570 --> 00:08:35.580 align:start position:0%
three thousand so now we have 2 300
 

00:08:35.580 --> 00:08:38.750 align:start position:0%
three thousand so now we have 2 300
tokens<00:08:36.479><c> returned</c><00:08:37.039><c> which</c><00:08:38.039><c> will</c><00:08:38.279><c> help</c><00:08:38.520><c> our</c>

00:08:38.750 --> 00:08:38.760 align:start position:0%
tokens returned which will help our
 

00:08:38.760 --> 00:08:40.550 align:start position:0%
tokens returned which will help our
queries<00:08:39.060><c> speed</c><00:08:39.300><c> up</c><00:08:39.479><c> quite</c><00:08:39.659><c> a</c><00:08:39.779><c> bit</c><00:08:39.899><c> while</c><00:08:40.320><c> still</c>

00:08:40.550 --> 00:08:40.560 align:start position:0%
queries speed up quite a bit while still
 

00:08:40.560 --> 00:08:42.829 align:start position:0%
queries speed up quite a bit while still
keeping<00:08:40.979><c> the</c><00:08:41.339><c> most</c><00:08:41.399><c> relevant</c><00:08:41.820><c> text</c>

00:08:42.829 --> 00:08:42.839 align:start position:0%
keeping the most relevant text
 

00:08:42.839 --> 00:08:44.750 align:start position:0%
keeping the most relevant text
and<00:08:43.500><c> we</c><00:08:43.620><c> know</c><00:08:43.740><c> it's</c><00:08:43.919><c> the</c><00:08:44.099><c> most</c><00:08:44.219><c> relevant</c><00:08:44.520><c> text</c>

00:08:44.750 --> 00:08:44.760 align:start position:0%
and we know it's the most relevant text
 

00:08:44.760 --> 00:08:47.870 align:start position:0%
and we know it's the most relevant text
because<00:08:45.380><c> the</c><00:08:46.380><c> nodes</c><00:08:46.560><c> in</c><00:08:47.100><c> the</c><00:08:47.220><c> post</c><00:08:47.279><c> processor</c>

00:08:47.870 --> 00:08:47.880 align:start position:0%
because the nodes in the post processor
 

00:08:47.880 --> 00:08:50.389 align:start position:0%
because the nodes in the post processor
are<00:08:48.180><c> ordered</c><00:08:48.600><c> by</c><00:08:48.779><c> similarity</c><00:08:49.380><c> so</c><00:08:49.800><c> we've</c><00:08:50.160><c> kept</c>

00:08:50.389 --> 00:08:50.399 align:start position:0%
are ordered by similarity so we've kept
 

00:08:50.399 --> 00:08:53.389 align:start position:0%
are ordered by similarity so we've kept
the<00:08:50.580><c> most</c><00:08:50.760><c> similar</c><00:08:51.300><c> ones</c><00:08:51.600><c> and</c><00:08:51.899><c> cut</c><00:08:52.080><c> it</c><00:08:52.260><c> off</c><00:08:52.399><c> at</c>

00:08:53.389 --> 00:08:53.399 align:start position:0%
the most similar ones and cut it off at
 

00:08:53.399 --> 00:08:56.030 align:start position:0%
the most similar ones and cut it off at
a<00:08:53.519><c> certain</c><00:08:53.820><c> token</c><00:08:54.240><c> length</c>

00:08:56.030 --> 00:08:56.040 align:start position:0%
a certain token length
 

00:08:56.040 --> 00:08:58.670 align:start position:0%
a certain token length
lastly<00:08:56.880><c> we</c><00:08:57.180><c> can</c><00:08:57.240><c> set</c><00:08:57.420><c> up</c><00:08:57.600><c> our</c><00:08:58.080><c> final</c><00:08:58.260><c> query</c>

00:08:58.670 --> 00:08:58.680 align:start position:0%
lastly we can set up our final query
 

00:08:58.680 --> 00:08:59.990 align:start position:0%
lastly we can set up our final query
engine

00:08:59.990 --> 00:09:00.000 align:start position:0%
engine
 

00:09:00.000 --> 00:09:02.750 align:start position:0%
engine
so<00:09:00.959><c> like</c><00:09:01.260><c> I</c><00:09:01.380><c> said</c><00:09:01.500><c> before</c><00:09:01.740><c> we're</c><00:09:02.220><c> setting</c><00:09:02.519><c> up</c><00:09:02.580><c> a</c>

00:09:02.750 --> 00:09:02.760 align:start position:0%
so like I said before we're setting up a
 

00:09:02.760 --> 00:09:05.389 align:start position:0%
so like I said before we're setting up a
sort<00:09:02.880><c> of</c><00:09:03.000><c> query</c><00:09:03.240><c> engine</c><00:09:03.420><c> tool</c><00:09:03.959><c> for</c><00:09:04.320><c> each</c><00:09:04.680><c> sub</c>

00:09:05.389 --> 00:09:05.399 align:start position:0%
sort of query engine tool for each sub
 

00:09:05.399 --> 00:09:07.610 align:start position:0%
sort of query engine tool for each sub
index<00:09:05.880><c> so</c><00:09:06.779><c> we</c><00:09:06.899><c> have</c><00:09:07.019><c> stuff</c><00:09:07.140><c> about</c><00:09:07.380><c> the</c>

00:09:07.610 --> 00:09:07.620 align:start position:0%
index so we have stuff about the
 

00:09:07.620 --> 00:09:10.490 align:start position:0%
index so we have stuff about the
community<00:09:07.800><c> stuff</c><00:09:08.279><c> about</c><00:09:08.519><c> agents</c><00:09:09.320><c> stuff</c><00:09:10.320><c> about</c>

00:09:10.490 --> 00:09:10.500 align:start position:0%
community stuff about agents stuff about
 

00:09:10.500 --> 00:09:12.350 align:start position:0%
community stuff about agents stuff about
getting<00:09:10.800><c> started</c>

00:09:12.350 --> 00:09:12.360 align:start position:0%
getting started
 

00:09:12.360 --> 00:09:14.509 align:start position:0%
getting started
we<00:09:12.660><c> can</c><00:09:12.839><c> load</c><00:09:13.140><c> all</c><00:09:13.320><c> these</c><00:09:13.560><c> and</c><00:09:13.920><c> load</c><00:09:14.279><c> them</c><00:09:14.399><c> into</c>

00:09:14.509 --> 00:09:14.519 align:start position:0%
we can load all these and load them into
 

00:09:14.519 --> 00:09:16.790 align:start position:0%
we can load all these and load them into
query<00:09:14.820><c> engine</c><00:09:15.000><c> tools</c><00:09:15.540><c> passing</c><00:09:16.260><c> along</c><00:09:16.380><c> our</c>

00:09:16.790 --> 00:09:16.800 align:start position:0%
query engine tools passing along our
 

00:09:16.800 --> 00:09:19.730 align:start position:0%
query engine tools passing along our
post<00:09:17.040><c> processor</c><00:09:17.720><c> and</c><00:09:18.720><c> then</c><00:09:18.779><c> we</c><00:09:19.019><c> can</c><00:09:19.140><c> build</c><00:09:19.320><c> a</c>

00:09:19.730 --> 00:09:19.740 align:start position:0%
post processor and then we can build a
 

00:09:19.740 --> 00:09:22.310 align:start position:0%
post processor and then we can build a
router<00:09:20.100><c> query</c><00:09:20.339><c> engine</c><00:09:20.519><c> on</c><00:09:20.880><c> top</c><00:09:21.060><c> I</c><00:09:21.899><c> think</c><00:09:22.019><c> in</c><00:09:22.200><c> a</c>

00:09:22.310 --> 00:09:22.320 align:start position:0%
router query engine on top I think in a
 

00:09:22.320 --> 00:09:23.990 align:start position:0%
router query engine on top I think in a
previous<00:09:22.440><c> episode</c><00:09:22.800><c> I</c><00:09:23.160><c> was</c><00:09:23.279><c> using</c><00:09:23.519><c> a</c><00:09:23.820><c> sub</c>

00:09:23.990 --> 00:09:24.000 align:start position:0%
previous episode I was using a sub
 

00:09:24.000 --> 00:09:26.630 align:start position:0%
previous episode I was using a sub
question<00:09:24.240><c> query</c><00:09:25.080><c> engine</c><00:09:25.320><c> I</c><00:09:25.980><c> found</c><00:09:26.160><c> it</c><00:09:26.339><c> quite</c><00:09:26.519><c> a</c>

00:09:26.630 --> 00:09:26.640 align:start position:0%
question query engine I found it quite a
 

00:09:26.640 --> 00:09:29.750 align:start position:0%
question query engine I found it quite a
bit<00:09:26.760><c> slower</c><00:09:27.540><c> than</c><00:09:27.779><c> what</c><00:09:27.959><c> I</c><00:09:28.140><c> wanted</c><00:09:28.700><c> and</c><00:09:29.700><c> I</c>

00:09:29.750 --> 00:09:29.760 align:start position:0%
bit slower than what I wanted and I
 

00:09:29.760 --> 00:09:31.070 align:start position:0%
bit slower than what I wanted and I
found<00:09:29.880><c> this</c><00:09:30.060><c> router</c><00:09:30.420><c> query</c><00:09:30.600><c> engine</c><00:09:30.779><c> to</c><00:09:31.019><c> be</c>

00:09:31.070 --> 00:09:31.080 align:start position:0%
found this router query engine to be
 

00:09:31.080 --> 00:09:33.110 align:start position:0%
found this router query engine to be
quite<00:09:31.260><c> a</c><00:09:31.380><c> bit</c><00:09:31.440><c> faster</c><00:09:31.800><c> and</c><00:09:32.339><c> it</c><00:09:32.640><c> almost</c><00:09:32.760><c> felt</c>

00:09:33.110 --> 00:09:33.120 align:start position:0%
quite a bit faster and it almost felt
 

00:09:33.120 --> 00:09:34.389 align:start position:0%
quite a bit faster and it almost felt
more<00:09:33.300><c> accurate</c>

00:09:34.389 --> 00:09:34.399 align:start position:0%
more accurate
 

00:09:34.399 --> 00:09:37.009 align:start position:0%
more accurate
the<00:09:35.399><c> difference</c><00:09:35.880><c> here</c><00:09:36.180><c> being</c><00:09:36.360><c> is</c><00:09:36.600><c> that</c><00:09:36.779><c> the</c>

00:09:37.009 --> 00:09:37.019 align:start position:0%
the difference here being is that the
 

00:09:37.019 --> 00:09:39.290 align:start position:0%
the difference here being is that the
router<00:09:37.260><c> query</c><00:09:37.500><c> engine</c><00:09:37.680><c> is</c><00:09:37.980><c> not</c><00:09:38.300><c> rewriting</c>

00:09:39.290 --> 00:09:39.300 align:start position:0%
router query engine is not rewriting
 

00:09:39.300 --> 00:09:41.930 align:start position:0%
router query engine is not rewriting
your<00:09:39.540><c> query</c><00:09:39.839><c> it's</c><00:09:40.260><c> just</c><00:09:40.620><c> copying</c><00:09:41.519><c> your</c><00:09:41.640><c> query</c>

00:09:41.930 --> 00:09:41.940 align:start position:0%
your query it's just copying your query
 

00:09:41.940 --> 00:09:44.090 align:start position:0%
your query it's just copying your query
and<00:09:42.240><c> sending</c><00:09:42.600><c> it</c><00:09:42.720><c> to</c><00:09:42.839><c> the</c><00:09:43.080><c> indexes</c><00:09:43.560><c> it</c><00:09:43.740><c> thinks</c>

00:09:44.090 --> 00:09:44.100 align:start position:0%
and sending it to the indexes it thinks
 

00:09:44.100 --> 00:09:45.829 align:start position:0%
and sending it to the indexes it thinks
are<00:09:44.399><c> most</c><00:09:44.820><c> relevant</c>

00:09:45.829 --> 00:09:45.839 align:start position:0%
are most relevant
 

00:09:45.839 --> 00:09:48.290 align:start position:0%
are most relevant
since<00:09:46.320><c> I</c><00:09:46.500><c> have</c><00:09:46.560><c> select</c><00:09:46.920><c> multi</c><00:09:47.279><c> equals</c><00:09:47.700><c> true</c><00:09:47.940><c> it</c>

00:09:48.290 --> 00:09:48.300 align:start position:0%
since I have select multi equals true it
 

00:09:48.300 --> 00:09:49.610 align:start position:0%
since I have select multi equals true it
could<00:09:48.420><c> send</c><00:09:48.600><c> it</c><00:09:48.779><c> to</c><00:09:48.899><c> any</c><00:09:49.200><c> number</c><00:09:49.440><c> of</c>

00:09:49.610 --> 00:09:49.620 align:start position:0%
could send it to any number of
 

00:09:49.620 --> 00:09:51.949 align:start position:0%
could send it to any number of
sub-indexes<00:09:50.820><c> then</c><00:09:51.180><c> the</c><00:09:51.360><c> responses</c><00:09:51.720><c> from</c>

00:09:51.949 --> 00:09:51.959 align:start position:0%
sub-indexes then the responses from
 

00:09:51.959 --> 00:09:55.670 align:start position:0%
sub-indexes then the responses from
those<00:09:52.080><c> indexes</c><00:09:52.560><c> are</c><00:09:52.680><c> aggregated</c>

00:09:55.670 --> 00:09:55.680 align:start position:0%
 
 

00:09:55.680 --> 00:09:57.110 align:start position:0%
 
so<00:09:55.920><c> we're</c><00:09:56.040><c> going</c><00:09:56.160><c> to</c><00:09:56.220><c> set</c><00:09:56.339><c> that</c><00:09:56.580><c> up</c><00:09:56.700><c> it's</c><00:09:56.940><c> going</c>

00:09:57.110 --> 00:09:57.120 align:start position:0%
so we're going to set that up it's going
 

00:09:57.120 --> 00:09:59.930 align:start position:0%
so we're going to set that up it's going
to<00:09:57.180><c> take</c><00:09:57.300><c> a</c><00:09:57.480><c> second</c><00:09:57.600><c> because</c><00:09:58.080><c> I</c><00:09:58.680><c> am</c><00:09:58.920><c> setting</c><00:09:59.820><c> up</c>

00:09:59.930 --> 00:09:59.940 align:start position:0%
to take a second because I am setting up
 

00:09:59.940 --> 00:10:02.329 align:start position:0%
to take a second because I am setting up
a<00:10:00.420><c> lot</c><00:10:00.540><c> of</c><00:10:00.660><c> indexes</c><00:10:01.140><c> and</c><00:10:01.620><c> building</c><00:10:01.860><c> them</c><00:10:02.040><c> on</c><00:10:02.160><c> my</c>

00:10:02.329 --> 00:10:02.339 align:start position:0%
a lot of indexes and building them on my
 

00:10:02.339 --> 00:10:04.250 align:start position:0%
a lot of indexes and building them on my
local<00:10:02.459><c> machine</c><00:10:02.760><c> so</c><00:10:03.600><c> we'll</c><00:10:03.779><c> give</c><00:10:03.959><c> that</c><00:10:04.080><c> a</c>

00:10:04.250 --> 00:10:04.260 align:start position:0%
local machine so we'll give that a
 

00:10:04.260 --> 00:10:05.150 align:start position:0%
local machine so we'll give that a
second

00:10:05.150 --> 00:10:05.160 align:start position:0%
second
 

00:10:05.160 --> 00:10:07.850 align:start position:0%
second
okay<00:10:05.760><c> so</c><00:10:06.120><c> the</c><00:10:06.300><c> query</c><00:10:06.480><c> engine</c><00:10:06.779><c> is</c><00:10:07.200><c> now</c><00:10:07.440><c> set</c><00:10:07.740><c> up</c>

00:10:07.850 --> 00:10:07.860 align:start position:0%
okay so the query engine is now set up
 

00:10:07.860 --> 00:10:09.470 align:start position:0%
okay so the query engine is now set up
it's<00:10:08.040><c> ready</c><00:10:08.279><c> to</c><00:10:08.459><c> go</c><00:10:08.580><c> we</c><00:10:08.820><c> can</c><00:10:08.940><c> test</c><00:10:09.120><c> a</c><00:10:09.300><c> few</c>

00:10:09.470 --> 00:10:09.480 align:start position:0%
it's ready to go we can test a few
 

00:10:09.480 --> 00:10:11.509 align:start position:0%
it's ready to go we can test a few
queries<00:10:09.899><c> uh</c><00:10:10.620><c> so</c><00:10:10.800><c> I</c><00:10:10.920><c> have</c><00:10:10.980><c> kind</c><00:10:11.160><c> of</c><00:10:11.279><c> like</c><00:10:11.339><c> three</c>

00:10:11.509 --> 00:10:11.519 align:start position:0%
queries uh so I have kind of like three
 

00:10:11.519 --> 00:10:13.850 align:start position:0%
queries uh so I have kind of like three
queries<00:10:11.820><c> here</c><00:10:12.060><c> that</c><00:10:12.240><c> I</c><00:10:12.360><c> wanted</c><00:10:12.480><c> to</c><00:10:12.720><c> try</c><00:10:12.899><c> out</c><00:10:13.080><c> uh</c>

00:10:13.850 --> 00:10:13.860 align:start position:0%
queries here that I wanted to try out uh
 

00:10:13.860 --> 00:10:15.889 align:start position:0%
queries here that I wanted to try out uh
one<00:10:14.100><c> how</c><00:10:14.339><c> do</c><00:10:14.459><c> I</c><00:10:14.580><c> set</c><00:10:14.700><c> up</c><00:10:14.820><c> a</c><00:10:14.940><c> chroma</c><00:10:15.180><c> DB</c><00:10:15.540><c> Vector</c>

00:10:15.889 --> 00:10:15.899 align:start position:0%
one how do I set up a chroma DB Vector
 

00:10:15.899 --> 00:10:17.870 align:start position:0%
one how do I set up a chroma DB Vector
store<00:10:16.080><c> and</c><00:10:16.560><c> I</c><00:10:16.620><c> also</c><00:10:16.860><c> asked</c><00:10:17.100><c> it</c><00:10:17.160><c> to</c><00:10:17.339><c> give</c><00:10:17.399><c> me</c><00:10:17.580><c> a</c>

00:10:17.870 --> 00:10:17.880 align:start position:0%
store and I also asked it to give me a
 

00:10:17.880 --> 00:10:20.930 align:start position:0%
store and I also asked it to give me a
code<00:10:18.000><c> sample</c><00:10:19.019><c> let's</c><00:10:19.560><c> see</c><00:10:19.800><c> if</c><00:10:19.920><c> it</c><00:10:20.160><c> can</c><00:10:20.339><c> manage</c>

00:10:20.930 --> 00:10:20.940 align:start position:0%
code sample let's see if it can manage
 

00:10:20.940 --> 00:10:23.810 align:start position:0%
code sample let's see if it can manage
to<00:10:21.120><c> answer</c><00:10:21.300><c> that</c><00:10:21.600><c> question</c>

00:10:23.810 --> 00:10:23.820 align:start position:0%
to answer that question
 

00:10:23.820 --> 00:10:25.370 align:start position:0%
to answer that question
the<00:10:24.000><c> second</c><00:10:24.240><c> question</c><00:10:24.420><c> here</c><00:10:24.720><c> is</c><00:10:24.899><c> how</c><00:10:25.140><c> can</c><00:10:25.260><c> I</c>

00:10:25.370 --> 00:10:25.380 align:start position:0%
the second question here is how can I
 

00:10:25.380 --> 00:10:27.170 align:start position:0%
the second question here is how can I
customize<00:10:25.740><c> document</c><00:10:26.160><c> objects</c><00:10:26.640><c> and</c><00:10:26.880><c> the</c><00:10:27.060><c> last</c>

00:10:27.170 --> 00:10:27.180 align:start position:0%
customize document objects and the last
 

00:10:27.180 --> 00:10:29.150 align:start position:0%
customize document objects and the last
question<00:10:27.420><c> is</c><00:10:27.779><c> how</c><00:10:28.140><c> can</c><00:10:28.260><c> I</c><00:10:28.380><c> customize</c><00:10:28.740><c> document</c>

00:10:29.150 --> 00:10:29.160 align:start position:0%
question is how can I customize document
 

00:10:29.160 --> 00:10:31.730 align:start position:0%
question is how can I customize document
metadata<00:10:30.420><c> so</c><00:10:30.839><c> here</c><00:10:31.140><c> we</c><00:10:31.320><c> got</c><00:10:31.440><c> our</c><00:10:31.560><c> first</c>

00:10:31.730 --> 00:10:31.740 align:start position:0%
metadata so here we got our first
 

00:10:31.740 --> 00:10:33.350 align:start position:0%
metadata so here we got our first
response

00:10:33.350 --> 00:10:33.360 align:start position:0%
response
 

00:10:33.360 --> 00:10:34.670 align:start position:0%
response
um

00:10:34.670 --> 00:10:34.680 align:start position:0%
um
 

00:10:34.680 --> 00:10:37.190 align:start position:0%
um
indeed<00:10:35.519><c> this</c><00:10:35.700><c> is</c><00:10:35.820><c> how</c><00:10:36.000><c> you</c><00:10:36.120><c> set</c><00:10:36.360><c> up</c><00:10:36.480><c> the</c><00:10:36.839><c> chroma</c>

00:10:37.190 --> 00:10:37.200 align:start position:0%
indeed this is how you set up the chroma
 

00:10:37.200 --> 00:10:39.710 align:start position:0%
indeed this is how you set up the chroma
Vector<00:10:37.620><c> store</c><00:10:37.800><c> so</c><00:10:38.459><c> that's</c><00:10:38.580><c> perfect</c><00:10:38.880><c> it</c>

00:10:39.710 --> 00:10:39.720 align:start position:0%
Vector store so that's perfect it
 

00:10:39.720 --> 00:10:41.449 align:start position:0%
Vector store so that's perfect it
obviously<00:10:40.140><c> retrieved</c><00:10:40.560><c> the</c><00:10:40.740><c> relevant</c><00:10:41.100><c> parts</c>

00:10:41.449 --> 00:10:41.459 align:start position:0%
obviously retrieved the relevant parts
 

00:10:41.459 --> 00:10:44.449 align:start position:0%
obviously retrieved the relevant parts
of<00:10:41.640><c> the</c><00:10:41.760><c> documentation</c><00:10:42.680><c> and</c><00:10:43.680><c> relate</c><00:10:44.160><c> that</c><00:10:44.339><c> to</c>

00:10:44.449 --> 00:10:44.459 align:start position:0%
of the documentation and relate that to
 

00:10:44.459 --> 00:10:46.430 align:start position:0%
of the documentation and relate that to
me<00:10:44.579><c> appropriately</c>

00:10:46.430 --> 00:10:46.440 align:start position:0%
me appropriately
 

00:10:46.440 --> 00:10:47.990 align:start position:0%
me appropriately
our<00:10:46.860><c> second</c><00:10:47.040><c> question</c><00:10:47.220><c> here</c><00:10:47.459><c> how</c><00:10:47.700><c> can</c><00:10:47.880><c> I</c>

00:10:47.990 --> 00:10:48.000 align:start position:0%
our second question here how can I
 

00:10:48.000 --> 00:10:50.389 align:start position:0%
our second question here how can I
customize<00:10:48.360><c> document</c><00:10:48.720><c> objects</c><00:10:49.260><c> it</c><00:10:49.980><c> gives</c><00:10:50.160><c> us</c><00:10:50.279><c> a</c>

00:10:50.389 --> 00:10:50.399 align:start position:0%
customize document objects it gives us a
 

00:10:50.399 --> 00:10:52.310 align:start position:0%
customize document objects it gives us a
bit<00:10:50.519><c> of</c><00:10:50.640><c> a</c><00:10:50.760><c> spiel</c><00:10:51.060><c> about</c><00:10:51.420><c> customizing</c><00:10:52.200><c> the</c>

00:10:52.310 --> 00:10:52.320 align:start position:0%
bit of a spiel about customizing the
 

00:10:52.320 --> 00:10:55.009 align:start position:0%
bit of a spiel about customizing the
metadata<00:10:52.860><c> attribute</c><00:10:53.540><c> we</c><00:10:54.540><c> have</c><00:10:54.720><c> things</c><00:10:54.839><c> like</c>

00:10:55.009 --> 00:10:55.019 align:start position:0%
metadata attribute we have things like
 

00:10:55.019 --> 00:10:57.370 align:start position:0%
metadata attribute we have things like
excluded<00:10:55.620><c> embed</c><00:10:55.980><c> Keys</c><00:10:56.279><c> metadata</c><00:10:56.940><c> template</c>

00:10:57.370 --> 00:10:57.380 align:start position:0%
excluded embed Keys metadata template
 

00:10:57.380 --> 00:11:01.250 align:start position:0%
excluded embed Keys metadata template
this<00:10:58.380><c> is</c><00:10:58.440><c> all</c><00:10:58.680><c> correct</c>

00:11:01.250 --> 00:11:01.260 align:start position:0%
 
 

00:11:01.260 --> 00:11:03.170 align:start position:0%
 
and<00:11:01.860><c> then</c><00:11:02.040><c> lastly</c><00:11:02.339><c> how</c><00:11:02.519><c> can</c><00:11:02.700><c> I</c><00:11:02.820><c> customize</c>

00:11:03.170 --> 00:11:03.180 align:start position:0%
and then lastly how can I customize
 

00:11:03.180 --> 00:11:05.930 align:start position:0%
and then lastly how can I customize
document<00:11:03.959><c> metadata</c><00:11:04.740><c> specifically</c><00:11:05.279><c> not</c><00:11:05.760><c> just</c>

00:11:05.930 --> 00:11:05.940 align:start position:0%
document metadata specifically not just
 

00:11:05.940 --> 00:11:08.210 align:start position:0%
document metadata specifically not just
the<00:11:06.120><c> document</c><00:11:06.420><c> object</c><00:11:06.839><c> it</c><00:11:07.620><c> gives</c><00:11:07.800><c> us</c><00:11:07.980><c> a</c><00:11:08.100><c> bit</c>

00:11:08.210 --> 00:11:08.220 align:start position:0%
the document object it gives us a bit
 

00:11:08.220 --> 00:11:10.190 align:start position:0%
the document object it gives us a bit
more<00:11:08.399><c> detailed</c><00:11:08.880><c> explanation</c><00:11:09.720><c> that</c><00:11:10.019><c> is</c>

00:11:10.190 --> 00:11:10.200 align:start position:0%
more detailed explanation that is
 

00:11:10.200 --> 00:11:12.470 align:start position:0%
more detailed explanation that is
actually<00:11:10.380><c> quite</c><00:11:10.740><c> helpful</c><00:11:11.100><c> it</c><00:11:11.820><c> mentions</c><00:11:12.120><c> both</c>

00:11:12.470 --> 00:11:12.480 align:start position:0%
actually quite helpful it mentions both
 

00:11:12.480 --> 00:11:15.350 align:start position:0%
actually quite helpful it mentions both
the<00:11:12.899><c> excluded</c><00:11:13.260><c> Ella</c><00:11:13.620><c> metadata</c><00:11:14.160><c> Keys</c><00:11:14.519><c> excluded</c>

00:11:15.350 --> 00:11:15.360 align:start position:0%
the excluded Ella metadata Keys excluded
 

00:11:15.360 --> 00:11:18.410 align:start position:0%
the excluded Ella metadata Keys excluded
embed<00:11:15.720><c> Keys</c><00:11:16.339><c> all</c><00:11:17.339><c> these</c><00:11:17.519><c> fancy</c><00:11:17.880><c> attributes</c>

00:11:18.410 --> 00:11:18.420 align:start position:0%
embed Keys all these fancy attributes
 

00:11:18.420 --> 00:11:20.690 align:start position:0%
embed Keys all these fancy attributes
here<00:11:18.660><c> that</c><00:11:18.899><c> are</c><00:11:19.560><c> very</c><00:11:20.160><c> helpful</c><00:11:20.459><c> for</c>

00:11:20.690 --> 00:11:20.700 align:start position:0%
here that are very helpful for
 

00:11:20.700 --> 00:11:23.329 align:start position:0%
here that are very helpful for
customizing<00:11:21.240><c> documents</c><00:11:21.660><c> in</c><00:11:21.839><c> their</c><00:11:22.019><c> metadata</c>

00:11:23.329 --> 00:11:23.339 align:start position:0%
customizing documents in their metadata
 

00:11:23.339 --> 00:11:25.910 align:start position:0%
customizing documents in their metadata
and<00:11:23.820><c> yeah</c><00:11:23.940><c> great</c><00:11:24.240><c> response</c>

00:11:25.910 --> 00:11:25.920 align:start position:0%
and yeah great response
 

00:11:25.920 --> 00:11:27.829 align:start position:0%
and yeah great response
and<00:11:26.339><c> so</c><00:11:26.459><c> yeah</c><00:11:26.760><c> that's</c><00:11:27.120><c> basically</c><00:11:27.360><c> it</c><00:11:27.540><c> we've</c>

00:11:27.829 --> 00:11:27.839 align:start position:0%
and so yeah that's basically it we've
 

00:11:27.839 --> 00:11:30.889 align:start position:0%
and so yeah that's basically it we've
covered<00:11:28.200><c> setting</c><00:11:28.980><c> up</c><00:11:29.160><c> a</c><00:11:29.700><c> custom</c><00:11:29.940><c> retriever</c><00:11:30.480><c> so</c>

00:11:30.889 --> 00:11:30.899 align:start position:0%
covered setting up a custom retriever so
 

00:11:30.899 --> 00:11:32.389 align:start position:0%
covered setting up a custom retriever so
specifically<00:11:31.320><c> the</c><00:11:31.500><c> auto</c><00:11:31.620><c> merging</c><00:11:32.040><c> retriever</c>

00:11:32.389 --> 00:11:32.399 align:start position:0%
specifically the auto merging retriever
 

00:11:32.399 --> 00:11:34.610 align:start position:0%
specifically the auto merging retriever
which<00:11:32.700><c> uses</c><00:11:33.060><c> the</c><00:11:33.360><c> hierarchical</c><00:11:33.779><c> node</c><00:11:34.200><c> parser</c>

00:11:34.610 --> 00:11:34.620 align:start position:0%
which uses the hierarchical node parser
 

00:11:34.620 --> 00:11:37.730 align:start position:0%
which uses the hierarchical node parser
we've<00:11:35.399><c> covered</c><00:11:35.700><c> implementing</c><00:11:36.420><c> a</c><00:11:37.079><c> custom</c><00:11:37.380><c> node</c>

00:11:37.730 --> 00:11:37.740 align:start position:0%
we've covered implementing a custom node
 

00:11:37.740 --> 00:11:40.250 align:start position:0%
we've covered implementing a custom node
post<00:11:37.920><c> processor</c><00:11:38.519><c> and</c><00:11:39.240><c> also</c><00:11:39.540><c> reviewed</c><00:11:39.959><c> how</c><00:11:40.140><c> to</c>

00:11:40.250 --> 00:11:40.260 align:start position:0%
post processor and also reviewed how to
 

00:11:40.260 --> 00:11:42.410 align:start position:0%
post processor and also reviewed how to
set<00:11:40.380><c> up</c><00:11:40.500><c> a</c><00:11:40.740><c> router</c><00:11:41.160><c> query</c><00:11:41.399><c> engine</c>

00:11:42.410 --> 00:11:42.420 align:start position:0%
set up a router query engine
 

00:11:42.420 --> 00:11:44.810 align:start position:0%
set up a router query engine
if<00:11:43.079><c> you</c><00:11:43.140><c> visit</c><00:11:43.320><c> the</c><00:11:43.680><c> repository</c><00:11:44.279><c> for</c><00:11:44.579><c> this</c>

00:11:44.810 --> 00:11:44.820 align:start position:0%
if you visit the repository for this
 

00:11:44.820 --> 00:11:47.090 align:start position:0%
if you visit the repository for this
video<00:11:45.060><c> you'll</c><00:11:45.480><c> find</c><00:11:45.720><c> the</c><00:11:46.260><c> full</c><00:11:46.620><c> code</c><00:11:46.860><c> is</c>

00:11:47.090 --> 00:11:47.100 align:start position:0%
video you'll find the full code is
 

00:11:47.100 --> 00:11:49.069 align:start position:0%
video you'll find the full code is
available<00:11:47.279><c> in</c><00:11:47.700><c> the</c><00:11:47.820><c> Llama</c><00:11:48.060><c> docs</c><00:11:48.480><c> bot</c><00:11:48.720><c> folder</c>

00:11:49.069 --> 00:11:49.079 align:start position:0%
available in the Llama docs bot folder
 

00:11:49.079 --> 00:11:51.710 align:start position:0%
available in the Llama docs bot folder
as<00:11:49.320><c> well</c><00:11:49.500><c> as</c><00:11:49.700><c> the</c><00:11:50.700><c> code</c><00:11:50.820><c> for</c><00:11:51.120><c> this</c><00:11:51.360><c> specific</c>

00:11:51.710 --> 00:11:51.720 align:start position:0%
as well as the code for this specific
 

00:11:51.720 --> 00:11:53.870 align:start position:0%
as well as the code for this specific
notebook<00:11:52.140><c> in</c><00:11:53.040><c> this</c><00:11:53.160><c> folder</c><00:11:53.399><c> I've</c><00:11:53.579><c> cleaned</c><00:11:53.820><c> it</c>

00:11:53.870 --> 00:11:53.880 align:start position:0%
notebook in this folder I've cleaned it
 

00:11:53.880 --> 00:11:56.509 align:start position:0%
notebook in this folder I've cleaned it
up<00:11:54.000><c> a</c><00:11:54.120><c> bit</c><00:11:54.180><c> made</c><00:11:54.480><c> it</c><00:11:54.600><c> easier</c><00:11:54.839><c> to</c><00:11:55.079><c> read</c><00:11:55.519><c> and</c>

00:11:56.509 --> 00:11:56.519 align:start position:0%
up a bit made it easier to read and
 

00:11:56.519 --> 00:11:58.130 align:start position:0%
up a bit made it easier to read and
understand

00:11:58.130 --> 00:11:58.140 align:start position:0%
understand
 

00:11:58.140 --> 00:12:00.170 align:start position:0%
understand
and<00:11:58.560><c> yeah</c><00:11:58.740><c> that's</c><00:11:58.920><c> everything</c><00:11:59.160><c> thank</c><00:11:59.880><c> you</c><00:12:00.000><c> and</c>

00:12:00.170 --> 00:12:00.180 align:start position:0%
and yeah that's everything thank you and
 

00:12:00.180 --> 00:12:02.899 align:start position:0%
and yeah that's everything thank you and
see<00:12:00.480><c> you</c><00:12:00.600><c> next</c><00:12:00.720><c> time</c>

