WEBVTT
Kind: captions
Language: en

00:00:01.120 --> 00:00:02.710 align:start position:0%
 
There's<00:00:01.360><c> an</c><00:00:01.520><c> epic</c><00:00:01.839><c> battle</c><00:00:02.080><c> on</c><00:00:02.240><c> the</c><00:00:02.399><c> internet,</c>

00:00:02.710 --> 00:00:02.720 align:start position:0%
There's an epic battle on the internet,
 

00:00:02.720 --> 00:00:05.349 align:start position:0%
There's an epic battle on the internet,
ladies<00:00:02.960><c> and</c><00:00:03.120><c> gentlemen.</c><00:00:04.000><c> In</c><00:00:04.240><c> this</c><00:00:04.480><c> corner,</c>

00:00:05.349 --> 00:00:05.359 align:start position:0%
ladies and gentlemen. In this corner,
 

00:00:05.359 --> 00:00:09.910 align:start position:0%
ladies and gentlemen. In this corner,
Rag<00:00:06.080><c> is</c><00:00:06.960><c> dead</c><00:00:07.839><c> again.</c><00:00:08.880><c> In</c><00:00:09.120><c> the</c><00:00:09.280><c> other</c><00:00:09.440><c> corner,</c>

00:00:09.910 --> 00:00:09.920 align:start position:0%
Rag is dead again. In the other corner,
 

00:00:09.920 --> 00:00:12.549 align:start position:0%
Rag is dead again. In the other corner,
Rag.<00:00:10.400><c> Long</c><00:00:10.639><c> live</c><00:00:10.880><c> Rag.</c><00:00:11.519><c> Rag</c><00:00:11.920><c> will</c><00:00:12.080><c> never</c><00:00:12.240><c> die.</c>

00:00:12.549 --> 00:00:12.559 align:start position:0%
Rag. Long live Rag. Rag will never die.
 

00:00:12.559 --> 00:00:15.829 align:start position:0%
Rag. Long live Rag. Rag will never die.
It's<00:00:12.719><c> going</c><00:00:12.800><c> to</c><00:00:12.880><c> be</c><00:00:12.960><c> with</c><00:00:13.200><c> us</c><00:00:14.080><c> forever.</c><00:00:15.519><c> What's</c>

00:00:15.829 --> 00:00:15.839 align:start position:0%
It's going to be with us forever. What's
 

00:00:15.839 --> 00:00:25.269 align:start position:0%
It's going to be with us forever. What's
the<00:00:16.000><c> truth?</c><00:00:16.240><c> Here's</c><00:00:16.480><c> my</c><00:00:16.640><c> take.</c>

00:00:25.269 --> 00:00:25.279 align:start position:0%
 
 

00:00:25.279 --> 00:00:27.509 align:start position:0%
 
The<00:00:25.519><c> argument</c><00:00:25.840><c> for</c><00:00:26.000><c> rag</c><00:00:26.320><c> is</c><00:00:26.560><c> dead</c><00:00:26.880><c> is</c><00:00:27.199><c> simply</c>

00:00:27.509 --> 00:00:27.519 align:start position:0%
The argument for rag is dead is simply
 

00:00:27.519 --> 00:00:30.790 align:start position:0%
The argument for rag is dead is simply
this.<00:00:28.160><c> that</c><00:00:28.480><c> as</c><00:00:28.720><c> language</c><00:00:29.119><c> models</c><00:00:29.920><c> get</c><00:00:30.480><c> larger</c>

00:00:30.790 --> 00:00:30.800 align:start position:0%
this. that as language models get larger
 

00:00:30.800 --> 00:00:32.870 align:start position:0%
this. that as language models get larger
in<00:00:31.039><c> their</c><00:00:31.279><c> context</c><00:00:31.760><c> window,</c><00:00:32.239><c> meaning</c><00:00:32.480><c> you</c><00:00:32.719><c> can</c>

00:00:32.870 --> 00:00:32.880 align:start position:0%
in their context window, meaning you can
 

00:00:32.880 --> 00:00:35.830 align:start position:0%
in their context window, meaning you can
put<00:00:33.200><c> more</c><00:00:33.600><c> information</c><00:00:34.800><c> uh</c><00:00:35.040><c> into</c><00:00:35.280><c> them</c><00:00:35.440><c> in</c><00:00:35.680><c> a</c>

00:00:35.830 --> 00:00:35.840 align:start position:0%
put more information uh into them in a
 

00:00:35.840 --> 00:00:39.510 align:start position:0%
put more information uh into them in a
single<00:00:36.079><c> prompt,</c><00:00:37.360><c> the</c><00:00:37.600><c> need</c><00:00:37.840><c> for</c><00:00:38.000><c> rag</c><00:00:39.200><c> gets</c>

00:00:39.510 --> 00:00:39.520 align:start position:0%
single prompt, the need for rag gets
 

00:00:39.520 --> 00:00:41.430 align:start position:0%
single prompt, the need for rag gets
smaller<00:00:39.840><c> and</c><00:00:40.079><c> smaller.</c><00:00:40.559><c> Remember,</c><00:00:40.879><c> rag</c><00:00:41.280><c> is</c>

00:00:41.430 --> 00:00:41.440 align:start position:0%
smaller and smaller. Remember, rag is
 

00:00:41.440 --> 00:00:43.270 align:start position:0%
smaller and smaller. Remember, rag is
all<00:00:41.600><c> about</c><00:00:41.920><c> retrieving</c><00:00:42.480><c> small</c><00:00:42.800><c> pieces</c><00:00:43.120><c> of</c>

00:00:43.270 --> 00:00:43.280 align:start position:0%
all about retrieving small pieces of
 

00:00:43.280 --> 00:00:45.030 align:start position:0%
all about retrieving small pieces of
information<00:00:43.680><c> from</c><00:00:43.920><c> a</c><00:00:44.160><c> database,</c><00:00:44.640><c> typically</c><00:00:44.879><c> a</c>

00:00:45.030 --> 00:00:45.040 align:start position:0%
information from a database, typically a
 

00:00:45.040 --> 00:00:47.510 align:start position:0%
information from a database, typically a
vector<00:00:45.360><c> database</c><00:00:45.760><c> of</c><00:00:46.000><c> documents.</c><00:00:47.120><c> And</c><00:00:47.280><c> when</c>

00:00:47.510 --> 00:00:47.520 align:start position:0%
vector database of documents. And when
 

00:00:47.520 --> 00:00:49.270 align:start position:0%
vector database of documents. And when
someone<00:00:47.760><c> asks</c><00:00:48.000><c> a</c><00:00:48.160><c> question,</c><00:00:48.480><c> you</c><00:00:48.719><c> retrieve</c><00:00:49.039><c> a</c>

00:00:49.270 --> 00:00:49.280 align:start position:0%
someone asks a question, you retrieve a
 

00:00:49.280 --> 00:00:51.190 align:start position:0%
someone asks a question, you retrieve a
couple<00:00:49.360><c> of</c><00:00:49.520><c> pieces</c><00:00:49.760><c> of</c><00:00:49.920><c> information,</c><00:00:50.719><c> and</c><00:00:50.960><c> you</c>

00:00:51.190 --> 00:00:51.200 align:start position:0%
couple of pieces of information, and you
 

00:00:51.200 --> 00:00:53.029 align:start position:0%
couple of pieces of information, and you
send<00:00:51.440><c> that</c><00:00:51.600><c> into</c><00:00:51.840><c> a</c><00:00:52.000><c> language</c><00:00:52.320><c> model</c><00:00:52.640><c> to</c><00:00:52.879><c> try</c>

00:00:53.029 --> 00:00:53.039 align:start position:0%
send that into a language model to try
 

00:00:53.039 --> 00:00:55.830 align:start position:0%
send that into a language model to try
to<00:00:53.280><c> create</c><00:00:53.520><c> the</c><00:00:53.760><c> answer</c><00:00:54.000><c> from</c><00:00:54.320><c> that</c><00:00:54.960><c> context.</c>

00:00:55.830 --> 00:00:55.840 align:start position:0%
to create the answer from that context.
 

00:00:55.840 --> 00:00:57.430 align:start position:0%
to create the answer from that context.
And<00:00:56.000><c> the</c><00:00:56.239><c> reason</c><00:00:56.399><c> you</c><00:00:56.640><c> do</c><00:00:56.719><c> that</c><00:00:56.960><c> is</c><00:00:57.199><c> because</c>

00:00:57.430 --> 00:00:57.440 align:start position:0%
And the reason you do that is because
 

00:00:57.440 --> 00:00:59.590 align:start position:0%
And the reason you do that is because
you<00:00:57.680><c> can't</c><00:00:58.000><c> stick</c><00:00:58.640><c> all</c><00:00:58.879><c> of</c><00:00:59.039><c> your</c><00:00:59.199><c> documents</c>

00:00:59.590 --> 00:00:59.600 align:start position:0%
you can't stick all of your documents
 

00:00:59.600 --> 00:01:01.510 align:start position:0%
you can't stick all of your documents
into<00:00:59.840><c> the</c><00:01:00.000><c> language</c><00:01:00.320><c> model</c><00:01:00.640><c> directly.</c><00:01:01.120><c> So</c><00:01:01.280><c> you</c>

00:01:01.510 --> 00:01:01.520 align:start position:0%
into the language model directly. So you
 

00:01:01.520 --> 00:01:03.270 align:start position:0%
into the language model directly. So you
try<00:01:01.760><c> to</c><00:01:01.920><c> search</c><00:01:02.160><c> those</c><00:01:02.480><c> documents,</c><00:01:02.960><c> find</c><00:01:03.120><c> a</c>

00:01:03.270 --> 00:01:03.280 align:start position:0%
try to search those documents, find a
 

00:01:03.280 --> 00:01:05.270 align:start position:0%
try to search those documents, find a
little<00:01:03.359><c> bit,</c><00:01:03.680><c> retrieve</c><00:01:03.920><c> it,</c><00:01:04.559><c> and</c><00:01:04.799><c> stick</c><00:01:05.040><c> into</c>

00:01:05.270 --> 00:01:05.280 align:start position:0%
little bit, retrieve it, and stick into
 

00:01:05.280 --> 00:01:07.590 align:start position:0%
little bit, retrieve it, and stick into
the<00:01:05.360><c> model.</c><00:01:06.320><c> If</c><00:01:06.560><c> you've</c><00:01:06.720><c> got</c><00:01:06.880><c> a</c><00:01:07.119><c> very</c><00:01:07.360><c> large</c>

00:01:07.590 --> 00:01:07.600 align:start position:0%
the model. If you've got a very large
 

00:01:07.600 --> 00:01:09.750 align:start position:0%
the model. If you've got a very large
language<00:01:08.000><c> model</c><00:01:08.400><c> context</c><00:01:08.960><c> window,</c><00:01:09.520><c> and</c>

00:01:09.750 --> 00:01:09.760 align:start position:0%
language model context window, and
 

00:01:09.760 --> 00:01:12.070 align:start position:0%
language model context window, and
increasingly<00:01:10.400><c> they're</c><00:01:10.640><c> getting</c><00:01:10.880><c> bigger.</c>

00:01:12.070 --> 00:01:12.080 align:start position:0%
increasingly they're getting bigger.
 

00:01:12.080 --> 00:01:15.030 align:start position:0%
increasingly they're getting bigger.
Google's<00:01:12.880><c> uh</c><00:01:13.040><c> Gemini</c><00:01:13.520><c> 2.5</c><00:01:14.080><c> is</c><00:01:14.240><c> at</c><00:01:14.400><c> a</c><00:01:14.640><c> million</c>

00:01:15.030 --> 00:01:15.040 align:start position:0%
Google's uh Gemini 2.5 is at a million
 

00:01:15.040 --> 00:01:17.510 align:start position:0%
Google's uh Gemini 2.5 is at a million
tokens.<00:01:16.240><c> They're</c><00:01:16.560><c> saying</c><00:01:16.880><c> 2</c><00:01:17.040><c> million</c><00:01:17.280><c> is</c>

00:01:17.510 --> 00:01:17.520 align:start position:0%
tokens. They're saying 2 million is
 

00:01:17.520 --> 00:01:20.870 align:start position:0%
tokens. They're saying 2 million is
coming.<00:01:18.479><c> Uh</c><00:01:19.360><c> Llama</c><00:01:19.759><c> Meta</c><00:01:20.159><c> has</c><00:01:20.240><c> a</c><00:01:20.400><c> model</c><00:01:20.640><c> out</c>

00:01:20.870 --> 00:01:20.880 align:start position:0%
coming. Uh Llama Meta has a model out
 

00:01:20.880 --> 00:01:22.710 align:start position:0%
coming. Uh Llama Meta has a model out
there<00:01:21.040><c> that</c><00:01:21.360><c> says</c><00:01:21.600><c> a</c><00:01:21.759><c> 10</c><00:01:22.000><c> million</c><00:01:22.320><c> token</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
there that says a 10 million token
 

00:01:22.720 --> 00:01:25.030 align:start position:0%
there that says a 10 million token
window.<00:01:23.840><c> These</c><00:01:24.240><c> are</c><00:01:24.400><c> getting</c><00:01:24.640><c> bigger</c><00:01:24.880><c> and</c>

00:01:25.030 --> 00:01:25.040 align:start position:0%
window. These are getting bigger and
 

00:01:25.040 --> 00:01:26.950 align:start position:0%
window. These are getting bigger and
bigger<00:01:25.439><c> considering</c><00:01:25.920><c> just</c><00:01:26.159><c> a</c><00:01:26.400><c> few</c><00:01:26.560><c> years</c><00:01:26.720><c> ago</c>

00:01:26.950 --> 00:01:26.960 align:start position:0%
bigger considering just a few years ago
 

00:01:26.960 --> 00:01:29.270 align:start position:0%
bigger considering just a few years ago
we<00:01:27.200><c> were</c><00:01:27.280><c> at</c><00:01:27.600><c> when</c><00:01:27.920><c> GPT</c><00:01:28.479><c> launched</c><00:01:28.799><c> I</c><00:01:28.960><c> think</c>

00:01:29.270 --> 00:01:29.280 align:start position:0%
we were at when GPT launched I think
 

00:01:29.280 --> 00:01:31.030 align:start position:0%
we were at when GPT launched I think
16,000<00:01:29.759><c> tokens.</c><00:01:30.159><c> We've</c><00:01:30.400><c> gone</c><00:01:30.560><c> from</c><00:01:30.720><c> several</c>

00:01:31.030 --> 00:01:31.040 align:start position:0%
16,000 tokens. We've gone from several
 

00:01:31.040 --> 00:01:33.749 align:start position:0%
16,000 tokens. We've gone from several
thousand<00:01:31.280><c> to</c><00:01:31.439><c> several</c><00:01:31.840><c> million</c><00:01:33.040><c> in</c><00:01:33.360><c> 24</c>

00:01:33.749 --> 00:01:33.759 align:start position:0%
thousand to several million in 24
 

00:01:33.759 --> 00:01:36.069 align:start position:0%
thousand to several million in 24
months.<00:01:34.479><c> And</c><00:01:34.640><c> the</c><00:01:34.880><c> argument</c><00:01:35.200><c> is</c><00:01:35.600><c> well</c><00:01:35.920><c> maybe</c>

00:01:36.069 --> 00:01:36.079 align:start position:0%
months. And the argument is well maybe
 

00:01:36.079 --> 00:01:37.670 align:start position:0%
months. And the argument is well maybe
you<00:01:36.240><c> can</c><00:01:36.400><c> only</c><00:01:36.560><c> put</c><00:01:36.640><c> in</c><00:01:36.880><c> a</c><00:01:37.040><c> few</c><00:01:37.200><c> paragraphs</c>

00:01:37.670 --> 00:01:37.680 align:start position:0%
you can only put in a few paragraphs
 

00:01:37.680 --> 00:01:39.190 align:start position:0%
you can only put in a few paragraphs
today<00:01:38.000><c> but</c><00:01:38.240><c> soon</c><00:01:38.400><c> you'll</c><00:01:38.640><c> be</c><00:01:38.720><c> able</c><00:01:38.799><c> to</c><00:01:38.880><c> put</c><00:01:39.040><c> in</c>

00:01:39.190 --> 00:01:39.200 align:start position:0%
today but soon you'll be able to put in
 

00:01:39.200 --> 00:01:41.350 align:start position:0%
today but soon you'll be able to put in
entire<00:01:39.600><c> books</c><00:01:40.079><c> and</c><00:01:40.320><c> then</c><00:01:40.479><c> entire</c><00:01:40.960><c> libraries</c>

00:01:41.350 --> 00:01:41.360 align:start position:0%
entire books and then entire libraries
 

00:01:41.360 --> 00:01:44.310 align:start position:0%
entire books and then entire libraries
of<00:01:41.520><c> books</c><00:01:42.159><c> and</c><00:01:42.400><c> you</c><00:01:42.640><c> don't</c><00:01:42.799><c> need</c><00:01:43.040><c> wreck.</c>

00:01:44.310 --> 00:01:44.320 align:start position:0%
of books and you don't need wreck.
 

00:01:44.320 --> 00:01:46.630 align:start position:0%
of books and you don't need wreck.
Here's<00:01:44.640><c> where</c><00:01:44.799><c> I</c><00:01:44.960><c> think</c><00:01:45.119><c> that's</c><00:01:45.439><c> wrong.</c>

00:01:46.630 --> 00:01:46.640 align:start position:0%
Here's where I think that's wrong.
 

00:01:46.640 --> 00:01:48.469 align:start position:0%
Here's where I think that's wrong.
Number<00:01:46.960><c> one,</c>

00:01:48.469 --> 00:01:48.479 align:start position:0%
Number one,
 

00:01:48.479 --> 00:01:50.870 align:start position:0%
Number one,
these<00:01:49.200><c> contact</c><00:01:49.600><c> lengths</c><00:01:50.000><c> sound</c><00:01:50.320><c> impressive,</c>

00:01:50.870 --> 00:01:50.880 align:start position:0%
these contact lengths sound impressive,
 

00:01:50.880 --> 00:01:53.590 align:start position:0%
these contact lengths sound impressive,
but<00:01:51.040><c> a</c><00:01:51.280><c> million</c><00:01:51.680><c> tokens</c><00:01:52.159><c> is</c><00:01:52.479><c> still</c><00:01:52.640><c> only</c><00:01:53.280><c> a</c>

00:01:53.590 --> 00:01:53.600 align:start position:0%
but a million tokens is still only a
 

00:01:53.600 --> 00:01:55.910 align:start position:0%
but a million tokens is still only a
couple<00:01:53.759><c> of</c><00:01:53.840><c> thousand</c><00:01:54.240><c> pages</c><00:01:54.640><c> of</c><00:01:54.799><c> content.</c><00:01:55.759><c> And</c>

00:01:55.910 --> 00:01:55.920 align:start position:0%
couple of thousand pages of content. And
 

00:01:55.920 --> 00:02:00.069 align:start position:0%
couple of thousand pages of content. And
that<00:01:56.159><c> may</c><00:01:56.399><c> be</c><00:01:56.960><c> great</c><00:01:57.360><c> for</c><00:01:58.640><c> a</c><00:01:58.960><c> small</c><00:01:59.200><c> business,</c>

00:02:00.069 --> 00:02:00.079 align:start position:0%
that may be great for a small business,
 

00:02:00.079 --> 00:02:02.069 align:start position:0%
that may be great for a small business,
but<00:02:00.320><c> if</c><00:02:00.479><c> you</c><00:02:00.560><c> imagine</c><00:02:00.880><c> a</c><00:02:01.040><c> single</c><00:02:01.280><c> lawsuit,</c><00:02:01.840><c> a</c>

00:02:02.069 --> 00:02:02.079 align:start position:0%
but if you imagine a single lawsuit, a
 

00:02:02.079 --> 00:02:04.630 align:start position:0%
but if you imagine a single lawsuit, a
single<00:02:02.320><c> civil</c><00:02:02.640><c> lawsuit</c><00:02:03.119><c> today</c><00:02:03.360><c> is</c><00:02:04.000><c> 1</c><00:02:04.159><c> to</c><00:02:04.399><c> 5</c>

00:02:04.630 --> 00:02:04.640 align:start position:0%
single civil lawsuit today is 1 to 5
 

00:02:04.640 --> 00:02:06.789 align:start position:0%
single civil lawsuit today is 1 to 5
million<00:02:04.880><c> pages</c><00:02:05.200><c> of</c><00:02:05.360><c> data.</c><00:02:06.000><c> So,</c><00:02:06.240><c> we're</c><00:02:06.479><c> nowhere</c>

00:02:06.789 --> 00:02:06.799 align:start position:0%
million pages of data. So, we're nowhere
 

00:02:06.799 --> 00:02:09.430 align:start position:0%
million pages of data. So, we're nowhere
near<00:02:07.600><c> storing</c><00:02:08.000><c> even</c><00:02:08.160><c> a</c><00:02:08.399><c> single</c><00:02:08.640><c> lawsuit</c><00:02:09.119><c> in</c><00:02:09.280><c> a</c>

00:02:09.430 --> 00:02:09.440 align:start position:0%
near storing even a single lawsuit in a
 

00:02:09.440 --> 00:02:11.589 align:start position:0%
near storing even a single lawsuit in a
language<00:02:09.840><c> model.</c><00:02:10.640><c> But</c><00:02:10.879><c> the</c><00:02:11.039><c> bigger</c><00:02:11.280><c> argument</c>

00:02:11.589 --> 00:02:11.599 align:start position:0%
language model. But the bigger argument
 

00:02:11.599 --> 00:02:13.190 align:start position:0%
language model. But the bigger argument
really<00:02:11.840><c> is</c><00:02:12.080><c> something</c><00:02:12.239><c> more</c><00:02:12.560><c> philosophical,</c>

00:02:13.190 --> 00:02:13.200 align:start position:0%
really is something more philosophical,
 

00:02:13.200 --> 00:02:14.949 align:start position:0%
really is something more philosophical,
which<00:02:13.360><c> is</c><00:02:13.520><c> if</c><00:02:13.680><c> you</c><00:02:13.920><c> step</c><00:02:14.080><c> back</c><00:02:14.239><c> from</c><00:02:14.400><c> a</c><00:02:14.560><c> macro</c>

00:02:14.949 --> 00:02:14.959 align:start position:0%
which is if you step back from a macro
 

00:02:14.959 --> 00:02:18.550 align:start position:0%
which is if you step back from a macro
level,<00:02:16.160><c> what</c><00:02:16.400><c> we're</c><00:02:16.720><c> talking</c><00:02:16.959><c> about</c><00:02:17.200><c> here</c><00:02:18.160><c> is</c>

00:02:18.550 --> 00:02:18.560 align:start position:0%
level, what we're talking about here is
 

00:02:18.560 --> 00:02:21.430 align:start position:0%
level, what we're talking about here is
taking<00:02:19.760><c> all</c><00:02:20.000><c> of</c><00:02:20.080><c> the</c><00:02:20.239><c> world's</c><00:02:20.640><c> information</c>

00:02:21.430 --> 00:02:21.440 align:start position:0%
taking all of the world's information
 

00:02:21.440 --> 00:02:23.190 align:start position:0%
taking all of the world's information
that's<00:02:21.760><c> currently</c><00:02:22.160><c> stored</c><00:02:22.400><c> on</c><00:02:22.560><c> the</c><00:02:22.800><c> cheapest</c>

00:02:23.190 --> 00:02:23.200 align:start position:0%
that's currently stored on the cheapest
 

00:02:23.200 --> 00:02:24.470 align:start position:0%
that's currently stored on the cheapest
medium<00:02:23.520><c> in</c><00:02:23.680><c> the</c><00:02:23.840><c> world,</c><00:02:24.000><c> which</c><00:02:24.160><c> is</c><00:02:24.319><c> hard</c>

00:02:24.470 --> 00:02:24.480 align:start position:0%
medium in the world, which is hard
 

00:02:24.480 --> 00:02:27.750 align:start position:0%
medium in the world, which is hard
drives,<00:02:25.440><c> and</c><00:02:25.760><c> moving</c><00:02:26.080><c> it</c><00:02:26.480><c> into</c><00:02:27.120><c> the</c><00:02:27.440><c> most</c>

00:02:27.750 --> 00:02:27.760 align:start position:0%
drives, and moving it into the most
 

00:02:27.760 --> 00:02:30.070 align:start position:0%
drives, and moving it into the most
expensive<00:02:28.400><c> medium</c><00:02:28.720><c> in</c><00:02:28.879><c> the</c><00:02:29.040><c> world,</c><00:02:29.599><c> which</c><00:02:29.920><c> are</c>

00:02:30.070 --> 00:02:30.080 align:start position:0%
expensive medium in the world, which are
 

00:02:30.080 --> 00:02:31.589 align:start position:0%
expensive medium in the world, which are
GPUs.

00:02:31.589 --> 00:02:31.599 align:start position:0%
GPUs.
 

00:02:31.599 --> 00:02:34.150 align:start position:0%
GPUs.
you<00:02:31.840><c> would</c><00:02:32.080><c> need</c><00:02:32.959><c> an</c><00:02:33.200><c> explosion</c><00:02:33.599><c> in</c><00:02:33.760><c> nuclear</c>

00:02:34.150 --> 00:02:34.160 align:start position:0%
you would need an explosion in nuclear
 

00:02:34.160 --> 00:02:35.910 align:start position:0%
you would need an explosion in nuclear
power<00:02:34.400><c> plants</c><00:02:34.720><c> just</c><00:02:34.959><c> to</c><00:02:35.200><c> even</c><00:02:35.440><c> contemplate</c>

00:02:35.910 --> 00:02:35.920 align:start position:0%
power plants just to even contemplate
 

00:02:35.920 --> 00:02:39.509 align:start position:0%
power plants just to even contemplate
moving<00:02:36.319><c> onetenth</c><00:02:36.800><c> of</c><00:02:36.959><c> Google's</c><00:02:37.360><c> cloud</c><00:02:38.160><c> onto</c>

00:02:39.509 --> 00:02:39.519 align:start position:0%
moving onetenth of Google's cloud onto
 

00:02:39.519 --> 00:02:42.550 align:start position:0%
moving onetenth of Google's cloud onto
uh<00:02:39.840><c> onto</c><00:02:40.239><c> GPUs.</c><00:02:41.519><c> So</c><00:02:41.920><c> unless</c><00:02:42.239><c> there's</c><00:02:42.400><c> a</c>

00:02:42.550 --> 00:02:42.560 align:start position:0%
uh onto GPUs. So unless there's a
 

00:02:42.560 --> 00:02:43.830 align:start position:0%
uh onto GPUs. So unless there's a
massive<00:02:42.879><c> hardware</c><00:02:43.280><c> change</c><00:02:43.519><c> and</c><00:02:43.680><c> there</c>

00:02:43.830 --> 00:02:43.840 align:start position:0%
massive hardware change and there
 

00:02:43.840 --> 00:02:45.350 align:start position:0%
massive hardware change and there
probably<00:02:44.080><c> will</c><00:02:44.239><c> be</c><00:02:44.480><c> architectures</c><00:02:45.200><c> will</c>

00:02:45.350 --> 00:02:45.360 align:start position:0%
probably will be architectures will
 

00:02:45.360 --> 00:02:46.949 align:start position:0%
probably will be architectures will
change<00:02:45.599><c> but</c><00:02:45.840><c> you</c><00:02:46.000><c> need</c><00:02:46.160><c> a</c><00:02:46.400><c> very</c><00:02:46.640><c> big</c>

00:02:46.949 --> 00:02:46.959 align:start position:0%
change but you need a very big
 

00:02:46.959 --> 00:02:48.550 align:start position:0%
change but you need a very big
significant<00:02:47.519><c> change</c><00:02:47.680><c> in</c><00:02:48.239><c> hardware</c>

00:02:48.550 --> 00:02:48.560 align:start position:0%
significant change in hardware
 

00:02:48.560 --> 00:02:50.390 align:start position:0%
significant change in hardware
architecture<00:02:49.519><c> and</c><00:02:49.760><c> that</c><00:02:49.920><c> doesn't</c><00:02:50.160><c> happen</c>

00:02:50.390 --> 00:02:50.400 align:start position:0%
architecture and that doesn't happen
 

00:02:50.400 --> 00:02:54.630 align:start position:0%
architecture and that doesn't happen
quickly<00:02:51.680><c> for</c><00:02:51.920><c> Rag</c><00:02:52.800><c> to</c><00:02:53.040><c> really</c><00:02:53.280><c> be</c><00:02:53.519><c> dead.</c><00:02:54.480><c> So</c>

00:02:54.630 --> 00:02:54.640 align:start position:0%
quickly for Rag to really be dead. So
 

00:02:54.640 --> 00:02:57.430 align:start position:0%
quickly for Rag to really be dead. So
for<00:02:54.800><c> all</c><00:02:54.959><c> the</c><00:02:55.120><c> haters</c><00:02:55.519><c> out</c><00:02:55.680><c> there,</c><00:02:56.640><c> Rag</c><00:02:57.200><c> is</c>

00:02:57.430 --> 00:02:57.440 align:start position:0%
for all the haters out there, Rag is
 

00:02:57.440 --> 00:02:59.589 align:start position:0%
for all the haters out there, Rag is
still<00:02:57.680><c> very</c><00:02:57.920><c> much</c><00:02:58.400><c> alive</c><00:02:58.959><c> and</c><00:02:59.200><c> that's</c><00:02:59.360><c> my</c>

00:02:59.589 --> 00:02:59.599 align:start position:0%
still very much alive and that's my
 

00:02:59.599 --> 00:03:02.480 align:start position:0%
still very much alive and that's my
take.

