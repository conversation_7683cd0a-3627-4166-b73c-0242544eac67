WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:03.189 align:start position:0%
 
I<00:00:00.240><c> have</c><00:00:00.440><c> one</c><00:00:00.799><c> prompt</c><00:00:01.680><c> 10</c><00:00:02.000><c> different</c><00:00:02.280><c> models</c>

00:00:03.189 --> 00:00:03.199 align:start position:0%
I have one prompt 10 different models
 

00:00:03.199 --> 00:00:05.390 align:start position:0%
I have one prompt 10 different models
and<00:00:03.439><c> 10</c><00:00:03.760><c> different</c><00:00:04.120><c> versions</c><00:00:04.720><c> of</c><00:00:04.880><c> the</c><00:00:05.080><c> game</c>

00:00:05.390 --> 00:00:05.400 align:start position:0%
and 10 different versions of the game
 

00:00:05.400 --> 00:00:07.550 align:start position:0%
and 10 different versions of the game
Snake<00:00:06.040><c> I'll</c><00:00:06.160><c> show</c><00:00:06.319><c> you</c><00:00:06.480><c> how</c><00:00:06.640><c> I</c><00:00:06.720><c> set</c><00:00:06.919><c> it</c><00:00:07.040><c> up</c><00:00:07.439><c> what</c>

00:00:07.550 --> 00:00:07.560 align:start position:0%
Snake I'll show you how I set it up what
 

00:00:07.560 --> 00:00:10.030 align:start position:0%
Snake I'll show you how I set it up what
I<00:00:07.680><c> did</c><00:00:07.879><c> to</c><00:00:08.080><c> use</c><00:00:08.280><c> it</c><00:00:08.719><c> and</c><00:00:08.880><c> then</c><00:00:09.000><c> find</c><00:00:09.320><c> the</c><00:00:09.519><c> output</c>

00:00:10.030 --> 00:00:10.040 align:start position:0%
I did to use it and then find the output
 

00:00:10.040 --> 00:00:12.070 align:start position:0%
I did to use it and then find the output
from<00:00:10.320><c> each</c><00:00:10.559><c> one</c><00:00:11.040><c> I'm</c><00:00:11.160><c> only</c><00:00:11.320><c> going</c><00:00:11.400><c> to</c><00:00:11.559><c> have</c><00:00:11.880><c> two</c>

00:00:12.070 --> 00:00:12.080 align:start position:0%
from each one I'm only going to have two
 

00:00:12.080 --> 00:00:14.030 align:start position:0%
from each one I'm only going to have two
rules<00:00:12.639><c> first</c><00:00:12.840><c> one</c><00:00:13.040><c> I'm</c><00:00:13.160><c> going</c><00:00:13.240><c> to</c><00:00:13.320><c> use</c><00:00:13.480><c> autogen</c>

00:00:14.030 --> 00:00:14.040 align:start position:0%
rules first one I'm going to use autogen
 

00:00:14.040 --> 00:00:15.910 align:start position:0%
rules first one I'm going to use autogen
with<00:00:14.160><c> the</c><00:00:14.240><c> same</c><00:00:14.480><c> prompts</c><00:00:15.120><c> and</c><00:00:15.280><c> two</c><00:00:15.679><c> I'm</c><00:00:15.759><c> only</c>

00:00:15.910 --> 00:00:15.920 align:start position:0%
with the same prompts and two I'm only
 

00:00:15.920 --> 00:00:18.070 align:start position:0%
with the same prompts and two I'm only
going<00:00:16.080><c> to</c><00:00:16.119><c> ask</c><00:00:16.320><c> you</c><00:00:16.480><c> to</c><00:00:16.640><c> fix</c><00:00:16.960><c> code</c><00:00:17.279><c> up</c><00:00:17.480><c> to</c><00:00:17.760><c> five</c>

00:00:18.070 --> 00:00:18.080 align:start position:0%
going to ask you to fix code up to five
 

00:00:18.080 --> 00:00:19.870 align:start position:0%
going to ask you to fix code up to five
times<00:00:18.720><c> all</c><00:00:18.840><c> right</c><00:00:18.960><c> so</c><00:00:19.080><c> I'll</c><00:00:19.199><c> be</c><00:00:19.320><c> using</c><00:00:19.560><c> runp</c>

00:00:19.870 --> 00:00:19.880 align:start position:0%
times all right so I'll be using runp
 

00:00:19.880 --> 00:00:22.349 align:start position:0%
times all right so I'll be using runp
pod.<00:00:20.480><c> to</c><00:00:20.640><c> host</c><00:00:20.880><c> my</c><00:00:21.000><c> machine</c><00:00:21.640><c> I</c><00:00:21.720><c> chose</c><00:00:22.000><c> this</c><00:00:22.160><c> pod</c>

00:00:22.349 --> 00:00:22.359 align:start position:0%
pod. to host my machine I chose this pod
 

00:00:22.359 --> 00:00:24.230 align:start position:0%
pod. to host my machine I chose this pod
for<00:00:22.519><c> 39</c><00:00:22.880><c> cents</c><00:00:23.080><c> an</c><00:00:23.240><c> hour</c><00:00:23.519><c> and</c><00:00:23.640><c> then</c><00:00:23.760><c> deploy</c><00:00:24.080><c> it</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
for 39 cents an hour and then deploy it
 

00:00:24.240 --> 00:00:26.070 align:start position:0%
for 39 cents an hour and then deploy it
connect<00:00:24.560><c> to</c><00:00:24.720><c> Jupiter</c><00:00:25.080><c> notebooks</c><00:00:25.800><c> and</c><00:00:25.920><c> then</c>

00:00:26.070 --> 00:00:26.080 align:start position:0%
connect to Jupiter notebooks and then
 

00:00:26.080 --> 00:00:27.750 align:start position:0%
connect to Jupiter notebooks and then
paste<00:00:26.279><c> my</c><00:00:26.400><c> autogen</c><00:00:26.920><c> code</c><00:00:27.160><c> to</c><00:00:27.320><c> execute</c><00:00:27.640><c> my</c>

00:00:27.750 --> 00:00:27.760 align:start position:0%
paste my autogen code to execute my
 

00:00:27.760 --> 00:00:29.710 align:start position:0%
paste my autogen code to execute my
prompt<00:00:28.039><c> to</c><00:00:28.240><c> create</c><00:00:28.560><c> snake</c><00:00:29.199><c> this</c><00:00:29.320><c> will</c><00:00:29.439><c> also</c><00:00:29.599><c> be</c>

00:00:29.710 --> 00:00:29.720 align:start position:0%
prompt to create snake this will also be
 

00:00:29.720 --> 00:00:30.870 align:start position:0%
prompt to create snake this will also be
my

00:00:30.870 --> 00:00:30.880 align:start position:0%
my
 

00:00:30.880 --> 00:00:33.790 align:start position:0%
my
download<00:00:31.359><c> olama</c><00:00:32.200><c> and</c><00:00:32.360><c> then</c><00:00:32.559><c> type</c><00:00:32.840><c> oama</c><00:00:33.399><c> serve</c>

00:00:33.790 --> 00:00:33.800 align:start position:0%
download olama and then type oama serve
 

00:00:33.800 --> 00:00:36.310 align:start position:0%
download olama and then type oama serve
to<00:00:33.960><c> start</c><00:00:34.160><c> the</c><00:00:34.320><c> local</c><00:00:34.680><c> server</c><00:00:35.200><c> on</c><00:00:35.559><c> the</c><00:00:35.760><c> Pod</c><00:00:36.200><c> I</c>

00:00:36.310 --> 00:00:36.320 align:start position:0%
to start the local server on the Pod I
 

00:00:36.320 --> 00:00:38.190 align:start position:0%
to start the local server on the Pod I
download<00:00:36.680><c> the</c><00:00:36.760><c> model</c><00:00:37.480><c> then</c><00:00:37.640><c> once</c><00:00:37.800><c> I'm</c><00:00:37.960><c> in</c><00:00:38.079><c> the</c>

00:00:38.190 --> 00:00:38.200 align:start position:0%
download the model then once I'm in the
 

00:00:38.200 --> 00:00:40.990 align:start position:0%
download the model then once I'm in the
workspace<00:00:38.800><c> directory</c><00:00:39.399><c> I</c><00:00:39.520><c> type</c><00:00:39.840><c> Python</c>

00:00:40.990 --> 00:00:41.000 align:start position:0%
workspace directory I type Python
 

00:00:41.000 --> 00:00:43.389 align:start position:0%
workspace directory I type Python
main.py<00:00:42.000><c> and</c><00:00:42.120><c> then</c><00:00:42.239><c> we</c><00:00:42.360><c> start</c><00:00:42.600><c> with</c><00:00:42.680><c> the</c><00:00:42.840><c> first</c>

00:00:43.389 --> 00:00:43.399 align:start position:0%
main.py and then we start with the first
 

00:00:43.399 --> 00:00:46.750 align:start position:0%
main.py and then we start with the first
model<00:00:44.399><c> all</c><00:00:44.520><c> right</c><00:00:44.680><c> the</c><00:00:44.760><c> first</c><00:00:44.960><c> model</c><00:00:45.239><c> up</c><00:00:45.399><c> is</c><00:00:45.760><c> 53</c>

00:00:46.750 --> 00:00:46.760 align:start position:0%
model all right the first model up is 53
 

00:00:46.760 --> 00:00:48.470 align:start position:0%
model all right the first model up is 53
after<00:00:46.960><c> the</c><00:00:47.120><c> initial</c><00:00:47.399><c> setup</c><00:00:47.840><c> the</c><00:00:48.000><c> game</c><00:00:48.280><c> did</c>

00:00:48.470 --> 00:00:48.480 align:start position:0%
after the initial setup the game did
 

00:00:48.480 --> 00:00:50.310 align:start position:0%
after the initial setup the game did
start<00:00:48.920><c> which</c><00:00:49.079><c> is</c><00:00:49.199><c> a</c><00:00:49.360><c> good</c><00:00:49.559><c> start</c><00:00:50.000><c> but</c><00:00:50.120><c> when</c><00:00:50.239><c> I</c>

00:00:50.310 --> 00:00:50.320 align:start position:0%
start which is a good start but when I
 

00:00:50.320 --> 00:00:52.389 align:start position:0%
start which is a good start but when I
try<00:00:50.480><c> to</c><00:00:50.640><c> hit</c><00:00:50.800><c> the</c><00:00:50.960><c> Apple</c><00:00:51.920><c> yeah</c><00:00:52.120><c> nothing</c>

00:00:52.389 --> 00:00:52.399 align:start position:0%
try to hit the Apple yeah nothing
 

00:00:52.399 --> 00:00:54.029 align:start position:0%
try to hit the Apple yeah nothing
happens<00:00:52.960><c> and</c><00:00:53.120><c> I</c><00:00:53.199><c> don't</c><00:00:53.399><c> die</c><00:00:53.559><c> when</c><00:00:53.680><c> I</c><00:00:53.760><c> hit</c><00:00:53.960><c> out</c>

00:00:54.029 --> 00:00:54.039 align:start position:0%
happens and I don't die when I hit out
 

00:00:54.039 --> 00:00:56.029 align:start position:0%
happens and I don't die when I hit out
of<00:00:54.160><c> bounds</c><00:00:54.600><c> I</c><00:00:54.680><c> tried</c><00:00:55.000><c> again</c><00:00:55.280><c> and</c><00:00:55.600><c> similar</c>

00:00:56.029 --> 00:00:56.039 align:start position:0%
of bounds I tried again and similar
 

00:00:56.039 --> 00:00:57.510 align:start position:0%
of bounds I tried again and similar
thing<00:00:56.359><c> I</c><00:00:56.480><c> then</c><00:00:56.600><c> asked</c><00:00:56.800><c> it</c><00:00:56.960><c> to</c><00:00:57.079><c> fix</c><00:00:57.359><c> this</c>

00:00:57.510 --> 00:00:57.520 align:start position:0%
thing I then asked it to fix this
 

00:00:57.520 --> 00:01:00.150 align:start position:0%
thing I then asked it to fix this
because<00:00:57.719><c> it</c><00:00:57.879><c> didn't</c><00:00:58.199><c> work</c><00:00:58.559><c> quite</c><00:00:58.960><c> right</c><00:00:59.760><c> then</c>

00:01:00.150 --> 00:01:00.160 align:start position:0%
because it didn't work quite right then
 

00:01:00.160 --> 00:01:03.630 align:start position:0%
because it didn't work quite right then
it<00:01:00.320><c> just</c><00:01:00.559><c> decides</c><00:01:00.960><c> to</c><00:01:01.120><c> give</c><00:01:01.280><c> me</c><00:01:01.640><c> hard</c>

00:01:03.630 --> 00:01:03.640 align:start position:0%
it just decides to give me hard
 

00:01:03.640 --> 00:01:07.109 align:start position:0%
it just decides to give me hard
mode<00:01:04.640><c> well</c><00:01:04.799><c> the</c><00:01:04.960><c> next</c><00:01:05.159><c> model</c><00:01:05.479><c> is</c><00:01:05.640><c> GPT</c><00:01:06.240><c> 4</c><00:01:07.000><c> I</c>

00:01:07.109 --> 00:01:07.119 align:start position:0%
mode well the next model is GPT 4 I
 

00:01:07.119 --> 00:01:08.950 align:start position:0%
mode well the next model is GPT 4 I
actually<00:01:07.360><c> only</c><00:01:07.560><c> had</c><00:01:07.720><c> to</c><00:01:07.960><c> ask</c><00:01:08.159><c> it</c><00:01:08.360><c> once</c><00:01:08.640><c> to</c><00:01:08.840><c> give</c>

00:01:08.950 --> 00:01:08.960 align:start position:0%
actually only had to ask it once to give
 

00:01:08.960 --> 00:01:10.670 align:start position:0%
actually only had to ask it once to give
me<00:01:09.200><c> this</c><00:01:09.439><c> and</c><00:01:09.600><c> though</c><00:01:09.759><c> the</c><00:01:09.920><c> colors</c><00:01:10.280><c> are</c>

00:01:10.670 --> 00:01:10.680 align:start position:0%
me this and though the colors are
 

00:01:10.680 --> 00:01:12.990 align:start position:0%
me this and though the colors are
questionable<00:01:11.479><c> the</c><00:01:11.640><c> game</c><00:01:11.880><c> is</c><00:01:12.119><c> working</c><00:01:12.880><c> it</c>

00:01:12.990 --> 00:01:13.000 align:start position:0%
questionable the game is working it
 

00:01:13.000 --> 00:01:14.830 align:start position:0%
questionable the game is working it
moves<00:01:13.320><c> by</c><00:01:13.479><c> itself</c><00:01:13.960><c> grows</c><00:01:14.280><c> when</c><00:01:14.400><c> it</c><00:01:14.479><c> eats</c><00:01:14.680><c> an</c>

00:01:14.830 --> 00:01:14.840 align:start position:0%
moves by itself grows when it eats an
 

00:01:14.840 --> 00:01:17.230 align:start position:0%
moves by itself grows when it eats an
apple<00:01:15.159><c> and</c><00:01:15.320><c> also</c><00:01:15.600><c> dies</c><00:01:15.960><c> when</c><00:01:16.080><c> I</c><00:01:16.240><c> hit</c><00:01:16.400><c> the</c><00:01:16.560><c> edge</c>

00:01:17.230 --> 00:01:17.240 align:start position:0%
apple and also dies when I hit the edge
 

00:01:17.240 --> 00:01:19.550 align:start position:0%
apple and also dies when I hit the edge
and<00:01:17.439><c> even</c><00:01:17.680><c> though</c><00:01:17.799><c> the</c><00:01:17.960><c> text</c><00:01:18.200><c> is</c><00:01:18.479><c> off</c><00:01:18.799><c> screen</c><00:01:19.400><c> I</c>

00:01:19.550 --> 00:01:19.560 align:start position:0%
and even though the text is off screen I
 

00:01:19.560 --> 00:01:22.950 align:start position:0%
and even though the text is off screen I
can<00:01:19.920><c> quit</c><00:01:20.280><c> or</c><00:01:20.600><c> play</c><00:01:21.040><c> again</c>

00:01:22.950 --> 00:01:22.960 align:start position:0%
can quit or play again
 

00:01:22.960 --> 00:01:26.230 align:start position:0%
can quit or play again
cool<00:01:23.960><c> the</c><00:01:24.040><c> next</c><00:01:24.200><c> model</c><00:01:24.439><c> is</c><00:01:24.600><c> GPT</c><00:01:25.119><c> 3.5</c><00:01:25.799><c> turbo</c>

00:01:26.230 --> 00:01:26.240 align:start position:0%
cool the next model is GPT 3.5 turbo
 

00:01:26.240 --> 00:01:27.469 align:start position:0%
cool the next model is GPT 3.5 turbo
which<00:01:26.320><c> is</c><00:01:26.439><c> the</c><00:01:26.600><c> last</c><00:01:26.799><c> one</c><00:01:27.000><c> that</c><00:01:27.119><c> you</c><00:01:27.240><c> actually</c>

00:01:27.469 --> 00:01:27.479 align:start position:0%
which is the last one that you actually
 

00:01:27.479 --> 00:01:29.590 align:start position:0%
which is the last one that you actually
have<00:01:27.600><c> to</c><00:01:27.720><c> pay</c><00:01:27.920><c> anything</c><00:01:28.240><c> for</c><00:01:29.040><c> again</c><00:01:29.400><c> this</c>

00:01:29.590 --> 00:01:29.600 align:start position:0%
have to pay anything for again this
 

00:01:29.600 --> 00:01:31.789 align:start position:0%
have to pay anything for again this
created<00:01:30.040><c> the</c><00:01:30.119><c> game</c><00:01:30.400><c> and</c><00:01:30.520><c> it</c><00:01:30.720><c> does</c><00:01:31.000><c> work</c><00:01:31.479><c> I</c><00:01:31.600><c> do</c>

00:01:31.789 --> 00:01:31.799 align:start position:0%
created the game and it does work I do
 

00:01:31.799 --> 00:01:33.830 align:start position:0%
created the game and it does work I do
like<00:01:31.960><c> the</c><00:01:32.079><c> border</c><00:01:32.600><c> and</c><00:01:32.720><c> the</c><00:01:32.880><c> gam</c><00:01:33.159><c> playay</c><00:01:33.520><c> is</c>

00:01:33.830 --> 00:01:33.840 align:start position:0%
like the border and the gam playay is
 

00:01:33.840 --> 00:01:35.710 align:start position:0%
like the border and the gam playay is
there<00:01:34.479><c> I</c><00:01:34.560><c> also</c><00:01:34.799><c> like</c><00:01:34.960><c> the</c><00:01:35.079><c> colors</c><00:01:35.439><c> here</c><00:01:35.640><c> a</c>

00:01:35.710 --> 00:01:35.720 align:start position:0%
there I also like the colors here a
 

00:01:35.720 --> 00:01:37.389 align:start position:0%
there I also like the colors here a
little<00:01:35.880><c> bit</c><00:01:36.000><c> better</c><00:01:36.200><c> than</c><00:01:36.320><c> what</c><00:01:36.439><c> GPT</c><00:01:36.880><c> 4</c><00:01:37.240><c> gave</c>

00:01:37.389 --> 00:01:37.399 align:start position:0%
little bit better than what GPT 4 gave
 

00:01:37.399 --> 00:01:40.590 align:start position:0%
little bit better than what GPT 4 gave
me<00:01:38.000><c> the</c><00:01:38.280><c> only</c><00:01:38.759><c> issue</c><00:01:39.240><c> is</c><00:01:39.720><c> the</c><00:01:39.880><c> Apple</c><00:01:40.200><c> can</c>

00:01:40.590 --> 00:01:40.600 align:start position:0%
me the only issue is the Apple can
 

00:01:40.600 --> 00:01:43.310 align:start position:0%
me the only issue is the Apple can
sometimes<00:01:40.920><c> appear</c><00:01:41.399><c> off</c><00:01:41.720><c> screen</c><00:01:42.280><c> and</c><00:01:42.560><c> well</c>

00:01:43.310 --> 00:01:43.320 align:start position:0%
sometimes appear off screen and well
 

00:01:43.320 --> 00:01:45.069 align:start position:0%
sometimes appear off screen and well
then<00:01:43.479><c> you</c><00:01:43.600><c> can't</c><00:01:43.799><c> really</c><00:01:44.040><c> play</c><00:01:44.560><c> and</c><00:01:44.680><c> I</c><00:01:44.799><c> did</c><00:01:44.920><c> try</c>

00:01:45.069 --> 00:01:45.079 align:start position:0%
then you can't really play and I did try
 

00:01:45.079 --> 00:01:47.469 align:start position:0%
then you can't really play and I did try
to<00:01:45.200><c> fix</c><00:01:45.479><c> this</c><00:01:45.680><c> with</c><00:01:45.880><c> a</c><00:01:46.000><c> few</c><00:01:46.320><c> iterations</c><00:01:47.320><c> and</c>

00:01:47.469 --> 00:01:47.479 align:start position:0%
to fix this with a few iterations and
 

00:01:47.479 --> 00:01:49.429 align:start position:0%
to fix this with a few iterations and
nothing<00:01:47.719><c> really</c><00:01:47.960><c> changed</c><00:01:48.600><c> but</c><00:01:48.840><c> generally</c><00:01:49.320><c> it</c>

00:01:49.429 --> 00:01:49.439 align:start position:0%
nothing really changed but generally it
 

00:01:49.439 --> 00:01:52.030 align:start position:0%
nothing really changed but generally it
does<00:01:49.719><c> work</c><00:01:50.240><c> all</c><00:01:50.439><c> right</c><00:01:50.600><c> now</c><00:01:50.719><c> we're</c><00:01:50.840><c> at</c><00:01:51.040><c> llama</c><00:01:51.479><c> 3</c>

00:01:52.030 --> 00:01:52.040 align:start position:0%
does work all right now we're at llama 3
 

00:01:52.040 --> 00:01:54.310 align:start position:0%
does work all right now we're at llama 3
I<00:01:52.159><c> had</c><00:01:52.439><c> high</c><00:01:52.640><c> hopes</c><00:01:52.920><c> for</c><00:01:53.119><c> this</c><00:01:53.280><c> model</c><00:01:54.040><c> and</c><00:01:54.200><c> this</c>

00:01:54.310 --> 00:01:54.320 align:start position:0%
I had high hopes for this model and this
 

00:01:54.320 --> 00:01:57.190 align:start position:0%
I had high hopes for this model and this
is<00:01:54.520><c> the</c><00:01:54.719><c> first</c><00:01:55.119><c> attempt</c><00:01:55.840><c> it</c><00:01:56.000><c> just</c><00:01:56.159><c> grows</c><00:01:56.920><c> no</c>

00:01:57.190 --> 00:01:57.200 align:start position:0%
is the first attempt it just grows no
 

00:01:57.200 --> 00:01:59.630 align:start position:0%
is the first attempt it just grows no
apple<00:01:57.600><c> or</c><00:01:57.759><c> food</c><00:01:58.039><c> on</c><00:01:58.159><c> the</c><00:01:58.280><c> screen</c><00:01:58.880><c> I</c><00:01:59.000><c> also</c><00:01:59.320><c> don't</c>

00:01:59.630 --> 00:01:59.640 align:start position:0%
apple or food on the screen I also don't
 

00:01:59.640 --> 00:02:02.149 align:start position:0%
apple or food on the screen I also don't
die<00:02:00.159><c> going</c><00:02:00.439><c> out</c><00:02:00.560><c> of</c><00:02:00.719><c> bounds</c><00:02:01.399><c> I</c><00:02:01.520><c> said</c><00:02:01.920><c> I</c><00:02:02.000><c> don't</c>

00:02:02.149 --> 00:02:02.159 align:start position:0%
die going out of bounds I said I don't
 

00:02:02.159 --> 00:02:03.749 align:start position:0%
die going out of bounds I said I don't
see<00:02:02.360><c> any</c><00:02:02.560><c> food</c><00:02:03.000><c> and</c><00:02:03.159><c> the</c><00:02:03.280><c> snake</c><00:02:03.520><c> grows</c>

00:02:03.749 --> 00:02:03.759 align:start position:0%
see any food and the snake grows
 

00:02:03.759 --> 00:02:05.190 align:start position:0%
see any food and the snake grows
immediately<00:02:04.240><c> it</c><00:02:04.320><c> doesn't</c><00:02:04.560><c> grow</c><00:02:04.759><c> when</c><00:02:04.920><c> food</c>

00:02:05.190 --> 00:02:05.200 align:start position:0%
immediately it doesn't grow when food
 

00:02:05.200 --> 00:02:08.589 align:start position:0%
immediately it doesn't grow when food
appears<00:02:05.719><c> can</c><00:02:05.840><c> you</c><00:02:06.000><c> please</c><00:02:06.360><c> fix</c><00:02:07.080><c> this</c><00:02:08.080><c> well</c><00:02:08.479><c> it</c>

00:02:08.589 --> 00:02:08.599 align:start position:0%
appears can you please fix this well it
 

00:02:08.599 --> 00:02:10.869 align:start position:0%
appears can you please fix this well it
didn't<00:02:08.879><c> fix</c><00:02:09.119><c> it</c><00:02:09.879><c> I</c><00:02:10.000><c> told</c><00:02:10.239><c> her</c><00:02:10.360><c> what's</c><00:02:10.560><c> wrong</c>

00:02:10.869 --> 00:02:10.879 align:start position:0%
didn't fix it I told her what's wrong
 

00:02:10.879 --> 00:02:12.229 align:start position:0%
didn't fix it I told her what's wrong
again<00:02:11.280><c> and</c><00:02:11.400><c> this</c><00:02:11.520><c> time</c><00:02:11.640><c> it</c><00:02:11.800><c> just</c><00:02:11.920><c> made</c><00:02:12.120><c> the</c>

00:02:12.229 --> 00:02:12.239 align:start position:0%
again and this time it just made the
 

00:02:12.239 --> 00:02:15.910 align:start position:0%
again and this time it just made the
snake<00:02:12.680><c> slower</c><00:02:13.680><c> I</c><00:02:13.800><c> tried</c><00:02:14.360><c> another</c><00:02:14.959><c> time</c><00:02:15.440><c> and</c>

00:02:15.910 --> 00:02:15.920 align:start position:0%
snake slower I tried another time and
 

00:02:15.920 --> 00:02:18.990 align:start position:0%
snake slower I tried another time and
nothing<00:02:16.640><c> so</c><00:02:17.280><c> llama</c><00:02:17.680><c> 3</c><00:02:18.120><c> even</c><00:02:18.360><c> though</c><00:02:18.560><c> the</c><00:02:18.680><c> game</c>

00:02:18.990 --> 00:02:19.000 align:start position:0%
nothing so llama 3 even though the game
 

00:02:19.000 --> 00:02:21.430 align:start position:0%
nothing so llama 3 even though the game
does<00:02:19.680><c> actually</c><00:02:20.040><c> start</c><00:02:20.560><c> it</c><00:02:20.680><c> didn't</c><00:02:20.920><c> really</c><00:02:21.280><c> get</c>

00:02:21.430 --> 00:02:21.440 align:start position:0%
does actually start it didn't really get
 

00:02:21.440 --> 00:02:24.509 align:start position:0%
does actually start it didn't really get
me<00:02:21.640><c> where</c><00:02:21.840><c> I</c><00:02:21.959><c> wanted</c><00:02:22.519><c> to</c><00:02:23.519><c> our</c><00:02:23.879><c> fifth</c><00:02:24.120><c> model</c><00:02:24.400><c> I</c>

00:02:24.509 --> 00:02:24.519 align:start position:0%
me where I wanted to our fifth model I
 

00:02:24.519 --> 00:02:27.589 align:start position:0%
me where I wanted to our fifth model I
chose<00:02:24.840><c> Gemini</c><00:02:25.280><c> 1.5</c><00:02:26.120><c> Pro</c><00:02:26.840><c> for</c><00:02:27.040><c> this</c><00:02:27.200><c> model</c><00:02:27.519><c> I</c>

00:02:27.589 --> 00:02:27.599 align:start position:0%
chose Gemini 1.5 Pro for this model I
 

00:02:27.599 --> 00:02:29.589 align:start position:0%
chose Gemini 1.5 Pro for this model I
did<00:02:27.760><c> use</c><00:02:28.080><c> vertex</c><00:02:28.640><c> AI</c><00:02:29.080><c> which</c><00:02:29.200><c> is</c><00:02:29.319><c> kind</c><00:02:29.400><c> of</c><00:02:29.480><c> like</c>

00:02:29.589 --> 00:02:29.599 align:start position:0%
did use vertex AI which is kind of like
 

00:02:29.599 --> 00:02:32.229 align:start position:0%
did use vertex AI which is kind of like
Google<00:02:30.080><c> Cloud</c><00:02:30.480><c> playground</c><00:02:31.480><c> with</c><00:02:31.680><c> this</c><00:02:31.840><c> model</c>

00:02:32.229 --> 00:02:32.239 align:start position:0%
Google Cloud playground with this model
 

00:02:32.239 --> 00:02:34.030 align:start position:0%
Google Cloud playground with this model
it<00:02:32.360><c> really</c><00:02:32.760><c> would</c><00:02:32.920><c> never</c><00:02:33.200><c> finish</c><00:02:33.519><c> the</c><00:02:33.680><c> code</c>

00:02:34.030 --> 00:02:34.040 align:start position:0%
it really would never finish the code
 

00:02:34.040 --> 00:02:35.869 align:start position:0%
it really would never finish the code
completely<00:02:34.720><c> so</c><00:02:34.920><c> I</c><00:02:35.040><c> then</c><00:02:35.200><c> had</c><00:02:35.319><c> to</c><00:02:35.440><c> ask</c><00:02:35.599><c> it</c><00:02:35.760><c> to</c>

00:02:35.869 --> 00:02:35.879 align:start position:0%
completely so I then had to ask it to
 

00:02:35.879 --> 00:02:37.869 align:start position:0%
completely so I then had to ask it to
finish<00:02:36.160><c> the</c><00:02:36.319><c> rest</c><00:02:36.480><c> of</c><00:02:36.599><c> the</c><00:02:36.760><c> code</c><00:02:37.360><c> that</c><00:02:37.640><c> that</c>

00:02:37.869 --> 00:02:37.879 align:start position:0%
finish the rest of the code that that
 

00:02:37.879 --> 00:02:39.350 align:start position:0%
finish the rest of the code that that
happened<00:02:38.239><c> all</c><00:02:38.400><c> the</c><00:02:38.519><c> time</c><00:02:38.720><c> with</c><00:02:38.920><c> just</c><00:02:39.159><c> this</c>

00:02:39.350 --> 00:02:39.360 align:start position:0%
happened all the time with just this
 

00:02:39.360 --> 00:02:41.990 align:start position:0%
happened all the time with just this
model<00:02:40.120><c> once</c><00:02:40.280><c> I</c><00:02:40.440><c> got</c><00:02:40.680><c> both</c><00:02:40.959><c> halves</c><00:02:41.440><c> I</c><00:02:41.560><c> ran</c><00:02:41.800><c> it</c>

00:02:41.990 --> 00:02:42.000 align:start position:0%
model once I got both halves I ran it
 

00:02:42.000 --> 00:02:43.830 align:start position:0%
model once I got both halves I ran it
and<00:02:42.159><c> it</c><00:02:42.280><c> worked</c><00:02:42.800><c> it</c><00:02:42.920><c> has</c><00:02:43.040><c> a</c><00:02:43.200><c> high</c><00:02:43.360><c> score</c><00:02:43.599><c> for</c>

00:02:43.830 --> 00:02:43.840 align:start position:0%
and it worked it has a high score for
 

00:02:43.840 --> 00:02:46.149 align:start position:0%
and it worked it has a high score for
each<00:02:44.000><c> food</c><00:02:44.280><c> piece</c><00:02:44.519><c> I</c><00:02:44.640><c> collect</c><00:02:45.239><c> responds</c><00:02:45.760><c> well</c>

00:02:46.149 --> 00:02:46.159 align:start position:0%
each food piece I collect responds well
 

00:02:46.159 --> 00:02:47.990 align:start position:0%
each food piece I collect responds well
and<00:02:46.319><c> the</c><00:02:46.400><c> food</c><00:02:46.680><c> does</c><00:02:46.920><c> appear</c><00:02:47.159><c> on</c><00:02:47.280><c> the</c><00:02:47.400><c> screen</c>

00:02:47.990 --> 00:02:48.000 align:start position:0%
and the food does appear on the screen
 

00:02:48.000 --> 00:02:50.390 align:start position:0%
and the food does appear on the screen
only<00:02:48.319><c> issue</c><00:02:49.000><c> and</c><00:02:49.159><c> really</c><00:02:49.480><c> the</c><00:02:49.680><c> only</c><00:02:49.959><c> issue</c><00:02:50.280><c> I</c>

00:02:50.390 --> 00:02:50.400 align:start position:0%
only issue and really the only issue I
 

00:02:50.400 --> 00:02:52.509 align:start position:0%
only issue and really the only issue I
found<00:02:50.920><c> is</c><00:02:51.120><c> the</c><00:02:51.280><c> snake</c><00:02:51.720><c> doesn't</c><00:02:52.040><c> move</c><00:02:52.319><c> by</c>

00:02:52.509 --> 00:02:52.519 align:start position:0%
found is the snake doesn't move by
 

00:02:52.519 --> 00:02:54.670 align:start position:0%
found is the snake doesn't move by
itself<00:02:52.920><c> which</c><00:02:53.080><c> is</c><00:02:53.280><c> kind</c><00:02:53.400><c> of</c><00:02:53.519><c> I</c><00:02:53.640><c> guess</c><00:02:53.879><c> the</c><00:02:54.080><c> core</c>

00:02:54.670 --> 00:02:54.680 align:start position:0%
itself which is kind of I guess the core
 

00:02:54.680 --> 00:02:56.350 align:start position:0%
itself which is kind of I guess the core
gameplay<00:02:55.080><c> mechanic</c><00:02:55.400><c> of</c><00:02:55.519><c> the</c><00:02:55.640><c> game</c><00:02:55.840><c> Snake</c><00:02:56.239><c> but</c>

00:02:56.350 --> 00:02:56.360 align:start position:0%
gameplay mechanic of the game Snake but
 

00:02:56.360 --> 00:02:58.869 align:start position:0%
gameplay mechanic of the game Snake but
the<00:02:56.480><c> text</c><00:02:56.720><c> on</c><00:02:56.959><c> dying</c><00:02:57.480><c> is</c><00:02:57.640><c> on</c><00:02:57.760><c> the</c><00:02:57.920><c> screen</c><00:02:58.599><c> and</c><00:02:58.800><c> I</c>

00:02:58.869 --> 00:02:58.879 align:start position:0%
the text on dying is on the screen and I
 

00:02:58.879 --> 00:03:01.070 align:start position:0%
the text on dying is on the screen and I
can<00:02:59.040><c> just</c><00:02:59.239><c> play</c><00:02:59.519><c> again</c><00:03:00.159><c> but</c><00:03:00.319><c> no</c><00:03:00.519><c> matter</c><00:03:00.879><c> how</c>

00:03:01.070 --> 00:03:01.080 align:start position:0%
can just play again but no matter how
 

00:03:01.080 --> 00:03:03.030 align:start position:0%
can just play again but no matter how
many<00:03:01.360><c> times</c><00:03:01.599><c> I</c><00:03:01.720><c> asked</c><00:03:01.959><c> it</c><00:03:02.080><c> to</c><00:03:02.239><c> fix</c><00:03:02.480><c> the</c><00:03:02.599><c> snake</c>

00:03:03.030 --> 00:03:03.040 align:start position:0%
many times I asked it to fix the snake
 

00:03:03.040 --> 00:03:05.390 align:start position:0%
many times I asked it to fix the snake
Auto<00:03:03.360><c> movement</c><00:03:04.159><c> it</c><00:03:04.319><c> just</c><00:03:04.519><c> wouldn't</c><00:03:05.120><c> all</c><00:03:05.239><c> right</c>

00:03:05.390 --> 00:03:05.400 align:start position:0%
Auto movement it just wouldn't all right
 

00:03:05.400 --> 00:03:07.190 align:start position:0%
Auto movement it just wouldn't all right
we're<00:03:05.680><c> halfway</c><00:03:06.040><c> and</c><00:03:06.159><c> I</c><00:03:06.239><c> got</c><00:03:06.319><c> to</c><00:03:06.400><c> say</c><00:03:06.640><c> the</c><00:03:06.879><c> next</c>

00:03:07.190 --> 00:03:07.200 align:start position:0%
we're halfway and I got to say the next
 

00:03:07.200 --> 00:03:10.509 align:start position:0%
we're halfway and I got to say the next
five<00:03:07.400><c> models</c><00:03:08.239><c> are</c><00:03:09.040><c> eh</c><00:03:09.440><c> and</c><00:03:09.760><c> interesting</c><00:03:10.400><c> and</c>

00:03:10.509 --> 00:03:10.519 align:start position:0%
five models are eh and interesting and
 

00:03:10.519 --> 00:03:12.509 align:start position:0%
five models are eh and interesting and
next<00:03:10.680><c> up</c><00:03:10.799><c> we</c><00:03:10.920><c> have</c><00:03:11.040><c> Gemma</c><00:03:11.400><c> 7B</c><00:03:12.080><c> well</c><00:03:12.280><c> did</c><00:03:12.400><c> you</c>

00:03:12.509 --> 00:03:12.519 align:start position:0%
next up we have Gemma 7B well did you
 

00:03:12.519 --> 00:03:14.750 align:start position:0%
next up we have Gemma 7B well did you
see<00:03:12.799><c> that</c><00:03:13.319><c> that</c><00:03:13.440><c> was</c><00:03:13.680><c> quick</c><00:03:14.080><c> I</c><00:03:14.200><c> know</c><00:03:14.519><c> let</c><00:03:14.640><c> me</c>

00:03:14.750 --> 00:03:14.760 align:start position:0%
see that that was quick I know let me
 

00:03:14.760 --> 00:03:16.710 align:start position:0%
see that that was quick I know let me
slow<00:03:15.000><c> it</c><00:03:15.080><c> down</c><00:03:15.200><c> to</c><00:03:15.360><c> a</c><00:03:15.560><c> 10th</c><00:03:15.959><c> of</c><00:03:16.120><c> the</c><00:03:16.239><c> speed</c><00:03:16.599><c> well</c>

00:03:16.710 --> 00:03:16.720 align:start position:0%
slow it down to a 10th of the speed well
 

00:03:16.720 --> 00:03:18.270 align:start position:0%
slow it down to a 10th of the speed well
it<00:03:16.840><c> just</c><00:03:16.920><c> goes</c><00:03:17.159><c> straight</c><00:03:17.400><c> up</c><00:03:17.560><c> on</c><00:03:17.720><c> start</c><00:03:17.959><c> and</c><00:03:18.120><c> I</c>

00:03:18.270 --> 00:03:18.280 align:start position:0%
it just goes straight up on start and I
 

00:03:18.280 --> 00:03:20.070 align:start position:0%
it just goes straight up on start and I
die<00:03:18.720><c> well</c><00:03:18.840><c> I</c><00:03:18.920><c> asked</c><00:03:19.080><c> it</c><00:03:19.200><c> to</c><00:03:19.360><c> fix</c><00:03:19.599><c> that</c><00:03:19.799><c> and</c><00:03:19.920><c> it</c>

00:03:20.070 --> 00:03:20.080 align:start position:0%
die well I asked it to fix that and it
 

00:03:20.080 --> 00:03:22.149 align:start position:0%
die well I asked it to fix that and it
does<00:03:20.319><c> and</c><00:03:20.440><c> it's</c><00:03:20.680><c> actually</c><00:03:21.040><c> slower</c><00:03:21.720><c> so</c><00:03:21.920><c> now</c><00:03:22.080><c> I</c>

00:03:22.149 --> 00:03:22.159 align:start position:0%
does and it's actually slower so now I
 

00:03:22.159 --> 00:03:24.149 align:start position:0%
does and it's actually slower so now I
can<00:03:22.319><c> do</c><00:03:22.599><c> something</c><00:03:23.239><c> however</c><00:03:23.440><c> there's</c><00:03:23.599><c> no</c><00:03:23.840><c> food</c>

00:03:24.149 --> 00:03:24.159 align:start position:0%
can do something however there's no food
 

00:03:24.159 --> 00:03:25.589 align:start position:0%
can do something however there's no food
and<00:03:24.280><c> I</c><00:03:24.360><c> can't</c><00:03:24.560><c> do</c><00:03:24.720><c> anything</c><00:03:25.080><c> I</c><00:03:25.159><c> has</c><00:03:25.319><c> to</c><00:03:25.360><c> get</c><00:03:25.480><c> to</c>

00:03:25.589 --> 00:03:25.599 align:start position:0%
and I can't do anything I has to get to
 

00:03:25.599 --> 00:03:27.949 align:start position:0%
and I can't do anything I has to get to
fix<00:03:25.959><c> that</c><00:03:26.239><c> and</c><00:03:26.440><c> I'm</c><00:03:26.640><c> pretty</c><00:03:26.799><c> sure</c><00:03:27.319><c> the</c><00:03:27.519><c> AI</c><00:03:27.840><c> at</c>

00:03:27.949 --> 00:03:27.959 align:start position:0%
fix that and I'm pretty sure the AI at
 

00:03:27.959 --> 00:03:29.350 align:start position:0%
fix that and I'm pretty sure the AI at
this<00:03:28.159><c> point</c><00:03:28.360><c> is</c><00:03:28.439><c> just</c><00:03:28.560><c> telling</c><00:03:28.760><c> me</c><00:03:28.840><c> to</c><00:03:29.000><c> screw</c>

00:03:29.350 --> 00:03:29.360 align:start position:0%
this point is just telling me to screw
 

00:03:29.360 --> 00:03:31.470 align:start position:0%
this point is just telling me to screw
off<00:03:29.879><c> and</c><00:03:30.080><c> my</c><00:03:30.280><c> demands</c><00:03:30.799><c> and</c><00:03:30.920><c> it</c><00:03:31.080><c> decided</c><00:03:31.400><c> to</c>

00:03:31.470 --> 00:03:31.480 align:start position:0%
off and my demands and it decided to
 

00:03:31.480 --> 00:03:34.869 align:start position:0%
off and my demands and it decided to
give<00:03:31.640><c> me</c><00:03:31.920><c> extreme</c><00:03:32.519><c> mode</c><00:03:33.480><c> now</c><00:03:33.640><c> we're</c><00:03:33.840><c> at</c><00:03:34.040><c> mistl</c>

00:03:34.869 --> 00:03:34.879 align:start position:0%
give me extreme mode now we're at mistl
 

00:03:34.879 --> 00:03:36.830 align:start position:0%
give me extreme mode now we're at mistl
I<00:03:35.000><c> had</c><00:03:35.200><c> High</c><00:03:35.400><c> Hopes</c><00:03:35.680><c> again</c><00:03:35.879><c> for</c><00:03:36.159><c> this</c><00:03:36.280><c> one</c>

00:03:36.830 --> 00:03:36.840 align:start position:0%
I had High Hopes again for this one
 

00:03:36.840 --> 00:03:39.270 align:start position:0%
I had High Hopes again for this one
however<00:03:37.400><c> they</c><00:03:37.599><c> weren't</c><00:03:38.000><c> quite</c><00:03:38.200><c> met</c><00:03:38.599><c> IED</c><00:03:39.000><c> a</c><00:03:39.080><c> few</c>

00:03:39.270 --> 00:03:39.280 align:start position:0%
however they weren't quite met IED a few
 

00:03:39.280 --> 00:03:41.270 align:start position:0%
however they weren't quite met IED a few
times<00:03:39.519><c> to</c><00:03:40.040><c> fix</c><00:03:40.360><c> the</c><00:03:40.480><c> original</c><00:03:40.879><c> code</c><00:03:41.159><c> that</c>

00:03:41.270 --> 00:03:41.280 align:start position:0%
times to fix the original code that
 

00:03:41.280 --> 00:03:43.830 align:start position:0%
times to fix the original code that
wouldn't<00:03:41.480><c> even</c><00:03:41.760><c> compile</c><00:03:42.760><c> finally</c><00:03:43.159><c> it</c><00:03:43.319><c> did</c><00:03:43.720><c> and</c>

00:03:43.830 --> 00:03:43.840 align:start position:0%
wouldn't even compile finally it did and
 

00:03:43.840 --> 00:03:46.070 align:start position:0%
wouldn't even compile finally it did and
I<00:03:43.920><c> got</c><00:03:44.360><c> this</c><00:03:44.920><c> it's</c><00:03:45.080><c> more</c><00:03:45.280><c> like</c><00:03:45.439><c> I</c><00:03:45.519><c> can</c><00:03:45.760><c> draw</c>

00:03:46.070 --> 00:03:46.080 align:start position:0%
I got this it's more like I can draw
 

00:03:46.080 --> 00:03:47.630 align:start position:0%
I got this it's more like I can draw
buildings<00:03:46.519><c> than</c><00:03:46.799><c> actually</c><00:03:47.040><c> play</c><00:03:47.200><c> a</c><00:03:47.319><c> snake</c>

00:03:47.630 --> 00:03:47.640 align:start position:0%
buildings than actually play a snake
 

00:03:47.640 --> 00:03:50.070 align:start position:0%
buildings than actually play a snake
game<00:03:48.200><c> so</c><00:03:48.400><c> after</c><00:03:48.920><c> asking</c><00:03:49.159><c> it</c><00:03:49.280><c> to</c><00:03:49.439><c> fix</c><00:03:49.599><c> the</c><00:03:49.720><c> code</c>

00:03:50.070 --> 00:03:50.080 align:start position:0%
game so after asking it to fix the code
 

00:03:50.080 --> 00:03:52.149 align:start position:0%
game so after asking it to fix the code
two<00:03:50.280><c> more</c><00:03:50.560><c> times</c><00:03:51.200><c> it</c><00:03:51.319><c> just</c><00:03:51.439><c> gave</c><00:03:51.599><c> me</c><00:03:51.760><c> something</c>

00:03:52.149 --> 00:03:52.159 align:start position:0%
two more times it just gave me something
 

00:03:52.159 --> 00:03:54.550 align:start position:0%
two more times it just gave me something
completely<00:03:52.879><c> different</c><00:03:53.519><c> I</c><00:03:53.599><c> mean</c><00:03:53.920><c> at</c><00:03:54.040><c> least</c><00:03:54.200><c> it</c>

00:03:54.550 --> 00:03:54.560 align:start position:0%
completely different I mean at least it
 

00:03:54.560 --> 00:03:56.830 align:start position:0%
completely different I mean at least it
more<00:03:54.879><c> resembles</c><00:03:55.400><c> snake</c><00:03:56.040><c> and</c><00:03:56.200><c> the</c><00:03:56.400><c> best</c><00:03:56.640><c> part</c>

00:03:56.830 --> 00:03:56.840 align:start position:0%
more resembles snake and the best part
 

00:03:56.840 --> 00:03:59.429 align:start position:0%
more resembles snake and the best part
is<00:03:57.239><c> wait</c><00:03:57.439><c> for</c><00:03:57.599><c> it</c><00:03:57.920><c> wait</c><00:03:58.159><c> for</c><00:03:58.280><c> it</c><00:03:58.720><c> wait</c><00:03:58.920><c> for</c><00:03:59.079><c> it</c><00:03:59.360><c> I</c>

00:03:59.429 --> 00:03:59.439 align:start position:0%
is wait for it wait for it wait for it I
 

00:03:59.439 --> 00:04:01.990 align:start position:0%
is wait for it wait for it wait for it I
actually<00:03:59.920><c> fail</c><00:04:00.280><c> want</c><00:04:00.439><c> to</c><00:04:00.640><c> hit</c><00:04:00.879><c> the</c><00:04:01.079><c> Apple</c>

00:04:01.990 --> 00:04:02.000 align:start position:0%
actually fail want to hit the Apple
 

00:04:02.000 --> 00:04:03.350 align:start position:0%
actually fail want to hit the Apple
reverse

00:04:03.350 --> 00:04:03.360 align:start position:0%
reverse
 

00:04:03.360 --> 00:04:06.990 align:start position:0%
reverse
snake<00:04:04.360><c> at</c><00:04:04.560><c> number</c><00:04:04.840><c> eight</c><00:04:05.120><c> we</c><00:04:05.280><c> have</c><00:04:05.560><c> Hermes</c><00:04:06.000><c> 2.0</c>

00:04:06.990 --> 00:04:07.000 align:start position:0%
snake at number eight we have Hermes 2.0
 

00:04:07.000 --> 00:04:08.750 align:start position:0%
snake at number eight we have Hermes 2.0
so<00:04:07.280><c> this</c><00:04:07.400><c> model</c><00:04:07.640><c> was</c><00:04:08.040><c> different</c><00:04:08.360><c> and</c><00:04:08.519><c> decided</c>

00:04:08.750 --> 00:04:08.760 align:start position:0%
so this model was different and decided
 

00:04:08.760 --> 00:04:10.670 align:start position:0%
so this model was different and decided
to<00:04:08.840><c> give</c><00:04:08.959><c> me</c><00:04:09.079><c> a</c><00:04:09.200><c> text</c><00:04:09.480><c> version</c><00:04:09.760><c> of</c><00:04:09.879><c> snake</c><00:04:10.519><c> which</c>

00:04:10.670 --> 00:04:10.680 align:start position:0%
to give me a text version of snake which
 

00:04:10.680 --> 00:04:11.670 align:start position:0%
to give me a text version of snake which
I<00:04:10.840><c> actually</c><00:04:11.120><c> thought</c><00:04:11.280><c> was</c><00:04:11.480><c> pretty</c>

00:04:11.670 --> 00:04:11.680 align:start position:0%
I actually thought was pretty
 

00:04:11.680 --> 00:04:14.030 align:start position:0%
I actually thought was pretty
interesting<00:04:12.599><c> however</c><00:04:13.400><c> it</c><00:04:13.560><c> doesn't</c><00:04:13.840><c> really</c>

00:04:14.030 --> 00:04:14.040 align:start position:0%
interesting however it doesn't really
 

00:04:14.040 --> 00:04:15.830 align:start position:0%
interesting however it doesn't really
work<00:04:14.480><c> I</c><00:04:14.560><c> would</c><00:04:14.720><c> input</c><00:04:15.000><c> the</c><00:04:15.079><c> commands</c><00:04:15.519><c> and</c><00:04:15.640><c> it</c>

00:04:15.830 --> 00:04:15.840 align:start position:0%
work I would input the commands and it
 

00:04:15.840 --> 00:04:18.629 align:start position:0%
work I would input the commands and it
never<00:04:16.199><c> reprinted</c><00:04:17.120><c> the</c><00:04:17.280><c> updated</c><00:04:17.759><c> text</c><00:04:18.040><c> board</c><00:04:18.519><c> I</c>

00:04:18.629 --> 00:04:18.639 align:start position:0%
never reprinted the updated text board I
 

00:04:18.639 --> 00:04:20.870 align:start position:0%
never reprinted the updated text board I
asked<00:04:18.880><c> to</c><00:04:19.040><c> fix</c><00:04:19.239><c> it</c><00:04:19.639><c> more</c><00:04:19.840><c> than</c><00:04:20.000><c> a</c><00:04:20.120><c> few</c><00:04:20.359><c> times</c>

00:04:20.870 --> 00:04:20.880 align:start position:0%
asked to fix it more than a few times
 

00:04:20.880 --> 00:04:23.790 align:start position:0%
asked to fix it more than a few times
and<00:04:21.000><c> it</c><00:04:21.120><c> just</c><00:04:21.280><c> never</c><00:04:21.600><c> did</c><00:04:22.280><c> so</c><00:04:22.880><c> kind</c><00:04:23.040><c> of</c><00:04:23.160><c> a</c><00:04:23.320><c> flop</c>

00:04:23.790 --> 00:04:23.800 align:start position:0%
and it just never did so kind of a flop
 

00:04:23.800 --> 00:04:25.310 align:start position:0%
and it just never did so kind of a flop
though<00:04:24.000><c> I</c><00:04:24.120><c> thought</c><00:04:24.400><c> this</c><00:04:24.600><c> version</c><00:04:24.919><c> was</c><00:04:25.080><c> pretty</c>

00:04:25.310 --> 00:04:25.320 align:start position:0%
though I thought this version was pretty
 

00:04:25.320 --> 00:04:27.430 align:start position:0%
though I thought this version was pretty
interesting<00:04:25.960><c> next</c><00:04:26.199><c> up</c><00:04:26.400><c> we</c><00:04:26.560><c> have</c><00:04:26.759><c> wizard</c><00:04:27.080><c> LM</c>

00:04:27.430 --> 00:04:27.440 align:start position:0%
interesting next up we have wizard LM
 

00:04:27.440 --> 00:04:28.590 align:start position:0%
interesting next up we have wizard LM
which<00:04:27.520><c> you</c><00:04:27.600><c> may</c><00:04:27.720><c> not</c><00:04:27.800><c> have</c><00:04:27.919><c> heard</c><00:04:28.120><c> of</c><00:04:28.479><c> the</c>

00:04:28.590 --> 00:04:28.600 align:start position:0%
which you may not have heard of the
 

00:04:28.600 --> 00:04:30.909 align:start position:0%
which you may not have heard of the
initial<00:04:28.960><c> code</c><00:04:29.400><c> gave</c><00:04:29.560><c> me</c><00:04:29.880><c> compile</c><00:04:30.320><c> error</c><00:04:30.639><c> so</c><00:04:30.800><c> I</c>

00:04:30.909 --> 00:04:30.919 align:start position:0%
initial code gave me compile error so I
 

00:04:30.919 --> 00:04:32.430 align:start position:0%
initial code gave me compile error so I
asked<00:04:31.120><c> it</c><00:04:31.240><c> to</c><00:04:31.400><c> fix</c><00:04:31.680><c> that</c><00:04:31.840><c> and</c><00:04:31.960><c> tried</c><00:04:32.160><c> to</c><00:04:32.280><c> paste</c>

00:04:32.430 --> 00:04:32.440 align:start position:0%
asked it to fix that and tried to paste
 

00:04:32.440 --> 00:04:34.230 align:start position:0%
asked it to fix that and tried to paste
a<00:04:32.560><c> new</c><00:04:32.720><c> code</c><00:04:32.960><c> in</c><00:04:33.360><c> well</c><00:04:33.479><c> then</c><00:04:33.600><c> I</c><00:04:33.680><c> got</c><00:04:33.919><c> another</c>

00:04:34.230 --> 00:04:34.240 align:start position:0%
a new code in well then I got another
 

00:04:34.240 --> 00:04:36.510 align:start position:0%
a new code in well then I got another
compile<00:04:34.639><c> error</c><00:04:35.240><c> asked</c><00:04:35.479><c> agent</c><00:04:35.720><c> to</c><00:04:35.880><c> fix</c><00:04:36.240><c> that</c>

00:04:36.510 --> 00:04:36.520 align:start position:0%
compile error asked agent to fix that
 

00:04:36.520 --> 00:04:38.550 align:start position:0%
compile error asked agent to fix that
and<00:04:36.680><c> then</c><00:04:36.840><c> I</c><00:04:37.039><c> finally</c><00:04:37.320><c> got</c><00:04:37.520><c> code</c><00:04:38.120><c> or</c><00:04:38.280><c> it</c><00:04:38.360><c> would</c>

00:04:38.550 --> 00:04:38.560 align:start position:0%
and then I finally got code or it would
 

00:04:38.560 --> 00:04:41.550 align:start position:0%
and then I finally got code or it would
actually<00:04:39.000><c> run</c><00:04:40.000><c> sort</c><00:04:40.280><c> of</c><00:04:41.000><c> however</c><00:04:41.240><c> this</c><00:04:41.360><c> time</c>

00:04:41.550 --> 00:04:41.560 align:start position:0%
actually run sort of however this time
 

00:04:41.560 --> 00:04:43.590 align:start position:0%
actually run sort of however this time
when<00:04:41.680><c> it</c><00:04:41.840><c> actually</c><00:04:42.120><c> ran</c><00:04:42.720><c> it</c><00:04:42.880><c> immediately</c><00:04:43.280><c> quit</c>

00:04:43.590 --> 00:04:43.600 align:start position:0%
when it actually ran it immediately quit
 

00:04:43.600 --> 00:04:45.790 align:start position:0%
when it actually ran it immediately quit
due<00:04:43.759><c> to</c><00:04:44.000><c> another</c><00:04:44.400><c> error</c><00:04:45.039><c> I</c><00:04:45.120><c> tried</c><00:04:45.400><c> up</c><00:04:45.560><c> to</c><00:04:45.680><c> the</c>

00:04:45.790 --> 00:04:45.800 align:start position:0%
due to another error I tried up to the
 

00:04:45.800 --> 00:04:47.749 align:start position:0%
due to another error I tried up to the
lotted<00:04:46.199><c> five</c><00:04:46.440><c> times</c><00:04:46.720><c> to</c><00:04:46.880><c> have</c><00:04:47.039><c> the</c><00:04:47.160><c> model</c><00:04:47.400><c> fix</c>

00:04:47.749 --> 00:04:47.759 align:start position:0%
lotted five times to have the model fix
 

00:04:47.759 --> 00:04:50.110 align:start position:0%
lotted five times to have the model fix
this<00:04:48.320><c> same</c><00:04:48.520><c> thing</c><00:04:48.759><c> happened</c><00:04:49.560><c> this</c><00:04:49.800><c> was</c>

00:04:50.110 --> 00:04:50.120 align:start position:0%
this same thing happened this was
 

00:04:50.120 --> 00:04:52.629 align:start position:0%
this same thing happened this was
another<00:04:50.479><c> flop</c><00:04:51.120><c> lastly</c><00:04:51.479><c> we</c><00:04:51.639><c> have</c><00:04:51.800><c> the</c><00:04:52.000><c> quen</c>

00:04:52.629 --> 00:04:52.639 align:start position:0%
another flop lastly we have the quen
 

00:04:52.639 --> 00:04:54.390 align:start position:0%
another flop lastly we have the quen
model<00:04:53.080><c> for</c><00:04:53.360><c> this</c><00:04:53.520><c> last</c><00:04:53.759><c> Model</c><00:04:54.000><c> and</c><00:04:54.080><c> I</c><00:04:54.160><c> say</c><00:04:54.320><c> this</c>

00:04:54.390 --> 00:04:54.400 align:start position:0%
model for this last Model and I say this
 

00:04:54.400 --> 00:04:57.550 align:start position:0%
model for this last Model and I say this
one<00:04:54.520><c> for</c><00:04:54.759><c> last</c><00:04:55.560><c> this</c><00:04:55.840><c> one</c><00:04:56.840><c> really</c><00:04:57.080><c> didn't</c><00:04:57.320><c> work</c>

00:04:57.550 --> 00:04:57.560 align:start position:0%
one for last this one really didn't work
 

00:04:57.560 --> 00:05:00.230 align:start position:0%
one for last this one really didn't work
or<00:04:57.800><c> compile</c><00:04:58.360><c> at</c><00:04:58.560><c> all</c><00:04:59.000><c> this</c><00:04:59.120><c> was</c><00:04:59.400><c> l</c><00:05:00.080><c> with</c>

00:05:00.230 --> 00:05:00.240 align:start position:0%
or compile at all this was l with
 

00:05:00.240 --> 00:05:02.270 align:start position:0%
or compile at all this was l with
spelling<00:05:00.600><c> mistakes</c><00:05:01.000><c> that</c><00:05:01.120><c> I</c><00:05:01.199><c> fixed</c><00:05:01.600><c> against</c>

00:05:02.270 --> 00:05:02.280 align:start position:0%
spelling mistakes that I fixed against
 

00:05:02.280 --> 00:05:03.830 align:start position:0%
spelling mistakes that I fixed against
Pro<00:05:02.560><c> going</c><00:05:02.800><c> against</c><00:05:03.000><c> my</c><00:05:03.120><c> rules</c><00:05:03.400><c> to</c><00:05:03.520><c> try</c><00:05:03.680><c> and</c>

00:05:03.830 --> 00:05:03.840 align:start position:0%
Pro going against my rules to try and
 

00:05:03.840 --> 00:05:05.950 align:start position:0%
Pro going against my rules to try and
help<00:05:04.080><c> it</c><00:05:05.080><c> it</c><00:05:05.160><c> would</c><00:05:05.320><c> just</c><00:05:05.440><c> keep</c><00:05:05.600><c> adding</c><00:05:05.840><c> more</c>

00:05:05.950 --> 00:05:05.960 align:start position:0%
help it it would just keep adding more
 

00:05:05.960 --> 00:05:07.990 align:start position:0%
help it it would just keep adding more
slashes<00:05:06.479><c> and</c><00:05:06.639><c> parentheses</c><00:05:07.199><c> than</c><00:05:07.360><c> needed</c><00:05:07.880><c> and</c>

00:05:07.990 --> 00:05:08.000 align:start position:0%
slashes and parentheses than needed and
 

00:05:08.000 --> 00:05:10.270 align:start position:0%
slashes and parentheses than needed and
it<00:05:08.120><c> wouldn't</c><00:05:08.479><c> fix</c><00:05:08.720><c> it</c><00:05:09.120><c> I</c><00:05:09.240><c> asked</c><00:05:09.520><c> it</c><00:05:09.639><c> to</c><00:05:10.039><c> just</c>

00:05:10.270 --> 00:05:10.280 align:start position:0%
it wouldn't fix it I asked it to just
 

00:05:10.280 --> 00:05:13.870 align:start position:0%
it wouldn't fix it I asked it to just
even<00:05:10.600><c> import</c><00:05:11.080><c> the</c><00:05:11.320><c> P</c><00:05:11.600><c> game</c><00:05:12.160><c> at</c><00:05:12.320><c> the</c><00:05:12.759><c> top</c><00:05:13.759><c> it</c>

00:05:13.870 --> 00:05:13.880 align:start position:0%
even import the P game at the top it
 

00:05:13.880 --> 00:05:15.670 align:start position:0%
even import the P game at the top it
said<00:05:14.120><c> it</c><00:05:14.280><c> did</c><00:05:14.759><c> and</c><00:05:14.880><c> then</c><00:05:15.039><c> it</c><00:05:15.160><c> never</c><00:05:15.360><c> actually</c>

00:05:15.670 --> 00:05:15.680 align:start position:0%
said it did and then it never actually
 

00:05:15.680 --> 00:05:17.670 align:start position:0%
said it did and then it never actually
did<00:05:16.120><c> I</c><00:05:16.240><c> ended</c><00:05:16.479><c> up</c><00:05:16.639><c> just</c><00:05:16.960><c> completely</c><00:05:17.320><c> giving</c><00:05:17.560><c> up</c>

00:05:17.670 --> 00:05:17.680 align:start position:0%
did I ended up just completely giving up
 

00:05:17.680 --> 00:05:19.870 align:start position:0%
did I ended up just completely giving up
on<00:05:17.800><c> this</c><00:05:17.960><c> model</c><00:05:18.319><c> and</c><00:05:18.560><c> this</c><00:05:19.000><c> is</c><00:05:19.160><c> the</c><00:05:19.319><c> one</c><00:05:19.680><c> that</c>

00:05:19.870 --> 00:05:19.880 align:start position:0%
on this model and this is the one that
 

00:05:19.880 --> 00:05:23.070 align:start position:0%
on this model and this is the one that
just<00:05:20.280><c> never</c><00:05:20.759><c> even</c><00:05:21.039><c> ran</c><00:05:21.440><c> a</c><00:05:21.600><c> snake</c><00:05:21.960><c> game</c><00:05:22.639><c> okay</c>

00:05:23.070 --> 00:05:23.080 align:start position:0%
just never even ran a snake game okay
 

00:05:23.080 --> 00:05:24.670 align:start position:0%
just never even ran a snake game okay
thank<00:05:23.240><c> you</c><00:05:23.319><c> for</c><00:05:23.520><c> watching</c><00:05:24.039><c> I</c><00:05:24.160><c> have</c><00:05:24.400><c> all</c><00:05:24.560><c> the</c>

00:05:24.670 --> 00:05:24.680 align:start position:0%
thank you for watching I have all the
 

00:05:24.680 --> 00:05:27.629 align:start position:0%
thank you for watching I have all the
models<00:05:25.280><c> in</c><00:05:25.400><c> the</c><00:05:25.560><c> description</c><00:05:26.360><c> and</c><00:05:26.720><c> all</c><00:05:27.039><c> the</c>

00:05:27.629 --> 00:05:27.639 align:start position:0%
models in the description and all the
 

00:05:27.639 --> 00:05:30.150 align:start position:0%
models in the description and all the
python<00:05:28.120><c> outputs</c><00:05:28.720><c> that</c><00:05:28.919><c> I</c><00:05:29.080><c> got</c><00:05:29.319><c> from</c><00:05:29.680><c> autogen</c>

00:05:30.150 --> 00:05:30.160 align:start position:0%
python outputs that I got from autogen
 

00:05:30.160 --> 00:05:32.270 align:start position:0%
python outputs that I got from autogen
for<00:05:30.319><c> each</c><00:05:30.440><c> of</c><00:05:30.560><c> the</c><00:05:30.720><c> models</c><00:05:31.360><c> will</c><00:05:31.639><c> be</c><00:05:31.840><c> in</c><00:05:32.000><c> my</c>

00:05:32.270 --> 00:05:32.280 align:start position:0%
for each of the models will be in my
 

00:05:32.280 --> 00:05:34.230 align:start position:0%
for each of the models will be in my
GitHub<00:05:32.880><c> in</c><00:05:33.039><c> my</c><00:05:33.240><c> AI</c><00:05:33.680><c> project</c><00:05:34.000><c> in</c><00:05:34.120><c> the</c>

00:05:34.230 --> 00:05:34.240 align:start position:0%
GitHub in my AI project in the
 

00:05:34.240 --> 00:05:36.029 align:start position:0%
GitHub in my AI project in the
description<00:05:34.880><c> please</c><00:05:35.120><c> like</c><00:05:35.240><c> And</c><00:05:35.400><c> subscribe</c>

00:05:36.029 --> 00:05:36.039 align:start position:0%
description please like And subscribe
 

00:05:36.039 --> 00:05:37.670 align:start position:0%
description please like And subscribe
join<00:05:36.280><c> my</c><00:05:36.479><c> Discord</c><00:05:36.800><c> Community</c><00:05:37.199><c> which</c><00:05:37.319><c> will</c><00:05:37.479><c> be</c>

00:05:37.670 --> 00:05:37.680 align:start position:0%
join my Discord Community which will be
 

00:05:37.680 --> 00:05:39.350 align:start position:0%
join my Discord Community which will be
in<00:05:37.759><c> the</c><00:05:37.840><c> description</c><00:05:38.319><c> as</c><00:05:38.440><c> well</c><00:05:38.919><c> here</c><00:05:39.080><c> are</c><00:05:39.240><c> a</c>

00:05:39.350 --> 00:05:39.360 align:start position:0%
in the description as well here are a
 

00:05:39.360 --> 00:05:41.189 align:start position:0%
in the description as well here are a
couple<00:05:39.680><c> AI</c><00:05:40.000><c> courses</c><00:05:40.440><c> that</c><00:05:40.560><c> you</c><00:05:40.680><c> can</c><00:05:40.840><c> take</c><00:05:41.080><c> that</c>

00:05:41.189 --> 00:05:41.199 align:start position:0%
couple AI courses that you can take that
 

00:05:41.199 --> 00:05:43.590 align:start position:0%
couple AI courses that you can take that
are<00:05:41.440><c> completely</c><00:05:41.800><c> free</c><00:05:42.120><c> on</c><00:05:42.319><c> YouTube</c><00:05:43.080><c> again</c>

00:05:43.590 --> 00:05:43.600 align:start position:0%
are completely free on YouTube again
 

00:05:43.600 --> 00:05:45.150 align:start position:0%
are completely free on YouTube again
thank<00:05:43.759><c> you</c><00:05:43.840><c> for</c><00:05:44.039><c> watching</c><00:05:44.479><c> I'll</c><00:05:44.600><c> see</c><00:05:44.759><c> you</c><00:05:44.919><c> next</c>

00:05:45.150 --> 00:05:45.160 align:start position:0%
thank you for watching I'll see you next
 

00:05:45.160 --> 00:05:47.600 align:start position:0%
thank you for watching I'll see you next
video

