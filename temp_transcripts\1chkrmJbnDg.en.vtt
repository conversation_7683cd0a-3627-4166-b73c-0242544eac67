WEBVTT
Kind: captions
Language: en

00:00:01.040 --> 00:00:02.950 align:start position:0%
 
and<00:00:01.360><c> howdy</c><00:00:01.680><c> guys</c><00:00:01.920><c> so</c><00:00:02.080><c> in</c><00:00:02.240><c> the</c><00:00:02.320><c> last</c><00:00:02.560><c> video</c><00:00:02.800><c> we</c>

00:00:02.950 --> 00:00:02.960 align:start position:0%
and howdy guys so in the last video we
 

00:00:02.960 --> 00:00:03.830 align:start position:0%
and howdy guys so in the last video we
started<00:00:03.199><c> to</c><00:00:03.360><c> set</c><00:00:03.600><c> up</c>

00:00:03.830 --> 00:00:03.840 align:start position:0%
started to set up
 

00:00:03.840 --> 00:00:06.950 align:start position:0%
started to set up
our<00:00:04.560><c> user</c><00:00:04.960><c> interface</c><00:00:05.680><c> and</c><00:00:05.920><c> updating</c><00:00:06.560><c> our</c>

00:00:06.950 --> 00:00:06.960 align:start position:0%
our user interface and updating our
 

00:00:06.960 --> 00:00:08.710 align:start position:0%
our user interface and updating our
state<00:00:07.279><c> within</c><00:00:07.600><c> the</c><00:00:07.759><c> actual</c><00:00:08.240><c> settings</c>

00:00:08.710 --> 00:00:08.720 align:start position:0%
state within the actual settings
 

00:00:08.720 --> 00:00:09.589 align:start position:0%
state within the actual settings
component

00:00:09.589 --> 00:00:09.599 align:start position:0%
component
 

00:00:09.599 --> 00:00:11.749 align:start position:0%
component
whenever<00:00:10.240><c> somebody</c><00:00:10.719><c> set</c><00:00:10.960><c> up</c><00:00:11.200><c> whenever</c>

00:00:11.749 --> 00:00:11.759 align:start position:0%
whenever somebody set up whenever
 

00:00:11.759 --> 00:00:13.589 align:start position:0%
whenever somebody set up whenever
somebody<00:00:12.160><c> actually</c><00:00:12.639><c> clicked</c><00:00:12.960><c> right</c><00:00:13.200><c> so</c>

00:00:13.589 --> 00:00:13.599 align:start position:0%
somebody actually clicked right so
 

00:00:13.599 --> 00:00:16.310 align:start position:0%
somebody actually clicked right so
click<00:00:13.920><c> post</c><00:00:14.799><c> click</c><00:00:15.040><c> update</c><00:00:15.440><c> post</c><00:00:15.679><c> moderation</c>

00:00:16.310 --> 00:00:16.320 align:start position:0%
click post click update post moderation
 

00:00:16.320 --> 00:00:17.830 align:start position:0%
click post click update post moderation
click<00:00:16.560><c> comment</c><00:00:16.960><c> moderation</c>

00:00:17.830 --> 00:00:17.840 align:start position:0%
click comment moderation
 

00:00:17.840 --> 00:00:20.070 align:start position:0%
click comment moderation
and<00:00:18.000><c> that</c><00:00:18.320><c> runs</c><00:00:18.640><c> functions</c><00:00:19.279><c> good</c><00:00:19.760><c> now</c><00:00:19.920><c> we</c>

00:00:20.070 --> 00:00:20.080 align:start position:0%
and that runs functions good now we
 

00:00:20.080 --> 00:00:21.590 align:start position:0%
and that runs functions good now we
actually<00:00:20.400><c> need</c><00:00:20.480><c> to</c><00:00:20.640><c> send</c><00:00:20.800><c> that</c><00:00:20.960><c> data</c><00:00:21.279><c> back</c><00:00:21.439><c> to</c>

00:00:21.590 --> 00:00:21.600 align:start position:0%
actually need to send that data back to
 

00:00:21.600 --> 00:00:23.750 align:start position:0%
actually need to send that data back to
the<00:00:21.680><c> back</c><00:00:22.000><c> end</c><00:00:22.160><c> but</c><00:00:22.320><c> before</c><00:00:22.560><c> we</c><00:00:22.720><c> can</c><00:00:22.880><c> actually</c>

00:00:23.750 --> 00:00:23.760 align:start position:0%
the back end but before we can actually
 

00:00:23.760 --> 00:00:25.429 align:start position:0%
the back end but before we can actually
go<00:00:23.920><c> ahead</c><00:00:24.160><c> and</c><00:00:24.320><c> implement</c><00:00:24.800><c> it</c><00:00:24.960><c> i</c><00:00:25.039><c> just</c><00:00:25.119><c> wanted</c>

00:00:25.429 --> 00:00:25.439 align:start position:0%
go ahead and implement it i just wanted
 

00:00:25.439 --> 00:00:27.029 align:start position:0%
go ahead and implement it i just wanted
to<00:00:25.519><c> take</c><00:00:25.760><c> a</c><00:00:25.840><c> minute</c><00:00:26.080><c> to</c><00:00:26.240><c> talk</c><00:00:26.480><c> about</c>

00:00:27.029 --> 00:00:27.039 align:start position:0%
to take a minute to talk about
 

00:00:27.039 --> 00:00:29.349 align:start position:0%
to take a minute to talk about
the<00:00:27.279><c> actual</c><00:00:27.760><c> architecture</c><00:00:28.720><c> okay</c><00:00:29.039><c> and</c><00:00:29.119><c> this</c><00:00:29.279><c> is</c>

00:00:29.349 --> 00:00:29.359 align:start position:0%
the actual architecture okay and this is
 

00:00:29.359 --> 00:00:30.790 align:start position:0%
the actual architecture okay and this is
the<00:00:29.519><c> architecture</c><00:00:30.080><c> across</c>

00:00:30.790 --> 00:00:30.800 align:start position:0%
the architecture across
 

00:00:30.800 --> 00:00:33.750 align:start position:0%
the architecture across
the<00:00:30.960><c> entire</c><00:00:31.439><c> admin</c><00:00:31.920><c> app</c><00:00:32.160><c> the</c><00:00:32.320><c> store</c><00:00:32.640><c> admin</c><00:00:33.120><c> app</c>

00:00:33.750 --> 00:00:33.760 align:start position:0%
the entire admin app the store admin app
 

00:00:33.760 --> 00:00:34.389 align:start position:0%
the entire admin app the store admin app
and

00:00:34.389 --> 00:00:34.399 align:start position:0%
and
 

00:00:34.399 --> 00:00:35.910 align:start position:0%
and
if<00:00:34.480><c> you</c><00:00:34.559><c> have</c><00:00:34.719><c> any</c><00:00:34.960><c> questions</c><00:00:35.440><c> feel</c><00:00:35.600><c> free</c><00:00:35.760><c> to</c>

00:00:35.910 --> 00:00:35.920 align:start position:0%
if you have any questions feel free to
 

00:00:35.920 --> 00:00:37.590 align:start position:0%
if you have any questions feel free to
reach<00:00:36.239><c> out</c><00:00:36.399><c> about</c><00:00:36.640><c> it</c><00:00:36.800><c> but</c><00:00:36.960><c> this</c><00:00:37.200><c> is</c><00:00:37.280><c> kind</c><00:00:37.520><c> of</c>

00:00:37.590 --> 00:00:37.600 align:start position:0%
reach out about it but this is kind of
 

00:00:37.600 --> 00:00:39.190 align:start position:0%
reach out about it but this is kind of
the<00:00:37.680><c> way</c><00:00:37.840><c> that</c><00:00:38.079><c> it</c><00:00:38.160><c> looks</c><00:00:38.399><c> right</c><00:00:38.640><c> now</c>

00:00:39.190 --> 00:00:39.200 align:start position:0%
the way that it looks right now
 

00:00:39.200 --> 00:00:40.709 align:start position:0%
the way that it looks right now
okay<00:00:39.600><c> let's</c><00:00:39.760><c> say</c><00:00:39.920><c> we</c><00:00:40.000><c> have</c><00:00:40.160><c> the</c><00:00:40.239><c> settings</c>

00:00:40.709 --> 00:00:40.719 align:start position:0%
okay let's say we have the settings
 

00:00:40.719 --> 00:00:43.110 align:start position:0%
okay let's say we have the settings
component<00:00:41.680><c> a</c><00:00:41.840><c> function</c><00:00:42.239><c> is</c><00:00:42.480><c> imported</c>

00:00:43.110 --> 00:00:43.120 align:start position:0%
component a function is imported
 

00:00:43.120 --> 00:00:45.510 align:start position:0%
component a function is imported
into<00:00:43.360><c> that</c><00:00:43.520><c> component</c><00:00:44.160><c> and</c><00:00:44.320><c> it's</c><00:00:44.480><c> an</c><00:00:44.640><c> http</c>

00:00:45.510 --> 00:00:45.520 align:start position:0%
into that component and it's an http
 

00:00:45.520 --> 00:00:47.510 align:start position:0%
into that component and it's an http
action<00:00:46.000><c> from</c><00:00:46.239><c> the</c><00:00:46.480><c> actions</c>

00:00:47.510 --> 00:00:47.520 align:start position:0%
action from the actions
 

00:00:47.520 --> 00:00:50.630 align:start position:0%
action from the actions
folder<00:00:48.320><c> okay</c><00:00:48.719><c> so</c><00:00:48.960><c> all</c><00:00:49.120><c> the</c><00:00:49.280><c> http</c><00:00:50.160><c> is</c><00:00:50.320><c> always</c>

00:00:50.630 --> 00:00:50.640 align:start position:0%
folder okay so all the http is always
 

00:00:50.640 --> 00:00:52.709 align:start position:0%
folder okay so all the http is always
isolated<00:00:51.360><c> to</c><00:00:51.520><c> our</c><00:00:51.760><c> actions</c><00:00:52.239><c> folder</c>

00:00:52.709 --> 00:00:52.719 align:start position:0%
isolated to our actions folder
 

00:00:52.719 --> 00:00:55.270 align:start position:0%
isolated to our actions folder
just<00:00:53.280><c> according</c><00:00:53.680><c> to</c><00:00:53.840><c> the</c><00:00:53.920><c> feature</c><00:00:54.800><c> feature</c>

00:00:55.270 --> 00:00:55.280 align:start position:0%
just according to the feature feature
 

00:00:55.280 --> 00:00:56.869 align:start position:0%
just according to the feature feature
set<00:00:55.520><c> and</c><00:00:55.600><c> that</c><00:00:55.680><c> makes</c><00:00:55.920><c> it</c><00:00:56.079><c> a</c><00:00:56.079><c> lot</c><00:00:56.320><c> cleaner</c><00:00:56.719><c> for</c>

00:00:56.869 --> 00:00:56.879 align:start position:0%
set and that makes it a lot cleaner for
 

00:00:56.879 --> 00:00:57.510 align:start position:0%
set and that makes it a lot cleaner for
us<00:00:57.039><c> right</c>

00:00:57.510 --> 00:00:57.520 align:start position:0%
us right
 

00:00:57.520 --> 00:00:58.869 align:start position:0%
us right
that's<00:00:57.840><c> imported</c><00:00:58.160><c> into</c><00:00:58.399><c> the</c><00:00:58.480><c> settings</c>

00:00:58.869 --> 00:00:58.879 align:start position:0%
that's imported into the settings
 

00:00:58.879 --> 00:01:01.029 align:start position:0%
that's imported into the settings
component<00:00:59.520><c> and</c><00:00:59.680><c> now</c><00:00:59.920><c> we</c><00:01:00.079><c> want</c><00:01:00.239><c> to</c><00:01:00.320><c> do</c>

00:01:01.029 --> 00:01:01.039 align:start position:0%
component and now we want to do
 

00:01:01.039 --> 00:01:04.070 align:start position:0%
component and now we want to do
is<00:01:01.199><c> that</c><00:01:01.440><c> when</c><00:01:01.680><c> somebody</c><00:01:02.160><c> actually</c><00:01:02.719><c> clicks</c>

00:01:04.070 --> 00:01:04.080 align:start position:0%
is that when somebody actually clicks
 

00:01:04.080 --> 00:01:06.789 align:start position:0%
is that when somebody actually clicks
update<00:01:04.720><c> the</c><00:01:04.960><c> post</c><00:01:05.519><c> moderation</c><00:01:06.400><c> it's</c><00:01:06.560><c> going</c><00:01:06.640><c> to</c>

00:01:06.789 --> 00:01:06.799 align:start position:0%
update the post moderation it's going to
 

00:01:06.799 --> 00:01:07.510 align:start position:0%
update the post moderation it's going to
send<00:01:07.040><c> a</c>

00:01:07.510 --> 00:01:07.520 align:start position:0%
send a
 

00:01:07.520 --> 00:01:10.070 align:start position:0%
send a
http<00:01:08.159><c> request</c><00:01:08.479><c> to</c><00:01:08.640><c> our</c><00:01:08.880><c> routes</c><00:01:09.600><c> the</c><00:01:09.760><c> route</c>

00:01:10.070 --> 00:01:10.080 align:start position:0%
http request to our routes the route
 

00:01:10.080 --> 00:01:10.870 align:start position:0%
http request to our routes the route
says<00:01:10.479><c> hey</c>

00:01:10.870 --> 00:01:10.880 align:start position:0%
says hey
 

00:01:10.880 --> 00:01:12.070 align:start position:0%
says hey
somebody<00:01:11.200><c> wants</c><00:01:11.439><c> to</c><00:01:11.600><c> update</c><00:01:11.840><c> their</c>

00:01:12.070 --> 00:01:12.080 align:start position:0%
somebody wants to update their
 

00:01:12.080 --> 00:01:13.990 align:start position:0%
somebody wants to update their
moderation<00:01:12.640><c> request</c><00:01:13.119><c> okay</c><00:01:13.520><c> do</c><00:01:13.680><c> i</c><00:01:13.760><c> have</c><00:01:13.920><c> a</c>

00:01:13.990 --> 00:01:14.000 align:start position:0%
moderation request okay do i have a
 

00:01:14.000 --> 00:01:15.510 align:start position:0%
moderation request okay do i have a
controller<00:01:14.560><c> function</c>

00:01:15.510 --> 00:01:15.520 align:start position:0%
controller function
 

00:01:15.520 --> 00:01:17.510 align:start position:0%
controller function
do<00:01:15.680><c> i</c><00:01:15.759><c> have</c><00:01:15.920><c> a</c><00:01:16.000><c> function</c><00:01:16.479><c> for</c><00:01:16.640><c> my</c><00:01:16.799><c> controllers</c>

00:01:17.510 --> 00:01:17.520 align:start position:0%
do i have a function for my controllers
 

00:01:17.520 --> 00:01:19.510 align:start position:0%
do i have a function for my controllers
folder<00:01:17.840><c> which</c><00:01:18.080><c> actually</c><00:01:18.560><c> does</c><00:01:18.799><c> that</c>

00:01:19.510 --> 00:01:19.520 align:start position:0%
folder which actually does that
 

00:01:19.520 --> 00:01:21.749 align:start position:0%
folder which actually does that
and<00:01:19.680><c> that</c><00:01:20.080><c> is</c><00:01:20.240><c> the</c><00:01:20.400><c> function</c><00:01:21.040><c> that</c><00:01:21.280><c> we</c><00:01:21.600><c> are</c>

00:01:21.749 --> 00:01:21.759 align:start position:0%
and that is the function that we are
 

00:01:21.759 --> 00:01:23.510 align:start position:0%
and that is the function that we are
going<00:01:22.000><c> to</c><00:01:22.159><c> create</c><00:01:22.640><c> it's</c><00:01:22.799><c> a</c><00:01:22.880><c> function</c><00:01:23.200><c> from</c><00:01:23.360><c> the</c>

00:01:23.510 --> 00:01:23.520 align:start position:0%
going to create it's a function from the
 

00:01:23.520 --> 00:01:25.030 align:start position:0%
going to create it's a function from the
controllers<00:01:24.159><c> folder</c>

00:01:25.030 --> 00:01:25.040 align:start position:0%
controllers folder
 

00:01:25.040 --> 00:01:27.510 align:start position:0%
controllers folder
and<00:01:25.280><c> we're</c><00:01:25.439><c> going</c><00:01:25.520><c> to</c><00:01:25.680><c> import</c><00:01:26.080><c> that</c><00:01:26.400><c> into</c><00:01:26.799><c> our</c>

00:01:27.510 --> 00:01:27.520 align:start position:0%
and we're going to import that into our
 

00:01:27.520 --> 00:01:28.469 align:start position:0%
and we're going to import that into our
routes<00:01:28.000><c> file</c>

00:01:28.469 --> 00:01:28.479 align:start position:0%
routes file
 

00:01:28.479 --> 00:01:30.469 align:start position:0%
routes file
and<00:01:28.640><c> that's</c><00:01:28.960><c> how</c><00:01:29.439><c> these</c><00:01:29.920><c> settings</c><00:01:30.320><c> the</c>

00:01:30.469 --> 00:01:30.479 align:start position:0%
and that's how these settings the
 

00:01:30.479 --> 00:01:32.710 align:start position:0%
and that's how these settings the
settings<00:01:30.799><c> component</c><00:01:31.360><c> and</c><00:01:31.439><c> the</c><00:01:31.680><c> routes</c>

00:01:32.710 --> 00:01:32.720 align:start position:0%
settings component and the routes
 

00:01:32.720 --> 00:01:34.789 align:start position:0%
settings component and the routes
file<00:01:33.439><c> are</c><00:01:33.600><c> going</c><00:01:33.680><c> to</c><00:01:33.759><c> be</c><00:01:33.920><c> able</c><00:01:34.159><c> to</c><00:01:34.320><c> talk</c><00:01:34.560><c> with</c>

00:01:34.789 --> 00:01:34.799 align:start position:0%
file are going to be able to talk with
 

00:01:34.799 --> 00:01:36.950 align:start position:0%
file are going to be able to talk with
one<00:01:35.040><c> another</c><00:01:35.600><c> all</c><00:01:35.680><c> right</c><00:01:35.920><c> guys</c><00:01:36.159><c> so</c><00:01:36.320><c> that's</c><00:01:36.640><c> how</c>

00:01:36.950 --> 00:01:36.960 align:start position:0%
one another all right guys so that's how
 

00:01:36.960 --> 00:01:38.710 align:start position:0%
one another all right guys so that's how
actually<00:01:37.360><c> how</c><00:01:37.520><c> our</c><00:01:37.759><c> admin</c><00:01:38.079><c> front</c><00:01:38.320><c> end</c><00:01:38.479><c> and</c><00:01:38.560><c> our</c>

00:01:38.710 --> 00:01:38.720 align:start position:0%
actually how our admin front end and our
 

00:01:38.720 --> 00:01:40.870 align:start position:0%
actually how our admin front end and our
admin<00:01:39.119><c> backend</c><00:01:39.600><c> talk</c><00:01:39.840><c> with</c><00:01:40.000><c> one</c><00:01:40.240><c> another</c>

00:01:40.870 --> 00:01:40.880 align:start position:0%
admin backend talk with one another
 

00:01:40.880 --> 00:01:42.230 align:start position:0%
admin backend talk with one another
you<00:01:41.040><c> can</c><00:01:41.200><c> kind</c><00:01:41.360><c> of</c><00:01:41.439><c> think</c><00:01:41.680><c> of</c><00:01:41.759><c> it</c><00:01:41.920><c> as</c><00:01:42.079><c> the</c>

00:01:42.230 --> 00:01:42.240 align:start position:0%
you can kind of think of it as the
 

00:01:42.240 --> 00:01:44.469 align:start position:0%
you can kind of think of it as the
settings<00:01:42.640><c> component</c><00:01:43.280><c> and</c><00:01:43.600><c> and</c><00:01:43.759><c> the</c><00:01:44.000><c> routes</c>

00:01:44.469 --> 00:01:44.479 align:start position:0%
settings component and and the routes
 

00:01:44.479 --> 00:01:46.149 align:start position:0%
settings component and and the routes
kind<00:01:44.640><c> of</c><00:01:44.720><c> like</c><00:01:44.960><c> being</c><00:01:45.200><c> the</c><00:01:45.360><c> bosses</c><00:01:45.840><c> the</c><00:01:45.920><c> guys</c>

00:01:46.149 --> 00:01:46.159 align:start position:0%
kind of like being the bosses the guys
 

00:01:46.159 --> 00:01:48.550 align:start position:0%
kind of like being the bosses the guys
that<00:01:46.399><c> are</c><00:01:46.560><c> in</c><00:01:46.720><c> charge</c><00:01:47.439><c> but</c><00:01:47.600><c> their</c><00:01:47.920><c> helpers</c>

00:01:48.550 --> 00:01:48.560 align:start position:0%
that are in charge but their helpers
 

00:01:48.560 --> 00:01:50.789 align:start position:0%
that are in charge but their helpers
are<00:01:48.720><c> the</c><00:01:49.360><c> are</c><00:01:49.600><c> the</c><00:01:49.840><c> are</c><00:01:50.000><c> the</c><00:01:50.159><c> functions</c><00:01:50.640><c> from</c>

00:01:50.789 --> 00:01:50.799 align:start position:0%
are the are the are the functions from
 

00:01:50.799 --> 00:01:51.749 align:start position:0%
are the are the are the functions from
the<00:01:50.960><c> folder</c>

00:01:51.749 --> 00:01:51.759 align:start position:0%
the folder
 

00:01:51.759 --> 00:01:54.469 align:start position:0%
the folder
um<00:01:53.119><c> from</c><00:01:53.280><c> the</c><00:01:53.439><c> actions</c><00:01:53.920><c> folder</c><00:01:54.240><c> and</c><00:01:54.399><c> the</c>

00:01:54.469 --> 00:01:54.479 align:start position:0%
um from the actions folder and the
 

00:01:54.479 --> 00:01:55.990 align:start position:0%
um from the actions folder and the
functions<00:01:54.880><c> from</c><00:01:55.040><c> the</c><00:01:55.200><c> controller</c>

00:01:55.990 --> 00:01:56.000 align:start position:0%
functions from the controller
 

00:01:56.000 --> 00:01:58.469 align:start position:0%
functions from the controller
folders<00:01:56.560><c> all</c><00:01:56.640><c> right</c><00:01:56.799><c> so</c><00:01:56.960><c> that's</c><00:01:57.200><c> why</c><00:01:57.759><c> all</c><00:01:57.920><c> the</c>

00:01:58.469 --> 00:01:58.479 align:start position:0%
folders all right so that's why all the
 

00:01:58.479 --> 00:01:59.190 align:start position:0%
folders all right so that's why all the
and<00:01:58.799><c> the</c>

00:01:59.190 --> 00:01:59.200 align:start position:0%
and the
 

00:01:59.200 --> 00:02:01.670 align:start position:0%
and the
the<00:01:59.360><c> controller</c><00:02:00.240><c> uh</c><00:02:00.640><c> function</c><00:02:01.200><c> the</c><00:02:01.360><c> function</c>

00:02:01.670 --> 00:02:01.680 align:start position:0%
the controller uh function the function
 

00:02:01.680 --> 00:02:03.749 align:start position:0%
the controller uh function the function
from<00:02:01.840><c> the</c><00:02:01.920><c> controller</c><00:02:02.479><c> and</c><00:02:02.560><c> the</c><00:02:02.640><c> actions</c>

00:02:03.749 --> 00:02:03.759 align:start position:0%
from the controller and the actions
 

00:02:03.759 --> 00:02:05.830 align:start position:0%
from the controller and the actions
folder<00:02:04.479><c> are</c><00:02:04.719><c> pretty</c><00:02:04.960><c> much</c><00:02:05.200><c> uh</c><00:02:05.520><c> they're</c><00:02:05.680><c> the</c>

00:02:05.830 --> 00:02:05.840 align:start position:0%
folder are pretty much uh they're the
 

00:02:05.840 --> 00:02:07.429 align:start position:0%
folder are pretty much uh they're the
ones<00:02:06.000><c> that</c><00:02:06.159><c> are</c><00:02:06.320><c> actually</c><00:02:06.640><c> doing</c><00:02:06.960><c> most</c><00:02:07.200><c> of</c><00:02:07.360><c> the</c>

00:02:07.429 --> 00:02:07.439 align:start position:0%
ones that are actually doing most of the
 

00:02:07.439 --> 00:02:09.109 align:start position:0%
ones that are actually doing most of the
logic<00:02:07.920><c> but</c><00:02:08.000><c> we</c><00:02:08.160><c> don't</c><00:02:08.319><c> want</c><00:02:08.479><c> to</c><00:02:08.560><c> take</c><00:02:08.800><c> up</c><00:02:08.959><c> too</c>

00:02:09.109 --> 00:02:09.119 align:start position:0%
logic but we don't want to take up too
 

00:02:09.119 --> 00:02:10.869 align:start position:0%
logic but we don't want to take up too
much<00:02:09.280><c> space</c><00:02:09.599><c> within</c><00:02:09.920><c> the</c><00:02:10.000><c> settings</c><00:02:10.399><c> component</c>

00:02:10.869 --> 00:02:10.879 align:start position:0%
much space within the settings component
 

00:02:10.879 --> 00:02:11.750 align:start position:0%
much space within the settings component
and<00:02:10.959><c> the</c><00:02:11.120><c> routes</c>

00:02:11.750 --> 00:02:11.760 align:start position:0%
and the routes
 

00:02:11.760 --> 00:02:13.270 align:start position:0%
and the routes
component<00:02:12.239><c> so</c><00:02:12.400><c> we</c><00:02:12.480><c> just</c><00:02:12.800><c> import</c><00:02:13.120><c> the</c>

00:02:13.270 --> 00:02:13.280 align:start position:0%
component so we just import the
 

00:02:13.280 --> 00:02:15.030 align:start position:0%
component so we just import the
functions<00:02:13.920><c> into</c><00:02:14.160><c> those</c><00:02:14.480><c> files</c>

00:02:15.030 --> 00:02:15.040 align:start position:0%
functions into those files
 

00:02:15.040 --> 00:02:16.390 align:start position:0%
functions into those files
all<00:02:15.200><c> right</c><00:02:15.360><c> guys</c><00:02:15.520><c> so</c><00:02:15.680><c> that's</c><00:02:15.840><c> kind</c><00:02:16.000><c> of</c><00:02:16.080><c> how</c><00:02:16.239><c> it</c>

00:02:16.390 --> 00:02:16.400 align:start position:0%
all right guys so that's kind of how it
 

00:02:16.400 --> 00:02:18.070 align:start position:0%
all right guys so that's kind of how it
works<00:02:16.879><c> you</c><00:02:16.959><c> know</c><00:02:17.120><c> what</c><00:02:17.200><c> let's</c><00:02:17.440><c> actually</c><00:02:17.920><c> go</c>

00:02:18.070 --> 00:02:18.080 align:start position:0%
works you know what let's actually go
 

00:02:18.080 --> 00:02:18.790 align:start position:0%
works you know what let's actually go
ahead<00:02:18.319><c> and</c><00:02:18.480><c> look</c>

00:02:18.790 --> 00:02:18.800 align:start position:0%
ahead and look
 

00:02:18.800 --> 00:02:20.790 align:start position:0%
ahead and look
at<00:02:18.959><c> this</c><00:02:19.200><c> example</c><00:02:19.599><c> within</c><00:02:19.840><c> the</c><00:02:20.080><c> actual</c><00:02:20.560><c> code</c>

00:02:20.790 --> 00:02:20.800 align:start position:0%
at this example within the actual code
 

00:02:20.800 --> 00:02:23.190 align:start position:0%
at this example within the actual code
so<00:02:20.959><c> i'm</c><00:02:21.040><c> going</c><00:02:21.200><c> to</c><00:02:21.280><c> go</c><00:02:21.920><c> into</c><00:02:22.239><c> our</c><00:02:22.400><c> desktop</c><00:02:22.959><c> over</c>

00:02:23.190 --> 00:02:23.200 align:start position:0%
so i'm going to go into our desktop over
 

00:02:23.200 --> 00:02:23.670 align:start position:0%
so i'm going to go into our desktop over
here

00:02:23.670 --> 00:02:23.680 align:start position:0%
here
 

00:02:23.680 --> 00:02:27.830 align:start position:0%
here
into<00:02:24.000><c> sk</c><00:02:26.160><c> and</c><00:02:26.400><c> i'll</c><00:02:26.560><c> illustrate</c><00:02:27.040><c> this</c><00:02:27.280><c> example</c>

00:02:27.830 --> 00:02:27.840 align:start position:0%
into sk and i'll illustrate this example
 

00:02:27.840 --> 00:02:28.790 align:start position:0%
into sk and i'll illustrate this example
exactly<00:02:28.400><c> now</c>

00:02:28.790 --> 00:02:28.800 align:start position:0%
exactly now
 

00:02:28.800 --> 00:02:30.790 align:start position:0%
exactly now
with<00:02:29.120><c> with</c><00:02:29.360><c> the</c><00:02:29.520><c> help</c><00:02:29.840><c> of</c><00:02:30.080><c> what</c><00:02:30.239><c> we're</c><00:02:30.480><c> looking</c>

00:02:30.790 --> 00:02:30.800 align:start position:0%
with with the help of what we're looking
 

00:02:30.800 --> 00:02:31.910 align:start position:0%
with with the help of what we're looking
at<00:02:30.959><c> now</c>

00:02:31.910 --> 00:02:31.920 align:start position:0%
at now
 

00:02:31.920 --> 00:02:35.030 align:start position:0%
at now
all<00:02:32.080><c> right</c><00:02:32.480><c> so</c><00:02:33.040><c> um</c><00:02:33.599><c> let's</c><00:02:33.920><c> look</c><00:02:34.400><c> on</c><00:02:34.560><c> the</c><00:02:34.720><c> left</c>

00:02:35.030 --> 00:02:35.040 align:start position:0%
all right so um let's look on the left
 

00:02:35.040 --> 00:02:36.949 align:start position:0%
all right so um let's look on the left
side<00:02:35.280><c> for</c><00:02:35.440><c> a</c><00:02:35.519><c> sec</c><00:02:35.840><c> a</c><00:02:36.000><c> second</c><00:02:36.319><c> just</c><00:02:36.480><c> look</c><00:02:36.720><c> at</c><00:02:36.800><c> the</c>

00:02:36.949 --> 00:02:36.959 align:start position:0%
side for a sec a second just look at the
 

00:02:36.959 --> 00:02:38.630 align:start position:0%
side for a sec a second just look at the
actual<00:02:37.360><c> folder</c><00:02:37.760><c> structure</c>

00:02:38.630 --> 00:02:38.640 align:start position:0%
actual folder structure
 

00:02:38.640 --> 00:02:40.949 align:start position:0%
actual folder structure
we<00:02:38.800><c> have</c><00:02:39.120><c> admin</c><00:02:39.519><c> backend</c><00:02:40.000><c> and</c><00:02:40.160><c> admin</c><00:02:40.640><c> front</c>

00:02:40.949 --> 00:02:40.959 align:start position:0%
we have admin backend and admin front
 

00:02:40.959 --> 00:02:42.790 align:start position:0%
we have admin backend and admin front
end<00:02:41.360><c> all</c><00:02:41.440><c> right</c><00:02:41.680><c> so</c><00:02:41.840><c> within</c><00:02:42.080><c> our</c><00:02:42.239><c> admin</c><00:02:42.560><c> front</c>

00:02:42.790 --> 00:02:42.800 align:start position:0%
end all right so within our admin front
 

00:02:42.800 --> 00:02:45.270 align:start position:0%
end all right so within our admin front
end<00:02:42.959><c> we</c><00:02:43.120><c> have</c><00:02:43.200><c> a</c><00:02:43.360><c> folder</c><00:02:43.760><c> called</c><00:02:44.080><c> actions</c>

00:02:45.270 --> 00:02:45.280 align:start position:0%
end we have a folder called actions
 

00:02:45.280 --> 00:02:46.949 align:start position:0%
end we have a folder called actions
and<00:02:45.440><c> that's</c><00:02:45.680><c> got</c><00:02:45.920><c> all</c><00:02:46.000><c> the</c><00:02:46.160><c> features</c><00:02:46.640><c> set</c>

00:02:46.949 --> 00:02:46.959 align:start position:0%
and that's got all the features set
 

00:02:46.959 --> 00:02:48.470 align:start position:0%
and that's got all the features set
according<00:02:47.680><c> to</c><00:02:48.080><c> the</c>

00:02:48.470 --> 00:02:48.480 align:start position:0%
according to the
 

00:02:48.480 --> 00:02:50.229 align:start position:0%
according to the
right<00:02:48.800><c> all</c><00:02:49.040><c> the</c><00:02:49.360><c> files</c><00:02:49.680><c> according</c><00:02:50.000><c> to</c><00:02:50.160><c> the</c>

00:02:50.229 --> 00:02:50.239 align:start position:0%
right all the files according to the
 

00:02:50.239 --> 00:02:51.670 align:start position:0%
right all the files according to the
feature<00:02:50.560><c> set</c><00:02:50.879><c> actions</c>

00:02:51.670 --> 00:02:51.680 align:start position:0%
feature set actions
 

00:02:51.680 --> 00:02:54.229 align:start position:0%
feature set actions
old<00:02:52.160><c> and</c><00:02:52.480><c> um</c><00:02:53.040><c> and</c><00:02:53.280><c> over</c><00:02:53.519><c> here</c><00:02:53.680><c> we</c><00:02:53.840><c> also</c><00:02:54.000><c> have</c>

00:02:54.229 --> 00:02:54.239 align:start position:0%
old and um and over here we also have
 

00:02:54.239 --> 00:02:55.910 align:start position:0%
old and um and over here we also have
pages<00:02:54.640><c> which</c><00:02:54.879><c> is</c><00:02:54.959><c> the</c><00:02:55.120><c> actual</c>

00:02:55.910 --> 00:02:55.920 align:start position:0%
pages which is the actual
 

00:02:55.920 --> 00:02:58.309 align:start position:0%
pages which is the actual
um<00:02:56.720><c> which</c><00:02:56.959><c> is</c><00:02:57.120><c> the</c><00:02:57.280><c> actual</c><00:02:57.680><c> component</c>

00:02:58.309 --> 00:02:58.319 align:start position:0%
um which is the actual component
 

00:02:58.319 --> 00:02:59.270 align:start position:0%
um which is the actual component
settings

00:02:59.270 --> 00:02:59.280 align:start position:0%
settings
 

00:02:59.280 --> 00:03:02.149 align:start position:0%
settings
and<00:02:59.519><c> index</c><00:03:00.000><c> okay</c><00:03:00.319><c> fine</c><00:03:01.120><c> right</c><00:03:01.360><c> it's</c><00:03:01.519><c> just</c><00:03:01.760><c> uh</c>

00:03:02.149 --> 00:03:02.159 align:start position:0%
and index okay fine right it's just uh
 

00:03:02.159 --> 00:03:04.229 align:start position:0%
and index okay fine right it's just uh
react<00:03:02.840><c> components</c>

00:03:04.229 --> 00:03:04.239 align:start position:0%
react components
 

00:03:04.239 --> 00:03:07.030 align:start position:0%
react components
next.js<00:03:04.879><c> components</c><00:03:05.440><c> right</c><00:03:06.080><c> and</c><00:03:06.400><c> uh</c><00:03:06.800><c> you</c><00:03:06.879><c> can</c>

00:03:07.030 --> 00:03:07.040 align:start position:0%
next.js components right and uh you can
 

00:03:07.040 --> 00:03:07.990 align:start position:0%
next.js components right and uh you can
see<00:03:07.280><c> over</c><00:03:07.519><c> here</c>

00:03:07.990 --> 00:03:08.000 align:start position:0%
see over here
 

00:03:08.000 --> 00:03:11.270 align:start position:0%
see over here
within<00:03:08.319><c> our</c><00:03:08.560><c> index.js</c><00:03:09.599><c> the</c><00:03:09.840><c> page</c><00:03:10.879><c> all</c><00:03:11.040><c> right</c>

00:03:11.270 --> 00:03:11.280 align:start position:0%
within our index.js the page all right
 

00:03:11.280 --> 00:03:13.030 align:start position:0%
within our index.js the page all right
we<00:03:11.440><c> have</c><00:03:11.599><c> cons</c><00:03:11.920><c> settings</c><00:03:12.400><c> equals</c><00:03:12.800><c> this</c>

00:03:13.030 --> 00:03:13.040 align:start position:0%
we have cons settings equals this
 

00:03:13.040 --> 00:03:13.670 align:start position:0%
we have cons settings equals this
function

00:03:13.670 --> 00:03:13.680 align:start position:0%
function
 

00:03:13.680 --> 00:03:15.110 align:start position:0%
function
right<00:03:13.920><c> component</c><00:03:14.480><c> is</c><00:03:14.560><c> just</c><00:03:14.720><c> something</c><00:03:15.040><c> that</c>

00:03:15.110 --> 00:03:15.120 align:start position:0%
right component is just something that
 

00:03:15.120 --> 00:03:17.589 align:start position:0%
right component is just something that
takes<00:03:15.360><c> some</c><00:03:15.519><c> data</c><00:03:15.920><c> spits</c><00:03:16.239><c> out</c><00:03:16.400><c> something</c><00:03:16.800><c> else</c>

00:03:17.589 --> 00:03:17.599 align:start position:0%
takes some data spits out something else
 

00:03:17.599 --> 00:03:19.509 align:start position:0%
takes some data spits out something else
right<00:03:18.000><c> and</c><00:03:18.239><c> we're</c><00:03:18.560><c> importing</c><00:03:19.200><c> three</c>

00:03:19.509 --> 00:03:19.519 align:start position:0%
right and we're importing three
 

00:03:19.519 --> 00:03:20.949 align:start position:0%
right and we're importing three
functions<00:03:20.000><c> at</c><00:03:20.080><c> the</c><00:03:20.239><c> top</c>

00:03:20.949 --> 00:03:20.959 align:start position:0%
functions at the top
 

00:03:20.959 --> 00:03:24.470 align:start position:0%
functions at the top
from<00:03:21.440><c> actions</c><00:03:22.159><c> slash</c><00:03:22.879><c> user</c><00:03:23.920><c> and</c><00:03:24.239><c> it's</c>

00:03:24.470 --> 00:03:24.480 align:start position:0%
from actions slash user and it's
 

00:03:24.480 --> 00:03:25.190 align:start position:0%
from actions slash user and it's
importing<00:03:24.879><c> of</c>

00:03:25.190 --> 00:03:25.200 align:start position:0%
importing of
 

00:03:25.200 --> 00:03:28.789 align:start position:0%
importing of
a<00:03:26.080><c> function</c><00:03:26.480><c> now</c><00:03:26.640><c> which</c><00:03:26.879><c> says</c><00:03:27.120><c> set</c><00:03:27.440><c> moderation</c>

00:03:28.789 --> 00:03:28.799 align:start position:0%
a function now which says set moderation
 

00:03:28.799 --> 00:03:31.830 align:start position:0%
a function now which says set moderation
that<00:03:29.040><c> set</c><00:03:29.280><c> moderation</c><00:03:30.000><c> function</c><00:03:30.879><c> i</c><00:03:31.040><c> believe</c>

00:03:31.830 --> 00:03:31.840 align:start position:0%
that set moderation function i believe
 

00:03:31.840 --> 00:03:33.750 align:start position:0%
that set moderation function i believe
we'll<00:03:32.000><c> be</c><00:03:32.239><c> able</c><00:03:32.480><c> to</c><00:03:32.799><c> see</c><00:03:33.040><c> it</c>

00:03:33.750 --> 00:03:33.760 align:start position:0%
we'll be able to see it
 

00:03:33.760 --> 00:03:36.390 align:start position:0%
we'll be able to see it
um<00:03:35.040><c> let's</c><00:03:35.280><c> see</c><00:03:35.519><c> over</c><00:03:35.760><c> here</c><00:03:35.920><c> we're</c><00:03:36.000><c> going</c><00:03:36.239><c> to</c>

00:03:36.390 --> 00:03:36.400 align:start position:0%
um let's see over here we're going to
 

00:03:36.400 --> 00:03:37.270 align:start position:0%
um let's see over here we're going to
actions<00:03:36.879><c> and</c><00:03:36.959><c> then</c>

00:03:37.270 --> 00:03:37.280 align:start position:0%
actions and then
 

00:03:37.280 --> 00:03:39.430 align:start position:0%
actions and then
user<00:03:38.319><c> okay</c><00:03:38.560><c> let's</c><00:03:38.720><c> just</c><00:03:38.879><c> look</c><00:03:39.040><c> at</c><00:03:39.120><c> it</c><00:03:39.280><c> on</c><00:03:39.360><c> the</c>

00:03:39.430 --> 00:03:39.440 align:start position:0%
user okay let's just look at it on the
 

00:03:39.440 --> 00:03:40.949 align:start position:0%
user okay let's just look at it on the
left<00:03:39.680><c> side</c><00:03:39.920><c> for</c><00:03:40.080><c> a</c><00:03:40.159><c> moment</c>

00:03:40.949 --> 00:03:40.959 align:start position:0%
left side for a moment
 

00:03:40.959 --> 00:03:42.949 align:start position:0%
left side for a moment
and<00:03:41.120><c> here's</c><00:03:41.360><c> the</c><00:03:41.519><c> set</c><00:03:41.760><c> moderation</c><00:03:42.480><c> function</c>

00:03:42.949 --> 00:03:42.959 align:start position:0%
and here's the set moderation function
 

00:03:42.959 --> 00:03:45.670 align:start position:0%
and here's the set moderation function
it<00:03:43.120><c> says</c><00:03:43.920><c> this</c><00:03:44.159><c> is</c><00:03:44.319><c> actually</c><00:03:44.720><c> where</c><00:03:44.879><c> the</c><00:03:45.040><c> http</c>

00:03:45.670 --> 00:03:45.680 align:start position:0%
it says this is actually where the http
 

00:03:45.680 --> 00:03:46.229 align:start position:0%
it says this is actually where the http
call

00:03:46.229 --> 00:03:46.239 align:start position:0%
call
 

00:03:46.239 --> 00:03:48.630 align:start position:0%
call
is<00:03:46.560><c> actually</c><00:03:47.040><c> defined</c><00:03:47.680><c> right</c><00:03:47.920><c> so</c><00:03:48.080><c> it</c><00:03:48.239><c> says</c><00:03:48.400><c> set</c>

00:03:48.630 --> 00:03:48.640 align:start position:0%
is actually defined right so it says set
 

00:03:48.640 --> 00:03:49.830 align:start position:0%
is actually defined right so it says set
moderation

00:03:49.830 --> 00:03:49.840 align:start position:0%
moderation
 

00:03:49.840 --> 00:03:51.910 align:start position:0%
moderation
here's<00:03:50.159><c> the</c><00:03:50.319><c> api</c><00:03:50.879><c> endpoint</c><00:03:51.360><c> that</c><00:03:51.519><c> we</c><00:03:51.599><c> need</c><00:03:51.760><c> to</c>

00:03:51.910 --> 00:03:51.920 align:start position:0%
here's the api endpoint that we need to
 

00:03:51.920 --> 00:03:53.190 align:start position:0%
here's the api endpoint that we need to
hit<00:03:52.159><c> on</c><00:03:52.319><c> the</c><00:03:52.400><c> back</c><00:03:52.720><c> end</c>

00:03:53.190 --> 00:03:53.200 align:start position:0%
hit on the back end
 

00:03:53.200 --> 00:03:55.910 align:start position:0%
hit on the back end
and<00:03:53.439><c> so</c><00:03:53.599><c> on</c><00:03:54.400><c> uh</c><00:03:54.640><c> here's</c><00:03:54.959><c> the</c><00:03:55.120><c> settings</c><00:03:55.599><c> that</c><00:03:55.760><c> we</c>

00:03:55.910 --> 00:03:55.920 align:start position:0%
and so on uh here's the settings that we
 

00:03:55.920 --> 00:03:56.710 align:start position:0%
and so on uh here's the settings that we
want<00:03:56.080><c> to</c><00:03:56.159><c> get</c><00:03:56.400><c> up</c>

00:03:56.710 --> 00:03:56.720 align:start position:0%
want to get up
 

00:03:56.720 --> 00:03:59.670 align:start position:0%
want to get up
that<00:03:56.879><c> we</c><00:03:57.040><c> want</c><00:03:57.200><c> to</c><00:03:57.360><c> update</c><00:03:58.000><c> and</c><00:03:58.239><c> finally</c><00:03:59.280><c> uh</c>

00:03:59.670 --> 00:03:59.680 align:start position:0%
that we want to update and finally uh
 

00:03:59.680 --> 00:04:00.070 align:start position:0%
that we want to update and finally uh
and

00:04:00.070 --> 00:04:00.080 align:start position:0%
and
 

00:04:00.080 --> 00:04:03.350 align:start position:0%
and
fine<00:04:00.480><c> and</c><00:04:00.799><c> go</c><00:04:01.120><c> uh</c><00:04:01.760><c> go</c><00:04:02.159><c> go</c><00:04:02.480><c> chat</c><00:04:02.799><c> with</c><00:04:02.959><c> the</c><00:04:03.200><c> with</c>

00:04:03.350 --> 00:04:03.360 align:start position:0%
fine and go uh go go chat with the with
 

00:04:03.360 --> 00:04:04.630 align:start position:0%
fine and go uh go go chat with the with
the<00:04:03.519><c> admin</c><00:04:04.000><c> backend</c>

00:04:04.630 --> 00:04:04.640 align:start position:0%
the admin backend
 

00:04:04.640 --> 00:04:06.390 align:start position:0%
the admin backend
and<00:04:04.879><c> uh</c><00:04:05.120><c> tell</c><00:04:05.360><c> them</c><00:04:05.519><c> that</c><00:04:05.599><c> this</c><00:04:05.840><c> is</c><00:04:06.080><c> what</c><00:04:06.239><c> the</c>

00:04:06.390 --> 00:04:06.400 align:start position:0%
and uh tell them that this is what the
 

00:04:06.400 --> 00:04:07.910 align:start position:0%
and uh tell them that this is what the
user<00:04:06.720><c> wants</c><00:04:07.040><c> to</c><00:04:07.200><c> do</c>

00:04:07.910 --> 00:04:07.920 align:start position:0%
user wants to do
 

00:04:07.920 --> 00:04:09.589 align:start position:0%
user wants to do
and<00:04:08.239><c> fine</c><00:04:08.480><c> so</c><00:04:08.640><c> that</c><00:04:08.799><c> wraps</c><00:04:09.120><c> up</c><00:04:09.200><c> the</c><00:04:09.280><c> admin</c>

00:04:09.589 --> 00:04:09.599 align:start position:0%
and fine so that wraps up the admin
 

00:04:09.599 --> 00:04:10.949 align:start position:0%
and fine so that wraps up the admin
front<00:04:09.760><c> end</c><00:04:10.000><c> if</c><00:04:10.080><c> we</c><00:04:10.159><c> go</c><00:04:10.239><c> into</c><00:04:10.400><c> the</c><00:04:10.560><c> admin</c>

00:04:10.949 --> 00:04:10.959 align:start position:0%
front end if we go into the admin
 

00:04:10.959 --> 00:04:12.710 align:start position:0%
front end if we go into the admin
backend<00:04:11.360><c> we</c><00:04:11.519><c> see</c><00:04:11.680><c> we</c><00:04:11.840><c> have</c><00:04:11.920><c> a</c><00:04:12.000><c> controllers</c>

00:04:12.710 --> 00:04:12.720 align:start position:0%
backend we see we have a controllers
 

00:04:12.720 --> 00:04:14.070 align:start position:0%
backend we see we have a controllers
folder<00:04:13.200><c> over</c><00:04:13.439><c> here</c>

00:04:14.070 --> 00:04:14.080 align:start position:0%
folder over here
 

00:04:14.080 --> 00:04:17.110 align:start position:0%
folder over here
and<00:04:14.159><c> the</c><00:04:14.319><c> controllers</c><00:04:14.959><c> folder</c><00:04:15.439><c> also</c><00:04:16.239><c> um</c>

00:04:17.110 --> 00:04:17.120 align:start position:0%
and the controllers folder also um
 

00:04:17.120 --> 00:04:18.949 align:start position:0%
and the controllers folder also um
is<00:04:17.199><c> where</c><00:04:17.440><c> all</c><00:04:17.600><c> the</c><00:04:17.759><c> logic</c><00:04:18.160><c> is</c><00:04:18.239><c> defined</c><00:04:18.639><c> but</c><00:04:18.799><c> we</c>

00:04:18.949 --> 00:04:18.959 align:start position:0%
is where all the logic is defined but we
 

00:04:18.959 --> 00:04:20.710 align:start position:0%
is where all the logic is defined but we
also<00:04:19.120><c> have</c><00:04:19.280><c> within</c><00:04:19.519><c> the</c><00:04:19.600><c> admin</c><00:04:20.000><c> backend</c><00:04:20.560><c> a</c>

00:04:20.710 --> 00:04:20.720 align:start position:0%
also have within the admin backend a
 

00:04:20.720 --> 00:04:21.909 align:start position:0%
also have within the admin backend a
routes<00:04:21.199><c> folder</c>

00:04:21.909 --> 00:04:21.919 align:start position:0%
routes folder
 

00:04:21.919 --> 00:04:26.070 align:start position:0%
routes folder
let's<00:04:22.160><c> say</c><00:04:22.400><c> routes</c><00:04:23.680><c> user</c><00:04:24.400><c> okay</c>

00:04:26.070 --> 00:04:26.080 align:start position:0%
let's say routes user okay
 

00:04:26.080 --> 00:04:28.150 align:start position:0%
let's say routes user okay
and<00:04:26.320><c> this</c><00:04:26.560><c> is</c><00:04:26.800><c> pretty</c><00:04:27.040><c> much</c><00:04:27.280><c> where</c><00:04:27.680><c> where</c><00:04:27.919><c> that</c>

00:04:28.150 --> 00:04:28.160 align:start position:0%
and this is pretty much where where that
 

00:04:28.160 --> 00:04:29.909 align:start position:0%
and this is pretty much where where that
dialogue<00:04:28.800><c> is</c><00:04:29.040><c> caught</c><00:04:29.360><c> when</c><00:04:29.520><c> we</c>

00:04:29.909 --> 00:04:29.919 align:start position:0%
dialogue is caught when we
 

00:04:29.919 --> 00:04:33.110 align:start position:0%
dialogue is caught when we
when<00:04:30.080><c> a</c><00:04:30.240><c> user</c><00:04:30.639><c> says</c><00:04:31.360><c> router.put</c><00:04:32.400><c> user</c><00:04:32.720><c> slash</c>

00:04:33.110 --> 00:04:33.120 align:start position:0%
when a user says router.put user slash
 

00:04:33.120 --> 00:04:34.710 align:start position:0%
when a user says router.put user slash
user

00:04:34.710 --> 00:04:34.720 align:start position:0%
user
 

00:04:34.720 --> 00:04:37.749 align:start position:0%
user
uh<00:04:35.440><c> wait</c><00:04:35.680><c> a</c><00:04:35.759><c> second</c>

00:04:37.749 --> 00:04:37.759 align:start position:0%
uh wait a second
 

00:04:37.759 --> 00:04:39.590 align:start position:0%
uh wait a second
let<00:04:37.919><c> me</c><00:04:38.000><c> just</c><00:04:38.160><c> make</c><00:04:38.320><c> sure</c><00:04:38.560><c> that</c><00:04:38.720><c> we</c><00:04:38.880><c> don't</c><00:04:39.120><c> lose</c>

00:04:39.590 --> 00:04:39.600 align:start position:0%
let me just make sure that we don't lose
 

00:04:39.600 --> 00:04:41.430 align:start position:0%
let me just make sure that we don't lose
our<00:04:39.919><c> update</c><00:04:40.400><c> function</c><00:04:40.800><c> there</c>

00:04:41.430 --> 00:04:41.440 align:start position:0%
our update function there
 

00:04:41.440 --> 00:04:44.790 align:start position:0%
our update function there
so<00:04:43.600><c> ctrl</c><00:04:44.160><c> c</c>

00:04:44.790 --> 00:04:44.800 align:start position:0%
so ctrl c
 

00:04:44.800 --> 00:04:48.629 align:start position:0%
so ctrl c
okay<00:04:46.160><c> one</c><00:04:46.320><c> sec</c><00:04:46.639><c> guys</c><00:04:47.199><c> router</c><00:04:47.600><c> dot</c><00:04:48.000><c> put</c>

00:04:48.629 --> 00:04:48.639 align:start position:0%
okay one sec guys router dot put
 

00:04:48.639 --> 00:04:51.749 align:start position:0%
okay one sec guys router dot put
okay<00:04:50.080><c> user</c><00:04:50.400><c> slash</c><00:04:50.800><c> username</c><00:04:51.280><c> slash</c>

00:04:51.749 --> 00:04:51.759 align:start position:0%
okay user slash username slash
 

00:04:51.759 --> 00:04:54.310 align:start position:0%
okay user slash username slash
update<00:04:52.240><c> when</c><00:04:52.479><c> a</c><00:04:52.560><c> person</c><00:04:53.199><c> when</c><00:04:53.360><c> a</c><00:04:53.440><c> user</c><00:04:53.759><c> presses</c>

00:04:54.310 --> 00:04:54.320 align:start position:0%
update when a person when a user presses
 

00:04:54.320 --> 00:04:55.030 align:start position:0%
update when a person when a user presses
save

00:04:55.030 --> 00:04:55.040 align:start position:0%
save
 

00:04:55.040 --> 00:04:56.950 align:start position:0%
save
it's<00:04:55.280><c> going</c><00:04:55.360><c> to</c><00:04:55.440><c> run</c><00:04:55.600><c> the</c><00:04:55.840><c> update</c><00:04:56.320><c> function</c>

00:04:56.950 --> 00:04:56.960 align:start position:0%
it's going to run the update function
 

00:04:56.960 --> 00:04:58.790 align:start position:0%
it's going to run the update function
and<00:04:57.120><c> when</c><00:04:57.280><c> a</c><00:04:57.440><c> user</c>

00:04:58.790 --> 00:04:58.800 align:start position:0%
and when a user
 

00:04:58.800 --> 00:05:01.270 align:start position:0%
and when a user
i<00:04:58.960><c> just</c><00:04:59.120><c> wants</c><00:04:59.360><c> to</c><00:04:59.600><c> update</c><00:05:00.000><c> the</c><00:05:00.160><c> moderation</c>

00:05:01.270 --> 00:05:01.280 align:start position:0%
i just wants to update the moderation
 

00:05:01.280 --> 00:05:02.710 align:start position:0%
i just wants to update the moderation
then<00:05:01.440><c> it's</c><00:05:01.600><c> going</c><00:05:01.680><c> to</c><00:05:01.759><c> run</c><00:05:02.000><c> this</c><00:05:02.240><c> function</c>

00:05:02.710 --> 00:05:02.720 align:start position:0%
then it's going to run this function
 

00:05:02.720 --> 00:05:03.110 align:start position:0%
then it's going to run this function
here

00:05:03.110 --> 00:05:03.120 align:start position:0%
here
 

00:05:03.120 --> 00:05:05.270 align:start position:0%
here
okay<00:05:03.360><c> so</c><00:05:03.520><c> you</c><00:05:03.680><c> see</c><00:05:04.160><c> when</c><00:05:04.320><c> somebody</c><00:05:04.720><c> hits</c><00:05:05.039><c> this</c>

00:05:05.270 --> 00:05:05.280 align:start position:0%
okay so you see when somebody hits this
 

00:05:05.280 --> 00:05:06.390 align:start position:0%
okay so you see when somebody hits this
api<00:05:05.759><c> endpoint</c>

00:05:06.390 --> 00:05:06.400 align:start position:0%
api endpoint
 

00:05:06.400 --> 00:05:08.629 align:start position:0%
api endpoint
run<00:05:06.720><c> this</c><00:05:07.120><c> function</c><00:05:07.680><c> which</c><00:05:07.919><c> is</c><00:05:08.000><c> the</c><00:05:08.240><c> update</c>

00:05:08.629 --> 00:05:08.639 align:start position:0%
run this function which is the update
 

00:05:08.639 --> 00:05:09.510 align:start position:0%
run this function which is the update
function

00:05:09.510 --> 00:05:09.520 align:start position:0%
function
 

00:05:09.520 --> 00:05:11.670 align:start position:0%
function
and<00:05:09.680><c> when</c><00:05:09.840><c> somebody</c><00:05:10.160><c> hits</c><00:05:10.479><c> this</c><00:05:10.720><c> api</c><00:05:11.199><c> endpoint</c>

00:05:11.670 --> 00:05:11.680 align:start position:0%
and when somebody hits this api endpoint
 

00:05:11.680 --> 00:05:13.350 align:start position:0%
and when somebody hits this api endpoint
which<00:05:11.840><c> is</c><00:05:12.000><c> update</c><00:05:12.320><c> moderation</c><00:05:13.039><c> run</c>

00:05:13.350 --> 00:05:13.360 align:start position:0%
which is update moderation run
 

00:05:13.360 --> 00:05:15.510 align:start position:0%
which is update moderation run
update<00:05:13.680><c> moderation</c><00:05:14.479><c> function</c><00:05:14.880><c> now</c><00:05:15.120><c> both</c><00:05:15.360><c> the</c>

00:05:15.510 --> 00:05:15.520 align:start position:0%
update moderation function now both the
 

00:05:15.520 --> 00:05:17.990 align:start position:0%
update moderation function now both the
update<00:05:15.840><c> moderation</c><00:05:16.400><c> and</c><00:05:16.639><c> update</c><00:05:17.120><c> functions</c>

00:05:17.990 --> 00:05:18.000 align:start position:0%
update moderation and update functions
 

00:05:18.000 --> 00:05:20.150 align:start position:0%
update moderation and update functions
are<00:05:18.240><c> going</c><00:05:18.320><c> to</c><00:05:18.400><c> be</c><00:05:18.639><c> imported</c><00:05:19.520><c> from</c><00:05:19.840><c> our</c>

00:05:20.150 --> 00:05:20.160 align:start position:0%
are going to be imported from our
 

00:05:20.160 --> 00:05:21.110 align:start position:0%
are going to be imported from our
controller

00:05:21.110 --> 00:05:21.120 align:start position:0%
controller
 

00:05:21.120 --> 00:05:23.830 align:start position:0%
controller
over<00:05:21.360><c> here</c><00:05:21.919><c> okay</c><00:05:22.880><c> so</c><00:05:23.120><c> let's</c><00:05:23.440><c> let's</c><00:05:23.680><c> have</c><00:05:23.759><c> a</c>

00:05:23.830 --> 00:05:23.840 align:start position:0%
over here okay so let's let's have a
 

00:05:23.840 --> 00:05:25.350 align:start position:0%
over here okay so let's let's have a
look<00:05:24.000><c> at</c><00:05:24.080><c> what</c><00:05:24.240><c> that</c><00:05:24.479><c> looks</c><00:05:24.639><c> like</c><00:05:24.880><c> over</c><00:05:25.199><c> here</c>

00:05:25.350 --> 00:05:25.360 align:start position:0%
look at what that looks like over here
 

00:05:25.360 --> 00:05:26.629 align:start position:0%
look at what that looks like over here
i'm<00:05:25.520><c> just</c><00:05:25.680><c> going</c><00:05:25.759><c> to</c><00:05:25.840><c> make</c><00:05:26.000><c> this</c><00:05:26.240><c> a</c><00:05:26.320><c> little</c><00:05:26.479><c> bit</c>

00:05:26.629 --> 00:05:26.639 align:start position:0%
i'm just going to make this a little bit
 

00:05:26.639 --> 00:05:27.189 align:start position:0%
i'm just going to make this a little bit
cleaner

00:05:27.189 --> 00:05:27.199 align:start position:0%
cleaner
 

00:05:27.199 --> 00:05:29.590 align:start position:0%
cleaner
okay<00:05:27.440><c> so</c><00:05:27.759><c> con</c><00:05:28.080><c> settings</c><00:05:28.479><c> page</c><00:05:28.880><c> read</c><00:05:29.120><c> pop</c><00:05:29.440><c> these</c>

00:05:29.590 --> 00:05:29.600 align:start position:0%
okay so con settings page read pop these
 

00:05:29.600 --> 00:05:31.029 align:start position:0%
okay so con settings page read pop these
are<00:05:29.759><c> a</c><00:05:29.840><c> bunch</c><00:05:30.080><c> of</c><00:05:30.240><c> functions</c>

00:05:31.029 --> 00:05:31.039 align:start position:0%
are a bunch of functions
 

00:05:31.039 --> 00:05:33.430 align:start position:0%
are a bunch of functions
that<00:05:31.280><c> get</c><00:05:31.840><c> imported</c><00:05:32.400><c> from</c><00:05:32.800><c> require</c>

00:05:33.430 --> 00:05:33.440 align:start position:0%
that get imported from require
 

00:05:33.440 --> 00:05:34.550 align:start position:0%
that get imported from require
controller<00:05:34.080><c> slash</c>

00:05:34.550 --> 00:05:34.560 align:start position:0%
controller slash
 

00:05:34.560 --> 00:05:36.870 align:start position:0%
controller slash
user<00:05:35.120><c> okay</c><00:05:35.840><c> and</c><00:05:35.919><c> then</c><00:05:36.080><c> if</c><00:05:36.160><c> we</c><00:05:36.320><c> actually</c><00:05:36.720><c> go</c>

00:05:36.870 --> 00:05:36.880 align:start position:0%
user okay and then if we actually go
 

00:05:36.880 --> 00:05:39.510 align:start position:0%
user okay and then if we actually go
into<00:05:37.120><c> admin</c><00:05:37.520><c> backend</c><00:05:38.000><c> slash</c><00:05:38.400><c> controllers</c>

00:05:39.510 --> 00:05:39.520 align:start position:0%
into admin backend slash controllers
 

00:05:39.520 --> 00:05:41.350 align:start position:0%
into admin backend slash controllers
here's<00:05:39.840><c> the</c><00:05:40.000><c> update</c><00:05:40.320><c> moderation</c><00:05:41.039><c> function</c>

00:05:41.350 --> 00:05:41.360 align:start position:0%
here's the update moderation function
 

00:05:41.360 --> 00:05:42.870 align:start position:0%
here's the update moderation function
that's<00:05:41.600><c> getting</c><00:05:41.919><c> imported</c><00:05:42.479><c> here's</c><00:05:42.720><c> the</c>

00:05:42.870 --> 00:05:42.880 align:start position:0%
that's getting imported here's the
 

00:05:42.880 --> 00:05:44.950 align:start position:0%
that's getting imported here's the
update<00:05:43.280><c> function</c><00:05:43.600><c> which</c><00:05:43.759><c> gets</c><00:05:44.080><c> imported</c>

00:05:44.950 --> 00:05:44.960 align:start position:0%
update function which gets imported
 

00:05:44.960 --> 00:05:46.870 align:start position:0%
update function which gets imported
and<00:05:45.120><c> you</c><00:05:45.280><c> see</c><00:05:45.440><c> our</c><00:05:45.600><c> routes</c><00:05:45.919><c> folder</c><00:05:46.400><c> our</c><00:05:46.560><c> routes</c>

00:05:46.870 --> 00:05:46.880 align:start position:0%
and you see our routes folder our routes
 

00:05:46.880 --> 00:05:48.550 align:start position:0%
and you see our routes folder our routes
file<00:05:47.120><c> is</c><00:05:47.280><c> incredibly</c><00:05:47.759><c> clean</c><00:05:48.080><c> and</c><00:05:48.160><c> that</c><00:05:48.320><c> makes</c>

00:05:48.550 --> 00:05:48.560 align:start position:0%
file is incredibly clean and that makes
 

00:05:48.560 --> 00:05:49.590 align:start position:0%
file is incredibly clean and that makes
it<00:05:48.639><c> much</c><00:05:48.880><c> easier</c>

00:05:49.590 --> 00:05:49.600 align:start position:0%
it much easier
 

00:05:49.600 --> 00:05:52.870 align:start position:0%
it much easier
to<00:05:49.759><c> declug</c><00:05:50.639><c> declog</c><00:05:51.199><c> any</c><00:05:51.680><c> any</c><00:05:52.000><c> data</c>

00:05:52.870 --> 00:05:52.880 align:start position:0%
to declug declog any any data
 

00:05:52.880 --> 00:05:55.110 align:start position:0%
to declug declog any any data
data<00:05:53.199><c> clogs</c><00:05:53.520><c> we</c><00:05:53.680><c> might</c><00:05:53.919><c> have</c><00:05:54.320><c> along</c><00:05:54.800><c> that</c>

00:05:55.110 --> 00:05:55.120 align:start position:0%
data clogs we might have along that
 

00:05:55.120 --> 00:05:56.309 align:start position:0%
data clogs we might have along that
along<00:05:55.360><c> that</c><00:05:55.600><c> journey</c><00:05:55.919><c> along</c><00:05:56.160><c> that</c>

00:05:56.309 --> 00:05:56.319 align:start position:0%
along that journey along that
 

00:05:56.319 --> 00:05:57.350 align:start position:0%
along that journey along that
communication

00:05:57.350 --> 00:05:57.360 align:start position:0%
communication
 

00:05:57.360 --> 00:05:59.270 align:start position:0%
communication
admin<00:05:57.680><c> backend</c><00:05:58.080><c> slash</c><00:05:58.400><c> controllers</c><00:05:59.039><c> we</c><00:05:59.120><c> have</c>

00:05:59.270 --> 00:05:59.280 align:start position:0%
admin backend slash controllers we have
 

00:05:59.280 --> 00:06:00.790 align:start position:0%
admin backend slash controllers we have
the<00:05:59.440><c> update</c><00:05:59.759><c> moderation</c>

00:06:00.790 --> 00:06:00.800 align:start position:0%
the update moderation
 

00:06:00.800 --> 00:06:02.390 align:start position:0%
the update moderation
just<00:06:01.039><c> to</c><00:06:01.199><c> make</c><00:06:01.360><c> sure</c><00:06:01.520><c> that</c><00:06:01.759><c> all</c><00:06:01.919><c> the</c><00:06:02.240><c> the</c>

00:06:02.390 --> 00:06:02.400 align:start position:0%
just to make sure that all the the
 

00:06:02.400 --> 00:06:04.150 align:start position:0%
just to make sure that all the the
wiring<00:06:02.880><c> is</c><00:06:02.960><c> set</c><00:06:03.199><c> up</c><00:06:03.360><c> correctly</c>

00:06:04.150 --> 00:06:04.160 align:start position:0%
wiring is set up correctly
 

00:06:04.160 --> 00:06:05.350 align:start position:0%
wiring is set up correctly
we're<00:06:04.319><c> just</c><00:06:04.479><c> going</c><00:06:04.639><c> to</c><00:06:04.720><c> run</c><00:06:04.880><c> this</c><00:06:05.039><c> update</c>

00:06:05.350 --> 00:06:05.360 align:start position:0%
we're just going to run this update
 

00:06:05.360 --> 00:06:08.150 align:start position:0%
we're just going to run this update
moderation<00:06:06.000><c> function</c><00:06:06.960><c> just</c><00:06:07.199><c> to</c><00:06:07.440><c> test</c><00:06:07.759><c> it</c>

00:06:08.150 --> 00:06:08.160 align:start position:0%
moderation function just to test it
 

00:06:08.160 --> 00:06:10.469 align:start position:0%
moderation function just to test it
but<00:06:08.400><c> um</c><00:06:09.039><c> this</c><00:06:09.280><c> is</c><00:06:09.360><c> kind</c><00:06:09.600><c> of</c><00:06:09.919><c> where</c><00:06:10.080><c> we're</c><00:06:10.319><c> at</c>

00:06:10.469 --> 00:06:10.479 align:start position:0%
but um this is kind of where we're at
 

00:06:10.479 --> 00:06:11.590 align:start position:0%
but um this is kind of where we're at
right<00:06:10.720><c> now</c><00:06:11.039><c> so</c>

00:06:11.590 --> 00:06:11.600 align:start position:0%
right now so
 

00:06:11.600 --> 00:06:13.350 align:start position:0%
right now so
uh<00:06:11.840><c> this</c><00:06:12.080><c> is</c><00:06:12.160><c> kind</c><00:06:12.319><c> of</c><00:06:12.400><c> an</c><00:06:12.560><c> important</c><00:06:12.960><c> video</c><00:06:13.280><c> to</c>

00:06:13.350 --> 00:06:13.360 align:start position:0%
uh this is kind of an important video to
 

00:06:13.360 --> 00:06:14.629 align:start position:0%
uh this is kind of an important video to
help<00:06:13.520><c> you</c><00:06:13.680><c> guys</c><00:06:13.919><c> understand</c><00:06:14.400><c> the</c>

00:06:14.629 --> 00:06:14.639 align:start position:0%
help you guys understand the
 

00:06:14.639 --> 00:06:15.830 align:start position:0%
help you guys understand the
architecture

00:06:15.830 --> 00:06:15.840 align:start position:0%
architecture
 

00:06:15.840 --> 00:06:18.309 align:start position:0%
architecture
of<00:06:16.080><c> how</c><00:06:16.479><c> there's</c><00:06:16.880><c> a</c><00:06:17.120><c> communication</c><00:06:18.000><c> between</c>

00:06:18.309 --> 00:06:18.319 align:start position:0%
of how there's a communication between
 

00:06:18.319 --> 00:06:19.990 align:start position:0%
of how there's a communication between
the<00:06:18.400><c> admin</c><00:06:18.800><c> front</c><00:06:19.039><c> end</c><00:06:19.360><c> and</c><00:06:19.440><c> the</c><00:06:19.600><c> admin</c>

00:06:19.990 --> 00:06:20.000 align:start position:0%
the admin front end and the admin
 

00:06:20.000 --> 00:06:23.270 align:start position:0%
the admin front end and the admin
back<00:06:20.319><c> end</c><00:06:20.960><c> thanks</c><00:06:21.360><c> for</c><00:06:21.520><c> tuning</c><00:06:22.000><c> in</c><00:06:22.479><c> again</c>

00:06:23.270 --> 00:06:23.280 align:start position:0%
back end thanks for tuning in again
 

00:06:23.280 --> 00:06:25.990 align:start position:0%
back end thanks for tuning in again
the<00:06:23.680><c> http</c><00:06:24.319><c> all</c><00:06:24.479><c> the</c><00:06:24.639><c> http</c><00:06:25.199><c> functions</c><00:06:25.680><c> are</c>

00:06:25.990 --> 00:06:26.000 align:start position:0%
the http all the http functions are
 

00:06:26.000 --> 00:06:28.309 align:start position:0%
the http all the http functions are
isolated<00:06:26.720><c> into</c><00:06:26.960><c> the</c><00:06:27.039><c> actions</c><00:06:27.520><c> folder</c>

00:06:28.309 --> 00:06:28.319 align:start position:0%
isolated into the actions folder
 

00:06:28.319 --> 00:06:30.469 align:start position:0%
isolated into the actions folder
and<00:06:28.560><c> imported</c><00:06:29.120><c> into</c><00:06:29.360><c> our</c><00:06:29.520><c> components</c><00:06:30.240><c> and</c><00:06:30.400><c> the</c>

00:06:30.469 --> 00:06:30.479 align:start position:0%
and imported into our components and the
 

00:06:30.479 --> 00:06:31.830 align:start position:0%
and imported into our components and the
component<00:06:30.960><c> talks</c><00:06:31.280><c> with</c>

00:06:31.830 --> 00:06:31.840 align:start position:0%
component talks with
 

00:06:31.840 --> 00:06:34.070 align:start position:0%
component talks with
uh<00:06:32.400><c> routes</c><00:06:33.120><c> and</c><00:06:33.280><c> the</c><00:06:33.440><c> functions</c><00:06:33.840><c> from</c><00:06:34.000><c> the</c>

00:06:34.070 --> 00:06:34.080 align:start position:0%
uh routes and the functions from the
 

00:06:34.080 --> 00:06:35.029 align:start position:0%
uh routes and the functions from the
controller<00:06:34.560><c> are</c><00:06:34.720><c> then</c>

00:06:35.029 --> 00:06:35.039 align:start position:0%
controller are then
 

00:06:35.039 --> 00:06:37.270 align:start position:0%
controller are then
imported<00:06:35.600><c> into</c><00:06:35.919><c> our</c><00:06:36.160><c> routes</c><00:06:36.560><c> file</c><00:06:36.960><c> and</c><00:06:37.039><c> that's</c>

00:06:37.270 --> 00:06:37.280 align:start position:0%
imported into our routes file and that's
 

00:06:37.280 --> 00:06:39.189 align:start position:0%
imported into our routes file and that's
how<00:06:37.919><c> we're</c><00:06:38.240><c> able</c><00:06:38.479><c> to</c><00:06:38.639><c> always</c>

00:06:39.189 --> 00:06:39.199 align:start position:0%
how we're able to always
 

00:06:39.199 --> 00:06:40.870 align:start position:0%
how we're able to always
you<00:06:39.280><c> know</c><00:06:39.520><c> create</c><00:06:39.840><c> a</c><00:06:39.919><c> very</c><00:06:40.240><c> clean</c><00:06:40.560><c> data</c>

00:06:40.870 --> 00:06:40.880 align:start position:0%
you know create a very clean data
 

00:06:40.880 --> 00:06:42.469 align:start position:0%
you know create a very clean data
pipeline<00:06:41.360><c> between</c><00:06:41.600><c> the</c><00:06:41.680><c> admin</c><00:06:42.000><c> front</c><00:06:42.240><c> end</c><00:06:42.400><c> and</c>

00:06:42.469 --> 00:06:42.479 align:start position:0%
pipeline between the admin front end and
 

00:06:42.479 --> 00:06:43.590 align:start position:0%
pipeline between the admin front end and
the<00:06:42.560><c> admin</c><00:06:42.960><c> back</c><00:06:43.199><c> end</c>

00:06:43.590 --> 00:06:43.600 align:start position:0%
the admin back end
 

00:06:43.600 --> 00:06:45.270 align:start position:0%
the admin back end
it's<00:06:43.759><c> not</c><00:06:44.000><c> too</c><00:06:44.160><c> verbose</c><00:06:44.639><c> where</c><00:06:44.880><c> it</c><00:06:44.960><c> doesn't</c>

00:06:45.270 --> 00:06:45.280 align:start position:0%
it's not too verbose where it doesn't
 

00:06:45.280 --> 00:06:47.430 align:start position:0%
it's not too verbose where it doesn't
need<00:06:45.440><c> to</c><00:06:45.600><c> be</c><00:06:46.160><c> and</c><00:06:46.319><c> everything</c><00:06:46.639><c> is</c><00:06:46.800><c> isolated</c>

00:06:47.430 --> 00:06:47.440 align:start position:0%
need to be and everything is isolated
 

00:06:47.440 --> 00:06:47.990 align:start position:0%
need to be and everything is isolated
and<00:06:47.520><c> it's</c><00:06:47.680><c> always</c>

00:06:47.990 --> 00:06:48.000 align:start position:0%
and it's always
 

00:06:48.000 --> 00:06:50.550 align:start position:0%
and it's always
easy<00:06:48.319><c> to</c><00:06:48.720><c> uh</c><00:06:48.960><c> hopefully</c><00:06:49.440><c> reason</c><00:06:49.840><c> about</c><00:06:50.160><c> it</c><00:06:50.400><c> all</c>

00:06:50.550 --> 00:06:50.560 align:start position:0%
easy to uh hopefully reason about it all
 

00:06:50.560 --> 00:06:52.469 align:start position:0%
easy to uh hopefully reason about it all
right<00:06:50.720><c> guys</c><00:06:50.960><c> so</c><00:06:51.280><c> thanks</c><00:06:51.520><c> for</c><00:06:51.680><c> tuning</c><00:06:52.080><c> in</c>

00:06:52.469 --> 00:06:52.479 align:start position:0%
right guys so thanks for tuning in
 

00:06:52.479 --> 00:06:53.670 align:start position:0%
right guys so thanks for tuning in
and<00:06:52.720><c> feel</c><00:06:52.880><c> free</c><00:06:53.039><c> to</c><00:06:53.120><c> reach</c><00:06:53.280><c> out</c><00:06:53.440><c> if</c><00:06:53.520><c> you</c><00:06:53.599><c> have</c>

00:06:53.670 --> 00:06:53.680 align:start position:0%
and feel free to reach out if you have
 

00:06:53.680 --> 00:06:55.510 align:start position:0%
and feel free to reach out if you have
any<00:06:53.919><c> questions</c><00:06:54.400><c> and</c><00:06:54.560><c> i'll</c><00:06:54.720><c> see</c><00:06:54.880><c> you</c><00:06:55.039><c> guys</c>

00:06:55.510 --> 00:06:55.520 align:start position:0%
any questions and i'll see you guys
 

00:06:55.520 --> 00:06:59.440 align:start position:0%
any questions and i'll see you guys
in<00:06:55.840><c> the</c><00:06:56.000><c> next</c><00:06:56.479><c> video</c>

