WEBVTT
Kind: captions
Language: en

00:00:00.160 --> 00:00:01.630 align:start position:0%
 
hey<00:00:00.320><c> and</c><00:00:00.440><c> welcome</c><00:00:00.719><c> back</c><00:00:01.040><c> in</c><00:00:01.199><c> this</c><00:00:01.319><c> video</c><00:00:01.560><c> I'm</c>

00:00:01.630 --> 00:00:01.640 align:start position:0%
hey and welcome back in this video I'm
 

00:00:01.640 --> 00:00:02.909 align:start position:0%
hey and welcome back in this video I'm
going<00:00:01.760><c> to</c><00:00:01.839><c> show</c><00:00:01.959><c> you</c><00:00:02.040><c> how</c><00:00:02.120><c> to</c><00:00:02.240><c> set</c><00:00:02.399><c> up</c><00:00:02.639><c> other</c>

00:00:02.909 --> 00:00:02.919 align:start position:0%
going to show you how to set up other
 

00:00:02.919 --> 00:00:04.950 align:start position:0%
going to show you how to set up other
open<00:00:03.240><c> source</c><00:00:03.520><c> llms</c><00:00:04.319><c> and</c><00:00:04.440><c> connect</c><00:00:04.720><c> them</c><00:00:04.839><c> to</c>

00:00:04.950 --> 00:00:04.960 align:start position:0%
open source llms and connect them to
 

00:00:04.960 --> 00:00:06.670 align:start position:0%
open source llms and connect them to
autogen<00:00:05.720><c> I'll</c><00:00:05.879><c> quickly</c><00:00:06.120><c> go</c><00:00:06.240><c> over</c><00:00:06.440><c> how</c><00:00:06.560><c> it</c>

00:00:06.670 --> 00:00:06.680 align:start position:0%
autogen I'll quickly go over how it
 

00:00:06.680 --> 00:00:08.709 align:start position:0%
autogen I'll quickly go over how it
works<00:00:07.240><c> then</c><00:00:07.399><c> we'll</c><00:00:07.560><c> set</c><00:00:07.720><c> it</c><00:00:07.839><c> up</c><00:00:08.320><c> and</c><00:00:08.440><c> then</c><00:00:08.559><c> I'll</c>

00:00:08.709 --> 00:00:08.719 align:start position:0%
works then we'll set it up and then I'll
 

00:00:08.719 --> 00:00:10.350 align:start position:0%
works then we'll set it up and then I'll
give<00:00:08.840><c> you</c><00:00:08.920><c> a</c><00:00:09.040><c> use</c><00:00:09.360><c> case</c><00:00:09.519><c> sample</c><00:00:09.920><c> to</c><00:00:10.040><c> show</c><00:00:10.200><c> you</c>

00:00:10.350 --> 00:00:10.360 align:start position:0%
give you a use case sample to show you
 

00:00:10.360 --> 00:00:12.110 align:start position:0%
give you a use case sample to show you
at<00:00:10.480><c> the</c><00:00:10.559><c> end</c><00:00:10.920><c> let's</c><00:00:11.080><c> get</c><00:00:11.240><c> started</c><00:00:11.840><c> the</c><00:00:11.960><c> first</c>

00:00:12.110 --> 00:00:12.120 align:start position:0%
at the end let's get started the first
 

00:00:12.120 --> 00:00:14.030 align:start position:0%
at the end let's get started the first
thing<00:00:12.240><c> we're</c><00:00:12.360><c> going</c><00:00:12.440><c> to</c><00:00:12.559><c> do</c><00:00:12.719><c> is</c><00:00:12.920><c> download</c><00:00:13.360><c> LM</c>

00:00:14.030 --> 00:00:14.040 align:start position:0%
thing we're going to do is download LM
 

00:00:14.040 --> 00:00:16.310 align:start position:0%
thing we're going to do is download LM
Studio<00:00:15.040><c> then</c><00:00:15.360><c> we're</c><00:00:15.519><c> going</c><00:00:15.639><c> to</c><00:00:15.759><c> choose</c><00:00:16.160><c> a</c>

00:00:16.310 --> 00:00:16.320 align:start position:0%
Studio then we're going to choose a
 

00:00:16.320 --> 00:00:17.830 align:start position:0%
Studio then we're going to choose a
large<00:00:16.560><c> language</c><00:00:16.920><c> model</c><00:00:17.359><c> that</c><00:00:17.480><c> you</c><00:00:17.560><c> want</c><00:00:17.680><c> to</c>

00:00:17.830 --> 00:00:17.840 align:start position:0%
large language model that you want to
 

00:00:17.840 --> 00:00:20.109 align:start position:0%
large language model that you want to
use<00:00:18.119><c> for</c><00:00:18.279><c> autogen</c><00:00:19.240><c> then</c><00:00:19.439><c> we're</c><00:00:19.600><c> going</c><00:00:19.720><c> to</c><00:00:19.840><c> load</c>

00:00:20.109 --> 00:00:20.119 align:start position:0%
use for autogen then we're going to load
 

00:00:20.119 --> 00:00:22.910 align:start position:0%
use for autogen then we're going to load
that<00:00:20.279><c> model</c><00:00:20.920><c> into</c><00:00:21.199><c> the</c><00:00:21.519><c> software</c><00:00:22.519><c> then</c><00:00:22.760><c> we're</c>

00:00:22.910 --> 00:00:22.920 align:start position:0%
that model into the software then we're
 

00:00:22.920 --> 00:00:25.230 align:start position:0%
that model into the software then we're
going<00:00:23.039><c> to</c><00:00:23.160><c> start</c><00:00:23.439><c> a</c><00:00:23.599><c> local</c><00:00:24.000><c> server</c><00:00:24.960><c> and</c><00:00:25.080><c> then</c>

00:00:25.230 --> 00:00:25.240 align:start position:0%
going to start a local server and then
 

00:00:25.240 --> 00:00:26.990 align:start position:0%
going to start a local server and then
connect<00:00:25.519><c> it</c><00:00:25.599><c> to</c><00:00:25.720><c> autogen</c><00:00:26.439><c> and</c><00:00:26.560><c> I'll</c><00:00:26.720><c> show</c><00:00:26.880><c> you</c>

00:00:26.990 --> 00:00:27.000 align:start position:0%
connect it to autogen and I'll show you
 

00:00:27.000 --> 00:00:28.710 align:start position:0%
connect it to autogen and I'll show you
a<00:00:27.160><c> use</c><00:00:27.400><c> case</c><00:00:27.560><c> at</c><00:00:27.679><c> the</c><00:00:27.760><c> end</c><00:00:28.039><c> so</c><00:00:28.199><c> the</c><00:00:28.320><c> first</c><00:00:28.480><c> step</c>

00:00:28.710 --> 00:00:28.720 align:start position:0%
a use case at the end so the first step
 

00:00:28.720 --> 00:00:32.229 align:start position:0%
a use case at the end so the first step
is<00:00:29.039><c> go</c><00:00:29.199><c> to</c><00:00:29.439><c> LM</c><00:00:29.960><c> studio.</c><00:00:30.599><c> a</c><00:00:31.400><c> and</c><00:00:31.519><c> once</c><00:00:31.679><c> you</c><00:00:32.000><c> get</c>

00:00:32.229 --> 00:00:32.239 align:start position:0%
is go to LM studio. a and once you get
 

00:00:32.239 --> 00:00:34.910 align:start position:0%
is go to LM studio. a and once you get
there<00:00:32.880><c> go</c><00:00:33.000><c> ahead</c><00:00:33.200><c> and</c><00:00:33.440><c> download</c><00:00:33.800><c> LM</c><00:00:34.200><c> Studio</c>

00:00:34.910 --> 00:00:34.920 align:start position:0%
there go ahead and download LM Studio
 

00:00:34.920 --> 00:00:36.310 align:start position:0%
there go ahead and download LM Studio
whichever<00:00:35.320><c> want</c><00:00:35.480><c> is</c><00:00:35.559><c> for</c><00:00:35.760><c> your</c><00:00:35.840><c> machine</c><00:00:36.239><c> and</c>

00:00:36.310 --> 00:00:36.320 align:start position:0%
whichever want is for your machine and
 

00:00:36.320 --> 00:00:37.869 align:start position:0%
whichever want is for your machine and
when<00:00:36.440><c> you're</c><00:00:36.640><c> done</c><00:00:36.920><c> downloading</c><00:00:37.320><c> it</c><00:00:37.559><c> go</c><00:00:37.680><c> ahead</c>

00:00:37.869 --> 00:00:37.879 align:start position:0%
when you're done downloading it go ahead
 

00:00:37.879 --> 00:00:39.549 align:start position:0%
when you're done downloading it go ahead
and<00:00:38.040><c> run</c><00:00:38.280><c> the</c><00:00:38.440><c> software</c><00:00:39.000><c> now</c><00:00:39.120><c> when</c><00:00:39.239><c> you</c><00:00:39.360><c> run</c>

00:00:39.549 --> 00:00:39.559 align:start position:0%
and run the software now when you run
 

00:00:39.559 --> 00:00:41.709 align:start position:0%
and run the software now when you run
the<00:00:39.719><c> software</c><00:00:40.600><c> you'll</c><00:00:40.800><c> see</c><00:00:41.120><c> this</c><00:00:41.280><c> screen</c><00:00:41.559><c> and</c>

00:00:41.709 --> 00:00:41.719 align:start position:0%
the software you'll see this screen and
 

00:00:41.719 --> 00:00:43.990 align:start position:0%
the software you'll see this screen and
this<00:00:41.800><c> is</c><00:00:41.960><c> the</c><00:00:42.120><c> main</c><00:00:42.399><c> screen</c><00:00:42.680><c> of</c><00:00:42.840><c> LM</c><00:00:43.160><c> studio</c><00:00:43.600><c> so</c>

00:00:43.990 --> 00:00:44.000 align:start position:0%
this is the main screen of LM studio so
 

00:00:44.000 --> 00:00:45.750 align:start position:0%
this is the main screen of LM studio so
this<00:00:44.079><c> is</c><00:00:44.200><c> a</c><00:00:44.320><c> standalone</c><00:00:44.840><c> software</c><00:00:45.520><c> all</c><00:00:45.640><c> we</c>

00:00:45.750 --> 00:00:45.760 align:start position:0%
this is a standalone software all we
 

00:00:45.760 --> 00:00:47.990 align:start position:0%
this is a standalone software all we
really<00:00:46.000><c> need</c><00:00:46.120><c> to</c><00:00:46.239><c> know</c><00:00:46.520><c> for</c><00:00:46.840><c> this</c><00:00:47.520><c> is</c><00:00:47.719><c> in</c><00:00:47.840><c> the</c>

00:00:47.990 --> 00:00:48.000 align:start position:0%
really need to know for this is in the
 

00:00:48.000 --> 00:00:50.310 align:start position:0%
really need to know for this is in the
middle<00:00:48.480><c> at</c><00:00:48.680><c> towards</c><00:00:48.960><c> the</c><00:00:49.160><c> top</c><00:00:49.760><c> you</c><00:00:49.879><c> can</c><00:00:50.039><c> search</c>

00:00:50.310 --> 00:00:50.320 align:start position:0%
middle at towards the top you can search
 

00:00:50.320 --> 00:00:53.430 align:start position:0%
middle at towards the top you can search
for<00:00:50.520><c> models</c><00:00:50.960><c> by</c><00:00:51.160><c> keyword</c><00:00:51.760><c> or</c><00:00:52.079><c> paste</c><00:00:52.520><c> a</c><00:00:52.879><c> repo</c>

00:00:53.430 --> 00:00:53.440 align:start position:0%
for models by keyword or paste a repo
 

00:00:53.440 --> 00:00:56.069 align:start position:0%
for models by keyword or paste a repo
URL<00:00:53.879><c> here</c><00:00:54.320><c> okay</c><00:00:54.480><c> so</c><00:00:55.000><c> all</c><00:00:55.199><c> I'm</c><00:00:55.280><c> going</c><00:00:55.399><c> to</c><00:00:55.520><c> do</c><00:00:55.920><c> is</c>

00:00:56.069 --> 00:00:56.079 align:start position:0%
URL here okay so all I'm going to do is
 

00:00:56.079 --> 00:00:57.549 align:start position:0%
URL here okay so all I'm going to do is
I'm<00:00:56.160><c> just</c><00:00:56.280><c> going</c><00:00:56.359><c> to</c><00:00:56.480><c> go</c><00:00:56.600><c> and</c><00:00:56.800><c> use</c><00:00:57.039><c> llama</c><00:00:57.359><c> for</c>

00:00:57.549 --> 00:00:57.559 align:start position:0%
I'm just going to go and use llama for
 

00:00:57.559 --> 00:01:01.150 align:start position:0%
I'm just going to go and use llama for
this<00:00:57.719><c> so</c><00:00:57.920><c> I'll</c><00:00:58.120><c> type</c><00:00:58.280><c> in</c><00:00:58.920><c> llama</c><00:01:00.160><c> click</c><00:01:00.480><c> go</c><00:01:00.920><c> or</c>

00:01:01.150 --> 00:01:01.160 align:start position:0%
this so I'll type in llama click go or
 

00:01:01.160 --> 00:01:03.069 align:start position:0%
this so I'll type in llama click go or
hit<00:01:01.320><c> enter</c><00:01:02.039><c> and</c><00:01:02.160><c> then</c><00:01:02.280><c> what</c><00:01:02.399><c> it's</c><00:01:02.519><c> going</c><00:01:02.600><c> to</c><00:01:02.760><c> do</c>

00:01:03.069 --> 00:01:03.079 align:start position:0%
hit enter and then what it's going to do
 

00:01:03.079 --> 00:01:05.350 align:start position:0%
hit enter and then what it's going to do
is<00:01:03.280><c> going</c><00:01:03.399><c> to</c><00:01:03.519><c> end</c><00:01:03.719><c> up</c><00:01:03.879><c> in</c><00:01:04.080><c> giving</c><00:01:04.360><c> us</c><00:01:04.720><c> all</c><00:01:05.040><c> of</c>

00:01:05.350 --> 00:01:05.360 align:start position:0%
is going to end up in giving us all of
 

00:01:05.360 --> 00:01:07.390 align:start position:0%
is going to end up in giving us all of
these<00:01:05.680><c> models</c><00:01:06.159><c> that</c><00:01:06.280><c> we</c><00:01:06.400><c> can</c><00:01:06.560><c> choose</c><00:01:06.880><c> from</c><00:01:07.200><c> so</c>

00:01:07.390 --> 00:01:07.400 align:start position:0%
these models that we can choose from so
 

00:01:07.400 --> 00:01:09.310 align:start position:0%
these models that we can choose from so
what<00:01:07.520><c> you</c><00:01:07.600><c> can</c><00:01:07.720><c> also</c><00:01:08.159><c> do</c><00:01:08.520><c> is</c><00:01:08.680><c> at</c><00:01:08.799><c> the</c><00:01:08.960><c> top</c><00:01:09.159><c> here</c>

00:01:09.310 --> 00:01:09.320 align:start position:0%
what you can also do is at the top here
 

00:01:09.320 --> 00:01:13.350 align:start position:0%
what you can also do is at the top here
you<00:01:09.400><c> can</c><00:01:09.600><c> sort</c><00:01:10.080><c> by</c><00:01:10.720><c> downloads</c><00:01:11.479><c> recent</c><00:01:12.360><c> likes</c>

00:01:13.350 --> 00:01:13.360 align:start position:0%
you can sort by downloads recent likes
 

00:01:13.360 --> 00:01:16.749 align:start position:0%
you can sort by downloads recent likes
uh<00:01:13.479><c> by</c><00:01:13.640><c> the</c><00:01:13.799><c> most</c><00:01:14.439><c> or</c><00:01:15.119><c> by</c><00:01:15.320><c> the</c><00:01:15.479><c> least</c><00:01:16.000><c> recent</c>

00:01:16.749 --> 00:01:16.759 align:start position:0%
uh by the most or by the least recent
 

00:01:16.759 --> 00:01:18.550 align:start position:0%
uh by the most or by the least recent
okay<00:01:17.159><c> or</c><00:01:17.320><c> by</c><00:01:17.479><c> the</c><00:01:17.600><c> most</c><00:01:17.799><c> downloads</c><00:01:18.320><c> least</c>

00:01:18.550 --> 00:01:18.560 align:start position:0%
okay or by the most downloads least
 

00:01:18.560 --> 00:01:21.310 align:start position:0%
okay or by the most downloads least
likes<00:01:18.920><c> Whatever</c><00:01:19.119><c> It</c><00:01:19.240><c> Is</c><00:01:19.960><c> Well</c><00:01:20.159><c> for</c><00:01:20.360><c> our</c><00:01:20.680><c> case</c>

00:01:21.310 --> 00:01:21.320 align:start position:0%
likes Whatever It Is Well for our case
 

00:01:21.320 --> 00:01:22.710 align:start position:0%
likes Whatever It Is Well for our case
I'm<00:01:21.439><c> going</c><00:01:21.520><c> to</c><00:01:21.640><c> click</c><00:01:21.880><c> this</c><00:01:22.040><c> top</c><00:01:22.240><c> one</c><00:01:22.479><c> by</c><00:01:22.640><c> the</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
I'm going to click this top one by the
 

00:01:22.720 --> 00:01:25.390 align:start position:0%
I'm going to click this top one by the
bloke<00:01:23.640><c> and</c><00:01:23.799><c> then</c><00:01:24.000><c> on</c><00:01:24.079><c> the</c><00:01:24.280><c> right</c><00:01:24.560><c> here</c><00:01:25.159><c> you</c><00:01:25.280><c> can</c>

00:01:25.390 --> 00:01:25.400 align:start position:0%
bloke and then on the right here you can
 

00:01:25.400 --> 00:01:27.350 align:start position:0%
bloke and then on the right here you can
see<00:01:25.560><c> we</c><00:01:25.680><c> have</c><00:01:26.000><c> all</c><00:01:26.360><c> of</c><00:01:26.560><c> these</c><00:01:26.759><c> to</c><00:01:26.920><c> choose</c><00:01:27.159><c> from</c>

00:01:27.350 --> 00:01:27.360 align:start position:0%
see we have all of these to choose from
 

00:01:27.360 --> 00:01:28.670 align:start position:0%
see we have all of these to choose from
to<00:01:27.520><c> download</c><00:01:27.880><c> and</c><00:01:28.000><c> these</c><00:01:28.079><c> are</c><00:01:28.240><c> just</c><00:01:28.360><c> different</c>

00:01:28.670 --> 00:01:28.680 align:start position:0%
to download and these are just different
 

00:01:28.680 --> 00:01:31.149 align:start position:0%
to download and these are just different
sizes<00:01:29.400><c> so</c><00:01:29.560><c> what</c><00:01:30.000><c> would</c><00:01:30.159><c> recommend</c><00:01:30.960><c> is</c>

00:01:31.149 --> 00:01:31.159 align:start position:0%
sizes so what would recommend is
 

00:01:31.159 --> 00:01:33.749 align:start position:0%
sizes so what would recommend is
actually<00:01:31.400><c> I</c><00:01:31.520><c> went</c><00:01:31.720><c> with</c><00:01:32.200><c> this</c><00:01:32.360><c> one</c><00:01:32.759><c> the</c>

00:01:33.749 --> 00:01:33.759 align:start position:0%
actually I went with this one the
 

00:01:33.759 --> 00:01:40.429 align:start position:0%
actually I went with this one the
ggf<00:01:34.759><c> and</c><00:01:35.479><c> I</c><00:01:36.040><c> downloaded</c><00:01:37.040><c> uh</c><00:01:37.320><c> this</c><00:01:37.720><c> Q4</c><00:01:38.720><c> km</c><00:01:39.640><c> so</c>

00:01:40.429 --> 00:01:40.439 align:start position:0%
ggf and I downloaded uh this Q4 km so
 

00:01:40.439 --> 00:01:42.789 align:start position:0%
ggf and I downloaded uh this Q4 km so
how<00:01:40.600><c> I</c><00:01:40.720><c> did</c><00:01:40.960><c> that</c><00:01:41.439><c> was</c><00:01:41.600><c> you</c><00:01:41.799><c> click</c><00:01:42.119><c> a</c><00:01:42.320><c> download</c>

00:01:42.789 --> 00:01:42.799 align:start position:0%
how I did that was you click a download
 

00:01:42.799 --> 00:01:44.950 align:start position:0%
how I did that was you click a download
on<00:01:42.960><c> the</c><00:01:43.119><c> model</c><00:01:43.360><c> that</c><00:01:43.439><c> you</c><00:01:43.560><c> chose</c><00:01:44.159><c> so</c><00:01:44.479><c> download</c>

00:01:44.950 --> 00:01:44.960 align:start position:0%
on the model that you chose so download
 

00:01:44.960 --> 00:01:48.109 align:start position:0%
on the model that you chose so download
this<00:01:45.719><c> then</c><00:01:46.439><c> we'll</c><00:01:46.759><c> I'll</c><00:01:46.920><c> bring</c><00:01:47.240><c> this</c><00:01:47.479><c> up</c><00:01:47.960><c> and</c>

00:01:48.109 --> 00:01:48.119 align:start position:0%
this then we'll I'll bring this up and
 

00:01:48.119 --> 00:01:50.310 align:start position:0%
this then we'll I'll bring this up and
you<00:01:48.200><c> can</c><00:01:48.360><c> see</c><00:01:48.680><c> here</c><00:01:49.159><c> that</c><00:01:49.320><c> it's</c><00:01:49.520><c> downloading</c>

00:01:50.310 --> 00:01:50.320 align:start position:0%
you can see here that it's downloading
 

00:01:50.320 --> 00:01:51.870 align:start position:0%
you can see here that it's downloading
and<00:01:50.439><c> once</c><00:01:50.640><c> this</c><00:01:50.759><c> is</c><00:01:50.960><c> done</c><00:01:51.360><c> we'll</c><00:01:51.560><c> go</c><00:01:51.640><c> to</c><00:01:51.759><c> the</c>

00:01:51.870 --> 00:01:51.880 align:start position:0%
and once this is done we'll go to the
 

00:01:51.880 --> 00:01:53.749 align:start position:0%
and once this is done we'll go to the
next<00:01:52.079><c> step</c><00:01:52.399><c> so</c><00:01:52.560><c> now</c><00:01:52.719><c> that</c><00:01:52.840><c> the</c><00:01:52.960><c> model</c><00:01:53.280><c> is</c><00:01:53.439><c> done</c>

00:01:53.749 --> 00:01:53.759 align:start position:0%
next step so now that the model is done
 

00:01:53.759 --> 00:01:55.950 align:start position:0%
next step so now that the model is done
downloading<00:01:54.759><c> what</c><00:01:54.880><c> we</c><00:01:54.960><c> want</c><00:01:55.079><c> to</c><00:01:55.240><c> do</c><00:01:55.520><c> next</c><00:01:55.840><c> is</c>

00:01:55.950 --> 00:01:55.960 align:start position:0%
downloading what we want to do next is
 

00:01:55.960 --> 00:01:57.630 align:start position:0%
downloading what we want to do next is
on<00:01:56.119><c> the</c><00:01:56.320><c> left</c><00:01:56.600><c> hand</c><00:01:56.799><c> side</c><00:01:56.960><c> of</c><00:01:57.039><c> the</c><00:01:57.200><c> software</c>

00:01:57.630 --> 00:01:57.640 align:start position:0%
on the left hand side of the software
 

00:01:57.640 --> 00:01:59.590 align:start position:0%
on the left hand side of the software
here<00:01:57.799><c> there's</c><00:01:58.039><c> this</c><00:01:58.200><c> little</c><00:01:58.560><c> Double</c><00:01:58.960><c> Arrow</c>

00:01:59.590 --> 00:01:59.600 align:start position:0%
here there's this little Double Arrow
 

00:01:59.600 --> 00:02:01.510 align:start position:0%
here there's this little Double Arrow
you<00:01:59.799><c> you'll</c><00:01:59.960><c> go</c><00:02:00.039><c> ah</c><00:02:00.159><c> and</c><00:02:00.320><c> click</c><00:02:00.640><c> this</c><00:02:01.240><c> and</c><00:02:01.439><c> this</c>

00:02:01.510 --> 00:02:01.520 align:start position:0%
you you'll go ah and click this and this
 

00:02:01.520 --> 00:02:03.749 align:start position:0%
you you'll go ah and click this and this
is<00:02:01.719><c> to</c><00:02:01.920><c> start</c><00:02:02.200><c> our</c><00:02:02.479><c> local</c><00:02:02.840><c> server</c><00:02:03.360><c> and</c><00:02:03.600><c> where</c>

00:02:03.749 --> 00:02:03.759 align:start position:0%
is to start our local server and where
 

00:02:03.759 --> 00:02:06.870 align:start position:0%
is to start our local server and where
we<00:02:03.920><c> load</c><00:02:04.320><c> the</c><00:02:04.439><c> model</c><00:02:05.439><c> so</c><00:02:05.680><c> at</c><00:02:05.840><c> the</c><00:02:06.119><c> top</c><00:02:06.479><c> here</c><00:02:06.759><c> you</c>

00:02:06.870 --> 00:02:06.880 align:start position:0%
we load the model so at the top here you
 

00:02:06.880 --> 00:02:09.270 align:start position:0%
we load the model so at the top here you
can<00:02:07.000><c> see</c><00:02:07.200><c> it</c><00:02:07.280><c> says</c><00:02:07.520><c> select</c><00:02:07.880><c> a</c><00:02:08.039><c> model</c><00:02:08.360><c> to</c><00:02:08.560><c> load</c>

00:02:09.270 --> 00:02:09.280 align:start position:0%
can see it says select a model to load
 

00:02:09.280 --> 00:02:11.350 align:start position:0%
can see it says select a model to load
you'll<00:02:09.560><c> click</c><00:02:09.800><c> this</c><00:02:10.000><c> drop</c><00:02:10.319><c> down</c><00:02:10.840><c> and</c><00:02:11.000><c> I</c><00:02:11.080><c> have</c><00:02:11.239><c> a</c>

00:02:11.350 --> 00:02:11.360 align:start position:0%
you'll click this drop down and I have a
 

00:02:11.360 --> 00:02:13.270 align:start position:0%
you'll click this drop down and I have a
few<00:02:11.640><c> downloaded</c><00:02:12.560><c> but</c><00:02:12.680><c> I'll</c><00:02:12.800><c> go</c><00:02:12.920><c> ahead</c><00:02:13.120><c> and</c>

00:02:13.270 --> 00:02:13.280 align:start position:0%
few downloaded but I'll go ahead and
 

00:02:13.280 --> 00:02:15.070 align:start position:0%
few downloaded but I'll go ahead and
click<00:02:13.560><c> llama</c><00:02:14.160><c> the</c><00:02:14.239><c> one</c><00:02:14.440><c> that</c><00:02:14.599><c> I</c><00:02:14.879><c> just</c>

00:02:15.070 --> 00:02:15.080 align:start position:0%
click llama the one that I just
 

00:02:15.080 --> 00:02:16.910 align:start position:0%
click llama the one that I just
downloaded<00:02:16.000><c> it's</c><00:02:16.120><c> going</c><00:02:16.200><c> to</c><00:02:16.319><c> take</c><00:02:16.480><c> a</c><00:02:16.599><c> minute</c>

00:02:16.910 --> 00:02:16.920 align:start position:0%
downloaded it's going to take a minute
 

00:02:16.920 --> 00:02:18.710 align:start position:0%
downloaded it's going to take a minute
it'll<00:02:17.160><c> load</c><00:02:17.480><c> the</c><00:02:17.560><c> model</c><00:02:17.920><c> into</c><00:02:18.120><c> the</c><00:02:18.280><c> software</c>

00:02:18.710 --> 00:02:18.720 align:start position:0%
it'll load the model into the software
 

00:02:18.720 --> 00:02:20.630 align:start position:0%
it'll load the model into the software
for<00:02:18.920><c> us</c><00:02:19.280><c> and</c><00:02:19.400><c> once</c><00:02:19.560><c> it's</c><00:02:19.720><c> done</c><00:02:20.239><c> you'll</c><00:02:20.440><c> see</c>

00:02:20.630 --> 00:02:20.640 align:start position:0%
for us and once it's done you'll see
 

00:02:20.640 --> 00:02:23.190 align:start position:0%
for us and once it's done you'll see
that<00:02:20.760><c> it's</c><00:02:21.000><c> loaded</c><00:02:22.000><c> and</c><00:02:22.720><c> because</c><00:02:22.920><c> it's</c><00:02:23.040><c> a</c>

00:02:23.190 --> 00:02:23.200 align:start position:0%
that it's loaded and because it's a
 

00:02:23.200 --> 00:02:25.910 align:start position:0%
that it's loaded and because it's a
local<00:02:23.680><c> server</c><00:02:24.680><c> right</c><00:02:24.959><c> here</c><00:02:25.280><c> where</c><00:02:25.400><c> it</c><00:02:25.519><c> says</c>

00:02:25.910 --> 00:02:25.920 align:start position:0%
local server right here where it says
 

00:02:25.920 --> 00:02:30.110 align:start position:0%
local server right here where it says
HTTP<00:02:26.760><c> Local</c><00:02:27.160><c> Host</c><00:02:27.400><c> 1</c><00:02:27.599><c> 23</c><00:02:28.040><c> 4</c><00:02:28.519><c> version</c><00:02:28.840><c> one</c><00:02:29.920><c> this</c>

00:02:30.110 --> 00:02:30.120 align:start position:0%
HTTP Local Host 1 23 4 version one this
 

00:02:30.120 --> 00:02:32.229 align:start position:0%
HTTP Local Host 1 23 4 version one this
is<00:02:30.280><c> the</c><00:02:30.440><c> URL</c><00:02:31.200><c> or</c><00:02:31.360><c> the</c><00:02:31.519><c> API</c><00:02:31.920><c> that</c><00:02:32.040><c> we're</c><00:02:32.120><c> going</c>

00:02:32.229 --> 00:02:32.239 align:start position:0%
is the URL or the API that we're going
 

00:02:32.239 --> 00:02:34.710 align:start position:0%
is the URL or the API that we're going
to<00:02:32.319><c> be</c><00:02:32.480><c> using</c><00:02:33.160><c> to</c><00:02:33.360><c> connect</c><00:02:33.680><c> to</c><00:02:33.920><c> autogen</c><00:02:34.599><c> so</c>

00:02:34.710 --> 00:02:34.720 align:start position:0%
to be using to connect to autogen so
 

00:02:34.720 --> 00:02:37.270 align:start position:0%
to be using to connect to autogen so
that<00:02:34.879><c> we</c><00:02:34.959><c> can</c><00:02:35.120><c> use</c><00:02:35.599><c> this</c><00:02:35.840><c> model</c><00:02:36.280><c> now</c><00:02:36.760><c> to</c><00:02:37.000><c> load</c>

00:02:37.270 --> 00:02:37.280 align:start position:0%
that we can use this model now to load
 

00:02:37.280 --> 00:02:39.350 align:start position:0%
that we can use this model now to load
our<00:02:37.480><c> prompts</c><00:02:38.000><c> and</c><00:02:38.120><c> see</c><00:02:38.360><c> what</c><00:02:38.480><c> it</c><00:02:38.599><c> gives</c><00:02:38.800><c> us</c><00:02:39.200><c> now</c>

00:02:39.350 --> 00:02:39.360 align:start position:0%
our prompts and see what it gives us now
 

00:02:39.360 --> 00:02:41.350 align:start position:0%
our prompts and see what it gives us now
the<00:02:39.519><c> last</c><00:02:39.760><c> step</c><00:02:40.159><c> is</c><00:02:40.360><c> pretty</c><00:02:40.560><c> simple</c><00:02:41.120><c> all</c><00:02:41.239><c> you</c>

00:02:41.350 --> 00:02:41.360 align:start position:0%
the last step is pretty simple all you
 

00:02:41.360 --> 00:02:43.910 align:start position:0%
the last step is pretty simple all you
have<00:02:41.440><c> to</c><00:02:41.560><c> do</c><00:02:41.680><c> is</c><00:02:41.800><c> start</c><00:02:42.040><c> the</c><00:02:42.200><c> server</c><00:02:42.720><c> this</c><00:02:43.040><c> big</c>

00:02:43.910 --> 00:02:43.920 align:start position:0%
have to do is start the server this big
 

00:02:43.920 --> 00:02:45.390 align:start position:0%
have to do is start the server this big
well<00:02:44.080><c> not</c><00:02:44.200><c> really</c><00:02:44.480><c> big</c><00:02:44.879><c> but</c><00:02:45.000><c> this</c><00:02:45.159><c> green</c>

00:02:45.390 --> 00:02:45.400 align:start position:0%
well not really big but this green
 

00:02:45.400 --> 00:02:47.430 align:start position:0%
well not really big but this green
button<00:02:45.680><c> here</c><00:02:46.200><c> it</c><00:02:46.280><c> says</c><00:02:46.519><c> start</c><00:02:46.760><c> server</c><00:02:47.200><c> just</c><00:02:47.360><c> go</c>

00:02:47.430 --> 00:02:47.440 align:start position:0%
button here it says start server just go
 

00:02:47.440 --> 00:02:49.270 align:start position:0%
button here it says start server just go
ahead<00:02:47.640><c> and</c><00:02:47.720><c> click</c><00:02:47.920><c> it</c><00:02:48.280><c> and</c><00:02:48.480><c> there</c><00:02:48.599><c> you</c><00:02:48.760><c> go</c><00:02:49.120><c> we</c>

00:02:49.270 --> 00:02:49.280 align:start position:0%
ahead and click it and there you go we
 

00:02:49.280 --> 00:02:52.470 align:start position:0%
ahead and click it and there you go we
started<00:02:49.959><c> the</c><00:02:50.159><c> server</c><00:02:50.760><c> with</c><00:02:50.920><c> LM</c><00:02:51.400><c> Studio</c><00:02:52.239><c> that</c>

00:02:52.470 --> 00:02:52.480 align:start position:0%
started the server with LM Studio that
 

00:02:52.480 --> 00:02:55.430 align:start position:0%
started the server with LM Studio that
has<00:02:52.720><c> the</c><00:02:52.959><c> Llama</c><00:02:53.560><c> model</c><00:02:53.959><c> loaded</c><00:02:54.400><c> into</c><00:02:54.720><c> it</c><00:02:55.239><c> now</c>

00:02:55.430 --> 00:02:55.440 align:start position:0%
has the Llama model loaded into it now
 

00:02:55.440 --> 00:02:57.710 align:start position:0%
has the Llama model loaded into it now
all<00:02:55.560><c> we</c><00:02:55.680><c> need</c><00:02:55.800><c> to</c><00:02:55.959><c> do</c><00:02:56.440><c> is</c><00:02:56.680><c> connect</c><00:02:57.239><c> that</c><00:02:57.400><c> to</c>

00:02:57.710 --> 00:02:57.720 align:start position:0%
all we need to do is connect that to
 

00:02:57.720 --> 00:02:59.470 align:start position:0%
all we need to do is connect that to
autogen<00:02:58.519><c> and</c><00:02:58.599><c> then</c><00:02:58.720><c> we</c><00:02:58.840><c> can</c><00:02:58.920><c> start</c><00:02:59.120><c> using</c><00:02:59.360><c> this</c>

00:02:59.470 --> 00:02:59.480 align:start position:0%
autogen and then we can start using this
 

00:02:59.480 --> 00:03:02.350 align:start position:0%
autogen and then we can start using this
model<00:02:59.879><c> for</c><00:03:00.000><c> our</c><00:03:00.120><c> prompts</c><00:03:01.080><c> okay</c><00:03:01.599><c> now</c><00:03:01.879><c> all</c><00:03:02.000><c> I</c><00:03:02.159><c> did</c>

00:03:02.350 --> 00:03:02.360 align:start position:0%
model for our prompts okay now all I did
 

00:03:02.360 --> 00:03:05.470 align:start position:0%
model for our prompts okay now all I did
here<00:03:02.640><c> was</c><00:03:02.959><c> I</c><00:03:03.239><c> created</c><00:03:03.720><c> a</c><00:03:03.959><c> python</c><00:03:04.440><c> file</c><00:03:05.080><c> and</c><00:03:05.280><c> I</c>

00:03:05.470 --> 00:03:05.480 align:start position:0%
here was I created a python file and I
 

00:03:05.480 --> 00:03:06.830 align:start position:0%
here was I created a python file and I
have<00:03:05.680><c> all</c><00:03:05.840><c> this</c><00:03:06.040><c> information</c><00:03:06.480><c> here</c><00:03:06.640><c> you</c><00:03:06.720><c> don't</c>

00:03:06.830 --> 00:03:06.840 align:start position:0%
have all this information here you don't
 

00:03:06.840 --> 00:03:08.710 align:start position:0%
have all this information here you don't
need<00:03:07.000><c> to</c><00:03:07.120><c> worry</c><00:03:07.360><c> about</c><00:03:08.000><c> uh</c><00:03:08.159><c> like</c><00:03:08.319><c> necessarily</c>

00:03:08.710 --> 00:03:08.720 align:start position:0%
need to worry about uh like necessarily
 

00:03:08.720 --> 00:03:10.149 align:start position:0%
need to worry about uh like necessarily
pausing<00:03:09.000><c> the</c><00:03:09.080><c> video</c><00:03:09.280><c> and</c><00:03:09.400><c> copying</c><00:03:09.840><c> all</c><00:03:10.000><c> this</c>

00:03:10.149 --> 00:03:10.159 align:start position:0%
pausing the video and copying all this
 

00:03:10.159 --> 00:03:11.550 align:start position:0%
pausing the video and copying all this
down<00:03:10.560><c> I'll</c><00:03:10.720><c> have</c><00:03:10.840><c> this</c><00:03:11.000><c> I</c><00:03:11.080><c> have</c><00:03:11.159><c> a</c><00:03:11.239><c> link</c><00:03:11.400><c> in</c><00:03:11.480><c> the</c>

00:03:11.550 --> 00:03:11.560 align:start position:0%
down I'll have this I have a link in the
 

00:03:11.560 --> 00:03:13.030 align:start position:0%
down I'll have this I have a link in the
description<00:03:11.879><c> that</c><00:03:12.040><c> has</c><00:03:12.239><c> all</c><00:03:12.480><c> this</c><00:03:12.599><c> for</c><00:03:12.760><c> you</c>

00:03:13.030 --> 00:03:13.040 align:start position:0%
description that has all this for you
 

00:03:13.040 --> 00:03:13.910 align:start position:0%
description that has all this for you
because<00:03:13.159><c> I'm</c><00:03:13.239><c> just</c><00:03:13.319><c> going</c><00:03:13.440><c> to</c><00:03:13.480><c> briefly</c><00:03:13.799><c> go</c>

00:03:13.910 --> 00:03:13.920 align:start position:0%
because I'm just going to briefly go
 

00:03:13.920 --> 00:03:15.589 align:start position:0%
because I'm just going to briefly go
over<00:03:14.200><c> this</c><00:03:14.760><c> because</c><00:03:14.920><c> this</c><00:03:15.000><c> is</c><00:03:15.159><c> just</c><00:03:15.280><c> to</c><00:03:15.400><c> really</c>

00:03:15.589 --> 00:03:15.599 align:start position:0%
over this because this is just to really
 

00:03:15.599 --> 00:03:17.350 align:start position:0%
over this because this is just to really
get<00:03:15.760><c> everything</c><00:03:16.080><c> set</c><00:03:16.239><c> up</c><00:03:16.560><c> show</c><00:03:16.760><c> you</c><00:03:16.920><c> a</c><00:03:17.040><c> use</c>

00:03:17.350 --> 00:03:17.360 align:start position:0%
get everything set up show you a use
 

00:03:17.360 --> 00:03:19.350 align:start position:0%
get everything set up show you a use
case<00:03:17.959><c> and</c><00:03:18.120><c> then</c><00:03:18.360><c> you</c><00:03:18.480><c> can</c><00:03:18.680><c> take</c><00:03:18.879><c> this</c><00:03:19.040><c> and</c><00:03:19.159><c> try</c>

00:03:19.350 --> 00:03:19.360 align:start position:0%
case and then you can take this and try
 

00:03:19.360 --> 00:03:22.110 align:start position:0%
case and then you can take this and try
it<00:03:19.440><c> on</c><00:03:19.560><c> your</c><00:03:19.640><c> own</c><00:03:19.799><c> local</c><00:03:20.040><c> machine</c><00:03:20.760><c> okay</c><00:03:21.319><c> so</c><00:03:21.840><c> the</c>

00:03:22.110 --> 00:03:22.120 align:start position:0%
it on your own local machine okay so the
 

00:03:22.120 --> 00:03:24.630 align:start position:0%
it on your own local machine okay so the
difference<00:03:22.519><c> here</c><00:03:23.080><c> is</c><00:03:23.400><c> we</c><00:03:23.519><c> do</c><00:03:24.000><c> again</c><00:03:24.280><c> have</c><00:03:24.400><c> to</c>

00:03:24.630 --> 00:03:24.640 align:start position:0%
difference here is we do again have to
 

00:03:24.640 --> 00:03:27.270 align:start position:0%
difference here is we do again have to
connect<00:03:25.400><c> autogen</c><00:03:26.319><c> to</c><00:03:26.519><c> the</c><00:03:26.640><c> local</c><00:03:26.879><c> server</c><00:03:27.159><c> so</c>

00:03:27.270 --> 00:03:27.280 align:start position:0%
connect autogen to the local server so
 

00:03:27.280 --> 00:03:28.710 align:start position:0%
connect autogen to the local server so
that<00:03:27.400><c> we</c><00:03:27.480><c> can</c><00:03:27.599><c> use</c><00:03:27.760><c> the</c><00:03:27.879><c> Llama</c><00:03:28.159><c> model</c><00:03:28.480><c> and</c><00:03:28.599><c> how</c>

00:03:28.710 --> 00:03:28.720 align:start position:0%
that we can use the Llama model and how
 

00:03:28.720 --> 00:03:30.949 align:start position:0%
that we can use the Llama model and how
you<00:03:28.879><c> do</c><00:03:29.080><c> that</c><00:03:29.400><c> is</c><00:03:29.720><c> you</c><00:03:29.799><c> have</c><00:03:29.879><c> a</c><00:03:30.000><c> config</c><00:03:30.319><c> list</c>

00:03:30.949 --> 00:03:30.959 align:start position:0%
you do that is you have a config list
 

00:03:30.959 --> 00:03:33.630 align:start position:0%
you do that is you have a config list
and<00:03:31.120><c> you</c><00:03:31.400><c> have</c><00:03:31.799><c> three</c><00:03:32.040><c> things</c><00:03:32.360><c> an</c><00:03:32.560><c> API</c><00:03:33.080><c> type</c>

00:03:33.630 --> 00:03:33.640 align:start position:0%
and you have three things an API type
 

00:03:33.640 --> 00:03:37.149 align:start position:0%
and you have three things an API type
API<00:03:34.120><c> base</c><00:03:34.519><c> and</c><00:03:34.680><c> an</c><00:03:34.879><c> API</c><00:03:35.439><c> key</c><00:03:35.879><c> now</c><00:03:36.040><c> the</c><00:03:36.200><c> API</c><00:03:36.799><c> type</c>

00:03:37.149 --> 00:03:37.159 align:start position:0%
API base and an API key now the API type
 

00:03:37.159 --> 00:03:39.869 align:start position:0%
API base and an API key now the API type
you<00:03:37.280><c> still</c><00:03:37.480><c> need</c><00:03:37.680><c> to</c><00:03:37.840><c> use</c><00:03:38.480><c> open</c><00:03:39.080><c> AI</c><00:03:39.720><c> we're</c>

00:03:39.869 --> 00:03:39.879 align:start position:0%
you still need to use open AI we're
 

00:03:39.879 --> 00:03:43.390 align:start position:0%
you still need to use open AI we're
still<00:03:40.120><c> using</c><00:03:40.760><c> the</c><00:03:40.959><c> API</c><00:03:41.480><c> for</c><00:03:41.879><c> open</c><00:03:42.319><c> AI</c><00:03:43.120><c> but</c><00:03:43.280><c> we</c>

00:03:43.390 --> 00:03:43.400 align:start position:0%
still using the API for open AI but we
 

00:03:43.400 --> 00:03:45.309 align:start position:0%
still using the API for open AI but we
just<00:03:43.519><c> don't</c><00:03:43.680><c> need</c><00:03:43.840><c> to</c><00:03:44.000><c> use</c><00:03:44.200><c> chat</c><00:03:44.480><c> GPT</c><00:03:45.159><c> and</c>

00:03:45.309 --> 00:03:45.319 align:start position:0%
just don't need to use chat GPT and
 

00:03:45.319 --> 00:03:47.910 align:start position:0%
just don't need to use chat GPT and
spend<00:03:45.720><c> you</c><00:03:45.840><c> know</c><00:03:46.000><c> some</c><00:03:46.239><c> sense</c><00:03:46.959><c> on</c><00:03:47.280><c> making</c>

00:03:47.910 --> 00:03:47.920 align:start position:0%
spend you know some sense on making
 

00:03:47.920 --> 00:03:49.830 align:start position:0%
spend you know some sense on making
requests<00:03:48.879><c> instead</c><00:03:49.200><c> this</c><00:03:49.319><c> is</c><00:03:49.400><c> going</c><00:03:49.519><c> to</c><00:03:49.640><c> be</c>

00:03:49.830 --> 00:03:49.840 align:start position:0%
requests instead this is going to be
 

00:03:49.840 --> 00:03:52.309 align:start position:0%
requests instead this is going to be
free<00:03:50.599><c> no</c><00:03:50.840><c> charge</c><00:03:51.280><c> and</c><00:03:51.400><c> we</c><00:03:51.519><c> can</c><00:03:51.640><c> use</c><00:03:52.000><c> open</c>

00:03:52.309 --> 00:03:52.319 align:start position:0%
free no charge and we can use open
 

00:03:52.319 --> 00:03:55.110 align:start position:0%
free no charge and we can use open
source<00:03:52.879><c> llms</c><00:03:53.879><c> so</c><00:03:54.040><c> we'll</c><00:03:54.280><c> have</c><00:03:54.400><c> the</c><00:03:54.519><c> API</c><00:03:54.920><c> type</c>

00:03:55.110 --> 00:03:55.120 align:start position:0%
source llms so we'll have the API type
 

00:03:55.120 --> 00:03:57.670 align:start position:0%
source llms so we'll have the API type
as<00:03:55.280><c> open</c><00:03:55.640><c> AI</c><00:03:56.640><c> this</c><00:03:56.840><c> is</c><00:03:57.040><c> where</c><00:03:57.200><c> we're</c><00:03:57.360><c> going</c><00:03:57.480><c> to</c>

00:03:57.670 --> 00:03:57.680 align:start position:0%
as open AI this is where we're going to
 

00:03:57.680 --> 00:03:59.869 align:start position:0%
as open AI this is where we're going to
put<00:03:57.959><c> our</c><00:03:58.439><c> URL</c><00:03:59.079><c> so</c><00:03:59.239><c> that</c><00:03:59.319><c> it</c><00:03:59.400><c> cannect</c><00:03:59.640><c> connect</c>

00:03:59.869 --> 00:03:59.879 align:start position:0%
put our URL so that it cannect connect
 

00:03:59.879 --> 00:04:02.470 align:start position:0%
put our URL so that it cannect connect
to<00:04:00.000><c> the</c><00:04:00.120><c> local</c><00:04:00.400><c> server</c><00:04:01.400><c> then</c><00:04:01.640><c> the</c><00:04:01.840><c> API</c><00:04:02.200><c> key</c><00:04:02.400><c> can</c>

00:04:02.470 --> 00:04:02.480 align:start position:0%
to the local server then the API key can
 

00:04:02.480 --> 00:04:03.949 align:start position:0%
to the local server then the API key can
just<00:04:02.560><c> be</c><00:04:02.680><c> null</c><00:04:03.079><c> because</c><00:04:03.439><c> again</c><00:04:03.640><c> you</c><00:04:03.760><c> don't</c>

00:04:03.949 --> 00:04:03.959 align:start position:0%
just be null because again you don't
 

00:04:03.959 --> 00:04:06.149 align:start position:0%
just be null because again you don't
need<00:04:04.159><c> an</c><00:04:04.360><c> open</c><00:04:04.720><c> AI</c><00:04:05.239><c> key</c><00:04:05.439><c> for</c><00:04:05.680><c> this</c><00:04:05.959><c> and</c><00:04:06.040><c> then</c>

00:04:06.149 --> 00:04:06.159 align:start position:0%
need an open AI key for this and then
 

00:04:06.159 --> 00:04:08.949 align:start position:0%
need an open AI key for this and then
for<00:04:06.319><c> the</c><00:04:06.439><c> llm</c><00:04:07.000><c> configuration</c><00:04:08.000><c> we</c><00:04:08.239><c> pass</c><00:04:08.439><c> in</c><00:04:08.799><c> the</c>

00:04:08.949 --> 00:04:08.959 align:start position:0%
for the llm configuration we pass in the
 

00:04:08.959 --> 00:04:11.789 align:start position:0%
for the llm configuration we pass in the
configuration<00:04:09.519><c> for</c><00:04:09.799><c> the</c><00:04:09.920><c> API</c><00:04:10.400><c> stuff</c><00:04:11.120><c> and</c><00:04:11.599><c> one</c>

00:04:11.789 --> 00:04:11.799 align:start position:0%
configuration for the API stuff and one
 

00:04:11.799 --> 00:04:13.869 align:start position:0%
configuration for the API stuff and one
thing<00:04:11.959><c> I</c><00:04:12.079><c> did</c><00:04:12.360><c> add</c><00:04:12.640><c> here</c><00:04:13.000><c> is</c><00:04:13.120><c> there's</c><00:04:13.280><c> a</c><00:04:13.480><c> Max</c>

00:04:13.869 --> 00:04:13.879 align:start position:0%
thing I did add here is there's a Max
 

00:04:13.879 --> 00:04:17.550 align:start position:0%
thing I did add here is there's a Max
tokens<00:04:14.680><c> of</c><00:04:15.200><c> -1</c><00:04:16.199><c> and</c><00:04:16.479><c> what</c><00:04:16.639><c> this</c><00:04:16.799><c> means</c><00:04:17.199><c> is</c>

00:04:17.550 --> 00:04:17.560 align:start position:0%
tokens of -1 and what this means is
 

00:04:17.560 --> 00:04:19.909 align:start position:0%
tokens of -1 and what this means is
whenever<00:04:17.919><c> we</c><00:04:18.079><c> have</c><00:04:18.199><c> the</c><00:04:18.359><c> server</c><00:04:18.919><c> running</c>

00:04:19.909 --> 00:04:19.919 align:start position:0%
whenever we have the server running
 

00:04:19.919 --> 00:04:22.790 align:start position:0%
whenever we have the server running
every<00:04:20.359><c> word</c><00:04:20.880><c> essentially</c><00:04:21.359><c> is</c><00:04:21.479><c> a</c><00:04:21.759><c> token</c><00:04:22.560><c> and</c>

00:04:22.790 --> 00:04:22.800 align:start position:0%
every word essentially is a token and
 

00:04:22.800 --> 00:04:24.870 align:start position:0%
every word essentially is a token and
some<00:04:22.960><c> of</c><00:04:23.120><c> the</c><00:04:23.639><c> some</c><00:04:23.759><c> of</c><00:04:23.840><c> the</c><00:04:23.919><c> models</c><00:04:24.360><c> have</c><00:04:24.720><c> like</c>

00:04:24.870 --> 00:04:24.880 align:start position:0%
some of the some of the models have like
 

00:04:24.880 --> 00:04:27.510 align:start position:0%
some of the some of the models have like
a<00:04:25.000><c> max</c><00:04:25.360><c> number</c><00:04:25.639><c> of</c><00:04:25.800><c> tokens</c><00:04:26.160><c> that</c><00:04:26.280><c> you</c><00:04:26.400><c> can</c><00:04:26.720><c> have</c>

00:04:27.510 --> 00:04:27.520 align:start position:0%
a max number of tokens that you can have
 

00:04:27.520 --> 00:04:29.230 align:start position:0%
a max number of tokens that you can have
uh<00:04:27.680><c> like</c><00:04:27.840><c> by</c><00:04:28.040><c> default</c><00:04:28.560><c> so</c><00:04:28.680><c> if</c><00:04:28.759><c> you</c><00:04:28.840><c> set</c><00:04:29.000><c> this</c><00:04:29.120><c> to</c>

00:04:29.230 --> 00:04:29.240 align:start position:0%
uh like by default so if you set this to
 

00:04:29.240 --> 00:04:31.230 align:start position:0%
uh like by default so if you set this to
Nega<00:04:29.680><c> one</c><00:04:29.960><c> now</c><00:04:30.080><c> I</c><00:04:30.160><c> haven't</c><00:04:30.400><c> tested</c><00:04:30.960><c> every</c>

00:04:31.230 --> 00:04:31.240 align:start position:0%
Nega one now I haven't tested every
 

00:04:31.240 --> 00:04:33.629 align:start position:0%
Nega one now I haven't tested every
model<00:04:31.840><c> of</c><00:04:32.000><c> course</c><00:04:32.919><c> but</c><00:04:33.080><c> for</c><00:04:33.240><c> instance</c><00:04:33.479><c> the</c>

00:04:33.629 --> 00:04:33.639 align:start position:0%
model of course but for instance the
 

00:04:33.639 --> 00:04:36.670 align:start position:0%
model of course but for instance the
Llama<00:04:33.960><c> one</c><00:04:34.240><c> had</c><00:04:34.400><c> about</c><00:04:34.600><c> 1,500</c><00:04:35.600><c> and</c><00:04:35.720><c> I</c><00:04:35.840><c> had</c><00:04:36.039><c> like</c>

00:04:36.670 --> 00:04:36.680 align:start position:0%
Llama one had about 1,500 and I had like
 

00:04:36.680 --> 00:04:38.790 align:start position:0%
Llama one had about 1,500 and I had like
a<00:04:36.880><c> lot</c><00:04:37.039><c> of</c><00:04:37.680><c> agents</c><00:04:38.000><c> in</c><00:04:38.120><c> the</c><00:04:38.280><c> group</c><00:04:38.560><c> and</c><00:04:38.680><c> there</c>

00:04:38.790 --> 00:04:38.800 align:start position:0%
a lot of agents in the group and there
 

00:04:38.800 --> 00:04:41.230 align:start position:0%
a lot of agents in the group and there
was<00:04:38.919><c> a</c><00:04:39.000><c> lot</c><00:04:39.120><c> of</c><00:04:39.240><c> talking</c><00:04:39.600><c> involved</c><00:04:40.360><c> when</c><00:04:40.960><c> I</c><00:04:41.039><c> set</c>

00:04:41.230 --> 00:04:41.240 align:start position:0%
was a lot of talking involved when I set
 

00:04:41.240 --> 00:04:42.790 align:start position:0%
was a lot of talking involved when I set
this<00:04:41.360><c> a</c><00:04:41.520><c> negative</c><00:04:41.800><c> one</c><00:04:42.039><c> it</c><00:04:42.160><c> allowed</c><00:04:42.400><c> me</c><00:04:42.479><c> to</c><00:04:42.560><c> go</c>

00:04:42.790 --> 00:04:42.800 align:start position:0%
this a negative one it allowed me to go
 

00:04:42.800 --> 00:04:44.710 align:start position:0%
this a negative one it allowed me to go
through<00:04:42.919><c> the</c><00:04:43.039><c> whole</c><00:04:43.240><c> thing</c><00:04:43.800><c> otherwise</c><00:04:44.639><c> I</c>

00:04:44.710 --> 00:04:44.720 align:start position:0%
through the whole thing otherwise I
 

00:04:44.720 --> 00:04:46.350 align:start position:0%
through the whole thing otherwise I
would<00:04:44.960><c> get</c><00:04:45.240><c> some</c><00:04:45.639><c> it</c><00:04:45.720><c> wasn't</c><00:04:45.919><c> necessarily</c><00:04:46.199><c> an</c>

00:04:46.350 --> 00:04:46.360 align:start position:0%
would get some it wasn't necessarily an
 

00:04:46.360 --> 00:04:48.710 align:start position:0%
would get some it wasn't necessarily an
error<00:04:47.080><c> but</c><00:04:47.199><c> it</c><00:04:47.320><c> would</c><00:04:47.520><c> say</c><00:04:47.800><c> that</c><00:04:48.080><c> I've</c><00:04:48.360><c> reached</c>

00:04:48.710 --> 00:04:48.720 align:start position:0%
error but it would say that I've reached
 

00:04:48.720 --> 00:04:50.350 align:start position:0%
error but it would say that I've reached
the<00:04:48.880><c> maximum</c><00:04:49.199><c> number</c><00:04:49.400><c> of</c><00:04:49.520><c> tokens</c><00:04:50.039><c> so</c><00:04:50.199><c> when</c><00:04:50.280><c> I</c>

00:04:50.350 --> 00:04:50.360 align:start position:0%
the maximum number of tokens so when I
 

00:04:50.360 --> 00:04:52.110 align:start position:0%
the maximum number of tokens so when I
set<00:04:50.520><c> this</c><00:04:50.639><c> to</c><00:04:50.759><c> negative</c><00:04:51.080><c> one</c><00:04:51.720><c> I</c><00:04:51.800><c> was</c><00:04:51.960><c> then</c>

00:04:52.110 --> 00:04:52.120 align:start position:0%
set this to negative one I was then
 

00:04:52.120 --> 00:04:55.189 align:start position:0%
set this to negative one I was then
allowed<00:04:52.440><c> to</c><00:04:52.919><c> have</c><00:04:53.280><c> more</c><00:04:53.919><c> words</c><00:04:54.800><c> uh</c><00:04:54.919><c> in</c><00:04:55.039><c> my</c>

00:04:55.189 --> 00:04:55.199 align:start position:0%
allowed to have more words uh in my
 

00:04:55.199 --> 00:04:56.830 align:start position:0%
allowed to have more words uh in my
description<00:04:55.600><c> and</c><00:04:55.759><c> they</c><00:04:55.840><c> were</c><00:04:56.160><c> able</c><00:04:56.360><c> to</c><00:04:56.479><c> talk</c>

00:04:56.830 --> 00:04:56.840 align:start position:0%
description and they were able to talk
 

00:04:56.840 --> 00:04:58.310 align:start position:0%
description and they were able to talk
to<00:04:57.000><c> each</c><00:04:57.120><c> other</c><00:04:57.320><c> without</c><00:04:57.560><c> interruption</c><00:04:58.160><c> now</c>

00:04:58.310 --> 00:04:58.320 align:start position:0%
to each other without interruption now
 

00:04:58.320 --> 00:05:00.710 align:start position:0%
to each other without interruption now
for<00:04:58.680><c> what</c><00:04:59.039><c> the</c><00:04:59.160><c> use</c><00:04:59.440><c> case</c><00:04:59.600><c> Cas</c><00:04:59.800><c> is</c><00:05:00.120><c> I</c><00:05:00.240><c> have</c><00:05:00.520><c> five</c>

00:05:00.710 --> 00:05:00.720 align:start position:0%
for what the use case Cas is I have five
 

00:05:00.720 --> 00:05:03.230 align:start position:0%
for what the use case Cas is I have five
people<00:05:01.039><c> here</c><00:05:01.280><c> I</c><00:05:01.440><c> have</c><00:05:01.759><c> me</c><00:05:02.199><c> the</c><00:05:02.320><c> human</c><00:05:02.639><c> admin</c>

00:05:03.230 --> 00:05:03.240 align:start position:0%
people here I have me the human admin
 

00:05:03.240 --> 00:05:04.790 align:start position:0%
people here I have me the human admin
and<00:05:03.320><c> then</c><00:05:03.440><c> I</c><00:05:03.520><c> have</c><00:05:03.639><c> a</c><00:05:03.840><c> content</c><00:05:04.199><c> creator</c><00:05:04.680><c> a</c>

00:05:04.790 --> 00:05:04.800 align:start position:0%
and then I have a content creator a
 

00:05:04.800 --> 00:05:06.629 align:start position:0%
and then I have a content creator a
script<00:05:05.120><c> writer</c><00:05:05.479><c> a</c><00:05:05.639><c> researcher</c><00:05:06.320><c> and</c><00:05:06.479><c> a</c>

00:05:06.629 --> 00:05:06.639 align:start position:0%
script writer a researcher and a
 

00:05:06.639 --> 00:05:08.990 align:start position:0%
script writer a researcher and a
reviewer<00:05:07.560><c> and</c><00:05:07.720><c> the</c><00:05:07.919><c> whole</c><00:05:08.240><c> idea</c><00:05:08.639><c> is</c><00:05:08.759><c> that</c><00:05:08.880><c> I</c>

00:05:08.990 --> 00:05:09.000 align:start position:0%
reviewer and the whole idea is that I
 

00:05:09.000 --> 00:05:10.629 align:start position:0%
reviewer and the whole idea is that I
want<00:05:09.120><c> to</c><00:05:09.280><c> create</c><00:05:09.479><c> a</c><00:05:09.600><c> YouTube</c><00:05:09.960><c> script</c><00:05:10.479><c> that</c>

00:05:10.629 --> 00:05:10.639 align:start position:0%
want to create a YouTube script that
 

00:05:10.639 --> 00:05:13.550 align:start position:0%
want to create a YouTube script that
talks<00:05:10.919><c> about</c><00:05:11.080><c> the</c><00:05:11.240><c> latest</c><00:05:11.720><c> paper</c><00:05:12.199><c> about</c><00:05:12.560><c> gb4</c>

00:05:13.550 --> 00:05:13.560 align:start position:0%
talks about the latest paper about gb4
 

00:05:13.560 --> 00:05:15.670 align:start position:0%
talks about the latest paper about gb4
basically<00:05:14.039><c> and</c><00:05:14.199><c> its</c><00:05:14.400><c> potential</c><00:05:14.800><c> applications</c>

00:05:15.670 --> 00:05:15.680 align:start position:0%
basically and its potential applications
 

00:05:15.680 --> 00:05:17.070 align:start position:0%
basically and its potential applications
and<00:05:15.840><c> because</c><00:05:16.160><c> I</c><00:05:16.240><c> wanted</c><00:05:16.520><c> this</c><00:05:16.600><c> to</c><00:05:16.680><c> be</c><00:05:16.759><c> a</c><00:05:16.840><c> group</c>

00:05:17.070 --> 00:05:17.080 align:start position:0%
and because I wanted this to be a group
 

00:05:17.080 --> 00:05:19.189 align:start position:0%
and because I wanted this to be a group
chat<00:05:17.440><c> I</c><00:05:17.560><c> created</c><00:05:17.880><c> a</c><00:05:17.960><c> group</c><00:05:18.160><c> chat</c><00:05:18.440><c> here</c><00:05:18.720><c> put</c><00:05:19.039><c> all</c>

00:05:19.189 --> 00:05:19.199 align:start position:0%
chat I created a group chat here put all
 

00:05:19.199 --> 00:05:21.550 align:start position:0%
chat I created a group chat here put all
of<00:05:19.400><c> the</c><00:05:19.560><c> agents</c><00:05:20.000><c> here</c><00:05:20.319><c> together</c><00:05:21.160><c> and</c><00:05:21.280><c> then</c><00:05:21.440><c> I</c>

00:05:21.550 --> 00:05:21.560 align:start position:0%
of the agents here together and then I
 

00:05:21.560 --> 00:05:23.629 align:start position:0%
of the agents here together and then I
have<00:05:21.680><c> a</c><00:05:21.840><c> manager</c><00:05:22.440><c> set</c><00:05:22.680><c> to</c><00:05:22.840><c> the</c><00:05:22.960><c> group</c><00:05:23.199><c> chat</c>

00:05:23.629 --> 00:05:23.639 align:start position:0%
have a manager set to the group chat
 

00:05:23.639 --> 00:05:26.070 align:start position:0%
have a manager set to the group chat
manager<00:05:24.639><c> and</c><00:05:24.800><c> then</c><00:05:25.199><c> we</c><00:05:25.360><c> give</c><00:05:25.520><c> it</c><00:05:25.759><c> the</c><00:05:25.880><c> group</c>

00:05:26.070 --> 00:05:26.080 align:start position:0%
manager and then we give it the group
 

00:05:26.080 --> 00:05:27.990 align:start position:0%
manager and then we give it the group
chat<00:05:26.319><c> so</c><00:05:26.720><c> all</c><00:05:27.000><c> of</c><00:05:27.120><c> the</c><00:05:27.280><c> agents</c><00:05:27.560><c> are</c><00:05:27.680><c> able</c><00:05:27.880><c> to</c>

00:05:27.990 --> 00:05:28.000 align:start position:0%
chat so all of the agents are able to
 

00:05:28.000 --> 00:05:30.430 align:start position:0%
chat so all of the agents are able to
talk<00:05:28.120><c> to</c><00:05:28.319><c> each</c><00:05:28.440><c> other</c><00:05:29.080><c> uh</c><00:05:29.240><c> give</c><00:05:29.759><c> the</c><00:05:29.840><c> llm</c>

00:05:30.430 --> 00:05:30.440 align:start position:0%
talk to each other uh give the llm
 

00:05:30.440 --> 00:05:33.150 align:start position:0%
talk to each other uh give the llm
configuration<00:05:31.440><c> and</c><00:05:31.680><c> then</c><00:05:32.120><c> me</c><00:05:32.319><c> as</c><00:05:32.479><c> the</c><00:05:32.639><c> admin</c><00:05:33.080><c> I</c>

00:05:33.150 --> 00:05:33.160 align:start position:0%
configuration and then me as the admin I
 

00:05:33.160 --> 00:05:35.550 align:start position:0%
configuration and then me as the admin I
will<00:05:33.440><c> initiate</c><00:05:33.880><c> the</c><00:05:34.039><c> chat</c><00:05:34.639><c> with</c><00:05:35.160><c> the</c><00:05:35.319><c> whole</c>

00:05:35.550 --> 00:05:35.560 align:start position:0%
will initiate the chat with the whole
 

00:05:35.560 --> 00:05:37.350 align:start position:0%
will initiate the chat with the whole
group<00:05:36.160><c> and</c><00:05:36.240><c> then</c><00:05:36.360><c> the</c><00:05:36.520><c> last</c><00:05:36.720><c> step</c><00:05:37.039><c> is</c><00:05:37.160><c> we</c><00:05:37.280><c> go</c>

00:05:37.350 --> 00:05:37.360 align:start position:0%
group and then the last step is we go
 

00:05:37.360 --> 00:05:39.029 align:start position:0%
group and then the last step is we go
ahead<00:05:37.479><c> and</c><00:05:37.600><c> just</c><00:05:37.720><c> run</c><00:05:38.000><c> this</c><00:05:38.440><c> so</c><00:05:38.680><c> go</c><00:05:38.800><c> over</c><00:05:38.919><c> to</c>

00:05:39.029 --> 00:05:39.039 align:start position:0%
ahead and just run this so go over to
 

00:05:39.039 --> 00:05:42.830 align:start position:0%
ahead and just run this so go over to
your<00:05:39.280><c> terminal</c><00:05:40.280><c> type</c><00:05:40.680><c> python</c><00:05:41.680><c> Lama</c>

00:05:42.830 --> 00:05:42.840 align:start position:0%
your terminal type python Lama
 

00:05:42.840 --> 00:05:45.270 align:start position:0%
your terminal type python Lama
lmore

00:05:45.270 --> 00:05:45.280 align:start position:0%
lmore
 

00:05:45.280 --> 00:05:47.950 align:start position:0%
lmore
test.py<00:05:46.280><c> and</c><00:05:46.520><c> you</c><00:05:46.840><c> you</c><00:05:46.960><c> just</c><00:05:47.120><c> hit</c><00:05:47.280><c> enter</c><00:05:47.840><c> and</c>

00:05:47.950 --> 00:05:47.960 align:start position:0%
test.py and you you just hit enter and
 

00:05:47.960 --> 00:05:50.350 align:start position:0%
test.py and you you just hit enter and
so<00:05:48.360><c> I'll</c><00:05:48.520><c> go</c><00:05:48.639><c> ahead</c><00:05:48.800><c> and</c><00:05:48.880><c> run</c><00:05:49.160><c> this</c><00:05:49.479><c> off</c><00:05:49.759><c> screen</c>

00:05:50.350 --> 00:05:50.360 align:start position:0%
so I'll go ahead and run this off screen
 

00:05:50.360 --> 00:05:51.950 align:start position:0%
so I'll go ahead and run this off screen
and<00:05:50.479><c> then</c><00:05:50.600><c> when</c><00:05:50.720><c> we</c><00:05:50.840><c> come</c><00:05:51.080><c> back</c><00:05:51.280><c> I'll</c><00:05:51.520><c> show</c><00:05:51.759><c> you</c>

00:05:51.950 --> 00:05:51.960 align:start position:0%
and then when we come back I'll show you
 

00:05:51.960 --> 00:05:54.990 align:start position:0%
and then when we come back I'll show you
the<00:05:52.120><c> chat</c><00:05:52.440><c> and</c><00:05:52.840><c> how</c><00:05:53.000><c> it</c><00:05:53.280><c> works</c><00:05:54.280><c> okay</c><00:05:54.639><c> and</c><00:05:54.759><c> we're</c>

00:05:54.990 --> 00:05:55.000 align:start position:0%
the chat and how it works okay and we're
 

00:05:55.000 --> 00:05:58.110 align:start position:0%
the chat and how it works okay and we're
done<00:05:55.560><c> it</c><00:05:55.759><c> ran</c><00:05:56.680><c> and</c><00:05:56.880><c> as</c><00:05:56.960><c> you</c><00:05:57.080><c> can</c><00:05:57.199><c> see</c><00:05:57.520><c> here</c><00:05:57.840><c> in</c>

00:05:58.110 --> 00:05:58.120 align:start position:0%
done it ran and as you can see here in
 

00:05:58.120 --> 00:06:00.790 align:start position:0%
done it ran and as you can see here in
LM<00:05:58.520><c> studio</c><00:05:58.880><c> in</c><00:05:59.000><c> the</c><00:05:59.080><c> server</c><00:05:59.639><c> logs</c><00:06:00.199><c> it'll</c><00:06:00.600><c> give</c>

00:06:00.790 --> 00:06:00.800 align:start position:0%
LM studio in the server logs it'll give
 

00:06:00.800 --> 00:06:02.870 align:start position:0%
LM studio in the server logs it'll give
you<00:06:01.280><c> the</c><00:06:01.440><c> conversations</c><00:06:02.000><c> that</c><00:06:02.160><c> everybody</c><00:06:02.560><c> had</c>

00:06:02.870 --> 00:06:02.880 align:start position:0%
you the conversations that everybody had
 

00:06:02.880 --> 00:06:05.029 align:start position:0%
you the conversations that everybody had
with<00:06:03.120><c> each</c><00:06:03.280><c> other</c><00:06:04.080><c> and</c><00:06:04.280><c> what</c><00:06:04.400><c> I</c><00:06:04.520><c> meant</c><00:06:04.720><c> by</c><00:06:04.880><c> the</c>

00:06:05.029 --> 00:06:05.039 align:start position:0%
with each other and what I meant by the
 

00:06:05.039 --> 00:06:07.070 align:start position:0%
with each other and what I meant by the
tokens<00:06:05.520><c> I'll</c><00:06:05.639><c> show</c><00:06:05.800><c> you</c><00:06:05.960><c> a</c><00:06:06.120><c> quick</c><00:06:06.599><c> so</c><00:06:06.880><c> one</c>

00:06:07.070 --> 00:06:07.080 align:start position:0%
tokens I'll show you a quick so one
 

00:06:07.080 --> 00:06:09.469 align:start position:0%
tokens I'll show you a quick so one
token<00:06:07.720><c> was</c><00:06:07.960><c> the</c><00:06:08.080><c> word</c><00:06:08.319><c> note</c><00:06:08.759><c> second</c><00:06:09.039><c> token</c><00:06:09.360><c> was</c>

00:06:09.469 --> 00:06:09.479 align:start position:0%
token was the word note second token was
 

00:06:09.479 --> 00:06:12.790 align:start position:0%
token was the word note second token was
a<00:06:09.720><c> semicolon</c><00:06:10.720><c> third</c><00:06:10.960><c> token</c><00:06:11.440><c> was</c><00:06:11.919><c> the</c><00:06:12.199><c> letter</c><00:06:12.520><c> I</c>

00:06:12.790 --> 00:06:12.800 align:start position:0%
a semicolon third token was the letter I
 

00:06:12.800 --> 00:06:13.830 align:start position:0%
a semicolon third token was the letter I
and<00:06:12.880><c> then</c><00:06:13.000><c> the</c><00:06:13.120><c> fourth</c><00:06:13.360><c> one</c><00:06:13.520><c> was</c><00:06:13.680><c> the</c>

00:06:13.830 --> 00:06:13.840 align:start position:0%
and then the fourth one was the
 

00:06:13.840 --> 00:06:15.230 align:start position:0%
and then the fourth one was the
apostrophe<00:06:14.360><c> and</c><00:06:14.440><c> so</c><00:06:14.599><c> forth</c><00:06:14.800><c> so</c><00:06:14.880><c> each</c><00:06:15.000><c> of</c><00:06:15.120><c> these</c>

00:06:15.230 --> 00:06:15.240 align:start position:0%
apostrophe and so forth so each of these
 

00:06:15.240 --> 00:06:17.390 align:start position:0%
apostrophe and so forth so each of these
are<00:06:15.400><c> tokens</c><00:06:16.120><c> and</c><00:06:16.280><c> with</c><00:06:16.400><c> the</c><00:06:16.479><c> max</c><00:06:16.759><c> token</c><00:06:17.039><c> minus</c>

00:06:17.390 --> 00:06:17.400 align:start position:0%
are tokens and with the max token minus
 

00:06:17.400 --> 00:06:19.469 align:start position:0%
are tokens and with the max token minus
one<00:06:18.160><c> um</c><00:06:18.280><c> you</c><00:06:18.360><c> don't</c><00:06:18.520><c> have</c><00:06:18.639><c> to</c><00:06:18.759><c> worry</c><00:06:19.000><c> about</c>

00:06:19.469 --> 00:06:19.479 align:start position:0%
one um you don't have to worry about
 

00:06:19.479 --> 00:06:21.670 align:start position:0%
one um you don't have to worry about
each<00:06:19.720><c> model</c><00:06:20.520><c> what</c><00:06:20.680><c> like</c><00:06:20.800><c> the</c><00:06:20.960><c> maximum</c><00:06:21.360><c> amount</c>

00:06:21.670 --> 00:06:21.680 align:start position:0%
each model what like the maximum amount
 

00:06:21.680 --> 00:06:23.830 align:start position:0%
each model what like the maximum amount
is<00:06:22.199><c> so</c><00:06:22.479><c> that</c><00:06:22.720><c> that</c><00:06:22.840><c> help</c><00:06:23.000><c> will</c><00:06:23.120><c> help</c><00:06:23.280><c> you</c><00:06:23.720><c> it</c>

00:06:23.830 --> 00:06:23.840 align:start position:0%
is so that that help will help you it
 

00:06:23.840 --> 00:06:26.070 align:start position:0%
is so that that help will help you it
can't<00:06:24.039><c> handle</c><00:06:24.520><c> some</c><00:06:24.680><c> of</c><00:06:24.880><c> these</c><00:06:25.080><c> models</c><00:06:25.639><c> well</c>

00:06:26.070 --> 00:06:26.080 align:start position:0%
can't handle some of these models well
 

00:06:26.080 --> 00:06:28.309 align:start position:0%
can't handle some of these models well
whenever<00:06:26.400><c> it's</c><00:06:26.560><c> running</c><00:06:27.560><c> um</c><00:06:27.800><c> so</c><00:06:28.039><c> as</c><00:06:28.120><c> you</c><00:06:28.199><c> can</c>

00:06:28.309 --> 00:06:28.319 align:start position:0%
whenever it's running um so as you can
 

00:06:28.319 --> 00:06:29.790 align:start position:0%
whenever it's running um so as you can
see<00:06:28.520><c> here</c><00:06:28.639><c> we</c><00:06:28.759><c> had</c><00:06:28.880><c> the</c><00:06:28.960><c> full</c><00:06:29.080><c> convers</c><00:06:29.520><c> St</c>

00:06:29.790 --> 00:06:29.800 align:start position:0%
see here we had the full convers St
 

00:06:29.800 --> 00:06:33.350 align:start position:0%
see here we had the full convers St
studio<00:06:30.599><c> and</c><00:06:30.759><c> if</c><00:06:30.919><c> we</c><00:06:31.160><c> also</c><00:06:32.000><c> go</c><00:06:32.400><c> back</c><00:06:32.759><c> to</c><00:06:33.120><c> pie</c>

00:06:33.350 --> 00:06:33.360 align:start position:0%
studio and if we also go back to pie
 

00:06:33.360 --> 00:06:35.469 align:start position:0%
studio and if we also go back to pie
charm<00:06:34.080><c> so</c><00:06:34.240><c> you</c><00:06:34.319><c> can</c><00:06:34.479><c> see</c><00:06:34.759><c> here</c><00:06:35.080><c> we</c><00:06:35.240><c> had</c><00:06:35.360><c> the</c>

00:06:35.469 --> 00:06:35.479 align:start position:0%
charm so you can see here we had the
 

00:06:35.479 --> 00:06:36.390 align:start position:0%
charm so you can see here we had the
same

00:06:36.390 --> 00:06:36.400 align:start position:0%
same
 

00:06:36.400 --> 00:06:38.230 align:start position:0%
same
conversation<00:06:37.400><c> because</c><00:06:37.639><c> I</c><00:06:37.800><c> changed</c><00:06:38.080><c> the</c>

00:06:38.230 --> 00:06:38.240 align:start position:0%
conversation because I changed the
 

00:06:38.240 --> 00:06:39.629 align:start position:0%
conversation because I changed the
temperature<00:06:39.039><c> they</c><00:06:39.160><c> didn't</c><00:06:39.360><c> really</c>

00:06:39.629 --> 00:06:39.639 align:start position:0%
temperature they didn't really
 

00:06:39.639 --> 00:06:42.029 align:start position:0%
temperature they didn't really
communicate<00:06:40.240><c> that</c><00:06:40.440><c> well</c><00:06:40.960><c> with</c><00:06:41.199><c> each</c><00:06:41.400><c> other</c>

00:06:42.029 --> 00:06:42.039 align:start position:0%
communicate that well with each other
 

00:06:42.039 --> 00:06:43.110 align:start position:0%
communicate that well with each other
and<00:06:42.160><c> they</c><00:06:42.240><c> didn't</c><00:06:42.440><c> come</c><00:06:42.560><c> up</c><00:06:42.720><c> with</c><00:06:42.880><c> like</c><00:06:43.000><c> a</c>

00:06:43.110 --> 00:06:43.120 align:start position:0%
and they didn't come up with like a
 

00:06:43.120 --> 00:06:47.070 align:start position:0%
and they didn't come up with like a
super<00:06:43.599><c> revised</c><00:06:44.440><c> script</c><00:06:45.440><c> um</c><00:06:46.280><c> but</c><00:06:46.599><c> here's</c><00:06:46.919><c> like</c>

00:06:47.070 --> 00:06:47.080 align:start position:0%
super revised script um but here's like
 

00:06:47.080 --> 00:06:48.749 align:start position:0%
super revised script um but here's like
the<00:06:47.240><c> in</c><00:06:47.560><c> the</c><00:06:47.720><c> the</c><00:06:47.880><c> points</c><00:06:48.199><c> that</c><00:06:48.319><c> the</c><00:06:48.440><c> content</c>

00:06:48.749 --> 00:06:48.759 align:start position:0%
the in the the points that the content
 

00:06:48.759 --> 00:06:51.029 align:start position:0%
the in the the points that the content
creator<00:06:49.160><c> wanted</c><00:06:49.840><c> here</c><00:06:49.960><c> was</c><00:06:50.120><c> the</c><00:06:50.280><c> introduction</c>

00:06:51.029 --> 00:06:51.039 align:start position:0%
creator wanted here was the introduction
 

00:06:51.039 --> 00:06:53.950 align:start position:0%
creator wanted here was the introduction
and<00:06:51.160><c> then</c><00:06:51.479><c> basically</c><00:06:52.479><c> uh</c><00:06:52.599><c> the</c><00:06:52.759><c> script</c><00:06:53.120><c> writer</c>

00:06:53.950 --> 00:06:53.960 align:start position:0%
and then basically uh the script writer
 

00:06:53.960 --> 00:06:55.950 align:start position:0%
and then basically uh the script writer
research<00:06:54.400><c> and</c><00:06:54.680><c> everybody</c><00:06:55.120><c> was</c><00:06:55.400><c> done</c><00:06:55.720><c> oh</c><00:06:55.879><c> they</c>

00:06:55.950 --> 00:06:55.960 align:start position:0%
research and everybody was done oh they
 

00:06:55.960 --> 00:06:58.110 align:start position:0%
research and everybody was done oh they
were<00:06:56.120><c> like</c><00:06:56.319><c> yep</c><00:06:56.759><c> that's</c><00:06:57.039><c> good</c><00:06:57.680><c> but</c><00:06:57.840><c> you</c><00:06:57.919><c> know</c>

00:06:58.110 --> 00:06:58.120 align:start position:0%
were like yep that's good but you know
 

00:06:58.120 --> 00:07:00.150 align:start position:0%
were like yep that's good but you know
this<00:06:58.199><c> was</c><00:06:58.319><c> an</c><00:06:58.520><c> example</c><00:06:59.240><c> um</c><00:06:59.520><c> actually</c><00:06:59.919><c> when</c><00:07:00.039><c> I</c>

00:07:00.150 --> 00:07:00.160 align:start position:0%
this was an example um actually when I
 

00:07:00.160 --> 00:07:02.790 align:start position:0%
this was an example um actually when I
ran<00:07:00.440><c> this</c><00:07:00.960><c> last</c><00:07:01.280><c> night</c><00:07:01.680><c> it</c><00:07:02.360><c> it</c><00:07:02.479><c> gave</c><00:07:02.639><c> me</c>

00:07:02.790 --> 00:07:02.800 align:start position:0%
ran this last night it it gave me
 

00:07:02.800 --> 00:07:04.589 align:start position:0%
ran this last night it it gave me
everything<00:07:03.160><c> and</c><00:07:03.319><c> I</c><00:07:03.400><c> think</c><00:07:03.680><c> part</c><00:07:03.960><c> of</c><00:07:04.400><c> the</c>

00:07:04.589 --> 00:07:04.599 align:start position:0%
everything and I think part of the
 

00:07:04.599 --> 00:07:06.270 align:start position:0%
everything and I think part of the
request<00:07:04.919><c> timeouts</c><00:07:05.360><c> or</c><00:07:05.599><c> maybe</c><00:07:05.800><c> something</c><00:07:06.120><c> up</c>

00:07:06.270 --> 00:07:06.280 align:start position:0%
request timeouts or maybe something up
 

00:07:06.280 --> 00:07:08.909 align:start position:0%
request timeouts or maybe something up
with<00:07:06.479><c> the</c><00:07:06.680><c> servers</c><00:07:07.039><c> with</c><00:07:07.199><c> LM</c><00:07:07.680><c> studio</c><00:07:08.680><c> um</c>

00:07:08.909 --> 00:07:08.919 align:start position:0%
with the servers with LM studio um
 

00:07:08.919 --> 00:07:10.550 align:start position:0%
with the servers with LM studio um
because<00:07:09.120><c> it</c><00:07:09.199><c> was</c><00:07:09.360><c> taking</c><00:07:09.720><c> quite</c><00:07:09.919><c> a</c><00:07:10.039><c> long</c><00:07:10.240><c> time</c>

00:07:10.550 --> 00:07:10.560 align:start position:0%
because it was taking quite a long time
 

00:07:10.560 --> 00:07:13.350 align:start position:0%
because it was taking quite a long time
last<00:07:10.759><c> night</c><00:07:10.960><c> it</c><00:07:11.080><c> was</c><00:07:11.599><c> really</c><00:07:11.919><c> quick</c><00:07:12.800><c> um</c><00:07:13.120><c> but</c>

00:07:13.350 --> 00:07:13.360 align:start position:0%
last night it was really quick um but
 

00:07:13.360 --> 00:07:14.550 align:start position:0%
last night it was really quick um but
that's<00:07:13.599><c> okay</c><00:07:13.960><c> you're</c><00:07:14.080><c> going</c><00:07:14.199><c> to</c><00:07:14.280><c> run</c><00:07:14.440><c> the</c>

00:07:14.550 --> 00:07:14.560 align:start position:0%
that's okay you're going to run the
 

00:07:14.560 --> 00:07:16.670 align:start position:0%
that's okay you're going to run the
stuff<00:07:14.759><c> whenever</c><00:07:15.039><c> you</c><00:07:15.440><c> do</c><00:07:15.639><c> things</c><00:07:15.840><c> like</c><00:07:16.000><c> this</c>

00:07:16.670 --> 00:07:16.680 align:start position:0%
stuff whenever you do things like this
 

00:07:16.680 --> 00:07:18.909 align:start position:0%
stuff whenever you do things like this
um<00:07:16.879><c> that's</c><00:07:17.080><c> okay</c><00:07:17.240><c> we</c><00:07:17.440><c> just</c><00:07:17.680><c> keep</c><00:07:17.879><c> trying</c><00:07:18.400><c> and</c>

00:07:18.909 --> 00:07:18.919 align:start position:0%
um that's okay we just keep trying and
 

00:07:18.919 --> 00:07:20.990 align:start position:0%
um that's okay we just keep trying and
maybe<00:07:19.160><c> my</c><00:07:19.319><c> prompting</c><00:07:19.720><c> could</c><00:07:19.840><c> be</c><00:07:20.000><c> better</c><00:07:20.360><c> but</c>

00:07:20.990 --> 00:07:21.000 align:start position:0%
maybe my prompting could be better but
 

00:07:21.000 --> 00:07:23.430 align:start position:0%
maybe my prompting could be better but
uh<00:07:21.120><c> anyways</c><00:07:21.759><c> it</c><00:07:21.960><c> worked</c><00:07:22.360><c> we</c><00:07:22.520><c> able</c><00:07:22.720><c> to</c><00:07:22.840><c> use</c><00:07:23.080><c> a</c>

00:07:23.430 --> 00:07:23.440 align:start position:0%
uh anyways it worked we able to use a
 

00:07:23.440 --> 00:07:25.790 align:start position:0%
uh anyways it worked we able to use a
different<00:07:23.840><c> open</c><00:07:24.160><c> source</c><00:07:24.520><c> model</c><00:07:25.280><c> to</c><00:07:25.479><c> create</c><00:07:25.680><c> a</c>

00:07:25.790 --> 00:07:25.800 align:start position:0%
different open source model to create a
 

00:07:25.800 --> 00:07:28.990 align:start position:0%
different open source model to create a
YouTube<00:07:26.160><c> script</c><00:07:26.680><c> about</c><00:07:27.639><c> uh</c><00:07:27.800><c> a</c><00:07:27.960><c> white</c><00:07:28.199><c> paper</c><00:07:28.759><c> on</c>

00:07:28.990 --> 00:07:29.000 align:start position:0%
YouTube script about uh a white paper on
 

00:07:29.000 --> 00:07:30.270 align:start position:0%
YouTube script about uh a white paper on
AI

00:07:30.270 --> 00:07:30.280 align:start position:0%
AI
 

00:07:30.280 --> 00:07:32.189 align:start position:0%
AI
all<00:07:30.400><c> right</c><00:07:30.599><c> and</c><00:07:30.720><c> so</c><00:07:30.960><c> we</c><00:07:31.120><c> have</c><00:07:31.319><c> successfully</c>

00:07:32.189 --> 00:07:32.199 align:start position:0%
all right and so we have successfully
 

00:07:32.199 --> 00:07:34.909 align:start position:0%
all right and so we have successfully
used<00:07:32.639><c> other</c><00:07:32.960><c> open</c><00:07:33.280><c> source</c><00:07:33.560><c> llms</c><00:07:34.520><c> using</c><00:07:34.800><c> a</c>

00:07:34.909 --> 00:07:34.919 align:start position:0%
used other open source llms using a
 

00:07:34.919 --> 00:07:36.950 align:start position:0%
used other open source llms using a
piece<00:07:35.080><c> of</c><00:07:35.240><c> software</c><00:07:35.599><c> called</c><00:07:35.759><c> LM</c><00:07:36.080><c> Studio</c><00:07:36.840><c> we</c>

00:07:36.950 --> 00:07:36.960 align:start position:0%
piece of software called LM Studio we
 

00:07:36.960 --> 00:07:39.390 align:start position:0%
piece of software called LM Studio we
connected<00:07:37.400><c> it</c><00:07:37.479><c> to</c><00:07:37.599><c> autogen</c><00:07:38.479><c> and</c><00:07:38.599><c> then</c><00:07:38.840><c> in</c>

00:07:39.390 --> 00:07:39.400 align:start position:0%
connected it to autogen and then in
 

00:07:39.400 --> 00:07:42.790 align:start position:0%
connected it to autogen and then in
autogen<00:07:40.400><c> we</c><00:07:40.599><c> had</c><00:07:40.919><c> prompts</c><00:07:41.479><c> and</c><00:07:41.720><c> AI</c><00:07:42.120><c> agents</c>

00:07:42.790 --> 00:07:42.800 align:start position:0%
autogen we had prompts and AI agents
 

00:07:42.800 --> 00:07:44.990 align:start position:0%
autogen we had prompts and AI agents
create<00:07:43.199><c> a</c><00:07:43.360><c> YouTube</c><00:07:43.720><c> script</c><00:07:44.120><c> for</c><00:07:44.319><c> us</c><00:07:44.759><c> we</c><00:07:44.879><c> just</c>

00:07:44.990 --> 00:07:45.000 align:start position:0%
create a YouTube script for us we just
 

00:07:45.000 --> 00:07:47.309 align:start position:0%
create a YouTube script for us we just
had<00:07:45.120><c> a</c><00:07:45.360><c> group</c><00:07:45.560><c> chat</c><00:07:45.759><c> to</c><00:07:45.919><c> do</c><00:07:46.159><c> this</c><00:07:46.639><c> and</c><00:07:47.080><c> kind</c><00:07:47.199><c> of</c>

00:07:47.309 --> 00:07:47.319 align:start position:0%
had a group chat to do this and kind of
 

00:07:47.319 --> 00:07:49.070 align:start position:0%
had a group chat to do this and kind of
change<00:07:47.639><c> some</c><00:07:47.800><c> things</c><00:07:48.159><c> because</c><00:07:48.440><c> I</c><00:07:48.599><c> did</c><00:07:48.800><c> I</c><00:07:48.879><c> do</c>

00:07:49.070 --> 00:07:49.080 align:start position:0%
change some things because I did I do
 

00:07:49.080 --> 00:07:51.909 align:start position:0%
change some things because I did I do
notice<00:07:49.440><c> that</c><00:07:49.680><c> at</c><00:07:50.039><c> night</c><00:07:51.039><c> uh</c><00:07:51.479><c> whenever</c><00:07:51.800><c> I</c>

00:07:51.909 --> 00:07:51.919 align:start position:0%
notice that at night uh whenever I
 

00:07:51.919 --> 00:07:53.869 align:start position:0%
notice that at night uh whenever I
connect<00:07:52.199><c> autogen</c><00:07:52.599><c> with</c><00:07:52.720><c> open</c><00:07:52.919><c> source</c><00:07:53.120><c> llms</c>

00:07:53.869 --> 00:07:53.879 align:start position:0%
connect autogen with open source llms
 

00:07:53.879 --> 00:07:57.230 align:start position:0%
connect autogen with open source llms
with<00:07:54.080><c> LM</c><00:07:54.400><c> Studio</c><00:07:55.000><c> it</c><00:07:55.120><c> runs</c><00:07:55.400><c> a</c><00:07:55.520><c> lot</c><00:07:55.879><c> smoother</c><00:07:56.879><c> um</c>

00:07:57.230 --> 00:07:57.240 align:start position:0%
with LM Studio it runs a lot smoother um
 

00:07:57.240 --> 00:07:58.629 align:start position:0%
with LM Studio it runs a lot smoother um
and<00:07:57.319><c> it</c><00:07:57.400><c> runs</c><00:07:57.639><c> a</c><00:07:57.720><c> lot</c><00:07:57.879><c> better</c><00:07:58.080><c> and</c><00:07:58.240><c> quicker</c><00:07:58.520><c> and</c>

00:07:58.629 --> 00:07:58.639 align:start position:0%
and it runs a lot better and quicker and
 

00:07:58.639 --> 00:08:00.790 align:start position:0%
and it runs a lot better and quicker and
I<00:07:58.720><c> don't</c><00:07:58.840><c> get</c><00:07:59.000><c> request</c><00:07:59.520><c> timeouts</c><00:08:00.440><c> um</c><00:08:00.560><c> so</c><00:08:00.720><c> if</c>

00:08:00.790 --> 00:08:00.800 align:start position:0%
I don't get request timeouts um so if
 

00:08:00.800 --> 00:08:02.309 align:start position:0%
I don't get request timeouts um so if
you<00:08:00.879><c> do</c><00:08:01.000><c> end</c><00:08:01.120><c> up</c><00:08:01.240><c> getting</c><00:08:01.560><c> request</c><00:08:01.879><c> timeouts</c><00:08:02.280><c> I</c>

00:08:02.309 --> 00:08:02.319 align:start position:0%
you do end up getting request timeouts I
 

00:08:02.319 --> 00:08:03.749 align:start position:0%
you do end up getting request timeouts I
will<00:08:02.520><c> suggest</c><00:08:02.840><c> just</c><00:08:03.039><c> increasing</c><00:08:03.479><c> that</c><00:08:03.599><c> in</c><00:08:03.680><c> the</c>

00:08:03.749 --> 00:08:03.759 align:start position:0%
will suggest just increasing that in the
 

00:08:03.759 --> 00:08:06.430 align:start position:0%
will suggest just increasing that in the
llm<00:08:04.240><c> config</c><00:08:04.919><c> like</c><00:08:05.039><c> you'll</c><00:08:05.280><c> see</c><00:08:05.759><c> whenever</c><00:08:06.080><c> you</c>

00:08:06.430 --> 00:08:06.440 align:start position:0%
llm config like you'll see whenever you
 

00:08:06.440 --> 00:08:07.950 align:start position:0%
llm config like you'll see whenever you
uh<00:08:06.560><c> look</c><00:08:06.680><c> at</c><00:08:06.840><c> all</c><00:08:06.960><c> my</c><00:08:07.120><c> configurations</c><00:08:07.720><c> for</c>

00:08:07.950 --> 00:08:07.960 align:start position:0%
uh look at all my configurations for
 

00:08:07.960 --> 00:08:10.070 align:start position:0%
uh look at all my configurations for
this<00:08:08.120><c> use</c><00:08:08.440><c> case</c><00:08:09.120><c> but</c><00:08:09.360><c> if</c><00:08:09.479><c> you</c><00:08:09.560><c> have</c><00:08:09.680><c> any</c><00:08:09.840><c> other</c>

00:08:10.070 --> 00:08:10.080 align:start position:0%
this use case but if you have any other
 

00:08:10.080 --> 00:08:12.029 align:start position:0%
this use case but if you have any other
questions<00:08:10.639><c> or</c><00:08:11.039><c> something</c><00:08:11.319><c> didn't</c><00:08:11.639><c> quite</c><00:08:11.840><c> make</c>

00:08:12.029 --> 00:08:12.039 align:start position:0%
questions or something didn't quite make
 

00:08:12.039 --> 00:08:14.469 align:start position:0%
questions or something didn't quite make
sense<00:08:12.479><c> leave</c><00:08:12.720><c> the</c><00:08:12.919><c> comments</c><00:08:13.280><c> down</c><00:08:13.479><c> below</c><00:08:14.319><c> um</c>

00:08:14.469 --> 00:08:14.479 align:start position:0%
sense leave the comments down below um
 

00:08:14.479 --> 00:08:16.469 align:start position:0%
sense leave the comments down below um
if<00:08:14.599><c> you</c><00:08:14.680><c> don't</c><00:08:14.800><c> mind</c><00:08:15.039><c> like</c><00:08:15.199><c> and</c><00:08:15.479><c> subscribing</c>

00:08:16.469 --> 00:08:16.479 align:start position:0%
if you don't mind like and subscribing
 

00:08:16.479 --> 00:08:17.909 align:start position:0%
if you don't mind like and subscribing
and<00:08:16.639><c> I</c><00:08:16.720><c> would</c><00:08:16.879><c> be</c><00:08:17.000><c> more</c><00:08:17.120><c> than</c><00:08:17.319><c> happy</c><00:08:17.520><c> to</c><00:08:17.720><c> talk</c>

00:08:17.909 --> 00:08:17.919 align:start position:0%
and I would be more than happy to talk
 

00:08:17.919 --> 00:08:20.189 align:start position:0%
and I would be more than happy to talk
with<00:08:18.080><c> you</c><00:08:18.319><c> about</c><00:08:18.680><c> Ai</c><00:08:19.280><c> and</c><00:08:19.560><c> respond</c><00:08:19.879><c> to</c><00:08:20.000><c> any</c><00:08:20.080><c> of</c>

00:08:20.189 --> 00:08:20.199 align:start position:0%
with you about Ai and respond to any of
 

00:08:20.199 --> 00:08:21.990 align:start position:0%
with you about Ai and respond to any of
your<00:08:20.319><c> comments</c><00:08:21.000><c> I'll</c><00:08:21.120><c> see</c><00:08:21.280><c> you</c><00:08:21.440><c> next</c><00:08:21.680><c> video</c>

00:08:21.990 --> 00:08:22.000 align:start position:0%
your comments I'll see you next video
 

00:08:22.000 --> 00:08:24.960 align:start position:0%
your comments I'll see you next video
and<00:08:22.159><c> have</c><00:08:22.280><c> a</c><00:08:22.440><c> good</c><00:08:22.599><c> day</c>

