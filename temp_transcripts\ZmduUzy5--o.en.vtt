WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:02.030 align:start position:0%
 
hey<00:00:00.280><c> and</c><00:00:00.399><c> welcome</c><00:00:00.680><c> to</c><00:00:01.000><c> another</c><00:00:01.480><c> video</c><00:00:01.839><c> on</c>

00:00:02.030 --> 00:00:02.040 align:start position:0%
hey and welcome to another video on
 

00:00:02.040 --> 00:00:03.510 align:start position:0%
hey and welcome to another video on
autogen<00:00:02.639><c> and</c><00:00:02.800><c> today</c><00:00:03.040><c> we're</c><00:00:03.199><c> going</c><00:00:03.280><c> to</c><00:00:03.399><c> be</c>

00:00:03.510 --> 00:00:03.520 align:start position:0%
autogen and today we're going to be
 

00:00:03.520 --> 00:00:06.190 align:start position:0%
autogen and today we're going to be
going<00:00:03.800><c> over</c><00:00:04.080><c> autogen</c><00:00:04.720><c> Studio</c><00:00:05.240><c> UI</c><00:00:05.879><c> this</c><00:00:06.000><c> is</c>

00:00:06.190 --> 00:00:06.200 align:start position:0%
going over autogen Studio UI this is
 

00:00:06.200 --> 00:00:08.350 align:start position:0%
going over autogen Studio UI this is
exciting<00:00:06.680><c> because</c><00:00:07.000><c> now</c><00:00:07.160><c> we</c><00:00:07.279><c> have</c><00:00:07.399><c> a</c><00:00:07.560><c> no</c><00:00:08.000><c> code</c>

00:00:08.350 --> 00:00:08.360 align:start position:0%
exciting because now we have a no code
 

00:00:08.360 --> 00:00:10.310 align:start position:0%
exciting because now we have a no code
way<00:00:08.679><c> of</c><00:00:08.880><c> creating</c><00:00:09.240><c> our</c><00:00:09.480><c> agents</c><00:00:09.920><c> and</c><00:00:10.120><c> having</c>

00:00:10.310 --> 00:00:10.320 align:start position:0%
way of creating our agents and having
 

00:00:10.320 --> 00:00:12.190 align:start position:0%
way of creating our agents and having
our<00:00:10.480><c> own</c><00:00:10.800><c> workflows</c><00:00:11.519><c> we're</c><00:00:11.679><c> going</c><00:00:11.759><c> to</c><00:00:11.880><c> cover</c>

00:00:12.190 --> 00:00:12.200 align:start position:0%
our own workflows we're going to cover
 

00:00:12.200 --> 00:00:14.030 align:start position:0%
our own workflows we're going to cover
everything<00:00:12.559><c> on</c><00:00:12.719><c> the</c><00:00:12.880><c> UI</c><00:00:13.280><c> so</c><00:00:13.559><c> let's</c><00:00:13.839><c> get</c>

00:00:14.030 --> 00:00:14.040 align:start position:0%
everything on the UI so let's get
 

00:00:14.040 --> 00:00:15.789 align:start position:0%
everything on the UI so let's get
started<00:00:14.799><c> okay</c><00:00:15.080><c> well</c><00:00:15.240><c> the</c><00:00:15.360><c> first</c><00:00:15.559><c> thing</c><00:00:15.719><c> we</c>

00:00:15.789 --> 00:00:15.799 align:start position:0%
started okay well the first thing we
 

00:00:15.799 --> 00:00:18.390 align:start position:0%
started okay well the first thing we
need<00:00:16.000><c> to</c><00:00:16.119><c> do</c><00:00:16.440><c> is</c><00:00:16.680><c> install</c><00:00:17.080><c> autogen</c><00:00:17.600><c> Studio</c><00:00:18.279><c> how</c>

00:00:18.390 --> 00:00:18.400 align:start position:0%
need to do is install autogen Studio how
 

00:00:18.400 --> 00:00:20.109 align:start position:0%
need to do is install autogen Studio how
we<00:00:18.560><c> do</c><00:00:18.760><c> that</c><00:00:18.960><c> is</c><00:00:19.119><c> you</c><00:00:19.240><c> open</c><00:00:19.480><c> up</c><00:00:19.600><c> your</c><00:00:19.720><c> terminal</c>

00:00:20.109 --> 00:00:20.119 align:start position:0%
we do that is you open up your terminal
 

00:00:20.119 --> 00:00:22.750 align:start position:0%
we do that is you open up your terminal
in<00:00:20.240><c> your</c><00:00:20.359><c> IDE</c><00:00:21.119><c> and</c><00:00:21.199><c> you'll</c><00:00:21.519><c> type</c><00:00:21.800><c> pip</c><00:00:22.240><c> install</c>

00:00:22.750 --> 00:00:22.760 align:start position:0%
in your IDE and you'll type pip install
 

00:00:22.760 --> 00:00:25.109 align:start position:0%
in your IDE and you'll type pip install
autogen<00:00:23.519><c> Studio</c><00:00:24.400><c> okay</c><00:00:24.519><c> so</c><00:00:24.720><c> once</c><00:00:24.920><c> that's</c>

00:00:25.109 --> 00:00:25.119 align:start position:0%
autogen Studio okay so once that's
 

00:00:25.119 --> 00:00:26.669 align:start position:0%
autogen Studio okay so once that's
finished<00:00:25.439><c> downloading</c><00:00:26.160><c> the</c><00:00:26.279><c> next</c><00:00:26.480><c> thing</c><00:00:26.599><c> we</c>

00:00:26.669 --> 00:00:26.679 align:start position:0%
finished downloading the next thing we
 

00:00:26.679 --> 00:00:28.269 align:start position:0%
finished downloading the next thing we
need<00:00:26.800><c> to</c><00:00:26.920><c> do</c><00:00:27.160><c> is</c><00:00:27.320><c> because</c><00:00:27.519><c> for</c><00:00:27.720><c> the</c><00:00:27.920><c> first</c>

00:00:28.269 --> 00:00:28.279 align:start position:0%
need to do is because for the first
 

00:00:28.279 --> 00:00:29.790 align:start position:0%
need to do is because for the first
example<00:00:28.920><c> I'm</c><00:00:29.039><c> going</c><00:00:29.119><c> to</c><00:00:29.240><c> show</c><00:00:29.400><c> you</c><00:00:29.519><c> how</c><00:00:29.599><c> to</c><00:00:29.679><c> use</c>

00:00:29.790 --> 00:00:29.800 align:start position:0%
example I'm going to show you how to use
 

00:00:29.800 --> 00:00:32.310 align:start position:0%
example I'm going to show you how to use
it<00:00:30.039><c> with</c><00:00:30.240><c> open</c><00:00:30.560><c> AI</c><00:00:31.320><c> so</c><00:00:31.519><c> we'll</c><00:00:31.640><c> need</c><00:00:31.800><c> to</c><00:00:31.960><c> export</c>

00:00:32.310 --> 00:00:32.320 align:start position:0%
it with open AI so we'll need to export
 

00:00:32.320 --> 00:00:36.190 align:start position:0%
it with open AI so we'll need to export
our<00:00:32.559><c> key</c><00:00:32.880><c> so</c><00:00:33.000><c> you'll</c><00:00:33.239><c> type</c><00:00:33.399><c> in</c><00:00:33.879><c> export</c><00:00:34.879><c> open</c><00:00:35.320><c> AI</c>

00:00:36.190 --> 00:00:36.200 align:start position:0%
our key so you'll type in export open AI
 

00:00:36.200 --> 00:00:39.110 align:start position:0%
our key so you'll type in export open AI
API<00:00:37.200><c> key</c><00:00:37.600><c> equals</c><00:00:38.120><c> and</c><00:00:38.200><c> then</c><00:00:38.280><c> you'll</c><00:00:38.520><c> type</c><00:00:38.760><c> in</c>

00:00:39.110 --> 00:00:39.120 align:start position:0%
API key equals and then you'll type in
 

00:00:39.120 --> 00:00:40.990 align:start position:0%
API key equals and then you'll type in
your<00:00:39.719><c> API</c><00:00:40.160><c> key</c><00:00:40.360><c> so</c><00:00:40.480><c> I'm</c><00:00:40.520><c> going</c><00:00:40.640><c> to</c><00:00:40.719><c> go</c><00:00:40.840><c> ahead</c>

00:00:40.990 --> 00:00:41.000 align:start position:0%
your API key so I'm going to go ahead
 

00:00:41.000 --> 00:00:42.510 align:start position:0%
your API key so I'm going to go ahead
and<00:00:41.079><c> do</c><00:00:41.239><c> that</c><00:00:41.440><c> here</c><00:00:41.879><c> then</c><00:00:42.000><c> you</c><00:00:42.120><c> just</c><00:00:42.280><c> click</c>

00:00:42.510 --> 00:00:42.520 align:start position:0%
and do that here then you just click
 

00:00:42.520 --> 00:00:44.190 align:start position:0%
and do that here then you just click
enter<00:00:42.920><c> and</c><00:00:43.039><c> you'll</c><00:00:43.239><c> be</c><00:00:43.360><c> done</c><00:00:43.800><c> and</c><00:00:43.920><c> now</c><00:00:44.039><c> we're</c>

00:00:44.190 --> 00:00:44.200 align:start position:0%
enter and you'll be done and now we're
 

00:00:44.200 --> 00:00:46.029 align:start position:0%
enter and you'll be done and now we're
ready<00:00:44.320><c> to</c><00:00:44.440><c> start</c><00:00:44.640><c> the</c><00:00:44.760><c> server</c><00:00:45.320><c> and</c><00:00:45.440><c> to</c><00:00:45.600><c> do</c><00:00:45.800><c> that</c>

00:00:46.029 --> 00:00:46.039 align:start position:0%
ready to start the server and to do that
 

00:00:46.039 --> 00:00:47.270 align:start position:0%
ready to start the server and to do that
you<00:00:46.199><c> just</c><00:00:46.360><c> type</c><00:00:46.559><c> in</c>

00:00:47.270 --> 00:00:47.280 align:start position:0%
you just type in
 

00:00:47.280 --> 00:00:52.510 align:start position:0%
you just type in
autogen<00:00:48.520><c> studio</c><00:00:49.920><c> UI</c><00:00:51.000><c> d-port</c><00:00:52.000><c> and</c><00:00:52.160><c> then</c><00:00:52.399><c> you</c>

00:00:52.510 --> 00:00:52.520 align:start position:0%
autogen studio UI d-port and then you
 

00:00:52.520 --> 00:00:55.229 align:start position:0%
autogen studio UI d-port and then you
put<00:00:52.680><c> the</c><00:00:52.800><c> port</c><00:00:53.039><c> number</c><00:00:53.480><c> 881</c><00:00:54.480><c> here</c><00:00:54.960><c> then</c><00:00:55.120><c> when</c>

00:00:55.229 --> 00:00:55.239 align:start position:0%
put the port number 881 here then when
 

00:00:55.239 --> 00:00:57.150 align:start position:0%
put the port number 881 here then when
you<00:00:55.399><c> press</c><00:00:55.640><c> enter</c><00:00:56.359><c> it's</c><00:00:56.520><c> going</c><00:00:56.640><c> to</c><00:00:56.800><c> go</c><00:00:56.920><c> ahead</c>

00:00:57.150 --> 00:00:57.160 align:start position:0%
you press enter it's going to go ahead
 

00:00:57.160 --> 00:00:59.310 align:start position:0%
you press enter it's going to go ahead
and<00:00:57.359><c> start</c><00:00:57.600><c> to</c><00:00:57.760><c> boot</c><00:00:58.039><c> up</c><00:00:58.199><c> a</c><00:00:58.359><c> local</c><00:00:58.719><c> host</c><00:00:59.000><c> server</c>

00:00:59.310 --> 00:00:59.320 align:start position:0%
and start to boot up a local host server
 

00:00:59.320 --> 00:01:01.709 align:start position:0%
and start to boot up a local host server
for<00:00:59.480><c> us</c><00:00:59.640><c> so</c><00:00:59.760><c> that</c><00:01:00.000><c> we</c><00:01:00.120><c> can</c><00:01:00.359><c> check</c><00:01:00.680><c> the</c><00:01:00.840><c> UI</c><00:01:01.480><c> once</c>

00:01:01.709 --> 00:01:01.719 align:start position:0%
for us so that we can check the UI once
 

00:01:01.719 --> 00:01:03.310 align:start position:0%
for us so that we can check the UI once
that's<00:01:01.920><c> done</c><00:01:02.239><c> you</c><00:01:02.359><c> just</c><00:01:02.559><c> click</c><00:01:02.840><c> this</c><00:01:03.000><c> Local</c>

00:01:03.310 --> 00:01:03.320 align:start position:0%
that's done you just click this Local
 

00:01:03.320 --> 00:01:05.229 align:start position:0%
that's done you just click this Local
Host<00:01:03.559><c> URL</c><00:01:03.960><c> you're</c><00:01:04.119><c> given</c><00:01:04.680><c> and</c><00:01:04.799><c> then</c><00:01:04.920><c> it</c><00:01:05.000><c> will</c>

00:01:05.229 --> 00:01:05.239 align:start position:0%
Host URL you're given and then it will
 

00:01:05.239 --> 00:01:08.030 align:start position:0%
Host URL you're given and then it will
open<00:01:05.560><c> up</c><00:01:05.799><c> the</c><00:01:05.960><c> UI</c><00:01:06.400><c> for</c><00:01:06.600><c> you</c><00:01:07.240><c> the</c><00:01:07.360><c> UI</c><00:01:07.640><c> may</c><00:01:07.799><c> look</c><00:01:07.960><c> a</c>

00:01:08.030 --> 00:01:08.040 align:start position:0%
open up the UI for you the UI may look a
 

00:01:08.040 --> 00:01:09.190 align:start position:0%
open up the UI for you the UI may look a
little<00:01:08.240><c> overwhelming</c><00:01:08.680><c> at</c><00:01:08.799><c> first</c><00:01:09.000><c> but</c><00:01:09.119><c> we're</c>

00:01:09.190 --> 00:01:09.200 align:start position:0%
little overwhelming at first but we're
 

00:01:09.200 --> 00:01:10.710 align:start position:0%
little overwhelming at first but we're
going<00:01:09.280><c> to</c><00:01:09.400><c> go</c><00:01:09.560><c> through</c><00:01:09.759><c> each</c><00:01:09.960><c> tab</c><00:01:10.320><c> in</c><00:01:10.479><c> each</c>

00:01:10.710 --> 00:01:10.720 align:start position:0%
going to go through each tab in each
 

00:01:10.720 --> 00:01:12.830 align:start position:0%
going to go through each tab in each
section<00:01:11.080><c> so</c><00:01:11.479><c> let's</c><00:01:11.680><c> get</c><00:01:11.840><c> started</c><00:01:12.520><c> okay</c><00:01:12.720><c> there</c>

00:01:12.830 --> 00:01:12.840 align:start position:0%
section so let's get started okay there
 

00:01:12.840 --> 00:01:15.109 align:start position:0%
section so let's get started okay there
are<00:01:13.119><c> three</c><00:01:13.400><c> tabs</c><00:01:13.759><c> over</c><00:01:14.000><c> here</c><00:01:14.280><c> we</c><00:01:14.400><c> have</c><00:01:14.560><c> skills</c>

00:01:15.109 --> 00:01:15.119 align:start position:0%
are three tabs over here we have skills
 

00:01:15.119 --> 00:01:17.910 align:start position:0%
are three tabs over here we have skills
agents<00:01:15.560><c> and</c><00:01:15.920><c> workflows</c><00:01:16.920><c> skills</c><00:01:17.439><c> are</c><00:01:17.680><c> kind</c><00:01:17.799><c> of</c>

00:01:17.910 --> 00:01:17.920 align:start position:0%
agents and workflows skills are kind of
 

00:01:17.920 --> 00:01:19.590 align:start position:0%
agents and workflows skills are kind of
like<00:01:18.040><c> the</c><00:01:18.240><c> functions</c><00:01:18.720><c> that</c><00:01:18.880><c> we</c><00:01:19.000><c> have</c><00:01:19.159><c> coded</c>

00:01:19.590 --> 00:01:19.600 align:start position:0%
like the functions that we have coded
 

00:01:19.600 --> 00:01:21.550 align:start position:0%
like the functions that we have coded
before<00:01:20.200><c> agents</c><00:01:20.799><c> uh</c><00:01:20.960><c> this</c><00:01:21.040><c> is</c><00:01:21.159><c> where</c><00:01:21.360><c> you</c>

00:01:21.550 --> 00:01:21.560 align:start position:0%
before agents uh this is where you
 

00:01:21.560 --> 00:01:23.069 align:start position:0%
before agents uh this is where you
actually<00:01:21.799><c> create</c><00:01:22.040><c> the</c><00:01:22.200><c> agents</c><00:01:22.640><c> that</c><00:01:22.759><c> you</c><00:01:22.920><c> want</c>

00:01:23.069 --> 00:01:23.079 align:start position:0%
actually create the agents that you want
 

00:01:23.079 --> 00:01:25.950 align:start position:0%
actually create the agents that you want
to<00:01:23.360><c> create</c><00:01:23.640><c> a</c><00:01:23.880><c> workflow</c><00:01:24.479><c> with</c><00:01:25.360><c> and</c><00:01:25.520><c> then</c><00:01:25.799><c> the</c>

00:01:25.950 --> 00:01:25.960 align:start position:0%
to create a workflow with and then the
 

00:01:25.960 --> 00:01:28.710 align:start position:0%
to create a workflow with and then the
workflow<00:01:26.799><c> is</c><00:01:27.240><c> essentially</c><00:01:27.680><c> saying</c><00:01:28.439><c> okay</c><00:01:28.600><c> we</c>

00:01:28.710 --> 00:01:28.720 align:start position:0%
workflow is essentially saying okay we
 

00:01:28.720 --> 00:01:30.469 align:start position:0%
workflow is essentially saying okay we
have<00:01:28.840><c> the</c><00:01:28.920><c> user</c><00:01:29.200><c> proxy</c><00:01:29.560><c> and</c><00:01:29.640><c> we</c><00:01:30.000><c> to</c><00:01:30.119><c> initiate</c>

00:01:30.469 --> 00:01:30.479 align:start position:0%
have the user proxy and we to initiate
 

00:01:30.479 --> 00:01:32.510 align:start position:0%
have the user proxy and we to initiate
the<00:01:30.640><c> chat</c><00:01:31.040><c> with</c><00:01:31.400><c> some</c><00:01:31.720><c> agent</c><00:01:32.119><c> and</c><00:01:32.240><c> this</c><00:01:32.320><c> is</c>

00:01:32.510 --> 00:01:32.520 align:start position:0%
the chat with some agent and this is
 

00:01:32.520 --> 00:01:34.190 align:start position:0%
the chat with some agent and this is
what<00:01:32.759><c> kicks</c><00:01:33.079><c> everything</c><00:01:33.479><c> off</c><00:01:33.759><c> so</c><00:01:33.880><c> if</c><00:01:33.960><c> we</c><00:01:34.079><c> look</c>

00:01:34.190 --> 00:01:34.200 align:start position:0%
what kicks everything off so if we look
 

00:01:34.200 --> 00:01:35.830 align:start position:0%
what kicks everything off so if we look
at<00:01:34.280><c> this</c><00:01:34.479><c> example</c><00:01:34.759><c> for</c><00:01:34.960><c> a</c><00:01:35.119><c> general</c><00:01:35.479><c> agent</c>

00:01:35.830 --> 00:01:35.840 align:start position:0%
at this example for a general agent
 

00:01:35.840 --> 00:01:37.469 align:start position:0%
at this example for a general agent
workflow<00:01:36.759><c> we</c><00:01:36.880><c> have</c><00:01:37.040><c> the</c><00:01:37.119><c> name</c><00:01:37.280><c> of</c><00:01:37.399><c> the</c>

00:01:37.469 --> 00:01:37.479 align:start position:0%
workflow we have the name of the
 

00:01:37.479 --> 00:01:39.670 align:start position:0%
workflow we have the name of the
workflow<00:01:38.159><c> the</c><00:01:38.320><c> description</c><00:01:39.200><c> the</c><00:01:39.280><c> summary</c>

00:01:39.670 --> 00:01:39.680 align:start position:0%
workflow the description the summary
 

00:01:39.680 --> 00:01:41.429 align:start position:0%
workflow the description the summary
method<00:01:40.000><c> just</c><00:01:40.119><c> means</c><00:01:40.479><c> how</c><00:01:40.600><c> do</c><00:01:40.720><c> we</c><00:01:40.799><c> want</c><00:01:40.920><c> to</c><00:01:41.159><c> get</c>

00:01:41.429 --> 00:01:41.439 align:start position:0%
method just means how do we want to get
 

00:01:41.439 --> 00:01:43.350 align:start position:0%
method just means how do we want to get
the<00:01:41.680><c> message</c><00:01:42.119><c> that</c><00:01:42.320><c> is</c><00:01:42.520><c> retrieved</c><00:01:43.040><c> from</c><00:01:43.200><c> an</c>

00:01:43.350 --> 00:01:43.360 align:start position:0%
the message that is retrieved from an
 

00:01:43.360 --> 00:01:44.709 align:start position:0%
the message that is retrieved from an
agent<00:01:43.759><c> and</c><00:01:43.840><c> then</c><00:01:43.960><c> you</c><00:01:44.040><c> can</c><00:01:44.159><c> see</c><00:01:44.360><c> we</c><00:01:44.479><c> have</c><00:01:44.640><c> the</c>

00:01:44.709 --> 00:01:44.719 align:start position:0%
agent and then you can see we have the
 

00:01:44.719 --> 00:01:46.910 align:start position:0%
agent and then you can see we have the
sender<00:01:45.200><c> and</c><00:01:45.360><c> the</c><00:01:45.759><c> receiver</c><00:01:46.159><c> agents</c><00:01:46.719><c> and</c><00:01:46.840><c> then</c>

00:01:46.910 --> 00:01:46.920 align:start position:0%
sender and the receiver agents and then
 

00:01:46.920 --> 00:01:48.429 align:start position:0%
sender and the receiver agents and then
we<00:01:47.040><c> have</c><00:01:47.159><c> the</c><00:01:47.320><c> section</c><00:01:47.560><c> for</c><00:01:47.759><c> the</c><00:01:47.840><c> sender</c><00:01:48.280><c> and</c>

00:01:48.429 --> 00:01:48.439 align:start position:0%
we have the section for the sender and
 

00:01:48.439 --> 00:01:50.429 align:start position:0%
we have the section for the sender and
the<00:01:48.759><c> receiver</c><00:01:49.119><c> so</c><00:01:49.280><c> if</c><00:01:49.360><c> we</c><00:01:49.560><c> open</c><00:01:49.799><c> up</c><00:01:49.960><c> the</c><00:01:50.079><c> user</c>

00:01:50.429 --> 00:01:50.439 align:start position:0%
the receiver so if we open up the user
 

00:01:50.439 --> 00:01:52.830 align:start position:0%
the receiver so if we open up the user
proxy<00:01:50.960><c> the</c><00:01:51.119><c> name</c><00:01:51.320><c> is</c><00:01:51.479><c> user</c><00:01:51.799><c> proxy</c><00:01:52.479><c> we</c><00:01:52.600><c> have</c><00:01:52.719><c> the</c>

00:01:52.830 --> 00:01:52.840 align:start position:0%
proxy the name is user proxy we have the
 

00:01:52.840 --> 00:01:54.749 align:start position:0%
proxy the name is user proxy we have the
agent<00:01:53.200><c> description</c><00:01:54.000><c> we</c><00:01:54.119><c> have</c><00:01:54.240><c> the</c><00:01:54.399><c> max</c>

00:01:54.749 --> 00:01:54.759 align:start position:0%
agent description we have the max
 

00:01:54.759 --> 00:01:56.429 align:start position:0%
agent description we have the max
consecutive<00:01:55.240><c> auto</c><00:01:55.520><c> replies</c><00:01:56.079><c> which</c><00:01:56.200><c> we</c><00:01:56.320><c> have</c>

00:01:56.429 --> 00:01:56.439 align:start position:0%
consecutive auto replies which we have
 

00:01:56.439 --> 00:01:58.270 align:start position:0%
consecutive auto replies which we have
seen<00:01:56.680><c> before</c><00:01:56.920><c> we've</c><00:01:57.119><c> seen</c><00:01:57.320><c> this</c><00:01:57.439><c> in</c><00:01:57.640><c> code</c><00:01:58.159><c> and</c>

00:01:58.270 --> 00:01:58.280 align:start position:0%
seen before we've seen this in code and
 

00:01:58.280 --> 00:02:00.350 align:start position:0%
seen before we've seen this in code and
a<00:01:58.439><c> human</c><00:01:58.719><c> input</c><00:01:59.159><c> can</c><00:01:59.320><c> always</c><00:01:59.479><c> be</c><00:01:59.880><c> never</c>

00:02:00.350 --> 00:02:00.360 align:start position:0%
a human input can always be never
 

00:02:00.360 --> 00:02:02.510 align:start position:0%
a human input can always be never
terminate<00:02:00.840><c> or</c><00:02:01.360><c> always</c><00:02:01.840><c> the</c><00:02:02.000><c> system</c><00:02:02.280><c> message</c>

00:02:02.510 --> 00:02:02.520 align:start position:0%
terminate or always the system message
 

00:02:02.520 --> 00:02:04.149 align:start position:0%
terminate or always the system message
here<00:02:02.719><c> we</c><00:02:02.799><c> have</c><00:02:02.880><c> seen</c><00:02:03.119><c> before</c><00:02:03.680><c> this</c><00:02:03.759><c> is</c><00:02:03.920><c> how</c><00:02:04.039><c> we</c>

00:02:04.149 --> 00:02:04.159 align:start position:0%
here we have seen before this is how we
 

00:02:04.159 --> 00:02:06.149 align:start position:0%
here we have seen before this is how we
are<00:02:04.360><c> describing</c><00:02:05.159><c> what</c><00:02:05.320><c> we</c><00:02:05.399><c> want</c><00:02:05.560><c> to</c><00:02:05.719><c> do</c><00:02:06.000><c> with</c>

00:02:06.149 --> 00:02:06.159 align:start position:0%
are describing what we want to do with
 

00:02:06.159 --> 00:02:08.229 align:start position:0%
are describing what we want to do with
the<00:02:06.320><c> agent</c><00:02:06.840><c> we</c><00:02:06.960><c> have</c><00:02:07.079><c> a</c><00:02:07.240><c> model</c><00:02:07.520><c> and</c><00:02:07.680><c> a</c><00:02:07.880><c> skill</c>

00:02:08.229 --> 00:02:08.239 align:start position:0%
the agent we have a model and a skill
 

00:02:08.239 --> 00:02:10.229 align:start position:0%
the agent we have a model and a skill
section<00:02:08.520><c> here</c><00:02:08.720><c> where</c><00:02:08.879><c> the</c><00:02:09.039><c> model</c><00:02:09.440><c> section</c><00:02:10.080><c> is</c>

00:02:10.229 --> 00:02:10.239 align:start position:0%
section here where the model section is
 

00:02:10.239 --> 00:02:13.110 align:start position:0%
section here where the model section is
you<00:02:10.360><c> could</c><00:02:10.800><c> enter</c><00:02:11.239><c> a</c><00:02:11.480><c> different</c><00:02:11.840><c> GPT</c><00:02:12.560><c> model</c><00:02:12.920><c> or</c>

00:02:13.110 --> 00:02:13.120 align:start position:0%
you could enter a different GPT model or
 

00:02:13.120 --> 00:02:15.190 align:start position:0%
you could enter a different GPT model or
local<00:02:13.400><c> model</c><00:02:13.680><c> that</c><00:02:13.800><c> you</c><00:02:13.920><c> wanted</c><00:02:14.200><c> to</c><00:02:14.400><c> use</c><00:02:15.000><c> so</c>

00:02:15.190 --> 00:02:15.200 align:start position:0%
local model that you wanted to use so
 

00:02:15.200 --> 00:02:17.869 align:start position:0%
local model that you wanted to use so
this<00:02:15.400><c> agent</c><00:02:15.879><c> could</c><00:02:16.040><c> use</c><00:02:16.280><c> GPT</c><00:02:16.840><c> 4</c><00:02:17.480><c> or</c><00:02:17.640><c> it</c><00:02:17.760><c> could</c>

00:02:17.869 --> 00:02:17.879 align:start position:0%
this agent could use GPT 4 or it could
 

00:02:17.879 --> 00:02:21.630 align:start position:0%
this agent could use GPT 4 or it could
use<00:02:18.080><c> GPT</c><00:02:18.680><c> 3.5</c><00:02:20.080><c> turbo</c><00:02:21.080><c> whatever</c><00:02:21.360><c> you</c><00:02:21.440><c> want</c><00:02:21.560><c> to</c>

00:02:21.630 --> 00:02:21.640 align:start position:0%
use GPT 3.5 turbo whatever you want to
 

00:02:21.640 --> 00:02:24.229 align:start position:0%
use GPT 3.5 turbo whatever you want to
use<00:02:21.879><c> here</c><00:02:22.160><c> and</c><00:02:22.239><c> then</c><00:02:22.400><c> the</c><00:02:22.680><c> skills</c><00:02:23.680><c> this</c><00:02:24.000><c> is</c>

00:02:24.229 --> 00:02:24.239 align:start position:0%
use here and then the skills this is
 

00:02:24.239 --> 00:02:26.390 align:start position:0%
use here and then the skills this is
where<00:02:24.760><c> you</c><00:02:24.959><c> create</c><00:02:25.239><c> the</c><00:02:25.480><c> functions</c><00:02:26.000><c> and</c><00:02:26.120><c> you</c>

00:02:26.390 --> 00:02:26.400 align:start position:0%
where you create the functions and you
 

00:02:26.400 --> 00:02:28.750 align:start position:0%
where you create the functions and you
attach<00:02:26.760><c> it</c><00:02:26.959><c> to</c><00:02:27.280><c> this</c><00:02:27.519><c> agent</c><00:02:28.040><c> so</c><00:02:28.200><c> for</c><00:02:28.360><c> instance</c>

00:02:28.750 --> 00:02:28.760 align:start position:0%
attach it to this agent so for instance
 

00:02:28.760 --> 00:02:30.229 align:start position:0%
attach it to this agent so for instance
here<00:02:28.920><c> is</c><00:02:29.040><c> a</c><00:02:29.239><c> python</c><00:02:29.519><c> function</c><00:02:29.959><c> that</c><00:02:30.080><c> was</c>

00:02:30.229 --> 00:02:30.239 align:start position:0%
here is a python function that was
 

00:02:30.239 --> 00:02:32.990 align:start position:0%
here is a python function that was
created<00:02:30.840><c> to</c><00:02:31.040><c> generate</c><00:02:31.440><c> and</c><00:02:31.680><c> save</c><00:02:32.040><c> images</c><00:02:32.840><c> so</c>

00:02:32.990 --> 00:02:33.000 align:start position:0%
created to generate and save images so
 

00:02:33.000 --> 00:02:35.550 align:start position:0%
created to generate and save images so
we<00:02:33.080><c> would</c><00:02:33.319><c> attach</c><00:02:33.760><c> this</c><00:02:34.120><c> to</c><00:02:34.400><c> another</c><00:02:34.800><c> agent</c>

00:02:35.550 --> 00:02:35.560 align:start position:0%
we would attach this to another agent
 

00:02:35.560 --> 00:02:38.670 align:start position:0%
we would attach this to another agent
that<00:02:35.720><c> would</c><00:02:36.480><c> execute</c><00:02:37.480><c> this</c><00:02:37.800><c> function</c><00:02:38.360><c> based</c>

00:02:38.670 --> 00:02:38.680 align:start position:0%
that would execute this function based
 

00:02:38.680 --> 00:02:40.509 align:start position:0%
that would execute this function based
off<00:02:38.920><c> of</c><00:02:39.120><c> a</c><00:02:39.280><c> query</c><00:02:39.680><c> that</c><00:02:39.800><c> it's</c><00:02:39.959><c> given</c><00:02:40.319><c> now</c><00:02:40.400><c> that</c>

00:02:40.509 --> 00:02:40.519 align:start position:0%
off of a query that it's given now that
 

00:02:40.519 --> 00:02:41.830 align:start position:0%
off of a query that it's given now that
we're<00:02:40.640><c> back</c><00:02:40.760><c> to</c><00:02:40.840><c> this</c><00:02:40.920><c> workflow</c><00:02:41.440><c> for</c><00:02:41.560><c> the</c>

00:02:41.830 --> 00:02:41.840 align:start position:0%
we're back to this workflow for the
 

00:02:41.840 --> 00:02:43.710 align:start position:0%
we're back to this workflow for the
receiver<00:02:42.080><c> it</c><00:02:42.200><c> has</c><00:02:42.360><c> a</c><00:02:42.599><c> primary</c><00:02:43.040><c> assistant</c>

00:02:43.710 --> 00:02:43.720 align:start position:0%
receiver it has a primary assistant
 

00:02:43.720 --> 00:02:45.149 align:start position:0%
receiver it has a primary assistant
where<00:02:43.879><c> the</c><00:02:44.000><c> description</c><00:02:44.400><c> is</c><00:02:44.560><c> the</c><00:02:44.760><c> default</c>

00:02:45.149 --> 00:02:45.159 align:start position:0%
where the description is the default
 

00:02:45.159 --> 00:02:46.550 align:start position:0%
where the description is the default
assistant<00:02:45.480><c> to</c><00:02:45.599><c> generate</c><00:02:45.959><c> plans</c><00:02:46.200><c> and</c><00:02:46.360><c> write</c>

00:02:46.550 --> 00:02:46.560 align:start position:0%
assistant to generate plans and write
 

00:02:46.560 --> 00:02:49.270 align:start position:0%
assistant to generate plans and write
code<00:02:46.800><c> to</c><00:02:46.920><c> solve</c><00:02:47.239><c> tasks</c><00:02:47.840><c> this</c><00:02:47.920><c> is</c><00:02:48.120><c> the</c><00:02:48.319><c> same</c><00:02:48.800><c> UI</c>

00:02:49.270 --> 00:02:49.280 align:start position:0%
code to solve tasks this is the same UI
 

00:02:49.280 --> 00:02:50.830 align:start position:0%
code to solve tasks this is the same UI
here<00:02:49.680><c> we</c><00:02:49.800><c> have</c><00:02:49.920><c> the</c><00:02:50.000><c> name</c><00:02:50.120><c> of</c><00:02:50.239><c> the</c><00:02:50.319><c> agent</c><00:02:50.680><c> the</c>

00:02:50.830 --> 00:02:50.840 align:start position:0%
here we have the name of the agent the
 

00:02:50.840 --> 00:02:54.070 align:start position:0%
here we have the name of the agent the
description<00:02:51.720><c> the</c><00:02:51.879><c> Max</c><00:02:52.440><c> uh</c><00:02:52.560><c> auto</c><00:02:52.959><c> replies</c><00:02:53.959><c> this</c>

00:02:54.070 --> 00:02:54.080 align:start position:0%
description the Max uh auto replies this
 

00:02:54.080 --> 00:02:55.750 align:start position:0%
description the Max uh auto replies this
is<00:02:54.239><c> the</c><00:02:54.440><c> system</c><00:02:54.760><c> message</c><00:02:55.120><c> that</c><00:02:55.239><c> we</c><00:02:55.319><c> would</c><00:02:55.560><c> give</c>

00:02:55.750 --> 00:02:55.760 align:start position:0%
is the system message that we would give
 

00:02:55.760 --> 00:02:57.550 align:start position:0%
is the system message that we would give
in<00:02:56.040><c> code</c><00:02:56.680><c> or</c><00:02:56.840><c> you</c><00:02:56.920><c> can</c><00:02:57.000><c> just</c><00:02:57.120><c> put</c><00:02:57.239><c> it</c><00:02:57.360><c> here</c><00:02:57.440><c> in</c>

00:02:57.550 --> 00:02:57.560 align:start position:0%
in code or you can just put it here in
 

00:02:57.560 --> 00:03:00.430 align:start position:0%
in code or you can just put it here in
this<00:02:57.720><c> text</c><00:02:58.000><c> area</c><00:02:58.760><c> and</c><00:02:58.920><c> then</c><00:02:59.080><c> you</c><00:02:59.159><c> can</c><00:02:59.360><c> choose</c>

00:03:00.430 --> 00:03:00.440 align:start position:0%
this text area and then you can choose
 

00:03:00.440 --> 00:03:02.509 align:start position:0%
this text area and then you can choose
uh<00:03:00.800><c> one</c><00:03:00.920><c> of</c><00:03:01.120><c> these</c><00:03:01.360><c> models</c><00:03:01.879><c> here</c><00:03:02.159><c> that</c><00:03:02.280><c> have</c>

00:03:02.509 --> 00:03:02.519 align:start position:0%
uh one of these models here that have
 

00:03:02.519 --> 00:03:04.630 align:start position:0%
uh one of these models here that have
already<00:03:02.800><c> been</c><00:03:03.040><c> predefined</c><00:03:03.680><c> for</c><00:03:03.920><c> us</c><00:03:04.480><c> this</c>

00:03:04.630 --> 00:03:04.640 align:start position:0%
already been predefined for us this
 

00:03:04.640 --> 00:03:06.390 align:start position:0%
already been predefined for us this
agent<00:03:04.840><c> was</c><00:03:05.000><c> given</c><00:03:05.200><c> a</c><00:03:05.319><c> couple</c><00:03:05.560><c> skills</c><00:03:06.200><c> uh</c><00:03:06.319><c> the</c>

00:03:06.390 --> 00:03:06.400 align:start position:0%
agent was given a couple skills uh the
 

00:03:06.400 --> 00:03:08.030 align:start position:0%
agent was given a couple skills uh the
one<00:03:06.560><c> Define</c><00:03:06.959><c> papers</c><00:03:07.319><c> and</c><00:03:07.440><c> another</c><00:03:07.799><c> to</c>

00:03:08.030 --> 00:03:08.040 align:start position:0%
one Define papers and another to
 

00:03:08.040 --> 00:03:09.910 align:start position:0%
one Define papers and another to
generate<00:03:08.400><c> the</c><00:03:08.519><c> images</c><00:03:09.000><c> you</c><00:03:09.080><c> can</c><00:03:09.280><c> choose</c><00:03:09.680><c> which</c>

00:03:09.910 --> 00:03:09.920 align:start position:0%
generate the images you can choose which
 

00:03:09.920 --> 00:03:12.149 align:start position:0%
generate the images you can choose which
functions<00:03:10.640><c> or</c><00:03:10.879><c> skills</c><00:03:11.640><c> that</c><00:03:11.760><c> you</c><00:03:11.879><c> want</c><00:03:12.040><c> the</c>

00:03:12.149 --> 00:03:12.159 align:start position:0%
functions or skills that you want the
 

00:03:12.159 --> 00:03:13.910 align:start position:0%
functions or skills that you want the
agents<00:03:12.440><c> to</c><00:03:12.640><c> have</c><00:03:13.239><c> okay</c><00:03:13.400><c> so</c><00:03:13.560><c> that</c><00:03:13.640><c> was</c><00:03:13.799><c> the</c>

00:03:13.910 --> 00:03:13.920 align:start position:0%
agents to have okay so that was the
 

00:03:13.920 --> 00:03:15.789 align:start position:0%
agents to have okay so that was the
workflow<00:03:14.720><c> but</c><00:03:14.840><c> let's</c><00:03:15.040><c> go</c><00:03:15.280><c> back</c><00:03:15.440><c> a</c><00:03:15.560><c> little</c>

00:03:15.789 --> 00:03:15.799 align:start position:0%
workflow but let's go back a little
 

00:03:15.799 --> 00:03:17.789 align:start position:0%
workflow but let's go back a little
further<00:03:16.159><c> to</c><00:03:16.440><c> how</c><00:03:16.599><c> we</c><00:03:16.799><c> actually</c><00:03:17.120><c> create</c><00:03:17.519><c> the</c>

00:03:17.789 --> 00:03:17.799 align:start position:0%
further to how we actually create the
 

00:03:17.799 --> 00:03:20.550 align:start position:0%
further to how we actually create the
agent<00:03:18.599><c> so</c><00:03:18.959><c> here</c><00:03:19.560><c> what</c><00:03:19.680><c> you</c><00:03:19.799><c> would</c><00:03:20.000><c> do</c><00:03:20.319><c> is</c><00:03:20.440><c> you</c>

00:03:20.550 --> 00:03:20.560 align:start position:0%
agent so here what you would do is you
 

00:03:20.560 --> 00:03:23.589 align:start position:0%
agent so here what you would do is you
can<00:03:20.840><c> click</c><00:03:21.159><c> create</c><00:03:21.640><c> new</c><00:03:22.120><c> agent</c><00:03:23.120><c> we</c><00:03:23.239><c> can</c><00:03:23.440><c> just</c>

00:03:23.589 --> 00:03:23.599 align:start position:0%
can click create new agent we can just
 

00:03:23.599 --> 00:03:25.750 align:start position:0%
can click create new agent we can just
name<00:03:23.840><c> it</c><00:03:23.959><c> sample</c><00:03:24.360><c> assistant</c><00:03:25.239><c> the</c><00:03:25.400><c> this</c><00:03:25.480><c> is</c><00:03:25.599><c> the</c>

00:03:25.750 --> 00:03:25.760 align:start position:0%
name it sample assistant the this is the
 

00:03:25.760 --> 00:03:27.710 align:start position:0%
name it sample assistant the this is the
description<00:03:26.159><c> the</c><00:03:26.280><c> sample</c><00:03:26.680><c> assistant</c><00:03:27.519><c> uh</c>

00:03:27.710 --> 00:03:27.720 align:start position:0%
description the sample assistant uh
 

00:03:27.720 --> 00:03:30.190 align:start position:0%
description the sample assistant uh
gives<00:03:28.080><c> help</c><00:03:28.599><c> and</c><00:03:28.920><c> nothing</c><00:03:29.360><c> else</c>

00:03:30.190 --> 00:03:30.200 align:start position:0%
gives help and nothing else
 

00:03:30.200 --> 00:03:32.630 align:start position:0%
gives help and nothing else
we<00:03:30.400><c> set</c><00:03:30.599><c> the</c><00:03:30.680><c> max</c><00:03:30.920><c> auto</c><00:03:31.200><c> reply</c><00:03:31.519><c> to</c><00:03:31.720><c> two</c><00:03:32.400><c> and</c><00:03:32.519><c> now</c>

00:03:32.630 --> 00:03:32.640 align:start position:0%
we set the max auto reply to two and now
 

00:03:32.640 --> 00:03:33.830 align:start position:0%
we set the max auto reply to two and now
for<00:03:32.760><c> the</c><00:03:32.840><c> system</c><00:03:33.120><c> message</c><00:03:33.439><c> we</c><00:03:33.519><c> just</c><00:03:33.640><c> want</c><00:03:33.720><c> to</c>

00:03:33.830 --> 00:03:33.840 align:start position:0%
for the system message we just want to
 

00:03:33.840 --> 00:03:35.550 align:start position:0%
for the system message we just want to
say<00:03:34.000><c> you</c><00:03:34.120><c> will</c><00:03:34.280><c> help</c><00:03:34.439><c> us</c><00:03:34.560><c> in</c><00:03:34.720><c> our</c><00:03:34.879><c> simple</c><00:03:35.120><c> math</c>

00:03:35.550 --> 00:03:35.560 align:start position:0%
say you will help us in our simple math
 

00:03:35.560 --> 00:03:37.990 align:start position:0%
say you will help us in our simple math
questions<00:03:36.560><c> now</c><00:03:36.760><c> I</c><00:03:36.879><c> can</c><00:03:37.159><c> say</c><00:03:37.400><c> now</c><00:03:37.519><c> I</c><00:03:37.599><c> can</c><00:03:37.840><c> have</c>

00:03:37.990 --> 00:03:38.000 align:start position:0%
questions now I can say now I can have
 

00:03:38.000 --> 00:03:40.270 align:start position:0%
questions now I can say now I can have
this<00:03:38.080><c> one</c><00:03:38.280><c> have</c><00:03:38.400><c> a</c><00:03:38.599><c> specific</c><00:03:39.000><c> model</c><00:03:39.599><c> the</c><00:03:39.760><c> GPT</c>

00:03:40.270 --> 00:03:40.280 align:start position:0%
this one have a specific model the GPT
 

00:03:40.280 --> 00:03:43.550 align:start position:0%
this one have a specific model the GPT
for<00:03:40.640><c> preview</c><00:03:41.599><c> and</c><00:03:41.840><c> no</c><00:03:42.080><c> skills</c><00:03:42.920><c> okay</c><00:03:43.159><c> awesome</c>

00:03:43.550 --> 00:03:43.560 align:start position:0%
for preview and no skills okay awesome
 

00:03:43.560 --> 00:03:45.350 align:start position:0%
for preview and no skills okay awesome
and<00:03:43.720><c> now</c><00:03:43.920><c> we</c><00:03:44.040><c> have</c><00:03:44.239><c> created</c><00:03:44.560><c> our</c><00:03:44.920><c> sample</c>

00:03:45.350 --> 00:03:45.360 align:start position:0%
and now we have created our sample
 

00:03:45.360 --> 00:03:47.589 align:start position:0%
and now we have created our sample
assistant<00:03:46.159><c> now</c><00:03:46.280><c> if</c><00:03:46.400><c> we</c><00:03:46.519><c> go</c><00:03:46.640><c> to</c><00:03:46.799><c> the</c><00:03:46.920><c> skills</c><00:03:47.319><c> tab</c>

00:03:47.589 --> 00:03:47.599 align:start position:0%
assistant now if we go to the skills tab
 

00:03:47.599 --> 00:03:49.350 align:start position:0%
assistant now if we go to the skills tab
we<00:03:47.720><c> can</c><00:03:47.840><c> go</c><00:03:48.000><c> back</c><00:03:48.120><c> a</c><00:03:48.239><c> little</c><00:03:48.400><c> bit</c><00:03:48.560><c> further</c>

00:03:49.350 --> 00:03:49.360 align:start position:0%
we can go back a little bit further
 

00:03:49.360 --> 00:03:51.470 align:start position:0%
we can go back a little bit further
these<00:03:49.560><c> are</c><00:03:49.799><c> where</c><00:03:50.040><c> all</c><00:03:50.319><c> the</c><00:03:50.560><c> functions</c><00:03:51.120><c> are</c>

00:03:51.470 --> 00:03:51.480 align:start position:0%
these are where all the functions are
 

00:03:51.480 --> 00:03:53.869 align:start position:0%
these are where all the functions are
that<00:03:51.640><c> we</c><00:03:51.760><c> can</c><00:03:52.079><c> assign</c><00:03:52.680><c> to</c><00:03:53.040><c> an</c><00:03:53.200><c> agent</c><00:03:53.720><c> for</c>

00:03:53.869 --> 00:03:53.879 align:start position:0%
that we can assign to an agent for
 

00:03:53.879 --> 00:03:55.069 align:start position:0%
that we can assign to an agent for
instance<00:03:54.239><c> we</c><00:03:54.360><c> can</c><00:03:54.480><c> have</c><00:03:54.560><c> a</c><00:03:54.720><c> function</c><00:03:54.920><c> to</c>

00:03:55.069 --> 00:03:55.079 align:start position:0%
instance we can have a function to
 

00:03:55.079 --> 00:03:57.110 align:start position:0%
instance we can have a function to
search<00:03:55.319><c> for</c><00:03:55.480><c> papers</c><00:03:56.000><c> so</c><00:03:56.360><c> if</c><00:03:56.439><c> we</c><00:03:56.560><c> open</c><00:03:56.840><c> this</c><00:03:56.920><c> one</c>

00:03:57.110 --> 00:03:57.120 align:start position:0%
search for papers so if we open this one
 

00:03:57.120 --> 00:03:58.830 align:start position:0%
search for papers so if we open this one
up<00:03:57.439><c> you</c><00:03:57.519><c> can</c><00:03:57.720><c> see</c><00:03:57.959><c> that</c><00:03:58.079><c> here</c><00:03:58.239><c> is</c><00:03:58.400><c> the</c><00:03:58.519><c> search</c>

00:03:58.830 --> 00:03:58.840 align:start position:0%
up you can see that here is the search
 

00:03:58.840 --> 00:04:01.110 align:start position:0%
up you can see that here is the search
function<00:03:59.720><c> and</c><00:03:59.799><c> then</c><00:03:59.920><c> if</c><00:04:00.040><c> we</c><00:04:00.200><c> scroll</c><00:04:00.640><c> down</c><00:04:01.000><c> it</c>

00:04:01.110 --> 00:04:01.120 align:start position:0%
function and then if we scroll down it
 

00:04:01.120 --> 00:04:03.390 align:start position:0%
function and then if we scroll down it
does<00:04:01.360><c> the</c><00:04:01.519><c> import</c><00:04:02.000><c> for</c><00:04:02.280><c> us</c><00:04:02.720><c> and</c><00:04:02.920><c> this</c><00:04:03.040><c> is</c><00:04:03.239><c> all</c>

00:04:03.390 --> 00:04:03.400 align:start position:0%
does the import for us and this is all
 

00:04:03.400 --> 00:04:05.589 align:start position:0%
does the import for us and this is all
of<00:04:03.560><c> the</c><00:04:03.760><c> Python</c><00:04:04.120><c> code</c><00:04:04.760><c> that</c><00:04:04.920><c> would</c><00:04:05.360><c> be</c>

00:04:05.589 --> 00:04:05.599 align:start position:0%
of the Python code that would be
 

00:04:05.599 --> 00:04:07.990 align:start position:0%
of the Python code that would be
executed<00:04:06.480><c> when</c><00:04:06.680><c> it's</c><00:04:06.879><c> attached</c><00:04:07.239><c> to</c><00:04:07.400><c> an</c><00:04:07.640><c> agent</c>

00:04:07.990 --> 00:04:08.000 align:start position:0%
executed when it's attached to an agent
 

00:04:08.000 --> 00:04:10.190 align:start position:0%
executed when it's attached to an agent
to<00:04:08.159><c> recap</c><00:04:08.599><c> this</c><00:04:08.760><c> part</c><00:04:09.280><c> we</c><00:04:09.400><c> have</c><00:04:09.599><c> the</c><00:04:09.760><c> skills</c>

00:04:10.190 --> 00:04:10.200 align:start position:0%
to recap this part we have the skills
 

00:04:10.200 --> 00:04:11.429 align:start position:0%
to recap this part we have the skills
which<00:04:10.319><c> are</c><00:04:10.480><c> the</c><00:04:10.640><c> functions</c><00:04:11.040><c> that</c><00:04:11.159><c> you</c><00:04:11.280><c> can</c>

00:04:11.429 --> 00:04:11.439 align:start position:0%
which are the functions that you can
 

00:04:11.439 --> 00:04:13.949 align:start position:0%
which are the functions that you can
create<00:04:11.680><c> for</c><00:04:11.879><c> agents</c><00:04:12.519><c> the</c><00:04:12.720><c> actual</c><00:04:13.200><c> agents</c>

00:04:13.949 --> 00:04:13.959 align:start position:0%
create for agents the actual agents
 

00:04:13.959 --> 00:04:15.429 align:start position:0%
create for agents the actual agents
where<00:04:14.120><c> you</c><00:04:14.280><c> click</c><00:04:14.560><c> the</c><00:04:14.680><c> new</c><00:04:14.920><c> agent</c><00:04:15.200><c> button</c>

00:04:15.429 --> 00:04:15.439 align:start position:0%
where you click the new agent button
 

00:04:15.439 --> 00:04:16.909 align:start position:0%
where you click the new agent button
over<00:04:15.680><c> here</c><00:04:15.959><c> and</c><00:04:16.040><c> then</c><00:04:16.160><c> you</c><00:04:16.320><c> just</c><00:04:16.519><c> create</c><00:04:16.759><c> the</c>

00:04:16.909 --> 00:04:16.919 align:start position:0%
over here and then you just create the
 

00:04:16.919 --> 00:04:18.710 align:start position:0%
over here and then you just create the
agent<00:04:17.199><c> that</c><00:04:17.320><c> you</c><00:04:17.479><c> want</c><00:04:18.199><c> and</c><00:04:18.359><c> then</c><00:04:18.519><c> the</c>

00:04:18.710 --> 00:04:18.720 align:start position:0%
agent that you want and then the
 

00:04:18.720 --> 00:04:21.710 align:start position:0%
agent that you want and then the
workflow<00:04:19.720><c> and</c><00:04:19.880><c> this</c><00:04:20.000><c> is</c><00:04:20.280><c> how</c><00:04:20.600><c> you</c><00:04:20.759><c> can</c><00:04:21.160><c> assign</c>

00:04:21.710 --> 00:04:21.720 align:start position:0%
workflow and this is how you can assign
 

00:04:21.720 --> 00:04:23.870 align:start position:0%
workflow and this is how you can assign
agents<00:04:22.360><c> and</c><00:04:22.560><c> initiate</c><00:04:23.000><c> a</c><00:04:23.160><c> chat</c><00:04:23.479><c> with</c><00:04:23.720><c> each</c>

00:04:23.870 --> 00:04:23.880 align:start position:0%
agents and initiate a chat with each
 

00:04:23.880 --> 00:04:25.870 align:start position:0%
agents and initiate a chat with each
other<00:04:24.400><c> now</c><00:04:24.520><c> if</c><00:04:24.600><c> we</c><00:04:24.720><c> wanted</c><00:04:24.919><c> a</c><00:04:25.080><c> new</c><00:04:25.280><c> workflow</c>

00:04:25.870 --> 00:04:25.880 align:start position:0%
other now if we wanted a new workflow
 

00:04:25.880 --> 00:04:27.830 align:start position:0%
other now if we wanted a new workflow
you<00:04:25.960><c> would</c><00:04:26.120><c> just</c><00:04:26.240><c> type</c><00:04:26.440><c> in</c><00:04:26.600><c> new</c><00:04:26.800><c> workflow</c><00:04:27.639><c> and</c>

00:04:27.830 --> 00:04:27.840 align:start position:0%
you would just type in new workflow and
 

00:04:27.840 --> 00:04:30.790 align:start position:0%
you would just type in new workflow and
we<00:04:28.000><c> would</c><00:04:28.199><c> say</c><00:04:29.160><c> math</c>

00:04:30.790 --> 00:04:30.800 align:start position:0%
we would say math
 

00:04:30.800 --> 00:04:33.710 align:start position:0%
we would say math
workflow<00:04:31.800><c> can</c><00:04:31.960><c> say</c><00:04:32.199><c> the</c><00:04:32.320><c> same</c><00:04:32.639><c> thing</c><00:04:33.000><c> here</c><00:04:33.600><c> and</c>

00:04:33.710 --> 00:04:33.720 align:start position:0%
workflow can say the same thing here and
 

00:04:33.720 --> 00:04:35.270 align:start position:0%
workflow can say the same thing here and
then<00:04:33.880><c> we</c><00:04:34.000><c> have</c><00:04:34.080><c> the</c><00:04:34.199><c> user</c><00:04:34.520><c> proxy</c><00:04:34.880><c> but</c><00:04:35.039><c> instead</c>

00:04:35.270 --> 00:04:35.280 align:start position:0%
then we have the user proxy but instead
 

00:04:35.280 --> 00:04:37.230 align:start position:0%
then we have the user proxy but instead
of<00:04:35.400><c> the</c><00:04:35.680><c> primary</c><00:04:36.160><c> assistant</c><00:04:36.840><c> and</c><00:04:36.960><c> you</c><00:04:37.080><c> may</c>

00:04:37.230 --> 00:04:37.240 align:start position:0%
of the primary assistant and you may
 

00:04:37.240 --> 00:04:39.510 align:start position:0%
of the primary assistant and you may
have<00:04:37.360><c> to</c><00:04:37.919><c> click</c><00:04:38.160><c> on</c><00:04:38.360><c> a</c><00:04:38.840><c> whole</c><00:04:39.120><c> separate</c>

00:04:39.510 --> 00:04:39.520 align:start position:0%
have to click on a whole separate
 

00:04:39.520 --> 00:04:41.150 align:start position:0%
have to click on a whole separate
assistant<00:04:40.000><c> here</c><00:04:40.400><c> so</c><00:04:40.560><c> you</c><00:04:40.680><c> might</c><00:04:40.880><c> need</c><00:04:41.000><c> to</c>

00:04:41.150 --> 00:04:41.160 align:start position:0%
assistant here so you might need to
 

00:04:41.160 --> 00:04:42.790 align:start position:0%
assistant here so you might need to
click<00:04:41.320><c> on</c><00:04:41.440><c> user</c><00:04:41.680><c> proxy</c><00:04:42.039><c> and</c><00:04:42.120><c> then</c><00:04:42.240><c> go</c><00:04:42.479><c> back</c><00:04:42.639><c> to</c>

00:04:42.790 --> 00:04:42.800 align:start position:0%
click on user proxy and then go back to
 

00:04:42.800 --> 00:04:45.070 align:start position:0%
click on user proxy and then go back to
sample<00:04:43.160><c> assistant</c><00:04:43.560><c> so</c><00:04:43.759><c> it</c><00:04:44.000><c> actually</c><00:04:44.639><c> fills</c><00:04:44.919><c> in</c>

00:04:45.070 --> 00:04:45.080 align:start position:0%
sample assistant so it actually fills in
 

00:04:45.080 --> 00:04:46.670 align:start position:0%
sample assistant so it actually fills in
the<00:04:45.199><c> correct</c><00:04:45.479><c> information</c><00:04:46.320><c> and</c><00:04:46.440><c> then</c><00:04:46.560><c> you</c>

00:04:46.670 --> 00:04:46.680 align:start position:0%
the correct information and then you
 

00:04:46.680 --> 00:04:48.350 align:start position:0%
the correct information and then you
click<00:04:47.080><c> okay</c><00:04:47.639><c> and</c><00:04:47.759><c> now</c><00:04:47.880><c> you</c><00:04:47.960><c> see</c><00:04:48.120><c> we</c><00:04:48.199><c> have</c><00:04:48.280><c> the</c>

00:04:48.350 --> 00:04:48.360 align:start position:0%
click okay and now you see we have the
 

00:04:48.360 --> 00:04:50.749 align:start position:0%
click okay and now you see we have the
user<00:04:48.639><c> proxy</c><00:04:49.039><c> and</c><00:04:49.199><c> the</c><00:04:49.280><c> sample</c><00:04:49.680><c> assistant</c><00:04:50.520><c> okay</c>

00:04:50.749 --> 00:04:50.759 align:start position:0%
user proxy and the sample assistant okay
 

00:04:50.759 --> 00:04:52.430 align:start position:0%
user proxy and the sample assistant okay
great<00:04:50.960><c> we</c><00:04:51.120><c> went</c><00:04:51.320><c> over</c><00:04:51.560><c> the</c><00:04:51.680><c> build</c><00:04:52.000><c> Tab</c><00:04:52.320><c> and</c>

00:04:52.430 --> 00:04:52.440 align:start position:0%
great we went over the build Tab and
 

00:04:52.440 --> 00:04:53.670 align:start position:0%
great we went over the build Tab and
this<00:04:52.520><c> is</c><00:04:52.680><c> where</c><00:04:52.840><c> you</c><00:04:53.000><c> set</c><00:04:53.240><c> up</c><00:04:53.360><c> all</c><00:04:53.520><c> the</c>

00:04:53.670 --> 00:04:53.680 align:start position:0%
this is where you set up all the
 

00:04:53.680 --> 00:04:55.909 align:start position:0%
this is where you set up all the
functions<00:04:54.080><c> the</c><00:04:54.240><c> agents</c><00:04:54.720><c> and</c><00:04:55.120><c> the</c><00:04:55.280><c> workflows</c>

00:04:55.909 --> 00:04:55.919 align:start position:0%
functions the agents and the workflows
 

00:04:55.919 --> 00:04:57.670 align:start position:0%
functions the agents and the workflows
that<00:04:56.039><c> we</c><00:04:56.160><c> want</c><00:04:56.320><c> to</c><00:04:56.600><c> execute</c><00:04:57.199><c> it's</c><00:04:57.360><c> kind</c><00:04:57.479><c> of</c>

00:04:57.670 --> 00:04:57.680 align:start position:0%
that we want to execute it's kind of
 

00:04:57.680 --> 00:04:59.909 align:start position:0%
that we want to execute it's kind of
like<00:04:57.880><c> building</c><00:04:58.240><c> up</c><00:04:58.400><c> all</c><00:04:58.560><c> the</c><00:04:58.800><c> work</c><00:04:59.320><c> and</c><00:04:59.639><c> now</c><00:04:59.759><c> we</c>

00:04:59.909 --> 00:04:59.919 align:start position:0%
like building up all the work and now we
 

00:04:59.919 --> 00:05:02.029 align:start position:0%
like building up all the work and now we
go<00:05:00.080><c> to</c><00:05:00.240><c> the</c><00:05:00.440><c> playground</c><00:05:01.440><c> which</c><00:05:01.560><c> is</c><00:05:01.680><c> where</c><00:05:01.840><c> we</c>

00:05:02.029 --> 00:05:02.039 align:start position:0%
go to the playground which is where we
 

00:05:02.039 --> 00:05:04.070 align:start position:0%
go to the playground which is where we
actually<00:05:02.440><c> execute</c><00:05:02.880><c> one</c><00:05:03.039><c> of</c><00:05:03.160><c> the</c><00:05:03.320><c> workflows</c><00:05:03.880><c> so</c>

00:05:04.070 --> 00:05:04.080 align:start position:0%
actually execute one of the workflows so
 

00:05:04.080 --> 00:05:06.390 align:start position:0%
actually execute one of the workflows so
we<00:05:04.199><c> can</c><00:05:04.400><c> start</c><00:05:04.680><c> the</c><00:05:04.840><c> chat</c><00:05:05.080><c> with</c><00:05:05.280><c> agents</c><00:05:06.160><c> on</c><00:05:06.280><c> the</c>

00:05:06.390 --> 00:05:06.400 align:start position:0%
we can start the chat with agents on the
 

00:05:06.400 --> 00:05:07.749 align:start position:0%
we can start the chat with agents on the
left<00:05:06.639><c> hand</c><00:05:06.800><c> side</c><00:05:07.000><c> here</c><00:05:07.160><c> you</c><00:05:07.280><c> see</c><00:05:07.520><c> all</c><00:05:07.639><c> the</c>

00:05:07.749 --> 00:05:07.759 align:start position:0%
left hand side here you see all the
 

00:05:07.759 --> 00:05:09.550 align:start position:0%
left hand side here you see all the
sessions<00:05:08.520><c> now</c><00:05:08.639><c> if</c><00:05:08.720><c> you</c><00:05:08.880><c> click</c><00:05:09.120><c> the</c><00:05:09.320><c> green</c>

00:05:09.550 --> 00:05:09.560 align:start position:0%
sessions now if you click the green
 

00:05:09.560 --> 00:05:11.790 align:start position:0%
sessions now if you click the green
button<00:05:09.800><c> for</c><00:05:10.039><c> new</c><00:05:10.880><c> here</c><00:05:11.039><c> you'll</c><00:05:11.240><c> have</c><00:05:11.320><c> a</c><00:05:11.479><c> drop</c>

00:05:11.790 --> 00:05:11.800 align:start position:0%
button for new here you'll have a drop
 

00:05:11.800 --> 00:05:13.469 align:start position:0%
button for new here you'll have a drop
down<00:05:11.919><c> of</c><00:05:12.160><c> all</c><00:05:12.280><c> of</c><00:05:12.400><c> the</c><00:05:12.520><c> workflows</c><00:05:13.080><c> that</c><00:05:13.240><c> we</c>

00:05:13.469 --> 00:05:13.479 align:start position:0%
down of all of the workflows that we
 

00:05:13.479 --> 00:05:15.350 align:start position:0%
down of all of the workflows that we
created<00:05:13.919><c> in</c><00:05:14.000><c> the</c><00:05:14.120><c> build</c><00:05:14.400><c> tab</c><00:05:14.720><c> so</c><00:05:14.919><c> we</c><00:05:15.080><c> created</c>

00:05:15.350 --> 00:05:15.360 align:start position:0%
created in the build tab so we created
 

00:05:15.360 --> 00:05:17.390 align:start position:0%
created in the build tab so we created
one<00:05:15.639><c> called</c><00:05:15.880><c> math</c><00:05:16.160><c> workflow</c><00:05:17.039><c> so</c><00:05:17.160><c> we're</c><00:05:17.280><c> going</c>

00:05:17.390 --> 00:05:17.400 align:start position:0%
one called math workflow so we're going
 

00:05:17.400 --> 00:05:20.110 align:start position:0%
one called math workflow so we're going
to<00:05:17.639><c> click</c><00:05:17.919><c> that</c><00:05:18.240><c> then</c><00:05:18.400><c> hit</c><00:05:18.680><c> create</c><00:05:19.680><c> now</c><00:05:19.800><c> in</c><00:05:19.960><c> the</c>

00:05:20.110 --> 00:05:20.120 align:start position:0%
to click that then hit create now in the
 

00:05:20.120 --> 00:05:22.350 align:start position:0%
to click that then hit create now in the
code<00:05:20.479><c> when</c><00:05:20.639><c> we</c><00:05:20.720><c> would</c><00:05:20.919><c> say</c><00:05:21.400><c> user</c><00:05:21.840><c> proxy</c>

00:05:22.350 --> 00:05:22.360 align:start position:0%
code when we would say user proxy
 

00:05:22.360 --> 00:05:24.070 align:start position:0%
code when we would say user proxy
initiate<00:05:22.800><c> chat</c><00:05:23.240><c> there</c><00:05:23.360><c> was</c><00:05:23.560><c> some</c><00:05:23.759><c> message</c>

00:05:24.070 --> 00:05:24.080 align:start position:0%
initiate chat there was some message
 

00:05:24.080 --> 00:05:25.390 align:start position:0%
initiate chat there was some message
that<00:05:24.160><c> you</c><00:05:24.280><c> have</c><00:05:24.400><c> to</c><00:05:24.560><c> put</c><00:05:24.720><c> in</c><00:05:24.880><c> there</c><00:05:25.080><c> for</c><00:05:25.280><c> the</c>

00:05:25.390 --> 00:05:25.400 align:start position:0%
that you have to put in there for the
 

00:05:25.400 --> 00:05:26.909 align:start position:0%
that you have to put in there for the
chat<00:05:25.639><c> to</c><00:05:25.880><c> start</c><00:05:26.440><c> and</c><00:05:26.520><c> this</c><00:05:26.639><c> is</c><00:05:26.720><c> what</c><00:05:26.800><c> we're</c>

00:05:26.909 --> 00:05:26.919 align:start position:0%
chat to start and this is what we're
 

00:05:26.919 --> 00:05:28.790 align:start position:0%
chat to start and this is what we're
going<00:05:27.039><c> to</c><00:05:27.160><c> do</c><00:05:27.440><c> here</c><00:05:27.800><c> we're</c><00:05:27.919><c> going</c><00:05:28.039><c> to</c><00:05:28.199><c> ask</c><00:05:28.600><c> what</c>

00:05:28.790 --> 00:05:28.800 align:start position:0%
going to do here we're going to ask what
 

00:05:28.800 --> 00:05:32.870 align:start position:0%
going to do here we're going to ask what
is<00:05:29.000><c> X</c><00:05:29.240><c> and</c><00:05:29.360><c> the</c><00:05:29.680><c> function</c><00:05:30.080><c> 3x</c><00:05:30.560><c> -</c><00:05:30.840><c> 4</c><00:05:31.120><c> =</c><00:05:31.560><c> 16</c><00:05:32.560><c> now</c><00:05:32.759><c> if</c>

00:05:32.870 --> 00:05:32.880 align:start position:0%
is X and the function 3x - 4 = 16 now if
 

00:05:32.880 --> 00:05:36.270 align:start position:0%
is X and the function 3x - 4 = 16 now if
we<00:05:33.080><c> go</c><00:05:33.560><c> back</c><00:05:33.880><c> to</c><00:05:34.120><c> our</c><00:05:34.560><c> IDE</c><00:05:35.560><c> you</c><00:05:35.639><c> can</c><00:05:35.800><c> see</c><00:05:36.039><c> here</c>

00:05:36.270 --> 00:05:36.280 align:start position:0%
we go back to our IDE you can see here
 

00:05:36.280 --> 00:05:38.710 align:start position:0%
we go back to our IDE you can see here
the<00:05:36.440><c> user</c><00:05:36.919><c> proxy</c><00:05:37.600><c> initiated</c><00:05:38.080><c> the</c><00:05:38.199><c> chat</c><00:05:38.560><c> with</c>

00:05:38.710 --> 00:05:38.720 align:start position:0%
the user proxy initiated the chat with
 

00:05:38.720 --> 00:05:41.070 align:start position:0%
the user proxy initiated the chat with
the<00:05:38.880><c> sample</c><00:05:39.360><c> assistant</c><00:05:40.120><c> this</c><00:05:40.240><c> is</c><00:05:40.400><c> what</c><00:05:40.560><c> we</c><00:05:40.680><c> see</c>

00:05:41.070 --> 00:05:41.080 align:start position:0%
the sample assistant this is what we see
 

00:05:41.080 --> 00:05:43.390 align:start position:0%
the sample assistant this is what we see
whenever<00:05:41.440><c> we</c><00:05:41.639><c> start</c><00:05:42.199><c> the</c><00:05:42.400><c> python</c><00:05:42.800><c> file</c><00:05:43.080><c> in</c>

00:05:43.390 --> 00:05:43.400 align:start position:0%
whenever we start the python file in
 

00:05:43.400 --> 00:05:46.189 align:start position:0%
whenever we start the python file in
code<00:05:44.360><c> okay</c><00:05:44.560><c> so</c><00:05:44.800><c> it</c><00:05:44.960><c> came</c><00:05:45.199><c> back</c><00:05:45.520><c> with</c><00:05:45.720><c> a</c><00:05:45.880><c> final</c>

00:05:46.189 --> 00:05:46.199 align:start position:0%
code okay so it came back with a final
 

00:05:46.199 --> 00:05:47.909 align:start position:0%
code okay so it came back with a final
message<00:05:46.520><c> for</c><00:05:46.759><c> us</c><00:05:47.120><c> and</c><00:05:47.240><c> it</c><00:05:47.319><c> says</c><00:05:47.520><c> the</c><00:05:47.600><c> value</c><00:05:47.840><c> of</c>

00:05:47.909 --> 00:05:47.919 align:start position:0%
message for us and it says the value of
 

00:05:47.919 --> 00:05:50.189 align:start position:0%
message for us and it says the value of
x<00:05:48.080><c> in</c><00:05:48.280><c> equation</c><00:05:48.800><c> 3x</c><00:05:49.160><c> -</c><00:05:49.400><c> 4</c><00:05:49.560><c> =</c><00:05:49.800><c> 16</c><00:05:50.080><c> is</c>

00:05:50.189 --> 00:05:50.199 align:start position:0%
x in equation 3x - 4 = 16 is
 

00:05:50.199 --> 00:05:52.790 align:start position:0%
x in equation 3x - 4 = 16 is
approximately<00:05:51.080><c> 6.67</c><00:05:52.080><c> now</c><00:05:52.280><c> what</c><00:05:52.360><c> you</c><00:05:52.440><c> can</c><00:05:52.600><c> see</c>

00:05:52.790 --> 00:05:52.800 align:start position:0%
approximately 6.67 now what you can see
 

00:05:52.800 --> 00:05:54.110 align:start position:0%
approximately 6.67 now what you can see
here<00:05:52.919><c> is</c><00:05:53.080><c> there's</c><00:05:53.240><c> this</c><00:05:53.400><c> little</c><00:05:53.680><c> tab</c><00:05:54.000><c> that</c>

00:05:54.110 --> 00:05:54.120 align:start position:0%
here is there's this little tab that
 

00:05:54.120 --> 00:05:55.790 align:start position:0%
here is there's this little tab that
says<00:05:54.440><c> agent</c><00:05:54.759><c> messages</c><00:05:55.120><c> and</c><00:05:55.280><c> there's</c><00:05:55.479><c> four</c><00:05:55.680><c> of</c>

00:05:55.790 --> 00:05:55.800 align:start position:0%
says agent messages and there's four of
 

00:05:55.800 --> 00:05:58.110 align:start position:0%
says agent messages and there's four of
them<00:05:56.240><c> so</c><00:05:56.360><c> if</c><00:05:56.440><c> you</c><00:05:56.560><c> go</c><00:05:56.680><c> ahead</c><00:05:56.840><c> and</c><00:05:57.039><c> click</c><00:05:57.479><c> this</c>

00:05:58.110 --> 00:05:58.120 align:start position:0%
them so if you go ahead and click this
 

00:05:58.120 --> 00:05:59.469 align:start position:0%
them so if you go ahead and click this
this<00:05:58.240><c> is</c><00:05:58.440><c> the</c><00:05:58.600><c> convers</c><00:05:59.039><c> the</c><00:05:59.120><c> whole</c>

00:05:59.469 --> 00:05:59.479 align:start position:0%
this is the convers the whole
 

00:05:59.479 --> 00:06:01.110 align:start position:0%
this is the convers the whole
conversation<00:05:59.960><c> between</c><00:06:00.280><c> the</c><00:06:00.400><c> user</c><00:06:00.720><c> proxy</c><00:06:01.039><c> and</c>

00:06:01.110 --> 00:06:01.120 align:start position:0%
conversation between the user proxy and
 

00:06:01.120 --> 00:06:03.070 align:start position:0%
conversation between the user proxy and
the<00:06:01.240><c> sample</c><00:06:01.600><c> assistant</c><00:06:02.280><c> so</c><00:06:02.400><c> the</c><00:06:02.479><c> user</c><00:06:02.759><c> proxy</c>

00:06:03.070 --> 00:06:03.080 align:start position:0%
the sample assistant so the user proxy
 

00:06:03.080 --> 00:06:05.390 align:start position:0%
the sample assistant so the user proxy
said<00:06:03.280><c> what</c><00:06:03.400><c> is</c><00:06:03.600><c> x</c><00:06:03.759><c> in</c><00:06:03.919><c> this</c><00:06:04.080><c> function</c><00:06:04.400><c> 3x</c><00:06:04.720><c> -</c><00:06:04.960><c> 4</c><00:06:05.120><c> =</c>

00:06:05.390 --> 00:06:05.400 align:start position:0%
said what is x in this function 3x - 4 =
 

00:06:05.400 --> 00:06:07.710 align:start position:0%
said what is x in this function 3x - 4 =
16<00:06:05.759><c> this</c><00:06:05.840><c> is</c><00:06:06.000><c> the</c><00:06:06.160><c> question</c><00:06:06.440><c> that</c><00:06:06.599><c> we</c><00:06:06.840><c> asked</c>

00:06:07.710 --> 00:06:07.720 align:start position:0%
16 this is the question that we asked
 

00:06:07.720 --> 00:06:09.390 align:start position:0%
16 this is the question that we asked
then<00:06:07.840><c> the</c><00:06:08.000><c> sample</c><00:06:08.360><c> assistant</c><00:06:08.800><c> came</c><00:06:09.039><c> back</c><00:06:09.240><c> with</c>

00:06:09.390 --> 00:06:09.400 align:start position:0%
then the sample assistant came back with
 

00:06:09.400 --> 00:06:11.110 align:start position:0%
then the sample assistant came back with
something<00:06:10.039><c> I</c><00:06:10.120><c> think</c><00:06:10.240><c> it</c><00:06:10.400><c> actually</c><00:06:10.720><c> came</c><00:06:10.960><c> up</c>

00:06:11.110 --> 00:06:11.120 align:start position:0%
something I think it actually came up
 

00:06:11.120 --> 00:06:13.270 align:start position:0%
something I think it actually came up
back<00:06:11.319><c> with</c><00:06:11.520><c> steps</c><00:06:11.919><c> and</c><00:06:12.080><c> then</c><00:06:12.560><c> some</c><00:06:12.840><c> code</c>

00:06:13.270 --> 00:06:13.280 align:start position:0%
back with steps and then some code
 

00:06:13.280 --> 00:06:16.670 align:start position:0%
back with steps and then some code
execute<00:06:14.280><c> and</c><00:06:14.440><c> then</c><00:06:14.720><c> the</c><00:06:14.880><c> user</c><00:06:15.240><c> proxy</c><00:06:16.240><c> executed</c>

00:06:16.670 --> 00:06:16.680 align:start position:0%
execute and then the user proxy executed
 

00:06:16.680 --> 00:06:19.029 align:start position:0%
execute and then the user proxy executed
the<00:06:16.840><c> code</c><00:06:17.560><c> and</c><00:06:17.759><c> then</c><00:06:18.039><c> had</c><00:06:18.199><c> an</c><00:06:18.400><c> answer</c><00:06:18.639><c> for</c><00:06:18.919><c> us</c>

00:06:19.029 --> 00:06:19.039 align:start position:0%
the code and then had an answer for us
 

00:06:19.039 --> 00:06:21.469 align:start position:0%
the code and then had an answer for us
so<00:06:19.160><c> when</c><00:06:19.280><c> the</c><00:06:19.360><c> sample</c><00:06:19.960><c> assistant</c><00:06:20.960><c> uh</c><00:06:21.199><c> the</c><00:06:21.319><c> code</c>

00:06:21.469 --> 00:06:21.479 align:start position:0%
so when the sample assistant uh the code
 

00:06:21.479 --> 00:06:23.070 align:start position:0%
so when the sample assistant uh the code
was<00:06:21.639><c> executed</c><00:06:22.080><c> the</c><00:06:22.160><c> sample</c><00:06:22.479><c> assistant</c><00:06:22.759><c> says</c>

00:06:23.070 --> 00:06:23.080 align:start position:0%
was executed the sample assistant says
 

00:06:23.080 --> 00:06:25.350 align:start position:0%
was executed the sample assistant says
okay<00:06:23.360><c> well</c><00:06:23.960><c> here</c><00:06:24.160><c> is</c><00:06:24.360><c> the</c><00:06:24.520><c> final</c><00:06:24.840><c> answer</c><00:06:25.120><c> for</c>

00:06:25.350 --> 00:06:25.360 align:start position:0%
okay well here is the final answer for
 

00:06:25.360 --> 00:06:28.029 align:start position:0%
okay well here is the final answer for
you<00:06:26.039><c> and</c><00:06:26.199><c> if</c><00:06:26.319><c> we</c><00:06:26.440><c> scroll</c><00:06:26.880><c> up</c><00:06:27.280><c> this</c><00:06:27.360><c> is</c><00:06:27.560><c> the</c><00:06:27.720><c> same</c>

00:06:28.029 --> 00:06:28.039 align:start position:0%
you and if we scroll up this is the same
 

00:06:28.039 --> 00:06:30.189 align:start position:0%
you and if we scroll up this is the same
message<00:06:28.680><c> the</c><00:06:28.840><c> same</c><00:06:29.080><c> last</c><00:06:29.560><c> message</c><00:06:29.919><c> that</c><00:06:30.080><c> we</c>

00:06:30.189 --> 00:06:30.199 align:start position:0%
message the same last message that we
 

00:06:30.199 --> 00:06:32.150 align:start position:0%
message the same last message that we
see<00:06:30.680><c> here</c><00:06:31.400><c> one</c><00:06:31.479><c> of</c><00:06:31.560><c> the</c><00:06:31.639><c> things</c><00:06:31.800><c> I</c><00:06:31.919><c> like</c><00:06:32.039><c> about</c>

00:06:32.150 --> 00:06:32.160 align:start position:0%
see here one of the things I like about
 

00:06:32.160 --> 00:06:34.270 align:start position:0%
see here one of the things I like about
autogen<00:06:32.680><c> is</c><00:06:32.800><c> the</c><00:06:33.000><c> cash</c><00:06:33.280><c> system</c><00:06:33.680><c> so</c><00:06:34.039><c> how</c><00:06:34.120><c> does</c>

00:06:34.270 --> 00:06:34.280 align:start position:0%
autogen is the cash system so how does
 

00:06:34.280 --> 00:06:36.270 align:start position:0%
autogen is the cash system so how does
that<00:06:34.400><c> translate</c><00:06:34.759><c> to</c><00:06:34.919><c> this</c><00:06:35.039><c> UI</c><00:06:35.720><c> well</c><00:06:35.960><c> we</c><00:06:36.120><c> have</c>

00:06:36.270 --> 00:06:36.280 align:start position:0%
that translate to this UI well we have
 

00:06:36.280 --> 00:06:38.189 align:start position:0%
that translate to this UI well we have
created<00:06:36.599><c> our</c><00:06:36.800><c> first</c><00:06:37.039><c> session</c><00:06:37.360><c> over</c><00:06:37.639><c> here</c><00:06:38.000><c> so</c>

00:06:38.189 --> 00:06:38.199 align:start position:0%
created our first session over here so
 

00:06:38.199 --> 00:06:40.029 align:start position:0%
created our first session over here so
on<00:06:38.319><c> the</c><00:06:38.440><c> left-</c><00:06:38.680><c> hand</c><00:06:38.880><c> side</c><00:06:39.280><c> under</c><00:06:39.599><c> the</c><00:06:39.720><c> session</c>

00:06:40.029 --> 00:06:40.039 align:start position:0%
on the left- hand side under the session
 

00:06:40.039 --> 00:06:41.870 align:start position:0%
on the left- hand side under the session
you<00:06:40.120><c> can</c><00:06:40.240><c> either</c><00:06:40.479><c> delete</c><00:06:40.720><c> it</c><00:06:40.919><c> or</c><00:06:41.240><c> publish</c><00:06:41.560><c> it</c>

00:06:41.870 --> 00:06:41.880 align:start position:0%
you can either delete it or publish it
 

00:06:41.880 --> 00:06:44.469 align:start position:0%
you can either delete it or publish it
well<00:06:42.039><c> if</c><00:06:42.160><c> we</c><00:06:42.360><c> click</c><00:06:42.960><c> publish</c><00:06:43.960><c> you</c><00:06:44.039><c> can</c><00:06:44.160><c> see</c>

00:06:44.469 --> 00:06:44.479 align:start position:0%
well if we click publish you can see
 

00:06:44.479 --> 00:06:47.070 align:start position:0%
well if we click publish you can see
session<00:06:44.880><c> successfully</c><00:06:45.440><c> published</c><00:06:46.319><c> now</c><00:06:46.759><c> the</c>

00:06:47.070 --> 00:06:47.080 align:start position:0%
session successfully published now the
 

00:06:47.080 --> 00:06:49.510 align:start position:0%
session successfully published now the
last<00:06:47.520><c> tab</c><00:06:47.960><c> is</c><00:06:48.120><c> the</c><00:06:48.360><c> gallery</c><00:06:48.800><c> tab</c><00:06:49.199><c> so</c><00:06:49.319><c> if</c><00:06:49.400><c> you</c>

00:06:49.510 --> 00:06:49.520 align:start position:0%
last tab is the gallery tab so if you
 

00:06:49.520 --> 00:06:52.230 align:start position:0%
last tab is the gallery tab so if you
click<00:06:49.759><c> the</c><00:06:49.960><c> gallery</c><00:06:50.360><c> tab</c><00:06:51.120><c> well</c><00:06:51.479><c> guess</c><00:06:51.720><c> what</c>

00:06:52.230 --> 00:06:52.240 align:start position:0%
click the gallery tab well guess what
 

00:06:52.240 --> 00:06:54.309 align:start position:0%
click the gallery tab well guess what
now<00:06:52.479><c> that</c><00:06:52.599><c> we</c><00:06:52.800><c> published</c><00:06:53.120><c> it</c><00:06:53.240><c> we</c><00:06:53.440><c> said</c><00:06:53.759><c> okay</c><00:06:54.160><c> we</c>

00:06:54.309 --> 00:06:54.319 align:start position:0%
now that we published it we said okay we
 

00:06:54.319 --> 00:06:56.189 align:start position:0%
now that we published it we said okay we
really<00:06:54.440><c> want</c><00:06:54.560><c> to</c><00:06:54.800><c> cach</c><00:06:55.199><c> this</c><00:06:55.520><c> now</c><00:06:55.759><c> so</c><00:06:55.919><c> that</c><00:06:56.080><c> we</c>

00:06:56.189 --> 00:06:56.199 align:start position:0%
really want to cach this now so that we
 

00:06:56.199 --> 00:06:59.110 align:start position:0%
really want to cach this now so that we
can<00:06:56.360><c> see</c><00:06:56.680><c> later</c><00:06:57.280><c> so</c><00:06:57.440><c> if</c><00:06:57.520><c> you</c><00:06:57.800><c> click</c><00:06:58.280><c> this</c><00:06:59.000><c> this</c>

00:06:59.110 --> 00:06:59.120 align:start position:0%
can see later so if you click this this
 

00:06:59.120 --> 00:07:01.189 align:start position:0%
can see later so if you click this this
is<00:06:59.440><c> going</c><00:06:59.520><c> to</c><00:06:59.639><c> give</c><00:06:59.759><c> you</c><00:07:00.440><c> the</c><00:07:00.759><c> question</c><00:07:01.080><c> that</c>

00:07:01.189 --> 00:07:01.199 align:start position:0%
is going to give you the question that
 

00:07:01.199 --> 00:07:03.790 align:start position:0%
is going to give you the question that
was<00:07:01.479><c> asked</c><00:07:02.240><c> the</c><00:07:02.479><c> last</c><00:07:02.879><c> message</c><00:07:03.319><c> and</c><00:07:03.479><c> then</c><00:07:03.680><c> we</c>

00:07:03.790 --> 00:07:03.800 align:start position:0%
was asked the last message and then we
 

00:07:03.800 --> 00:07:05.869 align:start position:0%
was asked the last message and then we
still<00:07:04.000><c> had</c><00:07:04.160><c> the</c><00:07:04.280><c> drop</c><00:07:04.680><c> down</c><00:07:05.120><c> of</c><00:07:05.280><c> the</c><00:07:05.400><c> full</c>

00:07:05.869 --> 00:07:05.879 align:start position:0%
still had the drop down of the full
 

00:07:05.879 --> 00:07:07.950 align:start position:0%
still had the drop down of the full
conversation<00:07:06.879><c> now</c><00:07:07.039><c> if</c><00:07:07.120><c> we</c><00:07:07.360><c> go</c><00:07:07.520><c> back</c><00:07:07.680><c> to</c><00:07:07.800><c> the</c>

00:07:07.950 --> 00:07:07.960 align:start position:0%
conversation now if we go back to the
 

00:07:07.960 --> 00:07:10.830 align:start position:0%
conversation now if we go back to the
playground<00:07:08.479><c> Tab</c><00:07:08.879><c> and</c><00:07:09.120><c> you</c><00:07:09.360><c> click</c><00:07:09.720><c> delete</c><00:07:10.680><c> of</c>

00:07:10.830 --> 00:07:10.840 align:start position:0%
playground Tab and you click delete of
 

00:07:10.840 --> 00:07:12.589 align:start position:0%
playground Tab and you click delete of
course<00:07:11.120><c> the</c><00:07:11.240><c> session</c><00:07:11.599><c> is</c><00:07:11.759><c> removed</c><00:07:12.240><c> from</c><00:07:12.400><c> the</c>

00:07:12.589 --> 00:07:12.599 align:start position:0%
course the session is removed from the
 

00:07:12.599 --> 00:07:15.070 align:start position:0%
course the session is removed from the
playground<00:07:13.599><c> but</c><00:07:13.800><c> if</c><00:07:13.879><c> you</c><00:07:14.000><c> go</c><00:07:14.199><c> back</c><00:07:14.319><c> to</c><00:07:14.560><c> Gallery</c>

00:07:15.070 --> 00:07:15.080 align:start position:0%
playground but if you go back to Gallery
 

00:07:15.080 --> 00:07:17.350 align:start position:0%
playground but if you go back to Gallery
you<00:07:15.240><c> can</c><00:07:15.479><c> still</c><00:07:15.800><c> see</c><00:07:16.080><c> it</c><00:07:16.400><c> here</c><00:07:16.879><c> and</c><00:07:17.000><c> this</c><00:07:17.080><c> is</c><00:07:17.199><c> a</c>

00:07:17.350 --> 00:07:17.360 align:start position:0%
you can still see it here and this is a
 

00:07:17.360 --> 00:07:19.390 align:start position:0%
you can still see it here and this is a
way<00:07:17.479><c> so</c><00:07:17.639><c> you</c><00:07:17.759><c> have</c><00:07:17.879><c> to</c><00:07:18.039><c> clog</c><00:07:18.360><c> up</c><00:07:18.759><c> the</c><00:07:18.919><c> UI</c><00:07:19.319><c> if</c>

00:07:19.390 --> 00:07:19.400 align:start position:0%
way so you have to clog up the UI if
 

00:07:19.400 --> 00:07:20.469 align:start position:0%
way so you have to clog up the UI if
you're<00:07:19.599><c> messing</c><00:07:19.879><c> around</c><00:07:20.080><c> with</c><00:07:20.240><c> different</c>

00:07:20.469 --> 00:07:20.479 align:start position:0%
you're messing around with different
 

00:07:20.479 --> 00:07:22.390 align:start position:0%
you're messing around with different
sessions<00:07:21.120><c> you</c><00:07:21.199><c> can</c><00:07:21.400><c> delete</c><00:07:21.759><c> that</c><00:07:22.000><c> as</c><00:07:22.120><c> long</c><00:07:22.280><c> as</c>

00:07:22.390 --> 00:07:22.400 align:start position:0%
sessions you can delete that as long as
 

00:07:22.400 --> 00:07:24.350 align:start position:0%
sessions you can delete that as long as
you<00:07:22.720><c> publish</c><00:07:23.039><c> it</c><00:07:23.199><c> so</c><00:07:23.440><c> it's</c><00:07:23.599><c> stored</c><00:07:24.039><c> in</c><00:07:24.199><c> the</c>

00:07:24.350 --> 00:07:24.360 align:start position:0%
you publish it so it's stored in the
 

00:07:24.360 --> 00:07:26.029 align:start position:0%
you publish it so it's stored in the
gallery<00:07:24.879><c> thank</c><00:07:25.000><c> you</c><00:07:25.120><c> for</c><00:07:25.319><c> watching</c><00:07:25.680><c> and</c><00:07:25.800><c> for</c>

00:07:26.029 --> 00:07:26.039 align:start position:0%
gallery thank you for watching and for
 

00:07:26.039 --> 00:07:28.189 align:start position:0%
gallery thank you for watching and for
more<00:07:26.280><c> videos</c><00:07:26.560><c> on</c><00:07:26.720><c> autogen</c><00:07:27.520><c> click</c><00:07:27.840><c> here</c><00:07:28.039><c> to</c>

00:07:28.189 --> 00:07:28.199 align:start position:0%
more videos on autogen click here to
 

00:07:28.199 --> 00:07:32.280 align:start position:0%
more videos on autogen click here to
learn<00:07:28.479><c> more</c><00:07:28.879><c> and</c><00:07:29.039><c> I'll</c><00:07:29.400><c> see</c><00:07:29.520><c> you</c><00:07:29.680><c> next</c><00:07:29.879><c> time</c>

