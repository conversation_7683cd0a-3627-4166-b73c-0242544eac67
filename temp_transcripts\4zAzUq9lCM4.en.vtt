WEBVTT
Kind: captions
Language: en

00:00:01.319 --> 00:00:04.510 align:start position:0%
 
Al<00:00:01.520><c> righty</c><00:00:02.000><c> guys</c><00:00:02.240><c> so</c><00:00:02.600><c> video</c><00:00:03.000><c> number</c><00:00:03.280><c> two</c><00:00:04.240><c> uh</c>

00:00:04.510 --> 00:00:04.520 align:start position:0%
Al righty guys so video number two uh
 

00:00:04.520 --> 00:00:06.670 align:start position:0%
Al righty guys so video number two uh
the<00:00:04.720><c> next</c><00:00:04.960><c> step</c><00:00:05.200><c> of</c><00:00:05.319><c> the</c><00:00:05.480><c> journey</c><00:00:06.279><c> once</c><00:00:06.480><c> we've</c>

00:00:06.670 --> 00:00:06.680 align:start position:0%
the next step of the journey once we've
 

00:00:06.680 --> 00:00:08.509 align:start position:0%
the next step of the journey once we've
got<00:00:06.879><c> zamp</c><00:00:07.319><c> already</c><00:00:07.600><c> installed</c><00:00:08.080><c> is</c><00:00:08.240><c> we</c><00:00:08.360><c> also</c>

00:00:08.509 --> 00:00:08.519 align:start position:0%
got zamp already installed is we also
 

00:00:08.519 --> 00:00:10.549 align:start position:0%
got zamp already installed is we also
need<00:00:08.639><c> to</c><00:00:08.800><c> set</c><00:00:08.960><c> up</c><00:00:09.120><c> a</c><00:00:09.320><c> database</c><00:00:09.960><c> all</c><00:00:10.120><c> right</c><00:00:10.360><c> a</c>

00:00:10.549 --> 00:00:10.559 align:start position:0%
need to set up a database all right a
 

00:00:10.559 --> 00:00:13.910 align:start position:0%
need to set up a database all right a
Heroku<00:00:11.120><c> database</c><00:00:11.799><c> in</c><00:00:12.000><c> the</c><00:00:12.280><c> cloud</c><00:00:13.280><c> so</c><00:00:13.679><c> what</c><00:00:13.799><c> I'm</c>

00:00:13.910 --> 00:00:13.920 align:start position:0%
Heroku database in the cloud so what I'm
 

00:00:13.920 --> 00:00:16.430 align:start position:0%
Heroku database in the cloud so what I'm
going<00:00:14.040><c> to</c><00:00:14.200><c> do</c><00:00:14.719><c> is</c><00:00:15.160><c> I'm</c><00:00:15.320><c> just</c><00:00:15.480><c> going</c><00:00:15.639><c> to</c><00:00:15.839><c> open</c><00:00:16.160><c> up</c>

00:00:16.430 --> 00:00:16.440 align:start position:0%
going to do is I'm just going to open up
 

00:00:16.440 --> 00:00:18.950 align:start position:0%
going to do is I'm just going to open up
Heroku<00:00:17.119><c> Heroku</c><00:00:17.680><c> allows</c><00:00:18.039><c> you</c><00:00:18.199><c> to</c>

00:00:18.950 --> 00:00:18.960 align:start position:0%
Heroku Heroku allows you to
 

00:00:18.960 --> 00:00:23.470 align:start position:0%
Heroku Heroku allows you to
create<00:00:19.960><c> um</c><00:00:20.720><c> to</c><00:00:21.000><c> create</c><00:00:21.519><c> these</c><00:00:22.119><c> uh</c><00:00:22.519><c> databases</c>

00:00:23.470 --> 00:00:23.480 align:start position:0%
create um to create these uh databases
 

00:00:23.480 --> 00:00:26.550 align:start position:0%
create um to create these uh databases
in<00:00:23.640><c> the</c><00:00:24.080><c> cloud</c><00:00:25.080><c> uh</c><00:00:25.240><c> you</c><00:00:25.320><c> do</c><00:00:25.519><c> need</c><00:00:25.680><c> to</c><00:00:25.880><c> put</c><00:00:26.039><c> in</c>

00:00:26.550 --> 00:00:26.560 align:start position:0%
in the cloud uh you do need to put in
 

00:00:26.560 --> 00:00:29.509 align:start position:0%
in the cloud uh you do need to put in
your<00:00:27.160><c> payment</c><00:00:28.160><c> um</c><00:00:28.400><c> just</c><00:00:28.560><c> a</c><00:00:28.800><c> credit</c><00:00:29.080><c> card</c><00:00:29.359><c> but</c>

00:00:29.509 --> 00:00:29.519 align:start position:0%
your payment um just a credit card but
 

00:00:29.519 --> 00:00:32.030 align:start position:0%
your payment um just a credit card but
Heroku<00:00:30.039><c> is</c><00:00:30.160><c> not</c><00:00:30.320><c> going</c><00:00:30.400><c> to</c><00:00:30.599><c> charge</c><00:00:31.000><c> you</c><00:00:31.920><c> uh</c>

00:00:32.030 --> 00:00:32.040 align:start position:0%
Heroku is not going to charge you uh
 

00:00:32.040 --> 00:00:34.790 align:start position:0%
Heroku is not going to charge you uh
unless<00:00:32.399><c> you</c><00:00:32.680><c> actually</c><00:00:33.440><c> buy</c><00:00:33.640><c> a</c><00:00:33.960><c> product</c><00:00:34.399><c> or</c><00:00:34.600><c> buy</c>

00:00:34.790 --> 00:00:34.800 align:start position:0%
unless you actually buy a product or buy
 

00:00:34.800 --> 00:00:38.190 align:start position:0%
unless you actually buy a product or buy
a<00:00:35.000><c> server</c><00:00:35.520><c> that</c><00:00:35.680><c> you</c><00:00:35.760><c> want</c><00:00:35.920><c> to</c><00:00:36.160><c> pay</c><00:00:36.760><c> for</c><00:00:37.760><c> and</c><00:00:38.120><c> a</c>

00:00:38.190 --> 00:00:38.200 align:start position:0%
a server that you want to pay for and a
 

00:00:38.200 --> 00:00:40.029 align:start position:0%
a server that you want to pay for and a
lot<00:00:38.320><c> of</c><00:00:38.440><c> the</c><00:00:38.559><c> products</c><00:00:38.879><c> are</c><00:00:39.040><c> free</c><00:00:39.440><c> for</c><00:00:39.640><c> example</c>

00:00:40.029 --> 00:00:40.039 align:start position:0%
lot of the products are free for example
 

00:00:40.039 --> 00:00:42.990 align:start position:0%
lot of the products are free for example
launching<00:00:40.440><c> a</c><00:00:40.600><c> mySQL</c><00:00:41.320><c> database</c><00:00:42.200><c> in</c><00:00:42.399><c> the</c><00:00:42.600><c> cloud</c>

00:00:42.990 --> 00:00:43.000 align:start position:0%
launching a mySQL database in the cloud
 

00:00:43.000 --> 00:00:45.790 align:start position:0%
launching a mySQL database in the cloud
on<00:00:43.200><c> Heroku</c><00:00:43.879><c> is</c><00:00:44.120><c> actually</c><00:00:44.640><c> free</c><00:00:45.079><c> so</c><00:00:45.559><c> the</c><00:00:45.640><c> way</c>

00:00:45.790 --> 00:00:45.800 align:start position:0%
on Heroku is actually free so the way
 

00:00:45.800 --> 00:00:47.709 align:start position:0%
on Heroku is actually free so the way
that<00:00:45.960><c> you</c><00:00:46.120><c> do</c><00:00:46.280><c> it</c><00:00:46.440><c> is</c><00:00:46.600><c> you</c><00:00:46.760><c> start</c><00:00:47.039><c> off</c><00:00:47.399><c> over</c>

00:00:47.709 --> 00:00:47.719 align:start position:0%
that you do it is you start off over
 

00:00:47.719 --> 00:00:50.750 align:start position:0%
that you do it is you start off over
here<00:00:48.120><c> in</c><00:00:48.760><c> personal</c><00:00:49.760><c> right</c><00:00:50.000><c> and</c><00:00:50.160><c> then</c><00:00:50.320><c> you</c><00:00:50.480><c> do</c>

00:00:50.750 --> 00:00:50.760 align:start position:0%
here in personal right and then you do
 

00:00:50.760 --> 00:00:53.470 align:start position:0%
here in personal right and then you do
new<00:00:51.719><c> right</c><00:00:51.840><c> you're</c><00:00:51.960><c> going</c><00:00:52.079><c> to</c><00:00:52.199><c> do</c><00:00:52.440><c> new</c><00:00:53.239><c> uh</c><00:00:53.399><c> we</c>

00:00:53.470 --> 00:00:53.480 align:start position:0%
new right you're going to do new uh we
 

00:00:53.480 --> 00:00:55.430 align:start position:0%
new right you're going to do new uh we
could<00:00:53.640><c> set</c><00:00:53.840><c> up</c><00:00:54.000><c> a</c><00:00:54.120><c> new</c><00:00:54.280><c> one</c><00:00:54.640><c> but</c><00:00:54.960><c> it</c><00:00:55.079><c> would</c><00:00:55.359><c> kind</c>

00:00:55.430 --> 00:00:55.440 align:start position:0%
could set up a new one but it would kind
 

00:00:55.440 --> 00:00:57.750 align:start position:0%
could set up a new one but it would kind
of<00:00:55.600><c> be</c><00:00:55.719><c> a</c><00:00:55.920><c> pain</c><00:00:56.879><c> uh</c><00:00:57.000><c> but</c><00:00:57.120><c> you</c><00:00:57.239><c> know</c><00:00:57.359><c> what</c><00:00:57.559><c> let's</c>

00:00:57.750 --> 00:00:57.760 align:start position:0%
of be a pain uh but you know what let's
 

00:00:57.760 --> 00:00:59.670 align:start position:0%
of be a pain uh but you know what let's
just<00:00:58.120><c> let's</c><00:00:58.440><c> let's</c><00:00:58.640><c> actually</c><00:00:59.000><c> do</c><00:00:59.199><c> this</c><00:00:59.320><c> entire</c>

00:00:59.670 --> 00:00:59.680 align:start position:0%
just let's let's actually do this entire
 

00:00:59.680 --> 00:01:01.470 align:start position:0%
just let's let's actually do this entire
process<00:01:00.079><c> says</c><00:01:00.359><c> together</c><00:01:00.600><c> all</c><00:01:00.719><c> right</c><00:01:00.960><c> new</c>

00:01:01.470 --> 00:01:01.480 align:start position:0%
process says together all right new
 

00:01:01.480 --> 00:01:03.709 align:start position:0%
process says together all right new
create<00:01:01.879><c> new</c><00:01:02.239><c> app</c><00:01:02.840><c> okay</c><00:01:03.039><c> what</c><00:01:03.120><c> should</c><00:01:03.320><c> we</c><00:01:03.440><c> call</c>

00:01:03.709 --> 00:01:03.719 align:start position:0%
create new app okay what should we call
 

00:01:03.719 --> 00:01:07.670 align:start position:0%
create new app okay what should we call
this<00:01:04.040><c> app</c><00:01:04.760><c> we're</c><00:01:04.920><c> going</c><00:01:05.040><c> to</c><00:01:05.199><c> call</c><00:01:05.519><c> this</c><00:01:05.960><c> app</c><00:01:06.960><c> um</c>

00:01:07.670 --> 00:01:07.680 align:start position:0%
this app we're going to call this app um
 

00:01:07.680 --> 00:01:12.630 align:start position:0%
this app we're going to call this app um
what<00:01:08.400><c> CSV</c><00:01:09.400><c> to</c><00:01:10.000><c> SQL</c><00:01:10.880><c> okay</c><00:01:11.119><c> CSV</c><00:01:11.640><c> to</c><00:01:11.840><c> SQL</c><00:01:12.479><c> is</c>

00:01:12.630 --> 00:01:12.640 align:start position:0%
what CSV to SQL okay CSV to SQL is
 

00:01:12.640 --> 00:01:17.109 align:start position:0%
what CSV to SQL okay CSV to SQL is
already<00:01:13.119><c> is</c><00:01:13.240><c> not</c><00:01:13.520><c> available</c><00:01:14.520><c> CSC</c><00:01:15.400><c> to</c><00:01:15.640><c> SQL</c><00:01:16.280><c> demo</c>

00:01:17.109 --> 00:01:17.119 align:start position:0%
already is not available CSC to SQL demo
 

00:01:17.119 --> 00:01:19.630 align:start position:0%
already is not available CSC to SQL demo
okay<00:01:17.560><c> and</c><00:01:17.720><c> we're</c><00:01:17.880><c> going</c><00:01:18.000><c> to</c><00:01:18.240><c> choose</c><00:01:18.479><c> a</c><00:01:18.680><c> region</c>

00:01:19.630 --> 00:01:19.640 align:start position:0%
okay and we're going to choose a region
 

00:01:19.640 --> 00:01:21.870 align:start position:0%
okay and we're going to choose a region
and<00:01:19.799><c> we're</c><00:01:19.920><c> going</c><00:01:20.040><c> to</c><00:01:20.320><c> say</c><00:01:20.799><c> create</c><00:01:21.159><c> an</c><00:01:21.439><c> app</c><00:01:21.759><c> all</c>

00:01:21.870 --> 00:01:21.880 align:start position:0%
and we're going to say create an app all
 

00:01:21.880 --> 00:01:24.590 align:start position:0%
and we're going to say create an app all
right<00:01:22.079><c> once</c><00:01:22.240><c> you</c><00:01:22.479><c> create</c><00:01:22.799><c> an</c><00:01:23.159><c> app</c><00:01:24.159><c> then</c><00:01:24.520><c> you</c>

00:01:24.590 --> 00:01:24.600 align:start position:0%
right once you create an app then you
 

00:01:24.600 --> 00:01:26.670 align:start position:0%
right once you create an app then you
can<00:01:24.840><c> actually</c><00:01:25.240><c> add</c><00:01:25.400><c> a</c><00:01:25.640><c> resource</c><00:01:26.159><c> to</c><00:01:26.360><c> the</c><00:01:26.520><c> app</c>

00:01:26.670 --> 00:01:26.680 align:start position:0%
can actually add a resource to the app
 

00:01:26.680 --> 00:01:28.870 align:start position:0%
can actually add a resource to the app
and<00:01:26.799><c> the</c><00:01:26.960><c> resource</c><00:01:27.360><c> we</c><00:01:27.479><c> want</c><00:01:27.640><c> to</c><00:01:27.920><c> add</c><00:01:28.360><c> we</c><00:01:28.520><c> do</c><00:01:28.759><c> we</c>

00:01:28.870 --> 00:01:28.880 align:start position:0%
and the resource we want to add we do we
 

00:01:28.880 --> 00:01:30.469 align:start position:0%
and the resource we want to add we do we
do<00:01:29.040><c> a</c><00:01:29.159><c> search</c><00:01:29.400><c> for</c><00:01:29.520><c> the</c><00:01:29.640><c> ad</c><00:01:30.000><c> on</c><00:01:30.200><c> we're</c><00:01:30.320><c> going</c><00:01:30.400><c> to</c>

00:01:30.469 --> 00:01:30.479 align:start position:0%
do a search for the ad on we're going to
 

00:01:30.479 --> 00:01:33.590 align:start position:0%
do a search for the ad on we're going to
search<00:01:30.840><c> my</c><00:01:31.159><c> SQL</c><00:01:32.159><c> all</c><00:01:32.320><c> right</c><00:01:32.960><c> uh</c><00:01:33.240><c> and</c><00:01:33.320><c> you</c><00:01:33.439><c> can</c>

00:01:33.590 --> 00:01:33.600 align:start position:0%
search my SQL all right uh and you can
 

00:01:33.600 --> 00:01:35.590 align:start position:0%
search my SQL all right uh and you can
choose<00:01:34.079><c> whatever</c><00:01:34.399><c> you</c><00:01:34.520><c> want</c><00:01:34.880><c> I</c><00:01:35.079><c> personally</c>

00:01:35.590 --> 00:01:35.600 align:start position:0%
choose whatever you want I personally
 

00:01:35.600 --> 00:01:38.190 align:start position:0%
choose whatever you want I personally
like<00:01:35.920><c> Jaw</c><00:01:36.360><c> DB</c><00:01:36.960><c> it</c><00:01:37.119><c> hasn't</c><00:01:37.360><c> failed</c><00:01:37.680><c> me</c><00:01:37.880><c> in</c><00:01:38.000><c> the</c>

00:01:38.190 --> 00:01:38.200 align:start position:0%
like Jaw DB it hasn't failed me in the
 

00:01:38.200 --> 00:01:41.830 align:start position:0%
like Jaw DB it hasn't failed me in the
past<00:01:39.159><c> jaw</c><00:01:39.640><c> DB</c><00:01:40.560><c> all</c><00:01:40.720><c> right</c><00:01:41.040><c> and</c><00:01:41.200><c> you</c><00:01:41.320><c> see</c><00:01:41.680><c> it's</c>

00:01:41.830 --> 00:01:41.840 align:start position:0%
past jaw DB all right and you see it's
 

00:01:41.840 --> 00:01:43.950 align:start position:0%
past jaw DB all right and you see it's
got<00:01:42.079><c> all</c><00:01:42.320><c> these</c><00:01:42.680><c> uh</c><00:01:42.840><c> different</c><00:01:43.159><c> types</c><00:01:43.399><c> of</c><00:01:43.520><c> SQL</c>

00:01:43.950 --> 00:01:43.960 align:start position:0%
got all these uh different types of SQL
 

00:01:43.960 --> 00:01:45.389 align:start position:0%
got all these uh different types of SQL
databases<00:01:44.600><c> I'm</c><00:01:44.719><c> just</c><00:01:44.840><c> going</c><00:01:44.920><c> to</c><00:01:45.040><c> go</c><00:01:45.159><c> with</c><00:01:45.280><c> a</c>

00:01:45.389 --> 00:01:45.399 align:start position:0%
databases I'm just going to go with a
 

00:01:45.399 --> 00:01:47.870 align:start position:0%
databases I'm just going to go with a
free<00:01:45.680><c> one</c><00:01:46.600><c> and</c><00:01:46.759><c> I'm</c><00:01:46.840><c> going</c><00:01:46.960><c> to</c><00:01:47.079><c> say</c><00:01:47.320><c> submit</c><00:01:47.719><c> the</c>

00:01:47.870 --> 00:01:47.880 align:start position:0%
free one and I'm going to say submit the
 

00:01:47.880 --> 00:01:50.950 align:start position:0%
free one and I'm going to say submit the
order<00:01:49.000><c> beautiful</c><00:01:50.000><c> now</c><00:01:50.159><c> that</c><00:01:50.320><c> I</c><00:01:50.439><c> submitted</c><00:01:50.880><c> the</c>

00:01:50.950 --> 00:01:50.960 align:start position:0%
order beautiful now that I submitted the
 

00:01:50.960 --> 00:01:53.310 align:start position:0%
order beautiful now that I submitted the
order<00:01:51.360><c> they</c><00:01:51.479><c> spin</c><00:01:51.799><c> up</c><00:01:51.920><c> a</c><00:01:52.040><c> free</c><00:01:52.360><c> SQL</c><00:01:52.840><c> database</c>

00:01:53.310 --> 00:01:53.320 align:start position:0%
order they spin up a free SQL database
 

00:01:53.320 --> 00:01:55.950 align:start position:0%
order they spin up a free SQL database
for<00:01:53.520><c> me</c><00:01:53.719><c> I</c><00:01:53.920><c> click</c><00:01:54.200><c> that</c><00:01:54.399><c> little</c><00:01:54.799><c> button</c><00:01:55.799><c> it's</c>

00:01:55.950 --> 00:01:55.960 align:start position:0%
for me I click that little button it's
 

00:01:55.960 --> 00:02:00.990 align:start position:0%
for me I click that little button it's
going<00:01:56.079><c> to</c><00:01:56.240><c> take</c><00:01:56.399><c> me</c><00:01:56.560><c> to</c><00:01:56.840><c> this</c><00:01:57.039><c> page</c><00:01:57.320><c> over</c><00:01:57.600><c> here</c>

00:02:00.990 --> 00:02:01.000 align:start position:0%
 
 

00:02:01.000 --> 00:02:02.389 align:start position:0%
 
and<00:02:01.159><c> that's</c><00:02:01.360><c> pretty</c><00:02:01.560><c> much</c><00:02:01.680><c> all</c><00:02:01.840><c> there</c><00:02:02.000><c> is</c><00:02:02.200><c> to</c>

00:02:02.389 --> 00:02:02.399 align:start position:0%
and that's pretty much all there is to
 

00:02:02.399 --> 00:02:05.389 align:start position:0%
and that's pretty much all there is to
it<00:02:02.759><c> I</c><00:02:02.920><c> now</c><00:02:03.360><c> have</c><00:02:04.039><c> uh</c><00:02:04.360><c> this</c><00:02:04.520><c> new</c><00:02:04.880><c> connection</c>

00:02:05.389 --> 00:02:05.399 align:start position:0%
it I now have uh this new connection
 

00:02:05.399 --> 00:02:07.510 align:start position:0%
it I now have uh this new connection
string<00:02:06.000><c> and</c><00:02:06.119><c> I</c><00:02:06.240><c> got</c><00:02:06.399><c> the</c><00:02:06.560><c> host</c><00:02:06.920><c> the</c><00:02:07.039><c> username</c>

00:02:07.510 --> 00:02:07.520 align:start position:0%
string and I got the host the username
 

00:02:07.520 --> 00:02:09.029 align:start position:0%
string and I got the host the username
and<00:02:07.680><c> the</c><00:02:07.880><c> password</c><00:02:08.319><c> and</c><00:02:08.440><c> the</c><00:02:08.560><c> port</c><00:02:08.840><c> and</c>

00:02:09.029 --> 00:02:09.039 align:start position:0%
and the password and the port and
 

00:02:09.039 --> 00:02:11.390 align:start position:0%
and the password and the port and
everything<00:02:09.599><c> all</c><00:02:09.759><c> right</c><00:02:09.959><c> cool</c><00:02:10.440><c> so</c><00:02:10.720><c> now</c><00:02:11.120><c> when</c><00:02:11.239><c> I</c>

00:02:11.390 --> 00:02:11.400 align:start position:0%
everything all right cool so now when I
 

00:02:11.400 --> 00:02:13.390 align:start position:0%
everything all right cool so now when I
go<00:02:11.640><c> back</c><00:02:11.840><c> into</c><00:02:12.239><c> zamp</c><00:02:12.800><c> I'm</c><00:02:12.959><c> actually</c><00:02:13.160><c> going</c><00:02:13.280><c> to</c>

00:02:13.390 --> 00:02:13.400 align:start position:0%
go back into zamp I'm actually going to
 

00:02:13.400 --> 00:02:16.229 align:start position:0%
go back into zamp I'm actually going to
do<00:02:13.560><c> a</c><00:02:13.720><c> split</c><00:02:14.040><c> screen</c><00:02:14.480><c> check</c><00:02:14.800><c> this</c><00:02:15.000><c> out</c><00:02:15.800><c> I</c><00:02:16.000><c> press</c>

00:02:16.229 --> 00:02:16.239 align:start position:0%
do a split screen check this out I press
 

00:02:16.239 --> 00:02:19.150 align:start position:0%
do a split screen check this out I press
the<00:02:16.360><c> windows</c><00:02:16.840><c> icon</c><00:02:17.280><c> and</c><00:02:17.480><c> to</c><00:02:17.680><c> the</c><00:02:17.920><c> left</c><00:02:18.599><c> Windows</c>

00:02:19.150 --> 00:02:19.160 align:start position:0%
the windows icon and to the left Windows
 

00:02:19.160 --> 00:02:23.110 align:start position:0%
the windows icon and to the left Windows
icon<00:02:19.720><c> right</c><00:02:20.280><c> all</c><00:02:20.440><c> right</c><00:02:21.280><c> so</c><00:02:21.640><c> we</c><00:02:21.840><c> got</c><00:02:22.040><c> our</c><00:02:22.640><c> PHP</c>

00:02:23.110 --> 00:02:23.120 align:start position:0%
icon right all right so we got our PHP
 

00:02:23.120 --> 00:02:26.470 align:start position:0%
icon right all right so we got our PHP
my<00:02:23.319><c> admin</c><00:02:23.599><c> on</c><00:02:23.680><c> the</c><00:02:23.879><c> left</c><00:02:24.280><c> we</c><00:02:24.400><c> got</c><00:02:24.720><c> the</c><00:02:25.319><c> the</c><00:02:25.519><c> live</c>

00:02:26.470 --> 00:02:26.480 align:start position:0%
my admin on the left we got the the live
 

00:02:26.480 --> 00:02:28.949 align:start position:0%
my admin on the left we got the the live
uh<00:02:26.599><c> the</c><00:02:26.680><c> mySQL</c><00:02:27.280><c> database</c><00:02:27.680><c> and</c><00:02:27.840><c> the</c><00:02:28.000><c> cloud</c><00:02:28.800><c> on</c>

00:02:28.949 --> 00:02:28.959 align:start position:0%
uh the mySQL database and the cloud on
 

00:02:28.959 --> 00:02:31.070 align:start position:0%
uh the mySQL database and the cloud on
the<00:02:29.200><c> right</c><00:02:29.560><c> and</c><00:02:29.959><c> we</c><00:02:30.080><c> got</c><00:02:30.200><c> all</c><00:02:30.319><c> our</c><00:02:30.519><c> credentials</c>

00:02:31.070 --> 00:02:31.080 align:start position:0%
the right and we got all our credentials
 

00:02:31.080 --> 00:02:33.110 align:start position:0%
the right and we got all our credentials
here<00:02:31.239><c> so</c><00:02:31.480><c> where</c><00:02:31.800><c> within</c><00:02:32.080><c> PHP</c><00:02:32.519><c> my</c><00:02:32.680><c> adwi</c><00:02:33.040><c> I'm</c>

00:02:33.110 --> 00:02:33.120 align:start position:0%
here so where within PHP my adwi I'm
 

00:02:33.120 --> 00:02:34.309 align:start position:0%
here so where within PHP my adwi I'm
going<00:02:33.239><c> to</c><00:02:33.360><c> say</c>

00:02:34.309 --> 00:02:34.319 align:start position:0%
going to say
 

00:02:34.319 --> 00:02:37.190 align:start position:0%
going to say
new<00:02:35.319><c> and</c><00:02:35.720><c> what</c><00:02:35.800><c> do</c><00:02:36.000><c> I</c><00:02:36.120><c> want</c><00:02:36.239><c> to</c><00:02:36.400><c> do</c><00:02:36.840><c> ah</c><00:02:37.040><c> I</c><00:02:37.120><c> want</c>

00:02:37.190 --> 00:02:37.200 align:start position:0%
new and what do I want to do ah I want
 

00:02:37.200 --> 00:02:40.990 align:start position:0%
new and what do I want to do ah I want
to<00:02:37.319><c> do</c><00:02:37.440><c> a</c><00:02:37.599><c> new</c><00:02:37.920><c> database</c><00:02:38.840><c> oh</c><00:02:39.080><c> okay</c><00:02:39.800><c> fine</c><00:02:40.800><c> uh</c>

00:02:40.990 --> 00:02:41.000 align:start position:0%
to do a new database oh okay fine uh
 

00:02:41.000 --> 00:02:44.030 align:start position:0%
to do a new database oh okay fine uh
first<00:02:41.319><c> I</c><00:02:41.400><c> need</c><00:02:41.560><c> to</c><00:02:41.800><c> actually</c><00:02:42.280><c> create</c><00:02:42.800><c> this</c>

00:02:44.030 --> 00:02:44.040 align:start position:0%
first I need to actually create this
 

00:02:44.040 --> 00:02:46.110 align:start position:0%
first I need to actually create this
database<00:02:45.040><c> all</c><00:02:45.159><c> right</c><00:02:45.280><c> let's</c><00:02:45.480><c> really</c><00:02:45.680><c> think</c>

00:02:46.110 --> 00:02:46.120 align:start position:0%
database all right let's really think
 

00:02:46.120 --> 00:02:49.270 align:start position:0%
database all right let's really think
think<00:02:46.400><c> through</c><00:02:46.680><c> how</c><00:02:46.800><c> to</c><00:02:47.000><c> do</c><00:02:47.440><c> this</c><00:02:48.440><c> ah</c><00:02:48.840><c> okay</c>

00:02:49.270 --> 00:02:49.280 align:start position:0%
think through how to do this ah okay
 

00:02:49.280 --> 00:02:51.990 align:start position:0%
think through how to do this ah okay
okay<00:02:50.200><c> um</c><00:02:50.959><c> okay</c><00:02:51.159><c> first</c><00:02:51.360><c> thing</c><00:02:51.480><c> we</c><00:02:51.599><c> need</c><00:02:51.720><c> to</c><00:02:51.840><c> do</c>

00:02:51.990 --> 00:02:52.000 align:start position:0%
okay um okay first thing we need to do
 

00:02:52.000 --> 00:02:54.070 align:start position:0%
okay um okay first thing we need to do
is<00:02:52.159><c> turn</c><00:02:52.360><c> our</c><00:02:53.040><c> we're</c><00:02:53.159><c> going</c><00:02:53.280><c> to</c><00:02:53.400><c> get</c><00:02:53.599><c> back</c><00:02:53.879><c> to</c>

00:02:54.070 --> 00:02:54.080 align:start position:0%
is turn our we're going to get back to
 

00:02:54.080 --> 00:02:56.589 align:start position:0%
is turn our we're going to get back to
this<00:02:54.360><c> to</c><00:02:54.560><c> pushing</c><00:02:54.879><c> the</c><00:02:55.040><c> data</c><00:02:55.360><c> into</c><00:02:55.599><c> the</c><00:02:55.840><c> cloud</c>

00:02:56.589 --> 00:02:56.599 align:start position:0%
this to pushing the data into the cloud
 

00:02:56.599 --> 00:02:58.869 align:start position:0%
this to pushing the data into the cloud
mySQL<00:02:57.239><c> database</c><00:02:57.680><c> in</c><00:02:57.760><c> a</c><00:02:57.920><c> moment</c><00:02:58.440><c> first</c><00:02:58.640><c> we</c><00:02:58.720><c> need</c>

00:02:58.869 --> 00:02:58.879 align:start position:0%
mySQL database in a moment first we need
 

00:02:58.879 --> 00:03:01.990 align:start position:0%
mySQL database in a moment first we need
to<00:02:59.000><c> take</c><00:02:59.200><c> that</c><00:02:59.319><c> CSV</c><00:03:00.080><c> data</c><00:03:00.560><c> and</c><00:03:00.920><c> turn</c><00:03:01.159><c> it</c><00:03:01.440><c> into</c><00:03:01.840><c> a</c>

00:03:01.990 --> 00:03:02.000 align:start position:0%
to take that CSV data and turn it into a
 

00:03:02.000 --> 00:03:05.350 align:start position:0%
to take that CSV data and turn it into a
SQL<00:03:02.799><c> database</c><00:03:03.799><c> all</c><00:03:03.959><c> right</c><00:03:04.840><c> uh</c><00:03:04.959><c> so</c><00:03:05.120><c> let's</c>

00:03:05.350 --> 00:03:05.360 align:start position:0%
SQL database all right uh so let's
 

00:03:05.360 --> 00:03:07.949 align:start position:0%
SQL database all right uh so let's
actually<00:03:05.720><c> do</c><00:03:05.959><c> that</c><00:03:06.200><c> within</c><00:03:06.680><c> PHP</c><00:03:07.120><c> my</c><00:03:07.360><c> admin</c><00:03:07.840><c> I</c>

00:03:07.949 --> 00:03:07.959 align:start position:0%
actually do that within PHP my admin I
 

00:03:07.959 --> 00:03:11.710 align:start position:0%
actually do that within PHP my admin I
press<00:03:08.280><c> create</c><00:03:08.760><c> create</c><00:03:09.000><c> a</c><00:03:09.400><c> database</c><00:03:10.560><c> SQL</c><00:03:11.560><c> uh</c>

00:03:11.710 --> 00:03:11.720 align:start position:0%
press create create a database SQL uh
 

00:03:11.720 --> 00:03:18.149 align:start position:0%
press create create a database SQL uh
sorry<00:03:12.480><c> CSV</c><00:03:13.480><c> right</c><00:03:13.640><c> CSV</c><00:03:14.360><c> to</c><00:03:15.319><c> SQL</c><00:03:16.319><c> demo</c><00:03:17.280><c> local</c>

00:03:18.149 --> 00:03:18.159 align:start position:0%
sorry CSV right CSV to SQL demo local
 

00:03:18.159 --> 00:03:19.949 align:start position:0%
sorry CSV right CSV to SQL demo local
okay<00:03:18.400><c> this</c><00:03:18.480><c> is</c><00:03:18.760><c> going</c><00:03:18.879><c> to</c><00:03:19.159><c> going</c><00:03:19.239><c> to</c><00:03:19.360><c> be</c><00:03:19.480><c> a</c><00:03:19.560><c> SQL</c>

00:03:19.949 --> 00:03:19.959 align:start position:0%
okay this is going to going to be a SQL
 

00:03:19.959 --> 00:03:22.309 align:start position:0%
okay this is going to going to be a SQL
database<00:03:20.400><c> on</c><00:03:20.519><c> my</c><00:03:20.760><c> local</c><00:03:21.159><c> machine</c><00:03:22.000><c> okay</c><00:03:22.200><c> I</c>

00:03:22.309 --> 00:03:22.319 align:start position:0%
database on my local machine okay I
 

00:03:22.319 --> 00:03:24.630 align:start position:0%
database on my local machine okay I
always<00:03:22.560><c> go</c><00:03:22.760><c> with</c>

00:03:24.630 --> 00:03:24.640 align:start position:0%
always go with
 

00:03:24.640 --> 00:03:30.309 align:start position:0%
always go with
utf8<00:03:25.640><c> right</c><00:03:25.799><c> UTF</c><00:03:26.360><c> 13</c><00:03:27.319><c> English</c><00:03:27.959><c> something</c><00:03:28.480><c> 32</c>

00:03:30.309 --> 00:03:30.319 align:start position:0%
utf8 right UTF 13 English something 32
 

00:03:30.319 --> 00:03:32.390 align:start position:0%
utf8 right UTF 13 English something 32
uh<00:03:30.599><c> let's</c>

00:03:32.390 --> 00:03:32.400 align:start position:0%
uh let's
 

00:03:32.400 --> 00:03:35.869 align:start position:0%
uh let's
see<00:03:33.400><c> let's</c><00:03:33.560><c> see</c><00:03:33.760><c> where's</c><00:03:34.120><c> English</c><00:03:34.560><c> UTF</c><00:03:35.159><c> 32</c>

00:03:35.869 --> 00:03:35.879 align:start position:0%
see let's see where's English UTF 32
 

00:03:35.879 --> 00:03:36.990 align:start position:0%
see let's see where's English UTF 32
General

00:03:36.990 --> 00:03:37.000 align:start position:0%
General
 

00:03:37.000 --> 00:03:39.550 align:start position:0%
General
CI<00:03:38.000><c> yeah</c><00:03:38.159><c> this</c><00:03:38.319><c> usually</c><00:03:38.640><c> does</c><00:03:38.879><c> the</c><00:03:39.080><c> job</c><00:03:39.319><c> and</c><00:03:39.480><c> if</c>

00:03:39.550 --> 00:03:39.560 align:start position:0%
CI yeah this usually does the job and if
 

00:03:39.560 --> 00:03:41.470 align:start position:0%
CI yeah this usually does the job and if
it<00:03:39.680><c> doesn't</c><00:03:40.040><c> it's</c><00:03:40.319><c> okay</c><00:03:40.799><c> I</c><00:03:40.920><c> will</c><00:03:41.120><c> figure</c><00:03:41.360><c> it</c>

00:03:41.470 --> 00:03:41.480 align:start position:0%
it doesn't it's okay I will figure it
 

00:03:41.480 --> 00:03:42.869 align:start position:0%
it doesn't it's okay I will figure it
out<00:03:41.879><c> okay</c>

00:03:42.869 --> 00:03:42.879 align:start position:0%
out okay
 

00:03:42.879 --> 00:03:47.589 align:start position:0%
out okay
General<00:03:43.879><c> uh</c><00:03:44.239><c> and</c><00:03:45.000><c> create</c><00:03:46.000><c> okay</c><00:03:46.360><c> cool</c><00:03:47.239><c> now</c><00:03:47.439><c> that</c>

00:03:47.589 --> 00:03:47.599 align:start position:0%
General uh and create okay cool now that
 

00:03:47.599 --> 00:03:50.990 align:start position:0%
General uh and create okay cool now that
I've<00:03:47.760><c> got</c><00:03:47.959><c> my</c><00:03:48.560><c> my</c><00:03:49.040><c> my</c><00:03:49.439><c> database</c><00:03:50.239><c> running</c>

00:03:50.990 --> 00:03:51.000 align:start position:0%
I've got my my my database running
 

00:03:51.000 --> 00:03:53.789 align:start position:0%
I've got my my my database running
locally<00:03:51.920><c> right</c><00:03:52.120><c> then</c><00:03:52.280><c> I'm</c><00:03:52.360><c> going</c><00:03:52.480><c> to</c><00:03:52.680><c> go</c><00:03:52.959><c> into</c>

00:03:53.789 --> 00:03:53.799 align:start position:0%
locally right then I'm going to go into
 

00:03:53.799 --> 00:03:56.990 align:start position:0%
locally right then I'm going to go into
the<00:03:54.040><c> import</c><00:03:54.840><c> feature</c><00:03:55.840><c> and</c><00:03:56.079><c> now</c><00:03:56.360><c> I</c><00:03:56.480><c> can</c><00:03:56.640><c> choose</c>

00:03:56.990 --> 00:03:57.000 align:start position:0%
the import feature and now I can choose
 

00:03:57.000 --> 00:04:00.149 align:start position:0%
the import feature and now I can choose
that<00:03:57.200><c> CSV</c><00:03:57.879><c> file</c><00:03:58.239><c> that</c><00:03:58.360><c> I</c><00:03:58.519><c> just</c><00:03:58.840><c> exported</c>

00:04:00.149 --> 00:04:00.159 align:start position:0%
that CSV file that I just exported
 

00:04:00.159 --> 00:04:03.149 align:start position:0%
that CSV file that I just exported
desktop<00:04:01.159><c> let's</c><00:04:01.319><c> see</c><00:04:01.519><c> it's</c><00:04:01.599><c> in</c><00:04:01.760><c> my</c><00:04:02.159><c> downloads</c>

00:04:03.149 --> 00:04:03.159 align:start position:0%
desktop let's see it's in my downloads
 

00:04:03.159 --> 00:04:05.910 align:start position:0%
desktop let's see it's in my downloads
okay<00:04:03.439><c> there's</c><00:04:03.720><c> my</c><00:04:04.159><c> campaign</c><00:04:04.680><c> data</c><00:04:05.239><c> I</c><00:04:05.360><c> choose</c>

00:04:05.910 --> 00:04:05.920 align:start position:0%
okay there's my campaign data I choose
 

00:04:05.920 --> 00:04:08.750 align:start position:0%
okay there's my campaign data I choose
that<00:04:06.920><c> okay</c><00:04:07.159><c> over</c><00:04:07.480><c> here</c><00:04:08.159><c> uh</c><00:04:08.280><c> some</c><00:04:08.439><c> of</c><00:04:08.599><c> the</c>

00:04:08.750 --> 00:04:08.760 align:start position:0%
that okay over here uh some of the
 

00:04:08.760 --> 00:04:11.270 align:start position:0%
that okay over here uh some of the
enable<00:04:09.159><c> foreign</c><00:04:09.480><c> key</c><00:04:09.720><c> checks</c><00:04:10.079><c> fine</c><00:04:11.000><c> oh</c>

00:04:11.270 --> 00:04:11.280 align:start position:0%
enable foreign key checks fine oh
 

00:04:11.280 --> 00:04:14.270 align:start position:0%
enable foreign key checks fine oh
character<00:04:11.640><c> set</c><00:04:11.840><c> of</c><00:04:12.000><c> the</c><00:04:12.120><c> file</c><00:04:12.400><c> F</c><00:04:12.879><c> utf8</c><00:04:13.879><c> format</c>

00:04:14.270 --> 00:04:14.280 align:start position:0%
character set of the file F utf8 format
 

00:04:14.280 --> 00:04:17.270 align:start position:0%
character set of the file F utf8 format
is<00:04:14.480><c> CSV</c><00:04:15.280><c> update</c><00:04:15.760><c> data</c><00:04:16.040><c> when</c><00:04:16.239><c> duplicate</c><00:04:16.919><c> Keys</c>

00:04:17.270 --> 00:04:17.280 align:start position:0%
is CSV update data when duplicate Keys
 

00:04:17.280 --> 00:04:19.789 align:start position:0%
is CSV update data when duplicate Keys
found<00:04:17.519><c> on</c><00:04:17.919><c> import</c><00:04:18.919><c> uh</c><00:04:19.040><c> don't</c><00:04:19.199><c> worry</c><00:04:19.440><c> about</c>

00:04:19.789 --> 00:04:19.799 align:start position:0%
found on import uh don't worry about
 

00:04:19.799 --> 00:04:21.710 align:start position:0%
found on import uh don't worry about
that<00:04:20.280><c> okay</c><00:04:20.479><c> this</c><00:04:20.600><c> is</c><00:04:20.799><c> important</c><00:04:21.199><c> the</c><00:04:21.400><c> first</c>

00:04:21.710 --> 00:04:21.720 align:start position:0%
that okay this is important the first
 

00:04:21.720 --> 00:04:23.950 align:start position:0%
that okay this is important the first
line<00:04:22.000><c> of</c><00:04:22.160><c> the</c><00:04:22.320><c> file</c><00:04:22.800><c> contains</c><00:04:23.199><c> the</c><00:04:23.400><c> table</c>

00:04:23.950 --> 00:04:23.960 align:start position:0%
line of the file contains the table
 

00:04:23.960 --> 00:04:26.629 align:start position:0%
line of the file contains the table
column<00:04:24.479><c> names</c><00:04:25.360><c> if</c><00:04:25.520><c> this</c><00:04:25.639><c> is</c><00:04:25.919><c> unchecked</c><00:04:26.520><c> the</c>

00:04:26.629 --> 00:04:26.639 align:start position:0%
column names if this is unchecked the
 

00:04:26.639 --> 00:04:28.749 align:start position:0%
column names if this is unchecked the
first<00:04:26.880><c> line</c><00:04:27.120><c> will</c><00:04:27.360><c> become</c><00:04:27.759><c> part</c><00:04:27.919><c> of</c><00:04:28.080><c> the</c><00:04:28.280><c> data</c>

00:04:28.749 --> 00:04:28.759 align:start position:0%
first line will become part of the data
 

00:04:28.759 --> 00:04:31.710 align:start position:0%
first line will become part of the data
so<00:04:30.000><c> uh</c><00:04:30.199><c> I</c><00:04:30.320><c> am</c><00:04:30.520><c> going</c><00:04:30.639><c> to</c><00:04:30.800><c> check</c><00:04:31.160><c> that</c><00:04:31.479><c> because</c>

00:04:31.710 --> 00:04:31.720 align:start position:0%
so uh I am going to check that because
 

00:04:31.720 --> 00:04:34.590 align:start position:0%
so uh I am going to check that because
if<00:04:31.840><c> you</c><00:04:32.000><c> remember</c><00:04:32.400><c> our</c><00:04:32.840><c> our</c><00:04:33.160><c> actual</c>

00:04:34.590 --> 00:04:34.600 align:start position:0%
if you remember our our actual
 

00:04:34.600 --> 00:04:37.350 align:start position:0%
if you remember our our actual
data<00:04:35.600><c> what</c><00:04:35.759><c> that</c><00:04:35.880><c> means</c><00:04:36.160><c> is</c><00:04:36.440><c> row</c><00:04:36.759><c> number</c><00:04:37.039><c> one</c>

00:04:37.350 --> 00:04:37.360 align:start position:0%
data what that means is row number one
 

00:04:37.360 --> 00:04:40.150 align:start position:0%
data what that means is row number one
here<00:04:37.680><c> is</c><00:04:38.039><c> actually</c><00:04:38.479><c> the</c><00:04:38.680><c> column</c><00:04:39.080><c> name</c><00:04:39.479><c> so</c>

00:04:40.150 --> 00:04:40.160 align:start position:0%
here is actually the column name so
 

00:04:40.160 --> 00:04:42.189 align:start position:0%
here is actually the column name so
these<00:04:40.600><c> these</c><00:04:40.880><c> titles</c><00:04:41.400><c> from</c><00:04:41.560><c> row</c><00:04:41.800><c> number</c><00:04:42.039><c> one</c>

00:04:42.189 --> 00:04:42.199 align:start position:0%
these these titles from row number one
 

00:04:42.199 --> 00:04:46.230 align:start position:0%
these these titles from row number one
are<00:04:42.360><c> going</c><00:04:42.440><c> to</c><00:04:42.600><c> turn</c><00:04:43.039><c> into</c><00:04:43.639><c> MySQL</c><00:04:44.479><c> tables</c><00:04:45.479><c> okay</c>

00:04:46.230 --> 00:04:46.240 align:start position:0%
are going to turn into MySQL tables okay
 

00:04:46.240 --> 00:04:49.270 align:start position:0%
are going to turn into MySQL tables okay
and<00:04:46.440><c> do</c><00:04:46.600><c> not</c><00:04:46.840><c> abort</c><00:04:47.320><c> on</c><00:04:47.759><c> insert</c><00:04:48.440><c> insert</c><00:04:48.919><c> error</c>

00:04:49.270 --> 00:04:49.280 align:start position:0%
and do not abort on insert insert error
 

00:04:49.280 --> 00:04:50.550 align:start position:0%
and do not abort on insert insert error
if<00:04:49.440><c> something</c><00:04:49.720><c> goes</c><00:04:50.000><c> wrong</c><00:04:50.280><c> while</c><00:04:50.440><c> you're</c>

00:04:50.550 --> 00:04:50.560 align:start position:0%
if something goes wrong while you're
 

00:04:50.560 --> 00:04:53.310 align:start position:0%
if something goes wrong while you're
trying<00:04:50.800><c> to</c><00:04:50.919><c> stuff</c><00:04:51.320><c> this</c><00:04:51.680><c> this</c><00:04:51.800><c> CSV</c><00:04:52.400><c> data</c><00:04:52.680><c> into</c>

00:04:53.310 --> 00:04:53.320 align:start position:0%
trying to stuff this this CSV data into
 

00:04:53.320 --> 00:04:56.230 align:start position:0%
trying to stuff this this CSV data into
local<00:04:53.680><c> SQL</c><00:04:54.120><c> database</c><00:04:54.919><c> just</c><00:04:55.120><c> keep</c><00:04:55.280><c> on</c><00:04:55.520><c> moving</c>

00:04:56.230 --> 00:04:56.240 align:start position:0%
local SQL database just keep on moving
 

00:04:56.240 --> 00:04:58.110 align:start position:0%
local SQL database just keep on moving
all<00:04:56.360><c> right</c><00:04:56.560><c> don't</c><00:04:56.840><c> stop</c><00:04:57.199><c> never</c><00:04:57.520><c> stop</c><00:04:57.919><c> never</c>

00:04:58.110 --> 00:04:58.120 align:start position:0%
all right don't stop never stop never
 

00:04:58.120 --> 00:05:01.950 align:start position:0%
all right don't stop never stop never
stop<00:04:58.400><c> dancing</c>

00:05:01.950 --> 00:05:01.960 align:start position:0%
 
 

00:05:01.960 --> 00:05:03.670 align:start position:0%
 
and<00:05:02.400><c> sometimes</c><00:05:02.560><c> it</c><00:05:02.720><c> does</c><00:05:02.919><c> take</c><00:05:03.120><c> a</c><00:05:03.240><c> little</c><00:05:03.479><c> bit</c>

00:05:03.670 --> 00:05:03.680 align:start position:0%
and sometimes it does take a little bit
 

00:05:03.680 --> 00:05:05.870 align:start position:0%
and sometimes it does take a little bit
a<00:05:03.759><c> little</c><00:05:03.960><c> bit</c><00:05:04.080><c> of</c><00:05:04.320><c> time</c><00:05:04.759><c> if</c><00:05:04.880><c> you've</c><00:05:05.080><c> got</c><00:05:05.280><c> a</c><00:05:05.680><c> a</c>

00:05:05.870 --> 00:05:05.880 align:start position:0%
a little bit of time if you've got a a
 

00:05:05.880 --> 00:05:09.590 align:start position:0%
a little bit of time if you've got a a
really<00:05:06.560><c> uh</c><00:05:06.720><c> a</c><00:05:06.840><c> really</c><00:05:07.120><c> big</c><00:05:07.360><c> CSV</c><00:05:08.039><c> file</c><00:05:09.039><c> it</c><00:05:09.240><c> could</c>

00:05:09.590 --> 00:05:09.600 align:start position:0%
really uh a really big CSV file it could
 

00:05:09.600 --> 00:05:11.710 align:start position:0%
really uh a really big CSV file it could
crash<00:05:10.360><c> but</c><00:05:10.600><c> and</c><00:05:10.720><c> there's</c><00:05:10.919><c> some</c><00:05:11.080><c> workarounds</c>

00:05:11.710 --> 00:05:11.720 align:start position:0%
crash but and there's some workarounds
 

00:05:11.720 --> 00:05:15.150 align:start position:0%
crash but and there's some workarounds
around<00:05:12.039><c> that</c><00:05:12.240><c> which</c><00:05:12.360><c> you'll</c><00:05:12.600><c> have</c><00:05:12.759><c> to</c><00:05:13.360><c> to</c><00:05:14.160><c> um</c>

00:05:15.150 --> 00:05:15.160 align:start position:0%
around that which you'll have to to um
 

00:05:15.160 --> 00:05:18.150 align:start position:0%
around that which you'll have to to um
to<00:05:15.639><c> uh</c><00:05:16.039><c> just</c><00:05:16.680><c> just</c><00:05:17.000><c> research</c><00:05:17.680><c> you'll</c><00:05:17.880><c> have</c><00:05:18.000><c> to</c>

00:05:18.150 --> 00:05:18.160 align:start position:0%
to uh just just research you'll have to
 

00:05:18.160 --> 00:05:20.350 align:start position:0%
to uh just just research you'll have to
make<00:05:18.319><c> some</c><00:05:18.560><c> changes</c><00:05:19.039><c> to</c><00:05:19.319><c> zap</c><00:05:19.639><c> to</c><00:05:19.800><c> your</c><00:05:19.960><c> zap</c>

00:05:20.350 --> 00:05:20.360 align:start position:0%
make some changes to zap to your zap
 

00:05:20.360 --> 00:05:22.550 align:start position:0%
make some changes to zap to your zap
configuration<00:05:21.120><c> if</c><00:05:21.240><c> the</c><00:05:21.360><c> CSV</c><00:05:21.880><c> file</c><00:05:22.160><c> is</c><00:05:22.319><c> too</c>

00:05:22.550 --> 00:05:22.560 align:start position:0%
configuration if the CSV file is too
 

00:05:22.560 --> 00:05:24.510 align:start position:0%
configuration if the CSV file is too
large<00:05:23.039><c> if</c><00:05:23.120><c> you</c><00:05:23.199><c> have</c><00:05:23.360><c> any</c><00:05:23.520><c> questions</c><00:05:23.880><c> on</c><00:05:24.080><c> that</c>

00:05:24.510 --> 00:05:24.520 align:start position:0%
large if you have any questions on that
 

00:05:24.520 --> 00:05:27.990 align:start position:0%
large if you have any questions on that
just<00:05:24.680><c> feel</c><00:05:24.919><c> free</c><00:05:25.120><c> to</c><00:05:25.280><c> reach</c><00:05:25.520><c> out</c><00:05:26.120><c> all</c><00:05:26.280><c> right</c><00:05:27.080><c> uh</c>

00:05:27.990 --> 00:05:28.000 align:start position:0%
just feel free to reach out all right uh
 

00:05:28.000 --> 00:05:30.510 align:start position:0%
just feel free to reach out all right uh
526<00:05:29.000><c> all</c><00:05:29.160><c> righty</c><00:05:29.440><c> guys</c><00:05:29.919><c> Okay</c><00:05:30.080><c> cool</c><00:05:30.319><c> so</c>

00:05:30.510 --> 00:05:30.520 align:start position:0%
526 all righty guys Okay cool so
 

00:05:30.520 --> 00:05:33.469 align:start position:0%
526 all righty guys Okay cool so
everything<00:05:30.840><c> worked</c><00:05:31.639><c> successfully</c><00:05:32.639><c> now</c><00:05:33.280><c> now</c>

00:05:33.469 --> 00:05:33.479 align:start position:0%
everything worked successfully now now
 

00:05:33.479 --> 00:05:36.230 align:start position:0%
everything worked successfully now now
that<00:05:33.639><c> the</c><00:05:33.840><c> data</c><00:05:34.280><c> is</c><00:05:34.479><c> in</c><00:05:34.680><c> my</c><00:05:34.880><c> SQL</c><00:05:35.400><c> database</c><00:05:36.080><c> on</c>

00:05:36.230 --> 00:05:36.240 align:start position:0%
that the data is in my SQL database on
 

00:05:36.240 --> 00:05:38.710 align:start position:0%
that the data is in my SQL database on
my<00:05:36.440><c> local</c><00:05:36.759><c> SQL</c><00:05:37.199><c> database</c><00:05:37.800><c> all</c><00:05:37.919><c> I</c><00:05:38.000><c> need</c><00:05:38.160><c> to</c><00:05:38.319><c> do</c>

00:05:38.710 --> 00:05:38.720 align:start position:0%
my local SQL database all I need to do
 

00:05:38.720 --> 00:05:42.950 align:start position:0%
my local SQL database all I need to do
is<00:05:38.919><c> take</c><00:05:39.160><c> this</c><00:05:39.319><c> SQL</c><00:05:40.199><c> database</c><00:05:41.199><c> and</c><00:05:41.680><c> upload</c><00:05:42.120><c> it</c>

00:05:42.950 --> 00:05:42.960 align:start position:0%
is take this SQL database and upload it
 

00:05:42.960 --> 00:05:45.390 align:start position:0%
is take this SQL database and upload it
uh<00:05:43.160><c> and</c><00:05:43.479><c> Export</c><00:05:43.960><c> it</c><00:05:44.560><c> first</c><00:05:44.960><c> and</c><00:05:45.080><c> then</c><00:05:45.199><c> I'll</c>

00:05:45.390 --> 00:05:45.400 align:start position:0%
uh and Export it first and then I'll
 

00:05:45.400 --> 00:05:49.909 align:start position:0%
uh and Export it first and then I'll
upload<00:05:46.000><c> this</c><00:05:46.199><c> SQL</c><00:05:46.639><c> database</c><00:05:47.400><c> into</c><00:05:48.600><c> our</c><00:05:49.600><c> into</c>

00:05:49.909 --> 00:05:49.919 align:start position:0%
upload this SQL database into our into
 

00:05:49.919 --> 00:05:53.590 align:start position:0%
upload this SQL database into our into
our<00:05:50.360><c> live</c><00:05:51.240><c> live</c><00:05:51.800><c> uh</c><00:05:52.000><c> Cloud</c><00:05:52.440><c> SQL</c><00:05:52.919><c> database</c><00:05:53.479><c> all</c>

00:05:53.590 --> 00:05:53.600 align:start position:0%
our live live uh Cloud SQL database all
 

00:05:53.600 --> 00:05:55.150 align:start position:0%
our live live uh Cloud SQL database all
right<00:05:53.759><c> so</c><00:05:53.880><c> here</c><00:05:54.000><c> we</c><00:05:54.120><c> go</c><00:05:54.360><c> step</c><00:05:54.560><c> number</c><00:05:54.800><c> one</c><00:05:55.080><c> how</c>

00:05:55.150 --> 00:05:55.160 align:start position:0%
right so here we go step number one how
 

00:05:55.160 --> 00:05:57.710 align:start position:0%
right so here we go step number one how
do<00:05:55.319><c> I</c><00:05:55.520><c> actually</c><00:05:56.000><c> export</c><00:05:56.479><c> this</c><00:05:57.160><c> ah</c><00:05:57.319><c> there's</c><00:05:57.440><c> an</c>

00:05:57.710 --> 00:05:57.720 align:start position:0%
do I actually export this ah there's an
 

00:05:57.720 --> 00:06:00.749 align:start position:0%
do I actually export this ah there's an
export<00:05:58.120><c> feature</c><00:05:58.560><c> over</c><00:05:58.840><c> here</c>

00:06:00.749 --> 00:06:00.759 align:start position:0%
export feature over here
 

00:06:00.759 --> 00:06:03.189 align:start position:0%
export feature over here
all<00:06:00.919><c> righty</c><00:06:01.680><c> let's</c><00:06:01.919><c> have</c><00:06:02.000><c> a</c><00:06:02.160><c> look</c><00:06:02.360><c> at</c><00:06:02.520><c> it</c><00:06:03.039><c> all</c>

00:06:03.189 --> 00:06:03.199 align:start position:0%
all righty let's have a look at it all
 

00:06:03.199 --> 00:06:06.670 align:start position:0%
all righty let's have a look at it all
right<00:06:03.440><c> cool</c><00:06:03.840><c> export</c><00:06:04.360><c> template</c><00:06:04.960><c> format</c><00:06:05.400><c> is</c><00:06:05.680><c> SQL</c>

00:06:06.670 --> 00:06:06.680 align:start position:0%
right cool export template format is SQL
 

00:06:06.680 --> 00:06:09.150 align:start position:0%
right cool export template format is SQL
template<00:06:07.240><c> name</c><00:06:08.080><c> n</c><00:06:08.360><c> that's</c><00:06:08.680><c> okay</c><00:06:08.919><c> I'm</c><00:06:09.039><c> just</c>

00:06:09.150 --> 00:06:09.160 align:start position:0%
template name n that's okay I'm just
 

00:06:09.160 --> 00:06:12.589 align:start position:0%
template name n that's okay I'm just
going<00:06:09.240><c> to</c><00:06:09.400><c> press</c><00:06:10.160><c> go</c><00:06:11.160><c> all</c><00:06:11.319><c> right</c><00:06:11.560><c> cool</c><00:06:11.800><c> CSV</c><00:06:12.400><c> to</c>

00:06:12.589 --> 00:06:12.599 align:start position:0%
going to press go all right cool CSV to
 

00:06:12.599 --> 00:06:16.390 align:start position:0%
going to press go all right cool CSV to
SQL<00:06:13.360><c> demo</c><00:06:14.360><c> it</c><00:06:14.639><c> do</c><00:06:14.960><c> SQL</c><00:06:15.360><c> you'll</c><00:06:15.520><c> notice</c><00:06:15.800><c> it's</c><00:06:15.919><c> a</c>

00:06:16.390 --> 00:06:16.400 align:start position:0%
SQL demo it do SQL you'll notice it's a
 

00:06:16.400 --> 00:06:18.710 align:start position:0%
SQL demo it do SQL you'll notice it's a
SQL<00:06:16.840><c> file</c><00:06:17.160><c> at</c><00:06:17.360><c> this</c><00:06:17.560><c> point</c><00:06:18.160><c> oh</c><00:06:18.400><c> by</c><00:06:18.520><c> the</c><00:06:18.599><c> way</c>

00:06:18.710 --> 00:06:18.720 align:start position:0%
SQL file at this point oh by the way
 

00:06:18.720 --> 00:06:22.270 align:start position:0%
SQL file at this point oh by the way
we've<00:06:18.880><c> got</c><00:06:19.080><c> table</c><00:06:19.400><c> one</c><00:06:19.720><c> over</c><00:06:20.240><c> here</c><00:06:21.240><c> okay</c><00:06:22.080><c> uh</c>

00:06:22.270 --> 00:06:22.280 align:start position:0%
we've got table one over here okay uh
 

00:06:22.280 --> 00:06:25.029 align:start position:0%
we've got table one over here okay uh
let's<00:06:22.479><c> see</c><00:06:22.759><c> over</c><00:06:23.160><c> here</c><00:06:24.160><c> okay</c><00:06:24.360><c> cool</c><00:06:24.560><c> so</c><00:06:24.759><c> all</c><00:06:24.919><c> the</c>

00:06:25.029 --> 00:06:25.039 align:start position:0%
let's see over here okay cool so all the
 

00:06:25.039 --> 00:06:30.150 align:start position:0%
let's see over here okay cool so all the
data<00:06:25.319><c> is</c><00:06:25.560><c> now</c><00:06:26.039><c> in</c><00:06:26.520><c> in</c><00:06:26.639><c> our</c><00:06:26.840><c> local</c><00:06:27.160><c> SQL</c><00:06:27.680><c> database</c>

00:06:30.150 --> 00:06:30.160 align:start position:0%
data is now in in our local SQL database
 

00:06:30.160 --> 00:06:32.670 align:start position:0%
data is now in in our local SQL database
H<00:06:31.160><c> you</c><00:06:31.280><c> know</c><00:06:31.440><c> what</c><00:06:31.599><c> we</c><00:06:31.720><c> may</c><00:06:31.880><c> want</c><00:06:32.000><c> to</c><00:06:32.240><c> change</c>

00:06:32.670 --> 00:06:32.680 align:start position:0%
H you know what we may want to change
 

00:06:32.680 --> 00:06:35.589 align:start position:0%
H you know what we may want to change
some<00:06:32.840><c> of</c><00:06:33.039><c> these</c><00:06:33.479><c> columns</c><00:06:34.479><c> we</c><00:06:34.639><c> also</c><00:06:35.199><c> uh</c><00:06:35.400><c> let's</c>

00:06:35.589 --> 00:06:35.599 align:start position:0%
some of these columns we also uh let's
 

00:06:35.599 --> 00:06:37.070 align:start position:0%
some of these columns we also uh let's
let's<00:06:35.759><c> have</c><00:06:35.880><c> a</c><00:06:35.960><c> look</c><00:06:36.080><c> at</c><00:06:36.199><c> the</c><00:06:36.360><c> table</c><00:06:36.720><c> for</c><00:06:36.880><c> a</c>

00:06:37.070 --> 00:06:37.080 align:start position:0%
let's have a look at the table for a
 

00:06:37.080 --> 00:06:38.309 align:start position:0%
let's have a look at the table for a
second<00:06:37.400><c> all</c><00:06:37.560><c> right</c><00:06:37.720><c> let's</c><00:06:37.880><c> look</c><00:06:38.000><c> at</c><00:06:38.160><c> the</c>

00:06:38.309 --> 00:06:38.319 align:start position:0%
second all right let's look at the
 

00:06:38.319 --> 00:06:39.670 align:start position:0%
second all right let's look at the
structure

00:06:39.670 --> 00:06:39.680 align:start position:0%
structure
 

00:06:39.680 --> 00:06:42.150 align:start position:0%
structure
here<00:06:40.680><c> all</c><00:06:40.919><c> righty</c><00:06:41.280><c> here</c><00:06:41.479><c> you</c><00:06:41.560><c> see</c><00:06:41.880><c> everything</c>

00:06:42.150 --> 00:06:42.160 align:start position:0%
here all righty here you see everything
 

00:06:42.160 --> 00:06:45.230 align:start position:0%
here all righty here you see everything
here<00:06:42.319><c> is</c><00:06:42.440><c> a</c><00:06:42.599><c> varar</c><00:06:43.400><c> 13</c><00:06:44.280><c> so</c><00:06:44.560><c> the</c><00:06:44.759><c> type</c><00:06:45.080><c> the</c>

00:06:45.230 --> 00:06:45.240 align:start position:0%
here is a varar 13 so the type the
 

00:06:45.240 --> 00:06:47.550 align:start position:0%
here is a varar 13 so the type the
column<00:06:45.639><c> type</c><00:06:45.880><c> is</c><00:06:46.039><c> also</c><00:06:46.360><c> important</c><00:06:46.800><c> over</c><00:06:47.039><c> here</c>

00:06:47.550 --> 00:06:47.560 align:start position:0%
column type is also important over here
 

00:06:47.560 --> 00:06:50.469 align:start position:0%
column type is also important over here
click<00:06:47.919><c> time</c><00:06:48.199><c> install</c><00:06:48.680><c> time</c><00:06:49.080><c> install</c><00:06:49.639><c> date</c>

00:06:50.469 --> 00:06:50.479 align:start position:0%
click time install time install date
 

00:06:50.479 --> 00:06:52.749 align:start position:0%
click time install time install date
these<00:06:50.680><c> need</c><00:06:50.840><c> to</c><00:06:51.000><c> be</c><00:06:51.160><c> changed</c><00:06:51.800><c> into</c><00:06:52.360><c> date</c>

00:06:52.749 --> 00:06:52.759 align:start position:0%
these need to be changed into date
 

00:06:52.759 --> 00:06:55.629 align:start position:0%
these need to be changed into date
format<00:06:53.400><c> before</c><00:06:53.840><c> we</c><00:06:54.080><c> actually</c><00:06:54.720><c> export</c><00:06:55.319><c> so</c><00:06:55.560><c> you</c>

00:06:55.629 --> 00:06:55.639 align:start position:0%
format before we actually export so you
 

00:06:55.639 --> 00:06:58.230 align:start position:0%
format before we actually export so you
know<00:06:55.800><c> what</c><00:06:56.080><c> cancel</c><00:06:56.520><c> this</c><00:06:56.759><c> ex</c><00:06:57.400><c> let's</c><00:06:57.639><c> go</c><00:06:57.800><c> into</c>

00:06:58.230 --> 00:06:58.240 align:start position:0%
know what cancel this ex let's go into
 

00:06:58.240 --> 00:07:00.550 align:start position:0%
know what cancel this ex let's go into
the<00:06:58.520><c> downloads</c><00:06:59.080><c> and</c><00:06:59.199><c> just</c><00:06:59.639><c> delete</c><00:07:00.000><c> that</c><00:07:00.240><c> file</c>

00:07:00.550 --> 00:07:00.560 align:start position:0%
the downloads and just delete that file
 

00:07:00.560 --> 00:07:02.189 align:start position:0%
the downloads and just delete that file
for<00:07:00.759><c> a</c>

00:07:02.189 --> 00:07:02.199 align:start position:0%
for a
 

00:07:02.199 --> 00:07:04.550 align:start position:0%
for a
second<00:07:03.199><c> all</c><00:07:03.360><c> righty</c><00:07:03.840><c> we're</c><00:07:04.000><c> going</c><00:07:04.080><c> to</c><00:07:04.240><c> delete</c>

00:07:04.550 --> 00:07:04.560 align:start position:0%
second all righty we're going to delete
 

00:07:04.560 --> 00:07:06.869 align:start position:0%
second all righty we're going to delete
that<00:07:04.680><c> SQL</c><00:07:05.160><c> file</c><00:07:06.160><c> uh</c><00:07:06.280><c> if</c><00:07:06.400><c> anything</c><00:07:06.680><c> is</c>

00:07:06.869 --> 00:07:06.879 align:start position:0%
that SQL file uh if anything is
 

00:07:06.879 --> 00:07:08.749 align:start position:0%
that SQL file uh if anything is
confusing<00:07:07.400><c> again</c><00:07:07.639><c> feel</c><00:07:07.840><c> free</c><00:07:08.000><c> to</c><00:07:08.160><c> reach</c><00:07:08.360><c> out</c>

00:07:08.749 --> 00:07:08.759 align:start position:0%
confusing again feel free to reach out
 

00:07:08.759 --> 00:07:11.350 align:start position:0%
confusing again feel free to reach out
hopefully<00:07:09.160><c> this</c><00:07:09.319><c> will</c><00:07:09.479><c> be</c><00:07:09.879><c> clear</c><00:07:10.879><c> all</c><00:07:11.000><c> righty</c>

00:07:11.350 --> 00:07:11.360 align:start position:0%
hopefully this will be clear all righty
 

00:07:11.360 --> 00:07:15.110 align:start position:0%
hopefully this will be clear all righty
so<00:07:12.080><c> um</c><00:07:12.720><c> now</c><00:07:13.120><c> if</c><00:07:13.199><c> I</c><00:07:13.360><c> go</c><00:07:13.479><c> into</c><00:07:13.720><c> my</c><00:07:14.000><c> type</c><00:07:14.720><c> I</c><00:07:14.840><c> know</c>

00:07:15.110 --> 00:07:15.120 align:start position:0%
so um now if I go into my type I know
 

00:07:15.120 --> 00:07:18.029 align:start position:0%
so um now if I go into my type I know
that<00:07:15.400><c> this</c><00:07:15.840><c> click</c><00:07:16.160><c> time</c><00:07:16.319><c> and</c><00:07:16.520><c> install</c><00:07:17.039><c> time</c><00:07:17.759><c> uh</c>

00:07:18.029 --> 00:07:18.039 align:start position:0%
that this click time and install time uh
 

00:07:18.039 --> 00:07:21.710 align:start position:0%
that this click time and install time uh
these<00:07:18.280><c> two</c><00:07:18.919><c> the</c><00:07:19.440><c> a</c><00:07:19.560><c> lot</c><00:07:19.680><c> of</c><00:07:19.879><c> these</c><00:07:20.800><c> um</c><00:07:21.160><c> columns</c>

00:07:21.710 --> 00:07:21.720 align:start position:0%
these two the a lot of these um columns
 

00:07:21.720 --> 00:07:24.270 align:start position:0%
these two the a lot of these um columns
are<00:07:22.000><c> actually</c><00:07:22.479><c> should</c><00:07:22.720><c> be</c><00:07:23.160><c> type</c><00:07:23.720><c> date</c><00:07:24.039><c> so</c><00:07:24.199><c> I'm</c>

00:07:24.270 --> 00:07:24.280 align:start position:0%
are actually should be type date so I'm
 

00:07:24.280 --> 00:07:26.589 align:start position:0%
are actually should be type date so I'm
going<00:07:24.400><c> to</c><00:07:24.599><c> go</c><00:07:24.759><c> ahead</c><00:07:24.919><c> and</c><00:07:25.120><c> change</c><00:07:25.520><c> it</c><00:07:26.400><c> and</c><00:07:26.520><c> I'm</c>

00:07:26.589 --> 00:07:26.599 align:start position:0%
going to go ahead and change it and I'm
 

00:07:26.599 --> 00:07:28.869 align:start position:0%
going to go ahead and change it and I'm
going<00:07:26.720><c> to</c><00:07:26.879><c> choose</c><00:07:27.520><c> the</c><00:07:27.720><c> type</c><00:07:28.000><c> is</c><00:07:28.160><c> a</c><00:07:28.400><c> date</c>

00:07:28.869 --> 00:07:28.879 align:start position:0%
going to choose the type is a date
 

00:07:28.879 --> 00:07:31.270 align:start position:0%
going to choose the type is a date
instead<00:07:29.120><c> of</c><00:07:29.240><c> typ</c><00:07:29.800><c> varar</c><00:07:30.800><c> I'm</c><00:07:30.919><c> going</c><00:07:31.000><c> to</c><00:07:31.160><c> go</c>

00:07:31.270 --> 00:07:31.280 align:start position:0%
instead of typ varar I'm going to go
 

00:07:31.280 --> 00:07:34.629 align:start position:0%
instead of typ varar I'm going to go
into<00:07:31.639><c> date</c><00:07:32.360><c> all</c><00:07:32.560><c> right</c><00:07:33.440><c> uh</c><00:07:33.680><c> there</c><00:07:33.800><c> we</c><00:07:34.000><c> go</c><00:07:34.440><c> oh</c>

00:07:34.629 --> 00:07:34.639 align:start position:0%
into date all right uh there we go oh
 

00:07:34.639 --> 00:07:36.510 align:start position:0%
into date all right uh there we go oh
wait<00:07:34.759><c> it's</c><00:07:34.960><c> actually</c><00:07:35.319><c> a</c><00:07:35.520><c> time</c><00:07:35.840><c> it's</c><00:07:35.960><c> a</c><00:07:36.160><c> date</c><00:07:36.360><c> or</c>

00:07:36.510 --> 00:07:36.520 align:start position:0%
wait it's actually a time it's a date or
 

00:07:36.520 --> 00:07:38.430 align:start position:0%
wait it's actually a time it's a date or
a<00:07:36.720><c> Tim</c><00:07:37.000><c> stamp</c><00:07:37.319><c> we</c><00:07:37.479><c> have</c><00:07:37.599><c> to</c><00:07:37.759><c> double</c><00:07:38.039><c> check</c><00:07:38.319><c> the</c>

00:07:38.430 --> 00:07:38.440 align:start position:0%
a Tim stamp we have to double check the
 

00:07:38.440 --> 00:07:40.990 align:start position:0%
a Tim stamp we have to double check the
data<00:07:38.800><c> over</c><00:07:39.039><c> here</c><00:07:39.800><c> let's</c><00:07:39.960><c> have</c><00:07:40.080><c> a</c><00:07:40.199><c> look</c><00:07:40.360><c> at</c><00:07:40.479><c> the</c>

00:07:40.990 --> 00:07:41.000 align:start position:0%
data over here let's have a look at the
 

00:07:41.000 --> 00:07:46.469 align:start position:0%
data over here let's have a look at the
data<00:07:42.199><c> okay</c><00:07:43.199><c> let's</c><00:07:43.440><c> see</c><00:07:43.759><c> here</c><00:07:44.919><c> uh</c><00:07:45.919><c> Okay</c><00:07:46.159><c> click</c>

00:07:46.469 --> 00:07:46.479 align:start position:0%
data okay let's see here uh Okay click
 

00:07:46.479 --> 00:07:48.550 align:start position:0%
data okay let's see here uh Okay click
time<00:07:46.840><c> oh</c><00:07:46.960><c> no</c><00:07:47.120><c> it's</c><00:07:47.240><c> a</c><00:07:47.440><c> time</c><00:07:47.720><c> stamp</c><00:07:48.159><c> install</c>

00:07:48.550 --> 00:07:48.560 align:start position:0%
time oh no it's a time stamp install
 

00:07:48.560 --> 00:07:50.550 align:start position:0%
time oh no it's a time stamp install
time<00:07:48.759><c> is</c><00:07:48.879><c> a</c><00:07:49.080><c> Tim</c><00:07:49.319><c> stamp</c><00:07:49.720><c> everything</c><00:07:49.960><c> is</c><00:07:50.120><c> a</c><00:07:50.280><c> time</c>

00:07:50.550 --> 00:07:50.560 align:start position:0%
time is a Tim stamp everything is a time
 

00:07:50.560 --> 00:07:54.710 align:start position:0%
time is a Tim stamp everything is a time
stamp<00:07:50.960><c> over</c><00:07:51.199><c> here</c><00:07:51.720><c> install</c><00:07:52.440><c> date</c><00:07:53.440><c> 0</c><00:07:53.759><c> 0</c><00:07:54.479><c> click</c>

00:07:54.710 --> 00:07:54.720 align:start position:0%
stamp over here install date 0 0 click
 

00:07:54.720 --> 00:07:57.670 align:start position:0%
stamp over here install date 0 0 click
to<00:07:54.960><c> install</c><00:07:55.560><c> time</c><00:07:56.560><c> uh</c><00:07:56.720><c> what's</c><00:07:57.000><c> that</c><00:07:57.319><c> that's</c><00:07:57.479><c> an</c>

00:07:57.670 --> 00:07:57.680 align:start position:0%
to install time uh what's that that's an
 

00:07:57.680 --> 00:08:01.469 align:start position:0%
to install time uh what's that that's an
actual<00:07:58.080><c> amount</c><00:07:58.360><c> of</c><00:07:58.599><c> time</c><00:07:59.759><c> H</c><00:08:00.479><c> okay</c><00:08:00.879><c> we'll</c><00:08:01.120><c> think</c>

00:08:01.469 --> 00:08:01.479 align:start position:0%
actual amount of time H okay we'll think
 

00:08:01.479 --> 00:08:03.869 align:start position:0%
actual amount of time H okay we'll think
that<00:08:01.680><c> through</c><00:08:02.280><c> through</c><00:08:02.560><c> at</c><00:08:02.680><c> a</c><00:08:02.840><c> later</c><00:08:03.280><c> stage</c>

00:08:03.869 --> 00:08:03.879 align:start position:0%
that through through at a later stage
 

00:08:03.879 --> 00:08:06.350 align:start position:0%
that through through at a later stage
over<00:08:04.319><c> there</c><00:08:05.000><c> all</c><00:08:05.120><c> right</c><00:08:05.319><c> meanwhile</c><00:08:06.199><c> let's</c>

00:08:06.350 --> 00:08:06.360 align:start position:0%
over there all right meanwhile let's
 

00:08:06.360 --> 00:08:09.270 align:start position:0%
over there all right meanwhile let's
just<00:08:06.479><c> turn</c><00:08:06.720><c> it</c><00:08:06.879><c> turn</c><00:08:07.080><c> it</c><00:08:07.360><c> into</c><00:08:08.360><c> into</c><00:08:08.759><c> what</c><00:08:09.080><c> a</c>

00:08:09.270 --> 00:08:09.280 align:start position:0%
just turn it turn it into into what a
 

00:08:09.280 --> 00:08:11.830 align:start position:0%
just turn it turn it into into what a
date<00:08:09.599><c> time</c><00:08:09.960><c> time</c><00:08:10.240><c> stamp</c><00:08:10.599><c> time</c><00:08:10.840><c> stamp</c><00:08:11.120><c> is</c><00:08:11.360><c> good</c>

00:08:11.830 --> 00:08:11.840 align:start position:0%
date time time stamp time stamp is good
 

00:08:11.840 --> 00:08:13.430 align:start position:0%
date time time stamp time stamp is good
all<00:08:12.000><c> right</c><00:08:12.199><c> time</c><00:08:12.440><c> stamp</c><00:08:12.759><c> and</c><00:08:12.879><c> we'll</c><00:08:13.039><c> see</c><00:08:13.159><c> if</c><00:08:13.280><c> it</c>

00:08:13.430 --> 00:08:13.440 align:start position:0%
all right time stamp and we'll see if it
 

00:08:13.440 --> 00:08:16.710 align:start position:0%
all right time stamp and we'll see if it
works<00:08:14.039><c> save</c><00:08:15.039><c> all</c><00:08:15.199><c> right</c><00:08:15.919><c> uh</c><00:08:16.159><c> two</c><00:08:16.440><c> big</c>

00:08:16.710 --> 00:08:16.720 align:start position:0%
works save all right uh two big
 

00:08:16.720 --> 00:08:19.309 align:start position:0%
works save all right uh two big
Precision<00:08:17.240><c> 13</c><00:08:17.879><c> specified</c><00:08:18.400><c> for</c><00:08:18.639><c> click</c><00:08:18.960><c> time</c>

00:08:19.309 --> 00:08:19.319 align:start position:0%
Precision 13 specified for click time
 

00:08:19.319 --> 00:08:21.430 align:start position:0%
Precision 13 specified for click time
maximize<00:08:19.919><c> is</c><00:08:20.199><c> maximum</c><00:08:20.639><c> is</c><00:08:20.759><c> six</c><00:08:21.120><c> okay</c><00:08:21.240><c> what</c><00:08:21.319><c> if</c>

00:08:21.430 --> 00:08:21.440 align:start position:0%
maximize is maximum is six okay what if
 

00:08:21.440 --> 00:08:24.230 align:start position:0%
maximize is maximum is six okay what if
we<00:08:21.560><c> turn</c><00:08:21.680><c> it</c><00:08:21.840><c> into</c><00:08:22.039><c> a</c><00:08:22.400><c> date</c><00:08:23.400><c> what</c><00:08:23.560><c> about</c><00:08:23.720><c> a</c><00:08:23.879><c> date</c>

00:08:24.230 --> 00:08:24.240 align:start position:0%
we turn it into a date what about a date
 

00:08:24.240 --> 00:08:29.869 align:start position:0%
we turn it into a date what about a date
time<00:08:25.240><c> let's</c><00:08:25.440><c> try</c><00:08:25.759><c> date</c><00:08:26.560><c> time</c><00:08:27.919><c> save</c><00:08:28.919><c> h</c><00:08:29.479><c> too</c><00:08:29.680><c> big</c>

00:08:29.869 --> 00:08:29.879 align:start position:0%
time let's try date time save h too big
 

00:08:29.879 --> 00:08:31.629 align:start position:0%
time let's try date time save h too big
Precision<00:08:30.479><c> we</c><00:08:30.560><c> got</c><00:08:30.680><c> to</c><00:08:30.879><c> take</c><00:08:31.199><c> that</c><00:08:31.400><c> and</c>

00:08:31.629 --> 00:08:31.639 align:start position:0%
Precision we got to take that and
 

00:08:31.639 --> 00:08:36.829 align:start position:0%
Precision we got to take that and
actually<00:08:32.240><c> Google</c><00:08:32.719><c> it</c><00:08:33.719><c> too</c><00:08:34.080><c> big</c>

00:08:36.829 --> 00:08:36.839 align:start position:0%
 
 

00:08:36.839 --> 00:08:39.430 align:start position:0%
 
Precision<00:08:37.839><c> uh</c><00:08:38.200><c> let's</c><00:08:38.399><c> see</c><00:08:38.719><c> what's</c><00:08:38.919><c> the</c><00:08:39.039><c> full</c>

00:08:39.430 --> 00:08:39.440 align:start position:0%
Precision uh let's see what's the full
 

00:08:39.440 --> 00:08:42.029 align:start position:0%
Precision uh let's see what's the full
error<00:08:39.959><c> here</c>

00:08:42.029 --> 00:08:42.039 align:start position:0%
error here
 

00:08:42.039 --> 00:08:45.870 align:start position:0%
error here
save<00:08:43.039><c> 13</c><00:08:43.959><c> specified</c><00:08:44.480><c> for</c><00:08:44.640><c> click</c><00:08:44.920><c> time</c><00:08:45.480><c> too</c><00:08:45.680><c> big</c>

00:08:45.870 --> 00:08:45.880 align:start position:0%
save 13 specified for click time too big
 

00:08:45.880 --> 00:08:50.550 align:start position:0%
save 13 specified for click time too big
Precision<00:08:46.800><c> 13</c><00:08:47.800><c> to</c><00:08:48.080><c> Big</c><00:08:48.480><c> Precision</c><00:08:49.480><c> 13</c><00:08:50.360><c> all</c>

00:08:50.550 --> 00:08:50.560 align:start position:0%
Precision 13 to Big Precision 13 all
 

00:08:50.560 --> 00:08:53.630 align:start position:0%
Precision 13 to Big Precision 13 all
right<00:08:51.160><c> what</c><00:08:51.399><c> 14</c><00:08:51.959><c> whatever</c><00:08:52.279><c> good</c><00:08:52.480><c> enough</c><00:08:53.480><c> all</c>

00:08:53.630 --> 00:08:53.640 align:start position:0%
right what 14 whatever good enough all
 

00:08:53.640 --> 00:08:56.910 align:start position:0%
right what 14 whatever good enough all
right<00:08:53.880><c> let's</c><00:08:54.080><c> have</c><00:08:54.200><c> a</c><00:08:54.360><c> look</c><00:08:54.519><c> at</c>

00:08:56.910 --> 00:08:56.920 align:start position:0%
 
 

00:08:56.920 --> 00:09:00.190 align:start position:0%
 
it<00:08:57.920><c> this</c><00:08:58.120><c> works</c><00:08:58.399><c> in</c><00:08:58.600><c> my</c><00:08:58.920><c> SQL</c><00:08:59.720><c> there</c><00:08:59.839><c> are</c><00:09:00.000><c> two</c>

00:09:00.190 --> 00:09:00.200 align:start position:0%
it this works in my SQL there are two
 

00:09:00.200 --> 00:09:02.670 align:start position:0%
it this works in my SQL there are two
changes<00:09:00.640><c> drop</c><00:09:00.959><c> the</c><00:09:01.120><c> 14</c><00:09:01.720><c> from</c><00:09:01.920><c> the</c><00:09:02.079><c> line</c><00:09:02.360><c> last</c>

00:09:02.670 --> 00:09:02.680 align:start position:0%
changes drop the 14 from the line last
 

00:09:02.680 --> 00:09:06.230 align:start position:0%
changes drop the 14 from the line last
Mod<00:09:03.079><c> timestamp</c><00:09:03.760><c> change</c><00:09:04.760><c> my</c><00:09:05.200><c> ISM</c><00:09:05.680><c> to</c><00:09:05.880><c> engine</c>

00:09:06.230 --> 00:09:06.240 align:start position:0%
Mod timestamp change my ISM to engine
 

00:09:06.240 --> 00:09:08.750 align:start position:0%
Mod timestamp change my ISM to engine
equals<00:09:06.600><c> my</c><00:09:06.880><c> ISM</c><00:09:07.440><c> this</c><00:09:08.320><c> there</c><00:09:08.399><c> could</c><00:09:08.560><c> be</c><00:09:08.640><c> a</c>

00:09:08.750 --> 00:09:08.760 align:start position:0%
equals my ISM this there could be a
 

00:09:08.760 --> 00:09:11.430 align:start position:0%
equals my ISM this there could be a
version<00:09:09.160><c> mismatch</c><00:09:09.640><c> for</c><00:09:09.800><c> Server</c>

00:09:11.430 --> 00:09:11.440 align:start position:0%
version mismatch for Server
 

00:09:11.440 --> 00:09:13.590 align:start position:0%
version mismatch for Server
configuration<00:09:12.440><c> oh</c><00:09:12.640><c> schnikes</c><00:09:13.120><c> it</c><00:09:13.240><c> could</c><00:09:13.399><c> be</c>

00:09:13.590 --> 00:09:13.600 align:start position:0%
configuration oh schnikes it could be
 

00:09:13.600 --> 00:09:15.389 align:start position:0%
configuration oh schnikes it could be
because<00:09:13.920><c> our</c><00:09:14.240><c> database</c><00:09:14.720><c> wasn't</c><00:09:15.000><c> the</c><00:09:15.120><c> right</c>

00:09:15.389 --> 00:09:15.399 align:start position:0%
because our database wasn't the right
 

00:09:15.399 --> 00:09:17.829 align:start position:0%
because our database wasn't the right
format<00:09:15.720><c> all</c><00:09:15.880><c> right</c><00:09:16.640><c> as</c><00:09:16.839><c> usual</c><00:09:17.320><c> stuff</c><00:09:17.560><c> goes</c>

00:09:17.829 --> 00:09:17.839 align:start position:0%
format all right as usual stuff goes
 

00:09:17.839 --> 00:09:20.630 align:start position:0%
format all right as usual stuff goes
wrong<00:09:18.079><c> in</c><00:09:18.200><c> the</c><00:09:18.320><c> middle</c><00:09:19.120><c> as</c><00:09:19.440><c> as</c><00:09:19.680><c> usual</c><00:09:20.279><c> so</c><00:09:20.519><c> I'm</c>

00:09:20.630 --> 00:09:20.640 align:start position:0%
wrong in the middle as as usual so I'm
 

00:09:20.640 --> 00:09:22.790 align:start position:0%
wrong in the middle as as usual so I'm
going<00:09:20.760><c> to</c><00:09:20.920><c> take</c><00:09:21.079><c> some</c><00:09:21.279><c> time</c><00:09:21.480><c> to</c><00:09:21.800><c> just</c><00:09:22.040><c> try</c><00:09:22.200><c> to</c>

00:09:22.790 --> 00:09:22.800 align:start position:0%
going to take some time to just try to
 

00:09:22.800 --> 00:09:25.030 align:start position:0%
going to take some time to just try to
try<00:09:23.000><c> to</c><00:09:23.160><c> Google</c><00:09:23.560><c> this</c><00:09:23.760><c> error</c><00:09:24.399><c> and</c><00:09:24.560><c> see</c><00:09:24.760><c> if</c><00:09:24.920><c> I</c>

00:09:25.030 --> 00:09:25.040 align:start position:0%
try to Google this error and see if I
 

00:09:25.040 --> 00:09:26.509 align:start position:0%
try to Google this error and see if I
can<00:09:25.240><c> fix</c><00:09:25.480><c> it</c><00:09:25.640><c> all</c><00:09:25.760><c> right</c><00:09:25.920><c> guys</c><00:09:26.040><c> and</c><00:09:26.160><c> I'll</c><00:09:26.320><c> see</c>

00:09:26.509 --> 00:09:26.519 align:start position:0%
can fix it all right guys and I'll see
 

00:09:26.519 --> 00:09:30.320 align:start position:0%
can fix it all right guys and I'll see
you<00:09:26.760><c> in</c><00:09:26.920><c> the</c><00:09:27.079><c> next</c><00:09:27.320><c> one</c>

