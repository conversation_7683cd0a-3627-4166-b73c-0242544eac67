WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.389 align:start position:0%
 
okay<00:00:00.599><c> this</c><00:00:01.020><c> video</c><00:00:01.260><c> will</c><00:00:01.500><c> be</c><00:00:01.620><c> just</c><00:00:01.860><c> a</c><00:00:02.040><c> quick</c><00:00:02.159><c> one</c>

00:00:02.389 --> 00:00:02.399 align:start position:0%
okay this video will be just a quick one
 

00:00:02.399 --> 00:00:04.730 align:start position:0%
okay this video will be just a quick one
to<00:00:02.639><c> follow</c><00:00:02.820><c> up</c><00:00:03.120><c> how</c><00:00:03.840><c> could</c><00:00:04.020><c> you</c><00:00:04.200><c> do</c><00:00:04.560><c> the</c>

00:00:04.730 --> 00:00:04.740 align:start position:0%
to follow up how could you do the
 

00:00:04.740 --> 00:00:07.070 align:start position:0%
to follow up how could you do the
summarization<00:00:05.279><c> with</c><00:00:05.580><c> the</c><00:00:05.700><c> chat</c><00:00:05.880><c> GPT</c><00:00:06.359><c> turbo</c>

00:00:07.070 --> 00:00:07.080 align:start position:0%
summarization with the chat GPT turbo
 

00:00:07.080 --> 00:00:08.330 align:start position:0%
summarization with the chat GPT turbo
API

00:00:08.330 --> 00:00:08.340 align:start position:0%
API
 

00:00:08.340 --> 00:00:10.190 align:start position:0%
API
so<00:00:08.700><c> I'm</c><00:00:08.820><c> going</c><00:00:09.000><c> to</c><00:00:09.059><c> show</c><00:00:09.179><c> you</c><00:00:09.300><c> we've</c><00:00:10.019><c> gone</c>

00:00:10.190 --> 00:00:10.200 align:start position:0%
so I'm going to show you we've gone
 

00:00:10.200 --> 00:00:12.350 align:start position:0%
so I'm going to show you we've gone
through<00:00:10.440><c> two</c><00:00:10.740><c> videos</c><00:00:10.980><c> now</c><00:00:11.280><c> of</c><00:00:11.460><c> doing</c><00:00:11.700><c> it</c><00:00:12.000><c> with</c>

00:00:12.350 --> 00:00:12.360 align:start position:0%
through two videos now of doing it with
 

00:00:12.360 --> 00:00:14.749 align:start position:0%
through two videos now of doing it with
the<00:00:12.719><c> standard</c><00:00:13.080><c> one</c><00:00:13.259><c> and</c><00:00:13.860><c> just</c><00:00:14.160><c> recapping</c>

00:00:14.749 --> 00:00:14.759 align:start position:0%
the standard one and just recapping
 

00:00:14.759 --> 00:00:16.730 align:start position:0%
the standard one and just recapping
there<00:00:15.000><c> you</c><00:00:15.360><c> could</c><00:00:15.480><c> do</c><00:00:15.719><c> it</c><00:00:15.839><c> either</c><00:00:16.139><c> through</c><00:00:16.500><c> the</c>

00:00:16.730 --> 00:00:16.740 align:start position:0%
there you could do it either through the
 

00:00:16.740 --> 00:00:18.590 align:start position:0%
there you could do it either through the
summarization<00:00:17.340><c> chains</c><00:00:17.940><c> or</c><00:00:18.359><c> you</c><00:00:18.480><c> could</c>

00:00:18.590 --> 00:00:18.600 align:start position:0%
summarization chains or you could
 

00:00:18.600 --> 00:00:20.210 align:start position:0%
summarization chains or you could
actually<00:00:18.779><c> just</c><00:00:19.020><c> set</c><00:00:19.199><c> up</c><00:00:19.440><c> a</c><00:00:19.800><c> large</c><00:00:19.920><c> language</c>

00:00:20.210 --> 00:00:20.220 align:start position:0%
actually just set up a large language
 

00:00:20.220 --> 00:00:22.070 align:start position:0%
actually just set up a large language
model<00:00:20.580><c> and</c><00:00:21.060><c> pass</c><00:00:21.180><c> it</c><00:00:21.359><c> in</c><00:00:21.480><c> so</c><00:00:21.720><c> we've</c><00:00:21.840><c> got</c><00:00:21.960><c> the</c>

00:00:22.070 --> 00:00:22.080 align:start position:0%
model and pass it in so we've got the
 

00:00:22.080 --> 00:00:23.990 align:start position:0%
model and pass it in so we've got the
exact<00:00:22.380><c> same</c><00:00:22.560><c> article</c><00:00:23.100><c> about</c><00:00:23.279><c> coinbase</c><00:00:23.760><c> that</c>

00:00:23.990 --> 00:00:24.000 align:start position:0%
exact same article about coinbase that
 

00:00:24.000 --> 00:00:26.029 align:start position:0%
exact same article about coinbase that
we<00:00:24.119><c> had</c><00:00:24.300><c> in</c><00:00:24.480><c> the</c><00:00:24.720><c> last</c><00:00:24.960><c> video</c><00:00:25.260><c> so</c><00:00:25.680><c> you</c><00:00:25.859><c> can</c><00:00:25.920><c> see</c>

00:00:26.029 --> 00:00:26.039 align:start position:0%
we had in the last video so you can see
 

00:00:26.039 --> 00:00:28.009 align:start position:0%
we had in the last video so you can see
the<00:00:26.160><c> facts</c><00:00:26.519><c> there</c><00:00:26.699><c> and</c><00:00:27.300><c> doing</c><00:00:27.660><c> the</c><00:00:27.900><c> fact</c>

00:00:28.009 --> 00:00:28.019 align:start position:0%
the facts there and doing the fact
 

00:00:28.019 --> 00:00:30.410 align:start position:0%
the facts there and doing the fact
extraction<00:00:28.619><c> we</c><00:00:29.099><c> get</c><00:00:29.279><c> that</c><00:00:29.460><c> with</c><00:00:29.760><c> the</c><00:00:30.000><c> standard</c>

00:00:30.410 --> 00:00:30.420 align:start position:0%
extraction we get that with the standard
 

00:00:30.420 --> 00:00:34.130 align:start position:0%
extraction we get that with the standard
API<00:00:31.140><c> so</c><00:00:31.740><c> with</c><00:00:31.980><c> the</c><00:00:32.160><c> chechi</c><00:00:32.520><c> BT</c><00:00:32.820><c> API</c><00:00:33.420><c> there</c><00:00:34.020><c> are</c>

00:00:34.130 --> 00:00:34.140 align:start position:0%
API so with the chechi BT API there are
 

00:00:34.140 --> 00:00:35.150 align:start position:0%
API so with the chechi BT API there are
a<00:00:34.200><c> couple</c><00:00:34.320><c> of</c><00:00:34.380><c> different</c><00:00:34.500><c> things</c><00:00:34.680><c> we</c><00:00:34.920><c> need</c><00:00:35.040><c> to</c>

00:00:35.150 --> 00:00:35.160 align:start position:0%
a couple of different things we need to
 

00:00:35.160 --> 00:00:37.250 align:start position:0%
a couple of different things we need to
think<00:00:35.280><c> about</c><00:00:35.460><c> one</c><00:00:36.059><c> that</c><00:00:36.300><c> this</c><00:00:36.540><c> is</c><00:00:36.660><c> built</c><00:00:37.079><c> for</c>

00:00:37.250 --> 00:00:37.260 align:start position:0%
think about one that this is built for
 

00:00:37.260 --> 00:00:40.610 align:start position:0%
think about one that this is built for
chat<00:00:37.739><c> the</c><00:00:38.160><c> big</c><00:00:38.340><c> difference</c><00:00:38.640><c> with</c><00:00:39.360><c> this</c><00:00:39.600><c> is</c><00:00:40.140><c> you</c>

00:00:40.610 --> 00:00:40.620 align:start position:0%
chat the big difference with this is you
 

00:00:40.620 --> 00:00:42.350 align:start position:0%
chat the big difference with this is you
now<00:00:40.800><c> have</c><00:00:41.040><c> different</c><00:00:41.340><c> sort</c><00:00:41.700><c> of</c><00:00:41.820><c> prompts</c><00:00:42.239><c> and</c>

00:00:42.350 --> 00:00:42.360 align:start position:0%
now have different sort of prompts and
 

00:00:42.360 --> 00:00:45.290 align:start position:0%
now have different sort of prompts and
different<00:00:42.600><c> inputs</c><00:00:43.260><c> based</c><00:00:43.920><c> on</c><00:00:44.040><c> whether</c><00:00:44.520><c> it's</c>

00:00:45.290 --> 00:00:45.300 align:start position:0%
different inputs based on whether it's
 

00:00:45.300 --> 00:00:47.930 align:start position:0%
different inputs based on whether it's
the<00:00:45.600><c> part</c><00:00:45.780><c> of</c><00:00:46.020><c> the</c><00:00:46.200><c> system</c><00:00:46.500><c> whether</c><00:00:47.280><c> it's</c><00:00:47.579><c> a</c>

00:00:47.930 --> 00:00:47.940 align:start position:0%
the part of the system whether it's a
 

00:00:47.940 --> 00:00:50.869 align:start position:0%
the part of the system whether it's a
user<00:00:48.420><c> whether</c><00:00:48.660><c> it's</c><00:00:48.899><c> the</c><00:00:49.140><c> ai's</c><00:00:49.620><c> response</c><00:00:50.280><c> that</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
user whether it's the ai's response that
 

00:00:50.879 --> 00:00:52.850 align:start position:0%
user whether it's the ai's response that
raises<00:00:51.239><c> some</c><00:00:51.480><c> interesting</c><00:00:51.780><c> issues</c><00:00:52.260><c> of</c><00:00:52.559><c> that</c>

00:00:52.850 --> 00:00:52.860 align:start position:0%
raises some interesting issues of that
 

00:00:52.860 --> 00:00:54.410 align:start position:0%
raises some interesting issues of that
we<00:00:53.160><c> need</c><00:00:53.280><c> to</c><00:00:53.399><c> think</c><00:00:53.520><c> about</c><00:00:53.700><c> okay</c><00:00:53.940><c> how</c><00:00:54.239><c> are</c><00:00:54.360><c> we</c>

00:00:54.410 --> 00:00:54.420 align:start position:0%
we need to think about okay how are we
 

00:00:54.420 --> 00:00:56.330 align:start position:0%
we need to think about okay how are we
going<00:00:54.600><c> to</c><00:00:54.719><c> package</c><00:00:55.079><c> this</c><00:00:55.379><c> up</c><00:00:55.620><c> so</c><00:00:55.980><c> if</c><00:00:56.100><c> you</c><00:00:56.219><c> look</c>

00:00:56.330 --> 00:00:56.340 align:start position:0%
going to package this up so if you look
 

00:00:56.340 --> 00:00:58.430 align:start position:0%
going to package this up so if you look
at<00:00:56.460><c> just</c><00:00:56.640><c> doing</c><00:00:56.820><c> a</c><00:00:57.059><c> standard</c><00:00:57.420><c> chat</c><00:00:57.840><c> with</c><00:00:58.199><c> this</c>

00:00:58.430 --> 00:00:58.440 align:start position:0%
at just doing a standard chat with this
 

00:00:58.440 --> 00:01:01.189 align:start position:0%
at just doing a standard chat with this
we<00:00:58.980><c> just</c><00:00:59.219><c> pass</c><00:00:59.399><c> in</c><00:00:59.579><c> a</c><00:00:59.820><c> human</c><00:00:59.940><c> message</c><00:01:00.300><c> and</c><00:01:00.960><c> that</c>

00:01:01.189 --> 00:01:01.199 align:start position:0%
we just pass in a human message and that
 

00:01:01.199 --> 00:01:02.990 align:start position:0%
we just pass in a human message and that
probably<00:01:01.320><c> has</c><00:01:01.559><c> a</c><00:01:01.739><c> system</c><00:01:01.920><c> message</c><00:01:02.280><c> on</c><00:01:02.699><c> top</c><00:01:02.820><c> of</c>

00:01:02.990 --> 00:01:03.000 align:start position:0%
probably has a system message on top of
 

00:01:03.000 --> 00:01:04.490 align:start position:0%
probably has a system message on top of
that<00:01:03.120><c> but</c><00:01:03.300><c> just</c><00:01:03.480><c> doing</c><00:01:03.719><c> this</c><00:01:04.019><c> this</c><00:01:04.260><c> is</c><00:01:04.440><c> an</c>

00:01:04.490 --> 00:01:04.500 align:start position:0%
that but just doing this this is an
 

00:01:04.500 --> 00:01:06.469 align:start position:0%
that but just doing this this is an
example<00:01:04.799><c> from</c><00:01:05.040><c> their</c><00:01:05.220><c> docs</c><00:01:05.580><c> we're</c><00:01:05.880><c> passing</c><00:01:06.240><c> a</c>

00:01:06.469 --> 00:01:06.479 align:start position:0%
example from their docs we're passing a
 

00:01:06.479 --> 00:01:07.850 align:start position:0%
example from their docs we're passing a
human<00:01:06.600><c> message</c><00:01:06.900><c> and</c><00:01:07.200><c> you</c><00:01:07.380><c> can</c><00:01:07.439><c> see</c><00:01:07.560><c> it</c><00:01:07.680><c> does</c><00:01:07.799><c> it</c>

00:01:07.850 --> 00:01:07.860 align:start position:0%
human message and you can see it does it
 

00:01:07.860 --> 00:01:10.130 align:start position:0%
human message and you can see it does it
quite<00:01:07.979><c> nicely</c><00:01:08.299><c> so</c><00:01:09.299><c> just</c><00:01:09.540><c> doing</c><00:01:09.720><c> a</c><00:01:10.020><c> normal</c>

00:01:10.130 --> 00:01:10.140 align:start position:0%
quite nicely so just doing a normal
 

00:01:10.140 --> 00:01:13.190 align:start position:0%
quite nicely so just doing a normal
summary<00:01:10.680><c> the</c><00:01:11.159><c> way</c><00:01:11.280><c> I've</c><00:01:11.460><c> done</c><00:01:11.760><c> it</c><00:01:11.939><c> is</c><00:01:12.360><c> that</c><00:01:12.720><c> you</c>

00:01:13.190 --> 00:01:13.200 align:start position:0%
summary the way I've done it is that you
 

00:01:13.200 --> 00:01:15.890 align:start position:0%
summary the way I've done it is that you
use<00:01:13.380><c> the</c><00:01:13.799><c> system</c><00:01:14.040><c> message</c><00:01:14.580><c> to</c><00:01:15.240><c> set</c><00:01:15.420><c> up</c><00:01:15.600><c> the</c>

00:01:15.890 --> 00:01:15.900 align:start position:0%
use the system message to set up the
 

00:01:15.900 --> 00:01:17.870 align:start position:0%
use the system message to set up the
context<00:01:16.320><c> of</c><00:01:16.799><c> what</c><00:01:17.040><c> the</c><00:01:17.159><c> whole</c><00:01:17.340><c> thing</c><00:01:17.460><c> is</c><00:01:17.640><c> about</c>

00:01:17.870 --> 00:01:17.880 align:start position:0%
context of what the whole thing is about
 

00:01:17.880 --> 00:01:20.810 align:start position:0%
context of what the whole thing is about
so<00:01:18.600><c> in</c><00:01:18.900><c> this</c><00:01:19.020><c> case</c><00:01:19.200><c> the</c><00:01:19.619><c> system</c><00:01:19.799><c> message</c><00:01:20.220><c> is</c>

00:01:20.810 --> 00:01:20.820 align:start position:0%
so in this case the system message is
 

00:01:20.820 --> 00:01:22.730 align:start position:0%
so in this case the system message is
you're<00:01:21.119><c> an</c><00:01:21.360><c> expert</c><00:01:21.659><c> at</c><00:01:22.020><c> making</c><00:01:22.320><c> strong</c>

00:01:22.730 --> 00:01:22.740 align:start position:0%
you're an expert at making strong
 

00:01:22.740 --> 00:01:25.609 align:start position:0%
you're an expert at making strong
factual<00:01:23.280><c> summarizations</c><00:01:24.180><c> take</c><00:01:25.020><c> the</c><00:01:25.259><c> article</c>

00:01:25.609 --> 00:01:25.619 align:start position:0%
factual summarizations take the article
 

00:01:25.619 --> 00:01:27.530 align:start position:0%
factual summarizations take the article
submitted<00:01:26.159><c> by</c><00:01:26.460><c> the</c><00:01:26.640><c> user</c><00:01:26.939><c> and</c><00:01:27.060><c> produce</c><00:01:27.360><c> a</c>

00:01:27.530 --> 00:01:27.540 align:start position:0%
submitted by the user and produce a
 

00:01:27.540 --> 00:01:29.929 align:start position:0%
submitted by the user and produce a
factual<00:01:27.900><c> useful</c><00:01:28.380><c> summary</c><00:01:28.799><c> here</c><00:01:29.580><c> we're</c><00:01:29.759><c> not</c>

00:01:29.929 --> 00:01:29.939 align:start position:0%
factual useful summary here we're not
 

00:01:29.939 --> 00:01:31.910 align:start position:0%
factual useful summary here we're not
using<00:01:30.240><c> the</c><00:01:30.479><c> user</c><00:01:30.780><c> to</c><00:01:30.960><c> give</c><00:01:31.080><c> any</c><00:01:31.320><c> instructions</c>

00:01:31.910 --> 00:01:31.920 align:start position:0%
using the user to give any instructions
 

00:01:31.920 --> 00:01:34.190 align:start position:0%
using the user to give any instructions
the<00:01:32.340><c> user</c><00:01:32.640><c> is</c><00:01:32.759><c> just</c><00:01:32.939><c> going</c><00:01:33.119><c> to</c><00:01:33.240><c> be</c><00:01:33.299><c> putting</c><00:01:33.780><c> in</c>

00:01:34.190 --> 00:01:34.200 align:start position:0%
the user is just going to be putting in
 

00:01:34.200 --> 00:01:36.530 align:start position:0%
the user is just going to be putting in
the<00:01:34.500><c> actual</c><00:01:34.799><c> article</c><00:01:35.340><c> So</c><00:01:35.759><c> the</c><00:01:36.119><c> instructions</c>

00:01:36.530 --> 00:01:36.540 align:start position:0%
the actual article So the instructions
 

00:01:36.540 --> 00:01:38.390 align:start position:0%
the actual article So the instructions
are<00:01:36.720><c> coming</c><00:01:36.900><c> in</c><00:01:37.140><c> the</c><00:01:37.380><c> system</c><00:01:37.560><c> message</c><00:01:37.979><c> here</c>

00:01:38.390 --> 00:01:38.400 align:start position:0%
are coming in the system message here
 

00:01:38.400 --> 00:01:39.890 align:start position:0%
are coming in the system message here
you<00:01:38.759><c> can</c><00:01:38.880><c> think</c><00:01:39.000><c> of</c><00:01:39.180><c> these</c><00:01:39.479><c> as</c><00:01:39.720><c> like</c>

00:01:39.890 --> 00:01:39.900 align:start position:0%
you can think of these as like
 

00:01:39.900 --> 00:01:42.230 align:start position:0%
you can think of these as like
placeholder<00:01:40.680><c> tokens</c><00:01:41.159><c> that</c><00:01:41.640><c> tell</c><00:01:41.820><c> the</c><00:01:42.060><c> model</c>

00:01:42.230 --> 00:01:42.240 align:start position:0%
placeholder tokens that tell the model
 

00:01:42.240 --> 00:01:44.810 align:start position:0%
placeholder tokens that tell the model
different<00:01:42.840><c> kinds</c><00:01:43.320><c> of</c><00:01:43.380><c> information</c><00:01:43.680><c> so</c><00:01:44.400><c> a</c>

00:01:44.810 --> 00:01:44.820 align:start position:0%
different kinds of information so a
 

00:01:44.820 --> 00:01:47.330 align:start position:0%
different kinds of information so a
system<00:01:45.000><c> message</c><00:01:45.659><c> token</c><00:01:46.259><c> tells</c><00:01:46.740><c> it</c><00:01:46.860><c> setting</c><00:01:47.220><c> up</c>

00:01:47.330 --> 00:01:47.340 align:start position:0%
system message token tells it setting up
 

00:01:47.340 --> 00:01:49.490 align:start position:0%
system message token tells it setting up
the<00:01:47.520><c> context</c><00:01:47.820><c> setting</c><00:01:48.299><c> up</c><00:01:48.360><c> the</c><00:01:48.659><c> task</c><00:01:48.900><c> often</c>

00:01:49.490 --> 00:01:49.500 align:start position:0%
the context setting up the task often
 

00:01:49.500 --> 00:01:51.590 align:start position:0%
the context setting up the task often
that<00:01:49.799><c> kind</c><00:01:49.979><c> of</c><00:01:50.100><c> thing</c><00:01:50.220><c> and</c><00:01:50.759><c> then</c><00:01:50.939><c> the</c><00:01:51.420><c> human</c>

00:01:51.590 --> 00:01:51.600 align:start position:0%
that kind of thing and then the human
 

00:01:51.600 --> 00:01:53.450 align:start position:0%
that kind of thing and then the human
message<00:01:51.899><c> is</c><00:01:52.259><c> what</c><00:01:52.560><c> you</c><00:01:52.740><c> would</c><00:01:52.860><c> expect</c><00:01:53.040><c> to</c><00:01:53.280><c> get</c>

00:01:53.450 --> 00:01:53.460 align:start position:0%
message is what you would expect to get
 

00:01:53.460 --> 00:01:55.850 align:start position:0%
message is what you would expect to get
from<00:01:53.759><c> the</c><00:01:53.939><c> user</c><00:01:54.299><c> so</c><00:01:54.780><c> we</c><00:01:55.140><c> take</c><00:01:55.320><c> this</c><00:01:55.560><c> we're</c>

00:01:55.850 --> 00:01:55.860 align:start position:0%
from the user so we take this we're
 

00:01:55.860 --> 00:01:58.490 align:start position:0%
from the user so we take this we're
passing<00:01:56.280><c> our</c><00:01:56.579><c> article</c><00:01:57.180><c> as</c><00:01:57.720><c> the</c><00:01:57.960><c> human</c><00:01:58.140><c> message</c>

00:01:58.490 --> 00:01:58.500 align:start position:0%
passing our article as the human message
 

00:01:58.500 --> 00:02:00.950 align:start position:0%
passing our article as the human message
so<00:01:59.040><c> we've</c><00:01:59.220><c> got</c><00:01:59.340><c> these</c><00:01:59.640><c> messages</c><00:01:59.880><c> this</c><00:02:00.540><c> list</c><00:02:00.720><c> of</c>

00:02:00.950 --> 00:02:00.960 align:start position:0%
so we've got these messages this list of
 

00:02:00.960 --> 00:02:02.810 align:start position:0%
so we've got these messages this list of
messages<00:02:01.200><c> and</c><00:02:01.799><c> if</c><00:02:01.979><c> we're</c><00:02:02.100><c> actually</c><00:02:02.340><c> doing</c><00:02:02.579><c> it</c>

00:02:02.810 --> 00:02:02.820 align:start position:0%
messages and if we're actually doing it
 

00:02:02.820 --> 00:02:05.749 align:start position:0%
messages and if we're actually doing it
in<00:02:03.180><c> chat</c><00:02:03.479><c> we</c><00:02:03.899><c> would</c><00:02:04.079><c> have</c><00:02:04.320><c> system</c><00:02:04.740><c> human</c><00:02:05.219><c> AI</c>

00:02:05.749 --> 00:02:05.759 align:start position:0%
in chat we would have system human AI
 

00:02:05.759 --> 00:02:08.809 align:start position:0%
in chat we would have system human AI
response<00:02:06.360><c> Etc</c><00:02:06.540><c> and</c><00:02:06.840><c> this</c><00:02:07.079><c> messages</c><00:02:07.560><c> list</c><00:02:08.280><c> can</c>

00:02:08.809 --> 00:02:08.819 align:start position:0%
response Etc and this messages list can
 

00:02:08.819 --> 00:02:11.150 align:start position:0%
response Etc and this messages list can
get<00:02:09.000><c> reasonably</c><00:02:09.539><c> long</c><00:02:09.780><c> doing</c><00:02:10.200><c> this</c><00:02:10.500><c> but</c><00:02:11.039><c> here</c>

00:02:11.150 --> 00:02:11.160 align:start position:0%
get reasonably long doing this but here
 

00:02:11.160 --> 00:02:12.530 align:start position:0%
get reasonably long doing this but here
because<00:02:11.459><c> we</c><00:02:11.760><c> really</c><00:02:11.940><c> just</c><00:02:12.120><c> want</c><00:02:12.239><c> to</c><00:02:12.360><c> translate</c>

00:02:12.530 --> 00:02:12.540 align:start position:0%
because we really just want to translate
 

00:02:12.540 --> 00:02:14.809 align:start position:0%
because we really just want to translate
one<00:02:13.020><c> thing</c><00:02:13.200><c> and</c><00:02:13.620><c> then</c><00:02:13.739><c> get</c><00:02:13.920><c> it</c><00:02:14.099><c> back</c><00:02:14.280><c> we're</c><00:02:14.700><c> not</c>

00:02:14.809 --> 00:02:14.819 align:start position:0%
one thing and then get it back we're not
 

00:02:14.819 --> 00:02:16.070 align:start position:0%
one thing and then get it back we're not
actually<00:02:15.000><c> setting</c><00:02:15.360><c> this</c><00:02:15.480><c> up</c><00:02:15.720><c> as</c>

00:02:16.070 --> 00:02:16.080 align:start position:0%
actually setting this up as
 

00:02:16.080 --> 00:02:18.050 align:start position:0%
actually setting this up as
conversational<00:02:16.800><c> chat</c><00:02:17.280><c> so</c><00:02:17.520><c> we</c><00:02:17.700><c> can</c><00:02:17.819><c> just</c><00:02:17.940><c> pass</c>

00:02:18.050 --> 00:02:18.060 align:start position:0%
conversational chat so we can just pass
 

00:02:18.060 --> 00:02:19.729 align:start position:0%
conversational chat so we can just pass
in<00:02:18.239><c> the</c><00:02:18.360><c> article</c><00:02:18.720><c> there</c><00:02:19.020><c> and</c><00:02:19.319><c> sure</c><00:02:19.440><c> enough</c><00:02:19.560><c> it</c>

00:02:19.729 --> 00:02:19.739 align:start position:0%
in the article there and sure enough it
 

00:02:19.739 --> 00:02:22.130 align:start position:0%
in the article there and sure enough it
gives<00:02:19.920><c> us</c><00:02:20.099><c> a</c><00:02:20.459><c> really</c><00:02:20.520><c> nice</c><00:02:20.819><c> summary</c><00:02:21.360><c> back</c><00:02:21.720><c> just</c>

00:02:22.130 --> 00:02:22.140 align:start position:0%
gives us a really nice summary back just
 

00:02:22.140 --> 00:02:25.490 align:start position:0%
gives us a really nice summary back just
from<00:02:22.319><c> doing</c><00:02:22.500><c> that</c><00:02:22.739><c> it's</c><00:02:23.220><c> treated</c><00:02:23.700><c> it</c><00:02:23.940><c> like</c><00:02:24.599><c> a</c><00:02:25.200><c> I</c>

00:02:25.490 --> 00:02:25.500 align:start position:0%
from doing that it's treated it like a I
 

00:02:25.500 --> 00:02:26.869 align:start position:0%
from doing that it's treated it like a I
guess<00:02:25.680><c> in</c><00:02:25.860><c> some</c><00:02:25.980><c> ways</c><00:02:26.220><c> you</c><00:02:26.340><c> could</c><00:02:26.459><c> think</c><00:02:26.580><c> of</c><00:02:26.760><c> it</c>

00:02:26.869 --> 00:02:26.879 align:start position:0%
guess in some ways you could think of it
 

00:02:26.879 --> 00:02:28.610 align:start position:0%
guess in some ways you could think of it
maybe<00:02:27.120><c> treating</c><00:02:27.540><c> it</c><00:02:27.720><c> like</c><00:02:27.959><c> a</c><00:02:28.200><c> conversation</c>

00:02:28.610 --> 00:02:28.620 align:start position:0%
maybe treating it like a conversation
 

00:02:28.620 --> 00:02:31.010 align:start position:0%
maybe treating it like a conversation
but<00:02:29.220><c> it's</c><00:02:29.520><c> eliminated</c><00:02:30.180><c> the</c><00:02:30.420><c> bits</c><00:02:30.720><c> about</c><00:02:30.840><c> the</c>

00:02:31.010 --> 00:02:31.020 align:start position:0%
but it's eliminated the bits about the
 

00:02:31.020 --> 00:02:33.110 align:start position:0%
but it's eliminated the bits about the
conversation<00:02:31.440><c> because</c><00:02:31.860><c> we've</c><00:02:32.400><c> set</c><00:02:32.640><c> the</c>

00:02:33.110 --> 00:02:33.120 align:start position:0%
conversation because we've set the
 

00:02:33.120 --> 00:02:35.570 align:start position:0%
conversation because we've set the
system<00:02:33.300><c> to</c><00:02:33.840><c> basically</c><00:02:34.260><c> just</c><00:02:34.920><c> take</c><00:02:35.220><c> the</c>

00:02:35.570 --> 00:02:35.580 align:start position:0%
system to basically just take the
 

00:02:35.580 --> 00:02:37.010 align:start position:0%
system to basically just take the
article<00:02:35.879><c> submitted</c><00:02:36.300><c> by</c><00:02:36.480><c> the</c><00:02:36.599><c> user</c><00:02:36.900><c> and</c>

00:02:37.010 --> 00:02:37.020 align:start position:0%
article submitted by the user and
 

00:02:37.020 --> 00:02:38.869 align:start position:0%
article submitted by the user and
produce<00:02:37.319><c> a</c><00:02:37.440><c> factual</c><00:02:37.800><c> summary</c><00:02:38.160><c> does</c><00:02:38.580><c> it</c><00:02:38.760><c> work</c>

00:02:38.869 --> 00:02:38.879 align:start position:0%
produce a factual summary does it work
 

00:02:38.879 --> 00:02:40.910 align:start position:0%
produce a factual summary does it work
for<00:02:39.060><c> the</c><00:02:39.239><c> bullet</c><00:02:39.540><c> lists</c><00:02:39.959><c> ones</c><00:02:40.319><c> yeah</c><00:02:40.560><c> you</c><00:02:40.800><c> can</c>

00:02:40.910 --> 00:02:40.920 align:start position:0%
for the bullet lists ones yeah you can
 

00:02:40.920 --> 00:02:43.430 align:start position:0%
for the bullet lists ones yeah you can
use<00:02:41.040><c> it</c><00:02:41.220><c> for</c><00:02:41.400><c> this</c><00:02:41.879><c> ones</c><00:02:42.239><c> again</c><00:02:42.480><c> here</c><00:02:43.140><c> you</c>

00:02:43.430 --> 00:02:43.440 align:start position:0%
use it for this ones again here you
 

00:02:43.440 --> 00:02:47.150 align:start position:0%
use it for this ones again here you
would<00:02:43.620><c> change</c><00:02:44.340><c> the</c><00:02:45.060><c> system</c><00:02:45.420><c> message</c><00:02:46.160><c> your</c>

00:02:47.150 --> 00:02:47.160 align:start position:0%
would change the system message your
 

00:02:47.160 --> 00:02:48.770 align:start position:0%
would change the system message your
human<00:02:47.400><c> message</c><00:02:47.640><c> is</c><00:02:47.879><c> going</c><00:02:48.060><c> to</c><00:02:48.120><c> be</c><00:02:48.180><c> the</c><00:02:48.420><c> article</c>

00:02:48.770 --> 00:02:48.780 align:start position:0%
human message is going to be the article
 

00:02:48.780 --> 00:02:50.690 align:start position:0%
human message is going to be the article
just<00:02:49.019><c> like</c><00:02:49.200><c> before</c><00:02:49.440><c> but</c><00:02:50.099><c> the</c><00:02:50.280><c> system</c><00:02:50.400><c> message</c>

00:02:50.690 --> 00:02:50.700 align:start position:0%
just like before but the system message
 

00:02:50.700 --> 00:02:52.430 align:start position:0%
just like before but the system message
here<00:02:50.940><c> is</c><00:02:51.120><c> you're</c><00:02:51.300><c> an</c><00:02:51.540><c> expert</c><00:02:51.959><c> at</c><00:02:52.200><c> making</c>

00:02:52.430 --> 00:02:52.440 align:start position:0%
here is you're an expert at making
 

00:02:52.440 --> 00:02:54.229 align:start position:0%
here is you're an expert at making
strong<00:02:52.739><c> factual</c><00:02:53.099><c> summarization</c><00:02:53.640><c> so</c><00:02:53.940><c> the</c><00:02:54.120><c> same</c>

00:02:54.229 --> 00:02:54.239 align:start position:0%
strong factual summarization so the same
 

00:02:54.239 --> 00:02:56.869 align:start position:0%
strong factual summarization so the same
as<00:02:54.420><c> before</c><00:02:54.599><c> but</c><00:02:55.200><c> now</c><00:02:55.379><c> extract</c><00:02:55.920><c> the</c><00:02:56.160><c> key</c><00:02:56.459><c> facts</c>

00:02:56.869 --> 00:02:56.879 align:start position:0%
as before but now extract the key facts
 

00:02:56.879 --> 00:02:59.030 align:start position:0%
as before but now extract the key facts
out<00:02:57.120><c> of</c><00:02:57.239><c> this</c><00:02:57.420><c> text</c><00:02:57.660><c> don't</c><00:02:58.200><c> include</c><00:02:58.560><c> opinions</c>

00:02:59.030 --> 00:02:59.040 align:start position:0%
out of this text don't include opinions
 

00:02:59.040 --> 00:03:01.610 align:start position:0%
out of this text don't include opinions
give<00:02:59.459><c> each</c><00:02:59.760><c> fact</c><00:03:00.000><c> a</c><00:03:00.420><c> number</c><00:03:00.599><c> and</c><00:03:01.200><c> keep</c><00:03:01.379><c> them</c>

00:03:01.610 --> 00:03:01.620 align:start position:0%
give each fact a number and keep them
 

00:03:01.620 --> 00:03:04.430 align:start position:0%
give each fact a number and keep them
short<00:03:02.099><c> sentences</c><00:03:02.760><c> and</c><00:03:03.480><c> we</c><00:03:03.660><c> can</c><00:03:03.780><c> see</c><00:03:03.959><c> that</c><00:03:04.200><c> it</c>

00:03:04.430 --> 00:03:04.440 align:start position:0%
short sentences and we can see that it
 

00:03:04.440 --> 00:03:07.250 align:start position:0%
short sentences and we can see that it
gives<00:03:04.620><c> back</c><00:03:05.220><c> pretty</c><00:03:05.760><c> nice</c><00:03:06.000><c> information</c><00:03:06.300><c> not</c>

00:03:07.250 --> 00:03:07.260 align:start position:0%
gives back pretty nice information not
 

00:03:07.260 --> 00:03:09.050 align:start position:0%
gives back pretty nice information not
that<00:03:07.440><c> different</c><00:03:07.620><c> not</c><00:03:07.920><c> hugely</c><00:03:08.220><c> different</c><00:03:08.760><c> than</c>

00:03:09.050 --> 00:03:09.060 align:start position:0%
that different not hugely different than
 

00:03:09.060 --> 00:03:11.089 align:start position:0%
that different not hugely different than
what<00:03:09.239><c> we</c><00:03:09.360><c> were</c><00:03:09.480><c> getting</c><00:03:09.599><c> before</c><00:03:09.840><c> the</c><00:03:10.800><c> thing</c><00:03:10.920><c> to</c>

00:03:11.089 --> 00:03:11.099 align:start position:0%
what we were getting before the thing to
 

00:03:11.099 --> 00:03:12.710 align:start position:0%
what we were getting before the thing to
think<00:03:11.280><c> about</c><00:03:11.400><c> here</c><00:03:11.700><c> though</c><00:03:11.940><c> is</c><00:03:12.239><c> that</c><00:03:12.420><c> each</c><00:03:12.599><c> of</c>

00:03:12.710 --> 00:03:12.720 align:start position:0%
think about here though is that each of
 

00:03:12.720 --> 00:03:14.390 align:start position:0%
think about here though is that each of
these<00:03:12.900><c> models</c><00:03:13.319><c> tensed</c><00:03:13.800><c> a</c><00:03:13.920><c> little</c><00:03:14.040><c> bit</c><00:03:14.220><c> of</c>

00:03:14.390 --> 00:03:14.400 align:start position:0%
these models tensed a little bit of
 

00:03:14.400 --> 00:03:17.869 align:start position:0%
these models tensed a little bit of
style<00:03:14.940><c> or</c><00:03:15.480><c> personality</c><00:03:16.140><c> to</c><00:03:16.500><c> itself</c><00:03:16.860><c> so</c><00:03:17.640><c> you</c>

00:03:17.869 --> 00:03:17.879 align:start position:0%
style or personality to itself so you
 

00:03:17.879 --> 00:03:19.369 align:start position:0%
style or personality to itself so you
want<00:03:17.940><c> to</c><00:03:18.120><c> play</c><00:03:18.239><c> around</c><00:03:18.420><c> with</c><00:03:18.780><c> prompt</c>

00:03:19.369 --> 00:03:19.379 align:start position:0%
want to play around with prompt
 

00:03:19.379 --> 00:03:20.630 align:start position:0%
want to play around with prompt
sometimes<00:03:19.860><c> you're</c><00:03:20.040><c> going</c><00:03:20.159><c> to</c><00:03:20.220><c> have</c><00:03:20.340><c> to</c><00:03:20.459><c> change</c>

00:03:20.630 --> 00:03:20.640 align:start position:0%
sometimes you're going to have to change
 

00:03:20.640 --> 00:03:23.270 align:start position:0%
sometimes you're going to have to change
the<00:03:20.940><c> prompt</c><00:03:21.300><c> for</c><00:03:21.599><c> the</c><00:03:21.900><c> kind</c><00:03:22.140><c> of</c><00:03:22.379><c> article</c><00:03:23.040><c> or</c>

00:03:23.270 --> 00:03:23.280 align:start position:0%
the prompt for the kind of article or
 

00:03:23.280 --> 00:03:25.550 align:start position:0%
the prompt for the kind of article or
the<00:03:23.459><c> kind</c><00:03:23.700><c> of</c><00:03:23.879><c> documentation</c><00:03:25.080><c> Etc</c><00:03:25.200><c> that</c>

00:03:25.550 --> 00:03:25.560 align:start position:0%
the kind of documentation Etc that
 

00:03:25.560 --> 00:03:27.949 align:start position:0%
the kind of documentation Etc that
you're<00:03:25.680><c> summarizing</c><00:03:26.340><c> so</c><00:03:27.120><c> that</c><00:03:27.420><c> works</c><00:03:27.780><c> what</c>

00:03:27.949 --> 00:03:27.959 align:start position:0%
you're summarizing so that works what
 

00:03:27.959 --> 00:03:30.530 align:start position:0%
you're summarizing so that works what
about<00:03:28.140><c> making</c><00:03:28.379><c> the</c><00:03:28.680><c> triples</c><00:03:29.159><c> so</c><00:03:30.060><c> normally</c>

00:03:30.530 --> 00:03:30.540 align:start position:0%
about making the triples so normally
 

00:03:30.540 --> 00:03:32.089 align:start position:0%
about making the triples so normally
what<00:03:30.840><c> I</c><00:03:30.959><c> was</c><00:03:31.019><c> doing</c><00:03:31.200><c> to</c><00:03:31.319><c> make</c><00:03:31.440><c> the</c><00:03:31.560><c> triples</c><00:03:31.860><c> was</c>

00:03:32.089 --> 00:03:32.099 align:start position:0%
what I was doing to make the triples was
 

00:03:32.099 --> 00:03:34.670 align:start position:0%
what I was doing to make the triples was
I<00:03:32.280><c> was</c><00:03:32.459><c> passing</c><00:03:32.819><c> in</c><00:03:32.940><c> these</c><00:03:33.360><c> facts</c><00:03:33.780><c> but</c><00:03:34.319><c> one</c><00:03:34.560><c> of</c>

00:03:34.670 --> 00:03:34.680 align:start position:0%
I was passing in these facts but one of
 

00:03:34.680 --> 00:03:36.710 align:start position:0%
I was passing in these facts but one of
the cool<00:03:34.860><c> things</c><00:03:34.980><c> with</c><00:03:35.280><c> this</c><00:03:35.640><c> is</c><00:03:36.180><c> that</c><00:03:36.360><c> we</c><00:03:36.540><c> can</c>

00:03:36.710 --> 00:03:36.720 align:start position:0%
the cool things with this is that we can
 

00:03:36.720 --> 00:03:38.809 align:start position:0%
the cool things with this is that we can
actually<00:03:36.840><c> just</c><00:03:37.080><c> pass</c><00:03:37.440><c> in</c><00:03:37.680><c> so</c><00:03:38.099><c> we</c><00:03:38.340><c> can</c><00:03:38.640><c> do</c><00:03:38.760><c> it</c>

00:03:38.809 --> 00:03:38.819 align:start position:0%
actually just pass in so we can do it
 

00:03:38.819 --> 00:03:40.729 align:start position:0%
actually just pass in so we can do it
like<00:03:39.000><c> that</c><00:03:39.180><c> but</c><00:03:39.599><c> we</c><00:03:39.780><c> can</c><00:03:39.840><c> also</c><00:03:40.080><c> just</c><00:03:40.260><c> pass</c><00:03:40.560><c> in</c>

00:03:40.729 --> 00:03:40.739 align:start position:0%
like that but we can also just pass in
 

00:03:40.739 --> 00:03:42.949 align:start position:0%
like that but we can also just pass in
the<00:03:40.920><c> article</c><00:03:41.340><c> Itself</c><00:03:41.700><c> by</c><00:03:42.299><c> passing</c><00:03:42.659><c> in</c><00:03:42.780><c> the</c>

00:03:42.949 --> 00:03:42.959 align:start position:0%
the article Itself by passing in the
 

00:03:42.959 --> 00:03:45.229 align:start position:0%
the article Itself by passing in the
article<00:03:43.319><c> itself</c><00:03:43.739><c> we</c><00:03:44.280><c> can</c><00:03:44.459><c> get</c><00:03:44.580><c> triples</c><00:03:45.000><c> and</c>

00:03:45.229 --> 00:03:45.239 align:start position:0%
article itself we can get triples and
 

00:03:45.239 --> 00:03:47.149 align:start position:0%
article itself we can get triples and
this<00:03:45.540><c> is</c><00:03:45.659><c> like</c><00:03:45.840><c> doing</c><00:03:46.019><c> it</c><00:03:46.260><c> the</c><00:03:46.500><c> old</c><00:03:46.680><c> way</c><00:03:46.860><c> with</c>

00:03:47.149 --> 00:03:47.159 align:start position:0%
this is like doing it the old way with
 

00:03:47.159 --> 00:03:49.369 align:start position:0%
this is like doing it the old way with
this<00:03:47.580><c> is</c><00:03:47.640><c> doing</c><00:03:47.819><c> one</c><00:03:48.060><c> way</c><00:03:48.239><c> so</c><00:03:48.840><c> here</c><00:03:49.080><c> we're</c>

00:03:49.369 --> 00:03:49.379 align:start position:0%
this is doing one way so here we're
 

00:03:49.379 --> 00:03:51.470 align:start position:0%
this is doing one way so here we're
basically<00:03:49.799><c> just</c><00:03:50.280><c> passing</c><00:03:50.640><c> these</c><00:03:50.879><c> in</c><00:03:51.000><c> but</c><00:03:51.299><c> we</c>

00:03:51.470 --> 00:03:51.480 align:start position:0%
basically just passing these in but we
 

00:03:51.480 --> 00:03:53.750 align:start position:0%
basically just passing these in but we
can<00:03:51.599><c> also</c><00:03:51.840><c> do</c><00:03:52.080><c> it</c><00:03:52.200><c> like</c><00:03:52.560><c> this</c><00:03:52.799><c> where</c><00:03:53.220><c> we've</c><00:03:53.580><c> got</c>

00:03:53.750 --> 00:03:53.760 align:start position:0%
can also do it like this where we've got
 

00:03:53.760 --> 00:03:54.949 align:start position:0%
can also do it like this where we've got
so<00:03:54.000><c> actually</c><00:03:54.120><c> sorry</c><00:03:54.360><c> that</c><00:03:54.599><c> was</c><00:03:54.780><c> just</c>

00:03:54.949 --> 00:03:54.959 align:start position:0%
so actually sorry that was just
 

00:03:54.959 --> 00:03:57.229 align:start position:0%
so actually sorry that was just
extracting<00:03:55.500><c> the</c><00:03:55.739><c> the</c><00:03:55.980><c> facts</c><00:03:56.459><c> out</c><00:03:56.640><c> there</c><00:03:56.879><c> and</c>

00:03:57.229 --> 00:03:57.239 align:start position:0%
extracting the the facts out there and
 

00:03:57.239 --> 00:03:58.729 align:start position:0%
extracting the the facts out there and
what<00:03:57.360><c> I</c><00:03:57.480><c> was</c><00:03:57.599><c> playing</c><00:03:57.720><c> around</c><00:03:57.900><c> with</c><00:03:58.140><c> was</c><00:03:58.500><c> that</c>

00:03:58.729 --> 00:03:58.739 align:start position:0%
what I was playing around with was that
 

00:03:58.739 --> 00:04:01.070 align:start position:0%
what I was playing around with was that
you<00:03:58.980><c> could</c><00:03:59.099><c> pass</c><00:03:59.400><c> this</c><00:03:59.760><c> in</c><00:04:00.000><c> but</c><00:04:00.599><c> actually</c><00:04:00.780><c> with</c>

00:04:01.070 --> 00:04:01.080 align:start position:0%
you could pass this in but actually with
 

00:04:01.080 --> 00:04:02.930 align:start position:0%
you could pass this in but actually with
the<00:04:01.260><c> chat</c><00:04:01.440><c> gbt</c><00:04:02.040><c> it</c><00:04:02.220><c> seems</c><00:04:02.519><c> to</c><00:04:02.580><c> get</c><00:04:02.700><c> better</c>

00:04:02.930 --> 00:04:02.940 align:start position:0%
the chat gbt it seems to get better
 

00:04:02.940 --> 00:04:05.089 align:start position:0%
the chat gbt it seems to get better
response<00:04:03.780><c> if</c><00:04:04.140><c> you</c><00:04:04.260><c> actually</c><00:04:04.440><c> just</c><00:04:04.680><c> pass</c><00:04:04.920><c> in</c>

00:04:05.089 --> 00:04:05.099 align:start position:0%
response if you actually just pass in
 

00:04:05.099 --> 00:04:07.789 align:start position:0%
response if you actually just pass in
the<00:04:05.340><c> full</c><00:04:05.519><c> article</c><00:04:06.060><c> so</c><00:04:06.720><c> rather</c><00:04:07.019><c> than</c><00:04:07.319><c> pass</c><00:04:07.620><c> the</c>

00:04:07.789 --> 00:04:07.799 align:start position:0%
the full article so rather than pass the
 

00:04:07.799 --> 00:04:09.949 align:start position:0%
the full article so rather than pass the
list<00:04:07.920><c> of</c><00:04:08.159><c> facts</c><00:04:08.940><c> which</c><00:04:09.060><c> would</c><00:04:09.239><c> you</c><00:04:09.540><c> can</c><00:04:09.659><c> play</c>

00:04:09.949 --> 00:04:09.959 align:start position:0%
list of facts which would you can play
 

00:04:09.959 --> 00:04:11.570 align:start position:0%
list of facts which would you can play
with<00:04:10.140><c> this</c><00:04:10.319><c> you're</c><00:04:10.560><c> just</c><00:04:10.860><c> passing</c><00:04:11.280><c> the</c>

00:04:11.570 --> 00:04:11.580 align:start position:0%
with this you're just passing the
 

00:04:11.580 --> 00:04:14.270 align:start position:0%
with this you're just passing the
responses<00:04:12.120><c> dot</c><00:04:12.480><c> content</c><00:04:12.780><c> in</c><00:04:13.140><c> here</c><00:04:13.379><c> but</c><00:04:14.099><c> what</c>

00:04:14.270 --> 00:04:14.280 align:start position:0%
responses dot content in here but what
 

00:04:14.280 --> 00:04:15.949 align:start position:0%
responses dot content in here but what
I've<00:04:14.400><c> passed</c><00:04:14.700><c> in</c><00:04:14.760><c> is</c><00:04:14.939><c> the</c><00:04:15.060><c> whole</c><00:04:15.180><c> article</c><00:04:15.599><c> and</c>

00:04:15.949 --> 00:04:15.959 align:start position:0%
I've passed in is the whole article and
 

00:04:15.959 --> 00:04:17.509 align:start position:0%
I've passed in is the whole article and
you<00:04:16.139><c> can</c><00:04:16.199><c> see</c><00:04:16.320><c> that</c><00:04:16.440><c> it's</c><00:04:16.620><c> giving</c><00:04:16.859><c> us</c><00:04:17.100><c> number</c>

00:04:17.509 --> 00:04:17.519 align:start position:0%
you can see that it's giving us number
 

00:04:17.519 --> 00:04:19.370 align:start position:0%
you can see that it's giving us number
for<00:04:17.820><c> each</c><00:04:18.060><c> triple</c><00:04:18.540><c> it's</c><00:04:18.780><c> given</c><00:04:19.139><c> us</c><00:04:19.260><c> the</c>

00:04:19.370 --> 00:04:19.380 align:start position:0%
for each triple it's given us the
 

00:04:19.380 --> 00:04:21.650 align:start position:0%
for each triple it's given us the
subject<00:04:19.680><c> to</c><00:04:20.040><c> predicate</c><00:04:20.400><c> object</c><00:04:20.880><c> for</c><00:04:21.299><c> each</c><00:04:21.479><c> of</c>

00:04:21.650 --> 00:04:21.660 align:start position:0%
subject to predicate object for each of
 

00:04:21.660 --> 00:04:23.930 align:start position:0%
subject to predicate object for each of
these<00:04:21.900><c> so</c><00:04:22.380><c> it</c><00:04:22.560><c> would</c><00:04:22.740><c> be</c><00:04:22.919><c> pretty</c><00:04:23.040><c> easy</c><00:04:23.280><c> to</c><00:04:23.699><c> even</c>

00:04:23.930 --> 00:04:23.940 align:start position:0%
these so it would be pretty easy to even
 

00:04:23.940 --> 00:04:25.730 align:start position:0%
these so it would be pretty easy to even
apps<00:04:24.360><c> chat</c><00:04:24.660><c> should</c><00:04:24.780><c> be</c><00:04:24.960><c> T</c><00:04:25.139><c> to</c><00:04:25.320><c> write</c><00:04:25.440><c> a</c><00:04:25.560><c> piece</c>

00:04:25.730 --> 00:04:25.740 align:start position:0%
apps chat should be T to write a piece
 

00:04:25.740 --> 00:04:27.890 align:start position:0%
apps chat should be T to write a piece
and<00:04:25.860><c> python</c><00:04:26.220><c> code</c><00:04:26.460><c> that</c><00:04:26.820><c> would</c><00:04:27.120><c> extract</c><00:04:27.479><c> these</c>

00:04:27.890 --> 00:04:27.900 align:start position:0%
and python code that would extract these
 

00:04:27.900 --> 00:04:30.170 align:start position:0%
and python code that would extract these
out<00:04:28.080><c> into</c><00:04:28.740><c> whatever</c><00:04:29.040><c> format</c><00:04:29.580><c> you</c><00:04:29.820><c> wanted</c><00:04:29.940><c> to</c>

00:04:30.170 --> 00:04:30.180 align:start position:0%
out into whatever format you wanted to
 

00:04:30.180 --> 00:04:31.969 align:start position:0%
out into whatever format you wanted to
use<00:04:30.360><c> in</c><00:04:30.600><c> your</c><00:04:30.840><c> knowledge</c><00:04:31.080><c> graph</c><00:04:31.380><c> once</c><00:04:31.800><c> we've</c>

00:04:31.969 --> 00:04:31.979 align:start position:0%
use in your knowledge graph once we've
 

00:04:31.979 --> 00:04:33.650 align:start position:0%
use in your knowledge graph once we've
got<00:04:32.100><c> them</c><00:04:32.220><c> in</c><00:04:32.520><c> this</c><00:04:32.639><c> kind</c><00:04:32.759><c> of</c><00:04:32.940><c> format</c><00:04:33.240><c> it</c><00:04:33.479><c> makes</c>

00:04:33.650 --> 00:04:33.660 align:start position:0%
got them in this kind of format it makes
 

00:04:33.660 --> 00:04:35.810 align:start position:0%
got them in this kind of format it makes
it<00:04:33.780><c> very</c><00:04:33.960><c> easy</c><00:04:34.199><c> to</c><00:04:34.500><c> do</c><00:04:34.620><c> and</c><00:04:34.979><c> looking</c><00:04:35.160><c> at</c><00:04:35.400><c> it</c><00:04:35.580><c> it</c>

00:04:35.810 --> 00:04:35.820 align:start position:0%
it very easy to do and looking at it it
 

00:04:35.820 --> 00:04:38.510 align:start position:0%
it very easy to do and looking at it it
seems<00:04:36.300><c> to</c><00:04:36.419><c> have</c><00:04:36.540><c> done</c><00:04:36.840><c> quite</c><00:04:37.380><c> a</c><00:04:37.560><c> nice</c><00:04:37.800><c> job</c><00:04:38.100><c> of</c>

00:04:38.510 --> 00:04:38.520 align:start position:0%
seems to have done quite a nice job of
 

00:04:38.520 --> 00:04:41.030 align:start position:0%
seems to have done quite a nice job of
doing<00:04:38.759><c> it</c><00:04:39.060><c> like</c><00:04:39.479><c> this</c><00:04:39.840><c> which</c><00:04:40.380><c> seems</c><00:04:40.800><c> different</c>

00:04:41.030 --> 00:04:41.040 align:start position:0%
doing it like this which seems different
 

00:04:41.040 --> 00:04:43.430 align:start position:0%
doing it like this which seems different
than<00:04:41.520><c> the</c><00:04:41.880><c> previous</c><00:04:42.000><c> one</c><00:04:42.360><c> was</c><00:04:42.660><c> doing</c><00:04:42.840><c> it's</c><00:04:43.199><c> cut</c>

00:04:43.430 --> 00:04:43.440 align:start position:0%
than the previous one was doing it's cut
 

00:04:43.440 --> 00:04:44.749 align:start position:0%
than the previous one was doing it's cut
off<00:04:43.560><c> a</c><00:04:43.740><c> little</c><00:04:43.800><c> bit</c><00:04:43.919><c> at</c><00:04:44.040><c> the</c><00:04:44.160><c> end</c><00:04:44.280><c> this</c><00:04:44.520><c> because</c>

00:04:44.749 --> 00:04:44.759 align:start position:0%
off a little bit at the end this because
 

00:04:44.759 --> 00:04:46.490 align:start position:0%
off a little bit at the end this because
of<00:04:45.000><c> the</c><00:04:45.120><c> number</c><00:04:45.300><c> of</c><00:04:45.479><c> Max</c><00:04:45.660><c> tokens</c><00:04:46.199><c> that</c><00:04:46.380><c> I've</c>

00:04:46.490 --> 00:04:46.500 align:start position:0%
of the number of Max tokens that I've
 

00:04:46.500 --> 00:04:48.469 align:start position:0%
of the number of Max tokens that I've
set<00:04:46.740><c> so</c><00:04:47.400><c> that's</c><00:04:47.639><c> something</c><00:04:47.880><c> that</c><00:04:48.240><c> you</c><00:04:48.419><c> could</c>

00:04:48.469 --> 00:04:48.479 align:start position:0%
set so that's something that you could
 

00:04:48.479 --> 00:04:49.790 align:start position:0%
set so that's something that you could
play<00:04:48.720><c> around</c><00:04:48.900><c> with</c>

00:04:49.790 --> 00:04:49.800 align:start position:0%
play around with
 

00:04:49.800 --> 00:04:52.070 align:start position:0%
play around with
so<00:04:50.520><c> as</c><00:04:50.880><c> always</c><00:04:51.000><c> if</c><00:04:51.240><c> you've</c><00:04:51.479><c> got</c><00:04:51.600><c> any</c><00:04:51.780><c> questions</c>

00:04:52.070 --> 00:04:52.080 align:start position:0%
so as always if you've got any questions
 

00:04:52.080 --> 00:04:54.110 align:start position:0%
so as always if you've got any questions
please<00:04:52.560><c> put</c><00:04:52.800><c> them</c><00:04:52.919><c> in</c><00:04:53.100><c> the</c><00:04:53.220><c> comments</c><00:04:53.520><c> if</c><00:04:53.940><c> this</c>

00:04:54.110 --> 00:04:54.120 align:start position:0%
please put them in the comments if this
 

00:04:54.120 --> 00:04:55.969 align:start position:0%
please put them in the comments if this
was<00:04:54.300><c> useful</c><00:04:54.540><c> to</c><00:04:54.780><c> you</c><00:04:54.960><c> please</c><00:04:55.440><c> click</c><00:04:55.860><c> and</c>

00:04:55.969 --> 00:04:55.979 align:start position:0%
was useful to you please click and
 

00:04:55.979 --> 00:04:58.189 align:start position:0%
was useful to you please click and
subscribe<00:04:56.580><c> and</c><00:04:57.120><c> I'll</c><00:04:57.300><c> see</c><00:04:57.540><c> you</c><00:04:57.660><c> in</c><00:04:57.900><c> the</c><00:04:58.020><c> next</c>

00:04:58.189 --> 00:04:58.199 align:start position:0%
subscribe and I'll see you in the next
 

00:04:58.199 --> 00:05:00.440 align:start position:0%
subscribe and I'll see you in the next
video

