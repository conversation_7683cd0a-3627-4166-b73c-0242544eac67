WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.570 align:start position:0%
 
alright<00:00:00.659><c> in</c><00:00:01.079><c> this</c><00:00:01.260><c> video</c><00:00:01.500><c> we're</c><00:00:02.040><c> going</c><00:00:02.280><c> to</c>

00:00:02.570 --> 00:00:02.580 align:start position:0%
alright in this video we're going to
 

00:00:02.580 --> 00:00:04.789 align:start position:0%
alright in this video we're going to
continue<00:00:02.879><c> looking</c><00:00:03.419><c> at</c><00:00:03.959><c> the</c><00:00:04.259><c> multi-dock</c>

00:00:04.789 --> 00:00:04.799 align:start position:0%
continue looking at the multi-dock
 

00:00:04.799 --> 00:00:05.870 align:start position:0%
continue looking at the multi-dock
retriever

00:00:05.870 --> 00:00:05.880 align:start position:0%
retriever
 

00:00:05.880 --> 00:00:07.789 align:start position:0%
retriever
we're<00:00:06.359><c> still</c><00:00:06.540><c> going</c><00:00:06.660><c> to</c><00:00:06.720><c> be</c><00:00:06.779><c> using</c><00:00:07.020><c> chroma</c><00:00:07.379><c> DB</c>

00:00:07.789 --> 00:00:07.799 align:start position:0%
we're still going to be using chroma DB
 

00:00:07.799 --> 00:00:10.970 align:start position:0%
we're still going to be using chroma DB
for<00:00:08.700><c> our</c><00:00:09.300><c> database</c><00:00:09.960><c> for</c><00:00:10.260><c> our</c><00:00:10.440><c> Vector</c><00:00:10.800><c> store</c>

00:00:10.970 --> 00:00:10.980 align:start position:0%
for our database for our Vector store
 

00:00:10.980 --> 00:00:12.589 align:start position:0%
for our database for our Vector store
but<00:00:11.700><c> the</c><00:00:11.820><c> big</c><00:00:11.940><c> thing</c><00:00:12.059><c> that</c><00:00:12.240><c> we're</c><00:00:12.360><c> going</c><00:00:12.540><c> to</c>

00:00:12.589 --> 00:00:12.599 align:start position:0%
but the big thing that we're going to
 

00:00:12.599 --> 00:00:14.650 align:start position:0%
but the big thing that we're going to
add<00:00:12.719><c> in</c><00:00:12.900><c> this</c><00:00:13.139><c> one</c><00:00:13.259><c> is</c><00:00:13.559><c> we're</c><00:00:13.740><c> going</c><00:00:13.860><c> to</c><00:00:13.980><c> add</c><00:00:14.160><c> in</c>

00:00:14.650 --> 00:00:14.660 align:start position:0%
add in this one is we're going to add in
 

00:00:14.660 --> 00:00:16.910 align:start position:0%
add in this one is we're going to add in
embeddings<00:00:15.660><c> that</c><00:00:16.320><c> are</c><00:00:16.560><c> actually</c><00:00:16.619><c> running</c>

00:00:16.910 --> 00:00:16.920 align:start position:0%
embeddings that are actually running
 

00:00:16.920 --> 00:00:19.849 align:start position:0%
embeddings that are actually running
locally<00:00:17.520><c> so</c><00:00:18.359><c> to</c><00:00:18.539><c> do</c><00:00:18.720><c> this</c><00:00:18.900><c> first</c><00:00:19.260><c> off</c><00:00:19.440><c> we</c><00:00:19.680><c> need</c>

00:00:19.849 --> 00:00:19.859 align:start position:0%
locally so to do this first off we need
 

00:00:19.859 --> 00:00:22.189 align:start position:0%
locally so to do this first off we need
to<00:00:19.920><c> have</c><00:00:20.100><c> a</c><00:00:20.279><c> GPU</c><00:00:20.760><c> or</c><00:00:21.240><c> it's</c><00:00:21.420><c> ideal</c><00:00:21.779><c> to</c><00:00:22.020><c> have</c><00:00:22.140><c> a</c>

00:00:22.189 --> 00:00:22.199 align:start position:0%
to have a GPU or it's ideal to have a
 

00:00:22.199 --> 00:00:25.070 align:start position:0%
to have a GPU or it's ideal to have a
GPU<00:00:22.619><c> running</c><00:00:22.920><c> so</c><00:00:23.340><c> I've</c><00:00:23.580><c> got</c><00:00:23.820><c> just</c><00:00:24.119><c> a</c><00:00:24.300><c> T4</c><00:00:24.720><c> here</c>

00:00:25.070 --> 00:00:25.080 align:start position:0%
GPU running so I've got just a T4 here
 

00:00:25.080 --> 00:00:27.830 align:start position:0%
GPU running so I've got just a T4 here
not<00:00:25.680><c> using</c><00:00:25.980><c> a</c><00:00:26.100><c> super</c><00:00:26.220><c> powerful</c><00:00:26.699><c> GPU</c><00:00:27.240><c> you</c><00:00:27.720><c> could</c>

00:00:27.830 --> 00:00:27.840 align:start position:0%
not using a super powerful GPU you could
 

00:00:27.840 --> 00:00:29.750 align:start position:0%
not using a super powerful GPU you could
run<00:00:28.019><c> this</c><00:00:28.260><c> on</c><00:00:28.439><c> the</c><00:00:28.560><c> CPU</c><00:00:29.039><c> it's</c><00:00:29.340><c> just</c><00:00:29.519><c> going</c><00:00:29.699><c> to</c>

00:00:29.750 --> 00:00:29.760 align:start position:0%
run this on the CPU it's just going to
 

00:00:29.760 --> 00:00:33.650 align:start position:0%
run this on the CPU it's just going to
take<00:00:30.000><c> a</c><00:00:30.180><c> fair</c><00:00:30.660><c> bit</c><00:00:30.900><c> more</c><00:00:31.140><c> time</c><00:00:31.439><c> to</c><00:00:32.340><c> do</c><00:00:32.520><c> this</c><00:00:32.759><c> so</c>

00:00:33.650 --> 00:00:33.660 align:start position:0%
take a fair bit more time to do this so
 

00:00:33.660 --> 00:00:35.450 align:start position:0%
take a fair bit more time to do this so
that<00:00:33.899><c> you'll</c><00:00:34.140><c> see</c><00:00:34.260><c> that</c><00:00:34.500><c> I'm</c><00:00:34.739><c> bringing</c><00:00:35.100><c> in</c><00:00:35.219><c> the</c>

00:00:35.450 --> 00:00:35.460 align:start position:0%
that you'll see that I'm bringing in the
 

00:00:35.460 --> 00:00:38.389 align:start position:0%
that you'll see that I'm bringing in the
same<00:00:35.640><c> stuff</c><00:00:35.940><c> we</c><00:00:36.480><c> actually</c><00:00:36.660><c> don't</c><00:00:37.320><c> need</c>

00:00:38.389 --> 00:00:38.399 align:start position:0%
same stuff we actually don't need
 

00:00:38.399 --> 00:00:41.510 align:start position:0%
same stuff we actually don't need
at<00:00:39.120><c> anymore</c><00:00:39.559><c> that's</c><00:00:40.559><c> the</c><00:00:40.800><c> two</c><00:00:40.920><c> new</c><00:00:41.100><c> ones</c><00:00:41.280><c> we're</c>

00:00:41.510 --> 00:00:41.520 align:start position:0%
at anymore that's the two new ones we're
 

00:00:41.520 --> 00:00:43.490 align:start position:0%
at anymore that's the two new ones we're
going<00:00:41.700><c> to</c><00:00:41.760><c> bring</c><00:00:41.940><c> in</c><00:00:42.120><c> the</c><00:00:43.020><c> instructor</c>

00:00:43.490 --> 00:00:43.500 align:start position:0%
going to bring in the instructor
 

00:00:43.500 --> 00:00:45.950 align:start position:0%
going to bring in the instructor
embedding<00:00:44.280><c> which</c><00:00:44.879><c> I'll</c><00:00:45.059><c> talk</c><00:00:45.239><c> about</c><00:00:45.360><c> in</c><00:00:45.660><c> a</c><00:00:45.780><c> sec</c>

00:00:45.950 --> 00:00:45.960 align:start position:0%
embedding which I'll talk about in a sec
 

00:00:45.960 --> 00:00:48.830 align:start position:0%
embedding which I'll talk about in a sec
and<00:00:46.440><c> basically</c><00:00:46.800><c> just</c><00:00:47.340><c> the</c><00:00:47.640><c> hugging</c><00:00:48.120><c> face</c><00:00:48.300><c> for</c>

00:00:48.830 --> 00:00:48.840 align:start position:0%
and basically just the hugging face for
 

00:00:48.840 --> 00:00:51.350 align:start position:0%
and basically just the hugging face for
using<00:00:49.260><c> here</c><00:00:49.739><c> so</c><00:00:50.460><c> another</c><00:00:50.579><c> difference</c><00:00:50.879><c> I</c><00:00:51.180><c> made</c>

00:00:51.350 --> 00:00:51.360 align:start position:0%
using here so another difference I made
 

00:00:51.360 --> 00:00:53.510 align:start position:0%
using here so another difference I made
in<00:00:51.539><c> this</c><00:00:51.719><c> one</c><00:00:51.899><c> is</c><00:00:52.320><c> a</c><00:00:52.800><c> lot</c><00:00:52.980><c> of</c><00:00:53.039><c> people</c><00:00:53.160><c> asking</c>

00:00:53.510 --> 00:00:53.520 align:start position:0%
in this one is a lot of people asking
 

00:00:53.520 --> 00:00:56.569 align:start position:0%
in this one is a lot of people asking
about<00:00:53.700><c> PDF</c><00:00:54.239><c> files</c><00:00:54.600><c> multiple</c><00:00:54.960><c> PDF</c><00:00:55.440><c> files</c><00:00:55.860><c> so</c><00:00:56.399><c> I</c>

00:00:56.569 --> 00:00:56.579 align:start position:0%
about PDF files multiple PDF files so I
 

00:00:56.579 --> 00:00:59.389 align:start position:0%
about PDF files multiple PDF files so I
swapped<00:00:56.940><c> out</c><00:00:57.059><c> the</c><00:00:57.480><c> text</c><00:00:57.719><c> files</c><00:00:58.320><c> for</c><00:00:59.100><c> doing</c>

00:00:59.389 --> 00:00:59.399 align:start position:0%
swapped out the text files for doing
 

00:00:59.399 --> 00:01:02.569 align:start position:0%
swapped out the text files for doing
multiple<00:00:59.820><c> PDF</c><00:01:00.360><c> files</c><00:01:00.840><c> in</c><00:01:01.440><c> here</c><00:01:02.100><c> and</c><00:01:02.520><c> actually</c>

00:01:02.569 --> 00:01:02.579 align:start position:0%
multiple PDF files in here and actually
 

00:01:02.579 --> 00:01:03.830 align:start position:0%
multiple PDF files in here and actually
if<00:01:02.820><c> we</c><00:01:02.940><c> have</c><00:01:03.059><c> a</c><00:01:03.180><c> look</c>

00:01:03.830 --> 00:01:03.840 align:start position:0%
if we have a look
 

00:01:03.840 --> 00:01:06.590 align:start position:0%
if we have a look
in<00:01:04.440><c> here</c><00:01:04.739><c> you'll</c><00:01:05.339><c> see</c><00:01:05.580><c> that</c><00:01:05.939><c> what</c><00:01:06.240><c> I've</c><00:01:06.420><c> done</c>

00:01:06.590 --> 00:01:06.600 align:start position:0%
in here you'll see that what I've done
 

00:01:06.600 --> 00:01:09.109 align:start position:0%
in here you'll see that what I've done
is<00:01:06.780><c> just</c><00:01:06.960><c> put</c><00:01:07.200><c> in</c><00:01:07.439><c> some</c><00:01:07.979><c> papers</c>

00:01:09.109 --> 00:01:09.119 align:start position:0%
is just put in some papers
 

00:01:09.119 --> 00:01:11.149 align:start position:0%
is just put in some papers
so<00:01:09.780><c> these</c><00:01:10.080><c> are</c><00:01:10.200><c> just</c><00:01:10.320><c> some</c><00:01:10.500><c> papers</c><00:01:10.920><c> from</c>

00:01:11.149 --> 00:01:11.159 align:start position:0%
so these are just some papers from
 

00:01:11.159 --> 00:01:14.210 align:start position:0%
so these are just some papers from
archive<00:01:11.640><c> about</c><00:01:12.540><c> react</c><00:01:13.080><c> to</c><00:01:13.320><c> a</c><00:01:13.619><c> former</c><00:01:13.979><c> flash</c>

00:01:14.210 --> 00:01:14.220 align:start position:0%
archive about react to a former flash
 

00:01:14.220 --> 00:01:16.850 align:start position:0%
archive about react to a former flash
attention<00:01:14.659><c> Alibi</c><00:01:15.659><c> so</c><00:01:16.260><c> just</c><00:01:16.439><c> some</c><00:01:16.619><c> stuff</c>

00:01:16.850 --> 00:01:16.860 align:start position:0%
attention Alibi so just some stuff
 

00:01:16.860 --> 00:01:19.609 align:start position:0%
attention Alibi so just some stuff
around<00:01:17.720><c> the</c><00:01:18.720><c> the</c><00:01:18.840><c> topics</c><00:01:19.200><c> that</c><00:01:19.380><c> we've</c><00:01:19.560><c> been</c>

00:01:19.609 --> 00:01:19.619 align:start position:0%
around the the topics that we've been
 

00:01:19.619 --> 00:01:21.350 align:start position:0%
around the the topics that we've been
looking<00:01:19.799><c> at</c><00:01:19.979><c> in</c><00:01:20.220><c> the</c><00:01:20.340><c> large</c><00:01:20.520><c> language</c><00:01:20.759><c> models</c>

00:01:21.350 --> 00:01:21.360 align:start position:0%
looking at in the large language models
 

00:01:21.360 --> 00:01:23.330 align:start position:0%
looking at in the large language models
recently<00:01:21.780><c> the</c><00:01:22.439><c> splitting</c><00:01:22.860><c> and</c><00:01:22.979><c> stuff</c><00:01:23.159><c> like</c>

00:01:23.330 --> 00:01:23.340 align:start position:0%
recently the splitting and stuff like
 

00:01:23.340 --> 00:01:25.249 align:start position:0%
recently the splitting and stuff like
that<00:01:23.460><c> is</c><00:01:23.640><c> all</c><00:01:23.820><c> the</c><00:01:24.000><c> same</c><00:01:24.180><c> so</c><00:01:24.720><c> we've</c><00:01:24.900><c> got</c><00:01:25.020><c> you</c>

00:01:25.249 --> 00:01:25.259 align:start position:0%
that is all the same so we've got you
 

00:01:25.259 --> 00:01:26.570 align:start position:0%
that is all the same so we've got you
know<00:01:25.320><c> basically</c><00:01:25.560><c> we're</c><00:01:25.740><c> just</c><00:01:25.920><c> bringing</c><00:01:26.280><c> it</c><00:01:26.400><c> in</c>

00:01:26.570 --> 00:01:26.580 align:start position:0%
know basically we're just bringing it in
 

00:01:26.580 --> 00:01:29.090 align:start position:0%
know basically we're just bringing it in
we're<00:01:27.060><c> just</c><00:01:27.240><c> using</c><00:01:27.600><c> the</c><00:01:27.840><c> simple</c><00:01:28.080><c> Pi</c><00:01:28.619><c> PDF</c>

00:01:29.090 --> 00:01:29.100 align:start position:0%
we're just using the simple Pi PDF
 

00:01:29.100 --> 00:01:32.990 align:start position:0%
we're just using the simple Pi PDF
loader<00:01:29.640><c> in</c><00:01:30.299><c> this</c><00:01:30.420><c> case</c><00:01:30.659><c> bring</c><00:01:31.500><c> things</c><00:01:31.860><c> in</c><00:01:32.159><c> and</c>

00:01:32.990 --> 00:01:33.000 align:start position:0%
loader in this case bring things in and
 

00:01:33.000 --> 00:01:35.510 align:start position:0%
loader in this case bring things in and
then<00:01:33.259><c> the</c><00:01:34.259><c> next</c><00:01:34.439><c> key</c><00:01:34.799><c> thing</c><00:01:34.979><c> is</c><00:01:35.100><c> we</c><00:01:35.220><c> just</c><00:01:35.400><c> get</c>

00:01:35.510 --> 00:01:35.520 align:start position:0%
then the next key thing is we just get
 

00:01:35.520 --> 00:01:37.429 align:start position:0%
then the next key thing is we just get
to<00:01:35.640><c> the</c><00:01:35.759><c> embedding</c><00:01:36.240><c> so</c><00:01:36.540><c> there's</c><00:01:36.720><c> two</c><00:01:36.960><c> ways</c><00:01:37.320><c> of</c>

00:01:37.429 --> 00:01:37.439 align:start position:0%
to the embedding so there's two ways of
 

00:01:37.439 --> 00:01:39.050 align:start position:0%
to the embedding so there's two ways of
doing<00:01:37.619><c> the</c><00:01:37.860><c> embeddings</c>

00:01:39.050 --> 00:01:39.060 align:start position:0%
doing the embeddings
 

00:01:39.060 --> 00:01:41.870 align:start position:0%
doing the embeddings
you<00:01:39.720><c> can</c><00:01:39.900><c> use</c><00:01:40.259><c> just</c><00:01:40.799><c> the</c><00:01:40.979><c> normal</c><00:01:41.159><c> hugging</c><00:01:41.700><c> face</c>

00:01:41.870 --> 00:01:41.880 align:start position:0%
you can use just the normal hugging face
 

00:01:41.880 --> 00:01:44.450 align:start position:0%
you can use just the normal hugging face
embeddings<00:01:42.540><c> so</c><00:01:43.200><c> this</c><00:01:43.439><c> is</c><00:01:43.560><c> using</c><00:01:43.860><c> things</c><00:01:44.100><c> like</c>

00:01:44.450 --> 00:01:44.460 align:start position:0%
embeddings so this is using things like
 

00:01:44.460 --> 00:01:47.450 align:start position:0%
embeddings so this is using things like
sentence<00:01:45.079><c> Transformers</c><00:01:46.079><c> and</c><00:01:47.040><c> there's</c><00:01:47.280><c> a</c>

00:01:47.450 --> 00:01:47.460 align:start position:0%
sentence Transformers and there's a
 

00:01:47.460 --> 00:01:49.069 align:start position:0%
sentence Transformers and there's a
whole<00:01:47.579><c> bunch</c><00:01:47.700><c> of</c><00:01:47.880><c> different</c><00:01:48.060><c> models</c><00:01:48.659><c> around</c>

00:01:49.069 --> 00:01:49.079 align:start position:0%
whole bunch of different models around
 

00:01:49.079 --> 00:01:52.310 align:start position:0%
whole bunch of different models around
that<00:01:49.500><c> they</c><00:01:50.280><c> vary</c><00:01:50.520><c> in</c><00:01:50.700><c> degrees</c><00:01:50.880><c> of</c><00:01:51.240><c> quality</c><00:01:51.720><c> and</c>

00:01:52.310 --> 00:01:52.320 align:start position:0%
that they vary in degrees of quality and
 

00:01:52.320 --> 00:01:53.749 align:start position:0%
that they vary in degrees of quality and
a<00:01:52.439><c> lot</c><00:01:52.500><c> of</c><00:01:52.619><c> it</c><00:01:52.740><c> will</c><00:01:52.920><c> also</c><00:01:53.159><c> depend</c><00:01:53.399><c> on</c><00:01:53.460><c> your</c>

00:01:53.749 --> 00:01:53.759 align:start position:0%
a lot of it will also depend on your
 

00:01:53.759 --> 00:01:56.149 align:start position:0%
a lot of it will also depend on your
data<00:01:54.180><c> as</c><00:01:54.420><c> well</c><00:01:54.659><c> which</c><00:01:55.020><c> ones</c><00:01:55.380><c> sort</c><00:01:55.860><c> of</c><00:01:55.979><c> match</c>

00:01:56.149 --> 00:01:56.159 align:start position:0%
data as well which ones sort of match
 

00:01:56.159 --> 00:01:57.289 align:start position:0%
data as well which ones sort of match
this

00:01:57.289 --> 00:01:57.299 align:start position:0%
this
 

00:01:57.299 --> 00:02:00.230 align:start position:0%
this
so<00:01:58.079><c> an</c><00:01:58.560><c> example</c><00:01:59.159><c> of</c><00:01:59.340><c> just</c><00:01:59.520><c> using</c><00:01:59.820><c> a</c><00:01:59.939><c> standard</c>

00:02:00.230 --> 00:02:00.240 align:start position:0%
so an example of just using a standard
 

00:02:00.240 --> 00:02:01.850 align:start position:0%
so an example of just using a standard
sentence<00:02:00.600><c> Transformer</c><00:02:01.140><c> would</c><00:02:01.320><c> be</c><00:02:01.439><c> this</c><00:02:01.740><c> one</c>

00:02:01.850 --> 00:02:01.860 align:start position:0%
sentence Transformer would be this one
 

00:02:01.860 --> 00:02:04.850 align:start position:0%
sentence Transformer would be this one
so<00:02:02.460><c> this</c><00:02:02.759><c> is</c><00:02:02.880><c> one</c><00:02:03.540><c> of</c><00:02:03.720><c> that</c><00:02:04.140><c> used</c><00:02:04.380><c> to</c><00:02:04.500><c> be</c><00:02:04.560><c> one</c><00:02:04.740><c> of</c>

00:02:04.850 --> 00:02:04.860 align:start position:0%
so this is one of that used to be one of
 

00:02:04.860 --> 00:02:07.190 align:start position:0%
so this is one of that used to be one of
the<00:02:04.979><c> top</c><00:02:05.100><c> models</c><00:02:05.579><c> for</c><00:02:06.000><c> doing</c><00:02:06.240><c> this</c><00:02:06.420><c> but</c><00:02:06.960><c> when</c>

00:02:07.190 --> 00:02:07.200 align:start position:0%
the top models for doing this but when
 

00:02:07.200 --> 00:02:09.050 align:start position:0%
the top models for doing this but when
my<00:02:07.439><c> testing</c><00:02:07.740><c> I</c><00:02:07.860><c> actually</c><00:02:08.039><c> came</c><00:02:08.280><c> across</c><00:02:08.640><c> that</c><00:02:08.880><c> a</c>

00:02:09.050 --> 00:02:09.060 align:start position:0%
my testing I actually came across that a
 

00:02:09.060 --> 00:02:10.609 align:start position:0%
my testing I actually came across that a
newer<00:02:09.360><c> model</c><00:02:09.539><c> that</c><00:02:10.020><c> seems</c><00:02:10.319><c> to</c><00:02:10.380><c> be</c><00:02:10.440><c> doing</c>

00:02:10.609 --> 00:02:10.619 align:start position:0%
newer model that seems to be doing
 

00:02:10.619 --> 00:02:13.250 align:start position:0%
newer model that seems to be doing
better<00:02:10.979><c> so</c><00:02:11.640><c> I</c><00:02:11.760><c> decided</c><00:02:12.120><c> to</c><00:02:12.180><c> go</c><00:02:12.420><c> with</c><00:02:12.599><c> that</c><00:02:12.900><c> and</c>

00:02:13.250 --> 00:02:13.260 align:start position:0%
better so I decided to go with that and
 

00:02:13.260 --> 00:02:15.290 align:start position:0%
better so I decided to go with that and
the<00:02:13.500><c> new</c><00:02:13.620><c> model</c><00:02:13.800><c> that</c><00:02:13.980><c> I'm</c><00:02:14.099><c> going</c><00:02:14.340><c> with</c><00:02:14.520><c> is</c><00:02:14.879><c> the</c>

00:02:15.290 --> 00:02:15.300 align:start position:0%
the new model that I'm going with is the
 

00:02:15.300 --> 00:02:18.229 align:start position:0%
the new model that I'm going with is the
instructor<00:02:15.780><c> embeddings</c><00:02:16.500><c> so</c><00:02:17.340><c> I</c><00:02:17.700><c> think</c><00:02:17.819><c> these</c>

00:02:18.229 --> 00:02:18.239 align:start position:0%
instructor embeddings so I think these
 

00:02:18.239 --> 00:02:22.070 align:start position:0%
instructor embeddings so I think these
kind<00:02:18.420><c> of</c><00:02:18.540><c> deserve</c><00:02:19.260><c> a</c><00:02:19.739><c> whole</c><00:02:20.340><c> video</c><00:02:21.300><c> to</c>

00:02:22.070 --> 00:02:22.080 align:start position:0%
kind of deserve a whole video to
 

00:02:22.080 --> 00:02:23.630 align:start position:0%
kind of deserve a whole video to
themselves<00:02:22.379><c> to</c><00:02:22.620><c> explain</c><00:02:22.739><c> the</c><00:02:23.160><c> paper</c><00:02:23.340><c> and</c>

00:02:23.630 --> 00:02:23.640 align:start position:0%
themselves to explain the paper and
 

00:02:23.640 --> 00:02:25.790 align:start position:0%
themselves to explain the paper and
stuff<00:02:23.819><c> like</c><00:02:24.000><c> that</c><00:02:24.180><c> the</c><00:02:24.840><c> idea</c><00:02:25.140><c> here</c><00:02:25.319><c> is</c><00:02:25.500><c> that</c>

00:02:25.790 --> 00:02:25.800 align:start position:0%
stuff like that the idea here is that
 

00:02:25.800 --> 00:02:28.309 align:start position:0%
stuff like that the idea here is that
these<00:02:26.160><c> are</c><00:02:26.520><c> custom</c><00:02:26.940><c> embeddings</c><00:02:27.480><c> depending</c><00:02:28.020><c> on</c>

00:02:28.309 --> 00:02:28.319 align:start position:0%
these are custom embeddings depending on
 

00:02:28.319 --> 00:02:31.250 align:start position:0%
these are custom embeddings depending on
what<00:02:28.800><c> it</c><00:02:28.980><c> is</c><00:02:29.160><c> uh</c><00:02:29.819><c> that</c><00:02:30.180><c> you're</c><00:02:30.360><c> using</c><00:02:30.840><c> them</c><00:02:31.020><c> for</c>

00:02:31.250 --> 00:02:31.260 align:start position:0%
what it is uh that you're using them for
 

00:02:31.260 --> 00:02:33.530 align:start position:0%
what it is uh that you're using them for
in<00:02:31.920><c> this</c><00:02:32.040><c> case</c><00:02:32.160><c> though</c><00:02:32.400><c> we're</c><00:02:32.580><c> just</c><00:02:32.760><c> using</c><00:02:33.180><c> the</c>

00:02:33.530 --> 00:02:33.540 align:start position:0%
in this case though we're just using the
 

00:02:33.540 --> 00:02:36.530 align:start position:0%
in this case though we're just using the
instruction<00:02:34.160><c> embeddings</c><00:02:35.160><c> and</c><00:02:36.000><c> we're</c><00:02:36.239><c> using</c>

00:02:36.530 --> 00:02:36.540 align:start position:0%
instruction embeddings and we're using
 

00:02:36.540 --> 00:02:39.170 align:start position:0%
instruction embeddings and we're using
the<00:02:36.780><c> Excel</c><00:02:37.200><c> variety</c><00:02:37.680><c> of</c><00:02:37.920><c> this</c><00:02:38.160><c> so</c><00:02:38.580><c> we</c><00:02:38.940><c> bring</c>

00:02:39.170 --> 00:02:39.180 align:start position:0%
the Excel variety of this so we bring
 

00:02:39.180 --> 00:02:41.630 align:start position:0%
the Excel variety of this so we bring
these<00:02:39.540><c> basically</c><00:02:39.959><c> into</c><00:02:40.260><c> Lang</c><00:02:40.620><c> Che</c>

00:02:41.630 --> 00:02:41.640 align:start position:0%
these basically into Lang Che
 

00:02:41.640 --> 00:02:44.210 align:start position:0%
these basically into Lang Che
there<00:02:42.480><c> you</c><00:02:42.780><c> can</c><00:02:42.900><c> see</c><00:02:43.080><c> that</c><00:02:43.440><c> we're</c><00:02:43.920><c> going</c><00:02:44.099><c> to</c>

00:02:44.210 --> 00:02:44.220 align:start position:0%
there you can see that we're going to
 

00:02:44.220 --> 00:02:45.949 align:start position:0%
there you can see that we're going to
run<00:02:44.280><c> them</c><00:02:44.459><c> locally</c><00:02:44.819><c> so</c><00:02:45.120><c> it's</c><00:02:45.239><c> downloading</c><00:02:45.720><c> the</c>

00:02:45.949 --> 00:02:45.959 align:start position:0%
run them locally so it's downloading the
 

00:02:45.959 --> 00:02:48.229 align:start position:0%
run them locally so it's downloading the
model<00:02:46.200><c> it's</c><00:02:46.560><c> downloading</c><00:02:47.099><c> all</c><00:02:47.340><c> the</c><00:02:47.640><c> files</c><00:02:48.000><c> for</c>

00:02:48.229 --> 00:02:48.239 align:start position:0%
model it's downloading all the files for
 

00:02:48.239 --> 00:02:50.750 align:start position:0%
model it's downloading all the files for
this<00:02:48.480><c> we're</c><00:02:49.080><c> actually</c><00:02:49.319><c> telling</c><00:02:49.800><c> it</c><00:02:49.920><c> here</c><00:02:50.220><c> that</c>

00:02:50.750 --> 00:02:50.760 align:start position:0%
this we're actually telling it here that
 

00:02:50.760 --> 00:02:52.970 align:start position:0%
this we're actually telling it here that
we're<00:02:50.940><c> going</c><00:02:51.060><c> to</c><00:02:51.239><c> put</c><00:02:51.420><c> it</c><00:02:51.540><c> on</c><00:02:51.780><c> the</c><00:02:51.959><c> GPU</c><00:02:52.500><c> so</c><00:02:52.800><c> this</c>

00:02:52.970 --> 00:02:52.980 align:start position:0%
we're going to put it on the GPU so this
 

00:02:52.980 --> 00:02:55.850 align:start position:0%
we're going to put it on the GPU so this
is<00:02:53.099><c> what</c><00:02:53.400><c> device</c><00:02:53.640><c> Cuda</c><00:02:54.420><c> is</c><00:02:54.900><c> here</c><00:02:55.560><c> um</c><00:02:55.620><c> if</c><00:02:55.739><c> you</c>

00:02:55.850 --> 00:02:55.860 align:start position:0%
is what device Cuda is here um if you
 

00:02:55.860 --> 00:02:57.350 align:start position:0%
is what device Cuda is here um if you
wanted<00:02:56.040><c> to</c><00:02:56.280><c> run</c><00:02:56.400><c> them</c><00:02:56.519><c> locally</c><00:02:56.879><c> you</c><00:02:57.060><c> could</c><00:02:57.180><c> put</c>

00:02:57.350 --> 00:02:57.360 align:start position:0%
wanted to run them locally you could put
 

00:02:57.360 --> 00:02:59.690 align:start position:0%
wanted to run them locally you could put
it<00:02:57.480><c> device</c><00:02:57.840><c> CPU</c><00:02:58.500><c> for</c><00:02:59.160><c> doing</c><00:02:59.400><c> that</c><00:02:59.519><c> it's</c>

00:02:59.690 --> 00:02:59.700 align:start position:0%
it device CPU for doing that it's
 

00:02:59.700 --> 00:03:01.070 align:start position:0%
it device CPU for doing that it's
definitely<00:03:00.000><c> going</c><00:03:00.120><c> to</c><00:03:00.239><c> make</c><00:03:00.360><c> it</c><00:03:00.480><c> a</c><00:03:00.599><c> lot</c><00:03:00.720><c> slower</c>

00:03:01.070 --> 00:03:01.080 align:start position:0%
definitely going to make it a lot slower
 

00:03:01.080 --> 00:03:02.869 align:start position:0%
definitely going to make it a lot slower
and<00:03:01.980><c> you'll</c><00:03:02.160><c> see</c><00:03:02.280><c> it's</c><00:03:02.400><c> going</c><00:03:02.580><c> to</c><00:03:02.700><c> basically</c>

00:03:02.869 --> 00:03:02.879 align:start position:0%
and you'll see it's going to basically
 

00:03:02.879 --> 00:03:05.630 align:start position:0%
and you'll see it's going to basically
load<00:03:03.300><c> these</c><00:03:03.599><c> up</c><00:03:03.780><c> and</c><00:03:04.260><c> bring</c><00:03:04.440><c> them</c><00:03:04.620><c> in</c><00:03:04.860><c> and</c><00:03:05.459><c> by</c>

00:03:05.630 --> 00:03:05.640 align:start position:0%
load these up and bring them in and by
 

00:03:05.640 --> 00:03:07.369 align:start position:0%
load these up and bring them in and by
default<00:03:06.000><c> these</c><00:03:06.420><c> are</c><00:03:06.480><c> operating</c><00:03:06.900><c> at</c><00:03:07.140><c> a</c>

00:03:07.369 --> 00:03:07.379 align:start position:0%
default these are operating at a
 

00:03:07.379 --> 00:03:10.850 align:start position:0%
default these are operating at a
sequence<00:03:07.800><c> length</c><00:03:08.220><c> of</c><00:03:08.400><c> 512</c><00:03:09.180><c> which</c><00:03:09.959><c> is</c><00:03:10.080><c> fine</c><00:03:10.379><c> for</c>

00:03:10.850 --> 00:03:10.860 align:start position:0%
sequence length of 512 which is fine for
 

00:03:10.860 --> 00:03:12.350 align:start position:0%
sequence length of 512 which is fine for
the<00:03:11.040><c> splitting</c><00:03:11.459><c> that</c><00:03:11.580><c> we're</c><00:03:11.760><c> doing</c><00:03:11.940><c> of</c><00:03:12.180><c> a</c>

00:03:12.350 --> 00:03:12.360 align:start position:0%
the splitting that we're doing of a
 

00:03:12.360 --> 00:03:13.910 align:start position:0%
the splitting that we're doing of a
thousand<00:03:12.659><c> characters</c><00:03:12.959><c> that</c><00:03:13.379><c> should</c><00:03:13.560><c> be</c><00:03:13.739><c> fine</c>

00:03:13.910 --> 00:03:13.920 align:start position:0%
thousand characters that should be fine
 

00:03:13.920 --> 00:03:15.890 align:start position:0%
thousand characters that should be fine
in<00:03:14.700><c> this</c><00:03:14.819><c> case</c>

00:03:15.890 --> 00:03:15.900 align:start position:0%
in this case
 

00:03:15.900 --> 00:03:18.410 align:start position:0%
in this case
okay<00:03:16.500><c> once</c><00:03:17.040><c> we've</c><00:03:17.159><c> got</c><00:03:17.280><c> the</c><00:03:17.459><c> embedding</c><00:03:17.879><c> set</c><00:03:18.239><c> up</c>

00:03:18.410 --> 00:03:18.420 align:start position:0%
okay once we've got the embedding set up
 

00:03:18.420 --> 00:03:20.930 align:start position:0%
okay once we've got the embedding set up
we're<00:03:19.260><c> then</c><00:03:19.500><c> going</c><00:03:19.739><c> to</c><00:03:19.920><c> need</c><00:03:20.099><c> to</c><00:03:20.400><c> make</c><00:03:20.640><c> our</c>

00:03:20.930 --> 00:03:20.940 align:start position:0%
we're then going to need to make our
 

00:03:20.940 --> 00:03:23.509 align:start position:0%
we're then going to need to make our
Vector<00:03:21.360><c> store</c><00:03:21.540><c> here</c><00:03:21.900><c> so</c><00:03:22.319><c> this</c><00:03:22.800><c> is</c><00:03:22.920><c> all</c><00:03:23.099><c> exactly</c>

00:03:23.509 --> 00:03:23.519 align:start position:0%
Vector store here so this is all exactly
 

00:03:23.519 --> 00:03:25.009 align:start position:0%
Vector store here so this is all exactly
the<00:03:23.700><c> same</c><00:03:23.819><c> as</c><00:03:24.000><c> the</c><00:03:24.239><c> last</c><00:03:24.360><c> video</c><00:03:24.599><c> we're</c>

00:03:25.009 --> 00:03:25.019 align:start position:0%
the same as the last video we're
 

00:03:25.019 --> 00:03:27.050 align:start position:0%
the same as the last video we're
basically<00:03:25.440><c> just</c><00:03:25.980><c> passing</c><00:03:26.400><c> in</c><00:03:26.580><c> the</c><00:03:26.940><c> new</c>

00:03:27.050 --> 00:03:27.060 align:start position:0%
basically just passing in the new
 

00:03:27.060 --> 00:03:29.630 align:start position:0%
basically just passing in the new
embeddings<00:03:27.599><c> here</c><00:03:27.840><c> so</c><00:03:28.739><c> we're</c><00:03:29.040><c> not</c><00:03:29.220><c> using</c><00:03:29.459><c> open</c>

00:03:29.630 --> 00:03:29.640 align:start position:0%
embeddings here so we're not using open
 

00:03:29.640 --> 00:03:32.210 align:start position:0%
embeddings here so we're not using open
AI<00:03:30.060><c> embeddings</c><00:03:30.480><c> anymore</c><00:03:30.659><c> okay</c><00:03:31.560><c> once</c><00:03:32.040><c> we've</c>

00:03:32.210 --> 00:03:32.220 align:start position:0%
AI embeddings anymore okay once we've
 

00:03:32.220 --> 00:03:34.550 align:start position:0%
AI embeddings anymore okay once we've
got<00:03:32.340><c> the</c><00:03:32.519><c> embedding</c><00:03:33.000><c> set</c><00:03:33.300><c> up</c><00:03:33.480><c> we're</c><00:03:34.200><c> now</c><00:03:34.379><c> going</c>

00:03:34.550 --> 00:03:34.560 align:start position:0%
got the embedding set up we're now going
 

00:03:34.560 --> 00:03:36.890 align:start position:0%
got the embedding set up we're now going
to<00:03:34.739><c> basically</c><00:03:35.040><c> just</c><00:03:35.459><c> go</c><00:03:35.760><c> along</c><00:03:36.239><c> with</c><00:03:36.540><c> what</c><00:03:36.780><c> we</c>

00:03:36.890 --> 00:03:36.900 align:start position:0%
to basically just go along with what we
 

00:03:36.900 --> 00:03:38.509 align:start position:0%
to basically just go along with what we
were<00:03:37.080><c> doing</c><00:03:37.200><c> before</c><00:03:37.500><c> so</c><00:03:37.860><c> we</c><00:03:38.040><c> need</c><00:03:38.159><c> to</c><00:03:38.280><c> set</c><00:03:38.400><c> up</c>

00:03:38.509 --> 00:03:38.519 align:start position:0%
were doing before so we need to set up
 

00:03:38.519 --> 00:03:40.729 align:start position:0%
were doing before so we need to set up
our<00:03:38.760><c> Vector</c><00:03:39.180><c> store</c><00:03:39.360><c> and</c><00:03:39.959><c> here</c><00:03:40.260><c> we're</c><00:03:40.440><c> using</c>

00:03:40.729 --> 00:03:40.739 align:start position:0%
our Vector store and here we're using
 

00:03:40.739 --> 00:03:42.710 align:start position:0%
our Vector store and here we're using
chroma<00:03:41.159><c> DB</c><00:03:41.580><c> for</c><00:03:41.879><c> setting</c><00:03:42.180><c> up</c><00:03:42.239><c> the</c><00:03:42.360><c> vector</c>

00:03:42.710 --> 00:03:42.720 align:start position:0%
chroma DB for setting up the vector
 

00:03:42.720 --> 00:03:43.490 align:start position:0%
chroma DB for setting up the vector
store

00:03:43.490 --> 00:03:43.500 align:start position:0%
store
 

00:03:43.500 --> 00:03:46.309 align:start position:0%
store
we've<00:03:44.159><c> persist</c><00:03:44.459><c> a</c><00:03:44.700><c> directory</c><00:03:45.239><c> we're</c><00:03:45.959><c> going</c><00:03:46.140><c> to</c>

00:03:46.309 --> 00:03:46.319 align:start position:0%
we've persist a directory we're going to
 

00:03:46.319 --> 00:03:48.770 align:start position:0%
we've persist a directory we're going to
need<00:03:46.440><c> to</c><00:03:46.739><c> create</c><00:03:47.040><c> this</c><00:03:47.400><c> from</c><00:03:47.640><c> documents</c><00:03:48.299><c> so</c>

00:03:48.770 --> 00:03:48.780 align:start position:0%
need to create this from documents so
 

00:03:48.780 --> 00:03:51.350 align:start position:0%
need to create this from documents so
we're<00:03:48.959><c> going</c><00:03:49.080><c> to</c><00:03:49.260><c> pass</c><00:03:49.440><c> in</c><00:03:49.739><c> the</c><00:03:50.400><c> in</c><00:03:50.819><c> structure</c>

00:03:51.350 --> 00:03:51.360 align:start position:0%
we're going to pass in the in structure
 

00:03:51.360 --> 00:03:53.210 align:start position:0%
we're going to pass in the in structure
embeddings<00:03:51.900><c> and</c><00:03:52.560><c> we're</c><00:03:52.680><c> going</c><00:03:52.799><c> to</c><00:03:52.860><c> pass</c><00:03:53.040><c> in</c>

00:03:53.210 --> 00:03:53.220 align:start position:0%
embeddings and we're going to pass in
 

00:03:53.220 --> 00:03:55.550 align:start position:0%
embeddings and we're going to pass in
the<00:03:53.459><c> document</c><00:03:53.760><c> text</c><00:03:54.060><c> that</c><00:03:54.420><c> we've</c><00:03:54.720><c> already</c><00:03:54.840><c> got</c>

00:03:55.550 --> 00:03:55.560 align:start position:0%
the document text that we've already got
 

00:03:55.560 --> 00:03:58.190 align:start position:0%
the document text that we've already got
out<00:03:55.799><c> from</c><00:03:56.400><c> that</c><00:03:56.640><c> so</c><00:03:57.299><c> this</c><00:03:57.540><c> is</c><00:03:57.659><c> exactly</c><00:03:58.019><c> the</c>

00:03:58.190 --> 00:03:58.200 align:start position:0%
out from that so this is exactly the
 

00:03:58.200 --> 00:03:59.690 align:start position:0%
out from that so this is exactly the
same<00:03:58.319><c> as</c><00:03:58.500><c> the</c><00:03:58.739><c> previous</c><00:03:58.860><c> video</c><00:03:59.220><c> we</c><00:03:59.519><c> haven't</c>

00:03:59.690 --> 00:03:59.700 align:start position:0%
same as the previous video we haven't
 

00:03:59.700 --> 00:04:00.949 align:start position:0%
same as the previous video we haven't
really<00:03:59.879><c> changed</c><00:04:00.239><c> anything</c><00:04:00.299><c> the</c><00:04:00.659><c> only</c><00:04:00.780><c> thing</c>

00:04:00.949 --> 00:04:00.959 align:start position:0%
really changed anything the only thing
 

00:04:00.959 --> 00:04:03.110 align:start position:0%
really changed anything the only thing
we're<00:04:01.140><c> doing</c><00:04:01.379><c> now</c><00:04:01.620><c> is</c><00:04:01.920><c> we're</c><00:04:02.159><c> using</c><00:04:02.580><c> these</c>

00:04:03.110 --> 00:04:03.120 align:start position:0%
we're doing now is we're using these
 

00:04:03.120 --> 00:04:05.869 align:start position:0%
we're doing now is we're using these
instructor<00:04:03.420><c> embeddings</c><00:04:04.140><c> in</c><00:04:04.799><c> there</c><00:04:04.980><c> we</c><00:04:05.640><c> now</c>

00:04:05.869 --> 00:04:05.879 align:start position:0%
instructor embeddings in there we now
 

00:04:05.879 --> 00:04:08.030 align:start position:0%
instructor embeddings in there we now
basically<00:04:06.420><c> can</c><00:04:06.780><c> do</c><00:04:06.959><c> the</c><00:04:07.260><c> same</c><00:04:07.500><c> sorts</c><00:04:07.980><c> of</c>

00:04:08.030 --> 00:04:08.040 align:start position:0%
basically can do the same sorts of
 

00:04:08.040 --> 00:04:10.670 align:start position:0%
basically can do the same sorts of
things<00:04:08.280><c> of</c><00:04:08.819><c> making</c><00:04:09.060><c> a</c><00:04:09.360><c> retriever</c><00:04:09.840><c> and</c><00:04:10.500><c> now</c>

00:04:10.670 --> 00:04:10.680 align:start position:0%
things of making a retriever and now
 

00:04:10.680 --> 00:04:13.610 align:start position:0%
things of making a retriever and now
obviously<00:04:11.040><c> this</c><00:04:11.280><c> retriever</c><00:04:11.700><c> is</c><00:04:12.180><c> using</c><00:04:12.659><c> our</c>

00:04:13.610 --> 00:04:13.620 align:start position:0%
obviously this retriever is using our
 

00:04:13.620 --> 00:04:16.310 align:start position:0%
obviously this retriever is using our
new<00:04:14.040><c> embeddings</c><00:04:14.939><c> for</c><00:04:15.360><c> that</c><00:04:15.599><c> and</c><00:04:16.019><c> now</c><00:04:16.139><c> the</c>

00:04:16.310 --> 00:04:16.320 align:start position:0%
new embeddings for that and now the
 

00:04:16.320 --> 00:04:18.050 align:start position:0%
new embeddings for that and now the
retriever<00:04:16.680><c> is</c><00:04:16.859><c> going</c><00:04:16.979><c> to</c><00:04:17.100><c> be</c><00:04:17.160><c> using</c><00:04:17.519><c> the</c><00:04:17.880><c> new</c>

00:04:18.050 --> 00:04:18.060 align:start position:0%
retriever is going to be using the new
 

00:04:18.060 --> 00:04:20.390 align:start position:0%
retriever is going to be using the new
embedding<00:04:18.720><c> the</c><00:04:18.959><c> instructor</c><00:04:19.260><c> embeddings</c><00:04:19.859><c> to</c>

00:04:20.390 --> 00:04:20.400 align:start position:0%
embedding the instructor embeddings to
 

00:04:20.400 --> 00:04:22.790 align:start position:0%
embedding the instructor embeddings to
actually<00:04:20.639><c> find</c><00:04:21.180><c> the</c><00:04:21.479><c> various</c><00:04:21.780><c> contexts</c><00:04:22.440><c> that</c>

00:04:22.790 --> 00:04:22.800 align:start position:0%
actually find the various contexts that
 

00:04:22.800 --> 00:04:25.610 align:start position:0%
actually find the various contexts that
match<00:04:23.160><c> based</c><00:04:23.940><c> on</c><00:04:24.000><c> a</c><00:04:24.180><c> query</c><00:04:24.479><c> in</c><00:04:24.840><c> here</c><00:04:25.020><c> next</c><00:04:25.500><c> up</c>

00:04:25.610 --> 00:04:25.620 align:start position:0%
match based on a query in here next up
 

00:04:25.620 --> 00:04:27.710 align:start position:0%
match based on a query in here next up
we<00:04:25.800><c> need</c><00:04:25.919><c> to</c><00:04:26.040><c> basically</c><00:04:26.340><c> make</c><00:04:26.580><c> a</c><00:04:26.759><c> chain</c><00:04:26.940><c> so</c>

00:04:27.710 --> 00:04:27.720 align:start position:0%
we need to basically make a chain so
 

00:04:27.720 --> 00:04:29.990 align:start position:0%
we need to basically make a chain so
this<00:04:28.259><c> is</c><00:04:28.380><c> again</c><00:04:28.620><c> the</c><00:04:28.919><c> same</c><00:04:29.040><c> as</c><00:04:29.220><c> before</c><00:04:29.460><c> nothing</c>

00:04:29.990 --> 00:04:30.000 align:start position:0%
this is again the same as before nothing
 

00:04:30.000 --> 00:04:31.730 align:start position:0%
this is again the same as before nothing
really<00:04:30.300><c> different</c><00:04:30.540><c> in</c><00:04:30.900><c> here</c><00:04:31.080><c> we're</c><00:04:31.380><c> passing</c>

00:04:31.730 --> 00:04:31.740 align:start position:0%
really different in here we're passing
 

00:04:31.740 --> 00:04:34.490 align:start position:0%
really different in here we're passing
in<00:04:31.860><c> the</c><00:04:32.040><c> retriever</c><00:04:32.520><c> that's</c><00:04:33.240><c> going</c><00:04:33.479><c> to</c><00:04:33.600><c> take</c>

00:04:34.490 --> 00:04:34.500 align:start position:0%
in the retriever that's going to take
 

00:04:34.500 --> 00:04:37.490 align:start position:0%
in the retriever that's going to take
care<00:04:34.680><c> of</c><00:04:34.860><c> the</c><00:04:35.699><c> vector</c><00:04:36.240><c> store</c><00:04:36.479><c> the</c><00:04:37.020><c> embeddings</c>

00:04:37.490 --> 00:04:37.500 align:start position:0%
care of the vector store the embeddings
 

00:04:37.500 --> 00:04:40.070 align:start position:0%
care of the vector store the embeddings
those<00:04:37.979><c> parts</c><00:04:38.160><c> there</c><00:04:38.639><c> I've</c><00:04:39.360><c> just</c><00:04:39.540><c> added</c><00:04:39.900><c> a</c>

00:04:40.070 --> 00:04:40.080 align:start position:0%
those parts there I've just added a
 

00:04:40.080 --> 00:04:42.290 align:start position:0%
those parts there I've just added a
little<00:04:40.320><c> bit</c><00:04:40.500><c> of</c><00:04:40.620><c> code</c><00:04:41.100><c> in</c><00:04:41.340><c> here</c><00:04:41.520><c> just</c><00:04:41.759><c> to</c><00:04:42.000><c> wrap</c>

00:04:42.290 --> 00:04:42.300 align:start position:0%
little bit of code in here just to wrap
 

00:04:42.300 --> 00:04:45.230 align:start position:0%
little bit of code in here just to wrap
the<00:04:42.960><c> answers</c><00:04:43.380><c> but</c><00:04:43.800><c> when</c><00:04:44.040><c> we</c><00:04:44.160><c> get</c><00:04:44.340><c> them</c><00:04:44.520><c> out</c>

00:04:45.230 --> 00:04:45.240 align:start position:0%
the answers but when we get them out
 

00:04:45.240 --> 00:04:47.749 align:start position:0%
the answers but when we get them out
and<00:04:45.720><c> we</c><00:04:45.840><c> can</c><00:04:45.960><c> see</c><00:04:46.080><c> that</c><00:04:46.320><c> if</c><00:04:46.500><c> we</c><00:04:46.740><c> look</c><00:04:47.340><c> at</c><00:04:47.520><c> this</c>

00:04:47.749 --> 00:04:47.759 align:start position:0%
and we can see that if we look at this
 

00:04:47.759 --> 00:04:50.150 align:start position:0%
and we can see that if we look at this
we<00:04:48.240><c> can</c><00:04:48.360><c> see</c><00:04:48.479><c> that</c><00:04:48.660><c> okay</c><00:04:48.840><c> starting</c><00:04:49.259><c> off</c><00:04:49.560><c> what</c>

00:04:50.150 --> 00:04:50.160 align:start position:0%
we can see that okay starting off what
 

00:04:50.160 --> 00:04:52.490 align:start position:0%
we can see that okay starting off what
is<00:04:50.280><c> Flash</c><00:04:50.460><c> retention</c><00:04:51.000><c> and</c><00:04:51.720><c> it's</c><00:04:52.080><c> going</c><00:04:52.259><c> to</c><00:04:52.380><c> go</c>

00:04:52.490 --> 00:04:52.500 align:start position:0%
is Flash retention and it's going to go
 

00:04:52.500 --> 00:04:54.830 align:start position:0%
is Flash retention and it's going to go
and<00:04:52.620><c> get</c><00:04:52.800><c> the</c><00:04:53.160><c> three</c><00:04:53.400><c> top</c><00:04:53.759><c> documents</c><00:04:54.419><c> and</c><00:04:54.660><c> in</c>

00:04:54.830 --> 00:04:54.840 align:start position:0%
and get the three top documents and in
 

00:04:54.840 --> 00:04:57.650 align:start position:0%
and get the three top documents and in
this<00:04:55.020><c> case</c><00:04:55.259><c> not</c><00:04:55.979><c> surprisingly</c><00:04:56.580><c> the</c><00:04:57.300><c> document</c>

00:04:57.650 --> 00:04:57.660 align:start position:0%
this case not surprisingly the document
 

00:04:57.660 --> 00:05:00.530 align:start position:0%
this case not surprisingly the document
that<00:04:58.199><c> the</c><00:04:58.440><c> embeddings</c><00:04:58.860><c> have</c><00:04:59.520><c> chosen</c><00:05:00.120><c> as</c><00:05:00.300><c> the</c>

00:05:00.530 --> 00:05:00.540 align:start position:0%
that the embeddings have chosen as the
 

00:05:00.540 --> 00:05:03.590 align:start position:0%
that the embeddings have chosen as the
similarity<00:05:01.139><c> that's</c><00:05:01.860><c> closest</c><00:05:02.160><c> to</c><00:05:02.880><c> what</c><00:05:03.180><c> we</c>

00:05:03.590 --> 00:05:03.600 align:start position:0%
similarity that's closest to what we
 

00:05:03.600 --> 00:05:05.990 align:start position:0%
similarity that's closest to what we
want<00:05:03.720><c> to</c><00:05:03.900><c> know</c><00:05:04.080><c> is</c><00:05:04.860><c> going</c><00:05:05.100><c> to</c><00:05:05.220><c> be</c><00:05:05.340><c> this</c><00:05:05.520><c> in</c><00:05:05.880><c> this</c>

00:05:05.990 --> 00:05:06.000 align:start position:0%
want to know is going to be this in this
 

00:05:06.000 --> 00:05:08.390 align:start position:0%
want to know is going to be this in this
flash<00:05:06.240><c> attention</c><00:05:06.600><c> paper</c><00:05:07.080><c> or</c><00:05:07.500><c> this</c><00:05:07.680><c> PDF</c><00:05:08.160><c> here</c>

00:05:08.390 --> 00:05:08.400 align:start position:0%
flash attention paper or this PDF here
 

00:05:08.400 --> 00:05:10.430 align:start position:0%
flash attention paper or this PDF here
and<00:05:09.120><c> so</c><00:05:09.240><c> basically</c><00:05:09.540><c> it</c><00:05:09.660><c> gives</c><00:05:09.840><c> us</c><00:05:10.020><c> back</c><00:05:10.199><c> a</c>

00:05:10.430 --> 00:05:10.440 align:start position:0%
and so basically it gives us back a
 

00:05:10.440 --> 00:05:12.710 align:start position:0%
and so basically it gives us back a
definition<00:05:10.620><c> for</c><00:05:11.160><c> Flash</c><00:05:11.400><c> attention</c><00:05:11.880><c> we</c><00:05:12.540><c> can</c>

00:05:12.710 --> 00:05:12.720 align:start position:0%
definition for Flash attention we can
 

00:05:12.720 --> 00:05:14.870 align:start position:0%
definition for Flash attention we can
then<00:05:12.900><c> skim</c><00:05:13.500><c> to</c><00:05:13.740><c> different</c><00:05:13.919><c> parts</c><00:05:14.160><c> of</c><00:05:14.400><c> this</c><00:05:14.580><c> so</c>

00:05:14.870 --> 00:05:14.880 align:start position:0%
then skim to different parts of this so
 

00:05:14.880 --> 00:05:17.330 align:start position:0%
then skim to different parts of this so
here<00:05:15.060><c> it</c><00:05:15.240><c> mentioned</c><00:05:15.600><c> IO</c><00:05:16.020><c> aware</c><00:05:16.440><c> so</c><00:05:16.919><c> I</c><00:05:17.160><c> wanted</c>

00:05:17.330 --> 00:05:17.340 align:start position:0%
here it mentioned IO aware so I wanted
 

00:05:17.340 --> 00:05:19.850 align:start position:0%
here it mentioned IO aware so I wanted
to<00:05:17.520><c> ask</c><00:05:17.639><c> out</c><00:05:17.880><c> what</c><00:05:18.300><c> is</c><00:05:18.479><c> that</c><00:05:18.720><c> it</c><00:05:19.320><c> basically</c><00:05:19.740><c> is</c>

00:05:19.850 --> 00:05:19.860 align:start position:0%
to ask out what is that it basically is
 

00:05:19.860 --> 00:05:22.790 align:start position:0%
to ask out what is that it basically is
able<00:05:20.160><c> to</c><00:05:20.280><c> go</c><00:05:20.520><c> through</c><00:05:20.759><c> and</c><00:05:21.240><c> find</c><00:05:21.540><c> again</c><00:05:22.020><c> from</c>

00:05:22.790 --> 00:05:22.800 align:start position:0%
able to go through and find again from
 

00:05:22.800 --> 00:05:24.170 align:start position:0%
able to go through and find again from
that<00:05:23.100><c> same</c><00:05:23.280><c> paper</c>

00:05:24.170 --> 00:05:24.180 align:start position:0%
that same paper
 

00:05:24.180 --> 00:05:26.930 align:start position:0%
that same paper
mentioned<00:05:25.020><c> tiling</c><00:05:25.500><c> I</c><00:05:25.800><c> go</c><00:05:25.979><c> through</c><00:05:26.160><c> can</c><00:05:26.699><c> find</c>

00:05:26.930 --> 00:05:26.940 align:start position:0%
mentioned tiling I go through can find
 

00:05:26.940 --> 00:05:29.270 align:start position:0%
mentioned tiling I go through can find
out<00:05:27.120><c> an</c><00:05:27.300><c> answer</c><00:05:27.479><c> for</c><00:05:27.720><c> that</c><00:05:27.960><c> as</c><00:05:28.199><c> well</c><00:05:28.380><c> so</c><00:05:28.979><c> then</c><00:05:29.160><c> I</c>

00:05:29.270 --> 00:05:29.280 align:start position:0%
out an answer for that as well so then I
 

00:05:29.280 --> 00:05:31.010 align:start position:0%
out an answer for that as well so then I
thought<00:05:29.400><c> okay</c><00:05:29.580><c> let's</c><00:05:30.060><c> ask</c><00:05:30.300><c> it</c><00:05:30.539><c> some</c><00:05:30.840><c> other</c>

00:05:31.010 --> 00:05:31.020 align:start position:0%
thought okay let's ask it some other
 

00:05:31.020 --> 00:05:34.249 align:start position:0%
thought okay let's ask it some other
questions<00:05:31.380><c> just</c><00:05:31.979><c> to</c><00:05:32.220><c> see</c><00:05:32.460><c> okay</c><00:05:32.940><c> what's</c><00:05:33.419><c> there</c>

00:05:34.249 --> 00:05:34.259 align:start position:0%
questions just to see okay what's there
 

00:05:34.259 --> 00:05:37.850 align:start position:0%
questions just to see okay what's there
by<00:05:34.860><c> asking</c><00:05:35.340><c> what</c><00:05:35.759><c> is</c><00:05:36.000><c> two</c><00:05:36.180><c> former</c><00:05:36.720><c> we're</c><00:05:37.560><c> then</c>

00:05:37.850 --> 00:05:37.860 align:start position:0%
by asking what is two former we're then
 

00:05:37.860 --> 00:05:41.090 align:start position:0%
by asking what is two former we're then
able<00:05:38.220><c> to</c><00:05:38.340><c> see</c><00:05:38.580><c> can</c><00:05:39.060><c> it</c><00:05:39.360><c> basically</c><00:05:40.139><c> is</c><00:05:40.800><c> it</c><00:05:40.979><c> going</c>

00:05:41.090 --> 00:05:41.100 align:start position:0%
able to see can it basically is it going
 

00:05:41.100 --> 00:05:42.409 align:start position:0%
able to see can it basically is it going
to<00:05:41.160><c> return</c><00:05:41.220><c> the</c><00:05:41.460><c> same</c><00:05:41.639><c> thing</c><00:05:41.699><c> what's</c><00:05:42.000><c> going</c><00:05:42.300><c> to</c>

00:05:42.409 --> 00:05:42.419 align:start position:0%
to return the same thing what's going to
 

00:05:42.419 --> 00:05:44.629 align:start position:0%
to return the same thing what's going to
get<00:05:42.539><c> and</c><00:05:43.320><c> sure</c><00:05:43.500><c> enough</c><00:05:43.680><c> here</c><00:05:44.100><c> we're</c><00:05:44.400><c> getting</c>

00:05:44.629 --> 00:05:44.639 align:start position:0%
get and sure enough here we're getting
 

00:05:44.639 --> 00:05:46.790 align:start position:0%
get and sure enough here we're getting
uh<00:05:45.120><c> two</c><00:05:45.240><c> former</c><00:05:45.660><c> as</c><00:05:45.840><c> a</c><00:05:45.960><c> language</c><00:05:46.080><c> model</c><00:05:46.380><c> that</c>

00:05:46.790 --> 00:05:46.800 align:start position:0%
uh two former as a language model that
 

00:05:46.800 --> 00:05:49.909 align:start position:0%
uh two former as a language model that
learns<00:05:47.220><c> and</c><00:05:47.759><c> a</c><00:05:47.940><c> self-supervised</c><00:05:48.660><c> way</c><00:05:48.900><c> and</c><00:05:49.740><c> so</c>

00:05:49.909 --> 00:05:49.919 align:start position:0%
learns and a self-supervised way and so
 

00:05:49.919 --> 00:05:51.890 align:start position:0%
learns and a self-supervised way and so
this<00:05:50.160><c> is</c><00:05:50.220><c> basically</c><00:05:50.520><c> just</c><00:05:50.759><c> showing</c><00:05:51.120><c> us</c><00:05:51.240><c> the</c>

00:05:51.890 --> 00:05:51.900 align:start position:0%
this is basically just showing us the
 

00:05:51.900 --> 00:05:54.650 align:start position:0%
this is basically just showing us the
rewriting<00:05:52.560><c> of</c><00:05:52.860><c> the</c><00:05:53.160><c> output</c><00:05:53.460><c> from</c><00:05:54.180><c> these</c><00:05:54.539><c> three</c>

00:05:54.650 --> 00:05:54.660 align:start position:0%
rewriting of the output from these three
 

00:05:54.660 --> 00:05:57.469 align:start position:0%
rewriting of the output from these three
examples<00:05:55.199><c> from</c><00:05:55.680><c> tall</c><00:05:56.220><c> football</c><00:05:56.520><c> the</c><00:05:57.180><c> three</c>

00:05:57.469 --> 00:05:57.479 align:start position:0%
examples from tall football the three
 

00:05:57.479 --> 00:05:59.930 align:start position:0%
examples from tall football the three
different<00:05:57.600><c> contexts</c><00:05:58.259><c> we</c><00:05:59.160><c> can</c><00:05:59.340><c> basically</c><00:05:59.639><c> ask</c>

00:05:59.930 --> 00:05:59.940 align:start position:0%
different contexts we can basically ask
 

00:05:59.940 --> 00:06:02.210 align:start position:0%
different contexts we can basically ask
some<00:06:00.479><c> more</c><00:06:00.660><c> questions</c><00:06:00.900><c> about</c><00:06:01.440><c> it</c><00:06:01.680><c> what</c><00:06:01.919><c> tools</c>

00:06:02.210 --> 00:06:02.220 align:start position:0%
some more questions about it what tools
 

00:06:02.220 --> 00:06:04.310 align:start position:0%
some more questions about it what tools
can<00:06:02.400><c> be</c><00:06:02.520><c> used</c><00:06:02.699><c> with</c><00:06:02.940><c> tool</c><00:06:03.300><c> former</c><00:06:03.780><c> and</c><00:06:04.199><c> use</c>

00:06:04.310 --> 00:06:04.320 align:start position:0%
can be used with tool former and use
 

00:06:04.320 --> 00:06:06.469 align:start position:0%
can be used with tool former and use
search<00:06:04.500><c> engines</c><00:06:04.860><c> calculators</c><00:06:05.520><c> translation</c>

00:06:06.469 --> 00:06:06.479 align:start position:0%
search engines calculators translation
 

00:06:06.479 --> 00:06:08.930 align:start position:0%
search engines calculators translation
systems<00:06:07.020><c> by</c><00:06:07.199><c> a</c><00:06:07.380><c> simple</c><00:06:07.500><c> API</c><00:06:08.039><c> calls</c><00:06:08.580><c> and</c><00:06:08.880><c> then</c>

00:06:08.930 --> 00:06:08.940 align:start position:0%
systems by a simple API calls and then
 

00:06:08.940 --> 00:06:11.330 align:start position:0%
systems by a simple API calls and then
we<00:06:09.060><c> can</c><00:06:09.180><c> even</c><00:06:09.720><c> ask</c><00:06:09.960><c> it</c><00:06:10.199><c> more</c><00:06:10.380><c> in</c><00:06:10.680><c> the</c><00:06:10.919><c> different</c>

00:06:11.330 --> 00:06:11.340 align:start position:0%
we can even ask it more in the different
 

00:06:11.340 --> 00:06:13.550 align:start position:0%
we can even ask it more in the different
examples<00:06:11.940><c> and</c><00:06:12.180><c> stuff</c><00:06:12.419><c> so</c><00:06:12.720><c> this</c><00:06:13.080><c> is</c><00:06:13.199><c> actually</c><00:06:13.320><c> a</c>

00:06:13.550 --> 00:06:13.560 align:start position:0%
examples and stuff so this is actually a
 

00:06:13.560 --> 00:06:16.189 align:start position:0%
examples and stuff so this is actually a
good<00:06:13.919><c> way</c><00:06:14.280><c> to</c><00:06:15.060><c> if</c><00:06:15.419><c> you've</c><00:06:15.600><c> gone</c><00:06:15.840><c> through</c><00:06:16.020><c> and</c>

00:06:16.189 --> 00:06:16.199 align:start position:0%
good way to if you've gone through and
 

00:06:16.199 --> 00:06:17.870 align:start position:0%
good way to if you've gone through and
skimmed<00:06:16.620><c> a</c><00:06:16.740><c> paper</c><00:06:16.919><c> and</c><00:06:17.280><c> you</c><00:06:17.400><c> want</c><00:06:17.580><c> to</c><00:06:17.759><c> actually</c>

00:06:17.870 --> 00:06:17.880 align:start position:0%
skimmed a paper and you want to actually
 

00:06:17.880 --> 00:06:19.790 align:start position:0%
skimmed a paper and you want to actually
ask<00:06:18.180><c> some</c><00:06:18.419><c> specific</c><00:06:18.720><c> questions</c><00:06:19.080><c> you</c><00:06:19.440><c> can</c><00:06:19.560><c> get</c>

00:06:19.790 --> 00:06:19.800 align:start position:0%
ask some specific questions you can get
 

00:06:19.800 --> 00:06:22.730 align:start position:0%
ask some specific questions you can get
some<00:06:20.100><c> things</c><00:06:20.580><c> out</c><00:06:21.000><c> of</c><00:06:21.120><c> this</c><00:06:21.380><c> it's</c><00:06:22.380><c> interesting</c>

00:06:22.730 --> 00:06:22.740 align:start position:0%
some things out of this it's interesting
 

00:06:22.740 --> 00:06:25.309 align:start position:0%
some things out of this it's interesting
when<00:06:23.100><c> we</c><00:06:23.340><c> ask</c><00:06:23.819><c> it</c><00:06:24.000><c> this</c><00:06:24.300><c> question</c><00:06:24.539><c> though</c><00:06:24.960><c> it's</c>

00:06:25.309 --> 00:06:25.319 align:start position:0%
when we ask it this question though it's
 

00:06:25.319 --> 00:06:27.290 align:start position:0%
when we ask it this question though it's
also<00:06:25.620><c> getting</c><00:06:25.919><c> its</c><00:06:26.699><c> answer</c><00:06:26.819><c> from</c><00:06:27.120><c> the</c>

00:06:27.290 --> 00:06:27.300 align:start position:0%
also getting its answer from the
 

00:06:27.300 --> 00:06:30.230 align:start position:0%
also getting its answer from the
augmenting<00:06:27.900><c> llms</c><00:06:28.620><c> paper</c><00:06:29.039><c> which</c><00:06:29.520><c> I</c><00:06:29.699><c> think</c><00:06:30.000><c> from</c>

00:06:30.230 --> 00:06:30.240 align:start position:0%
augmenting llms paper which I think from
 

00:06:30.240 --> 00:06:32.510 align:start position:0%
augmenting llms paper which I think from
memory<00:06:30.600><c> also</c><00:06:31.020><c> is</c><00:06:31.199><c> this</c><00:06:31.680><c> is</c><00:06:31.800><c> actually</c><00:06:31.979><c> a</c><00:06:32.220><c> survey</c>

00:06:32.510 --> 00:06:32.520 align:start position:0%
memory also is this is actually a survey
 

00:06:32.520 --> 00:06:35.450 align:start position:0%
memory also is this is actually a survey
paper<00:06:32.780><c> so</c><00:06:33.780><c> it</c><00:06:33.960><c> can</c><00:06:34.080><c> take</c><00:06:34.199><c> some</c><00:06:34.979><c> things</c><00:06:35.160><c> about</c>

00:06:35.450 --> 00:06:35.460 align:start position:0%
paper so it can take some things about
 

00:06:35.460 --> 00:06:38.210 align:start position:0%
paper so it can take some things about
tool<00:06:36.060><c> former</c><00:06:36.419><c> in</c><00:06:36.600><c> there</c><00:06:36.780><c> as</c><00:06:37.020><c> well</c><00:06:37.199><c> so</c><00:06:37.919><c> it's</c>

00:06:38.210 --> 00:06:38.220 align:start position:0%
tool former in there as well so it's
 

00:06:38.220 --> 00:06:40.550 align:start position:0%
tool former in there as well so it's
basically<00:06:38.639><c> gone</c><00:06:39.000><c> and</c><00:06:39.300><c> looked</c><00:06:39.720><c> and</c><00:06:40.080><c> decided</c>

00:06:40.550 --> 00:06:40.560 align:start position:0%
basically gone and looked and decided
 

00:06:40.560 --> 00:06:42.650 align:start position:0%
basically gone and looked and decided
the<00:06:40.740><c> top</c><00:06:40.919><c> three</c><00:06:41.100><c> contexts</c><00:06:41.759><c> were</c><00:06:42.060><c> from</c><00:06:42.419><c> the</c>

00:06:42.650 --> 00:06:42.660 align:start position:0%
the top three contexts were from the
 

00:06:42.660 --> 00:06:44.870 align:start position:0%
the top three contexts were from the
survey<00:06:43.020><c> paper</c><00:06:43.199><c> two</c><00:06:43.919><c> form</c><00:06:44.220><c> of</c><00:06:44.400><c> paper</c><00:06:44.520><c> itself</c>

00:06:44.870 --> 00:06:44.880 align:start position:0%
survey paper two form of paper itself
 

00:06:44.880 --> 00:06:46.189 align:start position:0%
survey paper two form of paper itself
and<00:06:45.060><c> then</c><00:06:45.180><c> another</c><00:06:45.300><c> one</c><00:06:45.539><c> from</c><00:06:45.720><c> the</c><00:06:45.840><c> survey</c>

00:06:46.189 --> 00:06:46.199 align:start position:0%
and then another one from the survey
 

00:06:46.199 --> 00:06:48.529 align:start position:0%
and then another one from the survey
paper<00:06:46.440><c> if</c><00:06:47.280><c> we</c><00:06:47.340><c> ask</c><00:06:47.520><c> it</c><00:06:47.699><c> some</c><00:06:47.940><c> questions</c><00:06:48.120><c> about</c>

00:06:48.529 --> 00:06:48.539 align:start position:0%
paper if we ask it some questions about
 

00:06:48.539 --> 00:06:51.290 align:start position:0%
paper if we ask it some questions about
retrieval<00:06:49.440><c> augmentation</c><00:06:50.100><c> now</c><00:06:50.880><c> the</c><00:06:51.180><c> only</c>

00:06:51.290 --> 00:06:51.300 align:start position:0%
retrieval augmentation now the only
 

00:06:51.300 --> 00:06:52.790 align:start position:0%
retrieval augmentation now the only
paper<00:06:51.479><c> that</c><00:06:51.720><c> we've</c><00:06:51.900><c> got</c><00:06:52.080><c> that</c><00:06:52.319><c> relates</c><00:06:52.620><c> to</c>

00:06:52.790 --> 00:06:52.800 align:start position:0%
paper that we've got that relates to
 

00:06:52.800 --> 00:06:55.610 align:start position:0%
paper that we've got that relates to
this<00:06:53.039><c> is</c><00:06:53.520><c> in</c><00:06:53.940><c> the</c><00:06:54.060><c> augmenting</c><00:06:54.539><c> llm</c><00:06:55.080><c> survey</c>

00:06:55.610 --> 00:06:55.620 align:start position:0%
this is in the augmenting llm survey
 

00:06:55.620 --> 00:06:58.189 align:start position:0%
this is in the augmenting llm survey
sure<00:06:56.460><c> enough</c><00:06:56.639><c> it's</c><00:06:56.940><c> able</c><00:06:57.240><c> to</c><00:06:57.360><c> get</c><00:06:57.840><c> some</c><00:06:58.080><c> of</c>

00:06:58.189 --> 00:06:58.199 align:start position:0%
sure enough it's able to get some of
 

00:06:58.199 --> 00:07:00.590 align:start position:0%
sure enough it's able to get some of
those<00:06:58.319><c> if</c><00:06:58.680><c> we</c><00:06:58.800><c> ask</c><00:06:59.039><c> it</c><00:06:59.340><c> some</c><00:06:59.880><c> specifics</c><00:07:00.419><c> about</c>

00:07:00.590 --> 00:07:00.600 align:start position:0%
those if we ask it some specifics about
 

00:07:00.600 --> 00:07:03.590 align:start position:0%
those if we ask it some specifics about
the<00:07:00.960><c> differences</c><00:07:01.380><c> between</c><00:07:01.880><c> realm</c><00:07:02.880><c> and</c><00:07:03.180><c> rag</c>

00:07:03.590 --> 00:07:03.600 align:start position:0%
the differences between realm and rag
 

00:07:03.600 --> 00:07:05.990 align:start position:0%
the differences between realm and rag
models<00:07:04.259><c> it's</c><00:07:04.680><c> able</c><00:07:04.979><c> to</c><00:07:05.100><c> then</c><00:07:05.280><c> tell</c><00:07:05.520><c> us</c><00:07:05.639><c> these</c>

00:07:05.990 --> 00:07:06.000 align:start position:0%
models it's able to then tell us these
 

00:07:06.000 --> 00:07:09.290 align:start position:0%
models it's able to then tell us these
kind<00:07:06.120><c> of</c><00:07:06.240><c> things</c><00:07:06.440><c> so</c><00:07:07.440><c> the</c><00:07:08.039><c> idea</c><00:07:08.340><c> here</c><00:07:08.639><c> is</c><00:07:08.880><c> that</c>

00:07:09.290 --> 00:07:09.300 align:start position:0%
kind of things so the idea here is that
 

00:07:09.300 --> 00:07:13.249 align:start position:0%
kind of things so the idea here is that
we're<00:07:09.780><c> still</c><00:07:10.020><c> using</c><00:07:10.380><c> open</c><00:07:10.680><c> AI</c><00:07:11.419><c> for</c><00:07:12.419><c> the</c><00:07:12.960><c> actual</c>

00:07:13.249 --> 00:07:13.259 align:start position:0%
we're still using open AI for the actual
 

00:07:13.259 --> 00:07:15.110 align:start position:0%
we're still using open AI for the actual
language<00:07:13.440><c> model</c><00:07:13.860><c> part</c><00:07:14.220><c> in</c><00:07:14.639><c> the</c><00:07:14.759><c> next</c><00:07:14.880><c> video</c>

00:07:15.110 --> 00:07:15.120 align:start position:0%
language model part in the next video
 

00:07:15.120 --> 00:07:17.029 align:start position:0%
language model part in the next video
we'll<00:07:15.539><c> have</c><00:07:16.020><c> a</c><00:07:16.139><c> look</c><00:07:16.199><c> at</c><00:07:16.380><c> trying</c><00:07:16.560><c> to</c><00:07:16.740><c> get</c><00:07:16.919><c> rid</c>

00:07:17.029 --> 00:07:17.039 align:start position:0%
we'll have a look at trying to get rid
 

00:07:17.039 --> 00:07:19.129 align:start position:0%
we'll have a look at trying to get rid
of<00:07:17.220><c> that</c><00:07:17.340><c> and</c><00:07:17.639><c> just</c><00:07:17.819><c> go</c><00:07:18.000><c> to</c><00:07:18.180><c> fully</c><00:07:18.660><c> running</c>

00:07:19.129 --> 00:07:19.139 align:start position:0%
of that and just go to fully running
 

00:07:19.139 --> 00:07:22.430 align:start position:0%
of that and just go to fully running
everything<00:07:19.500><c> locally</c><00:07:20.099><c> but</c><00:07:21.060><c> we're</c><00:07:21.599><c> now</c><00:07:21.840><c> using</c>

00:07:22.430 --> 00:07:22.440 align:start position:0%
everything locally but we're now using
 

00:07:22.440 --> 00:07:26.809 align:start position:0%
everything locally but we're now using
the<00:07:22.740><c> embedding</c><00:07:23.280><c> system</c><00:07:23.840><c> for</c><00:07:24.840><c> actually</c><00:07:25.819><c> using</c>

00:07:26.809 --> 00:07:26.819 align:start position:0%
the embedding system for actually using
 

00:07:26.819 --> 00:07:28.490 align:start position:0%
the embedding system for actually using
the<00:07:26.940><c> instruction</c><00:07:27.060><c> better</c><00:07:27.599><c> we're</c><00:07:28.139><c> not</c><00:07:28.259><c> using</c>

00:07:28.490 --> 00:07:28.500 align:start position:0%
the instruction better we're not using
 

00:07:28.500 --> 00:07:30.529 align:start position:0%
the instruction better we're not using
open<00:07:28.620><c> AI</c><00:07:29.099><c> for</c><00:07:29.280><c> this</c><00:07:29.460><c> so</c><00:07:29.819><c> the</c><00:07:29.940><c> big</c><00:07:30.120><c> Advantage</c>

00:07:30.529 --> 00:07:30.539 align:start position:0%
open AI for this so the big Advantage
 

00:07:30.539 --> 00:07:33.110 align:start position:0%
open AI for this so the big Advantage
for<00:07:30.840><c> this</c><00:07:31.139><c> means</c><00:07:31.860><c> that</c><00:07:32.160><c> your</c><00:07:32.520><c> data</c><00:07:32.880><c> never</c>

00:07:33.110 --> 00:07:33.120 align:start position:0%
for this means that your data never
 

00:07:33.120 --> 00:07:36.230 align:start position:0%
for this means that your data never
actually<00:07:33.479><c> has</c><00:07:33.840><c> to</c><00:07:34.020><c> go</c><00:07:34.139><c> all</c><00:07:34.919><c> of</c><00:07:35.099><c> it</c><00:07:35.220><c> go</c><00:07:35.520><c> up</c><00:07:35.819><c> to</c>

00:07:36.230 --> 00:07:36.240 align:start position:0%
actually has to go all of it go up to
 

00:07:36.240 --> 00:07:39.110 align:start position:0%
actually has to go all of it go up to
the<00:07:36.660><c> large</c><00:07:36.780><c> language</c><00:07:37.199><c> model</c><00:07:37.560><c> to</c><00:07:37.800><c> open</c><00:07:37.979><c> AI</c><00:07:38.460><c> now</c>

00:07:39.110 --> 00:07:39.120 align:start position:0%
the large language model to open AI now
 

00:07:39.120 --> 00:07:41.629 align:start position:0%
the large language model to open AI now
obviously<00:07:39.599><c> the</c><00:07:40.139><c> context</c><00:07:40.560><c> as</c><00:07:40.979><c> they</c><00:07:41.220><c> come</c><00:07:41.400><c> out</c>

00:07:41.629 --> 00:07:41.639 align:start position:0%
obviously the context as they come out
 

00:07:41.639 --> 00:07:45.110 align:start position:0%
obviously the context as they come out
are<00:07:42.180><c> still</c><00:07:42.360><c> going</c><00:07:42.599><c> up</c><00:07:42.900><c> to</c><00:07:43.139><c> open</c><00:07:43.380><c> AI</c><00:07:43.919><c> so</c><00:07:44.819><c> it's</c>

00:07:45.110 --> 00:07:45.120 align:start position:0%
are still going up to open AI so it's
 

00:07:45.120 --> 00:07:47.150 align:start position:0%
are still going up to open AI so it's
not<00:07:45.300><c> like</c><00:07:45.599><c> none</c><00:07:46.139><c> of</c><00:07:46.259><c> your</c><00:07:46.319><c> data</c><00:07:46.680><c> is</c><00:07:46.800><c> going</c><00:07:46.979><c> up</c>

00:07:47.150 --> 00:07:47.160 align:start position:0%
not like none of your data is going up
 

00:07:47.160 --> 00:07:49.790 align:start position:0%
not like none of your data is going up
but<00:07:47.460><c> it's</c><00:07:47.699><c> going</c><00:07:48.120><c> all</c><00:07:48.419><c> up</c><00:07:48.660><c> in</c><00:07:49.080><c> one</c><00:07:49.259><c> shot</c><00:07:49.440><c> just</c>

00:07:49.790 --> 00:07:49.800 align:start position:0%
but it's going all up in one shot just
 

00:07:49.800 --> 00:07:52.249 align:start position:0%
but it's going all up in one shot just
to<00:07:49.979><c> do</c><00:07:50.160><c> embeddings</c><00:07:50.819><c> for</c><00:07:51.599><c> this</c><00:07:51.840><c> kind</c><00:07:51.960><c> of</c><00:07:52.139><c> thing</c>

00:07:52.249 --> 00:07:52.259 align:start position:0%
to do embeddings for this kind of thing
 

00:07:52.259 --> 00:07:54.350 align:start position:0%
to do embeddings for this kind of thing
but<00:07:52.979><c> the</c><00:07:53.160><c> key</c><00:07:53.220><c> thing</c><00:07:53.400><c> is</c><00:07:53.580><c> it's</c><00:07:53.880><c> not</c><00:07:54.180><c> just</c>

00:07:54.350 --> 00:07:54.360 align:start position:0%
but the key thing is it's not just
 

00:07:54.360 --> 00:07:57.050 align:start position:0%
but the key thing is it's not just
putting<00:07:54.780><c> all</c><00:07:55.080><c> your</c><00:07:55.259><c> data</c><00:07:55.680><c> up</c><00:07:55.919><c> as</c><00:07:56.520><c> it's</c><00:07:56.759><c> doing</c>

00:07:57.050 --> 00:07:57.060 align:start position:0%
putting all your data up as it's doing
 

00:07:57.060 --> 00:08:00.170 align:start position:0%
putting all your data up as it's doing
the<00:07:57.419><c> embeddings</c><00:07:57.960><c> in</c><00:07:58.740><c> one</c><00:07:58.979><c> shot</c><00:07:59.220><c> so</c><00:07:59.699><c> you</c><00:08:00.060><c> do</c>

00:08:00.170 --> 00:08:00.180 align:start position:0%
the embeddings in one shot so you do
 

00:08:00.180 --> 00:08:02.089 align:start position:0%
the embeddings in one shot so you do
have<00:08:00.300><c> a</c><00:08:00.539><c> little</c><00:08:00.599><c> bit</c><00:08:00.720><c> more</c><00:08:00.960><c> privacy</c><00:08:01.500><c> here</c><00:08:01.800><c> in</c>

00:08:02.089 --> 00:08:02.099 align:start position:0%
have a little bit more privacy here in
 

00:08:02.099 --> 00:08:04.309 align:start position:0%
have a little bit more privacy here in
doing<00:08:02.280><c> it</c><00:08:02.520><c> this</c><00:08:02.759><c> way</c><00:08:02.940><c> of</c><00:08:03.660><c> course</c><00:08:03.840><c> this</c><00:08:04.139><c> is</c>

00:08:04.309 --> 00:08:04.319 align:start position:0%
doing it this way of course this is
 

00:08:04.319 --> 00:08:06.770 align:start position:0%
doing it this way of course this is
still<00:08:04.500><c> not</c><00:08:04.800><c> ideal</c><00:08:05.340><c> if</c><00:08:05.880><c> we</c><00:08:06.060><c> want</c><00:08:06.240><c> to</c><00:08:06.419><c> basically</c>

00:08:06.770 --> 00:08:06.780 align:start position:0%
still not ideal if we want to basically
 

00:08:06.780 --> 00:08:10.309 align:start position:0%
still not ideal if we want to basically
never<00:08:07.139><c> have</c><00:08:07.440><c> our</c><00:08:07.740><c> data</c><00:08:08.280><c> touch</c><00:08:08.699><c> a</c><00:08:09.000><c> server</c><00:08:09.360><c> so</c><00:08:10.080><c> in</c>

00:08:10.309 --> 00:08:10.319 align:start position:0%
never have our data touch a server so in
 

00:08:10.319 --> 00:08:12.830 align:start position:0%
never have our data touch a server so in
the<00:08:10.440><c> next</c><00:08:10.620><c> video</c><00:08:10.800><c> we'll</c><00:08:11.220><c> look</c><00:08:11.460><c> at</c><00:08:11.699><c> using</c><00:08:12.240><c> an</c>

00:08:12.830 --> 00:08:12.840 align:start position:0%
the next video we'll look at using an
 

00:08:12.840 --> 00:08:16.189 align:start position:0%
the next video we'll look at using an
actual<00:08:13.139><c> language</c><00:08:13.380><c> model</c><00:08:13.800><c> to</c><00:08:14.639><c> do</c><00:08:14.880><c> the</c><00:08:15.539><c> replying</c>

00:08:16.189 --> 00:08:16.199 align:start position:0%
actual language model to do the replying
 

00:08:16.199 --> 00:08:19.010 align:start position:0%
actual language model to do the replying
part<00:08:16.500><c> as</c><00:08:16.919><c> well</c><00:08:17.099><c> as</c><00:08:17.520><c> just</c><00:08:18.180><c> the</c><00:08:18.360><c> as</c><00:08:18.599><c> well</c><00:08:18.720><c> as</c><00:08:18.840><c> the</c>

00:08:19.010 --> 00:08:19.020 align:start position:0%
part as well as just the as well as the
 

00:08:19.020 --> 00:08:20.689 align:start position:0%
part as well as just the as well as the
embedding<00:08:19.500><c> part</c><00:08:19.680><c> here</c>

00:08:20.689 --> 00:08:20.699 align:start position:0%
embedding part here
 

00:08:20.699 --> 00:08:22.490 align:start position:0%
embedding part here
okay<00:08:21.060><c> the</c><00:08:21.300><c> rest</c><00:08:21.419><c> of</c><00:08:21.539><c> the</c><00:08:21.599><c> notebook</c><00:08:21.960><c> is</c><00:08:22.199><c> the</c>

00:08:22.490 --> 00:08:22.500 align:start position:0%
okay the rest of the notebook is the
 

00:08:22.500 --> 00:08:24.529 align:start position:0%
okay the rest of the notebook is the
same<00:08:22.620><c> just</c><00:08:22.919><c> going</c><00:08:23.160><c> through</c><00:08:23.460><c> deleting</c><00:08:24.240><c> the</c>

00:08:24.529 --> 00:08:24.539 align:start position:0%
same just going through deleting the
 

00:08:24.539 --> 00:08:27.409 align:start position:0%
same just going through deleting the
chroma<00:08:24.840><c> DB</c><00:08:25.379><c> database</c><00:08:26.039><c> and</c><00:08:26.580><c> and</c><00:08:26.759><c> bringing</c><00:08:27.240><c> that</c>

00:08:27.409 --> 00:08:27.419 align:start position:0%
chroma DB database and and bringing that
 

00:08:27.419 --> 00:08:29.150 align:start position:0%
chroma DB database and and bringing that
back<00:08:27.660><c> in</c><00:08:27.900><c> that's</c><00:08:28.440><c> the</c><00:08:28.680><c> same</c><00:08:28.800><c> as</c><00:08:28.919><c> what</c><00:08:29.039><c> we</c>

00:08:29.150 --> 00:08:29.160 align:start position:0%
back in that's the same as what we
 

00:08:29.160 --> 00:08:30.589 align:start position:0%
back in that's the same as what we
looked<00:08:29.400><c> at</c><00:08:29.460><c> before</c><00:08:29.639><c> if</c><00:08:29.940><c> you</c><00:08:30.060><c> want</c><00:08:30.180><c> to</c><00:08:30.300><c> try</c><00:08:30.419><c> out</c>

00:08:30.589 --> 00:08:30.599 align:start position:0%
looked at before if you want to try out
 

00:08:30.599 --> 00:08:34.909 align:start position:0%
looked at before if you want to try out
using<00:08:31.199><c> just</c><00:08:31.379><c> the</c><00:08:31.560><c> open</c><00:08:31.680><c> AI</c><00:08:32.039><c> GPT</c><00:08:32.520><c> 3.5</c><00:08:33.479><c> turbo</c><00:08:34.380><c> you</c>

00:08:34.909 --> 00:08:34.919 align:start position:0%
using just the open AI GPT 3.5 turbo you
 

00:08:34.919 --> 00:08:36.290 align:start position:0%
using just the open AI GPT 3.5 turbo you
can<00:08:35.039><c> do</c><00:08:35.159><c> that</c><00:08:35.339><c> here</c>

00:08:36.290 --> 00:08:36.300 align:start position:0%
can do that here
 

00:08:36.300 --> 00:08:38.990 align:start position:0%
can do that here
that's<00:08:36.779><c> it</c><00:08:36.959><c> for</c><00:08:37.260><c> this</c><00:08:37.380><c> notebook</c><00:08:37.740><c> uh</c><00:08:38.399><c> as</c><00:08:38.880><c> always</c>

00:08:38.990 --> 00:08:39.000 align:start position:0%
that's it for this notebook uh as always
 

00:08:39.000 --> 00:08:41.810 align:start position:0%
that's it for this notebook uh as always
if<00:08:39.240><c> you've</c><00:08:39.479><c> got</c><00:08:39.659><c> any</c><00:08:40.080><c> questions</c><00:08:40.740><c> please</c><00:08:41.580><c> put</c>

00:08:41.810 --> 00:08:41.820 align:start position:0%
if you've got any questions please put
 

00:08:41.820 --> 00:08:43.670 align:start position:0%
if you've got any questions please put
them<00:08:41.940><c> in</c><00:08:42.060><c> the</c><00:08:42.180><c> comments</c><00:08:42.419><c> below</c><00:08:42.779><c> if</c><00:08:43.320><c> you</c><00:08:43.560><c> found</c>

00:08:43.670 --> 00:08:43.680 align:start position:0%
them in the comments below if you found
 

00:08:43.680 --> 00:08:45.230 align:start position:0%
them in the comments below if you found
this<00:08:43.860><c> useful</c><00:08:44.219><c> please</c><00:08:44.580><c> click</c><00:08:44.880><c> like</c><00:08:45.000><c> And</c>

00:08:45.230 --> 00:08:45.240 align:start position:0%
this useful please click like And
 

00:08:45.240 --> 00:08:48.290 align:start position:0%
this useful please click like And
subscribe<00:08:45.720><c> in</c><00:08:46.620><c> the</c><00:08:46.740><c> next</c><00:08:46.920><c> video</c><00:08:47.279><c> we</c><00:08:47.820><c> will</c><00:08:48.000><c> look</c>

00:08:48.290 --> 00:08:48.300 align:start position:0%
subscribe in the next video we will look
 

00:08:48.300 --> 00:08:51.230 align:start position:0%
subscribe in the next video we will look
at<00:08:48.720><c> using</c><00:08:49.500><c> custom</c><00:08:50.100><c> models</c><00:08:50.640><c> for</c><00:08:50.940><c> everything</c>

00:08:51.230 --> 00:08:51.240 align:start position:0%
at using custom models for everything
 

00:08:51.240 --> 00:08:54.710 align:start position:0%
at using custom models for everything
for<00:08:52.140><c> this</c><00:08:52.680><c> so</c><00:08:53.399><c> okay</c><00:08:53.760><c> I</c><00:08:54.180><c> will</c><00:08:54.300><c> talk</c><00:08:54.420><c> to</c><00:08:54.540><c> you</c><00:08:54.600><c> in</c>

00:08:54.710 --> 00:08:54.720 align:start position:0%
for this so okay I will talk to you in
 

00:08:54.720 --> 00:08:58.040 align:start position:0%
for this so okay I will talk to you in
the<00:08:54.839><c> next</c><00:08:54.959><c> video</c><00:08:55.140><c> bye</c><00:08:55.680><c> for</c><00:08:55.800><c> now</c>

