WEBVTT
Kind: captions
Language: en

00:00:00.520 --> 00:00:02.550 align:start position:0%
 
last<00:00:00.799><c> week</c><00:00:01.280><c> I</c><00:00:01.439><c> found</c><00:00:01.719><c> myself</c><00:00:02.040><c> working</c><00:00:02.320><c> with</c><00:00:02.440><c> a</c>

00:00:02.550 --> 00:00:02.560 align:start position:0%
last week I found myself working with a
 

00:00:02.560 --> 00:00:04.910 align:start position:0%
last week I found myself working with a
company<00:00:02.879><c> managing</c><00:00:03.280><c> over</c><00:00:03.759><c> 25,000</c>

00:00:04.910 --> 00:00:04.920 align:start position:0%
company managing over 25,000
 

00:00:04.920 --> 00:00:06.990 align:start position:0%
company managing over 25,000
repositories<00:00:05.920><c> they</c><00:00:06.040><c> needed</c><00:00:06.319><c> to</c><00:00:06.440><c> be</c><00:00:06.520><c> able</c><00:00:06.720><c> to</c>

00:00:06.990 --> 00:00:07.000 align:start position:0%
repositories they needed to be able to
 

00:00:07.000 --> 00:00:09.190 align:start position:0%
repositories they needed to be able to
automatically<00:00:07.640><c> apply</c><00:00:08.120><c> policies</c><00:00:08.800><c> security</c>

00:00:09.190 --> 00:00:09.200 align:start position:0%
automatically apply policies security
 

00:00:09.200 --> 00:00:11.749 align:start position:0%
automatically apply policies security
rules<00:00:09.559><c> and</c><00:00:09.800><c> other</c><00:00:10.040><c> checks</c><00:00:10.320><c> to</c><00:00:10.480><c> these</c><00:00:10.639><c> repos</c><00:00:11.240><c> at</c>

00:00:11.749 --> 00:00:11.759 align:start position:0%
rules and other checks to these repos at
 

00:00:11.759 --> 00:00:14.749 align:start position:0%
rules and other checks to these repos at
scale<00:00:12.759><c> but</c><00:00:13.400><c> these</c><00:00:13.599><c> checks</c><00:00:13.920><c> needed</c><00:00:14.200><c> to</c><00:00:14.360><c> be</c>

00:00:14.749 --> 00:00:14.759 align:start position:0%
scale but these checks needed to be
 

00:00:14.759 --> 00:00:17.310 align:start position:0%
scale but these checks needed to be
selectively<00:00:15.400><c> targeted</c><00:00:16.080><c> for</c><00:00:16.279><c> example</c><00:00:16.920><c> if</c><00:00:17.039><c> a</c>

00:00:17.310 --> 00:00:17.320 align:start position:0%
selectively targeted for example if a
 

00:00:17.320 --> 00:00:19.630 align:start position:0%
selectively targeted for example if a
repository<00:00:17.920><c> contained</c><00:00:18.320><c> only</c><00:00:18.640><c> backend</c><00:00:19.119><c> code</c>

00:00:19.630 --> 00:00:19.640 align:start position:0%
repository contained only backend code
 

00:00:19.640 --> 00:00:21.029 align:start position:0%
repository contained only backend code
there<00:00:19.760><c> wouldn't</c><00:00:20.000><c> be</c><00:00:20.119><c> a</c><00:00:20.279><c> point</c><00:00:20.600><c> in</c><00:00:20.760><c> running</c>

00:00:21.029 --> 00:00:21.039 align:start position:0%
there wouldn't be a point in running
 

00:00:21.039 --> 00:00:23.390 align:start position:0%
there wouldn't be a point in running
their<00:00:21.240><c> front-end</c><00:00:21.680><c> test</c><00:00:22.000><c> Suite</c><00:00:23.000><c> or</c><00:00:23.279><c> if</c>

00:00:23.390 --> 00:00:23.400 align:start position:0%
their front-end test Suite or if
 

00:00:23.400 --> 00:00:24.589 align:start position:0%
their front-end test Suite or if
something<00:00:23.640><c> was</c><00:00:23.800><c> going</c><00:00:23.880><c> to</c><00:00:24.039><c> Shi</c><00:00:24.320><c> to</c><00:00:24.439><c> a</c>

00:00:24.589 --> 00:00:24.599 align:start position:0%
something was going to Shi to a
 

00:00:24.599 --> 00:00:26.269 align:start position:0%
something was going to Shi to a
production<00:00:25.080><c> environment</c><00:00:25.760><c> they'd</c><00:00:26.039><c> have</c><00:00:26.160><c> a</c>

00:00:26.269 --> 00:00:26.279 align:start position:0%
production environment they'd have a
 

00:00:26.279 --> 00:00:28.429 align:start position:0%
production environment they'd have a
whole<00:00:26.599><c> set</c><00:00:26.840><c> of</c><00:00:27.080><c> security</c><00:00:27.519><c> scans</c><00:00:28.000><c> they</c><00:00:28.119><c> wanted</c>

00:00:28.429 --> 00:00:28.439 align:start position:0%
whole set of security scans they wanted
 

00:00:28.439 --> 00:00:30.990 align:start position:0%
whole set of security scans they wanted
to<00:00:28.599><c> run</c><00:00:28.920><c> on</c><00:00:29.080><c> it</c><00:00:29.519><c> and</c><00:00:29.640><c> so</c><00:00:29.800><c> on</c>

00:00:30.990 --> 00:00:31.000 align:start position:0%
to run on it and so on
 

00:00:31.000 --> 00:00:33.670 align:start position:0%
to run on it and so on
fortunately<00:00:31.480><c> for</c><00:00:31.679><c> them</c><00:00:32.079><c> and</c><00:00:32.279><c> for</c><00:00:32.559><c> you</c><00:00:33.320><c> we've</c>

00:00:33.670 --> 00:00:33.680 align:start position:0%
fortunately for them and for you we've
 

00:00:33.680 --> 00:00:36.350 align:start position:0%
fortunately for them and for you we've
just<00:00:33.920><c> added</c><00:00:34.440><c> custom</c><00:00:35.120><c> properties</c><00:00:35.559><c> to</c><00:00:35.719><c> GitHub</c>

00:00:36.350 --> 00:00:36.360 align:start position:0%
just added custom properties to GitHub
 

00:00:36.360 --> 00:00:38.990 align:start position:0%
just added custom properties to GitHub
repositories<00:00:37.360><c> this</c><00:00:37.480><c> lets</c><00:00:37.680><c> you</c><00:00:37.960><c> decide</c><00:00:38.600><c> at</c><00:00:38.800><c> the</c>

00:00:38.990 --> 00:00:39.000 align:start position:0%
repositories this lets you decide at the
 

00:00:39.000 --> 00:00:42.029 align:start position:0%
repositories this lets you decide at the
org<00:00:39.640><c> level</c><00:00:40.399><c> what</c><00:00:40.640><c> custom</c><00:00:41.079><c> information</c><00:00:41.840><c> should</c>

00:00:42.029 --> 00:00:42.039 align:start position:0%
org level what custom information should
 

00:00:42.039 --> 00:00:44.950 align:start position:0%
org level what custom information should
be<00:00:42.320><c> defined</c><00:00:43.000><c> for</c><00:00:43.239><c> each</c><00:00:43.640><c> repository</c><00:00:44.640><c> these</c><00:00:44.800><c> can</c>

00:00:44.950 --> 00:00:44.960 align:start position:0%
be defined for each repository these can
 

00:00:44.960 --> 00:00:47.430 align:start position:0%
be defined for each repository these can
be<00:00:45.320><c> optional</c><00:00:46.000><c> or</c><00:00:46.160><c> they</c><00:00:46.280><c> could</c><00:00:46.399><c> be</c><00:00:46.559><c> mandatory</c>

00:00:47.430 --> 00:00:47.440 align:start position:0%
be optional or they could be mandatory
 

00:00:47.440 --> 00:00:49.590 align:start position:0%
be optional or they could be mandatory
so<00:00:47.680><c> for</c><00:00:47.840><c> example</c><00:00:48.280><c> you</c><00:00:48.360><c> could</c><00:00:48.640><c> require</c><00:00:49.320><c> every</c>

00:00:49.590 --> 00:00:49.600 align:start position:0%
so for example you could require every
 

00:00:49.600 --> 00:00:51.670 align:start position:0%
so for example you could require every
repo<00:00:50.320><c> to</c><00:00:50.520><c> have</c><00:00:50.640><c> a</c><00:00:50.879><c> property</c><00:00:51.239><c> called</c>

00:00:51.670 --> 00:00:51.680 align:start position:0%
repo to have a property called
 

00:00:51.680 --> 00:00:53.630 align:start position:0%
repo to have a property called
environment<00:00:52.520><c> and</c><00:00:52.680><c> a</c><00:00:52.879><c> value</c><00:00:53.239><c> of</c><00:00:53.399><c> either</c>

00:00:53.630 --> 00:00:53.640 align:start position:0%
environment and a value of either
 

00:00:53.640 --> 00:00:55.270 align:start position:0%
environment and a value of either
development<00:00:54.359><c> or</c>

00:00:55.270 --> 00:00:55.280 align:start position:0%
development or
 

00:00:55.280 --> 00:00:57.709 align:start position:0%
development or
production<00:00:56.280><c> now</c><00:00:56.520><c> once</c><00:00:56.800><c> these</c><00:00:57.079><c> properties</c><00:00:57.440><c> are</c>

00:00:57.709 --> 00:00:57.719 align:start position:0%
production now once these properties are
 

00:00:57.719 --> 00:00:59.910 align:start position:0%
production now once these properties are
defined<00:00:58.399><c> you</c><00:00:58.519><c> can</c><00:00:58.719><c> use</c><00:00:58.960><c> them</c><00:00:59.120><c> to</c><00:00:59.320><c> manually</c>

00:00:59.910 --> 00:00:59.920 align:start position:0%
defined you can use them to manually
 

00:00:59.920 --> 00:01:02.150 align:start position:0%
defined you can use them to manually
search<00:01:00.120><c> and</c><00:01:00.239><c> filter</c><00:01:00.559><c> your</c><00:01:00.920><c> repositories</c><00:01:01.920><c> but</c>

00:01:02.150 --> 00:01:02.160 align:start position:0%
search and filter your repositories but
 

00:01:02.160 --> 00:01:04.630 align:start position:0%
search and filter your repositories but
from<00:01:02.320><c> an</c><00:01:02.519><c> automation</c><00:01:03.399><c> perspective</c><00:01:04.280><c> you</c><00:01:04.400><c> can</c>

00:01:04.630 --> 00:01:04.640 align:start position:0%
from an automation perspective you can
 

00:01:04.640 --> 00:01:07.590 align:start position:0%
from an automation perspective you can
create<00:01:05.119><c> rule</c><00:01:05.560><c> sets</c><00:01:06.240><c> which</c><00:01:06.479><c> apply</c><00:01:06.840><c> to</c><00:01:07.000><c> repost</c>

00:01:07.590 --> 00:01:07.600 align:start position:0%
create rule sets which apply to repost
 

00:01:07.600 --> 00:01:10.310 align:start position:0%
create rule sets which apply to repost
based<00:01:08.159><c> on</c><00:01:08.360><c> their</c><00:01:08.720><c> properties</c><00:01:09.720><c> this</c><00:01:09.880><c> lets</c><00:01:10.119><c> you</c>

00:01:10.310 --> 00:01:10.320 align:start position:0%
based on their properties this lets you
 

00:01:10.320 --> 00:01:12.429 align:start position:0%
based on their properties this lets you
say<00:01:10.720><c> things</c><00:01:11.040><c> like</c><00:01:11.439><c> if</c><00:01:11.600><c> this</c><00:01:11.720><c> is</c><00:01:11.840><c> a</c><00:01:12.040><c> production</c>

00:01:12.429 --> 00:01:12.439 align:start position:0%
say things like if this is a production
 

00:01:12.439 --> 00:01:14.670 align:start position:0%
say things like if this is a production
environment<00:01:13.040><c> then</c><00:01:13.280><c> require</c><00:01:13.799><c> signed</c><00:01:14.119><c> commits</c>

00:01:14.670 --> 00:01:14.680 align:start position:0%
environment then require signed commits
 

00:01:14.680 --> 00:01:16.789 align:start position:0%
environment then require signed commits
and<00:01:15.240><c> make</c><00:01:15.479><c> sure</c><00:01:15.680><c> there's</c><00:01:15.920><c> a</c><00:01:16.000><c> full</c><00:01:16.280><c> dependency</c>

00:01:16.789 --> 00:01:16.799 align:start position:0%
and make sure there's a full dependency
 

00:01:16.799 --> 00:01:18.749 align:start position:0%
and make sure there's a full dependency
scan<00:01:17.320><c> every</c><00:01:17.560><c> time</c><00:01:17.799><c> code</c><00:01:18.040><c> is</c>

00:01:18.749 --> 00:01:18.759 align:start position:0%
scan every time code is
 

00:01:18.759 --> 00:01:22.109 align:start position:0%
scan every time code is
merged<00:01:19.759><c> custom</c><00:01:20.240><c> properties</c><00:01:21.119><c> will</c><00:01:21.400><c> take</c><00:01:21.759><c> Mass</c>

00:01:22.109 --> 00:01:22.119 align:start position:0%
merged custom properties will take Mass
 

00:01:22.119 --> 00:01:24.710 align:start position:0%
merged custom properties will take Mass
management<00:01:22.600><c> of</c><00:01:22.759><c> your</c><00:01:23.000><c> repos</c><00:01:23.600><c> to</c><00:01:23.880><c> a</c><00:01:24.119><c> whole</c><00:01:24.400><c> new</c>

00:01:24.710 --> 00:01:24.720 align:start position:0%
management of your repos to a whole new
 

00:01:24.720 --> 00:01:27.310 align:start position:0%
management of your repos to a whole new
level<00:01:25.320><c> allowing</c><00:01:25.720><c> you</c><00:01:25.880><c> to</c><00:01:26.159><c> improve</c><00:01:26.640><c> security</c>

00:01:27.310 --> 00:01:27.320 align:start position:0%
level allowing you to improve security
 

00:01:27.320 --> 00:01:30.590 align:start position:0%
level allowing you to improve security
and<00:01:27.520><c> reliably</c><00:01:28.280><c> enforce</c><00:01:28.840><c> policies</c><00:01:29.479><c> at</c><00:01:29.600><c> the</c><00:01:29.960><c> org</c>

00:01:30.590 --> 00:01:30.600 align:start position:0%
and reliably enforce policies at the org
 

00:01:30.600 --> 00:01:33.550 align:start position:0%
and reliably enforce policies at the org
level<00:01:31.600><c> and</c><00:01:31.920><c> everything</c><00:01:32.240><c> I've</c><00:01:32.439><c> just</c><00:01:32.600><c> shown</c><00:01:32.960><c> you</c>

00:01:33.550 --> 00:01:33.560 align:start position:0%
level and everything I've just shown you
 

00:01:33.560 --> 00:01:35.670 align:start position:0%
level and everything I've just shown you
plus<00:01:33.799><c> the</c><00:01:33.920><c> ability</c><00:01:34.240><c> to</c><00:01:34.439><c> set</c><00:01:34.640><c> and</c><00:01:34.920><c> query</c><00:01:35.399><c> these</c>

00:01:35.670 --> 00:01:35.680 align:start position:0%
plus the ability to set and query these
 

00:01:35.680 --> 00:01:39.109 align:start position:0%
plus the ability to set and query these
properties<00:01:36.119><c> via</c><00:01:36.360><c> API</c><00:01:37.280><c> is</c><00:01:37.479><c> available</c><00:01:38.079><c> now</c><00:01:39.000><c> you</c>

00:01:39.109 --> 00:01:39.119 align:start position:0%
properties via API is available now you
 

00:01:39.119 --> 00:01:41.830 align:start position:0%
properties via API is available now you
can<00:01:39.320><c> find</c><00:01:39.520><c> out</c><00:01:39.759><c> more</c><00:01:40.479><c> by</c><00:01:40.640><c> visiting</c>

00:01:41.830 --> 00:01:41.840 align:start position:0%
can find out more by visiting
 

00:01:41.840 --> 00:01:46.240 align:start position:0%
can find out more by visiting
G.I<00:01:42.840><c> repo</c><00:01:43.360><c> properties</c>

