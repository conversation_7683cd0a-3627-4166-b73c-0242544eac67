WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:03.070 align:start position:0%
 
this<00:00:00.240><c> is</c><00:00:00.359><c> autog</c><00:00:01.040><c> beta</c><00:00:01.319><c> version</c><00:00:01.879><c> 666</c><00:00:02.879><c> because</c>

00:00:03.070 --> 00:00:03.080 align:start position:0%
this is autog beta version 666 because
 

00:00:03.080 --> 00:00:05.030 align:start position:0%
this is autog beta version 666 because
it's<00:00:03.240><c> one</c><00:00:03.439><c> hell</c><00:00:03.639><c> of</c><00:00:03.719><c> an</c><00:00:03.879><c> upgrade</c><00:00:04.600><c> you</c><00:00:04.720><c> wanted</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
it's one hell of an upgrade you wanted
 

00:00:05.040 --> 00:00:06.950 align:start position:0%
it's one hell of an upgrade you wanted
autog<00:00:05.600><c> to</c><00:00:05.680><c> be</c><00:00:05.759><c> able</c><00:00:05.920><c> to</c><00:00:06.040><c> insert</c><00:00:06.399><c> skills</c><00:00:06.759><c> into</c>

00:00:06.950 --> 00:00:06.960 align:start position:0%
autog to be able to insert skills into
 

00:00:06.960 --> 00:00:09.110 align:start position:0%
autog to be able to insert skills into
your<00:00:07.080><c> autogen</c><00:00:07.600><c> agents</c><00:00:08.000><c> and</c><00:00:08.160><c> now</c><00:00:08.320><c> it</c><00:00:08.519><c> can</c>

00:00:09.110 --> 00:00:09.120 align:start position:0%
your autogen agents and now it can
 

00:00:09.120 --> 00:00:11.390 align:start position:0%
your autogen agents and now it can
there's<00:00:09.320><c> no</c><00:00:09.559><c> configuration</c><00:00:10.280><c> required</c><00:00:11.120><c> no</c>

00:00:11.390 --> 00:00:11.400 align:start position:0%
there's no configuration required no
 

00:00:11.400 --> 00:00:13.870 align:start position:0%
there's no configuration required no
code<00:00:11.639><c> to</c><00:00:11.840><c> write</c><00:00:12.519><c> just</c><00:00:12.719><c> drop</c><00:00:12.960><c> a</c><00:00:13.120><c> valid</c><00:00:13.519><c> python</c>

00:00:13.870 --> 00:00:13.880 align:start position:0%
code to write just drop a valid python
 

00:00:13.880 --> 00:00:15.749 align:start position:0%
code to write just drop a valid python
code<00:00:14.160><c> file</c><00:00:14.480><c> into</c><00:00:14.679><c> the</c><00:00:14.799><c> autog</c><00:00:15.160><c> gr</c><00:00:15.400><c> scripts</c>

00:00:15.749 --> 00:00:15.759 align:start position:0%
code file into the autog gr scripts
 

00:00:15.759 --> 00:00:18.349 align:start position:0%
code file into the autog gr scripts
folder<00:00:16.600><c> that's</c><00:00:16.800><c> all</c><00:00:17.000><c> there</c><00:00:17.119><c> is</c><00:00:17.320><c> to</c><00:00:17.520><c> it</c><00:00:18.160><c> okay</c>

00:00:18.349 --> 00:00:18.359 align:start position:0%
folder that's all there is to it okay
 

00:00:18.359 --> 00:00:21.310 align:start position:0%
folder that's all there is to it okay
you<00:00:18.480><c> were</c><00:00:18.800><c> right</c><00:00:19.720><c> AI</c><00:00:20.199><c> voices</c><00:00:20.760><c> can</c><00:00:21.080><c> get</c>

00:00:21.310 --> 00:00:21.320 align:start position:0%
you were right AI voices can get
 

00:00:21.320 --> 00:00:24.150 align:start position:0%
you were right AI voices can get
annoying<00:00:22.199><c> so</c><00:00:22.960><c> instead</c><00:00:23.279><c> of</c>

00:00:24.150 --> 00:00:24.160 align:start position:0%
annoying so instead of
 

00:00:24.160 --> 00:00:26.429 align:start position:0%
annoying so instead of
talking<00:00:25.160><c> why</c><00:00:25.240><c> don't</c><00:00:25.480><c> I</c><00:00:25.680><c> demonstrate</c><00:00:26.279><c> how</c>

00:00:26.429 --> 00:00:26.439 align:start position:0%
talking why don't I demonstrate how
 

00:00:26.439 --> 00:00:28.750 align:start position:0%
talking why don't I demonstrate how
autog<00:00:26.800><c> Gro</c><00:00:27.080><c> can</c><00:00:27.279><c> create</c><00:00:27.720><c> several</c><00:00:28.080><c> tailor</c><00:00:28.519><c> made</c>

00:00:28.750 --> 00:00:28.760 align:start position:0%
autog Gro can create several tailor made
 

00:00:28.760 --> 00:00:30.950 align:start position:0%
autog Gro can create several tailor made
AI<00:00:29.240><c> agents</c><00:00:29.560><c> and</c><00:00:29.720><c> their</c><00:00:30.000><c> skills</c><00:00:30.279><c> and</c><00:00:30.439><c> workflow</c>

00:00:30.950 --> 00:00:30.960 align:start position:0%
AI agents and their skills and workflow
 

00:00:30.960 --> 00:00:34.660 align:start position:0%
AI agents and their skills and workflow
in<00:00:31.119><c> under</c><00:00:31.439><c> 30</c>

00:00:34.660 --> 00:00:34.670 align:start position:0%
 
 

00:00:34.670 --> 00:00:38.880 align:start position:0%
 
[Music]

00:00:38.880 --> 00:00:38.890 align:start position:0%
[Music]
 

00:00:38.890 --> 00:00:46.100 align:start position:0%
[Music]
[Applause]

00:00:46.100 --> 00:00:46.110 align:start position:0%
 
 

00:00:46.110 --> 00:00:52.670 align:start position:0%
 
[Music]

00:00:52.670 --> 00:00:52.680 align:start position:0%
[Music]
 

00:00:52.680 --> 00:00:57.389 align:start position:0%
[Music]
seconds<00:00:53.680><c> under</c><00:00:54.039><c> 20</c><00:00:54.760><c> seconds</c><00:00:55.760><c> not</c><00:00:56.399><c> bad</c>

00:00:57.389 --> 00:00:57.399 align:start position:0%
seconds under 20 seconds not bad
 

00:00:57.399 --> 00:00:59.590 align:start position:0%
seconds under 20 seconds not bad
actually<00:00:58.039><c> we</c><00:00:58.160><c> were</c><00:00:58.359><c> done</c><00:00:58.719><c> the</c><00:00:58.920><c> second</c><00:00:59.280><c> we</c><00:00:59.440><c> hit</c>

00:00:59.590 --> 00:00:59.600 align:start position:0%
actually we were done the second we hit
 

00:00:59.600 --> 00:01:02.229 align:start position:0%
actually we were done the second we hit
the<00:00:59.960><c> ENT</c><00:01:00.280><c> key</c><00:01:00.760><c> in</c><00:01:00.960><c> reality</c><00:01:01.440><c> it</c><00:01:01.559><c> took</c><00:01:01.760><c> me</c><00:01:01.920><c> under</c>

00:01:02.229 --> 00:01:02.239 align:start position:0%
the ENT key in reality it took me under
 

00:01:02.239 --> 00:01:04.869 align:start position:0%
the ENT key in reality it took me under
10<00:01:02.640><c> seconds</c><00:01:03.079><c> to</c><00:01:03.239><c> make</c><00:01:03.440><c> these</c><00:01:03.640><c> 8ai</c><00:01:04.280><c> agents</c><00:01:04.720><c> and</c>

00:01:04.869 --> 00:01:04.879 align:start position:0%
10 seconds to make these 8ai agents and
 

00:01:04.879 --> 00:01:07.350 align:start position:0%
10 seconds to make these 8ai agents and
their<00:01:05.080><c> workflow</c><00:01:05.640><c> file</c><00:01:06.280><c> and</c><00:01:06.920><c> that's</c><00:01:07.159><c> just</c>

00:01:07.350 --> 00:01:07.360 align:start position:0%
their workflow file and that's just
 

00:01:07.360 --> 00:01:08.280 align:start position:0%
their workflow file and that's just
scratching<00:01:07.799><c> the</c>

00:01:08.280 --> 00:01:08.290 align:start position:0%
scratching the
 

00:01:08.290 --> 00:01:16.030 align:start position:0%
scratching the
[Music]

00:01:16.030 --> 00:01:16.040 align:start position:0%
[Music]
 

00:01:16.040 --> 00:01:18.040 align:start position:0%
[Music]
service

00:01:18.040 --> 00:01:18.050 align:start position:0%
service
 

00:01:18.050 --> 00:01:24.069 align:start position:0%
service
[Music]

00:01:24.069 --> 00:01:24.079 align:start position:0%
 
 

00:01:24.079 --> 00:01:26.870 align:start position:0%
 
autut<00:01:25.079><c> auto</c><00:01:25.479><c> Gro</c><00:01:25.840><c> comes</c><00:01:26.119><c> with</c><00:01:26.320><c> this</c><00:01:26.479><c> skills</c>

00:01:26.870 --> 00:01:26.880 align:start position:0%
autut auto Gro comes with this skills
 

00:01:26.880 --> 00:01:29.390 align:start position:0%
autut auto Gro comes with this skills
folder<00:01:27.320><c> and</c><00:01:27.520><c> one</c><00:01:27.799><c> default</c><00:01:28.200><c> fetch</c><00:01:28.600><c> web</c><00:01:28.960><c> content</c>

00:01:29.390 --> 00:01:29.400 align:start position:0%
folder and one default fetch web content
 

00:01:29.400 --> 00:01:31.510 align:start position:0%
folder and one default fetch web content
skill

00:01:31.510 --> 00:01:31.520 align:start position:0%
skill
 

00:01:31.520 --> 00:01:33.630 align:start position:0%
skill
you'll<00:01:31.759><c> find</c><00:01:31.960><c> it</c><00:01:32.159><c> as</c><00:01:32.280><c> an</c><00:01:32.479><c> option</c><00:01:32.720><c> in</c><00:01:32.880><c> autog</c><00:01:33.240><c> Gro</c>

00:01:33.630 --> 00:01:33.640 align:start position:0%
you'll find it as an option in autog Gro
 

00:01:33.640 --> 00:01:35.550 align:start position:0%
you'll find it as an option in autog Gro
when<00:01:33.799><c> we</c><00:01:33.920><c> edit</c><00:01:34.240><c> one</c><00:01:34.360><c> of</c><00:01:34.520><c> our</c><00:01:34.759><c> agents</c><00:01:35.280><c> it's</c><00:01:35.399><c> a</c>

00:01:35.550 --> 00:01:35.560 align:start position:0%
when we edit one of our agents it's a
 

00:01:35.560 --> 00:01:38.069 align:start position:0%
when we edit one of our agents it's a
simple<00:01:35.880><c> skill</c><00:01:36.240><c> file</c><00:01:36.640><c> but</c><00:01:36.880><c> fine</c><00:01:37.240><c> for</c><00:01:37.439><c> our</c>

00:01:38.069 --> 00:01:38.079 align:start position:0%
simple skill file but fine for our
 

00:01:38.079 --> 00:01:41.270 align:start position:0%
simple skill file but fine for our
demonstration<00:01:39.079><c> let's</c><00:01:39.320><c> fire</c><00:01:39.720><c> up</c><00:01:40.000><c> Auto</c><00:01:40.479><c> Gro</c><00:01:40.960><c> so</c>

00:01:41.270 --> 00:01:41.280 align:start position:0%
demonstration let's fire up Auto Gro so
 

00:01:41.280 --> 00:01:46.709 align:start position:0%
demonstration let's fire up Auto Gro so
you<00:01:41.399><c> can</c><00:01:41.600><c> see</c><00:01:41.840><c> what</c><00:01:42.000><c> I'm</c><00:01:42.240><c> talking</c>

00:01:46.709 --> 00:01:46.719 align:start position:0%
 
 

00:01:46.719 --> 00:01:48.950 align:start position:0%
 
about<00:01:47.719><c> you</c><00:01:47.840><c> don't</c><00:01:48.159><c> have</c><00:01:48.280><c> to</c><00:01:48.439><c> be</c><00:01:48.680><c> a</c>

00:01:48.950 --> 00:01:48.960 align:start position:0%
about you don't have to be a
 

00:01:48.960 --> 00:01:50.910 align:start position:0%
about you don't have to be a
professional<00:01:49.560><c> prompt</c><00:01:49.920><c> engineer</c><00:01:50.439><c> to</c><00:01:50.600><c> use</c>

00:01:50.910 --> 00:01:50.920 align:start position:0%
professional prompt engineer to use
 

00:01:50.920 --> 00:01:54.190 align:start position:0%
professional prompt engineer to use
autog<00:01:51.520><c> Gro</c><00:01:52.520><c> the</c><00:01:52.719><c> program</c><00:01:53.119><c> re-engineers</c><00:01:54.000><c> and</c>

00:01:54.190 --> 00:01:54.200 align:start position:0%
autog Gro the program re-engineers and
 

00:01:54.200 --> 00:01:56.630 align:start position:0%
autog Gro the program re-engineers and
refactors<00:01:54.920><c> your</c><00:01:55.200><c> request</c><00:01:55.640><c> for</c>

00:01:56.630 --> 00:01:56.640 align:start position:0%
refactors your request for
 

00:01:56.640 --> 00:01:59.230 align:start position:0%
refactors your request for
you<00:01:57.640><c> we</c><00:01:57.759><c> do</c><00:01:58.000><c> something</c><00:01:58.360><c> similar</c><00:01:58.719><c> for</c><00:01:58.880><c> you</c><00:01:59.079><c> with</c>

00:01:59.230 --> 00:01:59.240 align:start position:0%
you we do something similar for you with
 

00:01:59.240 --> 00:02:02.190 align:start position:0%
you we do something similar for you with
agent<00:01:59.520><c> descript</c><00:02:00.280><c> in</c><00:02:00.399><c> short</c><00:02:00.680><c> if</c><00:02:00.840><c> autog</c><00:02:01.159><c> Gro</c><00:02:01.640><c> can</c>

00:02:02.190 --> 00:02:02.200 align:start position:0%
agent descript in short if autog Gro can
 

00:02:02.200 --> 00:02:05.190 align:start position:0%
agent descript in short if autog Gro can
improve<00:02:02.600><c> your</c><00:02:02.799><c> code</c><00:02:03.079><c> for</c><00:02:03.280><c> you</c><00:02:03.520><c> it</c><00:02:03.880><c> will</c><00:02:04.880><c> to</c>

00:02:05.190 --> 00:02:05.200 align:start position:0%
improve your code for you it will to
 

00:02:05.200 --> 00:02:07.149 align:start position:0%
improve your code for you it will to
test<00:02:05.479><c> your</c><00:02:05.680><c> agents</c><00:02:06.079><c> you</c><00:02:06.280><c> simply</c><00:02:06.680><c> click</c><00:02:06.920><c> on</c>

00:02:07.149 --> 00:02:07.159 align:start position:0%
test your agents you simply click on
 

00:02:07.159 --> 00:02:09.550 align:start position:0%
test your agents you simply click on
them<00:02:08.119><c> the</c><00:02:08.280><c> agents</c><00:02:08.640><c> will</c><00:02:08.920><c> consider</c><00:02:09.319><c> the</c>

00:02:09.550 --> 00:02:09.560 align:start position:0%
them the agents will consider the
 

00:02:09.560 --> 00:02:12.070 align:start position:0%
them the agents will consider the
request<00:02:10.319><c> the</c><00:02:10.479><c> ensuing</c><00:02:11.080><c> discussion</c><00:02:11.599><c> and</c><00:02:11.800><c> any</c>

00:02:12.070 --> 00:02:12.080 align:start position:0%
request the ensuing discussion and any
 

00:02:12.080 --> 00:02:14.270 align:start position:0%
request the ensuing discussion and any
additional<00:02:12.599><c> remarks</c><00:02:13.120><c> entered</c><00:02:13.560><c> by</c><00:02:13.760><c> you</c><00:02:14.000><c> into</c>

00:02:14.270 --> 00:02:14.280 align:start position:0%
additional remarks entered by you into
 

00:02:14.280 --> 00:02:16.949 align:start position:0%
additional remarks entered by you into
the<00:02:14.480><c> additional</c><00:02:15.000><c> input</c><00:02:15.519><c> field</c><00:02:16.519><c> and</c><00:02:16.760><c> then</c>

00:02:16.949 --> 00:02:16.959 align:start position:0%
the additional input field and then
 

00:02:16.959 --> 00:02:19.830 align:start position:0%
the additional input field and then
they'll<00:02:17.319><c> reply</c><00:02:18.319><c> our</c><00:02:18.640><c> project</c><00:02:19.120><c> manager</c><00:02:19.640><c> has</c>

00:02:19.830 --> 00:02:19.840 align:start position:0%
they'll reply our project manager has
 

00:02:19.840 --> 00:02:21.070 align:start position:0%
they'll reply our project manager has
had<00:02:19.959><c> a</c><00:02:20.080><c> lot</c><00:02:20.280><c> to</c>

00:02:21.070 --> 00:02:21.080 align:start position:0%
had a lot to
 

00:02:21.080 --> 00:02:23.430 align:start position:0%
had a lot to
contribute<00:02:22.080><c> and</c><00:02:22.280><c> the</c><00:02:22.440><c> actions</c><00:02:22.920><c> of</c><00:02:23.080><c> the</c><00:02:23.239><c> rest</c>

00:02:23.430 --> 00:02:23.440 align:start position:0%
contribute and the actions of the rest
 

00:02:23.440 --> 00:02:25.589 align:start position:0%
contribute and the actions of the rest
of<00:02:23.560><c> the</c><00:02:23.680><c> team</c><00:02:24.080><c> will</c><00:02:24.280><c> be</c><00:02:24.519><c> influenced</c><00:02:25.239><c> by</c><00:02:25.400><c> our</c>

00:02:25.589 --> 00:02:25.599 align:start position:0%
of the team will be influenced by our
 

00:02:25.599 --> 00:02:29.670 align:start position:0%
of the team will be influenced by our
manager<00:02:26.920><c> remarks</c><00:02:27.920><c> for</c><00:02:28.239><c> now</c><00:02:29.040><c> let's</c><00:02:29.319><c> examine</c>

00:02:29.670 --> 00:02:29.680 align:start position:0%
manager remarks for now let's examine
 

00:02:29.680 --> 00:02:32.110 align:start position:0%
manager remarks for now let's examine
the<00:02:30.080><c> properties</c><00:02:30.519><c> and</c><00:02:30.800><c> skills</c><00:02:31.200><c> of</c><00:02:31.360><c> our</c><00:02:31.680><c> project</c>

00:02:32.110 --> 00:02:32.120 align:start position:0%
the properties and skills of our project
 

00:02:32.120 --> 00:02:34.910 align:start position:0%
the properties and skills of our project
manager<00:02:32.720><c> by</c><00:02:33.000><c> clicking</c><00:02:33.400><c> their</c><00:02:33.640><c> gear</c><00:02:34.040><c> icon</c><00:02:34.480><c> seen</c>

00:02:34.910 --> 00:02:34.920 align:start position:0%
manager by clicking their gear icon seen
 

00:02:34.920 --> 00:02:37.270 align:start position:0%
manager by clicking their gear icon seen
here<00:02:35.920><c> the</c><00:02:36.080><c> description</c><00:02:36.560><c> shown</c><00:02:36.920><c> is</c><00:02:37.120><c> the</c>

00:02:37.270 --> 00:02:37.280 align:start position:0%
here the description shown is the
 

00:02:37.280 --> 00:02:40.869 align:start position:0%
here the description shown is the
original<00:02:38.000><c> text</c><00:02:38.480><c> generated</c><00:02:39.080><c> by</c><00:02:39.280><c> autog</c><00:02:39.879><c> Gro</c>

00:02:40.869 --> 00:02:40.879 align:start position:0%
original text generated by autog Gro
 

00:02:40.879 --> 00:02:43.589 align:start position:0%
original text generated by autog Gro
when<00:02:41.080><c> the</c><00:02:41.200><c> first</c><00:02:41.560><c> request</c><00:02:42.000><c> was</c><00:02:42.440><c> entered</c><00:02:43.440><c> but</c>

00:02:43.589 --> 00:02:43.599 align:start position:0%
when the first request was entered but
 

00:02:43.599 --> 00:02:46.030 align:start position:0%
when the first request was entered but
as<00:02:43.720><c> I</c><00:02:43.879><c> mentioned</c><00:02:44.400><c> we</c><00:02:44.640><c> can</c><00:02:45.040><c> refactor</c><00:02:45.720><c> this</c>

00:02:46.030 --> 00:02:46.040 align:start position:0%
as I mentioned we can refactor this
 

00:02:46.040 --> 00:02:50.309 align:start position:0%
as I mentioned we can refactor this
description<00:02:46.560><c> by</c><00:02:46.760><c> clicking</c><00:02:47.080><c> the</c><00:02:47.239><c> reroll</c>

00:02:50.309 --> 00:02:50.319 align:start position:0%
 
 

00:02:50.319 --> 00:02:52.990 align:start position:0%
 
button<00:02:51.319><c> notice</c><00:02:51.680><c> the</c><00:02:51.840><c> check</c><00:02:52.159><c> box</c><00:02:52.519><c> below</c><00:02:52.800><c> the</c>

00:02:52.990 --> 00:02:53.000 align:start position:0%
button notice the check box below the
 

00:02:53.000 --> 00:02:54.910 align:start position:0%
button notice the check box below the
description<00:02:53.599><c> this</c><00:02:53.720><c> is</c><00:02:54.000><c> how</c><00:02:54.159><c> we</c><00:02:54.319><c> enable</c><00:02:54.800><c> the</c>

00:02:54.910 --> 00:02:54.920 align:start position:0%
description this is how we enable the
 

00:02:54.920 --> 00:02:56.589 align:start position:0%
description this is how we enable the
skill<00:02:55.280><c> we</c><00:02:55.400><c> put</c><00:02:55.560><c> in</c><00:02:55.680><c> the</c><00:02:55.800><c> skills</c><00:02:56.159><c> folder</c>

00:02:56.589 --> 00:02:56.599 align:start position:0%
skill we put in the skills folder
 

00:02:56.599 --> 00:02:58.869 align:start position:0%
skill we put in the skills folder
earlier<00:02:57.360><c> by</c><00:02:57.519><c> simply</c><00:02:57.840><c> checking</c><00:02:58.200><c> the</c><00:02:58.400><c> box</c><00:02:58.680><c> and</c>

00:02:58.869 --> 00:02:58.879 align:start position:0%
earlier by simply checking the box and
 

00:02:58.879 --> 00:03:00.949 align:start position:0%
earlier by simply checking the box and
hitting<00:02:59.239><c> save</c><00:02:59.599><c> we</c><00:02:59.840><c> we've</c><00:03:00.040><c> added</c><00:03:00.319><c> the</c><00:03:00.480><c> skill</c><00:03:00.800><c> to</c>

00:03:00.949 --> 00:03:00.959 align:start position:0%
hitting save we we've added the skill to
 

00:03:00.959 --> 00:03:02.910 align:start position:0%
hitting save we we've added the skill to
our<00:03:01.159><c> agent</c><00:03:01.560><c> definition</c><00:03:02.159><c> file</c><00:03:02.440><c> for</c><00:03:02.680><c> their</c>

00:03:02.910 --> 00:03:02.920 align:start position:0%
our agent definition file for their
 

00:03:02.920 --> 00:03:09.390 align:start position:0%
our agent definition file for their
export<00:03:03.319><c> to</c>

00:03:09.390 --> 00:03:09.400 align:start position:0%
 
 

00:03:09.400 --> 00:03:13.509 align:start position:0%
 
autogen<00:03:10.400><c> and</c><00:03:10.760><c> there</c><00:03:10.879><c> it</c>

00:03:13.509 --> 00:03:13.519 align:start position:0%
 
 

00:03:13.519 --> 00:03:16.070 align:start position:0%
 
is<00:03:14.519><c> and</c><00:03:14.680><c> there's</c><00:03:14.879><c> all</c><00:03:15.040><c> the</c><00:03:15.200><c> code</c><00:03:15.440><c> you</c><00:03:15.560><c> wrote</c><00:03:15.920><c> by</c>

00:03:16.070 --> 00:03:16.080 align:start position:0%
is and there's all the code you wrote by
 

00:03:16.080 --> 00:03:18.309 align:start position:0%
is and there's all the code you wrote by
checking<00:03:16.519><c> that</c><00:03:16.760><c> box</c><00:03:17.080><c> you're</c>

00:03:18.309 --> 00:03:18.319 align:start position:0%
checking that box you're
 

00:03:18.319 --> 00:03:20.710 align:start position:0%
checking that box you're
amazing<00:03:19.319><c> let's</c><00:03:19.599><c> also</c><00:03:19.959><c> take</c><00:03:20.120><c> a</c><00:03:20.239><c> peek</c><00:03:20.480><c> at</c><00:03:20.599><c> the</c>

00:03:20.710 --> 00:03:20.720 align:start position:0%
amazing let's also take a peek at the
 

00:03:20.720 --> 00:03:24.390 align:start position:0%
amazing let's also take a peek at the
J's<00:03:21.159><c> on</c><00:03:21.519><c> file</c><00:03:21.879><c> for</c><00:03:22.120><c> our</c><00:03:22.480><c> project</c><00:03:23.200><c> manager</c><00:03:24.200><c> so</c>

00:03:24.390 --> 00:03:24.400 align:start position:0%
J's on file for our project manager so
 

00:03:24.400 --> 00:03:26.750 align:start position:0%
J's on file for our project manager so
you<00:03:24.480><c> can</c><00:03:24.680><c> see</c><00:03:25.000><c> that</c><00:03:25.200><c> they</c><00:03:25.360><c> too</c><00:03:25.840><c> have</c><00:03:26.120><c> had</c><00:03:26.480><c> all</c>

00:03:26.750 --> 00:03:26.760 align:start position:0%
you can see that they too have had all
 

00:03:26.760 --> 00:03:29.070 align:start position:0%
you can see that they too have had all
their<00:03:27.040><c> code</c><00:03:27.440><c> automagically</c><00:03:28.439><c> created</c><00:03:28.879><c> for</c>

00:03:29.070 --> 00:03:29.080 align:start position:0%
their code automagically created for
 

00:03:29.080 --> 00:03:31.550 align:start position:0%
their code automagically created for
their<00:03:29.239><c> new</c><00:03:29.480><c> SC</c>

00:03:31.550 --> 00:03:31.560 align:start position:0%
their new SC
 

00:03:31.560 --> 00:03:33.949 align:start position:0%
their new SC
boy<00:03:32.439><c> finally</c><00:03:32.879><c> importing</c><00:03:33.360><c> your</c><00:03:33.560><c> newly</c>

00:03:33.949 --> 00:03:33.959 align:start position:0%
boy finally importing your newly
 

00:03:33.959 --> 00:03:36.589 align:start position:0%
boy finally importing your newly
generated<00:03:34.519><c> skill</c><00:03:34.959><c> into</c><00:03:35.480><c> autogen</c><00:03:36.120><c> is</c><00:03:36.239><c> a</c><00:03:36.439><c> piece</c>

00:03:36.589 --> 00:03:36.599 align:start position:0%
generated skill into autogen is a piece
 

00:03:36.599 --> 00:03:40.190 align:start position:0%
generated skill into autogen is a piece
of<00:03:36.799><c> cake</c><00:03:37.120><c> too</c><00:03:37.439><c> I</c><00:03:37.640><c> mean</c><00:03:38.640><c> you</c><00:03:38.879><c> click</c><00:03:39.120><c> on</c><00:03:39.560><c> upload</c><00:03:39.959><c> a</c>

00:03:40.190 --> 00:03:40.200 align:start position:0%
of cake too I mean you click on upload a
 

00:03:40.200 --> 00:03:45.509 align:start position:0%
of cake too I mean you click on upload a
skill<00:03:41.080><c> and</c><00:03:41.280><c> then</c><00:03:41.519><c> you</c><00:03:42.000><c> well</c><00:03:42.239><c> you</c><00:03:42.439><c> upload</c><00:03:42.760><c> a</c>

00:03:45.509 --> 00:03:45.519 align:start position:0%
 
 

00:03:45.519 --> 00:03:50.270 align:start position:0%
 
skill<00:03:46.840><c> likewise</c><00:03:47.840><c> to</c><00:03:48.120><c> upload</c><00:03:48.480><c> a</c><00:03:48.680><c> workflow</c><00:03:49.480><c> you</c>

00:03:50.270 --> 00:03:50.280 align:start position:0%
skill likewise to upload a workflow you
 

00:03:50.280 --> 00:03:52.030 align:start position:0%
skill likewise to upload a workflow you
you<00:03:50.439><c> know</c><00:03:50.720><c> upload</c><00:03:51.120><c> a</c>

00:03:52.030 --> 00:03:52.040 align:start position:0%
you know upload a
 

00:03:52.040 --> 00:03:54.270 align:start position:0%
you know upload a
workflow<00:03:53.040><c> I</c><00:03:53.159><c> should</c><00:03:53.400><c> probably</c><00:03:53.879><c> work</c><00:03:54.079><c> on</c>

00:03:54.270 --> 00:03:54.280 align:start position:0%
workflow I should probably work on
 

00:03:54.280 --> 00:03:56.110 align:start position:0%
workflow I should probably work on
making<00:03:54.599><c> this</c><00:03:54.799><c> stuff</c><00:03:55.040><c> sound</c><00:03:55.319><c> more</c><00:03:55.640><c> technical</c>

00:03:56.110 --> 00:03:56.120 align:start position:0%
making this stuff sound more technical
 

00:03:56.120 --> 00:03:58.149 align:start position:0%
making this stuff sound more technical
but<00:03:56.280><c> using</c><00:03:56.640><c> Auto</c><00:03:57.040><c> Gro</c><00:03:57.400><c> with</c><00:03:57.519><c> auto</c><00:03:57.879><c> genen</c>

00:03:58.149 --> 00:03:58.159 align:start position:0%
but using Auto Gro with auto genen
 

00:03:58.159 --> 00:04:00.869 align:start position:0%
but using Auto Gro with auto genen
Studio<00:03:58.799><c> really</c><00:03:59.040><c> is</c><00:03:59.280><c> this</c><00:03:59.439><c> easy</c><00:04:00.200><c> your</c><00:04:00.400><c> workflow</c>

00:04:00.869 --> 00:04:00.879 align:start position:0%
Studio really is this easy your workflow
 

00:04:00.879 --> 00:04:02.390 align:start position:0%
Studio really is this easy your workflow
is<00:04:01.000><c> going</c><00:04:01.200><c> to</c><00:04:01.360><c> have</c><00:04:01.599><c> all</c><00:04:01.799><c> your</c><00:04:02.000><c> agents</c>

00:04:02.390 --> 00:04:02.400 align:start position:0%
is going to have all your agents
 

00:04:02.400 --> 00:04:04.030 align:start position:0%
is going to have all your agents
imported<00:04:02.879><c> and</c><00:04:03.120><c> configured</c><00:04:03.680><c> for</c>

00:04:04.030 --> 00:04:04.040 align:start position:0%
imported and configured for
 

00:04:04.040 --> 00:04:06.750 align:start position:0%
imported and configured for
[Music]

00:04:06.750 --> 00:04:06.760 align:start position:0%
[Music]
 

00:04:06.760 --> 00:04:08.429 align:start position:0%
[Music]
you

00:04:08.429 --> 00:04:08.439 align:start position:0%
you
 

00:04:08.439 --> 00:04:10.750 align:start position:0%
you
aut<00:04:09.439><c> Auto</c>

00:04:10.750 --> 00:04:10.760 align:start position:0%
aut Auto
 

00:04:10.760 --> 00:04:22.950 align:start position:0%
aut Auto
autut<00:04:12.000><c> auto</c><00:04:13.000><c> auto</c><00:04:13.560><c> auto</c><00:04:14.439><c> auto</c><00:04:15.560><c> auto</c><00:04:17.000><c> aut</c>

00:04:22.950 --> 00:04:22.960 align:start position:0%
 
 

00:04:22.960 --> 00:04:25.950 align:start position:0%
 
aut<00:04:23.960><c> adding</c><00:04:24.199><c> a</c><00:04:24.360><c> skill</c><00:04:24.680><c> to</c><00:04:24.880><c> Auto</c><00:04:25.240><c> genen</c><00:04:25.639><c> via</c>

00:04:25.950 --> 00:04:25.960 align:start position:0%
aut adding a skill to Auto genen via
 

00:04:25.960 --> 00:04:29.790 align:start position:0%
aut adding a skill to Auto genen via
Auto<00:04:26.360><c> Gro</c><00:04:27.280><c> couldn't</c><00:04:27.600><c> be</c><00:04:28.160><c> easier</c><00:04:29.160><c> just</c><00:04:29.360><c> drop</c><00:04:29.600><c> a</c>

00:04:29.790 --> 00:04:29.800 align:start position:0%
Auto Gro couldn't be easier just drop a
 

00:04:29.800 --> 00:04:32.710 align:start position:0%
Auto Gro couldn't be easier just drop a
a<00:04:29.919><c> skill</c><00:04:30.240><c> python</c><00:04:30.800><c> file</c><00:04:31.320><c> into</c><00:04:31.600><c> autog</c><00:04:31.960><c> GR</c><00:04:32.320><c> skills</c>

00:04:32.710 --> 00:04:32.720 align:start position:0%
a skill python file into autog GR skills
 

00:04:32.720 --> 00:04:35.870 align:start position:0%
a skill python file into autog GR skills
folder<00:04:33.199><c> and</c><00:04:33.440><c> that's</c><00:04:33.800><c> that</c><00:04:34.680><c> GitHub</c><00:04:35.199><c> user</c><00:04:35.520><c> mad</c>

00:04:35.870 --> 00:04:35.880 align:start position:0%
folder and that's that GitHub user mad
 

00:04:35.880 --> 00:04:38.310 align:start position:0%
folder and that's that GitHub user mad
tank<00:04:36.160><c> has</c><00:04:36.280><c> a</c><00:04:36.400><c> few</c><00:04:36.639><c> great</c><00:04:36.840><c> skill</c><00:04:37.199><c> files</c><00:04:38.039><c> drop</c>

00:04:38.310 --> 00:04:38.320 align:start position:0%
tank has a few great skill files drop
 

00:04:38.320 --> 00:04:40.110 align:start position:0%
tank has a few great skill files drop
them<00:04:38.440><c> a</c><00:04:38.600><c> star</c><00:04:38.919><c> if</c><00:04:39.000><c> you</c><00:04:39.120><c> borrow</c><00:04:39.479><c> their</c><00:04:39.680><c> code</c>

00:04:40.110 --> 00:04:40.120 align:start position:0%
them a star if you borrow their code
 

00:04:40.120 --> 00:04:43.510 align:start position:0%
them a star if you borrow their code
I'll<00:04:40.280><c> steal</c><00:04:40.600><c> fetch</c><00:04:40.919><c> post</c><00:04:41.160><c> for</c><00:04:41.280><c> our</c>

00:04:43.510 --> 00:04:43.520 align:start position:0%
 
 

00:04:43.520 --> 00:04:46.469 align:start position:0%
 
demonstration<00:04:44.520><c> again</c><00:04:45.360><c> all</c><00:04:45.600><c> we</c><00:04:45.800><c> have</c><00:04:45.919><c> to</c><00:04:46.039><c> do</c><00:04:46.280><c> is</c>

00:04:46.469 --> 00:04:46.479 align:start position:0%
demonstration again all we have to do is
 

00:04:46.479 --> 00:04:49.590 align:start position:0%
demonstration again all we have to do is
put<00:04:46.639><c> the</c><00:04:46.800><c> py</c><00:04:47.320><c> file</c><00:04:47.720><c> in</c><00:04:47.840><c> our</c><00:04:48.080><c> Auto</c><00:04:48.440><c> Gro</c><00:04:48.800><c> skills</c>

00:04:49.590 --> 00:04:49.600 align:start position:0%
put the py file in our Auto Gro skills
 

00:04:49.600 --> 00:04:55.629 align:start position:0%
put the py file in our Auto Gro skills
[Music]

00:04:55.629 --> 00:04:55.639 align:start position:0%
 
 

00:04:55.639 --> 00:04:58.189 align:start position:0%
 
folder<00:04:56.639><c> and</c><00:04:56.840><c> there</c><00:04:56.960><c> it</c><00:04:57.120><c> is</c><00:04:57.440><c> let's</c><00:04:57.639><c> fire</c><00:04:58.000><c> up</c>

00:04:58.189 --> 00:04:58.199 align:start position:0%
folder and there it is let's fire up
 

00:04:58.199 --> 00:05:08.629 align:start position:0%
folder and there it is let's fire up
Auto<00:04:58.560><c> Gro</c><00:04:58.960><c> again</c><00:04:59.120><c> to</c><00:04:59.240><c> see</c><00:04:59.440><c> our</c><00:04:59.720><c> new</c><00:04:59.919><c> skill</c><00:05:00.280><c> file</c>

00:05:08.629 --> 00:05:08.639 align:start position:0%
 
 

00:05:08.639 --> 00:05:11.670 align:start position:0%
 
option<00:05:09.639><c> and</c><00:05:09.840><c> there</c><00:05:10.000><c> you</c><00:05:10.240><c> have</c><00:05:10.400><c> it</c><00:05:11.280><c> our</c><00:05:11.479><c> new</c>

00:05:11.670 --> 00:05:11.680 align:start position:0%
option and there you have it our new
 

00:05:11.680 --> 00:05:14.070 align:start position:0%
option and there you have it our new
skill<00:05:12.120><c> has</c><00:05:12.240><c> been</c><00:05:12.479><c> added</c><00:05:12.759><c> to</c><00:05:13.000><c> autog</c><00:05:13.400><c> Gro</c><00:05:13.880><c> and</c>

00:05:14.070 --> 00:05:14.080 align:start position:0%
skill has been added to autog Gro and
 

00:05:14.080 --> 00:05:15.950 align:start position:0%
skill has been added to autog Gro and
just<00:05:14.280><c> by</c><00:05:14.440><c> checking</c><00:05:14.800><c> the</c><00:05:15.000><c> box</c><00:05:15.280><c> and</c><00:05:15.479><c> clicking</c>

00:05:15.950 --> 00:05:15.960 align:start position:0%
just by checking the box and clicking
 

00:05:15.960 --> 00:05:20.309 align:start position:0%
just by checking the box and clicking
save<00:05:16.400><c> we</c><00:05:16.479><c> can</c><00:05:16.720><c> export</c><00:05:17.240><c> this</c><00:05:18.039><c> new</c><00:05:18.360><c> skill</c><00:05:18.800><c> to</c>

00:05:20.309 --> 00:05:20.319 align:start position:0%
save we can export this new skill to
 

00:05:20.319 --> 00:05:22.510 align:start position:0%
save we can export this new skill to
autogen<00:05:21.319><c> let's</c><00:05:21.560><c> take</c><00:05:21.680><c> a</c><00:05:21.840><c> peek</c><00:05:22.080><c> at</c><00:05:22.199><c> all</c><00:05:22.400><c> the</c>

00:05:22.510 --> 00:05:22.520 align:start position:0%
autogen let's take a peek at all the
 

00:05:22.520 --> 00:05:24.550 align:start position:0%
autogen let's take a peek at all the
Json<00:05:23.160><c> script</c><00:05:23.520><c> that's</c><00:05:23.759><c> been</c><00:05:23.880><c> written</c><00:05:24.160><c> for</c><00:05:24.319><c> you</c>

00:05:24.550 --> 00:05:24.560 align:start position:0%
Json script that's been written for you
 

00:05:24.560 --> 00:05:26.469 align:start position:0%
Json script that's been written for you
this<00:05:24.680><c> shows</c><00:05:24.960><c> us</c><00:05:25.160><c> how</c><00:05:25.319><c> much</c><00:05:25.560><c> typing</c><00:05:26.039><c> we</c><00:05:26.160><c> don't</c>

00:05:26.469 --> 00:05:26.479 align:start position:0%
this shows us how much typing we don't
 

00:05:26.479 --> 00:05:30.029 align:start position:0%
this shows us how much typing we don't
have<00:05:26.600><c> to</c><00:05:26.759><c> do</c><00:05:27.039><c> thanks</c><00:05:27.280><c> to</c><00:05:27.479><c> autog</c><00:05:28.039><c> Gro</c><00:05:29.039><c> so</c><00:05:29.759><c> that's</c>

00:05:30.029 --> 00:05:30.039 align:start position:0%
have to do thanks to autog Gro so that's
 

00:05:30.039 --> 00:05:31.990 align:start position:0%
have to do thanks to autog Gro so that's
this<00:05:30.199><c> week's</c><00:05:30.560><c> update</c><00:05:30.960><c> next</c><00:05:31.160><c> week's</c><00:05:31.520><c> focus</c><00:05:31.840><c> is</c>

00:05:31.990 --> 00:05:32.000 align:start position:0%
this week's update next week's focus is
 

00:05:32.000 --> 00:05:34.110 align:start position:0%
this week's update next week's focus is
more<00:05:32.240><c> Automation</c><00:05:32.800><c> and</c><00:05:33.000><c> by</c><00:05:33.280><c> popular</c><00:05:33.680><c> demand</c>

00:05:34.110 --> 00:05:34.120 align:start position:0%
more Automation and by popular demand
 

00:05:34.120 --> 00:05:37.550 align:start position:0%
more Automation and by popular demand
local<00:05:34.440><c> llm</c><00:05:35.360><c> capability</c><00:05:36.360><c> autog</c><00:05:36.800><c> gr</c><00:05:37.080><c> is</c><00:05:37.240><c> free</c>

00:05:37.550 --> 00:05:37.560 align:start position:0%
local llm capability autog gr is free
 

00:05:37.560 --> 00:05:39.670 align:start position:0%
local llm capability autog gr is free
and<00:05:37.800><c> open</c><00:05:38.120><c> source</c><00:05:38.520><c> all</c><00:05:38.680><c> we</c><00:05:38.880><c> ask</c><00:05:39.160><c> is</c><00:05:39.319><c> that</c><00:05:39.479><c> you</c>

00:05:39.670 --> 00:05:39.680 align:start position:0%
and open source all we ask is that you
 

00:05:39.680 --> 00:05:42.029 align:start position:0%
and open source all we ask is that you
like<00:05:40.360><c> and</c><00:05:40.600><c> subscribe</c><00:05:41.199><c> and</c><00:05:41.319><c> share</c><00:05:41.800><c> these</c>

00:05:42.029 --> 00:05:42.039 align:start position:0%
like and subscribe and share these
 

00:05:42.039 --> 00:05:44.749 align:start position:0%
like and subscribe and share these
videos<00:05:42.880><c> a</c><00:05:43.080><c> star</c><00:05:43.360><c> on</c><00:05:43.639><c> GitHub</c><00:05:44.120><c> would</c><00:05:44.280><c> be</c><00:05:44.479><c> great</c>

00:05:44.749 --> 00:05:44.759 align:start position:0%
videos a star on GitHub would be great
 

00:05:44.759 --> 00:05:47.189 align:start position:0%
videos a star on GitHub would be great
too<00:05:45.520><c> thanks</c><00:05:45.800><c> for</c><00:05:46.039><c> watching</c><00:05:46.520><c> as</c><00:05:46.680><c> always</c><00:05:46.960><c> I'll</c>

00:05:47.189 --> 00:05:47.199 align:start position:0%
too thanks for watching as always I'll
 

00:05:47.199 --> 00:05:50.830 align:start position:0%
too thanks for watching as always I'll
talk<00:05:47.400><c> to</c><00:05:47.520><c> you</c><00:05:47.639><c> in</c><00:05:47.759><c> the</c><00:05:47.919><c> comments</c><00:05:48.360><c> section</c>

00:05:50.830 --> 00:05:50.840 align:start position:0%
talk to you in the comments section
 

00:05:50.840 --> 00:05:58.370 align:start position:0%
talk to you in the comments section
[Music]

