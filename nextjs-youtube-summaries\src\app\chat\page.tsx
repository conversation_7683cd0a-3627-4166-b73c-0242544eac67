'use client'

import { useState } from 'react'
import { ChatInterface } from '@/components/ChatInterface'
import { TopicSelector } from '@/components/TopicSelector'
import { MessageCircle, Brain, Lightbulb } from 'lucide-react'

export default function ChatPage() {
  const [selectedTopics, setSelectedTopics] = useState<string[]>([])

  return (
    <div className="space-y-8 h-[calc(100vh-200px)]">
      {/* Page Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4 flex items-center justify-center gap-2">
          <MessageCircle className="w-8 h-8 text-blue-600" />
          AI Chat Assistant
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Chat with our AI assistant about YouTube video content. Ask questions, 
          get summaries, or explore topics across our video database.
        </p>
      </div>

      {/* Chat Controls */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center gap-2 mb-4">
          <Brain className="w-5 h-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-900">Topic Focus</h2>
        </div>
        <TopicSelector
          selectedTopics={selectedTopics}
          onTopicsChange={setSelectedTopics}
        />
        {selectedTopics.length > 0 ? (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Chat focus:</strong> The AI will prioritize content from{' '}
              <span className="font-semibold">{selectedTopics.join(', ')}</span> topics.
            </p>
          </div>
        ) : (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">
              The AI will search across all available video topics for relevant content.
            </p>
          </div>
        )}
      </div>

      {/* Chat Interface */}
      <div className="flex-1 min-h-0">
        <ChatInterface selectedTopics={selectedTopics} />
      </div>

      {/* Example Questions */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <div className="flex items-center gap-2 mb-3">
          <Lightbulb className="w-5 h-5 text-yellow-600" />
          <h3 className="text-lg font-semibold text-gray-900">Example Questions</h3>
        </div>        <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-700">
          <div>
            <h4 className="font-medium mb-2">Content Discovery:</h4>
            <ul className="space-y-1 list-disc list-inside">
              <li>&ldquo;What are the latest trends in AI?&rdquo;</li>
              <li>&ldquo;Show me cooking videos for beginners&rdquo;</li>
              <li>&ldquo;Find tutorials about React hooks&rdquo;</li>
              <li>&ldquo;What did this channel say about climate change?&rdquo;</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">Specific Questions:</h4>
            <ul className="space-y-1 list-disc list-inside">
              <li>&ldquo;How do I fix authentication errors in Next.js?&rdquo;</li>
              <li>&ldquo;What are the best practices for data science?&rdquo;</li>
              <li>&ldquo;Explain machine learning concepts from the videos&rdquo;</li>
              <li>&ldquo;Compare different programming languages&rdquo;</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
