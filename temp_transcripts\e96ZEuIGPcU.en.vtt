WEBVTT
Kind: captions
Language: en

00:00:00.680 --> 00:00:02.470 align:start position:0%
 
wow<00:00:01.240><c> AMA</c>

00:00:02.470 --> 00:00:02.480 align:start position:0%
wow AMA
 

00:00:02.480 --> 00:00:06.230 align:start position:0%
wow AMA
0.1.2<00:00:03.480><c> two</c><00:00:03.959><c> just</c><00:00:04.200><c> came</c><00:00:04.359><c> out</c><00:00:04.839><c> hot</c><00:00:05.120><c> on</c><00:00:05.319><c> the</c><00:00:05.480><c> heels</c>

00:00:06.230 --> 00:00:06.240 align:start position:0%
0.1.2 two just came out hot on the heels
 

00:00:06.240 --> 00:00:07.070 align:start position:0%
0.1.2 two just came out hot on the heels
of

00:00:07.070 --> 00:00:07.080 align:start position:0%
of
 

00:00:07.080 --> 00:00:10.950 align:start position:0%
of
0.1.2<00:00:08.080><c> one</c><00:00:08.679><c> two</c><00:00:09.040><c> releases</c><00:00:09.639><c> in</c><00:00:09.800><c> a</c><00:00:09.960><c> week</c><00:00:10.639><c> most</c><00:00:10.840><c> of</c>

00:00:10.950 --> 00:00:10.960 align:start position:0%
0.1.2 one two releases in a week most of
 

00:00:10.960 --> 00:00:12.789 align:start position:0%
0.1.2 one two releases in a week most of
the<00:00:11.080><c> new</c><00:00:11.240><c> features</c><00:00:11.599><c> were</c><00:00:11.920><c> in</c><00:00:12.080><c> the</c><00:00:12.200><c> first</c><00:00:12.480><c> one</c>

00:00:12.789 --> 00:00:12.799 align:start position:0%
the new features were in the first one
 

00:00:12.799 --> 00:00:15.589 align:start position:0%
the new features were in the first one
but<00:00:12.960><c> 22</c><00:00:13.519><c> cleans</c><00:00:13.920><c> things</c><00:00:14.120><c> up</c><00:00:14.400><c> a</c><00:00:14.519><c> bit</c><00:00:15.200><c> we'll</c><00:00:15.440><c> take</c>

00:00:15.589 --> 00:00:15.599 align:start position:0%
but 22 cleans things up a bit we'll take
 

00:00:15.599 --> 00:00:18.269 align:start position:0%
but 22 cleans things up a bit we'll take
a<00:00:15.719><c> look</c><00:00:15.879><c> at</c><00:00:16.240><c> all</c><00:00:16.440><c> the</c><00:00:16.600><c> new</c><00:00:16.800><c> features</c><00:00:17.320><c> and</c><00:00:17.680><c> I</c><00:00:17.800><c> am</c>

00:00:18.269 --> 00:00:18.279 align:start position:0%
a look at all the new features and I am
 

00:00:18.279 --> 00:00:20.670 align:start position:0%
a look at all the new features and I am
especially<00:00:18.840><c> excited</c><00:00:19.680><c> about</c><00:00:19.960><c> one</c><00:00:20.119><c> of</c><00:00:20.279><c> them</c>

00:00:20.670 --> 00:00:20.680 align:start position:0%
especially excited about one of them
 

00:00:20.680 --> 00:00:23.630 align:start position:0%
especially excited about one of them
that<00:00:20.840><c> can</c><00:00:20.960><c> make</c><00:00:21.119><c> a</c><00:00:21.359><c> big</c><00:00:21.600><c> impact</c><00:00:22.000><c> for</c><00:00:22.279><c> all</c><00:00:22.640><c> users</c>

00:00:23.630 --> 00:00:23.640 align:start position:0%
that can make a big impact for all users
 

00:00:23.640 --> 00:00:25.990 align:start position:0%
that can make a big impact for all users
first<00:00:24.000><c> we</c><00:00:24.080><c> see</c><00:00:24.279><c> a</c><00:00:24.439><c> bunch</c><00:00:24.599><c> of</c><00:00:24.760><c> new</c><00:00:24.960><c> models</c><00:00:25.519><c> quen</c>

00:00:25.990 --> 00:00:26.000 align:start position:0%
first we see a bunch of new models quen
 

00:00:26.000 --> 00:00:29.509 align:start position:0%
first we see a bunch of new models quen
duct<00:00:26.439><c> be</c><00:00:26.760><c> no</c><00:00:27.000><c> sequel</c><00:00:27.800><c> stable</c><00:00:28.240><c> code</c><00:00:28.560><c> new</c><00:00:28.800><c> Hermes</c>

00:00:29.509 --> 00:00:29.519 align:start position:0%
duct be no sequel stable code new Hermes
 

00:00:29.519 --> 00:00:33.549 align:start position:0%
duct be no sequel stable code new Hermes
two<00:00:30.119><c> mixol</c><00:00:31.119><c> and</c><00:00:31.320><c> stable</c><00:00:32.040><c> lm2</c><00:00:33.040><c> and</c><00:00:33.160><c> then</c><00:00:33.280><c> we</c><00:00:33.399><c> got</c>

00:00:33.549 --> 00:00:33.559 align:start position:0%
two mixol and stable lm2 and then we got
 

00:00:33.559 --> 00:00:35.549 align:start position:0%
two mixol and stable lm2 and then we got
the<00:00:33.680><c> announcement</c><00:00:34.360><c> of</c><00:00:34.600><c> the</c><00:00:34.760><c> new</c><00:00:35.079><c> official</c>

00:00:35.549 --> 00:00:35.559 align:start position:0%
the announcement of the new official
 

00:00:35.559 --> 00:00:37.750 align:start position:0%
the announcement of the new official
typescript<00:00:36.120><c> and</c><00:00:36.280><c> JavaScript</c><00:00:36.879><c> library</c><00:00:37.640><c> as</c>

00:00:37.750 --> 00:00:37.760 align:start position:0%
typescript and JavaScript library as
 

00:00:37.760 --> 00:00:39.950 align:start position:0%
typescript and JavaScript library as
well<00:00:37.920><c> as</c><00:00:38.040><c> a</c><00:00:38.160><c> new</c><00:00:38.360><c> official</c><00:00:38.800><c> python</c><00:00:39.160><c> Library</c>

00:00:39.950 --> 00:00:39.960 align:start position:0%
well as a new official python Library
 

00:00:39.960 --> 00:00:42.190 align:start position:0%
well as a new official python Library
making<00:00:40.239><c> it</c><00:00:40.440><c> even</c><00:00:40.760><c> easier</c><00:00:41.320><c> to</c><00:00:41.480><c> build</c><00:00:41.879><c> great</c>

00:00:42.190 --> 00:00:42.200 align:start position:0%
making it even easier to build great
 

00:00:42.200 --> 00:00:46.670 align:start position:0%
making it even easier to build great
apps<00:00:42.640><c> with</c><00:00:42.960><c> AMA</c><00:00:43.960><c> now</c><00:00:44.399><c> let's</c><00:00:44.680><c> talk</c><00:00:44.879><c> about</c><00:00:45.680><c> CPU</c>

00:00:46.670 --> 00:00:46.680 align:start position:0%
apps with AMA now let's talk about CPU
 

00:00:46.680 --> 00:00:49.229 align:start position:0%
apps with AMA now let's talk about CPU
from<00:00:46.879><c> the</c><00:00:47.039><c> beginning</c><00:00:47.520><c> olama</c><00:00:48.079><c> required</c><00:00:48.640><c> AVX</c>

00:00:49.229 --> 00:00:49.239 align:start position:0%
from the beginning olama required AVX
 

00:00:49.239 --> 00:00:52.189 align:start position:0%
from the beginning olama required AVX
instructions<00:00:49.719><c> to</c><00:00:49.840><c> be</c><00:00:50.039><c> available</c><00:00:50.640><c> on</c><00:00:50.800><c> the</c><00:00:51.199><c> CPU</c>

00:00:52.189 --> 00:00:52.199 align:start position:0%
instructions to be available on the CPU
 

00:00:52.199 --> 00:00:56.389 align:start position:0%
instructions to be available on the CPU
AVX<00:00:52.920><c> is</c><00:00:53.160><c> all</c><00:00:53.399><c> about</c><00:00:53.840><c> that</c>

00:00:56.389 --> 00:00:56.399 align:start position:0%
AVX is all about that
 

00:00:56.399 --> 00:00:57.950 align:start position:0%
AVX is all about that
oh

00:00:57.950 --> 00:00:57.960 align:start position:0%
oh
 

00:00:57.960 --> 00:01:02.910 align:start position:0%
oh
wow<00:00:58.960><c> whoa</c><00:01:00.039><c> double</c><00:01:00.600><c> Precision</c><00:01:01.239><c> floating</c><00:01:01.879><c> Point</c>

00:01:02.910 --> 00:01:02.920 align:start position:0%
wow whoa double Precision floating Point
 

00:01:02.920 --> 00:01:06.830 align:start position:0%
wow whoa double Precision floating Point
arithmetic<00:01:03.920><c> all</c><00:01:04.320><c> the</c><00:01:04.600><c> way</c><00:01:05.239><c> it's</c><00:01:05.479><c> so</c><00:01:06.000><c> be</c><00:01:06.680><c> oh</c>

00:01:06.830 --> 00:01:06.840 align:start position:0%
arithmetic all the way it's so be oh
 

00:01:06.840 --> 00:01:09.710 align:start position:0%
arithmetic all the way it's so be oh
sorry<00:01:07.560><c> memories</c><00:01:08.040><c> of</c><00:01:08.240><c> classic</c><00:01:08.560><c> YouTube</c><00:01:09.520><c> it's</c>

00:01:09.710 --> 00:01:09.720 align:start position:0%
sorry memories of classic YouTube it's
 

00:01:09.720 --> 00:01:12.190 align:start position:0%
sorry memories of classic YouTube it's
for<00:01:10.159><c> applications</c><00:01:10.799><c> that</c><00:01:10.960><c> do</c><00:01:11.240><c> a</c><00:01:11.439><c> lot</c><00:01:11.600><c> of</c><00:01:11.799><c> vector</c>

00:01:12.190 --> 00:01:12.200 align:start position:0%
for applications that do a lot of vector
 

00:01:12.200 --> 00:01:14.149 align:start position:0%
for applications that do a lot of vector
calculations<00:01:12.880><c> like</c><00:01:13.280><c> oh</c><00:01:13.479><c> I</c><00:01:13.560><c> don't</c><00:01:13.720><c> know</c><00:01:14.040><c> could</c>

00:01:14.149 --> 00:01:14.159 align:start position:0%
calculations like oh I don't know could
 

00:01:14.159 --> 00:01:15.230 align:start position:0%
calculations like oh I don't know could
it

00:01:15.230 --> 00:01:15.240 align:start position:0%
it
 

00:01:15.240 --> 00:01:18.429 align:start position:0%
it
be<00:01:16.240><c> a</c>

00:01:18.429 --> 00:01:18.439 align:start position:0%
be a
 

00:01:18.439 --> 00:01:21.350 align:start position:0%
be a
llama<00:01:19.439><c> and</c><00:01:19.600><c> it</c><00:01:19.759><c> seemed</c><00:01:20.119><c> like</c><00:01:20.280><c> a</c><00:01:20.439><c> no-brainer</c><00:01:21.200><c> to</c>

00:01:21.350 --> 00:01:21.360 align:start position:0%
llama and it seemed like a no-brainer to
 

00:01:21.360 --> 00:01:23.749 align:start position:0%
llama and it seemed like a no-brainer to
include<00:01:21.960><c> as</c><00:01:22.119><c> a</c><00:01:22.400><c> requirement</c><00:01:23.200><c> because</c><00:01:23.600><c> it's</c>

00:01:23.749 --> 00:01:23.759 align:start position:0%
include as a requirement because it's
 

00:01:23.759 --> 00:01:27.149 align:start position:0%
include as a requirement because it's
been<00:01:23.960><c> on</c><00:01:24.200><c> almost</c><00:01:24.880><c> every</c><00:01:25.280><c> CPU</c><00:01:26.280><c> for</c><00:01:26.600><c> over</c><00:01:26.920><c> a</c>

00:01:27.149 --> 00:01:27.159 align:start position:0%
been on almost every CPU for over a
 

00:01:27.159 --> 00:01:30.310 align:start position:0%
been on almost every CPU for over a
decade<00:01:28.119><c> well</c><00:01:28.439><c> it</c><00:01:28.600><c> turns</c><00:01:28.920><c> out</c><00:01:29.400><c> that</c><00:01:29.560><c> there</c><00:01:30.000><c> are</c>

00:01:30.310 --> 00:01:30.320 align:start position:0%
decade well it turns out that there are
 

00:01:30.320 --> 00:01:32.469 align:start position:0%
decade well it turns out that there are
a<00:01:30.479><c> lot</c><00:01:30.640><c> of</c><00:01:30.799><c> extra</c><00:01:31.079><c> machines</c><00:01:31.479><c> out</c><00:01:31.720><c> there</c><00:01:32.240><c> with</c>

00:01:32.469 --> 00:01:32.479 align:start position:0%
a lot of extra machines out there with
 

00:01:32.479 --> 00:01:35.389 align:start position:0%
a lot of extra machines out there with
15-year-old<00:01:33.280><c> CPUs</c><00:01:34.079><c> and</c><00:01:34.439><c> their</c><00:01:34.600><c> owners</c><00:01:35.119><c> need</c>

00:01:35.389 --> 00:01:35.399 align:start position:0%
15-year-old CPUs and their owners need
 

00:01:35.399 --> 00:01:38.069 align:start position:0%
15-year-old CPUs and their owners need
them<00:01:35.560><c> to</c><00:01:35.799><c> still</c><00:01:36.119><c> provide</c><00:01:36.520><c> value</c><00:01:37.200><c> but</c><00:01:37.360><c> ol</c><00:01:37.880><c> would</c>

00:01:38.069 --> 00:01:38.079 align:start position:0%
them to still provide value but ol would
 

00:01:38.079 --> 00:01:41.389 align:start position:0%
them to still provide value but ol would
just<00:01:38.640><c> crash</c><00:01:39.040><c> on</c><00:01:39.399><c> them</c><00:01:40.399><c> and</c><00:01:40.840><c> there</c><00:01:40.960><c> are</c><00:01:41.159><c> some</c>

00:01:41.389 --> 00:01:41.399 align:start position:0%
just crash on them and there are some
 

00:01:41.399 --> 00:01:44.310 align:start position:0%
just crash on them and there are some
newer<00:01:41.759><c> CPUs</c><00:01:42.360><c> that</c><00:01:42.520><c> support</c><00:01:43.119><c> avx2</c><00:01:44.119><c> that</c>

00:01:44.310 --> 00:01:44.320 align:start position:0%
newer CPUs that support avx2 that
 

00:01:44.320 --> 00:01:45.870 align:start position:0%
newer CPUs that support avx2 that
couldn't<00:01:44.640><c> take</c><00:01:44.880><c> advantage</c><00:01:45.520><c> of</c><00:01:45.680><c> the</c>

00:01:45.870 --> 00:01:45.880 align:start position:0%
couldn't take advantage of the
 

00:01:45.880 --> 00:01:48.950 align:start position:0%
couldn't take advantage of the
improvements<00:01:46.880><c> well</c><00:01:47.320><c> now</c><00:01:47.799><c> both</c><00:01:48.399><c> situations</c>

00:01:48.950 --> 00:01:48.960 align:start position:0%
improvements well now both situations
 

00:01:48.960 --> 00:01:51.429 align:start position:0%
improvements well now both situations
are<00:01:49.200><c> Now</c><00:01:49.719><c> supported</c><00:01:50.719><c> there</c><00:01:50.840><c> were</c><00:01:51.119><c> also</c>

00:01:51.429 --> 00:01:51.439 align:start position:0%
are Now supported there were also
 

00:01:51.439 --> 00:01:53.950 align:start position:0%
are Now supported there were also
occasions<00:01:51.920><c> when</c><00:01:52.119><c> the</c><00:01:52.280><c> GPU</c><00:01:52.840><c> wasn't</c><00:01:53.200><c> recognized</c>

00:01:53.950 --> 00:01:53.960 align:start position:0%
occasions when the GPU wasn't recognized
 

00:01:53.960 --> 00:01:58.109 align:start position:0%
occasions when the GPU wasn't recognized
and<00:01:54.119><c> AMA</c><00:01:54.600><c> would</c><00:01:54.799><c> just</c><00:01:55.880><c> crash</c><00:01:56.880><c> well</c><00:01:57.360><c> now</c><00:01:57.680><c> if</c><00:01:57.880><c> not</c>

00:01:58.109 --> 00:01:58.119 align:start position:0%
and AMA would just crash well now if not
 

00:01:58.119 --> 00:02:00.910 align:start position:0%
and AMA would just crash well now if not
recognized<00:01:58.920><c> olama</c><00:01:59.399><c> will</c><00:01:59.520><c> fall</c><00:02:00.039><c> back</c><00:02:00.280><c> to</c><00:02:00.479><c> using</c>

00:02:00.910 --> 00:02:00.920 align:start position:0%
recognized olama will fall back to using
 

00:02:00.920 --> 00:02:03.709 align:start position:0%
recognized olama will fall back to using
CPU<00:02:01.600><c> only</c><00:02:02.600><c> I</c><00:02:02.719><c> think</c><00:02:02.960><c> one</c><00:02:03.119><c> of</c><00:02:03.240><c> the</c><00:02:03.399><c> common</c>

00:02:03.709 --> 00:02:03.719 align:start position:0%
CPU only I think one of the common
 

00:02:03.719 --> 00:02:06.270 align:start position:0%
CPU only I think one of the common
reasons<00:02:04.200><c> this</c><00:02:04.399><c> happened</c><00:02:05.280><c> was</c><00:02:05.600><c> you</c><00:02:05.799><c> had</c><00:02:05.960><c> an</c>

00:02:06.270 --> 00:02:06.280 align:start position:0%
reasons this happened was you had an
 

00:02:06.280 --> 00:02:08.749 align:start position:0%
reasons this happened was you had an
ancient<00:02:06.759><c> Nvidia</c><00:02:07.240><c> GPU</c><00:02:07.799><c> that</c><00:02:08.000><c> wasn't</c><00:02:08.280><c> supported</c>

00:02:08.749 --> 00:02:08.759 align:start position:0%
ancient Nvidia GPU that wasn't supported
 

00:02:08.759 --> 00:02:11.190 align:start position:0%
ancient Nvidia GPU that wasn't supported
by<00:02:08.920><c> Cuda</c><00:02:09.840><c> you</c><00:02:09.959><c> can</c><00:02:10.119><c> get</c><00:02:10.280><c> some</c><00:02:10.520><c> super</c><00:02:10.879><c> cheap</c>

00:02:11.190 --> 00:02:11.200 align:start position:0%
by Cuda you can get some super cheap
 

00:02:11.200 --> 00:02:14.229 align:start position:0%
by Cuda you can get some super cheap
gpus<00:02:11.720><c> on</c><00:02:11.959><c> eBay</c><00:02:12.800><c> but</c><00:02:13.200><c> sometimes</c><00:02:13.680><c> the</c><00:02:13.879><c> promise</c>

00:02:14.229 --> 00:02:14.239 align:start position:0%
gpus on eBay but sometimes the promise
 

00:02:14.239 --> 00:02:17.190 align:start position:0%
gpus on eBay but sometimes the promise
is<00:02:14.400><c> too</c><00:02:14.680><c> good</c><00:02:14.959><c> to</c><00:02:15.120><c> be</c><00:02:15.480><c> true</c><00:02:16.480><c> there's</c><00:02:16.879><c> also</c>

00:02:17.190 --> 00:02:17.200 align:start position:0%
is too good to be true there's also
 

00:02:17.200 --> 00:02:20.550 align:start position:0%
is too good to be true there's also
better<00:02:17.480><c> support</c><00:02:17.760><c> for</c><00:02:17.959><c> NVIDIA</c><00:02:18.400><c> gpus</c><00:02:19.040><c> in</c><00:02:19.560><c> WSL</c>

00:02:20.550 --> 00:02:20.560 align:start position:0%
better support for NVIDIA gpus in WSL
 

00:02:20.560 --> 00:02:21.910 align:start position:0%
better support for NVIDIA gpus in WSL
some<00:02:20.800><c> people</c><00:02:21.000><c> were</c><00:02:21.160><c> seeing</c><00:02:21.440><c> problems</c><00:02:21.760><c> with</c>

00:02:21.910 --> 00:02:21.920 align:start position:0%
some people were seeing problems with
 

00:02:21.920 --> 00:02:24.990 align:start position:0%
some people were seeing problems with
AMA<00:02:22.760><c> where</c><00:02:23.000><c> it</c><00:02:23.080><c> would</c><00:02:23.400><c> hang</c><00:02:23.959><c> after</c><00:02:24.319><c> 20</c><00:02:24.599><c> or</c><00:02:24.760><c> so</c>

00:02:24.990 --> 00:02:25.000 align:start position:0%
AMA where it would hang after 20 or so
 

00:02:25.000 --> 00:02:27.710 align:start position:0%
AMA where it would hang after 20 or so
requests<00:02:25.959><c> I</c><00:02:26.120><c> tried</c><00:02:26.519><c> replicating</c><00:02:27.160><c> this</c><00:02:27.480><c> going</c>

00:02:27.710 --> 00:02:27.720 align:start position:0%
requests I tried replicating this going
 

00:02:27.720 --> 00:02:30.630 align:start position:0%
requests I tried replicating this going
on<00:02:28.000><c> for</c><00:02:28.239><c> an</c><00:02:28.519><c> hour</c><00:02:28.840><c> and</c><00:02:29.160><c> hundreds</c><00:02:29.560><c> of</c><00:02:29.920><c> requests</c>

00:02:30.630 --> 00:02:30.640 align:start position:0%
on for an hour and hundreds of requests
 

00:02:30.640 --> 00:02:32.550 align:start position:0%
on for an hour and hundreds of requests
and<00:02:30.800><c> had</c><00:02:31.000><c> no</c><00:02:31.239><c> issues</c><00:02:31.680><c> but</c><00:02:31.959><c> one</c><00:02:32.120><c> of</c><00:02:32.239><c> the</c><00:02:32.319><c> team</c>

00:02:32.550 --> 00:02:32.560 align:start position:0%
and had no issues but one of the team
 

00:02:32.560 --> 00:02:34.430 align:start position:0%
and had no issues but one of the team
members<00:02:32.959><c> figured</c><00:02:33.280><c> it</c><00:02:33.440><c> out</c><00:02:33.920><c> and</c><00:02:34.080><c> got</c><00:02:34.239><c> it</c>

00:02:34.430 --> 00:02:34.440 align:start position:0%
members figured it out and got it
 

00:02:34.440 --> 00:02:36.710 align:start position:0%
members figured it out and got it
resolved<00:02:35.440><c> there</c><00:02:35.599><c> are</c><00:02:35.879><c> a</c><00:02:36.080><c> bunch</c><00:02:36.319><c> of</c><00:02:36.440><c> folks</c>

00:02:36.710 --> 00:02:36.720 align:start position:0%
resolved there are a bunch of folks
 

00:02:36.720 --> 00:02:39.830 align:start position:0%
resolved there are a bunch of folks
using<00:02:37.000><c> olama</c><00:02:37.519><c> in</c><00:02:37.800><c> countries</c><00:02:38.200><c> with</c><00:02:38.640><c> uh</c><00:02:39.519><c> well</c>

00:02:39.830 --> 00:02:39.840 align:start position:0%
using olama in countries with uh well
 

00:02:39.840 --> 00:02:42.149 align:start position:0%
using olama in countries with uh well
questionable<00:02:40.440><c> access</c><00:02:40.720><c> to</c><00:02:40.879><c> the</c><00:02:41.159><c> internet</c>

00:02:42.149 --> 00:02:42.159 align:start position:0%
questionable access to the internet
 

00:02:42.159 --> 00:02:44.750 align:start position:0%
questionable access to the internet
either<00:02:42.480><c> it's</c><00:02:42.680><c> slow</c><00:02:43.200><c> or</c><00:02:43.560><c> connections</c><00:02:44.040><c> drop</c><00:02:44.440><c> or</c>

00:02:44.750 --> 00:02:44.760 align:start position:0%
either it's slow or connections drop or
 

00:02:44.760 --> 00:02:46.830 align:start position:0%
either it's slow or connections drop or
lots<00:02:45.000><c> of</c><00:02:45.159><c> noise</c><00:02:45.519><c> on</c><00:02:45.680><c> the</c><00:02:45.879><c> line</c><00:02:46.360><c> you</c><00:02:46.519><c> know</c>

00:02:46.830 --> 00:02:46.840 align:start position:0%
lots of noise on the line you know
 

00:02:46.840 --> 00:02:49.710 align:start position:0%
lots of noise on the line you know
places<00:02:47.200><c> like</c><00:02:47.360><c> the</c><00:02:47.640><c> us</c><00:02:48.640><c> but</c><00:02:49.000><c> also</c><00:02:49.360><c> Eastern</c>

00:02:49.710 --> 00:02:49.720 align:start position:0%
places like the us but also Eastern
 

00:02:49.720 --> 00:02:51.670 align:start position:0%
places like the us but also Eastern
European<00:02:50.159><c> countries</c><00:02:50.599><c> certain</c><00:02:51.120><c> Central</c><00:02:51.440><c> and</c>

00:02:51.670 --> 00:02:51.680 align:start position:0%
European countries certain Central and
 

00:02:51.680 --> 00:02:54.070 align:start position:0%
European countries certain Central and
South<00:02:51.959><c> American</c><00:02:52.400><c> countries</c><00:02:52.800><c> and</c><00:02:53.200><c> well</c><00:02:53.599><c> others</c>

00:02:54.070 --> 00:02:54.080 align:start position:0%
South American countries and well others
 

00:02:54.080 --> 00:02:55.990 align:start position:0%
South American countries and well others
all<00:02:54.239><c> over</c><00:02:54.440><c> the</c><00:02:54.560><c> world</c><00:02:55.440><c> well</c><00:02:55.640><c> if</c><00:02:55.800><c> that</c>

00:02:55.990 --> 00:02:56.000 align:start position:0%
all over the world well if that
 

00:02:56.000 --> 00:02:58.309 align:start position:0%
all over the world well if that
connection<00:02:56.360><c> dropped</c><00:02:56.920><c> for</c><00:02:57.400><c> whatever</c><00:02:57.800><c> reason</c>

00:02:58.309 --> 00:02:58.319 align:start position:0%
connection dropped for whatever reason
 

00:02:58.319 --> 00:03:00.710 align:start position:0%
connection dropped for whatever reason
during<00:02:58.640><c> a</c><00:02:58.840><c> pull</c><00:02:59.159><c> or</c><00:02:59.319><c> a</c><00:02:59.480><c> push</c><00:03:00.159><c> you</c><00:03:00.400><c> get</c><00:03:00.519><c> a</c>

00:03:00.710 --> 00:03:00.720 align:start position:0%
during a pull or a push you get a
 

00:03:00.720 --> 00:03:03.630 align:start position:0%
during a pull or a push you get a
cryptic<00:03:01.200><c> error</c><00:03:01.800><c> message</c><00:03:02.800><c> those</c><00:03:03.000><c> should</c><00:03:03.239><c> be</c>

00:03:03.630 --> 00:03:03.640 align:start position:0%
cryptic error message those should be
 

00:03:03.640 --> 00:03:06.390 align:start position:0%
cryptic error message those should be
resolved<00:03:04.560><c> it</c><00:03:04.799><c> can't</c><00:03:05.239><c> fix</c><00:03:05.519><c> your</c><00:03:05.680><c> connection</c>

00:03:06.390 --> 00:03:06.400 align:start position:0%
resolved it can't fix your connection
 

00:03:06.400 --> 00:03:08.630 align:start position:0%
resolved it can't fix your connection
but<00:03:06.560><c> it</c><00:03:06.640><c> can</c><00:03:06.799><c> be</c><00:03:06.959><c> a</c><00:03:07.080><c> bit</c><00:03:07.200><c> more</c><00:03:07.400><c> resilient</c><00:03:08.080><c> and</c>

00:03:08.630 --> 00:03:08.640 align:start position:0%
but it can be a bit more resilient and
 

00:03:08.640 --> 00:03:10.670 align:start position:0%
but it can be a bit more resilient and
any<00:03:08.879><c> error</c><00:03:09.239><c> that</c><00:03:09.440><c> happens</c><00:03:10.040><c> should</c><00:03:10.280><c> at</c><00:03:10.400><c> least</c>

00:03:10.670 --> 00:03:10.680 align:start position:0%
any error that happens should at least
 

00:03:10.680 --> 00:03:13.509 align:start position:0%
any error that happens should at least
make<00:03:11.040><c> sense</c><00:03:12.040><c> so</c><00:03:12.280><c> all</c><00:03:12.440><c> of</c><00:03:12.599><c> those</c><00:03:12.840><c> are</c><00:03:13.200><c> huge</c>

00:03:13.509 --> 00:03:13.519 align:start position:0%
make sense so all of those are huge
 

00:03:13.519 --> 00:03:15.990 align:start position:0%
make sense so all of those are huge
fixes<00:03:14.239><c> if</c><00:03:14.360><c> you</c><00:03:14.640><c> affected</c><00:03:15.000><c> by</c><00:03:15.159><c> them</c><00:03:15.400><c> but</c><00:03:15.879><c> I</c>

00:03:15.990 --> 00:03:16.000 align:start position:0%
fixes if you affected by them but I
 

00:03:16.000 --> 00:03:19.390 align:start position:0%
fixes if you affected by them but I
would<00:03:16.280><c> imagine</c><00:03:16.799><c> that</c><00:03:17.760><c> 90%</c><00:03:18.519><c> of</c><00:03:18.680><c> users</c><00:03:19.080><c> weren't</c>

00:03:19.390 --> 00:03:19.400 align:start position:0%
would imagine that 90% of users weren't
 

00:03:19.400 --> 00:03:23.229 align:start position:0%
would imagine that 90% of users weren't
touched<00:03:20.000><c> by</c><00:03:20.319><c> any</c><00:03:20.519><c> of</c><00:03:20.640><c> them</c><00:03:21.319><c> but</c><00:03:21.560><c> man</c><00:03:21.840><c> those</c>

00:03:23.229 --> 00:03:23.239 align:start position:0%
touched by any of them but man those
 

00:03:23.239 --> 00:03:26.430 align:start position:0%
touched by any of them but man those
10%<00:03:24.239><c> they're</c><00:03:24.440><c> certainly</c><00:03:25.000><c> vocal</c><00:03:26.000><c> well</c><00:03:26.200><c> let's</c>

00:03:26.430 --> 00:03:26.440 align:start position:0%
10% they're certainly vocal well let's
 

00:03:26.440 --> 00:03:28.750 align:start position:0%
10% they're certainly vocal well let's
look<00:03:26.599><c> at</c><00:03:26.720><c> some</c><00:03:26.959><c> settings</c><00:03:27.400><c> that</c><00:03:27.560><c> help</c><00:03:28.280><c> everyone</c>

00:03:28.750 --> 00:03:28.760 align:start position:0%
look at some settings that help everyone
 

00:03:28.760 --> 00:03:31.190 align:start position:0%
look at some settings that help everyone
out<00:03:29.200><c> first</c><00:03:29.760><c> is</c><00:03:29.840><c> the</c><00:03:30.000><c> messages</c><00:03:30.560><c> directive</c><00:03:31.080><c> in</c>

00:03:31.190 --> 00:03:31.200 align:start position:0%
out first is the messages directive in
 

00:03:31.200 --> 00:03:33.990 align:start position:0%
out first is the messages directive in
the<00:03:31.360><c> model</c><00:03:32.080><c> file</c><00:03:33.080><c> recently</c><00:03:33.560><c> the</c><00:03:33.720><c> chat</c>

00:03:33.990 --> 00:03:34.000 align:start position:0%
the model file recently the chat
 

00:03:34.000 --> 00:03:36.470 align:start position:0%
the model file recently the chat
endpoint<00:03:34.519><c> was</c><00:03:34.760><c> added</c><00:03:35.040><c> to</c><00:03:35.200><c> the</c><00:03:35.360><c> API</c><00:03:36.159><c> this</c><00:03:36.280><c> made</c>

00:03:36.470 --> 00:03:36.480 align:start position:0%
endpoint was added to the API this made
 

00:03:36.480 --> 00:03:38.990 align:start position:0%
endpoint was added to the API this made
it<00:03:36.760><c> super</c><00:03:37.120><c> easy</c><00:03:37.360><c> to</c><00:03:37.560><c> add</c><00:03:37.799><c> a</c><00:03:37.920><c> few</c><00:03:38.239><c> shot</c><00:03:38.519><c> prompt</c>

00:03:38.990 --> 00:03:39.000 align:start position:0%
it super easy to add a few shot prompt
 

00:03:39.000 --> 00:03:41.030 align:start position:0%
it super easy to add a few shot prompt
to<00:03:39.159><c> a</c><00:03:39.319><c> model</c><00:03:39.720><c> which</c><00:03:39.840><c> is</c><00:03:40.040><c> great</c><00:03:40.280><c> for</c><00:03:40.519><c> providing</c>

00:03:41.030 --> 00:03:41.040 align:start position:0%
to a model which is great for providing
 

00:03:41.040 --> 00:03:43.630 align:start position:0%
to a model which is great for providing
examples<00:03:41.599><c> of</c><00:03:41.799><c> what</c><00:03:41.920><c> you</c><00:03:42.040><c> want</c><00:03:42.879><c> for</c><00:03:43.080><c> instance</c>

00:03:43.630 --> 00:03:43.640 align:start position:0%
examples of what you want for instance
 

00:03:43.640 --> 00:03:45.910 align:start position:0%
examples of what you want for instance
if<00:03:43.760><c> you</c><00:03:43.879><c> want</c><00:03:44.000><c> to</c><00:03:44.159><c> Output</c><00:03:44.519><c> Json</c><00:03:45.200><c> it</c><00:03:45.280><c> would</c><00:03:45.519><c> help</c>

00:03:45.910 --> 00:03:45.920 align:start position:0%
if you want to Output Json it would help
 

00:03:45.920 --> 00:03:47.990 align:start position:0%
if you want to Output Json it would help
to<00:03:46.080><c> provide</c><00:03:46.400><c> the</c><00:03:46.519><c> schema</c><00:03:47.000><c> in</c><00:03:47.159><c> the</c><00:03:47.319><c> prompt</c><00:03:47.799><c> and</c>

00:03:47.990 --> 00:03:48.000 align:start position:0%
to provide the schema in the prompt and
 

00:03:48.000 --> 00:03:49.949 align:start position:0%
to provide the schema in the prompt and
then<00:03:48.280><c> things</c><00:03:48.519><c> got</c><00:03:48.799><c> better</c><00:03:49.560><c> if</c><00:03:49.680><c> you</c><00:03:49.799><c> could</c>

00:03:49.949 --> 00:03:49.959 align:start position:0%
then things got better if you could
 

00:03:49.959 --> 00:03:52.830 align:start position:0%
then things got better if you could
include<00:03:50.360><c> some</c><00:03:50.840><c> examples</c><00:03:51.840><c> well</c><00:03:52.280><c> now</c><00:03:52.519><c> you</c><00:03:52.599><c> can</c>

00:03:52.830 --> 00:03:52.840 align:start position:0%
include some examples well now you can
 

00:03:52.840 --> 00:03:55.190 align:start position:0%
include some examples well now you can
provide<00:03:53.200><c> those</c><00:03:53.400><c> examples</c><00:03:54.000><c> in</c><00:03:54.120><c> the</c><00:03:54.319><c> model</c><00:03:54.799><c> file</c>

00:03:55.190 --> 00:03:55.200 align:start position:0%
provide those examples in the model file
 

00:03:55.200 --> 00:03:57.910 align:start position:0%
provide those examples in the model file
as<00:03:55.360><c> well</c><00:03:56.200><c> you</c><00:03:56.400><c> use</c><00:03:56.840><c> message</c><00:03:57.439><c> and</c><00:03:57.599><c> then</c><00:03:57.760><c> the</c>

00:03:57.910 --> 00:03:57.920 align:start position:0%
as well you use message and then the
 

00:03:57.920 --> 00:03:59.910 align:start position:0%
as well you use message and then the
role<00:03:58.560><c> which</c><00:03:58.760><c> looks</c><00:03:59.000><c> like</c><00:03:59.159><c> it's</c><00:03:59.360><c> limited</c><00:03:59.640><c> lied</c>

00:03:59.910 --> 00:03:59.920 align:start position:0%
role which looks like it's limited lied
 

00:03:59.920 --> 00:04:02.030 align:start position:0%
role which looks like it's limited lied
to<00:04:00.120><c> user</c><00:04:00.560><c> and</c><00:04:00.879><c> assistant</c><00:04:01.560><c> and</c><00:04:01.680><c> then</c><00:04:01.840><c> the</c>

00:04:02.030 --> 00:04:02.040 align:start position:0%
to user and assistant and then the
 

00:04:02.040 --> 00:04:06.069 align:start position:0%
to user and assistant and then the
question<00:04:02.519><c> or</c><00:04:03.000><c> answer</c><00:04:04.000><c> pretty</c><00:04:04.480><c> cool</c><00:04:04.920><c> stuff</c><00:04:05.920><c> in</c>

00:04:06.069 --> 00:04:06.079 align:start position:0%
question or answer pretty cool stuff in
 

00:04:06.079 --> 00:04:08.309 align:start position:0%
question or answer pretty cool stuff in
the<00:04:06.239><c> beginning</c><00:04:06.680><c> AMA</c><00:04:07.200><c> just</c><00:04:07.360><c> let</c><00:04:07.519><c> you</c><00:04:07.760><c> configure</c>

00:04:08.309 --> 00:04:08.319 align:start position:0%
the beginning AMA just let you configure
 

00:04:08.319 --> 00:04:09.789 align:start position:0%
the beginning AMA just let you configure
all<00:04:08.480><c> the</c><00:04:08.640><c> settings</c><00:04:08.959><c> for</c><00:04:09.120><c> a</c><00:04:09.239><c> model</c><00:04:09.560><c> in</c><00:04:09.680><c> the</c>

00:04:09.789 --> 00:04:09.799 align:start position:0%
all the settings for a model in the
 

00:04:09.799 --> 00:04:12.190 align:start position:0%
all the settings for a model in the
model<00:04:10.159><c> file</c><00:04:10.799><c> now</c><00:04:11.000><c> over</c><00:04:11.360><c> time</c><00:04:11.640><c> more</c><00:04:11.840><c> of</c><00:04:12.000><c> that</c>

00:04:12.190 --> 00:04:12.200 align:start position:0%
model file now over time more of that
 

00:04:12.200 --> 00:04:14.830 align:start position:0%
model file now over time more of that
config<00:04:12.840><c> also</c><00:04:13.159><c> happen</c><00:04:13.519><c> in</c><00:04:13.599><c> the</c><00:04:13.720><c> AMA</c><00:04:14.120><c> reppel</c>

00:04:14.830 --> 00:04:14.840 align:start position:0%
config also happen in the AMA reppel
 

00:04:14.840 --> 00:04:17.909 align:start position:0%
config also happen in the AMA reppel
with<00:04:15.000><c> the/</c><00:04:15.799><c> set</c><00:04:16.120><c> commands</c><00:04:17.120><c> you</c><00:04:17.239><c> can</c><00:04:17.519><c> update</c>

00:04:17.909 --> 00:04:17.919 align:start position:0%
with the/ set commands you can update
 

00:04:17.919 --> 00:04:19.990 align:start position:0%
with the/ set commands you can update
the<00:04:18.040><c> system</c><00:04:18.400><c> prompt</c><00:04:18.799><c> or</c><00:04:19.000><c> template</c><00:04:19.440><c> or</c><00:04:19.680><c> any</c><00:04:19.840><c> of</c>

00:04:19.990 --> 00:04:20.000 align:start position:0%
the system prompt or template or any of
 

00:04:20.000 --> 00:04:22.310 align:start position:0%
the system prompt or template or any of
the<00:04:20.120><c> parameters</c><00:04:20.639><c> there</c><00:04:21.359><c> but</c><00:04:21.479><c> there</c><00:04:21.639><c> wasn't</c><00:04:22.079><c> an</c>

00:04:22.310 --> 00:04:22.320 align:start position:0%
the parameters there but there wasn't an
 

00:04:22.320 --> 00:04:25.510 align:start position:0%
the parameters there but there wasn't an
easy<00:04:22.680><c> way</c><00:04:22.880><c> to</c><00:04:23.199><c> serialize</c><00:04:24.080><c> your</c><00:04:24.320><c> new</c><00:04:24.600><c> creation</c>

00:04:25.510 --> 00:04:25.520 align:start position:0%
easy way to serialize your new creation
 

00:04:25.520 --> 00:04:28.629 align:start position:0%
easy way to serialize your new creation
well<00:04:25.759><c> now</c><00:04:26.000><c> there</c><00:04:26.199><c> is</c><00:04:26.560><c> also</c><00:04:26.960><c> a/s</c><00:04:27.639><c> saave</c><00:04:27.960><c> command</c>

00:04:28.629 --> 00:04:28.639 align:start position:0%
well now there is also a/s saave command
 

00:04:28.639 --> 00:04:30.629 align:start position:0%
well now there is also a/s saave command
that<00:04:28.759><c> lets</c><00:04:28.960><c> you</c><00:04:29.160><c> save</c><00:04:29.440><c> what</c><00:04:29.720><c> you've</c><00:04:29.919><c> done</c><00:04:30.320><c> to</c><00:04:30.520><c> a</c>

00:04:30.629 --> 00:04:30.639 align:start position:0%
that lets you save what you've done to a
 

00:04:30.639 --> 00:04:32.749 align:start position:0%
that lets you save what you've done to a
new<00:04:30.840><c> model</c><00:04:31.840><c> and</c><00:04:32.000><c> when</c><00:04:32.120><c> you're</c><00:04:32.320><c> done</c><00:04:32.520><c> with</c><00:04:32.639><c> a</c>

00:04:32.749 --> 00:04:32.759 align:start position:0%
new model and when you're done with a
 

00:04:32.759 --> 00:04:35.469 align:start position:0%
new model and when you're done with a
model<00:04:33.120><c> you</c><00:04:33.240><c> can</c><00:04:33.479><c> use</c><00:04:33.919><c> SL</c><00:04:34.440><c> load</c><00:04:34.919><c> and</c><00:04:35.039><c> a</c><00:04:35.199><c> model</c>

00:04:35.469 --> 00:04:35.479 align:start position:0%
model you can use SL load and a model
 

00:04:35.479 --> 00:04:38.629 align:start position:0%
model you can use SL load and a model
name<00:04:36.120><c> and</c><00:04:36.240><c> you</c><00:04:36.320><c> will</c><00:04:36.560><c> get</c><00:04:36.759><c> a</c><00:04:36.960><c> new</c><00:04:37.199><c> model</c><00:04:37.720><c> loaded</c>

00:04:38.629 --> 00:04:38.639 align:start position:0%
name and you will get a new model loaded
 

00:04:38.639 --> 00:04:40.749 align:start position:0%
name and you will get a new model loaded
so<00:04:38.919><c> maybe</c><00:04:39.160><c> you</c><00:04:39.280><c> want</c><00:04:39.360><c> to</c><00:04:39.560><c> switch</c><00:04:39.880><c> from</c><00:04:40.080><c> llama</c><00:04:40.479><c> 2</c>

00:04:40.749 --> 00:04:40.759 align:start position:0%
so maybe you want to switch from llama 2
 

00:04:40.759 --> 00:04:43.790 align:start position:0%
so maybe you want to switch from llama 2
to<00:04:41.000><c> mistol</c><00:04:41.800><c> that's</c><00:04:42.000><c> easy</c><00:04:42.240><c> to</c><00:04:42.400><c> do</c><00:04:43.120><c> you</c><00:04:43.199><c> can</c><00:04:43.520><c> also</c>

00:04:43.790 --> 00:04:43.800 align:start position:0%
to mistol that's easy to do you can also
 

00:04:43.800 --> 00:04:46.550 align:start position:0%
to mistol that's easy to do you can also
use<00:04:43.960><c> it</c><00:04:44.199><c> as</c><00:04:44.320><c> a</c><00:04:44.479><c> way</c><00:04:44.639><c> to</c><00:04:44.840><c> clear</c><00:04:45.199><c> the</c><00:04:45.400><c> context</c><00:04:46.400><c> so</c>

00:04:46.550 --> 00:04:46.560 align:start position:0%
use it as a way to clear the context so
 

00:04:46.560 --> 00:04:49.990 align:start position:0%
use it as a way to clear the context so
if<00:04:46.639><c> you're</c><00:04:46.880><c> in</c><00:04:47.120><c> mistol</c><00:04:48.080><c> try</c><00:04:48.639><c> SL</c><00:04:49.039><c> load</c><00:04:49.240><c> mistol</c>

00:04:49.990 --> 00:04:50.000 align:start position:0%
if you're in mistol try SL load mistol
 

00:04:50.000 --> 00:04:51.790 align:start position:0%
if you're in mistol try SL load mistol
and<00:04:50.120><c> it</c><00:04:50.240><c> will</c><00:04:50.520><c> forget</c><00:04:50.919><c> the</c><00:04:51.080><c> context</c><00:04:51.520><c> from</c><00:04:51.639><c> the</c>

00:04:51.790 --> 00:04:51.800 align:start position:0%
and it will forget the context from the
 

00:04:51.800 --> 00:04:53.790 align:start position:0%
and it will forget the context from the
previous<00:04:52.120><c> conversation</c><00:04:52.919><c> this</c><00:04:53.039><c> is</c><00:04:53.160><c> a</c><00:04:53.360><c> common</c>

00:04:53.790 --> 00:04:53.800 align:start position:0%
previous conversation this is a common
 

00:04:53.800 --> 00:04:55.469 align:start position:0%
previous conversation this is a common
request<00:04:54.120><c> in</c><00:04:54.240><c> the</c><00:04:54.400><c> Discord</c><00:04:55.000><c> you</c><00:04:55.120><c> know</c><00:04:55.280><c> about</c>

00:04:55.469 --> 00:04:55.479 align:start position:0%
request in the Discord you know about
 

00:04:55.479 --> 00:04:59.150 align:start position:0%
request in the Discord you know about
Discord<00:04:55.960><c> right</c><00:04:56.880><c> discord.gg</c><00:04:57.960><c> olama</c><00:04:58.960><c> and</c><00:04:59.080><c> when</c>

00:04:59.150 --> 00:04:59.160 align:start position:0%
Discord right discord.gg olama and when
 

00:04:59.160 --> 00:05:01.550 align:start position:0%
Discord right discord.gg olama and when
you<00:04:59.280><c> go</c><00:04:59.680><c> there</c><00:05:00.000><c> share</c><00:05:00.400><c> your</c><00:05:00.759><c> favorite</c><00:05:01.199><c> techno</c>

00:05:01.550 --> 00:05:01.560 align:start position:0%
you go there share your favorite techno
 

00:05:01.560 --> 00:05:04.270 align:start position:0%
you go there share your favorite techno
evangelist<00:05:02.080><c> video</c><00:05:02.440><c> to</c><00:05:02.639><c> let</c><00:05:03.000><c> everyone</c><00:05:03.639><c> know</c>

00:05:04.270 --> 00:05:04.280 align:start position:0%
evangelist video to let everyone know
 

00:05:04.280 --> 00:05:06.629 align:start position:0%
evangelist video to let everyone know
that<00:05:04.400><c> you</c><00:05:04.560><c> are</c>

00:05:06.629 --> 00:05:06.639 align:start position:0%
that you are
 

00:05:06.639 --> 00:05:09.350 align:start position:0%
that you are
awesome<00:05:07.639><c> one</c><00:05:07.800><c> more</c><00:05:08.039><c> thing</c><00:05:08.440><c> that</c><00:05:08.639><c> I'm</c><00:05:08.919><c> excited</c>

00:05:09.350 --> 00:05:09.360 align:start position:0%
awesome one more thing that I'm excited
 

00:05:09.360 --> 00:05:12.350 align:start position:0%
awesome one more thing that I'm excited
about<00:05:09.840><c> is</c><00:05:10.000><c> a</c><00:05:10.240><c> slow</c><00:05:11.080><c> parameter</c><00:05:11.600><c> command</c><00:05:12.280><c> it</c>

00:05:12.350 --> 00:05:12.360 align:start position:0%
about is a slow parameter command it
 

00:05:12.360 --> 00:05:14.390 align:start position:0%
about is a slow parameter command it
will<00:05:12.600><c> now</c><00:05:12.880><c> output</c><00:05:13.360><c> the</c><00:05:13.520><c> correct</c>

00:05:14.390 --> 00:05:14.400 align:start position:0%
will now output the correct
 

00:05:14.400 --> 00:05:17.990 align:start position:0%
will now output the correct
setting<00:05:15.400><c> often</c><00:05:15.919><c> I</c><00:05:16.000><c> would</c><00:05:16.160><c> set</c><00:05:16.440><c> temp</c><00:05:16.720><c> to</c><00:05:16.960><c> 0.9</c><00:05:17.759><c> or</c>

00:05:17.990 --> 00:05:18.000 align:start position:0%
setting often I would set temp to 0.9 or
 

00:05:18.000 --> 00:05:21.469 align:start position:0%
setting often I would set temp to 0.9 or
1.2<00:05:18.880><c> and/</c><00:05:19.880><c> show</c><00:05:20.160><c> parameters</c><00:05:20.919><c> would</c><00:05:21.080><c> say</c><00:05:21.319><c> that</c>

00:05:21.469 --> 00:05:21.479 align:start position:0%
1.2 and/ show parameters would say that
 

00:05:21.479 --> 00:05:24.870 align:start position:0%
1.2 and/ show parameters would say that
the<00:05:21.600><c> temp</c><00:05:21.960><c> was</c><00:05:22.600><c> one</c><00:05:23.600><c> now</c><00:05:24.000><c> it</c><00:05:24.120><c> will</c><00:05:24.319><c> output</c><00:05:24.759><c> the</c>

00:05:24.870 --> 00:05:24.880 align:start position:0%
the temp was one now it will output the
 

00:05:24.880 --> 00:05:27.710 align:start position:0%
the temp was one now it will output the
correct<00:05:25.560><c> values</c><00:05:26.560><c> and</c><00:05:26.720><c> that's</c><00:05:26.919><c> what's</c><00:05:27.160><c> new</c><00:05:27.520><c> in</c>

00:05:27.710 --> 00:05:27.720 align:start position:0%
correct values and that's what's new in
 

00:05:27.720 --> 00:05:31.029 align:start position:0%
correct values and that's what's new in
version<00:05:28.479><c> 0.1.2</c><00:05:29.840><c> and</c>

00:05:31.029 --> 00:05:31.039 align:start position:0%
version 0.1.2 and
 

00:05:31.039 --> 00:05:35.270 align:start position:0%
version 0.1.2 and
0.122<00:05:32.039><c> of</c><00:05:32.560><c> olama</c><00:05:33.560><c> I</c><00:05:33.680><c> think</c><00:05:34.000><c> these</c><00:05:34.319><c> have</c><00:05:34.680><c> a</c><00:05:34.960><c> lot</c>

00:05:35.270 --> 00:05:35.280 align:start position:0%
0.122 of olama I think these have a lot
 

00:05:35.280 --> 00:05:37.510 align:start position:0%
0.122 of olama I think these have a lot
for<00:05:35.639><c> everyone</c><00:05:36.080><c> and</c><00:05:36.199><c> are</c><00:05:36.360><c> going</c><00:05:36.520><c> to</c><00:05:36.639><c> be</c><00:05:36.960><c> magic</c>

00:05:37.510 --> 00:05:37.520 align:start position:0%
for everyone and are going to be magic
 

00:05:37.520 --> 00:05:39.629 align:start position:0%
for everyone and are going to be magic
for<00:05:37.759><c> a</c><00:05:37.960><c> few</c><00:05:38.960><c> what</c><00:05:39.080><c> do</c><00:05:39.160><c> you</c><00:05:39.240><c> think</c><00:05:39.400><c> of</c><00:05:39.520><c> the</c>

00:05:39.629 --> 00:05:39.639 align:start position:0%
for a few what do you think of the
 

00:05:39.639 --> 00:05:41.870 align:start position:0%
for a few what do you think of the
messages<00:05:40.199><c> in</c><00:05:40.360><c> the</c><00:05:40.479><c> model</c><00:05:40.800><c> file</c><00:05:41.080><c> feature</c><00:05:41.759><c> is</c>

00:05:41.870 --> 00:05:41.880 align:start position:0%
messages in the model file feature is
 

00:05:41.880 --> 00:05:43.430 align:start position:0%
messages in the model file feature is
there<00:05:42.080><c> something</c><00:05:42.440><c> else</c><00:05:42.720><c> that</c><00:05:42.880><c> resonates</c><00:05:43.319><c> with</c>

00:05:43.430 --> 00:05:43.440 align:start position:0%
there something else that resonates with
 

00:05:43.440 --> 00:05:45.710 align:start position:0%
there something else that resonates with
you<00:05:43.600><c> in</c><00:05:43.720><c> this</c><00:05:43.880><c> new</c><00:05:44.080><c> version</c><00:05:45.039><c> if</c><00:05:45.160><c> you</c><00:05:45.280><c> find</c><00:05:45.560><c> this</c>

00:05:45.710 --> 00:05:45.720 align:start position:0%
you in this new version if you find this
 

00:05:45.720 --> 00:05:47.909 align:start position:0%
you in this new version if you find this
useful<00:05:46.240><c> like</c><00:05:46.400><c> And</c><00:05:46.639><c> subscribe</c><00:05:47.319><c> and</c><00:05:47.520><c> thanks</c><00:05:47.759><c> so</c>

00:05:47.909 --> 00:05:47.919 align:start position:0%
useful like And subscribe and thanks so
 

00:05:47.919 --> 00:05:50.520 align:start position:0%
useful like And subscribe and thanks so
much<00:05:48.039><c> for</c><00:05:48.280><c> watching</c>

00:05:50.520 --> 00:05:50.530 align:start position:0%
much for watching
 

00:05:50.530 --> 00:05:58.469 align:start position:0%
much for watching
[Music]

00:05:58.469 --> 00:05:58.479 align:start position:0%
 
 

00:05:58.479 --> 00:06:01.479 align:start position:0%
 
goodbye

