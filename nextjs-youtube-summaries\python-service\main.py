#!/usr/bin/env python3
"""
YouTube Summaries AI Service
FastAPI server to handle AI operations for the NextJS application
This service integrates with your existing gemini_methods infrastructure
"""

import os
import sys
import asyncio
from typing import List, Dict, Optional, Any
from pydantic import BaseModel
from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
import uvicorn
from dotenv import load_dotenv

# Load environment variables from the main workspace .env file
main_workspace = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
env_path = os.path.join(main_workspace, '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"✅ Loaded environment variables from: {env_path}")
else:
    print(f"⚠️ Warning: Could not find .env file at {env_path}")
    load_dotenv()  # Load from current directory as fallback

# Add the main workspace directory to sys.path to import gemini_methods
sys.path.append(main_workspace)

try:
    from gemini_methods.genai_utils import (
        generate_embedding,
        get_available_embedding_models,
        get_default_embedding_model,
        call_gemini_api,
        DEFAULT_MODEL_NAME,
        DEFAULT_GENERATION_CONFIG,
        DEFAULT_SAFETY_SETTINGS
    )
    from gemini_methods.chat import (
        ChatManager,
        create_simple_chat
    )
    import pydantic
    GEMINI_AVAILABLE = True
    print("✅ Successfully imported gemini_methods!")
    print("✅ Successfully imported chat functionality!")
except ImportError as e:
    print(f"❌ Failed to initialize Google GenAI Client: {e}. Ensure GOOGLE_API_KEY or GEMINI_API_KEY is set.")
    GEMINI_AVAILABLE = False
    # Fallback values when gemini_methods is not available
    DEFAULT_MODEL_NAME = "gemini-2.5-flash-preview-04-17"
    DEFAULT_GENERATION_CONFIG = {}
    DEFAULT_SAFETY_SETTINGS = []

# Initialize global chat manager for conversation memory
chat_manager = None
if GEMINI_AVAILABLE:
    try:
        chat_manager = ChatManager(db_pool=None)  # No database persistence for now
        print("✅ ChatManager initialized for conversation memory!")
    except Exception as e:
        print(f"⚠️ ChatManager initialization failed: {e}")
        chat_manager = None

app = FastAPI(
    title="YouTube Summaries AI Service",
    description="AI service for YouTube video summaries using Google Gemini",
    version="1.0.0"
)

# CORS middleware for NextJS integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:9696", "http://127.0.0.1:9696"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify API key for authentication"""
    expected_key = os.getenv("PYTHON_AI_SERVICE_API_KEY", "dev-key-123")
    if credentials.credentials != expected_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return credentials.credentials

# Request/Response Models
class ChatRequest(BaseModel):
    message: str
    context: Optional[str] = None
    system_prompt: Optional[str] = None
    model: Optional[str] = DEFAULT_MODEL_NAME
    max_tokens: Optional[int] = 4096
    temperature: Optional[float] = 0.1
    session_id: Optional[str] = None  # For conversation memory

class ChatResponse(BaseModel):
    response: str
    model: str
    tokens_used: Optional[int] = None
    cost_estimate: Optional[float] = None
    session_id: Optional[str] = None  # Return session ID for conversation continuity

# Pydantic response schema for Gemini API
class GeminiChatResponse(pydantic.BaseModel):
    response: str

class EmbeddingRequest(BaseModel):
    text: str
    model: Optional[str] = "text-embedding-004"

class EmbeddingResponse(BaseModel):
    embedding: List[float]
    model: str
    dimensions: int
    cost_estimate: Optional[float] = None

class HealthResponse(BaseModel):
    status: str
    gemini_available: bool
    version: str

# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        gemini_available=GEMINI_AVAILABLE,
        version="1.0.0"
    )

# Chat endpoint
@app.post("/chat", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    api_key: str = Depends(verify_api_key)
):
    """Generate AI chat response using Gemini with conversation memory"""
    if not GEMINI_AVAILABLE or not chat_manager:
        raise HTTPException(
            status_code=503,
            detail="Gemini AI service is not available"
        )
    
    try:
        # Check if we have an existing session or need to create a new one
        session_id = request.session_id
        
        if session_id:
            # Verify the session exists
            try:
                await chat_manager.get_session_info(session_id)
            except ValueError:
                # Session doesn't exist, create a new one
                session_id = None
        
        if not session_id:
            # Create a new chat session with system context
            system_instruction = request.system_prompt or 'You are a helpful AI assistant for YouTube video content.'
            
            # If we have context, add it to the system instruction
            if request.context:
                system_instruction += f"""

CONTEXT INFORMATION:
{request.context}

Based on the context provided above, answer user questions accurately and helpfully. Always cite which videos you're referencing in your response when relevant."""
            
            session_id = await chat_manager.create_chat_session(
                model_name=request.model,
                system_instruction=system_instruction
            )
            print(f"🆕 Created new chat session: {session_id}")
        
        # Send the message to the chat session
        response_text, input_tokens, output_tokens, cost = await chat_manager.send_message(
            session_id=session_id,
            message=request.message
        )
        
        return ChatResponse(
            response=response_text,
            model=request.model,
            tokens_used=output_tokens,
            cost_estimate=cost,
            session_id=session_id
        )
        
    except Exception as e:
        print(f"❌ Error in chat endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating chat response: {str(e)}"
        )

# Embeddings endpoint
@app.post("/embeddings", response_model=EmbeddingResponse)
async def create_embedding(
    request: EmbeddingRequest,
    api_key: str = Depends(verify_api_key)
):
    """Generate embeddings using Gemini"""
    if not GEMINI_AVAILABLE:
        raise HTTPException(
            status_code=503,
            detail="Gemini AI service is not available"
        )
    
    try:        # Generate embedding using genai_utils
        embedding_result = await generate_embedding(
            text=request.text,
            model_name=request.model
        )
        
        # Extract embedding data from our standardized return format
        embedding_values = embedding_result.get('values', [])
        model_used = embedding_result.get('model', request.model)
        dimensions = embedding_result.get('dimensions', len(embedding_values))
        cost_estimate = embedding_result.get('cost_estimate')
            
        return EmbeddingResponse(
            embedding=embedding_values,
            model=model_used,
            dimensions=dimensions,
            cost_estimate=cost_estimate
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error generating embedding: {str(e)}"
        )

# Batch embeddings endpoint (for bulk processing)
@app.post("/embeddings/batch")
async def create_batch_embeddings(
    texts: List[str],
    model: str = "text-embedding-004",
    api_key: str = Depends(verify_api_key)
):
    """Generate embeddings for multiple texts"""
    if not GEMINI_AVAILABLE:
        raise HTTPException(
            status_code=503,
            detail="Gemini AI service is not available"
        )
    
    try:
        embeddings = []
        for text in texts:
            embedding_result = await generate_embedding(
                text=text,
                model_name=model
            )
            if isinstance(embedding_result, dict):
                embedding_values = embedding_result.get('values', embedding_result.get('embedding', []))
            else:
                embedding_values = embedding_result
            embeddings.append(embedding_values)
        
        return {
            "embeddings": embeddings,
            "model": model,
            "count": len(embeddings)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error generating batch embeddings: {str(e)}"
        )

if __name__ == "__main__":
    # Configuration
    HOST = os.getenv("HOST", "127.0.0.1")
    PORT = int(os.getenv("PORT", "9000"))
    
    print(f"Starting YouTube Summaries AI Service on {HOST}:{PORT}")
    print(f"Gemini available: {GEMINI_AVAILABLE}")
    
    # Development server
    uvicorn.run(
        "main:app",
        host=HOST,
        port=PORT,
        reload=True,
        log_level="info"
    )
