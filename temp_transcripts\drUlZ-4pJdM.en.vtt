WEBVTT
Kind: captions
Language: en

00:00:00.640 --> 00:00:02.629 align:start position:0%
 
and<00:00:01.040><c> howdy</c><00:00:01.520><c> everybody</c><00:00:02.080><c> and</c><00:00:02.240><c> thanks</c><00:00:02.480><c> for</c>

00:00:02.629 --> 00:00:02.639 align:start position:0%
and howdy everybody and thanks for
 

00:00:02.639 --> 00:00:04.230 align:start position:0%
and howdy everybody and thanks for
tuning<00:00:02.960><c> in</c><00:00:03.040><c> to</c><00:00:03.199><c> today's</c><00:00:03.600><c> video</c>

00:00:04.230 --> 00:00:04.240 align:start position:0%
tuning in to today's video
 

00:00:04.240 --> 00:00:07.510 align:start position:0%
tuning in to today's video
today<00:00:04.640><c> we're</c><00:00:04.799><c> going</c><00:00:04.960><c> to</c><00:00:05.040><c> be</c><00:00:05.440><c> covering</c><00:00:06.799><c> um</c><00:00:07.200><c> how</c>

00:00:07.510 --> 00:00:07.520 align:start position:0%
today we're going to be covering um how
 

00:00:07.520 --> 00:00:08.070 align:start position:0%
today we're going to be covering um how
to

00:00:08.070 --> 00:00:08.080 align:start position:0%
to
 

00:00:08.080 --> 00:00:11.110 align:start position:0%
to
add<00:00:08.400><c> a</c><00:00:08.559><c> new</c><00:00:08.880><c> feature</c><00:00:09.599><c> all</c><00:00:09.760><c> right</c><00:00:10.719><c> so</c><00:00:10.960><c> it's</c>

00:00:11.110 --> 00:00:11.120 align:start position:0%
add a new feature all right so it's
 

00:00:11.120 --> 00:00:13.749 align:start position:0%
add a new feature all right so it's
going<00:00:11.200><c> to</c><00:00:11.280><c> be</c><00:00:11.519><c> really</c><00:00:11.840><c> cool</c>

00:00:13.749 --> 00:00:13.759 align:start position:0%
going to be really cool
 

00:00:13.759 --> 00:00:17.590 align:start position:0%
going to be really cool
and<00:00:14.480><c> first</c><00:00:14.719><c> of</c><00:00:14.880><c> all</c><00:00:15.040><c> over</c><00:00:15.280><c> here</c><00:00:15.519><c> i'm</c><00:00:15.679><c> using</c><00:00:16.480><c> um</c>

00:00:17.590 --> 00:00:17.600 align:start position:0%
and first of all over here i'm using um
 

00:00:17.600 --> 00:00:19.109 align:start position:0%
and first of all over here i'm using um
let's<00:00:17.760><c> see</c><00:00:17.920><c> i'm</c><00:00:18.080><c> using</c><00:00:18.400><c> a</c><00:00:18.480><c> linux</c><00:00:18.800><c> machine</c>

00:00:19.109 --> 00:00:19.119 align:start position:0%
let's see i'm using a linux machine
 

00:00:19.119 --> 00:00:21.029 align:start position:0%
let's see i'm using a linux machine
always<00:00:19.439><c> on</c><00:00:19.600><c> top</c><00:00:19.920><c> right</c>

00:00:21.029 --> 00:00:21.039 align:start position:0%
always on top right
 

00:00:21.039 --> 00:00:22.870 align:start position:0%
always on top right
i'm<00:00:21.279><c> using</c><00:00:21.520><c> google</c><00:00:21.920><c> calendar</c><00:00:22.400><c> because</c><00:00:22.720><c> it</c>

00:00:22.870 --> 00:00:22.880 align:start position:0%
i'm using google calendar because it
 

00:00:22.880 --> 00:00:25.830 align:start position:0%
i'm using google calendar because it
helps<00:00:23.199><c> me</c><00:00:23.840><c> helps</c><00:00:24.080><c> me</c><00:00:24.320><c> stay</c><00:00:24.560><c> organized</c><00:00:25.119><c> so</c>

00:00:25.830 --> 00:00:25.840 align:start position:0%
helps me helps me stay organized so
 

00:00:25.840 --> 00:00:28.710 align:start position:0%
helps me helps me stay organized so
here's<00:00:26.080><c> this</c><00:00:26.560><c> sk</c><00:00:27.279><c> enable</c><00:00:27.760><c> auto</c><00:00:28.080><c> approving</c><00:00:28.640><c> a</c>

00:00:28.710 --> 00:00:28.720 align:start position:0%
here's this sk enable auto approving a
 

00:00:28.720 --> 00:00:30.310 align:start position:0%
here's this sk enable auto approving a
lot<00:00:28.880><c> of</c><00:00:29.039><c> clients</c><00:00:29.439><c> that</c><00:00:29.599><c> are</c><00:00:29.760><c> installing</c><00:00:30.240><c> the</c>

00:00:30.310 --> 00:00:30.320 align:start position:0%
lot of clients that are installing the
 

00:00:30.320 --> 00:00:32.069 align:start position:0%
lot of clients that are installing the
shopify<00:00:30.880><c> app</c><00:00:31.119><c> are</c><00:00:31.279><c> asking</c>

00:00:32.069 --> 00:00:32.079 align:start position:0%
shopify app are asking
 

00:00:32.079 --> 00:00:34.069 align:start position:0%
shopify app are asking
for<00:00:32.239><c> their</c><00:00:32.480><c> posts</c><00:00:32.800><c> to</c><00:00:32.880><c> be</c><00:00:33.120><c> auto</c><00:00:33.520><c> approved</c><00:00:33.920><c> so</c>

00:00:34.069 --> 00:00:34.079 align:start position:0%
for their posts to be auto approved so
 

00:00:34.079 --> 00:00:35.910 align:start position:0%
for their posts to be auto approved so
let's<00:00:34.320><c> go</c><00:00:34.399><c> ahead</c><00:00:34.640><c> and</c><00:00:34.800><c> actually</c><00:00:35.280><c> get</c><00:00:35.520><c> started</c>

00:00:35.910 --> 00:00:35.920 align:start position:0%
let's go ahead and actually get started
 

00:00:35.920 --> 00:00:36.950 align:start position:0%
let's go ahead and actually get started
the<00:00:36.000><c> first</c><00:00:36.239><c> thing</c><00:00:36.399><c> we</c><00:00:36.480><c> need</c><00:00:36.640><c> to</c><00:00:36.719><c> do</c><00:00:36.880><c> is</c>

00:00:36.950 --> 00:00:36.960 align:start position:0%
the first thing we need to do is
 

00:00:36.960 --> 00:00:39.350 align:start position:0%
the first thing we need to do is
actually<00:00:37.440><c> open</c><00:00:37.680><c> up</c><00:00:37.840><c> two</c><00:00:38.160><c> terminals</c><00:00:38.719><c> all</c><00:00:38.800><c> right</c>

00:00:39.350 --> 00:00:39.360 align:start position:0%
actually open up two terminals all right
 

00:00:39.360 --> 00:00:42.310 align:start position:0%
actually open up two terminals all right
one<00:00:39.600><c> for</c><00:00:39.760><c> the</c><00:00:39.920><c> proxy</c><00:00:40.399><c> app</c><00:00:40.719><c> and</c><00:00:40.960><c> one</c><00:00:41.200><c> for</c><00:00:41.920><c> the</c>

00:00:42.310 --> 00:00:42.320 align:start position:0%
one for the proxy app and one for the
 

00:00:42.320 --> 00:00:43.110 align:start position:0%
one for the proxy app and one for the
admin<00:00:42.800><c> app</c>

00:00:43.110 --> 00:00:43.120 align:start position:0%
admin app
 

00:00:43.120 --> 00:00:44.630 align:start position:0%
admin app
all<00:00:43.200><c> right</c><00:00:43.280><c> so</c><00:00:43.440><c> let's</c><00:00:43.680><c> do</c><00:00:43.920><c> go</c><00:00:44.079><c> ahead</c><00:00:44.320><c> and</c><00:00:44.399><c> do</c>

00:00:44.630 --> 00:00:44.640 align:start position:0%
all right so let's do go ahead and do
 

00:00:44.640 --> 00:00:46.150 align:start position:0%
all right so let's do go ahead and do
mgrock<00:00:45.200><c> http</c>

00:00:46.150 --> 00:00:46.160 align:start position:0%
mgrock http
 

00:00:46.160 --> 00:00:48.630 align:start position:0%
mgrock http
3000<00:00:47.520><c> that's</c><00:00:47.760><c> going</c><00:00:47.840><c> to</c><00:00:48.000><c> run</c><00:00:48.160><c> with</c><00:00:48.320><c> no</c>

00:00:48.630 --> 00:00:48.640 align:start position:0%
3000 that's going to run with no
 

00:00:48.640 --> 00:00:49.430 align:start position:0%
3000 that's going to run with no
problems

00:00:49.430 --> 00:00:49.440 align:start position:0%
problems
 

00:00:49.440 --> 00:00:51.590 align:start position:0%
problems
uh<00:00:49.600><c> wait</c><00:00:49.840><c> a</c><00:00:49.920><c> second</c><00:00:50.239><c> you</c><00:00:50.320><c> know</c><00:00:50.480><c> let's</c><00:00:50.800><c> do</c><00:00:51.440><c> the</c>

00:00:51.590 --> 00:00:51.600 align:start position:0%
uh wait a second you know let's do the
 

00:00:51.600 --> 00:00:52.709 align:start position:0%
uh wait a second you know let's do the
more<00:00:51.840><c> important</c><00:00:52.239><c> one</c><00:00:52.480><c> is</c>

00:00:52.709 --> 00:00:52.719 align:start position:0%
more important one is
 

00:00:52.719 --> 00:00:55.990 align:start position:0%
more important one is
is<00:00:52.960><c> this</c><00:00:53.199><c> one</c><00:00:53.440><c> first</c><00:00:54.000><c> m</c><00:00:54.079><c> graph</c><00:00:54.399><c> http</c><00:00:55.440><c> all</c><00:00:55.600><c> right</c>

00:00:55.990 --> 00:00:56.000 align:start position:0%
is this one first m graph http all right
 

00:00:56.000 --> 00:00:58.790 align:start position:0%
is this one first m graph http all right
triple<00:00:56.480><c> seven</c><00:00:57.199><c> quadruple</c><00:00:57.760><c> seven</c><00:00:58.160><c> file</c><00:00:58.559><c> new</c>

00:00:58.790 --> 00:00:58.800 align:start position:0%
triple seven quadruple seven file new
 

00:00:58.800 --> 00:00:59.830 align:start position:0%
triple seven quadruple seven file new
tab

00:00:59.830 --> 00:00:59.840 align:start position:0%
tab
 

00:00:59.840 --> 00:01:03.910 align:start position:0%
tab
ngrok<00:01:00.640><c> http</c><00:01:01.760><c> 3000.</c>

00:01:03.910 --> 00:01:03.920 align:start position:0%
ngrok http 3000.
 

00:01:03.920 --> 00:01:05.830 align:start position:0%
ngrok http 3000.
all<00:01:04.080><c> right</c><00:01:04.400><c> let's</c><00:01:04.559><c> see</c><00:01:04.879><c> ah</c><00:01:05.119><c> boom</c><00:01:05.439><c> okay</c><00:01:05.760><c> of</c>

00:01:05.830 --> 00:01:05.840 align:start position:0%
all right let's see ah boom okay of
 

00:01:05.840 --> 00:01:07.270 align:start position:0%
all right let's see ah boom okay of
course<00:01:06.159><c> it's</c><00:01:06.320><c> not</c><00:01:06.479><c> going</c><00:01:06.560><c> to</c><00:01:06.720><c> work</c><00:01:06.960><c> why</c>

00:01:07.270 --> 00:01:07.280 align:start position:0%
course it's not going to work why
 

00:01:07.280 --> 00:01:09.030 align:start position:0%
course it's not going to work why
because<00:01:07.600><c> ngrok</c><00:01:08.080><c> limits</c><00:01:08.400><c> you</c><00:01:08.560><c> to</c><00:01:08.720><c> one</c>

00:01:09.030 --> 00:01:09.040 align:start position:0%
because ngrok limits you to one
 

00:01:09.040 --> 00:01:11.109 align:start position:0%
because ngrok limits you to one
ngrok<00:01:09.520><c> connection</c><00:01:10.080><c> if</c><00:01:10.240><c> you</c><00:01:10.320><c> don't</c><00:01:10.479><c> have</c><00:01:10.640><c> a</c>

00:01:11.109 --> 00:01:11.119 align:start position:0%
ngrok connection if you don't have a
 

00:01:11.119 --> 00:01:12.310 align:start position:0%
ngrok connection if you don't have a
paid<00:01:11.439><c> plan</c>

00:01:12.310 --> 00:01:12.320 align:start position:0%
paid plan
 

00:01:12.320 --> 00:01:14.310 align:start position:0%
paid plan
that's<00:01:12.640><c> why</c><00:01:12.960><c> in</c><00:01:13.119><c> the</c><00:01:13.600><c> in</c><00:01:13.760><c> the</c><00:01:13.840><c> beginning</c><00:01:14.240><c> of</c>

00:01:14.310 --> 00:01:14.320 align:start position:0%
that's why in the in the beginning of
 

00:01:14.320 --> 00:01:16.149 align:start position:0%
that's why in the in the beginning of
the<00:01:14.400><c> course</c><00:01:14.720><c> i</c><00:01:14.960><c> believe</c><00:01:15.280><c> i</c><00:01:15.439><c> said</c><00:01:15.680><c> that</c><00:01:15.840><c> it's</c>

00:01:16.149 --> 00:01:16.159 align:start position:0%
the course i believe i said that it's
 

00:01:16.159 --> 00:01:18.310 align:start position:0%
the course i believe i said that it's
it's<00:01:16.400><c> recommended</c><00:01:17.040><c> to</c><00:01:17.200><c> just</c><00:01:17.520><c> get</c><00:01:17.680><c> an</c><00:01:17.920><c> ngrok</c>

00:01:18.310 --> 00:01:18.320 align:start position:0%
it's recommended to just get an ngrok
 

00:01:18.320 --> 00:01:19.590 align:start position:0%
it's recommended to just get an ngrok
subscription

00:01:19.590 --> 00:01:19.600 align:start position:0%
subscription
 

00:01:19.600 --> 00:01:22.710 align:start position:0%
subscription
um<00:01:20.320><c> in</c><00:01:20.479><c> this</c><00:01:20.720><c> case</c><00:01:21.439><c> because</c><00:01:22.400><c> i</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
um in this case because i
 

00:01:22.720 --> 00:01:26.630 align:start position:0%
um in this case because i
um<00:01:23.360><c> because</c><00:01:23.680><c> i</c><00:01:23.759><c> do</c><00:01:24.000><c> want</c><00:01:24.159><c> to</c><00:01:25.040><c> actually</c>

00:01:26.630 --> 00:01:26.640 align:start position:0%
um because i do want to actually
 

00:01:26.640 --> 00:01:28.310 align:start position:0%
um because i do want to actually
add<00:01:26.799><c> some</c><00:01:26.960><c> new</c><00:01:27.200><c> features</c><00:01:27.520><c> to</c><00:01:27.680><c> the</c><00:01:27.840><c> app</c><00:01:28.080><c> i</c><00:01:28.159><c> think</c>

00:01:28.310 --> 00:01:28.320 align:start position:0%
add some new features to the app i think
 

00:01:28.320 --> 00:01:29.910 align:start position:0%
add some new features to the app i think
it<00:01:28.400><c> does</c><00:01:28.560><c> make</c><00:01:28.799><c> sense</c><00:01:29.040><c> to</c><00:01:29.200><c> just</c><00:01:29.360><c> go</c><00:01:29.520><c> ahead</c><00:01:29.759><c> and</c>

00:01:29.910 --> 00:01:29.920 align:start position:0%
it does make sense to just go ahead and
 

00:01:29.920 --> 00:01:30.230 align:start position:0%
it does make sense to just go ahead and
get

00:01:30.230 --> 00:01:30.240 align:start position:0%
get
 

00:01:30.240 --> 00:01:32.149 align:start position:0%
get
get<00:01:30.640><c> get</c><00:01:30.880><c> a</c><00:01:30.960><c> new</c><00:01:31.119><c> subscription</c><00:01:31.680><c> otherwise</c><00:01:32.079><c> it</c>

00:01:32.149 --> 00:01:32.159 align:start position:0%
get get a new subscription otherwise it
 

00:01:32.159 --> 00:01:33.830 align:start position:0%
get get a new subscription otherwise it
just<00:01:32.400><c> takes</c><00:01:32.640><c> forever</c>

00:01:33.830 --> 00:01:33.840 align:start position:0%
just takes forever
 

00:01:33.840 --> 00:01:37.270 align:start position:0%
just takes forever
every<00:01:34.079><c> single</c><00:01:34.479><c> time</c><00:01:36.000><c> um</c>

00:01:37.270 --> 00:01:37.280 align:start position:0%
every single time um
 

00:01:37.280 --> 00:01:39.670 align:start position:0%
every single time um
you<00:01:37.360><c> know</c><00:01:37.600><c> every</c><00:01:37.759><c> single</c><00:01:38.079><c> time</c><00:01:38.400><c> that</c><00:01:39.439><c> that</c><00:01:39.600><c> you</c>

00:01:39.670 --> 00:01:39.680 align:start position:0%
you know every single time that that you
 

00:01:39.680 --> 00:01:40.789 align:start position:0%
you know every single time that that you
want<00:01:39.840><c> to</c><00:01:40.000><c> work</c>

00:01:40.789 --> 00:01:40.799 align:start position:0%
want to work
 

00:01:40.799 --> 00:01:42.230 align:start position:0%
want to work
and<00:01:40.960><c> you</c><00:01:41.040><c> want</c><00:01:41.200><c> to</c><00:01:41.280><c> switch</c><00:01:41.520><c> between</c><00:01:41.840><c> the</c><00:01:41.920><c> admin</c>

00:01:42.230 --> 00:01:42.240 align:start position:0%
and you want to switch between the admin
 

00:01:42.240 --> 00:01:43.830 align:start position:0%
and you want to switch between the admin
and<00:01:42.720><c> the</c><00:01:42.799><c> proxy</c><00:01:43.280><c> app</c>

00:01:43.830 --> 00:01:43.840 align:start position:0%
and the proxy app
 

00:01:43.840 --> 00:01:46.310 align:start position:0%
and the proxy app
you<00:01:44.000><c> have</c><00:01:44.159><c> to</c><00:01:44.399><c> reset</c><00:01:44.880><c> the</c><00:01:45.119><c> your</c><00:01:45.360><c> ngrok</c><00:01:45.840><c> url</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
you have to reset the your ngrok url
 

00:01:46.320 --> 00:01:48.230 align:start position:0%
you have to reset the your ngrok url
like<00:01:46.560><c> a</c><00:01:46.640><c> million</c><00:01:47.040><c> times</c><00:01:47.360><c> it's</c><00:01:47.520><c> just</c><00:01:47.680><c> not</c><00:01:47.920><c> worth</c>

00:01:48.230 --> 00:01:48.240 align:start position:0%
like a million times it's just not worth
 

00:01:48.240 --> 00:01:49.270 align:start position:0%
like a million times it's just not worth
my<00:01:48.479><c> time</c>

00:01:49.270 --> 00:01:49.280 align:start position:0%
my time
 

00:01:49.280 --> 00:01:50.789 align:start position:0%
my time
let's<00:01:49.439><c> see</c><00:01:49.600><c> if</c><00:01:49.759><c> let's</c><00:01:49.920><c> see</c><00:01:50.079><c> if</c><00:01:50.159><c> ambra</c><00:01:50.560><c> can</c>

00:01:50.789 --> 00:01:50.799 align:start position:0%
let's see if let's see if ambra can
 

00:01:50.799 --> 00:01:54.789 align:start position:0%
let's see if let's see if ambra can
actually<00:01:51.200><c> solve</c><00:01:51.520><c> the</c><00:01:51.680><c> issue</c><00:01:52.000><c> for</c><00:01:52.840><c> us</c>

00:01:54.789 --> 00:01:54.799 align:start position:0%
actually solve the issue for us
 

00:01:54.799 --> 00:01:56.630 align:start position:0%
actually solve the issue for us
so<00:01:54.960><c> i'm</c><00:01:55.040><c> going</c><00:01:55.200><c> to</c><00:01:55.280><c> go</c><00:01:55.360><c> ahead</c><00:01:55.680><c> into</c><00:01:55.920><c> mgrak</c><00:01:56.399><c> over</c>

00:01:56.630 --> 00:01:56.640 align:start position:0%
so i'm going to go ahead into mgrak over
 

00:01:56.640 --> 00:01:58.389 align:start position:0%
so i'm going to go ahead into mgrak over
here<00:01:56.960><c> ah</c><00:01:57.119><c> wait</c><00:01:57.360><c> a</c><00:01:57.439><c> second</c><00:01:57.680><c> did</c><00:01:57.920><c> i</c><00:01:58.000><c> even</c>

00:01:58.389 --> 00:01:58.399 align:start position:0%
here ah wait a second did i even
 

00:01:58.399 --> 00:02:00.550 align:start position:0%
here ah wait a second did i even
connect<00:01:58.719><c> my</c><00:01:58.960><c> account</c><00:01:59.520><c> maybe</c><00:01:59.759><c> i</c><00:01:59.920><c> didn't</c><00:02:00.320><c> let's</c>

00:02:00.550 --> 00:02:00.560 align:start position:0%
connect my account maybe i didn't let's
 

00:02:00.560 --> 00:02:02.310 align:start position:0%
connect my account maybe i didn't let's
see<00:02:00.719><c> what</c><00:02:00.960><c> engraft</c><00:02:01.439><c> tells</c><00:02:01.759><c> me</c>

00:02:02.310 --> 00:02:02.320 align:start position:0%
see what engraft tells me
 

00:02:02.320 --> 00:02:04.550 align:start position:0%
see what engraft tells me
your<00:02:02.560><c> account</c><00:02:03.040><c> alicia</c><00:02:03.439><c> kramer</c><00:02:04.000><c> is</c><00:02:04.079><c> limited</c><00:02:04.399><c> to</c>

00:02:04.550 --> 00:02:04.560 align:start position:0%
your account alicia kramer is limited to
 

00:02:04.560 --> 00:02:06.630 align:start position:0%
your account alicia kramer is limited to
one<00:02:04.799><c> simultaneous</c><00:02:05.600><c> oh</c><00:02:05.759><c> it</c><00:02:05.840><c> looks</c><00:02:06.079><c> like</c><00:02:06.240><c> i</c><00:02:06.399><c> did</c>

00:02:06.630 --> 00:02:06.640 align:start position:0%
one simultaneous oh it looks like i did
 

00:02:06.640 --> 00:02:08.790 align:start position:0%
one simultaneous oh it looks like i did
actually<00:02:07.200><c> connect</c><00:02:07.439><c> my</c><00:02:07.680><c> account</c>

00:02:08.790 --> 00:02:08.800 align:start position:0%
actually connect my account
 

00:02:08.800 --> 00:02:11.830 align:start position:0%
actually connect my account
uh<00:02:09.119><c> that</c><00:02:09.440><c> they</c><00:02:09.599><c> give</c><00:02:09.840><c> you</c><00:02:10.000><c> the</c><00:02:10.239><c> um</c><00:02:11.280><c> the</c><00:02:11.440><c> command</c>

00:02:11.830 --> 00:02:11.840 align:start position:0%
uh that they give you the um the command
 

00:02:11.840 --> 00:02:13.190 align:start position:0%
uh that they give you the um the command
over<00:02:12.080><c> there</c><00:02:12.319><c> as</c><00:02:12.400><c> well</c>

00:02:13.190 --> 00:02:13.200 align:start position:0%
over there as well
 

00:02:13.200 --> 00:02:15.430 align:start position:0%
over there as well
um<00:02:13.680><c> let's</c><00:02:13.920><c> see</c><00:02:14.239><c> all</c><00:02:14.319><c> right</c><00:02:14.480><c> so</c><00:02:14.640><c> i</c><00:02:14.800><c> do</c><00:02:14.959><c> actually</c>

00:02:15.430 --> 00:02:15.440 align:start position:0%
um let's see all right so i do actually
 

00:02:15.440 --> 00:02:17.270 align:start position:0%
um let's see all right so i do actually
have<00:02:15.599><c> to</c><00:02:15.760><c> go</c><00:02:15.920><c> into</c>

00:02:17.270 --> 00:02:17.280 align:start position:0%
have to go into
 

00:02:17.280 --> 00:02:22.150 align:start position:0%
have to go into
into<00:02:17.920><c> uh</c><00:02:18.959><c> into</c><00:02:19.200><c> the</c><00:02:20.840><c> status</c>

00:02:22.150 --> 00:02:22.160 align:start position:0%
into uh into the status
 

00:02:22.160 --> 00:02:26.390 align:start position:0%
into uh into the status
let's<00:02:22.400><c> see</c><00:02:22.879><c> billing</c><00:02:23.680><c> okay</c><00:02:24.239><c> upgrade</c><00:02:24.800><c> plan</c>

00:02:26.390 --> 00:02:26.400 align:start position:0%
let's see billing okay upgrade plan
 

00:02:26.400 --> 00:02:29.589 align:start position:0%
let's see billing okay upgrade plan
yeah<00:02:27.680><c> uh</c><00:02:27.920><c> five</c><00:02:28.239><c> dollars</c><00:02:28.640><c> a</c><00:02:28.720><c> month</c><00:02:29.120><c> okay</c><00:02:29.440><c> good</c>

00:02:29.589 --> 00:02:29.599 align:start position:0%
yeah uh five dollars a month okay good
 

00:02:29.599 --> 00:02:30.070 align:start position:0%
yeah uh five dollars a month okay good
enough

00:02:30.070 --> 00:02:30.080 align:start position:0%
enough
 

00:02:30.080 --> 00:02:33.430 align:start position:0%
enough
wait<00:02:30.480><c> basic</c><00:02:31.680><c> yearly</c><00:02:32.080><c> billing</c><00:02:32.800><c> five</c><00:02:33.040><c> dollars</c>

00:02:33.430 --> 00:02:33.440 align:start position:0%
wait basic yearly billing five dollars
 

00:02:33.440 --> 00:02:36.390 align:start position:0%
wait basic yearly billing five dollars
per<00:02:34.840><c> month</c>

00:02:36.390 --> 00:02:36.400 align:start position:0%
per month
 

00:02:36.400 --> 00:02:40.309 align:start position:0%
per month
let's<00:02:36.640><c> see</c><00:02:37.920><c> 60</c><00:02:38.720><c> build</c><00:02:39.040><c> yearly</c>

00:02:40.309 --> 00:02:40.319 align:start position:0%
let's see 60 build yearly
 

00:02:40.319 --> 00:02:43.110 align:start position:0%
let's see 60 build yearly
per<00:02:40.560><c> user</c><00:02:41.040><c> yearly</c><00:02:41.440><c> billing</c><00:02:42.560><c> well</c><00:02:42.800><c> i</c><00:02:42.879><c> don't</c>

00:02:43.110 --> 00:02:43.120 align:start position:0%
per user yearly billing well i don't
 

00:02:43.120 --> 00:02:45.050 align:start position:0%
per user yearly billing well i don't
want<00:02:43.280><c> it</c><00:02:43.519><c> i</c><00:02:43.599><c> don't</c><00:02:43.760><c> want</c><00:02:43.920><c> a</c><00:02:44.000><c> yearly</c><00:02:44.400><c> plan</c>

00:02:45.050 --> 00:02:45.060 align:start position:0%
want it i don't want a yearly plan
 

00:02:45.060 --> 00:02:46.309 align:start position:0%
want it i don't want a yearly plan
[Music]

00:02:46.309 --> 00:02:46.319 align:start position:0%
[Music]
 

00:02:46.319 --> 00:02:50.150 align:start position:0%
[Music]
yearly<00:02:46.720><c> billing</c><00:02:47.120><c> oh</c><00:02:48.840><c> crap</c>

00:02:50.150 --> 00:02:50.160 align:start position:0%
yearly billing oh crap
 

00:02:50.160 --> 00:02:52.949 align:start position:0%
yearly billing oh crap
they<00:02:50.319><c> have</c><00:02:50.560><c> the</c><00:02:50.640><c> most</c><00:02:51.040><c> confusing</c><00:02:51.760><c> ux</c><00:02:52.480><c> ever</c>

00:02:52.949 --> 00:02:52.959 align:start position:0%
they have the most confusing ux ever
 

00:02:52.959 --> 00:02:54.630 align:start position:0%
they have the most confusing ux ever
okay<00:02:53.280><c> interesting</c>

00:02:54.630 --> 00:02:54.640 align:start position:0%
okay interesting
 

00:02:54.640 --> 00:02:56.790 align:start position:0%
okay interesting
ah<00:02:55.040><c> basic</c><00:02:55.519><c> is</c><00:02:55.680><c> only</c><00:02:55.920><c> gives</c><00:02:56.160><c> you</c><00:02:56.319><c> yearly</c>

00:02:56.790 --> 00:02:56.800 align:start position:0%
ah basic is only gives you yearly
 

00:02:56.800 --> 00:02:58.309 align:start position:0%
ah basic is only gives you yearly
billing<00:02:57.360><c> okay</c>

00:02:58.309 --> 00:02:58.319 align:start position:0%
billing okay
 

00:02:58.319 --> 00:03:00.790 align:start position:0%
billing okay
look<00:02:58.560><c> at</c><00:02:58.640><c> what</c><00:02:58.800><c> they</c><00:02:58.959><c> do</c><00:02:59.120><c> there</c><00:02:59.360><c> talk</c><00:02:59.599><c> about</c>

00:03:00.790 --> 00:03:00.800 align:start position:0%
look at what they do there talk about
 

00:03:00.800 --> 00:03:02.229 align:start position:0%
look at what they do there talk about
what's<00:03:01.040><c> this</c><00:03:01.120><c> called</c><00:03:01.360><c> dark</c><00:03:01.680><c> patterns</c><00:03:02.159><c> or</c>

00:03:02.229 --> 00:03:02.239 align:start position:0%
what's this called dark patterns or
 

00:03:02.239 --> 00:03:03.110 align:start position:0%
what's this called dark patterns or
something

00:03:03.110 --> 00:03:03.120 align:start position:0%
something
 

00:03:03.120 --> 00:03:06.390 align:start position:0%
something
i<00:03:03.280><c> wanted</c><00:03:04.239><c> right</c><00:03:05.280><c> for</c><00:03:05.519><c> the</c><00:03:05.760><c> five</c><00:03:06.000><c> dollars</c><00:03:06.319><c> a</c>

00:03:06.390 --> 00:03:06.400 align:start position:0%
i wanted right for the five dollars a
 

00:03:06.400 --> 00:03:06.869 align:start position:0%
i wanted right for the five dollars a
month

00:03:06.869 --> 00:03:06.879 align:start position:0%
month
 

00:03:06.879 --> 00:03:08.390 align:start position:0%
month
obviously<00:03:07.360><c> everybody's</c><00:03:07.760><c> gonna</c><00:03:08.000><c> click</c><00:03:08.239><c> on</c>

00:03:08.390 --> 00:03:08.400 align:start position:0%
obviously everybody's gonna click on
 

00:03:08.400 --> 00:03:10.470 align:start position:0%
obviously everybody's gonna click on
that<00:03:09.120><c> and</c><00:03:09.280><c> then</c><00:03:09.519><c> they</c><00:03:09.680><c> say</c><00:03:09.920><c> oh</c><00:03:10.080><c> you</c><00:03:10.239><c> only</c><00:03:10.400><c> have</c>

00:03:10.470 --> 00:03:10.480 align:start position:0%
that and then they say oh you only have
 

00:03:10.480 --> 00:03:11.670 align:start position:0%
that and then they say oh you only have
the<00:03:10.560><c> yearly</c><00:03:10.879><c> billing</c><00:03:11.200><c> option</c>

00:03:11.670 --> 00:03:11.680 align:start position:0%
the yearly billing option
 

00:03:11.680 --> 00:03:13.830 align:start position:0%
the yearly billing option
for<00:03:11.840><c> the</c><00:03:12.000><c> pro</c><00:03:12.879><c> all</c><00:03:13.040><c> of</c><00:03:13.120><c> a</c><00:03:13.200><c> sudden</c><00:03:13.440><c> i</c><00:03:13.519><c> have</c><00:03:13.680><c> my</c>

00:03:13.830 --> 00:03:13.840 align:start position:0%
for the pro all of a sudden i have my
 

00:03:13.840 --> 00:03:15.270 align:start position:0%
for the pro all of a sudden i have my
monthly<00:03:14.239><c> billing</c>

00:03:15.270 --> 00:03:15.280 align:start position:0%
monthly billing
 

00:03:15.280 --> 00:03:17.110 align:start position:0%
monthly billing
all<00:03:15.440><c> right</c><00:03:15.680><c> so</c><00:03:15.840><c> whatever</c><00:03:16.239><c> i</c><00:03:16.400><c> only</c><00:03:16.720><c> willing</c><00:03:17.040><c> to</c>

00:03:17.110 --> 00:03:17.120 align:start position:0%
all right so whatever i only willing to
 

00:03:17.120 --> 00:03:18.550 align:start position:0%
all right so whatever i only willing to
pay<00:03:17.280><c> them</c><00:03:17.519><c> 10</c><00:03:17.680><c> bucks</c><00:03:18.000><c> for</c><00:03:18.159><c> now</c>

00:03:18.550 --> 00:03:18.560 align:start position:0%
pay them 10 bucks for now
 

00:03:18.560 --> 00:03:20.149 align:start position:0%
pay them 10 bucks for now
all<00:03:18.640><c> right</c><00:03:18.879><c> 10</c><00:03:19.120><c> bucks</c><00:03:19.360><c> per</c><00:03:19.519><c> user</c><00:03:19.760><c> per</c><00:03:19.920><c> month</c>

00:03:20.149 --> 00:03:20.159 align:start position:0%
all right 10 bucks per user per month
 

00:03:20.159 --> 00:03:22.630 align:start position:0%
all right 10 bucks per user per month
good<00:03:20.400><c> enough</c><00:03:20.720><c> upgrade</c>

00:03:22.630 --> 00:03:22.640 align:start position:0%
good enough upgrade
 

00:03:22.640 --> 00:03:25.350 align:start position:0%
good enough upgrade
all<00:03:22.800><c> right</c><00:03:23.360><c> thank</c><00:03:23.599><c> you</c><00:03:24.319><c> if</c><00:03:24.560><c> billing</c><00:03:24.879><c> resulted</c>

00:03:25.350 --> 00:03:25.360 align:start position:0%
all right thank you if billing resulted
 

00:03:25.360 --> 00:03:26.789 align:start position:0%
all right thank you if billing resulted
in<00:03:25.440><c> a</c><00:03:25.519><c> charge</c><00:03:25.840><c> to</c><00:03:25.920><c> your</c><00:03:26.080><c> credit</c><00:03:26.319><c> card</c><00:03:26.480><c> you</c><00:03:26.640><c> will</c>

00:03:26.789 --> 00:03:26.799 align:start position:0%
in a charge to your credit card you will
 

00:03:26.799 --> 00:03:27.830 align:start position:0%
in a charge to your credit card you will
receive<00:03:27.120><c> an</c><00:03:27.280><c> email</c>

00:03:27.830 --> 00:03:27.840 align:start position:0%
receive an email
 

00:03:27.840 --> 00:03:30.869 align:start position:0%
receive an email
all<00:03:28.000><c> right</c><00:03:28.319><c> perfect</c><00:03:29.120><c> let's</c><00:03:29.360><c> go</c><00:03:29.599><c> back</c><00:03:29.840><c> into</c>

00:03:30.869 --> 00:03:30.879 align:start position:0%
all right perfect let's go back into
 

00:03:30.879 --> 00:03:35.350 align:start position:0%
all right perfect let's go back into
into<00:03:31.519><c> uh</c><00:03:33.680><c> into</c><00:03:33.920><c> here</c>

00:03:35.350 --> 00:03:35.360 align:start position:0%
into uh into here
 

00:03:35.360 --> 00:03:37.430 align:start position:0%
into uh into here
and<00:03:35.599><c> run</c><00:03:35.840><c> it</c><00:03:36.000><c> again</c><00:03:36.480><c> beautiful</c><00:03:36.959><c> this</c><00:03:37.120><c> time</c><00:03:37.360><c> it</c>

00:03:37.430 --> 00:03:37.440 align:start position:0%
and run it again beautiful this time it
 

00:03:37.440 --> 00:03:38.390 align:start position:0%
and run it again beautiful this time it
does<00:03:37.680><c> work</c><00:03:37.920><c> so</c><00:03:38.080><c> they</c>

00:03:38.390 --> 00:03:38.400 align:start position:0%
does work so they
 

00:03:38.400 --> 00:03:41.030 align:start position:0%
does work so they
they<00:03:38.560><c> have</c><00:03:38.720><c> a</c><00:03:38.799><c> good</c><00:03:38.959><c> database</c><00:03:39.599><c> good</c><00:03:40.560><c> all</c><00:03:40.799><c> right</c>

00:03:41.030 --> 00:03:41.040 align:start position:0%
they have a good database good all right
 

00:03:41.040 --> 00:03:41.589 align:start position:0%
they have a good database good all right
awesome

00:03:41.589 --> 00:03:41.599 align:start position:0%
awesome
 

00:03:41.599 --> 00:03:44.830 align:start position:0%
awesome
so<00:03:42.000><c> uh</c><00:03:42.159><c> fine</c><00:03:42.480><c> we</c><00:03:42.560><c> got</c><00:03:42.799><c> ngrok</c><00:03:43.200><c> running</c><00:03:43.519><c> on</c>

00:03:44.830 --> 00:03:44.840 align:start position:0%
so uh fine we got ngrok running on
 

00:03:44.840 --> 00:03:45.990 align:start position:0%
so uh fine we got ngrok running on
onport3000

00:03:45.990 --> 00:03:46.000 align:start position:0%
onport3000
 

00:03:46.000 --> 00:03:49.190 align:start position:0%
onport3000
uh<00:03:46.400><c> sorry</c><00:03:46.720><c> on</c><00:03:46.959><c> port</c><00:03:47.519><c> uh</c><00:03:47.760><c> quadruple</c><00:03:48.480><c> 7</c><00:03:48.879><c> for</c><00:03:49.040><c> the</c>

00:03:49.190 --> 00:03:49.200 align:start position:0%
uh sorry on port uh quadruple 7 for the
 

00:03:49.200 --> 00:03:50.309 align:start position:0%
uh sorry on port uh quadruple 7 for the
proxy<00:03:49.680><c> app</c>

00:03:50.309 --> 00:03:50.319 align:start position:0%
proxy app
 

00:03:50.319 --> 00:03:52.789 align:start position:0%
proxy app
and<00:03:50.720><c> poor</c><00:03:50.959><c> 3000</c><00:03:51.599><c> for</c><00:03:51.760><c> the</c><00:03:51.920><c> admin</c><00:03:52.319><c> app</c><00:03:52.560><c> over</c>

00:03:52.789 --> 00:03:52.799 align:start position:0%
and poor 3000 for the admin app over
 

00:03:52.799 --> 00:03:53.990 align:start position:0%
and poor 3000 for the admin app over
here

00:03:53.990 --> 00:03:54.000 align:start position:0%
here
 

00:03:54.000 --> 00:03:57.110 align:start position:0%
here
let's<00:03:54.239><c> continue</c><00:03:54.799><c> here</c><00:03:55.439><c> all</c><00:03:55.599><c> right</c><00:03:55.840><c> so</c><00:03:56.239><c> um</c>

00:03:57.110 --> 00:03:57.120 align:start position:0%
let's continue here all right so um
 

00:03:57.120 --> 00:04:00.070 align:start position:0%
let's continue here all right so um
right<00:03:57.439><c> we</c><00:03:57.680><c> actually</c><00:03:58.080><c> want</c><00:03:58.239><c> to</c><00:03:58.400><c> open</c><00:03:58.640><c> up</c><00:03:59.680><c> oh</c><00:03:59.920><c> and</c>

00:04:00.070 --> 00:04:00.080 align:start position:0%
right we actually want to open up oh and
 

00:04:00.080 --> 00:04:01.830 align:start position:0%
right we actually want to open up oh and
now<00:04:00.239><c> we</c><00:04:00.480><c> actually</c><00:04:00.879><c> need</c><00:04:01.040><c> to</c>

00:04:01.830 --> 00:04:01.840 align:start position:0%
now we actually need to
 

00:04:01.840 --> 00:04:04.149 align:start position:0%
now we actually need to
run<00:04:02.080><c> these</c><00:04:02.319><c> apps</c><00:04:02.480><c> so</c><00:04:02.720><c> new</c><00:04:03.040><c> tab</c><00:04:03.599><c> i'm</c><00:04:03.760><c> going</c><00:04:03.920><c> to</c>

00:04:04.149 --> 00:04:04.159 align:start position:0%
run these apps so new tab i'm going to
 

00:04:04.159 --> 00:04:05.750 align:start position:0%
run these apps so new tab i'm going to
first<00:04:04.400><c> run</c><00:04:04.640><c> the</c><00:04:04.799><c> proxy</c><00:04:05.280><c> app</c>

00:04:05.750 --> 00:04:05.760 align:start position:0%
first run the proxy app
 

00:04:05.760 --> 00:04:08.789 align:start position:0%
first run the proxy app
cd<00:04:06.319><c> proxy</c><00:04:07.760><c> oh</c><00:04:08.080><c> cd</c>

00:04:08.789 --> 00:04:08.799 align:start position:0%
cd proxy oh cd
 

00:04:08.799 --> 00:04:14.550 align:start position:0%
cd proxy oh cd
desktop<00:04:09.439><c> cd</c><00:04:09.920><c> sk</c><00:04:11.040><c> cd</c><00:04:12.840><c> proxy</c>

00:04:14.550 --> 00:04:14.560 align:start position:0%
desktop cd sk cd proxy
 

00:04:14.560 --> 00:04:19.270 align:start position:0%
desktop cd sk cd proxy
oh<00:04:15.040><c> cd</c><00:04:15.519><c> social</c><00:04:16.000><c> king</c><00:04:16.880><c> cd</c><00:04:17.359><c> proxy</c>

00:04:19.270 --> 00:04:19.280 align:start position:0%
oh cd social king cd proxy
 

00:04:19.280 --> 00:04:23.430 align:start position:0%
oh cd social king cd proxy
okay<00:04:20.079><c> ls</c><00:04:20.799><c> okay</c><00:04:21.120><c> nodemon</c><00:04:21.919><c> start</c>

00:04:23.430 --> 00:04:23.440 align:start position:0%
okay ls okay nodemon start
 

00:04:23.440 --> 00:04:25.430 align:start position:0%
okay ls okay nodemon start
all<00:04:23.600><c> right</c><00:04:23.919><c> so</c><00:04:24.080><c> now</c><00:04:24.240><c> i</c><00:04:24.400><c> have</c><00:04:24.560><c> my</c><00:04:24.720><c> proxy</c><00:04:25.199><c> app</c>

00:04:25.430 --> 00:04:25.440 align:start position:0%
all right so now i have my proxy app
 

00:04:25.440 --> 00:04:26.469 align:start position:0%
all right so now i have my proxy app
running

00:04:26.469 --> 00:04:26.479 align:start position:0%
running
 

00:04:26.479 --> 00:04:29.990 align:start position:0%
running
but<00:04:26.639><c> i</c><00:04:26.800><c> also</c><00:04:27.040><c> want</c><00:04:27.280><c> to</c><00:04:27.440><c> run</c><00:04:28.240><c> my</c><00:04:28.560><c> um</c><00:04:29.280><c> my</c><00:04:29.520><c> admin</c>

00:04:29.990 --> 00:04:30.000 align:start position:0%
but i also want to run my um my admin
 

00:04:30.000 --> 00:04:32.230 align:start position:0%
but i also want to run my um my admin
app<00:04:30.320><c> right</c><00:04:30.560><c> so</c><00:04:30.880><c> the</c><00:04:31.040><c> store</c><00:04:31.360><c> admin</c><00:04:31.759><c> app</c><00:04:32.000><c> is</c><00:04:32.080><c> in</c>

00:04:32.230 --> 00:04:32.240 align:start position:0%
app right so the store admin app is in
 

00:04:32.240 --> 00:04:33.670 align:start position:0%
app right so the store admin app is in
the<00:04:32.479><c> the</c><00:04:32.639><c> shopify</c><00:04:33.199><c> admin</c>

00:04:33.670 --> 00:04:33.680 align:start position:0%
the the shopify admin
 

00:04:33.680 --> 00:04:36.550 align:start position:0%
the the shopify admin
dashboard<00:04:34.800><c> i'm</c><00:04:35.040><c> actually</c><00:04:35.440><c> going</c><00:04:35.600><c> to</c><00:04:35.680><c> go</c><00:04:35.919><c> into</c>

00:04:36.550 --> 00:04:36.560 align:start position:0%
dashboard i'm actually going to go into
 

00:04:36.560 --> 00:04:37.110 align:start position:0%
dashboard i'm actually going to go into
i<00:04:36.639><c> think</c>

00:04:37.110 --> 00:04:37.120 align:start position:0%
i think
 

00:04:37.120 --> 00:04:40.629 align:start position:0%
i think
tribe<00:04:37.680><c> app</c><00:04:39.360><c> all</c><00:04:39.440><c> right</c><00:04:39.680><c> tribe</c><00:04:40.160><c> app</c>

00:04:40.629 --> 00:04:40.639 align:start position:0%
tribe app all right tribe app
 

00:04:40.639 --> 00:04:42.950 align:start position:0%
tribe app all right tribe app
dot<00:04:40.880><c> at</c><00:04:41.680><c> admin</c><00:04:42.240><c> just</c><00:04:42.400><c> to</c><00:04:42.479><c> show</c><00:04:42.639><c> you</c><00:04:42.800><c> guys</c>

00:04:42.950 --> 00:04:42.960 align:start position:0%
dot at admin just to show you guys
 

00:04:42.960 --> 00:04:45.030 align:start position:0%
dot at admin just to show you guys
what's<00:04:43.199><c> being</c><00:04:43.440><c> discussed</c><00:04:43.919><c> over</c><00:04:44.080><c> here</c><00:04:44.320><c> but</c>

00:04:45.030 --> 00:04:45.040 align:start position:0%
what's being discussed over here but
 

00:04:45.040 --> 00:04:49.830 align:start position:0%
what's being discussed over here but
bottom<00:04:45.360><c> line</c><00:04:45.680><c> is</c><00:04:45.840><c> that</c><00:04:46.400><c> you</c><00:04:46.560><c> need</c><00:04:46.800><c> to</c><00:04:47.040><c> have</c><00:04:47.840><c> um</c>

00:04:49.830 --> 00:04:49.840 align:start position:0%
bottom line is that you need to have um
 

00:04:49.840 --> 00:04:53.110 align:start position:0%
bottom line is that you need to have um
all<00:04:50.000><c> right</c><00:04:50.240><c> here</c><00:04:51.199><c> right</c><00:04:51.440><c> if</c><00:04:51.600><c> i</c><00:04:51.680><c> go</c><00:04:52.080><c> into</c><00:04:52.720><c> apps</c>

00:04:53.110 --> 00:04:53.120 align:start position:0%
all right here right if i go into apps
 

00:04:53.120 --> 00:04:54.870 align:start position:0%
all right here right if i go into apps
here

00:04:54.870 --> 00:04:54.880 align:start position:0%
here
 

00:04:54.880 --> 00:04:56.710 align:start position:0%
here
then<00:04:55.040><c> i</c><00:04:55.199><c> should</c><00:04:55.360><c> have</c><00:04:55.520><c> my</c><00:04:55.759><c> social</c><00:04:56.160><c> king</c><00:04:56.400><c> dev</c>

00:04:56.710 --> 00:04:56.720 align:start position:0%
then i should have my social king dev
 

00:04:56.720 --> 00:04:58.310 align:start position:0%
then i should have my social king dev
app<00:04:57.120><c> good</c>

00:04:58.310 --> 00:04:58.320 align:start position:0%
app good
 

00:04:58.320 --> 00:05:00.070 align:start position:0%
app good
and<00:04:58.400><c> when</c><00:04:58.639><c> i</c><00:04:58.720><c> click</c><00:04:59.040><c> it</c><00:04:59.280><c> of</c><00:04:59.440><c> course</c><00:04:59.680><c> it's</c><00:04:59.840><c> not</c>

00:05:00.070 --> 00:05:00.080 align:start position:0%
and when i click it of course it's not
 

00:05:00.080 --> 00:05:01.350 align:start position:0%
and when i click it of course it's not
running<00:05:00.400><c> right</c><00:05:00.720><c> now</c>

00:05:01.350 --> 00:05:01.360 align:start position:0%
running right now
 

00:05:01.360 --> 00:05:02.870 align:start position:0%
running right now
okay<00:05:01.759><c> it's</c><00:05:01.919><c> going</c><00:05:02.000><c> to</c><00:05:02.080><c> give</c><00:05:02.240><c> us</c><00:05:02.400><c> an</c><00:05:02.560><c> error</c>

00:05:02.870 --> 00:05:02.880 align:start position:0%
okay it's going to give us an error
 

00:05:02.880 --> 00:05:05.430 align:start position:0%
okay it's going to give us an error
tunnel<00:05:03.280><c> not</c><00:05:03.600><c> found</c><00:05:04.160><c> okay</c><00:05:04.479><c> perfect</c>

00:05:05.430 --> 00:05:05.440 align:start position:0%
tunnel not found okay perfect
 

00:05:05.440 --> 00:05:07.670 align:start position:0%
tunnel not found okay perfect
so<00:05:05.600><c> let's</c><00:05:05.840><c> go</c><00:05:06.000><c> ahead</c><00:05:06.400><c> and</c><00:05:06.479><c> go</c><00:05:06.720><c> in</c><00:05:06.880><c> here</c><00:05:07.360><c> and</c>

00:05:07.670 --> 00:05:07.680 align:start position:0%
so let's go ahead and go in here and
 

00:05:07.680 --> 00:05:09.430 align:start position:0%
so let's go ahead and go in here and
what<00:05:07.840><c> are</c><00:05:07.919><c> we</c><00:05:08.000><c> going</c><00:05:08.160><c> to</c><00:05:08.240><c> say</c><00:05:08.560><c> here</c>

00:05:09.430 --> 00:05:09.440 align:start position:0%
what are we going to say here
 

00:05:09.440 --> 00:05:12.629 align:start position:0%
what are we going to say here
um<00:05:10.240><c> in</c><00:05:10.479><c> my</c><00:05:10.639><c> proxy</c><00:05:11.120><c> app</c><00:05:11.360><c> i</c><00:05:11.759><c> already</c><00:05:12.080><c> ran</c><00:05:12.400><c> known</c>

00:05:12.629 --> 00:05:12.639 align:start position:0%
um in my proxy app i already ran known
 

00:05:12.639 --> 00:05:14.390 align:start position:0%
um in my proxy app i already ran known
man<00:05:12.880><c> start</c><00:05:13.199><c> now</c><00:05:13.360><c> i</c><00:05:13.520><c> also</c><00:05:13.759><c> need</c><00:05:13.840><c> to</c><00:05:13.919><c> go</c><00:05:14.080><c> into</c><00:05:14.240><c> my</c>

00:05:14.390 --> 00:05:14.400 align:start position:0%
man start now i also need to go into my
 

00:05:14.400 --> 00:05:16.230 align:start position:0%
man start now i also need to go into my
admin<00:05:14.800><c> app</c><00:05:14.880><c> but</c><00:05:15.039><c> my</c><00:05:15.280><c> admin</c><00:05:15.680><c> app</c><00:05:15.840><c> has</c><00:05:16.000><c> two</c>

00:05:16.230 --> 00:05:16.240 align:start position:0%
admin app but my admin app has two
 

00:05:16.240 --> 00:05:18.070 align:start position:0%
admin app but my admin app has two
folders<00:05:16.639><c> the</c><00:05:16.800><c> admin</c><00:05:17.199><c> frontend</c><00:05:17.520><c> and</c><00:05:17.600><c> the</c><00:05:17.759><c> admin</c>

00:05:18.070 --> 00:05:18.080 align:start position:0%
folders the admin frontend and the admin
 

00:05:18.080 --> 00:05:18.950 align:start position:0%
folders the admin frontend and the admin
backend

00:05:18.950 --> 00:05:18.960 align:start position:0%
backend
 

00:05:18.960 --> 00:05:22.950 align:start position:0%
backend
see<00:05:19.199><c> the</c><00:05:19.440><c> admin</c><00:05:20.080><c> front</c><00:05:20.479><c> end</c><00:05:21.840><c> within</c><00:05:22.240><c> my</c><00:05:22.479><c> as</c><00:05:22.720><c> our</c>

00:05:22.950 --> 00:05:22.960 align:start position:0%
see the admin front end within my as our
 

00:05:22.960 --> 00:05:26.390 align:start position:0%
see the admin front end within my as our
admin<00:05:23.360><c> backend</c>

00:05:26.390 --> 00:05:26.400 align:start position:0%
 
 

00:05:26.400 --> 00:05:30.390 align:start position:0%
 
and<00:05:26.800><c> npm</c><00:05:27.360><c> star</c><00:05:28.720><c> and</c><00:05:29.039><c> i</c><00:05:29.199><c> also</c><00:05:29.360><c> need</c><00:05:29.520><c> to</c><00:05:29.680><c> add</c><00:05:30.000><c> cd</c>

00:05:30.390 --> 00:05:30.400 align:start position:0%
and npm star and i also need to add cd
 

00:05:30.400 --> 00:05:32.310 align:start position:0%
and npm star and i also need to add cd
into<00:05:30.639><c> my</c><00:05:30.800><c> admin</c>

00:05:32.310 --> 00:05:32.320 align:start position:0%
into my admin
 

00:05:32.320 --> 00:05:35.430 align:start position:0%
into my admin
admin<00:05:32.880><c> front</c><00:05:33.120><c> end</c><00:05:34.080><c> okay</c>

00:05:35.430 --> 00:05:35.440 align:start position:0%
admin front end okay
 

00:05:35.440 --> 00:05:37.510 align:start position:0%
admin front end okay
the<00:05:35.600><c> admin</c><00:05:35.919><c> frontend</c><00:05:36.320><c> is</c><00:05:36.400><c> the</c><00:05:36.479><c> next</c><00:05:36.720><c> js</c><00:05:37.280><c> app</c>

00:05:37.510 --> 00:05:37.520 align:start position:0%
the admin frontend is the next js app
 

00:05:37.520 --> 00:05:38.710 align:start position:0%
the admin frontend is the next js app
and<00:05:37.840><c> npm</c>

00:05:38.710 --> 00:05:38.720 align:start position:0%
and npm
 

00:05:38.720 --> 00:05:42.230 align:start position:0%
and npm
run<00:05:39.759><c> dev</c><00:05:41.199><c> okay</c>

00:05:42.230 --> 00:05:42.240 align:start position:0%
run dev okay
 

00:05:42.240 --> 00:05:46.870 align:start position:0%
run dev okay
now<00:05:42.639><c> everything</c><00:05:43.120><c> should</c><00:05:43.360><c> be</c><00:05:43.600><c> working</c><00:05:44.479><c> but</c><00:05:45.120><c> um</c>

00:05:46.870 --> 00:05:46.880 align:start position:0%
now everything should be working but um
 

00:05:46.880 --> 00:05:48.469 align:start position:0%
now everything should be working but um
there's<00:05:47.199><c> one</c><00:05:47.440><c> other</c><00:05:47.680><c> thing</c><00:05:47.840><c> that</c><00:05:48.000><c> needs</c><00:05:48.160><c> to</c><00:05:48.320><c> be</c>

00:05:48.469 --> 00:05:48.479 align:start position:0%
there's one other thing that needs to be
 

00:05:48.479 --> 00:05:51.510 align:start position:0%
there's one other thing that needs to be
done<00:05:48.720><c> i</c><00:05:48.800><c> need</c><00:05:48.960><c> to</c><00:05:49.039><c> take</c><00:05:49.280><c> my</c><00:05:49.520><c> ngrok</c><00:05:50.360><c> url</c>

00:05:51.510 --> 00:05:51.520 align:start position:0%
done i need to take my ngrok url
 

00:05:51.520 --> 00:05:55.110 align:start position:0%
done i need to take my ngrok url
um<00:05:52.320><c> let's</c><00:05:52.560><c> see</c><00:05:52.800><c> for</c><00:05:52.960><c> 3000</c><00:05:54.080><c> over</c><00:05:54.320><c> here</c>

00:05:55.110 --> 00:05:55.120 align:start position:0%
um let's see for 3000 over here
 

00:05:55.120 --> 00:05:56.790 align:start position:0%
um let's see for 3000 over here
and<00:05:55.280><c> i</c><00:05:55.440><c> need</c><00:05:55.520><c> to</c><00:05:55.600><c> go</c><00:05:55.759><c> into</c><00:05:56.000><c> my</c><00:05:56.240><c> partners</c>

00:05:56.790 --> 00:05:56.800 align:start position:0%
and i need to go into my partners
 

00:05:56.800 --> 00:05:58.950 align:start position:0%
and i need to go into my partners
account<00:05:57.120><c> here</c><00:05:57.360><c> and</c><00:05:57.759><c> on</c><00:05:57.919><c> linux</c><00:05:58.240><c> you</c><00:05:58.319><c> press</c><00:05:58.560><c> ctrl</c>

00:05:58.950 --> 00:05:58.960 align:start position:0%
account here and on linux you press ctrl
 

00:05:58.960 --> 00:06:00.309 align:start position:0%
account here and on linux you press ctrl
shift<00:05:59.360><c> c</c>

00:06:00.309 --> 00:06:00.319 align:start position:0%
shift c
 

00:06:00.319 --> 00:06:02.230 align:start position:0%
shift c
that<00:06:00.479><c> way</c><00:06:00.639><c> you</c><00:06:00.800><c> don't</c><00:06:01.039><c> x</c><00:06:01.280><c> out</c><00:06:01.440><c> of</c><00:06:01.520><c> your</c><00:06:01.759><c> ngra</c>

00:06:02.230 --> 00:06:02.240 align:start position:0%
that way you don't x out of your ngra
 

00:06:02.240 --> 00:06:04.150 align:start position:0%
that way you don't x out of your ngra
thing<00:06:03.120><c> god</c><00:06:03.360><c> forbid</c>

00:06:04.150 --> 00:06:04.160 align:start position:0%
thing god forbid
 

00:06:04.160 --> 00:06:07.590 align:start position:0%
thing god forbid
okay<00:06:04.840><c> partners</c>

00:06:07.590 --> 00:06:07.600 align:start position:0%
okay partners
 

00:06:07.600 --> 00:06:09.590 align:start position:0%
okay partners
i<00:06:07.759><c> go</c><00:06:08.000><c> to</c><00:06:08.160><c> log</c><00:06:08.479><c> in</c><00:06:08.720><c> so</c><00:06:08.880><c> you</c><00:06:08.960><c> can</c><00:06:09.120><c> see</c><00:06:09.280><c> there's</c><00:06:09.440><c> a</c>

00:06:09.590 --> 00:06:09.600 align:start position:0%
i go to log in so you can see there's a
 

00:06:09.600 --> 00:06:10.790 align:start position:0%
i go to log in so you can see there's a
lot<00:06:09.759><c> of</c><00:06:09.840><c> different</c><00:06:10.080><c> terminals</c>

00:06:10.790 --> 00:06:10.800 align:start position:0%
lot of different terminals
 

00:06:10.800 --> 00:06:13.270 align:start position:0%
lot of different terminals
just<00:06:10.960><c> for</c><00:06:11.120><c> the</c><00:06:11.360><c> setup</c><00:06:11.759><c> over</c><00:06:12.000><c> here</c><00:06:12.880><c> all</c><00:06:13.039><c> right</c>

00:06:13.270 --> 00:06:13.280 align:start position:0%
just for the setup over here all right
 

00:06:13.280 --> 00:06:14.070 align:start position:0%
just for the setup over here all right
social<00:06:13.680><c> king</c>

00:06:14.070 --> 00:06:14.080 align:start position:0%
social king
 

00:06:14.080 --> 00:06:17.430 align:start position:0%
social king
dev<00:06:14.400><c> here</c><00:06:14.880><c> right</c><00:06:16.960><c> and</c>

00:06:17.430 --> 00:06:17.440 align:start position:0%
dev here right and
 

00:06:17.440 --> 00:06:21.270 align:start position:0%
dev here right and
app<00:06:18.840><c> setup</c><00:06:20.319><c> all</c><00:06:20.479><c> right</c>

00:06:21.270 --> 00:06:21.280 align:start position:0%
app setup all right
 

00:06:21.280 --> 00:06:25.990 align:start position:0%
app setup all right
and<00:06:21.680><c> here's</c><00:06:22.000><c> my</c><00:06:22.160><c> url</c><00:06:22.720><c> over</c><00:06:22.960><c> here</c>

00:06:25.990 --> 00:06:26.000 align:start position:0%
 
 

00:06:26.000 --> 00:06:27.990 align:start position:0%
 
all<00:06:26.160><c> right</c><00:06:26.479><c> cool</c><00:06:26.880><c> here's</c><00:06:27.120><c> my</c><00:06:27.360><c> installation</c>

00:06:27.990 --> 00:06:28.000 align:start position:0%
all right cool here's my installation
 

00:06:28.000 --> 00:06:29.749 align:start position:0%
all right cool here's my installation
url<00:06:28.560><c> in</c><00:06:28.720><c> case</c><00:06:28.960><c> i</c><00:06:29.120><c> need</c><00:06:29.360><c> that</c>

00:06:29.749 --> 00:06:29.759 align:start position:0%
url in case i need that
 

00:06:29.759 --> 00:06:34.070 align:start position:0%
url in case i need that
save<00:06:31.520><c> and</c><00:06:31.759><c> now</c><00:06:32.000><c> i</c><00:06:32.080><c> have</c><00:06:32.240><c> my</c><00:06:32.479><c> admin</c><00:06:32.960><c> up</c><00:06:33.120><c> set</c><00:06:33.360><c> up</c>

00:06:34.070 --> 00:06:34.080 align:start position:0%
save and now i have my admin up set up
 

00:06:34.080 --> 00:06:37.189 align:start position:0%
save and now i have my admin up set up
okay<00:06:35.120><c> and</c><00:06:35.680><c> since</c><00:06:35.919><c> we're</c><00:06:36.160><c> already</c><00:06:36.560><c> at</c><00:06:36.720><c> it</c><00:06:36.960><c> let's</c>

00:06:37.189 --> 00:06:37.199 align:start position:0%
okay and since we're already at it let's
 

00:06:37.199 --> 00:06:37.670 align:start position:0%
okay and since we're already at it let's
just

00:06:37.670 --> 00:06:37.680 align:start position:0%
just
 

00:06:37.680 --> 00:06:40.950 align:start position:0%
just
also<00:06:38.080><c> set</c><00:06:38.400><c> up</c><00:06:38.720><c> our</c><00:06:39.199><c> proxy</c><00:06:39.680><c> app</c><00:06:40.000><c> as</c><00:06:40.160><c> well</c>

00:06:40.950 --> 00:06:40.960 align:start position:0%
also set up our proxy app as well
 

00:06:40.960 --> 00:06:42.629 align:start position:0%
also set up our proxy app as well
just<00:06:41.199><c> so</c><00:06:41.360><c> that</c><00:06:41.520><c> you</c><00:06:41.759><c> understand</c><00:06:42.160><c> the</c><00:06:42.319><c> first</c>

00:06:42.629 --> 00:06:42.639 align:start position:0%
just so that you understand the first
 

00:06:42.639 --> 00:06:45.189 align:start position:0%
just so that you understand the first
the<00:06:42.800><c> full</c><00:06:43.120><c> flow</c><00:06:43.440><c> of</c><00:06:43.600><c> setting</c><00:06:44.000><c> up</c>

00:06:45.189 --> 00:06:45.199 align:start position:0%
the full flow of setting up
 

00:06:45.199 --> 00:06:48.550 align:start position:0%
the full flow of setting up
over<00:06:45.520><c> here</c><00:06:46.000><c> okay</c><00:06:47.039><c> command</c><00:06:47.520><c> shift</c>

00:06:48.550 --> 00:06:48.560 align:start position:0%
over here okay command shift
 

00:06:48.560 --> 00:06:52.230 align:start position:0%
over here okay command shift
c<00:06:49.120><c> okay</c><00:06:50.000><c> you</c><00:06:50.160><c> should</c><00:06:50.400><c> see</c><00:06:51.280><c> all</c><00:06:51.440><c> right</c><00:06:51.680><c> cool</c>

00:06:52.230 --> 00:06:52.240 align:start position:0%
c okay you should see all right cool
 

00:06:52.240 --> 00:06:54.469 align:start position:0%
c okay you should see all right cool
now<00:06:52.479><c> i've</c><00:06:52.639><c> got</c><00:06:52.960><c> my</c><00:06:53.199><c> bath</c><00:06:53.599><c> i've</c><00:06:53.759><c> got</c><00:06:53.919><c> my</c><00:06:54.080><c> online</c>

00:06:54.469 --> 00:06:54.479 align:start position:0%
now i've got my bath i've got my online
 

00:06:54.479 --> 00:06:56.309 align:start position:0%
now i've got my bath i've got my online
store

00:06:56.309 --> 00:06:56.319 align:start position:0%
store
 

00:06:56.319 --> 00:06:59.670 align:start position:0%
store
i<00:06:56.479><c> go</c><00:06:56.639><c> into</c><00:06:56.880><c> manage</c><00:06:59.199><c> and</c>

00:06:59.670 --> 00:06:59.680 align:start position:0%
i go into manage and
 

00:06:59.680 --> 00:07:03.270 align:start position:0%
i go into manage and
finally<00:07:00.479><c> i</c><00:07:00.639><c> take</c><00:07:01.120><c> this</c><00:07:01.440><c> url</c><00:07:02.319><c> and</c><00:07:02.639><c> i</c><00:07:02.800><c> paste</c><00:07:03.120><c> it</c>

00:07:03.270 --> 00:07:03.280 align:start position:0%
finally i take this url and i paste it
 

00:07:03.280 --> 00:07:05.589 align:start position:0%
finally i take this url and i paste it
in<00:07:03.440><c> there</c><00:07:03.759><c> and</c><00:07:04.000><c> we</c><00:07:04.160><c> are</c><00:07:04.319><c> done</c>

00:07:05.589 --> 00:07:05.599 align:start position:0%
in there and we are done
 

00:07:05.599 --> 00:07:09.029 align:start position:0%
in there and we are done
all<00:07:05.759><c> right</c><00:07:06.000><c> guys</c><00:07:06.319><c> cool</c><00:07:06.800><c> so</c><00:07:07.520><c> i</c><00:07:07.680><c> press</c><00:07:08.080><c> save</c>

00:07:09.029 --> 00:07:09.039 align:start position:0%
all right guys cool so i press save
 

00:07:09.039 --> 00:07:11.510 align:start position:0%
all right guys cool so i press save
and<00:07:09.199><c> now</c><00:07:09.759><c> i</c><00:07:09.840><c> have</c><00:07:10.080><c> both</c><00:07:10.319><c> of</c><00:07:10.400><c> them</c><00:07:10.639><c> set</c><00:07:10.960><c> up</c><00:07:11.199><c> i</c><00:07:11.280><c> can</c>

00:07:11.510 --> 00:07:11.520 align:start position:0%
and now i have both of them set up i can
 

00:07:11.520 --> 00:07:12.390 align:start position:0%
and now i have both of them set up i can
refresh<00:07:12.000><c> this</c>

00:07:12.390 --> 00:07:12.400 align:start position:0%
refresh this
 

00:07:12.400 --> 00:07:15.110 align:start position:0%
refresh this
hopefully<00:07:12.880><c> it</c><00:07:12.960><c> will</c><00:07:13.120><c> work</c><00:07:14.400><c> all</c><00:07:14.560><c> right</c><00:07:14.880><c> i'm</c>

00:07:15.110 --> 00:07:15.120 align:start position:0%
hopefully it will work all right i'm
 

00:07:15.120 --> 00:07:16.390 align:start position:0%
hopefully it will work all right i'm
gonna<00:07:15.280><c> move</c><00:07:15.440><c> that</c><00:07:15.599><c> to</c><00:07:15.759><c> the</c><00:07:15.919><c> side</c>

00:07:16.390 --> 00:07:16.400 align:start position:0%
gonna move that to the side
 

00:07:16.400 --> 00:07:19.430 align:start position:0%
gonna move that to the side
and<00:07:16.560><c> i'm</c><00:07:16.720><c> also</c><00:07:17.039><c> gonna</c><00:07:17.440><c> check</c><00:07:17.840><c> if</c><00:07:18.080><c> i</c><00:07:18.240><c> can</c><00:07:18.560><c> run</c>

00:07:19.430 --> 00:07:19.440 align:start position:0%
and i'm also gonna check if i can run
 

00:07:19.440 --> 00:07:22.870 align:start position:0%
and i'm also gonna check if i can run
my<00:07:20.160><c> um</c><00:07:21.680><c> tribe</c><00:07:22.319><c> app</c>

00:07:22.870 --> 00:07:22.880 align:start position:0%
my um tribe app
 

00:07:22.880 --> 00:07:24.870 align:start position:0%
my um tribe app
dot<00:07:23.199><c> slash</c><00:07:23.599><c> community</c><00:07:24.000><c> slash</c><00:07:24.319><c> connect</c><00:07:24.720><c> this</c>

00:07:24.870 --> 00:07:24.880 align:start position:0%
dot slash community slash connect this
 

00:07:24.880 --> 00:07:27.510 align:start position:0%
dot slash community slash connect this
is<00:07:24.960><c> the</c><00:07:25.120><c> proxy</c><00:07:25.520><c> app</c><00:07:25.759><c> over</c><00:07:26.000><c> here</c>

00:07:27.510 --> 00:07:27.520 align:start position:0%
is the proxy app over here
 

00:07:27.520 --> 00:07:29.909 align:start position:0%
is the proxy app over here
all<00:07:27.759><c> right</c><00:07:28.319><c> okay</c><00:07:28.639><c> cool</c><00:07:28.880><c> the</c><00:07:29.039><c> proxy</c><00:07:29.520><c> app</c><00:07:29.759><c> is</c>

00:07:29.909 --> 00:07:29.919 align:start position:0%
all right okay cool the proxy app is
 

00:07:29.919 --> 00:07:30.550 align:start position:0%
all right okay cool the proxy app is
working

00:07:30.550 --> 00:07:30.560 align:start position:0%
working
 

00:07:30.560 --> 00:07:32.790 align:start position:0%
working
and<00:07:31.199><c> okay</c><00:07:31.520><c> on</c><00:07:31.680><c> the</c><00:07:31.759><c> left</c><00:07:32.000><c> side</c><00:07:32.319><c> we</c><00:07:32.479><c> should</c><00:07:32.639><c> be</c>

00:07:32.790 --> 00:07:32.800 align:start position:0%
and okay on the left side we should be
 

00:07:32.800 --> 00:07:34.710 align:start position:0%
and okay on the left side we should be
able<00:07:33.039><c> to</c><00:07:33.199><c> also</c><00:07:33.599><c> see</c>

00:07:34.710 --> 00:07:34.720 align:start position:0%
able to also see
 

00:07:34.720 --> 00:07:37.830 align:start position:0%
able to also see
uh<00:07:35.039><c> let's</c><00:07:35.280><c> see</c><00:07:36.080><c> the</c><00:07:36.479><c> admin</c><00:07:36.880><c> app</c><00:07:37.199><c> running</c>

00:07:37.830 --> 00:07:37.840 align:start position:0%
uh let's see the admin app running
 

00:07:37.840 --> 00:07:41.029 align:start position:0%
uh let's see the admin app running
this<00:07:38.080><c> is</c><00:07:38.160><c> the</c><00:07:38.319><c> admin</c><00:07:38.800><c> app</c><00:07:39.039><c> logs</c><00:07:39.360><c> over</c><00:07:39.599><c> here</c>

00:07:41.029 --> 00:07:41.039 align:start position:0%
this is the admin app logs over here
 

00:07:41.039 --> 00:07:43.270 align:start position:0%
this is the admin app logs over here
running<00:07:41.360><c> on</c><00:07:41.520><c> port</c><00:07:41.759><c> 3000</c><00:07:42.639><c> let's</c><00:07:42.800><c> just</c><00:07:42.960><c> double</c>

00:07:43.270 --> 00:07:43.280 align:start position:0%
running on port 3000 let's just double
 

00:07:43.280 --> 00:07:46.230 align:start position:0%
running on port 3000 let's just double
check<00:07:43.520><c> that</c><00:07:43.680><c> everything</c><00:07:44.160><c> works</c>

00:07:46.230 --> 00:07:46.240 align:start position:0%
check that everything works
 

00:07:46.240 --> 00:07:48.790 align:start position:0%
check that everything works
and<00:07:46.400><c> if</c><00:07:46.560><c> everything</c><00:07:46.960><c> works</c><00:07:47.360><c> we'll</c><00:07:47.599><c> just</c><00:07:48.400><c> uh</c>

00:07:48.790 --> 00:07:48.800 align:start position:0%
and if everything works we'll just uh
 

00:07:48.800 --> 00:07:54.230 align:start position:0%
and if everything works we'll just uh
pause<00:07:49.120><c> the</c><00:07:49.280><c> video</c><00:07:49.680><c> for</c><00:07:49.919><c> now</c>

00:07:54.230 --> 00:07:54.240 align:start position:0%
 
 

00:07:54.240 --> 00:07:56.790 align:start position:0%
 
just<00:07:54.400><c> to</c><00:07:54.560><c> verify</c><00:07:55.520><c> okay</c><00:07:55.840><c> cool</c><00:07:56.160><c> everything</c>

00:07:56.790 --> 00:07:56.800 align:start position:0%
just to verify okay cool everything
 

00:07:56.800 --> 00:07:59.110 align:start position:0%
just to verify okay cool everything
everything<00:07:57.199><c> works</c><00:07:57.520><c> oh</c><00:07:57.840><c> wait</c><00:07:58.160><c> a</c><00:07:58.240><c> second</c>

00:07:59.110 --> 00:07:59.120 align:start position:0%
everything works oh wait a second
 

00:07:59.120 --> 00:08:01.350 align:start position:0%
everything works oh wait a second
it<00:07:59.280><c> redirected</c><00:08:00.080><c> me</c><00:08:00.240><c> over</c><00:08:00.479><c> here</c><00:08:00.879><c> all</c><00:08:00.960><c> right</c><00:08:01.199><c> it</c>

00:08:01.350 --> 00:08:01.360 align:start position:0%
it redirected me over here all right it
 

00:08:01.360 --> 00:08:03.110 align:start position:0%
it redirected me over here all right it
redirected<00:08:02.080><c> me</c>

00:08:03.110 --> 00:08:03.120 align:start position:0%
redirected me
 

00:08:03.120 --> 00:08:07.270 align:start position:0%
redirected me
to<00:08:03.599><c> the</c><00:08:03.919><c> sign</c><00:08:04.240><c> up</c><00:08:04.400><c> page</c><00:08:04.800><c> over</c><00:08:05.039><c> here</c><00:08:06.000><c> um</c>

00:08:07.270 --> 00:08:07.280 align:start position:0%
to the sign up page over here um
 

00:08:07.280 --> 00:08:09.510 align:start position:0%
to the sign up page over here um
and<00:08:07.520><c> why</c><00:08:07.759><c> did</c><00:08:08.000><c> it</c><00:08:08.080><c> do</c><00:08:08.240><c> that</c><00:08:08.720><c> probably</c><00:08:09.199><c> because</c>

00:08:09.510 --> 00:08:09.520 align:start position:0%
and why did it do that probably because
 

00:08:09.520 --> 00:08:10.550 align:start position:0%
and why did it do that probably because
i<00:08:09.599><c> don't</c><00:08:09.759><c> have</c><00:08:09.919><c> a</c><00:08:10.080><c> charge</c>

00:08:10.550 --> 00:08:10.560 align:start position:0%
i don't have a charge
 

00:08:10.560 --> 00:08:13.909 align:start position:0%
i don't have a charge
a<00:08:10.800><c> charge</c><00:08:11.280><c> id</c><00:08:12.080><c> okay</c><00:08:12.960><c> and</c><00:08:13.280><c> we'll</c><00:08:13.520><c> talk</c><00:08:13.680><c> about</c>

00:08:13.909 --> 00:08:13.919 align:start position:0%
a charge id okay and we'll talk about
 

00:08:13.919 --> 00:08:15.430 align:start position:0%
a charge id okay and we'll talk about
that<00:08:14.080><c> in</c><00:08:14.240><c> another</c><00:08:14.639><c> video</c>

00:08:15.430 --> 00:08:15.440 align:start position:0%
that in another video
 

00:08:15.440 --> 00:08:17.670 align:start position:0%
that in another video
um<00:08:16.080><c> so</c><00:08:16.560><c> if</c><00:08:16.639><c> you</c><00:08:16.720><c> have</c><00:08:16.800><c> any</c><00:08:17.039><c> questions</c><00:08:17.360><c> on</c><00:08:17.520><c> how</c>

00:08:17.670 --> 00:08:17.680 align:start position:0%
um so if you have any questions on how
 

00:08:17.680 --> 00:08:18.629 align:start position:0%
um so if you have any questions on how
to<00:08:17.759><c> set</c><00:08:18.000><c> up</c>

00:08:18.629 --> 00:08:18.639 align:start position:0%
to set up
 

00:08:18.639 --> 00:08:20.629 align:start position:0%
to set up
the<00:08:18.800><c> repo</c><00:08:19.280><c> on</c><00:08:19.360><c> your</c><00:08:19.520><c> local</c><00:08:19.840><c> machine</c><00:08:20.319><c> feel</c><00:08:20.479><c> free</c>

00:08:20.629 --> 00:08:20.639 align:start position:0%
the repo on your local machine feel free
 

00:08:20.639 --> 00:08:21.909 align:start position:0%
the repo on your local machine feel free
to<00:08:20.720><c> reach</c><00:08:21.039><c> out</c><00:08:21.280><c> and</c>

00:08:21.909 --> 00:08:21.919 align:start position:0%
to reach out and
 

00:08:21.919 --> 00:08:25.350 align:start position:0%
to reach out and
in<00:08:22.160><c> the</c><00:08:22.800><c> in</c><00:08:22.960><c> the</c><00:08:23.039><c> next</c><00:08:23.360><c> videos</c><00:08:24.240><c> um</c>

00:08:25.350 --> 00:08:25.360 align:start position:0%
in the in the next videos um
 

00:08:25.360 --> 00:08:27.029 align:start position:0%
in the in the next videos um
we'll<00:08:25.520><c> continue</c><00:08:26.000><c> the</c><00:08:26.160><c> development</c><00:08:26.720><c> all</c><00:08:26.879><c> right</c>

00:08:27.029 --> 00:08:27.039 align:start position:0%
we'll continue the development all right
 

00:08:27.039 --> 00:08:30.560 align:start position:0%
we'll continue the development all right
guys<00:08:27.440><c> i'll</c><00:08:27.599><c> see</c><00:08:27.680><c> you</c><00:08:27.840><c> in</c><00:08:27.919><c> the</c><00:08:28.000><c> next</c><00:08:28.240><c> one</c>

