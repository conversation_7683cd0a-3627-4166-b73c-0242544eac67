WEBVTT
Kind: captions
Language: en

00:00:00.440 --> 00:00:06.269 align:start position:0%
 
[Music]

00:00:06.269 --> 00:00:06.279 align:start position:0%
 
 

00:00:06.279 --> 00:00:08.950 align:start position:0%
 
hello<00:00:06.720><c> I</c><00:00:06.839><c> am</c><00:00:07.080><c> Su</c><00:00:07.640><c> tangan</c><00:00:08.200><c> a</c><00:00:08.280><c> senior</c><00:00:08.639><c> data</c>

00:00:08.950 --> 00:00:08.960 align:start position:0%
hello I am Su tangan a senior data
 

00:00:08.960 --> 00:00:11.870 align:start position:0%
hello I am Su tangan a senior data
scientist<00:00:09.400><c> in</c><00:00:09.599><c> SNP</c><00:00:10.080><c> Global</c><00:00:10.920><c> and</c><00:00:11.120><c> today</c><00:00:11.519><c> I</c><00:00:11.639><c> am</c>

00:00:11.870 --> 00:00:11.880 align:start position:0%
scientist in SNP Global and today I am
 

00:00:11.880 --> 00:00:14.350 align:start position:0%
scientist in SNP Global and today I am
excited<00:00:12.360><c> to</c><00:00:12.559><c> present</c><00:00:12.920><c> our</c><00:00:13.160><c> work</c><00:00:13.440><c> on</c><00:00:13.679><c> sentiment</c>

00:00:14.350 --> 00:00:14.360 align:start position:0%
excited to present our work on sentiment
 

00:00:14.360 --> 00:00:16.230 align:start position:0%
excited to present our work on sentiment
analysis<00:00:15.200><c> applied</c><00:00:15.599><c> to</c><00:00:15.839><c> company</c>

00:00:16.230 --> 00:00:16.240 align:start position:0%
analysis applied to company
 

00:00:16.240 --> 00:00:18.990 align:start position:0%
analysis applied to company
self-disclosures<00:00:17.080><c> here</c><00:00:18.080><c> is</c><00:00:18.240><c> the</c><00:00:18.439><c> road</c><00:00:18.760><c> map</c>

00:00:18.990 --> 00:00:19.000 align:start position:0%
self-disclosures here is the road map
 

00:00:19.000 --> 00:00:21.269 align:start position:0%
self-disclosures here is the road map
for<00:00:19.240><c> today's</c><00:00:19.800><c> presentation</c><00:00:20.800><c> we'll</c><00:00:21.039><c> be</c>

00:00:21.269 --> 00:00:21.279 align:start position:0%
for today's presentation we'll be
 

00:00:21.279 --> 00:00:23.670 align:start position:0%
for today's presentation we'll be
starting<00:00:21.760><c> with</c><00:00:22.039><c> abstract</c><00:00:22.920><c> followed</c><00:00:23.400><c> by</c>

00:00:23.670 --> 00:00:23.680 align:start position:0%
starting with abstract followed by
 

00:00:23.680 --> 00:00:25.990 align:start position:0%
starting with abstract followed by
introduction<00:00:24.320><c> to</c><00:00:24.519><c> S&amp;P</c><00:00:25.039><c> Global</c><00:00:25.640><c> and</c><00:00:25.840><c> the</c>

00:00:25.990 --> 00:00:26.000 align:start position:0%
introduction to S&amp;P Global and the
 

00:00:26.000 --> 00:00:27.990 align:start position:0%
introduction to S&amp;P Global and the
services<00:00:26.519><c> it</c><00:00:26.760><c> provides</c><00:00:27.359><c> then</c><00:00:27.679><c> problem</c>

00:00:27.990 --> 00:00:28.000 align:start position:0%
services it provides then problem
 

00:00:28.000 --> 00:00:30.390 align:start position:0%
services it provides then problem
statement<00:00:28.519><c> we</c><00:00:28.640><c> are</c><00:00:28.800><c> working</c><00:00:29.199><c> on</c><00:00:30.039><c> then</c>

00:00:30.390 --> 00:00:30.400 align:start position:0%
statement we are working on then
 

00:00:30.400 --> 00:00:32.630 align:start position:0%
statement we are working on then
overview<00:00:31.000><c> of</c><00:00:31.199><c> some</c><00:00:31.439><c> traditional</c><00:00:32.079><c> methods</c>

00:00:32.630 --> 00:00:32.640 align:start position:0%
overview of some traditional methods
 

00:00:32.640 --> 00:00:35.470 align:start position:0%
overview of some traditional methods
followed<00:00:33.079><c> by</c><00:00:33.280><c> Transformer</c><00:00:33.920><c> based</c><00:00:34.280><c> models</c><00:00:35.200><c> and</c>

00:00:35.470 --> 00:00:35.480 align:start position:0%
followed by Transformer based models and
 

00:00:35.480 --> 00:00:37.510 align:start position:0%
followed by Transformer based models and
finally<00:00:36.040><c> some</c><00:00:36.360><c> insights</c><00:00:36.960><c> into</c><00:00:37.239><c> the</c>

00:00:37.510 --> 00:00:37.520 align:start position:0%
finally some insights into the
 

00:00:37.520 --> 00:00:39.950 align:start position:0%
finally some insights into the
comprehensive<00:00:38.320><c> methodology</c><00:00:38.960><c> used</c><00:00:39.559><c> along</c>

00:00:39.950 --> 00:00:39.960 align:start position:0%
comprehensive methodology used along
 

00:00:39.960 --> 00:00:41.869 align:start position:0%
comprehensive methodology used along
with<00:00:40.200><c> some</c><00:00:40.520><c> realtime</c>

00:00:41.869 --> 00:00:41.879 align:start position:0%
with some realtime
 

00:00:41.879 --> 00:00:44.389 align:start position:0%
with some realtime
examples<00:00:42.879><c> as</c><00:00:43.079><c> we</c><00:00:43.239><c> know</c><00:00:43.480><c> natural</c><00:00:43.920><c> language</c>

00:00:44.389 --> 00:00:44.399 align:start position:0%
examples as we know natural language
 

00:00:44.399 --> 00:00:46.590 align:start position:0%
examples as we know natural language
processing<00:00:44.920><c> is</c><00:00:45.039><c> a</c><00:00:45.320><c> dynamic</c><00:00:45.879><c> and</c><00:00:46.079><c> evolving</c>

00:00:46.590 --> 00:00:46.600 align:start position:0%
processing is a dynamic and evolving
 

00:00:46.600 --> 00:00:48.910 align:start position:0%
processing is a dynamic and evolving
field<00:00:47.120><c> with</c><00:00:47.280><c> notable</c><00:00:47.800><c> applications</c><00:00:48.480><c> across</c>

00:00:48.910 --> 00:00:48.920 align:start position:0%
field with notable applications across
 

00:00:48.920 --> 00:00:52.430 align:start position:0%
field with notable applications across
various<00:00:49.320><c> domains</c><00:00:50.280><c> particularly</c><00:00:51.000><c> in</c><00:00:51.440><c> finance</c>

00:00:52.430 --> 00:00:52.440 align:start position:0%
various domains particularly in finance
 

00:00:52.440 --> 00:00:55.189 align:start position:0%
various domains particularly in finance
so<00:00:52.640><c> in</c><00:00:52.840><c> this</c><00:00:53.120><c> presentation</c><00:00:54.120><c> we'll</c><00:00:54.559><c> explore</c>

00:00:55.189 --> 00:00:55.199 align:start position:0%
so in this presentation we'll explore
 

00:00:55.199 --> 00:00:56.990 align:start position:0%
so in this presentation we'll explore
different<00:00:55.600><c> methods</c><00:00:56.039><c> for</c><00:00:56.359><c> analyzing</c>

00:00:56.990 --> 00:00:57.000 align:start position:0%
different methods for analyzing
 

00:00:57.000 --> 00:00:58.950 align:start position:0%
different methods for analyzing
sentiments<00:00:57.719><c> within</c><00:00:58.120><c> self-disclosures</c>

00:00:58.950 --> 00:00:58.960 align:start position:0%
sentiments within self-disclosures
 

00:00:58.960 --> 00:01:02.590 align:start position:0%
sentiments within self-disclosures
published<00:01:00.000><c> on</c><00:01:00.239><c> company</c><00:01:01.199><c> websites</c><00:01:02.199><c> we</c><00:01:02.359><c> will</c>

00:01:02.590 --> 00:01:02.600 align:start position:0%
published on company websites we will
 

00:01:02.600 --> 00:01:04.549 align:start position:0%
published on company websites we will
then<00:01:02.840><c> delve</c><00:01:03.239><c> into</c><00:01:03.519><c> the</c><00:01:03.760><c> design</c><00:01:04.280><c> and</c>

00:01:04.549 --> 00:01:04.559 align:start position:0%
then delve into the design and
 

00:01:04.559 --> 00:01:06.710 align:start position:0%
then delve into the design and
architecture<00:01:05.280><c> of</c><00:01:05.439><c> the</c><00:01:05.680><c> techniques</c><00:01:06.240><c> used</c><00:01:06.600><c> in</c>

00:01:06.710 --> 00:01:06.720 align:start position:0%
architecture of the techniques used in
 

00:01:06.720 --> 00:01:10.109 align:start position:0%
architecture of the techniques used in
our<00:01:07.159><c> solution</c><00:01:08.159><c> which</c><00:01:08.479><c> include</c><00:01:09.360><c> dependency</c>

00:01:10.109 --> 00:01:10.119 align:start position:0%
our solution which include dependency
 

00:01:10.119 --> 00:01:12.830 align:start position:0%
our solution which include dependency
passings<00:01:11.119><c> part</c><00:01:11.360><c> of</c><00:01:11.560><c> speech</c><00:01:11.880><c> tagging</c>

00:01:12.830 --> 00:01:12.840 align:start position:0%
passings part of speech tagging
 

00:01:12.840 --> 00:01:15.270 align:start position:0%
passings part of speech tagging
contextual<00:01:13.840><c> understanding</c><00:01:14.439><c> and</c><00:01:14.720><c> sentiment</c>

00:01:15.270 --> 00:01:15.280 align:start position:0%
contextual understanding and sentiment
 

00:01:15.280 --> 00:01:17.830 align:start position:0%
contextual understanding and sentiment
tagging<00:01:16.240><c> and</c><00:01:16.400><c> the</c><00:01:16.640><c> important</c><00:01:17.240><c> point</c><00:01:17.560><c> that</c><00:01:17.759><c> I</c>

00:01:17.830 --> 00:01:17.840 align:start position:0%
tagging and the important point that I
 

00:01:17.840 --> 00:01:20.749 align:start position:0%
tagging and the important point that I
would<00:01:18.080><c> like</c><00:01:18.320><c> to</c><00:01:18.600><c> highlight</c><00:01:19.119><c> here</c><00:01:19.360><c> is</c><00:01:19.720><c> that</c><00:01:20.439><c> our</c>

00:01:20.749 --> 00:01:20.759 align:start position:0%
would like to highlight here is that our
 

00:01:20.759 --> 00:01:22.870 align:start position:0%
would like to highlight here is that our
approach<00:01:21.200><c> integrates</c><00:01:22.000><c> both</c><00:01:22.360><c> classical</c>

00:01:22.870 --> 00:01:22.880 align:start position:0%
approach integrates both classical
 

00:01:22.880 --> 00:01:25.149 align:start position:0%
approach integrates both classical
machine<00:01:23.240><c> learning</c><00:01:23.640><c> techniques</c><00:01:24.520><c> and</c><00:01:24.759><c> modern</c>

00:01:25.149 --> 00:01:25.159 align:start position:0%
machine learning techniques and modern
 

00:01:25.159 --> 00:01:27.910 align:start position:0%
machine learning techniques and modern
Transformer<00:01:25.799><c> based</c><00:01:26.240><c> models</c><00:01:27.240><c> which</c><00:01:27.439><c> you</c><00:01:27.640><c> know</c>

00:01:27.910 --> 00:01:27.920 align:start position:0%
Transformer based models which you know
 

00:01:27.920 --> 00:01:30.030 align:start position:0%
Transformer based models which you know
finally<00:01:28.560><c> helped</c><00:01:28.920><c> us</c><00:01:29.079><c> in</c><00:01:29.280><c> achieving</c><00:01:29.759><c> this</c><00:01:29.920><c> the</c>

00:01:30.030 --> 00:01:30.040 align:start position:0%
finally helped us in achieving this the
 

00:01:30.040 --> 00:01:32.149 align:start position:0%
finally helped us in achieving this the
desired

00:01:32.149 --> 00:01:32.159 align:start position:0%
desired
 

00:01:32.159 --> 00:01:35.310 align:start position:0%
desired
performance<00:01:33.159><c> now</c><00:01:33.560><c> to</c><00:01:33.759><c> give</c><00:01:33.960><c> you</c><00:01:34.119><c> some</c><00:01:34.520><c> context</c>

00:01:35.310 --> 00:01:35.320 align:start position:0%
performance now to give you some context
 

00:01:35.320 --> 00:01:38.270 align:start position:0%
performance now to give you some context
S&amp;P<00:01:35.840><c> Global</c><00:01:36.360><c> provides</c><00:01:36.920><c> a</c><00:01:37.119><c> range</c><00:01:37.479><c> of</c><00:01:37.680><c> financial</c>

00:01:38.270 --> 00:01:38.280 align:start position:0%
S&amp;P Global provides a range of financial
 

00:01:38.280 --> 00:01:41.870 align:start position:0%
S&amp;P Global provides a range of financial
services<00:01:39.119><c> including</c><00:01:39.759><c> ratings</c><00:01:40.880><c> indices</c>

00:01:41.870 --> 00:01:41.880 align:start position:0%
services including ratings indices
 

00:01:41.880 --> 00:01:43.990 align:start position:0%
services including ratings indices
commodity<00:01:42.479><c> insights</c><00:01:43.240><c> and</c><00:01:43.479><c> Market</c>

00:01:43.990 --> 00:01:44.000 align:start position:0%
commodity insights and Market
 

00:01:44.000 --> 00:01:47.149 align:start position:0%
commodity insights and Market
intelligence<00:01:45.000><c> which</c><00:01:45.240><c> further</c><00:01:45.719><c> covers</c><00:01:46.479><c> credit</c>

00:01:47.149 --> 00:01:47.159 align:start position:0%
intelligence which further covers credit
 

00:01:47.159 --> 00:01:49.910 align:start position:0%
intelligence which further covers credit
market<00:01:47.880><c> and</c><00:01:48.119><c> operational</c><00:01:48.759><c> risks</c><00:01:49.520><c> and</c><00:01:49.680><c> a</c>

00:01:49.910 --> 00:01:49.920 align:start position:0%
market and operational risks and a
 

00:01:49.920 --> 00:01:52.590 align:start position:0%
market and operational risks and a
sentiment<00:01:50.479><c> analysis</c><00:01:51.240><c> specifically</c><00:01:52.000><c> targets</c>

00:01:52.590 --> 00:01:52.600 align:start position:0%
sentiment analysis specifically targets
 

00:01:52.600 --> 00:01:55.310 align:start position:0%
sentiment analysis specifically targets
the<00:01:52.799><c> credit</c><00:01:53.159><c> risk</c>

00:01:55.310 --> 00:01:55.320 align:start position:0%
the credit risk
 

00:01:55.320 --> 00:01:57.709 align:start position:0%
the credit risk
segment<00:01:56.320><c> now</c><00:01:56.680><c> coming</c><00:01:56.920><c> to</c><00:01:57.079><c> the</c><00:01:57.280><c> problem</c>

00:01:57.709 --> 00:01:57.719 align:start position:0%
segment now coming to the problem
 

00:01:57.719 --> 00:02:00.670 align:start position:0%
segment now coming to the problem
statement<00:01:58.560><c> the</c><00:01:58.799><c> objective</c><00:01:59.360><c> is</c><00:01:59.520><c> to</c><00:01:59.960><c> evaluate</c>

00:02:00.670 --> 00:02:00.680 align:start position:0%
statement the objective is to evaluate
 

00:02:00.680 --> 00:02:03.389 align:start position:0%
statement the objective is to evaluate
or<00:02:01.000><c> assess</c><00:02:01.360><c> the</c><00:02:01.560><c> sentiments</c><00:02:02.240><c> associated</c><00:02:02.960><c> with</c>

00:02:03.389 --> 00:02:03.399 align:start position:0%
or assess the sentiments associated with
 

00:02:03.399 --> 00:02:06.350 align:start position:0%
or assess the sentiments associated with
companies<00:02:04.399><c> specifically</c><00:02:05.280><c> utilizing</c><00:02:06.159><c> the</c>

00:02:06.350 --> 00:02:06.360 align:start position:0%
companies specifically utilizing the
 

00:02:06.360 --> 00:02:08.869 align:start position:0%
companies specifically utilizing the
self-disclosures<00:02:07.319><c> available</c><00:02:08.319><c> on</c><00:02:08.560><c> their</c>

00:02:08.869 --> 00:02:08.879 align:start position:0%
self-disclosures available on their
 

00:02:08.879 --> 00:02:11.589 align:start position:0%
self-disclosures available on their
respective<00:02:09.879><c> websites</c><00:02:10.879><c> we</c><00:02:11.080><c> use</c><00:02:11.400><c> these</c>

00:02:11.589 --> 00:02:11.599 align:start position:0%
respective websites we use these
 

00:02:11.599 --> 00:02:13.350 align:start position:0%
respective websites we use these
self-disclosures

00:02:13.350 --> 00:02:13.360 align:start position:0%
self-disclosures
 

00:02:13.360 --> 00:02:15.670 align:start position:0%
self-disclosures
because<00:02:14.360><c> understanding</c><00:02:14.800><c> sentiments</c><00:02:15.440><c> and</c>

00:02:15.670 --> 00:02:15.680 align:start position:0%
because understanding sentiments and
 

00:02:15.680 --> 00:02:18.390 align:start position:0%
because understanding sentiments and
self-disclosures<00:02:16.560><c> give</c><00:02:17.319><c> us</c><00:02:17.680><c> valuable</c>

00:02:18.390 --> 00:02:18.400 align:start position:0%
self-disclosures give us valuable
 

00:02:18.400 --> 00:02:21.869 align:start position:0%
self-disclosures give us valuable
insights<00:02:19.239><c> into</c><00:02:19.519><c> a</c><00:02:19.760><c> company's</c><00:02:20.519><c> credit</c><00:02:20.959><c> health</c>

00:02:21.869 --> 00:02:21.879 align:start position:0%
insights into a company's credit health
 

00:02:21.879 --> 00:02:25.550 align:start position:0%
insights into a company's credit health
and<00:02:22.160><c> are</c><00:02:22.640><c> then</c><00:02:22.879><c> fine</c><00:02:23.319><c> tuned</c><00:02:24.040><c> as</c><00:02:24.160><c> an</c><00:02:24.599><c> overlay</c>

00:02:25.550 --> 00:02:25.560 align:start position:0%
and are then fine tuned as an overlay
 

00:02:25.560 --> 00:02:28.710 align:start position:0%
and are then fine tuned as an overlay
into<00:02:25.920><c> our</c><00:02:26.200><c> probability</c><00:02:26.959><c> of</c><00:02:27.200><c> default</c><00:02:27.800><c> models</c>

00:02:28.710 --> 00:02:28.720 align:start position:0%
into our probability of default models
 

00:02:28.720 --> 00:02:32.229 align:start position:0%
into our probability of default models
as<00:02:28.959><c> well</c><00:02:29.959><c> and</c><00:02:30.360><c> accurate</c><00:02:30.800><c> sentiment</c><00:02:31.400><c> analysis</c>

00:02:32.229 --> 00:02:32.239 align:start position:0%
as well and accurate sentiment analysis
 

00:02:32.239 --> 00:02:35.270 align:start position:0%
as well and accurate sentiment analysis
can<00:02:32.560><c> further</c><00:02:33.040><c> help</c><00:02:33.319><c> us</c><00:02:33.519><c> in</c><00:02:34.200><c> understanding</c><00:02:35.000><c> how</c>

00:02:35.270 --> 00:02:35.280 align:start position:0%
can further help us in understanding how
 

00:02:35.280 --> 00:02:37.589 align:start position:0%
can further help us in understanding how
companies<00:02:35.840><c> perceive</c><00:02:36.360><c> their</c><00:02:36.599><c> own</c><00:02:37.000><c> Financial</c>

00:02:37.589 --> 00:02:37.599 align:start position:0%
companies perceive their own Financial
 

00:02:37.599 --> 00:02:41.350 align:start position:0%
companies perceive their own Financial
Health<00:02:38.120><c> and</c><00:02:38.599><c> risks</c><00:02:39.599><c> now</c><00:02:39.879><c> to</c><00:02:40.159><c> achieve</c><00:02:40.680><c> this</c><00:02:41.120><c> we</c>

00:02:41.350 --> 00:02:41.360 align:start position:0%
Health and risks now to achieve this we
 

00:02:41.360 --> 00:02:43.550 align:start position:0%
Health and risks now to achieve this we
leveraged<00:02:42.120><c> Advanced</c><00:02:42.680><c> natural</c><00:02:43.080><c> language</c>

00:02:43.550 --> 00:02:43.560 align:start position:0%
leveraged Advanced natural language
 

00:02:43.560 --> 00:02:45.869 align:start position:0%
leveraged Advanced natural language
processing<00:02:44.360><c> and</c><00:02:44.640><c> sentiment</c><00:02:45.200><c> analysis</c>

00:02:45.869 --> 00:02:45.879 align:start position:0%
processing and sentiment analysis
 

00:02:45.879 --> 00:02:48.270 align:start position:0%
processing and sentiment analysis
techniques<00:02:46.720><c> to</c><00:02:47.040><c> process</c><00:02:47.480><c> self-disclosures</c>

00:02:48.270 --> 00:02:48.280 align:start position:0%
techniques to process self-disclosures
 

00:02:48.280 --> 00:02:51.190 align:start position:0%
techniques to process self-disclosures
and<00:02:48.920><c> extract</c><00:02:49.440><c> sentiments</c><00:02:50.280><c> related</c><00:02:50.760><c> to</c><00:02:51.000><c> the</c>

00:02:51.190 --> 00:02:51.200 align:start position:0%
and extract sentiments related to the
 

00:02:51.200 --> 00:02:53.149 align:start position:0%
and extract sentiments related to the
Target

00:02:53.149 --> 00:02:53.159 align:start position:0%
Target
 

00:02:53.159 --> 00:02:55.710 align:start position:0%
Target
company<00:02:54.159><c> we</c><00:02:54.360><c> explored</c><00:02:54.879><c> several</c><00:02:55.280><c> back</c><00:02:55.560><c> of</c>

00:02:55.710 --> 00:02:55.720 align:start position:0%
company we explored several back of
 

00:02:55.720 --> 00:02:58.790 align:start position:0%
company we explored several back of
words<00:02:56.080><c> methods</c><00:02:56.680><c> like</c><00:02:56.920><c> Tex</c><00:02:57.280><c> blobe</c><00:02:57.840><c> Vader</c><00:02:58.560><c> and</c>

00:02:58.790 --> 00:02:58.800 align:start position:0%
words methods like Tex blobe Vader and
 

00:02:58.800 --> 00:03:00.390 align:start position:0%
words methods like Tex blobe Vader and
McDonald's<00:02:59.400><c> dictionary</c>

00:03:00.390 --> 00:03:00.400 align:start position:0%
McDonald's dictionary
 

00:03:00.400 --> 00:03:03.710 align:start position:0%
McDonald's dictionary
we<00:03:00.599><c> also</c><00:03:00.879><c> utilize</c><00:03:01.640><c> Transformer</c><00:03:02.280><c> based</c><00:03:02.720><c> models</c>

00:03:03.710 --> 00:03:03.720 align:start position:0%
we also utilize Transformer based models
 

00:03:03.720 --> 00:03:07.110 align:start position:0%
we also utilize Transformer based models
like<00:03:04.040><c> BD</c><00:03:04.640><c> and</c><00:03:04.840><c> finbert</c><00:03:05.760><c> which</c><00:03:06.040><c> offer</c><00:03:06.599><c> Advanced</c>

00:03:07.110 --> 00:03:07.120 align:start position:0%
like BD and finbert which offer Advanced
 

00:03:07.120 --> 00:03:10.030 align:start position:0%
like BD and finbert which offer Advanced
capabilities<00:03:07.840><c> over</c><00:03:08.159><c> traditional</c><00:03:09.040><c> methods</c>

00:03:10.030 --> 00:03:10.040 align:start position:0%
capabilities over traditional methods
 

00:03:10.040 --> 00:03:12.270 align:start position:0%
capabilities over traditional methods
and<00:03:10.319><c> then</c><00:03:10.560><c> curated</c><00:03:11.239><c> the</c><00:03:11.480><c> aspect</c><00:03:11.920><c> based</c>

00:03:12.270 --> 00:03:12.280 align:start position:0%
and then curated the aspect based
 

00:03:12.280 --> 00:03:14.789 align:start position:0%
and then curated the aspect based
algorithm<00:03:13.280><c> which</c><00:03:13.440><c> is</c><00:03:13.720><c> essentially</c><00:03:14.480><c> the</c>

00:03:14.789 --> 00:03:14.799 align:start position:0%
algorithm which is essentially the
 

00:03:14.799 --> 00:03:17.869 align:start position:0%
algorithm which is essentially the
combination<00:03:15.560><c> of</c><00:03:16.200><c> part</c><00:03:16.400><c> of</c><00:03:16.640><c> speech</c><00:03:16.959><c> tagger</c>

00:03:17.869 --> 00:03:17.879 align:start position:0%
combination of part of speech tagger
 

00:03:17.879 --> 00:03:20.430 align:start position:0%
combination of part of speech tagger
dependency<00:03:18.640><c> parser</c><00:03:19.599><c> and</c>

00:03:20.430 --> 00:03:20.440 align:start position:0%
dependency parser and
 

00:03:20.440 --> 00:03:22.990 align:start position:0%
dependency parser and
Transformer<00:03:21.440><c> each</c><00:03:21.760><c> approach</c><00:03:22.200><c> here</c><00:03:22.599><c> has</c><00:03:22.799><c> its</c>

00:03:22.990 --> 00:03:23.000 align:start position:0%
Transformer each approach here has its
 

00:03:23.000 --> 00:03:25.030 align:start position:0%
Transformer each approach here has its
own<00:03:23.280><c> strength</c><00:03:23.879><c> and</c><00:03:24.080><c> weaknesses</c><00:03:24.799><c> by</c>

00:03:25.030 --> 00:03:25.040 align:start position:0%
own strength and weaknesses by
 

00:03:25.040 --> 00:03:27.990 align:start position:0%
own strength and weaknesses by
classifying

00:03:27.990 --> 00:03:28.000 align:start position:0%
 
 

00:03:28.000 --> 00:03:31.550 align:start position:0%
 
sentiments<00:03:29.000><c> for</c><00:03:29.280><c> instance</c><00:03:30.200><c> out</c><00:03:30.439><c> of</c><00:03:30.720><c> 20K</c>

00:03:31.550 --> 00:03:31.560 align:start position:0%
sentiments for instance out of 20K
 

00:03:31.560 --> 00:03:35.270 align:start position:0%
sentiments for instance out of 20K
articles<00:03:32.280><c> here</c><00:03:32.799><c> text</c><00:03:33.159><c> blob</c><00:03:33.519><c> classified</c><00:03:34.400><c> only</c>

00:03:35.270 --> 00:03:35.280 align:start position:0%
articles here text blob classified only
 

00:03:35.280 --> 00:03:38.750 align:start position:0%
articles here text blob classified only
53.4%<00:03:36.280><c> of</c><00:03:36.480><c> Articles</c><00:03:36.959><c> as</c><00:03:37.319><c> positive</c><00:03:38.319><c> whereas</c>

00:03:38.750 --> 00:03:38.760 align:start position:0%
53.4% of Articles as positive whereas
 

00:03:38.760 --> 00:03:40.550 align:start position:0%
53.4% of Articles as positive whereas
Vader<00:03:39.200><c> classified</c>

00:03:40.550 --> 00:03:40.560 align:start position:0%
Vader classified
 

00:03:40.560 --> 00:03:44.429 align:start position:0%
Vader classified
63.2%<00:03:41.560><c> as</c><00:03:41.840><c> positive</c><00:03:42.799><c> that</c><00:03:42.959><c> is</c><00:03:43.200><c> about</c><00:03:43.640><c> 10%</c>

00:03:44.429 --> 00:03:44.439 align:start position:0%
63.2% as positive that is about 10%
 

00:03:44.439 --> 00:03:46.589 align:start position:0%
63.2% as positive that is about 10%
increase<00:03:44.840><c> in</c><00:03:45.120><c> percentage</c><00:03:45.599><c> of</c><00:03:45.920><c> positive</c>

00:03:46.589 --> 00:03:46.599 align:start position:0%
increase in percentage of positive
 

00:03:46.599 --> 00:03:49.750 align:start position:0%
increase in percentage of positive
articles<00:03:47.599><c> which</c><00:03:47.799><c> sort</c><00:03:48.120><c> of</c><00:03:48.439><c> also</c><00:03:48.799><c> shows</c><00:03:49.239><c> us</c>

00:03:49.750 --> 00:03:49.760 align:start position:0%
articles which sort of also shows us
 

00:03:49.760 --> 00:03:51.990 align:start position:0%
articles which sort of also shows us
that<00:03:50.239><c> different</c><00:03:50.640><c> methods</c><00:03:51.159><c> can</c><00:03:51.360><c> lead</c><00:03:51.680><c> to</c>

00:03:51.990 --> 00:03:52.000 align:start position:0%
that different methods can lead to
 

00:03:52.000 --> 00:03:53.949 align:start position:0%
that different methods can lead to
different<00:03:52.439><c> interpretations</c><00:03:53.239><c> of</c><00:03:53.400><c> the</c><00:03:53.560><c> same</c>

00:03:53.949 --> 00:03:53.959 align:start position:0%
different interpretations of the same
 

00:03:53.959 --> 00:03:56.869 align:start position:0%
different interpretations of the same
text<00:03:54.840><c> and</c><00:03:55.079><c> key</c><00:03:55.319><c> observations</c><00:03:56.079><c> we</c><00:03:56.319><c> had</c><00:03:56.599><c> from</c>

00:03:56.869 --> 00:03:56.879 align:start position:0%
text and key observations we had from
 

00:03:56.879 --> 00:03:59.910 align:start position:0%
text and key observations we had from
these<00:03:57.120><c> methods</c><00:03:57.599><c> is</c><00:03:58.480><c> first</c><00:03:58.720><c> one</c><00:03:58.920><c> is</c><00:03:59.120><c> dependence</c>

00:03:59.910 --> 00:03:59.920 align:start position:0%
these methods is first one is dependence
 

00:03:59.920 --> 00:04:02.789 align:start position:0%
these methods is first one is dependence
on<00:04:00.480><c> dictionaries</c><00:04:01.480><c> the</c><00:04:01.720><c> accuracy</c><00:04:02.280><c> of</c><00:04:02.519><c> these</c>

00:04:02.789 --> 00:04:02.799 align:start position:0%
on dictionaries the accuracy of these
 

00:04:02.799 --> 00:04:05.830 align:start position:0%
on dictionaries the accuracy of these
methods<00:04:03.680><c> relies</c><00:04:04.680><c> heavily</c><00:04:05.040><c> on</c><00:04:05.280><c> the</c>

00:04:05.830 --> 00:04:05.840 align:start position:0%
methods relies heavily on the
 

00:04:05.840 --> 00:04:08.550 align:start position:0%
methods relies heavily on the
construction<00:04:07.280><c> comprehensiveness</c><00:04:08.280><c> and</c>

00:04:08.550 --> 00:04:08.560 align:start position:0%
construction comprehensiveness and
 

00:04:08.560 --> 00:04:11.110 align:start position:0%
construction comprehensiveness and
relevance<00:04:09.040><c> of</c><00:04:09.200><c> the</c><00:04:09.400><c> sentiment</c><00:04:10.120><c> dictionaries</c>

00:04:11.110 --> 00:04:11.120 align:start position:0%
relevance of the sentiment dictionaries
 

00:04:11.120 --> 00:04:13.229 align:start position:0%
relevance of the sentiment dictionaries
and<00:04:11.360><c> another</c><00:04:11.760><c> challenge</c><00:04:12.200><c> is</c><00:04:12.560><c> continuous</c>

00:04:13.229 --> 00:04:13.239 align:start position:0%
and another challenge is continuous
 

00:04:13.239 --> 00:04:15.910 align:start position:0%
and another challenge is continuous
option<00:04:13.840><c> required</c><00:04:14.720><c> which</c><00:04:14.920><c> can</c><00:04:15.239><c> eventually</c>

00:04:15.910 --> 00:04:15.920 align:start position:0%
option required which can eventually
 

00:04:15.920 --> 00:04:20.949 align:start position:0%
option required which can eventually
manage<00:04:16.600><c> evolving</c><00:04:17.280><c> language</c><00:04:18.079><c> and</c><00:04:18.280><c> emerging</c>

00:04:20.949 --> 00:04:20.959 align:start position:0%
 
 

00:04:20.959 --> 00:04:23.790 align:start position:0%
 
terms<00:04:21.959><c> here</c><00:04:22.120><c> are</c><00:04:22.320><c> some</c><00:04:22.639><c> other</c><00:04:22.960><c> drawbacks</c><00:04:23.560><c> of</c>

00:04:23.790 --> 00:04:23.800 align:start position:0%
terms here are some other drawbacks of
 

00:04:23.800 --> 00:04:26.749 align:start position:0%
terms here are some other drawbacks of
using<00:04:24.120><c> the</c><00:04:24.400><c> back</c><00:04:24.639><c> of</c><00:04:24.800><c> word</c><00:04:25.120><c> approaches</c><00:04:26.080><c> like</c>

00:04:26.749 --> 00:04:26.759 align:start position:0%
using the back of word approaches like
 

00:04:26.759 --> 00:04:29.230 align:start position:0%
using the back of word approaches like
there<00:04:26.919><c> is</c><00:04:27.280><c> fixed</c><00:04:27.840><c> weight</c><00:04:28.360><c> associated</c><00:04:29.000><c> with</c>

00:04:29.230 --> 00:04:29.240 align:start position:0%
there is fixed weight associated with
 

00:04:29.240 --> 00:04:31.469 align:start position:0%
there is fixed weight associated with
each<00:04:29.520><c> word</c><00:04:29.759><c> word</c><00:04:30.400><c> which</c><00:04:30.520><c> is</c><00:04:30.720><c> you</c><00:04:30.840><c> know</c><00:04:31.120><c> helping</c>

00:04:31.469 --> 00:04:31.479 align:start position:0%
each word word which is you know helping
 

00:04:31.479 --> 00:04:34.390 align:start position:0%
each word word which is you know helping
us<00:04:31.639><c> in</c><00:04:31.880><c> deciding</c><00:04:32.440><c> the</c><00:04:32.759><c> polarity</c><00:04:33.759><c> but</c><00:04:34.000><c> certain</c>

00:04:34.390 --> 00:04:34.400 align:start position:0%
us in deciding the polarity but certain
 

00:04:34.400 --> 00:04:36.430 align:start position:0%
us in deciding the polarity but certain
words<00:04:34.759><c> like</c><00:04:35.000><c> mentionable</c><00:04:35.880><c> cannot</c><00:04:36.240><c> be</c>

00:04:36.430 --> 00:04:36.440 align:start position:0%
words like mentionable cannot be
 

00:04:36.440 --> 00:04:38.830 align:start position:0%
words like mentionable cannot be
directly<00:04:36.880><c> classified</c><00:04:37.440><c> as</c><00:04:37.759><c> positive</c><00:04:38.360><c> negative</c>

00:04:38.830 --> 00:04:38.840 align:start position:0%
directly classified as positive negative
 

00:04:38.840 --> 00:04:41.950 align:start position:0%
directly classified as positive negative
or<00:04:39.479><c> neutral</c><00:04:40.479><c> these</c><00:04:40.720><c> methods</c><00:04:41.199><c> also</c><00:04:41.479><c> struggle</c>

00:04:41.950 --> 00:04:41.960 align:start position:0%
or neutral these methods also struggle
 

00:04:41.960 --> 00:04:44.189 align:start position:0%
or neutral these methods also struggle
with<00:04:42.199><c> situations</c><00:04:42.880><c> where</c><00:04:43.120><c> certain</c><00:04:43.520><c> words</c><00:04:43.960><c> have</c>

00:04:44.189 --> 00:04:44.199 align:start position:0%
with situations where certain words have
 

00:04:44.199 --> 00:04:47.350 align:start position:0%
with situations where certain words have
multiple<00:04:44.720><c> meanings</c><00:04:45.520><c> like</c><00:04:46.039><c> head</c><00:04:46.240><c> of</c><00:04:46.440><c> the</c><00:04:46.560><c> deal</c>

00:04:47.350 --> 00:04:47.360 align:start position:0%
multiple meanings like head of the deal
 

00:04:47.360 --> 00:04:51.029 align:start position:0%
multiple meanings like head of the deal
versus<00:04:47.960><c> ear</c><00:04:48.280><c> but</c><00:04:48.560><c> hurts</c><00:04:49.000><c> the</c><00:04:49.400><c> head</c><00:04:50.400><c> then</c><00:04:50.600><c> words</c>

00:04:51.029 --> 00:04:51.039 align:start position:0%
versus ear but hurts the head then words
 

00:04:51.039 --> 00:04:54.390 align:start position:0%
versus ear but hurts the head then words
like<00:04:51.320><c> no</c><00:04:51.960><c> not</c><00:04:52.639><c> less</c><00:04:53.039><c> do</c><00:04:53.240><c> not</c><00:04:53.680><c> always</c><00:04:54.000><c> indicate</c>

00:04:54.390 --> 00:04:54.400 align:start position:0%
like no not less do not always indicate
 

00:04:54.400 --> 00:04:55.790 align:start position:0%
like no not less do not always indicate
a<00:04:54.560><c> negative</c>

00:04:55.790 --> 00:04:55.800 align:start position:0%
a negative
 

00:04:55.800 --> 00:04:58.469 align:start position:0%
a negative
sentiment<00:04:56.800><c> and</c><00:04:57.039><c> there</c><00:04:57.400><c> also</c><00:04:57.800><c> exist</c><00:04:58.240><c> other</c>

00:04:58.469 --> 00:04:58.479 align:start position:0%
sentiment and there also exist other
 

00:04:58.479 --> 00:05:00.590 align:start position:0%
sentiment and there also exist other
scenarios<00:04:59.320><c> which</c><00:04:59.479><c> are</c><00:04:59.840><c> although</c><00:05:00.320><c> less</c>

00:05:00.590 --> 00:05:00.600 align:start position:0%
scenarios which are although less
 

00:05:00.600 --> 00:05:02.670 align:start position:0%
scenarios which are although less
relevant<00:05:01.039><c> for</c><00:05:01.320><c> companies</c><00:05:02.039><c> like</c><00:05:02.199><c> spending</c>

00:05:02.670 --> 00:05:02.680 align:start position:0%
relevant for companies like spending
 

00:05:02.680 --> 00:05:05.510 align:start position:0%
relevant for companies like spending
mistakes<00:05:03.320><c> SLS</c><00:05:04.240><c> biases</c><00:05:04.759><c> while</c><00:05:04.960><c> creating</c>

00:05:05.510 --> 00:05:05.520 align:start position:0%
mistakes SLS biases while creating
 

00:05:05.520 --> 00:05:09.189 align:start position:0%
mistakes SLS biases while creating
dictionaries<00:05:06.520><c> sarcasm</c><00:05:07.680><c> irony</c><00:05:08.680><c> which</c><00:05:08.960><c> these</c>

00:05:09.189 --> 00:05:09.199 align:start position:0%
dictionaries sarcasm irony which these
 

00:05:09.199 --> 00:05:13.070 align:start position:0%
dictionaries sarcasm irony which these
methods<00:05:09.919><c> generally</c><00:05:10.440><c> handle</c>

00:05:13.070 --> 00:05:13.080 align:start position:0%
 
 

00:05:13.080 --> 00:05:16.029 align:start position:0%
 
poorly<00:05:14.080><c> on</c><00:05:14.320><c> doing</c><00:05:14.639><c> error</c><00:05:15.039><c> analysis</c><00:05:15.560><c> of</c><00:05:15.800><c> some</c>

00:05:16.029 --> 00:05:16.039 align:start position:0%
poorly on doing error analysis of some
 

00:05:16.039 --> 00:05:19.070 align:start position:0%
poorly on doing error analysis of some
of<00:05:16.240><c> the</c><00:05:16.400><c> Articles</c><00:05:17.240><c> we</c><00:05:17.479><c> found</c><00:05:18.000><c> that</c><00:05:18.440><c> words</c><00:05:18.880><c> like</c>

00:05:19.070 --> 00:05:19.080 align:start position:0%
of the Articles we found that words like
 

00:05:19.080 --> 00:05:22.590 align:start position:0%
of the Articles we found that words like
Shing<00:05:20.000><c> fighting</c><00:05:20.880><c> pushing</c><00:05:21.720><c> failure</c><00:05:22.400><c> are</c>

00:05:22.590 --> 00:05:22.600 align:start position:0%
Shing fighting pushing failure are
 

00:05:22.600 --> 00:05:25.469 align:start position:0%
Shing fighting pushing failure are
leading<00:05:22.960><c> to</c><00:05:23.199><c> incorrect</c><00:05:24.199><c> predictions</c><00:05:25.199><c> if</c><00:05:25.319><c> we</c>

00:05:25.469 --> 00:05:25.479 align:start position:0%
leading to incorrect predictions if we
 

00:05:25.479 --> 00:05:28.029 align:start position:0%
leading to incorrect predictions if we
look<00:05:25.639><c> at</c><00:05:25.840><c> the</c><00:05:26.039><c> examples</c><00:05:26.800><c> here</c><00:05:27.400><c> the</c><00:05:27.560><c> articles</c>

00:05:28.029 --> 00:05:28.039 align:start position:0%
look at the examples here the articles
 

00:05:28.039 --> 00:05:30.390 align:start position:0%
look at the examples here the articles
are<00:05:28.319><c> actually</c><00:05:28.840><c> positive</c><00:05:29.720><c> like</c><00:05:29.960><c> pushing</c>

00:05:30.390 --> 00:05:30.400 align:start position:0%
are actually positive like pushing
 

00:05:30.400 --> 00:05:34.309 align:start position:0%
are actually positive like pushing
boundaries<00:05:31.000><c> with</c>

00:05:34.309 --> 00:05:34.319 align:start position:0%
 
 

00:05:34.319 --> 00:05:37.390 align:start position:0%
 
AI<00:05:35.319><c> we</c><00:05:35.560><c> then</c><00:05:35.720><c> switch</c><00:05:36.120><c> to</c><00:05:36.400><c> Transformer</c><00:05:37.080><c> based</c>

00:05:37.390 --> 00:05:37.400 align:start position:0%
AI we then switch to Transformer based
 

00:05:37.400 --> 00:05:41.070 align:start position:0%
AI we then switch to Transformer based
models<00:05:38.280><c> like</c><00:05:38.560><c> bird</c><00:05:39.080><c> and</c><00:05:39.360><c> financial</c><00:05:40.000><c> bird</c><00:05:40.840><c> now</c>

00:05:41.070 --> 00:05:41.080 align:start position:0%
models like bird and financial bird now
 

00:05:41.080 --> 00:05:43.270 align:start position:0%
models like bird and financial bird now
there<00:05:41.199><c> are</c><00:05:41.479><c> some</c><00:05:41.880><c> certain</c><00:05:42.400><c> advantages</c><00:05:43.039><c> and</c>

00:05:43.270 --> 00:05:43.280 align:start position:0%
there are some certain advantages and
 

00:05:43.280 --> 00:05:47.110 align:start position:0%
there are some certain advantages and
limitations<00:05:43.960><c> of</c><00:05:44.280><c> these</c><00:05:44.720><c> as</c><00:05:45.000><c> well</c><00:05:45.919><c> and</c>

00:05:47.110 --> 00:05:47.120 align:start position:0%
limitations of these as well and
 

00:05:47.120 --> 00:05:50.590 align:start position:0%
limitations of these as well and
advantages<00:05:48.120><c> uh</c><00:05:48.280><c> we</c><00:05:48.479><c> have</c><00:05:48.840><c> context</c><00:05:49.600><c> awareness</c>

00:05:50.590 --> 00:05:50.600 align:start position:0%
advantages uh we have context awareness
 

00:05:50.600 --> 00:05:53.469 align:start position:0%
advantages uh we have context awareness
these<00:05:50.840><c> models</c><00:05:51.319><c> handle</c><00:05:51.759><c> polyi</c><00:05:52.440><c> and</c><00:05:52.639><c> negations</c>

00:05:53.469 --> 00:05:53.479 align:start position:0%
these models handle polyi and negations
 

00:05:53.479 --> 00:05:55.710 align:start position:0%
these models handle polyi and negations
more<00:05:53.880><c> effectively</c><00:05:54.479><c> by</c><00:05:55.039><c> understanding</c><00:05:55.400><c> the</c>

00:05:55.710 --> 00:05:55.720 align:start position:0%
more effectively by understanding the
 

00:05:55.720 --> 00:05:58.710 align:start position:0%
more effectively by understanding the
context<00:05:56.280><c> in</c><00:05:56.479><c> which</c><00:05:56.720><c> words</c><00:05:57.080><c> are</c><00:05:57.280><c> being</c><00:05:57.720><c> used</c>

00:05:58.710 --> 00:05:58.720 align:start position:0%
context in which words are being used
 

00:05:58.720 --> 00:06:00.710 align:start position:0%
context in which words are being used
then<00:05:58.919><c> comes</c><00:05:59.240><c> trans</c><00:05:59.680><c> for</c><00:05:59.880><c> learning</c>

00:06:00.710 --> 00:06:00.720 align:start position:0%
then comes trans for learning
 

00:06:00.720 --> 00:06:03.150 align:start position:0%
then comes trans for learning
pre-trained<00:06:01.400><c> on</c><00:06:01.680><c> vast</c><00:06:02.080><c> data</c><00:06:02.440><c> sets</c><00:06:02.960><c> these</c>

00:06:03.150 --> 00:06:03.160 align:start position:0%
pre-trained on vast data sets these
 

00:06:03.160 --> 00:06:05.430 align:start position:0%
pre-trained on vast data sets these
models<00:06:03.560><c> can</c><00:06:03.680><c> be</c><00:06:03.880><c> fine-</c><00:06:04.240><c> tuned</c><00:06:04.560><c> for</c><00:06:04.840><c> specific</c>

00:06:05.430 --> 00:06:05.440 align:start position:0%
models can be fine- tuned for specific
 

00:06:05.440 --> 00:06:07.870 align:start position:0%
models can be fine- tuned for specific
tasks<00:06:06.440><c> then</c><00:06:06.680><c> natural</c><00:06:07.120><c> language</c>

00:06:07.870 --> 00:06:07.880 align:start position:0%
tasks then natural language
 

00:06:07.880 --> 00:06:10.070 align:start position:0%
tasks then natural language
understanding<00:06:08.639><c> they</c><00:06:08.759><c> are</c><00:06:09.080><c> Adept</c><00:06:09.520><c> at</c><00:06:09.720><c> dealing</c>

00:06:10.070 --> 00:06:10.080 align:start position:0%
understanding they are Adept at dealing
 

00:06:10.080 --> 00:06:12.909 align:start position:0%
understanding they are Adept at dealing
with<00:06:10.240><c> spelling</c><00:06:10.720><c> mistakes</c><00:06:11.400><c> slang</c><00:06:12.039><c> and</c><00:06:12.240><c> other</c>

00:06:12.909 --> 00:06:12.919 align:start position:0%
with spelling mistakes slang and other
 

00:06:12.919 --> 00:06:15.710 align:start position:0%
with spelling mistakes slang and other
languages<00:06:13.919><c> and</c><00:06:14.120><c> few</c><00:06:14.440><c> short</c><00:06:14.720><c> learning</c><00:06:15.479><c> these</c>

00:06:15.710 --> 00:06:15.720 align:start position:0%
languages and few short learning these
 

00:06:15.720 --> 00:06:17.830 align:start position:0%
languages and few short learning these
models<00:06:16.120><c> are</c><00:06:16.400><c> capable</c><00:06:16.759><c> of</c><00:06:16.919><c> learning</c><00:06:17.440><c> from</c><00:06:17.639><c> a</c>

00:06:17.830 --> 00:06:17.840 align:start position:0%
models are capable of learning from a
 

00:06:17.840 --> 00:06:21.189 align:start position:0%
models are capable of learning from a
small<00:06:18.160><c> number</c><00:06:18.479><c> of</c><00:06:18.759><c> examples</c><00:06:19.759><c> scalability</c><00:06:20.639><c> and</c>

00:06:21.189 --> 00:06:21.199 align:start position:0%
small number of examples scalability and
 

00:06:21.199 --> 00:06:23.589 align:start position:0%
small number of examples scalability and
parallelization<00:06:22.199><c> they</c><00:06:22.319><c> can</c><00:06:22.720><c> efficiently</c>

00:06:23.589 --> 00:06:23.599 align:start position:0%
parallelization they can efficiently
 

00:06:23.599 --> 00:06:26.749 align:start position:0%
parallelization they can efficiently
process<00:06:24.080><c> large</c><00:06:24.400><c> amount</c><00:06:24.680><c> of</c><00:06:25.080><c> data</c><00:06:26.080><c> and</c><00:06:26.400><c> some</c><00:06:26.599><c> of</c>

00:06:26.749 --> 00:06:26.759 align:start position:0%
process large amount of data and some of
 

00:06:26.759 --> 00:06:29.990 align:start position:0%
process large amount of data and some of
the<00:06:26.919><c> limitations</c><00:06:27.599><c> are</c><00:06:28.440><c> like</c><00:06:28.759><c> entity</c><00:06:29.240><c> specific</c>

00:06:29.990 --> 00:06:30.000 align:start position:0%
the limitations are like entity specific
 

00:06:30.000 --> 00:06:33.029 align:start position:0%
the limitations are like entity specific
sentiment<00:06:30.560><c> extraction</c><00:06:31.160><c> is</c><00:06:31.440><c> not</c><00:06:31.800><c> possible</c><00:06:32.759><c> so</c>

00:06:33.029 --> 00:06:33.039 align:start position:0%
sentiment extraction is not possible so
 

00:06:33.039 --> 00:06:35.350 align:start position:0%
sentiment extraction is not possible so
difficulties<00:06:33.840><c> arise</c><00:06:34.599><c> when</c><00:06:34.840><c> multiple</c>

00:06:35.350 --> 00:06:35.360 align:start position:0%
difficulties arise when multiple
 

00:06:35.360 --> 00:06:37.670 align:start position:0%
difficulties arise when multiple
entities<00:06:35.919><c> are</c><00:06:36.160><c> mentioned</c><00:06:36.599><c> in</c><00:06:36.680><c> a</c><00:06:36.880><c> single</c>

00:06:37.670 --> 00:06:37.680 align:start position:0%
entities are mentioned in a single
 

00:06:37.680 --> 00:06:40.550 align:start position:0%
entities are mentioned in a single
sentence<00:06:38.680><c> like</c><00:06:38.919><c> the</c><00:06:39.199><c> example</c><00:06:39.800><c> we</c><00:06:39.960><c> mentioned</c>

00:06:40.550 --> 00:06:40.560 align:start position:0%
sentence like the example we mentioned
 

00:06:40.560 --> 00:06:44.189 align:start position:0%
sentence like the example we mentioned
here<00:06:41.520><c> before</c><00:06:41.960><c> the</c><00:06:42.160><c> help</c><00:06:42.440><c> of</c><00:06:42.680><c> Unika</c><00:06:43.440><c> and</c><00:06:43.720><c> Amazon</c>

00:06:44.189 --> 00:06:44.199 align:start position:0%
here before the help of Unika and Amazon
 

00:06:44.199 --> 00:06:46.990 align:start position:0%
here before the help of Unika and Amazon
web<00:06:44.639><c> services</c><00:06:45.639><c> web</c><00:06:45.919><c> Coast</c><00:06:46.199><c> media</c><00:06:46.800><c> was</c>

00:06:46.990 --> 00:06:47.000 align:start position:0%
web services web Coast media was
 

00:06:47.000 --> 00:06:49.710 align:start position:0%
web services web Coast media was
struggling<00:06:47.560><c> in</c><00:06:47.759><c> two</c><00:06:48.039><c> different</c><00:06:48.720><c> environments</c>

00:06:49.710 --> 00:06:49.720 align:start position:0%
struggling in two different environments
 

00:06:49.720 --> 00:06:52.870 align:start position:0%
struggling in two different environments
so<00:06:50.120><c> here</c><00:06:50.639><c> we</c><00:06:50.840><c> cannot</c><00:06:51.440><c> expect</c><00:06:51.960><c> sentiments</c><00:06:52.639><c> with</c>

00:06:52.870 --> 00:06:52.880 align:start position:0%
so here we cannot expect sentiments with
 

00:06:52.880 --> 00:06:55.589 align:start position:0%
so here we cannot expect sentiments with
respect<00:06:53.160><c> to</c><00:06:53.400><c> different</c><00:06:53.960><c> entities</c><00:06:54.599><c> mentioned</c>

00:06:55.589 --> 00:06:55.599 align:start position:0%
respect to different entities mentioned
 

00:06:55.599 --> 00:06:58.790 align:start position:0%
respect to different entities mentioned
like<00:06:55.879><c> Unika</c><00:06:56.720><c> Amazon</c><00:06:57.160><c> web</c><00:06:57.400><c> services</c><00:06:58.280><c> and</c><00:06:58.520><c> web</c>

00:06:58.790 --> 00:06:58.800 align:start position:0%
like Unika Amazon web services and web
 

00:06:58.800 --> 00:07:00.230 align:start position:0%
like Unika Amazon web services and web
post<00:06:59.039><c> media</c>

00:07:00.230 --> 00:07:00.240 align:start position:0%
post media
 

00:07:00.240 --> 00:07:03.510 align:start position:0%
post media
and<00:07:00.560><c> the</c><00:07:01.039><c> uh</c><00:07:01.199><c> second</c><00:07:01.520><c> limitation</c><00:07:02.120><c> is</c><00:07:02.479><c> lack</c><00:07:02.759><c> of</c>

00:07:03.510 --> 00:07:03.520 align:start position:0%
and the uh second limitation is lack of
 

00:07:03.520 --> 00:07:05.790 align:start position:0%
and the uh second limitation is lack of
interpretability<00:07:04.520><c> wherein</c><00:07:05.440><c> understanding</c>

00:07:05.790 --> 00:07:05.800 align:start position:0%
interpretability wherein understanding
 

00:07:05.800 --> 00:07:08.550 align:start position:0%
interpretability wherein understanding
the<00:07:06.039><c> reasoning</c><00:07:06.560><c> behind</c><00:07:06.960><c> model</c><00:07:07.400><c> decisons</c><00:07:08.360><c> can</c>

00:07:08.550 --> 00:07:08.560 align:start position:0%
the reasoning behind model decisons can
 

00:07:08.560 --> 00:07:11.189 align:start position:0%
the reasoning behind model decisons can
be

00:07:11.189 --> 00:07:11.199 align:start position:0%
 
 

00:07:11.199 --> 00:07:13.230 align:start position:0%
 
challenging<00:07:12.199><c> then</c><00:07:12.319><c> for</c><00:07:12.560><c> doing</c><00:07:12.840><c> error</c>

00:07:13.230 --> 00:07:13.240 align:start position:0%
challenging then for doing error
 

00:07:13.240 --> 00:07:16.189 align:start position:0%
challenging then for doing error
analysis<00:07:14.080><c> we</c><00:07:14.360><c> visualize</c><00:07:15.000><c> the</c><00:07:15.160><c> attention</c><00:07:15.720><c> View</c>

00:07:16.189 --> 00:07:16.199 align:start position:0%
analysis we visualize the attention View
 

00:07:16.199 --> 00:07:18.670 align:start position:0%
analysis we visualize the attention View
and<00:07:16.440><c> what</c><00:07:16.560><c> we</c><00:07:16.800><c> found</c><00:07:17.160><c> is</c><00:07:17.440><c> that</c><00:07:17.879><c> company</c><00:07:18.240><c> names</c>

00:07:18.670 --> 00:07:18.680 align:start position:0%
and what we found is that company names
 

00:07:18.680 --> 00:07:21.589 align:start position:0%
and what we found is that company names
like<00:07:18.919><c> Ona</c><00:07:19.560><c> and</c><00:07:19.800><c> Amazon</c><00:07:20.199><c> web</c><00:07:20.479><c> services</c><00:07:21.360><c> or</c>

00:07:21.589 --> 00:07:21.599 align:start position:0%
like Ona and Amazon web services or
 

00:07:21.599 --> 00:07:24.309 align:start position:0%
like Ona and Amazon web services or
domain<00:07:22.039><c> dependent</c><00:07:22.520><c> terms</c><00:07:23.199><c> are</c><00:07:23.560><c> generally</c><00:07:24.120><c> out</c>

00:07:24.309 --> 00:07:24.319 align:start position:0%
domain dependent terms are generally out
 

00:07:24.319 --> 00:07:26.350 align:start position:0%
domain dependent terms are generally out
of<00:07:24.560><c> vocabulary</c><00:07:25.360><c> so</c><00:07:25.560><c> we</c><00:07:25.720><c> have</c><00:07:25.879><c> noisy</c>

00:07:26.350 --> 00:07:26.360 align:start position:0%
of vocabulary so we have noisy
 

00:07:26.360 --> 00:07:30.270 align:start position:0%
of vocabulary so we have noisy
tokenization

00:07:30.270 --> 00:07:30.280 align:start position:0%
 
 

00:07:30.280 --> 00:07:32.430 align:start position:0%
 
this<00:07:30.400><c> is</c><00:07:30.560><c> our</c><00:07:30.840><c> final</c><00:07:31.199><c> methodology</c><00:07:32.120><c> which</c>

00:07:32.430 --> 00:07:32.440 align:start position:0%
this is our final methodology which
 

00:07:32.440 --> 00:07:36.070 align:start position:0%
this is our final methodology which
involves<00:07:33.280><c> a</c><00:07:33.479><c> multi-step</c><00:07:34.360><c> process</c><00:07:35.080><c> including</c>

00:07:36.070 --> 00:07:36.080 align:start position:0%
involves a multi-step process including
 

00:07:36.080 --> 00:07:39.830 align:start position:0%
involves a multi-step process including
data<00:07:36.639><c> crawling</c><00:07:37.639><c> text</c><00:07:38.120><c> filtering</c><00:07:39.120><c> content</c>

00:07:39.830 --> 00:07:39.840 align:start position:0%
data crawling text filtering content
 

00:07:39.840 --> 00:07:43.230 align:start position:0%
data crawling text filtering content
relevance<00:07:40.840><c> and</c><00:07:41.120><c> sentiment</c><00:07:41.680><c> extraction</c><00:07:42.240><c> using</c>

00:07:43.230 --> 00:07:43.240 align:start position:0%
relevance and sentiment extraction using
 

00:07:43.240 --> 00:07:46.469 align:start position:0%
relevance and sentiment extraction using
finbert<00:07:44.240><c> step</c><00:07:44.520><c> one</c><00:07:44.800><c> here</c><00:07:45.000><c> is</c><00:07:45.159><c> data</c><00:07:45.520><c> crawling</c>

00:07:46.469 --> 00:07:46.479 align:start position:0%
finbert step one here is data crawling
 

00:07:46.479 --> 00:07:48.749 align:start position:0%
finbert step one here is data crawling
this<00:07:46.680><c> step</c><00:07:47.159><c> involves</c><00:07:47.840><c> systematically</c>

00:07:48.749 --> 00:07:48.759 align:start position:0%
this step involves systematically
 

00:07:48.759 --> 00:07:51.469 align:start position:0%
this step involves systematically
extracting<00:07:49.560><c> or</c><00:07:49.840><c> retrieving</c><00:07:50.479><c> data</c><00:07:50.879><c> from</c><00:07:51.159><c> each</c>

00:07:51.469 --> 00:07:51.479 align:start position:0%
extracting or retrieving data from each
 

00:07:51.479 --> 00:07:54.550 align:start position:0%
extracting or retrieving data from each
company's<00:07:52.280><c> website</c><00:07:53.280><c> now</c><00:07:53.560><c> once</c><00:07:53.800><c> the</c><00:07:53.960><c> data</c><00:07:54.280><c> is</c>

00:07:54.550 --> 00:07:54.560 align:start position:0%
company's website now once the data is
 

00:07:54.560 --> 00:07:57.029 align:start position:0%
company's website now once the data is
gathered<00:07:55.400><c> it</c><00:07:55.599><c> under</c><00:07:55.960><c> goes</c><00:07:56.199><c> a</c><00:07:56.360><c> filtering</c>

00:07:57.029 --> 00:07:57.039 align:start position:0%
gathered it under goes a filtering
 

00:07:57.039 --> 00:08:00.430 align:start position:0%
gathered it under goes a filtering
process<00:07:57.599><c> to</c><00:07:57.879><c> isolate</c><00:07:58.400><c> the</c><00:07:58.520><c> relevant</c><00:07:59.039><c> content</c>

00:08:00.430 --> 00:08:00.440 align:start position:0%
process to isolate the relevant content
 

00:08:00.440 --> 00:08:03.070 align:start position:0%
process to isolate the relevant content
so<00:08:00.680><c> in</c><00:08:00.919><c> step</c><00:08:01.319><c> two</c><00:08:02.120><c> we</c><00:08:02.240><c> are</c><00:08:02.520><c> essentially</c>

00:08:03.070 --> 00:08:03.080 align:start position:0%
so in step two we are essentially
 

00:08:03.080 --> 00:08:06.029 align:start position:0%
so in step two we are essentially
filtering<00:08:03.680><c> Pages</c><00:08:04.199><c> like</c><00:08:04.479><c> news</c><00:08:05.240><c> media</c><00:08:05.840><c> and</c>

00:08:06.029 --> 00:08:06.039 align:start position:0%
filtering Pages like news media and
 

00:08:06.039 --> 00:08:08.469 align:start position:0%
filtering Pages like news media and
press<00:08:06.479><c> releases</c><00:08:07.479><c> so</c><00:08:07.639><c> we</c><00:08:07.759><c> are</c><00:08:07.960><c> basically</c>

00:08:08.469 --> 00:08:08.479 align:start position:0%
press releases so we are basically
 

00:08:08.479 --> 00:08:11.830 align:start position:0%
press releases so we are basically
refining<00:08:09.159><c> the</c><00:08:09.360><c> crawled</c><00:08:09.919><c> data</c><00:08:10.400><c> here</c><00:08:11.400><c> and</c><00:08:11.560><c> in</c>

00:08:11.830 --> 00:08:11.840 align:start position:0%
refining the crawled data here and in
 

00:08:11.840 --> 00:08:14.749 align:start position:0%
refining the crawled data here and in
step<00:08:12.240><c> three</c><00:08:12.840><c> we</c><00:08:13.039><c> eliminate</c><00:08:13.720><c> headlines</c><00:08:14.400><c> or</c>

00:08:14.749 --> 00:08:14.759 align:start position:0%
step three we eliminate headlines or
 

00:08:14.759 --> 00:08:17.350 align:start position:0%
step three we eliminate headlines or
articles<00:08:15.759><c> that</c><00:08:16.000><c> predominantly</c><00:08:16.840><c> discuss</c>

00:08:17.350 --> 00:08:17.360 align:start position:0%
articles that predominantly discuss
 

00:08:17.360 --> 00:08:20.629 align:start position:0%
articles that predominantly discuss
other<00:08:17.680><c> companies</c><00:08:18.520><c> or</c><00:08:19.159><c> entities</c><00:08:20.159><c> other</c><00:08:20.440><c> than</c>

00:08:20.629 --> 00:08:20.639 align:start position:0%
other companies or entities other than
 

00:08:20.639 --> 00:08:23.390 align:start position:0%
other companies or entities other than
the<00:08:20.800><c> target</c><00:08:21.440><c> company</c><00:08:22.440><c> like</c><00:08:22.680><c> suppose</c><00:08:23.120><c> if</c><00:08:23.280><c> we</c>

00:08:23.390 --> 00:08:23.400 align:start position:0%
the target company like suppose if we
 

00:08:23.400 --> 00:08:26.390 align:start position:0%
the target company like suppose if we
are<00:08:23.639><c> analyzing</c><00:08:24.240><c> S&amp;P</c><00:08:24.759><c> Global</c><00:08:25.639><c> then</c><00:08:25.759><c> our</c><00:08:26.039><c> focus</c>

00:08:26.390 --> 00:08:26.400 align:start position:0%
are analyzing S&amp;P Global then our focus
 

00:08:26.400 --> 00:08:29.149 align:start position:0%
are analyzing S&amp;P Global then our focus
should<00:08:26.840><c> primarily</c><00:08:27.440><c> be</c><00:08:27.639><c> on</c><00:08:27.960><c> text</c><00:08:28.360><c> around</c><00:08:28.759><c> S&amp;P</c>

00:08:29.149 --> 00:08:29.159 align:start position:0%
should primarily be on text around S&amp;P
 

00:08:29.159 --> 00:08:30.110 align:start position:0%
should primarily be on text around S&amp;P
global

00:08:30.110 --> 00:08:30.120 align:start position:0%
global
 

00:08:30.120 --> 00:08:32.630 align:start position:0%
global
and<00:08:30.319><c> not</c><00:08:30.560><c> on</c><00:08:30.800><c> other</c><00:08:31.080><c> entities</c><00:08:31.639><c> like</c><00:08:31.919><c> Microsoft</c>

00:08:32.630 --> 00:08:32.640 align:start position:0%
and not on other entities like Microsoft
 

00:08:32.640 --> 00:08:36.149 align:start position:0%
and not on other entities like Microsoft
Bloomberg<00:08:33.200><c> and</c><00:08:33.399><c> all</c><00:08:34.279><c> so</c><00:08:34.599><c> step</c><00:08:35.000><c> two</c><00:08:35.479><c> and</c><00:08:35.760><c> step</c>

00:08:36.149 --> 00:08:36.159 align:start position:0%
Bloomberg and all so step two and step
 

00:08:36.159 --> 00:08:38.389 align:start position:0%
Bloomberg and all so step two and step
three<00:08:36.599><c> here</c><00:08:36.919><c> are</c><00:08:37.200><c> ensuring</c><00:08:37.959><c> that</c><00:08:38.240><c> the</c>

00:08:38.389 --> 00:08:38.399 align:start position:0%
three here are ensuring that the
 

00:08:38.399 --> 00:08:41.509 align:start position:0%
three here are ensuring that the
analysis<00:08:39.159><c> remain</c><00:08:39.399><c> laser</c><00:08:40.080><c> focused</c><00:08:40.880><c> only</c><00:08:41.279><c> on</c>

00:08:41.509 --> 00:08:41.519 align:start position:0%
analysis remain laser focused only on
 

00:08:41.519 --> 00:08:44.230 align:start position:0%
analysis remain laser focused only on
the<00:08:41.800><c> relevant</c><00:08:42.399><c> content</c><00:08:42.880><c> of</c><00:08:43.080><c> the</c><00:08:43.479><c> target</c>

00:08:44.230 --> 00:08:44.240 align:start position:0%
the relevant content of the target
 

00:08:44.240 --> 00:08:46.990 align:start position:0%
the relevant content of the target
company<00:08:45.240><c> NeXT</c><00:08:45.560><c> we</c><00:08:45.680><c> have</c><00:08:45.839><c> a</c><00:08:46.000><c> dependency</c><00:08:46.600><c> parsal</c>

00:08:46.990 --> 00:08:47.000 align:start position:0%
company NeXT we have a dependency parsal
 

00:08:47.000 --> 00:08:49.990 align:start position:0%
company NeXT we have a dependency parsal
based<00:08:47.600><c> algorithm</c><00:08:48.600><c> in</c><00:08:48.839><c> this</c><00:08:49.080><c> step</c><00:08:49.519><c> we</c><00:08:49.680><c> are</c>

00:08:49.990 --> 00:08:50.000 align:start position:0%
based algorithm in this step we are
 

00:08:50.000 --> 00:08:52.150 align:start position:0%
based algorithm in this step we are
capturing<00:08:50.519><c> the</c><00:08:50.839><c> parts</c><00:08:51.200><c> of</c><00:08:51.360><c> the</c><00:08:51.560><c> text</c><00:08:51.880><c> or</c>

00:08:52.150 --> 00:08:52.160 align:start position:0%
capturing the parts of the text or
 

00:08:52.160 --> 00:08:55.070 align:start position:0%
capturing the parts of the text or
phrases<00:08:53.040><c> which</c><00:08:53.240><c> have</c><00:08:53.560><c> key</c><00:08:53.800><c> sentiment</c><00:08:54.279><c> bearing</c>

00:08:55.070 --> 00:08:55.080 align:start position:0%
phrases which have key sentiment bearing
 

00:08:55.080 --> 00:08:57.630 align:start position:0%
phrases which have key sentiment bearing
Expressions<00:08:56.080><c> so</c><00:08:56.320><c> at</c><00:08:56.560><c> first</c><00:08:57.040><c> we</c><00:08:57.160><c> are</c><00:08:57.360><c> looking</c>

00:08:57.630 --> 00:08:57.640 align:start position:0%
Expressions so at first we are looking
 

00:08:57.640 --> 00:08:59.630 align:start position:0%
Expressions so at first we are looking
at<00:08:57.880><c> the</c><00:08:58.040><c> grammatical</c><00:08:58.680><c> structure</c><00:08:59.120><c> of</c><00:08:59.279><c> this</c>

00:08:59.630 --> 00:08:59.640 align:start position:0%
at the grammatical structure of this
 

00:08:59.640 --> 00:09:01.870 align:start position:0%
at the grammatical structure of this
sentences<00:09:00.640><c> and</c><00:09:00.839><c> then</c><00:09:01.120><c> identifying</c>

00:09:01.870 --> 00:09:01.880 align:start position:0%
sentences and then identifying
 

00:09:01.880 --> 00:09:04.590 align:start position:0%
sentences and then identifying
relationship<00:09:02.600><c> between</c><00:09:02.959><c> different</c><00:09:03.600><c> words</c>

00:09:04.590 --> 00:09:04.600 align:start position:0%
relationship between different words
 

00:09:04.600 --> 00:09:07.389 align:start position:0%
relationship between different words
then<00:09:04.760><c> in</c><00:09:05.040><c> step</c><00:09:05.560><c> five</c><00:09:06.279><c> we</c><00:09:06.399><c> are</c><00:09:06.680><c> extracting</c>

00:09:07.389 --> 00:09:07.399 align:start position:0%
then in step five we are extracting
 

00:09:07.399 --> 00:09:10.509 align:start position:0%
then in step five we are extracting
sentiments<00:09:07.959><c> using</c><00:09:08.480><c> Financial</c><00:09:09.320><c> bird</c><00:09:10.320><c> the</c>

00:09:10.509 --> 00:09:10.519 align:start position:0%
sentiments using Financial bird the
 

00:09:10.519 --> 00:09:13.269 align:start position:0%
sentiments using Financial bird the
output<00:09:11.040><c> of</c><00:09:11.279><c> step</c><00:09:11.600><c> four</c><00:09:12.279><c> that</c><00:09:12.399><c> is</c><00:09:12.640><c> dependency</c>

00:09:13.269 --> 00:09:13.279 align:start position:0%
output of step four that is dependency
 

00:09:13.279 --> 00:09:16.350 align:start position:0%
output of step four that is dependency
paral<00:09:13.720><c> based</c><00:09:14.040><c> algorithm</c><00:09:14.800><c> is</c><00:09:15.120><c> now</c><00:09:15.640><c> fed</c><00:09:16.040><c> into</c>

00:09:16.350 --> 00:09:16.360 align:start position:0%
paral based algorithm is now fed into
 

00:09:16.360 --> 00:09:19.389 align:start position:0%
paral based algorithm is now fed into
Finn<00:09:16.760><c> bird</c><00:09:17.640><c> which</c><00:09:17.800><c> will</c><00:09:18.120><c> assign</c><00:09:18.760><c> sentiment</c>

00:09:19.389 --> 00:09:19.399 align:start position:0%
Finn bird which will assign sentiment
 

00:09:19.399 --> 00:09:23.150 align:start position:0%
Finn bird which will assign sentiment
scores<00:09:19.959><c> in</c><00:09:20.160><c> the</c><00:09:20.360><c> range</c><00:09:20.959><c> minus1</c><00:09:21.680><c> to</c><00:09:22.000><c> +</c><00:09:22.320><c> one</c><00:09:22.839><c> to</c>

00:09:23.150 --> 00:09:23.160 align:start position:0%
scores in the range minus1 to + one to
 

00:09:23.160 --> 00:09:26.030 align:start position:0%
scores in the range minus1 to + one to
each<00:09:23.560><c> of</c><00:09:23.760><c> the</c><00:09:24.240><c> inputs</c><00:09:25.240><c> where</c><00:09:25.440><c> minus</c><00:09:25.800><c> one</c>

00:09:26.030 --> 00:09:26.040 align:start position:0%
each of the inputs where minus one
 

00:09:26.040 --> 00:09:28.829 align:start position:0%
each of the inputs where minus one
indicates<00:09:26.760><c> highly</c><00:09:27.120><c> negative</c><00:09:27.640><c> sentiment</c><00:09:28.640><c> plus</c>

00:09:28.829 --> 00:09:28.839 align:start position:0%
indicates highly negative sentiment plus
 

00:09:28.839 --> 00:09:30.590 align:start position:0%
indicates highly negative sentiment plus
one<00:09:29.040><c> indicates</c><00:09:29.360><c> creates</c><00:09:29.720><c> highly</c><00:09:30.120><c> positive</c>

00:09:30.590 --> 00:09:30.600 align:start position:0%
one indicates creates highly positive
 

00:09:30.600 --> 00:09:34.710 align:start position:0%
one indicates creates highly positive
sentiment<00:09:31.600><c> and</c><00:09:31.800><c> scores</c><00:09:32.320><c> around</c><00:09:32.800><c> zero</c><00:09:33.320><c> means</c>

00:09:34.710 --> 00:09:34.720 align:start position:0%
sentiment and scores around zero means
 

00:09:34.720 --> 00:09:38.230 align:start position:0%
sentiment and scores around zero means
neutrals<00:09:35.720><c> final</c><00:09:36.079><c> step</c><00:09:36.440><c> is</c><00:09:36.800><c> sentiment</c><00:09:37.440><c> score</c>

00:09:38.230 --> 00:09:38.240 align:start position:0%
neutrals final step is sentiment score
 

00:09:38.240 --> 00:09:41.190 align:start position:0%
neutrals final step is sentiment score
aggregation<00:09:39.240><c> finally</c><00:09:39.680><c> the</c><00:09:39.839><c> sentiment</c><00:09:40.440><c> scores</c>

00:09:41.190 --> 00:09:41.200 align:start position:0%
aggregation finally the sentiment scores
 

00:09:41.200 --> 00:09:43.790 align:start position:0%
aggregation finally the sentiment scores
assigned<00:09:41.640><c> to</c><00:09:41.920><c> each</c><00:09:42.200><c> selected</c><00:09:42.760><c> piece</c><00:09:43.000><c> of</c><00:09:43.279><c> text</c>

00:09:43.790 --> 00:09:43.800 align:start position:0%
assigned to each selected piece of text
 

00:09:43.800 --> 00:09:45.910 align:start position:0%
assigned to each selected piece of text
across<00:09:44.320><c> all</c><00:09:44.640><c> articles</c><00:09:45.079><c> or</c><00:09:45.360><c> headlines</c>

00:09:45.910 --> 00:09:45.920 align:start position:0%
across all articles or headlines
 

00:09:45.920 --> 00:09:47.990 align:start position:0%
across all articles or headlines
associated<00:09:46.480><c> with</c><00:09:46.640><c> a</c><00:09:46.839><c> given</c><00:09:47.200><c> company</c><00:09:47.600><c> are</c>

00:09:47.990 --> 00:09:48.000 align:start position:0%
associated with a given company are
 

00:09:48.000 --> 00:09:50.750 align:start position:0%
associated with a given company are
aggregated<00:09:48.800><c> here</c><00:09:49.680><c> and</c><00:09:49.880><c> this</c><00:09:50.120><c> aggregation</c>

00:09:50.750 --> 00:09:50.760 align:start position:0%
aggregated here and this aggregation
 

00:09:50.760 --> 00:09:53.389 align:start position:0%
aggregated here and this aggregation
step<00:09:51.079><c> will</c><00:09:51.279><c> yield</c><00:09:51.720><c> us</c><00:09:52.079><c> an</c><00:09:52.320><c> overall</c><00:09:52.800><c> sentiment</c>

00:09:53.389 --> 00:09:53.399 align:start position:0%
step will yield us an overall sentiment
 

00:09:53.399 --> 00:09:59.470 align:start position:0%
step will yield us an overall sentiment
score<00:09:53.839><c> for</c><00:09:54.040><c> a</c><00:09:54.279><c> given</c><00:09:54.680><c> Target</c><00:09:55.320><c> company</c>

00:09:59.470 --> 00:09:59.480 align:start position:0%
 
 

00:09:59.480 --> 00:10:02.470 align:start position:0%
 
now<00:09:59.760><c> extracting</c><00:10:00.600><c> Target</c><00:10:01.120><c> company</c><00:10:01.519><c> name</c><00:10:02.000><c> from</c>

00:10:02.470 --> 00:10:02.480 align:start position:0%
now extracting Target company name from
 

00:10:02.480 --> 00:10:05.670 align:start position:0%
now extracting Target company name from
text<00:10:03.120><c> is</c><00:10:03.360><c> itself</c><00:10:03.760><c> a</c><00:10:03.920><c> challenging</c><00:10:04.560><c> task</c><00:10:05.320><c> as</c><00:10:05.480><c> a</c>

00:10:05.670 --> 00:10:05.680 align:start position:0%
text is itself a challenging task as a
 

00:10:05.680 --> 00:10:07.590 align:start position:0%
text is itself a challenging task as a
given<00:10:06.000><c> company</c><00:10:06.360><c> name</c><00:10:06.640><c> may</c><00:10:06.800><c> be</c><00:10:06.959><c> referred</c><00:10:07.399><c> by</c>

00:10:07.590 --> 00:10:07.600 align:start position:0%
given company name may be referred by
 

00:10:07.600 --> 00:10:10.110 align:start position:0%
given company name may be referred by
different<00:10:08.040><c> names</c><00:10:08.640><c> or</c><00:10:08.839><c> maybe</c><00:10:09.279><c> abbreviations</c>

00:10:10.110 --> 00:10:10.120 align:start position:0%
different names or maybe abbreviations
 

00:10:10.120 --> 00:10:13.630 align:start position:0%
different names or maybe abbreviations
also<00:10:11.120><c> we</c><00:10:11.279><c> use</c><00:10:11.640><c> techniques</c><00:10:12.200><c> like</c><00:10:12.440><c> tfidf</c><00:10:13.440><c> and</c>

00:10:13.630 --> 00:10:13.640 align:start position:0%
also we use techniques like tfidf and
 

00:10:13.640 --> 00:10:16.150 align:start position:0%
also we use techniques like tfidf and
fuzzy<00:10:14.040><c> matching</c><00:10:14.480><c> to</c><00:10:14.800><c> accurately</c><00:10:15.360><c> identify</c>

00:10:16.150 --> 00:10:16.160 align:start position:0%
fuzzy matching to accurately identify
 

00:10:16.160 --> 00:10:19.509 align:start position:0%
fuzzy matching to accurately identify
all<00:10:16.480><c> such</c><00:10:17.040><c> references</c><00:10:18.040><c> for</c><00:10:18.320><c> example</c><00:10:19.320><c> a</c>

00:10:19.509 --> 00:10:19.519 align:start position:0%
all such references for example a
 

00:10:19.519 --> 00:10:22.630 align:start position:0%
all such references for example a
company<00:10:19.959><c> might</c><00:10:20.200><c> be</c><00:10:20.399><c> referred</c><00:10:20.720><c> to</c><00:10:20.959><c> as</c><00:10:21.399><c> 123</c><00:10:21.880><c> net</c>

00:10:22.630 --> 00:10:22.640 align:start position:0%
company might be referred to as 123 net
 

00:10:22.640 --> 00:10:26.389 align:start position:0%
company might be referred to as 123 net
123<00:10:23.519><c> or</c><00:10:23.839><c> one</c><00:10:24.040><c> 123</c><00:10:24.480><c> Network</c><00:10:25.399><c> and</c><00:10:25.560><c> a</c><00:10:25.760><c> system</c><00:10:26.120><c> can</c>

00:10:26.389 --> 00:10:26.399 align:start position:0%
123 or one 123 Network and a system can
 

00:10:26.399 --> 00:10:28.910 align:start position:0%
123 or one 123 Network and a system can
recognize<00:10:27.320><c> all</c><00:10:27.640><c> these</c><00:10:27.920><c> variations</c><00:10:28.600><c> as</c><00:10:28.800><c> the</c>

00:10:28.910 --> 00:10:28.920 align:start position:0%
recognize all these variations as the
 

00:10:28.920 --> 00:10:32.389 align:start position:0%
recognize all these variations as the
same<00:10:29.399><c> entity</c><00:10:30.399><c> for</c><00:10:30.720><c> achieving</c><00:10:31.320><c> this</c><00:10:31.920><c> at</c><00:10:32.120><c> first</c>

00:10:32.389 --> 00:10:32.399 align:start position:0%
same entity for achieving this at first
 

00:10:32.399 --> 00:10:35.310 align:start position:0%
same entity for achieving this at first
we<00:10:32.560><c> are</c><00:10:32.720><c> grouping</c><00:10:33.160><c> text</c><00:10:33.519><c> by</c><00:10:33.800><c> company</c><00:10:34.320><c> name</c>

00:10:35.310 --> 00:10:35.320 align:start position:0%
we are grouping text by company name
 

00:10:35.320 --> 00:10:38.230 align:start position:0%
we are grouping text by company name
then<00:10:35.560><c> creating</c><00:10:36.079><c> engrams</c><00:10:36.760><c> for</c><00:10:36.959><c> each</c><00:10:37.320><c> company</c>

00:10:38.230 --> 00:10:38.240 align:start position:0%
then creating engrams for each company
 

00:10:38.240 --> 00:10:40.910 align:start position:0%
then creating engrams for each company
then<00:10:38.480><c> Computing</c><00:10:39.040><c> tfidf</c><00:10:39.959><c> scores</c><00:10:40.440><c> of</c><00:10:40.639><c> these</c>

00:10:40.910 --> 00:10:40.920 align:start position:0%
then Computing tfidf scores of these
 

00:10:40.920 --> 00:10:43.310 align:start position:0%
then Computing tfidf scores of these
engrams<00:10:41.920><c> then</c><00:10:42.120><c> we</c><00:10:42.240><c> are</c><00:10:42.440><c> sorting</c><00:10:43.040><c> and</c>

00:10:43.310 --> 00:10:43.320 align:start position:0%
engrams then we are sorting and
 

00:10:43.320 --> 00:10:45.670 align:start position:0%
engrams then we are sorting and
selecting<00:10:43.839><c> the</c><00:10:44.079><c> top</c><00:10:44.440><c> features</c><00:10:45.279><c> based</c><00:10:45.560><c> on</c>

00:10:45.670 --> 00:10:45.680 align:start position:0%
selecting the top features based on
 

00:10:45.680 --> 00:10:49.550 align:start position:0%
selecting the top features based on
tfidf<00:10:46.880><c> scores</c><00:10:47.880><c> parallely</c><00:10:48.560><c> we</c><00:10:48.680><c> are</c><00:10:48.880><c> extracting</c>

00:10:49.550 --> 00:10:49.560 align:start position:0%
tfidf scores parallely we are extracting
 

00:10:49.560 --> 00:10:52.030 align:start position:0%
tfidf scores parallely we are extracting
company<00:10:49.920><c> names</c><00:10:50.279><c> from</c><00:10:50.560><c> domain</c><00:10:50.959><c> names</c><00:10:51.760><c> and</c>

00:10:52.030 --> 00:10:52.040 align:start position:0%
company names from domain names and
 

00:10:52.040 --> 00:10:54.310 align:start position:0%
company names from domain names and
finally<00:10:52.639><c> we</c><00:10:52.760><c> are</c><00:10:52.920><c> running</c><00:10:53.360><c> the</c><00:10:53.519><c> fuzzy</c><00:10:53.920><c> search</c>

00:10:54.310 --> 00:10:54.320 align:start position:0%
finally we are running the fuzzy search
 

00:10:54.320 --> 00:10:57.829 align:start position:0%
finally we are running the fuzzy search
between<00:10:54.880><c> these</c><00:10:55.560><c> and</c><00:10:55.839><c> top</c><00:10:56.160><c> igf</c><00:10:56.680><c> features</c><00:10:57.680><c> which</c>

00:10:57.829 --> 00:10:57.839 align:start position:0%
between these and top igf features which
 

00:10:57.839 --> 00:11:01.069 align:start position:0%
between these and top igf features which
will<00:10:58.120><c> give</c><00:10:58.360><c> us</c><00:10:58.600><c> the</c><00:10:58.839><c> final</c><00:10:59.519><c> set</c><00:10:59.959><c> of</c><00:11:00.519><c> all</c><00:11:00.839><c> the</c>

00:11:01.069 --> 00:11:01.079 align:start position:0%
will give us the final set of all the
 

00:11:01.079 --> 00:11:04.629 align:start position:0%
will give us the final set of all the
names<00:11:01.519><c> used</c><00:11:01.880><c> for</c><00:11:02.079><c> a</c><00:11:02.320><c> given</c>

00:11:04.629 --> 00:11:04.639 align:start position:0%
names used for a given
 

00:11:04.639 --> 00:11:07.069 align:start position:0%
names used for a given
company<00:11:05.639><c> now</c><00:11:05.839><c> let's</c><00:11:06.079><c> look</c><00:11:06.240><c> at</c><00:11:06.360><c> a</c><00:11:06.519><c> real</c><00:11:06.839><c> time</c>

00:11:07.069 --> 00:11:07.079 align:start position:0%
company now let's look at a real time
 

00:11:07.079 --> 00:11:11.110 align:start position:0%
company now let's look at a real time
example<00:11:07.760><c> with</c><00:11:07.920><c> a</c><00:11:08.160><c> company</c><00:11:08.760><c> named</c><00:11:09.480><c> 123</c><00:11:09.959><c> net</c><00:11:10.920><c> so</c>

00:11:11.110 --> 00:11:11.120 align:start position:0%
example with a company named 123 net so
 

00:11:11.120 --> 00:11:14.269 align:start position:0%
example with a company named 123 net so
this<00:11:11.240><c> is</c><00:11:11.440><c> news</c><00:11:11.880><c> page</c><00:11:12.120><c> of</c><00:11:12.519><c> 123</c>

00:11:14.269 --> 00:11:14.279 align:start position:0%
this is news page of 123
 

00:11:14.279 --> 00:11:18.030 align:start position:0%
this is news page of 123
net<00:11:15.279><c> now</c><00:11:15.680><c> on</c><00:11:16.000><c> left</c><00:11:16.360><c> side</c><00:11:16.839><c> we</c><00:11:17.040><c> have</c><00:11:17.240><c> the</c><00:11:17.440><c> crawled</c>

00:11:18.030 --> 00:11:18.040 align:start position:0%
net now on left side we have the crawled
 

00:11:18.040 --> 00:11:21.389 align:start position:0%
net now on left side we have the crawled
text<00:11:18.920><c> and</c><00:11:19.079><c> on</c><00:11:19.440><c> right</c><00:11:20.120><c> we</c><00:11:20.240><c> have</c><00:11:20.480><c> filtered</c><00:11:21.160><c> that</c>

00:11:21.389 --> 00:11:21.399 align:start position:0%
text and on right we have filtered that
 

00:11:21.399 --> 00:11:23.710 align:start position:0%
text and on right we have filtered that
crawled<00:11:21.880><c> text</c><00:11:22.399><c> by</c><00:11:22.600><c> removing</c><00:11:23.000><c> redundent</c>

00:11:23.710 --> 00:11:23.720 align:start position:0%
crawled text by removing redundent
 

00:11:23.720 --> 00:11:26.190 align:start position:0%
crawled text by removing redundent
headlines<00:11:24.320><c> and</c><00:11:24.600><c> articles</c><00:11:25.600><c> that</c><00:11:25.800><c> do</c><00:11:25.959><c> not</c>

00:11:26.190 --> 00:11:26.200 align:start position:0%
headlines and articles that do not
 

00:11:26.200 --> 00:11:28.590 align:start position:0%
headlines and articles that do not
discuss<00:11:26.600><c> one</c><00:11:26.800><c> two</c><00:11:27.000><c> 3</c><00:11:27.240><c> net</c><00:11:27.639><c> that</c><00:11:27.760><c> is</c><00:11:27.839><c> a</c><00:11:28.160><c> target</c>

00:11:28.590 --> 00:11:28.600 align:start position:0%
discuss one two 3 net that is a target
 

00:11:28.600 --> 00:11:30.949 align:start position:0%
discuss one two 3 net that is a target
company<00:11:29.040><c> here</c>

00:11:30.949 --> 00:11:30.959 align:start position:0%
company here
 

00:11:30.959 --> 00:11:33.990 align:start position:0%
company here
now<00:11:31.240><c> from</c><00:11:31.519><c> Filter</c><00:11:32.160><c> text</c><00:11:32.800><c> we'll</c><00:11:33.120><c> be</c><00:11:33.360><c> resolving</c>

00:11:33.990 --> 00:11:34.000 align:start position:0%
now from Filter text we'll be resolving
 

00:11:34.000 --> 00:11:36.790 align:start position:0%
now from Filter text we'll be resolving
cor<00:11:34.279><c> references</c>

00:11:36.790 --> 00:11:36.800 align:start position:0%
cor references
 

00:11:36.800 --> 00:11:39.629 align:start position:0%
cor references
here<00:11:37.800><c> now</c><00:11:38.360><c> after</c><00:11:38.680><c> resolving</c><00:11:39.200><c> the</c><00:11:39.360><c> cor</c>

00:11:39.629 --> 00:11:39.639 align:start position:0%
here now after resolving the cor
 

00:11:39.639 --> 00:11:42.310 align:start position:0%
here now after resolving the cor
references<00:11:40.639><c> we</c><00:11:40.839><c> do</c><00:11:41.200><c> targeted</c><00:11:41.800><c> text</c>

00:11:42.310 --> 00:11:42.320 align:start position:0%
references we do targeted text
 

00:11:42.320 --> 00:11:45.310 align:start position:0%
references we do targeted text
extraction<00:11:43.320><c> so</c><00:11:43.680><c> here</c><00:11:44.040><c> we</c><00:11:44.160><c> are</c><00:11:44.399><c> basically</c>

00:11:45.310 --> 00:11:45.320 align:start position:0%
extraction so here we are basically
 

00:11:45.320 --> 00:11:47.550 align:start position:0%
extraction so here we are basically
bucketing<00:11:45.880><c> text</c><00:11:46.200><c> around</c><00:11:46.600><c> Target</c><00:11:47.040><c> company</c>

00:11:47.550 --> 00:11:47.560 align:start position:0%
bucketing text around Target company
 

00:11:47.560 --> 00:11:50.069 align:start position:0%
bucketing text around Target company
which<00:11:47.720><c> is</c><00:11:47.880><c> one</c><00:11:48.079><c> to</c><00:11:48.320><c> three</c><00:11:48.560><c> Net</c><00:11:49.240><c> and</c><00:11:49.480><c> removing</c>

00:11:50.069 --> 00:11:50.079 align:start position:0%
which is one to three Net and removing
 

00:11:50.079 --> 00:11:54.629 align:start position:0%
which is one to three Net and removing
content<00:11:50.680><c> discussing</c><00:11:51.279><c> other</c>

00:11:54.629 --> 00:11:54.639 align:start position:0%
 
 

00:11:54.639 --> 00:11:57.389 align:start position:0%
 
companies<00:11:55.639><c> and</c><00:11:56.040><c> here</c><00:11:56.480><c> we</c><00:11:56.760><c> apply</c><00:11:57.160><c> the</c>

00:11:57.389 --> 00:11:57.399 align:start position:0%
companies and here we apply the
 

00:11:57.399 --> 00:12:00.430 align:start position:0%
companies and here we apply the
Transformer<00:11:58.279><c> model</c><00:11:58.720><c> to</c><00:11:58.880><c> the</c><00:11:59.240><c> filter</c><00:11:59.720><c> text</c><00:12:00.200><c> to</c>

00:12:00.430 --> 00:12:00.440 align:start position:0%
Transformer model to the filter text to
 

00:12:00.440 --> 00:12:02.910 align:start position:0%
Transformer model to the filter text to
determine<00:12:00.920><c> the</c><00:12:01.079><c> sentiment</c><00:12:01.639><c> score</c><00:12:02.519><c> and</c><00:12:02.680><c> to</c>

00:12:02.910 --> 00:12:02.920 align:start position:0%
determine the sentiment score and to
 

00:12:02.920 --> 00:12:05.190 align:start position:0%
determine the sentiment score and to
compute<00:12:03.480><c> that</c><00:12:03.839><c> we</c><00:12:03.959><c> are</c><00:12:04.160><c> passing</c><00:12:04.720><c> all</c><00:12:05.000><c> the</c>

00:12:05.190 --> 00:12:05.200 align:start position:0%
compute that we are passing all the
 

00:12:05.200 --> 00:12:07.949 align:start position:0%
compute that we are passing all the
relevant<00:12:05.720><c> phrases</c><00:12:06.399><c> and</c><00:12:06.639><c> generic</c><00:12:07.200><c> articles</c><00:12:07.680><c> to</c>

00:12:07.949 --> 00:12:07.959 align:start position:0%
relevant phrases and generic articles to
 

00:12:07.959 --> 00:12:10.750 align:start position:0%
relevant phrases and generic articles to
finir<00:12:08.720><c> that</c><00:12:08.839><c> is</c><00:12:09.040><c> financial</c><00:12:09.600><c> bir</c><00:12:10.440><c> and</c><00:12:10.600><c> the</c>

00:12:10.750 --> 00:12:10.760 align:start position:0%
finir that is financial bir and the
 

00:12:10.760 --> 00:12:13.389 align:start position:0%
finir that is financial bir and the
final<00:12:11.079><c> sentiment</c><00:12:11.639><c> score</c><00:12:11.959><c> of</c><00:12:12.079><c> a</c><00:12:12.320><c> company</c><00:12:12.880><c> is</c>

00:12:13.389 --> 00:12:13.399 align:start position:0%
final sentiment score of a company is
 

00:12:13.399 --> 00:12:15.790 align:start position:0%
final sentiment score of a company is
average<00:12:13.800><c> score</c><00:12:14.079><c> of</c><00:12:14.360><c> all</c><00:12:14.639><c> these</c><00:12:14.839><c> articles</c><00:12:15.279><c> and</c>

00:12:15.790 --> 00:12:15.800 align:start position:0%
average score of all these articles and
 

00:12:15.800 --> 00:12:18.870 align:start position:0%
average score of all these articles and
phrases<00:12:16.800><c> so</c><00:12:17.000><c> in</c><00:12:17.160><c> summary</c><00:12:17.839><c> we</c><00:12:18.000><c> have</c><00:12:18.320><c> developed</c>

00:12:18.870 --> 00:12:18.880 align:start position:0%
phrases so in summary we have developed
 

00:12:18.880 --> 00:12:21.629 align:start position:0%
phrases so in summary we have developed
a<00:12:19.079><c> robust</c><00:12:19.519><c> sentiment</c><00:12:20.040><c> analysis</c><00:12:20.639><c> framework</c>

00:12:21.629 --> 00:12:21.639 align:start position:0%
a robust sentiment analysis framework
 

00:12:21.639 --> 00:12:24.750 align:start position:0%
a robust sentiment analysis framework
combining<00:12:22.519><c> traditional</c><00:12:23.399><c> and</c><00:12:23.680><c> modern</c><00:12:24.160><c> NLP</c>

00:12:24.750 --> 00:12:24.760 align:start position:0%
combining traditional and modern NLP
 

00:12:24.760 --> 00:12:26.750 align:start position:0%
combining traditional and modern NLP
techniques<00:12:25.600><c> to</c><00:12:25.839><c> analyze</c><00:12:26.440><c> company</c>

00:12:26.750 --> 00:12:26.760 align:start position:0%
techniques to analyze company
 

00:12:26.760 --> 00:12:29.269 align:start position:0%
techniques to analyze company
self-disclosures<00:12:27.560><c> and</c><00:12:28.519><c> we</c><00:12:28.680><c> believe</c><00:12:29.040><c> that</c>

00:12:29.269 --> 00:12:29.279 align:start position:0%
self-disclosures and we believe that
 

00:12:29.279 --> 00:12:31.350 align:start position:0%
self-disclosures and we believe that
that<00:12:29.519><c> this</c><00:12:29.680><c> methodology</c><00:12:30.320><c> can</c><00:12:30.600><c> significantly</c>

00:12:31.350 --> 00:12:31.360 align:start position:0%
that this methodology can significantly
 

00:12:31.360 --> 00:12:33.389 align:start position:0%
that this methodology can significantly
improve<00:12:31.839><c> risk</c><00:12:32.160><c> assessment</c><00:12:32.760><c> and</c><00:12:33.000><c> decision</c>

00:12:33.389 --> 00:12:33.399 align:start position:0%
improve risk assessment and decision
 

00:12:33.399 --> 00:12:37.790 align:start position:0%
improve risk assessment and decision
making<00:12:34.040><c> processes</c><00:12:35.040><c> thank</c><00:12:35.760><c> you</c><00:12:36.760><c> this</c><00:12:36.959><c> was</c><00:12:37.360><c> the</c>

00:12:37.790 --> 00:12:37.800 align:start position:0%
making processes thank you this was the
 

00:12:37.800 --> 00:12:40.430 align:start position:0%
making processes thank you this was the
final<00:12:38.240><c> session</c><00:12:38.720><c> of</c><00:12:38.880><c> the</c><00:12:39.040><c> fifth</c><00:12:39.440><c> annual</c><00:12:39.839><c> NLP</c>

00:12:40.430 --> 00:12:40.440 align:start position:0%
final session of the fifth annual NLP
 

00:12:40.440 --> 00:12:43.030 align:start position:0%
final session of the fifth annual NLP
Summit<00:12:41.279><c> thank</c><00:12:41.480><c> you</c><00:12:41.639><c> all</c><00:12:42.000><c> very</c><00:12:42.279><c> much</c><00:12:42.760><c> for</c>

00:12:43.030 --> 00:12:43.040 align:start position:0%
Summit thank you all very much for
 

00:12:43.040 --> 00:12:45.310 align:start position:0%
Summit thank you all very much for
joining<00:12:43.560><c> this</c><00:12:43.760><c> three-day</c><00:12:44.360><c> Community</c><00:12:44.839><c> event</c>

00:12:45.310 --> 00:12:45.320 align:start position:0%
joining this three-day Community event
 

00:12:45.320 --> 00:12:47.110 align:start position:0%
joining this three-day Community event
and<00:12:45.440><c> for</c><00:12:45.720><c> contributing</c><00:12:46.519><c> with</c><00:12:46.760><c> great</c>

00:12:47.110 --> 00:12:47.120 align:start position:0%
and for contributing with great
 

00:12:47.120 --> 00:12:49.189 align:start position:0%
and for contributing with great
questions<00:12:47.720><c> and</c><00:12:47.880><c> your</c><00:12:48.079><c> own</c>

00:12:49.189 --> 00:12:49.199 align:start position:0%
questions and your own
 

00:12:49.199 --> 00:12:51.509 align:start position:0%
questions and your own
experiences<00:12:50.199><c> during</c><00:12:50.560><c> this</c><00:12:50.760><c> Summit</c><00:12:51.279><c> we</c>

00:12:51.509 --> 00:12:51.519 align:start position:0%
experiences during this Summit we
 

00:12:51.519 --> 00:12:53.910 align:start position:0%
experiences during this Summit we
watched<00:12:51.959><c> over</c><00:12:52.279><c> 50</c><00:12:52.720><c> sessions</c><00:12:53.320><c> that</c><00:12:53.480><c> spend</c>

00:12:53.910 --> 00:12:53.920 align:start position:0%
watched over 50 sessions that spend
 

00:12:53.920 --> 00:12:58.230 align:start position:0%
watched over 50 sessions that spend
topics<00:12:54.519><c> from</c><00:12:55.519><c> open</c><00:12:55.839><c> source</c><00:12:56.639><c> Genera</c><00:12:57.639><c> to</c><00:12:57.959><c> real</c>

00:12:58.230 --> 00:12:58.240 align:start position:0%
topics from open source Genera to real
 

00:12:58.240 --> 00:12:59.670 align:start position:0%
topics from open source Genera to real
world<00:12:58.720><c> production</c>

00:12:59.670 --> 00:12:59.680 align:start position:0%
world production
 

00:12:59.680 --> 00:13:02.509 align:start position:0%
world production
applications<00:13:00.680><c> in</c><00:13:00.920><c> Industries</c><00:13:01.680><c> like</c>

00:13:02.509 --> 00:13:02.519 align:start position:0%
applications in Industries like
 

00:13:02.519 --> 00:13:05.750 align:start position:0%
applications in Industries like
healthcare<00:13:03.600><c> Finance</c><00:13:04.680><c> recruiting</c>

00:13:05.750 --> 00:13:05.760 align:start position:0%
healthcare Finance recruiting
 

00:13:05.760 --> 00:13:08.629 align:start position:0%
healthcare Finance recruiting
manufacturing<00:13:06.760><c> and</c><00:13:07.320><c> insurance</c><00:13:08.320><c> our</c>

00:13:08.629 --> 00:13:08.639 align:start position:0%
manufacturing and insurance our
 

00:13:08.639 --> 00:13:10.590 align:start position:0%
manufacturing and insurance our
discussions<00:13:09.199><c> have</c><00:13:09.360><c> been</c><00:13:09.639><c> Guided</c><00:13:10.120><c> by</c><00:13:10.360><c> this</c>

00:13:10.590 --> 00:13:10.600 align:start position:0%
discussions have been Guided by this
 

00:13:10.600 --> 00:13:14.030 align:start position:0%
discussions have been Guided by this
year's<00:13:10.880><c> team</c><00:13:11.279><c> putting</c><00:13:11.680><c> Genera</c><00:13:12.519><c> to</c><00:13:13.040><c> work</c>

00:13:14.030 --> 00:13:14.040 align:start position:0%
year's team putting Genera to work
 

00:13:14.040 --> 00:13:16.670 align:start position:0%
year's team putting Genera to work
highlighting<00:13:14.639><c> both</c><00:13:14.959><c> the</c><00:13:15.360><c> potential</c><00:13:16.000><c> and</c><00:13:16.199><c> the</c>

00:13:16.670 --> 00:13:16.680 align:start position:0%
highlighting both the potential and the
 

00:13:16.680 --> 00:13:19.189 align:start position:0%
highlighting both the potential and the
challenges<00:13:17.680><c> when</c><00:13:18.120><c> get</c><00:13:18.600><c> getting</c><00:13:18.959><c> those</c>

00:13:19.189 --> 00:13:19.199 align:start position:0%
challenges when get getting those
 

00:13:19.199 --> 00:13:22.990 align:start position:0%
challenges when get getting those
systems<00:13:19.800><c> from</c><00:13:20.360><c> a</c><00:13:20.560><c> great</c><00:13:20.880><c> idea</c><00:13:21.440><c> to</c><00:13:21.720><c> a</c><00:13:22.120><c> reliable</c>

00:13:22.990 --> 00:13:23.000 align:start position:0%
systems from a great idea to a reliable
 

00:13:23.000 --> 00:13:25.310 align:start position:0%
systems from a great idea to a reliable
production<00:13:24.000><c> Software</c>

00:13:25.310 --> 00:13:25.320 align:start position:0%
production Software
 

00:13:25.320 --> 00:13:27.990 align:start position:0%
production Software
System<00:13:26.320><c> for</c><00:13:26.600><c> those</c><00:13:26.880><c> wishing</c><00:13:27.160><c> to</c><00:13:27.440><c> revisit</c>

00:13:27.990 --> 00:13:28.000 align:start position:0%
System for those wishing to revisit
 

00:13:28.000 --> 00:13:29.629 align:start position:0%
System for those wishing to revisit
sessions<00:13:28.399><c> or</c><00:13:28.639><c> catch</c><00:13:28.839><c> up</c><00:13:29.120><c> on</c><00:13:29.240><c> the</c><00:13:29.320><c> ones</c><00:13:29.519><c> you</c>

00:13:29.629 --> 00:13:29.639 align:start position:0%
sessions or catch up on the ones you
 

00:13:29.639 --> 00:13:32.189 align:start position:0%
sessions or catch up on the ones you
missed<00:13:30.440><c> all</c><00:13:30.800><c> content</c><00:13:31.199><c> will</c><00:13:31.360><c> be</c><00:13:31.600><c> available</c><00:13:32.040><c> on</c>

00:13:32.189 --> 00:13:32.199 align:start position:0%
missed all content will be available on
 

00:13:32.199 --> 00:13:36.949 align:start position:0%
missed all content will be available on
this<00:13:32.440><c> platform</c><00:13:32.880><c> on</c><00:13:33.079><c> demand</c><00:13:33.440><c> for</c><00:13:33.639><c> the</c><00:13:33.839><c> next</c><00:13:34.320><c> two</c>

00:13:36.949 --> 00:13:36.959 align:start position:0%
 
 

00:13:36.959 --> 00:13:39.790 align:start position:0%
 
weeks<00:13:37.959><c> a</c><00:13:38.160><c> final</c><00:13:38.519><c> note</c><00:13:38.760><c> of</c><00:13:38.920><c> gratitude</c><00:13:39.399><c> to</c><00:13:39.600><c> our</c>

00:13:39.790 --> 00:13:39.800 align:start position:0%
weeks a final note of gratitude to our
 

00:13:39.800 --> 00:13:42.470 align:start position:0%
weeks a final note of gratitude to our
amazing<00:13:40.199><c> speakers</c><00:13:40.720><c> and</c><00:13:40.880><c> to</c><00:13:41.040><c> you</c><00:13:41.360><c> our</c><00:13:41.560><c> audience</c>

00:13:42.470 --> 00:13:42.480 align:start position:0%
amazing speakers and to you our audience
 

00:13:42.480 --> 00:13:44.870 align:start position:0%
amazing speakers and to you our audience
for<00:13:42.760><c> making</c><00:13:43.199><c> this</c><00:13:43.480><c> one</c><00:13:43.639><c> of</c><00:13:43.839><c> the</c><00:13:44.000><c> most</c>

00:13:44.870 --> 00:13:44.880 align:start position:0%
for making this one of the most
 

00:13:44.880 --> 00:13:48.550 align:start position:0%
for making this one of the most
engaging<00:13:45.880><c> AI</c><00:13:46.360><c> community</c><00:13:47.000><c> events</c><00:13:48.000><c> please</c><00:13:48.279><c> stay</c>

00:13:48.550 --> 00:13:48.560 align:start position:0%
engaging AI community events please stay
 

00:13:48.560 --> 00:13:50.670 align:start position:0%
engaging AI community events please stay
connected<00:13:49.120><c> throughout</c><00:13:49.519><c> the</c><00:13:49.639><c> year</c><00:13:50.120><c> and</c><00:13:50.360><c> have</c><00:13:50.519><c> a</c>

00:13:50.670 --> 00:13:50.680 align:start position:0%
connected throughout the year and have a
 

00:13:50.680 --> 00:13:54.519 align:start position:0%
connected throughout the year and have a
wonderful<00:13:51.240><c> week</c><00:13:51.519><c> ahead</c>

