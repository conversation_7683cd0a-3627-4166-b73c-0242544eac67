import asyncio
import aiohttp
import logging
from datetime import datetime, timezone
import json
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

# Configure logging with Rich handler
console = Console()
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    handlers=[<PERSON><PERSON><PERSON><PERSON>(console=console, show_path=False)]
)

# Test data
TEST_PROMPT = """Analyze this text and return ONLY a JSON object with exactly this structure:
{
    "keywords": ["word1", "word2", "word3"],
    "questions": ["Question 1?", "Question 2?"],
    "insights": ["Insight 1", "Insight 2"],
    "recommendations": ["Recommendation 1", "Recommendation 2"]
}

Here is the text to analyze:
Cats are fascinating animals that make great pets.
They sleep for about 16 hours a day.
Cats have excellent night vision and can see in one-sixth the light that humans need.
They are independent but also very affectionate with their owners."""

async def query_ollama(prompt, model="gemma3:4b"):
    """Query Ollama API and ensure valid JSON response"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.1,
                        "num_predict": 2000,
                        "top_k": 10,
                        "top_p": 0.9
                    }
                },
                timeout=300
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    response_text = result.get('response', '')
                    
                    # Clean up response text to find valid JSON
                    try:
                        # Find first { and last }
                        start = response_text.find('{')
                        end = response_text.rfind('}') + 1
                        if start >= 0 and end > start:
                            json_str = response_text[start:end]
                            # Validate JSON by parsing it
                            return json.loads(json_str)
                        else:
                            raise ValueError("No JSON object found in response")
                    except (json.JSONDecodeError, ValueError) as e:
                        console.print(f"[red]Raw response: {response_text}[/red]")
                        raise Exception(f"Failed to parse JSON: {str(e)}")
                else:
                    raise Exception(f"Ollama API error {response.status}: {await response.text()}")
    except Exception as e:
        logging.error(f"Error querying Ollama: {str(e)}")
        raise

async def test_ollama_api():
    """Run basic test of Ollama API"""
    console.print("[bold blue]Starting Ollama API Tests[/bold blue]\n")
    
    # Test 1: Basic API Connectivity with Response Validation
    console.print("[yellow]Test 1: Basic API Connectivity and Response Format[/yellow]")
    try:
        with console.status("[bold blue]Sending request to Ollama API...", spinner="dots"):
            start_time = datetime.now()
            json_response = await query_ollama(TEST_PROMPT)
            duration = (datetime.now() - start_time).total_seconds()
        
        console.print("[green]✓ Successfully received and parsed JSON response[/green]")
        console.print(f"[dim]Response time: {duration:.2f} seconds[/dim]\n")
        
        # Pretty print the JSON response
        console.print("[bold]Response structure:[/bold]")
        console.print_json(json.dumps(json_response, indent=2))
        
    except Exception as e:
        console.print(f"[red]× Test failed: {str(e)}[/red]")

if __name__ == "__main__":
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(test_ollama_api())
    except Exception as e:
        console.print(f"[red]Critical error: {str(e)}[/red]")
    finally:
        loop.close()
        console.print("\n[bold blue]=== Test Script Completed ===[/bold blue]")