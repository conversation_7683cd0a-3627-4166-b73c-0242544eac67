import { NextRequest, NextResponse } from 'next/server'
import { DatabaseService } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    console.log('Fetching channels from /api/channels...')
    
    // Get topics from query parameters to filter channels by selected topics
    const { searchParams } = new URL(request.url)
    const topicsParam = searchParams.get('topics')
    const topics = topicsParam ? topicsParam.split(',').filter(t => t.trim()) : undefined
    
    console.log('Requested topics for channel filtering:', topics)
    
    const channels = await DatabaseService.getChannels(topics)
    
    console.log(`Returning ${channels.length} channels from API`)
    return NextResponse.json({ 
      channels,
      count: channels.length,
      filteredByTopics: topics || []
    })
  } catch (error) {
    console.error('Error in channels API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch channels' },
      { status: 500 }
    )
  }
}
