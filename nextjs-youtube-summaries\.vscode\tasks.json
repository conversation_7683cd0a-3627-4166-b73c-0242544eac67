{"version": "2.0.0", "tasks": [{"label": "Start Next.js Dev Server", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/nextjs-youtube-summaries"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": []}, {"label": "Start FastAPI Server", "type": "shell", "command": "python", "args": ["-m", "u<PERSON><PERSON>", "main:app", "--reload", "--host", "127.0.0.1", "--port", "8000"], "options": {"cwd": "${workspaceFolder}/nextjs-youtube-summaries/python-service"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": []}, {"label": "Start Both Servers", "dependsOrder": "parallel", "dependsOn": ["Start Next.js Dev Server", "Start FastAPI Server"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}}]}