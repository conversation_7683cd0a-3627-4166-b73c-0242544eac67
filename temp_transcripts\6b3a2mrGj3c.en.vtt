WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.370 align:start position:0%
 
back<00:00:00.210><c> today</c><00:00:00.570><c> we're</c><00:00:00.750><c> going</c><00:00:00.870><c> to</c><00:00:00.960><c> be</c><00:00:01.079><c> talking</c>

00:00:01.370 --> 00:00:01.380 align:start position:0%
back today we're going to be talking
 

00:00:01.380 --> 00:00:07.789 align:start position:0%
back today we're going to be talking
about<00:00:03.830><c> AWS</c><00:00:04.830><c> s3</c><00:00:05.720><c> image</c><00:00:06.720><c> uploading</c><00:00:07.350><c> and</c><00:00:07.529><c> also</c>

00:00:07.789 --> 00:00:07.799 align:start position:0%
about AWS s3 image uploading and also
 

00:00:07.799 --> 00:00:10.520 align:start position:0%
about AWS s3 image uploading and also
merging<00:00:08.400><c> conflicts</c><00:00:09.000><c> and</c><00:00:09.240><c> get</c><00:00:09.450><c> as</c><00:00:09.599><c> well</c><00:00:09.840><c> so</c>

00:00:10.520 --> 00:00:10.530 align:start position:0%
merging conflicts and get as well so
 

00:00:10.530 --> 00:00:14.539 align:start position:0%
merging conflicts and get as well so
what<00:00:11.040><c> we've</c><00:00:11.190><c> done</c><00:00:11.370><c> so</c><00:00:11.639><c> far</c><00:00:11.670><c> is</c><00:00:12.679><c> I've</c><00:00:13.679><c> created</c>

00:00:14.539 --> 00:00:14.549 align:start position:0%
what we've done so far is I've created
 

00:00:14.549 --> 00:00:20.810 align:start position:0%
what we've done so far is I've created
of<00:00:15.269><c> blended</c><00:00:16.170><c> in</c><00:00:16.850><c> a</c><00:00:17.869><c> repo</c><00:00:18.920><c> into</c><00:00:19.920><c> this</c><00:00:20.310><c> one</c><00:00:20.609><c> and</c>

00:00:20.810 --> 00:00:20.820 align:start position:0%
of blended in a repo into this one and
 

00:00:20.820 --> 00:00:23.990 align:start position:0%
of blended in a repo into this one and
you<00:00:21.750><c> see</c><00:00:21.960><c> if</c><00:00:22.020><c> you</c><00:00:22.140><c> now</c><00:00:22.410><c> go</c><00:00:22.470><c> to</c><00:00:22.710><c> new</c><00:00:23.189><c> blog</c><00:00:23.460><c> posts</c>

00:00:23.990 --> 00:00:24.000 align:start position:0%
you see if you now go to new blog posts
 

00:00:24.000 --> 00:00:26.450 align:start position:0%
you see if you now go to new blog posts
you're<00:00:24.150><c> gonna</c><00:00:24.269><c> click</c><00:00:24.539><c> Choose</c><00:00:24.869><c> file</c><00:00:25.460><c> you're</c>

00:00:26.450 --> 00:00:26.460 align:start position:0%
you're gonna click Choose file you're
 

00:00:26.460 --> 00:00:29.950 align:start position:0%
you're gonna click Choose file you're
gonna<00:00:26.609><c> choose</c><00:00:26.910><c> a</c><00:00:26.970><c> file</c><00:00:27.300><c> by</c><00:00:27.990><c> Homer</c><00:00:28.800><c> Simpson</c><00:00:29.400><c> and</c>

00:00:29.950 --> 00:00:29.960 align:start position:0%
gonna choose a file by Homer Simpson and
 

00:00:29.960 --> 00:00:34.100 align:start position:0%
gonna choose a file by Homer Simpson and
image<00:00:30.960><c> upload</c><00:00:31.320><c> failed</c><00:00:31.710><c> oh</c><00:00:32.390><c> yeah</c><00:00:33.420><c> exactly</c><00:00:34.020><c> we</c>

00:00:34.100 --> 00:00:34.110 align:start position:0%
image upload failed oh yeah exactly we
 

00:00:34.110 --> 00:00:36.830 align:start position:0%
image upload failed oh yeah exactly we
need<00:00:34.260><c> to</c><00:00:34.550><c> need</c><00:00:35.550><c> to</c><00:00:35.610><c> start</c><00:00:35.969><c> the</c><00:00:36.090><c> server</c><00:00:36.360><c> so</c><00:00:36.719><c> you</c>

00:00:36.830 --> 00:00:36.840 align:start position:0%
need to need to start the server so you
 

00:00:36.840 --> 00:00:44.150 align:start position:0%
need to need to start the server so you
guys<00:00:37.579><c> don't</c><00:00:38.579><c> mind</c><00:00:38.760><c> server</c><00:00:39.120><c> yes</c><00:00:42.950><c> let's</c><00:00:43.950><c> do</c><00:00:44.129><c> it</c>

00:00:44.150 --> 00:00:44.160 align:start position:0%
guys don't mind server yes let's do it
 

00:00:44.160 --> 00:00:44.750 align:start position:0%
guys don't mind server yes let's do it
again

00:00:44.750 --> 00:00:44.760 align:start position:0%
again
 

00:00:44.760 --> 00:00:58.959 align:start position:0%
again
reload<00:00:45.770><c> controller</c><00:00:52.160><c> choose</c><00:00:53.160><c> file</c><00:00:55.160><c> or</c><00:00:56.160><c> Simpson</c>

00:00:58.959 --> 00:00:58.969 align:start position:0%
 
 

00:00:58.969 --> 00:01:02.869 align:start position:0%
 
ok<00:01:00.530><c> and</c><00:01:01.530><c> as</c><00:01:01.770><c> you</c><00:01:01.800><c> can</c><00:01:02.010><c> see</c><00:01:02.070><c> we</c><00:01:02.370><c> have</c><00:01:02.489><c> a</c><00:01:02.520><c> huge</c>

00:01:02.869 --> 00:01:02.879 align:start position:0%
ok and as you can see we have a huge
 

00:01:02.879 --> 00:01:05.030 align:start position:0%
ok and as you can see we have a huge
Homer<00:01:03.270><c> picture</c><00:01:03.719><c> we</c><00:01:04.110><c> can</c><00:01:04.229><c> make</c><00:01:04.409><c> that</c><00:01:04.589><c> smaller</c>

00:01:05.030 --> 00:01:05.040 align:start position:0%
Homer picture we can make that smaller
 

00:01:05.040 --> 00:01:07.640 align:start position:0%
Homer picture we can make that smaller
and<00:01:05.430><c> we'll</c><00:01:05.640><c> figure</c><00:01:05.850><c> that</c><00:01:06.060><c> out</c><00:01:06.119><c> soon</c><00:01:06.659><c> if</c><00:01:07.229><c> we</c>

00:01:07.640 --> 00:01:07.650 align:start position:0%
and we'll figure that out soon if we
 

00:01:07.650 --> 00:01:10.580 align:start position:0%
and we'll figure that out soon if we
want<00:01:07.890><c> but</c><00:01:08.700><c> ok</c><00:01:09.060><c> so</c><00:01:09.119><c> we</c><00:01:09.420><c> have</c><00:01:09.540><c> Homer</c><00:01:09.990><c> Simpson</c>

00:01:10.580 --> 00:01:10.590 align:start position:0%
want but ok so we have Homer Simpson
 

00:01:10.590 --> 00:01:13.179 align:start position:0%
want but ok so we have Homer Simpson
actually<00:01:11.250><c> it</c><00:01:11.310><c> looks</c><00:01:11.430><c> pretty</c><00:01:11.700><c> good</c><00:01:11.909><c> on</c><00:01:11.970><c> there</c>

00:01:13.179 --> 00:01:13.189 align:start position:0%
actually it looks pretty good on there
 

00:01:13.189 --> 00:01:16.969 align:start position:0%
actually it looks pretty good on there
yeah<00:01:14.189><c> it's</c><00:01:14.790><c> a</c><00:01:14.850><c> great</c><00:01:15.060><c> show</c><00:01:15.360><c> at</c><00:01:15.540><c> a</c><00:01:15.600><c> great</c><00:01:16.470><c> show</c>

00:01:16.969 --> 00:01:16.979 align:start position:0%
yeah it's a great show at a great show
 

00:01:16.979 --> 00:01:22.370 align:start position:0%
yeah it's a great show at a great show
so<00:01:17.670><c> anyways</c><00:01:18.770><c> and</c><00:01:19.770><c> if</c><00:01:20.400><c> we</c><00:01:20.520><c> go</c><00:01:20.670><c> over</c><00:01:20.700><c> here</c><00:01:21.380><c> we're</c>

00:01:22.370 --> 00:01:22.380 align:start position:0%
so anyways and if we go over here we're
 

00:01:22.380 --> 00:01:26.240 align:start position:0%
so anyways and if we go over here we're
gonna<00:01:22.530><c> go</c><00:01:22.799><c> to</c><00:01:22.860><c> our</c><00:01:23.460><c> mailer</c><00:01:24.090><c> time</c><00:01:24.420><c> right</c><00:01:25.250><c> now</c>

00:01:26.240 --> 00:01:26.250 align:start position:0%
gonna go to our mailer time right now
 

00:01:26.250 --> 00:01:26.899 align:start position:0%
gonna go to our mailer time right now
let<00:01:26.369><c> me</c><00:01:26.430><c> see</c>

00:01:26.899 --> 00:01:26.909 align:start position:0%
let me see
 

00:01:26.909 --> 00:01:30.560 align:start position:0%
let me see
now<00:01:27.659><c> we</c><00:01:27.720><c> have</c><00:01:27.900><c> 12</c><00:01:28.200><c> images</c><00:01:28.820><c> let's</c><00:01:29.820><c> reload</c><00:01:30.270><c> this</c>

00:01:30.560 --> 00:01:30.570 align:start position:0%
now we have 12 images let's reload this
 

00:01:30.570 --> 00:01:46.060 align:start position:0%
now we have 12 images let's reload this
we<00:01:30.780><c> need</c><00:01:30.930><c> 13</c><00:01:37.130><c> me</c><00:01:38.130><c> tomorrow</c><00:01:38.689><c> just</c><00:01:39.689><c> go</c><00:01:39.930><c> to</c><00:01:39.990><c> github</c>

00:01:46.060 --> 00:01:46.070 align:start position:0%
 
 

00:01:46.070 --> 00:01:48.120 align:start position:0%
 
ok

00:01:48.120 --> 00:01:48.130 align:start position:0%
ok
 

00:01:48.130 --> 00:01:51.270 align:start position:0%
ok
Oh<00:01:48.630><c> gasps</c><00:01:49.630><c> and</c><00:01:49.899><c> we</c><00:01:49.990><c> have</c><00:01:50.079><c> 13</c><00:01:50.590><c> now</c><00:01:50.799><c> we</c><00:01:50.860><c> go</c><00:01:51.159><c> all</c>

00:01:51.270 --> 00:01:51.280 align:start position:0%
Oh gasps and we have 13 now we go all
 

00:01:51.280 --> 00:01:53.969 align:start position:0%
Oh gasps and we have 13 now we go all
the<00:01:51.310><c> way</c><00:01:51.399><c> to</c><00:01:51.490><c> the</c><00:01:51.640><c> 13th</c><00:01:52.210><c> images</c><00:01:52.770><c> image</c><00:01:53.770><c> we</c>

00:01:53.969 --> 00:01:53.979 align:start position:0%
the way to the 13th images image we
 

00:01:53.979 --> 00:01:57.149 align:start position:0%
the way to the 13th images image we
click<00:01:54.219><c> here</c><00:01:54.250><c> we</c><00:01:54.759><c> click</c><00:01:55.060><c> open</c><00:01:55.570><c> to</c><00:01:55.950><c> verify</c><00:01:56.950><c> and</c>

00:01:57.149 --> 00:01:57.159 align:start position:0%
click here we click open to verify and
 

00:01:57.159 --> 00:01:59.609 align:start position:0%
click here we click open to verify and
there's<00:01:57.340><c> Homer</c><00:01:57.789><c> Simpson</c><00:01:58.299><c> awesome</c><00:01:58.630><c> so</c><00:01:58.840><c> this</c><00:01:59.409><c> is</c>

00:01:59.609 --> 00:01:59.619 align:start position:0%
there's Homer Simpson awesome so this is
 

00:01:59.619 --> 00:02:01.919 align:start position:0%
there's Homer Simpson awesome so this is
all<00:01:59.799><c> good</c><00:02:00.310><c> but</c><00:02:00.700><c> the</c><00:02:00.729><c> only</c><00:02:00.880><c> thing</c><00:02:01.240><c> is</c><00:02:01.420><c> now</c><00:02:01.630><c> that</c>

00:02:01.919 --> 00:02:01.929 align:start position:0%
all good but the only thing is now that
 

00:02:01.929 --> 00:02:04.319 align:start position:0%
all good but the only thing is now that
we<00:02:02.380><c> have</c><00:02:02.500><c> added</c><00:02:02.950><c> this</c><00:02:03.070><c> we</c><00:02:03.250><c> also</c><00:02:03.399><c> need</c><00:02:03.490><c> to</c><00:02:03.789><c> merge</c>

00:02:04.319 --> 00:02:04.329 align:start position:0%
we have added this we also need to merge
 

00:02:04.329 --> 00:02:07.169 align:start position:0%
we have added this we also need to merge
the<00:02:04.960><c> get</c><00:02:05.679><c> conflicts</c><00:02:06.310><c> I</c><00:02:06.429><c> tried</c><00:02:06.700><c> to</c><00:02:06.850><c> create</c><00:02:07.090><c> a</c>

00:02:07.169 --> 00:02:07.179 align:start position:0%
the get conflicts I tried to create a
 

00:02:07.179 --> 00:02:12.120 align:start position:0%
the get conflicts I tried to create a
commit<00:02:07.659><c> here</c><00:02:08.019><c> and</c><00:02:08.399><c> I'm</c><00:02:09.399><c> gonna</c><00:02:09.670><c> go</c><00:02:10.800><c> once</c><00:02:11.800><c> I</c><00:02:11.920><c> try</c>

00:02:12.120 --> 00:02:12.130 align:start position:0%
commit here and I'm gonna go once I try
 

00:02:12.130 --> 00:02:14.870 align:start position:0%
commit here and I'm gonna go once I try
to<00:02:12.190><c> do</c><00:02:12.459><c> once</c><00:02:12.819><c> I</c><00:02:12.970><c> tried</c><00:02:13.209><c> to</c><00:02:13.390><c> create</c><00:02:13.540><c> a</c><00:02:13.720><c> commit</c><00:02:14.230><c> I</c>

00:02:14.870 --> 00:02:14.880 align:start position:0%
to do once I tried to create a commit I
 

00:02:14.880 --> 00:02:18.900 align:start position:0%
to do once I tried to create a commit I
got<00:02:15.880><c> this</c><00:02:16.209><c> air</c><00:02:16.480><c> this</c><00:02:16.810><c> message</c><00:02:17.519><c> adding</c><00:02:18.519><c> s</c><00:02:18.640><c> 3</c>

00:02:18.900 --> 00:02:18.910 align:start position:0%
got this air this message adding s 3
 

00:02:18.910 --> 00:02:22.559 align:start position:0%
got this air this message adding s 3
image<00:02:19.180><c> uploading</c><00:02:20.489><c> this</c><00:02:21.489><c> benches</c><00:02:21.940><c> conflicts</c>

00:02:22.559 --> 00:02:22.569 align:start position:0%
image uploading this benches conflicts
 

00:02:22.569 --> 00:02:24.120 align:start position:0%
image uploading this benches conflicts
and<00:02:22.720><c> must</c><00:02:22.959><c> be</c><00:02:23.049><c> resolved</c><00:02:23.709><c> so</c><00:02:23.830><c> we</c><00:02:23.920><c> need</c><00:02:24.040><c> to</c>

00:02:24.120 --> 00:02:24.130 align:start position:0%
and must be resolved so we need to
 

00:02:24.130 --> 00:02:25.830 align:start position:0%
and must be resolved so we need to
resolve<00:02:24.459><c> the</c><00:02:24.640><c> conflicts</c><00:02:25.180><c> and</c><00:02:25.330><c> let's</c><00:02:25.540><c> try</c><00:02:25.780><c> to</c>

00:02:25.830 --> 00:02:25.840 align:start position:0%
resolve the conflicts and let's try to
 

00:02:25.840 --> 00:02:28.020 align:start position:0%
resolve the conflicts and let's try to
let's<00:02:26.739><c> try</c><00:02:27.010><c> to</c><00:02:27.069><c> do</c><00:02:27.250><c> that</c><00:02:27.489><c> from</c><00:02:27.700><c> your</c><00:02:27.849><c> project</c>

00:02:28.020 --> 00:02:28.030 align:start position:0%
let's try to do that from your project
 

00:02:28.030 --> 00:02:31.320 align:start position:0%
let's try to do that from your project
repository<00:02:28.420><c> git</c><00:02:29.140><c> fetch</c><00:02:30.090><c> bring</c><00:02:31.090><c> in</c><00:02:31.209><c> the</c>

00:02:31.320 --> 00:02:31.330 align:start position:0%
repository git fetch bring in the
 

00:02:31.330 --> 00:02:36.150 align:start position:0%
repository git fetch bring in the
changes<00:02:31.810><c> in</c><00:02:31.989><c> text</c><00:02:32.500><c> and</c><00:02:32.819><c> test</c><00:02:34.590><c> ok</c><00:02:35.590><c> so</c><00:02:35.769><c> let's</c><00:02:35.950><c> do</c>

00:02:36.150 --> 00:02:36.160 align:start position:0%
changes in text and test ok so let's do
 

00:02:36.160 --> 00:02:38.190 align:start position:0%
changes in text and test ok so let's do
that

