WEBVTT
Kind: captions
Language: en

00:00:00.480 --> 00:00:02.350 align:start position:0%
 
hello<00:00:00.880><c> so</c><00:00:01.079><c> the</c><00:00:01.240><c> next</c><00:00:01.480><c> thing</c><00:00:01.680><c> I</c><00:00:01.760><c> want</c><00:00:01.880><c> to</c><00:00:02.120><c> talk</c>

00:00:02.350 --> 00:00:02.360 align:start position:0%
hello so the next thing I want to talk
 

00:00:02.360 --> 00:00:06.990 align:start position:0%
hello so the next thing I want to talk
about<00:00:03.199><c> is</c><00:00:04.040><c> how</c><00:00:04.200><c> to</c><00:00:04.680><c> actually</c><00:00:05.680><c> log</c><00:00:06.000><c> a</c><00:00:06.200><c> model</c><00:00:06.799><c> to</c>

00:00:06.990 --> 00:00:07.000 align:start position:0%
about is how to actually log a model to
 

00:00:07.000 --> 00:00:09.750 align:start position:0%
about is how to actually log a model to
the<00:00:07.160><c> model</c><00:00:07.679><c> registry</c><00:00:08.679><c> and</c><00:00:08.920><c> Link</c><00:00:09.200><c> a</c><00:00:09.360><c> model</c><00:00:09.639><c> from</c>

00:00:09.750 --> 00:00:09.760 align:start position:0%
the model registry and Link a model from
 

00:00:09.760 --> 00:00:13.310 align:start position:0%
the model registry and Link a model from
a<00:00:10.040><c> run</c><00:00:11.040><c> uh</c><00:00:11.400><c> you</c><00:00:11.519><c> know</c><00:00:11.639><c> to</c><00:00:11.799><c> the</c><00:00:11.920><c> model</c><00:00:12.320><c> registry</c>

00:00:13.310 --> 00:00:13.320 align:start position:0%
a run uh you know to the model registry
 

00:00:13.320 --> 00:00:16.109 align:start position:0%
a run uh you know to the model registry
and<00:00:13.599><c> before</c><00:00:13.799><c> I</c><00:00:13.960><c> get</c><00:00:14.080><c> into</c><00:00:14.360><c> the</c><00:00:14.519><c> code</c><00:00:15.440><c> and</c><00:00:16.000><c> all</c>

00:00:16.109 --> 00:00:16.119 align:start position:0%
and before I get into the code and all
 

00:00:16.119 --> 00:00:17.590 align:start position:0%
and before I get into the code and all
the<00:00:16.240><c> different</c><00:00:16.440><c> moving</c>

00:00:17.590 --> 00:00:17.600 align:start position:0%
the different moving
 

00:00:17.600 --> 00:00:19.670 align:start position:0%
the different moving
Parts<00:00:18.600><c> um</c><00:00:18.760><c> I</c><00:00:18.840><c> just</c><00:00:18.960><c> want</c>

00:00:19.670 --> 00:00:19.680 align:start position:0%
Parts um I just want
 

00:00:19.680 --> 00:00:22.830 align:start position:0%
Parts um I just want
to<00:00:20.680><c> remind</c><00:00:21.000><c> you</c><00:00:21.160><c> about</c><00:00:21.359><c> the</c><00:00:21.560><c> documentation</c><00:00:22.519><c> so</c>

00:00:22.830 --> 00:00:22.840 align:start position:0%
to remind you about the documentation so
 

00:00:22.840 --> 00:00:25.589 align:start position:0%
to remind you about the documentation so
anytime<00:00:23.840><c> that</c><00:00:24.199><c> you</c><00:00:24.359><c> get</c><00:00:24.560><c> lost</c><00:00:24.880><c> in</c><00:00:25.279><c> what</c><00:00:25.439><c> I'm</c>

00:00:25.589 --> 00:00:25.599 align:start position:0%
anytime that you get lost in what I'm
 

00:00:25.599 --> 00:00:26.630 align:start position:0%
anytime that you get lost in what I'm
saying<00:00:25.840><c> or</c><00:00:25.960><c> you</c><00:00:26.039><c> want</c><00:00:26.160><c> to</c><00:00:26.279><c> know</c><00:00:26.439><c> more</c>

00:00:26.630 --> 00:00:26.640 align:start position:0%
saying or you want to know more
 

00:00:26.640 --> 00:00:29.109 align:start position:0%
saying or you want to know more
information<00:00:27.160><c> a</c><00:00:27.359><c> really</c><00:00:27.560><c> good</c><00:00:27.960><c> resource</c><00:00:28.960><c> is</c>

00:00:29.109 --> 00:00:29.119 align:start position:0%
information a really good resource is
 

00:00:29.119 --> 00:00:30.990 align:start position:0%
information a really good resource is
the<00:00:29.240><c> weights</c><00:00:29.480><c> and</c><00:00:29.640><c> biases</c><00:00:30.199><c> docs</c><00:00:30.599><c> this</c>

00:00:30.990 --> 00:00:31.000 align:start position:0%
the weights and biases docs this
 

00:00:31.000 --> 00:00:33.990 align:start position:0%
the weights and biases docs this
actually<00:00:32.000><c> um</c><00:00:32.279><c> out</c><00:00:32.399><c> of</c><00:00:32.559><c> all</c><00:00:32.719><c> the</c><00:00:33.079><c> tools</c><00:00:33.440><c> I</c><00:00:33.600><c> use</c><00:00:33.920><c> I</c>

00:00:33.990 --> 00:00:34.000 align:start position:0%
actually um out of all the tools I use I
 

00:00:34.000 --> 00:00:35.790 align:start position:0%
actually um out of all the tools I use I
would<00:00:34.120><c> say</c><00:00:34.280><c> was</c><00:00:34.440><c> and</c><00:00:34.600><c> biases</c><00:00:35.239><c> does</c><00:00:35.399><c> a</c><00:00:35.600><c> pretty</c>

00:00:35.790 --> 00:00:35.800 align:start position:0%
would say was and biases does a pretty
 

00:00:35.800 --> 00:00:38.310 align:start position:0%
would say was and biases does a pretty
good<00:00:36.079><c> job</c><00:00:36.480><c> of</c><00:00:36.719><c> keeping</c><00:00:37.040><c> their</c><00:00:37.200><c> docs</c><00:00:37.480><c> up</c><00:00:37.640><c> to</c>

00:00:38.310 --> 00:00:38.320 align:start position:0%
good job of keeping their docs up to
 

00:00:38.320 --> 00:00:40.950 align:start position:0%
good job of keeping their docs up to
date<00:00:39.320><c> um</c><00:00:39.640><c> and</c><00:00:39.760><c> so</c><00:00:40.000><c> the</c><00:00:40.120><c> way</c><00:00:40.320><c> I</c><00:00:40.440><c> like</c><00:00:40.600><c> to</c><00:00:40.719><c> use</c>

00:00:40.950 --> 00:00:40.960 align:start position:0%
date um and so the way I like to use
 

00:00:40.960 --> 00:00:45.470 align:start position:0%
date um and so the way I like to use
docs<00:00:41.920><c> is</c><00:00:42.280><c> I</c><00:00:42.640><c> like</c><00:00:43.039><c> to</c><00:00:44.039><c> uh</c><00:00:44.200><c> use</c><00:00:44.640><c> on</c><00:00:44.960><c> on</c><00:00:45.160><c> Mac</c>

00:00:45.470 --> 00:00:45.480 align:start position:0%
docs is I like to uh use on on Mac
 

00:00:45.480 --> 00:00:47.670 align:start position:0%
docs is I like to uh use on on Mac
anyways<00:00:45.960><c> it's</c><00:00:46.199><c> command</c><00:00:46.600><c> K</c><00:00:47.160><c> and</c><00:00:47.280><c> you</c><00:00:47.440><c> just</c>

00:00:47.670 --> 00:00:47.680 align:start position:0%
anyways it's command K and you just
 

00:00:47.680 --> 00:00:50.790 align:start position:0%
anyways it's command K and you just
search<00:00:48.680><c> so</c><00:00:49.199><c> um</c><00:00:49.360><c> in</c><00:00:49.480><c> this</c><00:00:49.680><c> case</c><00:00:50.039><c> you</c><00:00:50.160><c> know</c><00:00:50.320><c> model</c>

00:00:50.790 --> 00:00:50.800 align:start position:0%
search so um in this case you know model
 

00:00:50.800 --> 00:00:53.349 align:start position:0%
search so um in this case you know model
registry<00:00:51.800><c> I</c><00:00:51.920><c> have</c><00:00:52.120><c> caps</c><00:00:52.399><c> lock</c><00:00:52.600><c> on</c><00:00:52.840><c> sorry</c><00:00:53.120><c> about</c>

00:00:53.349 --> 00:00:53.359 align:start position:0%
registry I have caps lock on sorry about
 

00:00:53.359 --> 00:00:54.590 align:start position:0%
registry I have caps lock on sorry about
that<00:00:53.600><c> model</c>

00:00:54.590 --> 00:00:54.600 align:start position:0%
that model
 

00:00:54.600 --> 00:00:57.110 align:start position:0%
that model
registry<00:00:55.600><c> and</c><00:00:56.359><c> I</c><00:00:56.480><c> like</c><00:00:56.600><c> to</c><00:00:56.719><c> start</c><00:00:56.920><c> with</c>

00:00:57.110 --> 00:00:57.120 align:start position:0%
registry and I like to start with
 

00:00:57.120 --> 00:00:58.910 align:start position:0%
registry and I like to start with
tutorials<00:00:57.719><c> so</c><00:00:57.879><c> in</c><00:00:58.039><c> this</c><00:00:58.199><c> case</c><00:00:58.440><c> register</c>

00:00:58.910 --> 00:00:58.920 align:start position:0%
tutorials so in this case register
 

00:00:58.920 --> 00:01:01.590 align:start position:0%
tutorials so in this case register
models<00:01:00.000><c> is</c><00:01:00.079><c> a</c><00:01:00.199><c> really</c><00:01:00.359><c> good</c><00:01:00.559><c> place</c><00:01:00.680><c> to</c><00:01:00.879><c> start</c>

00:01:01.590 --> 00:01:01.600 align:start position:0%
models is a really good place to start
 

00:01:01.600 --> 00:01:05.109 align:start position:0%
models is a really good place to start
and<00:01:02.160><c> do</c><00:01:02.440><c> has</c><00:01:02.760><c> has</c><00:01:02.879><c> a</c><00:01:03.039><c> great</c><00:01:03.280><c> walk</c><00:01:03.640><c> through</c><00:01:04.119><c> of</c>

00:01:05.109 --> 00:01:05.119 align:start position:0%
and do has has a great walk through of
 

00:01:05.119 --> 00:01:08.429 align:start position:0%
and do has has a great walk through of
real<00:01:05.400><c> end</c><00:01:05.640><c> to-end</c><00:01:06.000><c> example</c><00:01:06.479><c> of</c><00:01:06.680><c> how</c><00:01:06.880><c> to</c><00:01:07.759><c> log</c>

00:01:08.429 --> 00:01:08.439 align:start position:0%
real end to-end example of how to log
 

00:01:08.439 --> 00:01:11.390 align:start position:0%
real end to-end example of how to log
models<00:01:09.439><c> and</c><00:01:09.920><c> create</c><00:01:10.200><c> artifacts</c><00:01:11.000><c> and</c><00:01:11.119><c> do</c><00:01:11.280><c> all</c>

00:01:11.390 --> 00:01:11.400 align:start position:0%
models and create artifacts and do all
 

00:01:11.400 --> 00:01:13.670 align:start position:0%
models and create artifacts and do all
the<00:01:11.520><c> things</c><00:01:11.840><c> I'm</c><00:01:11.920><c> going</c><00:01:12.000><c> to</c><00:01:12.119><c> show</c><00:01:12.280><c> you</c><00:01:12.680><c> today</c>

00:01:13.670 --> 00:01:13.680 align:start position:0%
the things I'm going to show you today
 

00:01:13.680 --> 00:01:14.870 align:start position:0%
the things I'm going to show you today
but<00:01:13.880><c> more</c>

00:01:14.870 --> 00:01:14.880 align:start position:0%
but more
 

00:01:14.880 --> 00:01:18.469 align:start position:0%
but more
specifically<00:01:15.880><c> uh</c><00:01:16.240><c> there's</c><00:01:16.600><c> also</c><00:01:17.119><c> these</c><00:01:17.479><c> docs</c>

00:01:18.469 --> 00:01:18.479 align:start position:0%
specifically uh there's also these docs
 

00:01:18.479 --> 00:01:21.710 align:start position:0%
specifically uh there's also these docs
which<00:01:18.759><c> go</c><00:01:19.400><c> through</c><00:01:20.400><c> um</c><00:01:20.759><c> kind</c><00:01:20.880><c> of</c><00:01:21.119><c> almost</c><00:01:21.479><c> the</c>

00:01:21.710 --> 00:01:21.720 align:start position:0%
which go through um kind of almost the
 

00:01:21.720 --> 00:01:24.310 align:start position:0%
which go through um kind of almost the
exact<00:01:22.159><c> example</c><00:01:22.600><c> that</c><00:01:22.720><c> I'm</c><00:01:22.840><c> going</c><00:01:23.000><c> to</c><00:01:23.159><c> show</c>

00:01:24.310 --> 00:01:24.320 align:start position:0%
exact example that I'm going to show
 

00:01:24.320 --> 00:01:27.390 align:start position:0%
exact example that I'm going to show
you<00:01:25.320><c> and</c><00:01:25.600><c> kind</c><00:01:25.720><c> of</c><00:01:25.960><c> explain</c><00:01:26.720><c> that</c><00:01:26.920><c> from</c><00:01:27.079><c> a</c><00:01:27.240><c> more</c>

00:01:27.390 --> 00:01:27.400 align:start position:0%
you and kind of explain that from a more
 

00:01:27.400 --> 00:01:30.670 align:start position:0%
you and kind of explain that from a more
of<00:01:27.520><c> an</c><00:01:27.720><c> API</c><00:01:28.479><c> perspective</c><00:01:29.479><c> of</c><00:01:29.759><c> like</c><00:01:30.159><c> how</c><00:01:30.439><c> what's</c>

00:01:30.670 --> 00:01:30.680 align:start position:0%
of an API perspective of like how what's
 

00:01:30.680 --> 00:01:33.429 align:start position:0%
of an API perspective of like how what's
going<00:01:30.840><c> on</c><00:01:31.040><c> in</c><00:01:31.200><c> that</c><00:01:31.880><c> code</c><00:01:32.880><c> so</c><00:01:33.040><c> I</c><00:01:33.159><c> highly</c>

00:01:33.429 --> 00:01:33.439 align:start position:0%
going on in that code so I highly
 

00:01:33.439 --> 00:01:35.469 align:start position:0%
going on in that code so I highly
recommend<00:01:34.040><c> at</c><00:01:34.200><c> least</c><00:01:34.560><c> going</c><00:01:34.880><c> through</c><00:01:35.159><c> this</c>

00:01:35.469 --> 00:01:35.479 align:start position:0%
recommend at least going through this
 

00:01:35.479 --> 00:01:36.590 align:start position:0%
recommend at least going through this
this<00:01:35.720><c> log</c>

00:01:36.590 --> 00:01:36.600 align:start position:0%
this log
 

00:01:36.600 --> 00:01:40.389 align:start position:0%
this log
models<00:01:37.600><c> and</c><00:01:38.240><c> um</c><00:01:38.840><c> this</c><00:01:38.960><c> is</c><00:01:39.119><c> in</c><00:01:39.280><c> the</c><00:01:39.560><c> guides</c><00:01:40.240><c> so</c>

00:01:40.389 --> 00:01:40.399 align:start position:0%
models and um this is in the guides so
 

00:01:40.399 --> 00:01:42.950 align:start position:0%
models and um this is in the guides so
there's<00:01:40.600><c> tutorials</c><00:01:41.200><c> also</c><00:01:41.560><c> guides</c><00:01:42.479><c> so</c><00:01:42.680><c> this</c><00:01:42.799><c> is</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
there's tutorials also guides so this is
 

00:01:42.960 --> 00:01:45.429 align:start position:0%
there's tutorials also guides so this is
under<00:01:43.799><c> uh</c><00:01:44.240><c> this</c><00:01:44.320><c> is</c><00:01:44.520><c> one</c><00:01:44.680><c> of</c><00:01:44.799><c> the</c><00:01:45.040><c> guides</c>

00:01:45.429 --> 00:01:45.439 align:start position:0%
under uh this is one of the guides
 

00:01:45.439 --> 00:01:48.149 align:start position:0%
under uh this is one of the guides
called<00:01:45.680><c> log</c><00:01:46.040><c> models</c><00:01:46.600><c> and</c><00:01:46.920><c> and</c><00:01:47.799><c> you</c><00:01:47.920><c> know</c>

00:01:48.149 --> 00:01:48.159 align:start position:0%
called log models and and you know
 

00:01:48.159 --> 00:01:51.149 align:start position:0%
called log models and and you know
please<00:01:48.799><c> take</c><00:01:48.960><c> a</c><00:01:49.119><c> look</c><00:01:49.240><c> at</c><00:01:49.479><c> this</c><00:01:50.399><c> so</c><00:01:50.640><c> next</c><00:01:50.920><c> let's</c>

00:01:51.149 --> 00:01:51.159 align:start position:0%
please take a look at this so next let's
 

00:01:51.159 --> 00:01:52.469 align:start position:0%
please take a look at this so next let's
jump<00:01:51.439><c> into</c><00:01:51.680><c> the</c>

00:01:52.469 --> 00:01:52.479 align:start position:0%
jump into the
 

00:01:52.479 --> 00:01:55.830 align:start position:0%
jump into the
code<00:01:53.479><c> so</c><00:01:53.640><c> this</c><00:01:53.759><c> is</c><00:01:53.960><c> the</c><00:01:54.200><c> code</c><00:01:55.200><c> that</c><00:01:55.399><c> we'll</c><00:01:55.640><c> be</c>

00:01:55.830 --> 00:01:55.840 align:start position:0%
code so this is the code that we'll be
 

00:01:55.840 --> 00:02:00.630 align:start position:0%
code so this is the code that we'll be
using<00:01:56.640><c> to</c><00:01:57.560><c> link</c><00:01:57.880><c> the</c><00:01:58.360><c> model</c><00:01:59.360><c> to</c><00:01:59.560><c> theel</c>

00:02:00.630 --> 00:02:00.640 align:start position:0%
using to link the model to theel
 

00:02:00.640 --> 00:02:03.389 align:start position:0%
using to link the model to theel
registry<00:02:01.640><c> so</c><00:02:01.840><c> first</c><00:02:02.039><c> thing</c><00:02:02.119><c> you</c><00:02:02.240><c> want</c><00:02:02.320><c> to</c><00:02:02.520><c> do</c>

00:02:03.389 --> 00:02:03.399 align:start position:0%
registry so first thing you want to do
 

00:02:03.399 --> 00:02:05.910 align:start position:0%
registry so first thing you want to do
is<00:02:03.920><c> install</c><00:02:04.280><c> weights</c><00:02:04.520><c> and</c><00:02:04.719><c> biases</c><00:02:05.719><c> and</c><00:02:05.799><c> you</c>

00:02:05.910 --> 00:02:05.920 align:start position:0%
is install weights and biases and you
 

00:02:05.920 --> 00:02:07.149 align:start position:0%
is install weights and biases and you
want<00:02:06.000><c> to</c><00:02:06.119><c> make</c><00:02:06.240><c> sure</c><00:02:06.399><c> you</c><00:02:06.560><c> install</c><00:02:06.799><c> the</c><00:02:06.880><c> latest</c>

00:02:07.149 --> 00:02:07.159 align:start position:0%
want to make sure you install the latest
 

00:02:07.159 --> 00:02:08.790 align:start position:0%
want to make sure you install the latest
version<00:02:07.399><c> of</c><00:02:07.479><c> weights</c><00:02:07.680><c> and</c><00:02:07.799><c> bies</c><00:02:08.319><c> so</c><00:02:08.560><c> pip</c>

00:02:08.790 --> 00:02:08.800 align:start position:0%
version of weights and bies so pip
 

00:02:08.800 --> 00:02:10.550 align:start position:0%
version of weights and bies so pip
install

00:02:10.550 --> 00:02:10.560 align:start position:0%
install
 

00:02:10.560 --> 00:02:14.110 align:start position:0%
install
-<00:02:11.560><c> next</c><00:02:11.720><c> thing</c><00:02:11.840><c> we</c><00:02:11.920><c> want</c><00:02:12.040><c> to</c><00:02:12.200><c> do</c><00:02:12.319><c> is</c><00:02:12.480><c> some</c><00:02:13.120><c> setup</c>

00:02:14.110 --> 00:02:14.120 align:start position:0%
- next thing we want to do is some setup
 

00:02:14.120 --> 00:02:16.589 align:start position:0%
- next thing we want to do is some setup
so<00:02:14.440><c> first</c><00:02:15.360><c> we</c><00:02:15.480><c> want</c>

00:02:16.589 --> 00:02:16.599 align:start position:0%
so first we want
 

00:02:16.599 --> 00:02:19.710 align:start position:0%
so first we want
to<00:02:17.599><c> attach</c><00:02:17.959><c> to</c><00:02:18.160><c> a</c><00:02:18.440><c> project</c><00:02:19.200><c> that</c><00:02:19.319><c> we</c><00:02:19.480><c> already</c>

00:02:19.710 --> 00:02:19.720 align:start position:0%
to attach to a project that we already
 

00:02:19.720 --> 00:02:21.710 align:start position:0%
to attach to a project that we already
have<00:02:19.920><c> or</c><00:02:20.080><c> create</c><00:02:20.319><c> a</c><00:02:20.440><c> new</c><00:02:20.720><c> project</c><00:02:21.360><c> so</c><00:02:21.560><c> that's</c>

00:02:21.710 --> 00:02:21.720 align:start position:0%
have or create a new project so that's
 

00:02:21.720 --> 00:02:25.509 align:start position:0%
have or create a new project so that's
what<00:02:21.879><c> this</c><00:02:22.040><c> wbit</c><00:02:22.920><c> is</c><00:02:23.480><c> doing</c><00:02:24.480><c> and</c><00:02:24.640><c> then</c><00:02:25.239><c> we</c><00:02:25.360><c> are</c>

00:02:25.509 --> 00:02:25.519 align:start position:0%
what this wbit is doing and then we are
 

00:02:25.519 --> 00:02:27.470 align:start position:0%
what this wbit is doing and then we are
going<00:02:25.680><c> to</c><00:02:25.920><c> create</c><00:02:26.239><c> the</c><00:02:26.400><c> artifact</c><00:02:27.200><c> in</c><00:02:27.319><c> this</c>

00:02:27.470 --> 00:02:27.480 align:start position:0%
going to create the artifact in this
 

00:02:27.480 --> 00:02:29.110 align:start position:0%
going to create the artifact in this
case<00:02:27.640><c> we're</c><00:02:27.800><c> creating</c><00:02:28.080><c> a</c><00:02:28.200><c> dummy</c><00:02:28.599><c> artifact</c>

00:02:29.110 --> 00:02:29.120 align:start position:0%
case we're creating a dummy artifact
 

00:02:29.120 --> 00:02:31.150 align:start position:0%
case we're creating a dummy artifact
which<00:02:29.239><c> is</c><00:02:29.319><c> just</c><00:02:29.440><c> a</c><00:02:29.560><c> text</c><00:02:30.000><c> file</c><00:02:30.599><c> so</c><00:02:30.760><c> what</c><00:02:30.879><c> is</c>

00:02:31.150 --> 00:02:31.160 align:start position:0%
which is just a text file so what is
 

00:02:31.160 --> 00:02:33.110 align:start position:0%
which is just a text file so what is
artifact<00:02:31.680><c> an</c><00:02:31.800><c> artifact</c>

00:02:33.110 --> 00:02:33.120 align:start position:0%
artifact an artifact
 

00:02:33.120 --> 00:02:37.110 align:start position:0%
artifact an artifact
is<00:02:34.120><c> um</c><00:02:34.800><c> it's</c><00:02:35.000><c> a</c><00:02:35.200><c> file</c><00:02:35.720><c> that</c><00:02:36.040><c> has</c><00:02:36.560><c> that</c><00:02:36.720><c> saves</c>

00:02:37.110 --> 00:02:37.120 align:start position:0%
is um it's a file that has that saves
 

00:02:37.120 --> 00:02:38.990 align:start position:0%
is um it's a file that has that saves
some<00:02:37.280><c> kind</c><00:02:37.400><c> of</c><00:02:37.599><c> state</c><00:02:38.000><c> about</c><00:02:38.200><c> your</c><00:02:38.360><c> model</c><00:02:38.879><c> that</c>

00:02:38.990 --> 00:02:39.000 align:start position:0%
some kind of state about your model that
 

00:02:39.000 --> 00:02:40.830 align:start position:0%
some kind of state about your model that
you<00:02:39.120><c> can</c><00:02:39.239><c> use</c><00:02:39.519><c> Downstream</c><00:02:40.280><c> for</c><00:02:40.480><c> further</c>

00:02:40.830 --> 00:02:40.840 align:start position:0%
you can use Downstream for further
 

00:02:40.840 --> 00:02:43.630 align:start position:0%
you can use Downstream for further
training<00:02:41.280><c> or</c><00:02:41.480><c> inference</c><00:02:42.280><c> in</c><00:02:42.440><c> this</c><00:02:42.640><c> case</c><00:02:43.400><c> we're</c>

00:02:43.630 --> 00:02:43.640 align:start position:0%
training or inference in this case we're
 

00:02:43.640 --> 00:02:46.030 align:start position:0%
training or inference in this case we're
just<00:02:43.920><c> creating</c><00:02:44.280><c> a</c><00:02:44.400><c> dummy</c><00:02:44.760><c> file</c><00:02:45.560><c> that</c><00:02:45.680><c> is</c><00:02:45.879><c> just</c>

00:02:46.030 --> 00:02:46.040 align:start position:0%
just creating a dummy file that is just
 

00:02:46.040 --> 00:02:49.790 align:start position:0%
just creating a dummy file that is just
a<00:02:46.200><c> text</c><00:02:46.800><c> file</c><00:02:47.800><c> but</c><00:02:48.040><c> that</c><00:02:48.480><c> that</c><00:02:48.640><c> doesn't</c><00:02:48.959><c> matter</c>

00:02:49.790 --> 00:02:49.800 align:start position:0%
a text file but that that doesn't matter
 

00:02:49.800 --> 00:02:51.910 align:start position:0%
a text file but that that doesn't matter
uh<00:02:49.959><c> this</c><00:02:50.080><c> is</c><00:02:50.360><c> just</c><00:02:50.920><c> to</c><00:02:51.120><c> make</c><00:02:51.280><c> sure</c><00:02:51.519><c> that</c><00:02:51.720><c> this</c>

00:02:51.910 --> 00:02:51.920 align:start position:0%
uh this is just to make sure that this
 

00:02:51.920 --> 00:02:53.670 align:start position:0%
uh this is just to make sure that this
example<00:02:52.360><c> is</c><00:02:52.560><c> minimal</c><00:02:53.200><c> so</c><00:02:53.360><c> you</c><00:02:53.480><c> can</c>

00:02:53.670 --> 00:02:53.680 align:start position:0%
example is minimal so you can
 

00:02:53.680 --> 00:02:58.430 align:start position:0%
example is minimal so you can
concentrate<00:02:54.440><c> on</c><00:02:55.480><c> understanding</c><00:02:56.480><c> how</c><00:02:57.159><c> to</c><00:02:58.159><c> log</c>

00:02:58.430 --> 00:02:58.440 align:start position:0%
concentrate on understanding how to log
 

00:02:58.440 --> 00:03:00.949 align:start position:0%
concentrate on understanding how to log
a<00:02:58.599><c> model</c><00:02:58.879><c> and</c><00:02:59.040><c> Link</c><00:02:59.319><c> it</c><00:03:00.040><c> to</c><00:03:00.239><c> the</c>

00:03:00.949 --> 00:03:00.959 align:start position:0%
a model and Link it to the
 

00:03:00.959 --> 00:03:04.509 align:start position:0%
a model and Link it to the
registry<00:03:01.959><c> the</c><00:03:02.120><c> next</c><00:03:02.360><c> step</c><00:03:02.840><c> after</c><00:03:03.840><c> you</c><00:03:04.000><c> know</c>

00:03:04.509 --> 00:03:04.519 align:start position:0%
registry the next step after you know
 

00:03:04.519 --> 00:03:06.910 align:start position:0%
registry the next step after you know
what<00:03:04.760><c> asset</c><00:03:05.080><c> you</c><00:03:05.319><c> have</c><00:03:06.000><c> or</c><00:03:06.200><c> where</c><00:03:06.360><c> your</c><00:03:06.599><c> asset</c>

00:03:06.910 --> 00:03:06.920 align:start position:0%
what asset you have or where your asset
 

00:03:06.920 --> 00:03:10.949 align:start position:0%
what asset you have or where your asset
is<00:03:07.519><c> is</c><00:03:07.680><c> to</c><00:03:08.440><c> actually</c><00:03:09.440><c> uh</c><00:03:09.560><c> create</c><00:03:09.879><c> an</c>

00:03:10.949 --> 00:03:10.959 align:start position:0%
is is to actually uh create an
 

00:03:10.959 --> 00:03:13.869 align:start position:0%
is is to actually uh create an
artifact<00:03:11.959><c> and</c><00:03:12.080><c> so</c><00:03:12.360><c> you</c><00:03:12.640><c> so</c><00:03:12.879><c> this</c><00:03:13.000><c> is</c><00:03:13.120><c> a</c><00:03:13.360><c> good</c>

00:03:13.869 --> 00:03:13.879 align:start position:0%
artifact and so you so this is a good
 

00:03:13.879 --> 00:03:18.470 align:start position:0%
artifact and so you so this is a good
API<00:03:14.400><c> to</c><00:03:14.599><c> use</c><00:03:15.080><c> wb.</c><00:03:15.879><c> log</c><00:03:16.319><c> model</c><00:03:17.319><c> WB</c><00:03:17.840><c> log</c><00:03:18.080><c> model</c><00:03:18.400><c> is</c>

00:03:18.470 --> 00:03:18.480 align:start position:0%
API to use wb. log model WB log model is
 

00:03:18.480 --> 00:03:21.229 align:start position:0%
API to use wb. log model WB log model is
a<00:03:18.640><c> shortcut</c><00:03:19.480><c> for</c><00:03:19.760><c> logging</c><00:03:20.159><c> an</c><00:03:20.400><c> artifact</c><00:03:21.000><c> of</c>

00:03:21.229 --> 00:03:21.239 align:start position:0%
a shortcut for logging an artifact of
 

00:03:21.239 --> 00:03:22.270 align:start position:0%
a shortcut for logging an artifact of
type

00:03:22.270 --> 00:03:22.280 align:start position:0%
type
 

00:03:22.280 --> 00:03:24.949 align:start position:0%
type
model<00:03:23.280><c> and</c><00:03:23.360><c> so</c><00:03:23.519><c> what</c><00:03:23.599><c> we</c><00:03:23.799><c> do</c><00:03:24.000><c> here</c><00:03:24.159><c> is</c><00:03:24.319><c> we</c><00:03:24.799><c> we</c>

00:03:24.949 --> 00:03:24.959 align:start position:0%
model and so what we do here is we we
 

00:03:24.959 --> 00:03:26.630 align:start position:0%
model and so what we do here is we we
pass<00:03:25.159><c> in</c><00:03:25.280><c> the</c><00:03:25.440><c> name</c><00:03:25.760><c> of</c><00:03:25.879><c> the</c><00:03:26.000><c> artifact</c><00:03:26.560><c> which</c>

00:03:26.630 --> 00:03:26.640 align:start position:0%
pass in the name of the artifact which
 

00:03:26.640 --> 00:03:31.110 align:start position:0%
pass in the name of the artifact which
is<00:03:26.840><c> just</c><00:03:27.400><c> model-</c><00:03:28.400><c> wb.</c><00:03:29.200><c> run</c><00:03:29.360><c> ID</c><00:03:30.239><c> give</c><00:03:30.400><c> it</c><00:03:30.599><c> the</c>

00:03:31.110 --> 00:03:31.120 align:start position:0%
is just model- wb. run ID give it the
 

00:03:31.120 --> 00:03:34.630 align:start position:0%
is just model- wb. run ID give it the
path<00:03:32.120><c> to</c><00:03:32.840><c> the</c><00:03:33.000><c> artifact</c><00:03:33.879><c> which</c><00:03:34.040><c> in</c><00:03:34.239><c> this</c><00:03:34.439><c> case</c>

00:03:34.630 --> 00:03:34.640 align:start position:0%
path to the artifact which in this case
 

00:03:34.640 --> 00:03:38.589 align:start position:0%
path to the artifact which in this case
is<00:03:34.840><c> just</c><00:03:35.120><c> this</c><00:03:35.599><c> text</c><00:03:35.879><c> file</c><00:03:36.799><c> and</c><00:03:36.920><c> then</c><00:03:37.519><c> aliases</c>

00:03:38.589 --> 00:03:38.599 align:start position:0%
is just this text file and then aliases
 

00:03:38.599 --> 00:03:42.270 align:start position:0%
is just this text file and then aliases
aliases<00:03:39.599><c> are</c><00:03:39.920><c> metadata</c><00:03:40.599><c> that</c><00:03:40.720><c> you</c><00:03:40.879><c> can</c><00:03:41.400><c> attach</c>

00:03:42.270 --> 00:03:42.280 align:start position:0%
aliases are metadata that you can attach
 

00:03:42.280 --> 00:03:43.190 align:start position:0%
aliases are metadata that you can attach
to<00:03:42.480><c> an</c>

00:03:43.190 --> 00:03:43.200 align:start position:0%
to an
 

00:03:43.200 --> 00:03:47.670 align:start position:0%
to an
artifact<00:03:44.280><c> that</c><00:03:45.280><c> U</c><00:03:45.760><c> flag</c><00:03:46.360><c> the</c><00:03:46.560><c> life</c><00:03:46.840><c> cycle</c><00:03:47.519><c> of</c>

00:03:47.670 --> 00:03:47.680 align:start position:0%
artifact that U flag the life cycle of
 

00:03:47.680 --> 00:03:48.630 align:start position:0%
artifact that U flag the life cycle of
the

00:03:48.630 --> 00:03:48.640 align:start position:0%
the
 

00:03:48.640 --> 00:03:50.830 align:start position:0%
the
artifact<00:03:49.640><c> you</c><00:03:49.799><c> can</c><00:03:49.920><c> use</c><00:03:50.080><c> it</c><00:03:50.200><c> in</c><00:03:50.319><c> any</c><00:03:50.519><c> way</c><00:03:50.680><c> you</c>

00:03:50.830 --> 00:03:50.840 align:start position:0%
artifact you can use it in any way you
 

00:03:50.840 --> 00:03:53.589 align:start position:0%
artifact you can use it in any way you
want<00:03:51.400><c> but</c><00:03:51.760><c> what</c><00:03:51.920><c> I</c><00:03:52.079><c> recommend</c><00:03:52.560><c> is</c><00:03:52.920><c> you</c><00:03:53.280><c> try</c><00:03:53.439><c> to</c>

00:03:53.589 --> 00:03:53.599 align:start position:0%
want but what I recommend is you try to
 

00:03:53.599 --> 00:03:57.390 align:start position:0%
want but what I recommend is you try to
create<00:03:54.040><c> aliases</c><00:03:55.040><c> that</c><00:03:55.480><c> sort</c><00:03:55.920><c> of</c><00:03:56.920><c> give</c><00:03:57.079><c> you</c><00:03:57.200><c> an</c>

00:03:57.390 --> 00:03:57.400 align:start position:0%
create aliases that sort of give you an
 

00:03:57.400 --> 00:04:00.630 align:start position:0%
create aliases that sort of give you an
idea<00:03:58.319><c> of</c><00:03:58.519><c> where</c><00:03:58.760><c> that</c><00:03:58.920><c> model</c><00:03:59.319><c> is</c>

00:04:00.630 --> 00:04:00.640 align:start position:0%
idea of where that model is
 

00:04:00.640 --> 00:04:03.789 align:start position:0%
idea of where that model is
in<00:04:01.040><c> kind</c><00:04:01.159><c> of</c><00:04:01.400><c> this</c><00:04:01.599><c> life</c><00:04:01.840><c> cycle</c><00:04:02.439><c> so</c><00:04:03.439><c> examples</c>

00:04:03.789 --> 00:04:03.799 align:start position:0%
in kind of this life cycle so examples
 

00:04:03.799 --> 00:04:07.429 align:start position:0%
in kind of this life cycle so examples
of<00:04:03.959><c> tags</c><00:04:04.239><c> that</c><00:04:04.400><c> I</c><00:04:04.519><c> like</c><00:04:04.640><c> to</c><00:04:04.840><c> use</c><00:04:05.280><c> are</c><00:04:06.280><c> Dev</c>

00:04:07.429 --> 00:04:07.439 align:start position:0%
of tags that I like to use are Dev
 

00:04:07.439 --> 00:04:08.949 align:start position:0%
of tags that I like to use are Dev
staging

00:04:08.949 --> 00:04:08.959 align:start position:0%
staging
 

00:04:08.959 --> 00:04:12.789 align:start position:0%
staging
Review<00:04:09.959><c> production</c><00:04:10.959><c> all</c><00:04:11.079><c> of</c><00:04:11.480><c> these</c><00:04:12.480><c> uh</c>

00:04:12.789 --> 00:04:12.799 align:start position:0%
Review production all of these uh
 

00:04:12.799 --> 00:04:16.509 align:start position:0%
Review production all of these uh
different<00:04:13.439><c> sort</c><00:04:13.599><c> of</c><00:04:13.760><c> aliases</c><00:04:14.400><c> are</c><00:04:14.640><c> good</c>

00:04:16.509 --> 00:04:16.519 align:start position:0%
different sort of aliases are good
 

00:04:16.519 --> 00:04:19.629 align:start position:0%
different sort of aliases are good
aliases<00:04:17.519><c> and</c><00:04:17.959><c> so</c><00:04:18.959><c> uh</c><00:04:19.079><c> I'm</c><00:04:19.160><c> going</c><00:04:19.280><c> to</c><00:04:19.400><c> go</c><00:04:19.479><c> ahead</c>

00:04:19.629 --> 00:04:19.639 align:start position:0%
aliases and so uh I'm going to go ahead
 

00:04:19.639 --> 00:04:27.550 align:start position:0%
aliases and so uh I'm going to go ahead
and<00:04:19.759><c> run</c><00:04:19.959><c> this</c>

00:04:27.550 --> 00:04:27.560 align:start position:0%
 
 

00:04:27.560 --> 00:04:29.870 align:start position:0%
 
code<00:04:28.560><c> and</c><00:04:28.720><c> it's</c><00:04:28.880><c> going</c><00:04:28.960><c> to</c><00:04:29.479><c> it's</c><00:04:29.680><c> it's</c><00:04:29.800><c> going</c>

00:04:29.870 --> 00:04:29.880 align:start position:0%
code and it's going to it's it's going
 

00:04:29.880 --> 00:04:31.350 align:start position:0%
code and it's going to it's it's going
to<00:04:30.039><c> upload</c><00:04:30.560><c> this</c>

00:04:31.350 --> 00:04:31.360 align:start position:0%
to upload this
 

00:04:31.360 --> 00:04:34.270 align:start position:0%
to upload this
artifact<00:04:32.360><c> into</c><00:04:32.639><c> the</c><00:04:32.840><c> run</c><00:04:33.840><c> and</c><00:04:33.919><c> it's</c><00:04:34.080><c> going</c><00:04:34.160><c> to</c>

00:04:34.270 --> 00:04:34.280 align:start position:0%
artifact into the run and it's going to
 

00:04:34.280 --> 00:04:35.629 align:start position:0%
artifact into the run and it's going to
give<00:04:34.360><c> me</c><00:04:34.479><c> a</c><00:04:34.600><c> link</c><00:04:34.840><c> let's</c><00:04:35.000><c> go</c><00:04:35.080><c> ahead</c><00:04:35.240><c> and</c><00:04:35.320><c> clink</c>

00:04:35.629 --> 00:04:35.639 align:start position:0%
give me a link let's go ahead and clink
 

00:04:35.639 --> 00:04:40.950 align:start position:0%
give me a link let's go ahead and clink
this

00:04:40.950 --> 00:04:40.960 align:start position:0%
 
 

00:04:40.960 --> 00:04:43.590 align:start position:0%
 
link<00:04:41.960><c> okay</c><00:04:42.120><c> so</c><00:04:42.320><c> there's</c><00:04:42.520><c> nothing</c><00:04:43.080><c> logged</c><00:04:43.479><c> to</c>

00:04:43.590 --> 00:04:43.600 align:start position:0%
link okay so there's nothing logged to
 

00:04:43.600 --> 00:04:45.390 align:start position:0%
link okay so there's nothing logged to
this<00:04:43.720><c> run</c><00:04:44.160><c> except</c><00:04:44.400><c> for</c><00:04:44.560><c> the</c><00:04:44.680><c> artifact</c><00:04:45.160><c> so</c><00:04:45.320><c> I'm</c>

00:04:45.390 --> 00:04:45.400 align:start position:0%
this run except for the artifact so I'm
 

00:04:45.400 --> 00:04:47.430 align:start position:0%
this run except for the artifact so I'm
going<00:04:45.520><c> to</c><00:04:45.880><c> in</c><00:04:46.080><c> this</c><00:04:46.479><c> view</c><00:04:46.919><c> so</c><00:04:47.120><c> this</c><00:04:47.199><c> is</c><00:04:47.320><c> the</c>

00:04:47.430 --> 00:04:47.440 align:start position:0%
going to in this view so this is the
 

00:04:47.440 --> 00:04:49.670 align:start position:0%
going to in this view so this is the
overview<00:04:47.919><c> of</c><00:04:48.080><c> that</c><00:04:48.280><c> specific</c><00:04:48.680><c> run</c><00:04:49.400><c> nothing</c>

00:04:49.670 --> 00:04:49.680 align:start position:0%
overview of that specific run nothing
 

00:04:49.680 --> 00:04:52.270 align:start position:0%
overview of that specific run nothing
really<00:04:49.919><c> happened</c><00:04:50.199><c> in</c><00:04:50.360><c> this</c><00:04:50.520><c> run</c><00:04:51.280><c> but</c><00:04:51.440><c> there's</c>

00:04:52.270 --> 00:04:52.280 align:start position:0%
really happened in this run but there's
 

00:04:52.280 --> 00:04:55.390 align:start position:0%
really happened in this run but there's
artifacts<00:04:53.280><c> and</c><00:04:53.520><c> in</c><00:04:53.720><c> this</c><00:04:53.919><c> case</c><00:04:54.240><c> we</c><00:04:54.560><c> have</c><00:04:54.960><c> that</c>

00:04:55.390 --> 00:04:55.400 align:start position:0%
artifacts and in this case we have that
 

00:04:55.400 --> 00:04:58.629 align:start position:0%
artifacts and in this case we have that
artifact<00:04:55.919><c> that</c><00:04:56.039><c> we</c><00:04:56.199><c> logged</c><00:04:56.880><c> of</c><00:04:57.080><c> type</c>

00:04:58.629 --> 00:04:58.639 align:start position:0%
artifact that we logged of type
 

00:04:58.639 --> 00:05:00.870 align:start position:0%
artifact that we logged of type
model<00:04:59.720><c> I</c><00:04:59.800><c> can</c><00:04:59.960><c> click</c>

00:05:00.870 --> 00:05:00.880 align:start position:0%
model I can click
 

00:05:00.880 --> 00:05:02.550 align:start position:0%
model I can click
on<00:05:01.880><c> that</c>

00:05:02.550 --> 00:05:02.560 align:start position:0%
on that
 

00:05:02.560 --> 00:05:05.749 align:start position:0%
on that
artifact<00:05:03.560><c> and</c><00:05:04.400><c> the</c><00:05:04.600><c> next</c><00:05:04.880><c> thing</c><00:05:05.320><c> that</c><00:05:05.479><c> you'll</c>

00:05:05.749 --> 00:05:05.759 align:start position:0%
artifact and the next thing that you'll
 

00:05:05.759 --> 00:05:09.029 align:start position:0%
artifact and the next thing that you'll
notice<00:05:06.199><c> here</c><00:05:06.880><c> is</c><00:05:07.800><c> there's</c><00:05:08.039><c> the</c><00:05:08.240><c> ability</c><00:05:08.840><c> to</c>

00:05:09.029 --> 00:05:09.039 align:start position:0%
notice here is there's the ability to
 

00:05:09.039 --> 00:05:12.189 align:start position:0%
notice here is there's the ability to
link<00:05:09.400><c> this</c><00:05:09.639><c> artifact</c><00:05:10.560><c> to</c><00:05:10.759><c> a</c><00:05:11.039><c> registry</c><00:05:12.039><c> now</c>

00:05:12.189 --> 00:05:12.199 align:start position:0%
link this artifact to a registry now
 

00:05:12.199 --> 00:05:13.469 align:start position:0%
link this artifact to a registry now
there's<00:05:12.360><c> many</c><00:05:12.600><c> different</c><00:05:12.800><c> ways</c><00:05:13.000><c> to</c><00:05:13.120><c> link</c><00:05:13.320><c> to</c><00:05:13.400><c> a</c>

00:05:13.469 --> 00:05:13.479 align:start position:0%
there's many different ways to link to a
 

00:05:13.479 --> 00:05:15.270 align:start position:0%
there's many different ways to link to a
registry<00:05:13.880><c> or</c><00:05:14.039><c> there's</c><00:05:14.320><c> really</c><00:05:14.560><c> two</c><00:05:14.759><c> ways</c><00:05:15.120><c> one</c>

00:05:15.270 --> 00:05:15.280 align:start position:0%
registry or there's really two ways one
 

00:05:15.280 --> 00:05:17.749 align:start position:0%
registry or there's really two ways one
is<00:05:15.400><c> with</c><00:05:15.520><c> the</c><00:05:15.680><c> UI</c><00:05:16.280><c> and</c><00:05:16.400><c> one</c><00:05:16.560><c> is</c><00:05:16.680><c> with</c><00:05:16.880><c> code</c><00:05:17.680><c> I</c>

00:05:17.749 --> 00:05:17.759 align:start position:0%
is with the UI and one is with code I
 

00:05:17.759 --> 00:05:19.710 align:start position:0%
is with the UI and one is with code I
want<00:05:17.880><c> to</c><00:05:18.000><c> show</c><00:05:18.240><c> you</c><00:05:18.680><c> how</c><00:05:18.759><c> to</c><00:05:18.960><c> do</c><00:05:19.120><c> it</c><00:05:19.440><c> with</c><00:05:19.560><c> the</c>

00:05:19.710 --> 00:05:19.720 align:start position:0%
want to show you how to do it with the
 

00:05:19.720 --> 00:05:22.309 align:start position:0%
want to show you how to do it with the
UI<00:05:20.160><c> first</c><00:05:21.120><c> just</c><00:05:21.240><c> so</c><00:05:21.400><c> you</c><00:05:21.520><c> get</c><00:05:21.639><c> a</c><00:05:21.759><c> good</c><00:05:21.919><c> mental</c>

00:05:22.309 --> 00:05:22.319 align:start position:0%
UI first just so you get a good mental
 

00:05:22.319 --> 00:05:24.749 align:start position:0%
UI first just so you get a good mental
model<00:05:22.800><c> of</c><00:05:23.000><c> how</c><00:05:23.160><c> things</c><00:05:23.400><c> work</c><00:05:24.080><c> but</c><00:05:24.240><c> feel</c><00:05:24.479><c> free</c>

00:05:24.749 --> 00:05:24.759 align:start position:0%
model of how things work but feel free
 

00:05:24.759 --> 00:05:26.629 align:start position:0%
model of how things work but feel free
to<00:05:25.400><c> you</c><00:05:25.479><c> know</c><00:05:25.680><c> after</c><00:05:25.840><c> you</c><00:05:26.000><c> learn</c><00:05:26.240><c> how</c><00:05:26.319><c> to</c><00:05:26.479><c> use</c>

00:05:26.629 --> 00:05:26.639 align:start position:0%
to you know after you learn how to use
 

00:05:26.639 --> 00:05:29.749 align:start position:0%
to you know after you learn how to use
the<00:05:27.160><c> UI</c><00:05:28.160><c> uh</c><00:05:28.400><c> you</c><00:05:28.479><c> know</c><00:05:28.600><c> you</c><00:05:28.720><c> can</c><00:05:28.840><c> use</c><00:05:29.000><c> the</c><00:05:29.160><c> code</c>

00:05:29.749 --> 00:05:29.759 align:start position:0%
the UI uh you know you can use the code
 

00:05:29.759 --> 00:05:32.670 align:start position:0%
the UI uh you know you can use the code
you'll<00:05:29.960><c> see</c><00:05:30.240><c> here</c><00:05:30.960><c> the</c><00:05:31.120><c> different</c><00:05:31.680><c> aliases</c>

00:05:32.670 --> 00:05:32.680 align:start position:0%
you'll see here the different aliases
 

00:05:32.680 --> 00:05:35.830 align:start position:0%
you'll see here the different aliases
that<00:05:33.120><c> we</c><00:05:34.120><c> that</c><00:05:34.280><c> we</c><00:05:34.560><c> used</c><00:05:34.840><c> in</c><00:05:34.960><c> our</c><00:05:35.160><c> code</c><00:05:35.600><c> plus</c>

00:05:35.830 --> 00:05:35.840 align:start position:0%
that we that we used in our code plus
 

00:05:35.840 --> 00:05:38.510 align:start position:0%
that we that we used in our code plus
some<00:05:36.080><c> other</c><00:05:36.360><c> default</c><00:05:36.880><c> ones</c><00:05:37.880><c> so</c><00:05:38.120><c> weights</c><00:05:38.360><c> and</c>

00:05:38.510 --> 00:05:38.520 align:start position:0%
some other default ones so weights and
 

00:05:38.520 --> 00:05:41.469 align:start position:0%
some other default ones so weights and
biases<00:05:38.960><c> will</c><00:05:39.120><c> automatically</c><00:05:39.759><c> add</c><00:05:39.960><c> the</c><00:05:40.479><c> latest</c>

00:05:41.469 --> 00:05:41.479 align:start position:0%
biases will automatically add the latest
 

00:05:41.479 --> 00:05:43.749 align:start position:0%
biases will automatically add the latest
and<00:05:42.000><c> have</c><00:05:42.600><c> like</c><00:05:42.759><c> semantic</c>

00:05:43.749 --> 00:05:43.759 align:start position:0%
and have like semantic
 

00:05:43.759 --> 00:05:46.430 align:start position:0%
and have like semantic
versioning<00:05:44.759><c> um</c><00:05:45.240><c> Alias</c><00:05:45.759><c> already</c><00:05:46.000><c> added</c><00:05:46.280><c> for</c>

00:05:46.430 --> 00:05:46.440 align:start position:0%
versioning um Alias already added for
 

00:05:46.440 --> 00:05:48.749 align:start position:0%
versioning um Alias already added for
you<00:05:46.720><c> Noah</c><00:05:47.039><c> do</c><00:05:47.120><c> you</c><00:05:47.199><c> want</c><00:05:47.319><c> to</c><00:05:47.479><c> talk</c><00:05:47.680><c> about</c><00:05:48.319><c> the</c>

00:05:48.749 --> 00:05:48.759 align:start position:0%
you Noah do you want to talk about the
 

00:05:48.759 --> 00:05:51.990 align:start position:0%
you Noah do you want to talk about the
these<00:05:49.160><c> two</c><00:05:49.680><c> tags</c><00:05:50.680><c> these</c><00:05:51.360><c> uh</c><00:05:51.479><c> ones</c><00:05:51.720><c> that</c><00:05:51.800><c> are</c>

00:05:51.990 --> 00:05:52.000 align:start position:0%
these two tags these uh ones that are
 

00:05:52.000 --> 00:05:53.670 align:start position:0%
these two tags these uh ones that are
added<00:05:52.280><c> by</c><00:05:52.440><c> default</c><00:05:52.759><c> by</c><00:05:52.880><c> weights</c><00:05:53.080><c> and</c><00:05:53.199><c> biases</c>

00:05:53.670 --> 00:05:53.680 align:start position:0%
added by default by weights and biases
 

00:05:53.680 --> 00:05:58.590 align:start position:0%
added by default by weights and biases
and<00:05:54.120><c> how</c><00:05:54.319><c> that</c><00:05:54.960><c> works</c><00:05:55.960><c> yeah</c><00:05:56.360><c> sure</c><00:05:57.360><c> so</c><00:05:57.960><c> um</c><00:05:58.240><c> as</c>

00:05:58.590 --> 00:05:58.600 align:start position:0%
and how that works yeah sure so um as
 

00:05:58.600 --> 00:06:01.909 align:start position:0%
and how that works yeah sure so um as
Hamil<00:05:59.000><c> mentioned</c><00:06:00.000><c> the</c><00:06:00.639><c> the</c><00:06:00.840><c> way</c><00:06:01.440><c> we</c><00:06:01.639><c> like</c><00:06:01.800><c> to</c>

00:06:01.909 --> 00:06:01.919 align:start position:0%
Hamil mentioned the the way we like to
 

00:06:01.919 --> 00:06:04.510 align:start position:0%
Hamil mentioned the the way we like to
use<00:06:02.120><c> aliases</c><00:06:02.680><c> or</c><00:06:02.960><c> kind</c><00:06:03.080><c> of</c><00:06:03.280><c> the</c><00:06:03.479><c> the</c><00:06:03.639><c> idea</c><00:06:04.360><c> um</c>

00:06:04.510 --> 00:06:04.520 align:start position:0%
use aliases or kind of the the idea um
 

00:06:04.520 --> 00:06:07.950 align:start position:0%
use aliases or kind of the the idea um
of<00:06:04.639><c> being</c><00:06:04.840><c> able</c><00:06:05.039><c> to</c><00:06:05.240><c> attach</c><00:06:05.680><c> aliases</c><00:06:06.440><c> to</c><00:06:07.360><c> uh</c>

00:06:07.950 --> 00:06:07.960 align:start position:0%
of being able to attach aliases to uh
 

00:06:07.960 --> 00:06:10.830 align:start position:0%
of being able to attach aliases to uh
model<00:06:08.440><c> version</c><00:06:09.360><c> is</c><00:06:09.520><c> to</c><00:06:09.720><c> serve</c><00:06:10.080><c> as</c>

00:06:10.830 --> 00:06:10.840 align:start position:0%
model version is to serve as
 

00:06:10.840 --> 00:06:13.390 align:start position:0%
model version is to serve as
semantically<00:06:11.560><c> friendly</c><00:06:12.479><c> uh</c><00:06:12.599><c> kind</c><00:06:12.720><c> of</c>

00:06:13.390 --> 00:06:13.400 align:start position:0%
semantically friendly uh kind of
 

00:06:13.400 --> 00:06:16.230 align:start position:0%
semantically friendly uh kind of
identifiers<00:06:14.400><c> um</c><00:06:14.560><c> for</c><00:06:14.919><c> a</c><00:06:15.160><c> specific</c><00:06:15.680><c> model</c>

00:06:16.230 --> 00:06:16.240 align:start position:0%
identifiers um for a specific model
 

00:06:16.240 --> 00:06:18.629 align:start position:0%
identifiers um for a specific model
version<00:06:17.240><c> um</c><00:06:17.560><c> so</c><00:06:17.720><c> you</c><00:06:17.840><c> don't</c><00:06:18.080><c> necessarily</c><00:06:18.560><c> have</c>

00:06:18.629 --> 00:06:18.639 align:start position:0%
version um so you don't necessarily have
 

00:06:18.639 --> 00:06:21.550 align:start position:0%
version um so you don't necessarily have
to<00:06:18.840><c> remember</c><00:06:19.520><c> oh</c><00:06:19.960><c> like</c><00:06:20.360><c> this</c><00:06:20.479><c> is</c><00:06:21.160><c> the</c><00:06:21.400><c> the</c>

00:06:21.550 --> 00:06:21.560 align:start position:0%
to remember oh like this is the the
 

00:06:21.560 --> 00:06:23.430 align:start position:0%
to remember oh like this is the the
third<00:06:21.880><c> version</c><00:06:22.160><c> of</c><00:06:22.360><c> this</c><00:06:22.520><c> artifact</c><00:06:23.039><c> that</c><00:06:23.199><c> I'm</c>

00:06:23.430 --> 00:06:23.440 align:start position:0%
third version of this artifact that I'm
 

00:06:23.440 --> 00:06:26.309 align:start position:0%
third version of this artifact that I'm
I'm<00:06:23.639><c> working</c><00:06:23.960><c> on</c><00:06:24.840><c> um</c><00:06:25.000><c> so</c><00:06:25.479><c> in</c><00:06:25.599><c> this</c><00:06:25.840><c> specific</c>

00:06:26.309 --> 00:06:26.319 align:start position:0%
I'm working on um so in this specific
 

00:06:26.319 --> 00:06:29.909 align:start position:0%
I'm working on um so in this specific
case<00:06:26.759><c> our</c><00:06:27.280><c> artifact</c><00:06:27.840><c> only</c><00:06:28.199><c> has</c><00:06:28.599><c> one</c><00:06:28.919><c> version</c>

00:06:29.909 --> 00:06:29.919 align:start position:0%
case our artifact only has one version
 

00:06:29.919 --> 00:06:31.909 align:start position:0%
case our artifact only has one version
um<00:06:30.039><c> but</c><00:06:30.160><c> you</c><00:06:30.280><c> can</c><00:06:30.440><c> imagine</c><00:06:30.720><c> a</c><00:06:30.960><c> case</c><00:06:31.319><c> where</c>

00:06:31.909 --> 00:06:31.919 align:start position:0%
um but you can imagine a case where
 

00:06:31.919 --> 00:06:33.550 align:start position:0%
um but you can imagine a case where
you've<00:06:32.199><c> done</c><00:06:32.400><c> a</c><00:06:32.520><c> bunch</c><00:06:32.680><c> of</c><00:06:32.800><c> versioning</c><00:06:33.319><c> maybe</c>

00:06:33.550 --> 00:06:33.560 align:start position:0%
you've done a bunch of versioning maybe
 

00:06:33.560 --> 00:06:35.909 align:start position:0%
you've done a bunch of versioning maybe
tuned<00:06:34.000><c> some</c><00:06:34.160><c> of</c><00:06:34.319><c> the</c><00:06:34.639><c> hyperparameters</c><00:06:35.639><c> or</c>

00:06:35.909 --> 00:06:35.919 align:start position:0%
tuned some of the hyperparameters or
 

00:06:35.919 --> 00:06:38.950 align:start position:0%
tuned some of the hyperparameters or
you're<00:06:36.240><c> kind</c><00:06:36.400><c> of</c><00:06:37.080><c> um</c><00:06:37.280><c> logging</c><00:06:38.280><c> a</c><00:06:38.440><c> model</c><00:06:38.759><c> at</c>

00:06:38.950 --> 00:06:38.960 align:start position:0%
you're kind of um logging a model at
 

00:06:38.960 --> 00:06:41.270 align:start position:0%
you're kind of um logging a model at
every<00:06:39.520><c> uh</c><00:06:39.680><c> checkpoint</c><00:06:40.479><c> um</c><00:06:40.599><c> and</c><00:06:40.680><c> so</c><00:06:40.840><c> you</c><00:06:41.000><c> might</c>

00:06:41.270 --> 00:06:41.280 align:start position:0%
every uh checkpoint um and so you might
 

00:06:41.280 --> 00:06:43.670 align:start position:0%
every uh checkpoint um and so you might
have<00:06:41.639><c> you</c><00:06:41.759><c> know</c><00:06:42.080><c> an</c><00:06:42.240><c> artifact</c><00:06:42.720><c> with</c><00:06:42.960><c> a</c><00:06:43.120><c> 100</c>

00:06:43.670 --> 00:06:43.680 align:start position:0%
have you know an artifact with a 100
 

00:06:43.680 --> 00:06:46.990 align:start position:0%
have you know an artifact with a 100
checkpoints<00:06:44.400><c> and</c><00:06:45.000><c> aliases</c><00:06:45.599><c> are</c><00:06:45.720><c> a</c><00:06:45.880><c> way</c><00:06:46.080><c> to</c><00:06:46.319><c> use</c>

00:06:46.990 --> 00:06:47.000 align:start position:0%
checkpoints and aliases are a way to use
 

00:06:47.000 --> 00:06:49.150 align:start position:0%
checkpoints and aliases are a way to use
again<00:06:47.319><c> a</c><00:06:47.560><c> semantically</c><00:06:48.560><c> uh</c><00:06:48.680><c> friendly</c>

00:06:49.150 --> 00:06:49.160 align:start position:0%
again a semantically uh friendly
 

00:06:49.160 --> 00:06:51.110 align:start position:0%
again a semantically uh friendly
identifier<00:06:49.880><c> so</c><00:06:50.039><c> that</c><00:06:50.199><c> later</c><00:06:50.479><c> in</c><00:06:50.599><c> your</c><00:06:50.800><c> code</c>

00:06:51.110 --> 00:06:51.120 align:start position:0%
identifier so that later in your code
 

00:06:51.120 --> 00:06:55.029 align:start position:0%
identifier so that later in your code
you<00:06:51.240><c> can</c><00:06:51.759><c> reference</c><00:06:52.759><c> um</c><00:06:53.080><c> the</c><00:06:53.319><c> the</c><00:06:53.720><c> artifact</c><00:06:54.720><c> um</c>

00:06:55.029 --> 00:06:55.039 align:start position:0%
you can reference um the the artifact um
 

00:06:55.039 --> 00:06:56.670 align:start position:0%
you can reference um the the artifact um
rather<00:06:55.280><c> than</c><00:06:55.479><c> remembering</c><00:06:56.000><c> the</c><00:06:56.199><c> specific</c>

00:06:56.670 --> 00:06:56.680 align:start position:0%
rather than remembering the specific
 

00:06:56.680 --> 00:06:59.990 align:start position:0%
rather than remembering the specific
version<00:06:57.360><c> number</c><00:06:58.360><c> um</c><00:06:58.479><c> so</c><00:06:58.680><c> latest</c><00:06:59.360><c> is</c><00:06:59.720><c> one</c><00:06:59.840><c> of</c>

00:06:59.990 --> 00:07:00.000 align:start position:0%
version number um so latest is one of
 

00:07:00.000 --> 00:07:04.510 align:start position:0%
version number um so latest is one of
the<00:07:00.240><c> the</c><00:07:00.400><c> default</c><00:07:01.800><c> aliases</c><00:07:02.800><c> and</c><00:07:03.479><c> we</c><00:07:04.199><c> add</c>

00:07:04.510 --> 00:07:04.520 align:start position:0%
the the default aliases and we add
 

00:07:04.520 --> 00:07:06.830 align:start position:0%
the the default aliases and we add
aliases<00:07:05.479><c> and</c><00:07:06.039><c> essentially</c><00:07:06.400><c> make</c><00:07:06.520><c> sure</c><00:07:06.720><c> that</c>

00:07:06.830 --> 00:07:06.840 align:start position:0%
aliases and essentially make sure that
 

00:07:06.840 --> 00:07:09.390 align:start position:0%
aliases and essentially make sure that
there's<00:07:07.120><c> always</c><00:07:08.120><c> a</c><00:07:08.280><c> pointer</c><00:07:08.639><c> to</c><00:07:08.800><c> the</c><00:07:08.960><c> latest</c>

00:07:09.390 --> 00:07:09.400 align:start position:0%
there's always a pointer to the latest
 

00:07:09.400 --> 00:07:12.029 align:start position:0%
there's always a pointer to the latest
Alias<00:07:10.240><c> the</c><00:07:10.360><c> latest</c><00:07:10.720><c> Alias</c><00:07:11.160><c> serves</c><00:07:11.720><c> as</c><00:07:11.840><c> a</c>

00:07:12.029 --> 00:07:12.039 align:start position:0%
Alias the latest Alias serves as a
 

00:07:12.039 --> 00:07:15.070 align:start position:0%
Alias the latest Alias serves as a
pointer<00:07:12.440><c> to</c><00:07:13.120><c> the</c><00:07:13.360><c> most</c><00:07:13.759><c> recent</c><00:07:14.520><c> uh</c><00:07:14.680><c> model</c>

00:07:15.070 --> 00:07:15.080 align:start position:0%
pointer to the most recent uh model
 

00:07:15.080 --> 00:07:18.430 align:start position:0%
pointer to the most recent uh model
version<00:07:15.680><c> that</c><00:07:15.800><c> you</c><00:07:16.000><c> added</c><00:07:16.400><c> to</c><00:07:16.680><c> an</c><00:07:17.240><c> artifact</c><00:07:18.240><c> um</c>

00:07:18.430 --> 00:07:18.440 align:start position:0%
version that you added to an artifact um
 

00:07:18.440 --> 00:07:21.309 align:start position:0%
version that you added to an artifact um
and<00:07:18.520><c> so</c><00:07:18.840><c> this</c><00:07:19.080><c> way</c><00:07:20.000><c> you</c><00:07:20.120><c> know</c><00:07:20.280><c> wherever</c><00:07:21.240><c> if</c>

00:07:21.309 --> 00:07:21.319 align:start position:0%
and so this way you know wherever if
 

00:07:21.319 --> 00:07:24.029 align:start position:0%
and so this way you know wherever if
you're<00:07:21.639><c> referencing</c><00:07:22.639><c> or</c><00:07:22.840><c> using</c><00:07:23.319><c> this</c><00:07:23.720><c> model</c>

00:07:24.029 --> 00:07:24.039 align:start position:0%
you're referencing or using this model
 

00:07:24.039 --> 00:07:26.430 align:start position:0%
you're referencing or using this model
in<00:07:24.199><c> your</c><00:07:24.400><c> code</c><00:07:25.240><c> um</c><00:07:25.400><c> you</c><00:07:25.479><c> can</c><00:07:25.680><c> use</c><00:07:25.919><c> the</c><00:07:26.039><c> latest</c>

00:07:26.430 --> 00:07:26.440 align:start position:0%
in your code um you can use the latest
 

00:07:26.440 --> 00:07:28.749 align:start position:0%
in your code um you can use the latest
Alias<00:07:27.000><c> to</c><00:07:27.160><c> make</c><00:07:27.319><c> sure</c><00:07:27.560><c> that</c><00:07:27.840><c> as</c><00:07:28.120><c> the</c><00:07:28.360><c> the</c><00:07:28.479><c> model</c>

00:07:28.749 --> 00:07:28.759 align:start position:0%
Alias to make sure that as the the model
 

00:07:28.759 --> 00:07:30.150 align:start position:0%
Alias to make sure that as the the model
is<00:07:28.919><c> being</c><00:07:29.120><c> versioned</c>

00:07:30.150 --> 00:07:30.160 align:start position:0%
is being versioned
 

00:07:30.160 --> 00:07:32.670 align:start position:0%
is being versioned
um<00:07:30.360><c> that</c><00:07:30.479><c> there's</c><00:07:30.800><c> kind</c><00:07:30.919><c> of</c><00:07:31.520><c> always</c><00:07:31.919><c> a</c><00:07:32.080><c> way</c><00:07:32.319><c> to</c>

00:07:32.670 --> 00:07:32.680 align:start position:0%
um that there's kind of always a way to
 

00:07:32.680 --> 00:07:34.189 align:start position:0%
um that there's kind of always a way to
make<00:07:32.840><c> sure</c><00:07:33.000><c> that</c><00:07:33.080><c> you're</c><00:07:33.319><c> pointing</c><00:07:33.720><c> to</c><00:07:34.000><c> the</c>

00:07:34.189 --> 00:07:34.199 align:start position:0%
make sure that you're pointing to the
 

00:07:34.199 --> 00:07:37.350 align:start position:0%
make sure that you're pointing to the
the<00:07:34.319><c> most</c><00:07:34.639><c> recent</c><00:07:35.479><c> version</c><00:07:36.479><c> um</c><00:07:36.800><c> in</c><00:07:36.960><c> the</c><00:07:37.080><c> other</c>

00:07:37.350 --> 00:07:37.360 align:start position:0%
the most recent version um in the other
 

00:07:37.360 --> 00:07:40.710 align:start position:0%
the most recent version um in the other
Alias<00:07:37.919><c> here</c><00:07:38.199><c> that</c><00:07:38.319><c> we</c><00:07:38.520><c> also</c><00:07:38.879><c> add</c><00:07:39.599><c> um</c><00:07:39.960><c> Again</c><00:07:40.319><c> by</c>

00:07:40.710 --> 00:07:40.720 align:start position:0%
Alias here that we also add um Again by
 

00:07:40.720 --> 00:07:42.589 align:start position:0%
Alias here that we also add um Again by
by<00:07:40.879><c> default</c><00:07:41.440><c> is</c><00:07:41.639><c> what</c><00:07:41.759><c> you'll</c><00:07:41.919><c> see</c><00:07:42.120><c> like</c><00:07:42.400><c> the</c>

00:07:42.589 --> 00:07:42.599 align:start position:0%
by default is what you'll see like the
 

00:07:42.599 --> 00:07:46.070 align:start position:0%
by default is what you'll see like the
the<00:07:42.720><c> v</c><00:07:43.520><c> v0</c><00:07:44.520><c> um</c><00:07:44.759><c> so</c><00:07:45.080><c> as</c><00:07:45.240><c> you</c><00:07:45.520><c> increase</c><00:07:45.919><c> the</c>

00:07:46.070 --> 00:07:46.080 align:start position:0%
the v v0 um so as you increase the
 

00:07:46.080 --> 00:07:48.309 align:start position:0%
the v v0 um so as you increase the
number<00:07:46.400><c> of</c><00:07:46.720><c> versions</c><00:07:47.720><c> um</c><00:07:47.879><c> we're</c><00:07:48.039><c> going</c><00:07:48.159><c> to</c>

00:07:48.309 --> 00:07:48.319 align:start position:0%
number of versions um we're going to
 

00:07:48.319 --> 00:07:51.550 align:start position:0%
number of versions um we're going to
keep<00:07:48.680><c> count</c><00:07:49.199><c> and</c><00:07:49.479><c> automatically</c><00:07:50.560><c> increment</c>

00:07:51.550 --> 00:07:51.560 align:start position:0%
keep count and automatically increment
 

00:07:51.560 --> 00:07:53.990 align:start position:0%
keep count and automatically increment
um<00:07:51.840><c> as</c><00:07:52.039><c> any</c><00:07:52.199><c> of</c><00:07:52.360><c> the</c><00:07:52.560><c> files</c><00:07:53.039><c> change</c><00:07:53.639><c> in</c><00:07:53.800><c> your</c>

00:07:53.990 --> 00:07:54.000 align:start position:0%
um as any of the files change in your
 

00:07:54.000 --> 00:07:56.350 align:start position:0%
um as any of the files change in your
artifact<00:07:54.960><c> um</c><00:07:55.080><c> will</c><00:07:55.280><c> increment</c><00:07:55.919><c> kind</c><00:07:56.039><c> of</c><00:07:56.159><c> to</c>

00:07:56.350 --> 00:07:56.360 align:start position:0%
artifact um will increment kind of to
 

00:07:56.360 --> 00:08:00.680 align:start position:0%
artifact um will increment kind of to
the<00:07:56.639><c> the</c><00:07:56.800><c> next</c><00:07:57.240><c> the</c><00:07:57.440><c> next</c><00:07:57.680><c> version</c>

