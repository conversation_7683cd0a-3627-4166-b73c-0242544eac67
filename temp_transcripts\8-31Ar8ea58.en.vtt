WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.990 align:start position:0%
 
welcome<00:00:00.160><c> to</c><00:00:00.320><c> another</c><00:00:00.719><c> very</c><00:00:01.120><c> exciting</c><00:00:01.599><c> video</c>

00:00:01.990 --> 00:00:02.000 align:start position:0%
welcome to another very exciting video
 

00:00:02.000 --> 00:00:03.990 align:start position:0%
welcome to another very exciting video
series<00:00:02.399><c> in</c><00:00:02.560><c> this</c><00:00:03.120><c> series</c><00:00:03.520><c> we're</c><00:00:03.679><c> going</c><00:00:03.760><c> to</c><00:00:03.840><c> be</c>

00:00:03.990 --> 00:00:04.000 align:start position:0%
series in this series we're going to be
 

00:00:04.000 --> 00:00:06.150 align:start position:0%
series in this series we're going to be
talking<00:00:04.560><c> about</c><00:00:05.040><c> intercom</c>

00:00:06.150 --> 00:00:06.160 align:start position:0%
talking about intercom
 

00:00:06.160 --> 00:00:07.510 align:start position:0%
talking about intercom
intercom<00:00:06.720><c> is</c><00:00:06.799><c> really</c><00:00:07.120><c> awesome</c><00:00:07.359><c> we're</c>

00:00:07.510 --> 00:00:07.520 align:start position:0%
intercom is really awesome we're
 

00:00:07.520 --> 00:00:09.030 align:start position:0%
intercom is really awesome we're
actually<00:00:07.759><c> going</c><00:00:07.839><c> to</c><00:00:07.919><c> be</c><00:00:08.080><c> using</c><00:00:08.400><c> some</c><00:00:08.559><c> of</c><00:00:08.880><c> the</c>

00:00:09.030 --> 00:00:09.040 align:start position:0%
actually going to be using some of the
 

00:00:09.040 --> 00:00:11.030 align:start position:0%
actually going to be using some of the
existing<00:00:09.679><c> technologies</c><00:00:10.320><c> that</c><00:00:10.480><c> we</c><00:00:10.639><c> used</c><00:00:10.960><c> in</c>

00:00:11.030 --> 00:00:11.040 align:start position:0%
existing technologies that we used in
 

00:00:11.040 --> 00:00:12.709 align:start position:0%
existing technologies that we used in
some<00:00:11.200><c> of</c><00:00:11.360><c> our</c><00:00:11.440><c> previous</c><00:00:11.920><c> videos</c><00:00:12.400><c> including</c>

00:00:12.709 --> 00:00:12.719 align:start position:0%
some of our previous videos including
 

00:00:12.719 --> 00:00:14.910 align:start position:0%
some of our previous videos including
the<00:00:12.880><c> metabase</c>

00:00:14.910 --> 00:00:14.920 align:start position:0%
the metabase
 

00:00:14.920 --> 00:00:18.230 align:start position:0%
the metabase
um<00:00:15.920><c> the</c><00:00:16.080><c> metabase</c><00:00:16.720><c> deployment</c><00:00:17.440><c> and</c><00:00:17.760><c> metabase</c>

00:00:18.230 --> 00:00:18.240 align:start position:0%
um the metabase deployment and metabase
 

00:00:18.240 --> 00:00:20.790 align:start position:0%
um the metabase deployment and metabase
is<00:00:18.400><c> good</c><00:00:18.560><c> for</c><00:00:18.800><c> data</c><00:00:19.119><c> visualization</c><00:00:20.240><c> and</c><00:00:20.560><c> we're</c>

00:00:20.790 --> 00:00:20.800 align:start position:0%
is good for data visualization and we're
 

00:00:20.800 --> 00:00:22.790 align:start position:0%
is good for data visualization and we're
also<00:00:21.359><c> uh</c><00:00:21.680><c> we</c><00:00:21.840><c> may</c><00:00:22.000><c> even</c><00:00:22.240><c> talk</c><00:00:22.400><c> about</c><00:00:22.560><c> this</c>

00:00:22.790 --> 00:00:22.800 align:start position:0%
also uh we may even talk about this
 

00:00:22.800 --> 00:00:24.390 align:start position:0%
also uh we may even talk about this
github<00:00:23.279><c> repo</c>

00:00:24.390 --> 00:00:24.400 align:start position:0%
github repo
 

00:00:24.400 --> 00:00:27.509 align:start position:0%
github repo
eventually<00:00:25.760><c> that's</c><00:00:26.000><c> another</c><00:00:26.400><c> ongoing</c><00:00:26.960><c> series</c>

00:00:27.509 --> 00:00:27.519 align:start position:0%
eventually that's another ongoing series
 

00:00:27.519 --> 00:00:29.349 align:start position:0%
eventually that's another ongoing series
it's<00:00:27.760><c> called</c><00:00:28.320><c> the</c>

00:00:29.349 --> 00:00:29.359 align:start position:0%
it's called the
 

00:00:29.359 --> 00:00:31.830 align:start position:0%
it's called the
modern<00:00:29.679><c> day</c><00:00:29.920><c> cms</c><00:00:30.560><c> project</c><00:00:31.039><c> and</c><00:00:31.119><c> we</c><00:00:31.279><c> might</c><00:00:31.519><c> even</c>

00:00:31.830 --> 00:00:31.840 align:start position:0%
modern day cms project and we might even
 

00:00:31.840 --> 00:00:34.069 align:start position:0%
modern day cms project and we might even
integrate<00:00:32.239><c> the</c><00:00:32.399><c> intercom</c><00:00:32.960><c> code</c><00:00:33.600><c> into</c><00:00:33.840><c> this</c>

00:00:34.069 --> 00:00:34.079 align:start position:0%
integrate the intercom code into this
 

00:00:34.079 --> 00:00:36.389 align:start position:0%
integrate the intercom code into this
project<00:00:34.559><c> eventually</c><00:00:35.200><c> but</c><00:00:35.360><c> first</c><00:00:36.000><c> we</c><00:00:36.160><c> need</c><00:00:36.320><c> to</c>

00:00:36.389 --> 00:00:36.399 align:start position:0%
project eventually but first we need to
 

00:00:36.399 --> 00:00:39.990 align:start position:0%
project eventually but first we need to
go<00:00:36.559><c> to</c><00:00:36.719><c> intercom</c><00:00:37.360><c> and</c><00:00:37.680><c> very</c><00:00:38.000><c> simply</c><00:00:38.480><c> you</c><00:00:38.640><c> just</c>

00:00:39.990 --> 00:00:40.000 align:start position:0%
go to intercom and very simply you just
 

00:00:40.000 --> 00:00:42.069 align:start position:0%
go to intercom and very simply you just
sign<00:00:40.320><c> up</c><00:00:40.640><c> and</c><00:00:40.879><c> this</c><00:00:41.120><c> is</c><00:00:41.200><c> the</c><00:00:41.520><c> page</c><00:00:41.760><c> that</c><00:00:41.920><c> you're</c>

00:00:42.069 --> 00:00:42.079 align:start position:0%
sign up and this is the page that you're
 

00:00:42.079 --> 00:00:43.750 align:start position:0%
sign up and this is the page that you're
going<00:00:42.160><c> to</c><00:00:42.399><c> get</c><00:00:42.640><c> taken</c><00:00:42.960><c> to</c><00:00:43.200><c> everything</c><00:00:43.600><c> is</c>

00:00:43.750 --> 00:00:43.760 align:start position:0%
going to get taken to everything is
 

00:00:43.760 --> 00:00:46.389 align:start position:0%
going to get taken to everything is
totally<00:00:44.399><c> new</c><00:00:45.120><c> and</c><00:00:45.360><c> i</c><00:00:45.440><c> want</c><00:00:45.600><c> to</c><00:00:45.680><c> do</c><00:00:45.840><c> customize</c>

00:00:46.389 --> 00:00:46.399 align:start position:0%
totally new and i want to do customize
 

00:00:46.399 --> 00:00:48.869 align:start position:0%
totally new and i want to do customize
the<00:00:46.559><c> interco</c><00:00:47.520><c> install</c><00:00:47.920><c> intercom</c><00:00:48.480><c> sync</c><00:00:48.719><c> your</c>

00:00:48.869 --> 00:00:48.879 align:start position:0%
the interco install intercom sync your
 

00:00:48.879 --> 00:00:51.430 align:start position:0%
the interco install intercom sync your
data<00:00:49.200><c> and</c><00:00:49.280><c> install</c><00:00:49.680><c> intercom</c><00:00:50.239><c> messenger</c>

00:00:51.430 --> 00:00:51.440 align:start position:0%
data and install intercom messenger
 

00:00:51.440 --> 00:00:53.510 align:start position:0%
data and install intercom messenger
okay<00:00:51.760><c> so</c><00:00:51.920><c> we</c><00:00:52.079><c> want</c><00:00:52.239><c> to</c><00:00:52.399><c> do</c><00:00:52.559><c> this</c><00:00:52.960><c> to</c><00:00:53.120><c> install</c>

00:00:53.510 --> 00:00:53.520 align:start position:0%
okay so we want to do this to install
 

00:00:53.520 --> 00:00:55.189 align:start position:0%
okay so we want to do this to install
intercom<00:00:53.920><c> choose</c><00:00:54.160><c> how</c><00:00:54.320><c> you</c>

00:00:55.189 --> 00:00:55.199 align:start position:0%
intercom choose how you
 

00:00:55.199 --> 00:00:58.389 align:start position:0%
intercom choose how you
you<00:00:55.360><c> want</c><00:00:55.520><c> to</c><00:00:55.680><c> sync</c><00:00:56.000><c> or</c><00:00:56.160><c> connect</c><00:00:56.480><c> your</c><00:00:56.719><c> data</c>

00:00:58.389 --> 00:00:58.399 align:start position:0%
you want to sync or connect your data
 

00:00:58.399 --> 00:00:59.189 align:start position:0%
you want to sync or connect your data
so

00:00:59.189 --> 00:00:59.199 align:start position:0%
so
 

00:00:59.199 --> 00:01:00.630 align:start position:0%
so
let's<00:00:59.440><c> see</c>

00:01:00.630 --> 00:01:00.640 align:start position:0%
let's see
 

00:01:00.640 --> 00:01:02.790 align:start position:0%
let's see
i<00:01:00.800><c> want</c><00:01:00.879><c> to</c><00:01:00.960><c> do</c><00:01:01.120><c> it</c><00:01:01.280><c> via</c><00:01:01.520><c> a</c><00:01:01.840><c> single</c><00:01:02.160><c> page</c><00:01:02.480><c> web</c>

00:01:02.790 --> 00:01:02.800 align:start position:0%
i want to do it via a single page web
 

00:01:02.800 --> 00:01:04.630 align:start position:0%
i want to do it via a single page web
app

00:01:04.630 --> 00:01:04.640 align:start position:0%
app
 

00:01:04.640 --> 00:01:05.990 align:start position:0%
app
and<00:01:04.960><c> to</c><00:01:05.040><c> install</c><00:01:05.439><c> the</c><00:01:05.519><c> countries</c><00:01:05.840><c> that</c><00:01:05.920><c> you</c>

00:01:05.990 --> 00:01:06.000 align:start position:0%
and to install the countries that you
 

00:01:06.000 --> 00:01:07.270 align:start position:0%
and to install the countries that you
want<00:01:06.080><c> to</c><00:01:06.159><c> sync</c><00:01:06.320><c> or</c><00:01:06.400><c> connect</c><00:01:06.640><c> your</c><00:01:06.799><c> data</c><00:01:07.040><c> choose</c>

00:01:07.270 --> 00:01:07.280 align:start position:0%
want to sync or connect your data choose
 

00:01:07.280 --> 00:01:08.870 align:start position:0%
want to sync or connect your data choose
this<00:01:07.520><c> method</c><00:01:07.840><c> if</c><00:01:07.920><c> your</c><00:01:08.080><c> web</c><00:01:08.320><c> app</c><00:01:08.479><c> uses</c><00:01:08.720><c> any</c>

00:01:08.870 --> 00:01:08.880 align:start position:0%
this method if your web app uses any
 

00:01:08.880 --> 00:01:10.310 align:start position:0%
this method if your web app uses any
synchronous<00:01:09.360><c> javascript</c><00:01:09.920><c> and</c><00:01:10.000><c> rarely</c>

00:01:10.310 --> 00:01:10.320 align:start position:0%
synchronous javascript and rarely
 

00:01:10.320 --> 00:01:12.550 align:start position:0%
synchronous javascript and rarely
triggers<00:01:10.720><c> full</c><00:01:10.960><c> page</c><00:01:11.200><c> refreshes</c>

00:01:12.550 --> 00:01:12.560 align:start position:0%
triggers full page refreshes
 

00:01:12.560 --> 00:01:14.070 align:start position:0%
triggers full page refreshes
add<00:01:12.720><c> this</c><00:01:12.960><c> code</c><00:01:13.200><c> on</c><00:01:13.360><c> every</c><00:01:13.520><c> page</c><00:01:13.760><c> you'd</c><00:01:13.920><c> like</c>

00:01:14.070 --> 00:01:14.080 align:start position:0%
add this code on every page you'd like
 

00:01:14.080 --> 00:01:15.590 align:start position:0%
add this code on every page you'd like
to<00:01:14.159><c> display</c><00:01:14.479><c> intercom</c><00:01:14.880><c> messenger</c><00:01:15.280><c> copy</c><00:01:15.520><c> and</c>

00:01:15.590 --> 00:01:15.600 align:start position:0%
to display intercom messenger copy and
 

00:01:15.600 --> 00:01:17.590 align:start position:0%
to display intercom messenger copy and
paste<00:01:15.759><c> it</c><00:01:15.840><c> right</c><00:01:16.000><c> before</c><00:01:16.320><c> the</c><00:01:16.720><c> slash</c><00:01:17.119><c> head</c><00:01:17.360><c> tag</c>

00:01:17.590 --> 00:01:17.600 align:start position:0%
paste it right before the slash head tag
 

00:01:17.600 --> 00:01:20.710 align:start position:0%
paste it right before the slash head tag
on<00:01:17.759><c> each</c><00:01:18.000><c> page</c><00:01:18.720><c> copy</c><00:01:19.119><c> code</c><00:01:19.520><c> okay</c><00:01:20.000><c> and</c>

00:01:20.710 --> 00:01:20.720 align:start position:0%
on each page copy code okay and
 

00:01:20.720 --> 00:01:23.590 align:start position:0%
on each page copy code okay and
luckily<00:01:21.119><c> for</c><00:01:21.280><c> us</c><00:01:21.439><c> if</c><00:01:21.520><c> you've</c><00:01:21.680><c> been</c><00:01:21.920><c> following</c>

00:01:23.590 --> 00:01:23.600 align:start position:0%
luckily for us if you've been following
 

00:01:23.600 --> 00:01:26.390 align:start position:0%
luckily for us if you've been following
with<00:01:24.240><c> uh</c><00:01:24.720><c> along</c><00:01:25.119><c> with</c><00:01:25.520><c> our</c>

00:01:26.390 --> 00:01:26.400 align:start position:0%
with uh along with our
 

00:01:26.400 --> 00:01:31.990 align:start position:0%
with uh along with our
modern<00:01:26.720><c> day</c><00:01:26.960><c> cms</c><00:01:27.600><c> project</c>

00:01:31.990 --> 00:01:32.000 align:start position:0%
 
 

00:01:32.000 --> 00:01:33.670 align:start position:0%
 
we<00:01:32.240><c> have</c>

00:01:33.670 --> 00:01:33.680 align:start position:0%
we have
 

00:01:33.680 --> 00:01:35.990 align:start position:0%
we have
a<00:01:33.840><c> head</c><00:01:34.400><c> that</c><00:01:34.560><c> goes</c><00:01:34.960><c> on</c><00:01:35.119><c> every</c><00:01:35.360><c> single</c><00:01:35.680><c> page</c>

00:01:35.990 --> 00:01:36.000 align:start position:0%
a head that goes on every single page
 

00:01:36.000 --> 00:01:38.390 align:start position:0%
a head that goes on every single page
this<00:01:36.159><c> is</c><00:01:36.240><c> a</c><00:01:36.400><c> beautiful</c><00:01:36.720><c> thing</c><00:01:37.040><c> cd</c>

00:01:38.390 --> 00:01:38.400 align:start position:0%
this is a beautiful thing cd
 

00:01:38.400 --> 00:01:41.590 align:start position:0%
this is a beautiful thing cd
my<00:01:38.640><c> sql</c><00:01:39.040><c> node</c><00:01:39.360><c> angular</c><00:01:40.320><c> and</c>

00:01:41.590 --> 00:01:41.600 align:start position:0%
my sql node angular and
 

00:01:41.600 --> 00:01:43.030 align:start position:0%
my sql node angular and
nodemon

00:01:43.030 --> 00:01:43.040 align:start position:0%
nodemon
 

00:01:43.040 --> 00:01:44.550 align:start position:0%
nodemon
server

00:01:44.550 --> 00:01:44.560 align:start position:0%
server
 

00:01:44.560 --> 00:01:46.710 align:start position:0%
server
dot<00:01:44.799><c> js</c>

00:01:46.710 --> 00:01:46.720 align:start position:0%
dot js
 

00:01:46.720 --> 00:01:49.670 align:start position:0%
dot js
and<00:01:47.040><c> let's</c><00:01:47.280><c> open</c><00:01:47.600><c> up</c>

00:01:49.670 --> 00:01:49.680 align:start position:0%
and let's open up
 

00:01:49.680 --> 00:01:50.480 align:start position:0%
and let's open up
the

00:01:50.480 --> 00:01:50.490 align:start position:0%
the
 

00:01:50.490 --> 00:01:52.389 align:start position:0%
the
[Music]

00:01:52.389 --> 00:01:52.399 align:start position:0%
[Music]
 

00:01:52.399 --> 00:02:01.429 align:start position:0%
[Music]
sublime

00:02:01.429 --> 00:02:01.439 align:start position:0%
 
 

00:02:01.439 --> 00:02:08.949 align:start position:0%
 
okay

00:02:08.949 --> 00:02:08.959 align:start position:0%
 
 

00:02:08.959 --> 00:02:11.750 align:start position:0%
 
okay<00:02:09.280><c> corey.js</c><00:02:10.319><c> we</c><00:02:10.479><c> go</c><00:02:10.720><c> into</c><00:02:10.959><c> our</c><00:02:11.200><c> partials</c>

00:02:11.750 --> 00:02:11.760 align:start position:0%
okay corey.js we go into our partials
 

00:02:11.760 --> 00:02:12.790 align:start position:0%
okay corey.js we go into our partials
folder

00:02:12.790 --> 00:02:12.800 align:start position:0%
folder
 

00:02:12.800 --> 00:02:14.869 align:start position:0%
folder
and<00:02:13.120><c> we</c><00:02:13.280><c> said</c><00:02:13.520><c> right</c><00:02:13.680><c> before</c><00:02:14.000><c> that</c><00:02:14.319><c> closing</c>

00:02:14.869 --> 00:02:14.879 align:start position:0%
and we said right before that closing
 

00:02:14.879 --> 00:02:16.630 align:start position:0%
and we said right before that closing
head<00:02:15.120><c> tag</c><00:02:15.360><c> so</c><00:02:15.520><c> that's</c><00:02:15.680><c> got</c><00:02:15.840><c> to</c><00:02:15.920><c> be</c>

00:02:16.630 --> 00:02:16.640 align:start position:0%
head tag so that's got to be
 

00:02:16.640 --> 00:02:20.150 align:start position:0%
head tag so that's got to be
over<00:02:16.959><c> here</c><00:02:17.680><c> okay</c>

00:02:20.150 --> 00:02:20.160 align:start position:0%
 
 

00:02:20.160 --> 00:02:23.599 align:start position:0%
 
ready<00:02:20.480><c> for</c><00:02:20.640><c> our</c><00:02:20.720><c> closing</c><00:02:21.120><c> head</c><00:02:21.360><c> tag</c>

