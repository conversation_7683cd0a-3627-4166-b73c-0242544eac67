WEBVTT
Kind: captions
Language: en

00:00:00.280 --> 00:00:02.270 align:start position:0%
 
and<00:00:00.560><c> howdy</c><00:00:01.000><c> guys</c><00:00:01.240><c> so</c><00:00:01.680><c> this</c><00:00:01.800><c> is</c><00:00:01.920><c> going</c><00:00:02.040><c> to</c><00:00:02.120><c> be</c>

00:00:02.270 --> 00:00:02.280 align:start position:0%
and howdy guys so this is going to be
 

00:00:02.280 --> 00:00:04.950 align:start position:0%
and howdy guys so this is going to be
the<00:00:02.399><c> final</c><00:00:02.720><c> video</c><00:00:03.000><c> in</c><00:00:03.120><c> the</c><00:00:03.320><c> series</c><00:00:04.279><c> in</c><00:00:04.480><c> this</c><00:00:04.839><c> uh</c>

00:00:04.950 --> 00:00:04.960 align:start position:0%
the final video in the series in this uh
 

00:00:04.960 --> 00:00:06.550 align:start position:0%
the final video in the series in this uh
video<00:00:05.240><c> we're</c><00:00:05.480><c> answering</c><00:00:05.879><c> the</c><00:00:06.040><c> question</c><00:00:06.440><c> what</c>

00:00:06.550 --> 00:00:06.560 align:start position:0%
video we're answering the question what
 

00:00:06.560 --> 00:00:08.430 align:start position:0%
video we're answering the question what
are<00:00:06.720><c> the</c><00:00:06.960><c> three</c><00:00:07.240><c> things</c><00:00:07.520><c> you</c><00:00:07.640><c> would</c><00:00:07.879><c> suggest</c>

00:00:08.430 --> 00:00:08.440 align:start position:0%
are the three things you would suggest
 

00:00:08.440 --> 00:00:10.990 align:start position:0%
are the three things you would suggest
this<00:00:08.639><c> client</c><00:00:09.000><c> to</c><00:00:09.200><c> do</c><00:00:09.679><c> currently</c><00:00:10.200><c> engaged</c><00:00:10.639><c> in</c>

00:00:10.990 --> 00:00:11.000 align:start position:0%
this client to do currently engaged in
 

00:00:11.000 --> 00:00:12.870 align:start position:0%
this client to do currently engaged in
cper<00:00:11.480><c> impression</c><00:00:11.920><c> user</c><00:00:12.320><c> acquisition</c><00:00:12.719><c> in</c>

00:00:12.870 --> 00:00:12.880 align:start position:0%
cper impression user acquisition in
 

00:00:12.880 --> 00:00:15.310 align:start position:0%
cper impression user acquisition in
France<00:00:13.679><c> in</c><00:00:13.759><c> order</c><00:00:14.000><c> to</c><00:00:14.160><c> upsell</c><00:00:14.599><c> and</c><00:00:14.719><c> scale</c><00:00:15.120><c> the</c>

00:00:15.310 --> 00:00:15.320 align:start position:0%
France in order to upsell and scale the
 

00:00:15.320 --> 00:00:17.870 align:start position:0%
France in order to upsell and scale the
account<00:00:15.599><c> Lim</c><00:00:15.799><c> me</c><00:00:15.920><c> yourself</c><00:00:16.240><c> to</c><00:00:16.560><c> 100</c><00:00:16.880><c> words</c><00:00:17.439><c> Max</c>

00:00:17.870 --> 00:00:17.880 align:start position:0%
account Lim me yourself to 100 words Max
 

00:00:17.880 --> 00:00:19.870 align:start position:0%
account Lim me yourself to 100 words Max
so<00:00:18.720><c> uh</c><00:00:18.840><c> what</c><00:00:18.960><c> they're</c><00:00:19.160><c> really</c><00:00:19.400><c> asking</c><00:00:19.680><c> is</c>

00:00:19.870 --> 00:00:19.880 align:start position:0%
so uh what they're really asking is
 

00:00:19.880 --> 00:00:21.590 align:start position:0%
so uh what they're really asking is
based<00:00:20.160><c> off</c><00:00:20.279><c> of</c><00:00:20.480><c> this</c><00:00:20.720><c> data</c><00:00:21.240><c> what</c><00:00:21.359><c> would</c><00:00:21.480><c> you</c>

00:00:21.590 --> 00:00:21.600 align:start position:0%
based off of this data what would you
 

00:00:21.600 --> 00:00:23.429 align:start position:0%
based off of this data what would you
suggest<00:00:21.960><c> the</c><00:00:22.119><c> client</c><00:00:22.439><c> to</c><00:00:22.600><c> do</c><00:00:22.880><c> now</c><00:00:23.000><c> in</c><00:00:23.080><c> order</c><00:00:23.279><c> to</c>

00:00:23.429 --> 00:00:23.439 align:start position:0%
suggest the client to do now in order to
 

00:00:23.439 --> 00:00:26.349 align:start position:0%
suggest the client to do now in order to
answer<00:00:23.720><c> this</c><00:00:24.039><c> question</c><00:00:25.039><c> I</c><00:00:25.560><c> I'd</c><00:00:25.720><c> also</c><00:00:25.960><c> like</c><00:00:26.119><c> to</c>

00:00:26.349 --> 00:00:26.359 align:start position:0%
answer this question I I'd also like to
 

00:00:26.359 --> 00:00:28.630 align:start position:0%
answer this question I I'd also like to
kind<00:00:26.480><c> of</c><00:00:26.599><c> run</c><00:00:26.840><c> through</c><00:00:27.119><c> with</c><00:00:27.279><c> you</c><00:00:27.519><c> guys</c><00:00:28.080><c> the</c><00:00:28.439><c> uh</c>

00:00:28.630 --> 00:00:28.640 align:start position:0%
kind of run through with you guys the uh
 

00:00:28.640 --> 00:00:31.870 align:start position:0%
kind of run through with you guys the uh
the<00:00:29.000><c> the</c><00:00:29.160><c> feature</c><00:00:29.599><c> of</c><00:00:30.199><c> custom</c><00:00:31.199><c> um</c><00:00:31.439><c> custom</c>

00:00:31.870 --> 00:00:31.880 align:start position:0%
the the feature of custom um custom
 

00:00:31.880 --> 00:00:34.549 align:start position:0%
the the feature of custom um custom
filters<00:00:32.439><c> on</c><00:00:32.599><c> the</c><00:00:32.880><c> dashboard</c><00:00:33.480><c> level</c><00:00:34.320><c> so</c>

00:00:34.549 --> 00:00:34.559 align:start position:0%
filters on the dashboard level so
 

00:00:34.559 --> 00:00:37.510 align:start position:0%
filters on the dashboard level so
metabase<00:00:35.079><c> has</c><00:00:35.200><c> an</c><00:00:35.360><c> awesome</c><00:00:36.200><c> feature</c><00:00:37.200><c> uh</c><00:00:37.360><c> which</c>

00:00:37.510 --> 00:00:37.520 align:start position:0%
metabase has an awesome feature uh which
 

00:00:37.520 --> 00:00:38.910 align:start position:0%
metabase has an awesome feature uh which
allows<00:00:37.760><c> you</c><00:00:37.840><c> to</c><00:00:38.040><c> set</c><00:00:38.239><c> filters</c><00:00:38.600><c> on</c><00:00:38.719><c> the</c>

00:00:38.910 --> 00:00:38.920 align:start position:0%
allows you to set filters on the
 

00:00:38.920 --> 00:00:41.229 align:start position:0%
allows you to set filters on the
questions<00:00:39.680><c> and</c><00:00:39.879><c> then</c><00:00:40.360><c> and</c><00:00:40.480><c> then</c><00:00:40.680><c> after</c><00:00:40.960><c> that</c>

00:00:41.229 --> 00:00:41.239 align:start position:0%
questions and then and then after that
 

00:00:41.239 --> 00:00:44.630 align:start position:0%
questions and then and then after that
on<00:00:41.840><c> the</c><00:00:42.320><c> um</c><00:00:43.039><c> on</c><00:00:43.360><c> the</c><00:00:43.719><c> dashboard</c><00:00:44.239><c> level</c><00:00:44.520><c> so</c>

00:00:44.630 --> 00:00:44.640 align:start position:0%
on the um on the dashboard level so
 

00:00:44.640 --> 00:00:46.950 align:start position:0%
on the um on the dashboard level so
let's<00:00:44.800><c> go</c><00:00:44.920><c> ahead</c><00:00:45.120><c> and</c><00:00:45.360><c> start</c><00:00:45.640><c> doing</c><00:00:46.120><c> that</c><00:00:46.840><c> I'm</c>

00:00:46.950 --> 00:00:46.960 align:start position:0%
let's go ahead and start doing that I'm
 

00:00:46.960 --> 00:00:48.869 align:start position:0%
let's go ahead and start doing that I'm
going<00:00:47.079><c> to</c><00:00:47.559><c> go</c><00:00:47.719><c> ahead</c><00:00:47.960><c> and</c><00:00:48.320><c> uh</c><00:00:48.399><c> show</c><00:00:48.559><c> you</c><00:00:48.719><c> guys</c>

00:00:48.869 --> 00:00:48.879 align:start position:0%
going to go ahead and uh show you guys
 

00:00:48.879 --> 00:00:51.069 align:start position:0%
going to go ahead and uh show you guys
an<00:00:49.079><c> example</c><00:00:49.520><c> of</c><00:00:49.640><c> a</c><00:00:49.840><c> custom</c><00:00:50.280><c> filter</c><00:00:50.719><c> over</c><00:00:50.879><c> here</c>

00:00:51.069 --> 00:00:51.079 align:start position:0%
an example of a custom filter over here
 

00:00:51.079 --> 00:00:53.110 align:start position:0%
an example of a custom filter over here
because<00:00:51.239><c> if</c><00:00:51.320><c> you</c><00:00:51.440><c> remember</c><00:00:52.359><c> in</c><00:00:52.559><c> this</c><00:00:52.760><c> question</c>

00:00:53.110 --> 00:00:53.120 align:start position:0%
because if you remember in this question
 

00:00:53.120 --> 00:00:56.229 align:start position:0%
because if you remember in this question
we<00:00:53.359><c> manually</c><00:00:54.039><c> put</c><00:00:54.239><c> in</c><00:00:54.559><c> where</c><00:00:54.920><c> sum</c><00:00:55.640><c> is</c><00:00:55.879><c> greater</c>

00:00:56.229 --> 00:00:56.239 align:start position:0%
we manually put in where sum is greater
 

00:00:56.239 --> 00:00:59.389 align:start position:0%
we manually put in where sum is greater
than<00:00:56.480><c> zero</c><00:00:56.960><c> what</c><00:00:57.120><c> was</c><00:00:57.359><c> Sum</c><00:00:57.719><c> in</c><00:00:57.920><c> this</c><00:00:58.239><c> case</c><00:00:59.120><c> this</c>

00:00:59.389 --> 00:00:59.399 align:start position:0%
than zero what was Sum in this case this
 

00:00:59.399 --> 00:01:03.830 align:start position:0%
than zero what was Sum in this case this
was<00:01:00.359><c> uh</c><00:01:00.760><c> the</c><00:01:01.559><c> the</c><00:01:01.719><c> sum</c><00:01:02.079><c> of</c><00:01:02.440><c> deposits</c><00:01:03.440><c> right</c><00:01:03.600><c> so</c>

00:01:03.830 --> 00:01:03.840 align:start position:0%
was uh the the sum of deposits right so
 

00:01:03.840 --> 00:01:05.270 align:start position:0%
was uh the the sum of deposits right so
make<00:01:03.960><c> sure</c><00:01:04.199><c> that</c><00:01:04.360><c> everything</c>

00:01:05.270 --> 00:01:05.280 align:start position:0%
make sure that everything
 

00:01:05.280 --> 00:01:09.030 align:start position:0%
make sure that everything
is<00:01:06.280><c> uh</c><00:01:06.560><c> is</c><00:01:06.799><c> equal</c><00:01:07.439><c> where</c><00:01:07.759><c> the</c>

00:01:09.030 --> 00:01:09.040 align:start position:0%
is uh is equal where the
 

00:01:09.040 --> 00:01:11.230 align:start position:0%
is uh is equal where the
sum<00:01:10.040><c> of</c>

00:01:11.230 --> 00:01:11.240 align:start position:0%
sum of
 

00:01:11.240 --> 00:01:13.710 align:start position:0%
sum of
deposits<00:01:12.240><c> instead</c><00:01:12.520><c> of</c><00:01:12.759><c> manually</c><00:01:13.360><c> putting</c>

00:01:13.710 --> 00:01:13.720 align:start position:0%
deposits instead of manually putting
 

00:01:13.720 --> 00:01:15.149 align:start position:0%
deposits instead of manually putting
that<00:01:13.920><c> zero</c><00:01:14.240><c> in</c><00:01:14.400><c> there</c><00:01:14.560><c> let</c><00:01:14.680><c> me</c><00:01:14.799><c> make</c><00:01:14.960><c> that</c><00:01:15.080><c> a</c>

00:01:15.149 --> 00:01:15.159 align:start position:0%
that zero in there let me make that a
 

00:01:15.159 --> 00:01:17.230 align:start position:0%
that zero in there let me make that a
little<00:01:15.320><c> bit</c><00:01:15.479><c> bigger</c><00:01:15.680><c> for</c><00:01:15.799><c> you</c><00:01:16.080><c> guys</c><00:01:16.920><c> where</c><00:01:17.119><c> the</c>

00:01:17.230 --> 00:01:17.240 align:start position:0%
little bit bigger for you guys where the
 

00:01:17.240 --> 00:01:18.870 align:start position:0%
little bit bigger for you guys where the
sum<00:01:17.439><c> of</c><00:01:17.560><c> deposits</c><00:01:17.920><c> is</c><00:01:18.040><c> greater</c><00:01:18.280><c> than</c><00:01:18.439><c> zero</c><00:01:18.720><c> in</c>

00:01:18.870 --> 00:01:18.880 align:start position:0%
sum of deposits is greater than zero in
 

00:01:18.880 --> 00:01:20.870 align:start position:0%
sum of deposits is greater than zero in
this<00:01:19.040><c> case</c><00:01:19.360><c> I</c><00:01:19.439><c> want</c><00:01:19.560><c> to</c><00:01:19.720><c> do</c><00:01:20.159><c> where</c><00:01:20.320><c> the</c><00:01:20.439><c> sum</c><00:01:20.640><c> of</c>

00:01:20.870 --> 00:01:20.880 align:start position:0%
this case I want to do where the sum of
 

00:01:20.880 --> 00:01:22.749 align:start position:0%
this case I want to do where the sum of
deposits<00:01:21.520><c> is</c><00:01:21.640><c> greater</c><00:01:21.960><c> than</c><00:01:22.079><c> a</c><00:01:22.280><c> given</c>

00:01:22.749 --> 00:01:22.759 align:start position:0%
deposits is greater than a given
 

00:01:22.759 --> 00:01:31.190 align:start position:0%
deposits is greater than a given
variable<00:01:24.000><c> okay</c><00:01:25.000><c> uh</c><00:01:25.240><c> this</c><00:01:25.720><c> is</c><00:01:26.720><c> uh</c><00:01:27.479><c> Min</c><00:01:28.479><c> sum</c>

00:01:31.190 --> 00:01:31.200 align:start position:0%
variable okay uh this is uh Min sum
 

00:01:31.200 --> 00:01:32.749 align:start position:0%
variable okay uh this is uh Min sum
of

00:01:32.749 --> 00:01:32.759 align:start position:0%
of
 

00:01:32.759 --> 00:01:36.149 align:start position:0%
of
deposits<00:01:33.759><c> okay</c><00:01:34.600><c> and</c><00:01:34.799><c> I'm</c><00:01:34.880><c> going</c><00:01:35.000><c> to</c><00:01:35.119><c> say</c><00:01:35.439><c> this</c>

00:01:36.149 --> 00:01:36.159 align:start position:0%
deposits okay and I'm going to say this
 

00:01:36.159 --> 00:01:38.230 align:start position:0%
deposits okay and I'm going to say this
and<00:01:36.399><c> you</c><00:01:36.520><c> see</c><00:01:36.880><c> as</c><00:01:37.040><c> I</c><00:01:37.320><c> started</c><00:01:37.720><c> putting</c><00:01:38.040><c> that</c>

00:01:38.230 --> 00:01:38.240 align:start position:0%
and you see as I started putting that
 

00:01:38.240 --> 00:01:40.550 align:start position:0%
and you see as I started putting that
syntax<00:01:38.640><c> in</c><00:01:38.840><c> it</c><00:01:39.040><c> give</c><00:01:39.240><c> me</c><00:01:39.479><c> this</c><00:01:39.640><c> little</c><00:01:39.960><c> box</c><00:01:40.240><c> on</c>

00:01:40.550 --> 00:01:40.560 align:start position:0%
syntax in it give me this little box on
 

00:01:40.560 --> 00:01:43.590 align:start position:0%
syntax in it give me this little box on
top<00:01:41.320><c> and</c><00:01:41.520><c> now</c><00:01:42.119><c> if</c><00:01:42.640><c> um</c><00:01:43.040><c> I'm</c><00:01:43.119><c> going</c><00:01:43.240><c> to</c><00:01:43.360><c> go</c><00:01:43.439><c> ahead</c>

00:01:43.590 --> 00:01:43.600 align:start position:0%
top and now if um I'm going to go ahead
 

00:01:43.600 --> 00:01:45.310 align:start position:0%
top and now if um I'm going to go ahead
and<00:01:43.720><c> say</c><00:01:43.960><c> what's</c><00:01:44.200><c> this</c><00:01:44.439><c> variable</c><00:01:44.920><c> type</c><00:01:45.240><c> this</c>

00:01:45.310 --> 00:01:45.320 align:start position:0%
and say what's this variable type this
 

00:01:45.320 --> 00:01:47.590 align:start position:0%
and say what's this variable type this
is<00:01:45.520><c> actually</c><00:01:46.040><c> a</c><00:01:46.200><c> number</c><00:01:46.680><c> variable</c><00:01:47.200><c> type</c><00:01:47.520><c> and</c>

00:01:47.590 --> 00:01:47.600 align:start position:0%
is actually a number variable type and
 

00:01:47.600 --> 00:01:49.510 align:start position:0%
is actually a number variable type and
I'm<00:01:47.719><c> going</c><00:01:47.799><c> to</c><00:01:47.960><c> say</c><00:01:48.439><c> sum</c><00:01:48.680><c> of</c><00:01:48.920><c> deposits</c><00:01:49.360><c> is</c>

00:01:49.510 --> 00:01:49.520 align:start position:0%
I'm going to say sum of deposits is
 

00:01:49.520 --> 00:01:52.749 align:start position:0%
I'm going to say sum of deposits is
greater<00:01:49.840><c> than</c><00:01:50.079><c> zero</c><00:01:50.560><c> I</c><00:01:50.719><c> press</c><00:01:51.479><c> enter</c><00:01:52.479><c> and</c><00:01:52.600><c> then</c>

00:01:52.749 --> 00:01:52.759 align:start position:0%
greater than zero I press enter and then
 

00:01:52.759 --> 00:01:54.389 align:start position:0%
greater than zero I press enter and then
it<00:01:52.920><c> only</c><00:01:53.119><c> shows</c><00:01:53.439><c> me</c><00:01:53.640><c> the</c><00:01:53.799><c> records</c><00:01:54.159><c> where</c><00:01:54.320><c> the</c>

00:01:54.389 --> 00:01:54.399 align:start position:0%
it only shows me the records where the
 

00:01:54.399 --> 00:01:56.510 align:start position:0%
it only shows me the records where the
sum<00:01:54.560><c> of</c><00:01:54.719><c> deposits</c><00:01:55.119><c> is</c><00:01:55.240><c> greater</c><00:01:55.520><c> than</c><00:01:55.799><c> zero</c><00:01:56.360><c> so</c>

00:01:56.510 --> 00:01:56.520 align:start position:0%
sum of deposits is greater than zero so
 

00:01:56.520 --> 00:01:59.230 align:start position:0%
sum of deposits is greater than zero so
that's<00:01:56.759><c> awesome</c><00:01:57.280><c> that</c><00:01:57.439><c> works</c><00:01:57.759><c> I</c><00:01:57.920><c> press</c><00:01:58.280><c> save</c>

00:01:59.230 --> 00:01:59.240 align:start position:0%
that's awesome that works I press save
 

00:01:59.240 --> 00:02:02.270 align:start position:0%
that's awesome that works I press save
if<00:01:59.320><c> I</c><00:01:59.479><c> go</c><00:01:59.680><c> back</c><00:02:00.000><c> back</c><00:02:00.280><c> into</c><00:02:01.240><c> um</c><00:02:01.799><c> into</c><00:02:02.039><c> the</c>

00:02:02.270 --> 00:02:02.280 align:start position:0%
if I go back back into um into the
 

00:02:02.280 --> 00:02:05.469 align:start position:0%
if I go back back into um into the
dashboard<00:02:03.000><c> now</c><00:02:03.840><c> I</c><00:02:03.920><c> can</c><00:02:04.119><c> also</c><00:02:04.520><c> add</c><00:02:04.799><c> that</c><00:02:05.039><c> filter</c>

00:02:05.469 --> 00:02:05.479 align:start position:0%
dashboard now I can also add that filter
 

00:02:05.479 --> 00:02:07.550 align:start position:0%
dashboard now I can also add that filter
that<00:02:05.640><c> I</c><00:02:05.759><c> just</c><00:02:06.000><c> created</c><00:02:06.640><c> into</c><00:02:06.920><c> the</c><00:02:07.119><c> actual</c>

00:02:07.550 --> 00:02:07.560 align:start position:0%
that I just created into the actual
 

00:02:07.560 --> 00:02:09.949 align:start position:0%
that I just created into the actual
dashboard<00:02:08.160><c> so</c><00:02:08.319><c> I</c><00:02:08.440><c> go</c><00:02:08.679><c> edit</c><00:02:09.039><c> dashboard</c><00:02:09.720><c> and</c><00:02:09.840><c> I</c>

00:02:09.949 --> 00:02:09.959 align:start position:0%
dashboard so I go edit dashboard and I
 

00:02:09.959 --> 00:02:11.270 align:start position:0%
dashboard so I go edit dashboard and I
say<00:02:10.239><c> add</c><00:02:10.440><c> a</c>

00:02:11.270 --> 00:02:11.280 align:start position:0%
say add a
 

00:02:11.280 --> 00:02:14.630 align:start position:0%
say add a
filter<00:02:12.280><c> and</c><00:02:12.520><c> this</c><00:02:12.640><c> is</c><00:02:13.040><c> a</c><00:02:13.360><c> other</c><00:02:13.760><c> category</c>

00:02:14.630 --> 00:02:14.640 align:start position:0%
filter and this is a other category
 

00:02:14.640 --> 00:02:17.430 align:start position:0%
filter and this is a other category
filter<00:02:15.640><c> uh</c><00:02:15.879><c> what's</c><00:02:16.480><c> what</c><00:02:16.800><c> how</c><00:02:16.920><c> do</c><00:02:17.040><c> I</c><00:02:17.160><c> want</c><00:02:17.239><c> to</c>

00:02:17.430 --> 00:02:17.440 align:start position:0%
filter uh what's what how do I want to
 

00:02:17.440 --> 00:02:19.830 align:start position:0%
filter uh what's what how do I want to
name<00:02:17.760><c> this</c><00:02:17.959><c> filter</c><00:02:18.599><c> I'm</c><00:02:18.720><c> going</c><00:02:18.800><c> to</c><00:02:19.040><c> do</c><00:02:19.360><c> call</c><00:02:19.599><c> it</c>

00:02:19.830 --> 00:02:19.840 align:start position:0%
name this filter I'm going to do call it
 

00:02:19.840 --> 00:02:24.030 align:start position:0%
name this filter I'm going to do call it
Min<00:02:20.560><c> sum</c><00:02:21.480><c> of</c>

00:02:24.030 --> 00:02:24.040 align:start position:0%
Min sum of
 

00:02:24.040 --> 00:02:26.949 align:start position:0%
Min sum of
deposits<00:02:25.040><c> and</c><00:02:25.280><c> that</c><00:02:25.640><c> relates</c><00:02:26.080><c> to</c><00:02:26.400><c> this</c><00:02:26.680><c> Min</c>

00:02:26.949 --> 00:02:26.959 align:start position:0%
deposits and that relates to this Min
 

00:02:26.959 --> 00:02:30.350 align:start position:0%
deposits and that relates to this Min
some<00:02:27.160><c> of</c><00:02:27.560><c> deposits</c><00:02:28.560><c> and</c><00:02:29.000><c> I</c><00:02:29.200><c> press</c><00:02:29.519><c> done</c><00:02:30.080><c> and</c><00:02:30.200><c> I</c>

00:02:30.350 --> 00:02:30.360 align:start position:0%
some of deposits and I press done and I
 

00:02:30.360 --> 00:02:33.350 align:start position:0%
some of deposits and I press done and I
press<00:02:30.760><c> save</c><00:02:31.760><c> and</c><00:02:31.879><c> then</c><00:02:32.080><c> I</c><00:02:32.200><c> do</c><00:02:32.599><c> oh</c><00:02:32.920><c> okay</c><00:02:33.120><c> fine</c>

00:02:33.350 --> 00:02:33.360 align:start position:0%
press save and then I do oh okay fine
 

00:02:33.360 --> 00:02:34.550 align:start position:0%
press save and then I do oh okay fine
this<00:02:33.440><c> is</c>

00:02:34.550 --> 00:02:34.560 align:start position:0%
this is
 

00:02:34.560 --> 00:02:37.470 align:start position:0%
this is
important<00:02:35.560><c> uh</c><00:02:35.959><c> there</c><00:02:36.080><c> should</c><00:02:36.319><c> be</c><00:02:36.519><c> a</c><00:02:36.760><c> a</c><00:02:36.920><c> default</c>

00:02:37.470 --> 00:02:37.480 align:start position:0%
important uh there should be a a default
 

00:02:37.480 --> 00:02:39.750 align:start position:0%
important uh there should be a a default
value<00:02:37.959><c> over</c><00:02:38.200><c> there</c><00:02:38.440><c> but</c><00:02:38.720><c> I</c><00:02:38.879><c> maybe</c><00:02:39.159><c> it</c><00:02:39.319><c> comes</c><00:02:39.519><c> up</c>

00:02:39.750 --> 00:02:39.760 align:start position:0%
value over there but I maybe it comes up
 

00:02:39.760 --> 00:02:42.390 align:start position:0%
value over there but I maybe it comes up
anyways<00:02:40.080><c> let</c><00:02:40.200><c> me</c><00:02:40.319><c> reload</c><00:02:40.760><c> the</c><00:02:40.959><c> page</c><00:02:41.200><c> and</c><00:02:41.400><c> see</c>

00:02:42.390 --> 00:02:42.400 align:start position:0%
anyways let me reload the page and see
 

00:02:42.400 --> 00:02:44.710 align:start position:0%
anyways let me reload the page and see
see<00:02:42.640><c> if</c><00:02:42.840><c> everything</c><00:02:43.280><c> works</c><00:02:44.200><c> okay</c><00:02:44.400><c> no</c><00:02:44.599><c> it</c>

00:02:44.710 --> 00:02:44.720 align:start position:0%
see if everything works okay no it
 

00:02:44.720 --> 00:02:47.270 align:start position:0%
see if everything works okay no it
doesn't<00:02:45.080><c> work</c><00:02:45.480><c> because</c><00:02:45.680><c> I</c><00:02:45.879><c> also</c><00:02:46.159><c> need</c><00:02:46.519><c> a</c><00:02:46.920><c> uh</c><00:02:47.080><c> a</c>

00:02:47.270 --> 00:02:47.280 align:start position:0%
doesn't work because I also need a uh a
 

00:02:47.280 --> 00:02:49.470 align:start position:0%
doesn't work because I also need a uh a
default<00:02:47.840><c> value</c><00:02:48.239><c> there</c><00:02:48.599><c> okay</c><00:02:48.800><c> mint</c><00:02:49.120><c> some</c><00:02:49.280><c> of</c>

00:02:49.470 --> 00:02:49.480 align:start position:0%
default value there okay mint some of
 

00:02:49.480 --> 00:02:52.630 align:start position:0%
default value there okay mint some of
deposits<00:02:50.040><c> edit</c><00:02:50.400><c> enter</c><00:02:50.640><c> a</c><00:02:50.840><c> default</c><00:02:51.280><c> value</c><00:02:51.720><c> zero</c>

00:02:52.630 --> 00:02:52.640 align:start position:0%
deposits edit enter a default value zero
 

00:02:52.640 --> 00:02:55.070 align:start position:0%
deposits edit enter a default value zero
okay<00:02:53.080><c> done</c><00:02:54.080><c> and</c>

00:02:55.070 --> 00:02:55.080 align:start position:0%
okay done and
 

00:02:55.080 --> 00:02:58.149 align:start position:0%
okay done and
save<00:02:56.080><c> and</c><00:02:56.280><c> then</c><00:02:56.480><c> I</c><00:02:56.640><c> reload</c><00:02:57.120><c> the</c><00:02:57.360><c> page</c><00:02:57.959><c> let's</c>

00:02:58.149 --> 00:02:58.159 align:start position:0%
save and then I reload the page let's
 

00:02:58.159 --> 00:03:01.190 align:start position:0%
save and then I reload the page let's
see<00:02:58.280><c> if</c><00:02:58.400><c> it</c><00:02:58.560><c> works</c><00:02:58.920><c> this</c><00:02:59.159><c> time</c><00:03:00.280><c> H</c><00:03:00.840><c> okay</c><00:03:01.000><c> for</c>

00:03:01.190 --> 00:03:01.200 align:start position:0%
see if it works this time H okay for
 

00:03:01.200 --> 00:03:03.070 align:start position:0%
see if it works this time H okay for
some<00:03:01.400><c> reason</c><00:03:01.720><c> it</c><00:03:01.879><c> does</c><00:03:02.080><c> not</c><00:03:02.319><c> work</c><00:03:02.640><c> Su</c><00:03:02.879><c> of</c>

00:03:03.070 --> 00:03:03.080 align:start position:0%
some reason it does not work Su of
 

00:03:03.080 --> 00:03:05.589 align:start position:0%
some reason it does not work Su of
deposits<00:03:03.560><c> per</c><00:03:03.720><c> sub</c><00:03:04.040><c> publisher</c><00:03:04.640><c> okay</c><00:03:04.799><c> maybe</c><00:03:05.239><c> on</c>

00:03:05.589 --> 00:03:05.599 align:start position:0%
deposits per sub publisher okay maybe on
 

00:03:05.599 --> 00:03:09.710 align:start position:0%
deposits per sub publisher okay maybe on
this<00:03:06.480><c> I</c><00:03:06.560><c> need</c><00:03:06.720><c> to</c><00:03:06.920><c> set</c><00:03:07.680><c> uh</c><00:03:08.680><c> the</c><00:03:08.879><c> default</c><00:03:09.360><c> value</c>

00:03:09.710 --> 00:03:09.720 align:start position:0%
this I need to set uh the default value
 

00:03:09.720 --> 00:03:13.030 align:start position:0%
this I need to set uh the default value
over<00:03:10.000><c> here</c><00:03:10.480><c> filter</c><00:03:10.959><c> widget</c><00:03:11.959><c> required</c><00:03:12.840><c> yes</c>

00:03:13.030 --> 00:03:13.040 align:start position:0%
over here filter widget required yes
 

00:03:13.040 --> 00:03:15.430 align:start position:0%
over here filter widget required yes
it's<00:03:13.280><c> required</c><00:03:13.799><c> default</c><00:03:14.200><c> widget</c><00:03:14.680><c> value</c><00:03:15.159><c> is</c>

00:03:15.430 --> 00:03:15.440 align:start position:0%
it's required default widget value is
 

00:03:15.440 --> 00:03:20.149 align:start position:0%
it's required default widget value is
zero<00:03:16.440><c> okay</c><00:03:17.440><c> and</c><00:03:17.640><c> I</c><00:03:17.840><c> press</c><00:03:18.159><c> save</c>

00:03:20.149 --> 00:03:20.159 align:start position:0%
zero okay and I press save
 

00:03:20.159 --> 00:03:24.949 align:start position:0%
zero okay and I press save
save<00:03:21.159><c> okay</c><00:03:21.799><c> go</c><00:03:22.040><c> back</c><00:03:22.440><c> into</c><00:03:23.440><c> the</c><00:03:24.440><c> into</c><00:03:24.760><c> the</c>

00:03:24.949 --> 00:03:24.959 align:start position:0%
save okay go back into the into the
 

00:03:24.959 --> 00:03:27.869 align:start position:0%
save okay go back into the into the
dashboard<00:03:25.680><c> see</c><00:03:25.920><c> if</c><00:03:26.040><c> it</c><00:03:26.360><c> loads</c><00:03:27.360><c> okay</c><00:03:27.560><c> cool</c>

00:03:27.869 --> 00:03:27.879 align:start position:0%
dashboard see if it loads okay cool
 

00:03:27.879 --> 00:03:30.229 align:start position:0%
dashboard see if it loads okay cool
everything<00:03:28.239><c> loaded</c><00:03:29.239><c> now</c><00:03:29.400><c> I</c><00:03:29.519><c> just</c><00:03:29.840><c> need</c><00:03:29.959><c> to</c><00:03:30.080><c> go</c>

00:03:30.229 --> 00:03:30.239 align:start position:0%
everything loaded now I just need to go
 

00:03:30.239 --> 00:03:33.910 align:start position:0%
everything loaded now I just need to go
ahead<00:03:30.920><c> and</c><00:03:31.760><c> uh</c><00:03:32.599><c> and</c><00:03:32.840><c> turn</c><00:03:33.040><c> it</c><00:03:33.200><c> into</c><00:03:33.439><c> a</c><00:03:33.640><c> bar</c>

00:03:33.910 --> 00:03:33.920 align:start position:0%
ahead and uh and turn it into a bar
 

00:03:33.920 --> 00:03:35.350 align:start position:0%
ahead and uh and turn it into a bar
chart

00:03:35.350 --> 00:03:35.360 align:start position:0%
chart
 

00:03:35.360 --> 00:03:39.830 align:start position:0%
chart
again<00:03:36.360><c> okay</c><00:03:36.640><c> xais</c><00:03:37.560><c> oh</c><00:03:38.159><c> schnikes</c><00:03:39.159><c> okay</c><00:03:39.319><c> sum</c><00:03:39.599><c> of</c>

00:03:39.830 --> 00:03:39.840 align:start position:0%
again okay xais oh schnikes okay sum of
 

00:03:39.840 --> 00:03:42.550 align:start position:0%
again okay xais oh schnikes okay sum of
deposits<00:03:40.400><c> per</c><00:03:40.640><c> sub</c>

00:03:42.550 --> 00:03:42.560 align:start position:0%
deposits per sub
 

00:03:42.560 --> 00:03:46.110 align:start position:0%
deposits per sub
publisher<00:03:43.560><c> okay</c><00:03:43.799><c> cool</c><00:03:44.360><c> that</c><00:03:44.560><c> worked</c><00:03:45.280><c> go</c><00:03:45.560><c> back</c>

00:03:46.110 --> 00:03:46.120 align:start position:0%
publisher okay cool that worked go back
 

00:03:46.120 --> 00:03:50.910 align:start position:0%
publisher okay cool that worked go back
bar<00:03:46.680><c> chart</c><00:03:47.680><c> and</c><00:03:48.080><c> perfect</c><00:03:48.920><c> okay</c><00:03:49.200><c> so</c><00:03:49.720><c> now</c><00:03:50.720><c> uh</c>

00:03:50.910 --> 00:03:50.920 align:start position:0%
bar chart and perfect okay so now uh
 

00:03:50.920 --> 00:03:52.990 align:start position:0%
bar chart and perfect okay so now uh
everything<00:03:51.239><c> is</c><00:03:51.439><c> working</c><00:03:51.840><c> over</c><00:03:52.120><c> here</c><00:03:52.519><c> I</c><00:03:52.680><c> press</c>

00:03:52.990 --> 00:03:53.000 align:start position:0%
everything is working over here I press
 

00:03:53.000 --> 00:03:54.789 align:start position:0%
everything is working over here I press
save<00:03:53.400><c> because</c><00:03:53.560><c> I</c><00:03:53.640><c> need</c><00:03:53.760><c> to</c><00:03:53.920><c> save</c><00:03:54.079><c> it</c><00:03:54.200><c> as</c><00:03:54.319><c> a</c><00:03:54.439><c> bar</c>

00:03:54.789 --> 00:03:54.799 align:start position:0%
save because I need to save it as a bar
 

00:03:54.799 --> 00:03:57.149 align:start position:0%
save because I need to save it as a bar
chart<00:03:55.680><c> I</c><00:03:55.879><c> press</c><00:03:56.159><c> save</c><00:03:56.480><c> and</c><00:03:56.640><c> I'm</c><00:03:56.760><c> going</c><00:03:56.840><c> to</c><00:03:57.040><c> go</c>

00:03:57.149 --> 00:03:57.159 align:start position:0%
chart I press save and I'm going to go
 

00:03:57.159 --> 00:04:00.509 align:start position:0%
chart I press save and I'm going to go
ahead<00:03:57.360><c> and</c><00:03:57.560><c> reload</c><00:03:58.040><c> my</c><00:03:58.239><c> dashboard</c><00:03:58.799><c> over</c><00:03:59.040><c> here</c>

00:04:00.509 --> 00:04:00.519 align:start position:0%
ahead and reload my dashboard over here
 

00:04:00.519 --> 00:04:02.710 align:start position:0%
ahead and reload my dashboard over here
and<00:04:00.840><c> everything</c><00:04:01.239><c> works</c><00:04:01.680><c> okay</c><00:04:01.840><c> so</c><00:04:01.959><c> Min</c><00:04:02.280><c> sum</c><00:04:02.480><c> of</c>

00:04:02.710 --> 00:04:02.720 align:start position:0%
and everything works okay so Min sum of
 

00:04:02.720 --> 00:04:04.910 align:start position:0%
and everything works okay so Min sum of
deposits<00:04:03.439><c> I</c><00:04:03.519><c> want</c><00:04:03.640><c> to</c><00:04:03.799><c> see</c><00:04:04.159><c> show</c><00:04:04.360><c> me</c><00:04:04.519><c> only</c><00:04:04.760><c> the</c>

00:04:04.910 --> 00:04:04.920 align:start position:0%
deposits I want to see show me only the
 

00:04:04.920 --> 00:04:07.190 align:start position:0%
deposits I want to see show me only the
sources<00:04:05.519><c> with</c><00:04:05.720><c> at</c><00:04:05.840><c> least</c><00:04:06.079><c> three</c><00:04:06.400><c> deposits</c><00:04:07.040><c> I</c>

00:04:07.190 --> 00:04:07.200 align:start position:0%
sources with at least three deposits I
 

00:04:07.200 --> 00:04:09.190 align:start position:0%
sources with at least three deposits I
press<00:04:07.439><c> enter</c><00:04:08.280><c> and</c><00:04:08.400><c> now</c><00:04:08.560><c> I</c><00:04:08.680><c> only</c><00:04:08.840><c> see</c><00:04:09.079><c> the</c>

00:04:09.190 --> 00:04:09.200 align:start position:0%
press enter and now I only see the
 

00:04:09.200 --> 00:04:11.270 align:start position:0%
press enter and now I only see the
sources<00:04:09.640><c> with</c><00:04:09.799><c> at</c><00:04:09.920><c> least</c><00:04:10.159><c> three</c><00:04:10.480><c> deposits</c><00:04:11.159><c> all</c>

00:04:11.270 --> 00:04:11.280 align:start position:0%
sources with at least three deposits all
 

00:04:11.280 --> 00:04:13.429 align:start position:0%
sources with at least three deposits all
right<00:04:11.439><c> so</c><00:04:11.680><c> that's</c><00:04:12.319><c> that's</c><00:04:12.519><c> the</c><00:04:12.760><c> power</c><00:04:13.159><c> of</c>

00:04:13.429 --> 00:04:13.439 align:start position:0%
right so that's that's the power of
 

00:04:13.439 --> 00:04:15.550 align:start position:0%
right so that's that's the power of
custom<00:04:13.799><c> filters</c><00:04:14.360><c> in</c><00:04:14.599><c> in</c><00:04:14.720><c> metabase</c><00:04:15.360><c> and</c><00:04:15.480><c> the</c>

00:04:15.550 --> 00:04:15.560 align:start position:0%
custom filters in in metabase and the
 

00:04:15.560 --> 00:04:17.670 align:start position:0%
custom filters in in metabase and the
nice<00:04:15.840><c> thing</c><00:04:16.000><c> about</c><00:04:16.160><c> it</c><00:04:16.359><c> is</c><00:04:17.079><c> I</c><00:04:17.280><c> already</c><00:04:17.519><c> have</c>

00:04:17.670 --> 00:04:17.680 align:start position:0%
nice thing about it is I already have
 

00:04:17.680 --> 00:04:20.550 align:start position:0%
nice thing about it is I already have
the<00:04:17.840><c> custom</c><00:04:18.120><c> filter</c><00:04:18.440><c> on</c><00:04:18.680><c> this</c><00:04:19.040><c> question</c><00:04:20.040><c> um</c>

00:04:20.550 --> 00:04:20.560 align:start position:0%
the custom filter on this question um
 

00:04:20.560 --> 00:04:22.710 align:start position:0%
the custom filter on this question um
and<00:04:20.720><c> on</c><00:04:20.880><c> the</c><00:04:21.040><c> dashboard</c><00:04:21.560><c> as</c><00:04:21.639><c> a</c><00:04:21.799><c> whole</c><00:04:22.199><c> but</c><00:04:22.479><c> if</c><00:04:22.600><c> I</c>

00:04:22.710 --> 00:04:22.720 align:start position:0%
and on the dashboard as a whole but if I
 

00:04:22.720 --> 00:04:25.550 align:start position:0%
and on the dashboard as a whole but if I
want<00:04:22.880><c> to</c><00:04:23.639><c> uh</c><00:04:23.880><c> apply</c><00:04:24.360><c> this</c><00:04:24.600><c> filter</c><00:04:25.120><c> to</c><00:04:25.320><c> all</c>

00:04:25.550 --> 00:04:25.560 align:start position:0%
want to uh apply this filter to all
 

00:04:25.560 --> 00:04:28.189 align:start position:0%
want to uh apply this filter to all
three<00:04:25.919><c> questions</c><00:04:26.880><c> I</c><00:04:27.000><c> just</c><00:04:27.160><c> need</c><00:04:27.360><c> to</c><00:04:27.720><c> add</c><00:04:27.960><c> that</c>

00:04:28.189 --> 00:04:28.199 align:start position:0%
three questions I just need to add that
 

00:04:28.199 --> 00:04:30.189 align:start position:0%
three questions I just need to add that
variable<00:04:28.639><c> to</c><00:04:28.800><c> the</c><00:04:28.919><c> other</c><00:04:29.240><c> questions</c><00:04:29.880><c> as</c><00:04:30.000><c> well</c>

00:04:30.189 --> 00:04:30.199 align:start position:0%
variable to the other questions as well
 

00:04:30.199 --> 00:04:32.110 align:start position:0%
variable to the other questions as well
so<00:04:30.320><c> I'm</c><00:04:30.440><c> going</c><00:04:30.520><c> to</c><00:04:30.639><c> go</c><00:04:30.800><c> ahead</c><00:04:31.000><c> and</c><00:04:31.560><c> go</c><00:04:31.720><c> into</c><00:04:31.960><c> my</c>

00:04:32.110 --> 00:04:32.120 align:start position:0%
so I'm going to go ahead and go into my
 

00:04:32.120 --> 00:04:34.870 align:start position:0%
so I'm going to go ahead and go into my
other<00:04:32.479><c> questions</c><00:04:33.479><c> and</c><00:04:33.800><c> where</c><00:04:34.000><c> the</c><00:04:34.199><c> Su</c><00:04:34.639><c> and</c><00:04:34.759><c> I'm</c>

00:04:34.870 --> 00:04:34.880 align:start position:0%
other questions and where the Su and I'm
 

00:04:34.880 --> 00:04:37.150 align:start position:0%
other questions and where the Su and I'm
going<00:04:34.960><c> to</c><00:04:35.199><c> add</c><00:04:35.440><c> that</c><00:04:35.680><c> variable</c><00:04:36.440><c> over</c><00:04:36.720><c> there</c>

00:04:37.150 --> 00:04:37.160 align:start position:0%
going to add that variable over there
 

00:04:37.160 --> 00:04:38.710 align:start position:0%
going to add that variable over there
where<00:04:37.400><c> sum</c>

00:04:38.710 --> 00:04:38.720 align:start position:0%
where sum
 

00:04:38.720 --> 00:04:42.510 align:start position:0%
where sum
equals<00:04:39.720><c> um</c><00:04:40.320><c> where</c><00:04:40.520><c> sum</c><00:04:40.880><c> is</c><00:04:41.160><c> greater</c>

00:04:42.510 --> 00:04:42.520 align:start position:0%
equals um where sum is greater
 

00:04:42.520 --> 00:04:50.950 align:start position:0%
equals um where sum is greater
than<00:04:43.520><c> um</c><00:04:44.120><c> Min</c><00:04:45.440><c> sum</c><00:04:46.680><c> of</c><00:04:48.080><c> deposits</c><00:04:49.360><c> okay</c><00:04:50.360><c> I</c><00:04:50.800><c> put</c>

00:04:50.950 --> 00:04:50.960 align:start position:0%
than um Min sum of deposits okay I put
 

00:04:50.960 --> 00:04:53.550 align:start position:0%
than um Min sum of deposits okay I put
in<00:04:51.120><c> a</c><00:04:51.360><c> default</c><00:04:51.919><c> value</c><00:04:52.680><c> default</c><00:04:53.160><c> value</c><00:04:53.440><c> is</c>

00:04:53.550 --> 00:04:53.560 align:start position:0%
in a default value default value is
 

00:04:53.560 --> 00:04:55.990 align:start position:0%
in a default value default value is
going<00:04:53.720><c> to</c><00:04:53.840><c> be</c><00:04:54.080><c> zero</c><00:04:55.080><c> okay</c><00:04:55.440><c> make</c><00:04:55.600><c> sure</c><00:04:55.800><c> that</c>

00:04:55.990 --> 00:04:56.000 align:start position:0%
going to be zero okay make sure that
 

00:04:56.000 --> 00:04:57.590 align:start position:0%
going to be zero okay make sure that
everything<00:04:56.400><c> works</c><00:04:56.759><c> over</c>

00:04:57.590 --> 00:04:57.600 align:start position:0%
everything works over
 

00:04:57.600 --> 00:05:00.950 align:start position:0%
everything works over
here<00:04:58.600><c> okay</c><00:04:58.919><c> cool</c><00:04:59.520><c> uh</c><00:04:59.840><c> uh</c><00:04:59.960><c> turn</c><00:05:00.160><c> it</c><00:05:00.320><c> into</c><00:05:00.520><c> a</c><00:05:00.680><c> bar</c>

00:05:00.950 --> 00:05:00.960 align:start position:0%
here okay cool uh uh turn it into a bar
 

00:05:00.960 --> 00:05:02.830 align:start position:0%
here okay cool uh uh turn it into a bar
chart<00:05:01.320><c> over</c><00:05:01.600><c> here</c><00:05:01.800><c> as</c><00:05:02.000><c> well</c><00:05:02.360><c> to</c><00:05:02.520><c> make</c><00:05:02.680><c> it</c>

00:05:02.830 --> 00:05:02.840 align:start position:0%
chart over here as well to make it
 

00:05:02.840 --> 00:05:05.990 align:start position:0%
chart over here as well to make it
cleaner<00:05:03.479><c> a</c><00:05:03.680><c> bar</c><00:05:04.120><c> chart</c><00:05:05.120><c> okay</c><00:05:05.360><c> cool</c><00:05:05.680><c> everything</c>

00:05:05.990 --> 00:05:06.000 align:start position:0%
cleaner a bar chart okay cool everything
 

00:05:06.000 --> 00:05:09.110 align:start position:0%
cleaner a bar chart okay cool everything
looks<00:05:06.440><c> good</c><00:05:07.440><c> uh</c><00:05:07.600><c> underperforming</c><00:05:08.560><c> sub</c>

00:05:09.110 --> 00:05:09.120 align:start position:0%
looks good uh underperforming sub
 

00:05:09.120 --> 00:05:11.870 align:start position:0%
looks good uh underperforming sub
Publishers<00:05:10.120><c> order</c><00:05:10.479><c> by</c><00:05:10.680><c> number</c><00:05:10.960><c> of</c>

00:05:11.870 --> 00:05:11.880 align:start position:0%
Publishers order by number of
 

00:05:11.880 --> 00:05:16.670 align:start position:0%
Publishers order by number of
clicks<00:05:12.880><c> uh</c><00:05:13.080><c> Min</c><00:05:13.440><c> sum</c><00:05:13.680><c> of</c><00:05:14.320><c> deposits</c><00:05:15.320><c> okay</c><00:05:16.320><c> me</c>

00:05:16.670 --> 00:05:16.680 align:start position:0%
clicks uh Min sum of deposits okay me
 

00:05:16.680 --> 00:05:17.670 align:start position:0%
clicks uh Min sum of deposits okay me
sum<00:05:16.919><c> of</c>

00:05:17.670 --> 00:05:17.680 align:start position:0%
sum of
 

00:05:17.680 --> 00:05:21.350 align:start position:0%
sum of
deposits<00:05:18.680><c> um</c><00:05:19.160><c> H</c><00:05:20.080><c> okay</c><00:05:20.240><c> maybe</c><00:05:20.479><c> we</c><00:05:20.919><c> we</c><00:05:21.000><c> need</c><00:05:21.160><c> to</c>

00:05:21.350 --> 00:05:21.360 align:start position:0%
deposits um H okay maybe we we need to
 

00:05:21.360 --> 00:05:25.309 align:start position:0%
deposits um H okay maybe we we need to
rethink<00:05:21.960><c> this</c><00:05:22.960><c> where</c><00:05:23.720><c> sum</c>

00:05:25.309 --> 00:05:25.319 align:start position:0%
rethink this where sum
 

00:05:25.319 --> 00:05:27.590 align:start position:0%
rethink this where sum
equals<00:05:26.319><c> well</c><00:05:26.520><c> you</c><00:05:26.639><c> know</c><00:05:26.840><c> what</c><00:05:27.000><c> maybe</c><00:05:27.280><c> in</c><00:05:27.440><c> this</c>

00:05:27.590 --> 00:05:27.600 align:start position:0%
equals well you know what maybe in this
 

00:05:27.600 --> 00:05:30.629 align:start position:0%
equals well you know what maybe in this
case<00:05:27.800><c> we</c><00:05:27.919><c> shouldn't</c><00:05:28.360><c> have</c><00:05:28.560><c> it</c><00:05:29.919><c> uh</c><00:05:30.120><c> whatever</c><00:05:30.479><c> in</c>

00:05:30.629 --> 00:05:30.639 align:start position:0%
case we shouldn't have it uh whatever in
 

00:05:30.639 --> 00:05:34.189 align:start position:0%
case we shouldn't have it uh whatever in
this<00:05:30.800><c> case</c><00:05:31.160><c> let's</c><00:05:31.479><c> let's</c><00:05:31.680><c> just</c><00:05:31.880><c> keep</c><00:05:32.039><c> it</c><00:05:32.240><c> as</c><00:05:32.479><c> is</c>

00:05:34.189 --> 00:05:34.199 align:start position:0%
this case let's let's just keep it as is
 

00:05:34.199 --> 00:05:36.430 align:start position:0%
this case let's let's just keep it as is
um<00:05:35.199><c> that's</c><00:05:35.360><c> why</c><00:05:35.520><c> you</c><00:05:35.639><c> have</c><00:05:35.720><c> to</c><00:05:35.880><c> really</c><00:05:36.120><c> really</c>

00:05:36.430 --> 00:05:36.440 align:start position:0%
um that's why you have to really really
 

00:05:36.440 --> 00:05:38.070 align:start position:0%
um that's why you have to really really
think<00:05:36.840><c> think</c><00:05:37.120><c> through</c><00:05:37.479><c> how</c><00:05:37.720><c> you're</c><00:05:37.840><c> going</c><00:05:37.960><c> to</c>

00:05:38.070 --> 00:05:38.080 align:start position:0%
think think through how you're going to
 

00:05:38.080 --> 00:05:40.830 align:start position:0%
think think through how you're going to
make<00:05:38.280><c> this</c><00:05:38.479><c> as</c><00:05:38.639><c> intuitive</c><00:05:39.199><c> as</c><00:05:39.639><c> possible</c><00:05:40.639><c> let's</c>

00:05:40.830 --> 00:05:40.840 align:start position:0%
make this as intuitive as possible let's
 

00:05:40.840 --> 00:05:42.590 align:start position:0%
make this as intuitive as possible let's
go<00:05:40.960><c> into</c><00:05:41.160><c> the</c><00:05:41.319><c> assignment</c><00:05:41.759><c> here</c><00:05:41.919><c> Min</c><00:05:42.199><c> sum</c><00:05:42.400><c> of</c>

00:05:42.590 --> 00:05:42.600 align:start position:0%
go into the assignment here Min sum of
 

00:05:42.600 --> 00:05:45.909 align:start position:0%
go into the assignment here Min sum of
deposits<00:05:43.360><c> equals</c><00:05:44.080><c> three</c><00:05:44.520><c> Depo</c><00:05:45.000><c> five</c><00:05:45.319><c> deposits</c>

00:05:45.909 --> 00:05:45.919 align:start position:0%
deposits equals three Depo five deposits
 

00:05:45.919 --> 00:05:50.230 align:start position:0%
deposits equals three Depo five deposits
I<00:05:46.080><c> press</c><00:05:46.759><c> enter</c><00:05:47.759><c> it</c><00:05:47.880><c> should</c><00:05:48.520><c> also</c><00:05:49.520><c> uh</c><00:05:49.720><c> populate</c>

00:05:50.230 --> 00:05:50.240 align:start position:0%
I press enter it should also uh populate
 

00:05:50.240 --> 00:05:53.029 align:start position:0%
I press enter it should also uh populate
these<00:05:50.520><c> other</c><00:05:51.160><c> these</c><00:05:51.400><c> other</c><00:05:52.080><c> uh</c><00:05:52.280><c> tables</c><00:05:52.800><c> as</c>

00:05:53.029 --> 00:05:53.039 align:start position:0%
these other these other uh tables as
 

00:05:53.039 --> 00:05:58.110 align:start position:0%
these other these other uh tables as
well<00:05:53.600><c> okay</c><00:05:54.560><c> Min</c><00:05:54.960><c> sum</c><00:05:55.199><c> of</c><00:05:56.000><c> deposits</c>

00:05:58.110 --> 00:05:58.120 align:start position:0%
well okay Min sum of deposits
 

00:05:58.120 --> 00:06:01.430 align:start position:0%
well okay Min sum of deposits
H<00:05:59.120><c> Min</c><00:05:59.360><c> sum</c><00:05:59.520><c> of</c><00:05:59.919><c> deposits</c><00:06:00.880><c> that's</c><00:06:01.080><c> why</c>

00:06:01.430 --> 00:06:01.440 align:start position:0%
H Min sum of deposits that's why
 

00:06:01.440 --> 00:06:02.909 align:start position:0%
H Min sum of deposits that's why
sometimes<00:06:01.639><c> you</c><00:06:01.759><c> don't</c><00:06:02.080><c> you</c><00:06:02.520><c> you</c><00:06:02.639><c> need</c><00:06:02.800><c> to</c>

00:06:02.909 --> 00:06:02.919 align:start position:0%
sometimes you don't you you need to
 

00:06:02.919 --> 00:06:05.150 align:start position:0%
sometimes you don't you you need to
think<00:06:03.199><c> through</c><00:06:03.560><c> which</c><00:06:04.080><c> which</c><00:06:04.639><c> uh</c><00:06:05.000><c> which</c>

00:06:05.150 --> 00:06:05.160 align:start position:0%
think through which which uh which
 

00:06:05.160 --> 00:06:06.830 align:start position:0%
think through which which uh which
filters<00:06:05.520><c> should</c><00:06:05.720><c> be</c><00:06:05.840><c> hardcoded</c><00:06:06.520><c> and</c><00:06:06.720><c> which</c>

00:06:06.830 --> 00:06:06.840 align:start position:0%
filters should be hardcoded and which
 

00:06:06.840 --> 00:06:08.909 align:start position:0%
filters should be hardcoded and which
one<00:06:07.039><c> should</c><00:06:07.199><c> be</c><00:06:07.280><c> on</c><00:06:07.400><c> a</c><00:06:07.599><c> top</c><00:06:07.880><c> level</c><00:06:08.639><c> okay</c><00:06:08.840><c> this</c>

00:06:08.909 --> 00:06:08.919 align:start position:0%
one should be on a top level okay this
 

00:06:08.919 --> 00:06:11.230 align:start position:0%
one should be on a top level okay this
one<00:06:09.080><c> is</c><00:06:09.199><c> underperforming</c><00:06:10.039><c> subp</c><00:06:10.479><c> Publishers</c>

00:06:11.230 --> 00:06:11.240 align:start position:0%
one is underperforming subp Publishers
 

00:06:11.240 --> 00:06:14.110 align:start position:0%
one is underperforming subp Publishers
where<00:06:11.440><c> the</c><00:06:11.560><c> sum</c><00:06:11.800><c> of</c><00:06:12.000><c> deposits</c><00:06:12.599><c> equals</c>

00:06:14.110 --> 00:06:14.120 align:start position:0%
where the sum of deposits equals
 

00:06:14.120 --> 00:06:18.510 align:start position:0%
where the sum of deposits equals
zero<00:06:15.120><c> um</c><00:06:15.800><c> let's</c>

00:06:18.510 --> 00:06:18.520 align:start position:0%
 
 

00:06:18.520 --> 00:06:22.430 align:start position:0%
 
see<00:06:19.520><c> okay</c><00:06:19.840><c> where</c>

00:06:22.430 --> 00:06:22.440 align:start position:0%
 
 

00:06:22.440 --> 00:06:25.589 align:start position:0%
 
sum<00:06:23.440><c> equals</c>

00:06:25.589 --> 00:06:25.599 align:start position:0%
sum equals
 

00:06:25.599 --> 00:06:39.629 align:start position:0%
sum equals
zero<00:06:26.960><c> uh</c><00:06:27.960><c> and</c>

00:06:39.629 --> 00:06:39.639 align:start position:0%
 
 

00:06:39.639 --> 00:06:44.629 align:start position:0%
 
and<00:06:39.880><c> some</c>

00:06:44.629 --> 00:06:44.639 align:start position:0%
 
 

00:06:44.639 --> 00:06:47.390 align:start position:0%
 
deposits<00:06:45.639><c> s</c><00:06:45.880><c> is</c><00:06:46.080><c> greater</c>

00:06:47.390 --> 00:06:47.400 align:start position:0%
deposits s is greater
 

00:06:47.400 --> 00:06:49.950 align:start position:0%
deposits s is greater
than<00:06:48.400><c> okay</c>

00:06:49.950 --> 00:06:49.960 align:start position:0%
than okay
 

00:06:49.960 --> 00:06:53.390 align:start position:0%
than okay
cool<00:06:50.960><c> uh</c><00:06:51.080><c> so</c><00:06:51.400><c> that</c><00:06:51.919><c> that</c><00:06:52.520><c> automatically</c><00:06:53.120><c> turns</c>

00:06:53.390 --> 00:06:53.400 align:start position:0%
cool uh so that that automatically turns
 

00:06:53.400 --> 00:06:57.270 align:start position:0%
cool uh so that that automatically turns
it<00:06:53.560><c> to</c><00:06:53.880><c> zero</c>

00:06:57.270 --> 00:06:57.280 align:start position:0%
 
 

00:06:57.280 --> 00:07:00.510 align:start position:0%
 
okay<00:06:58.280><c> where</c><00:06:58.520><c> some</c><00:06:58.800><c> equals</c><00:06:59.160><c> zero</c>

00:07:00.510 --> 00:07:00.520 align:start position:0%
okay where some equals zero
 

00:07:00.520 --> 00:07:02.510 align:start position:0%
okay where some equals zero
okay<00:07:00.840><c> whatever</c><00:07:01.360><c> it's</c><00:07:01.560><c> good</c><00:07:01.759><c> enough</c><00:07:02.039><c> as</c><00:07:02.160><c> it</c><00:07:02.319><c> is</c>

00:07:02.510 --> 00:07:02.520 align:start position:0%
okay whatever it's good enough as it is
 

00:07:02.520 --> 00:07:03.790 align:start position:0%
okay whatever it's good enough as it is
in<00:07:02.639><c> this</c><00:07:02.840><c> case</c><00:07:03.039><c> I</c><00:07:03.160><c> don't</c><00:07:03.360><c> think</c><00:07:03.479><c> we</c><00:07:03.599><c> should</c>

00:07:03.790 --> 00:07:03.800 align:start position:0%
in this case I don't think we should
 

00:07:03.800 --> 00:07:06.710 align:start position:0%
in this case I don't think we should
have<00:07:03.960><c> a</c><00:07:04.919><c> specific</c><00:07:05.440><c> variable</c><00:07:05.919><c> for</c><00:07:06.240><c> that</c><00:07:06.639><c> I</c>

00:07:06.710 --> 00:07:06.720 align:start position:0%
have a specific variable for that I
 

00:07:06.720 --> 00:07:07.749 align:start position:0%
have a specific variable for that I
think<00:07:06.840><c> we've</c><00:07:07.000><c> already</c><00:07:07.199><c> got</c><00:07:07.400><c> enough</c>

00:07:07.749 --> 00:07:07.759 align:start position:0%
think we've already got enough
 

00:07:07.759 --> 00:07:09.510 align:start position:0%
think we've already got enough
information<00:07:08.240><c> to</c><00:07:08.400><c> really</c><00:07:08.680><c> analyze</c><00:07:09.120><c> the</c><00:07:09.280><c> data</c>

00:07:09.510 --> 00:07:09.520 align:start position:0%
information to really analyze the data
 

00:07:09.520 --> 00:07:11.029 align:start position:0%
information to really analyze the data
at<00:07:09.680><c> this</c><00:07:09.879><c> point</c><00:07:10.160><c> but</c><00:07:10.240><c> I</c><00:07:10.360><c> just</c><00:07:10.440><c> wanted</c><00:07:10.680><c> to</c><00:07:10.840><c> show</c>

00:07:11.029 --> 00:07:11.039 align:start position:0%
at this point but I just wanted to show
 

00:07:11.039 --> 00:07:13.510 align:start position:0%
at this point but I just wanted to show
you<00:07:11.240><c> guys</c><00:07:11.479><c> this</c><00:07:11.879><c> this</c><00:07:12.039><c> feature</c><00:07:12.520><c> over</c><00:07:12.759><c> here</c>

00:07:13.510 --> 00:07:13.520 align:start position:0%
you guys this this feature over here
 

00:07:13.520 --> 00:07:14.830 align:start position:0%
you guys this this feature over here
maybe<00:07:13.759><c> we</c><00:07:13.840><c> should</c><00:07:14.000><c> add</c><00:07:14.160><c> it</c><00:07:14.240><c> to</c><00:07:14.360><c> the</c><00:07:14.520><c> third</c>

00:07:14.830 --> 00:07:14.840 align:start position:0%
maybe we should add it to the third
 

00:07:14.840 --> 00:07:16.230 align:start position:0%
maybe we should add it to the third
question<00:07:15.240><c> over</c><00:07:15.440><c> here</c><00:07:15.680><c> let's</c><00:07:15.800><c> look</c><00:07:15.960><c> at</c><00:07:16.039><c> the</c>

00:07:16.230 --> 00:07:16.240 align:start position:0%
question over here let's look at the
 

00:07:16.240 --> 00:07:18.029 align:start position:0%
question over here let's look at the
third<00:07:16.440><c> highest</c><00:07:16.680><c> converting</c><00:07:17.160><c> subp</c><00:07:17.520><c> Publishers</c>

00:07:18.029 --> 00:07:18.039 align:start position:0%
third highest converting subp Publishers
 

00:07:18.039 --> 00:07:19.869 align:start position:0%
third highest converting subp Publishers
with<00:07:18.240><c> at</c><00:07:18.360><c> least</c><00:07:18.680><c> three</c>

00:07:19.869 --> 00:07:19.879 align:start position:0%
with at least three
 

00:07:19.879 --> 00:07:22.469 align:start position:0%
with at least three
deposits<00:07:20.879><c> okay</c><00:07:21.319><c> with</c><00:07:21.520><c> at</c>

00:07:22.469 --> 00:07:22.479 align:start position:0%
deposits okay with at
 

00:07:22.479 --> 00:07:25.070 align:start position:0%
deposits okay with at
least<00:07:23.479><c> uh</c><00:07:23.800><c> we</c><00:07:23.879><c> can</c><00:07:24.080><c> rename</c><00:07:24.520><c> this</c><00:07:24.800><c> with</c><00:07:24.960><c> at</c>

00:07:25.070 --> 00:07:25.080 align:start position:0%
least uh we can rename this with at
 

00:07:25.080 --> 00:07:26.830 align:start position:0%
least uh we can rename this with at
least<00:07:25.560><c> okay</c><00:07:25.759><c> this</c><00:07:25.840><c> is</c><00:07:25.960><c> going</c><00:07:26.080><c> to</c><00:07:26.199><c> be</c><00:07:26.360><c> easier</c>

00:07:26.830 --> 00:07:26.840 align:start position:0%
least okay this is going to be easier
 

00:07:26.840 --> 00:07:31.550 align:start position:0%
least okay this is going to be easier
with<00:07:27.000><c> at</c><00:07:27.120><c> least</c><00:07:27.400><c> X</c><00:07:27.840><c> deposits</c>

00:07:31.550 --> 00:07:31.560 align:start position:0%
 
 

00:07:31.560 --> 00:07:35.110 align:start position:0%
 
okay<00:07:32.120><c> sum</c><00:07:32.400><c> of</c><00:07:32.680><c> deposits</c><00:07:33.400><c> is</c><00:07:33.680><c> greater</c>

00:07:35.110 --> 00:07:35.120 align:start position:0%
okay sum of deposits is greater
 

00:07:35.120 --> 00:07:42.350 align:start position:0%
okay sum of deposits is greater
than<00:07:36.120><c> over</c><00:07:36.720><c> here</c><00:07:37.879><c> Minore</c><00:07:38.879><c> sum</c><00:07:39.560><c> of</c>

00:07:42.350 --> 00:07:42.360 align:start position:0%
 
 

00:07:42.360 --> 00:07:45.589 align:start position:0%
 
deposits<00:07:43.360><c> and</c><00:07:44.000><c> required</c><00:07:45.000><c> we're</c><00:07:45.199><c> going</c><00:07:45.280><c> to</c><00:07:45.479><c> put</c>

00:07:45.589 --> 00:07:45.599 align:start position:0%
deposits and required we're going to put
 

00:07:45.599 --> 00:07:48.230 align:start position:0%
deposits and required we're going to put
the<00:07:45.759><c> default</c><00:07:46.199><c> value</c><00:07:46.479><c> as</c><00:07:46.800><c> zero</c><00:07:47.800><c> we're</c><00:07:47.960><c> going</c><00:07:48.080><c> to</c>

00:07:48.230 --> 00:07:48.240 align:start position:0%
the default value as zero we're going to
 

00:07:48.240 --> 00:07:51.390 align:start position:0%
the default value as zero we're going to
press<00:07:48.599><c> enter</c><00:07:49.599><c> and</c><00:07:50.159><c> okay</c><00:07:50.400><c> cool</c><00:07:50.800><c> deposits</c>

00:07:51.390 --> 00:07:51.400 align:start position:0%
press enter and okay cool deposits
 

00:07:51.400 --> 00:07:52.990 align:start position:0%
press enter and okay cool deposits
conversion<00:07:51.879><c> rate</c><00:07:52.159><c> everything</c><00:07:52.440><c> is</c><00:07:52.639><c> good</c><00:07:52.879><c> we're</c>

00:07:52.990 --> 00:07:53.000 align:start position:0%
conversion rate everything is good we're
 

00:07:53.000 --> 00:07:57.629 align:start position:0%
conversion rate everything is good we're
going<00:07:53.080><c> to</c><00:07:53.199><c> press</c><00:07:53.919><c> save</c><00:07:55.280><c> save</c><00:07:56.280><c> go</c><00:07:56.520><c> back</c><00:07:56.759><c> into</c>

00:07:57.629 --> 00:07:57.639 align:start position:0%
going to press save save go back into
 

00:07:57.639 --> 00:07:59.950 align:start position:0%
going to press save save go back into
our<00:07:58.400><c> dashboard</c>

00:07:59.950 --> 00:07:59.960 align:start position:0%
our dashboard
 

00:07:59.960 --> 00:08:02.110 align:start position:0%
our dashboard
and<00:08:00.199><c> now</c><00:08:00.599><c> now</c><00:08:00.759><c> that</c><00:08:00.879><c> we</c><00:08:01.039><c> created</c><00:08:01.400><c> a</c><00:08:01.520><c> filter</c><00:08:01.879><c> on</c>

00:08:02.110 --> 00:08:02.120 align:start position:0%
and now now that we created a filter on
 

00:08:02.120 --> 00:08:04.909 align:start position:0%
and now now that we created a filter on
that<00:08:02.360><c> third</c><00:08:02.720><c> question</c><00:08:03.199><c> we</c><00:08:03.319><c> can</c><00:08:04.000><c> edit</c><00:08:04.520><c> this</c>

00:08:04.909 --> 00:08:04.919 align:start position:0%
that third question we can edit this
 

00:08:04.919 --> 00:08:07.950 align:start position:0%
that third question we can edit this
filter<00:08:05.560><c> over</c><00:08:05.879><c> there</c><00:08:06.120><c> and</c><00:08:06.280><c> we</c><00:08:06.520><c> want</c><00:08:06.680><c> to</c><00:08:07.440><c> uh</c><00:08:07.639><c> set</c>

00:08:07.950 --> 00:08:07.960 align:start position:0%
filter over there and we want to uh set
 

00:08:07.960 --> 00:08:11.070 align:start position:0%
filter over there and we want to uh set
that<00:08:08.159><c> filter</c><00:08:08.599><c> to</c><00:08:08.800><c> align</c><00:08:09.199><c> to</c><00:08:09.520><c> this</c><00:08:10.000><c> to</c><00:08:10.240><c> also</c><00:08:10.919><c> uh</c>

00:08:11.070 --> 00:08:11.080 align:start position:0%
that filter to align to this to also uh
 

00:08:11.080 --> 00:08:12.869 align:start position:0%
that filter to align to this to also uh
correlate<00:08:11.560><c> to</c><00:08:11.840><c> this</c><00:08:12.000><c> filter</c><00:08:12.400><c> on</c><00:08:12.680><c> this</c>

00:08:12.869 --> 00:08:12.879 align:start position:0%
correlate to this filter on this
 

00:08:12.879 --> 00:08:15.710 align:start position:0%
correlate to this filter on this
question<00:08:13.360><c> as</c><00:08:13.639><c> well</c><00:08:14.639><c> and</c><00:08:14.800><c> I'm</c><00:08:14.919><c> going</c><00:08:15.039><c> to</c><00:08:15.199><c> press</c>

00:08:15.710 --> 00:08:15.720 align:start position:0%
question as well and I'm going to press
 

00:08:15.720 --> 00:08:19.589 align:start position:0%
question as well and I'm going to press
done<00:08:16.560><c> and</c><00:08:17.520><c> save</c><00:08:18.520><c> and</c><00:08:18.639><c> the</c><00:08:18.800><c> client</c><00:08:19.159><c> can</c><00:08:19.319><c> really</c>

00:08:19.589 --> 00:08:19.599 align:start position:0%
done and save and the client can really
 

00:08:19.599 --> 00:08:22.950 align:start position:0%
done and save and the client can really
do<00:08:19.840><c> his</c><00:08:19.960><c> own</c><00:08:20.199><c> analysis</c><00:08:20.759><c> at</c><00:08:20.960><c> this</c><00:08:21.520><c> point</c><00:08:22.520><c> uh</c>

00:08:22.950 --> 00:08:22.960 align:start position:0%
do his own analysis at this point uh
 

00:08:22.960 --> 00:08:24.830 align:start position:0%
do his own analysis at this point uh
right<00:08:23.479><c> over</c><00:08:23.639><c> here</c><00:08:23.800><c> we</c><00:08:23.919><c> see</c><00:08:24.240><c> the</c><00:08:24.440><c> sum</c><00:08:24.680><c> of</c>

00:08:24.830 --> 00:08:24.840 align:start position:0%
right over here we see the sum of
 

00:08:24.840 --> 00:08:27.710 align:start position:0%
right over here we see the sum of
deposits<00:08:25.280><c> is</c><00:08:25.440><c> 1</c><00:08:25.680><c> one</c><00:08:25.919><c> one</c><00:08:26.159><c> one</c><00:08:26.400><c> one</c><00:08:27.000><c> I</c><00:08:27.159><c> don't</c><00:08:27.440><c> I</c>

00:08:27.710 --> 00:08:27.720 align:start position:0%
deposits is 1 one one one one I don't I
 

00:08:27.720 --> 00:08:29.230 align:start position:0%
deposits is 1 one one one one I don't I
I<00:08:27.759><c> don't</c><00:08:27.919><c> want</c><00:08:28.159><c> any</c><00:08:28.319><c> of</c><00:08:28.479><c> these</c><00:08:28.680><c> records</c><00:08:29.080><c> where</c>

00:08:29.230 --> 00:08:29.240 align:start position:0%
I don't want any of these records where
 

00:08:29.240 --> 00:08:30.830 align:start position:0%
I don't want any of these records where
the<00:08:29.520><c> sum</c><00:08:29.639><c> of</c><00:08:29.720><c> deposits</c><00:08:30.120><c> was</c><00:08:30.240><c> only</c><00:08:30.479><c> one</c><00:08:30.759><c> I</c>

00:08:30.830 --> 00:08:30.840 align:start position:0%
the sum of deposits was only one I
 

00:08:30.840 --> 00:08:33.269 align:start position:0%
the sum of deposits was only one I
wanted<00:08:31.159><c> where</c><00:08:31.319><c> the</c><00:08:31.440><c> sum</c><00:08:31.599><c> of</c><00:08:31.919><c> deposits</c><00:08:32.919><c> was</c><00:08:33.120><c> at</c>

00:08:33.269 --> 00:08:33.279 align:start position:0%
wanted where the sum of deposits was at
 

00:08:33.279 --> 00:08:36.230 align:start position:0%
wanted where the sum of deposits was at
least<00:08:33.760><c> three</c><00:08:34.760><c> and</c><00:08:34.959><c> now</c><00:08:35.240><c> that'll</c><00:08:35.560><c> also</c><00:08:35.800><c> reflect</c>

00:08:36.230 --> 00:08:36.240 align:start position:0%
least three and now that'll also reflect
 

00:08:36.240 --> 00:08:38.350 align:start position:0%
least three and now that'll also reflect
over<00:08:36.560><c> here</c><00:08:37.039><c> as</c><00:08:37.200><c> well</c><00:08:37.560><c> all</c><00:08:37.680><c> right</c><00:08:37.880><c> guys</c><00:08:38.000><c> so</c><00:08:38.200><c> then</c>

00:08:38.350 --> 00:08:38.360 align:start position:0%
over here as well all right guys so then
 

00:08:38.360 --> 00:08:40.550 align:start position:0%
over here as well all right guys so then
I<00:08:38.479><c> can</c><00:08:38.959><c> go</c><00:08:39.080><c> ahead</c><00:08:39.279><c> and</c><00:08:39.479><c> continue</c><00:08:39.919><c> analyzing</c>

00:08:40.550 --> 00:08:40.560 align:start position:0%
I can go ahead and continue analyzing
 

00:08:40.560 --> 00:08:43.230 align:start position:0%
I can go ahead and continue analyzing
the<00:08:40.800><c> data</c><00:08:41.800><c> and</c><00:08:42.320><c> uh</c><00:08:42.560><c> that's</c><00:08:42.719><c> how</c><00:08:42.839><c> we</c><00:08:42.959><c> set</c>

00:08:43.230 --> 00:08:43.240 align:start position:0%
the data and uh that's how we set
 

00:08:43.240 --> 00:08:46.790 align:start position:0%
the data and uh that's how we set
filters<00:08:43.839><c> on</c><00:08:44.399><c> the</c><00:08:44.880><c> um</c><00:08:45.160><c> the</c><00:08:45.480><c> question</c><00:08:45.839><c> level</c><00:08:46.440><c> and</c>

00:08:46.790 --> 00:08:46.800 align:start position:0%
filters on the um the question level and
 

00:08:46.800 --> 00:08:49.430 align:start position:0%
filters on the um the question level and
also<00:08:47.279><c> on</c><00:08:47.600><c> the</c><00:08:47.839><c> dashboard</c><00:08:48.600><c> after</c><00:08:48.880><c> we</c><00:08:49.000><c> set</c><00:08:49.200><c> it</c><00:08:49.320><c> on</c>

00:08:49.430 --> 00:08:49.440 align:start position:0%
also on the dashboard after we set it on
 

00:08:49.440 --> 00:08:51.030 align:start position:0%
also on the dashboard after we set it on
the<00:08:49.760><c> question</c><00:08:50.120><c> level</c><00:08:50.399><c> we</c><00:08:50.519><c> set</c><00:08:50.680><c> it</c><00:08:50.760><c> on</c><00:08:50.880><c> the</c>

00:08:51.030 --> 00:08:51.040 align:start position:0%
the question level we set it on the
 

00:08:51.040 --> 00:08:53.509 align:start position:0%
the question level we set it on the
dashboard<00:08:51.480><c> level</c><00:08:51.800><c> as</c><00:08:51.959><c> well</c><00:08:52.720><c> and</c><00:08:52.880><c> users</c><00:08:53.279><c> can</c>

00:08:53.509 --> 00:08:53.519 align:start position:0%
dashboard level as well and users can
 

00:08:53.519 --> 00:08:55.949 align:start position:0%
dashboard level as well and users can
intuitively<00:08:54.320><c> just</c><00:08:54.480><c> set</c><00:08:54.839><c> filters</c><00:08:55.360><c> over</c><00:08:55.640><c> there</c>

00:08:55.949 --> 00:08:55.959 align:start position:0%
intuitively just set filters over there
 

00:08:55.959 --> 00:08:57.550 align:start position:0%
intuitively just set filters over there
and<00:08:56.080><c> it'll</c><00:08:56.440><c> reflect</c><00:08:56.800><c> on</c><00:08:56.920><c> the</c><00:08:57.120><c> entire</c>

00:08:57.550 --> 00:08:57.560 align:start position:0%
and it'll reflect on the entire
 

00:08:57.560 --> 00:08:59.470 align:start position:0%
and it'll reflect on the entire
dashboard<00:08:58.519><c> all</c><00:08:58.640><c> right</c><00:08:58.800><c> guys</c><00:08:58.920><c> so</c><00:08:59.000><c> I</c><00:08:59.079><c> hope</c><00:08:59.200><c> you</c>

00:08:59.470 --> 00:08:59.480 align:start position:0%
dashboard all right guys so I hope you
 

00:08:59.480 --> 00:09:01.430 align:start position:0%
dashboard all right guys so I hope you
enjoyed<00:08:59.760><c> that</c><00:08:59.920><c> little</c><00:09:00.160><c> tutorial</c><00:09:00.760><c> of</c><00:09:00.920><c> how</c><00:09:01.000><c> to</c>

00:09:01.430 --> 00:09:01.440 align:start position:0%
enjoyed that little tutorial of how to
 

00:09:01.440 --> 00:09:04.470 align:start position:0%
enjoyed that little tutorial of how to
take<00:09:01.640><c> CSV</c><00:09:02.240><c> data</c><00:09:02.640><c> upload</c><00:09:02.959><c> it</c><00:09:03.040><c> to</c><00:09:03.200><c> metabase</c><00:09:04.160><c> run</c>

00:09:04.470 --> 00:09:04.480 align:start position:0%
take CSV data upload it to metabase run
 

00:09:04.480 --> 00:09:06.990 align:start position:0%
take CSV data upload it to metabase run
analysis<00:09:04.959><c> on</c><00:09:05.200><c> metabase</c><00:09:05.839><c> with</c><00:09:05.959><c> a</c><00:09:06.120><c> visual</c><00:09:06.560><c> query</c>

00:09:06.990 --> 00:09:07.000 align:start position:0%
analysis on metabase with a visual query
 

00:09:07.000 --> 00:09:09.829 align:start position:0%
analysis on metabase with a visual query
Builder<00:09:08.000><c> and</c><00:09:08.560><c> uh</c><00:09:08.720><c> with</c><00:09:08.839><c> metabase</c><00:09:09.440><c> custom</c>

00:09:09.829 --> 00:09:09.839 align:start position:0%
Builder and uh with metabase custom
 

00:09:09.839 --> 00:09:11.790 align:start position:0%
Builder and uh with metabase custom
filters<00:09:10.399><c> and</c><00:09:10.560><c> the</c><00:09:10.640><c> Sorting</c><00:09:11.120><c> features</c><00:09:11.600><c> and</c>

00:09:11.790 --> 00:09:11.800 align:start position:0%
filters and the Sorting features and
 

00:09:11.800 --> 00:09:13.829 align:start position:0%
filters and the Sorting features and
custom<00:09:12.120><c> SQL</c><00:09:12.560><c> syntax</c><00:09:13.279><c> if</c><00:09:13.360><c> you</c><00:09:13.480><c> have</c><00:09:13.600><c> any</c>

00:09:13.829 --> 00:09:13.839 align:start position:0%
custom SQL syntax if you have any
 

00:09:13.839 --> 00:09:16.230 align:start position:0%
custom SQL syntax if you have any
questions<00:09:14.160><c> or</c><00:09:14.320><c> need</c><00:09:14.560><c> any</c><00:09:14.839><c> help</c><00:09:15.720><c> uh</c><00:09:15.839><c> along</c><00:09:16.120><c> the</c>

00:09:16.230 --> 00:09:16.240 align:start position:0%
questions or need any help uh along the
 

00:09:16.240 --> 00:09:18.190 align:start position:0%
questions or need any help uh along the
way<00:09:16.399><c> feel</c><00:09:16.560><c> free</c><00:09:16.760><c> to</c><00:09:16.920><c> reach</c><00:09:17.160><c> out</c><00:09:17.560><c> and</c><00:09:17.680><c> I</c><00:09:17.839><c> really</c>

00:09:18.190 --> 00:09:18.200 align:start position:0%
way feel free to reach out and I really
 

00:09:18.200 --> 00:09:20.310 align:start position:0%
way feel free to reach out and I really
hope<00:09:18.399><c> you</c><00:09:18.600><c> guys</c><00:09:18.880><c> enjoyed</c><00:09:19.279><c> that</c><00:09:19.440><c> tutorial</c><00:09:20.160><c> best</c>

00:09:20.310 --> 00:09:20.320 align:start position:0%
hope you guys enjoyed that tutorial best
 

00:09:20.320 --> 00:09:22.949 align:start position:0%
hope you guys enjoyed that tutorial best
of<00:09:20.519><c> luck</c><00:09:20.760><c> to</c><00:09:21.000><c> you</c><00:09:21.720><c> and</c><00:09:22.000><c> have</c><00:09:22.240><c> an</c><00:09:22.440><c> amazing</c>

00:09:22.949 --> 00:09:22.959 align:start position:0%
of luck to you and have an amazing
 

00:09:22.959 --> 00:09:26.519 align:start position:0%
of luck to you and have an amazing
amazing<00:09:23.519><c> day</c>

