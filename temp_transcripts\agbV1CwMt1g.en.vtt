WEBVTT
Kind: captions
Language: en

00:00:00.319 --> 00:00:02.930 align:start position:0%
 
in<00:00:01.319><c> this</c><00:00:01.439><c> video</c><00:00:01.620><c> I</c><00:00:02.159><c> will</c><00:00:02.280><c> demonstrate</c><00:00:02.760><c> the</c>

00:00:02.930 --> 00:00:02.940 align:start position:0%
in this video I will demonstrate the
 

00:00:02.940 --> 00:00:04.970 align:start position:0%
in this video I will demonstrate the
steps<00:00:03.240><c> for</c><00:00:03.419><c> subscribing</c><00:00:04.020><c> to</c><00:00:04.259><c> Johnson</c><00:00:04.440><c> Labs</c>

00:00:04.970 --> 00:00:04.980 align:start position:0%
steps for subscribing to Johnson Labs
 

00:00:04.980 --> 00:00:08.390 align:start position:0%
steps for subscribing to Johnson Labs
NLP<00:00:05.339><c> libraries</c><00:00:05.819><c> via</c><00:00:06.420><c> AWS</c><00:00:07.080><c> Marketplace</c><00:00:07.620><c> and</c><00:00:08.220><c> to</c>

00:00:08.390 --> 00:00:08.400 align:start position:0%
NLP libraries via AWS Marketplace and to
 

00:00:08.400 --> 00:00:10.549 align:start position:0%
NLP libraries via AWS Marketplace and to
configure<00:00:08.820><c> a</c><00:00:09.179><c> dedicated</c><00:00:09.540><c> server</c><00:00:10.019><c> you</c><00:00:10.260><c> can</c><00:00:10.440><c> use</c>

00:00:10.549 --> 00:00:10.559 align:start position:0%
configure a dedicated server you can use
 

00:00:10.559 --> 00:00:12.530 align:start position:0%
configure a dedicated server you can use
for<00:00:10.800><c> processing</c><00:00:11.219><c> your</c><00:00:11.400><c> documents</c>

00:00:12.530 --> 00:00:12.540 align:start position:0%
for processing your documents
 

00:00:12.540 --> 00:00:15.410 align:start position:0%
for processing your documents
for<00:00:12.960><c> start</c><00:00:13.200><c> go</c><00:00:13.740><c> to</c><00:00:13.860><c> AWS</c><00:00:14.519><c> Marketplace</c><00:00:15.000><c> and</c>

00:00:15.410 --> 00:00:15.420 align:start position:0%
for start go to AWS Marketplace and
 

00:00:15.420 --> 00:00:17.269 align:start position:0%
for start go to AWS Marketplace and
navigate<00:00:15.599><c> to</c><00:00:15.900><c> the</c><00:00:16.020><c> Johnstone</c><00:00:16.379><c> Labs</c><00:00:16.800><c> NLP</c>

00:00:17.269 --> 00:00:17.279 align:start position:0%
navigate to the Johnstone Labs NLP
 

00:00:17.279 --> 00:00:19.550 align:start position:0%
navigate to the Johnstone Labs NLP
libraries<00:00:17.699><c> product</c><00:00:18.000><c> page</c>

00:00:19.550 --> 00:00:19.560 align:start position:0%
libraries product page
 

00:00:19.560 --> 00:00:21.950 align:start position:0%
libraries product page
here<00:00:20.039><c> press</c><00:00:20.400><c> on</c><00:00:20.699><c> the</c><00:00:20.820><c> continue</c><00:00:21.000><c> to</c><00:00:21.359><c> subscribe</c>

00:00:21.950 --> 00:00:21.960 align:start position:0%
here press on the continue to subscribe
 

00:00:21.960 --> 00:00:23.150 align:start position:0%
here press on the continue to subscribe
button

00:00:23.150 --> 00:00:23.160 align:start position:0%
button
 

00:00:23.160 --> 00:00:25.070 align:start position:0%
button
read<00:00:23.699><c> through</c><00:00:23.939><c> the</c><00:00:24.119><c> terms</c><00:00:24.420><c> and</c><00:00:24.539><c> conditions</c>

00:00:25.070 --> 00:00:25.080 align:start position:0%
read through the terms and conditions
 

00:00:25.080 --> 00:00:27.650 align:start position:0%
read through the terms and conditions
and<00:00:25.680><c> if</c><00:00:25.980><c> you</c><00:00:26.100><c> agree</c><00:00:26.340><c> of</c><00:00:26.580><c> course</c><00:00:26.699><c> accept</c><00:00:27.300><c> the</c>

00:00:27.650 --> 00:00:27.660 align:start position:0%
and if you agree of course accept the
 

00:00:27.660 --> 00:00:28.730 align:start position:0%
and if you agree of course accept the
terms

00:00:28.730 --> 00:00:28.740 align:start position:0%
terms
 

00:00:28.740 --> 00:00:30.950 align:start position:0%
terms
on<00:00:29.279><c> this</c><00:00:29.460><c> page</c><00:00:29.640><c> wait</c><00:00:30.000><c> until</c><00:00:30.300><c> the</c><00:00:30.599><c> subscription</c>

00:00:30.950 --> 00:00:30.960 align:start position:0%
on this page wait until the subscription
 

00:00:30.960 --> 00:00:33.350 align:start position:0%
on this page wait until the subscription
is<00:00:31.199><c> created</c><00:00:31.560><c> and</c><00:00:32.340><c> then</c><00:00:32.460><c> choose</c><00:00:32.820><c> continue</c><00:00:33.059><c> to</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
is created and then choose continue to
 

00:00:33.360 --> 00:00:35.270 align:start position:0%
is created and then choose continue to
configuration

00:00:35.270 --> 00:00:35.280 align:start position:0%
configuration
 

00:00:35.280 --> 00:00:37.610 align:start position:0%
configuration
here<00:00:35.760><c> you</c><00:00:36.000><c> need</c><00:00:36.120><c> to</c><00:00:36.239><c> select</c><00:00:36.540><c> the</c><00:00:36.780><c> details</c><00:00:37.140><c> of</c>

00:00:37.610 --> 00:00:37.620 align:start position:0%
here you need to select the details of
 

00:00:37.620 --> 00:00:39.229 align:start position:0%
here you need to select the details of
the<00:00:37.800><c> configuration</c><00:00:38.340><c> that</c><00:00:38.700><c> you</c><00:00:38.820><c> want</c><00:00:38.940><c> to</c><00:00:39.059><c> use</c>

00:00:39.229 --> 00:00:39.239 align:start position:0%
the configuration that you want to use
 

00:00:39.239 --> 00:00:40.670 align:start position:0%
the configuration that you want to use
for<00:00:39.600><c> instance</c><00:00:39.840><c> the</c><00:00:40.079><c> version</c><00:00:40.260><c> of</c><00:00:40.500><c> the</c>

00:00:40.670 --> 00:00:40.680 align:start position:0%
for instance the version of the
 

00:00:40.680 --> 00:00:42.650 align:start position:0%
for instance the version of the
libraries<00:00:41.040><c> by</c><00:00:41.579><c> default</c><00:00:41.940><c> the</c><00:00:42.120><c> latest</c><00:00:42.300><c> version</c>

00:00:42.650 --> 00:00:42.660 align:start position:0%
libraries by default the latest version
 

00:00:42.660 --> 00:00:44.030 align:start position:0%
libraries by default the latest version
is<00:00:43.020><c> selected</c>

00:00:44.030 --> 00:00:44.040 align:start position:0%
is selected
 

00:00:44.040 --> 00:00:46.549 align:start position:0%
is selected
when<00:00:44.399><c> done</c><00:00:44.640><c> click</c><00:00:45.300><c> on</c><00:00:45.899><c> the</c><00:00:46.079><c> continue</c><00:00:46.260><c> to</c>

00:00:46.549 --> 00:00:46.559 align:start position:0%
when done click on the continue to
 

00:00:46.559 --> 00:00:48.049 align:start position:0%
when done click on the continue to
launch<00:00:46.920><c> button</c>

00:00:48.049 --> 00:00:48.059 align:start position:0%
launch button
 

00:00:48.059 --> 00:00:50.090 align:start position:0%
launch button
on<00:00:48.420><c> this</c><00:00:48.539><c> page</c><00:00:48.719><c> select</c><00:00:49.200><c> the</c><00:00:49.320><c> type</c><00:00:49.500><c> of</c><00:00:49.680><c> instance</c>

00:00:50.090 --> 00:00:50.100 align:start position:0%
on this page select the type of instance
 

00:00:50.100 --> 00:00:51.590 align:start position:0%
on this page select the type of instance
you<00:00:50.280><c> want</c><00:00:50.460><c> to</c><00:00:50.579><c> use</c><00:00:50.700><c> for</c><00:00:51.000><c> deploying</c><00:00:51.420><c> your</c>

00:00:51.590 --> 00:00:51.600 align:start position:0%
you want to use for deploying your
 

00:00:51.600 --> 00:00:53.029 align:start position:0%
you want to use for deploying your
application

00:00:53.029 --> 00:00:53.039 align:start position:0%
application
 

00:00:53.039 --> 00:00:55.029 align:start position:0%
application
then<00:00:53.460><c> create</c><00:00:53.760><c> a</c><00:00:54.180><c> new</c><00:00:54.300><c> Security</c><00:00:54.539><c> Group</c>

00:00:55.029 --> 00:00:55.039 align:start position:0%
then create a new Security Group
 

00:00:55.039 --> 00:00:58.069 align:start position:0%
then create a new Security Group
specifying<00:00:56.039><c> name</c><00:00:56.280><c> and</c><00:00:56.640><c> a</c><00:00:56.820><c> description</c><00:00:57.120><c> for</c><00:00:57.480><c> it</c>

00:00:58.069 --> 00:00:58.079 align:start position:0%
specifying name and a description for it
 

00:00:58.079 --> 00:01:01.069 align:start position:0%
specifying name and a description for it
this<00:00:58.620><c> ensures</c><00:00:59.039><c> that</c><00:00:59.399><c> the</c><00:00:59.579><c> port</c><00:00:59.760><c> 5000</c><00:01:00.600><c> is</c><00:01:00.840><c> open</c>

00:01:01.069 --> 00:01:01.079 align:start position:0%
this ensures that the port 5000 is open
 

00:01:01.079 --> 00:01:03.709 align:start position:0%
this ensures that the port 5000 is open
so<00:01:01.379><c> that</c><00:01:01.559><c> Jupiter</c><00:01:01.980><c> lab</c><00:01:02.219><c> is</c><00:01:02.460><c> accessible</c><00:01:02.940><c> as</c>

00:01:03.709 --> 00:01:03.719 align:start position:0%
so that Jupiter lab is accessible as
 

00:01:03.719 --> 00:01:06.770 align:start position:0%
so that Jupiter lab is accessible as
well<00:01:03.899><c> as</c><00:01:04.199><c> Port</c><00:01:04.559><c> 80</c><00:01:04.860><c> is</c><00:01:05.220><c> available</c><00:01:05.400><c> via</c><00:01:06.000><c> HTTP</c><00:01:06.540><c> so</c>

00:01:06.770 --> 00:01:06.780 align:start position:0%
well as Port 80 is available via HTTP so
 

00:01:06.780 --> 00:01:08.390 align:start position:0%
well as Port 80 is available via HTTP so
that<00:01:06.960><c> you</c><00:01:07.080><c> can</c><00:01:07.260><c> access</c><00:01:07.439><c> the</c><00:01:07.799><c> products</c><00:01:08.220><c> welcome</c>

00:01:08.390 --> 00:01:08.400 align:start position:0%
that you can access the products welcome
 

00:01:08.400 --> 00:01:09.770 align:start position:0%
that you can access the products welcome
page

00:01:09.770 --> 00:01:09.780 align:start position:0%
page
 

00:01:09.780 --> 00:01:13.130 align:start position:0%
page
click<00:01:10.260><c> on</c><00:01:10.380><c> launch</c><00:01:10.799><c> button</c>

00:01:13.130 --> 00:01:13.140 align:start position:0%
click on launch button
 

00:01:13.140 --> 00:01:15.530 align:start position:0%
click on launch button
a<00:01:13.799><c> new</c><00:01:13.979><c> ec2</c><00:01:14.400><c> instance</c><00:01:14.760><c> is</c><00:01:15.000><c> now</c><00:01:15.240><c> launched</c>

00:01:15.530 --> 00:01:15.540 align:start position:0%
a new ec2 instance is now launched
 

00:01:15.540 --> 00:01:17.149 align:start position:0%
a new ec2 instance is now launched
behind<00:01:15.960><c> the</c><00:01:16.080><c> scenes</c><00:01:16.439><c> and</c><00:01:16.619><c> you</c><00:01:16.740><c> will</c><00:01:16.860><c> be</c><00:01:16.979><c> able</c>

00:01:17.149 --> 00:01:17.159 align:start position:0%
behind the scenes and you will be able
 

00:01:17.159 --> 00:01:20.149 align:start position:0%
behind the scenes and you will be able
to<00:01:17.280><c> see</c><00:01:17.580><c> it</c><00:01:17.700><c> in</c><00:01:18.000><c> the</c><00:01:18.119><c> ec2</c><00:01:18.540><c> console</c><00:01:19.380><c> we</c><00:01:19.860><c> see</c><00:01:19.979><c> here</c>

00:01:20.149 --> 00:01:20.159 align:start position:0%
to see it in the ec2 console we see here
 

00:01:20.159 --> 00:01:22.910 align:start position:0%
to see it in the ec2 console we see here
the<00:01:20.400><c> instance</c><00:01:20.759><c> is</c><00:01:21.000><c> in</c><00:01:21.240><c> pending</c><00:01:21.720><c> state</c>

00:01:22.910 --> 00:01:22.920 align:start position:0%
the instance is in pending state
 

00:01:22.920 --> 00:01:24.950 align:start position:0%
the instance is in pending state
click<00:01:23.400><c> on</c><00:01:23.520><c> the</c><00:01:23.640><c> instance</c><00:01:24.000><c> ID</c><00:01:24.180><c> and</c><00:01:24.600><c> see</c><00:01:24.780><c> the</c>

00:01:24.950 --> 00:01:24.960 align:start position:0%
click on the instance ID and see the
 

00:01:24.960 --> 00:01:26.450 align:start position:0%
click on the instance ID and see the
details<00:01:25.380><c> of</c><00:01:25.500><c> the</c><00:01:25.680><c> machine</c>

00:01:26.450 --> 00:01:26.460 align:start position:0%
details of the machine
 

00:01:26.460 --> 00:01:28.429 align:start position:0%
details of the machine
the<00:01:26.880><c> product's</c><00:01:27.299><c> welcome</c><00:01:27.420><c> page</c><00:01:27.780><c> is</c><00:01:28.020><c> accessible</c>

00:01:28.429 --> 00:01:28.439 align:start position:0%
the product's welcome page is accessible
 

00:01:28.439 --> 00:01:31.010 align:start position:0%
the product's welcome page is accessible
via<00:01:28.860><c> HTTP</c><00:01:29.460><c> on</c><00:01:29.880><c> the</c><00:01:30.060><c> IP</c><00:01:30.299><c> address</c><00:01:30.540><c> of</c><00:01:30.840><c> the</c>

00:01:31.010 --> 00:01:31.020 align:start position:0%
via HTTP on the IP address of the
 

00:01:31.020 --> 00:01:33.590 align:start position:0%
via HTTP on the IP address of the
machine<00:01:31.200><c> you</c><00:01:32.159><c> can</c><00:01:32.280><c> access</c><00:01:32.580><c> ready</c><00:01:33.180><c> to</c><00:01:33.479><c> run</c>

00:01:33.590 --> 00:01:33.600 align:start position:0%
machine you can access ready to run
 

00:01:33.600 --> 00:01:35.569 align:start position:0%
machine you can access ready to run
Jupiter<00:01:34.259><c> notebooks</c><00:01:34.740><c> for</c><00:01:34.920><c> each</c><00:01:35.159><c> one</c><00:01:35.280><c> of</c><00:01:35.460><c> the</c>

00:01:35.569 --> 00:01:35.579 align:start position:0%
Jupiter notebooks for each one of the
 

00:01:35.579 --> 00:01:37.910 align:start position:0%
Jupiter notebooks for each one of the
included<00:01:35.939><c> libraries</c><00:01:36.420><c> by</c><00:01:37.140><c> clicking</c><00:01:37.560><c> on</c><00:01:37.680><c> the</c>

00:01:37.910 --> 00:01:37.920 align:start position:0%
included libraries by clicking on the
 

00:01:37.920 --> 00:01:39.710 align:start position:0%
included libraries by clicking on the
corresponding<00:01:38.460><c> button</c><00:01:38.820><c> for</c><00:01:39.180><c> instance</c><00:01:39.420><c> Park</c>

00:01:39.710 --> 00:01:39.720 align:start position:0%
corresponding button for instance Park
 

00:01:39.720 --> 00:01:42.530 align:start position:0%
corresponding button for instance Park
LLP<00:01:40.200><c> you</c><00:01:41.040><c> will</c><00:01:41.220><c> get</c><00:01:41.340><c> redirected</c><00:01:41.939><c> to</c><00:01:42.060><c> Jupiter</c>

00:01:42.530 --> 00:01:42.540 align:start position:0%
LLP you will get redirected to Jupiter
 

00:01:42.540 --> 00:01:45.109 align:start position:0%
LLP you will get redirected to Jupiter
lab<00:01:42.720><c> the</c><00:01:43.619><c> password</c><00:01:43.799><c> for</c><00:01:44.159><c> accessing</c><00:01:44.579><c> Jupiter</c>

00:01:45.109 --> 00:01:45.119 align:start position:0%
lab the password for accessing Jupiter
 

00:01:45.119 --> 00:01:47.020 align:start position:0%
lab the password for accessing Jupiter
lab<00:01:45.240><c> is</c><00:01:45.479><c> the</c><00:01:45.659><c> instance</c><00:01:46.020><c> ID</c>

00:01:47.020 --> 00:01:47.030 align:start position:0%
lab is the instance ID
 

00:01:47.030 --> 00:01:50.990 align:start position:0%
lab is the instance ID
[Music]

00:01:50.990 --> 00:01:51.000 align:start position:0%
 
 

00:01:51.000 --> 00:01:53.330 align:start position:0%
 
on<00:01:51.479><c> Jupiter</c><00:01:51.960><c> lab</c><00:01:52.140><c> you</c><00:01:52.439><c> see</c><00:01:52.619><c> the</c><00:01:52.740><c> ready</c><00:01:52.920><c> to</c><00:01:53.159><c> use</c>

00:01:53.330 --> 00:01:53.340 align:start position:0%
on Jupiter lab you see the ready to use
 

00:01:53.340 --> 00:01:55.730 align:start position:0%
on Jupiter lab you see the ready to use
notebooks<00:01:54.119><c> which</c><00:01:54.600><c> are</c><00:01:54.780><c> included</c><00:01:55.200><c> into</c><00:01:55.439><c> the</c>

00:01:55.730 --> 00:01:55.740 align:start position:0%
notebooks which are included into the
 

00:01:55.740 --> 00:01:56.810 align:start position:0%
notebooks which are included into the
product

00:01:56.810 --> 00:01:56.820 align:start position:0%
product
 

00:01:56.820 --> 00:01:58.969 align:start position:0%
product
click<00:01:57.299><c> on</c><00:01:57.420><c> one</c><00:01:57.600><c> of</c><00:01:57.720><c> them</c><00:01:57.899><c> and</c><00:01:58.740><c> start</c>

00:01:58.969 --> 00:01:58.979 align:start position:0%
click on one of them and start
 

00:01:58.979 --> 00:02:03.469 align:start position:0%
click on one of them and start
experimenting

00:02:03.469 --> 00:02:03.479 align:start position:0%
 
 

00:02:03.479 --> 00:02:05.209 align:start position:0%
 
in<00:02:03.960><c> this</c><00:02:04.079><c> video</c><00:02:04.200><c> you</c><00:02:04.500><c> have</c><00:02:04.619><c> seen</c><00:02:04.860><c> how</c><00:02:05.100><c> to</c>

00:02:05.209 --> 00:02:05.219 align:start position:0%
in this video you have seen how to
 

00:02:05.219 --> 00:02:07.130 align:start position:0%
in this video you have seen how to
subscribe<00:02:05.579><c> to</c><00:02:05.759><c> Johnson</c><00:02:05.939><c> Labs</c><00:02:06.420><c> NLP</c><00:02:06.780><c> libraries</c>

00:02:07.130 --> 00:02:07.140 align:start position:0%
subscribe to Johnson Labs NLP libraries
 

00:02:07.140 --> 00:02:10.070 align:start position:0%
subscribe to Johnson Labs NLP libraries
via<00:02:07.439><c> AWS</c><00:02:08.039><c> Marketplace</c><00:02:08.640><c> and</c><00:02:09.300><c> how</c><00:02:09.479><c> to</c><00:02:09.599><c> configure</c>

00:02:10.070 --> 00:02:10.080 align:start position:0%
via AWS Marketplace and how to configure
 

00:02:10.080 --> 00:02:12.050 align:start position:0%
via AWS Marketplace and how to configure
a<00:02:10.440><c> ready-to-use</c><00:02:10.979><c> server</c><00:02:11.340><c> for</c><00:02:11.520><c> processing</c>

00:02:12.050 --> 00:02:12.060 align:start position:0%
a ready-to-use server for processing
 

00:02:12.060 --> 00:02:14.180 align:start position:0%
a ready-to-use server for processing
your<00:02:12.180><c> documents</c><00:02:12.720><c> in</c><00:02:13.319><c> just</c><00:02:13.500><c> 5</c><00:02:13.860><c> minutes</c>

00:02:14.180 --> 00:02:14.190 align:start position:0%
your documents in just 5 minutes
 

00:02:14.190 --> 00:02:17.849 align:start position:0%
your documents in just 5 minutes
[Music]

