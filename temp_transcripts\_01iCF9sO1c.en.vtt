WEBVTT
Kind: captions
Language: en

00:00:01.280 --> 00:00:03.110 align:start position:0%
 
hey<00:00:01.439><c> everyone</c><00:00:01.880><c> and</c><00:00:02.080><c> welcome</c><00:00:02.320><c> to</c><00:00:02.480><c> 15</c><00:00:02.840><c> minutes</c>

00:00:03.110 --> 00:00:03.120 align:start position:0%
hey everyone and welcome to 15 minutes
 

00:00:03.120 --> 00:00:05.510 align:start position:0%
hey everyone and welcome to 15 minutes
to<00:00:03.280><c> merge</c><00:00:03.879><c> I'm</c><00:00:04.080><c> April</c><00:00:04.400><c> Edwards</c><00:00:04.920><c> I'm</c><00:00:05.040><c> your</c><00:00:05.240><c> host</c>

00:00:05.510 --> 00:00:05.520 align:start position:0%
to merge I'm April <PERSON> I'm your host
 

00:00:05.520 --> 00:00:07.150 align:start position:0%
to merge I'm April <PERSON> I'm your host
and<00:00:05.680><c> with</c><00:00:05.799><c> us</c><00:00:05.960><c> today</c><00:00:06.200><c> we</c><00:00:06.279><c> have</c><00:00:06.399><c> a</c><00:00:06.560><c> very</c><00:00:06.799><c> special</c>

00:00:07.150 --> 00:00:07.160 align:start position:0%
and with us today we have a very special
 

00:00:07.160 --> 00:00:10.870 align:start position:0%
and with us today we have a very special
guest<00:00:07.520><c> Mark</c><00:00:07.839><c> dier</c><00:00:08.519><c> welcome</c><00:00:09.240><c> Mark</c><00:00:10.240><c> heyel</c><00:00:10.759><c> nice</c>

00:00:10.870 --> 00:00:10.880 align:start position:0%
guest Mark dier welcome Mark heyel nice
 

00:00:10.880 --> 00:00:13.789 align:start position:0%
guest Mark dier welcome Mark heyel nice
to<00:00:11.000><c> be</c><00:00:11.160><c> here</c><00:00:11.880><c> great</c><00:00:12.080><c> to</c><00:00:12.200><c> have</c><00:00:12.320><c> you</c><00:00:12.519><c> back</c><00:00:12.880><c> so</c><00:00:13.440><c> you</c>

00:00:13.789 --> 00:00:13.799 align:start position:0%
to be here great to have you back so you
 

00:00:13.799 --> 00:00:15.789 align:start position:0%
to be here great to have you back so you
work<00:00:14.120><c> for</c><00:00:14.480><c> a</c><00:00:14.639><c> really</c><00:00:14.839><c> cool</c><00:00:15.080><c> company</c><00:00:15.519><c> but</c><00:00:15.639><c> for</c>

00:00:15.789 --> 00:00:15.799 align:start position:0%
work for a really cool company but for
 

00:00:15.799 --> 00:00:16.910 align:start position:0%
work for a really cool company but for
everyone<00:00:16.080><c> that</c><00:00:16.199><c> doesn't</c><00:00:16.440><c> know</c><00:00:16.560><c> who</c><00:00:16.680><c> you</c><00:00:16.760><c> are</c>

00:00:16.910 --> 00:00:16.920 align:start position:0%
everyone that doesn't know who you are
 

00:00:16.920 --> 00:00:17.990 align:start position:0%
everyone that doesn't know who you are
can<00:00:17.039><c> you</c><00:00:17.160><c> tell</c><00:00:17.279><c> them</c><00:00:17.480><c> who</c><00:00:17.600><c> you</c><00:00:17.680><c> are</c><00:00:17.800><c> and</c><00:00:17.920><c> what</c>

00:00:17.990 --> 00:00:18.000 align:start position:0%
can you tell them who you are and what
 

00:00:18.000 --> 00:00:20.630 align:start position:0%
can you tell them who you are and what
do<00:00:18.160><c> you</c><00:00:18.279><c> do</c><00:00:19.279><c> hi</c><00:00:19.439><c> I'm</c><00:00:19.560><c> Mark</c><00:00:20.160><c> I'm</c><00:00:20.279><c> a</c><00:00:20.400><c> senior</c>

00:00:20.630 --> 00:00:20.640 align:start position:0%
do you do hi I'm Mark I'm a senior
 

00:00:20.640 --> 00:00:22.950 align:start position:0%
do you do hi I'm Mark I'm a senior
developer<00:00:21.039><c> Advocate</c><00:00:21.439><c> at</c><00:00:21.600><c> digit</c><00:00:22.240><c> and</c><00:00:22.400><c> at</c><00:00:22.560><c> digit</c>

00:00:22.950 --> 00:00:22.960 align:start position:0%
developer Advocate at digit and at digit
 

00:00:22.960 --> 00:00:24.589 align:start position:0%
developer Advocate at digit and at digit
we<00:00:23.080><c> build</c><00:00:23.320><c> products</c><00:00:23.599><c> for</c><00:00:23.800><c> developers</c><00:00:24.320><c> to</c>

00:00:24.589 --> 00:00:24.599 align:start position:0%
we build products for developers to
 

00:00:24.599 --> 00:00:26.669 align:start position:0%
we build products for developers to
build<00:00:24.880><c> and</c><00:00:25.039><c> run</c><00:00:25.519><c> distribute</c><00:00:26.000><c> applications</c>

00:00:26.669 --> 00:00:26.679 align:start position:0%
build and run distribute applications
 

00:00:26.679 --> 00:00:29.310 align:start position:0%
build and run distribute applications
that<00:00:26.800><c> are</c><00:00:27.000><c> built</c><00:00:27.240><c> on</c><00:00:27.400><c> deer</c><00:00:27.720><c> open</c><00:00:28.160><c> source</c><00:00:29.160><c> and</c>

00:00:29.310 --> 00:00:29.320 align:start position:0%
that are built on deer open source and
 

00:00:29.320 --> 00:00:30.550 align:start position:0%
that are built on deer open source and
since<00:00:29.480><c> you're</c><00:00:29.720><c> part</c><00:00:30.039><c> the</c><00:00:30.119><c> open</c><00:00:30.320><c> source</c>

00:00:30.550 --> 00:00:30.560 align:start position:0%
since you're part the open source
 

00:00:30.560 --> 00:00:32.589 align:start position:0%
since you're part the open source
Community<00:00:31.320><c> you</c><00:00:31.880><c> have</c><00:00:32.040><c> a</c><00:00:32.160><c> lot</c><00:00:32.279><c> of</c><00:00:32.399><c> people</c>

00:00:32.589 --> 00:00:32.599 align:start position:0%
Community you have a lot of people
 

00:00:32.599 --> 00:00:34.030 align:start position:0%
Community you have a lot of people
contributing<00:00:33.000><c> to</c><00:00:33.120><c> your</c><00:00:33.280><c> projects</c><00:00:33.840><c> and</c><00:00:33.960><c> I</c>

00:00:34.030 --> 00:00:34.040 align:start position:0%
contributing to your projects and I
 

00:00:34.040 --> 00:00:36.110 align:start position:0%
contributing to your projects and I
think<00:00:34.239><c> one</c><00:00:34.360><c> of</c><00:00:34.480><c> the</c><00:00:34.600><c> things</c><00:00:34.840><c> we</c><00:00:35.040><c> find</c><00:00:35.399><c> is</c><00:00:35.960><c> when</c>

00:00:36.110 --> 00:00:36.120 align:start position:0%
think one of the things we find is when
 

00:00:36.120 --> 00:00:37.549 align:start position:0%
think one of the things we find is when
we<00:00:36.239><c> need</c><00:00:36.399><c> to</c><00:00:36.520><c> get</c><00:00:36.680><c> started</c><00:00:37.280><c> sometimes</c><00:00:37.440><c> it's</c>

00:00:37.549 --> 00:00:37.559 align:start position:0%
we need to get started sometimes it's
 

00:00:37.559 --> 00:00:39.790 align:start position:0%
we need to get started sometimes it's
really<00:00:37.719><c> hard</c><00:00:37.840><c> to</c><00:00:38.000><c> get</c><00:00:38.120><c> started</c><00:00:38.559><c> with</c><00:00:39.160><c> um</c><00:00:39.680><c> just</c>

00:00:39.790 --> 00:00:39.800 align:start position:0%
really hard to get started with um just
 

00:00:39.800 --> 00:00:41.630 align:start position:0%
really hard to get started with um just
all<00:00:39.960><c> the</c><00:00:40.039><c> tooling</c><00:00:40.440><c> all</c><00:00:40.559><c> the</c><00:00:40.719><c> requirements</c>

00:00:41.630 --> 00:00:41.640 align:start position:0%
all the tooling all the requirements
 

00:00:41.640 --> 00:00:43.750 align:start position:0%
all the tooling all the requirements
just<00:00:41.960><c> all</c><00:00:42.160><c> things</c><00:00:42.320><c> we</c><00:00:42.440><c> need</c><00:00:42.520><c> to</c><00:00:42.680><c> install</c><00:00:43.440><c> so</c><00:00:43.640><c> in</c>

00:00:43.750 --> 00:00:43.760 align:start position:0%
just all things we need to install so in
 

00:00:43.760 --> 00:00:44.790 align:start position:0%
just all things we need to install so in
one<00:00:43.879><c> of</c><00:00:44.000><c> our</c><00:00:44.160><c> episodes</c><00:00:44.559><c> which</c><00:00:44.680><c> we'll</c>

00:00:44.790 --> 00:00:44.800 align:start position:0%
one of our episodes which we'll
 

00:00:44.800 --> 00:00:46.310 align:start position:0%
one of our episodes which we'll
reference<00:00:45.160><c> below</c><00:00:45.399><c> for</c><00:00:45.559><c> everyone</c><00:00:45.960><c> you</c><00:00:46.079><c> and</c><00:00:46.160><c> I</c>

00:00:46.310 --> 00:00:46.320 align:start position:0%
reference below for everyone you and I
 

00:00:46.320 --> 00:00:47.869 align:start position:0%
reference below for everyone you and I
talked<00:00:46.559><c> about</c><00:00:46.760><c> the</c><00:00:46.879><c> importance</c><00:00:47.320><c> of</c><00:00:47.440><c> a</c><00:00:47.600><c> Dev</c>

00:00:47.869 --> 00:00:47.879 align:start position:0%
talked about the importance of a Dev
 

00:00:47.879 --> 00:00:50.510 align:start position:0%
talked about the importance of a Dev
container<00:00:48.360><c> in</c><00:00:48.480><c> your</c><00:00:49.079><c> repository</c><00:00:50.079><c> So</c><00:00:50.280><c> today</c>

00:00:50.510 --> 00:00:50.520 align:start position:0%
container in your repository So today
 

00:00:50.520 --> 00:00:51.750 align:start position:0%
container in your repository So today
we're<00:00:50.640><c> going</c><00:00:50.760><c> to</c><00:00:50.840><c> take</c><00:00:51.000><c> that</c><00:00:51.079><c> a</c><00:00:51.199><c> step</c><00:00:51.399><c> further</c>

00:00:51.750 --> 00:00:51.760 align:start position:0%
we're going to take that a step further
 

00:00:51.760 --> 00:00:53.069 align:start position:0%
we're going to take that a step further
and<00:00:51.840><c> we're</c><00:00:51.960><c> going</c><00:00:52.079><c> to</c><00:00:52.199><c> actually</c><00:00:52.359><c> spin</c><00:00:52.640><c> up</c><00:00:52.840><c> our</c>

00:00:53.069 --> 00:00:53.079 align:start position:0%
and we're going to actually spin up our
 

00:00:53.079 --> 00:00:55.229 align:start position:0%
and we're going to actually spin up our
Dev<00:00:53.359><c> container</c><00:00:53.800><c> in</c><00:00:54.160><c> a</c><00:00:54.320><c> GitHub</c><00:00:54.719><c> codes</c><00:00:55.000><c> space</c>

00:00:55.229 --> 00:00:55.239 align:start position:0%
Dev container in a GitHub codes space
 

00:00:55.239 --> 00:00:56.510 align:start position:0%
Dev container in a GitHub codes space
and<00:00:55.320><c> you're</c><00:00:55.440><c> going</c><00:00:55.520><c> to</c><00:00:55.640><c> show</c><00:00:55.800><c> us</c><00:00:55.920><c> how</c><00:00:56.000><c> to</c><00:00:56.120><c> do</c>

00:00:56.510 --> 00:00:56.520 align:start position:0%
and you're going to show us how to do
 

00:00:56.520 --> 00:00:59.430 align:start position:0%
and you're going to show us how to do
that<00:00:57.520><c> yes</c><00:00:57.719><c> exactly</c><00:00:58.320><c> yeah</c><00:00:58.519><c> I</c><00:00:58.719><c> really</c><00:00:58.920><c> love</c><00:00:59.199><c> Dev</c>

00:00:59.430 --> 00:00:59.440 align:start position:0%
that yes exactly yeah I really love Dev
 

00:00:59.440 --> 00:01:01.189 align:start position:0%
that yes exactly yeah I really love Dev
containers<00:01:00.000><c> and</c><00:01:00.160><c> especially</c><00:01:00.640><c> GI</c><00:01:00.800><c> of</c><00:01:00.960><c> cod</c>

00:01:01.189 --> 00:01:01.199 align:start position:0%
containers and especially GI of cod
 

00:01:01.199 --> 00:01:03.509 align:start position:0%
containers and especially GI of cod
spaces<00:01:01.680><c> because</c><00:01:02.199><c> with</c><00:01:02.399><c> GI</c><00:01:02.559><c> of</c><00:01:02.760><c> cod</c><00:01:03.000><c> space</c><00:01:03.399><c> yeah</c>

00:01:03.509 --> 00:01:03.519 align:start position:0%
spaces because with GI of cod space yeah
 

00:01:03.519 --> 00:01:05.070 align:start position:0%
spaces because with GI of cod space yeah
you<00:01:03.640><c> don't</c><00:01:03.960><c> even</c><00:01:04.199><c> have</c><00:01:04.320><c> to</c><00:01:04.479><c> run</c><00:01:04.680><c> your</c><00:01:04.839><c> code</c>

00:01:05.070 --> 00:01:05.080 align:start position:0%
you don't even have to run your code
 

00:01:05.080 --> 00:01:07.190 align:start position:0%
you don't even have to run your code
locally<00:01:05.439><c> anymore</c><00:01:05.799><c> or</c><00:01:06.479><c> download</c><00:01:06.880><c> the</c><00:01:07.000><c> def</c>

00:01:07.190 --> 00:01:07.200 align:start position:0%
locally anymore or download the def
 

00:01:07.200 --> 00:01:08.830 align:start position:0%
locally anymore or download the def
container<00:01:07.560><c> or</c><00:01:07.680><c> any</c><00:01:07.799><c> tools</c><00:01:08.159><c> locally</c><00:01:08.600><c> you</c><00:01:08.720><c> can</c>

00:01:08.830 --> 00:01:08.840 align:start position:0%
container or any tools locally you can
 

00:01:08.840 --> 00:01:10.710 align:start position:0%
container or any tools locally you can
just<00:01:09.000><c> do</c><00:01:09.159><c> it</c><00:01:09.240><c> straight</c><00:01:09.520><c> from</c><00:01:09.640><c> the</c><00:01:09.799><c> browser</c><00:01:10.520><c> so</c>

00:01:10.710 --> 00:01:10.720 align:start position:0%
just do it straight from the browser so
 

00:01:10.720 --> 00:01:12.310 align:start position:0%
just do it straight from the browser so
it's<00:01:10.880><c> really</c><00:01:11.080><c> a</c><00:01:11.240><c> big</c><00:01:11.479><c> timesaver</c><00:01:12.080><c> and</c><00:01:12.200><c> it</c>

00:01:12.310 --> 00:01:12.320 align:start position:0%
it's really a big timesaver and it
 

00:01:12.320 --> 00:01:14.070 align:start position:0%
it's really a big timesaver and it
really<00:01:12.520><c> enables</c><00:01:12.880><c> a</c><00:01:12.960><c> lot</c><00:01:13.080><c> of</c><00:01:13.240><c> people</c><00:01:13.479><c> to</c><00:01:13.759><c> help</c>

00:01:14.070 --> 00:01:14.080 align:start position:0%
really enables a lot of people to help
 

00:01:14.080 --> 00:01:16.350 align:start position:0%
really enables a lot of people to help
contribute<00:01:14.479><c> to</c><00:01:14.600><c> open</c><00:01:14.799><c> source</c><00:01:15.159><c> projects</c><00:01:16.159><c> and</c>

00:01:16.350 --> 00:01:16.360 align:start position:0%
contribute to open source projects and
 

00:01:16.360 --> 00:01:18.190 align:start position:0%
contribute to open source projects and
also<00:01:17.000><c> sometimes</c><00:01:17.200><c> our</c><00:01:17.320><c> machines</c><00:01:17.680><c> run</c><00:01:17.880><c> out</c><00:01:18.000><c> of</c>

00:01:18.190 --> 00:01:18.200 align:start position:0%
also sometimes our machines run out of
 

00:01:18.200 --> 00:01:20.149 align:start position:0%
also sometimes our machines run out of
compute<00:01:19.040><c> and</c><00:01:19.200><c> that's</c><00:01:19.400><c> happened</c><00:01:19.600><c> to</c><00:01:19.720><c> me</c><00:01:19.840><c> a</c><00:01:19.960><c> few</c>

00:01:20.149 --> 00:01:20.159 align:start position:0%
compute and that's happened to me a few
 

00:01:20.159 --> 00:01:22.789 align:start position:0%
compute and that's happened to me a few
times<00:01:20.840><c> so</c><00:01:21.159><c> we</c><00:01:21.280><c> have</c><00:01:21.400><c> the</c><00:01:21.520><c> ability</c><00:01:21.840><c> to</c><00:01:22.000><c> scale</c><00:01:22.439><c> up</c>

00:01:22.789 --> 00:01:22.799 align:start position:0%
times so we have the ability to scale up
 

00:01:22.799 --> 00:01:24.630 align:start position:0%
times so we have the ability to scale up
our<00:01:23.240><c> GitHub</c><00:01:23.600><c> code</c><00:01:23.799><c> space</c><00:01:24.159><c> to</c><00:01:24.320><c> get</c><00:01:24.439><c> more</c>

00:01:24.630 --> 00:01:24.640 align:start position:0%
our GitHub code space to get more
 

00:01:24.640 --> 00:01:26.069 align:start position:0%
our GitHub code space to get more
compute<00:01:25.000><c> when</c><00:01:25.119><c> we</c><00:01:25.200><c> need</c><00:01:25.320><c> it</c><00:01:25.479><c> and</c><00:01:25.680><c> scale</c><00:01:25.920><c> it</c>

00:01:26.069 --> 00:01:26.079 align:start position:0%
compute when we need it and scale it
 

00:01:26.079 --> 00:01:27.830 align:start position:0%
compute when we need it and scale it
down<00:01:26.240><c> if</c><00:01:26.320><c> we</c><00:01:26.439><c> don't</c><00:01:26.560><c> need</c><00:01:26.799><c> that</c><00:01:27.040><c> so</c><00:01:27.600><c> why</c><00:01:27.720><c> don't</c>

00:01:27.830 --> 00:01:27.840 align:start position:0%
down if we don't need that so why don't
 

00:01:27.840 --> 00:01:29.230 align:start position:0%
down if we don't need that so why don't
you<00:01:27.960><c> go</c><00:01:28.079><c> ahead</c><00:01:28.320><c> take</c><00:01:28.439><c> it</c><00:01:28.560><c> away</c><00:01:28.759><c> and</c><00:01:28.920><c> show</c><00:01:29.119><c> us</c>

00:01:29.230 --> 00:01:29.240 align:start position:0%
you go ahead take it away and show us
 

00:01:29.240 --> 00:01:30.950 align:start position:0%
you go ahead take it away and show us
how<00:01:29.360><c> we're</c><00:01:29.479><c> going</c><00:01:29.560><c> to</c><00:01:29.640><c> spin</c><00:01:29.799><c> up</c><00:01:30.000><c> a</c><00:01:30.079><c> GitHub</c><00:01:30.320><c> code</c>

00:01:30.950 --> 00:01:30.960 align:start position:0%
how we're going to spin up a GitHub code
 

00:01:30.960 --> 00:01:33.230 align:start position:0%
how we're going to spin up a GitHub code
space<00:01:31.960><c> yes</c><00:01:32.159><c> let's</c><00:01:32.439><c> switch</c><00:01:32.680><c> to</c><00:01:32.759><c> the</c><00:01:32.880><c> browser</c>

00:01:33.230 --> 00:01:33.240 align:start position:0%
space yes let's switch to the browser
 

00:01:33.240 --> 00:01:34.429 align:start position:0%
space yes let's switch to the browser
and<00:01:33.360><c> I'll</c><00:01:33.439><c> will</c><00:01:33.600><c> show</c><00:01:33.759><c> you</c><00:01:33.880><c> how</c><00:01:34.000><c> to</c><00:01:34.119><c> create</c><00:01:34.320><c> a</c>

00:01:34.429 --> 00:01:34.439 align:start position:0%
and I'll will show you how to create a
 

00:01:34.439 --> 00:01:36.870 align:start position:0%
and I'll will show you how to create a
new<00:01:34.600><c> instance</c><00:01:34.880><c> of</c><00:01:34.960><c> a</c><00:01:35.079><c> code</c><00:01:35.399><c> space</c><00:01:36.399><c> exactly</c>

00:01:36.870 --> 00:01:36.880 align:start position:0%
new instance of a code space exactly
 

00:01:36.880 --> 00:01:40.149 align:start position:0%
new instance of a code space exactly
yeah<00:01:37.119><c> so</c><00:01:37.399><c> now</c><00:01:37.600><c> let's</c><00:01:37.880><c> make</c><00:01:38.040><c> use</c><00:01:38.240><c> of</c><00:01:38.360><c> it</c><00:01:39.040><c> cool</c><00:01:39.880><c> so</c>

00:01:40.149 --> 00:01:40.159 align:start position:0%
yeah so now let's make use of it cool so
 

00:01:40.159 --> 00:01:44.429 align:start position:0%
yeah so now let's make use of it cool so
instead<00:01:40.439><c> of</c><00:01:41.040><c> going</c><00:01:41.439><c> here</c><00:01:41.680><c> and</c><00:01:42.200><c> clone</c><00:01:43.159><c> local</c><00:01:44.159><c> we</c>

00:01:44.429 --> 00:01:44.439 align:start position:0%
instead of going here and clone local we
 

00:01:44.439 --> 00:01:47.709 align:start position:0%
instead of going here and clone local we
are<00:01:44.719><c> going</c><00:01:44.960><c> to</c><00:01:45.240><c> clone</c><00:01:45.600><c> and</c><00:01:45.719><c> use</c><00:01:46.000><c> a</c><00:01:46.200><c> code</c><00:01:46.719><c> space</c>

00:01:47.709 --> 00:01:47.719 align:start position:0%
are going to clone and use a code space
 

00:01:47.719 --> 00:01:50.469 align:start position:0%
are going to clone and use a code space
so<00:01:48.040><c> you</c><00:01:48.159><c> can</c><00:01:48.399><c> immediately</c><00:01:49.399><c> if</c><00:01:49.479><c> you</c><00:01:49.600><c> want</c><00:01:50.280><c> click</c>

00:01:50.469 --> 00:01:50.479 align:start position:0%
so you can immediately if you want click
 

00:01:50.479 --> 00:01:52.670 align:start position:0%
so you can immediately if you want click
this<00:01:50.640><c> button</c><00:01:50.960><c> create</c><00:01:51.240><c> code</c><00:01:51.479><c> space</c><00:01:51.680><c> on</c><00:01:51.840><c> Main</c><00:01:52.560><c> uh</c>

00:01:52.670 --> 00:01:52.680 align:start position:0%
this button create code space on Main uh
 

00:01:52.680 --> 00:01:56.109 align:start position:0%
this button create code space on Main uh
and<00:01:52.920><c> that</c><00:01:53.040><c> will</c><00:01:53.799><c> actually</c><00:01:54.640><c> do</c><00:01:55.360><c> much</c><00:01:55.560><c> of</c><00:01:55.719><c> the</c>

00:01:56.109 --> 00:01:56.119 align:start position:0%
and that will actually do much of the
 

00:01:56.119 --> 00:01:58.429 align:start position:0%
and that will actually do much of the
things<00:01:56.399><c> for</c><00:01:56.600><c> you</c><00:01:56.880><c> but</c><00:01:57.039><c> it</c><00:01:57.200><c> will</c><00:01:57.520><c> use</c><00:01:57.920><c> like</c><00:01:58.159><c> the</c>

00:01:58.429 --> 00:01:58.439 align:start position:0%
things for you but it will use like the
 

00:01:58.439 --> 00:02:01.310 align:start position:0%
things for you but it will use like the
smallest<00:01:59.399><c> um</c><00:02:00.000><c> F</c><00:02:00.399><c> machine</c><00:02:00.680><c> with</c><00:02:00.840><c> just</c><00:02:00.960><c> the</c><00:02:01.119><c> two</c>

00:02:01.310 --> 00:02:01.320 align:start position:0%
smallest um F machine with just the two
 

00:02:01.320 --> 00:02:04.029 align:start position:0%
smallest um F machine with just the two
cores<00:02:01.640><c> for</c><00:02:01.799><c> you</c><00:02:02.039><c> by</c><00:02:02.479><c> default</c><00:02:03.479><c> uh</c><00:02:03.600><c> so</c><00:02:03.840><c> what</c><00:02:03.960><c> I</c>

00:02:04.029 --> 00:02:04.039 align:start position:0%
cores for you by default uh so what I
 

00:02:04.039 --> 00:02:06.310 align:start position:0%
cores for you by default uh so what I
will<00:02:04.200><c> do</c><00:02:04.719><c> is</c><00:02:04.960><c> I'll</c><00:02:05.479><c> click</c><00:02:05.680><c> on</c><00:02:05.840><c> these</c><00:02:06.119><c> three</c>

00:02:06.310 --> 00:02:06.320 align:start position:0%
will do is I'll click on these three
 

00:02:06.320 --> 00:02:08.910 align:start position:0%
will do is I'll click on these three
dots<00:02:06.640><c> here</c><00:02:06.840><c> and</c><00:02:07.119><c> choose</c><00:02:07.520><c> new</c><00:02:07.960><c> with</c><00:02:08.200><c> options</c>

00:02:08.910 --> 00:02:08.920 align:start position:0%
dots here and choose new with options
 

00:02:08.920 --> 00:02:10.469 align:start position:0%
dots here and choose new with options
because<00:02:09.200><c> here</c><00:02:09.319><c> we</c><00:02:09.399><c> can</c><00:02:09.560><c> actually</c><00:02:09.840><c> specify</c><00:02:10.280><c> the</c>

00:02:10.469 --> 00:02:10.479 align:start position:0%
because here we can actually specify the
 

00:02:10.479 --> 00:02:13.550 align:start position:0%
because here we can actually specify the
size<00:02:10.679><c> of</c><00:02:10.800><c> the</c>

00:02:13.550 --> 00:02:13.560 align:start position:0%
 
 

00:02:13.560 --> 00:02:15.589 align:start position:0%
 
machine<00:02:14.560><c> so</c><00:02:14.760><c> we'll</c><00:02:14.920><c> be</c><00:02:15.040><c> working</c><00:02:15.239><c> on</c><00:02:15.319><c> the</c><00:02:15.400><c> main</c>

00:02:15.589 --> 00:02:15.599 align:start position:0%
machine so we'll be working on the main
 

00:02:15.599 --> 00:02:17.470 align:start position:0%
machine so we'll be working on the main
branch<00:02:15.959><c> that's</c><00:02:16.120><c> totally</c><00:02:16.480><c> fine</c><00:02:17.040><c> there's</c><00:02:17.239><c> only</c>

00:02:17.470 --> 00:02:17.480 align:start position:0%
branch that's totally fine there's only
 

00:02:17.480 --> 00:02:19.190 align:start position:0%
branch that's totally fine there's only
one<00:02:17.680><c> def</c><00:02:17.879><c> container</c><00:02:18.239><c> configuration</c><00:02:18.879><c> thater</c>

00:02:19.190 --> 00:02:19.200 align:start position:0%
one def container configuration thater
 

00:02:19.200 --> 00:02:20.949 align:start position:0%
one def container configuration thater
workflow<00:02:19.560><c> demo</c><00:02:20.080><c> but</c><00:02:20.200><c> it's</c><00:02:20.360><c> nice</c><00:02:20.560><c> to</c><00:02:20.640><c> know</c><00:02:20.840><c> that</c>

00:02:20.949 --> 00:02:20.959 align:start position:0%
workflow demo but it's nice to know that
 

00:02:20.959 --> 00:02:22.550 align:start position:0%
workflow demo but it's nice to know that
if<00:02:21.080><c> you</c><00:02:21.200><c> have</c><00:02:21.360><c> more</c><00:02:21.599><c> you</c><00:02:21.680><c> can</c><00:02:21.840><c> actually</c><00:02:22.120><c> choose</c>

00:02:22.550 --> 00:02:22.560 align:start position:0%
if you have more you can actually choose
 

00:02:22.560 --> 00:02:24.270 align:start position:0%
if you have more you can actually choose
which<00:02:23.120><c> def</c><00:02:23.360><c> container</c><00:02:23.720><c> configuration</c><00:02:24.200><c> you</c>

00:02:24.270 --> 00:02:24.280 align:start position:0%
which def container configuration you
 

00:02:24.280 --> 00:02:27.190 align:start position:0%
which def container configuration you
want<00:02:24.400><c> to</c><00:02:24.519><c> use</c><00:02:25.040><c> so</c><00:02:25.239><c> that's</c><00:02:25.720><c> good</c><00:02:26.720><c> the</c><00:02:26.840><c> region</c>

00:02:27.190 --> 00:02:27.200 align:start position:0%
want to use so that's good the region
 

00:02:27.200 --> 00:02:28.949 align:start position:0%
want to use so that's good the region
your<00:02:27.519><c> best</c><00:02:27.720><c> is</c><00:02:27.840><c> closest</c><00:02:28.120><c> to</c><00:02:28.239><c> me</c><00:02:28.400><c> so</c><00:02:28.560><c> that</c><00:02:28.720><c> makes</c>

00:02:28.949 --> 00:02:28.959 align:start position:0%
your best is closest to me so that makes
 

00:02:28.959 --> 00:02:30.630 align:start position:0%
your best is closest to me so that makes
sense<00:02:29.400><c> and</c><00:02:29.599><c> this</c><00:02:30.000><c> the</c><00:02:30.120><c> thing</c><00:02:30.280><c> I</c><00:02:30.360><c> want</c><00:02:30.480><c> to</c>

00:02:30.630 --> 00:02:30.640 align:start position:0%
sense and this the thing I want to
 

00:02:30.640 --> 00:02:32.869 align:start position:0%
sense and this the thing I want to
change<00:02:31.120><c> I</c><00:02:31.200><c> want</c><00:02:31.360><c> to</c><00:02:31.519><c> change</c><00:02:31.840><c> from</c><00:02:32.200><c> two</c><00:02:32.480><c> core</c><00:02:32.720><c> to</c>

00:02:32.869 --> 00:02:32.879 align:start position:0%
change I want to change from two core to
 

00:02:32.879 --> 00:02:35.550 align:start position:0%
change I want to change from two core to
four<00:02:33.480><c> core</c><00:02:34.480><c> that</c><00:02:34.599><c> will</c><00:02:34.720><c> be</c><00:02:35.040><c> sufficient</c><00:02:35.400><c> for</c>

00:02:35.550 --> 00:02:35.560 align:start position:0%
four core that will be sufficient for
 

00:02:35.560 --> 00:02:37.949 align:start position:0%
four core that will be sufficient for
this<00:02:35.720><c> demo</c><00:02:36.640><c> and</c><00:02:36.760><c> then</c><00:02:36.879><c> I</c><00:02:37.040><c> hit</c><00:02:37.319><c> create</c><00:02:37.640><c> create</c>

00:02:37.949 --> 00:02:37.959 align:start position:0%
this demo and then I hit create create
 

00:02:37.959 --> 00:02:42.070 align:start position:0%
this demo and then I hit create create
Co

00:02:42.070 --> 00:02:42.080 align:start position:0%
 
 

00:02:42.080 --> 00:02:44.550 align:start position:0%
 
space<00:02:43.080><c> so</c><00:02:43.239><c> now</c><00:02:43.360><c> we</c><00:02:43.480><c> have</c><00:02:43.560><c> to</c><00:02:43.720><c> wait</c><00:02:44.159><c> a</c><00:02:44.280><c> couple</c><00:02:44.480><c> of</c>

00:02:44.550 --> 00:02:44.560 align:start position:0%
space so now we have to wait a couple of
 

00:02:44.560 --> 00:02:52.350 align:start position:0%
space so now we have to wait a couple of
minutes<00:02:44.879><c> before</c><00:02:45.159><c> it's</c>

00:02:52.350 --> 00:02:52.360 align:start position:0%
 
 

00:02:52.360 --> 00:02:54.589 align:start position:0%
 
done<00:02:53.360><c> okay</c><00:02:53.560><c> now</c><00:02:53.680><c> our</c><00:02:53.920><c> Dev</c><00:02:54.080><c> container</c><00:02:54.440><c> is</c>

00:02:54.589 --> 00:02:54.599 align:start position:0%
done okay now our Dev container is
 

00:02:54.599 --> 00:02:57.149 align:start position:0%
done okay now our Dev container is
completely<00:02:55.000><c> done</c><00:02:55.680><c> and</c><00:02:55.959><c> we</c><00:02:56.080><c> can</c><00:02:56.280><c> start</c><00:02:56.599><c> our</c>

00:02:57.149 --> 00:02:57.159 align:start position:0%
completely done and we can start our
 

00:02:57.159 --> 00:02:58.790 align:start position:0%
completely done and we can start our
demo<00:02:57.480><c> so</c><00:02:57.599><c> let's</c><00:02:57.800><c> start</c><00:02:58.000><c> with</c><00:02:58.159><c> the</c><00:02:58.519><c> basic</c>

00:02:58.790 --> 00:02:58.800 align:start position:0%
demo so let's start with the basic
 

00:02:58.800 --> 00:03:00.550 align:start position:0%
demo so let's start with the basic
workflow<00:02:59.200><c> samples</c><00:02:59.599><c> so</c><00:02:59.840><c> so</c><00:02:59.959><c> let's</c><00:03:00.120><c> move</c><00:03:00.319><c> into</c>

00:03:00.550 --> 00:03:00.560 align:start position:0%
workflow samples so so let's move into
 

00:03:00.560 --> 00:03:01.790 align:start position:0%
workflow samples so so let's move into
that

00:03:01.790 --> 00:03:01.800 align:start position:0%
that
 

00:03:01.800 --> 00:03:04.710 align:start position:0%
that
folder<00:03:02.800><c> and</c><00:03:02.920><c> we'll</c><00:03:03.159><c> use</c><00:03:03.400><c> the</c><00:03:03.560><c> deer</c><00:03:03.920><c> CLI</c><00:03:04.599><c> with</c>

00:03:04.710 --> 00:03:04.720 align:start position:0%
folder and we'll use the deer CLI with
 

00:03:04.720 --> 00:03:07.110 align:start position:0%
folder and we'll use the deer CLI with
the<00:03:04.920><c> multi</c><00:03:05.280><c> app</c><00:03:05.440><c> run</c><00:03:05.720><c> configuration</c><00:03:06.280><c> to</c><00:03:07.000><c> uh</c>

00:03:07.110 --> 00:03:07.120 align:start position:0%
the multi app run configuration to uh
 

00:03:07.120 --> 00:03:17.789 align:start position:0%
the multi app run configuration to uh
start<00:03:07.680><c> the</c><00:03:08.200><c> the</c>

00:03:17.789 --> 00:03:17.799 align:start position:0%
 
 

00:03:17.799 --> 00:03:20.630 align:start position:0%
 
demo<00:03:18.799><c> okay</c><00:03:18.920><c> so</c><00:03:19.080><c> the</c><00:03:19.239><c> application</c><00:03:19.599><c> is</c><00:03:19.760><c> running</c>

00:03:20.630 --> 00:03:20.640 align:start position:0%
demo okay so the application is running
 

00:03:20.640 --> 00:03:23.390 align:start position:0%
demo okay so the application is running
um<00:03:21.360><c> we</c><00:03:21.480><c> see</c><00:03:21.680><c> that</c><00:03:21.920><c> some</c><00:03:22.080><c> ports</c><00:03:22.400><c> are</c><00:03:22.560><c> available</c>

00:03:23.390 --> 00:03:23.400 align:start position:0%
um we see that some ports are available
 

00:03:23.400 --> 00:03:25.149 align:start position:0%
um we see that some ports are available
we<00:03:23.519><c> won't</c><00:03:23.840><c> open</c><00:03:24.040><c> it</c><00:03:24.159><c> in</c><00:03:24.239><c> the</c><00:03:24.360><c> browser</c><00:03:24.799><c> because</c>

00:03:25.149 --> 00:03:25.159 align:start position:0%
we won't open it in the browser because
 

00:03:25.159 --> 00:03:27.350 align:start position:0%
we won't open it in the browser because
there<00:03:25.280><c> is</c><00:03:25.440><c> no</c><00:03:25.560><c> front</c><00:03:25.840><c> end</c><00:03:26.560><c> um</c><00:03:26.680><c> but</c><00:03:26.799><c> it</c><00:03:26.920><c> means</c><00:03:27.200><c> we</c>

00:03:27.350 --> 00:03:27.360 align:start position:0%
there is no front end um but it means we
 

00:03:27.360 --> 00:03:30.429 align:start position:0%
there is no front end um but it means we
can<00:03:27.640><c> access</c><00:03:28.519><c> um</c><00:03:28.799><c> those</c><00:03:29.000><c> end</c><00:03:29.280><c> points</c><00:03:29.480><c> and</c>

00:03:30.429 --> 00:03:30.439 align:start position:0%
can access um those end points and
 

00:03:30.439 --> 00:03:32.910 align:start position:0%
can access um those end points and
start<00:03:30.879><c> a</c><00:03:31.120><c> workflow</c><00:03:31.560><c> and</c><00:03:31.680><c> also</c><00:03:31.959><c> get</c><00:03:32.200><c> the</c>

00:03:32.910 --> 00:03:32.920 align:start position:0%
start a workflow and also get the
 

00:03:32.920 --> 00:03:35.229 align:start position:0%
start a workflow and also get the
results<00:03:33.280><c> from</c><00:03:33.400><c> the</c><00:03:33.519><c> workflow</c><00:03:34.519><c> so</c><00:03:34.760><c> let's</c><00:03:35.000><c> do</c>

00:03:35.229 --> 00:03:35.239 align:start position:0%
results from the workflow so let's do
 

00:03:35.239 --> 00:03:37.789 align:start position:0%
results from the workflow so let's do
that<00:03:35.640><c> by</c><00:03:36.640><c> opening</c><00:03:37.000><c> this</c><00:03:37.159><c> basic</c><00:03:37.400><c> workflow</c>

00:03:37.789 --> 00:03:37.799 align:start position:0%
that by opening this basic workflow
 

00:03:37.799 --> 00:03:40.350 align:start position:0%
that by opening this basic workflow
samples<00:03:38.280><c> and</c><00:03:38.480><c> go</c><00:03:38.640><c> to</c><00:03:38.799><c> this</c><00:03:38.959><c> HTTP</c><00:03:39.599><c> file</c><00:03:40.120><c> because</c>

00:03:40.350 --> 00:03:40.360 align:start position:0%
samples and go to this HTTP file because
 

00:03:40.360 --> 00:03:42.429 align:start position:0%
samples and go to this HTTP file because
one<00:03:40.480><c> of</c><00:03:40.640><c> the</c><00:03:40.920><c> things</c><00:03:41.200><c> that</c><00:03:41.400><c> we</c><00:03:41.799><c> enabled</c><00:03:42.319><c> in</c>

00:03:42.429 --> 00:03:42.439 align:start position:0%
one of the things that we enabled in
 

00:03:42.439 --> 00:03:45.149 align:start position:0%
one of the things that we enabled in
this<00:03:42.640><c> Dev</c><00:03:42.879><c> container</c><00:03:43.400><c> in</c><00:03:43.519><c> this</c><00:03:43.920><c> code</c><00:03:44.200><c> space</c><00:03:45.040><c> uh</c>

00:03:45.149 --> 00:03:45.159 align:start position:0%
this Dev container in this code space uh
 

00:03:45.159 --> 00:03:47.350 align:start position:0%
this Dev container in this code space uh
was<00:03:45.319><c> the</c><00:03:45.480><c> rest</c><00:03:45.840><c> client</c><00:03:46.560><c> so</c><00:03:46.840><c> we</c><00:03:46.959><c> can</c><00:03:47.120><c> actually</c>

00:03:47.350 --> 00:03:47.360 align:start position:0%
was the rest client so we can actually
 

00:03:47.360 --> 00:03:50.710 align:start position:0%
was the rest client so we can actually
use<00:03:47.760><c> this</c><00:03:48.640><c> HTP</c><00:03:49.120><c> file</c><00:03:49.599><c> which</c><00:03:49.799><c> contains</c><00:03:50.239><c> our</c>

00:03:50.710 --> 00:03:50.720 align:start position:0%
use this HTP file which contains our
 

00:03:50.720 --> 00:03:53.789 align:start position:0%
use this HTP file which contains our
endpoints<00:03:51.560><c> to</c><00:03:51.920><c> start</c><00:03:52.159><c> our</c><00:03:52.519><c> workflow</c><00:03:53.519><c> because</c>

00:03:53.789 --> 00:03:53.799 align:start position:0%
endpoints to start our workflow because
 

00:03:53.799 --> 00:03:56.229 align:start position:0%
endpoints to start our workflow because
there's<00:03:53.959><c> this</c><00:03:54.439><c> little</c><00:03:54.959><c> text</c><00:03:55.400><c> button</c><00:03:55.760><c> here</c>

00:03:56.229 --> 00:03:56.239 align:start position:0%
there's this little text button here
 

00:03:56.239 --> 00:03:58.710 align:start position:0%
there's this little text button here
sent<00:03:56.640><c> request</c><00:03:57.120><c> I'll</c><00:03:57.280><c> make</c><00:03:57.400><c> it</c><00:03:57.480><c> a</c><00:03:57.560><c> bit</c>

00:03:58.710 --> 00:03:58.720 align:start position:0%
sent request I'll make it a bit
 

00:03:58.720 --> 00:04:00.390 align:start position:0%
sent request I'll make it a bit
bigger

00:04:00.390 --> 00:04:00.400 align:start position:0%
bigger
 

00:04:00.400 --> 00:04:03.190 align:start position:0%
bigger
so<00:04:00.680><c> this</c><00:04:00.799><c> will</c><00:04:01.239><c> uh</c><00:04:01.400><c> start</c><00:04:01.840><c> the</c><00:04:02.079><c> workflow</c><00:04:02.840><c> named</c>

00:04:03.190 --> 00:04:03.200 align:start position:0%
so this will uh start the workflow named
 

00:04:03.200 --> 00:04:07.710 align:start position:0%
so this will uh start the workflow named
hello<00:04:03.480><c> world</c><00:04:04.040><c> workflow</c><00:04:05.040><c> um</c><00:04:05.200><c> so</c><00:04:05.360><c> let's</c><00:04:05.560><c> start</c>

00:04:07.710 --> 00:04:07.720 align:start position:0%
hello world workflow um so let's start
 

00:04:07.720 --> 00:04:10.670 align:start position:0%
hello world workflow um so let's start
this<00:04:08.720><c> all</c><00:04:08.840><c> right</c><00:04:09.000><c> we</c><00:04:09.120><c> get</c><00:04:09.280><c> backy</c><00:04:09.599><c> 202</c><00:04:10.040><c> accepted</c>

00:04:10.670 --> 00:04:10.680 align:start position:0%
this all right we get backy 202 accepted
 

00:04:10.680 --> 00:04:12.670 align:start position:0%
this all right we get backy 202 accepted
with<00:04:10.879><c> the</c><00:04:11.120><c> instance</c><00:04:11.560><c> ID</c><00:04:11.840><c> of</c><00:04:11.920><c> the</c><00:04:12.040><c> workflow</c><00:04:12.519><c> so</c>

00:04:12.670 --> 00:04:12.680 align:start position:0%
with the instance ID of the workflow so
 

00:04:12.680 --> 00:04:15.309 align:start position:0%
with the instance ID of the workflow so
that<00:04:12.840><c> all</c><00:04:13.159><c> seems</c><00:04:13.439><c> to</c><00:04:13.599><c> be</c><00:04:14.079><c> working</c><00:04:14.519><c> okay</c><00:04:15.120><c> and</c><00:04:15.239><c> if</c>

00:04:15.309 --> 00:04:15.319 align:start position:0%
that all seems to be working okay and if
 

00:04:15.319 --> 00:04:17.430 align:start position:0%
that all seems to be working okay and if
we<00:04:15.480><c> scroll</c><00:04:15.920><c> down</c><00:04:16.079><c> a</c><00:04:16.239><c> bit</c><00:04:16.759><c> if</c><00:04:16.880><c> you</c><00:04:17.000><c> give</c><00:04:17.160><c> this</c><00:04:17.320><c> a</c>

00:04:17.430 --> 00:04:17.440 align:start position:0%
we scroll down a bit if you give this a
 

00:04:17.440 --> 00:04:20.789 align:start position:0%
we scroll down a bit if you give this a
bit<00:04:17.600><c> more</c><00:04:18.280><c> space</c><00:04:19.280><c> then</c><00:04:19.440><c> we</c><00:04:19.560><c> can</c><00:04:20.120><c> actually</c><00:04:20.560><c> get</c>

00:04:20.789 --> 00:04:20.799 align:start position:0%
bit more space then we can actually get
 

00:04:20.799 --> 00:04:23.590 align:start position:0%
bit more space then we can actually get
the<00:04:20.959><c> result</c><00:04:21.320><c> from</c><00:04:21.479><c> this</c><00:04:21.600><c> workflow</c><00:04:22.160><c> by</c><00:04:23.160><c> calling</c>

00:04:23.590 --> 00:04:23.600 align:start position:0%
the result from this workflow by calling
 

00:04:23.600 --> 00:04:27.350 align:start position:0%
the result from this workflow by calling
this<00:04:24.560><c> um</c><00:04:24.800><c> workflow</c><00:04:25.280><c> Deber</c><00:04:26.199><c> endpoint</c><00:04:27.199><c> with</c>

00:04:27.350 --> 00:04:27.360 align:start position:0%
this um workflow Deber endpoint with
 

00:04:27.360 --> 00:04:30.590 align:start position:0%
this um workflow Deber endpoint with
ends<00:04:27.720><c> with</c><00:04:27.919><c> the</c><00:04:28.639><c> ID</c><00:04:29.520><c> that</c><00:04:29.800><c> we</c><00:04:29.880><c> are</c><00:04:30.000><c> seeing</c><00:04:30.320><c> here</c>

00:04:30.590 --> 00:04:30.600 align:start position:0%
ends with the ID that we are seeing here
 

00:04:30.600 --> 00:04:32.189 align:start position:0%
ends with the ID that we are seeing here
so<00:04:30.759><c> let's</c><00:04:30.919><c> do</c><00:04:31.080><c> this</c><00:04:31.280><c> get</c>

00:04:32.189 --> 00:04:32.199 align:start position:0%
so let's do this get
 

00:04:32.199 --> 00:04:36.110 align:start position:0%
so let's do this get
request<00:04:33.199><c> and</c><00:04:33.320><c> we</c><00:04:33.440><c> get</c><00:04:33.600><c> back</c><00:04:33.759><c> at</c><00:04:33.880><c> to</c><00:04:34.360><c> 200</c><00:04:35.120><c> okay</c>

00:04:36.110 --> 00:04:36.120 align:start position:0%
request and we get back at to 200 okay
 

00:04:36.120 --> 00:04:37.909 align:start position:0%
request and we get back at to 200 okay
so<00:04:36.360><c> without</c><00:04:36.600><c> installing</c><00:04:37.000><c> anything</c><00:04:37.520><c> the</c><00:04:37.680><c> user</c>

00:04:37.909 --> 00:04:37.919 align:start position:0%
so without installing anything the user
 

00:04:37.919 --> 00:04:39.390 align:start position:0%
so without installing anything the user
is<00:04:38.080><c> capable</c><00:04:38.360><c> of</c><00:04:38.479><c> just</c><00:04:38.600><c> running</c><00:04:38.840><c> this</c><00:04:39.000><c> deer</c>

00:04:39.390 --> 00:04:39.400 align:start position:0%
is capable of just running this deer
 

00:04:39.400 --> 00:04:41.990 align:start position:0%
is capable of just running this deer
demo<00:04:40.360><c> uh</c><00:04:40.520><c> and</c><00:04:40.800><c> quickly</c><00:04:41.280><c> start</c><00:04:41.600><c> a</c><00:04:41.720><c> depper</c>

00:04:41.990 --> 00:04:42.000 align:start position:0%
demo uh and quickly start a depper
 

00:04:42.000 --> 00:04:43.310 align:start position:0%
demo uh and quickly start a depper
workflow<00:04:42.360><c> and</c><00:04:42.520><c> get</c><00:04:42.639><c> the</c>

00:04:43.310 --> 00:04:43.320 align:start position:0%
workflow and get the
 

00:04:43.320 --> 00:04:45.790 align:start position:0%
workflow and get the
results<00:04:44.320><c> Okay</c><00:04:44.440><c> so</c><00:04:44.639><c> we've</c><00:04:44.880><c> now</c><00:04:45.000><c> seen</c><00:04:45.360><c> that</c><00:04:45.600><c> a</c>

00:04:45.790 --> 00:04:45.800 align:start position:0%
results Okay so we've now seen that a
 

00:04:45.800 --> 00:04:47.550 align:start position:0%
results Okay so we've now seen that a
user<00:04:46.080><c> can</c><00:04:46.320><c> get</c><00:04:46.479><c> up</c><00:04:46.639><c> and</c><00:04:46.759><c> running</c><00:04:47.080><c> very</c><00:04:47.280><c> quickly</c>

00:04:47.550 --> 00:04:47.560 align:start position:0%
user can get up and running very quickly
 

00:04:47.560 --> 00:04:49.150 align:start position:0%
user can get up and running very quickly
with<00:04:47.720><c> Dev</c><00:04:47.960><c> containers</c><00:04:48.520><c> because</c><00:04:48.759><c> there's</c><00:04:49.000><c> no</c>

00:04:49.150 --> 00:04:49.160 align:start position:0%
with Dev containers because there's no
 

00:04:49.160 --> 00:04:51.110 align:start position:0%
with Dev containers because there's no
need<00:04:49.360><c> to</c><00:04:49.520><c> install</c><00:04:49.960><c> anything</c><00:04:50.560><c> we</c><00:04:50.720><c> have</c><00:04:50.919><c> just</c>

00:04:51.110 --> 00:04:51.120 align:start position:0%
need to install anything we have just
 

00:04:51.120 --> 00:04:54.550 align:start position:0%
need to install anything we have just
run<00:04:51.440><c> this</c><00:04:51.600><c> deer</c><00:04:52.080><c> demo</c><00:04:53.080><c> by</c><00:04:53.280><c> just</c><00:04:53.800><c> starting</c><00:04:54.240><c> deer</c>

00:04:54.550 --> 00:04:54.560 align:start position:0%
run this deer demo by just starting deer
 

00:04:54.560 --> 00:04:57.469 align:start position:0%
run this deer demo by just starting deer
command<00:04:55.440><c> um</c><00:04:55.600><c> and</c><00:04:55.680><c> we've</c><00:04:55.840><c> seen</c><00:04:56.039><c> the</c><00:04:56.479><c> results</c>

00:04:57.469 --> 00:04:57.479 align:start position:0%
command um and we've seen the results
 

00:04:57.479 --> 00:04:59.230 align:start position:0%
command um and we've seen the results
absolutely<00:04:58.039><c> and</c><00:04:58.160><c> I</c><00:04:58.320><c> love</c><00:04:58.520><c> this</c><00:04:58.680><c> Mark</c><00:04:59.080><c> because</c>

00:04:59.230 --> 00:04:59.240 align:start position:0%
absolutely and I love this Mark because
 

00:04:59.240 --> 00:05:00.830 align:start position:0%
absolutely and I love this Mark because
we<00:04:59.360><c> can</c><00:04:59.800><c> get</c><00:04:59.919><c> started</c><00:05:00.160><c> working</c><00:05:00.440><c> not</c><00:05:00.600><c> only</c><00:05:00.759><c> in</c>

00:05:00.830 --> 00:05:00.840 align:start position:0%
we can get started working not only in
 

00:05:00.840 --> 00:05:03.430 align:start position:0%
we can get started working not only in
the<00:05:00.960><c> Dapper</c><00:05:01.400><c> project</c><00:05:01.840><c> but</c><00:05:02.240><c> any</c><00:05:02.639><c> project</c><00:05:03.320><c> with</c>

00:05:03.430 --> 00:05:03.440 align:start position:0%
the Dapper project but any project with
 

00:05:03.440 --> 00:05:05.070 align:start position:0%
the Dapper project but any project with
a<00:05:03.600><c> Dev</c><00:05:03.800><c> container</c><00:05:04.199><c> config</c><00:05:04.600><c> so</c><00:05:04.720><c> you've</c><00:05:04.880><c> showed</c>

00:05:05.070 --> 00:05:05.080 align:start position:0%
a Dev container config so you've showed
 

00:05:05.080 --> 00:05:07.150 align:start position:0%
a Dev container config so you've showed
us<00:05:05.160><c> how</c><00:05:05.280><c> to</c><00:05:05.360><c> spin</c><00:05:05.560><c> up</c><00:05:05.680><c> a</c><00:05:05.759><c> GitHub</c><00:05:06.080><c> codes</c><00:05:06.320><c> space</c>

00:05:07.150 --> 00:05:07.160 align:start position:0%
us how to spin up a GitHub codes space
 

00:05:07.160 --> 00:05:09.469 align:start position:0%
us how to spin up a GitHub codes space
in<00:05:07.759><c> GitHub</c><00:05:08.199><c> on</c><00:05:08.360><c> the</c><00:05:08.520><c> on</c><00:05:08.600><c> the</c><00:05:08.720><c> web</c><00:05:08.919><c> UI</c><00:05:09.280><c> but</c><00:05:09.400><c> we</c>

00:05:09.469 --> 00:05:09.479 align:start position:0%
in GitHub on the on the web UI but we
 

00:05:09.479 --> 00:05:11.830 align:start position:0%
in GitHub on the on the web UI but we
can<00:05:09.680><c> also</c><00:05:09.919><c> spin</c><00:05:10.199><c> up</c><00:05:10.440><c> that</c><00:05:11.039><c> uh</c><00:05:11.199><c> Dev</c><00:05:11.440><c> container</c>

00:05:11.830 --> 00:05:11.840 align:start position:0%
can also spin up that uh Dev container
 

00:05:11.840 --> 00:05:14.230 align:start position:0%
can also spin up that uh Dev container
locally<00:05:12.400><c> in</c><00:05:12.600><c> vs</c><00:05:13.039><c> code</c><00:05:13.479><c> in</c><00:05:13.600><c> that</c><00:05:13.720><c> containerized</c>

00:05:14.230 --> 00:05:14.240 align:start position:0%
locally in vs code in that containerized
 

00:05:14.240 --> 00:05:16.550 align:start position:0%
locally in vs code in that containerized
environment<00:05:14.600><c> so</c><00:05:14.759><c> I</c><00:05:14.880><c> love</c><00:05:15.160><c> that</c><00:05:15.919><c> thank</c><00:05:16.120><c> you</c><00:05:16.360><c> so</c>

00:05:16.550 --> 00:05:16.560 align:start position:0%
environment so I love that thank you so
 

00:05:16.560 --> 00:05:18.150 align:start position:0%
environment so I love that thank you so
much<00:05:16.840><c> Mark</c><00:05:17.120><c> for</c><00:05:17.240><c> showing</c><00:05:17.520><c> us</c><00:05:17.639><c> how</c><00:05:17.720><c> to</c><00:05:17.840><c> do</c><00:05:18.000><c> this</c>

00:05:18.150 --> 00:05:18.160 align:start position:0%
much Mark for showing us how to do this
 

00:05:18.160 --> 00:05:20.189 align:start position:0%
much Mark for showing us how to do this
today<00:05:18.800><c> this</c><00:05:18.919><c> is</c><00:05:19.199><c> awesome</c><00:05:19.680><c> and</c><00:05:19.840><c> hopefully</c>

00:05:20.189 --> 00:05:20.199 align:start position:0%
today this is awesome and hopefully
 

00:05:20.199 --> 00:05:22.150 align:start position:0%
today this is awesome and hopefully
everyone<00:05:20.520><c> can</c><00:05:20.680><c> now</c><00:05:20.880><c> get</c><00:05:21.039><c> started</c><00:05:21.520><c> with</c><00:05:21.800><c> GitHub</c>

00:05:22.150 --> 00:05:22.160 align:start position:0%
everyone can now get started with GitHub
 

00:05:22.160 --> 00:05:24.749 align:start position:0%
everyone can now get started with GitHub
code<00:05:22.400><c> spaces</c><00:05:23.080><c> super</c><00:05:23.319><c> easily</c><00:05:24.080><c> so</c><00:05:24.280><c> see</c><00:05:24.440><c> you</c><00:05:24.560><c> all</c>

00:05:24.749 --> 00:05:24.759 align:start position:0%
code spaces super easily so see you all
 

00:05:24.759 --> 00:05:26.710 align:start position:0%
code spaces super easily so see you all
next<00:05:24.960><c> time</c><00:05:25.120><c> on</c><00:05:25.360><c> 15</c><00:05:25.680><c> minutes</c><00:05:25.919><c> to</c><00:05:26.080><c> merge</c><00:05:26.520><c> and</c>

00:05:26.710 --> 00:05:26.720 align:start position:0%
next time on 15 minutes to merge and
 

00:05:26.720 --> 00:05:28.309 align:start position:0%
next time on 15 minutes to merge and
thank<00:05:26.919><c> you</c><00:05:27.080><c> so</c><00:05:27.240><c> much</c><00:05:27.440><c> Mark</c><00:05:27.720><c> for</c><00:05:27.880><c> joining</c><00:05:28.160><c> us</c>

00:05:28.309 --> 00:05:28.319 align:start position:0%
thank you so much Mark for joining us
 

00:05:28.319 --> 00:05:30.230 align:start position:0%
thank you so much Mark for joining us
today<00:05:29.199><c> my</c><00:05:29.319><c> pleasure</c>

00:05:30.230 --> 00:05:30.240 align:start position:0%
today my pleasure
 

00:05:30.240 --> 00:05:31.710 align:start position:0%
today my pleasure
bye

00:05:31.710 --> 00:05:31.720 align:start position:0%
bye
 

00:05:31.720 --> 00:05:34.720 align:start position:0%
bye
bye

