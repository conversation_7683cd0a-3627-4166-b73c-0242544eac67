#!/usr/bin/env python3
"""
Unified YouTube Summaries Python Service
FastAPI server to handle all AI operations and data processing for the NextJS application
Combines chat, embeddings, and all other Python functionality in one service
"""

import os
import sys
import asyncio
from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import uvicorn
import logging

# Add the main workspace directory to sys.path to import gemini_methods
main_workspace = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(main_workspace)

try:
    from gemini_methods.genai_utils import (
        generate_embedding,
        get_available_embedding_models,
        get_default_embedding_model,
        DEFAULT_MODEL_NAME,
        DEFAULT_GENERATION_CONFIG,
        DEFAULT_SAFETY_SETTINGS
    )
    GEMINI_AVAILABLE = True
    print("✅ Successfully imported gemini_methods!")
except ImportError as e:
    print(f"❌ Error importing gemini_methods: {e}")
    GEMINI_AVAILABLE = False
    print("Running in fallback mode without Gemini integration")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="YouTube Summaries Unified Service",
    description="Unified Python service for YouTube Summaries app - handles chat, embeddings, and data processing",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:9696", "http://127.0.0.1:9696"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer(auto_error=False)

# Pydantic models
class ChatRequest(BaseModel):
    message: str
    max_tokens: Optional[int] = 1000
    temperature: Optional[float] = 0.7

class ContextualChatRequest(BaseModel):
    message: str
    selectedTopics: Optional[List[str]] = []
    selectedChannels: Optional[List[str]] = []
    videoContext: Optional[List[Dict[str, Any]]] = []
    max_tokens: Optional[int] = 1000
    temperature: Optional[float] = 0.7

class ChatResponse(BaseModel):
    response: str
    context_videos: List[Dict[str, Any]] = []

class EmbeddingRequest(BaseModel):
    text: str
    model: Optional[str] = None

class EmbeddingResponse(BaseModel):
    embedding: List[float]
    model: str
    dimensions: int

class VideoSearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 10
    topics: Optional[List[str]] = []
    channels: Optional[List[str]] = []

class HealthResponse(BaseModel):
    status: str
    gemini_available: bool
    version: str

# Authentication middleware
async def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify API key for production use"""
    if not credentials:
        # For development, allow requests without API key
        return True
    
    expected_key = os.getenv("PYTHON_AI_SERVICE_API_KEY", "dev-key-123")
    if credentials.credentials != expected_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )
    return True

# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        gemini_available=GEMINI_AVAILABLE,
        version="1.0.0"
    )

# Chat endpoint - handles both legacy and contextual formats
@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(
    request: ChatRequest | ContextualChatRequest,
    _: bool = Depends(verify_api_key)
):
    """
    Handle chat requests with optional context
    Supports both legacy chat and contextual chat formats
    """
    try:
        message = request.message
        
        # Check if this is a contextual chat request
        if hasattr(request, 'selectedTopics') and hasattr(request, 'selectedChannels'):
            # Contextual chat request
            contextual_request = request
            context_info = []
            
            if contextual_request.videoContext:
                context_info.append(f"Based on {len(contextual_request.videoContext)} videos")
            
            if contextual_request.selectedTopics:
                context_info.append(f"Topics: {', '.join(contextual_request.selectedTopics)}")
                
            if contextual_request.selectedChannels:
                context_info.append(f"Channels: {', '.join(contextual_request.selectedChannels)}")
            
            context_str = "; ".join(context_info) if context_info else "No specific context"
            
            if GEMINI_AVAILABLE:
                # TODO: Implement actual Gemini chat with context
                response_text = f"[Contextual Response - {context_str}] This is a placeholder response to your message: {message}. Gemini integration will be implemented based on your existing gemini_methods."
            else:
                response_text = f"[Contextual Response - {context_str}] This is a placeholder response to your message: {message}. Text generation will be implemented in a future update."
        else:
            # Legacy chat request
            if GEMINI_AVAILABLE:
                # TODO: Implement actual Gemini chat
                response_text = f"This is a placeholder response to your message: {message}. Gemini integration will be implemented based on your existing gemini_methods."
            else:
                response_text = f"This is a placeholder response to your message: {message}. Text generation will be implemented in a future update."
        
        return ChatResponse(
            response=response_text,
            context_videos=[]
        )
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Chat processing failed: {str(e)}")

# Embeddings endpoint
@app.post("/embeddings", response_model=EmbeddingResponse)
async def create_embedding(
    request: EmbeddingRequest,
    _: bool = Depends(verify_api_key)
):
    """Generate embeddings for text using Gemini"""
    try:
        if not GEMINI_AVAILABLE:
            raise HTTPException(
                status_code=503,
                detail="Gemini services not available"
            )
        
        model = request.model or get_default_embedding_model()
        embedding = generate_embedding(request.text, model=model)
        
        return EmbeddingResponse(
            embedding=embedding,
            model=model,
            dimensions=len(embedding)
        )
        
    except Exception as e:
        logger.error(f"Error generating embedding: {e}")
        raise HTTPException(status_code=500, detail=f"Embedding generation failed: {str(e)}")

# Vector search endpoint
@app.post("/search")
async def search_videos(
    request: VideoSearchRequest,
    _: bool = Depends(verify_api_key)
):
    """Search videos using vector similarity"""
    try:
        # TODO: Implement actual vector search using your existing database
        # This would integrate with your Supabase database and existing search logic
        
        return {
            "videos": [],
            "query": request.query,
            "total_results": 0,
            "message": "Vector search will be implemented using your existing database infrastructure"
        }
        
    except Exception as e:
        logger.error(f"Error in video search: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

# Models endpoint
@app.get("/models")
async def get_models(_: bool = Depends(verify_api_key)):
    """Get available AI models"""
    try:
        if not GEMINI_AVAILABLE:
            return {"models": [], "available": False}
        
        embedding_models = get_available_embedding_models()
        
        return {
            "embedding_models": embedding_models,
            "default_embedding_model": get_default_embedding_model(),
            "available": True
        }
        
    except Exception as e:
        logger.error(f"Error getting models: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get models: {str(e)}")

if __name__ == "__main__":
    port = int(os.getenv("PYTHON_SERVICE_PORT", "9000"))
    
    print(f"Starting Unified YouTube Summaries Python Service on 127.0.0.1:{port}")
    print(f"Gemini available: {GEMINI_AVAILABLE}")
    print("This service now handles all Python operations:")
    print("  - Chat (legacy and contextual)")
    print("  - Embeddings") 
    print("  - Vector search")
    print("  - Health checks")
    
    uvicorn.run(
        "unified_main:app",
        host="127.0.0.1",
        port=port,
        reload=True,
        log_level="info"
    )
