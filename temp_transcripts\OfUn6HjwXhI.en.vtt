WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:00.670 align:start position:0%
 
[Music]

00:00:00.670 --> 00:00:00.680 align:start position:0%
[Music]
 

00:00:00.680 --> 00:00:02.640 align:start position:0%
[Music]
foreign

00:00:02.640 --> 00:00:02.650 align:start position:0%
foreign
 

00:00:02.650 --> 00:00:05.889 align:start position:0%
foreign
[Music]

00:00:05.889 --> 00:00:05.899 align:start position:0%
[Music]
 

00:00:05.899 --> 00:00:09.169 align:start position:0%
[Music]
engineer<00:00:06.899><c> at</c><00:00:07.020><c> anthropic</c><00:00:07.620><c> I</c><00:00:08.400><c> help</c><00:00:08.639><c> people</c><00:00:08.880><c> get</c>

00:00:09.169 --> 00:00:09.179 align:start position:0%
engineer at anthropic I help people get
 

00:00:09.179 --> 00:00:11.209 align:start position:0%
engineer at anthropic I help people get
the<00:00:09.300><c> most</c><00:00:09.420><c> out</c><00:00:09.599><c> of</c><00:00:09.720><c> Claude</c><00:00:10.019><c> with</c><00:00:10.500><c> safety</c><00:00:10.920><c> at</c>

00:00:11.209 --> 00:00:11.219 align:start position:0%
the most out of Claude with safety at
 

00:00:11.219 --> 00:00:13.370 align:start position:0%
the most out of Claude with safety at
the<00:00:11.340><c> top</c><00:00:11.460><c> of</c><00:00:11.639><c> the</c><00:00:11.700><c> moment</c><00:00:12.019><c> first</c><00:00:13.019><c> got</c><00:00:13.259><c> into</c>

00:00:13.370 --> 00:00:13.380 align:start position:0%
the top of the moment first got into
 

00:00:13.380 --> 00:00:15.230 align:start position:0%
the top of the moment first got into
prompt<00:00:13.740><c> engineering</c><00:00:14.160><c> uh</c><00:00:14.639><c> back</c><00:00:14.820><c> in</c><00:00:15.000><c> last</c>

00:00:15.230 --> 00:00:15.240 align:start position:0%
prompt engineering uh back in last
 

00:00:15.240 --> 00:00:17.570 align:start position:0%
prompt engineering uh back in last
August<00:00:15.920><c> anthropic</c><00:00:16.920><c> released</c><00:00:17.279><c> their</c><00:00:17.340><c> paper</c>

00:00:17.570 --> 00:00:17.580 align:start position:0%
August anthropic released their paper
 

00:00:17.580 --> 00:00:19.250 align:start position:0%
August anthropic released their paper
red<00:00:17.940><c> teaming</c><00:00:18.359><c> language</c><00:00:18.539><c> models</c><00:00:18.960><c> to</c><00:00:19.080><c> reduce</c>

00:00:19.250 --> 00:00:19.260 align:start position:0%
red teaming language models to reduce
 

00:00:19.260 --> 00:00:21.890 align:start position:0%
red teaming language models to reduce
harms<00:00:19.800><c> and</c><00:00:20.760><c> immediately</c><00:00:21.180><c> I</c><00:00:21.359><c> read</c><00:00:21.480><c> it</c><00:00:21.660><c> and</c><00:00:21.779><c> it</c>

00:00:21.890 --> 00:00:21.900 align:start position:0%
harms and immediately I read it and it
 

00:00:21.900 --> 00:00:23.029 align:start position:0%
harms and immediately I read it and it
was<00:00:22.020><c> hooked</c>

00:00:23.029 --> 00:00:23.039 align:start position:0%
was hooked
 

00:00:23.039 --> 00:00:25.490 align:start position:0%
was hooked
I<00:00:23.640><c> was</c><00:00:23.820><c> inspired</c><00:00:24.359><c> to</c><00:00:24.480><c> see</c><00:00:24.660><c> that</c><00:00:24.900><c> a</c><00:00:25.019><c> company</c><00:00:25.140><c> was</c>

00:00:25.490 --> 00:00:25.500 align:start position:0%
I was inspired to see that a company was
 

00:00:25.500 --> 00:00:27.769 align:start position:0%
I was inspired to see that a company was
taking<00:00:25.680><c> a</c><00:00:26.100><c> safety</c><00:00:26.460><c> first</c><00:00:26.580><c> approach</c><00:00:27.180><c> to</c>

00:00:27.769 --> 00:00:27.779 align:start position:0%
taking a safety first approach to
 

00:00:27.779 --> 00:00:29.810 align:start position:0%
taking a safety first approach to
researching<00:00:28.680><c> language</c><00:00:28.859><c> models</c><00:00:29.400><c> and</c><00:00:29.699><c> I</c>

00:00:29.810 --> 00:00:29.820 align:start position:0%
researching language models and I
 

00:00:29.820 --> 00:00:31.790 align:start position:0%
researching language models and I
thought<00:00:29.939><c> it</c><00:00:30.060><c> was</c><00:00:30.180><c> really</c><00:00:30.300><c> interesting</c><00:00:30.800><c> how</c>

00:00:31.790 --> 00:00:31.800 align:start position:0%
thought it was really interesting how
 

00:00:31.800 --> 00:00:34.450 align:start position:0%
thought it was really interesting how
you<00:00:32.160><c> could</c><00:00:32.279><c> see</c><00:00:32.520><c> the</c><00:00:32.759><c> ways</c><00:00:33.059><c> that</c><00:00:33.180><c> models</c><00:00:33.600><c> would</c>

00:00:34.450 --> 00:00:34.460 align:start position:0%
you could see the ways that models would
 

00:00:34.460 --> 00:00:37.910 align:start position:0%
you could see the ways that models would
output<00:00:35.460><c> to</c><00:00:35.940><c> different</c><00:00:36.300><c> and</c><00:00:36.899><c> diverse</c><00:00:37.500><c> ranges</c>

00:00:37.910 --> 00:00:37.920 align:start position:0%
output to different and diverse ranges
 

00:00:37.920 --> 00:00:39.049 align:start position:0%
output to different and diverse ranges
of<00:00:38.100><c> prompts</c>

00:00:39.049 --> 00:00:39.059 align:start position:0%
of prompts
 

00:00:39.059 --> 00:00:40.670 align:start position:0%
of prompts
you<00:00:39.540><c> may</c><00:00:39.660><c> be</c><00:00:39.780><c> familiar</c><00:00:40.020><c> with</c><00:00:40.140><c> red</c><00:00:40.320><c> teaming</c>

00:00:40.670 --> 00:00:40.680 align:start position:0%
you may be familiar with red teaming
 

00:00:40.680 --> 00:00:43.130 align:start position:0%
you may be familiar with red teaming
attacks<00:00:41.040><c> as</c><00:00:41.520><c> prompt</c><00:00:42.000><c> exploits</c><00:00:42.600><c> or</c><00:00:42.780><c> the</c><00:00:43.020><c> more</c>

00:00:43.130 --> 00:00:43.140 align:start position:0%
attacks as prompt exploits or the more
 

00:00:43.140 --> 00:00:46.250 align:start position:0%
attacks as prompt exploits or the more
Infamous<00:00:43.440><c> name</c><00:00:43.879><c> jailbreaks</c><00:00:44.879><c> I</c><00:00:45.840><c> decided</c><00:00:46.140><c> to</c>

00:00:46.250 --> 00:00:46.260 align:start position:0%
Infamous name jailbreaks I decided to
 

00:00:46.260 --> 00:00:47.750 align:start position:0%
Infamous name jailbreaks I decided to
start<00:00:46.379><c> writing</c><00:00:46.680><c> jailbreaks</c><00:00:47.219><c> after</c><00:00:47.399><c> reading</c>

00:00:47.750 --> 00:00:47.760 align:start position:0%
start writing jailbreaks after reading
 

00:00:47.760 --> 00:00:49.490 align:start position:0%
start writing jailbreaks after reading
the<00:00:47.879><c> paper</c><00:00:48.000><c> and</c><00:00:48.300><c> becoming</c><00:00:48.660><c> inspired</c><00:00:49.140><c> by</c><00:00:49.320><c> the</c>

00:00:49.490 --> 00:00:49.500 align:start position:0%
the paper and becoming inspired by the
 

00:00:49.500 --> 00:00:51.110 align:start position:0%
the paper and becoming inspired by the
opportunities<00:00:49.920><c> that</c><00:00:50.280><c> still</c><00:00:50.460><c> existed</c><00:00:50.760><c> to</c><00:00:51.000><c> Red</c>

00:00:51.110 --> 00:00:51.120 align:start position:0%
opportunities that still existed to Red
 

00:00:51.120 --> 00:00:52.970 align:start position:0%
opportunities that still existed to Red
Team<00:00:51.300><c> these</c><00:00:51.600><c> models</c><00:00:51.920><c> jailbreaks</c><00:00:52.920><c> are</c>

00:00:52.970 --> 00:00:52.980 align:start position:0%
Team these models jailbreaks are
 

00:00:52.980 --> 00:00:54.830 align:start position:0%
Team these models jailbreaks are
specific<00:00:53.280><c> prompts</c><00:00:53.700><c> that</c><00:00:53.940><c> are</c><00:00:54.239><c> written</c><00:00:54.600><c> to</c>

00:00:54.830 --> 00:00:54.840 align:start position:0%
specific prompts that are written to
 

00:00:54.840 --> 00:00:57.529 align:start position:0%
specific prompts that are written to
circumvent<00:00:55.559><c> the</c><00:00:56.039><c> filters</c><00:00:56.940><c> that</c><00:00:57.300><c> have</c><00:00:57.420><c> been</c>

00:00:57.529 --> 00:00:57.539 align:start position:0%
circumvent the filters that have been
 

00:00:57.539 --> 00:00:59.689 align:start position:0%
circumvent the filters that have been
applied<00:00:57.899><c> on</c><00:00:58.079><c> top</c><00:00:58.260><c> of</c><00:00:58.379><c> language</c><00:00:58.620><c> models</c><00:00:59.160><c> prompt</c>

00:00:59.689 --> 00:00:59.699 align:start position:0%
applied on top of language models prompt
 

00:00:59.699 --> 00:01:01.369 align:start position:0%
applied on top of language models prompt
engineering<00:01:00.180><c> is</c><00:01:00.480><c> the</c><00:01:00.719><c> practice</c><00:01:00.899><c> of</c>

00:01:01.369 --> 00:01:01.379 align:start position:0%
engineering is the practice of
 

00:01:01.379 --> 00:01:03.229 align:start position:0%
engineering is the practice of
optimizing<00:01:01.980><c> your</c><00:01:02.160><c> prompt</c><00:01:02.520><c> in</c><00:01:02.760><c> order</c><00:01:02.879><c> to</c><00:01:03.059><c> get</c>

00:01:03.229 --> 00:01:03.239 align:start position:0%
optimizing your prompt in order to get
 

00:01:03.239 --> 00:01:04.609 align:start position:0%
optimizing your prompt in order to get
the<00:01:03.480><c> best</c><00:01:03.600><c> response</c><00:01:04.140><c> from</c><00:01:04.379><c> the</c><00:01:04.500><c> language</c>

00:01:04.609 --> 00:01:04.619 align:start position:0%
the best response from the language
 

00:01:04.619 --> 00:01:07.190 align:start position:0%
the best response from the language
model<00:01:05.040><c> at</c><00:01:05.700><c> anthropic</c><00:01:06.240><c> we</c><00:01:06.420><c> like</c><00:01:06.600><c> to</c><00:01:06.720><c> take</c><00:01:06.900><c> an</c>

00:01:07.190 --> 00:01:07.200 align:start position:0%
model at anthropic we like to take an
 

00:01:07.200 --> 00:01:09.230 align:start position:0%
model at anthropic we like to take an
empirical<00:01:07.680><c> test</c><00:01:08.040><c> driven</c><00:01:08.460><c> approach</c><00:01:08.760><c> to</c><00:01:08.939><c> prompt</c>

00:01:09.230 --> 00:01:09.240 align:start position:0%
empirical test driven approach to prompt
 

00:01:09.240 --> 00:01:11.210 align:start position:0%
empirical test driven approach to prompt
engineering<00:01:09.780><c> whenever</c><00:01:10.560><c> we</c><00:01:10.740><c> write</c><00:01:10.920><c> a</c><00:01:11.159><c> new</c>

00:01:11.210 --> 00:01:11.220 align:start position:0%
engineering whenever we write a new
 

00:01:11.220 --> 00:01:13.070 align:start position:0%
engineering whenever we write a new
prompt<00:01:11.580><c> we</c><00:01:12.000><c> run</c><00:01:12.119><c> it</c><00:01:12.299><c> against</c><00:01:12.479><c> a</c><00:01:12.720><c> series</c><00:01:12.840><c> of</c>

00:01:13.070 --> 00:01:13.080 align:start position:0%
prompt we run it against a series of
 

00:01:13.080 --> 00:01:14.630 align:start position:0%
prompt we run it against a series of
benchmarks<00:01:13.619><c> in</c><00:01:13.740><c> order</c><00:01:13.860><c> to</c><00:01:14.100><c> scientifically</c>

00:01:14.630 --> 00:01:14.640 align:start position:0%
benchmarks in order to scientifically
 

00:01:14.640 --> 00:01:17.149 align:start position:0%
benchmarks in order to scientifically
measure<00:01:15.060><c> its</c><00:01:15.420><c> performance</c><00:01:16.140><c> with</c><00:01:16.860><c> Claude</c>

00:01:17.149 --> 00:01:17.159 align:start position:0%
measure its performance with Claude
 

00:01:17.159 --> 00:01:19.010 align:start position:0%
measure its performance with Claude
we've<00:01:17.520><c> discovered</c><00:01:17.880><c> a</c><00:01:18.119><c> set</c><00:01:18.299><c> of</c><00:01:18.420><c> best</c><00:01:18.600><c> practices</c>

00:01:19.010 --> 00:01:19.020 align:start position:0%
we've discovered a set of best practices
 

00:01:19.020 --> 00:01:21.109 align:start position:0%
we've discovered a set of best practices
that'll<00:01:19.619><c> allow</c><00:01:19.920><c> you</c><00:01:20.040><c> to</c><00:01:20.280><c> get</c><00:01:20.460><c> the</c><00:01:20.700><c> most</c><00:01:20.759><c> out</c><00:01:21.060><c> of</c>

00:01:21.109 --> 00:01:21.119 align:start position:0%
that'll allow you to get the most out of
 

00:01:21.119 --> 00:01:22.910 align:start position:0%
that'll allow you to get the most out of
the<00:01:21.240><c> model</c>

00:01:22.910 --> 00:01:22.920 align:start position:0%
the model
 

00:01:22.920 --> 00:01:25.850 align:start position:0%
the model
so<00:01:23.520><c> let's</c><00:01:24.060><c> get</c><00:01:24.240><c> into</c><00:01:24.420><c> it</c><00:01:24.659><c> here</c><00:01:25.380><c> are</c><00:01:25.560><c> my</c><00:01:25.680><c> five</c>

00:01:25.850 --> 00:01:25.860 align:start position:0%
so let's get into it here are my five
 

00:01:25.860 --> 00:01:27.410 align:start position:0%
so let's get into it here are my five
tips<00:01:26.220><c> for</c><00:01:26.460><c> getting</c><00:01:26.640><c> the</c><00:01:26.880><c> best</c><00:01:27.000><c> performance</c>

00:01:27.410 --> 00:01:27.420 align:start position:0%
tips for getting the best performance
 

00:01:27.420 --> 00:01:28.789 align:start position:0%
tips for getting the best performance
from<00:01:27.600><c> Claude</c>

00:01:28.789 --> 00:01:28.799 align:start position:0%
from Claude
 

00:01:28.799 --> 00:01:31.789 align:start position:0%
from Claude
first<00:01:29.340><c> describe</c><00:01:30.119><c> your</c><00:01:30.299><c> task</c><00:01:30.479><c> Claude</c><00:01:31.439><c> responds</c>

00:01:31.789 --> 00:01:31.799 align:start position:0%
first describe your task Claude responds
 

00:01:31.799 --> 00:01:33.890 align:start position:0%
first describe your task Claude responds
well<00:01:31.979><c> to</c><00:01:32.280><c> clear</c><00:01:32.520><c> direct</c><00:01:32.939><c> and</c><00:01:33.420><c> Specific</c>

00:01:33.890 --> 00:01:33.900 align:start position:0%
well to clear direct and Specific
 

00:01:33.900 --> 00:01:35.450 align:start position:0%
well to clear direct and Specific
Instructions

00:01:35.450 --> 00:01:35.460 align:start position:0%
Instructions
 

00:01:35.460 --> 00:01:37.010 align:start position:0%
Instructions
let's<00:01:35.880><c> say</c><00:01:36.060><c> you</c><00:01:36.180><c> wanted</c><00:01:36.299><c> Claude</c><00:01:36.720><c> to</c><00:01:36.900><c> remove</c>

00:01:37.010 --> 00:01:37.020 align:start position:0%
let's say you wanted Claude to remove
 

00:01:37.020 --> 00:01:39.649 align:start position:0%
let's say you wanted Claude to remove
personal<00:01:37.680><c> identifiable</c><00:01:38.640><c> information</c><00:01:38.939><c> from</c><00:01:39.540><c> a</c>

00:01:39.649 --> 00:01:39.659 align:start position:0%
personal identifiable information from a
 

00:01:39.659 --> 00:01:40.910 align:start position:0%
personal identifiable information from a
piece<00:01:39.840><c> of</c><00:01:39.960><c> text</c>

00:01:40.910 --> 00:01:40.920 align:start position:0%
piece of text
 

00:01:40.920 --> 00:01:43.130 align:start position:0%
piece of text
explaining<00:01:41.759><c> the</c><00:01:41.939><c> quad</c><00:01:42.240><c> exactly</c><00:01:42.720><c> what</c><00:01:42.960><c> that</c>

00:01:43.130 --> 00:01:43.140 align:start position:0%
explaining the quad exactly what that
 

00:01:43.140 --> 00:01:45.350 align:start position:0%
explaining the quad exactly what that
means<00:01:43.439><c> helps</c><00:01:43.920><c> Claude</c><00:01:44.220><c> recognize</c><00:01:44.640><c> what</c><00:01:44.939><c> pieces</c>

00:01:45.350 --> 00:01:45.360 align:start position:0%
means helps Claude recognize what pieces
 

00:01:45.360 --> 00:01:47.330 align:start position:0%
means helps Claude recognize what pieces
of<00:01:45.479><c> text</c><00:01:45.720><c> to</c><00:01:46.020><c> remove</c>

00:01:47.330 --> 00:01:47.340 align:start position:0%
of text to remove
 

00:01:47.340 --> 00:01:49.910 align:start position:0%
of text to remove
for<00:01:47.820><c> example</c><00:01:48.119><c> email</c><00:01:48.720><c> addresses</c><00:01:49.380><c> and</c><00:01:49.740><c> phone</c>

00:01:49.910 --> 00:01:49.920 align:start position:0%
for example email addresses and phone
 

00:01:49.920 --> 00:01:53.270 align:start position:0%
for example email addresses and phone
numbers<00:01:50.479><c> second</c><00:01:51.479><c> Mark</c><00:01:52.380><c> different</c><00:01:52.860><c> parts</c><00:01:53.100><c> of</c>

00:01:53.270 --> 00:01:53.280 align:start position:0%
numbers second Mark different parts of
 

00:01:53.280 --> 00:01:56.330 align:start position:0%
numbers second Mark different parts of
your<00:01:53.399><c> prompt</c><00:01:53.700><c> with</c><00:01:54.060><c> XML</c><00:01:54.479><c> tags</c><00:01:54.960><c> XML</c><00:01:55.860><c> tags</c><00:01:56.220><c> look</c>

00:01:56.330 --> 00:01:56.340 align:start position:0%
your prompt with XML tags XML tags look
 

00:01:56.340 --> 00:01:58.550 align:start position:0%
your prompt with XML tags XML tags look
like<00:01:56.520><c> this</c>

00:01:58.550 --> 00:01:58.560 align:start position:0%
like this
 

00:01:58.560 --> 00:02:00.109 align:start position:0%
like this
Claude<00:01:59.040><c> has</c><00:01:59.159><c> been</c><00:01:59.340><c> fine-tuned</c><00:01:59.820><c> to</c><00:01:59.939><c> pay</c>

00:02:00.109 --> 00:02:00.119 align:start position:0%
Claude has been fine-tuned to pay
 

00:02:00.119 --> 00:02:02.330 align:start position:0%
Claude has been fine-tuned to pay
special<00:02:00.360><c> attention</c><00:02:00.720><c> to</c><00:02:01.020><c> their</c><00:02:01.200><c> structure</c>

00:02:02.330 --> 00:02:02.340 align:start position:0%
special attention to their structure
 

00:02:02.340 --> 00:02:04.490 align:start position:0%
special attention to their structure
in<00:02:02.700><c> our</c><00:02:02.820><c> example</c><00:02:03.119><c> we</c><00:02:03.360><c> use</c><00:02:03.479><c> XML</c><00:02:04.020><c> tags</c><00:02:04.380><c> to</c>

00:02:04.490 --> 00:02:04.500 align:start position:0%
in our example we use XML tags to
 

00:02:04.500 --> 00:02:06.050 align:start position:0%
in our example we use XML tags to
indicate<00:02:04.860><c> the</c><00:02:05.100><c> beginning</c><00:02:05.219><c> and</c><00:02:05.579><c> end</c><00:02:05.759><c> of</c><00:02:05.939><c> text</c>

00:02:06.050 --> 00:02:06.060 align:start position:0%
indicate the beginning and end of text
 

00:02:06.060 --> 00:02:08.410 align:start position:0%
indicate the beginning and end of text
that<00:02:06.420><c> claw</c><00:02:06.719><c> needs</c><00:02:07.020><c> to</c><00:02:07.079><c> de-identify</c>

00:02:08.410 --> 00:02:08.420 align:start position:0%
that claw needs to de-identify
 

00:02:08.420 --> 00:02:12.050 align:start position:0%
that claw needs to de-identify
third<00:02:09.420><c> give</c><00:02:10.259><c> examples</c><00:02:10.800><c> the</c><00:02:11.520><c> more</c><00:02:11.700><c> examples</c>

00:02:12.050 --> 00:02:12.060 align:start position:0%
third give examples the more examples
 

00:02:12.060 --> 00:02:13.850 align:start position:0%
third give examples the more examples
the<00:02:12.420><c> better</c>

00:02:13.850 --> 00:02:13.860 align:start position:0%
the better
 

00:02:13.860 --> 00:02:15.890 align:start position:0%
the better
including<00:02:14.459><c> a</c><00:02:14.640><c> wide</c><00:02:14.760><c> range</c><00:02:15.000><c> of</c><00:02:15.120><c> examples</c><00:02:15.480><c> helps</c>

00:02:15.890 --> 00:02:15.900 align:start position:0%
including a wide range of examples helps
 

00:02:15.900 --> 00:02:18.530 align:start position:0%
including a wide range of examples helps
Claude<00:02:16.140><c> learn</c><00:02:16.440><c> how</c><00:02:16.680><c> to</c><00:02:16.800><c> do</c><00:02:16.920><c> the</c><00:02:17.099><c> task</c>

00:02:18.530 --> 00:02:18.540 align:start position:0%
Claude learn how to do the task
 

00:02:18.540 --> 00:02:21.050 align:start position:0%
Claude learn how to do the task
back<00:02:19.020><c> to</c><00:02:19.140><c> our</c><00:02:19.319><c> pii</c><00:02:19.860><c> prompt</c>

00:02:21.050 --> 00:02:21.060 align:start position:0%
back to our pii prompt
 

00:02:21.060 --> 00:02:23.089 align:start position:0%
back to our pii prompt
we<00:02:21.420><c> provide</c><00:02:21.720><c> cod</c><00:02:21.959><c> with</c><00:02:22.260><c> examples</c><00:02:22.620><c> of</c><00:02:22.800><c> how</c><00:02:22.980><c> to</c>

00:02:23.089 --> 00:02:23.099 align:start position:0%
we provide cod with examples of how to
 

00:02:23.099 --> 00:02:26.930 align:start position:0%
we provide cod with examples of how to
de-identify<00:02:23.700><c> text</c><00:02:23.940><c> within</c><00:02:24.360><c> XML</c><00:02:24.840><c> tags</c>

00:02:26.930 --> 00:02:26.940 align:start position:0%
de-identify text within XML tags
 

00:02:26.940 --> 00:02:30.589 align:start position:0%
de-identify text within XML tags
fourth<00:02:27.780><c> make</c><00:02:28.620><c> use</c><00:02:28.800><c> of</c><00:02:28.980><c> the</c><00:02:29.220><c> long</c><00:02:29.340><c> context</c>

00:02:30.589 --> 00:02:30.599 align:start position:0%
fourth make use of the long context
 

00:02:30.599 --> 00:02:32.449 align:start position:0%
fourth make use of the long context
pod<00:02:31.080><c> can</c><00:02:31.319><c> read</c><00:02:31.440><c> up</c><00:02:31.680><c> to</c><00:02:31.800><c> a</c><00:02:31.920><c> hundred</c><00:02:32.160><c> thousand</c>

00:02:32.449 --> 00:02:32.459 align:start position:0%
pod can read up to a hundred thousand
 

00:02:32.459 --> 00:02:35.690 align:start position:0%
pod can read up to a hundred thousand
tokens<00:02:32.879><c> that's</c><00:02:33.599><c> roughly</c><00:02:34.020><c> 70</c><00:02:34.920><c> 000</c><00:02:35.040><c> words</c><00:02:35.280><c> or</c>

00:02:35.690 --> 00:02:35.700 align:start position:0%
tokens that's roughly 70 000 words or
 

00:02:35.700 --> 00:02:38.809 align:start position:0%
tokens that's roughly 70 000 words or
the<00:02:35.940><c> length</c><00:02:36.180><c> of</c><00:02:36.300><c> the</c><00:02:36.540><c> entire</c><00:02:36.720><c> Great</c><00:02:36.959><c> Gatsby</c>

00:02:38.809 --> 00:02:38.819 align:start position:0%
the length of the entire Great Gatsby
 

00:02:38.819 --> 00:02:41.690 align:start position:0%
the length of the entire Great Gatsby
and<00:02:39.420><c> finally</c><00:02:39.739><c> the</c><00:02:40.739><c> last</c><00:02:40.860><c> tip</c><00:02:41.160><c> is</c><00:02:41.340><c> to</c><00:02:41.580><c> let</c>

00:02:41.690 --> 00:02:41.700 align:start position:0%
and finally the last tip is to let
 

00:02:41.700 --> 00:02:43.089 align:start position:0%
and finally the last tip is to let
Claude<00:02:42.180><c> think</c>

00:02:43.089 --> 00:02:43.099 align:start position:0%
Claude think
 

00:02:43.099 --> 00:02:45.229 align:start position:0%
Claude think
researchers<00:02:44.099><c> have</c><00:02:44.280><c> discovered</c><00:02:44.700><c> that</c><00:02:45.000><c> giving</c>

00:02:45.229 --> 00:02:45.239 align:start position:0%
researchers have discovered that giving
 

00:02:45.239 --> 00:02:46.790 align:start position:0%
researchers have discovered that giving
language<00:02:45.480><c> models</c><00:02:46.019><c> some</c><00:02:46.200><c> time</c><00:02:46.379><c> to</c><00:02:46.680><c> think</c>

00:02:46.790 --> 00:02:46.800 align:start position:0%
language models some time to think
 

00:02:46.800 --> 00:02:48.410 align:start position:0%
language models some time to think
through<00:02:47.040><c> their</c><00:02:47.280><c> response</c><00:02:47.700><c> before</c><00:02:47.879><c> producing</c>

00:02:48.410 --> 00:02:48.420 align:start position:0%
through their response before producing
 

00:02:48.420 --> 00:02:50.270 align:start position:0%
through their response before producing
their<00:02:48.599><c> final</c><00:02:48.780><c> answer</c><00:02:49.080><c> leads</c><00:02:49.980><c> to</c><00:02:50.099><c> better</c>

00:02:50.270 --> 00:02:50.280 align:start position:0%
their final answer leads to better
 

00:02:50.280 --> 00:02:52.070 align:start position:0%
their final answer leads to better
performance

00:02:52.070 --> 00:02:52.080 align:start position:0%
performance
 

00:02:52.080 --> 00:02:54.470 align:start position:0%
performance
with<00:02:52.500><c> Claude</c><00:02:52.860><c> we</c><00:02:53.280><c> like</c><00:02:53.400><c> to</c><00:02:53.519><c> use</c><00:02:53.640><c> thinking</c><00:02:54.060><c> tags</c>

00:02:54.470 --> 00:02:54.480 align:start position:0%
with Claude we like to use thinking tags
 

00:02:54.480 --> 00:02:56.449 align:start position:0%
with Claude we like to use thinking tags
so<00:02:54.780><c> that</c><00:02:54.959><c> it</c><00:02:55.080><c> can</c><00:02:55.200><c> jot</c><00:02:55.440><c> down</c><00:02:55.620><c> its</c><00:02:55.920><c> ideas</c><00:02:55.980><c> before</c>

00:02:56.449 --> 00:02:56.459 align:start position:0%
so that it can jot down its ideas before
 

00:02:56.459 --> 00:02:58.970 align:start position:0%
so that it can jot down its ideas before
answering<00:02:57.000><c> a</c><00:02:57.180><c> complex</c><00:02:57.480><c> question</c>

00:02:58.970 --> 00:02:58.980 align:start position:0%
answering a complex question
 

00:02:58.980 --> 00:03:01.190 align:start position:0%
answering a complex question
here<00:02:59.459><c> in</c><00:02:59.700><c> this</c><00:02:59.819><c> example</c><00:03:00.180><c> you</c><00:03:00.540><c> can</c><00:03:00.660><c> see</c><00:03:00.840><c> Claude</c>

00:03:01.190 --> 00:03:01.200 align:start position:0%
here in this example you can see Claude
 

00:03:01.200 --> 00:03:03.050 align:start position:0%
here in this example you can see Claude
starts<00:03:01.620><c> to</c><00:03:01.739><c> reason</c><00:03:01.860><c> within</c><00:03:02.340><c> thinking</c><00:03:02.700><c> tags</c>

00:03:03.050 --> 00:03:03.060 align:start position:0%
starts to reason within thinking tags
 

00:03:03.060 --> 00:03:09.650 align:start position:0%
starts to reason within thinking tags
and<00:03:03.480><c> then</c><00:03:03.660><c> outputs</c><00:03:04.080><c> its</c><00:03:04.319><c> final</c><00:03:04.440><c> answer</c>

00:03:09.650 --> 00:03:09.660 align:start position:0%
 
 

00:03:09.660 --> 00:03:12.050 align:start position:0%
 
alright<00:03:10.440><c> so</c><00:03:10.920><c> those</c><00:03:11.099><c> are</c><00:03:11.280><c> my</c><00:03:11.400><c> top</c><00:03:11.580><c> tips</c><00:03:11.879><c> for</c>

00:03:12.050 --> 00:03:12.060 align:start position:0%
alright so those are my top tips for
 

00:03:12.060 --> 00:03:13.610 align:start position:0%
alright so those are my top tips for
getting<00:03:12.239><c> the</c><00:03:12.420><c> most</c><00:03:12.540><c> out</c><00:03:12.780><c> of</c><00:03:12.900><c> Claude</c><00:03:13.200><c> and</c><00:03:13.560><c> a</c>

00:03:13.610 --> 00:03:13.620 align:start position:0%
getting the most out of Claude and a
 

00:03:13.620 --> 00:03:15.229 align:start position:0%
getting the most out of Claude and a
little<00:03:13.739><c> bit</c><00:03:13.860><c> about</c><00:03:13.980><c> me</c><00:03:14.280><c> and</c><00:03:14.519><c> my</c><00:03:14.700><c> own</c><00:03:14.819><c> prompting</c>

00:03:15.229 --> 00:03:15.239 align:start position:0%
little bit about me and my own prompting
 

00:03:15.239 --> 00:03:16.910 align:start position:0%
little bit about me and my own prompting
Journey<00:03:15.599><c> stay</c><00:03:16.080><c> up</c><00:03:16.260><c> to</c><00:03:16.379><c> date</c><00:03:16.500><c> on</c><00:03:16.739><c> the</c><00:03:16.860><c> latest</c>

00:03:16.910 --> 00:03:16.920 align:start position:0%
Journey stay up to date on the latest
 

00:03:16.920 --> 00:03:19.309 align:start position:0%
Journey stay up to date on the latest
prompting<00:03:17.459><c> best</c><00:03:17.640><c> practices</c><00:03:18.060><c> make</c><00:03:18.900><c> sure</c><00:03:19.019><c> to</c><00:03:19.200><c> go</c>

00:03:19.309 --> 00:03:19.319 align:start position:0%
prompting best practices make sure to go
 

00:03:19.319 --> 00:03:21.770 align:start position:0%
prompting best practices make sure to go
check<00:03:19.500><c> out</c><00:03:19.680><c> our</c><00:03:19.980><c> developer</c><00:03:20.400><c> dog</c><00:03:20.640><c> site</c><00:03:21.300><c> and</c><00:03:21.720><c> if</c>

00:03:21.770 --> 00:03:21.780 align:start position:0%
check out our developer dog site and if
 

00:03:21.780 --> 00:03:23.690 align:start position:0%
check out our developer dog site and if
you<00:03:21.900><c> haven't</c><00:03:22.080><c> got</c><00:03:22.319><c> access</c><00:03:22.560><c> to</c><00:03:22.800><c> the</c><00:03:22.920><c> Claude</c><00:03:23.159><c> API</c>

00:03:23.690 --> 00:03:23.700 align:start position:0%
you haven't got access to the Claude API
 

00:03:23.700 --> 00:03:25.610 align:start position:0%
you haven't got access to the Claude API
yet<00:03:23.879><c> you</c><00:03:24.540><c> can</c><00:03:24.659><c> still</c><00:03:24.780><c> practice</c><00:03:25.019><c> your</c><00:03:25.379><c> prompt</c>

00:03:25.610 --> 00:03:25.620 align:start position:0%
yet you can still practice your prompt
 

00:03:25.620 --> 00:03:28.100 align:start position:0%
yet you can still practice your prompt
engineering<00:03:26.040><c> right</c><00:03:26.280><c> now</c><00:03:26.459><c> at</c><00:03:26.879><c> claude.ai</c>

00:03:28.100 --> 00:03:28.110 align:start position:0%
engineering right now at claude.ai
 

00:03:28.110 --> 00:03:31.299 align:start position:0%
engineering right now at claude.ai
[Music]

