import os
import sys
import codecs
import platform

# Configure console encoding for Windows
if platform.system() == 'Windows':
    # Enable unicode output in Windows console
    if sys.stdout.encoding != 'utf-8':
        sys.stdout.reconfigure(encoding='utf-8')
    if sys.stderr.encoding != 'utf-8':
        sys.stderr.reconfigure(encoding='utf-8')
    # Ensure Windows console is in UTF-8 mode
    os.system('chcp 65001')

import logging
import csv
from dotenv import load_dotenv
from supabase import create_client, Client
from supabase.lib.client_options import ClientOptions
import yt_dlp
from tqdm import tqdm
from datetime import datetime, timedelta, timezone
import uuid
import urllib3
import pytz
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Integer, Boolean, DateTime, JSON
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import sessionmaker
from rich import print as rprint
from rich.console import Console
from rich.table import Table as RichTable
import warnings
import webvtt
import re
from io import StringIO
import nltk
from nltk.tokenize import sent_tokenize
import time
import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential
import ssl
import certifi
from httpx import Timeout
import asyncio
from rich.logging import RichHandler
from rich.markdown import Markdown
from rich.theme import Theme
from pathlib import Path
from functools import wraps
import threading
import json
import tiktoken
# Note: Ollama prompt templates are imported in the separate Ollama script
# The script ollama_supabase_query_videos_V7_2request_parallel_feedbackgiven_new_column.py
# imports llama31_promptsV2 which contains the actual prompts used for processing


# Define paths for temporary transcript files
TEMP_TRANSCRIPTS_DIR = "temp_transcripts"
AUX_VTT_FILES_DIR = "aux_vtt_files"

# Ensure temp directories exist
Path(TEMP_TRANSCRIPTS_DIR).mkdir(exist_ok=True)
Path(AUX_VTT_FILES_DIR).mkdir(exist_ok=True)

CHOSEN_WATCHLIST_FILE = "youtube_channels_Watchlist_last_48hours_videos.md"  # Full channel list for catching up after holidays

# Your existing code starts here
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Ensure NLTK sentence tokenizer is downloaded
nltk.download('punkt', quiet=True)

#option here to run without API
USE_YOUTUBE_API = os.getenv('USE_YOUTUBE_API', 'true').lower() == 'false'

# Add this near the top of the file, with other global variables (around line 30-40)
failed_transcript_fetches = {}

"""
This script is designed to fetch youtube videos THAT ARE NOT YET IN THE DATABASE, and save their transcripts and metadata to a Supabase database.
It reads the markdown file "youtube_channels_Watchlist.md" that defines the channels to fetch and a Supabase database to store the metadata.
# Metadata collection explanation

This script collects metadata for YouTube videos using the following process:

1. Channel data is read from a markdown file ("youtube_channels_Watchlist.md") using the `read_channel_data` function.
   - The file contains topics and associated YouTube channel names and IDs.

2. The script uses yt-dlp (a fork of youtube-dl) to fetch video information from each channel.
   - This is likely done in a function not shown in the current file snippet.

3. For each video, the script collects the following metadata:
   - Video ID
   - Channel name
   - Video title
   - Published date
   - Duration
   - Transcript (using YouTubeTranscriptApi)

4. The collected metadata is then saved to a Supabase database.
   - The database has separate tables for different topics (e.g., "youtube_renewable_energy", "youtube_artificial_intelligence", etc.)

5. The script checks for existing videos in the database to avoid duplicates.
   - This is done by comparing the video IDs of newly fetched videos with those already in the database.

6. New videos (those not yet in the database) have their metadata and transcripts saved to the appropriate table in the Supabase database.

The script uses environment variables for Supabase credentials and employs logging for tracking the process and any errors that occur during execution.


"""

# Load environment variables and set up logging
load_dotenv()

# Replace the existing logging configuration with this (after load_dotenv())
def setup_logging():
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Generate filenames with today's date
    today = datetime.now().strftime('%Y%m%d')
    log_file = log_dir / f"youtube_fetcher_{today}.log"
    md_log_file = log_dir / f"youtube_fetcher_{today}.md"
    
    # Custom theme for Rich
    custom_theme = Theme({
        "info": "cyan",
        "warning": "yellow",
        "error": "red bold",
        "critical": "red bold reverse",
    })
    
    # Create Rich console with theme
    console = Console(theme=custom_theme, file=open(md_log_file, "a", encoding="utf-8"))
    
    # Configure logging with both file and Rich handlers
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            RichHandler(console=console, rich_tracebacks=True, markup=True),
            logging.FileHandler(log_file, encoding='utf-8'),
        ]
    )
    
    # Suppress specific loggers
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    # Add markdown header to the MD file
    with open(md_log_file, "a", encoding="utf-8") as md_file:
        md_file.write(f"""# YouTube Fetcher Log - {datetime.now().strftime('%Y-%m-%d')}

## System Information
- Script Version: V25
- Execution Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- Python Version: {sys.version.split()[0]}

## Log Entries

""")
    
    return console, md_log_file

# Initialize logging and get console object
console, md_log_file = setup_logging()

# Constants
MAX_VIDEOS = -1  # Set to -1 to fetch all videos, or any positive number to limit

# Comment out these lines to use all tables from the .md file
# TABLES_TO_CHECK = [
#     "youtube_gme",
# ]

# Add this function after other helper functions (around line 100-150)
def track_failed_fetch(table_name, channel_name, video_id):
    """Track failed transcript fetches by table and channel"""
    if table_name not in failed_transcript_fetches:
        failed_transcript_fetches[table_name] = {}
    if channel_name not in failed_transcript_fetches[table_name]:
        failed_transcript_fetches[table_name][channel_name] = []
    if video_id not in failed_transcript_fetches[table_name][channel_name]:
        failed_transcript_fetches[table_name][channel_name].append(video_id)


# New retry decorator for Supabase API calls

@retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=4, max=10))
def supabase_api_call(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logging.error(f"Supabase API Error: {str(e)}")
            raise
    return wrapper

# Move the Supabase client creation into an async function
async def create_supabase_client():
    url: str = os.getenv('SUPABASE_URL')
    key: str = os.getenv('SERVICE_ROLE_KEY')

    ssl_context = ssl.create_default_context(cafile=certifi.where())
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE

    conn = aiohttp.TCPConnector(limit=100, force_close=True, enable_cleanup_closed=True, ssl=ssl_context)
    session = aiohttp.ClientSession(connector=conn)

    options = ClientOptions(
        schema="public",
        headers={},
        postgrest_client_timeout=Timeout(connect=30, read=30, write=30, pool=30),
        storage_client_timeout=Timeout(connect=30, read=30, write=30, pool=30),
    )

    return create_client(url, key, options=options), session

# Global variable for Supabase client
supabase = None

def read_channel_data(file_name):
    file_path = os.path.join(os.getcwd(), file_name)
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"The file {file_name} does not exist in the current directory.")

    with open(file_path, 'r') as file:
        lines = file.readlines()
    
    channel_data = {}
    current_topic = None
    
    for line in lines:
        line = line.strip()
        if line.startswith('## '):
            current_topic = line[3:]
            channel_data[current_topic] = []
        elif line.startswith('- '):
            parts = line[2:].split(' - ')
            if len(parts) == 2:
                channel_name, channel_id = parts
                channel_data[current_topic].append((channel_name.strip(), channel_id.strip()))
            elif len(parts) == 1:
                channel_id = parts[0].strip()
                channel_data[current_topic].append((None, channel_id))
    
    return channel_data

def check_for_duplicate_ids(channel_data):
    seen_ids = set()
    for topic, channels in channel_data.items():
        for _, channel_id in channels:
            if channel_id in seen_ids:
                logging.error(f"Duplicate channel ID found: {channel_id} in topic {topic}")
                return False
            seen_ids.add(channel_id)
    return True

async def load_existing_video_data(table_name, channel_name):
    try:
        response = supabase.table(table_name) \
            .select("video_id") \
            .eq("channel_name", channel_name) \
            .in_("processed", ["pending", "completed"]) \
            .execute()
        
        return {row['video_id'] for row in response.data}
    except Exception as e:
        logging.error(f"Error loading existing videos: {str(e)}")
        return set()
    
    
def check_channel_name_consistency(channel_data):
    for topic, channels in channel_data.items():
        table_name = f"youtube_{topic.lower().replace(' ', '_')}"
        db_channels = supabase.table(table_name).select("channel_name").execute().data
        db_channel_names = {c['channel_name'] for c in db_channels}
        for channel_name, _ in channels:
            if channel_name and channel_name not in db_channel_names:
                logging.warning(f"Channel name mismatch: '{channel_name}' in .md file not found in database for topic '{topic}'")

def fetch_channel_videos(channel_id, max_videos=MAX_VIDEOS):
    """
    Fetches all videos from the given channel ID.
    Uses an optimized approach to fetch in batches.
    """
    logging.info(f"Fetching videos for channel: {channel_id}")
    
    # First yt-dlp call to get video list
    ydl_opts = {
        'extract_flat': 'in_playlist',
        'force_generic_extractor': False,
        'quiet': True,
        'no_warnings': True,
        'ignoreerrors': True,
        'playlistend': 300 if max_videos <= 0 else max_videos,
    }

    channel_url = f"https://www.youtube.com/channel/{channel_id}/videos"
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            info = ydl.extract_info(channel_url, download=False)
            if not info or 'entries' not in info:
                logging.error(f"No video information found for channel ID: {channel_id}")
                return [], {}

            video_ids = []
            video_info = {}
            
            # Debug: Print all entries
            logging.info(f"Total entries found: {len(info['entries'])}")
            
            # Second yt-dlp call with different options to get detailed metadata
            detailed_opts = {
                'quiet': True,
                'no_warnings': True,
                'ignoreerrors': True,
                'skip_download': True,
            }
            
            with yt_dlp.YoutubeDL(detailed_opts) as detailed_ydl:
                for entry in info['entries']:
                    if entry:
                        video_id = entry['id']
                        
                        try:
                            video_info_entry = detailed_ydl.extract_info(
                                f"https://www.youtube.com/watch?v={video_id}", 
                                download=False
                            )
                            
                            # Get upload date using multiple timestamp fields
                            if video_info_entry.get('release_timestamp'):
                                published_at = datetime.fromtimestamp(video_info_entry['release_timestamp'], tz=timezone.utc)
                            elif video_info_entry.get('timestamp'):
                                published_at = datetime.fromtimestamp(video_info_entry['timestamp'], tz=timezone.utc)
                            elif video_info_entry.get('upload_date'):
                                published_at = datetime.strptime(video_info_entry['upload_date'], '%Y%m%d').replace(tzinfo=timezone.utc)
                            else:
                                logging.warning(f"No publication date found for video {video_id}")
                                continue

                            video_ids.append(video_id)
                            video_info[video_id] = {
                                'id': video_id,
                                'title': video_info_entry.get('title'),
                                'duration': video_info_entry.get('duration'),
                                'upload_date': published_at.strftime('%Y-%m-%d %H:%M:%S'),
                                'timestamp': video_info_entry.get('timestamp'),
                                'release_timestamp': video_info_entry.get('release_timestamp')
                            }
                            logging.info(f"Added video {video_id} to results (uploaded at {published_at})")
                            
                        except Exception as e:
                            logging.error(f"Error fetching detailed info for video {video_id}: {str(e)}")
                            continue

            logging.info(f"Fetched {len(video_ids)} total videos for channel ID: {channel_id}")
            return video_ids, video_info

        except Exception as e:
            logging.error(f"Error fetching videos for channel ID {channel_id}: {str(e)}")
            return [], {}

async def compare_videos(table_name, channel_name, channel_id):
    # Get existing videos once
    existing_videos = await load_existing_video_data(table_name, channel_name)
    
    # Fetch channel videos only once
    channel_video_ids, channel_video_info = fetch_channel_videos(channel_id)
    
    # Debug logging
    logging.info(f"Raw fetch results for {channel_name}:")
    logging.info(f"Number of videos fetched: {len(channel_video_ids)}")
    for vid_id, info in channel_video_info.items():
        logging.info(f"Video {vid_id}: Upload date = {info.get('upload_date')}")
    
    # Process results once
    existing_video_set = set(existing_videos)
    channel_video_set = set(channel_video_ids)
    
    new_videos = list(channel_video_set - existing_video_set)
    missing_videos = list(existing_video_set - channel_video_set)
    
    # Create summary once
    summary = f"""Comparison for channel: {channel_name} (ID: {channel_id})
Total videos in database: {len(existing_videos)}
Total videos on YouTube channel (fetched up to our limit): {len(channel_video_ids)}
New videos (on YouTube but not in database): {len(new_videos)}
Missing videos (in database but not on YouTube): {len(missing_videos)}
Video IDs found: {', '.join(channel_video_ids) if channel_video_ids else 'None'}"""
    
    logging.info(summary)
    
    # Return results only for new videos
    new_video_info = {vid: channel_video_info[vid] for vid in new_videos}
    
    # Return results exactly once
    return new_videos, new_video_info, summary

def format_transcript(transcript):
    formatted_text = ""
    for segment in transcript:
        start_time = f"{int(segment['start'] // 60):02d}:{int(segment['start'] % 60):02d}"
        formatted_text += f"[{start_time}] {segment['text']}\n"
    
    return {"formatted_text": formatted_text.strip()}

import sys
import logging

# Suppress HTTP request logs
logging.getLogger("httpx").setLevel(logging.WARNING)

def clean_text(text):
    # Remove HTML-like tags and extra spaces
    text = re.sub(r'<[^>]+>', '', text)
    text = re.sub(r'\s+', ' ', text).strip()
    return text

def find_overlap(text1, text2):
    # Find the longest overlap between the end of text1 and the start of text2
    max_overlap = min(len(text1), len(text2))
    for i in range(max_overlap, 0, -1):
        if text1[-i:] == text2[:i]:
            return i
    return 0

def merge_captions(captions):
    # Merge captions while removing overlaps
    full_text = ""
    for caption in captions:
        clean_caption = clean_text(caption)
        if not full_text:
            full_text = clean_caption
        else:
            overlap_len = find_overlap(full_text, clean_caption)
            non_overlapping_part = clean_caption[overlap_len:]
            full_text += ' ' + non_overlapping_part
    return full_text

def remove_repeated_sentences(text):
    # Tokenize the text into sentences and remove duplicates
    sentences = sent_tokenize(text)
    unique_sentences = []
    seen_sentences = set()
    for sentence in sentences:
        cleaned_sentence = sentence.strip()
        lower_sentence = cleaned_sentence.lower()
        if lower_sentence not in seen_sentences:
            unique_sentences.append(cleaned_sentence)
            seen_sentences.add(lower_sentence)
    return ' '.join(unique_sentences)

# Replace the supabase_api_call decorator and related functions
async def save_to_supabase(table_name, video_data):
    """Async function to save data to Supabase with proper error handling"""
    @supabase_api_call
    async def insert_video_data():
        response = await supabase.table(table_name).insert(video_data).execute()
        return response

    try:
        response = await insert_video_data()
        if response and response.data:
            return True
        return False
    except Exception as e:
        logging.error(f"Error in save_to_supabase: {str(e)}")
        return False

def fetch_transcript_yt_dlp(video_id):
    """Fetch and process YouTube transcript using yt-dlp with improved file handling"""
    # Use the temp directory for VTT files
    vtt_filename = os.path.join(TEMP_TRANSCRIPTS_DIR, f'{video_id}.en.vtt')
    
    # Pre-cleanup of any existing VTT file
    if os.path.exists(vtt_filename):
        try:
            os.remove(vtt_filename)
        except Exception as e:
            logging.error(f"Error removing existing VTT file for {video_id}: {str(e)}")
            return None

    ydl_opts = {
        'skip_download': True,
        'writesubtitles': False,
        'writeautomaticsub': True,
        'subtitlesformat': 'vtt',
        'subtitleslangs': ['en'],
        'outtmpl': os.path.join(TEMP_TRANSCRIPTS_DIR, f'{video_id}'),
        'quiet': True,
        'no_warnings': True,
    }

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Extract video info first
            info = ydl.extract_info(f'https://www.youtube.com/watch?v={video_id}', download=False)
            
            # Verify automatic captions availability
            if not info.get('automatic_captions', {}).get('en'):
                logging.warning(f"No automatic English subtitles available for video {video_id}")
                return None
            
            # Download the automatic subtitles
            ydl.download([f'https://www.youtube.com/watch?v={video_id}'])
            
            if not os.path.exists(vtt_filename):
                logging.error(f"VTT file not created for video {video_id}")
                return None
            
            try:
                with open(vtt_filename, 'r', encoding='utf-8') as vtt_file:
                    vtt_content = vtt_file.read()
                
                vtt_file = webvtt.read_buffer(StringIO(vtt_content))
                captions = [caption.text for caption in vtt_file]
                full_text = merge_captions(captions)
                
                # Clean the transcript
                cleaned_transcript = remove_repeated_sentences(full_text)
                
                return {"formatted_text": cleaned_transcript}
                
            finally:
                # Ensure VTT file cleanup
                try:
                    if os.path.exists(vtt_filename):
                        os.remove(vtt_filename)
                except Exception as e:
                    logging.error(f"Error cleaning up VTT file for {video_id}: {str(e)}")
                    
    except Exception as e:
        logging.error(f"Error in yt-dlp fetching transcript or processing for video {video_id}: {str(e)}")
        # Cleanup attempt even after error
        try:
            if os.path.exists(vtt_filename):
                os.remove(vtt_filename)
        except Exception as cleanup_error:
            logging.error(f"Error cleaning up VTT file after failure for {video_id}: {str(cleanup_error)}")
        return None

async def fetch_and_save_metadata(table_name, channel_name, channel_id, new_videos, new_video_info):
    """Fetch and save video metadata with improved error handling and async operations"""
    successful_saves = 0
    last_error_message = None
    truly_new_videos = []

    # Use rich for better progress display
    console = Console()

    for index, video_id in enumerate(new_videos, 1):
        if video_id not in new_video_info:
            logging.warning(f"No info found for video {video_id}")
            continue

        info = new_video_info[video_id]
        console.print(f"[yellow]Fetching transcript for video {video_id} ({index}/{len(new_videos)})[/yellow]")
        transcript_result = fetch_transcript_yt_dlp(video_id)
        
        if transcript_result:
            transcript_length = len(transcript_result['formatted_text'])
            console.print(f"[green]✓ Got transcript for {video_id} (length: {transcript_length})[/green]")
            
            # Get current time for created_at
            current_time = datetime.now(timezone.utc)
            
            # Get the video's actual published date from video info
            published_at = datetime.fromtimestamp(
                info.get('release_timestamp') or info.get('timestamp'),
                tz=timezone.utc
            ) if info.get('release_timestamp') or info.get('timestamp') else datetime.strptime(
                info.get('upload_date'),
                '%Y%m%d'
            ).replace(tzinfo=timezone.utc)
            
            # Prepare video data matching exactly with the table schema
            video_data = {
                'id': str(uuid.uuid4()),
                'video_id': video_id,
                'channel_name': channel_name,
                'title': info.get('title', ''),
                'duration': info.get('duration', 0),
                'published_at': published_at.isoformat(),
                'transcript': transcript_result['formatted_text'],
                'processed': 'pending',
                'created_at': current_time.isoformat(),
                'email_sent': False,
                'summary': None,
                'keywords': None,
                'visual_description': None,
                'llm_response': None,
                'llm_call_date': None
            }

            # Save to Supabase
            try:
                console.print(f"[yellow]Saving to Supabase...[/yellow]")
                
                @supabase_api_call
                async def insert_video_data():
                    return supabase.table(table_name).insert(video_data).execute()

                response = await insert_video_data()
                
                if response and response.data:
                    successful_saves += 1
                    truly_new_videos.append(video_id)
                    console.print(f"[green]✓ Successfully saved video {video_id} to {table_name}[/green]")
                else:
                    error_msg = "No data returned from insert operation"
                    console.print(f"[red]× Failed to save video {video_id}: {error_msg}[/red]")
                    logging.error(f"Failed to save video {video_id}: {error_msg}")
                    
            except Exception as e:
                error_message = f"Error saving metadata for video {video_id}: {str(e)}"
                logging.error(error_message)
                console.print(f"[red]× {error_message}[/red]")
                last_error_message = error_message

        else:
            console.print(f"[red]× No transcript available for video {video_id}[/red]")
            track_failed_fetch(table_name, channel_name, video_id)

        # Print a separator line between videos
        console.print("[dim]" + "-" * 80 + "[/dim]")

    # Print summary for this channel
    console.print(f"""
[bold]Channel Summary for {channel_name}:[/bold]
Total videos processed: {len(new_videos)}
Successfully saved: {successful_saves}
Failed: {len(new_videos) - successful_saves}
    """)

    return successful_saves, last_error_message, truly_new_videos

def create_table_if_not_exists(table_name):
    engine = create_engine(os.getenv('DIL_POSTGRES_CONNECTION_STRING'))
    metadata = MetaData()

    try:
        # Check if the table exists
        with engine.connect() as connection:
            if not engine.dialect.has_table(connection, table_name):
                # Table doesn't exist, so create it
                table = Table(table_name, metadata,
                    Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
                    Column('channel_name', String),
                    Column('video_id', String, unique=True, nullable=False),
                    Column('title', String),
                    Column('published_at', DateTime(timezone=True)),
                    Column('duration', Integer),
                    Column('transcript', String),
                    Column('summary', String),
                    Column('keywords', JSON),
                    Column('visual_description', String),
                    Column('llm_response', JSONB, nullable=True),
                    Column('llm_call_date', DateTime(timezone=True)),
                    Column('processed', String, check="processed IN ('pending', 'completed', 'error', 'skipped')"),
                    Column('created_at', DateTime(timezone=True), server_default=text('CURRENT_TIMESTAMP'))
                )
                
                metadata.create_all(engine)
                print(f"Table {table_name} created successfully.")
                
                # Add a trigger to ensure llm_response is always valid JSONB
                connection.execute(text(f"""
                    CREATE OR REPLACE FUNCTION validate_llm_response()
                    RETURNS TRIGGER AS $$
                    BEGIN
                        IF NEW.llm_response IS NOT NULL THEN
                            -- If it's a string, try to parse it as JSON
                            IF pg_typeof(NEW.llm_response) = 'text'::regtype THEN
                                NEW.llm_response = NEW.llm_response::jsonb;
                            END IF;
                        END IF;
                        RETURN NEW;
                    EXCEPTION WHEN OTHERS THEN
                        RAISE WARNING 'Invalid JSONB in llm_response: %', NEW.llm_response;
                        NEW.llm_response = NULL;
                        RETURN NEW;
                    END;
                    $$ LANGUAGE plpgsql;

                    CREATE TRIGGER ensure_valid_llm_response
                    BEFORE INSERT OR UPDATE ON {table_name}
                    FOR EACH ROW
                    EXECUTE FUNCTION validate_llm_response();
                """))
                connection.commit()
            else:
                print(f"Table {table_name} already exists.")

        # Enable RLS for the table (whether it's new or existing)
        Session = sessionmaker(bind=engine)
        with Session() as session:
            enable_rls_for_table(session, table_name)
    except Exception as e:
        print(f"Error creating table {table_name}: {str(e)}")
    finally:
        engine.dispose()

def enable_rls_for_table(session, table_name):
    try:
        session.execute(text(f"ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY;"))
        session.commit()
        print(f"RLS enabled for table {table_name}")
    except Exception as e:
        print(f"Error enabling RLS for table {table_name}: {str(e)}")
        session.rollback()

def print_table(title, data, columns):
    # Create Rich table as before
    table = RichTable(title=title)
    for column in columns:
        table.add_column(column, style="cyan")
    for row in data:
        table.add_row(*[str(item) for item in row])
    
    # Print to console
    console.print(table)
    
    # Create markdown version of the table
    md_table = f"### {title}\n\n|" + "|".join(columns) + "|\n|" + "|".join(["---" for _ in columns]) + "|\n"
    for row in data:
        md_table += "|" + "|".join(str(item) for item in row) + "|\n"
    
    # Fix: Change md_file to md_log_file
    log_to_markdown(md_log_file, "TABLE", md_table)

def generate_table_names(channel_data):
    return [f"youtube_{topic.lower().replace(' ', '_')}" for topic in channel_data.keys()]

def get_tables_to_check(channel_data):
    if 'TABLES_TO_CHECK' in globals():
        return globals()['TABLES_TO_CHECK']
    else:
        return generate_table_names(channel_data)

def validate_processed_status(status):
    """Validate that a processed status is one of the allowed values."""
    valid_statuses = {'pending', 'completed', 'error', 'skipped'}
    if status not in valid_statuses:
        raise ValueError(f"Invalid processed status: {status}. Must be one of {valid_statuses}")
    return status

# Add this function after setup_logging() and before other functions
def log_to_markdown(md_file_path, level, message):
    """
    Logs a message to the markdown file with timestamp and level.
    
    Args:
        md_file_path (Path): Path to the markdown log file
        level (str): Log level (INFO, ERROR, TABLE, etc.)
        message (str): The message to log
    """
    timestamp = datetime.now().strftime('%H:%M:%S')
    with open(md_file_path, "a", encoding="utf-8") as f:
        f.write(f"### {timestamp} - {level}\n")
        f.write(f"{message}\n\n")

async def main():
    global supabase
    session = None
    
    logging.info("=== Starting YouTube Video Fetcher ===")
    log_to_markdown(md_log_file, "INFO", "Starting YouTube Video Fetcher")
    
    try:
        supabase, session = await create_supabase_client()
        
        print("Reading channel data...")
        sys.stdout.flush()
        channel_data = read_channel_data(CHOSEN_WATCHLIST_FILE)
        
        if not check_for_duplicate_ids(channel_data):
            print("Duplicate channel IDs found in the .md file. Exiting.")
            sys.stdout.flush()
            return

        # Create all tables before processing (COMMENTED OUT - tables already exist)
        # print("Creating tables for all topics...")
        # sys.stdout.flush()
        # for topic in channel_data.keys():
        #     table_name = f"youtube_{topic.lower().replace(' ', '_')}"
        #     create_table_if_not_exists(table_name)
        
        # Now check channel name consistency
        print("Checking channel name consistency...")
        sys.stdout.flush()
        check_channel_name_consistency(channel_data)
        
        initial_video_counts = []
        final_video_counts = []

        print("Getting tables to check...")
        sys.stdout.flush()
        tables_to_check = get_tables_to_check(channel_data)

        # Single loop for processing all topics and channels
        for topic, channels in channel_data.items():
            print(f"\nProcessing topic: {topic}")
            sys.stdout.flush()
            
            table_name = f"youtube_{topic.lower().replace(' ', '_')}"
            
            if table_name not in tables_to_check:
                print(f"Skipping table: {table_name}")
                sys.stdout.flush()
                continue
            
            channels_processed = 0
            total_new_videos = 0
            
            topic_initial_counts = []
            topic_final_counts = []
            
            # Process each channel once
            for channel_name, channel_id in tqdm(channels, desc="Channels", disable=None):
                print(f"Processing channel: {channel_name}")
                sys.stdout.flush()
                try:
                    new_videos, new_video_info, summary = await compare_videos(table_name, channel_name, channel_id)
                    print(summary)
                    sys.stdout.flush()
                    
                    initial_count = len(new_videos)
                    topic_initial_counts.append((channel_name, initial_count))
                    
                    if new_videos:
                        await fetch_and_save_metadata(table_name, channel_name, channel_id, new_videos, new_video_info)
                        total_new_videos += len(new_videos)
                    
                    channels_processed += 1
                    
                    final_count = len(new_videos)
                    topic_final_counts.append((channel_name, final_count))
                    
                    print(f"Waiting 0.5 second before next channel...")
                    sys.stdout.flush()
                    time.sleep(0.5)
                except Exception as e:
                    print(f"Error processing channel {channel_name}: {str(e)}")
                    sys.stdout.flush()
                    continue
            
            initial_video_counts.extend([(topic, *count) for count in topic_initial_counts])
            final_video_counts.extend([(topic, *count) for count in topic_final_counts])
            
            print(f"\nCompleted processing for topic: {topic}")
            print(f"Channels processed: {channels_processed}")
            print(f"Total new videos saved: {total_new_videos}")
            sys.stdout.flush()
        
        # Start transcript retry phase
        logging.info("=== Starting Transcript Retry Phase ===")
        print("\nStarting transcript retry phase...")
        sys.stdout.flush()
        
        total_retries = 0
        total_successful = 0
        total_failed = 0
        
        retry_results = []
        
        # Use our tracked failures instead of querying the database
        for table_name, channels in failed_transcript_fetches.items():
            if table_name not in tables_to_check:
                continue
                
            for channel_name, failed_videos in channels.items():
                if not failed_videos:  # Skip if no failed videos for this channel
                    continue
                    
                logging.info(f"Retrying {len(failed_videos)} failed transcripts for channel: {channel_name}")
                
                successful = 0
                failed = 0
                
                for video_id in failed_videos:
                    try:
                        # Try to fetch transcript
                        transcript = None
                        logging.info(f"Retrying transcript fetch for video {video_id}...")
                        
                        # First try with yt-dlp
                        transcript = fetch_transcript_yt_dlp(video_id)
                        
                        # Update the database if we got a transcript
                        if transcript and transcript.get('formatted_text'):
                            @supabase_api_call
                            async def update_transcript():
                                return supabase.table(table_name) \
                                    .update({"transcript": transcript['formatted_text'], "processed": "pending"}) \
                                    .eq("video_id", video_id) \
                                    .execute()
                            
                            await update_transcript()
                            successful += 1
                            logging.info(f"Successfully updated transcript for video {video_id}")
                        else:
                            failed += 1
                            logging.warning(f"Could not fetch transcript for {video_id} in retry attempt")
                        
                        await asyncio.sleep(0.5)  # Small delay between requests
                        
                    except Exception as e:
                        logging.error(f"Error processing video {video_id} during retry: {str(e)}")
                        failed += 1
                
                total_retries += successful + failed
                total_successful += successful
                total_failed += failed
                
                if successful + failed > 0:
                    retry_results.append((table_name.replace('youtube_', ''), channel_name, successful, failed))
        
        # Print retry results if any retries were attempted
        if retry_results:
            print_table("Transcript Retry Results", 
                       retry_results, 
                       ["Topic", "Channel", "Successful Updates", "Failed Updates"])
            
            retry_summary = f"""
Transcript Retry Phase Complete:
- Total videos retried: {total_retries}
- Successfully updated: {total_successful}
- Failed updates: {total_failed}
"""
            logging.info(retry_summary)
            print(retry_summary)
            sys.stdout.flush()
            log_to_markdown(md_log_file, "INFO", retry_summary)
        
        print("\nPrinting summary tables...")
        sys.stdout.flush()
        print_table("Initial Video Counts", initial_video_counts, ["Topic", "Channel", "New Videos"])
        print_table("Final Video Counts", final_video_counts, ["Topic", "Channel", "Saved Videos"])
    
    except FileNotFoundError as e:
        print(f"Error: {str(e)}")
        sys.stdout.flush()
    except Exception as e:
        error_msg = f"Critical error: {str(e)}"
        logging.error(error_msg)
        log_to_markdown(md_log_file, "ERROR", f"```\n{error_msg}\n```")
        raise
    finally:
        if session:
            await session.close()
        
        completion_msg = "Script execution completed."
        logging.info(completion_msg)
        log_to_markdown(md_log_file, "INFO", completion_msg)

async def run_script(script_name):
    print(f"Starting {script_name}...")
    sys.stdout.flush()
    
    try:
        # Use '-u' flag for unbuffered output and set PYTHONUNBUFFERED to '1'
        process = await asyncio.create_subprocess_exec(
            sys.executable, '-u', script_name,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            env=dict(os.environ, PYTHONUNBUFFERED='1')
        )

        async def read_stream(stream, prefix):
            try:
                while True:
                    data = await stream.read(1024)
                    if not data:
                        break
                    try:
                        # Try UTF-8 first
                        decoded = data.decode('utf-8')
                    except UnicodeDecodeError:
                        try:
                            # Fall back to Windows-1252 encoding
                            decoded = data.decode('cp1252')
                        except UnicodeDecodeError:
                            # Last resort - replace invalid characters
                            decoded = data.decode('utf-8', errors='replace')
                    print(f"{prefix}: {decoded}", end='', flush=True)
            except Exception as e:
                logging.error(f"Error reading stream: {e}")

        # Run both stdout and stderr readers concurrently
        await asyncio.gather(
            read_stream(process.stdout, f"{script_name} [OUT]"),
            read_stream(process.stderr, f"{script_name} [ERR]")
        )

        return_code = await process.wait()
        print(f"{script_name} completed with return code {return_code}", flush=True)
    finally:
        # Ensure proper cleanup
        if process.stdout:
            process.stdout.close()
        if process.stderr:
            process.stderr.close()
        await process.wait()

# Modify the script execution
if __name__ == "__main__":
    try:
        # Create and get event loop
        if asyncio.get_event_loop().is_closed():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        else:
            loop = asyncio.get_event_loop()
        
        # Run the main function for video fetching
        loop.run_until_complete(main())
        
        # Add a small delay before running Ollama script
        loop.run_until_complete(asyncio.sleep(2))
        
        # Run Ollama script for processing
        try:
            print("\n=== Running Ollama Processing Script ===")
            loop.run_until_complete(run_script("ollama_supabase_query_videos_V7_2request_parallel_feedbackgiven_new_column.py"))
            
            # Add a small delay before email script
            loop.run_until_complete(asyncio.sleep(2))
            
            # Run email script
            print("\n=== Running Email Processing Script ===")
            loop.run_until_complete(run_script("Just_email_V2.py"))
        except Exception as e:
            logging.error(f"Error in additional scripts: {e}")
            # Continue execution, don't exit
        
    except Exception as e:
        error_msg = f"Critical error in main process: {str(e)}"
        logging.error(error_msg)
        log_to_markdown(md_log_file, "ERROR", f"```\n{error_msg}\n```")
        sys.exit(1)
    finally:
        # Proper cleanup of the event loop
        try:
            pending = asyncio.all_tasks(loop)
            loop.run_until_complete(asyncio.gather(*pending))
        except Exception:
            pass
        
        # Close the loop
        loop.close()
        
        # Final completion message
        print("\n=== Script Execution Completed ===")
        sys.exit(0)
