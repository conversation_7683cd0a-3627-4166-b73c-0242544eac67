'use client'

import { useState, useEffect, useCallback } from 'react'
import { Users, ChevronDown } from 'lucide-react'

interface ChannelSelectorProps {
  selectedChannels: string[]
  onChannelsChange: (channels: string[]) => void
  selectedTopics?: string[]
}

export function ChannelSelector({ selectedChannels, onChannelsChange, selectedTopics }: ChannelSelectorProps) {
  const [channels, setChannels] = useState<string[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(true)

  const fetchChannels = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (selectedTopics && selectedTopics.length > 0) {
        params.append('topics', selectedTopics.join(','))
      }
      
      const response = await fetch(`/api/channels?${params}`)
      if (response.ok) {
        const data = await response.json()
        setChannels(data.channels)
      }
    } catch (error) {
      console.error('Error fetching channels:', error)
    } finally {
      setLoading(false)
    }
  }, [selectedTopics])

  useEffect(() => {
    fetchChannels()
  }, [fetchChannels])

  const handleChannelToggle = (channelName: string) => {
    const newChannels = selectedChannels.includes(channelName)
      ? selectedChannels.filter(c => c !== channelName)
      : [...selectedChannels, channelName]
    
    onChannelsChange(newChannels)
  }

  const clearSelection = () => {
    onChannelsChange([])
  }

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-10 bg-gray-200 rounded-lg"></div>
      </div>
    )
  }

  return (
    <div className="relative">
      <div className="flex items-center gap-2 mb-2">
        <Users className="w-4 h-4 text-gray-600" />
        <span className="text-sm font-medium text-gray-700">
          Filter by Channel {selectedChannels.length > 0 && `(${selectedChannels.length})`}
        </span>
      </div>
      
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <div className="flex items-center justify-between">
            <span className="text-sm">
              {selectedChannels.length === 0
                ? 'All channels'
                : selectedChannels.length === 1
                ? selectedChannels[0]
                : `${selectedChannels.length} channels selected`
              }
            </span>
            <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </div>
        </button>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            <div className="p-2">
              {selectedChannels.length > 0 && (
                <button
                  onClick={clearSelection}
                  className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 rounded"
                >
                  Clear all channels
                </button>
              )}
              
              {channels.length === 0 ? (
                <div className="px-3 py-2 text-sm text-gray-500">
                  No channels found{selectedTopics?.length ? ' for selected topics' : ''}
                </div>
              ) : (
                channels.map((channel) => (
                  <label
                    key={channel}
                    className="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer rounded"
                  >
                    <input
                      type="checkbox"
                      checked={selectedChannels.includes(channel)}
                      onChange={() => handleChannelToggle(channel)}
                      className="mr-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700 truncate" title={channel}>
                      {channel}
                    </span>
                  </label>
                ))
              )}
            </div>
          </div>
        )}
      </div>
      
      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}
