# Port Configuration Summary

## Updated Port Assignment

| Service | Old Port | New Port | Status |
|---------|----------|----------|--------|
| NextJS Frontend | 3000 | 9696 | ✅ Updated |
| Python Service (main) | 8000 | 9000 | ✅ Updated |
| Python AI Service | 8001 | 9001 | ✅ Updated |

## Service URLs

- **Frontend**: http://localhost:9696
- **Python Service**: http://localhost:9000
  - API Documentation: http://localhost:9000/docs
  - Health Check: http://localhost:9000/health
- **Python AI Service**: http://localhost:9001
  - Health Check: http://localhost:9001/

## Files Updated

### Configuration Files
- `package.json` - Updated dev and start scripts
- `.env.example` - Updated default URLs
- `.env.local` - Updated service URLs

### Python Services
- `python-service/main.py` - Port 9000, CORS updated
- `python-ai-service/main.py` - Port 9001, CORS updated

### API Routes
- `src/app/api/chat/route.ts` - Updated fallback URL
- `src/app/api/embeddings/route.ts` - Updated fallback URL

### Test Files
- `tests/test_python_service.py` - Port 9000
- `tests/test_python_ai_service.py` - Port 9001

### Documentation
- `README.md` - Updated frontend URL
- `DEVELOPMENT.md` - Updated all service URLs
- `DATABASE_SETUP.md` - Updated configuration examples

## Quick Start Commands

```bash
# Start Python service (port 9000)
cd python-service
python main.py

# Start Python AI service (port 9001)
cd python-ai-service
python main.py

# Start Frontend (port 9696)
npm run dev
```

## Testing

```bash
# Test Python service
cd tests
python test_python_service.py

# Test Python AI service
python test_python_ai_service.py
```

All services can now run simultaneously without port conflicts.
