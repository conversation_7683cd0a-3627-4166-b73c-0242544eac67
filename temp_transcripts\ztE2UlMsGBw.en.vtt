WEBVTT
Kind: captions
Language: en

00:00:00.030 --> 00:00:01.670 align:start position:0%
 
welcome<00:00:00.420><c> back</c><00:00:00.630><c> so</c><00:00:00.900><c> what</c><00:00:01.140><c> we</c><00:00:01.260><c> did</c><00:00:01.410><c> in</c><00:00:01.500><c> the</c><00:00:01.560><c> last</c>

00:00:01.670 --> 00:00:01.680 align:start position:0%
welcome back so what we did in the last
 

00:00:01.680 --> 00:00:04.370 align:start position:0%
welcome back so what we did in the last
video<00:00:01.949><c> is</c><00:00:02.340><c> we</c><00:00:02.520><c> created</c><00:00:03.000><c> a</c><00:00:03.210><c> new</c><00:00:03.419><c> branch</c><00:00:03.629><c> for</c><00:00:04.350><c> a</c>

00:00:04.370 --> 00:00:04.380 align:start position:0%
video is we created a new branch for a
 

00:00:04.380 --> 00:00:06.829 align:start position:0%
video is we created a new branch for a
new<00:00:04.620><c> feature</c><00:00:05.009><c> where</c><00:00:05.790><c> we're</c><00:00:06.330><c> doing</c><00:00:06.629><c> which</c><00:00:06.810><c> is</c>

00:00:06.829 --> 00:00:06.839 align:start position:0%
new feature where we're doing which is
 

00:00:06.839 --> 00:00:11.650 align:start position:0%
new feature where we're doing which is
just<00:00:07.140><c> a</c><00:00:07.230><c> small</c><00:00:07.379><c> edit</c><00:00:08.389><c> to</c><00:00:09.389><c> one</c><00:00:09.540><c> of</c><00:00:09.690><c> the</c><00:00:09.780><c> tabs</c><00:00:10.019><c> and</c>

00:00:11.650 --> 00:00:11.660 align:start position:0%
just a small edit to one of the tabs and
 

00:00:11.660 --> 00:00:14.419 align:start position:0%
just a small edit to one of the tabs and
we've<00:00:12.660><c> we've</c><00:00:13.139><c> made</c><00:00:13.380><c> a</c><00:00:13.410><c> small</c><00:00:13.740><c> change</c><00:00:13.769><c> and</c><00:00:14.340><c> now</c>

00:00:14.419 --> 00:00:14.429 align:start position:0%
we've we've made a small change and now
 

00:00:14.429 --> 00:00:16.790 align:start position:0%
we've we've made a small change and now
we<00:00:14.490><c> want</c><00:00:14.759><c> to</c><00:00:14.820><c> push</c><00:00:15.089><c> that</c><00:00:15.330><c> change</c><00:00:15.719><c> to</c><00:00:16.080><c> our</c><00:00:16.109><c> repo</c>

00:00:16.790 --> 00:00:16.800 align:start position:0%
we want to push that change to our repo
 

00:00:16.800 --> 00:00:18.859 align:start position:0%
we want to push that change to our repo
so<00:00:17.460><c> I</c><00:00:17.490><c> know</c><00:00:17.670><c> how</c><00:00:17.820><c> to</c><00:00:17.970><c> do</c><00:00:18.150><c> that</c><00:00:18.359><c> we</c><00:00:18.630><c> need</c><00:00:18.779><c> to</c>

00:00:18.859 --> 00:00:18.869 align:start position:0%
so I know how to do that we need to
 

00:00:18.869 --> 00:00:22.849 align:start position:0%
so I know how to do that we need to
first<00:00:19.080><c> check</c><00:00:19.550><c> we</c><00:00:20.550><c> need</c><00:00:20.699><c> to</c><00:00:20.820><c> go</c><00:00:20.970><c> to</c><00:00:21.710><c> yeah</c><00:00:22.710><c> that's</c>

00:00:22.849 --> 00:00:22.859 align:start position:0%
first check we need to go to yeah that's
 

00:00:22.859 --> 00:00:26.060 align:start position:0%
first check we need to go to yeah that's
perfect<00:00:23.070><c> that's</c><00:00:23.369><c> great</c><00:00:24.359><c> thanks</c><00:00:24.900><c> thank</c><00:00:25.710><c> you</c>

00:00:26.060 --> 00:00:26.070 align:start position:0%
perfect that's great thanks thank you
 

00:00:26.070 --> 00:00:29.240 align:start position:0%
perfect that's great thanks thank you
we<00:00:26.430><c> need</c><00:00:26.580><c> to</c><00:00:26.670><c> go</c><00:00:26.820><c> to</c><00:00:26.880><c> our</c><00:00:27.000><c> our</c><00:00:27.619><c> branch</c><00:00:28.619><c> which</c><00:00:29.010><c> is</c>

00:00:29.240 --> 00:00:29.250 align:start position:0%
we need to go to our our branch which is
 

00:00:29.250 --> 00:00:36.799 align:start position:0%
we need to go to our our branch which is
it's<00:00:30.179><c> called</c><00:00:32.989><c> it's</c><00:00:33.989><c> over</c><00:00:34.200><c> here</c><00:00:35.239><c> editing</c><00:00:36.239><c> admin</c>

00:00:36.799 --> 00:00:36.809 align:start position:0%
it's called it's over here editing admin
 

00:00:36.809 --> 00:00:38.410 align:start position:0%
it's called it's over here editing admin
tab<00:00:37.110><c> okay</c>

00:00:38.410 --> 00:00:38.420 align:start position:0%
tab okay
 

00:00:38.420 --> 00:00:42.290 align:start position:0%
tab okay
swish<00:00:39.420><c> meanwhile</c><00:00:39.930><c> tar</c><00:00:40.700><c> master</c><00:00:41.700><c> tab</c><00:00:41.910><c> and</c><00:00:42.180><c> I</c><00:00:42.239><c> do</c>

00:00:42.290 --> 00:00:42.300 align:start position:0%
swish meanwhile tar master tab and I do
 

00:00:42.300 --> 00:00:44.779 align:start position:0%
swish meanwhile tar master tab and I do
control<00:00:42.809><c> V</c><00:00:43.079><c> get</c><00:00:43.649><c> check</c><00:00:43.890><c> out</c><00:00:44.070><c> editing</c><00:00:44.460><c> admin</c>

00:00:44.779 --> 00:00:44.789 align:start position:0%
control V get check out editing admin
 

00:00:44.789 --> 00:00:46.400 align:start position:0%
control V get check out editing admin
tab<00:00:44.969><c> and</c><00:00:45.180><c> I</c><00:00:45.239><c> switch</c><00:00:45.510><c> to</c><00:00:45.719><c> that</c><00:00:45.840><c> branch</c><00:00:46.170><c> now</c>

00:00:46.400 --> 00:00:46.410 align:start position:0%
tab and I switch to that branch now
 

00:00:46.410 --> 00:00:50.270 align:start position:0%
tab and I switch to that branch now
we're<00:00:46.559><c> going</c><00:00:46.710><c> to</c><00:00:46.770><c> do</c><00:00:47.390><c> get</c><00:00:48.390><c> at</c><00:00:48.629><c> a</c><00:00:48.660><c> halt</c><00:00:49.219><c> when</c><00:00:50.219><c> I</c>

00:00:50.270 --> 00:00:50.280 align:start position:0%
we're going to do get at a halt when I
 

00:00:50.280 --> 00:00:56.619 align:start position:0%
we're going to do get at a halt when I
do<00:00:50.460><c> get</c><00:00:50.670><c> commit</c><00:00:51.410><c> -</c><00:00:52.410><c> mm</c><00:00:53.219><c> fixed</c><00:00:53.550><c> admin</c><00:00:54.420><c> tab</c><00:00:55.129><c> okay</c>

00:00:56.619 --> 00:00:56.629 align:start position:0%
do get commit - mm fixed admin tab okay
 

00:00:56.629 --> 00:01:02.150 align:start position:0%
do get commit - mm fixed admin tab okay
and<00:00:57.629><c> finally</c><00:00:58.199><c> I'm</c><00:00:58.289><c> gonna</c><00:00:58.410><c> do</c><00:00:58.649><c> git</c><00:00:58.949><c> push</c><00:00:59.539><c> to</c><00:01:01.160><c> get</c>

00:01:02.150 --> 00:01:02.160 align:start position:0%
and finally I'm gonna do git push to get
 

00:01:02.160 --> 00:01:10.850 align:start position:0%
and finally I'm gonna do git push to get
push<00:01:02.460><c> origin</c><00:01:07.460><c> editing</c><00:01:08.460><c> admin</c><00:01:08.850><c> tab</c><00:01:09.439><c> okay</c><00:01:10.439><c> now</c>

00:01:10.850 --> 00:01:10.860 align:start position:0%
push origin editing admin tab okay now
 

00:01:10.860 --> 00:01:13.340 align:start position:0%
push origin editing admin tab okay now
when<00:01:11.280><c> I</c><00:01:11.310><c> push</c><00:01:11.670><c> there</c><00:01:12.090><c> here's</c><00:01:13.049><c> what's</c><00:01:13.260><c> gonna</c>

00:01:13.340 --> 00:01:13.350 align:start position:0%
when I push there here's what's gonna
 

00:01:13.350 --> 00:01:18.530 align:start position:0%
when I push there here's what's gonna
happen<00:01:13.470><c> I'm</c><00:01:13.890><c> gonna</c><00:01:14.040><c> go</c><00:01:14.189><c> back</c><00:01:14.400><c> to</c><00:01:14.430><c> my</c><00:01:14.700><c> to</c><00:01:15.299><c> my</c><00:01:17.540><c> the</c>

00:01:18.530 --> 00:01:18.540 align:start position:0%
happen I'm gonna go back to my to my the
 

00:01:18.540 --> 00:01:23.840 align:start position:0%
happen I'm gonna go back to my to my the
actual<00:01:19.020><c> repo</c><00:01:19.560><c> here</c><00:01:21.950><c> look</c><00:01:22.950><c> what</c><00:01:23.070><c> happened</c><00:01:23.580><c> at</c>

00:01:23.840 --> 00:01:23.850 align:start position:0%
actual repo here look what happened at
 

00:01:23.850 --> 00:01:36.770 align:start position:0%
actual repo here look what happened at
user<00:01:24.299><c> name</c>

00:01:36.770 --> 00:01:36.780 align:start position:0%
 
 

00:01:36.780 --> 00:01:41.640 align:start position:0%
 
let's<00:01:37.780><c> see</c><00:01:37.930><c> what</c><00:01:38.080><c> happens</c><00:01:38.470><c> here</c><00:01:39.480><c> okay</c><00:01:40.650><c> it</c>

00:01:41.640 --> 00:01:41.650 align:start position:0%
let's see what happens here okay it
 

00:01:41.650 --> 00:01:44.340 align:start position:0%
let's see what happens here okay it
pushed<00:01:41.950><c> it</c><00:01:42.100><c> beautiful</c><00:01:43.000><c> this</c><00:01:43.660><c> is</c><00:01:43.720><c> what</c><00:01:44.020><c> we</c><00:01:44.140><c> want</c>

00:01:44.340 --> 00:01:44.350 align:start position:0%
pushed it beautiful this is what we want
 

00:01:44.350 --> 00:01:46.470 align:start position:0%
pushed it beautiful this is what we want
to<00:01:44.530><c> editing</c><00:01:44.920><c> admin</c><00:01:45.460><c> tab</c><00:01:45.700><c> less</c><00:01:46.030><c> than</c><00:01:46.240><c> a</c><00:01:46.300><c> minute</c>

00:01:46.470 --> 00:01:46.480 align:start position:0%
to editing admin tab less than a minute
 

00:01:46.480 --> 00:01:50.790 align:start position:0%
to editing admin tab less than a minute
ago<00:01:46.840><c> okay</c><00:01:47.850><c> it</c><00:01:48.850><c> it</c><00:01:49.270><c> automatically</c><00:01:50.230><c> says</c><00:01:50.530><c> here</c>

00:01:50.790 --> 00:01:50.800 align:start position:0%
ago okay it it automatically says here
 

00:01:50.800 --> 00:01:53.040 align:start position:0%
ago okay it it automatically says here
to<00:01:51.010><c> compare</c><00:01:51.370><c> and</c><00:01:51.580><c> put</c><00:01:51.820><c> and</c><00:01:51.970><c> and</c><00:01:52.330><c> create</c><00:01:52.690><c> a</c><00:01:52.720><c> pull</c>

00:01:53.040 --> 00:01:53.050 align:start position:0%
to compare and put and and create a pull
 

00:01:53.050 --> 00:01:59.430 align:start position:0%
to compare and put and and create a pull
request<00:01:54.180><c> okay</c><00:01:57.180><c> very</c><00:01:58.180><c> nice</c><00:01:58.330><c> this</c><00:01:59.080><c> is</c><00:01:59.260><c> what</c>

00:01:59.430 --> 00:01:59.440 align:start position:0%
request okay very nice this is what
 

00:01:59.440 --> 00:02:00.660 align:start position:0%
request okay very nice this is what
we're<00:01:59.620><c> always</c><00:01:59.740><c> saying</c><00:02:00.010><c> in</c><00:02:00.130><c> terms</c><00:02:00.280><c> of</c><00:02:00.430><c> pull</c>

00:02:00.660 --> 00:02:00.670 align:start position:0%
we're always saying in terms of pull
 

00:02:00.670 --> 00:02:02.400 align:start position:0%
we're always saying in terms of pull
request<00:02:01.060><c> the</c><00:02:01.180><c> pull</c><00:02:01.420><c> request</c><00:02:01.570><c> is</c><00:02:01.930><c> actually</c><00:02:02.110><c> a</c>

00:02:02.400 --> 00:02:02.410 align:start position:0%
request the pull request is actually a
 

00:02:02.410 --> 00:02:05.100 align:start position:0%
request the pull request is actually a
push<00:02:02.710><c> request</c><00:02:03.190><c> because</c><00:02:04.150><c> I</c><00:02:04.390><c> want</c><00:02:04.570><c> to</c><00:02:04.660><c> push</c><00:02:04.900><c> the</c>

00:02:05.100 --> 00:02:05.110 align:start position:0%
push request because I want to push the
 

00:02:05.110 --> 00:02:07.320 align:start position:0%
push request because I want to push the
changes<00:02:05.620><c> to</c><00:02:05.800><c> the</c><00:02:05.890><c> master</c><00:02:06.100><c> branch</c><00:02:06.520><c> okay</c><00:02:07.150><c> what</c>

00:02:07.320 --> 00:02:07.330 align:start position:0%
changes to the master branch okay what
 

00:02:07.330 --> 00:02:11.040 align:start position:0%
changes to the master branch okay what
I'm<00:02:07.420><c> doing</c><00:02:07.720><c> here</c><00:02:08.020><c> is</c><00:02:08.310><c> I</c><00:02:09.310><c> go</c><00:02:10.119><c> to</c><00:02:10.149><c> the</c><00:02:10.510><c> pull</c>

00:02:11.040 --> 00:02:11.050 align:start position:0%
I'm doing here is I go to the pull
 

00:02:11.050 --> 00:02:14.220 align:start position:0%
I'm doing here is I go to the pull
request<00:02:11.260><c> here</c><00:02:12.250><c> I</c><00:02:12.550><c> go</c><00:02:13.540><c> to</c><00:02:13.600><c> open</c><00:02:13.750><c> a</c><00:02:13.959><c> pull</c><00:02:14.170><c> request</c>

00:02:14.220 --> 00:02:14.230 align:start position:0%
request here I go to open a pull request
 

00:02:14.230 --> 00:02:16.290 align:start position:0%
request here I go to open a pull request
and<00:02:14.770><c> I'm</c><00:02:14.830><c> saying</c><00:02:15.070><c> I'm</c><00:02:15.250><c> comparing</c><00:02:15.730><c> the</c><00:02:15.820><c> master</c>

00:02:16.290 --> 00:02:16.300 align:start position:0%
and I'm saying I'm comparing the master
 

00:02:16.300 --> 00:02:18.720 align:start position:0%
and I'm saying I'm comparing the master
branch<00:02:16.630><c> to</c><00:02:17.440><c> this</c><00:02:17.590><c> other</c><00:02:17.860><c> branch</c><00:02:18.220><c> which</c><00:02:18.700><c> is</c>

00:02:18.720 --> 00:02:18.730 align:start position:0%
branch to this other branch which is
 

00:02:18.730 --> 00:02:21.720 align:start position:0%
branch to this other branch which is
called<00:02:19.060><c> editing</c><00:02:19.570><c> admin</c><00:02:20.290><c> tab</c><00:02:20.560><c> okay</c><00:02:20.980><c> you</c><00:02:21.220><c> see</c><00:02:21.580><c> we</c>

00:02:21.720 --> 00:02:21.730 align:start position:0%
called editing admin tab okay you see we
 

00:02:21.730 --> 00:02:23.340 align:start position:0%
called editing admin tab okay you see we
have<00:02:21.850><c> other</c><00:02:22.060><c> branches</c><00:02:22.510><c> we</c><00:02:22.660><c> can</c><00:02:22.810><c> compare</c><00:02:23.230><c> in</c>

00:02:23.340 --> 00:02:23.350 align:start position:0%
have other branches we can compare in
 

00:02:23.350 --> 00:02:25.920 align:start position:0%
have other branches we can compare in
order<00:02:23.530><c> to</c><00:02:23.680><c> create</c><00:02:23.920><c> a</c><00:02:24.340><c> new</c><00:02:24.520><c> poll</c><00:02:25.180><c> request</c><00:02:25.630><c> and</c>

00:02:25.920 --> 00:02:25.930 align:start position:0%
order to create a new poll request and
 

00:02:25.930 --> 00:02:28.050 align:start position:0%
order to create a new poll request and
then<00:02:26.770><c> I</c><00:02:26.800><c> you</c><00:02:26.980><c> can</c><00:02:27.310><c> write</c><00:02:27.490><c> some</c><00:02:27.670><c> notes</c><00:02:27.850><c> over</c>

00:02:28.050 --> 00:02:28.060 align:start position:0%
then I you can write some notes over
 

00:02:28.060 --> 00:02:30.510 align:start position:0%
then I you can write some notes over
here<00:02:28.240><c> that's</c><00:02:28.750><c> the</c><00:02:28.959><c> message</c><00:02:29.200><c> and</c><00:02:29.770><c> I</c><00:02:30.040><c> see</c><00:02:30.250><c> that</c>

00:02:30.510 --> 00:02:30.520 align:start position:0%
here that's the message and I see that
 

00:02:30.520 --> 00:02:33.120 align:start position:0%
here that's the message and I see that
the<00:02:30.970><c> pages</c><00:02:31.510><c> that</c><00:02:31.750><c> were</c><00:02:32.080><c> changed</c><00:02:32.560><c> over</c><00:02:32.830><c> here</c>

00:02:33.120 --> 00:02:33.130 align:start position:0%
the pages that were changed over here
 

00:02:33.130 --> 00:02:35.550 align:start position:0%
the pages that were changed over here
everything<00:02:34.000><c> is</c><00:02:34.120><c> in</c><00:02:34.240><c> red</c><00:02:34.450><c> is</c><00:02:34.600><c> what</c><00:02:34.630><c> was</c><00:02:34.959><c> deleted</c>

00:02:35.550 --> 00:02:35.560 align:start position:0%
everything is in red is what was deleted
 

00:02:35.560 --> 00:02:39.360 align:start position:0%
everything is in red is what was deleted
everything<00:02:36.160><c> and</c><00:02:37.530><c> as</c><00:02:38.530><c> you</c><00:02:38.680><c> can</c><00:02:38.800><c> see</c><00:02:38.860><c> some</c><00:02:39.160><c> stuff</c>

00:02:39.360 --> 00:02:39.370 align:start position:0%
everything and as you can see some stuff
 

00:02:39.370 --> 00:02:43.290 align:start position:0%
everything and as you can see some stuff
was<00:02:39.580><c> deleted</c><00:02:41.010><c> okay</c><00:02:42.010><c> so</c><00:02:42.070><c> we're</c><00:02:42.580><c> ready</c><00:02:42.610><c> to</c><00:02:42.880><c> push</c>

00:02:43.290 --> 00:02:43.300 align:start position:0%
was deleted okay so we're ready to push
 

00:02:43.300 --> 00:02:45.870 align:start position:0%
was deleted okay so we're ready to push
it<00:02:43.510><c> create</c><00:02:44.380><c> the</c><00:02:44.530><c> pull</c><00:02:44.769><c> request</c><00:02:44.890><c> we</c><00:02:45.430><c> verify</c>

00:02:45.870 --> 00:02:45.880 align:start position:0%
it create the pull request we verify
 

00:02:45.880 --> 00:02:50.009 align:start position:0%
it create the pull request we verify
that<00:02:46.030><c> everything</c><00:02:46.180><c> works</c><00:02:47.400><c> okay</c><00:02:48.400><c> and</c><00:02:49.019><c> it's</c>

00:02:50.009 --> 00:02:50.019 align:start position:0%
that everything works okay and it's
 

00:02:50.019 --> 00:02:51.720 align:start position:0%
that everything works okay and it's
gonna<00:02:50.200><c> check</c><00:02:50.530><c> whether</c><00:02:50.769><c> it's</c><00:02:50.980><c> able</c><00:02:51.220><c> to</c><00:02:51.370><c> merge</c>

00:02:51.720 --> 00:02:51.730 align:start position:0%
gonna check whether it's able to merge
 

00:02:51.730 --> 00:02:54.540 align:start position:0%
gonna check whether it's able to merge
without<00:02:52.150><c> any</c><00:02:52.300><c> conflicts</c><00:02:52.989><c> and</c><00:02:53.140><c> merge</c><00:02:54.100><c> the</c><00:02:54.340><c> pull</c>

00:02:54.540 --> 00:02:54.550 align:start position:0%
without any conflicts and merge the pull
 

00:02:54.550 --> 00:02:57.120 align:start position:0%
without any conflicts and merge the pull
request<00:02:54.670><c> confirm</c><00:02:55.450><c> the</c><00:02:55.630><c> merge</c><00:02:55.870><c> and</c><00:02:56.200><c> you</c><00:02:56.950><c> are</c>

00:02:57.120 --> 00:02:57.130 align:start position:0%
request confirm the merge and you are
 

00:02:57.130 --> 00:02:59.759 align:start position:0%
request confirm the merge and you are
done<00:02:57.430><c> okay</c><00:02:58.000><c> if</c><00:02:58.600><c> you</c><00:02:58.720><c> go</c><00:02:58.870><c> back</c><00:02:59.080><c> over</c><00:02:59.110><c> here</c><00:02:59.470><c> to</c>

00:02:59.759 --> 00:02:59.769 align:start position:0%
done okay if you go back over here to
 

00:02:59.769 --> 00:03:03.000 align:start position:0%
done okay if you go back over here to
the<00:03:00.310><c> home</c><00:03:00.519><c> page</c><00:03:00.959><c> we're</c><00:03:01.959><c> gonna</c><00:03:02.110><c> see</c><00:03:02.440><c> that</c><00:03:02.830><c> our</c>

00:03:03.000 --> 00:03:03.010 align:start position:0%
the home page we're gonna see that our
 

00:03:03.010 --> 00:03:05.940 align:start position:0%
the home page we're gonna see that our
changes<00:03:03.340><c> were</c><00:03:03.670><c> made</c><00:03:03.940><c> it'll</c><00:03:04.510><c> say</c><00:03:04.780><c> two</c><00:03:05.590><c> minutes</c>

00:03:05.940 --> 00:03:05.950 align:start position:0%
changes were made it'll say two minutes
 

00:03:05.950 --> 00:03:06.420 align:start position:0%
changes were made it'll say two minutes
ago

00:03:06.420 --> 00:03:06.430 align:start position:0%
ago
 

00:03:06.430 --> 00:03:08.610 align:start position:0%
ago
I<00:03:06.459><c> fixed</c><00:03:07.060><c> the</c><00:03:07.120><c> admin</c><00:03:07.570><c> tab</c><00:03:07.780><c> of</c><00:03:07.959><c> that</c><00:03:08.110><c> specific</c>

00:03:08.610 --> 00:03:08.620 align:start position:0%
I fixed the admin tab of that specific
 

00:03:08.620 --> 00:03:09.960 align:start position:0%
I fixed the admin tab of that specific
page<00:03:08.830><c> and</c><00:03:09.070><c> that</c><00:03:09.160><c> and</c><00:03:09.400><c> right</c><00:03:09.519><c> now</c><00:03:09.670><c> we're</c><00:03:09.820><c> on</c><00:03:09.850><c> the</c>

00:03:09.960 --> 00:03:09.970 align:start position:0%
page and that and right now we're on the
 

00:03:09.970 --> 00:03:11.790 align:start position:0%
page and that and right now we're on the
branch<00:03:10.300><c> master</c><00:03:10.630><c> so</c><00:03:11.440><c> you</c><00:03:11.530><c> know</c><00:03:11.650><c> that</c>

00:03:11.790 --> 00:03:11.800 align:start position:0%
branch master so you know that
 

00:03:11.800 --> 00:03:13.320 align:start position:0%
branch master so you know that
everything<00:03:12.100><c> has</c><00:03:12.220><c> been</c><00:03:12.250><c> pushed</c><00:03:12.640><c> to</c><00:03:12.820><c> the</c><00:03:12.910><c> master</c>

00:03:13.320 --> 00:03:13.330 align:start position:0%
everything has been pushed to the master
 

00:03:13.330 --> 00:03:15.660 align:start position:0%
everything has been pushed to the master
branch<00:03:13.540><c> next</c><00:03:13.900><c> step</c><00:03:14.230><c> is</c><00:03:14.769><c> to</c><00:03:15.040><c> push</c><00:03:15.220><c> everything</c>

00:03:15.660 --> 00:03:15.670 align:start position:0%
branch next step is to push everything
 

00:03:15.670 --> 00:03:18.440 align:start position:0%
branch next step is to push everything
to<00:03:15.850><c> our</c><00:03:15.880><c> Heroku</c><00:03:16.660><c> app</c><00:03:16.870><c> as</c><00:03:17.200><c> you</c><00:03:17.470><c> can</c><00:03:17.650><c> see</c><00:03:17.890><c> are</c>

00:03:18.440 --> 00:03:18.450 align:start position:0%
to our Heroku app as you can see are
 

00:03:18.450 --> 00:03:22.380 align:start position:0%
to our Heroku app as you can see are
currently<00:03:19.450><c> on</c><00:03:19.630><c> the</c><00:03:19.660><c> Roku</c><00:03:19.989><c> app</c><00:03:20.350><c> as</c><00:03:20.680><c> this</c><00:03:21.610><c> this</c>

00:03:22.380 --> 00:03:22.390 align:start position:0%
currently on the Roku app as this this
 

00:03:22.390 --> 00:03:24.390 align:start position:0%
currently on the Roku app as this this
stuff<00:03:22.630><c> over</c><00:03:22.840><c> here</c><00:03:22.989><c> that</c><00:03:23.290><c> we</c><00:03:23.440><c> don't</c><00:03:23.620><c> want</c><00:03:23.860><c> ok</c>

00:03:24.390 --> 00:03:24.400 align:start position:0%
stuff over here that we don't want ok
 

00:03:24.400 --> 00:03:26.460 align:start position:0%
stuff over here that we don't want ok
this<00:03:24.580><c> admin</c><00:03:25.090><c> strap</c><00:03:25.390><c> all</c><00:03:25.600><c> that</c><00:03:25.810><c> stuff</c><00:03:26.080><c> is</c><00:03:26.230><c> about</c>

00:03:26.460 --> 00:03:26.470 align:start position:0%
this admin strap all that stuff is about
 

00:03:26.470 --> 00:03:32.310 align:start position:0%
this admin strap all that stuff is about
to<00:03:26.650><c> get</c><00:03:26.830><c> to</c><00:03:27.670><c> get</c><00:03:28.470><c> creamed</c><00:03:29.500><c> okay</c><00:03:30.060><c> get</c><00:03:31.060><c> push</c><00:03:31.450><c> Oh</c>

00:03:32.310 --> 00:03:32.320 align:start position:0%
to get to get creamed okay get push Oh
 

00:03:32.320 --> 00:03:34.289 align:start position:0%
to get to get creamed okay get push Oh
but<00:03:32.440><c> in</c><00:03:32.650><c> order</c><00:03:32.860><c> to</c><00:03:33.100><c> push</c><00:03:33.340><c> to</c><00:03:33.519><c> Heroku</c><00:03:33.820><c> you</c><00:03:34.180><c> need</c>

00:03:34.289 --> 00:03:34.299 align:start position:0%
but in order to push to Heroku you need
 

00:03:34.299 --> 00:03:39.120 align:start position:0%
but in order to push to Heroku you need
to<00:03:34.420><c> be</c><00:03:34.540><c> on</c><00:03:34.830><c> the</c><00:03:37.200><c> in</c><00:03:38.200><c> to</c><00:03:38.350><c> be</c><00:03:38.440><c> on</c><00:03:38.560><c> the</c><00:03:38.680><c> master</c>

00:03:39.120 --> 00:03:39.130 align:start position:0%
to be on the in to be on the master
 

00:03:39.130 --> 00:03:40.920 align:start position:0%
to be on the in to be on the master
branch<00:03:39.310><c> okay</c><00:03:39.730><c> you</c><00:03:39.790><c> can't</c><00:03:40.090><c> pull</c><00:03:40.450><c> from</c><00:03:40.690><c> you</c>

00:03:40.920 --> 00:03:40.930 align:start position:0%
branch okay you can't pull from you
 

00:03:40.930 --> 00:03:43.229 align:start position:0%
branch okay you can't pull from you
can't<00:03:41.230><c> push</c><00:03:41.440><c> to</c><00:03:41.620><c> Heroku</c><00:03:41.860><c> from</c><00:03:42.220><c> anything</c><00:03:43.000><c> other</c>

00:03:43.229 --> 00:03:43.239 align:start position:0%
can't push to Heroku from anything other
 

00:03:43.239 --> 00:03:45.569 align:start position:0%
can't push to Heroku from anything other
than<00:03:43.450><c> the</c><00:03:43.570><c> master</c><00:03:43.930><c> branch</c><00:03:44.170><c> so</c><00:03:44.440><c> in</c><00:03:44.859><c> order</c><00:03:45.310><c> to</c><00:03:45.430><c> do</c>

00:03:45.569 --> 00:03:45.579 align:start position:0%
than the master branch so in order to do
 

00:03:45.579 --> 00:03:47.520 align:start position:0%
than the master branch so in order to do
that<00:03:45.760><c> first</c><00:03:46.030><c> we</c><00:03:46.329><c> need</c><00:03:46.480><c> to</c><00:03:46.690><c> check</c><00:03:46.959><c> which</c><00:03:47.200><c> branch</c>

00:03:47.520 --> 00:03:47.530 align:start position:0%
that first we need to check which branch
 

00:03:47.530 --> 00:03:48.530 align:start position:0%
that first we need to check which branch
around

00:03:48.530 --> 00:03:48.540 align:start position:0%
around
 

00:03:48.540 --> 00:03:51.330 align:start position:0%
around
we're<00:03:49.540><c> on</c><00:03:49.660><c> the</c><00:03:49.780><c> editing</c><00:03:50.110><c> admin</c><00:03:50.620><c> tab</c><00:03:50.860><c> and</c><00:03:51.190><c> we</c>

00:03:51.330 --> 00:03:51.340 align:start position:0%
we're on the editing admin tab and we
 

00:03:51.340 --> 00:03:54.480 align:start position:0%
we're on the editing admin tab and we
want<00:03:51.520><c> to</c><00:03:51.610><c> go</c><00:03:51.760><c> to</c><00:03:51.820><c> get</c><00:03:52.240><c> check</c><00:03:52.630><c> out</c><00:03:52.840><c> master</c><00:03:53.710><c> wanna</c>

00:03:54.480 --> 00:03:54.490 align:start position:0%
want to go to get check out master wanna
 

00:03:54.490 --> 00:03:56.820 align:start position:0%
want to go to get check out master wanna
go<00:03:54.610><c> to</c><00:03:54.670><c> the</c><00:03:54.790><c> master</c><00:03:55.180><c> branch</c><00:03:55.360><c> here</c><00:03:55.810><c> and</c><00:03:56.590><c> it</c><00:03:56.740><c> says</c>

00:03:56.820 --> 00:03:56.830 align:start position:0%
go to the master branch here and it says
 

00:03:56.830 --> 00:03:58.620 align:start position:0%
go to the master branch here and it says
your<00:03:57.010><c> branch</c><00:03:57.250><c> is</c><00:03:57.370><c> ahead</c><00:03:57.490><c> of</c><00:03:57.790><c> origin</c><00:03:58.180><c> master</c><00:03:58.330><c> by</c>

00:03:58.620 --> 00:03:58.630 align:start position:0%
your branch is ahead of origin master by
 

00:03:58.630 --> 00:04:00.630 align:start position:0%
your branch is ahead of origin master by
35<00:03:59.050><c> commit</c><00:03:59.440><c> so</c><00:03:59.560><c> I</c><00:03:59.590><c> want</c><00:03:59.770><c> to</c><00:03:59.830><c> pull</c><00:04:00.220><c> everything</c>

00:04:00.630 --> 00:04:00.640 align:start position:0%
35 commit so I want to pull everything
 

00:04:00.640 --> 00:04:04.280 align:start position:0%
35 commit so I want to pull everything
from<00:04:01.150><c> from</c><00:04:01.780><c> this</c><00:04:01.960><c> new</c><00:04:02.320><c> this</c><00:04:02.950><c> new</c><00:04:03.010><c> one</c><00:04:03.460><c> here</c>

00:04:04.280 --> 00:04:04.290 align:start position:0%
from from this new this new one here
 

00:04:04.290 --> 00:04:12.990 align:start position:0%
from from this new this new one here
editing<00:04:05.290><c> admin</c><00:04:05.860><c> tab</c><00:04:06.660><c> let's</c><00:04:07.660><c> see</c><00:04:07.840><c> and</c><00:04:08.490><c> I</c><00:04:12.000><c> don't</c>

00:04:12.990 --> 00:04:13.000 align:start position:0%
editing admin tab let's see and I don't
 

00:04:13.000 --> 00:04:15.060 align:start position:0%
editing admin tab let's see and I don't
know<00:04:13.120><c> we</c><00:04:13.270><c> saw</c><00:04:13.450><c> master</c><00:04:13.750><c> is</c><00:04:14.050><c> good</c><00:04:14.290><c> okay</c><00:04:14.770><c> so</c><00:04:14.830><c> we</c>

00:04:15.060 --> 00:04:15.070 align:start position:0%
know we saw master is good okay so we
 

00:04:15.070 --> 00:04:16.229 align:start position:0%
know we saw master is good okay so we
want<00:04:15.190><c> to</c><00:04:15.310><c> get</c><00:04:15.670><c> push</c>

00:04:16.229 --> 00:04:16.239 align:start position:0%
want to get push
 

00:04:16.239 --> 00:04:21.810 align:start position:0%
want to get push
Oroku<00:04:17.940><c> master</c><00:04:20.130><c> let's</c><00:04:21.130><c> check</c><00:04:21.310><c> if</c><00:04:21.430><c> everything</c>

00:04:21.810 --> 00:04:21.820 align:start position:0%
Oroku master let's check if everything
 

00:04:21.820 --> 00:04:25.290 align:start position:0%
Oroku master let's check if everything
is<00:04:21.850><c> video</c><00:04:22.300><c> oh</c><00:04:22.450><c> crap</c><00:04:23.230><c> no</c><00:04:23.590><c> I</c><00:04:23.970><c> need</c><00:04:24.970><c> to</c><00:04:25.120><c> cancel</c>

00:04:25.290 --> 00:04:25.300 align:start position:0%
is video oh crap no I need to cancel
 

00:04:25.300 --> 00:04:30.450 align:start position:0%
is video oh crap no I need to cancel
this<00:04:25.620><c> okay</c><00:04:28.020><c> here's</c><00:04:29.020><c> the</c><00:04:29.230><c> problem</c><00:04:29.380><c> the</c><00:04:30.280><c> moment</c>

00:04:30.450 --> 00:04:30.460 align:start position:0%
this okay here's the problem the moment
 

00:04:30.460 --> 00:04:32.390 align:start position:0%
this okay here's the problem the moment
that<00:04:30.730><c> we</c><00:04:30.850><c> switched</c><00:04:31.180><c> our</c><00:04:31.510><c> master</c><00:04:32.080><c> branch</c>

00:04:32.390 --> 00:04:32.400 align:start position:0%
that we switched our master branch
 

00:04:32.400 --> 00:04:36.210 align:start position:0%
that we switched our master branch
you'll<00:04:33.400><c> notice</c><00:04:33.700><c> that</c><00:04:33.880><c> we</c><00:04:34.060><c> have</c><00:04:34.920><c> we</c><00:04:35.920><c> have</c><00:04:35.950><c> the</c>

00:04:36.210 --> 00:04:36.220 align:start position:0%
you'll notice that we have we have the
 

00:04:36.220 --> 00:04:39.210 align:start position:0%
you'll notice that we have we have the
text<00:04:36.580><c> again</c><00:04:36.820><c> okay</c><00:04:37.510><c> this</c><00:04:38.290><c> welcome</c><00:04:38.800><c> Brad</c><00:04:39.010><c> that's</c>

00:04:39.210 --> 00:04:39.220 align:start position:0%
text again okay this welcome Brad that's
 

00:04:39.220 --> 00:04:41.070 align:start position:0%
text again okay this welcome Brad that's
because<00:04:39.550><c> this</c><00:04:39.730><c> is</c><00:04:39.880><c> the</c><00:04:40.000><c> master</c><00:04:40.450><c> branch</c><00:04:40.660><c> on</c><00:04:40.930><c> my</c>

00:04:41.070 --> 00:04:41.080 align:start position:0%
because this is the master branch on my
 

00:04:41.080 --> 00:04:43.260 align:start position:0%
because this is the master branch on my
local<00:04:41.350><c> computer</c><00:04:41.530><c> still</c><00:04:42.280><c> has</c><00:04:42.460><c> the</c><00:04:42.700><c> text</c><00:04:43.090><c> that</c><00:04:43.210><c> I</c>

00:04:43.260 --> 00:04:43.270 align:start position:0%
local computer still has the text that I
 

00:04:43.270 --> 00:04:45.540 align:start position:0%
local computer still has the text that I
want<00:04:43.480><c> to</c><00:04:43.630><c> get</c><00:04:43.780><c> rid</c><00:04:44.050><c> of</c><00:04:44.110><c> and</c><00:04:44.890><c> in</c><00:04:45.040><c> order</c><00:04:45.280><c> to</c><00:04:45.400><c> do</c>

00:04:45.540 --> 00:04:45.550 align:start position:0%
want to get rid of and in order to do
 

00:04:45.550 --> 00:04:49.790 align:start position:0%
want to get rid of and in order to do
that<00:04:45.730><c> I'm</c><00:04:46.000><c> gonna</c><00:04:46.150><c> do</c><00:04:46.360><c> a</c><00:04:46.390><c> git</c><00:04:46.750><c> pull</c><00:04:47.110><c> origin</c>

00:04:49.790 --> 00:04:49.800 align:start position:0%
 
 

00:04:49.800 --> 00:04:52.890 align:start position:0%
 
editing<00:04:50.800><c> admin</c><00:04:51.220><c> tab</c><00:04:51.460><c> and</c><00:04:51.850><c> pulling</c><00:04:52.180><c> all</c><00:04:52.570><c> the</c>

00:04:52.890 --> 00:04:52.900 align:start position:0%
editing admin tab and pulling all the
 

00:04:52.900 --> 00:04:54.659 align:start position:0%
editing admin tab and pulling all the
changes<00:04:53.440><c> that</c><00:04:53.650><c> were</c><00:04:53.740><c> made</c><00:04:53.770><c> to</c><00:04:54.130><c> that</c><00:04:54.280><c> branch</c>

00:04:54.659 --> 00:04:54.669 align:start position:0%
changes that were made to that branch
 

00:04:54.669 --> 00:04:59.190 align:start position:0%
changes that were made to that branch
into<00:04:55.270><c> the</c><00:04:55.690><c> master</c><00:04:56.200><c> branch</c><00:04:56.410><c> and</c><00:04:58.050><c> we're</c><00:04:59.050><c> gonna</c>

00:04:59.190 --> 00:04:59.200 align:start position:0%
into the master branch and we're gonna
 

00:04:59.200 --> 00:05:02.430 align:start position:0%
into the master branch and we're gonna
continue<00:04:59.440><c> in</c><00:04:59.830><c> the</c><00:04:59.919><c> next</c><00:05:00.070><c> video</c>

