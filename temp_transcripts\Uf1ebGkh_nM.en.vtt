WEBVTT
Kind: captions
Language: en

00:00:00.730 --> 00:00:05.230 align:start position:0%
 
[Music]

00:00:05.230 --> 00:00:05.240 align:start position:0%
 
 

00:00:05.240 --> 00:00:07.670 align:start position:0%
 
my<00:00:05.400><c> name</c><00:00:05.520><c> is</c><00:00:05.640><c> DK</c><00:00:05.960><c> quk</c><00:00:06.799><c> and</c><00:00:06.960><c> I</c><00:00:07.120><c> work</c><00:00:07.439><c> as</c><00:00:07.560><c> a</c>

00:00:07.670 --> 00:00:07.680 align:start position:0%
my name is <PERSON><PERSON> quk and I work as a
 

00:00:07.680 --> 00:00:09.589 align:start position:0%
my name is <PERSON><PERSON> quk and I work as a
machine<00:00:08.000><c> learning</c><00:00:08.360><c> engineer</c><00:00:09.000><c> in</c><00:00:09.160><c> the</c><00:00:09.280><c> growth</c>

00:00:09.589 --> 00:00:09.599 align:start position:0%
machine learning engineer in the growth
 

00:00:09.599 --> 00:00:12.470 align:start position:0%
machine learning engineer in the growth
team<00:00:09.880><c> at</c><00:00:10.080><c> weights</c><00:00:10.320><c> and</c><00:00:10.639><c> biases</c><00:00:11.639><c> I'm</c><00:00:11.880><c> thrilled</c>

00:00:12.470 --> 00:00:12.480 align:start position:0%
team at weights and biases I'm thrilled
 

00:00:12.480 --> 00:00:14.589 align:start position:0%
team at weights and biases I'm thrilled
to<00:00:12.639><c> be</c><00:00:12.759><c> a</c><00:00:12.960><c> part</c><00:00:13.160><c> of</c><00:00:13.320><c> this</c><00:00:13.519><c> course</c><00:00:13.799><c> on</c><00:00:14.000><c> building</c>

00:00:14.589 --> 00:00:14.599 align:start position:0%
to be a part of this course on building
 

00:00:14.599 --> 00:00:16.470 align:start position:0%
to be a part of this course on building
llm<00:00:15.240><c> powerered</c>

00:00:16.470 --> 00:00:16.480 align:start position:0%
llm powerered
 

00:00:16.480 --> 00:00:18.910 align:start position:0%
llm powerered
applications<00:00:17.480><c> Chris</c><00:00:17.800><c> Alban</c><00:00:18.439><c> director</c><00:00:18.800><c> of</c>

00:00:18.910 --> 00:00:18.920 align:start position:0%
applications Chris Alban director of
 

00:00:18.920 --> 00:00:21.109 align:start position:0%
applications Chris Alban director of
machine<00:00:19.240><c> learning</c><00:00:19.560><c> at</c><00:00:19.720><c> Wikipedia</c><00:00:20.680><c> said</c>

00:00:21.109 --> 00:00:21.119 align:start position:0%
machine learning at Wikipedia said
 

00:00:21.119 --> 00:00:24.230 align:start position:0%
machine learning at Wikipedia said
something<00:00:21.480><c> very</c><00:00:21.760><c> inspiring</c><00:00:22.400><c> not</c><00:00:22.640><c> long</c><00:00:23.119><c> ago</c><00:00:24.119><c> he</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
something very inspiring not long ago he
 

00:00:24.240 --> 00:00:26.109 align:start position:0%
something very inspiring not long ago he
talked<00:00:24.560><c> about</c><00:00:24.760><c> the</c><00:00:24.920><c> unique</c><00:00:25.320><c> opportunity</c><00:00:25.960><c> we</c>

00:00:26.109 --> 00:00:26.119 align:start position:0%
talked about the unique opportunity we
 

00:00:26.119 --> 00:00:27.589 align:start position:0%
talked about the unique opportunity we
have<00:00:26.320><c> now</c><00:00:26.560><c> with</c>

00:00:27.589 --> 00:00:27.599 align:start position:0%
have now with
 

00:00:27.599 --> 00:00:30.470 align:start position:0%
have now with
llms<00:00:28.599><c> the</c><00:00:28.720><c> field</c><00:00:29.199><c> is</c><00:00:29.359><c> being</c><00:00:29.599><c> created</c><00:00:30.199><c> as</c><00:00:30.320><c> we</c>

00:00:30.470 --> 00:00:30.480 align:start position:0%
llms the field is being created as we
 

00:00:30.480 --> 00:00:32.790 align:start position:0%
llms the field is being created as we
speak<00:00:31.480><c> which</c><00:00:31.640><c> means</c><00:00:31.920><c> that</c><00:00:32.079><c> if</c><00:00:32.160><c> you</c><00:00:32.320><c> join</c><00:00:32.559><c> in</c>

00:00:32.790 --> 00:00:32.800 align:start position:0%
speak which means that if you join in
 

00:00:32.800 --> 00:00:34.830 align:start position:0%
speak which means that if you join in
now<00:00:33.320><c> you</c><00:00:33.440><c> can</c><00:00:33.680><c> be</c><00:00:33.920><c> there</c><00:00:34.160><c> from</c><00:00:34.360><c> the</c><00:00:34.559><c> very</c>

00:00:34.830 --> 00:00:34.840 align:start position:0%
now you can be there from the very
 

00:00:34.840 --> 00:00:37.990 align:start position:0%
now you can be there from the very
beginning<00:00:35.399><c> and</c><00:00:35.559><c> become</c><00:00:35.879><c> an</c><00:00:36.480><c> expert</c><00:00:37.480><c> even</c><00:00:37.760><c> the</c>

00:00:37.990 --> 00:00:38.000 align:start position:0%
beginning and become an expert even the
 

00:00:38.000 --> 00:00:39.590 align:start position:0%
beginning and become an expert even the
experts<00:00:38.360><c> in</c><00:00:38.520><c> these</c><00:00:38.640><c> fields</c><00:00:39.079><c> have</c><00:00:39.280><c> only</c>

00:00:39.590 --> 00:00:39.600 align:start position:0%
experts in these fields have only
 

00:00:39.600 --> 00:00:42.110 align:start position:0%
experts in these fields have only
started<00:00:40.079><c> exploring</c><00:00:40.600><c> it</c><00:00:40.800><c> recently</c><00:00:41.760><c> so</c><00:00:41.960><c> the</c>

00:00:42.110 --> 00:00:42.120 align:start position:0%
started exploring it recently so the
 

00:00:42.120 --> 00:00:45.470 align:start position:0%
started exploring it recently so the
barriers<00:00:42.520><c> to</c><00:00:42.719><c> entry</c><00:00:43.120><c> are</c><00:00:43.719><c> low</c><00:00:44.719><c> by</c><00:00:44.920><c> taking</c><00:00:45.280><c> this</c>

00:00:45.470 --> 00:00:45.480 align:start position:0%
barriers to entry are low by taking this
 

00:00:45.480 --> 00:00:48.150 align:start position:0%
barriers to entry are low by taking this
course<00:00:46.160><c> and</c><00:00:46.360><c> starting</c><00:00:46.760><c> to</c><00:00:46.960><c> build</c><00:00:47.360><c> apps</c><00:00:47.960><c> with</c>

00:00:48.150 --> 00:00:48.160 align:start position:0%
course and starting to build apps with
 

00:00:48.160 --> 00:00:50.830 align:start position:0%
course and starting to build apps with
llms<00:00:49.079><c> you'll</c><00:00:49.320><c> have</c><00:00:49.440><c> a</c><00:00:49.640><c> big</c><00:00:49.879><c> Advantage</c><00:00:50.680><c> when</c>

00:00:50.830 --> 00:00:50.840 align:start position:0%
llms you'll have a big Advantage when
 

00:00:50.840 --> 00:00:52.029 align:start position:0%
llms you'll have a big Advantage when
the<00:00:51.039><c> industry</c>

00:00:52.029 --> 00:00:52.039 align:start position:0%
the industry
 

00:00:52.039 --> 00:00:54.510 align:start position:0%
the industry
matures<00:00:53.039><c> if</c><00:00:53.160><c> you're</c><00:00:53.399><c> already</c><00:00:53.760><c> interested</c><00:00:54.280><c> in</c>

00:00:54.510 --> 00:00:54.520 align:start position:0%
matures if you're already interested in
 

00:00:54.520 --> 00:00:57.189 align:start position:0%
matures if you're already interested in
llms<00:00:55.520><c> then</c><00:00:55.719><c> you</c><00:00:56.000><c> probably</c><00:00:56.399><c> don't</c><00:00:56.640><c> need</c><00:00:56.840><c> to</c><00:00:56.960><c> be</c>

00:00:57.189 --> 00:00:57.199 align:start position:0%
llms then you probably don't need to be
 

00:00:57.199 --> 00:01:00.069 align:start position:0%
llms then you probably don't need to be
convinced<00:00:58.039><c> of</c><00:00:58.199><c> their</c><00:00:58.480><c> impact</c><00:00:58.800><c> on</c><00:00:58.960><c> the</c><00:00:59.120><c> world</c>

00:01:00.069 --> 00:01:00.079 align:start position:0%
convinced of their impact on the world
 

00:01:00.079 --> 00:01:02.069 align:start position:0%
convinced of their impact on the world
but<00:01:00.320><c> just</c><00:01:00.480><c> to</c><00:01:00.640><c> show</c><00:01:00.840><c> you</c><00:01:01.039><c> how</c><00:01:01.320><c> pervasive</c><00:01:01.920><c> they</c>

00:01:02.069 --> 00:01:02.079 align:start position:0%
but just to show you how pervasive they
 

00:01:02.079 --> 00:01:05.109 align:start position:0%
but just to show you how pervasive they
are<00:01:02.640><c> check</c><00:01:02.840><c> out</c><00:01:03.079><c> this</c><00:01:03.280><c> Market</c><00:01:03.680><c> map</c><00:01:03.960><c> from</c>

00:01:05.109 --> 00:01:05.119 align:start position:0%
are check out this Market map from
 

00:01:05.119 --> 00:01:08.710 align:start position:0%
are check out this Market map from
seoa<00:01:06.119><c> llms</c><00:01:06.960><c> appear</c><00:01:07.240><c> in</c><00:01:07.520><c> various</c><00:01:08.040><c> Industries</c>

00:01:08.710 --> 00:01:08.720 align:start position:0%
seoa llms appear in various Industries
 

00:01:08.720 --> 00:01:11.870 align:start position:0%
seoa llms appear in various Industries
and<00:01:08.880><c> support</c><00:01:09.240><c> a</c><00:01:09.479><c> wide</c><00:01:09.720><c> range</c><00:01:10.159><c> of</c><00:01:10.680><c> functions</c><00:01:11.680><c> we</c>

00:01:11.870 --> 00:01:11.880 align:start position:0%
and support a wide range of functions we
 

00:01:11.880 --> 00:01:13.710 align:start position:0%
and support a wide range of functions we
at<00:01:12.000><c> weights</c><00:01:12.240><c> and</c><00:01:12.400><c> biases</c><00:01:12.840><c> are</c><00:01:13.080><c> also</c><00:01:13.320><c> in</c><00:01:13.479><c> this</c>

00:01:13.710 --> 00:01:13.720 align:start position:0%
at weights and biases are also in this
 

00:01:13.720 --> 00:01:17.310 align:start position:0%
at weights and biases are also in this
space<00:01:14.520><c> and</c><00:01:14.759><c> have</c><00:01:15.119><c> developed</c><00:01:15.600><c> our</c><00:01:15.960><c> own</c><00:01:16.360><c> llm</c><00:01:16.960><c> Ops</c>

00:01:17.310 --> 00:01:17.320 align:start position:0%
space and have developed our own llm Ops
 

00:01:17.320 --> 00:01:18.190 align:start position:0%
space and have developed our own llm Ops
tool

00:01:18.190 --> 00:01:18.200 align:start position:0%
tool
 

00:01:18.200 --> 00:01:20.950 align:start position:0%
tool
set<00:01:19.200><c> we</c><00:01:19.320><c> want</c><00:01:19.479><c> to</c><00:01:19.720><c> help</c><00:01:19.920><c> you</c><00:01:20.119><c> get</c><00:01:20.320><c> started</c><00:01:20.799><c> on</c>

00:01:20.950 --> 00:01:20.960 align:start position:0%
set we want to help you get started on
 

00:01:20.960 --> 00:01:23.630 align:start position:0%
set we want to help you get started on
the<00:01:21.159><c> right</c><00:01:21.360><c> foot</c><00:01:22.079><c> we</c><00:01:22.280><c> assume</c><00:01:22.799><c> some</c><00:01:23.200><c> Python</c>

00:01:23.630 --> 00:01:23.640 align:start position:0%
the right foot we assume some Python
 

00:01:23.640 --> 00:01:26.230 align:start position:0%
the right foot we assume some Python
programming<00:01:24.240><c> experience</c><00:01:25.200><c> but</c><00:01:25.400><c> we</c><00:01:25.520><c> don't</c><00:01:25.880><c> make</c>

00:01:26.230 --> 00:01:26.240 align:start position:0%
programming experience but we don't make
 

00:01:26.240 --> 00:01:28.630 align:start position:0%
programming experience but we don't make
many<00:01:26.520><c> other</c><00:01:26.799><c> assumptions</c><00:01:27.400><c> about</c><00:01:27.640><c> your</c>

00:01:28.630 --> 00:01:28.640 align:start position:0%
many other assumptions about your
 

00:01:28.640 --> 00:01:30.830 align:start position:0%
many other assumptions about your
background<00:01:29.640><c> where</c><00:01:30.040><c> provide</c><00:01:30.280><c> you</c><00:01:30.400><c> with</c><00:01:30.560><c> key</c>

00:01:30.830 --> 00:01:30.840 align:start position:0%
background where provide you with key
 

00:01:30.840 --> 00:01:33.630 align:start position:0%
background where provide you with key
Concepts<00:01:31.520><c> starting</c><00:01:32.079><c> advice</c><00:01:32.920><c> and</c><00:01:33.079><c> a</c><00:01:33.280><c> process</c>

00:01:33.630 --> 00:01:33.640 align:start position:0%
Concepts starting advice and a process
 

00:01:33.640 --> 00:01:35.990 align:start position:0%
Concepts starting advice and a process
that<00:01:33.759><c> you</c><00:01:33.880><c> can</c><00:01:34.079><c> follow</c><00:01:34.840><c> to</c><00:01:35.040><c> make</c><00:01:35.479><c> continuous</c>

00:01:35.990 --> 00:01:36.000 align:start position:0%
that you can follow to make continuous
 

00:01:36.000 --> 00:01:37.990 align:start position:0%
that you can follow to make continuous
improvements<00:01:36.799><c> to</c><00:01:37.000><c> your</c>

00:01:37.990 --> 00:01:38.000 align:start position:0%
improvements to your
 

00:01:38.000 --> 00:01:40.550 align:start position:0%
improvements to your
applications<00:01:39.000><c> we</c><00:01:39.159><c> believe</c><00:01:39.520><c> that</c><00:01:39.720><c> one</c><00:01:40.000><c> day</c><00:01:40.399><c> you</c>

00:01:40.550 --> 00:01:40.560 align:start position:0%
applications we believe that one day you
 

00:01:40.560 --> 00:01:43.789 align:start position:0%
applications we believe that one day you
could<00:01:40.759><c> even</c><00:01:41.000><c> teach</c><00:01:41.439><c> your</c><00:01:41.640><c> own</c><00:01:42.079><c> llm</c>

00:01:43.789 --> 00:01:43.799 align:start position:0%
could even teach your own llm
 

00:01:43.799 --> 00:01:46.389 align:start position:0%
could even teach your own llm
class<00:01:44.799><c> during</c><00:01:45.200><c> this</c><00:01:45.399><c> course</c><00:01:45.880><c> we'll</c><00:01:46.040><c> show</c><00:01:46.240><c> you</c>

00:01:46.389 --> 00:01:46.399 align:start position:0%
class during this course we'll show you
 

00:01:46.399 --> 00:01:48.910 align:start position:0%
class during this course we'll show you
the<00:01:46.560><c> prompt</c><00:01:46.920><c> engineering</c><00:01:47.520><c> workflow</c><00:01:48.520><c> and</c><00:01:48.719><c> how</c>

00:01:48.910 --> 00:01:48.920 align:start position:0%
the prompt engineering workflow and how
 

00:01:48.920 --> 00:01:51.350 align:start position:0%
the prompt engineering workflow and how
weights<00:01:49.159><c> and</c><00:01:49.320><c> biases</c><00:01:49.759><c> supports</c><00:01:50.200><c> it</c><00:01:51.159><c> we'll</c>

00:01:51.350 --> 00:01:51.360 align:start position:0%
weights and biases supports it we'll
 

00:01:51.360 --> 00:01:53.550 align:start position:0%
weights and biases supports it we'll
share<00:01:51.719><c> with</c><00:01:51.880><c> you</c><00:01:52.079><c> our</c><00:01:52.439><c> very</c><00:01:52.640><c> own</c><00:01:53.040><c> app</c><00:01:53.399><c> that</c>

00:01:53.550 --> 00:01:53.560 align:start position:0%
share with you our very own app that
 

00:01:53.560 --> 00:01:55.789 align:start position:0%
share with you our very own app that
helps<00:01:53.920><c> weights</c><00:01:54.119><c> and</c><00:01:54.280><c> biases</c><00:01:54.719><c> users</c><00:01:55.520><c> get</c>

00:01:55.789 --> 00:01:55.799 align:start position:0%
helps weights and biases users get
 

00:01:55.799 --> 00:01:58.830 align:start position:0%
helps weights and biases users get
support<00:01:56.479><c> via</c><00:01:56.759><c> our</c><00:01:57.039><c> Discord</c><00:01:57.560><c> Community</c>

00:01:58.830 --> 00:01:58.840 align:start position:0%
support via our Discord Community
 

00:01:58.840 --> 00:02:01.310 align:start position:0%
support via our Discord Community
server<00:01:59.840><c> will</c><00:02:00.000><c> take</c><00:02:00.159><c> steps</c><00:02:00.600><c> together</c><00:02:00.960><c> towards</c>

00:02:01.310 --> 00:02:01.320 align:start position:0%
server will take steps together towards
 

00:02:01.320 --> 00:02:03.630 align:start position:0%
server will take steps together towards
building<00:02:01.680><c> a</c><00:02:01.840><c> similar</c><00:02:02.360><c> application</c><00:02:03.360><c> and</c>

00:02:03.630 --> 00:02:03.640 align:start position:0%
building a similar application and
 

00:02:03.640 --> 00:02:05.670 align:start position:0%
building a similar application and
provide<00:02:03.920><c> you</c><00:02:04.039><c> with</c><00:02:04.159><c> a</c><00:02:04.360><c> process</c><00:02:04.640><c> to</c><00:02:04.840><c> follow</c><00:02:05.280><c> to</c>

00:02:05.670 --> 00:02:05.680 align:start position:0%
provide you with a process to follow to
 

00:02:05.680 --> 00:02:07.589 align:start position:0%
provide you with a process to follow to
improve<00:02:06.079><c> your</c><00:02:06.320><c> app</c>

00:02:07.589 --> 00:02:07.599 align:start position:0%
improve your app
 

00:02:07.599 --> 00:02:10.070 align:start position:0%
improve your app
continuously<00:02:08.599><c> lastly</c><00:02:09.319><c> we</c><00:02:09.479><c> also</c><00:02:09.679><c> invite</c><00:02:09.959><c> you</c>

00:02:10.070 --> 00:02:10.080 align:start position:0%
continuously lastly we also invite you
 

00:02:10.080 --> 00:02:12.630 align:start position:0%
continuously lastly we also invite you
to<00:02:10.280><c> join</c><00:02:10.640><c> our</c><00:02:10.959><c> Discord</c><00:02:11.480><c> Community</c><00:02:12.400><c> where</c><00:02:12.560><c> you</c>

00:02:12.630 --> 00:02:12.640 align:start position:0%
to join our Discord Community where you
 

00:02:12.640 --> 00:02:14.869 align:start position:0%
to join our Discord Community where you
can<00:02:12.840><c> chat</c><00:02:13.080><c> with</c><00:02:13.280><c> fellow</c><00:02:13.680><c> participants</c><00:02:14.560><c> and</c>

00:02:14.869 --> 00:02:14.879 align:start position:0%
can chat with fellow participants and
 

00:02:14.879 --> 00:02:18.589 align:start position:0%
can chat with fellow participants and
ask<00:02:15.120><c> us</c><00:02:15.360><c> any</c><00:02:15.599><c> questions</c><00:02:16.280><c> you</c><00:02:16.440><c> may</c><00:02:17.239><c> have</c><00:02:18.239><c> we're</c>

00:02:18.589 --> 00:02:18.599 align:start position:0%
ask us any questions you may have we're
 

00:02:18.599 --> 00:02:20.790 align:start position:0%
ask us any questions you may have we're
excited<00:02:19.040><c> to</c><00:02:19.239><c> get</c><00:02:19.440><c> started</c><00:02:20.000><c> and</c><00:02:20.160><c> show</c><00:02:20.440><c> you</c><00:02:20.680><c> how</c>

00:02:20.790 --> 00:02:20.800 align:start position:0%
excited to get started and show you how
 

00:02:20.800 --> 00:02:23.790 align:start position:0%
excited to get started and show you how
to<00:02:21.000><c> build</c><00:02:21.319><c> LL</c><00:02:21.640><c> owered</c><00:02:22.480><c> applications</c><00:02:23.480><c> in</c><00:02:23.599><c> the</c>

00:02:23.790 --> 00:02:23.800 align:start position:0%
to build LL owered applications in the
 

00:02:23.800 --> 00:02:25.710 align:start position:0%
to build LL owered applications in the
next<00:02:24.080><c> video</c><00:02:24.400><c> we'll</c><00:02:24.640><c> jump</c><00:02:25.000><c> right</c><00:02:25.200><c> into</c><00:02:25.480><c> our</c>

00:02:25.710 --> 00:02:25.720 align:start position:0%
next video we'll jump right into our
 

00:02:25.720 --> 00:02:28.270 align:start position:0%
next video we'll jump right into our
Discord<00:02:26.080><c> server</c><00:02:26.840><c> and</c><00:02:27.000><c> show</c><00:02:27.200><c> you</c><00:02:27.360><c> an</c><00:02:27.560><c> example</c>

00:02:28.270 --> 00:02:28.280 align:start position:0%
Discord server and show you an example
 

00:02:28.280 --> 00:02:39.229 align:start position:0%
Discord server and show you an example
of<00:02:28.440><c> an</c><00:02:28.640><c> llm</c><00:02:29.200><c> powered</c>

00:02:39.229 --> 00:02:39.239 align:start position:0%
 
 

00:02:39.239 --> 00:02:42.239 align:start position:0%
 
ation

