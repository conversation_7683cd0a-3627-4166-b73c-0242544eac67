WEBVTT
Kind: captions
Language: en

00:00:01.360 --> 00:00:03.389 align:start position:0%
 
what<00:00:01.520><c> you</c><00:00:01.640><c> need</c><00:00:01.839><c> to</c><00:00:01.959><c> know</c><00:00:02.280><c> about</c><00:00:02.520><c> the</c><00:00:02.679><c> XZ</c>

00:00:03.389 --> 00:00:03.399 align:start position:0%
what you need to know about the XZ
 

00:00:03.399 --> 00:00:05.789 align:start position:0%
what you need to know about the XZ
backdoor<00:00:04.279><c> new</c><00:00:04.520><c> releases</c><00:00:04.920><c> from</c><00:00:05.120><c> bun</c><00:00:05.480><c> and</c>

00:00:05.789 --> 00:00:05.799 align:start position:0%
backdoor new releases from bun and
 

00:00:05.799 --> 00:00:09.110 align:start position:0%
backdoor new releases from bun and
babylonjs<00:00:06.799><c> GitHub</c><00:00:07.160><c> co-pilot</c><00:00:07.600><c> CLI</c><00:00:08.120><c> goes</c><00:00:08.400><c> GA</c>

00:00:09.110 --> 00:00:09.120 align:start position:0%
babylonjs GitHub co-pilot CLI goes GA
 

00:00:09.120 --> 00:00:11.430 align:start position:0%
babylonjs GitHub co-pilot CLI goes GA
and<00:00:09.400><c> a</c><00:00:09.519><c> new</c><00:00:09.679><c> Beyonce</c><00:00:10.120><c> album</c><00:00:10.960><c> All</c><00:00:11.160><c> That</c><00:00:11.280><c> and</c>

00:00:11.430 --> 00:00:11.440 align:start position:0%
and a new Beyonce album All That and
 

00:00:11.440 --> 00:00:17.550 align:start position:0%
and a new Beyonce album All That and
More<00:00:11.679><c> on</c><00:00:11.880><c> this</c><00:00:12.040><c> episode</c><00:00:12.480><c> of</c><00:00:12.840><c> the</c>

00:00:17.550 --> 00:00:17.560 align:start position:0%
 
 

00:00:17.560 --> 00:00:19.790 align:start position:0%
 
download<00:00:18.560><c> welcome</c><00:00:18.840><c> back</c><00:00:18.960><c> to</c><00:00:19.119><c> another</c><00:00:19.359><c> episode</c>

00:00:19.790 --> 00:00:19.800 align:start position:0%
download welcome back to another episode
 

00:00:19.800 --> 00:00:21.670 align:start position:0%
download welcome back to another episode
of<00:00:19.960><c> the</c><00:00:20.119><c> download</c><00:00:20.720><c> I'm</c><00:00:20.840><c> your</c><00:00:21.000><c> host</c><00:00:21.279><c> Christina</c>

00:00:21.670 --> 00:00:21.680 align:start position:0%
of the download I'm your host Christina
 

00:00:21.680 --> 00:00:23.269 align:start position:0%
of the download I'm your host Christina
Warren<00:00:22.039><c> senior</c><00:00:22.320><c> developer</c><00:00:22.680><c> Advocate</c><00:00:23.039><c> at</c>

00:00:23.269 --> 00:00:23.279 align:start position:0%
Warren senior developer Advocate at
 

00:00:23.279 --> 00:00:25.150 align:start position:0%
Warren senior developer Advocate at
GitHub<00:00:23.800><c> and</c><00:00:24.279><c> this</c><00:00:24.400><c> is</c><00:00:24.519><c> the</c><00:00:24.640><c> show</c><00:00:24.840><c> where</c><00:00:25.000><c> we</c>

00:00:25.150 --> 00:00:25.160 align:start position:0%
GitHub and this is the show where we
 

00:00:25.160 --> 00:00:26.870 align:start position:0%
GitHub and this is the show where we
cover<00:00:25.400><c> the</c><00:00:25.480><c> latest</c><00:00:25.760><c> developer</c><00:00:26.119><c> news</c><00:00:26.480><c> and</c><00:00:26.640><c> open</c>

00:00:26.870 --> 00:00:26.880 align:start position:0%
cover the latest developer news and open
 

00:00:26.880 --> 00:00:28.589 align:start position:0%
cover the latest developer news and open
source<00:00:27.199><c> Projects</c><00:00:28.080><c> please</c><00:00:28.320><c> like</c><00:00:28.439><c> And</c>

00:00:28.589 --> 00:00:28.599 align:start position:0%
source Projects please like And
 

00:00:28.599 --> 00:00:30.630 align:start position:0%
source Projects please like And
subscribe<00:00:29.519><c> it</c><00:00:29.640><c> is</c><00:00:30.039><c> been</c><00:00:30.160><c> a</c><00:00:30.199><c> minute</c><00:00:30.439><c> since</c>

00:00:30.630 --> 00:00:30.640 align:start position:0%
subscribe it is been a minute since
 

00:00:30.640 --> 00:00:32.870 align:start position:0%
subscribe it is been a minute since
we've<00:00:30.800><c> had</c><00:00:30.920><c> a</c><00:00:31.039><c> show</c><00:00:31.560><c> spring</c><00:00:31.920><c> is</c><00:00:32.200><c> here</c><00:00:32.719><c> there's</c>

00:00:32.870 --> 00:00:32.880 align:start position:0%
we've had a show spring is here there's
 

00:00:32.880 --> 00:00:35.310 align:start position:0%
we've had a show spring is here there's
a<00:00:33.000><c> new</c><00:00:33.160><c> Beyonce</c><00:00:33.559><c> album</c><00:00:34.120><c> more</c><00:00:34.320><c> on</c><00:00:34.480><c> that</c><00:00:34.640><c> later</c>

00:00:35.310 --> 00:00:35.320 align:start position:0%
a new Beyonce album more on that later
 

00:00:35.320 --> 00:00:36.950 align:start position:0%
a new Beyonce album more on that later
and<00:00:35.520><c> there's</c><00:00:35.760><c> tons</c><00:00:35.960><c> and</c><00:00:36.120><c> tons</c><00:00:36.399><c> of</c><00:00:36.520><c> news</c><00:00:36.800><c> to</c>

00:00:36.950 --> 00:00:36.960 align:start position:0%
and there's tons and tons of news to
 

00:00:36.960 --> 00:00:39.030 align:start position:0%
and there's tons and tons of news to
cover<00:00:37.559><c> uh</c><00:00:37.680><c> and</c><00:00:37.760><c> my</c><00:00:37.960><c> shirt</c><00:00:38.200><c> this</c><00:00:38.320><c> week</c><00:00:38.640><c> is</c><00:00:38.960><c> the</c>

00:00:39.030 --> 00:00:39.040 align:start position:0%
cover uh and my shirt this week is the
 

00:00:39.040 --> 00:00:40.750 align:start position:0%
cover uh and my shirt this week is the
band<00:00:39.320><c> boy</c><00:00:39.520><c> genius</c><00:00:40.079><c> and</c><00:00:40.239><c> they</c><00:00:40.320><c> might</c><00:00:40.480><c> be</c><00:00:40.600><c> on</c>

00:00:40.750 --> 00:00:40.760 align:start position:0%
band boy genius and they might be on
 

00:00:40.760 --> 00:00:43.430 align:start position:0%
band boy genius and they might be on
Hiatus<00:00:41.360><c> but</c><00:00:41.559><c> they</c><00:00:41.640><c> are</c><00:00:41.840><c> forever</c><00:00:42.200><c> in</c><00:00:42.360><c> my</c><00:00:42.520><c> heart</c>

00:00:43.430 --> 00:00:43.440 align:start position:0%
Hiatus but they are forever in my heart
 

00:00:43.440 --> 00:00:45.350 align:start position:0%
Hiatus but they are forever in my heart
all<00:00:43.600><c> right</c><00:00:43.840><c> first</c><00:00:44.239><c> let's</c><00:00:44.480><c> talk</c><00:00:44.640><c> about</c><00:00:44.840><c> a</c><00:00:44.960><c> few</c>

00:00:45.350 --> 00:00:45.360 align:start position:0%
all right first let's talk about a few
 

00:00:45.360 --> 00:00:47.310 align:start position:0%
all right first let's talk about a few
GitHub<00:00:45.719><c> updates</c><00:00:46.399><c> so</c><00:00:46.719><c> the</c><00:00:46.840><c> first</c><00:00:47.000><c> thing</c><00:00:47.120><c> I</c><00:00:47.199><c> want</c>

00:00:47.310 --> 00:00:47.320 align:start position:0%
GitHub updates so the first thing I want
 

00:00:47.320 --> 00:00:49.549 align:start position:0%
GitHub updates so the first thing I want
to<00:00:47.559><c> mention</c><00:00:47.960><c> is</c><00:00:48.120><c> that</c><00:00:48.360><c> the</c><00:00:48.520><c> GitHub</c><00:00:48.920><c> co-pilot</c>

00:00:49.549 --> 00:00:49.559 align:start position:0%
to mention is that the GitHub co-pilot
 

00:00:49.559 --> 00:00:53.150 align:start position:0%
to mention is that the GitHub co-pilot
CLI<00:00:50.440><c> is</c><00:00:50.640><c> now</c><00:00:50.879><c> generally</c><00:00:51.320><c> available</c><00:00:52.120><c> yay</c><00:00:52.879><c> so</c>

00:00:53.150 --> 00:00:53.160 align:start position:0%
CLI is now generally available yay so
 

00:00:53.160 --> 00:00:55.069 align:start position:0%
CLI is now generally available yay so
this<00:00:53.320><c> means</c><00:00:53.800><c> you</c><00:00:54.000><c> can</c><00:00:54.199><c> now</c><00:00:54.399><c> use</c><00:00:54.760><c> gith</c><00:00:54.960><c> up</c>

00:00:55.069 --> 00:00:55.079 align:start position:0%
this means you can now use gith up
 

00:00:55.079 --> 00:00:56.990 align:start position:0%
this means you can now use gith up
co-pilot<00:00:55.640><c> in</c><00:00:55.760><c> your</c><00:00:55.960><c> terminal</c><00:00:56.640><c> and</c><00:00:56.840><c> it's</c>

00:00:56.990 --> 00:00:57.000 align:start position:0%
co-pilot in your terminal and it's
 

00:00:57.000 --> 00:00:59.189 align:start position:0%
co-pilot in your terminal and it's
available<00:00:57.320><c> for</c><00:00:57.520><c> all</c><00:00:57.719><c> users</c><00:00:58.440><c> uh</c><00:00:58.600><c> business</c><00:00:59.079><c> uh</c>

00:00:59.189 --> 00:00:59.199 align:start position:0%
available for all users uh business uh
 

00:00:59.199 --> 00:01:01.430 align:start position:0%
available for all users uh business uh
Enterprise<00:00:59.640><c> and</c><00:01:00.000><c> individuals</c><00:01:00.960><c> my</c><00:01:01.120><c> favorite</c>

00:01:01.430 --> 00:01:01.440 align:start position:0%
Enterprise and individuals my favorite
 

00:01:01.440 --> 00:01:03.830 align:start position:0%
Enterprise and individuals my favorite
use<00:01:01.760><c> case</c><00:01:01.960><c> for</c><00:01:02.239><c> the</c><00:01:02.399><c> co-pilot</c><00:01:02.879><c> CLI</c><00:01:03.399><c> is</c><00:01:03.519><c> to</c><00:01:03.719><c> use</c>

00:01:03.830 --> 00:01:03.840 align:start position:0%
use case for the co-pilot CLI is to use
 

00:01:03.840 --> 00:01:06.710 align:start position:0%
use case for the co-pilot CLI is to use
it<00:01:03.960><c> to</c><00:01:04.119><c> do</c><00:01:04.400><c> stuff</c><00:01:04.720><c> in</c><00:01:05.240><c> ffmpeg</c><00:01:06.240><c> without</c><00:01:06.520><c> having</c>

00:01:06.710 --> 00:01:06.720 align:start position:0%
it to do stuff in ffmpeg without having
 

00:01:06.720 --> 00:01:08.870 align:start position:0%
it to do stuff in ffmpeg without having
to<00:01:07.000><c> read</c><00:01:07.240><c> the</c><00:01:07.400><c> manual</c><00:01:07.880><c> and</c><00:01:08.119><c> try</c><00:01:08.360><c> and</c><00:01:08.520><c> fail</c><00:01:08.759><c> a</c>

00:01:08.870 --> 00:01:08.880 align:start position:0%
to read the manual and try and fail a
 

00:01:08.880 --> 00:01:11.149 align:start position:0%
to read the manual and try and fail a
bunch<00:01:09.040><c> of</c><00:01:09.200><c> times</c><00:01:09.520><c> it's</c><00:01:09.680><c> very</c><00:01:09.920><c> convenient</c><00:01:10.920><c> also</c>

00:01:11.149 --> 00:01:11.159 align:start position:0%
bunch of times it's very convenient also
 

00:01:11.159 --> 00:01:12.910 align:start position:0%
bunch of times it's very convenient also
speaking<00:01:11.439><c> of</c><00:01:11.560><c> GitHub</c><00:01:11.880><c> co-pilot</c><00:01:12.520><c> my</c><00:01:12.640><c> girl</c>

00:01:12.910 --> 00:01:12.920 align:start position:0%
speaking of GitHub co-pilot my girl
 

00:01:12.920 --> 00:01:15.670 align:start position:0%
speaking of GitHub co-pilot my girl
cadesa<00:01:13.400><c> care</c><00:01:13.720><c> wrote</c><00:01:14.080><c> a</c><00:01:14.360><c> great</c><00:01:14.720><c> blog</c><00:01:15.119><c> post</c><00:01:15.520><c> on</c>

00:01:15.670 --> 00:01:15.680 align:start position:0%
cadesa care wrote a great blog post on
 

00:01:15.680 --> 00:01:17.550 align:start position:0%
cadesa care wrote a great blog post on
the<00:01:15.840><c> GitHub</c><00:01:16.200><c> blog</c><00:01:16.720><c> offering</c><00:01:17.080><c> some</c><00:01:17.280><c> really</c>

00:01:17.550 --> 00:01:17.560 align:start position:0%
the GitHub blog offering some really
 

00:01:17.560 --> 00:01:19.030 align:start position:0%
the GitHub blog offering some really
comprehensive<00:01:18.159><c> tips</c><00:01:18.360><c> and</c><00:01:18.520><c> tricks</c><00:01:18.840><c> for</c>

00:01:19.030 --> 00:01:19.040 align:start position:0%
comprehensive tips and tricks for
 

00:01:19.040 --> 00:01:20.429 align:start position:0%
comprehensive tips and tricks for
getting<00:01:19.320><c> the</c><00:01:19.439><c> most</c><00:01:19.720><c> out</c><00:01:19.880><c> of</c><00:01:20.000><c> the</c><00:01:20.119><c> GitHub</c>

00:01:20.429 --> 00:01:20.439 align:start position:0%
getting the most out of the GitHub
 

00:01:20.439 --> 00:01:22.830 align:start position:0%
getting the most out of the GitHub
co-pilot<00:01:21.240><c> uh</c><00:01:21.439><c> in</c><00:01:21.560><c> your</c><00:01:21.720><c> IDE</c><00:01:22.439><c> and</c><00:01:22.520><c> so</c><00:01:22.640><c> I've</c><00:01:22.759><c> got</c>

00:01:22.830 --> 00:01:22.840 align:start position:0%
co-pilot uh in your IDE and so I've got
 

00:01:22.840 --> 00:01:24.310 align:start position:0%
co-pilot uh in your IDE and so I've got
a<00:01:22.960><c> link</c><00:01:23.159><c> to</c><00:01:23.360><c> that</c><00:01:23.520><c> in</c><00:01:23.600><c> the</c><00:01:23.680><c> show</c><00:01:23.920><c> notes</c><00:01:24.119><c> in</c><00:01:24.240><c> the</c>

00:01:24.310 --> 00:01:24.320 align:start position:0%
a link to that in the show notes in the
 

00:01:24.320 --> 00:01:26.109 align:start position:0%
a link to that in the show notes in the
description<00:01:24.720><c> down</c><00:01:24.920><c> below</c><00:01:25.560><c> as</c><00:01:25.680><c> well</c><00:01:25.799><c> as</c><00:01:25.920><c> more</c>

00:01:26.109 --> 00:01:26.119 align:start position:0%
description down below as well as more
 

00:01:26.119 --> 00:01:28.390 align:start position:0%
description down below as well as more
info<00:01:26.400><c> on</c><00:01:26.520><c> the</c><00:01:26.680><c> GitHub</c><00:01:26.960><c> co-pilot</c><00:01:27.360><c> CLI</c><00:01:28.079><c> and</c><00:01:28.240><c> some</c>

00:01:28.390 --> 00:01:28.400 align:start position:0%
info on the GitHub co-pilot CLI and some
 

00:01:28.400 --> 00:01:29.910 align:start position:0%
info on the GitHub co-pilot CLI and some
other<00:01:28.600><c> news</c><00:01:28.960><c> we've</c><00:01:29.119><c> announced</c><00:01:29.479><c> that</c><00:01:29.600><c> Co</c>

00:01:29.910 --> 00:01:29.920 align:start position:0%
other news we've announced that Co
 

00:01:29.920 --> 00:01:31.950 align:start position:0%
other news we've announced that Co
scanning<00:01:30.240><c> autofix</c><00:01:31.119><c> powered</c><00:01:31.439><c> by</c><00:01:31.600><c> GitHub</c>

00:01:31.950 --> 00:01:31.960 align:start position:0%
scanning autofix powered by GitHub
 

00:01:31.960 --> 00:01:34.389 align:start position:0%
scanning autofix powered by GitHub
co-pilot<00:01:32.479><c> and</c><00:01:32.720><c> code</c><00:01:33.040><c> ql</c><00:01:33.640><c> is</c><00:01:33.799><c> now</c><00:01:33.960><c> in</c><00:01:34.159><c> public</c>

00:01:34.389 --> 00:01:34.399 align:start position:0%
co-pilot and code ql is now in public
 

00:01:34.399 --> 00:01:36.429 align:start position:0%
co-pilot and code ql is now in public
beta<00:01:34.799><c> for</c><00:01:35.159><c> GitHub</c><00:01:35.520><c> Advanced</c><00:01:36.000><c> security</c>

00:01:36.429 --> 00:01:36.439 align:start position:0%
beta for GitHub Advanced security
 

00:01:36.439 --> 00:01:39.030 align:start position:0%
beta for GitHub Advanced security
customers<00:01:37.360><c> and</c><00:01:37.560><c> so</c><00:01:38.119><c> how</c><00:01:38.320><c> the</c><00:01:38.479><c> Cod</c><00:01:38.720><c> scanning</c>

00:01:39.030 --> 00:01:39.040 align:start position:0%
customers and so how the Cod scanning
 

00:01:39.040 --> 00:01:41.030 align:start position:0%
customers and so how the Cod scanning
autofix<00:01:39.600><c> works</c><00:01:40.159><c> is</c><00:01:40.280><c> that</c><00:01:40.439><c> it</c><00:01:40.560><c> covers</c><00:01:40.880><c> more</c>

00:01:41.030 --> 00:01:41.040 align:start position:0%
autofix works is that it covers more
 

00:01:41.040 --> 00:01:43.670 align:start position:0%
autofix works is that it covers more
than<00:01:41.200><c> 90%</c><00:01:41.960><c> of</c><00:01:42.119><c> alert</c><00:01:42.520><c> types</c><00:01:43.000><c> in</c><00:01:43.200><c> JavaScript</c>

00:01:43.670 --> 00:01:43.680 align:start position:0%
than 90% of alert types in JavaScript
 

00:01:43.680 --> 00:01:46.389 align:start position:0%
than 90% of alert types in JavaScript
typescript<00:01:44.360><c> Java</c><00:01:44.799><c> and</c><00:01:45.079><c> Python</c><00:01:45.880><c> and</c><00:01:46.079><c> basically</c>

00:01:46.389 --> 00:01:46.399 align:start position:0%
typescript Java and Python and basically
 

00:01:46.399 --> 00:01:48.870 align:start position:0%
typescript Java and Python and basically
it'll<00:01:46.600><c> deliver</c><00:01:46.920><c> a</c><00:01:47.040><c> code</c><00:01:47.320><c> suggestion</c><00:01:47.920><c> shown</c><00:01:48.799><c> U</c>

00:01:48.870 --> 00:01:48.880 align:start position:0%
it'll deliver a code suggestion shown U
 

00:01:48.880 --> 00:01:50.709 align:start position:0%
it'll deliver a code suggestion shown U
to<00:01:49.040><c> basically</c><00:01:49.439><c> help</c><00:01:49.640><c> you</c><00:01:49.920><c> fix</c><00:01:50.280><c> more</c><00:01:50.479><c> than</c>

00:01:50.709 --> 00:01:50.719 align:start position:0%
to basically help you fix more than
 

00:01:50.719 --> 00:01:53.190 align:start position:0%
to basically help you fix more than
2third<00:01:51.719><c> of</c><00:01:51.920><c> of</c><00:01:52.079><c> bugs</c><00:01:52.600><c> U</c><00:01:52.719><c> or</c><00:01:52.920><c> found</c>

00:01:53.190 --> 00:01:53.200 align:start position:0%
2third of of bugs U or found
 

00:01:53.200 --> 00:01:54.709 align:start position:0%
2third of of bugs U or found
vulnerabilities<00:01:54.079><c> with</c><00:01:54.240><c> little</c><00:01:54.439><c> or</c><00:01:54.560><c> no</c>

00:01:54.709 --> 00:01:54.719 align:start position:0%
vulnerabilities with little or no
 

00:01:54.719 --> 00:01:56.550 align:start position:0%
vulnerabilities with little or no
editing<00:01:55.200><c> so</c><00:01:55.360><c> the</c><00:01:55.520><c> idea</c><00:01:55.920><c> is</c><00:01:56.079><c> basically</c><00:01:56.439><c> that</c>

00:01:56.550 --> 00:01:56.560 align:start position:0%
editing so the idea is basically that
 

00:01:56.560 --> 00:01:58.029 align:start position:0%
editing so the idea is basically that
your<00:01:56.680><c> code</c><00:01:56.920><c> gets</c><00:01:57.079><c> scanned</c><00:01:57.799><c> if</c><00:01:57.920><c> a</c>

00:01:58.029 --> 00:01:58.039 align:start position:0%
your code gets scanned if a
 

00:01:58.039 --> 00:01:59.910 align:start position:0%
your code gets scanned if a
vulnerability<00:01:58.600><c> is</c><00:01:58.719><c> found</c><00:01:59.159><c> in</c><00:01:59.280><c> a</c><00:01:59.399><c> supported</c>

00:01:59.910 --> 00:01:59.920 align:start position:0%
vulnerability is found in a supported
 

00:01:59.920 --> 00:02:01.310 align:start position:0%
vulnerability is found in a supported
language<00:02:00.360><c> you're</c><00:02:00.520><c> going</c><00:02:00.600><c> to</c><00:02:00.719><c> get</c><00:02:00.840><c> an</c><00:02:00.960><c> alert</c>

00:02:01.310 --> 00:02:01.320 align:start position:0%
language you're going to get an alert
 

00:02:01.320 --> 00:02:03.670 align:start position:0%
language you're going to get an alert
and<00:02:01.479><c> fix</c><00:02:01.799><c> suggestions</c><00:02:02.439><c> that</c><00:02:02.560><c> are</c><00:02:03.200><c> um</c>

00:02:03.670 --> 00:02:03.680 align:start position:0%
and fix suggestions that are um
 

00:02:03.680 --> 00:02:05.870 align:start position:0%
and fix suggestions that are um
explained<00:02:04.039><c> in</c><00:02:04.159><c> natural</c><00:02:04.520><c> language</c><00:02:05.360><c> uh</c><00:02:05.560><c> what</c>

00:02:05.870 --> 00:02:05.880 align:start position:0%
explained in natural language uh what
 

00:02:05.880 --> 00:02:08.029 align:start position:0%
explained in natural language uh what
what<00:02:06.000><c> the</c><00:02:06.159><c> fix</c><00:02:06.840><c> is</c><00:02:07.240><c> and</c><00:02:07.320><c> then</c><00:02:07.439><c> you</c><00:02:07.520><c> can</c><00:02:07.680><c> preview</c>

00:02:08.029 --> 00:02:08.039 align:start position:0%
what the fix is and then you can preview
 

00:02:08.039 --> 00:02:09.469 align:start position:0%
what the fix is and then you can preview
the<00:02:08.119><c> code</c><00:02:08.360><c> suggestion</c><00:02:08.879><c> you</c><00:02:08.959><c> can</c><00:02:09.080><c> choose</c><00:02:09.319><c> to</c>

00:02:09.469 --> 00:02:09.479 align:start position:0%
the code suggestion you can choose to
 

00:02:09.479 --> 00:02:12.150 align:start position:0%
the code suggestion you can choose to
edit<00:02:09.840><c> accept</c><00:02:10.440><c> or</c><00:02:10.640><c> dismiss</c><00:02:11.039><c> it</c><00:02:11.560><c> and</c><00:02:11.800><c> then</c><00:02:12.000><c> you</c>

00:02:12.150 --> 00:02:12.160 align:start position:0%
edit accept or dismiss it and then you
 

00:02:12.160 --> 00:02:14.550 align:start position:0%
edit accept or dismiss it and then you
can<00:02:12.680><c> uh</c><00:02:12.800><c> commit</c><00:02:13.080><c> those</c><00:02:13.280><c> changes</c><00:02:13.640><c> to</c><00:02:13.800><c> your</c><00:02:14.000><c> file</c>

00:02:14.550 --> 00:02:14.560 align:start position:0%
can uh commit those changes to your file
 

00:02:14.560 --> 00:02:16.229 align:start position:0%
can uh commit those changes to your file
it<00:02:14.680><c> can</c><00:02:14.800><c> even</c><00:02:15.000><c> go</c><00:02:15.160><c> into</c><00:02:15.360><c> multiple</c><00:02:15.680><c> files</c><00:02:16.080><c> and</c>

00:02:16.229 --> 00:02:16.239 align:start position:0%
it can even go into multiple files and
 

00:02:16.239 --> 00:02:18.190 align:start position:0%
it can even go into multiple files and
inde<00:02:16.440><c> dependencies</c><00:02:16.879><c> of</c><00:02:16.959><c> your</c><00:02:17.200><c> project</c><00:02:18.040><c> and</c>

00:02:18.190 --> 00:02:18.200 align:start position:0%
inde dependencies of your project and
 

00:02:18.200 --> 00:02:20.270 align:start position:0%
inde dependencies of your project and
you're<00:02:18.400><c> done</c><00:02:19.280><c> um</c><00:02:19.519><c> there</c><00:02:19.640><c> are</c><00:02:19.720><c> more</c><00:02:19.879><c> details</c><00:02:20.200><c> in</c>

00:02:20.270 --> 00:02:20.280 align:start position:0%
you're done um there are more details in
 

00:02:20.280 --> 00:02:21.869 align:start position:0%
you're done um there are more details in
the<00:02:20.400><c> blog</c><00:02:20.640><c> post</c><00:02:20.879><c> linked</c><00:02:21.160><c> down</c><00:02:21.319><c> below</c><00:02:21.640><c> as</c><00:02:21.720><c> well</c>

00:02:21.869 --> 00:02:21.879 align:start position:0%
the blog post linked down below as well
 

00:02:21.879 --> 00:02:23.589 align:start position:0%
the blog post linked down below as well
as<00:02:22.000><c> in</c><00:02:22.080><c> our</c><00:02:22.239><c> code</c><00:02:22.519><c> documentation</c><00:02:23.200><c> so</c><00:02:23.360><c> be</c><00:02:23.480><c> sure</c>

00:02:23.589 --> 00:02:23.599 align:start position:0%
as in our code documentation so be sure
 

00:02:23.599 --> 00:02:25.589 align:start position:0%
as in our code documentation so be sure
to<00:02:23.720><c> check</c><00:02:24.040><c> that</c><00:02:24.200><c> out</c><00:02:24.879><c> this</c><00:02:25.000><c> is</c><00:02:25.120><c> something</c><00:02:25.400><c> that</c>

00:02:25.589 --> 00:02:25.599 align:start position:0%
to check that out this is something that
 

00:02:25.599 --> 00:02:26.910 align:start position:0%
to check that out this is something that
I<00:02:25.840><c> I</c><00:02:25.959><c> hope</c><00:02:26.120><c> will</c><00:02:26.239><c> lead</c><00:02:26.480><c> to</c><00:02:26.680><c> less</c>

00:02:26.910 --> 00:02:26.920 align:start position:0%
I I hope will lead to less
 

00:02:26.920 --> 00:02:28.830 align:start position:0%
I I hope will lead to less
vulnerabilities<00:02:27.879><c> in</c><00:02:28.040><c> the</c><00:02:28.160><c> code</c><00:02:28.400><c> that</c><00:02:28.519><c> we</c><00:02:28.640><c> all</c>

00:02:28.830 --> 00:02:28.840 align:start position:0%
vulnerabilities in the code that we all
 

00:02:28.840 --> 00:02:31.750 align:start position:0%
vulnerabilities in the code that we all
write<00:02:29.920><c> speaking</c><00:02:30.200><c> of</c><00:02:30.360><c> security</c><00:02:31.319><c> this</c><00:02:31.440><c> is</c><00:02:31.599><c> my</c>

00:02:31.750 --> 00:02:31.760 align:start position:0%
write speaking of security this is my
 

00:02:31.760 --> 00:02:34.150 align:start position:0%
write speaking of security this is my
weird<00:02:32.000><c> segue</c><00:02:32.560><c> into</c><00:02:32.840><c> talking</c><00:02:33.120><c> about</c><00:02:33.360><c> the</c><00:02:33.519><c> XZ</c>

00:02:34.150 --> 00:02:34.160 align:start position:0%
weird segue into talking about the XZ
 

00:02:34.160 --> 00:02:37.030 align:start position:0%
weird segue into talking about the XZ
back<00:02:34.440><c> door</c><00:02:35.160><c> okay</c><00:02:35.599><c> so</c><00:02:35.760><c> this</c><00:02:35.879><c> is</c><00:02:36.000><c> a</c><00:02:36.160><c> big</c><00:02:36.319><c> deal</c><00:02:36.920><c> uh</c>

00:02:37.030 --> 00:02:37.040 align:start position:0%
back door okay so this is a big deal uh
 

00:02:37.040 --> 00:02:39.270 align:start position:0%
back door okay so this is a big deal uh
the<00:02:37.160><c> XZ</c><00:02:37.560><c> compression</c><00:02:38.040><c> library</c><00:02:38.599><c> is</c><00:02:38.760><c> incredibly</c>

00:02:39.270 --> 00:02:39.280 align:start position:0%
the XZ compression library is incredibly
 

00:02:39.280 --> 00:02:41.630 align:start position:0%
the XZ compression library is incredibly
common<00:02:39.640><c> and</c><00:02:39.840><c> prolific</c><00:02:40.360><c> across</c><00:02:41.000><c> Linux</c><00:02:41.440><c> and</c>

00:02:41.630 --> 00:02:41.640 align:start position:0%
common and prolific across Linux and
 

00:02:41.640 --> 00:02:43.869 align:start position:0%
common and prolific across Linux and
Unix<00:02:42.040><c> like</c><00:02:42.280><c> systems</c><00:02:43.040><c> it's</c><00:02:43.200><c> used</c><00:02:43.480><c> by</c><00:02:43.640><c> most</c>

00:02:43.869 --> 00:02:43.879 align:start position:0%
Unix like systems it's used by most
 

00:02:43.879 --> 00:02:46.190 align:start position:0%
Unix like systems it's used by most
Linux<00:02:44.239><c> dros</c><00:02:44.840><c> and</c><00:02:44.920><c> it's</c><00:02:45.120><c> even</c><00:02:45.400><c> a</c><00:02:45.560><c> dependency</c>

00:02:46.190 --> 00:02:46.200 align:start position:0%
Linux dros and it's even a dependency
 

00:02:46.200 --> 00:02:49.309 align:start position:0%
Linux dros and it's even a dependency
for<00:02:46.440><c> open</c><00:02:47.080><c> SSH</c><00:02:48.080><c> well</c><00:02:48.360><c> a</c><00:02:48.440><c> few</c><00:02:48.640><c> days</c><00:02:48.800><c> ago</c><00:02:49.040><c> as</c><00:02:49.159><c> I'm</c>

00:02:49.309 --> 00:02:49.319 align:start position:0%
for open SSH well a few days ago as I'm
 

00:02:49.319 --> 00:02:52.309 align:start position:0%
for open SSH well a few days ago as I'm
recording<00:02:49.760><c> this</c><00:02:50.400><c> uh</c><00:02:50.519><c> Andre</c><00:02:50.920><c> fry</c><00:02:51.599><c> a</c><00:02:51.879><c> Microsoft</c>

00:02:52.309 --> 00:02:52.319 align:start position:0%
recording this uh Andre fry a Microsoft
 

00:02:52.319 --> 00:02:54.430 align:start position:0%
recording this uh Andre fry a Microsoft
engineer<00:02:52.720><c> in</c><00:02:52.879><c> an</c><00:02:53.000><c> all-around</c><00:02:53.400><c> Linux</c><00:02:53.720><c> geek</c><00:02:54.280><c> who</c>

00:02:54.430 --> 00:02:54.440 align:start position:0%
engineer in an all-around Linux geek who
 

00:02:54.440 --> 00:02:56.710 align:start position:0%
engineer in an all-around Linux geek who
works<00:02:54.800><c> on</c><00:02:55.040><c> postgress</c><00:02:56.040><c> noticed</c><00:02:56.519><c> some</c>

00:02:56.710 --> 00:02:56.720 align:start position:0%
works on postgress noticed some
 

00:02:56.720 --> 00:02:58.350 align:start position:0%
works on postgress noticed some
performance<00:02:57.159><c> issues</c><00:02:57.599><c> that</c><00:02:57.760><c> he</c><00:02:57.920><c> was</c><00:02:58.040><c> able</c><00:02:58.239><c> to</c>

00:02:58.350 --> 00:02:58.360 align:start position:0%
performance issues that he was able to
 

00:02:58.360 --> 00:03:01.470 align:start position:0%
performance issues that he was able to
track<00:02:58.720><c> down</c><00:02:59.000><c> to</c><00:02:59.440><c> SSA</c><00:02:59.840><c> stage</c><00:03:00.120><c> on</c><00:03:00.360><c> his</c><00:03:01.000><c> debut</c><00:03:01.400><c> and</c>

00:03:01.470 --> 00:03:01.480 align:start position:0%
track down to SSA stage on his debut and
 

00:03:01.480 --> 00:03:03.630 align:start position:0%
track down to SSA stage on his debut and
unstable<00:03:01.920><c> build</c><00:03:02.560><c> and</c><00:03:02.720><c> after</c><00:03:02.879><c> some</c><00:03:03.080><c> digging</c><00:03:03.480><c> he</c>

00:03:03.630 --> 00:03:03.640 align:start position:0%
unstable build and after some digging he
 

00:03:03.640 --> 00:03:05.070 align:start position:0%
unstable build and after some digging he
realized<00:03:04.040><c> that</c><00:03:04.200><c> there</c><00:03:04.280><c> was</c><00:03:04.480><c> something</c><00:03:04.840><c> in</c><00:03:04.959><c> the</c>

00:03:05.070 --> 00:03:05.080 align:start position:0%
realized that there was something in the
 

00:03:05.080 --> 00:03:07.589 align:start position:0%
realized that there was something in the
latest<00:03:05.440><c> version</c><00:03:05.879><c> of</c><00:03:06.120><c> XZ</c><00:03:06.799><c> that</c><00:03:06.920><c> was</c><00:03:07.200><c> causing</c>

00:03:07.589 --> 00:03:07.599 align:start position:0%
latest version of XZ that was causing
 

00:03:07.599 --> 00:03:09.830 align:start position:0%
latest version of XZ that was causing
the<00:03:07.799><c> issue</c><00:03:08.440><c> and</c><00:03:08.560><c> then</c><00:03:08.879><c> further</c><00:03:09.239><c> investigation</c>

00:03:09.830 --> 00:03:09.840 align:start position:0%
the issue and then further investigation
 

00:03:09.840 --> 00:03:12.430 align:start position:0%
the issue and then further investigation
revealed<00:03:10.280><c> that</c><00:03:10.400><c> it</c><00:03:10.480><c> was</c><00:03:10.920><c> actually</c><00:03:11.920><c> a</c><00:03:12.159><c> back</c>

00:03:12.430 --> 00:03:12.440 align:start position:0%
revealed that it was actually a back
 

00:03:12.440 --> 00:03:14.430 align:start position:0%
revealed that it was actually a back
door<00:03:12.840><c> that</c><00:03:12.959><c> if</c><00:03:13.120><c> triggered</c><00:03:13.720><c> could</c><00:03:13.879><c> allow</c><00:03:14.159><c> for</c>

00:03:14.430 --> 00:03:14.440 align:start position:0%
door that if triggered could allow for
 

00:03:14.440 --> 00:03:16.229 align:start position:0%
door that if triggered could allow for
remote<00:03:14.760><c> code</c><00:03:15.040><c> execution</c><00:03:15.920><c> by</c><00:03:16.080><c> an</c>

00:03:16.229 --> 00:03:16.239 align:start position:0%
remote code execution by an
 

00:03:16.239 --> 00:03:18.710 align:start position:0%
remote code execution by an
unauthenticated<00:03:17.040><c> user</c><00:03:17.840><c> so</c><00:03:18.200><c> this</c><00:03:18.319><c> is</c><00:03:18.440><c> where</c><00:03:18.599><c> it</c>

00:03:18.710 --> 00:03:18.720 align:start position:0%
unauthenticated user so this is where it
 

00:03:18.720 --> 00:03:21.149 align:start position:0%
unauthenticated user so this is where it
gets<00:03:19.000><c> bad</c><00:03:19.879><c> the</c><00:03:20.080><c> person</c><00:03:20.360><c> who</c><00:03:20.519><c> put</c><00:03:20.720><c> the</c><00:03:20.879><c> back</c>

00:03:21.149 --> 00:03:21.159 align:start position:0%
gets bad the person who put the back
 

00:03:21.159 --> 00:03:23.229 align:start position:0%
gets bad the person who put the back
door<00:03:21.480><c> in</c><00:03:21.680><c> XD</c><00:03:22.319><c> was</c><00:03:22.519><c> someone</c><00:03:22.799><c> who</c><00:03:22.959><c> had</c><00:03:23.080><c> been</c>

00:03:23.229 --> 00:03:23.239 align:start position:0%
door in XD was someone who had been
 

00:03:23.239 --> 00:03:25.270 align:start position:0%
door in XD was someone who had been
contributing<00:03:23.799><c> code</c><00:03:24.000><c> to</c><00:03:24.120><c> the</c><00:03:24.280><c> project</c><00:03:25.000><c> for</c><00:03:25.200><c> a</c>

00:03:25.270 --> 00:03:25.280 align:start position:0%
contributing code to the project for a
 

00:03:25.280 --> 00:03:26.990 align:start position:0%
contributing code to the project for a
couple<00:03:25.480><c> of</c><00:03:25.640><c> years</c><00:03:25.879><c> and</c><00:03:26.040><c> had</c><00:03:26.319><c> actually</c><00:03:26.799><c> been</c>

00:03:26.990 --> 00:03:27.000 align:start position:0%
couple of years and had actually been
 

00:03:27.000 --> 00:03:29.509 align:start position:0%
couple of years and had actually been
made<00:03:27.480><c> a</c><00:03:27.680><c> co-maintainer</c><00:03:28.239><c> of</c><00:03:28.519><c> the</c><00:03:28.680><c> project</c><00:03:29.400><c> and</c>

00:03:29.509 --> 00:03:29.519 align:start position:0%
made a co-maintainer of the project and
 

00:03:29.519 --> 00:03:31.149 align:start position:0%
made a co-maintainer of the project and
so<00:03:29.879><c> this</c><00:03:30.000><c> is</c><00:03:30.080><c> a</c><00:03:30.239><c> person</c><00:03:30.480><c> who</c><00:03:30.680><c> didn't</c><00:03:30.920><c> have</c><00:03:31.040><c> a</c>

00:03:31.149 --> 00:03:31.159 align:start position:0%
so this is a person who didn't have a
 

00:03:31.159 --> 00:03:32.990 align:start position:0%
so this is a person who didn't have a
history<00:03:31.439><c> of</c><00:03:31.519><c> submitting</c><00:03:31.920><c> bad</c><00:03:32.159><c> code</c><00:03:32.760><c> and</c><00:03:32.879><c> who</c>

00:03:32.990 --> 00:03:33.000 align:start position:0%
history of submitting bad code and who
 

00:03:33.000 --> 00:03:35.070 align:start position:0%
history of submitting bad code and who
went<00:03:33.239><c> out</c><00:03:33.360><c> of</c><00:03:33.519><c> their</c><00:03:33.680><c> way</c><00:03:33.920><c> to</c><00:03:34.080><c> make</c><00:03:34.319><c> the</c><00:03:34.959><c> the</c>

00:03:35.070 --> 00:03:35.080 align:start position:0%
went out of their way to make the the
 

00:03:35.080 --> 00:03:38.830 align:start position:0%
went out of their way to make the the
code<00:03:35.280><c> hard</c><00:03:35.519><c> to</c><00:03:35.760><c> spot</c><00:03:36.760><c> and</c><00:03:37.159><c> Andre</c><00:03:37.519><c> only</c><00:03:37.920><c> found</c>

00:03:38.830 --> 00:03:38.840 align:start position:0%
code hard to spot and Andre only found
 

00:03:38.840 --> 00:03:41.830 align:start position:0%
code hard to spot and Andre only found
uh<00:03:39.280><c> this</c><00:03:39.519><c> out</c><00:03:40.360><c> basically</c><00:03:40.720><c> by</c><00:03:40.840><c> happen</c><00:03:41.200><c> stance</c>

00:03:41.830 --> 00:03:41.840 align:start position:0%
uh this out basically by happen stance
 

00:03:41.840 --> 00:03:43.270 align:start position:0%
uh this out basically by happen stance
now<00:03:42.000><c> the</c><00:03:42.120><c> code</c><00:03:42.319><c> was</c><00:03:42.439><c> only</c><00:03:42.840><c> around</c><00:03:43.040><c> for</c><00:03:43.159><c> a</c>

00:03:43.270 --> 00:03:43.280 align:start position:0%
now the code was only around for a
 

00:03:43.280 --> 00:03:45.949 align:start position:0%
now the code was only around for a
couple<00:03:43.439><c> of</c><00:03:43.599><c> weeks</c><00:03:44.519><c> um</c><00:03:45.000><c> but</c><00:03:45.400><c> it</c><00:03:45.560><c> had</c><00:03:45.760><c> already</c>

00:03:45.949 --> 00:03:45.959 align:start position:0%
couple of weeks um but it had already
 

00:03:45.959 --> 00:03:47.630 align:start position:0%
couple of weeks um but it had already
been<00:03:46.080><c> added</c><00:03:46.319><c> to</c><00:03:46.439><c> the</c><00:03:46.519><c> unstable</c><00:03:47.080><c> or</c><00:03:47.280><c> rolling</c>

00:03:47.630 --> 00:03:47.640 align:start position:0%
been added to the unstable or rolling
 

00:03:47.640 --> 00:03:49.990 align:start position:0%
been added to the unstable or rolling
branches<00:03:48.040><c> of</c><00:03:48.200><c> fuelex</c><00:03:48.799><c> distributions</c><00:03:49.799><c> so</c><00:03:49.959><c> if</c>

00:03:49.990 --> 00:03:50.000 align:start position:0%
branches of fuelex distributions so if
 

00:03:50.000 --> 00:03:52.110 align:start position:0%
branches of fuelex distributions so if
you're<00:03:50.159><c> running</c><00:03:50.640><c> uh</c><00:03:50.760><c> Debbie</c><00:03:51.040><c> and</c><00:03:51.159><c> unstable</c><00:03:52.000><c> uh</c>

00:03:52.110 --> 00:03:52.120 align:start position:0%
you're running uh Debbie and unstable uh
 

00:03:52.120 --> 00:03:54.149 align:start position:0%
you're running uh Debbie and unstable uh
certain<00:03:52.400><c> versions</c><00:03:52.720><c> of</c><00:03:52.879><c> Fedora</c><00:03:53.480><c> or</c><00:03:53.799><c> Susa</c>

00:03:54.149 --> 00:03:54.159 align:start position:0%
certain versions of Fedora or Susa
 

00:03:54.159 --> 00:03:55.350 align:start position:0%
certain versions of Fedora or Susa
tumble<00:03:54.480><c> weed</c><00:03:54.879><c> you're</c><00:03:55.000><c> going</c><00:03:55.079><c> to</c><00:03:55.159><c> want</c><00:03:55.280><c> to</c>

00:03:55.350 --> 00:03:55.360 align:start position:0%
tumble weed you're going to want to
 

00:03:55.360 --> 00:03:57.270 align:start position:0%
tumble weed you're going to want to
update<00:03:55.640><c> your</c><00:03:55.720><c> system</c><00:03:56.040><c> immediately</c><00:03:56.879><c> and</c><00:03:57.120><c> and</c>

00:03:57.270 --> 00:03:57.280 align:start position:0%
update your system immediately and and
 

00:03:57.280 --> 00:04:00.270 align:start position:0%
update your system immediately and and
as<00:03:57.400><c> Andre</c><00:03:57.799><c> said</c><00:03:58.040><c> himself</c><00:03:58.920><c> we</c><00:03:59.079><c> got</c><00:03:59.280><c> so</c><00:03:59.799><c> ly</c><00:04:00.159><c> that</c>

00:04:00.270 --> 00:04:00.280 align:start position:0%
as Andre said himself we got so ly that
 

00:04:00.280 --> 00:04:01.869 align:start position:0%
as Andre said himself we got so ly that
a<00:04:00.400><c> Confluence</c><00:04:00.799><c> of</c><00:04:00.959><c> events</c><00:04:01.319><c> happened</c><00:04:01.720><c> that</c>

00:04:01.869 --> 00:04:01.879 align:start position:0%
a Confluence of events happened that
 

00:04:01.879 --> 00:04:03.990 align:start position:0%
a Confluence of events happened that
allowed<00:04:02.560><c> this</c><00:04:02.720><c> to</c><00:04:02.840><c> be</c><00:04:03.000><c> spotted</c><00:04:03.480><c> as</c><00:04:03.599><c> quickly</c><00:04:03.920><c> as</c>

00:04:03.990 --> 00:04:04.000 align:start position:0%
allowed this to be spotted as quickly as
 

00:04:04.000 --> 00:04:05.990 align:start position:0%
allowed this to be spotted as quickly as
it<00:04:04.200><c> was</c><00:04:04.879><c> and</c><00:04:05.000><c> I've</c><00:04:05.120><c> got</c><00:04:05.280><c> tons</c><00:04:05.480><c> of</c><00:04:05.599><c> links</c><00:04:05.840><c> down</c>

00:04:05.990 --> 00:04:06.000 align:start position:0%
it was and I've got tons of links down
 

00:04:06.000 --> 00:04:07.470 align:start position:0%
it was and I've got tons of links down
below<00:04:06.280><c> that</c><00:04:06.439><c> highlight</c><00:04:06.799><c> the</c><00:04:06.920><c> timeline</c><00:04:07.280><c> about</c>

00:04:07.470 --> 00:04:07.480 align:start position:0%
below that highlight the timeline about
 

00:04:07.480 --> 00:04:09.589 align:start position:0%
below that highlight the timeline about
how<00:04:07.760><c> this</c><00:04:08.000><c> happened</c><00:04:08.879><c> uh</c><00:04:09.000><c> from</c><00:04:09.200><c> what</c><00:04:09.319><c> we</c><00:04:09.439><c> can</c>

00:04:09.589 --> 00:04:09.599 align:start position:0%
how this happened uh from what we can
 

00:04:09.599 --> 00:04:12.270 align:start position:0%
how this happened uh from what we can
tell<00:04:10.560><c> so</c><00:04:10.799><c> far</c><00:04:11.480><c> a</c><00:04:11.560><c> look</c><00:04:11.680><c> at</c><00:04:11.799><c> the</c><00:04:11.879><c> back</c><00:04:12.079><c> door</c>

00:04:12.270 --> 00:04:12.280 align:start position:0%
tell so far a look at the back door
 

00:04:12.280 --> 00:04:14.110 align:start position:0%
tell so far a look at the back door
itself<00:04:12.879><c> and</c><00:04:13.040><c> some</c><00:04:13.280><c> discussions</c><00:04:13.840><c> that</c><00:04:13.959><c> are</c>

00:04:14.110 --> 00:04:14.120 align:start position:0%
itself and some discussions that are
 

00:04:14.120 --> 00:04:15.949 align:start position:0%
itself and some discussions that are
taking<00:04:14.400><c> place</c><00:04:14.640><c> about</c><00:04:15.319><c> what</c><00:04:15.439><c> we</c><00:04:15.519><c> can</c><00:04:15.680><c> learn</c>

00:04:15.949 --> 00:04:15.959 align:start position:0%
taking place about what we can learn
 

00:04:15.959 --> 00:04:18.270 align:start position:0%
taking place about what we can learn
from<00:04:16.160><c> this</c><00:04:16.680><c> but</c><00:04:16.840><c> the</c><00:04:16.959><c> big</c><00:04:17.199><c> takeaway</c><00:04:17.919><c> and</c><00:04:18.120><c> and</c>

00:04:18.270 --> 00:04:18.280 align:start position:0%
from this but the big takeaway and and
 

00:04:18.280 --> 00:04:20.390 align:start position:0%
from this but the big takeaway and and
this<00:04:18.479><c> comes</c><00:04:19.079><c> about</c><00:04:19.280><c> a</c><00:04:19.479><c> decade</c><00:04:19.880><c> after</c><00:04:20.160><c> one</c><00:04:20.280><c> of</c>

00:04:20.390 --> 00:04:20.400 align:start position:0%
this comes about a decade after one of
 

00:04:20.400 --> 00:04:22.510 align:start position:0%
this comes about a decade after one of
the<00:04:20.519><c> first</c><00:04:20.880><c> big</c><00:04:21.120><c> crisis</c><00:04:21.720><c> of</c><00:04:22.079><c> conscience</c>

00:04:22.510 --> 00:04:22.520 align:start position:0%
the first big crisis of conscience
 

00:04:22.520 --> 00:04:24.110 align:start position:0%
the first big crisis of conscience
moments<00:04:22.880><c> in</c><00:04:23.000><c> the</c><00:04:23.080><c> open</c><00:04:23.320><c> source</c><00:04:23.520><c> Community</c>

00:04:24.110 --> 00:04:24.120 align:start position:0%
moments in the open source Community
 

00:04:24.120 --> 00:04:26.189 align:start position:0%
moments in the open source Community
which<00:04:24.280><c> was</c><00:04:24.600><c> heart</c><00:04:24.800><c> bed</c><00:04:25.080><c> and</c><00:04:25.240><c> that</c><00:04:25.360><c> was</c><00:04:25.800><c> a</c><00:04:25.960><c> very</c>

00:04:26.189 --> 00:04:26.199 align:start position:0%
which was heart bed and that was a very
 

00:04:26.199 --> 00:04:28.270 align:start position:0%
which was heart bed and that was a very
different<00:04:26.440><c> type</c><00:04:26.560><c> of</c><00:04:26.960><c> vulnerability</c><00:04:27.960><c> is</c><00:04:28.120><c> that</c>

00:04:28.270 --> 00:04:28.280 align:start position:0%
different type of vulnerability is that
 

00:04:28.280 --> 00:04:30.110 align:start position:0%
different type of vulnerability is that
we<00:04:28.440><c> need</c><00:04:28.600><c> to</c><00:04:28.759><c> find</c><00:04:28.919><c> a</c><00:04:29.080><c> way</c><00:04:29.280><c> to</c><00:04:29.720><c> offer</c><00:04:29.919><c> more</c>

00:04:30.110 --> 00:04:30.120 align:start position:0%
we need to find a way to offer more
 

00:04:30.120 --> 00:04:31.550 align:start position:0%
we need to find a way to offer more
support<00:04:30.479><c> to</c><00:04:30.600><c> the</c><00:04:30.720><c> maintainers</c><00:04:31.240><c> who</c><00:04:31.320><c> are</c>

00:04:31.550 --> 00:04:31.560 align:start position:0%
support to the maintainers who are
 

00:04:31.560 --> 00:04:33.790 align:start position:0%
support to the maintainers who are
responsible<00:04:32.400><c> for</c><00:04:32.600><c> some</c><00:04:32.720><c> of</c><00:04:32.800><c> our</c><00:04:33.000><c> most</c><00:04:33.360><c> trusted</c>

00:04:33.790 --> 00:04:33.800 align:start position:0%
responsible for some of our most trusted
 

00:04:33.800 --> 00:04:35.990 align:start position:0%
responsible for some of our most trusted
libraries<00:04:34.479><c> often</c><00:04:34.759><c> with</c><00:04:34.960><c> little</c><00:04:35.240><c> help</c><00:04:35.440><c> or</c>

00:04:35.990 --> 00:04:36.000 align:start position:0%
libraries often with little help or
 

00:04:36.000 --> 00:04:37.990 align:start position:0%
libraries often with little help or
compensation<00:04:37.000><c> the</c><00:04:37.160><c> original</c><00:04:37.479><c> maintainer</c>

00:04:37.990 --> 00:04:38.000 align:start position:0%
compensation the original maintainer
 

00:04:38.000 --> 00:04:40.790 align:start position:0%
compensation the original maintainer
vexi<00:04:38.520><c> didn't</c><00:04:38.759><c> commit</c><00:04:39.160><c> the</c><00:04:39.440><c> code</c><00:04:40.160><c> um</c><00:04:40.479><c> and</c><00:04:40.639><c> put</c>

00:04:40.790 --> 00:04:40.800 align:start position:0%
vexi didn't commit the code um and put
 

00:04:40.800 --> 00:04:42.550 align:start position:0%
vexi didn't commit the code um and put
the<00:04:40.919><c> back</c><00:04:41.080><c> door</c><00:04:41.280><c> in</c><00:04:41.600><c> and</c><00:04:41.720><c> there</c><00:04:41.800><c> was</c><00:04:42.080><c> nothing</c>

00:04:42.550 --> 00:04:42.560 align:start position:0%
the back door in and there was nothing
 

00:04:42.560 --> 00:04:44.270 align:start position:0%
the back door in and there was nothing
obvious<00:04:43.240><c> that</c><00:04:43.320><c> should</c><00:04:43.479><c> have</c><00:04:43.639><c> set</c><00:04:43.840><c> off</c><00:04:44.000><c> alarm</c>

00:04:44.270 --> 00:04:44.280 align:start position:0%
obvious that should have set off alarm
 

00:04:44.280 --> 00:04:46.150 align:start position:0%
obvious that should have set off alarm
Bells<00:04:44.639><c> about</c><00:04:44.840><c> the</c><00:04:45.000><c> person</c><00:04:45.320><c> who</c><00:04:45.520><c> he</c><00:04:45.680><c> made</c><00:04:45.840><c> a</c><00:04:45.960><c> CO</c>

00:04:46.150 --> 00:04:46.160 align:start position:0%
Bells about the person who he made a CO
 

00:04:46.160 --> 00:04:48.350 align:start position:0%
Bells about the person who he made a CO
maintainer<00:04:47.080><c> but</c><00:04:47.199><c> for</c><00:04:47.320><c> a</c><00:04:47.440><c> library</c><00:04:47.800><c> like</c><00:04:47.919><c> XZ</c>

00:04:48.350 --> 00:04:48.360 align:start position:0%
maintainer but for a library like XZ
 

00:04:48.360 --> 00:04:50.150 align:start position:0%
maintainer but for a library like XZ
utils<00:04:48.840><c> to</c><00:04:48.960><c> be</c><00:04:49.120><c> maintained</c><00:04:49.600><c> by</c><00:04:49.720><c> a</c><00:04:49.840><c> single</c>

00:04:50.150 --> 00:04:50.160 align:start position:0%
utils to be maintained by a single
 

00:04:50.160 --> 00:04:52.870 align:start position:0%
utils to be maintained by a single
person<00:04:50.600><c> who</c><00:04:51.240><c> had</c><00:04:51.400><c> already</c><00:04:51.759><c> expressed</c><00:04:52.520><c> um</c><00:04:52.759><c> the</c>

00:04:52.870 --> 00:04:52.880 align:start position:0%
person who had already expressed um the
 

00:04:52.880 --> 00:04:54.710 align:start position:0%
person who had already expressed um the
fact<00:04:53.000><c> that</c><00:04:53.120><c> he</c><00:04:53.240><c> was</c><00:04:53.360><c> facing</c><00:04:53.880><c> burnout</c><00:04:54.479><c> and</c><00:04:54.600><c> and</c>

00:04:54.710 --> 00:04:54.720 align:start position:0%
fact that he was facing burnout and and
 

00:04:54.720 --> 00:04:57.590 align:start position:0%
fact that he was facing burnout and and
feeling<00:04:55.440><c> unsupported</c><00:04:56.440><c> that's</c><00:04:56.639><c> not</c><00:04:57.000><c> okay</c><00:04:57.440><c> and</c>

00:04:57.590 --> 00:04:57.600 align:start position:0%
feeling unsupported that's not okay and
 

00:04:57.600 --> 00:04:58.749 align:start position:0%
feeling unsupported that's not okay and
and<00:04:57.680><c> we've</c><00:04:57.840><c> got</c><00:04:57.960><c> to</c><00:04:58.080><c> do</c><00:04:58.280><c> better</c><00:04:58.520><c> as</c><00:04:58.639><c> a</c>

00:04:58.749 --> 00:04:58.759 align:start position:0%
and we've got to do better as a
 

00:04:58.759 --> 00:05:00.909 align:start position:0%
and we've got to do better as a
community<00:04:59.240><c> and</c><00:04:59.360><c> as</c><00:04:59.680><c> ecosystem</c><00:05:00.120><c> to</c><00:05:00.280><c> support</c>

00:05:00.909 --> 00:05:00.919 align:start position:0%
community and as ecosystem to support
 

00:05:00.919 --> 00:05:02.870 align:start position:0%
community and as ecosystem to support
maintainers<00:05:01.919><c> as</c><00:05:02.039><c> I've</c><00:05:02.199><c> said</c><00:05:02.440><c> I've</c><00:05:02.560><c> got</c><00:05:02.680><c> a</c><00:05:02.759><c> lot</c>

00:05:02.870 --> 00:05:02.880 align:start position:0%
maintainers as I've said I've got a lot
 

00:05:02.880 --> 00:05:04.830 align:start position:0%
maintainers as I've said I've got a lot
of<00:05:03.000><c> resources</c><00:05:03.400><c> linked</c><00:05:03.680><c> down</c><00:05:03.840><c> below</c><00:05:04.639><c> um</c><00:05:04.720><c> but</c>

00:05:04.830 --> 00:05:04.840 align:start position:0%
of resources linked down below um but
 

00:05:04.840 --> 00:05:06.189 align:start position:0%
of resources linked down below um but
I'm<00:05:04.919><c> sure</c><00:05:05.080><c> we're</c><00:05:05.199><c> going</c><00:05:05.320><c> to</c><00:05:05.400><c> be</c><00:05:05.520><c> talking</c><00:05:05.840><c> about</c>

00:05:06.189 --> 00:05:06.199 align:start position:0%
I'm sure we're going to be talking about
 

00:05:06.199 --> 00:05:08.270 align:start position:0%
I'm sure we're going to be talking about
this<00:05:06.440><c> in</c><00:05:06.560><c> the</c><00:05:06.680><c> weeks</c><00:05:06.919><c> and</c><00:05:07.080><c> months</c><00:05:07.320><c> to</c><00:05:07.440><c> come</c><00:05:08.160><c> all</c>

00:05:08.270 --> 00:05:08.280 align:start position:0%
this in the weeks and months to come all
 

00:05:08.280 --> 00:05:10.070 align:start position:0%
this in the weeks and months to come all
right<00:05:08.440><c> let's</c><00:05:08.600><c> talk</c><00:05:08.759><c> about</c><00:05:08.919><c> some</c><00:05:09.039><c> new</c><00:05:09.199><c> releases</c>

00:05:10.070 --> 00:05:10.080 align:start position:0%
right let's talk about some new releases
 

00:05:10.080 --> 00:05:14.270 align:start position:0%
right let's talk about some new releases
first<00:05:10.360><c> up</c><00:05:10.800><c> bun.</c><00:05:11.440><c> one.</c><00:05:12.240><c> Bun</c><00:05:12.919><c> 1.1</c><00:05:13.919><c> has</c><00:05:14.080><c> been</c>

00:05:14.270 --> 00:05:14.280 align:start position:0%
first up bun. one. Bun 1.1 has been
 

00:05:14.280 --> 00:05:16.070 align:start position:0%
first up bun. one. Bun 1.1 has been
released<00:05:14.880><c> so</c><00:05:15.240><c> we've</c><00:05:15.440><c> talked</c><00:05:15.680><c> about</c><00:05:15.840><c> bun</c>

00:05:16.070 --> 00:05:16.080 align:start position:0%
released so we've talked about bun
 

00:05:16.080 --> 00:05:19.390 align:start position:0%
released so we've talked about bun
before<00:05:16.800><c> um</c><00:05:17.120><c> basically</c><00:05:18.080><c> uh</c><00:05:18.400><c> how</c><00:05:18.639><c> its</c><00:05:18.880><c> team</c>

00:05:19.390 --> 00:05:19.400 align:start position:0%
before um basically uh how its team
 

00:05:19.400 --> 00:05:22.590 align:start position:0%
before um basically uh how its team
describes<00:05:19.919><c> itself</c><00:05:20.800><c> is</c><00:05:21.120><c> as</c><00:05:21.440><c> a</c><00:05:21.720><c> fast</c><00:05:22.080><c> all-in-one</c>

00:05:22.590 --> 00:05:22.600 align:start position:0%
describes itself is as a fast all-in-one
 

00:05:22.600 --> 00:05:24.790 align:start position:0%
describes itself is as a fast all-in-one
toolkit<00:05:23.080><c> for</c><00:05:23.680><c> running</c><00:05:24.000><c> building</c><00:05:24.360><c> testing</c><00:05:24.680><c> and</c>

00:05:24.790 --> 00:05:24.800 align:start position:0%
toolkit for running building testing and
 

00:05:24.800 --> 00:05:26.749 align:start position:0%
toolkit for running building testing and
debugging<00:05:25.199><c> JavaScript</c><00:05:25.680><c> or</c><00:05:25.800><c> typescript</c><00:05:26.639><c> from</c>

00:05:26.749 --> 00:05:26.759 align:start position:0%
debugging JavaScript or typescript from
 

00:05:26.759 --> 00:05:28.390 align:start position:0%
debugging JavaScript or typescript from
a<00:05:26.880><c> single</c><00:05:27.199><c> script</c><00:05:27.520><c> to</c><00:05:27.680><c> a</c><00:05:27.759><c> full</c><00:05:28.000><c> stack</c>

00:05:28.390 --> 00:05:28.400 align:start position:0%
a single script to a full stack
 

00:05:28.400 --> 00:05:30.629 align:start position:0%
a single script to a full stack
application<00:05:29.400><c> uh</c><00:05:29.919><c> that's</c><00:05:30.039><c> what</c><00:05:30.160><c> it</c><00:05:30.240><c> says</c><00:05:30.479><c> and</c>

00:05:30.629 --> 00:05:30.639 align:start position:0%
application uh that's what it says and
 

00:05:30.639 --> 00:05:32.150 align:start position:0%
application uh that's what it says and
that's<00:05:30.840><c> actually</c><00:05:31.240><c> what</c><00:05:31.360><c> it</c><00:05:31.520><c> does</c><00:05:31.720><c> it's</c><00:05:31.880><c> pretty</c>

00:05:32.150 --> 00:05:32.160 align:start position:0%
that's actually what it does it's pretty
 

00:05:32.160 --> 00:05:35.150 align:start position:0%
that's actually what it does it's pretty
great<00:05:33.080><c> and</c><00:05:33.680><c> uh</c><00:05:34.000><c> the</c><00:05:34.120><c> new</c><00:05:34.319><c> release</c><00:05:34.800><c> adds</c><00:05:35.039><c> a</c>

00:05:35.150 --> 00:05:35.160 align:start position:0%
great and uh the new release adds a
 

00:05:35.160 --> 00:05:37.350 align:start position:0%
great and uh the new release adds a
bunch<00:05:35.360><c> of</c><00:05:35.479><c> new</c><00:05:35.639><c> features</c><00:05:36.400><c> the</c><00:05:36.560><c> biggest</c><00:05:36.880><c> one</c><00:05:37.199><c> is</c>

00:05:37.350 --> 00:05:37.360 align:start position:0%
bunch of new features the biggest one is
 

00:05:37.360 --> 00:05:39.469 align:start position:0%
bunch of new features the biggest one is
that<00:05:37.600><c> Windows</c><00:05:37.960><c> support</c><00:05:38.319><c> is</c><00:05:38.520><c> finally</c><00:05:38.880><c> here</c>

00:05:39.469 --> 00:05:39.479 align:start position:0%
that Windows support is finally here
 

00:05:39.479 --> 00:05:40.909 align:start position:0%
that Windows support is finally here
this<00:05:39.600><c> was</c><00:05:39.800><c> actually</c><00:05:40.039><c> previewed</c><00:05:40.479><c> back</c><00:05:40.680><c> with</c>

00:05:40.909 --> 00:05:40.919 align:start position:0%
this was actually previewed back with
 

00:05:40.919 --> 00:05:43.909 align:start position:0%
this was actually previewed back with
bun<00:05:41.319><c> 1.0</c><00:05:42.000><c> but</c><00:05:42.199><c> bun</c><00:05:42.479><c> 1.1</c><00:05:43.199><c> adds</c><00:05:43.520><c> support</c><00:05:43.759><c> for</c>

00:05:43.909 --> 00:05:43.919 align:start position:0%
bun 1.0 but bun 1.1 adds support for
 

00:05:43.919 --> 00:05:46.430 align:start position:0%
bun 1.0 but bun 1.1 adds support for
Windows<00:05:44.199><c> 10</c><00:05:44.400><c> and</c><00:05:44.520><c> later</c><00:05:44.960><c> which</c><00:05:45.080><c> is</c><00:05:45.319><c> awesome</c><00:05:46.319><c> uh</c>

00:05:46.430 --> 00:05:46.440 align:start position:0%
Windows 10 and later which is awesome uh
 

00:05:46.440 --> 00:05:47.990 align:start position:0%
Windows 10 and later which is awesome uh
another<00:05:46.680><c> feature</c><00:05:46.960><c> in</c><00:05:47.080><c> this</c><00:05:47.240><c> release</c><00:05:47.680><c> is</c><00:05:47.880><c> the</c>

00:05:47.990 --> 00:05:48.000 align:start position:0%
another feature in this release is the
 

00:05:48.000 --> 00:05:50.270 align:start position:0%
another feature in this release is the
bun<00:05:48.319><c> shell</c><00:05:49.080><c> which</c><00:05:49.240><c> was</c><00:05:49.600><c> actually</c><00:05:49.840><c> announced</c><00:05:50.199><c> a</c>

00:05:50.270 --> 00:05:50.280 align:start position:0%
bun shell which was actually announced a
 

00:05:50.280 --> 00:05:52.110 align:start position:0%
bun shell which was actually announced a
couple<00:05:50.520><c> months</c><00:05:50.759><c> ago</c><00:05:51.319><c> but</c><00:05:51.479><c> it's</c><00:05:51.680><c> basically</c><00:05:51.960><c> a</c>

00:05:52.110 --> 00:05:52.120 align:start position:0%
couple months ago but it's basically a
 

00:05:52.120 --> 00:05:53.790 align:start position:0%
couple months ago but it's basically a
cross-platform<00:05:52.759><c> shell</c><00:05:53.120><c> that</c><00:05:53.199><c> is</c><00:05:53.319><c> similar</c><00:05:53.600><c> to</c>

00:05:53.790 --> 00:05:53.800 align:start position:0%
cross-platform shell that is similar to
 

00:05:53.800 --> 00:05:55.909 align:start position:0%
cross-platform shell that is similar to
bash<00:05:54.120><c> but</c><00:05:54.240><c> it</c><00:05:54.360><c> also</c><00:05:54.520><c> works</c><00:05:54.759><c> on</c><00:05:54.919><c> Windows</c><00:05:55.440><c> and</c><00:05:55.759><c> um</c>

00:05:55.909 --> 00:05:55.919 align:start position:0%
bash but it also works on Windows and um
 

00:05:55.919 --> 00:05:57.029 align:start position:0%
bash but it also works on Windows and um
there<00:05:56.000><c> are</c><00:05:56.120><c> lots</c><00:05:56.280><c> of</c><00:05:56.400><c> other</c><00:05:56.520><c> improvements</c><00:05:56.919><c> and</c>

00:05:57.029 --> 00:05:57.039 align:start position:0%
there are lots of other improvements and
 

00:05:57.039 --> 00:05:58.510 align:start position:0%
there are lots of other improvements and
features<00:05:57.440><c> too</c><00:05:57.840><c> so</c><00:05:57.960><c> you</c><00:05:58.039><c> can</c><00:05:58.160><c> check</c><00:05:58.319><c> out</c><00:05:58.440><c> the</c>

00:05:58.510 --> 00:05:58.520 align:start position:0%
features too so you can check out the
 

00:05:58.520 --> 00:06:00.670 align:start position:0%
features too so you can check out the
bun<00:05:58.800><c> website</c><00:05:59.360><c> and</c><00:05:59.759><c> their</c><00:05:59.960><c> GitHub</c><00:06:00.280><c> repo</c><00:06:00.560><c> for</c>

00:06:00.670 --> 00:06:00.680 align:start position:0%
bun website and their GitHub repo for
 

00:06:00.680 --> 00:06:02.230 align:start position:0%
bun website and their GitHub repo for
more<00:06:00.880><c> info</c><00:06:01.360><c> and</c><00:06:01.479><c> I've</c><00:06:01.600><c> got</c><00:06:01.759><c> all</c><00:06:01.880><c> that</c><00:06:02.000><c> linked</c>

00:06:02.230 --> 00:06:02.240 align:start position:0%
more info and I've got all that linked
 

00:06:02.240 --> 00:06:05.070 align:start position:0%
more info and I've got all that linked
down<00:06:02.520><c> below</c><00:06:03.520><c> and</c><00:06:03.680><c> in</c><00:06:03.880><c> other</c><00:06:04.120><c> new</c><00:06:04.319><c> release</c><00:06:04.680><c> news</c>

00:06:05.070 --> 00:06:05.080 align:start position:0%
down below and in other new release news
 

00:06:05.080 --> 00:06:07.990 align:start position:0%
down below and in other new release news
babylonjs<00:06:06.000><c> 7.0</c><00:06:06.800><c> has</c><00:06:06.919><c> been</c><00:06:07.080><c> released</c><00:06:07.840><c> and</c>

00:06:07.990 --> 00:06:08.000 align:start position:0%
babylonjs 7.0 has been released and
 

00:06:08.000 --> 00:06:09.790 align:start position:0%
babylonjs 7.0 has been released and
babylonjs<00:06:08.680><c> is</c><00:06:08.800><c> an</c><00:06:08.919><c> open</c><00:06:09.199><c> web</c><00:06:09.400><c> rendering</c>

00:06:09.790 --> 00:06:09.800 align:start position:0%
babylonjs is an open web rendering
 

00:06:09.800 --> 00:06:11.870 align:start position:0%
babylonjs is an open web rendering
engine<00:06:10.080><c> that's</c><00:06:10.240><c> used</c><00:06:10.479><c> for</c><00:06:10.599><c> creating</c><00:06:10.919><c> 3D</c><00:06:11.319><c> games</c>

00:06:11.870 --> 00:06:11.880 align:start position:0%
engine that's used for creating 3D games
 

00:06:11.880 --> 00:06:14.629 align:start position:0%
engine that's used for creating 3D games
and<00:06:12.080><c> experiences</c><00:06:12.599><c> in</c><00:06:12.720><c> the</c><00:06:12.800><c> browser</c><00:06:13.800><c> it</c><00:06:14.160><c> is</c>

00:06:14.629 --> 00:06:14.639 align:start position:0%
and experiences in the browser it is
 

00:06:14.639 --> 00:06:17.110 align:start position:0%
and experiences in the browser it is
awesome<00:06:15.440><c> and</c><00:06:15.720><c> babyon</c><00:06:16.039><c> js7</c><00:06:16.840><c> is</c><00:06:16.960><c> the</c>

00:06:17.110 --> 00:06:17.120 align:start position:0%
awesome and babyon js7 is the
 

00:06:17.120 --> 00:06:19.110 align:start position:0%
awesome and babyon js7 is the
culmination<00:06:17.720><c> of</c><00:06:18.039><c> tons</c><00:06:18.319><c> of</c><00:06:18.479><c> hard</c><00:06:18.720><c> work</c><00:06:18.960><c> from</c>

00:06:19.110 --> 00:06:19.120 align:start position:0%
culmination of tons of hard work from
 

00:06:19.120 --> 00:06:21.189 align:start position:0%
culmination of tons of hard work from
the<00:06:19.319><c> last</c><00:06:19.599><c> year</c><00:06:20.160><c> and</c><00:06:20.280><c> it</c><00:06:20.400><c> includes</c><00:06:20.800><c> a</c><00:06:20.919><c> bunch</c><00:06:21.080><c> of</c>

00:06:21.189 --> 00:06:21.199 align:start position:0%
the last year and it includes a bunch of
 

00:06:21.199 --> 00:06:23.070 align:start position:0%
the last year and it includes a bunch of
new<00:06:21.319><c> features</c><00:06:21.759><c> including</c><00:06:22.560><c> procedural</c>

00:06:23.070 --> 00:06:23.080 align:start position:0%
new features including procedural
 

00:06:23.080 --> 00:06:25.270 align:start position:0%
new features including procedural
geometry<00:06:23.759><c> which</c><00:06:23.960><c> the</c><00:06:24.080><c> team</c><00:06:24.319><c> is</c><00:06:24.479><c> calling</c><00:06:24.880><c> node</c>

00:06:25.270 --> 00:06:25.280 align:start position:0%
geometry which the team is calling node
 

00:06:25.280 --> 00:06:27.390 align:start position:0%
geometry which the team is calling node
geometry<00:06:26.280><c> and</c><00:06:26.400><c> it</c><00:06:26.560><c> also</c><00:06:26.759><c> includes</c><00:06:27.120><c> support</c>

00:06:27.390 --> 00:06:27.400 align:start position:0%
geometry and it also includes support
 

00:06:27.400 --> 00:06:29.550 align:start position:0%
geometry and it also includes support
for<00:06:27.639><c> basic</c><00:06:28.000><c> Global</c><00:06:28.319><c> illumination</c><00:06:29.199><c> um</c><00:06:29.319><c> this</c><00:06:29.440><c> is</c>

00:06:29.550 --> 00:06:29.560 align:start position:0%
for basic Global illumination um this is
 

00:06:29.560 --> 00:06:31.029 align:start position:0%
for basic Global illumination um this is
has<00:06:29.680><c> been</c><00:06:29.759><c> a</c><00:06:29.840><c> much</c><00:06:30.039><c> requested</c><00:06:30.440><c> feature</c><00:06:30.919><c> that</c>

00:06:31.029 --> 00:06:31.039 align:start position:0%
has been a much requested feature that
 

00:06:31.039 --> 00:06:33.710 align:start position:0%
has been a much requested feature that
will<00:06:31.479><c> let</c><00:06:31.720><c> Babylon</c><00:06:32.120><c> JS</c><00:06:32.479><c> scenes</c><00:06:32.800><c> render</c><00:06:33.520><c> even</c>

00:06:33.710 --> 00:06:33.720 align:start position:0%
will let Babylon JS scenes render even
 

00:06:33.720 --> 00:06:36.070 align:start position:0%
will let Babylon JS scenes render even
more<00:06:34.000><c> lifelike</c><00:06:34.759><c> um</c><00:06:34.919><c> scenes</c><00:06:35.400><c> and</c><00:06:35.639><c> and</c><00:06:35.960><c> uh</c>

00:06:36.070 --> 00:06:36.080 align:start position:0%
more lifelike um scenes and and uh
 

00:06:36.080 --> 00:06:38.510 align:start position:0%
more lifelike um scenes and and uh
there's<00:06:36.319><c> also</c><00:06:36.599><c> stuff</c><00:06:36.880><c> like</c><00:06:37.120><c> the</c><00:06:37.440><c> uh</c><00:06:38.160><c> uh</c>

00:06:38.510 --> 00:06:38.520 align:start position:0%
there's also stuff like the uh uh
 

00:06:38.520 --> 00:06:41.629 align:start position:0%
there's also stuff like the uh uh
gussian<00:06:39.360><c> Splat</c><00:06:39.840><c> rendering</c><00:06:40.720><c> there's</c><00:06:41.039><c> full</c><00:06:41.400><c> web</c>

00:06:41.629 --> 00:06:41.639 align:start position:0%
gussian Splat rendering there's full web
 

00:06:41.639 --> 00:06:44.390 align:start position:0%
gussian Splat rendering there's full web
XR<00:06:42.160><c> support</c><00:06:42.800><c> and</c><00:06:43.280><c> support</c><00:06:43.639><c> for</c><00:06:44.000><c> the</c><00:06:44.120><c> Apple</c>

00:06:44.390 --> 00:06:44.400 align:start position:0%
XR support and support for the Apple
 

00:06:44.400 --> 00:06:46.309 align:start position:0%
XR support and support for the Apple
Vision<00:06:44.720><c> Pro</c><00:06:45.199><c> so</c><00:06:45.479><c> um</c><00:06:45.639><c> in</c><00:06:45.759><c> the</c><00:06:45.840><c> links</c><00:06:46.120><c> in</c><00:06:46.240><c> the</c>

00:06:46.309 --> 00:06:46.319 align:start position:0%
Vision Pro so um in the links in the
 

00:06:46.319 --> 00:06:47.309 align:start position:0%
Vision Pro so um in the links in the
description<00:06:46.680><c> I've</c><00:06:46.800><c> got</c><00:06:46.880><c> a</c><00:06:46.960><c> link</c><00:06:47.160><c> to</c><00:06:47.240><c> the</c>

00:06:47.309 --> 00:06:47.319 align:start position:0%
description I've got a link to the
 

00:06:47.319 --> 00:06:49.029 align:start position:0%
description I've got a link to the
babylonjs<00:06:47.919><c> highlights</c><00:06:48.319><c> video</c><00:06:48.880><c> their</c>

00:06:49.029 --> 00:06:49.039 align:start position:0%
babylonjs highlights video their
 

00:06:49.039 --> 00:06:50.749 align:start position:0%
babylonjs highlights video their
announcement<00:06:49.479><c> blog</c><00:06:49.759><c> and</c><00:06:49.880><c> of</c><00:06:50.039><c> course</c><00:06:50.599><c> the</c>

00:06:50.749 --> 00:06:50.759 align:start position:0%
announcement blog and of course the
 

00:06:50.759 --> 00:06:52.469 align:start position:0%
announcement blog and of course the
project<00:06:51.039><c> page</c><00:06:51.199><c> on</c><00:06:51.400><c> GitHub</c><00:06:51.960><c> and</c><00:06:52.080><c> now</c><00:06:52.240><c> it's</c><00:06:52.360><c> time</c>

00:06:52.469 --> 00:06:52.479 align:start position:0%
project page on GitHub and now it's time
 

00:06:52.479 --> 00:06:55.150 align:start position:0%
project page on GitHub and now it's time
for<00:06:52.599><c> my</c><00:06:52.800><c> pick</c><00:06:52.960><c> of</c><00:06:53.039><c> the</c><00:06:53.160><c> week</c><00:06:54.120><c> okay</c><00:06:54.440><c> well</c><00:06:55.039><c> I</c>

00:06:55.150 --> 00:06:55.160 align:start position:0%
for my pick of the week okay well I
 

00:06:55.160 --> 00:06:57.189 align:start position:0%
for my pick of the week okay well I
spoil<00:06:55.560><c> this</c><00:06:55.680><c> at</c><00:06:55.800><c> the</c><00:06:55.919><c> beginning</c><00:06:56.319><c> but</c><00:06:56.599><c> yes</c><00:06:57.080><c> a</c>

00:06:57.189 --> 00:06:57.199 align:start position:0%
spoil this at the beginning but yes a
 

00:06:57.199 --> 00:06:59.110 align:start position:0%
spoil this at the beginning but yes a
new<00:06:57.360><c> Beyonce</c><00:06:57.759><c> album</c><00:06:58.039><c> came</c><00:06:58.160><c> out</c><00:06:58.400><c> last</c><00:06:58.639><c> week</c>

00:06:59.110 --> 00:06:59.120 align:start position:0%
new Beyonce album came out last week
 

00:06:59.120 --> 00:07:01.110 align:start position:0%
new Beyonce album came out last week
it's<00:06:59.280><c> called</c><00:06:59.720><c> Cowboy</c><00:07:00.039><c> Carter</c><00:07:00.720><c> and</c><00:07:00.879><c> you</c><00:07:00.960><c> should</c>

00:07:01.110 --> 00:07:01.120 align:start position:0%
it's called Cowboy Carter and you should
 

00:07:01.120 --> 00:07:02.830 align:start position:0%
it's called Cowboy Carter and you should
definitely<00:07:01.440><c> listen</c><00:07:01.680><c> to</c><00:07:01.840><c> it</c><00:07:02.440><c> uh</c><00:07:02.520><c> there's</c><00:07:02.720><c> a</c>

00:07:02.830 --> 00:07:02.840 align:start position:0%
definitely listen to it uh there's a
 

00:07:02.840 --> 00:07:04.830 align:start position:0%
definitely listen to it uh there's a
bunch<00:07:03.000><c> of</c><00:07:03.120><c> discourse</c><00:07:03.599><c> about</c><00:07:03.800><c> Beyonce's</c><00:07:04.400><c> cover</c>

00:07:04.830 --> 00:07:04.840 align:start position:0%
bunch of discourse about Beyonce's cover
 

00:07:04.840 --> 00:07:07.070 align:start position:0%
bunch of discourse about Beyonce's cover
of<00:07:05.000><c> Dolly</c><00:07:05.319><c> Parton</c><00:07:05.680><c> Jolene</c><00:07:06.680><c> and</c><00:07:06.840><c> how</c><00:07:06.960><c> she</c>

00:07:07.070 --> 00:07:07.080 align:start position:0%
of Dolly Parton Jolene and how she
 

00:07:07.080 --> 00:07:08.830 align:start position:0%
of Dolly Parton Jolene and how she
changed<00:07:07.360><c> the</c><00:07:07.440><c> meaning</c><00:07:07.720><c> of</c><00:07:07.840><c> the</c><00:07:08.000><c> song</c><00:07:08.639><c> I</c><00:07:08.720><c> don't</c>

00:07:08.830 --> 00:07:08.840 align:start position:0%
changed the meaning of the song I don't
 

00:07:08.840 --> 00:07:10.270 align:start position:0%
changed the meaning of the song I don't
really<00:07:09.000><c> care</c><00:07:09.160><c> about</c><00:07:09.360><c> any</c><00:07:09.479><c> of</c><00:07:09.639><c> that</c><00:07:10.039><c> I</c><00:07:10.160><c> care</c>

00:07:10.270 --> 00:07:10.280 align:start position:0%
really care about any of that I care
 

00:07:10.280 --> 00:07:12.390 align:start position:0%
really care about any of that I care
about<00:07:10.479><c> good</c><00:07:10.639><c> music</c><00:07:11.160><c> and</c><00:07:11.639><c> this</c><00:07:11.759><c> album's</c><00:07:12.120><c> great</c>

00:07:12.390 --> 00:07:12.400 align:start position:0%
about good music and this album's great
 

00:07:12.400 --> 00:07:14.150 align:start position:0%
about good music and this album's great
anyway<00:07:12.759><c> let</c><00:07:12.919><c> me</c><00:07:13.039><c> know</c><00:07:13.280><c> your</c><00:07:13.479><c> favorite</c><00:07:13.759><c> Beyonce</c>

00:07:14.150 --> 00:07:14.160 align:start position:0%
anyway let me know your favorite Beyonce
 

00:07:14.160 --> 00:07:17.029 align:start position:0%
anyway let me know your favorite Beyonce
track<00:07:14.800><c> uh</c><00:07:14.879><c> on</c><00:07:15.120><c> this</c><00:07:15.240><c> album</c><00:07:15.560><c> or</c><00:07:15.919><c> otherwise</c><00:07:16.840><c> any</c>

00:07:17.029 --> 00:07:17.039 align:start position:0%
track uh on this album or otherwise any
 

00:07:17.039 --> 00:07:19.309 align:start position:0%
track uh on this album or otherwise any
Destiny's<00:07:17.400><c> Child</c><00:07:17.680><c> fans</c><00:07:18.039><c> up</c><00:07:18.280><c> there</c><00:07:18.759><c> out</c><00:07:19.000><c> there</c>

00:07:19.309 --> 00:07:19.319 align:start position:0%
Destiny's Child fans up there out there
 

00:07:19.319 --> 00:07:21.110 align:start position:0%
Destiny's Child fans up there out there
let<00:07:19.440><c> me</c><00:07:19.599><c> know</c><00:07:20.240><c> but</c><00:07:20.400><c> you</c><00:07:20.479><c> can</c><00:07:20.599><c> also</c><00:07:20.759><c> let</c><00:07:20.879><c> me</c><00:07:20.960><c> know</c>

00:07:21.110 --> 00:07:21.120 align:start position:0%
let me know but you can also let me know
 

00:07:21.120 --> 00:07:22.189 align:start position:0%
let me know but you can also let me know
your<00:07:21.319><c> thoughts</c><00:07:21.520><c> on</c><00:07:21.720><c> any</c><00:07:21.840><c> of</c><00:07:21.919><c> the</c><00:07:22.039><c> other</c>

00:07:22.189 --> 00:07:22.199 align:start position:0%
your thoughts on any of the other
 

00:07:22.199 --> 00:07:23.909 align:start position:0%
your thoughts on any of the other
stories<00:07:22.479><c> that</c><00:07:22.599><c> we</c><00:07:22.720><c> covered</c><00:07:23.039><c> this</c><00:07:23.160><c> week</c><00:07:23.440><c> in</c><00:07:23.759><c> the</c>

00:07:23.909 --> 00:07:23.919 align:start position:0%
stories that we covered this week in the
 

00:07:23.919 --> 00:07:26.230 align:start position:0%
stories that we covered this week in the
comments<00:07:24.319><c> down</c><00:07:24.520><c> below</c><00:07:25.440><c> and</c><00:07:25.879><c> uh</c><00:07:26.000><c> that's</c><00:07:26.160><c> going</c>

00:07:26.230 --> 00:07:26.240 align:start position:0%
comments down below and uh that's going
 

00:07:26.240 --> 00:07:28.390 align:start position:0%
comments down below and uh that's going
to<00:07:26.360><c> do</c><00:07:26.479><c> it</c><00:07:26.560><c> for</c><00:07:26.759><c> me</c><00:07:27.120><c> if</c><00:07:27.199><c> you</c><00:07:27.360><c> like</c><00:07:27.599><c> this</c><00:07:27.759><c> episode</c>

00:07:28.390 --> 00:07:28.400 align:start position:0%
to do it for me if you like this episode
 

00:07:28.400 --> 00:07:29.629 align:start position:0%
to do it for me if you like this episode
please<00:07:28.639><c> leave</c><00:07:28.800><c> a</c><00:07:28.919><c> like</c><00:07:29.080><c> it</c><00:07:29.199><c> helps</c><00:07:29.560><c> the</c>

00:07:29.629 --> 00:07:29.639 align:start position:0%
please leave a like it helps the
 

00:07:29.639 --> 00:07:31.589 align:start position:0%
please leave a like it helps the
algorithm<00:07:30.280><c> and</c><00:07:30.639><c> go</c><00:07:30.759><c> ahead</c><00:07:30.919><c> and</c><00:07:31.039><c> subscribe</c><00:07:31.400><c> to</c>

00:07:31.589 --> 00:07:31.599 align:start position:0%
algorithm and go ahead and subscribe to
 

00:07:31.599 --> 00:07:33.150 align:start position:0%
algorithm and go ahead and subscribe to
github's<00:07:32.039><c> YouTube</c><00:07:32.360><c> channel</c><00:07:32.800><c> for</c><00:07:32.919><c> all</c><00:07:33.039><c> your</c>

00:07:33.150 --> 00:07:33.160 align:start position:0%
github's YouTube channel for all your
 

00:07:33.160 --> 00:07:39.230 align:start position:0%
github's YouTube channel for all your
nerd<00:07:33.440><c> needs</c><00:07:34.199><c> see</c><00:07:34.360><c> you</c><00:07:34.520><c> next</c>

00:07:39.230 --> 00:07:39.240 align:start position:0%
 
 

00:07:39.240 --> 00:07:42.240 align:start position:0%
 
time

