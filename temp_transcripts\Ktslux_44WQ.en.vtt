WEBVTT
Kind: captions
Language: en

00:00:08.160 --> 00:00:10.150 align:start position:0%
 
Generally<00:00:08.639><c> we</c><00:00:08.880><c> differentiate</c><00:00:09.440><c> between</c><00:00:09.840><c> five</c>

00:00:10.150 --> 00:00:10.160 align:start position:0%
Generally we differentiate between five
 

00:00:10.160 --> 00:00:12.310 align:start position:0%
Generally we differentiate between five
different<00:00:10.400><c> types</c><00:00:10.880><c> based</c><00:00:11.120><c> on</c><00:00:11.280><c> their</c><00:00:11.519><c> origin.</c>

00:00:12.310 --> 00:00:12.320 align:start position:0%
different types based on their origin.
 

00:00:12.320 --> 00:00:14.950 align:start position:0%
different types based on their origin.
So<00:00:12.559><c> there's</c><00:00:12.800><c> dust</c><00:00:13.120><c> soiling</c><00:00:13.840><c> that</c><00:00:14.160><c> is</c><00:00:14.719><c> most</c>

00:00:14.950 --> 00:00:14.960 align:start position:0%
So there's dust soiling that is most
 

00:00:14.960 --> 00:00:17.269 align:start position:0%
So there's dust soiling that is most
dominant<00:00:15.360><c> in</c><00:00:15.599><c> aid</c><00:00:15.920><c> regions</c><00:00:16.400><c> picked</c><00:00:16.720><c> up</c><00:00:16.880><c> by</c><00:00:17.119><c> the</c>

00:00:17.269 --> 00:00:17.279 align:start position:0%
dominant in aid regions picked up by the
 

00:00:17.279 --> 00:00:19.029 align:start position:0%
dominant in aid regions picked up by the
wind<00:00:17.520><c> from</c><00:00:17.680><c> the</c><00:00:17.840><c> ground</c><00:00:18.080><c> and</c><00:00:18.320><c> deposited</c><00:00:18.880><c> on</c>

00:00:19.029 --> 00:00:19.039 align:start position:0%
wind from the ground and deposited on
 

00:00:19.039 --> 00:00:21.590 align:start position:0%
wind from the ground and deposited on
the<00:00:19.199><c> on</c><00:00:19.439><c> the</c><00:00:19.600><c> modules.</c><00:00:20.240><c> We</c><00:00:20.480><c> have</c><00:00:20.640><c> snow</c><00:00:20.960><c> soiling</c>

00:00:21.590 --> 00:00:21.600 align:start position:0%
the on the modules. We have snow soiling
 

00:00:21.600 --> 00:00:23.910 align:start position:0%
the on the modules. We have snow soiling
more<00:00:21.840><c> in</c><00:00:22.000><c> northern</c><00:00:22.400><c> regions</c><00:00:23.279><c> um</c><00:00:23.439><c> where</c><00:00:23.680><c> it's</c>

00:00:23.910 --> 00:00:23.920 align:start position:0%
more in northern regions um where it's
 

00:00:23.920 --> 00:00:26.230 align:start position:0%
more in northern regions um where it's
snowy<00:00:24.240><c> in</c><00:00:24.400><c> winter.</c><00:00:25.199><c> We</c><00:00:25.439><c> have</c><00:00:25.600><c> pollen</c><00:00:26.000><c> or</c>

00:00:26.230 --> 00:00:26.240 align:start position:0%
snowy in winter. We have pollen or
 

00:00:26.240 --> 00:00:28.790 align:start position:0%
snowy in winter. We have pollen or
organic<00:00:26.640><c> soiling</c><00:00:27.519><c> where</c><00:00:27.840><c> which</c><00:00:28.240><c> comes</c><00:00:28.560><c> from</c>

00:00:28.790 --> 00:00:28.800 align:start position:0%
organic soiling where which comes from
 

00:00:28.800 --> 00:00:31.189 align:start position:0%
organic soiling where which comes from
plants<00:00:29.279><c> in</c><00:00:29.599><c> springtime.</c><00:00:30.480><c> And</c><00:00:30.640><c> then</c><00:00:30.800><c> we</c><00:00:31.039><c> also</c>

00:00:31.189 --> 00:00:31.199 align:start position:0%
plants in springtime. And then we also
 

00:00:31.199 --> 00:00:34.069 align:start position:0%
plants in springtime. And then we also
have<00:00:31.439><c> soiling</c><00:00:32.000><c> from</c><00:00:32.960><c> human</c><00:00:33.280><c> activities</c><00:00:33.840><c> such</c>

00:00:34.069 --> 00:00:34.079 align:start position:0%
have soiling from human activities such
 

00:00:34.079 --> 00:00:36.709 align:start position:0%
have soiling from human activities such
as<00:00:34.239><c> agriculture</c><00:00:34.880><c> or</c><00:00:35.120><c> industry</c><00:00:35.920><c> which</c><00:00:36.160><c> can</c><00:00:36.399><c> be</c>

00:00:36.709 --> 00:00:36.719 align:start position:0%
as agriculture or industry which can be
 

00:00:36.719 --> 00:00:38.869 align:start position:0%
as agriculture or industry which can be
fairly<00:00:37.120><c> difficult</c><00:00:37.600><c> to</c><00:00:37.840><c> describe</c><00:00:38.320><c> because</c><00:00:38.640><c> it</c>

00:00:38.869 --> 00:00:38.879 align:start position:0%
fairly difficult to describe because it
 

00:00:38.879 --> 00:00:41.750 align:start position:0%
fairly difficult to describe because it
can<00:00:39.600><c> be</c><00:00:39.840><c> very</c><00:00:40.559><c> different</c><00:00:40.879><c> in</c><00:00:41.120><c> kind</c><00:00:41.360><c> and</c><00:00:41.680><c> then</c>

00:00:41.750 --> 00:00:41.760 align:start position:0%
can be very different in kind and then
 

00:00:41.760 --> 00:00:43.750 align:start position:0%
can be very different in kind and then
how<00:00:41.840><c> it</c><00:00:42.000><c> acts</c><00:00:42.239><c> upon</c><00:00:42.480><c> the</c><00:00:42.640><c> modules.</c><00:00:43.360><c> And</c><00:00:43.520><c> we</c>

00:00:43.750 --> 00:00:43.760 align:start position:0%
how it acts upon the modules. And we
 

00:00:43.760 --> 00:00:46.150 align:start position:0%
how it acts upon the modules. And we
have<00:00:44.000><c> of</c><00:00:44.239><c> course</c><00:00:44.559><c> wildlife</c><00:00:45.200><c> such</c><00:00:45.360><c> as</c><00:00:45.520><c> birds</c>

00:00:46.150 --> 00:00:46.160 align:start position:0%
have of course wildlife such as birds
 

00:00:46.160 --> 00:00:52.150 align:start position:0%
have of course wildlife such as birds
that<00:00:46.480><c> also</c><00:00:47.039><c> add</c><00:00:47.280><c> to</c><00:00:47.440><c> the</c><00:00:47.600><c> soiling</c><00:00:48.000><c> mix.</c>

00:00:52.150 --> 00:00:52.160 align:start position:0%
 
 

00:00:52.160 --> 00:00:54.549 align:start position:0%
 
can<00:00:52.480><c> depending</c><00:00:52.879><c> on</c><00:00:52.960><c> the</c><00:00:53.199><c> region</c><00:00:54.079><c> um</c><00:00:54.320><c> be</c>

00:00:54.549 --> 00:00:54.559 align:start position:0%
can depending on the region um be
 

00:00:54.559 --> 00:00:57.270 align:start position:0%
can depending on the region um be
somewhere<00:00:55.199><c> between</c><00:00:55.680><c> in</c><00:00:55.920><c> average</c><00:00:56.239><c> per</c><00:00:56.480><c> year</c>

00:00:57.270 --> 00:00:57.280 align:start position:0%
somewhere between in average per year
 

00:00:57.280 --> 00:01:01.830 align:start position:0%
somewhere between in average per year
from<00:00:57.600><c> 1%</c><00:00:58.480><c> up</c><00:00:58.719><c> to</c><00:00:59.039><c> maybe</c><00:00:59.440><c> 5</c><00:00:59.760><c> 10%</c><00:01:00.239><c> if</c><00:01:00.559><c> untreated.</c>

00:01:01.830 --> 00:01:01.840 align:start position:0%
from 1% up to maybe 5 10% if untreated.
 

00:01:01.840 --> 00:01:03.910 align:start position:0%
from 1% up to maybe 5 10% if untreated.
Momentarily<00:01:02.559><c> you</c><00:01:02.800><c> can</c><00:01:02.960><c> reach</c><00:01:03.199><c> values</c><00:01:03.680><c> maybe</c>

00:01:03.910 --> 00:01:03.920 align:start position:0%
Momentarily you can reach values maybe
 

00:01:03.920 --> 00:01:06.550 align:start position:0%
Momentarily you can reach values maybe
during<00:01:04.239><c> a</c><00:01:04.479><c> dry</c><00:01:04.799><c> season</c><00:01:05.600><c> um</c><00:01:05.840><c> soiling</c><00:01:06.320><c> can</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
during a dry season um soiling can
 

00:01:06.560 --> 00:01:09.270 align:start position:0%
during a dry season um soiling can
momentarily<00:01:07.280><c> build</c><00:01:07.520><c> up</c><00:01:07.760><c> up</c><00:01:07.920><c> to</c><00:01:08.080><c> maybe</c><00:01:08.320><c> 20</c><00:01:08.640><c> 30%</c>

00:01:09.270 --> 00:01:09.280 align:start position:0%
momentarily build up up to maybe 20 30%
 

00:01:09.280 --> 00:01:11.350 align:start position:0%
momentarily build up up to maybe 20 30%
but<00:01:09.520><c> usually</c><00:01:09.760><c> in</c><00:01:10.000><c> that</c><00:01:10.159><c> regions</c><00:01:10.720><c> rain</c><00:01:11.040><c> cleans</c>

00:01:11.350 --> 00:01:11.360 align:start position:0%
but usually in that regions rain cleans
 

00:01:11.360 --> 00:01:13.830 align:start position:0%
but usually in that regions rain cleans
away<00:01:12.159><c> uh</c><00:01:12.320><c> most</c><00:01:12.560><c> of</c><00:01:12.640><c> it</c><00:01:12.880><c> so</c><00:01:13.119><c> that</c><00:01:13.280><c> you</c><00:01:13.520><c> wouldn't</c>

00:01:13.830 --> 00:01:13.840 align:start position:0%
away uh most of it so that you wouldn't
 

00:01:13.840 --> 00:01:16.550 align:start position:0%
away uh most of it so that you wouldn't
expect<00:01:14.240><c> this</c><00:01:14.479><c> kind</c><00:01:14.640><c> of</c><00:01:14.799><c> value</c><00:01:15.840><c> uh</c><00:01:16.080><c> in</c><00:01:16.320><c> the</c>

00:01:16.550 --> 00:01:16.560 align:start position:0%
expect this kind of value uh in the
 

00:01:16.560 --> 00:01:19.030 align:start position:0%
expect this kind of value uh in the
global<00:01:16.880><c> average</c><00:01:17.680><c> exceptions</c><00:01:18.240><c> of</c><00:01:18.479><c> course</c><00:01:18.799><c> when</c>

00:01:19.030 --> 00:01:19.040 align:start position:0%
global average exceptions of course when
 

00:01:19.040 --> 00:01:21.350 align:start position:0%
global average exceptions of course when
you<00:01:19.200><c> are</c><00:01:19.360><c> in</c><00:01:19.520><c> the</c><00:01:19.759><c> Middle</c><00:01:20.000><c> East</c><00:01:20.320><c> in</c><00:01:20.720><c> some</c><00:01:21.040><c> very</c>

00:01:21.350 --> 00:01:21.360 align:start position:0%
you are in the Middle East in some very
 

00:01:21.360 --> 00:01:24.310 align:start position:0%
you are in the Middle East in some very
dry<00:01:22.080><c> um</c><00:01:22.320><c> deserts</c><00:01:23.280><c> I</c><00:01:23.439><c> think</c><00:01:23.600><c> for</c><00:01:23.759><c> Germany</c><00:01:24.080><c> a</c>

00:01:24.310 --> 00:01:24.320 align:start position:0%
dry um deserts I think for Germany a
 

00:01:24.320 --> 00:01:26.789 align:start position:0%
dry um deserts I think for Germany a
good<00:01:24.400><c> estimate</c><00:01:24.799><c> is</c><00:01:25.040><c> somewhere</c><00:01:25.360><c> between</c><00:01:25.759><c> 2</c><00:01:26.080><c> 3%</c>

00:01:26.789 --> 00:01:26.799 align:start position:0%
good estimate is somewhere between 2 3%
 

00:01:26.799 --> 00:01:30.070 align:start position:0%
good estimate is somewhere between 2 3%
although<00:01:27.920><c> um</c><00:01:28.560><c> there</c><00:01:28.880><c> is</c><00:01:29.040><c> some</c><00:01:29.600><c> phenomenon</c>

00:01:30.070 --> 00:01:30.080 align:start position:0%
although um there is some phenomenon
 

00:01:30.080 --> 00:01:31.990 align:start position:0%
although um there is some phenomenon
where<00:01:30.400><c> soiling</c><00:01:30.799><c> builds</c><00:01:31.040><c> up</c><00:01:31.280><c> over</c><00:01:31.600><c> multiple</c>

00:01:31.990 --> 00:01:32.000 align:start position:0%
where soiling builds up over multiple
 

00:01:32.000 --> 00:01:37.950 align:start position:0%
where soiling builds up over multiple
years<00:01:32.479><c> where</c><00:01:32.799><c> you</c><00:01:32.960><c> can</c><00:01:33.200><c> also</c><00:01:33.520><c> reach</c><00:01:33.759><c> higher</c>

00:01:37.950 --> 00:01:37.960 align:start position:0%
 
 

00:01:37.960 --> 00:01:40.870 align:start position:0%
 
values.<00:01:38.960><c> So</c><00:01:39.520><c> when</c><00:01:39.759><c> it</c><00:01:39.840><c> comes</c><00:01:40.000><c> to</c><00:01:40.159><c> cleaning</c><00:01:40.640><c> I</c>

00:01:40.870 --> 00:01:40.880 align:start position:0%
values. So when it comes to cleaning I
 

00:01:40.880 --> 00:01:43.590 align:start position:0%
values. So when it comes to cleaning I
think<00:01:41.439><c> well</c><00:01:42.079><c> the</c><00:01:42.400><c> effectiveness</c><00:01:42.960><c> as</c><00:01:43.200><c> in</c><00:01:43.439><c> how</c>

00:01:43.590 --> 00:01:43.600 align:start position:0%
think well the effectiveness as in how
 

00:01:43.600 --> 00:01:47.270 align:start position:0%
think well the effectiveness as in how
much<00:01:44.240><c> of</c><00:01:44.479><c> the</c><00:01:44.640><c> dust</c><00:01:45.040><c> does</c><00:01:45.200><c> is</c><00:01:45.439><c> taken</c><00:01:45.759><c> away</c><00:01:46.560><c> um</c>

00:01:47.270 --> 00:01:47.280 align:start position:0%
much of the dust does is taken away um
 

00:01:47.280 --> 00:01:49.510 align:start position:0%
much of the dust does is taken away um
from<00:01:47.680><c> the</c><00:01:47.920><c> cleaning</c><00:01:48.240><c> system</c><00:01:48.799><c> they</c><00:01:49.119><c> the</c>

00:01:49.510 --> 00:01:49.520 align:start position:0%
from the cleaning system they the
 

00:01:49.520 --> 00:01:51.749 align:start position:0%
from the cleaning system they the
cleaning<00:01:49.840><c> systems</c><00:01:50.240><c> don't</c><00:01:50.479><c> differ</c><00:01:50.720><c> that</c><00:01:51.040><c> much.</c>

00:01:51.749 --> 00:01:51.759 align:start position:0%
cleaning systems don't differ that much.
 

00:01:51.759 --> 00:01:54.230 align:start position:0%
cleaning systems don't differ that much.
What<00:01:52.079><c> they</c><00:01:52.399><c> differ</c><00:01:52.640><c> in</c><00:01:52.880><c> is</c><00:01:53.200><c> the</c><00:01:53.600><c> cost</c><00:01:53.920><c> model</c>

00:01:54.230 --> 00:01:54.240 align:start position:0%
What they differ in is the cost model
 

00:01:54.240 --> 00:01:56.870 align:start position:0%
What they differ in is the cost model
and<00:01:54.479><c> the</c><00:01:54.720><c> cost</c><00:01:54.880><c> that</c><00:01:55.119><c> it</c><00:01:55.280><c> takes</c><00:01:55.600><c> to</c><00:01:55.759><c> to</c><00:01:55.920><c> to</c>

00:01:56.870 --> 00:01:56.880 align:start position:0%
and the cost that it takes to to to
 

00:01:56.880 --> 00:01:59.030 align:start position:0%
and the cost that it takes to to to
apply<00:01:57.280><c> the</c><00:01:57.520><c> cleaning.</c><00:01:58.240><c> You</c><00:01:58.479><c> have</c><00:01:58.640><c> manual</c>

00:01:59.030 --> 00:01:59.040 align:start position:0%
apply the cleaning. You have manual
 

00:01:59.040 --> 00:02:01.749 align:start position:0%
apply the cleaning. You have manual
cleaning<00:01:59.439><c> which</c><00:01:59.759><c> may</c><00:02:00.000><c> be</c><00:02:00.880><c> uh</c><00:02:01.040><c> which</c><00:02:01.360><c> takes</c><00:02:01.600><c> a</c>

00:02:01.749 --> 00:02:01.759 align:start position:0%
cleaning which may be uh which takes a
 

00:02:01.759 --> 00:02:03.510 align:start position:0%
cleaning which may be uh which takes a
lot<00:02:01.840><c> of</c><00:02:02.000><c> time</c><00:02:02.079><c> and</c><00:02:02.320><c> a</c><00:02:02.479><c> lot</c><00:02:02.560><c> of</c><00:02:02.719><c> manpower.</c><00:02:03.280><c> They</c>

00:02:03.510 --> 00:02:03.520 align:start position:0%
lot of time and a lot of manpower. They
 

00:02:03.520 --> 00:02:05.590 align:start position:0%
lot of time and a lot of manpower. They
can<00:02:03.680><c> automatize</c><00:02:04.240><c> that</c><00:02:04.560><c> cleaning</c><00:02:05.040><c> and</c><00:02:05.200><c> then</c><00:02:05.360><c> it</c>

00:02:05.590 --> 00:02:05.600 align:start position:0%
can automatize that cleaning and then it
 

00:02:05.600 --> 00:02:07.190 align:start position:0%
can automatize that cleaning and then it
depends<00:02:05.840><c> really</c><00:02:06.079><c> on</c><00:02:06.240><c> the</c><00:02:06.399><c> region.</c><00:02:06.799><c> So</c><00:02:06.960><c> you</c>

00:02:07.190 --> 00:02:07.200 align:start position:0%
depends really on the region. So you
 

00:02:07.200 --> 00:02:09.830 align:start position:0%
depends really on the region. So you
could<00:02:07.360><c> have</c><00:02:07.439><c> a</c><00:02:08.160><c> fully</c><00:02:08.479><c> automous</c><00:02:09.280><c> autonomous</c>

00:02:09.830 --> 00:02:09.840 align:start position:0%
could have a fully automous autonomous
 

00:02:09.840 --> 00:02:11.589 align:start position:0%
could have a fully automous autonomous
cleaning<00:02:10.160><c> robot</c><00:02:10.479><c> with</c><00:02:10.720><c> a</c><00:02:10.879><c> high</c><00:02:11.120><c> upfront</c>

00:02:11.589 --> 00:02:11.599 align:start position:0%
cleaning robot with a high upfront
 

00:02:11.599 --> 00:02:13.430 align:start position:0%
cleaning robot with a high upfront
investment<00:02:12.319><c> which</c><00:02:12.480><c> is</c><00:02:12.640><c> very</c><00:02:12.879><c> cheap</c><00:02:13.040><c> for</c><00:02:13.280><c> each</c>

00:02:13.430 --> 00:02:13.440 align:start position:0%
investment which is very cheap for each
 

00:02:13.440 --> 00:02:14.949 align:start position:0%
investment which is very cheap for each
individual<00:02:13.920><c> cleaning</c><00:02:14.319><c> which</c><00:02:14.560><c> makes</c><00:02:14.720><c> sense</c>

00:02:14.949 --> 00:02:14.959 align:start position:0%
individual cleaning which makes sense
 

00:02:14.959 --> 00:02:18.070 align:start position:0%
individual cleaning which makes sense
for<00:02:15.200><c> very</c><00:02:15.440><c> dry</c><00:02:15.920><c> and</c><00:02:16.160><c> remote</c><00:02:16.560><c> places.</c><00:02:17.440><c> But</c><00:02:17.599><c> then</c>

00:02:18.070 --> 00:02:18.080 align:start position:0%
for very dry and remote places. But then
 

00:02:18.080 --> 00:02:20.710 align:start position:0%
for very dry and remote places. But then
in<00:02:18.560><c> areas</c><00:02:18.959><c> where</c><00:02:19.280><c> you</c><00:02:19.440><c> have</c><00:02:19.520><c> a</c><00:02:19.760><c> dry</c><00:02:20.239><c> season</c><00:02:20.560><c> and</c>

00:02:20.710 --> 00:02:20.720 align:start position:0%
in areas where you have a dry season and
 

00:02:20.720 --> 00:02:22.550 align:start position:0%
in areas where you have a dry season and
a<00:02:20.959><c> wet</c><00:02:21.200><c> season</c><00:02:21.599><c> most</c><00:02:21.840><c> likely</c><00:02:22.080><c> you</c><00:02:22.239><c> will</c><00:02:22.480><c> want</c>

00:02:22.550 --> 00:02:22.560 align:start position:0%
a wet season most likely you will want
 

00:02:22.560 --> 00:02:26.390 align:start position:0%
a wet season most likely you will want
to<00:02:22.720><c> go</c><00:02:23.040><c> with</c><00:02:23.480><c> tractors.</c><00:02:24.560><c> Um</c><00:02:25.200><c> and</c><00:02:25.440><c> then</c><00:02:26.160><c> yeah</c>

00:02:26.390 --> 00:02:26.400 align:start position:0%
to go with tractors. Um and then yeah
 

00:02:26.400 --> 00:02:28.470 align:start position:0%
to go with tractors. Um and then yeah
the<00:02:26.879><c> it</c><00:02:27.200><c> is</c><00:02:27.360><c> really</c><00:02:27.520><c> the</c><00:02:27.760><c> service</c><00:02:28.000><c> price</c><00:02:28.239><c> that</c>

00:02:28.470 --> 00:02:28.480 align:start position:0%
the it is really the service price that
 

00:02:28.480 --> 00:02:33.630 align:start position:0%
the it is really the service price that
defines<00:02:28.800><c> whether</c><00:02:29.120><c> cleaning</c><00:02:29.440><c> makes</c><00:02:29.599><c> sense</c><00:02:29.760><c> or</c>

00:02:33.630 --> 00:02:33.640 align:start position:0%
 
 

00:02:33.640 --> 00:02:35.150 align:start position:0%
 
not.

00:02:35.150 --> 00:02:35.160 align:start position:0%
not.
 

00:02:35.160 --> 00:02:38.550 align:start position:0%
not.
So<00:02:36.160><c> the</c><00:02:37.120><c> kind</c><00:02:37.360><c> of</c><00:02:37.519><c> obvious</c><00:02:37.840><c> answer</c><00:02:38.080><c> that</c><00:02:38.319><c> most</c>

00:02:38.550 --> 00:02:38.560 align:start position:0%
So the kind of obvious answer that most
 

00:02:38.560 --> 00:02:40.869 align:start position:0%
So the kind of obvious answer that most
people<00:02:38.720><c> will</c><00:02:38.879><c> think</c><00:02:38.959><c> of</c><00:02:39.120><c> is</c><00:02:39.360><c> by</c><00:02:39.879><c> measurement</c>

00:02:40.869 --> 00:02:40.879 align:start position:0%
people will think of is by measurement
 

00:02:40.879 --> 00:02:43.350 align:start position:0%
people will think of is by measurement
and<00:02:41.120><c> I</c><00:02:41.519><c> would</c><00:02:41.760><c> al</c><00:02:42.080><c> and</c><00:02:42.239><c> I</c><00:02:42.400><c> would</c><00:02:42.560><c> totally</c><00:02:42.879><c> me</c>

00:02:43.350 --> 00:02:43.360 align:start position:0%
and I would al and I would totally me
 

00:02:43.360 --> 00:02:45.910 align:start position:0%
and I would al and I would totally me
recommend<00:02:43.840><c> measuring</c><00:02:44.400><c> soiling</c><00:02:44.959><c> at</c><00:02:45.200><c> any</c><00:02:45.519><c> site</c>

00:02:45.910 --> 00:02:45.920 align:start position:0%
recommend measuring soiling at any site
 

00:02:45.920 --> 00:02:48.790 align:start position:0%
recommend measuring soiling at any site
where<00:02:46.760><c> soiling</c><00:02:47.760><c> could</c><00:02:48.000><c> potentially</c><00:02:48.400><c> be</c><00:02:48.560><c> a</c>

00:02:48.790 --> 00:02:48.800 align:start position:0%
where soiling could potentially be a
 

00:02:48.800 --> 00:02:50.550 align:start position:0%
where soiling could potentially be a
problem.<00:02:49.360><c> However,</c><00:02:49.760><c> even</c><00:02:49.920><c> a</c><00:02:50.080><c> measurement</c>

00:02:50.550 --> 00:02:50.560 align:start position:0%
problem. However, even a measurement
 

00:02:50.560 --> 00:02:53.030 align:start position:0%
problem. However, even a measurement
just<00:02:50.879><c> describes</c><00:02:51.519><c> the</c><00:02:51.840><c> history.</c><00:02:52.400><c> And</c><00:02:52.560><c> when</c><00:02:52.800><c> you</c>

00:02:53.030 --> 00:02:53.040 align:start position:0%
just describes the history. And when you
 

00:02:53.040 --> 00:02:55.509 align:start position:0%
just describes the history. And when you
want<00:02:53.200><c> to</c><00:02:53.440><c> schedule</c><00:02:54.400><c> cleanings,</c><00:02:55.040><c> you</c><00:02:55.200><c> always</c>

00:02:55.509 --> 00:02:55.519 align:start position:0%
want to schedule cleanings, you always
 

00:02:55.519 --> 00:02:58.790 align:start position:0%
want to schedule cleanings, you always
are<00:02:55.760><c> interested</c><00:02:56.080><c> about</c><00:02:56.239><c> how</c><00:02:56.480><c> soiling</c><00:02:56.959><c> will</c><00:02:58.000><c> um</c>

00:02:58.790 --> 00:02:58.800 align:start position:0%
are interested about how soiling will um
 

00:02:58.800 --> 00:03:00.630 align:start position:0%
are interested about how soiling will um
grow<00:02:59.200><c> in</c><00:02:59.440><c> the</c><00:02:59.599><c> future.</c><00:03:00.000><c> So</c><00:03:00.160><c> for</c><00:03:00.319><c> that,</c><00:03:00.480><c> you</c>

00:03:00.630 --> 00:03:00.640 align:start position:0%
grow in the future. So for that, you
 

00:03:00.640 --> 00:03:02.869 align:start position:0%
grow in the future. So for that, you
will<00:03:00.800><c> always</c><00:03:01.040><c> need</c><00:03:01.200><c> a</c><00:03:01.440><c> model.</c><00:03:02.159><c> And</c><00:03:02.319><c> a</c><00:03:02.560><c> soiling</c>

00:03:02.869 --> 00:03:02.879 align:start position:0%
will always need a model. And a soiling
 

00:03:02.879 --> 00:03:05.470 align:start position:0%
will always need a model. And a soiling
model<00:03:03.680><c> um</c><00:03:03.840><c> can</c><00:03:04.239><c> use</c>

00:03:05.470 --> 00:03:05.480 align:start position:0%
model um can use
 

00:03:05.480 --> 00:03:07.350 align:start position:0%
model um can use
environmental<00:03:06.480><c> data</c><00:03:06.879><c> such</c><00:03:07.040><c> as</c><00:03:07.200><c> the</c>

00:03:07.350 --> 00:03:07.360 align:start position:0%
environmental data such as the
 

00:03:07.360 --> 00:03:08.550 align:start position:0%
environmental data such as the
concentration<00:03:07.760><c> of</c><00:03:07.920><c> particles</c><00:03:08.239><c> in</c><00:03:08.400><c> the</c>

00:03:08.550 --> 00:03:08.560 align:start position:0%
concentration of particles in the
 

00:03:08.560 --> 00:03:11.830 align:start position:0%
concentration of particles in the
atmosphere,<00:03:09.360><c> wind,</c><00:03:10.159><c> uh</c><00:03:10.319><c> rainfall</c><00:03:11.120><c> to</c><00:03:11.519><c> predict</c>

00:03:11.830 --> 00:03:11.840 align:start position:0%
atmosphere, wind, uh rainfall to predict
 

00:03:11.840 --> 00:03:14.949 align:start position:0%
atmosphere, wind, uh rainfall to predict
how<00:03:12.080><c> soiling</c><00:03:13.280><c> um</c><00:03:14.159><c> will</c><00:03:14.400><c> behave</c><00:03:14.720><c> in</c><00:03:14.879><c> the</c>

00:03:14.949 --> 00:03:14.959 align:start position:0%
how soiling um will behave in the
 

00:03:14.959 --> 00:03:16.070 align:start position:0%
how soiling um will behave in the
future.<00:03:15.120><c> And</c><00:03:15.280><c> then</c><00:03:15.440><c> you</c><00:03:15.519><c> can</c><00:03:15.680><c> build</c><00:03:15.840><c> your</c>

00:03:16.070 --> 00:03:16.080 align:start position:0%
future. And then you can build your
 

00:03:16.080 --> 00:03:19.190 align:start position:0%
future. And then you can build your
cleaning<00:03:16.480><c> schedule</c><00:03:17.440><c> on</c><00:03:17.760><c> that.</c>

00:03:19.190 --> 00:03:19.200 align:start position:0%
cleaning schedule on that.
 

00:03:19.200 --> 00:03:21.350 align:start position:0%
cleaning schedule on that.
A<00:03:19.440><c> measurement</c><00:03:19.920><c> helps</c><00:03:20.159><c> you</c><00:03:20.400><c> to</c><00:03:20.560><c> determine</c><00:03:21.040><c> the</c>

00:03:21.350 --> 00:03:21.360 align:start position:0%
A measurement helps you to determine the
 

00:03:21.360 --> 00:03:24.790 align:start position:0%
A measurement helps you to determine the
correct<00:03:22.239><c> model</c><00:03:22.720><c> parameters,</c><00:03:23.519><c> but</c><00:03:23.760><c> a</c><00:03:24.080><c> model</c>

00:03:24.790 --> 00:03:24.800 align:start position:0%
correct model parameters, but a model
 

00:03:24.800 --> 00:03:26.790 align:start position:0%
correct model parameters, but a model
measurement<00:03:25.200><c> itself</c><00:03:26.000><c> won't</c><00:03:26.319><c> tell</c><00:03:26.480><c> you</c><00:03:26.640><c> when</c>

00:03:26.790 --> 00:03:26.800 align:start position:0%
measurement itself won't tell you when
 

00:03:26.800 --> 00:03:31.309 align:start position:0%
measurement itself won't tell you when
to

00:03:31.309 --> 00:03:31.319 align:start position:0%
 
 

00:03:31.319 --> 00:03:34.710 align:start position:0%
 
clean.<00:03:32.360><c> Well,</c><00:03:33.360><c> so</c><00:03:33.599><c> of</c><00:03:33.840><c> course</c><00:03:34.000><c> there</c><00:03:34.239><c> are</c>

00:03:34.710 --> 00:03:34.720 align:start position:0%
clean. Well, so of course there are
 

00:03:34.720 --> 00:03:37.350 align:start position:0%
clean. Well, so of course there are
automization<00:03:35.280><c> is</c><00:03:35.519><c> a</c><00:03:35.680><c> big</c><00:03:35.840><c> one</c><00:03:36.159><c> where</c><00:03:36.879><c> now</c><00:03:37.120><c> we</c>

00:03:37.350 --> 00:03:37.360 align:start position:0%
automization is a big one where now we
 

00:03:37.360 --> 00:03:38.869 align:start position:0%
automization is a big one where now we
have<00:03:37.519><c> cleaning</c><00:03:37.920><c> robots</c><00:03:38.319><c> that</c><00:03:38.560><c> drive</c>

00:03:38.869 --> 00:03:38.879 align:start position:0%
have cleaning robots that drive
 

00:03:38.879 --> 00:03:41.030 align:start position:0%
have cleaning robots that drive
autonomously<00:03:39.760><c> and</c><00:03:40.080><c> before</c><00:03:40.400><c> we</c><00:03:40.560><c> had</c><00:03:40.720><c> cleaning</c>

00:03:41.030 --> 00:03:41.040 align:start position:0%
autonomously and before we had cleaning
 

00:03:41.040 --> 00:03:43.990 align:start position:0%
autonomously and before we had cleaning
robots<00:03:41.280><c> that</c><00:03:41.440><c> you</c><00:03:41.599><c> would</c><00:03:41.840><c> install</c><00:03:42.319><c> fixed</c><00:03:42.720><c> on</c><00:03:43.120><c> a</c>

00:03:43.990 --> 00:03:44.000 align:start position:0%
robots that you would install fixed on a
 

00:03:44.000 --> 00:03:45.509 align:start position:0%
robots that you would install fixed on a
row<00:03:44.080><c> of</c><00:03:44.239><c> trackers</c><00:03:44.640><c> that</c><00:03:44.959><c> could</c><00:03:45.120><c> just</c><00:03:45.280><c> path</c>

00:03:45.509 --> 00:03:45.519 align:start position:0%
row of trackers that could just path
 

00:03:45.519 --> 00:03:47.270 align:start position:0%
row of trackers that could just path
from<00:03:45.680><c> one</c><00:03:45.840><c> end</c><00:03:46.080><c> to</c><00:03:46.239><c> another.</c><00:03:46.799><c> Now</c><00:03:47.040><c> we</c><00:03:47.200><c> have</c>

00:03:47.270 --> 00:03:47.280 align:start position:0%
from one end to another. Now we have
 

00:03:47.280 --> 00:03:48.949 align:start position:0%
from one end to another. Now we have
robots<00:03:47.680><c> the</c><00:03:47.840><c> first</c><00:03:48.080><c> robots</c><00:03:48.480><c> that</c>

00:03:48.949 --> 00:03:48.959 align:start position:0%
robots the first robots that
 

00:03:48.959 --> 00:03:50.869 align:start position:0%
robots the first robots that
autonomously<00:03:49.920><c> clean</c><00:03:50.159><c> one</c><00:03:50.319><c> rack</c><00:03:50.560><c> and</c><00:03:50.720><c> then</c>

00:03:50.869 --> 00:03:50.879 align:start position:0%
autonomously clean one rack and then
 

00:03:50.879 --> 00:03:52.309 align:start position:0%
autonomously clean one rack and then
drive<00:03:51.120><c> to</c><00:03:51.280><c> the</c><00:03:51.360><c> other</c><00:03:51.519><c> and</c><00:03:51.760><c> clean</c><00:03:52.000><c> that</c><00:03:52.159><c> one</c>

00:03:52.309 --> 00:03:52.319 align:start position:0%
drive to the other and clean that one
 

00:03:52.319 --> 00:03:54.470 align:start position:0%
drive to the other and clean that one
and<00:03:52.560><c> drive</c><00:03:52.720><c> to</c><00:03:52.879><c> the</c><00:03:53.040><c> next.</c><00:03:53.680><c> Um</c><00:03:53.920><c> so</c><00:03:54.080><c> I</c><00:03:54.319><c> think</c>

00:03:54.470 --> 00:03:54.480 align:start position:0%
and drive to the next. Um so I think
 

00:03:54.480 --> 00:03:56.949 align:start position:0%
and drive to the next. Um so I think
automization<00:03:55.040><c> is</c><00:03:55.280><c> is</c><00:03:55.519><c> a</c><00:03:55.680><c> big</c><00:03:56.000><c> uh</c><00:03:56.239><c> topic</c><00:03:56.560><c> in</c>

00:03:56.949 --> 00:03:56.959 align:start position:0%
automization is is a big uh topic in
 

00:03:56.959 --> 00:03:59.030 align:start position:0%
automization is is a big uh topic in
cleaning<00:03:57.439><c> as</c><00:03:57.760><c> everything</c><00:03:58.000><c> is</c><00:03:58.159><c> so</c><00:03:58.400><c> regular</c><00:03:58.799><c> and</c>

00:03:59.030 --> 00:03:59.040 align:start position:0%
cleaning as everything is so regular and
 

00:03:59.040 --> 00:04:00.789 align:start position:0%
cleaning as everything is so regular and
so<00:03:59.280><c> well</c><00:03:59.760><c> designed</c><00:04:00.239><c> actually</c><00:04:00.560><c> for</c>

00:04:00.789 --> 00:04:00.799 align:start position:0%
so well designed actually for
 

00:04:00.799 --> 00:04:02.550 align:start position:0%
so well designed actually for
automization.<00:04:01.760><c> However,</c><00:04:02.080><c> there</c><00:04:02.239><c> are</c><00:04:02.400><c> other</c>

00:04:02.550 --> 00:04:02.560 align:start position:0%
automization. However, there are other
 

00:04:02.560 --> 00:04:04.229 align:start position:0%
automization. However, there are other
factors<00:04:02.959><c> that</c><00:04:03.200><c> come</c><00:04:03.360><c> into</c><00:04:03.599><c> play</c><00:04:03.760><c> I</c><00:04:03.920><c> think</c><00:04:04.080><c> that</c>

00:04:04.229 --> 00:04:04.239 align:start position:0%
factors that come into play I think that
 

00:04:04.239 --> 00:04:05.670 align:start position:0%
factors that come into play I think that
are<00:04:04.480><c> changing</c><00:04:04.799><c> a</c><00:04:04.879><c> little</c><00:04:05.040><c> bit</c><00:04:05.120><c> the</c><00:04:05.360><c> cleaning</c>

00:04:05.670 --> 00:04:05.680 align:start position:0%
are changing a little bit the cleaning
 

00:04:05.680 --> 00:04:07.190 align:start position:0%
are changing a little bit the cleaning
landscape<00:04:06.159><c> and</c><00:04:06.319><c> when</c><00:04:06.480><c> it</c><00:04:06.640><c> makes</c><00:04:06.799><c> sense</c><00:04:07.040><c> or</c>

00:04:07.190 --> 00:04:07.200 align:start position:0%
landscape and when it makes sense or
 

00:04:07.200 --> 00:04:08.949 align:start position:0%
landscape and when it makes sense or
not.<00:04:07.519><c> We</c><00:04:07.680><c> see</c><00:04:07.760><c> a</c><00:04:08.000><c> lot</c><00:04:08.080><c> of</c><00:04:08.159><c> negative</c><00:04:08.560><c> prices</c>

00:04:08.949 --> 00:04:08.959 align:start position:0%
not. We see a lot of negative prices
 

00:04:08.959 --> 00:04:10.710 align:start position:0%
not. We see a lot of negative prices
over<00:04:09.120><c> the</c><00:04:09.280><c> course</c><00:04:09.439><c> of</c><00:04:09.519><c> a</c><00:04:09.680><c> day.</c><00:04:10.239><c> Of</c><00:04:10.400><c> course</c><00:04:10.560><c> when</c>

00:04:10.710 --> 00:04:10.720 align:start position:0%
over the course of a day. Of course when
 

00:04:10.720 --> 00:04:12.229 align:start position:0%
over the course of a day. Of course when
that<00:04:10.879><c> happens</c><00:04:11.280><c> you</c><00:04:11.439><c> wouldn't</c><00:04:11.680><c> really</c><00:04:11.920><c> want</c><00:04:12.080><c> to</c>

00:04:12.229 --> 00:04:12.239 align:start position:0%
that happens you wouldn't really want to
 

00:04:12.239 --> 00:04:14.949 align:start position:0%
that happens you wouldn't really want to
clean.<00:04:12.720><c> So</c><00:04:13.120><c> I</c><00:04:13.360><c> think</c><00:04:13.519><c> the</c><00:04:14.000><c> general</c><00:04:14.400><c> economics</c>

00:04:14.949 --> 00:04:14.959 align:start position:0%
clean. So I think the general economics
 

00:04:14.959 --> 00:04:16.870 align:start position:0%
clean. So I think the general economics
of<00:04:15.120><c> cleaning</c><00:04:15.439><c> is</c><00:04:15.680><c> changing</c><00:04:16.000><c> due</c><00:04:16.239><c> to</c><00:04:16.560><c> the</c>

00:04:16.870 --> 00:04:16.880 align:start position:0%
of cleaning is changing due to the
 

00:04:16.880 --> 00:04:17.909 align:start position:0%
of cleaning is changing due to the
market<00:04:17.120><c> being</c><00:04:17.359><c> changing</c><00:04:17.600><c> and</c><00:04:17.759><c> the</c>

00:04:17.909 --> 00:04:17.919 align:start position:0%
market being changing and the
 

00:04:17.919 --> 00:04:20.780 align:start position:0%
market being changing and the
environment<00:04:18.320><c> changing.</c>

00:04:20.780 --> 00:04:20.790 align:start position:0%
environment changing.
 

00:04:20.790 --> 00:04:26.269 align:start position:0%
environment changing.
[Music]

