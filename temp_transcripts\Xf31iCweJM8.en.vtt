WEBVTT
Kind: captions
Language: en

00:00:00.240 --> 00:00:02.230 align:start position:0%
 
now<00:00:00.359><c> we</c><00:00:00.520><c> know</c><00:00:00.719><c> that</c><00:00:00.840><c> to</c><00:00:00.960><c> build</c><00:00:01.280><c> tall</c><00:00:02.000><c> we</c><00:00:02.120><c> need</c>

00:00:02.230 --> 00:00:02.240 align:start position:0%
now we know that to build tall we need
 

00:00:02.240 --> 00:00:04.710 align:start position:0%
now we know that to build tall we need
to<00:00:02.399><c> go</c><00:00:02.639><c> deep</c><00:00:02.960><c> with</c><00:00:03.080><c> the</c><00:00:03.279><c> foundation</c><00:00:04.279><c> the</c><00:00:04.400><c> birge</c>

00:00:04.710 --> 00:00:04.720 align:start position:0%
to go deep with the foundation the birge
 

00:00:04.720 --> 00:00:07.309 align:start position:0%
to go deep with the foundation the birge
is<00:00:04.880><c> no</c><00:00:05.440><c> exception</c><00:00:06.440><c> the</c><00:00:06.600><c> concrete</c><00:00:07.000><c> slab</c>

00:00:07.309 --> 00:00:07.319 align:start position:0%
is no exception the concrete slab
 

00:00:07.319 --> 00:00:09.709 align:start position:0%
is no exception the concrete slab
Foundation<00:00:07.839><c> is</c><00:00:08.120><c> extremely</c><00:00:08.639><c> solid</c><00:00:09.320><c> at</c><00:00:09.519><c> more</c>

00:00:09.709 --> 00:00:09.719 align:start position:0%
Foundation is extremely solid at more
 

00:00:09.719 --> 00:00:12.629 align:start position:0%
Foundation is extremely solid at more
than<00:00:09.960><c> 12</c><00:00:10.360><c> foot</c><00:00:10.920><c> thick</c><00:00:11.920><c> and</c><00:00:12.080><c> that</c><00:00:12.240><c> might</c><00:00:12.400><c> be</c>

00:00:12.629 --> 00:00:12.639 align:start position:0%
than 12 foot thick and that might be
 

00:00:12.639 --> 00:00:15.749 align:start position:0%
than 12 foot thick and that might be
enough<00:00:13.200><c> in</c><00:00:13.360><c> other</c><00:00:13.759><c> places</c><00:00:14.759><c> but</c><00:00:14.920><c> Dubai</c><00:00:15.519><c> is</c><00:00:15.599><c> a</c>

00:00:15.749 --> 00:00:15.759 align:start position:0%
enough in other places but Dubai is a
 

00:00:15.759 --> 00:00:18.550 align:start position:0%
enough in other places but Dubai is a
desert<00:00:16.640><c> and</c><00:00:16.840><c> sand</c><00:00:17.199><c> is</c>

00:00:18.550 --> 00:00:18.560 align:start position:0%
desert and sand is
 

00:00:18.560 --> 00:00:22.070 align:start position:0%
desert and sand is
soft<00:00:19.560><c> so</c><00:00:19.760><c> in</c><00:00:19.920><c> addition</c><00:00:20.480><c> to</c><00:00:20.640><c> the</c><00:00:20.800><c> concrete</c><00:00:21.199><c> slab</c>

00:00:22.070 --> 00:00:22.080 align:start position:0%
soft so in addition to the concrete slab
 

00:00:22.080 --> 00:00:25.910 align:start position:0%
soft so in addition to the concrete slab
there<00:00:22.240><c> are</c><00:00:22.880><c> 192</c><00:00:23.680><c> hanging</c><00:00:24.160><c> piles</c><00:00:25.080><c> driving</c><00:00:25.599><c> more</c>

00:00:25.910 --> 00:00:25.920 align:start position:0%
there are 192 hanging piles driving more
 

00:00:25.920 --> 00:00:29.390 align:start position:0%
there are 192 hanging piles driving more
than<00:00:26.840><c> 140</c><00:00:27.240><c> ft</c><00:00:27.679><c> into</c><00:00:27.960><c> the</c><00:00:28.160><c> ground</c><00:00:29.160><c> together</c>

00:00:29.390 --> 00:00:29.400 align:start position:0%
than 140 ft into the ground together
 

00:00:29.400 --> 00:00:31.790 align:start position:0%
than 140 ft into the ground together
with<00:00:29.519><c> the</c><00:00:29.640><c> slab</c><00:00:30.359><c> it</c><00:00:30.599><c> provides</c><00:00:31.000><c> enough</c><00:00:31.279><c> support</c>

00:00:31.790 --> 00:00:31.800 align:start position:0%
with the slab it provides enough support
 

00:00:31.800 --> 00:00:32.869 align:start position:0%
with the slab it provides enough support
for

00:00:32.869 --> 00:00:32.879 align:start position:0%
for
 

00:00:32.879 --> 00:00:34.830 align:start position:0%
for
450,000

00:00:34.830 --> 00:00:34.840 align:start position:0%
450,000
 

00:00:34.840 --> 00:00:37.630 align:start position:0%
450,000
tons<00:00:35.840><c> and</c><00:00:36.000><c> that</c><00:00:36.120><c> might</c><00:00:36.280><c> be</c><00:00:36.480><c> enough</c><00:00:36.840><c> in</c><00:00:37.000><c> other</c>

00:00:37.630 --> 00:00:37.640 align:start position:0%
tons and that might be enough in other
 

00:00:37.640 --> 00:00:41.110 align:start position:0%
tons and that might be enough in other
places<00:00:38.640><c> but</c><00:00:38.840><c> Dubai</c><00:00:39.160><c> is</c><00:00:39.320><c> by</c><00:00:39.440><c> the</c><00:00:39.600><c> Sea</c><00:00:40.600><c> and</c><00:00:40.840><c> at</c><00:00:40.960><c> a</c>

00:00:41.110 --> 00:00:41.120 align:start position:0%
places but Dubai is by the Sea and at a
 

00:00:41.120 --> 00:00:43.430 align:start position:0%
places but Dubai is by the Sea and at a
depth<00:00:41.360><c> of</c><00:00:41.480><c> more</c><00:00:41.640><c> than</c><00:00:41.879><c> 100</c><00:00:42.160><c> feet</c><00:00:42.680><c> Salty</c><00:00:43.079><c> Sea</c>

00:00:43.430 --> 00:00:43.440 align:start position:0%
depth of more than 100 feet Salty Sea
 

00:00:43.440 --> 00:00:45.270 align:start position:0%
depth of more than 100 feet Salty Sea
water<00:00:43.640><c> seeps</c><00:00:44.120><c> into</c><00:00:44.360><c> the</c><00:00:44.520><c> ground</c><00:00:45.079><c> and</c>

00:00:45.270 --> 00:00:45.280 align:start position:0%
water seeps into the ground and
 

00:00:45.280 --> 00:00:47.990 align:start position:0%
water seeps into the ground and
threatens<00:00:45.719><c> to</c><00:00:45.960><c> corrode</c><00:00:46.360><c> the</c><00:00:46.440><c> metal</c><00:00:46.760><c> piles</c><00:00:47.760><c> so</c>

00:00:47.990 --> 00:00:48.000 align:start position:0%
threatens to corrode the metal piles so
 

00:00:48.000 --> 00:00:50.310 align:start position:0%
threatens to corrode the metal piles so
not<00:00:48.160><c> only</c><00:00:48.399><c> do</c><00:00:48.559><c> we</c><00:00:48.719><c> need</c><00:00:49.160><c> a</c><00:00:49.399><c> massive</c><00:00:49.800><c> amount</c><00:00:50.079><c> of</c>

00:00:50.310 --> 00:00:50.320 align:start position:0%
not only do we need a massive amount of
 

00:00:50.320 --> 00:00:53.590 align:start position:0%
not only do we need a massive amount of
concrete<00:00:51.160><c> and</c><00:00:51.399><c> lots</c><00:00:51.600><c> of</c><00:00:51.719><c> Steel</c><00:00:52.120><c> piles</c><00:00:53.039><c> we</c><00:00:53.280><c> also</c>

00:00:53.590 --> 00:00:53.600 align:start position:0%
concrete and lots of Steel piles we also
 

00:00:53.600 --> 00:00:55.869 align:start position:0%
concrete and lots of Steel piles we also
need<00:00:53.960><c> to</c><00:00:54.320><c> have</c><00:00:54.640><c> a</c><00:00:54.879><c> constant</c><00:00:55.280><c> flow</c><00:00:55.559><c> of</c>

00:00:55.869 --> 00:00:55.879 align:start position:0%
need to have a constant flow of
 

00:00:55.879 --> 00:00:58.630 align:start position:0%
need to have a constant flow of
electricity<00:00:56.879><c> through</c><00:00:57.120><c> the</c><00:00:57.440><c> foundation</c><00:00:58.440><c> to</c>

00:00:58.630 --> 00:00:58.640 align:start position:0%
electricity through the foundation to
 

00:00:58.640 --> 00:01:01.709 align:start position:0%
electricity through the foundation to
neutralize<00:00:59.280><c> the</c><00:00:59.399><c> ions</c><00:00:59.719><c> from</c><00:01:00.120><c> sea</c><00:01:00.600><c> water</c><00:01:01.600><c> and</c>

00:01:01.709 --> 00:01:01.719 align:start position:0%
neutralize the ions from sea water and
 

00:01:01.719 --> 00:01:03.389 align:start position:0%
neutralize the ions from sea water and
it's<00:01:01.960><c> only</c><00:01:02.199><c> when</c><00:01:02.320><c> we</c><00:01:02.480><c> combine</c><00:01:02.879><c> all</c><00:01:03.079><c> three</c><00:01:03.239><c> of</c>

00:01:03.389 --> 00:01:03.399 align:start position:0%
it's only when we combine all three of
 

00:01:03.399 --> 00:01:05.229 align:start position:0%
it's only when we combine all three of
these<00:01:03.559><c> Design</c><00:01:03.920><c> Elements</c><00:01:04.879><c> can</c><00:01:05.040><c> we</c>

00:01:05.229 --> 00:01:05.239 align:start position:0%
these Design Elements can we
 

00:01:05.239 --> 00:01:07.230 align:start position:0%
these Design Elements can we
successfully<00:01:05.920><c> build</c><00:01:06.320><c> the</c><00:01:06.439><c> world's</c><00:01:06.840><c> tallest</c>

00:01:07.230 --> 00:01:07.240 align:start position:0%
successfully build the world's tallest
 

00:01:07.240 --> 00:01:08.710 align:start position:0%
successfully build the world's tallest
building<00:01:07.560><c> on</c>

00:01:08.710 --> 00:01:08.720 align:start position:0%
building on
 

00:01:08.720 --> 00:01:11.109 align:start position:0%
building on
top<00:01:09.720><c> even</c><00:01:09.920><c> though</c><00:01:10.080><c> the</c><00:01:10.200><c> foundation</c><00:01:10.720><c> is</c><00:01:10.880><c> inv</c>

00:01:11.109 --> 00:01:11.119 align:start position:0%
top even though the foundation is inv
 

00:01:11.119 --> 00:01:13.749 align:start position:0%
top even though the foundation is inv
visible<00:01:11.439><c> at</c><00:01:11.560><c> all</c><00:01:12.360><c> if</c><00:01:12.479><c> we</c><00:01:12.680><c> don't</c><00:01:13.040><c> pay</c><00:01:13.280><c> enough</c>

00:01:13.749 --> 00:01:13.759 align:start position:0%
visible at all if we don't pay enough
 

00:01:13.759 --> 00:01:16.429 align:start position:0%
visible at all if we don't pay enough
attention<00:01:14.159><c> to</c><00:01:14.360><c> it</c><00:01:15.200><c> we</c><00:01:15.520><c> would</c><00:01:15.720><c> end</c><00:01:16.000><c> up</c><00:01:16.200><c> with</c><00:01:16.320><c> the</c>

00:01:16.429 --> 00:01:16.439 align:start position:0%
attention to it we would end up with the
 

00:01:16.439 --> 00:01:18.390 align:start position:0%
attention to it we would end up with the
leaning<00:01:17.040><c> tower</c>

00:01:18.390 --> 00:01:18.400 align:start position:0%
leaning tower
 

00:01:18.400 --> 00:01:21.350 align:start position:0%
leaning tower
of<00:01:19.400><c> well</c><00:01:19.880><c> if</c><00:01:20.000><c> you</c><00:01:20.079><c> were</c><00:01:20.240><c> going</c><00:01:20.360><c> to</c><00:01:20.439><c> say</c><00:01:20.720><c> Pisa</c>

00:01:21.350 --> 00:01:21.360 align:start position:0%
of well if you were going to say Pisa
 

00:01:21.360 --> 00:01:23.270 align:start position:0%
of well if you were going to say Pisa
you'd<00:01:21.600><c> be</c><00:01:21.799><c> wrong</c><00:01:22.479><c> because</c><00:01:22.720><c> I'm</c><00:01:22.920><c> thinking</c>

00:01:23.270 --> 00:01:23.280 align:start position:0%
you'd be wrong because I'm thinking
 

00:01:23.280 --> 00:01:25.870 align:start position:0%
you'd be wrong because I'm thinking
about<00:01:23.640><c> the</c><00:01:23.759><c> Leaning</c><00:01:24.200><c> Tower</c><00:01:24.479><c> of</c><00:01:24.600><c> San</c><00:01:24.880><c> Francisco</c>

00:01:25.870 --> 00:01:25.880 align:start position:0%
about the Leaning Tower of San Francisco
 

00:01:25.880 --> 00:01:28.510 align:start position:0%
about the Leaning Tower of San Francisco
the<00:01:26.000><c> Millennium</c><00:01:26.479><c> tow</c><00:01:26.680><c> is</c><00:01:26.799><c> on</c><00:01:26.920><c> Market</c><00:01:27.520><c> Street</c>

00:01:28.510 --> 00:01:28.520 align:start position:0%
the Millennium tow is on Market Street
 

00:01:28.520 --> 00:01:31.190 align:start position:0%
the Millennium tow is on Market Street
open<00:01:28.759><c> to</c><00:01:28.960><c> Residents</c><00:01:29.439><c> in</c><00:01:29.600><c> 20</c><00:01:29.920><c> 9</c><00:01:30.759><c> Millennium</c>

00:01:31.190 --> 00:01:31.200 align:start position:0%
open to Residents in 20 9 Millennium
 

00:01:31.200 --> 00:01:32.870 align:start position:0%
open to Residents in 20 9 Millennium
Towers<00:01:31.520><c> has</c><00:01:31.680><c> been</c><00:01:31.840><c> sinking</c><00:01:32.200><c> and</c><00:01:32.360><c> tilting</c>

00:01:32.870 --> 00:01:32.880 align:start position:0%
Towers has been sinking and tilting
 

00:01:32.880 --> 00:01:35.550 align:start position:0%
Towers has been sinking and tilting
since<00:01:33.720><c> costing</c><00:01:34.280><c> hundreds</c><00:01:34.640><c> of</c><00:01:34.960><c> millions</c><00:01:35.320><c> of</c>

00:01:35.550 --> 00:01:35.560 align:start position:0%
since costing hundreds of millions of
 

00:01:35.560 --> 00:01:38.109 align:start position:0%
since costing hundreds of millions of
dollars<00:01:35.960><c> in</c><00:01:36.159><c> repairs</c><00:01:37.000><c> and</c><00:01:37.159><c> causing</c><00:01:37.560><c> enormous</c>

00:01:38.109 --> 00:01:38.119 align:start position:0%
dollars in repairs and causing enormous
 

00:01:38.119 --> 00:01:40.910 align:start position:0%
dollars in repairs and causing enormous
drops<00:01:38.720><c> in</c><00:01:38.960><c> property</c><00:01:39.360><c> value</c><00:01:40.159><c> residents</c><00:01:40.680><c> have</c>

00:01:40.910 --> 00:01:40.920 align:start position:0%
drops in property value residents have
 

00:01:40.920 --> 00:01:43.270 align:start position:0%
drops in property value residents have
reported<00:01:41.360><c> cracked</c><00:01:41.720><c> windows</c><00:01:42.680><c> and</c><00:01:42.920><c> popping</c>

00:01:43.270 --> 00:01:43.280 align:start position:0%
reported cracked windows and popping
 

00:01:43.280 --> 00:01:46.030 align:start position:0%
reported cracked windows and popping
sounds<00:01:43.720><c> during</c><00:01:44.079><c> high</c><00:01:44.360><c> wind</c><00:01:45.360><c> today</c><00:01:45.719><c> you</c><00:01:45.880><c> can</c>

00:01:46.030 --> 00:01:46.040 align:start position:0%
sounds during high wind today you can
 

00:01:46.040 --> 00:01:48.830 align:start position:0%
sounds during high wind today you can
incur<00:01:46.439><c> a</c><00:01:46.520><c> $110,000</c><00:01:47.360><c> HOA</c><00:01:47.840><c> fine</c><00:01:48.360><c> if</c><00:01:48.439><c> you</c><00:01:48.600><c> open</c>

00:01:48.830 --> 00:01:48.840 align:start position:0%
incur a $110,000 HOA fine if you open
 

00:01:48.840 --> 00:01:52.230 align:start position:0%
incur a $110,000 HOA fine if you open
the<00:01:48.960><c> window</c><00:01:49.560><c> at</c><00:01:49.640><c> the</c><00:01:49.759><c> wrong</c><00:01:50.040><c> time</c><00:01:50.200><c> of</c><00:01:50.640><c> day</c>

00:01:52.230 --> 00:01:52.240 align:start position:0%
the window at the wrong time of day
 

00:01:52.240 --> 00:01:54.630 align:start position:0%
the window at the wrong time of day
foundation<00:01:53.240><c> are</c>

00:01:54.630 --> 00:01:54.640 align:start position:0%
foundation are
 

00:01:54.640 --> 00:01:56.550 align:start position:0%
foundation are
important<00:01:55.640><c> but</c><00:01:55.759><c> we're</c><00:01:55.920><c> not</c><00:01:56.079><c> here</c><00:01:56.200><c> to</c><00:01:56.399><c> talk</c>

00:01:56.550 --> 00:01:56.560 align:start position:0%
important but we're not here to talk
 

00:01:56.560 --> 00:01:58.550 align:start position:0%
important but we're not here to talk
about<00:01:56.840><c> architecture</c><00:01:57.439><c> today</c><00:01:57.799><c> we're</c><00:01:58.000><c> here</c><00:01:58.320><c> for</c>

00:01:58.550 --> 00:01:58.560 align:start position:0%
about architecture today we're here for
 

00:01:58.560 --> 00:02:01.510 align:start position:0%
about architecture today we're here for
machine<00:01:58.799><c> learning</c><00:01:59.039><c> and</c><00:01:59.240><c> AI</c><00:01:59.960><c> and</c><00:02:00.079><c> for</c><00:02:00.280><c> mln</c><00:02:00.759><c> aai</c>

00:02:01.510 --> 00:02:01.520 align:start position:0%
machine learning and AI and for mln aai
 

00:02:01.520 --> 00:02:04.149 align:start position:0%
machine learning and AI and for mln aai
the<00:02:01.680><c> foundation</c><00:02:02.680><c> is</c>

00:02:04.149 --> 00:02:04.159 align:start position:0%
the foundation is
 

00:02:04.159 --> 00:02:05.670 align:start position:0%
the foundation is
data

00:02:05.670 --> 00:02:05.680 align:start position:0%
data
 

00:02:05.680 --> 00:02:08.150 align:start position:0%
data
hello<00:02:06.680><c> my</c><00:02:06.840><c> name</c><00:02:06.960><c> is</c><00:02:07.119><c> chunga</c><00:02:07.640><c> CEO</c><00:02:08.039><c> and</c>

00:02:08.150 --> 00:02:08.160 align:start position:0%
hello my name is chunga CEO and
 

00:02:08.160 --> 00:02:10.949 align:start position:0%
hello my name is chunga CEO and
co-founder<00:02:08.640><c> of</c><00:02:08.800><c> Lance</c><00:02:09.119><c> CBE</c><00:02:10.039><c> the</c><00:02:10.200><c> database</c><00:02:10.679><c> for</c>

00:02:10.949 --> 00:02:10.959 align:start position:0%
co-founder of Lance CBE the database for
 

00:02:10.959 --> 00:02:14.390 align:start position:0%
co-founder of Lance CBE the database for
multimodal<00:02:11.680><c> AI</c><00:02:12.440><c> from</c><00:02:12.640><c> rag</c><00:02:13.000><c> to</c><00:02:13.160><c> model</c><00:02:13.480><c> training</c>

00:02:14.390 --> 00:02:14.400 align:start position:0%
multimodal AI from rag to model training
 

00:02:14.400 --> 00:02:17.509 align:start position:0%
multimodal AI from rag to model training
Lance<00:02:14.760><c> CB</c><00:02:15.120><c> makes</c><00:02:15.480><c> massive</c><00:02:15.879><c> scale</c><00:02:16.239><c> AI</c><00:02:16.599><c> not</c><00:02:16.800><c> just</c>

00:02:17.509 --> 00:02:17.519 align:start position:0%
Lance CB makes massive scale AI not just
 

00:02:17.519 --> 00:02:20.550 align:start position:0%
Lance CB makes massive scale AI not just
possible<00:02:18.519><c> but</c><00:02:18.920><c> easy</c><00:02:19.920><c> I</c><00:02:20.040><c> was</c><00:02:20.200><c> one</c><00:02:20.319><c> of</c><00:02:20.400><c> the</c>

00:02:20.550 --> 00:02:20.560 align:start position:0%
possible but easy I was one of the
 

00:02:20.560 --> 00:02:22.110 align:start position:0%
possible but easy I was one of the
original<00:02:20.920><c> contributors</c><00:02:21.480><c> to</c><00:02:21.599><c> the</c><00:02:21.720><c> pandas</c>

00:02:22.110 --> 00:02:22.120 align:start position:0%
original contributors to the pandas
 

00:02:22.120 --> 00:02:23.949 align:start position:0%
original contributors to the pandas
library<00:02:22.920><c> and</c><00:02:23.040><c> have</c><00:02:23.200><c> been</c><00:02:23.319><c> building</c><00:02:23.640><c> data</c>

00:02:23.949 --> 00:02:23.959 align:start position:0%
library and have been building data
 

00:02:23.959 --> 00:02:26.470 align:start position:0%
library and have been building data
tools<00:02:24.239><c> for</c><00:02:24.440><c> almost</c><00:02:24.680><c> two</c><00:02:25.040><c> decades</c><00:02:26.040><c> the</c><00:02:26.160><c> Lance</c>

00:02:26.470 --> 00:02:26.480 align:start position:0%
tools for almost two decades the Lance
 

00:02:26.480 --> 00:02:29.190 align:start position:0%
tools for almost two decades the Lance
CB<00:02:26.800><c> team</c><00:02:27.239><c> consists</c><00:02:27.720><c> of</c><00:02:28.000><c> core</c><00:02:28.280><c> contributors</c><00:02:28.959><c> to</c>

00:02:29.190 --> 00:02:29.200 align:start position:0%
CB team consists of core contributors to
 

00:02:29.200 --> 00:02:32.509 align:start position:0%
CB team consists of core contributors to
Hadoop<00:02:30.040><c> Apache</c><00:02:30.480><c> Arrow</c><00:02:31.120><c> Delta</c><00:02:31.640><c> and</c><00:02:31.800><c> many</c><00:02:32.080><c> other</c>

00:02:32.509 --> 00:02:32.519 align:start position:0%
Hadoop Apache Arrow Delta and many other
 

00:02:32.519 --> 00:02:34.990 align:start position:0%
Hadoop Apache Arrow Delta and many other
major<00:02:32.879><c> data</c><00:02:33.280><c> projects</c><00:02:34.280><c> it</c><00:02:34.360><c> would</c><00:02:34.519><c> not</c><00:02:34.680><c> be</c><00:02:34.800><c> an</c>

00:02:34.990 --> 00:02:35.000 align:start position:0%
major data projects it would not be an
 

00:02:35.000 --> 00:02:37.229 align:start position:0%
major data projects it would not be an
exaggeration<00:02:35.680><c> to</c><00:02:35.879><c> say</c><00:02:36.200><c> that</c><00:02:36.840><c> if</c><00:02:36.959><c> you've</c>

00:02:37.229 --> 00:02:37.239 align:start position:0%
exaggeration to say that if you've
 

00:02:37.239 --> 00:02:39.910 align:start position:0%
exaggeration to say that if you've
worked<00:02:37.599><c> with</c><00:02:37.879><c> data</c><00:02:38.319><c> in</c><00:02:38.440><c> the</c><00:02:38.640><c> past</c><00:02:39.000><c> decade</c>

00:02:39.910 --> 00:02:39.920 align:start position:0%
worked with data in the past decade
 

00:02:39.920 --> 00:02:42.949 align:start position:0%
worked with data in the past decade
you've<00:02:40.159><c> used</c><00:02:40.480><c> the</c><00:02:40.599><c> tools</c><00:02:41.000><c> built</c><00:02:41.280><c> by</c><00:02:41.480><c> this</c><00:02:41.959><c> team</c>

00:02:42.949 --> 00:02:42.959 align:start position:0%
you've used the tools built by this team
 

00:02:42.959 --> 00:02:44.990 align:start position:0%
you've used the tools built by this team
today<00:02:43.360><c> we're</c><00:02:43.599><c> working</c><00:02:43.879><c> on</c><00:02:44.040><c> a</c><00:02:44.200><c> new</c><00:02:44.440><c> open</c><00:02:44.720><c> source</c>

00:02:44.990 --> 00:02:45.000 align:start position:0%
today we're working on a new open source
 

00:02:45.000 --> 00:02:47.630 align:start position:0%
today we're working on a new open source
foundation<00:02:45.400><c> for</c><00:02:45.640><c> data</c><00:02:46.280><c> so</c><00:02:46.480><c> the</c><00:02:46.720><c> AI</c><00:02:47.040><c> ecosystem</c>

00:02:47.630 --> 00:02:47.640 align:start position:0%
foundation for data so the AI ecosystem
 

00:02:47.640 --> 00:02:49.430 align:start position:0%
foundation for data so the AI ecosystem
will<00:02:47.840><c> look</c><00:02:48.080><c> more</c><00:02:48.319><c> like</c><00:02:48.440><c> the</c><00:02:48.599><c> Burge</c><00:02:49.280><c> than</c>

00:02:49.430 --> 00:02:49.440 align:start position:0%
will look more like the Burge than
 

00:02:49.440 --> 00:02:51.990 align:start position:0%
will look more like the Burge than
Millennium

00:02:51.990 --> 00:02:52.000 align:start position:0%
 
 

00:02:52.000 --> 00:02:56.550 align:start position:0%
 
Towers<00:02:53.000><c> first</c><00:02:53.760><c> why</c><00:02:53.959><c> does</c><00:02:54.159><c> AI</c><00:02:54.480><c> data</c><00:02:54.920><c> need</c><00:02:55.599><c> New</c>

00:02:56.550 --> 00:02:56.560 align:start position:0%
Towers first why does AI data need New
 

00:02:56.560 --> 00:02:59.030 align:start position:0%
Towers first why does AI data need New
Foundations<00:02:57.560><c> well</c><00:02:58.319><c> let's</c><00:02:58.519><c> start</c><00:02:58.720><c> with</c><00:02:58.840><c> the</c>

00:02:59.030 --> 00:02:59.040 align:start position:0%
Foundations well let's start with the
 

00:02:59.040 --> 00:03:02.110 align:start position:0%
Foundations well let's start with the
size<00:02:59.920><c> AI</c><00:03:00.280><c> data</c><00:03:00.519><c> is</c><00:03:00.680><c> much</c><00:03:00.959><c> bigger</c><00:03:01.360><c> than</c><00:03:01.599><c> tabular</c>

00:03:02.110 --> 00:03:02.120 align:start position:0%
size AI data is much bigger than tabular
 

00:03:02.120 --> 00:03:04.430 align:start position:0%
size AI data is much bigger than tabular
data<00:03:03.000><c> the</c><00:03:03.159><c> typical</c><00:03:03.519><c> database</c><00:03:03.959><c> table</c><00:03:04.239><c> is</c>

00:03:04.430 --> 00:03:04.440 align:start position:0%
data the typical database table is
 

00:03:04.440 --> 00:03:08.110 align:start position:0%
data the typical database table is
around<00:03:05.280><c> 145</c><00:03:05.920><c> bytes</c><00:03:06.480><c> per</c><00:03:06.680><c> row</c><00:03:07.560><c> when</c><00:03:07.720><c> you</c><00:03:07.920><c> add</c>

00:03:08.110 --> 00:03:08.120 align:start position:0%
around 145 bytes per row when you add
 

00:03:08.120 --> 00:03:10.030 align:start position:0%
around 145 bytes per row when you add
embeddings<00:03:08.640><c> to</c><00:03:08.840><c> it</c><00:03:09.200><c> that</c><00:03:09.400><c> immediately</c>

00:03:10.030 --> 00:03:10.040 align:start position:0%
embeddings to it that immediately
 

00:03:10.040 --> 00:03:13.149 align:start position:0%
embeddings to it that immediately
increases<00:03:10.560><c> it</c><00:03:10.879><c> by</c><00:03:11.159><c> 25</c><00:03:11.760><c> times</c><00:03:12.760><c> and</c><00:03:12.879><c> if</c><00:03:12.959><c> you're</c>

00:03:13.149 --> 00:03:13.159 align:start position:0%
increases it by 25 times and if you're
 

00:03:13.159 --> 00:03:16.350 align:start position:0%
increases it by 25 times and if you're
storing<00:03:13.720><c> images</c><00:03:14.120><c> in</c><00:03:14.239><c> the</c><00:03:14.440><c> table</c><00:03:15.000><c> that's</c><00:03:15.400><c> 500</c>

00:03:16.350 --> 00:03:16.360 align:start position:0%
storing images in the table that's 500
 

00:03:16.360 --> 00:03:19.750 align:start position:0%
storing images in the table that's 500
times<00:03:17.360><c> and</c><00:03:17.560><c> with</c><00:03:17.760><c> videos</c><00:03:18.680><c> we</c><00:03:18.799><c> can</c><00:03:19.239><c> easily</c><00:03:19.599><c> get</c>

00:03:19.750 --> 00:03:19.760 align:start position:0%
times and with videos we can easily get
 

00:03:19.760 --> 00:03:22.550 align:start position:0%
times and with videos we can easily get
up<00:03:19.920><c> to</c><00:03:20.159><c> gigabytes</c><00:03:20.879><c> per</c><00:03:21.080><c> row</c><00:03:21.959><c> which</c><00:03:22.120><c> is</c>

00:03:22.550 --> 00:03:22.560 align:start position:0%
up to gigabytes per row which is
 

00:03:22.560 --> 00:03:24.509 align:start position:0%
up to gigabytes per row which is
hundreds<00:03:22.920><c> of</c><00:03:23.120><c> thousands</c><00:03:23.440><c> of</c><00:03:23.599><c> times</c><00:03:24.080><c> bigger</c>

00:03:24.509 --> 00:03:24.519 align:start position:0%
hundreds of thousands of times bigger
 

00:03:24.519 --> 00:03:27.589 align:start position:0%
hundreds of thousands of times bigger
than<00:03:24.680><c> a</c><00:03:24.879><c> typical</c><00:03:25.319><c> row</c><00:03:25.760><c> of</c><00:03:25.959><c> tabular</c>

00:03:27.589 --> 00:03:27.599 align:start position:0%
than a typical row of tabular
 

00:03:27.599 --> 00:03:31.070 align:start position:0%
than a typical row of tabular
data<00:03:28.599><c> second</c><00:03:29.040><c> not</c><00:03:29.200><c> only</c><00:03:29.480><c> is</c><00:03:29.840><c> the</c><00:03:30.000><c> data</c><00:03:30.519><c> larger</c>

00:03:31.070 --> 00:03:31.080 align:start position:0%
data second not only is the data larger
 

00:03:31.080 --> 00:03:33.270 align:start position:0%
data second not only is the data larger
it's<00:03:31.319><c> also</c><00:03:31.640><c> arriving</c><00:03:32.280><c> faster</c><00:03:32.760><c> than</c><00:03:32.959><c> ever</c>

00:03:33.270 --> 00:03:33.280 align:start position:0%
it's also arriving faster than ever
 

00:03:33.280 --> 00:03:35.830 align:start position:0%
it's also arriving faster than ever
before<00:03:34.280><c> instead</c><00:03:34.560><c> of</c><00:03:34.760><c> being</c><00:03:35.080><c> limited</c><00:03:35.560><c> by</c><00:03:35.680><c> the</c>

00:03:35.830 --> 00:03:35.840 align:start position:0%
before instead of being limited by the
 

00:03:35.840 --> 00:03:37.670 align:start position:0%
before instead of being limited by the
speed<00:03:36.120><c> of</c><00:03:36.280><c> human</c><00:03:36.599><c> input</c><00:03:37.200><c> we</c><00:03:37.319><c> are</c><00:03:37.439><c> now</c>

00:03:37.670 --> 00:03:37.680 align:start position:0%
speed of human input we are now
 

00:03:37.680 --> 00:03:39.710 align:start position:0%
speed of human input we are now
generating<00:03:38.159><c> data</c><00:03:38.439><c> at</c><00:03:38.680><c> thousands</c><00:03:39.040><c> of</c><00:03:39.239><c> tokens</c>

00:03:39.710 --> 00:03:39.720 align:start position:0%
generating data at thousands of tokens
 

00:03:39.720 --> 00:03:41.390 align:start position:0%
generating data at thousands of tokens
per

00:03:41.390 --> 00:03:41.400 align:start position:0%
per
 

00:03:41.400 --> 00:03:43.789 align:start position:0%
per
second<00:03:42.400><c> when</c><00:03:42.560><c> you</c><00:03:42.760><c> put</c><00:03:43.000><c> these</c><00:03:43.120><c> two</c><00:03:43.360><c> facts</c>

00:03:43.789 --> 00:03:43.799 align:start position:0%
second when you put these two facts
 

00:03:43.799 --> 00:03:45.670 align:start position:0%
second when you put these two facts
together<00:03:44.280><c> the</c><00:03:44.439><c> phenomenon</c><00:03:44.920><c> that</c><00:03:45.040><c> we</c><00:03:45.200><c> see</c><00:03:45.560><c> is</c>

00:03:45.670 --> 00:03:45.680 align:start position:0%
together the phenomenon that we see is
 

00:03:45.680 --> 00:03:47.949 align:start position:0%
together the phenomenon that we see is
that<00:03:45.959><c> AI</c><00:03:46.239><c> training</c><00:03:46.599><c> data</c><00:03:46.840><c> sets</c><00:03:47.159><c> Grows</c><00:03:47.680><c> by</c>

00:03:47.949 --> 00:03:47.959 align:start position:0%
that AI training data sets Grows by
 

00:03:47.959 --> 00:03:49.910 align:start position:0%
that AI training data sets Grows by
several<00:03:48.280><c> orders</c><00:03:48.640><c> of</c><00:03:48.840><c> magnitude</c><00:03:49.360><c> every</c><00:03:49.519><c> few</c>

00:03:49.910 --> 00:03:49.920 align:start position:0%
several orders of magnitude every few
 

00:03:49.920 --> 00:03:54.630 align:start position:0%
several orders of magnitude every few
years<00:03:50.920><c> CFR</c><00:03:51.799><c> was</c><00:03:52.040><c> roughly</c><00:03:52.720><c> 163</c><00:03:53.640><c> megabytes</c>

00:03:54.630 --> 00:03:54.640 align:start position:0%
years CFR was roughly 163 megabytes
 

00:03:54.640 --> 00:03:59.149 align:start position:0%
years CFR was roughly 163 megabytes
image<00:03:55.040><c> net</c><00:03:55.360><c> up</c><00:03:55.519><c> to</c><00:03:56.000><c> 150</c><00:03:56.720><c> gigb</c><00:03:57.159><c> or</c><00:03:57.599><c> so</c><00:03:58.599><c> a</c><00:03:58.799><c> modern</c>

00:03:59.149 --> 00:03:59.159 align:start position:0%
image net up to 150 gigb or so a modern
 

00:03:59.159 --> 00:04:02.069 align:start position:0%
image net up to 150 gigb or so a modern
llm<00:03:59.840><c> training</c><00:04:00.200><c> data</c><00:04:00.519><c> set</c><00:04:01.079><c> with</c><00:04:01.319><c> just</c><00:04:01.560><c> text</c>

00:04:02.069 --> 00:04:02.079 align:start position:0%
llm training data set with just text
 

00:04:02.079 --> 00:04:04.589 align:start position:0%
llm training data set with just text
gets<00:04:02.319><c> up</c><00:04:02.480><c> to</c><00:04:02.799><c> around</c><00:04:03.560><c> 10</c>

00:04:04.589 --> 00:04:04.599 align:start position:0%
gets up to around 10
 

00:04:04.599 --> 00:04:06.589 align:start position:0%
gets up to around 10
terabytes<00:04:05.599><c> and</c><00:04:05.760><c> if</c><00:04:05.840><c> you're</c><00:04:06.000><c> working</c><00:04:06.360><c> with</c>

00:04:06.589 --> 00:04:06.599 align:start position:0%
terabytes and if you're working with
 

00:04:06.599 --> 00:04:08.670 align:start position:0%
terabytes and if you're working with
images<00:04:06.959><c> and</c><00:04:07.159><c> videos</c><00:04:07.840><c> this</c><00:04:07.959><c> is</c><00:04:08.200><c> easily</c>

00:04:08.670 --> 00:04:08.680 align:start position:0%
images and videos this is easily
 

00:04:08.680 --> 00:04:10.229 align:start position:0%
images and videos this is easily
petabyte

00:04:10.229 --> 00:04:10.239 align:start position:0%
petabyte
 

00:04:10.239 --> 00:04:13.350 align:start position:0%
petabyte
scale<00:04:11.239><c> in</c><00:04:11.439><c> addition</c><00:04:11.760><c> to</c><00:04:11.959><c> the</c><00:04:12.159><c> scale</c><00:04:13.079><c> AI</c>

00:04:13.350 --> 00:04:13.360 align:start position:0%
scale in addition to the scale AI
 

00:04:13.360 --> 00:04:16.550 align:start position:0%
scale in addition to the scale AI
workloads<00:04:13.959><c> are</c><00:04:14.200><c> also</c><00:04:14.680><c> more</c><00:04:15.359><c> diverse</c><00:04:16.359><c> we're</c>

00:04:16.550 --> 00:04:16.560 align:start position:0%
workloads are also more diverse we're
 

00:04:16.560 --> 00:04:18.909 align:start position:0%
workloads are also more diverse we're
not<00:04:16.720><c> just</c><00:04:16.880><c> running</c><00:04:17.199><c> analytics</c><00:04:17.759><c> anymore</c><00:04:18.680><c> we're</c>

00:04:18.909 --> 00:04:18.919 align:start position:0%
not just running analytics anymore we're
 

00:04:18.919 --> 00:04:21.590 align:start position:0%
not just running analytics anymore we're
now<00:04:19.199><c> training</c><00:04:19.759><c> large</c><00:04:20.120><c> models</c><00:04:20.919><c> often</c><00:04:21.239><c> times</c>

00:04:21.590 --> 00:04:21.600 align:start position:0%
now training large models often times
 

00:04:21.600 --> 00:04:24.030 align:start position:0%
now training large models often times
multimodel<00:04:22.479><c> on</c><00:04:22.680><c> trillions</c><00:04:23.080><c> of</c><00:04:23.280><c> tokens</c><00:04:23.840><c> and</c>

00:04:24.030 --> 00:04:24.040 align:start position:0%
multimodel on trillions of tokens and
 

00:04:24.040 --> 00:04:26.710 align:start position:0%
multimodel on trillions of tokens and
pedabytes<00:04:24.360><c> of</c><00:04:25.040><c> data</c><00:04:26.040><c> to</c><00:04:26.240><c> support</c><00:04:26.560><c> these</c>

00:04:26.710 --> 00:04:26.720 align:start position:0%
pedabytes of data to support these
 

00:04:26.720 --> 00:04:28.790 align:start position:0%
pedabytes of data to support these
training<00:04:27.040><c> runs</c><00:04:27.759><c> we</c><00:04:27.919><c> also</c><00:04:28.120><c> need</c><00:04:28.280><c> to</c><00:04:28.479><c> work</c><00:04:28.680><c> with</c>

00:04:28.790 --> 00:04:28.800 align:start position:0%
training runs we also need to work with
 

00:04:28.800 --> 00:04:32.430 align:start position:0%
training runs we also need to work with
the<00:04:28.960><c> data</c><00:04:29.199><c> for</c><00:04:29.440><c> EXP</c><00:04:30.199><c> exploration</c><00:04:31.320><c> curation</c>

00:04:32.430 --> 00:04:32.440 align:start position:0%
the data for EXP exploration curation
 

00:04:32.440 --> 00:04:35.270 align:start position:0%
the data for EXP exploration curation
evaluation<00:04:33.440><c> and</c><00:04:33.600><c> much</c><00:04:33.800><c> much</c>

00:04:35.270 --> 00:04:35.280 align:start position:0%
evaluation and much much
 

00:04:35.280 --> 00:04:38.110 align:start position:0%
evaluation and much much
more<00:04:36.280><c> if</c><00:04:36.400><c> we</c><00:04:36.600><c> take</c><00:04:37.039><c> just</c><00:04:37.199><c> a</c><00:04:37.360><c> single</c><00:04:37.759><c> training</c>

00:04:38.110 --> 00:04:38.120 align:start position:0%
more if we take just a single training
 

00:04:38.120 --> 00:04:41.029 align:start position:0%
more if we take just a single training
run<00:04:38.400><c> for</c><00:04:38.639><c> example</c><00:04:39.639><c> we</c><00:04:39.800><c> first</c><00:04:40.080><c> need</c><00:04:40.280><c> to</c><00:04:40.639><c> filter</c>

00:04:41.029 --> 00:04:41.039 align:start position:0%
run for example we first need to filter
 

00:04:41.039 --> 00:04:43.629 align:start position:0%
run for example we first need to filter
the<00:04:41.199><c> data</c><00:04:41.720><c> to</c><00:04:41.960><c> get</c><00:04:42.120><c> to</c><00:04:42.280><c> the</c><00:04:42.479><c> right</c><00:04:42.800><c> subset</c><00:04:43.400><c> this</c>

00:04:43.629 --> 00:04:43.639 align:start position:0%
the data to get to the right subset this
 

00:04:43.639 --> 00:04:46.870 align:start position:0%
the data to get to the right subset this
requires<00:04:44.120><c> a</c><00:04:44.400><c> fast</c><00:04:44.960><c> scan</c><00:04:45.680><c> of</c><00:04:45.880><c> the</c><00:04:46.000><c> filtering</c>

00:04:46.870 --> 00:04:46.880 align:start position:0%
requires a fast scan of the filtering
 

00:04:46.880 --> 00:04:48.830 align:start position:0%
requires a fast scan of the filtering
Dimensions<00:04:47.880><c> but</c><00:04:48.039><c> remember</c><00:04:48.360><c> we're</c><00:04:48.520><c> building</c>

00:04:48.830 --> 00:04:48.840 align:start position:0%
Dimensions but remember we're building
 

00:04:48.840 --> 00:04:51.590 align:start position:0%
Dimensions but remember we're building
on<00:04:49.039><c> sand</c><00:04:49.960><c> we</c><00:04:50.080><c> need</c><00:04:50.240><c> to</c><00:04:50.360><c> shuffle</c><00:04:50.759><c> the</c><00:04:50.919><c> data</c><00:04:51.400><c> so</c>

00:04:51.590 --> 00:04:51.600 align:start position:0%
on sand we need to shuffle the data so
 

00:04:51.600 --> 00:04:54.150 align:start position:0%
on sand we need to shuffle the data so
the<00:04:51.759><c> model</c><00:04:52.160><c> isn't</c><00:04:52.600><c> seeing</c><00:04:53.080><c> the</c><00:04:53.199><c> same</c><00:04:53.400><c> rows</c><00:04:54.039><c> in</c>

00:04:54.150 --> 00:04:54.160 align:start position:0%
the model isn't seeing the same rows in
 

00:04:54.160 --> 00:04:56.029 align:start position:0%
the model isn't seeing the same rows in
the<00:04:54.280><c> same</c><00:04:54.479><c> order</c><00:04:54.800><c> all</c><00:04:54.960><c> the</c><00:04:55.120><c> time</c><00:04:55.840><c> this</c>

00:04:56.029 --> 00:04:56.039 align:start position:0%
the same order all the time this
 

00:04:56.039 --> 00:04:59.150 align:start position:0%
the same order all the time this
requires<00:04:56.560><c> fast</c><00:04:56.840><c> random</c><00:04:57.400><c> access</c><00:04:58.400><c> and</c><00:04:58.560><c> finally</c>

00:04:59.150 --> 00:04:59.160 align:start position:0%
requires fast random access and finally
 

00:04:59.160 --> 00:05:01.629 align:start position:0%
requires fast random access and finally
here<00:04:59.320><c> comes</c><00:04:59.520><c> the</c><00:05:00.039><c> seawater</c><00:05:01.039><c> we</c><00:05:01.160><c> need</c><00:05:01.320><c> to</c><00:05:01.479><c> be</c>

00:05:01.629 --> 00:05:01.639 align:start position:0%
here comes the seawater we need to be
 

00:05:01.639 --> 00:05:04.150 align:start position:0%
here comes the seawater we need to be
able<00:05:01.919><c> to</c><00:05:02.280><c> quickly</c><00:05:02.600><c> stream</c><00:05:03.039><c> data</c><00:05:03.720><c> from</c><00:05:03.960><c> these</c>

00:05:04.150 --> 00:05:04.160 align:start position:0%
able to quickly stream data from these
 

00:05:04.160 --> 00:05:07.070 align:start position:0%
able to quickly stream data from these
randomized<00:05:04.800><c> positions</c><00:05:05.639><c> into</c><00:05:06.360><c> gpus</c><00:05:06.880><c> for</c>

00:05:07.070 --> 00:05:07.080 align:start position:0%
randomized positions into gpus for
 

00:05:07.080 --> 00:05:08.790 align:start position:0%
randomized positions into gpus for
training<00:05:07.800><c> which</c><00:05:07.919><c> means</c><00:05:08.199><c> we</c><00:05:08.320><c> have</c><00:05:08.440><c> to</c><00:05:08.560><c> be</c>

00:05:08.790 --> 00:05:08.800 align:start position:0%
training which means we have to be
 

00:05:08.800 --> 00:05:10.950 align:start position:0%
training which means we have to be
effective<00:05:09.479><c> when</c><00:05:09.720><c> working</c><00:05:10.080><c> with</c><00:05:10.320><c> large</c><00:05:10.639><c> blob</c>

00:05:10.950 --> 00:05:10.960 align:start position:0%
effective when working with large blob
 

00:05:10.960 --> 00:05:13.029 align:start position:0%
effective when working with large blob
data<00:05:11.280><c> like</c><00:05:11.520><c> text</c><00:05:11.919><c> image</c><00:05:12.240><c> and</c>

00:05:13.029 --> 00:05:13.039 align:start position:0%
data like text image and
 

00:05:13.039 --> 00:05:15.110 align:start position:0%
data like text image and
videos<00:05:14.039><c> unfortunately</c><00:05:14.720><c> the</c><00:05:14.840><c> current</c>

00:05:15.110 --> 00:05:15.120 align:start position:0%
videos unfortunately the current
 

00:05:15.120 --> 00:05:16.950 align:start position:0%
videos unfortunately the current
foundations<00:05:15.680><c> for</c><00:05:15.919><c> data</c><00:05:16.199><c> was</c><00:05:16.320><c> laid</c><00:05:16.560><c> out</c><00:05:16.800><c> a</c>

00:05:16.950 --> 00:05:16.960 align:start position:0%
foundations for data was laid out a
 

00:05:16.960 --> 00:05:19.790 align:start position:0%
foundations for data was laid out a
decade<00:05:17.280><c> ago</c><00:05:17.520><c> for</c><00:05:17.759><c> tabular</c><00:05:18.280><c> data</c><00:05:19.080><c> and</c><00:05:19.360><c> existing</c>

00:05:19.790 --> 00:05:19.800 align:start position:0%
decade ago for tabular data and existing
 

00:05:19.800 --> 00:05:22.350 align:start position:0%
decade ago for tabular data and existing
systems<00:05:20.280><c> only</c><00:05:20.560><c> support</c><00:05:21.039><c> one</c><00:05:21.400><c> or</c><00:05:21.639><c> two</c><00:05:21.960><c> of</c><00:05:22.120><c> the</c>

00:05:22.350 --> 00:05:22.360 align:start position:0%
systems only support one or two of the
 

00:05:22.360 --> 00:05:24.710 align:start position:0%
systems only support one or two of the
requirements<00:05:23.240><c> never</c><00:05:23.680><c> all</c>

00:05:24.710 --> 00:05:24.720 align:start position:0%
requirements never all
 

00:05:24.720 --> 00:05:27.629 align:start position:0%
requirements never all
three<00:05:25.720><c> I</c><00:05:25.840><c> call</c><00:05:26.120><c> this</c><00:05:26.479><c> the</c><00:05:26.639><c> new</c><00:05:26.919><c> cap</c><00:05:27.199><c> theorem</c>

00:05:27.629 --> 00:05:27.639 align:start position:0%
three I call this the new cap theorem
 

00:05:27.639 --> 00:05:31.110 align:start position:0%
three I call this the new cap theorem
for<00:05:27.880><c> AI</c><00:05:28.199><c> data</c>

00:05:31.110 --> 00:05:31.120 align:start position:0%
for AI data
 

00:05:31.120 --> 00:05:33.350 align:start position:0%
for AI data
the<00:05:31.319><c> consequence</c><00:05:32.160><c> of</c><00:05:32.360><c> building</c><00:05:32.720><c> on</c><00:05:33.000><c> top</c><00:05:33.199><c> of</c>

00:05:33.350 --> 00:05:33.360 align:start position:0%
the consequence of building on top of
 

00:05:33.360 --> 00:05:35.670 align:start position:0%
the consequence of building on top of
the<00:05:33.560><c> existing</c><00:05:34.120><c> foundations</c><00:05:35.120><c> is</c><00:05:35.319><c> that</c><00:05:35.479><c> you</c>

00:05:35.670 --> 00:05:35.680 align:start position:0%
the existing foundations is that you
 

00:05:35.680 --> 00:05:37.510 align:start position:0%
the existing foundations is that you
often<00:05:35.960><c> need</c><00:05:36.199><c> to</c><00:05:36.400><c> have</c><00:05:36.560><c> multiple</c><00:05:37.080><c> copies</c><00:05:37.400><c> of</c>

00:05:37.510 --> 00:05:37.520 align:start position:0%
often need to have multiple copies of
 

00:05:37.520 --> 00:05:39.710 align:start position:0%
often need to have multiple copies of
the<00:05:37.680><c> data</c><00:05:37.919><c> for</c><00:05:38.120><c> different</c><00:05:38.479><c> purposes</c><00:05:39.479><c> when</c><00:05:39.600><c> you</c>

00:05:39.710 --> 00:05:39.720 align:start position:0%
the data for different purposes when you
 

00:05:39.720 --> 00:05:41.710 align:start position:0%
the data for different purposes when you
have<00:05:39.919><c> pedabytes</c><00:05:40.240><c> of</c><00:05:40.560><c> training</c><00:05:40.919><c> data</c><00:05:41.440><c> this</c><00:05:41.560><c> is</c>

00:05:41.710 --> 00:05:41.720 align:start position:0%
have pedabytes of training data this is
 

00:05:41.720 --> 00:05:43.110 align:start position:0%
have pedabytes of training data this is
incredibly

00:05:43.110 --> 00:05:43.120 align:start position:0%
incredibly
 

00:05:43.120 --> 00:05:44.950 align:start position:0%
incredibly
expensive<00:05:44.120><c> and</c><00:05:44.240><c> if</c><00:05:44.360><c> you're</c><00:05:44.560><c> managing</c>

00:05:44.950 --> 00:05:44.960 align:start position:0%
expensive and if you're managing
 

00:05:44.960 --> 00:05:46.990 align:start position:0%
expensive and if you're managing
multiple<00:05:45.360><c> copies</c><00:05:46.039><c> you</c><00:05:46.160><c> need</c><00:05:46.319><c> to</c><00:05:46.520><c> manually</c>

00:05:46.990 --> 00:05:47.000 align:start position:0%
multiple copies you need to manually
 

00:05:47.000 --> 00:05:49.189 align:start position:0%
multiple copies you need to manually
translate<00:05:47.520><c> between</c><00:05:47.800><c> them</c><00:05:48.240><c> keep</c><00:05:48.440><c> them</c><00:05:48.560><c> in</c><00:05:48.720><c> sync</c>

00:05:49.189 --> 00:05:49.199 align:start position:0%
translate between them keep them in sync
 

00:05:49.199 --> 00:05:50.629 align:start position:0%
translate between them keep them in sync
and<00:05:49.280><c> you</c><00:05:49.400><c> have</c><00:05:49.520><c> to</c><00:05:49.639><c> use</c><00:05:49.880><c> different</c><00:05:50.160><c> tools</c><00:05:50.520><c> that</c>

00:05:50.629 --> 00:05:50.639 align:start position:0%
and you have to use different tools that
 

00:05:50.639 --> 00:05:53.029 align:start position:0%
and you have to use different tools that
are<00:05:50.840><c> optimized</c><00:05:51.280><c> for</c><00:05:51.440><c> each</c><00:05:51.639><c> individual</c><00:05:52.080><c> format</c>

00:05:53.029 --> 00:05:53.039 align:start position:0%
are optimized for each individual format
 

00:05:53.039 --> 00:05:54.710 align:start position:0%
are optimized for each individual format
this<00:05:53.160><c> makes</c><00:05:53.360><c> it</c><00:05:53.600><c> overly</c>

00:05:54.710 --> 00:05:54.720 align:start position:0%
this makes it overly
 

00:05:54.720 --> 00:05:57.590 align:start position:0%
this makes it overly
complicated<00:05:55.720><c> and</c><00:05:55.880><c> doing</c><00:05:56.160><c> all</c><00:05:56.319><c> of</c><00:05:56.520><c> that</c><00:05:56.840><c> means</c>

00:05:57.590 --> 00:05:57.600 align:start position:0%
complicated and doing all of that means
 

00:05:57.600 --> 00:05:59.110 align:start position:0%
complicated and doing all of that means
the<00:05:57.759><c> most</c><00:05:58.039><c> expensive</c><00:05:58.520><c> hires</c><00:05:58.840><c> in</c><00:05:58.960><c> your</c>

00:05:59.110 --> 00:05:59.120 align:start position:0%
the most expensive hires in your
 

00:05:59.120 --> 00:06:01.790 align:start position:0%
the most expensive hires in your
organization<00:05:59.960><c> are</c><00:06:00.240><c> spending</c><00:06:01.199><c> way</c><00:06:01.400><c> too</c><00:06:01.600><c> much</c>

00:06:01.790 --> 00:06:01.800 align:start position:0%
organization are spending way too much
 

00:06:01.800 --> 00:06:03.629 align:start position:0%
organization are spending way too much
time<00:06:02.080><c> dealing</c><00:06:02.440><c> with</c><00:06:02.600><c> lowlevel</c><00:06:03.120><c> details</c><00:06:03.520><c> of</c>

00:06:03.629 --> 00:06:03.639 align:start position:0%
time dealing with lowlevel details of
 

00:06:03.639 --> 00:06:05.790 align:start position:0%
time dealing with lowlevel details of
the<00:06:03.800><c> data</c><00:06:04.720><c> instead</c><00:06:05.000><c> of</c><00:06:05.120><c> what</c><00:06:05.240><c> you</c><00:06:05.360><c> hire</c><00:06:05.639><c> them</c>

00:06:05.790 --> 00:06:05.800 align:start position:0%
the data instead of what you hire them
 

00:06:05.800 --> 00:06:08.469 align:start position:0%
the data instead of what you hire them
for<00:06:06.400><c> which</c><00:06:06.520><c> is</c><00:06:06.639><c> to</c><00:06:06.800><c> improve</c><00:06:07.120><c> the</c><00:06:07.280><c> model</c><00:06:07.960><c> or</c><00:06:08.280><c> the</c>

00:06:08.469 --> 00:06:08.479 align:start position:0%
for which is to improve the model or the
 

00:06:08.479 --> 00:06:11.629 align:start position:0%
for which is to improve the model or the
AI<00:06:08.840><c> native</c>

00:06:11.629 --> 00:06:11.639 align:start position:0%
 
 

00:06:11.639 --> 00:06:13.510 align:start position:0%
 
application<00:06:12.639><c> to</c><00:06:12.840><c> work</c><00:06:13.039><c> around</c><00:06:13.319><c> these</c>

00:06:13.510 --> 00:06:13.520 align:start position:0%
application to work around these
 

00:06:13.520 --> 00:06:16.029 align:start position:0%
application to work around these
limitations<00:06:14.479><c> we're</c><00:06:14.759><c> often</c><00:06:15.000><c> forced</c><00:06:15.400><c> to</c><00:06:15.599><c> set</c><00:06:15.800><c> up</c>

00:06:16.029 --> 00:06:16.039 align:start position:0%
limitations we're often forced to set up
 

00:06:16.039 --> 00:06:18.550 align:start position:0%
limitations we're often forced to set up
rubbe<00:06:16.400><c> Goldberg</c><00:06:16.960><c> data</c><00:06:17.199><c> Stacks</c><00:06:18.000><c> where</c><00:06:18.240><c> each</c>

00:06:18.550 --> 00:06:18.560 align:start position:0%
rubbe Goldberg data Stacks where each
 

00:06:18.560 --> 00:06:21.430 align:start position:0%
rubbe Goldberg data Stacks where each
task<00:06:19.199><c> comes</c><00:06:19.560><c> with</c><00:06:19.759><c> its</c><00:06:19.919><c> own</c><00:06:20.240><c> copy</c><00:06:20.479><c> of</c><00:06:20.680><c> data</c><00:06:21.240><c> in</c>

00:06:21.430 --> 00:06:21.440 align:start position:0%
task comes with its own copy of data in
 

00:06:21.440 --> 00:06:22.589 align:start position:0%
task comes with its own copy of data in
single-purpose

00:06:22.589 --> 00:06:22.599 align:start position:0%
single-purpose
 

00:06:22.599 --> 00:06:25.270 align:start position:0%
single-purpose
compute<00:06:23.599><c> navigating</c><00:06:24.160><c> these</c><00:06:24.400><c> systems</c><00:06:25.039><c> can</c><00:06:25.160><c> be</c>

00:06:25.270 --> 00:06:25.280 align:start position:0%
compute navigating these systems can be
 

00:06:25.280 --> 00:06:27.309 align:start position:0%
compute navigating these systems can be
a<00:06:25.440><c> nightmare</c><00:06:26.160><c> and</c><00:06:26.319><c> trying</c><00:06:26.520><c> to</c><00:06:26.680><c> build</c><00:06:26.919><c> a</c><00:06:27.080><c> data</c>

00:06:27.309 --> 00:06:27.319 align:start position:0%
a nightmare and trying to build a data
 

00:06:27.319 --> 00:06:29.670 align:start position:0%
a nightmare and trying to build a data
flywheel<00:06:27.919><c> to</c><00:06:28.080><c> support</c><00:06:28.479><c> rapid</c><00:06:28.800><c> iteration</c><00:06:29.319><c> is</c>

00:06:29.670 --> 00:06:29.680 align:start position:0%
flywheel to support rapid iteration is
 

00:06:29.680 --> 00:06:31.189 align:start position:0%
flywheel to support rapid iteration is
all<00:06:29.919><c> but</c>

00:06:31.189 --> 00:06:31.199 align:start position:0%
all but
 

00:06:31.199 --> 00:06:33.670 align:start position:0%
all but
impossible<00:06:32.199><c> one</c><00:06:32.599><c> autonomous</c><00:06:33.199><c> vehicles</c>

00:06:33.670 --> 00:06:33.680 align:start position:0%
impossible one autonomous vehicles
 

00:06:33.680 --> 00:06:36.469 align:start position:0%
impossible one autonomous vehicles
company<00:06:34.360><c> once</c><00:06:34.639><c> told</c><00:06:34.919><c> me</c><00:06:35.160><c> that</c><00:06:35.560><c> to</c><00:06:35.840><c> navigate</c>

00:06:36.469 --> 00:06:36.479 align:start position:0%
company once told me that to navigate
 

00:06:36.479 --> 00:06:38.710 align:start position:0%
company once told me that to navigate
one<00:06:36.800><c> full</c><00:06:37.080><c> training</c><00:06:37.560><c> cycle</c><00:06:37.960><c> from</c><00:06:38.199><c> analyzing</c>

00:06:38.710 --> 00:06:38.720 align:start position:0%
one full training cycle from analyzing
 

00:06:38.720 --> 00:06:41.430 align:start position:0%
one full training cycle from analyzing
the<00:06:38.880><c> data</c><00:06:39.360><c> to</c><00:06:39.560><c> training</c><00:06:39.960><c> to</c><00:06:40.240><c> evaluation</c><00:06:41.240><c> their</c>

00:06:41.430 --> 00:06:41.440 align:start position:0%
the data to training to evaluation their
 

00:06:41.440 --> 00:06:43.510 align:start position:0%
the data to training to evaluation their
Engineers<00:06:41.960><c> need</c><00:06:42.160><c> to</c><00:06:42.319><c> learn</c><00:06:42.759><c> seven</c><00:06:43.160><c> different</c>

00:06:43.510 --> 00:06:43.520 align:start position:0%
Engineers need to learn seven different
 

00:06:43.520 --> 00:06:45.350 align:start position:0%
Engineers need to learn seven different
systems<00:06:44.199><c> and</c><00:06:44.360><c> write</c><00:06:44.639><c> four</c><00:06:45.000><c> different</c>

00:06:45.350 --> 00:06:45.360 align:start position:0%
systems and write four different
 

00:06:45.360 --> 00:06:50.629 align:start position:0%
systems and write four different
programming<00:06:46.319><c> languages</c><00:06:47.319><c> this</c><00:06:47.680><c> is</c><00:06:48.680><c> pretty</c>

00:06:50.629 --> 00:06:50.639 align:start position:0%
programming languages this is pretty
 

00:06:50.639 --> 00:06:53.350 align:start position:0%
programming languages this is pretty
crazy<00:06:51.639><c> so</c><00:06:52.360><c> we</c><00:06:52.520><c> went</c><00:06:52.720><c> back</c><00:06:52.840><c> to</c><00:06:52.960><c> the</c><00:06:53.080><c> drawing</c>

00:06:53.350 --> 00:06:53.360 align:start position:0%
crazy so we went back to the drawing
 

00:06:53.360 --> 00:06:56.550 align:start position:0%
crazy so we went back to the drawing
board<00:06:53.840><c> and</c><00:06:54.039><c> asked</c><00:06:54.880><c> what</c><00:06:55.160><c> if</c><00:06:55.960><c> we</c><00:06:56.080><c> can</c><00:06:56.280><c> have</c><00:06:56.400><c> it</c>

00:06:56.550 --> 00:06:56.560 align:start position:0%
board and asked what if we can have it
 

00:06:56.560 --> 00:07:01.990 align:start position:0%
board and asked what if we can have it
all<00:06:56.919><c> we</c><00:06:57.000><c> can</c><00:06:57.199><c> have</c><00:06:57.360><c> our</c><00:06:57.639><c> cake</c><00:06:58.039><c> and</c><00:06:58.240><c> eat</c><00:06:58.479><c> it</c><00:06:58.639><c> too</c>

00:07:01.990 --> 00:07:02.000 align:start position:0%
 
 

00:07:02.000 --> 00:07:04.309 align:start position:0%
 
to<00:07:02.160><c> solve</c><00:07:02.440><c> the</c><00:07:02.639><c> problem</c><00:07:03.000><c> at</c><00:07:03.199><c> its</c><00:07:03.400><c> foundation</c>

00:07:04.309 --> 00:07:04.319 align:start position:0%
to solve the problem at its foundation
 

00:07:04.319 --> 00:07:07.270 align:start position:0%
to solve the problem at its foundation
we<00:07:04.520><c> designed</c><00:07:05.280><c> Lance's</c><00:07:05.759><c> colum</c><00:07:06.120><c> or</c><00:07:06.280><c> format</c><00:07:07.000><c> as</c><00:07:07.120><c> a</c>

00:07:07.270 --> 00:07:07.280 align:start position:0%
we designed Lance's colum or format as a
 

00:07:07.280 --> 00:07:10.070 align:start position:0%
we designed Lance's colum or format as a
new<00:07:07.479><c> way</c><00:07:07.680><c> to</c><00:07:07.919><c> store</c><00:07:08.400><c> AI</c><00:07:08.800><c> data</c><00:07:09.240><c> on</c><00:07:09.560><c> persistent</c>

00:07:10.070 --> 00:07:10.080 align:start position:0%
new way to store AI data on persistent
 

00:07:10.080 --> 00:07:14.350 align:start position:0%
new way to store AI data on persistent
storage<00:07:10.960><c> whether</c><00:07:11.280><c> it's</c><00:07:11.720><c> local</c><00:07:12.520><c> mvme</c><00:07:13.520><c> or</c><00:07:14.160><c> an</c>

00:07:14.350 --> 00:07:14.360 align:start position:0%
storage whether it's local mvme or an
 

00:07:14.360 --> 00:07:17.790 align:start position:0%
storage whether it's local mvme or an
object<00:07:14.680><c> store</c><00:07:15.160><c> like</c><00:07:15.800><c> S3</c><00:07:16.800><c> reading</c><00:07:17.160><c> and</c><00:07:17.360><c> writing</c>

00:07:17.790 --> 00:07:17.800 align:start position:0%
object store like S3 reading and writing
 

00:07:17.800 --> 00:07:20.230 align:start position:0%
object store like S3 reading and writing
Lance<00:07:18.240><c> data</c><00:07:18.639><c> goes</c><00:07:18.919><c> through</c><00:07:19.240><c> an</c><00:07:19.440><c> Apache</c><00:07:19.840><c> arrow</c>

00:07:20.230 --> 00:07:20.240 align:start position:0%
Lance data goes through an Apache arrow
 

00:07:20.240 --> 00:07:22.430 align:start position:0%
Lance data goes through an Apache arrow
interface<00:07:20.639><c> in</c><00:07:20.800><c> memory</c><00:07:21.599><c> which</c><00:07:21.759><c> makes</c><00:07:21.960><c> it</c><00:07:22.199><c> easy</c>

00:07:22.430 --> 00:07:22.440 align:start position:0%
interface in memory which makes it easy
 

00:07:22.440 --> 00:07:24.589 align:start position:0%
interface in memory which makes it easy
for<00:07:22.639><c> Lance</c><00:07:22.960><c> to</c><00:07:23.080><c> be</c><00:07:23.199><c> used</c><00:07:23.720><c> by</c><00:07:23.879><c> the</c><00:07:24.080><c> entire</c>

00:07:24.589 --> 00:07:24.599 align:start position:0%
for Lance to be used by the entire
 

00:07:24.599 --> 00:07:27.990 align:start position:0%
for Lance to be used by the entire
ecosystem<00:07:25.160><c> of</c><00:07:25.319><c> data</c><00:07:26.000><c> tools</c><00:07:27.000><c> you</c><00:07:27.120><c> can</c><00:07:27.360><c> run</c>

00:07:27.990 --> 00:07:28.000 align:start position:0%
ecosystem of data tools you can run
 

00:07:28.000 --> 00:07:32.150 align:start position:0%
ecosystem of data tools you can run
analytics<00:07:28.720><c> using</c><00:07:29.199><c> duck</c><00:07:29.639><c> DB</c><00:07:30.599><c> pandas</c>

00:07:32.150 --> 00:07:32.160 align:start position:0%
analytics using duck DB pandas
 

00:07:32.160 --> 00:07:35.189 align:start position:0%
analytics using duck DB pandas
polers<00:07:33.160><c> distributed</c><00:07:33.680><c> ETL</c><00:07:34.199><c> can</c><00:07:34.319><c> be</c><00:07:34.520><c> done</c><00:07:34.919><c> via</c>

00:07:35.189 --> 00:07:35.199 align:start position:0%
polers distributed ETL can be done via
 

00:07:35.199 --> 00:07:39.550 align:start position:0%
polers distributed ETL can be done via
spark<00:07:35.720><c> or</c><00:07:36.039><c> Daft</c><00:07:37.039><c> Lance</c><00:07:37.599><c> comes</c><00:07:37.960><c> with</c><00:07:38.680><c> a</c><00:07:38.960><c> pytorch</c>

00:07:39.550 --> 00:07:39.560 align:start position:0%
spark or Daft Lance comes with a pytorch
 

00:07:39.560 --> 00:07:42.110 align:start position:0%
spark or Daft Lance comes with a pytorch
data<00:07:39.800><c> loader</c><00:07:40.759><c> or</c><00:07:41.039><c> native</c><00:07:41.400><c> Ray</c><00:07:41.680><c> integration</c>

00:07:42.110 --> 00:07:42.120 align:start position:0%
data loader or native Ray integration
 

00:07:42.120 --> 00:07:44.869 align:start position:0%
data loader or native Ray integration
for<00:07:42.280><c> a</c><00:07:42.520><c> training</c><00:07:43.520><c> and</c><00:07:43.720><c> finally</c><00:07:44.360><c> Lance's</c>

00:07:44.869 --> 00:07:44.879 align:start position:0%
for a training and finally Lance's
 

00:07:44.879 --> 00:07:47.710 align:start position:0%
for a training and finally Lance's
indices<00:07:45.800><c> enable</c><00:07:46.400><c> every</c><00:07:46.639><c> Lance</c><00:07:47.039><c> data</c><00:07:47.319><c> set</c><00:07:47.520><c> to</c>

00:07:47.710 --> 00:07:47.720 align:start position:0%
indices enable every Lance data set to
 

00:07:47.720 --> 00:07:52.270 align:start position:0%
indices enable every Lance data set to
support<00:07:48.120><c> vector</c><00:07:48.479><c> and</c><00:07:48.639><c> fulltech</c>

00:07:52.270 --> 00:07:52.280 align:start position:0%
 
 

00:07:52.280 --> 00:07:56.149 align:start position:0%
 
search<00:07:53.280><c> now</c><00:07:53.560><c> let's</c><00:07:53.759><c> zoom</c><00:07:54.000><c> in</c><00:07:54.400><c> on</c><00:07:55.400><c> a</c><00:07:55.520><c> lance</c><00:07:55.879><c> data</c>

00:07:56.149 --> 00:07:56.159 align:start position:0%
search now let's zoom in on a lance data
 

00:07:56.159 --> 00:07:59.149 align:start position:0%
search now let's zoom in on a lance data
set<00:07:56.960><c> imagine</c><00:07:57.360><c> if</c><00:07:57.479><c> you</c><00:07:57.599><c> had</c><00:07:57.720><c> a</c><00:07:57.879><c> data</c><00:07:58.159><c> set</c><00:07:58.560><c> with</c>

00:07:59.149 --> 00:07:59.159 align:start position:0%
set imagine if you had a data set with
 

00:07:59.159 --> 00:08:01.550 align:start position:0%
set imagine if you had a data set with
meta<00:07:59.639><c> data</c><00:07:59.879><c> columns</c><00:08:00.240><c> for</c>

00:08:01.550 --> 00:08:01.560 align:start position:0%
meta data columns for
 

00:08:01.560 --> 00:08:04.550 align:start position:0%
meta data columns for
filtering<00:08:02.560><c> text</c><00:08:03.120><c> image</c><00:08:03.680><c> or</c><00:08:03.919><c> video</c><00:08:04.240><c> columns</c>

00:08:04.550 --> 00:08:04.560 align:start position:0%
filtering text image or video columns
 

00:08:04.560 --> 00:08:07.710 align:start position:0%
filtering text image or video columns
for<00:08:04.759><c> training</c><00:08:05.560><c> and</c><00:08:06.080><c> an</c><00:08:06.240><c> embedding</c><00:08:06.800><c> column</c><00:08:07.479><c> for</c>

00:08:07.710 --> 00:08:07.720 align:start position:0%
for training and an embedding column for
 

00:08:07.720 --> 00:08:10.110 align:start position:0%
for training and an embedding column for
search<00:08:08.000><c> and</c><00:08:08.520><c> retrieval</c><00:08:09.520><c> typically</c><00:08:10.000><c> the</c>

00:08:10.110 --> 00:08:10.120 align:start position:0%
search and retrieval typically the
 

00:08:10.120 --> 00:08:13.110 align:start position:0%
search and retrieval typically the
metadata<00:08:10.639><c> would</c><00:08:10.759><c> be</c><00:08:10.879><c> stored</c><00:08:11.240><c> in</c><00:08:11.440><c> say</c><00:08:12.240><c> bigquery</c>

00:08:13.110 --> 00:08:13.120 align:start position:0%
metadata would be stored in say bigquery
 

00:08:13.120 --> 00:08:15.350 align:start position:0%
metadata would be stored in say bigquery
or<00:08:13.599><c> parquet</c><00:08:14.080><c> files</c><00:08:14.400><c> so</c><00:08:14.520><c> you</c><00:08:14.599><c> can</c><00:08:14.720><c> run</c><00:08:14.879><c> SQL</c><00:08:15.240><c> on</c>

00:08:15.350 --> 00:08:15.360 align:start position:0%
or parquet files so you can run SQL on
 

00:08:15.360 --> 00:08:18.869 align:start position:0%
or parquet files so you can run SQL on
that<00:08:16.159><c> the</c><00:08:16.280><c> training</c><00:08:16.720><c> data</c><00:08:17.000><c> would</c><00:08:17.199><c> live</c><00:08:17.680><c> on</c><00:08:17.919><c> S3</c>

00:08:18.869 --> 00:08:18.879 align:start position:0%
that the training data would live on S3
 

00:08:18.879 --> 00:08:21.110 align:start position:0%
that the training data would live on S3
as<00:08:19.080><c> raw</c><00:08:19.400><c> files</c><00:08:19.720><c> are</c><00:08:19.960><c> perhaps</c><00:08:20.240><c> in</c><00:08:20.360><c> a</c><00:08:20.520><c> tarball</c>

00:08:21.110 --> 00:08:21.120 align:start position:0%
as raw files are perhaps in a tarball
 

00:08:21.120 --> 00:08:23.469 align:start position:0%
as raw files are perhaps in a tarball
like<00:08:21.319><c> web</c><00:08:21.560><c> data</c><00:08:21.840><c> set</c><00:08:22.560><c> and</c><00:08:22.680><c> the</c><00:08:22.800><c> embeddings</c>

00:08:23.469 --> 00:08:23.479 align:start position:0%
like web data set and the embeddings
 

00:08:23.479 --> 00:08:25.710 align:start position:0%
like web data set and the embeddings
would<00:08:23.680><c> go</c><00:08:23.879><c> to</c><00:08:24.039><c> a</c><00:08:24.240><c> specialized</c><00:08:24.800><c> Vector</c><00:08:25.120><c> store</c>

00:08:25.710 --> 00:08:25.720 align:start position:0%
would go to a specialized Vector store
 

00:08:25.720 --> 00:08:28.350 align:start position:0%
would go to a specialized Vector store
for<00:08:25.919><c> search</c><00:08:26.199><c> and</c><00:08:26.800><c> retrieval</c><00:08:27.800><c> and</c><00:08:27.960><c> you</c><00:08:28.080><c> have</c><00:08:28.199><c> to</c>

00:08:28.350 --> 00:08:28.360 align:start position:0%
for search and retrieval and you have to
 

00:08:28.360 --> 00:08:31.230 align:start position:0%
for search and retrieval and you have to
manually<00:08:28.919><c> keep</c><00:08:29.199><c> all</c><00:08:29.720><c> three</c><00:08:29.960><c> systems</c><00:08:30.360><c> in</c><00:08:30.520><c> sync</c>

00:08:31.230 --> 00:08:31.240 align:start position:0%
manually keep all three systems in sync
 

00:08:31.240 --> 00:08:33.269 align:start position:0%
manually keep all three systems in sync
and<00:08:31.400><c> manage</c><00:08:31.759><c> all</c><00:08:31.960><c> three</c>

00:08:33.269 --> 00:08:33.279 align:start position:0%
and manage all three
 

00:08:33.279 --> 00:08:35.589 align:start position:0%
and manage all three
systems<00:08:34.279><c> instead</c><00:08:34.640><c> with</c><00:08:34.800><c> Lance</c><00:08:35.120><c> format</c><00:08:35.519><c> you</c>

00:08:35.589 --> 00:08:35.599 align:start position:0%
systems instead with Lance format you
 

00:08:35.599 --> 00:08:38.709 align:start position:0%
systems instead with Lance format you
can<00:08:35.800><c> unify</c><00:08:36.200><c> storage</c><00:08:36.719><c> into</c><00:08:37.519><c> a</c><00:08:37.640><c> single</c><00:08:37.919><c> data</c><00:08:38.200><c> set</c>

00:08:38.709 --> 00:08:38.719 align:start position:0%
can unify storage into a single data set
 

00:08:38.719 --> 00:08:41.110 align:start position:0%
can unify storage into a single data set
on<00:08:39.000><c> Object</c><00:08:39.320><c> Store</c><00:08:40.120><c> and</c><00:08:40.240><c> be</c><00:08:40.360><c> able</c><00:08:40.560><c> to</c><00:08:40.719><c> run</c><00:08:40.959><c> all</c>

00:08:41.110 --> 00:08:41.120 align:start position:0%
on Object Store and be able to run all
 

00:08:41.120 --> 00:08:42.829 align:start position:0%
on Object Store and be able to run all
the<00:08:41.279><c> workloads</c><00:08:41.760><c> you</c><00:08:41.880><c> need</c><00:08:42.240><c> on</c><00:08:42.399><c> that</c><00:08:42.560><c> single</c>

00:08:42.829 --> 00:08:42.839 align:start position:0%
the workloads you need on that single
 

00:08:42.839 --> 00:08:44.990 align:start position:0%
the workloads you need on that single
source<00:08:43.080><c> of</c><00:08:43.240><c> Truth</c><00:08:44.000><c> you</c><00:08:44.080><c> can</c><00:08:44.279><c> run</c><00:08:44.440><c> SQL</c><00:08:44.800><c> for</c>

00:08:44.990 --> 00:08:45.000 align:start position:0%
source of Truth you can run SQL for
 

00:08:45.000 --> 00:08:46.750 align:start position:0%
source of Truth you can run SQL for
analytics<00:08:45.640><c> you</c><00:08:45.760><c> can</c><00:08:45.880><c> stream</c><00:08:46.279><c> the</c><00:08:46.399><c> training</c>

00:08:46.750 --> 00:08:46.760 align:start position:0%
analytics you can stream the training
 

00:08:46.760 --> 00:08:49.949 align:start position:0%
analytics you can stream the training
data<00:08:47.000><c> into</c><00:08:47.200><c> gpus</c><00:08:48.040><c> for</c><00:08:48.240><c> better</c><00:08:48.959><c> utilization</c>

00:08:49.949 --> 00:08:49.959 align:start position:0%
data into gpus for better utilization
 

00:08:49.959 --> 00:08:51.990 align:start position:0%
data into gpus for better utilization
and<00:08:50.080><c> you</c><00:08:50.160><c> can</c><00:08:50.320><c> run</c><00:08:50.560><c> real-time</c><00:08:51.120><c> an</c><00:08:51.640><c> uh</c><00:08:51.760><c> you</c><00:08:51.880><c> can</c>

00:08:51.990 --> 00:08:52.000 align:start position:0%
and you can run real-time an uh you can
 

00:08:52.000 --> 00:08:53.670 align:start position:0%
and you can run real-time an uh you can
run<00:08:52.279><c> real-time</c><00:08:52.760><c> Vector</c><00:08:53.040><c> search</c><00:08:53.480><c> on</c><00:08:53.560><c> the</c>

00:08:53.670 --> 00:08:53.680 align:start position:0%
run real-time Vector search on the
 

00:08:53.680 --> 00:08:55.750 align:start position:0%
run real-time Vector search on the
embeddings<00:08:54.080><c> for</c><00:08:54.240><c> D</c><00:08:54.480><c> duplication</c><00:08:55.480><c> data</c>

00:08:55.750 --> 00:08:55.760 align:start position:0%
embeddings for D duplication data
 

00:08:55.760 --> 00:08:57.990 align:start position:0%
embeddings for D duplication data
curation<00:08:56.720><c> and</c>

00:08:57.990 --> 00:08:58.000 align:start position:0%
curation and
 

00:08:58.000 --> 00:09:00.790 align:start position:0%
curation and
more<00:08:59.000><c> there</c><00:08:59.120><c> are</c><00:08:59.240><c> three</c><00:08:59.440><c> three</c><00:08:59.680><c> main</c><00:08:59.959><c> parts</c>

00:09:00.790 --> 00:09:00.800 align:start position:0%
more there are three three main parts
 

00:09:00.800 --> 00:09:04.509 align:start position:0%
more there are three three main parts
for<00:09:01.079><c> any</c><00:09:01.720><c> Lance</c><00:09:02.079><c> data</c><00:09:02.360><c> set</c><00:09:03.000><c> first</c><00:09:03.800><c> Lance</c><00:09:04.240><c> data</c>

00:09:04.509 --> 00:09:04.519 align:start position:0%
for any Lance data set first Lance data
 

00:09:04.519 --> 00:09:07.470 align:start position:0%
for any Lance data set first Lance data
files<00:09:04.959><c> specify</c><00:09:05.600><c> how</c><00:09:05.880><c> data</c><00:09:06.160><c> is</c><00:09:06.399><c> actually</c>

00:09:07.470 --> 00:09:07.480 align:start position:0%
files specify how data is actually
 

00:09:07.480 --> 00:09:10.630 align:start position:0%
files specify how data is actually
stored<00:09:08.480><c> a</c><00:09:08.680><c> new</c><00:09:08.920><c> data</c><00:09:09.240><c> layout</c><00:09:09.640><c> enables</c><00:09:10.120><c> L</c><00:09:10.440><c> to</c>

00:09:10.630 --> 00:09:10.640 align:start position:0%
stored a new data layout enables L to
 

00:09:10.640 --> 00:09:13.350 align:start position:0%
stored a new data layout enables L to
support<00:09:10.959><c> fast</c><00:09:11.240><c> random</c><00:09:11.680><c> access</c><00:09:12.440><c> useful</c><00:09:13.000><c> for</c>

00:09:13.350 --> 00:09:13.360 align:start position:0%
support fast random access useful for
 

00:09:13.360 --> 00:09:16.069 align:start position:0%
support fast random access useful for
search<00:09:14.240><c> or</c><00:09:14.519><c> shuffling</c><00:09:15.079><c> during</c>

00:09:16.069 --> 00:09:16.079 align:start position:0%
search or shuffling during
 

00:09:16.079 --> 00:09:19.110 align:start position:0%
search or shuffling during
training<00:09:17.079><c> in</c><00:09:17.279><c> in</c><00:09:17.399><c> a</c><00:09:17.600><c> major</c><00:09:17.959><c> departure</c><00:09:18.560><c> from</c>

00:09:19.110 --> 00:09:19.120 align:start position:0%
training in in a major departure from
 

00:09:19.120 --> 00:09:22.190 align:start position:0%
training in in a major departure from
parquet<00:09:20.120><c> we've</c><00:09:20.519><c> removed</c><00:09:20.959><c> row</c><00:09:21.279><c> groups</c><00:09:21.839><c> so</c><00:09:22.040><c> that</c>

00:09:22.190 --> 00:09:22.200 align:start position:0%
parquet we've removed row groups so that
 

00:09:22.200 --> 00:09:24.190 align:start position:0%
parquet we've removed row groups so that
we<00:09:22.399><c> reduce</c><00:09:22.839><c> the</c><00:09:22.959><c> memory</c><00:09:23.399><c> issues</c><00:09:23.720><c> when</c><00:09:23.880><c> writing</c>

00:09:24.190 --> 00:09:24.200 align:start position:0%
we reduce the memory issues when writing
 

00:09:24.200 --> 00:09:27.470 align:start position:0%
we reduce the memory issues when writing
large<00:09:24.920><c> blobs</c><00:09:25.920><c> and</c><00:09:26.120><c> we</c><00:09:26.320><c> also</c><00:09:26.600><c> deliver</c><00:09:27.120><c> great</c>

00:09:27.470 --> 00:09:27.480 align:start position:0%
large blobs and we also deliver great
 

00:09:27.480 --> 00:09:32.230 align:start position:0%
large blobs and we also deliver great
performance<00:09:28.480><c> for</c><00:09:28.880><c> both</c><00:09:29.480><c> small</c><00:09:29.880><c> and</c><00:09:30.079><c> large</c>

00:09:32.230 --> 00:09:32.240 align:start position:0%
performance for both small and large
 

00:09:32.240 --> 00:09:34.590 align:start position:0%
performance for both small and large
columns<00:09:33.240><c> and</c><00:09:33.399><c> because</c><00:09:33.640><c> Lance</c><00:09:34.000><c> is</c><00:09:34.160><c> designed</c><00:09:34.480><c> to</c>

00:09:34.590 --> 00:09:34.600 align:start position:0%
columns and because Lance is designed to
 

00:09:34.600 --> 00:09:36.150 align:start position:0%
columns and because Lance is designed to
deal<00:09:34.800><c> with</c><00:09:34.959><c> lots</c><00:09:35.160><c> of</c><00:09:35.320><c> different</c><00:09:35.600><c> data</c><00:09:35.880><c> types</c>

00:09:36.150 --> 00:09:36.160 align:start position:0%
deal with lots of different data types
 

00:09:36.160 --> 00:09:38.509 align:start position:0%
deal with lots of different data types
for<00:09:36.360><c> AI</c><00:09:37.079><c> the</c><00:09:37.240><c> encodings</c><00:09:37.760><c> can</c><00:09:37.959><c> easily</c><00:09:38.320><c> be</c>

00:09:38.509 --> 00:09:38.519 align:start position:0%
for AI the encodings can easily be
 

00:09:38.519 --> 00:09:41.430 align:start position:0%
for AI the encodings can easily be
extended<00:09:39.360><c> to</c><00:09:39.600><c> support</c><00:09:40.160><c> compressive</c>

00:09:41.430 --> 00:09:41.440 align:start position:0%
extended to support compressive
 

00:09:41.440 --> 00:09:44.670 align:start position:0%
extended to support compressive
encodings<00:09:42.440><c> to</c><00:09:42.640><c> make</c><00:09:42.920><c> the</c><00:09:43.079><c> dis</c><00:09:43.399><c> size</c><00:09:43.680><c> smaller</c>

00:09:44.670 --> 00:09:44.680 align:start position:0%
encodings to make the dis size smaller
 

00:09:44.680 --> 00:09:46.710 align:start position:0%
encodings to make the dis size smaller
or<00:09:45.079><c> specialized</c><00:09:45.680><c> en</c><00:09:45.839><c> codings</c><00:09:46.200><c> for</c><00:09:46.440><c> different</c>

00:09:46.710 --> 00:09:46.720 align:start position:0%
or specialized en codings for different
 

00:09:46.720 --> 00:09:49.470 align:start position:0%
or specialized en codings for different
data<00:09:47.000><c> types</c><00:09:47.519><c> think</c><00:09:48.079><c> time</c><00:09:48.320><c> series</c><00:09:48.760><c> data</c>

00:09:49.470 --> 00:09:49.480 align:start position:0%
data types think time series data
 

00:09:49.480 --> 00:09:54.949 align:start position:0%
data types think time series data
geospatial<00:09:50.480><c> data</c><00:09:51.120><c> and</c>

00:09:54.949 --> 00:09:54.959 align:start position:0%
 
 

00:09:54.959 --> 00:09:58.590 align:start position:0%
 
more<00:09:55.959><c> the</c><00:09:56.440><c> performance</c><00:09:57.440><c> characteristics</c><00:09:58.440><c> of</c>

00:09:58.590 --> 00:09:58.600 align:start position:0%
more the performance characteristics of
 

00:09:58.600 --> 00:10:00.350 align:start position:0%
more the performance characteristics of
Lance

00:10:00.350 --> 00:10:00.360 align:start position:0%
Lance
 

00:10:00.360 --> 00:10:03.069 align:start position:0%
Lance
means<00:10:00.880><c> that</c><00:10:01.399><c> many</c><00:10:01.880><c> generative</c><00:10:02.399><c> AI</c><00:10:02.720><c> companies</c>

00:10:03.069 --> 00:10:03.079 align:start position:0%
means that many generative AI companies
 

00:10:03.079 --> 00:10:05.550 align:start position:0%
means that many generative AI companies
are<00:10:03.240><c> using</c><00:10:03.560><c> Lance</c><00:10:03.959><c> to</c><00:10:04.160><c> store</c><00:10:04.959><c> their</c><00:10:05.160><c> training</c>

00:10:05.550 --> 00:10:05.560 align:start position:0%
are using Lance to store their training
 

00:10:05.560 --> 00:10:08.110 align:start position:0%
are using Lance to store their training
data<00:10:05.880><c> sets</c><00:10:06.480><c> today</c><00:10:06.880><c> Lance</c><00:10:07.360><c> format</c><00:10:07.640><c> is</c><00:10:07.760><c> being</c>

00:10:08.110 --> 00:10:08.120 align:start position:0%
data sets today Lance format is being
 

00:10:08.120 --> 00:10:10.310 align:start position:0%
data sets today Lance format is being
downloaded<00:10:09.120><c> downloaded</c><00:10:09.680><c> several</c><00:10:10.040><c> hundred</c>

00:10:10.310 --> 00:10:10.320 align:start position:0%
downloaded downloaded several hundred
 

00:10:10.320 --> 00:10:12.590 align:start position:0%
downloaded downloaded several hundred
times<00:10:10.680><c> per</c><00:10:10.880><c> month</c><00:10:11.640><c> and</c><00:10:11.760><c> the</c><00:10:11.920><c> largest</c><00:10:12.279><c> Lance</c>

00:10:12.590 --> 00:10:12.600 align:start position:0%
times per month and the largest Lance
 

00:10:12.600 --> 00:10:15.630 align:start position:0%
times per month and the largest Lance
data<00:10:12.880><c> set</c><00:10:13.079><c> we've</c><00:10:13.240><c> seen</c><00:10:13.920><c> to</c><00:10:14.200><c> date</c><00:10:14.720><c> is</c><00:10:14.920><c> around</c><00:10:15.360><c> 10</c>

00:10:15.630 --> 00:10:15.640 align:start position:0%
data set we've seen to date is around 10
 

00:10:15.640 --> 00:10:17.750 align:start position:0%
data set we've seen to date is around 10
pedabytes

00:10:17.750 --> 00:10:17.760 align:start position:0%
pedabytes
 

00:10:17.760 --> 00:10:21.670 align:start position:0%
pedabytes
the<00:10:18.760><c> second</c><00:10:19.040><c> part</c><00:10:19.200><c> of</c><00:10:19.360><c> Lance</c><00:10:20.160><c> is</c><00:10:20.399><c> a</c><00:10:20.560><c> table</c>

00:10:21.670 --> 00:10:21.680 align:start position:0%
the second part of Lance is a table
 

00:10:21.680 --> 00:10:24.910 align:start position:0%
the second part of Lance is a table
format<00:10:22.680><c> that</c><00:10:22.880><c> supports</c><00:10:23.680><c> reproducibility</c><00:10:24.680><c> and</c>

00:10:24.910 --> 00:10:24.920 align:start position:0%
format that supports reproducibility and
 

00:10:24.920 --> 00:10:26.750 align:start position:0%
format that supports reproducibility and
Rapid

00:10:26.750 --> 00:10:26.760 align:start position:0%
Rapid
 

00:10:26.760 --> 00:10:29.550 align:start position:0%
Rapid
experimentation<00:10:27.760><c> it</c><00:10:28.040><c> specifies</c><00:10:28.640><c> how</c><00:10:28.880><c> schema</c>

00:10:29.550 --> 00:10:29.560 align:start position:0%
experimentation it specifies how schema
 

00:10:29.560 --> 00:10:31.389 align:start position:0%
experimentation it specifies how schema
is<00:10:29.680><c> stored</c><00:10:30.040><c> in</c><00:10:30.160><c> the</c><00:10:30.320><c> data</c><00:10:30.560><c> set</c><00:10:30.920><c> and</c><00:10:31.079><c> how</c><00:10:31.279><c> the</c>

00:10:31.389 --> 00:10:31.399 align:start position:0%
is stored in the data set and how the
 

00:10:31.399 --> 00:10:32.790 align:start position:0%
is stored in the data set and how the
data<00:10:31.680><c> set</c><00:10:31.920><c> is</c>

00:10:32.790 --> 00:10:32.800 align:start position:0%
data set is
 

00:10:32.800 --> 00:10:35.590 align:start position:0%
data set is
versioned<00:10:33.800><c> adding</c><00:10:34.279><c> and</c><00:10:34.440><c> removing</c><00:10:34.959><c> columns</c>

00:10:35.590 --> 00:10:35.600 align:start position:0%
versioned adding and removing columns
 

00:10:35.600 --> 00:10:38.269 align:start position:0%
versioned adding and removing columns
does<00:10:35.800><c> not</c><00:10:36.079><c> require</c><00:10:36.680><c> copying</c><00:10:37.000><c> the</c><00:10:37.120><c> data</c><00:10:37.480><c> set</c>

00:10:38.269 --> 00:10:38.279 align:start position:0%
does not require copying the data set
 

00:10:38.279 --> 00:10:41.069 align:start position:0%
does not require copying the data set
which<00:10:38.560><c> drastically</c><00:10:39.200><c> reduces</c><00:10:40.160><c> data</c><00:10:40.480><c> transfer</c>

00:10:41.069 --> 00:10:41.079 align:start position:0%
which drastically reduces data transfer
 

00:10:41.079 --> 00:10:43.910 align:start position:0%
which drastically reduces data transfer
and<00:10:41.320><c> compute</c><00:10:41.760><c> costs</c><00:10:42.279><c> when</c><00:10:42.480><c> creating</c>

00:10:43.910 --> 00:10:43.920 align:start position:0%
and compute costs when creating
 

00:10:43.920 --> 00:10:46.190 align:start position:0%
and compute costs when creating
experiments<00:10:44.920><c> and</c><00:10:45.079><c> the</c><00:10:45.320><c> data</c><00:10:46.000><c> is</c>

00:10:46.190 --> 00:10:46.200 align:start position:0%
experiments and the data is
 

00:10:46.200 --> 00:10:47.990 align:start position:0%
experiments and the data is
automatically<00:10:46.760><c> versioned</c><00:10:47.360><c> when</c><00:10:47.480><c> you</c><00:10:47.720><c> add</c>

00:10:47.990 --> 00:10:48.000 align:start position:0%
automatically versioned when you add
 

00:10:48.000 --> 00:10:51.470 align:start position:0%
automatically versioned when you add
remove<00:10:48.519><c> rows</c><00:10:48.959><c> or</c><00:10:49.560><c> columns</c><00:10:50.560><c> and</c><00:10:50.680><c> you</c><00:10:50.760><c> can</c><00:10:51.120><c> tag</c>

00:10:51.470 --> 00:10:51.480 align:start position:0%
remove rows or columns and you can tag
 

00:10:51.480 --> 00:10:55.470 align:start position:0%
remove rows or columns and you can tag
certain<00:10:51.800><c> versions</c><00:10:52.600><c> for</c><00:10:52.920><c> longterm</c>

00:10:55.470 --> 00:10:55.480 align:start position:0%
certain versions for longterm
 

00:10:55.480 --> 00:10:58.389 align:start position:0%
certain versions for longterm
tension<00:10:56.480><c> in</c><00:10:56.760><c> production</c><00:10:57.760><c> being</c><00:10:57.959><c> able</c><00:10:58.160><c> to</c>

00:10:58.389 --> 00:10:58.399 align:start position:0%
tension in production being able to
 

00:10:58.399 --> 00:11:01.550 align:start position:0%
tension in production being able to
check<00:10:58.600><c> out</c><00:10:58.800><c> older</c><00:10:59.720><c> verions</c><00:11:00.720><c> quickly</c><00:11:01.200><c> means</c>

00:11:01.550 --> 00:11:01.560 align:start position:0%
check out older verions quickly means
 

00:11:01.560 --> 00:11:04.150 align:start position:0%
check out older verions quickly means
that<00:11:01.839><c> roll</c><00:11:02.279><c> backs</c><00:11:03.079><c> will</c><00:11:03.320><c> not</c><00:11:03.600><c> result</c><00:11:03.959><c> in</c>

00:11:04.150 --> 00:11:04.160 align:start position:0%
that roll backs will not result in
 

00:11:04.160 --> 00:11:08.110 align:start position:0%
that roll backs will not result in
downtime<00:11:04.760><c> and</c><00:11:04.880><c> can</c><00:11:05.040><c> essentially</c><00:11:05.480><c> be</c>

00:11:08.110 --> 00:11:08.120 align:start position:0%
 
 

00:11:08.120 --> 00:11:12.110 align:start position:0%
 
instantaneous<00:11:09.120><c> to</c><00:11:09.360><c> hear</c><00:11:09.639><c> from</c><00:11:10.040><c> a</c><00:11:10.240><c> lance</c>

00:11:12.110 --> 00:11:12.120 align:start position:0%
instantaneous to hear from a lance
 

00:11:12.120 --> 00:11:16.190 align:start position:0%
instantaneous to hear from a lance
user<00:11:13.120><c> Devon</c><00:11:13.519><c> Stein</c><00:11:14.000><c> the</c><00:11:14.160><c> founder</c><00:11:14.560><c> of</c><00:11:15.200><c> dosu</c>

00:11:16.190 --> 00:11:16.200 align:start position:0%
user Devon Stein the founder of dosu
 

00:11:16.200 --> 00:11:19.550 align:start position:0%
user Devon Stein the founder of dosu
uses<00:11:16.760><c> Lance</c><00:11:17.720><c> especially</c><00:11:18.200><c> for</c><00:11:18.680><c> its</c><00:11:19.120><c> ability</c>

00:11:19.550 --> 00:11:19.560 align:start position:0%
uses Lance especially for its ability
 

00:11:19.560 --> 00:11:21.910 align:start position:0%
uses Lance especially for its ability
for<00:11:19.800><c> versioning</c><00:11:20.279><c> and</c><00:11:20.480><c> time</c><00:11:20.760><c> travel</c><00:11:21.600><c> and</c><00:11:21.760><c> made</c>

00:11:21.910 --> 00:11:21.920 align:start position:0%
for versioning and time travel and made
 

00:11:21.920 --> 00:11:24.150 align:start position:0%
for versioning and time travel and made
it<00:11:22.120><c> significantly</c><00:11:22.800><c> easier</c><00:11:23.279><c> for</c><00:11:23.519><c> them</c><00:11:23.920><c> to</c>

00:11:24.150 --> 00:11:24.160 align:start position:0%
it significantly easier for them to
 

00:11:24.160 --> 00:11:26.949 align:start position:0%
it significantly easier for them to
reproduce<00:11:24.839><c> experiment</c><00:11:25.519><c> and</c><00:11:25.760><c> evaluate</c><00:11:26.600><c> on</c>

00:11:26.949 --> 00:11:26.959 align:start position:0%
reproduce experiment and evaluate on
 

00:11:26.959 --> 00:11:29.829 align:start position:0%
reproduce experiment and evaluate on
historical<00:11:27.560><c> data</c><00:11:28.040><c> for</c><00:11:28.800><c> uh</c><00:11:29.320><c> producing</c>

00:11:29.829 --> 00:11:29.839 align:start position:0%
historical data for uh producing
 

00:11:29.839 --> 00:11:32.150 align:start position:0%
historical data for uh producing
previous<00:11:30.240><c> agent</c>

00:11:32.150 --> 00:11:32.160 align:start position:0%
previous agent
 

00:11:32.160 --> 00:11:35.190 align:start position:0%
previous agent
runs<00:11:33.160><c> the</c><00:11:33.360><c> last</c><00:11:33.600><c> piece</c><00:11:33.760><c> of</c><00:11:33.920><c> magic</c><00:11:34.200><c> and</c><00:11:34.360><c> Lance</c>

00:11:35.190 --> 00:11:35.200 align:start position:0%
runs the last piece of magic and Lance
 

00:11:35.200 --> 00:11:37.990 align:start position:0%
runs the last piece of magic and Lance
is<00:11:35.360><c> in</c><00:11:35.600><c> the</c><00:11:35.760><c> index</c><00:11:36.360><c> format</c><00:11:37.360><c> from</c><00:11:37.680><c> Vector</c>

00:11:37.990 --> 00:11:38.000 align:start position:0%
is in the index format from Vector
 

00:11:38.000 --> 00:11:41.110 align:start position:0%
is in the index format from Vector
indices<00:11:38.920><c> to</c><00:11:39.200><c> full</c><00:11:39.480><c> Tech</c><00:11:39.720><c> search</c><00:11:40.000><c> index</c><00:11:40.800><c> to</c>

00:11:41.110 --> 00:11:41.120 align:start position:0%
indices to full Tech search index to
 

00:11:41.120 --> 00:11:43.790 align:start position:0%
indices to full Tech search index to
traditional<00:11:41.680><c> column</c>

00:11:43.790 --> 00:11:43.800 align:start position:0%
traditional column
 

00:11:43.800 --> 00:11:45.829 align:start position:0%
traditional column
indices<00:11:44.800><c> Lance</c>

00:11:45.829 --> 00:11:45.839 align:start position:0%
indices Lance
 

00:11:45.839 --> 00:11:48.350 align:start position:0%
indices Lance
DB<00:11:46.839><c> supports</c><00:11:47.480><c> fast</c>

00:11:48.350 --> 00:11:48.360 align:start position:0%
DB supports fast
 

00:11:48.360 --> 00:11:51.350 align:start position:0%
DB supports fast
retrieval<00:11:49.360><c> and</c><00:11:49.839><c> filtering</c><00:11:50.320><c> for</c><00:11:50.600><c> rag</c><00:11:51.040><c> for</c>

00:11:51.350 --> 00:11:51.360 align:start position:0%
retrieval and filtering for rag for
 

00:11:51.360 --> 00:11:52.670 align:start position:0%
retrieval and filtering for rag for
semantic

00:11:52.670 --> 00:11:52.680 align:start position:0%
semantic
 

00:11:52.680 --> 00:11:55.750 align:start position:0%
semantic
search<00:11:53.680><c> and</c><00:11:53.839><c> when</c><00:11:53.959><c> you</c><00:11:54.160><c> put</c><00:11:54.920><c> these</c><00:11:55.120><c> two</c><00:11:55.360><c> search</c>

00:11:55.750 --> 00:11:55.760 align:start position:0%
search and when you put these two search
 

00:11:55.760 --> 00:11:57.990 align:start position:0%
search and when you put these two search
techniques<00:11:56.399><c> together</c><00:11:57.079><c> you</c><00:11:57.240><c> can</c><00:11:57.399><c> get</c><00:11:57.720><c> much</c>

00:11:57.990 --> 00:11:58.000 align:start position:0%
techniques together you can get much
 

00:11:58.000 --> 00:12:00.949 align:start position:0%
techniques together you can get much
better<00:11:58.519><c> performance</c><00:11:59.519><c> for</c><00:11:59.920><c> your</c><00:12:00.480><c> AI</c>

00:12:00.949 --> 00:12:00.959 align:start position:0%
better performance for your AI
 

00:12:00.959 --> 00:12:04.350 align:start position:0%
better performance for your AI
applications<00:12:01.959><c> using</c><00:12:02.360><c> hybrid</c><00:12:02.680><c> search</c>

00:12:04.350 --> 00:12:04.360 align:start position:0%
applications using hybrid search
 

00:12:04.360 --> 00:12:07.949 align:start position:0%
applications using hybrid search
techniques<00:12:05.360><c> and</c><00:12:05.600><c> moreover</c><00:12:06.600><c> the</c><00:12:06.760><c> Lance</c><00:12:07.600><c> IND</c>

00:12:07.949 --> 00:12:07.959 align:start position:0%
techniques and moreover the Lance IND
 

00:12:07.959 --> 00:12:11.550 align:start position:0%
techniques and moreover the Lance IND
indices<00:12:08.839><c> are</c><00:12:09.160><c> accessible</c><00:12:09.680><c> directly</c><00:12:10.079><c> from</c><00:12:10.560><c> S3</c>

00:12:11.550 --> 00:12:11.560 align:start position:0%
indices are accessible directly from S3
 

00:12:11.560 --> 00:12:14.750 align:start position:0%
indices are accessible directly from S3
so<00:12:11.839><c> that</c><00:12:12.320><c> it</c><00:12:12.560><c> keeps</c><00:12:13.079><c> your</c><00:12:13.480><c> cost</c><00:12:13.920><c> very</c><00:12:14.199><c> low</c><00:12:14.639><c> as</c>

00:12:14.750 --> 00:12:14.760 align:start position:0%
so that it keeps your cost very low as
 

00:12:14.760 --> 00:12:17.750 align:start position:0%
so that it keeps your cost very low as
you<00:12:14.880><c> scale</c>

00:12:17.750 --> 00:12:17.760 align:start position:0%
 
 

00:12:17.760 --> 00:12:21.590 align:start position:0%
 
up<00:12:18.760><c> the</c><00:12:18.920><c> Lance</c><00:12:19.600><c> scaler</c><00:12:20.040><c> indices</c><00:12:20.760><c> on</c><00:12:21.120><c> columns</c>

00:12:21.590 --> 00:12:21.600 align:start position:0%
up the Lance scaler indices on columns
 

00:12:21.600 --> 00:12:24.550 align:start position:0%
up the Lance scaler indices on columns
like<00:12:21.800><c> strings</c><00:12:22.440><c> and</c><00:12:22.680><c> and</c><00:12:22.880><c> numbers</c><00:12:23.880><c> supports</c>

00:12:24.550 --> 00:12:24.560 align:start position:0%
like strings and and numbers supports
 

00:12:24.560 --> 00:12:26.710 align:start position:0%
like strings and and numbers supports
Vector<00:12:24.920><c> search</c><00:12:25.839><c> uh</c><00:12:25.920><c> and</c><00:12:26.079><c> works</c><00:12:26.399><c> with</c><00:12:26.560><c> these</c>

00:12:26.710 --> 00:12:26.720 align:start position:0%
Vector search uh and works with these
 

00:12:26.720 --> 00:12:30.509 align:start position:0%
Vector search uh and works with these
search<00:12:27.000><c> indices</c><00:12:27.959><c> but</c><00:12:28.240><c> also</c><00:12:28.800><c> makes</c><00:12:29.519><c> filtering</c>

00:12:30.509 --> 00:12:30.519 align:start position:0%
search indices but also makes filtering
 

00:12:30.519 --> 00:12:33.470 align:start position:0%
search indices but also makes filtering
uh<00:12:30.760><c> pushdowns</c><00:12:31.480><c> much</c><00:12:31.800><c> faster</c><00:12:32.279><c> for</c><00:12:33.000><c> SQL</c>

00:12:33.470 --> 00:12:33.480 align:start position:0%
uh pushdowns much faster for SQL
 

00:12:33.480 --> 00:12:35.230 align:start position:0%
uh pushdowns much faster for SQL
workloads<00:12:34.040><c> or</c><00:12:34.240><c> data</c><00:12:34.480><c> frame</c>

00:12:35.230 --> 00:12:35.240 align:start position:0%
workloads or data frame
 

00:12:35.240 --> 00:12:39.389 align:start position:0%
workloads or data frame
workloads<00:12:36.240><c> and</c><00:12:36.760><c> allows</c><00:12:37.240><c> Lance</c><00:12:37.639><c> to</c><00:12:38.079><c> serve</c><00:12:39.079><c> not</c>

00:12:39.389 --> 00:12:39.399 align:start position:0%
workloads and allows Lance to serve not
 

00:12:39.399 --> 00:12:42.310 align:start position:0%
workloads and allows Lance to serve not
just<00:12:39.680><c> search</c><00:12:40.000><c> and</c><00:12:40.160><c> retrieval</c><00:12:40.800><c> queries</c><00:12:41.800><c> but</c>

00:12:42.310 --> 00:12:42.320 align:start position:0%
just search and retrieval queries but
 

00:12:42.320 --> 00:12:44.590 align:start position:0%
just search and retrieval queries but
traditional<00:12:43.000><c> data</c><00:12:43.360><c> analytics</c><00:12:43.880><c> queries</c><00:12:44.360><c> much</c>

00:12:44.590 --> 00:12:44.600 align:start position:0%
traditional data analytics queries much
 

00:12:44.600 --> 00:12:47.590 align:start position:0%
traditional data analytics queries much
faster<00:12:45.040><c> as</c>

00:12:47.590 --> 00:12:47.600 align:start position:0%
 
 

00:12:47.600 --> 00:12:51.629 align:start position:0%
 
well<00:12:48.600><c> so</c><00:12:49.440><c> when</c><00:12:49.600><c> you</c><00:12:49.880><c> have</c><00:12:50.600><c> lots</c><00:12:50.839><c> of</c><00:12:51.000><c> AI</c><00:12:51.360><c> data</c>

00:12:51.629 --> 00:12:51.639 align:start position:0%
well so when you have lots of AI data
 

00:12:51.639 --> 00:12:52.710 align:start position:0%
well so when you have lots of AI data
and<00:12:51.800><c> lots</c><00:12:52.000><c> of</c>

00:12:52.710 --> 00:12:52.720 align:start position:0%
and lots of
 

00:12:52.720 --> 00:12:55.790 align:start position:0%
and lots of
vectors<00:12:53.720><c> you</c><00:12:53.880><c> can</c><00:12:54.079><c> store</c><00:12:54.440><c> the</c><00:12:54.639><c> data</c><00:12:55.240><c> in</c><00:12:55.440><c> just</c><00:12:55.600><c> a</c>

00:12:55.790 --> 00:12:55.800 align:start position:0%
vectors you can store the data in just a
 

00:12:55.800 --> 00:13:03.949 align:start position:0%
vectors you can store the data in just a
file<00:12:56.560><c> rather</c><00:12:56.880><c> than</c><00:12:57.199><c> a</c><00:12:57.360><c> vector</c><00:12:57.680><c> database</c>

00:13:03.949 --> 00:13:03.959 align:start position:0%
 
 

00:13:03.959 --> 00:13:07.910 align:start position:0%
 
for<00:13:04.399><c> users</c><00:13:04.959><c> like</c><00:13:05.320><c> character</c><00:13:06.199><c> AI</c><00:13:07.199><c> they've</c><00:13:07.560><c> used</c>

00:13:07.910 --> 00:13:07.920 align:start position:0%
for users like character AI they've used
 

00:13:07.920 --> 00:13:10.829 align:start position:0%
for users like character AI they've used
Lance<00:13:08.639><c> to</c><00:13:08.880><c> migrate</c><00:13:09.760><c> away</c><00:13:10.120><c> from</c><00:13:10.399><c> elastic</c>

00:13:10.829 --> 00:13:10.839 align:start position:0%
Lance to migrate away from elastic
 

00:13:10.839 --> 00:13:14.350 align:start position:0%
Lance to migrate away from elastic
search<00:13:11.360><c> and</c><00:13:11.519><c> reduced</c><00:13:12.040><c> their</c><00:13:12.720><c> latency</c><00:13:13.360><c> by</c><00:13:13.560><c> over</c>

00:13:14.350 --> 00:13:14.360 align:start position:0%
search and reduced their latency by over
 

00:13:14.360 --> 00:13:15.910 align:start position:0%
search and reduced their latency by over
90%

00:13:15.910 --> 00:13:15.920 align:start position:0%
90%
 

00:13:15.920 --> 00:13:19.829 align:start position:0%
90%
moreover<00:13:16.920><c> Lance</c><00:13:17.480><c> works</c><00:13:18.199><c> with</c><00:13:18.680><c> existing</c><00:13:19.560><c> data</c>

00:13:19.829 --> 00:13:19.839 align:start position:0%
moreover Lance works with existing data
 

00:13:19.839 --> 00:13:23.590 align:start position:0%
moreover Lance works with existing data
tools<00:13:20.560><c> like</c><00:13:20.839><c> spark</c><00:13:21.760><c> or</c><00:13:22.079><c> Ray</c><00:13:22.839><c> and</c><00:13:23.000><c> allows</c><00:13:23.399><c> them</c>

00:13:23.590 --> 00:13:23.600 align:start position:0%
tools like spark or Ray and allows them
 

00:13:23.600 --> 00:13:26.069 align:start position:0%
tools like spark or Ray and allows them
to<00:13:23.839><c> continue</c><00:13:24.639><c> iterating</c><00:13:25.120><c> on</c><00:13:25.360><c> this</c><00:13:25.560><c> system</c>

00:13:26.069 --> 00:13:26.079 align:start position:0%
to continue iterating on this system
 

00:13:26.079 --> 00:13:27.629 align:start position:0%
to continue iterating on this system
faster<00:13:26.440><c> than</c>

00:13:27.629 --> 00:13:27.639 align:start position:0%
faster than
 

00:13:27.639 --> 00:13:31.509 align:start position:0%
faster than
ever<00:13:28.639><c> at</c><00:13:28.839><c> a</c><00:13:29.160><c> scale</c><00:13:29.760><c> that</c><00:13:30.760><c> uh</c><00:13:30.920><c> many</c><00:13:31.199><c> have</c><00:13:31.360><c> not</c>

00:13:31.509 --> 00:13:31.519 align:start position:0%
ever at a scale that uh many have not
 

00:13:31.519 --> 00:13:34.870 align:start position:0%
ever at a scale that uh many have not
seen

00:13:34.870 --> 00:13:34.880 align:start position:0%
 
 

00:13:34.880 --> 00:13:37.150 align:start position:0%
 
before<00:13:35.880><c> while</c><00:13:36.040><c> Lance</c><00:13:36.399><c> format</c><00:13:36.839><c> is</c><00:13:36.959><c> the</c>

00:13:37.150 --> 00:13:37.160 align:start position:0%
before while Lance format is the
 

00:13:37.160 --> 00:13:39.750 align:start position:0%
before while Lance format is the
complete<00:13:37.800><c> Foundation</c><00:13:38.800><c> Building</c><00:13:39.240><c> the</c><00:13:39.399><c> rest</c><00:13:39.639><c> of</c>

00:13:39.750 --> 00:13:39.760 align:start position:0%
complete Foundation Building the rest of
 

00:13:39.760 --> 00:13:42.230 align:start position:0%
complete Foundation Building the rest of
the<00:13:39.880><c> Burge</c><00:13:40.440><c> will</c><00:13:40.639><c> take</c><00:13:41.000><c> the</c><00:13:41.160><c> entire</c><00:13:41.560><c> data</c>

00:13:42.230 --> 00:13:42.240 align:start position:0%
the Burge will take the entire data
 

00:13:42.240 --> 00:13:44.590 align:start position:0%
the Burge will take the entire data
ecosystem<00:13:43.240><c> this</c><00:13:43.440><c> requires</c><00:13:43.959><c> these</c><00:13:44.240><c> projects</c>

00:13:44.590 --> 00:13:44.600 align:start position:0%
ecosystem this requires these projects
 

00:13:44.600 --> 00:13:47.230 align:start position:0%
ecosystem this requires these projects
to<00:13:44.800><c> have</c><00:13:45.120><c> open</c><00:13:45.399><c> apis</c><00:13:46.360><c> and</c><00:13:46.519><c> work</c><00:13:46.839><c> well</c><00:13:47.079><c> with</c>

00:13:47.230 --> 00:13:47.240 align:start position:0%
to have open apis and work well with
 

00:13:47.240 --> 00:13:49.069 align:start position:0%
to have open apis and work well with
each<00:13:47.440><c> other</c><00:13:47.760><c> through</c><00:13:48.160><c> common</c>

00:13:49.069 --> 00:13:49.079 align:start position:0%
each other through common
 

00:13:49.079 --> 00:13:51.590 align:start position:0%
each other through common
standards<00:13:50.079><c> and</c><00:13:50.199><c> we</c><00:13:50.360><c> firmly</c><00:13:50.800><c> believe</c><00:13:51.120><c> that</c><00:13:51.320><c> the</c>

00:13:51.590 --> 00:13:51.600 align:start position:0%
standards and we firmly believe that the
 

00:13:51.600 --> 00:13:54.550 align:start position:0%
standards and we firmly believe that the
future<00:13:51.959><c> of</c><00:13:52.120><c> AI</c><00:13:52.440><c> is</c><00:13:52.880><c> open</c><00:13:53.880><c> but</c><00:13:54.040><c> it's</c><00:13:54.199><c> going</c><00:13:54.360><c> to</c>

00:13:54.550 --> 00:13:54.560 align:start position:0%
future of AI is open but it's going to
 

00:13:54.560 --> 00:13:57.790 align:start position:0%
future of AI is open but it's going to
take<00:13:54.880><c> building</c><00:13:55.839><c> a</c><00:13:56.040><c> composable</c><00:13:56.600><c> stack</c><00:13:56.880><c> for</c><00:13:57.120><c> AI</c>

00:13:57.790 --> 00:13:57.800 align:start position:0%
take building a composable stack for AI
 

00:13:57.800 --> 00:14:02.710 align:start position:0%
take building a composable stack for AI
to<00:13:58.000><c> make</c><00:13:58.279><c> that</c><00:13:58.600><c> successful</c>

00:14:02.710 --> 00:14:02.720 align:start position:0%
 
 

00:14:02.720 --> 00:14:04.790 align:start position:0%
 
thank<00:14:02.880><c> you</c><00:14:03.000><c> for</c><00:14:03.199><c> listening</c><00:14:03.560><c> to</c><00:14:03.720><c> my</c><00:14:03.920><c> talk</c><00:14:04.720><c> if</c>

00:14:04.790 --> 00:14:04.800 align:start position:0%
thank you for listening to my talk if
 

00:14:04.800 --> 00:14:06.069 align:start position:0%
thank you for listening to my talk if
you're<00:14:04.959><c> building</c><00:14:05.320><c> AI</c><00:14:05.560><c> models</c><00:14:05.839><c> and</c>

00:14:06.069 --> 00:14:06.079 align:start position:0%
you're building AI models and
 

00:14:06.079 --> 00:14:08.790 align:start position:0%
you're building AI models and
applications<00:14:07.079><c> we'd</c><00:14:07.240><c> love</c><00:14:07.399><c> to</c><00:14:07.519><c> hear</c><00:14:07.680><c> from</c><00:14:07.880><c> you</c>

00:14:08.790 --> 00:14:08.800 align:start position:0%
applications we'd love to hear from you
 

00:14:08.800 --> 00:14:11.069 align:start position:0%
applications we'd love to hear from you
and<00:14:08.920><c> you</c><00:14:09.000><c> can</c><00:14:09.160><c> use</c><00:14:09.440><c> this</c><00:14:09.720><c> QR</c><00:14:10.079><c> code</c><00:14:10.519><c> to</c><00:14:10.680><c> join</c><00:14:10.880><c> our</c>

00:14:11.069 --> 00:14:11.079 align:start position:0%
and you can use this QR code to join our
 

00:14:11.079 --> 00:14:13.990 align:start position:0%
and you can use this QR code to join our
Discord<00:14:11.519><c> Community</c><00:14:12.360><c> or</c><00:14:12.560><c> send</c><00:14:13.079><c> an</c><00:14:13.279><c> email</c><00:14:13.680><c> to</c>

00:14:13.990 --> 00:14:14.000 align:start position:0%
Discord Community or send an email to
 

00:14:14.000 --> 00:14:17.269 align:start position:0%
Discord Community or send an email to
contact<00:14:14.399><c> atlan</c><00:14:15.000><c> cb.com</c><00:14:16.000><c> to</c><00:14:16.320><c> find</c><00:14:16.519><c> out</c><00:14:16.720><c> more</c>

00:14:17.269 --> 00:14:17.279 align:start position:0%
contact atlan cb.com to find out more
 

00:14:17.279 --> 00:14:18.990 align:start position:0%
contact atlan cb.com to find out more
and<00:14:17.440><c> of</c><00:14:17.600><c> course</c><00:14:18.079><c> you</c><00:14:18.199><c> can</c><00:14:18.399><c> find</c><00:14:18.600><c> the</c><00:14:18.720><c> code</c>

00:14:18.990 --> 00:14:19.000 align:start position:0%
and of course you can find the code
 

00:14:19.000 --> 00:14:22.990 align:start position:0%
and of course you can find the code
itself<00:14:19.480><c> on</c><00:14:19.720><c> GitHub</c><00:14:20.600><c> we</c><00:14:20.800><c> are</c><00:14:21.399><c> open</c><00:14:21.759><c> source</c><00:14:22.600><c> and</c>

00:14:22.990 --> 00:14:23.000 align:start position:0%
itself on GitHub we are open source and
 

00:14:23.000 --> 00:14:25.629 align:start position:0%
itself on GitHub we are open source and
aache<00:14:23.720><c> license</c><00:14:24.720><c> whether</c><00:14:24.959><c> you</c><00:14:25.079><c> want</c><00:14:25.240><c> to</c><00:14:25.399><c> use</c>

00:14:25.629 --> 00:14:25.639 align:start position:0%
aache license whether you want to use
 

00:14:25.639 --> 00:14:28.670 align:start position:0%
aache license whether you want to use
Lance<00:14:26.320><c> or</c><00:14:26.600><c> become</c><00:14:26.839><c> a</c><00:14:27.399><c> contributor</c><00:14:28.399><c> let's</c>

00:14:28.670 --> 00:14:28.680 align:start position:0%
Lance or become a contributor let's
 

00:14:28.680 --> 00:14:31.150 align:start position:0%
Lance or become a contributor let's
build<00:14:29.360><c> the</c><00:14:29.519><c> new</c><00:14:29.880><c> open</c><00:14:30.160><c> source</c><00:14:30.440><c> foundation</c><00:14:30.959><c> for</c>

00:14:31.150 --> 00:14:31.160 align:start position:0%
build the new open source foundation for
 

00:14:31.160 --> 00:14:34.440 align:start position:0%
build the new open source foundation for
AI<00:14:31.480><c> data</c><00:14:31.959><c> together</c>

