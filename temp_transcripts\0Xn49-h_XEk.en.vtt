WEBVTT
Kind: captions
Language: en

00:00:08.320 --> 00:00:10.470 align:start position:0%
 
it's<00:00:08.639><c> finally</c><00:00:09.280><c> available</c>

00:00:10.470 --> 00:00:10.480 align:start position:0%
it's finally available
 

00:00:10.480 --> 00:00:13.830 align:start position:0%
it's finally available
quadrant<00:00:11.040><c> 0.8</c><00:00:11.920><c> has</c><00:00:12.160><c> been</c><00:00:12.320><c> released</c><00:00:13.120><c> we</c><00:00:13.440><c> made</c><00:00:13.679><c> a</c>

00:00:13.830 --> 00:00:13.840 align:start position:0%
quadrant 0.8 has been released we made a
 

00:00:13.840 --> 00:00:16.230 align:start position:0%
quadrant 0.8 has been released we made a
lot<00:00:14.080><c> of</c><00:00:14.240><c> performance</c><00:00:14.920><c> improvements</c><00:00:15.920><c> but</c>

00:00:16.230 --> 00:00:16.240 align:start position:0%
lot of performance improvements but
 

00:00:16.240 --> 00:00:17.990 align:start position:0%
lot of performance improvements but
there<00:00:16.480><c> are</c><00:00:16.720><c> also</c><00:00:17.119><c> some</c><00:00:17.440><c> brand</c><00:00:17.840><c> new</c>

00:00:17.990 --> 00:00:18.000 align:start position:0%
there are also some brand new
 

00:00:18.000 --> 00:00:19.990 align:start position:0%
there are also some brand new
functionalities<00:00:18.960><c> we've</c><00:00:19.119><c> been</c><00:00:19.359><c> all</c><00:00:19.600><c> waiting</c>

00:00:19.990 --> 00:00:20.000 align:start position:0%
functionalities we've been all waiting
 

00:00:20.000 --> 00:00:20.950 align:start position:0%
functionalities we've been all waiting
for

00:00:20.950 --> 00:00:20.960 align:start position:0%
for
 

00:00:20.960 --> 00:00:23.109 align:start position:0%
for
and<00:00:21.199><c> in</c><00:00:21.359><c> this</c><00:00:21.520><c> video</c><00:00:22.080><c> we</c><00:00:22.240><c> are</c><00:00:22.400><c> going</c><00:00:22.640><c> to</c><00:00:22.800><c> review</c>

00:00:23.109 --> 00:00:23.119 align:start position:0%
and in this video we are going to review
 

00:00:23.119 --> 00:00:24.390 align:start position:0%
and in this video we are going to review
them

00:00:24.390 --> 00:00:24.400 align:start position:0%
them
 

00:00:24.400 --> 00:00:26.870 align:start position:0%
them
quadrant<00:00:25.039><c> is</c><00:00:25.119><c> a</c><00:00:25.199><c> vector</c><00:00:25.519><c> database</c><00:00:26.240><c> making</c><00:00:26.640><c> it</c>

00:00:26.870 --> 00:00:26.880 align:start position:0%
quadrant is a vector database making it
 

00:00:26.880 --> 00:00:29.349 align:start position:0%
quadrant is a vector database making it
easy<00:00:27.279><c> to</c><00:00:27.439><c> perform</c><00:00:28.000><c> nearest</c><00:00:28.480><c> neighbors</c><00:00:28.960><c> search</c>

00:00:29.349 --> 00:00:29.359 align:start position:0%
easy to perform nearest neighbors search
 

00:00:29.359 --> 00:00:32.790 align:start position:0%
easy to perform nearest neighbors search
with<00:00:29.679><c> some</c><00:00:29.920><c> additional</c><00:00:30.640><c> filtering</c><00:00:31.199><c> criteria</c>

00:00:32.790 --> 00:00:32.800 align:start position:0%
with some additional filtering criteria
 

00:00:32.800 --> 00:00:35.030 align:start position:0%
with some additional filtering criteria
up<00:00:32.960><c> till</c><00:00:33.200><c> now</c><00:00:33.440><c> we</c><00:00:33.520><c> were</c><00:00:33.920><c> only</c><00:00:34.320><c> able</c><00:00:34.559><c> to</c><00:00:34.800><c> launch</c>

00:00:35.030 --> 00:00:35.040 align:start position:0%
up till now we were only able to launch
 

00:00:35.040 --> 00:00:37.270 align:start position:0%
up till now we were only able to launch
it<00:00:35.200><c> on</c><00:00:35.360><c> a</c><00:00:35.760><c> single</c><00:00:36.160><c> machine</c>

00:00:37.270 --> 00:00:37.280 align:start position:0%
it on a single machine
 

00:00:37.280 --> 00:00:40.229 align:start position:0%
it on a single machine
so<00:00:38.000><c> in</c><00:00:38.239><c> case</c><00:00:38.559><c> of</c><00:00:38.719><c> having</c><00:00:39.200><c> really</c><00:00:39.920><c> big</c>

00:00:40.229 --> 00:00:40.239 align:start position:0%
so in case of having really big
 

00:00:40.239 --> 00:00:41.510 align:start position:0%
so in case of having really big
collections

00:00:41.510 --> 00:00:41.520 align:start position:0%
collections
 

00:00:41.520 --> 00:00:45.270 align:start position:0%
collections
we<00:00:42.239><c> had</c><00:00:42.559><c> to</c><00:00:42.800><c> provide</c><00:00:43.360><c> the</c><00:00:43.520><c> machine</c><00:00:44.000><c> with</c><00:00:44.239><c> a</c>

00:00:45.270 --> 00:00:45.280 align:start position:0%
we had to provide the machine with a
 

00:00:45.280 --> 00:00:49.029 align:start position:0%
we had to provide the machine with a
relatively<00:00:46.239><c> huge</c><00:00:46.800><c> amount</c><00:00:47.200><c> of</c><00:00:47.360><c> memory</c>

00:00:49.029 --> 00:00:49.039 align:start position:0%
relatively huge amount of memory
 

00:00:49.039 --> 00:00:51.830 align:start position:0%
relatively huge amount of memory
that<00:00:49.360><c> also</c><00:00:49.680><c> had</c><00:00:49.840><c> another</c><00:00:50.239><c> drawback</c>

00:00:51.830 --> 00:00:51.840 align:start position:0%
that also had another drawback
 

00:00:51.840 --> 00:00:54.229 align:start position:0%
that also had another drawback
all<00:00:52.160><c> our</c><00:00:52.480><c> search</c><00:00:52.879><c> operations</c><00:00:53.760><c> have</c><00:00:53.920><c> been</c>

00:00:54.229 --> 00:00:54.239 align:start position:0%
all our search operations have been
 

00:00:54.239 --> 00:00:57.189 align:start position:0%
all our search operations have been
fully<00:00:54.640><c> executed</c><00:00:55.280><c> on</c><00:00:55.440><c> the</c><00:00:55.680><c> same</c><00:00:56.000><c> machine</c>

00:00:57.189 --> 00:00:57.199 align:start position:0%
fully executed on the same machine
 

00:00:57.199 --> 00:01:00.389 align:start position:0%
fully executed on the same machine
so<00:00:57.920><c> handling</c><00:00:58.399><c> heavy</c><00:00:58.800><c> traffic</c><00:00:59.359><c> from</c><00:00:59.840><c> multiple</c>

00:01:00.389 --> 00:01:00.399 align:start position:0%
so handling heavy traffic from multiple
 

00:01:00.399 --> 00:01:03.349 align:start position:0%
so handling heavy traffic from multiple
clients<00:01:01.120><c> at</c><00:01:01.280><c> the</c><00:01:01.520><c> same</c><00:01:01.920><c> time</c><00:01:02.719><c> could</c><00:01:02.960><c> become</c><00:01:03.280><c> a</c>

00:01:03.349 --> 00:01:03.359 align:start position:0%
clients at the same time could become a
 

00:01:03.359 --> 00:01:04.950 align:start position:0%
clients at the same time could become a
bottleneck

00:01:04.950 --> 00:01:04.960 align:start position:0%
bottleneck
 

00:01:04.960 --> 00:01:07.350 align:start position:0%
bottleneck
the<00:01:05.199><c> latest</c><00:01:05.600><c> version</c><00:01:06.000><c> of</c><00:01:06.159><c> quadrant</c><00:01:06.960><c> has</c><00:01:07.200><c> the</c>

00:01:07.350 --> 00:01:07.360 align:start position:0%
the latest version of quadrant has the
 

00:01:07.360 --> 00:01:10.390 align:start position:0%
the latest version of quadrant has the
experimental<00:01:08.159><c> distributed</c><00:01:08.880><c> mode</c><00:01:09.200><c> available</c>

00:01:10.390 --> 00:01:10.400 align:start position:0%
experimental distributed mode available
 

00:01:10.400 --> 00:01:12.630 align:start position:0%
experimental distributed mode available
so<00:01:10.640><c> now</c><00:01:11.040><c> you</c><00:01:11.280><c> are</c><00:01:11.439><c> able</c><00:01:11.680><c> to</c><00:01:12.000><c> launch</c><00:01:12.320><c> it</c><00:01:12.479><c> with</c>

00:01:12.630 --> 00:01:12.640 align:start position:0%
so now you are able to launch it with
 

00:01:12.640 --> 00:01:15.190 align:start position:0%
so now you are able to launch it with
the<00:01:12.799><c> data</c><00:01:13.280><c> spread</c><00:01:13.760><c> across</c><00:01:14.240><c> the</c><00:01:14.479><c> cluster</c><00:01:15.040><c> of</c>

00:01:15.190 --> 00:01:15.200 align:start position:0%
the data spread across the cluster of
 

00:01:15.200 --> 00:01:17.749 align:start position:0%
the data spread across the cluster of
machines

00:01:17.749 --> 00:01:17.759 align:start position:0%
machines
 

00:01:17.759 --> 00:01:20.149 align:start position:0%
machines
under<00:01:18.080><c> the</c><00:01:18.240><c> hood</c><00:01:18.560><c> we</c><00:01:18.720><c> are</c><00:01:18.880><c> using</c><00:01:19.280><c> horizontal</c>

00:01:20.149 --> 00:01:20.159 align:start position:0%
under the hood we are using horizontal
 

00:01:20.159 --> 00:01:22.469 align:start position:0%
under the hood we are using horizontal
partitioning<00:01:21.119><c> so</c><00:01:21.600><c> your</c><00:01:21.840><c> collection</c><00:01:22.320><c> is</c>

00:01:22.469 --> 00:01:22.479 align:start position:0%
partitioning so your collection is
 

00:01:22.479 --> 00:01:25.749 align:start position:0%
partitioning so your collection is
divided<00:01:23.119><c> into</c><00:01:23.439><c> several</c><00:01:24.000><c> parts</c><00:01:24.640><c> or</c><00:01:24.960><c> charts</c>

00:01:25.749 --> 00:01:25.759 align:start position:0%
divided into several parts or charts
 

00:01:25.759 --> 00:01:28.630 align:start position:0%
divided into several parts or charts
which<00:01:26.080><c> do</c><00:01:26.240><c> not</c><00:01:26.479><c> intersect</c><00:01:27.119><c> each</c><00:01:27.280><c> other</c>

00:01:28.630 --> 00:01:28.640 align:start position:0%
which do not intersect each other
 

00:01:28.640 --> 00:01:30.710 align:start position:0%
which do not intersect each other
that<00:01:28.880><c> means</c><00:01:29.119><c> your</c><00:01:29.439><c> query</c><00:01:29.840><c> might</c><00:01:30.000><c> be</c><00:01:30.240><c> launched</c>

00:01:30.710 --> 00:01:30.720 align:start position:0%
that means your query might be launched
 

00:01:30.720 --> 00:01:33.910 align:start position:0%
that means your query might be launched
in<00:01:30.960><c> parallel</c><00:01:31.759><c> on</c><00:01:32.079><c> all</c><00:01:32.240><c> the</c><00:01:32.400><c> machines</c><00:01:32.960><c> at</c><00:01:33.119><c> once</c>

00:01:33.910 --> 00:01:33.920 align:start position:0%
in parallel on all the machines at once
 

00:01:33.920 --> 00:01:36.149 align:start position:0%
in parallel on all the machines at once
as<00:01:34.240><c> each</c><00:01:34.479><c> of</c><00:01:34.640><c> them</c><00:01:34.880><c> will</c><00:01:35.119><c> be</c><00:01:35.439><c> working</c><00:01:35.840><c> on</c><00:01:36.000><c> a</c>

00:01:36.149 --> 00:01:36.159 align:start position:0%
as each of them will be working on a
 

00:01:36.159 --> 00:01:38.390 align:start position:0%
as each of them will be working on a
subset<00:01:36.720><c> of</c><00:01:36.880><c> points</c><00:01:37.280><c> only</c>

00:01:38.390 --> 00:01:38.400 align:start position:0%
subset of points only
 

00:01:38.400 --> 00:01:39.749 align:start position:0%
subset of points only
there<00:01:38.560><c> is</c><00:01:38.720><c> also</c><00:01:38.880><c> a</c><00:01:39.040><c> plan</c><00:01:39.280><c> to</c><00:01:39.360><c> support</c>

00:01:39.749 --> 00:01:39.759 align:start position:0%
there is also a plan to support
 

00:01:39.759 --> 00:01:42.069 align:start position:0%
there is also a plan to support
replication<00:01:40.479><c> in</c><00:01:40.640><c> the</c><00:01:40.720><c> next</c><00:01:41.040><c> releases</c><00:01:41.840><c> to</c>

00:01:42.069 --> 00:01:42.079 align:start position:0%
replication in the next releases to
 

00:01:42.079 --> 00:01:45.990 align:start position:0%
replication in the next releases to
bring<00:01:42.320><c> default</c><00:01:42.799><c> tolerance</c><00:01:43.439><c> property</c>

00:01:45.990 --> 00:01:46.000 align:start position:0%
 
 

00:01:46.000 --> 00:01:48.950 align:start position:0%
 
but<00:01:46.240><c> the</c><00:01:46.320><c> distributed</c><00:01:47.040><c> mode</c><00:01:47.759><c> is</c><00:01:48.079><c> not</c><00:01:48.320><c> the</c><00:01:48.560><c> only</c>

00:01:48.950 --> 00:01:48.960 align:start position:0%
but the distributed mode is not the only
 

00:01:48.960 --> 00:01:51.670 align:start position:0%
but the distributed mode is not the only
major<00:01:49.439><c> change</c><00:01:49.759><c> in</c><00:01:49.920><c> the</c><00:01:50.000><c> current</c><00:01:50.399><c> release</c>

00:01:51.670 --> 00:01:51.680 align:start position:0%
major change in the current release
 

00:01:51.680 --> 00:01:53.670 align:start position:0%
major change in the current release
quadrant<00:01:52.159><c> allows</c><00:01:52.479><c> you</c><00:01:52.640><c> to</c><00:01:52.799><c> search</c><00:01:53.119><c> not</c><00:01:53.360><c> only</c>

00:01:53.670 --> 00:01:53.680 align:start position:0%
quadrant allows you to search not only
 

00:01:53.680 --> 00:01:56.310 align:start position:0%
quadrant allows you to search not only
using<00:01:54.079><c> vector</c><00:01:54.479><c> similarity</c><00:01:55.119><c> but</c><00:01:55.600><c> also</c><00:01:56.079><c> with</c>

00:01:56.310 --> 00:01:56.320 align:start position:0%
using vector similarity but also with
 

00:01:56.320 --> 00:01:59.190 align:start position:0%
using vector similarity but also with
some<00:01:56.640><c> conventional</c><00:01:57.360><c> filters</c><00:01:58.320><c> so</c><00:01:58.799><c> only</c><00:01:59.040><c> the</c>

00:01:59.190 --> 00:01:59.200 align:start position:0%
some conventional filters so only the
 

00:01:59.200 --> 00:02:01.030 align:start position:0%
some conventional filters so only the
points<00:01:59.600><c> matching</c><00:01:59.920><c> the</c><00:02:00.079><c> criteria</c><00:02:00.880><c> are</c>

00:02:01.030 --> 00:02:01.040 align:start position:0%
points matching the criteria are
 

00:02:01.040 --> 00:02:02.789 align:start position:0%
points matching the criteria are
returned

00:02:02.789 --> 00:02:02.799 align:start position:0%
returned
 

00:02:02.799 --> 00:02:05.190 align:start position:0%
returned
to<00:02:03.040><c> use</c><00:02:03.200><c> this</c><00:02:03.439><c> filtering</c><00:02:04.399><c> you</c><00:02:04.560><c> should</c><00:02:04.799><c> provide</c>

00:02:05.190 --> 00:02:05.200 align:start position:0%
to use this filtering you should provide
 

00:02:05.200 --> 00:02:08.070 align:start position:0%
to use this filtering you should provide
the<00:02:05.360><c> json</c><00:02:05.920><c> payload</c><00:02:06.479><c> along</c><00:02:06.799><c> with</c><00:02:07.040><c> the</c><00:02:07.200><c> vector</c>

00:02:08.070 --> 00:02:08.080 align:start position:0%
the json payload along with the vector
 

00:02:08.080 --> 00:02:10.389 align:start position:0%
the json payload along with the vector
and<00:02:08.239><c> then</c><00:02:08.479><c> just</c><00:02:08.720><c> query</c><00:02:09.200><c> based</c><00:02:09.520><c> on</c><00:02:09.759><c> selected</c>

00:02:10.389 --> 00:02:10.399 align:start position:0%
and then just query based on selected
 

00:02:10.399 --> 00:02:12.150 align:start position:0%
and then just query based on selected
attribute<00:02:10.879><c> values</c>

00:02:12.150 --> 00:02:12.160 align:start position:0%
attribute values
 

00:02:12.160 --> 00:02:14.630 align:start position:0%
attribute values
or<00:02:12.640><c> several</c><00:02:13.040><c> attributes</c><00:02:13.760><c> at</c><00:02:13.920><c> the</c><00:02:14.080><c> same</c><00:02:14.319><c> time</c>

00:02:14.630 --> 00:02:14.640 align:start position:0%
or several attributes at the same time
 

00:02:14.640 --> 00:02:16.070 align:start position:0%
or several attributes at the same time
as<00:02:14.800><c> well</c>

00:02:16.070 --> 00:02:16.080 align:start position:0%
as well
 

00:02:16.080 --> 00:02:17.190 align:start position:0%
as well
previously

00:02:17.190 --> 00:02:17.200 align:start position:0%
previously
 

00:02:17.200 --> 00:02:20.229 align:start position:0%
previously
all<00:02:17.520><c> your</c><00:02:17.920><c> payloads</c><00:02:18.560><c> were</c><00:02:18.879><c> loaded</c><00:02:19.360><c> into</c><00:02:19.599><c> ram</c>

00:02:20.229 --> 00:02:20.239 align:start position:0%
all your payloads were loaded into ram
 

00:02:20.239 --> 00:02:22.150 align:start position:0%
all your payloads were loaded into ram
which<00:02:20.480><c> could</c><00:02:20.720><c> have</c><00:02:20.800><c> slightly</c><00:02:21.360><c> increased</c><00:02:21.840><c> your</c>

00:02:22.150 --> 00:02:22.160 align:start position:0%
which could have slightly increased your
 

00:02:22.160 --> 00:02:24.309 align:start position:0%
which could have slightly increased your
memory<00:02:22.720><c> requirements</c>

00:02:24.309 --> 00:02:24.319 align:start position:0%
memory requirements
 

00:02:24.319 --> 00:02:26.470 align:start position:0%
memory requirements
right<00:02:24.560><c> now</c><00:02:25.200><c> if</c><00:02:25.360><c> you</c><00:02:25.680><c> already</c><00:02:26.080><c> know</c><00:02:26.400><c> a</c>

00:02:26.470 --> 00:02:26.480 align:start position:0%
right now if you already know a
 

00:02:26.480 --> 00:02:28.630 align:start position:0%
right now if you already know a
particular<00:02:27.200><c> collection</c><00:02:27.840><c> won't</c><00:02:28.000><c> be</c><00:02:28.239><c> accessing</c>

00:02:28.630 --> 00:02:28.640 align:start position:0%
particular collection won't be accessing
 

00:02:28.640 --> 00:02:31.270 align:start position:0%
particular collection won't be accessing
the<00:02:28.800><c> payloads</c><00:02:29.360><c> that</c><00:02:29.599><c> often</c><00:02:30.400><c> you</c><00:02:30.560><c> can</c><00:02:30.879><c> create</c>

00:02:31.270 --> 00:02:31.280 align:start position:0%
the payloads that often you can create
 

00:02:31.280 --> 00:02:33.589 align:start position:0%
the payloads that often you can create
it<00:02:31.440><c> in</c><00:02:31.599><c> a</c><00:02:31.680><c> way</c><00:02:32.000><c> that</c><00:02:32.239><c> it</c><00:02:32.480><c> will</c><00:02:32.640><c> be</c><00:02:32.959><c> storing</c><00:02:33.440><c> all</c>

00:02:33.589 --> 00:02:33.599 align:start position:0%
it in a way that it will be storing all
 

00:02:33.599 --> 00:02:37.030 align:start position:0%
it in a way that it will be storing all
the<00:02:33.840><c> payloads</c><00:02:34.400><c> on</c><00:02:34.560><c> disk</c><00:02:34.959><c> only</c><00:02:36.160><c> and</c><00:02:36.560><c> that</c><00:02:36.800><c> will</c>

00:02:37.030 --> 00:02:37.040 align:start position:0%
the payloads on disk only and that will
 

00:02:37.040 --> 00:02:39.910 align:start position:0%
the payloads on disk only and that will
reduce<00:02:37.519><c> the</c><00:02:37.680><c> amount</c><00:02:38.000><c> of</c><00:02:38.160><c> memory</c><00:02:38.560><c> being</c><00:02:38.879><c> used</c>

00:02:39.910 --> 00:02:39.920 align:start position:0%
reduce the amount of memory being used
 

00:02:39.920 --> 00:02:43.270 align:start position:0%
reduce the amount of memory being used
but<00:02:40.560><c> it</c><00:02:40.640><c> may</c><00:02:40.959><c> also</c><00:02:41.519><c> increase</c><00:02:41.920><c> the</c><00:02:42.080><c> latency</c><00:02:43.120><c> if</c>

00:02:43.270 --> 00:02:43.280 align:start position:0%
but it may also increase the latency if
 

00:02:43.280 --> 00:02:45.509 align:start position:0%
but it may also increase the latency if
you<00:02:43.440><c> decide</c><00:02:43.920><c> to</c><00:02:44.160><c> use</c><00:02:44.400><c> the</c><00:02:44.560><c> filters</c>

00:02:45.509 --> 00:02:45.519 align:start position:0%
you decide to use the filters
 

00:02:45.519 --> 00:02:47.190 align:start position:0%
you decide to use the filters
extensively

00:02:47.190 --> 00:02:47.200 align:start position:0%
extensively
 

00:02:47.200 --> 00:02:49.750 align:start position:0%
extensively
some<00:02:47.599><c> io</c><00:02:48.000><c> operations</c><00:02:48.640><c> will</c><00:02:48.800><c> be</c><00:02:48.959><c> executed</c>

00:02:49.750 --> 00:02:49.760 align:start position:0%
some io operations will be executed
 

00:02:49.760 --> 00:02:52.070 align:start position:0%
some io operations will be executed
while<00:02:50.160><c> searching</c><00:02:51.040><c> so</c>

00:02:52.070 --> 00:02:52.080 align:start position:0%
while searching so
 

00:02:52.080 --> 00:02:53.350 align:start position:0%
while searching so
be<00:02:52.319><c> careful</c>

00:02:53.350 --> 00:02:53.360 align:start position:0%
be careful
 

00:02:53.360 --> 00:02:56.390 align:start position:0%
be careful
but<00:02:53.680><c> if</c><00:02:53.920><c> you</c><00:02:54.080><c> know</c><00:02:54.400><c> your</c><00:02:54.720><c> data</c><00:02:55.440><c> and</c><00:02:55.680><c> the</c><00:02:55.840><c> way</c><00:02:56.239><c> it</c>

00:02:56.390 --> 00:02:56.400 align:start position:0%
but if you know your data and the way it
 

00:02:56.400 --> 00:02:58.229 align:start position:0%
but if you know your data and the way it
is<00:02:56.560><c> going</c><00:02:56.800><c> to</c><00:02:56.959><c> be</c><00:02:57.200><c> queried</c>

00:02:58.229 --> 00:02:58.239 align:start position:0%
is going to be queried
 

00:02:58.239 --> 00:03:00.229 align:start position:0%
is going to be queried
you<00:02:58.400><c> might</c><00:02:58.640><c> be</c><00:02:58.800><c> happy</c><00:02:59.120><c> to</c><00:02:59.360><c> see</c><00:02:59.680><c> you</c><00:02:59.840><c> can</c><00:03:00.000><c> now</c>

00:03:00.229 --> 00:03:00.239 align:start position:0%
you might be happy to see you can now
 

00:03:00.239 --> 00:03:03.670 align:start position:0%
you might be happy to see you can now
store<00:03:00.720><c> more</c><00:03:01.040><c> vectors</c><00:03:01.840><c> on</c><00:03:02.000><c> the</c><00:03:02.239><c> same</c><00:03:02.480><c> machine</c>

00:03:03.670 --> 00:03:03.680 align:start position:0%
store more vectors on the same machine
 

00:03:03.680 --> 00:03:06.149 align:start position:0%
store more vectors on the same machine
or<00:03:04.239><c> just</c><00:03:04.640><c> cut</c><00:03:04.879><c> off</c><00:03:05.040><c> some</c><00:03:05.280><c> of</c><00:03:05.360><c> your</c><00:03:05.599><c> expenses</c>

00:03:06.149 --> 00:03:06.159 align:start position:0%
or just cut off some of your expenses
 

00:03:06.159 --> 00:03:09.430 align:start position:0%
or just cut off some of your expenses
because<00:03:06.560><c> you</c><00:03:06.959><c> don't</c><00:03:07.200><c> necessarily</c><00:03:07.920><c> need</c><00:03:08.440><c> 128</c>

00:03:09.430 --> 00:03:09.440 align:start position:0%
because you don't necessarily need 128
 

00:03:09.440 --> 00:03:11.990 align:start position:0%
because you don't necessarily need 128
gigabytes<00:03:10.000><c> of</c><00:03:10.239><c> ram</c><00:03:10.800><c> to</c><00:03:10.959><c> perform</c><00:03:11.440><c> a</c><00:03:11.519><c> neural</c>

00:03:11.990 --> 00:03:12.000 align:start position:0%
gigabytes of ram to perform a neural
 

00:03:12.000 --> 00:03:12.949 align:start position:0%
gigabytes of ram to perform a neural
search

00:03:12.949 --> 00:03:12.959 align:start position:0%
search
 

00:03:12.959 --> 00:03:14.229 align:start position:0%
search
do<00:03:13.200><c> you</c>

00:03:14.229 --> 00:03:14.239 align:start position:0%
do you
 

00:03:14.239 --> 00:03:16.070 align:start position:0%
do you
and<00:03:14.480><c> last</c><00:03:14.800><c> but</c><00:03:14.959><c> not</c><00:03:15.200><c> least</c>

00:03:16.070 --> 00:03:16.080 align:start position:0%
and last but not least
 

00:03:16.080 --> 00:03:18.229 align:start position:0%
and last but not least
there<00:03:16.239><c> is</c><00:03:16.400><c> a</c><00:03:16.480><c> possibility</c><00:03:17.280><c> to</c><00:03:17.519><c> filter</c><00:03:18.000><c> by</c>

00:03:18.229 --> 00:03:18.239 align:start position:0%
there is a possibility to filter by
 

00:03:18.239 --> 00:03:20.070 align:start position:0%
there is a possibility to filter by
similarity<00:03:18.879><c> score</c>

00:03:20.070 --> 00:03:20.080 align:start position:0%
similarity score
 

00:03:20.080 --> 00:03:22.229 align:start position:0%
similarity score
many<00:03:20.319><c> of</c><00:03:20.480><c> you</c><00:03:20.720><c> have</c><00:03:20.959><c> asked</c><00:03:21.200><c> for</c><00:03:21.360><c> that</c><00:03:21.599><c> feature</c>

00:03:22.229 --> 00:03:22.239 align:start position:0%
many of you have asked for that feature
 

00:03:22.239 --> 00:03:25.270 align:start position:0%
many of you have asked for that feature
so<00:03:22.800><c> we</c><00:03:22.959><c> made</c><00:03:23.280><c> that</c><00:03:23.440><c> dream</c><00:03:23.760><c> come</c><00:03:24.000><c> true</c>

00:03:25.270 --> 00:03:25.280 align:start position:0%
so we made that dream come true
 

00:03:25.280 --> 00:03:27.350 align:start position:0%
so we made that dream come true
if<00:03:25.519><c> you</c><00:03:25.760><c> know</c><00:03:26.000><c> your</c><00:03:26.159><c> distance</c><00:03:26.720><c> function</c><00:03:27.200><c> and</c>

00:03:27.350 --> 00:03:27.360 align:start position:0%
if you know your distance function and
 

00:03:27.360 --> 00:03:30.630 align:start position:0%
if you know your distance function and
have<00:03:27.599><c> a</c><00:03:27.680><c> specific</c><00:03:28.799><c> acceptance</c><00:03:29.440><c> threshold</c><00:03:30.480><c> you</c>

00:03:30.630 --> 00:03:30.640 align:start position:0%
have a specific acceptance threshold you
 

00:03:30.640 --> 00:03:34.550 align:start position:0%
have a specific acceptance threshold you
can<00:03:30.879><c> now</c><00:03:31.360><c> put</c><00:03:31.680><c> it</c><00:03:32.159><c> into</c><00:03:32.560><c> your</c><00:03:33.040><c> search</c><00:03:33.360><c> query</c>

00:03:34.550 --> 00:03:34.560 align:start position:0%
can now put it into your search query
 

00:03:34.560 --> 00:03:37.430 align:start position:0%
can now put it into your search query
and<00:03:34.959><c> you</c><00:03:35.200><c> won't</c><00:03:35.440><c> even</c><00:03:35.840><c> see</c><00:03:36.319><c> anything</c>

00:03:37.430 --> 00:03:37.440 align:start position:0%
and you won't even see anything
 

00:03:37.440 --> 00:03:41.509 align:start position:0%
and you won't even see anything
that<00:03:37.760><c> falls</c><00:03:38.080><c> below</c>

00:03:41.509 --> 00:03:41.519 align:start position:0%
 
 

00:03:41.519 --> 00:03:43.910 align:start position:0%
 
how<00:03:41.680><c> do</c><00:03:41.840><c> you</c><00:03:41.920><c> enjoy</c><00:03:42.239><c> using</c><00:03:42.480><c> quadrants</c><00:03:42.879><c> so</c><00:03:43.040><c> far</c>

00:03:43.910 --> 00:03:43.920 align:start position:0%
how do you enjoy using quadrants so far
 

00:03:43.920 --> 00:03:45.270 align:start position:0%
how do you enjoy using quadrants so far
please<00:03:44.159><c> let</c><00:03:44.400><c> us</c><00:03:44.480><c> know</c><00:03:44.720><c> in</c><00:03:44.799><c> the</c><00:03:44.959><c> comment</c>

00:03:45.270 --> 00:03:45.280 align:start position:0%
please let us know in the comment
 

00:03:45.280 --> 00:03:46.949 align:start position:0%
please let us know in the comment
section<00:03:45.680><c> below</c>

00:03:46.949 --> 00:03:46.959 align:start position:0%
section below
 

00:03:46.959 --> 00:03:48.789 align:start position:0%
section below
and<00:03:47.120><c> if</c><00:03:47.280><c> you</c><00:03:47.519><c> don't</c>

00:03:48.789 --> 00:03:48.799 align:start position:0%
and if you don't
 

00:03:48.799 --> 00:03:50.550 align:start position:0%
and if you don't
let's<00:03:49.040><c> see</c><00:03:49.200><c> our</c><00:03:49.519><c> github</c>

00:03:50.550 --> 00:03:50.560 align:start position:0%
let's see our github
 

00:03:50.560 --> 00:03:54.400 align:start position:0%
let's see our github
and<00:03:50.959><c> give</c><00:03:51.200><c> us</c><00:03:51.360><c> a</c><00:03:51.440><c> star</c>

