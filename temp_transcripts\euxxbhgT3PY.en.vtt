WEBVTT
Kind: captions
Language: en

00:00:00.730 --> 00:00:05.470 align:start position:0%
 
[Music]

00:00:05.470 --> 00:00:05.480 align:start position:0%
 
 

00:00:05.480 --> 00:00:07.749 align:start position:0%
 
hi<00:00:05.640><c> folks</c><00:00:06.000><c> it's</c><00:00:06.160><c> Anton</c><00:00:06.520><c> here</c><00:00:06.640><c> from</c><00:00:06.799><c> chroma</c><00:00:07.560><c> um</c>

00:00:07.749 --> 00:00:07.759 align:start position:0%
hi folks it's Anton here from chroma um
 

00:00:07.759 --> 00:00:08.830 align:start position:0%
hi folks it's <PERSON> here from chroma um
here<00:00:07.879><c> to</c><00:00:08.040><c> talk</c><00:00:08.200><c> to</c><00:00:08.240><c> you</c><00:00:08.320><c> a</c><00:00:08.400><c> little</c><00:00:08.519><c> bit</c><00:00:08.639><c> about</c>

00:00:08.830 --> 00:00:08.840 align:start position:0%
here to talk to you a little bit about
 

00:00:08.840 --> 00:00:10.629 align:start position:0%
here to talk to you a little bit about
embedding<00:00:09.200><c> stores</c><00:00:09.440><c> and</c><00:00:09.599><c> Vector</c><00:00:09.840><c> databases</c>

00:00:10.629 --> 00:00:10.639 align:start position:0%
embedding stores and Vector databases
 

00:00:10.639 --> 00:00:13.950 align:start position:0%
embedding stores and Vector databases
and<00:00:10.759><c> how</c><00:00:10.920><c> they</c><00:00:11.080><c> work</c><00:00:11.440><c> in</c><00:00:11.960><c> the</c><00:00:12.160><c> context</c><00:00:12.840><c> of</c><00:00:13.320><c> llm</c>

00:00:13.950 --> 00:00:13.960 align:start position:0%
and how they work in the context of llm
 

00:00:13.960 --> 00:00:15.509 align:start position:0%
and how they work in the context of llm
in<00:00:14.080><c> the</c><00:00:14.240><c> loop</c><00:00:14.639><c> applications</c><00:00:15.200><c> like</c><00:00:15.320><c> the</c><00:00:15.400><c> One</c>

00:00:15.509 --> 00:00:15.519 align:start position:0%
in the loop applications like the One
 

00:00:15.519 --> 00:00:18.390 align:start position:0%
in the loop applications like the One
You're<00:00:15.679><c> Building</c><00:00:16.000><c> in</c><00:00:16.160><c> this</c><00:00:16.359><c> course</c><00:00:17.359><c> so</c><00:00:18.039><c> by</c><00:00:18.199><c> now</c>

00:00:18.390 --> 00:00:18.400 align:start position:0%
You're Building in this course so by now
 

00:00:18.400 --> 00:00:19.830 align:start position:0%
You're Building in this course so by now
you've<00:00:18.560><c> learned</c><00:00:18.840><c> a</c><00:00:18.960><c> little</c><00:00:19.119><c> bit</c><00:00:19.320><c> about</c>

00:00:19.830 --> 00:00:19.840 align:start position:0%
you've learned a little bit about
 

00:00:19.840 --> 00:00:22.830 align:start position:0%
you've learned a little bit about
embeddings<00:00:20.840><c> and</c><00:00:21.119><c> nearest</c><00:00:21.560><c> neighbor</c><00:00:22.279><c> and</c><00:00:22.720><c> you</c>

00:00:22.830 --> 00:00:22.840 align:start position:0%
embeddings and nearest neighbor and you
 

00:00:22.840 --> 00:00:24.990 align:start position:0%
embeddings and nearest neighbor and you
know<00:00:23.000><c> how</c><00:00:23.119><c> to</c><00:00:23.279><c> use</c><00:00:23.480><c> them</c><00:00:23.640><c> for</c><00:00:24.080><c> similarity</c>

00:00:24.990 --> 00:00:25.000 align:start position:0%
know how to use them for similarity
 

00:00:25.000 --> 00:00:27.269 align:start position:0%
know how to use them for similarity
search<00:00:26.000><c> um</c><00:00:26.480><c> I</c><00:00:26.560><c> want</c><00:00:26.679><c> to</c><00:00:26.800><c> talk</c><00:00:26.920><c> to</c><00:00:27.000><c> you</c><00:00:27.080><c> a</c><00:00:27.160><c> little</c>

00:00:27.269 --> 00:00:27.279 align:start position:0%
search um I want to talk to you a little
 

00:00:27.279 --> 00:00:28.669 align:start position:0%
search um I want to talk to you a little
bit<00:00:27.439><c> about</c><00:00:27.599><c> some</c><00:00:27.720><c> of</c><00:00:27.840><c> the</c><00:00:27.960><c> software</c><00:00:28.320><c> packages</c>

00:00:28.669 --> 00:00:28.679 align:start position:0%
bit about some of the software packages
 

00:00:28.679 --> 00:00:30.269 align:start position:0%
bit about some of the software packages
and<00:00:28.800><c> tools</c><00:00:29.080><c> available</c><00:00:29.439><c> for</c><00:00:29.599><c> you</c><00:00:29.720><c> that</c><00:00:30.039><c> make</c>

00:00:30.269 --> 00:00:30.279 align:start position:0%
and tools available for you that make
 

00:00:30.279 --> 00:00:32.109 align:start position:0%
and tools available for you that make
that<00:00:30.640><c> a</c><00:00:30.759><c> bit</c><00:00:30.960><c> easier</c><00:00:31.439><c> especially</c><00:00:31.840><c> if</c><00:00:31.920><c> you're</c>

00:00:32.109 --> 00:00:32.119 align:start position:0%
that a bit easier especially if you're
 

00:00:32.119 --> 00:00:33.750 align:start position:0%
that a bit easier especially if you're
working<00:00:32.439><c> with</c><00:00:32.599><c> large</c><00:00:32.840><c> amounts</c><00:00:33.120><c> of</c><00:00:33.280><c> data</c><00:00:33.600><c> or</c>

00:00:33.750 --> 00:00:33.760 align:start position:0%
working with large amounts of data or
 

00:00:33.760 --> 00:00:35.670 align:start position:0%
working with large amounts of data or
you're<00:00:33.960><c> ready</c><00:00:34.120><c> to</c><00:00:34.280><c> build</c><00:00:34.440><c> a</c><00:00:34.600><c> production</c><00:00:34.960><c> grade</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
you're ready to build a production grade
 

00:00:35.680 --> 00:00:38.229 align:start position:0%
you're ready to build a production grade
application<00:00:36.680><c> so</c><00:00:36.920><c> let's</c><00:00:37.120><c> get</c><00:00:37.280><c> right</c><00:00:37.440><c> into</c><00:00:37.640><c> it</c>

00:00:38.229 --> 00:00:38.239 align:start position:0%
application so let's get right into it
 

00:00:38.239 --> 00:00:40.389 align:start position:0%
application so let's get right into it
what<00:00:38.440><c> is</c><00:00:38.680><c> an</c><00:00:38.840><c> embedding</c><00:00:39.200><c> store</c><00:00:39.960><c> an</c><00:00:40.079><c> embedding</c>

00:00:40.389 --> 00:00:40.399 align:start position:0%
what is an embedding store an embedding
 

00:00:40.399 --> 00:00:41.790 align:start position:0%
what is an embedding store an embedding
store<00:00:40.640><c> is</c><00:00:40.760><c> basically</c><00:00:41.039><c> a</c><00:00:41.160><c> software</c><00:00:41.559><c> package</c>

00:00:41.790 --> 00:00:41.800 align:start position:0%
store is basically a software package
 

00:00:41.800 --> 00:00:43.029 align:start position:0%
store is basically a software package
that<00:00:41.960><c> abstracts</c><00:00:42.399><c> away</c><00:00:42.600><c> a</c><00:00:42.680><c> lot</c><00:00:42.800><c> of</c><00:00:42.920><c> the</c>

00:00:43.029 --> 00:00:43.039 align:start position:0%
that abstracts away a lot of the
 

00:00:43.039 --> 00:00:44.430 align:start position:0%
that abstracts away a lot of the
operations<00:00:43.520><c> that</c><00:00:43.640><c> you've</c><00:00:43.840><c> already</c><00:00:44.079><c> learned</c>

00:00:44.430 --> 00:00:44.440 align:start position:0%
operations that you've already learned
 

00:00:44.440 --> 00:00:47.510 align:start position:0%
operations that you've already learned
about<00:00:45.079><c> so</c><00:00:45.879><c> first</c><00:00:46.239><c> you</c><00:00:46.440><c> take</c><00:00:46.960><c> a</c><00:00:47.160><c> set</c><00:00:47.320><c> of</c>

00:00:47.510 --> 00:00:47.520 align:start position:0%
about so first you take a set of
 

00:00:47.520 --> 00:00:49.069 align:start position:0%
about so first you take a set of
documents<00:00:47.960><c> that</c><00:00:48.120><c> represent</c><00:00:48.520><c> your</c><00:00:48.680><c> knowledge</c>

00:00:49.069 --> 00:00:49.079 align:start position:0%
documents that represent your knowledge
 

00:00:49.079 --> 00:00:51.430 align:start position:0%
documents that represent your knowledge
base<00:00:49.719><c> and</c><00:00:50.039><c> you</c><00:00:50.280><c> can</c><00:00:50.440><c> use</c><00:00:50.680><c> the</c><00:00:50.800><c> embedding</c><00:00:51.160><c> store</c>

00:00:51.430 --> 00:00:51.440 align:start position:0%
base and you can use the embedding store
 

00:00:51.440 --> 00:00:53.110 align:start position:0%
base and you can use the embedding store
to<00:00:51.600><c> embed</c><00:00:51.920><c> them</c><00:00:52.039><c> using</c><00:00:52.320><c> its</c><00:00:52.480><c> embedding</c>

00:00:53.110 --> 00:00:53.120 align:start position:0%
to embed them using its embedding
 

00:00:53.120 --> 00:00:56.069 align:start position:0%
to embed them using its embedding
function<00:00:54.120><c> and</c><00:00:54.239><c> then</c><00:00:54.359><c> when</c><00:00:54.480><c> a</c><00:00:54.680><c> query</c><00:00:55.039><c> comes</c><00:00:55.320><c> in</c>

00:00:56.069 --> 00:00:56.079 align:start position:0%
function and then when a query comes in
 

00:00:56.079 --> 00:00:57.630 align:start position:0%
function and then when a query comes in
the<00:00:56.239><c> same</c><00:00:56.440><c> embedding</c><00:00:56.840><c> function</c><00:00:57.160><c> gets</c><00:00:57.320><c> called</c>

00:00:57.630 --> 00:00:57.640 align:start position:0%
the same embedding function gets called
 

00:00:57.640 --> 00:00:59.590 align:start position:0%
the same embedding function gets called
it<00:00:57.760><c> generates</c><00:00:58.239><c> an</c><00:00:58.399><c> embedding</c><00:00:59.280><c> uh</c><00:00:59.399><c> as</c><00:00:59.519><c> the</c>

00:00:59.590 --> 00:00:59.600 align:start position:0%
it generates an embedding uh as the
 

00:00:59.600 --> 00:01:02.110 align:start position:0%
it generates an embedding uh as the
query<00:01:00.039><c> embedding</c><00:01:00.519><c> and</c><00:01:00.680><c> then</c><00:01:01.160><c> the</c><00:01:01.760><c> embedding</c>

00:01:02.110 --> 00:01:02.120 align:start position:0%
query embedding and then the embedding
 

00:01:02.120 --> 00:01:03.750 align:start position:0%
query embedding and then the embedding
store<00:01:02.480><c> itself</c><00:01:03.039><c> performs</c><00:01:03.399><c> the</c><00:01:03.480><c> nearest</c>

00:01:03.750 --> 00:01:03.760 align:start position:0%
store itself performs the nearest
 

00:01:03.760 --> 00:01:06.590 align:start position:0%
store itself performs the nearest
neighbor<00:01:04.040><c> search</c><00:01:04.360><c> for</c><00:01:04.559><c> you</c><00:01:05.280><c> and</c><00:01:05.720><c> Returns</c><00:01:06.439><c> the</c>

00:01:06.590 --> 00:01:06.600 align:start position:0%
neighbor search for you and Returns the
 

00:01:06.600 --> 00:01:08.510 align:start position:0%
neighbor search for you and Returns the
relevant<00:01:07.040><c> documents</c><00:01:07.439><c> to</c><00:01:07.560><c> the</c><00:01:07.680><c> llm</c><00:01:08.159><c> context</c>

00:01:08.510 --> 00:01:08.520 align:start position:0%
relevant documents to the llm context
 

00:01:08.520 --> 00:01:10.510 align:start position:0%
relevant documents to the llm context
window<00:01:08.920><c> thus</c><00:01:09.280><c> basically</c><00:01:09.720><c> simplifying</c><00:01:10.320><c> a</c><00:01:10.400><c> lot</c>

00:01:10.510 --> 00:01:10.520 align:start position:0%
window thus basically simplifying a lot
 

00:01:10.520 --> 00:01:11.990 align:start position:0%
window thus basically simplifying a lot
of<00:01:10.640><c> the</c><00:01:10.759><c> operations</c><00:01:11.320><c> that</c><00:01:11.439><c> you</c><00:01:11.560><c> would</c><00:01:11.720><c> have</c><00:01:11.840><c> to</c>

00:01:11.990 --> 00:01:12.000 align:start position:0%
of the operations that you would have to
 

00:01:12.000 --> 00:01:15.070 align:start position:0%
of the operations that you would have to
implement<00:01:12.880><c> yourself</c><00:01:13.880><c> um</c><00:01:14.600><c> so</c><00:01:14.759><c> the</c><00:01:14.880><c> next</c>

00:01:15.070 --> 00:01:15.080 align:start position:0%
implement yourself um so the next
 

00:01:15.080 --> 00:01:17.510 align:start position:0%
implement yourself um so the next
question<00:01:15.320><c> is</c><00:01:15.640><c> when</c><00:01:15.960><c> should</c><00:01:16.119><c> you</c><00:01:16.280><c> be</c><00:01:16.400><c> using</c><00:01:16.759><c> one</c>

00:01:17.510 --> 00:01:17.520 align:start position:0%
question is when should you be using one
 

00:01:17.520 --> 00:01:19.310 align:start position:0%
question is when should you be using one
well<00:01:18.040><c> the</c><00:01:18.159><c> first</c><00:01:18.439><c> answer</c><00:01:18.680><c> is</c><00:01:18.799><c> when</c><00:01:18.920><c> your</c><00:01:19.080><c> data</c>

00:01:19.310 --> 00:01:19.320 align:start position:0%
well the first answer is when your data
 

00:01:19.320 --> 00:01:21.149 align:start position:0%
well the first answer is when your data
gets<00:01:19.520><c> sufficiently</c><00:01:19.960><c> large</c><00:01:20.720><c> Computing</c>

00:01:21.149 --> 00:01:21.159 align:start position:0%
gets sufficiently large Computing
 

00:01:21.159 --> 00:01:23.270 align:start position:0%
gets sufficiently large Computing
distances<00:01:21.799><c> to</c><00:01:22.000><c> each</c><00:01:22.240><c> embedding</c><00:01:22.799><c> for</c><00:01:23.040><c> each</c>

00:01:23.270 --> 00:01:23.280 align:start position:0%
distances to each embedding for each
 

00:01:23.280 --> 00:01:25.270 align:start position:0%
distances to each embedding for each
query<00:01:23.720><c> is</c><00:01:24.000><c> is</c><00:01:24.119><c> pretty</c><00:01:24.320><c> slow</c><00:01:24.560><c> and</c><00:01:24.759><c> expensive</c>

00:01:25.270 --> 00:01:25.280 align:start position:0%
query is is pretty slow and expensive
 

00:01:25.280 --> 00:01:27.109 align:start position:0%
query is is pretty slow and expensive
especially<00:01:26.079><c> u</c><00:01:26.200><c> under</c><00:01:26.439><c> certain</c><00:01:26.720><c> distance</c>

00:01:27.109 --> 00:01:27.119 align:start position:0%
especially u under certain distance
 

00:01:27.119 --> 00:01:29.390 align:start position:0%
especially u under certain distance
functions<00:01:27.960><c> A</c><00:01:28.079><c> good</c><00:01:28.240><c> rule</c><00:01:28.439><c> of</c><00:01:28.600><c> thumb</c><00:01:28.960><c> is</c><00:01:29.200><c> that</c>

00:01:29.390 --> 00:01:29.400 align:start position:0%
functions A good rule of thumb is that
 

00:01:29.400 --> 00:01:31.789 align:start position:0%
functions A good rule of thumb is that
when<00:01:29.560><c> you're</c><00:01:29.960><c> are</c><00:01:30.479><c> over</c><00:01:30.799><c> about</c><00:01:31.079><c> 10,000</c>

00:01:31.789 --> 00:01:31.799 align:start position:0%
when you're are over about 10,000
 

00:01:31.799 --> 00:01:33.789 align:start position:0%
when you're are over about 10,000
embeddings<00:01:32.399><c> using</c><00:01:33.000><c> some</c><00:01:33.159><c> of</c><00:01:33.280><c> the</c><00:01:33.399><c> commercial</c>

00:01:33.789 --> 00:01:33.799 align:start position:0%
embeddings using some of the commercial
 

00:01:33.799 --> 00:01:35.590 align:start position:0%
embeddings using some of the commercial
or<00:01:33.920><c> open</c><00:01:34.119><c> source</c><00:01:34.360><c> embedding</c><00:01:34.759><c> models</c><00:01:35.399><c> it's</c>

00:01:35.590 --> 00:01:35.600 align:start position:0%
or open source embedding models it's
 

00:01:35.600 --> 00:01:36.990 align:start position:0%
or open source embedding models it's
probably<00:01:35.920><c> a</c><00:01:36.040><c> good</c><00:01:36.240><c> time</c><00:01:36.399><c> to</c><00:01:36.520><c> switch</c><00:01:36.759><c> to</c><00:01:36.880><c> a</c>

00:01:36.990 --> 00:01:37.000 align:start position:0%
probably a good time to switch to a
 

00:01:37.000 --> 00:01:38.710 align:start position:0%
probably a good time to switch to a
vector<00:01:37.320><c> database</c><00:01:37.759><c> or</c><00:01:38.000><c> embedding</c><00:01:38.320><c> store</c><00:01:38.600><c> and</c>

00:01:38.710 --> 00:01:38.720 align:start position:0%
vector database or embedding store and
 

00:01:38.720 --> 00:01:41.270 align:start position:0%
vector database or embedding store and
we'll<00:01:38.880><c> get</c><00:01:39.000><c> to</c><00:01:39.600><c> a</c><00:01:39.720><c> little</c><00:01:39.920><c> bit</c><00:01:40.200><c> about</c><00:01:40.640><c> why</c><00:01:41.119><c> that</c>

00:01:41.270 --> 00:01:41.280 align:start position:0%
we'll get to a little bit about why that
 

00:01:41.280 --> 00:01:43.670 align:start position:0%
we'll get to a little bit about why that
is<00:01:41.439><c> in</c><00:01:41.560><c> a</c><00:01:41.880><c> minute</c><00:01:42.880><c> the</c><00:01:43.000><c> other</c><00:01:43.200><c> important</c><00:01:43.520><c> thing</c>

00:01:43.670 --> 00:01:43.680 align:start position:0%
is in a minute the other important thing
 

00:01:43.680 --> 00:01:45.630 align:start position:0%
is in a minute the other important thing
is<00:01:43.799><c> not</c><00:01:43.960><c> to</c><00:01:44.280><c> underestimate</c><00:01:44.960><c> the</c><00:01:45.079><c> convenience</c>

00:01:45.630 --> 00:01:45.640 align:start position:0%
is not to underestimate the convenience
 

00:01:45.640 --> 00:01:47.069 align:start position:0%
is not to underestimate the convenience
that<00:01:45.880><c> actually</c><00:01:46.079><c> using</c><00:01:46.360><c> an</c><00:01:46.479><c> off-the-shelf</c>

00:01:47.069 --> 00:01:47.079 align:start position:0%
that actually using an off-the-shelf
 

00:01:47.079 --> 00:01:48.990 align:start position:0%
that actually using an off-the-shelf
package<00:01:47.360><c> gives</c><00:01:47.560><c> you</c><00:01:47.759><c> here</c><00:01:48.360><c> llm</c><00:01:48.840><c> powerered</c>

00:01:48.990 --> 00:01:49.000 align:start position:0%
package gives you here llm powerered
 

00:01:49.000 --> 00:01:51.190 align:start position:0%
package gives you here llm powerered
applications<00:01:49.840><c> need</c><00:01:50.040><c> to</c><00:01:50.200><c> support</c><00:01:50.640><c> many</c><00:01:50.799><c> users</c>

00:01:51.190 --> 00:01:51.200 align:start position:0%
applications need to support many users
 

00:01:51.200 --> 00:01:53.030 align:start position:0%
applications need to support many users
across<00:01:51.479><c> many</c><00:01:51.680><c> indices</c><00:01:52.600><c> um</c><00:01:52.680><c> you</c><00:01:52.799><c> need</c><00:01:52.920><c> to</c>

00:01:53.030 --> 00:01:53.040 align:start position:0%
across many indices um you need to
 

00:01:53.040 --> 00:01:55.389 align:start position:0%
across many indices um you need to
handle<00:01:53.439><c> data</c><00:01:53.759><c> and</c><00:01:53.960><c> scaling</c><00:01:54.399><c> automatically</c>

00:01:55.389 --> 00:01:55.399 align:start position:0%
handle data and scaling automatically
 

00:01:55.399 --> 00:01:57.149 align:start position:0%
handle data and scaling automatically
and<00:01:55.719><c> basically</c><00:01:56.079><c> you</c><00:01:56.159><c> want</c><00:01:56.320><c> it</c><00:01:56.399><c> to</c><00:01:56.640><c> just</c><00:01:56.920><c> work</c>

00:01:57.149 --> 00:01:57.159 align:start position:0%
and basically you want it to just work
 

00:01:57.159 --> 00:01:58.469 align:start position:0%
and basically you want it to just work
so<00:01:57.399><c> rather</c><00:01:57.600><c> than</c><00:01:57.799><c> getting</c><00:01:58.000><c> into</c><00:01:58.240><c> all</c><00:01:58.360><c> the</c>

00:01:58.469 --> 00:01:58.479 align:start position:0%
so rather than getting into all the
 

00:01:58.479 --> 00:01:59.950 align:start position:0%
so rather than getting into all the
nitty-gritty<00:01:59.000><c> implementation</c><00:01:59.560><c> as</c><00:01:59.680><c> long</c><00:01:59.840><c> long</c>

00:01:59.950 --> 00:01:59.960 align:start position:0%
nitty-gritty implementation as long long
 

00:01:59.960 --> 00:02:01.870 align:start position:0%
nitty-gritty implementation as long long
as<00:02:00.039><c> you</c><00:02:00.399><c> understand</c><00:02:00.600><c> what's</c><00:02:00.840><c> going</c><00:02:01.039><c> on</c><00:02:01.600><c> often</c>

00:02:01.870 --> 00:02:01.880 align:start position:0%
as you understand what's going on often
 

00:02:01.880 --> 00:02:03.149 align:start position:0%
as you understand what's going on often
times<00:02:02.079><c> it's</c><00:02:02.240><c> easier</c><00:02:02.560><c> to</c><00:02:02.719><c> just</c><00:02:02.840><c> use</c><00:02:03.000><c> an</c>

00:02:03.149 --> 00:02:03.159 align:start position:0%
times it's easier to just use an
 

00:02:03.159 --> 00:02:05.429 align:start position:0%
times it's easier to just use an
off-the-shelf<00:02:03.719><c> solution</c><00:02:04.079><c> like</c>

00:02:05.429 --> 00:02:05.439 align:start position:0%
off-the-shelf solution like
 

00:02:05.439 --> 00:02:09.790 align:start position:0%
off-the-shelf solution like
chroma<00:02:06.439><c> so</c><00:02:06.920><c> how</c><00:02:07.520><c> exactly</c><00:02:08.399><c> do</c><00:02:09.200><c> embedding</c><00:02:09.520><c> stars</c>

00:02:09.790 --> 00:02:09.800 align:start position:0%
chroma so how exactly do embedding stars
 

00:02:09.800 --> 00:02:11.630 align:start position:0%
chroma so how exactly do embedding stars
in<00:02:09.920><c> Vector</c><00:02:10.239><c> databases</c><00:02:10.959><c> deal</c><00:02:11.200><c> with</c><00:02:11.400><c> large</c>

00:02:11.630 --> 00:02:11.640 align:start position:0%
in Vector databases deal with large
 

00:02:11.640 --> 00:02:13.390 align:start position:0%
in Vector databases deal with large
amounts<00:02:11.920><c> of</c><00:02:12.080><c> data</c><00:02:12.440><c> without</c><00:02:12.680><c> incurring</c><00:02:13.200><c> the</c>

00:02:13.390 --> 00:02:13.400 align:start position:0%
amounts of data without incurring the
 

00:02:13.400 --> 00:02:15.830 align:start position:0%
amounts of data without incurring the
costs<00:02:13.800><c> of</c><00:02:14.360><c> computing</c><00:02:14.879><c> distances</c><00:02:15.400><c> between</c><00:02:15.680><c> the</c>

00:02:15.830 --> 00:02:15.840 align:start position:0%
costs of computing distances between the
 

00:02:15.840 --> 00:02:17.790 align:start position:0%
costs of computing distances between the
query<00:02:16.160><c> and</c><00:02:16.360><c> every</c><00:02:16.560><c> single</c><00:02:16.879><c> embedding</c><00:02:17.280><c> that</c><00:02:17.519><c> it</c>

00:02:17.790 --> 00:02:17.800 align:start position:0%
query and every single embedding that it
 

00:02:17.800 --> 00:02:20.630 align:start position:0%
query and every single embedding that it
stores<00:02:18.800><c> well</c><00:02:19.519><c> as</c><00:02:19.640><c> we've</c><00:02:19.800><c> seen</c><00:02:20.120><c> exact's</c>

00:02:20.630 --> 00:02:20.640 align:start position:0%
stores well as we've seen exact's
 

00:02:20.640 --> 00:02:22.630 align:start position:0%
stores well as we've seen exact's
neighbor<00:02:21.000><c> basically</c><00:02:21.360><c> requires</c><00:02:21.720><c> us</c><00:02:21.879><c> to</c><00:02:22.040><c> scan</c>

00:02:22.630 --> 00:02:22.640 align:start position:0%
neighbor basically requires us to scan
 

00:02:22.640 --> 00:02:24.110 align:start position:0%
neighbor basically requires us to scan
over<00:02:22.879><c> the</c><00:02:23.040><c> entire</c><00:02:23.360><c> list</c><00:02:23.519><c> of</c><00:02:23.640><c> embeddings</c><00:02:24.000><c> and</c>

00:02:24.110 --> 00:02:24.120 align:start position:0%
over the entire list of embeddings and
 

00:02:24.120 --> 00:02:26.350 align:start position:0%
over the entire list of embeddings and
compute<00:02:24.360><c> the</c><00:02:24.480><c> distances</c><00:02:24.840><c> to</c><00:02:25.040><c> each</c><00:02:25.200><c> one</c><00:02:26.120><c> um</c>

00:02:26.350 --> 00:02:26.360 align:start position:0%
compute the distances to each one um
 

00:02:26.360 --> 00:02:28.710 align:start position:0%
compute the distances to each one um
that<00:02:26.519><c> takes</c><00:02:26.760><c> o</c><00:02:26.959><c> n</c><00:02:27.400><c> operations</c><00:02:28.400><c> commonly</c>

00:02:28.710 --> 00:02:28.720 align:start position:0%
that takes o n operations commonly
 

00:02:28.720 --> 00:02:31.190 align:start position:0%
that takes o n operations commonly
called<00:02:28.879><c> linear</c><00:02:29.239><c> time</c><00:02:29.480><c> where</c><00:02:29.840><c> n</c><00:02:30.319><c> is</c><00:02:30.800><c> the</c><00:02:30.959><c> number</c>

00:02:31.190 --> 00:02:31.200 align:start position:0%
called linear time where n is the number
 

00:02:31.200 --> 00:02:33.630 align:start position:0%
called linear time where n is the number
of<00:02:31.319><c> embeddings</c><00:02:31.680><c> you</c><00:02:31.800><c> already</c><00:02:32.239><c> have</c><00:02:33.239><c> embedding</c>

00:02:33.630 --> 00:02:33.640 align:start position:0%
of embeddings you already have embedding
 

00:02:33.640 --> 00:02:35.030 align:start position:0%
of embeddings you already have embedding
stores<00:02:33.959><c> typically</c><00:02:34.239><c> use</c><00:02:34.400><c> an</c><00:02:34.599><c> approximate</c>

00:02:35.030 --> 00:02:35.040 align:start position:0%
stores typically use an approximate
 

00:02:35.040 --> 00:02:36.990 align:start position:0%
stores typically use an approximate
nearest<00:02:35.319><c> neighbor</c><00:02:35.599><c> algorithm</c><00:02:36.440><c> and</c><00:02:36.640><c> basically</c>

00:02:36.990 --> 00:02:37.000 align:start position:0%
nearest neighbor algorithm and basically
 

00:02:37.000 --> 00:02:38.350 align:start position:0%
nearest neighbor algorithm and basically
what<00:02:37.120><c> they</c><00:02:37.280><c> do</c><00:02:37.440><c> is</c><00:02:37.560><c> they</c><00:02:37.800><c> exploit</c><00:02:38.239><c> the</c>

00:02:38.350 --> 00:02:38.360 align:start position:0%
what they do is they exploit the
 

00:02:38.360 --> 00:02:40.990 align:start position:0%
what they do is they exploit the
underlying<00:02:38.920><c> structure</c><00:02:39.360><c> of</c><00:02:39.480><c> the</c><00:02:39.680><c> data</c><00:02:40.560><c> um</c><00:02:40.879><c> that</c>

00:02:40.990 --> 00:02:41.000 align:start position:0%
underlying structure of the data um that
 

00:02:41.000 --> 00:02:43.949 align:start position:0%
underlying structure of the data um that
you<00:02:41.159><c> have</c><00:02:41.400><c> stored</c><00:02:42.159><c> and</c><00:02:42.280><c> they</c><00:02:42.400><c> take</c><00:02:42.599><c> Only</c><00:02:42.879><c> log</c><00:02:43.159><c> n</c>

00:02:43.949 --> 00:02:43.959 align:start position:0%
you have stored and they take Only log n
 

00:02:43.959 --> 00:02:45.309 align:start position:0%
you have stored and they take Only log n
operations<00:02:44.599><c> in</c><00:02:44.720><c> other</c><00:02:44.840><c> words</c><00:02:45.080><c> they're</c>

00:02:45.309 --> 00:02:45.319 align:start position:0%
operations in other words they're
 

00:02:45.319 --> 00:02:48.030 align:start position:0%
operations in other words they're
sublinear<00:02:46.239><c> they</c><00:02:46.720><c> um</c><00:02:47.200><c> it's</c><00:02:47.360><c> a</c><00:02:47.519><c> lot</c><00:02:47.760><c> less</c>

00:02:48.030 --> 00:02:48.040 align:start position:0%
sublinear they um it's a lot less
 

00:02:48.040 --> 00:02:50.670 align:start position:0%
sublinear they um it's a lot less
expensive<00:02:48.480><c> to</c><00:02:48.599><c> do</c><00:02:48.720><c> an</c><00:02:48.879><c> OLN</c><00:02:49.879><c> lookup</c><00:02:50.280><c> than</c><00:02:50.400><c> it</c><00:02:50.519><c> is</c>

00:02:50.670 --> 00:02:50.680 align:start position:0%
expensive to do an OLN lookup than it is
 

00:02:50.680 --> 00:02:53.190 align:start position:0%
expensive to do an OLN lookup than it is
to<00:02:51.000><c> scan</c><00:02:51.360><c> the</c><00:02:51.519><c> entire</c><00:02:51.920><c> list</c><00:02:52.720><c> and</c><00:02:52.840><c> the</c><00:02:52.959><c> way</c><00:02:53.080><c> that</c>

00:02:53.190 --> 00:02:53.200 align:start position:0%
to scan the entire list and the way that
 

00:02:53.200 --> 00:02:54.350 align:start position:0%
to scan the entire list and the way that
they<00:02:53.319><c> do</c><00:02:53.480><c> that</c><00:02:53.640><c> is</c><00:02:53.800><c> because</c><00:02:53.959><c> they</c><00:02:54.080><c> trade</c>

00:02:54.350 --> 00:02:54.360 align:start position:0%
they do that is because they trade
 

00:02:54.360 --> 00:02:56.470 align:start position:0%
they do that is because they trade
recoil<00:02:55.159><c> in</c><00:02:55.319><c> other</c><00:02:55.480><c> words</c><00:02:56.040><c> sometimes</c><00:02:56.319><c> they</c>

00:02:56.470 --> 00:02:56.480 align:start position:0%
recoil in other words sometimes they
 

00:02:56.480 --> 00:02:59.350 align:start position:0%
recoil in other words sometimes they
might<00:02:56.840><c> miss</c><00:02:57.840><c> um</c><00:02:58.120><c> the</c><00:02:58.280><c> truly</c><00:02:58.640><c> nth</c><00:02:59.000><c> nearest</c>

00:02:59.350 --> 00:02:59.360 align:start position:0%
might miss um the truly nth nearest
 

00:02:59.360 --> 00:03:01.790 align:start position:0%
might miss um the truly nth nearest
neighbor<00:02:59.920><c> and</c><00:03:00.120><c> and</c><00:03:00.280><c> grab</c><00:03:00.599><c> something</c><00:03:00.920><c> else</c><00:03:01.640><c> in</c>

00:03:01.790 --> 00:03:01.800 align:start position:0%
neighbor and and grab something else in
 

00:03:01.800 --> 00:03:03.910 align:start position:0%
neighbor and and grab something else in
exchange<00:03:02.200><c> for</c><00:03:02.400><c> Speed</c><00:03:02.680><c> and</c><00:03:02.840><c> computational</c>

00:03:03.910 --> 00:03:03.920 align:start position:0%
exchange for Speed and computational
 

00:03:03.920 --> 00:03:06.110 align:start position:0%
exchange for Speed and computational
complexity<00:03:04.920><c> now</c><00:03:05.400><c> that</c><00:03:05.519><c> might</c><00:03:05.640><c> sound</c><00:03:05.840><c> a</c><00:03:05.920><c> little</c>

00:03:06.110 --> 00:03:06.120 align:start position:0%
complexity now that might sound a little
 

00:03:06.120 --> 00:03:07.509 align:start position:0%
complexity now that might sound a little
worrying<00:03:06.760><c> but</c><00:03:06.879><c> the</c><00:03:07.000><c> reality</c><00:03:07.280><c> is</c><00:03:07.360><c> that</c><00:03:07.480><c> the</c>

00:03:07.509 --> 00:03:07.519 align:start position:0%
worrying but the reality is that the
 

00:03:07.519 --> 00:03:08.830 align:start position:0%
worrying but the reality is that the
trade<00:03:07.760><c> off</c><00:03:07.920><c> between</c><00:03:08.120><c> speed</c><00:03:08.319><c> and</c><00:03:08.400><c> recall</c><00:03:08.760><c> can</c>

00:03:08.830 --> 00:03:08.840 align:start position:0%
trade off between speed and recall can
 

00:03:08.840 --> 00:03:10.670 align:start position:0%
trade off between speed and recall can
be<00:03:08.959><c> tuned</c><00:03:09.400><c> depending</c><00:03:09.680><c> on</c><00:03:09.760><c> the</c><00:03:09.920><c> algorithm</c><00:03:10.400><c> used</c>

00:03:10.670 --> 00:03:10.680 align:start position:0%
be tuned depending on the algorithm used
 

00:03:10.680 --> 00:03:12.190 align:start position:0%
be tuned depending on the algorithm used
and<00:03:10.799><c> there's</c><00:03:10.959><c> a</c><00:03:11.040><c> lot</c><00:03:11.159><c> of</c><00:03:11.319><c> flexibility</c><00:03:11.920><c> in</c><00:03:12.040><c> how</c>

00:03:12.190 --> 00:03:12.200 align:start position:0%
and there's a lot of flexibility in how
 

00:03:12.200 --> 00:03:13.789 align:start position:0%
and there's a lot of flexibility in how
these<00:03:12.360><c> things</c><00:03:12.519><c> are</c>

00:03:13.789 --> 00:03:13.799 align:start position:0%
these things are
 

00:03:13.799 --> 00:03:15.750 align:start position:0%
these things are
implemented<00:03:14.799><c> so</c><00:03:15.000><c> going</c><00:03:15.200><c> into</c><00:03:15.400><c> a</c><00:03:15.480><c> little</c><00:03:15.599><c> bit</c>

00:03:15.750 --> 00:03:15.760 align:start position:0%
implemented so going into a little bit
 

00:03:15.760 --> 00:03:17.470 align:start position:0%
implemented so going into a little bit
more<00:03:15.959><c> depth</c><00:03:16.239><c> about</c><00:03:16.440><c> approximate</c><00:03:16.840><c> nearest</c>

00:03:17.470 --> 00:03:17.480 align:start position:0%
more depth about approximate nearest
 

00:03:17.480 --> 00:03:20.190 align:start position:0%
more depth about approximate nearest
neighbors<00:03:18.480><c> a&amp;n</c><00:03:18.920><c> algorithms</c><00:03:19.360><c> are</c><00:03:19.480><c> an</c><00:03:19.680><c> active</c>

00:03:20.190 --> 00:03:20.200 align:start position:0%
neighbors a&amp;n algorithms are an active
 

00:03:20.200 --> 00:03:21.630 align:start position:0%
neighbors a&amp;n algorithms are an active
area<00:03:20.480><c> of</c><00:03:20.599><c> research</c><00:03:21.040><c> and</c><00:03:21.159><c> there's</c><00:03:21.319><c> a</c><00:03:21.400><c> lot</c><00:03:21.519><c> of</c>

00:03:21.630 --> 00:03:21.640 align:start position:0%
area of research and there's a lot of
 

00:03:21.640 --> 00:03:24.030 align:start position:0%
area of research and there's a lot of
different<00:03:21.879><c> ones</c><00:03:22.720><c> um</c><00:03:23.159><c> commonly</c><00:03:23.440><c> used</c><00:03:23.720><c> ones</c>

00:03:24.030 --> 00:03:24.040 align:start position:0%
different ones um commonly used ones
 

00:03:24.040 --> 00:03:25.509 align:start position:0%
different ones um commonly used ones
include<00:03:24.400><c> things</c><00:03:24.640><c> called</c><00:03:24.879><c> inverted</c><00:03:25.280><c> file</c>

00:03:25.509 --> 00:03:25.519 align:start position:0%
include things called inverted file
 

00:03:25.519 --> 00:03:28.110 align:start position:0%
include things called inverted file
indexes<00:03:26.280><c> locality</c><00:03:26.720><c> sensitive</c><00:03:27.120><c> hashing</c><00:03:27.760><c> and</c>

00:03:28.110 --> 00:03:28.120 align:start position:0%
indexes locality sensitive hashing and
 

00:03:28.120 --> 00:03:29.869 align:start position:0%
indexes locality sensitive hashing and
hierarchical<00:03:28.760><c> navigable</c><00:03:29.159><c> small</c><00:03:29.439><c> world</c>

00:03:29.869 --> 00:03:29.879 align:start position:0%
hierarchical navigable small world
 

00:03:29.879 --> 00:03:31.470 align:start position:0%
hierarchical navigable small world
graphs<00:03:30.280><c> and</c><00:03:30.680><c> if</c><00:03:30.760><c> you</c><00:03:30.840><c> want</c><00:03:30.959><c> to</c><00:03:31.080><c> learn</c><00:03:31.280><c> more</c>

00:03:31.470 --> 00:03:31.480 align:start position:0%
graphs and if you want to learn more
 

00:03:31.480 --> 00:03:32.869 align:start position:0%
graphs and if you want to learn more
details<00:03:31.920><c> about</c><00:03:32.159><c> each</c><00:03:32.280><c> of</c><00:03:32.480><c> these</c><00:03:32.640><c> there's</c>

00:03:32.869 --> 00:03:32.879 align:start position:0%
details about each of these there's
 

00:03:32.879 --> 00:03:35.589 align:start position:0%
details about each of these there's
plenty<00:03:33.120><c> of</c><00:03:33.239><c> detail</c><00:03:33.680><c> available</c><00:03:34.599><c> online</c>

00:03:35.589 --> 00:03:35.599 align:start position:0%
plenty of detail available online
 

00:03:35.599 --> 00:03:36.869 align:start position:0%
plenty of detail available online
different<00:03:35.879><c> algorithms</c><00:03:36.360><c> work</c><00:03:36.599><c> best</c><00:03:36.760><c> in</c>

00:03:36.869 --> 00:03:36.879 align:start position:0%
different algorithms work best in
 

00:03:36.879 --> 00:03:39.789 align:start position:0%
different algorithms work best in
different<00:03:37.120><c> settings</c><00:03:38.120><c> um</c><00:03:38.360><c> a</c><00:03:38.599><c> lot</c><00:03:39.239><c> of</c><00:03:39.640><c> the</c>

00:03:39.789 --> 00:03:39.799 align:start position:0%
different settings um a lot of the
 

00:03:39.799 --> 00:03:41.390 align:start position:0%
different settings um a lot of the
approximate<00:03:40.239><c> nearest</c><00:03:40.519><c> neighbor</c><00:03:40.840><c> algorithms</c>

00:03:41.390 --> 00:03:41.400 align:start position:0%
approximate nearest neighbor algorithms
 

00:03:41.400 --> 00:03:44.429 align:start position:0%
approximate nearest neighbor algorithms
used<00:03:41.879><c> in</c><00:03:42.519><c> many</c><00:03:42.959><c> solutions</c><00:03:43.439><c> today</c><00:03:43.840><c> are</c><00:03:44.040><c> set</c><00:03:44.239><c> up</c>

00:03:44.429 --> 00:03:44.439 align:start position:0%
used in many solutions today are set up
 

00:03:44.439 --> 00:03:46.030 align:start position:0%
used in many solutions today are set up
for<00:03:44.720><c> the</c><00:03:44.879><c> case</c><00:03:45.159><c> where</c><00:03:45.360><c> the</c><00:03:45.439><c> index</c><00:03:45.799><c> doesn't</c>

00:03:46.030 --> 00:03:46.040 align:start position:0%
for the case where the index doesn't
 

00:03:46.040 --> 00:03:48.509 align:start position:0%
for the case where the index doesn't
change<00:03:46.360><c> very</c><00:03:46.519><c> much</c><00:03:46.680><c> and</c><00:03:46.879><c> gets</c><00:03:47.040><c> updated</c><00:03:47.480><c> only</c>

00:03:48.509 --> 00:03:48.519 align:start position:0%
change very much and gets updated only
 

00:03:48.519 --> 00:03:50.509 align:start position:0%
change very much and gets updated only
infrequently<00:03:49.519><c> however</c><00:03:49.959><c> if</c><00:03:50.040><c> we're</c><00:03:50.239><c> building</c>

00:03:50.509 --> 00:03:50.519 align:start position:0%
infrequently however if we're building
 

00:03:50.519 --> 00:03:52.390 align:start position:0%
infrequently however if we're building
an<00:03:50.640><c> llm</c><00:03:51.040><c> in</c><00:03:51.159><c> the</c><00:03:51.239><c> loop</c><00:03:51.519><c> application</c><00:03:52.000><c> it's</c><00:03:52.200><c> very</c>

00:03:52.390 --> 00:03:52.400 align:start position:0%
an llm in the loop application it's very
 

00:03:52.400 --> 00:03:54.270 align:start position:0%
an llm in the loop application it's very
likely<00:03:52.720><c> that</c><00:03:52.840><c> our</c><00:03:53.000><c> data</c><00:03:53.200><c> will</c><00:03:53.360><c> mutate</c><00:03:53.799><c> online</c>

00:03:54.270 --> 00:03:54.280 align:start position:0%
likely that our data will mutate online
 

00:03:54.280 --> 00:03:56.350 align:start position:0%
likely that our data will mutate online
quite<00:03:54.439><c> frequently</c><00:03:54.879><c> and</c><00:03:55.000><c> so</c><00:03:55.799><c> a</c><00:03:56.040><c> graph-based</c>

00:03:56.350 --> 00:03:56.360 align:start position:0%
quite frequently and so a graph-based
 

00:03:56.360 --> 00:03:59.110 align:start position:0%
quite frequently and so a graph-based
algorithm<00:03:57.000><c> like</c><00:03:57.360><c> hnsw</c><00:03:58.360><c> works</c><00:03:58.680><c> well</c><00:03:58.840><c> in</c><00:03:59.000><c> these</c>

00:03:59.110 --> 00:03:59.120 align:start position:0%
algorithm like hnsw works well in these
 

00:03:59.120 --> 00:04:01.270 align:start position:0%
algorithm like hnsw works well in these
types<00:03:59.319><c> ofli</c><00:04:00.159><c> because</c><00:04:00.360><c> we</c><00:04:00.480><c> can</c><00:04:00.640><c> iteratively</c>

00:04:01.270 --> 00:04:01.280 align:start position:0%
types ofli because we can iteratively
 

00:04:01.280 --> 00:04:03.670 align:start position:0%
types ofli because we can iteratively
construct<00:04:01.720><c> and</c><00:04:01.879><c> iterate</c><00:04:02.400><c> on</c><00:04:02.560><c> the</c>

00:04:03.670 --> 00:04:03.680 align:start position:0%
construct and iterate on the
 

00:04:03.680 --> 00:04:06.550 align:start position:0%
construct and iterate on the
graph<00:04:04.680><c> so</c><00:04:05.360><c> what's</c><00:04:05.680><c> next</c><00:04:05.959><c> I</c><00:04:06.040><c> mean</c><00:04:06.200><c> this</c><00:04:06.319><c> all</c>

00:04:06.550 --> 00:04:06.560 align:start position:0%
graph so what's next I mean this all
 

00:04:06.560 --> 00:04:08.149 align:start position:0%
graph so what's next I mean this all
sounds<00:04:06.920><c> great</c><00:04:07.200><c> but</c><00:04:07.319><c> the</c><00:04:07.439><c> issue</c><00:04:07.799><c> is</c><00:04:07.920><c> that</c><00:04:08.040><c> there</c>

00:04:08.149 --> 00:04:08.159 align:start position:0%
sounds great but the issue is that there
 

00:04:08.159 --> 00:04:10.149 align:start position:0%
sounds great but the issue is that there
is<00:04:08.439><c> quite</c><00:04:08.599><c> a</c><00:04:08.720><c> few</c><00:04:08.920><c> fundamental</c><00:04:09.480><c> limitations</c>

00:04:10.149 --> 00:04:10.159 align:start position:0%
is quite a few fundamental limitations
 

00:04:10.159 --> 00:04:11.710 align:start position:0%
is quite a few fundamental limitations
even<00:04:10.400><c> when</c><00:04:10.519><c> you</c><00:04:10.720><c> have</c><00:04:10.840><c> a</c><00:04:11.040><c> software</c><00:04:11.439><c> package</c>

00:04:11.710 --> 00:04:11.720 align:start position:0%
even when you have a software package
 

00:04:11.720 --> 00:04:13.149 align:start position:0%
even when you have a software package
that's<00:04:11.920><c> supporting</c><00:04:12.280><c> you</c><00:04:12.400><c> in</c><00:04:12.560><c> finding</c><00:04:12.840><c> nearest</c>

00:04:13.149 --> 00:04:13.159 align:start position:0%
that's supporting you in finding nearest
 

00:04:13.159 --> 00:04:14.509 align:start position:0%
that's supporting you in finding nearest
Neighbors<00:04:13.439><c> in</c><00:04:13.560><c> this</c><00:04:13.680><c> way</c><00:04:13.959><c> because</c><00:04:14.120><c> it</c><00:04:14.239><c> doesn't</c>

00:04:14.509 --> 00:04:14.519 align:start position:0%
Neighbors in this way because it doesn't
 

00:04:14.519 --> 00:04:16.390 align:start position:0%
Neighbors in this way because it doesn't
answer<00:04:14.760><c> certain</c><00:04:15.079><c> questions</c><00:04:16.040><c> it</c><00:04:16.160><c> doesn't</c>

00:04:16.390 --> 00:04:16.400 align:start position:0%
answer certain questions it doesn't
 

00:04:16.400 --> 00:04:17.990 align:start position:0%
answer certain questions it doesn't
answer<00:04:16.600><c> for</c><00:04:16.759><c> you</c><00:04:16.880><c> which</c><00:04:17.040><c> embedding</c><00:04:17.440><c> model</c><00:04:17.840><c> is</c>

00:04:17.990 --> 00:04:18.000 align:start position:0%
answer for you which embedding model is
 

00:04:18.000 --> 00:04:20.509 align:start position:0%
answer for you which embedding model is
best<00:04:18.160><c> for</c><00:04:18.359><c> my</c><00:04:18.600><c> task</c><00:04:18.799><c> or</c><00:04:18.959><c> your</c><00:04:19.160><c> data</c><00:04:20.160><c> um</c><00:04:20.400><c> it</c>

00:04:20.509 --> 00:04:20.519 align:start position:0%
best for my task or your data um it
 

00:04:20.519 --> 00:04:21.430 align:start position:0%
best for my task or your data um it
doesn't<00:04:20.720><c> tell</c><00:04:20.880><c> you</c><00:04:21.000><c> how</c><00:04:21.120><c> you</c><00:04:21.199><c> should</c><00:04:21.359><c> be</c>

00:04:21.430 --> 00:04:21.440 align:start position:0%
doesn't tell you how you should be
 

00:04:21.440 --> 00:04:22.870 align:start position:0%
doesn't tell you how you should be
chunking<00:04:21.759><c> up</c><00:04:21.919><c> your</c><00:04:22.040><c> data</c><00:04:22.240><c> to</c><00:04:22.360><c> ensure</c><00:04:22.639><c> good</c>

00:04:22.870 --> 00:04:22.880 align:start position:0%
chunking up your data to ensure good
 

00:04:22.880 --> 00:04:25.430 align:start position:0%
chunking up your data to ensure good
results<00:04:23.880><c> and</c><00:04:24.479><c> just</c><00:04:24.680><c> because</c><00:04:24.840><c> you've</c><00:04:25.080><c> gotten</c>

00:04:25.430 --> 00:04:25.440 align:start position:0%
results and just because you've gotten
 

00:04:25.440 --> 00:04:27.030 align:start position:0%
results and just because you've gotten
the<00:04:25.600><c> n</c><00:04:25.840><c> nearest</c><00:04:26.120><c> neighbors</c><00:04:26.600><c> doesn't</c><00:04:26.880><c> actually</c>

00:04:27.030 --> 00:04:27.040 align:start position:0%
the n nearest neighbors doesn't actually
 

00:04:27.040 --> 00:04:28.189 align:start position:0%
the n nearest neighbors doesn't actually
tell<00:04:27.199><c> you</c><00:04:27.360><c> whether</c><00:04:27.560><c> those</c><00:04:27.759><c> neighbors</c><00:04:28.040><c> are</c>

00:04:28.189 --> 00:04:28.199 align:start position:0%
tell you whether those neighbors are
 

00:04:28.199 --> 00:04:29.670 align:start position:0%
tell you whether those neighbors are
relevant<00:04:28.560><c> or</c><00:04:28.680><c> how</c><00:04:28.840><c> relevant</c><00:04:29.160><c> they</c><00:04:29.280><c> actually</c>

00:04:29.670 --> 00:04:29.680 align:start position:0%
relevant or how relevant they actually
 

00:04:29.680 --> 00:04:32.189 align:start position:0%
relevant or how relevant they actually
are<00:04:30.160><c> so</c><00:04:30.320><c> what</c><00:04:30.440><c> should</c><00:04:30.600><c> you</c><00:04:30.800><c> do</c><00:04:31.520><c> um</c><00:04:31.919><c> with</c><00:04:32.039><c> the</c>

00:04:32.189 --> 00:04:32.199 align:start position:0%
are so what should you do um with the
 

00:04:32.199 --> 00:04:34.390 align:start position:0%
are so what should you do um with the
information<00:04:32.600><c> that</c><00:04:32.720><c> they're</c><00:04:32.960><c> not</c><00:04:33.520><c> sometimes</c>

00:04:34.390 --> 00:04:34.400 align:start position:0%
information that they're not sometimes
 

00:04:34.400 --> 00:04:35.430 align:start position:0%
information that they're not sometimes
and<00:04:34.520><c> finally</c><00:04:34.880><c> I</c><00:04:34.960><c> think</c><00:04:35.080><c> it's</c><00:04:35.240><c> really</c>

00:04:35.430 --> 00:04:35.440 align:start position:0%
and finally I think it's really
 

00:04:35.440 --> 00:04:36.710 align:start position:0%
and finally I think it's really
important<00:04:35.720><c> and</c><00:04:35.840><c> one</c><00:04:35.960><c> of</c><00:04:36.039><c> the</c><00:04:36.160><c> core</c><00:04:36.360><c> principles</c>

00:04:36.710 --> 00:04:36.720 align:start position:0%
important and one of the core principles
 

00:04:36.720 --> 00:04:38.629 align:start position:0%
important and one of the core principles
of<00:04:36.880><c> AI</c><00:04:37.199><c> is</c><00:04:37.280><c> that</c><00:04:37.400><c> we</c><00:04:37.520><c> can</c><00:04:37.720><c> easily</c><00:04:38.080><c> incorporate</c>

00:04:38.629 --> 00:04:38.639 align:start position:0%
of AI is that we can easily incorporate
 

00:04:38.639 --> 00:04:41.670 align:start position:0%
of AI is that we can easily incorporate
human<00:04:38.960><c> feedback</c><00:04:39.960><c> into</c><00:04:40.960><c> the</c><00:04:41.160><c> data</c><00:04:41.400><c> that</c><00:04:41.520><c> we're</c>

00:04:41.670 --> 00:04:41.680 align:start position:0%
human feedback into the data that we're
 

00:04:41.680 --> 00:04:43.909 align:start position:0%
human feedback into the data that we're
returning<00:04:42.039><c> to</c><00:04:42.199><c> the</c><00:04:42.320><c> model</c><00:04:43.000><c> so</c><00:04:43.520><c> providing</c>

00:04:43.909 --> 00:04:43.919 align:start position:0%
returning to the model so providing
 

00:04:43.919 --> 00:04:45.830 align:start position:0%
returning to the model so providing
opportunities<00:04:44.520><c> for</c><00:04:44.880><c> and</c><00:04:45.080><c> affordances</c><00:04:45.639><c> to</c>

00:04:45.830 --> 00:04:45.840 align:start position:0%
opportunities for and affordances to
 

00:04:45.840 --> 00:04:47.189 align:start position:0%
opportunities for and affordances to
actually<00:04:46.039><c> do</c><00:04:46.199><c> that</c><00:04:46.320><c> for</c><00:04:46.479><c> developers</c><00:04:47.039><c> is</c>

00:04:47.189 --> 00:04:47.199 align:start position:0%
actually do that for developers is
 

00:04:47.199 --> 00:04:48.990 align:start position:0%
actually do that for developers is
something<00:04:47.479><c> that's</c><00:04:47.680><c> really</c><00:04:47.919><c> important</c><00:04:48.880><c> these</c>

00:04:48.990 --> 00:04:49.000 align:start position:0%
something that's really important these
 

00:04:49.000 --> 00:04:50.270 align:start position:0%
something that's really important these
are<00:04:49.160><c> all</c><00:04:49.320><c> features</c><00:04:49.639><c> that</c><00:04:49.720><c> we're</c><00:04:49.840><c> looking</c><00:04:50.080><c> into</c>

00:04:50.270 --> 00:04:50.280 align:start position:0%
are all features that we're looking into
 

00:04:50.280 --> 00:04:52.110 align:start position:0%
are all features that we're looking into
building<00:04:50.560><c> into</c><00:04:50.800><c> chroma</c><00:04:51.240><c> and</c><00:04:51.400><c> as</c><00:04:51.520><c> I</c><00:04:51.639><c> said</c><00:04:51.960><c> this</c>

00:04:52.110 --> 00:04:52.120 align:start position:0%
building into chroma and as I said this
 

00:04:52.120 --> 00:04:56.670 align:start position:0%
building into chroma and as I said this
is<00:04:52.560><c> a</c><00:04:52.759><c> still</c><00:04:53.440><c> growing</c><00:04:54.400><c> uh</c><00:04:55.400><c> this</c><00:04:55.520><c> is</c><00:04:55.759><c> still</c><00:04:56.520><c> a</c>

00:04:56.670 --> 00:04:56.680 align:start position:0%
is a still growing uh this is still a
 

00:04:56.680 --> 00:04:58.070 align:start position:0%
is a still growing uh this is still a
field<00:04:57.080><c> with</c><00:04:57.199><c> a</c><00:04:57.400><c> lot</c><00:04:57.560><c> of</c><00:04:57.759><c> product</c>

00:04:58.070 --> 00:04:58.080 align:start position:0%
field with a lot of product
 

00:04:58.080 --> 00:04:59.310 align:start position:0%
field with a lot of product
experimentation<00:04:58.759><c> going</c><00:04:58.960><c> on</c><00:04:59.080><c> and</c><00:04:59.199><c> we're</c>

00:04:59.310 --> 00:04:59.320 align:start position:0%
experimentation going on and we're
 

00:04:59.320 --> 00:05:02.350 align:start position:0%
experimentation going on and we're
hoping<00:04:59.720><c> to</c><00:04:59.880><c> support</c><00:05:00.880><c> uh</c><00:05:01.000><c> users</c><00:05:01.440><c> like</c><00:05:01.600><c> you</c><00:05:02.160><c> in</c>

00:05:02.350 --> 00:05:02.360 align:start position:0%
hoping to support uh users like you in
 

00:05:02.360 --> 00:05:03.670 align:start position:0%
hoping to support uh users like you in
doing<00:05:02.639><c> all</c><00:05:02.759><c> of</c><00:05:02.919><c> these</c><00:05:03.080><c> things</c><00:05:03.280><c> in</c><00:05:03.360><c> the</c><00:05:03.479><c> near</c>

00:05:03.670 --> 00:05:03.680 align:start position:0%
doing all of these things in the near
 

00:05:03.680 --> 00:05:14.510 align:start position:0%
doing all of these things in the near
future<00:05:04.639><c> thank</c>

00:05:14.510 --> 00:05:14.520 align:start position:0%
 
 

00:05:14.520 --> 00:05:17.520 align:start position:0%
 
you

