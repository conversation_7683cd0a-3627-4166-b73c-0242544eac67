WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.699 align:start position:0%
 
and<00:00:00.179><c> welcome</c><00:00:00.510><c> to</c><00:00:00.599><c> the</c><00:00:00.690><c> next</c><00:00:00.840><c> video</c><00:00:01.079><c> of</c><00:00:01.350><c> the</c><00:00:01.500><c> get</c>

00:00:01.699 --> 00:00:01.709 align:start position:0%
and welcome to the next video of the get
 

00:00:01.709 --> 00:00:03.980 align:start position:0%
and welcome to the next video of the get
tutorial<00:00:01.949><c> series</c><00:00:02.730><c> and</c><00:00:03.149><c> in</c><00:00:03.240><c> this</c><00:00:03.419><c> video</c><00:00:03.780><c> we're</c>

00:00:03.980 --> 00:00:03.990 align:start position:0%
tutorial series and in this video we're
 

00:00:03.990 --> 00:00:06.079 align:start position:0%
tutorial series and in this video we're
going<00:00:04.140><c> to</c><00:00:04.230><c> do</c><00:00:04.350><c> a</c><00:00:04.380><c> real</c><00:00:04.680><c> use</c><00:00:04.950><c> case</c><00:00:05.279><c> of</c><00:00:05.520><c> fixing</c><00:00:06.000><c> a</c>

00:00:06.079 --> 00:00:06.089 align:start position:0%
going to do a real use case of fixing a
 

00:00:06.089 --> 00:00:11.419 align:start position:0%
going to do a real use case of fixing a
bug<00:00:06.330><c> on</c><00:00:06.600><c> live</c><00:00:07.109><c> production</c><00:00:07.890><c> site</c><00:00:08.160><c> and</c><00:00:10.429><c> updating</c>

00:00:11.419 --> 00:00:11.429 align:start position:0%
bug on live production site and updating
 

00:00:11.429 --> 00:00:14.600 align:start position:0%
bug on live production site and updating
our<00:00:11.639><c> github</c><00:00:11.969><c> repo</c><00:00:12.509><c> and</c><00:00:12.809><c> also</c><00:00:13.460><c> pushing</c><00:00:14.460><c> the</c>

00:00:14.600 --> 00:00:14.610 align:start position:0%
our github repo and also pushing the
 

00:00:14.610 --> 00:00:17.330 align:start position:0%
our github repo and also pushing the
changes<00:00:15.089><c> up</c><00:00:15.690><c> to</c><00:00:15.750><c> the</c><00:00:16.260><c> live</c><00:00:16.440><c> production</c><00:00:17.039><c> site</c>

00:00:17.330 --> 00:00:17.340 align:start position:0%
changes up to the live production site
 

00:00:17.340 --> 00:00:20.870 align:start position:0%
changes up to the live production site
via<00:00:17.699><c> the</c><00:00:17.970><c> Heroku</c><00:00:18.539><c> CLI</c><00:00:19.080><c> as</c><00:00:19.260><c> well</c><00:00:19.500><c> so</c><00:00:19.880><c> we're</c>

00:00:20.870 --> 00:00:20.880 align:start position:0%
via the Heroku CLI as well so we're
 

00:00:20.880 --> 00:00:23.390 align:start position:0%
via the Heroku CLI as well so we're
going<00:00:21.000><c> to</c><00:00:21.119><c> update</c><00:00:21.330><c> both</c><00:00:21.720><c> our</c><00:00:21.869><c> github</c><00:00:22.230><c> repo</c><00:00:23.130><c> and</c>

00:00:23.390 --> 00:00:23.400 align:start position:0%
going to update both our github repo and
 

00:00:23.400 --> 00:00:26.750 align:start position:0%
going to update both our github repo and
our<00:00:23.640><c> hero</c><00:00:23.850><c> fool</c><00:00:24.210><c> repo</c><00:00:24.539><c> and</c><00:00:25.279><c> the</c><00:00:26.279><c> first</c><00:00:26.550><c> thing</c>

00:00:26.750 --> 00:00:26.760 align:start position:0%
our hero fool repo and the first thing
 

00:00:26.760 --> 00:00:28.370 align:start position:0%
our hero fool repo and the first thing
that<00:00:26.789><c> we're</c><00:00:27.029><c> going</c><00:00:27.180><c> to</c><00:00:27.300><c> need</c><00:00:27.390><c> to</c><00:00:27.510><c> do</c><00:00:27.720><c> actually</c>

00:00:28.370 --> 00:00:28.380 align:start position:0%
that we're going to need to do actually
 

00:00:28.380 --> 00:00:31.070 align:start position:0%
that we're going to need to do actually
is<00:00:28.590><c> create</c><00:00:29.310><c> a</c><00:00:29.580><c> new</c><00:00:29.910><c> branch</c><00:00:30.300><c> so</c><00:00:30.630><c> let's</c><00:00:30.869><c> look</c>

00:00:31.070 --> 00:00:31.080 align:start position:0%
is create a new branch so let's look
 

00:00:31.080 --> 00:00:33.500 align:start position:0%
is create a new branch so let's look
first<00:00:31.349><c> at</c><00:00:31.560><c> what</c><00:00:32.430><c> we're</c><00:00:32.610><c> gonna</c><00:00:32.790><c> try</c><00:00:33.090><c> to</c><00:00:33.120><c> fix</c>

00:00:33.500 --> 00:00:33.510 align:start position:0%
first at what we're gonna try to fix
 

00:00:33.510 --> 00:00:38.420 align:start position:0%
first at what we're gonna try to fix
we're<00:00:34.110><c> gonna</c><00:00:34.320><c> go</c><00:00:34.969><c> to</c><00:00:36.620><c> see</c><00:00:37.620><c> I</c><00:00:37.649><c> think</c><00:00:37.739><c> the</c><00:00:37.980><c> stats</c>

00:00:38.420 --> 00:00:38.430 align:start position:0%
we're gonna go to see I think the stats
 

00:00:38.430 --> 00:00:40.250 align:start position:0%
we're gonna go to see I think the stats
page<00:00:38.550><c> looks</c><00:00:39.059><c> pretty</c><00:00:39.300><c> bad</c><00:00:39.570><c> you'll</c><00:00:39.840><c> see</c><00:00:40.020><c> we</c><00:00:40.140><c> have</c>

00:00:40.250 --> 00:00:40.260 align:start position:0%
page looks pretty bad you'll see we have
 

00:00:40.260 --> 00:00:42.229 align:start position:0%
page looks pretty bad you'll see we have
this<00:00:40.469><c> thing</c><00:00:40.710><c> here</c><00:00:41.010><c> this</c><00:00:41.489><c> was</c><00:00:41.730><c> taken</c><00:00:42.030><c> from</c><00:00:42.120><c> a</c>

00:00:42.229 --> 00:00:42.239 align:start position:0%
this thing here this was taken from a
 

00:00:42.239 --> 00:00:45.889 align:start position:0%
this thing here this was taken from a
brad<00:00:42.510><c> traversée</c><00:00:43.290><c> travesty</c><00:00:44.040><c> media</c><00:00:44.899><c> tutorial</c>

00:00:45.889 --> 00:00:45.899 align:start position:0%
brad traversée travesty media tutorial
 

00:00:45.899 --> 00:00:48.080 align:start position:0%
brad traversée travesty media tutorial
series<00:00:46.110><c> I'm</c><00:00:46.440><c> a</c><00:00:46.500><c> big</c><00:00:46.710><c> fan</c><00:00:47.010><c> also</c><00:00:47.489><c> some</c><00:00:47.820><c> of</c><00:00:47.910><c> the</c>

00:00:48.080 --> 00:00:48.090 align:start position:0%
series I'm a big fan also some of the
 

00:00:48.090 --> 00:00:50.150 align:start position:0%
series I'm a big fan also some of the
other<00:00:48.329><c> pieces</c><00:00:48.629><c> of</c><00:00:48.899><c> code</c><00:00:49.110><c> in</c><00:00:49.289><c> this</c><00:00:49.440><c> project</c><00:00:49.680><c> are</c>

00:00:50.150 --> 00:00:50.160 align:start position:0%
other pieces of code in this project are
 

00:00:50.160 --> 00:00:54.560 align:start position:0%
other pieces of code in this project are
also<00:00:50.340><c> from</c><00:00:50.670><c> Traverse</c><00:00:50.969><c> II</c><00:00:52.610><c> so</c><00:00:53.610><c> anyways</c><00:00:54.030><c> what</c><00:00:54.449><c> we</c>

00:00:54.560 --> 00:00:54.570 align:start position:0%
also from Traverse II so anyways what we
 

00:00:54.570 --> 00:00:57.290 align:start position:0%
also from Traverse II so anyways what we
have<00:00:54.750><c> here</c><00:00:55.170><c> is</c><00:00:55.440><c> we</c><00:00:55.829><c> want</c><00:00:55.949><c> to</c><00:00:56.070><c> just</c><00:00:56.250><c> go</c><00:00:57.059><c> ahead</c>

00:00:57.290 --> 00:00:57.300 align:start position:0%
have here is we want to just go ahead
 

00:00:57.300 --> 00:00:59.720 align:start position:0%
have here is we want to just go ahead
and<00:00:57.449><c> delete</c><00:00:58.039><c> what</c><00:00:59.039><c> do</c><00:00:59.100><c> we</c><00:00:59.219><c> want</c><00:00:59.399><c> to</c><00:00:59.489><c> delete</c>

00:00:59.720 --> 00:00:59.730 align:start position:0%
and delete what do we want to delete
 

00:00:59.730 --> 00:01:02.529 align:start position:0%
and delete what do we want to delete
community<00:01:00.719><c> distanc</c>

00:01:02.529 --> 00:01:02.539 align:start position:0%
community distanc
 

00:01:02.539 --> 00:01:05.270 align:start position:0%
community distanc
everything<00:01:03.539><c> from</c><00:01:03.780><c> pages</c><00:01:04.199><c> and</c><00:01:04.710><c> manage</c><00:01:05.010><c> site</c>

00:01:05.270 --> 00:01:05.280 align:start position:0%
everything from pages and manage site
 

00:01:05.280 --> 00:01:08.690 align:start position:0%
everything from pages and manage site
pages<00:01:05.840><c> up</c><00:01:06.840><c> until</c><00:01:07.200><c> admins</c><00:01:07.680><c> strap</c><00:01:08.040><c> okay</c><00:01:08.400><c> and</c>

00:01:08.690 --> 00:01:08.700 align:start position:0%
pages up until admins strap okay and
 

00:01:08.700 --> 00:01:10.969 align:start position:0%
pages up until admins strap okay and
this<00:01:09.030><c> blockbuster</c><00:01:09.360><c> demise</c><00:01:10.110><c> and</c><00:01:10.290><c> Netflix</c><00:01:10.770><c> rise</c>

00:01:10.969 --> 00:01:10.979 align:start position:0%
this blockbuster demise and Netflix rise
 

00:01:10.979 --> 00:01:12.920 align:start position:0%
this blockbuster demise and Netflix rise
it<00:01:11.220><c> doesn't</c><00:01:11.520><c> even</c><00:01:11.610><c> make</c><00:01:11.850><c> sense</c><00:01:11.909><c> right</c><00:01:12.420><c> now</c><00:01:12.450><c> so</c>

00:01:12.920 --> 00:01:12.930 align:start position:0%
it doesn't even make sense right now so
 

00:01:12.930 --> 00:01:15.050 align:start position:0%
it doesn't even make sense right now so
let's<00:01:13.710><c> try</c><00:01:13.920><c> to</c><00:01:13.979><c> figure</c><00:01:14.460><c> out</c><00:01:14.700><c> how</c><00:01:14.820><c> we're</c><00:01:15.000><c> gonna</c>

00:01:15.050 --> 00:01:15.060 align:start position:0%
let's try to figure out how we're gonna
 

00:01:15.060 --> 00:01:17.390 align:start position:0%
let's try to figure out how we're gonna
go<00:01:15.330><c> about</c><00:01:15.420><c> do</c><00:01:15.689><c> that</c><00:01:15.869><c> first</c><00:01:16.049><c> we</c><00:01:16.140><c> need</c><00:01:16.350><c> a</c><00:01:16.619><c> new</c>

00:01:17.390 --> 00:01:17.400 align:start position:0%
go about do that first we need a new
 

00:01:17.400 --> 00:01:24.249 align:start position:0%
go about do that first we need a new
branch<00:01:18.299><c> get</c><00:01:18.840><c> checkout</c><00:01:19.350><c> -</c><00:01:19.860><c> be</c><00:01:20.659><c> ok</c><00:01:21.659><c> I</c><00:01:22.110><c> remove</c>

00:01:24.249 --> 00:01:24.259 align:start position:0%
branch get checkout - be ok I remove
 

00:01:24.259 --> 00:01:26.660 align:start position:0%
branch get checkout - be ok I remove
let's<00:01:25.259><c> see</c><00:01:25.470><c> what's</c><00:01:25.860><c> the</c><00:01:25.950><c> name</c><00:01:26.130><c> of</c><00:01:26.159><c> it</c><00:01:26.400><c> it's</c>

00:01:26.660 --> 00:01:26.670 align:start position:0%
let's see what's the name of it it's
 

00:01:26.670 --> 00:01:29.780 align:start position:0%
let's see what's the name of it it's
called<00:01:26.850><c> the</c><00:01:27.210><c> stats</c><00:01:27.950><c> stats</c><00:01:28.950><c> but</c><00:01:29.280><c> that</c><00:01:29.430><c> doesn't</c>

00:01:29.780 --> 00:01:29.790 align:start position:0%
called the stats stats but that doesn't
 

00:01:29.790 --> 00:01:31.819 align:start position:0%
called the stats stats but that doesn't
even<00:01:29.880><c> make</c><00:01:30.119><c> sense</c><00:01:30.180><c> I'm</c><00:01:31.020><c> just</c><00:01:31.350><c> gonna</c><00:01:31.500><c> call</c><00:01:31.799><c> it</c>

00:01:31.819 --> 00:01:31.829 align:start position:0%
even make sense I'm just gonna call it
 

00:01:31.829 --> 00:01:36.850 align:start position:0%
even make sense I'm just gonna call it
admin<00:01:33.380><c> let's</c><00:01:34.380><c> just</c><00:01:34.590><c> rename</c><00:01:34.799><c> it</c><00:01:35.159><c> admin</c><00:01:35.700><c> ok</c>

00:01:36.850 --> 00:01:36.860 align:start position:0%
admin let's just rename it admin ok
 

00:01:36.860 --> 00:01:40.910 align:start position:0%
admin let's just rename it admin ok
editing<00:01:37.860><c> the</c><00:01:38.009><c> admin</c><00:01:38.700><c> tab</c><00:01:39.200><c> -</c><00:01:40.200><c> so</c><00:01:40.439><c> we'll</c><00:01:40.590><c> call</c><00:01:40.770><c> it</c>

00:01:40.910 --> 00:01:40.920 align:start position:0%
editing the admin tab - so we'll call it
 

00:01:40.920 --> 00:01:47.270 align:start position:0%
editing the admin tab - so we'll call it
that<00:01:41.390><c> editing</c><00:01:43.189><c> admin</c><00:01:44.189><c> tab</c><00:01:45.619><c> if</c><00:01:46.619><c> check</c><00:01:46.860><c> out</c><00:01:47.040><c> -</c>

00:01:47.270 --> 00:01:47.280 align:start position:0%
that editing admin tab if check out -
 

00:01:47.280 --> 00:01:49.700 align:start position:0%
that editing admin tab if check out -
Pete<00:01:47.640><c> ok</c><00:01:48.149><c> and</c><00:01:48.390><c> let's</c><00:01:48.750><c> look</c><00:01:48.930><c> at</c><00:01:49.110><c> do</c><00:01:49.380><c> a</c><00:01:49.409><c> git</c>

00:01:49.700 --> 00:01:49.710 align:start position:0%
Pete ok and let's look at do a git
 

00:01:49.710 --> 00:01:50.899 align:start position:0%
Pete ok and let's look at do a git
branch<00:01:49.920><c> and</c><00:01:50.340><c> that's</c><00:01:50.460><c> gonna</c><00:01:50.579><c> let</c><00:01:50.759><c> us</c><00:01:50.820><c> know</c>

00:01:50.899 --> 00:01:50.909 align:start position:0%
branch and that's gonna let us know
 

00:01:50.909 --> 00:01:53.030 align:start position:0%
branch and that's gonna let us know
which<00:01:51.210><c> branch</c><00:01:51.570><c> we're</c><00:01:51.780><c> on</c><00:01:51.899><c> right</c><00:01:52.110><c> now</c><00:01:52.320><c> oh</c><00:01:52.470><c> no</c>

00:01:53.030 --> 00:01:53.040 align:start position:0%
which branch we're on right now oh no
 

00:01:53.040 --> 00:01:55.069 align:start position:0%
which branch we're on right now oh no
editing<00:01:53.520><c> admin</c><00:01:53.909><c> tab</c><00:01:54.090><c> is</c><00:01:54.210><c> not</c><00:01:54.360><c> a</c><00:01:54.420><c> valid</c><00:01:54.659><c> branch</c>

00:01:55.069 --> 00:01:55.079 align:start position:0%
editing admin tab is not a valid branch
 

00:01:55.079 --> 00:01:56.870 align:start position:0%
editing admin tab is not a valid branch
name<00:01:55.439><c> let's</c><00:01:56.219><c> try</c><00:01:56.430><c> that</c><00:01:56.549><c> again</c>

00:01:56.870 --> 00:01:56.880 align:start position:0%
name let's try that again
 

00:01:56.880 --> 00:01:58.580 align:start position:0%
name let's try that again
first<00:01:57.360><c> let's</c><00:01:57.540><c> look</c><00:01:57.630><c> at</c><00:01:57.780><c> which</c><00:01:57.899><c> branch</c><00:01:58.229><c> were</c><00:01:58.409><c> on</c>

00:01:58.580 --> 00:01:58.590 align:start position:0%
first let's look at which branch were on
 

00:01:58.590 --> 00:02:02.330 align:start position:0%
first let's look at which branch were on
currently<00:01:59.420><c> we</c><00:02:00.420><c> are</c><00:02:00.570><c> on</c><00:02:00.780><c> a</c><00:02:00.840><c> branch</c><00:02:01.350><c> git</c><00:02:02.130><c> branch</c>

00:02:02.330 --> 00:02:02.340 align:start position:0%
currently we are on a branch git branch
 

00:02:02.340 --> 00:02:06.260 align:start position:0%
currently we are on a branch git branch
as<00:02:02.850><c> you</c><00:02:02.969><c> which</c><00:02:03.119><c> branch</c><00:02:03.420><c> you're</c><00:02:03.630><c> on</c><00:02:04.040><c> say</c><00:02:05.270><c> we're</c>

00:02:06.260 --> 00:02:06.270 align:start position:0%
as you which branch you're on say we're
 

00:02:06.270 --> 00:02:08.690 align:start position:0%
as you which branch you're on say we're
on<00:02:06.329><c> the</c><00:02:06.450><c> master</c><00:02:06.869><c> branch</c><00:02:07.140><c> right</c><00:02:07.380><c> now</c><00:02:07.590><c> so</c><00:02:07.829><c> let's</c>

00:02:08.690 --> 00:02:08.700 align:start position:0%
on the master branch right now so let's
 

00:02:08.700 --> 00:02:10.790 align:start position:0%
on the master branch right now so let's
do<00:02:08.910><c> this</c><00:02:09.060><c> get</c><00:02:09.360><c> check</c><00:02:09.629><c> out</c><00:02:09.810><c> -</c><00:02:09.989><c> be</c><00:02:10.229><c> all</c><00:02:10.349><c> you</c><00:02:10.500><c> do</c><00:02:10.679><c> is</c>

00:02:10.790 --> 00:02:10.800 align:start position:0%
do this get check out - be all you do is
 

00:02:10.800 --> 00:02:12.800 align:start position:0%
do this get check out - be all you do is
press<00:02:10.979><c> the</c><00:02:11.160><c> uparrow</c><00:02:11.520><c> together</c>

00:02:12.800 --> 00:02:12.810 align:start position:0%
press the uparrow together
 

00:02:12.810 --> 00:02:17.510 align:start position:0%
press the uparrow together
editing<00:02:13.170><c> admin</c><00:02:13.590><c> tab</c><00:02:15.080><c> let's</c><00:02:16.080><c> try</c><00:02:16.350><c> to</c><00:02:16.410><c> do</c><00:02:16.650><c> it</c><00:02:16.860><c> try</c>

00:02:17.510 --> 00:02:17.520 align:start position:0%
editing admin tab let's try to do it try
 

00:02:17.520 --> 00:02:20.900 align:start position:0%
editing admin tab let's try to do it try
to<00:02:17.580><c> remove</c><00:02:17.850><c> the</c><00:02:18.030><c> spaces</c><00:02:18.330><c> if</c><00:02:18.840><c> that</c><00:02:19.080><c> works</c><00:02:19.910><c> ok</c>

00:02:20.900 --> 00:02:20.910 align:start position:0%
to remove the spaces if that works ok
 

00:02:20.910 --> 00:02:22.610 align:start position:0%
to remove the spaces if that works ok
switch<00:02:21.180><c> to</c><00:02:21.390><c> a</c><00:02:21.420><c> new</c><00:02:21.600><c> branch</c><00:02:21.900><c> called</c><00:02:22.260><c> editing</c>

00:02:22.610 --> 00:02:22.620 align:start position:0%
switch to a new branch called editing
 

00:02:22.620 --> 00:02:26.080 align:start position:0%
switch to a new branch called editing
admin<00:02:23.069><c> tab</c><00:02:23.310><c> type</c><00:02:23.670><c> type</c><00:02:24.450><c> git</c><00:02:24.780><c> branch</c><00:02:24.990><c> again</c><00:02:25.530><c> and</c>

00:02:26.080 --> 00:02:26.090 align:start position:0%
admin tab type type git branch again and
 

00:02:26.090 --> 00:02:29.360 align:start position:0%
admin tab type type git branch again and
this<00:02:27.090><c> time</c><00:02:27.150><c> you</c><00:02:27.420><c> see</c><00:02:27.660><c> the</c><00:02:28.110><c> red</c><00:02:28.350><c> the</c><00:02:28.650><c> green</c><00:02:28.950><c> is</c>

00:02:29.360 --> 00:02:29.370 align:start position:0%
this time you see the red the green is
 

00:02:29.370 --> 00:02:31.100 align:start position:0%
this time you see the red the green is
editing<00:02:29.819><c> admin</c><00:02:30.300><c> tab</c><00:02:30.510><c> so</c><00:02:30.630><c> that's</c><00:02:30.810><c> where</c><00:02:30.959><c> we</c>

00:02:31.100 --> 00:02:31.110 align:start position:0%
editing admin tab so that's where we
 

00:02:31.110 --> 00:02:32.690 align:start position:0%
editing admin tab so that's where we
want<00:02:31.230><c> to</c><00:02:31.380><c> be</c><00:02:31.530><c> right</c><00:02:31.709><c> now</c><00:02:31.950><c> the</c><00:02:32.010><c> next</c><00:02:32.489><c> thing</c><00:02:32.580><c> we</c>

00:02:32.690 --> 00:02:32.700 align:start position:0%
want to be right now the next thing we
 

00:02:32.700 --> 00:02:35.270 align:start position:0%
want to be right now the next thing we
need<00:02:32.760><c> to</c><00:02:32.880><c> do</c><00:02:33.150><c> is</c><00:02:33.750><c> we're</c><00:02:34.020><c> gonna</c><00:02:34.080><c> go</c><00:02:34.410><c> into</c><00:02:35.040><c> our</c>

00:02:35.270 --> 00:02:35.280 align:start position:0%
need to do is we're gonna go into our
 

00:02:35.280 --> 00:02:48.620 align:start position:0%
need to do is we're gonna go into our
repo<00:02:37.280><c> here's</c><00:02:38.280><c> I</c><00:02:38.430><c> rethought</c><00:02:47.030><c> no</c><00:02:48.030><c> problem</c><00:02:48.480><c> yeah</c>

00:02:48.620 --> 00:02:48.630 align:start position:0%
repo here's I rethought no problem yeah
 

00:02:48.630 --> 00:02:50.690 align:start position:0%
repo here's I rethought no problem yeah
yeah<00:02:48.840><c> as</c><00:02:49.620><c> long</c><00:02:49.950><c> as</c><00:02:50.070><c> there's</c><00:02:50.250><c> enough</c><00:02:50.459><c> hot</c><00:02:50.670><c> water</c>

00:02:50.690 --> 00:02:50.700 align:start position:0%
yeah as long as there's enough hot water
 

00:02:50.700 --> 00:02:54.680 align:start position:0%
yeah as long as there's enough hot water
ok<00:02:52.190><c> let's</c><00:02:53.190><c> see</c><00:02:53.400><c> what's</c><00:02:53.640><c> next</c><00:02:53.850><c> here</c><00:02:54.239><c> okay</c><00:02:54.660><c> so</c>

00:02:54.680 --> 00:02:54.690 align:start position:0%
ok let's see what's next here okay so
 

00:02:54.690 --> 00:02:56.120 align:start position:0%
ok let's see what's next here okay so
we're<00:02:54.900><c> gonna</c><00:02:54.989><c> go</c><00:02:55.170><c> into</c><00:02:55.380><c> our</c><00:02:55.410><c> views</c><00:02:55.830><c> we're</c>

00:02:56.120 --> 00:02:56.130 align:start position:0%
we're gonna go into our views we're
 

00:02:56.130 --> 00:03:02.900 align:start position:0%
we're gonna go into our views we're
gonna<00:02:56.250><c> go</c><00:02:56.489><c> into</c><00:02:57.120><c> a</c><00:02:59.510><c> yes/no</c><00:03:00.510><c> public</c><00:03:01.910><c> it's</c>

00:03:02.900 --> 00:03:02.910 align:start position:0%
gonna go into a yes/no public it's
 

00:03:02.910 --> 00:03:07.070 align:start position:0%
gonna go into a yes/no public it's
called<00:03:03.209><c> home</c><00:03:03.540><c> -</c><00:03:03.840><c> lists</c><00:03:04.200><c> and</c><00:03:04.769><c> always</c><00:03:06.080><c> close</c>

00:03:07.070 --> 00:03:07.080 align:start position:0%
called home - lists and always close
 

00:03:07.080 --> 00:03:14.240 align:start position:0%
called home - lists and always close
this<00:03:09.200><c> index</c><00:03:10.200><c> a</c><00:03:10.380><c> DJ</c><00:03:10.709><c> is</c><00:03:12.260><c> like</c><00:03:13.260><c> this</c><00:03:13.470><c> no</c><00:03:13.860><c> this</c><00:03:13.890><c> is</c>

00:03:14.240 --> 00:03:14.250 align:start position:0%
this index a DJ is like this no this is
 

00:03:14.250 --> 00:03:24.770 align:start position:0%
this index a DJ is like this no this is
the<00:03:14.370><c> online</c><00:03:14.730><c> users</c><00:03:16.370><c> card</c><00:03:17.370><c> style</c><00:03:23.780><c> content</c>

00:03:24.770 --> 00:03:24.780 align:start position:0%
the online users card style content
 

00:03:24.780 --> 00:03:28.759 align:start position:0%
the online users card style content
pages<00:03:26.630><c> let's</c><00:03:27.630><c> see</c><00:03:27.810><c> well</c><00:03:28.019><c> we</c><00:03:28.200><c> could</c><00:03:28.350><c> always</c><00:03:28.530><c> do</c>

00:03:28.759 --> 00:03:28.769 align:start position:0%
pages let's see well we could always do
 

00:03:28.769 --> 00:03:33.910 align:start position:0%
pages let's see well we could always do
a<00:03:28.799><c> ctrl</c><00:03:29.340><c> shift</c><00:03:29.370><c> F</c><00:03:30.030><c> and</c><00:03:31.100><c> then</c><00:03:32.100><c> search</c><00:03:32.880><c> for</c>

00:03:33.910 --> 00:03:33.920 align:start position:0%
a ctrl shift F and then search for
 

00:03:33.920 --> 00:03:40.850 align:start position:0%
a ctrl shift F and then search for
blockbuster<00:03:38.720><c> that's</c><00:03:39.720><c> gonna</c><00:03:39.900><c> search</c><00:03:40.200><c> the</c>

00:03:40.850 --> 00:03:40.860 align:start position:0%
blockbuster that's gonna search the
 

00:03:40.860 --> 00:03:45.830 align:start position:0%
blockbuster that's gonna search the
entire<00:03:40.980><c> repo</c><00:03:41.670><c> for</c><00:03:41.880><c> the</c><00:03:41.970><c> word</c><00:03:42.120><c> blockbuster</c>

00:03:45.830 --> 00:03:45.840 align:start position:0%
 
 

00:03:45.840 --> 00:03:48.759 align:start position:0%
 
[Music]

00:03:48.759 --> 00:03:48.769 align:start position:0%
[Music]
 

00:03:48.769 --> 00:03:55.060 align:start position:0%
[Music]
Dada<00:03:49.769><c> sure</c><00:03:51.799><c> where</c><00:03:52.799><c> is</c><00:03:52.950><c> it</c><00:03:53.130><c> is</c><00:03:53.790><c> it</c><00:03:53.850><c> homeless</c><00:03:54.299><c> -</c>

00:03:55.060 --> 00:03:55.070 align:start position:0%
Dada sure where is it is it homeless -
 

00:03:55.070 --> 00:03:58.789 align:start position:0%
Dada sure where is it is it homeless -
here<00:03:56.070><c> it</c><00:03:56.160><c> is</c><00:03:56.310><c> sorry</c><00:03:56.989><c> this</c><00:03:57.989><c> is</c><00:03:58.230><c> where</c><00:03:58.410><c> it's</c><00:03:58.590><c> at</c>

00:03:58.789 --> 00:03:58.799 align:start position:0%
here it is sorry this is where it's at
 

00:03:58.799 --> 00:04:03.050 align:start position:0%
here it is sorry this is where it's at
so<00:03:58.829><c> we</c><00:03:59.700><c> need</c><00:03:59.850><c> that</c><00:04:00.120><c> I'm</c><00:04:00.450><c> gonna</c><00:04:00.660><c> erase</c><00:04:02.060><c> erase</c>

00:04:03.050 --> 00:04:03.060 align:start position:0%
so we need that I'm gonna erase erase
 

00:04:03.060 --> 00:04:08.120 align:start position:0%
so we need that I'm gonna erase erase
this<00:04:04.400><c> ok</c><00:04:05.400><c> toggle</c><00:04:05.880><c> navigation</c><00:04:06.690><c> admin</c><00:04:07.350><c> strap</c><00:04:07.799><c> we</c>

00:04:08.120 --> 00:04:08.130 align:start position:0%
this ok toggle navigation admin strap we
 

00:04:08.130 --> 00:04:10.340 align:start position:0%
this ok toggle navigation admin strap we
don't<00:04:08.310><c> need</c><00:04:08.519><c> it</c><00:04:08.780><c> everything</c><00:04:09.780><c> up</c><00:04:09.900><c> until</c><00:04:10.019><c> the</c>

00:04:10.340 --> 00:04:10.350 align:start position:0%
don't need it everything up until the
 

00:04:10.350 --> 00:04:11.990 align:start position:0%
don't need it everything up until the
nav<00:04:10.560><c> bar</c><00:04:10.590><c> because</c><00:04:11.100><c> we</c><00:04:11.130><c> already</c><00:04:11.400><c> have</c><00:04:11.700><c> in</c><00:04:11.850><c> that</c>

00:04:11.990 --> 00:04:12.000 align:start position:0%
nav bar because we already have in that
 

00:04:12.000 --> 00:04:14.330 align:start position:0%
nav bar because we already have in that
bar<00:04:12.269><c> anyways</c><00:04:12.720><c> and</c><00:04:13.410><c> let's</c><00:04:13.829><c> see</c><00:04:13.920><c> what</c><00:04:14.040><c> it</c><00:04:14.130><c> looks</c>

00:04:14.330 --> 00:04:14.340 align:start position:0%
bar anyways and let's see what it looks
 

00:04:14.340 --> 00:04:18.620 align:start position:0%
bar anyways and let's see what it looks
like<00:04:14.400><c> so</c><00:04:14.970><c> we're</c><00:04:15.090><c> gonna</c><00:04:15.180><c> do</c><00:04:15.390><c> a</c><00:04:15.420><c> note</c><00:04:15.690><c> on</c><00:04:17.630><c> server</c>

00:04:18.620 --> 00:04:18.630 align:start position:0%
like so we're gonna do a note on server
 

00:04:18.630 --> 00:04:25.780 align:start position:0%
like so we're gonna do a note on server
bypass

00:04:25.780 --> 00:04:25.790 align:start position:0%
 
 

00:04:25.790 --> 00:04:32.600 align:start position:0%
 
website<00:04:29.600><c> it's</c><00:04:30.600><c> running</c><00:04:31.080><c> I</c><00:04:31.290><c> can</c><00:04:32.220><c> go</c><00:04:32.370><c> to</c><00:04:32.430><c> the</c>

00:04:32.600 --> 00:04:32.610 align:start position:0%
website it's running I can go to the
 

00:04:32.610 --> 00:04:35.890 align:start position:0%
website it's running I can go to the
local<00:04:33.030><c> branch</c><00:04:33.270><c> ok</c><00:04:33.900><c> local</c><00:04:34.350><c> host</c><00:04:34.530><c> right</c><00:04:35.100><c> and</c>

00:04:35.890 --> 00:04:35.900 align:start position:0%
local branch ok local host right and
 

00:04:35.900 --> 00:04:43.600 align:start position:0%
local branch ok local host right and
don't<00:04:37.820><c> regress</c><00:04:38.820><c> enter</c><00:04:40.760><c> see</c><00:04:41.760><c> ya</c>

00:04:43.600 --> 00:04:43.610 align:start position:0%
don't regress enter see ya
 

00:04:43.610 --> 00:04:47.510 align:start position:0%
don't regress enter see ya
[Music]

00:04:47.510 --> 00:04:47.520 align:start position:0%
 
 

00:04:47.520 --> 00:04:50.690 align:start position:0%
 
and<00:04:48.110><c> as</c><00:04:49.110><c> you</c><00:04:49.380><c> can</c><00:04:49.530><c> see</c><00:04:49.590><c> that</c><00:04:50.010><c> thing</c><00:04:50.370><c> went</c><00:04:50.639><c> away</c>

00:04:50.690 --> 00:04:50.700 align:start position:0%
and as you can see that thing went away
 

00:04:50.700 --> 00:04:55.070 align:start position:0%
and as you can see that thing went away
ok<00:04:51.360><c> though</c><00:04:51.740><c> so</c><00:04:52.740><c> that's</c><00:04:52.919><c> good</c><00:04:53.360><c> so</c><00:04:54.360><c> we'll</c>

00:04:55.070 --> 00:04:55.080 align:start position:0%
ok though so that's good so we'll
 

00:04:55.080 --> 00:04:56.719 align:start position:0%
ok though so that's good so we'll
continue<00:04:55.290><c> in</c><00:04:55.680><c> the</c><00:04:55.770><c> next</c><00:04:56.010><c> video</c>

00:04:56.719 --> 00:04:56.729 align:start position:0%
continue in the next video
 

00:04:56.729 --> 00:05:00.950 align:start position:0%
continue in the next video
pushing<00:04:57.419><c> the</c><00:04:57.570><c> changes</c><00:04:58.110><c> to</c><00:04:58.500><c> the</c><00:04:58.620><c> repo</c>

