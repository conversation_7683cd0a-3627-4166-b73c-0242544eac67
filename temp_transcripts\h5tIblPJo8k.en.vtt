WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.610 align:start position:0%
 
hey<00:00:00.420><c> and</c><00:00:00.659><c> welcome</c><00:00:00.840><c> back</c><00:00:01.020><c> to</c><00:00:01.140><c> another</c><00:00:01.260><c> video</c>

00:00:01.610 --> 00:00:01.620 align:start position:0%
hey and welcome back to another video
 

00:00:01.620 --> 00:00:03.110 align:start position:0%
hey and welcome back to another video
and<00:00:02.100><c> today</c><00:00:02.280><c> we're</c><00:00:02.520><c> going</c><00:00:02.639><c> to</c><00:00:02.760><c> be</c><00:00:02.820><c> simply</c>

00:00:03.110 --> 00:00:03.120 align:start position:0%
and today we're going to be simply
 

00:00:03.120 --> 00:00:05.329 align:start position:0%
and today we're going to be simply
explaining<00:00:03.659><c> the</c><00:00:04.200><c> fourth</c><00:00:04.560><c> of</c><00:00:04.680><c> five</c><00:00:04.860><c> solid</c>

00:00:05.329 --> 00:00:05.339 align:start position:0%
explaining the fourth of five solid
 

00:00:05.339 --> 00:00:07.670 align:start position:0%
explaining the fourth of five solid
principles<00:00:05.700><c> the</c><00:00:06.240><c> integration</c><00:00:06.779><c> segregation</c>

00:00:07.670 --> 00:00:07.680 align:start position:0%
principles the integration segregation
 

00:00:07.680 --> 00:00:09.650 align:start position:0%
principles the integration segregation
principle<00:00:08.220><c> this</c><00:00:08.700><c> principle</c><00:00:09.000><c> states</c><00:00:09.300><c> that</c>

00:00:09.650 --> 00:00:09.660 align:start position:0%
principle this principle states that
 

00:00:09.660 --> 00:00:11.870 align:start position:0%
principle this principle states that
clients<00:00:10.200><c> should</c><00:00:10.679><c> not</c><00:00:10.860><c> be</c><00:00:11.040><c> forced</c><00:00:11.400><c> to</c><00:00:11.519><c> depend</c>

00:00:11.870 --> 00:00:11.880 align:start position:0%
clients should not be forced to depend
 

00:00:11.880 --> 00:00:14.810 align:start position:0%
clients should not be forced to depend
on<00:00:12.059><c> interfaces</c><00:00:12.780><c> they</c><00:00:13.440><c> do</c><00:00:13.620><c> not</c><00:00:13.799><c> use</c><00:00:14.160><c> basically</c>

00:00:14.810 --> 00:00:14.820 align:start position:0%
on interfaces they do not use basically
 

00:00:14.820 --> 00:00:16.609 align:start position:0%
on interfaces they do not use basically
this<00:00:15.059><c> is</c><00:00:15.179><c> saying</c><00:00:15.480><c> that</c><00:00:15.540><c> interfaces</c><00:00:16.139><c> should</c><00:00:16.500><c> be</c>

00:00:16.609 --> 00:00:16.619 align:start position:0%
this is saying that interfaces should be
 

00:00:16.619 --> 00:00:18.710 align:start position:0%
this is saying that interfaces should be
more<00:00:16.800><c> focused</c><00:00:17.279><c> and</c><00:00:17.520><c> specific</c><00:00:17.940><c> rather</c><00:00:18.539><c> than</c>

00:00:18.710 --> 00:00:18.720 align:start position:0%
more focused and specific rather than
 

00:00:18.720 --> 00:00:21.170 align:start position:0%
more focused and specific rather than
having<00:00:18.960><c> a</c><00:00:19.440><c> large</c><00:00:19.560><c> interface</c><00:00:20.160><c> that</c><00:00:20.580><c> provides</c><00:00:21.060><c> a</c>

00:00:21.170 --> 00:00:21.180 align:start position:0%
having a large interface that provides a
 

00:00:21.180 --> 00:00:23.689 align:start position:0%
having a large interface that provides a
lot<00:00:21.359><c> of</c><00:00:21.480><c> methods</c><00:00:22.080><c> which</c><00:00:22.740><c> may</c><00:00:22.980><c> not</c><00:00:23.100><c> be</c><00:00:23.279><c> relevant</c>

00:00:23.689 --> 00:00:23.699 align:start position:0%
lot of methods which may not be relevant
 

00:00:23.699 --> 00:00:25.550 align:start position:0%
lot of methods which may not be relevant
to<00:00:24.000><c> all</c><00:00:24.240><c> classes</c><00:00:24.660><c> that</c><00:00:24.840><c> are</c><00:00:24.960><c> implementing</c><00:00:25.439><c> it</c>

00:00:25.550 --> 00:00:25.560 align:start position:0%
to all classes that are implementing it
 

00:00:25.560 --> 00:00:26.929 align:start position:0%
to all classes that are implementing it
let's<00:00:25.800><c> just</c><00:00:25.980><c> Dive</c><00:00:26.160><c> Right</c><00:00:26.340><c> into</c><00:00:26.460><c> an</c><00:00:26.640><c> example</c>

00:00:26.929 --> 00:00:26.939 align:start position:0%
let's just Dive Right into an example
 

00:00:26.939 --> 00:00:28.730 align:start position:0%
let's just Dive Right into an example
okay<00:00:27.240><c> so</c><00:00:27.480><c> here</c><00:00:27.660><c> we</c><00:00:27.779><c> have</c><00:00:27.900><c> a</c><00:00:28.080><c> book</c><00:00:28.260><c> interface</c>

00:00:28.730 --> 00:00:28.740 align:start position:0%
okay so here we have a book interface
 

00:00:28.740 --> 00:00:30.589 align:start position:0%
okay so here we have a book interface
with<00:00:28.980><c> five</c><00:00:29.220><c> methods</c><00:00:29.699><c> open</c><00:00:30.000><c> close</c><00:00:30.300><c> read</c>

00:00:30.589 --> 00:00:30.599 align:start position:0%
with five methods open close read
 

00:00:30.599 --> 00:00:32.389 align:start position:0%
with five methods open close read
bookmark<00:00:31.019><c> and</c><00:00:31.199><c> search</c><00:00:31.439><c> and</c><00:00:31.859><c> then</c><00:00:31.980><c> we</c><00:00:32.099><c> have</c><00:00:32.220><c> two</c>

00:00:32.389 --> 00:00:32.399 align:start position:0%
bookmark and search and then we have two
 

00:00:32.399 --> 00:00:33.830 align:start position:0%
bookmark and search and then we have two
classes<00:00:32.759><c> implementing</c><00:00:33.239><c> this</c><00:00:33.360><c> interface</c>

00:00:33.830 --> 00:00:33.840 align:start position:0%
classes implementing this interface
 

00:00:33.840 --> 00:00:36.110 align:start position:0%
classes implementing this interface
paperback<00:00:34.559><c> book</c><00:00:34.800><c> and</c><00:00:35.100><c> audiobook</c><00:00:35.520><c> and</c><00:00:35.880><c> because</c>

00:00:36.110 --> 00:00:36.120 align:start position:0%
paperback book and audiobook and because
 

00:00:36.120 --> 00:00:38.270 align:start position:0%
paperback book and audiobook and because
these<00:00:36.480><c> classes</c><00:00:36.840><c> implement</c><00:00:37.260><c> the</c><00:00:37.440><c> interface</c><00:00:37.860><c> we</c>

00:00:38.270 --> 00:00:38.280 align:start position:0%
these classes implement the interface we
 

00:00:38.280 --> 00:00:39.770 align:start position:0%
these classes implement the interface we
also<00:00:38.460><c> have</c><00:00:38.520><c> to</c><00:00:38.700><c> implement</c><00:00:39.000><c> each</c><00:00:39.480><c> of</c><00:00:39.660><c> their</c>

00:00:39.770 --> 00:00:39.780 align:start position:0%
also have to implement each of their
 

00:00:39.780 --> 00:00:42.350 align:start position:0%
also have to implement each of their
methods<00:00:40.260><c> now</c><00:00:40.680><c> the</c><00:00:40.920><c> problem</c><00:00:41.100><c> is</c><00:00:41.640><c> that</c><00:00:41.879><c> not</c><00:00:42.059><c> all</c>

00:00:42.350 --> 00:00:42.360 align:start position:0%
methods now the problem is that not all
 

00:00:42.360 --> 00:00:43.670 align:start position:0%
methods now the problem is that not all
the<00:00:42.480><c> methods</c><00:00:42.899><c> defined</c><00:00:43.260><c> in</c><00:00:43.440><c> the</c><00:00:43.559><c> book</c>

00:00:43.670 --> 00:00:43.680 align:start position:0%
the methods defined in the book
 

00:00:43.680 --> 00:00:46.010 align:start position:0%
the methods defined in the book
interface<00:00:44.160><c> are</c><00:00:44.579><c> relevant</c><00:00:44.940><c> to</c><00:00:45.239><c> both</c><00:00:45.420><c> classes</c>

00:00:46.010 --> 00:00:46.020 align:start position:0%
interface are relevant to both classes
 

00:00:46.020 --> 00:00:49.250 align:start position:0%
interface are relevant to both classes
for<00:00:46.800><c> example</c><00:00:47.100><c> the</c><00:00:47.820><c> search</c><00:00:48.000><c> method</c><00:00:48.480><c> may</c><00:00:49.079><c> be</c>

00:00:49.250 --> 00:00:49.260 align:start position:0%
for example the search method may be
 

00:00:49.260 --> 00:00:51.410 align:start position:0%
for example the search method may be
specific<00:00:49.559><c> to</c><00:00:49.800><c> an</c><00:00:50.039><c> electronic</c><00:00:50.579><c> format</c><00:00:50.940><c> such</c><00:00:51.239><c> as</c>

00:00:51.410 --> 00:00:51.420 align:start position:0%
specific to an electronic format such as
 

00:00:51.420 --> 00:00:54.410 align:start position:0%
specific to an electronic format such as
an<00:00:51.600><c> ebook</c><00:00:51.899><c> or</c><00:00:52.200><c> an</c><00:00:52.379><c> audiobook</c><00:00:52.800><c> but</c><00:00:53.460><c> not</c><00:00:53.700><c> to</c><00:00:54.059><c> a</c>

00:00:54.410 --> 00:00:54.420 align:start position:0%
an ebook or an audiobook but not to a
 

00:00:54.420 --> 00:00:56.389 align:start position:0%
an ebook or an audiobook but not to a
physical<00:00:54.539><c> paperback</c><00:00:55.260><c> book</c><00:00:55.559><c> that</c><00:00:55.980><c> might</c><00:00:56.160><c> be</c>

00:00:56.389 --> 00:00:56.399 align:start position:0%
physical paperback book that might be
 

00:00:56.399 --> 00:00:58.729 align:start position:0%
physical paperback book that might be
another<00:00:56.820><c> method</c><00:00:57.300><c> saying</c><00:00:57.780><c> flipping</c><00:00:58.199><c> Pages</c><00:00:58.440><c> or</c>

00:00:58.729 --> 00:00:58.739 align:start position:0%
another method saying flipping Pages or
 

00:00:58.739 --> 00:00:59.930 align:start position:0%
another method saying flipping Pages or
something<00:00:58.920><c> now</c><00:00:59.219><c> what</c><00:00:59.399><c> we'd</c><00:00:59.579><c> have</c><00:00:59.699><c> to</c><00:00:59.760><c> do</c><00:00:59.820><c> here</c>

00:00:59.930 --> 00:00:59.940 align:start position:0%
something now what we'd have to do here
 

00:00:59.940 --> 00:01:02.209 align:start position:0%
something now what we'd have to do here
here<00:01:00.360><c> to</c><00:01:00.660><c> apply</c><00:01:00.899><c> the</c><00:01:01.079><c> interface</c><00:01:01.500><c> segregation</c>

00:01:02.209 --> 00:01:02.219 align:start position:0%
here to apply the interface segregation
 

00:01:02.219 --> 00:01:04.850 align:start position:0%
here to apply the interface segregation
principle<00:01:02.760><c> is</c><00:01:03.480><c> we</c><00:01:03.840><c> can</c><00:01:04.019><c> split</c><00:01:04.440><c> the</c><00:01:04.680><c> book</c>

00:01:04.850 --> 00:01:04.860 align:start position:0%
principle is we can split the book
 

00:01:04.860 --> 00:01:07.370 align:start position:0%
principle is we can split the book
interface<00:01:05.400><c> into</c><00:01:05.820><c> smaller</c><00:01:06.420><c> more</c><00:01:06.840><c> focused</c>

00:01:07.370 --> 00:01:07.380 align:start position:0%
interface into smaller more focused
 

00:01:07.380 --> 00:01:09.289 align:start position:0%
interface into smaller more focused
interfaces<00:01:08.159><c> all</c><00:01:08.400><c> right</c><00:01:08.580><c> in</c><00:01:08.700><c> our</c><00:01:08.880><c> code</c><00:01:09.000><c> this</c><00:01:09.180><c> is</c>

00:01:09.289 --> 00:01:09.299 align:start position:0%
interfaces all right in our code this is
 

00:01:09.299 --> 00:01:10.670 align:start position:0%
interfaces all right in our code this is
how<00:01:09.420><c> it</c><00:01:09.600><c> would</c><00:01:09.720><c> look</c><00:01:09.840><c> we</c><00:01:10.140><c> have</c><00:01:10.260><c> the</c><00:01:10.560><c> book</c>

00:01:10.670 --> 00:01:10.680 align:start position:0%
how it would look we have the book
 

00:01:10.680 --> 00:01:12.649 align:start position:0%
how it would look we have the book
interface<00:01:11.100><c> with</c><00:01:11.280><c> all</c><00:01:11.400><c> the</c><00:01:11.520><c> methods</c><00:01:11.939><c> and</c><00:01:12.540><c> then</c>

00:01:12.649 --> 00:01:12.659 align:start position:0%
interface with all the methods and then
 

00:01:12.659 --> 00:01:14.450 align:start position:0%
interface with all the methods and then
we<00:01:12.840><c> have</c><00:01:12.900><c> the</c><00:01:13.140><c> audiobook</c><00:01:13.500><c> that</c><00:01:13.979><c> influenced</c>

00:01:14.450 --> 00:01:14.460 align:start position:0%
we have the audiobook that influenced
 

00:01:14.460 --> 00:01:15.950 align:start position:0%
we have the audiobook that influenced
them<00:01:14.580><c> and</c><00:01:14.820><c> we</c><00:01:14.880><c> also</c><00:01:15.119><c> have</c><00:01:15.240><c> the</c><00:01:15.360><c> paperback</c><00:01:15.720><c> book</c>

00:01:15.950 --> 00:01:15.960 align:start position:0%
them and we also have the paperback book
 

00:01:15.960 --> 00:01:18.170 align:start position:0%
them and we also have the paperback book
that<00:01:16.560><c> also</c><00:01:16.799><c> implements</c><00:01:17.220><c> them</c><00:01:17.340><c> what</c><00:01:17.760><c> we</c><00:01:17.939><c> can</c><00:01:18.060><c> do</c>

00:01:18.170 --> 00:01:18.180 align:start position:0%
that also implements them what we can do
 

00:01:18.180 --> 00:01:19.850 align:start position:0%
that also implements them what we can do
now<00:01:18.299><c> is</c><00:01:18.540><c> split</c><00:01:18.900><c> the</c><00:01:19.020><c> methods</c><00:01:19.439><c> into</c><00:01:19.619><c> two</c>

00:01:19.850 --> 00:01:19.860 align:start position:0%
now is split the methods into two
 

00:01:19.860 --> 00:01:22.010 align:start position:0%
now is split the methods into two
different<00:01:19.979><c> interfaces</c><00:01:20.700><c> the</c><00:01:21.360><c> readable</c><00:01:21.720><c> can</c>

00:01:22.010 --> 00:01:22.020 align:start position:0%
different interfaces the readable can
 

00:01:22.020 --> 00:01:23.570 align:start position:0%
different interfaces the readable can
have<00:01:22.200><c> four</c><00:01:22.439><c> of</c><00:01:22.560><c> them</c><00:01:22.680><c> and</c><00:01:22.920><c> the</c><00:01:23.040><c> searchable</c><00:01:23.340><c> has</c>

00:01:23.570 --> 00:01:23.580 align:start position:0%
have four of them and the searchable has
 

00:01:23.580 --> 00:01:25.609 align:start position:0%
have four of them and the searchable has
one<00:01:23.820><c> the</c><00:01:24.240><c> search</c><00:01:24.479><c> method</c><00:01:24.960><c> and</c><00:01:25.380><c> the</c><00:01:25.500><c> search</c>

00:01:25.609 --> 00:01:25.619 align:start position:0%
one the search method and the search
 

00:01:25.619 --> 00:01:27.710 align:start position:0%
one the search method and the search
method<00:01:26.040><c> is</c><00:01:26.220><c> only</c><00:01:26.520><c> used</c><00:01:26.820><c> by</c><00:01:27.060><c> the</c><00:01:27.299><c> audiobook</c>

00:01:27.710 --> 00:01:27.720 align:start position:0%
method is only used by the audiobook
 

00:01:27.720 --> 00:01:29.630 align:start position:0%
method is only used by the audiobook
let's<00:01:28.080><c> see</c><00:01:28.259><c> how</c><00:01:28.380><c> this</c><00:01:28.500><c> looks</c><00:01:28.799><c> in</c><00:01:28.860><c> a</c><00:01:28.979><c> diagram</c><00:01:29.220><c> as</c>

00:01:29.630 --> 00:01:29.640 align:start position:0%
let's see how this looks in a diagram as
 

00:01:29.640 --> 00:01:31.550 align:start position:0%
let's see how this looks in a diagram as
I<00:01:29.820><c> mentioned</c><00:01:30.060><c> we</c><00:01:30.420><c> now</c><00:01:30.540><c> have</c><00:01:30.780><c> two</c><00:01:31.020><c> interfaces</c>

00:01:31.550 --> 00:01:31.560 align:start position:0%
I mentioned we now have two interfaces
 

00:01:31.560 --> 00:01:33.590 align:start position:0%
I mentioned we now have two interfaces
the<00:01:31.799><c> readable</c><00:01:32.159><c> and</c><00:01:32.460><c> searchable</c><00:01:32.939><c> now</c><00:01:33.479><c> the</c>

00:01:33.590 --> 00:01:33.600 align:start position:0%
the readable and searchable now the
 

00:01:33.600 --> 00:01:35.510 align:start position:0%
the readable and searchable now the
readable<00:01:33.960><c> only</c><00:01:34.259><c> has</c><00:01:34.439><c> the</c><00:01:34.619><c> open</c><00:01:34.740><c> close</c><00:01:35.040><c> reading</c>

00:01:35.510 --> 00:01:35.520 align:start position:0%
readable only has the open close reading
 

00:01:35.520 --> 00:01:37.370 align:start position:0%
readable only has the open close reading
bookmark<00:01:35.820><c> no</c><00:01:36.479><c> longer</c><00:01:36.780><c> does</c><00:01:36.960><c> it</c><00:01:37.140><c> have</c><00:01:37.259><c> the</c>

00:01:37.370 --> 00:01:37.380 align:start position:0%
bookmark no longer does it have the
 

00:01:37.380 --> 00:01:39.950 align:start position:0%
bookmark no longer does it have the
search<00:01:37.560><c> method</c><00:01:37.979><c> which</c><00:01:38.520><c> we</c><00:01:38.880><c> took</c><00:01:39.360><c> out</c><00:01:39.540><c> and</c><00:01:39.780><c> put</c>

00:01:39.950 --> 00:01:39.960 align:start position:0%
search method which we took out and put
 

00:01:39.960 --> 00:01:42.289 align:start position:0%
search method which we took out and put
into<00:01:40.200><c> another</c><00:01:40.560><c> interface</c><00:01:41.159><c> so</c><00:01:41.700><c> the</c><00:01:41.880><c> paperback</c>

00:01:42.289 --> 00:01:42.299 align:start position:0%
into another interface so the paperback
 

00:01:42.299 --> 00:01:44.749 align:start position:0%
into another interface so the paperback
book<00:01:42.600><c> now</c><00:01:43.079><c> only</c><00:01:43.380><c> implements</c><00:01:44.040><c> the</c><00:01:44.400><c> readable</c>

00:01:44.749 --> 00:01:44.759 align:start position:0%
book now only implements the readable
 

00:01:44.759 --> 00:01:47.149 align:start position:0%
book now only implements the readable
interface<00:01:45.360><c> which</c><00:01:45.780><c> means</c><00:01:46.140><c> it</c><00:01:46.740><c> also</c><00:01:47.040><c> only</c>

00:01:47.149 --> 00:01:47.159 align:start position:0%
interface which means it also only
 

00:01:47.159 --> 00:01:49.609 align:start position:0%
interface which means it also only
implements<00:01:47.759><c> those</c><00:01:48.119><c> four</c><00:01:48.360><c> methods</c><00:01:48.960><c> no</c><00:01:49.380><c> longer</c>

00:01:49.609 --> 00:01:49.619 align:start position:0%
implements those four methods no longer
 

00:01:49.619 --> 00:01:50.929 align:start position:0%
implements those four methods no longer
does<00:01:49.799><c> it</c><00:01:49.979><c> need</c><00:01:50.159><c> to</c><00:01:50.280><c> implement</c><00:01:50.579><c> the</c><00:01:50.759><c> search</c>

00:01:50.929 --> 00:01:50.939 align:start position:0%
does it need to implement the search
 

00:01:50.939 --> 00:01:52.490 align:start position:0%
does it need to implement the search
method<00:01:51.299><c> because</c><00:01:51.600><c> it</c><00:01:51.840><c> can't</c><00:01:51.960><c> do</c><00:01:52.140><c> anything</c><00:01:52.259><c> with</c>

00:01:52.490 --> 00:01:52.500 align:start position:0%
method because it can't do anything with
 

00:01:52.500 --> 00:01:54.830 align:start position:0%
method because it can't do anything with
it<00:01:52.680><c> anyways</c><00:01:53.220><c> we</c><00:01:53.579><c> want</c><00:01:53.700><c> it</c><00:01:53.820><c> to</c><00:01:53.939><c> be</c><00:01:54.060><c> focused</c><00:01:54.540><c> and</c>

00:01:54.830 --> 00:01:54.840 align:start position:0%
it anyways we want it to be focused and
 

00:01:54.840 --> 00:01:57.230 align:start position:0%
it anyways we want it to be focused and
specific<00:01:55.259><c> so</c><00:01:55.619><c> now</c><00:01:55.799><c> the</c><00:01:56.040><c> audiobook</c><00:01:56.520><c> implements</c>

00:01:57.230 --> 00:01:57.240 align:start position:0%
specific so now the audiobook implements
 

00:01:57.240 --> 00:01:59.389 align:start position:0%
specific so now the audiobook implements
the<00:01:57.420><c> searchable</c><00:01:57.780><c> and</c><00:01:58.740><c> the</c><00:01:59.100><c> readable</c>

00:01:59.389 --> 00:01:59.399 align:start position:0%
the searchable and the readable
 

00:01:59.399 --> 00:02:02.030 align:start position:0%
the searchable and the readable
interface<00:01:59.939><c> because</c><00:02:00.479><c> it</c><00:02:00.899><c> can</c><00:02:01.079><c> Implement</c><00:02:01.740><c> all</c>

00:02:02.030 --> 00:02:02.040 align:start position:0%
interface because it can Implement all
 

00:02:02.040 --> 00:02:03.649 align:start position:0%
interface because it can Implement all
five<00:02:02.159><c> of</c><00:02:02.399><c> those</c><00:02:02.520><c> methods</c><00:02:02.939><c> now</c><00:02:03.299><c> let's</c><00:02:03.420><c> go</c><00:02:03.600><c> ahead</c>

00:02:03.649 --> 00:02:03.659 align:start position:0%
five of those methods now let's go ahead
 

00:02:03.659 --> 00:02:05.030 align:start position:0%
five of those methods now let's go ahead
and<00:02:03.780><c> code</c><00:02:03.960><c> this</c><00:02:04.200><c> all</c><00:02:04.500><c> right</c><00:02:04.680><c> well</c><00:02:04.799><c> let's</c><00:02:04.920><c> go</c>

00:02:05.030 --> 00:02:05.040 align:start position:0%
and code this all right well let's go
 

00:02:05.040 --> 00:02:06.590 align:start position:0%
and code this all right well let's go
and<00:02:05.159><c> separate</c><00:02:05.460><c> out</c><00:02:05.640><c> the</c><00:02:05.759><c> book</c><00:02:05.880><c> interface</c><00:02:06.299><c> into</c>

00:02:06.590 --> 00:02:06.600 align:start position:0%
and separate out the book interface into
 

00:02:06.600 --> 00:02:11.510 align:start position:0%
and separate out the book interface into
two<00:02:06.899><c> separate</c><00:02:07.259><c> ones</c>

00:02:11.510 --> 00:02:11.520 align:start position:0%
 
 

00:02:11.520 --> 00:02:13.190 align:start position:0%
 
okay<00:02:11.940><c> now</c><00:02:12.180><c> we</c><00:02:12.300><c> have</c><00:02:12.420><c> the</c><00:02:12.480><c> readable</c><00:02:12.780><c> and</c><00:02:13.020><c> the</c>

00:02:13.190 --> 00:02:13.200 align:start position:0%
okay now we have the readable and the
 

00:02:13.200 --> 00:02:14.809 align:start position:0%
okay now we have the readable and the
searchable<00:02:13.500><c> interfaces</c><00:02:14.099><c> all</c><00:02:14.340><c> we</c><00:02:14.459><c> have</c><00:02:14.580><c> to</c><00:02:14.640><c> do</c>

00:02:14.809 --> 00:02:14.819 align:start position:0%
searchable interfaces all we have to do
 

00:02:14.819 --> 00:02:16.910 align:start position:0%
searchable interfaces all we have to do
now<00:02:15.000><c> is</c><00:02:15.300><c> re-implement</c><00:02:16.080><c> the</c><00:02:16.260><c> audiobook</c><00:02:16.680><c> and</c>

00:02:16.910 --> 00:02:16.920 align:start position:0%
now is re-implement the audiobook and
 

00:02:16.920 --> 00:02:18.890 align:start position:0%
now is re-implement the audiobook and
paperback<00:02:17.340><c> book</c><00:02:17.580><c> classes</c><00:02:18.120><c> for</c><00:02:18.480><c> these</c><00:02:18.780><c> new</c>

00:02:18.890 --> 00:02:18.900 align:start position:0%
paperback book classes for these new
 

00:02:18.900 --> 00:02:23.710 align:start position:0%
paperback book classes for these new
interfaces

00:02:23.710 --> 00:02:23.720 align:start position:0%
 
 

00:02:23.720 --> 00:02:26.089 align:start position:0%
 
okay<00:02:24.720><c> now</c><00:02:24.900><c> for</c><00:02:25.080><c> the</c><00:02:25.200><c> paperback</c><00:02:25.500><c> book</c><00:02:25.739><c> class</c>

00:02:26.089 --> 00:02:26.099 align:start position:0%
okay now for the paperback book class
 

00:02:26.099 --> 00:02:27.530 align:start position:0%
okay now for the paperback book class
all<00:02:26.459><c> we</c><00:02:26.580><c> need</c><00:02:26.700><c> to</c><00:02:26.760><c> do</c><00:02:26.879><c> is</c><00:02:26.940><c> implement</c><00:02:27.300><c> the</c>

00:02:27.530 --> 00:02:27.540 align:start position:0%
all we need to do is implement the
 

00:02:27.540 --> 00:02:30.290 align:start position:0%
all we need to do is implement the
readable<00:02:27.959><c> interface</c><00:02:28.440><c> not</c><00:02:28.800><c> both</c><00:02:29.099><c> of</c><00:02:29.340><c> them</c><00:02:29.459><c> so</c>

00:02:30.290 --> 00:02:30.300 align:start position:0%
readable interface not both of them so
 

00:02:30.300 --> 00:02:31.690 align:start position:0%
readable interface not both of them so
when<00:02:30.420><c> I</c><00:02:30.540><c> go</c><00:02:30.660><c> to</c><00:02:30.720><c> do</c><00:02:30.959><c> this</c>

00:02:31.690 --> 00:02:31.700 align:start position:0%
when I go to do this
 

00:02:31.700 --> 00:02:34.430 align:start position:0%
when I go to do this
implements<00:02:32.700><c> readable</c>

00:02:34.430 --> 00:02:34.440 align:start position:0%
implements readable
 

00:02:34.440 --> 00:02:36.470 align:start position:0%
implements readable
it's<00:02:34.860><c> only</c><00:02:34.980><c> going</c><00:02:35.160><c> to</c><00:02:35.280><c> have</c><00:02:35.400><c> me</c><00:02:35.580><c> implement</c><00:02:35.940><c> the</c>

00:02:36.470 --> 00:02:36.480 align:start position:0%
it's only going to have me implement the
 

00:02:36.480 --> 00:02:39.110 align:start position:0%
it's only going to have me implement the
four<00:02:36.660><c> methods</c><00:02:37.200><c> instead</c><00:02:37.560><c> of</c><00:02:37.680><c> all</c><00:02:37.860><c> five</c><00:02:38.160><c> and</c><00:02:39.000><c> now</c>

00:02:39.110 --> 00:02:39.120 align:start position:0%
four methods instead of all five and now
 

00:02:39.120 --> 00:02:41.089 align:start position:0%
four methods instead of all five and now
we<00:02:39.239><c> have</c><00:02:39.360><c> fixed</c><00:02:39.660><c> the</c><00:02:39.720><c> code</c><00:02:39.840><c> to</c><00:02:40.200><c> conform</c><00:02:40.680><c> to</c><00:02:40.860><c> the</c>

00:02:41.089 --> 00:02:41.099 align:start position:0%
we have fixed the code to conform to the
 

00:02:41.099 --> 00:02:43.550 align:start position:0%
we have fixed the code to conform to the
interface<00:02:41.519><c> segregation</c><00:02:42.180><c> principle</c><00:02:42.660><c> again</c><00:02:43.200><c> we</c>

00:02:43.550 --> 00:02:43.560 align:start position:0%
interface segregation principle again we
 

00:02:43.560 --> 00:02:45.410 align:start position:0%
interface segregation principle again we
don't<00:02:43.680><c> want</c><00:02:43.860><c> to</c><00:02:43.980><c> force</c><00:02:44.340><c> classes</c><00:02:44.760><c> to</c><00:02:45.120><c> implement</c>

00:02:45.410 --> 00:02:45.420 align:start position:0%
don't want to force classes to implement
 

00:02:45.420 --> 00:02:47.630 align:start position:0%
don't want to force classes to implement
methods<00:02:45.959><c> that</c><00:02:46.140><c> they</c><00:02:46.319><c> don't</c><00:02:46.440><c> need</c><00:02:46.739><c> to</c><00:02:46.980><c> here's</c><00:02:47.519><c> a</c>

00:02:47.630 --> 00:02:47.640 align:start position:0%
methods that they don't need to here's a
 

00:02:47.640 --> 00:02:49.190 align:start position:0%
methods that they don't need to here's a
playlist<00:02:47.760><c> on</c><00:02:48.120><c> the</c><00:02:48.300><c> other</c><00:02:48.360><c> videos</c><00:02:48.540><c> of</c><00:02:48.840><c> solid</c>

00:02:49.190 --> 00:02:49.200 align:start position:0%
playlist on the other videos of solid
 

00:02:49.200 --> 00:02:50.509 align:start position:0%
playlist on the other videos of solid
principles<00:02:49.560><c> that</c><00:02:49.800><c> I've</c><00:02:49.920><c> created</c><00:02:50.220><c> if</c><00:02:50.400><c> you</c>

00:02:50.509 --> 00:02:50.519 align:start position:0%
principles that I've created if you
 

00:02:50.519 --> 00:02:51.890 align:start position:0%
principles that I've created if you
haven't<00:02:50.640><c> watched</c><00:02:50.940><c> them</c><00:02:51.060><c> go</c><00:02:51.540><c> ahead</c><00:02:51.660><c> and</c><00:02:51.720><c> click</c>

00:02:51.890 --> 00:02:51.900 align:start position:0%
haven't watched them go ahead and click
 

00:02:51.900 --> 00:02:53.150 align:start position:0%
haven't watched them go ahead and click
this<00:02:52.019><c> link</c><00:02:52.140><c> so</c><00:02:52.319><c> you</c><00:02:52.440><c> can</c><00:02:52.560><c> do</c><00:02:52.620><c> so</c><00:02:52.739><c> if</c><00:02:52.980><c> you</c><00:02:53.099><c> have</c>

00:02:53.150 --> 00:02:53.160 align:start position:0%
this link so you can do so if you have
 

00:02:53.160 --> 00:02:54.770 align:start position:0%
this link so you can do so if you have
any<00:02:53.280><c> questions</c><00:02:53.519><c> or</c><00:02:53.760><c> suggestions</c><00:02:54.180><c> put</c><00:02:54.540><c> them</c><00:02:54.660><c> in</c>

00:02:54.770 --> 00:02:54.780 align:start position:0%
any questions or suggestions put them in
 

00:02:54.780 --> 00:02:56.330 align:start position:0%
any questions or suggestions put them in
the<00:02:54.900><c> comment</c><00:02:55.080><c> section</c><00:02:55.319><c> below</c><00:02:55.620><c> and</c><00:02:56.099><c> I'll</c><00:02:56.160><c> get</c>

00:02:56.330 --> 00:02:56.340 align:start position:0%
the comment section below and I'll get
 

00:02:56.340 --> 00:02:59.420 align:start position:0%
the comment section below and I'll get
back<00:02:56.459><c> to</c><00:02:56.580><c> you</c><00:02:56.700><c> have</c><00:02:57.000><c> a</c><00:02:57.120><c> wonderful</c><00:02:57.300><c> day</c>

