WEBVTT
Kind: captions
Language: en

00:00:01.360 --> 00:00:04.110 align:start position:0%
 
new<00:00:01.640><c> items</c><00:00:02.040><c> in</c><00:00:02.200><c> the</c><00:00:02.360><c> GitHub</c><00:00:02.760><c> shop</c><00:00:03.199><c> a</c><00:00:03.399><c> great</c><00:00:03.719><c> new</c>

00:00:04.110 --> 00:00:04.120 align:start position:0%
new items in the GitHub shop a great new
 

00:00:04.120 --> 00:00:07.349 align:start position:0%
new items in the GitHub shop a great new
small<00:00:04.680><c> language</c><00:00:05.120><c> model</c><00:00:06.040><c> code</c><00:00:06.359><c> font</c><00:00:06.640><c> updates</c>

00:00:07.349 --> 00:00:07.359 align:start position:0%
small language model code font updates
 

00:00:07.359 --> 00:00:09.750 align:start position:0%
small language model code font updates
and<00:00:08.040><c> the</c><00:00:08.120><c> nerdiest</c><00:00:08.599><c> Eurovision</c><00:00:09.080><c> entry</c><00:00:09.480><c> of</c><00:00:09.639><c> the</c>

00:00:09.750 --> 00:00:09.760 align:start position:0%
and the nerdiest Eurovision entry of the
 

00:00:09.760 --> 00:00:11.950 align:start position:0%
and the nerdiest Eurovision entry of the
Year<00:00:10.559><c> all</c><00:00:10.759><c> that</c><00:00:10.880><c> and</c><00:00:11.040><c> more</c><00:00:11.240><c> on</c><00:00:11.400><c> this</c><00:00:11.559><c> episode</c>

00:00:11.950 --> 00:00:11.960 align:start position:0%
Year all that and more on this episode
 

00:00:11.960 --> 00:00:15.010 align:start position:0%
Year all that and more on this episode
of<00:00:12.280><c> the</c>

00:00:15.010 --> 00:00:15.020 align:start position:0%
 
 

00:00:15.020 --> 00:00:17.950 align:start position:0%
 
[Music]

00:00:17.950 --> 00:00:17.960 align:start position:0%
[Music]
 

00:00:17.960 --> 00:00:20.230 align:start position:0%
[Music]
download<00:00:18.960><c> welcome</c><00:00:19.240><c> back</c><00:00:19.359><c> to</c><00:00:19.520><c> another</c><00:00:19.760><c> episode</c>

00:00:20.230 --> 00:00:20.240 align:start position:0%
download welcome back to another episode
 

00:00:20.240 --> 00:00:22.109 align:start position:0%
download welcome back to another episode
of<00:00:20.439><c> the</c><00:00:20.600><c> download</c><00:00:21.160><c> I'm</c><00:00:21.240><c> your</c><00:00:21.439><c> host</c><00:00:21.720><c> Christina</c>

00:00:22.109 --> 00:00:22.119 align:start position:0%
of the download I'm your host Christina
 

00:00:22.119 --> 00:00:23.830 align:start position:0%
of the download I'm your host Christina
Warren<00:00:22.439><c> senior</c><00:00:22.680><c> develop</c><00:00:23.000><c> for</c><00:00:23.119><c> Advocate</c><00:00:23.560><c> at</c>

00:00:23.830 --> 00:00:23.840 align:start position:0%
Warren senior develop for Advocate at
 

00:00:23.840 --> 00:00:25.950 align:start position:0%
Warren senior develop for Advocate at
GitHub<00:00:24.760><c> and</c><00:00:25.160><c> this</c><00:00:25.279><c> is</c><00:00:25.400><c> the</c><00:00:25.519><c> show</c><00:00:25.720><c> where</c><00:00:25.840><c> we</c>

00:00:25.950 --> 00:00:25.960 align:start position:0%
GitHub and this is the show where we
 

00:00:25.960 --> 00:00:27.589 align:start position:0%
GitHub and this is the show where we
cover<00:00:26.160><c> the</c><00:00:26.279><c> latest</c><00:00:26.519><c> developer</c><00:00:26.920><c> news</c><00:00:27.199><c> and</c><00:00:27.359><c> open</c>

00:00:27.589 --> 00:00:27.599 align:start position:0%
cover the latest developer news and open
 

00:00:27.599 --> 00:00:29.310 align:start position:0%
cover the latest developer news and open
source<00:00:27.920><c> Projects</c><00:00:28.800><c> please</c><00:00:29.039><c> like</c><00:00:29.160><c> And</c>

00:00:29.310 --> 00:00:29.320 align:start position:0%
source Projects please like And
 

00:00:29.320 --> 00:00:32.510 align:start position:0%
source Projects please like And
subscribe<00:00:30.119><c> so</c><00:00:30.560><c> my</c><00:00:30.800><c> shirt</c><00:00:31.640><c> this</c><00:00:31.800><c> week</c><00:00:32.160><c> is</c>

00:00:32.510 --> 00:00:32.520 align:start position:0%
subscribe so my shirt this week is
 

00:00:32.520 --> 00:00:35.069 align:start position:0%
subscribe so my shirt this week is
actually<00:00:33.200><c> related</c><00:00:33.640><c> to</c><00:00:33.800><c> some</c><00:00:34.000><c> news</c><00:00:34.680><c> it</c><00:00:34.800><c> is</c><00:00:34.920><c> a</c>

00:00:35.069 --> 00:00:35.079 align:start position:0%
actually related to some news it is a
 

00:00:35.079 --> 00:00:38.030 align:start position:0%
actually related to some news it is a
new<00:00:35.360><c> item</c><00:00:35.879><c> in</c><00:00:36.160><c> the</c><00:00:36.320><c> GitHub</c><00:00:36.719><c> shop</c><00:00:37.120><c> from</c><00:00:37.559><c> our</c>

00:00:38.030 --> 00:00:38.040 align:start position:0%
new item in the GitHub shop from our
 

00:00:38.040 --> 00:00:40.709 align:start position:0%
new item in the GitHub shop from our
sporttech<00:00:39.120><c> collection</c><00:00:40.120><c> uh</c><00:00:40.239><c> the</c><00:00:40.320><c> sweatshirt</c>

00:00:40.709 --> 00:00:40.719 align:start position:0%
sporttech collection uh the sweatshirt
 

00:00:40.719 --> 00:00:42.029 align:start position:0%
sporttech collection uh the sweatshirt
that<00:00:40.800><c> I'm</c><00:00:40.920><c> wearing</c><00:00:41.320><c> is</c><00:00:41.480><c> called</c><00:00:41.800><c> the</c>

00:00:42.029 --> 00:00:42.039 align:start position:0%
that I'm wearing is called the
 

00:00:42.039 --> 00:00:44.069 align:start position:0%
that I'm wearing is called the
Collegiate<00:00:42.920><c> sweatshirt</c><00:00:43.520><c> and</c><00:00:43.719><c> you</c><00:00:43.840><c> know</c><00:00:43.960><c> it</c>

00:00:44.069 --> 00:00:44.079 align:start position:0%
Collegiate sweatshirt and you know it
 

00:00:44.079 --> 00:00:46.430 align:start position:0%
Collegiate sweatshirt and you know it
fits<00:00:44.360><c> the</c><00:00:44.480><c> vibe</c><00:00:44.920><c> I</c><00:00:45.039><c> like</c><00:00:45.200><c> it</c><00:00:45.800><c> I've</c><00:00:46.000><c> also</c><00:00:46.199><c> got</c>

00:00:46.430 --> 00:00:46.440 align:start position:0%
fits the vibe I like it I've also got
 

00:00:46.440 --> 00:00:48.950 align:start position:0%
fits the vibe I like it I've also got
another<00:00:46.879><c> hoodie</c><00:00:47.559><c> with</c><00:00:47.760><c> me</c><00:00:48.399><c> this</c><00:00:48.520><c> one</c><00:00:48.800><c> right</c>

00:00:48.950 --> 00:00:48.960 align:start position:0%
another hoodie with me this one right
 

00:00:48.960 --> 00:00:51.830 align:start position:0%
another hoodie with me this one right
here<00:00:49.600><c> um</c><00:00:49.800><c> this</c><00:00:49.879><c> is</c><00:00:50.039><c> the</c><00:00:50.160><c> sportique</c><00:00:51.160><c> logo</c><00:00:51.600><c> patch</c>

00:00:51.830 --> 00:00:51.840 align:start position:0%
here um this is the sportique logo patch
 

00:00:51.840 --> 00:00:54.510 align:start position:0%
here um this is the sportique logo patch
hoodie<00:00:52.440><c> complete</c><00:00:52.960><c> with</c><00:00:53.399><c> um</c><00:00:53.559><c> embroidered</c><00:00:54.359><c> um</c>

00:00:54.510 --> 00:00:54.520 align:start position:0%
hoodie complete with um embroidered um
 

00:00:54.520 --> 00:00:56.790 align:start position:0%
hoodie complete with um embroidered um
Mona<00:00:54.920><c> logo</c><00:00:55.480><c> you</c><00:00:55.600><c> know</c><00:00:55.840><c> because</c><00:00:56.079><c> octocat</c><00:00:56.640><c> are</c>

00:00:56.790 --> 00:00:56.800 align:start position:0%
Mona logo you know because octocat are
 

00:00:56.800 --> 00:00:59.069 align:start position:0%
Mona logo you know because octocat are
better<00:00:57.000><c> than</c><00:00:57.680><c> crocodiles</c><00:00:58.680><c> there</c><00:00:58.760><c> are</c><00:00:58.920><c> some</c>

00:00:59.069 --> 00:00:59.079 align:start position:0%
better than crocodiles there are some
 

00:00:59.079 --> 00:01:00.950 align:start position:0%
better than crocodiles there are some
other<00:00:59.239><c> new</c><00:00:59.440><c> items</c><00:01:00.000><c> in</c><00:01:00.079><c> the</c><00:01:00.199><c> store</c><00:01:00.480><c> too</c>

00:01:00.950 --> 00:01:00.960 align:start position:0%
other new items in the store too
 

00:01:00.960 --> 00:01:03.590 align:start position:0%
other new items in the store too
including<00:01:01.399><c> a</c><00:01:01.559><c> new</c><00:01:01.800><c> travel</c><00:01:02.480><c> backpack</c><00:01:03.399><c> some</c>

00:01:03.590 --> 00:01:03.600 align:start position:0%
including a new travel backpack some
 

00:01:03.600 --> 00:01:05.950 align:start position:0%
including a new travel backpack some
tumblers<00:01:04.559><c> and</c><00:01:04.760><c> a</c><00:01:04.879><c> new</c><00:01:05.000><c> t-shirt</c><00:01:05.400><c> that</c><00:01:05.519><c> comes</c><00:01:05.760><c> in</c>

00:01:05.950 --> 00:01:05.960 align:start position:0%
tumblers and a new t-shirt that comes in
 

00:01:05.960 --> 00:01:08.190 align:start position:0%
tumblers and a new t-shirt that comes in
different<00:01:06.280><c> naturally</c><00:01:06.720><c> dyed</c><00:01:07.080><c> colors</c><00:01:07.920><c> and</c>

00:01:08.190 --> 00:01:08.200 align:start position:0%
different naturally dyed colors and
 

00:01:08.200 --> 00:01:09.830 align:start position:0%
different naturally dyed colors and
keeping<00:01:08.400><c> it</c><00:01:08.560><c> Collegiate</c><00:01:09.040><c> there</c><00:01:09.159><c> is</c><00:01:09.400><c> also</c><00:01:09.720><c> a</c>

00:01:09.830 --> 00:01:09.840 align:start position:0%
keeping it Collegiate there is also a
 

00:01:09.840 --> 00:01:12.070 align:start position:0%
keeping it Collegiate there is also a
varsity<00:01:10.320><c> jacket</c><00:01:11.159><c> you</c><00:01:11.280><c> can</c><00:01:11.400><c> check</c><00:01:11.560><c> out</c><00:01:11.720><c> all</c><00:01:11.840><c> the</c>

00:01:12.070 --> 00:01:12.080 align:start position:0%
varsity jacket you can check out all the
 

00:01:12.080 --> 00:01:15.030 align:start position:0%
varsity jacket you can check out all the
items<00:01:12.680><c> at</c><00:01:13.040><c> the</c><00:01:13.240><c> GitHub</c><00:01:13.759><c> shop.com</c><00:01:14.759><c> and</c><00:01:14.920><c> I've</c>

00:01:15.030 --> 00:01:15.040 align:start position:0%
items at the GitHub shop.com and I've
 

00:01:15.040 --> 00:01:16.830 align:start position:0%
items at the GitHub shop.com and I've
got<00:01:15.159><c> that</c><00:01:15.320><c> linked</c><00:01:15.600><c> down</c><00:01:15.759><c> below</c>

00:01:16.830 --> 00:01:16.840 align:start position:0%
got that linked down below
 

00:01:16.840 --> 00:01:20.590 align:start position:0%
got that linked down below
to<00:01:17.840><c> speaking</c><00:01:18.159><c> of</c><00:01:18.400><c> Swag</c><00:01:19.400><c> conferences</c><00:01:20.159><c> always</c>

00:01:20.590 --> 00:01:20.600 align:start position:0%
to speaking of Swag conferences always
 

00:01:20.600 --> 00:01:23.950 align:start position:0%
to speaking of Swag conferences always
have<00:01:20.920><c> swag</c><00:01:21.720><c> which</c><00:01:21.920><c> is</c><00:01:22.159><c> my</c><00:01:22.720><c> very</c><00:01:22.960><c> awkward</c><00:01:23.320><c> segue</c>

00:01:23.950 --> 00:01:23.960 align:start position:0%
have swag which is my very awkward segue
 

00:01:23.960 --> 00:01:25.469 align:start position:0%
have swag which is my very awkward segue
into<00:01:24.240><c> reminding</c><00:01:24.600><c> you</c><00:01:24.720><c> all</c><00:01:24.920><c> that</c><00:01:25.040><c> Microsoft</c>

00:01:25.469 --> 00:01:25.479 align:start position:0%
into reminding you all that Microsoft
 

00:01:25.479 --> 00:01:27.429 align:start position:0%
into reminding you all that Microsoft
build<00:01:26.040><c> will</c><00:01:26.200><c> be</c><00:01:26.360><c> taking</c><00:01:26.680><c> place</c><00:01:26.920><c> in</c><00:01:27.079><c> a</c><00:01:27.200><c> couple</c>

00:01:27.429 --> 00:01:27.439 align:start position:0%
build will be taking place in a couple
 

00:01:27.439 --> 00:01:30.550 align:start position:0%
build will be taking place in a couple
of<00:01:27.560><c> weeks</c><00:01:28.040><c> May</c><00:01:28.280><c> 21st</c><00:01:28.960><c> through</c><00:01:29.200><c> May</c><00:01:29.400><c> 23rd</c><00:01:30.360><c> in</c>

00:01:30.550 --> 00:01:30.560 align:start position:0%
of weeks May 21st through May 23rd in
 

00:01:30.560 --> 00:01:33.270 align:start position:0%
of weeks May 21st through May 23rd in
fact<00:01:31.040><c> online</c><00:01:31.479><c> and</c><00:01:31.640><c> in</c><00:01:31.799><c> person</c><00:01:32.159><c> in</c><00:01:32.320><c> Seattle</c><00:01:33.159><c> I'm</c>

00:01:33.270 --> 00:01:33.280 align:start position:0%
fact online and in person in Seattle I'm
 

00:01:33.280 --> 00:01:34.789 align:start position:0%
fact online and in person in Seattle I'm
going<00:01:33.399><c> to</c><00:01:33.479><c> be</c><00:01:33.560><c> at</c><00:01:33.640><c> Microsoft</c><00:01:34.040><c> build</c><00:01:34.479><c> so</c><00:01:34.640><c> if</c><00:01:34.720><c> you</c>

00:01:34.789 --> 00:01:34.799 align:start position:0%
going to be at Microsoft build so if you
 

00:01:34.799 --> 00:01:36.109 align:start position:0%
going to be at Microsoft build so if you
tune<00:01:35.000><c> into</c><00:01:35.119><c> to</c><00:01:35.240><c> the</c><00:01:35.360><c> live</c><00:01:35.520><c> stream</c><00:01:35.840><c> you</c><00:01:35.960><c> might</c>

00:01:36.109 --> 00:01:36.119 align:start position:0%
tune into to the live stream you might
 

00:01:36.119 --> 00:01:38.149 align:start position:0%
tune into to the live stream you might
see<00:01:36.320><c> my</c><00:01:36.520><c> face</c><00:01:36.960><c> but</c><00:01:37.119><c> if</c><00:01:37.200><c> you're</c><00:01:37.399><c> in</c><00:01:37.560><c> person</c><00:01:38.000><c> and</c>

00:01:38.149 --> 00:01:38.159 align:start position:0%
see my face but if you're in person and
 

00:01:38.159 --> 00:01:40.469 align:start position:0%
see my face but if you're in person and
you<00:01:38.280><c> see</c><00:01:38.479><c> me</c><00:01:39.040><c> please</c><00:01:39.280><c> say</c><00:01:39.520><c> hello</c><00:01:40.280><c> there's</c>

00:01:40.469 --> 00:01:40.479 align:start position:0%
you see me please say hello there's
 

00:01:40.479 --> 00:01:42.710 align:start position:0%
you see me please say hello there's
going<00:01:40.600><c> to</c><00:01:40.680><c> be</c><00:01:40.840><c> a</c><00:01:40.960><c> ton</c><00:01:41.159><c> of</c><00:01:41.399><c> AI</c><00:01:41.880><c> content</c><00:01:42.320><c> and</c><00:01:42.479><c> also</c>

00:01:42.710 --> 00:01:42.720 align:start position:0%
going to be a ton of AI content and also
 

00:01:42.720 --> 00:01:45.109 align:start position:0%
going to be a ton of AI content and also
some<00:01:42.920><c> GitHub</c><00:01:43.280><c> co-pilot</c><00:01:44.240><c> um</c><00:01:44.399><c> updates</c><00:01:44.840><c> and</c><00:01:44.960><c> of</c>

00:01:45.109 --> 00:01:45.119 align:start position:0%
some GitHub co-pilot um updates and of
 

00:01:45.119 --> 00:01:46.310 align:start position:0%
some GitHub co-pilot um updates and of
course<00:01:45.320><c> we</c><00:01:45.439><c> will</c><00:01:45.600><c> cover</c><00:01:45.920><c> all</c><00:01:46.040><c> the</c><00:01:46.159><c> big</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
course we will cover all the big
 

00:01:46.320 --> 00:01:48.590 align:start position:0%
course we will cover all the big
announcements<00:01:47.200><c> after</c><00:01:47.439><c> the</c><00:01:47.520><c> show</c><00:01:47.960><c> right</c><00:01:48.159><c> here</c>

00:01:48.590 --> 00:01:48.600 align:start position:0%
announcements after the show right here
 

00:01:48.600 --> 00:01:50.469 align:start position:0%
announcements after the show right here
and<00:01:48.719><c> I've</c><00:01:48.840><c> got</c><00:01:48.920><c> a</c><00:01:49.040><c> link</c><00:01:49.560><c> U</c><00:01:49.920><c> about</c><00:01:50.079><c> Microsoft</c>

00:01:50.469 --> 00:01:50.479 align:start position:0%
and I've got a link U about Microsoft
 

00:01:50.479 --> 00:01:52.789 align:start position:0%
and I've got a link U about Microsoft
build<00:01:50.759><c> Link</c><00:01:51.000><c> down</c><00:01:51.360><c> below</c><00:01:52.360><c> speaking</c><00:01:52.640><c> of</c>

00:01:52.789 --> 00:01:52.799 align:start position:0%
build Link down below speaking of
 

00:01:52.799 --> 00:01:55.510 align:start position:0%
build Link down below speaking of
co-pilot<00:01:53.640><c> GitHub</c><00:01:54.000><c> co-pilot</c><00:01:54.439><c> chat</c><00:01:54.799><c> in</c><00:01:55.119><c> GitHub</c>

00:01:55.510 --> 00:01:55.520 align:start position:0%
co-pilot GitHub co-pilot chat in GitHub
 

00:01:55.520 --> 00:01:57.789 align:start position:0%
co-pilot GitHub co-pilot chat in GitHub
mobile<00:01:56.000><c> is</c><00:01:56.159><c> now</c><00:01:56.439><c> generally</c><00:01:56.880><c> available</c><00:01:57.600><c> and</c>

00:01:57.789 --> 00:01:57.799 align:start position:0%
mobile is now generally available and
 

00:01:57.799 --> 00:01:59.950 align:start position:0%
mobile is now generally available and
this<00:01:57.960><c> feature</c><00:01:58.280><c> allows</c><00:01:58.600><c> you</c><00:01:58.759><c> to</c><00:01:59.119><c> chat</c><00:01:59.399><c> with</c><00:01:59.600><c> Co</c>

00:01:59.950 --> 00:01:59.960 align:start position:0%
this feature allows you to chat with Co
 

00:01:59.960 --> 00:02:01.990 align:start position:0%
this feature allows you to chat with Co
pilot<00:02:00.520><c> in</c><00:02:00.680><c> the</c><00:02:00.840><c> GitHub</c><00:02:01.159><c> mobile</c><00:02:01.399><c> app</c><00:02:01.759><c> and</c><00:02:01.880><c> you</c>

00:02:01.990 --> 00:02:02.000 align:start position:0%
pilot in the GitHub mobile app and you
 

00:02:02.000 --> 00:02:04.270 align:start position:0%
pilot in the GitHub mobile app and you
can<00:02:02.200><c> ask</c><00:02:02.479><c> questions</c><00:02:03.119><c> get</c><00:02:03.320><c> code</c><00:02:03.600><c> suggestions</c>

00:02:04.270 --> 00:02:04.280 align:start position:0%
can ask questions get code suggestions
 

00:02:04.280 --> 00:02:05.910 align:start position:0%
can ask questions get code suggestions
and<00:02:04.439><c> more</c><00:02:04.880><c> and</c><00:02:05.000><c> it's</c><00:02:05.159><c> a</c><00:02:05.280><c> really</c><00:02:05.439><c> great</c><00:02:05.600><c> way</c><00:02:05.759><c> to</c>

00:02:05.910 --> 00:02:05.920 align:start position:0%
and more and it's a really great way to
 

00:02:05.920 --> 00:02:08.070 align:start position:0%
and more and it's a really great way to
get<00:02:06.079><c> help</c><00:02:06.280><c> on</c><00:02:06.399><c> the</c><00:02:06.560><c> go</c><00:02:07.520><c> and</c><00:02:07.640><c> I've</c><00:02:07.799><c> got</c><00:02:07.960><c> more</c>

00:02:08.070 --> 00:02:08.080 align:start position:0%
get help on the go and I've got more
 

00:02:08.080 --> 00:02:10.150 align:start position:0%
get help on the go and I've got more
details<00:02:08.479><c> about</c><00:02:08.720><c> GitHub</c><00:02:09.039><c> co-pilot</c><00:02:09.479><c> chat</c><00:02:10.000><c> in</c>

00:02:10.150 --> 00:02:10.160 align:start position:0%
details about GitHub co-pilot chat in
 

00:02:10.160 --> 00:02:11.869 align:start position:0%
details about GitHub co-pilot chat in
the<00:02:10.280><c> GitHub</c><00:02:10.560><c> mobile</c><00:02:10.840><c> app</c><00:02:11.200><c> um</c><00:02:11.360><c> available</c><00:02:11.760><c> on</c>

00:02:11.869 --> 00:02:11.879 align:start position:0%
the GitHub mobile app um available on
 

00:02:11.879 --> 00:02:14.430 align:start position:0%
the GitHub mobile app um available on
the<00:02:12.000><c> GitHub</c><00:02:12.520><c> blog</c><00:02:13.520><c> say</c><00:02:13.760><c> GitHub</c><00:02:14.080><c> one</c><00:02:14.160><c> more</c><00:02:14.280><c> time</c>

00:02:14.430 --> 00:02:14.440 align:start position:0%
the GitHub blog say GitHub one more time
 

00:02:14.440 --> 00:02:16.869 align:start position:0%
the GitHub blog say GitHub one more time
Christina<00:02:15.160><c> I've</c><00:02:15.319><c> got</c><00:02:15.440><c> that</c><00:02:15.519><c> link</c><00:02:15.720><c> down</c><00:02:15.879><c> below</c>

00:02:16.869 --> 00:02:16.879 align:start position:0%
Christina I've got that link down below
 

00:02:16.879 --> 00:02:18.350 align:start position:0%
Christina I've got that link down below
and<00:02:17.000><c> to</c><00:02:17.160><c> round</c><00:02:17.360><c> out</c><00:02:17.480><c> some</c><00:02:17.640><c> product</c><00:02:17.920><c> updates</c>

00:02:18.350 --> 00:02:18.360 align:start position:0%
and to round out some product updates
 

00:02:18.360 --> 00:02:20.670 align:start position:0%
and to round out some product updates
dependabot<00:02:19.160><c> our</c><00:02:19.440><c> tool</c><00:02:19.760><c> for</c><00:02:20.040><c> monitoring</c><00:02:20.519><c> your</c>

00:02:20.670 --> 00:02:20.680 align:start position:0%
dependabot our tool for monitoring your
 

00:02:20.680 --> 00:02:22.550 align:start position:0%
dependabot our tool for monitoring your
repos<00:02:21.440><c> and</c><00:02:21.599><c> alerting</c><00:02:22.000><c> you</c><00:02:22.160><c> when</c><00:02:22.280><c> there's</c><00:02:22.440><c> a</c>

00:02:22.550 --> 00:02:22.560 align:start position:0%
repos and alerting you when there's a
 

00:02:22.560 --> 00:02:24.670 align:start position:0%
repos and alerting you when there's a
known<00:02:22.840><c> security</c><00:02:23.160><c> vulnerability</c><00:02:24.120><c> can</c><00:02:24.360><c> now</c><00:02:24.519><c> be</c>

00:02:24.670 --> 00:02:24.680 align:start position:0%
known security vulnerability can now be
 

00:02:24.680 --> 00:02:26.750 align:start position:0%
known security vulnerability can now be
enabled<00:02:25.040><c> on</c><00:02:25.160><c> repos</c><00:02:25.599><c> or</c><00:02:25.760><c> organizations</c><00:02:26.400><c> to</c><00:02:26.560><c> run</c>

00:02:26.750 --> 00:02:26.760 align:start position:0%
enabled on repos or organizations to run
 

00:02:26.760 --> 00:02:29.430 align:start position:0%
enabled on repos or organizations to run
dependabot<00:02:27.480><c> update</c><00:02:27.879><c> jobs</c><00:02:28.680><c> as</c><00:02:28.840><c> a</c><00:02:29.040><c> GitHub</c>

00:02:29.430 --> 00:02:29.440 align:start position:0%
dependabot update jobs as a GitHub
 

00:02:29.440 --> 00:02:31.589 align:start position:0%
dependabot update jobs as a GitHub
action<00:02:30.040><c> both</c><00:02:30.239><c> for</c><00:02:30.440><c> hosted</c><00:02:30.879><c> and</c><00:02:31.000><c> self-hosted</c>

00:02:31.589 --> 00:02:31.599 align:start position:0%
action both for hosted and self-hosted
 

00:02:31.599 --> 00:02:33.190 align:start position:0%
action both for hosted and self-hosted
Runners<00:02:32.239><c> and</c><00:02:32.400><c> even</c><00:02:32.640><c> better</c><00:02:32.920><c> running</c>

00:02:33.190 --> 00:02:33.200 align:start position:0%
Runners and even better running
 

00:02:33.200 --> 00:02:35.470 align:start position:0%
Runners and even better running
dependabot<00:02:33.879><c> does</c><00:02:34.120><c> not</c><00:02:34.440><c> count</c><00:02:34.680><c> towards</c><00:02:35.280><c> your</c>

00:02:35.470 --> 00:02:35.480 align:start position:0%
dependabot does not count towards your
 

00:02:35.480 --> 00:02:37.070 align:start position:0%
dependabot does not count towards your
GitHub<00:02:35.840><c> actions</c><00:02:36.120><c> minutes</c><00:02:36.519><c> meaning</c><00:02:36.879><c> that</c>

00:02:37.070 --> 00:02:37.080 align:start position:0%
GitHub actions minutes meaning that
 

00:02:37.080 --> 00:02:38.869 align:start position:0%
GitHub actions minutes meaning that
using<00:02:37.400><c> dependabot</c><00:02:38.040><c> continues</c><00:02:38.400><c> to</c><00:02:38.519><c> be</c><00:02:38.640><c> free</c>

00:02:38.869 --> 00:02:38.879 align:start position:0%
using dependabot continues to be free
 

00:02:38.879 --> 00:02:40.710 align:start position:0%
using dependabot continues to be free
for<00:02:39.080><c> everybody</c><00:02:40.000><c> uh</c><00:02:40.120><c> but</c><00:02:40.239><c> what's</c><00:02:40.400><c> great</c><00:02:40.599><c> about</c>

00:02:40.710 --> 00:02:40.720 align:start position:0%
for everybody uh but what's great about
 

00:02:40.720 --> 00:02:42.190 align:start position:0%
for everybody uh but what's great about
moving<00:02:41.000><c> to</c><00:02:41.120><c> the</c><00:02:41.280><c> actions</c><00:02:41.640><c> is</c><00:02:41.760><c> that</c><00:02:41.879><c> this</c><00:02:42.000><c> is</c>

00:02:42.190 --> 00:02:42.200 align:start position:0%
moving to the actions is that this is
 

00:02:42.200 --> 00:02:43.990 align:start position:0%
moving to the actions is that this is
really<00:02:42.400><c> good</c><00:02:42.519><c> for</c><00:02:42.680><c> anyone</c><00:02:43.000><c> who</c><00:02:43.159><c> has</c><00:02:43.560><c> some</c><00:02:43.760><c> on</c>

00:02:43.990 --> 00:02:44.000 align:start position:0%
really good for anyone who has some on
 

00:02:44.000 --> 00:02:45.470 align:start position:0%
really good for anyone who has some on
premise<00:02:44.360><c> resources</c><00:02:44.800><c> like</c><00:02:44.959><c> private</c><00:02:45.400><c> uh</c>

00:02:45.470 --> 00:02:45.480 align:start position:0%
premise resources like private uh
 

00:02:45.480 --> 00:02:47.350 align:start position:0%
premise resources like private uh
Registries<00:02:46.319><c> that</c><00:02:46.519><c> previously</c><00:02:47.000><c> were</c><00:02:47.200><c> not</c>

00:02:47.350 --> 00:02:47.360 align:start position:0%
Registries that previously were not
 

00:02:47.360 --> 00:02:49.270 align:start position:0%
Registries that previously were not
visible<00:02:47.640><c> to</c><00:02:47.800><c> dependabot</c><00:02:48.680><c> and</c><00:02:48.760><c> it</c><00:02:48.879><c> also</c><00:02:49.080><c> means</c>

00:02:49.270 --> 00:02:49.280 align:start position:0%
visible to dependabot and it also means
 

00:02:49.280 --> 00:02:51.070 align:start position:0%
visible to dependabot and it also means
you<00:02:49.400><c> can</c><00:02:49.560><c> script</c><00:02:50.040><c> dependabot</c><00:02:50.640><c> into</c><00:02:50.800><c> your</c><00:02:50.920><c> own</c>

00:02:51.070 --> 00:02:51.080 align:start position:0%
you can script dependabot into your own
 

00:02:51.080 --> 00:02:52.830 align:start position:0%
you can script dependabot into your own
workflows<00:02:51.519><c> and</c><00:02:51.680><c> pipelines</c><00:02:52.480><c> and</c><00:02:52.599><c> I've</c><00:02:52.680><c> got</c>

00:02:52.830 --> 00:02:52.840 align:start position:0%
workflows and pipelines and I've got
 

00:02:52.840 --> 00:02:54.470 align:start position:0%
workflows and pipelines and I've got
more<00:02:53.000><c> details</c><00:02:53.360><c> about</c><00:02:53.560><c> how</c><00:02:53.680><c> all</c><00:02:53.879><c> this</c><00:02:54.040><c> works</c><00:02:54.360><c> in</c>

00:02:54.470 --> 00:02:54.480 align:start position:0%
more details about how all this works in
 

00:02:54.480 --> 00:02:56.430 align:start position:0%
more details about how all this works in
the<00:02:54.599><c> blog</c><00:02:54.920><c> post</c><00:02:55.239><c> link</c><00:02:55.480><c> Down</c><00:02:55.640><c> Below</c><00:02:55.879><c> in</c><00:02:56.000><c> the</c>

00:02:56.430 --> 00:02:56.440 align:start position:0%
the blog post link Down Below in the
 

00:02:56.440 --> 00:02:58.910 align:start position:0%
the blog post link Down Below in the
description<00:02:57.440><c> okay</c><00:02:57.800><c> so</c><00:02:58.040><c> a</c><00:02:58.120><c> few</c><00:02:58.280><c> weeks</c><00:02:58.519><c> ago</c><00:02:58.760><c> we</c>

00:02:58.910 --> 00:02:58.920 align:start position:0%
description okay so a few weeks ago we
 

00:02:58.920 --> 00:03:01.830 align:start position:0%
description okay so a few weeks ago we
talked<00:02:59.159><c> about</c><00:02:59.360><c> some</c><00:02:59.519><c> new</c><00:03:00.040><c> open</c><00:03:00.680><c> AI</c><00:03:01.000><c> models</c><00:03:01.680><c> and</c>

00:03:01.830 --> 00:03:01.840 align:start position:0%
talked about some new open AI models and
 

00:03:01.840 --> 00:03:04.030 align:start position:0%
talked about some new open AI models and
of<00:03:02.000><c> course</c><00:03:02.920><c> right</c><00:03:03.080><c> after</c><00:03:03.280><c> we</c><00:03:03.440><c> filmed</c>

00:03:04.030 --> 00:03:04.040 align:start position:0%
of course right after we filmed
 

00:03:04.040 --> 00:03:06.750 align:start position:0%
of course right after we filmed
Microsoft<00:03:04.519><c> released</c><00:03:04.959><c> its</c><00:03:05.239><c> new</c><00:03:05.640><c> 53</c><00:03:06.440><c> mini</c>

00:03:06.750 --> 00:03:06.760 align:start position:0%
Microsoft released its new 53 mini
 

00:03:06.760 --> 00:03:08.789 align:start position:0%
Microsoft released its new 53 mini
family<00:03:07.080><c> of</c><00:03:07.239><c> open</c><00:03:07.440><c> models</c><00:03:07.959><c> and</c><00:03:08.280><c> what's</c><00:03:08.560><c> really</c>

00:03:08.789 --> 00:03:08.799 align:start position:0%
family of open models and what's really
 

00:03:08.799 --> 00:03:10.670 align:start position:0%
family of open models and what's really
great<00:03:09.000><c> about</c><00:03:09.280><c> the</c><00:03:09.599><c> about</c><00:03:09.879><c> 53</c><00:03:10.319><c> is</c><00:03:10.440><c> that</c><00:03:10.560><c> these</c>

00:03:10.670 --> 00:03:10.680 align:start position:0%
great about the about 53 is that these
 

00:03:10.680 --> 00:03:13.110 align:start position:0%
great about the about 53 is that these
are<00:03:10.840><c> specifically</c><00:03:11.319><c> designed</c><00:03:12.319><c> um</c><00:03:12.760><c> smaller</c>

00:03:13.110 --> 00:03:13.120 align:start position:0%
are specifically designed um smaller
 

00:03:13.120 --> 00:03:14.390 align:start position:0%
are specifically designed um smaller
language<00:03:13.519><c> models</c><00:03:13.840><c> meaning</c><00:03:14.120><c> that</c><00:03:14.239><c> they're</c>

00:03:14.390 --> 00:03:14.400 align:start position:0%
language models meaning that they're
 

00:03:14.400 --> 00:03:17.149 align:start position:0%
language models meaning that they're
optimized<00:03:14.959><c> to</c><00:03:15.080><c> run</c><00:03:15.360><c> on</c><00:03:15.519><c> local</c><00:03:15.840><c> devices</c><00:03:16.799><c> and</c><00:03:16.959><c> to</c>

00:03:17.149 --> 00:03:17.159 align:start position:0%
optimized to run on local devices and to
 

00:03:17.159 --> 00:03:19.670 align:start position:0%
optimized to run on local devices and to
Cost<00:03:17.440><c> Less</c><00:03:17.720><c> when</c><00:03:17.920><c> they</c><00:03:18.080><c> run</c><00:03:18.280><c> in</c><00:03:18.400><c> the</c><00:03:18.519><c> cloud</c><00:03:19.360><c> and</c>

00:03:19.670 --> 00:03:19.680 align:start position:0%
Cost Less when they run in the cloud and
 

00:03:19.680 --> 00:03:21.710 align:start position:0%
Cost Less when they run in the cloud and
53<00:03:20.120><c> mini</c><00:03:20.400><c> is</c><00:03:20.519><c> available</c><00:03:20.879><c> in</c><00:03:21.040><c> two</c><00:03:21.319><c> context</c>

00:03:21.710 --> 00:03:21.720 align:start position:0%
53 mini is available in two context
 

00:03:21.720 --> 00:03:25.630 align:start position:0%
53 mini is available in two context
lengths<00:03:22.200><c> right</c><00:03:22.319><c> now</c><00:03:22.959><c> uh</c><00:03:23.080><c> 4K</c><00:03:23.720><c> and</c><00:03:23.879><c> 128k</c><00:03:24.720><c> tokens</c>

00:03:25.630 --> 00:03:25.640 align:start position:0%
lengths right now uh 4K and 128k tokens
 

00:03:25.640 --> 00:03:27.630 align:start position:0%
lengths right now uh 4K and 128k tokens
and<00:03:25.920><c> as</c><00:03:26.000><c> I</c><00:03:26.159><c> said</c><00:03:26.400><c> you</c><00:03:26.480><c> can</c><00:03:26.879><c> uh</c><00:03:27.000><c> run</c><00:03:27.159><c> it</c><00:03:27.280><c> locally</c>

00:03:27.630 --> 00:03:27.640 align:start position:0%
and as I said you can uh run it locally
 

00:03:27.640 --> 00:03:29.869 align:start position:0%
and as I said you can uh run it locally
you<00:03:27.720><c> can</c><00:03:27.840><c> run</c><00:03:28.000><c> it</c><00:03:28.080><c> on</c><00:03:28.239><c> Azure</c><00:03:28.560><c> AI</c><00:03:28.840><c> Studio</c>

00:03:29.869 --> 00:03:29.879 align:start position:0%
you can run it on Azure AI Studio
 

00:03:29.879 --> 00:03:32.470 align:start position:0%
you can run it on Azure AI Studio
through<00:03:30.040><c> hugging</c><00:03:30.360><c> face</c><00:03:30.680><c> or</c><00:03:30.920><c> via</c><00:03:31.239><c> AMA</c><00:03:32.239><c> and</c>

00:03:32.470 --> 00:03:32.480 align:start position:0%
through hugging face or via AMA and
 

00:03:32.480 --> 00:03:33.990 align:start position:0%
through hugging face or via AMA and
because<00:03:32.680><c> it's</c><00:03:32.840><c> an</c><00:03:32.959><c> open</c><00:03:33.159><c> model</c><00:03:33.560><c> it's</c><00:03:33.760><c> already</c>

00:03:33.990 --> 00:03:34.000 align:start position:0%
because it's an open model it's already
 

00:03:34.000 --> 00:03:36.149 align:start position:0%
because it's an open model it's already
been<00:03:34.239><c> adapted</c><00:03:34.760><c> by</c><00:03:35.280><c> uh</c><00:03:35.480><c> a</c><00:03:35.599><c> bunch</c><00:03:35.760><c> of</c><00:03:35.920><c> other</c>

00:03:36.149 --> 00:03:36.159 align:start position:0%
been adapted by uh a bunch of other
 

00:03:36.159 --> 00:03:38.229 align:start position:0%
been adapted by uh a bunch of other
projects<00:03:36.519><c> and</c><00:03:36.640><c> tooling</c><00:03:37.000><c> too</c><00:03:37.760><c> there's</c><00:03:38.080><c> some</c>

00:03:38.229 --> 00:03:38.239 align:start position:0%
projects and tooling too there's some
 

00:03:38.239 --> 00:03:39.390 align:start position:0%
projects and tooling too there's some
really<00:03:38.480><c> great</c><00:03:38.799><c> uh</c><00:03:38.920><c> stuff</c><00:03:39.120><c> that</c><00:03:39.200><c> I've</c><00:03:39.319><c> got</c>

00:03:39.390 --> 00:03:39.400 align:start position:0%
really great uh stuff that I've got
 

00:03:39.400 --> 00:03:41.110 align:start position:0%
really great uh stuff that I've got
linked<00:03:39.680><c> to</c><00:03:39.799><c> in</c><00:03:39.879><c> the</c><00:03:40.000><c> announcement</c><00:03:40.400><c> Blog</c><00:03:41.000><c> the</c>

00:03:41.110 --> 00:03:41.120 align:start position:0%
linked to in the announcement Blog the
 

00:03:41.120 --> 00:03:43.070 align:start position:0%
linked to in the announcement Blog the
model<00:03:41.400><c> cards</c><00:03:41.640><c> on</c><00:03:41.799><c> hugging</c><00:03:42.159><c> face</c><00:03:42.760><c> and</c><00:03:42.879><c> there's</c>

00:03:43.070 --> 00:03:43.080 align:start position:0%
model cards on hugging face and there's
 

00:03:43.080 --> 00:03:44.470 align:start position:0%
model cards on hugging face and there's
also<00:03:43.239><c> a</c><00:03:43.360><c> cool</c><00:03:43.640><c> project</c><00:03:44.040><c> called</c><00:03:44.360><c> uh</c>

00:03:44.470 --> 00:03:44.480 align:start position:0%
also a cool project called uh
 

00:03:44.480 --> 00:03:46.589 align:start position:0%
also a cool project called uh
transformers.<00:03:45.200><c> JS</c><00:03:45.920><c> which</c><00:03:46.040><c> lets</c><00:03:46.239><c> you</c><00:03:46.360><c> run</c>

00:03:46.589 --> 00:03:46.599 align:start position:0%
transformers. JS which lets you run
 

00:03:46.599 --> 00:03:48.149 align:start position:0%
transformers. JS which lets you run
Transformers<00:03:47.159><c> directly</c><00:03:47.439><c> in</c><00:03:47.519><c> the</c><00:03:47.640><c> browser</c><00:03:48.000><c> no</c>

00:03:48.149 --> 00:03:48.159 align:start position:0%
Transformers directly in the browser no
 

00:03:48.159 --> 00:03:50.190 align:start position:0%
Transformers directly in the browser no
server<00:03:48.480><c> required</c><00:03:49.280><c> and</c><00:03:49.480><c> someone</c><00:03:49.840><c> has</c><00:03:49.959><c> already</c>

00:03:50.190 --> 00:03:50.200 align:start position:0%
server required and someone has already
 

00:03:50.200 --> 00:03:52.550 align:start position:0%
server required and someone has already
updated<00:03:50.599><c> that</c><00:03:50.720><c> to</c><00:03:50.920><c> to</c><00:03:51.040><c> work</c><00:03:51.200><c> with</c><00:03:51.319><c> 53</c><00:03:52.200><c> now</c><00:03:52.400><c> I've</c>

00:03:52.550 --> 00:03:52.560 align:start position:0%
updated that to to work with 53 now I've
 

00:03:52.560 --> 00:03:54.750 align:start position:0%
updated that to to work with 53 now I've
played<00:03:52.840><c> with</c><00:03:53.000><c> 53</c><00:03:53.439><c> a</c><00:03:53.560><c> bit</c><00:03:53.840><c> and</c><00:03:53.920><c> it</c><00:03:54.040><c> is</c><00:03:54.439><c> really</c>

00:03:54.750 --> 00:03:54.760 align:start position:0%
played with 53 a bit and it is really
 

00:03:54.760 --> 00:03:56.750 align:start position:0%
played with 53 a bit and it is really
impressive<00:03:55.280><c> what</c><00:03:55.400><c> it</c><00:03:55.519><c> can</c><00:03:55.640><c> do</c><00:03:56.360><c> with</c><00:03:56.480><c> such</c><00:03:56.640><c> a</c>

00:03:56.750 --> 00:03:56.760 align:start position:0%
impressive what it can do with such a
 

00:03:56.760 --> 00:03:59.429 align:start position:0%
impressive what it can do with such a
small<00:03:56.959><c> model</c><00:03:57.560><c> especially</c><00:03:57.959><c> on</c><00:03:58.120><c> local</c><00:03:58.439><c> machines</c>

00:03:59.429 --> 00:03:59.439 align:start position:0%
small model especially on local machines
 

00:03:59.439 --> 00:04:01.509 align:start position:0%
small model especially on local machines
and<00:04:00.120><c> as</c><00:04:00.200><c> I</c><00:04:00.319><c> said</c><00:04:00.439><c> all</c><00:04:00.560><c> the</c><00:04:00.879><c> details</c><00:04:01.159><c> are</c><00:04:01.280><c> linked</c>

00:04:01.509 --> 00:04:01.519 align:start position:0%
and as I said all the details are linked
 

00:04:01.519 --> 00:04:03.670 align:start position:0%
and as I said all the details are linked
down<00:04:01.680><c> below</c><00:04:02.640><c> now</c><00:04:02.799><c> moving</c><00:04:03.000><c> on</c><00:04:03.120><c> to</c><00:04:03.239><c> some</c><00:04:03.400><c> font</c>

00:04:03.670 --> 00:04:03.680 align:start position:0%
down below now moving on to some font
 

00:04:03.680 --> 00:04:06.350 align:start position:0%
down below now moving on to some font
news<00:04:04.599><c> Cascadia</c><00:04:05.200><c> code</c><00:04:05.519><c> the</c><00:04:05.599><c> open</c><00:04:05.840><c> source</c><00:04:06.079><c> font</c>

00:04:06.350 --> 00:04:06.360 align:start position:0%
news Cascadia code the open source font
 

00:04:06.360 --> 00:04:08.030 align:start position:0%
news Cascadia code the open source font
that<00:04:06.480><c> Microsoft</c><00:04:06.959><c> first</c><00:04:07.159><c> released</c><00:04:07.640><c> as</c><00:04:07.799><c> part</c><00:04:07.920><c> of</c>

00:04:08.030 --> 00:04:08.040 align:start position:0%
that Microsoft first released as part of
 

00:04:08.040 --> 00:04:10.110 align:start position:0%
that Microsoft first released as part of
Windows<00:04:08.400><c> terminal</c><00:04:09.400><c> um</c><00:04:09.560><c> and</c><00:04:09.640><c> it's</c><00:04:09.799><c> become</c><00:04:10.040><c> one</c>

00:04:10.110 --> 00:04:10.120 align:start position:0%
Windows terminal um and it's become one
 

00:04:10.120 --> 00:04:11.830 align:start position:0%
Windows terminal um and it's become one
of<00:04:10.200><c> my</c><00:04:10.319><c> favorite</c><00:04:10.599><c> monospace</c><00:04:11.079><c> fonts</c><00:04:11.680><c> it</c>

00:04:11.830 --> 00:04:11.840 align:start position:0%
of my favorite monospace fonts it
 

00:04:11.840 --> 00:04:13.390 align:start position:0%
of my favorite monospace fonts it
recently<00:04:12.360><c> received</c><00:04:12.519><c> a</c><00:04:12.640><c> huge</c><00:04:12.840><c> update</c><00:04:13.200><c> the</c>

00:04:13.390 --> 00:04:13.400 align:start position:0%
recently received a huge update the
 

00:04:13.400 --> 00:04:15.270 align:start position:0%
recently received a huge update the
first<00:04:13.680><c> update</c><00:04:13.959><c> in</c><00:04:14.040><c> more</c><00:04:14.200><c> than</c><00:04:14.360><c> three</c><00:04:14.640><c> years</c>

00:04:15.270 --> 00:04:15.280 align:start position:0%
first update in more than three years
 

00:04:15.280 --> 00:04:16.990 align:start position:0%
first update in more than three years
and<00:04:15.439><c> this</c><00:04:15.599><c> update</c><00:04:15.959><c> adds</c><00:04:16.199><c> in</c><00:04:16.359><c> some</c><00:04:16.600><c> great</c><00:04:16.799><c> new</c>

00:04:16.990 --> 00:04:17.000 align:start position:0%
and this update adds in some great new
 

00:04:17.000 --> 00:04:18.670 align:start position:0%
and this update adds in some great new
features<00:04:17.359><c> including</c><00:04:17.759><c> support</c><00:04:18.040><c> for</c><00:04:18.199><c> quadrants</c>

00:04:18.670 --> 00:04:18.680 align:start position:0%
features including support for quadrants
 

00:04:18.680 --> 00:04:22.150 align:start position:0%
features including support for quadrants
and<00:04:18.840><c> seant</c><00:04:19.720><c> and</c><00:04:20.079><c> octants</c><00:04:21.079><c> um</c><00:04:21.239><c> and</c><00:04:21.359><c> the</c><00:04:21.440><c> tldr</c><00:04:21.959><c> on</c>

00:04:22.150 --> 00:04:22.160 align:start position:0%
and seant and octants um and the tldr on
 

00:04:22.160 --> 00:04:23.670 align:start position:0%
and seant and octants um and the tldr on
on<00:04:22.320><c> on</c><00:04:22.520><c> those</c><00:04:22.880><c> is</c><00:04:23.000><c> that</c><00:04:23.120><c> they're</c><00:04:23.320><c> characters</c>

00:04:23.670 --> 00:04:23.680 align:start position:0%
on on those is that they're characters
 

00:04:23.680 --> 00:04:25.150 align:start position:0%
on on those is that they're characters
that<00:04:23.800><c> can</c><00:04:23.880><c> be</c><00:04:23.960><c> used</c><00:04:24.160><c> to</c><00:04:24.240><c> generate</c><00:04:24.759><c> Graphics</c><00:04:25.080><c> in</c>

00:04:25.150 --> 00:04:25.160 align:start position:0%
that can be used to generate Graphics in
 

00:04:25.160 --> 00:04:27.870 align:start position:0%
that can be used to generate Graphics in
the<00:04:25.280><c> browser</c><00:04:26.080><c> see</c><00:04:26.320><c> this</c><00:04:26.479><c> fun</c><00:04:26.720><c> example</c><00:04:27.560><c> um</c><00:04:27.800><c> as</c>

00:04:27.870 --> 00:04:27.880 align:start position:0%
the browser see this fun example um as
 

00:04:27.880 --> 00:04:29.710 align:start position:0%
the browser see this fun example um as
well<00:04:28.040><c> as</c><00:04:28.120><c> some</c><00:04:28.280><c> large</c><00:04:28.520><c> tyght</c><00:04:28.800><c> pieces</c><00:04:29.199><c> for</c>

00:04:29.710 --> 00:04:29.720 align:start position:0%
well as some large tyght pieces for
 

00:04:29.720 --> 00:04:31.710 align:start position:0%
well as some large tyght pieces for
better<00:04:30.039><c> terminal</c><00:04:30.400><c> customization</c><00:04:31.400><c> but</c><00:04:31.560><c> the</c>

00:04:31.710 --> 00:04:31.720 align:start position:0%
better terminal customization but the
 

00:04:31.720 --> 00:04:33.350 align:start position:0%
better terminal customization but the
biggest<00:04:32.080><c> news</c><00:04:32.520><c> is</c><00:04:32.639><c> that</c><00:04:32.759><c> there's</c><00:04:33.000><c> now</c>

00:04:33.350 --> 00:04:33.360 align:start position:0%
biggest news is that there's now
 

00:04:33.360 --> 00:04:35.670 align:start position:0%
biggest news is that there's now
Official<00:04:33.720><c> Nerd</c><00:04:34.080><c> font</c><00:04:34.400><c> variants</c><00:04:34.919><c> of</c><00:04:35.120><c> Cascadia</c>

00:04:35.670 --> 00:04:35.680 align:start position:0%
Official Nerd font variants of Cascadia
 

00:04:35.680 --> 00:04:37.270 align:start position:0%
Official Nerd font variants of Cascadia
code<00:04:36.240><c> which</c><00:04:36.360><c> is</c><00:04:36.479><c> the</c><00:04:36.600><c> standard</c><00:04:36.919><c> font</c><00:04:37.160><c> that</c>

00:04:37.270 --> 00:04:37.280 align:start position:0%
code which is the standard font that
 

00:04:37.280 --> 00:04:39.990 align:start position:0%
code which is the standard font that
includes<00:04:37.680><c> code</c><00:04:37.960><c> L</c><00:04:38.280><c> ligatures</c><00:04:39.160><c> and</c><00:04:39.400><c> Cascadia</c>

00:04:39.990 --> 00:04:40.000 align:start position:0%
includes code L ligatures and Cascadia
 

00:04:40.000 --> 00:04:42.550 align:start position:0%
includes code L ligatures and Cascadia
mono<00:04:40.759><c> which</c><00:04:40.919><c> is</c><00:04:41.160><c> just</c><00:04:41.280><c> the</c><00:04:41.400><c> mono</c><00:04:41.720><c> space</c><00:04:41.960><c> style</c>

00:04:42.550 --> 00:04:42.560 align:start position:0%
mono which is just the mono space style
 

00:04:42.560 --> 00:04:43.870 align:start position:0%
mono which is just the mono space style
and<00:04:42.680><c> that</c><00:04:42.800><c> means</c><00:04:43.280><c> that</c><00:04:43.400><c> you</c><00:04:43.520><c> don't</c><00:04:43.639><c> have</c><00:04:43.759><c> to</c>

00:04:43.870 --> 00:04:43.880 align:start position:0%
and that means that you don't have to
 

00:04:43.880 --> 00:04:45.670 align:start position:0%
and that means that you don't have to
patch<00:04:44.160><c> the</c><00:04:44.240><c> nerd</c><00:04:44.520><c> font</c><00:04:44.759><c> variants</c><00:04:45.160><c> anymore</c>

00:04:45.670 --> 00:04:45.680 align:start position:0%
patch the nerd font variants anymore
 

00:04:45.680 --> 00:04:47.790 align:start position:0%
patch the nerd font variants anymore
it's<00:04:45.919><c> actually</c><00:04:46.240><c> like</c><00:04:46.520><c> part</c><00:04:46.639><c> of</c><00:04:46.759><c> the</c><00:04:46.880><c> glyph</c><00:04:47.199><c> set</c>

00:04:47.790 --> 00:04:47.800 align:start position:0%
it's actually like part of the glyph set
 

00:04:47.800 --> 00:04:51.430 align:start position:0%
it's actually like part of the glyph set
and<00:04:47.960><c> so</c><00:04:48.560><c> I</c><00:04:48.720><c> love</c><00:04:49.039><c> this</c><00:04:49.520><c> I</c><00:04:49.639><c> love</c><00:04:49.840><c> Cascadia</c><00:04:50.440><c> code</c>

00:04:51.430 --> 00:04:51.440 align:start position:0%
and so I love this I love Cascadia code
 

00:04:51.440 --> 00:04:53.670 align:start position:0%
and so I love this I love Cascadia code
um<00:04:51.639><c> I</c><00:04:51.759><c> actually</c><00:04:51.919><c> wrote</c><00:04:52.160><c> the</c><00:04:52.320><c> script</c><00:04:53.199><c> uh</c><00:04:53.280><c> using</c>

00:04:53.670 --> 00:04:53.680 align:start position:0%
um I actually wrote the script uh using
 

00:04:53.680 --> 00:04:56.469 align:start position:0%
um I actually wrote the script uh using
C<00:04:54.199><c> code</c><00:04:54.400><c> n</c><00:04:54.560><c> vs</c><00:04:54.840><c> code</c><00:04:55.600><c> uh</c><00:04:55.759><c> and</c><00:04:56.039><c> I've</c><00:04:56.120><c> got</c><00:04:56.240><c> links</c>

00:04:56.469 --> 00:04:56.479 align:start position:0%
C code n vs code uh and I've got links
 

00:04:56.479 --> 00:04:58.790 align:start position:0%
C code n vs code uh and I've got links
to<00:04:56.720><c> that</c><00:04:57.120><c> um</c><00:04:57.759><c> in</c><00:04:57.919><c> their</c><00:04:58.080><c> GitHub</c><00:04:58.360><c> repo</c><00:04:58.639><c> down</c>

00:04:58.790 --> 00:04:58.800 align:start position:0%
to that um in their GitHub repo down
 

00:04:58.800 --> 00:05:00.909 align:start position:0%
to that um in their GitHub repo down
below<00:04:59.720><c> and</c><00:04:59.800><c> now</c><00:04:59.919><c> it</c><00:05:00.000><c> is</c><00:05:00.120><c> time</c><00:05:00.240><c> for</c><00:05:00.360><c> my</c><00:05:00.479><c> GitHub</c>

00:05:00.909 --> 00:05:00.919 align:start position:0%
below and now it is time for my GitHub
 

00:05:00.919 --> 00:05:02.590 align:start position:0%
below and now it is time for my GitHub
project<00:05:01.280><c> Spotlight</c><00:05:01.919><c> and</c><00:05:02.080><c> this</c><00:05:02.160><c> is</c><00:05:02.280><c> where</c><00:05:02.440><c> I</c>

00:05:02.590 --> 00:05:02.600 align:start position:0%
project Spotlight and this is where I
 

00:05:02.600 --> 00:05:04.029 align:start position:0%
project Spotlight and this is where I
highlight<00:05:02.960><c> a</c><00:05:03.160><c> great</c><00:05:03.479><c> project</c><00:05:03.759><c> from</c><00:05:03.919><c> the</c>

00:05:04.029 --> 00:05:04.039 align:start position:0%
highlight a great project from the
 

00:05:04.039 --> 00:05:06.070 align:start position:0%
highlight a great project from the
community<00:05:04.800><c> and</c><00:05:04.960><c> this</c><00:05:05.120><c> time</c><00:05:05.360><c> I</c><00:05:05.440><c> want</c><00:05:05.560><c> to</c><00:05:05.759><c> call</c>

00:05:06.070 --> 00:05:06.080 align:start position:0%
community and this time I want to call
 

00:05:06.080 --> 00:05:08.310 align:start position:0%
community and this time I want to call
out<00:05:06.440><c> a</c><00:05:06.639><c> project</c><00:05:06.919><c> called</c><00:05:07.080><c> secret</c><00:05:07.440><c> llama</c><00:05:08.199><c> and</c>

00:05:08.310 --> 00:05:08.320 align:start position:0%
out a project called secret llama and
 

00:05:08.320 --> 00:05:10.710 align:start position:0%
out a project called secret llama and
this<00:05:08.440><c> is</c><00:05:08.560><c> a</c><00:05:08.720><c> fully</c><00:05:09.120><c> private</c><00:05:09.560><c> llm</c><00:05:10.080><c> chatbot</c><00:05:10.600><c> that</c>

00:05:10.710 --> 00:05:10.720 align:start position:0%
this is a fully private llm chatbot that
 

00:05:10.720 --> 00:05:12.550 align:start position:0%
this is a fully private llm chatbot that
runs<00:05:11.000><c> entirely</c><00:05:11.400><c> in</c><00:05:11.520><c> the</c><00:05:11.600><c> browser</c><00:05:12.320><c> and</c><00:05:12.400><c> it</c>

00:05:12.550 --> 00:05:12.560 align:start position:0%
runs entirely in the browser and it
 

00:05:12.560 --> 00:05:14.790 align:start position:0%
runs entirely in the browser and it
supports<00:05:12.919><c> mistol</c><00:05:13.400><c> and</c><00:05:13.600><c> llama</c><00:05:13.960><c> 3</c><00:05:14.520><c> and</c><00:05:14.639><c> even</c>

00:05:14.790 --> 00:05:14.800 align:start position:0%
supports mistol and llama 3 and even
 

00:05:14.800 --> 00:05:16.350 align:start position:0%
supports mistol and llama 3 and even
better<00:05:15.039><c> the</c><00:05:15.120><c> Creator</c><00:05:15.440><c> says</c><00:05:15.800><c> that</c><00:05:16.000><c> those</c><00:05:16.160><c> new</c>

00:05:16.350 --> 00:05:16.360 align:start position:0%
better the Creator says that those new
 

00:05:16.360 --> 00:05:18.350 align:start position:0%
better the Creator says that those new
53<00:05:16.840><c> mini</c><00:05:17.199><c> models</c><00:05:17.520><c> that</c><00:05:17.600><c> we</c><00:05:17.720><c> talked</c><00:05:17.960><c> about</c>

00:05:18.350 --> 00:05:18.360 align:start position:0%
53 mini models that we talked about
 

00:05:18.360 --> 00:05:20.510 align:start position:0%
53 mini models that we talked about
earlier<00:05:18.759><c> are</c><00:05:18.919><c> going</c><00:05:19.039><c> to</c><00:05:19.120><c> be</c><00:05:19.280><c> added</c><00:05:19.560><c> soon</c><00:05:20.280><c> maybe</c>

00:05:20.510 --> 00:05:20.520 align:start position:0%
earlier are going to be added soon maybe
 

00:05:20.520 --> 00:05:22.430 align:start position:0%
earlier are going to be added soon maybe
even<00:05:20.720><c> by</c><00:05:20.840><c> the</c><00:05:20.919><c> time</c><00:05:21.039><c> you</c><00:05:21.160><c> see</c><00:05:21.400><c> this</c><00:05:22.120><c> and</c><00:05:22.360><c> you</c>

00:05:22.430 --> 00:05:22.440 align:start position:0%
even by the time you see this and you
 

00:05:22.440 --> 00:05:24.309 align:start position:0%
even by the time you see this and you
can<00:05:22.600><c> run</c><00:05:22.759><c> it</c><00:05:22.880><c> in</c><00:05:23.000><c> your</c><00:05:23.120><c> browser</c><00:05:23.680><c> from</c><00:05:23.919><c> Secret</c>

00:05:24.309 --> 00:05:24.319 align:start position:0%
can run it in your browser from Secret
 

00:05:24.319 --> 00:05:26.390 align:start position:0%
can run it in your browser from Secret
llama.com<00:05:24.880><c> uh</c><00:05:25.639><c> the</c><00:05:25.759><c> first</c><00:05:25.919><c> time</c><00:05:26.039><c> it</c><00:05:26.160><c> loads</c>

00:05:26.390 --> 00:05:26.400 align:start position:0%
llama.com uh the first time it loads
 

00:05:26.400 --> 00:05:27.749 align:start position:0%
llama.com uh the first time it loads
it's<00:05:26.520><c> going</c><00:05:26.639><c> to</c><00:05:26.759><c> take</c><00:05:26.919><c> some</c><00:05:27.120><c> time</c><00:05:27.280><c> to</c><00:05:27.440><c> download</c>

00:05:27.749 --> 00:05:27.759 align:start position:0%
it's going to take some time to download
 

00:05:27.759 --> 00:05:30.230 align:start position:0%
it's going to take some time to download
the<00:05:27.840><c> model</c><00:05:28.479><c> in</c><00:05:28.600><c> your</c><00:05:28.720><c> browser</c><00:05:29.520><c> um</c><00:05:29.800><c> um</c><00:05:30.039><c> but</c><00:05:30.160><c> of</c>

00:05:30.230 --> 00:05:30.240 align:start position:0%
the model in your browser um um but of
 

00:05:30.240 --> 00:05:31.990 align:start position:0%
the model in your browser um um but of
course<00:05:30.400><c> you</c><00:05:30.479><c> can</c><00:05:30.560><c> still</c><00:05:30.800><c> post</c><00:05:31.039><c> this</c><00:05:31.160><c> too</c><00:05:31.840><c> I</c>

00:05:31.990 --> 00:05:32.000 align:start position:0%
course you can still post this too I
 

00:05:32.000 --> 00:05:33.749 align:start position:0%
course you can still post this too I
love<00:05:32.240><c> projects</c><00:05:32.639><c> like</c><00:05:32.840><c> this</c><00:05:33.120><c> because</c><00:05:33.319><c> it's</c><00:05:33.520><c> a</c>

00:05:33.749 --> 00:05:33.759 align:start position:0%
love projects like this because it's a
 

00:05:33.759 --> 00:05:35.629 align:start position:0%
love projects like this because it's a
really<00:05:34.160><c> great</c><00:05:34.400><c> proof</c><00:05:34.600><c> of</c><00:05:34.759><c> concept</c><00:05:35.280><c> uh</c><00:05:35.479><c> and</c>

00:05:35.629 --> 00:05:35.639 align:start position:0%
really great proof of concept uh and
 

00:05:35.639 --> 00:05:37.390 align:start position:0%
really great proof of concept uh and
marrying<00:05:36.000><c> kind</c><00:05:36.080><c> of</c><00:05:36.240><c> the</c><00:05:36.319><c> best</c><00:05:36.520><c> of</c><00:05:36.880><c> these</c><00:05:37.080><c> open</c>

00:05:37.390 --> 00:05:37.400 align:start position:0%
marrying kind of the best of these open
 

00:05:37.400 --> 00:05:39.749 align:start position:0%
marrying kind of the best of these open
source<00:05:37.880><c> you</c><00:05:38.000><c> know</c><00:05:38.199><c> AI</c><00:05:38.479><c> models</c><00:05:39.319><c> as</c><00:05:39.440><c> well</c><00:05:39.600><c> as</c>

00:05:39.749 --> 00:05:39.759 align:start position:0%
source you know AI models as well as
 

00:05:39.759 --> 00:05:41.909 align:start position:0%
source you know AI models as well as
Technologies<00:05:40.400><c> like</c><00:05:40.600><c> web</c><00:05:40.840><c> llm</c><00:05:41.400><c> and</c><00:05:41.600><c> and</c><00:05:41.720><c> web</c>

00:05:41.909 --> 00:05:41.919 align:start position:0%
Technologies like web llm and and web
 

00:05:41.919 --> 00:05:44.469 align:start position:0%
Technologies like web llm and and web
assembly<00:05:42.759><c> and</c><00:05:42.880><c> so</c><00:05:43.240><c> I've</c><00:05:43.400><c> got</c><00:05:43.520><c> the</c><00:05:43.639><c> GitHub</c><00:05:43.960><c> repo</c>

00:05:44.469 --> 00:05:44.479 align:start position:0%
assembly and so I've got the GitHub repo
 

00:05:44.479 --> 00:05:46.070 align:start position:0%
assembly and so I've got the GitHub repo
and<00:05:44.600><c> the</c><00:05:44.680><c> main</c><00:05:44.960><c> site</c><00:05:45.120><c> link</c><00:05:45.360><c> down</c><00:05:45.520><c> below</c><00:05:45.880><c> but</c>

00:05:46.070 --> 00:05:46.080 align:start position:0%
and the main site link down below but
 

00:05:46.080 --> 00:05:48.150 align:start position:0%
and the main site link down below but
this<00:05:46.199><c> is</c><00:05:46.360><c> really</c><00:05:46.600><c> great</c><00:05:47.560><c> and</c><00:05:47.720><c> now</c><00:05:47.840><c> it's</c><00:05:48.039><c> time</c>

00:05:48.150 --> 00:05:48.160 align:start position:0%
this is really great and now it's time
 

00:05:48.160 --> 00:05:50.390 align:start position:0%
this is really great and now it's time
for<00:05:48.319><c> my</c><00:05:48.479><c> pick</c><00:05:48.639><c> of</c><00:05:48.759><c> the</c><00:05:48.880><c> week</c><00:05:49.479><c> so</c><00:05:49.960><c> by</c><00:05:50.080><c> the</c><00:05:50.240><c> time</c>

00:05:50.390 --> 00:05:50.400 align:start position:0%
for my pick of the week so by the time
 

00:05:50.400 --> 00:05:52.550 align:start position:0%
for my pick of the week so by the time
you<00:05:50.520><c> see</c><00:05:50.840><c> this</c><00:05:51.199><c> we</c><00:05:51.400><c> might</c><00:05:51.759><c> already</c><00:05:52.120><c> know</c><00:05:52.440><c> the</c>

00:05:52.550 --> 00:05:52.560 align:start position:0%
you see this we might already know the
 

00:05:52.560 --> 00:05:54.950 align:start position:0%
you see this we might already know the
winner<00:05:53.039><c> of</c><00:05:53.199><c> the</c><00:05:53.319><c> Eurovision</c><00:05:53.800><c> song</c><00:05:54.160><c> contest</c>

00:05:54.950 --> 00:05:54.960 align:start position:0%
winner of the Eurovision song contest
 

00:05:54.960 --> 00:05:56.390 align:start position:0%
winner of the Eurovision song contest
but<00:05:55.120><c> I</c><00:05:55.240><c> did</c><00:05:55.400><c> want</c><00:05:55.479><c> to</c><00:05:55.639><c> call</c><00:05:55.800><c> out</c><00:05:55.960><c> my</c><00:05:56.080><c> favorite</c>

00:05:56.390 --> 00:05:56.400 align:start position:0%
but I did want to call out my favorite
 

00:05:56.400 --> 00:05:58.590 align:start position:0%
but I did want to call out my favorite
entry<00:05:57.280><c> regardless</c><00:05:57.720><c> of</c><00:05:57.840><c> who</c><00:05:57.960><c> ultimately</c><00:05:58.400><c> takes</c>

00:05:58.590 --> 00:05:58.600 align:start position:0%
entry regardless of who ultimately takes
 

00:05:58.600 --> 00:06:01.029 align:start position:0%
entry regardless of who ultimately takes
the<00:05:58.720><c> crown</c><00:05:59.360><c> and</c><00:05:59.680><c> that</c><00:05:59.800><c> is</c><00:05:59.919><c> finland's</c><00:06:00.400><c> entry</c><00:06:00.919><c> a</c>

00:06:01.029 --> 00:06:01.039 align:start position:0%
the crown and that is finland's entry a
 

00:06:01.039 --> 00:06:05.430 align:start position:0%
the crown and that is finland's entry a
song<00:06:01.360><c> called</c><00:06:01.680><c> No</c><00:06:01.919><c> Rules</c><00:06:02.680><c> by</c><00:06:03.080><c> Windows</c><00:06:03.560><c> 95</c><00:06:04.440><c> man</c>

00:06:05.430 --> 00:06:05.440 align:start position:0%
song called No Rules by Windows 95 man
 

00:06:05.440 --> 00:06:06.830 align:start position:0%
song called No Rules by Windows 95 man
I've<00:06:05.600><c> actually</c><00:06:05.759><c> been</c><00:06:05.880><c> a</c><00:06:06.000><c> fan</c><00:06:06.120><c> of</c><00:06:06.199><c> windows95</c>

00:06:06.830 --> 00:06:06.840 align:start position:0%
I've actually been a fan of windows95
 

00:06:06.840 --> 00:06:08.870 align:start position:0%
I've actually been a fan of windows95
man<00:06:07.000><c> for</c><00:06:07.120><c> about</c><00:06:07.319><c> 6</c><00:06:07.639><c> years</c><00:06:08.240><c> we</c><00:06:08.400><c> even</c><00:06:08.560><c> tried</c><00:06:08.800><c> to</c>

00:06:08.870 --> 00:06:08.880 align:start position:0%
man for about 6 years we even tried to
 

00:06:08.880 --> 00:06:10.749 align:start position:0%
man for about 6 years we even tried to
get<00:06:09.000><c> him</c><00:06:09.120><c> to</c><00:06:09.280><c> perform</c><00:06:09.759><c> at</c><00:06:09.880><c> a</c><00:06:10.039><c> Microsoft</c><00:06:10.520><c> Event</c>

00:06:10.749 --> 00:06:10.759 align:start position:0%
get him to perform at a Microsoft Event
 

00:06:10.759 --> 00:06:13.029 align:start position:0%
get him to perform at a Microsoft Event
once<00:06:11.039><c> but</c><00:06:11.280><c> I</c><00:06:11.440><c> think</c><00:06:11.599><c> the</c><00:06:11.720><c> branding</c><00:06:12.440><c> people</c>

00:06:13.029 --> 00:06:13.039 align:start position:0%
once but I think the branding people
 

00:06:13.039 --> 00:06:14.950 align:start position:0%
once but I think the branding people
like<00:06:13.199><c> wouldn't</c><00:06:13.479><c> let</c><00:06:13.639><c> us</c><00:06:14.240><c> anyway</c><00:06:14.759><c> you</c><00:06:14.840><c> can</c>

00:06:14.950 --> 00:06:14.960 align:start position:0%
like wouldn't let us anyway you can
 

00:06:14.960 --> 00:06:16.150 align:start position:0%
like wouldn't let us anyway you can
check<00:06:15.160><c> this</c><00:06:15.280><c> performance</c><00:06:15.639><c> out</c><00:06:15.800><c> from</c><00:06:15.880><c> a</c><00:06:15.960><c> couple</c>

00:06:16.150 --> 00:06:16.160 align:start position:0%
check this performance out from a couple
 

00:06:16.160 --> 00:06:17.830 align:start position:0%
check this performance out from a couple
months<00:06:16.360><c> ago</c><00:06:16.599><c> it</c><00:06:16.680><c> was</c><00:06:16.919><c> great</c><00:06:17.479><c> I'm</c><00:06:17.639><c> really</c>

00:06:17.830 --> 00:06:17.840 align:start position:0%
months ago it was great I'm really
 

00:06:17.840 --> 00:06:20.670 align:start position:0%
months ago it was great I'm really
hoping<00:06:18.120><c> he</c><00:06:18.240><c> wins</c><00:06:18.720><c> because</c><00:06:19.080><c> you</c><00:06:19.240><c> know</c><00:06:19.639><c> come</c><00:06:19.759><c> on</c>

00:06:20.670 --> 00:06:20.680 align:start position:0%
hoping he wins because you know come on
 

00:06:20.680 --> 00:06:22.950 align:start position:0%
hoping he wins because you know come on
it's<00:06:20.919><c> Windows</c><00:06:21.240><c> 95</c>

00:06:22.950 --> 00:06:22.960 align:start position:0%
it's Windows 95
 

00:06:22.960 --> 00:06:25.629 align:start position:0%
it's Windows 95
man<00:06:23.960><c> it's</c><00:06:24.199><c> great</c><00:06:24.960><c> that's</c><00:06:25.120><c> going</c><00:06:25.199><c> to</c><00:06:25.280><c> do</c><00:06:25.400><c> it</c><00:06:25.479><c> for</c>

00:06:25.629 --> 00:06:25.639 align:start position:0%
man it's great that's going to do it for
 

00:06:25.639 --> 00:06:27.029 align:start position:0%
man it's great that's going to do it for
me<00:06:25.919><c> let</c><00:06:26.000><c> me</c><00:06:26.120><c> know</c><00:06:26.280><c> your</c><00:06:26.440><c> thoughts</c><00:06:26.639><c> on</c><00:06:26.759><c> Windows</c>

00:06:27.029 --> 00:06:27.039 align:start position:0%
me let me know your thoughts on Windows
 

00:06:27.039 --> 00:06:29.150 align:start position:0%
me let me know your thoughts on Windows
95<00:06:27.479><c> man</c><00:06:27.720><c> or</c><00:06:27.880><c> any</c><00:06:28.039><c> of</c><00:06:28.160><c> our</c><00:06:28.319><c> other</c><00:06:28.560><c> stories</c><00:06:29.080><c> that</c>

00:06:29.150 --> 00:06:29.160 align:start position:0%
95 man or any of our other stories that
 

00:06:29.160 --> 00:06:30.469 align:start position:0%
95 man or any of our other stories that
we<00:06:29.240><c> talked</c><00:06:29.599><c> about</c><00:06:29.759><c> in</c><00:06:29.880><c> the</c><00:06:29.960><c> comments</c><00:06:30.280><c> down</c>

00:06:30.469 --> 00:06:30.479 align:start position:0%
we talked about in the comments down
 

00:06:30.479 --> 00:06:31.790 align:start position:0%
we talked about in the comments down
below<00:06:30.800><c> or</c><00:06:31.120><c> you</c><00:06:31.240><c> know</c><00:06:31.319><c> if</c><00:06:31.400><c> you</c><00:06:31.479><c> had</c><00:06:31.599><c> another</c>

00:06:31.790 --> 00:06:31.800 align:start position:0%
below or you know if you had another
 

00:06:31.800 --> 00:06:33.830 align:start position:0%
below or you know if you had another
euro<00:06:32.120><c> vision</c><00:06:32.319><c> entry</c><00:06:32.639><c> you</c><00:06:32.720><c> liked</c><00:06:32.960><c> better</c><00:06:33.680><c> and</c>

00:06:33.830 --> 00:06:33.840 align:start position:0%
euro vision entry you liked better and
 

00:06:33.840 --> 00:06:35.550 align:start position:0%
euro vision entry you liked better and
if<00:06:33.919><c> you</c><00:06:34.039><c> like</c><00:06:34.240><c> this</c><00:06:34.400><c> episode</c><00:06:34.919><c> please</c><00:06:35.280><c> give</c><00:06:35.400><c> us</c>

00:06:35.550 --> 00:06:35.560 align:start position:0%
if you like this episode please give us
 

00:06:35.560 --> 00:06:37.350 align:start position:0%
if you like this episode please give us
a<00:06:35.720><c> like</c><00:06:35.960><c> on</c><00:06:36.160><c> YouTube</c><00:06:36.800><c> it</c><00:06:36.919><c> helps</c><00:06:37.120><c> out</c><00:06:37.280><c> the</c>

00:06:37.350 --> 00:06:37.360 align:start position:0%
a like on YouTube it helps out the
 

00:06:37.360 --> 00:06:39.430 align:start position:0%
a like on YouTube it helps out the
algorithm<00:06:38.199><c> And</c><00:06:38.479><c> subscribe</c><00:06:38.840><c> to</c><00:06:38.960><c> the</c><00:06:39.120><c> GitHub</c>

00:06:39.430 --> 00:06:39.440 align:start position:0%
algorithm And subscribe to the GitHub
 

00:06:39.440 --> 00:06:41.550 align:start position:0%
algorithm And subscribe to the GitHub
YouTube<00:06:39.680><c> channel</c><00:06:39.919><c> for</c><00:06:40.039><c> all</c><00:06:40.160><c> your</c><00:06:40.280><c> nerd</c><00:06:40.560><c> needs</c>

00:06:41.550 --> 00:06:41.560 align:start position:0%
YouTube channel for all your nerd needs
 

00:06:41.560 --> 00:06:43.840 align:start position:0%
YouTube channel for all your nerd needs
see<00:06:41.720><c> you</c><00:06:41.880><c> next</c><00:06:42.080><c> time</c>

00:06:43.840 --> 00:06:43.850 align:start position:0%
see you next time
 

00:06:43.850 --> 00:06:48.939 align:start position:0%
see you next time
[Music]

