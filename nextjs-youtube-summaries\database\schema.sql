-- YouTube Summaries Database Schema Setup
-- This file contains all the SQL commands needed to set up the database schema

-- Enable pgvector extension for vector similarity search
CREATE EXTENSION IF NOT EXISTS vector;

-- Add vector columns to existing tables
-- You'll need to run this for each of your topic tables

DO $$
DECLARE
    table_name text;
    tables text[] := ARRAY[
        'youtube_artificial_intelligence',
        'youtube_renewable_energy', 
        'youtube_gme',
        'youtube_sustainability',
        'youtube_startups',
        'youtube_financial_markets',
        'youtube_general'
    ];
BEGIN
    FOREACH table_name IN ARRAY tables
    LOOP
        -- Add embedding columns if they don't exist
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS transcript_embedding vector(1536)', table_name);
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS summary_embedding vector(1536)', table_name);
        
        -- Add standard columns that might be missing
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS duration text', table_name);
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS view_count integer DEFAULT 0', table_name);
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS thumbnail_url text', table_name);
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS video_url text', table_name);
        EXECUTE format('ALTER TABLE %I ADD COLUMN IF NOT EXISTS llm_summary text', table_name);
    END LOOP;
END
$$;

-- Create vector indexes for similarity search
DO $$
DECLARE
    table_name text;
    tables text[] := ARRAY[
        'youtube_artificial_intelligence',
        'youtube_renewable_energy', 
        'youtube_gme',
        'youtube_sustainability',
        'youtube_startups',
        'youtube_financial_markets',
        'youtube_general'
    ];
BEGIN
    FOREACH table_name IN ARRAY tables
    LOOP
        -- Create indexes for vector similarity search
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I_transcript_embedding_idx ON %I USING ivfflat (transcript_embedding vector_cosine_ops)', table_name || '_transcript', table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I_summary_embedding_idx ON %I USING ivfflat (summary_embedding vector_cosine_ops)', table_name || '_summary', table_name);
        
        -- Create regular indexes for performance
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I_published_at_idx ON %I (published_at DESC)', table_name, table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I_video_id_idx ON %I (video_id)', table_name, table_name);
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I_processed_idx ON %I (processed)', table_name, table_name);
    END LOOP;
END
$$;

-- Create function to get topic tables information
CREATE OR REPLACE FUNCTION get_topic_tables()
RETURNS TABLE(
    name text,
    display_name text,
    description text,
    video_count bigint
) 
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'youtube_artificial_intelligence'::text as name,
        'Artificial Intelligence'::text as display_name,
        'AI, Machine Learning, and Technology'::text as description,
        (SELECT COUNT(*) FROM youtube_artificial_intelligence WHERE processed = true) as video_count
    UNION ALL
    SELECT 
        'youtube_renewable_energy'::text,
        'Renewable Energy'::text,
        'Solar, Wind, and Clean Energy'::text,
        (SELECT COUNT(*) FROM youtube_renewable_energy WHERE processed = true)
    UNION ALL
    SELECT 
        'youtube_gme'::text,
        'GME & Finance'::text,
        'GameStop and Financial Markets'::text,
        (SELECT COUNT(*) FROM youtube_gme WHERE processed = true)
    UNION ALL
    SELECT 
        'youtube_sustainability'::text,
        'Sustainability'::text,
        'Environmental and Sustainable Practices'::text,
        (SELECT COUNT(*) FROM youtube_sustainability WHERE processed = true)
    UNION ALL
    SELECT 
        'youtube_startups'::text,
        'Startups'::text,
        'Entrepreneurship and Business'::text,
        (SELECT COUNT(*) FROM youtube_startups WHERE processed = true)
    UNION ALL
    SELECT 
        'youtube_financial_markets'::text,
        'Financial Markets'::text,
        'Trading, Investing, and Markets'::text,
        (SELECT COUNT(*) FROM youtube_financial_markets WHERE processed = true)
    UNION ALL
    SELECT 
        'youtube_general'::text,
        'General'::text,
        'General Topics and Miscellaneous'::text,
        (SELECT COUNT(*) FROM youtube_general WHERE processed = true);
END;
$$;

-- Create function for vector similarity search
CREATE OR REPLACE FUNCTION search_videos_by_embedding(
    table_name text,
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10
)
RETURNS TABLE(
    id uuid,
    video_id text,
    title text,
    channel_name text,
    published_at timestamp,
    duration text,
    view_count integer,
    transcript text,
    llm_summary text,
    thumbnail_url text,
    video_url text,
    topic_category text,
    similarity_score float,
    relevant_chunks text[]
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- This is a template function - you'll need to customize based on your exact table structure
    RETURN QUERY EXECUTE format('
        SELECT 
            t.id,
            t.video_id,
            t.title,
            t.channel_name,
            t.published_at,
            COALESCE(t.duration, '''') as duration,
            COALESCE(t.view_count, 0) as view_count,
            COALESCE(t.transcript, '''') as transcript,
            COALESCE(t.llm_summary, t.summary, '''') as llm_summary,
            COALESCE(t.thumbnail_url, '''') as thumbnail_url,
            COALESCE(t.video_url, ''https://youtube.com/watch?v='' || t.video_id) as video_url,
            %L as topic_category,
            (1 - (t.transcript_embedding <=> %L)) as similarity_score,
            ARRAY[]::text[] as relevant_chunks
        FROM %I t
        WHERE t.processed = true 
        AND t.transcript_embedding IS NOT NULL
        AND (1 - (t.transcript_embedding <=> %L)) > %L
        ORDER BY t.transcript_embedding <=> %L
        LIMIT %L
    ', 
    table_name, 
    query_embedding, 
    table_name, 
    query_embedding, 
    match_threshold, 
    query_embedding, 
    match_count);
END;
$$;

-- Create a unified view for searching across all tables (optional)
-- Note: This view might be heavy for large datasets, use carefully
CREATE OR REPLACE VIEW all_youtube_videos AS
SELECT 
    id, 
    video_id, 
    title, 
    channel_name, 
    published_at, 
    COALESCE(duration, '') as duration,
    COALESCE(view_count, 0) as view_count,
    COALESCE(transcript, '') as transcript,
    COALESCE(llm_summary, summary, '') as llm_summary,
    COALESCE(thumbnail_url, '') as thumbnail_url,
    COALESCE(video_url, 'https://youtube.com/watch?v=' || video_id) as video_url,
    transcript_embedding,
    summary_embedding,
    'artificial_intelligence' as topic_category,
    created_at,
    updated_at
FROM youtube_artificial_intelligence
WHERE processed = true

UNION ALL

SELECT 
    id, 
    video_id, 
    title, 
    channel_name, 
    published_at, 
    COALESCE(duration, '') as duration,
    COALESCE(view_count, 0) as view_count,
    COALESCE(transcript, '') as transcript,
    COALESCE(llm_summary, summary, '') as llm_summary,
    COALESCE(thumbnail_url, '') as thumbnail_url,
    COALESCE(video_url, 'https://youtube.com/watch?v=' || video_id) as video_url,
    transcript_embedding,
    summary_embedding,
    'renewable_energy' as topic_category,
    created_at,
    updated_at
FROM youtube_renewable_energy
WHERE processed = true

UNION ALL

SELECT 
    id, 
    video_id, 
    title, 
    channel_name, 
    published_at, 
    COALESCE(duration, '') as duration,
    COALESCE(view_count, 0) as view_count,
    COALESCE(transcript, '') as transcript,
    COALESCE(llm_summary, summary, '') as llm_summary,
    COALESCE(thumbnail_url, '') as thumbnail_url,
    COALESCE(video_url, 'https://youtube.com/watch?v=' || video_id) as video_url,
    transcript_embedding,
    summary_embedding,
    'gme' as topic_category,
    created_at,
    updated_at
FROM youtube_gme
WHERE processed = true

UNION ALL

SELECT 
    id, 
    video_id, 
    title, 
    channel_name, 
    published_at, 
    COALESCE(duration, '') as duration,
    COALESCE(view_count, 0) as view_count,
    COALESCE(transcript, '') as transcript,
    COALESCE(llm_summary, summary, '') as llm_summary,
    COALESCE(thumbnail_url, '') as thumbnail_url,
    COALESCE(video_url, 'https://youtube.com/watch?v=' || video_id) as video_url,
    transcript_embedding,
    summary_embedding,
    'sustainability' as topic_category,
    created_at,
    updated_at
FROM youtube_sustainability
WHERE processed = true

UNION ALL

SELECT 
    id, 
    video_id, 
    title, 
    channel_name, 
    published_at, 
    COALESCE(duration, '') as duration,
    COALESCE(view_count, 0) as view_count,
    COALESCE(transcript, '') as transcript,
    COALESCE(llm_summary, summary, '') as llm_summary,
    COALESCE(thumbnail_url, '') as thumbnail_url,
    COALESCE(video_url, 'https://youtube.com/watch?v=' || video_id) as video_url,
    transcript_embedding,
    summary_embedding,
    'startups' as topic_category,
    created_at,
    updated_at
FROM youtube_startups
WHERE processed = true

UNION ALL

SELECT 
    id, 
    video_id, 
    title, 
    channel_name, 
    published_at, 
    COALESCE(duration, '') as duration,
    COALESCE(view_count, 0) as view_count,
    COALESCE(transcript, '') as transcript,
    COALESCE(llm_summary, summary, '') as llm_summary,
    COALESCE(thumbnail_url, '') as thumbnail_url,
    COALESCE(video_url, 'https://youtube.com/watch?v=' || video_id) as video_url,
    transcript_embedding,
    summary_embedding,
    'financial_markets' as topic_category,
    created_at,
    updated_at
FROM youtube_financial_markets
WHERE processed = true

UNION ALL

SELECT 
    id, 
    video_id, 
    title, 
    channel_name, 
    published_at, 
    COALESCE(duration, '') as duration,
    COALESCE(view_count, 0) as view_count,
    COALESCE(transcript, '') as transcript,
    COALESCE(llm_summary, summary, '') as llm_summary,
    COALESCE(thumbnail_url, '') as thumbnail_url,
    COALESCE(video_url, 'https://youtube.com/watch?v=' || video_id) as video_url,
    transcript_embedding,
    summary_embedding,
    'general' as topic_category,
    created_at,
    updated_at
FROM youtube_general
WHERE processed = true;
