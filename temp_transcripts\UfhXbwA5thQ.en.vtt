WEBVTT
Kind: captions
Language: en

00:00:00.359 --> 00:00:03.110 align:start position:0%
 
one<00:00:00.520><c> of</c><00:00:00.680><c> the</c><00:00:00.919><c> great</c><00:00:01.240><c> things</c><00:00:01.760><c> about</c><00:00:02.080><c> AMA</c><00:00:03.000><c> is</c>

00:00:03.110 --> 00:00:03.120 align:start position:0%
one of the great things about AMA is
 

00:00:03.120 --> 00:00:05.950 align:start position:0%
one of the great things about AMA is
that<00:00:03.280><c> it</c><00:00:03.399><c> makes</c><00:00:03.639><c> it</c><00:00:04.120><c> super</c><00:00:04.640><c> easy</c><00:00:05.319><c> to</c><00:00:05.560><c> download</c>

00:00:05.950 --> 00:00:05.960 align:start position:0%
that it makes it super easy to download
 

00:00:05.960 --> 00:00:08.950 align:start position:0%
that it makes it super easy to download
models<00:00:06.319><c> that</c><00:00:06.480><c> work</c><00:00:06.680><c> with</c><00:00:07.040><c> AMA</c><00:00:08.040><c> you</c><00:00:08.240><c> just</c><00:00:08.519><c> run</c>

00:00:08.950 --> 00:00:08.960 align:start position:0%
models that work with AMA you just run
 

00:00:08.960 --> 00:00:13.589 align:start position:0%
models that work with AMA you just run
olama<00:00:09.599><c> pull</c><00:00:10.320><c> llama</c><00:00:10.719><c> 2</c><00:00:11.080><c> or</c><00:00:11.280><c> olama</c><00:00:11.840><c> run</c><00:00:12.480><c> llama</c><00:00:12.840><c> 2</c>

00:00:13.589 --> 00:00:13.599 align:start position:0%
olama pull llama 2 or olama run llama 2
 

00:00:13.599 --> 00:00:15.110 align:start position:0%
olama pull llama 2 or olama run llama 2
and<00:00:13.799><c> if</c><00:00:13.880><c> you</c><00:00:13.960><c> don't</c><00:00:14.200><c> have</c><00:00:14.360><c> the</c><00:00:14.480><c> model</c><00:00:15.000><c> it</c>

00:00:15.110 --> 00:00:15.120 align:start position:0%
and if you don't have the model it
 

00:00:15.120 --> 00:00:17.510 align:start position:0%
and if you don't have the model it
starts<00:00:15.560><c> downloading</c><00:00:16.560><c> but</c><00:00:16.960><c> let's</c><00:00:17.199><c> say</c><00:00:17.359><c> you</c>

00:00:17.510 --> 00:00:17.520 align:start position:0%
starts downloading but let's say you
 

00:00:17.520 --> 00:00:19.670 align:start position:0%
starts downloading but let's say you
want<00:00:17.720><c> to</c><00:00:17.920><c> use</c><00:00:18.240><c> the</c><00:00:18.400><c> model</c><00:00:18.760><c> in</c><00:00:19.039><c> something</c><00:00:19.320><c> else</c>

00:00:19.670 --> 00:00:19.680 align:start position:0%
want to use the model in something else
 

00:00:19.680 --> 00:00:23.830 align:start position:0%
want to use the model in something else
like<00:00:19.840><c> LM</c><00:00:20.320><c> Studio</c><00:00:21.320><c> well</c><00:00:21.519><c> now</c><00:00:21.720><c> you</c><00:00:21.960><c> go</c><00:00:22.199><c> to</c><00:00:23.080><c> l/m</c>

00:00:23.830 --> 00:00:23.840 align:start position:0%
like LM Studio well now you go to l/m
 

00:00:23.840 --> 00:00:26.669 align:start position:0%
like LM Studio well now you go to l/m
models<00:00:24.320><c> SL</c><00:00:24.720><c> blobs</c><00:00:25.359><c> and</c><00:00:25.920><c> well</c><00:00:26.240><c> good</c><00:00:26.439><c> luck</c>

00:00:26.669 --> 00:00:26.679 align:start position:0%
models SL blobs and well good luck
 

00:00:26.679 --> 00:00:29.509 align:start position:0%
models SL blobs and well good luck
figuring<00:00:27.000><c> out</c><00:00:27.240><c> which</c><00:00:27.400><c> one</c><00:00:27.800><c> is</c><00:00:27.960><c> llama</c><00:00:28.359><c> 2</c><00:00:29.240><c> so</c><00:00:29.400><c> I</c>

00:00:29.509 --> 00:00:29.519 align:start position:0%
figuring out which one is llama 2 so I
 

00:00:29.519 --> 00:00:31.310 align:start position:0%
figuring out which one is llama 2 so I
created<00:00:29.800><c> a</c><00:00:30.080><c> simple</c><00:00:30.359><c> shell</c><00:00:30.679><c> script</c><00:00:31.160><c> that</c>

00:00:31.310 --> 00:00:31.320 align:start position:0%
created a simple shell script that
 

00:00:31.320 --> 00:00:33.670 align:start position:0%
created a simple shell script that
creates<00:00:31.679><c> Sim</c><00:00:31.960><c> links</c><00:00:32.360><c> to</c><00:00:32.559><c> a</c><00:00:32.719><c> folder</c><00:00:33.239><c> of</c><00:00:33.399><c> your</c>

00:00:33.670 --> 00:00:33.680 align:start position:0%
creates Sim links to a folder of your
 

00:00:33.680 --> 00:00:35.950 align:start position:0%
creates Sim links to a folder of your
choosing<00:00:34.680><c> you</c><00:00:34.800><c> can</c><00:00:34.960><c> find</c><00:00:35.120><c> the</c><00:00:35.280><c> script</c><00:00:35.719><c> in</c><00:00:35.840><c> the</c>

00:00:35.950 --> 00:00:35.960 align:start position:0%
choosing you can find the script in the
 

00:00:35.960 --> 00:00:38.910 align:start position:0%
choosing you can find the script in the
GitHub<00:00:36.320><c> repo</c><00:00:37.000><c> Matt's</c><00:00:37.520><c> shell</c><00:00:37.960><c> scripts</c><00:00:38.800><c> and</c>

00:00:38.910 --> 00:00:38.920 align:start position:0%
GitHub repo Matt's shell scripts and
 

00:00:38.920 --> 00:00:41.150 align:start position:0%
GitHub repo Matt's shell scripts and
it's<00:00:39.120><c> called</c><00:00:39.399><c> sync</c><00:00:39.719><c> models</c><00:00:40.600><c> this</c><00:00:40.760><c> was</c><00:00:40.920><c> written</c>

00:00:41.150 --> 00:00:41.160 align:start position:0%
it's called sync models this was written
 

00:00:41.160 --> 00:00:42.990 align:start position:0%
it's called sync models this was written
for<00:00:41.399><c> Mac</c><00:00:41.680><c> OS</c><00:00:42.000><c> so</c><00:00:42.120><c> you</c><00:00:42.200><c> might</c><00:00:42.360><c> need</c><00:00:42.480><c> to</c><00:00:42.719><c> adjust</c>

00:00:42.990 --> 00:00:43.000 align:start position:0%
for Mac OS so you might need to adjust
 

00:00:43.000 --> 00:00:46.069 align:start position:0%
for Mac OS so you might need to adjust
it<00:00:43.239><c> a</c><00:00:43.399><c> bit</c><00:00:43.600><c> for</c><00:00:43.920><c> Linux</c><00:00:44.440><c> and</c><00:00:44.719><c> windows</c><00:00:45.719><c> let's</c><00:00:45.960><c> go</c>

00:00:46.069 --> 00:00:46.079 align:start position:0%
it a bit for Linux and windows let's go
 

00:00:46.079 --> 00:00:48.350 align:start position:0%
it a bit for Linux and windows let's go
over<00:00:46.280><c> the</c><00:00:46.399><c> script</c><00:00:46.800><c> here</c><00:00:47.719><c> the</c><00:00:47.920><c> first</c><00:00:48.160><c> thing</c>

00:00:48.350 --> 00:00:48.360 align:start position:0%
over the script here the first thing
 

00:00:48.360 --> 00:00:49.869 align:start position:0%
over the script here the first thing
you're<00:00:48.559><c> going</c><00:00:48.719><c> to</c><00:00:48.879><c> have</c><00:00:49.000><c> to</c><00:00:49.120><c> do</c><00:00:49.399><c> is</c><00:00:49.520><c> set</c><00:00:49.760><c> the</c>

00:00:49.869 --> 00:00:49.879 align:start position:0%
you're going to have to do is set the
 

00:00:49.879 --> 00:00:52.229 align:start position:0%
you're going to have to do is set the
base<00:00:50.239><c> directories</c><00:00:51.199><c> first</c><00:00:51.440><c> there's</c><00:00:51.640><c> the</c><00:00:51.800><c> olama</c>

00:00:52.229 --> 00:00:52.239 align:start position:0%
base directories first there's the olama
 

00:00:52.239 --> 00:00:55.590 align:start position:0%
base directories first there's the olama
directory<00:00:52.879><c> on</c><00:00:53.120><c> Mac</c><00:00:53.440><c> that's</c><00:00:53.879><c> do/</c><00:00:54.879><c> models</c><00:00:55.399><c> off</c>

00:00:55.590 --> 00:00:55.600 align:start position:0%
directory on Mac that's do/ models off
 

00:00:55.600 --> 00:00:58.069 align:start position:0%
directory on Mac that's do/ models off
the<00:00:55.719><c> user's</c><00:00:56.079><c> route</c><00:00:56.960><c> the</c><00:00:57.120><c> Manifest</c><00:00:57.680><c> directory</c>

00:00:58.069 --> 00:00:58.079 align:start position:0%
the user's route the Manifest directory
 

00:00:58.079 --> 00:00:59.509 align:start position:0%
the user's route the Manifest directory
and<00:00:58.160><c> the</c><00:00:58.280><c> blob</c><00:00:58.559><c> directory</c><00:00:58.960><c> are</c><00:00:59.320><c> probably</c>

00:00:59.509 --> 00:00:59.519 align:start position:0%
and the blob directory are probably
 

00:00:59.519 --> 00:01:02.349 align:start position:0%
and the blob directory are probably
going<00:00:59.640><c> to</c><00:00:59.719><c> be</c><00:01:00.079><c> the</c><00:01:00.160><c> same</c><00:01:00.480><c> for</c><00:01:01.039><c> everyone</c><00:01:02.039><c> public</c>

00:01:02.349 --> 00:01:02.359 align:start position:0%
going to be the same for everyone public
 

00:01:02.359 --> 00:01:04.350 align:start position:0%
going to be the same for everyone public
models<00:01:02.719><c> directory</c><00:01:03.320><c> is</c><00:01:03.519><c> wherever</c><00:01:03.960><c> you</c><00:01:04.119><c> want</c>

00:01:04.350 --> 00:01:04.360 align:start position:0%
models directory is wherever you want
 

00:01:04.360 --> 00:01:07.590 align:start position:0%
models directory is wherever you want
the<00:01:04.519><c> Sim</c><00:01:04.760><c> links</c><00:01:05.080><c> to</c><00:01:05.240><c> be</c><00:01:06.240><c> do</c><00:01:06.479><c> not</c><00:01:06.760><c> set</c><00:01:07.080><c> this</c><00:01:07.280><c> to</c>

00:01:07.590 --> 00:01:07.600 align:start position:0%
the Sim links to be do not set this to
 

00:01:07.600 --> 00:01:09.749 align:start position:0%
the Sim links to be do not set this to
anything<00:01:08.280><c> with</c><00:01:08.479><c> files</c><00:01:08.799><c> in</c><00:01:08.960><c> it</c><00:01:09.200><c> because</c><00:01:09.479><c> my</c>

00:01:09.749 --> 00:01:09.759 align:start position:0%
anything with files in it because my
 

00:01:09.759 --> 00:01:12.190 align:start position:0%
anything with files in it because my
first<00:01:10.080><c> step</c><00:01:10.479><c> in</c><00:01:10.680><c> this</c><00:01:10.840><c> script</c><00:01:11.400><c> is</c><00:01:11.520><c> to</c><00:01:11.720><c> delete</c>

00:01:12.190 --> 00:01:12.200 align:start position:0%
first step in this script is to delete
 

00:01:12.200 --> 00:01:14.749 align:start position:0%
first step in this script is to delete
everything<00:01:13.200><c> next</c><00:01:13.680><c> go</c><00:01:13.799><c> to</c><00:01:14.000><c> that</c><00:01:14.159><c> destination</c>

00:01:14.749 --> 00:01:14.759 align:start position:0%
everything next go to that destination
 

00:01:14.759 --> 00:01:16.590 align:start position:0%
everything next go to that destination
directory<00:01:15.280><c> and</c><00:01:15.439><c> delete</c><00:01:15.720><c> everything</c><00:01:16.080><c> in</c><00:01:16.200><c> it</c><00:01:16.360><c> so</c>

00:01:16.590 --> 00:01:16.600 align:start position:0%
directory and delete everything in it so
 

00:01:16.600 --> 00:01:19.749 align:start position:0%
directory and delete everything in it so
again<00:01:17.119><c> don't</c><00:01:17.520><c> point</c><00:01:17.880><c> this</c><00:01:18.040><c> to</c><00:01:18.240><c> a</c><00:01:18.439><c> populated</c>

00:01:19.749 --> 00:01:19.759 align:start position:0%
again don't point this to a populated
 

00:01:19.759 --> 00:01:22.789 align:start position:0%
again don't point this to a populated
directory<00:01:20.759><c> then</c><00:01:21.520><c> we</c><00:01:21.640><c> are</c><00:01:21.799><c> going</c><00:01:22.040><c> to</c><00:01:22.360><c> iterate</c>

00:01:22.789 --> 00:01:22.799 align:start position:0%
directory then we are going to iterate
 

00:01:22.799 --> 00:01:24.590 align:start position:0%
directory then we are going to iterate
through<00:01:23.200><c> the</c><00:01:23.360><c> Manifest</c><00:01:23.880><c> directory</c><00:01:24.320><c> searching</c>

00:01:24.590 --> 00:01:24.600 align:start position:0%
through the Manifest directory searching
 

00:01:24.600 --> 00:01:27.950 align:start position:0%
through the Manifest directory searching
for<00:01:24.799><c> models</c><00:01:25.759><c> I</c><00:01:25.880><c> set</c><00:01:26.119><c> the</c><00:01:26.240><c> user</c><00:01:26.759><c> model</c><00:01:27.360><c> tag</c><00:01:27.759><c> and</c>

00:01:27.950 --> 00:01:27.960 align:start position:0%
for models I set the user model tag and
 

00:01:27.960 --> 00:01:30.310 align:start position:0%
for models I set the user model tag and
digest<00:01:28.400><c> for</c><00:01:28.600><c> each</c><00:01:28.759><c> model</c><00:01:29.040><c> and</c><00:01:29.240><c> tag</c><00:01:30.000><c> notice</c>

00:01:30.310 --> 00:01:30.320 align:start position:0%
digest for each model and tag notice
 

00:01:30.320 --> 00:01:33.749 align:start position:0%
digest for each model and tag notice
that<00:01:30.479><c> I</c><00:01:30.560><c> use</c><00:01:30.840><c> JQ</c><00:01:31.439><c> to</c><00:01:31.680><c> parse</c><00:01:32.119><c> the</c><00:01:32.439><c> Manifest</c><00:01:33.079><c> file</c>

00:01:33.749 --> 00:01:33.759 align:start position:0%
that I use JQ to parse the Manifest file
 

00:01:33.759 --> 00:01:36.030 align:start position:0%
that I use JQ to parse the Manifest file
this<00:01:33.880><c> is</c><00:01:34.040><c> a</c><00:01:34.200><c> Json</c><00:01:34.600><c> file</c><00:01:34.799><c> and</c><00:01:34.960><c> JQ</c><00:01:35.399><c> is</c><00:01:35.520><c> a</c><00:01:35.720><c> super</c>

00:01:36.030 --> 00:01:36.040 align:start position:0%
this is a Json file and JQ is a super
 

00:01:36.040 --> 00:01:38.429 align:start position:0%
this is a Json file and JQ is a super
cool<00:01:36.320><c> way</c><00:01:36.520><c> to</c><00:01:36.680><c> parse</c><00:01:37.040><c> Json</c><00:01:37.799><c> on</c><00:01:38.000><c> the</c><00:01:38.119><c> command</c>

00:01:38.429 --> 00:01:38.439 align:start position:0%
cool way to parse Json on the command
 

00:01:38.439 --> 00:01:41.190 align:start position:0%
cool way to parse Json on the command
line<00:01:39.119><c> I</c><00:01:39.240><c> got</c><00:01:39.399><c> another</c><00:01:39.720><c> video</c><00:01:40.040><c> about</c><00:01:40.320><c> using</c>

00:01:41.190 --> 00:01:41.200 align:start position:0%
line I got another video about using
 

00:01:41.200 --> 00:01:44.149 align:start position:0%
line I got another video about using
JQ<00:01:42.200><c> then</c><00:01:42.360><c> for</c><00:01:42.600><c> each</c><00:01:42.840><c> tag</c><00:01:43.119><c> file</c><00:01:43.520><c> create</c><00:01:43.799><c> the</c><00:01:43.920><c> Sim</c>

00:01:44.149 --> 00:01:44.159 align:start position:0%
JQ then for each tag file create the Sim
 

00:01:44.159 --> 00:01:46.630 align:start position:0%
JQ then for each tag file create the Sim
link<00:01:44.640><c> and</c><00:01:44.880><c> print</c><00:01:45.079><c> out</c><00:01:45.240><c> the</c><00:01:45.360><c> model</c><00:01:45.719><c> to</c><00:01:45.960><c> the</c><00:01:46.079><c> CLI</c>

00:01:46.630 --> 00:01:46.640 align:start position:0%
link and print out the model to the CLI
 

00:01:46.640 --> 00:01:49.270 align:start position:0%
link and print out the model to the CLI
and<00:01:47.119><c> but</c><00:01:47.360><c> that's</c><00:01:47.560><c> it</c><00:01:48.360><c> now</c><00:01:48.520><c> you</c><00:01:48.600><c> can</c><00:01:48.799><c> go</c><00:01:48.960><c> over</c><00:01:49.119><c> to</c>

00:01:49.270 --> 00:01:49.280 align:start position:0%
and but that's it now you can go over to
 

00:01:49.280 --> 00:01:51.749 align:start position:0%
and but that's it now you can go over to
a<00:01:49.399><c> tool</c><00:01:49.759><c> like</c><00:01:49.960><c> LM</c><00:01:50.360><c> studio</c><00:01:51.000><c> and</c><00:01:51.119><c> it</c><00:01:51.280><c> finds</c><00:01:51.560><c> all</c>

00:01:51.749 --> 00:01:51.759 align:start position:0%
a tool like LM studio and it finds all
 

00:01:51.759 --> 00:01:53.190 align:start position:0%
a tool like LM studio and it finds all
the<00:01:51.880><c> models</c><00:01:52.240><c> you've</c><00:01:52.439><c> already</c><00:01:52.680><c> downloaded</c>

00:01:53.190 --> 00:01:53.200 align:start position:0%
the models you've already downloaded
 

00:01:53.200 --> 00:01:56.709 align:start position:0%
the models you've already downloaded
from<00:01:53.439><c> AMA</c><00:01:54.119><c> inside</c><00:01:54.520><c> of</c><00:01:54.759><c> LM</c><00:01:55.280><c> Studio</c><00:01:56.280><c> let</c><00:01:56.399><c> me</c><00:01:56.560><c> know</c>

00:01:56.709 --> 00:01:56.719 align:start position:0%
from AMA inside of LM Studio let me know
 

00:01:56.719 --> 00:01:58.950 align:start position:0%
from AMA inside of LM Studio let me know
if<00:01:56.840><c> this</c><00:01:56.960><c> is</c><00:01:57.119><c> useful</c><00:01:57.479><c> for</c><00:01:57.680><c> you</c><00:01:58.240><c> I</c><00:01:58.320><c> wrote</c><00:01:58.600><c> it</c><00:01:58.840><c> a</c>

00:01:58.950 --> 00:01:58.960 align:start position:0%
if this is useful for you I wrote it a
 

00:01:58.960 --> 00:02:01.389 align:start position:0%
if this is useful for you I wrote it a
few<00:01:59.119><c> months</c><00:01:59.479><c> back</c><00:02:00.039><c> and</c><00:02:00.560><c> then</c><00:02:00.840><c> have</c><00:02:01.000><c> mentioned</c>

00:02:01.389 --> 00:02:01.399 align:start position:0%
few months back and then have mentioned
 

00:02:01.399 --> 00:02:03.749 align:start position:0%
few months back and then have mentioned
it<00:02:01.640><c> every</c><00:02:01.840><c> few</c><00:02:02.079><c> weeks</c><00:02:02.439><c> in</c><00:02:02.560><c> the</c><00:02:02.759><c> Discord</c><00:02:03.520><c> so</c>

00:02:03.749 --> 00:02:03.759 align:start position:0%
it every few weeks in the Discord so
 

00:02:03.759 --> 00:02:05.670 align:start position:0%
it every few weeks in the Discord so
maybe<00:02:04.320><c> next</c><00:02:04.560><c> time</c><00:02:04.920><c> you</c><00:02:05.039><c> could</c><00:02:05.200><c> just</c><00:02:05.360><c> find</c><00:02:05.520><c> it</c>

00:02:05.670 --> 00:02:05.680 align:start position:0%
maybe next time you could just find it
 

00:02:05.680 --> 00:02:08.109 align:start position:0%
maybe next time you could just find it
here<00:02:05.840><c> on</c><00:02:06.079><c> YouTube</c><00:02:07.079><c> well</c><00:02:07.240><c> that's</c><00:02:07.399><c> it</c><00:02:07.560><c> for</c><00:02:07.719><c> now</c>

00:02:08.109 --> 00:02:08.119 align:start position:0%
here on YouTube well that's it for now
 

00:02:08.119 --> 00:02:10.270 align:start position:0%
here on YouTube well that's it for now
thanks<00:02:08.319><c> so</c><00:02:08.479><c> much</c><00:02:08.640><c> for</c><00:02:08.800><c> watching</c><00:02:09.759><c> see</c><00:02:09.920><c> you</c><00:02:10.119><c> next</c>

00:02:10.270 --> 00:02:10.280 align:start position:0%
thanks so much for watching see you next
 

00:02:10.280 --> 00:02:12.040 align:start position:0%
thanks so much for watching see you next
time

00:02:12.040 --> 00:02:12.050 align:start position:0%
time
 

00:02:12.050 --> 00:02:28.550 align:start position:0%
time
[Music]

00:02:28.550 --> 00:02:28.560 align:start position:0%
[Music]
 

00:02:28.560 --> 00:02:31.560 align:start position:0%
[Music]
bye

