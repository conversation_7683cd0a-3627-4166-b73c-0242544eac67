WEBVTT
Kind: captions
Language: en

00:00:02.510 --> 00:00:08.790 align:start position:0%
 
[Music]

00:00:08.790 --> 00:00:08.800 align:start position:0%
[Music]
 

00:00:08.800 --> 00:00:11.509 align:start position:0%
[Music]
let's<00:00:09.120><c> create</c><00:00:09.280><c> a</c><00:00:09.440><c> class</c><00:00:09.760><c> name</c><00:00:10.000><c> it</c><00:00:10.160><c> universe</c>

00:00:11.509 --> 00:00:11.519 align:start position:0%
let's create a class name it universe
 

00:00:11.519 --> 00:00:13.030 align:start position:0%
let's create a class name it universe
within<00:00:11.840><c> here</c><00:00:12.000><c> we're</c><00:00:12.160><c> going</c><00:00:12.240><c> to</c><00:00:12.400><c> create</c><00:00:12.559><c> a</c><00:00:12.719><c> main</c>

00:00:13.030 --> 00:00:13.040 align:start position:0%
within here we're going to create a main
 

00:00:13.040 --> 00:00:14.629 align:start position:0%
within here we're going to create a main
method<00:00:13.840><c> and</c><00:00:14.000><c> explain</c>

00:00:14.629 --> 00:00:14.639 align:start position:0%
method and explain
 

00:00:14.639 --> 00:00:18.710 align:start position:0%
method and explain
all<00:00:14.799><c> the</c><00:00:14.880><c> pieces</c><00:00:15.200><c> of</c><00:00:15.280><c> code</c><00:00:15.599><c> within</c><00:00:16.000><c> this</c><00:00:16.160><c> class</c>

00:00:18.710 --> 00:00:18.720 align:start position:0%
 
 

00:00:18.720 --> 00:00:20.870 align:start position:0%
 
so<00:00:18.960><c> we</c><00:00:19.039><c> have</c><00:00:19.119><c> the</c><00:00:19.279><c> first</c><00:00:19.600><c> public</c><00:00:20.000><c> here</c><00:00:20.640><c> this</c>

00:00:20.870 --> 00:00:20.880 align:start position:0%
so we have the first public here this
 

00:00:20.880 --> 00:00:23.429 align:start position:0%
so we have the first public here this
just<00:00:21.119><c> means</c><00:00:21.439><c> anyone</c><00:00:21.840><c> can</c><00:00:22.080><c> run</c><00:00:22.240><c> this</c><00:00:22.480><c> program</c>

00:00:23.429 --> 00:00:23.439 align:start position:0%
just means anyone can run this program
 

00:00:23.439 --> 00:00:25.990 align:start position:0%
just means anyone can run this program
class<00:00:23.840><c> is</c><00:00:23.920><c> a</c><00:00:24.000><c> reserved</c><00:00:24.480><c> word</c><00:00:25.119><c> because</c><00:00:25.519><c> all</c>

00:00:25.990 --> 00:00:26.000 align:start position:0%
class is a reserved word because all
 

00:00:26.000 --> 00:00:28.070 align:start position:0%
class is a reserved word because all
java<00:00:26.400><c> programs</c><00:00:26.880><c> must</c><00:00:27.119><c> belong</c><00:00:27.680><c> to</c>

00:00:28.070 --> 00:00:28.080 align:start position:0%
java programs must belong to
 

00:00:28.080 --> 00:00:31.029 align:start position:0%
java programs must belong to
a<00:00:28.240><c> class</c><00:00:29.279><c> universe</c><00:00:30.160><c> this</c><00:00:30.320><c> is</c><00:00:30.400><c> just</c><00:00:30.560><c> simply</c><00:00:30.880><c> the</c>

00:00:31.029 --> 00:00:31.039 align:start position:0%
a class universe this is just simply the
 

00:00:31.039 --> 00:00:32.310 align:start position:0%
a class universe this is just simply the
name<00:00:31.279><c> of</c><00:00:31.439><c> the</c><00:00:31.519><c> class</c>

00:00:32.310 --> 00:00:32.320 align:start position:0%
name of the class
 

00:00:32.320 --> 00:00:34.870 align:start position:0%
name of the class
this<00:00:32.559><c> curly</c><00:00:32.880><c> brace</c><00:00:33.680><c> is</c><00:00:33.840><c> used</c><00:00:34.079><c> for</c><00:00:34.239><c> the</c><00:00:34.399><c> opening</c>

00:00:34.870 --> 00:00:34.880 align:start position:0%
this curly brace is used for the opening
 

00:00:34.880 --> 00:00:35.670 align:start position:0%
this curly brace is used for the opening
of<00:00:35.040><c> the</c><00:00:35.200><c> class</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
of the class
 

00:00:35.680 --> 00:00:38.790 align:start position:0%
of the class
body<00:00:36.480><c> this</c><00:00:36.719><c> public</c><00:00:37.360><c> again</c><00:00:37.840><c> means</c><00:00:38.160><c> anybody</c><00:00:38.559><c> can</c>

00:00:38.790 --> 00:00:38.800 align:start position:0%
body this public again means anybody can
 

00:00:38.800 --> 00:00:40.069 align:start position:0%
body this public again means anybody can
run<00:00:39.120><c> this</c><00:00:39.440><c> method</c>

00:00:40.069 --> 00:00:40.079 align:start position:0%
run this method
 

00:00:40.079 --> 00:00:42.310 align:start position:0%
run this method
static<00:00:40.559><c> means</c><00:00:41.040><c> this</c><00:00:41.280><c> method</c><00:00:41.600><c> belongs</c><00:00:42.000><c> to</c><00:00:42.160><c> the</c>

00:00:42.310 --> 00:00:42.320 align:start position:0%
static means this method belongs to the
 

00:00:42.320 --> 00:00:43.990 align:start position:0%
static means this method belongs to the
class<00:00:42.960><c> not</c><00:00:43.200><c> an</c><00:00:43.360><c> object</c>

00:00:43.990 --> 00:00:44.000 align:start position:0%
class not an object
 

00:00:44.000 --> 00:00:47.750 align:start position:0%
class not an object
so<00:00:44.239><c> for</c><00:00:44.480><c> instance</c><00:00:45.200><c> we</c><00:00:45.360><c> can</c><00:00:45.680><c> say</c><00:00:46.600><c> universe.main</c>

00:00:47.750 --> 00:00:47.760 align:start position:0%
so for instance we can say universe.main
 

00:00:47.760 --> 00:00:49.670 align:start position:0%
so for instance we can say universe.main
instead<00:00:48.079><c> of</c><00:00:48.160><c> creating</c><00:00:48.480><c> a</c><00:00:48.640><c> universe</c><00:00:49.200><c> object</c>

00:00:49.670 --> 00:00:49.680 align:start position:0%
instead of creating a universe object
 

00:00:49.680 --> 00:00:51.910 align:start position:0%
instead of creating a universe object
first<00:00:50.000><c> and</c><00:00:50.160><c> then</c><00:00:50.399><c> calling</c><00:00:50.640><c> the</c><00:00:50.719><c> main</c><00:00:51.039><c> method</c>

00:00:51.910 --> 00:00:51.920 align:start position:0%
first and then calling the main method
 

00:00:51.920 --> 00:00:54.549 align:start position:0%
first and then calling the main method
void<00:00:52.559><c> means</c><00:00:53.120><c> the</c><00:00:53.280><c> method</c><00:00:53.680><c> doesn't</c><00:00:54.000><c> return</c>

00:00:54.549 --> 00:00:54.559 align:start position:0%
void means the method doesn't return
 

00:00:54.559 --> 00:00:56.069 align:start position:0%
void means the method doesn't return
anything

00:00:56.069 --> 00:00:56.079 align:start position:0%
anything
 

00:00:56.079 --> 00:00:58.389 align:start position:0%
anything
main<00:00:56.719><c> is</c><00:00:56.800><c> the</c><00:00:56.960><c> name</c><00:00:57.199><c> of</c><00:00:57.280><c> this</c><00:00:57.440><c> method</c><00:00:58.000><c> which</c><00:00:58.239><c> is</c>

00:00:58.389 --> 00:00:58.399 align:start position:0%
main is the name of this method which is
 

00:00:58.399 --> 00:01:01.189 align:start position:0%
main is the name of this method which is
also<00:00:58.719><c> a</c><00:00:58.879><c> special</c><00:00:59.199><c> method</c><00:00:59.600><c> in</c><00:00:59.760><c> java</c>

00:01:01.189 --> 00:01:01.199 align:start position:0%
also a special method in java
 

00:01:01.199 --> 00:01:02.950 align:start position:0%
also a special method in java
within<00:01:01.520><c> the</c><00:01:01.600><c> left</c><00:01:01.840><c> and</c><00:01:01.920><c> right</c><00:01:02.079><c> parenthesis</c>

00:01:02.950 --> 00:01:02.960 align:start position:0%
within the left and right parenthesis
 

00:01:02.960 --> 00:01:05.270 align:start position:0%
within the left and right parenthesis
are<00:01:03.120><c> the</c><00:01:03.199><c> parameters</c><00:01:03.840><c> passed</c><00:01:04.080><c> to</c><00:01:04.239><c> this</c><00:01:04.479><c> method</c>

00:01:05.270 --> 00:01:05.280 align:start position:0%
are the parameters passed to this method
 

00:01:05.280 --> 00:01:07.510 align:start position:0%
are the parameters passed to this method
in<00:01:05.439><c> this</c><00:01:05.760><c> case</c><00:01:06.240><c> we</c><00:01:06.400><c> have</c><00:01:06.560><c> a</c><00:01:06.640><c> type</c><00:01:06.960><c> of</c><00:01:07.040><c> string</c>

00:01:07.510 --> 00:01:07.520 align:start position:0%
in this case we have a type of string
 

00:01:07.520 --> 00:01:08.710 align:start position:0%
in this case we have a type of string
array

00:01:08.710 --> 00:01:08.720 align:start position:0%
array
 

00:01:08.720 --> 00:01:12.149 align:start position:0%
array
and<00:01:08.880><c> assigning</c><00:01:09.600><c> the</c><00:01:09.760><c> name</c><00:01:10.000><c> to</c><00:01:10.240><c> it</c><00:01:10.840><c> args</c>

00:01:12.149 --> 00:01:12.159 align:start position:0%
and assigning the name to it args
 

00:01:12.159 --> 00:01:13.990 align:start position:0%
and assigning the name to it args
here's<00:01:12.479><c> the</c><00:01:12.560><c> curly</c><00:01:12.880><c> brace</c><00:01:13.360><c> for</c><00:01:13.520><c> the</c><00:01:13.680><c> opening</c>

00:01:13.990 --> 00:01:14.000 align:start position:0%
here's the curly brace for the opening
 

00:01:14.000 --> 00:01:17.149 align:start position:0%
here's the curly brace for the opening
of<00:01:14.080><c> the</c><00:01:14.159><c> method</c><00:01:14.479><c> body</c>

00:01:17.149 --> 00:01:17.159 align:start position:0%
 
 

00:01:17.159 --> 00:01:18.950 align:start position:0%
 
system.out.println<00:01:18.320><c> is</c><00:01:18.400><c> the</c><00:01:18.479><c> name</c><00:01:18.720><c> of</c><00:01:18.799><c> the</c>

00:01:18.950 --> 00:01:18.960 align:start position:0%
system.out.println is the name of the
 

00:01:18.960 --> 00:01:20.789 align:start position:0%
system.out.println is the name of the
method<00:01:19.360><c> we</c><00:01:19.520><c> want</c><00:01:19.680><c> to</c><00:01:19.920><c> call</c>

00:01:20.789 --> 00:01:20.799 align:start position:0%
method we want to call
 

00:01:20.799 --> 00:01:22.310 align:start position:0%
method we want to call
in<00:01:20.880><c> this</c><00:01:21.119><c> case</c><00:01:21.439><c> this</c><00:01:21.600><c> is</c><00:01:21.759><c> the</c><00:01:21.840><c> method</c><00:01:22.159><c> for</c>

00:01:22.310 --> 00:01:22.320 align:start position:0%
in this case this is the method for
 

00:01:22.320 --> 00:01:24.870 align:start position:0%
in this case this is the method for
printing<00:01:22.720><c> strings</c><00:01:23.280><c> on</c><00:01:23.759><c> to</c><00:01:23.920><c> the</c><00:01:24.000><c> screen</c>

00:01:24.870 --> 00:01:24.880 align:start position:0%
printing strings on to the screen
 

00:01:24.880 --> 00:01:27.270 align:start position:0%
printing strings on to the screen
within<00:01:25.280><c> the</c><00:01:25.439><c> left</c><00:01:25.680><c> and</c><00:01:25.759><c> right</c><00:01:26.000><c> parenthesis</c><00:01:27.119><c> we</c>

00:01:27.270 --> 00:01:27.280 align:start position:0%
within the left and right parenthesis we
 

00:01:27.280 --> 00:01:28.870 align:start position:0%
within the left and right parenthesis we
have<00:01:27.439><c> the</c><00:01:27.759><c> parameter</c><00:01:28.240><c> being</c><00:01:28.479><c> passed</c>

00:01:28.870 --> 00:01:28.880 align:start position:0%
have the parameter being passed
 

00:01:28.880 --> 00:01:31.510 align:start position:0%
have the parameter being passed
the<00:01:28.960><c> method</c><00:01:29.439><c> in</c><00:01:29.520><c> this</c><00:01:29.759><c> case</c><00:01:30.560><c> hello</c><00:01:30.960><c> world</c><00:01:31.360><c> is</c>

00:01:31.510 --> 00:01:31.520 align:start position:0%
the method in this case hello world is
 

00:01:31.520 --> 00:01:33.830 align:start position:0%
the method in this case hello world is
the<00:01:31.600><c> string</c><00:01:32.079><c> we</c><00:01:32.240><c> want</c><00:01:32.400><c> to</c><00:01:32.560><c> print</c>

00:01:33.830 --> 00:01:33.840 align:start position:0%
the string we want to print
 

00:01:33.840 --> 00:01:36.230 align:start position:0%
the string we want to print
the<00:01:34.000><c> semicolon</c><00:01:34.799><c> indicates</c><00:01:35.280><c> the</c><00:01:35.520><c> end</c><00:01:35.840><c> of</c><00:01:36.000><c> this</c>

00:01:36.230 --> 00:01:36.240 align:start position:0%
the semicolon indicates the end of this
 

00:01:36.240 --> 00:01:37.429 align:start position:0%
the semicolon indicates the end of this
statement

00:01:37.429 --> 00:01:37.439 align:start position:0%
statement
 

00:01:37.439 --> 00:01:39.190 align:start position:0%
statement
this<00:01:37.680><c> curly</c><00:01:38.000><c> brace</c><00:01:38.320><c> is</c><00:01:38.400><c> used</c><00:01:38.560><c> for</c><00:01:38.799><c> closing</c><00:01:39.119><c> the</c>

00:01:39.190 --> 00:01:39.200 align:start position:0%
this curly brace is used for closing the
 

00:01:39.200 --> 00:01:41.590 align:start position:0%
this curly brace is used for closing the
method<00:01:39.520><c> body</c>

00:01:41.590 --> 00:01:41.600 align:start position:0%
method body
 

00:01:41.600 --> 00:01:43.109 align:start position:0%
method body
and<00:01:41.680><c> then</c><00:01:41.840><c> this</c><00:01:42.000><c> curly</c><00:01:42.320><c> brace</c><00:01:42.640><c> is</c><00:01:42.799><c> used</c><00:01:42.960><c> for</c>

00:01:43.109 --> 00:01:43.119 align:start position:0%
and then this curly brace is used for
 

00:01:43.119 --> 00:01:54.550 align:start position:0%
and then this curly brace is used for
closing<00:01:43.680><c> the</c><00:01:52.840><c> class</c>

00:01:54.550 --> 00:01:54.560 align:start position:0%
closing the class
 

00:01:54.560 --> 00:01:56.640 align:start position:0%
closing the class
you

