WEBVTT
Kind: captions
Language: en

00:00:00.199 --> 00:00:02.389 align:start position:0%
 
okay<00:00:00.440><c> so</c><00:00:00.640><c> huging</c><00:00:01.079><c> face</c><00:00:01.360><c> has</c><00:00:01.520><c> released</c><00:00:01.920><c> a</c><00:00:02.120><c> new</c>

00:00:02.389 --> 00:00:02.399 align:start position:0%
okay so huging face has released a new
 

00:00:02.399 --> 00:00:05.749 align:start position:0%
okay so huging face has released a new
model<00:00:02.919><c> called</c><00:00:03.240><c> zepha</c><00:00:03.760><c> 7B</c><00:00:04.560><c> Alpha</c><00:00:05.440><c> this</c><00:00:05.520><c> is</c><00:00:05.640><c> a</c>

00:00:05.749 --> 00:00:05.759 align:start position:0%
model called zepha 7B Alpha this is a
 

00:00:05.759 --> 00:00:07.990 align:start position:0%
model called zepha 7B Alpha this is a
model<00:00:06.040><c> that's</c><00:00:06.279><c> basically</c><00:00:06.600><c> a</c><00:00:06.799><c> fine-tuning</c><00:00:07.759><c> of</c>

00:00:07.990 --> 00:00:08.000 align:start position:0%
model that's basically a fine-tuning of
 

00:00:08.000 --> 00:00:10.589 align:start position:0%
model that's basically a fine-tuning of
the<00:00:08.120><c> mistal</c><00:00:08.599><c> model</c><00:00:09.440><c> but</c><00:00:09.559><c> it's</c><00:00:09.719><c> got</c><00:00:09.880><c> a</c><00:00:09.960><c> few</c><00:00:10.320><c> new</c>

00:00:10.589 --> 00:00:10.599 align:start position:0%
the mistal model but it's got a few new
 

00:00:10.599 --> 00:00:12.589 align:start position:0%
the mistal model but it's got a few new
sort<00:00:10.800><c> of</c><00:00:10.960><c> ways</c><00:00:11.280><c> about</c><00:00:11.679><c> the</c><00:00:11.880><c> recipe</c><00:00:12.320><c> that</c><00:00:12.480><c> they</c>

00:00:12.589 --> 00:00:12.599 align:start position:0%
sort of ways about the recipe that they
 

00:00:12.599 --> 00:00:14.230 align:start position:0%
sort of ways about the recipe that they
used<00:00:12.960><c> for</c><00:00:13.120><c> the</c><00:00:13.240><c> fine</c><00:00:13.480><c> tuning</c><00:00:13.839><c> which</c><00:00:14.000><c> I</c><00:00:14.080><c> think</c>

00:00:14.230 --> 00:00:14.240 align:start position:0%
used for the fine tuning which I think
 

00:00:14.240 --> 00:00:16.310 align:start position:0%
used for the fine tuning which I think
is<00:00:14.400><c> pretty</c><00:00:14.719><c> interesting</c><00:00:15.679><c> so</c><00:00:15.839><c> they</c><00:00:16.000><c> started</c>

00:00:16.310 --> 00:00:16.320 align:start position:0%
is pretty interesting so they started
 

00:00:16.320 --> 00:00:18.750 align:start position:0%
is pretty interesting so they started
off<00:00:16.440><c> doing</c><00:00:16.720><c> supervised</c><00:00:17.320><c> fine</c><00:00:17.600><c> tuning</c><00:00:18.480><c> with</c><00:00:18.600><c> a</c>

00:00:18.750 --> 00:00:18.760 align:start position:0%
off doing supervised fine tuning with a
 

00:00:18.760 --> 00:00:21.070 align:start position:0%
off doing supervised fine tuning with a
data<00:00:19.039><c> set</c><00:00:19.279><c> called</c><00:00:19.520><c> Ultra</c><00:00:20.000><c> chat</c><00:00:20.680><c> and</c><00:00:20.800><c> they</c><00:00:20.880><c> talk</c>

00:00:21.070 --> 00:00:21.080 align:start position:0%
data set called Ultra chat and they talk
 

00:00:21.080 --> 00:00:22.670 align:start position:0%
data set called Ultra chat and they talk
about<00:00:21.279><c> that</c><00:00:21.400><c> they</c><00:00:21.519><c> originally</c><00:00:21.960><c> did</c><00:00:22.199><c> the</c><00:00:22.320><c> full</c>

00:00:22.670 --> 00:00:22.680 align:start position:0%
about that they originally did the full
 

00:00:22.680 --> 00:00:24.630 align:start position:0%
about that they originally did the full
data<00:00:23.000><c> set</c><00:00:23.320><c> but</c><00:00:23.480><c> they</c><00:00:23.640><c> found</c><00:00:24.119><c> that</c><00:00:24.400><c> actually</c>

00:00:24.630 --> 00:00:24.640 align:start position:0%
data set but they found that actually
 

00:00:24.640 --> 00:00:26.589 align:start position:0%
data set but they found that actually
that<00:00:24.800><c> didn't</c><00:00:25.119><c> create</c><00:00:25.560><c> what</c><00:00:25.720><c> they</c><00:00:25.880><c> wanted</c><00:00:26.400><c> in</c>

00:00:26.589 --> 00:00:26.599 align:start position:0%
that didn't create what they wanted in
 

00:00:26.599 --> 00:00:29.109 align:start position:0%
that didn't create what they wanted in
regards<00:00:27.039><c> to</c><00:00:27.640><c> the</c><00:00:27.840><c> personality</c><00:00:28.599><c> of</c><00:00:28.840><c> the</c>

00:00:29.109 --> 00:00:29.119 align:start position:0%
regards to the personality of the
 

00:00:29.119 --> 00:00:30.550 align:start position:0%
regards to the personality of the
chatbot

00:00:30.550 --> 00:00:30.560 align:start position:0%
chatbot
 

00:00:30.560 --> 00:00:34.630 align:start position:0%
chatbot
so<00:00:30.960><c> zepa</c><00:00:31.480><c> 7B</c><00:00:32.320><c> is</c><00:00:32.480><c> the</c><00:00:32.719><c> first</c><00:00:33.079><c> in</c><00:00:33.239><c> a</c><00:00:33.559><c> series</c><00:00:34.239><c> of</c>

00:00:34.630 --> 00:00:34.640 align:start position:0%
so zepa 7B is the first in a series of
 

00:00:34.640 --> 00:00:37.709 align:start position:0%
so zepa 7B is the first in a series of
sort<00:00:34.840><c> of</c><00:00:35.239><c> chat</c><00:00:36.239><c> models</c><00:00:36.680><c> I'm</c><00:00:36.840><c> guessing</c><00:00:37.040><c> sort</c><00:00:37.239><c> of</c>

00:00:37.709 --> 00:00:37.719 align:start position:0%
sort of chat models I'm guessing sort of
 

00:00:37.719 --> 00:00:39.670 align:start position:0%
sort of chat models I'm guessing sort of
instruct<00:00:38.160><c> models</c><00:00:38.480><c> as</c><00:00:38.600><c> well</c><00:00:38.800><c> that</c><00:00:38.960><c> they're</c><00:00:39.480><c> are</c>

00:00:39.670 --> 00:00:39.680 align:start position:0%
instruct models as well that they're are
 

00:00:39.680 --> 00:00:41.790 align:start position:0%
instruct models as well that they're are
trying<00:00:39.920><c> to</c><00:00:40.079><c> train</c><00:00:40.760><c> the</c><00:00:40.879><c> idea</c><00:00:41.160><c> with</c><00:00:41.320><c> these</c><00:00:41.640><c> is</c>

00:00:41.790 --> 00:00:41.800 align:start position:0%
trying to train the idea with these is
 

00:00:41.800 --> 00:00:43.389 align:start position:0%
trying to train the idea with these is
they're<00:00:41.960><c> trying</c><00:00:42.160><c> to</c><00:00:42.280><c> make</c><00:00:42.480><c> them</c><00:00:42.680><c> helpful</c>

00:00:43.389 --> 00:00:43.399 align:start position:0%
they're trying to make them helpful
 

00:00:43.399 --> 00:00:45.830 align:start position:0%
they're trying to make them helpful
assistance<00:00:44.399><c> so</c><00:00:45.000><c> it</c><00:00:45.079><c> is</c><00:00:45.239><c> very</c><00:00:45.440><c> interesting</c>

00:00:45.830 --> 00:00:45.840 align:start position:0%
assistance so it is very interesting
 

00:00:45.840 --> 00:00:47.029 align:start position:0%
assistance so it is very interesting
that<00:00:46.000><c> they</c><00:00:46.120><c> seem</c><00:00:46.280><c> to</c><00:00:46.399><c> be</c><00:00:46.520><c> trying</c><00:00:46.800><c> out</c>

00:00:47.029 --> 00:00:47.039 align:start position:0%
that they seem to be trying out
 

00:00:47.039 --> 00:00:49.310 align:start position:0%
that they seem to be trying out
different<00:00:47.440><c> recipes</c><00:00:48.280><c> to</c><00:00:48.480><c> work</c><00:00:48.680><c> out</c><00:00:48.960><c> what's</c><00:00:49.160><c> the</c>

00:00:49.310 --> 00:00:49.320 align:start position:0%
different recipes to work out what's the
 

00:00:49.320 --> 00:00:51.630 align:start position:0%
different recipes to work out what's the
best<00:00:49.559><c> recipe</c><00:00:49.920><c> to</c><00:00:50.079><c> make</c><00:00:50.280><c> a</c><00:00:50.480><c> helpful</c><00:00:50.960><c> assistant</c>

00:00:51.630 --> 00:00:51.640 align:start position:0%
best recipe to make a helpful assistant
 

00:00:51.640 --> 00:00:53.630 align:start position:0%
best recipe to make a helpful assistant
for<00:00:51.879><c> you</c><00:00:52.120><c> based</c><00:00:52.359><c> on</c><00:00:52.520><c> what</c><00:00:52.680><c> data</c><00:00:53.079><c> set</c><00:00:53.480><c> what</c>

00:00:53.630 --> 00:00:53.640 align:start position:0%
for you based on what data set what
 

00:00:53.640 --> 00:00:55.830 align:start position:0%
for you based on what data set what
techniques<00:00:54.280><c> Etc</c><00:00:55.160><c> it's</c><00:00:55.320><c> also</c><00:00:55.520><c> a</c><00:00:55.640><c> very</c>

00:00:55.830 --> 00:00:55.840 align:start position:0%
techniques Etc it's also a very
 

00:00:55.840 --> 00:00:57.910 align:start position:0%
techniques Etc it's also a very
interesting<00:00:56.359><c> project</c><00:00:56.719><c> in</c><00:00:56.960><c> this</c><00:00:57.480><c> one</c><00:00:57.600><c> of</c><00:00:57.760><c> the</c>

00:00:57.910 --> 00:00:57.920 align:start position:0%
interesting project in this one of the
 

00:00:57.920 --> 00:01:01.189 align:start position:0%
interesting project in this one of the
first<00:00:58.440><c> where</c><00:00:59.160><c> they're</c><00:00:59.480><c> actually</c><00:01:00.079><c> using</c><00:01:01.039><c> a</c>

00:01:01.189 --> 00:01:01.199 align:start position:0%
first where they're actually using a
 

00:01:01.199 --> 00:01:03.670 align:start position:0%
first where they're actually using a
sort<00:01:01.399><c> of</c><00:01:01.960><c> a</c><00:01:02.519><c> reinforcement</c><00:01:03.320><c> learning</c>

00:01:03.670 --> 00:01:03.680 align:start position:0%
sort of a reinforcement learning
 

00:01:03.680 --> 00:01:05.509 align:start position:0%
sort of a reinforcement learning
feedback<00:01:04.119><c> system</c><00:01:04.519><c> here</c><00:01:04.920><c> so</c><00:01:05.119><c> that</c><00:01:05.280><c> they're</c>

00:01:05.509 --> 00:01:05.519 align:start position:0%
feedback system here so that they're
 

00:01:05.519 --> 00:01:07.310 align:start position:0%
feedback system here so that they're
actually<00:01:05.720><c> training</c><00:01:06.159><c> it</c><00:01:06.479><c> with</c><00:01:06.840><c> uh</c><00:01:07.000><c> direct</c>

00:01:07.310 --> 00:01:07.320 align:start position:0%
actually training it with uh direct
 

00:01:07.320 --> 00:01:09.670 align:start position:0%
actually training it with uh direct
preference<00:01:08.119><c> optimization</c><00:01:09.119><c> which</c><00:01:09.240><c> is</c><00:01:09.360><c> an</c>

00:01:09.670 --> 00:01:09.680 align:start position:0%
preference optimization which is an
 

00:01:09.680 --> 00:01:12.670 align:start position:0%
preference optimization which is an
alternative<00:01:10.479><c> to</c><00:01:10.759><c> rlh</c><00:01:11.479><c> jef</c><00:01:12.280><c> and</c><00:01:12.400><c> they've</c>

00:01:12.670 --> 00:01:12.680 align:start position:0%
alternative to rlh jef and they've
 

00:01:12.680 --> 00:01:14.830 align:start position:0%
alternative to rlh jef and they've
documented<00:01:13.200><c> the</c><00:01:13.320><c> whole</c><00:01:13.560><c> thing</c><00:01:13.920><c> there</c><00:01:14.560><c> so</c><00:01:14.720><c> they</c>

00:01:14.830 --> 00:01:14.840 align:start position:0%
documented the whole thing there so they
 

00:01:14.840 --> 00:01:17.590 align:start position:0%
documented the whole thing there so they
started<00:01:15.159><c> off</c><00:01:15.320><c> with</c><00:01:15.439><c> the</c><00:01:15.560><c> mistal</c><00:01:15.960><c> 7B</c><00:01:16.520><c> model</c><00:01:17.360><c> and</c>

00:01:17.590 --> 00:01:17.600 align:start position:0%
started off with the mistal 7B model and
 

00:01:17.600 --> 00:01:19.789 align:start position:0%
started off with the mistal 7B model and
they've<00:01:17.880><c> basically</c><00:01:18.320><c> taken</c><00:01:18.680><c> that</c><00:01:18.960><c> and</c><00:01:19.479><c> done</c><00:01:19.640><c> a</c>

00:01:19.789 --> 00:01:19.799 align:start position:0%
they've basically taken that and done a
 

00:01:19.799 --> 00:01:22.870 align:start position:0%
they've basically taken that and done a
supervised<00:01:20.400><c> fine</c><00:01:20.799><c> tuning</c><00:01:21.799><c> on</c><00:01:21.960><c> a</c><00:01:22.159><c> data</c><00:01:22.520><c> set</c>

00:01:22.870 --> 00:01:22.880 align:start position:0%
supervised fine tuning on a data set
 

00:01:22.880 --> 00:01:25.270 align:start position:0%
supervised fine tuning on a data set
called<00:01:23.200><c> Ultra</c><00:01:23.640><c> chat</c><00:01:24.280><c> so</c><00:01:24.479><c> as</c><00:01:24.600><c> far</c><00:01:24.720><c> as</c><00:01:24.840><c> I</c>

00:01:25.270 --> 00:01:25.280 align:start position:0%
called Ultra chat so as far as I
 

00:01:25.280 --> 00:01:27.870 align:start position:0%
called Ultra chat so as far as I
understand<00:01:25.479><c> Ultra</c><00:01:25.920><c> chat</c><00:01:26.280><c> is</c><00:01:26.720><c> basically</c><00:01:27.360><c> a</c>

00:01:27.870 --> 00:01:27.880 align:start position:0%
understand Ultra chat is basically a
 

00:01:27.880 --> 00:01:30.910 align:start position:0%
understand Ultra chat is basically a
multi-turn<00:01:28.880><c> sort</c><00:01:29.079><c> of</c><00:01:29.240><c> conversation</c><00:01:30.159><c> dialogue</c>

00:01:30.910 --> 00:01:30.920 align:start position:0%
multi-turn sort of conversation dialogue
 

00:01:30.920 --> 00:01:32.910 align:start position:0%
multi-turn sort of conversation dialogue
data<00:01:31.280><c> set</c><00:01:31.600><c> here</c><00:01:32.200><c> and</c><00:01:32.320><c> they</c><00:01:32.399><c> mentioned</c><00:01:32.720><c> that</c>

00:01:32.910 --> 00:01:32.920 align:start position:0%
data set here and they mentioned that
 

00:01:32.920 --> 00:01:34.350 align:start position:0%
data set here and they mentioned that
they<00:01:33.119><c> started</c><00:01:33.399><c> on</c><00:01:33.560><c> training</c><00:01:33.840><c> on</c><00:01:33.920><c> the</c><00:01:34.040><c> full</c>

00:01:34.350 --> 00:01:34.360 align:start position:0%
they started on training on the full
 

00:01:34.360 --> 00:01:37.550 align:start position:0%
they started on training on the full
data<00:01:34.680><c> set</c><00:01:34.960><c> which</c><00:01:35.040><c> is</c><00:01:35.240><c> about</c><00:01:35.600><c> 770,000</c><00:01:36.600><c> and</c>

00:01:37.550 --> 00:01:37.560 align:start position:0%
data set which is about 770,000 and
 

00:01:37.560 --> 00:01:39.950 align:start position:0%
data set which is about 770,000 and
found<00:01:38.000><c> that</c><00:01:38.280><c> actually</c><00:01:38.680><c> that</c><00:01:39.159><c> gave</c><00:01:39.399><c> them</c><00:01:39.560><c> not</c><00:01:39.759><c> a</c>

00:01:39.950 --> 00:01:39.960 align:start position:0%
found that actually that gave them not a
 

00:01:39.960 --> 00:01:42.190 align:start position:0%
found that actually that gave them not a
personality<00:01:40.560><c> that</c><00:01:40.720><c> they</c><00:01:40.840><c> wanted</c><00:01:41.399><c> so</c><00:01:41.720><c> they</c>

00:01:42.190 --> 00:01:42.200 align:start position:0%
personality that they wanted so they
 

00:01:42.200 --> 00:01:44.910 align:start position:0%
personality that they wanted so they
talk<00:01:42.439><c> about</c><00:01:42.640><c> filtering</c><00:01:43.079><c> it</c><00:01:43.320><c> down</c><00:01:43.640><c> to</c><00:01:44.079><c> 200,000</c>

00:01:44.910 --> 00:01:44.920 align:start position:0%
talk about filtering it down to 200,000
 

00:01:44.920 --> 00:01:48.069 align:start position:0%
talk about filtering it down to 200,000
examples<00:01:45.439><c> in</c><00:01:45.680><c> here</c><00:01:46.560><c> then</c><00:01:46.799><c> they</c><00:01:46.920><c> went</c><00:01:47.119><c> to</c><00:01:47.640><c> the</c>

00:01:48.069 --> 00:01:48.079 align:start position:0%
examples in here then they went to the
 

00:01:48.079 --> 00:01:50.990 align:start position:0%
examples in here then they went to the
DPO<00:01:49.079><c> for</c><00:01:49.479><c> this</c><00:01:49.960><c> and</c><00:01:50.200><c> so</c><00:01:50.479><c> this</c><00:01:50.600><c> is</c><00:01:50.719><c> where</c><00:01:50.920><c> I</c>

00:01:50.990 --> 00:01:51.000 align:start position:0%
DPO for this and so this is where I
 

00:01:51.000 --> 00:01:52.510 align:start position:0%
DPO for this and so this is where I
think<00:01:51.240><c> this</c><00:01:51.439><c> project</c><00:01:51.759><c> definitely</c><00:01:52.079><c> is</c><00:01:52.240><c> kind</c><00:01:52.360><c> of</c>

00:01:52.510 --> 00:01:52.520 align:start position:0%
think this project definitely is kind of
 

00:01:52.520 --> 00:01:54.389 align:start position:0%
think this project definitely is kind of
interesting<00:01:52.960><c> to</c><00:01:53.240><c> compare</c><00:01:53.640><c> to</c><00:01:53.920><c> what</c><00:01:54.040><c> a</c><00:01:54.119><c> lot</c><00:01:54.240><c> of</c>

00:01:54.389 --> 00:01:54.399 align:start position:0%
interesting to compare to what a lot of
 

00:01:54.399 --> 00:01:57.109 align:start position:0%
interesting to compare to what a lot of
other<00:01:54.640><c> people</c><00:01:55.280><c> are</c><00:01:55.479><c> doing</c><00:01:55.880><c> for</c><00:01:56.560><c> alignment</c>

00:01:57.109 --> 00:01:57.119 align:start position:0%
other people are doing for alignment
 

00:01:57.119 --> 00:01:59.230 align:start position:0%
other people are doing for alignment
training<00:01:57.799><c> where</c><00:01:57.960><c> they're</c><00:01:58.200><c> purely</c><00:01:58.640><c> using</c>

00:01:59.230 --> 00:01:59.240 align:start position:0%
training where they're purely using
 

00:01:59.240 --> 00:02:01.910 align:start position:0%
training where they're purely using
supervisor<00:01:59.880><c> fun</c><00:02:00.159><c> tuning</c><00:02:01.159><c> and</c><00:02:01.280><c> I</c><00:02:01.360><c> think</c><00:02:01.520><c> that</c><00:02:01.759><c> a</c>

00:02:01.910 --> 00:02:01.920 align:start position:0%
supervisor fun tuning and I think that a
 

00:02:01.920 --> 00:02:03.950 align:start position:0%
supervisor fun tuning and I think that a
number<00:02:02.159><c> of</c><00:02:02.399><c> people</c><00:02:02.759><c> myself</c><00:02:03.159><c> included</c><00:02:03.759><c> have</c>

00:02:03.950 --> 00:02:03.960 align:start position:0%
number of people myself included have
 

00:02:03.960 --> 00:02:06.149 align:start position:0%
number of people myself included have
been<00:02:04.479><c> a</c><00:02:04.600><c> little</c><00:02:04.759><c> bit</c><00:02:05.000><c> suspicious</c><00:02:05.520><c> of</c><00:02:05.840><c> how</c><00:02:06.000><c> much</c>

00:02:06.149 --> 00:02:06.159 align:start position:0%
been a little bit suspicious of how much
 

00:02:06.159 --> 00:02:09.350 align:start position:0%
been a little bit suspicious of how much
do<00:02:06.320><c> we</c><00:02:06.600><c> actually</c><00:02:07.000><c> need</c><00:02:07.840><c> rhf</c><00:02:08.759><c> for</c><00:02:09.000><c> these</c><00:02:09.239><c> kind</c>

00:02:09.350 --> 00:02:09.360 align:start position:0%
do we actually need rhf for these kind
 

00:02:09.360 --> 00:02:11.270 align:start position:0%
do we actually need rhf for these kind
of<00:02:09.560><c> things</c><00:02:10.440><c> there</c><00:02:10.520><c> have</c><00:02:10.640><c> been</c><00:02:10.800><c> a</c><00:02:10.920><c> number</c><00:02:11.120><c> of</c>

00:02:11.270 --> 00:02:11.280 align:start position:0%
of things there have been a number of
 

00:02:11.280 --> 00:02:13.550 align:start position:0%
of things there have been a number of
signs<00:02:11.640><c> in</c><00:02:11.760><c> the</c><00:02:12.000><c> past</c><00:02:12.280><c> that</c><00:02:12.480><c> openai</c><00:02:13.160><c> perhaps</c>

00:02:13.550 --> 00:02:13.560 align:start position:0%
signs in the past that openai perhaps
 

00:02:13.560 --> 00:02:16.470 align:start position:0%
signs in the past that openai perhaps
wasn't<00:02:13.959><c> using</c><00:02:14.319><c> rhf</c><00:02:15.239><c> when</c><00:02:15.480><c> they</c><00:02:15.959><c> said</c><00:02:16.319><c> they</c>

00:02:16.470 --> 00:02:16.480 align:start position:0%
wasn't using rhf when they said they
 

00:02:16.480 --> 00:02:19.350 align:start position:0%
wasn't using rhf when they said they
were<00:02:17.360><c> so</c><00:02:17.680><c> this</c><00:02:17.959><c> is</c><00:02:18.200><c> an</c><00:02:18.400><c> interesting</c><00:02:19.000><c> project</c>

00:02:19.350 --> 00:02:19.360 align:start position:0%
were so this is an interesting project
 

00:02:19.360 --> 00:02:23.030 align:start position:0%
were so this is an interesting project
to<00:02:19.560><c> see</c><00:02:20.120><c> okay</c><00:02:20.640><c> how</c><00:02:20.800><c> much</c><00:02:21.040><c> does</c><00:02:21.640><c> rlf</c><00:02:22.640><c> actually</c>

00:02:23.030 --> 00:02:23.040 align:start position:0%
to see okay how much does rlf actually
 

00:02:23.040 --> 00:02:24.949 align:start position:0%
to see okay how much does rlf actually
affect<00:02:23.319><c> the</c><00:02:23.440><c> model</c><00:02:24.120><c> I</c><00:02:24.239><c> would</c><00:02:24.360><c> have</c><00:02:24.519><c> loved</c><00:02:24.760><c> it</c>

00:02:24.949 --> 00:02:24.959 align:start position:0%
affect the model I would have loved it
 

00:02:24.959 --> 00:02:28.589 align:start position:0%
affect the model I would have loved it
if<00:02:25.120><c> they</c><00:02:25.319><c> released</c><00:02:25.959><c> the</c><00:02:26.959><c> sft</c><00:02:27.680><c> only</c><00:02:28.319><c> fine</c>

00:02:28.589 --> 00:02:28.599 align:start position:0%
if they released the sft only fine
 

00:02:28.599 --> 00:02:30.390 align:start position:0%
if they released the sft only fine
tuning<00:02:28.920><c> of</c><00:02:29.080><c> this</c><00:02:29.200><c> so</c><00:02:29.360><c> we</c><00:02:29.480><c> could</c><00:02:29.599><c> have</c><00:02:29.879><c> compared</c>

00:02:30.390 --> 00:02:30.400 align:start position:0%
tuning of this so we could have compared
 

00:02:30.400 --> 00:02:33.150 align:start position:0%
tuning of this so we could have compared
how<00:02:30.599><c> much</c><00:02:30.840><c> the</c><00:02:31.000><c> DPO</c><00:02:31.920><c> and</c><00:02:32.080><c> the</c><00:02:32.200><c> other</c><00:02:32.680><c> data</c><00:02:32.959><c> set</c>

00:02:33.150 --> 00:02:33.160 align:start position:0%
how much the DPO and the other data set
 

00:02:33.160 --> 00:02:34.670 align:start position:0%
how much the DPO and the other data set
has<00:02:33.319><c> actually</c><00:02:33.599><c> affected</c><00:02:34.000><c> this</c><00:02:34.360><c> but</c>

00:02:34.670 --> 00:02:34.680 align:start position:0%
has actually affected this but
 

00:02:34.680 --> 00:02:35.750 align:start position:0%
has actually affected this but
unfortunately<00:02:35.200><c> I</c><00:02:35.239><c> think</c><00:02:35.400><c> they've</c><00:02:35.599><c> just</c>

00:02:35.750 --> 00:02:35.760 align:start position:0%
unfortunately I think they've just
 

00:02:35.760 --> 00:02:38.309 align:start position:0%
unfortunately I think they've just
released<00:02:36.239><c> this</c><00:02:36.440><c> one</c><00:02:37.160><c> anyway</c><00:02:37.560><c> they've</c><00:02:37.800><c> tested</c>

00:02:38.309 --> 00:02:38.319 align:start position:0%
released this one anyway they've tested
 

00:02:38.319 --> 00:02:41.589 align:start position:0%
released this one anyway they've tested
on<00:02:38.680><c> Mt</c><00:02:39.120><c> bench</c><00:02:40.080><c> which</c><00:02:40.239><c> is</c><00:02:40.360><c> a</c><00:02:40.599><c> multi-turn</c>

00:02:41.589 --> 00:02:41.599 align:start position:0%
on Mt bench which is a multi-turn
 

00:02:41.599 --> 00:02:43.790 align:start position:0%
on Mt bench which is a multi-turn
benchmarking<00:02:42.319><c> system</c><00:02:43.080><c> and</c><00:02:43.239><c> they</c><00:02:43.360><c> find</c><00:02:43.640><c> that</c>

00:02:43.790 --> 00:02:43.800 align:start position:0%
benchmarking system and they find that
 

00:02:43.800 --> 00:02:46.630 align:start position:0%
benchmarking system and they find that
it<00:02:43.920><c> actually</c><00:02:44.159><c> gets</c><00:02:44.560><c> better</c><00:02:45.280><c> scores</c><00:02:46.280><c> than</c><00:02:46.519><c> the</c>

00:02:46.630 --> 00:02:46.640 align:start position:0%
it actually gets better scores than the
 

00:02:46.640 --> 00:02:49.589 align:start position:0%
it actually gets better scores than the
Llama<00:02:47.080><c> 2</c><00:02:47.519><c> 70</c><00:02:48.040><c> billion</c><00:02:48.480><c> chat</c><00:02:48.879><c> model</c><00:02:49.280><c> which</c><00:02:49.400><c> is</c>

00:02:49.589 --> 00:02:49.599 align:start position:0%
Llama 2 70 billion chat model which is
 

00:02:49.599 --> 00:02:51.470 align:start position:0%
Llama 2 70 billion chat model which is
definitely<00:02:50.040><c> very</c><00:02:50.280><c> impressive</c><00:02:51.159><c> so</c><00:02:51.319><c> if</c><00:02:51.360><c> you</c>

00:02:51.470 --> 00:02:51.480 align:start position:0%
definitely very impressive so if you
 

00:02:51.480 --> 00:02:52.670 align:start position:0%
definitely very impressive so if you
jump<00:02:51.680><c> in</c><00:02:51.800><c> and</c><00:02:51.920><c> have</c><00:02:52.040><c> a</c><00:02:52.159><c> look</c><00:02:52.440><c> you</c><00:02:52.519><c> can</c>

00:02:52.670 --> 00:02:52.680 align:start position:0%
jump in and have a look you can
 

00:02:52.680 --> 00:02:54.869 align:start position:0%
jump in and have a look you can
definitely<00:02:53.000><c> have</c><00:02:53.080><c> a</c><00:02:53.280><c> play</c><00:02:53.560><c> with</c><00:02:53.840><c> this</c><00:02:54.159><c> in</c><00:02:54.720><c> the</c>

00:02:54.869 --> 00:02:54.879 align:start position:0%
definitely have a play with this in the
 

00:02:54.879 --> 00:02:57.910 align:start position:0%
definitely have a play with this in the
browser<00:02:55.400><c> they've</c><00:02:55.640><c> put</c><00:02:55.840><c> up</c><00:02:56.239><c> a</c><00:02:56.959><c> huging</c><00:02:57.599><c> face</c>

00:02:57.910 --> 00:02:57.920 align:start position:0%
browser they've put up a huging face
 

00:02:57.920 --> 00:03:00.229 align:start position:0%
browser they've put up a huging face
chat<00:02:58.480><c> kind</c><00:02:58.599><c> of</c><00:02:58.760><c> thing</c><00:02:58.920><c> for</c><00:02:59.280><c> this</c><00:02:59.879><c> and</c><00:03:00.000><c> you're</c>

00:03:00.229 --> 00:03:00.239 align:start position:0%
chat kind of thing for this and you're
 

00:03:00.239 --> 00:03:01.990 align:start position:0%
chat kind of thing for this and you're
able<00:03:00.440><c> to</c><00:03:00.599><c> then</c><00:03:00.800><c> ask</c><00:03:01.000><c> it</c><00:03:01.200><c> a</c><00:03:01.319><c> bunch</c><00:03:01.480><c> of</c><00:03:01.680><c> different</c>

00:03:01.990 --> 00:03:02.000 align:start position:0%
able to then ask it a bunch of different
 

00:03:02.000 --> 00:03:04.430 align:start position:0%
able to then ask it a bunch of different
things<00:03:02.720><c> and</c><00:03:02.840><c> it</c><00:03:02.959><c> can</c><00:03:03.080><c> do</c><00:03:03.319><c> things</c><00:03:03.680><c> both</c><00:03:04.040><c> like</c>

00:03:04.430 --> 00:03:04.440 align:start position:0%
things and it can do things both like
 

00:03:04.440 --> 00:03:07.270 align:start position:0%
things and it can do things both like
code<00:03:05.319><c> as</c><00:03:05.440><c> well</c><00:03:05.640><c> as</c><00:03:05.840><c> just</c><00:03:06.080><c> standard</c><00:03:06.480><c> chatting</c>

00:03:07.270 --> 00:03:07.280 align:start position:0%
code as well as just standard chatting
 

00:03:07.280 --> 00:03:10.509 align:start position:0%
code as well as just standard chatting
to<00:03:07.760><c> this</c><00:03:08.319><c> let's</c><00:03:08.480><c> see</c><00:03:08.640><c> if</c><00:03:08.760><c> we</c><00:03:08.959><c> ask</c>

00:03:10.509 --> 00:03:10.519 align:start position:0%
to this let's see if we ask
 

00:03:10.519 --> 00:03:12.830 align:start position:0%
to this let's see if we ask
it<00:03:11.519><c> okay</c><00:03:11.640><c> so</c><00:03:11.760><c> if</c><00:03:11.840><c> I</c><00:03:11.959><c> ask</c><00:03:12.120><c> it</c><00:03:12.239><c> a</c><00:03:12.360><c> question</c><00:03:12.640><c> like</c>

00:03:12.830 --> 00:03:12.840 align:start position:0%
it okay so if I ask it a question like
 

00:03:12.840 --> 00:03:14.789 align:start position:0%
it okay so if I ask it a question like
explain<00:03:13.400><c> best</c><00:03:13.680><c> how</c><00:03:13.799><c> to</c><00:03:13.959><c> find</c><00:03:14.239><c> prime</c><00:03:14.480><c> numbers</c>

00:03:14.789 --> 00:03:14.799 align:start position:0%
explain best how to find prime numbers
 

00:03:14.799 --> 00:03:17.110 align:start position:0%
explain best how to find prime numbers
above<00:03:15.000><c> a</c><00:03:15.319><c> trillion</c><00:03:16.319><c> seems</c><00:03:16.599><c> like</c><00:03:16.720><c> it's</c><00:03:16.879><c> giving</c>

00:03:17.110 --> 00:03:17.120 align:start position:0%
above a trillion seems like it's giving
 

00:03:17.120 --> 00:03:19.710 align:start position:0%
above a trillion seems like it's giving
me<00:03:17.560><c> a</c><00:03:17.720><c> code</c><00:03:18.040><c> answer</c><00:03:18.480><c> here</c><00:03:19.159><c> I</c><00:03:19.239><c> could</c><00:03:19.440><c> also</c>

00:03:19.710 --> 00:03:19.720 align:start position:0%
me a code answer here I could also
 

00:03:19.720 --> 00:03:21.869 align:start position:0%
me a code answer here I could also
probably<00:03:20.000><c> Post</c><00:03:20.239><c> in</c><00:03:20.400><c> some</c><00:03:20.640><c> code</c><00:03:20.959><c> and</c><00:03:21.120><c> ask</c><00:03:21.319><c> it</c><00:03:21.519><c> to</c>

00:03:21.869 --> 00:03:21.879 align:start position:0%
probably Post in some code and ask it to
 

00:03:21.879 --> 00:03:24.869 align:start position:0%
probably Post in some code and ask it to
explain<00:03:22.440><c> that</c><00:03:23.120><c> for</c><00:03:23.360><c> for</c><00:03:23.560><c> trying</c><00:03:24.000><c> this</c><00:03:24.239><c> out</c><00:03:24.799><c> we</c>

00:03:24.869 --> 00:03:24.879 align:start position:0%
explain that for for trying this out we
 

00:03:24.879 --> 00:03:26.229 align:start position:0%
explain that for for trying this out we
can<00:03:25.000><c> see</c><00:03:25.200><c> that</c><00:03:25.319><c> definitely</c><00:03:25.599><c> on</c><00:03:25.760><c> their</c><00:03:25.920><c> system</c>

00:03:26.229 --> 00:03:26.239 align:start position:0%
can see that definitely on their system
 

00:03:26.239 --> 00:03:28.509 align:start position:0%
can see that definitely on their system
it's<00:03:26.400><c> running</c><00:03:26.840><c> very</c><00:03:27.080><c> nice</c><00:03:27.280><c> and</c><00:03:27.519><c> very</c><00:03:27.760><c> Snappy</c>

00:03:28.509 --> 00:03:28.519 align:start position:0%
it's running very nice and very Snappy
 

00:03:28.519 --> 00:03:30.630 align:start position:0%
it's running very nice and very Snappy
in<00:03:28.760><c> here</c><00:03:29.319><c> so</c><00:03:29.480><c> if</c><00:03:29.760><c> we</c><00:03:29.840><c> jump</c><00:03:30.040><c> into</c><00:03:30.360><c> actually</c>

00:03:30.630 --> 00:03:30.640 align:start position:0%
in here so if we jump into actually
 

00:03:30.640 --> 00:03:32.830 align:start position:0%
in here so if we jump into actually
trying<00:03:31.000><c> it</c><00:03:31.239><c> en</c><00:03:31.480><c> code</c><00:03:31.760><c> ourselves</c><00:03:32.519><c> here</c><00:03:32.640><c> I've</c>

00:03:32.830 --> 00:03:32.840 align:start position:0%
trying it en code ourselves here I've
 

00:03:32.840 --> 00:03:34.910 align:start position:0%
trying it en code ourselves here I've
basically<00:03:33.239><c> just</c><00:03:33.400><c> set</c><00:03:33.599><c> up</c><00:03:33.959><c> a</c><00:03:34.080><c> simple</c><00:03:34.360><c> notebook</c>

00:03:34.910 --> 00:03:34.920 align:start position:0%
basically just set up a simple notebook
 

00:03:34.920 --> 00:03:37.270 align:start position:0%
basically just set up a simple notebook
to<00:03:35.319><c> go</c><00:03:35.560><c> through</c><00:03:35.760><c> it</c><00:03:35.879><c> and</c><00:03:36.000><c> have</c><00:03:36.080><c> a</c><00:03:36.280><c> play</c><00:03:36.519><c> with</c><00:03:36.680><c> it</c>

00:03:37.270 --> 00:03:37.280 align:start position:0%
to go through it and have a play with it
 

00:03:37.280 --> 00:03:39.350 align:start position:0%
to go through it and have a play with it
you<00:03:37.439><c> you</c><00:03:37.560><c> can</c><00:03:37.760><c> try</c><00:03:37.959><c> it</c><00:03:38.040><c> out</c><00:03:38.280><c> yourself</c><00:03:39.120><c> one</c><00:03:39.239><c> of</c>

00:03:39.350 --> 00:03:39.360 align:start position:0%
you you can try it out yourself one of
 

00:03:39.360 --> 00:03:42.149 align:start position:0%
you you can try it out yourself one of
the<00:03:39.519><c> interesting</c><00:03:40.040><c> things</c><00:03:40.599><c> is</c><00:03:41.000><c> that</c><00:03:41.680><c> they've</c>

00:03:42.149 --> 00:03:42.159 align:start position:0%
the interesting things is that they've
 

00:03:42.159 --> 00:03:45.789 align:start position:0%
the interesting things is that they've
trained<00:03:42.720><c> this</c><00:03:43.239><c> to</c><00:03:43.519><c> use</c><00:03:43.959><c> the</c><00:03:44.120><c> chat</c><00:03:44.439><c> ml</c><00:03:44.920><c> system</c>

00:03:45.789 --> 00:03:45.799 align:start position:0%
trained this to use the chat ml system
 

00:03:45.799 --> 00:03:47.710 align:start position:0%
trained this to use the chat ml system
and<00:03:45.920><c> now</c><00:03:46.040><c> hugging</c><00:03:46.439><c> face</c><00:03:46.959><c> themselves</c><00:03:47.480><c> have</c>

00:03:47.710 --> 00:03:47.720 align:start position:0%
and now hugging face themselves have
 

00:03:47.720 --> 00:03:50.509 align:start position:0%
and now hugging face themselves have
actually<00:03:48.239><c> Incorporated</c><00:03:49.200><c> chat</c><00:03:49.760><c> templates</c>

00:03:50.509 --> 00:03:50.519 align:start position:0%
actually Incorporated chat templates
 

00:03:50.519 --> 00:03:53.069 align:start position:0%
actually Incorporated chat templates
into<00:03:51.159><c> Transformers</c><00:03:52.159><c> so</c><00:03:52.360><c> what</c><00:03:52.519><c> this</c><00:03:52.720><c> actually</c>

00:03:53.069 --> 00:03:53.079 align:start position:0%
into Transformers so what this actually
 

00:03:53.079 --> 00:03:56.309 align:start position:0%
into Transformers so what this actually
does<00:03:53.840><c> is</c><00:03:53.959><c> it</c><00:03:54.159><c> allows</c><00:03:54.599><c> you</c><00:03:54.840><c> to</c><00:03:55.200><c> basically</c><00:03:55.720><c> use</c>

00:03:56.309 --> 00:03:56.319 align:start position:0%
does is it allows you to basically use
 

00:03:56.319 --> 00:03:58.589 align:start position:0%
does is it allows you to basically use
the<00:03:56.480><c> same</c><00:03:56.760><c> chat</c><00:03:57.079><c> ml</c><00:03:57.599><c> template</c><00:03:58.159><c> that</c><00:03:58.239><c> you</c><00:03:58.360><c> would</c>

00:03:58.589 --> 00:03:58.599 align:start position:0%
the same chat ml template that you would
 

00:03:58.599 --> 00:04:01.789 align:start position:0%
the same chat ml template that you would
use<00:03:59.159><c> with</c><00:03:59.360><c> say</c><00:03:59.680><c> open</c><00:04:00.000><c> AI</c><00:04:00.720><c> to</c><00:04:00.879><c> run</c><00:04:01.120><c> it</c><00:04:01.319><c> through</c><00:04:01.640><c> a</c>

00:04:01.789 --> 00:04:01.799 align:start position:0%
use with say open AI to run it through a
 

00:04:01.799 --> 00:04:04.630 align:start position:0%
use with say open AI to run it through a
hung<00:04:02.239><c> face</c><00:04:03.040><c> tokenizer</c><00:04:04.040><c> if</c><00:04:04.120><c> we</c><00:04:04.239><c> come</c><00:04:04.400><c> in</c><00:04:04.480><c> and</c>

00:04:04.630 --> 00:04:04.640 align:start position:0%
hung face tokenizer if we come in and
 

00:04:04.640 --> 00:04:06.670 align:start position:0%
hung face tokenizer if we come in and
look<00:04:04.840><c> at</c><00:04:05.000><c> at</c><00:04:05.159><c> their</c><00:04:05.319><c> code</c><00:04:05.680><c> here</c><00:04:06.239><c> we</c><00:04:06.319><c> can</c><00:04:06.480><c> take</c>

00:04:06.670 --> 00:04:06.680 align:start position:0%
look at at their code here we can take
 

00:04:06.680 --> 00:04:08.069 align:start position:0%
look at at their code here we can take
the<00:04:06.799><c> same</c><00:04:07.000><c> format</c><00:04:07.360><c> that</c><00:04:07.480><c> we</c><00:04:07.599><c> would</c><00:04:07.720><c> send</c><00:04:07.920><c> to</c>

00:04:08.069 --> 00:04:08.079 align:start position:0%
the same format that we would send to
 

00:04:08.079 --> 00:04:10.869 align:start position:0%
the same format that we would send to
open<00:04:08.400><c> Ai</c><00:04:09.159><c> and</c><00:04:09.360><c> now</c><00:04:09.599><c> we</c><00:04:09.720><c> can</c><00:04:10.120><c> actually</c><00:04:10.480><c> run</c><00:04:10.720><c> that</c>

00:04:10.869 --> 00:04:10.879 align:start position:0%
open Ai and now we can actually run that
 

00:04:10.879 --> 00:04:11.830 align:start position:0%
open Ai and now we can actually run that
through<00:04:11.079><c> the</c>

00:04:11.830 --> 00:04:11.840 align:start position:0%
through the
 

00:04:11.840 --> 00:04:14.149 align:start position:0%
through the
tokenizer<00:04:12.840><c> we</c><00:04:13.000><c> can</c><00:04:13.159><c> just</c><00:04:13.400><c> apply</c><00:04:13.720><c> the</c><00:04:13.840><c> chat</c>

00:04:14.149 --> 00:04:14.159 align:start position:0%
tokenizer we can just apply the chat
 

00:04:14.159 --> 00:04:16.550 align:start position:0%
tokenizer we can just apply the chat
template<00:04:14.720><c> in</c><00:04:14.920><c> here</c><00:04:15.680><c> and</c><00:04:15.920><c> this</c><00:04:16.000><c> will</c><00:04:16.199><c> basically</c>

00:04:16.550 --> 00:04:16.560 align:start position:0%
template in here and this will basically
 

00:04:16.560 --> 00:04:18.749 align:start position:0%
template in here and this will basically
go<00:04:16.759><c> through</c><00:04:17.120><c> the</c><00:04:17.280><c> whole</c><00:04:17.519><c> thing</c><00:04:18.199><c> and</c><00:04:18.359><c> set</c><00:04:18.600><c> that</c>

00:04:18.749 --> 00:04:18.759 align:start position:0%
go through the whole thing and set that
 

00:04:18.759 --> 00:04:20.749 align:start position:0%
go through the whole thing and set that
up<00:04:18.880><c> for</c><00:04:19.079><c> doing</c><00:04:19.320><c> our</c><00:04:19.519><c> prediction</c><00:04:20.000><c> out</c><00:04:20.680><c> and</c>

00:04:20.749 --> 00:04:20.759 align:start position:0%
up for doing our prediction out and
 

00:04:20.759 --> 00:04:21.749 align:start position:0%
up for doing our prediction out and
you'll<00:04:20.919><c> see</c><00:04:21.120><c> that</c><00:04:21.280><c> what</c><00:04:21.359><c> we're</c><00:04:21.560><c> actually</c>

00:04:21.749 --> 00:04:21.759 align:start position:0%
you'll see that what we're actually
 

00:04:21.759 --> 00:04:23.830 align:start position:0%
you'll see that what we're actually
getting<00:04:22.199><c> back</c><00:04:22.479><c> from</c><00:04:22.680><c> that</c><00:04:22.919><c> prompt</c><00:04:23.400><c> is</c><00:04:23.520><c> it's</c>

00:04:23.830 --> 00:04:23.840 align:start position:0%
getting back from that prompt is it's
 

00:04:23.840 --> 00:04:25.870 align:start position:0%
getting back from that prompt is it's
formatted<00:04:24.440><c> it</c><00:04:24.880><c> so</c><00:04:25.080><c> you've</c><00:04:25.280><c> seen</c><00:04:25.520><c> probably</c><00:04:25.759><c> a</c>

00:04:25.870 --> 00:04:25.880 align:start position:0%
formatted it so you've seen probably a
 

00:04:25.880 --> 00:04:27.909 align:start position:0%
formatted it so you've seen probably a
lot<00:04:26.320><c> my</c><00:04:26.479><c> other</c><00:04:26.680><c> notebooks</c><00:04:27.240><c> where</c><00:04:27.479><c> I</c><00:04:27.600><c> have</c><00:04:27.680><c> to</c>

00:04:27.909 --> 00:04:27.919 align:start position:0%
lot my other notebooks where I have to
 

00:04:27.919 --> 00:04:29.990 align:start position:0%
lot my other notebooks where I have to
do<00:04:28.080><c> the</c><00:04:28.240><c> formatting</c><00:04:28.759><c> manually</c><00:04:29.639><c> or</c><00:04:29.759><c> put</c><00:04:29.880><c> it</c>

00:04:29.990 --> 00:04:30.000 align:start position:0%
do the formatting manually or put it
 

00:04:30.000 --> 00:04:32.390 align:start position:0%
do the formatting manually or put it
into<00:04:30.160><c> a</c><00:04:30.320><c> function</c><00:04:31.080><c> Etc</c><00:04:31.479><c> to</c><00:04:31.639><c> do</c><00:04:31.840><c> this</c><00:04:32.080><c> this</c><00:04:32.199><c> is</c>

00:04:32.390 --> 00:04:32.400 align:start position:0%
into a function Etc to do this this is
 

00:04:32.400 --> 00:04:34.830 align:start position:0%
into a function Etc to do this this is
now<00:04:32.800><c> sort</c><00:04:33.000><c> of</c><00:04:33.160><c> baked</c><00:04:33.520><c> into</c><00:04:34.240><c> hugging</c><00:04:34.600><c> face</c>

00:04:34.830 --> 00:04:34.840 align:start position:0%
now sort of baked into hugging face
 

00:04:34.840 --> 00:04:36.790 align:start position:0%
now sort of baked into hugging face
Transformers<00:04:35.479><c> which</c><00:04:35.600><c> is</c><00:04:35.800><c> nice</c><00:04:36.000><c> to</c><00:04:36.160><c> see</c><00:04:36.680><c> all</c>

00:04:36.790 --> 00:04:36.800 align:start position:0%
Transformers which is nice to see all
 

00:04:36.800 --> 00:04:38.790 align:start position:0%
Transformers which is nice to see all
right<00:04:36.919><c> so</c><00:04:37.080><c> I</c><00:04:37.199><c> still</c><00:04:37.400><c> set</c><00:04:37.680><c> up</c><00:04:37.919><c> a</c><00:04:38.120><c> function</c><00:04:38.479><c> for</c>

00:04:38.790 --> 00:04:38.800 align:start position:0%
right so I still set up a function for
 

00:04:38.800 --> 00:04:41.230 align:start position:0%
right so I still set up a function for
doing<00:04:39.160><c> this</c><00:04:39.720><c> where</c><00:04:39.880><c> we</c><00:04:40.000><c> can</c><00:04:40.240><c> pass</c><00:04:40.520><c> in</c><00:04:40.960><c> both</c>

00:04:41.230 --> 00:04:41.240 align:start position:0%
doing this where we can pass in both
 

00:04:41.240 --> 00:04:44.469 align:start position:0%
doing this where we can pass in both
input<00:04:41.639><c> text</c><00:04:42.320><c> system</c><00:04:42.759><c> prompt</c><00:04:43.240><c> max</c><00:04:43.600><c> length</c><00:04:44.120><c> for</c>

00:04:44.469 --> 00:04:44.479 align:start position:0%
input text system prompt max length for
 

00:04:44.479 --> 00:04:46.590 align:start position:0%
input text system prompt max length for
this<00:04:44.960><c> and</c><00:04:45.080><c> we're</c><00:04:45.199><c> going</c><00:04:45.280><c> to</c><00:04:45.360><c> filter</c><00:04:45.759><c> out</c><00:04:46.360><c> what</c>

00:04:46.590 --> 00:04:46.600 align:start position:0%
this and we're going to filter out what
 

00:04:46.600 --> 00:04:49.230 align:start position:0%
this and we're going to filter out what
we<00:04:47.280><c> passed</c><00:04:47.600><c> in</c><00:04:47.759><c> as</c><00:04:47.919><c> the</c><00:04:48.080><c> query</c><00:04:48.960><c> and</c><00:04:49.039><c> you</c><00:04:49.120><c> can</c>

00:04:49.230 --> 00:04:49.240 align:start position:0%
we passed in as the query and you can
 

00:04:49.240 --> 00:04:51.189 align:start position:0%
we passed in as the query and you can
see<00:04:49.400><c> sure</c><00:04:49.680><c> enough</c><00:04:50.039><c> we're</c><00:04:50.280><c> able</c><00:04:50.479><c> to</c><00:04:50.680><c> do</c><00:04:50.880><c> our</c>

00:04:51.189 --> 00:04:51.199 align:start position:0%
see sure enough we're able to do our
 

00:04:51.199 --> 00:04:53.189 align:start position:0%
see sure enough we're able to do our
code<00:04:51.520><c> generation</c><00:04:52.160><c> examples</c><00:04:52.759><c> just</c><00:04:52.919><c> like</c>

00:04:53.189 --> 00:04:53.199 align:start position:0%
code generation examples just like
 

00:04:53.199 --> 00:04:56.230 align:start position:0%
code generation examples just like
before<00:04:54.080><c> instructor</c><00:04:54.639><c> answering</c><00:04:55.360><c> examples</c>

00:04:56.230 --> 00:04:56.240 align:start position:0%
before instructor answering examples
 

00:04:56.240 --> 00:04:58.390 align:start position:0%
before instructor answering examples
like<00:04:56.479><c> before</c><00:04:57.280><c> it</c><00:04:57.440><c> does</c><00:04:57.600><c> seem</c><00:04:57.800><c> to</c><00:04:58.000><c> have</c><00:04:58.199><c> the</c>

00:04:58.390 --> 00:04:58.400 align:start position:0%
like before it does seem to have the
 

00:04:58.400 --> 00:05:01.790 align:start position:0%
like before it does seem to have the
base<00:04:58.759><c> strength</c><00:04:59.320><c> of</c><00:04:59.680><c> the</c><00:04:59.800><c> mistal</c><00:05:00.280><c> 7B</c><00:05:01.080><c> in</c><00:05:01.320><c> there</c>

00:05:01.790 --> 00:05:01.800 align:start position:0%
base strength of the mistal 7B in there
 

00:05:01.800 --> 00:05:03.469 align:start position:0%
base strength of the mistal 7B in there
but<00:05:02.000><c> definitely</c><00:05:02.520><c> a</c><00:05:02.759><c> different</c><00:05:03.080><c> sort</c><00:05:03.240><c> of</c>

00:05:03.469 --> 00:05:03.479 align:start position:0%
but definitely a different sort of
 

00:05:03.479 --> 00:05:06.189 align:start position:0%
but definitely a different sort of
personality<00:05:04.240><c> and</c><00:05:04.600><c> style</c><00:05:05.360><c> say</c><00:05:05.680><c> compared</c><00:05:06.039><c> to</c>

00:05:06.189 --> 00:05:06.199 align:start position:0%
personality and style say compared to
 

00:05:06.199 --> 00:05:08.870 align:start position:0%
personality and style say compared to
the<00:05:06.320><c> mistal</c><00:05:06.759><c> Orca</c><00:05:07.600><c> model</c><00:05:08.280><c> even</c><00:05:08.560><c> though</c><00:05:08.759><c> some</c>

00:05:08.870 --> 00:05:08.880 align:start position:0%
the mistal Orca model even though some
 

00:05:08.880 --> 00:05:10.590 align:start position:0%
the mistal Orca model even though some
of<00:05:09.000><c> the</c><00:05:09.160><c> answers</c><00:05:09.440><c> are</c><00:05:09.680><c> actually</c><00:05:10.000><c> very</c><00:05:10.199><c> similar</c>

00:05:10.590 --> 00:05:10.600 align:start position:0%
of the answers are actually very similar
 

00:05:10.600 --> 00:05:12.790 align:start position:0%
of the answers are actually very similar
if<00:05:10.680><c> you</c><00:05:10.840><c> look</c><00:05:10.960><c> at</c><00:05:11.120><c> the</c><00:05:11.360><c> answer</c><00:05:11.680><c> from</c><00:05:12.120><c> Mr</c><00:05:12.440><c> Ora</c>

00:05:12.790 --> 00:05:12.800 align:start position:0%
if you look at the answer from Mr Ora
 

00:05:12.800 --> 00:05:15.670 align:start position:0%
if you look at the answer from Mr Ora
for<00:05:13.039><c> this</c><00:05:13.560><c> it</c><00:05:13.680><c> was</c><00:05:13.960><c> also</c><00:05:14.240><c> sort</c><00:05:14.440><c> of</c><00:05:14.680><c> classifying</c>

00:05:15.670 --> 00:05:15.680 align:start position:0%
for this it was also sort of classifying
 

00:05:15.680 --> 00:05:18.710 align:start position:0%
for this it was also sort of classifying
these<00:05:15.919><c> things</c><00:05:16.320><c> by</c><00:05:16.600><c> size</c><00:05:17.039><c> origin</c><00:05:17.639><c> hair</c><00:05:18.560><c> that</c>

00:05:18.710 --> 00:05:18.720 align:start position:0%
these things by size origin hair that
 

00:05:18.720 --> 00:05:21.150 align:start position:0%
these things by size origin hair that
kind<00:05:18.840><c> of</c><00:05:19.039><c> thing</c><00:05:19.520><c> as</c><00:05:19.639><c> we</c><00:05:19.800><c> go</c><00:05:20.000><c> through</c><00:05:20.199><c> it</c><00:05:20.840><c> if</c><00:05:20.960><c> we</c>

00:05:21.150 --> 00:05:21.160 align:start position:0%
kind of thing as we go through it if we
 

00:05:21.160 --> 00:05:24.390 align:start position:0%
kind of thing as we go through it if we
look<00:05:21.360><c> at</c><00:05:21.680><c> the</c><00:05:22.400><c> writing</c><00:05:22.880><c> the</c><00:05:23.039><c> emails</c><00:05:23.919><c> thing</c>

00:05:24.390 --> 00:05:24.400 align:start position:0%
look at the writing the emails thing
 

00:05:24.400 --> 00:05:26.710 align:start position:0%
look at the writing the emails thing
again<00:05:24.680><c> we're</c><00:05:24.840><c> still</c><00:05:25.120><c> getting</c><00:05:25.880><c> that</c><00:05:26.160><c> kind</c><00:05:26.360><c> of</c>

00:05:26.710 --> 00:05:26.720 align:start position:0%
again we're still getting that kind of
 

00:05:26.720 --> 00:05:29.629 align:start position:0%
again we're still getting that kind of
structured<00:05:27.520><c> first</c><00:05:27.880><c> second</c><00:05:28.319><c> third</c><00:05:28.880><c> approach</c>

00:05:29.629 --> 00:05:29.639 align:start position:0%
structured first second third approach
 

00:05:29.639 --> 00:05:31.629 align:start position:0%
structured first second third approach
that<00:05:29.759><c> we</c><00:05:29.960><c> got</c><00:05:30.120><c> with</c><00:05:30.280><c> M</c><00:05:30.639><c> Orca</c><00:05:31.319><c> and</c><00:05:31.440><c> I</c><00:05:31.520><c> was</c>

00:05:31.629 --> 00:05:31.639 align:start position:0%
that we got with M Orca and I was
 

00:05:31.639 --> 00:05:32.950 align:start position:0%
that we got with M Orca and I was
wondering<00:05:31.960><c> if</c><00:05:32.120><c> that</c><00:05:32.240><c> was</c><00:05:32.400><c> coming</c><00:05:32.639><c> from</c><00:05:32.800><c> the</c>

00:05:32.950 --> 00:05:32.960 align:start position:0%
wondering if that was coming from the
 

00:05:32.960 --> 00:05:34.990 align:start position:0%
wondering if that was coming from the
Orca<00:05:33.360><c> data</c><00:05:33.680><c> set</c><00:05:33.960><c> but</c><00:05:34.080><c> it</c><00:05:34.199><c> seems</c><00:05:34.520><c> like</c><00:05:34.720><c> maybe</c>

00:05:34.990 --> 00:05:35.000 align:start position:0%
Orca data set but it seems like maybe
 

00:05:35.000 --> 00:05:36.550 align:start position:0%
Orca data set but it seems like maybe
that<00:05:35.120><c> was</c><00:05:35.280><c> actually</c><00:05:35.520><c> coming</c><00:05:35.800><c> from</c><00:05:35.960><c> the</c><00:05:36.080><c> mistal</c>

00:05:36.550 --> 00:05:36.560 align:start position:0%
that was actually coming from the mistal
 

00:05:36.560 --> 00:05:39.830 align:start position:0%
that was actually coming from the mistal
base<00:05:36.880><c> model</c><00:05:37.800><c> as</c><00:05:37.919><c> well</c><00:05:38.080><c> as</c><00:05:38.240><c> the</c><00:05:38.319><c> Ora</c><00:05:38.720><c> data</c><00:05:39.039><c> set</c>

00:05:39.830 --> 00:05:39.840 align:start position:0%
base model as well as the Ora data set
 

00:05:39.840 --> 00:05:41.350 align:start position:0%
base model as well as the Ora data set
in<00:05:39.960><c> some</c><00:05:40.240><c> examples</c><00:05:40.720><c> like</c><00:05:40.919><c> this</c><00:05:41.080><c> where</c><00:05:41.199><c> you're</c>

00:05:41.350 --> 00:05:41.360 align:start position:0%
in some examples like this where you're
 

00:05:41.360 --> 00:05:43.909 align:start position:0%
in some examples like this where you're
asking<00:05:41.639><c> it</c><00:05:41.800><c> to</c><00:05:41.919><c> take</c><00:05:42.199><c> on</c><00:05:42.319><c> a</c><00:05:42.800><c> personality</c><00:05:43.800><c> I'm</c>

00:05:43.909 --> 00:05:43.919 align:start position:0%
asking it to take on a personality I'm
 

00:05:43.919 --> 00:05:45.550 align:start position:0%
asking it to take on a personality I'm
not<00:05:44.080><c> sure</c><00:05:44.319><c> if</c><00:05:44.440><c> it's</c><00:05:44.800><c> in</c><00:05:44.919><c> some</c><00:05:45.080><c> way</c><00:05:45.240><c> I</c><00:05:45.319><c> think</c><00:05:45.440><c> I</c>

00:05:45.550 --> 00:05:45.560 align:start position:0%
not sure if it's in some way I think I
 

00:05:45.560 --> 00:05:48.029 align:start position:0%
not sure if it's in some way I think I
like<00:05:45.720><c> the</c><00:05:45.800><c> Mr</c><00:05:46.120><c> Ora</c><00:05:46.440><c> one</c><00:05:46.600><c> a</c><00:05:46.680><c> bit</c><00:05:46.880><c> better</c><00:05:47.280><c> for</c>

00:05:48.029 --> 00:05:48.039 align:start position:0%
like the Mr Ora one a bit better for
 

00:05:48.039 --> 00:05:49.670 align:start position:0%
like the Mr Ora one a bit better for
this<00:05:48.199><c> sort</c><00:05:48.360><c> of</c><00:05:48.560><c> thing</c><00:05:49.160><c> although</c><00:05:49.440><c> you</c><00:05:49.520><c> can</c>

00:05:49.670 --> 00:05:49.680 align:start position:0%
this sort of thing although you can
 

00:05:49.680 --> 00:05:51.350 align:start position:0%
this sort of thing although you can
clearly<00:05:49.960><c> see</c><00:05:50.199><c> that</c><00:05:50.319><c> it</c><00:05:50.520><c> has</c><00:05:50.759><c> taken</c><00:05:51.000><c> on</c><00:05:51.199><c> the</c>

00:05:51.350 --> 00:05:51.360 align:start position:0%
clearly see that it has taken on the
 

00:05:51.360 --> 00:05:53.309 align:start position:0%
clearly see that it has taken on the
personality<00:05:52.000><c> and</c><00:05:52.160><c> written</c><00:05:52.440><c> the</c><00:05:52.560><c> email</c><00:05:53.000><c> out</c>

00:05:53.309 --> 00:05:53.319 align:start position:0%
personality and written the email out
 

00:05:53.319 --> 00:05:55.710 align:start position:0%
personality and written the email out
like<00:05:53.680><c> that</c><00:05:54.560><c> the</c><00:05:54.720><c> email</c><00:05:55.080><c> from</c><00:05:55.319><c> the</c><00:05:55.440><c> vice</c>

00:05:55.710 --> 00:05:55.720 align:start position:0%
like that the email from the vice
 

00:05:55.720 --> 00:05:58.150 align:start position:0%
like that the email from the vice
president<00:05:56.639><c> here</c><00:05:56.840><c> we</c><00:05:56.960><c> can</c><00:05:57.160><c> see</c><00:05:57.800><c> this</c><00:05:57.919><c> is</c>

00:05:58.150 --> 00:05:58.160 align:start position:0%
president here we can see this is
 

00:05:58.160 --> 00:06:00.150 align:start position:0%
president here we can see this is
actually<00:05:58.360><c> written</c><00:05:58.800><c> quite</c><00:05:59.039><c> nicely</c><00:05:59.840><c> talking</c>

00:06:00.150 --> 00:06:00.160 align:start position:0%
actually written quite nicely talking
 

00:06:00.160 --> 00:06:02.550 align:start position:0%
actually written quite nicely talking
about<00:06:00.440><c> the</c><00:06:00.639><c> different</c><00:06:01.520><c> reasons</c><00:06:01.960><c> for</c><00:06:02.240><c> open</c>

00:06:02.550 --> 00:06:02.560 align:start position:0%
about the different reasons for open
 

00:06:02.560 --> 00:06:04.550 align:start position:0%
about the different reasons for open
sourcing<00:06:03.280><c> so</c><00:06:03.440><c> we</c><00:06:03.560><c> can</c><00:06:03.680><c> see</c><00:06:03.919><c> that</c><00:06:04.120><c> definitely</c>

00:06:04.550 --> 00:06:04.560 align:start position:0%
sourcing so we can see that definitely
 

00:06:04.560 --> 00:06:06.710 align:start position:0%
sourcing so we can see that definitely
this<00:06:04.720><c> model</c><00:06:05.160><c> responds</c><00:06:05.720><c> well</c><00:06:05.960><c> to</c><00:06:06.160><c> the</c><00:06:06.319><c> system</c>

00:06:06.710 --> 00:06:06.720 align:start position:0%
this model responds well to the system
 

00:06:06.720 --> 00:06:09.029 align:start position:0%
this model responds well to the system
prompt<00:06:07.479><c> just</c><00:06:07.639><c> like</c><00:06:07.759><c> the</c><00:06:07.880><c> Mr</c><00:06:08.160><c> Ora</c><00:06:08.599><c> one</c><00:06:08.880><c> was</c>

00:06:09.029 --> 00:06:09.039 align:start position:0%
prompt just like the Mr Ora one was
 

00:06:09.039 --> 00:06:12.469 align:start position:0%
prompt just like the Mr Ora one was
doing<00:06:09.479><c> as</c><00:06:09.639><c> well</c><00:06:10.280><c> in</c><00:06:10.560><c> here</c><00:06:11.360><c> if</c><00:06:11.479><c> we</c><00:06:11.680><c> look</c><00:06:11.919><c> at</c><00:06:12.319><c> some</c>

00:06:12.469 --> 00:06:12.479 align:start position:0%
doing as well in here if we look at some
 

00:06:12.479 --> 00:06:14.790 align:start position:0%
doing as well in here if we look at some
of<00:06:12.599><c> the</c><00:06:12.800><c> things</c><00:06:13.319><c> like</c><00:06:14.240><c> the</c><00:06:14.360><c> question</c><00:06:14.639><c> about</c>

00:06:14.790 --> 00:06:14.800 align:start position:0%
of the things like the question about
 

00:06:14.800 --> 00:06:17.589 align:start position:0%
of the things like the question about
Jeffrey<00:06:15.120><c> Hinton</c><00:06:15.360><c> and</c><00:06:15.520><c> George</c><00:06:15.919><c> Washington</c><00:06:16.919><c> we</c>

00:06:17.589 --> 00:06:17.599 align:start position:0%
Jeffrey Hinton and George Washington we
 

00:06:17.599 --> 00:06:19.469 align:start position:0%
Jeffrey Hinton and George Washington we
get<00:06:17.880><c> the</c><00:06:18.280><c> answer</c><00:06:18.639><c> out</c><00:06:18.919><c> we're</c><00:06:19.080><c> probably</c><00:06:19.319><c> not</c>

00:06:19.469 --> 00:06:19.479 align:start position:0%
get the answer out we're probably not
 

00:06:19.479 --> 00:06:21.990 align:start position:0%
get the answer out we're probably not
getting<00:06:19.800><c> as</c><00:06:20.000><c> much</c><00:06:20.360><c> reasoning</c><00:06:20.919><c> step</c><00:06:21.240><c> by</c><00:06:21.520><c> step</c>

00:06:21.990 --> 00:06:22.000 align:start position:0%
getting as much reasoning step by step
 

00:06:22.000 --> 00:06:24.830 align:start position:0%
getting as much reasoning step by step
so<00:06:22.599><c> that's</c><00:06:22.800><c> where</c><00:06:22.960><c> maybe</c><00:06:23.199><c> the</c><00:06:23.440><c> Ora</c><00:06:24.120><c> data</c><00:06:24.479><c> set</c>

00:06:24.830 --> 00:06:24.840 align:start position:0%
so that's where maybe the Ora data set
 

00:06:24.840 --> 00:06:26.909 align:start position:0%
so that's where maybe the Ora data set
actually<00:06:25.160><c> does</c><00:06:25.520><c> help</c><00:06:26.080><c> for</c><00:06:26.440><c> that</c><00:06:26.599><c> kind</c><00:06:26.720><c> of</c>

00:06:26.909 --> 00:06:26.919 align:start position:0%
actually does help for that kind of
 

00:06:26.919 --> 00:06:28.670 align:start position:0%
actually does help for that kind of
thing<00:06:27.400><c> where</c><00:06:27.520><c> it</c><00:06:27.680><c> really</c><00:06:27.880><c> sort</c><00:06:28.080><c> of</c><00:06:28.240><c> broke</c><00:06:28.479><c> it</c>

00:06:28.670 --> 00:06:28.680 align:start position:0%
thing where it really sort of broke it
 

00:06:28.680 --> 00:06:31.710 align:start position:0%
thing where it really sort of broke it
down<00:06:29.080><c> it's</c><00:06:29.479><c> thinking</c><00:06:29.880><c> down</c><00:06:30.520><c> here</c><00:06:31.319><c> for</c><00:06:31.560><c> the</c>

00:06:31.710 --> 00:06:31.720 align:start position:0%
down it's thinking down here for the
 

00:06:31.720 --> 00:06:34.670 align:start position:0%
down it's thinking down here for the
creative<00:06:32.199><c> writing</c><00:06:32.759><c> thing</c><00:06:33.479><c> okay</c><00:06:33.840><c> this</c><00:06:34.000><c> is</c>

00:06:34.670 --> 00:06:34.680 align:start position:0%
creative writing thing okay this is
 

00:06:34.680 --> 00:06:36.150 align:start position:0%
creative writing thing okay this is
probably<00:06:34.960><c> not</c><00:06:35.120><c> what</c><00:06:35.240><c> it</c><00:06:35.360><c> was</c><00:06:35.560><c> actually</c><00:06:35.919><c> meant</c>

00:06:36.150 --> 00:06:36.160 align:start position:0%
probably not what it was actually meant
 

00:06:36.160 --> 00:06:38.510 align:start position:0%
probably not what it was actually meant
to<00:06:36.400><c> create</c><00:06:36.840><c> in</c><00:06:37.080><c> here</c><00:06:37.840><c> and</c><00:06:37.960><c> then</c><00:06:38.199><c> finally</c>

00:06:38.510 --> 00:06:38.520 align:start position:0%
to create in here and then finally
 

00:06:38.520 --> 00:06:41.589 align:start position:0%
to create in here and then finally
looking<00:06:38.840><c> at</c><00:06:39.000><c> the</c><00:06:39.160><c> GSM</c><00:06:39.680><c> 8K</c><00:06:40.360><c> questions</c><00:06:41.240><c> it</c><00:06:41.400><c> does</c>

00:06:41.589 --> 00:06:41.599 align:start position:0%
looking at the GSM 8K questions it does
 

00:06:41.599 --> 00:06:44.309 align:start position:0%
looking at the GSM 8K questions it does
a<00:06:41.720><c> reasonably</c><00:06:42.280><c> good</c><00:06:42.599><c> job</c><00:06:42.960><c> here</c><00:06:43.639><c> of</c><00:06:43.880><c> being</c><00:06:44.120><c> able</c>

00:06:44.309 --> 00:06:44.319 align:start position:0%
a reasonably good job here of being able
 

00:06:44.319 --> 00:06:46.550 align:start position:0%
a reasonably good job here of being able
to<00:06:44.479><c> break</c><00:06:44.880><c> these</c><00:06:45.240><c> down</c><00:06:46.120><c> into</c><00:06:46.400><c> the</c>

00:06:46.550 --> 00:06:46.560 align:start position:0%
to break these down into the
 

00:06:46.560 --> 00:06:48.749 align:start position:0%
to break these down into the
step-by-step<00:06:47.319><c> format</c><00:06:48.000><c> very</c><00:06:48.240><c> similar</c><00:06:48.560><c> to</c>

00:06:48.749 --> 00:06:48.759 align:start position:0%
step-by-step format very similar to
 

00:06:48.759 --> 00:06:51.790 align:start position:0%
step-by-step format very similar to
mistal<00:06:49.199><c> Ora</c><00:06:50.199><c> here</c><00:06:51.039><c> here</c><00:06:51.199><c> it</c><00:06:51.319><c> seems</c><00:06:51.520><c> to</c><00:06:51.680><c> have</c>

00:06:51.790 --> 00:06:51.800 align:start position:0%
mistal Ora here here it seems to have
 

00:06:51.800 --> 00:06:53.589 align:start position:0%
mistal Ora here here it seems to have
run<00:06:52.000><c> out</c><00:06:52.120><c> of</c><00:06:52.280><c> tokens</c><00:06:52.759><c> from</c><00:06:52.960><c> that</c><00:06:53.199><c> one</c><00:06:53.360><c> of</c><00:06:53.479><c> the</c>

00:06:53.589 --> 00:06:53.599 align:start position:0%
run out of tokens from that one of the
 

00:06:53.599 --> 00:06:55.390 align:start position:0%
run out of tokens from that one of the
runs<00:06:53.840><c> I</c><00:06:53.960><c> ran</c><00:06:54.280><c> before</c><00:06:54.560><c> it</c><00:06:54.680><c> was</c><00:06:54.800><c> able</c><00:06:55.000><c> to</c><00:06:55.199><c> work</c>

00:06:55.390 --> 00:06:55.400 align:start position:0%
runs I ran before it was able to work
 

00:06:55.400 --> 00:06:57.990 align:start position:0%
runs I ran before it was able to work
out<00:06:55.960><c> that</c><00:06:56.120><c> there</c><00:06:56.199><c> were</c><00:06:56.360><c> nine</c><00:06:56.599><c> apples</c><00:06:57.240><c> left</c><00:06:57.879><c> we</c>

00:06:57.990 --> 00:06:58.000 align:start position:0%
out that there were nine apples left we
 

00:06:58.000 --> 00:06:59.670 align:start position:0%
out that there were nine apples left we
can<00:06:58.120><c> see</c><00:06:58.319><c> in</c><00:06:58.479><c> this</c><00:06:58.599><c> one</c><00:06:58.800><c> it</c><00:06:58.919><c> works</c><00:06:59.160><c> out</c><00:06:59.560><c> that</c>

00:06:59.670 --> 00:06:59.680 align:start position:0%
can see in this one it works out that
 

00:06:59.680 --> 00:07:01.950 align:start position:0%
can see in this one it works out that
she<00:06:59.879><c> could</c><00:07:00.199><c> should</c><00:07:00.400><c> only</c><00:07:00.639><c> get</c><00:07:00.800><c> paid</c><00:07:01.000><c> $10</c><00:07:01.759><c> for</c>

00:07:01.950 --> 00:07:01.960 align:start position:0%
she could should only get paid $10 for
 

00:07:01.960 --> 00:07:04.510 align:start position:0%
she could should only get paid $10 for
50<00:07:02.319><c> minutes</c><00:07:03.240><c> it</c><00:07:03.400><c> still</c><00:07:03.680><c> doesn't</c><00:07:03.919><c> seem</c><00:07:04.120><c> to</c><00:07:04.319><c> get</c>

00:07:04.510 --> 00:07:04.520 align:start position:0%
50 minutes it still doesn't seem to get
 

00:07:04.520 --> 00:07:07.990 align:start position:0%
50 minutes it still doesn't seem to get
this<00:07:04.759><c> final</c><00:07:05.160><c> GSM</c><00:07:05.720><c> 8K</c><00:07:06.720><c> question</c><00:07:07.160><c> here</c><00:07:07.680><c> and</c><00:07:07.840><c> I</c>

00:07:07.990 --> 00:07:08.000 align:start position:0%
this final GSM 8K question here and I
 

00:07:08.000 --> 00:07:09.790 align:start position:0%
this final GSM 8K question here and I
give<00:07:08.120><c> it</c><00:07:08.319><c> like</c><00:07:08.520><c> this</c><00:07:08.720><c> and</c><00:07:08.879><c> I</c><00:07:09.039><c> also</c><00:07:09.360><c> broke</c><00:07:09.639><c> it</c>

00:07:09.790 --> 00:07:09.800 align:start position:0%
give it like this and I also broke it
 

00:07:09.800 --> 00:07:11.710 align:start position:0%
give it like this and I also broke it
down<00:07:10.160><c> to</c><00:07:10.400><c> be</c><00:07:10.560><c> a</c><00:07:10.639><c> little</c><00:07:10.800><c> bit</c><00:07:10.960><c> simpler</c><00:07:11.440><c> and</c><00:07:11.560><c> give</c>

00:07:11.710 --> 00:07:11.720 align:start position:0%
down to be a little bit simpler and give
 

00:07:11.720 --> 00:07:14.110 align:start position:0%
down to be a little bit simpler and give
it<00:07:11.840><c> like</c><00:07:12.080><c> that</c><00:07:12.319><c> it</c><00:07:12.520><c> also</c><00:07:13.240><c> didn't</c><00:07:13.520><c> do</c><00:07:13.639><c> a</c><00:07:13.800><c> great</c>

00:07:14.110 --> 00:07:14.120 align:start position:0%
it like that it also didn't do a great
 

00:07:14.120 --> 00:07:16.390 align:start position:0%
it like that it also didn't do a great
job<00:07:14.759><c> anyway</c><00:07:15.199><c> I</c><00:07:15.360><c> do</c><00:07:15.520><c> think</c><00:07:15.720><c> this</c><00:07:15.840><c> is</c><00:07:16.080><c> definitely</c>

00:07:16.390 --> 00:07:16.400 align:start position:0%
job anyway I do think this is definitely
 

00:07:16.400 --> 00:07:17.950 align:start position:0%
job anyway I do think this is definitely
an<00:07:16.599><c> interesting</c><00:07:17.120><c> model</c><00:07:17.400><c> if</c><00:07:17.479><c> you</c><00:07:17.560><c> want</c><00:07:17.680><c> to</c><00:07:17.759><c> do</c>

00:07:17.950 --> 00:07:17.960 align:start position:0%
an interesting model if you want to do
 

00:07:17.960 --> 00:07:20.270 align:start position:0%
an interesting model if you want to do
multi-turn<00:07:18.680><c> chat</c><00:07:19.400><c> so</c><00:07:19.560><c> if</c><00:07:19.680><c> there's</c><00:07:19.879><c> something</c>

00:07:20.270 --> 00:07:20.280 align:start position:0%
multi-turn chat so if there's something
 

00:07:20.280 --> 00:07:21.990 align:start position:0%
multi-turn chat so if there's something
in<00:07:20.599><c> there</c><00:07:21.199><c> the</c><00:07:21.400><c> the</c><00:07:21.479><c> challenge</c><00:07:21.800><c> you're</c><00:07:21.919><c> going</c>

00:07:21.990 --> 00:07:22.000 align:start position:0%
in there the the challenge you're going
 

00:07:22.000 --> 00:07:24.150 align:start position:0%
in there the the challenge you're going
to<00:07:22.160><c> have</c><00:07:22.479><c> though</c><00:07:22.800><c> is</c><00:07:23.440><c> you</c><00:07:23.639><c> probably</c><00:07:23.840><c> want</c><00:07:23.960><c> to</c>

00:07:24.150 --> 00:07:24.160 align:start position:0%
to have though is you probably want to
 

00:07:24.160 --> 00:07:26.110 align:start position:0%
to have though is you probably want to
give<00:07:24.280><c> it</c><00:07:24.599><c> your</c><00:07:24.960><c> particular</c><00:07:25.440><c> personality</c><00:07:26.000><c> so</c>

00:07:26.110 --> 00:07:26.120 align:start position:0%
give it your particular personality so
 

00:07:26.120 --> 00:07:28.589 align:start position:0%
give it your particular personality so
you<00:07:26.240><c> want</c><00:07:26.360><c> to</c><00:07:26.560><c> try</c><00:07:26.840><c> it</c><00:07:26.960><c> out</c><00:07:27.639><c> with</c><00:07:27.919><c> injecting</c><00:07:28.360><c> a</c>

00:07:28.589 --> 00:07:28.599 align:start position:0%
you want to try it out with injecting a
 

00:07:28.599 --> 00:07:30.550 align:start position:0%
you want to try it out with injecting a
personality<00:07:29.160><c> and</c><00:07:29.479><c> see</c><00:07:29.879><c> okay</c><00:07:30.080><c> how</c>

00:07:30.550 --> 00:07:30.560 align:start position:0%
personality and see okay how
 

00:07:30.560 --> 00:07:33.230 align:start position:0%
personality and see okay how
consistently<00:07:31.560><c> can</c><00:07:31.720><c> it</c><00:07:31.960><c> use</c><00:07:32.360><c> that</c><00:07:32.879><c> and</c><00:07:33.000><c> then</c>

00:07:33.230 --> 00:07:33.240 align:start position:0%
consistently can it use that and then
 

00:07:33.240 --> 00:07:34.990 align:start position:0%
consistently can it use that and then
like<00:07:33.400><c> how</c><00:07:33.560><c> much</c><00:07:33.759><c> can</c><00:07:33.919><c> we</c><00:07:34.120><c> inject</c><00:07:34.560><c> into</c><00:07:34.840><c> the</c>

00:07:34.990 --> 00:07:35.000 align:start position:0%
like how much can we inject into the
 

00:07:35.000 --> 00:07:37.390 align:start position:0%
like how much can we inject into the
system<00:07:35.400><c> prompt</c><00:07:35.720><c> to</c><00:07:35.919><c> try</c><00:07:36.199><c> this</c><00:07:36.440><c> out</c><00:07:37.039><c> it</c><00:07:37.160><c> does</c>

00:07:37.390 --> 00:07:37.400 align:start position:0%
system prompt to try this out it does
 

00:07:37.400 --> 00:07:38.550 align:start position:0%
system prompt to try this out it does
seem<00:07:37.599><c> like</c><00:07:37.800><c> this</c><00:07:37.879><c> is</c><00:07:38.039><c> going</c><00:07:38.160><c> to</c><00:07:38.280><c> be</c><00:07:38.400><c> an</c>

00:07:38.550 --> 00:07:38.560 align:start position:0%
seem like this is going to be an
 

00:07:38.560 --> 00:07:40.430 align:start position:0%
seem like this is going to be an
interesting<00:07:38.960><c> model</c><00:07:39.280><c> for</c><00:07:39.560><c> playing</c><00:07:39.919><c> around</c>

00:07:40.430 --> 00:07:40.440 align:start position:0%
interesting model for playing around
 

00:07:40.440 --> 00:07:42.909 align:start position:0%
interesting model for playing around
with<00:07:40.919><c> things</c><00:07:41.199><c> like</c><00:07:41.400><c> rag</c><00:07:42.000><c> things</c><00:07:42.360><c> like</c><00:07:42.599><c> Lang</c>

00:07:42.909 --> 00:07:42.919 align:start position:0%
with things like rag things like Lang
 

00:07:42.919 --> 00:07:45.510 align:start position:0%
with things like rag things like Lang
chain<00:07:43.199><c> tools</c><00:07:43.720><c> Etc</c><00:07:44.520><c> maybe</c><00:07:44.800><c> I'll</c><00:07:45.080><c> have</c><00:07:45.199><c> a</c><00:07:45.360><c> play</c>

00:07:45.510 --> 00:07:45.520 align:start position:0%
chain tools Etc maybe I'll have a play
 

00:07:45.520 --> 00:07:46.950 align:start position:0%
chain tools Etc maybe I'll have a play
with<00:07:45.680><c> that</c><00:07:45.800><c> and</c><00:07:45.919><c> if</c><00:07:46.159><c> if</c><00:07:46.240><c> I</c><00:07:46.360><c> do</c><00:07:46.479><c> find</c><00:07:46.720><c> anything</c>

00:07:46.950 --> 00:07:46.960 align:start position:0%
with that and if if I do find anything
 

00:07:46.960 --> 00:07:48.550 align:start position:0%
with that and if if I do find anything
interesting<00:07:47.319><c> I'll</c><00:07:47.440><c> make</c><00:07:47.599><c> a</c><00:07:47.680><c> new</c><00:07:47.919><c> video</c><00:07:48.280><c> about</c>

00:07:48.550 --> 00:07:48.560 align:start position:0%
interesting I'll make a new video about
 

00:07:48.560 --> 00:07:50.309 align:start position:0%
interesting I'll make a new video about
that<00:07:48.919><c> can</c><00:07:49.199><c> actually</c><00:07:49.400><c> have</c><00:07:49.479><c> a</c><00:07:49.639><c> play</c><00:07:49.840><c> with</c><00:07:49.960><c> it</c>

00:07:50.309 --> 00:07:50.319 align:start position:0%
that can actually have a play with it
 

00:07:50.319 --> 00:07:52.710 align:start position:0%
that can actually have a play with it
yourself<00:07:51.280><c> anyway</c><00:07:51.599><c> check</c><00:07:51.759><c> it</c><00:07:51.879><c> out</c><00:07:52.319><c> see</c><00:07:52.560><c> what</c>

00:07:52.710 --> 00:07:52.720 align:start position:0%
yourself anyway check it out see what
 

00:07:52.720 --> 00:07:55.589 align:start position:0%
yourself anyway check it out see what
you<00:07:52.919><c> think</c><00:07:53.440><c> I</c><00:07:53.520><c> will</c><00:07:53.680><c> put</c><00:07:53.840><c> the</c><00:07:54.000><c> link</c><00:07:54.280><c> to</c><00:07:54.919><c> their</c>

00:07:55.589 --> 00:07:55.599 align:start position:0%
you think I will put the link to their
 

00:07:55.599 --> 00:07:58.070 align:start position:0%
you think I will put the link to their
version<00:07:56.120><c> that</c><00:07:56.240><c> you</c><00:07:56.360><c> can</c><00:07:56.599><c> try</c><00:07:57.240><c> as</c><00:07:57.400><c> well</c><00:07:57.599><c> as</c><00:07:57.879><c> the</c>

00:07:58.070 --> 00:07:58.080 align:start position:0%
version that you can try as well as the
 

00:07:58.080 --> 00:08:00.550 align:start position:0%
version that you can try as well as the
code<00:07:58.960><c> in</c><00:07:59.120><c> here</c><00:07:59.360><c> here</c><00:07:59.759><c> it's</c><00:07:59.960><c> interesting</c><00:08:00.360><c> that</c>

00:08:00.550 --> 00:08:00.560 align:start position:0%
code in here here it's interesting that
 

00:08:00.560 --> 00:08:02.670 align:start position:0%
code in here here it's interesting that
this<00:08:00.639><c> is</c><00:08:00.879><c> just</c><00:08:01.080><c> the</c><00:08:01.319><c> first</c><00:08:01.639><c> of</c><00:08:01.759><c> a</c><00:08:01.960><c> series</c><00:08:02.360><c> so</c><00:08:02.520><c> we</c>

00:08:02.670 --> 00:08:02.680 align:start position:0%
this is just the first of a series so we
 

00:08:02.680 --> 00:08:04.869 align:start position:0%
this is just the first of a series so we
might<00:08:02.919><c> be</c><00:08:03.039><c> able</c><00:08:03.240><c> to</c><00:08:03.319><c> see</c><00:08:03.639><c> different</c><00:08:04.000><c> sorts</c><00:08:04.280><c> of</c>

00:08:04.869 --> 00:08:04.879 align:start position:0%
might be able to see different sorts of
 

00:08:04.879 --> 00:08:07.869 align:start position:0%
might be able to see different sorts of
training<00:08:05.879><c> formats</c><00:08:06.400><c> and</c><00:08:06.639><c> different</c><00:08:06.919><c> sort</c><00:08:07.120><c> of</c>

00:08:07.869 --> 00:08:07.879 align:start position:0%
training formats and different sort of
 

00:08:07.879 --> 00:08:10.110 align:start position:0%
training formats and different sort of
recipes<00:08:08.400><c> for</c><00:08:08.639><c> training</c><00:08:09.080><c> these</c><00:08:09.240><c> models</c><00:08:09.680><c> to</c><00:08:09.800><c> see</c>

00:08:10.110 --> 00:08:10.120 align:start position:0%
recipes for training these models to see
 

00:08:10.120 --> 00:08:12.110 align:start position:0%
recipes for training these models to see
what<00:08:10.440><c> comes</c><00:08:10.720><c> out</c><00:08:11.360><c> it</c><00:08:11.440><c> would</c><00:08:11.560><c> be</c><00:08:11.720><c> really</c><00:08:11.960><c> great</c>

00:08:12.110 --> 00:08:12.120 align:start position:0%
what comes out it would be really great
 

00:08:12.120 --> 00:08:14.430 align:start position:0%
what comes out it would be really great
to<00:08:12.240><c> be</c><00:08:12.360><c> able</c><00:08:12.560><c> to</c><00:08:12.720><c> compare</c><00:08:13.720><c> different</c><00:08:14.080><c> models</c>

00:08:14.430 --> 00:08:14.440 align:start position:0%
to be able to compare different models
 

00:08:14.440 --> 00:08:16.869 align:start position:0%
to be able to compare different models
at<00:08:14.639><c> different</c><00:08:15.000><c> times</c><00:08:15.599><c> to</c><00:08:15.800><c> get</c><00:08:15.960><c> a</c><00:08:16.120><c> sense</c><00:08:16.400><c> of</c><00:08:16.680><c> how</c>

00:08:16.869 --> 00:08:16.879 align:start position:0%
at different times to get a sense of how
 

00:08:16.879 --> 00:08:19.029 align:start position:0%
at different times to get a sense of how
much<00:08:17.120><c> do</c><00:08:17.360><c> certain</c><00:08:17.639><c> things</c><00:08:17.919><c> help</c><00:08:18.520><c> and</c><00:08:18.720><c> how</c><00:08:18.840><c> much</c>

00:08:19.029 --> 00:08:19.039 align:start position:0%
much do certain things help and how much
 

00:08:19.039 --> 00:08:22.430 align:start position:0%
much do certain things help and how much
is<00:08:19.159><c> it</c><00:08:19.440><c> just</c><00:08:19.759><c> the</c><00:08:20.400><c> well</c><00:08:20.800><c> curated</c><00:08:21.800><c> data</c><00:08:22.120><c> set</c><00:08:22.280><c> for</c>

00:08:22.430 --> 00:08:22.440 align:start position:0%
is it just the well curated data set for
 

00:08:22.440 --> 00:08:25.469 align:start position:0%
is it just the well curated data set for
doing<00:08:22.680><c> the</c><00:08:22.840><c> sft</c><00:08:23.400><c> in</c><00:08:23.639><c> this</c><00:08:24.520><c> anyway</c><00:08:25.080><c> as</c><00:08:25.240><c> always</c>

00:08:25.469 --> 00:08:25.479 align:start position:0%
doing the sft in this anyway as always
 

00:08:25.479 --> 00:08:26.790 align:start position:0%
doing the sft in this anyway as always
if<00:08:25.599><c> you</c><00:08:25.720><c> have</c><00:08:25.879><c> questions</c><00:08:26.280><c> please</c><00:08:26.479><c> put</c><00:08:26.599><c> them</c><00:08:26.720><c> in</c>

00:08:26.790 --> 00:08:26.800 align:start position:0%
if you have questions please put them in
 

00:08:26.800 --> 00:08:28.589 align:start position:0%
if you have questions please put them in
the<00:08:26.879><c> comments</c><00:08:27.199><c> below</c><00:08:28.080><c> if</c><00:08:28.159><c> you</c><00:08:28.240><c> found</c><00:08:28.440><c> this</c>

00:08:28.589 --> 00:08:28.599 align:start position:0%
the comments below if you found this
 

00:08:28.599 --> 00:08:30.430 align:start position:0%
the comments below if you found this
video<00:08:28.840><c> useful</c><00:08:29.520><c> please</c><00:08:29.840><c> click</c><00:08:30.120><c> like</c><00:08:30.240><c> And</c>

00:08:30.430 --> 00:08:30.440 align:start position:0%
video useful please click like And
 

00:08:30.440 --> 00:08:32.029 align:start position:0%
video useful please click like And
subscribe<00:08:31.159><c> I</c><00:08:31.240><c> will</c><00:08:31.360><c> talk</c><00:08:31.479><c> to</c><00:08:31.520><c> you</c><00:08:31.639><c> in</c><00:08:31.720><c> the</c><00:08:31.840><c> next</c>

00:08:32.029 --> 00:08:32.039 align:start position:0%
subscribe I will talk to you in the next
 

00:08:32.039 --> 00:08:36.599 align:start position:0%
subscribe I will talk to you in the next
video<00:08:32.760><c> bye</c><00:08:32.959><c> for</c><00:08:33.599><c> now</c>

