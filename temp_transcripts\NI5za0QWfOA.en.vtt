WEBVTT
Kind: captions
Language: en

00:00:01.560 --> 00:00:04.150 align:start position:0%
 
hey<00:00:01.760><c> everybody</c><00:00:02.480><c> we</c><00:00:02.879><c> are</c><00:00:03.399><c> here</c><00:00:03.679><c> with</c><00:00:03.879><c> another</c>

00:00:04.150 --> 00:00:04.160 align:start position:0%
hey everybody we are here with another
 

00:00:04.160 --> 00:00:07.590 align:start position:0%
hey everybody we are here with another
Vector<00:00:04.560><c> space</c><00:00:05.080><c> talks</c><00:00:06.000><c> and</c><00:00:07.000><c> it</c><00:00:07.120><c> is</c><00:00:07.319><c> quite</c><00:00:07.480><c> the</c>

00:00:07.590 --> 00:00:07.600 align:start position:0%
Vector space talks and it is quite the
 

00:00:07.600 --> 00:00:09.589 align:start position:0%
Vector space talks and it is quite the
pleasure<00:00:08.000><c> today</c><00:00:08.360><c> because</c><00:00:08.800><c> quadrant</c><00:00:09.360><c> just</c>

00:00:09.589 --> 00:00:09.599 align:start position:0%
pleasure today because quadrant just
 

00:00:09.599 --> 00:00:10.990 align:start position:0%
pleasure today because quadrant just
released

00:00:10.990 --> 00:00:11.000 align:start position:0%
released
 

00:00:11.000 --> 00:00:14.230 align:start position:0%
released
1.7<00:00:12.000><c> with</c><00:00:12.200><c> all</c><00:00:12.400><c> kinds</c><00:00:12.599><c> of</c><00:00:12.799><c> cool</c><00:00:13.080><c> features</c><00:00:13.799><c> and</c>

00:00:14.230 --> 00:00:14.240 align:start position:0%
1.7 with all kinds of cool features and
 

00:00:14.240 --> 00:00:15.789 align:start position:0%
1.7 with all kinds of cool features and
we're<00:00:14.440><c> here</c><00:00:14.559><c> to</c><00:00:14.719><c> talk</c><00:00:14.920><c> about</c><00:00:15.160><c> them</c><00:00:15.360><c> today</c>

00:00:15.789 --> 00:00:15.799 align:start position:0%
we're here to talk about them today
 

00:00:15.799 --> 00:00:18.510 align:start position:0%
we're here to talk about them today
they're<00:00:16.039><c> are</c><00:00:16.359><c> three</c><00:00:16.680><c> notable</c><00:00:17.400><c> pieces</c><00:00:17.880><c> to</c><00:00:18.119><c> this</c>

00:00:18.510 --> 00:00:18.520 align:start position:0%
they're are three notable pieces to this
 

00:00:18.520 --> 00:00:21.630 align:start position:0%
they're are three notable pieces to this
Puzzle<00:00:19.520><c> first</c><00:00:19.760><c> one</c><00:00:20.080><c> is</c><00:00:20.279><c> custom</c><00:00:20.640><c> sharding</c>

00:00:21.630 --> 00:00:21.640 align:start position:0%
Puzzle first one is custom sharding
 

00:00:21.640 --> 00:00:24.710 align:start position:0%
Puzzle first one is custom sharding
second<00:00:22.000><c> one</c><00:00:22.800><c> is</c><00:00:23.039><c> all</c><00:00:23.279><c> about</c><00:00:23.560><c> sparse</c><00:00:24.039><c> vectors</c>

00:00:24.710 --> 00:00:24.720 align:start position:0%
second one is all about sparse vectors
 

00:00:24.720 --> 00:00:27.189 align:start position:0%
second one is all about sparse vectors
and<00:00:24.840><c> the</c><00:00:25.039><c> third</c><00:00:25.400><c> piece</c><00:00:25.680><c> is</c><00:00:25.840><c> the</c><00:00:26.199><c> Discovery</c><00:00:26.519><c> API</c>

00:00:27.189 --> 00:00:27.199 align:start position:0%
and the third piece is the Discovery API
 

00:00:27.199 --> 00:00:28.790 align:start position:0%
and the third piece is the Discovery API
this<00:00:27.400><c> video</c><00:00:27.840><c> right</c><00:00:28.039><c> here</c><00:00:28.199><c> we're</c><00:00:28.400><c> talking</c><00:00:28.640><c> with</c>

00:00:28.790 --> 00:00:28.800 align:start position:0%
this video right here we're talking with
 

00:00:28.800 --> 00:00:33.229 align:start position:0%
this video right here we're talking with
my<00:00:29.000><c> man</c><00:00:29.560><c> Andre</c><00:00:30.199><c> V</c><00:00:30.480><c> the</c><00:00:30.640><c> CTO</c><00:00:31.199><c> of</c><00:00:31.439><c> quadrant</c><00:00:32.160><c> all</c>

00:00:33.229 --> 00:00:33.239 align:start position:0%
my man Andre V the CTO of quadrant all
 

00:00:33.239 --> 00:00:37.030 align:start position:0%
my man Andre V the CTO of quadrant all
about<00:00:34.239><c> custom</c><00:00:35.160><c> charting</c><00:00:36.160><c> so</c><00:00:36.440><c> let's</c><00:00:36.680><c> get</c><00:00:36.800><c> into</c>

00:00:37.030 --> 00:00:37.040 align:start position:0%
about custom charting so let's get into
 

00:00:37.040 --> 00:00:39.389 align:start position:0%
about custom charting so let's get into
it<00:00:37.239><c> and</c><00:00:37.440><c> as</c><00:00:37.559><c> a</c><00:00:37.719><c> reminder</c><00:00:38.280><c> if</c><00:00:38.440><c> anybody</c><00:00:38.960><c> wants</c>

00:00:39.389 --> 00:00:39.399 align:start position:0%
it and as a reminder if anybody wants
 

00:00:39.399 --> 00:00:41.869 align:start position:0%
it and as a reminder if anybody wants
one<00:00:39.520><c> of</c><00:00:39.719><c> these</c><00:00:39.920><c> cool</c><00:00:40.200><c> shirts</c><00:00:41.000><c> quadrant</c><00:00:41.640><c> is</c>

00:00:41.869 --> 00:00:41.879 align:start position:0%
one of these cool shirts quadrant is
 

00:00:41.879 --> 00:00:44.150 align:start position:0%
one of these cool shirts quadrant is
just<00:00:42.120><c> giving</c><00:00:42.399><c> them</c><00:00:42.559><c> away</c><00:00:42.840><c> like</c><00:00:43.000><c> hotcakes</c><00:00:43.600><c> baby</c>

00:00:44.150 --> 00:00:44.160 align:start position:0%
just giving them away like hotcakes baby
 

00:00:44.160 --> 00:00:48.470 align:start position:0%
just giving them away like hotcakes baby
Andre<00:00:45.160><c> what</c><00:00:45.399><c> is</c><00:00:46.280><c> custom</c><00:00:46.879><c> sharding</c><00:00:47.879><c> and</c><00:00:48.199><c> why</c><00:00:48.360><c> is</c>

00:00:48.470 --> 00:00:48.480 align:start position:0%
Andre what is custom sharding and why is
 

00:00:48.480 --> 00:00:53.790 align:start position:0%
Andre what is custom sharding and why is
it<00:00:49.160><c> relevant</c><00:00:50.160><c> hi</c><00:00:50.680><c> hi</c><00:00:50.879><c> everyone</c><00:00:51.760><c> uh</c><00:00:52.239><c> yeah</c><00:00:52.640><c> so</c><00:00:53.640><c> as</c>

00:00:53.790 --> 00:00:53.800 align:start position:0%
it relevant hi hi everyone uh yeah so as
 

00:00:53.800 --> 00:00:56.910 align:start position:0%
it relevant hi hi everyone uh yeah so as
you<00:00:54.000><c> might</c><00:00:54.239><c> know</c><00:00:54.920><c> quadrant</c><00:00:55.640><c> have</c><00:00:55.920><c> already</c>

00:00:56.910 --> 00:00:56.920 align:start position:0%
you might know quadrant have already
 

00:00:56.920 --> 00:01:00.270 align:start position:0%
you might know quadrant have already
sharding<00:00:57.680><c> in</c><00:00:57.920><c> place</c><00:00:58.239><c> for</c><00:00:58.640><c> quite</c><00:00:58.879><c> some</c><00:00:59.160><c> time</c><00:01:00.160><c> it</c>

00:01:00.270 --> 00:01:00.280 align:start position:0%
sharding in place for quite some time it
 

00:01:00.280 --> 00:01:03.549 align:start position:0%
sharding in place for quite some time it
allows<00:01:00.680><c> us</c><00:01:00.960><c> to</c><00:01:01.879><c> scale</c><00:01:02.440><c> Beyond</c><00:01:02.920><c> just</c><00:01:03.079><c> a</c><00:01:03.239><c> single</c>

00:01:03.549 --> 00:01:03.559 align:start position:0%
allows us to scale Beyond just a single
 

00:01:03.559 --> 00:01:06.310 align:start position:0%
allows us to scale Beyond just a single
knot<00:01:03.960><c> you</c><00:01:04.080><c> can</c><00:01:04.280><c> spawn</c><00:01:05.199><c> as</c><00:01:05.360><c> many</c><00:01:05.640><c> machines</c><00:01:06.159><c> as</c>

00:01:06.310 --> 00:01:06.320 align:start position:0%
knot you can spawn as many machines as
 

00:01:06.320 --> 00:01:09.390 align:start position:0%
knot you can spawn as many machines as
you<00:01:06.479><c> like</c><00:01:07.280><c> and</c><00:01:07.920><c> data</c><00:01:08.200><c> will</c><00:01:08.400><c> be</c><00:01:08.640><c> automatically</c>

00:01:09.390 --> 00:01:09.400 align:start position:0%
you like and data will be automatically
 

00:01:09.400 --> 00:01:12.390 align:start position:0%
you like and data will be automatically
distributed<00:01:10.080><c> across</c><00:01:10.479><c> them</c><00:01:11.200><c> but</c><00:01:11.479><c> there</c><00:01:11.720><c> were</c><00:01:12.200><c> a</c>

00:01:12.390 --> 00:01:12.400 align:start position:0%
distributed across them but there were a
 

00:01:12.400 --> 00:01:15.070 align:start position:0%
distributed across them but there were a
small<00:01:12.720><c> limitation</c><00:01:13.640><c> is</c><00:01:13.880><c> that</c><00:01:14.680><c> once</c><00:01:14.920><c> you</c>

00:01:15.070 --> 00:01:15.080 align:start position:0%
small limitation is that once you
 

00:01:15.080 --> 00:01:18.390 align:start position:0%
small limitation is that once you
created<00:01:15.759><c> shards</c><00:01:16.759><c> it</c><00:01:17.159><c> was</c><00:01:17.479><c> kind</c><00:01:17.640><c> of</c><00:01:17.799><c> fixed</c><00:01:18.159><c> in</c>

00:01:18.390 --> 00:01:18.400 align:start position:0%
created shards it was kind of fixed in
 

00:01:18.400 --> 00:01:20.590 align:start position:0%
created shards it was kind of fixed in
place<00:01:18.720><c> so</c><00:01:19.000><c> you</c><00:01:19.200><c> could</c><00:01:19.400><c> not</c><00:01:20.000><c> create</c><00:01:20.400><c> more</c>

00:01:20.590 --> 00:01:20.600 align:start position:0%
place so you could not create more
 

00:01:20.600 --> 00:01:22.469 align:start position:0%
place so you could not create more
shards<00:01:21.079><c> inside</c><00:01:21.400><c> the</c><00:01:21.600><c> collection</c><00:01:22.079><c> you</c><00:01:22.240><c> could</c>

00:01:22.469 --> 00:01:22.479 align:start position:0%
shards inside the collection you could
 

00:01:22.479 --> 00:01:24.910 align:start position:0%
shards inside the collection you could
not<00:01:23.119><c> delete</c><00:01:23.479><c> some</c><00:01:23.640><c> shards</c><00:01:24.360><c> you</c><00:01:24.479><c> could</c><00:01:24.680><c> only</c>

00:01:24.910 --> 00:01:24.920 align:start position:0%
not delete some shards you could only
 

00:01:24.920 --> 00:01:28.350 align:start position:0%
not delete some shards you could only
move<00:01:25.200><c> them</c><00:01:25.439><c> around</c><00:01:25.960><c> and</c><00:01:26.159><c> create</c><00:01:26.799><c> replicas</c><00:01:27.799><c> and</c>

00:01:28.350 --> 00:01:28.360 align:start position:0%
move them around and create replicas and
 

00:01:28.360 --> 00:01:32.710 align:start position:0%
move them around and create replicas and
uh<00:01:28.520><c> it</c><00:01:28.680><c> is</c><00:01:28.920><c> fine</c><00:01:29.240><c> for</c><00:01:29.479><c> some</c><00:01:30.119><c> level</c><00:01:30.640><c> of</c><00:01:31.280><c> uh</c><00:01:32.280><c> of</c><00:01:32.600><c> of</c>

00:01:32.710 --> 00:01:32.720 align:start position:0%
uh it is fine for some level of uh of of
 

00:01:32.720 --> 00:01:36.670 align:start position:0%
uh it is fine for some level of uh of of
the<00:01:33.040><c> scale</c><00:01:34.040><c> but</c><00:01:34.560><c> we</c><00:01:34.720><c> want</c><00:01:34.920><c> to</c><00:01:35.159><c> go</c><00:01:35.920><c> beyond</c><00:01:36.399><c> that</c>

00:01:36.670 --> 00:01:36.680 align:start position:0%
the scale but we want to go beyond that
 

00:01:36.680 --> 00:01:38.670 align:start position:0%
the scale but we want to go beyond that
we<00:01:36.840><c> want</c><00:01:37.040><c> to</c><00:01:37.280><c> go</c><00:01:37.960><c> to</c>

00:01:38.670 --> 00:01:38.680 align:start position:0%
we want to go to
 

00:01:38.680 --> 00:01:42.230 align:start position:0%
we want to go to
the<00:01:39.680><c> cilian</c><00:01:40.280><c> scale</c><00:01:40.759><c> deployment</c><00:01:41.759><c> and</c><00:01:41.960><c> that's</c>

00:01:42.230 --> 00:01:42.240 align:start position:0%
the cilian scale deployment and that's
 

00:01:42.240 --> 00:01:45.270 align:start position:0%
the cilian scale deployment and that's
where<00:01:42.680><c> you</c><00:01:42.920><c> actually</c><00:01:43.240><c> need</c><00:01:43.920><c> custom</c><00:01:44.280><c> sharting</c>

00:01:45.270 --> 00:01:45.280 align:start position:0%
where you actually need custom sharting
 

00:01:45.280 --> 00:01:49.510 align:start position:0%
where you actually need custom sharting
so<00:01:45.680><c> what</c><00:01:45.960><c> custom</c><00:01:46.280><c> sharting</c><00:01:46.719><c> actually</c><00:01:47.119><c> does</c><00:01:48.520><c> it</c>

00:01:49.510 --> 00:01:49.520 align:start position:0%
so what custom sharting actually does it
 

00:01:49.520 --> 00:01:53.389 align:start position:0%
so what custom sharting actually does it
uh<00:01:49.680><c> allows</c><00:01:50.079><c> you</c><00:01:50.280><c> to</c><00:01:50.600><c> specify</c><00:01:51.600><c> a</c><00:01:52.200><c> key</c><00:01:53.200><c> which</c>

00:01:53.389 --> 00:01:53.399 align:start position:0%
uh allows you to specify a key which
 

00:01:53.399 --> 00:01:56.830 align:start position:0%
uh allows you to specify a key which
would<00:01:53.600><c> be</c><00:01:53.880><c> used</c><00:01:54.680><c> uh</c><00:01:54.840><c> to</c><00:01:55.360><c> locate</c><00:01:56.039><c> which</c><00:01:56.280><c> chart</c>

00:01:56.830 --> 00:01:56.840 align:start position:0%
would be used uh to locate which chart
 

00:01:56.840 --> 00:01:59.190 align:start position:0%
would be used uh to locate which chart
should<00:01:57.079><c> be</c><00:01:57.240><c> used</c><00:01:57.520><c> for</c><00:01:57.759><c> your</c><00:01:58.039><c> data</c><00:01:58.640><c> so</c><00:01:58.840><c> whenever</c>

00:01:59.190 --> 00:01:59.200 align:start position:0%
should be used for your data so whenever
 

00:01:59.200 --> 00:02:01.789 align:start position:0%
should be used for your data so whenever
you<00:01:59.399><c> upload</c><00:01:59.960><c> something</c><00:02:00.759><c> you</c><00:02:00.920><c> can</c><00:02:01.159><c> specify</c>

00:02:01.789 --> 00:02:01.799 align:start position:0%
you upload something you can specify
 

00:02:01.799 --> 00:02:04.469 align:start position:0%
you upload something you can specify
this<00:02:02.600><c> and</c><00:02:03.079><c> quadrant</c><00:02:03.560><c> will</c><00:02:03.799><c> automatically</c>

00:02:04.469 --> 00:02:04.479 align:start position:0%
this and quadrant will automatically
 

00:02:04.479 --> 00:02:07.109 align:start position:0%
this and quadrant will automatically
know<00:02:05.200><c> on</c><00:02:05.479><c> which</c><00:02:05.719><c> machine</c><00:02:06.360><c> your</c><00:02:06.640><c> data</c><00:02:06.880><c> should</c>

00:02:07.109 --> 00:02:07.119 align:start position:0%
know on which machine your data should
 

00:02:07.119 --> 00:02:09.309 align:start position:0%
know on which machine your data should
be<00:02:07.320><c> placed</c><00:02:08.200><c> and</c><00:02:08.360><c> it</c><00:02:08.520><c> works</c><00:02:08.879><c> in</c><00:02:09.039><c> another</c>

00:02:09.309 --> 00:02:09.319 align:start position:0%
be placed and it works in another
 

00:02:09.319 --> 00:02:11.190 align:start position:0%
be placed and it works in another
direction<00:02:09.720><c> as</c><00:02:09.920><c> well</c><00:02:10.319><c> whenever</c><00:02:10.640><c> you</c><00:02:10.800><c> search</c>

00:02:11.190 --> 00:02:11.200 align:start position:0%
direction as well whenever you search
 

00:02:11.200 --> 00:02:14.390 align:start position:0%
direction as well whenever you search
for<00:02:11.440><c> something</c><00:02:11.879><c> you</c><00:02:12.040><c> can</c><00:02:12.280><c> specify</c><00:02:13.000><c> A</c><00:02:13.160><c> Shard</c><00:02:13.920><c> or</c>

00:02:14.390 --> 00:02:14.400 align:start position:0%
for something you can specify A Shard or
 

00:02:14.400 --> 00:02:17.309 align:start position:0%
for something you can specify A Shard or
several<00:02:14.840><c> shards</c><00:02:15.640><c> and</c><00:02:15.879><c> quadrant</c><00:02:16.319><c> will</c><00:02:16.519><c> know</c>

00:02:17.309 --> 00:02:17.319 align:start position:0%
several shards and quadrant will know
 

00:02:17.319 --> 00:02:21.110 align:start position:0%
several shards and quadrant will know
where<00:02:17.560><c> to</c><00:02:17.760><c> find</c><00:02:18.120><c> them</c><00:02:18.840><c> and</c><00:02:19.360><c> avoid</c><00:02:20.360><c> asking</c><00:02:20.840><c> all</c>

00:02:21.110 --> 00:02:21.120 align:start position:0%
where to find them and avoid asking all
 

00:02:21.120 --> 00:02:24.309 align:start position:0%
where to find them and avoid asking all
machines<00:02:21.680><c> in</c><00:02:21.840><c> your</c><00:02:22.519><c> cluster</c><00:02:23.519><c> for</c><00:02:23.879><c> for</c><00:02:24.160><c> the</c>

00:02:24.309 --> 00:02:24.319 align:start position:0%
machines in your cluster for for the
 

00:02:24.319 --> 00:02:28.110 align:start position:0%
machines in your cluster for for the
results<00:02:25.280><c> it</c><00:02:25.440><c> will</c><00:02:26.120><c> uh</c><00:02:26.599><c> minimize</c><00:02:27.120><c> the</c><00:02:27.239><c> overhead</c>

00:02:28.110 --> 00:02:28.120 align:start position:0%
results it will uh minimize the overhead
 

00:02:28.120 --> 00:02:31.270 align:start position:0%
results it will uh minimize the overhead
and<00:02:28.560><c> maximize</c><00:02:29.040><c> the</c><00:02:29.200><c> performance</c>

00:02:31.270 --> 00:02:31.280 align:start position:0%
and maximize the performance
 

00:02:31.280 --> 00:02:33.390 align:start position:0%
and maximize the performance
yeah<00:02:31.760><c> that's</c><00:02:32.120><c> that's</c><00:02:32.280><c> basic</c>

00:02:33.390 --> 00:02:33.400 align:start position:0%
yeah that's that's basic
 

00:02:33.400 --> 00:02:37.270 align:start position:0%
yeah that's that's basic
C<00:02:34.400><c> okay</c><00:02:35.000><c> so</c><00:02:35.560><c> if</c><00:02:36.120><c> I'm</c><00:02:36.640><c> understanding</c><00:02:37.000><c> this</c>

00:02:37.270 --> 00:02:37.280 align:start position:0%
C okay so if I'm understanding this
 

00:02:37.280 --> 00:02:39.949 align:start position:0%
C okay so if I'm understanding this
correctly<00:02:38.280><c> you</c><00:02:38.440><c> get</c><00:02:38.560><c> to</c><00:02:38.800><c> say</c><00:02:39.360><c> exactly</c><00:02:39.800><c> which</c>

00:02:39.949 --> 00:02:39.959 align:start position:0%
correctly you get to say exactly which
 

00:02:39.959 --> 00:02:42.509 align:start position:0%
correctly you get to say exactly which
Shard<00:02:40.319><c> you</c><00:02:40.400><c> wanted</c><00:02:40.640><c> to</c><00:02:40.800><c> go</c><00:02:40.959><c> in</c><00:02:41.440><c> just</c><00:02:41.680><c> by</c><00:02:42.200><c> that</c>

00:02:42.509 --> 00:02:42.519 align:start position:0%
Shard you wanted to go in just by that
 

00:02:42.519 --> 00:02:44.030 align:start position:0%
Shard you wanted to go in just by that
that<00:02:42.640><c> you're</c><00:02:42.760><c> showing</c><00:02:43.040><c> on</c><00:02:43.159><c> the</c><00:02:43.319><c> screen</c><00:02:43.920><c> which</c>

00:02:44.030 --> 00:02:44.040 align:start position:0%
that you're showing on the screen which
 

00:02:44.040 --> 00:02:47.550 align:start position:0%
that you're showing on the screen which
is<00:02:44.280><c> just</c><00:02:44.480><c> like</c><00:02:44.680><c> Shard</c><00:02:45.120><c> key</c><00:02:45.480><c> my</c><00:02:45.879><c> Shard</c><00:02:46.879><c> and</c><00:02:47.360><c> I'm</c>

00:02:47.550 --> 00:02:47.560 align:start position:0%
is just like Shard key my Shard and I'm
 

00:02:47.560 --> 00:02:51.509 align:start position:0%
is just like Shard key my Shard and I'm
wondering<00:02:48.120><c> about</c><00:02:49.040><c> the</c><00:02:49.480><c> scaling</c><00:02:50.280><c> of</c><00:02:50.599><c> this</c><00:02:51.120><c> and</c>

00:02:51.509 --> 00:02:51.519 align:start position:0%
wondering about the scaling of this and
 

00:02:51.519 --> 00:02:54.309 align:start position:0%
wondering about the scaling of this and
if<00:02:51.680><c> it</c><00:02:51.920><c> is</c><00:02:52.319><c> dynamic</c><00:02:53.280><c> like</c><00:02:53.440><c> if</c><00:02:53.800><c> all</c><00:02:53.920><c> of</c><00:02:54.000><c> a</c><00:02:54.080><c> sudden</c>

00:02:54.309 --> 00:02:54.319 align:start position:0%
if it is dynamic like if all of a sudden
 

00:02:54.319 --> 00:02:56.550 align:start position:0%
if it is dynamic like if all of a sudden
I<00:02:54.440><c> throw</c><00:02:54.680><c> in</c><00:02:54.920><c> too</c><00:02:55.120><c> much</c><00:02:55.800><c> what</c><00:02:55.920><c> does</c><00:02:56.120><c> that</c><00:02:56.319><c> look</c>

00:02:56.550 --> 00:02:56.560 align:start position:0%
I throw in too much what does that look
 

00:02:56.560 --> 00:02:58.070 align:start position:0%
I throw in too much what does that look
like<00:02:56.879><c> I</c><00:02:56.959><c> feel</c><00:02:57.159><c> like</c><00:02:57.319><c> there's</c><00:02:57.599><c> something</c>

00:02:58.070 --> 00:02:58.080 align:start position:0%
like I feel like there's something
 

00:02:58.080 --> 00:02:59.309 align:start position:0%
like I feel like there's something
special<00:02:58.400><c> in</c><00:02:58.599><c> there</c><00:02:58.760><c> that</c><00:02:58.840><c> you</c><00:02:58.920><c> want</c><00:02:59.040><c> to</c><00:02:59.159><c> talk</c>

00:02:59.309 --> 00:02:59.319 align:start position:0%
special in there that you want to talk
 

00:02:59.319 --> 00:03:00.550 align:start position:0%
special in there that you want to talk
to<00:02:59.400><c> us</c><00:02:59.560><c> about</c>

00:03:00.550 --> 00:03:00.560 align:start position:0%
to us about
 

00:03:00.560 --> 00:03:05.070 align:start position:0%
to us about
yeah<00:03:00.840><c> exactly</c><00:03:01.599><c> so</c><00:03:02.360><c> uh</c><00:03:02.599><c> we</c><00:03:03.360><c> introduce</c><00:03:04.360><c> uh</c><00:03:04.760><c> two</c>

00:03:05.070 --> 00:03:05.080 align:start position:0%
yeah exactly so uh we introduce uh two
 

00:03:05.080 --> 00:03:08.789 align:start position:0%
yeah exactly so uh we introduce uh two
new<00:03:05.840><c> API</c><00:03:06.560><c> endpoints</c><00:03:07.560><c> one</c><00:03:07.959><c> for</c><00:03:08.319><c> creating</c>

00:03:08.789 --> 00:03:08.799 align:start position:0%
new API endpoints one for creating
 

00:03:08.799 --> 00:03:12.110 align:start position:0%
new API endpoints one for creating
sharts<00:03:09.640><c> and</c><00:03:09.840><c> one</c><00:03:10.000><c> for</c><00:03:10.239><c> deleting</c><00:03:10.680><c> sharts</c><00:03:11.480><c> so</c>

00:03:12.110 --> 00:03:12.120 align:start position:0%
sharts and one for deleting sharts so
 

00:03:12.120 --> 00:03:15.190 align:start position:0%
sharts and one for deleting sharts so
whenever<00:03:12.519><c> you</c><00:03:12.760><c> have</c><00:03:13.360><c> a</c><00:03:14.040><c> collection</c><00:03:15.040><c> uh</c>

00:03:15.190 --> 00:03:15.200 align:start position:0%
whenever you have a collection uh
 

00:03:15.200 --> 00:03:17.270 align:start position:0%
whenever you have a collection uh
configure<00:03:16.000><c> to</c><00:03:16.239><c> support</c><00:03:16.720><c> this</c><00:03:16.959><c> custom</c>

00:03:17.270 --> 00:03:17.280 align:start position:0%
configure to support this custom
 

00:03:17.280 --> 00:03:19.630 align:start position:0%
configure to support this custom
sharting<00:03:18.080><c> you</c><00:03:18.200><c> can</c><00:03:18.560><c> create</c><00:03:19.000><c> create</c><00:03:19.440><c> new</c>

00:03:19.630 --> 00:03:19.640 align:start position:0%
sharting you can create create new
 

00:03:19.640 --> 00:03:22.070 align:start position:0%
sharting you can create create new
shards<00:03:20.120><c> you</c><00:03:20.200><c> can</c><00:03:20.400><c> delete</c><00:03:20.680><c> shards</c><00:03:21.159><c> dynamically</c>

00:03:22.070 --> 00:03:22.080 align:start position:0%
shards you can delete shards dynamically
 

00:03:22.080 --> 00:03:25.270 align:start position:0%
shards you can delete shards dynamically
without<00:03:22.480><c> restarts</c><00:03:23.000><c> without</c><00:03:23.519><c> downtime</c><00:03:24.519><c> and</c>

00:03:25.270 --> 00:03:25.280 align:start position:0%
without restarts without downtime and
 

00:03:25.280 --> 00:03:26.229 align:start position:0%
without restarts without downtime and
this

00:03:26.229 --> 00:03:26.239 align:start position:0%
this
 

00:03:26.239 --> 00:03:30.990 align:start position:0%
this
actually<00:03:27.239><c> gives</c><00:03:27.760><c> us</c><00:03:28.760><c> um</c><00:03:29.480><c> a</c><00:03:29.799><c> three</c><00:03:30.120><c> level</c><00:03:30.560><c> of</c>

00:03:30.990 --> 00:03:31.000 align:start position:0%
actually gives us um a three level of
 

00:03:31.000 --> 00:03:33.710 align:start position:0%
actually gives us um a three level of
isolation<00:03:32.000><c> of</c><00:03:32.159><c> your</c><00:03:32.400><c> data</c><00:03:32.879><c> so</c><00:03:33.080><c> we</c><00:03:33.239><c> already</c>

00:03:33.710 --> 00:03:33.720 align:start position:0%
isolation of your data so we already
 

00:03:33.720 --> 00:03:36.670 align:start position:0%
isolation of your data so we already
have<00:03:34.200><c> a</c><00:03:34.439><c> collection</c><00:03:34.959><c> based</c><00:03:35.560><c> isolation</c><00:03:36.519><c> we</c>

00:03:36.670 --> 00:03:36.680 align:start position:0%
have a collection based isolation we
 

00:03:36.680 --> 00:03:39.789 align:start position:0%
have a collection based isolation we
already<00:03:37.120><c> had</c><00:03:37.640><c> a</c><00:03:37.840><c> pad</c><00:03:38.239><c> based</c><00:03:38.920><c> isolation</c><00:03:39.599><c> which</c>

00:03:39.789 --> 00:03:39.799 align:start position:0%
already had a pad based isolation which
 

00:03:39.799 --> 00:03:43.350 align:start position:0%
already had a pad based isolation which
is<00:03:40.280><c> uh</c><00:03:40.920><c> frequently</c><00:03:41.519><c> used</c><00:03:41.920><c> to</c><00:03:42.599><c> uh</c><00:03:42.879><c> create</c><00:03:43.200><c> a</c>

00:03:43.350 --> 00:03:43.360 align:start position:0%
is uh frequently used to uh create a
 

00:03:43.360 --> 00:03:47.429 align:start position:0%
is uh frequently used to uh create a
multi-tenancy<00:03:44.200><c> setup</c><00:03:45.200><c> but</c><00:03:45.439><c> now</c><00:03:46.319><c> we</c><00:03:46.560><c> have</c><00:03:46.920><c> this</c>

00:03:47.429 --> 00:03:47.439 align:start position:0%
multi-tenancy setup but now we have this
 

00:03:47.439 --> 00:03:49.750 align:start position:0%
multi-tenancy setup but now we have this
resource-based<00:03:48.439><c> isolation</c><00:03:49.200><c> where</c><00:03:49.400><c> you</c><00:03:49.599><c> have</c>

00:03:49.750 --> 00:03:49.760 align:start position:0%
resource-based isolation where you have
 

00:03:49.760 --> 00:03:52.630 align:start position:0%
resource-based isolation where you have
a<00:03:49.920><c> single</c><00:03:50.280><c> collection</c><00:03:51.200><c> but</c><00:03:51.400><c> you</c><00:03:51.560><c> can</c><00:03:52.360><c> um</c>

00:03:52.630 --> 00:03:52.640 align:start position:0%
a single collection but you can um
 

00:03:52.640 --> 00:03:56.110 align:start position:0%
a single collection but you can um
manipulate<00:03:53.560><c> and</c><00:03:54.239><c> uh</c><00:03:54.720><c> customize</c><00:03:55.640><c> the</c>

00:03:56.110 --> 00:03:56.120 align:start position:0%
manipulate and uh customize the
 

00:03:56.120 --> 00:03:58.789 align:start position:0%
manipulate and uh customize the
placement<00:03:56.640><c> of</c><00:03:56.760><c> shots</c><00:03:57.280><c> inside</c><00:03:57.640><c> your</c><00:03:57.879><c> cluster</c>

00:03:58.789 --> 00:03:58.799 align:start position:0%
placement of shots inside your cluster
 

00:03:58.799 --> 00:04:02.550 align:start position:0%
placement of shots inside your cluster
more<00:03:59.439><c> like</c><00:03:59.879><c> accurately</c><00:04:00.599><c> more</c><00:04:00.920><c> more</c><00:04:01.560><c> precise</c>

00:04:02.550 --> 00:04:02.560 align:start position:0%
more like accurately more more precise
 

00:04:02.560 --> 00:04:03.589 align:start position:0%
more like accurately more more precise
and

00:04:03.589 --> 00:04:03.599 align:start position:0%
and
 

00:04:03.599 --> 00:04:05.710 align:start position:0%
and
avoid<00:04:04.599><c> any</c><00:04:04.840><c> kind</c><00:04:05.000><c> of</c>

00:04:05.710 --> 00:04:05.720 align:start position:0%
avoid any kind of
 

00:04:05.720 --> 00:04:10.069 align:start position:0%
avoid any kind of
overhead<00:04:06.720><c> um</c><00:04:07.439><c> for</c><00:04:07.640><c> for</c><00:04:07.920><c> this</c><00:04:08.480><c> book</c><00:04:09.480><c> and</c><00:04:09.720><c> for</c>

00:04:10.069 --> 00:04:10.079 align:start position:0%
overhead um for for this book and for
 

00:04:10.079 --> 00:04:12.390 align:start position:0%
overhead um for for this book and for
those<00:04:10.480><c> who</c><00:04:10.959><c> potentially</c><00:04:11.599><c> haven't</c><00:04:12.000><c> played</c>

00:04:12.390 --> 00:04:12.400 align:start position:0%
those who potentially haven't played
 

00:04:12.400 --> 00:04:15.430 align:start position:0%
those who potentially haven't played
around<00:04:12.760><c> or</c><00:04:12.959><c> don't</c><00:04:13.280><c> live</c><00:04:13.519><c> in</c><00:04:14.079><c> Cassandra</c>

00:04:15.430 --> 00:04:15.440 align:start position:0%
around or don't live in Cassandra
 

00:04:15.440 --> 00:04:20.310 align:start position:0%
around or don't live in Cassandra
land<00:04:16.440><c> what</c><00:04:17.320><c> problem</c><00:04:18.320><c> does</c><00:04:18.680><c> sharding</c>

00:04:20.310 --> 00:04:20.320 align:start position:0%
land what problem does sharding
 

00:04:20.320 --> 00:04:22.710 align:start position:0%
land what problem does sharding
solve<00:04:21.320><c> right</c><00:04:21.560><c> so</c><00:04:21.959><c> uh</c><00:04:22.079><c> there</c><00:04:22.160><c> are</c><00:04:22.320><c> several</c>

00:04:22.710 --> 00:04:22.720 align:start position:0%
solve right so uh there are several
 

00:04:22.720 --> 00:04:24.710 align:start position:0%
solve right so uh there are several
scenarios<00:04:23.320><c> which</c><00:04:23.440><c> you</c><00:04:23.840><c> might</c><00:04:24.040><c> be</c><00:04:24.240><c> interested</c>

00:04:24.710 --> 00:04:24.720 align:start position:0%
scenarios which you might be interested
 

00:04:24.720 --> 00:04:29.670 align:start position:0%
scenarios which you might be interested
in<00:04:25.720><c> first</c><00:04:26.280><c> is</c><00:04:26.919><c> uh</c><00:04:27.040><c> a</c><00:04:27.479><c> regional</c><00:04:28.479><c> region</c><00:04:29.039><c> based</c>

00:04:29.670 --> 00:04:29.680 align:start position:0%
in first is uh a regional region based
 

00:04:29.680 --> 00:04:32.390 align:start position:0%
in first is uh a regional region based
data<00:04:30.000><c> placement</c><00:04:30.440><c> so</c><00:04:30.639><c> imagine</c><00:04:31.000><c> you</c><00:04:31.240><c> have</c><00:04:32.120><c> uh</c>

00:04:32.390 --> 00:04:32.400 align:start position:0%
data placement so imagine you have uh
 

00:04:32.400 --> 00:04:35.670 align:start position:0%
data placement so imagine you have uh
customers<00:04:33.039><c> all</c><00:04:33.240><c> around</c><00:04:33.520><c> the</c><00:04:33.800><c> world</c><00:04:34.800><c> and</c><00:04:35.400><c> while</c>

00:04:35.670 --> 00:04:35.680 align:start position:0%
customers all around the world and while
 

00:04:35.680 --> 00:04:39.830 align:start position:0%
customers all around the world and while
customers<00:04:36.240><c> want</c><00:04:36.479><c> to</c><00:04:36.720><c> query</c><00:04:37.160><c> data</c><00:04:37.600><c> from</c><00:04:38.600><c> uh</c><00:04:38.840><c> us</c>

00:04:39.830 --> 00:04:39.840 align:start position:0%
customers want to query data from uh us
 

00:04:39.840 --> 00:04:42.230 align:start position:0%
customers want to query data from uh us
other<00:04:40.199><c> customers</c><00:04:40.840><c> want</c><00:04:41.080><c> to</c><00:04:41.280><c> query</c><00:04:41.680><c> data</c><00:04:41.960><c> from</c>

00:04:42.230 --> 00:04:42.240 align:start position:0%
other customers want to query data from
 

00:04:42.240 --> 00:04:45.189 align:start position:0%
other customers want to query data from
Europe<00:04:43.240><c> and</c><00:04:43.479><c> to</c><00:04:43.759><c> achieve</c><00:04:44.199><c> this</c><00:04:44.720><c> within</c><00:04:45.039><c> the</c>

00:04:45.189 --> 00:04:45.199 align:start position:0%
Europe and to achieve this within the
 

00:04:45.199 --> 00:04:47.270 align:start position:0%
Europe and to achieve this within the
same<00:04:45.479><c> cluster</c><00:04:46.240><c> what</c><00:04:46.360><c> you</c><00:04:46.479><c> can</c><00:04:46.680><c> do</c><00:04:46.880><c> you</c><00:04:47.000><c> can</c>

00:04:47.270 --> 00:04:47.280 align:start position:0%
same cluster what you can do you can
 

00:04:47.280 --> 00:04:51.909 align:start position:0%
same cluster what you can do you can
just<00:04:47.840><c> assign</c><00:04:48.840><c> uh</c><00:04:49.039><c> sharp</c><00:04:49.560><c> Keys</c><00:04:50.440><c> based</c><00:04:50.880><c> on</c><00:04:51.680><c> the</c>

00:04:51.909 --> 00:04:51.919 align:start position:0%
just assign uh sharp Keys based on the
 

00:04:51.919 --> 00:04:53.830 align:start position:0%
just assign uh sharp Keys based on the
data<00:04:52.280><c> location</c><00:04:52.759><c> so</c><00:04:52.919><c> you</c><00:04:53.039><c> would</c><00:04:53.280><c> have</c><00:04:53.600><c> one</c>

00:04:53.830 --> 00:04:53.840 align:start position:0%
data location so you would have one
 

00:04:53.840 --> 00:04:57.430 align:start position:0%
data location so you would have one
Shard<00:04:54.320><c> for</c><00:04:54.919><c> Europe</c><00:04:55.360><c> one</c><00:04:55.560><c> sh</c><00:04:56.000><c> Shard</c><00:04:56.400><c> for</c><00:04:57.000><c> Asia</c>

00:04:57.430 --> 00:04:57.440 align:start position:0%
Shard for Europe one sh Shard for Asia
 

00:04:57.440 --> 00:04:59.830 align:start position:0%
Shard for Europe one sh Shard for Asia
one<00:04:57.600><c> Shard</c><00:04:58.000><c> for</c><00:04:58.280><c> United</c><00:04:58.720><c> States</c><00:04:59.360><c> and</c><00:04:59.560><c> and</c><00:04:59.680><c> so</c>

00:04:59.830 --> 00:04:59.840 align:start position:0%
one Shard for United States and and so
 

00:04:59.840 --> 00:05:02.950 align:start position:0%
one Shard for United States and and so
on<00:05:00.240><c> and</c><00:05:00.880><c> each</c><00:05:01.199><c> customer</c><00:05:01.680><c> will</c><00:05:01.919><c> only</c><00:05:02.320><c> query</c><00:05:02.759><c> the</c>

00:05:02.950 --> 00:05:02.960 align:start position:0%
on and each customer will only query the
 

00:05:02.960 --> 00:05:06.990 align:start position:0%
on and each customer will only query the
region<00:05:03.639><c> they</c><00:05:03.880><c> actually</c><00:05:04.759><c> interested</c><00:05:05.759><c> in</c><00:05:06.280><c> and</c>

00:05:06.990 --> 00:05:07.000 align:start position:0%
region they actually interested in and
 

00:05:07.000 --> 00:05:11.189 align:start position:0%
region they actually interested in and
it<00:05:07.160><c> will</c><00:05:08.160><c> allow</c><00:05:08.440><c> you</c><00:05:08.600><c> to</c><00:05:08.919><c> avoid</c><00:05:09.360><c> this</c><00:05:09.800><c> cross</c><00:05:10.800><c> uh</c>

00:05:11.189 --> 00:05:11.199 align:start position:0%
it will allow you to avoid this cross uh
 

00:05:11.199 --> 00:05:13.270 align:start position:0%
it will allow you to avoid this cross uh
Continental<00:05:11.880><c> traffic</c><00:05:12.600><c> which</c><00:05:12.720><c> is</c><00:05:12.919><c> kind</c><00:05:13.080><c> of</c>

00:05:13.270 --> 00:05:13.280 align:start position:0%
Continental traffic which is kind of
 

00:05:13.280 --> 00:05:15.710 align:start position:0%
Continental traffic which is kind of
expensive<00:05:14.080><c> at</c><00:05:14.199><c> the</c><00:05:14.360><c> same</c><00:05:14.680><c> time</c><00:05:15.120><c> you</c><00:05:15.240><c> will</c><00:05:15.520><c> be</c>

00:05:15.710 --> 00:05:15.720 align:start position:0%
expensive at the same time you will be
 

00:05:15.720 --> 00:05:18.070 align:start position:0%
expensive at the same time you will be
able<00:05:16.080><c> to</c><00:05:16.479><c> manage</c><00:05:16.960><c> your</c><00:05:17.199><c> data</c><00:05:17.479><c> from</c><00:05:17.680><c> the</c><00:05:17.840><c> one</c>

00:05:18.070 --> 00:05:18.080 align:start position:0%
able to manage your data from the one
 

00:05:18.080 --> 00:05:20.430 align:start position:0%
able to manage your data from the one
place<00:05:18.360><c> from</c><00:05:18.520><c> the</c><00:05:18.680><c> one</c><00:05:18.919><c> collection</c><00:05:19.800><c> and</c><00:05:20.199><c> one</c>

00:05:20.430 --> 00:05:20.440 align:start position:0%
place from the one collection and one
 

00:05:20.440 --> 00:05:24.270 align:start position:0%
place from the one collection and one
cluster<00:05:20.880><c> and</c><00:05:21.120><c> even</c><00:05:21.840><c> uh</c><00:05:21.960><c> enable</c><00:05:22.520><c> a</c><00:05:23.280><c> replication</c>

00:05:24.270 --> 00:05:24.280 align:start position:0%
cluster and even uh enable a replication
 

00:05:24.280 --> 00:05:26.309 align:start position:0%
cluster and even uh enable a replication
across<00:05:25.199><c> different</c>

00:05:26.309 --> 00:05:26.319 align:start position:0%
across different
 

00:05:26.319 --> 00:05:28.950 align:start position:0%
across different
regions<00:05:27.319><c> yeah</c><00:05:27.520><c> and</c><00:05:27.720><c> this</c><00:05:28.080><c> actually</c><00:05:28.600><c> I</c><00:05:28.680><c> know</c><00:05:28.880><c> a</c>

00:05:28.950 --> 00:05:28.960 align:start position:0%
regions yeah and this actually I know a
 

00:05:28.960 --> 00:05:31.710 align:start position:0%
regions yeah and this actually I know a
lot<00:05:29.080><c> of</c><00:05:29.160><c> you</c><00:05:29.600><c> cases</c><00:05:30.160><c> of</c><00:05:31.000><c> people</c><00:05:31.280><c> working</c><00:05:31.560><c> in</c>

00:05:31.710 --> 00:05:31.720 align:start position:0%
lot of you cases of people working in
 

00:05:31.720 --> 00:05:35.029 align:start position:0%
lot of you cases of people working in
the<00:05:31.840><c> health</c><00:05:32.160><c> care</c><00:05:32.600><c> space</c><00:05:33.600><c> where</c><00:05:34.440><c> they</c><00:05:34.720><c> can't</c>

00:05:35.029 --> 00:05:35.039 align:start position:0%
the health care space where they can't
 

00:05:35.039 --> 00:05:37.029 align:start position:0%
the health care space where they can't
for<00:05:35.280><c> maybe</c><00:05:35.560><c> some</c><00:05:35.800><c> kind</c><00:05:35.919><c> of</c><00:05:36.080><c> hippoc</c><00:05:36.479><c> compliance</c>

00:05:37.029 --> 00:05:37.039 align:start position:0%
for maybe some kind of hippoc compliance
 

00:05:37.039 --> 00:05:40.029 align:start position:0%
for maybe some kind of hippoc compliance
or<00:05:37.319><c> if</c><00:05:37.759><c> I</c><00:05:37.840><c> know</c><00:05:38.039><c> if</c><00:05:38.120><c> you're</c><00:05:38.280><c> in</c><00:05:38.680><c> Canada</c><00:05:39.680><c> data</c>

00:05:40.029 --> 00:05:40.039 align:start position:0%
or if I know if you're in Canada data
 

00:05:40.039 --> 00:05:41.870 align:start position:0%
or if I know if you're in Canada data
that<00:05:40.199><c> is</c><00:05:40.360><c> related</c><00:05:40.720><c> to</c><00:05:40.919><c> healthcare</c><00:05:41.440><c> data</c>

00:05:41.870 --> 00:05:41.880 align:start position:0%
that is related to healthcare data
 

00:05:41.880 --> 00:05:44.830 align:start position:0%
that is related to healthcare data
cannot<00:05:42.319><c> leave</c><00:05:42.800><c> Canada</c><00:05:43.720><c> so</c><00:05:44.120><c> even</c><00:05:44.360><c> if</c><00:05:44.479><c> your</c>

00:05:44.830 --> 00:05:44.840 align:start position:0%
cannot leave Canada so even if your
 

00:05:44.840 --> 00:05:46.990 align:start position:0%
cannot leave Canada so even if your
company<00:05:45.160><c> is</c><00:05:45.319><c> global</c><00:05:46.080><c> you</c><00:05:46.240><c> can't</c><00:05:46.440><c> have</c><00:05:46.639><c> any</c><00:05:46.800><c> of</c>

00:05:46.990 --> 00:05:47.000 align:start position:0%
company is global you can't have any of
 

00:05:47.000 --> 00:05:49.710 align:start position:0%
company is global you can't have any of
that<00:05:47.440><c> Canadian</c><00:05:48.080><c> data</c><00:05:48.440><c> going</c><00:05:48.800><c> anywhere</c><00:05:49.360><c> and</c><00:05:49.639><c> I</c>

00:05:49.710 --> 00:05:49.720 align:start position:0%
that Canadian data going anywhere and I
 

00:05:49.720 --> 00:05:51.990 align:start position:0%
that Canadian data going anywhere and I
feel<00:05:49.960><c> like</c><00:05:50.160><c> this</c><00:05:50.280><c> is</c><00:05:50.520><c> perfect</c><00:05:50.800><c> for</c><00:05:51.000><c> that</c><00:05:51.240><c> right</c>

00:05:51.990 --> 00:05:52.000 align:start position:0%
feel like this is perfect for that right
 

00:05:52.000 --> 00:05:53.950 align:start position:0%
feel like this is perfect for that right
right<00:05:52.360><c> that</c><00:05:52.639><c> yeah</c><00:05:53.039><c> that's</c><00:05:53.360><c> that's</c><00:05:53.600><c> another</c>

00:05:53.950 --> 00:05:53.960 align:start position:0%
right that yeah that's that's another
 

00:05:53.960 --> 00:05:56.909 align:start position:0%
right that yeah that's that's another
reason<00:05:54.600><c> uh</c><00:05:54.960><c> you</c><00:05:55.160><c> still</c><00:05:55.600><c> want</c><00:05:56.000><c> the</c><00:05:56.240><c> data</c><00:05:56.520><c> to</c><00:05:56.680><c> be</c>

00:05:56.909 --> 00:05:56.919 align:start position:0%
reason uh you still want the data to be
 

00:05:56.919 --> 00:05:59.469 align:start position:0%
reason uh you still want the data to be
available<00:05:57.479><c> from</c><00:05:57.720><c> other</c><00:05:58.000><c> regions</c><00:05:59.000><c> but</c><00:05:59.160><c> you</c>

00:05:59.469 --> 00:05:59.479 align:start position:0%
available from other regions but you
 

00:05:59.479 --> 00:06:02.390 align:start position:0%
available from other regions but you
want<00:05:59.639><c> to</c><00:05:59.800><c> keep</c><00:06:00.039><c> them</c><00:06:00.400><c> in</c><00:06:01.000><c> a</c><00:06:01.240><c> specific</c><00:06:01.639><c> Shard</c>

00:06:02.390 --> 00:06:02.400 align:start position:0%
want to keep them in a specific Shard
 

00:06:02.400 --> 00:06:04.749 align:start position:0%
want to keep them in a specific Shard
which<00:06:02.520><c> is</c><00:06:02.720><c> located</c><00:06:03.160><c> on</c><00:06:03.319><c> a</c><00:06:03.520><c> specific</c><00:06:03.960><c> machine</c>

00:06:04.749 --> 00:06:04.759 align:start position:0%
which is located on a specific machine
 

00:06:04.759 --> 00:06:07.710 align:start position:0%
which is located on a specific machine
exactly<00:06:05.319><c> and</c><00:06:05.800><c> uh</c><00:06:06.039><c> do</c><00:06:06.199><c> not</c><00:06:06.440><c> do</c><00:06:06.599><c> not</c><00:06:06.759><c> leave</c><00:06:07.120><c> the</c>

00:06:07.710 --> 00:06:07.720 align:start position:0%
exactly and uh do not do not leave the
 

00:06:07.720 --> 00:06:10.430 align:start position:0%
exactly and uh do not do not leave the
the<00:06:08.160><c> region</c><00:06:09.160><c> uh</c><00:06:09.360><c> that</c><00:06:09.520><c> was</c><00:06:09.639><c> the</c><00:06:09.840><c> first</c><00:06:10.120><c> use</c>

00:06:10.430 --> 00:06:10.440 align:start position:0%
the region uh that was the first use
 

00:06:10.440 --> 00:06:13.150 align:start position:0%
the region uh that was the first use
case<00:06:11.080><c> uh</c><00:06:11.240><c> the</c><00:06:11.400><c> Second</c><00:06:11.720><c> Use</c><00:06:12.080><c> case</c><00:06:12.560><c> is</c><00:06:12.759><c> actually</c>

00:06:13.150 --> 00:06:13.160 align:start position:0%
case uh the Second Use case is actually
 

00:06:13.160 --> 00:06:16.150 align:start position:0%
case uh the Second Use case is actually
a<00:06:13.360><c> time-based</c><00:06:14.280><c> data</c><00:06:14.639><c> placement</c><00:06:15.080><c> so</c><00:06:15.360><c> it</c><00:06:15.520><c> is</c>

00:06:16.150 --> 00:06:16.160 align:start position:0%
a time-based data placement so it is
 

00:06:16.160 --> 00:06:20.350 align:start position:0%
a time-based data placement so it is
similar<00:06:16.639><c> to</c><00:06:17.639><c> uh</c><00:06:17.840><c> region</c><00:06:18.360><c> but</c><00:06:19.080><c> in</c><00:06:19.280><c> this</c><00:06:19.479><c> case</c>

00:06:20.350 --> 00:06:20.360 align:start position:0%
similar to uh region but in this case
 

00:06:20.360 --> 00:06:23.749 align:start position:0%
similar to uh region but in this case
let's<00:06:20.880><c> let's</c><00:06:21.039><c> say</c><00:06:21.240><c> you</c><00:06:21.440><c> have</c><00:06:21.720><c> a</c><00:06:22.080><c> a</c><00:06:22.880><c> a</c><00:06:23.039><c> stream</c><00:06:23.479><c> of</c>

00:06:23.749 --> 00:06:23.759 align:start position:0%
let's let's say you have a a a stream of
 

00:06:23.759 --> 00:06:27.629 align:start position:0%
let's let's say you have a a a stream of
data<00:06:24.759><c> and</c><00:06:24.919><c> you're</c><00:06:25.160><c> only</c><00:06:25.479><c> interested</c><00:06:26.120><c> in</c><00:06:26.639><c> the</c>

00:06:27.629 --> 00:06:27.639 align:start position:0%
data and you're only interested in the
 

00:06:27.639 --> 00:06:33.230 align:start position:0%
data and you're only interested in the
latest<00:06:28.160><c> updates</c><00:06:29.080><c> you</c><00:06:29.560><c> not</c><00:06:29.759><c> want</c><00:06:30.400><c> to</c><00:06:31.400><c> um</c><00:06:32.400><c> index</c>

00:06:33.230 --> 00:06:33.240 align:start position:0%
latest updates you not want to um index
 

00:06:33.240 --> 00:06:37.870 align:start position:0%
latest updates you not want to um index
a<00:06:33.520><c> whole</c><00:06:34.639><c> history</c><00:06:35.639><c> but</c><00:06:35.840><c> you</c><00:06:36.039><c> only</c><00:06:36.720><c> uh</c><00:06:37.080><c> want</c>

00:06:37.870 --> 00:06:37.880 align:start position:0%
a whole history but you only uh want
 

00:06:37.880 --> 00:06:40.070 align:start position:0%
a whole history but you only uh want
let's<00:06:38.039><c> say</c><00:06:38.319><c> last</c><00:06:38.599><c> week</c><00:06:39.039><c> so</c><00:06:39.199><c> in</c><00:06:39.360><c> this</c><00:06:39.720><c> in</c><00:06:39.880><c> this</c>

00:06:40.070 --> 00:06:40.080 align:start position:0%
let's say last week so in this in this
 

00:06:40.080 --> 00:06:43.390 align:start position:0%
let's say last week so in this in this
case<00:06:40.319><c> you</c><00:06:40.479><c> can</c><00:06:41.000><c> uh</c><00:06:41.479><c> organize</c><00:06:42.199><c> your</c><00:06:42.960><c> um</c><00:06:43.240><c> your</c>

00:06:43.390 --> 00:06:43.400 align:start position:0%
case you can uh organize your um your
 

00:06:43.400 --> 00:06:48.110 align:start position:0%
case you can uh organize your um your
shards<00:06:44.160><c> by</c><00:06:44.919><c> by</c><00:06:45.080><c> the</c><00:06:45.520><c> date</c><00:06:46.520><c> and</c><00:06:47.120><c> uh</c><00:06:47.400><c> all</c><00:06:47.720><c> recent</c>

00:06:48.110 --> 00:06:48.120 align:start position:0%
shards by by the date and uh all recent
 

00:06:48.120 --> 00:06:51.230 align:start position:0%
shards by by the date and uh all recent
updates<00:06:48.520><c> will</c><00:06:48.759><c> go</c><00:06:49.039><c> into</c><00:06:49.319><c> one</c><00:06:49.840><c> sharts</c><00:06:50.840><c> uh</c><00:06:51.000><c> and</c>

00:06:51.230 --> 00:06:51.240 align:start position:0%
updates will go into one sharts uh and
 

00:06:51.240 --> 00:06:54.230 align:start position:0%
updates will go into one sharts uh and
once<00:06:52.000><c> uh</c><00:06:52.440><c> the</c><00:06:52.599><c> next</c><00:06:52.919><c> day</c><00:06:53.160><c> come</c><00:06:53.639><c> you</c><00:06:53.759><c> will</c><00:06:54.039><c> just</c>

00:06:54.230 --> 00:06:54.240 align:start position:0%
once uh the next day come you will just
 

00:06:54.240 --> 00:06:57.390 align:start position:0%
once uh the next day come you will just
deprecate<00:06:55.160><c> the</c><00:06:55.280><c> shart</c><00:06:55.720><c> which</c><00:06:55.879><c> is</c><00:06:56.319><c> outdated</c><00:06:57.280><c> it</c>

00:06:57.390 --> 00:06:57.400 align:start position:0%
deprecate the shart which is outdated it
 

00:06:57.400 --> 00:07:02.110 align:start position:0%
deprecate the shart which is outdated it
will<00:06:57.599><c> be</c><00:06:57.759><c> removed</c><00:06:58.280><c> without</c><00:06:58.759><c> any</c><00:06:59.879><c> um</c><00:07:01.120><c> latency</c>

00:07:02.110 --> 00:07:02.120 align:start position:0%
will be removed without any um latency
 

00:07:02.120 --> 00:07:03.909 align:start position:0%
will be removed without any um latency
because<00:07:02.440><c> you</c><00:07:02.560><c> would</c><00:07:02.759><c> need</c><00:07:03.360><c> you</c><00:07:03.520><c> would</c><00:07:03.800><c> you</c>

00:07:03.909 --> 00:07:03.919 align:start position:0%
because you would need you would you
 

00:07:03.919 --> 00:07:07.710 align:start position:0%
because you would need you would you
would<00:07:04.199><c> bypass</c><00:07:04.919><c> the</c><00:07:05.440><c> requirement</c><00:07:06.080><c> to</c><00:07:07.080><c> pinpoint</c>

00:07:07.710 --> 00:07:07.720 align:start position:0%
would bypass the requirement to pinpoint
 

00:07:07.720 --> 00:07:09.830 align:start position:0%
would bypass the requirement to pinpoint
exact<00:07:08.120><c> points</c><00:07:08.560><c> you</c><00:07:08.680><c> should</c><00:07:08.960><c> delete</c><00:07:09.560><c> and</c><00:07:09.720><c> it</c>

00:07:09.830 --> 00:07:09.840 align:start position:0%
exact points you should delete and it
 

00:07:09.840 --> 00:07:13.110 align:start position:0%
exact points you should delete and it
will<00:07:10.240><c> just</c><00:07:10.440><c> delete</c><00:07:10.800><c> the</c><00:07:11.039><c> whole</c><00:07:12.039><c> uh</c><00:07:12.160><c> shart</c><00:07:12.879><c> in</c>

00:07:13.110 --> 00:07:13.120 align:start position:0%
will just delete the whole uh shart in
 

00:07:13.120 --> 00:07:15.629 align:start position:0%
will just delete the whole uh shart in
one<00:07:13.599><c> one</c><00:07:13.800><c> shot</c><00:07:14.479><c> it's</c><00:07:14.759><c> especially</c><00:07:15.160><c> relevant</c>

00:07:15.629 --> 00:07:15.639 align:start position:0%
one one shot it's especially relevant
 

00:07:15.639 --> 00:07:18.990 align:start position:0%
one one shot it's especially relevant
for<00:07:15.919><c> social</c><00:07:16.280><c> media</c><00:07:17.039><c> platforms</c><00:07:18.039><c> uh</c><00:07:18.199><c> which</c>

00:07:18.990 --> 00:07:19.000 align:start position:0%
for social media platforms uh which
 

00:07:19.000 --> 00:07:22.869 align:start position:0%
for social media platforms uh which
deals<00:07:19.400><c> with</c><00:07:19.599><c> a</c><00:07:19.720><c> lot</c><00:07:19.919><c> of</c><00:07:20.280><c> traffic</c><00:07:21.280><c> um</c><00:07:22.160><c> and</c><00:07:22.560><c> you</c>

00:07:22.869 --> 00:07:22.879 align:start position:0%
deals with a lot of traffic um and you
 

00:07:22.879 --> 00:07:25.390 align:start position:0%
deals with a lot of traffic um and you
those<00:07:23.360><c> uh</c><00:07:23.759><c> companies</c><00:07:24.199><c> who</c><00:07:24.440><c> work</c><00:07:24.800><c> with</c><00:07:25.160><c> this</c>

00:07:25.390 --> 00:07:25.400 align:start position:0%
those uh companies who work with this
 

00:07:25.400 --> 00:07:28.430 align:start position:0%
those uh companies who work with this
type<00:07:25.599><c> of</c><00:07:25.800><c> data</c><00:07:26.479><c> yeah</c><00:07:26.639><c> so</c><00:07:26.919><c> then</c><00:07:27.360><c> the</c><00:07:27.639><c> last</c><00:07:28.039><c> use</c>

00:07:28.430 --> 00:07:28.440 align:start position:0%
type of data yeah so then the last use
 

00:07:28.440 --> 00:07:30.510 align:start position:0%
type of data yeah so then the last use
case<00:07:28.639><c> that</c><00:07:28.759><c> you</c><00:07:28.919><c> had</c><00:07:29.120><c> here</c>

00:07:30.510 --> 00:07:30.520 align:start position:0%
case that you had here
 

00:07:30.520 --> 00:07:33.390 align:start position:0%
case that you had here
or<00:07:30.759><c> the</c><00:07:31.000><c> last</c><00:07:31.560><c> um</c><00:07:32.080><c> piece</c><00:07:32.560><c> of</c><00:07:32.879><c> why</c><00:07:33.080><c> we</c><00:07:33.199><c> would</c>

00:07:33.390 --> 00:07:33.400 align:start position:0%
or the last um piece of why we would
 

00:07:33.400 --> 00:07:37.070 align:start position:0%
or the last um piece of why we would
Shard<00:07:34.039><c> I</c><00:07:34.240><c> guess</c><00:07:34.560><c> and</c><00:07:34.960><c> not</c><00:07:35.479><c> exactly</c><00:07:35.879><c> use</c><00:07:36.280><c> case</c>

00:07:37.070 --> 00:07:37.080 align:start position:0%
Shard I guess and not exactly use case
 

00:07:37.080 --> 00:07:39.909 align:start position:0%
Shard I guess and not exactly use case
is<00:07:37.720><c> being</c><00:07:38.000><c> able</c><00:07:38.199><c> to</c><00:07:38.479><c> control</c><00:07:39.479><c> have</c><00:07:39.639><c> more</c>

00:07:39.909 --> 00:07:39.919 align:start position:0%
is being able to control have more
 

00:07:39.919 --> 00:07:43.070 align:start position:0%
is being able to control have more
control<00:07:40.360><c> over</c><00:07:40.759><c> indexing</c><00:07:41.759><c> exactly</c><00:07:42.639><c> uh</c><00:07:42.919><c> yeah</c>

00:07:43.070 --> 00:07:43.080 align:start position:0%
control over indexing exactly uh yeah
 

00:07:43.080 --> 00:07:48.350 align:start position:0%
control over indexing exactly uh yeah
that's<00:07:43.479><c> a</c><00:07:44.520><c> um</c><00:07:45.520><c> key</c><00:07:45.800><c> component</c><00:07:46.560><c> of</c><00:07:47.440><c> of</c><00:07:47.639><c> the</c><00:07:48.159><c> uh</c>

00:07:48.350 --> 00:07:48.360 align:start position:0%
that's a um key component of of the uh
 

00:07:48.360 --> 00:07:51.710 align:start position:0%
that's a um key component of of the uh
separation<00:07:49.280><c> between</c><00:07:49.639><c> search</c><00:07:50.159><c> and</c><00:07:50.360><c> index</c><00:07:50.840><c> time</c>

00:07:51.710 --> 00:07:51.720 align:start position:0%
separation between search and index time
 

00:07:51.720 --> 00:07:54.710 align:start position:0%
separation between search and index time
uh<00:07:51.840><c> so</c><00:07:52.440><c> imagine</c><00:07:52.800><c> you</c><00:07:52.960><c> have</c><00:07:53.280><c> machine</c><00:07:54.280><c> which</c><00:07:54.479><c> can</c>

00:07:54.710 --> 00:07:54.720 align:start position:0%
uh so imagine you have machine which can
 

00:07:54.720 --> 00:07:58.710 align:start position:0%
uh so imagine you have machine which can
do<00:07:55.440><c> um</c><00:07:56.319><c> a</c><00:07:56.440><c> lot</c><00:07:56.639><c> of</c><00:07:56.879><c> indexing</c><00:07:57.520><c> it</c><00:07:57.680><c> is</c><00:07:58.280><c> it</c><00:07:58.440><c> have</c>

00:07:58.710 --> 00:07:58.720 align:start position:0%
do um a lot of indexing it is it have
 

00:07:58.720 --> 00:08:02.629 align:start position:0%
do um a lot of indexing it is it have
high<00:07:58.919><c> CPU</c><00:07:59.400><c> you</c><00:07:59.879><c> high</c><00:08:00.400><c> memory</c><00:08:01.400><c> uh</c><00:08:02.319><c> but</c><00:08:02.479><c> it's</c>

00:08:02.629 --> 00:08:02.639 align:start position:0%
high CPU you high memory uh but it's
 

00:08:02.639 --> 00:08:04.909 align:start position:0%
high CPU you high memory uh but it's
also<00:08:02.960><c> very</c><00:08:03.240><c> expensive</c><00:08:04.120><c> and</c><00:08:04.240><c> you</c><00:08:04.360><c> don't</c><00:08:04.680><c> need</c>

00:08:04.909 --> 00:08:04.919 align:start position:0%
also very expensive and you don't need
 

00:08:04.919 --> 00:08:09.390 align:start position:0%
also very expensive and you don't need
it<00:08:05.240><c> to</c><00:08:05.479><c> be</c><00:08:06.240><c> uh</c><00:08:06.520><c> engaged</c><00:08:07.120><c> all</c><00:08:07.360><c> the</c><00:08:07.639><c> time</c><00:08:08.639><c> um</c><00:08:09.039><c> so</c>

00:08:09.390 --> 00:08:09.400 align:start position:0%
it to be uh engaged all the time um so
 

00:08:09.400 --> 00:08:13.350 align:start position:0%
it to be uh engaged all the time um so
what<00:08:09.520><c> you</c><00:08:09.680><c> can</c><00:08:09.879><c> do</c><00:08:10.319><c> you</c><00:08:10.479><c> can</c><00:08:11.120><c> create</c><00:08:12.080><c> a</c><00:08:12.360><c> Shard</c>

00:08:13.350 --> 00:08:13.360 align:start position:0%
what you can do you can create a Shard
 

00:08:13.360 --> 00:08:17.550 align:start position:0%
what you can do you can create a Shard
especially<00:08:14.039><c> for</c><00:08:15.000><c> uh</c><00:08:15.159><c> this</c><00:08:15.639><c> machine</c><00:08:16.639><c> uh</c><00:08:16.960><c> create</c>

00:08:17.550 --> 00:08:17.560 align:start position:0%
especially for uh this machine uh create
 

00:08:17.560 --> 00:08:20.990 align:start position:0%
especially for uh this machine uh create
indexes<00:08:18.560><c> with</c><00:08:18.879><c> this</c><00:08:19.479><c> uh</c><00:08:20.039><c> large</c><00:08:20.440><c> machine</c><00:08:20.840><c> and</c>

00:08:20.990 --> 00:08:21.000 align:start position:0%
indexes with this uh large machine and
 

00:08:21.000 --> 00:08:24.270 align:start position:0%
indexes with this uh large machine and
then<00:08:21.240><c> transfer</c><00:08:22.120><c> the</c><00:08:22.319><c> shards</c><00:08:22.840><c> to</c><00:08:23.120><c> another</c><00:08:24.120><c> uh</c>

00:08:24.270 --> 00:08:24.280 align:start position:0%
then transfer the shards to another uh
 

00:08:24.280 --> 00:08:26.749 align:start position:0%
then transfer the shards to another uh
machine<00:08:24.680><c> which</c><00:08:24.840><c> is</c><00:08:25.319><c> designed</c><00:08:25.840><c> to</c><00:08:26.039><c> serve</c><00:08:26.479><c> data</c>

00:08:26.749 --> 00:08:26.759 align:start position:0%
machine which is designed to serve data
 

00:08:26.759 --> 00:08:30.749 align:start position:0%
machine which is designed to serve data
so<00:08:26.960><c> it</c><00:08:27.120><c> can</c><00:08:27.680><c> have</c><00:08:28.199><c> high</c><00:08:29.080><c> uh</c><00:08:29.479><c> dis</c><00:08:29.800><c> throughput</c>

00:08:30.749 --> 00:08:30.759 align:start position:0%
so it can have high uh dis throughput
 

00:08:30.759 --> 00:08:34.350 align:start position:0%
so it can have high uh dis throughput
but<00:08:31.000><c> maybe</c><00:08:31.280><c> not</c><00:08:31.560><c> as</c><00:08:31.840><c> B</c><00:08:32.120><c> CR</c><00:08:32.959><c> and</c><00:08:33.159><c> CPU</c>

00:08:34.350 --> 00:08:34.360 align:start position:0%
but maybe not as B CR and CPU
 

00:08:34.360 --> 00:08:39.230 align:start position:0%
but maybe not as B CR and CPU
configuration<00:08:35.360><c> uh</c><00:08:35.719><c> and</c><00:08:36.120><c> it's</c><00:08:36.320><c> also</c><00:08:37.080><c> um</c><00:08:37.719><c> now</c><00:08:38.719><c> um</c>

00:08:39.230 --> 00:08:39.240 align:start position:0%
configuration uh and it's also um now um
 

00:08:39.240 --> 00:08:42.350 align:start position:0%
configuration uh and it's also um now um
available<00:08:39.880><c> because</c><00:08:40.159><c> we</c><00:08:40.839><c> implemented</c>

00:08:42.350 --> 00:08:42.360 align:start position:0%
available because we implemented
 

00:08:42.360 --> 00:08:45.350 align:start position:0%
available because we implemented
um<00:08:43.360><c> snapshot</c><00:08:43.959><c> based</c><00:08:44.240><c> sharp</c><00:08:44.560><c> transfer</c><00:08:45.000><c> in</c><00:08:45.120><c> our</c>

00:08:45.350 --> 00:08:45.360 align:start position:0%
um snapshot based sharp transfer in our
 

00:08:45.360 --> 00:08:48.590 align:start position:0%
um snapshot based sharp transfer in our
recent<00:08:45.680><c> version</c><00:08:46.160><c> so</c><00:08:46.360><c> it's</c><00:08:46.800><c> uh</c><00:08:47.160><c> not</c><00:08:47.680><c> directly</c>

00:08:48.590 --> 00:08:48.600 align:start position:0%
recent version so it's uh not directly
 

00:08:48.600 --> 00:08:51.550 align:start position:0%
recent version so it's uh not directly
uh<00:08:49.279><c> due</c><00:08:49.519><c> to</c><00:08:50.160><c> custom</c><00:08:50.440><c> shutting</c><00:08:50.920><c> but</c><00:08:51.040><c> it's</c><00:08:51.279><c> also</c>

00:08:51.550 --> 00:08:51.560 align:start position:0%
uh due to custom shutting but it's also
 

00:08:51.560 --> 00:08:54.470 align:start position:0%
uh due to custom shutting but it's also
the<00:08:51.720><c> feature</c><00:08:52.160><c> we</c><00:08:52.920><c> introduced</c><00:08:53.839><c> in</c><00:08:54.160><c> in</c><00:08:54.279><c> the</c>

00:08:54.470 --> 00:08:54.480 align:start position:0%
the feature we introduced in in the
 

00:08:54.480 --> 00:08:57.430 align:start position:0%
the feature we introduced in in the
latest<00:08:55.160><c> release</c><00:08:56.160><c> and</c><00:08:56.320><c> what</c><00:08:56.440><c> is</c><00:08:56.640><c> that</c><00:08:56.959><c> exactly</c>

00:08:57.430 --> 00:08:57.440 align:start position:0%
latest release and what is that exactly
 

00:08:57.440 --> 00:09:00.269 align:start position:0%
latest release and what is that exactly
the<00:08:57.600><c> snapshot</c><00:08:58.200><c> based</c><00:08:59.160><c> snapshot</c><00:08:59.600><c> based</c><00:08:59.880><c> sh</c>

00:09:00.269 --> 00:09:00.279 align:start position:0%
the snapshot based snapshot based sh
 

00:09:00.279 --> 00:09:02.630 align:start position:0%
the snapshot based snapshot based sh
transferring<00:09:00.800><c> so</c><00:09:01.440><c> previously</c><00:09:02.000><c> we</c><00:09:02.360><c> just</c>

00:09:02.630 --> 00:09:02.640 align:start position:0%
transferring so previously we just
 

00:09:02.640 --> 00:09:04.590 align:start position:0%
transferring so previously we just
transferred<00:09:03.200><c> data</c><00:09:03.519><c> between</c><00:09:03.880><c> machines</c><00:09:04.360><c> within</c>

00:09:04.590 --> 00:09:04.600 align:start position:0%
transferred data between machines within
 

00:09:04.600 --> 00:09:07.230 align:start position:0%
transferred data between machines within
the<00:09:04.720><c> clust</c><00:09:05.440><c> now</c><00:09:05.640><c> we</c><00:09:05.760><c> can</c><00:09:06.000><c> transfer</c><00:09:06.480><c> data</c><00:09:06.959><c> and</c>

00:09:07.230 --> 00:09:07.240 align:start position:0%
the clust now we can transfer data and
 

00:09:07.240 --> 00:09:12.030 align:start position:0%
the clust now we can transfer data and
the<00:09:07.399><c> index</c><00:09:08.079><c> you</c><00:09:08.800><c> build</c><00:09:09.800><c> oh</c><00:09:10.079><c> nice</c><00:09:10.839><c> okay</c><00:09:11.519><c> so</c><00:09:11.800><c> this</c>

00:09:12.030 --> 00:09:12.040 align:start position:0%
the index you build oh nice okay so this
 

00:09:12.040 --> 00:09:15.829 align:start position:0%
the index you build oh nice okay so this
feels<00:09:12.800><c> like</c><00:09:13.800><c> it's</c><00:09:14.200><c> perfect</c><00:09:14.640><c> for</c><00:09:15.160><c> use</c><00:09:15.480><c> cases</c>

00:09:15.829 --> 00:09:15.839 align:start position:0%
feels like it's perfect for use cases
 

00:09:15.839 --> 00:09:19.590 align:start position:0%
feels like it's perfect for use cases
with<00:09:16.000><c> a</c><00:09:16.279><c> lot</c><00:09:16.480><c> of</c><00:09:17.120><c> data</c><00:09:18.120><c> what</c><00:09:18.680><c> scale</c><00:09:19.200><c> do</c><00:09:19.320><c> you</c>

00:09:19.590 --> 00:09:19.600 align:start position:0%
with a lot of data what scale do you
 

00:09:19.600 --> 00:09:22.430 align:start position:0%
with a lot of data what scale do you
recommend<00:09:20.480><c> us</c><00:09:21.160><c> using</c><00:09:21.600><c> the</c><00:09:21.720><c> sharding</c><00:09:22.320><c> as</c>

00:09:22.430 --> 00:09:22.440 align:start position:0%
recommend us using the sharding as
 

00:09:22.440 --> 00:09:25.509 align:start position:0%
recommend us using the sharding as
opposed<00:09:22.800><c> to</c><00:09:23.240><c> just</c><00:09:24.240><c> the</c><00:09:24.800><c> good</c><00:09:24.959><c> old</c><00:09:25.200><c> plain</c>

00:09:25.509 --> 00:09:25.519 align:start position:0%
opposed to just the good old plain
 

00:09:25.519 --> 00:09:28.509 align:start position:0%
opposed to just the good old plain
vanilla<00:09:26.279><c> version</c><00:09:26.600><c> of</c><00:09:26.760><c> it</c><00:09:26.959><c> yeah</c><00:09:27.760><c> as</c><00:09:27.880><c> soon</c><00:09:28.360><c> as</c>

00:09:28.509 --> 00:09:28.519 align:start position:0%
vanilla version of it yeah as soon as
 

00:09:28.519 --> 00:09:31.230 align:start position:0%
vanilla version of it yeah as soon as
you<00:09:28.680><c> see</c><00:09:29.480><c> that</c><00:09:29.680><c> your</c><00:09:30.000><c> data</c><00:09:30.320><c> may</c><00:09:30.560><c> not</c><00:09:30.760><c> feed</c><00:09:31.079><c> the</c>

00:09:31.230 --> 00:09:31.240 align:start position:0%
you see that your data may not feed the
 

00:09:31.240 --> 00:09:34.150 align:start position:0%
you see that your data may not feed the
single<00:09:31.600><c> machine</c><00:09:32.399><c> is</c><00:09:32.600><c> already</c><00:09:33.000><c> relevant</c><00:09:33.959><c> it</c>

00:09:34.150 --> 00:09:34.160 align:start position:0%
single machine is already relevant it
 

00:09:34.160 --> 00:09:37.230 align:start position:0%
single machine is already relevant it
gives<00:09:34.399><c> you</c><00:09:34.839><c> a</c><00:09:35.000><c> precise</c><00:09:35.480><c> control</c><00:09:35.959><c> so</c><00:09:36.720><c> you</c><00:09:36.839><c> don't</c>

00:09:37.230 --> 00:09:37.240 align:start position:0%
gives you a precise control so you don't
 

00:09:37.240 --> 00:09:39.389 align:start position:0%
gives you a precise control so you don't
need<00:09:37.440><c> to</c><00:09:37.760><c> actually</c><00:09:38.160><c> have</c><00:09:38.279><c> a</c><00:09:38.480><c> gazillion</c><00:09:39.000><c> scale</c>

00:09:39.389 --> 00:09:39.399 align:start position:0%
need to actually have a gazillion scale
 

00:09:39.399 --> 00:09:41.710 align:start position:0%
need to actually have a gazillion scale
deployment<00:09:39.920><c> but</c><00:09:40.600><c> uh</c><00:09:40.800><c> if</c><00:09:40.880><c> you</c><00:09:41.079><c> do</c><00:09:41.360><c> have</c><00:09:41.560><c> a</c>

00:09:41.710 --> 00:09:41.720 align:start position:0%
deployment but uh if you do have a
 

00:09:41.720 --> 00:09:43.790 align:start position:0%
deployment but uh if you do have a
gazillion<00:09:42.200><c> scale</c><00:09:42.600><c> deployment</c><00:09:43.120><c> you</c><00:09:43.440><c> kind</c><00:09:43.600><c> of</c>

00:09:43.790 --> 00:09:43.800 align:start position:0%
gazillion scale deployment you kind of
 

00:09:43.800 --> 00:09:47.710 align:start position:0%
gazillion scale deployment you kind of
have<00:09:43.959><c> no</c><00:09:44.200><c> choice</c><00:09:44.839><c> but</c><00:09:45.160><c> use</c><00:09:45.720><c> uh</c><00:09:45.959><c> this</c><00:09:46.440><c> uh</c><00:09:47.279><c> um</c>

00:09:47.710 --> 00:09:47.720 align:start position:0%
have no choice but use uh this uh um
 

00:09:47.720 --> 00:09:50.630 align:start position:0%
have no choice but use uh this uh um
precise<00:09:48.440><c> management</c><00:09:49.440><c> of</c><00:09:49.600><c> your</c>

00:09:50.630 --> 00:09:50.640 align:start position:0%
precise management of your
 

00:09:50.640 --> 00:09:54.350 align:start position:0%
precise management of your
data<00:09:51.640><c> but</c><00:09:51.839><c> it's</c><00:09:52.079><c> still</c><00:09:52.360><c> useful</c><00:09:53.320><c> even</c><00:09:53.680><c> for</c>

00:09:54.350 --> 00:09:54.360 align:start position:0%
data but it's still useful even for
 

00:09:54.360 --> 00:09:56.509 align:start position:0%
data but it's still useful even for
smaller

00:09:56.509 --> 00:09:56.519 align:start position:0%
smaller
 

00:09:56.519 --> 00:09:58.790 align:start position:0%
smaller
deployments<00:09:57.519><c> yeah</c><00:09:58.040><c> that</c><00:09:58.200><c> makes</c><00:09:58.440><c> sense</c><00:09:58.680><c> and</c>

00:09:58.790 --> 00:09:58.800 align:start position:0%
deployments yeah that makes sense and
 

00:09:58.800 --> 00:10:01.870 align:start position:0%
deployments yeah that makes sense and
does<00:09:58.920><c> it</c><00:09:59.200><c> matter</c><00:09:59.560><c> the</c><00:10:00.040><c> size</c><00:10:00.279><c> of</c><00:10:00.399><c> the</c><00:10:00.880><c> vectors</c>

00:10:01.870 --> 00:10:01.880 align:start position:0%
does it matter the size of the vectors
 

00:10:01.880 --> 00:10:06.269 align:start position:0%
does it matter the size of the vectors
no<00:10:02.279><c> no</c><00:10:03.200><c> all</c><00:10:03.440><c> vectors</c><00:10:04.240><c> can</c><00:10:04.399><c> be</c><00:10:04.519><c> used</c><00:10:04.760><c> to</c><00:10:05.279><c> this</c>

00:10:06.269 --> 00:10:06.279 align:start position:0%
no no all vectors can be used to this
 

00:10:06.279 --> 00:10:08.550 align:start position:0%
no no all vectors can be used to this
cool<00:10:07.000><c> all</c><00:10:07.240><c> right</c>

00:10:08.550 --> 00:10:08.560 align:start position:0%
cool all right
 

00:10:08.560 --> 00:10:11.590 align:start position:0%
cool all right
and<00:10:09.560><c> I</c><00:10:09.720><c> think</c><00:10:10.040><c> that's</c><00:10:10.279><c> about</c><00:10:10.640><c> it</c><00:10:11.160><c> as</c><00:10:11.240><c> far</c><00:10:11.399><c> as</c>

00:10:11.590 --> 00:10:11.600 align:start position:0%
and I think that's about it as far as
 

00:10:11.600 --> 00:10:13.710 align:start position:0%
and I think that's about it as far as
custom<00:10:11.920><c> sharding</c><00:10:12.519><c> is</c><00:10:12.640><c> there</c><00:10:12.839><c> anything</c><00:10:13.240><c> else</c>

00:10:13.710 --> 00:10:13.720 align:start position:0%
custom sharding is there anything else
 

00:10:13.720 --> 00:10:16.910 align:start position:0%
custom sharding is there anything else
that<00:10:14.000><c> you</c><00:10:14.839><c> want</c><00:10:15.200><c> to</c><00:10:15.399><c> go</c><00:10:15.560><c> into</c><00:10:16.399><c> oh</c><00:10:16.600><c> I</c><00:10:16.720><c> think</c>

00:10:16.910 --> 00:10:16.920 align:start position:0%
that you want to go into oh I think
 

00:10:16.920 --> 00:10:19.630 align:start position:0%
that you want to go into oh I think
that's<00:10:17.279><c> fine</c><00:10:17.839><c> uh</c><00:10:18.040><c> for</c><00:10:18.240><c> for</c><00:10:18.480><c> now</c><00:10:19.079><c> how</c><00:10:19.200><c> do</c><00:10:19.320><c> we</c><00:10:19.480><c> get</c>

00:10:19.630 --> 00:10:19.640 align:start position:0%
that's fine uh for for now how do we get
 

00:10:19.640 --> 00:10:22.389 align:start position:0%
that's fine uh for for now how do we get
started<00:10:20.640><c> uh</c><00:10:20.839><c> yeah</c><00:10:21.200><c> just</c><00:10:21.480><c> create</c><00:10:21.760><c> a</c><00:10:21.920><c> collection</c>

00:10:22.389 --> 00:10:22.399 align:start position:0%
started uh yeah just create a collection
 

00:10:22.399 --> 00:10:23.829 align:start position:0%
started uh yeah just create a collection
with<00:10:22.600><c> sharting</c>

00:10:23.829 --> 00:10:23.839 align:start position:0%
with sharting
 

00:10:23.839 --> 00:10:26.430 align:start position:0%
with sharting
method<00:10:24.839><c> uh</c><00:10:25.079><c> explicitly</c><00:10:25.839><c> and</c><00:10:26.120><c> yeah</c><00:10:26.240><c> you're</c>

00:10:26.430 --> 00:10:26.440 align:start position:0%
method uh explicitly and yeah you're
 

00:10:26.440 --> 00:10:28.750 align:start position:0%
method uh explicitly and yeah you're
good<00:10:26.560><c> to</c><00:10:26.720><c> go</c><00:10:27.440><c> and</c><00:10:27.640><c> of</c><00:10:27.760><c> course</c><00:10:28.040><c> upgrade</c><00:10:28.440><c> to</c>

00:10:28.750 --> 00:10:28.760 align:start position:0%
good to go and of course upgrade to
 

00:10:28.760 --> 00:10:29.829 align:start position:0%
good to go and of course upgrade to
version

00:10:29.829 --> 00:10:29.839 align:start position:0%
version
 

00:10:29.839 --> 00:10:32.350 align:start position:0%
version
1.7<00:10:30.839><c> yeah</c><00:10:31.240><c> hopefully</c><00:10:31.560><c> is</c><00:10:31.680><c> a</c><00:10:31.839><c> given</c><00:10:32.120><c> but</c><00:10:32.240><c> you</c>

00:10:32.350 --> 00:10:32.360 align:start position:0%
1.7 yeah hopefully is a given but you
 

00:10:32.360 --> 00:10:33.990 align:start position:0%
1.7 yeah hopefully is a given but you
never<00:10:32.519><c> know</c><00:10:32.720><c> you</c><00:10:32.800><c> can</c><00:10:32.920><c> never</c><00:10:33.120><c> be</c><00:10:33.279><c> too</c><00:10:33.560><c> explicit</c>

00:10:33.990 --> 00:10:34.000 align:start position:0%
never know you can never be too explicit
 

00:10:34.000 --> 00:10:36.269 align:start position:0%
never know you can never be too explicit
these<00:10:34.240><c> days</c><00:10:35.000><c> right</c><00:10:35.160><c> on</c><00:10:35.519><c> well</c><00:10:35.760><c> Andre</c><00:10:36.160><c> I</c>

00:10:36.269 --> 00:10:36.279 align:start position:0%
these days right on well Andre I
 

00:10:36.279 --> 00:10:38.829 align:start position:0%
these days right on well Andre I
appreciate<00:10:36.880><c> the</c><00:10:37.160><c> time</c><00:10:37.680><c> and</c><00:10:37.880><c> you</c><00:10:38.240><c> explaining</c>

00:10:38.829 --> 00:10:38.839 align:start position:0%
appreciate the time and you explaining
 

00:10:38.839 --> 00:10:40.350 align:start position:0%
appreciate the time and you explaining
this<00:10:39.000><c> new</c><00:10:39.160><c> feature</c><00:10:39.519><c> it</c><00:10:39.639><c> sounds</c><00:10:39.880><c> like</c><00:10:40.040><c> a</c><00:10:40.120><c> lot</c><00:10:40.240><c> of</c>

00:10:40.350 --> 00:10:40.360 align:start position:0%
this new feature it sounds like a lot of
 

00:10:40.360 --> 00:10:43.190 align:start position:0%
this new feature it sounds like a lot of
fun<00:10:40.760><c> if</c><00:10:40.920><c> anybody</c><00:10:41.880><c> decides</c><00:10:42.360><c> to</c><00:10:42.560><c> use</c><00:10:42.760><c> it</c><00:10:43.000><c> and</c>

00:10:43.190 --> 00:10:43.200 align:start position:0%
fun if anybody decides to use it and
 

00:10:43.200 --> 00:10:45.509 align:start position:0%
fun if anybody decides to use it and
write<00:10:43.519><c> about</c><00:10:43.760><c> it</c><00:10:44.519><c> we</c><00:10:44.680><c> will</c><00:10:44.880><c> send</c><00:10:45.200><c> you</c><00:10:45.320><c> some</c>

00:10:45.509 --> 00:10:45.519 align:start position:0%
write about it we will send you some
 

00:10:45.519 --> 00:10:49.110 align:start position:0%
write about it we will send you some
shirts<00:10:46.279><c> we</c><00:10:46.560><c> love</c><00:10:47.360><c> hearing</c><00:10:48.040><c> about</c><00:10:48.760><c> what</c><00:10:48.959><c> the</c>

00:10:49.110 --> 00:10:49.120 align:start position:0%
shirts we love hearing about what the
 

00:10:49.120 --> 00:10:51.790 align:start position:0%
shirts we love hearing about what the
community<00:10:49.560><c> is</c><00:10:49.760><c> doing</c><00:10:50.279><c> so</c><00:10:50.800><c> hit</c><00:10:50.959><c> us</c><00:10:51.160><c> up</c><00:10:51.480><c> let</c><00:10:51.600><c> us</c>

00:10:51.790 --> 00:10:51.800 align:start position:0%
community is doing so hit us up let us
 

00:10:51.800 --> 00:10:55.230 align:start position:0%
community is doing so hit us up let us
know<00:10:52.279><c> what</c><00:10:52.399><c> you</c><00:10:52.560><c> thought</c><00:10:52.760><c> of</c><00:10:52.880><c> it</c><00:10:54.079><c> and</c><00:10:55.079><c> who</c>

00:10:55.230 --> 00:10:55.240 align:start position:0%
know what you thought of it and who
 

00:10:55.240 --> 00:10:57.990 align:start position:0%
know what you thought of it and who
knows<00:10:55.639><c> maybe</c><00:10:56.120><c> submit</c><00:10:56.399><c> a</c><00:10:56.560><c> PR</c><00:10:57.040><c> so</c><00:10:57.360><c> Andre</c><00:10:57.839><c> can</c>

00:10:57.990 --> 00:10:58.000 align:start position:0%
knows maybe submit a PR so Andre can
 

00:10:58.000 --> 00:10:59.750 align:start position:0%
knows maybe submit a PR so Andre can
have<00:10:58.160><c> something</c><00:10:58.399><c> to</c><00:10:58.519><c> do</c><00:10:58.680><c> at</c><00:10:58.839><c> night</c>

00:10:59.750 --> 00:10:59.760 align:start position:0%
have something to do at night
 

00:10:59.760 --> 00:11:01.230 align:start position:0%
have something to do at night
I'll<00:10:59.959><c> see</c><00:11:00.160><c> you</c><00:11:00.279><c> all</c>

00:11:01.230 --> 00:11:01.240 align:start position:0%
I'll see you all
 

00:11:01.240 --> 00:11:06.949 align:start position:0%
I'll see you all
later<00:11:02.240><c> thank</c>

00:11:06.949 --> 00:11:06.959 align:start position:0%
 
 

00:11:06.959 --> 00:11:09.959 align:start position:0%
 
you

