WEBVTT
Kind: captions
Language: en

00:00:00.179 --> 00:00:02.210 align:start position:0%
 
let's<00:00:00.659><c> begin</c><00:00:00.900><c> understanding</c><00:00:01.500><c> selection</c><00:00:01.860><c> sort</c>

00:00:02.210 --> 00:00:02.220 align:start position:0%
let's begin understanding selection sort
 

00:00:02.220 --> 00:00:04.789 align:start position:0%
let's begin understanding selection sort
the<00:00:02.879><c> idea</c><00:00:03.120><c> is</c><00:00:03.419><c> to</c><00:00:03.600><c> move</c><00:00:03.780><c> the</c><00:00:04.200><c> minimum</c><00:00:04.500><c> element</c>

00:00:04.789 --> 00:00:04.799 align:start position:0%
the idea is to move the minimum element
 

00:00:04.799 --> 00:00:07.249 align:start position:0%
the idea is to move the minimum element
through<00:00:05.460><c> each</c><00:00:05.759><c> iteration</c><00:00:06.180><c> of</c><00:00:06.420><c> this</c><00:00:06.540><c> array</c><00:00:06.839><c> to</c>

00:00:07.249 --> 00:00:07.259 align:start position:0%
through each iteration of this array to
 

00:00:07.259 --> 00:00:09.470 align:start position:0%
through each iteration of this array to
the<00:00:07.379><c> beginning</c><00:00:07.560><c> or</c><00:00:08.040><c> left</c><00:00:08.280><c> side</c><00:00:08.519><c> of</c><00:00:08.940><c> this</c><00:00:09.179><c> array</c>

00:00:09.470 --> 00:00:09.480 align:start position:0%
the beginning or left side of this array
 

00:00:09.480 --> 00:00:11.030 align:start position:0%
the beginning or left side of this array
we're<00:00:09.840><c> going</c><00:00:09.960><c> to</c><00:00:10.019><c> have</c><00:00:10.139><c> two</c><00:00:10.200><c> pointers</c><00:00:10.559><c> one</c><00:00:10.860><c> for</c>

00:00:11.030 --> 00:00:11.040 align:start position:0%
we're going to have two pointers one for
 

00:00:11.040 --> 00:00:12.709 align:start position:0%
we're going to have two pointers one for
the<00:00:11.219><c> current</c><00:00:11.340><c> position</c><00:00:11.580><c> in</c><00:00:11.880><c> the</c><00:00:12.000><c> array</c><00:00:12.300><c> and</c>

00:00:12.709 --> 00:00:12.719 align:start position:0%
the current position in the array and
 

00:00:12.719 --> 00:00:14.509 align:start position:0%
the current position in the array and
another<00:00:12.900><c> for</c><00:00:13.259><c> the</c><00:00:13.559><c> current</c><00:00:13.799><c> minimum</c><00:00:14.340><c> element</c>

00:00:14.509 --> 00:00:14.519 align:start position:0%
another for the current minimum element
 

00:00:14.519 --> 00:00:17.390 align:start position:0%
another for the current minimum element
in<00:00:15.179><c> this</c><00:00:15.480><c> iteration</c><00:00:15.960><c> we're</c><00:00:16.500><c> going</c><00:00:16.680><c> to</c><00:00:16.740><c> ask</c><00:00:16.920><c> is</c>

00:00:17.390 --> 00:00:17.400 align:start position:0%
in this iteration we're going to ask is
 

00:00:17.400 --> 00:00:20.990 align:start position:0%
in this iteration we're going to ask is
7<00:00:17.760><c> less</c><00:00:18.359><c> than</c><00:00:18.539><c> 23</c><00:00:19.020><c> it</c><00:00:19.560><c> is</c><00:00:19.740><c> we</c><00:00:20.100><c> move</c><00:00:20.400><c> the</c><00:00:20.760><c> minimum</c>

00:00:20.990 --> 00:00:21.000 align:start position:0%
7 less than 23 it is we move the minimum
 

00:00:21.000 --> 00:00:24.349 align:start position:0%
7 less than 23 it is we move the minimum
element<00:00:21.180><c> pointer</c><00:00:21.779><c> to</c><00:00:22.500><c> that</c><00:00:23.100><c> values</c><00:00:23.580><c> index</c><00:00:23.939><c> and</c>

00:00:24.349 --> 00:00:24.359 align:start position:0%
element pointer to that values index and
 

00:00:24.359 --> 00:00:25.730 align:start position:0%
element pointer to that values index and
now<00:00:24.600><c> the</c><00:00:24.720><c> minimum</c><00:00:24.960><c> element</c><00:00:25.080><c> holds</c><00:00:25.560><c> the</c><00:00:25.619><c> value</c>

00:00:25.730 --> 00:00:25.740 align:start position:0%
now the minimum element holds the value
 

00:00:25.740 --> 00:00:27.890 align:start position:0%
now the minimum element holds the value
of<00:00:25.980><c> 7.</c><00:00:26.400><c> we</c><00:00:26.880><c> move</c><00:00:27.060><c> the</c><00:00:27.240><c> current</c><00:00:27.420><c> position</c><00:00:27.599><c> over</c>

00:00:27.890 --> 00:00:27.900 align:start position:0%
of 7. we move the current position over
 

00:00:27.900 --> 00:00:29.450 align:start position:0%
of 7. we move the current position over
to<00:00:28.080><c> the</c><00:00:28.199><c> next</c><00:00:28.380><c> index</c>

00:00:29.450 --> 00:00:29.460 align:start position:0%
to the next index
 

00:00:29.460 --> 00:00:33.530 align:start position:0%
to the next index
and<00:00:29.880><c> we</c><00:00:30.060><c> ask</c><00:00:30.240><c> is</c><00:00:30.539><c> 4</c><00:00:31.019><c> less</c><00:00:31.500><c> than</c><00:00:31.740><c> 7</c><00:00:32.160><c> it</c><00:00:32.940><c> is</c><00:00:33.120><c> so</c><00:00:33.360><c> we</c>

00:00:33.530 --> 00:00:33.540 align:start position:0%
and we ask is 4 less than 7 it is so we
 

00:00:33.540 --> 00:00:35.750 align:start position:0%
and we ask is 4 less than 7 it is so we
move<00:00:33.660><c> the</c><00:00:33.899><c> minimum</c><00:00:34.200><c> element</c><00:00:34.380><c> pointer</c><00:00:34.920><c> over</c><00:00:35.280><c> to</c>

00:00:35.750 --> 00:00:35.760 align:start position:0%
move the minimum element pointer over to
 

00:00:35.760 --> 00:00:37.850 align:start position:0%
move the minimum element pointer over to
that<00:00:36.059><c> index</c><00:00:36.480><c> we</c><00:00:37.079><c> move</c><00:00:37.260><c> the</c><00:00:37.440><c> current</c><00:00:37.620><c> position</c>

00:00:37.850 --> 00:00:37.860 align:start position:0%
that index we move the current position
 

00:00:37.860 --> 00:00:40.790 align:start position:0%
that index we move the current position
to<00:00:38.160><c> the</c><00:00:38.340><c> next</c><00:00:38.520><c> array</c><00:00:38.940><c> index</c><00:00:39.780><c> we</c><00:00:40.200><c> ask</c><00:00:40.379><c> is</c><00:00:40.620><c> 12</c>

00:00:40.790 --> 00:00:40.800 align:start position:0%
to the next array index we ask is 12
 

00:00:40.800 --> 00:00:43.010 align:start position:0%
to the next array index we ask is 12
less<00:00:41.100><c> than</c><00:00:41.280><c> 4</c><00:00:41.460><c> it</c><00:00:41.879><c> is</c><00:00:42.000><c> not</c><00:00:42.180><c> so</c><00:00:42.660><c> the</c><00:00:42.840><c> current</c>

00:00:43.010 --> 00:00:43.020 align:start position:0%
less than 4 it is not so the current
 

00:00:43.020 --> 00:00:44.869 align:start position:0%
less than 4 it is not so the current
minimum<00:00:43.500><c> element</c><00:00:43.680><c> in</c><00:00:43.980><c> this</c><00:00:44.160><c> iteration</c><00:00:44.520><c> is</c>

00:00:44.869 --> 00:00:44.879 align:start position:0%
minimum element in this iteration is
 

00:00:44.879 --> 00:00:47.090 align:start position:0%
minimum element in this iteration is
still<00:00:45.120><c> four</c><00:00:45.480><c> we</c><00:00:46.260><c> then</c><00:00:46.440><c> move</c><00:00:46.620><c> the</c><00:00:46.920><c> current</c>

00:00:47.090 --> 00:00:47.100 align:start position:0%
still four we then move the current
 

00:00:47.100 --> 00:00:49.670 align:start position:0%
still four we then move the current
position<00:00:47.340><c> over</c><00:00:47.640><c> to</c><00:00:47.879><c> the</c><00:00:48.059><c> next</c><00:00:48.180><c> array</c><00:00:48.660><c> index</c>

00:00:49.670 --> 00:00:49.680 align:start position:0%
position over to the next array index
 

00:00:49.680 --> 00:00:53.750 align:start position:0%
position over to the next array index
now<00:00:50.160><c> we</c><00:00:50.340><c> ask</c><00:00:50.520><c> is</c><00:00:50.820><c> 3</c><00:00:51.180><c> less</c><00:00:51.660><c> than</c><00:00:51.840><c> four</c><00:00:52.140><c> it</c><00:00:52.980><c> is</c><00:00:53.160><c> we</c>

00:00:53.750 --> 00:00:53.760 align:start position:0%
now we ask is 3 less than four it is we
 

00:00:53.760 --> 00:00:55.850 align:start position:0%
now we ask is 3 less than four it is we
then<00:00:53.940><c> move</c><00:00:54.120><c> the</c><00:00:54.420><c> current</c><00:00:54.600><c> minimum</c><00:00:55.020><c> element</c><00:00:55.199><c> to</c>

00:00:55.850 --> 00:00:55.860 align:start position:0%
then move the current minimum element to
 

00:00:55.860 --> 00:00:58.430 align:start position:0%
then move the current minimum element to
that<00:00:56.219><c> array</c><00:00:56.579><c> index</c><00:00:57.059><c> now</c><00:00:57.780><c> that</c><00:00:57.960><c> we're</c><00:00:58.079><c> at</c><00:00:58.320><c> the</c>

00:00:58.430 --> 00:00:58.440 align:start position:0%
that array index now that we're at the
 

00:00:58.440 --> 00:01:00.889 align:start position:0%
that array index now that we're at the
end<00:00:58.559><c> of</c><00:00:58.739><c> the</c><00:00:58.800><c> array</c><00:00:59.100><c> the</c><00:00:59.640><c> minimum</c><00:00:59.940><c> element</c><00:01:00.180><c> is</c>

00:01:00.889 --> 00:01:00.899 align:start position:0%
end of the array the minimum element is
 

00:01:00.899 --> 00:01:03.049 align:start position:0%
end of the array the minimum element is
the<00:01:01.079><c> value</c><00:01:01.199><c> 3</c><00:01:01.500><c> at</c><00:01:01.739><c> index</c><00:01:02.039><c> 4.</c><00:01:02.520><c> so</c><00:01:02.820><c> we're</c><00:01:02.940><c> going</c>

00:01:03.049 --> 00:01:03.059 align:start position:0%
the value 3 at index 4. so we're going
 

00:01:03.059 --> 00:01:04.369 align:start position:0%
the value 3 at index 4. so we're going
to<00:01:03.120><c> swap</c><00:01:03.359><c> that</c><00:01:03.539><c> with</c><00:01:03.780><c> the</c><00:01:03.960><c> next</c><00:01:04.140><c> available</c>

00:01:04.369 --> 00:01:04.379 align:start position:0%
to swap that with the next available
 

00:01:04.379 --> 00:01:06.170 align:start position:0%
to swap that with the next available
index<00:01:04.979><c> starting</c><00:01:05.339><c> at</c><00:01:05.580><c> the</c><00:01:05.700><c> beginning</c><00:01:05.760><c> of</c><00:01:06.060><c> the</c>

00:01:06.170 --> 00:01:06.180 align:start position:0%
index starting at the beginning of the
 

00:01:06.180 --> 00:01:08.570 align:start position:0%
index starting at the beginning of the
array<00:01:06.479><c> this</c><00:01:07.260><c> means</c><00:01:07.439><c> we</c><00:01:07.619><c> need</c><00:01:07.740><c> to</c><00:01:07.860><c> swap</c><00:01:08.100><c> 3</c><00:01:08.400><c> with</c>

00:01:08.570 --> 00:01:08.580 align:start position:0%
array this means we need to swap 3 with
 

00:01:08.580 --> 00:01:10.130 align:start position:0%
array this means we need to swap 3 with
23

00:01:10.130 --> 00:01:10.140 align:start position:0%
23
 

00:01:10.140 --> 00:01:12.770 align:start position:0%
23
and<00:01:10.619><c> now</c><00:01:10.740><c> we</c><00:01:10.920><c> know</c><00:01:11.100><c> three</c><00:01:11.460><c> is</c><00:01:12.000><c> at</c><00:01:12.299><c> the</c><00:01:12.540><c> correct</c>

00:01:12.770 --> 00:01:12.780 align:start position:0%
and now we know three is at the correct
 

00:01:12.780 --> 00:01:15.469 align:start position:0%
and now we know three is at the correct
position<00:01:13.080><c> in</c><00:01:13.979><c> this</c><00:01:14.100><c> array</c><00:01:14.460><c> now</c><00:01:15.060><c> we</c><00:01:15.180><c> get</c><00:01:15.360><c> ready</c>

00:01:15.469 --> 00:01:15.479 align:start position:0%
position in this array now we get ready
 

00:01:15.479 --> 00:01:17.390 align:start position:0%
position in this array now we get ready
for<00:01:15.659><c> the</c><00:01:15.780><c> next</c><00:01:15.900><c> iteration</c><00:01:16.320><c> of</c><00:01:16.560><c> this</c><00:01:16.680><c> array</c><00:01:16.979><c> we</c>

00:01:17.390 --> 00:01:17.400 align:start position:0%
for the next iteration of this array we
 

00:01:17.400 --> 00:01:19.609 align:start position:0%
for the next iteration of this array we
move<00:01:17.580><c> the</c><00:01:17.880><c> current</c><00:01:18.119><c> positions</c><00:01:18.659><c> to</c><00:01:18.900><c> and</c><00:01:19.439><c> we</c>

00:01:19.609 --> 00:01:19.619 align:start position:0%
move the current positions to and we
 

00:01:19.619 --> 00:01:21.530 align:start position:0%
move the current positions to and we
move<00:01:19.740><c> the</c><00:01:19.979><c> minimum</c><00:01:20.340><c> element</c><00:01:20.580><c> pointer</c><00:01:21.240><c> to</c>

00:01:21.530 --> 00:01:21.540 align:start position:0%
move the minimum element pointer to
 

00:01:21.540 --> 00:01:24.350 align:start position:0%
move the minimum element pointer to
index<00:01:21.960><c> one</c><00:01:22.200><c> it</c><00:01:22.619><c> is</c><00:01:22.860><c> 4</c><00:01:23.100><c> less</c><00:01:23.340><c> than</c><00:01:23.520><c> seven</c><00:01:23.759><c> it</c><00:01:24.240><c> is</c>

00:01:24.350 --> 00:01:24.360 align:start position:0%
index one it is 4 less than seven it is
 

00:01:24.360 --> 00:01:26.510 align:start position:0%
index one it is 4 less than seven it is
so<00:01:24.960><c> we</c><00:01:25.140><c> move</c><00:01:25.320><c> the</c><00:01:25.619><c> current</c><00:01:25.799><c> minimum</c><00:01:26.340><c> element</c>

00:01:26.510 --> 00:01:26.520 align:start position:0%
so we move the current minimum element
 

00:01:26.520 --> 00:01:28.789 align:start position:0%
so we move the current minimum element
pointer<00:01:27.060><c> to</c><00:01:27.420><c> index</c><00:01:27.780><c> two</c><00:01:27.960><c> we</c><00:01:28.320><c> move</c><00:01:28.500><c> the</c><00:01:28.680><c> current</c>

00:01:28.789 --> 00:01:28.799 align:start position:0%
pointer to index two we move the current
 

00:01:28.799 --> 00:01:31.490 align:start position:0%
pointer to index two we move the current
position<00:01:29.040><c> over</c><00:01:29.340><c> to</c><00:01:29.580><c> the</c><00:01:29.700><c> next</c><00:01:29.820><c> index</c><00:01:30.240><c> now</c><00:01:31.200><c> is</c>

00:01:31.490 --> 00:01:31.500 align:start position:0%
position over to the next index now is
 

00:01:31.500 --> 00:01:33.890 align:start position:0%
position over to the next index now is
12<00:01:31.680><c> less</c><00:01:31.979><c> than</c><00:01:32.159><c> four</c><00:01:32.340><c> it</c><00:01:32.880><c> is</c><00:01:33.060><c> not</c><00:01:33.180><c> so</c><00:01:33.600><c> we</c><00:01:33.720><c> don't</c>

00:01:33.890 --> 00:01:33.900 align:start position:0%
12 less than four it is not so we don't
 

00:01:33.900 --> 00:01:36.050 align:start position:0%
12 less than four it is not so we don't
do<00:01:34.020><c> anything</c><00:01:34.200><c> we</c><00:01:34.799><c> just</c><00:01:34.920><c> simply</c><00:01:35.159><c> move</c><00:01:35.460><c> the</c>

00:01:36.050 --> 00:01:36.060 align:start position:0%
do anything we just simply move the
 

00:01:36.060 --> 00:01:37.789 align:start position:0%
do anything we just simply move the
current<00:01:36.180><c> position</c><00:01:36.420><c> over</c><00:01:36.780><c> to</c><00:01:37.020><c> the</c><00:01:37.200><c> next</c><00:01:37.380><c> array</c>

00:01:37.789 --> 00:01:37.799 align:start position:0%
current position over to the next array
 

00:01:37.799 --> 00:01:41.569 align:start position:0%
current position over to the next array
index<00:01:38.159><c> now</c><00:01:39.000><c> is</c><00:01:39.240><c> 23</c><00:01:39.780><c> less</c><00:01:40.140><c> than</c><00:01:40.320><c> 4</c><00:01:40.500><c> it</c><00:01:41.220><c> isn't</c>

00:01:41.569 --> 00:01:41.579 align:start position:0%
index now is 23 less than 4 it isn't
 

00:01:41.579 --> 00:01:43.010 align:start position:0%
index now is 23 less than 4 it isn't
which<00:01:41.880><c> means</c><00:01:42.060><c> the</c><00:01:42.240><c> minimum</c><00:01:42.479><c> element</c><00:01:42.600><c> in</c><00:01:42.900><c> this</c>

00:01:43.010 --> 00:01:43.020 align:start position:0%
which means the minimum element in this
 

00:01:43.020 --> 00:01:45.109 align:start position:0%
which means the minimum element in this
iteration<00:01:43.380><c> is</c><00:01:43.680><c> 4</c><00:01:43.860><c> and</c><00:01:44.400><c> we</c><00:01:44.520><c> need</c><00:01:44.700><c> to</c><00:01:44.759><c> swap</c><00:01:45.000><c> it</c>

00:01:45.109 --> 00:01:45.119 align:start position:0%
iteration is 4 and we need to swap it
 

00:01:45.119 --> 00:01:46.910 align:start position:0%
iteration is 4 and we need to swap it
with<00:01:45.299><c> the</c><00:01:45.540><c> next</c><00:01:45.720><c> available</c><00:01:45.960><c> position</c><00:01:46.500><c> in</c><00:01:46.799><c> the</c>

00:01:46.910 --> 00:01:46.920 align:start position:0%
with the next available position in the
 

00:01:46.920 --> 00:01:49.010 align:start position:0%
with the next available position in the
array<00:01:47.220><c> which</c><00:01:47.520><c> is</c><00:01:47.640><c> index</c><00:01:47.939><c> one</c>

00:01:49.010 --> 00:01:49.020 align:start position:0%
array which is index one
 

00:01:49.020 --> 00:01:51.050 align:start position:0%
array which is index one
so<00:01:49.320><c> we're</c><00:01:49.500><c> going</c><00:01:49.619><c> to</c><00:01:49.680><c> perform</c><00:01:49.799><c> a</c><00:01:50.040><c> swap</c><00:01:50.280><c> 4</c><00:01:50.640><c> is</c><00:01:50.880><c> in</c>

00:01:51.050 --> 00:01:51.060 align:start position:0%
so we're going to perform a swap 4 is in
 

00:01:51.060 --> 00:01:52.730 align:start position:0%
so we're going to perform a swap 4 is in
the<00:01:51.180><c> correct</c><00:01:51.420><c> position</c><00:01:51.659><c> and</c><00:01:52.200><c> we</c><00:01:52.320><c> get</c><00:01:52.500><c> ready</c>

00:01:52.730 --> 00:01:52.740 align:start position:0%
the correct position and we get ready
 

00:01:52.740 --> 00:01:54.830 align:start position:0%
the correct position and we get ready
for<00:01:52.979><c> the</c><00:01:53.159><c> next</c><00:01:53.340><c> iteration</c>

00:01:54.830 --> 00:01:54.840 align:start position:0%
for the next iteration
 

00:01:54.840 --> 00:01:56.030 align:start position:0%
for the next iteration
so<00:01:55.140><c> we're</c><00:01:55.259><c> going</c><00:01:55.380><c> to</c><00:01:55.500><c> do</c><00:01:55.619><c> this</c><00:01:55.680><c> three</c><00:01:55.860><c> more</c>

00:01:56.030 --> 00:01:56.040 align:start position:0%
so we're going to do this three more
 

00:01:56.040 --> 00:01:58.429 align:start position:0%
so we're going to do this three more
times<00:01:56.280><c> until</c><00:01:56.640><c> the</c><00:01:57.060><c> array</c><00:01:57.360><c> is</c><00:01:57.600><c> sorted</c><00:01:58.020><c> because</c>

00:01:58.429 --> 00:01:58.439 align:start position:0%
times until the array is sorted because
 

00:01:58.439 --> 00:02:00.109 align:start position:0%
times until the array is sorted because
of<00:01:58.619><c> the</c><00:01:58.740><c> way</c><00:01:58.799><c> selection</c><00:01:59.159><c> sort</c><00:01:59.399><c> works</c><00:01:59.759><c> it</c><00:01:59.939><c> is</c>

00:02:00.109 --> 00:02:00.119 align:start position:0%
of the way selection sort works it is
 

00:02:00.119 --> 00:02:03.350 align:start position:0%
of the way selection sort works it is
also<00:02:00.420><c> a</c><00:02:00.720><c> Time</c><00:02:00.840><c> complexity</c><00:02:01.500><c> of</c><00:02:01.920><c> N</c><00:02:02.159><c> squared</c>

00:02:03.350 --> 00:02:03.360 align:start position:0%
also a Time complexity of N squared
 

00:02:03.360 --> 00:02:05.030 align:start position:0%
also a Time complexity of N squared
you're<00:02:03.720><c> going</c><00:02:03.899><c> to</c><00:02:03.960><c> end</c><00:02:04.079><c> up</c><00:02:04.200><c> having</c><00:02:04.380><c> two</c><00:02:04.740><c> for</c>

00:02:05.030 --> 00:02:05.040 align:start position:0%
you're going to end up having two for
 

00:02:05.040 --> 00:02:07.370 align:start position:0%
you're going to end up having two for
Loops<00:02:05.399><c> whenever</c><00:02:06.060><c> you</c><00:02:06.240><c> code</c><00:02:06.479><c> this</c><00:02:06.719><c> to</c><00:02:07.200><c> make</c>

00:02:07.370 --> 00:02:07.380 align:start position:0%
Loops whenever you code this to make
 

00:02:07.380 --> 00:02:09.650 align:start position:0%
Loops whenever you code this to make
this<00:02:07.619><c> work</c><00:02:07.799><c> congratulations</c><00:02:08.759><c> you</c><00:02:09.479><c> just</c>

00:02:09.650 --> 00:02:09.660 align:start position:0%
this work congratulations you just
 

00:02:09.660 --> 00:02:11.330 align:start position:0%
this work congratulations you just
completed<00:02:10.080><c> selection</c><00:02:10.440><c> sort</c><00:02:10.800><c> if</c><00:02:11.099><c> you</c><00:02:11.220><c> didn't</c>

00:02:11.330 --> 00:02:11.340 align:start position:0%
completed selection sort if you didn't
 

00:02:11.340 --> 00:02:13.190 align:start position:0%
completed selection sort if you didn't
quite<00:02:11.459><c> understand</c><00:02:11.640><c> it</c><00:02:12.000><c> that's</c><00:02:12.360><c> okay</c><00:02:12.599><c> rewatch</c>

00:02:13.190 --> 00:02:13.200 align:start position:0%
quite understand it that's okay rewatch
 

00:02:13.200 --> 00:02:15.530 align:start position:0%
quite understand it that's okay rewatch
the<00:02:13.379><c> video</c><00:02:13.560><c> but</c><00:02:13.920><c> this</c><00:02:14.280><c> time</c><00:02:14.459><c> also</c><00:02:15.000><c> do</c><00:02:15.239><c> it</c><00:02:15.360><c> on</c>

00:02:15.530 --> 00:02:15.540 align:start position:0%
the video but this time also do it on
 

00:02:15.540 --> 00:02:17.750 align:start position:0%
the video but this time also do it on
paper<00:02:15.780><c> I</c><00:02:16.379><c> find</c><00:02:16.500><c> out</c><00:02:16.680><c> that</c><00:02:16.800><c> if</c><00:02:16.980><c> I</c><00:02:17.160><c> also</c><00:02:17.340><c> write</c><00:02:17.580><c> it</c>

00:02:17.750 --> 00:02:17.760 align:start position:0%
paper I find out that if I also write it
 

00:02:17.760 --> 00:02:20.390 align:start position:0%
paper I find out that if I also write it
on<00:02:17.940><c> paper</c><00:02:18.180><c> that</c><00:02:18.720><c> helps</c><00:02:19.080><c> me</c><00:02:19.379><c> understand</c><00:02:20.040><c> a</c>

00:02:20.390 --> 00:02:20.400 align:start position:0%
on paper that helps me understand a
 

00:02:20.400 --> 00:02:22.130 align:start position:0%
on paper that helps me understand a
little<00:02:20.520><c> bit</c><00:02:20.640><c> better</c><00:02:20.879><c> and</c><00:02:21.300><c> I</c><00:02:21.480><c> can</c><00:02:21.720><c> remember</c>

00:02:22.130 --> 00:02:22.140 align:start position:0%
little bit better and I can remember
 

00:02:22.140 --> 00:02:24.530 align:start position:0%
little bit better and I can remember
what<00:02:22.620><c> I'm</c><00:02:22.739><c> doing</c><00:02:22.980><c> but</c><00:02:23.700><c> that's</c><00:02:24.000><c> just</c><00:02:24.180><c> how</c><00:02:24.360><c> I</c>

00:02:24.530 --> 00:02:24.540 align:start position:0%
what I'm doing but that's just how I
 

00:02:24.540 --> 00:02:26.330 align:start position:0%
what I'm doing but that's just how I
learn<00:02:24.720><c> I</c><00:02:25.260><c> know</c><00:02:25.319><c> you</c><00:02:25.620><c> understand</c><00:02:25.800><c> how</c><00:02:26.220><c> you</c>

00:02:26.330 --> 00:02:26.340 align:start position:0%
learn I know you understand how you
 

00:02:26.340 --> 00:02:28.790 align:start position:0%
learn I know you understand how you
learn<00:02:26.520><c> so</c><00:02:27.000><c> you</c><00:02:27.239><c> do</c><00:02:27.420><c> it</c><00:02:27.599><c> your</c><00:02:27.900><c> way</c><00:02:28.080><c> have</c><00:02:28.680><c> a</c>

00:02:28.790 --> 00:02:28.800 align:start position:0%
learn so you do it your way have a
 

00:02:28.800 --> 00:02:30.470 align:start position:0%
learn so you do it your way have a
wonderful<00:02:28.980><c> day</c><00:02:29.160><c> and</c><00:02:29.400><c> in</c><00:02:29.520><c> the</c><00:02:29.640><c> meantime</c><00:02:29.940><c> watch</c>

00:02:30.470 --> 00:02:30.480 align:start position:0%
wonderful day and in the meantime watch
 

00:02:30.480 --> 00:02:32.900 align:start position:0%
wonderful day and in the meantime watch
this<00:02:30.720><c> video</c>

