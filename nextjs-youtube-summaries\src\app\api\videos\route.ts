import { NextRequest, NextResponse } from 'next/server'
import { DatabaseService } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const topic = searchParams.get('topic')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    if (topic) {
      const videos = await DatabaseService.getVideosByTopic(topic, limit, offset)
      return NextResponse.json({ videos, topic })
    } else {
      const videos = await DatabaseService.getRecentVideos(limit)
      return NextResponse.json({ videos })
    }
  } catch (error) {
    console.error('Error in videos API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch videos' },
      { status: 500 }
    )
  }
}
