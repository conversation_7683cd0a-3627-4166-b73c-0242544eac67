WEBVTT
Kind: captions
Language: en

00:00:00.280 --> 00:00:02.590 align:start position:0%
 
hi<00:00:00.520><c> this</c><00:00:00.599><c> is</c><00:00:00.719><c> floran</c><00:00:01.160><c> <PERSON><PERSON></c><00:00:01.560><c> with</c><00:00:01.760><c> GitHub</c><00:00:02.440><c> and</c>

00:00:02.590 --> 00:00:02.600 align:start position:0%
hi this is flora<PERSON> with GitHub and
 

00:00:02.600 --> 00:00:04.990 align:start position:0%
hi this is flora<PERSON> with G<PERSON><PERSON><PERSON> and
I'm<00:00:02.800><c> going</c><00:00:02.960><c> to</c><00:00:03.159><c> show</c><00:00:03.399><c> you</c><00:00:03.719><c> how</c><00:00:03.879><c> to</c><00:00:04.120><c> use</c><00:00:04.520><c> inline</c>

00:00:04.990 --> 00:00:05.000 align:start position:0%
I'm going to show you how to use inline
 

00:00:05.000 --> 00:00:07.550 align:start position:0%
I'm going to show you how to use inline
chat<00:00:05.440><c> from</c><00:00:05.680><c> GitHub</c><00:00:06.040><c> co-pilot</c><00:00:06.879><c> using</c><00:00:07.160><c> a</c><00:00:07.279><c> screen</c>

00:00:07.550 --> 00:00:07.560 align:start position:0%
chat from GitHub co-pilot using a screen
 

00:00:07.560 --> 00:00:10.190 align:start position:0%
chat from GitHub co-pilot using a screen
reader<00:00:08.120><c> I</c><00:00:08.200><c> will</c><00:00:08.320><c> be</c><00:00:08.400><c> using</c><00:00:08.599><c> mvda</c><00:00:09.080><c> 202</c><00:00:09.280><c> 24.1</c><00:00:10.080><c> on</c>

00:00:10.190 --> 00:00:10.200 align:start position:0%
reader I will be using mvda 202 24.1 on
 

00:00:10.200 --> 00:00:12.709 align:start position:0%
reader I will be using mvda 202 24.1 on
Windows<00:00:10.559><c> 11</c><00:00:11.160><c> using</c><00:00:11.480><c> the</c><00:00:11.679><c> GitHub</c><00:00:12.120><c> co-pilot</c>

00:00:12.709 --> 00:00:12.719 align:start position:0%
Windows 11 using the GitHub co-pilot
 

00:00:12.719 --> 00:00:15.509 align:start position:0%
Windows 11 using the GitHub co-pilot
extension<00:00:13.400><c> with</c><00:00:13.559><c> nvs</c><00:00:14.040><c> code</c><00:00:14.320><c> version</c><00:00:14.599><c> 1.88</c>

00:00:15.509 --> 00:00:15.519 align:start position:0%
extension with nvs code version 1.88
 

00:00:15.519 --> 00:00:18.390 align:start position:0%
extension with nvs code version 1.88
which<00:00:15.639><c> is</c><00:00:15.719><c> the</c><00:00:15.920><c> March</c><00:00:16.480><c> 2024</c><00:00:17.279><c> release</c><00:00:18.000><c> to</c><00:00:18.160><c> start</c>

00:00:18.390 --> 00:00:18.400 align:start position:0%
which is the March 2024 release to start
 

00:00:18.400 --> 00:00:20.189 align:start position:0%
which is the March 2024 release to start
out<00:00:18.600><c> with</c><00:00:18.800><c> we</c><00:00:18.920><c> need</c><00:00:19.080><c> some</c><00:00:19.279><c> code</c><00:00:19.520><c> to</c><00:00:19.680><c> work</c><00:00:19.920><c> with</c>

00:00:20.189 --> 00:00:20.199 align:start position:0%
out with we need some code to work with
 

00:00:20.199 --> 00:00:21.550 align:start position:0%
out with we need some code to work with
we<00:00:20.279><c> have</c><00:00:20.400><c> a</c><00:00:20.560><c> function</c><00:00:21.000><c> from</c><00:00:21.160><c> the</c><00:00:21.320><c> getting</c>

00:00:21.550 --> 00:00:21.560 align:start position:0%
we have a function from the getting
 

00:00:21.560 --> 00:00:23.550 align:start position:0%
we have a function from the getting
start<00:00:21.920><c> documentation</c><00:00:22.519><c> already</c><00:00:22.840><c> in</c><00:00:23.039><c> place</c><00:00:23.480><c> and</c>

00:00:23.550 --> 00:00:23.560 align:start position:0%
start documentation already in place and
 

00:00:23.560 --> 00:00:25.269 align:start position:0%
start documentation already in place and
what<00:00:23.680><c> we're</c><00:00:23.800><c> going</c><00:00:24.000><c> to</c><00:00:24.160><c> do</c><00:00:24.279><c> is</c><00:00:24.519><c> highlight</c><00:00:25.039><c> this</c>

00:00:25.269 --> 00:00:25.279 align:start position:0%
what we're going to do is highlight this
 

00:00:25.279 --> 00:00:27.070 align:start position:0%
what we're going to do is highlight this
function<00:00:25.840><c> function</c><00:00:26.039><c> calculate</c><00:00:26.640><c> two</c><00:00:26.760><c> days</c>

00:00:27.070 --> 00:00:27.080 align:start position:0%
function function calculate two days
 

00:00:27.080 --> 00:00:31.749 align:start position:0%
function function calculate two days
start<00:00:28.080><c> 24</c><00:00:28.480><c> 60</c><00:00:28.800><c> 60</c><00:00:29.119><c> 1000</c>

00:00:31.749 --> 00:00:31.759 align:start position:0%
start 24 60 60 1000
 

00:00:31.759 --> 00:00:33.590 align:start position:0%
start 24 60 60 1000
the<00:00:31.880><c> purpose</c><00:00:32.119><c> of</c><00:00:32.279><c> GitHub</c><00:00:32.599><c> co-pilot</c><00:00:33.160><c> inline</c>

00:00:33.590 --> 00:00:33.600 align:start position:0%
the purpose of GitHub co-pilot inline
 

00:00:33.600 --> 00:00:35.950 align:start position:0%
the purpose of GitHub co-pilot inline
chat<00:00:33.960><c> is</c><00:00:34.079><c> to</c><00:00:34.280><c> ask</c><00:00:34.440><c> a</c><00:00:34.719><c> question</c><00:00:35.160><c> about</c><00:00:35.480><c> existing</c>

00:00:35.950 --> 00:00:35.960 align:start position:0%
chat is to ask a question about existing
 

00:00:35.960 --> 00:00:38.110 align:start position:0%
chat is to ask a question about existing
code<00:00:36.440><c> without</c><00:00:36.680><c> leaving</c><00:00:37.160><c> the</c><00:00:37.440><c> context</c><00:00:37.960><c> you're</c>

00:00:38.110 --> 00:00:38.120 align:start position:0%
code without leaving the context you're
 

00:00:38.120 --> 00:00:40.750 align:start position:0%
code without leaving the context you're
in<00:00:38.600><c> we</c><00:00:38.719><c> can</c><00:00:38.920><c> highlight</c><00:00:39.280><c> some</c><00:00:39.520><c> code</c><00:00:40.079><c> and</c><00:00:40.280><c> ask</c>

00:00:40.750 --> 00:00:40.760 align:start position:0%
in we can highlight some code and ask
 

00:00:40.760 --> 00:00:42.910 align:start position:0%
in we can highlight some code and ask
GitHub<00:00:41.160><c> co-pilot</c><00:00:41.680><c> to</c><00:00:41.800><c> explain</c><00:00:42.160><c> or</c><00:00:42.320><c> modify</c><00:00:42.760><c> the</c>

00:00:42.910 --> 00:00:42.920 align:start position:0%
GitHub co-pilot to explain or modify the
 

00:00:42.920 --> 00:00:45.190 align:start position:0%
GitHub co-pilot to explain or modify the
code<00:00:43.360><c> after</c><00:00:43.640><c> pressing</c><00:00:44.000><c> control</c><00:00:44.399><c> I</c><00:00:44.800><c> to</c><00:00:44.960><c> bring</c>

00:00:45.190 --> 00:00:45.200 align:start position:0%
code after pressing control I to bring
 

00:00:45.200 --> 00:00:47.990 align:start position:0%
code after pressing control I to bring
up<00:00:45.320><c> the</c><00:00:45.480><c> GitHub</c><00:00:45.800><c> cops</c><00:00:46.440><c> inline</c><00:00:46.879><c> chat</c><00:00:47.640><c> chatut</c>

00:00:47.990 --> 00:00:48.000 align:start position:0%
up the GitHub cops inline chat chatut
 

00:00:48.000 --> 00:00:50.830 align:start position:0%
up the GitHub cops inline chat chatut
type<00:00:48.239><c> ask</c><00:00:48.360><c> questions</c><00:00:49.000><c> topics</c><00:00:49.879><c> request</c><00:00:50.559><c> for</c>

00:00:50.830 --> 00:00:50.840 align:start position:0%
type ask questions topics request for
 

00:00:50.840 --> 00:00:53.389 align:start position:0%
type ask questions topics request for
accessibility<00:00:51.840><c> M</c><00:00:52.719><c> let's</c><00:00:52.960><c> ask</c><00:00:53.120><c> for</c><00:00:53.280><c> an</c>

00:00:53.389 --> 00:00:53.399 align:start position:0%
accessibility M let's ask for an
 

00:00:53.399 --> 00:00:55.189 align:start position:0%
accessibility M let's ask for an
explanation<00:00:53.800><c> of</c><00:00:53.960><c> this</c><00:00:54.199><c> code</c><00:00:54.680><c> by</c><00:00:54.840><c> simply</c>

00:00:55.189 --> 00:00:55.199 align:start position:0%
explanation of this code by simply
 

00:00:55.199 --> 00:00:57.069 align:start position:0%
explanation of this code by simply
typing<00:00:55.600><c> explain</c><00:00:56.000><c> this</c><00:00:56.199><c> code</c><00:00:56.600><c> and</c><00:00:56.719><c> hitting</c>

00:00:57.069 --> 00:00:57.079 align:start position:0%
typing explain this code and hitting
 

00:00:57.079 --> 00:00:58.990 align:start position:0%
typing explain this code and hitting
enter<00:00:57.800><c> chat</c><00:00:58.000><c> reest</c><00:00:58.440><c> input</c><00:00:58.680><c> type</c><00:00:58.800><c> to</c><00:00:58.879><c> ask</c>

00:00:58.990 --> 00:00:59.000 align:start position:0%
enter chat reest input type to ask
 

00:00:59.000 --> 00:01:00.470 align:start position:0%
enter chat reest input type to ask
questions<00:00:59.160><c> or</c><00:00:59.239><c> typ</c><00:00:59.640><c> topics</c><00:01:00.160><c> to</c><00:01:00.199><c> send</c><00:01:00.320><c> out</c><00:01:00.440><c> the</c>

00:01:00.470 --> 00:01:00.480 align:start position:0%
questions or typ topics to send out the
 

00:01:00.480 --> 00:01:02.150 align:start position:0%
questions or typ topics to send out the
request<00:01:00.960><c> plus1</c><00:01:01.199><c> for</c><00:01:01.440><c> accessibility</c><00:01:01.760><c> help</c><00:01:02.079><c> has</c>

00:01:02.150 --> 00:01:02.160 align:start position:0%
request plus1 for accessibility help has
 

00:01:02.160 --> 00:01:02.990 align:start position:0%
request plus1 for accessibility help has
auto<00:01:02.320><c> complete</c><00:01:02.480><c> multi</c><00:01:02.640><c> line</c><00:01:02.760><c> selected</c>

00:01:02.990 --> 00:01:03.000 align:start position:0%
auto complete multi line selected
 

00:01:03.000 --> 00:01:03.950 align:start position:0%
auto complete multi line selected
explain<00:01:03.239><c> this</c><00:01:03.320><c> code</c><00:01:03.519><c> this</c><00:01:03.600><c> code</c><00:01:03.719><c> defines</c><00:01:03.920><c> a</c>

00:01:03.950 --> 00:01:03.960 align:start position:0%
explain this code this code defines a
 

00:01:03.960 --> 00:01:04.869 align:start position:0%
explain this code this code defines a
function<00:01:04.159><c> called</c><00:01:04.320><c> calculate</c><00:01:04.600><c> dat</c><00:01:04.720><c> between</c>

00:01:04.869 --> 00:01:04.879 align:start position:0%
function called calculate dat between
 

00:01:04.879 --> 00:01:05.750 align:start position:0%
function called calculate dat between
two<00:01:04.960><c> dates</c><00:01:05.119><c> and</c><00:01:05.159><c> JavaScript</c><00:01:05.519><c> the</c><00:01:05.560><c> function</c>

00:01:05.750 --> 00:01:05.760 align:start position:0%
two dates and JavaScript the function
 

00:01:05.760 --> 00:01:06.910 align:start position:0%
two dates and JavaScript the function
takes<00:01:05.920><c> two</c><00:01:06.000><c> parameters</c><00:01:06.320><c> start</c><00:01:06.680><c> date</c><00:01:06.840><c> which</c>

00:01:06.910 --> 00:01:06.920 align:start position:0%
takes two parameters start date which
 

00:01:06.920 --> 00:01:07.910 align:start position:0%
takes two parameters start date which
represent<00:01:07.159><c> two</c><00:01:07.240><c> dates</c><00:01:07.439><c> inside</c><00:01:07.640><c> the</c><00:01:07.680><c> function</c>

00:01:07.910 --> 00:01:07.920 align:start position:0%
represent two dates inside the function
 

00:01:07.920 --> 00:01:08.670 align:start position:0%
represent two dates inside the function
the<00:01:08.000><c> constant</c><00:01:08.240><c> variable</c><00:01:08.479><c> one</c><00:01:08.560><c> day</c><00:01:08.640><c> is</c>

00:01:08.670 --> 00:01:08.680 align:start position:0%
the constant variable one day is
 

00:01:08.680 --> 00:01:10.190 align:start position:0%
the constant variable one day is
declared<00:01:08.960><c> the</c><00:01:09.040><c> side</c><00:01:09.159><c> the</c><00:01:09.200><c> value</c><00:01:09.360><c> of</c><00:01:09.439><c> 24</c><00:01:09.880><c> 60</c><00:01:10.040><c> St</c>

00:01:10.190 --> 00:01:10.200 align:start position:0%
declared the side the value of 24 60 St
 

00:01:10.200 --> 00:01:11.429 align:start position:0%
declared the side the value of 24 60 St
60<00:01:10.520><c> 1,000</c><00:01:10.880><c> this</c><00:01:10.960><c> value</c><00:01:11.119><c> represents</c><00:01:11.400><c> the</c>

00:01:11.429 --> 00:01:11.439 align:start position:0%
60 1,000 this value represents the
 

00:01:11.439 --> 00:01:12.270 align:start position:0%
60 1,000 this value represents the
number<00:01:11.560><c> of</c><00:01:11.600><c> milliseconds</c><00:01:11.960><c> in</c><00:01:12.040><c> one</c><00:01:12.119><c> day</c><00:01:12.240><c> the</c>

00:01:12.270 --> 00:01:12.280 align:start position:0%
number of milliseconds in one day the
 

00:01:12.280 --> 00:01:13.109 align:start position:0%
number of milliseconds in one day the
function<00:01:12.479><c> then</c><00:01:12.600><c> calculates</c><00:01:12.880><c> the</c><00:01:12.960><c> number</c><00:01:13.080><c> of</c>

00:01:13.109 --> 00:01:13.119 align:start position:0%
function then calculates the number of
 

00:01:13.119 --> 00:01:14.030 align:start position:0%
function then calculates the number of
days<00:01:13.280><c> between</c><00:01:13.439><c> the</c><00:01:13.479><c> start</c><00:01:13.600><c> dat</c><00:01:13.720><c> and</c><00:01:13.799><c> end</c><00:01:13.880><c> date</c>

00:01:14.030 --> 00:01:14.040 align:start position:0%
days between the start dat and end date
 

00:01:14.040 --> 00:01:14.870 align:start position:0%
days between the start dat and end date
by<00:01:14.159><c> subtracting</c><00:01:14.479><c> the</c><00:01:14.520><c> start</c><00:01:14.640><c> date</c><00:01:14.759><c> from</c><00:01:14.840><c> the</c>

00:01:14.870 --> 00:01:14.880 align:start position:0%
by subtracting the start date from the
 

00:01:14.880 --> 00:01:15.830 align:start position:0%
by subtracting the start date from the
end<00:01:15.000><c> date</c><00:01:15.159><c> and</c><00:01:15.240><c> dividing</c><00:01:15.439><c> the</c><00:01:15.520><c> result</c><00:01:15.680><c> by</c><00:01:15.759><c> one</c>

00:01:15.830 --> 00:01:15.840 align:start position:0%
end date and dividing the result by one
 

00:01:15.840 --> 00:01:17.070 align:start position:0%
end date and dividing the result by one
date<00:01:15.960><c> the</c><00:01:16.000><c> ma</c><00:01:16.320><c> function</c><00:01:16.520><c> is</c><00:01:16.560><c> used</c><00:01:16.720><c> to</c><00:01:16.799><c> Ure</c><00:01:17.000><c> that</c>

00:01:17.070 --> 00:01:17.080 align:start position:0%
date the ma function is used to Ure that
 

00:01:17.080 --> 00:01:17.990 align:start position:0%
date the ma function is used to Ure that
the<00:01:17.159><c> result</c><00:01:17.320><c> is</c><00:01:17.400><c> always</c><00:01:17.520><c> positive</c><00:01:17.759><c> regardless</c>

00:01:17.990 --> 00:01:18.000 align:start position:0%
the result is always positive regardless
 

00:01:18.000 --> 00:01:19.870 align:start position:0%
the result is always positive regardless
of<00:01:18.080><c> the</c><00:01:18.119><c> order</c><00:01:18.240><c> of</c><00:01:18.320><c> the</c><00:01:18.400><c> dates</c><00:01:18.960><c> okay</c><00:01:19.479><c> we</c><00:01:19.600><c> got</c><00:01:19.720><c> an</c>

00:01:19.870 --> 00:01:19.880 align:start position:0%
of the order of the dates okay we got an
 

00:01:19.880 --> 00:01:21.390 align:start position:0%
of the order of the dates okay we got an
explanation<00:01:20.280><c> of</c><00:01:20.400><c> our</c><00:01:20.640><c> code</c><00:01:20.960><c> that's</c><00:01:21.200><c> very</c>

00:01:21.390 --> 00:01:21.400 align:start position:0%
explanation of our code that's very
 

00:01:21.400 --> 00:01:24.789 align:start position:0%
explanation of our code that's very
helpful<00:01:22.079><c> you</c><00:01:22.159><c> can</c><00:01:22.360><c> also</c><00:01:22.680><c> ask</c><00:01:23.200><c> GitHub</c><00:01:23.799><c> copilot</c>

00:01:24.789 --> 00:01:24.799 align:start position:0%
helpful you can also ask GitHub copilot
 

00:01:24.799 --> 00:01:26.550 align:start position:0%
helpful you can also ask GitHub copilot
to<00:01:25.119><c> modify</c><00:01:25.600><c> the</c><00:01:25.720><c> code</c><00:01:26.000><c> that's</c><00:01:26.159><c> currently</c>

00:01:26.550 --> 00:01:26.560 align:start position:0%
to modify the code that's currently
 

00:01:26.560 --> 00:01:28.270 align:start position:0%
to modify the code that's currently
highlighted<00:01:27.200><c> when</c><00:01:27.360><c> GitHub</c><00:01:27.720><c> co-pilot</c>

00:01:28.270 --> 00:01:28.280 align:start position:0%
highlighted when GitHub co-pilot
 

00:01:28.280 --> 00:01:30.350 align:start position:0%
highlighted when GitHub co-pilot
modifies<00:01:28.840><c> code</c><00:01:29.159><c> it</c><00:01:29.280><c> will</c><00:01:29.439><c> show</c><00:01:29.640><c> you</c><00:01:29.799><c> a</c><00:01:29.960><c> a</c><00:01:30.079><c> diff</c>

00:01:30.350 --> 00:01:30.360 align:start position:0%
modifies code it will show you a a diff
 

00:01:30.360 --> 00:01:32.670 align:start position:0%
modifies code it will show you a a diff
of<00:01:30.520><c> the</c><00:01:30.640><c> suggested</c><00:01:31.159><c> changes</c><00:01:32.079><c> which</c><00:01:32.240><c> you</c><00:01:32.360><c> can</c>

00:01:32.670 --> 00:01:32.680 align:start position:0%
of the suggested changes which you can
 

00:01:32.680 --> 00:01:35.389 align:start position:0%
of the suggested changes which you can
accept<00:01:33.159><c> regenerate</c><00:01:33.799><c> or</c><00:01:34.000><c> decline</c><00:01:34.399><c> altogether</c>

00:01:35.389 --> 00:01:35.399 align:start position:0%
accept regenerate or decline altogether
 

00:01:35.399 --> 00:01:37.389 align:start position:0%
accept regenerate or decline altogether
let's<00:01:35.640><c> try</c><00:01:35.920><c> this</c><00:01:36.159><c> now</c><00:01:36.560><c> by</c><00:01:36.720><c> asking</c><00:01:37.040><c> GitHub</c>

00:01:37.389 --> 00:01:37.399 align:start position:0%
let's try this now by asking GitHub
 

00:01:37.399 --> 00:01:39.590 align:start position:0%
let's try this now by asking GitHub
co-pilot<00:01:37.920><c> to</c><00:01:38.079><c> make</c><00:01:38.280><c> this</c><00:01:38.439><c> code</c><00:01:38.720><c> better</c><00:01:39.320><c> I</c><00:01:39.399><c> will</c>

00:01:39.590 --> 00:01:39.600 align:start position:0%
co-pilot to make this code better I will
 

00:01:39.600 --> 00:01:41.910 align:start position:0%
co-pilot to make this code better I will
simply<00:01:39.960><c> type</c><00:01:40.399><c> improve</c><00:01:40.920><c> this</c><00:01:41.119><c> code</c><00:01:41.520><c> and</c><00:01:41.680><c> hit</c>

00:01:41.910 --> 00:01:41.920 align:start position:0%
simply type improve this code and hit
 

00:01:41.920 --> 00:01:43.630 align:start position:0%
simply type improve this code and hit
enter<00:01:42.680><c> chat</c><00:01:42.840><c> request</c><00:01:43.200><c> improve</c><00:01:43.399><c> this</c><00:01:43.520><c> code</c>

00:01:43.630 --> 00:01:43.640 align:start position:0%
enter chat request improve this code
 

00:01:43.640 --> 00:01:46.630 align:start position:0%
enter chat request improve this code
selected<00:01:44.040><c> chat</c><00:01:44.600><c> response1</c><00:01:45.600><c> plus</c><00:01:46.040><c> add</c>

00:01:46.630 --> 00:01:46.640 align:start position:0%
selected chat response1 plus add
 

00:01:46.640 --> 00:01:47.789 align:start position:0%
selected chat response1 plus add
function<00:01:46.880><c> calculate</c><00:01:47.200><c> days</c><00:01:47.320><c> between</c><00:01:47.520><c> two</c><00:01:47.640><c> dat</c>

00:01:47.789 --> 00:01:47.799 align:start position:0%
function calculate days between two dat
 

00:01:47.799 --> 00:01:52.069 align:start position:0%
function calculate days between two dat
start<00:01:48.200><c> dat</c><00:01:49.200><c> level</c><00:01:50.000><c> 24</c><00:01:50.399><c> 60</c><00:01:50.719><c> 60</c><00:01:51.040><c> 1000</c><00:01:51.399><c> original</c>

00:01:52.069 --> 00:01:52.079 align:start position:0%
start dat level 24 60 60 1000 original
 

00:01:52.079 --> 00:01:53.670 align:start position:0%
start dat level 24 60 60 1000 original
return<00:01:52.159><c> ma</c>

00:01:53.670 --> 00:01:53.680 align:start position:0%
return ma
 

00:01:53.680 --> 00:01:57.590 align:start position:0%
return ma
ma<00:01:54.680><c> modif</c><00:01:55.520><c> level</c><00:01:56.119><c> ER</c><00:01:56.399><c> start</c><00:01:56.840><c> must</c><00:01:57.159><c> object</c><00:01:57.479><c> the</c>

00:01:57.590 --> 00:01:57.600 align:start position:0%
ma modif level ER start must object the
 

00:01:57.600 --> 00:02:00.109 align:start position:0%
ma modif level ER start must object the
accessible<00:01:58.079><c> D</c><00:01:58.320><c> viewer</c><00:01:59.000><c> gives</c><00:01:59.159><c> us</c><00:01:59.399><c> sound</c><00:01:59.640><c> key</c>

00:02:00.109 --> 00:02:00.119 align:start position:0%
accessible D viewer gives us sound key
 

00:02:00.119 --> 00:02:01.550 align:start position:0%
accessible D viewer gives us sound key
and<00:02:00.280><c> verbal</c><00:02:00.600><c> announcements</c><00:02:01.159><c> about</c><00:02:01.399><c> what</c>

00:02:01.550 --> 00:02:01.560 align:start position:0%
and verbal announcements about what
 

00:02:01.560 --> 00:02:04.029 align:start position:0%
and verbal announcements about what
lines<00:02:01.920><c> have</c><00:02:02.079><c> been</c><00:02:02.479><c> unmodified</c><00:02:03.439><c> changed</c>

00:02:04.029 --> 00:02:04.039 align:start position:0%
lines have been unmodified changed
 

00:02:04.039 --> 00:02:05.789 align:start position:0%
lines have been unmodified changed
deleted<00:02:04.479><c> or</c><00:02:04.719><c> added</c><00:02:05.159><c> if</c><00:02:05.240><c> we're</c><00:02:05.439><c> happy</c><00:02:05.680><c> with</c>

00:02:05.789 --> 00:02:05.799 align:start position:0%
deleted or added if we're happy with
 

00:02:05.799 --> 00:02:07.350 align:start position:0%
deleted or added if we're happy with
these<00:02:05.960><c> changes</c><00:02:06.399><c> we</c><00:02:06.479><c> can</c><00:02:06.680><c> tap</c><00:02:06.960><c> into</c><00:02:07.200><c> the</c>

00:02:07.350 --> 00:02:07.360 align:start position:0%
these changes we can tap into the
 

00:02:07.360 --> 00:02:09.150 align:start position:0%
these changes we can tap into the
toolbar<00:02:07.880><c> and</c><00:02:08.039><c> press</c><00:02:08.239><c> the</c><00:02:08.399><c> accept</c><00:02:08.720><c> button</c>

00:02:09.150 --> 00:02:09.160 align:start position:0%
toolbar and press the accept button
 

00:02:09.160 --> 00:02:13.070 align:start position:0%
toolbar and press the accept button
accept<00:02:09.360><c> button</c><00:02:10.239><c> wig</c><00:02:10.599><c> 123</c><00:02:11.599><c> aut</c><00:02:12.480><c> function</c><00:02:12.680><c> Cal</c>

00:02:13.070 --> 00:02:13.080 align:start position:0%
accept button wig 123 aut function Cal
 

00:02:13.080 --> 00:02:14.790 align:start position:0%
accept button wig 123 aut function Cal
between<00:02:13.239><c> two</c><00:02:13.400><c> days</c><00:02:13.680><c> it</c><00:02:13.760><c> has</c><00:02:13.959><c> changed</c><00:02:14.280><c> our</c><00:02:14.519><c> code</c>

00:02:14.790 --> 00:02:14.800 align:start position:0%
between two days it has changed our code
 

00:02:14.800 --> 00:02:16.470 align:start position:0%
between two days it has changed our code
to<00:02:15.040><c> match</c><00:02:15.280><c> up</c><00:02:15.480><c> with</c><00:02:15.599><c> the</c><00:02:15.720><c> new</c><00:02:15.959><c> code</c><00:02:16.239><c> that</c><00:02:16.360><c> was</c>

00:02:16.470 --> 00:02:16.480 align:start position:0%
to match up with the new code that was
 

00:02:16.480 --> 00:02:18.390 align:start position:0%
to match up with the new code that was
shown<00:02:16.720><c> in</c><00:02:16.840><c> the</c><00:02:17.000><c> diff</c><00:02:17.239><c> viewer</c><00:02:17.920><c> I</c><00:02:18.000><c> would</c><00:02:18.160><c> like</c><00:02:18.280><c> to</c>

00:02:18.390 --> 00:02:18.400 align:start position:0%
shown in the diff viewer I would like to
 

00:02:18.400 --> 00:02:20.070 align:start position:0%
shown in the diff viewer I would like to
thank<00:02:18.560><c> you</c><00:02:18.680><c> for</c><00:02:18.879><c> watching</c><00:02:19.160><c> this</c><00:02:19.360><c> video</c><00:02:19.840><c> and</c><00:02:19.959><c> to</c>

00:02:20.070 --> 00:02:20.080 align:start position:0%
thank you for watching this video and to
 

00:02:20.080 --> 00:02:21.750 align:start position:0%
thank you for watching this video and to
be<00:02:20.200><c> on</c><00:02:20.319><c> the</c><00:02:20.440><c> lookout</c><00:02:20.840><c> for</c><00:02:21.000><c> more</c><00:02:21.239><c> videos</c><00:02:21.560><c> to</c>

00:02:21.750 --> 00:02:21.760 align:start position:0%
be on the lookout for more videos to
 

00:02:21.760 --> 00:02:24.760 align:start position:0%
be on the lookout for more videos to
come

