# Configuration variables
import os
import yt_dlp

CHANNEL_ID = "UClGbPcWpPnBKDxDmemMSXQQ"
OUTPUT_FOLDER = "D:\\1 - Youtube_Vimeo_Videos\\IEA PVPS\\"

def create_ydl_opts(output_path):
    return {
        'format': 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best',
        'outtmpl': os.path.join(output_path, '%(title)s.%(ext)s'),
        'quiet': True,
        'no_warnings': True,
        'ignoreerrors': True,
    }

def get_all_video_urls(channel_id):
    # Use the channel URL with '/videos' to get all uploaded videos
    channel_url = f"https://www.youtube.com/channel/{channel_id}/videos"
    ydl_opts = {
        'extract_flat': 'in_playlist',  # Extract all videos in the playlist
        'skip_download': True,
        'quiet': True,
        'no_warnings': True,
        'ignoreerrors': True,
        'playlistreverse': True,  # Start from the oldest video
        'playlistend': None,  # Download all videos (no limit)
    }
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            info = ydl.extract_info(channel_url, download=False)
            return [entry['url'] for entry in info['entries'] if entry.get('url')]
        except Exception as e:
            print(f"Error fetching video URLs: {str(e)}")
            return []

def download_video(video_url, output_path):
    ydl_opts = create_ydl_opts(output_path)
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            info = ydl.extract_info(video_url, download=True)
            try:
                title = info.get("title")
                if not title:
                    raise ValueError("Title missing in primary extraction")
            except Exception as ex:
                print(f"Error extracting title: {ex}. Falling back using yt_dlp default.")
                with yt_dlp.YoutubeDL({}) as fallback_ydl:
                    info = fallback_ydl.extract_info(video_url, download=False)
                title = info.get("title", "unknown_video")
            print(f"Downloaded: {title}")
        except Exception as e:
            print(f"Error downloading {video_url}: {str(e)}")

def main():
    print("Starting YouTube channel video downloader...")
    
    try:
        import yt_dlp
    except ImportError:
        print("Error: yt-dlp is not installed. Please install it using 'pip install yt-dlp'")
        return

    if not os.path.exists(OUTPUT_FOLDER):
        os.makedirs(OUTPUT_FOLDER)
        print(f"Created output folder: {OUTPUT_FOLDER}")
    
    print(f"Fetching video URLs for channel ID: {CHANNEL_ID}")
    video_urls = get_all_video_urls(CHANNEL_ID)
    
    if video_urls:
        print(f"Found {len(video_urls)} videos. Downloading all videos...")
        for i, url in enumerate(video_urls, 1):
            print(f"\nDownloading video {i} of {len(video_urls)}...")
            download_video(url, OUTPUT_FOLDER)
    else:
        print("No videos found in the channel or an error occurred while fetching URLs.")

    print("Script execution completed.")

if __name__ == "__main__":
    main()