WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:03.230 align:start position:0%
 
hey<00:00:00.240><c> Noah</c><00:00:00.919><c> thanks</c><00:00:01.120><c> a</c><00:00:01.280><c> lot</c><00:00:01.480><c> for</c><00:00:01.800><c> helping</c><00:00:02.240><c> me</c>

00:00:03.230 --> 00:00:03.240 align:start position:0%
hey <PERSON> thanks a lot for helping me
 

00:00:03.240 --> 00:00:04.950 align:start position:0%
hey <PERSON> thanks a lot for helping me
kind<00:00:03.360><c> of</c><00:00:03.480><c> do</c><00:00:03.679><c> this</c><00:00:03.879><c> overview</c><00:00:04.400><c> of</c><00:00:04.560><c> the</c><00:00:04.640><c> model</c>

00:00:04.950 --> 00:00:04.960 align:start position:0%
kind of do this overview of the model
 

00:00:04.960 --> 00:00:07.190 align:start position:0%
kind of do this overview of the model
registry<00:00:05.920><c> and</c><00:00:06.080><c> sort</c><00:00:06.279><c> of</c><00:00:06.399><c> the</c><00:00:06.480><c> nuts</c><00:00:06.720><c> and</c><00:00:06.839><c> bolts</c>

00:00:07.190 --> 00:00:07.200 align:start position:0%
registry and sort of the nuts and bolts
 

00:00:07.200 --> 00:00:08.310 align:start position:0%
registry and sort of the nuts and bolts
around

00:00:08.310 --> 00:00:08.320 align:start position:0%
around
 

00:00:08.320 --> 00:00:11.830 align:start position:0%
around
it<00:00:09.320><c> um</c><00:00:09.679><c> you</c><00:00:09.800><c> know</c><00:00:10.639><c> in</c><00:00:10.800><c> the</c><00:00:10.920><c> early</c><00:00:11.280><c> days</c><00:00:11.719><c> of</c>

00:00:11.830 --> 00:00:11.840 align:start position:0%
it um you know in the early days of
 

00:00:11.840 --> 00:00:14.749 align:start position:0%
it um you know in the early days of
weights<00:00:12.080><c> and</c><00:00:12.240><c> biases</c><00:00:12.880><c> I</c><00:00:13.000><c> built</c><00:00:13.280><c> a</c><00:00:13.400><c> lot</c><00:00:13.639><c> of</c><00:00:14.639><c> what</c>

00:00:14.749 --> 00:00:14.759 align:start position:0%
weights and biases I built a lot of what
 

00:00:14.759 --> 00:00:16.830 align:start position:0%
weights and biases I built a lot of what
I<00:00:14.879><c> would</c><00:00:15.040><c> call</c><00:00:15.280><c> Enterprise</c><00:00:15.839><c> tools</c><00:00:16.359><c> myself</c>

00:00:16.830 --> 00:00:16.840 align:start position:0%
I would call Enterprise tools myself
 

00:00:16.840 --> 00:00:20.830 align:start position:0%
I would call Enterprise tools myself
gluing<00:00:17.320><c> things</c><00:00:17.760><c> together</c><00:00:18.760><c> and</c><00:00:19.439><c> um</c><00:00:20.439><c> you</c><00:00:20.560><c> know</c><00:00:20.760><c> I</c>

00:00:20.830 --> 00:00:20.840 align:start position:0%
gluing things together and um you know I
 

00:00:20.840 --> 00:00:23.750 align:start position:0%
gluing things together and um you know I
know<00:00:21.080><c> that</c><00:00:21.279><c> you</c><00:00:22.039><c> have</c><00:00:23.039><c> created</c><00:00:23.400><c> a</c><00:00:23.519><c> lot</c><00:00:23.640><c> of</c>

00:00:23.750 --> 00:00:23.760 align:start position:0%
know that you have created a lot of
 

00:00:23.760 --> 00:00:27.630 align:start position:0%
know that you have created a lot of
features<00:00:24.320><c> now</c><00:00:25.080><c> that</c><00:00:26.039><c> uh</c><00:00:26.160><c> make</c><00:00:26.320><c> it</c><00:00:26.519><c> really</c><00:00:26.800><c> easy</c>

00:00:27.630 --> 00:00:27.640 align:start position:0%
features now that uh make it really easy
 

00:00:27.640 --> 00:00:29.269 align:start position:0%
features now that uh make it really easy
for<00:00:27.960><c> people</c><00:00:28.279><c> so</c><00:00:28.480><c> they</c><00:00:28.560><c> don't</c><00:00:28.760><c> have</c><00:00:28.880><c> to</c><00:00:29.119><c> like</c>

00:00:29.269 --> 00:00:29.279 align:start position:0%
for people so they don't have to like
 

00:00:29.279 --> 00:00:30.830 align:start position:0%
for people so they don't have to like
make<00:00:29.439><c> their</c><00:00:29.560><c> own</c><00:00:29.720><c> to</c>

00:00:30.830 --> 00:00:30.840 align:start position:0%
make their own to
 

00:00:30.840 --> 00:00:32.870 align:start position:0%
make their own to
you<00:00:30.960><c> know</c><00:00:31.119><c> they</c><00:00:31.240><c> can</c><00:00:31.439><c> just</c><00:00:31.800><c> have</c><00:00:32.079><c> like</c><00:00:32.399><c> one</c><00:00:32.719><c> or</c>

00:00:32.870 --> 00:00:32.880 align:start position:0%
you know they can just have like one or
 

00:00:32.880 --> 00:00:34.670 align:start position:0%
you know they can just have like one or
two<00:00:33.040><c> lines</c><00:00:33.280><c> of</c><00:00:33.440><c> code</c><00:00:33.680><c> and</c><00:00:33.879><c> Like</c><00:00:34.000><c> Glue</c><00:00:34.320><c> things</c>

00:00:34.670 --> 00:00:34.680 align:start position:0%
two lines of code and Like Glue things
 

00:00:34.680 --> 00:00:36.270 align:start position:0%
two lines of code and Like Glue things
together<00:00:35.239><c> so</c><00:00:35.399><c> can</c><00:00:35.480><c> you</c><00:00:35.600><c> talk</c><00:00:35.760><c> a</c><00:00:35.879><c> little</c><00:00:36.040><c> bit</c>

00:00:36.270 --> 00:00:36.280 align:start position:0%
together so can you talk a little bit
 

00:00:36.280 --> 00:00:38.950 align:start position:0%
together so can you talk a little bit
about<00:00:36.960><c> like</c><00:00:37.120><c> some</c><00:00:37.280><c> of</c><00:00:37.440><c> those</c><00:00:37.680><c> Enterprise</c>

00:00:38.950 --> 00:00:38.960 align:start position:0%
about like some of those Enterprise
 

00:00:38.960 --> 00:00:41.270 align:start position:0%
about like some of those Enterprise
features<00:00:39.960><c> uh</c><00:00:40.079><c> are</c><00:00:40.360><c> the</c><00:00:40.480><c> some</c><00:00:40.600><c> of</c><00:00:40.719><c> the</c><00:00:40.879><c> notable</c>

00:00:41.270 --> 00:00:41.280 align:start position:0%
features uh are the some of the notable
 

00:00:41.280 --> 00:00:43.229 align:start position:0%
features uh are the some of the notable
ones<00:00:41.600><c> that</c><00:00:41.719><c> come</c><00:00:41.840><c> to</c><00:00:42.000><c> your</c><00:00:42.160><c> mind</c><00:00:42.480><c> and</c><00:00:43.039><c> what</c>

00:00:43.229 --> 00:00:43.239 align:start position:0%
ones that come to your mind and what
 

00:00:43.239 --> 00:00:44.990 align:start position:0%
ones that come to your mind and what
what's<00:00:43.559><c> like</c><00:00:43.719><c> really</c><00:00:44.039><c> popular</c><00:00:44.520><c> and</c><00:00:44.760><c> and</c>

00:00:44.990 --> 00:00:45.000 align:start position:0%
what's like really popular and and
 

00:00:45.000 --> 00:00:46.830 align:start position:0%
what's like really popular and and
what's<00:00:45.160><c> on</c><00:00:45.480><c> what's</c><00:00:45.600><c> on</c><00:00:45.760><c> your</c>

00:00:46.830 --> 00:00:46.840 align:start position:0%
what's on what's on your
 

00:00:46.840 --> 00:00:50.229 align:start position:0%
what's on what's on your
mind<00:00:47.840><c> yeah</c><00:00:48.039><c> sure</c><00:00:48.800><c> um</c><00:00:49.079><c> wow</c><00:00:49.399><c> where</c><00:00:49.840><c> where</c><00:00:50.000><c> to</c>

00:00:50.229 --> 00:00:50.239 align:start position:0%
mind yeah sure um wow where where to
 

00:00:50.239 --> 00:00:52.869 align:start position:0%
mind yeah sure um wow where where to
begin<00:00:51.000><c> uh</c><00:00:51.120><c> there's</c><00:00:51.480><c> a</c><00:00:51.640><c> whole</c><00:00:51.840><c> host</c><00:00:52.160><c> of</c><00:00:52.480><c> of</c><00:00:52.680><c> good</c>

00:00:52.869 --> 00:00:52.879 align:start position:0%
begin uh there's a whole host of of good
 

00:00:52.879 --> 00:00:55.670 align:start position:0%
begin uh there's a whole host of of good
stuff<00:00:53.160><c> but</c><00:00:53.320><c> I</c><00:00:53.440><c> think</c><00:00:54.320><c> um</c><00:00:54.800><c> one</c><00:00:54.920><c> of</c><00:00:55.079><c> the</c><00:00:55.280><c> one</c><00:00:55.559><c> one</c>

00:00:55.670 --> 00:00:55.680 align:start position:0%
stuff but I think um one of the one one
 

00:00:55.680 --> 00:00:57.590 align:start position:0%
stuff but I think um one of the one one
of<00:00:55.879><c> the</c><00:00:56.120><c> I</c><00:00:56.199><c> think</c><00:00:56.440><c> most</c><00:00:56.680><c> important</c><00:00:57.000><c> ones</c><00:00:57.239><c> to</c>

00:00:57.590 --> 00:00:57.600 align:start position:0%
of the I think most important ones to
 

00:00:57.600 --> 00:00:59.630 align:start position:0%
of the I think most important ones to
bring<00:00:57.879><c> up</c><00:00:58.199><c> that</c><00:00:58.879><c> folks</c><00:00:59.160><c> who</c><00:00:59.280><c> are</c><00:00:59.440><c> already</c>

00:00:59.630 --> 00:00:59.640 align:start position:0%
bring up that folks who are already
 

00:00:59.640 --> 00:01:00.549 align:start position:0%
bring up that folks who are already
using

00:01:00.549 --> 00:01:00.559 align:start position:0%
using
 

00:01:00.559 --> 00:01:03.270 align:start position:0%
using
um<00:01:01.000><c> artifacts</c><00:01:02.000><c> might</c><00:01:02.199><c> already</c><00:01:02.480><c> be</c><00:01:02.719><c> familiar</c>

00:01:03.270 --> 00:01:03.280 align:start position:0%
um artifacts might already be familiar
 

00:01:03.280 --> 00:01:06.230 align:start position:0%
um artifacts might already be familiar
with<00:01:03.920><c> um</c><00:01:04.199><c> and</c><00:01:04.680><c> it's</c><00:01:04.879><c> less</c><00:01:05.320><c> specific</c><00:01:05.799><c> to</c><00:01:06.000><c> the</c>

00:01:06.230 --> 00:01:06.240 align:start position:0%
with um and it's less specific to the
 

00:01:06.240 --> 00:01:11.030 align:start position:0%
with um and it's less specific to the
the<00:01:06.360><c> model</c><00:01:06.920><c> registry</c><00:01:08.040><c> um</c><00:01:09.040><c> but</c><00:01:09.720><c> you</c><00:01:09.840><c> know</c><00:01:10.640><c> we're</c>

00:01:11.030 --> 00:01:11.040 align:start position:0%
the model registry um but you know we're
 

00:01:11.040 --> 00:01:12.870 align:start position:0%
the model registry um but you know we're
talking<00:01:11.320><c> about</c><00:01:11.479><c> Enterprise</c><00:01:12.000><c> teams</c><00:01:12.640><c> that</c>

00:01:12.870 --> 00:01:12.880 align:start position:0%
talking about Enterprise teams that
 

00:01:12.880 --> 00:01:15.310 align:start position:0%
talking about Enterprise teams that
typically<00:01:13.439><c> might</c><00:01:13.680><c> work</c><00:01:14.240><c> they</c><00:01:14.400><c> might</c><00:01:14.680><c> have</c><00:01:15.200><c> all</c>

00:01:15.310 --> 00:01:15.320 align:start position:0%
typically might work they might have all
 

00:01:15.320 --> 00:01:18.149 align:start position:0%
typically might work they might have all
of<00:01:15.479><c> their</c><00:01:15.960><c> models</c><00:01:16.520><c> and</c><00:01:16.720><c> data</c><00:01:17.040><c> sets</c><00:01:17.280><c> stored</c><00:01:18.000><c> um</c>

00:01:18.149 --> 00:01:18.159 align:start position:0%
of their models and data sets stored um
 

00:01:18.159 --> 00:01:20.950 align:start position:0%
of their models and data sets stored um
somewhere<00:01:18.640><c> else</c><00:01:19.640><c> and</c><00:01:20.280><c> if</c><00:01:20.560><c> you've</c><00:01:20.799><c> been</c>

00:01:20.950 --> 00:01:20.960 align:start position:0%
somewhere else and if you've been
 

00:01:20.960 --> 00:01:22.950 align:start position:0%
somewhere else and if you've been
following<00:01:21.400><c> this</c><00:01:21.600><c> course</c><00:01:22.200><c> and</c><00:01:22.439><c> seeing</c><00:01:22.880><c> you</c>

00:01:22.950 --> 00:01:22.960 align:start position:0%
following this course and seeing you
 

00:01:22.960 --> 00:01:24.590 align:start position:0%
following this course and seeing you
know<00:01:23.280><c> the</c><00:01:23.360><c> demos</c><00:01:23.680><c> on</c><00:01:23.799><c> model</c><00:01:24.119><c> registry</c><00:01:24.520><c> and</c>

00:01:24.590 --> 00:01:24.600 align:start position:0%
know the demos on model registry and
 

00:01:24.600 --> 00:01:26.350 align:start position:0%
know the demos on model registry and
you're<00:01:24.799><c> like</c><00:01:25.040><c> that's</c><00:01:25.320><c> great</c><00:01:25.720><c> but</c><00:01:26.119><c> you</c><00:01:26.240><c> know</c>

00:01:26.350 --> 00:01:26.360 align:start position:0%
you're like that's great but you know
 

00:01:26.360 --> 00:01:28.230 align:start position:0%
you're like that's great but you know
we're<00:01:26.520><c> already</c><00:01:26.759><c> storing</c><00:01:27.400><c> all</c><00:01:27.600><c> of</c><00:01:27.720><c> our</c><00:01:27.920><c> model</c>

00:01:28.230 --> 00:01:28.240 align:start position:0%
we're already storing all of our model
 

00:01:28.240 --> 00:01:31.469 align:start position:0%
we're already storing all of our model
files<00:01:28.680><c> in</c><00:01:28.920><c> S3</c><00:01:30.000><c> um</c><00:01:30.159><c> how</c><00:01:30.320><c> is</c><00:01:30.479><c> this</c><00:01:30.720><c> relevant</c><00:01:31.280><c> to</c>

00:01:31.469 --> 00:01:31.479 align:start position:0%
files in S3 um how is this relevant to
 

00:01:31.479 --> 00:01:33.270 align:start position:0%
files in S3 um how is this relevant to
me<00:01:31.759><c> I</c><00:01:31.840><c> don't</c><00:01:32.040><c> really</c><00:01:32.320><c> want</c><00:01:32.520><c> to</c><00:01:32.960><c> you</c><00:01:33.079><c> know</c>

00:01:33.270 --> 00:01:33.280 align:start position:0%
me I don't really want to you know
 

00:01:33.280 --> 00:01:36.069 align:start position:0%
me I don't really want to you know
download<00:01:33.759><c> them</c><00:01:34.040><c> move</c><00:01:34.360><c> them</c><00:01:34.560><c> over</c><00:01:35.360><c> uh</c><00:01:35.520><c> to</c><00:01:35.960><c> kind</c>

00:01:36.069 --> 00:01:36.079 align:start position:0%
download them move them over uh to kind
 

00:01:36.079 --> 00:01:37.990 align:start position:0%
download them move them over uh to kind
of<00:01:36.200><c> locally</c><00:01:36.640><c> upload</c><00:01:37.000><c> them</c><00:01:37.119><c> to</c><00:01:37.280><c> weights</c><00:01:37.479><c> and</c>

00:01:37.990 --> 00:01:38.000 align:start position:0%
of locally upload them to weights and
 

00:01:38.000 --> 00:01:40.590 align:start position:0%
of locally upload them to weights and
biases<00:01:39.000><c> um</c><00:01:39.479><c> so</c><00:01:39.759><c> I'm</c><00:01:39.880><c> going</c><00:01:40.000><c> to</c><00:01:40.119><c> pull</c><00:01:40.320><c> up</c><00:01:40.479><c> kind</c>

00:01:40.590 --> 00:01:40.600 align:start position:0%
biases um so I'm going to pull up kind
 

00:01:40.600 --> 00:01:43.389 align:start position:0%
biases um so I'm going to pull up kind
of<00:01:40.840><c> this</c><00:01:41.159><c> this</c><00:01:42.000><c> uh</c><00:01:42.200><c> documentation</c><00:01:42.960><c> here</c><00:01:43.200><c> which</c>

00:01:43.389 --> 00:01:43.399 align:start position:0%
of this this uh documentation here which
 

00:01:43.399 --> 00:01:46.389 align:start position:0%
of this this uh documentation here which
talks<00:01:43.720><c> through</c><00:01:44.640><c> how</c><00:01:44.880><c> you</c><00:01:45.040><c> can</c><00:01:45.360><c> actually</c><00:01:46.240><c> um</c>

00:01:46.389 --> 00:01:46.399 align:start position:0%
talks through how you can actually um
 

00:01:46.399 --> 00:01:48.990 align:start position:0%
talks through how you can actually um
track<00:01:46.799><c> files</c><00:01:47.240><c> that</c><00:01:47.399><c> live</c><00:01:48.079><c> not</c><00:01:48.360><c> necessarily</c><00:01:48.840><c> in</c>

00:01:48.990 --> 00:01:49.000 align:start position:0%
track files that live not necessarily in
 

00:01:49.000 --> 00:01:52.270 align:start position:0%
track files that live not necessarily in
weights<00:01:49.240><c> and</c><00:01:49.399><c> biases</c><00:01:50.399><c> um</c><00:01:50.640><c> but</c><00:01:51.040><c> externally</c><00:01:51.759><c> so</c>

00:01:52.270 --> 00:01:52.280 align:start position:0%
weights and biases um but externally so
 

00:01:52.280 --> 00:01:54.389 align:start position:0%
weights and biases um but externally so
you<00:01:52.399><c> know</c><00:01:52.719><c> the</c><00:01:52.840><c> most</c><00:01:53.159><c> common</c><00:01:53.479><c> examples</c><00:01:54.000><c> we</c><00:01:54.159><c> see</c>

00:01:54.389 --> 00:01:54.399 align:start position:0%
you know the most common examples we see
 

00:01:54.399 --> 00:01:56.350 align:start position:0%
you know the most common examples we see
customers<00:01:54.920><c> working</c><00:01:55.280><c> with</c><00:01:55.479><c> are</c><00:01:56.079><c> they</c><00:01:56.200><c> have</c>

00:01:56.350 --> 00:01:56.360 align:start position:0%
customers working with are they have
 

00:01:56.360 --> 00:01:58.630 align:start position:0%
customers working with are they have
their<00:01:56.520><c> models</c><00:01:56.880><c> maybe</c><00:01:57.119><c> saved</c><00:01:57.439><c> in</c><00:01:57.600><c> S3</c><00:01:58.119><c> or</c><00:01:58.360><c> any</c><00:01:58.520><c> of</c>

00:01:58.630 --> 00:01:58.640 align:start position:0%
their models maybe saved in S3 or any of
 

00:01:58.640 --> 00:02:02.510 align:start position:0%
their models maybe saved in S3 or any of
the<00:01:58.840><c> other</c><00:01:59.240><c> major</c><00:01:59.520><c> cloud</c><00:02:00.280><c> providers</c><00:02:01.280><c> um</c><00:02:02.240><c> like</c>

00:02:02.510 --> 00:02:02.520 align:start position:0%
the other major cloud providers um like
 

00:02:02.520 --> 00:02:06.109 align:start position:0%
the other major cloud providers um like
Azure<00:02:03.000><c> or</c><00:02:03.159><c> in</c><00:02:03.320><c> GCS</c><00:02:04.280><c> and</c><00:02:05.119><c> um</c><00:02:05.280><c> you</c><00:02:05.399><c> can</c><00:02:05.680><c> actually</c>

00:02:06.109 --> 00:02:06.119 align:start position:0%
Azure or in GCS and um you can actually
 

00:02:06.119 --> 00:02:08.630 align:start position:0%
Azure or in GCS and um you can actually
just<00:02:07.119><c> similar</c><00:02:07.560><c> to</c><00:02:07.719><c> like</c><00:02:07.880><c> how</c><00:02:08.039><c> you</c><00:02:08.119><c> would</c><00:02:08.319><c> add</c><00:02:08.440><c> a</c>

00:02:08.630 --> 00:02:08.640 align:start position:0%
just similar to like how you would add a
 

00:02:08.640 --> 00:02:10.589 align:start position:0%
just similar to like how you would add a
file<00:02:09.000><c> to</c><00:02:09.200><c> the</c><00:02:09.360><c> artifact</c><00:02:09.959><c> if</c><00:02:10.080><c> we</c><00:02:10.200><c> take</c><00:02:10.360><c> a</c><00:02:10.479><c> look</c>

00:02:10.589 --> 00:02:10.599 align:start position:0%
file to the artifact if we take a look
 

00:02:10.599 --> 00:02:13.589 align:start position:0%
file to the artifact if we take a look
at<00:02:10.720><c> this</c><00:02:10.879><c> code</c><00:02:11.120><c> snippet</c><00:02:11.640><c> here</c><00:02:12.640><c> um</c><00:02:13.239><c> you</c><00:02:13.400><c> can</c>

00:02:13.589 --> 00:02:13.599 align:start position:0%
at this code snippet here um you can
 

00:02:13.599 --> 00:02:16.470 align:start position:0%
at this code snippet here um you can
have<00:02:13.760><c> your</c><00:02:13.920><c> artifact</c><00:02:14.440><c> store</c><00:02:14.879><c> a</c><00:02:15.200><c> reference</c><00:02:15.959><c> to</c>

00:02:16.470 --> 00:02:16.480 align:start position:0%
have your artifact store a reference to
 

00:02:16.480 --> 00:02:19.190 align:start position:0%
have your artifact store a reference to
any<00:02:16.760><c> location</c><00:02:17.280><c> inside</c><00:02:17.599><c> a</c><00:02:17.760><c> bucket</c><00:02:18.640><c> um</c><00:02:18.760><c> so</c><00:02:19.040><c> that</c>

00:02:19.190 --> 00:02:19.200 align:start position:0%
any location inside a bucket um so that
 

00:02:19.200 --> 00:02:21.190 align:start position:0%
any location inside a bucket um so that
way<00:02:19.440><c> you're</c><00:02:19.680><c> not</c><00:02:19.959><c> kind</c><00:02:20.080><c> of</c><00:02:20.440><c> dealing</c><00:02:20.879><c> with</c>

00:02:21.190 --> 00:02:21.200 align:start position:0%
way you're not kind of dealing with
 

00:02:21.200 --> 00:02:23.190 align:start position:0%
way you're not kind of dealing with
moving<00:02:21.640><c> things</c><00:02:21.840><c> over</c><00:02:22.040><c> to</c><00:02:22.200><c> weights</c><00:02:22.440><c> and</c><00:02:22.560><c> biases</c>

00:02:23.190 --> 00:02:23.200 align:start position:0%
moving things over to weights and biases
 

00:02:23.200 --> 00:02:25.430 align:start position:0%
moving things over to weights and biases
and<00:02:23.440><c> you're</c><00:02:23.599><c> leaving</c><00:02:24.400><c> everything</c><00:02:24.959><c> inside</c><00:02:25.280><c> the</c>

00:02:25.430 --> 00:02:25.440 align:start position:0%
and you're leaving everything inside the
 

00:02:25.440 --> 00:02:28.430 align:start position:0%
and you're leaving everything inside the
buckets<00:02:25.879><c> as</c><00:02:26.200><c> is</c><00:02:26.920><c> but</c><00:02:27.080><c> you're</c><00:02:27.680><c> kind</c><00:02:27.800><c> of</c><00:02:27.959><c> adding</c>

00:02:28.430 --> 00:02:28.440 align:start position:0%
buckets as is but you're kind of adding
 

00:02:28.440 --> 00:02:31.430 align:start position:0%
buckets as is but you're kind of adding
pointers<00:02:29.319><c> um</c><00:02:29.440><c> to</c><00:02:29.599><c> where</c><00:02:30.200><c> where</c><00:02:30.440><c> those</c><00:02:30.680><c> live</c><00:02:31.200><c> um</c>

00:02:31.430 --> 00:02:31.440 align:start position:0%
pointers um to where where those live um
 

00:02:31.440 --> 00:02:34.270 align:start position:0%
pointers um to where where those live um
and<00:02:31.560><c> it's</c><00:02:32.319><c> instead</c><00:02:32.560><c> of</c><00:02:32.640><c> doing</c><00:02:32.879><c> ad</c><00:02:33.160><c> file</c><00:02:33.959><c> um</c><00:02:34.080><c> or</c>

00:02:34.270 --> 00:02:34.280 align:start position:0%
and it's instead of doing ad file um or
 

00:02:34.280 --> 00:02:35.869 align:start position:0%
and it's instead of doing ad file um or
add<00:02:34.519><c> directory</c><00:02:35.040><c> then</c><00:02:35.160><c> you</c><00:02:35.239><c> would</c><00:02:35.480><c> directly</c>

00:02:35.869 --> 00:02:35.879 align:start position:0%
add directory then you would directly
 

00:02:35.879 --> 00:02:39.309 align:start position:0%
add directory then you would directly
just<00:02:36.080><c> being</c><00:02:36.519><c> be</c><00:02:37.319><c> adding</c><00:02:37.800><c> a</c><00:02:37.959><c> reference</c><00:02:38.440><c> here</c><00:02:39.200><c> um</c>

00:02:39.309 --> 00:02:39.319 align:start position:0%
just being be adding a reference here um
 

00:02:39.319 --> 00:02:41.949 align:start position:0%
just being be adding a reference here um
so<00:02:39.560><c> depending</c><00:02:40.000><c> on</c><00:02:40.440><c> you</c><00:02:40.560><c> know</c><00:02:40.840><c> which</c><00:02:41.599><c> type</c><00:02:41.800><c> of</c>

00:02:41.949 --> 00:02:41.959 align:start position:0%
so depending on you know which type of
 

00:02:41.959 --> 00:02:44.030 align:start position:0%
so depending on you know which type of
Provider<00:02:42.360><c> you're</c><00:02:42.560><c> working</c><00:02:43.000><c> with</c><00:02:43.440><c> and</c><00:02:43.560><c> we</c><00:02:43.760><c> also</c>

00:02:44.030 --> 00:02:44.040 align:start position:0%
Provider you're working with and we also
 

00:02:44.040 --> 00:02:47.869 align:start position:0%
Provider you're working with and we also
work<00:02:44.319><c> with</c><00:02:44.720><c> NFS</c><00:02:45.720><c> uh</c><00:02:45.879><c> drives</c><00:02:46.239><c> as</c><00:02:46.440><c> well</c><00:02:47.400><c> um</c><00:02:47.720><c> we</c>

00:02:47.869 --> 00:02:47.879 align:start position:0%
work with NFS uh drives as well um we
 

00:02:47.879 --> 00:02:49.509 align:start position:0%
work with NFS uh drives as well um we
have<00:02:48.040><c> some</c><00:02:48.280><c> some</c><00:02:48.440><c> great</c><00:02:48.720><c> guides</c><00:02:49.000><c> which</c>

00:02:49.509 --> 00:02:49.519 align:start position:0%
have some some great guides which
 

00:02:49.519 --> 00:02:52.190 align:start position:0%
have some some great guides which
specifically<00:02:50.239><c> walks</c><00:02:50.599><c> through</c><00:02:50.959><c> this</c><00:02:51.519><c> um</c><00:02:51.959><c> but</c><00:02:52.120><c> I</c>

00:02:52.190 --> 00:02:52.200 align:start position:0%
specifically walks through this um but I
 

00:02:52.200 --> 00:02:53.750 align:start position:0%
specifically walks through this um but I
would<00:02:52.360><c> take</c><00:02:52.480><c> a</c><00:02:52.599><c> look</c><00:02:52.720><c> at</c><00:02:52.840><c> the</c><00:02:53.000><c> documentation</c>

00:02:53.750 --> 00:02:53.760 align:start position:0%
would take a look at the documentation
 

00:02:53.760 --> 00:02:57.949 align:start position:0%
would take a look at the documentation
and<00:02:54.480><c> that's</c><00:02:54.720><c> more</c><00:02:54.920><c> of</c><00:02:55.120><c> a</c><00:02:55.599><c> a</c><00:02:55.800><c> high</c><00:02:56.040><c> level</c><00:02:57.040><c> um</c><00:02:57.440><c> hey</c>

00:02:57.949 --> 00:02:57.959 align:start position:0%
and that's more of a a high level um hey
 

00:02:57.959 --> 00:02:59.470 align:start position:0%
and that's more of a a high level um hey
you<00:02:58.080><c> don't</c><00:02:58.239><c> need</c><00:02:58.400><c> to</c><00:02:58.519><c> be</c><00:02:58.640><c> moving</c><00:02:59.000><c> stuff</c><00:02:59.239><c> out</c><00:02:59.360><c> of</c>

00:02:59.470 --> 00:02:59.480 align:start position:0%
you don't need to be moving stuff out of
 

00:02:59.480 --> 00:03:02.190 align:start position:0%
you don't need to be moving stuff out of
your<00:02:59.599><c> butt</c><00:02:59.840><c> buckets</c><00:03:00.640><c> um</c><00:03:01.239><c> the</c><00:03:01.440><c> the</c><00:03:01.599><c> advantages</c>

00:03:02.190 --> 00:03:02.200 align:start position:0%
your butt buckets um the the advantages
 

00:03:02.200 --> 00:03:05.470 align:start position:0%
your butt buckets um the the advantages
of<00:03:02.680><c> still</c><00:03:03.080><c> using</c><00:03:03.920><c> um</c><00:03:04.040><c> weights</c><00:03:04.319><c> and</c><00:03:04.480><c> biases</c><00:03:05.120><c> to</c>

00:03:05.470 --> 00:03:05.480 align:start position:0%
of still using um weights and biases to
 

00:03:05.480 --> 00:03:07.830 align:start position:0%
of still using um weights and biases to
kind<00:03:05.599><c> of</c><00:03:05.799><c> Mark</c><00:03:06.760><c> um</c><00:03:06.959><c> your</c><00:03:07.200><c> your</c><00:03:07.319><c> models</c><00:03:07.640><c> and</c>

00:03:07.830 --> 00:03:07.840 align:start position:0%
kind of Mark um your your models and
 

00:03:07.840 --> 00:03:10.229 align:start position:0%
kind of Mark um your your models and
linking<00:03:08.120><c> them</c><00:03:08.239><c> to</c><00:03:08.400><c> the</c><00:03:08.560><c> registry</c><00:03:09.440><c> is</c><00:03:10.000><c> almost</c>

00:03:10.229 --> 00:03:10.239 align:start position:0%
linking them to the registry is almost
 

00:03:10.239 --> 00:03:12.949 align:start position:0%
linking them to the registry is almost
to<00:03:10.400><c> be</c><00:03:10.599><c> this</c><00:03:10.720><c> layer</c><00:03:11.040><c> on</c><00:03:11.319><c> top</c><00:03:11.560><c> of</c><00:03:12.000><c> let's</c><00:03:12.200><c> say</c><00:03:12.519><c> s</c>

00:03:12.949 --> 00:03:12.959 align:start position:0%
to be this layer on top of let's say s
 

00:03:12.959 --> 00:03:16.270 align:start position:0%
to be this layer on top of let's say s
S3<00:03:13.959><c> um</c><00:03:14.879><c> where</c><00:03:15.120><c> you're</c><00:03:15.360><c> able</c><00:03:15.680><c> to</c><00:03:15.879><c> get</c><00:03:16.040><c> all</c><00:03:16.159><c> of</c>

00:03:16.270 --> 00:03:16.280 align:start position:0%
S3 um where you're able to get all of
 

00:03:16.280 --> 00:03:18.589 align:start position:0%
S3 um where you're able to get all of
the<00:03:16.400><c> features</c><00:03:16.840><c> around</c><00:03:17.319><c> automatic</c><00:03:17.840><c> versioning</c>

00:03:18.589 --> 00:03:18.599 align:start position:0%
the features around automatic versioning
 

00:03:18.599 --> 00:03:21.149 align:start position:0%
the features around automatic versioning
and<00:03:18.959><c> lineage</c><00:03:19.680><c> so</c><00:03:19.879><c> that</c><00:03:20.120><c> kind</c><00:03:20.239><c> of</c><00:03:20.400><c> graph</c><00:03:20.799><c> you</c><00:03:21.040><c> we</c>

00:03:21.149 --> 00:03:21.159 align:start position:0%
and lineage so that kind of graph you we
 

00:03:21.159 --> 00:03:24.470 align:start position:0%
and lineage so that kind of graph you we
talked<00:03:21.440><c> about</c><00:03:22.200><c> um</c><00:03:22.360><c> by</c><00:03:22.519><c> using</c><00:03:22.920><c> the</c><00:03:23.120><c> the</c><00:03:23.480><c> apis</c>

00:03:24.470 --> 00:03:24.480 align:start position:0%
talked about um by using the the apis
 

00:03:24.480 --> 00:03:26.589 align:start position:0%
talked about um by using the the apis
where<00:03:25.040><c> the</c><00:03:25.440><c> the</c><00:03:25.599><c> storage</c><00:03:26.000><c> the</c><00:03:26.120><c> underlying</c>

00:03:26.589 --> 00:03:26.599 align:start position:0%
where the the storage the underlying
 

00:03:26.599 --> 00:03:27.949 align:start position:0%
where the the storage the underlying
storage<00:03:26.920><c> is</c><00:03:27.040><c> still</c><00:03:27.239><c> going</c><00:03:27.360><c> to</c><00:03:27.480><c> be</c><00:03:27.640><c> kind</c><00:03:27.760><c> of</c>

00:03:27.949 --> 00:03:27.959 align:start position:0%
storage is still going to be kind of
 

00:03:27.959 --> 00:03:31.830 align:start position:0%
storage is still going to be kind of
your<00:03:28.560><c> um</c><00:03:28.959><c> cloud</c><00:03:29.319><c> provider</c>

00:03:31.830 --> 00:03:31.840 align:start position:0%
 
 

00:03:31.840 --> 00:03:34.789 align:start position:0%
 
cool<00:03:32.319><c> and</c><00:03:32.519><c> then</c><00:03:33.120><c> the</c><00:03:33.319><c> other</c><00:03:33.640><c> thing</c><00:03:34.120><c> I</c><00:03:34.239><c> wanted</c>

00:03:34.789 --> 00:03:34.799 align:start position:0%
cool and then the other thing I wanted
 

00:03:34.799 --> 00:03:38.390 align:start position:0%
cool and then the other thing I wanted
you<00:03:34.920><c> know</c><00:03:35.239><c> yeah</c><00:03:35.680><c> on</c><00:03:35.959><c> the</c><00:03:36.840><c> the</c><00:03:37.000><c> story</c><00:03:37.360><c> of</c><00:03:37.879><c> um</c>

00:03:38.390 --> 00:03:38.400 align:start position:0%
you know yeah on the the story of um
 

00:03:38.400 --> 00:03:40.509 align:start position:0%
you know yeah on the the story of um
important<00:03:38.879><c> features</c><00:03:39.319><c> to</c><00:03:39.519><c> call</c><00:03:39.720><c> out</c><00:03:39.879><c> for</c>

00:03:40.509 --> 00:03:40.519 align:start position:0%
important features to call out for
 

00:03:40.519 --> 00:03:43.509 align:start position:0%
important features to call out for
Enterprise<00:03:41.519><c> um</c><00:03:41.840><c> customers</c><00:03:42.799><c> I</c><00:03:42.920><c> also</c><00:03:43.120><c> wanted</c><00:03:43.360><c> to</c>

00:03:43.509 --> 00:03:43.519 align:start position:0%
Enterprise um customers I also wanted to
 

00:03:43.519 --> 00:03:47.229 align:start position:0%
Enterprise um customers I also wanted to
talk<00:03:43.720><c> through</c><00:03:44.560><c> um</c><00:03:45.040><c> protected</c><00:03:46.000><c> aliases</c><00:03:47.000><c> um</c><00:03:47.120><c> so</c>

00:03:47.229 --> 00:03:47.239 align:start position:0%
talk through um protected aliases um so
 

00:03:47.239 --> 00:03:49.229 align:start position:0%
talk through um protected aliases um so
we<00:03:47.360><c> spent</c><00:03:47.599><c> a</c><00:03:47.720><c> good</c><00:03:47.879><c> amount</c><00:03:48.080><c> of</c><00:03:48.319><c> time</c><00:03:48.599><c> talking</c>

00:03:49.229 --> 00:03:49.239 align:start position:0%
we spent a good amount of time talking
 

00:03:49.239 --> 00:03:52.470 align:start position:0%
we spent a good amount of time talking
about<00:03:49.959><c> aliases</c><00:03:50.959><c> and</c><00:03:51.519><c> you</c><00:03:51.680><c> know</c><00:03:51.959><c> how</c><00:03:52.159><c> they're</c>

00:03:52.470 --> 00:03:52.480 align:start position:0%
about aliases and you know how they're
 

00:03:52.480 --> 00:03:55.149 align:start position:0%
about aliases and you know how they're
relevant<00:03:52.879><c> in</c><00:03:53.000><c> the</c><00:03:53.120><c> model</c><00:03:53.680><c> registry</c><00:03:54.200><c> to</c><00:03:54.439><c> track</c>

00:03:55.149 --> 00:03:55.159 align:start position:0%
relevant in the model registry to track
 

00:03:55.159 --> 00:03:59.190 align:start position:0%
relevant in the model registry to track
uh<00:03:55.480><c> the</c><00:03:55.640><c> life</c><00:03:56.000><c> cycle</c><00:03:56.840><c> um</c><00:03:57.200><c> of</c><00:03:57.760><c> uh</c><00:03:58.280><c> kind</c><00:03:58.400><c> of</c><00:03:58.760><c> which</c>

00:03:59.190 --> 00:03:59.200 align:start position:0%
uh the life cycle um of uh kind of which
 

00:03:59.200 --> 00:04:01.550 align:start position:0%
uh the life cycle um of uh kind of which
version<00:03:59.959><c> is</c><00:04:00.200><c> in</c><00:04:00.439><c> which</c><00:04:00.680><c> state</c><00:04:00.959><c> of</c><00:04:01.120><c> the</c><00:04:01.200><c> model</c>

00:04:01.550 --> 00:04:01.560 align:start position:0%
version is in which state of the model
 

00:04:01.560 --> 00:04:06.830 align:start position:0%
version is in which state of the model
life<00:04:02.000><c> cycle</c><00:04:03.000><c> um</c><00:04:03.280><c> and</c><00:04:03.400><c> so</c><00:04:03.640><c> this</c><00:04:03.920><c> concept</c><00:04:04.680><c> of</c><00:04:05.840><c> um</c>

00:04:06.830 --> 00:04:06.840 align:start position:0%
life cycle um and so this concept of um
 

00:04:06.840 --> 00:04:09.630 align:start position:0%
life cycle um and so this concept of um
protected<00:04:07.319><c> aliases</c><00:04:07.959><c> is</c><00:04:08.120><c> that</c><00:04:08.239><c> you</c><00:04:08.360><c> can</c><00:04:08.560><c> Mark</c><00:04:09.400><c> a</c>

00:04:09.630 --> 00:04:09.640 align:start position:0%
protected aliases is that you can Mark a
 

00:04:09.640 --> 00:04:14.390 align:start position:0%
protected aliases is that you can Mark a
few<00:04:10.560><c> aliases</c><00:04:11.519><c> as</c><00:04:11.799><c> being</c><00:04:12.799><c> kind</c><00:04:12.959><c> of</c><00:04:13.159><c> admin</c><00:04:13.680><c> only</c>

00:04:14.390 --> 00:04:14.400 align:start position:0%
few aliases as being kind of admin only
 

00:04:14.400 --> 00:04:17.390 align:start position:0%
few aliases as being kind of admin only
as<00:04:14.560><c> in</c><00:04:15.040><c> not</c><00:04:15.319><c> everyone</c><00:04:16.079><c> will</c><00:04:16.280><c> be</c><00:04:16.440><c> able</c><00:04:16.759><c> to</c><00:04:17.040><c> add</c>

00:04:17.390 --> 00:04:17.400 align:start position:0%
as in not everyone will be able to add
 

00:04:17.400 --> 00:04:21.430 align:start position:0%
as in not everyone will be able to add
the<00:04:17.560><c> Alias</c><00:04:18.320><c> um</c><00:04:18.600><c> production</c><00:04:19.400><c> or</c><00:04:20.239><c> evaluation</c><00:04:21.239><c> um</c>

00:04:21.430 --> 00:04:21.440 align:start position:0%
the Alias um production or evaluation um
 

00:04:21.440 --> 00:04:23.749 align:start position:0%
the Alias um production or evaluation um
and<00:04:21.600><c> it</c><00:04:21.880><c> typically</c><00:04:22.240><c> we</c><00:04:22.360><c> see</c><00:04:22.639><c> customers</c><00:04:23.199><c> using</c>

00:04:23.749 --> 00:04:23.759 align:start position:0%
and it typically we see customers using
 

00:04:23.759 --> 00:04:26.350 align:start position:0%
and it typically we see customers using
kind<00:04:23.880><c> of</c><00:04:24.040><c> a</c><00:04:24.240><c> key</c><00:04:24.840><c> set</c><00:04:25.280><c> of</c><00:04:25.759><c> you</c><00:04:25.880><c> know</c><00:04:26.240><c> the</c>

00:04:26.350 --> 00:04:26.360 align:start position:0%
kind of a key set of you know the
 

00:04:26.360 --> 00:04:29.749 align:start position:0%
kind of a key set of you know the
important<00:04:26.720><c> aliases</c><00:04:27.280><c> that</c><00:04:27.639><c> Mark</c><00:04:28.639><c> um</c><00:04:29.280><c> like</c>

00:04:29.749 --> 00:04:29.759 align:start position:0%
important aliases that Mark um like
 

00:04:29.759 --> 00:04:31.550 align:start position:0%
important aliases that Mark um like
production<00:04:30.360><c> that</c><00:04:30.560><c> not</c><00:04:30.840><c> everyone</c><00:04:31.199><c> should</c><00:04:31.400><c> be</c>

00:04:31.550 --> 00:04:31.560 align:start position:0%
production that not everyone should be
 

00:04:31.560 --> 00:04:34.150 align:start position:0%
production that not everyone should be
able<00:04:31.759><c> to</c><00:04:32.039><c> Pro</c><00:04:32.479><c> promote</c><00:04:32.759><c> a</c><00:04:32.919><c> model</c><00:04:33.240><c> to</c><00:04:33.560><c> to</c><00:04:33.759><c> this</c>

00:04:34.150 --> 00:04:34.160 align:start position:0%
able to Pro promote a model to to this
 

00:04:34.160 --> 00:04:36.590 align:start position:0%
able to Pro promote a model to to this
state<00:04:35.160><c> uh</c><00:04:35.320><c> and</c><00:04:35.639><c> why</c><00:04:35.759><c> is</c><00:04:35.919><c> this</c><00:04:36.120><c> important</c>

00:04:36.590 --> 00:04:36.600 align:start position:0%
state uh and why is this important
 

00:04:36.600 --> 00:04:37.909 align:start position:0%
state uh and why is this important
because<00:04:37.039><c> sometimes</c><00:04:37.280><c> you</c><00:04:37.400><c> might</c><00:04:37.680><c> have</c><00:04:37.800><c> an</c>

00:04:37.909 --> 00:04:37.919 align:start position:0%
because sometimes you might have an
 

00:04:37.919 --> 00:04:41.629 align:start position:0%
because sometimes you might have an
alias<00:04:38.479><c> like</c><00:04:39.240><c> production</c><00:04:40.240><c> um</c><00:04:41.160><c> that</c><00:04:41.440><c> is</c>

00:04:41.629 --> 00:04:41.639 align:start position:0%
alias like production um that is
 

00:04:41.639 --> 00:04:42.990 align:start position:0%
alias like production um that is
actually<00:04:41.880><c> going</c><00:04:42.000><c> to</c><00:04:42.199><c> kick</c><00:04:42.479><c> off</c><00:04:42.800><c> this</c>

00:04:42.990 --> 00:04:43.000 align:start position:0%
actually going to kick off this
 

00:04:43.000 --> 00:04:44.749 align:start position:0%
actually going to kick off this
automation<00:04:43.759><c> as</c><00:04:43.880><c> we</c><00:04:44.039><c> talked</c><00:04:44.320><c> about</c><00:04:44.560><c> you</c><00:04:44.639><c> can</c>

00:04:44.749 --> 00:04:44.759 align:start position:0%
automation as we talked about you can
 

00:04:44.759 --> 00:04:46.909 align:start position:0%
automation as we talked about you can
use<00:04:44.919><c> an</c><00:04:45.039><c> alias</c><00:04:45.440><c> to</c><00:04:45.680><c> kick</c><00:04:45.840><c> off</c><00:04:46.000><c> an</c><00:04:46.160><c> Automation</c>

00:04:46.909 --> 00:04:46.919 align:start position:0%
use an alias to kick off an Automation
 

00:04:46.919 --> 00:04:48.950 align:start position:0%
use an alias to kick off an Automation
and<00:04:47.000><c> so</c><00:04:47.199><c> you</c><00:04:47.320><c> want</c><00:04:47.440><c> to</c><00:04:47.600><c> make</c><00:04:47.759><c> sure</c><00:04:48.039><c> that</c><00:04:48.680><c> not</c>

00:04:48.950 --> 00:04:48.960 align:start position:0%
and so you want to make sure that not
 

00:04:48.960 --> 00:04:51.230 align:start position:0%
and so you want to make sure that not
everyone<00:04:49.320><c> in</c><00:04:49.440><c> the</c><00:04:49.600><c> team</c><00:04:50.080><c> is</c><00:04:50.400><c> you</c><00:04:50.520><c> know</c><00:04:50.680><c> adding</c>

00:04:51.230 --> 00:04:51.240 align:start position:0%
everyone in the team is you know adding
 

00:04:51.240 --> 00:04:54.270 align:start position:0%
everyone in the team is you know adding
this<00:04:51.560><c> production</c><00:04:52.400><c> uh</c><00:04:52.560><c> production</c><00:04:53.160><c> Alias</c><00:04:54.160><c> um</c>

00:04:54.270 --> 00:04:54.280 align:start position:0%
this production uh production Alias um
 

00:04:54.280 --> 00:04:56.189 align:start position:0%
this production uh production Alias um
so<00:04:54.440><c> that's</c><00:04:54.639><c> another</c><00:04:54.880><c> feature</c><00:04:55.240><c> to</c><00:04:55.400><c> call</c><00:04:55.639><c> out</c>

00:04:56.189 --> 00:04:56.199 align:start position:0%
so that's another feature to call out
 

00:04:56.199 --> 00:04:58.909 align:start position:0%
so that's another feature to call out
and<00:04:56.360><c> to</c><00:04:56.560><c> help</c><00:04:56.840><c> users</c><00:04:57.400><c> kind</c><00:04:57.520><c> of</c><00:04:57.680><c> control</c><00:04:58.639><c> um</c><00:04:58.759><c> or</c>

00:04:58.909 --> 00:04:58.919 align:start position:0%
and to help users kind of control um or
 

00:04:58.919 --> 00:05:01.990 align:start position:0%
and to help users kind of control um or
help<00:04:59.160><c> team</c><00:04:59.400><c> ad</c><00:04:59.680><c> admins</c><00:05:00.240><c> control</c><00:05:01.240><c> um</c><00:05:01.440><c> you</c><00:05:01.560><c> know</c>

00:05:01.990 --> 00:05:02.000 align:start position:0%
help team ad admins control um you know
 

00:05:02.000 --> 00:05:05.110 align:start position:0%
help team ad admins control um you know
save<00:05:02.400><c> a</c><00:05:02.600><c> special</c><00:05:03.039><c> set</c><00:05:03.240><c> of</c><00:05:03.479><c> aliases</c><00:05:04.479><c> um</c><00:05:04.600><c> to</c><00:05:04.800><c> only</c>

00:05:05.110 --> 00:05:05.120 align:start position:0%
save a special set of aliases um to only
 

00:05:05.120 --> 00:05:07.189 align:start position:0%
save a special set of aliases um to only
be<00:05:05.320><c> added</c><00:05:05.840><c> by</c><00:05:06.120><c> folks</c><00:05:06.440><c> that</c><00:05:06.560><c> are</c><00:05:06.759><c> under</c><00:05:07.080><c> this</c>

00:05:07.189 --> 00:05:07.199 align:start position:0%
be added by folks that are under this
 

00:05:07.199 --> 00:05:10.469 align:start position:0%
be added by folks that are under this
role<00:05:07.440><c> of</c><00:05:07.560><c> a</c><00:05:08.160><c> um</c><00:05:08.600><c> you</c><00:05:08.759><c> know</c><00:05:09.320><c> a</c><00:05:09.520><c> model</c><00:05:09.840><c> registry</c>

00:05:10.469 --> 00:05:10.479 align:start position:0%
role of a um you know a model registry
 

00:05:10.479 --> 00:05:12.870 align:start position:0%
role of a um you know a model registry
admin<00:05:11.479><c> um</c><00:05:11.680><c> which</c><00:05:11.800><c> you</c><00:05:11.880><c> can</c><00:05:12.039><c> see</c><00:05:12.280><c> above</c><00:05:12.720><c> and</c>

00:05:12.870 --> 00:05:12.880 align:start position:0%
admin um which you can see above and
 

00:05:12.880 --> 00:05:15.550 align:start position:0%
admin um which you can see above and
that's<00:05:13.080><c> managed</c><00:05:13.840><c> inside</c><00:05:14.840><c> um</c><00:05:15.080><c> you</c><00:05:15.199><c> know</c><00:05:15.479><c> the</c>

00:05:15.550 --> 00:05:15.560 align:start position:0%
that's managed inside um you know the
 

00:05:15.560 --> 00:05:17.790 align:start position:0%
that's managed inside um you know the
doc<00:05:16.280><c> uh</c><00:05:16.400><c> covers</c><00:05:16.840><c> this</c><00:05:17.080><c> but</c><00:05:17.160><c> you</c><00:05:17.280><c> can</c><00:05:17.440><c> find</c><00:05:17.600><c> it</c>

00:05:17.790 --> 00:05:17.800 align:start position:0%
doc uh covers this but you can find it
 

00:05:17.800 --> 00:05:20.029 align:start position:0%
doc uh covers this but you can find it
inside<00:05:18.120><c> kind</c><00:05:18.240><c> of</c><00:05:18.400><c> the</c><00:05:18.520><c> settings</c><00:05:19.039><c> of</c><00:05:19.280><c> your</c><00:05:19.919><c> uh</c>

00:05:20.029 --> 00:05:20.039 align:start position:0%
inside kind of the settings of your uh
 

00:05:20.039 --> 00:05:23.360 align:start position:0%
inside kind of the settings of your uh
model<00:05:20.360><c> registry</c>

