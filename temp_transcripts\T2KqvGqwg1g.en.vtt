WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.760 align:start position:0%
 
hi<00:00:00.089><c> guys</c><00:00:00.329><c> and</c><00:00:00.599><c> finally</c><00:00:01.020><c> in</c><00:00:01.110><c> this</c><00:00:01.230><c> episode</c>

00:00:01.760 --> 00:00:01.770 align:start position:0%
hi guys and finally in this episode
 

00:00:01.770 --> 00:00:04.490 align:start position:0%
hi guys and finally in this episode
we're<00:00:02.070><c> going</c><00:00:02.280><c> to</c><00:00:02.429><c> be</c><00:00:02.899><c> wrapping</c><00:00:03.899><c> up</c><00:00:04.140><c> our</c><00:00:04.319><c> whole</c>

00:00:04.490 --> 00:00:04.500 align:start position:0%
we're going to be wrapping up our whole
 

00:00:04.500 --> 00:00:08.169 align:start position:0%
we're going to be wrapping up our whole
section<00:00:05.040><c> here</c><00:00:05.069><c> about</c><00:00:05.670><c> the</c><00:00:06.600><c> database</c><00:00:07.379><c> and</c>

00:00:08.169 --> 00:00:08.179 align:start position:0%
section here about the database and
 

00:00:08.179 --> 00:00:10.640 align:start position:0%
section here about the database and
saving<00:00:09.179><c> the</c><00:00:09.330><c> theme</c><00:00:09.599><c> and</c><00:00:09.780><c> all</c><00:00:09.929><c> that</c><00:00:10.170><c> that's</c><00:00:10.410><c> all</c>

00:00:10.640 --> 00:00:10.650 align:start position:0%
saving the theme and all that that's all
 

00:00:10.650 --> 00:00:13.039 align:start position:0%
saving the theme and all that that's all
done<00:00:11.010><c> already</c><00:00:11.340><c> we're</c><00:00:11.880><c> gonna</c><00:00:12.030><c> launch</c><00:00:12.840><c> our</c>

00:00:13.039 --> 00:00:13.049 align:start position:0%
done already we're gonna launch our
 

00:00:13.049 --> 00:00:15.079 align:start position:0%
done already we're gonna launch our
server<00:00:13.559><c> over</c><00:00:13.830><c> here</c><00:00:13.860><c> and</c><00:00:14.340><c> we're</c><00:00:14.639><c> gonna</c><00:00:14.759><c> notice</c>

00:00:15.079 --> 00:00:15.089 align:start position:0%
server over here and we're gonna notice
 

00:00:15.089 --> 00:00:21.349 align:start position:0%
server over here and we're gonna notice
that<00:00:15.120><c> every</c><00:00:15.509><c> time</c><00:00:15.780><c> we</c><00:00:16.529><c> update</c><00:00:17.100><c> our</c><00:00:19.850><c> our</c><00:00:20.850><c> theme</c>

00:00:21.349 --> 00:00:21.359 align:start position:0%
that every time we update our our theme
 

00:00:21.359 --> 00:00:23.810 align:start position:0%
that every time we update our our theme
it's<00:00:21.930><c> going</c><00:00:22.109><c> to</c><00:00:22.170><c> be</c><00:00:22.320><c> persistent</c><00:00:22.830><c> which</c><00:00:23.580><c> means</c>

00:00:23.810 --> 00:00:23.820 align:start position:0%
it's going to be persistent which means
 

00:00:23.820 --> 00:00:24.950 align:start position:0%
it's going to be persistent which means
that<00:00:23.970><c> it's</c><00:00:24.060><c> gonna</c><00:00:24.150><c> get</c><00:00:24.359><c> saved</c><00:00:24.689><c> to</c><00:00:24.840><c> the</c>

00:00:24.950 --> 00:00:24.960 align:start position:0%
that it's gonna get saved to the
 

00:00:24.960 --> 00:00:26.570 align:start position:0%
that it's gonna get saved to the
database<00:00:25.109><c> and</c><00:00:25.650><c> every</c><00:00:25.800><c> time</c><00:00:25.980><c> that</c><00:00:26.010><c> the</c><00:00:26.340><c> page</c>

00:00:26.570 --> 00:00:26.580 align:start position:0%
database and every time that the page
 

00:00:26.580 --> 00:00:29.080 align:start position:0%
database and every time that the page
reloads<00:00:26.910><c> with</c><00:00:27.449><c> a</c><00:00:27.480><c> specific</c><00:00:27.779><c> user</c><00:00:28.260><c> logged</c><00:00:28.619><c> in</c>

00:00:29.080 --> 00:00:29.090 align:start position:0%
reloads with a specific user logged in
 

00:00:29.090 --> 00:00:32.269 align:start position:0%
reloads with a specific user logged in
the<00:00:30.090><c> directive</c><00:00:30.750><c> sends</c><00:00:31.170><c> the</c><00:00:31.349><c> theme</c><00:00:31.650><c> to</c><00:00:32.160><c> the</c>

00:00:32.269 --> 00:00:32.279 align:start position:0%
the directive sends the theme to the
 

00:00:32.279 --> 00:00:36.560 align:start position:0%
the directive sends the theme to the
controller<00:00:32.759><c> and</c><00:00:33.059><c> the</c><00:00:33.149><c> controller</c><00:00:33.840><c> loads</c><00:00:35.570><c> I'm</c>

00:00:36.560 --> 00:00:36.570 align:start position:0%
controller and the controller loads I'm
 

00:00:36.570 --> 00:00:38.270 align:start position:0%
controller and the controller loads I'm
sorry<00:00:36.809><c> the</c><00:00:36.989><c> Directorate</c><00:00:37.559><c> Act</c><00:00:37.739><c> directive</c>

00:00:38.270 --> 00:00:38.280 align:start position:0%
sorry the Directorate Act directive
 

00:00:38.280 --> 00:00:39.799 align:start position:0%
sorry the Directorate Act directive
actually<00:00:38.550><c> sets</c><00:00:38.790><c> it</c><00:00:38.910><c> in</c><00:00:39.000><c> the</c><00:00:39.120><c> root</c><00:00:39.300><c> scope</c><00:00:39.480><c> so</c>

00:00:39.799 --> 00:00:39.809 align:start position:0%
actually sets it in the root scope so
 

00:00:39.809 --> 00:00:42.410 align:start position:0%
actually sets it in the root scope so
let's<00:00:39.989><c> do</c><00:00:40.410><c> themes</c><00:00:40.860><c> here</c><00:00:41.250><c> I'm</c><00:00:41.940><c> gonna</c><00:00:42.120><c> choose</c>

00:00:42.410 --> 00:00:42.420 align:start position:0%
let's do themes here I'm gonna choose
 

00:00:42.420 --> 00:00:45.830 align:start position:0%
let's do themes here I'm gonna choose
the<00:00:42.840><c> cyborg</c><00:00:43.469><c> theme</c><00:00:43.829><c> and</c><00:00:44.420><c> there</c><00:00:45.420><c> we</c><00:00:45.539><c> go</c>

00:00:45.830 --> 00:00:45.840 align:start position:0%
the cyborg theme and there we go
 

00:00:45.840 --> 00:00:48.260 align:start position:0%
the cyborg theme and there we go
once<00:00:45.960><c> I</c><00:00:46.079><c> choose</c><00:00:46.289><c> cyborg</c><00:00:46.950><c> I</c><00:00:47.250><c> go</c><00:00:47.730><c> back</c><00:00:47.940><c> over</c><00:00:48.239><c> here</c>

00:00:48.260 --> 00:00:48.270 align:start position:0%
once I choose cyborg I go back over here
 

00:00:48.270 --> 00:00:50.840 align:start position:0%
once I choose cyborg I go back over here
and<00:00:48.750><c> I</c><00:00:48.930><c> do</c><00:00:48.989><c> a</c><00:00:49.410><c> second</c><00:00:50.129><c> ago</c><00:00:50.309><c> the</c><00:00:50.430><c> client</c><00:00:50.730><c> theme</c>

00:00:50.840 --> 00:00:50.850 align:start position:0%
and I do a second ago the client theme
 

00:00:50.850 --> 00:00:53.360 align:start position:0%
and I do a second ago the client theme
was<00:00:51.030><c> Yeti</c><00:00:51.270><c> I</c><00:00:51.600><c> do</c><00:00:51.870><c> so</c><00:00:52.260><c> that's</c><00:00:52.379><c> all</c><00:00:52.590><c> from</c><00:00:52.829><c> client</c>

00:00:53.360 --> 00:00:53.370 align:start position:0%
was Yeti I do so that's all from client
 

00:00:53.370 --> 00:00:55.100 align:start position:0%
was Yeti I do so that's all from client
where<00:00:53.550><c> client</c><00:00:53.850><c> ID</c><00:00:54.030><c> was</c><00:00:54.270><c> 8</c><00:00:54.510><c> let's</c><00:00:54.750><c> see</c><00:00:54.870><c> what</c><00:00:55.020><c> the</c>

00:00:55.100 --> 00:00:55.110 align:start position:0%
where client ID was 8 let's see what the
 

00:00:55.110 --> 00:00:57.920 align:start position:0%
where client ID was 8 let's see what the
theme<00:00:55.410><c> is</c><00:00:55.649><c> gonna</c><00:00:55.980><c> be</c><00:00:56.250><c> it's</c><00:00:57.030><c> gonna</c><00:00:57.149><c> be</c><00:00:57.270><c> cyborgs</c>

00:00:57.920 --> 00:00:57.930 align:start position:0%
theme is gonna be it's gonna be cyborgs
 

00:00:57.930 --> 00:01:00.619 align:start position:0%
theme is gonna be it's gonna be cyborgs
okay<00:00:58.350><c> and</c><00:00:58.620><c> then</c><00:00:58.800><c> I'm</c><00:00:58.920><c> gonna</c><00:00:59.149><c> reload</c><00:01:00.149><c> the</c><00:01:00.329><c> page</c>

00:01:00.619 --> 00:01:00.629 align:start position:0%
okay and then I'm gonna reload the page
 

00:01:00.629 --> 00:01:05.020 align:start position:0%
okay and then I'm gonna reload the page
and<00:01:01.280><c> I'm</c><00:01:02.280><c> still</c><00:01:02.609><c> gonna</c><00:01:02.760><c> have</c><00:01:03.030><c> cyborg</c>

00:01:05.020 --> 00:01:05.030 align:start position:0%
and I'm still gonna have cyborg
 

00:01:05.030 --> 00:01:07.370 align:start position:0%
and I'm still gonna have cyborg
I'm<00:01:06.030><c> gonna</c><00:01:06.210><c> log</c><00:01:06.450><c> out</c><00:01:06.630><c> and</c><00:01:06.780><c> log</c><00:01:06.960><c> back</c><00:01:06.990><c> in</c><00:01:07.350><c> with</c>

00:01:07.370 --> 00:01:07.380 align:start position:0%
I'm gonna log out and log back in with
 

00:01:07.380 --> 00:01:09.020 align:start position:0%
I'm gonna log out and log back in with
the<00:01:07.590><c> same</c><00:01:07.619><c> user</c><00:01:08.100><c> and</c><00:01:08.369><c> I'm</c><00:01:08.460><c> still</c><00:01:08.729><c> gonna</c><00:01:08.850><c> have</c>

00:01:09.020 --> 00:01:09.030 align:start position:0%
the same user and I'm still gonna have
 

00:01:09.030 --> 00:01:10.340 align:start position:0%
the same user and I'm still gonna have
cyborg<00:01:09.450><c> we're</c><00:01:09.570><c> gonna</c><00:01:09.750><c> do</c><00:01:09.869><c> something</c><00:01:10.080><c> about</c>

00:01:10.340 --> 00:01:10.350 align:start position:0%
cyborg we're gonna do something about
 

00:01:10.350 --> 00:01:13.899 align:start position:0%
cyborg we're gonna do something about
the<00:01:10.729><c> animation</c><00:01:11.729><c> and</c><00:01:12.140><c> loading</c><00:01:13.140><c> animation</c>

00:01:13.899 --> 00:01:13.909 align:start position:0%
the animation and loading animation
 

00:01:13.909 --> 00:01:16.700 align:start position:0%
the animation and loading animation
while<00:01:14.909><c> the</c><00:01:15.060><c> page</c><00:01:15.240><c> is</c><00:01:15.450><c> loading</c><00:01:15.869><c> cuz</c><00:01:16.080><c> it's</c><00:01:16.290><c> not</c>

00:01:16.700 --> 00:01:16.710 align:start position:0%
while the page is loading cuz it's not
 

00:01:16.710 --> 00:01:18.200 align:start position:0%
while the page is loading cuz it's not
looking<00:01:17.100><c> very</c><00:01:17.130><c> good</c><00:01:17.490><c> right</c><00:01:17.549><c> now</c><00:01:17.759><c> as</c><00:01:18.060><c> you</c><00:01:18.180><c> can</c>

00:01:18.200 --> 00:01:18.210 align:start position:0%
looking very good right now as you can
 

00:01:18.210 --> 00:01:20.660 align:start position:0%
looking very good right now as you can
see<00:01:18.330><c> but</c><00:01:18.659><c> that's</c><00:01:18.780><c> okay</c><00:01:19.229><c> we're</c><00:01:20.189><c> gonna</c><00:01:20.340><c> be</c><00:01:20.490><c> able</c>

00:01:20.660 --> 00:01:20.670 align:start position:0%
see but that's okay we're gonna be able
 

00:01:20.670 --> 00:01:23.050 align:start position:0%
see but that's okay we're gonna be able
to<00:01:20.790><c> fix</c><00:01:21.000><c> that</c><00:01:21.240><c> so</c><00:01:21.390><c> one</c><00:01:21.570><c> thing</c><00:01:21.810><c> at</c><00:01:21.960><c> a</c><00:01:21.990><c> time</c><00:01:22.080><c> here</c>

00:01:23.050 --> 00:01:23.060 align:start position:0%
to fix that so one thing at a time here
 

00:01:23.060 --> 00:01:25.249 align:start position:0%
to fix that so one thing at a time here
and<00:01:24.060><c> we're</c><00:01:24.270><c> gonna</c><00:01:24.390><c> log</c><00:01:24.600><c> back</c><00:01:24.780><c> in</c><00:01:24.930><c> with</c><00:01:24.960><c> Lea</c><00:01:25.200><c> she</c>

00:01:25.249 --> 00:01:25.259 align:start position:0%
and we're gonna log back in with Lea she
 

00:01:25.259 --> 00:01:28.789 align:start position:0%
and we're gonna log back in with Lea she
should<00:01:25.710><c> be</c><00:01:25.860><c> cyborg</c><00:01:26.400><c> theme</c><00:01:26.700><c> black</c><00:01:27.270><c> and</c><00:01:27.799><c> there</c>

00:01:28.789 --> 00:01:28.799 align:start position:0%
should be cyborg theme black and there
 

00:01:28.799 --> 00:01:30.380 align:start position:0%
should be cyborg theme black and there
we<00:01:28.920><c> go</c><00:01:29.100><c> because</c><00:01:29.729><c> it</c><00:01:29.850><c> was</c><00:01:29.939><c> saved</c><00:01:30.150><c> in</c><00:01:30.329><c> the</c>

00:01:30.380 --> 00:01:30.390 align:start position:0%
we go because it was saved in the
 

00:01:30.390 --> 00:01:33.499 align:start position:0%
we go because it was saved in the
database<00:01:30.570><c> I</c><00:01:31.229><c> haven't</c><00:01:32.220><c> changed</c><00:01:32.400><c> it</c><00:01:32.759><c> and</c><00:01:33.000><c> it's</c>

00:01:33.499 --> 00:01:33.509 align:start position:0%
database I haven't changed it and it's
 

00:01:33.509 --> 00:01:36.499 align:start position:0%
database I haven't changed it and it's
still<00:01:33.689><c> gonna</c><00:01:33.840><c> be</c><00:01:33.960><c> cyborg</c><00:01:34.590><c> so</c><00:01:34.890><c> that's</c><00:01:35.579><c> the</c><00:01:36.119><c> code</c>

00:01:36.499 --> 00:01:36.509 align:start position:0%
still gonna be cyborg so that's the code
 

00:01:36.509 --> 00:01:38.330 align:start position:0%
still gonna be cyborg so that's the code
I've<00:01:36.810><c> already</c><00:01:37.049><c> pushed</c><00:01:37.409><c> it</c><00:01:37.619><c> to</c><00:01:37.740><c> github</c><00:01:38.100><c> now</c><00:01:38.310><c> I</c>

00:01:38.330 --> 00:01:38.340 align:start position:0%
I've already pushed it to github now I
 

00:01:38.340 --> 00:01:42.170 align:start position:0%
I've already pushed it to github now I
actually<00:01:38.700><c> want</c><00:01:39.060><c> to</c><00:01:40.250><c> push</c><00:01:41.250><c> it</c><00:01:41.400><c> to</c><00:01:41.520><c> Heroku</c><00:01:41.820><c> so</c>

00:01:42.170 --> 00:01:42.180 align:start position:0%
actually want to push it to Heroku so
 

00:01:42.180 --> 00:01:46.550 align:start position:0%
actually want to push it to Heroku so
let's<00:01:42.360><c> see</c><00:01:43.140><c> how</c><00:01:43.500><c> we</c><00:01:43.950><c> can</c><00:01:44.189><c> do</c><00:01:44.369><c> that</c><00:01:45.049><c> one</c><00:01:46.049><c> second</c>

00:01:46.550 --> 00:01:46.560 align:start position:0%
let's see how we can do that one second
 

00:01:46.560 --> 00:01:51.499 align:start position:0%
let's see how we can do that one second
we're<00:01:46.979><c> gonna</c><00:01:47.100><c> go</c><00:01:47.369><c> to</c><00:01:48.890><c> get</c><00:01:49.890><c> branch</c><00:01:50.340><c> not</c><00:01:51.149><c> get</c>

00:01:51.499 --> 00:01:51.509 align:start position:0%
we're gonna go to get branch not get
 

00:01:51.509 --> 00:01:55.280 align:start position:0%
we're gonna go to get branch not get
checkout<00:01:51.990><c> master</c><00:01:52.729><c> okay</c><00:01:53.729><c> okay</c><00:01:54.659><c> I</c><00:01:54.840><c> got</c><00:01:54.930><c> mastered</c>

00:01:55.280 --> 00:01:55.290 align:start position:0%
checkout master okay okay I got mastered
 

00:01:55.290 --> 00:01:57.139 align:start position:0%
checkout master okay okay I got mastered
in<00:01:55.470><c> order</c><00:01:55.680><c> to</c><00:01:55.770><c> push</c><00:01:55.979><c> to</c><00:01:56.189><c> Heroku</c><00:01:56.460><c> we</c><00:01:56.790><c> need</c><00:01:56.909><c> to</c><00:01:57.000><c> be</c>

00:01:57.139 --> 00:01:57.149 align:start position:0%
in order to push to Heroku we need to be
 

00:01:57.149 --> 00:01:59.630 align:start position:0%
in order to push to Heroku we need to be
on<00:01:57.360><c> our</c><00:01:57.390><c> master</c><00:01:58.110><c> branch</c><00:01:58.320><c> we're</c><00:01:59.250><c> gonna</c><00:01:59.399><c> get</c>

00:01:59.630 --> 00:01:59.640 align:start position:0%
on our master branch we're gonna get
 

00:01:59.640 --> 00:02:05.270 align:start position:0%
on our master branch we're gonna get
full<00:02:02.240><c> let's</c><00:02:03.240><c> see</c><00:02:03.509><c> what</c><00:02:04.170><c> was</c><00:02:04.290><c> the</c><00:02:04.409><c> name</c><00:02:04.619><c> user</c>

00:02:05.270 --> 00:02:05.280 align:start position:0%
full let's see what was the name user
 

00:02:05.280 --> 00:02:07.130 align:start position:0%
full let's see what was the name user
template<00:02:05.909><c> option</c><00:02:06.299><c> was</c><00:02:06.479><c> the</c><00:02:06.659><c> name</c><00:02:06.840><c> of</c><00:02:06.990><c> the</c>

00:02:07.130 --> 00:02:07.140 align:start position:0%
template option was the name of the
 

00:02:07.140 --> 00:02:08.930 align:start position:0%
template option was the name of the
branch<00:02:07.409><c> we</c><00:02:07.619><c> need</c><00:02:07.770><c> to</c><00:02:07.860><c> pull</c><00:02:08.160><c> this</c><00:02:08.310><c> branch</c><00:02:08.640><c> into</c>

00:02:08.930 --> 00:02:08.940 align:start position:0%
branch we need to pull this branch into
 

00:02:08.940 --> 00:02:12.790 align:start position:0%
branch we need to pull this branch into
the<00:02:09.030><c> master</c><00:02:09.420><c> branch</c><00:02:09.920><c> getting</c><00:02:10.920><c> full</c><00:02:11.280><c> order</c><00:02:11.940><c> Joe</c>

00:02:12.790 --> 00:02:12.800 align:start position:0%
the master branch getting full order Joe
 

00:02:12.800 --> 00:02:15.490 align:start position:0%
the master branch getting full order Joe
user<00:02:13.280><c> template</c><00:02:13.940><c> option</c><00:02:14.450><c> gonna</c><00:02:15.080><c> pull</c><00:02:15.350><c> that</c>

00:02:15.490 --> 00:02:15.500 align:start position:0%
user template option gonna pull that
 

00:02:15.500 --> 00:02:19.810 align:start position:0%
user template option gonna pull that
into<00:02:15.800><c> the</c><00:02:15.920><c> main</c><00:02:16.100><c> branch</c><00:02:18.550><c> everything</c><00:02:19.550><c> from</c>

00:02:19.810 --> 00:02:19.820 align:start position:0%
into the main branch everything from
 

00:02:19.820 --> 00:02:22.150 align:start position:0%
into the main branch everything from
there<00:02:20.060><c> I'm</c><00:02:20.390><c> already</c><00:02:20.690><c> in</c><00:02:20.960><c> master</c><00:02:21.410><c> there</c><00:02:21.830><c> we</c><00:02:21.950><c> go</c>

00:02:22.150 --> 00:02:22.160 align:start position:0%
there I'm already in master there we go
 

00:02:22.160 --> 00:02:25.390 align:start position:0%
there I'm already in master there we go
and<00:02:22.430><c> finally</c><00:02:22.820><c> I'm</c><00:02:23.060><c> gonna</c><00:02:23.300><c> do</c><00:02:23.540><c> a</c><00:02:23.780><c> get</c><00:02:24.740><c> at</c><00:02:24.980><c> all</c>

00:02:25.390 --> 00:02:25.400 align:start position:0%
and finally I'm gonna do a get at all
 

00:02:25.400 --> 00:02:27.280 align:start position:0%
and finally I'm gonna do a get at all
I'm<00:02:25.730><c> pretty</c><00:02:26.300><c> sure</c><00:02:26.540><c> that</c><00:02:26.630><c> it's</c><00:02:26.810><c> already</c><00:02:27.020><c> good</c>

00:02:27.280 --> 00:02:27.290 align:start position:0%
I'm pretty sure that it's already good
 

00:02:27.290 --> 00:02:34.990 align:start position:0%
I'm pretty sure that it's already good
get<00:02:28.100><c> commit</c><00:02:28.520><c> theme</c><00:02:31.150><c> okay</c><00:02:32.180><c> and</c><00:02:32.420><c> get</c><00:02:33.320><c> push</c><00:02:34.000><c> local</c>

00:02:34.990 --> 00:02:35.000 align:start position:0%
get commit theme okay and get push local
 

00:02:35.000 --> 00:02:39.160 align:start position:0%
get commit theme okay and get push local
master<00:02:36.040><c> Roku</c><00:02:37.040><c> master</c><00:02:38.000><c> let's</c><00:02:38.750><c> look</c><00:02:38.900><c> at</c><00:02:39.020><c> what</c>

00:02:39.160 --> 00:02:39.170 align:start position:0%
master Roku master let's look at what
 

00:02:39.170 --> 00:02:41.140 align:start position:0%
master Roku master let's look at what
the<00:02:39.290><c> hell</c><00:02:39.410><c> cool</c><00:02:39.800><c> currently</c><00:02:40.370><c> looks</c><00:02:40.610><c> like</c><00:02:40.850><c> right</c>

00:02:41.140 --> 00:02:41.150 align:start position:0%
the hell cool currently looks like right
 

00:02:41.150 --> 00:02:50.200 align:start position:0%
the hell cool currently looks like right
now<00:02:41.180><c> is</c><00:02:42.160><c> 239</c><00:02:43.190><c> I</c><00:02:43.400><c> go</c><00:02:43.580><c> to</c><00:02:43.640><c> koala</c><00:02:44.180><c> CMS</c><00:02:48.700><c> data</c><00:02:49.700><c> Roku</c>

00:02:50.200 --> 00:02:50.210 align:start position:0%
now is 239 I go to koala CMS data Roku
 

00:02:50.210 --> 00:02:53.110 align:start position:0%
now is 239 I go to koala CMS data Roku
app<00:02:50.390><c> calm</c><00:02:51.130><c> this</c><00:02:52.130><c> is</c><00:02:52.370><c> currently</c><00:02:52.700><c> being</c><00:02:53.000><c> built</c>

00:02:53.110 --> 00:02:53.120 align:start position:0%
app calm this is currently being built
 

00:02:53.120 --> 00:03:00.430 align:start position:0%
app calm this is currently being built
over<00:02:53.540><c> here</c><00:02:55.990><c> as</c><00:02:56.990><c> you</c><00:02:57.200><c> can</c><00:02:57.350><c> see</c><00:02:57.560><c> currently</c><00:02:57.800><c> we</c><00:02:59.440><c> do</c>

00:03:00.430 --> 00:03:00.440 align:start position:0%
over here as you can see currently we do
 

00:03:00.440 --> 00:03:03.970 align:start position:0%
over here as you can see currently we do
not<00:03:00.650><c> have</c><00:03:01.070><c> the</c><00:03:01.850><c> themes</c><00:03:02.210><c> option</c><00:03:02.650><c> either</c><00:03:03.650><c> for</c>

00:03:03.970 --> 00:03:03.980 align:start position:0%
not have the themes option either for
 

00:03:03.980 --> 00:03:06.160 align:start position:0%
not have the themes option either for
the<00:03:04.070><c> logged</c><00:03:04.340><c> in</c><00:03:04.550><c> user</c><00:03:04.610><c> but</c><00:03:05.570><c> we're</c><00:03:05.810><c> gonna</c><00:03:05.900><c> log</c>

00:03:06.160 --> 00:03:06.170 align:start position:0%
the logged in user but we're gonna log
 

00:03:06.170 --> 00:03:11.590 align:start position:0%
the logged in user but we're gonna log
in<00:03:06.200><c> with</c><00:03:06.530><c> Li</c><00:03:06.890><c> XI</c><00:03:06.950><c> a</c><00:03:07.310><c> koala</c><00:03:07.730><c> CMS</c><00:03:08.270><c> calm</c><00:03:10.420><c> okay</c><00:03:11.420><c> this</c>

00:03:11.590 --> 00:03:11.600 align:start position:0%
in with Li XI a koala CMS calm okay this
 

00:03:11.600 --> 00:03:13.180 align:start position:0%
in with Li XI a koala CMS calm okay this
is<00:03:11.750><c> still</c><00:03:11.960><c> getting</c><00:03:12.110><c> built</c><00:03:12.410><c> as</c><00:03:12.590><c> you</c><00:03:12.710><c> can</c><00:03:12.830><c> see</c><00:03:13.040><c> my</c>

00:03:13.180 --> 00:03:13.190 align:start position:0%
is still getting built as you can see my
 

00:03:13.190 --> 00:03:15.400 align:start position:0%
is still getting built as you can see my
theme<00:03:13.490><c> now</c><00:03:13.760><c> was</c><00:03:14.270><c> the</c><00:03:14.450><c> default</c><00:03:14.660><c> theme</c><00:03:15.110><c> I</c><00:03:15.230><c> don't</c>

00:03:15.400 --> 00:03:15.410 align:start position:0%
theme now was the default theme I don't
 

00:03:15.410 --> 00:03:17.980 align:start position:0%
theme now was the default theme I don't
even<00:03:15.590><c> have</c><00:03:15.740><c> the</c><00:03:15.950><c> themes</c><00:03:16.250><c> option</c><00:03:16.730><c> here</c><00:03:17.090><c> because</c>

00:03:17.980 --> 00:03:17.990 align:start position:0%
even have the themes option here because
 

00:03:17.990 --> 00:03:20.260 align:start position:0%
even have the themes option here because
that<00:03:18.140><c> all</c><00:03:18.410><c> got</c><00:03:18.680><c> added</c><00:03:19.100><c> now</c><00:03:19.280><c> and</c><00:03:19.580><c> now</c><00:03:19.850><c> that's</c>

00:03:20.260 --> 00:03:20.270 align:start position:0%
that all got added now and now that's
 

00:03:20.270 --> 00:03:22.750 align:start position:0%
that all got added now and now that's
totally<00:03:20.990><c> done</c><00:03:21.260><c> it's</c><00:03:21.980><c> been</c><00:03:22.220><c> pushed</c><00:03:22.520><c> now</c><00:03:22.700><c> I'm</c>

00:03:22.750 --> 00:03:22.760 align:start position:0%
totally done it's been pushed now I'm
 

00:03:22.760 --> 00:03:24.640 align:start position:0%
totally done it's been pushed now I'm
gonna<00:03:22.880><c> get</c><00:03:23.090><c> the</c><00:03:23.150><c> themes</c><00:03:23.570><c> tab</c><00:03:23.900><c> over</c><00:03:24.290><c> here</c>

00:03:24.640 --> 00:03:24.650 align:start position:0%
gonna get the themes tab over here
 

00:03:24.650 --> 00:03:26.710 align:start position:0%
gonna get the themes tab over here
because<00:03:24.830><c> that</c><00:03:24.950><c> was</c><00:03:25.070><c> pushed</c><00:03:25.400><c> within</c><00:03:26.300><c> this</c>

00:03:26.710 --> 00:03:26.720 align:start position:0%
because that was pushed within this
 

00:03:26.720 --> 00:03:28.660 align:start position:0%
because that was pushed within this
specific<00:03:27.020><c> release</c><00:03:27.770><c> okay</c><00:03:28.310><c> and</c><00:03:28.400><c> you</c><00:03:28.460><c> can</c><00:03:28.610><c> see</c>

00:03:28.660 --> 00:03:28.670 align:start position:0%
specific release okay and you can see
 

00:03:28.670 --> 00:03:30.790 align:start position:0%
specific release okay and you can see
we're<00:03:29.360><c> getting</c><00:03:29.450><c> a</c><00:03:29.630><c> ton</c><00:03:29.870><c> of</c><00:03:29.930><c> problems</c><00:03:30.230><c> there</c>

00:03:30.790 --> 00:03:30.800 align:start position:0%
we're getting a ton of problems there
 

00:03:30.800 --> 00:03:32.740 align:start position:0%
we're getting a ton of problems there
that's<00:03:31.490><c> gonna</c><00:03:31.670><c> need</c><00:03:31.880><c> to</c><00:03:32.000><c> get</c><00:03:32.150><c> fixed</c><00:03:32.420><c> and</c><00:03:32.570><c> I</c>

00:03:32.740 --> 00:03:32.750 align:start position:0%
that's gonna need to get fixed and I
 

00:03:32.750 --> 00:03:36.000 align:start position:0%
that's gonna need to get fixed and I
have<00:03:32.900><c> the</c><00:03:33.050><c> themes</c><00:03:33.350><c> here</c><00:03:33.530><c> and</c><00:03:34.060><c> if</c><00:03:35.060><c> I</c><00:03:35.270><c> click</c><00:03:35.300><c> on</c>

00:03:36.000 --> 00:03:36.010 align:start position:0%
have the themes here and if I click on
 

00:03:36.010 --> 00:03:41.320 align:start position:0%
have the themes here and if I click on
Flatley<00:03:37.450><c> and</c><00:03:38.590><c> then</c><00:03:39.590><c> I</c><00:03:39.710><c> go</c><00:03:39.890><c> back</c><00:03:39.920><c> into</c><00:03:40.490><c> here</c><00:03:41.090><c> and</c>

00:03:41.320 --> 00:03:41.330 align:start position:0%
Flatley and then I go back into here and
 

00:03:41.330 --> 00:03:43.870 align:start position:0%
Flatley and then I go back into here and
I<00:03:41.540><c> do</c><00:03:41.600><c> write</c><00:03:42.470><c> another</c><00:03:42.710><c> client</c><00:03:43.070><c> theme</c><00:03:43.190><c> a</c><00:03:43.220><c> cyborg</c>

00:03:43.870 --> 00:03:43.880 align:start position:0%
I do write another client theme a cyborg
 

00:03:43.880 --> 00:03:47.160 align:start position:0%
I do write another client theme a cyborg
hopefully<00:03:44.810><c> it's</c><00:03:44.930><c> Flatley</c><00:03:45.410><c> now</c><00:03:45.590><c> oh</c>

00:03:47.160 --> 00:03:47.170 align:start position:0%
hopefully it's Flatley now oh
 

00:03:47.170 --> 00:03:54.120 align:start position:0%
hopefully it's Flatley now oh
still<00:03:48.170><c> side</c><00:03:48.440><c> work</c><00:03:50.830><c> why</c><00:03:51.830><c> is</c><00:03:52.010><c> that</c>

00:03:54.120 --> 00:03:54.130 align:start position:0%
 
 

00:03:54.130 --> 00:03:56.980 align:start position:0%
 
Who<00:03:55.130><c> am</c><00:03:55.160><c> I</c><00:03:55.310><c> logged</c><00:03:55.490><c> in</c><00:03:55.700><c> is</c><00:03:55.820><c> Li</c><00:03:56.270><c> she</c><00:03:56.330><c> at</c><00:03:56.600><c> kuala</c>

00:03:56.980 --> 00:03:56.990 align:start position:0%
Who am I logged in is Li she at kuala
 

00:03:56.990 --> 00:03:59.160 align:start position:0%
Who am I logged in is Li she at kuala
CNS<00:03:57.530><c> comm</c><00:03:57.980><c> sorry</c><00:03:58.250><c> that's</c><00:03:58.460><c> a</c><00:03:58.580><c> different</c>

00:03:59.160 --> 00:03:59.170 align:start position:0%
CNS comm sorry that's a different
 

00:03:59.170 --> 00:04:05.500 align:start position:0%
CNS comm sorry that's a different
different<00:04:00.170><c> client</c><00:04:00.620><c> who</c><00:04:00.800><c> is</c><00:04:00.830><c> Li</c><00:04:01.160><c> she</c><00:04:02.830><c> client</c><00:04:04.510><c> so</c>

00:04:05.500 --> 00:04:05.510 align:start position:0%
different client who is Li she client so
 

00:04:05.510 --> 00:04:08.050 align:start position:0%
different client who is Li she client so
that's<00:04:05.660><c> all</c><00:04:05.870><c> from</c><00:04:06.110><c> client</c><00:04:06.710><c> you</c><00:04:07.610><c> want</c><00:04:07.760><c> to</c><00:04:07.880><c> see</c>

00:04:08.050 --> 00:04:08.060 align:start position:0%
that's all from client you want to see
 

00:04:08.060 --> 00:04:12.250 align:start position:0%
that's all from client you want to see
who<00:04:08.240><c> Lisa</c><00:04:09.730><c> there</c><00:04:10.730><c> is</c><00:04:10.880><c> Alicia</c><00:04:11.180><c> at</c><00:04:11.420><c> koala</c><00:04:11.780><c> CMS</c>

00:04:12.250 --> 00:04:12.260 align:start position:0%
who Lisa there is Alicia at koala CMS
 

00:04:12.260 --> 00:04:14.080 align:start position:0%
who Lisa there is Alicia at koala CMS
comma<00:04:12.709><c> and</c><00:04:12.860><c> there</c><00:04:13.010><c> is</c><00:04:13.220><c> the</c><00:04:13.459><c> client</c><00:04:13.760><c> theme</c><00:04:13.940><c> is</c>

00:04:14.080 --> 00:04:14.090 align:start position:0%
comma and there is the client theme is
 

00:04:14.090 --> 00:04:16.690 align:start position:0%
comma and there is the client theme is
flatly<00:04:14.660><c> okay</c><00:04:15.620><c> so</c><00:04:15.680><c> that</c><00:04:15.980><c> takes</c><00:04:16.160><c> care</c><00:04:16.430><c> of</c><00:04:16.489><c> that</c>

00:04:16.690 --> 00:04:16.700 align:start position:0%
flatly okay so that takes care of that
 

00:04:16.700 --> 00:04:19.000 align:start position:0%
flatly okay so that takes care of that
we've<00:04:17.090><c> actually</c><00:04:17.299><c> pushed</c><00:04:17.720><c> it</c><00:04:17.900><c> to</c><00:04:18.020><c> Roku</c><00:04:18.290><c> if</c><00:04:18.890><c> you</c>

00:04:19.000 --> 00:04:19.010 align:start position:0%
we've actually pushed it to Roku if you
 

00:04:19.010 --> 00:04:20.590 align:start position:0%
we've actually pushed it to Roku if you
guys<00:04:19.130><c> have</c><00:04:19.340><c> any</c><00:04:19.459><c> questions</c><00:04:19.850><c> feel</c><00:04:20.060><c> free</c><00:04:20.120><c> to</c><00:04:20.359><c> ask</c>

00:04:20.590 --> 00:04:20.600 align:start position:0%
guys have any questions feel free to ask
 

00:04:20.600 --> 00:04:22.420 align:start position:0%
guys have any questions feel free to ask
but<00:04:20.989><c> in</c><00:04:21.109><c> this</c><00:04:21.229><c> one</c><00:04:21.500><c> we</c><00:04:21.739><c> did</c><00:04:21.890><c> a</c><00:04:21.919><c> pretty</c><00:04:22.190><c> good</c><00:04:22.370><c> job</c>

00:04:22.420 --> 00:04:22.430 align:start position:0%
but in this one we did a pretty good job
 

00:04:22.430 --> 00:04:26.050 align:start position:0%
but in this one we did a pretty good job
here<00:04:23.060><c> and</c><00:04:23.240><c> I</c><00:04:24.200><c> can</c><00:04:24.590><c> do</c><00:04:24.740><c> also</c><00:04:25.280><c> I</c><00:04:25.490><c> want</c><00:04:25.790><c> to</c>

00:04:26.050 --> 00:04:26.060 align:start position:0%
here and I can do also I want to
 

00:04:26.060 --> 00:04:28.810 align:start position:0%
here and I can do also I want to
today<00:04:26.240><c> my</c><00:04:26.950><c> say</c><00:04:27.950><c> I</c><00:04:27.980><c> want</c><00:04:28.220><c> to</c><00:04:28.310><c> update</c><00:04:28.639><c> it</c><00:04:28.790><c> to</c>

00:04:28.810 --> 00:04:28.820 align:start position:0%
today my say I want to update it to
 

00:04:28.820 --> 00:04:33.550 align:start position:0%
today my say I want to update it to
slate<00:04:29.860><c> my</c><00:04:30.860><c> new</c><00:04:31.070><c> theme</c><00:04:31.700><c> and</c><00:04:32.000><c> I</c><00:04:32.900><c> want</c><00:04:33.080><c> to</c><00:04:33.200><c> reload</c>

00:04:33.550 --> 00:04:33.560 align:start position:0%
slate my new theme and I want to reload
 

00:04:33.560 --> 00:04:38.520 align:start position:0%
slate my new theme and I want to reload
the<00:04:33.740><c> page</c><00:04:34.660><c> okay</c><00:04:36.460><c> it's</c><00:04:37.460><c> still</c><00:04:37.669><c> gonna</c><00:04:37.850><c> be</c><00:04:37.940><c> slate</c>

00:04:38.520 --> 00:04:38.530 align:start position:0%
the page okay it's still gonna be slate
 

00:04:38.530 --> 00:04:43.510 align:start position:0%
the page okay it's still gonna be slate
you<00:04:39.530><c> know</c><00:04:39.560><c> what</c><00:04:39.800><c> I</c><00:04:39.889><c> can</c><00:04:40.070><c> do</c><00:04:40.220><c> I</c><00:04:40.430><c> can</c><00:04:40.550><c> do</c><00:04:40.910><c> this</c><00:04:42.520><c> I'm</c>

00:04:43.510 --> 00:04:43.520 align:start position:0%
you know what I can do I can do this I'm
 

00:04:43.520 --> 00:04:48.460 align:start position:0%
you know what I can do I can do this I'm
gonna<00:04:43.669><c> go</c><00:04:43.940><c> into</c><00:04:44.690><c> our</c><00:04:45.340><c> index</c><00:04:46.340><c> dot</c><00:04:46.610><c> ejs</c><00:04:47.470><c> I'm</c>

00:04:48.460 --> 00:04:48.470 align:start position:0%
gonna go into our index dot ejs I'm
 

00:04:48.470 --> 00:04:53.170 align:start position:0%
gonna go into our index dot ejs I'm
gonna<00:04:49.220><c> move</c><00:04:49.490><c> that</c><00:04:49.700><c> into</c><00:04:51.010><c> the</c><00:04:52.010><c> head</c><00:04:52.280><c> okay</c>

00:04:53.170 --> 00:04:53.180 align:start position:0%
gonna move that into the head okay
 

00:04:53.180 --> 00:04:59.200 align:start position:0%
gonna move that into the head okay
and<00:04:54.610><c> reload</c><00:04:55.610><c> the</c><00:04:55.820><c> page</c><00:04:56.030><c> and</c><00:04:56.330><c> see</c><00:04:56.540><c> what</c><00:04:56.720><c> happens</c>

