WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:02.149 align:start position:0%
 
okay<00:00:00.320><c> so</c><00:00:00.520><c> this</c><00:00:00.640><c> is</c><00:00:00.880><c> the</c><00:00:01.120><c> third</c><00:00:01.400><c> and</c><00:00:01.719><c> last</c>

00:00:02.149 --> 00:00:02.159 align:start position:0%
okay so this is the third and last
 

00:00:02.159 --> 00:00:05.670 align:start position:0%
okay so this is the third and last
wrap-up<00:00:02.760><c> video</c><00:00:03.399><c> of</c><00:00:03.840><c> Google</c><00:00:04.279><c> IO</c><00:00:05.279><c> and</c><00:00:05.400><c> I</c><00:00:05.480><c> want</c><00:00:05.560><c> to</c>

00:00:05.670 --> 00:00:05.680 align:start position:0%
wrap-up video of Google IO and I want to
 

00:00:05.680 --> 00:00:07.309 align:start position:0%
wrap-up video of Google IO and I want to
go<00:00:05.839><c> through</c><00:00:06.120><c> three</c><00:00:06.480><c> things</c><00:00:06.839><c> that</c><00:00:06.960><c> are</c><00:00:07.120><c> all</c>

00:00:07.309 --> 00:00:07.319 align:start position:0%
go through three things that are all
 

00:00:07.319 --> 00:00:09.950 align:start position:0%
go through three things that are all
sort<00:00:07.520><c> of</c><00:00:07.680><c> geared</c><00:00:08.160><c> at</c><00:00:08.480><c> developers</c><00:00:09.400><c> rather</c><00:00:09.679><c> than</c>

00:00:09.950 --> 00:00:09.960 align:start position:0%
sort of geared at developers rather than
 

00:00:09.960 --> 00:00:12.310 align:start position:0%
sort of geared at developers rather than
Google's<00:00:10.599><c> proprietary</c><00:00:11.120><c> models</c><00:00:11.639><c> like</c><00:00:11.880><c> Gemini</c>

00:00:12.310 --> 00:00:12.320 align:start position:0%
Google's proprietary models like Gemini
 

00:00:12.320 --> 00:00:14.190 align:start position:0%
Google's proprietary models like Gemini
which<00:00:12.400><c> I</c><00:00:12.519><c> covered</c><00:00:12.799><c> in</c><00:00:12.880><c> the</c><00:00:13.040><c> first</c><00:00:13.320><c> video</c><00:00:14.000><c> or</c>

00:00:14.190 --> 00:00:14.200 align:start position:0%
which I covered in the first video or
 

00:00:14.200 --> 00:00:16.029 align:start position:0%
which I covered in the first video or
their<00:00:14.360><c> whole</c><00:00:14.559><c> strategy</c><00:00:15.040><c> around</c><00:00:15.440><c> agents</c><00:00:15.920><c> which</c>

00:00:16.029 --> 00:00:16.039 align:start position:0%
their whole strategy around agents which
 

00:00:16.039 --> 00:00:17.910 align:start position:0%
their whole strategy around agents which
I<00:00:16.160><c> covered</c><00:00:16.400><c> in</c><00:00:16.520><c> the</c><00:00:16.680><c> second</c><00:00:17.000><c> video</c><00:00:17.640><c> in</c><00:00:17.800><c> this</c>

00:00:17.910 --> 00:00:17.920 align:start position:0%
I covered in the second video in this
 

00:00:17.920 --> 00:00:19.670 align:start position:0%
I covered in the second video in this
video<00:00:18.119><c> I</c><00:00:18.199><c> want</c><00:00:18.320><c> to</c><00:00:18.439><c> cover</c><00:00:18.720><c> stuff</c><00:00:19.000><c> that</c><00:00:19.160><c> you</c><00:00:19.320><c> can</c>

00:00:19.670 --> 00:00:19.680 align:start position:0%
video I want to cover stuff that you can
 

00:00:19.680 --> 00:00:23.070 align:start position:0%
video I want to cover stuff that you can
use<00:00:20.480><c> more</c><00:00:20.760><c> that</c><00:00:20.920><c> they</c><00:00:21.080><c> talked</c><00:00:21.400><c> about</c><00:00:21.800><c> there</c><00:00:22.600><c> so</c>

00:00:23.070 --> 00:00:23.080 align:start position:0%
use more that they talked about there so
 

00:00:23.080 --> 00:00:24.349 align:start position:0%
use more that they talked about there so
the<00:00:23.240><c> three</c><00:00:23.400><c> things</c><00:00:23.560><c> I'm</c><00:00:23.680><c> going</c><00:00:23.760><c> to</c><00:00:23.840><c> talk</c><00:00:24.039><c> about</c>

00:00:24.349 --> 00:00:24.359 align:start position:0%
the three things I'm going to talk about
 

00:00:24.359 --> 00:00:26.830 align:start position:0%
the three things I'm going to talk about
is<00:00:24.519><c> first</c><00:00:24.680><c> off</c><00:00:24.880><c> the</c><00:00:25.000><c> new</c><00:00:25.519><c> tpus</c><00:00:26.519><c> which</c><00:00:26.640><c> I'll</c>

00:00:26.830 --> 00:00:26.840 align:start position:0%
is first off the new tpus which I'll
 

00:00:26.840 --> 00:00:28.230 align:start position:0%
is first off the new tpus which I'll
talk<00:00:27.000><c> about</c><00:00:27.279><c> briefly</c><00:00:27.720><c> but</c><00:00:27.840><c> I</c><00:00:27.920><c> do</c><00:00:28.080><c> think</c>

00:00:28.230 --> 00:00:28.240 align:start position:0%
talk about briefly but I do think
 

00:00:28.240 --> 00:00:29.990 align:start position:0%
talk about briefly but I do think
they're<00:00:28.560><c> important</c><00:00:29.240><c> but</c><00:00:29.400><c> the</c><00:00:29.480><c> second</c><00:00:29.720><c> thing</c>

00:00:29.990 --> 00:00:30.000 align:start position:0%
they're important but the second thing
 

00:00:30.000 --> 00:00:31.749 align:start position:0%
they're important but the second thing
is<00:00:30.119><c> the</c><00:00:30.279><c> new</c><00:00:30.480><c> Gemma</c><00:00:30.920><c> models</c><00:00:31.400><c> which</c><00:00:31.560><c> I</c><00:00:31.640><c> think</c>

00:00:31.749 --> 00:00:31.759 align:start position:0%
is the new Gemma models which I think
 

00:00:31.759 --> 00:00:33.950 align:start position:0%
is the new Gemma models which I think
are<00:00:32.200><c> really</c><00:00:32.520><c> interesting</c><00:00:33.399><c> and</c><00:00:33.480><c> then</c><00:00:33.640><c> lastly</c>

00:00:33.950 --> 00:00:33.960 align:start position:0%
are really interesting and then lastly
 

00:00:33.960 --> 00:00:35.389 align:start position:0%
are really interesting and then lastly
I'll<00:00:34.120><c> talk</c><00:00:34.320><c> about</c><00:00:34.760><c> some</c><00:00:34.879><c> of</c><00:00:35.000><c> the</c><00:00:35.120><c> things</c><00:00:35.280><c> that</c>

00:00:35.389 --> 00:00:35.399 align:start position:0%
I'll talk about some of the things that
 

00:00:35.399 --> 00:00:37.670 align:start position:0%
I'll talk about some of the things that
are<00:00:35.520><c> coming</c><00:00:35.760><c> to</c><00:00:36.079><c> Firebase</c><00:00:37.079><c> and</c><00:00:37.239><c> perhaps</c><00:00:37.520><c> how</c>

00:00:37.670 --> 00:00:37.680 align:start position:0%
are coming to Firebase and perhaps how
 

00:00:37.680 --> 00:00:39.990 align:start position:0%
are coming to Firebase and perhaps how
they<00:00:37.840><c> opening</c><00:00:38.200><c> up</c><00:00:38.399><c> some</c><00:00:38.559><c> of</c><00:00:38.680><c> the</c><00:00:39.000><c> ecosystem</c>

00:00:39.990 --> 00:00:40.000 align:start position:0%
they opening up some of the ecosystem
 

00:00:40.000 --> 00:00:43.389 align:start position:0%
they opening up some of the ecosystem
for<00:00:40.760><c> languages</c><00:00:41.440><c> like</c><00:00:41.760><c> JavaScript</c><00:00:42.520><c> typescript</c>

00:00:43.389 --> 00:00:43.399 align:start position:0%
for languages like JavaScript typescript
 

00:00:43.399 --> 00:00:46.869 align:start position:0%
for languages like JavaScript typescript
and<00:00:43.640><c> even</c><00:00:43.920><c> taking</c><00:00:44.239><c> on</c><00:00:44.520><c> some</c><00:00:44.680><c> of</c><00:00:44.920><c> the</c><00:00:45.440><c> versel</c><00:00:46.320><c> AI</c>

00:00:46.869 --> 00:00:46.879 align:start position:0%
and even taking on some of the versel AI
 

00:00:46.879 --> 00:00:49.029 align:start position:0%
and even taking on some of the versel AI
kind<00:00:47.000><c> of</c><00:00:47.199><c> things</c><00:00:47.600><c> there</c><00:00:48.320><c> so</c><00:00:48.600><c> the</c><00:00:48.719><c> first</c><00:00:48.920><c> thing</c>

00:00:49.029 --> 00:00:49.039 align:start position:0%
kind of things there so the first thing
 

00:00:49.039 --> 00:00:51.350 align:start position:0%
kind of things there so the first thing
I<00:00:49.120><c> want</c><00:00:49.199><c> to</c><00:00:49.320><c> talk</c><00:00:49.480><c> about</c><00:00:49.760><c> is</c><00:00:49.879><c> the</c><00:00:50.039><c> new</c><00:00:50.320><c> tpus</c><00:00:51.199><c> so</c>

00:00:51.350 --> 00:00:51.360 align:start position:0%
I want to talk about is the new tpus so
 

00:00:51.360 --> 00:00:53.670 align:start position:0%
I want to talk about is the new tpus so
the<00:00:51.480><c> new</c><00:00:51.640><c> tpus</c><00:00:52.160><c> were</c><00:00:52.359><c> announced</c><00:00:53.199><c> it's</c><00:00:53.399><c> called</c>

00:00:53.670 --> 00:00:53.680 align:start position:0%
the new tpus were announced it's called
 

00:00:53.680 --> 00:00:56.389 align:start position:0%
the new tpus were announced it's called
Trillium<00:00:54.680><c> this</c><00:00:54.800><c> is</c><00:00:55.000><c> the</c><00:00:55.199><c> sixth</c><00:00:55.640><c> generation</c>

00:00:56.389 --> 00:00:56.399 align:start position:0%
Trillium this is the sixth generation
 

00:00:56.399 --> 00:00:58.950 align:start position:0%
Trillium this is the sixth generation
tpus<00:00:57.239><c> now</c><00:00:57.879><c> for</c><00:00:58.079><c> those</c><00:00:58.239><c> of</c><00:00:58.359><c> you</c><00:00:58.480><c> who</c><00:00:58.600><c> don't</c><00:00:58.760><c> know</c>

00:00:58.950 --> 00:00:58.960 align:start position:0%
tpus now for those of you who don't know
 

00:00:58.960 --> 00:01:00.709 align:start position:0%
tpus now for those of you who don't know
the<00:00:59.079><c> first</c><00:00:59.320><c> generation</c><00:00:59.719><c> was</c><00:01:00.000><c> for</c><00:01:00.160><c> inference</c>

00:01:00.709 --> 00:01:00.719 align:start position:0%
the first generation was for inference
 

00:01:00.719 --> 00:01:03.349 align:start position:0%
the first generation was for inference
only<00:01:01.719><c> and</c><00:01:01.840><c> then</c><00:01:02.000><c> each</c><00:01:02.239><c> generation</c><00:01:03.000><c> basically</c>

00:01:03.349 --> 00:01:03.359 align:start position:0%
only and then each generation basically
 

00:01:03.359 --> 00:01:06.310 align:start position:0%
only and then each generation basically
has<00:01:03.519><c> been</c><00:01:03.720><c> training</c><00:01:04.720><c> although</c><00:01:05.080><c> the</c><00:01:05.239><c> TPU</c><00:01:05.799><c> 5S</c>

00:01:06.310 --> 00:01:06.320 align:start position:0%
has been training although the TPU 5S
 

00:01:06.320 --> 00:01:08.030 align:start position:0%
has been training although the TPU 5S
had<00:01:06.640><c> a</c><00:01:06.799><c> version</c><00:01:07.159><c> that</c><00:01:07.360><c> specialized</c><00:01:07.840><c> in</c>

00:01:08.030 --> 00:01:08.040 align:start position:0%
had a version that specialized in
 

00:01:08.040 --> 00:01:10.390 align:start position:0%
had a version that specialized in
inference<00:01:08.600><c> as</c><00:01:08.759><c> well</c><00:01:09.600><c> okay</c><00:01:09.880><c> why</c><00:01:10.040><c> is</c><00:01:10.200><c> this</c>

00:01:10.390 --> 00:01:10.400 align:start position:0%
inference as well okay why is this
 

00:01:10.400 --> 00:01:11.950 align:start position:0%
inference as well okay why is this
important<00:01:10.799><c> and</c><00:01:11.000><c> what</c><00:01:11.119><c> does</c><00:01:11.360><c> this</c><00:01:11.640><c> actually</c>

00:01:11.950 --> 00:01:11.960 align:start position:0%
important and what does this actually
 

00:01:11.960 --> 00:01:14.710 align:start position:0%
important and what does this actually
mean<00:01:12.600><c> first</c><00:01:12.880><c> off</c><00:01:13.439><c> you</c><00:01:13.560><c> should</c><00:01:14.240><c> understand</c>

00:01:14.710 --> 00:01:14.720 align:start position:0%
mean first off you should understand
 

00:01:14.720 --> 00:01:17.710 align:start position:0%
mean first off you should understand
that<00:01:15.080><c> the</c><00:01:15.600><c> Gemini</c><00:01:16.520><c> Ultra</c><00:01:16.960><c> model</c><00:01:17.320><c> which</c><00:01:17.400><c> is</c><00:01:17.560><c> the</c>

00:01:17.710 --> 00:01:17.720 align:start position:0%
that the Gemini Ultra model which is the
 

00:01:17.720 --> 00:01:20.030 align:start position:0%
that the Gemini Ultra model which is the
biggest<00:01:18.119><c> Gemini</c><00:01:18.759><c> model</c><00:01:19.560><c> was</c><00:01:19.799><c> actually</c>

00:01:20.030 --> 00:01:20.040 align:start position:0%
biggest Gemini model was actually
 

00:01:20.040 --> 00:01:23.230 align:start position:0%
biggest Gemini model was actually
trained<00:01:20.439><c> on</c><00:01:20.680><c> TPU</c><00:01:21.439><c> 4S</c><00:01:22.439><c> right</c><00:01:22.640><c> and</c><00:01:22.720><c> it</c><00:01:22.880><c> took</c><00:01:23.159><c> a</c>

00:01:23.230 --> 00:01:23.240 align:start position:0%
trained on TPU 4S right and it took a
 

00:01:23.240 --> 00:01:25.390 align:start position:0%
trained on TPU 4S right and it took a
bit<00:01:23.400><c> over</c><00:01:23.680><c> 3</c><00:01:24.040><c> months</c><00:01:24.400><c> apparently</c><00:01:24.840><c> to</c><00:01:25.040><c> do</c><00:01:25.240><c> the</c>

00:01:25.390 --> 00:01:25.400 align:start position:0%
bit over 3 months apparently to do the
 

00:01:25.400 --> 00:01:28.630 align:start position:0%
bit over 3 months apparently to do the
training<00:01:26.000><c> there</c><00:01:26.640><c> we</c><00:01:26.799><c> know</c><00:01:27.040><c> that</c><00:01:27.240><c> the</c><00:01:27.360><c> TPU</c><00:01:28.000><c> 5S</c>

00:01:28.630 --> 00:01:28.640 align:start position:0%
training there we know that the TPU 5S
 

00:01:28.640 --> 00:01:32.109 align:start position:0%
training there we know that the TPU 5S
were<00:01:29.000><c> about</c><00:01:29.560><c> two</c><00:01:29.920><c> 2.7</c><00:01:30.479><c> 2.8</c><00:01:31.159><c> times</c><00:01:31.600><c> faster</c><00:01:32.040><c> I</c>

00:01:32.109 --> 00:01:32.119 align:start position:0%
were about two 2.7 2.8 times faster I
 

00:01:32.119 --> 00:01:35.630 align:start position:0%
were about two 2.7 2.8 times faster I
think<00:01:32.280><c> from</c><00:01:32.479><c> memory</c><00:01:33.240><c> than</c><00:01:33.399><c> the</c><00:01:33.560><c> TPU</c><00:01:34.360><c> 4S</c><00:01:35.360><c> so</c><00:01:35.520><c> you</c>

00:01:35.630 --> 00:01:35.640 align:start position:0%
think from memory than the TPU 4S so you
 

00:01:35.640 --> 00:01:37.429 align:start position:0%
think from memory than the TPU 4S so you
could<00:01:35.840><c> imagine</c><00:01:36.399><c> that's</c><00:01:36.640><c> made</c><00:01:36.880><c> training</c><00:01:37.240><c> a</c>

00:01:37.429 --> 00:01:37.439 align:start position:0%
could imagine that's made training a
 

00:01:37.439 --> 00:01:39.870 align:start position:0%
could imagine that's made training a
model<00:01:37.720><c> on</c><00:01:37.960><c> the</c><00:01:38.159><c> size</c><00:01:38.439><c> of</c><00:01:38.680><c> that</c><00:01:38.880><c> Ultra</c><00:01:39.280><c> model</c><00:01:39.600><c> go</c>

00:01:39.870 --> 00:01:39.880 align:start position:0%
model on the size of that Ultra model go
 

00:01:39.880 --> 00:01:43.910 align:start position:0%
model on the size of that Ultra model go
from<00:01:40.159><c> 3</c><00:01:40.479><c> months</c><00:01:41.439><c> down</c><00:01:41.680><c> to</c><00:01:41.920><c> a</c><00:01:42.040><c> bit</c><00:01:42.240><c> over</c><00:01:43.000><c> a</c><00:01:43.240><c> month</c>

00:01:43.910 --> 00:01:43.920 align:start position:0%
from 3 months down to a bit over a month
 

00:01:43.920 --> 00:01:47.030 align:start position:0%
from 3 months down to a bit over a month
now<00:01:44.119><c> with</c><00:01:44.240><c> the</c><00:01:44.439><c> announcement</c><00:01:45.280><c> of</c><00:01:45.640><c> the</c><00:01:46.040><c> Sixers</c>

00:01:47.030 --> 00:01:47.040 align:start position:0%
now with the announcement of the Sixers
 

00:01:47.040 --> 00:01:49.069 align:start position:0%
now with the announcement of the Sixers
they're<00:01:47.320><c> basically</c><00:01:47.799><c> saying</c><00:01:48.200><c> that</c><00:01:48.479><c> these</c><00:01:48.799><c> are</c>

00:01:49.069 --> 00:01:49.079 align:start position:0%
they're basically saying that these are
 

00:01:49.079 --> 00:01:52.709 align:start position:0%
they're basically saying that these are
up<00:01:49.280><c> to</c><00:01:50.240><c> not</c><00:01:50.479><c> always</c><00:01:50.960><c> perhaps</c><00:01:51.360><c> but</c><00:01:51.560><c> up</c><00:01:51.719><c> to</c><00:01:51.960><c> 4.7</c>

00:01:52.709 --> 00:01:52.719 align:start position:0%
up to not always perhaps but up to 4.7
 

00:01:52.719 --> 00:01:57.510 align:start position:0%
up to not always perhaps but up to 4.7
times<00:01:53.320><c> faster</c><00:01:54.320><c> than</c><00:01:54.560><c> the</c><00:01:54.680><c> TPU</c><00:01:55.560><c> 5S</c><00:01:56.560><c> so</c><00:01:57.079><c> this</c><00:01:57.200><c> is</c>

00:01:57.510 --> 00:01:57.520 align:start position:0%
times faster than the TPU 5S so this is
 

00:01:57.520 --> 00:02:00.190 align:start position:0%
times faster than the TPU 5S so this is
insane<00:01:58.240><c> that</c><00:01:58.600><c> just</c><00:01:58.759><c> in</c><00:01:58.920><c> the</c><00:01:59.119><c> space</c><00:01:59.520><c> of</c><00:01:59.960><c> for</c>

00:02:00.190 --> 00:02:00.200 align:start position:0%
insane that just in the space of for
 

00:02:00.200 --> 00:02:02.630 align:start position:0%
insane that just in the space of for
year<00:02:00.560><c> we've</c><00:02:00.799><c> gone</c><00:02:01.039><c> from</c><00:02:01.240><c> having</c><00:02:01.840><c> the</c><00:02:02.000><c> largest</c>

00:02:02.630 --> 00:02:02.640 align:start position:0%
year we've gone from having the largest
 

00:02:02.640 --> 00:02:05.310 align:start position:0%
year we've gone from having the largest
Gemini<00:02:03.079><c> model</c><00:02:03.439><c> that</c><00:02:03.600><c> Google's</c><00:02:04.119><c> got</c><00:02:04.799><c> taking</c>

00:02:05.310 --> 00:02:05.320 align:start position:0%
Gemini model that Google's got taking
 

00:02:05.320 --> 00:02:08.469 align:start position:0%
Gemini model that Google's got taking
over<00:02:05.600><c> 3</c><00:02:05.799><c> months</c><00:02:06.079><c> to</c><00:02:06.280><c> train</c><00:02:07.039><c> to</c><00:02:07.520><c> now</c><00:02:08.000><c> where</c><00:02:08.239><c> they</c>

00:02:08.469 --> 00:02:08.479 align:start position:0%
over 3 months to train to now where they
 

00:02:08.479 --> 00:02:10.910 align:start position:0%
over 3 months to train to now where they
probably<00:02:08.879><c> could</c><00:02:09.039><c> train</c><00:02:09.640><c> something</c><00:02:10.119><c> similar</c>

00:02:10.910 --> 00:02:10.920 align:start position:0%
probably could train something similar
 

00:02:10.920 --> 00:02:13.550 align:start position:0%
probably could train something similar
in<00:02:11.080><c> about</c><00:02:11.280><c> a</c><00:02:11.440><c> week</c><00:02:11.640><c> or</c><00:02:11.879><c> under</c><00:02:12.800><c> so</c><00:02:13.080><c> that's</c><00:02:13.200><c> a</c>

00:02:13.550 --> 00:02:13.560 align:start position:0%
in about a week or under so that's a
 

00:02:13.560 --> 00:02:15.869 align:start position:0%
in about a week or under so that's a
huge<00:02:14.239><c> change</c><00:02:14.800><c> in</c><00:02:15.040><c> things</c><00:02:15.319><c> and</c><00:02:15.440><c> I</c><00:02:15.519><c> think</c><00:02:15.680><c> we're</c>

00:02:15.869 --> 00:02:15.879 align:start position:0%
huge change in things and I think we're
 

00:02:15.879 --> 00:02:18.070 align:start position:0%
huge change in things and I think we're
seeing<00:02:16.200><c> this</c><00:02:16.440><c> across</c><00:02:16.720><c> the</c><00:02:16.879><c> industry</c><00:02:17.840><c> if</c><00:02:17.920><c> we</c>

00:02:18.070 --> 00:02:18.080 align:start position:0%
seeing this across the industry if we
 

00:02:18.080 --> 00:02:21.509 align:start position:0%
seeing this across the industry if we
look<00:02:18.200><c> at</c><00:02:18.480><c> the</c><00:02:18.599><c> new</c><00:02:18.800><c> gbt</c><00:02:19.280><c> 4</c><00:02:20.120><c> model</c><00:02:20.920><c> clearly</c><00:02:21.319><c> that</c>

00:02:21.509 --> 00:02:21.519 align:start position:0%
look at the new gbt 4 model clearly that
 

00:02:21.519 --> 00:02:23.910 align:start position:0%
look at the new gbt 4 model clearly that
is<00:02:21.720><c> like</c><00:02:21.840><c> a</c><00:02:22.000><c> whole</c><00:02:22.200><c> new</c><00:02:22.440><c> trained</c><00:02:23.080><c> model</c><00:02:23.599><c> not</c>

00:02:23.910 --> 00:02:23.920 align:start position:0%
is like a whole new trained model not
 

00:02:23.920 --> 00:02:27.270 align:start position:0%
is like a whole new trained model not
just<00:02:24.360><c> a</c><00:02:24.440><c> sort</c><00:02:24.599><c> of</c><00:02:24.720><c> fine-tuned</c><00:02:25.440><c> version</c><00:02:25.720><c> of</c><00:02:26.280><c> gp4</c>

00:02:27.270 --> 00:02:27.280 align:start position:0%
just a sort of fine-tuned version of gp4
 

00:02:27.280 --> 00:02:29.630 align:start position:0%
just a sort of fine-tuned version of gp4
the<00:02:27.360><c> fact</c><00:02:27.599><c> it's</c><00:02:27.720><c> got</c><00:02:27.800><c> a</c><00:02:27.920><c> new</c><00:02:28.080><c> tokenizer</c><00:02:28.840><c> Etc</c>

00:02:29.630 --> 00:02:29.640 align:start position:0%
the fact it's got a new tokenizer Etc
 

00:02:29.640 --> 00:02:32.110 align:start position:0%
the fact it's got a new tokenizer Etc
that's<00:02:29.959><c> a</c><00:02:30.080><c> big</c><00:02:30.360><c> deal</c><00:02:31.200><c> but</c><00:02:31.400><c> while</c><00:02:31.599><c> everyone</c><00:02:31.920><c> is</c>

00:02:32.110 --> 00:02:32.120 align:start position:0%
that's a big deal but while everyone is
 

00:02:32.120 --> 00:02:35.110 align:start position:0%
that's a big deal but while everyone is
scrambling<00:02:32.720><c> for</c><00:02:33.080><c> compute</c><00:02:34.000><c> out</c><00:02:34.239><c> there</c><00:02:34.400><c> and</c><00:02:34.680><c> ex</c>

00:02:35.110 --> 00:02:35.120 align:start position:0%
scrambling for compute out there and ex
 

00:02:35.120 --> 00:02:38.710 align:start position:0%
scrambling for compute out there and ex
scrambling<00:02:35.680><c> for</c><00:02:36.000><c> their</c><00:02:36.440><c> Nvidia</c><00:02:37.400><c> gpus</c><00:02:38.400><c> Google</c>

00:02:38.710 --> 00:02:38.720 align:start position:0%
scrambling for their Nvidia gpus Google
 

00:02:38.720 --> 00:02:42.030 align:start position:0%
scrambling for their Nvidia gpus Google
is<00:02:38.879><c> able</c><00:02:39.159><c> to</c><00:02:39.400><c> just</c><00:02:39.720><c> make</c><00:02:40.519><c> these</c><00:02:40.760><c> tpus</c><00:02:41.640><c> and</c>

00:02:42.030 --> 00:02:42.040 align:start position:0%
is able to just make these tpus and
 

00:02:42.040 --> 00:02:43.790 align:start position:0%
is able to just make these tpus and
because<00:02:42.480><c> they</c><00:02:42.840><c> make</c><00:02:43.040><c> them</c><00:02:43.440><c> themselves</c>

00:02:43.790 --> 00:02:43.800 align:start position:0%
because they make them themselves
 

00:02:43.800 --> 00:02:45.869 align:start position:0%
because they make them themselves
they're<00:02:43.959><c> not</c><00:02:44.159><c> buying</c><00:02:44.519><c> them</c><00:02:44.680><c> from</c><00:02:44.879><c> anyone</c>

00:02:45.869 --> 00:02:45.879 align:start position:0%
they're not buying them from anyone
 

00:02:45.879 --> 00:02:47.550 align:start position:0%
they're not buying them from anyone
really<00:02:46.239><c> you</c><00:02:46.319><c> can</c><00:02:46.519><c> imagine</c><00:02:46.920><c> these</c><00:02:47.080><c> things</c><00:02:47.319><c> are</c>

00:02:47.550 --> 00:02:47.560 align:start position:0%
really you can imagine these things are
 

00:02:47.560 --> 00:02:50.750 align:start position:0%
really you can imagine these things are
not<00:02:47.879><c> costing</c><00:02:48.360><c> them</c><00:02:49.080><c> anywhere</c><00:02:49.599><c> near</c><00:02:50.360><c> the</c><00:02:50.519><c> cost</c>

00:02:50.750 --> 00:02:50.760 align:start position:0%
not costing them anywhere near the cost
 

00:02:50.760 --> 00:02:52.990 align:start position:0%
not costing them anywhere near the cost
of<00:02:50.959><c> what</c><00:02:51.120><c> people</c><00:02:51.319><c> are</c><00:02:51.519><c> paying</c><00:02:51.920><c> for</c><00:02:52.480><c> NVIDIA</c>

00:02:52.990 --> 00:02:53.000 align:start position:0%
of what people are paying for NVIDIA
 

00:02:53.000 --> 00:02:55.589 align:start position:0%
of what people are paying for NVIDIA
gpus<00:02:53.760><c> here</c><00:02:54.480><c> I</c><00:02:54.560><c> do</c><00:02:54.720><c> think</c><00:02:54.879><c> this</c><00:02:55.000><c> is</c><00:02:55.120><c> a</c><00:02:55.280><c> pretty</c>

00:02:55.589 --> 00:02:55.599 align:start position:0%
gpus here I do think this is a pretty
 

00:02:55.599 --> 00:02:57.830 align:start position:0%
gpus here I do think this is a pretty
amazing<00:02:56.599><c> breakthrough</c><00:02:57.080><c> that</c><00:02:57.200><c> we're</c><00:02:57.360><c> seeing</c>

00:02:57.830 --> 00:02:57.840 align:start position:0%
amazing breakthrough that we're seeing
 

00:02:57.840 --> 00:03:00.149 align:start position:0%
amazing breakthrough that we're seeing
here<00:02:58.560><c> is</c><00:02:58.760><c> that</c><00:02:58.959><c> this</c><00:02:59.200><c> Compu</c><00:02:59.640><c> is</c><00:02:59.840><c> is</c><00:02:59.959><c> just</c>

00:03:00.149 --> 00:03:00.159 align:start position:0%
here is that this Compu is is just
 

00:03:00.159 --> 00:03:02.750 align:start position:0%
here is that this Compu is is just
getting<00:03:00.599><c> faster</c><00:03:01.040><c> and</c><00:03:01.400><c> faster</c><00:03:02.400><c> which</c><00:03:02.519><c> is</c><00:03:02.640><c> going</c>

00:03:02.750 --> 00:03:02.760 align:start position:0%
getting faster and faster which is going
 

00:03:02.760 --> 00:03:05.110 align:start position:0%
getting faster and faster which is going
to<00:03:02.959><c> allow</c><00:03:03.239><c> for</c><00:03:03.519><c> bigger</c><00:03:04.000><c> models</c><00:03:04.599><c> and</c><00:03:04.799><c> basically</c>

00:03:05.110 --> 00:03:05.120 align:start position:0%
to allow for bigger models and basically
 

00:03:05.120 --> 00:03:07.390 align:start position:0%
to allow for bigger models and basically
training<00:03:05.480><c> for</c><00:03:05.720><c> a</c><00:03:05.879><c> lot</c><00:03:06.080><c> more</c><00:03:06.360><c> tokens</c><00:03:06.879><c> Etc</c><00:03:07.319><c> as</c>

00:03:07.390 --> 00:03:07.400 align:start position:0%
training for a lot more tokens Etc as
 

00:03:07.400 --> 00:03:09.350 align:start position:0%
training for a lot more tokens Etc as
you<00:03:07.560><c> go</c><00:03:07.840><c> through</c><00:03:08.200><c> here</c><00:03:08.640><c> so</c><00:03:08.840><c> I'll</c><00:03:08.959><c> leave</c><00:03:09.120><c> it</c><00:03:09.239><c> for</c>

00:03:09.350 --> 00:03:09.360 align:start position:0%
you go through here so I'll leave it for
 

00:03:09.360 --> 00:03:10.869 align:start position:0%
you go through here so I'll leave it for
you<00:03:09.480><c> to</c><00:03:09.599><c> go</c><00:03:09.760><c> through</c><00:03:09.920><c> the</c><00:03:10.040><c> article</c><00:03:10.360><c> and</c><00:03:10.640><c> read</c>

00:03:10.869 --> 00:03:10.879 align:start position:0%
you to go through the article and read
 

00:03:10.879 --> 00:03:12.630 align:start position:0%
you to go through the article and read
this<00:03:11.040><c> but</c><00:03:11.159><c> I</c><00:03:11.280><c> think</c><00:03:11.480><c> this</c><00:03:11.599><c> is</c><00:03:12.040><c> one</c><00:03:12.280><c> thing</c><00:03:12.480><c> that</c>

00:03:12.630 --> 00:03:12.640 align:start position:0%
this but I think this is one thing that
 

00:03:12.640 --> 00:03:14.390 align:start position:0%
this but I think this is one thing that
a<00:03:12.760><c> lot</c><00:03:12.879><c> of</c><00:03:13.000><c> people</c><00:03:13.280><c> have</c><00:03:13.519><c> just</c><00:03:13.760><c> overlooked</c>

00:03:14.390 --> 00:03:14.400 align:start position:0%
a lot of people have just overlooked
 

00:03:14.400 --> 00:03:17.270 align:start position:0%
a lot of people have just overlooked
totally<00:03:15.280><c> that</c><00:03:15.440><c> came</c><00:03:15.760><c> out</c><00:03:16.239><c> of</c><00:03:16.400><c> the</c><00:03:16.560><c> io</c>

00:03:17.270 --> 00:03:17.280 align:start position:0%
totally that came out of the io
 

00:03:17.280 --> 00:03:19.309 align:start position:0%
totally that came out of the io
announcements<00:03:18.280><c> so</c><00:03:18.480><c> the</c><00:03:18.599><c> whole</c><00:03:18.799><c> purpose</c><00:03:19.120><c> of</c>

00:03:19.309 --> 00:03:19.319 align:start position:0%
announcements so the whole purpose of
 

00:03:19.319 --> 00:03:22.670 align:start position:0%
announcements so the whole purpose of
including<00:03:20.120><c> the</c><00:03:20.560><c> uh</c><00:03:20.720><c> TPU</c><00:03:21.480><c> stuff</c><00:03:21.799><c> in</c><00:03:22.080><c> here</c><00:03:22.400><c> is</c><00:03:22.560><c> I</c>

00:03:22.670 --> 00:03:22.680 align:start position:0%
including the uh TPU stuff in here is I
 

00:03:22.680 --> 00:03:24.910 align:start position:0%
including the uh TPU stuff in here is I
kind<00:03:22.799><c> of</c><00:03:22.879><c> feel</c><00:03:23.200><c> that</c><00:03:23.720><c> developers</c><00:03:24.239><c> need</c><00:03:24.400><c> to</c><00:03:24.560><c> be</c>

00:03:24.910 --> 00:03:24.920 align:start position:0%
kind of feel that developers need to be
 

00:03:24.920 --> 00:03:27.949 align:start position:0%
kind of feel that developers need to be
getting<00:03:25.319><c> prepared</c><00:03:26.319><c> for</c><00:03:27.080><c> being</c><00:03:27.360><c> able</c><00:03:27.599><c> to</c><00:03:27.760><c> do</c>

00:03:27.949 --> 00:03:27.959 align:start position:0%
getting prepared for being able to do
 

00:03:27.959 --> 00:03:31.229 align:start position:0%
getting prepared for being able to do
fine<00:03:28.319><c> tunes</c><00:03:28.840><c> on</c><00:03:29.080><c> TPU</c><00:03:30.239><c> so</c><00:03:30.439><c> Google</c><00:03:30.879><c> announced</c>

00:03:31.229 --> 00:03:31.239 align:start position:0%
fine tunes on TPU so Google announced
 

00:03:31.239 --> 00:03:32.789 align:start position:0%
fine tunes on TPU so Google announced
already<00:03:31.480><c> they're</c><00:03:31.640><c> going</c><00:03:31.720><c> to</c><00:03:31.840><c> make</c><00:03:32.040><c> these</c><00:03:32.200><c> tpus</c>

00:03:32.789 --> 00:03:32.799 align:start position:0%
already they're going to make these tpus
 

00:03:32.799 --> 00:03:34.550 align:start position:0%
already they're going to make these tpus
available<00:03:33.239><c> to</c><00:03:33.439><c> people</c><00:03:33.840><c> at</c><00:03:33.920><c> the</c><00:03:34.040><c> end</c><00:03:34.200><c> of</c><00:03:34.360><c> the</c>

00:03:34.550 --> 00:03:34.560 align:start position:0%
available to people at the end of the
 

00:03:34.560 --> 00:03:36.509 align:start position:0%
available to people at the end of the
year<00:03:35.239><c> and</c><00:03:35.319><c> I</c><00:03:35.439><c> think</c><00:03:35.519><c> in</c><00:03:35.680><c> the</c><00:03:35.840><c> find</c><00:03:36.120><c> pricing</c>

00:03:36.509 --> 00:03:36.519 align:start position:0%
year and I think in the find pricing
 

00:03:36.519 --> 00:03:37.750 align:start position:0%
year and I think in the find pricing
wise<00:03:36.760><c> this</c><00:03:36.879><c> is</c><00:03:37.000><c> going</c><00:03:37.080><c> to</c><00:03:37.200><c> be</c><00:03:37.400><c> really</c>

00:03:37.750 --> 00:03:37.760 align:start position:0%
wise this is going to be really
 

00:03:37.760 --> 00:03:41.070 align:start position:0%
wise this is going to be really
reasonable<00:03:38.519><c> compared</c><00:03:38.959><c> to</c><00:03:39.200><c> training</c><00:03:39.640><c> on</c><00:03:40.080><c> gpus</c>

00:03:41.070 --> 00:03:41.080 align:start position:0%
reasonable compared to training on gpus
 

00:03:41.080 --> 00:03:42.869 align:start position:0%
reasonable compared to training on gpus
so<00:03:41.439><c> you</c><00:03:41.599><c> probably</c><00:03:41.879><c> will</c><00:03:42.040><c> be</c><00:03:42.200><c> able</c><00:03:42.400><c> to</c><00:03:42.599><c> take</c>

00:03:42.869 --> 00:03:42.879 align:start position:0%
so you probably will be able to take
 

00:03:42.879 --> 00:03:45.509 align:start position:0%
so you probably will be able to take
some<00:03:43.040><c> of</c><00:03:43.239><c> these</c><00:03:43.439><c> models</c><00:03:43.879><c> like</c><00:03:44.080><c> a</c><00:03:44.239><c> Gemma</c><00:03:44.640><c> 2</c><00:03:45.400><c> and</c>

00:03:45.509 --> 00:03:45.519 align:start position:0%
some of these models like a Gemma 2 and
 

00:03:45.519 --> 00:03:48.309 align:start position:0%
some of these models like a Gemma 2 and
find<00:03:45.920><c> tune</c><00:03:46.200><c> it</c><00:03:46.599><c> quite</c><00:03:46.920><c> cheaply</c><00:03:47.840><c> perhaps</c><00:03:48.159><c> not</c>

00:03:48.309 --> 00:03:48.319 align:start position:0%
find tune it quite cheaply perhaps not
 

00:03:48.319 --> 00:03:50.910 align:start position:0%
find tune it quite cheaply perhaps not
totally<00:03:48.799><c> easy</c><00:03:49.360><c> but</c><00:03:49.640><c> quite</c><00:03:49.879><c> cheaply</c><00:03:50.680><c> going</c>

00:03:50.910 --> 00:03:50.920 align:start position:0%
totally easy but quite cheaply going
 

00:03:50.920 --> 00:03:52.869 align:start position:0%
totally easy but quite cheaply going
forward<00:03:51.280><c> for</c><00:03:51.560><c> this</c><00:03:52.200><c> the</c><00:03:52.319><c> second</c><00:03:52.560><c> thing</c><00:03:52.680><c> that</c><00:03:52.799><c> I</c>

00:03:52.869 --> 00:03:52.879 align:start position:0%
forward for this the second thing that I
 

00:03:52.879 --> 00:03:54.990 align:start position:0%
forward for this the second thing that I
wanted<00:03:53.040><c> to</c><00:03:53.159><c> jump</c><00:03:53.400><c> in</c><00:03:53.560><c> and</c><00:03:53.760><c> talk</c><00:03:54.000><c> about</c><00:03:54.439><c> is</c><00:03:54.840><c> the</c>

00:03:54.990 --> 00:03:55.000 align:start position:0%
wanted to jump in and talk about is the
 

00:03:55.000 --> 00:03:59.030 align:start position:0%
wanted to jump in and talk about is the
new<00:03:55.280><c> Gemma</c><00:03:55.720><c> models</c><00:03:56.720><c> first</c><00:03:57.040><c> off</c><00:03:57.319><c> is</c><00:03:57.599><c> py</c><00:03:58.159><c> Gemma</c>

00:03:59.030 --> 00:03:59.040 align:start position:0%
new Gemma models first off is py Gemma
 

00:03:59.040 --> 00:04:01.990 align:start position:0%
new Gemma models first off is py Gemma
so<00:03:59.280><c> basically</c><00:03:59.920><c> where</c><00:04:00.120><c> they're</c><00:04:00.560><c> combining</c><00:04:01.560><c> a</c>

00:04:01.990 --> 00:04:02.000 align:start position:0%
so basically where they're combining a
 

00:04:02.000 --> 00:04:05.110 align:start position:0%
so basically where they're combining a
Gemma<00:04:02.920><c> model</c><00:04:03.400><c> with</c><00:04:03.599><c> this</c><00:04:03.840><c> sig</c><00:04:04.239><c> lip</c><00:04:04.599><c> Vision</c>

00:04:05.110 --> 00:04:05.120 align:start position:0%
Gemma model with this sig lip Vision
 

00:04:05.120 --> 00:04:07.429 align:start position:0%
Gemma model with this sig lip Vision
model<00:04:05.519><c> in</c><00:04:05.760><c> here</c><00:04:06.519><c> so</c><00:04:06.840><c> this</c><00:04:06.920><c> is</c><00:04:07.120><c> actually</c><00:04:07.280><c> been</c>

00:04:07.429 --> 00:04:07.439 align:start position:0%
model in here so this is actually been
 

00:04:07.439 --> 00:04:10.470 align:start position:0%
model in here so this is actually been
used<00:04:07.760><c> in</c><00:04:07.920><c> other</c><00:04:08.159><c> VMS</c><00:04:08.879><c> out</c><00:04:09.200><c> there</c><00:04:09.879><c> and</c><00:04:10.079><c> so</c><00:04:10.360><c> it</c>

00:04:10.470 --> 00:04:10.480 align:start position:0%
used in other VMS out there and so it
 

00:04:10.480 --> 00:04:11.390 align:start position:0%
used in other VMS out there and so it
seems<00:04:10.720><c> like</c><00:04:10.840><c> what</c><00:04:10.959><c> they've</c><00:04:11.120><c> done</c><00:04:11.280><c> is</c>

00:04:11.390 --> 00:04:11.400 align:start position:0%
seems like what they've done is
 

00:04:11.400 --> 00:04:13.229 align:start position:0%
seems like what they've done is
basically<00:04:11.760><c> just</c><00:04:11.920><c> take</c><00:04:12.200><c> this</c><00:04:12.519><c> do</c><00:04:12.799><c> their</c><00:04:13.000><c> own</c>

00:04:13.229 --> 00:04:13.239 align:start position:0%
basically just take this do their own
 

00:04:13.239 --> 00:04:14.910 align:start position:0%
basically just take this do their own
sort<00:04:13.439><c> of</c><00:04:13.560><c> fine</c><00:04:13.840><c> tuning</c><00:04:14.200><c> and</c><00:04:14.400><c> putting</c><00:04:14.640><c> it</c>

00:04:14.910 --> 00:04:14.920 align:start position:0%
sort of fine tuning and putting it
 

00:04:14.920 --> 00:04:16.830 align:start position:0%
sort of fine tuning and putting it
together<00:04:15.760><c> and</c><00:04:15.879><c> what</c><00:04:16.000><c> I've</c><00:04:16.160><c> been</c><00:04:16.320><c> hearing</c><00:04:16.600><c> from</c>

00:04:16.830 --> 00:04:16.840 align:start position:0%
together and what I've been hearing from
 

00:04:16.840 --> 00:04:18.150 align:start position:0%
together and what I've been hearing from
people<00:04:17.079><c> and</c><00:04:17.359><c> I</c><00:04:17.440><c> think</c><00:04:17.560><c> I'll</c><00:04:17.680><c> probably</c><00:04:17.919><c> do</c><00:04:18.040><c> a</c>

00:04:18.150 --> 00:04:18.160 align:start position:0%
people and I think I'll probably do a
 

00:04:18.160 --> 00:04:19.990 align:start position:0%
people and I think I'll probably do a
whole<00:04:18.359><c> video</c><00:04:18.680><c> Just</c><00:04:18.840><c> Around</c><00:04:19.160><c> Pary</c><00:04:19.519><c> gemer</c><00:04:19.880><c> and</c>

00:04:19.990 --> 00:04:20.000 align:start position:0%
whole video Just Around Pary gemer and
 

00:04:20.000 --> 00:04:22.390 align:start position:0%
whole video Just Around Pary gemer and
talking<00:04:20.239><c> about</c><00:04:20.400><c> how</c><00:04:20.519><c> to</c><00:04:20.639><c> find</c><00:04:20.919><c> tune</c><00:04:21.160><c> it</c><00:04:21.400><c> Etc</c>

00:04:22.390 --> 00:04:22.400 align:start position:0%
talking about how to find tune it Etc
 

00:04:22.400 --> 00:04:23.590 align:start position:0%
talking about how to find tune it Etc
but<00:04:22.639><c> what</c><00:04:22.759><c> I've</c><00:04:22.880><c> been</c><00:04:23.000><c> hearing</c><00:04:23.240><c> from</c><00:04:23.400><c> people</c>

00:04:23.590 --> 00:04:23.600 align:start position:0%
but what I've been hearing from people
 

00:04:23.600 --> 00:04:25.430 align:start position:0%
but what I've been hearing from people
is<00:04:23.759><c> that</c><00:04:23.919><c> people</c><00:04:24.080><c> are</c><00:04:24.240><c> getting</c><00:04:24.880><c> this</c><00:04:25.000><c> to</c><00:04:25.160><c> do</c>

00:04:25.430 --> 00:04:25.440 align:start position:0%
is that people are getting this to do
 

00:04:25.440 --> 00:04:27.950 align:start position:0%
is that people are getting this to do
all<00:04:25.680><c> sorts</c><00:04:26.000><c> of</c><00:04:26.600><c> tasks</c><00:04:27.479><c> that</c><00:04:27.639><c> they</c><00:04:27.759><c> can</c>

00:04:27.950 --> 00:04:27.960 align:start position:0%
all sorts of tasks that they can
 

00:04:27.960 --> 00:04:30.350 align:start position:0%
all sorts of tasks that they can
fine-tune<00:04:28.680><c> pretty</c><00:04:29.120><c> easily</c><00:04:29.800><c> because</c><00:04:30.240><c> the</c>

00:04:30.350 --> 00:04:30.360 align:start position:0%
fine-tune pretty easily because the
 

00:04:30.360 --> 00:04:32.590 align:start position:0%
fine-tune pretty easily because the
thing<00:04:30.560><c> is</c><00:04:30.720><c> so</c><00:04:31.039><c> small</c><00:04:31.759><c> we're</c><00:04:31.960><c> talking</c><00:04:32.240><c> about</c>

00:04:32.590 --> 00:04:32.600 align:start position:0%
thing is so small we're talking about
 

00:04:32.600 --> 00:04:35.110 align:start position:0%
thing is so small we're talking about
something<00:04:32.880><c> that's</c><00:04:33.120><c> like</c><00:04:33.320><c> under</c><00:04:33.639><c> a</c><00:04:33.840><c> 3</c><00:04:34.160><c> billion</c>

00:04:35.110 --> 00:04:35.120 align:start position:0%
something that's like under a 3 billion
 

00:04:35.120 --> 00:04:37.390 align:start position:0%
something that's like under a 3 billion
parameter<00:04:35.639><c> model</c><00:04:36.120><c> here</c><00:04:36.720><c> so</c><00:04:36.880><c> it</c><00:04:37.039><c> really</c><00:04:37.240><c> is</c>

00:04:37.390 --> 00:04:37.400 align:start position:0%
parameter model here so it really is
 

00:04:37.400 --> 00:04:40.150 align:start position:0%
parameter model here so it really is
something<00:04:37.840><c> that</c><00:04:37.960><c> you</c><00:04:38.080><c> can</c><00:04:38.280><c> run</c><00:04:39.080><c> without</c><00:04:39.919><c> a</c><00:04:40.039><c> lot</c>

00:04:40.150 --> 00:04:40.160 align:start position:0%
something that you can run without a lot
 

00:04:40.160 --> 00:04:42.430 align:start position:0%
something that you can run without a lot
of<00:04:40.400><c> compute</c><00:04:41.240><c> to</c><00:04:41.400><c> do</c><00:04:41.600><c> this</c><00:04:41.759><c> kind</c><00:04:41.880><c> of</c><00:04:42.039><c> thing</c><00:04:42.320><c> you</c>

00:04:42.430 --> 00:04:42.440 align:start position:0%
of compute to do this kind of thing you
 

00:04:42.440 --> 00:04:45.870 align:start position:0%
of compute to do this kind of thing you
can<00:04:42.639><c> fine-tune</c><00:04:43.240><c> it</c><00:04:43.560><c> yourself</c><00:04:44.360><c> for</c><00:04:45.160><c> various</c>

00:04:45.870 --> 00:04:45.880 align:start position:0%
can fine-tune it yourself for various
 

00:04:45.880 --> 00:04:48.350 align:start position:0%
can fine-tune it yourself for various
tasks<00:04:46.880><c> and</c><00:04:47.000><c> I</c><00:04:47.080><c> think</c><00:04:47.240><c> Google's</c><00:04:47.720><c> already</c><00:04:48.039><c> set</c>

00:04:48.350 --> 00:04:48.360 align:start position:0%
tasks and I think Google's already set
 

00:04:48.360 --> 00:04:51.310 align:start position:0%
tasks and I think Google's already set
this<00:04:48.600><c> up</c><00:04:48.840><c> to</c><00:04:49.160><c> work</c><00:04:49.520><c> easily</c><00:04:50.039><c> for</c><00:04:50.400><c> doing</c><00:04:51.000><c> things</c>

00:04:51.310 --> 00:04:51.320 align:start position:0%
this up to work easily for doing things
 

00:04:51.320 --> 00:04:53.270 align:start position:0%
this up to work easily for doing things
like<00:04:51.479><c> you</c><00:04:51.600><c> know</c><00:04:51.840><c> object</c><00:04:52.199><c> detection</c><00:04:52.720><c> and</c>

00:04:53.270 --> 00:04:53.280 align:start position:0%
like you know object detection and
 

00:04:53.280 --> 00:04:55.909 align:start position:0%
like you know object detection and
segmentation<00:04:54.280><c> by</c><00:04:54.560><c> using</c><00:04:54.919><c> natural</c><00:04:55.360><c> language</c>

00:04:55.909 --> 00:04:55.919 align:start position:0%
segmentation by using natural language
 

00:04:55.919 --> 00:04:57.230 align:start position:0%
segmentation by using natural language
to<00:04:56.120><c> help</c><00:04:56.440><c> like</c><00:04:56.560><c> I</c><00:04:56.639><c> said</c><00:04:56.800><c> this</c><00:04:56.960><c> probably</c>

00:04:57.230 --> 00:04:57.240 align:start position:0%
to help like I said this probably
 

00:04:57.240 --> 00:04:59.110 align:start position:0%
to help like I said this probably
deserves<00:04:57.759><c> a</c><00:04:57.840><c> whole</c><00:04:58.039><c> video</c><00:04:58.320><c> by</c><00:04:58.479><c> itself</c><00:04:58.880><c> we</c><00:04:58.960><c> can</c>

00:04:59.110 --> 00:04:59.120 align:start position:0%
deserves a whole video by itself we can
 

00:04:59.120 --> 00:05:01.749 align:start position:0%
deserves a whole video by itself we can
look<00:04:59.240><c> at</c><00:04:59.400><c> that</c><00:05:00.039><c> in</c><00:05:00.160><c> the</c><00:05:00.320><c> future</c><00:05:01.039><c> the</c><00:05:01.199><c> other</c><00:05:01.520><c> big</c>

00:05:01.749 --> 00:05:01.759 align:start position:0%
look at that in the future the other big
 

00:05:01.759 --> 00:05:04.790 align:start position:0%
look at that in the future the other big
news<00:05:02.479><c> with</c><00:05:02.720><c> Gemma</c><00:05:03.440><c> was</c><00:05:04.000><c> the</c><00:05:04.160><c> announcement</c>

00:05:04.790 --> 00:05:04.800 align:start position:0%
news with Gemma was the announcement
 

00:05:04.800 --> 00:05:07.390 align:start position:0%
news with Gemma was the announcement
that<00:05:04.960><c> Gemma</c><00:05:05.400><c> 2</c><00:05:06.280><c> is</c><00:05:06.440><c> coming</c><00:05:06.720><c> in</c><00:05:06.880><c> a</c><00:05:06.960><c> few</c><00:05:07.160><c> weeks</c>

00:05:07.390 --> 00:05:07.400 align:start position:0%
that Gemma 2 is coming in a few weeks
 

00:05:07.400 --> 00:05:10.150 align:start position:0%
that Gemma 2 is coming in a few weeks
from<00:05:07.560><c> now</c><00:05:08.360><c> so</c><00:05:08.800><c> this</c><00:05:09.160><c> model</c><00:05:09.560><c> is</c><00:05:09.759><c> actually</c><00:05:10.000><c> a</c>

00:05:10.150 --> 00:05:10.160 align:start position:0%
from now so this model is actually a
 

00:05:10.160 --> 00:05:12.830 align:start position:0%
from now so this model is actually a
much<00:05:10.479><c> bigger</c><00:05:11.120><c> Gemma</c><00:05:11.520><c> model</c><00:05:11.840><c> so</c><00:05:12.320><c> in</c><00:05:12.440><c> the</c><00:05:12.600><c> past</c>

00:05:12.830 --> 00:05:12.840 align:start position:0%
much bigger Gemma model so in the past
 

00:05:12.840 --> 00:05:15.070 align:start position:0%
much bigger Gemma model so in the past
we've<00:05:13.080><c> had</c><00:05:13.320><c> like</c><00:05:13.479><c> the</c><00:05:13.560><c> 8</c><00:05:13.840><c> billion</c><00:05:14.600><c> parameter</c>

00:05:15.070 --> 00:05:15.080 align:start position:0%
we've had like the 8 billion parameter
 

00:05:15.080 --> 00:05:17.670 align:start position:0%
we've had like the 8 billion parameter
model<00:05:16.080><c> we've</c><00:05:16.320><c> had</c><00:05:16.479><c> a</c><00:05:16.600><c> two</c><00:05:16.840><c> billion</c><00:05:17.240><c> parameter</c>

00:05:17.670 --> 00:05:17.680 align:start position:0%
model we've had a two billion parameter
 

00:05:17.680 --> 00:05:20.390 align:start position:0%
model we've had a two billion parameter
model<00:05:18.360><c> we've</c><00:05:18.560><c> had</c><00:05:18.720><c> the</c><00:05:18.880><c> recurrent</c><00:05:19.440><c> Gemma</c><00:05:20.199><c> a</c>

00:05:20.390 --> 00:05:20.400 align:start position:0%
model we've had the recurrent Gemma a
 

00:05:20.400 --> 00:05:22.830 align:start position:0%
model we've had the recurrent Gemma a
code<00:05:20.720><c> Gemma</c><00:05:21.160><c> which</c><00:05:21.280><c> was</c><00:05:21.400><c> a</c><00:05:21.560><c> fine</c><00:05:21.880><c> tune</c><00:05:22.600><c> for</c>

00:05:22.830 --> 00:05:22.840 align:start position:0%
code Gemma which was a fine tune for
 

00:05:22.840 --> 00:05:25.070 align:start position:0%
code Gemma which was a fine tune for
doing<00:05:23.160><c> code</c><00:05:23.479><c> things</c><00:05:24.319><c> but</c><00:05:24.479><c> we've</c><00:05:24.639><c> been</c><00:05:24.800><c> missing</c>

00:05:25.070 --> 00:05:25.080 align:start position:0%
doing code things but we've been missing
 

00:05:25.080 --> 00:05:27.670 align:start position:0%
doing code things but we've been missing
a<00:05:25.280><c> bigger</c><00:05:25.880><c> version</c><00:05:26.479><c> than</c><00:05:27.039><c> something</c><00:05:27.400><c> that's</c>

00:05:27.670 --> 00:05:27.680 align:start position:0%
a bigger version than something that's
 

00:05:27.680 --> 00:05:30.909 align:start position:0%
a bigger version than something that's
above<00:05:28.080><c> 10</c><00:05:28.360><c> billion</c><00:05:28.840><c> parameters</c><00:05:30.120><c> and</c><00:05:30.360><c> so</c><00:05:30.600><c> sure</c>

00:05:30.909 --> 00:05:30.919 align:start position:0%
above 10 billion parameters and so sure
 

00:05:30.919 --> 00:05:32.430 align:start position:0%
above 10 billion parameters and so sure
enough<00:05:31.240><c> they've</c><00:05:31.479><c> announced</c><00:05:32.039><c> that</c><00:05:32.199><c> they're</c>

00:05:32.430 --> 00:05:32.440 align:start position:0%
enough they've announced that they're
 

00:05:32.440 --> 00:05:34.950 align:start position:0%
enough they've announced that they're
actually<00:05:32.639><c> going</c><00:05:32.720><c> to</c><00:05:32.880><c> release</c><00:05:33.360><c> Gemma</c><00:05:33.800><c> 2</c><00:05:34.800><c> now</c>

00:05:34.950 --> 00:05:34.960 align:start position:0%
actually going to release Gemma 2 now
 

00:05:34.960 --> 00:05:36.830 align:start position:0%
actually going to release Gemma 2 now
I'm<00:05:35.080><c> not</c><00:05:35.240><c> sure</c><00:05:35.479><c> if</c><00:05:35.720><c> this</c><00:05:35.840><c> is</c><00:05:36.000><c> going</c><00:05:36.080><c> to</c><00:05:36.240><c> be</c><00:05:36.479><c> one</c>

00:05:36.830 --> 00:05:36.840 align:start position:0%
I'm not sure if this is going to be one
 

00:05:36.840 --> 00:05:39.670 align:start position:0%
I'm not sure if this is going to be one
model<00:05:37.319><c> or</c><00:05:37.479><c> a</c><00:05:37.800><c> family</c><00:05:38.120><c> of</c><00:05:38.280><c> models</c><00:05:39.199><c> but</c><00:05:39.360><c> at</c><00:05:39.479><c> least</c>

00:05:39.670 --> 00:05:39.680 align:start position:0%
model or a family of models but at least
 

00:05:39.680 --> 00:05:41.390 align:start position:0%
model or a family of models but at least
one<00:05:39.919><c> version</c><00:05:40.199><c> of</c><00:05:40.319><c> Gemma</c><00:05:40.680><c> 2</c><00:05:40.960><c> is</c><00:05:41.080><c> going</c><00:05:41.199><c> to</c>

00:05:41.390 --> 00:05:41.400 align:start position:0%
one version of Gemma 2 is going to
 

00:05:41.400 --> 00:05:44.550 align:start position:0%
one version of Gemma 2 is going to
actually<00:05:41.639><c> be</c><00:05:42.000><c> 27</c><00:05:42.720><c> billion</c><00:05:43.319><c> parameters</c><00:05:44.319><c> in</c>

00:05:44.550 --> 00:05:44.560 align:start position:0%
actually be 27 billion parameters in
 

00:05:44.560 --> 00:05:46.350 align:start position:0%
actually be 27 billion parameters in
here<00:05:45.160><c> so</c><00:05:45.360><c> this</c><00:05:45.479><c> is</c><00:05:45.680><c> certainly</c><00:05:45.919><c> going</c><00:05:46.000><c> to</c><00:05:46.080><c> be</c><00:05:46.199><c> a</c>

00:05:46.350 --> 00:05:46.360 align:start position:0%
here so this is certainly going to be a
 

00:05:46.360 --> 00:05:49.029 align:start position:0%
here so this is certainly going to be a
much<00:05:46.639><c> bigger</c><00:05:47.000><c> model</c><00:05:47.520><c> than</c><00:05:47.840><c> what</c><00:05:48.039><c> most</c><00:05:48.319><c> people</c>

00:05:49.029 --> 00:05:49.039 align:start position:0%
much bigger model than what most people
 

00:05:49.039 --> 00:05:51.029 align:start position:0%
much bigger model than what most people
can<00:05:49.360><c> run</c><00:05:50.240><c> but</c><00:05:50.360><c> you</c><00:05:50.440><c> can</c><00:05:50.560><c> imagine</c><00:05:50.880><c> there's</c>

00:05:51.029 --> 00:05:51.039 align:start position:0%
can run but you can imagine there's
 

00:05:51.039 --> 00:05:53.510 align:start position:0%
can run but you can imagine there's
going<00:05:51.120><c> to</c><00:05:51.199><c> be</c><00:05:51.319><c> quantized</c><00:05:52.000><c> versions</c><00:05:52.479><c> of</c><00:05:52.840><c> this</c>

00:05:53.510 --> 00:05:53.520 align:start position:0%
going to be quantized versions of this
 

00:05:53.520 --> 00:05:55.830 align:start position:0%
going to be quantized versions of this
and<00:05:53.720><c> actually</c><00:05:53.919><c> a</c><00:05:54.080><c> 27</c><00:05:54.639><c> billion</c><00:05:55.319><c> parameter</c>

00:05:55.830 --> 00:05:55.840 align:start position:0%
and actually a 27 billion parameter
 

00:05:55.840 --> 00:05:59.110 align:start position:0%
and actually a 27 billion parameter
model<00:05:56.560><c> that's</c><00:05:56.880><c> quantized</c><00:05:57.520><c> down</c><00:05:57.720><c> to</c><00:05:58.240><c> 4</c><00:05:58.639><c> bit</c><00:05:59.039><c> I</c>

00:05:59.110 --> 00:05:59.120 align:start position:0%
model that's quantized down to 4 bit I
 

00:05:59.120 --> 00:06:00.070 align:start position:0%
model that's quantized down to 4 bit I
think<00:05:59.240><c> a</c><00:05:59.319><c> lot</c><00:05:59.400><c> of</c><00:05:59.479><c> people</c><00:05:59.639><c> people</c><00:05:59.840><c> will</c>

00:06:00.070 --> 00:06:00.080 align:start position:0%
think a lot of people people will
 

00:06:00.080 --> 00:06:03.150 align:start position:0%
think a lot of people people will
actually<00:06:00.280><c> be</c><00:06:00.400><c> able</c><00:06:00.560><c> to</c><00:06:00.680><c> run</c><00:06:01.000><c> this</c><00:06:01.560><c> in</c><00:06:01.919><c> ol</c><00:06:02.880><c> and</c>

00:06:03.150 --> 00:06:03.160 align:start position:0%
actually be able to run this in ol and
 

00:06:03.160 --> 00:06:05.710 align:start position:0%
actually be able to run this in ol and
various<00:06:03.479><c> quantized</c><00:06:04.080><c> different</c><00:06:04.400><c> versions</c><00:06:04.840><c> of</c>

00:06:05.710 --> 00:06:05.720 align:start position:0%
various quantized different versions of
 

00:06:05.720 --> 00:06:07.990 align:start position:0%
various quantized different versions of
they're<00:06:05.960><c> also</c><00:06:06.280><c> claiming</c><00:06:06.880><c> that</c><00:06:07.080><c> this</c><00:06:07.280><c> model</c>

00:06:07.990 --> 00:06:08.000 align:start position:0%
they're also claiming that this model
 

00:06:08.000 --> 00:06:09.790 align:start position:0%
they're also claiming that this model
basically<00:06:08.400><c> delivers</c><00:06:09.120><c> performance</c>

00:06:09.790 --> 00:06:09.800 align:start position:0%
basically delivers performance
 

00:06:09.800 --> 00:06:12.670 align:start position:0%
basically delivers performance
comparable<00:06:10.360><c> with</c><00:06:10.520><c> the</c><00:06:10.639><c> Llama</c><00:06:11.039><c> 370</c><00:06:11.840><c> billion</c>

00:06:12.670 --> 00:06:12.680 align:start position:0%
comparable with the Llama 370 billion
 

00:06:12.680 --> 00:06:14.110 align:start position:0%
comparable with the Llama 370 billion
that<00:06:12.800><c> would</c><00:06:12.919><c> be</c><00:06:13.080><c> awesome</c><00:06:13.400><c> if</c><00:06:13.520><c> that</c><00:06:13.680><c> works</c><00:06:13.919><c> out</c>

00:06:14.110 --> 00:06:14.120 align:start position:0%
that would be awesome if that works out
 

00:06:14.120 --> 00:06:16.189 align:start position:0%
that would be awesome if that works out
to<00:06:14.240><c> be</c><00:06:14.440><c> the</c><00:06:14.639><c> case</c><00:06:15.199><c> for</c><00:06:15.440><c> most</c><00:06:15.840><c> things</c><00:06:16.039><c> that</c>

00:06:16.189 --> 00:06:16.199 align:start position:0%
to be the case for most things that
 

00:06:16.199 --> 00:06:18.150 align:start position:0%
to be the case for most things that
people<00:06:16.479><c> want</c><00:06:16.599><c> to</c><00:06:16.800><c> do</c><00:06:17.039><c> with</c><00:06:17.280><c> this</c><00:06:17.840><c> it's</c><00:06:17.960><c> really</c>

00:06:18.150 --> 00:06:18.160 align:start position:0%
people want to do with this it's really
 

00:06:18.160 --> 00:06:20.749 align:start position:0%
people want to do with this it's really
going<00:06:18.280><c> to</c><00:06:18.520><c> open</c><00:06:18.960><c> up</c><00:06:19.520><c> the</c><00:06:19.720><c> ability</c><00:06:20.160><c> to</c><00:06:20.319><c> do</c><00:06:20.560><c> lots</c>

00:06:20.749 --> 00:06:20.759 align:start position:0%
going to open up the ability to do lots
 

00:06:20.759 --> 00:06:23.309 align:start position:0%
going to open up the ability to do lots
of<00:06:20.919><c> fine</c><00:06:21.280><c> tunings</c><00:06:21.840><c> now</c><00:06:22.280><c> Gemma</c><00:06:22.720><c> has</c><00:06:22.800><c> a</c><00:06:23.000><c> really</c>

00:06:23.309 --> 00:06:23.319 align:start position:0%
of fine tunings now Gemma has a really
 

00:06:23.319 --> 00:06:25.550 align:start position:0%
of fine tunings now Gemma has a really
good<00:06:23.720><c> tokenizer</c><00:06:24.360><c> for</c><00:06:24.520><c> doing</c><00:06:24.800><c> multilingual</c>

00:06:25.550 --> 00:06:25.560 align:start position:0%
good tokenizer for doing multilingual
 

00:06:25.560 --> 00:06:27.629 align:start position:0%
good tokenizer for doing multilingual
stuff<00:06:25.840><c> I've</c><00:06:25.960><c> talked</c><00:06:26.199><c> about</c><00:06:26.479><c> this</c><00:06:26.720><c> before</c>

00:06:27.629 --> 00:06:27.639 align:start position:0%
stuff I've talked about this before
 

00:06:27.639 --> 00:06:29.070 align:start position:0%
stuff I've talked about this before
Google<00:06:28.039><c> actually</c><00:06:28.280><c> showed</c><00:06:28.639><c> off</c><00:06:28.840><c> in</c><00:06:28.960><c> the</c>

00:06:29.070 --> 00:06:29.080 align:start position:0%
Google actually showed off in the
 

00:06:29.080 --> 00:06:30.990 align:start position:0%
Google actually showed off in the
Keynote<00:06:29.960><c> that</c><00:06:30.080><c> some</c><00:06:30.280><c> people</c><00:06:30.520><c> are</c><00:06:30.759><c> actually</c>

00:06:30.990 --> 00:06:31.000 align:start position:0%
Keynote that some people are actually
 

00:06:31.000 --> 00:06:34.350 align:start position:0%
Keynote that some people are actually
starting<00:06:31.479><c> to</c><00:06:32.199><c> fine-tune</c><00:06:32.880><c> the</c><00:06:33.120><c> first</c><00:06:33.520><c> versions</c>

00:06:34.350 --> 00:06:34.360 align:start position:0%
starting to fine-tune the first versions
 

00:06:34.360 --> 00:06:37.629 align:start position:0%
starting to fine-tune the first versions
of<00:06:34.759><c> Gemma</c><00:06:35.240><c> for</c><00:06:35.400><c> multilingual</c><00:06:36.080><c> use</c><00:06:36.479><c> cases</c><00:06:37.479><c> I</c>

00:06:37.629 --> 00:06:37.639 align:start position:0%
of Gemma for multilingual use cases I
 

00:06:37.639 --> 00:06:39.390 align:start position:0%
of Gemma for multilingual use cases I
guess<00:06:37.800><c> is</c><00:06:37.919><c> that</c><00:06:38.080><c> we</c><00:06:38.199><c> will</c><00:06:38.400><c> see</c><00:06:38.880><c> that</c><00:06:39.080><c> happen</c>

00:06:39.390 --> 00:06:39.400 align:start position:0%
guess is that we will see that happen
 

00:06:39.400 --> 00:06:42.110 align:start position:0%
guess is that we will see that happen
even<00:06:39.680><c> more</c><00:06:39.960><c> with</c><00:06:40.160><c> Gemma</c><00:06:40.560><c> 2</c><00:06:41.400><c> and</c><00:06:41.560><c> I'm</c><00:06:41.759><c> hoping</c>

00:06:42.110 --> 00:06:42.120 align:start position:0%
even more with Gemma 2 and I'm hoping
 

00:06:42.120 --> 00:06:44.070 align:start position:0%
even more with Gemma 2 and I'm hoping
that<00:06:42.400><c> when</c><00:06:42.520><c> we</c><00:06:42.639><c> see</c><00:06:42.919><c> the</c><00:06:43.080><c> amount</c><00:06:43.360><c> of</c><00:06:43.599><c> tokens</c>

00:06:44.070 --> 00:06:44.080 align:start position:0%
that when we see the amount of tokens
 

00:06:44.080 --> 00:06:45.670 align:start position:0%
that when we see the amount of tokens
that<00:06:44.240><c> this</c><00:06:44.360><c> is</c><00:06:44.599><c> actually</c><00:06:44.800><c> been</c><00:06:45.039><c> trained</c><00:06:45.400><c> on</c>

00:06:45.670 --> 00:06:45.680 align:start position:0%
that this is actually been trained on
 

00:06:45.680 --> 00:06:49.150 align:start position:0%
that this is actually been trained on
Etc<00:06:46.639><c> that</c><00:06:46.919><c> maybe</c><00:06:47.759><c> a</c><00:06:47.919><c> decent</c><00:06:48.319><c> chunk</c><00:06:48.639><c> of</c><00:06:48.840><c> those</c>

00:06:49.150 --> 00:06:49.160 align:start position:0%
Etc that maybe a decent chunk of those
 

00:06:49.160 --> 00:06:51.629 align:start position:0%
Etc that maybe a decent chunk of those
tokens<00:06:49.800><c> will</c><00:06:50.039><c> actually</c><00:06:50.319><c> be</c><00:06:50.639><c> multilingual</c>

00:06:51.629 --> 00:06:51.639 align:start position:0%
tokens will actually be multilingual
 

00:06:51.639 --> 00:06:54.110 align:start position:0%
tokens will actually be multilingual
tokens<00:06:52.280><c> themselves</c><00:06:52.759><c> so</c><00:06:53.240><c> this</c><00:06:53.400><c> model</c><00:06:53.759><c> may</c><00:06:53.919><c> end</c>

00:06:54.110 --> 00:06:54.120 align:start position:0%
tokens themselves so this model may end
 

00:06:54.120 --> 00:06:56.070 align:start position:0%
tokens themselves so this model may end
up<00:06:54.319><c> being</c><00:06:54.639><c> really</c><00:06:54.919><c> good</c><00:06:55.160><c> for</c><00:06:55.400><c> doing</c><00:06:55.759><c> any</c><00:06:55.960><c> kind</c>

00:06:56.070 --> 00:06:56.080 align:start position:0%
up being really good for doing any kind
 

00:06:56.080 --> 00:06:59.189 align:start position:0%
up being really good for doing any kind
of<00:06:56.240><c> multilingual</c><00:06:57.120><c> task</c><00:06:57.960><c> as</c><00:06:58.120><c> well</c><00:06:58.319><c> as</c><00:06:58.560><c> being</c>

00:06:59.189 --> 00:06:59.199 align:start position:0%
of multilingual task as well as being
 

00:06:59.199 --> 00:07:01.510 align:start position:0%
of multilingual task as well as being
able<00:06:59.560><c> to</c><00:06:59.720><c> fine</c><00:07:00.039><c> tune</c><00:07:00.400><c> for</c><00:07:00.639><c> a</c><00:07:00.800><c> variety</c><00:07:01.319><c> of</c>

00:07:01.510 --> 00:07:01.520 align:start position:0%
able to fine tune for a variety of
 

00:07:01.520 --> 00:07:03.230 align:start position:0%
able to fine tune for a variety of
different<00:07:01.879><c> tasks</c><00:07:02.479><c> so</c><00:07:02.639><c> the</c><00:07:02.759><c> last</c><00:07:02.919><c> thing</c><00:07:03.039><c> that</c><00:07:03.160><c> I</c>

00:07:03.230 --> 00:07:03.240 align:start position:0%
different tasks so the last thing that I
 

00:07:03.240 --> 00:07:06.029 align:start position:0%
different tasks so the last thing that I
wanted<00:07:03.440><c> to</c><00:07:03.560><c> talk</c><00:07:03.800><c> about</c><00:07:04.240><c> is</c><00:07:04.479><c> the</c><00:07:04.680><c> Firebase</c><00:07:05.560><c> gen</c>

00:07:06.029 --> 00:07:06.039 align:start position:0%
wanted to talk about is the Firebase gen
 

00:07:06.039 --> 00:07:09.629 align:start position:0%
wanted to talk about is the Firebase gen
kit<00:07:06.479><c> as</c><00:07:06.599><c> I</c><00:07:07.160><c> understand</c><00:07:07.360><c> it</c><00:07:07.919><c> this</c><00:07:08.080><c> is</c><00:07:08.639><c> basically</c>

00:07:09.629 --> 00:07:09.639 align:start position:0%
kit as I understand it this is basically
 

00:07:09.639 --> 00:07:12.950 align:start position:0%
kit as I understand it this is basically
the<00:07:09.960><c> Firebase</c><00:07:10.599><c> team</c><00:07:11.000><c> who</c><00:07:11.199><c> been</c><00:07:11.520><c> working</c><00:07:12.080><c> on</c>

00:07:12.950 --> 00:07:12.960 align:start position:0%
the Firebase team who been working on
 

00:07:12.960 --> 00:07:15.670 align:start position:0%
the Firebase team who been working on
creating<00:07:13.960><c> a</c><00:07:14.199><c> JavaScript</c><00:07:14.919><c> or</c><00:07:15.240><c> actually</c><00:07:15.520><c> think</c>

00:07:15.670 --> 00:07:15.680 align:start position:0%
creating a JavaScript or actually think
 

00:07:15.680 --> 00:07:18.469 align:start position:0%
creating a JavaScript or actually think
it's<00:07:16.240><c> typescript</c><00:07:17.240><c> which</c><00:07:17.840><c> surely</c><00:07:18.160><c> has</c><00:07:18.280><c> got</c><00:07:18.360><c> to</c>

00:07:18.469 --> 00:07:18.479 align:start position:0%
it's typescript which surely has got to
 

00:07:18.479 --> 00:07:20.510 align:start position:0%
it's typescript which surely has got to
make<00:07:18.639><c> it</c><00:07:18.759><c> one</c><00:07:18.879><c> of</c><00:07:19.039><c> the</c><00:07:19.240><c> first</c><00:07:19.680><c> typescript</c>

00:07:20.510 --> 00:07:20.520 align:start position:0%
make it one of the first typescript
 

00:07:20.520 --> 00:07:22.950 align:start position:0%
make it one of the first typescript
products<00:07:20.919><c> from</c><00:07:21.120><c> Google</c><00:07:21.840><c> Google</c><00:07:22.199><c> generally</c>

00:07:22.950 --> 00:07:22.960 align:start position:0%
products from Google Google generally
 

00:07:22.960 --> 00:07:25.230 align:start position:0%
products from Google Google generally
hasn't<00:07:23.199><c> been</c><00:07:23.360><c> a</c><00:07:23.520><c> big</c><00:07:23.759><c> user</c><00:07:24.160><c> of</c><00:07:24.479><c> typescript</c><00:07:25.120><c> in</c>

00:07:25.230 --> 00:07:25.240 align:start position:0%
hasn't been a big user of typescript in
 

00:07:25.240 --> 00:07:28.390 align:start position:0%
hasn't been a big user of typescript in
the<00:07:25.440><c> past</c><00:07:26.440><c> but</c><00:07:26.560><c> the</c><00:07:26.680><c> whole</c><00:07:26.919><c> idea</c><00:07:27.360><c> is</c><00:07:27.639><c> that</c><00:07:28.199><c> as</c>

00:07:28.390 --> 00:07:28.400 align:start position:0%
the past but the whole idea is that as
 

00:07:28.400 --> 00:07:30.430 align:start position:0%
the past but the whole idea is that as
people<00:07:28.639><c> are</c><00:07:28.879><c> starting</c><00:07:29.199><c> to</c><00:07:29.560><c> make</c><00:07:29.919><c> generative</c>

00:07:30.430 --> 00:07:30.440 align:start position:0%
people are starting to make generative
 

00:07:30.440 --> 00:07:34.710 align:start position:0%
people are starting to make generative
AI<00:07:30.919><c> apps</c><00:07:31.319><c> using</c><00:07:31.720><c> things</c><00:07:32.240><c> like</c><00:07:32.800><c> nextjs</c><00:07:33.720><c> Etc</c>

00:07:34.710 --> 00:07:34.720 align:start position:0%
AI apps using things like nextjs Etc
 

00:07:34.720 --> 00:07:37.110 align:start position:0%
AI apps using things like nextjs Etc
they<00:07:34.879><c> want</c><00:07:35.080><c> to</c><00:07:35.360><c> actually</c><00:07:35.639><c> be</c><00:07:35.840><c> able</c><00:07:36.080><c> to</c><00:07:36.440><c> use</c>

00:07:37.110 --> 00:07:37.120 align:start position:0%
they want to actually be able to use
 

00:07:37.120 --> 00:07:40.070 align:start position:0%
they want to actually be able to use
apis<00:07:38.080><c> straight</c><00:07:38.400><c> from</c><00:07:38.639><c> the</c><00:07:38.840><c> JavaScript</c><00:07:39.560><c> SL</c>

00:07:40.070 --> 00:07:40.080 align:start position:0%
apis straight from the JavaScript SL
 

00:07:40.080 --> 00:07:42.710 align:start position:0%
apis straight from the JavaScript SL
typescript<00:07:40.840><c> interface</c><00:07:41.800><c> uh</c><00:07:41.919><c> and</c><00:07:42.039><c> the</c><00:07:42.160><c> idea</c><00:07:42.560><c> is</c>

00:07:42.710 --> 00:07:42.720 align:start position:0%
typescript interface uh and the idea is
 

00:07:42.720 --> 00:07:44.749 align:start position:0%
typescript interface uh and the idea is
that<00:07:42.879><c> up</c><00:07:43.039><c> until</c><00:07:43.280><c> now</c><00:07:43.479><c> I</c><00:07:43.680><c> guess</c><00:07:43.879><c> we've</c><00:07:44.159><c> had</c><00:07:44.440><c> the</c>

00:07:44.749 --> 00:07:44.759 align:start position:0%
that up until now I guess we've had the
 

00:07:44.759 --> 00:07:47.990 align:start position:0%
that up until now I guess we've had the
cell<00:07:45.599><c> has</c><00:07:45.840><c> made</c><00:07:46.319><c> some</c><00:07:46.599><c> things</c><00:07:46.879><c> around</c><00:07:47.400><c> this</c>

00:07:47.990 --> 00:07:48.000 align:start position:0%
cell has made some things around this
 

00:07:48.000 --> 00:07:50.350 align:start position:0%
cell has made some things around this
but<00:07:48.199><c> Google's</c><00:07:48.520><c> sort</c><00:07:48.680><c> of</c><00:07:48.800><c> stepping</c><00:07:49.159><c> in</c><00:07:49.840><c> and</c>

00:07:50.350 --> 00:07:50.360 align:start position:0%
but Google's sort of stepping in and
 

00:07:50.360 --> 00:07:53.830 align:start position:0%
but Google's sort of stepping in and
interestingly<00:07:51.199><c> supporting</c><00:07:52.520><c> nextjs</c><00:07:53.520><c> with</c>

00:07:53.830 --> 00:07:53.840 align:start position:0%
interestingly supporting nextjs with
 

00:07:53.840 --> 00:07:56.270 align:start position:0%
interestingly supporting nextjs with
this<00:07:54.440><c> and</c><00:07:54.680><c> also</c><00:07:55.120><c> adding</c><00:07:55.400><c> in</c><00:07:55.800><c> quite</c><00:07:55.960><c> a</c><00:07:56.039><c> bit</c><00:07:56.159><c> of</c>

00:07:56.270 --> 00:07:56.280 align:start position:0%
this and also adding in quite a bit of
 

00:07:56.280 --> 00:07:58.869 align:start position:0%
this and also adding in quite a bit of
support<00:07:56.639><c> I</c><00:07:56.720><c> think</c><00:07:56.919><c> for</c><00:07:57.199><c> nextjs</c><00:07:57.919><c> with</c><00:07:58.120><c> Firebase</c>

00:07:58.869 --> 00:07:58.879 align:start position:0%
support I think for nextjs with Firebase
 

00:07:58.879 --> 00:08:01.510 align:start position:0%
support I think for nextjs with Firebase
as<00:07:59.080><c> well</c><00:07:59.759><c> in</c><00:07:59.960><c> here</c><00:08:00.599><c> the</c><00:08:00.759><c> idea</c><00:08:01.120><c> here</c><00:08:01.280><c> is</c><00:08:01.400><c> that</c>

00:08:01.510 --> 00:08:01.520 align:start position:0%
as well in here the idea here is that
 

00:08:01.520 --> 00:08:04.270 align:start position:0%
as well in here the idea here is that
you'll<00:08:01.680><c> be</c><00:08:01.840><c> able</c><00:08:02.039><c> to</c><00:08:02.319><c> run</c><00:08:03.159><c> and</c><00:08:03.440><c> ping</c><00:08:04.000><c> many</c>

00:08:04.270 --> 00:08:04.280 align:start position:0%
you'll be able to run and ping many
 

00:08:04.280 --> 00:08:06.469 align:start position:0%
you'll be able to run and ping many
different<00:08:04.599><c> models</c><00:08:05.199><c> with</c><00:08:05.400><c> the</c><00:08:05.560><c> same</c><00:08:05.840><c> kind</c><00:08:06.000><c> of</c>

00:08:06.469 --> 00:08:06.479 align:start position:0%
different models with the same kind of
 

00:08:06.479 --> 00:08:09.149 align:start position:0%
different models with the same kind of
interface<00:08:07.479><c> be</c><00:08:07.639><c> able</c><00:08:07.879><c> to</c><00:08:08.360><c> put</c><00:08:08.560><c> that</c><00:08:08.680><c> into</c><00:08:08.919><c> your</c>

00:08:09.149 --> 00:08:09.159 align:start position:0%
interface be able to put that into your
 

00:08:09.159 --> 00:08:11.309 align:start position:0%
interface be able to put that into your
code<00:08:09.759><c> and</c><00:08:09.960><c> and</c><00:08:10.080><c> be</c><00:08:10.240><c> able</c><00:08:10.400><c> to</c><00:08:10.479><c> do</c><00:08:10.720><c> everything</c>

00:08:11.309 --> 00:08:11.319 align:start position:0%
code and and be able to do everything
 

00:08:11.319 --> 00:08:15.189 align:start position:0%
code and and be able to do everything
from<00:08:11.879><c> things</c><00:08:12.120><c> like</c><00:08:12.319><c> Gemini</c><00:08:12.919><c> Pro</c><00:08:13.440><c> to</c><00:08:13.800><c> AMA</c><00:08:14.800><c> to</c><00:08:15.039><c> a</c>

00:08:15.189 --> 00:08:15.199 align:start position:0%
from things like Gemini Pro to AMA to a
 

00:08:15.199 --> 00:08:17.550 align:start position:0%
from things like Gemini Pro to AMA to a
variety<00:08:15.720><c> of</c><00:08:15.960><c> different</c><00:08:16.319><c> kind</c><00:08:16.440><c> of</c><00:08:16.599><c> models</c><00:08:17.440><c> that</c>

00:08:17.550 --> 00:08:17.560 align:start position:0%
variety of different kind of models that
 

00:08:17.560 --> 00:08:20.430 align:start position:0%
variety of different kind of models that
they've<00:08:17.759><c> got</c><00:08:17.960><c> in</c><00:08:18.159><c> here</c><00:08:18.440><c> both</c><00:08:18.680><c> from</c><00:08:19.240><c> vertex</c><00:08:19.759><c> Ai</c>

00:08:20.430 --> 00:08:20.440 align:start position:0%
they've got in here both from vertex Ai
 

00:08:20.440 --> 00:08:22.950 align:start position:0%
they've got in here both from vertex Ai
and<00:08:20.560><c> my</c><00:08:20.759><c> guess</c><00:08:20.919><c> from</c><00:08:21.120><c> AI</c><00:08:21.479><c> Studio</c><00:08:22.360><c> as</c><00:08:22.520><c> well</c><00:08:22.759><c> in</c>

00:08:22.950 --> 00:08:22.960 align:start position:0%
and my guess from AI Studio as well in
 

00:08:22.960 --> 00:08:25.149 align:start position:0%
and my guess from AI Studio as well in
here<00:08:23.599><c> now</c><00:08:23.759><c> they</c><00:08:23.879><c> do</c><00:08:24.039><c> say</c><00:08:24.199><c> that</c><00:08:24.319><c> it's</c><00:08:24.479><c> an</c><00:08:24.759><c> open</c>

00:08:25.149 --> 00:08:25.159 align:start position:0%
here now they do say that it's an open
 

00:08:25.159 --> 00:08:28.149 align:start position:0%
here now they do say that it's an open
source<00:08:25.759><c> framework</c><00:08:26.520><c> I'm</c><00:08:26.639><c> not</c><00:08:26.800><c> sure</c><00:08:27.319><c> how</c><00:08:27.639><c> open</c>

00:08:28.149 --> 00:08:28.159 align:start position:0%
source framework I'm not sure how open
 

00:08:28.159 --> 00:08:30.270 align:start position:0%
source framework I'm not sure how open
are<00:08:28.280><c> they</c><00:08:28.400><c> going</c><00:08:28.520><c> to</c><00:08:28.639><c> allow</c><00:08:28.919><c> people</c><00:08:29.159><c> to</c><00:08:29.560><c> add</c><00:08:29.800><c> in</c>

00:08:30.270 --> 00:08:30.280 align:start position:0%
are they going to allow people to add in
 

00:08:30.280 --> 00:08:32.709 align:start position:0%
are they going to allow people to add in
the<00:08:30.440><c> open</c><00:08:30.759><c> Ai</c><00:08:31.199><c> sdks</c><00:08:31.840><c> and</c><00:08:32.000><c> stuff</c><00:08:32.200><c> like</c><00:08:32.399><c> that</c>

00:08:32.709 --> 00:08:32.719 align:start position:0%
the open Ai sdks and stuff like that
 

00:08:32.719 --> 00:08:34.630 align:start position:0%
the open Ai sdks and stuff like that
that<00:08:32.839><c> I'm</c><00:08:33.000><c> not</c><00:08:33.159><c> sure</c><00:08:33.479><c> about</c><00:08:34.159><c> I</c><00:08:34.279><c> guess</c><00:08:34.440><c> i'</c><00:08:34.519><c> would</c>

00:08:34.630 --> 00:08:34.640 align:start position:0%
that I'm not sure about I guess i' would
 

00:08:34.640 --> 00:08:37.469 align:start position:0%
that I'm not sure about I guess i' would
be<00:08:34.839><c> surprised</c><00:08:35.440><c> if</c><00:08:35.680><c> that</c><00:08:35.880><c> was</c><00:08:36.200><c> the</c><00:08:36.440><c> case</c><00:08:37.240><c> but</c>

00:08:37.469 --> 00:08:37.479 align:start position:0%
be surprised if that was the case but
 

00:08:37.479 --> 00:08:39.350 align:start position:0%
be surprised if that was the case but
this<00:08:37.640><c> does</c><00:08:37.880><c> sort</c><00:08:38.080><c> of</c><00:08:38.240><c> give</c><00:08:38.360><c> you</c><00:08:38.680><c> a</c><00:08:38.959><c> lot</c><00:08:39.159><c> of</c>

00:08:39.350 --> 00:08:39.360 align:start position:0%
this does sort of give you a lot of
 

00:08:39.360 --> 00:08:41.509 align:start position:0%
this does sort of give you a lot of
tools<00:08:39.760><c> that</c><00:08:39.919><c> you</c><00:08:40.000><c> can</c><00:08:40.240><c> get</c><00:08:40.479><c> started</c><00:08:41.000><c> with</c><00:08:41.279><c> for</c>

00:08:41.509 --> 00:08:41.519 align:start position:0%
tools that you can get started with for
 

00:08:41.519 --> 00:08:44.310 align:start position:0%
tools that you can get started with for
doing<00:08:41.959><c> both</c><00:08:42.200><c> llm</c><00:08:42.880><c> stuff</c><00:08:43.279><c> and</c><00:08:43.440><c> multimodal</c>

00:08:44.310 --> 00:08:44.320 align:start position:0%
doing both llm stuff and multimodal
 

00:08:44.320 --> 00:08:47.590 align:start position:0%
doing both llm stuff and multimodal
stuff<00:08:44.760><c> like</c><00:08:45.000><c> generating</c><00:08:45.800><c> images</c><00:08:46.800><c> using</c><00:08:47.399><c> some</c>

00:08:47.590 --> 00:08:47.600 align:start position:0%
stuff like generating images using some
 

00:08:47.600 --> 00:08:49.870 align:start position:0%
stuff like generating images using some
of<00:08:47.920><c> Google's</c><00:08:48.399><c> image</c><00:08:48.720><c> Generation</c><00:08:49.480><c> video</c>

00:08:49.870 --> 00:08:49.880 align:start position:0%
of Google's image Generation video
 

00:08:49.880 --> 00:08:52.630 align:start position:0%
of Google's image Generation video
generation<00:08:50.760><c> sdks</c><00:08:51.600><c> that</c><00:08:51.720><c> are</c><00:08:51.880><c> slowly</c><00:08:52.320><c> being</c>

00:08:52.630 --> 00:08:52.640 align:start position:0%
generation sdks that are slowly being
 

00:08:52.640 --> 00:08:55.030 align:start position:0%
generation sdks that are slowly being
made<00:08:52.959><c> available</c><00:08:53.519><c> to</c><00:08:53.760><c> people</c><00:08:54.560><c> the</c><00:08:54.680><c> other</c>

00:08:55.030 --> 00:08:55.040 align:start position:0%
made available to people the other
 

00:08:55.040 --> 00:08:56.630 align:start position:0%
made available to people the other
interesting<00:08:55.440><c> thing</c><00:08:55.640><c> is</c><00:08:55.959><c> here</c><00:08:56.200><c> is</c><00:08:56.399><c> that</c>

00:08:56.630 --> 00:08:56.640 align:start position:0%
interesting thing is here is that
 

00:08:56.640 --> 00:08:59.670 align:start position:0%
interesting thing is here is that
they've<00:08:57.000><c> made</c><00:08:57.800><c> a</c><00:08:58.120><c> local</c><00:08:58.720><c> gen</c><00:08:59.040><c> kit</c><00:08:59.200><c> devel</c><00:08:59.399><c> velop</c>

00:08:59.670 --> 00:08:59.680 align:start position:0%
they've made a local gen kit devel velop
 

00:08:59.680 --> 00:09:02.350 align:start position:0%
they've made a local gen kit devel velop
a<00:08:59.839><c> UI</c><00:09:00.680><c> for</c><00:09:00.839><c> you</c><00:09:00.920><c> to</c><00:09:01.120><c> try</c><00:09:01.440><c> things</c><00:09:01.720><c> out</c><00:09:02.079><c> and</c>

00:09:02.350 --> 00:09:02.360 align:start position:0%
a UI for you to try things out and
 

00:09:02.360 --> 00:09:04.870 align:start position:0%
a UI for you to try things out and
experiment<00:09:02.839><c> so</c><00:09:03.079><c> this</c><00:09:03.600><c> looks</c><00:09:04.000><c> very</c><00:09:04.240><c> similar</c><00:09:04.640><c> to</c>

00:09:04.870 --> 00:09:04.880 align:start position:0%
experiment so this looks very similar to
 

00:09:04.880 --> 00:09:07.710 align:start position:0%
experiment so this looks very similar to
AI<00:09:05.360><c> Studio</c><00:09:06.360><c> here</c><00:09:06.640><c> but</c><00:09:06.800><c> I</c><00:09:06.920><c> guess</c><00:09:07.200><c> this</c><00:09:07.440><c> you</c><00:09:07.519><c> know</c>

00:09:07.710 --> 00:09:07.720 align:start position:0%
AI Studio here but I guess this you know
 

00:09:07.720 --> 00:09:09.310 align:start position:0%
AI Studio here but I guess this you know
allows<00:09:08.040><c> you</c><00:09:08.200><c> to</c><00:09:08.360><c> sort</c><00:09:08.519><c> of</c><00:09:08.680><c> try</c><00:09:09.000><c> different</c>

00:09:09.310 --> 00:09:09.320 align:start position:0%
allows you to sort of try different
 

00:09:09.320 --> 00:09:11.069 align:start position:0%
allows you to sort of try different
things<00:09:09.600><c> out</c><00:09:09.920><c> try</c><00:09:10.200><c> different</c><00:09:10.519><c> prompts</c>

00:09:11.069 --> 00:09:11.079 align:start position:0%
things out try different prompts
 

00:09:11.079 --> 00:09:13.949 align:start position:0%
things out try different prompts
different<00:09:11.519><c> models</c><00:09:12.519><c> see</c><00:09:12.880><c> what</c><00:09:13.000><c> you</c><00:09:13.200><c> get</c><00:09:13.519><c> out</c><00:09:13.680><c> of</c>

00:09:13.949 --> 00:09:13.959 align:start position:0%
different models see what you get out of
 

00:09:13.959 --> 00:09:15.389 align:start position:0%
different models see what you get out of
this<00:09:14.480><c> and</c><00:09:14.600><c> then</c><00:09:14.720><c> be</c><00:09:14.880><c> able</c><00:09:15.040><c> to</c><00:09:15.160><c> just</c>

00:09:15.389 --> 00:09:15.399 align:start position:0%
this and then be able to just
 

00:09:15.399 --> 00:09:17.910 align:start position:0%
this and then be able to just
incorporate<00:09:15.959><c> it</c><00:09:16.120><c> straight</c><00:09:16.480><c> into</c><00:09:16.800><c> your</c><00:09:17.399><c> app</c>

00:09:17.910 --> 00:09:17.920 align:start position:0%
incorporate it straight into your app
 

00:09:17.920 --> 00:09:20.069 align:start position:0%
incorporate it straight into your app
going<00:09:18.160><c> forward</c><00:09:18.560><c> in</c><00:09:18.800><c> here</c><00:09:19.480><c> another</c><00:09:19.800><c> thing</c><00:09:19.959><c> is</c>

00:09:20.069 --> 00:09:20.079 align:start position:0%
going forward in here another thing is
 

00:09:20.079 --> 00:09:21.630 align:start position:0%
going forward in here another thing is
that<00:09:20.279><c> they</c><00:09:20.399><c> talk</c><00:09:20.640><c> about</c><00:09:20.920><c> the</c><00:09:21.040><c> whole</c><00:09:21.200><c> sort</c><00:09:21.399><c> of</c>

00:09:21.630 --> 00:09:21.640 align:start position:0%
that they talk about the whole sort of
 

00:09:21.640 --> 00:09:24.990 align:start position:0%
that they talk about the whole sort of
idea<00:09:22.079><c> of</c><00:09:22.519><c> flows</c><00:09:23.519><c> and</c><00:09:23.680><c> being</c><00:09:23.880><c> able</c><00:09:24.160><c> to</c><00:09:24.480><c> track</c>

00:09:24.990 --> 00:09:25.000 align:start position:0%
idea of flows and being able to track
 

00:09:25.000 --> 00:09:26.870 align:start position:0%
idea of flows and being able to track
these<00:09:25.600><c> this</c><00:09:25.720><c> is</c><00:09:26.000><c> interesting</c><00:09:26.440><c> thing</c><00:09:26.720><c> where</c>

00:09:26.870 --> 00:09:26.880 align:start position:0%
these this is interesting thing where
 

00:09:26.880 --> 00:09:28.870 align:start position:0%
these this is interesting thing where
perhaps<00:09:27.120><c> they're</c><00:09:27.279><c> taking</c><00:09:27.560><c> on</c><00:09:27.800><c> some</c><00:09:27.959><c> of</c><00:09:28.200><c> the</c>

00:09:28.870 --> 00:09:28.880 align:start position:0%
perhaps they're taking on some of the
 

00:09:28.880 --> 00:09:30.829 align:start position:0%
perhaps they're taking on some of the
functional<00:09:29.720><c> of</c><00:09:29.839><c> something</c><00:09:30.160><c> like</c><00:09:30.279><c> a</c><00:09:30.440><c> Langs</c>

00:09:30.829 --> 00:09:30.839 align:start position:0%
functional of something like a Langs
 

00:09:30.839 --> 00:09:33.110 align:start position:0%
functional of something like a Langs
Smith<00:09:31.160><c> or</c><00:09:31.320><c> something</c><00:09:32.120><c> and</c><00:09:32.320><c> allowing</c><00:09:32.720><c> you</c><00:09:32.880><c> to</c>

00:09:33.110 --> 00:09:33.120 align:start position:0%
Smith or something and allowing you to
 

00:09:33.120 --> 00:09:36.829 align:start position:0%
Smith or something and allowing you to
basically<00:09:33.720><c> do</c><00:09:34.079><c> this</c><00:09:35.360><c> yourself</c><00:09:36.360><c> rather</c><00:09:36.640><c> than</c>

00:09:36.829 --> 00:09:36.839 align:start position:0%
basically do this yourself rather than
 

00:09:36.839 --> 00:09:38.509 align:start position:0%
basically do this yourself rather than
having<00:09:37.040><c> to</c><00:09:37.200><c> use</c><00:09:37.519><c> you</c><00:09:37.640><c> know</c><00:09:37.800><c> an</c><00:09:38.000><c> external</c>

00:09:38.509 --> 00:09:38.519 align:start position:0%
having to use you know an external
 

00:09:38.519 --> 00:09:40.910 align:start position:0%
having to use you know an external
service<00:09:38.959><c> for</c><00:09:39.200><c> doing</c><00:09:39.560><c> this</c><00:09:40.079><c> so</c><00:09:40.240><c> it's</c><00:09:40.360><c> a</c><00:09:40.560><c> new</c>

00:09:40.910 --> 00:09:40.920 align:start position:0%
service for doing this so it's a new
 

00:09:40.920 --> 00:09:43.470 align:start position:0%
service for doing this so it's a new
sort<00:09:41.120><c> of</c><00:09:41.399><c> project</c><00:09:41.880><c> or</c><00:09:42.240><c> framework</c><00:09:43.000><c> that</c><00:09:43.200><c> from</c>

00:09:43.470 --> 00:09:43.480 align:start position:0%
sort of project or framework that from
 

00:09:43.480 --> 00:09:45.069 align:start position:0%
sort of project or framework that from
Google<00:09:44.000><c> it's</c><00:09:44.200><c> actually</c><00:09:44.560><c> like</c><00:09:44.720><c> from</c><00:09:44.959><c> the</c>

00:09:45.069 --> 00:09:45.079 align:start position:0%
Google it's actually like from the
 

00:09:45.079 --> 00:09:48.509 align:start position:0%
Google it's actually like from the
Firebase<00:09:45.720><c> team</c><00:09:46.560><c> and</c><00:09:46.680><c> it</c><00:09:46.839><c> does</c><00:09:47.120><c> bridge</c><00:09:47.560><c> the</c><00:09:47.880><c> gap</c>

00:09:48.509 --> 00:09:48.519 align:start position:0%
Firebase team and it does bridge the gap
 

00:09:48.519 --> 00:09:51.190 align:start position:0%
Firebase team and it does bridge the gap
that<00:09:48.720><c> Firebase</c><00:09:49.720><c> had</c><00:09:50.000><c> of</c><00:09:50.240><c> where</c><00:09:50.760><c> you</c><00:09:50.959><c> either</c>

00:09:51.190 --> 00:09:51.200 align:start position:0%
that Firebase had of where you either
 

00:09:51.200 --> 00:09:55.550 align:start position:0%
that Firebase had of where you either
had<00:09:51.320><c> to</c><00:09:51.519><c> decide</c><00:09:51.920><c> to</c><00:09:52.079><c> go</c><00:09:52.279><c> fully</c><00:09:53.040><c> gcp</c><00:09:53.760><c> cloud</c><00:09:54.560><c> or</c>

00:09:55.550 --> 00:09:55.560 align:start position:0%
had to decide to go fully gcp cloud or
 

00:09:55.560 --> 00:09:58.069 align:start position:0%
had to decide to go fully gcp cloud or
if<00:09:55.680><c> you</c><00:09:55.760><c> wanted</c><00:09:55.959><c> to</c><00:09:56.079><c> use</c><00:09:56.279><c> anything</c><00:09:56.680><c> AI</c><00:09:57.399><c> so</c><00:09:57.920><c> this</c>

00:09:58.069 --> 00:09:58.079 align:start position:0%
if you wanted to use anything AI so this
 

00:09:58.079 --> 00:10:00.269 align:start position:0%
if you wanted to use anything AI so this
allows<00:09:58.480><c> you</c><00:09:58.680><c> I</c><00:09:58.760><c> think</c><00:09:58.959><c> now</c><00:09:59.560><c> to</c><00:09:59.760><c> have</c><00:09:59.959><c> sort</c><00:10:00.120><c> of</c>

00:10:00.269 --> 00:10:00.279 align:start position:0%
allows you I think now to have sort of
 

00:10:00.279 --> 00:10:03.269 align:start position:0%
allows you I think now to have sort of
things<00:10:00.560><c> like</c><00:10:00.720><c> the</c><00:10:00.839><c> vertex</c><00:10:01.240><c> AI</c><00:10:01.600><c> for</c><00:10:02.279><c> Firebase</c>

00:10:03.269 --> 00:10:03.279 align:start position:0%
things like the vertex AI for Firebase
 

00:10:03.279 --> 00:10:05.470 align:start position:0%
things like the vertex AI for Firebase
which<00:10:03.480><c> gives</c><00:10:03.680><c> you</c><00:10:03.920><c> access</c><00:10:04.200><c> to</c><00:10:04.320><c> the</c><00:10:04.480><c> Gemini</c><00:10:04.959><c> API</c>

00:10:05.470 --> 00:10:05.480 align:start position:0%
which gives you access to the Gemini API
 

00:10:05.480 --> 00:10:08.030 align:start position:0%
which gives you access to the Gemini API
and<00:10:05.640><c> other</c><00:10:05.880><c> things</c><00:10:06.480><c> easily</c><00:10:07.000><c> in</c><00:10:07.240><c> here</c><00:10:07.800><c> so</c><00:10:07.959><c> if</c>

00:10:08.030 --> 00:10:08.040 align:start position:0%
and other things easily in here so if
 

00:10:08.040 --> 00:10:09.350 align:start position:0%
and other things easily in here so if
you're<00:10:08.160><c> a</c><00:10:08.240><c> JavaScript</c><00:10:08.680><c> developer</c><00:10:09.040><c> I'd</c><00:10:09.200><c> say</c>

00:10:09.350 --> 00:10:09.360 align:start position:0%
you're a JavaScript developer I'd say
 

00:10:09.360 --> 00:10:11.269 align:start position:0%
you're a JavaScript developer I'd say
definitely<00:10:09.680><c> check</c><00:10:09.920><c> this</c><00:10:10.079><c> out</c><00:10:10.680><c> and</c><00:10:10.839><c> see</c><00:10:11.120><c> what</c>

00:10:11.269 --> 00:10:11.279 align:start position:0%
definitely check this out and see what
 

00:10:11.279 --> 00:10:13.389 align:start position:0%
definitely check this out and see what
you<00:10:11.399><c> can</c><00:10:11.880><c> actually</c><00:10:12.240><c> build</c><00:10:12.720><c> with</c><00:10:12.920><c> this</c><00:10:13.279><c> it's</c>

00:10:13.389 --> 00:10:13.399 align:start position:0%
you can actually build with this it's
 

00:10:13.399 --> 00:10:15.430 align:start position:0%
you can actually build with this it's
going<00:10:13.480><c> to</c><00:10:13.560><c> be</c><00:10:13.760><c> interesting</c><00:10:14.160><c> to</c><00:10:14.360><c> see</c><00:10:15.040><c> how</c><00:10:15.200><c> it</c>

00:10:15.430 --> 00:10:15.440 align:start position:0%
going to be interesting to see how it
 

00:10:15.440 --> 00:10:18.230 align:start position:0%
going to be interesting to see how it
goes<00:10:16.440><c> so</c><00:10:16.680><c> overall</c><00:10:17.079><c> this</c><00:10:17.200><c> is</c><00:10:17.440><c> just</c><00:10:17.760><c> three</c>

00:10:18.230 --> 00:10:18.240 align:start position:0%
goes so overall this is just three
 

00:10:18.240 --> 00:10:20.750 align:start position:0%
goes so overall this is just three
things<00:10:18.680><c> that</c><00:10:18.920><c> I</c><00:10:19.000><c> felt</c><00:10:19.320><c> like</c><00:10:19.760><c> deserved</c><00:10:20.279><c> to</c><00:10:20.440><c> be</c>

00:10:20.750 --> 00:10:20.760 align:start position:0%
things that I felt like deserved to be
 

00:10:20.760 --> 00:10:23.110 align:start position:0%
things that I felt like deserved to be
put<00:10:20.959><c> in</c><00:10:21.160><c> a</c><00:10:21.360><c> video</c><00:10:21.800><c> I</c><00:10:21.839><c> will</c><00:10:22.000><c> certainly</c><00:10:22.480><c> cover</c>

00:10:23.110 --> 00:10:23.120 align:start position:0%
put in a video I will certainly cover
 

00:10:23.120 --> 00:10:25.470 align:start position:0%
put in a video I will certainly cover
the<00:10:23.360><c> Gemma</c><00:10:23.680><c> 2</c><00:10:24.040><c> a</c><00:10:24.160><c> lot</c><00:10:24.360><c> more</c><00:10:24.640><c> when</c><00:10:24.839><c> the</c><00:10:24.959><c> model</c><00:10:25.240><c> is</c>

00:10:25.470 --> 00:10:25.480 align:start position:0%
the Gemma 2 a lot more when the model is
 

00:10:25.480 --> 00:10:27.389 align:start position:0%
the Gemma 2 a lot more when the model is
actually<00:10:25.839><c> available</c><00:10:26.800><c> I</c><00:10:26.920><c> think</c><00:10:27.040><c> we'll</c><00:10:27.200><c> do</c>

00:10:27.389 --> 00:10:27.399 align:start position:0%
actually available I think we'll do
 

00:10:27.399 --> 00:10:29.590 align:start position:0%
actually available I think we'll do
another<00:10:27.680><c> video</c><00:10:28.240><c> looking</c><00:10:28.480><c> at</c><00:10:28.640><c> the</c><00:10:28.800><c> Pary</c><00:10:29.320><c> Gemma</c>

00:10:29.590 --> 00:10:29.600 align:start position:0%
another video looking at the Pary Gemma
 

00:10:29.600 --> 00:10:31.870 align:start position:0%
another video looking at the Pary Gemma
and<00:10:29.720><c> perhaps</c><00:10:30.079><c> looking</c><00:10:30.279><c> at</c><00:10:30.480><c> fine-tuning</c><00:10:31.279><c> that</c>

00:10:31.870 --> 00:10:31.880 align:start position:0%
and perhaps looking at fine-tuning that
 

00:10:31.880 --> 00:10:32.910 align:start position:0%
and perhaps looking at fine-tuning that
but<00:10:32.000><c> these</c><00:10:32.120><c> are</c><00:10:32.279><c> some</c><00:10:32.399><c> of</c><00:10:32.480><c> the</c><00:10:32.600><c> things</c><00:10:32.800><c> that</c>

00:10:32.910 --> 00:10:32.920 align:start position:0%
but these are some of the things that
 

00:10:32.920 --> 00:10:35.190 align:start position:0%
but these are some of the things that
you<00:10:33.000><c> can</c><00:10:33.200><c> start</c><00:10:33.480><c> using</c><00:10:33.800><c> to</c><00:10:34.000><c> develop</c><00:10:34.480><c> your</c><00:10:34.720><c> own</c>

00:10:35.190 --> 00:10:35.200 align:start position:0%
you can start using to develop your own
 

00:10:35.200 --> 00:10:37.269 align:start position:0%
you can start using to develop your own
apps<00:10:35.560><c> that</c><00:10:35.720><c> Google</c><00:10:36.079><c> made</c><00:10:36.360><c> available</c><00:10:36.920><c> here</c>

00:10:37.269 --> 00:10:37.279 align:start position:0%
apps that Google made available here
 

00:10:37.279 --> 00:10:40.670 align:start position:0%
apps that Google made available here
without<00:10:37.639><c> having</c><00:10:37.839><c> to</c><00:10:38.079><c> use</c><00:10:38.519><c> the</c><00:10:39.040><c> Gemini</c><00:10:39.839><c> apis</c>

00:10:40.670 --> 00:10:40.680 align:start position:0%
without having to use the Gemini apis
 

00:10:40.680 --> 00:10:42.870 align:start position:0%
without having to use the Gemini apis
directly<00:10:41.200><c> and</c><00:10:41.399><c> stuff</c><00:10:41.639><c> like</c><00:10:41.839><c> that</c><00:10:42.040><c> so</c><00:10:42.480><c> I</c><00:10:42.760><c> kind</c>

00:10:42.870 --> 00:10:42.880 align:start position:0%
directly and stuff like that so I kind
 

00:10:42.880 --> 00:10:44.870 align:start position:0%
directly and stuff like that so I kind
of<00:10:42.959><c> felt</c><00:10:43.160><c> that</c><00:10:43.279><c> a</c><00:10:43.360><c> lot</c><00:10:43.480><c> of</c><00:10:43.639><c> these</c><00:10:43.880><c> did</c><00:10:44.120><c> get</c><00:10:44.360><c> lost</c>

00:10:44.870 --> 00:10:44.880 align:start position:0%
of felt that a lot of these did get lost
 

00:10:44.880 --> 00:10:47.670 align:start position:0%
of felt that a lot of these did get lost
in<00:10:45.079><c> the</c><00:10:45.519><c> main</c><00:10:45.880><c> announcements</c><00:10:46.600><c> around</c><00:10:47.079><c> IO</c><00:10:47.560><c> and</c>

00:10:47.670 --> 00:10:47.680 align:start position:0%
in the main announcements around IO and
 

00:10:47.680 --> 00:10:49.509 align:start position:0%
in the main announcements around IO and
deserved<00:10:48.079><c> at</c><00:10:48.200><c> least</c><00:10:48.360><c> a</c><00:10:48.519><c> small</c><00:10:48.800><c> video</c><00:10:49.160><c> looking</c>

00:10:49.509 --> 00:10:49.519 align:start position:0%
deserved at least a small video looking
 

00:10:49.519 --> 00:10:51.910 align:start position:0%
deserved at least a small video looking
at<00:10:49.800><c> at</c><00:10:49.959><c> them</c><00:10:50.560><c> so</c><00:10:50.760><c> anyway</c><00:10:51.160><c> as</c><00:10:51.320><c> always</c><00:10:51.680><c> if</c><00:10:51.760><c> you've</c>

00:10:51.910 --> 00:10:51.920 align:start position:0%
at at them so anyway as always if you've
 

00:10:51.920 --> 00:10:53.310 align:start position:0%
at at them so anyway as always if you've
got<00:10:52.040><c> any</c><00:10:52.200><c> comments</c><00:10:52.600><c> please</c><00:10:52.839><c> put</c><00:10:52.959><c> them</c><00:10:53.079><c> in</c><00:10:53.200><c> the</c>

00:10:53.310 --> 00:10:53.320 align:start position:0%
got any comments please put them in the
 

00:10:53.320 --> 00:10:56.150 align:start position:0%
got any comments please put them in the
comments<00:10:53.680><c> below</c><00:10:54.240><c> any</c><00:10:54.519><c> questions</c><00:10:55.160><c> let</c><00:10:55.279><c> me</c><00:10:55.519><c> know</c>

00:10:56.150 --> 00:10:56.160 align:start position:0%
comments below any questions let me know
 

00:10:56.160 --> 00:10:57.629 align:start position:0%
comments below any questions let me know
if<00:10:56.279><c> you</c><00:10:56.360><c> found</c><00:10:56.560><c> the</c><00:10:56.680><c> video</c><00:10:56.959><c> useful</c><00:10:57.399><c> please</c>

00:10:57.629 --> 00:10:57.639 align:start position:0%
if you found the video useful please
 

00:10:57.639 --> 00:10:59.550 align:start position:0%
if you found the video useful please
click<00:10:57.880><c> like</c><00:10:58.040><c> And</c><00:10:58.240><c> subscribe</c><00:10:58.839><c> and</c><00:10:59.320><c> we'll</c><00:10:59.440><c> talk</c>

00:10:59.550 --> 00:10:59.560 align:start position:0%
click like And subscribe and we'll talk
 

00:10:59.560 --> 00:11:02.839 align:start position:0%
click like And subscribe and we'll talk
to<00:10:59.639><c> you</c><00:10:59.720><c> in</c><00:10:59.839><c> the</c><00:10:59.959><c> next</c><00:11:00.160><c> video</c>

