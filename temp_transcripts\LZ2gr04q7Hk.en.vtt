WEBVTT
Kind: captions
Language: en

00:00:00.730 --> 00:00:06.869 align:start position:0%
 
[Music]

00:00:06.869 --> 00:00:06.879 align:start position:0%
 
 

00:00:06.879 --> 00:00:09.750 align:start position:0%
 
welcome<00:00:07.319><c> to</c><00:00:07.480><c> module</c><00:00:07.800><c> 2</c><00:00:08.800><c> in</c><00:00:08.960><c> this</c><00:00:09.160><c> module</c><00:00:09.639><c> we</c>

00:00:09.750 --> 00:00:09.760 align:start position:0%
welcome to module 2 in this module we
 

00:00:09.760 --> 00:00:12.270 align:start position:0%
welcome to module 2 in this module we
want<00:00:09.920><c> to</c><00:00:10.599><c> understand</c><00:00:11.040><c> how</c><00:00:11.519><c> large</c><00:00:11.880><c> language</c>

00:00:12.270 --> 00:00:12.280 align:start position:0%
want to understand how large language
 

00:00:12.280 --> 00:00:15.150 align:start position:0%
want to understand how large language
models<00:00:12.839><c> work</c><00:00:13.839><c> but</c><00:00:14.080><c> first</c><00:00:14.519><c> let's</c><00:00:14.799><c> check</c><00:00:14.960><c> out</c>

00:00:15.150 --> 00:00:15.160 align:start position:0%
models work but first let's check out
 

00:00:15.160 --> 00:00:18.830 align:start position:0%
models work but first let's check out
some<00:00:15.360><c> use</c><00:00:15.679><c> cases</c><00:00:16.080><c> that</c><00:00:16.279><c> llms</c><00:00:17.160><c> enable</c><00:00:18.160><c> llms</c><00:00:18.720><c> can</c>

00:00:18.830 --> 00:00:18.840 align:start position:0%
some use cases that llms enable llms can
 

00:00:18.840 --> 00:00:21.550 align:start position:0%
some use cases that llms enable llms can
be<00:00:18.920><c> used</c><00:00:19.119><c> to</c><00:00:19.279><c> generate</c><00:00:19.800><c> text</c><00:00:20.800><c> like</c><00:00:21.039><c> marketing</c>

00:00:21.550 --> 00:00:21.560 align:start position:0%
be used to generate text like marketing
 

00:00:21.560 --> 00:00:25.269 align:start position:0%
be used to generate text like marketing
copy<00:00:21.880><c> or</c><00:00:22.320><c> emails</c><00:00:23.320><c> they</c><00:00:23.480><c> can</c><00:00:23.760><c> answer</c><00:00:24.279><c> questions</c>

00:00:25.269 --> 00:00:25.279 align:start position:0%
copy or emails they can answer questions
 

00:00:25.279 --> 00:00:27.950 align:start position:0%
copy or emails they can answer questions
translate<00:00:26.039><c> documents</c><00:00:27.039><c> and</c><00:00:27.320><c> determine</c><00:00:27.760><c> the</c>

00:00:27.950 --> 00:00:27.960 align:start position:0%
translate documents and determine the
 

00:00:27.960 --> 00:00:31.830 align:start position:0%
translate documents and determine the
sentiment<00:00:28.560><c> of</c><00:00:28.679><c> a</c><00:00:28.960><c> text</c><00:00:30.400><c> llms</c><00:00:31.000><c> can</c><00:00:31.320><c> summarize</c>

00:00:31.830 --> 00:00:31.840 align:start position:0%
sentiment of a text llms can summarize
 

00:00:31.840 --> 00:00:34.190 align:start position:0%
sentiment of a text llms can summarize
long<00:00:32.160><c> documents</c><00:00:32.880><c> they</c><00:00:33.000><c> can</c><00:00:33.239><c> act</c><00:00:33.440><c> as</c><00:00:33.719><c> personal</c>

00:00:34.190 --> 00:00:34.200 align:start position:0%
long documents they can act as personal
 

00:00:34.200 --> 00:00:36.510 align:start position:0%
long documents they can act as personal
assistants<00:00:34.719><c> or</c><00:00:34.920><c> chatbots</c><00:00:35.920><c> we</c><00:00:36.040><c> can</c><00:00:36.200><c> use</c><00:00:36.399><c> them</c>

00:00:36.510 --> 00:00:36.520 align:start position:0%
assistants or chatbots we can use them
 

00:00:36.520 --> 00:00:39.790 align:start position:0%
assistants or chatbots we can use them
to<00:00:36.680><c> query</c><00:00:37.000><c> tabular</c><00:00:37.480><c> data</c><00:00:38.239><c> interact</c><00:00:38.760><c> with</c><00:00:38.960><c> API</c>

00:00:39.790 --> 00:00:39.800 align:start position:0%
to query tabular data interact with API
 

00:00:39.800 --> 00:00:43.670 align:start position:0%
to query tabular data interact with API
or<00:00:40.079><c> even</c><00:00:40.559><c> evaluate</c><00:00:41.320><c> other</c><00:00:42.000><c> language</c>

00:00:43.670 --> 00:00:43.680 align:start position:0%
or even evaluate other language
 

00:00:43.680 --> 00:00:46.430 align:start position:0%
or even evaluate other language
models<00:00:44.680><c> but</c><00:00:44.920><c> what</c><00:00:45.079><c> happens</c><00:00:45.399><c> behind</c><00:00:45.680><c> the</c>

00:00:46.430 --> 00:00:46.440 align:start position:0%
models but what happens behind the
 

00:00:46.440 --> 00:00:49.389 align:start position:0%
models but what happens behind the
scenes<00:00:47.440><c> understanding</c><00:00:47.960><c> llm</c><00:00:48.559><c> architecture</c>

00:00:49.389 --> 00:00:49.399 align:start position:0%
scenes understanding llm architecture
 

00:00:49.399 --> 00:00:51.430 align:start position:0%
scenes understanding llm architecture
isn't<00:00:49.840><c> necessary</c><00:00:50.719><c> for</c><00:00:50.960><c> building</c>

00:00:51.430 --> 00:00:51.440 align:start position:0%
isn't necessary for building
 

00:00:51.440 --> 00:00:54.110 align:start position:0%
isn't necessary for building
applications<00:00:52.280><c> it's</c><00:00:52.440><c> like</c><00:00:52.719><c> driving</c><00:00:53.079><c> a</c><00:00:53.280><c> car</c><00:00:54.000><c> you</c>

00:00:54.110 --> 00:00:54.120 align:start position:0%
applications it's like driving a car you
 

00:00:54.120 --> 00:00:55.950 align:start position:0%
applications it's like driving a car you
don't<00:00:54.320><c> need</c><00:00:54.440><c> to</c><00:00:54.600><c> know</c><00:00:54.920><c> how</c><00:00:55.079><c> the</c><00:00:55.280><c> engine</c><00:00:55.640><c> works</c>

00:00:55.950 --> 00:00:55.960 align:start position:0%
don't need to know how the engine works
 

00:00:55.960 --> 00:00:59.150 align:start position:0%
don't need to know how the engine works
to<00:00:56.160><c> drive</c><00:00:57.160><c> still</c><00:00:57.920><c> some</c><00:00:58.239><c> technical</c><00:00:58.680><c> details</c>

00:00:59.150 --> 00:00:59.160 align:start position:0%
to drive still some technical details
 

00:00:59.160 --> 00:01:01.630 align:start position:0%
to drive still some technical details
can<00:00:59.280><c> be</c><00:00:59.440><c> helpful</c>

00:01:01.630 --> 00:01:01.640 align:start position:0%
can be helpful
 

00:01:01.640 --> 00:01:04.509 align:start position:0%
can be helpful
looking<00:01:01.960><c> at</c><00:01:02.320><c> gp4</c><00:01:03.320><c> technical</c><00:01:03.800><c> report</c><00:01:04.199><c> we</c><00:01:04.320><c> can</c>

00:01:04.509 --> 00:01:04.519 align:start position:0%
looking at gp4 technical report we can
 

00:01:04.519 --> 00:01:08.990 align:start position:0%
looking at gp4 technical report we can
read<00:01:04.960><c> that</c><00:01:05.760><c> gp4</c><00:01:06.760><c> one</c><00:01:06.920><c> of</c><00:01:07.080><c> the</c><00:01:07.240><c> most</c><00:01:07.520><c> known</c><00:01:08.000><c> llms</c>

00:01:08.990 --> 00:01:09.000 align:start position:0%
read that gp4 one of the most known llms
 

00:01:09.000 --> 00:01:12.310 align:start position:0%
read that gp4 one of the most known llms
is<00:01:09.119><c> a</c><00:01:09.360><c> Transformer</c><00:01:10.080><c> based</c><00:01:11.000><c> model</c><00:01:11.680><c> pre-trained</c>

00:01:12.310 --> 00:01:12.320 align:start position:0%
is a Transformer based model pre-trained
 

00:01:12.320 --> 00:01:15.310 align:start position:0%
is a Transformer based model pre-trained
to<00:01:12.520><c> predict</c><00:01:12.880><c> the</c><00:01:13.080><c> next</c><00:01:13.400><c> token</c><00:01:14.119><c> in</c><00:01:14.280><c> a</c>

00:01:15.310 --> 00:01:15.320 align:start position:0%
to predict the next token in a
 

00:01:15.320 --> 00:01:18.670 align:start position:0%
to predict the next token in a
document<00:01:16.320><c> we</c><00:01:16.520><c> won't</c><00:01:16.920><c> dive</c><00:01:17.400><c> and</c><00:01:17.880><c> and</c><00:01:18.040><c> try</c><00:01:18.240><c> to</c>

00:01:18.670 --> 00:01:18.680 align:start position:0%
document we won't dive and and try to
 

00:01:18.680 --> 00:01:20.749 align:start position:0%
document we won't dive and and try to
understand<00:01:18.799><c> the</c><00:01:18.960><c> Transformer</c><00:01:19.759><c> architecture</c>

00:01:20.749 --> 00:01:20.759 align:start position:0%
understand the Transformer architecture
 

00:01:20.759 --> 00:01:23.230 align:start position:0%
understand the Transformer architecture
that's<00:01:21.040><c> not</c><00:01:21.360><c> necessary</c><00:01:21.920><c> for</c><00:01:22.119><c> building</c><00:01:22.560><c> Alm</c>

00:01:23.230 --> 00:01:23.240 align:start position:0%
that's not necessary for building Alm
 

00:01:23.240 --> 00:01:25.510 align:start position:0%
that's not necessary for building Alm
applications<00:01:24.240><c> but</c><00:01:24.360><c> we</c><00:01:24.479><c> want</c><00:01:24.680><c> to</c><00:01:25.079><c> we</c><00:01:25.200><c> want</c><00:01:25.320><c> to</c>

00:01:25.510 --> 00:01:25.520 align:start position:0%
applications but we want to we want to
 

00:01:25.520 --> 00:01:27.030 align:start position:0%
applications but we want to we want to
focus<00:01:25.840><c> on</c><00:01:26.000><c> the</c><00:01:26.200><c> second</c><00:01:26.560><c> part</c><00:01:26.720><c> of</c><00:01:26.840><c> the</c>

00:01:27.030 --> 00:01:27.040 align:start position:0%
focus on the second part of the
 

00:01:27.040 --> 00:01:29.149 align:start position:0%
focus on the second part of the
statement<00:01:27.759><c> which</c><00:01:27.880><c> is</c><00:01:28.119><c> predicting</c><00:01:28.640><c> the</c><00:01:28.840><c> next</c>

00:01:29.149 --> 00:01:29.159 align:start position:0%
statement which is predicting the next
 

00:01:29.159 --> 00:01:30.830 align:start position:0%
statement which is predicting the next
token<00:01:29.960><c> in</c><00:01:30.079><c> a</c>

00:01:30.830 --> 00:01:30.840 align:start position:0%
token in a
 

00:01:30.840 --> 00:01:33.630 align:start position:0%
token in a
document<00:01:31.840><c> so</c><00:01:32.079><c> here's</c><00:01:32.320><c> how</c><00:01:32.520><c> this</c><00:01:32.720><c> works</c><00:01:33.479><c> we</c>

00:01:33.630 --> 00:01:33.640 align:start position:0%
document so here's how this works we
 

00:01:33.640 --> 00:01:36.190 align:start position:0%
document so here's how this works we
start<00:01:33.960><c> with</c><00:01:34.159><c> some</c><00:01:34.439><c> input</c><00:01:34.840><c> text</c><00:01:35.520><c> in</c><00:01:35.680><c> our</c><00:01:35.920><c> case</c>

00:01:36.190 --> 00:01:36.200 align:start position:0%
start with some input text in our case
 

00:01:36.200 --> 00:01:39.030 align:start position:0%
start with some input text in our case
weights<00:01:36.479><c> and</c><00:01:36.640><c> biases</c><00:01:37.119><c> is</c><00:01:38.119><c> then</c><00:01:38.280><c> we</c><00:01:38.479><c> tokenize</c>

00:01:39.030 --> 00:01:39.040 align:start position:0%
weights and biases is then we tokenize
 

00:01:39.040 --> 00:01:41.469 align:start position:0%
weights and biases is then we tokenize
the<00:01:39.240><c> text</c><00:01:39.560><c> we</c><00:01:39.680><c> need</c><00:01:39.840><c> to</c><00:01:40.000><c> split</c><00:01:40.360><c> it</c><00:01:40.560><c> into</c><00:01:40.880><c> tokens</c>

00:01:41.469 --> 00:01:41.479 align:start position:0%
the text we need to split it into tokens
 

00:01:41.479 --> 00:01:44.030 align:start position:0%
the text we need to split it into tokens
that<00:01:41.600><c> are</c><00:01:42.000><c> represented</c><00:01:42.560><c> by</c><00:01:42.759><c> numbers</c><00:01:43.680><c> that</c><00:01:43.840><c> we</c>

00:01:44.030 --> 00:01:44.040 align:start position:0%
that are represented by numbers that we
 

00:01:44.040 --> 00:01:46.550 align:start position:0%
that are represented by numbers that we
fit<00:01:44.439><c> into</c><00:01:44.719><c> the</c><00:01:44.880><c> black</c><00:01:45.200><c> box</c><00:01:45.560><c> which</c><00:01:45.680><c> is</c><00:01:45.880><c> the</c>

00:01:46.550 --> 00:01:46.560 align:start position:0%
fit into the black box which is the
 

00:01:46.560 --> 00:01:49.749 align:start position:0%
fit into the black box which is the
llm<00:01:47.560><c> then</c><00:01:47.880><c> as</c><00:01:48.000><c> an</c><00:01:48.280><c> output</c><00:01:48.680><c> of</c><00:01:48.840><c> the</c><00:01:49.000><c> llm</c><00:01:49.520><c> we</c><00:01:49.640><c> have</c>

00:01:49.749 --> 00:01:49.759 align:start position:0%
llm then as an output of the llm we have
 

00:01:49.759 --> 00:01:52.270 align:start position:0%
llm then as an output of the llm we have
a<00:01:49.960><c> distribution</c><00:01:50.479><c> of</c><00:01:50.799><c> probabilities</c><00:01:51.799><c> over</c><00:01:52.119><c> the</c>

00:01:52.270 --> 00:01:52.280 align:start position:0%
a distribution of probabilities over the
 

00:01:52.280 --> 00:01:55.109 align:start position:0%
a distribution of probabilities over the
entire<00:01:52.880><c> vocabulary</c><00:01:53.880><c> all</c><00:01:54.000><c> of</c><00:01:54.159><c> the</c><00:01:54.320><c> tokens</c><00:01:55.000><c> that</c>

00:01:55.109 --> 00:01:55.119 align:start position:0%
entire vocabulary all of the tokens that
 

00:01:55.119 --> 00:01:58.550 align:start position:0%
entire vocabulary all of the tokens that
we<00:01:55.320><c> have</c><00:01:55.759><c> available</c><00:01:56.640><c> for</c><00:01:56.880><c> our</c><00:01:57.119><c> model</c><00:01:58.079><c> and</c><00:01:58.399><c> each</c>

00:01:58.550 --> 00:01:58.560 align:start position:0%
we have available for our model and each
 

00:01:58.560 --> 00:02:01.350 align:start position:0%
we have available for our model and each
of<00:01:58.719><c> these</c><00:01:58.920><c> tokens</c><00:01:59.360><c> comes</c><00:01:59.840><c> with</c><00:02:00.000><c> a</c><00:02:00.360><c> probability</c>

00:02:01.350 --> 00:02:01.360 align:start position:0%
of these tokens comes with a probability
 

00:02:01.360 --> 00:02:03.469 align:start position:0%
of these tokens comes with a probability
that<00:02:01.680><c> it</c><00:02:01.920><c> comes</c><00:02:02.280><c> as</c><00:02:02.399><c> a</c><00:02:02.600><c> next</c><00:02:02.920><c> token</c><00:02:03.200><c> in</c><00:02:03.320><c> the</c>

00:02:03.469 --> 00:02:03.479 align:start position:0%
that it comes as a next token in the
 

00:02:03.479 --> 00:02:05.190 align:start position:0%
that it comes as a next token in the
sequence<00:02:04.200><c> and</c><00:02:04.479><c> based</c><00:02:04.759><c> on</c><00:02:04.920><c> those</c>

00:02:05.190 --> 00:02:05.200 align:start position:0%
sequence and based on those
 

00:02:05.200 --> 00:02:08.510 align:start position:0%
sequence and based on those
probabilities<00:02:05.920><c> we</c><00:02:06.159><c> pick</c><00:02:06.439><c> we</c><00:02:06.719><c> sample</c><00:02:07.719><c> uh</c><00:02:08.360><c> one</c>

00:02:08.510 --> 00:02:08.520 align:start position:0%
probabilities we pick we sample uh one
 

00:02:08.520 --> 00:02:11.630 align:start position:0%
probabilities we pick we sample uh one
of<00:02:08.679><c> the</c><00:02:09.039><c> tokens</c><00:02:10.039><c> to</c><00:02:10.280><c> follow</c><00:02:10.800><c> to</c><00:02:11.080><c> continue</c><00:02:11.520><c> with</c>

00:02:11.630 --> 00:02:11.640 align:start position:0%
of the tokens to follow to continue with
 

00:02:11.640 --> 00:02:13.949 align:start position:0%
of the tokens to follow to continue with
the<00:02:11.800><c> sequence</c><00:02:12.599><c> in</c><00:02:12.760><c> this</c><00:02:12.959><c> case</c><00:02:13.319><c> we</c><00:02:13.480><c> select</c><00:02:13.800><c> the</c>

00:02:13.949 --> 00:02:13.959 align:start position:0%
the sequence in this case we select the
 

00:02:13.959 --> 00:02:17.390 align:start position:0%
the sequence in this case we select the
token<00:02:14.239><c> the</c><00:02:14.640><c> because</c><00:02:14.920><c> it</c><00:02:15.120><c> has</c><00:02:15.239><c> a</c><00:02:15.480><c> high</c><00:02:15.840><c> output</c>

00:02:17.390 --> 00:02:17.400 align:start position:0%
token the because it has a high output
 

00:02:17.400 --> 00:02:20.070 align:start position:0%
token the because it has a high output
probability<00:02:18.400><c> then</c><00:02:18.720><c> we</c><00:02:18.920><c> append</c><00:02:19.319><c> this</c><00:02:19.519><c> token</c><00:02:19.879><c> to</c>

00:02:20.070 --> 00:02:20.080 align:start position:0%
probability then we append this token to
 

00:02:20.080 --> 00:02:22.030 align:start position:0%
probability then we append this token to
our<00:02:20.280><c> input</c><00:02:20.640><c> sequence</c><00:02:21.200><c> and</c><00:02:21.319><c> we</c><00:02:21.519><c> repeat</c><00:02:21.840><c> the</c>

00:02:22.030 --> 00:02:22.040 align:start position:0%
our input sequence and we repeat the
 

00:02:22.040 --> 00:02:24.869 align:start position:0%
our input sequence and we repeat the
process<00:02:22.720><c> we</c><00:02:22.920><c> tokenize</c><00:02:23.440><c> it</c><00:02:24.040><c> we</c><00:02:24.239><c> fit</c><00:02:24.519><c> it</c><00:02:24.680><c> into</c>

00:02:24.869 --> 00:02:24.879 align:start position:0%
process we tokenize it we fit it into
 

00:02:24.879 --> 00:02:27.830 align:start position:0%
process we tokenize it we fit it into
the<00:02:25.000><c> llm</c><00:02:25.640><c> and</c><00:02:25.840><c> again</c><00:02:26.080><c> we</c><00:02:26.239><c> get</c><00:02:26.920><c> a</c><00:02:27.239><c> distribution</c>

00:02:27.830 --> 00:02:27.840 align:start position:0%
the llm and again we get a distribution
 

00:02:27.840 --> 00:02:30.830 align:start position:0%
the llm and again we get a distribution
of<00:02:28.080><c> probabilities</c><00:02:28.760><c> across</c><00:02:29.200><c> our</c><00:02:30.000><c> vocabulary</c>

00:02:30.830 --> 00:02:30.840 align:start position:0%
of probabilities across our vocabulary
 

00:02:30.840 --> 00:02:33.350 align:start position:0%
of probabilities across our vocabulary
all<00:02:31.000><c> of</c><00:02:31.120><c> the</c><00:02:31.280><c> tokens</c><00:02:32.080><c> and</c><00:02:32.400><c> again</c><00:02:32.599><c> we</c><00:02:32.840><c> pick</c><00:02:33.200><c> a</c>

00:02:33.350 --> 00:02:33.360 align:start position:0%
all of the tokens and again we pick a
 

00:02:33.360 --> 00:02:36.070 align:start position:0%
all of the tokens and again we pick a
token<00:02:33.760><c> with</c><00:02:34.000><c> high</c><00:02:34.400><c> probability</c><00:02:35.400><c> in</c><00:02:35.560><c> this</c><00:02:35.760><c> case</c>

00:02:36.070 --> 00:02:36.080 align:start position:0%
token with high probability in this case
 

00:02:36.080 --> 00:02:37.830 align:start position:0%
token with high probability in this case
let's<00:02:36.319><c> pick</c>

00:02:37.830 --> 00:02:37.840 align:start position:0%
let's pick
 

00:02:37.840 --> 00:02:41.550 align:start position:0%
let's pick
machine<00:02:38.840><c> and</c><00:02:39.319><c> finally</c><00:02:40.200><c> uh</c><00:02:40.480><c> we</c><00:02:40.760><c> again</c><00:02:41.159><c> repeat</c>

00:02:41.550 --> 00:02:41.560 align:start position:0%
machine and finally uh we again repeat
 

00:02:41.560 --> 00:02:43.470 align:start position:0%
machine and finally uh we again repeat
this<00:02:41.720><c> whole</c><00:02:41.959><c> process</c><00:02:42.519><c> and</c><00:02:42.760><c> we</c><00:02:42.920><c> sample</c><00:02:43.360><c> the</c>

00:02:43.470 --> 00:02:43.480 align:start position:0%
this whole process and we sample the
 

00:02:43.480 --> 00:02:47.149 align:start position:0%
this whole process and we sample the
token<00:02:44.000><c> learning</c><00:02:45.319><c> and</c><00:02:46.319><c> if</c><00:02:46.440><c> we</c><00:02:46.640><c> continue</c><00:02:47.000><c> with</c>

00:02:47.149 --> 00:02:47.159 align:start position:0%
token learning and if we continue with
 

00:02:47.159 --> 00:02:49.470 align:start position:0%
token learning and if we continue with
this<00:02:47.400><c> process</c><00:02:47.920><c> we</c><00:02:48.040><c> can</c><00:02:48.599><c> predict</c><00:02:49.200><c> we</c><00:02:49.319><c> can</c>

00:02:49.470 --> 00:02:49.480 align:start position:0%
this process we can predict we can
 

00:02:49.480 --> 00:02:51.710 align:start position:0%
this process we can predict we can
sample<00:02:49.879><c> the</c><00:02:50.080><c> text</c><00:02:50.360><c> weights</c><00:02:50.640><c> and</c><00:02:50.800><c> biases</c><00:02:51.360><c> is</c>

00:02:51.710 --> 00:02:51.720 align:start position:0%
sample the text weights and biases is
 

00:02:51.720 --> 00:02:54.550 align:start position:0%
sample the text weights and biases is
the<00:02:52.200><c> machine</c><00:02:52.599><c> learning</c>

00:02:54.550 --> 00:02:54.560 align:start position:0%
the machine learning
 

00:02:54.560 --> 00:02:57.910 align:start position:0%
the machine learning
platform<00:02:55.560><c> companies</c><00:02:56.120><c> like</c><00:02:56.400><c> open</c><00:02:56.720><c> AI</c><00:02:57.239><c> C</c><00:02:57.519><c> here</c>

00:02:57.910 --> 00:02:57.920 align:start position:0%
platform companies like open AI C here
 

00:02:57.920 --> 00:03:00.309 align:start position:0%
platform companies like open AI C here
Mosaic<00:02:58.560><c> or</c><00:02:58.760><c> meta</c><00:02:59.239><c> have</c><00:02:59.400><c> already</c><00:02:59.959><c> trained</c>

00:03:00.309 --> 00:03:00.319 align:start position:0%
Mosaic or meta have already trained
 

00:03:00.319 --> 00:03:03.309 align:start position:0%
Mosaic or meta have already trained
models<00:03:01.040><c> for</c><00:03:01.400><c> us</c><00:03:02.319><c> and</c><00:03:02.480><c> we</c><00:03:02.640><c> use</c><00:03:02.840><c> them</c><00:03:03.000><c> behind</c>

00:03:03.309 --> 00:03:03.319 align:start position:0%
models for us and we use them behind
 

00:03:03.319 --> 00:03:05.350 align:start position:0%
models for us and we use them behind
apis<00:03:03.840><c> which</c><00:03:03.959><c> means</c><00:03:04.200><c> we</c><00:03:04.319><c> do</c><00:03:04.480><c> not</c><00:03:04.720><c> need</c><00:03:04.920><c> to</c><00:03:05.080><c> train</c>

00:03:05.350 --> 00:03:05.360 align:start position:0%
apis which means we do not need to train
 

00:03:05.360 --> 00:03:07.750 align:start position:0%
apis which means we do not need to train
these<00:03:05.560><c> models</c><00:03:06.400><c> to</c><00:03:06.680><c> use</c><00:03:06.920><c> them</c><00:03:07.040><c> in</c><00:03:07.159><c> our</c>

00:03:07.750 --> 00:03:07.760 align:start position:0%
these models to use them in our
 

00:03:07.760 --> 00:03:09.910 align:start position:0%
these models to use them in our
applications<00:03:08.760><c> however</c><00:03:09.080><c> knowing</c><00:03:09.599><c> how</c><00:03:09.799><c> they</c>

00:03:09.910 --> 00:03:09.920 align:start position:0%
applications however knowing how they
 

00:03:09.920 --> 00:03:12.670 align:start position:0%
applications however knowing how they
were<00:03:10.120><c> trained</c><00:03:10.599><c> can</c><00:03:10.799><c> provide</c><00:03:11.120><c> useful</c>

00:03:12.670 --> 00:03:12.680 align:start position:0%
were trained can provide useful
 

00:03:12.680 --> 00:03:16.550 align:start position:0%
were trained can provide useful
insights<00:03:13.680><c> there</c><00:03:14.080><c> are</c><00:03:15.080><c> two</c><00:03:15.720><c> main</c><00:03:16.000><c> steps</c><00:03:16.400><c> in</c>

00:03:16.550 --> 00:03:16.560 align:start position:0%
insights there are two main steps in
 

00:03:16.560 --> 00:03:19.309 align:start position:0%
insights there are two main steps in
training<00:03:16.959><c> llms</c><00:03:17.959><c> the</c><00:03:18.120><c> first</c><00:03:18.319><c> is</c><00:03:18.519><c> pre-training</c>

00:03:19.309 --> 00:03:19.319 align:start position:0%
training llms the first is pre-training
 

00:03:19.319 --> 00:03:21.509 align:start position:0%
training llms the first is pre-training
where<00:03:19.480><c> the</c><00:03:19.560><c> model</c><00:03:19.920><c> learns</c><00:03:20.599><c> from</c><00:03:20.840><c> a</c><00:03:21.080><c> massive</c>

00:03:21.509 --> 00:03:21.519 align:start position:0%
where the model learns from a massive
 

00:03:21.519 --> 00:03:24.149 align:start position:0%
where the model learns from a massive
data<00:03:21.840><c> set</c><00:03:22.440><c> with</c><00:03:22.640><c> sources</c><00:03:23.040><c> like</c><00:03:23.200><c> the</c><00:03:23.360><c> entire</c>

00:03:24.149 --> 00:03:24.159 align:start position:0%
data set with sources like the entire
 

00:03:24.159 --> 00:03:26.869 align:start position:0%
data set with sources like the entire
internet<00:03:25.040><c> such</c><00:03:25.200><c> as</c><00:03:25.400><c> common</c><00:03:25.680><c> C</c>

00:03:26.869 --> 00:03:26.879 align:start position:0%
internet such as common C
 

00:03:26.879 --> 00:03:30.390 align:start position:0%
internet such as common C
C4<00:03:27.879><c> uh</c><00:03:28.080><c> GitHub</c><00:03:28.680><c> Wikipedia</c><00:03:29.400><c> book</c><00:03:29.760><c> books</c>

00:03:30.390 --> 00:03:30.400 align:start position:0%
C4 uh GitHub Wikipedia book books
 

00:03:30.400 --> 00:03:33.190 align:start position:0%
C4 uh GitHub Wikipedia book books
archive<00:03:31.159><c> which</c><00:03:31.760><c> are</c><00:03:32.040><c> academic</c><00:03:32.519><c> papers</c><00:03:33.040><c> and</c>

00:03:33.190 --> 00:03:33.200 align:start position:0%
archive which are academic papers and
 

00:03:33.200 --> 00:03:34.830 align:start position:0%
archive which are academic papers and
Stock<00:03:33.560><c> Exchange</c><00:03:34.120><c> which</c><00:03:34.200><c> is</c><00:03:34.319><c> a</c><00:03:34.480><c> set</c><00:03:34.640><c> of</c>

00:03:34.830 --> 00:03:34.840 align:start position:0%
Stock Exchange which is a set of
 

00:03:34.840 --> 00:03:37.350 align:start position:0%
Stock Exchange which is a set of
questions<00:03:35.200><c> and</c><00:03:35.640><c> answers</c><00:03:36.640><c> this</c><00:03:36.799><c> pre-training</c>

00:03:37.350 --> 00:03:37.360 align:start position:0%
questions and answers this pre-training
 

00:03:37.360 --> 00:03:40.470 align:start position:0%
questions and answers this pre-training
data<00:03:37.720><c> set</c><00:03:37.959><c> has</c><00:03:38.080><c> been</c><00:03:38.319><c> published</c><00:03:38.799><c> by</c><00:03:39.400><c> meta</c><00:03:40.319><c> uh</c>

00:03:40.470 --> 00:03:40.480 align:start position:0%
data set has been published by meta uh
 

00:03:40.480 --> 00:03:42.710 align:start position:0%
data set has been published by meta uh
that<00:03:40.640><c> trained</c><00:03:40.959><c> Lama</c><00:03:41.319><c> model</c><00:03:41.959><c> we</c><00:03:42.120><c> don't</c><00:03:42.319><c> know</c>

00:03:42.710 --> 00:03:42.720 align:start position:0%
that trained Lama model we don't know
 

00:03:42.720 --> 00:03:44.710 align:start position:0%
that trained Lama model we don't know
exactly<00:03:43.239><c> the</c><00:03:43.439><c> pre-training</c><00:03:44.000><c> data</c><00:03:44.319><c> set</c><00:03:44.480><c> used</c>

00:03:44.710 --> 00:03:44.720 align:start position:0%
exactly the pre-training data set used
 

00:03:44.720 --> 00:03:47.429 align:start position:0%
exactly the pre-training data set used
for<00:03:44.879><c> training</c><00:03:45.159><c> GPT</c><00:03:45.599><c> 4</c><00:03:46.280><c> but</c><00:03:46.400><c> we</c><00:03:46.519><c> can</c><00:03:46.760><c> imagine</c><00:03:47.159><c> it</c>

00:03:47.429 --> 00:03:47.439 align:start position:0%
for training GPT 4 but we can imagine it
 

00:03:47.439 --> 00:03:49.589 align:start position:0%
for training GPT 4 but we can imagine it
must<00:03:47.599><c> have</c><00:03:47.760><c> been</c><00:03:47.959><c> something</c>

00:03:49.589 --> 00:03:49.599 align:start position:0%
must have been something
 

00:03:49.599 --> 00:03:52.429 align:start position:0%
must have been something
similar<00:03:50.599><c> in</c><00:03:50.799><c> this</c><00:03:51.040><c> case</c><00:03:51.319><c> in</c><00:03:51.519><c> pre-training</c><00:03:52.200><c> a</c>

00:03:52.429 --> 00:03:52.439 align:start position:0%
similar in this case in pre-training a
 

00:03:52.439 --> 00:03:54.990 align:start position:0%
similar in this case in pre-training a
model<00:03:53.439><c> that</c><00:03:53.720><c> has</c><00:03:53.879><c> gone</c><00:03:54.079><c> through</c><00:03:54.280><c> this</c><00:03:54.480><c> phase</c>

00:03:54.990 --> 00:03:55.000 align:start position:0%
model that has gone through this phase
 

00:03:55.000 --> 00:03:58.589 align:start position:0%
model that has gone through this phase
is<00:03:55.480><c> pretty</c><00:03:55.799><c> good</c><00:03:56.079><c> in</c><00:03:56.799><c> predicting</c><00:03:57.799><c> texts</c><00:03:58.280><c> such</c>

00:03:58.589 --> 00:03:58.599 align:start position:0%
is pretty good in predicting texts such
 

00:03:58.599 --> 00:04:00.429 align:start position:0%
is pretty good in predicting texts such
as<00:03:58.760><c> found</c><00:03:59.040><c> in</c><00:03:59.159><c> this</c><00:03:59.319><c> data</c><00:03:59.760><c> set</c><00:04:00.000><c> on</c><00:04:00.120><c> the</c>

00:04:00.429 --> 00:04:00.439 align:start position:0%
as found in this data set on the
 

00:04:00.439 --> 00:04:03.949 align:start position:0%
as found in this data set on the
internet<00:04:01.439><c> uh</c><00:04:01.680><c> on</c><00:04:01.920><c> on</c><00:04:02.159><c> GitHub</c><00:04:02.799><c> on</c><00:04:03.000><c> Wikipedia</c>

00:04:03.949 --> 00:04:03.959 align:start position:0%
internet uh on on GitHub on Wikipedia
 

00:04:03.959 --> 00:04:07.789 align:start position:0%
internet uh on on GitHub on Wikipedia
and<00:04:04.120><c> so</c><00:04:04.360><c> on</c><00:04:05.360><c> but</c><00:04:06.040><c> this</c><00:04:06.200><c> may</c><00:04:06.400><c> not</c><00:04:06.560><c> be</c><00:04:06.799><c> enough</c><00:04:07.480><c> we</c>

00:04:07.789 --> 00:04:07.799 align:start position:0%
and so on but this may not be enough we
 

00:04:07.799 --> 00:04:10.830 align:start position:0%
and so on but this may not be enough we
actually<00:04:08.120><c> want</c><00:04:08.400><c> this</c><00:04:08.599><c> model</c><00:04:09.120><c> to</c><00:04:10.120><c> follow</c><00:04:10.560><c> our</c>

00:04:10.830 --> 00:04:10.840 align:start position:0%
actually want this model to follow our
 

00:04:10.840 --> 00:04:12.789 align:start position:0%
actually want this model to follow our
instructions<00:04:11.400><c> to</c><00:04:11.599><c> respond</c><00:04:11.879><c> to</c><00:04:12.040><c> our</c><00:04:12.280><c> questions</c>

00:04:12.789 --> 00:04:12.799 align:start position:0%
instructions to respond to our questions
 

00:04:12.799 --> 00:04:14.830 align:start position:0%
instructions to respond to our questions
and<00:04:12.920><c> this</c><00:04:13.040><c> is</c><00:04:13.159><c> where</c><00:04:13.400><c> the</c><00:04:13.599><c> Second</c><00:04:13.920><c> Step</c><00:04:14.720><c> which</c>

00:04:14.830 --> 00:04:14.840 align:start position:0%
and this is where the Second Step which
 

00:04:14.840 --> 00:04:17.749 align:start position:0%
and this is where the Second Step which
is<00:04:15.040><c> supervised</c><00:04:15.760><c> instruction</c><00:04:16.479><c> tuning</c><00:04:17.479><c> uh</c><00:04:17.600><c> can</c>

00:04:17.749 --> 00:04:17.759 align:start position:0%
is supervised instruction tuning uh can
 

00:04:17.759 --> 00:04:20.949 align:start position:0%
is supervised instruction tuning uh can
be<00:04:18.280><c> helpful</c><00:04:19.280><c> in</c><00:04:19.440><c> this</c><00:04:19.639><c> step</c><00:04:20.000><c> the</c><00:04:20.160><c> model</c><00:04:20.759><c> is</c>

00:04:20.949 --> 00:04:20.959 align:start position:0%
be helpful in this step the model is
 

00:04:20.959 --> 00:04:23.710 align:start position:0%
be helpful in this step the model is
further<00:04:21.440><c> trained</c><00:04:22.240><c> with</c><00:04:22.560><c> expert</c><00:04:22.960><c> generated</c>

00:04:23.710 --> 00:04:23.720 align:start position:0%
further trained with expert generated
 

00:04:23.720 --> 00:04:26.710 align:start position:0%
further trained with expert generated
question<00:04:24.280><c> answer</c><00:04:24.960><c> Pairs</c><00:04:25.960><c> and</c><00:04:26.120><c> this</c><00:04:26.320><c> helps</c>

00:04:26.710 --> 00:04:26.720 align:start position:0%
question answer Pairs and this helps
 

00:04:26.720 --> 00:04:29.390 align:start position:0%
question answer Pairs and this helps
align<00:04:27.080><c> the</c><00:04:27.240><c> model</c><00:04:27.880><c> with</c><00:04:28.080><c> user</c><00:04:28.479><c> expectations</c>

00:04:29.390 --> 00:04:29.400 align:start position:0%
align the model with user expectations
 

00:04:29.400 --> 00:04:32.469 align:start position:0%
align the model with user expectations
and<00:04:29.720><c> follow</c>

00:04:32.469 --> 00:04:32.479 align:start position:0%
 
 

00:04:32.479 --> 00:04:36.629 align:start position:0%
 
instructions<00:04:33.479><c> some</c><00:04:33.759><c> llms</c><00:04:34.520><c> like</c><00:04:34.960><c> gp4</c><00:04:35.960><c> undergo</c>

00:04:36.629 --> 00:04:36.639 align:start position:0%
instructions some llms like gp4 undergo
 

00:04:36.639 --> 00:04:38.670 align:start position:0%
instructions some llms like gp4 undergo
an<00:04:36.840><c> additional</c><00:04:37.320><c> phase</c><00:04:38.039><c> reinforcement</c>

00:04:38.670 --> 00:04:38.680 align:start position:0%
an additional phase reinforcement
 

00:04:38.680 --> 00:04:41.150 align:start position:0%
an additional phase reinforcement
learning<00:04:39.080><c> from</c><00:04:39.280><c> Human</c><00:04:39.759><c> feedback</c><00:04:40.759><c> here</c><00:04:41.000><c> the</c>

00:04:41.150 --> 00:04:41.160 align:start position:0%
learning from Human feedback here the
 

00:04:41.160 --> 00:04:43.790 align:start position:0%
learning from Human feedback here the
model<00:04:41.520><c> is</c><00:04:41.680><c> trained</c><00:04:42.039><c> to</c><00:04:42.280><c> optimize</c><00:04:43.039><c> for</c><00:04:43.440><c> higher</c>

00:04:43.790 --> 00:04:43.800 align:start position:0%
model is trained to optimize for higher
 

00:04:43.800 --> 00:04:47.629 align:start position:0%
model is trained to optimize for higher
quality<00:04:44.280><c> answers</c><00:04:45.199><c> preferred</c><00:04:45.919><c> by</c><00:04:46.160><c> human</c>

00:04:47.629 --> 00:04:47.639 align:start position:0%
quality answers preferred by human
 

00:04:47.639 --> 00:04:49.390 align:start position:0%
quality answers preferred by human
judges<00:04:48.639><c> understanding</c><00:04:48.919><c> these</c><00:04:49.080><c> training</c>

00:04:49.390 --> 00:04:49.400 align:start position:0%
judges understanding these training
 

00:04:49.400 --> 00:04:51.590 align:start position:0%
judges understanding these training
phases<00:04:49.800><c> can</c><00:04:49.919><c> be</c><00:04:50.039><c> helpful</c><00:04:50.919><c> it</c><00:04:51.039><c> can</c><00:04:51.240><c> give</c><00:04:51.400><c> us</c>

00:04:51.590 --> 00:04:51.600 align:start position:0%
phases can be helpful it can give us
 

00:04:51.600 --> 00:04:54.110 align:start position:0%
phases can be helpful it can give us
intuitions<00:04:52.600><c> for</c><00:04:52.800><c> example</c><00:04:53.240><c> how</c><00:04:53.360><c> to</c><00:04:53.639><c> formulate</c>

00:04:54.110 --> 00:04:54.120 align:start position:0%
intuitions for example how to formulate
 

00:04:54.120 --> 00:04:57.230 align:start position:0%
intuitions for example how to formulate
a<00:04:54.400><c> prompt</c><00:04:55.160><c> in</c><00:04:55.360><c> order</c><00:04:55.759><c> to</c><00:04:56.000><c> get</c><00:04:56.440><c> the</c><00:04:56.639><c> expected</c>

00:04:57.230 --> 00:04:57.240 align:start position:0%
a prompt in order to get the expected
 

00:04:57.240 --> 00:04:59.270 align:start position:0%
a prompt in order to get the expected
answer<00:04:57.759><c> the</c><00:04:57.960><c> expected</c><00:04:58.440><c> output</c><00:04:59.000><c> from</c><00:04:59.199><c> the</c>

00:04:59.270 --> 00:04:59.280 align:start position:0%
answer the expected output from the
 

00:04:59.280 --> 00:05:00.790 align:start position:0%
answer the expected output from the
model

00:05:00.790 --> 00:05:00.800 align:start position:0%
model
 

00:05:00.800 --> 00:05:03.350 align:start position:0%
model
in<00:05:00.919><c> the</c><00:05:01.120><c> next</c><00:05:01.360><c> video</c><00:05:02.039><c> we'll</c><00:05:02.520><c> experiment</c><00:05:03.199><c> with</c>

00:05:03.350 --> 00:05:03.360 align:start position:0%
in the next video we'll experiment with
 

00:05:03.360 --> 00:05:14.310 align:start position:0%
in the next video we'll experiment with
this<00:05:03.600><c> concept</c><00:05:04.400><c> in</c><00:05:04.639><c> Jupiter</c><00:05:05.080><c> notebook</c><00:05:05.600><c> with</c>

00:05:14.310 --> 00:05:14.320 align:start position:0%
 
 

00:05:14.320 --> 00:05:17.320 align:start position:0%
 
code

