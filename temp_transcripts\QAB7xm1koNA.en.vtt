WEBVTT
Kind: captions
Language: en

00:00:00.030 --> 00:00:01.840 align:start position:0%
 
and<00:00:00.120><c> welcome</c><00:00:00.480><c> back</c><00:00:00.510><c> in</c><00:00:00.719><c> a</c><00:00:00.989><c> previous</c><00:00:01.410><c> video</c>

00:00:01.840 --> 00:00:01.850 align:start position:0%
and welcome back in a previous video
 

00:00:01.850 --> 00:00:03.830 align:start position:0%
and welcome back in a previous video
series<00:00:02.850><c> we</c><00:00:03.120><c> actually</c><00:00:03.540><c> talked</c><00:00:03.750><c> about</c>

00:00:03.830 --> 00:00:03.840 align:start position:0%
series we actually talked about
 

00:00:03.840 --> 00:00:05.749 align:start position:0%
series we actually talked about
something<00:00:04.259><c> called</c><00:00:04.410><c> meta</c><00:00:04.710><c> base</c><00:00:04.980><c> meta</c><00:00:05.310><c> bases</c><00:00:05.640><c> an</c>

00:00:05.749 --> 00:00:05.759 align:start position:0%
something called meta base meta bases an
 

00:00:05.759 --> 00:00:08.179 align:start position:0%
something called meta base meta bases an
open-sourced<00:00:06.420><c> a</c><00:00:06.600><c> data</c><00:00:07.049><c> visualization</c><00:00:07.290><c> tool</c>

00:00:08.179 --> 00:00:08.189 align:start position:0%
open-sourced a data visualization tool
 

00:00:08.189 --> 00:00:10.040 align:start position:0%
open-sourced a data visualization tool
which<00:00:08.370><c> is</c><00:00:08.519><c> automatically</c><00:00:09.240><c> always</c><00:00:09.540><c> connected</c>

00:00:10.040 --> 00:00:10.050 align:start position:0%
which is automatically always connected
 

00:00:10.050 --> 00:00:12.650 align:start position:0%
which is automatically always connected
to<00:00:10.080><c> your</c><00:00:10.260><c> database</c><00:00:10.559><c> so</c><00:00:10.920><c> for</c><00:00:11.759><c> this</c><00:00:11.880><c> example</c><00:00:12.330><c> I</c>

00:00:12.650 --> 00:00:12.660 align:start position:0%
to your database so for this example I
 

00:00:12.660 --> 00:00:15.890 align:start position:0%
to your database so for this example I
actually<00:00:12.809><c> want</c><00:00:13.230><c> to</c><00:00:13.320><c> go</c><00:00:13.469><c> ahead</c><00:00:13.620><c> and</c><00:00:14.099><c> an</c><00:00:14.969><c> open</c><00:00:15.809><c> up</c>

00:00:15.890 --> 00:00:15.900 align:start position:0%
actually want to go ahead and an open up
 

00:00:15.900 --> 00:00:17.930 align:start position:0%
actually want to go ahead and an open up
meta<00:00:16.199><c> base</c><00:00:16.470><c> here</c><00:00:16.949><c> so</c><00:00:17.310><c> that</c><00:00:17.460><c> means</c><00:00:17.640><c> that</c><00:00:17.820><c> I'm</c>

00:00:17.930 --> 00:00:17.940 align:start position:0%
meta base here so that means that I'm
 

00:00:17.940 --> 00:00:20.450 align:start position:0%
meta base here so that means that I'm
gonna<00:00:18.119><c> go</c><00:00:18.359><c> to</c><00:00:18.650><c> meta</c><00:00:19.650><c> base</c><00:00:19.859><c> and</c><00:00:20.220><c> I</c><00:00:20.279><c> have</c>

00:00:20.450 --> 00:00:20.460 align:start position:0%
gonna go to meta base and I have
 

00:00:20.460 --> 00:00:23.450 align:start position:0%
gonna go to meta base and I have
databases<00:00:21.060><c> then</c><00:00:21.960><c> now</c><00:00:22.439><c> I'm</c><00:00:22.920><c> gonna</c><00:00:23.160><c> share</c><00:00:23.430><c> my</c>

00:00:23.450 --> 00:00:23.460 align:start position:0%
databases then now I'm gonna share my
 

00:00:23.460 --> 00:00:25.550 align:start position:0%
databases then now I'm gonna share my
credentials<00:00:24.090><c> because</c><00:00:24.660><c> I'll</c><00:00:24.960><c> probably</c><00:00:25.260><c> end</c><00:00:25.470><c> up</c>

00:00:25.550 --> 00:00:25.560 align:start position:0%
credentials because I'll probably end up
 

00:00:25.560 --> 00:00:27.859 align:start position:0%
credentials because I'll probably end up
getting<00:00:25.680><c> a</c><00:00:25.890><c> new</c><00:00:26.010><c> database</c><00:00:26.310><c> anyways</c><00:00:26.849><c> but</c><00:00:27.090><c> this</c>

00:00:27.859 --> 00:00:27.869 align:start position:0%
getting a new database anyways but this
 

00:00:27.869 --> 00:00:29.540 align:start position:0%
getting a new database anyways but this
is<00:00:27.990><c> what</c><00:00:28.170><c> this</c><00:00:28.320><c> string</c><00:00:28.710><c> this</c><00:00:29.220><c> is</c><00:00:29.279><c> what</c><00:00:29.460><c> it</c>

00:00:29.540 --> 00:00:29.550 align:start position:0%
is what this string this is what it
 

00:00:29.550 --> 00:00:33.620 align:start position:0%
is what this string this is what it
looks<00:00:29.730><c> like</c><00:00:29.939><c> in</c><00:00:30.240><c> my</c><00:00:30.710><c> jaws</c><00:00:31.710><c> DB</c><00:00:32.279><c> user</c><00:00:32.969><c> interface</c>

00:00:33.620 --> 00:00:33.630 align:start position:0%
looks like in my jaws DB user interface
 

00:00:33.630 --> 00:00:36.229 align:start position:0%
looks like in my jaws DB user interface
it's<00:00:34.020><c> it's</c><00:00:34.710><c> kind</c><00:00:34.950><c> of</c><00:00:35.010><c> nice</c><00:00:35.130><c> it</c><00:00:35.489><c> tells</c><00:00:36.059><c> me</c><00:00:36.210><c> my</c>

00:00:36.229 --> 00:00:36.239 align:start position:0%
it's it's kind of nice it tells me my
 

00:00:36.239 --> 00:00:38.209 align:start position:0%
it's it's kind of nice it tells me my
host<00:00:36.630><c> and</c><00:00:36.960><c> that's</c><00:00:37.170><c> exactly</c><00:00:37.620><c> what</c><00:00:37.739><c> I</c><00:00:37.770><c> need</c><00:00:38.010><c> in</c>

00:00:38.209 --> 00:00:38.219 align:start position:0%
host and that's exactly what I need in
 

00:00:38.219 --> 00:00:43.520 align:start position:0%
host and that's exactly what I need in
order<00:00:38.370><c> to</c><00:00:38.670><c> add</c><00:00:38.879><c> a</c><00:00:39.590><c> roku</c><00:00:40.590><c> databse</c><00:00:40.980><c> Aria</c><00:00:42.530><c> meta</c>

00:00:43.520 --> 00:00:43.530 align:start position:0%
order to add a roku databse Aria meta
 

00:00:43.530 --> 00:00:46.910 align:start position:0%
order to add a roku databse Aria meta
based<00:00:43.770><c> database</c><00:00:44.160><c> and</c><00:00:44.820><c> that</c><00:00:45.000><c> is</c><00:00:45.590><c> it'll</c><00:00:46.590><c> be</c><00:00:46.649><c> my</c>

00:00:46.910 --> 00:00:46.920 align:start position:0%
based database and that is it'll be my
 

00:00:46.920 --> 00:00:51.130 align:start position:0%
based database and that is it'll be my
sequel<00:00:47.840><c> the</c><00:00:48.840><c> name</c><00:00:49.079><c> of</c><00:00:49.260><c> it</c><00:00:49.440><c> is</c><00:00:49.680><c> I</c><00:00:50.190><c> don't</c><00:00:50.370><c> know</c>

00:00:51.130 --> 00:00:51.140 align:start position:0%
sequel the name of it is I don't know
 

00:00:51.140 --> 00:00:55.479 align:start position:0%
sequel the name of it is I don't know
what's<00:00:52.140><c> the</c><00:00:52.289><c> IP</c><00:00:52.440><c> the</c><00:00:52.800><c> host</c><00:00:53.280><c> this</c><00:00:53.820><c> is</c><00:00:53.879><c> the</c><00:00:54.090><c> host</c>

00:00:55.479 --> 00:00:55.489 align:start position:0%
what's the IP the host this is the host
 

00:00:55.489 --> 00:00:58.340 align:start position:0%
what's the IP the host this is the host
mm-hmm<00:00:56.550><c> the</c><00:00:56.850><c> name</c><00:00:57.059><c> of</c><00:00:57.210><c> it</c><00:00:57.300><c> is</c><00:00:57.420><c> over</c><00:00:57.719><c> here</c><00:00:58.140><c> I</c>

00:00:58.340 --> 00:00:58.350 align:start position:0%
mm-hmm the name of it is over here I
 

00:00:58.350 --> 00:01:00.709 align:start position:0%
mm-hmm the name of it is over here I
only<00:00:58.680><c> got</c><00:00:58.890><c> it</c><00:00:59.160><c> once</c><00:00:59.460><c> I</c><00:00:59.609><c> connected</c><00:01:00.180><c> to</c><00:01:00.359><c> it</c><00:01:00.510><c> for</c>

00:01:00.709 --> 00:01:00.719 align:start position:0%
only got it once I connected to it for
 

00:01:00.719 --> 00:01:03.260 align:start position:0%
only got it once I connected to it for
some<00:01:00.899><c> reason</c><00:01:01.260><c> they</c><00:01:02.250><c> don't</c><00:01:02.370><c> have</c><00:01:02.730><c> it</c><00:01:02.910><c> anywhere</c>

00:01:03.260 --> 00:01:03.270 align:start position:0%
some reason they don't have it anywhere
 

00:01:03.270 --> 00:01:05.090 align:start position:0%
some reason they don't have it anywhere
else<00:01:03.420><c> which</c><00:01:03.690><c> actually</c><00:01:04.379><c> it</c><00:01:04.470><c> would</c><00:01:04.559><c> be</c><00:01:04.589><c> helpful</c>

00:01:05.090 --> 00:01:05.100 align:start position:0%
else which actually it would be helpful
 

00:01:05.100 --> 00:01:09.969 align:start position:0%
else which actually it would be helpful
it's<00:01:05.220><c> VK</c><00:01:05.729><c> 22</c><00:01:07.310><c> see</c><00:01:08.310><c> there</c><00:01:08.520><c> it</c><00:01:08.610><c> is</c><00:01:08.700><c> UK</c><00:01:09.030><c> 22</c><00:01:09.479><c> awesome</c>

00:01:09.969 --> 00:01:09.979 align:start position:0%
it's VK 22 see there it is UK 22 awesome
 

00:01:09.979 --> 00:01:14.960 align:start position:0%
it's VK 22 see there it is UK 22 awesome
3306<00:01:10.979><c> by</c><00:01:11.220><c> default</c><00:01:12.590><c> database</c><00:01:13.590><c> name</c><00:01:13.979><c> oh</c><00:01:14.250><c> crap</c>

00:01:14.960 --> 00:01:14.970 align:start position:0%
3306 by default database name oh crap
 

00:01:14.970 --> 00:01:18.440 align:start position:0%
3306 by default database name oh crap
wait<00:01:15.600><c> no</c><00:01:15.869><c> I</c><00:01:15.900><c> need</c><00:01:16.080><c> write</c><00:01:16.409><c> my</c><00:01:16.710><c> name</c><00:01:17.450><c> database</c>

00:01:18.440 --> 00:01:18.450 align:start position:0%
wait no I need write my name database
 

00:01:18.450 --> 00:01:23.929 align:start position:0%
wait no I need write my name database
name<00:01:19.159><c> this</c><00:01:20.159><c> is</c><00:01:20.310><c> gonna</c><00:01:20.430><c> be</c><00:01:20.640><c> called</c><00:01:20.990><c> koala</c><00:01:22.939><c> koala</c>

00:01:23.929 --> 00:01:23.939 align:start position:0%
name this is gonna be called koala koala
 

00:01:23.939 --> 00:01:29.899 align:start position:0%
name this is gonna be called koala koala
CMS<00:01:24.630><c> okay</c><00:01:25.700><c> lcms</c><00:01:26.700><c> nice</c><00:01:26.939><c> the</c><00:01:27.900><c> root</c><00:01:28.080><c> is</c><00:01:28.350><c> not</c><00:01:28.909><c> the</c>

00:01:29.899 --> 00:01:29.909 align:start position:0%
CMS okay lcms nice the root is not the
 

00:01:29.909 --> 00:01:32.270 align:start position:0%
CMS okay lcms nice the root is not the
username<00:01:30.299><c> is</c><00:01:30.420><c> not</c><00:01:30.450><c> rude</c><00:01:30.960><c> it's</c><00:01:31.259><c> actually</c><00:01:31.920><c> given</c>

00:01:32.270 --> 00:01:32.280 align:start position:0%
username is not rude it's actually given
 

00:01:32.280 --> 00:01:34.999 align:start position:0%
username is not rude it's actually given
to<00:01:32.430><c> us</c><00:01:32.659><c> again</c><00:01:33.659><c> over</c><00:01:33.960><c> here</c><00:01:34.200><c> at</c><00:01:34.290><c> the</c><00:01:34.409><c> jaws</c><00:01:34.619><c> DB</c>

00:01:34.999 --> 00:01:35.009 align:start position:0%
to us again over here at the jaws DB
 

00:01:35.009 --> 00:01:41.060 align:start position:0%
to us again over here at the jaws DB
user<00:01:35.250><c> interface</c><00:01:35.820><c> user</c><00:01:36.210><c> name</c><00:01:37.220><c> you'll</c><00:01:38.220><c> see</c><00:01:40.070><c> user</c>

00:01:41.060 --> 00:01:41.070 align:start position:0%
user interface user name you'll see user
 

00:01:41.070 --> 00:01:44.690 align:start position:0%
user interface user name you'll see user
name<00:01:41.460><c> and</c><00:01:41.939><c> finally</c><00:01:42.329><c> the</c><00:01:42.659><c> password</c><00:01:43.490><c> here's</c><00:01:44.490><c> our</c>

00:01:44.690 --> 00:01:44.700 align:start position:0%
name and finally the password here's our
 

00:01:44.700 --> 00:01:46.490 align:start position:0%
name and finally the password here's our
password<00:01:45.149><c> and</c><00:01:45.509><c> we</c><00:01:45.990><c> were</c><00:01:46.079><c> right</c><00:01:46.229><c> about</c><00:01:46.350><c> the</c>

00:01:46.490 --> 00:01:46.500 align:start position:0%
password and we were right about the
 

00:01:46.500 --> 00:01:53.300 align:start position:0%
password and we were right about the
court<00:01:51.170><c> additional</c><00:01:52.170><c> options</c><00:01:52.680><c> no</c><00:01:52.920><c> that's</c>

00:01:53.300 --> 00:01:53.310 align:start position:0%
court additional options no that's
 

00:01:53.310 --> 00:01:59.749 align:start position:0%
court additional options no that's
pretty<00:01:53.549><c> much</c><00:01:53.700><c> it</c><00:01:53.939><c> and</c><00:01:54.149><c> save</c><00:01:58.369><c> damn</c><00:01:59.369><c> it's</c><00:01:59.520><c> nice</c>

00:01:59.749 --> 00:01:59.759 align:start position:0%
pretty much it and save damn it's nice
 

00:01:59.759 --> 00:02:01.789 align:start position:0%
pretty much it and save damn it's nice
okay<00:02:00.149><c> I</c><00:02:00.360><c> want</c><00:02:00.570><c> to</c><00:02:00.630><c> save</c><00:02:00.869><c> it</c><00:02:00.899><c> your</c><00:02:01.290><c> database</c><00:02:01.649><c> has</c>

00:02:01.789 --> 00:02:01.799 align:start position:0%
okay I want to save it your database has
 

00:02:01.799 --> 00:02:04.429 align:start position:0%
okay I want to save it your database has
been<00:02:01.829><c> added</c><00:02:02.280><c> that's</c><00:02:02.579><c> awesome</c><00:02:03.000><c> and</c><00:02:03.689><c> I'm</c><00:02:04.290><c> gonna</c>

00:02:04.429 --> 00:02:04.439 align:start position:0%
been added that's awesome and I'm gonna
 

00:02:04.439 --> 00:02:07.999 align:start position:0%
been added that's awesome and I'm gonna
go<00:02:04.619><c> back</c><00:02:04.829><c> into</c><00:02:05.070><c> my</c><00:02:05.340><c> base</c><00:02:05.579><c> now</c><00:02:06.590><c> she'll</c><00:02:07.590><c> want</c><00:02:07.890><c> to</c>

00:02:07.999 --> 00:02:08.009 align:start position:0%
go back into my base now she'll want to
 

00:02:08.009 --> 00:02:10.240 align:start position:0%
go back into my base now she'll want to
something

00:02:10.240 --> 00:02:10.250 align:start position:0%
something
 

00:02:10.250 --> 00:02:13.220 align:start position:0%
something
the<00:02:11.250><c> normal</c><00:02:11.670><c> app</c><00:02:11.820><c> right</c><00:02:12.690><c> right</c><00:02:12.930><c> now</c><00:02:13.050><c> I'm</c><00:02:13.110><c> in</c>

00:02:13.220 --> 00:02:13.230 align:start position:0%
the normal app right right now I'm in
 

00:02:13.230 --> 00:02:14.960 align:start position:0%
the normal app right right now I'm in
the<00:02:13.350><c> admin</c><00:02:13.680><c> panel</c><00:02:13.920><c> I</c><00:02:14.190><c> want</c><00:02:14.310><c> to</c><00:02:14.430><c> go</c><00:02:14.520><c> back</c><00:02:14.730><c> to</c><00:02:14.850><c> the</c>

00:02:14.960 --> 00:02:14.970 align:start position:0%
the admin panel I want to go back to the
 

00:02:14.970 --> 00:02:18.170 align:start position:0%
the admin panel I want to go back to the
normal<00:02:15.330><c> user</c><00:02:15.540><c> interface</c><00:02:16.250><c> and</c><00:02:17.250><c> if</c><00:02:18.030><c> you</c>

00:02:18.170 --> 00:02:18.180 align:start position:0%
normal user interface and if you
 

00:02:18.180 --> 00:02:21.920 align:start position:0%
normal user interface and if you
remember<00:02:18.450><c> we</c><00:02:18.720><c> created</c><00:02:19.230><c> a</c><00:02:19.440><c> user</c><00:02:19.830><c> over</c><00:02:20.490><c> here</c><00:02:20.930><c> our</c>

00:02:21.920 --> 00:02:21.930 align:start position:0%
remember we created a user over here our
 

00:02:21.930 --> 00:02:24.230 align:start position:0%
remember we created a user over here our
first<00:02:22.260><c> user</c><00:02:22.590><c> ah</c><00:02:22.890><c> let's</c><00:02:23.370><c> create</c><00:02:23.610><c> a</c><00:02:23.730><c> user</c><00:02:23.910><c> for</c>

00:02:24.230 --> 00:02:24.240 align:start position:0%
first user ah let's create a user for
 

00:02:24.240 --> 00:02:25.730 align:start position:0%
first user ah let's create a user for
the<00:02:24.300><c> sake</c><00:02:24.510><c> of</c><00:02:24.540><c> it</c><00:02:24.750><c> alright</c><00:02:25.110><c> now</c><00:02:25.290><c> we</c><00:02:25.350><c> already</c>

00:02:25.730 --> 00:02:25.740 align:start position:0%
the sake of it alright now we already
 

00:02:25.740 --> 00:02:28.250 align:start position:0%
the sake of it alright now we already
have<00:02:25.890><c> one</c><00:02:26.420><c> he's</c><00:02:27.420><c> logged</c><00:02:27.660><c> in</c><00:02:27.840><c> here</c><00:02:28.050><c> that's</c>

00:02:28.250 --> 00:02:28.260 align:start position:0%
have one he's logged in here that's
 

00:02:28.260 --> 00:02:30.710 align:start position:0%
have one he's logged in here that's
Cramer<00:02:28.500><c> 1346</c><00:02:29.340><c> gmail.com</c><00:02:29.760><c> want</c><00:02:30.480><c> to</c><00:02:30.540><c> create</c>

00:02:30.710 --> 00:02:30.720 align:start position:0%
Cramer 1346 gmail.com want to create
 

00:02:30.720 --> 00:02:32.950 align:start position:0%
Cramer 1346 gmail.com want to create
another<00:02:31.050><c> one</c><00:02:31.260><c> so</c><00:02:32.100><c> one</c><00:02:32.190><c> is</c><00:02:32.280><c> gonna</c><00:02:32.430><c> be</c><00:02:32.610><c> called</c>

00:02:32.950 --> 00:02:32.960 align:start position:0%
another one so one is gonna be called
 

00:02:32.960 --> 00:02:37.280 align:start position:0%
another one so one is gonna be called
donations<00:02:33.960><c> at</c><00:02:34.200><c> hi</c><00:02:34.470><c> Israel</c><00:02:34.920><c> or</c><00:02:35.900><c> I'm</c><00:02:36.900><c> gonna</c><00:02:36.990><c> put</c>

00:02:37.280 --> 00:02:37.290 align:start position:0%
donations at hi Israel or I'm gonna put
 

00:02:37.290 --> 00:02:39.170 align:start position:0%
donations at hi Israel or I'm gonna put
in<00:02:37.410><c> a</c><00:02:37.500><c> password</c><00:02:37.710><c> we're</c><00:02:38.250><c> gonna</c><00:02:38.340><c> press</c><00:02:38.640><c> login</c>

00:02:39.170 --> 00:02:39.180 align:start position:0%
in a password we're gonna press login
 

00:02:39.180 --> 00:02:45.140 align:start position:0%
in a password we're gonna press login
well<00:02:39.420><c> now</c><00:02:39.750><c> we</c><00:02:39.810><c> can't</c><00:02:40.170><c> login</c><00:02:41.900><c> we</c><00:02:42.900><c> cannot</c><00:02:44.150><c> crap</c>

00:02:45.140 --> 00:02:45.150 align:start position:0%
well now we can't login we cannot crap
 

00:02:45.150 --> 00:02:46.550 align:start position:0%
well now we can't login we cannot crap
the<00:02:45.300><c> username</c><00:02:45.630><c> does</c><00:02:45.780><c> not</c><00:02:45.960><c> exist</c><00:02:46.290><c> in</c><00:02:46.440><c> our</c>

00:02:46.550 --> 00:02:46.560 align:start position:0%
the username does not exist in our
 

00:02:46.560 --> 00:02:48.590 align:start position:0%
the username does not exist in our
database<00:02:46.950><c> muchacha</c><00:02:47.460><c> okay</c><00:02:47.850><c> as</c><00:02:48.060><c> you</c><00:02:48.270><c> can</c><00:02:48.300><c> see</c>

00:02:48.590 --> 00:02:48.600 align:start position:0%
database muchacha okay as you can see
 

00:02:48.600 --> 00:02:51.500 align:start position:0%
database muchacha okay as you can see
the<00:02:48.810><c> error</c><00:02:49.590><c> handling</c><00:02:50.190><c> is</c><00:02:50.490><c> has</c><00:02:50.910><c> a</c><00:02:50.940><c> personality</c>

00:02:51.500 --> 00:02:51.510 align:start position:0%
the error handling is has a personality
 

00:02:51.510 --> 00:02:55.370 align:start position:0%
the error handling is has a personality
so<00:02:52.520><c> that's</c><00:02:53.520><c> a</c><00:02:53.610><c> good</c><00:02:53.760><c> thing</c><00:02:53.970><c> it's</c><00:02:54.900><c> absolutely</c>

00:02:55.370 --> 00:02:55.380 align:start position:0%
so that's a good thing it's absolutely
 

00:02:55.380 --> 00:02:57.260 align:start position:0%
so that's a good thing it's absolutely
correct<00:02:55.530><c> username</c><00:02:56.190><c> to</c><00:02:56.430><c> non-existent</c><00:02:56.700><c> can</c>

00:02:57.260 --> 00:02:57.270 align:start position:0%
correct username to non-existent can
 

00:02:57.270 --> 00:03:01.910 align:start position:0%
correct username to non-existent can
create<00:02:57.570><c> it</c><00:02:57.690><c> now</c><00:02:58.490><c> donations</c><00:03:00.410><c> as</c><00:03:01.410><c> high</c><00:03:01.620><c> as</c><00:03:01.680><c> your</c>

00:03:01.910 --> 00:03:01.920 align:start position:0%
create it now donations as high as your
 

00:03:01.920 --> 00:03:05.840 align:start position:0%
create it now donations as high as your
thyroid<00:03:02.400><c> mm-hmm</c><00:03:03.710><c> we</c><00:03:04.710><c> have</c><00:03:04.890><c> cookies</c><00:03:05.370><c> enabled</c>

00:03:05.840 --> 00:03:05.850 align:start position:0%
thyroid mm-hmm we have cookies enabled
 

00:03:05.850 --> 00:03:07.340 align:start position:0%
thyroid mm-hmm we have cookies enabled
in<00:03:05.970><c> the</c><00:03:06.060><c> project</c><00:03:06.540><c> and</c><00:03:06.690><c> that's</c><00:03:06.750><c> really</c><00:03:07.050><c> nice</c>

00:03:07.340 --> 00:03:07.350 align:start position:0%
in the project and that's really nice
 

00:03:07.350 --> 00:03:09.800 align:start position:0%
in the project and that's really nice
it's<00:03:07.650><c> kind</c><00:03:07.830><c> of</c><00:03:07.860><c> registered</c><00:03:08.460><c> and</c><00:03:09.150><c> we're</c>

00:03:09.800 --> 00:03:09.810 align:start position:0%
it's kind of registered and we're
 

00:03:09.810 --> 00:03:12.830 align:start position:0%
it's kind of registered and we're
already<00:03:09.930><c> gonna</c><00:03:10.350><c> log</c><00:03:10.590><c> in</c><00:03:11.420><c> now</c><00:03:12.420><c> that</c><00:03:12.570><c> we</c><00:03:12.660><c> have</c>

00:03:12.830 --> 00:03:12.840 align:start position:0%
already gonna log in now that we have
 

00:03:12.840 --> 00:03:14.510 align:start position:0%
already gonna log in now that we have
that<00:03:13.020><c> we</c><00:03:13.290><c> want</c><00:03:13.380><c> to</c><00:03:13.530><c> go</c><00:03:13.620><c> back</c><00:03:13.770><c> into</c><00:03:14.010><c> men</c><00:03:14.250><c> debase</c>

00:03:14.510 --> 00:03:14.520 align:start position:0%
that we want to go back into men debase
 

00:03:14.520 --> 00:03:24.770 align:start position:0%
that we want to go back into men debase
here<00:03:17.270><c> close</c><00:03:22.760><c> yes'm</c><00:03:23.790><c> I</c><00:03:23.970><c> save</c><00:03:24.210><c> the</c><00:03:24.330><c> password</c>

00:03:24.770 --> 00:03:24.780 align:start position:0%
here close yes'm I save the password
 

00:03:24.780 --> 00:03:28.910 align:start position:0%
here close yes'm I save the password
here<00:03:26.660><c> and</c><00:03:27.660><c> we're</c><00:03:27.960><c> gonna</c><00:03:28.050><c> go</c><00:03:28.200><c> to</c><00:03:28.260><c> new</c><00:03:28.470><c> question</c>

00:03:28.910 --> 00:03:28.920 align:start position:0%
here and we're gonna go to new question
 

00:03:28.920 --> 00:03:34.400 align:start position:0%
here and we're gonna go to new question
here<00:03:29.780><c> custom</c><00:03:31.760><c> I'm</c><00:03:32.760><c> gonna</c><00:03:32.910><c> go</c><00:03:33.090><c> to</c><00:03:33.150><c> kuala</c><00:03:33.870><c> CMS</c>

00:03:34.400 --> 00:03:34.410 align:start position:0%
here custom I'm gonna go to kuala CMS
 

00:03:34.410 --> 00:03:35.990 align:start position:0%
here custom I'm gonna go to kuala CMS
the<00:03:34.560><c> database</c><00:03:34.920><c> that</c><00:03:35.070><c> we</c><00:03:35.190><c> just</c><00:03:35.370><c> added</c><00:03:35.610><c> and</c>

00:03:35.990 --> 00:03:36.000 align:start position:0%
the database that we just added and
 

00:03:36.000 --> 00:03:37.790 align:start position:0%
the database that we just added and
we're<00:03:36.120><c> gonna</c><00:03:36.300><c> write</c><00:03:36.540><c> a</c><00:03:36.570><c> query</c><00:03:36.930><c> for</c><00:03:37.320><c> swish</c><00:03:37.650><c> the</c>

00:03:37.790 --> 00:03:37.800 align:start position:0%
we're gonna write a query for swish the
 

00:03:37.800 --> 00:03:49.570 align:start position:0%
we're gonna write a query for swish the
sequel<00:03:38.190><c> I</c><00:03:38.820><c> select</c><00:03:41.240><c> ctrl</c><00:03:42.240><c> +</c><00:03:43.490><c> select</c><00:03:48.320><c> run</c><00:03:49.320><c> oh</c>

00:03:49.570 --> 00:03:49.580 align:start position:0%
sequel I select ctrl + select run oh
 

00:03:49.580 --> 00:03:54.680 align:start position:0%
sequel I select ctrl + select run oh
right<00:03:51.260><c> get</c><00:03:52.260><c> the</c><00:03:52.470><c> ends</c><00:03:53.120><c> I'm</c><00:03:54.120><c> gonna</c><00:03:54.270><c> have</c><00:03:54.420><c> two</c>

00:03:54.680 --> 00:03:54.690 align:start position:0%
right get the ends I'm gonna have two
 

00:03:54.690 --> 00:03:58.040 align:start position:0%
right get the ends I'm gonna have two
clients<00:03:54.990><c> in</c><00:03:55.320><c> this</c><00:03:55.470><c> database</c><00:03:55.770><c> Oh</c><00:03:56.510><c> three</c><00:03:57.510><c> kramer</c>

00:03:58.040 --> 00:03:58.050 align:start position:0%
clients in this database Oh three kramer
 

00:03:58.050 --> 00:04:01.100 align:start position:0%
clients in this database Oh three kramer
16<00:03:58.590><c> kramer</c><00:03:59.010><c> 1346</c><00:03:59.880><c> and</c><00:04:00.060><c> donations</c><00:04:00.630><c> at</c><00:04:00.780><c> high</c><00:04:00.930><c> is</c>

00:04:01.100 --> 00:04:01.110 align:start position:0%
16 kramer 1346 and donations at high is
 

00:04:01.110 --> 00:04:03.440 align:start position:0%
16 kramer 1346 and donations at high is
really<00:04:01.470><c> I</c><00:04:01.980><c> don't</c><00:04:02.580><c> even</c><00:04:02.760><c> remember</c><00:04:03.000><c> creating</c>

00:04:03.440 --> 00:04:03.450 align:start position:0%
really I don't even remember creating
 

00:04:03.450 --> 00:04:05.570 align:start position:0%
really I don't even remember creating
this<00:04:03.810><c> uh</c><00:04:03.990><c> yeah</c><00:04:04.620><c> cuz</c><00:04:04.830><c> that's</c><00:04:04.980><c> in</c><00:04:05.100><c> the</c><00:04:05.220><c> sequel</c>

00:04:05.570 --> 00:04:05.580 align:start position:0%
this uh yeah cuz that's in the sequel
 

00:04:05.580 --> 00:04:08.180 align:start position:0%
this uh yeah cuz that's in the sequel
query<00:04:05.790><c> itself</c><00:04:06.209><c> will</c><00:04:06.420><c> create</c><00:04:06.540><c> a</c><00:04:06.720><c> user</c><00:04:07.040><c> just</c><00:04:08.040><c> a</c>

00:04:08.180 --> 00:04:08.190 align:start position:0%
query itself will create a user just a
 

00:04:08.190 --> 00:04:10.120 align:start position:0%
query itself will create a user just a
test<00:04:08.430><c> it</c><00:04:08.660><c> so</c><00:04:09.660><c> that's</c><00:04:09.840><c> great</c>

00:04:10.120 --> 00:04:10.130 align:start position:0%
test it so that's great
 

00:04:10.130 --> 00:04:15.070 align:start position:0%
test it so that's great
and<00:04:11.130><c> this</c><00:04:11.850><c> is</c><00:04:12.180><c> we</c><00:04:13.110><c> saved</c><00:04:13.380><c> all</c>

00:04:15.070 --> 00:04:15.080 align:start position:0%
and this is we saved all
 

00:04:15.080 --> 00:04:20.479 align:start position:0%
and this is we saved all
clients<00:04:17.720><c> so</c><00:04:18.720><c> what</c><00:04:19.470><c> we've</c><00:04:19.620><c> done</c><00:04:19.769><c> so</c><00:04:20.010><c> far</c><00:04:20.040><c> is</c>

00:04:20.479 --> 00:04:20.489 align:start position:0%
clients so what we've done so far is
 

00:04:20.489 --> 00:04:23.960 align:start position:0%
clients so what we've done so far is
we've<00:04:20.810><c> enabled</c><00:04:21.810><c> we</c><00:04:22.290><c> pushed</c><00:04:22.620><c> the</c><00:04:22.920><c> project</c><00:04:23.850><c> to</c>

00:04:23.960 --> 00:04:23.970 align:start position:0%
we've enabled we pushed the project to
 

00:04:23.970 --> 00:04:28.010 align:start position:0%
we've enabled we pushed the project to
Heroku<00:04:26.150><c> we're</c><00:04:27.150><c> not</c><00:04:27.270><c> gonna</c><00:04:27.390><c> add</c><00:04:27.600><c> a</c><00:04:27.630><c> tab</c>

00:04:28.010 --> 00:04:28.020 align:start position:0%
Heroku we're not gonna add a tab
 

00:04:28.020 --> 00:04:29.870 align:start position:0%
Heroku we're not gonna add a tab
dashboard<00:04:28.530><c> now</c><00:04:28.680><c> that's</c><00:04:29.040><c> another</c><00:04:29.310><c> series</c><00:04:29.730><c> in</c>

00:04:29.870 --> 00:04:29.880 align:start position:0%
dashboard now that's another series in
 

00:04:29.880 --> 00:04:31.100 align:start position:0%
dashboard now that's another series in
charge<00:04:30.060><c> of</c><00:04:30.150><c> all</c><00:04:30.240><c> the</c><00:04:30.390><c> dashboards</c><00:04:30.930><c> there's</c>

00:04:31.100 --> 00:04:31.110 align:start position:0%
charge of all the dashboards there's
 

00:04:31.110 --> 00:04:32.930 align:start position:0%
charge of all the dashboards there's
another<00:04:31.320><c> series</c><00:04:31.710><c> that</c><00:04:31.740><c> can</c><00:04:32.010><c> all</c><00:04:32.430><c> about</c><00:04:32.670><c> meta</c>

00:04:32.930 --> 00:04:32.940 align:start position:0%
another series that can all about meta
 

00:04:32.940 --> 00:04:35.330 align:start position:0%
another series that can all about meta
base<00:04:33.180><c> and</c><00:04:33.450><c> that</c><00:04:34.260><c> pretty</c><00:04:34.500><c> much</c><00:04:34.680><c> holds</c><00:04:35.070><c> all</c><00:04:35.190><c> your</c>

00:04:35.330 --> 00:04:35.340 align:start position:0%
base and that pretty much holds all your
 

00:04:35.340 --> 00:04:37.400 align:start position:0%
base and that pretty much holds all your
database<00:04:35.760><c> information</c><00:04:36.090><c> and</c><00:04:36.690><c> lets</c><00:04:37.170><c> you</c><00:04:37.260><c> create</c>

00:04:37.400 --> 00:04:37.410 align:start position:0%
database information and lets you create
 

00:04:37.410 --> 00:04:39.950 align:start position:0%
database information and lets you create
beautiful<00:04:37.890><c> dashboards</c><00:04:38.790><c> and</c><00:04:38.970><c> charts</c><00:04:39.360><c> and</c>

00:04:39.950 --> 00:04:39.960 align:start position:0%
beautiful dashboards and charts and
 

00:04:39.960 --> 00:04:41.750 align:start position:0%
beautiful dashboards and charts and
visualizations<00:04:40.590><c> and</c><00:04:41.010><c> all</c><00:04:41.130><c> that</c><00:04:41.280><c> stuff</c><00:04:41.550><c> and</c>

00:04:41.750 --> 00:04:41.760 align:start position:0%
visualizations and all that stuff and
 

00:04:41.760 --> 00:04:44.290 align:start position:0%
visualizations and all that stuff and
you<00:04:42.540><c> have</c><00:04:42.690><c> a</c><00:04:42.720><c> link</c><00:04:42.990><c> that</c><00:04:43.200><c> in</c><00:04:43.350><c> the</c><00:04:43.410><c> description</c>

00:04:44.290 --> 00:04:44.300 align:start position:0%
you have a link that in the description
 

00:04:44.300 --> 00:04:47.900 align:start position:0%
you have a link that in the description
but<00:04:45.300><c> anyways</c><00:04:45.660><c> that's</c><00:04:46.200><c> pretty</c><00:04:46.380><c> much</c><00:04:46.700><c> how</c><00:04:47.700><c> we've</c>

00:04:47.900 --> 00:04:47.910 align:start position:0%
but anyways that's pretty much how we've
 

00:04:47.910 --> 00:04:51.940 align:start position:0%
but anyways that's pretty much how we've
deployed<00:04:48.360><c> our</c><00:04:49.320><c> app</c><00:04:49.590><c> and</c><00:04:49.920><c> that's</c><00:04:50.550><c> good</c><00:04:50.910><c> and</c>

00:04:51.940 --> 00:04:51.950 align:start position:0%
deployed our app and that's good and
 

00:04:51.950 --> 00:04:54.290 align:start position:0%
deployed our app and that's good and
we'll<00:04:52.950><c> see</c><00:04:53.100><c> what</c><00:04:53.250><c> else</c><00:04:53.280><c> there</c><00:04:53.580><c> is</c><00:04:53.640><c> to</c><00:04:53.880><c> cover</c><00:04:54.120><c> in</c>

00:04:54.290 --> 00:04:54.300 align:start position:0%
we'll see what else there is to cover in
 

00:04:54.300 --> 00:04:55.700 align:start position:0%
we'll see what else there is to cover in
the<00:04:54.360><c> next</c><00:04:54.420><c> video</c><00:04:54.870><c> so</c><00:04:55.050><c> thanks</c><00:04:55.260><c> for</c><00:04:55.350><c> joining</c><00:04:55.590><c> me</c>

00:04:55.700 --> 00:04:55.710 align:start position:0%
the next video so thanks for joining me
 

00:04:55.710 --> 00:04:57.710 align:start position:0%
the next video so thanks for joining me
give<00:04:56.220><c> it</c><00:04:56.310><c> a</c><00:04:56.370><c> like</c><00:04:56.580><c> and</c><00:04:56.850><c> see</c><00:04:57.210><c> you</c><00:04:57.240><c> in</c><00:04:57.360><c> the</c><00:04:57.510><c> next</c>

00:04:57.710 --> 00:04:57.720 align:start position:0%
give it a like and see you in the next
 

00:04:57.720 --> 00:04:59.810 align:start position:0%
give it a like and see you in the next
one

