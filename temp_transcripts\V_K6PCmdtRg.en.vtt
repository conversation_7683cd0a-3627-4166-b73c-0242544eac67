WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:02.030 align:start position:0%
 
okay<00:00:00.320><c> so</c><00:00:00.560><c> recently</c><00:00:01.040><c> I've</c><00:00:01.199><c> been</c><00:00:01.360><c> talking</c><00:00:01.680><c> a</c><00:00:01.839><c> lot</c>

00:00:02.030 --> 00:00:02.040 align:start position:0%
okay so recently I've been talking a lot
 

00:00:02.040 --> 00:00:04.990 align:start position:0%
okay so recently I've been talking a lot
in<00:00:02.280><c> videos</c><00:00:03.080><c> about</c><00:00:03.439><c> the</c><00:00:03.639><c> whole</c><00:00:04.040><c> idea</c><00:00:04.680><c> of</c>

00:00:04.990 --> 00:00:05.000 align:start position:0%
in videos about the whole idea of
 

00:00:05.000 --> 00:00:08.270 align:start position:0%
in videos about the whole idea of
Cheaper<00:00:05.480><c> models</c><00:00:06.480><c> and</c><00:00:06.799><c> faster</c><00:00:07.279><c> models</c><00:00:08.160><c> and</c>

00:00:08.270 --> 00:00:08.280 align:start position:0%
Cheaper models and faster models and
 

00:00:08.280 --> 00:00:10.830 align:start position:0%
Cheaper models and faster models and
being<00:00:08.480><c> able</c><00:00:08.679><c> to</c><00:00:08.880><c> use</c><00:00:09.360><c> them</c><00:00:10.120><c> over</c><00:00:10.480><c> some</c><00:00:10.599><c> of</c><00:00:10.719><c> the</c>

00:00:10.830 --> 00:00:10.840 align:start position:0%
being able to use them over some of the
 

00:00:10.840 --> 00:00:13.270 align:start position:0%
being able to use them over some of the
more<00:00:11.120><c> expensive</c><00:00:11.599><c> models</c><00:00:11.960><c> a</c><00:00:12.040><c> lot</c><00:00:12.200><c> of</c><00:00:12.320><c> the</c><00:00:12.519><c> time</c>

00:00:13.270 --> 00:00:13.280 align:start position:0%
more expensive models a lot of the time
 

00:00:13.280 --> 00:00:14.910 align:start position:0%
more expensive models a lot of the time
and<00:00:13.360><c> the</c><00:00:13.480><c> big</c><00:00:13.639><c> reason</c><00:00:13.880><c> for</c><00:00:14.080><c> doing</c><00:00:14.400><c> that</c><00:00:14.559><c> is</c><00:00:14.719><c> I'm</c>

00:00:14.910 --> 00:00:14.920 align:start position:0%
and the big reason for doing that is I'm
 

00:00:14.920 --> 00:00:17.710 align:start position:0%
and the big reason for doing that is I'm
just<00:00:15.080><c> seeing</c><00:00:15.559><c> so</c><00:00:15.799><c> many</c><00:00:16.160><c> people</c><00:00:16.520><c> waste</c><00:00:17.080><c> so</c><00:00:17.439><c> much</c>

00:00:17.710 --> 00:00:17.720 align:start position:0%
just seeing so many people waste so much
 

00:00:17.720 --> 00:00:21.710 align:start position:0%
just seeing so many people waste so much
money<00:00:18.520><c> with</c><00:00:18.800><c> doing</c><00:00:19.279><c> calls</c><00:00:19.840><c> to</c><00:00:20.119><c> GPT</c><00:00:20.680><c> 4</c><00:00:21.320><c> for</c>

00:00:21.710 --> 00:00:21.720 align:start position:0%
money with doing calls to GPT 4 for
 

00:00:21.720 --> 00:00:24.349 align:start position:0%
money with doing calls to GPT 4 for
everything<00:00:22.359><c> or</c><00:00:22.560><c> calls</c><00:00:22.880><c> to</c><00:00:23.359><c> Claude</c><00:00:23.720><c> Opus</c><00:00:24.160><c> and</c>

00:00:24.349 --> 00:00:24.359 align:start position:0%
everything or calls to Claude Opus and
 

00:00:24.359 --> 00:00:26.429 align:start position:0%
everything or calls to Claude Opus and
stuff<00:00:24.599><c> like</c><00:00:24.760><c> that</c><00:00:25.160><c> for</c><00:00:25.439><c> lots</c><00:00:25.680><c> of</c><00:00:25.920><c> things</c><00:00:26.240><c> where</c>

00:00:26.429 --> 00:00:26.439 align:start position:0%
stuff like that for lots of things where
 

00:00:26.439 --> 00:00:29.189 align:start position:0%
stuff like that for lots of things where
they<00:00:26.640><c> don't</c><00:00:27.039><c> actually</c><00:00:27.480><c> need</c><00:00:28.480><c> that</c><00:00:28.760><c> kind</c><00:00:28.960><c> of</c>

00:00:29.189 --> 00:00:29.199 align:start position:0%
they don't actually need that kind of
 

00:00:29.199 --> 00:00:30.749 align:start position:0%
they don't actually need that kind of
model<00:00:29.599><c> they</c><00:00:29.679><c> would</c><00:00:29.800><c> be</c><00:00:30.119><c> much</c><00:00:30.279><c> better</c><00:00:30.480><c> to</c><00:00:30.640><c> go</c>

00:00:30.749 --> 00:00:30.759 align:start position:0%
model they would be much better to go
 

00:00:30.759 --> 00:00:33.830 align:start position:0%
model they would be much better to go
for<00:00:30.920><c> a</c><00:00:31.119><c> faster</c><00:00:31.519><c> model</c><00:00:32.119><c> and</c><00:00:32.279><c> a</c><00:00:32.439><c> cheaper</c><00:00:32.880><c> model</c>

00:00:33.830 --> 00:00:33.840 align:start position:0%
for a faster model and a cheaper model
 

00:00:33.840 --> 00:00:36.310 align:start position:0%
for a faster model and a cheaper model
the<00:00:34.000><c> one</c><00:00:34.280><c> challenge</c><00:00:34.800><c> with</c><00:00:35.079><c> that</c><00:00:35.719><c> has</c><00:00:35.920><c> always</c>

00:00:36.310 --> 00:00:36.320 align:start position:0%
the one challenge with that has always
 

00:00:36.320 --> 00:00:39.310 align:start position:0%
the one challenge with that has always
been<00:00:36.840><c> well</c><00:00:37.120><c> what</c><00:00:37.239><c> if</c><00:00:37.520><c> some</c><00:00:37.760><c> of</c><00:00:38.079><c> your</c><00:00:38.719><c> calls</c><00:00:39.160><c> to</c>

00:00:39.310 --> 00:00:39.320 align:start position:0%
been well what if some of your calls to
 

00:00:39.320 --> 00:00:42.470 align:start position:0%
been well what if some of your calls to
the<00:00:39.440><c> llm</c><00:00:40.239><c> actually</c><00:00:40.680><c> do</c><00:00:41.000><c> need</c><00:00:41.559><c> the</c><00:00:41.719><c> big</c><00:00:42.039><c> model</c>

00:00:42.470 --> 00:00:42.480 align:start position:0%
the llm actually do need the big model
 

00:00:42.480 --> 00:00:44.990 align:start position:0%
the llm actually do need the big model
or<00:00:42.680><c> do</c><00:00:42.920><c> need</c><00:00:43.160><c> the</c><00:00:43.320><c> more</c><00:00:43.600><c> powerful</c><00:00:44.079><c> model</c><00:00:44.879><c> so</c>

00:00:44.990 --> 00:00:45.000 align:start position:0%
or do need the more powerful model so
 

00:00:45.000 --> 00:00:46.790 align:start position:0%
or do need the more powerful model so
what<00:00:45.120><c> I</c><00:00:45.200><c> want</c><00:00:45.280><c> to</c><00:00:45.399><c> talk</c><00:00:45.559><c> about</c><00:00:45.960><c> in</c><00:00:46.160><c> today's</c>

00:00:46.790 --> 00:00:46.800 align:start position:0%
what I want to talk about in today's
 

00:00:46.800 --> 00:00:49.029 align:start position:0%
what I want to talk about in today's
video<00:00:47.559><c> is</c><00:00:47.760><c> something</c><00:00:48.079><c> that's</c><00:00:48.280><c> been</c><00:00:48.520><c> released</c>

00:00:49.029 --> 00:00:49.039 align:start position:0%
video is something that's been released
 

00:00:49.039 --> 00:00:51.709 align:start position:0%
video is something that's been released
by<00:00:49.239><c> LM</c><00:00:49.840><c> Cy</c><00:00:50.199><c> so</c><00:00:50.360><c> this</c><00:00:50.559><c> is</c><00:00:50.760><c> the</c><00:00:50.920><c> people</c><00:00:51.160><c> who</c><00:00:51.360><c> run</c>

00:00:51.709 --> 00:00:51.719 align:start position:0%
by LM Cy so this is the people who run
 

00:00:51.719 --> 00:00:55.069 align:start position:0%
by LM Cy so this is the people who run
chatbot<00:00:52.359><c> Arena</c><00:00:53.359><c> who</c><00:00:53.960><c> were</c><00:00:54.239><c> some</c><00:00:54.399><c> of</c><00:00:54.559><c> the</c><00:00:54.840><c> first</c>

00:00:55.069 --> 00:00:55.079 align:start position:0%
chatbot Arena who were some of the first
 

00:00:55.079 --> 00:00:57.830 align:start position:0%
chatbot Arena who were some of the first
people<00:00:55.280><c> to</c><00:00:55.480><c> create</c><00:00:55.800><c> models</c><00:00:56.239><c> like</c><00:00:56.719><c> vuna</c><00:00:57.719><c> and</c>

00:00:57.830 --> 00:00:57.840 align:start position:0%
people to create models like vuna and
 

00:00:57.840 --> 00:00:59.590 align:start position:0%
people to create models like vuna and
for<00:00:58.039><c> the</c><00:00:58.239><c> past</c><00:00:58.519><c> year</c><00:00:58.840><c> plus</c><00:00:59.199><c> they've</c><00:00:59.359><c> been</c>

00:00:59.590 --> 00:00:59.600 align:start position:0%
for the past year plus they've been
 

00:00:59.600 --> 00:01:01.869 align:start position:0%
for the past year plus they've been
bench<00:01:00.079><c> marking</c><00:01:00.559><c> all</c><00:01:00.760><c> the</c><00:01:01.079><c> different</c><00:01:01.399><c> models</c>

00:01:01.869 --> 00:01:01.879 align:start position:0%
bench marking all the different models
 

00:01:01.879 --> 00:01:03.709 align:start position:0%
bench marking all the different models
giving<00:01:02.120><c> them</c><00:01:02.239><c> an</c><00:01:02.399><c> ELO</c><00:01:02.800><c> score</c><00:01:03.280><c> that</c><00:01:03.399><c> kind</c><00:01:03.559><c> of</c>

00:01:03.709 --> 00:01:03.719 align:start position:0%
giving them an ELO score that kind of
 

00:01:03.719 --> 00:01:05.350 align:start position:0%
giving them an ELO score that kind of
thing<00:01:04.280><c> so</c><00:01:04.479><c> one</c><00:01:04.559><c> of</c><00:01:04.680><c> the</c><00:01:04.799><c> cool</c><00:01:05.000><c> things</c><00:01:05.199><c> that</c>

00:01:05.350 --> 00:01:05.360 align:start position:0%
thing so one of the cool things that
 

00:01:05.360 --> 00:01:07.789 align:start position:0%
thing so one of the cool things that
they<00:01:05.560><c> just</c><00:01:05.760><c> released</c><00:01:06.320><c> earlier</c><00:01:06.760><c> this</c><00:01:06.960><c> week</c><00:01:07.640><c> was</c>

00:01:07.789 --> 00:01:07.799 align:start position:0%
they just released earlier this week was
 

00:01:07.799 --> 00:01:09.749 align:start position:0%
they just released earlier this week was
this<00:01:08.040><c> new</c><00:01:08.320><c> open</c><00:01:08.640><c> source</c><00:01:08.920><c> framework</c><00:01:09.520><c> called</c>

00:01:09.749 --> 00:01:09.759 align:start position:0%
this new open source framework called
 

00:01:09.759 --> 00:01:12.429 align:start position:0%
this new open source framework called
route<00:01:10.119><c> llm</c><00:01:11.040><c> and</c><00:01:11.159><c> this</c><00:01:11.280><c> is</c><00:01:11.479><c> basically</c><00:01:11.920><c> an</c><00:01:12.119><c> open</c>

00:01:12.429 --> 00:01:12.439 align:start position:0%
route llm and this is basically an open
 

00:01:12.439 --> 00:01:14.870 align:start position:0%
route llm and this is basically an open
source<00:01:12.720><c> framework</c><00:01:13.280><c> for</c><00:01:13.560><c> costeffective</c><00:01:14.360><c> llm</c>

00:01:14.870 --> 00:01:14.880 align:start position:0%
source framework for costeffective llm
 

00:01:14.880 --> 00:01:19.070 align:start position:0%
source framework for costeffective llm
routing<00:01:15.640><c> so</c><00:01:15.880><c> the</c><00:01:16.200><c> idea</c><00:01:16.840><c> here</c><00:01:17.720><c> is</c><00:01:18.159><c> that</c>

00:01:19.070 --> 00:01:19.080 align:start position:0%
routing so the idea here is that
 

00:01:19.080 --> 00:01:21.230 align:start position:0%
routing so the idea here is that
sometimes<00:01:20.079><c> you're</c><00:01:20.240><c> going</c><00:01:20.360><c> to</c><00:01:20.479><c> be</c><00:01:20.640><c> using</c><00:01:21.000><c> your</c>

00:01:21.230 --> 00:01:21.240 align:start position:0%
sometimes you're going to be using your
 

00:01:21.240 --> 00:01:24.270 align:start position:0%
sometimes you're going to be using your
cheaper<00:01:21.600><c> model</c><00:01:22.040><c> whether</c><00:01:22.240><c> that's</c><00:01:22.360><c> a</c><00:01:22.520><c> llama</c><00:01:23.280><c> 38b</c>

00:01:24.270 --> 00:01:24.280 align:start position:0%
cheaper model whether that's a llama 38b
 

00:01:24.280 --> 00:01:26.270 align:start position:0%
cheaper model whether that's a llama 38b
whether<00:01:24.560><c> that's</c><00:01:24.840><c> a</c><00:01:25.000><c> Gemini</c><00:01:25.439><c> flash</c><00:01:26.000><c> whether</c>

00:01:26.270 --> 00:01:26.280 align:start position:0%
whether that's a Gemini flash whether
 

00:01:26.280 --> 00:01:28.910 align:start position:0%
whether that's a Gemini flash whether
it's<00:01:26.400><c> a</c><00:01:26.520><c> clawed</c><00:01:26.920><c> Hau</c><00:01:27.520><c> something</c><00:01:27.880><c> like</c><00:01:28.159><c> that</c>

00:01:28.910 --> 00:01:28.920 align:start position:0%
it's a clawed Hau something like that
 

00:01:28.920 --> 00:01:30.350 align:start position:0%
it's a clawed Hau something like that
and<00:01:29.040><c> then</c><00:01:29.159><c> at</c><00:01:29.320><c> other</c><00:01:29.600><c> times</c><00:01:30.040><c> you're</c><00:01:30.159><c> going</c><00:01:30.240><c> to</c>

00:01:30.350 --> 00:01:30.360 align:start position:0%
and then at other times you're going to
 

00:01:30.360 --> 00:01:32.670 align:start position:0%
and then at other times you're going to
want<00:01:30.479><c> to</c><00:01:30.640><c> use</c><00:01:30.960><c> your</c><00:01:31.240><c> really</c><00:01:31.600><c> powerful</c><00:01:32.079><c> model</c>

00:01:32.670 --> 00:01:32.680 align:start position:0%
want to use your really powerful model
 

00:01:32.680 --> 00:01:36.389 align:start position:0%
want to use your really powerful model
like<00:01:33.240><c> the</c><00:01:33.479><c> gp4</c><00:01:34.479><c> the</c><00:01:34.600><c> Claude</c><00:01:35.000><c> Opus</c><00:01:35.720><c> like</c><00:01:35.960><c> Gemini</c>

00:01:36.389 --> 00:01:36.399 align:start position:0%
like the gp4 the Claude Opus like Gemini
 

00:01:36.399 --> 00:01:38.550 align:start position:0%
like the gp4 the Claude Opus like Gemini
Ultra<00:01:36.960><c> that</c><00:01:37.079><c> kind</c><00:01:37.240><c> of</c><00:01:37.399><c> thing</c><00:01:38.280><c> but</c><00:01:38.439><c> the</c>

00:01:38.550 --> 00:01:38.560 align:start position:0%
Ultra that kind of thing but the
 

00:01:38.560 --> 00:01:41.870 align:start position:0%
Ultra that kind of thing but the
challenge<00:01:39.040><c> is</c><00:01:39.399><c> how</c><00:01:39.560><c> do</c><00:01:39.720><c> you</c><00:01:40.119><c> decide</c><00:01:41.119><c> when</c><00:01:41.640><c> to</c>

00:01:41.870 --> 00:01:41.880 align:start position:0%
challenge is how do you decide when to
 

00:01:41.880 --> 00:01:44.709 align:start position:0%
challenge is how do you decide when to
use<00:01:42.240><c> one</c><00:01:42.680><c> versus</c><00:01:43.159><c> the</c><00:01:43.320><c> other</c><00:01:44.000><c> so</c><00:01:44.240><c> most</c><00:01:44.439><c> of</c><00:01:44.560><c> the</c>

00:01:44.709 --> 00:01:44.719 align:start position:0%
use one versus the other so most of the
 

00:01:44.719 --> 00:01:46.670 align:start position:0%
use one versus the other so most of the
time<00:01:44.960><c> up</c><00:01:45.119><c> until</c><00:01:45.439><c> now</c><00:01:45.880><c> what</c><00:01:46.040><c> I've</c><00:01:46.240><c> generally</c>

00:01:46.670 --> 00:01:46.680 align:start position:0%
time up until now what I've generally
 

00:01:46.680 --> 00:01:48.469 align:start position:0%
time up until now what I've generally
done<00:01:47.200><c> for</c><00:01:47.399><c> projects</c><00:01:47.719><c> that</c><00:01:47.880><c> I've</c><00:01:48.000><c> been</c><00:01:48.159><c> working</c>

00:01:48.469 --> 00:01:48.479 align:start position:0%
done for projects that I've been working
 

00:01:48.479 --> 00:01:51.149 align:start position:0%
done for projects that I've been working
on<00:01:48.840><c> is</c><00:01:49.079><c> that</c><00:01:49.320><c> certain</c><00:01:49.719><c> calls</c><00:01:50.280><c> I</c><00:01:50.399><c> make</c><00:01:50.640><c> to</c><00:01:51.040><c> the</c>

00:01:51.149 --> 00:01:51.159 align:start position:0%
on is that certain calls I make to the
 

00:01:51.159 --> 00:01:53.310 align:start position:0%
on is that certain calls I make to the
cheaper<00:01:51.560><c> models</c><00:01:52.079><c> and</c><00:01:52.240><c> certain</c><00:01:52.600><c> calls</c><00:01:53.040><c> I</c><00:01:53.119><c> make</c>

00:01:53.310 --> 00:01:53.320 align:start position:0%
cheaper models and certain calls I make
 

00:01:53.320 --> 00:01:55.630 align:start position:0%
cheaper models and certain calls I make
to<00:01:53.479><c> the</c><00:01:53.560><c> more</c><00:01:53.799><c> expensive</c><00:01:54.320><c> models</c><00:01:55.280><c> what</c><00:01:55.479><c> this</c>

00:01:55.630 --> 00:01:55.640 align:start position:0%
to the more expensive models what this
 

00:01:55.640 --> 00:01:58.310 align:start position:0%
to the more expensive models what this
framework<00:01:56.240><c> actually</c><00:01:56.920><c> proposes</c><00:01:57.920><c> is</c><00:01:58.159><c> that</c>

00:01:58.310 --> 00:01:58.320 align:start position:0%
framework actually proposes is that
 

00:01:58.320 --> 00:02:00.789 align:start position:0%
framework actually proposes is that
you've<00:01:58.560><c> got</c><00:01:58.799><c> this</c><00:01:59.039><c> router</c><00:01:59.479><c> in</c><00:01:59.680><c> the</c><00:02:00.000><c> middle</c>

00:02:00.789 --> 00:02:00.799 align:start position:0%
you've got this router in the middle
 

00:02:00.799 --> 00:02:02.630 align:start position:0%
you've got this router in the middle
that<00:02:01.039><c> looks</c><00:02:01.320><c> at</c><00:02:01.520><c> the</c><00:02:01.719><c> call</c><00:02:02.119><c> looks</c><00:02:02.360><c> at</c><00:02:02.479><c> the</c>

00:02:02.630 --> 00:02:02.640 align:start position:0%
that looks at the call looks at the
 

00:02:02.640 --> 00:02:05.950 align:start position:0%
that looks at the call looks at the
prompt<00:02:03.000><c> that's</c><00:02:03.200><c> coming</c><00:02:03.560><c> in</c><00:02:04.240><c> and</c><00:02:04.560><c> decides</c><00:02:05.560><c> hey</c>

00:02:05.950 --> 00:02:05.960 align:start position:0%
prompt that's coming in and decides hey
 

00:02:05.960 --> 00:02:08.990 align:start position:0%
prompt that's coming in and decides hey
is<00:02:06.159><c> this</c><00:02:06.479><c> best</c><00:02:06.840><c> suited</c><00:02:07.320><c> to</c><00:02:08.000><c> a</c><00:02:08.200><c> really</c><00:02:08.759><c> strong</c>

00:02:08.990 --> 00:02:09.000 align:start position:0%
is this best suited to a really strong
 

00:02:09.000 --> 00:02:11.910 align:start position:0%
is this best suited to a really strong
model<00:02:09.520><c> like</c><00:02:09.840><c> gp4</c><00:02:10.840><c> or</c><00:02:11.000><c> can</c><00:02:11.160><c> we</c><00:02:11.319><c> just</c><00:02:11.560><c> answer</c>

00:02:11.910 --> 00:02:11.920 align:start position:0%
model like gp4 or can we just answer
 

00:02:11.920 --> 00:02:14.190 align:start position:0%
model like gp4 or can we just answer
this<00:02:12.080><c> with</c><00:02:12.239><c> a</c><00:02:12.400><c> weaker</c><00:02:12.760><c> model</c><00:02:13.560><c> like</c><00:02:13.800><c> you</c><00:02:13.959><c> know</c><00:02:14.080><c> a</c>

00:02:14.190 --> 00:02:14.200 align:start position:0%
this with a weaker model like you know a
 

00:02:14.200 --> 00:02:17.550 align:start position:0%
this with a weaker model like you know a
Gemini<00:02:14.640><c> flash</c><00:02:15.040><c> or</c><00:02:15.200><c> a</c><00:02:15.319><c> llama</c><00:02:15.680><c> 3</c><00:02:16.200><c> Etc</c><00:02:17.200><c> and</c><00:02:17.360><c> by</c>

00:02:17.550 --> 00:02:17.560 align:start position:0%
Gemini flash or a llama 3 Etc and by
 

00:02:17.560 --> 00:02:20.910 align:start position:0%
Gemini flash or a llama 3 Etc and by
doing<00:02:18.040><c> this</c><00:02:18.599><c> they're</c><00:02:18.959><c> able</c><00:02:19.319><c> to</c><00:02:19.680><c> then</c><00:02:20.200><c> get</c><00:02:20.640><c> this</c>

00:02:20.910 --> 00:02:20.920 align:start position:0%
doing this they're able to then get this
 

00:02:20.920 --> 00:02:24.630 align:start position:0%
doing this they're able to then get this
router<00:02:21.519><c> to</c><00:02:21.840><c> decide</c><00:02:22.519><c> which</c><00:02:22.800><c> model</c><00:02:23.239><c> gets</c><00:02:23.640><c> used</c>

00:02:24.630 --> 00:02:24.640 align:start position:0%
router to decide which model gets used
 

00:02:24.640 --> 00:02:27.790 align:start position:0%
router to decide which model gets used
based<00:02:25.040><c> on</c><00:02:25.239><c> the</c><00:02:25.440><c> query</c><00:02:25.879><c> that's</c><00:02:26.120><c> coming</c><00:02:26.560><c> in</c>

00:02:27.790 --> 00:02:27.800 align:start position:0%
based on the query that's coming in
 

00:02:27.800 --> 00:02:31.710 align:start position:0%
based on the query that's coming in
overall<00:02:28.800><c> this</c><00:02:29.120><c> saves</c><00:02:29.959><c> a</c><00:02:30.200><c> lot</c><00:02:30.519><c> of</c><00:02:30.720><c> money</c><00:02:31.440><c> so</c><00:02:31.599><c> if</c>

00:02:31.710 --> 00:02:31.720 align:start position:0%
overall this saves a lot of money so if
 

00:02:31.720 --> 00:02:33.390 align:start position:0%
overall this saves a lot of money so if
we<00:02:31.840><c> look</c><00:02:32.000><c> into</c><00:02:32.280><c> their</c><00:02:32.480><c> blog</c><00:02:32.800><c> post</c><00:02:33.080><c> a</c><00:02:33.160><c> little</c>

00:02:33.390 --> 00:02:33.400 align:start position:0%
we look into their blog post a little
 

00:02:33.400 --> 00:02:35.949 align:start position:0%
we look into their blog post a little
bit<00:02:34.000><c> we</c><00:02:34.080><c> can</c><00:02:34.280><c> see</c><00:02:34.800><c> that</c><00:02:35.239><c> what</c><00:02:35.400><c> they're</c><00:02:35.599><c> saying</c>

00:02:35.949 --> 00:02:35.959 align:start position:0%
bit we can see that what they're saying
 

00:02:35.959 --> 00:02:38.470 align:start position:0%
bit we can see that what they're saying
here<00:02:36.360><c> is</c><00:02:36.560><c> that</c><00:02:36.840><c> this</c><00:02:36.959><c> is</c><00:02:37.400><c> making</c><00:02:37.720><c> cost</c><00:02:38.040><c> Savings</c>

00:02:38.470 --> 00:02:38.480 align:start position:0%
here is that this is making cost Savings
 

00:02:38.480 --> 00:02:41.830 align:start position:0%
here is that this is making cost Savings
of<00:02:38.680><c> over</c><00:02:39.000><c> 85%</c><00:02:40.000><c> on</c><00:02:40.800><c> various</c><00:02:41.159><c> data</c><00:02:41.440><c> sets</c><00:02:41.680><c> that</c>

00:02:41.830 --> 00:02:41.840 align:start position:0%
of over 85% on various data sets that
 

00:02:41.840 --> 00:02:44.390 align:start position:0%
of over 85% on various data sets that
they've<00:02:42.040><c> tried</c><00:02:42.400><c> out</c><00:02:42.720><c> there</c><00:02:43.400><c> that</c><00:02:43.840><c> it's</c><00:02:44.080><c> still</c>

00:02:44.390 --> 00:02:44.400 align:start position:0%
they've tried out there that it's still
 

00:02:44.400 --> 00:02:47.030 align:start position:0%
they've tried out there that it's still
able<00:02:44.640><c> to</c><00:02:44.879><c> get</c><00:02:45.239><c> very</c><00:02:45.480><c> high</c><00:02:45.680><c> level</c><00:02:46.000><c> of</c><00:02:46.360><c> accuracy</c>

00:02:47.030 --> 00:02:47.040 align:start position:0%
able to get very high level of accuracy
 

00:02:47.040 --> 00:02:49.630 align:start position:0%
able to get very high level of accuracy
on<00:02:47.280><c> those</c><00:02:47.599><c> benchmarks</c><00:02:48.599><c> but</c><00:02:48.720><c> it's</c><00:02:48.959><c> able</c><00:02:49.200><c> to</c><00:02:49.440><c> do</c>

00:02:49.630 --> 00:02:49.640 align:start position:0%
on those benchmarks but it's able to do
 

00:02:49.640 --> 00:02:52.309 align:start position:0%
on those benchmarks but it's able to do
it<00:02:50.040><c> by</c><00:02:50.599><c> deciding</c><00:02:51.239><c> that</c><00:02:51.560><c> actually</c><00:02:52.040><c> this</c>

00:02:52.309 --> 00:02:52.319 align:start position:0%
it by deciding that actually this
 

00:02:52.319 --> 00:02:54.149 align:start position:0%
it by deciding that actually this
particular<00:02:52.840><c> question</c><00:02:53.239><c> we</c><00:02:53.400><c> don't</c><00:02:53.599><c> need</c><00:02:54.000><c> the</c>

00:02:54.149 --> 00:02:54.159 align:start position:0%
particular question we don't need the
 

00:02:54.159 --> 00:02:56.670 align:start position:0%
particular question we don't need the
Top<00:02:54.400><c> Model</c><00:02:54.920><c> we</c><00:02:55.040><c> can</c><00:02:55.200><c> go</c><00:02:55.319><c> for</c><00:02:55.519><c> a</c><00:02:55.640><c> cheaper</c><00:02:56.000><c> model</c>

00:02:56.670 --> 00:02:56.680 align:start position:0%
Top Model we can go for a cheaper model
 

00:02:56.680 --> 00:02:58.270 align:start position:0%
Top Model we can go for a cheaper model
and<00:02:56.800><c> so</c><00:02:57.040><c> therefore</c><00:02:57.319><c> they're</c><00:02:57.599><c> able</c><00:02:57.840><c> to</c><00:02:58.000><c> make</c>

00:02:58.270 --> 00:02:58.280 align:start position:0%
and so therefore they're able to make
 

00:02:58.280 --> 00:03:00.910 align:start position:0%
and so therefore they're able to make
cost<00:02:58.519><c> reductions</c><00:02:58.920><c> of</c><00:02:59.159><c> so</c><00:02:59.400><c> much</c><00:02:59.840><c> on</c><00:03:00.040><c> each</c><00:03:00.560><c> data</c>

00:03:00.910 --> 00:03:00.920 align:start position:0%
cost reductions of so much on each data
 

00:03:00.920 --> 00:03:02.550 align:start position:0%
cost reductions of so much on each data
set<00:03:01.400><c> it</c><00:03:01.519><c> also</c><00:03:01.680><c> shows</c><00:03:01.920><c> us</c><00:03:02.040><c> that</c><00:03:02.200><c> things</c><00:03:02.360><c> like</c>

00:03:02.550 --> 00:03:02.560 align:start position:0%
set it also shows us that things like
 

00:03:02.560 --> 00:03:04.990 align:start position:0%
set it also shows us that things like
the<00:03:02.680><c> GSM</c><00:03:03.080><c> 8K</c><00:03:03.640><c> data</c><00:03:03.959><c> set</c><00:03:04.159><c> is</c><00:03:04.319><c> probably</c><00:03:04.640><c> actually</c>

00:03:04.990 --> 00:03:05.000 align:start position:0%
the GSM 8K data set is probably actually
 

00:03:05.000 --> 00:03:07.589 align:start position:0%
the GSM 8K data set is probably actually
a<00:03:05.159><c> lot</c><00:03:05.720><c> harder</c><00:03:06.319><c> obviously</c><00:03:06.799><c> than</c><00:03:07.239><c> certain</c>

00:03:07.589 --> 00:03:07.599 align:start position:0%
a lot harder obviously than certain
 

00:03:07.599 --> 00:03:09.430 align:start position:0%
a lot harder obviously than certain
other<00:03:07.959><c> things</c><00:03:08.200><c> so</c><00:03:08.400><c> the</c><00:03:08.560><c> cost</c><00:03:08.799><c> savings</c><00:03:09.200><c> are</c>

00:03:09.430 --> 00:03:09.440 align:start position:0%
other things so the cost savings are
 

00:03:09.440 --> 00:03:11.550 align:start position:0%
other things so the cost savings are
actually<00:03:09.760><c> lower</c><00:03:10.239><c> on</c><00:03:10.560><c> that</c><00:03:11.159><c> because</c><00:03:11.360><c> it's</c>

00:03:11.550 --> 00:03:11.560 align:start position:0%
actually lower on that because it's
 

00:03:11.560 --> 00:03:14.110 align:start position:0%
actually lower on that because it's
probably<00:03:11.840><c> falling</c><00:03:12.319><c> back</c><00:03:12.519><c> to</c><00:03:12.720><c> gbd4</c><00:03:13.680><c> for</c><00:03:13.879><c> a</c><00:03:14.000><c> lot</c>

00:03:14.110 --> 00:03:14.120 align:start position:0%
probably falling back to gbd4 for a lot
 

00:03:14.120 --> 00:03:15.990 align:start position:0%
probably falling back to gbd4 for a lot
of<00:03:14.319><c> those</c><00:03:14.680><c> as</c><00:03:14.879><c> compared</c><00:03:15.200><c> to</c><00:03:15.319><c> some</c><00:03:15.480><c> other</c><00:03:15.680><c> data</c>

00:03:15.990 --> 00:03:16.000 align:start position:0%
of those as compared to some other data
 

00:03:16.000 --> 00:03:18.309 align:start position:0%
of those as compared to some other data
sets<00:03:16.239><c> out</c><00:03:16.560><c> there</c><00:03:17.080><c> and</c><00:03:17.159><c> you</c><00:03:17.280><c> can</c><00:03:17.400><c> see</c><00:03:17.760><c> even</c><00:03:18.080><c> with</c>

00:03:18.309 --> 00:03:18.319 align:start position:0%
sets out there and you can see even with
 

00:03:18.319 --> 00:03:20.350 align:start position:0%
sets out there and you can see even with
these<00:03:18.599><c> cost</c><00:03:18.840><c> savings</c><00:03:19.280><c> they're</c><00:03:19.519><c> still</c><00:03:19.760><c> able</c><00:03:20.000><c> to</c>

00:03:20.350 --> 00:03:20.360 align:start position:0%
these cost savings they're still able to
 

00:03:20.360 --> 00:03:23.949 align:start position:0%
these cost savings they're still able to
achieve<00:03:20.959><c> 95%</c><00:03:21.959><c> of</c><00:03:22.159><c> GPT</c><00:03:22.640><c> 4's</c><00:03:23.120><c> performance</c><00:03:23.680><c> in</c>

00:03:23.949 --> 00:03:23.959 align:start position:0%
achieve 95% of GPT 4's performance in
 

00:03:23.959 --> 00:03:26.750 align:start position:0%
achieve 95% of GPT 4's performance in
here<00:03:24.799><c> so</c><00:03:25.000><c> this</c><00:03:25.120><c> is</c><00:03:25.319><c> a</c><00:03:25.519><c> a</c><00:03:25.720><c> really</c><00:03:26.000><c> nice</c><00:03:26.360><c> idea</c>

00:03:26.750 --> 00:03:26.760 align:start position:0%
here so this is a a really nice idea
 

00:03:26.760 --> 00:03:29.270 align:start position:0%
here so this is a a really nice idea
that<00:03:26.920><c> they've</c><00:03:27.120><c> put</c><00:03:27.280><c> out</c><00:03:27.480><c> as</c><00:03:27.599><c> a</c><00:03:27.879><c> paper</c><00:03:28.879><c> but</c><00:03:29.080><c> far</c>

00:03:29.270 --> 00:03:29.280 align:start position:0%
that they've put out as a paper but far
 

00:03:29.280 --> 00:03:30.830 align:start position:0%
that they've put out as a paper but far
more<00:03:29.480><c> than</c><00:03:29.599><c> that</c><00:03:29.959><c> they've</c><00:03:30.200><c> released</c><00:03:30.680><c> the</c>

00:03:30.830 --> 00:03:30.840 align:start position:0%
more than that they've released the
 

00:03:30.840 --> 00:03:32.589 align:start position:0%
more than that they've released the
open-<00:03:31.159><c> source</c><00:03:31.439><c> framework</c><00:03:32.000><c> they've</c><00:03:32.200><c> released</c>

00:03:32.589 --> 00:03:32.599 align:start position:0%
open- source framework they've released
 

00:03:32.599 --> 00:03:34.830 align:start position:0%
open- source framework they've released
the<00:03:32.799><c> code</c><00:03:33.519><c> they've</c><00:03:33.680><c> released</c><00:03:34.080><c> the</c><00:03:34.239><c> data</c><00:03:34.560><c> sets</c>

00:03:34.830 --> 00:03:34.840 align:start position:0%
the code they've released the data sets
 

00:03:34.840 --> 00:03:36.789 align:start position:0%
the code they've released the data sets
that<00:03:35.000><c> they</c><00:03:35.159><c> use</c><00:03:35.439><c> to</c><00:03:35.599><c> do</c><00:03:35.879><c> this</c><00:03:36.480><c> and</c><00:03:36.640><c> they've</c>

00:03:36.789 --> 00:03:36.799 align:start position:0%
that they use to do this and they've
 

00:03:36.799 --> 00:03:38.190 align:start position:0%
that they use to do this and they've
released<00:03:37.159><c> the</c><00:03:37.280><c> models</c><00:03:37.760><c> that</c><00:03:37.879><c> you</c><00:03:38.000><c> can</c>

00:03:38.190 --> 00:03:38.200 align:start position:0%
released the models that you can
 

00:03:38.200 --> 00:03:40.110 align:start position:0%
released the models that you can
actually<00:03:38.760><c> take</c><00:03:39.120><c> and</c><00:03:39.280><c> start</c><00:03:39.519><c> putting</c><00:03:39.799><c> this</c><00:03:39.920><c> in</c>

00:03:40.110 --> 00:03:40.120 align:start position:0%
actually take and start putting this in
 

00:03:40.120 --> 00:03:42.470 align:start position:0%
actually take and start putting this in
production<00:03:41.080><c> so</c><00:03:41.280><c> if</c><00:03:41.400><c> you</c><00:03:41.519><c> do</c><00:03:41.799><c> have</c><00:03:41.959><c> an</c><00:03:42.239><c> app</c>

00:03:42.470 --> 00:03:42.480 align:start position:0%
production so if you do have an app
 

00:03:42.480 --> 00:03:44.429 align:start position:0%
production so if you do have an app
that's<00:03:42.680><c> in</c><00:03:42.879><c> production</c><00:03:43.879><c> and</c><00:03:43.959><c> you're</c><00:03:44.200><c> looking</c>

00:03:44.429 --> 00:03:44.439 align:start position:0%
that's in production and you're looking
 

00:03:44.439 --> 00:03:46.910 align:start position:0%
that's in production and you're looking
at<00:03:44.680><c> ways</c><00:03:44.920><c> to</c><00:03:45.159><c> save</c><00:03:45.480><c> money</c><00:03:45.920><c> on</c><00:03:46.159><c> this</c><00:03:46.519><c> and</c><00:03:46.640><c> I</c><00:03:46.720><c> know</c>

00:03:46.910 --> 00:03:46.920 align:start position:0%
at ways to save money on this and I know
 

00:03:46.920 --> 00:03:48.830 align:start position:0%
at ways to save money on this and I know
that<00:03:47.080><c> quite</c><00:03:47.239><c> a</c><00:03:47.360><c> few</c><00:03:47.519><c> of</c><00:03:47.720><c> the</c><00:03:48.239><c> apps</c><00:03:48.560><c> that</c><00:03:48.680><c> are</c>

00:03:48.830 --> 00:03:48.840 align:start position:0%
that quite a few of the apps that are
 

00:03:48.840 --> 00:03:51.270 align:start position:0%
that quite a few of the apps that are
out<00:03:49.080><c> there</c><00:03:49.239><c> in</c><00:03:49.439><c> production</c><00:03:50.000><c> now</c><00:03:50.560><c> are</c><00:03:50.760><c> sort</c><00:03:50.959><c> of</c>

00:03:51.270 --> 00:03:51.280 align:start position:0%
out there in production now are sort of
 

00:03:51.280 --> 00:03:53.910 align:start position:0%
out there in production now are sort of
often<00:03:51.920><c> borderline</c><00:03:52.920><c> between</c><00:03:53.480><c> being</c>

00:03:53.910 --> 00:03:53.920 align:start position:0%
often borderline between being
 

00:03:53.920 --> 00:03:56.149 align:start position:0%
often borderline between being
profitable<00:03:54.599><c> versus</c><00:03:55.000><c> non-profitable</c><00:03:56.000><c> and</c>

00:03:56.149 --> 00:03:56.159 align:start position:0%
profitable versus non-profitable and
 

00:03:56.159 --> 00:03:58.110 align:start position:0%
profitable versus non-profitable and
stuff<00:03:56.360><c> like</c><00:03:56.599><c> that</c><00:03:57.280><c> and</c><00:03:57.360><c> a</c><00:03:57.480><c> lot</c><00:03:57.599><c> of</c><00:03:57.680><c> it</c><00:03:57.840><c> comes</c>

00:03:58.110 --> 00:03:58.120 align:start position:0%
stuff like that and a lot of it comes
 

00:03:58.120 --> 00:03:59.910 align:start position:0%
stuff like that and a lot of it comes
down<00:03:58.319><c> to</c><00:03:58.599><c> what</c><00:03:58.799><c> models</c><00:03:59.159><c> you're</c><00:03:59.360><c> actually</c>

00:03:59.910 --> 00:03:59.920 align:start position:0%
down to what models you're actually
 

00:03:59.920 --> 00:04:02.390 align:start position:0%
down to what models you're actually
using<00:04:00.920><c> so</c><00:04:01.360><c> this</c><00:04:01.519><c> kind</c><00:04:01.640><c> of</c><00:04:01.799><c> thing</c><00:04:01.959><c> allows</c><00:04:02.280><c> you</c>

00:04:02.390 --> 00:04:02.400 align:start position:0%
using so this kind of thing allows you
 

00:04:02.400 --> 00:04:05.429 align:start position:0%
using so this kind of thing allows you
to<00:04:02.560><c> have</c><00:04:02.920><c> the</c><00:04:03.159><c> benefit</c><00:04:03.799><c> of</c><00:04:04.040><c> when</c><00:04:04.680><c> 80%</c><00:04:05.200><c> of</c><00:04:05.280><c> your</c>

00:04:05.429 --> 00:04:05.439 align:start position:0%
to have the benefit of when 80% of your
 

00:04:05.439 --> 00:04:07.030 align:start position:0%
to have the benefit of when 80% of your
queries<00:04:05.840><c> really</c><00:04:06.040><c> could</c><00:04:06.200><c> be</c><00:04:06.319><c> done</c><00:04:06.519><c> by</c><00:04:06.640><c> a</c><00:04:06.760><c> cheap</c>

00:04:07.030 --> 00:04:07.040 align:start position:0%
queries really could be done by a cheap
 

00:04:07.040 --> 00:04:08.949 align:start position:0%
queries really could be done by a cheap
model<00:04:07.400><c> and</c><00:04:07.519><c> you</c><00:04:07.760><c> only</c><00:04:08.040><c> need</c><00:04:08.319><c> the</c><00:04:08.519><c> really</c>

00:04:08.949 --> 00:04:08.959 align:start position:0%
model and you only need the really
 

00:04:08.959 --> 00:04:11.309 align:start position:0%
model and you only need the really
strong<00:04:09.159><c> model</c><00:04:09.439><c> for</c><00:04:09.640><c> 20%</c><00:04:10.120><c> of</c><00:04:10.239><c> the</c><00:04:10.400><c> queries</c><00:04:11.159><c> it</c>

00:04:11.309 --> 00:04:11.319 align:start position:0%
strong model for 20% of the queries it
 

00:04:11.319 --> 00:04:13.470 align:start position:0%
strong model for 20% of the queries it
gives<00:04:11.480><c> you</c><00:04:11.680><c> massive</c><00:04:12.280><c> cost</c><00:04:12.560><c> Savings</c><00:04:12.959><c> in</c><00:04:13.120><c> doing</c>

00:04:13.470 --> 00:04:13.480 align:start position:0%
gives you massive cost Savings in doing
 

00:04:13.480 --> 00:04:15.509 align:start position:0%
gives you massive cost Savings in doing
this<00:04:14.079><c> so</c><00:04:14.239><c> let's</c><00:04:14.439><c> look</c><00:04:14.560><c> at</c><00:04:14.799><c> how</c><00:04:15.000><c> they</c><00:04:15.239><c> actually</c>

00:04:15.509 --> 00:04:15.519 align:start position:0%
this so let's look at how they actually
 

00:04:15.519 --> 00:04:17.909 align:start position:0%
this so let's look at how they actually
do<00:04:16.000><c> this</c><00:04:16.759><c> so</c><00:04:16.959><c> what</c><00:04:17.120><c> they</c><00:04:17.239><c> did</c><00:04:17.440><c> was</c><00:04:17.639><c> they've</c><00:04:17.799><c> got</c>

00:04:17.909 --> 00:04:17.919 align:start position:0%
do this so what they did was they've got
 

00:04:17.919 --> 00:04:20.110 align:start position:0%
do this so what they did was they've got
a<00:04:18.040><c> bunch</c><00:04:18.199><c> of</c><00:04:18.359><c> data</c><00:04:18.600><c> sets</c><00:04:18.919><c> already</c><00:04:19.440><c> of</c><00:04:19.680><c> human</c>

00:04:20.110 --> 00:04:20.120 align:start position:0%
a bunch of data sets already of human
 

00:04:20.120 --> 00:04:23.030 align:start position:0%
a bunch of data sets already of human
preference<00:04:20.639><c> data</c><00:04:21.199><c> of</c><00:04:21.440><c> knowing</c><00:04:21.959><c> that</c><00:04:22.479><c> when</c><00:04:22.759><c> for</c>

00:04:23.030 --> 00:04:23.040 align:start position:0%
preference data of knowing that when for
 

00:04:23.040 --> 00:04:25.030 align:start position:0%
preference data of knowing that when for
this<00:04:23.280><c> particular</c><00:04:23.840><c> question</c><00:04:24.360><c> someone</c><00:04:24.680><c> typed</c>

00:04:25.030 --> 00:04:25.040 align:start position:0%
this particular question someone typed
 

00:04:25.040 --> 00:04:28.629 align:start position:0%
this particular question someone typed
in<00:04:25.680><c> Model</c><00:04:26.000><c> A</c><00:04:26.280><c> said</c><00:04:26.680><c> this</c><00:04:26.960><c> model</c><00:04:27.240><c> B</c><00:04:27.520><c> said</c><00:04:27.960><c> this</c>

00:04:28.629 --> 00:04:28.639 align:start position:0%
in Model A said this model B said this
 

00:04:28.639 --> 00:04:32.230 align:start position:0%
in Model A said this model B said this
and<00:04:28.880><c> people</c><00:04:29.240><c> picked</c><00:04:29.880><c> Model</c><00:04:30.600><c> A</c><00:04:31.000><c> over</c><00:04:31.280><c> model</c><00:04:31.600><c> B</c>

00:04:32.230 --> 00:04:32.240 align:start position:0%
and people picked Model A over model B
 

00:04:32.240 --> 00:04:34.749 align:start position:0%
and people picked Model A over model B
80%<00:04:32.840><c> of</c><00:04:32.960><c> the</c><00:04:33.120><c> time</c><00:04:33.400><c> or</c><00:04:33.560><c> something</c><00:04:33.919><c> like</c><00:04:34.160><c> that</c>

00:04:34.749 --> 00:04:34.759 align:start position:0%
80% of the time or something like that
 

00:04:34.759 --> 00:04:36.310 align:start position:0%
80% of the time or something like that
from<00:04:35.000><c> that</c><00:04:35.240><c> they're</c><00:04:35.400><c> able</c><00:04:35.600><c> to</c><00:04:35.720><c> build</c><00:04:35.960><c> a</c><00:04:36.080><c> number</c>

00:04:36.310 --> 00:04:36.320 align:start position:0%
from that they're able to build a number
 

00:04:36.320 --> 00:04:38.670 align:start position:0%
from that they're able to build a number
of<00:04:36.479><c> different</c><00:04:36.759><c> models</c><00:04:37.199><c> to</c><00:04:37.400><c> try</c><00:04:37.720><c> and</c><00:04:38.080><c> predict</c>

00:04:38.670 --> 00:04:38.680 align:start position:0%
of different models to try and predict
 

00:04:38.680 --> 00:04:40.870 align:start position:0%
of different models to try and predict
that<00:04:39.160><c> on</c><00:04:39.440><c> things</c><00:04:39.759><c> that</c><00:04:39.960><c> they</c><00:04:40.120><c> haven't</c><00:04:40.440><c> seen</c>

00:04:40.870 --> 00:04:40.880 align:start position:0%
that on things that they haven't seen
 

00:04:40.880 --> 00:04:43.710 align:start position:0%
that on things that they haven't seen
before<00:04:41.720><c> so</c><00:04:42.120><c> the</c><00:04:42.280><c> obvious</c><00:04:42.639><c> sort</c><00:04:42.840><c> of</c><00:04:43.000><c> way</c><00:04:43.199><c> to</c><00:04:43.360><c> do</c>

00:04:43.710 --> 00:04:43.720 align:start position:0%
before so the obvious sort of way to do
 

00:04:43.720 --> 00:04:45.870 align:start position:0%
before so the obvious sort of way to do
this<00:04:44.320><c> is</c><00:04:44.560><c> you</c><00:04:44.720><c> would</c><00:04:44.919><c> take</c><00:04:45.080><c> an</c><00:04:45.280><c> embedding</c>

00:04:45.870 --> 00:04:45.880 align:start position:0%
this is you would take an embedding
 

00:04:45.880 --> 00:04:47.029 align:start position:0%
this is you would take an embedding
right<00:04:46.039><c> and</c><00:04:46.199><c> that's</c><00:04:46.360><c> what</c><00:04:46.479><c> they</c><00:04:46.600><c> do</c><00:04:46.880><c> they</c>

00:04:47.029 --> 00:04:47.039 align:start position:0%
right and that's what they do they
 

00:04:47.039 --> 00:04:49.590 align:start position:0%
right and that's what they do they
actually<00:04:47.320><c> I</c><00:04:47.400><c> think</c><00:04:47.600><c> use</c><00:04:48.000><c> the</c><00:04:48.520><c> open</c><00:04:48.840><c> AI</c><00:04:49.160><c> small</c>

00:04:49.590 --> 00:04:49.600 align:start position:0%
actually I think use the open AI small
 

00:04:49.600 --> 00:04:52.029 align:start position:0%
actually I think use the open AI small
embeddings<00:04:50.199><c> in</c><00:04:50.360><c> doing</c><00:04:50.759><c> this</c><00:04:51.400><c> but</c><00:04:51.560><c> then</c><00:04:51.720><c> once</c>

00:04:52.029 --> 00:04:52.039 align:start position:0%
embeddings in doing this but then once
 

00:04:52.039 --> 00:04:54.909 align:start position:0%
embeddings in doing this but then once
they've<00:04:52.400><c> got</c><00:04:52.720><c> that</c><00:04:53.039><c> data</c><00:04:54.000><c> they</c><00:04:54.199><c> look</c><00:04:54.639><c> at</c>

00:04:54.909 --> 00:04:54.919 align:start position:0%
they've got that data they look at
 

00:04:54.919 --> 00:04:56.950 align:start position:0%
they've got that data they look at
different<00:04:55.360><c> ways</c><00:04:55.960><c> that</c><00:04:56.199><c> they</c><00:04:56.360><c> can</c><00:04:56.600><c> you</c><00:04:56.720><c> know</c>

00:04:56.950 --> 00:04:56.960 align:start position:0%
different ways that they can you know
 

00:04:56.960 --> 00:04:59.310 align:start position:0%
different ways that they can you know
use<00:04:57.360><c> that</c><00:04:57.639><c> and</c><00:04:57.840><c> train</c><00:04:58.320><c> that</c><00:04:58.840><c> so</c><00:04:59.000><c> one</c><00:04:59.120><c> of</c><00:04:59.199><c> the</c>

00:04:59.310 --> 00:04:59.320 align:start position:0%
use that and train that so one of the
 

00:04:59.320 --> 00:05:00.870 align:start position:0%
use that and train that so one of the
first<00:04:59.840><c> ways</c><00:05:00.000><c> that</c><00:05:00.160><c> they</c><00:05:00.280><c> do</c><00:05:00.440><c> this</c><00:05:00.600><c> is</c><00:05:00.720><c> a</c>

00:05:00.870 --> 00:05:00.880 align:start position:0%
first ways that they do this is a
 

00:05:00.880 --> 00:05:03.150 align:start position:0%
first ways that they do this is a
similarity<00:05:01.560><c> weighted</c><00:05:02.280><c> thing</c><00:05:02.520><c> so</c><00:05:02.720><c> this</c><00:05:02.840><c> is</c>

00:05:03.150 --> 00:05:03.160 align:start position:0%
similarity weighted thing so this is
 

00:05:03.160 --> 00:05:05.749 align:start position:0%
similarity weighted thing so this is
basically<00:05:04.160><c> where</c><00:05:04.680><c> they're</c><00:05:05.000><c> working</c><00:05:05.320><c> out</c><00:05:05.600><c> a</c>

00:05:05.749 --> 00:05:05.759 align:start position:0%
basically where they're working out a
 

00:05:05.759 --> 00:05:09.350 align:start position:0%
basically where they're working out a
weighted<00:05:06.199><c> ELO</c><00:05:07.080><c> calculation</c><00:05:08.080><c> based</c><00:05:08.520><c> on</c>

00:05:09.350 --> 00:05:09.360 align:start position:0%
weighted ELO calculation based on
 

00:05:09.360 --> 00:05:11.830 align:start position:0%
weighted ELO calculation based on
similarity<00:05:10.199><c> of</c><00:05:10.440><c> the</c><00:05:10.639><c> embeddings</c><00:05:11.440><c> and</c><00:05:11.639><c> it's</c><00:05:11.759><c> a</c>

00:05:11.830 --> 00:05:11.840 align:start position:0%
similarity of the embeddings and it's a
 

00:05:11.840 --> 00:05:13.110 align:start position:0%
similarity of the embeddings and it's a
little<00:05:12.000><c> more</c><00:05:12.160><c> complicated</c><00:05:12.600><c> than</c><00:05:12.759><c> just</c><00:05:12.840><c> doing</c>

00:05:13.110 --> 00:05:13.120 align:start position:0%
little more complicated than just doing
 

00:05:13.120 --> 00:05:15.150 align:start position:0%
little more complicated than just doing
a<00:05:13.280><c> cosign</c><00:05:13.800><c> score</c><00:05:14.280><c> they</c><00:05:14.400><c> have</c><00:05:14.520><c> to</c><00:05:14.639><c> work</c><00:05:14.800><c> out</c><00:05:15.000><c> the</c>

00:05:15.150 --> 00:05:15.160 align:start position:0%
a cosign score they have to work out the
 

00:05:15.160 --> 00:05:17.670 align:start position:0%
a cosign score they have to work out the
similarity<00:05:15.880><c> and</c><00:05:16.000><c> then</c><00:05:16.199><c> also</c><00:05:16.759><c> that</c><00:05:17.000><c> related</c><00:05:17.440><c> to</c>

00:05:17.670 --> 00:05:17.680 align:start position:0%
similarity and then also that related to
 

00:05:17.680 --> 00:05:19.950 align:start position:0%
similarity and then also that related to
the<00:05:17.840><c> different</c><00:05:18.160><c> models</c><00:05:18.560><c> in</c><00:05:18.840><c> there</c><00:05:19.560><c> the</c><00:05:19.680><c> second</c>

00:05:19.950 --> 00:05:19.960 align:start position:0%
the different models in there the second
 

00:05:19.960 --> 00:05:21.790 align:start position:0%
the different models in there the second
one<00:05:20.120><c> that</c><00:05:20.280><c> they</c><00:05:20.440><c> do</c><00:05:20.880><c> is</c><00:05:21.080><c> a</c><00:05:21.280><c> matrix</c>

00:05:21.790 --> 00:05:21.800 align:start position:0%
one that they do is a matrix
 

00:05:21.800 --> 00:05:24.309 align:start position:0%
one that they do is a matrix
factorization<00:05:22.720><c> model</c><00:05:23.240><c> so</c><00:05:23.440><c> this</c><00:05:23.520><c> is</c><00:05:23.759><c> cool</c><00:05:24.080><c> so</c>

00:05:24.309 --> 00:05:24.319 align:start position:0%
factorization model so this is cool so
 

00:05:24.319 --> 00:05:25.550 align:start position:0%
factorization model so this is cool so
where<00:05:24.440><c> they've</c><00:05:24.639><c> got</c><00:05:24.840><c> imagine</c><00:05:25.120><c> you've</c><00:05:25.280><c> got</c><00:05:25.400><c> a</c>

00:05:25.550 --> 00:05:25.560 align:start position:0%
where they've got imagine you've got a
 

00:05:25.560 --> 00:05:28.230 align:start position:0%
where they've got imagine you've got a
really<00:05:25.840><c> big</c><00:05:26.160><c> Matrix</c><00:05:27.160><c> of</c><00:05:27.400><c> where</c><00:05:27.680><c> you've</c><00:05:27.919><c> got</c>

00:05:28.230 --> 00:05:28.240 align:start position:0%
really big Matrix of where you've got
 

00:05:28.240 --> 00:05:30.150 align:start position:0%
really big Matrix of where you've got
some<00:05:29.039><c> data</c>

00:05:30.150 --> 00:05:30.160 align:start position:0%
some data
 

00:05:30.160 --> 00:05:32.309 align:start position:0%
some data
that<00:05:30.400><c> you</c><00:05:30.639><c> know</c><00:05:31.160><c> that</c><00:05:31.440><c> okay</c><00:05:31.680><c> this</c><00:05:31.840><c> model</c><00:05:32.160><c> is</c>

00:05:32.309 --> 00:05:32.319 align:start position:0%
that you know that okay this model is
 

00:05:32.319 --> 00:05:33.830 align:start position:0%
that you know that okay this model is
better<00:05:32.600><c> than</c><00:05:32.840><c> this</c><00:05:33.039><c> model</c><00:05:33.319><c> for</c><00:05:33.600><c> this</c>

00:05:33.830 --> 00:05:33.840 align:start position:0%
better than this model for this
 

00:05:33.840 --> 00:05:36.270 align:start position:0%
better than this model for this
particular<00:05:34.479><c> phrase</c><00:05:35.360><c> but</c><00:05:35.520><c> for</c><00:05:35.680><c> a</c><00:05:35.759><c> lot</c><00:05:35.919><c> of</c><00:05:36.039><c> it</c>

00:05:36.270 --> 00:05:36.280 align:start position:0%
particular phrase but for a lot of it
 

00:05:36.280 --> 00:05:39.029 align:start position:0%
particular phrase but for a lot of it
you're<00:05:36.520><c> missing</c><00:05:36.960><c> data</c><00:05:37.880><c> so</c><00:05:38.360><c> what</c><00:05:38.479><c> they</c><00:05:38.639><c> do</c><00:05:38.880><c> is</c>

00:05:39.029 --> 00:05:39.039 align:start position:0%
you're missing data so what they do is
 

00:05:39.039 --> 00:05:41.230 align:start position:0%
you're missing data so what they do is
they<00:05:39.160><c> come</c><00:05:39.319><c> up</c><00:05:39.479><c> with</c><00:05:39.680><c> two</c><00:05:39.960><c> matrices</c><00:05:40.960><c> that</c><00:05:41.120><c> they</c>

00:05:41.230 --> 00:05:41.240 align:start position:0%
they come up with two matrices that they
 

00:05:41.240 --> 00:05:44.230 align:start position:0%
they come up with two matrices that they
can<00:05:41.440><c> then</c><00:05:41.639><c> multiply</c><00:05:42.600><c> to</c><00:05:42.800><c> try</c><00:05:42.960><c> and</c><00:05:43.319><c> approximate</c>

00:05:44.230 --> 00:05:44.240 align:start position:0%
can then multiply to try and approximate
 

00:05:44.240 --> 00:05:46.270 align:start position:0%
can then multiply to try and approximate
the<00:05:44.440><c> data</c><00:05:44.720><c> that</c><00:05:44.880><c> they've</c><00:05:45.199><c> got</c><00:05:45.880><c> and</c><00:05:46.000><c> at</c><00:05:46.160><c> the</c>

00:05:46.270 --> 00:05:46.280 align:start position:0%
the data that they've got and at the
 

00:05:46.280 --> 00:05:48.390 align:start position:0%
the data that they've got and at the
same<00:05:46.560><c> time</c><00:05:46.800><c> it</c><00:05:46.960><c> fills</c><00:05:47.319><c> in</c><00:05:47.639><c> all</c><00:05:47.800><c> the</c><00:05:47.960><c> empty</c>

00:05:48.390 --> 00:05:48.400 align:start position:0%
same time it fills in all the empty
 

00:05:48.400 --> 00:05:50.670 align:start position:0%
same time it fills in all the empty
spots<00:05:48.800><c> that</c><00:05:48.960><c> they've</c><00:05:49.199><c> got</c><00:05:49.479><c> as</c><00:05:49.639><c> well</c><00:05:50.319><c> so</c><00:05:50.560><c> by</c>

00:05:50.670 --> 00:05:50.680 align:start position:0%
spots that they've got as well so by
 

00:05:50.680 --> 00:05:52.629 align:start position:0%
spots that they've got as well so by
using<00:05:51.039><c> that</c><00:05:51.199><c> they're</c><00:05:51.440><c> then</c><00:05:51.680><c> able</c><00:05:51.960><c> to</c><00:05:52.199><c> predict</c>

00:05:52.629 --> 00:05:52.639 align:start position:0%
using that they're then able to predict
 

00:05:52.639 --> 00:05:54.950 align:start position:0%
using that they're then able to predict
that<00:05:52.960><c> okay</c><00:05:53.199><c> for</c><00:05:53.720><c> this</c><00:05:54.080><c> new</c><00:05:54.479><c> kind</c><00:05:54.639><c> of</c><00:05:54.759><c> thing</c>

00:05:54.950 --> 00:05:54.960 align:start position:0%
that okay for this new kind of thing
 

00:05:54.960 --> 00:05:56.830 align:start position:0%
that okay for this new kind of thing
that<00:05:55.080><c> we</c><00:05:55.199><c> haven't</c><00:05:55.400><c> seen</c><00:05:55.759><c> before</c><00:05:56.440><c> this</c><00:05:56.560><c> is</c><00:05:56.720><c> the</c>

00:05:56.830 --> 00:05:56.840 align:start position:0%
that we haven't seen before this is the
 

00:05:56.840 --> 00:05:58.749 align:start position:0%
that we haven't seen before this is the
model<00:05:57.160><c> that</c><00:05:57.319><c> most</c><00:05:57.520><c> likely</c><00:05:57.800><c> is</c><00:05:57.919><c> going</c><00:05:58.039><c> to</c><00:05:58.240><c> work</c>

00:05:58.749 --> 00:05:58.759 align:start position:0%
model that most likely is going to work
 

00:05:58.759 --> 00:06:00.350 align:start position:0%
model that most likely is going to work
and<00:05:58.880><c> it</c><00:05:59.039><c> turns</c><00:05:59.280><c> out</c><00:05:59.520><c> that</c><00:05:59.639><c> that</c><00:05:59.800><c> this</c><00:05:59.919><c> one</c><00:06:00.120><c> is</c>

00:06:00.350 --> 00:06:00.360 align:start position:0%
and it turns out that that this one is
 

00:06:00.360 --> 00:06:01.909 align:start position:0%
and it turns out that that this one is
probably<00:06:00.759><c> the</c><00:06:00.960><c> best</c><00:06:01.160><c> model</c><00:06:01.479><c> which</c><00:06:01.600><c> I'll</c><00:06:01.720><c> talk</c>

00:06:01.909 --> 00:06:01.919 align:start position:0%
probably the best model which I'll talk
 

00:06:01.919 --> 00:06:04.189 align:start position:0%
probably the best model which I'll talk
about<00:06:02.199><c> as</c><00:06:02.400><c> we</c><00:06:02.560><c> come</c><00:06:02.759><c> back</c><00:06:03.199><c> the</c><00:06:03.360><c> third</c><00:06:03.639><c> one</c><00:06:04.039><c> is</c>

00:06:04.189 --> 00:06:04.199 align:start position:0%
about as we come back the third one is
 

00:06:04.199 --> 00:06:05.590 align:start position:0%
about as we come back the third one is
where<00:06:04.360><c> they're</c><00:06:04.479><c> using</c><00:06:04.680><c> a</c><00:06:04.840><c> Bert</c><00:06:05.160><c> model</c><00:06:05.479><c> and</c>

00:06:05.590 --> 00:06:05.600 align:start position:0%
where they're using a Bert model and
 

00:06:05.600 --> 00:06:07.749 align:start position:0%
where they're using a Bert model and
training<00:06:05.880><c> up</c><00:06:06.039><c> a</c><00:06:06.240><c> classifier</c><00:06:06.800><c> for</c><00:06:07.000><c> it</c><00:06:07.479><c> and</c><00:06:07.680><c> the</c>

00:06:07.749 --> 00:06:07.759 align:start position:0%
training up a classifier for it and the
 

00:06:07.759 --> 00:06:09.270 align:start position:0%
training up a classifier for it and the
fourth<00:06:08.000><c> one</c><00:06:08.199><c> is</c><00:06:08.599><c> kind</c><00:06:08.720><c> of</c><00:06:08.840><c> similar</c><00:06:09.160><c> but</c>

00:06:09.270 --> 00:06:09.280 align:start position:0%
fourth one is kind of similar but
 

00:06:09.280 --> 00:06:12.110 align:start position:0%
fourth one is kind of similar but
they're<00:06:09.440><c> using</c><00:06:09.840><c> an</c><00:06:10.000><c> llm</c><00:06:10.599><c> classifier</c><00:06:11.240><c> for</c><00:06:11.479><c> it</c>

00:06:12.110 --> 00:06:12.120 align:start position:0%
they're using an llm classifier for it
 

00:06:12.120 --> 00:06:13.390 align:start position:0%
they're using an llm classifier for it
if<00:06:12.199><c> you</c><00:06:12.319><c> look</c><00:06:12.440><c> at</c><00:06:12.560><c> the</c><00:06:12.680><c> results</c><00:06:12.960><c> in</c><00:06:13.120><c> here</c><00:06:13.319><c> what</c>

00:06:13.390 --> 00:06:13.400 align:start position:0%
if you look at the results in here what
 

00:06:13.400 --> 00:06:14.830 align:start position:0%
if you look at the results in here what
we're<00:06:13.560><c> looking</c><00:06:13.759><c> for</c><00:06:14.120><c> is</c><00:06:14.280><c> this</c><00:06:14.479><c> kind</c><00:06:14.599><c> of</c>

00:06:14.830 --> 00:06:14.840 align:start position:0%
we're looking for is this kind of
 

00:06:14.840 --> 00:06:17.430 align:start position:0%
we're looking for is this kind of
efficient<00:06:15.240><c> Frontier</c><00:06:16.240><c> of</c><00:06:16.400><c> working</c><00:06:16.759><c> out</c><00:06:17.240><c> what</c>

00:06:17.430 --> 00:06:17.440 align:start position:0%
efficient Frontier of working out what
 

00:06:17.440 --> 00:06:19.830 align:start position:0%
efficient Frontier of working out what
model<00:06:17.759><c> you</c><00:06:17.880><c> should</c><00:06:18.160><c> go</c><00:06:18.400><c> for</c><00:06:19.199><c> based</c><00:06:19.440><c> on</c><00:06:19.639><c> this</c>

00:06:19.830 --> 00:06:19.840 align:start position:0%
model you should go for based on this
 

00:06:19.840 --> 00:06:21.790 align:start position:0%
model you should go for based on this
efficient<00:06:20.240><c> Frontier</c><00:06:20.800><c> and</c><00:06:21.000><c> here</c><00:06:21.560><c> when</c><00:06:21.720><c> they</c>

00:06:21.790 --> 00:06:21.800 align:start position:0%
efficient Frontier and here when they
 

00:06:21.800 --> 00:06:24.350 align:start position:0%
efficient Frontier and here when they
train<00:06:22.080><c> this</c><00:06:22.199><c> up</c><00:06:22.400><c> on</c><00:06:22.680><c> just</c><00:06:22.919><c> the</c><00:06:23.080><c> data</c><00:06:23.360><c> set</c><00:06:23.639><c> pairs</c>

00:06:24.350 --> 00:06:24.360 align:start position:0%
train this up on just the data set pairs
 

00:06:24.360 --> 00:06:26.469 align:start position:0%
train this up on just the data set pairs
that<00:06:24.520><c> they</c><00:06:24.680><c> have</c><00:06:25.160><c> themselves</c><00:06:25.680><c> they</c><00:06:25.919><c> get</c><00:06:26.080><c> out</c><00:06:26.280><c> a</c>

00:06:26.469 --> 00:06:26.479 align:start position:0%
that they have themselves they get out a
 

00:06:26.479 --> 00:06:28.990 align:start position:0%
that they have themselves they get out a
model<00:06:27.280><c> but</c><00:06:27.440><c> then</c><00:06:27.639><c> what</c><00:06:27.880><c> they</c><00:06:28.199><c> also</c><00:06:28.479><c> worked</c><00:06:28.759><c> out</c>

00:06:28.990 --> 00:06:29.000 align:start position:0%
model but then what they also worked out
 

00:06:29.000 --> 00:06:31.029 align:start position:0%
model but then what they also worked out
they<00:06:29.120><c> could</c><00:06:29.240><c> do</c><00:06:29.759><c> augment</c><00:06:30.199><c> this</c><00:06:30.440><c> data</c><00:06:30.800><c> so</c>

00:06:31.029 --> 00:06:31.039 align:start position:0%
they could do augment this data so
 

00:06:31.039 --> 00:06:34.550 align:start position:0%
they could do augment this data so
basically<00:06:31.840><c> they</c><00:06:32.000><c> could</c><00:06:32.240><c> use</c><00:06:33.039><c> gp4</c><00:06:34.039><c> to</c><00:06:34.199><c> be</c><00:06:34.440><c> like</c>

00:06:34.550 --> 00:06:34.560 align:start position:0%
basically they could use gp4 to be like
 

00:06:34.560 --> 00:06:37.629 align:start position:0%
basically they could use gp4 to be like
a<00:06:34.759><c> human</c><00:06:35.080><c> and</c><00:06:35.280><c> judging</c><00:06:35.840><c> as</c><00:06:36.000><c> well</c><00:06:36.720><c> in</c><00:06:36.919><c> there</c><00:06:37.479><c> and</c>

00:06:37.629 --> 00:06:37.639 align:start position:0%
a human and judging as well in there and
 

00:06:37.639 --> 00:06:39.589 align:start position:0%
a human and judging as well in there and
that<00:06:38.000><c> turns</c><00:06:38.240><c> out</c><00:06:38.400><c> to</c><00:06:38.639><c> you</c><00:06:38.720><c> know</c><00:06:38.880><c> work</c><00:06:39.080><c> out</c><00:06:39.520><c> you</c>

00:06:39.589 --> 00:06:39.599 align:start position:0%
that turns out to you know work out you
 

00:06:39.599 --> 00:06:41.390 align:start position:0%
that turns out to you know work out you
know<00:06:39.800><c> quite</c><00:06:40.039><c> nicely</c><00:06:40.560><c> that</c><00:06:40.720><c> when</c><00:06:40.840><c> you</c><00:06:41.080><c> then</c>

00:06:41.390 --> 00:06:41.400 align:start position:0%
know quite nicely that when you then
 

00:06:41.400 --> 00:06:43.189 align:start position:0%
know quite nicely that when you then
train<00:06:41.680><c> up</c><00:06:41.840><c> your</c><00:06:41.960><c> models</c><00:06:42.319><c> on</c><00:06:42.479><c> this</c><00:06:42.680><c> augmented</c>

00:06:43.189 --> 00:06:43.199 align:start position:0%
train up your models on this augmented
 

00:06:43.199 --> 00:06:46.469 align:start position:0%
train up your models on this augmented
data<00:06:43.560><c> as</c><00:06:43.680><c> well</c><00:06:44.479><c> I</c><00:06:44.560><c> think</c><00:06:44.800><c> all</c><00:06:44.960><c> of</c><00:06:45.120><c> them</c><00:06:45.479><c> improve</c>

00:06:46.469 --> 00:06:46.479 align:start position:0%
data as well I think all of them improve
 

00:06:46.479 --> 00:06:48.390 align:start position:0%
data as well I think all of them improve
and<00:06:46.639><c> this</c><00:06:46.759><c> Matrix</c><00:06:47.240><c> factorization</c><00:06:48.000><c> seems</c><00:06:48.280><c> to</c>

00:06:48.390 --> 00:06:48.400 align:start position:0%
and this Matrix factorization seems to
 

00:06:48.400 --> 00:06:50.670 align:start position:0%
and this Matrix factorization seems to
do<00:06:48.720><c> really</c><00:06:49.000><c> well</c><00:06:49.880><c> this</c><00:06:50.000><c> is</c><00:06:50.199><c> where</c><00:06:50.440><c> they're</c>

00:06:50.670 --> 00:06:50.680 align:start position:0%
do really well this is where they're
 

00:06:50.680 --> 00:06:52.950 align:start position:0%
do really well this is where they're
using<00:06:51.599><c> the</c><00:06:51.720><c> model</c><00:06:51.960><c> to</c><00:06:52.160><c> predict</c><00:06:52.520><c> and</c><00:06:52.639><c> it</c><00:06:52.759><c> ends</c>

00:06:52.950 --> 00:06:52.960 align:start position:0%
using the model to predict and it ends
 

00:06:52.960 --> 00:06:56.749 align:start position:0%
using the model to predict and it ends
up<00:06:53.160><c> using</c><00:06:53.560><c> gp4</c><00:06:54.199><c> 26%</c><00:06:54.960><c> of</c><00:06:55.080><c> the</c><00:06:55.280><c> time</c><00:06:55.960><c> and</c><00:06:56.479><c> I</c><00:06:56.599><c> guess</c>

00:06:56.749 --> 00:06:56.759 align:start position:0%
up using gp4 26% of the time and I guess
 

00:06:56.759 --> 00:06:58.909 align:start position:0%
up using gp4 26% of the time and I guess
the<00:06:56.919><c> other</c><00:06:57.199><c> model</c><00:06:57.639><c> the</c><00:06:57.800><c> the</c><00:06:57.960><c> rest</c><00:06:58.120><c> of</c><00:06:58.240><c> the</c><00:06:58.440><c> time</c>

00:06:58.909 --> 00:06:58.919 align:start position:0%
the other model the the rest of the time
 

00:06:58.919 --> 00:07:01.150 align:start position:0%
the other model the the rest of the time
and<00:06:59.039><c> that</c><00:06:59.160><c> turn</c><00:06:59.400><c> out</c><00:06:59.680><c> to</c><00:06:59.759><c> be</c><00:07:00.039><c> half</c><00:07:00.280><c> as</c><00:07:00.479><c> cheap</c><00:07:00.919><c> as</c>

00:07:01.150 --> 00:07:01.160 align:start position:0%
and that turn out to be half as cheap as
 

00:07:01.160 --> 00:07:04.070 align:start position:0%
and that turn out to be half as cheap as
just<00:07:01.479><c> going</c><00:07:01.759><c> for</c><00:07:02.160><c> a</c><00:07:02.360><c> random</c><00:07:02.840><c> Baseline</c><00:07:03.840><c> for</c>

00:07:04.070 --> 00:07:04.080 align:start position:0%
just going for a random Baseline for
 

00:07:04.080 --> 00:07:06.510 align:start position:0%
just going for a random Baseline for
doing<00:07:04.639><c> this</c><00:07:05.639><c> another</c><00:07:05.919><c> thing</c><00:07:06.080><c> that</c><00:07:06.199><c> they</c><00:07:06.319><c> found</c>

00:07:06.510 --> 00:07:06.520 align:start position:0%
doing this another thing that they found
 

00:07:06.520 --> 00:07:08.150 align:start position:0%
doing this another thing that they found
that<00:07:06.639><c> was</c><00:07:06.800><c> really</c><00:07:07.080><c> interesting</c><00:07:07.680><c> here</c><00:07:08.000><c> was</c>

00:07:08.150 --> 00:07:08.160 align:start position:0%
that was really interesting here was
 

00:07:08.160 --> 00:07:09.869 align:start position:0%
that was really interesting here was
that<00:07:08.360><c> while</c><00:07:08.560><c> they</c><00:07:08.680><c> did</c><00:07:08.879><c> their</c><00:07:09.080><c> training</c><00:07:09.680><c> on</c>

00:07:09.869 --> 00:07:09.879 align:start position:0%
that while they did their training on
 

00:07:09.879 --> 00:07:14.150 align:start position:0%
that while they did their training on
Mixr<00:07:10.639><c> versus</c><00:07:11.400><c> gp4</c><00:07:12.199><c> models</c><00:07:13.039><c> the</c><00:07:13.280><c> actual</c><00:07:13.680><c> models</c>

00:07:14.150 --> 00:07:14.160 align:start position:0%
Mixr versus gp4 models the actual models
 

00:07:14.160 --> 00:07:16.390 align:start position:0%
Mixr versus gp4 models the actual models
that<00:07:14.319><c> they</c><00:07:14.479><c> end</c><00:07:14.680><c> up</c><00:07:14.919><c> making</c><00:07:15.400><c> that</c><00:07:15.680><c> make</c><00:07:16.120><c> this</c>

00:07:16.390 --> 00:07:16.400 align:start position:0%
that they end up making that make this
 

00:07:16.400 --> 00:07:19.790 align:start position:0%
that they end up making that make this
prediction<00:07:16.960><c> of</c><00:07:17.280><c> which</c><00:07:17.520><c> llm</c><00:07:18.080><c> to</c><00:07:18.319><c> use</c><00:07:19.319><c> works</c>

00:07:19.790 --> 00:07:19.800 align:start position:0%
prediction of which llm to use works
 

00:07:19.800 --> 00:07:21.990 align:start position:0%
prediction of which llm to use works
really<00:07:20.160><c> well</c><00:07:20.759><c> even</c><00:07:21.080><c> when</c><00:07:21.199><c> you</c><00:07:21.360><c> swap</c><00:07:21.720><c> out</c><00:07:21.879><c> the</c>

00:07:21.990 --> 00:07:22.000 align:start position:0%
really well even when you swap out the
 

00:07:22.000 --> 00:07:24.430 align:start position:0%
really well even when you swap out the
models<00:07:22.520><c> so</c><00:07:22.759><c> when</c><00:07:22.879><c> they</c><00:07:23.039><c> change</c><00:07:23.440><c> the</c><00:07:23.599><c> Mixel</c><00:07:24.199><c> for</c>

00:07:24.430 --> 00:07:24.440 align:start position:0%
models so when they change the Mixel for
 

00:07:24.440 --> 00:07:28.790 align:start position:0%
models so when they change the Mixel for
llama<00:07:25.080><c> 38b</c><00:07:26.080><c> and</c><00:07:26.199><c> the</c><00:07:26.319><c> gbd4</c><00:07:27.160><c> for</c><00:07:27.479><c> Claude</c><00:07:27.919><c> Opus</c>

00:07:28.790 --> 00:07:28.800 align:start position:0%
llama 38b and the gbd4 for Claude Opus
 

00:07:28.800 --> 00:07:31.469 align:start position:0%
llama 38b and the gbd4 for Claude Opus
you<00:07:28.919><c> can</c><00:07:29.080><c> see</c><00:07:29.720><c> that</c><00:07:30.120><c> the</c><00:07:30.280><c> model</c><00:07:30.720><c> still</c><00:07:31.120><c> works</c>

00:07:31.469 --> 00:07:31.479 align:start position:0%
you can see that the model still works
 

00:07:31.479 --> 00:07:34.390 align:start position:0%
you can see that the model still works
out<00:07:32.280><c> which</c><00:07:32.560><c> model</c><00:07:32.879><c> to</c><00:07:33.240><c> you</c><00:07:33.360><c> know</c><00:07:33.599><c> actually</c><00:07:34.039><c> use</c>

00:07:34.390 --> 00:07:34.400 align:start position:0%
out which model to you know actually use
 

00:07:34.400 --> 00:07:36.830 align:start position:0%
out which model to you know actually use
in<00:07:34.639><c> here</c><00:07:34.960><c> so</c><00:07:35.479><c> again</c><00:07:35.720><c> you</c><00:07:35.879><c> see</c><00:07:36.199><c> these</c><00:07:36.560><c> cost</c>

00:07:36.830 --> 00:07:36.840 align:start position:0%
in here so again you see these cost
 

00:07:36.840 --> 00:07:39.270 align:start position:0%
in here so again you see these cost
savings<00:07:37.240><c> and</c><00:07:37.479><c> stuff</c><00:07:37.720><c> like</c><00:07:37.919><c> that</c><00:07:38.160><c> from</c><00:07:38.440><c> this</c>

00:07:39.270 --> 00:07:39.280 align:start position:0%
savings and stuff like that from this
 

00:07:39.280 --> 00:07:41.350 align:start position:0%
savings and stuff like that from this
finally<00:07:39.680><c> just</c><00:07:39.759><c> to</c><00:07:39.960><c> finish</c><00:07:40.319><c> up</c><00:07:40.840><c> there</c><00:07:40.960><c> are</c><00:07:41.120><c> some</c>

00:07:41.350 --> 00:07:41.360 align:start position:0%
finally just to finish up there are some
 

00:07:41.360 --> 00:07:42.790 align:start position:0%
finally just to finish up there are some
Commercial<00:07:41.800><c> Services</c><00:07:42.160><c> out</c><00:07:42.319><c> there</c><00:07:42.479><c> have</c><00:07:42.639><c> been</c>

00:07:42.790 --> 00:07:42.800 align:start position:0%
Commercial Services out there have been
 

00:07:42.800 --> 00:07:44.950 align:start position:0%
Commercial Services out there have been
offering<00:07:43.160><c> things</c><00:07:43.560><c> like</c><00:07:43.919><c> this</c><00:07:44.440><c> for</c><00:07:44.639><c> people</c><00:07:44.840><c> to</c>

00:07:44.950 --> 00:07:44.960 align:start position:0%
offering things like this for people to
 

00:07:44.960 --> 00:07:46.909 align:start position:0%
offering things like this for people to
be<00:07:45.080><c> able</c><00:07:45.240><c> to</c><00:07:45.479><c> run</c><00:07:45.720><c> their</c><00:07:45.919><c> calls</c><00:07:46.360><c> through</c><00:07:46.680><c> this</c>

00:07:46.909 --> 00:07:46.919 align:start position:0%
be able to run their calls through this
 

00:07:46.919 --> 00:07:49.270 align:start position:0%
be able to run their calls through this
and<00:07:47.039><c> it</c><00:07:47.199><c> would</c><00:07:47.440><c> decide</c><00:07:47.960><c> what</c><00:07:48.080><c> to</c><00:07:48.319><c> use</c><00:07:49.039><c> and</c><00:07:49.159><c> they</c>

00:07:49.270 --> 00:07:49.280 align:start position:0%
and it would decide what to use and they
 

00:07:49.280 --> 00:07:51.029 align:start position:0%
and it would decide what to use and they
show<00:07:49.599><c> that</c><00:07:49.800><c> basically</c><00:07:50.560><c> you</c><00:07:50.680><c> know</c><00:07:50.879><c> what</c>

00:07:51.029 --> 00:07:51.039 align:start position:0%
show that basically you know what
 

00:07:51.039 --> 00:07:53.670 align:start position:0%
show that basically you know what
they're<00:07:51.240><c> releasing</c><00:07:51.919><c> as</c><00:07:52.159><c> open</c><00:07:52.520><c> source</c><00:07:52.919><c> here</c>

00:07:53.670 --> 00:07:53.680 align:start position:0%
they're releasing as open source here
 

00:07:53.680 --> 00:07:55.830 align:start position:0%
they're releasing as open source here
actually<00:07:54.199><c> is</c><00:07:54.360><c> doing</c><00:07:54.800><c> just</c><00:07:54.960><c> as</c><00:07:55.159><c> good</c><00:07:55.440><c> as</c><00:07:55.680><c> the</c>

00:07:55.830 --> 00:07:55.840 align:start position:0%
actually is doing just as good as the
 

00:07:55.840 --> 00:07:57.830 align:start position:0%
actually is doing just as good as the
the<00:07:55.919><c> commercial</c><00:07:56.400><c> ones</c><00:07:56.800><c> and</c><00:07:56.919><c> then</c><00:07:57.360><c> obviously</c>

00:07:57.830 --> 00:07:57.840 align:start position:0%
the commercial ones and then obviously
 

00:07:57.840 --> 00:07:59.869 align:start position:0%
the commercial ones and then obviously
also<00:07:58.039><c> a</c><00:07:58.159><c> lot</c><00:07:58.360><c> cheaper</c><00:07:58.759><c> and</c><00:07:58.919><c> stuff</c><00:07:59.159><c> as</c><00:07:59.280><c> well</c><00:07:59.720><c> in</c>

00:07:59.869 --> 00:07:59.879 align:start position:0%
also a lot cheaper and stuff as well in
 

00:07:59.879 --> 00:08:02.790 align:start position:0%
also a lot cheaper and stuff as well in
here<00:08:00.759><c> so</c><00:08:01.000><c> just</c><00:08:01.120><c> to</c><00:08:01.280><c> finish</c><00:08:01.560><c> up</c><00:08:01.800><c> I</c><00:08:01.879><c> would</c><00:08:02.120><c> say</c><00:08:02.680><c> if</c>

00:08:02.790 --> 00:08:02.800 align:start position:0%
here so just to finish up I would say if
 

00:08:02.800 --> 00:08:03.950 align:start position:0%
here so just to finish up I would say if
you're<00:08:03.000><c> doing</c><00:08:03.199><c> a</c><00:08:03.440><c> project</c><00:08:03.759><c> where</c><00:08:03.840><c> you're</c>

00:08:03.950 --> 00:08:03.960 align:start position:0%
you're doing a project where you're
 

00:08:03.960 --> 00:08:06.430 align:start position:0%
you're doing a project where you're
going<00:08:04.039><c> to</c><00:08:04.159><c> put</c><00:08:04.280><c> an</c><00:08:04.400><c> llm</c><00:08:04.960><c> into</c><00:08:05.360><c> production</c><00:08:06.319><c> and</c>

00:08:06.430 --> 00:08:06.440 align:start position:0%
going to put an llm into production and
 

00:08:06.440 --> 00:08:08.230 align:start position:0%
going to put an llm into production and
you<00:08:06.639><c> realize</c><00:08:07.159><c> that</c><00:08:07.599><c> sometimes</c><00:08:07.800><c> you</c><00:08:07.919><c> need</c><00:08:08.080><c> a</c>

00:08:08.230 --> 00:08:08.240 align:start position:0%
you realize that sometimes you need a
 

00:08:08.240 --> 00:08:09.830 align:start position:0%
you realize that sometimes you need a
really<00:08:08.599><c> strong</c><00:08:08.759><c> model</c><00:08:09.240><c> and</c><00:08:09.400><c> a</c><00:08:09.520><c> lot</c><00:08:09.639><c> of</c><00:08:09.720><c> the</c>

00:08:09.830 --> 00:08:09.840 align:start position:0%
really strong model and a lot of the
 

00:08:09.840 --> 00:08:11.469 align:start position:0%
really strong model and a lot of the
times<00:08:10.000><c> you're</c><00:08:10.159><c> perhaps</c><00:08:10.479><c> not</c><00:08:10.639><c> going</c><00:08:10.720><c> to</c><00:08:10.879><c> need</c><00:08:11.240><c> a</c>

00:08:11.469 --> 00:08:11.479 align:start position:0%
times you're perhaps not going to need a
 

00:08:11.479 --> 00:08:13.670 align:start position:0%
times you're perhaps not going to need a
strong<00:08:11.599><c> model</c><00:08:12.240><c> this</c><00:08:12.400><c> is</c><00:08:12.680><c> definitely</c><00:08:13.199><c> worth</c>

00:08:13.670 --> 00:08:13.680 align:start position:0%
strong model this is definitely worth
 

00:08:13.680 --> 00:08:15.430 align:start position:0%
strong model this is definitely worth
you<00:08:13.800><c> know</c><00:08:14.039><c> considering</c><00:08:14.599><c> and</c><00:08:14.800><c> worth</c><00:08:15.039><c> checking</c>

00:08:15.430 --> 00:08:15.440 align:start position:0%
you know considering and worth checking
 

00:08:15.440 --> 00:08:19.309 align:start position:0%
you know considering and worth checking
out<00:08:15.800><c> here</c><00:08:16.159><c> this</c><00:08:16.560><c> route</c><00:08:16.879><c> llm</c><00:08:17.520><c> from</c><00:08:17.800><c> LM</c><00:08:18.360><c> sis</c><00:08:19.159><c> not</c>

00:08:19.309 --> 00:08:19.319 align:start position:0%
out here this route llm from LM sis not
 

00:08:19.319 --> 00:08:21.790 align:start position:0%
out here this route llm from LM sis not
only<00:08:19.680><c> have</c><00:08:19.919><c> they</c><00:08:20.159><c> released</c><00:08:20.759><c> the</c><00:08:21.039><c> the</c><00:08:21.199><c> code</c><00:08:21.599><c> so</c>

00:08:21.790 --> 00:08:21.800 align:start position:0%
only have they released the the code so
 

00:08:21.800 --> 00:08:23.869 align:start position:0%
only have they released the the code so
you<00:08:21.879><c> can</c><00:08:22.080><c> run</c><00:08:22.360><c> the</c><00:08:22.479><c> models</c><00:08:22.919><c> that</c><00:08:23.120><c> they've</c><00:08:23.479><c> got</c>

00:08:23.869 --> 00:08:23.879 align:start position:0%
you can run the models that they've got
 

00:08:23.879 --> 00:08:25.550 align:start position:0%
you can run the models that they've got
and<00:08:24.080><c> and</c><00:08:24.199><c> use</c><00:08:24.479><c> the</c><00:08:24.599><c> routers</c><00:08:25.039><c> that</c><00:08:25.199><c> they've</c>

00:08:25.550 --> 00:08:25.560 align:start position:0%
and and use the routers that they've
 

00:08:25.560 --> 00:08:28.149 align:start position:0%
and and use the routers that they've
made<00:08:26.319><c> there</c><00:08:26.800><c> they've</c><00:08:27.039><c> also</c><00:08:27.440><c> got</c><00:08:27.840><c> everything</c>

00:08:28.149 --> 00:08:28.159 align:start position:0%
made there they've also got everything
 

00:08:28.159 --> 00:08:29.710 align:start position:0%
made there they've also got everything
that<00:08:28.280><c> you</c><00:08:28.440><c> need</c><00:08:28.680><c> to</c><00:08:28.919><c> basically</c><00:08:29.280><c> make</c><00:08:29.520><c> make</c>

00:08:29.710 --> 00:08:29.720 align:start position:0%
that you need to basically make make
 

00:08:29.720 --> 00:08:31.909 align:start position:0%
that you need to basically make make
your<00:08:29.879><c> own</c><00:08:30.240><c> routers</c><00:08:31.199><c> and</c><00:08:31.280><c> we</c><00:08:31.400><c> can</c><00:08:31.520><c> see</c><00:08:31.759><c> that</c>

00:08:31.909 --> 00:08:31.919 align:start position:0%
your own routers and we can see that
 

00:08:31.919 --> 00:08:34.829 align:start position:0%
your own routers and we can see that
they've<00:08:32.240><c> released</c><00:08:32.760><c> all</c><00:08:32.959><c> the</c><00:08:33.120><c> models</c><00:08:34.039><c> as</c><00:08:34.320><c> well</c>

00:08:34.829 --> 00:08:34.839 align:start position:0%
they've released all the models as well
 

00:08:34.839 --> 00:08:38.029 align:start position:0%
they've released all the models as well
as<00:08:35.080><c> the</c><00:08:35.240><c> data</c><00:08:35.599><c> sets</c><00:08:36.159><c> on</c><00:08:36.360><c> hugging</c><00:08:36.839><c> face</c><00:08:37.200><c> here</c><00:08:37.560><c> so</c>

00:08:38.029 --> 00:08:38.039 align:start position:0%
as the data sets on hugging face here so
 

00:08:38.039 --> 00:08:40.430 align:start position:0%
as the data sets on hugging face here so
this<00:08:38.320><c> allows</c><00:08:38.719><c> you</c><00:08:38.959><c> to</c><00:08:39.320><c> basically</c><00:08:40.039><c> look</c><00:08:40.240><c> at</c>

00:08:40.430 --> 00:08:40.440 align:start position:0%
this allows you to basically look at
 

00:08:40.440 --> 00:08:42.909 align:start position:0%
this allows you to basically look at
these<00:08:40.680><c> yourself</c><00:08:41.200><c> and</c><00:08:41.360><c> try</c><00:08:41.640><c> them</c><00:08:41.880><c> out</c><00:08:42.159><c> yourself</c>

00:08:42.909 --> 00:08:42.919 align:start position:0%
these yourself and try them out yourself
 

00:08:42.919 --> 00:08:44.430 align:start position:0%
these yourself and try them out yourself
imagine<00:08:43.279><c> the</c><00:08:43.440><c> open</c><00:08:43.680><c> source</c><00:08:43.919><c> Community</c><00:08:44.279><c> will</c>

00:08:44.430 --> 00:08:44.440 align:start position:0%
imagine the open source Community will
 

00:08:44.440 --> 00:08:46.990 align:start position:0%
imagine the open source Community will
be<00:08:44.560><c> able</c><00:08:44.760><c> to</c><00:08:44.959><c> build</c><00:08:45.240><c> on</c><00:08:45.519><c> this</c><00:08:46.279><c> and</c><00:08:46.680><c> come</c><00:08:46.839><c> up</c>

00:08:46.990 --> 00:08:47.000 align:start position:0%
be able to build on this and come up
 

00:08:47.000 --> 00:08:48.430 align:start position:0%
be able to build on this and come up
with<00:08:47.160><c> things</c><00:08:47.360><c> of</c><00:08:47.560><c> where</c><00:08:47.880><c> you're</c><00:08:48.040><c> comparing</c>

00:08:48.430 --> 00:08:48.440 align:start position:0%
with things of where you're comparing
 

00:08:48.440 --> 00:08:49.910 align:start position:0%
with things of where you're comparing
all<00:08:48.600><c> the</c><00:08:48.760><c> different</c><00:08:49.080><c> popular</c><00:08:49.440><c> models</c><00:08:49.800><c> that</c>

00:08:49.910 --> 00:08:49.920 align:start position:0%
all the different popular models that
 

00:08:49.920 --> 00:08:51.870 align:start position:0%
all the different popular models that
are<00:08:50.080><c> out</c><00:08:50.360><c> there</c><00:08:50.920><c> so</c><00:08:51.080><c> in</c><00:08:51.200><c> some</c><00:08:51.360><c> ways</c><00:08:51.600><c> perhaps</c>

00:08:51.870 --> 00:08:51.880 align:start position:0%
are out there so in some ways perhaps
 

00:08:51.880 --> 00:08:54.269 align:start position:0%
are out there so in some ways perhaps
this<00:08:51.959><c> is</c><00:08:52.080><c> not</c><00:08:52.240><c> a</c><00:08:52.440><c> super</c><00:08:52.760><c> sexy</c><00:08:53.519><c> topic</c><00:08:54.000><c> but</c><00:08:54.200><c> if</c>

00:08:54.269 --> 00:08:54.279 align:start position:0%
this is not a super sexy topic but if
 

00:08:54.279 --> 00:08:55.430 align:start position:0%
this is not a super sexy topic but if
you<00:08:54.360><c> do</c><00:08:54.560><c> anything</c><00:08:54.839><c> where</c><00:08:54.959><c> you're</c><00:08:55.080><c> spending</c>

00:08:55.430 --> 00:08:55.440 align:start position:0%
you do anything where you're spending
 

00:08:55.440 --> 00:08:58.069 align:start position:0%
you do anything where you're spending
quite<00:08:55.560><c> a</c><00:08:55.680><c> bit</c><00:08:55.800><c> of</c><00:08:55.920><c> money</c><00:08:56.360><c> on</c><00:08:57.080><c> tokens</c><00:08:57.680><c> this</c><00:08:57.800><c> is</c>

00:08:58.069 --> 00:08:58.079 align:start position:0%
quite a bit of money on tokens this is
 

00:08:58.079 --> 00:08:59.670 align:start position:0%
quite a bit of money on tokens this is
definitely<00:08:58.560><c> something</c><00:08:58.920><c> that</c><00:08:59.000><c> you</c><00:08:59.079><c> should</c><00:08:59.279><c> be</c>

00:08:59.670 --> 00:08:59.680 align:start position:0%
definitely something that you should be
 

00:08:59.680 --> 00:09:01.910 align:start position:0%
definitely something that you should be
checking<00:09:00.000><c> out</c><00:09:00.480><c> for</c><00:09:00.680><c> using</c><00:09:01.000><c> in</c><00:09:01.279><c> production</c><00:09:01.680><c> and</c>

00:09:01.910 --> 00:09:01.920 align:start position:0%
checking out for using in production and
 

00:09:01.920 --> 00:09:04.590 align:start position:0%
checking out for using in production and
stuff<00:09:02.920><c> all</c><00:09:03.079><c> right</c><00:09:03.560><c> as</c><00:09:03.720><c> always</c><00:09:04.040><c> please</c><00:09:04.320><c> put</c><00:09:04.480><c> you</c>

00:09:04.590 --> 00:09:04.600 align:start position:0%
stuff all right as always please put you
 

00:09:04.600 --> 00:09:06.790 align:start position:0%
stuff all right as always please put you
know<00:09:04.760><c> any</c><00:09:05.000><c> comments</c><00:09:05.440><c> and</c><00:09:05.680><c> questions</c><00:09:06.399><c> in</c><00:09:06.560><c> the</c>

00:09:06.790 --> 00:09:06.800 align:start position:0%
know any comments and questions in the
 

00:09:06.800 --> 00:09:08.829 align:start position:0%
know any comments and questions in the
the<00:09:06.920><c> comments</c><00:09:07.279><c> below</c><00:09:08.279><c> if</c><00:09:08.399><c> you</c><00:09:08.519><c> found</c><00:09:08.680><c> the</c>

00:09:08.829 --> 00:09:08.839 align:start position:0%
the comments below if you found the
 

00:09:08.839 --> 00:09:10.590 align:start position:0%
the comments below if you found the
video<00:09:09.079><c> useful</c><00:09:09.560><c> please</c><00:09:09.800><c> click</c><00:09:10.079><c> like</c><00:09:10.279><c> And</c>

00:09:10.590 --> 00:09:10.600 align:start position:0%
video useful please click like And
 

00:09:10.600 --> 00:09:12.630 align:start position:0%
video useful please click like And
subscribe<00:09:11.600><c> and</c><00:09:11.800><c> I</c><00:09:11.920><c> will</c><00:09:12.079><c> talk</c><00:09:12.240><c> to</c><00:09:12.320><c> you</c><00:09:12.440><c> in</c><00:09:12.519><c> the</c>

00:09:12.630 --> 00:09:12.640 align:start position:0%
subscribe and I will talk to you in the
 

00:09:12.640 --> 00:09:16.920 align:start position:0%
subscribe and I will talk to you in the
next<00:09:12.880><c> video</c><00:09:13.519><c> bye</c><00:09:13.720><c> for</c><00:09:13.920><c> now</c>

