WEBVTT
Kind: captions
Language: en

00:00:00.060 --> 00:00:02.330 align:start position:0%
 
hey<00:00:00.539><c> coders</c><00:00:01.079><c> and</c><00:00:01.319><c> welcome</c><00:00:01.560><c> to</c><00:00:01.860><c> the</c><00:00:02.040><c> bucket</c>

00:00:02.330 --> 00:00:02.340 align:start position:0%
hey coders and welcome to the bucket
 

00:00:02.340 --> 00:00:04.130 align:start position:0%
hey coders and welcome to the bucket
sort<00:00:02.520><c> explanation</c><00:00:03.120><c> I'm</c><00:00:03.720><c> just</c><00:00:03.899><c> going</c><00:00:03.959><c> to</c><00:00:04.020><c> take</c>

00:00:04.130 --> 00:00:04.140 align:start position:0%
sort explanation I'm just going to take
 

00:00:04.140 --> 00:00:05.510 align:start position:0%
sort explanation I'm just going to take
a<00:00:04.259><c> minute</c><00:00:04.380><c> to</c><00:00:04.620><c> explain</c><00:00:04.860><c> a</c><00:00:05.100><c> couple</c><00:00:05.220><c> things</c>

00:00:05.510 --> 00:00:05.520 align:start position:0%
a minute to explain a couple things
 

00:00:05.520 --> 00:00:07.610 align:start position:0%
a minute to explain a couple things
before<00:00:05.819><c> we</c><00:00:06.120><c> get</c><00:00:06.180><c> to</c><00:00:06.299><c> a</c><00:00:06.420><c> visualization</c><00:00:07.080><c> example</c>

00:00:07.610 --> 00:00:07.620 align:start position:0%
before we get to a visualization example
 

00:00:07.620 --> 00:00:09.770 align:start position:0%
before we get to a visualization example
of<00:00:07.799><c> how</c><00:00:08.040><c> it</c><00:00:08.160><c> works</c><00:00:08.460><c> bugasort</c><00:00:09.420><c> is</c><00:00:09.599><c> different</c>

00:00:09.770 --> 00:00:09.780 align:start position:0%
of how it works bugasort is different
 

00:00:09.780 --> 00:00:11.690 align:start position:0%
of how it works bugasort is different
because<00:00:10.019><c> it</c><00:00:10.380><c> doesn't</c><00:00:10.559><c> just</c><00:00:10.860><c> compare</c><00:00:11.280><c> elements</c>

00:00:11.690 --> 00:00:11.700 align:start position:0%
because it doesn't just compare elements
 

00:00:11.700 --> 00:00:13.970 align:start position:0%
because it doesn't just compare elements
at<00:00:11.880><c> least</c><00:00:12.059><c> right</c><00:00:12.240><c> away</c><00:00:12.420><c> instead</c><00:00:13.139><c> we</c><00:00:13.559><c> create</c><00:00:13.740><c> a</c>

00:00:13.970 --> 00:00:13.980 align:start position:0%
at least right away instead we create a
 

00:00:13.980 --> 00:00:16.070 align:start position:0%
at least right away instead we create a
set<00:00:14.099><c> number</c><00:00:14.280><c> of</c><00:00:14.460><c> buckets</c><00:00:14.880><c> which</c><00:00:15.420><c> are</c><00:00:15.660><c> just</c><00:00:15.900><c> a</c>

00:00:16.070 --> 00:00:16.080 align:start position:0%
set number of buckets which are just a
 

00:00:16.080 --> 00:00:17.570 align:start position:0%
set number of buckets which are just a
data<00:00:16.379><c> structure</c><00:00:16.680><c> themselves</c><00:00:16.980><c> such</c><00:00:17.279><c> as</c><00:00:17.400><c> an</c>

00:00:17.570 --> 00:00:17.580 align:start position:0%
data structure themselves such as an
 

00:00:17.580 --> 00:00:19.910 align:start position:0%
data structure themselves such as an
array<00:00:17.880><c> or</c><00:00:18.060><c> linked</c><00:00:18.420><c> list</c><00:00:18.539><c> typically</c><00:00:19.199><c> you</c><00:00:19.740><c> will</c>

00:00:19.910 --> 00:00:19.920 align:start position:0%
array or linked list typically you will
 

00:00:19.920 --> 00:00:21.950 align:start position:0%
array or linked list typically you will
hear<00:00:20.160><c> or</c><00:00:20.400><c> you'll</c><00:00:20.699><c> see</c><00:00:20.939><c> you</c><00:00:21.300><c> just</c><00:00:21.480><c> create</c><00:00:21.660><c> 10</c>

00:00:21.950 --> 00:00:21.960 align:start position:0%
hear or you'll see you just create 10
 

00:00:21.960 --> 00:00:24.470 align:start position:0%
hear or you'll see you just create 10
buckets<00:00:22.439><c> I'll</c><00:00:22.800><c> do</c><00:00:23.039><c> in</c><00:00:23.160><c> my</c><00:00:23.279><c> example</c><00:00:23.580><c> but</c><00:00:24.240><c> one</c>

00:00:24.470 --> 00:00:24.480 align:start position:0%
buckets I'll do in my example but one
 

00:00:24.480 --> 00:00:26.570 align:start position:0%
buckets I'll do in my example but one
thing<00:00:24.600><c> to</c><00:00:24.840><c> note</c><00:00:24.960><c> is</c><00:00:25.680><c> you</c><00:00:25.920><c> don't</c><00:00:26.100><c> want</c><00:00:26.460><c> to</c>

00:00:26.570 --> 00:00:26.580 align:start position:0%
thing to note is you don't want to
 

00:00:26.580 --> 00:00:28.550 align:start position:0%
thing to note is you don't want to
create<00:00:26.760><c> a</c><00:00:27.180><c> number</c><00:00:27.300><c> of</c><00:00:27.420><c> buckets</c><00:00:27.779><c> based</c><00:00:28.260><c> on</c><00:00:28.320><c> how</c>

00:00:28.550 --> 00:00:28.560 align:start position:0%
create a number of buckets based on how
 

00:00:28.560 --> 00:00:30.890 align:start position:0%
create a number of buckets based on how
many<00:00:28.680><c> elements</c><00:00:29.220><c> there</c><00:00:29.519><c> are</c><00:00:29.699><c> in</c><00:00:30.060><c> the</c><00:00:30.180><c> array</c><00:00:30.480><c> if</c>

00:00:30.890 --> 00:00:30.900 align:start position:0%
many elements there are in the array if
 

00:00:30.900 --> 00:00:32.749 align:start position:0%
many elements there are in the array if
we<00:00:31.019><c> do</c><00:00:31.140><c> that</c><00:00:31.320><c> then</c><00:00:31.679><c> we're</c><00:00:31.920><c> really</c><00:00:32.220><c> just</c><00:00:32.460><c> doing</c>

00:00:32.749 --> 00:00:32.759 align:start position:0%
we do that then we're really just doing
 

00:00:32.759 --> 00:00:34.790 align:start position:0%
we do that then we're really just doing
a<00:00:33.120><c> counting</c><00:00:33.540><c> sort</c><00:00:33.780><c> instead</c><00:00:34.320><c> we</c><00:00:34.500><c> want</c><00:00:34.620><c> the</c>

00:00:34.790 --> 00:00:34.800 align:start position:0%
a counting sort instead we want the
 

00:00:34.800 --> 00:00:36.650 align:start position:0%
a counting sort instead we want the
buckets<00:00:35.100><c> to</c><00:00:35.280><c> hold</c><00:00:35.399><c> a</c><00:00:35.579><c> range</c><00:00:35.940><c> of</c><00:00:36.059><c> values</c><00:00:36.420><c> like</c>

00:00:36.650 --> 00:00:36.660 align:start position:0%
buckets to hold a range of values like
 

00:00:36.660 --> 00:00:38.750 align:start position:0%
buckets to hold a range of values like
you<00:00:36.840><c> see</c><00:00:36.960><c> in</c><00:00:37.079><c> this</c><00:00:37.260><c> image</c><00:00:37.559><c> which</c><00:00:38.219><c> I'll</c><00:00:38.520><c> show</c>

00:00:38.750 --> 00:00:38.760 align:start position:0%
you see in this image which I'll show
 

00:00:38.760 --> 00:00:41.150 align:start position:0%
you see in this image which I'll show
you<00:00:38.880><c> more</c><00:00:39.059><c> of</c><00:00:39.180><c> the</c><00:00:39.719><c> idea</c><00:00:40.079><c> is</c><00:00:40.379><c> to</c><00:00:40.620><c> store</c><00:00:40.800><c> values</c>

00:00:41.150 --> 00:00:41.160 align:start position:0%
you more of the idea is to store values
 

00:00:41.160 --> 00:00:43.729 align:start position:0%
you more of the idea is to store values
into<00:00:41.280><c> buckets</c><00:00:41.760><c> sort</c><00:00:42.600><c> each</c><00:00:42.960><c> of</c><00:00:43.079><c> those</c><00:00:43.320><c> buckets</c>

00:00:43.729 --> 00:00:43.739 align:start position:0%
into buckets sort each of those buckets
 

00:00:43.739 --> 00:00:46.729 align:start position:0%
into buckets sort each of those buckets
and<00:00:44.520><c> then</c><00:00:44.700><c> append</c><00:00:45.180><c> those</c><00:00:45.780><c> values</c><00:00:46.320><c> or</c><00:00:46.559><c> the</c>

00:00:46.729 --> 00:00:46.739 align:start position:0%
and then append those values or the
 

00:00:46.739 --> 00:00:48.410 align:start position:0%
and then append those values or the
buckets<00:00:47.100><c> back</c><00:00:47.460><c> into</c><00:00:47.579><c> the</c><00:00:47.820><c> original</c><00:00:47.879><c> array</c>

00:00:48.410 --> 00:00:48.420 align:start position:0%
buckets back into the original array
 

00:00:48.420 --> 00:00:50.209 align:start position:0%
buckets back into the original array
let's<00:00:48.780><c> look</c><00:00:48.899><c> at</c><00:00:49.020><c> an</c><00:00:49.140><c> example</c><00:00:49.379><c> here</c><00:00:49.860><c> we</c><00:00:50.039><c> have</c><00:00:50.100><c> an</c>

00:00:50.209 --> 00:00:50.219 align:start position:0%
let's look at an example here we have an
 

00:00:50.219 --> 00:00:52.369 align:start position:0%
let's look at an example here we have an
array<00:00:50.460><c> that</c><00:00:50.700><c> holds</c><00:00:51.000><c> 10</c><00:00:51.239><c> elements</c>

00:00:52.369 --> 00:00:52.379 align:start position:0%
array that holds 10 elements
 

00:00:52.379 --> 00:00:54.650 align:start position:0%
array that holds 10 elements
and<00:00:52.920><c> for</c><00:00:53.100><c> this</c><00:00:53.280><c> example</c><00:00:53.700><c> we're</c><00:00:54.180><c> just</c><00:00:54.360><c> going</c><00:00:54.480><c> to</c>

00:00:54.650 --> 00:00:54.660 align:start position:0%
and for this example we're just going to
 

00:00:54.660 --> 00:00:57.049 align:start position:0%
and for this example we're just going to
create<00:00:54.840><c> 10</c><00:00:55.260><c> bucks</c><00:00:55.500><c> that</c><00:00:55.860><c> hold</c><00:00:56.100><c> a</c><00:00:56.460><c> range</c><00:00:56.820><c> of</c>

00:00:57.049 --> 00:00:57.059 align:start position:0%
create 10 bucks that hold a range of
 

00:00:57.059 --> 00:00:58.369 align:start position:0%
create 10 bucks that hold a range of
values

00:00:58.369 --> 00:00:58.379 align:start position:0%
values
 

00:00:58.379 --> 00:01:01.549 align:start position:0%
values
so<00:00:58.800><c> in</c><00:00:59.039><c> blue</c><00:00:59.280><c> are</c><00:00:59.760><c> buckets</c><00:01:00.239><c> they're</c><00:01:01.199><c> going</c><00:01:01.379><c> to</c>

00:01:01.549 --> 00:01:01.559 align:start position:0%
so in blue are buckets they're going to
 

00:01:01.559 --> 00:01:03.410 align:start position:0%
so in blue are buckets they're going to
hold<00:01:01.860><c> a</c><00:01:02.039><c> range</c><00:01:02.219><c> of</c><00:01:02.340><c> values</c><00:01:02.640><c> the</c><00:01:02.940><c> first</c><00:01:03.059><c> one</c><00:01:03.239><c> or</c>

00:01:03.410 --> 00:01:03.420 align:start position:0%
hold a range of values the first one or
 

00:01:03.420 --> 00:01:05.030 align:start position:0%
hold a range of values the first one or
buck<00:01:03.600><c> zero</c><00:01:04.080><c> is</c><00:01:04.199><c> going</c><00:01:04.320><c> to</c><00:01:04.379><c> hold</c><00:01:04.500><c> zero</c><00:01:04.920><c> through</c>

00:01:05.030 --> 00:01:05.040 align:start position:0%
buck zero is going to hold zero through
 

00:01:05.040 --> 00:01:07.490 align:start position:0%
buck zero is going to hold zero through
nine<00:01:05.339><c> the</c><00:01:05.939><c> next</c><00:01:06.180><c> one</c><00:01:06.360><c> values</c><00:01:06.900><c> 10</c><00:01:07.140><c> through</c><00:01:07.320><c> 19</c>

00:01:07.490 --> 00:01:07.500 align:start position:0%
nine the next one values 10 through 19
 

00:01:07.500 --> 00:01:10.190 align:start position:0%
nine the next one values 10 through 19
and<00:01:08.159><c> so</c><00:01:08.400><c> forth</c><00:01:08.820><c> now</c><00:01:09.299><c> what</c><00:01:09.479><c> we'll</c><00:01:09.600><c> do</c><00:01:09.780><c> is</c><00:01:10.020><c> we</c>

00:01:10.190 --> 00:01:10.200 align:start position:0%
and so forth now what we'll do is we
 

00:01:10.200 --> 00:01:12.109 align:start position:0%
and so forth now what we'll do is we
iterate<00:01:10.619><c> through</c><00:01:10.799><c> each</c><00:01:11.040><c> value</c><00:01:11.340><c> in</c><00:01:11.700><c> the</c><00:01:11.820><c> array</c>

00:01:12.109 --> 00:01:12.119 align:start position:0%
iterate through each value in the array
 

00:01:12.119 --> 00:01:14.149 align:start position:0%
iterate through each value in the array
insert<00:01:12.840><c> them</c><00:01:13.020><c> into</c><00:01:13.140><c> a</c><00:01:13.439><c> function</c><00:01:13.680><c> that</c><00:01:14.040><c> will</c>

00:01:14.149 --> 00:01:14.159 align:start position:0%
insert them into a function that will
 

00:01:14.159 --> 00:01:16.910 align:start position:0%
insert them into a function that will
determine<00:01:14.580><c> which</c><00:01:15.000><c> bucket</c><00:01:15.360><c> it</c><00:01:15.540><c> will</c><00:01:15.720><c> go</c><00:01:15.900><c> into</c>

00:01:16.910 --> 00:01:16.920 align:start position:0%
determine which bucket it will go into
 

00:01:16.920 --> 00:01:20.390 align:start position:0%
determine which bucket it will go into
for<00:01:17.520><c> example</c><00:01:17.820><c> the</c><00:01:18.420><c> first</c><00:01:18.540><c> value</c><00:01:18.780><c> 85</c><00:01:19.619><c> when</c>

00:01:20.390 --> 00:01:20.400 align:start position:0%
for example the first value 85 when
 

00:01:20.400 --> 00:01:22.850 align:start position:0%
for example the first value 85 when
inserted<00:01:20.939><c> into</c><00:01:21.060><c> this</c><00:01:21.360><c> function</c><00:01:21.720><c> is</c><00:01:22.380><c> going</c><00:01:22.680><c> to</c>

00:01:22.850 --> 00:01:22.860 align:start position:0%
inserted into this function is going to
 

00:01:22.860 --> 00:01:26.630 align:start position:0%
inserted into this function is going to
go<00:01:23.159><c> to</c><00:01:23.700><c> bucket</c><00:01:24.180><c> 8.</c>

00:01:26.630 --> 00:01:26.640 align:start position:0%
go to bucket 8.
 

00:01:26.640 --> 00:01:29.450 align:start position:0%
go to bucket 8.
then<00:01:27.060><c> 14</c><00:01:27.360><c> is</c><00:01:27.659><c> going</c><00:01:27.780><c> to</c><00:01:27.840><c> go</c><00:01:27.900><c> into</c><00:01:28.020><c> bucket</c><00:01:28.500><c> one</c>

00:01:29.450 --> 00:01:29.460 align:start position:0%
then 14 is going to go into bucket one
 

00:01:29.460 --> 00:01:32.330 align:start position:0%
then 14 is going to go into bucket one
or<00:01:29.820><c> in</c><00:01:30.000><c> a</c><00:01:30.119><c> bucket</c><00:01:30.420><c> 0</c><00:01:30.680><c> and</c><00:01:31.680><c> we're</c><00:01:31.920><c> going</c><00:01:32.100><c> to</c><00:01:32.220><c> keep</c>

00:01:32.330 --> 00:01:32.340 align:start position:0%
or in a bucket 0 and we're going to keep
 

00:01:32.340 --> 00:01:34.370 align:start position:0%
or in a bucket 0 and we're going to keep
doing<00:01:32.640><c> this</c><00:01:32.880><c> as</c><00:01:33.360><c> you</c><00:01:33.540><c> see</c><00:01:33.659><c> when</c><00:01:33.840><c> we</c><00:01:34.020><c> get</c><00:01:34.140><c> to</c>

00:01:34.370 --> 00:01:34.380 align:start position:0%
doing this as you see when we get to
 

00:01:34.380 --> 00:01:37.010 align:start position:0%
doing this as you see when we get to
Value<00:01:34.979><c> one</c><00:01:35.280><c> it's</c><00:01:35.700><c> also</c><00:01:36.180><c> going</c><00:01:36.420><c> to</c><00:01:36.600><c> go</c><00:01:36.840><c> into</c>

00:01:37.010 --> 00:01:37.020 align:start position:0%
Value one it's also going to go into
 

00:01:37.020 --> 00:01:39.469 align:start position:0%
Value one it's also going to go into
bucket<00:01:37.560><c> zero</c><00:01:37.920><c> and</c><00:01:38.280><c> we</c><00:01:38.460><c> just</c><00:01:38.579><c> append</c><00:01:39.000><c> it</c><00:01:39.119><c> to</c><00:01:39.299><c> the</c>

00:01:39.469 --> 00:01:39.479 align:start position:0%
bucket zero and we just append it to the
 

00:01:39.479 --> 00:01:41.390 align:start position:0%
bucket zero and we just append it to the
end<00:01:39.659><c> of</c><00:01:39.960><c> the</c><00:01:40.200><c> array</c><00:01:40.439><c> or</c><00:01:40.619><c> linked</c><00:01:40.979><c> list</c><00:01:41.040><c> whatever</c>

00:01:41.390 --> 00:01:41.400 align:start position:0%
end of the array or linked list whatever
 

00:01:41.400 --> 00:01:43.190 align:start position:0%
end of the array or linked list whatever
structure<00:01:41.939><c> you</c><00:01:42.000><c> want</c><00:01:42.119><c> to</c><00:01:42.180><c> use</c><00:01:42.299><c> and</c><00:01:42.780><c> we'll</c><00:01:43.020><c> do</c>

00:01:43.190 --> 00:01:43.200 align:start position:0%
structure you want to use and we'll do
 

00:01:43.200 --> 00:01:46.249 align:start position:0%
structure you want to use and we'll do
this<00:01:43.320><c> for</c><00:01:43.560><c> the</c><00:01:43.680><c> rest</c><00:01:43.799><c> of</c><00:01:43.920><c> them</c>

00:01:46.249 --> 00:01:46.259 align:start position:0%
 
 

00:01:46.259 --> 00:01:49.609 align:start position:0%
 
then<00:01:47.100><c> once</c><00:01:47.640><c> we</c><00:01:47.820><c> have</c><00:01:48.000><c> them</c><00:01:48.240><c> in</c><00:01:48.540><c> the</c><00:01:48.659><c> buckets</c><00:01:49.020><c> we</c>

00:01:49.609 --> 00:01:49.619 align:start position:0%
then once we have them in the buckets we
 

00:01:49.619 --> 00:01:53.090 align:start position:0%
then once we have them in the buckets we
want<00:01:49.979><c> to</c><00:01:50.100><c> sort</c><00:01:50.700><c> each</c><00:01:51.360><c> of</c><00:01:51.659><c> these</c><00:01:52.020><c> buckets</c><00:01:52.380><c> you</c>

00:01:53.090 --> 00:01:53.100 align:start position:0%
want to sort each of these buckets you
 

00:01:53.100 --> 00:01:55.069 align:start position:0%
want to sort each of these buckets you
can<00:01:53.220><c> do</c><00:01:53.399><c> something</c><00:01:53.579><c> like</c><00:01:53.939><c> insertion</c><00:01:54.479><c> sort</c><00:01:54.840><c> or</c>

00:01:55.069 --> 00:01:55.079 align:start position:0%
can do something like insertion sort or
 

00:01:55.079 --> 00:01:56.630 align:start position:0%
can do something like insertion sort or
merge<00:01:55.500><c> sort</c><00:01:55.680><c> or</c><00:01:55.860><c> whatever</c><00:01:56.040><c> you</c><00:01:56.280><c> want</c><00:01:56.399><c> to</c><00:01:56.520><c> do</c>

00:01:56.630 --> 00:01:56.640 align:start position:0%
merge sort or whatever you want to do
 

00:01:56.640 --> 00:01:59.149 align:start position:0%
merge sort or whatever you want to do
but<00:01:57.180><c> this</c><00:01:57.540><c> at</c><00:01:57.840><c> this</c><00:01:58.020><c> step</c><00:01:58.200><c> we</c><00:01:58.560><c> just</c><00:01:58.740><c> sort</c><00:01:58.920><c> them</c>

00:01:59.149 --> 00:01:59.159 align:start position:0%
but this at this step we just sort them
 

00:01:59.159 --> 00:02:01.370 align:start position:0%
but this at this step we just sort them
then<00:01:59.520><c> once</c><00:01:59.820><c> we're</c><00:01:59.939><c> done</c><00:02:00.119><c> with</c><00:02:00.299><c> that</c><00:02:00.479><c> we</c><00:02:01.259><c> want</c>

00:02:01.370 --> 00:02:01.380 align:start position:0%
then once we're done with that we want
 

00:02:01.380 --> 00:02:03.710 align:start position:0%
then once we're done with that we want
to<00:02:01.560><c> start</c><00:02:01.619><c> at</c><00:02:01.799><c> bucket</c><00:02:02.220><c> zero</c><00:02:02.579><c> and</c><00:02:02.880><c> append</c><00:02:03.299><c> each</c>

00:02:03.710 --> 00:02:03.720 align:start position:0%
to start at bucket zero and append each
 

00:02:03.720 --> 00:02:06.770 align:start position:0%
to start at bucket zero and append each
value<00:02:04.079><c> in</c><00:02:04.860><c> these</c><00:02:05.520><c> buckets</c><00:02:05.880><c> back</c><00:02:06.299><c> to</c><00:02:06.540><c> the</c>

00:02:06.770 --> 00:02:06.780 align:start position:0%
value in these buckets back to the
 

00:02:06.780 --> 00:02:09.710 align:start position:0%
value in these buckets back to the
original<00:02:06.960><c> array</c>

00:02:09.710 --> 00:02:09.720 align:start position:0%
original array
 

00:02:09.720 --> 00:02:12.830 align:start position:0%
original array
again<00:02:10.619><c> something</c><00:02:11.160><c> to</c><00:02:11.520><c> note</c><00:02:11.640><c> is</c><00:02:12.360><c> if</c><00:02:12.480><c> we</c><00:02:12.660><c> had</c>

00:02:12.830 --> 00:02:12.840 align:start position:0%
again something to note is if we had
 

00:02:12.840 --> 00:02:15.290 align:start position:0%
again something to note is if we had
5000<00:02:13.500><c> elements</c><00:02:13.860><c> in</c><00:02:13.980><c> this</c><00:02:14.099><c> array</c><00:02:14.400><c> I</c><00:02:15.060><c> don't</c><00:02:15.180><c> want</c>

00:02:15.290 --> 00:02:15.300 align:start position:0%
5000 elements in this array I don't want
 

00:02:15.300 --> 00:02:17.510 align:start position:0%
5000 elements in this array I don't want
to<00:02:15.360><c> create</c><00:02:15.480><c> 5000</c><00:02:16.020><c> buckets</c><00:02:16.379><c> maybe</c><00:02:17.099><c> I</c><00:02:17.340><c> still</c>

00:02:17.510 --> 00:02:17.520 align:start position:0%
to create 5000 buckets maybe I still
 

00:02:17.520 --> 00:02:19.970 align:start position:0%
to create 5000 buckets maybe I still
only<00:02:17.760><c> create</c><00:02:18.060><c> 10</c><00:02:18.360><c> buckets</c><00:02:18.840><c> because</c><00:02:19.440><c> if</c><00:02:19.800><c> I</c>

00:02:19.970 --> 00:02:19.980 align:start position:0%
only create 10 buckets because if I
 

00:02:19.980 --> 00:02:21.830 align:start position:0%
only create 10 buckets because if I
create<00:02:20.160><c> the</c><00:02:20.459><c> same</c><00:02:20.640><c> number</c><00:02:20.819><c> of</c><00:02:21.000><c> buckets</c><00:02:21.360><c> as</c>

00:02:21.830 --> 00:02:21.840 align:start position:0%
create the same number of buckets as
 

00:02:21.840 --> 00:02:24.650 align:start position:0%
create the same number of buckets as
there<00:02:22.140><c> are</c><00:02:22.520><c> values</c><00:02:23.520><c> in</c><00:02:23.700><c> the</c><00:02:23.819><c> array</c><00:02:24.120><c> then</c><00:02:24.420><c> this</c>

00:02:24.650 --> 00:02:24.660 align:start position:0%
there are values in the array then this
 

00:02:24.660 --> 00:02:27.470 align:start position:0%
there are values in the array then this
is<00:02:24.780><c> essentially</c><00:02:25.319><c> counting</c><00:02:26.040><c> sort</c><00:02:26.340><c> not</c><00:02:26.940><c> bucket</c>

00:02:27.470 --> 00:02:27.480 align:start position:0%
is essentially counting sort not bucket
 

00:02:27.480 --> 00:02:29.510 align:start position:0%
is essentially counting sort not bucket
sort<00:02:27.720><c> all</c><00:02:28.500><c> right</c><00:02:28.680><c> here</c><00:02:28.920><c> is</c><00:02:29.040><c> the</c><00:02:29.220><c> coding</c>

00:02:29.510 --> 00:02:29.520 align:start position:0%
sort all right here is the coding
 

00:02:29.520 --> 00:02:31.190 align:start position:0%
sort all right here is the coding
portion<00:02:29.819><c> so</c><00:02:30.120><c> let's</c><00:02:30.360><c> just</c><00:02:30.540><c> go</c><00:02:30.660><c> and</c><00:02:30.780><c> get</c><00:02:30.959><c> started</c>

00:02:31.190 --> 00:02:31.200 align:start position:0%
portion so let's just go and get started
 

00:02:31.200 --> 00:02:33.650 align:start position:0%
portion so let's just go and get started
with<00:02:31.319><c> bucket</c><00:02:31.620><c> sort</c><00:02:31.920><c> I</c><00:02:32.760><c> have</c><00:02:32.940><c> a</c><00:02:33.239><c> method</c><00:02:33.480><c> called</c>

00:02:33.650 --> 00:02:33.660 align:start position:0%
with bucket sort I have a method called
 

00:02:33.660 --> 00:02:35.930 align:start position:0%
with bucket sort I have a method called
apply<00:02:34.020><c> sort</c><00:02:34.260><c> where</c><00:02:34.500><c> we</c><00:02:34.680><c> take</c><00:02:34.920><c> in</c><00:02:35.160><c> the</c><00:02:35.640><c> array</c>

00:02:35.930 --> 00:02:35.940 align:start position:0%
apply sort where we take in the array
 

00:02:35.940 --> 00:02:38.150 align:start position:0%
apply sort where we take in the array
and<00:02:36.180><c> the</c><00:02:36.360><c> bucket</c><00:02:36.660><c> size</c><00:02:36.840><c> typically</c><00:02:37.680><c> the</c><00:02:37.860><c> bucket</c>

00:02:38.150 --> 00:02:38.160 align:start position:0%
and the bucket size typically the bucket
 

00:02:38.160 --> 00:02:40.610 align:start position:0%
and the bucket size typically the bucket
size<00:02:38.280><c> that</c><00:02:38.520><c> you're</c><00:02:38.580><c> going</c><00:02:38.760><c> to</c><00:02:38.819><c> see</c><00:02:38.940><c> is</c><00:02:39.300><c> 10</c><00:02:39.720><c> so</c><00:02:40.260><c> I</c>

00:02:40.610 --> 00:02:40.620 align:start position:0%
size that you're going to see is 10 so I
 

00:02:40.620 --> 00:02:42.589 align:start position:0%
size that you're going to see is 10 so I
just<00:02:40.860><c> have</c><00:02:41.280><c> that</c><00:02:41.459><c> as</c><00:02:41.580><c> a</c><00:02:41.700><c> parameter</c><00:02:42.120><c> so</c><00:02:42.360><c> that</c><00:02:42.480><c> we</c>

00:02:42.589 --> 00:02:42.599 align:start position:0%
just have that as a parameter so that we
 

00:02:42.599 --> 00:02:44.449 align:start position:0%
just have that as a parameter so that we
can<00:02:42.720><c> set</c><00:02:42.900><c> it</c><00:02:43.019><c> to</c><00:02:43.140><c> 10</c><00:02:43.379><c> and</c><00:02:43.680><c> use</c><00:02:43.860><c> that</c><00:02:44.099><c> throughout</c>

00:02:44.449 --> 00:02:44.459 align:start position:0%
can set it to 10 and use that throughout
 

00:02:44.459 --> 00:02:46.850 align:start position:0%
can set it to 10 and use that throughout
this<00:02:44.700><c> method</c><00:02:45.060><c> the</c><00:02:45.840><c> first</c><00:02:45.959><c> thing</c><00:02:46.140><c> we</c><00:02:46.319><c> need</c><00:02:46.500><c> is</c>

00:02:46.850 --> 00:02:46.860 align:start position:0%
this method the first thing we need is
 

00:02:46.860 --> 00:02:48.530 align:start position:0%
this method the first thing we need is
our<00:02:46.980><c> null</c><00:02:47.280><c> checks</c><00:02:47.700><c> and</c><00:02:47.819><c> that</c><00:02:48.000><c> the</c><00:02:48.120><c> bucket</c><00:02:48.360><c> size</c>

00:02:48.530 --> 00:02:48.540 align:start position:0%
our null checks and that the bucket size
 

00:02:48.540 --> 00:02:50.330 align:start position:0%
our null checks and that the bucket size
is<00:02:48.720><c> greater</c><00:02:49.080><c> than</c><00:02:49.140><c> zero</c>

00:02:50.330 --> 00:02:50.340 align:start position:0%
is greater than zero
 

00:02:50.340 --> 00:02:52.430 align:start position:0%
is greater than zero
let's<00:02:50.940><c> do</c><00:02:51.120><c> array</c>

00:02:52.430 --> 00:02:52.440 align:start position:0%
let's do array
 

00:02:52.440 --> 00:02:56.150 align:start position:0%
let's do array
array<00:02:53.220><c> equals</c><00:02:53.760><c> null</c>

00:02:56.150 --> 00:02:56.160 align:start position:0%
 
 

00:02:56.160 --> 00:02:59.330 align:start position:0%
 
or<00:02:56.940><c> array</c><00:02:57.360><c> dot</c><00:02:57.840><c> length</c>

00:02:59.330 --> 00:02:59.340 align:start position:0%
or array dot length
 

00:02:59.340 --> 00:03:01.430 align:start position:0%
or array dot length
equals<00:03:00.180><c> zero</c>

00:03:01.430 --> 00:03:01.440 align:start position:0%
equals zero
 

00:03:01.440 --> 00:03:05.690 align:start position:0%
equals zero
or<00:03:02.220><c> bucket</c><00:03:03.180><c> size</c><00:03:03.420><c> is</c><00:03:03.840><c> less</c><00:03:04.080><c> than</c><00:03:04.319><c> zero</c>

00:03:05.690 --> 00:03:05.700 align:start position:0%
or bucket size is less than zero
 

00:03:05.700 --> 00:03:07.309 align:start position:0%
or bucket size is less than zero
and<00:03:06.060><c> we</c><00:03:06.300><c> just</c><00:03:06.480><c> return</c>

00:03:07.309 --> 00:03:07.319 align:start position:0%
and we just return
 

00:03:07.319 --> 00:03:09.110 align:start position:0%
and we just return
the<00:03:07.680><c> next</c><00:03:07.800><c> thing</c><00:03:07.980><c> we</c><00:03:08.099><c> need</c><00:03:08.280><c> to</c><00:03:08.400><c> do</c><00:03:08.519><c> is</c><00:03:08.760><c> find</c><00:03:08.940><c> the</c>

00:03:09.110 --> 00:03:09.120 align:start position:0%
the next thing we need to do is find the
 

00:03:09.120 --> 00:03:11.449 align:start position:0%
the next thing we need to do is find the
minimum<00:03:09.480><c> and</c><00:03:09.780><c> the</c><00:03:09.959><c> maximum</c><00:03:10.500><c> values</c><00:03:11.040><c> in</c><00:03:11.340><c> the</c>

00:03:11.449 --> 00:03:11.459 align:start position:0%
minimum and the maximum values in the
 

00:03:11.459 --> 00:03:13.610 align:start position:0%
minimum and the maximum values in the
array<00:03:11.760><c> which</c><00:03:12.239><c> is</c><00:03:12.360><c> going</c><00:03:12.540><c> to</c><00:03:12.659><c> be</c><00:03:12.720><c> helpful</c><00:03:13.080><c> for</c>

00:03:13.610 --> 00:03:13.620 align:start position:0%
array which is going to be helpful for
 

00:03:13.620 --> 00:03:16.850 align:start position:0%
array which is going to be helpful for
setting<00:03:14.580><c> the</c><00:03:14.760><c> range</c><00:03:15.060><c> for</c><00:03:15.360><c> the</c><00:03:15.540><c> bucket</c><00:03:15.840><c> sort</c>

00:03:16.850 --> 00:03:16.860 align:start position:0%
setting the range for the bucket sort
 

00:03:16.860 --> 00:03:21.910 align:start position:0%
setting the range for the bucket sort
so<00:03:17.519><c> find</c><00:03:18.000><c> the</c><00:03:18.480><c> Min</c><00:03:18.720><c> and</c><00:03:19.260><c> Max</c><00:03:19.500><c> values</c><00:03:20.280><c> in</c><00:03:20.580><c> Array</c>

00:03:21.910 --> 00:03:21.920 align:start position:0%
so find the Min and Max values in Array
 

00:03:21.920 --> 00:03:27.949 align:start position:0%
so find the Min and Max values in Array
int<00:03:22.920><c> Min</c><00:03:23.159><c> value</c><00:03:23.640><c> equals</c><00:03:24.480><c> array</c><00:03:25.140><c> of</c><00:03:26.040><c> 0</c><00:03:26.540><c> and</c><00:03:27.540><c> max</c>

00:03:27.949 --> 00:03:27.959 align:start position:0%
int Min value equals array of 0 and max
 

00:03:27.959 --> 00:03:31.610 align:start position:0%
int Min value equals array of 0 and max
value<00:03:28.319><c> equals</c><00:03:28.980><c> array</c><00:03:29.519><c> of</c><00:03:30.420><c> zero</c><00:03:30.900><c> just</c><00:03:31.200><c> to</c><00:03:31.379><c> start</c>

00:03:31.610 --> 00:03:31.620 align:start position:0%
value equals array of zero just to start
 

00:03:31.620 --> 00:03:33.350 align:start position:0%
value equals array of zero just to start
it<00:03:31.800><c> out</c><00:03:31.920><c> with</c><00:03:32.040><c> some</c><00:03:32.220><c> value</c>

00:03:33.350 --> 00:03:33.360 align:start position:0%
it out with some value
 

00:03:33.360 --> 00:03:35.449 align:start position:0%
it out with some value
now<00:03:33.780><c> we</c><00:03:33.900><c> need</c><00:03:34.019><c> a</c><00:03:34.200><c> for</c><00:03:34.319><c> each</c><00:03:34.500><c> Loop</c><00:03:34.860><c> to</c><00:03:35.099><c> go</c><00:03:35.340><c> over</c>

00:03:35.449 --> 00:03:35.459 align:start position:0%
now we need a for each Loop to go over
 

00:03:35.459 --> 00:03:37.790 align:start position:0%
now we need a for each Loop to go over
the<00:03:35.700><c> whole</c><00:03:35.879><c> array</c><00:03:36.300><c> and</c><00:03:36.599><c> find</c><00:03:36.900><c> these</c><00:03:37.440><c> minimum</c>

00:03:37.790 --> 00:03:37.800 align:start position:0%
the whole array and find these minimum
 

00:03:37.800 --> 00:03:40.729 align:start position:0%
the whole array and find these minimum
and<00:03:37.920><c> maximum</c><00:03:38.340><c> values</c><00:03:38.760><c> so</c><00:03:39.420><c> 4</c>

00:03:40.729 --> 00:03:40.739 align:start position:0%
and maximum values so 4
 

00:03:40.739 --> 00:03:44.809 align:start position:0%
and maximum values so 4
int<00:03:41.459><c> value</c><00:03:41.940><c> array</c>

00:03:44.809 --> 00:03:44.819 align:start position:0%
int value array
 

00:03:44.819 --> 00:03:49.009 align:start position:0%
int value array
if<00:03:45.480><c> value</c><00:03:45.900><c> is</c><00:03:46.260><c> less</c><00:03:46.500><c> than</c><00:03:46.620><c> Min</c><00:03:46.920><c> value</c>

00:03:49.009 --> 00:03:49.019 align:start position:0%
if value is less than Min value
 

00:03:49.019 --> 00:03:53.210 align:start position:0%
if value is less than Min value
Min<00:03:49.739><c> value</c><00:03:50.099><c> equals</c><00:03:50.819><c> value</c>

00:03:53.210 --> 00:03:53.220 align:start position:0%
Min value equals value
 

00:03:53.220 --> 00:03:55.369 align:start position:0%
Min value equals value
else<00:03:53.940><c> if</c>

00:03:55.369 --> 00:03:55.379 align:start position:0%
else if
 

00:03:55.379 --> 00:03:59.270 align:start position:0%
else if
value<00:03:55.980><c> is</c><00:03:56.340><c> greater</c><00:03:56.819><c> than</c><00:03:56.940><c> max</c><00:03:57.900><c> value</c>

00:03:59.270 --> 00:03:59.280 align:start position:0%
value is greater than max value
 

00:03:59.280 --> 00:04:01.729 align:start position:0%
value is greater than max value
max<00:03:59.940><c> value</c><00:04:00.299><c> equals</c><00:04:00.900><c> value</c>

00:04:01.729 --> 00:04:01.739 align:start position:0%
max value equals value
 

00:04:01.739 --> 00:04:03.589 align:start position:0%
max value equals value
now<00:04:02.159><c> we</c><00:04:02.280><c> need</c><00:04:02.459><c> to</c><00:04:02.519><c> get</c><00:04:02.640><c> the</c><00:04:02.819><c> range</c><00:04:03.060><c> so</c><00:04:03.299><c> that</c><00:04:03.420><c> we</c>

00:04:03.589 --> 00:04:03.599 align:start position:0%
now we need to get the range so that we
 

00:04:03.599 --> 00:04:05.630 align:start position:0%
now we need to get the range so that we
can<00:04:03.659><c> ensure</c><00:04:04.080><c> each</c><00:04:04.440><c> value</c><00:04:04.739><c> from</c><00:04:05.220><c> the</c><00:04:05.400><c> array</c>

00:04:05.630 --> 00:04:05.640 align:start position:0%
can ensure each value from the array
 

00:04:05.640 --> 00:04:08.330 align:start position:0%
can ensure each value from the array
goes<00:04:06.120><c> into</c><00:04:06.360><c> the</c><00:04:06.959><c> correct</c><00:04:07.260><c> bucket</c><00:04:07.680><c> how</c><00:04:08.159><c> we're</c>

00:04:08.330 --> 00:04:08.340 align:start position:0%
goes into the correct bucket how we're
 

00:04:08.340 --> 00:04:11.630 align:start position:0%
goes into the correct bucket how we're
going<00:04:08.459><c> to</c><00:04:08.519><c> do</c><00:04:08.640><c> that</c><00:04:08.760><c> is</c><00:04:09.239><c> double</c><00:04:09.980><c> range</c><00:04:10.980><c> equals</c>

00:04:11.630 --> 00:04:11.640 align:start position:0%
going to do that is double range equals
 

00:04:11.640 --> 00:04:15.830 align:start position:0%
going to do that is double range equals
math.ceiling

00:04:15.830 --> 00:04:15.840 align:start position:0%
 
 

00:04:15.840 --> 00:04:17.750 align:start position:0%
 
double

00:04:17.750 --> 00:04:17.760 align:start position:0%
double
 

00:04:17.760 --> 00:04:22.430 align:start position:0%
double
and<00:04:18.299><c> then</c><00:04:18.479><c> max</c><00:04:18.900><c> value</c><00:04:19.579><c> minus</c><00:04:20.579><c> Min</c><00:04:20.880><c> value</c>

00:04:22.430 --> 00:04:22.440 align:start position:0%
and then max value minus Min value
 

00:04:22.440 --> 00:04:24.590 align:start position:0%
and then max value minus Min value
plus<00:04:22.979><c> one</c>

00:04:24.590 --> 00:04:24.600 align:start position:0%
plus one
 

00:04:24.600 --> 00:04:28.610 align:start position:0%
plus one
divided<00:04:25.440><c> by</c><00:04:25.740><c> the</c><00:04:26.699><c> bucket</c><00:04:27.120><c> size</c>

00:04:28.610 --> 00:04:28.620 align:start position:0%
divided by the bucket size
 

00:04:28.620 --> 00:04:30.950 align:start position:0%
divided by the bucket size
basically<00:04:29.280><c> what</c><00:04:29.520><c> we're</c><00:04:29.639><c> doing</c><00:04:29.820><c> is</c><00:04:30.419><c> we</c><00:04:30.840><c> are</c>

00:04:30.950 --> 00:04:30.960 align:start position:0%
basically what we're doing is we are
 

00:04:30.960 --> 00:04:34.070 align:start position:0%
basically what we're doing is we are
going<00:04:31.199><c> to</c><00:04:31.380><c> subtract</c><00:04:32.220><c> the</c><00:04:32.880><c> maximum</c><00:04:33.540><c> and</c><00:04:33.960><c> the</c>

00:04:34.070 --> 00:04:34.080 align:start position:0%
going to subtract the maximum and the
 

00:04:34.080 --> 00:04:36.350 align:start position:0%
going to subtract the maximum and the
minimum<00:04:34.380><c> values</c><00:04:34.800><c> we're</c><00:04:35.400><c> just</c><00:04:35.639><c> going</c><00:04:35.759><c> to</c><00:04:35.880><c> add</c><00:04:36.000><c> 1</c>

00:04:36.350 --> 00:04:36.360 align:start position:0%
minimum values we're just going to add 1
 

00:04:36.360 --> 00:04:39.290 align:start position:0%
minimum values we're just going to add 1
and<00:04:37.080><c> then</c><00:04:37.199><c> we're</c><00:04:37.380><c> going</c><00:04:37.500><c> to</c><00:04:37.680><c> divide</c><00:04:38.400><c> by</c><00:04:39.120><c> the</c>

00:04:39.290 --> 00:04:39.300 align:start position:0%
and then we're going to divide by the
 

00:04:39.300 --> 00:04:40.969 align:start position:0%
and then we're going to divide by the
bucket<00:04:39.600><c> size</c><00:04:39.720><c> this</c><00:04:40.259><c> is</c><00:04:40.380><c> really</c><00:04:40.560><c> just</c><00:04:40.740><c> a</c>

00:04:40.969 --> 00:04:40.979 align:start position:0%
bucket size this is really just a
 

00:04:40.979 --> 00:04:43.550 align:start position:0%
bucket size this is really just a
standard<00:04:41.280><c> equation</c><00:04:41.639><c> to</c><00:04:42.300><c> ensure</c><00:04:42.960><c> that</c><00:04:43.259><c> we're</c>

00:04:43.550 --> 00:04:43.560 align:start position:0%
standard equation to ensure that we're
 

00:04:43.560 --> 00:04:46.850 align:start position:0%
standard equation to ensure that we're
always<00:04:43.919><c> going</c><00:04:44.220><c> to</c><00:04:44.400><c> have</c><00:04:44.820><c> a</c><00:04:45.240><c> value</c><00:04:45.560><c> in</c><00:04:46.560><c> some</c>

00:04:46.850 --> 00:04:46.860 align:start position:0%
always going to have a value in some
 

00:04:46.860 --> 00:04:49.850 align:start position:0%
always going to have a value in some
range<00:04:47.280><c> so</c><00:04:47.699><c> that</c><00:04:48.060><c> it</c><00:04:48.960><c> can</c><00:04:49.139><c> go</c><00:04:49.259><c> into</c><00:04:49.440><c> the</c><00:04:49.620><c> correct</c>

00:04:49.850 --> 00:04:49.860 align:start position:0%
range so that it can go into the correct
 

00:04:49.860 --> 00:04:52.490 align:start position:0%
range so that it can go into the correct
index<00:04:50.340><c> bucket</c><00:04:50.759><c> so</c><00:04:51.060><c> if</c><00:04:51.180><c> we</c><00:04:51.360><c> have</c><00:04:51.540><c> a</c><00:04:52.080><c> bucket</c><00:04:52.380><c> of</c>

00:04:52.490 --> 00:04:52.500 align:start position:0%
index bucket so if we have a bucket of
 

00:04:52.500 --> 00:04:54.770 align:start position:0%
index bucket so if we have a bucket of
size<00:04:52.620><c> 10</c><00:04:52.919><c> that</c><00:04:53.520><c> means</c><00:04:53.880><c> it</c><00:04:54.180><c> can</c><00:04:54.360><c> only</c><00:04:54.540><c> have</c>

00:04:54.770 --> 00:04:54.780 align:start position:0%
size 10 that means it can only have
 

00:04:54.780 --> 00:04:58.010 align:start position:0%
size 10 that means it can only have
values<00:04:55.259><c> 0</c><00:04:55.560><c> through</c><00:04:55.919><c> 9.</c>

00:04:58.010 --> 00:04:58.020 align:start position:0%
values 0 through 9.
 

00:04:58.020 --> 00:05:00.290 align:start position:0%
values 0 through 9.
in<00:04:58.500><c> order</c><00:04:58.620><c> to</c><00:04:58.800><c> make</c><00:04:59.040><c> sure</c><00:04:59.280><c> that</c><00:04:59.820><c> when</c><00:05:00.000><c> the</c>

00:05:00.290 --> 00:05:00.300 align:start position:0%
in order to make sure that when the
 

00:05:00.300 --> 00:05:03.290 align:start position:0%
in order to make sure that when the
range<00:05:00.600><c> is</c><00:05:00.840><c> between</c><00:05:01.139><c> 0</c><00:05:01.680><c> and</c><00:05:02.160><c> 9</c><00:05:02.460><c> we</c><00:05:03.000><c> have</c><00:05:03.120><c> to</c><00:05:03.240><c> have</c>

00:05:03.290 --> 00:05:03.300 align:start position:0%
range is between 0 and 9 we have to have
 

00:05:03.300 --> 00:05:05.030 align:start position:0%
range is between 0 and 9 we have to have
an<00:05:03.419><c> equation</c><00:05:03.660><c> like</c><00:05:03.900><c> this</c><00:05:04.080><c> that</c><00:05:04.560><c> ensures</c><00:05:04.860><c> that</c>

00:05:05.030 --> 00:05:05.040 align:start position:0%
an equation like this that ensures that
 

00:05:05.040 --> 00:05:05.870 align:start position:0%
an equation like this that ensures that
happens

00:05:05.870 --> 00:05:05.880 align:start position:0%
happens
 

00:05:05.880 --> 00:05:07.310 align:start position:0%
happens
the<00:05:06.240><c> next</c><00:05:06.360><c> thing</c><00:05:06.479><c> we</c><00:05:06.600><c> need</c><00:05:06.720><c> to</c><00:05:06.780><c> do</c><00:05:06.960><c> is</c><00:05:07.139><c> to</c>

00:05:07.310 --> 00:05:07.320 align:start position:0%
the next thing we need to do is to
 

00:05:07.320 --> 00:05:12.110 align:start position:0%
the next thing we need to do is to
create<00:05:07.440><c> the</c><00:05:07.680><c> buckets</c><00:05:07.919><c> so</c><00:05:08.460><c> list</c><00:05:08.759><c> integer</c>

00:05:12.110 --> 00:05:12.120 align:start position:0%
 
 

00:05:12.120 --> 00:05:13.790 align:start position:0%
 
buckets

00:05:13.790 --> 00:05:13.800 align:start position:0%
buckets
 

00:05:13.800 --> 00:05:16.430 align:start position:0%
buckets
I<00:05:14.100><c> can</c><00:05:14.220><c> type</c><00:05:14.340><c> equals</c><00:05:15.180><c> new</c>

00:05:16.430 --> 00:05:16.440 align:start position:0%
I can type equals new
 

00:05:16.440 --> 00:05:18.710 align:start position:0%
I can type equals new
linked<00:05:17.100><c> list</c>

00:05:18.710 --> 00:05:18.720 align:start position:0%
linked list
 

00:05:18.720 --> 00:05:22.610 align:start position:0%
linked list
of<00:05:19.020><c> bucket</c><00:05:19.800><c> size</c><00:05:20.280><c> so</c><00:05:20.699><c> 10</c><00:05:21.300><c> basically</c>

00:05:22.610 --> 00:05:22.620 align:start position:0%
of bucket size so 10 basically
 

00:05:22.620 --> 00:05:24.409 align:start position:0%
of bucket size so 10 basically
so<00:05:22.979><c> we're</c><00:05:23.100><c> declaring</c><00:05:23.520><c> a</c><00:05:23.639><c> list</c><00:05:23.820><c> of</c><00:05:24.000><c> integers</c>

00:05:24.409 --> 00:05:24.419 align:start position:0%
so we're declaring a list of integers
 

00:05:24.419 --> 00:05:27.230 align:start position:0%
so we're declaring a list of integers
where<00:05:24.840><c> each</c><00:05:25.139><c> element</c><00:05:25.320><c> of</c><00:05:25.860><c> the</c><00:05:26.039><c> array</c><00:05:26.460><c> is</c><00:05:26.940><c> a</c>

00:05:27.230 --> 00:05:27.240 align:start position:0%
where each element of the array is a
 

00:05:27.240 --> 00:05:28.969 align:start position:0%
where each element of the array is a
linked<00:05:27.600><c> list</c><00:05:27.780><c> the</c><00:05:28.380><c> reason</c><00:05:28.500><c> I</c><00:05:28.620><c> want</c><00:05:28.740><c> to</c><00:05:28.800><c> do</c><00:05:28.919><c> that</c>

00:05:28.969 --> 00:05:28.979 align:start position:0%
linked list the reason I want to do that
 

00:05:28.979 --> 00:05:30.050 align:start position:0%
linked list the reason I want to do that
is<00:05:29.100><c> because</c><00:05:29.220><c> we</c><00:05:29.400><c> have</c><00:05:29.520><c> a</c><00:05:29.639><c> bucket</c><00:05:29.940><c> that</c>

00:05:30.050 --> 00:05:30.060 align:start position:0%
is because we have a bucket that
 

00:05:30.060 --> 00:05:32.450 align:start position:0%
is because we have a bucket that
potentially<00:05:30.539><c> holds</c><00:05:31.020><c> hundreds</c><00:05:31.860><c> thousand</c>

00:05:32.450 --> 00:05:32.460 align:start position:0%
potentially holds hundreds thousand
 

00:05:32.460 --> 00:05:35.270 align:start position:0%
potentially holds hundreds thousand
however<00:05:33.060><c> many</c><00:05:33.300><c> values</c><00:05:34.259><c> I'm</c><00:05:34.979><c> just</c><00:05:35.160><c> going</c><00:05:35.280><c> to</c>

00:05:35.270 --> 00:05:35.280 align:start position:0%
however many values I'm just going to
 

00:05:35.280 --> 00:05:37.129 align:start position:0%
however many values I'm just going to
allow<00:05:35.460><c> the</c><00:05:35.580><c> collections</c><00:05:36.000><c> to</c><00:05:36.240><c> dynamically</c>

00:05:37.129 --> 00:05:37.139 align:start position:0%
allow the collections to dynamically
 

00:05:37.139 --> 00:05:39.469 align:start position:0%
allow the collections to dynamically
adjust<00:05:37.740><c> the</c><00:05:38.160><c> size</c><00:05:38.400><c> of</c><00:05:38.759><c> the</c><00:05:38.880><c> linked</c><00:05:39.180><c> list</c><00:05:39.240><c> so</c>

00:05:39.469 --> 00:05:39.479 align:start position:0%
adjust the size of the linked list so
 

00:05:39.479 --> 00:05:41.029 align:start position:0%
adjust the size of the linked list so
that<00:05:39.600><c> we</c><00:05:39.780><c> don't</c><00:05:39.840><c> have</c><00:05:40.020><c> to</c><00:05:40.080><c> worry</c><00:05:40.199><c> about</c><00:05:40.320><c> the</c>

00:05:41.029 --> 00:05:41.039 align:start position:0%
that we don't have to worry about the
 

00:05:41.039 --> 00:05:43.189 align:start position:0%
that we don't have to worry about the
operation<00:05:41.460><c> of</c><00:05:41.820><c> if</c><00:05:42.000><c> we</c><00:05:42.180><c> reach</c><00:05:42.479><c> the</c><00:05:42.900><c> maximum</c>

00:05:43.189 --> 00:05:43.199 align:start position:0%
operation of if we reach the maximum
 

00:05:43.199 --> 00:05:45.170 align:start position:0%
operation of if we reach the maximum
size<00:05:43.380><c> of</c><00:05:43.620><c> the</c><00:05:43.680><c> array</c><00:05:43.979><c> then</c><00:05:44.460><c> copying</c><00:05:45.060><c> it</c>

00:05:45.170 --> 00:05:45.180 align:start position:0%
size of the array then copying it
 

00:05:45.180 --> 00:05:47.510 align:start position:0%
size of the array then copying it
incrementing<00:05:46.080><c> the</c><00:05:46.199><c> size</c><00:05:46.380><c> and</c><00:05:46.620><c> so</c><00:05:46.860><c> forth</c><00:05:47.160><c> the</c>

00:05:47.510 --> 00:05:47.520 align:start position:0%
incrementing the size and so forth the
 

00:05:47.520 --> 00:05:49.490 align:start position:0%
incrementing the size and so forth the
next<00:05:47.639><c> thing</c><00:05:47.759><c> we</c><00:05:47.880><c> need</c><00:05:48.000><c> to</c><00:05:48.120><c> do</c><00:05:48.240><c> is</c><00:05:48.539><c> to</c><00:05:48.960><c> create</c><00:05:49.199><c> a</c>

00:05:49.490 --> 00:05:49.500 align:start position:0%
next thing we need to do is to create a
 

00:05:49.500 --> 00:05:51.529 align:start position:0%
next thing we need to do is to create a
new<00:05:49.620><c> linked</c><00:05:49.979><c> list</c><00:05:50.100><c> for</c><00:05:50.580><c> each</c><00:05:50.880><c> bucket</c><00:05:51.240><c> in</c><00:05:51.419><c> the</c>

00:05:51.529 --> 00:05:51.539 align:start position:0%
new linked list for each bucket in the
 

00:05:51.539 --> 00:05:55.310 align:start position:0%
new linked list for each bucket in the
array<00:05:52.199><c> or</c><00:05:53.039><c> it's</c><00:05:53.400><c> I</c><00:05:53.820><c> equals</c><00:05:54.300><c> zero</c>

00:05:55.310 --> 00:05:55.320 align:start position:0%
array or it's I equals zero
 

00:05:55.320 --> 00:05:58.070 align:start position:0%
array or it's I equals zero
I<00:05:55.800><c> is</c><00:05:56.039><c> less</c><00:05:56.220><c> than</c><00:05:56.400><c> the</c><00:05:56.639><c> bucket</c><00:05:57.060><c> size</c>

00:05:58.070 --> 00:05:58.080 align:start position:0%
I is less than the bucket size
 

00:05:58.080 --> 00:06:01.010 align:start position:0%
I is less than the bucket size
increment<00:05:58.860><c> I</c>

00:06:01.010 --> 00:06:01.020 align:start position:0%
increment I
 

00:06:01.020 --> 00:06:04.670 align:start position:0%
increment I
buckets<00:06:01.860><c> of</c><00:06:02.639><c> I</c><00:06:02.940><c> so</c><00:06:03.240><c> for</c><00:06:03.539><c> each</c><00:06:03.900><c> index</c><00:06:04.440><c> we're</c>

00:06:04.670 --> 00:06:04.680 align:start position:0%
buckets of I so for each index we're
 

00:06:04.680 --> 00:06:08.330 align:start position:0%
buckets of I so for each index we're
going<00:06:04.860><c> to</c><00:06:04.919><c> say</c><00:06:05.039><c> new</c><00:06:05.660><c> linked</c><00:06:06.660><c> list</c>

00:06:08.330 --> 00:06:08.340 align:start position:0%
going to say new linked list
 

00:06:08.340 --> 00:06:10.249 align:start position:0%
going to say new linked list
now<00:06:08.759><c> we</c><00:06:08.880><c> need</c><00:06:09.000><c> to</c><00:06:09.120><c> insert</c><00:06:09.360><c> the</c><00:06:09.600><c> values</c><00:06:09.840><c> into</c>

00:06:10.249 --> 00:06:10.259 align:start position:0%
now we need to insert the values into
 

00:06:10.259 --> 00:06:11.629 align:start position:0%
now we need to insert the values into
each<00:06:10.680><c> bucket</c>

00:06:11.629 --> 00:06:11.639 align:start position:0%
each bucket
 

00:06:11.639 --> 00:06:13.730 align:start position:0%
each bucket
so<00:06:12.060><c> we'll</c><00:06:12.240><c> just</c><00:06:12.419><c> iterate</c><00:06:12.780><c> over</c><00:06:12.960><c> the</c><00:06:13.259><c> array</c><00:06:13.560><c> so</c>

00:06:13.730 --> 00:06:13.740 align:start position:0%
so we'll just iterate over the array so
 

00:06:13.740 --> 00:06:16.670 align:start position:0%
so we'll just iterate over the array so
int<00:06:14.160><c> value</c><00:06:14.460><c> array</c>

00:06:16.670 --> 00:06:16.680 align:start position:0%
int value array
 

00:06:16.680 --> 00:06:18.890 align:start position:0%
int value array
now<00:06:17.220><c> we</c><00:06:17.520><c> have</c><00:06:17.639><c> a</c><00:06:17.759><c> bucket</c><00:06:18.120><c> index</c><00:06:18.419><c> that</c><00:06:18.600><c> we</c><00:06:18.780><c> need</c>

00:06:18.890 --> 00:06:18.900 align:start position:0%
now we have a bucket index that we need
 

00:06:18.900 --> 00:06:20.629 align:start position:0%
now we have a bucket index that we need
to<00:06:19.080><c> get</c><00:06:19.380><c> and</c><00:06:19.680><c> this</c><00:06:19.860><c> is</c><00:06:19.919><c> going</c><00:06:20.100><c> to</c><00:06:20.100><c> be</c><00:06:20.220><c> based</c><00:06:20.580><c> off</c>

00:06:20.629 --> 00:06:20.639 align:start position:0%
to get and this is going to be based off
 

00:06:20.639 --> 00:06:22.909 align:start position:0%
to get and this is going to be based off
of<00:06:20.820><c> the</c><00:06:21.000><c> range</c><00:06:21.240><c> that</c><00:06:21.419><c> we</c><00:06:21.600><c> did</c><00:06:21.780><c> earlier</c>

00:06:22.909 --> 00:06:22.919 align:start position:0%
of the range that we did earlier
 

00:06:22.919 --> 00:06:26.570 align:start position:0%
of the range that we did earlier
we<00:06:23.639><c> have</c><00:06:23.940><c> value</c><00:06:24.900><c> the</c><00:06:25.740><c> value</c><00:06:25.919><c> from</c><00:06:26.220><c> the</c><00:06:26.340><c> array</c>

00:06:26.570 --> 00:06:26.580 align:start position:0%
we have value the value from the array
 

00:06:26.580 --> 00:06:29.270 align:start position:0%
we have value the value from the array
minus<00:06:27.180><c> the</c><00:06:27.419><c> Min</c><00:06:27.600><c> value</c><00:06:27.900><c> and</c><00:06:28.860><c> then</c><00:06:28.979><c> we're</c><00:06:29.160><c> going</c>

00:06:29.270 --> 00:06:29.280 align:start position:0%
minus the Min value and then we're going
 

00:06:29.280 --> 00:06:31.790 align:start position:0%
minus the Min value and then we're going
to<00:06:29.340><c> divide</c><00:06:29.639><c> that</c><00:06:29.759><c> by</c><00:06:30.000><c> the</c><00:06:30.180><c> range</c>

00:06:31.790 --> 00:06:31.800 align:start position:0%
to divide that by the range
 

00:06:31.800 --> 00:06:34.610 align:start position:0%
to divide that by the range
actually<00:06:32.580><c> we</c><00:06:32.819><c> need</c><00:06:33.060><c> to</c><00:06:33.240><c> cast</c><00:06:33.479><c> this</c>

00:06:34.610 --> 00:06:34.620 align:start position:0%
actually we need to cast this
 

00:06:34.620 --> 00:06:37.010 align:start position:0%
actually we need to cast this
so<00:06:35.400><c> it's</c><00:06:35.580><c> going</c><00:06:35.699><c> to</c><00:06:35.759><c> be</c>

00:06:37.010 --> 00:06:37.020 align:start position:0%
so it's going to be
 

00:06:37.020 --> 00:06:38.450 align:start position:0%
so it's going to be
int

00:06:38.450 --> 00:06:38.460 align:start position:0%
int
 

00:06:38.460 --> 00:06:39.650 align:start position:0%
int
okay<00:06:38.699><c> so</c><00:06:38.880><c> we're</c><00:06:39.000><c> going</c><00:06:39.060><c> to</c><00:06:39.120><c> cast</c><00:06:39.240><c> that</c><00:06:39.360><c> to</c><00:06:39.479><c> an</c>

00:06:39.650 --> 00:06:39.660 align:start position:0%
okay so we're going to cast that to an
 

00:06:39.660 --> 00:06:42.409 align:start position:0%
okay so we're going to cast that to an
INT<00:06:39.900><c> so</c><00:06:40.199><c> now</c><00:06:40.380><c> we</c><00:06:40.500><c> have</c><00:06:40.620><c> our</c><00:06:40.800><c> bucket</c><00:06:41.280><c> index</c><00:06:41.819><c> and</c>

00:06:42.409 --> 00:06:42.419 align:start position:0%
INT so now we have our bucket index and
 

00:06:42.419 --> 00:06:44.689 align:start position:0%
INT so now we have our bucket index and
now<00:06:42.600><c> we</c><00:06:42.720><c> just</c><00:06:42.900><c> add</c><00:06:43.199><c> the</c><00:06:43.500><c> value</c><00:06:43.620><c> into</c><00:06:44.039><c> the</c>

00:06:44.689 --> 00:06:44.699 align:start position:0%
now we just add the value into the
 

00:06:44.699 --> 00:06:46.610 align:start position:0%
now we just add the value into the
correct<00:06:45.060><c> linked</c><00:06:45.419><c> list</c><00:06:45.539><c> or</c><00:06:46.080><c> the</c><00:06:46.380><c> correct</c>

00:06:46.610 --> 00:06:46.620 align:start position:0%
correct linked list or the correct
 

00:06:46.620 --> 00:06:48.170 align:start position:0%
correct linked list or the correct
bucket<00:06:46.979><c> which</c><00:06:47.160><c> is</c><00:06:47.220><c> really</c><00:06:47.400><c> just</c><00:06:47.580><c> the</c><00:06:47.759><c> index</c><00:06:48.000><c> of</c>

00:06:48.170 --> 00:06:48.180 align:start position:0%
bucket which is really just the index of
 

00:06:48.180 --> 00:06:51.650 align:start position:0%
bucket which is really just the index of
the<00:06:48.300><c> array</c><00:06:48.539><c> so</c><00:06:49.139><c> we</c><00:06:49.319><c> say</c><00:06:49.500><c> buckets</c><00:06:50.360><c> of</c><00:06:51.360><c> the</c>

00:06:51.650 --> 00:06:51.660 align:start position:0%
the array so we say buckets of the
 

00:06:51.660 --> 00:06:53.930 align:start position:0%
the array so we say buckets of the
bucket<00:06:52.139><c> indexed</c><00:06:52.740><c> and</c><00:06:52.979><c> because</c><00:06:53.160><c> the</c><00:06:53.520><c> linked</c>

00:06:53.930 --> 00:06:53.940 align:start position:0%
bucket indexed and because the linked
 

00:06:53.940 --> 00:06:56.870 align:start position:0%
bucket indexed and because the linked
list<00:06:54.180><c> we</c><00:06:54.900><c> can</c><00:06:55.080><c> just</c><00:06:55.259><c> say</c><00:06:55.500><c> dot</c><00:06:56.039><c> add</c>

00:06:56.870 --> 00:06:56.880 align:start position:0%
list we can just say dot add
 

00:06:56.880 --> 00:06:59.330 align:start position:0%
list we can just say dot add
the<00:06:57.300><c> value</c>

00:06:59.330 --> 00:06:59.340 align:start position:0%
the value
 

00:06:59.340 --> 00:07:01.550 align:start position:0%
the value
in<00:06:59.880><c> this</c><00:07:00.060><c> is</c><00:07:00.300><c> going</c><00:07:00.539><c> to</c><00:07:00.720><c> add</c><00:07:00.840><c> all</c><00:07:01.139><c> the</c><00:07:01.259><c> values</c>

00:07:01.550 --> 00:07:01.560 align:start position:0%
in this is going to add all the values
 

00:07:01.560 --> 00:07:04.070 align:start position:0%
in this is going to add all the values
into<00:07:01.860><c> the</c><00:07:02.340><c> correct</c><00:07:02.699><c> bucket</c><00:07:03.240><c> the</c><00:07:03.840><c> next</c><00:07:03.960><c> thing</c>

00:07:04.070 --> 00:07:04.080 align:start position:0%
into the correct bucket the next thing
 

00:07:04.080 --> 00:07:05.870 align:start position:0%
into the correct bucket the next thing
we<00:07:04.199><c> need</c><00:07:04.319><c> to</c><00:07:04.440><c> do</c><00:07:04.500><c> is</c><00:07:04.740><c> sort</c><00:07:04.979><c> each</c><00:07:05.220><c> bucket</c><00:07:05.580><c> and</c>

00:07:05.870 --> 00:07:05.880 align:start position:0%
we need to do is sort each bucket and
 

00:07:05.880 --> 00:07:07.010 align:start position:0%
we need to do is sort each bucket and
we're<00:07:06.000><c> just</c><00:07:06.120><c> going</c><00:07:06.240><c> to</c><00:07:06.240><c> use</c><00:07:06.360><c> collections</c><00:07:06.900><c> to</c>

00:07:07.010 --> 00:07:07.020 align:start position:0%
we're just going to use collections to
 

00:07:07.020 --> 00:07:08.570 align:start position:0%
we're just going to use collections to
do<00:07:07.139><c> that</c><00:07:07.259><c> but</c><00:07:07.500><c> if</c><00:07:07.620><c> you</c><00:07:07.800><c> really</c><00:07:07.919><c> want</c><00:07:08.100><c> to</c><00:07:08.220><c> use</c><00:07:08.340><c> a</c>

00:07:08.570 --> 00:07:08.580 align:start position:0%
do that but if you really want to use a
 

00:07:08.580 --> 00:07:10.249 align:start position:0%
do that but if you really want to use a
specific<00:07:08.819><c> algorithm</c><00:07:09.419><c> you</c><00:07:09.780><c> can</c><00:07:09.840><c> do</c><00:07:09.960><c> that</c><00:07:10.080><c> here</c>

00:07:10.249 --> 00:07:10.259 align:start position:0%
specific algorithm you can do that here
 

00:07:10.259 --> 00:07:11.749 align:start position:0%
specific algorithm you can do that here
but<00:07:10.440><c> I'm</c><00:07:10.860><c> just</c><00:07:10.979><c> going</c><00:07:11.100><c> to</c><00:07:11.160><c> let</c><00:07:11.220><c> collections</c>

00:07:11.749 --> 00:07:11.759 align:start position:0%
but I'm just going to let collections
 

00:07:11.759 --> 00:07:13.790 align:start position:0%
but I'm just going to let collections
sort<00:07:12.060><c> this</c><00:07:12.300><c> I'll</c><00:07:12.600><c> have</c><00:07:12.780><c> a</c><00:07:12.840><c> for</c><00:07:12.960><c> each</c><00:07:13.199><c> Loop</c><00:07:13.620><c> so</c>

00:07:13.790 --> 00:07:13.800 align:start position:0%
sort this I'll have a for each Loop so
 

00:07:13.800 --> 00:07:15.790 align:start position:0%
sort this I'll have a for each Loop so
for<00:07:14.220><c> list</c>

00:07:15.790 --> 00:07:15.800 align:start position:0%
for list
 

00:07:15.800 --> 00:07:19.550 align:start position:0%
for list
integer<00:07:16.800><c> oh</c><00:07:17.699><c> integer</c>

00:07:19.550 --> 00:07:19.560 align:start position:0%
integer oh integer
 

00:07:19.560 --> 00:07:23.409 align:start position:0%
integer oh integer
buckets<00:07:20.400><c> of</c><00:07:20.940><c> buckets</c>

00:07:23.409 --> 00:07:23.419 align:start position:0%
buckets of buckets
 

00:07:23.419 --> 00:07:27.770 align:start position:0%
buckets of buckets
collections<00:07:24.419><c> dot</c><00:07:25.259><c> sort</c>

00:07:27.770 --> 00:07:27.780 align:start position:0%
 
 

00:07:27.780 --> 00:07:29.210 align:start position:0%
 
bucket

00:07:29.210 --> 00:07:29.220 align:start position:0%
bucket
 

00:07:29.220 --> 00:07:31.969 align:start position:0%
bucket
so<00:07:29.520><c> it's</c><00:07:29.699><c> going</c><00:07:29.880><c> to</c><00:07:29.940><c> take</c><00:07:30.120><c> each</c><00:07:30.780><c> length</c><00:07:31.620><c> list</c>

00:07:31.969 --> 00:07:31.979 align:start position:0%
so it's going to take each length list
 

00:07:31.979 --> 00:07:35.570 align:start position:0%
so it's going to take each length list
so<00:07:32.639><c> we</c><00:07:33.539><c> have</c><00:07:33.660><c> an</c><00:07:33.900><c> array</c><00:07:34.139><c> size</c><00:07:34.319><c> of</c><00:07:34.500><c> say</c><00:07:34.740><c> 10.</c><00:07:35.280><c> it's</c>

00:07:35.570 --> 00:07:35.580 align:start position:0%
so we have an array size of say 10. it's
 

00:07:35.580 --> 00:07:37.790 align:start position:0%
so we have an array size of say 10. it's
going<00:07:35.699><c> to</c><00:07:35.759><c> go</c><00:07:35.880><c> to</c><00:07:35.940><c> index</c><00:07:36.240><c> 0</c><00:07:36.479><c> take</c><00:07:37.199><c> that</c><00:07:37.380><c> linked</c>

00:07:37.790 --> 00:07:37.800 align:start position:0%
going to go to index 0 take that linked
 

00:07:37.800 --> 00:07:39.409 align:start position:0%
going to go to index 0 take that linked
list<00:07:37.919><c> and</c><00:07:38.280><c> then</c><00:07:38.400><c> sort</c><00:07:38.699><c> all</c><00:07:38.940><c> the</c><00:07:39.060><c> values</c><00:07:39.300><c> that</c>

00:07:39.409 --> 00:07:39.419 align:start position:0%
list and then sort all the values that
 

00:07:39.419 --> 00:07:41.089 align:start position:0%
list and then sort all the values that
linked<00:07:39.780><c> list</c><00:07:39.840><c> it's</c><00:07:40.319><c> going</c><00:07:40.500><c> to</c><00:07:40.560><c> do</c><00:07:40.680><c> that</c><00:07:40.800><c> for</c>

00:07:41.089 --> 00:07:41.099 align:start position:0%
linked list it's going to do that for
 

00:07:41.099 --> 00:07:43.309 align:start position:0%
linked list it's going to do that for
all<00:07:41.460><c> of</c><00:07:41.639><c> the</c><00:07:41.759><c> indexes</c><00:07:42.240><c> of</c><00:07:42.360><c> the</c><00:07:42.479><c> array</c><00:07:42.720><c> and</c>

00:07:43.309 --> 00:07:43.319 align:start position:0%
all of the indexes of the array and
 

00:07:43.319 --> 00:07:46.070 align:start position:0%
all of the indexes of the array and
lastly<00:07:43.620><c> we</c><00:07:44.340><c> basically</c><00:07:44.699><c> need</c><00:07:44.880><c> to</c><00:07:45.000><c> just</c><00:07:45.240><c> add</c><00:07:45.539><c> the</c>

00:07:46.070 --> 00:07:46.080 align:start position:0%
lastly we basically need to just add the
 

00:07:46.080 --> 00:07:48.710 align:start position:0%
lastly we basically need to just add the
linked<00:07:46.440><c> lists</c><00:07:46.919><c> in</c><00:07:47.520><c> order</c><00:07:47.759><c> so</c><00:07:48.180><c> starting</c><00:07:48.479><c> at</c>

00:07:48.710 --> 00:07:48.720 align:start position:0%
linked lists in order so starting at
 

00:07:48.720 --> 00:07:52.070 align:start position:0%
linked lists in order so starting at
index<00:07:49.080><c> 0</c><00:07:49.319><c> back</c><00:07:50.220><c> into</c><00:07:50.520><c> the</c><00:07:50.880><c> original</c><00:07:51.060><c> array</c><00:07:51.539><c> so</c>

00:07:52.070 --> 00:07:52.080 align:start position:0%
index 0 back into the original array so
 

00:07:52.080 --> 00:07:53.629 align:start position:0%
index 0 back into the original array so
for

00:07:53.629 --> 00:07:53.639 align:start position:0%
for
 

00:07:53.639 --> 00:07:56.990 align:start position:0%
for
list<00:07:54.500><c> integer</c><00:07:55.500><c> so</c><00:07:55.800><c> go</c><00:07:56.039><c> over</c><00:07:56.220><c> each</c><00:07:56.520><c> we</c><00:07:56.759><c> need</c><00:07:56.880><c> to</c>

00:07:56.990 --> 00:07:57.000 align:start position:0%
list integer so go over each we need to
 

00:07:57.000 --> 00:08:00.170 align:start position:0%
list integer so go over each we need to
go<00:07:57.180><c> over</c><00:07:57.419><c> each</c><00:07:58.259><c> bucket</c>

00:08:00.170 --> 00:08:00.180 align:start position:0%
go over each bucket
 

00:08:00.180 --> 00:08:03.110 align:start position:0%
go over each bucket
and<00:08:00.720><c> then</c><00:08:00.840><c> for</c><00:08:01.139><c> each</c><00:08:01.440><c> value</c>

00:08:03.110 --> 00:08:03.120 align:start position:0%
and then for each value
 

00:08:03.120 --> 00:08:06.110 align:start position:0%
and then for each value
in<00:08:03.720><c> that</c><00:08:03.900><c> bucket</c>

00:08:06.110 --> 00:08:06.120 align:start position:0%
in that bucket
 

00:08:06.120 --> 00:08:08.210 align:start position:0%
in that bucket
we're<00:08:06.419><c> going</c><00:08:06.599><c> to</c><00:08:06.599><c> say</c><00:08:06.780><c> array</c>

00:08:08.210 --> 00:08:08.220 align:start position:0%
we're going to say array
 

00:08:08.220 --> 00:08:11.089 align:start position:0%
we're going to say array
index<00:08:08.940><c> oh</c><00:08:09.599><c> I</c><00:08:09.840><c> forgot</c><00:08:10.080><c> to</c><00:08:10.259><c> create</c><00:08:10.680><c> the</c><00:08:10.860><c> variable</c>

00:08:11.089 --> 00:08:11.099 align:start position:0%
index oh I forgot to create the variable
 

00:08:11.099 --> 00:08:16.670 align:start position:0%
index oh I forgot to create the variable
it's<00:08:11.280><c> okay</c><00:08:11.479><c> index</c><00:08:12.500><c> equals</c><00:08:13.500><c> value</c>

00:08:16.670 --> 00:08:16.680 align:start position:0%
 
 

00:08:16.680 --> 00:08:20.089 align:start position:0%
 
in<00:08:17.099><c> index</c><00:08:17.639><c> equals</c><00:08:18.120><c> zero</c><00:08:18.479><c> so</c><00:08:19.259><c> all</c><00:08:19.740><c> we're</c><00:08:19.919><c> doing</c>

00:08:20.089 --> 00:08:20.099 align:start position:0%
in index equals zero so all we're doing
 

00:08:20.099 --> 00:08:22.850 align:start position:0%
in index equals zero so all we're doing
is<00:08:20.520><c> for</c><00:08:20.940><c> each</c><00:08:21.300><c> index</c><00:08:21.599><c> in</c><00:08:21.840><c> the</c><00:08:21.960><c> array</c><00:08:22.199><c> we</c><00:08:22.620><c> just</c>

00:08:22.850 --> 00:08:22.860 align:start position:0%
is for each index in the array we just
 

00:08:22.860 --> 00:08:24.589 align:start position:0%
is for each index in the array we just
add<00:08:22.979><c> the</c><00:08:23.099><c> values</c><00:08:23.340><c> of</c><00:08:23.460><c> the</c><00:08:23.580><c> buckets</c><00:08:23.879><c> in</c><00:08:24.360><c> order</c>

00:08:24.589 --> 00:08:24.599 align:start position:0%
add the values of the buckets in order
 

00:08:24.599 --> 00:08:27.409 align:start position:0%
add the values of the buckets in order
from<00:08:25.440><c> index</c><00:08:25.800><c> 0</c><00:08:26.039><c> all</c><00:08:26.460><c> the</c><00:08:26.580><c> way</c><00:08:26.639><c> to</c><00:08:26.819><c> index</c><00:08:27.120><c> nine</c>

00:08:27.409 --> 00:08:27.419 align:start position:0%
from index 0 all the way to index nine
 

00:08:27.419 --> 00:08:29.270 align:start position:0%
from index 0 all the way to index nine
if<00:08:27.660><c> this</c><00:08:27.780><c> bucket</c><00:08:28.080><c> size</c><00:08:28.259><c> was</c><00:08:28.500><c> 10.</c><00:08:28.860><c> all</c><00:08:29.160><c> right</c>

00:08:29.270 --> 00:08:29.280 align:start position:0%
if this bucket size was 10. all right
 

00:08:29.280 --> 00:08:31.430 align:start position:0%
if this bucket size was 10. all right
and<00:08:29.400><c> what</c><00:08:29.580><c> I</c><00:08:29.699><c> have</c><00:08:29.819><c> here</c><00:08:30.000><c> is</c><00:08:30.300><c> I</c><00:08:30.599><c> have</c><00:08:30.720><c> a</c><00:08:31.139><c> bunch</c>

00:08:31.430 --> 00:08:31.440 align:start position:0%
and what I have here is I have a bunch
 

00:08:31.440 --> 00:08:33.889 align:start position:0%
and what I have here is I have a bunch
of<00:08:31.800><c> tests</c><00:08:32.339><c> that</c><00:08:32.580><c> we</c><00:08:32.700><c> can</c><00:08:32.760><c> use</c><00:08:32.940><c> whenever</c><00:08:33.539><c> we</c><00:08:33.719><c> go</c>

00:08:33.889 --> 00:08:33.899 align:start position:0%
of tests that we can use whenever we go
 

00:08:33.899 --> 00:08:35.930 align:start position:0%
of tests that we can use whenever we go
to<00:08:34.020><c> run</c><00:08:34.320><c> this</c>

00:08:35.930 --> 00:08:35.940 align:start position:0%
to run this
 

00:08:35.940 --> 00:08:39.290 align:start position:0%
to run this
just<00:08:36.419><c> go</c><00:08:36.599><c> in</c><00:08:36.779><c> all</c><00:08:36.959><c> these</c><00:08:37.140><c> test</c><00:08:37.260><c> cases</c>

00:08:39.290 --> 00:08:39.300 align:start position:0%
just go in all these test cases
 

00:08:39.300 --> 00:08:41.570 align:start position:0%
just go in all these test cases
so<00:08:39.659><c> if</c><00:08:39.779><c> we</c><00:08:39.899><c> say</c><00:08:40.080><c> if</c><00:08:40.320><c> you</c><00:08:40.440><c> see</c><00:08:40.500><c> that</c><00:08:40.800><c> we</c><00:08:40.979><c> start</c><00:08:41.219><c> up</c>

00:08:41.570 --> 00:08:41.580 align:start position:0%
so if we say if you see that we start up
 

00:08:41.580 --> 00:08:42.409 align:start position:0%
so if we say if you see that we start up
here

00:08:42.409 --> 00:08:42.419 align:start position:0%
here
 

00:08:42.419 --> 00:08:44.149 align:start position:0%
here
it's<00:08:42.899><c> going</c><00:08:43.140><c> to</c><00:08:43.260><c> have</c><00:08:43.500><c> we</c><00:08:43.860><c> still</c><00:08:43.979><c> have</c><00:08:44.039><c> a</c>

00:08:44.149 --> 00:08:44.159 align:start position:0%
it's going to have we still have a
 

00:08:44.159 --> 00:08:45.889 align:start position:0%
it's going to have we still have a
bucket<00:08:44.399><c> size</c><00:08:44.520><c> of</c><00:08:44.640><c> 10</c><00:08:44.760><c> for</c><00:08:45.000><c> all</c><00:08:45.180><c> of</c><00:08:45.300><c> for</c><00:08:45.480><c> most</c><00:08:45.720><c> of</c>

00:08:45.889 --> 00:08:45.899 align:start position:0%
bucket size of 10 for all of for most of
 

00:08:45.899 --> 00:08:48.710 align:start position:0%
bucket size of 10 for all of for most of
these<00:08:46.220><c> and</c><00:08:47.220><c> it</c><00:08:47.580><c> go</c><00:08:47.760><c> ahead</c><00:08:47.880><c> and</c><00:08:47.940><c> it</c><00:08:48.300><c> goes</c><00:08:48.540><c> ahead</c>

00:08:48.710 --> 00:08:48.720 align:start position:0%
these and it go ahead and it goes ahead
 

00:08:48.720 --> 00:08:51.350 align:start position:0%
these and it go ahead and it goes ahead
and<00:08:48.779><c> sorts</c><00:08:49.200><c> them</c><00:08:49.380><c> now</c><00:08:50.040><c> this</c><00:08:50.339><c> one</c><00:08:50.459><c> is</c><00:08:50.580><c> tricky</c><00:08:50.940><c> I</c>

00:08:51.350 --> 00:08:51.360 align:start position:0%
and sorts them now this one is tricky I
 

00:08:51.360 --> 00:08:53.030 align:start position:0%
and sorts them now this one is tricky I
kind<00:08:51.540><c> of</c><00:08:51.660><c> made</c><00:08:51.779><c> this</c><00:08:51.959><c> one</c><00:08:52.200><c> intentionally</c>

00:08:53.030 --> 00:08:53.040 align:start position:0%
kind of made this one intentionally
 

00:08:53.040 --> 00:08:57.710 align:start position:0%
kind of made this one intentionally
tricky<00:08:53.580><c> so</c><00:08:53.880><c> the</c><00:08:54.420><c> biggest</c><00:08:54.600><c> value</c><00:08:54.899><c> is</c><00:08:55.640><c> 299</c><00:08:56.720><c> and</c>

00:08:57.710 --> 00:08:57.720 align:start position:0%
tricky so the biggest value is 299 and
 

00:08:57.720 --> 00:09:00.290 align:start position:0%
tricky so the biggest value is 299 and
essentially<00:08:58.260><c> the</c><00:08:59.100><c> range</c><00:08:59.399><c> for</c><00:08:59.700><c> each</c><00:08:59.940><c> of</c><00:09:00.060><c> these</c>

00:09:00.290 --> 00:09:00.300 align:start position:0%
essentially the range for each of these
 

00:09:00.300 --> 00:09:04.190 align:start position:0%
essentially the range for each of these
buckets<00:09:00.720><c> or</c><00:09:01.080><c> the</c><00:09:01.260><c> bucket</c><00:09:01.620><c> range</c><00:09:01.920><c> is</c><00:09:02.040><c> 300</c><00:09:02.700><c> so</c><00:09:03.600><c> a</c>

00:09:04.190 --> 00:09:04.200 align:start position:0%
buckets or the bucket range is 300 so a
 

00:09:04.200 --> 00:09:06.710 align:start position:0%
buckets or the bucket range is 300 so a
bucket<00:09:04.740><c> of</c><00:09:04.980><c> zero</c><00:09:05.459><c> will</c><00:09:05.880><c> hold</c><00:09:06.060><c> values</c><00:09:06.480><c> from</c>

00:09:06.710 --> 00:09:06.720 align:start position:0%
bucket of zero will hold values from
 

00:09:06.720 --> 00:09:11.210 align:start position:0%
bucket of zero will hold values from
zero<00:09:07.200><c> to</c><00:09:07.980><c> 299</c><00:09:09.019><c> and</c><00:09:10.019><c> then</c><00:09:10.140><c> bucket</c><00:09:10.800><c> one</c><00:09:10.920><c> will</c>

00:09:11.210 --> 00:09:11.220 align:start position:0%
zero to 299 and then bucket one will
 

00:09:11.220 --> 00:09:16.490 align:start position:0%
zero to 299 and then bucket one will
hold<00:09:11.339><c> from</c><00:09:11.580><c> 300</c><00:09:12.180><c> to</c><00:09:13.100><c> 599</c><00:09:14.100><c> and</c><00:09:14.880><c> so</c><00:09:15.120><c> forth</c><00:09:15.480><c> so</c><00:09:15.959><c> I</c>

00:09:16.490 --> 00:09:16.500 align:start position:0%
hold from 300 to 599 and so forth so I
 

00:09:16.500 --> 00:09:17.930 align:start position:0%
hold from 300 to 599 and so forth so I
just<00:09:16.560><c> want</c><00:09:16.680><c> to</c><00:09:16.800><c> make</c><00:09:16.920><c> sure</c><00:09:17.040><c> that</c><00:09:17.339><c> I</c><00:09:17.580><c> could</c><00:09:17.700><c> get</c>

00:09:17.930 --> 00:09:17.940 align:start position:0%
just want to make sure that I could get
 

00:09:17.940 --> 00:09:20.810 align:start position:0%
just want to make sure that I could get
to<00:09:18.240><c> the</c><00:09:18.600><c> very</c><00:09:18.899><c> last</c><00:09:19.140><c> value</c>

00:09:20.810 --> 00:09:20.820 align:start position:0%
to the very last value
 

00:09:20.820 --> 00:09:24.410 align:start position:0%
to the very last value
um<00:09:20.880><c> like</c><00:09:21.060><c> 299</c><00:09:21.959><c> kind</c><00:09:22.800><c> of</c><00:09:22.860><c> like</c><00:09:23.100><c> within</c><00:09:23.940><c> this</c>

00:09:24.410 --> 00:09:24.420 align:start position:0%
um like 299 kind of like within this
 

00:09:24.420 --> 00:09:27.290 align:start position:0%
um like 299 kind of like within this
test<00:09:24.899><c> and</c><00:09:25.680><c> it</c><00:09:25.920><c> still</c><00:09:26.100><c> goes</c><00:09:26.459><c> to</c><00:09:26.640><c> bucket</c><00:09:27.060><c> nine</c>

00:09:27.290 --> 00:09:27.300 align:start position:0%
test and it still goes to bucket nine
 

00:09:27.300 --> 00:09:29.449 align:start position:0%
test and it still goes to bucket nine
there<00:09:27.899><c> there</c><00:09:28.200><c> is</c><00:09:28.380><c> a</c><00:09:28.560><c> potential</c><00:09:28.920><c> for</c><00:09:29.279><c> an</c>

00:09:29.449 --> 00:09:29.459 align:start position:0%
there there is a potential for an
 

00:09:29.459 --> 00:09:32.210 align:start position:0%
there there is a potential for an
overflow<00:09:29.940><c> or</c><00:09:30.480><c> I</c><00:09:30.720><c> mean</c><00:09:30.899><c> it</c><00:09:31.440><c> could</c><00:09:31.620><c> go</c><00:09:31.740><c> to</c><00:09:31.860><c> index</c>

00:09:32.210 --> 00:09:32.220 align:start position:0%
overflow or I mean it could go to index
 

00:09:32.220 --> 00:09:33.650 align:start position:0%
overflow or I mean it could go to index
10<00:09:32.459><c> if</c><00:09:32.760><c> you</c><00:09:32.880><c> don't</c><00:09:33.000><c> need</c><00:09:33.120><c> the</c><00:09:33.300><c> calculation</c>

00:09:33.650 --> 00:09:33.660 align:start position:0%
10 if you don't need the calculation
 

00:09:33.660 --> 00:09:35.630 align:start position:0%
10 if you don't need the calculation
just<00:09:34.019><c> correct</c><00:09:34.440><c> and</c><00:09:34.980><c> so</c><00:09:35.100><c> if</c><00:09:35.220><c> you</c><00:09:35.339><c> get</c><00:09:35.519><c> some</c>

00:09:35.630 --> 00:09:35.640 align:start position:0%
just correct and so if you get some
 

00:09:35.640 --> 00:09:37.250 align:start position:0%
just correct and so if you get some
errors<00:09:36.000><c> on</c><00:09:36.120><c> these</c><00:09:36.300><c> tests</c><00:09:36.600><c> that</c><00:09:36.720><c> you've</c><00:09:36.959><c> copied</c>

00:09:37.250 --> 00:09:37.260 align:start position:0%
errors on these tests that you've copied
 

00:09:37.260 --> 00:09:41.090 align:start position:0%
errors on these tests that you've copied
it's<00:09:37.740><c> probably</c><00:09:37.980><c> because</c><00:09:38.279><c> the</c><00:09:39.240><c> range</c><00:09:40.100><c> equation</c>

00:09:41.090 --> 00:09:41.100 align:start position:0%
it's probably because the range equation
 

00:09:41.100 --> 00:09:43.190 align:start position:0%
it's probably because the range equation
isn't<00:09:41.459><c> quite</c><00:09:41.640><c> right</c><00:09:41.760><c> maybe</c><00:09:41.940><c> just</c><00:09:42.480><c> something</c>

00:09:43.190 --> 00:09:43.200 align:start position:0%
isn't quite right maybe just something
 

00:09:43.200 --> 00:09:45.350 align:start position:0%
isn't quite right maybe just something
and<00:09:43.620><c> that's</c><00:09:43.860><c> okay</c><00:09:44.040><c> well</c><00:09:44.700><c> good</c><00:09:44.880><c> job</c><00:09:45.000><c> we</c><00:09:45.180><c> just</c>

00:09:45.350 --> 00:09:45.360 align:start position:0%
and that's okay well good job we just
 

00:09:45.360 --> 00:09:46.970 align:start position:0%
and that's okay well good job we just
got<00:09:45.480><c> through</c><00:09:45.600><c> bucket</c><00:09:45.959><c> sort</c><00:09:46.200><c> and</c><00:09:46.440><c> something</c><00:09:46.680><c> to</c>

00:09:46.970 --> 00:09:46.980 align:start position:0%
got through bucket sort and something to
 

00:09:46.980 --> 00:09:48.710 align:start position:0%
got through bucket sort and something to
note<00:09:47.100><c> about</c><00:09:47.399><c> the</c><00:09:47.640><c> time</c><00:09:47.700><c> complexity</c><00:09:48.240><c> of</c><00:09:48.420><c> bucket</c>

00:09:48.710 --> 00:09:48.720 align:start position:0%
note about the time complexity of bucket
 

00:09:48.720 --> 00:09:51.829 align:start position:0%
note about the time complexity of bucket
sort<00:09:48.959><c> is</c><00:09:49.740><c> in</c><00:09:50.220><c> the</c><00:09:50.339><c> worst</c><00:09:50.640><c> case</c><00:09:50.940><c> this</c><00:09:51.360><c> is</c><00:09:51.420><c> Big</c><00:09:51.660><c> O</c>

00:09:51.829 --> 00:09:51.839 align:start position:0%
sort is in the worst case this is Big O
 

00:09:51.839 --> 00:09:54.410 align:start position:0%
sort is in the worst case this is Big O
of<00:09:52.080><c> N</c><00:09:52.260><c> squared</c><00:09:52.740><c> and</c><00:09:52.980><c> the</c><00:09:53.160><c> reason</c><00:09:53.339><c> is</c><00:09:54.180><c> because</c>

00:09:54.410 --> 00:09:54.420 align:start position:0%
of N squared and the reason is because
 

00:09:54.420 --> 00:09:56.329 align:start position:0%
of N squared and the reason is because
when<00:09:54.839><c> we're</c><00:09:54.959><c> in</c><00:09:55.320><c> the</c><00:09:55.380><c> beginning</c><00:09:55.560><c> of</c><00:09:55.980><c> bucket</c>

00:09:56.329 --> 00:09:56.339 align:start position:0%
when we're in the beginning of bucket
 

00:09:56.339 --> 00:09:57.650 align:start position:0%
when we're in the beginning of bucket
store<00:09:56.519><c> and</c><00:09:56.700><c> we</c><00:09:56.820><c> need</c><00:09:56.940><c> to</c><00:09:57.060><c> insert</c><00:09:57.300><c> all</c><00:09:57.480><c> the</c>

00:09:57.650 --> 00:09:57.660 align:start position:0%
store and we need to insert all the
 

00:09:57.660 --> 00:09:59.750 align:start position:0%
store and we need to insert all the
values<00:09:57.959><c> from</c><00:09:58.140><c> the</c><00:09:58.320><c> array</c><00:09:58.620><c> into</c><00:09:59.220><c> each</c><00:09:59.519><c> of</c><00:09:59.640><c> the</c>

00:09:59.750 --> 00:09:59.760 align:start position:0%
values from the array into each of the
 

00:09:59.760 --> 00:10:02.509 align:start position:0%
values from the array into each of the
buckets<00:10:00.060><c> if</c><00:10:00.720><c> all</c><00:10:01.019><c> those</c><00:10:01.140><c> values</c><00:10:01.560><c> go</c><00:10:01.740><c> into</c><00:10:01.860><c> a</c>

00:10:02.509 --> 00:10:02.519 align:start position:0%
buckets if all those values go into a
 

00:10:02.519 --> 00:10:04.150 align:start position:0%
buckets if all those values go into a
single<00:10:02.820><c> bucket</c>

00:10:04.150 --> 00:10:04.160 align:start position:0%
single bucket
 

00:10:04.160 --> 00:10:07.250 align:start position:0%
single bucket
not<00:10:05.160><c> good</c><00:10:05.399><c> that's</c><00:10:06.000><c> that's</c><00:10:06.300><c> the</c><00:10:06.600><c> worst</c><00:10:06.839><c> case</c><00:10:06.959><c> we</c>

00:10:07.250 --> 00:10:07.260 align:start position:0%
not good that's that's the worst case we
 

00:10:07.260 --> 00:10:09.530 align:start position:0%
not good that's that's the worst case we
don't<00:10:07.380><c> want</c><00:10:07.620><c> that</c><00:10:07.800><c> all</c><00:10:08.040><c> right</c><00:10:08.220><c> bucket</c><00:10:08.820><c> sort</c><00:10:09.060><c> is</c>

00:10:09.530 --> 00:10:09.540 align:start position:0%
don't want that all right bucket sort is
 

00:10:09.540 --> 00:10:13.070 align:start position:0%
don't want that all right bucket sort is
best<00:10:10.260><c> for</c><00:10:10.560><c> uniformly</c><00:10:11.459><c> distributed</c><00:10:12.300><c> data</c><00:10:12.779><c> okay</c>

00:10:13.070 --> 00:10:13.080 align:start position:0%
best for uniformly distributed data okay
 

00:10:13.080 --> 00:10:15.110 align:start position:0%
best for uniformly distributed data okay
it's<00:10:13.440><c> called</c><00:10:13.680><c> a</c><00:10:13.920><c> like</c><00:10:14.399><c> some</c><00:10:14.640><c> I</c><00:10:14.880><c> think</c><00:10:15.000><c> it's</c>

00:10:15.110 --> 00:10:15.120 align:start position:0%
it's called a like some I think it's
 

00:10:15.120 --> 00:10:18.170 align:start position:0%
it's called a like some I think it's
called<00:10:15.240><c> scatter</c><00:10:15.720><c> gather</c><00:10:16.140><c> where</c><00:10:16.560><c> we</c><00:10:17.160><c> take</c><00:10:17.519><c> all</c>

00:10:18.170 --> 00:10:18.180 align:start position:0%
called scatter gather where we take all
 

00:10:18.180 --> 00:10:20.570 align:start position:0%
called scatter gather where we take all
of<00:10:18.300><c> the</c><00:10:18.480><c> values</c><00:10:18.839><c> put</c><00:10:19.500><c> them</c><00:10:19.680><c> into</c><00:10:19.860><c> like</c><00:10:20.220><c> make</c>

00:10:20.570 --> 00:10:20.580 align:start position:0%
of the values put them into like make
 

00:10:20.580 --> 00:10:22.970 align:start position:0%
of the values put them into like make
them<00:10:20.760><c> in</c><00:10:20.880><c> the</c><00:10:21.060><c> little</c><00:10:21.180><c> sub</c><00:10:21.480><c> problems</c><00:10:22.140><c> and</c><00:10:22.860><c> then</c>

00:10:22.970 --> 00:10:22.980 align:start position:0%
them in the little sub problems and then
 

00:10:22.980 --> 00:10:25.310 align:start position:0%
them in the little sub problems and then
we<00:10:23.399><c> sort</c><00:10:23.640><c> all</c><00:10:23.940><c> those</c><00:10:24.060><c> sub</c><00:10:24.240><c> problems</c><00:10:24.779><c> and</c><00:10:25.140><c> then</c>

00:10:25.310 --> 00:10:25.320 align:start position:0%
we sort all those sub problems and then
 

00:10:25.320 --> 00:10:28.610 align:start position:0%
we sort all those sub problems and then
gather<00:10:25.920><c> them</c><00:10:26.220><c> back</c><00:10:26.580><c> together</c><00:10:26.940><c> okay</c><00:10:27.779><c> don't</c>

00:10:28.610 --> 00:10:28.620 align:start position:0%
gather them back together okay don't
 

00:10:28.620 --> 00:10:30.230 align:start position:0%
gather them back together okay don't
forget<00:10:28.740><c> to</c><00:10:29.040><c> like</c><00:10:29.279><c> And</c><00:10:29.459><c> subscribe</c><00:10:29.880><c> it</c><00:10:30.060><c> would</c>

00:10:30.230 --> 00:10:30.240 align:start position:0%
forget to like And subscribe it would
 

00:10:30.240 --> 00:10:32.150 align:start position:0%
forget to like And subscribe it would
really<00:10:30.360><c> help</c><00:10:30.540><c> me</c><00:10:30.720><c> out</c><00:10:30.899><c> and</c><00:10:31.560><c> comment</c><00:10:31.920><c> down</c>

00:10:32.150 --> 00:10:32.160 align:start position:0%
really help me out and comment down
 

00:10:32.160 --> 00:10:34.430 align:start position:0%
really help me out and comment down
below<00:10:32.279><c> if</c><00:10:32.640><c> you</c><00:10:32.760><c> have</c><00:10:32.940><c> any</c><00:10:33.180><c> questions</c><00:10:33.480><c> other</c>

00:10:34.430 --> 00:10:34.440 align:start position:0%
below if you have any questions other
 

00:10:34.440 --> 00:10:36.170 align:start position:0%
below if you have any questions other
than<00:10:34.680><c> that</c><00:10:34.860><c> here</c><00:10:35.220><c> are</c><00:10:35.399><c> some</c><00:10:35.519><c> more</c><00:10:35.640><c> videos</c><00:10:35.820><c> on</c>

00:10:36.170 --> 00:10:36.180 align:start position:0%
than that here are some more videos on
 

00:10:36.180 --> 00:10:37.910 align:start position:0%
than that here are some more videos on
sorting<00:10:36.600><c> algorithms</c><00:10:37.080><c> I'll</c><00:10:37.440><c> see</c><00:10:37.620><c> you</c><00:10:37.740><c> next</c>

00:10:37.910 --> 00:10:37.920 align:start position:0%
sorting algorithms I'll see you next
 

00:10:37.920 --> 00:10:40.040 align:start position:0%
sorting algorithms I'll see you next
time

