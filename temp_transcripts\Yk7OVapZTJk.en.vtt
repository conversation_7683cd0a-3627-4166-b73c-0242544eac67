WEBVTT
Kind: captions
Language: en

00:00:00.960 --> 00:00:02.869 align:start position:0%
 
and<00:00:01.280><c> welcome</c><00:00:01.599><c> back</c><00:00:01.839><c> guys</c><00:00:02.159><c> so</c><00:00:02.399><c> in</c><00:00:02.480><c> the</c><00:00:02.560><c> last</c>

00:00:02.869 --> 00:00:02.879 align:start position:0%
and welcome back guys so in the last
 

00:00:02.879 --> 00:00:06.550 align:start position:0%
and welcome back guys so in the last
video<00:00:03.280><c> we</c><00:00:03.520><c> saw</c><00:00:03.840><c> how</c><00:00:04.080><c> to</c><00:00:04.319><c> deploy</c><00:00:05.040><c> a</c><00:00:05.200><c> wordpress</c>

00:00:06.550 --> 00:00:06.560 align:start position:0%
video we saw how to deploy a wordpress
 

00:00:06.560 --> 00:00:07.510 align:start position:0%
video we saw how to deploy a wordpress
um

00:00:07.510 --> 00:00:07.520 align:start position:0%
um
 

00:00:07.520 --> 00:00:11.190 align:start position:0%
um
site<00:00:08.000><c> onto</c><00:00:08.800><c> a</c><00:00:09.360><c> digitalocean</c><00:00:10.240><c> droplet</c><00:00:10.880><c> but</c>

00:00:11.190 --> 00:00:11.200 align:start position:0%
site onto a digitalocean droplet but
 

00:00:11.200 --> 00:00:13.749 align:start position:0%
site onto a digitalocean droplet but
right<00:00:11.360><c> now</c><00:00:11.679><c> our</c><00:00:11.840><c> site</c><00:00:12.160><c> simply</c><00:00:12.559><c> sits</c><00:00:12.960><c> on</c><00:00:13.120><c> our</c><00:00:13.280><c> ip</c>

00:00:13.749 --> 00:00:13.759 align:start position:0%
right now our site simply sits on our ip
 

00:00:13.759 --> 00:00:16.310 align:start position:0%
right now our site simply sits on our ip
address<00:00:14.639><c> now</c><00:00:14.799><c> i</c><00:00:15.040><c> assume</c><00:00:15.599><c> most</c><00:00:15.920><c> of</c><00:00:15.920><c> you</c><00:00:16.080><c> guys</c>

00:00:16.310 --> 00:00:16.320 align:start position:0%
address now i assume most of you guys
 

00:00:16.320 --> 00:00:18.310 align:start position:0%
address now i assume most of you guys
that<00:00:16.480><c> are</c><00:00:16.640><c> watching</c><00:00:16.960><c> this</c><00:00:17.119><c> tutorial</c><00:00:17.920><c> do</c><00:00:18.160><c> want</c>

00:00:18.310 --> 00:00:18.320 align:start position:0%
that are watching this tutorial do want
 

00:00:18.320 --> 00:00:20.630 align:start position:0%
that are watching this tutorial do want
to<00:00:18.480><c> configure</c><00:00:19.039><c> this</c>

00:00:20.630 --> 00:00:20.640 align:start position:0%
to configure this
 

00:00:20.640 --> 00:00:23.670 align:start position:0%
to configure this
to<00:00:21.119><c> run</c><00:00:21.359><c> on</c><00:00:21.520><c> a</c><00:00:21.600><c> specific</c><00:00:22.240><c> domain</c><00:00:22.800><c> name</c><00:00:23.119><c> so</c><00:00:23.600><c> in</c>

00:00:23.670 --> 00:00:23.680 align:start position:0%
to run on a specific domain name so in
 

00:00:23.680 --> 00:00:25.750 align:start position:0%
to run on a specific domain name so in
this<00:00:23.920><c> example</c><00:00:24.880><c> i'm</c><00:00:25.039><c> going</c><00:00:25.119><c> to</c><00:00:25.199><c> be</c><00:00:25.359><c> using</c>

00:00:25.750 --> 00:00:25.760 align:start position:0%
this example i'm going to be using
 

00:00:25.760 --> 00:00:27.990 align:start position:0%
this example i'm going to be using
cloudflare<00:00:26.400><c> and</c><00:00:26.560><c> namecheap</c><00:00:27.279><c> to</c><00:00:27.519><c> point</c><00:00:27.760><c> my</c>

00:00:27.990 --> 00:00:28.000 align:start position:0%
cloudflare and namecheap to point my
 

00:00:28.000 --> 00:00:31.830 align:start position:0%
cloudflare and namecheap to point my
domain<00:00:28.480><c> name</c><00:00:29.199><c> at</c><00:00:29.519><c> my</c><00:00:30.160><c> um</c><00:00:30.800><c> digitalocean</c>

00:00:31.830 --> 00:00:31.840 align:start position:0%
domain name at my um digitalocean
 

00:00:31.840 --> 00:00:34.790 align:start position:0%
domain name at my um digitalocean
droplet<00:00:32.399><c> at</c><00:00:32.640><c> the</c><00:00:32.719><c> digitalocean</c><00:00:33.600><c> server</c><00:00:34.079><c> so</c>

00:00:34.790 --> 00:00:34.800 align:start position:0%
droplet at the digitalocean server so
 

00:00:34.800 --> 00:00:37.830 align:start position:0%
droplet at the digitalocean server so
it's<00:00:35.120><c> almost</c><00:00:35.680><c> um</c><00:00:36.160><c> identical</c><00:00:37.280><c> to</c><00:00:37.440><c> the</c><00:00:37.520><c> way</c><00:00:37.680><c> that</c>

00:00:37.830 --> 00:00:37.840 align:start position:0%
it's almost um identical to the way that
 

00:00:37.840 --> 00:00:40.790 align:start position:0%
it's almost um identical to the way that
you<00:00:37.920><c> would</c><00:00:38.079><c> do</c><00:00:38.239><c> it</c><00:00:38.320><c> with</c><00:00:38.480><c> godaddy</c><00:00:39.200><c> or</c><00:00:39.360><c> another</c>

00:00:40.790 --> 00:00:40.800 align:start position:0%
you would do it with godaddy or another
 

00:00:40.800 --> 00:00:43.110 align:start position:0%
you would do it with godaddy or another
domain<00:00:41.280><c> name</c><00:00:41.520><c> provider</c><00:00:42.079><c> so</c><00:00:42.239><c> in</c><00:00:42.320><c> this</c><00:00:42.480><c> case</c>

00:00:43.110 --> 00:00:43.120 align:start position:0%
domain name provider so in this case
 

00:00:43.120 --> 00:00:44.410 align:start position:0%
domain name provider so in this case
here<00:00:43.360><c> we</c><00:00:43.520><c> have</c><00:00:44.079><c> um</c>

00:00:44.410 --> 00:00:44.420 align:start position:0%
here we have um
 

00:00:44.420 --> 00:00:46.229 align:start position:0%
here we have um
[Music]

00:00:46.229 --> 00:00:46.239 align:start position:0%
[Music]
 

00:00:46.239 --> 00:00:49.270 align:start position:0%
[Music]
we<00:00:46.559><c> we</c><00:00:46.719><c> have</c><00:00:47.039><c> the</c><00:00:47.680><c> domain</c><00:00:48.239><c> within</c><00:00:48.719><c> namecheap</c>

00:00:49.270 --> 00:00:49.280 align:start position:0%
we we have the domain within namecheap
 

00:00:49.280 --> 00:00:50.950 align:start position:0%
we we have the domain within namecheap
i'm<00:00:49.360><c> going</c><00:00:49.440><c> to</c><00:00:49.520><c> go</c><00:00:49.680><c> ahead</c><00:00:49.840><c> and</c><00:00:50.000><c> press</c><00:00:50.399><c> manage</c>

00:00:50.950 --> 00:00:50.960 align:start position:0%
i'm going to go ahead and press manage
 

00:00:50.960 --> 00:00:54.229 align:start position:0%
i'm going to go ahead and press manage
on<00:00:51.120><c> the</c><00:00:51.280><c> domain</c><00:00:51.840><c> kingcommerce.biz</c>

00:00:54.229 --> 00:00:54.239 align:start position:0%
on the domain kingcommerce.biz
 

00:00:54.239 --> 00:00:58.549 align:start position:0%
on the domain kingcommerce.biz
and<00:00:55.039><c> i</c><00:00:55.280><c> have</c><00:00:55.520><c> over</c><00:00:55.840><c> here</c><00:00:56.320><c> a</c><00:00:56.640><c> custom</c><00:00:57.120><c> dns</c><00:00:58.239><c> is</c>

00:00:58.549 --> 00:00:58.559 align:start position:0%
and i have over here a custom dns is
 

00:00:58.559 --> 00:01:01.029 align:start position:0%
and i have over here a custom dns is
selected<00:00:59.280><c> and</c><00:00:59.440><c> i'm</c><00:00:59.600><c> pointing</c><00:01:00.000><c> it</c><00:01:00.160><c> at</c><00:01:00.719><c> these</c>

00:01:01.029 --> 00:01:01.039 align:start position:0%
selected and i'm pointing it at these
 

00:01:01.039 --> 00:01:02.869 align:start position:0%
selected and i'm pointing it at these
two<00:01:01.359><c> cloudflare</c>

00:01:02.869 --> 00:01:02.879 align:start position:0%
two cloudflare
 

00:01:02.879 --> 00:01:05.149 align:start position:0%
two cloudflare
domains<00:01:03.840><c> um</c>

00:01:05.149 --> 00:01:05.159 align:start position:0%
domains um
 

00:01:05.159 --> 00:01:06.310 align:start position:0%
domains um
anna.ns.cloudflare<00:01:06.240><c> and</c>

00:01:06.310 --> 00:01:06.320 align:start position:0%
anna.ns.cloudflare and
 

00:01:06.320 --> 00:01:08.469 align:start position:0%
anna.ns.cloudflare and
odin.ns.cloudflare

00:01:08.469 --> 00:01:08.479 align:start position:0%
odin.ns.cloudflare
 

00:01:08.479 --> 00:01:10.230 align:start position:0%
odin.ns.cloudflare
what<00:01:08.640><c> that</c><00:01:08.880><c> does</c><00:01:09.119><c> is</c><00:01:09.360><c> every</c><00:01:09.520><c> time</c><00:01:09.760><c> somebody</c>

00:01:10.230 --> 00:01:10.240 align:start position:0%
what that does is every time somebody
 

00:01:10.240 --> 00:01:13.109 align:start position:0%
what that does is every time somebody
visits<00:01:10.640><c> kingcommerce.biz</c><00:01:12.080><c> this</c><00:01:12.320><c> domain</c><00:01:12.720><c> name</c>

00:01:13.109 --> 00:01:13.119 align:start position:0%
visits kingcommerce.biz this domain name
 

00:01:13.119 --> 00:01:15.510 align:start position:0%
visits kingcommerce.biz this domain name
it<00:01:13.280><c> immediately</c><00:01:14.240><c> routes</c><00:01:14.640><c> the</c><00:01:14.799><c> request</c><00:01:15.360><c> to</c>

00:01:15.510 --> 00:01:15.520 align:start position:0%
it immediately routes the request to
 

00:01:15.520 --> 00:01:17.830 align:start position:0%
it immediately routes the request to
cloudflare<00:01:16.320><c> now</c><00:01:16.560><c> on</c><00:01:16.720><c> cloudflare</c><00:01:17.439><c> i</c><00:01:17.600><c> have</c>

00:01:17.830 --> 00:01:17.840 align:start position:0%
cloudflare now on cloudflare i have
 

00:01:17.840 --> 00:01:19.030 align:start position:0%
cloudflare now on cloudflare i have
these<00:01:18.159><c> two</c>

00:01:19.030 --> 00:01:19.040 align:start position:0%
these two
 

00:01:19.040 --> 00:01:22.630 align:start position:0%
these two
uh<00:01:19.280><c> dns</c><00:01:19.920><c> records</c><00:01:20.400><c> set</c><00:01:20.720><c> up</c><00:01:20.880><c> one</c><00:01:21.119><c> is</c><00:01:21.280><c> an</c><00:01:21.439><c> a</c><00:01:22.159><c> a</c><00:01:22.479><c> uh</c>

00:01:22.630 --> 00:01:22.640 align:start position:0%
uh dns records set up one is an a a uh
 

00:01:22.640 --> 00:01:25.510 align:start position:0%
uh dns records set up one is an a a uh
record<00:01:23.280><c> pointing</c><00:01:23.759><c> to</c><00:01:23.920><c> the</c><00:01:24.080><c> ip</c><00:01:24.560><c> address</c><00:01:25.280><c> that</c>

00:01:25.510 --> 00:01:25.520 align:start position:0%
record pointing to the ip address that
 

00:01:25.520 --> 00:01:27.830 align:start position:0%
record pointing to the ip address that
we<00:01:25.680><c> got</c><00:01:26.080><c> from</c><00:01:26.479><c> digitalocean</c><00:01:27.360><c> i'm</c><00:01:27.520><c> going</c><00:01:27.600><c> to</c><00:01:27.680><c> go</c>

00:01:27.830 --> 00:01:27.840 align:start position:0%
we got from digitalocean i'm going to go
 

00:01:27.840 --> 00:01:31.429 align:start position:0%
we got from digitalocean i'm going to go
ahead<00:01:28.000><c> and</c><00:01:28.640><c> update</c><00:01:29.040><c> that</c><00:01:29.280><c> now</c><00:01:29.759><c> copy</c>

00:01:31.429 --> 00:01:31.439 align:start position:0%
ahead and update that now copy
 

00:01:31.439 --> 00:01:33.990 align:start position:0%
ahead and update that now copy
and<00:01:31.759><c> edit</c><00:01:32.159><c> it</c>

00:01:33.990 --> 00:01:34.000 align:start position:0%
and edit it
 

00:01:34.000 --> 00:01:36.230 align:start position:0%
and edit it
and<00:01:34.320><c> paste</c><00:01:34.560><c> it</c><00:01:34.720><c> in</c><00:01:34.799><c> there</c><00:01:35.119><c> and</c><00:01:35.280><c> press</c><00:01:35.600><c> save</c><00:01:36.079><c> and</c>

00:01:36.230 --> 00:01:36.240 align:start position:0%
and paste it in there and press save and
 

00:01:36.240 --> 00:01:39.190 align:start position:0%
and paste it in there and press save and
i<00:01:36.400><c> also</c><00:01:36.720><c> have</c><00:01:37.040><c> a</c><00:01:37.280><c> cname</c>

00:01:39.190 --> 00:01:39.200 align:start position:0%
i also have a cname
 

00:01:39.200 --> 00:01:41.830 align:start position:0%
i also have a cname
where<00:01:39.439><c> the</c><00:01:39.600><c> wreck</c><00:01:39.920><c> the</c><00:01:40.400><c> record</c><00:01:40.799><c> is</c><00:01:40.880><c> www</c>

00:01:41.830 --> 00:01:41.840 align:start position:0%
where the wreck the record is www
 

00:01:41.840 --> 00:01:43.830 align:start position:0%
where the wreck the record is www
pointing<00:01:42.240><c> at</c><00:01:42.320><c> the</c><00:01:42.399><c> domain</c><00:01:42.960><c> name</c><00:01:43.280><c> itself</c><00:01:43.680><c> and</c>

00:01:43.830 --> 00:01:43.840 align:start position:0%
pointing at the domain name itself and
 

00:01:43.840 --> 00:01:46.230 align:start position:0%
pointing at the domain name itself and
you<00:01:44.000><c> enter</c><00:01:44.799><c> whatever</c><00:01:45.200><c> domain</c><00:01:45.600><c> name</c><00:01:45.840><c> you</c><00:01:46.000><c> have</c>

00:01:46.230 --> 00:01:46.240 align:start position:0%
you enter whatever domain name you have
 

00:01:46.240 --> 00:01:48.789 align:start position:0%
you enter whatever domain name you have
there<00:01:46.640><c> okay</c><00:01:46.880><c> so</c><00:01:47.280><c> what</c><00:01:47.520><c> that</c>

00:01:48.789 --> 00:01:48.799 align:start position:0%
there okay so what that
 

00:01:48.799 --> 00:01:49.590 align:start position:0%
there okay so what that
does

00:01:49.590 --> 00:01:49.600 align:start position:0%
does
 

00:01:49.600 --> 00:01:51.830 align:start position:0%
does
pointing<00:01:49.920><c> from</c><00:01:50.159><c> namecheap</c><00:01:50.640><c> to</c><00:01:50.799><c> cloudflare</c><00:01:51.520><c> to</c>

00:01:51.830 --> 00:01:51.840 align:start position:0%
pointing from namecheap to cloudflare to
 

00:01:51.840 --> 00:01:54.950 align:start position:0%
pointing from namecheap to cloudflare to
my<00:01:52.560><c> digitalocean</c><00:01:52.560><c> digitaloceanip</c>

00:01:54.950 --> 00:01:54.960 align:start position:0%
my digitalocean digitaloceanip
 

00:01:54.960 --> 00:01:57.109 align:start position:0%
my digitalocean digitaloceanip
now<00:01:55.200><c> enables</c><00:01:55.759><c> me</c><00:01:55.920><c> to</c><00:01:56.079><c> do</c><00:01:56.399><c> something</c><00:01:56.719><c> like</c><00:01:56.880><c> this</c>

00:01:57.109 --> 00:01:57.119 align:start position:0%
now enables me to do something like this
 

00:01:57.119 --> 00:01:59.350 align:start position:0%
now enables me to do something like this
instead<00:01:57.439><c> of</c><00:01:57.600><c> telling</c><00:01:57.920><c> people</c><00:01:58.240><c> to</c><00:01:58.399><c> go</c><00:01:58.640><c> to</c>

00:01:59.350 --> 00:01:59.360 align:start position:0%
instead of telling people to go to
 

00:01:59.360 --> 00:02:01.990 align:start position:0%
instead of telling people to go to
the<00:01:59.600><c> ip</c><00:02:00.000><c> address</c><00:02:00.719><c> to</c><00:02:00.960><c> access</c><00:02:01.360><c> the</c><00:02:01.520><c> wordpress</c>

00:02:01.990 --> 00:02:02.000 align:start position:0%
the ip address to access the wordpress
 

00:02:02.000 --> 00:02:03.910 align:start position:0%
the ip address to access the wordpress
site<00:02:02.640><c> they</c><00:02:02.799><c> can</c><00:02:02.960><c> now</c><00:02:03.200><c> just</c><00:02:03.439><c> go</c><00:02:03.680><c> on</c>

00:02:03.910 --> 00:02:03.920 align:start position:0%
site they can now just go on
 

00:02:03.920 --> 00:02:05.670 align:start position:0%
site they can now just go on
kingcommerce.biz

00:02:05.670 --> 00:02:05.680 align:start position:0%
kingcommerce.biz
 

00:02:05.680 --> 00:02:07.910 align:start position:0%
kingcommerce.biz
and<00:02:05.840><c> that</c><00:02:06.000><c> will</c><00:02:06.240><c> point</c><00:02:06.640><c> at</c><00:02:06.719><c> the</c><00:02:06.960><c> exact</c><00:02:07.360><c> same</c>

00:02:07.910 --> 00:02:07.920 align:start position:0%
and that will point at the exact same
 

00:02:07.920 --> 00:02:10.229 align:start position:0%
and that will point at the exact same
wordpress<00:02:08.560><c> site</c><00:02:08.800><c> that</c><00:02:08.879><c> we're</c><00:02:09.039><c> talking</c><00:02:09.440><c> about</c>

00:02:10.229 --> 00:02:10.239 align:start position:0%
wordpress site that we're talking about
 

00:02:10.239 --> 00:02:12.390 align:start position:0%
wordpress site that we're talking about
the<00:02:10.399><c> second</c><00:02:11.120><c> uh</c><00:02:11.680><c> reason</c><00:02:11.920><c> why</c><00:02:12.080><c> that's</c>

00:02:12.390 --> 00:02:12.400 align:start position:0%
the second uh reason why that's
 

00:02:12.400 --> 00:02:15.110 align:start position:0%
the second uh reason why that's
important<00:02:12.720><c> is</c><00:02:12.879><c> if</c><00:02:13.040><c> we</c><00:02:13.200><c> go</c><00:02:13.360><c> into</c><00:02:14.160><c> our</c><00:02:14.560><c> p</c><00:02:14.720><c> address</c>

00:02:15.110 --> 00:02:15.120 align:start position:0%
important is if we go into our p address
 

00:02:15.120 --> 00:02:18.070 align:start position:0%
important is if we go into our p address
slash<00:02:15.440><c> wp-admin</c>

00:02:18.070 --> 00:02:18.080 align:start position:0%
slash wp-admin
 

00:02:18.080 --> 00:02:20.550 align:start position:0%
slash wp-admin
it's<00:02:18.239><c> gonna</c><00:02:18.480><c> immediately</c><00:02:19.040><c> point</c><00:02:19.360><c> us</c><00:02:19.920><c> to</c><00:02:20.239><c> king</c>

00:02:20.550 --> 00:02:20.560 align:start position:0%
it's gonna immediately point us to king
 

00:02:20.560 --> 00:02:22.150 align:start position:0%
it's gonna immediately point us to king
commerce.biz

00:02:22.150 --> 00:02:22.160 align:start position:0%
commerce.biz
 

00:02:22.160 --> 00:02:25.510 align:start position:0%
commerce.biz
so<00:02:22.640><c> that's</c><00:02:23.040><c> uh</c><00:02:23.360><c> the</c><00:02:23.680><c> official</c><00:02:24.080><c> url</c><00:02:25.200><c> of</c><00:02:25.360><c> the</c>

00:02:25.510 --> 00:02:25.520 align:start position:0%
so that's uh the official url of the
 

00:02:25.520 --> 00:02:27.350 align:start position:0%
so that's uh the official url of the
wordpress<00:02:26.400><c> instance</c><00:02:26.800><c> so</c><00:02:26.959><c> i'm</c><00:02:27.040><c> going</c><00:02:27.120><c> to</c><00:02:27.200><c> go</c>

00:02:27.350 --> 00:02:27.360 align:start position:0%
wordpress instance so i'm going to go
 

00:02:27.360 --> 00:02:28.710 align:start position:0%
wordpress instance so i'm going to go
ahead<00:02:27.520><c> and</c><00:02:27.760><c> enter</c><00:02:28.000><c> the</c><00:02:28.080><c> username</c><00:02:28.560><c> and</c>

00:02:28.710 --> 00:02:28.720 align:start position:0%
ahead and enter the username and
 

00:02:28.720 --> 00:02:30.470 align:start position:0%
ahead and enter the username and
password<00:02:29.520><c> that</c><00:02:29.680><c> i</c><00:02:29.840><c> entered</c><00:02:30.160><c> through</c><00:02:30.400><c> the</c>

00:02:30.470 --> 00:02:30.480 align:start position:0%
password that i entered through the
 

00:02:30.480 --> 00:02:33.589 align:start position:0%
password that i entered through the
command<00:02:30.959><c> line</c><00:02:31.360><c> in</c><00:02:31.440><c> the</c><00:02:31.599><c> last</c><00:02:31.920><c> section</c>

00:02:33.589 --> 00:02:33.599 align:start position:0%
command line in the last section
 

00:02:33.599 --> 00:02:38.630 align:start position:0%
command line in the last section
okay

00:02:38.630 --> 00:02:38.640 align:start position:0%
 
 

00:02:38.640 --> 00:02:40.710 align:start position:0%
 
remember<00:02:39.040><c> me</c><00:02:39.280><c> and</c><00:02:39.440><c> login</c><00:02:39.920><c> and</c><00:02:40.000><c> this</c><00:02:40.160><c> is</c><00:02:40.319><c> a</c><00:02:40.400><c> very</c>

00:02:40.710 --> 00:02:40.720 align:start position:0%
remember me and login and this is a very
 

00:02:40.720 --> 00:02:43.589 align:start position:0%
remember me and login and this is a very
important<00:02:41.200><c> part</c><00:02:41.519><c> over</c><00:02:41.760><c> here</c>

00:02:43.589 --> 00:02:43.599 align:start position:0%
important part over here
 

00:02:43.599 --> 00:02:45.270 align:start position:0%
important part over here
um<00:02:44.080><c> you're</c><00:02:44.239><c> going</c><00:02:44.319><c> to</c><00:02:44.400><c> want</c><00:02:44.560><c> to</c><00:02:44.640><c> go</c><00:02:44.800><c> into</c><00:02:45.040><c> the</c>

00:02:45.270 --> 00:02:45.280 align:start position:0%
um you're going to want to go into the
 

00:02:45.280 --> 00:02:47.030 align:start position:0%
um you're going to want to go into the
settings<00:02:45.680><c> section</c>

00:02:47.030 --> 00:02:47.040 align:start position:0%
settings section
 

00:02:47.040 --> 00:02:49.190 align:start position:0%
settings section
and<00:02:47.440><c> you</c><00:02:47.599><c> want</c><00:02:47.760><c> to</c><00:02:47.920><c> change</c><00:02:48.239><c> the</c><00:02:48.400><c> site</c><00:02:48.800><c> address</c>

00:02:49.190 --> 00:02:49.200 align:start position:0%
and you want to change the site address
 

00:02:49.200 --> 00:02:50.630 align:start position:0%
and you want to change the site address
from<00:02:49.440><c> http</c>

00:02:50.630 --> 00:02:50.640 align:start position:0%
from http
 

00:02:50.640 --> 00:02:53.750 align:start position:0%
from http
to<00:02:51.160><c> https</c><00:02:52.480><c> over</c><00:02:52.720><c> here</c>

00:02:53.750 --> 00:02:53.760 align:start position:0%
to https over here
 

00:02:53.760 --> 00:02:54.550 align:start position:0%
to https over here
and

00:02:54.550 --> 00:02:54.560 align:start position:0%
and
 

00:02:54.560 --> 00:02:56.309 align:start position:0%
and
that's<00:02:54.879><c> important</c><00:02:55.360><c> in</c><00:02:55.519><c> order</c><00:02:55.680><c> to</c><00:02:55.840><c> be</c><00:02:56.000><c> able</c><00:02:56.160><c> to</c>

00:02:56.309 --> 00:02:56.319 align:start position:0%
that's important in order to be able to
 

00:02:56.319 --> 00:02:58.949 align:start position:0%
that's important in order to be able to
make<00:02:56.640><c> updates</c><00:02:57.200><c> and</c><00:02:57.680><c> i'll</c><00:02:58.159><c> um</c>

00:02:58.949 --> 00:02:58.959 align:start position:0%
make updates and i'll um
 

00:02:58.959 --> 00:03:01.509 align:start position:0%
make updates and i'll um
i<00:02:59.120><c> can</c><00:02:59.360><c> illustrate</c><00:03:00.319><c> why</c><00:03:00.560><c> that's</c><00:03:00.879><c> important</c>

00:03:01.509 --> 00:03:01.519 align:start position:0%
i can illustrate why that's important
 

00:03:01.519 --> 00:03:03.589 align:start position:0%
i can illustrate why that's important
over<00:03:01.760><c> here</c><00:03:02.319><c> let's</c><00:03:02.480><c> say</c><00:03:02.640><c> i</c><00:03:02.800><c> try</c><00:03:02.959><c> to</c><00:03:03.280><c> create</c><00:03:03.519><c> a</c>

00:03:03.589 --> 00:03:03.599 align:start position:0%
over here let's say i try to create a
 

00:03:03.599 --> 00:03:05.509 align:start position:0%
over here let's say i try to create a
new<00:03:03.840><c> post</c><00:03:04.239><c> over</c><00:03:04.480><c> here</c><00:03:04.800><c> right</c>

00:03:05.509 --> 00:03:05.519 align:start position:0%
new post over here right
 

00:03:05.519 --> 00:03:08.149 align:start position:0%
new post over here right
and<00:03:05.680><c> i</c><00:03:05.760><c> want</c><00:03:05.920><c> to</c><00:03:06.080><c> edit</c><00:03:06.319><c> the</c><00:03:06.400><c> hello</c><00:03:06.800><c> world</c><00:03:07.120><c> post</c>

00:03:08.149 --> 00:03:08.159 align:start position:0%
and i want to edit the hello world post
 

00:03:08.159 --> 00:03:09.990 align:start position:0%
and i want to edit the hello world post
i<00:03:08.319><c> can</c><00:03:08.560><c> open</c><00:03:08.879><c> up</c><00:03:09.040><c> something</c><00:03:09.360><c> called</c><00:03:09.680><c> the</c>

00:03:09.990 --> 00:03:10.000 align:start position:0%
i can open up something called the
 

00:03:10.000 --> 00:03:12.869 align:start position:0%
i can open up something called the
developer<00:03:10.720><c> tools</c>

00:03:12.869 --> 00:03:12.879 align:start position:0%
developer tools
 

00:03:12.879 --> 00:03:15.030 align:start position:0%
developer tools
developer<00:03:13.599><c> tools</c>

00:03:15.030 --> 00:03:15.040 align:start position:0%
developer tools
 

00:03:15.040 --> 00:03:18.550 align:start position:0%
developer tools
and<00:03:15.680><c> let's</c><00:03:15.920><c> say</c><00:03:16.159><c> i</c><00:03:16.400><c> try</c><00:03:16.840><c> to</c><00:03:17.920><c> as</c><00:03:18.159><c> you</c><00:03:18.239><c> can</c><00:03:18.319><c> see</c><00:03:18.480><c> it</c>

00:03:18.550 --> 00:03:18.560 align:start position:0%
and let's say i try to as you can see it
 

00:03:18.560 --> 00:03:21.589 align:start position:0%
and let's say i try to as you can see it
says<00:03:19.280><c> mixed</c><00:03:19.680><c> content</c><00:03:20.720><c> right</c>

00:03:21.589 --> 00:03:21.599 align:start position:0%
says mixed content right
 

00:03:21.599 --> 00:03:24.149 align:start position:0%
says mixed content right
and<00:03:22.159><c> what</c><00:03:22.400><c> it</c><00:03:22.480><c> says</c><00:03:22.720><c> over</c><00:03:23.040><c> here</c><00:03:23.280><c> was</c>

00:03:24.149 --> 00:03:24.159 align:start position:0%
and what it says over here was
 

00:03:24.159 --> 00:03:25.830 align:start position:0%
and what it says over here was
um

00:03:25.830 --> 00:03:25.840 align:start position:0%
um
 

00:03:25.840 --> 00:03:28.309 align:start position:0%
um
this<00:03:26.159><c> page</c><00:03:26.400><c> was</c><00:03:26.640><c> loaded</c><00:03:26.959><c> over</c><00:03:27.200><c> https</c><00:03:28.159><c> but</c>

00:03:28.309 --> 00:03:28.319 align:start position:0%
this page was loaded over https but
 

00:03:28.319 --> 00:03:31.509 align:start position:0%
this page was loaded over https but
requested<00:03:28.879><c> an</c><00:03:29.120><c> http</c><00:03:30.000><c> an</c><00:03:30.319><c> insecure</c><00:03:30.959><c> resource</c>

00:03:31.509 --> 00:03:31.519 align:start position:0%
requested an http an insecure resource
 

00:03:31.519 --> 00:03:34.710 align:start position:0%
requested an http an insecure resource
at<00:03:31.760><c> http</c><00:03:32.959><c> so</c><00:03:33.120><c> that's</c><00:03:33.440><c> important</c><00:03:34.400><c> what</c><00:03:34.560><c> it</c>

00:03:34.710 --> 00:03:34.720 align:start position:0%
at http so that's important what it
 

00:03:34.720 --> 00:03:37.270 align:start position:0%
at http so that's important what it
means<00:03:35.280><c> is</c><00:03:35.519><c> that</c><00:03:35.760><c> i</c><00:03:35.920><c> can't</c><00:03:36.239><c> actually</c><00:03:36.799><c> update</c>

00:03:37.270 --> 00:03:37.280 align:start position:0%
means is that i can't actually update
 

00:03:37.280 --> 00:03:39.509 align:start position:0%
means is that i can't actually update
anything<00:03:37.920><c> let's</c><00:03:38.159><c> say</c><00:03:38.319><c> i</c><00:03:38.480><c> try</c><00:03:38.640><c> to</c><00:03:38.720><c> do</c><00:03:39.040><c> update</c>

00:03:39.509 --> 00:03:39.519 align:start position:0%
anything let's say i try to do update
 

00:03:39.519 --> 00:03:41.110 align:start position:0%
anything let's say i try to do update
this

00:03:41.110 --> 00:03:41.120 align:start position:0%
this
 

00:03:41.120 --> 00:03:42.550 align:start position:0%
this
it's<00:03:41.280><c> not</c><00:03:41.519><c> going</c><00:03:41.599><c> to</c><00:03:41.760><c> allow</c><00:03:42.000><c> me</c><00:03:42.159><c> to</c><00:03:42.319><c> have</c><00:03:42.480><c> a</c>

00:03:42.550 --> 00:03:42.560 align:start position:0%
it's not going to allow me to have a
 

00:03:42.560 --> 00:03:43.910 align:start position:0%
it's not going to allow me to have a
look<00:03:42.640><c> at</c><00:03:42.720><c> what</c><00:03:42.879><c> happens</c><00:03:43.120><c> on</c><00:03:43.280><c> the</c><00:03:43.440><c> errors</c><00:03:43.840><c> on</c>

00:03:43.910 --> 00:03:43.920 align:start position:0%
look at what happens on the errors on
 

00:03:43.920 --> 00:03:45.750 align:start position:0%
look at what happens on the errors on
the<00:03:44.000><c> right</c><00:03:44.319><c> side</c>

00:03:45.750 --> 00:03:45.760 align:start position:0%
the right side
 

00:03:45.760 --> 00:03:47.910 align:start position:0%
the right side
updating<00:03:46.319><c> failed</c><00:03:46.720><c> you</c><00:03:46.879><c> are</c><00:03:47.040><c> probably</c><00:03:47.360><c> offline</c>

00:03:47.910 --> 00:03:47.920 align:start position:0%
updating failed you are probably offline
 

00:03:47.920 --> 00:03:50.470 align:start position:0%
updating failed you are probably offline
no<00:03:48.159><c> i'm</c><00:03:48.319><c> not</c><00:03:48.560><c> offline</c><00:03:49.280><c> i</c><00:03:49.440><c> am</c><00:03:49.599><c> online</c><00:03:49.920><c> but</c><00:03:50.159><c> it</c><00:03:50.400><c> it</c>

00:03:50.470 --> 00:03:50.480 align:start position:0%
no i'm not offline i am online but it it
 

00:03:50.480 --> 00:03:53.270 align:start position:0%
no i'm not offline i am online but it it
keeps<00:03:50.799><c> running</c><00:03:51.040><c> this</c><00:03:51.760><c> this</c><00:03:52.319><c> mixed</c><00:03:52.640><c> content</c>

00:03:53.270 --> 00:03:53.280 align:start position:0%
keeps running this this mixed content
 

00:03:53.280 --> 00:03:55.350 align:start position:0%
keeps running this this mixed content
warning<00:03:53.760><c> so</c><00:03:54.080><c> in</c><00:03:54.239><c> order</c><00:03:54.480><c> to</c><00:03:54.560><c> get</c><00:03:54.720><c> around</c><00:03:55.040><c> that</c><00:03:55.280><c> i</c>

00:03:55.350 --> 00:03:55.360 align:start position:0%
warning so in order to get around that i
 

00:03:55.360 --> 00:03:57.990 align:start position:0%
warning so in order to get around that i
need<00:03:55.519><c> to</c><00:03:55.680><c> go</c><00:03:55.920><c> in</c><00:03:56.080><c> here</c><00:03:56.480><c> and</c><00:03:56.879><c> don't</c><00:03:57.360><c> update</c><00:03:57.760><c> your</c>

00:03:57.990 --> 00:03:58.000 align:start position:0%
need to go in here and don't update your
 

00:03:58.000 --> 00:04:00.869 align:start position:0%
need to go in here and don't update your
wordpress<00:03:58.560><c> address</c><00:03:59.040><c> url</c><00:04:00.159><c> don't</c><00:04:00.400><c> touch</c><00:04:00.720><c> it</c>

00:04:00.869 --> 00:04:00.879 align:start position:0%
wordpress address url don't touch it
 

00:04:00.879 --> 00:04:03.990 align:start position:0%
wordpress address url don't touch it
only<00:04:01.200><c> touch</c><00:04:01.439><c> this</c><00:04:01.680><c> part</c><00:04:02.080><c> site</c><00:04:02.480><c> address</c><00:04:03.360><c> url</c>

00:04:03.990 --> 00:04:04.000 align:start position:0%
only touch this part site address url
 

00:04:04.000 --> 00:04:06.470 align:start position:0%
only touch this part site address url
and<00:04:04.239><c> update</c><00:04:04.640><c> it</c><00:04:04.720><c> to</c><00:04:04.879><c> https</c>

00:04:06.470 --> 00:04:06.480 align:start position:0%
and update it to https
 

00:04:06.480 --> 00:04:08.789 align:start position:0%
and update it to https
and<00:04:06.560><c> that's</c><00:04:06.879><c> pretty</c><00:04:07.040><c> much</c><00:04:07.280><c> the</c><00:04:07.439><c> fix</c><00:04:08.000><c> i</c><00:04:08.159><c> press</c>

00:04:08.789 --> 00:04:08.799 align:start position:0%
and that's pretty much the fix i press
 

00:04:08.799 --> 00:04:12.070 align:start position:0%
and that's pretty much the fix i press
save<00:04:09.200><c> changes</c><00:04:10.159><c> i'm</c><00:04:10.400><c> gonna</c><00:04:11.040><c> reload</c><00:04:11.599><c> the</c><00:04:11.760><c> page</c>

00:04:12.070 --> 00:04:12.080 align:start position:0%
save changes i'm gonna reload the page
 

00:04:12.080 --> 00:04:15.030 align:start position:0%
save changes i'm gonna reload the page
over<00:04:12.319><c> here</c><00:04:12.879><c> yes</c><00:04:13.200><c> i</c><00:04:13.360><c> do</c><00:04:13.519><c> want</c><00:04:13.680><c> to</c><00:04:13.840><c> reload</c><00:04:14.319><c> it</c>

00:04:15.030 --> 00:04:15.040 align:start position:0%
over here yes i do want to reload it
 

00:04:15.040 --> 00:04:16.789 align:start position:0%
over here yes i do want to reload it
and<00:04:15.120><c> have</c><00:04:15.360><c> a</c><00:04:15.439><c> look</c><00:04:15.680><c> all</c><00:04:15.840><c> of</c><00:04:15.920><c> a</c><00:04:16.000><c> sudden</c><00:04:16.400><c> we</c><00:04:16.560><c> don't</c>

00:04:16.789 --> 00:04:16.799 align:start position:0%
and have a look all of a sudden we don't
 

00:04:16.799 --> 00:04:19.110 align:start position:0%
and have a look all of a sudden we don't
have<00:04:17.120><c> that</c><00:04:17.359><c> crazy</c><00:04:17.840><c> error</c><00:04:18.160><c> on</c><00:04:18.320><c> the</c><00:04:18.400><c> right</c><00:04:18.720><c> side</c>

00:04:19.110 --> 00:04:19.120 align:start position:0%
have that crazy error on the right side
 

00:04:19.120 --> 00:04:21.590 align:start position:0%
have that crazy error on the right side
which<00:04:19.280><c> is</c><00:04:19.440><c> the</c><00:04:19.519><c> mixed</c><00:04:19.840><c> content</c><00:04:20.720><c> http</c><00:04:21.359><c> versus</c>

00:04:21.590 --> 00:04:21.600 align:start position:0%
which is the mixed content http versus
 

00:04:21.600 --> 00:04:26.150 align:start position:0%
which is the mixed content http versus
https<00:04:22.639><c> warning</c><00:04:23.360><c> i</c><00:04:23.600><c> go</c><00:04:23.759><c> ahead</c><00:04:24.000><c> and</c><00:04:24.160><c> press</c>

00:04:26.150 --> 00:04:26.160 align:start position:0%
https warning i go ahead and press
 

00:04:26.160 --> 00:04:31.430 align:start position:0%
https warning i go ahead and press
press<00:04:26.840><c> update</c><00:04:27.840><c> this</c><00:04:28.560><c> update</c><00:04:29.199><c> this</c>

00:04:31.430 --> 00:04:31.440 align:start position:0%
press update this update this
 

00:04:31.440 --> 00:04:33.590 align:start position:0%
press update this update this
next<00:04:32.080><c> update</c>

00:04:33.590 --> 00:04:33.600 align:start position:0%
next update
 

00:04:33.600 --> 00:04:36.790 align:start position:0%
next update
this<00:04:34.320><c> and</c><00:04:34.639><c> press</c><00:04:35.040><c> update</c>

00:04:36.790 --> 00:04:36.800 align:start position:0%
this and press update
 

00:04:36.800 --> 00:04:39.830 align:start position:0%
this and press update
okay<00:04:37.120><c> so</c><00:04:37.280><c> that's</c><00:04:37.600><c> cool</c><00:04:38.080><c> let's</c><00:04:38.240><c> say</c><00:04:38.400><c> i</c><00:04:38.560><c> go</c><00:04:38.800><c> to</c>

00:04:39.830 --> 00:04:39.840 align:start position:0%
okay so that's cool let's say i go to
 

00:04:39.840 --> 00:04:42.629 align:start position:0%
okay so that's cool let's say i go to
the<00:04:40.160><c> website</c><00:04:40.639><c> and</c><00:04:40.800><c> reload</c><00:04:41.680><c> and</c><00:04:41.840><c> there</c><00:04:42.160><c> it</c><00:04:42.320><c> is</c>

00:04:42.629 --> 00:04:42.639 align:start position:0%
the website and reload and there it is
 

00:04:42.639 --> 00:04:44.390 align:start position:0%
the website and reload and there it is
update<00:04:43.040><c> this</c><00:04:43.280><c> it's</c><00:04:43.440><c> been</c><00:04:43.680><c> successfully</c>

00:04:44.390 --> 00:04:44.400 align:start position:0%
update this it's been successfully
 

00:04:44.400 --> 00:04:46.629 align:start position:0%
update this it's been successfully
updated<00:04:45.040><c> all</c><00:04:45.120><c> right</c><00:04:45.280><c> so</c><00:04:45.440><c> that's</c><00:04:45.680><c> pretty</c><00:04:45.919><c> much</c>

00:04:46.629 --> 00:04:46.639 align:start position:0%
updated all right so that's pretty much
 

00:04:46.639 --> 00:04:50.070 align:start position:0%
updated all right so that's pretty much
uh<00:04:47.199><c> how</c><00:04:47.360><c> we</c><00:04:47.520><c> do</c><00:04:47.759><c> that</c><00:04:48.400><c> again</c><00:04:48.720><c> from</c><00:04:49.040><c> namecheap</c>

00:04:50.070 --> 00:04:50.080 align:start position:0%
uh how we do that again from namecheap
 

00:04:50.080 --> 00:04:51.909 align:start position:0%
uh how we do that again from namecheap
we<00:04:50.320><c> point</c><00:04:50.960><c> to</c>

00:04:51.909 --> 00:04:51.919 align:start position:0%
we point to
 

00:04:51.919 --> 00:04:53.909 align:start position:0%
we point to
uh<00:04:52.160><c> cloudflare</c><00:04:52.880><c> from</c><00:04:53.040><c> cloudflare</c><00:04:53.680><c> we</c><00:04:53.840><c> have</c>

00:04:53.909 --> 00:04:53.919 align:start position:0%
uh cloudflare from cloudflare we have
 

00:04:53.919 --> 00:04:55.590 align:start position:0%
uh cloudflare from cloudflare we have
these<00:04:54.240><c> two</c><00:04:54.479><c> records</c>

00:04:55.590 --> 00:04:55.600 align:start position:0%
these two records
 

00:04:55.600 --> 00:04:56.950 align:start position:0%
these two records
and

00:04:56.950 --> 00:04:56.960 align:start position:0%
and
 

00:04:56.960 --> 00:04:58.710 align:start position:0%
and
and<00:04:57.040><c> we</c><00:04:57.280><c> point</c><00:04:57.440><c> that</c><00:04:57.680><c> at</c><00:04:57.759><c> the</c><00:04:57.919><c> ip</c><00:04:58.560><c> of</c>

00:04:58.710 --> 00:04:58.720 align:start position:0%
and we point that at the ip of
 

00:04:58.720 --> 00:05:01.720 align:start position:0%
and we point that at the ip of
digitalocean

