WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:03.050 align:start position:0%
 
welcome<00:00:00.329><c> back</c><00:00:00.510><c> so</c><00:00:00.719><c> and</c><00:00:00.930><c> this</c><00:00:01.079><c> and</c><00:00:01.790><c> in</c><00:00:02.790><c> this</c>

00:00:03.050 --> 00:00:03.060 align:start position:0%
welcome back so and this and in this
 

00:00:03.060 --> 00:00:04.460 align:start position:0%
welcome back so and this and in this
video<00:00:03.389><c> we're</c><00:00:03.750><c> going</c><00:00:03.840><c> to</c><00:00:03.899><c> be</c><00:00:04.020><c> talking</c><00:00:04.259><c> about</c>

00:00:04.460 --> 00:00:04.470 align:start position:0%
video we're going to be talking about
 

00:00:04.470 --> 00:00:08.000 align:start position:0%
video we're going to be talking about
how<00:00:04.770><c> we're</c><00:00:05.009><c> saving</c><00:00:05.609><c> the</c><00:00:06.060><c> theme</c><00:00:07.049><c> of</c><00:00:07.080><c> the</c><00:00:07.440><c> client</c>

00:00:08.000 --> 00:00:08.010 align:start position:0%
how we're saving the theme of the client
 

00:00:08.010 --> 00:00:10.490 align:start position:0%
how we're saving the theme of the client
in<00:00:08.460><c> the</c><00:00:08.880><c> database</c><00:00:09.360><c> and</c><00:00:09.540><c> depending</c><00:00:10.110><c> on</c><00:00:10.170><c> what</c><00:00:10.349><c> we</c>

00:00:10.490 --> 00:00:10.500 align:start position:0%
in the database and depending on what we
 

00:00:10.500 --> 00:00:13.039 align:start position:0%
in the database and depending on what we
save<00:00:10.710><c> the</c><00:00:10.920><c> theme</c><00:00:11.190><c> the</c><00:00:11.700><c> theme</c><00:00:12.090><c> of</c><00:00:12.330><c> the</c><00:00:12.570><c> client</c>

00:00:13.039 --> 00:00:13.049 align:start position:0%
save the theme the theme of the client
 

00:00:13.049 --> 00:00:15.589 align:start position:0%
save the theme the theme of the client
has<00:00:13.200><c> in</c><00:00:13.440><c> the</c><00:00:13.530><c> database</c><00:00:14.089><c> that's</c><00:00:15.089><c> what</c><00:00:15.420><c> its</c>

00:00:15.589 --> 00:00:15.599 align:start position:0%
has in the database that's what its
 

00:00:15.599 --> 00:00:17.960 align:start position:0%
has in the database that's what its
gonna<00:00:15.780><c> that's</c><00:00:16.170><c> what's</c><00:00:16.410><c> gonna</c><00:00:16.590><c> appear</c><00:00:17.220><c> with</c>

00:00:17.960 --> 00:00:17.970 align:start position:0%
gonna that's what's gonna appear with
 

00:00:17.970 --> 00:00:20.420 align:start position:0%
gonna that's what's gonna appear with
when<00:00:18.690><c> we</c><00:00:18.840><c> refresh</c><00:00:19.230><c> the</c><00:00:19.590><c> page</c><00:00:19.770><c> so</c><00:00:19.980><c> let's</c><00:00:20.189><c> do</c>

00:00:20.420 --> 00:00:20.430 align:start position:0%
when we refresh the page so let's do
 

00:00:20.430 --> 00:00:23.509 align:start position:0%
when we refresh the page so let's do
lumen<00:00:20.970><c> update</c><00:00:21.810><c> client</c><00:00:22.140><c> set</c><00:00:22.590><c> client</c><00:00:23.039><c> theme</c><00:00:23.279><c> to</c>

00:00:23.509 --> 00:00:23.519 align:start position:0%
lumen update client set client theme to
 

00:00:23.519 --> 00:00:27.529 align:start position:0%
lumen update client set client theme to
lumen<00:00:23.970><c> where</c><00:00:24.570><c> client</c><00:00:25.080><c> ID</c><00:00:25.230><c> was</c><00:00:25.680><c> eight</c><00:00:25.980><c> and</c><00:00:26.539><c> that</c>

00:00:27.529 --> 00:00:27.539 align:start position:0%
lumen where client ID was eight and that
 

00:00:27.539 --> 00:00:29.330 align:start position:0%
lumen where client ID was eight and that
ran<00:00:28.019><c> smoothly</c><00:00:28.470><c> and</c><00:00:28.949><c> now</c><00:00:29.039><c> we're</c><00:00:29.189><c> going</c><00:00:29.279><c> to</c>

00:00:29.330 --> 00:00:29.340 align:start position:0%
ran smoothly and now we're going to
 

00:00:29.340 --> 00:00:33.200 align:start position:0%
ran smoothly and now we're going to
refresh<00:00:29.820><c> the</c><00:00:30.060><c> page</c><00:00:30.240><c> over</c><00:00:30.510><c> here</c><00:00:30.630><c> and</c><00:00:31.340><c> the</c><00:00:32.340><c> the</c>

00:00:33.200 --> 00:00:33.210 align:start position:0%
refresh the page over here and the the
 

00:00:33.210 --> 00:00:36.530 align:start position:0%
refresh the page over here and the the
client<00:00:33.870><c> service</c><00:00:34.380><c> is</c><00:00:34.649><c> going</c><00:00:35.550><c> to</c><00:00:35.850><c> automatically</c>

00:00:36.530 --> 00:00:36.540 align:start position:0%
client service is going to automatically
 

00:00:36.540 --> 00:00:40.160 align:start position:0%
client service is going to automatically
fetch<00:00:37.610><c> the</c><00:00:38.610><c> client</c><00:00:39.210><c> theme</c><00:00:39.450><c> from</c><00:00:39.690><c> the</c><00:00:39.780><c> database</c>

00:00:40.160 --> 00:00:40.170 align:start position:0%
fetch the client theme from the database
 

00:00:40.170 --> 00:00:42.170 align:start position:0%
fetch the client theme from the database
and<00:00:40.200><c> it's</c><00:00:40.469><c> going</c><00:00:40.620><c> to</c><00:00:40.700><c> automatically</c><00:00:41.700><c> set</c><00:00:41.969><c> that</c>

00:00:42.170 --> 00:00:42.180 align:start position:0%
and it's going to automatically set that
 

00:00:42.180 --> 00:00:47.840 align:start position:0%
and it's going to automatically set that
as<00:00:42.420><c> the</c><00:00:42.719><c> as</c><00:00:43.260><c> the</c><00:00:43.800><c> as</c><00:00:44.480><c> the</c><00:00:45.480><c> href</c><00:00:46.170><c> ng</c><00:00:46.770><c> h</c><00:00:47.010><c> RF</c><00:00:47.370><c> over</c>

00:00:47.840 --> 00:00:47.850 align:start position:0%
as the as the as the href ng h RF over
 

00:00:47.850 --> 00:00:51.139 align:start position:0%
as the as the as the href ng h RF over
here<00:00:48.210><c> and</c><00:00:48.899><c> G</c><00:00:49.140><c> H</c><00:00:49.260><c> F</c><00:00:49.500><c> equals</c><00:00:49.980><c> max</c><00:00:50.340><c> CDN</c><00:00:50.850><c> /</c>

00:00:51.139 --> 00:00:51.149 align:start position:0%
here and G H F equals max CDN /
 

00:00:51.149 --> 00:00:53.869 align:start position:0%
here and G H F equals max CDN /
bootstraps<00:00:51.719><c> last</c><00:00:52.050><c> three</c><00:00:52.260><c> 407</c><00:00:52.949><c> slash</c><00:00:53.219><c> my</c><00:00:53.520><c> theme</c>

00:00:53.869 --> 00:00:53.879 align:start position:0%
bootstraps last three 407 slash my theme
 

00:00:53.879 --> 00:00:57.200 align:start position:0%
bootstraps last three 407 slash my theme
which<00:00:54.120><c> is</c><00:00:54.270><c> a</c><00:00:54.300><c> root</c><00:00:54.660><c> scope</c><00:00:55.190><c> object</c><00:00:56.190><c> so</c><00:00:56.610><c> that</c>

00:00:57.200 --> 00:00:57.210 align:start position:0%
which is a root scope object so that
 

00:00:57.210 --> 00:00:59.000 align:start position:0%
which is a root scope object so that
takes<00:00:57.480><c> care</c><00:00:57.629><c> of</c><00:00:57.840><c> that</c><00:00:58.109><c> and</c><00:00:58.170><c> now</c><00:00:58.500><c> we</c><00:00:58.559><c> want</c><00:00:58.890><c> to</c>

00:00:59.000 --> 00:00:59.010 align:start position:0%
takes care of that and now we want to
 

00:00:59.010 --> 00:01:00.529 align:start position:0%
takes care of that and now we want to
set<00:00:59.190><c> it</c><00:00:59.250><c> that</c><00:00:59.370><c> every</c><00:00:59.820><c> time</c><00:00:59.850><c> that</c><00:01:00.059><c> the</c><00:01:00.359><c> user</c>

00:01:00.529 --> 00:01:00.539 align:start position:0%
set it that every time that the user
 

00:01:00.539 --> 00:01:04.640 align:start position:0%
set it that every time that the user
goes<00:01:00.899><c> into</c><00:01:01.199><c> the</c><00:01:01.350><c> themes</c><00:01:01.649><c> here</c><00:01:03.320><c> he's</c><00:01:04.320><c> gonna</c><00:01:04.470><c> be</c>

00:01:04.640 --> 00:01:04.650 align:start position:0%
goes into the themes here he's gonna be
 

00:01:04.650 --> 00:01:06.289 align:start position:0%
goes into the themes here he's gonna be
able<00:01:04.739><c> to</c><00:01:04.860><c> click</c><00:01:05.100><c> on</c><00:01:05.309><c> this</c><00:01:05.460><c> and</c><00:01:05.939><c> the</c><00:01:06.030><c> moment</c>

00:01:06.289 --> 00:01:06.299 align:start position:0%
able to click on this and the moment
 

00:01:06.299 --> 00:01:07.910 align:start position:0%
able to click on this and the moment
that<00:01:06.330><c> he</c><00:01:06.479><c> clicks</c><00:01:06.780><c> on</c><00:01:06.930><c> cyborg</c><00:01:07.470><c> it's</c><00:01:07.740><c> gonna</c>

00:01:07.910 --> 00:01:07.920 align:start position:0%
that he clicks on cyborg it's gonna
 

00:01:07.920 --> 00:01:09.740 align:start position:0%
that he clicks on cyborg it's gonna
update<00:01:08.159><c> his</c><00:01:08.580><c> client</c><00:01:09.090><c> theme</c><00:01:09.299><c> within</c><00:01:09.540><c> the</c>

00:01:09.740 --> 00:01:09.750 align:start position:0%
update his client theme within the
 

00:01:09.750 --> 00:01:12.380 align:start position:0%
update his client theme within the
database<00:01:10.200><c> so</c><00:01:10.470><c> in</c><00:01:11.159><c> order</c><00:01:11.310><c> to</c><00:01:11.520><c> do</c><00:01:11.850><c> that</c><00:01:12.030><c> we</c><00:01:12.240><c> need</c>

00:01:12.380 --> 00:01:12.390 align:start position:0%
database so in order to do that we need
 

00:01:12.390 --> 00:01:16.810 align:start position:0%
database so in order to do that we need
to<00:01:12.479><c> set</c><00:01:12.720><c> up</c><00:01:12.900><c> a</c><00:01:13.700><c> route</c><00:01:14.700><c> within</c><00:01:15.180><c> our</c><00:01:15.900><c> routes</c>

00:01:16.810 --> 00:01:16.820 align:start position:0%
to set up a route within our routes
 

00:01:16.820 --> 00:01:19.130 align:start position:0%
to set up a route within our routes
folder<00:01:17.820><c> which</c><00:01:18.030><c> essentially</c><00:01:18.600><c> takes</c><00:01:18.750><c> care</c><00:01:19.110><c> of</c>

00:01:19.130 --> 00:01:19.140 align:start position:0%
folder which essentially takes care of
 

00:01:19.140 --> 00:01:21.170 align:start position:0%
folder which essentially takes care of
all<00:01:19.350><c> of</c><00:01:19.680><c> this</c><00:01:19.830><c> kind</c><00:01:20.100><c> of</c><00:01:20.220><c> stuff</c><00:01:20.460><c> so</c><00:01:20.490><c> apps</c><00:01:20.850><c> apps</c>

00:01:21.170 --> 00:01:21.180 align:start position:0%
all of this kind of stuff so apps apps
 

00:01:21.180 --> 00:01:25.880 align:start position:0%
all of this kind of stuff so apps apps
lash<00:01:21.600><c> post</c><00:01:22.520><c> okay</c><00:01:23.520><c> let's</c><00:01:23.880><c> do</c><00:01:24.150><c> ab</c><00:01:24.450><c> /</c><00:01:24.720><c> post</c><00:01:25.229><c> update</c>

00:01:25.880 --> 00:01:25.890 align:start position:0%
lash post okay let's do ab / post update
 

00:01:25.890 --> 00:01:29.960 align:start position:0%
lash post okay let's do ab / post update
theme<00:01:26.220><c> okay</c><00:01:27.320><c> API</c><00:01:28.320><c> slash</c><00:01:28.409><c> no</c><00:01:28.860><c> message</c><00:01:29.369><c> one</c>

00:01:29.960 --> 00:01:29.970 align:start position:0%
theme okay API slash no message one
 

00:01:29.970 --> 00:01:31.149 align:start position:0%
theme okay API slash no message one
second

00:01:31.149 --> 00:01:31.159 align:start position:0%
second
 

00:01:31.159 --> 00:01:35.569 align:start position:0%
second
codes<00:01:33.049><c> so</c><00:01:34.049><c> what</c><00:01:34.259><c> the</c><00:01:34.380><c> abdullah</c><00:01:34.860><c> sh</c><00:01:34.890><c> post</c><00:01:35.280><c> looks</c>

00:01:35.569 --> 00:01:35.579 align:start position:0%
codes so what the abdullah sh post looks
 

00:01:35.579 --> 00:01:38.090 align:start position:0%
codes so what the abdullah sh post looks
like<00:01:35.790><c> over</c><00:01:36.090><c> here</c><00:01:36.119><c> just</c><00:01:36.960><c> for</c><00:01:37.140><c> inspiration</c><00:01:37.350><c> ah</c>

00:01:38.090 --> 00:01:38.100 align:start position:0%
like over here just for inspiration ah
 

00:01:38.100 --> 00:01:39.260 align:start position:0%
like over here just for inspiration ah
perfect

00:01:39.260 --> 00:01:39.270 align:start position:0%
perfect
 

00:01:39.270 --> 00:01:44.749 align:start position:0%
perfect
add<00:01:39.869><c> that</c><00:01:40.140><c> post</c><00:01:40.470><c> API</c><00:01:40.890><c> slash</c><00:01:41.009><c> new</c><00:01:41.490><c> message</c><00:01:43.759><c> have</c>

00:01:44.749 --> 00:01:44.759 align:start position:0%
add that post API slash new message have
 

00:01:44.759 --> 00:01:53.569 align:start position:0%
add that post API slash new message have
slash<00:01:45.270><c> post</c><00:01:48.290><c> update</c><00:01:49.290><c> theme</c><00:01:51.470><c> API</c><00:01:52.470><c> slash</c><00:01:52.619><c> update</c>

00:01:53.569 --> 00:01:53.579 align:start position:0%
slash post update theme API slash update
 

00:01:53.579 --> 00:02:03.480 align:start position:0%
slash post update theme API slash update
theme<00:01:59.960><c> update</c>

00:02:03.480 --> 00:02:03.490 align:start position:0%
 
 

00:02:03.490 --> 00:02:06.280 align:start position:0%
 
and<00:02:04.490><c> every</c><00:02:05.210><c> time</c><00:02:05.330><c> we</c><00:02:05.480><c> do</c><00:02:05.630><c> that</c><00:02:05.870><c> it's</c><00:02:06.170><c> gonna</c>

00:02:06.280 --> 00:02:06.290 align:start position:0%
and every time we do that it's gonna
 

00:02:06.290 --> 00:02:10.480 align:start position:0%
and every time we do that it's gonna
take<00:02:06.530><c> a</c><00:02:06.830><c> request</c><00:02:07.550><c> and</c><00:02:07.700><c> a</c><00:02:07.790><c> response</c><00:02:09.370><c> it's</c><00:02:10.370><c> been</c>

00:02:10.480 --> 00:02:10.490 align:start position:0%
take a request and a response it's been
 

00:02:10.490 --> 00:02:13.930 align:start position:0%
take a request and a response it's been
a<00:02:10.520><c> concert</c><00:02:11.030><c> that</c><00:02:11.060><c> blog</c><00:02:11.650><c> rec</c><00:02:12.650><c> dot</c><00:02:12.890><c> body</c><00:02:13.220><c> that</c>

00:02:13.930 --> 00:02:13.940 align:start position:0%
a concert that blog rec dot body that
 

00:02:13.940 --> 00:02:24.490 align:start position:0%
a concert that blog rec dot body that
message<00:02:20.650><c> and</c><00:02:22.360><c> it's</c><00:02:23.360><c> gonna</c><00:02:23.510><c> do</c><00:02:23.690><c> a</c><00:02:23.720><c> connection</c>

00:02:24.490 --> 00:02:24.500 align:start position:0%
message and it's gonna do a connection
 

00:02:24.500 --> 00:02:31.950 align:start position:0%
message and it's gonna do a connection
query<00:02:27.190><c> something</c><00:02:28.190><c> here</c><00:02:29.290><c> you'll</c><00:02:30.290><c> see</c>

00:02:31.950 --> 00:02:31.960 align:start position:0%
query something here you'll see
 

00:02:31.960 --> 00:02:36.760 align:start position:0%
query something here you'll see
connection<00:02:32.960><c> dot</c><00:02:33.170><c> query</c><00:02:33.590><c> something</c>

00:02:36.760 --> 00:02:36.770 align:start position:0%
 
 

00:02:36.770 --> 00:02:38.650 align:start position:0%
 
what's<00:02:37.250><c> the</c><00:02:37.430><c> query</c><00:02:37.700><c> we're</c><00:02:38.000><c> gonna</c><00:02:38.120><c> grab</c><00:02:38.420><c> the</c>

00:02:38.650 --> 00:02:38.660 align:start position:0%
what's the query we're gonna grab the
 

00:02:38.660 --> 00:02:42.250 align:start position:0%
what's the query we're gonna grab the
query<00:02:39.050><c> from</c><00:02:39.260><c> over</c><00:02:39.710><c> here</c><00:02:40.660><c> control</c><00:02:41.660><c> a</c><00:02:41.900><c> control</c>

00:02:42.250 --> 00:02:42.260 align:start position:0%
query from over here control a control
 

00:02:42.260 --> 00:02:51.180 align:start position:0%
query from over here control a control
there<00:02:47.410><c> wreck</c><00:02:48.410><c> that</c><00:02:48.620><c> body</c><00:02:48.950><c> dot</c><00:02:49.250><c> message</c><00:02:49.760><c> good</c>

00:02:51.180 --> 00:02:51.190 align:start position:0%
there wreck that body dot message good
 

00:02:51.190 --> 00:02:57.340 align:start position:0%
there wreck that body dot message good
connection<00:02:52.190><c> dot</c><00:02:52.370><c> query</c><00:02:53.680><c> yeah</c><00:02:56.200><c> cause</c><00:02:57.200><c> the</c>

00:02:57.340 --> 00:02:57.350 align:start position:0%
connection dot query yeah cause the
 

00:02:57.350 --> 00:03:06.360 align:start position:0%
connection dot query yeah cause the
parentheses<00:02:57.950><c> here</c><00:02:58.690><c> I'm</c><00:02:59.690><c> going</c><00:03:00.140><c> to</c><00:03:00.350><c> pull</c><00:03:04.810><c> me</c>

00:03:06.360 --> 00:03:06.370 align:start position:0%
parentheses here I'm going to pull me
 

00:03:06.370 --> 00:03:10.840 align:start position:0%
parentheses here I'm going to pull me
like<00:03:07.370><c> that</c><00:03:07.580><c> buddy</c><00:03:07.820><c> done</c><00:03:08.090><c> oh</c><00:03:09.430><c> let's</c><00:03:10.430><c> just</c><00:03:10.520><c> save</c>

00:03:10.840 --> 00:03:10.850 align:start position:0%
like that buddy done oh let's just save
 

00:03:10.850 --> 00:03:16.620 align:start position:0%
like that buddy done oh let's just save
take<00:03:11.510><c> this</c><00:03:11.750><c> piece</c><00:03:12.020><c> of</c><00:03:12.170><c> the</c><00:03:12.260><c> query</c><00:03:12.530><c> here</c>

00:03:16.620 --> 00:03:16.630 align:start position:0%
 
 

00:03:16.630 --> 00:03:22.830 align:start position:0%
 
disappear<00:03:19.420><c> like</c><00:03:20.420><c> that</c><00:03:20.480><c> buddy</c><00:03:20.870><c> that</c><00:03:21.200><c> message</c>

00:03:22.830 --> 00:03:22.840 align:start position:0%
disappear like that buddy that message
 

00:03:22.840 --> 00:03:33.690 align:start position:0%
disappear like that buddy that message
like<00:03:23.840><c> that</c><00:03:23.900><c> Bobby</c><00:03:24.320><c> duck</c><00:03:24.590><c> feet</c><00:03:24.950><c> okay</c><00:03:28.360><c> doc</c><00:03:29.360><c> team</c>

00:03:33.690 --> 00:03:33.700 align:start position:0%
 
 

00:03:33.700 --> 00:03:37.000 align:start position:0%
 
update<00:03:34.700><c> clients</c><00:03:35.420><c> that</c><00:03:35.570><c> client</c><00:03:36.050><c> theme</c><00:03:36.350><c> equal</c>

00:03:37.000 --> 00:03:37.010 align:start position:0%
update clients that client theme equal
 

00:03:37.010 --> 00:04:01.080 align:start position:0%
update clients that client theme equal
to<00:03:38.050><c> rec</c><00:03:39.050><c> dot</c><00:03:39.260><c> dot</c><00:03:39.470><c> e</c><00:03:39.500><c> dot</c><00:03:39.590><c> theme</c><00:03:42.700><c> let's</c><00:03:43.700><c> do</c><00:03:43.910><c> that</c>

00:04:01.080 --> 00:04:01.090 align:start position:0%
 
 

00:04:01.090 --> 00:04:06.100 align:start position:0%
 
thus<00:04:02.090><c> racked</c><00:04:02.690><c> up</c><00:04:02.840><c> a</c><00:04:03.050><c> yacht</c><00:04:03.410><c> theme</c><00:04:03.770><c> plus</c><00:04:05.110><c> this</c>

00:04:06.100 --> 00:04:06.110 align:start position:0%
thus racked up a yacht theme plus this
 

00:04:06.110 --> 00:04:16.539 align:start position:0%
thus racked up a yacht theme plus this
where<00:04:06.950><c> client</c><00:04:07.490><c> underscore</c><00:04:08.060><c> ID</c><00:04:08.300><c> equals</c><00:04:15.549><c> it's</c>

00:04:16.539 --> 00:04:16.549 align:start position:0%
where client underscore ID equals it's
 

00:04:16.549 --> 00:04:18.090 align:start position:0%
where client underscore ID equals it's
gonna<00:04:16.670><c> be</c><00:04:16.910><c> in</c><00:04:17.150><c> there</c><00:04:17.450><c> yeah</c>

00:04:18.090 --> 00:04:18.100 align:start position:0%
gonna be in there yeah
 

00:04:18.100 --> 00:04:23.850 align:start position:0%
gonna be in there yeah
what<00:04:19.100><c> client</c><00:04:19.489><c> underscore</c><00:04:19.910><c> ID</c><00:04:20.060><c> equals</c><00:04:20.920><c> write</c>

00:04:23.850 --> 00:04:23.860 align:start position:0%
what client underscore ID equals write
 

00:04:23.860 --> 00:04:30.850 align:start position:0%
what client underscore ID equals write
something<00:04:25.870><c> where</c><00:04:26.870><c> is</c><00:04:26.960><c> reka</c><00:04:27.260><c> user</c><00:04:29.350><c> that</c><00:04:30.350><c> client</c>

00:04:30.850 --> 00:04:30.860 align:start position:0%
something where is reka user that client
 

00:04:30.860 --> 00:04:33.430 align:start position:0%
something where is reka user that client
ID<00:04:30.980><c> ah</c><00:04:31.120><c> crap</c><00:04:32.120><c> this</c><00:04:32.419><c> is</c><00:04:32.600><c> what</c><00:04:32.750><c> we</c><00:04:32.870><c> needed</c><00:04:33.110><c> guys</c>

00:04:33.430 --> 00:04:33.440 align:start position:0%
ID ah crap this is what we needed guys
 

00:04:33.440 --> 00:04:38.580 align:start position:0%
ID ah crap this is what we needed guys
sorry<00:04:35.140><c> you</c><00:04:36.140><c> did</c><00:04:36.290><c> that</c><00:04:36.320><c> exact</c><00:04:36.800><c> syntax</c><00:04:37.130><c> here</c>

00:04:38.580 --> 00:04:38.590 align:start position:0%
sorry you did that exact syntax here
 

00:04:38.590 --> 00:04:42.550 align:start position:0%
sorry you did that exact syntax here
okay<00:04:39.590><c> it's</c><00:04:39.740><c> like</c><00:04:40.810><c> it's</c><00:04:41.810><c> very</c><00:04:41.870><c> important</c><00:04:42.470><c> to</c>

00:04:42.550 --> 00:04:42.560 align:start position:0%
okay it's like it's very important to
 

00:04:42.560 --> 00:04:46.300 align:start position:0%
okay it's like it's very important to
get<00:04:42.710><c> the</c><00:04:42.919><c> syntax</c><00:04:43.820><c> exactly</c><00:04:44.480><c> correct</c><00:04:45.160><c> in</c><00:04:46.160><c> the</c>

00:04:46.300 --> 00:04:46.310 align:start position:0%
get the syntax exactly correct in the
 

00:04:46.310 --> 00:04:48.730 align:start position:0%
get the syntax exactly correct in the
next<00:04:46.669><c> next</c><00:04:47.360><c> video</c><00:04:47.780><c> we're</c><00:04:47.990><c> gonna</c><00:04:48.110><c> continue</c>

00:04:48.730 --> 00:04:48.740 align:start position:0%
next next video we're gonna continue
 

00:04:48.740 --> 00:04:51.670 align:start position:0%
next next video we're gonna continue
this<00:04:48.919><c> and</c><00:04:49.690><c> you</c><00:04:50.690><c> get</c><00:04:50.840><c> the</c><00:04:50.960><c> idea</c><00:04:51.020><c> every</c><00:04:51.470><c> time</c>

00:04:51.670 --> 00:04:51.680 align:start position:0%
this and you get the idea every time
 

00:04:51.680 --> 00:04:54.040 align:start position:0%
this and you get the idea every time
that<00:04:51.950><c> the</c><00:04:52.130><c> the</c><00:04:52.580><c> client</c><00:04:52.970><c> is</c><00:04:53.060><c> gonna</c><00:04:53.180><c> click</c><00:04:53.750><c> over</c>

00:04:54.040 --> 00:04:54.050 align:start position:0%
that the the client is gonna click over
 

00:04:54.050 --> 00:04:55.900 align:start position:0%
that the the client is gonna click over
here<00:04:54.110><c> something</c><00:04:54.590><c> on</c><00:04:54.860><c> the</c><00:04:54.950><c> front</c><00:04:55.100><c> end</c><00:04:55.430><c> it's</c>

00:04:55.900 --> 00:04:55.910 align:start position:0%
here something on the front end it's
 

00:04:55.910 --> 00:04:59.650 align:start position:0%
here something on the front end it's
gonna<00:04:56.000><c> make</c><00:04:56.330><c> that</c><00:04:56.900><c> that</c><00:04:57.710><c> API</c><00:04:58.100><c> call</c><00:04:58.300><c> post</c><00:04:59.300><c> and</c>

00:04:59.650 --> 00:04:59.660 align:start position:0%
gonna make that that API call post and
 

00:04:59.660 --> 00:05:02.320 align:start position:0%
gonna make that that API call post and
we're<00:04:59.810><c> going</c><00:04:59.930><c> to</c><00:04:59.990><c> continue</c>

