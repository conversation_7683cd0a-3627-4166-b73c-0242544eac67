WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.969 align:start position:0%
 
according<00:00:00.719><c> to</c><00:00:00.960><c> Verizon's</c><00:00:01.560><c> stolen</c>

00:00:01.969 --> 00:00:01.979 align:start position:0%
according to Verizon's stolen
 

00:00:01.979 --> 00:00:03.710 align:start position:0%
according to <PERSON><PERSON><PERSON>'s stolen
credentials<00:00:02.520><c> are</c><00:00:02.700><c> responsible</c><00:00:03.120><c> for</c><00:00:03.480><c> almost</c>

00:00:03.710 --> 00:00:03.720 align:start position:0%
credentials are responsible for almost
 

00:00:03.720 --> 00:00:06.590 align:start position:0%
credentials are responsible for almost
half<00:00:04.259><c> of</c><00:00:04.620><c> all</c><00:00:04.799><c> attacks</c><00:00:05.279><c> posing</c><00:00:06.180><c> a</c><00:00:06.359><c> challenge</c>

00:00:06.590 --> 00:00:06.600 align:start position:0%
half of all attacks posing a challenge
 

00:00:06.600 --> 00:00:09.169 align:start position:0%
half of all attacks posing a challenge
due<00:00:07.080><c> to</c><00:00:07.259><c> their</c><00:00:07.440><c> widespread</c><00:00:08.040><c> use</c><00:00:08.220><c> in</c><00:00:08.760><c> software</c>

00:00:09.169 --> 00:00:09.179 align:start position:0%
due to their widespread use in software
 

00:00:09.179 --> 00:00:12.589 align:start position:0%
due to their widespread use in software
development<00:00:09.660><c> in</c><00:00:10.559><c> fact</c><00:00:10.679><c> over</c><00:00:11.179><c> 1.7</c><00:00:12.179><c> million</c>

00:00:12.589 --> 00:00:12.599 align:start position:0%
development in fact over 1.7 million
 

00:00:12.599 --> 00:00:14.690 align:start position:0%
development in fact over 1.7 million
exposed<00:00:13.200><c> secrets</c><00:00:13.679><c> were</c><00:00:13.920><c> found</c><00:00:14.099><c> on</c><00:00:14.340><c> GitHub</c>

00:00:14.690 --> 00:00:14.700 align:start position:0%
exposed secrets were found on GitHub
 

00:00:14.700 --> 00:00:18.470 align:start position:0%
exposed secrets were found on GitHub
public<00:00:14.880><c> repositories</c><00:00:15.660><c> in</c><00:00:16.560><c> 2022.</c><00:00:17.760><c> GitHub</c>

00:00:18.470 --> 00:00:18.480 align:start position:0%
public repositories in 2022. GitHub
 

00:00:18.480 --> 00:00:20.570 align:start position:0%
public repositories in 2022. GitHub
secret<00:00:18.660><c> scanning</c><00:00:19.199><c> offers</c><00:00:19.560><c> a</c><00:00:19.800><c> solution</c>

00:00:20.570 --> 00:00:20.580 align:start position:0%
secret scanning offers a solution
 

00:00:20.580 --> 00:00:23.150 align:start position:0%
secret scanning offers a solution
integrated<00:00:21.359><c> within</c><00:00:21.900><c> the</c><00:00:22.020><c> GitHub</c><00:00:22.380><c> UI</c><00:00:22.740><c> it</c>

00:00:23.150 --> 00:00:23.160 align:start position:0%
integrated within the GitHub UI it
 

00:00:23.160 --> 00:00:25.730 align:start position:0%
integrated within the GitHub UI it
detects<00:00:23.460><c> secret</c><00:00:23.640><c> leaks</c><00:00:24.240><c> in</c><00:00:24.480><c> code</c><00:00:24.779><c> issues</c>

00:00:25.730 --> 00:00:25.740 align:start position:0%
detects secret leaks in code issues
 

00:00:25.740 --> 00:00:28.730 align:start position:0%
detects secret leaks in code issues
descriptions<00:00:26.460><c> and</c><00:00:27.000><c> comments</c><00:00:27.900><c> with</c><00:00:28.500><c> support</c>

00:00:28.730 --> 00:00:28.740 align:start position:0%
descriptions and comments with support
 

00:00:28.740 --> 00:00:32.030 align:start position:0%
descriptions and comments with support
for<00:00:29.039><c> scanning</c><00:00:29.519><c> over</c><00:00:29.880><c> 200</c><00:00:30.539><c> token</c><00:00:31.019><c> types</c><00:00:31.380><c> and</c>

00:00:32.030 --> 00:00:32.040 align:start position:0%
for scanning over 200 token types and
 

00:00:32.040 --> 00:00:34.010 align:start position:0%
for scanning over 200 token types and
collaboration<00:00:32.579><c> with</c><00:00:32.940><c> a</c><00:00:33.180><c> network</c><00:00:33.420><c> of</c>

00:00:34.010 --> 00:00:34.020 align:start position:0%
collaboration with a network of
 

00:00:34.020 --> 00:00:36.410 align:start position:0%
collaboration with a network of
approximately<00:00:34.620><c> 150</c><00:00:35.579><c> service</c><00:00:35.760><c> providers</c>

00:00:36.410 --> 00:00:36.420 align:start position:0%
approximately 150 service providers
 

00:00:36.420 --> 00:00:38.870 align:start position:0%
approximately 150 service providers
secret<00:00:37.200><c> scanning</c><00:00:37.860><c> can</c><00:00:38.100><c> identify</c><00:00:38.579><c> League</c>

00:00:38.870 --> 00:00:38.880 align:start position:0%
secret scanning can identify League
 

00:00:38.880 --> 00:00:40.670 align:start position:0%
secret scanning can identify League
Secrets<00:00:39.480><c> at</c><00:00:39.780><c> scale</c>

00:00:40.670 --> 00:00:40.680 align:start position:0%
Secrets at scale
 

00:00:40.680 --> 00:00:43.130 align:start position:0%
Secrets at scale
push<00:00:41.040><c> protection</c><00:00:41.579><c> further</c><00:00:42.239><c> prevents</c><00:00:42.600><c> secrets</c>

00:00:43.130 --> 00:00:43.140 align:start position:0%
push protection further prevents secrets
 

00:00:43.140 --> 00:00:45.170 align:start position:0%
push protection further prevents secrets
from<00:00:43.320><c> being</c><00:00:43.559><c> committed</c><00:00:43.980><c> to</c><00:00:44.160><c> code</c><00:00:44.340><c> secret</c>

00:00:45.170 --> 00:00:45.180 align:start position:0%
from being committed to code secret
 

00:00:45.180 --> 00:00:47.510 align:start position:0%
from being committed to code secret
scanning<00:00:45.719><c> is</c><00:00:46.020><c> free</c><00:00:46.260><c> for</c><00:00:46.559><c> public</c><00:00:46.800><c> repositories</c>

00:00:47.510 --> 00:00:47.520 align:start position:0%
scanning is free for public repositories
 

00:00:47.520 --> 00:00:49.970 align:start position:0%
scanning is free for public repositories
and<00:00:48.420><c> included</c><00:00:48.899><c> in</c><00:00:49.200><c> your</c><00:00:49.320><c> Advanced</c><00:00:49.800><c> security</c>

00:00:49.970 --> 00:00:49.980 align:start position:0%
and included in your Advanced security
 

00:00:49.980 --> 00:00:52.150 align:start position:0%
and included in your Advanced security
license<00:00:50.520><c> for</c><00:00:51.059><c> private</c><00:00:51.420><c> repositories</c>

00:00:52.150 --> 00:00:52.160 align:start position:0%
license for private repositories
 

00:00:52.160 --> 00:00:55.069 align:start position:0%
license for private repositories
providing<00:00:53.160><c> a</c><00:00:53.460><c> simple</c><00:00:53.700><c> and</c><00:00:54.180><c> effective</c><00:00:54.600><c> way</c><00:00:54.840><c> to</c>

00:00:55.069 --> 00:00:55.079 align:start position:0%
providing a simple and effective way to
 

00:00:55.079 --> 00:00:57.650 align:start position:0%
providing a simple and effective way to
prevent<00:00:55.379><c> secret</c><00:00:55.620><c> leaks</c><00:00:56.219><c> without</c><00:00:56.879><c> requiring</c>

00:00:57.650 --> 00:00:57.660 align:start position:0%
prevent secret leaks without requiring
 

00:00:57.660 --> 00:00:59.090 align:start position:0%
prevent secret leaks without requiring
additional<00:00:58.140><c> tools</c>

00:00:59.090 --> 00:00:59.100 align:start position:0%
additional tools
 

00:00:59.100 --> 00:01:01.010 align:start position:0%
additional tools
let's<00:00:59.640><c> get</c><00:00:59.879><c> started</c><00:01:00.239><c> with</c><00:01:00.539><c> configuring</c>

00:01:01.010 --> 00:01:01.020 align:start position:0%
let's get started with configuring
 

00:01:01.020 --> 00:01:03.170 align:start position:0%
let's get started with configuring
secret<00:01:01.199><c> scanning</c><00:01:01.739><c> for</c><00:01:01.860><c> your</c><00:01:01.980><c> repositories</c><00:01:02.520><c> on</c>

00:01:03.170 --> 00:01:03.180 align:start position:0%
secret scanning for your repositories on
 

00:01:03.180 --> 00:01:05.329 align:start position:0%
secret scanning for your repositories on
github.com<00:01:03.780><c> navigate</c><00:01:04.260><c> to</c><00:01:04.619><c> the</c><00:01:04.799><c> main</c><00:01:04.920><c> page</c><00:01:05.159><c> of</c>

00:01:05.329 --> 00:01:05.339 align:start position:0%
github.com navigate to the main page of
 

00:01:05.339 --> 00:01:07.250 align:start position:0%
github.com navigate to the main page of
your<00:01:05.460><c> repository</c><00:01:06.060><c> under</c><00:01:06.540><c> your</c><00:01:06.840><c> repository</c>

00:01:07.250 --> 00:01:07.260 align:start position:0%
your repository under your repository
 

00:01:07.260 --> 00:01:09.289 align:start position:0%
your repository under your repository
name<00:01:07.439><c> click</c><00:01:07.860><c> settings</c><00:01:08.040><c> in</c><00:01:09.000><c> the</c><00:01:09.119><c> security</c>

00:01:09.289 --> 00:01:09.299 align:start position:0%
name click settings in the security
 

00:01:09.299 --> 00:01:11.450 align:start position:0%
name click settings in the security
section<00:01:09.900><c> of</c><00:01:10.260><c> your</c><00:01:10.439><c> sidebar</c><00:01:10.860><c> click</c><00:01:11.340><c> code</c>

00:01:11.450 --> 00:01:11.460 align:start position:0%
section of your sidebar click code
 

00:01:11.460 --> 00:01:14.149 align:start position:0%
section of your sidebar click code
security<00:01:11.760><c> and</c><00:01:12.240><c> Analysis</c><00:01:12.840><c> click</c><00:01:13.619><c> enable</c><00:01:13.920><c> for</c>

00:01:14.149 --> 00:01:14.159 align:start position:0%
security and Analysis click enable for
 

00:01:14.159 --> 00:01:16.250 align:start position:0%
security and Analysis click enable for
secret<00:01:14.340><c> scanning</c><00:01:14.880><c> check</c><00:01:15.600><c> out</c><00:01:15.780><c> our</c><00:01:15.900><c> GitHub</c>

00:01:16.250 --> 00:01:16.260 align:start position:0%
secret scanning check out our GitHub
 

00:01:16.260 --> 00:01:17.990 align:start position:0%
secret scanning check out our GitHub
skills<00:01:16.680><c> course</c><00:01:16.860><c> to</c><00:01:17.220><c> learn</c><00:01:17.400><c> about</c><00:01:17.580><c> software</c>

00:01:17.990 --> 00:01:18.000 align:start position:0%
skills course to learn about software
 

00:01:18.000 --> 00:01:19.730 align:start position:0%
skills course to learn about software
Security<00:01:18.299><c> in</c><00:01:18.780><c> a</c><00:01:18.960><c> fun</c><00:01:19.140><c> educational</c>

00:01:19.730 --> 00:01:19.740 align:start position:0%
Security in a fun educational
 

00:01:19.740 --> 00:01:21.590 align:start position:0%
Security in a fun educational
environment

00:01:21.590 --> 00:01:21.600 align:start position:0%
environment
 

00:01:21.600 --> 00:01:25.280 align:start position:0%
environment
click<00:01:22.320><c> this</c><00:01:22.439><c> link</c><00:01:22.680><c> to</c><00:01:22.920><c> know</c><00:01:23.100><c> more</c>

