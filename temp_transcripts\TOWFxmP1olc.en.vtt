WEBVTT
Kind: captions
Language: en

00:00:00.599 --> 00:00:03.750 align:start position:0%
 
and<00:00:00.840><c> howdy</c><00:00:01.120><c> you</c><00:00:01.360><c> guys</c><00:00:01.959><c> and</c><00:00:02.560><c> um</c><00:00:03.120><c> as</c><00:00:03.240><c> we</c><00:00:03.360><c> said</c><00:00:03.600><c> at</c>

00:00:03.750 --> 00:00:03.760 align:start position:0%
and howdy you guys and um as we said at
 

00:00:03.760 --> 00:00:06.110 align:start position:0%
and howdy you guys and um as we said at
the<00:00:03.879><c> end</c><00:00:04.080><c> of</c><00:00:04.200><c> the</c><00:00:04.400><c> last</c><00:00:04.720><c> video</c><00:00:05.279><c> part</c><00:00:05.560><c> one</c><00:00:05.960><c> has</c>

00:00:06.110 --> 00:00:06.120 align:start position:0%
the end of the last video part one has
 

00:00:06.120 --> 00:00:08.669 align:start position:0%
the end of the last video part one has
been<00:00:06.520><c> accomplished</c><00:00:07.240><c> and</c><00:00:07.480><c> we</c><00:00:07.879><c> we</c><00:00:08.120><c> transferred</c>

00:00:08.669 --> 00:00:08.679 align:start position:0%
been accomplished and we we transferred
 

00:00:08.679 --> 00:00:12.669 align:start position:0%
been accomplished and we we transferred
the<00:00:08.840><c> data</c><00:00:09.160><c> from</c><00:00:09.599><c> CSV</c><00:00:10.599><c> to</c><00:00:10.920><c> a</c><00:00:11.080><c> SQL</c><00:00:11.679><c> file</c><00:00:12.440><c> right</c>

00:00:12.669 --> 00:00:12.679 align:start position:0%
the data from CSV to a SQL file right
 

00:00:12.679 --> 00:00:15.990 align:start position:0%
the data from CSV to a SQL file right
correctly<00:00:13.240><c> formatted</c><00:00:14.240><c> with</c><00:00:14.400><c> the</c><00:00:14.639><c> date</c><00:00:15.360><c> date</c>

00:00:15.990 --> 00:00:16.000 align:start position:0%
correctly formatted with the date date
 

00:00:16.000 --> 00:00:19.670 align:start position:0%
correctly formatted with the date date
types<00:00:16.440><c> varar</c><00:00:17.199><c> and</c><00:00:17.480><c> integer</c><00:00:18.039><c> types</c><00:00:18.439><c> per</c><00:00:18.760><c> column</c>

00:00:19.670 --> 00:00:19.680 align:start position:0%
types varar and integer types per column
 

00:00:19.680 --> 00:00:22.070 align:start position:0%
types varar and integer types per column
everything<00:00:20.000><c> is</c><00:00:20.160><c> all</c><00:00:20.320><c> good</c><00:00:20.480><c> in</c><00:00:20.640><c> the</c><00:00:20.760><c> hood</c><00:00:21.199><c> now</c>

00:00:22.070 --> 00:00:22.080 align:start position:0%
everything is all good in the hood now
 

00:00:22.080 --> 00:00:25.670 align:start position:0%
everything is all good in the hood now
we<00:00:22.359><c> actually</c><00:00:23.039><c> need</c><00:00:23.720><c> to</c><00:00:24.439><c> uh</c><00:00:24.599><c> to</c><00:00:24.800><c> take</c><00:00:25.080><c> that</c><00:00:25.279><c> data</c>

00:00:25.670 --> 00:00:25.680 align:start position:0%
we actually need to uh to take that data
 

00:00:25.680 --> 00:00:29.150 align:start position:0%
we actually need to uh to take that data
and<00:00:25.920><c> upload</c><00:00:26.359><c> it</c><00:00:27.000><c> to</c><00:00:27.359><c> our</c><00:00:27.880><c> Cloud</c><00:00:28.439><c> mySQL</c>

00:00:29.150 --> 00:00:29.160 align:start position:0%
and upload it to our Cloud mySQL
 

00:00:29.160 --> 00:00:33.630 align:start position:0%
and upload it to our Cloud mySQL
database<00:00:30.039><c> on</c><00:00:30.640><c> ja</c><00:00:31.080><c> dbh</c><00:00:32.040><c> Heroku</c><00:00:32.800><c> all</c><00:00:32.960><c> right</c><00:00:33.160><c> so</c>

00:00:33.630 --> 00:00:33.640 align:start position:0%
database on ja dbh Heroku all right so
 

00:00:33.640 --> 00:00:35.709 align:start position:0%
database on ja dbh Heroku all right so
in<00:00:33.760><c> order</c><00:00:34.000><c> to</c><00:00:34.160><c> do</c><00:00:34.440><c> this</c><00:00:34.640><c> step</c><00:00:35.040><c> we</c><00:00:35.280><c> actually</c>

00:00:35.709 --> 00:00:35.719 align:start position:0%
in order to do this step we actually
 

00:00:35.719 --> 00:00:39.110 align:start position:0%
in order to do this step we actually
need<00:00:35.960><c> to</c><00:00:36.280><c> have</c><00:00:37.079><c> um</c><00:00:37.559><c> another</c><00:00:38.200><c> program</c><00:00:38.760><c> in</c><00:00:38.920><c> this</c>

00:00:39.110 --> 00:00:39.120 align:start position:0%
need to have um another program in this
 

00:00:39.120 --> 00:00:41.029 align:start position:0%
need to have um another program in this
case<00:00:39.360><c> I'm</c><00:00:39.520><c> using</c><00:00:39.960><c> a</c>

00:00:41.029 --> 00:00:41.039 align:start position:0%
case I'm using a
 

00:00:41.039 --> 00:00:44.709 align:start position:0%
case I'm using a
program<00:00:42.039><c> uh</c><00:00:42.200><c> which</c><00:00:42.399><c> I</c><00:00:42.600><c> really</c><00:00:42.960><c> like</c><00:00:43.640><c> it's</c>

00:00:44.709 --> 00:00:44.719 align:start position:0%
program uh which I really like it's
 

00:00:44.719 --> 00:00:46.790 align:start position:0%
program uh which I really like it's
called<00:00:45.719><c> uh</c><00:00:45.879><c> let</c><00:00:46.000><c> me</c><00:00:46.120><c> double</c><00:00:46.360><c> check</c><00:00:46.600><c> what</c><00:00:46.680><c> the</c>

00:00:46.790 --> 00:00:46.800 align:start position:0%
called uh let me double check what the
 

00:00:46.800 --> 00:00:50.069 align:start position:0%
called uh let me double check what the
name<00:00:46.960><c> of</c><00:00:47.160><c> this</c><00:00:47.480><c> program</c><00:00:48.399><c> is</c><00:00:49.399><c> um</c><00:00:49.640><c> let's</c><00:00:49.800><c> see</c><00:00:50.000><c> I'm</c>

00:00:50.069 --> 00:00:50.079 align:start position:0%
name of this program is um let's see I'm
 

00:00:50.079 --> 00:00:53.790 align:start position:0%
name of this program is um let's see I'm
going<00:00:50.199><c> to</c><00:00:50.360><c> call</c><00:00:50.960><c> SQL</c><00:00:51.840><c> my</c><00:00:52.280><c> SQL</c><00:00:53.039><c> oh</c><00:00:53.239><c> yeah</c><00:00:53.680><c> there</c>

00:00:53.790 --> 00:00:53.800 align:start position:0%
going to call SQL my SQL oh yeah there
 

00:00:53.800 --> 00:00:55.150 align:start position:0%
going to call SQL my SQL oh yeah there
we

00:00:55.150 --> 00:00:55.160 align:start position:0%
we
 

00:00:55.160 --> 00:01:00.709 align:start position:0%
we
go<00:00:56.160><c> okay</c><00:00:56.359><c> my</c><00:00:57.039><c> SQL</c><00:00:58.039><c> um</c><00:00:58.719><c> oh</c><00:00:58.920><c> wait</c><00:00:59.320><c> hold</c><00:00:59.519><c> on</c><00:01:00.320><c> it's</c>

00:01:00.709 --> 00:01:00.719 align:start position:0%
go okay my SQL um oh wait hold on it's
 

00:01:00.719 --> 00:01:02.310 align:start position:0%
go okay my SQL um oh wait hold on it's
this<00:01:01.239><c> hold</c>

00:01:02.310 --> 00:01:02.320 align:start position:0%
this hold
 

00:01:02.320 --> 00:01:06.030 align:start position:0%
this hold
on<00:01:03.320><c> ah</c><00:01:03.640><c> okay</c><00:01:03.960><c> it's</c><00:01:04.119><c> called</c><00:01:04.479><c> Heidi</c><00:01:05.000><c> SQL</c><00:01:05.680><c> Heidi</c>

00:01:06.030 --> 00:01:06.040 align:start position:0%
on ah okay it's called Heidi SQL Heidi
 

00:01:06.040 --> 00:01:07.950 align:start position:0%
on ah okay it's called Heidi SQL Heidi
SQL<00:01:06.400><c> is</c><00:01:06.560><c> the</c><00:01:06.720><c> program</c><00:01:07.040><c> that</c><00:01:07.159><c> I'm</c><00:01:07.320><c> using</c><00:01:07.720><c> I</c>

00:01:07.950 --> 00:01:07.960 align:start position:0%
SQL is the program that I'm using I
 

00:01:07.960 --> 00:01:10.149 align:start position:0%
SQL is the program that I'm using I
already<00:01:08.200><c> have</c><00:01:08.320><c> it</c><00:01:08.680><c> installed</c><00:01:09.680><c> it's</c><00:01:09.880><c> pretty</c>

00:01:10.149 --> 00:01:10.159 align:start position:0%
already have it installed it's pretty
 

00:01:10.159 --> 00:01:13.789 align:start position:0%
already have it installed it's pretty
simple<00:01:10.520><c> to</c><00:01:10.720><c> set</c><00:01:11.360><c> up</c><00:01:12.360><c> um</c><00:01:13.040><c> and</c><00:01:13.159><c> it's</c><00:01:13.320><c> a</c><00:01:13.560><c> really</c>

00:01:13.789 --> 00:01:13.799 align:start position:0%
simple to set up um and it's a really
 

00:01:13.799 --> 00:01:16.830 align:start position:0%
simple to set up um and it's a really
awesome<00:01:14.280><c> program</c><00:01:14.759><c> I'm</c><00:01:14.880><c> using</c><00:01:15.159><c> a</c><00:01:15.320><c> Windows</c>

00:01:16.830 --> 00:01:16.840 align:start position:0%
awesome program I'm using a Windows
 

00:01:16.840 --> 00:01:20.190 align:start position:0%
awesome program I'm using a Windows
machine<00:01:17.840><c> and</c><00:01:17.960><c> the</c><00:01:18.119><c> nice</c><00:01:18.320><c> thing</c><00:01:18.560><c> about</c><00:01:19.280><c> hii</c><00:01:19.720><c> SQL</c>

00:01:20.190 --> 00:01:20.200 align:start position:0%
machine and the nice thing about hii SQL
 

00:01:20.200 --> 00:01:22.510 align:start position:0%
machine and the nice thing about hii SQL
is<00:01:20.400><c> that</c><00:01:21.040><c> that</c><00:01:21.360><c> actually</c><00:01:21.840><c> gives</c><00:01:22.040><c> me</c><00:01:22.280><c> the</c>

00:01:22.510 --> 00:01:22.520 align:start position:0%
is that that actually gives me the
 

00:01:22.520 --> 00:01:25.109 align:start position:0%
is that that actually gives me the
connection<00:01:23.119><c> to</c><00:01:23.320><c> the</c><00:01:23.479><c> cloud</c><00:01:23.960><c> database</c><00:01:24.640><c> from</c><00:01:24.920><c> my</c>

00:01:25.109 --> 00:01:25.119 align:start position:0%
connection to the cloud database from my
 

00:01:25.119 --> 00:01:27.830 align:start position:0%
connection to the cloud database from my
local<00:01:25.560><c> machine</c><00:01:26.320><c> and</c><00:01:26.799><c> and</c><00:01:27.200><c> then</c><00:01:27.360><c> I</c><00:01:27.439><c> can</c><00:01:27.600><c> take</c>

00:01:27.830 --> 00:01:27.840 align:start position:0%
local machine and and then I can take
 

00:01:27.840 --> 00:01:30.789 align:start position:0%
local machine and and then I can take
that<00:01:28.000><c> SQL</c><00:01:28.520><c> file</c><00:01:29.520><c> that</c><00:01:29.680><c> we</c><00:01:30.240><c> that</c><00:01:30.400><c> we</c><00:01:30.560><c> just</c>

00:01:30.789 --> 00:01:30.799 align:start position:0%
that SQL file that we that we just
 

00:01:30.799 --> 00:01:33.469 align:start position:0%
that SQL file that we that we just
created<00:01:31.240><c> and</c><00:01:31.479><c> uploaded</c><00:01:32.000><c> to</c><00:01:32.159><c> a</c><00:01:32.399><c> cloud</c><00:01:32.799><c> mySQL</c>

00:01:33.469 --> 00:01:33.479 align:start position:0%
created and uploaded to a cloud mySQL
 

00:01:33.479 --> 00:01:36.630 align:start position:0%
created and uploaded to a cloud mySQL
database<00:01:34.040><c> so</c><00:01:34.640><c> let's</c><00:01:34.880><c> actually</c><00:01:35.200><c> go</c><00:01:35.360><c> into</c><00:01:35.680><c> MyQ</c>

00:01:36.630 --> 00:01:36.640 align:start position:0%
database so let's actually go into MyQ
 

00:01:36.640 --> 00:01:39.510 align:start position:0%
database so let's actually go into MyQ
um<00:01:37.399><c> after</c><00:01:37.640><c> you</c><00:01:37.880><c> go</c><00:01:38.079><c> ahead</c><00:01:38.280><c> and</c><00:01:38.600><c> install</c><00:01:39.079><c> Heidi</c>

00:01:39.510 --> 00:01:39.520 align:start position:0%
um after you go ahead and install Heidi
 

00:01:39.520 --> 00:01:41.910 align:start position:0%
um after you go ahead and install Heidi
SQL<00:01:39.960><c> for</c><00:01:40.200><c> Windows</c><00:01:40.880><c> I</c><00:01:40.960><c> think</c><00:01:41.119><c> it</c><00:01:41.240><c> also</c><00:01:41.439><c> runs</c><00:01:41.720><c> on</c>

00:01:41.910 --> 00:01:41.920 align:start position:0%
SQL for Windows I think it also runs on
 

00:01:41.920 --> 00:01:47.990 align:start position:0%
SQL for Windows I think it also runs on
Linux<00:01:42.600><c> but</c><00:01:43.600><c> in</c><00:01:43.720><c> this</c><00:01:43.920><c> case</c><00:01:44.079><c> we're</c><00:01:44.240><c> using</c>

00:01:47.990 --> 00:01:48.000 align:start position:0%
 
 

00:01:48.000 --> 00:01:50.630 align:start position:0%
 
Windows<00:01:49.000><c> um</c><00:01:49.439><c> we</c><00:01:49.520><c> need</c><00:01:49.680><c> to</c><00:01:49.880><c> actually</c><00:01:50.200><c> create</c><00:01:50.479><c> a</c>

00:01:50.630 --> 00:01:50.640 align:start position:0%
Windows um we need to actually create a
 

00:01:50.640 --> 00:01:53.550 align:start position:0%
Windows um we need to actually create a
connection<00:01:51.320><c> to</c><00:01:51.520><c> our</c><00:01:51.880><c> to</c><00:01:52.079><c> our</c><00:01:52.439><c> our</c><00:01:52.680><c> Cloud</c><00:01:53.000><c> mySQL</c>

00:01:53.550 --> 00:01:53.560 align:start position:0%
connection to our to our our Cloud mySQL
 

00:01:53.560 --> 00:01:55.030 align:start position:0%
connection to our to our our Cloud mySQL
database<00:01:54.040><c> what</c><00:01:54.119><c> are</c><00:01:54.200><c> we</c><00:01:54.280><c> going</c><00:01:54.399><c> to</c><00:01:54.560><c> call</c><00:01:54.799><c> this</c>

00:01:55.030 --> 00:01:55.040 align:start position:0%
database what are we going to call this
 

00:01:55.040 --> 00:01:58.550 align:start position:0%
database what are we going to call this
we're<00:01:55.159><c> going</c><00:01:55.280><c> to</c><00:01:55.439><c> call</c><00:01:55.680><c> this</c><00:01:56.280><c> CSV</c><00:01:57.360><c> to</c><00:01:58.360><c> a</c>

00:01:58.550 --> 00:01:58.560 align:start position:0%
we're going to call this CSV to a
 

00:01:58.560 --> 00:02:00.190 align:start position:0%
we're going to call this CSV to a
metabase

00:02:00.190 --> 00:02:00.200 align:start position:0%
metabase
 

00:02:00.200 --> 00:02:04.389 align:start position:0%
metabase
demo<00:02:01.520><c> okay</c><00:02:02.520><c> and</c><00:02:02.840><c> I'm</c><00:02:02.960><c> going</c><00:02:03.079><c> to</c><00:02:03.240><c> click</c><00:02:03.680><c> that</c><00:02:04.240><c> oh</c>

00:02:04.389 --> 00:02:04.399 align:start position:0%
demo okay and I'm going to click that oh
 

00:02:04.399 --> 00:02:07.270 align:start position:0%
demo okay and I'm going to click that oh
schnikes<00:02:04.880><c> I</c><00:02:04.960><c> forgot</c><00:02:05.240><c> to</c><00:02:05.320><c> put</c><00:02:05.479><c> in</c><00:02:05.640><c> the</c><00:02:06.280><c> details</c>

00:02:07.270 --> 00:02:07.280 align:start position:0%
schnikes I forgot to put in the details
 

00:02:07.280 --> 00:02:10.350 align:start position:0%
schnikes I forgot to put in the details
okay<00:02:08.280><c> great</c><00:02:08.920><c> okay</c><00:02:09.200><c> one</c><00:02:09.520><c> second</c><00:02:10.000><c> let</c><00:02:10.080><c> me</c><00:02:10.239><c> go</c>

00:02:10.350 --> 00:02:10.360 align:start position:0%
okay great okay one second let me go
 

00:02:10.360 --> 00:02:11.550 align:start position:0%
okay great okay one second let me go
into

00:02:11.550 --> 00:02:11.560 align:start position:0%
into
 

00:02:11.560 --> 00:02:16.070 align:start position:0%
into
file<00:02:12.560><c> CSV</c><00:02:13.040><c> to</c><00:02:13.239><c> my</c><00:02:13.680><c> to</c><00:02:13.840><c> metabase</c><00:02:14.640><c> demo</c><00:02:15.640><c> sorry</c><00:02:16.000><c> I</c>

00:02:16.070 --> 00:02:16.080 align:start position:0%
file CSV to my to metabase demo sorry I
 

00:02:16.080 --> 00:02:17.750 align:start position:0%
file CSV to my to metabase demo sorry I
didn't<00:02:16.280><c> do</c><00:02:16.440><c> this</c><00:02:16.560><c> correctly</c>

00:02:17.750 --> 00:02:17.760 align:start position:0%
didn't do this correctly
 

00:02:17.760 --> 00:02:21.710 align:start position:0%
didn't do this correctly
file<00:02:18.760><c> uh</c><00:02:18.959><c> new</c><00:02:19.480><c> window</c><00:02:20.480><c> no</c><00:02:20.840><c> file</c><00:02:21.440><c> yeah</c><00:02:21.599><c> there</c>

00:02:21.710 --> 00:02:21.720 align:start position:0%
file uh new window no file yeah there
 

00:02:21.720 --> 00:02:25.990 align:start position:0%
file uh new window no file yeah there
you<00:02:21.879><c> go</c><00:02:22.519><c> okay</c><00:02:23.519><c> uh</c><00:02:23.680><c> I</c><00:02:23.800><c> think</c><00:02:24.000><c> I</c><00:02:24.080><c> can</c><00:02:24.280><c> close</c><00:02:25.000><c> that</c>

00:02:25.990 --> 00:02:26.000 align:start position:0%
you go okay uh I think I can close that
 

00:02:26.000 --> 00:02:27.550 align:start position:0%
you go okay uh I think I can close that
yes<00:02:26.560><c> close</c>

00:02:27.550 --> 00:02:27.560 align:start position:0%
yes close
 

00:02:27.560 --> 00:02:34.390 align:start position:0%
yes close
that<00:02:28.560><c> oh</c><00:02:28.879><c> no</c><00:02:30.319><c> no</c><00:02:31.319><c> no</c><00:02:31.879><c> no</c><00:02:32.720><c> all</c><00:02:32.840><c> right</c><00:02:33.760><c> uh</c><00:02:34.000><c> now</c><00:02:34.280><c> I'm</c>

00:02:34.390 --> 00:02:34.400 align:start position:0%
that oh no no no no all right uh now I'm
 

00:02:34.400 --> 00:02:37.190 align:start position:0%
that oh no no no no all right uh now I'm
going<00:02:34.519><c> to</c><00:02:34.720><c> go</c><00:02:35.000><c> in</c><00:02:35.280><c> all</c><00:02:35.400><c> right</c><00:02:35.560><c> CSV</c><00:02:36.080><c> to</c><00:02:36.200><c> metabase</c>

00:02:37.190 --> 00:02:37.200 align:start position:0%
going to go in all right CSV to metabase
 

00:02:37.200 --> 00:02:39.630 align:start position:0%
going to go in all right CSV to metabase
demo<00:02:38.200><c> okay</c><00:02:38.640><c> we're</c><00:02:38.800><c> going</c><00:02:38.920><c> to</c><00:02:39.040><c> need</c><00:02:39.200><c> to</c><00:02:39.400><c> create</c>

00:02:39.630 --> 00:02:39.640 align:start position:0%
demo okay we're going to need to create
 

00:02:39.640 --> 00:02:42.630 align:start position:0%
demo okay we're going to need to create
a<00:02:39.760><c> new</c><00:02:40.200><c> one</c><00:02:41.200><c> what</c><00:02:41.280><c> are</c><00:02:41.400><c> we</c><00:02:41.519><c> going</c><00:02:41.640><c> to</c><00:02:41.840><c> call</c><00:02:42.159><c> this</c>

00:02:42.630 --> 00:02:42.640 align:start position:0%
a new one what are we going to call this
 

00:02:42.640 --> 00:02:45.550 align:start position:0%
a new one what are we going to call this
thing<00:02:43.640><c> we're</c><00:02:43.800><c> going</c><00:02:43.920><c> to</c><00:02:44.120><c> call</c>

00:02:45.550 --> 00:02:45.560 align:start position:0%
thing we're going to call
 

00:02:45.560 --> 00:02:50.830 align:start position:0%
thing we're going to call
this<00:02:46.840><c> metabase</c><00:02:48.000><c> rocks</c><00:02:49.000><c> okay</c><00:02:49.959><c> over</c><00:02:50.239><c> here</c><00:02:50.519><c> host</c>

00:02:50.830 --> 00:02:50.840 align:start position:0%
this metabase rocks okay over here host
 

00:02:50.840 --> 00:02:53.190 align:start position:0%
this metabase rocks okay over here host
name<00:02:51.080><c> what's</c><00:02:51.319><c> the</c><00:02:51.480><c> host</c><00:02:51.840><c> name</c><00:02:52.080><c> of</c><00:02:52.280><c> this</c><00:02:52.480><c> new</c><00:02:53.040><c> of</c>

00:02:53.190 --> 00:02:53.200 align:start position:0%
name what's the host name of this new of
 

00:02:53.200 --> 00:02:54.670 align:start position:0%
name what's the host name of this new of
this<00:02:53.360><c> new</c><00:02:53.640><c> thing</c><00:02:54.000><c> I'm</c><00:02:54.080><c> going</c><00:02:54.200><c> to</c><00:02:54.280><c> go</c><00:02:54.400><c> into</c><00:02:54.560><c> our</c>

00:02:54.670 --> 00:02:54.680 align:start position:0%
this new thing I'm going to go into our
 

00:02:54.680 --> 00:02:59.270 align:start position:0%
this new thing I'm going to go into our
ja<00:02:55.040><c> DB</c><00:02:56.000><c> the</c><00:02:56.200><c> host</c><00:02:56.720><c> is</c><00:02:57.159><c> this</c><00:02:57.760><c> is</c><00:02:58.080><c> this</c><00:02:58.480><c> contrl</c><00:02:58.879><c> C</c>

00:02:59.270 --> 00:02:59.280 align:start position:0%
ja DB the host is this is this contrl C
 

00:02:59.280 --> 00:03:02.190 align:start position:0%
ja DB the host is this is this contrl C
it's<00:02:59.480><c> just</c><00:02:59.640><c> a</c><00:02:59.879><c> a</c><00:03:00.080><c> matter</c><00:03:00.360><c> of</c><00:03:01.360><c> of</c><00:03:01.560><c> just</c><00:03:01.840><c> copy</c>

00:03:02.190 --> 00:03:02.200 align:start position:0%
it's just a a matter of of just copy
 

00:03:02.200 --> 00:03:04.710 align:start position:0%
it's just a a matter of of just copy
pasting<00:03:02.640><c> at</c><00:03:02.840><c> this</c><00:03:03.200><c> at</c><00:03:03.400><c> this</c><00:03:03.680><c> point</c><00:03:04.200><c> all</c><00:03:04.360><c> right</c>

00:03:04.710 --> 00:03:04.720 align:start position:0%
pasting at this at this point all right
 

00:03:04.720 --> 00:03:10.070 align:start position:0%
pasting at this at this point all right
host<00:03:05.400><c> control</c><00:03:06.640><c> copy</c><00:03:07.640><c> and</c><00:03:08.480><c> contrl</c><00:03:08.920><c> V</c><00:03:09.720><c> the</c>

00:03:10.070 --> 00:03:10.080 align:start position:0%
host control copy and contrl V the
 

00:03:10.080 --> 00:03:14.309 align:start position:0%
host control copy and contrl V the
username<00:03:11.080><c> again</c><00:03:11.519><c> contrl</c><00:03:12.840><c> copy</c><00:03:13.840><c> is</c><00:03:14.040><c> going</c><00:03:14.159><c> to</c>

00:03:14.309 --> 00:03:14.319 align:start position:0%
username again contrl copy is going to
 

00:03:14.319 --> 00:03:18.430 align:start position:0%
username again contrl copy is going to
go<00:03:14.480><c> in</c><00:03:14.720><c> there</c><00:03:15.319><c> the</c><00:03:15.680><c> password</c><00:03:16.280><c> is</c><00:03:16.879><c> this</c><00:03:17.879><c> contrl</c>

00:03:18.430 --> 00:03:18.440 align:start position:0%
go in there the password is this contrl
 

00:03:18.440 --> 00:03:22.509 align:start position:0%
go in there the password is this contrl
c<00:03:19.440><c> contrl</c><00:03:19.879><c> v</c><00:03:20.560><c> the</c><00:03:20.760><c> port</c><00:03:21.080><c> 3306</c><00:03:21.959><c> all</c><00:03:22.200><c> good</c><00:03:22.360><c> and</c>

00:03:22.509 --> 00:03:22.519 align:start position:0%
c contrl v the port 3306 all good and
 

00:03:22.519 --> 00:03:26.910 align:start position:0%
c contrl v the port 3306 all good and
the<00:03:22.799><c> database</c><00:03:23.799><c> Al</c><00:03:24.120><c> important</c><00:03:24.760><c> contrl</c><00:03:25.680><c> C</c><00:03:26.680><c> goes</c>

00:03:26.910 --> 00:03:26.920 align:start position:0%
the database Al important contrl C goes
 

00:03:26.920 --> 00:03:31.630 align:start position:0%
the database Al important contrl C goes
in<00:03:27.200><c> there</c><00:03:27.640><c> and</c><00:03:27.840><c> we're</c><00:03:28.040><c> all</c><00:03:28.239><c> ready</c><00:03:28.480><c> to</c><00:03:28.720><c> rock</c>

00:03:31.630 --> 00:03:31.640 align:start position:0%
in there and we're all ready to rock
 

00:03:31.640 --> 00:03:34.830 align:start position:0%
in there and we're all ready to rock
okay<00:03:32.640><c> make</c><00:03:32.799><c> sure</c><00:03:33.000><c> there's</c><00:03:33.239><c> no</c><00:03:33.519><c> spaces</c><00:03:34.360><c> and</c><00:03:34.720><c> we</c>

00:03:34.830 --> 00:03:34.840 align:start position:0%
okay make sure there's no spaces and we
 

00:03:34.840 --> 00:03:37.750 align:start position:0%
okay make sure there's no spaces and we
are<00:03:35.080><c> good</c><00:03:35.400><c> open</c><00:03:36.120><c> okay</c><00:03:36.360><c> metabase</c><00:03:37.000><c> D</c><00:03:37.239><c> rocks</c><00:03:37.519><c> is</c>

00:03:37.750 --> 00:03:37.760 align:start position:0%
are good open okay metabase D rocks is
 

00:03:37.760 --> 00:03:40.149 align:start position:0%
are good open okay metabase D rocks is
the<00:03:37.879><c> name</c><00:03:38.159><c> of</c><00:03:38.400><c> this</c><00:03:38.680><c> connection</c><00:03:39.680><c> settings</c><00:03:40.040><c> for</c>

00:03:40.149 --> 00:03:40.159 align:start position:0%
the name of this connection settings for
 

00:03:40.159 --> 00:03:42.470 align:start position:0%
the name of this connection settings for
metabase<00:03:40.599><c> rock</c><00:03:40.840><c> war</c><00:03:41.080><c> change</c><00:03:41.680><c> yes</c><00:03:41.920><c> I</c><00:03:42.000><c> wanted</c><00:03:42.280><c> to</c>

00:03:42.470 --> 00:03:42.480 align:start position:0%
metabase rock war change yes I wanted to
 

00:03:42.480 --> 00:03:45.429 align:start position:0%
metabase rock war change yes I wanted to
save<00:03:42.760><c> it</c><00:03:42.920><c> I</c><00:03:43.000><c> want</c><00:03:43.120><c> to</c><00:03:43.319><c> save</c><00:03:43.640><c> it</c><00:03:44.640><c> and</c><00:03:45.080><c> I</c><00:03:45.200><c> get</c>

00:03:45.429 --> 00:03:45.439 align:start position:0%
save it I want to save it and I get
 

00:03:45.439 --> 00:03:48.710 align:start position:0%
save it I want to save it and I get
something<00:03:45.879><c> that</c><00:03:46.040><c> looks</c><00:03:46.319><c> like</c><00:03:46.599><c> this</c><00:03:47.439><c> okay</c><00:03:48.439><c> so</c>

00:03:48.710 --> 00:03:48.720 align:start position:0%
something that looks like this okay so
 

00:03:48.720 --> 00:03:50.830 align:start position:0%
something that looks like this okay so
everything<00:03:49.000><c> is</c><00:03:49.319><c> good</c><00:03:49.879><c> now</c><00:03:50.120><c> that</c><00:03:50.280><c> I've</c><00:03:50.480><c> got</c>

00:03:50.830 --> 00:03:50.840 align:start position:0%
everything is good now that I've got
 

00:03:50.840 --> 00:03:55.350 align:start position:0%
everything is good now that I've got
that<00:03:51.200><c> I</c><00:03:51.319><c> go</c><00:03:51.480><c> into</c><00:03:51.760><c> that</c><00:03:51.959><c> database</c><00:03:52.599><c> over</c><00:03:52.920><c> here</c>

00:03:55.350 --> 00:03:55.360 align:start position:0%
that I go into that database over here
 

00:03:55.360 --> 00:03:58.270 align:start position:0%
that I go into that database over here
right<00:03:56.360><c> and</c><00:03:57.079><c> uh</c><00:03:57.200><c> what</c><00:03:57.280><c> do</c><00:03:57.439><c> I</c><00:03:57.599><c> have</c><00:03:57.799><c> over</c><00:03:58.000><c> here</c>

00:03:58.270 --> 00:03:58.280 align:start position:0%
right and uh what do I have over here
 

00:03:58.280 --> 00:04:01.830 align:start position:0%
right and uh what do I have over here
create<00:03:58.840><c> table</c><00:04:00.079><c> conversion</c><00:04:00.799><c> data</c><00:04:01.319><c> huh</c>

00:04:01.830 --> 00:04:01.840 align:start position:0%
create table conversion data huh
 

00:04:01.840 --> 00:04:04.630 align:start position:0%
create table conversion data huh
interesting<00:04:02.840><c> for</c><00:04:03.040><c> some</c><00:04:03.319><c> reason</c><00:04:03.680><c> it</c><00:04:04.480><c> it</c>

00:04:04.630 --> 00:04:04.640 align:start position:0%
interesting for some reason it it
 

00:04:04.640 --> 00:04:07.069 align:start position:0%
interesting for some reason it it
already<00:04:05.079><c> pasted</c><00:04:05.560><c> everything</c><00:04:05.879><c> in</c>

00:04:07.069 --> 00:04:07.079 align:start position:0%
already pasted everything in
 

00:04:07.079 --> 00:04:09.869 align:start position:0%
already pasted everything in
there<00:04:08.079><c> um</c><00:04:08.400><c> not</c><00:04:08.560><c> sure</c><00:04:08.799><c> exactly</c><00:04:09.200><c> how</c><00:04:09.439><c> that</c><00:04:09.720><c> that</c>

00:04:09.869 --> 00:04:09.879 align:start position:0%
there um not sure exactly how that that
 

00:04:09.879 --> 00:04:11.990 align:start position:0%
there um not sure exactly how that that
all<00:04:10.040><c> got</c><00:04:10.319><c> pasted</c><00:04:10.680><c> in</c><00:04:10.920><c> there</c><00:04:11.360><c> but</c><00:04:11.680><c> it</c><00:04:11.799><c> looks</c>

00:04:11.990 --> 00:04:12.000 align:start position:0%
all got pasted in there but it looks
 

00:04:12.000 --> 00:04:13.869 align:start position:0%
all got pasted in there but it looks
like<00:04:12.200><c> this</c><00:04:12.319><c> is</c><00:04:12.519><c> the</c><00:04:12.720><c> data</c><00:04:13.159><c> create</c><00:04:13.519><c> table</c>

00:04:13.869 --> 00:04:13.879 align:start position:0%
like this is the data create table
 

00:04:13.879 --> 00:04:17.110 align:start position:0%
like this is the data create table
conversion<00:04:14.480><c> data</c><00:04:15.200><c> yes</c><00:04:15.480><c> I</c><00:04:15.599><c> want</c><00:04:15.799><c> to</c><00:04:15.959><c> do</c><00:04:16.239><c> that</c><00:04:17.000><c> I</c>

00:04:17.110 --> 00:04:17.120 align:start position:0%
conversion data yes I want to do that I
 

00:04:17.120 --> 00:04:19.229 align:start position:0%
conversion data yes I want to do that I
want<00:04:17.199><c> to</c><00:04:17.359><c> run</c><00:04:17.639><c> this</c><00:04:17.840><c> query</c><00:04:18.680><c> most</c><00:04:18.840><c> of</c><00:04:18.959><c> the</c><00:04:19.120><c> time</c>

00:04:19.229 --> 00:04:19.239 align:start position:0%
want to run this query most of the time
 

00:04:19.239 --> 00:04:20.870 align:start position:0%
want to run this query most of the time
you'll<00:04:19.400><c> need</c><00:04:19.519><c> to</c><00:04:19.639><c> go</c><00:04:19.799><c> into</c>

00:04:20.870 --> 00:04:20.880 align:start position:0%
you'll need to go into
 

00:04:20.880 --> 00:04:24.310 align:start position:0%
you'll need to go into
file<00:04:21.880><c> you'll</c><00:04:22.120><c> need</c><00:04:22.240><c> to</c><00:04:22.520><c> rightclick</c><00:04:23.120><c> it</c><00:04:23.600><c> and</c>

00:04:24.310 --> 00:04:24.320 align:start position:0%
file you'll need to rightclick it and
 

00:04:24.320 --> 00:04:27.070 align:start position:0%
file you'll need to rightclick it and
right<00:04:24.639><c> export</c><00:04:25.520><c> and</c><00:04:25.960><c> import</c>

00:04:27.070 --> 00:04:27.080 align:start position:0%
right export and import
 

00:04:27.080 --> 00:04:28.710 align:start position:0%
right export and import
data

00:04:28.710 --> 00:04:28.720 align:start position:0%
data
 

00:04:28.720 --> 00:04:31.270 align:start position:0%
data
okay<00:04:29.759><c> SQL</c><00:04:30.160><c> error</c><00:04:30.440><c> you</c><00:04:30.520><c> have</c><00:04:30.639><c> an</c><00:04:30.800><c> error</c><00:04:31.080><c> in</c><00:04:31.160><c> your</c>

00:04:31.270 --> 00:04:31.280 align:start position:0%
okay SQL error you have an error in your
 

00:04:31.280 --> 00:04:32.440 align:start position:0%
okay SQL error you have an error in your
SQL<00:04:31.680><c> syntax</c>

00:04:32.440 --> 00:04:32.450 align:start position:0%
SQL syntax
 

00:04:32.450 --> 00:04:33.550 align:start position:0%
SQL syntax
[Music]

00:04:33.550 --> 00:04:33.560 align:start position:0%
[Music]
 

00:04:33.560 --> 00:04:38.270 align:start position:0%
[Music]
check<00:04:34.560><c> blink</c><00:04:34.840><c> at</c><00:04:35.080><c> line</c><00:04:35.400><c> one</c><00:04:36.759><c> okay</c><00:04:37.759><c> your</c><00:04:37.919><c> query</c>

00:04:38.270 --> 00:04:38.280 align:start position:0%
check blink at line one okay your query
 

00:04:38.280 --> 00:04:41.350 align:start position:0%
check blink at line one okay your query
produced<00:04:38.720><c> One</c><00:04:39.440><c> warning</c><00:04:40.440><c> H</c><00:04:40.880><c> question</c><00:04:41.160><c> is</c>

00:04:41.350 --> 00:04:41.360 align:start position:0%
produced One warning H question is
 

00:04:41.360 --> 00:04:43.590 align:start position:0%
produced One warning H question is
whether<00:04:41.600><c> it</c><00:04:41.800><c> actually</c>

00:04:43.590 --> 00:04:43.600 align:start position:0%
whether it actually
 

00:04:43.600 --> 00:04:46.150 align:start position:0%
whether it actually
worked<00:04:44.600><c> okay</c><00:04:44.960><c> it</c><00:04:45.080><c> looks</c><00:04:45.280><c> like</c><00:04:45.600><c> ah</c><00:04:45.880><c> wait</c><00:04:46.039><c> a</c>

00:04:46.150 --> 00:04:46.160 align:start position:0%
worked okay it looks like ah wait a
 

00:04:46.160 --> 00:04:47.990 align:start position:0%
worked okay it looks like ah wait a
second<00:04:46.440><c> we</c><00:04:46.560><c> may</c><00:04:46.759><c> have</c><00:04:46.960><c> pushed</c><00:04:47.280><c> the</c><00:04:47.440><c> wrong</c><00:04:47.720><c> data</c>

00:04:47.990 --> 00:04:48.000 align:start position:0%
second we may have pushed the wrong data
 

00:04:48.000 --> 00:04:51.390 align:start position:0%
second we may have pushed the wrong data
in<00:04:48.199><c> there</c><00:04:48.479><c> oh</c><00:04:48.639><c> it</c><00:04:48.720><c> looks</c><00:04:48.880><c> like</c><00:04:49.080><c> the</c><00:04:49.240><c> correct</c>

00:04:51.390 --> 00:04:51.400 align:start position:0%
in there oh it looks like the correct
 

00:04:51.400 --> 00:04:54.950 align:start position:0%
in there oh it looks like the correct
data<00:04:52.400><c> okay</c><00:04:53.120><c> yeah</c><00:04:53.360><c> date</c><00:04:53.720><c> date</c><00:04:54.000><c> date</c><00:04:54.520><c> this</c><00:04:54.720><c> seems</c>

00:04:54.950 --> 00:04:54.960 align:start position:0%
data okay yeah date date date this seems
 

00:04:54.960 --> 00:04:56.830 align:start position:0%
data okay yeah date date date this seems
like<00:04:55.160><c> it's</c><00:04:55.320><c> the</c><00:04:55.520><c> correct</c><00:04:55.960><c> data</c><00:04:56.320><c> so</c><00:04:56.600><c> what</c>

00:04:56.830 --> 00:04:56.840 align:start position:0%
like it's the correct data so what
 

00:04:56.840 --> 00:04:58.790 align:start position:0%
like it's the correct data so what
happened<00:04:57.280><c> there</c><00:04:57.680><c> and</c><00:04:57.840><c> let's</c><00:04:58.000><c> see</c><00:04:58.240><c> whether</c><00:04:58.560><c> any</c>

00:04:58.790 --> 00:04:58.800 align:start position:0%
happened there and let's see whether any
 

00:04:58.800 --> 00:05:01.029 align:start position:0%
happened there and let's see whether any
data<00:04:59.120><c> actually</c><00:04:59.880><c> got</c><00:05:00.440><c> uh</c><00:05:00.560><c> you</c><00:05:00.639><c> know</c><00:05:00.759><c> what</c><00:05:00.840><c> I'm</c>

00:05:01.029 --> 00:05:01.039 align:start position:0%
data actually got uh you know what I'm
 

00:05:01.039 --> 00:05:03.430 align:start position:0%
data actually got uh you know what I'm
just<00:05:01.680><c> got</c><00:05:02.000><c> imported</c><00:05:02.520><c> in</c><00:05:02.759><c> there</c><00:05:03.080><c> so</c><00:05:03.240><c> I've</c>

00:05:03.430 --> 00:05:03.440 align:start position:0%
just got imported in there so I've
 

00:05:03.440 --> 00:05:05.430 align:start position:0%
just got imported in there so I've
created<00:05:03.800><c> a</c><00:05:04.039><c> connection</c>

00:05:05.430 --> 00:05:05.440 align:start position:0%
created a connection
 

00:05:05.440 --> 00:05:09.110 align:start position:0%
created a connection
there<00:05:06.440><c> okay</c><00:05:06.840><c> query</c><00:05:07.680><c> select</c><00:05:08.160><c> all</c><00:05:08.639><c> from</c>

00:05:09.110 --> 00:05:09.120 align:start position:0%
there okay query select all from
 

00:05:09.120 --> 00:05:12.790 align:start position:0%
there okay query select all from
conversion<00:05:09.960><c> uncore</c><00:05:10.960><c> data</c><00:05:11.960><c> see</c><00:05:12.199><c> if</c><00:05:12.360><c> anything</c>

00:05:12.790 --> 00:05:12.800 align:start position:0%
conversion uncore data see if anything
 

00:05:12.800 --> 00:05:14.990 align:start position:0%
conversion uncore data see if anything
happened

00:05:14.990 --> 00:05:15.000 align:start position:0%
happened
 

00:05:15.000 --> 00:05:17.510 align:start position:0%
happened
there<00:05:16.000><c> awesome</c><00:05:16.440><c> everything</c><00:05:16.800><c> came</c><00:05:17.000><c> in</c><00:05:17.199><c> good</c>

00:05:17.510 --> 00:05:17.520 align:start position:0%
there awesome everything came in good
 

00:05:17.520 --> 00:05:20.590 align:start position:0%
there awesome everything came in good
click<00:05:17.840><c> time</c><00:05:18.160><c> install</c><00:05:18.600><c> time</c><00:05:18.759><c> and</c><00:05:18.960><c> install</c><00:05:19.600><c> date</c>

00:05:20.590 --> 00:05:20.600 align:start position:0%
click time install time and install date
 

00:05:20.600 --> 00:05:23.790 align:start position:0%
click time install time and install date
how<00:05:20.720><c> many</c><00:05:21.000><c> rows</c><00:05:22.039><c> though</c><00:05:23.039><c> all</c><00:05:23.160><c> right</c><00:05:23.360><c> hopefully</c>

00:05:23.790 --> 00:05:23.800 align:start position:0%
how many rows though all right hopefully
 

00:05:23.800 --> 00:05:25.469 align:start position:0%
how many rows though all right hopefully
it'll<00:05:24.039><c> be</c><00:05:24.280><c> okay</c><00:05:24.440><c> so</c><00:05:24.600><c> it</c><00:05:24.720><c> looks</c><00:05:24.880><c> like</c><00:05:25.039><c> the</c><00:05:25.160><c> data</c>

00:05:25.469 --> 00:05:25.479 align:start position:0%
it'll be okay so it looks like the data
 

00:05:25.479 --> 00:05:27.749 align:start position:0%
it'll be okay so it looks like the data
did<00:05:25.720><c> get</c><00:05:26.199><c> get</c><00:05:26.400><c> pumped</c><00:05:26.720><c> in</c><00:05:26.960><c> there</c><00:05:27.479><c> the</c><00:05:27.600><c> main</c>

00:05:27.749 --> 00:05:27.759 align:start position:0%
did get get pumped in there the main
 

00:05:27.759 --> 00:05:30.469 align:start position:0%
did get get pumped in there the main
thing<00:05:27.919><c> I</c><00:05:28.000><c> did</c><00:05:28.199><c> was</c><00:05:28.360><c> I</c><00:05:28.479><c> took</c><00:05:28.680><c> that</c><00:05:28.840><c> SQL</c><00:05:29.280><c> file</c>

00:05:30.469 --> 00:05:30.479 align:start position:0%
thing I did was I took that SQL file
 

00:05:30.479 --> 00:05:32.390 align:start position:0%
thing I did was I took that SQL file
and<00:05:30.800><c> all</c><00:05:30.919><c> you</c><00:05:31.000><c> need</c><00:05:31.160><c> to</c><00:05:31.319><c> do</c><00:05:31.800><c> actually</c><00:05:32.160><c> over</c>

00:05:32.390 --> 00:05:32.400 align:start position:0%
and all you need to do actually over
 

00:05:32.400 --> 00:05:37.870 align:start position:0%
and all you need to do actually over
here<00:05:32.560><c> is</c><00:05:33.280><c> file</c><00:05:34.280><c> uh</c><00:05:34.520><c> let's</c><00:05:34.919><c> see</c><00:05:35.919><c> uh</c><00:05:36.120><c> open</c><00:05:36.440><c> a</c><00:05:36.639><c> SQL</c>

00:05:37.870 --> 00:05:37.880 align:start position:0%
here is file uh let's see uh open a SQL
 

00:05:37.880 --> 00:05:39.710 align:start position:0%
here is file uh let's see uh open a SQL
file

00:05:39.710 --> 00:05:39.720 align:start position:0%
file
 

00:05:39.720 --> 00:05:42.950 align:start position:0%
file
um<00:05:40.720><c> there's</c><00:05:40.880><c> a</c><00:05:41.000><c> way</c><00:05:41.120><c> to</c><00:05:41.360><c> import</c><00:05:41.800><c> SQL</c><00:05:42.240><c> in</c><00:05:42.360><c> there</c>

00:05:42.950 --> 00:05:42.960 align:start position:0%
um there's a way to import SQL in there
 

00:05:42.960 --> 00:05:47.270 align:start position:0%
um there's a way to import SQL in there
file<00:05:43.960><c> load</c><00:05:44.400><c> SQL</c><00:05:45.000><c> file</c><00:05:45.840><c> right</c><00:05:46.039><c> and</c><00:05:46.160><c> then</c><00:05:46.360><c> I</c><00:05:47.000><c> it</c>

00:05:47.270 --> 00:05:47.280 align:start position:0%
file load SQL file right and then I it
 

00:05:47.280 --> 00:05:50.790 align:start position:0%
file load SQL file right and then I it
and<00:05:47.440><c> then</c><00:05:47.560><c> you</c><00:05:47.919><c> choose</c><00:05:48.600><c> that</c><00:05:49.600><c> right</c><00:05:49.880><c> and</c><00:05:50.039><c> then</c>

00:05:50.790 --> 00:05:50.800 align:start position:0%
and then you choose that right and then
 

00:05:50.800 --> 00:05:52.950 align:start position:0%
and then you choose that right and then
then<00:05:50.960><c> you</c><00:05:51.120><c> just</c><00:05:51.400><c> press</c><00:05:52.319><c> and</c><00:05:52.440><c> then</c><00:05:52.560><c> you</c><00:05:52.720><c> just</c>

00:05:52.950 --> 00:05:52.960 align:start position:0%
then you just press and then you just
 

00:05:52.960 --> 00:05:55.830 align:start position:0%
then you just press and then you just
press<00:05:53.240><c> Run</c><00:05:54.120><c> Okay</c><00:05:54.400><c> so</c><00:05:54.960><c> that's</c><00:05:55.160><c> kind</c><00:05:55.280><c> of</c><00:05:55.440><c> we</c><00:05:55.560><c> take</c>

00:05:55.830 --> 00:05:55.840 align:start position:0%
press Run Okay so that's kind of we take
 

00:05:55.840 --> 00:05:59.230 align:start position:0%
press Run Okay so that's kind of we take
that<00:05:55.960><c> SQL</c><00:05:56.360><c> file</c><00:05:56.680><c> we</c><00:05:56.880><c> push</c><00:05:57.120><c> that</c><00:05:57.280><c> SQL</c><00:05:58.120><c> data</c><00:05:59.120><c> uh</c>

00:05:59.230 --> 00:05:59.240 align:start position:0%
that SQL file we push that SQL data uh
 

00:05:59.240 --> 00:06:01.950 align:start position:0%
that SQL file we push that SQL data uh
correctly<00:05:59.840><c> formatted</c><00:06:00.360><c> into</c><00:06:00.600><c> our</c><00:06:00.800><c> Cloud</c><00:06:01.120><c> DB</c>

00:06:01.950 --> 00:06:01.960 align:start position:0%
correctly formatted into our Cloud DB
 

00:06:01.960 --> 00:06:05.070 align:start position:0%
correctly formatted into our Cloud DB
and<00:06:02.520><c> it's</c><00:06:03.120><c> uh</c><00:06:03.440><c> and</c><00:06:03.560><c> we</c><00:06:03.759><c> just</c><00:06:03.960><c> ran</c><00:06:04.440><c> that</c><00:06:04.759><c> that</c>

00:06:05.070 --> 00:06:05.080 align:start position:0%
and it's uh and we just ran that that
 

00:06:05.080 --> 00:06:08.189 align:start position:0%
and it's uh and we just ran that that
query<00:06:06.080><c> which</c><00:06:06.479><c> proved</c><00:06:06.880><c> to</c><00:06:07.120><c> us</c><00:06:07.599><c> that</c><00:06:07.880><c> everything</c>

00:06:08.189 --> 00:06:08.199 align:start position:0%
query which proved to us that everything
 

00:06:08.199 --> 00:06:09.909 align:start position:0%
query which proved to us that everything
is<00:06:08.440><c> the</c><00:06:08.520><c> way</c><00:06:08.680><c> that</c><00:06:08.800><c> it</c><00:06:08.919><c> needs</c><00:06:09.160><c> to</c><00:06:09.360><c> be</c><00:06:09.720><c> right</c>

00:06:09.909 --> 00:06:09.919 align:start position:0%
is the way that it needs to be right
 

00:06:09.919 --> 00:06:13.270 align:start position:0%
is the way that it needs to be right
select<00:06:10.280><c> all</c><00:06:10.479><c> from</c><00:06:10.759><c> conversion</c><00:06:11.560><c> data</c><00:06:12.560><c> show</c><00:06:12.800><c> me</c>

00:06:13.270 --> 00:06:13.280 align:start position:0%
select all from conversion data show me
 

00:06:13.280 --> 00:06:15.150 align:start position:0%
select all from conversion data show me
what's<00:06:13.440><c> in</c><00:06:13.599><c> the</c><00:06:13.759><c> conversion</c><00:06:14.240><c> data</c><00:06:14.599><c> table</c><00:06:14.960><c> of</c>

00:06:15.150 --> 00:06:15.160 align:start position:0%
what's in the conversion data table of
 

00:06:15.160 --> 00:06:17.589 align:start position:0%
what's in the conversion data table of
this<00:06:15.360><c> live</c><00:06:15.720><c> SQL</c><00:06:16.160><c> database</c><00:06:16.880><c> and</c><00:06:17.000><c> it</c><00:06:17.160><c> looks</c><00:06:17.400><c> like</c>

00:06:17.589 --> 00:06:17.599 align:start position:0%
this live SQL database and it looks like
 

00:06:17.599 --> 00:06:20.309 align:start position:0%
this live SQL database and it looks like
everything<00:06:17.880><c> is</c><00:06:18.039><c> all</c><00:06:18.400><c> good</c><00:06:19.400><c> so</c><00:06:19.639><c> now</c><00:06:19.840><c> we</c><00:06:20.000><c> just</c><00:06:20.120><c> go</c>

00:06:20.309 --> 00:06:20.319 align:start position:0%
everything is all good so now we just go
 

00:06:20.319 --> 00:06:22.870 align:start position:0%
everything is all good so now we just go
into<00:06:20.599><c> metabase</c><00:06:21.240><c> at</c><00:06:21.400><c> this</c>

00:06:22.870 --> 00:06:22.880 align:start position:0%
into metabase at this
 

00:06:22.880 --> 00:06:25.950 align:start position:0%
into metabase at this
point<00:06:23.880><c> let's</c><00:06:24.120><c> open</c><00:06:24.400><c> this</c><00:06:24.599><c> up</c><00:06:24.840><c> over</c><00:06:25.120><c> here</c><00:06:25.720><c> let's</c>

00:06:25.950 --> 00:06:25.960 align:start position:0%
point let's open this up over here let's
 

00:06:25.960 --> 00:06:27.430 align:start position:0%
point let's open this up over here let's
go<00:06:26.280><c> into</c>

00:06:27.430 --> 00:06:27.440 align:start position:0%
go into
 

00:06:27.440 --> 00:06:30.790 align:start position:0%
go into
m-<00:06:28.440><c> analytics</c><00:06:29.240><c> one</c><00:06:29.400><c> second</c><00:06:29.639><c> second</c><00:06:30.039><c> guys</c><00:06:30.680><c> this</c>

00:06:30.790 --> 00:06:30.800 align:start position:0%
m- analytics one second second guys this
 

00:06:30.800 --> 00:06:33.469 align:start position:0%
m- analytics one second second guys this
is<00:06:30.919><c> a</c><00:06:31.039><c> metabase</c><00:06:31.639><c> deployment</c><00:06:32.280><c> if</c><00:06:32.360><c> you</c><00:06:32.479><c> look</c><00:06:32.639><c> at</c>

00:06:33.469 --> 00:06:33.479 align:start position:0%
is a metabase deployment if you look at
 

00:06:33.479 --> 00:06:37.629 align:start position:0%
is a metabase deployment if you look at
the<00:06:34.479><c> uh</c><00:06:34.919><c> right</c><00:06:35.080><c> the</c><00:06:35.280><c> previous</c><00:06:35.840><c> videos</c><00:06:36.479><c> or</c><00:06:37.400><c> uh</c>

00:06:37.629 --> 00:06:37.639 align:start position:0%
the uh right the previous videos or uh
 

00:06:37.639 --> 00:06:40.749 align:start position:0%
the uh right the previous videos or uh
I've<00:06:37.800><c> got</c><00:06:38.000><c> other</c><00:06:38.240><c> videos</c><00:06:38.599><c> on</c><00:06:38.840><c> how</c><00:06:39.199><c> to</c><00:06:40.199><c> actually</c>

00:06:40.749 --> 00:06:40.759 align:start position:0%
I've got other videos on how to actually
 

00:06:40.759 --> 00:06:43.990 align:start position:0%
I've got other videos on how to actually
create<00:06:41.199><c> a</c><00:06:41.880><c> metabase</c><00:06:42.479><c> deployment</c><00:06:43.039><c> on</c><00:06:43.240><c> Heroku</c>

00:06:43.990 --> 00:06:44.000 align:start position:0%
create a metabase deployment on Heroku
 

00:06:44.000 --> 00:06:48.430 align:start position:0%
create a metabase deployment on Heroku
as

00:06:48.430 --> 00:06:48.440 align:start position:0%
 
 

00:06:48.440 --> 00:06:51.469 align:start position:0%
 
well<00:06:49.440><c> because</c><00:06:49.720><c> I'm</c><00:06:49.880><c> not</c><00:06:50.240><c> paying</c><00:06:50.759><c> for</c><00:06:51.080><c> the</c>

00:06:51.469 --> 00:06:51.479 align:start position:0%
well because I'm not paying for the
 

00:06:51.479 --> 00:06:53.309 align:start position:0%
well because I'm not paying for the
metabase

00:06:53.309 --> 00:06:53.319 align:start position:0%
metabase
 

00:06:53.319 --> 00:06:58.029 align:start position:0%
metabase
um<00:06:54.319><c> uh</c><00:06:54.440><c> metabase</c><00:06:55.520><c> server</c><00:06:56.520><c> then</c><00:06:57.080><c> uh</c><00:06:57.479><c> it</c><00:06:57.879><c> the</c>

00:06:58.029 --> 00:06:58.039 align:start position:0%
um uh metabase server then uh it the
 

00:06:58.039 --> 00:07:00.469 align:start position:0%
um uh metabase server then uh it the
server<00:06:58.360><c> goes</c><00:06:58.639><c> to</c><00:06:58.879><c> sleep</c><00:06:59.639><c> and</c><00:06:59.840><c> when</c><00:07:00.000><c> the</c><00:07:00.120><c> server</c>

00:07:00.469 --> 00:07:00.479 align:start position:0%
server goes to sleep and when the server
 

00:07:00.479 --> 00:07:01.589 align:start position:0%
server goes to sleep and when the server
goes<00:07:00.720><c> to</c>

00:07:01.589 --> 00:07:01.599 align:start position:0%
goes to
 

00:07:01.599 --> 00:07:03.110 align:start position:0%
goes to
sleep

00:07:03.110 --> 00:07:03.120 align:start position:0%
sleep
 

00:07:03.120 --> 00:07:06.950 align:start position:0%
sleep
um<00:07:04.120><c> if</c><00:07:04.240><c> I</c><00:07:04.400><c> don't</c><00:07:04.639><c> use</c><00:07:05.240><c> metabase</c><00:07:06.240><c> um</c><00:07:06.520><c> for</c><00:07:06.720><c> a</c>

00:07:06.950 --> 00:07:06.960 align:start position:0%
um if I don't use metabase um for a
 

00:07:06.960 --> 00:07:09.270 align:start position:0%
um if I don't use metabase um for a
specific<00:07:07.400><c> amount</c><00:07:07.759><c> over</c><00:07:07.960><c> a</c><00:07:08.160><c> specific</c><00:07:08.599><c> interval</c>

00:07:09.270 --> 00:07:09.280 align:start position:0%
specific amount over a specific interval
 

00:07:09.280 --> 00:07:10.909 align:start position:0%
specific amount over a specific interval
the<00:07:09.400><c> server</c><00:07:09.680><c> goes</c><00:07:09.879><c> to</c><00:07:10.080><c> sleep</c><00:07:10.319><c> and</c><00:07:10.479><c> you</c><00:07:10.599><c> need</c><00:07:10.759><c> to</c>

00:07:10.909 --> 00:07:10.919 align:start position:0%
the server goes to sleep and you need to
 

00:07:10.919 --> 00:07:12.830 align:start position:0%
the server goes to sleep and you need to
wake<00:07:11.160><c> it</c><00:07:11.319><c> up</c><00:07:11.639><c> so</c><00:07:11.960><c> that's</c><00:07:12.160><c> what's</c><00:07:12.360><c> happening</c>

00:07:12.830 --> 00:07:12.840 align:start position:0%
wake it up so that's what's happening
 

00:07:12.840 --> 00:07:15.469 align:start position:0%
wake it up so that's what's happening
right<00:07:13.039><c> now</c><00:07:13.919><c> takes</c><00:07:14.160><c> a</c><00:07:14.280><c> little</c><00:07:14.599><c> time</c><00:07:15.039><c> but</c><00:07:15.240><c> what</c>

00:07:15.469 --> 00:07:15.479 align:start position:0%
right now takes a little time but what
 

00:07:15.479 --> 00:07:18.110 align:start position:0%
right now takes a little time but what
it's<00:07:15.599><c> only</c><00:07:15.800><c> about</c><00:07:16.000><c> 10</c><00:07:16.319><c> seconds</c><00:07:16.919><c> all</c><00:07:17.080><c> right</c><00:07:17.840><c> now</c>

00:07:18.110 --> 00:07:18.120 align:start position:0%
it's only about 10 seconds all right now
 

00:07:18.120 --> 00:07:20.589 align:start position:0%
it's only about 10 seconds all right now
I<00:07:18.240><c> go</c><00:07:18.560><c> within</c><00:07:18.919><c> metabase</c><00:07:19.680><c> I</c><00:07:19.759><c> need</c><00:07:19.919><c> to</c><00:07:20.080><c> go</c><00:07:20.240><c> into</c>

00:07:20.589 --> 00:07:20.599 align:start position:0%
I go within metabase I need to go into
 

00:07:20.599 --> 00:07:22.110 align:start position:0%
I go within metabase I need to go into
settings

00:07:22.110 --> 00:07:22.120 align:start position:0%
settings
 

00:07:22.120 --> 00:07:25.309 align:start position:0%
settings
admin<00:07:23.120><c> I</c><00:07:23.240><c> go</c><00:07:23.560><c> into</c><00:07:24.000><c> databases</c><00:07:25.000><c> and</c><00:07:25.120><c> I'm</c><00:07:25.240><c> going</c>

00:07:25.309 --> 00:07:25.319 align:start position:0%
admin I go into databases and I'm going
 

00:07:25.319 --> 00:07:27.309 align:start position:0%
admin I go into databases and I'm going
to<00:07:25.440><c> do</c><00:07:25.599><c> the</c><00:07:25.720><c> same</c><00:07:26.039><c> process</c><00:07:26.440><c> that</c><00:07:26.560><c> I</c><00:07:26.680><c> did</c><00:07:26.919><c> before</c>

00:07:27.309 --> 00:07:27.319 align:start position:0%
to do the same process that I did before
 

00:07:27.319 --> 00:07:29.909 align:start position:0%
to do the same process that I did before
I'm<00:07:27.400><c> going</c><00:07:27.520><c> to</c><00:07:27.639><c> press</c><00:07:27.960><c> add</c><00:07:28.240><c> database</c>

00:07:29.909 --> 00:07:29.919 align:start position:0%
I'm going to press add database
 

00:07:29.919 --> 00:07:32.909 align:start position:0%
I'm going to press add database
and<00:07:30.319><c> again</c><00:07:30.840><c> this</c><00:07:31.000><c> time</c><00:07:31.199><c> I'm</c><00:07:31.319><c> using</c><00:07:31.759><c> metabase</c>

00:07:32.909 --> 00:07:32.919 align:start position:0%
and again this time I'm using metabase
 

00:07:32.919 --> 00:07:37.749 align:start position:0%
and again this time I'm using metabase
here<00:07:33.919><c> control</c><00:07:35.039><c> left</c><00:07:36.039><c> and</c><00:07:36.800><c> let's</c><00:07:37.000><c> go</c><00:07:37.240><c> into</c><00:07:37.599><c> our</c>

00:07:37.749 --> 00:07:37.759 align:start position:0%
here control left and let's go into our
 

00:07:37.759 --> 00:07:40.670 align:start position:0%
here control left and let's go into our
Jaz<00:07:38.120><c> DB</c><00:07:38.599><c> credentials</c><00:07:39.599><c> okay</c><00:07:39.879><c> cool</c><00:07:40.199><c> I've</c><00:07:40.400><c> got</c>

00:07:40.670 --> 00:07:40.680 align:start position:0%
Jaz DB credentials okay cool I've got
 

00:07:40.680 --> 00:07:44.629 align:start position:0%
Jaz DB credentials okay cool I've got
that<00:07:41.360><c> I</c><00:07:41.599><c> press</c><00:07:41.919><c> windows</c><00:07:42.319><c> and</c><00:07:42.560><c> control</c><00:07:43.639><c> left</c>

00:07:44.629 --> 00:07:44.639 align:start position:0%
that I press windows and control left
 

00:07:44.639 --> 00:07:46.629 align:start position:0%
that I press windows and control left
and<00:07:44.879><c> what</c><00:07:45.039><c> type</c><00:07:45.199><c> of</c><00:07:45.479><c> datab</c><00:07:45.840><c> Base</c><00:07:46.080><c> do</c><00:07:46.240><c> I</c><00:07:46.360><c> want</c><00:07:46.479><c> to</c>

00:07:46.629 --> 00:07:46.639 align:start position:0%
and what type of datab Base do I want to
 

00:07:46.639 --> 00:07:48.869 align:start position:0%
and what type of datab Base do I want to
connect<00:07:46.960><c> to</c><00:07:47.120><c> metabase</c><00:07:47.599><c> a</c><00:07:47.800><c> MySQL</c><00:07:48.479><c> Cloud</c>

00:07:48.869 --> 00:07:48.879 align:start position:0%
connect to metabase a MySQL Cloud
 

00:07:48.879 --> 00:07:51.270 align:start position:0%
connect to metabase a MySQL Cloud
database<00:07:49.639><c> I'm</c><00:07:49.840><c> going</c><00:07:50.280><c> Cloud</c><00:07:50.720><c> whatever</c><00:07:51.080><c> I</c><00:07:51.159><c> want</c>

00:07:51.270 --> 00:07:51.280 align:start position:0%
database I'm going Cloud whatever I want
 

00:07:51.280 --> 00:07:54.670 align:start position:0%
database I'm going Cloud whatever I want
to<00:07:51.479><c> call</c><00:07:51.720><c> it</c><00:07:52.000><c> I'm</c><00:07:52.120><c> going</c><00:07:52.240><c> to</c><00:07:52.360><c> do</c><00:07:52.840><c> call</c><00:07:53.680><c> metabase</c>

00:07:54.670 --> 00:07:54.680 align:start position:0%
to call it I'm going to do call metabase
 

00:07:54.680 --> 00:07:59.230 align:start position:0%
to call it I'm going to do call metabase
I'm<00:07:54.800><c> going</c><00:07:54.919><c> to</c><00:07:55.039><c> do</c><00:07:55.280><c> CSV</c><00:07:56.280><c> to</c><00:07:57.280><c> metabase</c><00:07:58.280><c> demo</c>

00:07:59.230 --> 00:07:59.240 align:start position:0%
I'm going to do CSV to metabase demo
 

00:07:59.240 --> 00:08:01.990 align:start position:0%
I'm going to do CSV to metabase demo
okay<00:07:59.759><c> the</c><00:08:00.000><c> host</c><00:08:00.599><c> again</c><00:08:01.000><c> I</c><00:08:01.120><c> just</c><00:08:01.360><c> copy</c><00:08:01.720><c> paste</c>

00:08:01.990 --> 00:08:02.000 align:start position:0%
okay the host again I just copy paste
 

00:08:02.000 --> 00:08:02.909 align:start position:0%
okay the host again I just copy paste
these

00:08:02.909 --> 00:08:02.919 align:start position:0%
these
 

00:08:02.919 --> 00:08:06.029 align:start position:0%
these
values<00:08:04.000><c> value</c><00:08:05.000><c> contrl</c>

00:08:06.029 --> 00:08:06.039 align:start position:0%
values value contrl
 

00:08:06.039 --> 00:08:10.390 align:start position:0%
values value contrl
C<00:08:07.039><c> host</c><00:08:08.000><c> okay</c><00:08:08.280><c> get</c><00:08:08.479><c> rid</c><00:08:08.680><c> of</c><00:08:08.960><c> the</c><00:08:09.120><c> empty</c>

00:08:10.390 --> 00:08:10.400 align:start position:0%
C host okay get rid of the empty
 

00:08:10.400 --> 00:08:14.749 align:start position:0%
C host okay get rid of the empty
space<00:08:11.400><c> HD</c><00:08:12.199><c> blank</c><00:08:12.879><c> the</c><00:08:13.440><c> username</c><00:08:14.440><c> straight</c>

00:08:14.749 --> 00:08:14.759 align:start position:0%
space HD blank the username straight
 

00:08:14.759 --> 00:08:17.749 align:start position:0%
space HD blank the username straight
from<00:08:14.960><c> ja</c><00:08:15.319><c> DB</c><00:08:15.840><c> again</c>

00:08:17.749 --> 00:08:17.759 align:start position:0%
from ja DB again
 

00:08:17.759 --> 00:08:19.589 align:start position:0%
from ja DB again
username

00:08:19.589 --> 00:08:19.599 align:start position:0%
username
 

00:08:19.599 --> 00:08:24.869 align:start position:0%
username
okay<00:08:20.599><c> all</c><00:08:20.960><c> good</c><00:08:21.960><c> and</c><00:08:22.199><c> the</c>

00:08:24.869 --> 00:08:24.879 align:start position:0%
 
 

00:08:24.879 --> 00:08:28.230 align:start position:0%
 
password<00:08:25.879><c> okay</c><00:08:26.080><c> here's</c><00:08:26.360><c> my</c>

00:08:28.230 --> 00:08:28.240 align:start position:0%
password okay here's my
 

00:08:28.240 --> 00:08:31.350 align:start position:0%
password okay here's my
password<00:08:29.240><c> and</c><00:08:29.560><c> and</c><00:08:29.759><c> the</c><00:08:29.919><c> port</c>

00:08:31.350 --> 00:08:31.360 align:start position:0%
password and and the port
 

00:08:31.360 --> 00:08:32.870 align:start position:0%
password and and the port
3306

00:08:32.870 --> 00:08:32.880 align:start position:0%
3306
 

00:08:32.880 --> 00:08:34.829 align:start position:0%
3306
yes

00:08:34.829 --> 00:08:34.839 align:start position:0%
yes
 

00:08:34.839 --> 00:08:37.829 align:start position:0%
yes
3306<00:08:35.839><c> and</c><00:08:36.000><c> the</c><00:08:36.200><c> database</c>

00:08:37.829 --> 00:08:37.839 align:start position:0%
3306 and the database
 

00:08:37.839 --> 00:08:41.310 align:start position:0%
3306 and the database
name<00:08:38.839><c> take</c><00:08:39.039><c> from</c>

00:08:41.310 --> 00:08:41.320 align:start position:0%
name take from
 

00:08:41.320 --> 00:08:44.470 align:start position:0%
name take from
here<00:08:42.320><c> all</c><00:08:42.479><c> right</c><00:08:42.719><c> everything</c><00:08:43.000><c> is</c><00:08:43.279><c> good</c><00:08:44.200><c> uh</c><00:08:44.360><c> I</c>

00:08:44.470 --> 00:08:44.480 align:start position:0%
here all right everything is good uh I
 

00:08:44.480 --> 00:08:47.389 align:start position:0%
here all right everything is good uh I
got<00:08:44.640><c> it</c><00:08:44.959><c> I</c><00:08:45.080><c> got</c><00:08:45.279><c> everything</c><00:08:45.720><c> CSV</c><00:08:46.240><c> to</c><00:08:46.360><c> metabase</c>

00:08:47.389 --> 00:08:47.399 align:start position:0%
got it I got everything CSV to metabase
 

00:08:47.399 --> 00:08:50.509 align:start position:0%
got it I got everything CSV to metabase
demo<00:08:48.399><c> and</c><00:08:49.080><c> I'm</c><00:08:49.200><c> going</c><00:08:49.320><c> to</c><00:08:49.560><c> go</c><00:08:49.720><c> ahead</c><00:08:49.920><c> and</c><00:08:50.200><c> press</c>

00:08:50.509 --> 00:08:50.519 align:start position:0%
demo and I'm going to go ahead and press
 

00:08:50.519 --> 00:08:57.710 align:start position:0%
demo and I'm going to go ahead and press
save<00:08:50.800><c> on</c><00:08:50.959><c> the</c><00:08:51.080><c> bottom</c><00:08:51.440><c> let's</c><00:08:51.600><c> see</c><00:08:51.760><c> if</c><00:08:51.880><c> it</c>

00:08:57.710 --> 00:08:57.720 align:start position:0%
 
 

00:08:57.720 --> 00:09:00.670 align:start position:0%
 
works<00:08:58.720><c> see</c><00:08:58.959><c> H</c><00:08:59.120><c> connect</c><00:08:59.560><c> connect</c><00:08:59.800><c> to</c><00:09:00.079><c> address</c>

00:09:00.670 --> 00:09:00.680 align:start position:0%
works see H connect connect to address
 

00:09:00.680 --> 00:09:03.829 align:start position:0%
works see H connect connect to address
blank<00:09:01.120><c> socket</c><00:09:01.480><c> fail</c><00:09:01.800><c> to</c>

00:09:03.829 --> 00:09:03.839 align:start position:0%
blank socket fail to
 

00:09:03.839 --> 00:09:08.190 align:start position:0%
blank socket fail to
connect<00:09:04.839><c> uh</c><00:09:05.079><c> let's</c><00:09:05.279><c> see</c><00:09:05.680><c> here</c><00:09:06.680><c> the</c>

00:09:08.190 --> 00:09:08.200 align:start position:0%
connect uh let's see here the
 

00:09:08.200 --> 00:09:12.509 align:start position:0%
connect uh let's see here the
host<00:09:09.200><c> database</c><00:09:09.839><c> name</c><00:09:10.760><c> that's</c><00:09:11.040><c> good</c><00:09:11.279><c> database</c>

00:09:12.509 --> 00:09:12.519 align:start position:0%
host database name that's good database
 

00:09:12.519 --> 00:09:14.990 align:start position:0%
host database name that's good database
username<00:09:13.519><c> uh</c><00:09:13.640><c> maybe</c><00:09:13.880><c> the</c><00:09:14.040><c> database</c><00:09:14.600><c> password</c>

00:09:14.990 --> 00:09:15.000 align:start position:0%
username uh maybe the database password
 

00:09:15.000 --> 00:09:16.910 align:start position:0%
username uh maybe the database password
has<00:09:15.240><c> Extra</c><00:09:15.640><c> Spaces</c><00:09:16.120><c> so</c><00:09:16.279><c> I'm</c><00:09:16.360><c> going</c><00:09:16.480><c> to</c><00:09:16.640><c> grab</c>

00:09:16.910 --> 00:09:16.920 align:start position:0%
has Extra Spaces so I'm going to grab
 

00:09:16.920 --> 00:09:18.269 align:start position:0%
has Extra Spaces so I'm going to grab
the<00:09:17.120><c> database</c>

00:09:18.269 --> 00:09:18.279 align:start position:0%
the database
 

00:09:18.279 --> 00:09:20.509 align:start position:0%
the database
password<00:09:19.279><c> and</c><00:09:19.480><c> just</c><00:09:19.720><c> paste</c><00:09:19.959><c> it</c><00:09:20.160><c> into</c>

00:09:20.509 --> 00:09:20.519 align:start position:0%
password and just paste it into
 

00:09:20.519 --> 00:09:24.509 align:start position:0%
password and just paste it into
something

00:09:24.509 --> 00:09:24.519 align:start position:0%
 
 

00:09:24.519 --> 00:09:28.030 align:start position:0%
 
else<00:09:25.519><c> contrl</c>

00:09:28.030 --> 00:09:28.040 align:start position:0%
 
 

00:09:28.040 --> 00:09:34.829 align:start position:0%
 
c<00:09:29.040><c> h</c><00:09:29.399><c> use</c><00:09:29.640><c> a</c><00:09:29.880><c> secure</c><00:09:30.399><c> SSL</c><00:09:31.279><c> fine</c><00:09:32.279><c> use</c>

00:09:34.829 --> 00:09:34.839 align:start position:0%
 
 

00:09:34.839 --> 00:09:38.150 align:start position:0%
 
SSH<00:09:35.839><c> no</c><00:09:36.240><c> I</c><00:09:36.320><c> don't</c><00:09:36.440><c> want</c><00:09:36.560><c> to</c><00:09:36.720><c> use</c><00:09:36.959><c> that</c><00:09:37.200><c> I</c><00:09:37.440><c> press</c>

00:09:38.150 --> 00:09:38.160 align:start position:0%
SSH no I don't want to use that I press
 

00:09:38.160 --> 00:09:41.150 align:start position:0%
SSH no I don't want to use that I press
save<00:09:39.160><c> could</c><00:09:39.399><c> not</c><00:09:39.560><c> connect</c><00:09:39.839><c> to</c><00:09:40.120><c> address</c><00:09:40.720><c> host</c>

00:09:41.150 --> 00:09:41.160 align:start position:0%
save could not connect to address host
 

00:09:41.160 --> 00:09:47.230 align:start position:0%
save could not connect to address host
equals<00:09:41.959><c> blank</c><00:09:42.959><c> socket</c><00:09:43.519><c> fail</c><00:09:43.880><c> to</c>

00:09:47.230 --> 00:09:47.240 align:start position:0%
 
 

00:09:47.240 --> 00:09:49.150 align:start position:0%
 
connect<00:09:48.240><c> let's</c>

00:09:49.150 --> 00:09:49.160 align:start position:0%
connect let's
 

00:09:49.160 --> 00:09:51.630 align:start position:0%
connect let's
see<00:09:50.160><c> all</c><00:09:50.320><c> right</c><00:09:50.760><c> uh</c><00:09:50.920><c> looks</c><00:09:51.079><c> like</c><00:09:51.240><c> we're</c><00:09:51.360><c> up</c><00:09:51.480><c> to</c>

00:09:51.630 --> 00:09:51.640 align:start position:0%
see all right uh looks like we're up to
 

00:09:51.640 --> 00:09:53.430 align:start position:0%
see all right uh looks like we're up to
10<00:09:51.839><c> minutes</c><00:09:52.120><c> over</c><00:09:52.279><c> here</c><00:09:52.440><c> so</c><00:09:52.560><c> in</c><00:09:52.680><c> the</c><00:09:52.800><c> next</c><00:09:53.360><c> in</c>

00:09:53.430 --> 00:09:53.440 align:start position:0%
10 minutes over here so in the next in
 

00:09:53.440 --> 00:09:55.430 align:start position:0%
10 minutes over here so in the next in
the<00:09:53.600><c> next</c><00:09:53.880><c> video</c><00:09:54.279><c> we'll</c><00:09:54.560><c> just</c><00:09:54.920><c> uh</c><00:09:55.079><c> go</c><00:09:55.240><c> ahead</c>

00:09:55.430 --> 00:09:55.440 align:start position:0%
the next video we'll just uh go ahead
 

00:09:55.440 --> 00:09:57.790 align:start position:0%
the next video we'll just uh go ahead
and<00:09:55.640><c> continue</c><00:09:56.040><c> wrapping</c><00:09:56.519><c> things</c><00:09:56.839><c> up</c><00:09:57.240><c> and</c><00:09:57.440><c> I'll</c>

00:09:57.790 --> 00:09:57.800 align:start position:0%
and continue wrapping things up and I'll
 

00:09:57.800 --> 00:09:59.590 align:start position:0%
and continue wrapping things up and I'll
I'll<00:09:57.920><c> try</c><00:09:58.079><c> to</c><00:09:58.200><c> debug</c><00:09:58.680><c> this</c><00:09:58.800><c> and</c><00:09:58.959><c> get</c><00:09:59.120><c> back</c><00:09:59.519><c> to</c>

00:09:59.590 --> 00:09:59.600 align:start position:0%
I'll try to debug this and get back to
 

00:09:59.600 --> 00:10:01.590 align:start position:0%
I'll try to debug this and get back to
you<00:09:59.760><c> guys</c><00:09:59.880><c> soon</c><00:10:00.240><c> and</c><00:10:00.360><c> I'll</c><00:10:00.560><c> see</c><00:10:00.760><c> you</c><00:10:01.040><c> in</c><00:10:01.360><c> the</c>

00:10:01.590 --> 00:10:01.600 align:start position:0%
you guys soon and I'll see you in the
 

00:10:01.600 --> 00:10:04.839 align:start position:0%
you guys soon and I'll see you in the
next<00:10:01.839><c> one</c>

