import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDuration(duration: string | number): string {
  // Handle number input (duration in seconds)
  if (typeof duration === 'number') {
    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration % 3600) / 60)
    const seconds = duration % 60

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Handle string input (YouTube duration format PT4M13S)
  if (typeof duration === 'string') {
    const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/)
    if (!match) return duration

    const hours = match[1] ? parseInt(match[1].replace('H', '')) : 0
    const minutes = match[2] ? parseInt(match[2].replace('M', '')) : 0
    const seconds = match[3] ? parseInt(match[3].replace('S', '')) : 0

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Fallback for unexpected input types
  return String(duration)
}

export function formatViewCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M views`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K views`
  }
  return `${count} views`
}

export function formatDate(dateString: string | null | undefined): string {
  if (!dateString || dateString === 'null' || dateString === 'undefined') {
    // Handle NULL/empty dates - these are considered "older" videos
    return "Older video";
  }

  const date = new Date(dateString);

  if (isNaN(date.getTime())) {
    console.warn(`formatDate: Invalid date string received: "${dateString}"`);
    return `Invalid date: ${dateString.substring(0, 20)}${dateString.length > 20 ? '...' : ''}`;
  }

  // Return the actual publication date instead of relative time
  // Format: "MMM DD, YYYY" (e.g., "Dec 15, 2024")
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

export function truncateText(text: string | null | undefined, maxLength: number): string {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}
