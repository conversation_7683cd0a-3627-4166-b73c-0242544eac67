WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.790 align:start position:0%
 
hey<00:00:00.420><c> and</c><00:00:00.659><c> welcome</c><00:00:00.780><c> back</c><00:00:00.960><c> to</c><00:00:01.140><c> the</c><00:00:01.260><c> second</c><00:00:01.439><c> of</c>

00:00:01.790 --> 00:00:01.800 align:start position:0%
hey and welcome back to the second of
 

00:00:01.800 --> 00:00:03.710 align:start position:0%
hey and welcome back to the second of
five<00:00:02.040><c> principles</c><00:00:02.580><c> and</c><00:00:03.060><c> this</c><00:00:03.240><c> one</c><00:00:03.360><c> is</c><00:00:03.480><c> called</c>

00:00:03.710 --> 00:00:03.720 align:start position:0%
five principles and this one is called
 

00:00:03.720 --> 00:00:05.749 align:start position:0%
five principles and this one is called
the<00:00:04.020><c> open</c><00:00:04.140><c> closed</c><00:00:04.740><c> principle</c><00:00:05.100><c> another</c><00:00:05.339><c> way</c><00:00:05.640><c> to</c>

00:00:05.749 --> 00:00:05.759 align:start position:0%
the open closed principle another way to
 

00:00:05.759 --> 00:00:07.130 align:start position:0%
the open closed principle another way to
say<00:00:05.880><c> this</c><00:00:06.000><c> is</c><00:00:06.180><c> that</c><00:00:06.299><c> we</c><00:00:06.480><c> are</c><00:00:06.660><c> open</c><00:00:06.839><c> for</c>

00:00:07.130 --> 00:00:07.140 align:start position:0%
say this is that we are open for
 

00:00:07.140 --> 00:00:09.110 align:start position:0%
say this is that we are open for
extension<00:00:07.680><c> but</c><00:00:08.160><c> closed</c><00:00:08.519><c> for</c><00:00:08.639><c> modification</c>

00:00:09.110 --> 00:00:09.120 align:start position:0%
extension but closed for modification
 

00:00:09.120 --> 00:00:10.970 align:start position:0%
extension but closed for modification
what<00:00:09.660><c> does</c><00:00:09.780><c> that</c><00:00:09.900><c> mean</c><00:00:10.080><c> this</c><00:00:10.500><c> means</c><00:00:10.740><c> that</c><00:00:10.860><c> our</c>

00:00:10.970 --> 00:00:10.980 align:start position:0%
what does that mean this means that our
 

00:00:10.980 --> 00:00:12.830 align:start position:0%
what does that mean this means that our
code<00:00:11.099><c> should</c><00:00:11.340><c> be</c><00:00:11.460><c> designed</c><00:00:11.880><c> in</c><00:00:12.120><c> a</c><00:00:12.300><c> way</c><00:00:12.420><c> that</c>

00:00:12.830 --> 00:00:12.840 align:start position:0%
code should be designed in a way that
 

00:00:12.840 --> 00:00:14.690 align:start position:0%
code should be designed in a way that
allows<00:00:13.080><c> us</c><00:00:13.200><c> to</c><00:00:13.440><c> easily</c><00:00:13.799><c> add</c><00:00:14.040><c> new</c><00:00:14.219><c> features</c><00:00:14.519><c> or</c>

00:00:14.690 --> 00:00:14.700 align:start position:0%
allows us to easily add new features or
 

00:00:14.700 --> 00:00:17.269 align:start position:0%
allows us to easily add new features or
behaviors<00:00:15.179><c> without</c><00:00:15.780><c> modifying</c><00:00:16.619><c> the</c><00:00:16.920><c> existing</c>

00:00:17.269 --> 00:00:17.279 align:start position:0%
behaviors without modifying the existing
 

00:00:17.279 --> 00:00:18.650 align:start position:0%
behaviors without modifying the existing
code<00:00:17.460><c> we</c><00:00:17.880><c> should</c><00:00:18.000><c> be</c><00:00:18.119><c> able</c><00:00:18.180><c> to</c><00:00:18.300><c> extend</c><00:00:18.539><c> the</c>

00:00:18.650 --> 00:00:18.660 align:start position:0%
code we should be able to extend the
 

00:00:18.660 --> 00:00:20.450 align:start position:0%
code we should be able to extend the
functionality<00:00:19.140><c> of</c><00:00:19.320><c> the</c><00:00:19.500><c> software</c><00:00:19.800><c> or</c><00:00:20.160><c> code</c>

00:00:20.450 --> 00:00:20.460 align:start position:0%
functionality of the software or code
 

00:00:20.460 --> 00:00:23.090 align:start position:0%
functionality of the software or code
Base<00:00:20.699><c> by</c><00:00:21.359><c> adding</c><00:00:21.720><c> new</c><00:00:21.900><c> code</c><00:00:22.140><c> rather</c><00:00:22.859><c> than</c>

00:00:23.090 --> 00:00:23.100 align:start position:0%
Base by adding new code rather than
 

00:00:23.100 --> 00:00:24.950 align:start position:0%
Base by adding new code rather than
changing<00:00:23.640><c> the</c><00:00:23.880><c> existing</c><00:00:24.300><c> code</c><00:00:24.480><c> now</c><00:00:24.840><c> the</c>

00:00:24.950 --> 00:00:24.960 align:start position:0%
changing the existing code now the
 

00:00:24.960 --> 00:00:26.450 align:start position:0%
changing the existing code now the
second<00:00:25.140><c> part</c><00:00:25.320><c> closed</c><00:00:25.800><c> for</c><00:00:25.920><c> modification</c>

00:00:26.450 --> 00:00:26.460 align:start position:0%
second part closed for modification
 

00:00:26.460 --> 00:00:28.910 align:start position:0%
second part closed for modification
implies<00:00:27.359><c> that</c><00:00:27.539><c> once</c><00:00:27.779><c> a</c><00:00:27.960><c> class</c><00:00:28.140><c> module</c><00:00:28.740><c> or</c>

00:00:28.910 --> 00:00:28.920 align:start position:0%
implies that once a class module or
 

00:00:28.920 --> 00:00:32.030 align:start position:0%
implies that once a class module or
component<00:00:29.400><c> has</c><00:00:29.880><c> been</c><00:00:30.119><c> written</c><00:00:30.660><c> and</c><00:00:31.199><c> tested</c><00:00:31.679><c> we</c>

00:00:32.030 --> 00:00:32.040 align:start position:0%
component has been written and tested we
 

00:00:32.040 --> 00:00:33.889 align:start position:0%
component has been written and tested we
should<00:00:32.160><c> avoid</c><00:00:32.460><c> making</c><00:00:32.700><c> changes</c><00:00:33.120><c> to</c><00:00:33.300><c> it</c><00:00:33.420><c> this</c>

00:00:33.889 --> 00:00:33.899 align:start position:0%
should avoid making changes to it this
 

00:00:33.899 --> 00:00:36.229 align:start position:0%
should avoid making changes to it this
could<00:00:34.140><c> lead</c><00:00:34.440><c> to</c><00:00:34.680><c> new</c><00:00:34.920><c> bugs</c><00:00:35.280><c> or</c><00:00:35.460><c> untended</c><00:00:36.059><c> side</c>

00:00:36.229 --> 00:00:36.239 align:start position:0%
could lead to new bugs or untended side
 

00:00:36.239 --> 00:00:37.549 align:start position:0%
could lead to new bugs or untended side
effects<00:00:36.600><c> now</c><00:00:36.840><c> let's</c><00:00:37.020><c> just</c><00:00:37.140><c> jump</c><00:00:37.260><c> into</c><00:00:37.380><c> an</c>

00:00:37.549 --> 00:00:37.559 align:start position:0%
effects now let's just jump into an
 

00:00:37.559 --> 00:00:38.630 align:start position:0%
effects now let's just jump into an
example<00:00:37.800><c> to</c><00:00:37.980><c> see</c><00:00:38.100><c> what</c><00:00:38.160><c> I'm</c><00:00:38.340><c> talking</c><00:00:38.460><c> about</c>

00:00:38.630 --> 00:00:38.640 align:start position:0%
example to see what I'm talking about
 

00:00:38.640 --> 00:00:40.069 align:start position:0%
example to see what I'm talking about
okay<00:00:39.000><c> so</c><00:00:39.239><c> we</c><00:00:39.420><c> haven't</c><00:00:39.540><c> we</c><00:00:39.840><c> have</c><00:00:39.960><c> another</c>

00:00:40.069 --> 00:00:40.079 align:start position:0%
okay so we haven't we have another
 

00:00:40.079 --> 00:00:41.990 align:start position:0%
okay so we haven't we have another
employee<00:00:40.559><c> class</c><00:00:40.739><c> called</c><00:00:40.980><c> employee</c><00:00:41.399><c> OC</c><00:00:41.760><c> for</c>

00:00:41.990 --> 00:00:42.000 align:start position:0%
employee class called employee OC for
 

00:00:42.000 --> 00:00:44.090 align:start position:0%
employee class called employee OC for
open<00:00:42.120><c> close</c><00:00:42.420><c> principle</c><00:00:42.899><c> we</c><00:00:43.320><c> have</c><00:00:43.500><c> two</c><00:00:43.680><c> Fields</c>

00:00:44.090 --> 00:00:44.100 align:start position:0%
open close principle we have two Fields
 

00:00:44.100 --> 00:00:46.790 align:start position:0%
open close principle we have two Fields
so<00:00:44.340><c> Constructor</c><00:00:44.940><c> two</c><00:00:45.600><c> methods</c><00:00:46.079><c> that</c><00:00:46.260><c> get</c><00:00:46.500><c> the</c>

00:00:46.790 --> 00:00:46.800 align:start position:0%
so Constructor two methods that get the
 

00:00:46.800 --> 00:00:48.529 align:start position:0%
so Constructor two methods that get the
name<00:00:46.980><c> and</c><00:00:47.219><c> the</c><00:00:47.340><c> role</c><00:00:47.640><c> and</c><00:00:47.940><c> then</c><00:00:48.000><c> we</c><00:00:48.180><c> have</c><00:00:48.300><c> this</c>

00:00:48.529 --> 00:00:48.539 align:start position:0%
name and the role and then we have this
 

00:00:48.539 --> 00:00:50.510 align:start position:0%
name and the role and then we have this
method<00:00:48.840><c> called</c><00:00:49.020><c> perform</c><00:00:49.260><c> duties</c><00:00:49.860><c> which</c><00:00:50.399><c> has</c>

00:00:50.510 --> 00:00:50.520 align:start position:0%
method called perform duties which has
 

00:00:50.520 --> 00:00:52.369 align:start position:0%
method called perform duties which has
some<00:00:50.760><c> if</c><00:00:50.879><c> else</c><00:00:51.180><c> statements</c><00:00:51.660><c> depending</c><00:00:52.320><c> on</c>

00:00:52.369 --> 00:00:52.379 align:start position:0%
some if else statements depending on
 

00:00:52.379 --> 00:00:53.750 align:start position:0%
some if else statements depending on
what<00:00:52.559><c> role</c><00:00:52.860><c> it</c><00:00:52.980><c> is</c><00:00:53.100><c> then</c><00:00:53.280><c> it</c><00:00:53.460><c> prints</c><00:00:53.700><c> out</c>

00:00:53.750 --> 00:00:53.760 align:start position:0%
what role it is then it prints out
 

00:00:53.760 --> 00:00:55.610 align:start position:0%
what role it is then it prints out
something<00:00:53.940><c> now</c><00:00:54.300><c> this</c><00:00:54.539><c> class</c><00:00:54.719><c> violates</c><00:00:55.379><c> the</c>

00:00:55.610 --> 00:00:55.620 align:start position:0%
something now this class violates the
 

00:00:55.620 --> 00:00:57.650 align:start position:0%
something now this class violates the
principle<00:00:55.980><c> because</c><00:00:56.520><c> it</c><00:00:56.820><c> is</c><00:00:56.940><c> not</c><00:00:57.120><c> closed</c><00:00:57.539><c> for</c>

00:00:57.650 --> 00:00:57.660 align:start position:0%
principle because it is not closed for
 

00:00:57.660 --> 00:00:59.930 align:start position:0%
principle because it is not closed for
modification<00:00:58.199><c> now</c><00:00:59.039><c> look</c><00:00:59.280><c> at</c><00:00:59.460><c> the</c><00:00:59.579><c> perform</c>

00:00:59.930 --> 00:00:59.940 align:start position:0%
modification now look at the perform
 

00:00:59.940 --> 00:01:03.049 align:start position:0%
modification now look at the perform
duties<00:01:00.660><c> method</c><00:01:01.140><c> we</c><00:01:01.739><c> have</c><00:01:01.920><c> if</c><00:01:02.280><c> else</c><00:01:02.520><c> statements</c>

00:01:03.049 --> 00:01:03.059 align:start position:0%
duties method we have if else statements
 

00:01:03.059 --> 00:01:05.810 align:start position:0%
duties method we have if else statements
that<00:01:03.180><c> say</c><00:01:03.420><c> if</c><00:01:03.899><c> the</c><00:01:04.140><c> role</c><00:01:04.559><c> is</c><00:01:04.860><c> something</c><00:01:05.159><c> so</c><00:01:05.580><c> if</c>

00:01:05.810 --> 00:01:05.820 align:start position:0%
that say if the role is something so if
 

00:01:05.820 --> 00:01:07.550 align:start position:0%
that say if the role is something so if
it's<00:01:05.939><c> developer</c><00:01:06.479><c> then</c><00:01:06.780><c> we</c><00:01:07.020><c> print</c><00:01:07.200><c> out</c><00:01:07.380><c> what</c>

00:01:07.550 --> 00:01:07.560 align:start position:0%
it's developer then we print out what
 

00:01:07.560 --> 00:01:09.830 align:start position:0%
it's developer then we print out what
they're<00:01:07.680><c> doing</c><00:01:07.920><c> but</c><00:01:08.580><c> what</c><00:01:09.000><c> if</c><00:01:09.180><c> we</c><00:01:09.299><c> want</c><00:01:09.479><c> to</c><00:01:09.600><c> add</c>

00:01:09.830 --> 00:01:09.840 align:start position:0%
they're doing but what if we want to add
 

00:01:09.840 --> 00:01:11.510 align:start position:0%
they're doing but what if we want to add
a<00:01:09.960><c> new</c><00:01:10.140><c> role</c><00:01:10.500><c> then</c><00:01:10.920><c> what</c><00:01:11.159><c> we're</c><00:01:11.220><c> going</c><00:01:11.340><c> to</c><00:01:11.400><c> have</c>

00:01:11.510 --> 00:01:11.520 align:start position:0%
a new role then what we're going to have
 

00:01:11.520 --> 00:01:13.969 align:start position:0%
a new role then what we're going to have
to<00:01:11.640><c> do</c><00:01:11.820><c> is</c><00:01:12.119><c> modify</c><00:01:12.720><c> this</c><00:01:13.080><c> existing</c><00:01:13.560><c> method</c>

00:01:13.969 --> 00:01:13.979 align:start position:0%
to do is modify this existing method
 

00:01:13.979 --> 00:01:16.310 align:start position:0%
to do is modify this existing method
okay<00:01:14.220><c> that's</c><00:01:14.580><c> not</c><00:01:14.880><c> what</c><00:01:15.240><c> we</c><00:01:15.420><c> want</c><00:01:15.600><c> to</c><00:01:15.780><c> do</c><00:01:15.960><c> we're</c>

00:01:16.310 --> 00:01:16.320 align:start position:0%
okay that's not what we want to do we're
 

00:01:16.320 --> 00:01:18.649 align:start position:0%
okay that's not what we want to do we're
going<00:01:16.439><c> to</c><00:01:16.500><c> have</c><00:01:16.619><c> to</c><00:01:16.740><c> add</c><00:01:16.920><c> another</c><00:01:17.159><c> if</c><00:01:17.700><c> else</c><00:01:18.060><c> and</c>

00:01:18.649 --> 00:01:18.659 align:start position:0%
going to have to add another if else and
 

00:01:18.659 --> 00:01:20.749 align:start position:0%
going to have to add another if else and
depending<00:01:19.320><c> on</c><00:01:19.500><c> how</c><00:01:19.799><c> many</c><00:01:19.979><c> roles</c><00:01:20.520><c> or</c>

00:01:20.749 --> 00:01:20.759 align:start position:0%
depending on how many roles or
 

00:01:20.759 --> 00:01:22.490 align:start position:0%
depending on how many roles or
eventually<00:01:21.119><c> that</c><00:01:21.420><c> will</c><00:01:21.540><c> be</c><00:01:21.659><c> added</c><00:01:22.020><c> you</c><00:01:22.380><c> know</c>

00:01:22.490 --> 00:01:22.500 align:start position:0%
eventually that will be added you know
 

00:01:22.500 --> 00:01:24.590 align:start position:0%
eventually that will be added you know
we're<00:01:22.619><c> going</c><00:01:22.920><c> to</c><00:01:23.100><c> always</c><00:01:23.280><c> modify</c><00:01:23.939><c> this</c><00:01:24.240><c> method</c>

00:01:24.590 --> 00:01:24.600 align:start position:0%
we're going to always modify this method
 

00:01:24.600 --> 00:01:26.749 align:start position:0%
we're going to always modify this method
okay<00:01:24.780><c> we</c><00:01:25.439><c> don't</c><00:01:25.560><c> want</c><00:01:25.740><c> to</c><00:01:25.860><c> do</c><00:01:26.040><c> that</c><00:01:26.220><c> so</c><00:01:26.640><c> what</c>

00:01:26.749 --> 00:01:26.759 align:start position:0%
okay we don't want to do that so what
 

00:01:26.759 --> 00:01:28.670 align:start position:0%
okay we don't want to do that so what
can<00:01:26.939><c> we</c><00:01:27.060><c> do</c><00:01:27.240><c> here</c><00:01:27.420><c> well</c><00:01:27.900><c> one</c><00:01:28.140><c> way</c><00:01:28.259><c> is</c><00:01:28.500><c> to</c>

00:01:28.670 --> 00:01:28.680 align:start position:0%
can we do here well one way is to
 

00:01:28.680 --> 00:01:30.649 align:start position:0%
can we do here well one way is to
introduce<00:01:28.979><c> an</c><00:01:29.220><c> abstract</c><00:01:29.520><c> base</c><00:01:29.880><c> class</c><00:01:30.119><c> or</c><00:01:30.479><c> an</c>

00:01:30.649 --> 00:01:30.659 align:start position:0%
introduce an abstract base class or an
 

00:01:30.659 --> 00:01:32.030 align:start position:0%
introduce an abstract base class or an
interface<00:01:31.020><c> I'm</c><00:01:31.380><c> going</c><00:01:31.560><c> to</c><00:01:31.619><c> be</c><00:01:31.680><c> using</c><00:01:31.920><c> the</c>

00:01:32.030 --> 00:01:32.040 align:start position:0%
interface I'm going to be using the
 

00:01:32.040 --> 00:01:34.070 align:start position:0%
interface I'm going to be using the
interface<00:01:32.400><c> for</c><00:01:32.700><c> my</c><00:01:33.060><c> solution</c><00:01:33.360><c> we</c><00:01:33.780><c> want</c><00:01:33.900><c> the</c>

00:01:34.070 --> 00:01:34.080 align:start position:0%
interface for my solution we want the
 

00:01:34.080 --> 00:01:36.170 align:start position:0%
interface for my solution we want the
interface<00:01:34.380><c> to</c><00:01:34.619><c> Define</c><00:01:34.799><c> common</c><00:01:35.340><c> methods</c><00:01:35.880><c> and</c>

00:01:36.170 --> 00:01:36.180 align:start position:0%
interface to Define common methods and
 

00:01:36.180 --> 00:01:37.850 align:start position:0%
interface to Define common methods and
then<00:01:36.299><c> each</c><00:01:36.540><c> implementation</c><00:01:37.200><c> of</c><00:01:37.680><c> the</c>

00:01:37.850 --> 00:01:37.860 align:start position:0%
then each implementation of the
 

00:01:37.860 --> 00:01:39.950 align:start position:0%
then each implementation of the
interface<00:01:38.280><c> will</c><00:01:38.700><c> do</c><00:01:38.939><c> what</c><00:01:39.479><c> they</c><00:01:39.600><c> need</c><00:01:39.720><c> to</c><00:01:39.840><c> do</c>

00:01:39.950 --> 00:01:39.960 align:start position:0%
interface will do what they need to do
 

00:01:39.960 --> 00:01:42.170 align:start position:0%
interface will do what they need to do
which<00:01:40.200><c> is</c><00:01:40.380><c> perform</c><00:01:40.680><c> a</c><00:01:41.220><c> duty</c><00:01:41.520><c> now</c><00:01:41.880><c> let's</c><00:01:42.000><c> code</c>

00:01:42.170 --> 00:01:42.180 align:start position:0%
which is perform a duty now let's code
 

00:01:42.180 --> 00:01:43.789 align:start position:0%
which is perform a duty now let's code
our<00:01:42.360><c> solution</c><00:01:42.659><c> so</c><00:01:43.020><c> for</c><00:01:43.200><c> our</c><00:01:43.320><c> solution</c><00:01:43.560><c> again</c>

00:01:43.789 --> 00:01:43.799 align:start position:0%
our solution so for our solution again
 

00:01:43.799 --> 00:01:45.170 align:start position:0%
our solution so for our solution again
I'm<00:01:44.040><c> going</c><00:01:44.159><c> to</c><00:01:44.159><c> be</c><00:01:44.280><c> using</c><00:01:44.460><c> an</c><00:01:44.520><c> interface</c><00:01:44.939><c> now</c>

00:01:45.170 --> 00:01:45.180 align:start position:0%
I'm going to be using an interface now
 

00:01:45.180 --> 00:01:46.670 align:start position:0%
I'm going to be using an interface now
the<00:01:45.360><c> interface</c><00:01:45.659><c> is</c><00:01:45.780><c> going</c><00:01:45.960><c> to</c><00:01:46.020><c> have</c><00:01:46.200><c> two</c>

00:01:46.670 --> 00:01:46.680 align:start position:0%
the interface is going to have two
 

00:01:46.680 --> 00:01:48.890 align:start position:0%
the interface is going to have two
methods<00:01:47.159><c> it's</c><00:01:47.640><c> going</c><00:01:47.820><c> to</c><00:01:47.939><c> get</c><00:01:48.119><c> the</c><00:01:48.299><c> name</c><00:01:48.479><c> and</c>

00:01:48.890 --> 00:01:48.900 align:start position:0%
methods it's going to get the name and
 

00:01:48.900 --> 00:01:50.210 align:start position:0%
methods it's going to get the name and
we're<00:01:49.020><c> gonna</c><00:01:49.200><c> have</c><00:01:49.259><c> the</c><00:01:49.380><c> method</c><00:01:49.619><c> for</c><00:01:49.860><c> perform</c>

00:01:50.210 --> 00:01:50.220 align:start position:0%
we're gonna have the method for perform
 

00:01:50.220 --> 00:01:52.789 align:start position:0%
we're gonna have the method for perform
duties<00:01:51.000><c> we</c><00:01:51.299><c> don't</c><00:01:51.479><c> need</c><00:01:51.720><c> to</c><00:01:52.020><c> get</c><00:01:52.200><c> the</c><00:01:52.380><c> role</c>

00:01:52.789 --> 00:01:52.799 align:start position:0%
duties we don't need to get the role
 

00:01:52.799 --> 00:01:54.469 align:start position:0%
duties we don't need to get the role
since<00:01:53.220><c> each</c><00:01:53.520><c> role</c><00:01:53.820><c> is</c><00:01:53.939><c> going</c><00:01:54.060><c> to</c><00:01:54.060><c> be</c><00:01:54.180><c> defined</c>

00:01:54.469 --> 00:01:54.479 align:start position:0%
since each role is going to be defined
 

00:01:54.479 --> 00:01:56.690 align:start position:0%
since each role is going to be defined
by<00:01:54.720><c> the</c><00:01:54.899><c> implementation</c><00:01:55.439><c> from</c><00:01:55.860><c> the</c><00:01:56.159><c> employee</c>

00:01:56.690 --> 00:01:56.700 align:start position:0%
by the implementation from the employee
 

00:01:56.700 --> 00:01:58.010 align:start position:0%
by the implementation from the employee
class<00:01:56.880><c> let's</c><00:01:57.119><c> just</c><00:01:57.299><c> code</c><00:01:57.479><c> this</c><00:01:57.659><c> and</c><00:01:57.780><c> see</c><00:01:57.899><c> what</c>

00:01:58.010 --> 00:01:58.020 align:start position:0%
class let's just code this and see what
 

00:01:58.020 --> 00:01:59.450 align:start position:0%
class let's just code this and see what
I<00:01:58.079><c> mean</c><00:01:58.259><c> first</c><00:01:58.680><c> thing</c><00:01:58.860><c> I'm</c><00:01:58.979><c> going</c><00:01:59.040><c> to</c><00:01:59.100><c> do</c><00:01:59.220><c> is</c>

00:01:59.450 --> 00:01:59.460 align:start position:0%
I mean first thing I'm going to do is
 

00:01:59.460 --> 00:02:03.289 align:start position:0%
I mean first thing I'm going to do is
create<00:01:59.939><c> the</c><00:02:00.360><c> interface</c><00:02:00.720><c> class</c><00:02:00.899><c> so</c><00:02:01.439><c> I</c><00:02:01.799><c> employee</c>

00:02:03.289 --> 00:02:03.299 align:start position:0%
create the interface class so I employee
 

00:02:03.299 --> 00:02:05.330 align:start position:0%
create the interface class so I employee
an<00:02:03.780><c> interface</c><00:02:04.200><c> and</c><00:02:04.740><c> then</c><00:02:04.799><c> we</c><00:02:04.920><c> need</c><00:02:05.040><c> to</c><00:02:05.159><c> give</c><00:02:05.219><c> it</c>

00:02:05.330 --> 00:02:05.340 align:start position:0%
an interface and then we need to give it
 

00:02:05.340 --> 00:02:07.490 align:start position:0%
an interface and then we need to give it
the<00:02:05.460><c> two</c><00:02:05.579><c> methods</c><00:02:06.000><c> that</c><00:02:06.180><c> we</c><00:02:06.360><c> want</c><00:02:06.540><c> so</c><00:02:06.960><c> we're</c>

00:02:07.490 --> 00:02:07.500 align:start position:0%
the two methods that we want so we're
 

00:02:07.500 --> 00:02:10.550 align:start position:0%
the two methods that we want so we're
going<00:02:07.680><c> to</c><00:02:07.740><c> say</c><00:02:07.979><c> string</c><00:02:08.700><c> get</c><00:02:09.360><c> name</c>

00:02:10.550 --> 00:02:10.560 align:start position:0%
going to say string get name
 

00:02:10.560 --> 00:02:15.350 align:start position:0%
going to say string get name
and<00:02:11.400><c> then</c><00:02:11.640><c> void</c><00:02:12.420><c> perform</c><00:02:13.099><c> duties</c><00:02:14.099><c> okay</c><00:02:14.819><c> so</c><00:02:15.180><c> now</c>

00:02:15.350 --> 00:02:15.360 align:start position:0%
and then void perform duties okay so now
 

00:02:15.360 --> 00:02:18.350 align:start position:0%
and then void perform duties okay so now
each<00:02:15.720><c> implementation</c><00:02:16.440><c> that</c><00:02:17.400><c> we</c><00:02:17.580><c> have</c><00:02:17.760><c> for</c><00:02:18.120><c> I</c>

00:02:18.350 --> 00:02:18.360 align:start position:0%
each implementation that we have for I
 

00:02:18.360 --> 00:02:20.510 align:start position:0%
each implementation that we have for I
employee<00:02:18.900><c> such</c><00:02:19.200><c> as</c><00:02:19.379><c> developer</c><00:02:19.980><c> or</c><00:02:20.099><c> manager</c>

00:02:20.510 --> 00:02:20.520 align:start position:0%
employee such as developer or manager
 

00:02:20.520 --> 00:02:22.970 align:start position:0%
employee such as developer or manager
are<00:02:20.819><c> going</c><00:02:21.060><c> to</c><00:02:21.180><c> have</c><00:02:21.420><c> to</c><00:02:22.020><c> fill</c><00:02:22.440><c> in</c><00:02:22.680><c> these</c>

00:02:22.970 --> 00:02:22.980 align:start position:0%
are going to have to fill in these
 

00:02:22.980 --> 00:02:25.490 align:start position:0%
are going to have to fill in these
methods<00:02:23.400><c> now</c><00:02:23.760><c> if</c><00:02:23.879><c> we</c><00:02:24.060><c> look</c><00:02:24.180><c> back</c><00:02:24.420><c> at</c><00:02:24.660><c> before</c><00:02:25.020><c> we</c>

00:02:25.490 --> 00:02:25.500 align:start position:0%
methods now if we look back at before we
 

00:02:25.500 --> 00:02:26.990 align:start position:0%
methods now if we look back at before we
had<00:02:25.800><c> three</c><00:02:26.160><c> different</c><00:02:26.340><c> roles</c><00:02:26.640><c> we</c><00:02:26.879><c> had</c>

00:02:26.990 --> 00:02:27.000 align:start position:0%
had three different roles we had
 

00:02:27.000 --> 00:02:29.089 align:start position:0%
had three different roles we had
developer<00:02:27.360><c> manager</c><00:02:27.840><c> and</c><00:02:27.959><c> tester</c><00:02:28.319><c> so</c><00:02:28.800><c> let's</c><00:02:28.920><c> go</c>

00:02:29.089 --> 00:02:29.099 align:start position:0%
developer manager and tester so let's go
 

00:02:29.099 --> 00:02:30.949 align:start position:0%
developer manager and tester so let's go
ahead<00:02:29.160><c> and</c><00:02:29.280><c> create</c><00:02:29.459><c> the</c><00:02:29.700><c> developer</c><00:02:30.120><c> one</c>

00:02:30.949 --> 00:02:30.959 align:start position:0%
ahead and create the developer one
 

00:02:30.959 --> 00:02:35.869 align:start position:0%
ahead and create the developer one
so<00:02:31.440><c> new</c><00:02:31.739><c> class</c><00:02:32.099><c> call</c><00:02:32.580><c> this</c><00:02:32.760><c> developer</c>

00:02:35.869 --> 00:02:35.879 align:start position:0%
 
 

00:02:35.879 --> 00:02:39.110 align:start position:0%
 
and<00:02:36.300><c> then</c><00:02:36.360><c> we</c><00:02:36.540><c> have</c><00:02:36.720><c> to</c><00:02:36.959><c> implement</c><00:02:37.879><c> the</c><00:02:38.879><c> I</c>

00:02:39.110 --> 00:02:39.120 align:start position:0%
and then we have to implement the I
 

00:02:39.120 --> 00:02:41.809 align:start position:0%
and then we have to implement the I
employee<00:02:39.800><c> and</c><00:02:40.800><c> now</c><00:02:40.980><c> this</c><00:02:41.220><c> is</c><00:02:41.340><c> going</c><00:02:41.459><c> to</c><00:02:41.519><c> force</c>

00:02:41.809 --> 00:02:41.819 align:start position:0%
employee and now this is going to force
 

00:02:41.819 --> 00:02:43.729 align:start position:0%
employee and now this is going to force
us<00:02:41.940><c> to</c><00:02:42.480><c> implement</c><00:02:42.840><c> the</c><00:02:43.140><c> methods</c><00:02:43.500><c> from</c><00:02:43.620><c> the</c>

00:02:43.729 --> 00:02:43.739 align:start position:0%
us to implement the methods from the
 

00:02:43.739 --> 00:02:45.890 align:start position:0%
us to implement the methods from the
interface

00:02:45.890 --> 00:02:45.900 align:start position:0%
interface
 

00:02:45.900 --> 00:02:49.850 align:start position:0%
interface
so<00:02:46.440><c> the</c><00:02:46.739><c> first</c><00:02:46.920><c> thing</c><00:02:47.160><c> is</c><00:02:47.879><c> we</c><00:02:48.180><c> need</c><00:02:48.420><c> a</c><00:02:48.959><c> private</c>

00:02:49.850 --> 00:02:49.860 align:start position:0%
so the first thing is we need a private
 

00:02:49.860 --> 00:02:51.309 align:start position:0%
so the first thing is we need a private
string

00:02:51.309 --> 00:02:51.319 align:start position:0%
string
 

00:02:51.319 --> 00:02:54.350 align:start position:0%
string
string<00:02:52.319><c> name</c>

00:02:54.350 --> 00:02:54.360 align:start position:0%
string name
 

00:02:54.360 --> 00:02:56.509 align:start position:0%
string name
and<00:02:54.780><c> then</c><00:02:54.900><c> we</c><00:02:55.080><c> need</c><00:02:55.140><c> our</c><00:02:55.379><c> Constructor</c>

00:02:56.509 --> 00:02:56.519 align:start position:0%
and then we need our Constructor
 

00:02:56.519 --> 00:03:00.050 align:start position:0%
and then we need our Constructor
which<00:02:56.940><c> is</c><00:02:57.060><c> public</c><00:02:57.500><c> developer</c><00:02:58.500><c> string</c><00:02:59.400><c> name</c>

00:03:00.050 --> 00:03:00.060 align:start position:0%
which is public developer string name
 

00:03:00.060 --> 00:03:03.229 align:start position:0%
which is public developer string name
and<00:03:00.900><c> then</c><00:03:01.019><c> this</c><00:03:01.379><c> dot</c><00:03:01.800><c> name</c><00:03:01.980><c> equals</c><00:03:02.519><c> name</c>

00:03:03.229 --> 00:03:03.239 align:start position:0%
and then this dot name equals name
 

00:03:03.239 --> 00:03:04.850 align:start position:0%
and then this dot name equals name
so<00:03:03.480><c> we're</c><00:03:03.599><c> going</c><00:03:03.780><c> to</c><00:03:03.780><c> replace</c><00:03:03.959><c> the</c><00:03:04.319><c> name</c><00:03:04.500><c> here</c>

00:03:04.850 --> 00:03:04.860 align:start position:0%
so we're going to replace the name here
 

00:03:04.860 --> 00:03:07.850 align:start position:0%
so we're going to replace the name here
and<00:03:05.640><c> then</c><00:03:05.700><c> for</c><00:03:05.940><c> perform</c><00:03:06.180><c> duties</c><00:03:06.780><c> back</c><00:03:07.560><c> in</c><00:03:07.680><c> the</c>

00:03:07.850 --> 00:03:07.860 align:start position:0%
and then for perform duties back in the
 

00:03:07.860 --> 00:03:10.369 align:start position:0%
and then for perform duties back in the
employee<00:03:08.220><c> OC</c><00:03:08.640><c> class</c><00:03:09.000><c> we</c><00:03:09.720><c> said</c><00:03:09.900><c> that</c><00:03:10.080><c> we're</c>

00:03:10.369 --> 00:03:10.379 align:start position:0%
employee OC class we said that we're
 

00:03:10.379 --> 00:03:12.649 align:start position:0%
employee OC class we said that we're
going<00:03:10.560><c> to</c><00:03:10.739><c> print</c><00:03:10.920><c> out</c><00:03:11.220><c> that</c><00:03:11.760><c> this</c><00:03:12.120><c> person</c><00:03:12.300><c> is</c>

00:03:12.649 --> 00:03:12.659 align:start position:0%
going to print out that this person is
 

00:03:12.659 --> 00:03:15.229 align:start position:0%
going to print out that this person is
coding<00:03:13.200><c> so</c><00:03:13.620><c> back</c><00:03:13.920><c> to</c><00:03:14.040><c> the</c><00:03:14.159><c> developer</c>

00:03:15.229 --> 00:03:15.239 align:start position:0%
coding so back to the developer
 

00:03:15.239 --> 00:03:17.570 align:start position:0%
coding so back to the developer
we're<00:03:15.659><c> going</c><00:03:15.780><c> to</c><00:03:15.900><c> say</c><00:03:16.280><c> system.out.print</c><00:03:17.280><c> and</c>

00:03:17.570 --> 00:03:17.580 align:start position:0%
we're going to say system.out.print and
 

00:03:17.580 --> 00:03:19.910 align:start position:0%
we're going to say system.out.print and
whoever<00:03:18.120><c> whoever's</c><00:03:18.840><c> name</c><00:03:19.140><c> this</c><00:03:19.379><c> is</c><00:03:19.560><c> they're</c>

00:03:19.910 --> 00:03:19.920 align:start position:0%
whoever whoever's name this is they're
 

00:03:19.920 --> 00:03:22.009 align:start position:0%
whoever whoever's name this is they're
going<00:03:20.040><c> to</c><00:03:20.159><c> be</c><00:03:20.220><c> coding</c><00:03:20.580><c> okay</c><00:03:21.180><c> and</c><00:03:21.659><c> that's</c><00:03:21.780><c> it</c>

00:03:22.009 --> 00:03:22.019 align:start position:0%
going to be coding okay and that's it
 

00:03:22.019 --> 00:03:24.710 align:start position:0%
going to be coding okay and that's it
that's<00:03:22.440><c> one</c><00:03:23.340><c> of</c><00:03:23.519><c> the</c><00:03:23.760><c> implementations</c><00:03:24.239><c> for</c>

00:03:24.710 --> 00:03:24.720 align:start position:0%
that's one of the implementations for
 

00:03:24.720 --> 00:03:28.369 align:start position:0%
that's one of the implementations for
the<00:03:25.080><c> employee</c><00:03:25.500><c> class</c><00:03:25.739><c> okay</c><00:03:26.220><c> so</c><00:03:26.640><c> now</c><00:03:26.819><c> we</c><00:03:27.180><c> have</c><00:03:27.480><c> a</c>

00:03:28.369 --> 00:03:28.379 align:start position:0%
the employee class okay so now we have a
 

00:03:28.379 --> 00:03:29.690 align:start position:0%
the employee class okay so now we have a
manager<00:03:28.800><c> class</c>

00:03:29.690 --> 00:03:29.700 align:start position:0%
manager class
 

00:03:29.700 --> 00:03:31.910 align:start position:0%
manager class
and<00:03:30.239><c> a</c><00:03:30.420><c> tester</c><00:03:30.780><c> class</c><00:03:30.900><c> and</c><00:03:31.319><c> these</c><00:03:31.560><c> are</c><00:03:31.680><c> all</c>

00:03:31.910 --> 00:03:31.920 align:start position:0%
and a tester class and these are all
 

00:03:31.920 --> 00:03:34.610 align:start position:0%
and a tester class and these are all
implementations<00:03:32.640><c> of</c><00:03:33.480><c> the</c><00:03:33.959><c> I</c><00:03:34.080><c> employee</c>

00:03:34.610 --> 00:03:34.620 align:start position:0%
implementations of the I employee
 

00:03:34.620 --> 00:03:36.649 align:start position:0%
implementations of the I employee
interface<00:03:34.980><c> that</c><00:03:35.220><c> we</c><00:03:35.340><c> created</c><00:03:35.640><c> okay</c><00:03:36.360><c> so</c><00:03:36.540><c> now</c>

00:03:36.649 --> 00:03:36.659 align:start position:0%
interface that we created okay so now
 

00:03:36.659 --> 00:03:39.050 align:start position:0%
interface that we created okay so now
the<00:03:36.900><c> principle</c><00:03:37.200><c> again</c><00:03:37.560><c> open</c><00:03:38.220><c> for</c><00:03:38.519><c> extension</c>

00:03:39.050 --> 00:03:39.060 align:start position:0%
the principle again open for extension
 

00:03:39.060 --> 00:03:41.570 align:start position:0%
the principle again open for extension
close<00:03:39.780><c> for</c><00:03:40.019><c> modification</c><00:03:40.560><c> now</c><00:03:41.040><c> before</c><00:03:41.159><c> in</c><00:03:41.459><c> the</c>

00:03:41.570 --> 00:03:41.580 align:start position:0%
close for modification now before in the
 

00:03:41.580 --> 00:03:43.729 align:start position:0%
close for modification now before in the
employee<00:03:41.940><c> OC</c><00:03:42.299><c> class</c><00:03:42.599><c> let's</c><00:03:43.200><c> say</c><00:03:43.379><c> we</c><00:03:43.560><c> want</c><00:03:43.680><c> to</c>

00:03:43.729 --> 00:03:43.739 align:start position:0%
employee OC class let's say we want to
 

00:03:43.739 --> 00:03:45.710 align:start position:0%
employee OC class let's say we want to
add<00:03:43.860><c> another</c><00:03:43.980><c> role</c><00:03:44.400><c> called</c><00:03:44.700><c> scrum</c><00:03:45.180><c> master</c><00:03:45.360><c> so</c>

00:03:45.710 --> 00:03:45.720 align:start position:0%
add another role called scrum master so
 

00:03:45.720 --> 00:03:49.970 align:start position:0%
add another role called scrum master so
we<00:03:45.900><c> would</c><00:03:46.080><c> say</c><00:03:46.200><c> else</c><00:03:46.620><c> if</c><00:03:47.159><c> roll</c><00:03:47.760><c> dot</c><00:03:48.299><c> equals</c>

00:03:49.970 --> 00:03:49.980 align:start position:0%
we would say else if roll dot equals
 

00:03:49.980 --> 00:03:51.830 align:start position:0%
we would say else if roll dot equals
scrum<00:03:50.640><c> master</c>

00:03:51.830 --> 00:03:51.840 align:start position:0%
scrum master
 

00:03:51.840 --> 00:03:54.470 align:start position:0%
scrum master
whoop<00:03:52.860><c> and</c><00:03:53.459><c> then</c>

00:03:54.470 --> 00:03:54.480 align:start position:0%
whoop and then
 

00:03:54.480 --> 00:03:57.910 align:start position:0%
whoop and then
we<00:03:54.900><c> could</c><00:03:55.019><c> just</c><00:03:55.260><c> say</c><00:03:55.560><c> that</c><00:03:55.980><c> they</c><00:03:56.580><c> are</c>

00:03:57.910 --> 00:03:57.920 align:start position:0%
we could just say that they are
 

00:03:57.920 --> 00:04:01.690 align:start position:0%
we could just say that they are
scrumming<00:03:58.920><c> so</c><00:03:59.159><c> it's</c><00:03:59.459><c> a</c><00:03:59.819><c> name</c><00:04:00.120><c> plus</c><00:04:00.720><c> is</c>

00:04:01.690 --> 00:04:01.700 align:start position:0%
scrumming so it's a name plus is
 

00:04:01.700 --> 00:04:04.729 align:start position:0%
scrumming so it's a name plus is
grumming<00:04:02.700><c> okay</c>

00:04:04.729 --> 00:04:04.739 align:start position:0%
grumming okay
 

00:04:04.739 --> 00:04:06.589 align:start position:0%
grumming okay
now<00:04:05.280><c> every</c><00:04:05.640><c> time</c><00:04:05.879><c> we</c><00:04:06.120><c> want</c><00:04:06.239><c> to</c><00:04:06.360><c> add</c><00:04:06.420><c> another</c>

00:04:06.589 --> 00:04:06.599 align:start position:0%
now every time we want to add another
 

00:04:06.599 --> 00:04:08.570 align:start position:0%
now every time we want to add another
role<00:04:07.080><c> we're</c><00:04:07.319><c> going</c><00:04:07.440><c> to</c><00:04:07.560><c> have</c><00:04:07.620><c> to</c><00:04:07.739><c> go</c><00:04:07.980><c> into</c><00:04:08.280><c> this</c>

00:04:08.570 --> 00:04:08.580 align:start position:0%
role we're going to have to go into this
 

00:04:08.580 --> 00:04:12.110 align:start position:0%
role we're going to have to go into this
method<00:04:09.000><c> in</c><00:04:09.480><c> this</c><00:04:09.659><c> class</c><00:04:09.959><c> and</c><00:04:10.439><c> modify</c><00:04:10.860><c> it</c><00:04:11.120><c> that</c>

00:04:12.110 --> 00:04:12.120 align:start position:0%
method in this class and modify it that
 

00:04:12.120 --> 00:04:14.869 align:start position:0%
method in this class and modify it that
violates<00:04:12.720><c> the</c><00:04:13.080><c> principle</c><00:04:13.580><c> instead</c><00:04:14.580><c> what</c>

00:04:14.869 --> 00:04:14.879 align:start position:0%
violates the principle instead what
 

00:04:14.879 --> 00:04:16.849 align:start position:0%
violates the principle instead what
we're<00:04:15.000><c> going</c><00:04:15.120><c> to</c><00:04:15.180><c> do</c><00:04:15.360><c> is</c><00:04:16.320><c> we're</c><00:04:16.500><c> going</c><00:04:16.620><c> to</c><00:04:16.680><c> come</c>

00:04:16.849 --> 00:04:16.859 align:start position:0%
we're going to do is we're going to come
 

00:04:16.859 --> 00:04:19.969 align:start position:0%
we're going to do is we're going to come
up<00:04:16.979><c> here</c><00:04:17.699><c> in</c><00:04:18.060><c> the</c><00:04:18.180><c> after</c><00:04:18.299><c> package</c><00:04:18.840><c> and</c><00:04:19.620><c> we</c><00:04:19.859><c> are</c>

00:04:19.969 --> 00:04:19.979 align:start position:0%
up here in the after package and we are
 

00:04:19.979 --> 00:04:23.030 align:start position:0%
up here in the after package and we are
going<00:04:20.160><c> to</c><00:04:20.340><c> just</c><00:04:20.579><c> create</c><00:04:20.940><c> another</c><00:04:22.040><c> employee</c>

00:04:23.030 --> 00:04:23.040 align:start position:0%
going to just create another employee
 

00:04:23.040 --> 00:04:24.469 align:start position:0%
going to just create another employee
implementation<00:04:23.699><c> so</c><00:04:24.000><c> we're</c><00:04:24.180><c> going</c><00:04:24.360><c> to</c><00:04:24.360><c> say</c>

00:04:24.469 --> 00:04:24.479 align:start position:0%
implementation so we're going to say
 

00:04:24.479 --> 00:04:27.189 align:start position:0%
implementation so we're going to say
scrum<00:04:25.020><c> master</c>

00:04:27.189 --> 00:04:27.199 align:start position:0%
scrum master
 

00:04:27.199 --> 00:04:28.850 align:start position:0%
scrum master
implements

00:04:28.850 --> 00:04:28.860 align:start position:0%
implements
 

00:04:28.860 --> 00:04:30.409 align:start position:0%
implements
implements

00:04:30.409 --> 00:04:30.419 align:start position:0%
implements
 

00:04:30.419 --> 00:04:32.270 align:start position:0%
implements
I<00:04:31.139><c> employee</c>

00:04:32.270 --> 00:04:32.280 align:start position:0%
I employee
 

00:04:32.280 --> 00:04:34.310 align:start position:0%
I employee
and<00:04:32.820><c> again</c><00:04:33.000><c> we</c><00:04:33.360><c> have</c><00:04:33.540><c> to</c><00:04:33.840><c> implement</c><00:04:34.139><c> the</c>

00:04:34.310 --> 00:04:34.320 align:start position:0%
and again we have to implement the
 

00:04:34.320 --> 00:04:37.610 align:start position:0%
and again we have to implement the
methods<00:04:34.740><c> and</c><00:04:35.160><c> now</c><00:04:35.400><c> each</c><00:04:36.180><c> time</c><00:04:36.540><c> we</c><00:04:37.259><c> want</c><00:04:37.440><c> to</c><00:04:37.500><c> add</c>

00:04:37.610 --> 00:04:37.620 align:start position:0%
methods and now each time we want to add
 

00:04:37.620 --> 00:04:39.890 align:start position:0%
methods and now each time we want to add
another<00:04:37.740><c> one</c><00:04:38.040><c> again</c><00:04:38.639><c> I</c><00:04:39.000><c> know</c><00:04:39.419><c> I'm</c><00:04:39.540><c> repeating</c>

00:04:39.890 --> 00:04:39.900 align:start position:0%
another one again I know I'm repeating
 

00:04:39.900 --> 00:04:41.350 align:start position:0%
another one again I know I'm repeating
myself<00:04:40.080><c> but</c><00:04:40.500><c> I</c><00:04:40.680><c> just</c><00:04:40.800><c> need</c><00:04:40.919><c> you</c><00:04:41.100><c> to</c><00:04:41.220><c> understand</c>

00:04:41.350 --> 00:04:41.360 align:start position:0%
myself but I just need you to understand
 

00:04:41.360 --> 00:04:44.090 align:start position:0%
myself but I just need you to understand
that<00:04:42.360><c> all</c><00:04:42.540><c> we</c><00:04:42.660><c> have</c><00:04:42.780><c> to</c><00:04:42.900><c> do</c><00:04:43.080><c> is</c><00:04:43.500><c> come</c><00:04:43.740><c> in</c><00:04:43.919><c> here</c>

00:04:44.090 --> 00:04:44.100 align:start position:0%
that all we have to do is come in here
 

00:04:44.100 --> 00:04:46.010 align:start position:0%
that all we have to do is come in here
create<00:04:44.580><c> another</c><00:04:44.820><c> implementation</c><00:04:45.540><c> of</c><00:04:45.900><c> the</c>

00:04:46.010 --> 00:04:46.020 align:start position:0%
create another implementation of the
 

00:04:46.020 --> 00:04:48.530 align:start position:0%
create another implementation of the
employee<00:04:46.440><c> and</c><00:04:46.979><c> we're</c><00:04:47.160><c> not</c><00:04:47.400><c> modifying</c><00:04:48.180><c> the</c>

00:04:48.530 --> 00:04:48.540 align:start position:0%
employee and we're not modifying the
 

00:04:48.540 --> 00:04:51.950 align:start position:0%
employee and we're not modifying the
employee<00:04:49.080><c> itself</c><00:04:49.620><c> okay</c><00:04:50.280><c> we're</c><00:04:50.880><c> extending</c><00:04:51.540><c> its</c>

00:04:51.950 --> 00:04:51.960 align:start position:0%
employee itself okay we're extending its
 

00:04:51.960 --> 00:04:53.810 align:start position:0%
employee itself okay we're extending its
functionality<00:04:52.440><c> to</c><00:04:52.979><c> different</c><00:04:53.280><c> types</c><00:04:53.699><c> of</c>

00:04:53.810 --> 00:04:53.820 align:start position:0%
functionality to different types of
 

00:04:53.820 --> 00:04:55.550 align:start position:0%
functionality to different types of
employees<00:04:54.300><c> okay</c><00:04:54.660><c> so</c><00:04:54.900><c> we</c><00:04:55.020><c> just</c><00:04:55.139><c> got</c><00:04:55.259><c> the</c><00:04:55.320><c> basics</c>

00:04:55.550 --> 00:04:55.560 align:start position:0%
employees okay so we just got the basics
 

00:04:55.560 --> 00:04:57.650 align:start position:0%
employees okay so we just got the basics
down<00:04:55.740><c> of</c><00:04:56.040><c> the</c><00:04:56.160><c> open</c><00:04:56.280><c> close</c><00:04:56.580><c> principle</c><00:04:57.060><c> it's</c>

00:04:57.650 --> 00:04:57.660 align:start position:0%
down of the open close principle it's
 

00:04:57.660 --> 00:04:59.210 align:start position:0%
down of the open close principle it's
one<00:04:57.900><c> of</c><00:04:58.020><c> the</c><00:04:58.080><c> easier</c><00:04:58.500><c> of</c><00:04:58.620><c> the</c><00:04:58.800><c> five</c><00:04:58.919><c> because</c>

00:04:59.210 --> 00:04:59.220 align:start position:0%
one of the easier of the five because
 

00:04:59.220 --> 00:05:00.590 align:start position:0%
one of the easier of the five because
there's<00:04:59.400><c> not</c><00:04:59.580><c> a</c><00:04:59.759><c> lot</c><00:04:59.880><c> to</c><00:05:00.060><c> it</c><00:05:00.180><c> it's</c><00:05:00.360><c> pretty</c>

00:05:00.590 --> 00:05:00.600 align:start position:0%
there's not a lot to it it's pretty
 

00:05:00.600 --> 00:05:02.150 align:start position:0%
there's not a lot to it it's pretty
straightforward<00:05:01.199><c> and</c><00:05:01.740><c> just</c><00:05:01.919><c> to</c><00:05:02.040><c> repeat</c>

00:05:02.150 --> 00:05:02.160 align:start position:0%
straightforward and just to repeat
 

00:05:02.160 --> 00:05:04.249 align:start position:0%
straightforward and just to repeat
myself<00:05:02.400><c> one</c><00:05:02.699><c> more</c><00:05:02.880><c> time</c><00:05:03.000><c> we</c><00:05:03.660><c> want</c><00:05:03.840><c> to</c><00:05:03.960><c> allow</c>

00:05:04.249 --> 00:05:04.259 align:start position:0%
myself one more time we want to allow
 

00:05:04.259 --> 00:05:07.790 align:start position:0%
myself one more time we want to allow
our<00:05:04.440><c> code</c><00:05:04.680><c> to</c><00:05:05.580><c> be</c><00:05:05.820><c> able</c><00:05:06.300><c> to</c><00:05:06.419><c> be</c><00:05:06.660><c> extended</c><00:05:07.139><c> but</c>

00:05:07.790 --> 00:05:07.800 align:start position:0%
our code to be able to be extended but
 

00:05:07.800 --> 00:05:10.129 align:start position:0%
our code to be able to be extended but
closed<00:05:08.280><c> for</c><00:05:08.400><c> modification</c><00:05:09.000><c> with</c><00:05:09.780><c> that</c><00:05:09.900><c> I</c><00:05:10.080><c> have</c>

00:05:10.129 --> 00:05:10.139 align:start position:0%
closed for modification with that I have
 

00:05:10.139 --> 00:05:11.629 align:start position:0%
closed for modification with that I have
a<00:05:10.259><c> link</c><00:05:10.380><c> to</c><00:05:10.500><c> the</c><00:05:10.560><c> GitHub</c><00:05:10.800><c> description</c><00:05:11.220><c> and</c>

00:05:11.629 --> 00:05:11.639 align:start position:0%
a link to the GitHub description and
 

00:05:11.639 --> 00:05:13.490 align:start position:0%
a link to the GitHub description and
here<00:05:11.940><c> is</c><00:05:12.120><c> the</c><00:05:12.300><c> first</c><00:05:12.419><c> principle</c><00:05:12.780><c> video</c><00:05:13.020><c> thanks</c>

00:05:13.490 --> 00:05:13.500 align:start position:0%
here is the first principle video thanks
 

00:05:13.500 --> 00:05:16.940 align:start position:0%
here is the first principle video thanks
for<00:05:13.680><c> watching</c><00:05:13.979><c> I'll</c><00:05:14.280><c> see</c><00:05:14.460><c> you</c><00:05:14.580><c> next</c><00:05:14.759><c> time</c>

