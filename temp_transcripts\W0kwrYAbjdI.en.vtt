WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:01.949 align:start position:0%
 
hey<00:00:00.359><c> everybody</c><00:00:00.799><c> welcome</c><00:00:01.160><c> back</c><00:00:01.319><c> and</c><00:00:01.480><c> today</c><00:00:01.800><c> we</c>

00:00:01.949 --> 00:00:01.959 align:start position:0%
hey everybody welcome back and today we
 

00:00:01.959 --> 00:00:04.070 align:start position:0%
hey everybody welcome back and today we
have<00:00:02.240><c> another</c><00:00:02.560><c> autogen</c><00:00:03.159><c> update</c><00:00:03.520><c> we</c><00:00:03.639><c> are</c><00:00:03.879><c> now</c>

00:00:04.070 --> 00:00:04.080 align:start position:0%
have another autogen update we are now
 

00:00:04.080 --> 00:00:05.309 align:start position:0%
have another autogen update we are now
at<00:00:04.279><c> version</c>

00:00:05.309 --> 00:00:05.319 align:start position:0%
at version
 

00:00:05.319 --> 00:00:07.550 align:start position:0%
at version
0.2.4<00:00:06.319><c> we'll</c><00:00:06.480><c> go</c><00:00:06.600><c> over</c><00:00:06.759><c> the</c><00:00:06.879><c> simple</c><00:00:07.160><c> updates</c>

00:00:07.550 --> 00:00:07.560 align:start position:0%
0.2.4 we'll go over the simple updates
 

00:00:07.560 --> 00:00:10.150 align:start position:0%
0.2.4 we'll go over the simple updates
first<00:00:08.120><c> and</c><00:00:08.240><c> then</c><00:00:08.440><c> context</c><00:00:08.880><c> handling</c><00:00:09.519><c> logging</c>

00:00:10.150 --> 00:00:10.160 align:start position:0%
first and then context handling logging
 

00:00:10.160 --> 00:00:12.549 align:start position:0%
first and then context handling logging
and<00:00:10.360><c> code</c><00:00:10.719><c> executors</c><00:00:11.679><c> let's</c><00:00:11.880><c> get</c><00:00:12.040><c> started</c>

00:00:12.549 --> 00:00:12.559 align:start position:0%
and code executors let's get started
 

00:00:12.559 --> 00:00:14.030 align:start position:0%
and code executors let's get started
okay<00:00:12.679><c> for</c><00:00:12.799><c> the</c><00:00:12.920><c> simple</c><00:00:13.200><c> updates</c><00:00:13.599><c> we</c><00:00:13.759><c> have</c>

00:00:14.030 --> 00:00:14.040 align:start position:0%
okay for the simple updates we have
 

00:00:14.040 --> 00:00:15.589 align:start position:0%
okay for the simple updates we have
Improvement<00:00:14.440><c> to</c><00:00:14.559><c> the</c><00:00:14.679><c> GPT</c><00:00:15.160><c> assistant</c>

00:00:15.589 --> 00:00:15.599 align:start position:0%
Improvement to the GPT assistant
 

00:00:15.599 --> 00:00:17.189 align:start position:0%
Improvement to the GPT assistant
updating<00:00:16.039><c> the</c><00:00:16.160><c> gallery</c><00:00:16.480><c> section</c><00:00:16.840><c> on</c><00:00:17.000><c> their</c>

00:00:17.189 --> 00:00:17.199 align:start position:0%
updating the gallery section on their
 

00:00:17.199 --> 00:00:18.870 align:start position:0%
updating the gallery section on their
website<00:00:17.520><c> to</c><00:00:17.640><c> make</c><00:00:17.760><c> it</c><00:00:17.840><c> easier</c><00:00:18.119><c> to</c><00:00:18.279><c> navigate</c><00:00:18.760><c> we</c>

00:00:18.870 --> 00:00:18.880 align:start position:0%
website to make it easier to navigate we
 

00:00:18.880 --> 00:00:21.150 align:start position:0%
website to make it easier to navigate we
now<00:00:19.000><c> have</c><00:00:19.160><c> mock</c><00:00:19.520><c> validation</c><00:00:20.160><c> of</c><00:00:20.320><c> the</c><00:00:20.439><c> open</c><00:00:20.760><c> AI</c>

00:00:21.150 --> 00:00:21.160 align:start position:0%
now have mock validation of the open AI
 

00:00:21.160 --> 00:00:23.710 align:start position:0%
now have mock validation of the open AI
API<00:00:21.600><c> key</c><00:00:21.960><c> and</c><00:00:22.119><c> the</c><00:00:22.240><c> llm</c><00:00:22.800><c> config</c><00:00:23.359><c> and</c><00:00:23.480><c> we</c><00:00:23.599><c> can</c>

00:00:23.710 --> 00:00:23.720 align:start position:0%
API key and the llm config and we can
 

00:00:23.720 --> 00:00:25.830 align:start position:0%
API key and the llm config and we can
individually<00:00:24.359><c> retrieve</c><00:00:24.840><c> agents</c><00:00:25.240><c> now</c><00:00:25.560><c> from</c>

00:00:25.830 --> 00:00:25.840 align:start position:0%
individually retrieve agents now from
 

00:00:25.840 --> 00:00:27.269 align:start position:0%
individually retrieve agents now from
group<00:00:26.119><c> chat</c><00:00:26.519><c> let's</c><00:00:26.679><c> first</c><00:00:26.880><c> go</c><00:00:26.960><c> over</c><00:00:27.119><c> to</c>

00:00:27.269 --> 00:00:27.279 align:start position:0%
group chat let's first go over to
 

00:00:27.279 --> 00:00:29.029 align:start position:0%
group chat let's first go over to
context<00:00:27.720><c> handling</c><00:00:28.240><c> and</c><00:00:28.400><c> what</c><00:00:28.480><c> do</c><00:00:28.599><c> I</c><00:00:28.679><c> mean</c><00:00:28.880><c> by</c>

00:00:29.029 --> 00:00:29.039 align:start position:0%
context handling and what do I mean by
 

00:00:29.039 --> 00:00:31.070 align:start position:0%
context handling and what do I mean by
this<00:00:29.279><c> well</c><00:00:29.640><c> an</c><00:00:30.000><c> agent's</c><00:00:30.320><c> chat</c><00:00:30.599><c> history</c><00:00:30.920><c> with</c>

00:00:31.070 --> 00:00:31.080 align:start position:0%
this well an agent's chat history with
 

00:00:31.080 --> 00:00:33.030 align:start position:0%
this well an agent's chat history with
other<00:00:31.320><c> agents</c><00:00:31.679><c> is</c><00:00:31.800><c> a</c><00:00:31.960><c> common</c><00:00:32.399><c> context</c><00:00:32.840><c> that</c><00:00:32.920><c> it</c>

00:00:33.030 --> 00:00:33.040 align:start position:0%
other agents is a common context that it
 

00:00:33.040 --> 00:00:35.150 align:start position:0%
other agents is a common context that it
uses<00:00:33.360><c> to</c><00:00:33.559><c> generate</c><00:00:34.000><c> a</c><00:00:34.200><c> reply</c><00:00:34.800><c> and</c><00:00:34.920><c> the</c><00:00:35.000><c> thing</c>

00:00:35.150 --> 00:00:35.160 align:start position:0%
uses to generate a reply and the thing
 

00:00:35.160 --> 00:00:37.709 align:start position:0%
uses to generate a reply and the thing
is<00:00:35.280><c> if</c><00:00:35.440><c> the</c><00:00:35.600><c> context</c><00:00:36.079><c> gets</c><00:00:36.360><c> too</c><00:00:36.680><c> big</c><00:00:37.399><c> then</c><00:00:37.600><c> we</c>

00:00:37.709 --> 00:00:37.719 align:start position:0%
is if the context gets too big then we
 

00:00:37.719 --> 00:00:39.670 align:start position:0%
is if the context gets too big then we
don't<00:00:38.000><c> want</c><00:00:38.200><c> the</c><00:00:38.320><c> agents</c><00:00:38.640><c> to</c><00:00:38.879><c> crash</c><00:00:39.520><c> let's</c>

00:00:39.670 --> 00:00:39.680 align:start position:0%
don't want the agents to crash let's
 

00:00:39.680 --> 00:00:40.869 align:start position:0%
don't want the agents to crash let's
just<00:00:39.800><c> go</c><00:00:39.920><c> through</c><00:00:40.039><c> an</c><00:00:40.200><c> example</c><00:00:40.480><c> and</c><00:00:40.600><c> I'll</c><00:00:40.719><c> show</c>

00:00:40.869 --> 00:00:40.879 align:start position:0%
just go through an example and I'll show
 

00:00:40.879 --> 00:00:43.430 align:start position:0%
just go through an example and I'll show
you<00:00:41.160><c> how</c><00:00:41.360><c> it</c><00:00:41.559><c> works</c><00:00:42.320><c> we</c><00:00:42.480><c> first</c><00:00:42.680><c> want</c><00:00:42.840><c> to</c><00:00:43.039><c> import</c>

00:00:43.430 --> 00:00:43.440 align:start position:0%
you how it works we first want to import
 

00:00:43.440 --> 00:00:45.229 align:start position:0%
you how it works we first want to import
context<00:00:43.879><c> handling</c><00:00:44.320><c> here</c><00:00:44.719><c> then</c><00:00:44.879><c> we</c><00:00:45.039><c> just</c>

00:00:45.229 --> 00:00:45.239 align:start position:0%
context handling here then we just
 

00:00:45.239 --> 00:00:47.510 align:start position:0%
context handling here then we just
create<00:00:45.520><c> an</c><00:00:45.680><c> assistant</c><00:00:46.079><c> agent</c><00:00:46.360><c> like</c><00:00:46.559><c> usual</c><00:00:47.320><c> but</c>

00:00:47.510 --> 00:00:47.520 align:start position:0%
create an assistant agent like usual but
 

00:00:47.520 --> 00:00:49.590 align:start position:0%
create an assistant agent like usual but
now<00:00:48.039><c> we</c><00:00:48.160><c> have</c><00:00:48.320><c> this</c><00:00:48.480><c> manage</c><00:00:48.879><c> chat</c><00:00:49.239><c> history</c>

00:00:49.590 --> 00:00:49.600 align:start position:0%
now we have this manage chat history
 

00:00:49.600 --> 00:00:51.790 align:start position:0%
now we have this manage chat history
variable<00:00:50.000><c> where</c><00:00:50.160><c> we</c><00:00:50.280><c> say</c><00:00:50.559><c> context</c><00:00:50.960><c> handling.</c>

00:00:51.790 --> 00:00:51.800 align:start position:0%
variable where we say context handling.
 

00:00:51.800 --> 00:00:53.590 align:start position:0%
variable where we say context handling.
transform<00:00:52.359><c> chat</c><00:00:52.680><c> history</c><00:00:53.199><c> so</c><00:00:53.359><c> this</c><00:00:53.440><c> is</c><00:00:53.520><c> going</c>

00:00:53.590 --> 00:00:53.600 align:start position:0%
transform chat history so this is going
 

00:00:53.600 --> 00:00:55.830 align:start position:0%
transform chat history so this is going
to<00:00:53.719><c> call</c><00:00:53.960><c> the</c><00:00:54.239><c> class</c><00:00:54.840><c> transform</c><00:00:55.320><c> chat</c><00:00:55.520><c> history</c>

00:00:55.830 --> 00:00:55.840 align:start position:0%
to call the class transform chat history
 

00:00:55.840 --> 00:00:57.590 align:start position:0%
to call the class transform chat history
over<00:00:56.039><c> here</c><00:00:56.520><c> and</c><00:00:56.640><c> we</c><00:00:56.760><c> have</c><00:00:56.920><c> three</c><00:00:57.160><c> variables</c>

00:00:57.590 --> 00:00:57.600 align:start position:0%
over here and we have three variables
 

00:00:57.600 --> 00:00:59.430 align:start position:0%
over here and we have three variables
that<00:00:57.680><c> we</c><00:00:57.800><c> need</c><00:00:58.079><c> we</c><00:00:58.199><c> have</c><00:00:58.440><c> a</c><00:00:58.600><c> Max</c><00:00:58.920><c> tokens</c><00:00:59.280><c> per</c>

00:00:59.430 --> 00:00:59.440 align:start position:0%
that we need we have a Max tokens per
 

00:00:59.440 --> 00:01:02.389 align:start position:0%
that we need we have a Max tokens per
message<00:01:00.000><c> a</c><00:01:00.160><c> Max</c><00:01:00.440><c> messages</c><00:01:01.039><c> and</c><00:01:01.239><c> a</c><00:01:01.440><c> Max</c><00:01:01.800><c> tokens</c>

00:01:02.389 --> 00:01:02.399 align:start position:0%
message a Max messages and a Max tokens
 

00:01:02.399 --> 00:01:04.549 align:start position:0%
message a Max messages and a Max tokens
okay<00:01:02.559><c> this</c><00:01:02.680><c> means</c><00:01:03.239><c> per</c><00:01:03.480><c> message</c><00:01:03.920><c> we</c><00:01:04.040><c> only</c><00:01:04.280><c> want</c>

00:01:04.549 --> 00:01:04.559 align:start position:0%
okay this means per message we only want
 

00:01:04.559 --> 00:01:06.550 align:start position:0%
okay this means per message we only want
a<00:01:04.760><c> maximum</c><00:01:05.119><c> number</c><00:01:05.360><c> of</c><00:01:05.640><c> 100</c><00:01:05.840><c> tokens</c><00:01:06.439><c> the</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
a maximum number of 100 tokens the
 

00:01:06.560 --> 00:01:09.350 align:start position:0%
a maximum number of 100 tokens the
number<00:01:06.799><c> of</c><00:01:06.960><c> messages</c><00:01:07.520><c> total</c><00:01:08.240><c> is</c><00:01:08.439><c> 10</c><00:01:09.080><c> and</c><00:01:09.200><c> the</c>

00:01:09.350 --> 00:01:09.360 align:start position:0%
number of messages total is 10 and the
 

00:01:09.360 --> 00:01:11.350 align:start position:0%
number of messages total is 10 and the
max<00:01:09.680><c> number</c><00:01:09.920><c> of</c><00:01:10.159><c> tokens</c><00:01:10.600><c> alog</c><00:01:11.000><c> together</c><00:01:11.240><c> we</c>

00:01:11.350 --> 00:01:11.360 align:start position:0%
max number of tokens alog together we
 

00:01:11.360 --> 00:01:14.590 align:start position:0%
max number of tokens alog together we
want<00:01:11.680><c> is</c><00:01:11.840><c> 1,000</c><00:01:12.560><c> we</c><00:01:12.720><c> then</c><00:01:12.960><c> can</c><00:01:13.200><c> add</c><00:01:13.520><c> this</c><00:01:14.000><c> to</c><00:01:14.360><c> an</c>

00:01:14.590 --> 00:01:14.600 align:start position:0%
want is 1,000 we then can add this to an
 

00:01:14.600 --> 00:01:16.910 align:start position:0%
want is 1,000 we then can add this to an
agent<00:01:14.960><c> so</c><00:01:15.080><c> we're</c><00:01:15.200><c> going</c><00:01:15.320><c> to</c><00:01:15.560><c> add</c><00:01:16.240><c> this</c><00:01:16.439><c> to</c><00:01:16.680><c> the</c>

00:01:16.910 --> 00:01:16.920 align:start position:0%
agent so we're going to add this to the
 

00:01:16.920 --> 00:01:19.789 align:start position:0%
agent so we're going to add this to the
assistant<00:01:17.520><c> agent</c><00:01:18.479><c> we</c><00:01:18.640><c> have</c><00:01:18.840><c> a</c><00:01:19.000><c> user</c><00:01:19.400><c> proxy</c>

00:01:19.789 --> 00:01:19.799 align:start position:0%
assistant agent we have a user proxy
 

00:01:19.799 --> 00:01:21.830 align:start position:0%
assistant agent we have a user proxy
agent<00:01:20.400><c> and</c><00:01:20.520><c> then</c><00:01:20.640><c> we</c><00:01:20.799><c> just</c><00:01:21.000><c> initiate</c><00:01:21.400><c> the</c><00:01:21.560><c> chat</c>

00:01:21.830 --> 00:01:21.840 align:start position:0%
agent and then we just initiate the chat
 

00:01:21.840 --> 00:01:24.230 align:start position:0%
agent and then we just initiate the chat
like<00:01:22.119><c> normal</c><00:01:22.720><c> okay</c><00:01:22.840><c> so</c><00:01:22.960><c> here's</c><00:01:23.119><c> an</c><00:01:23.360><c> example</c><00:01:24.079><c> we</c>

00:01:24.230 --> 00:01:24.240 align:start position:0%
like normal okay so here's an example we
 

00:01:24.240 --> 00:01:26.830 align:start position:0%
like normal okay so here's an example we
have<00:01:24.360><c> an</c><00:01:24.560><c> example</c><00:01:25.400><c> with</c><00:01:25.680><c> and</c><00:01:25.960><c> without</c><00:01:26.360><c> context</c>

00:01:26.830 --> 00:01:26.840 align:start position:0%
have an example with and without context
 

00:01:26.840 --> 00:01:29.469 align:start position:0%
have an example with and without context
handling<00:01:27.320><c> so</c><00:01:27.520><c> without</c><00:01:28.000><c> context</c><00:01:28.560><c> handling</c>

00:01:29.469 --> 00:01:29.479 align:start position:0%
handling so without context handling
 

00:01:29.479 --> 00:01:32.310 align:start position:0%
handling so without context handling
what<00:01:29.600><c> we</c><00:01:30.000><c> have</c><00:01:30.200><c> here</c><00:01:30.360><c> is</c><00:01:30.479><c> an</c><00:01:30.759><c> error</c><00:01:31.479><c> so</c><00:01:31.920><c> up</c><00:01:32.119><c> here</c>

00:01:32.310 --> 00:01:32.320 align:start position:0%
what we have here is an error so up here
 

00:01:32.320 --> 00:01:34.870 align:start position:0%
what we have here is an error so up here
in<00:01:32.439><c> this</c><00:01:32.640><c> for</c><00:01:33.079><c> Loop</c><00:01:33.799><c> they've</c><00:01:34.119><c> essentially</c>

00:01:34.870 --> 00:01:34.880 align:start position:0%
in this for Loop they've essentially
 

00:01:34.880 --> 00:01:37.950 align:start position:0%
in this for Loop they've essentially
added<00:01:35.479><c> very</c><00:01:36.040><c> long</c><00:01:36.560><c> messages</c><00:01:37.159><c> with</c><00:01:37.360><c> a</c><00:01:37.520><c> lot</c><00:01:37.720><c> of</c>

00:01:37.950 --> 00:01:37.960 align:start position:0%
added very long messages with a lot of
 

00:01:37.960 --> 00:01:40.310 align:start position:0%
added very long messages with a lot of
tokens<00:01:38.560><c> the</c><00:01:38.680><c> model's</c><00:01:39.159><c> maximum</c><00:01:39.640><c> context</c>

00:01:40.310 --> 00:01:40.320 align:start position:0%
tokens the model's maximum context
 

00:01:40.320 --> 00:01:43.310 align:start position:0%
tokens the model's maximum context
length<00:01:40.640><c> is</c><00:01:40.960><c> 497</c><00:01:41.960><c> tokens</c><00:01:42.880><c> however</c><00:01:43.119><c> your</c>

00:01:43.310 --> 00:01:43.320 align:start position:0%
length is 497 tokens however your
 

00:01:43.320 --> 00:01:46.190 align:start position:0%
length is 497 tokens however your
message<00:01:43.840><c> resulted</c><00:01:44.360><c> in</c><00:01:44.880><c> just</c><00:01:45.079><c> over</c><00:01:45.399><c> 1</c><00:01:45.680><c> million</c>

00:01:46.190 --> 00:01:46.200 align:start position:0%
message resulted in just over 1 million
 

00:01:46.200 --> 00:01:48.230 align:start position:0%
message resulted in just over 1 million
token<00:01:46.759><c> okay</c><00:01:46.880><c> so</c><00:01:47.040><c> this</c><00:01:47.159><c> is</c><00:01:47.600><c> this</c><00:01:47.719><c> is</c><00:01:47.880><c> if</c><00:01:48.000><c> we</c><00:01:48.079><c> were</c>

00:01:48.230 --> 00:01:48.240 align:start position:0%
token okay so this is this is if we were
 

00:01:48.240 --> 00:01:49.870 align:start position:0%
token okay so this is this is if we were
to<00:01:48.360><c> do</c><00:01:48.520><c> this</c><00:01:48.680><c> like</c><00:01:48.880><c> normal</c><00:01:49.520><c> this</c><00:01:49.640><c> would</c>

00:01:49.870 --> 00:01:49.880 align:start position:0%
to do this like normal this would
 

00:01:49.880 --> 00:01:51.190 align:start position:0%
to do this like normal this would
obviously<00:01:50.280><c> crash</c><00:01:50.560><c> the</c><00:01:50.680><c> agents</c><00:01:51.000><c> and</c><00:01:51.119><c> we</c>

00:01:51.190 --> 00:01:51.200 align:start position:0%
obviously crash the agents and we
 

00:01:51.200 --> 00:01:52.429 align:start position:0%
obviously crash the agents and we
wouldn't<00:01:51.360><c> be</c><00:01:51.439><c> able</c><00:01:51.600><c> to</c><00:01:51.680><c> run</c><00:01:51.840><c> it</c><00:01:52.119><c> however</c><00:01:52.240><c> if</c><00:01:52.320><c> we</c>

00:01:52.429 --> 00:01:52.439 align:start position:0%
wouldn't be able to run it however if we
 

00:01:52.439 --> 00:01:54.310 align:start position:0%
wouldn't be able to run it however if we
do<00:01:52.600><c> the</c><00:01:52.799><c> same</c><00:01:53.079><c> thing</c><00:01:53.560><c> but</c><00:01:53.880><c> we're</c><00:01:54.000><c> going</c><00:01:54.079><c> to</c>

00:01:54.310 --> 00:01:54.320 align:start position:0%
do the same thing but we're going to
 

00:01:54.320 --> 00:01:56.310 align:start position:0%
do the same thing but we're going to
have<00:01:54.560><c> this</c><00:01:54.719><c> transform</c><00:01:55.200><c> chat</c><00:01:55.520><c> history</c><00:01:55.920><c> now</c>

00:01:56.310 --> 00:01:56.320 align:start position:0%
have this transform chat history now
 

00:01:56.320 --> 00:01:58.469 align:start position:0%
have this transform chat history now
Associated<00:01:56.759><c> to</c><00:01:56.920><c> the</c><00:01:57.039><c> assistant</c><00:01:57.479><c> agent</c><00:01:58.360><c> this</c>

00:01:58.469 --> 00:01:58.479 align:start position:0%
Associated to the assistant agent this
 

00:01:58.479 --> 00:02:00.389 align:start position:0%
Associated to the assistant agent this
means<00:01:58.759><c> that</c><00:01:58.840><c> we're</c><00:01:59.000><c> going</c><00:01:59.079><c> to</c><00:01:59.240><c> truncate</c>

00:02:00.389 --> 00:02:00.399 align:start position:0%
means that we're going to truncate
 

00:02:00.399 --> 00:02:02.310 align:start position:0%
means that we're going to truncate
parts<00:02:00.759><c> of</c><00:02:00.960><c> the</c><00:02:01.159><c> chat</c><00:02:01.640><c> so</c><00:02:01.840><c> that</c><00:02:01.960><c> we</c><00:02:02.079><c> can</c>

00:02:02.310 --> 00:02:02.320 align:start position:0%
parts of the chat so that we can
 

00:02:02.320 --> 00:02:04.950 align:start position:0%
parts of the chat so that we can
actually<00:02:02.600><c> run</c><00:02:02.880><c> this</c><00:02:03.200><c> it</c><00:02:03.280><c> truncated</c><00:02:03.960><c> 1991</c>

00:02:04.950 --> 00:02:04.960 align:start position:0%
actually run this it truncated 1991
 

00:02:04.960 --> 00:02:08.630 align:start position:0%
actually run this it truncated 1991
messages<00:02:05.799><c> and</c><00:02:06.439><c> 49,800</c><00:02:07.439><c> tokens</c><00:02:08.160><c> and</c><00:02:08.319><c> then</c><00:02:08.520><c> it</c>

00:02:08.630 --> 00:02:08.640 align:start position:0%
messages and 49,800 tokens and then it
 

00:02:08.640 --> 00:02:10.710 align:start position:0%
messages and 49,800 tokens and then it
was<00:02:08.840><c> able</c><00:02:09.080><c> to</c><00:02:09.280><c> run</c><00:02:09.879><c> okay</c><00:02:10.000><c> so</c><00:02:10.200><c> it</c><00:02:10.319><c> just</c><00:02:10.440><c> said</c><00:02:10.599><c> it</c>

00:02:10.710 --> 00:02:10.720 align:start position:0%
was able to run okay so it just said it
 

00:02:10.720 --> 00:02:12.430 align:start position:0%
was able to run okay so it just said it
truncated<00:02:11.200><c> a</c><00:02:11.280><c> bunch</c><00:02:11.440><c> of</c><00:02:11.560><c> things</c><00:02:11.800><c> but</c><00:02:12.040><c> let's</c><00:02:12.280><c> go</c>

00:02:12.430 --> 00:02:12.440 align:start position:0%
truncated a bunch of things but let's go
 

00:02:12.440 --> 00:02:15.070 align:start position:0%
truncated a bunch of things but let's go
over<00:02:12.680><c> the</c><00:02:12.959><c> logic</c><00:02:13.319><c> to</c><00:02:13.480><c> see</c><00:02:14.120><c> why</c><00:02:14.400><c> or</c><00:02:14.680><c> how</c><00:02:14.879><c> it's</c>

00:02:15.070 --> 00:02:15.080 align:start position:0%
over the logic to see why or how it's
 

00:02:15.080 --> 00:02:17.190 align:start position:0%
over the logic to see why or how it's
actually<00:02:15.519><c> working</c><00:02:16.519><c> okay</c><00:02:16.680><c> so</c><00:02:16.959><c> in</c><00:02:17.080><c> the</c>

00:02:17.190 --> 00:02:17.200 align:start position:0%
actually working okay so in the
 

00:02:17.200 --> 00:02:19.509 align:start position:0%
actually working okay so in the
transform<00:02:17.680><c> chat</c><00:02:18.000><c> history</c><00:02:18.519><c> what</c><00:02:18.680><c> they've</c><00:02:18.920><c> done</c>

00:02:19.509 --> 00:02:19.519 align:start position:0%
transform chat history what they've done
 

00:02:19.519 --> 00:02:21.509 align:start position:0%
transform chat history what they've done
is<00:02:19.680><c> they</c><00:02:19.840><c> have</c><00:02:20.080><c> three</c><00:02:20.319><c> strategies</c><00:02:20.879><c> here</c><00:02:21.319><c> in</c>

00:02:21.509 --> 00:02:21.519 align:start position:0%
is they have three strategies here in
 

00:02:21.519 --> 00:02:23.190 align:start position:0%
is they have three strategies here in
this<00:02:21.720><c> order</c><00:02:22.400><c> and</c><00:02:22.519><c> this</c><00:02:22.640><c> is</c><00:02:22.760><c> how</c><00:02:22.879><c> it's</c><00:02:23.000><c> going</c><00:02:23.120><c> to</c>

00:02:23.190 --> 00:02:23.200 align:start position:0%
this order and this is how it's going to
 

00:02:23.200 --> 00:02:25.430 align:start position:0%
this order and this is how it's going to
truncate<00:02:23.800><c> tokens</c><00:02:24.239><c> and</c><00:02:24.440><c> messages</c><00:02:25.080><c> so</c><00:02:25.239><c> that</c><00:02:25.360><c> we</c>

00:02:25.430 --> 00:02:25.440 align:start position:0%
truncate tokens and messages so that we
 

00:02:25.440 --> 00:02:27.869 align:start position:0%
truncate tokens and messages so that we
can<00:02:25.599><c> actually</c><00:02:25.879><c> perform</c><00:02:26.239><c> the</c><00:02:26.519><c> task</c><00:02:27.519><c> the</c><00:02:27.680><c> first</c>

00:02:27.869 --> 00:02:27.879 align:start position:0%
can actually perform the task the first
 

00:02:27.879 --> 00:02:29.830 align:start position:0%
can actually perform the task the first
thing<00:02:28.040><c> is</c><00:02:28.160><c> it</c><00:02:28.319><c> truncates</c><00:02:28.920><c> messages</c><00:02:29.280><c> to</c><00:02:29.440><c> a</c>

00:02:29.830 --> 00:02:29.840 align:start position:0%
thing is it truncates messages to a
 

00:02:29.840 --> 00:02:32.150 align:start position:0%
thing is it truncates messages to a
maximum<00:02:30.200><c> number</c><00:02:30.480><c> of</c><00:02:30.800><c> tokens</c><00:02:31.800><c> in</c><00:02:31.959><c> this</c>

00:02:32.150 --> 00:02:32.160 align:start position:0%
maximum number of tokens in this
 

00:02:32.160 --> 00:02:34.229 align:start position:0%
maximum number of tokens in this
variable<00:02:32.640><c> here</c><00:02:32.920><c> that</c><00:02:33.080><c> we</c><00:02:33.280><c> created</c><00:02:33.920><c> in</c><00:02:34.040><c> the</c>

00:02:34.229 --> 00:02:34.239 align:start position:0%
variable here that we created in the
 

00:02:34.239 --> 00:02:36.190 align:start position:0%
variable here that we created in the
context<00:02:34.640><c> handling.</c><00:02:35.040><c> transform</c><00:02:35.400><c> chat</c><00:02:35.640><c> history</c>

00:02:36.190 --> 00:02:36.200 align:start position:0%
context handling. transform chat history
 

00:02:36.200 --> 00:02:38.110 align:start position:0%
context handling. transform chat history
we<00:02:36.319><c> say</c><00:02:36.519><c> that</c><00:02:36.720><c> Max</c><00:02:36.959><c> tokens</c><00:02:37.280><c> per</c><00:02:37.440><c> message</c><00:02:37.680><c> is</c>

00:02:38.110 --> 00:02:38.120 align:start position:0%
we say that Max tokens per message is
 

00:02:38.120 --> 00:02:40.390 align:start position:0%
we say that Max tokens per message is
100<00:02:38.680><c> so</c><00:02:38.840><c> it's</c><00:02:38.959><c> going</c><00:02:39.080><c> to</c><00:02:39.200><c> truncate</c><00:02:40.040><c> all</c><00:02:40.239><c> the</c>

00:02:40.390 --> 00:02:40.400 align:start position:0%
100 so it's going to truncate all the
 

00:02:40.400 --> 00:02:43.710 align:start position:0%
100 so it's going to truncate all the
messages<00:02:41.040><c> down</c><00:02:41.280><c> to</c><00:02:41.840><c> 100</c><00:02:42.239><c> tokens</c><00:02:43.040><c> then</c><00:02:43.560><c> it</c>

00:02:43.710 --> 00:02:43.720 align:start position:0%
messages down to 100 tokens then it
 

00:02:43.720 --> 00:02:45.990 align:start position:0%
messages down to 100 tokens then it
limits<00:02:44.080><c> the</c><00:02:44.200><c> number</c><00:02:44.440><c> of</c><00:02:44.599><c> messages</c><00:02:44.959><c> to</c><00:02:45.239><c> keep</c><00:02:45.800><c> so</c>

00:02:45.990 --> 00:02:46.000 align:start position:0%
limits the number of messages to keep so
 

00:02:46.000 --> 00:02:48.910 align:start position:0%
limits the number of messages to keep so
let's<00:02:46.200><c> say</c><00:02:46.800><c> we</c><00:02:47.000><c> still</c><00:02:47.480><c> have</c><00:02:48.120><c> like</c><00:02:48.319><c> a</c><00:02:48.480><c> th000</c>

00:02:48.910 --> 00:02:48.920 align:start position:0%
let's say we still have like a th000
 

00:02:48.920 --> 00:02:51.589 align:start position:0%
let's say we still have like a th000
messages<00:02:49.720><c> but</c><00:02:49.879><c> now</c><00:02:50.040><c> they're</c><00:02:50.239><c> each</c><00:02:50.519><c> under</c><00:02:51.319><c> 100</c>

00:02:51.589 --> 00:02:51.599 align:start position:0%
messages but now they're each under 100
 

00:02:51.599 --> 00:02:53.630 align:start position:0%
messages but now they're each under 100
tokens<00:02:51.959><c> or</c><00:02:52.159><c> at</c><00:02:52.280><c> the</c><00:02:52.400><c> maximum</c><00:02:52.840><c> 100</c><00:02:53.040><c> tokens</c><00:02:53.480><c> well</c>

00:02:53.630 --> 00:02:53.640 align:start position:0%
tokens or at the maximum 100 tokens well
 

00:02:53.640 --> 00:02:56.550 align:start position:0%
tokens or at the maximum 100 tokens well
that's<00:02:53.879><c> still</c><00:02:54.280><c> a</c><00:02:54.480><c> lot</c><00:02:54.640><c> of</c><00:02:54.840><c> tokens</c><00:02:55.560><c> so</c><00:02:55.959><c> now</c><00:02:56.400><c> this</c>

00:02:56.550 --> 00:02:56.560 align:start position:0%
that's still a lot of tokens so now this
 

00:02:56.560 --> 00:02:59.030 align:start position:0%
that's still a lot of tokens so now this
Max<00:02:56.879><c> messages</c><00:02:57.319><c> equals</c><00:02:57.640><c> 10</c><00:02:58.319><c> we</c><00:02:58.560><c> now</c><00:02:58.800><c> will</c>

00:02:59.030 --> 00:02:59.040 align:start position:0%
Max messages equals 10 we now will
 

00:02:59.040 --> 00:03:01.470 align:start position:0%
Max messages equals 10 we now will
truncate<00:02:59.920><c> all</c><00:03:00.120><c> the</c><00:03:00.239><c> messages</c><00:03:01.000><c> meaning</c><00:03:01.239><c> we'll</c>

00:03:01.470 --> 00:03:01.480 align:start position:0%
truncate all the messages meaning we'll
 

00:03:01.480 --> 00:03:03.750 align:start position:0%
truncate all the messages meaning we'll
remove<00:03:01.879><c> them</c><00:03:02.200><c> down</c><00:03:02.400><c> till</c><00:03:02.640><c> we</c><00:03:02.760><c> have</c><00:03:02.920><c> 10</c><00:03:03.280><c> left</c>

00:03:03.750 --> 00:03:03.760 align:start position:0%
remove them down till we have 10 left
 

00:03:03.760 --> 00:03:05.710 align:start position:0%
remove them down till we have 10 left
and<00:03:04.000><c> then</c><00:03:04.360><c> finally</c><00:03:04.799><c> it</c><00:03:04.920><c> limits</c><00:03:05.280><c> the</c><00:03:05.440><c> total</c>

00:03:05.710 --> 00:03:05.720 align:start position:0%
and then finally it limits the total
 

00:03:05.720 --> 00:03:08.030 align:start position:0%
and then finally it limits the total
number<00:03:05.959><c> of</c><00:03:06.080><c> tokens</c><00:03:06.560><c> in</c><00:03:06.680><c> the</c><00:03:06.879><c> chat</c><00:03:07.280><c> history</c>

00:03:08.030 --> 00:03:08.040 align:start position:0%
number of tokens in the chat history
 

00:03:08.040 --> 00:03:10.270 align:start position:0%
number of tokens in the chat history
which<00:03:08.159><c> means</c><00:03:08.560><c> if</c><00:03:08.680><c> it</c><00:03:08.799><c> adds</c><00:03:09.080><c> up</c><00:03:09.400><c> all</c><00:03:09.760><c> of</c><00:03:10.000><c> the</c>

00:03:10.270 --> 00:03:10.280 align:start position:0%
which means if it adds up all of the
 

00:03:10.280 --> 00:03:12.789 align:start position:0%
which means if it adds up all of the
number<00:03:10.680><c> of</c><00:03:10.879><c> tokens</c><00:03:11.480><c> in</c><00:03:12.159><c> all</c><00:03:12.360><c> these</c><00:03:12.519><c> 10</c>

00:03:12.789 --> 00:03:12.799 align:start position:0%
number of tokens in all these 10
 

00:03:12.799 --> 00:03:15.270 align:start position:0%
number of tokens in all these 10
messages<00:03:13.560><c> if</c><00:03:13.720><c> they're</c><00:03:14.040><c> still</c><00:03:14.560><c> above</c><00:03:15.000><c> this</c>

00:03:15.270 --> 00:03:15.280 align:start position:0%
messages if they're still above this
 

00:03:15.280 --> 00:03:17.630 align:start position:0%
messages if they're still above this
maximum<00:03:15.760><c> number</c><00:03:16.319><c> so</c><00:03:16.560><c> say</c><00:03:16.720><c> if</c><00:03:16.799><c> this</c><00:03:16.920><c> was</c>

00:03:17.630 --> 00:03:17.640 align:start position:0%
maximum number so say if this was
 

00:03:17.640 --> 00:03:20.309 align:start position:0%
maximum number so say if this was
999<00:03:18.640><c> so</c><00:03:19.000><c> we</c><00:03:19.080><c> could</c><00:03:19.239><c> have</c><00:03:19.560><c> 100</c><00:03:19.799><c> tokens</c><00:03:20.159><c> per</c>

00:03:20.309 --> 00:03:20.319 align:start position:0%
999 so we could have 100 tokens per
 

00:03:20.319 --> 00:03:21.509 align:start position:0%
999 so we could have 100 tokens per
message<00:03:20.560><c> and</c><00:03:20.680><c> have</c><00:03:20.840><c> 10</c><00:03:21.000><c> of</c><00:03:21.080><c> them</c><00:03:21.280><c> that</c><00:03:21.360><c> would</c>

00:03:21.509 --> 00:03:21.519 align:start position:0%
message and have 10 of them that would
 

00:03:21.519 --> 00:03:24.830 align:start position:0%
message and have 10 of them that would
be<00:03:21.799><c> 1,000</c><00:03:22.480><c> well</c><00:03:22.640><c> the</c><00:03:22.720><c> max</c><00:03:23.000><c> tokens</c><00:03:23.280><c> is</c><00:03:23.720><c> 999</c><00:03:24.720><c> then</c>

00:03:24.830 --> 00:03:24.840 align:start position:0%
be 1,000 well the max tokens is 999 then
 

00:03:24.840 --> 00:03:26.390 align:start position:0%
be 1,000 well the max tokens is 999 then
it'll<00:03:25.040><c> take</c><00:03:25.200><c> away</c><00:03:25.400><c> a</c><00:03:25.519><c> token</c><00:03:25.879><c> from</c><00:03:26.159><c> one</c><00:03:26.280><c> of</c>

00:03:26.390 --> 00:03:26.400 align:start position:0%
it'll take away a token from one of
 

00:03:26.400 --> 00:03:28.110 align:start position:0%
it'll take away a token from one of
those<00:03:26.560><c> messages</c><00:03:26.959><c> so</c><00:03:27.120><c> it</c><00:03:27.200><c> now</c><00:03:27.360><c> reaches</c><00:03:27.799><c> down</c><00:03:27.959><c> to</c>

00:03:28.110 --> 00:03:28.120 align:start position:0%
those messages so it now reaches down to
 

00:03:28.120 --> 00:03:30.470 align:start position:0%
those messages so it now reaches down to
999<00:03:29.120><c> for</c><00:03:29.239><c> the</c><00:03:29.319><c> next</c><00:03:29.480><c> up</c><00:03:30.040><c> they've</c><00:03:30.200><c> added</c>

00:03:30.470 --> 00:03:30.480 align:start position:0%
999 for the next up they've added
 

00:03:30.480 --> 00:03:32.309 align:start position:0%
999 for the next up they've added
runtime<00:03:30.920><c> logging</c><00:03:31.319><c> with</c><00:03:31.480><c> autogen</c><00:03:32.120><c> this</c><00:03:32.200><c> is</c>

00:03:32.309 --> 00:03:32.319 align:start position:0%
runtime logging with autogen this is
 

00:03:32.319 --> 00:03:34.070 align:start position:0%
runtime logging with autogen this is
going<00:03:32.400><c> to</c><00:03:32.560><c> help</c><00:03:32.720><c> to</c><00:03:32.920><c> log</c><00:03:33.200><c> data</c><00:03:33.439><c> for</c><00:03:33.640><c> debugging</c>

00:03:34.070 --> 00:03:34.080 align:start position:0%
going to help to log data for debugging
 

00:03:34.080 --> 00:03:36.030 align:start position:0%
going to help to log data for debugging
and<00:03:34.319><c> performance</c><00:03:34.799><c> analysis</c><00:03:35.640><c> and</c><00:03:35.720><c> basically</c>

00:03:36.030 --> 00:03:36.040 align:start position:0%
and performance analysis and basically
 

00:03:36.040 --> 00:03:37.270 align:start position:0%
and performance analysis and basically
how<00:03:36.120><c> this</c><00:03:36.280><c> works</c><00:03:36.480><c> is</c><00:03:36.599><c> there's</c><00:03:36.760><c> an</c><00:03:36.840><c> autogen</c>

00:03:37.270 --> 00:03:37.280 align:start position:0%
how this works is there's an autogen
 

00:03:37.280 --> 00:03:39.789 align:start position:0%
how this works is there's an autogen
runtime<00:03:37.799><c> logging</c><00:03:38.239><c> dostart</c><00:03:39.239><c> and</c><00:03:39.400><c> then</c><00:03:39.519><c> you</c><00:03:39.599><c> can</c>

00:03:39.789 --> 00:03:39.799 align:start position:0%
runtime logging dostart and then you can
 

00:03:39.799 --> 00:03:42.350 align:start position:0%
runtime logging dostart and then you can
stop<00:03:40.080><c> it</c><00:03:40.280><c> by</c><00:03:40.560><c> calling</c><00:03:40.920><c> the</c><00:03:41.080><c> runtime</c><00:03:41.519><c> log.</c><00:03:42.080><c> stop</c>

00:03:42.350 --> 00:03:42.360 align:start position:0%
stop it by calling the runtime log. stop
 

00:03:42.360 --> 00:03:44.309 align:start position:0%
stop it by calling the runtime log. stop
method<00:03:42.720><c> and</c><00:03:42.879><c> here</c><00:03:43.120><c> they</c><00:03:43.239><c> start</c><00:03:43.560><c> the</c><00:03:43.680><c> logging</c>

00:03:44.309 --> 00:03:44.319 align:start position:0%
method and here they start the logging
 

00:03:44.319 --> 00:03:45.949 align:start position:0%
method and here they start the logging
and<00:03:44.400><c> then</c><00:03:44.560><c> at</c><00:03:44.680><c> the</c><00:03:44.879><c> end</c><00:03:45.200><c> after</c><00:03:45.439><c> they</c><00:03:45.560><c> initiate</c>

00:03:45.949 --> 00:03:45.959 align:start position:0%
and then at the end after they initiate
 

00:03:45.959 --> 00:03:48.229 align:start position:0%
and then at the end after they initiate
the<00:03:46.040><c> chat</c><00:03:46.360><c> so</c><00:03:46.560><c> whenever</c><00:03:47.000><c> we're</c><00:03:47.239><c> done</c><00:03:48.040><c> having</c>

00:03:48.229 --> 00:03:48.239 align:start position:0%
the chat so whenever we're done having
 

00:03:48.239 --> 00:03:50.789 align:start position:0%
the chat so whenever we're done having
the<00:03:48.360><c> llm</c><00:03:48.799><c> calls</c><00:03:49.519><c> then</c><00:03:49.720><c> we</c><00:03:49.840><c> stop</c><00:03:50.120><c> the</c><00:03:50.239><c> logging</c>

00:03:50.789 --> 00:03:50.799 align:start position:0%
the llm calls then we stop the logging
 

00:03:50.799 --> 00:03:52.670 align:start position:0%
the llm calls then we stop the logging
and<00:03:50.879><c> it's</c><00:03:51.040><c> not</c><00:03:51.360><c> just</c><00:03:51.599><c> that</c><00:03:51.840><c> we</c><00:03:51.920><c> can</c><00:03:52.079><c> also</c><00:03:52.400><c> log</c>

00:03:52.670 --> 00:03:52.680 align:start position:0%
and it's not just that we can also log
 

00:03:52.680 --> 00:03:54.789 align:start position:0%
and it's not just that we can also log
these<00:03:52.879><c> into</c><00:03:53.159><c> a</c><00:03:53.360><c> database</c><00:03:54.159><c> as</c><00:03:54.239><c> you</c><00:03:54.360><c> can</c><00:03:54.480><c> see</c>

00:03:54.789 --> 00:03:54.799 align:start position:0%
these into a database as you can see
 

00:03:54.799 --> 00:03:57.309 align:start position:0%
these into a database as you can see
here<00:03:55.040><c> when</c><00:03:55.200><c> we</c><00:03:55.400><c> go</c><00:03:55.560><c> to</c><00:03:55.840><c> runtime</c><00:03:56.319><c> login.</c><00:03:56.920><c> start</c>

00:03:57.309 --> 00:03:57.319 align:start position:0%
here when we go to runtime login. start
 

00:03:57.319 --> 00:03:59.149 align:start position:0%
here when we go to runtime login. start
we<00:03:57.439><c> can</c><00:03:57.599><c> give</c><00:03:57.720><c> it</c><00:03:57.920><c> a</c><00:03:58.079><c> config</c><00:03:58.560><c> with</c><00:03:58.680><c> a</c><00:03:58.799><c> property</c>

00:03:59.149 --> 00:03:59.159 align:start position:0%
we can give it a config with a property
 

00:03:59.159 --> 00:04:01.789 align:start position:0%
we can give it a config with a property
DB<00:03:59.480><c> name</c><00:03:59.840><c> and</c><00:03:59.920><c> then</c><00:04:00.120><c> the</c><00:04:00.239><c> name</c><00:04:00.480><c> logs.</c><00:04:01.040><c> DB</c><00:04:01.640><c> and</c>

00:04:01.789 --> 00:04:01.799 align:start position:0%
DB name and then the name logs. DB and
 

00:04:01.799 --> 00:04:04.149 align:start position:0%
DB name and then the name logs. DB and
what<00:04:01.959><c> this</c><00:04:02.200><c> does</c><00:04:02.439><c> is</c><00:04:02.560><c> it</c><00:04:02.720><c> logs</c><00:04:03.200><c> the</c><00:04:03.439><c> request</c>

00:04:04.149 --> 00:04:04.159 align:start position:0%
what this does is it logs the request
 

00:04:04.159 --> 00:04:07.630 align:start position:0%
what this does is it logs the request
the<00:04:04.519><c> response</c><00:04:05.519><c> the</c><00:04:05.840><c> cost</c><00:04:06.720><c> and</c><00:04:06.840><c> the</c><00:04:07.000><c> start</c><00:04:07.319><c> time</c>

00:04:07.630 --> 00:04:07.640 align:start position:0%
the response the cost and the start time
 

00:04:07.640 --> 00:04:09.750 align:start position:0%
the response the cost and the start time
and<00:04:07.840><c> end</c><00:04:08.079><c> time</c><00:04:08.480><c> and</c><00:04:08.799><c> the</c><00:04:08.920><c> total</c><00:04:09.239><c> number</c><00:04:09.519><c> of</c>

00:04:09.750 --> 00:04:09.760 align:start position:0%
and end time and the total number of
 

00:04:09.760 --> 00:04:12.390 align:start position:0%
and end time and the total number of
tokens<00:04:10.480><c> for</c><00:04:10.920><c> that</c><00:04:11.079><c> llm</c><00:04:11.560><c> call</c><00:04:12.040><c> and</c><00:04:12.159><c> this</c><00:04:12.239><c> is</c>

00:04:12.390 --> 00:04:12.400 align:start position:0%
tokens for that llm call and this is
 

00:04:12.400 --> 00:04:14.390 align:start position:0%
tokens for that llm call and this is
useful<00:04:12.920><c> because</c><00:04:13.400><c> let's</c><00:04:13.560><c> say</c><00:04:13.840><c> we</c><00:04:14.000><c> have</c><00:04:14.200><c> a</c>

00:04:14.390 --> 00:04:14.400 align:start position:0%
useful because let's say we have a
 

00:04:14.400 --> 00:04:17.310 align:start position:0%
useful because let's say we have a
session<00:04:14.799><c> where</c><00:04:14.959><c> you</c><00:04:15.159><c> have</c><00:04:15.400><c> a</c><00:04:15.879><c> huge</c><00:04:16.400><c> prompt</c>

00:04:17.310 --> 00:04:17.320 align:start position:0%
session where you have a huge prompt
 

00:04:17.320 --> 00:04:19.349 align:start position:0%
session where you have a huge prompt
well<00:04:17.519><c> you</c><00:04:17.639><c> can</c><00:04:17.880><c> see</c><00:04:18.400><c> how</c><00:04:18.560><c> much</c><00:04:18.799><c> it's</c><00:04:18.959><c> going</c><00:04:19.079><c> to</c>

00:04:19.349 --> 00:04:19.359 align:start position:0%
well you can see how much it's going to
 

00:04:19.359 --> 00:04:22.030 align:start position:0%
well you can see how much it's going to
cost<00:04:19.720><c> and</c><00:04:19.880><c> the</c><00:04:19.959><c> number</c><00:04:20.160><c> of</c><00:04:20.320><c> tokens</c><00:04:21.120><c> it</c><00:04:21.359><c> took</c><00:04:21.720><c> to</c>

00:04:22.030 --> 00:04:22.040 align:start position:0%
cost and the number of tokens it took to
 

00:04:22.040 --> 00:04:24.150 align:start position:0%
cost and the number of tokens it took to
create<00:04:22.479><c> that</c><00:04:22.759><c> and</c><00:04:22.840><c> then</c><00:04:23.040><c> with</c><00:04:23.160><c> this</c><00:04:23.320><c> analysis</c>

00:04:24.150 --> 00:04:24.160 align:start position:0%
create that and then with this analysis
 

00:04:24.160 --> 00:04:25.629 align:start position:0%
create that and then with this analysis
you<00:04:24.280><c> can</c><00:04:24.400><c> maybe</c><00:04:24.639><c> determine</c><00:04:25.040><c> if</c><00:04:25.160><c> you</c><00:04:25.240><c> need</c><00:04:25.479><c> to</c>

00:04:25.629 --> 00:04:25.639 align:start position:0%
you can maybe determine if you need to
 

00:04:25.639 --> 00:04:27.070 align:start position:0%
you can maybe determine if you need to
have<00:04:25.800><c> better</c><00:04:26.080><c> performance</c><00:04:26.600><c> maybe</c><00:04:26.800><c> it's</c><00:04:26.960><c> with</c>

00:04:27.070 --> 00:04:27.080 align:start position:0%
have better performance maybe it's with
 

00:04:27.080 --> 00:04:29.350 align:start position:0%
have better performance maybe it's with
your<00:04:27.280><c> prompt</c><00:04:27.680><c> or</c><00:04:27.880><c> a</c><00:04:28.040><c> different</c><00:04:28.360><c> model</c><00:04:29.120><c> and</c><00:04:29.240><c> we</c>

00:04:29.350 --> 00:04:29.360 align:start position:0%
your prompt or a different model and we
 

00:04:29.360 --> 00:04:31.550 align:start position:0%
your prompt or a different model and we
can<00:04:29.440><c> also</c><00:04:29.759><c> see</c><00:04:30.080><c> what</c><00:04:30.280><c> models</c><00:04:30.680><c> perform</c><00:04:31.160><c> better</c>

00:04:31.550 --> 00:04:31.560 align:start position:0%
can also see what models perform better
 

00:04:31.560 --> 00:04:32.749 align:start position:0%
can also see what models perform better
I<00:04:31.680><c> really</c><00:04:31.840><c> like</c><00:04:32.039><c> this</c><00:04:32.160><c> one</c><00:04:32.320><c> that</c><00:04:32.440><c> we</c><00:04:32.520><c> can</c><00:04:32.600><c> have</c>

00:04:32.749 --> 00:04:32.759 align:start position:0%
I really like this one that we can have
 

00:04:32.759 --> 00:04:34.350 align:start position:0%
I really like this one that we can have
performance<00:04:33.199><c> analysis</c><00:04:33.919><c> because</c><00:04:34.160><c> just</c><00:04:34.240><c> a</c>

00:04:34.350 --> 00:04:34.360 align:start position:0%
performance analysis because just a
 

00:04:34.360 --> 00:04:36.230 align:start position:0%
performance analysis because just a
couple<00:04:34.600><c> updates</c><00:04:34.919><c> ago</c><00:04:35.400><c> we</c><00:04:35.520><c> had</c><00:04:35.639><c> the</c><00:04:35.759><c> autogen</c>

00:04:36.230 --> 00:04:36.240 align:start position:0%
couple updates ago we had the autogen
 

00:04:36.240 --> 00:04:38.749 align:start position:0%
couple updates ago we had the autogen
Benchmark<00:04:36.880><c> so</c><00:04:37.039><c> we</c><00:04:37.160><c> can</c><00:04:37.280><c> see</c><00:04:38.000><c> which</c><00:04:38.199><c> models</c>

00:04:38.749 --> 00:04:38.759 align:start position:0%
Benchmark so we can see which models
 

00:04:38.759 --> 00:04:40.790 align:start position:0%
Benchmark so we can see which models
have<00:04:39.160><c> correct</c><00:04:39.600><c> tests</c><00:04:40.120><c> and</c><00:04:40.280><c> have</c><00:04:40.440><c> correct</c>

00:04:40.790 --> 00:04:40.800 align:start position:0%
have correct tests and have correct
 

00:04:40.800 --> 00:04:42.629 align:start position:0%
have correct tests and have correct
python<00:04:41.160><c> code</c><00:04:41.680><c> and</c><00:04:41.800><c> then</c><00:04:42.000><c> with</c><00:04:42.160><c> that</c><00:04:42.360><c> we</c><00:04:42.440><c> can</c>

00:04:42.629 --> 00:04:42.639 align:start position:0%
python code and then with that we can
 

00:04:42.639 --> 00:04:44.469 align:start position:0%
python code and then with that we can
also<00:04:42.960><c> log</c><00:04:43.280><c> those</c><00:04:43.479><c> sessions</c><00:04:43.880><c> to</c><00:04:44.000><c> see</c><00:04:44.160><c> how</c><00:04:44.280><c> much</c>

00:04:44.469 --> 00:04:44.479 align:start position:0%
also log those sessions to see how much
 

00:04:44.479 --> 00:04:46.670 align:start position:0%
also log those sessions to see how much
they<00:04:44.639><c> cost</c><00:04:45.120><c> and</c><00:04:45.280><c> how</c><00:04:45.440><c> long</c><00:04:45.880><c> it</c><00:04:46.039><c> took</c><00:04:46.320><c> to</c>

00:04:46.670 --> 00:04:46.680 align:start position:0%
they cost and how long it took to
 

00:04:46.680 --> 00:04:48.710 align:start position:0%
they cost and how long it took to
actually<00:04:47.000><c> run</c><00:04:47.720><c> uh</c><00:04:47.880><c> and</c><00:04:48.039><c> test</c><00:04:48.199><c> the</c><00:04:48.360><c> functions</c>

00:04:48.710 --> 00:04:48.720 align:start position:0%
actually run uh and test the functions
 

00:04:48.720 --> 00:04:49.909 align:start position:0%
actually run uh and test the functions
that<00:04:48.800><c> they</c><00:04:48.960><c> created</c><00:04:49.400><c> and</c><00:04:49.520><c> for</c><00:04:49.639><c> the</c><00:04:49.759><c> last</c>

00:04:49.909 --> 00:04:49.919 align:start position:0%
that they created and for the last
 

00:04:49.919 --> 00:04:51.830 align:start position:0%
that they created and for the last
change<00:04:50.160><c> we</c><00:04:50.280><c> have</c><00:04:50.360><c> the</c><00:04:50.479><c> code</c><00:04:50.759><c> executors</c><00:04:51.639><c> now</c>

00:04:51.830 --> 00:04:51.840 align:start position:0%
change we have the code executors now
 

00:04:51.840 --> 00:04:53.590 align:start position:0%
change we have the code executors now
remember<00:04:52.120><c> whenever</c><00:04:52.440><c> the</c><00:04:52.600><c> assistant</c><00:04:53.120><c> agent</c>

00:04:53.590 --> 00:04:53.600 align:start position:0%
remember whenever the assistant agent
 

00:04:53.600 --> 00:04:56.070 align:start position:0%
remember whenever the assistant agent
writes<00:04:54.120><c> python</c><00:04:54.560><c> code</c><00:04:55.120><c> it's</c><00:04:55.320><c> the</c><00:04:55.479><c> user</c><00:04:55.759><c> agent</c>

00:04:56.070 --> 00:04:56.080 align:start position:0%
writes python code it's the user agent
 

00:04:56.080 --> 00:04:57.990 align:start position:0%
writes python code it's the user agent
that<00:04:56.240><c> actually</c><00:04:56.639><c> executes</c><00:04:57.199><c> that</c><00:04:57.400><c> code</c>

00:04:57.990 --> 00:04:58.000 align:start position:0%
that actually executes that code
 

00:04:58.000 --> 00:04:59.189 align:start position:0%
that actually executes that code
normally<00:04:58.520><c> it's</c><00:04:58.680><c> just</c><00:04:58.759><c> going</c><00:04:58.880><c> to</c><00:04:58.919><c> be</c><00:04:59.000><c> done</c><00:04:59.120><c> on</c>

00:04:59.189 --> 00:04:59.199 align:start position:0%
normally it's just going to be done on
 

00:04:59.199 --> 00:05:00.830 align:start position:0%
normally it's just going to be done on
your<00:04:59.280><c> local</c><00:04:59.680><c> machine</c><00:05:00.160><c> because</c><00:05:00.560><c> if</c><00:05:00.639><c> you</c><00:05:00.720><c> don't</c>

00:05:00.830 --> 00:05:00.840 align:start position:0%
your local machine because if you don't
 

00:05:00.840 --> 00:05:02.150 align:start position:0%
your local machine because if you don't
have<00:05:00.960><c> Docker</c><00:05:01.240><c> running</c><00:05:01.560><c> you</c><00:05:01.639><c> need</c><00:05:01.800><c> to</c><00:05:01.880><c> set</c><00:05:02.039><c> the</c>

00:05:02.150 --> 00:05:02.160 align:start position:0%
have Docker running you need to set the
 

00:05:02.160 --> 00:05:04.590 align:start position:0%
have Docker running you need to set the
used<00:05:02.400><c> Docker</c><00:05:02.840><c> property</c><00:05:03.199><c> to</c><00:05:03.440><c> false</c><00:05:04.160><c> otherwise</c>

00:05:04.590 --> 00:05:04.600 align:start position:0%
used Docker property to false otherwise
 

00:05:04.600 --> 00:05:06.270 align:start position:0%
used Docker property to false otherwise
it'll<00:05:04.880><c> run</c><00:05:05.039><c> in</c><00:05:05.160><c> a</c><00:05:05.240><c> Docker</c><00:05:05.560><c> container</c><00:05:06.160><c> right</c>

00:05:06.270 --> 00:05:06.280 align:start position:0%
it'll run in a Docker container right
 

00:05:06.280 --> 00:05:07.950 align:start position:0%
it'll run in a Docker container right
now<00:05:06.479><c> those</c><00:05:06.600><c> are</c><00:05:06.759><c> really</c><00:05:06.960><c> the</c><00:05:07.080><c> only</c><00:05:07.320><c> two</c><00:05:07.720><c> ways</c>

00:05:07.950 --> 00:05:07.960 align:start position:0%
now those are really the only two ways
 

00:05:07.960 --> 00:05:09.830 align:start position:0%
now those are really the only two ways
to<00:05:08.199><c> have</c><00:05:08.400><c> code</c><00:05:08.720><c> execution</c><00:05:09.440><c> well</c><00:05:09.560><c> what</c><00:05:09.639><c> if</c><00:05:09.720><c> we</c>

00:05:09.830 --> 00:05:09.840 align:start position:0%
to have code execution well what if we
 

00:05:09.840 --> 00:05:10.990 align:start position:0%
to have code execution well what if we
want<00:05:09.960><c> to</c><00:05:10.039><c> use</c><00:05:10.199><c> something</c><00:05:10.400><c> like</c><00:05:10.560><c> Jupiter</c>

00:05:10.990 --> 00:05:11.000 align:start position:0%
want to use something like Jupiter
 

00:05:11.000 --> 00:05:12.670 align:start position:0%
want to use something like Jupiter
notebooks<00:05:11.639><c> right</c><00:05:11.840><c> or</c><00:05:12.080><c> a</c><00:05:12.320><c> different</c>

00:05:12.670 --> 00:05:12.680 align:start position:0%
notebooks right or a different
 

00:05:12.680 --> 00:05:14.629 align:start position:0%
notebooks right or a different
environment<00:05:13.639><c> well</c><00:05:13.960><c> this</c><00:05:14.039><c> is</c><00:05:14.280><c> what</c><00:05:14.440><c> this</c>

00:05:14.629 --> 00:05:14.639 align:start position:0%
environment well this is what this
 

00:05:14.639 --> 00:05:17.150 align:start position:0%
environment well this is what this
change<00:05:15.039><c> is</c><00:05:15.199><c> for</c><00:05:15.960><c> for</c><00:05:16.160><c> instance</c><00:05:16.680><c> here</c><00:05:16.840><c> is</c><00:05:16.960><c> a</c>

00:05:17.150 --> 00:05:17.160 align:start position:0%
change is for for instance here is a
 

00:05:17.160 --> 00:05:19.430 align:start position:0%
change is for for instance here is a
piece<00:05:17.320><c> of</c><00:05:17.479><c> code</c><00:05:17.759><c> we</c><00:05:17.840><c> can</c><00:05:18.000><c> say</c><00:05:18.639><c> user</c><00:05:19.080><c> proxy</c>

00:05:19.430 --> 00:05:19.440 align:start position:0%
piece of code we can say user proxy
 

00:05:19.440 --> 00:05:21.670 align:start position:0%
piece of code we can say user proxy
agent<00:05:19.759><c> give</c><00:05:19.880><c> it</c><00:05:20.000><c> the</c><00:05:20.120><c> name</c><00:05:20.400><c> like</c><00:05:20.600><c> usual</c><00:05:21.400><c> code</c>

00:05:21.670 --> 00:05:21.680 align:start position:0%
agent give it the name like usual code
 

00:05:21.680 --> 00:05:23.830 align:start position:0%
agent give it the name like usual code
execution<00:05:22.160><c> config</c><00:05:22.919><c> but</c><00:05:23.120><c> now</c><00:05:23.400><c> there's</c><00:05:23.639><c> an</c>

00:05:23.830 --> 00:05:23.840 align:start position:0%
execution config but now there's an
 

00:05:23.840 --> 00:05:26.430 align:start position:0%
execution config but now there's an
Executive<00:05:24.400><c> Property</c><00:05:25.199><c> I</c><00:05:25.479><c> python</c><00:05:25.840><c> embedded</c>

00:05:26.430 --> 00:05:26.440 align:start position:0%
Executive Property I python embedded
 

00:05:26.440 --> 00:05:28.350 align:start position:0%
Executive Property I python embedded
this<00:05:26.520><c> is</c><00:05:26.680><c> basically</c><00:05:27.039><c> the</c><00:05:27.160><c> old</c><00:05:27.360><c> version</c><00:05:27.840><c> or</c><00:05:28.000><c> the</c>

00:05:28.350 --> 00:05:28.360 align:start position:0%
this is basically the old version or the
 

00:05:28.360 --> 00:05:30.110 align:start position:0%
this is basically the old version or the
old<00:05:28.560><c> name</c><00:05:28.800><c> of</c><00:05:28.919><c> jup</c><00:05:29.240><c> notebook</c>

00:05:30.110 --> 00:05:30.120 align:start position:0%
old name of jup notebook
 

00:05:30.120 --> 00:05:31.749 align:start position:0%
old name of jup notebook
and<00:05:30.240><c> then</c><00:05:30.560><c> with</c><00:05:30.800><c> that</c><00:05:31.120><c> you</c><00:05:31.240><c> can</c><00:05:31.440><c> give</c><00:05:31.600><c> an</c>

00:05:31.749 --> 00:05:31.759 align:start position:0%
and then with that you can give an
 

00:05:31.759 --> 00:05:33.309 align:start position:0%
and then with that you can give an
output<00:05:32.160><c> directory</c><00:05:32.680><c> or</c><00:05:32.840><c> if</c><00:05:32.919><c> you</c><00:05:33.000><c> just</c><00:05:33.120><c> want</c><00:05:33.199><c> to</c>

00:05:33.309 --> 00:05:33.319 align:start position:0%
output directory or if you just want to
 

00:05:33.319 --> 00:05:35.350 align:start position:0%
output directory or if you just want to
run<00:05:33.520><c> local</c><00:05:33.800><c> command</c><00:05:34.080><c> line</c><00:05:34.319><c> code</c><00:05:34.840><c> you</c><00:05:34.960><c> can</c><00:05:35.120><c> say</c>

00:05:35.350 --> 00:05:35.360 align:start position:0%
run local command line code you can say
 

00:05:35.360 --> 00:05:38.309 align:start position:0%
run local command line code you can say
user<00:05:35.720><c> proxy</c><00:05:36.080><c> agent</c><00:05:36.800><c> code</c><00:05:37.120><c> execution</c><00:05:37.680><c> config</c>

00:05:38.309 --> 00:05:38.319 align:start position:0%
user proxy agent code execution config
 

00:05:38.319 --> 00:05:39.950 align:start position:0%
user proxy agent code execution config
the<00:05:38.479><c> executive</c><00:05:39.039><c> you</c><00:05:39.120><c> can</c><00:05:39.280><c> give</c><00:05:39.400><c> it</c><00:05:39.560><c> command</c>

00:05:39.950 --> 00:05:39.960 align:start position:0%
the executive you can give it command
 

00:05:39.960 --> 00:05:41.710 align:start position:0%
the executive you can give it command
line<00:05:40.240><c> local</c><00:05:40.880><c> and</c><00:05:41.000><c> then</c><00:05:41.120><c> give</c><00:05:41.280><c> the</c><00:05:41.440><c> working</c>

00:05:41.710 --> 00:05:41.720 align:start position:0%
line local and then give the working
 

00:05:41.720 --> 00:05:42.990 align:start position:0%
line local and then give the working
directory<00:05:42.240><c> which</c><00:05:42.360><c> is</c><00:05:42.479><c> the</c><00:05:42.600><c> property</c><00:05:42.919><c> that</c>

00:05:42.990 --> 00:05:43.000 align:start position:0%
directory which is the property that
 

00:05:43.000 --> 00:05:44.469 align:start position:0%
directory which is the property that
we're<00:05:43.160><c> used</c><00:05:43.400><c> to</c><00:05:43.520><c> seeing</c><00:05:43.800><c> in</c><00:05:43.919><c> this</c><00:05:44.120><c> call</c><00:05:44.319><c> it</c>

00:05:44.469 --> 00:05:44.479 align:start position:0%
we're used to seeing in this call it
 

00:05:44.479 --> 00:05:45.790 align:start position:0%
we're used to seeing in this call it
coding<00:05:44.840><c> but</c><00:05:44.960><c> if</c><00:05:45.039><c> we're</c><00:05:45.160><c> using</c><00:05:45.400><c> something</c><00:05:45.680><c> like</c>

00:05:45.790 --> 00:05:45.800 align:start position:0%
coding but if we're using something like
 

00:05:45.800 --> 00:05:47.469 align:start position:0%
coding but if we're using something like
Jupiter<00:05:46.120><c> notebooks</c><00:05:46.600><c> it</c><00:05:46.680><c> would</c><00:05:46.800><c> be</c><00:05:46.960><c> nice</c><00:05:47.319><c> the</c>

00:05:47.469 --> 00:05:47.479 align:start position:0%
Jupiter notebooks it would be nice the
 

00:05:47.479 --> 00:05:49.790 align:start position:0%
Jupiter notebooks it would be nice the
agent<00:05:47.840><c> knows</c><00:05:48.400><c> that</c><00:05:48.479><c> it's</c><00:05:48.680><c> interacting</c><00:05:49.360><c> with</c><00:05:49.600><c> a</c>

00:05:49.790 --> 00:05:49.800 align:start position:0%
agent knows that it's interacting with a
 

00:05:49.800 --> 00:05:51.550 align:start position:0%
agent knows that it's interacting with a
Jupiter<00:05:50.240><c> notebook</c><00:05:50.720><c> and</c><00:05:50.840><c> it</c><00:05:50.960><c> can</c><00:05:51.080><c> use</c><00:05:51.280><c> some</c><00:05:51.400><c> of</c>

00:05:51.550 --> 00:05:51.560 align:start position:0%
Jupiter notebook and it can use some of
 

00:05:51.560 --> 00:05:53.710 align:start position:0%
Jupiter notebook and it can use some of
its<00:05:51.800><c> features</c><00:05:52.600><c> while</c><00:05:52.800><c> you</c><00:05:52.919><c> can</c><00:05:53.080><c> also</c><00:05:53.280><c> create</c><00:05:53.600><c> a</c>

00:05:53.710 --> 00:05:53.720 align:start position:0%
its features while you can also create a
 

00:05:53.720 --> 00:05:55.629 align:start position:0%
its features while you can also create a
userdefined<00:05:54.400><c> executor</c><00:05:55.240><c> and</c><00:05:55.319><c> here's</c><00:05:55.479><c> an</c>

00:05:55.629 --> 00:05:55.639 align:start position:0%
userdefined executor and here's an
 

00:05:55.639 --> 00:05:56.950 align:start position:0%
userdefined executor and here's an
example<00:05:55.919><c> that</c><00:05:56.039><c> they've</c><00:05:56.199><c> created</c><00:05:56.560><c> for</c><00:05:56.800><c> a</c>

00:05:56.950 --> 00:05:56.960 align:start position:0%
example that they've created for a
 

00:05:56.960 --> 00:05:58.749 align:start position:0%
example that they've created for a
notebook<00:05:57.479><c> executor</c><00:05:58.080><c> basically</c><00:05:58.400><c> there's</c><00:05:58.560><c> this</c>

00:05:58.749 --> 00:05:58.759 align:start position:0%
notebook executor basically there's this
 

00:05:58.759 --> 00:06:00.309 align:start position:0%
notebook executor basically there's this
main<00:05:59.000><c> function</c><00:05:59.280><c> that</c><00:05:59.479><c> you</c><00:05:59.560><c> need</c><00:05:59.680><c> to</c><00:05:59.800><c> overwrite</c>

00:06:00.309 --> 00:06:00.319 align:start position:0%
main function that you need to overwrite
 

00:06:00.319 --> 00:06:03.150 align:start position:0%
main function that you need to overwrite
execute<00:06:00.720><c> code</c><00:06:01.039><c> blocks</c><00:06:02.039><c> and</c><00:06:02.160><c> so</c><00:06:02.319><c> for</c><00:06:02.600><c> each</c><00:06:02.880><c> code</c>

00:06:03.150 --> 00:06:03.160 align:start position:0%
execute code blocks and so for each code
 

00:06:03.160 --> 00:06:06.469 align:start position:0%
execute code blocks and so for each code
Block<00:06:03.840><c> in</c><00:06:04.240><c> the</c><00:06:04.479><c> list</c><00:06:04.759><c> of</c><00:06:05.000><c> code</c><00:06:05.280><c> blocks</c><00:06:06.280><c> it's</c>

00:06:06.469 --> 00:06:06.479 align:start position:0%
Block in the list of code blocks it's
 

00:06:06.479 --> 00:06:08.870 align:start position:0%
Block in the list of code blocks it's
going<00:06:06.599><c> to</c><00:06:06.840><c> run</c><00:06:07.440><c> a</c><00:06:07.599><c> cell</c><00:06:08.160><c> and</c><00:06:08.319><c> then</c><00:06:08.599><c> give</c><00:06:08.759><c> the</c>

00:06:08.870 --> 00:06:08.880 align:start position:0%
going to run a cell and then give the
 

00:06:08.880 --> 00:06:10.510 align:start position:0%
going to run a cell and then give the
standard<00:06:09.280><c> output</c><00:06:09.639><c> and</c><00:06:09.720><c> the</c><00:06:09.840><c> standard</c><00:06:10.160><c> error</c>

00:06:10.510 --> 00:06:10.520 align:start position:0%
standard output and the standard error
 

00:06:10.520 --> 00:06:12.110 align:start position:0%
standard output and the standard error
if<00:06:10.639><c> there</c><00:06:10.800><c> was</c><00:06:11.000><c> one</c><00:06:11.560><c> but</c><00:06:11.680><c> this</c><00:06:11.800><c> is</c><00:06:11.919><c> going</c><00:06:12.000><c> to</c>

00:06:12.110 --> 00:06:12.120 align:start position:0%
if there was one but this is going to
 

00:06:12.120 --> 00:06:13.909 align:start position:0%
if there was one but this is going to
allow<00:06:12.360><c> it</c><00:06:12.520><c> to</c><00:06:12.800><c> show</c><00:06:13.039><c> up</c><00:06:13.199><c> on</c><00:06:13.319><c> your</c><00:06:13.440><c> jupyter</c>

00:06:13.909 --> 00:06:13.919 align:start position:0%
allow it to show up on your jupyter
 

00:06:13.919 --> 00:06:16.309 align:start position:0%
allow it to show up on your jupyter
notebooks<00:06:14.400><c> in</c><00:06:14.560><c> a</c><00:06:14.759><c> nicer</c><00:06:15.160><c> formatted</c><00:06:15.919><c> way</c><00:06:16.199><c> and</c>

00:06:16.309 --> 00:06:16.319 align:start position:0%
notebooks in a nicer formatted way and
 

00:06:16.319 --> 00:06:18.870 align:start position:0%
notebooks in a nicer formatted way and
then<00:06:16.479><c> down</c><00:06:16.680><c> here</c><00:06:17.000><c> for</c><00:06:17.199><c> a</c><00:06:17.360><c> user</c><00:06:17.759><c> proxy</c><00:06:18.120><c> agent</c>

00:06:18.870 --> 00:06:18.880 align:start position:0%
then down here for a user proxy agent
 

00:06:18.880 --> 00:06:20.270 align:start position:0%
then down here for a user proxy agent
you<00:06:18.960><c> can</c><00:06:19.120><c> give</c><00:06:19.240><c> it</c><00:06:19.360><c> the</c><00:06:19.479><c> code</c><00:06:19.759><c> execution</c>

00:06:20.270 --> 00:06:20.280 align:start position:0%
you can give it the code execution
 

00:06:20.280 --> 00:06:22.510 align:start position:0%
you can give it the code execution
config<00:06:21.000><c> the</c><00:06:21.199><c> Executive</c><00:06:21.759><c> Property</c><00:06:22.120><c> will</c><00:06:22.280><c> now</c>

00:06:22.510 --> 00:06:22.520 align:start position:0%
config the Executive Property will now
 

00:06:22.520 --> 00:06:24.350 align:start position:0%
config the Executive Property will now
just<00:06:22.720><c> point</c><00:06:23.039><c> to</c><00:06:23.240><c> that</c><00:06:23.440><c> notebook</c><00:06:23.880><c> executive</c>

00:06:24.350 --> 00:06:24.360 align:start position:0%
just point to that notebook executive
 

00:06:24.360 --> 00:06:25.749 align:start position:0%
just point to that notebook executive
class<00:06:24.639><c> that</c><00:06:24.759><c> we</c><00:06:24.919><c> just</c><00:06:25.039><c> created</c><00:06:25.520><c> all</c><00:06:25.639><c> right</c>

00:06:25.749 --> 00:06:25.759 align:start position:0%
class that we just created all right
 

00:06:25.759 --> 00:06:27.430 align:start position:0%
class that we just created all right
those<00:06:25.880><c> are</c><00:06:26.039><c> the</c><00:06:26.120><c> major</c><00:06:26.400><c> updates</c><00:06:26.680><c> from</c><00:06:26.840><c> 2.13</c>

00:06:27.430 --> 00:06:27.440 align:start position:0%
those are the major updates from 2.13
 

00:06:27.440 --> 00:06:29.510 align:start position:0%
those are the major updates from 2.13
and<00:06:27.560><c> 2.14</c><00:06:28.520><c> I</c><00:06:28.599><c> hope</c><00:06:28.720><c> you</c><00:06:28.800><c> learned</c><00:06:29.039><c> something</c>

00:06:29.510 --> 00:06:29.520 align:start position:0%
and 2.14 I hope you learned something
 

00:06:29.520 --> 00:06:31.150 align:start position:0%
and 2.14 I hope you learned something
and<00:06:29.840><c> you</c><00:06:29.960><c> know</c><00:06:30.120><c> it</c><00:06:30.199><c> seems</c><00:06:30.440><c> like</c><00:06:30.720><c> every</c><00:06:30.960><c> week</c>

00:06:31.150 --> 00:06:31.160 align:start position:0%
and you know it seems like every week
 

00:06:31.160 --> 00:06:32.790 align:start position:0%
and you know it seems like every week
they're<00:06:31.319><c> coming</c><00:06:31.520><c> out</c><00:06:31.639><c> with</c><00:06:31.800><c> at</c><00:06:31.960><c> least</c><00:06:32.280><c> one</c><00:06:32.520><c> new</c>

00:06:32.790 --> 00:06:32.800 align:start position:0%
they're coming out with at least one new
 

00:06:32.800 --> 00:06:34.309 align:start position:0%
they're coming out with at least one new
version<00:06:33.319><c> and</c><00:06:33.440><c> they're</c><00:06:33.639><c> really</c><00:06:33.840><c> trying</c><00:06:34.080><c> to</c>

00:06:34.309 --> 00:06:34.319 align:start position:0%
version and they're really trying to
 

00:06:34.319 --> 00:06:36.510 align:start position:0%
version and they're really trying to
push<00:06:34.599><c> the</c><00:06:34.800><c> limits</c><00:06:35.120><c> of</c><00:06:35.280><c> autogen</c><00:06:36.039><c> I</c><00:06:36.120><c> think</c><00:06:36.280><c> from</c>

00:06:36.510 --> 00:06:36.520 align:start position:0%
push the limits of autogen I think from
 

00:06:36.520 --> 00:06:38.270 align:start position:0%
push the limits of autogen I think from
this<00:06:36.800><c> the</c><00:06:37.039><c> code</c><00:06:37.319><c> execution</c><00:06:37.720><c> and</c><00:06:37.840><c> the</c><00:06:37.960><c> logging</c>

00:06:38.270 --> 00:06:38.280 align:start position:0%
this the code execution and the logging
 

00:06:38.280 --> 00:06:40.350 align:start position:0%
this the code execution and the logging
are<00:06:38.520><c> probably</c><00:06:38.759><c> two</c><00:06:39.039><c> of</c><00:06:39.520><c> the</c><00:06:39.720><c> better</c><00:06:39.960><c> ones</c><00:06:40.240><c> cuz</c>

00:06:40.350 --> 00:06:40.360 align:start position:0%
are probably two of the better ones cuz
 

00:06:40.360 --> 00:06:42.909 align:start position:0%
are probably two of the better ones cuz
the<00:06:40.520><c> context</c><00:06:40.919><c> handling</c><00:06:41.720><c> I</c><00:06:42.199><c> understand</c><00:06:42.479><c> but</c>

00:06:42.909 --> 00:06:42.919 align:start position:0%
the context handling I understand but
 

00:06:42.919 --> 00:06:44.790 align:start position:0%
the context handling I understand but
the<00:06:43.039><c> models</c><00:06:43.400><c> are</c><00:06:43.599><c> starting</c><00:06:43.919><c> to</c><00:06:44.080><c> have</c><00:06:44.360><c> bigger</c>

00:06:44.790 --> 00:06:44.800 align:start position:0%
the models are starting to have bigger
 

00:06:44.800 --> 00:06:47.870 align:start position:0%
the models are starting to have bigger
context<00:06:45.280><c> windows</c><00:06:46.280><c> and</c><00:06:46.400><c> so</c><00:06:46.560><c> I</c><00:06:46.639><c> don't</c><00:06:46.800><c> know</c><00:06:47.080><c> how</c>

00:06:47.870 --> 00:06:47.880 align:start position:0%
context windows and so I don't know how
 

00:06:47.880 --> 00:06:48.950 align:start position:0%
context windows and so I don't know how
like<00:06:48.039><c> I</c><00:06:48.080><c> don't</c><00:06:48.199><c> know</c><00:06:48.319><c> how</c><00:06:48.479><c> important</c><00:06:48.759><c> it</c><00:06:48.800><c> will</c>

00:06:48.950 --> 00:06:48.960 align:start position:0%
like I don't know how important it will
 

00:06:48.960 --> 00:06:50.629 align:start position:0%
like I don't know how important it will
be<00:06:49.160><c> because</c><00:06:49.639><c> a</c><00:06:49.720><c> lot</c><00:06:49.840><c> of</c><00:06:49.919><c> people</c><00:06:50.120><c> don't</c><00:06:50.360><c> really</c>

00:06:50.629 --> 00:06:50.639 align:start position:0%
be because a lot of people don't really
 

00:06:50.639 --> 00:06:53.270 align:start position:0%
be because a lot of people don't really
use<00:06:51.080><c> the</c><00:06:51.240><c> whole</c><00:06:51.680><c> context</c><00:06:52.240><c> window</c><00:06:52.800><c> I</c><00:06:52.919><c> know</c><00:06:53.080><c> we'd</c>

00:06:53.270 --> 00:06:53.280 align:start position:0%
use the whole context window I know we'd
 

00:06:53.280 --> 00:06:55.070 align:start position:0%
use the whole context window I know we'd
liked<00:06:53.520><c> it</c><00:06:53.680><c> especially</c><00:06:53.960><c> in</c><00:06:54.039><c> the</c><00:06:54.199><c> beginning</c><00:06:54.960><c> it</c>

00:06:55.070 --> 00:06:55.080 align:start position:0%
liked it especially in the beginning it
 

00:06:55.080 --> 00:06:56.909 align:start position:0%
liked it especially in the beginning it
was<00:06:55.479><c> kind</c><00:06:55.560><c> of</c><00:06:55.680><c> a</c><00:06:55.840><c> huge</c><00:06:56.080><c> deal</c><00:06:56.479><c> right</c><00:06:56.639><c> especially</c>

00:06:56.909 --> 00:06:56.919 align:start position:0%
was kind of a huge deal right especially
 

00:06:56.919 --> 00:06:58.309 align:start position:0%
was kind of a huge deal right especially
with<00:06:57.039><c> something</c><00:06:57.199><c> like</c><00:06:57.280><c> M</c><00:06:57.520><c> GPT</c><00:06:57.960><c> that</c><00:06:58.120><c> really</c>

00:06:58.309 --> 00:06:58.319 align:start position:0%
with something like M GPT that really
 

00:06:58.319 --> 00:07:00.390 align:start position:0%
with something like M GPT that really
helped<00:06:58.599><c> out</c><00:06:58.759><c> with</c><00:06:58.919><c> that</c><00:06:59.199><c> and</c><00:06:59.520><c> this</c><00:06:59.720><c> is</c><00:06:59.919><c> helpful</c>

00:07:00.390 --> 00:07:00.400 align:start position:0%
helped out with that and this is helpful
 

00:07:00.400 --> 00:07:01.749 align:start position:0%
helped out with that and this is helpful
I<00:07:00.520><c> I</c><00:07:00.599><c> totally</c><00:07:01.039><c> understand</c><00:07:01.160><c> that</c><00:07:01.319><c> the</c><00:07:01.440><c> context</c>

00:07:01.749 --> 00:07:01.759 align:start position:0%
I I totally understand that the context
 

00:07:01.759 --> 00:07:03.150 align:start position:0%
I I totally understand that the context
handling<00:07:02.080><c> could</c><00:07:02.240><c> be</c><00:07:02.360><c> helpful</c><00:07:02.800><c> but</c><00:07:02.919><c> I</c><00:07:03.039><c> think</c>

00:07:03.150 --> 00:07:03.160 align:start position:0%
handling could be helpful but I think
 

00:07:03.160 --> 00:07:04.790 align:start position:0%
handling could be helpful but I think
the<00:07:03.240><c> logging</c><00:07:03.560><c> for</c><00:07:03.800><c> performance</c><00:07:04.160><c> analysis</c>

00:07:04.790 --> 00:07:04.800 align:start position:0%
the logging for performance analysis
 

00:07:04.800 --> 00:07:06.070 align:start position:0%
the logging for performance analysis
along<00:07:05.039><c> with</c><00:07:05.199><c> the</c><00:07:05.319><c> Benchmark</c><00:07:05.720><c> that</c><00:07:05.840><c> came</c><00:07:05.960><c> up</c>

00:07:06.070 --> 00:07:06.080 align:start position:0%
along with the Benchmark that came up
 

00:07:06.080 --> 00:07:08.070 align:start position:0%
along with the Benchmark that came up
with<00:07:06.199><c> a</c><00:07:06.280><c> couple</c><00:07:06.440><c> weeks</c><00:07:06.639><c> ago</c><00:07:07.120><c> that</c><00:07:07.280><c> can</c><00:07:07.720><c> really</c>

00:07:08.070 --> 00:07:08.080 align:start position:0%
with a couple weeks ago that can really
 

00:07:08.080 --> 00:07:10.029 align:start position:0%
with a couple weeks ago that can really
help<00:07:08.879><c> understand</c><00:07:09.080><c> what</c><00:07:09.240><c> models</c><00:07:09.560><c> are</c><00:07:09.759><c> better</c>

00:07:10.029 --> 00:07:10.039 align:start position:0%
help understand what models are better
 

00:07:10.039 --> 00:07:11.830 align:start position:0%
help understand what models are better
for<00:07:10.280><c> certain</c><00:07:10.919><c> you</c><00:07:11.039><c> know</c><00:07:11.120><c> for</c><00:07:11.280><c> certain</c><00:07:11.720><c> uh</c>

00:07:11.830 --> 00:07:11.840 align:start position:0%
for certain you know for certain uh
 

00:07:11.840 --> 00:07:13.150 align:start position:0%
for certain you know for certain uh
projects<00:07:12.160><c> that</c><00:07:12.240><c> you</c><00:07:12.319><c> want</c><00:07:12.440><c> to</c><00:07:12.520><c> work</c><00:07:12.720><c> with</c>

00:07:13.150 --> 00:07:13.160 align:start position:0%
projects that you want to work with
 

00:07:13.160 --> 00:07:14.309 align:start position:0%
projects that you want to work with
thank<00:07:13.280><c> you</c><00:07:13.360><c> for</c><00:07:13.560><c> watching</c><00:07:14.000><c> like</c><00:07:14.160><c> And</c>

00:07:14.309 --> 00:07:14.319 align:start position:0%
thank you for watching like And
 

00:07:14.319 --> 00:07:15.869 align:start position:0%
thank you for watching like And
subscribe<00:07:14.800><c> I</c><00:07:14.919><c> also</c><00:07:15.080><c> have</c><00:07:15.160><c> a</c><00:07:15.240><c> free</c><00:07:15.479><c> newsletter</c>

00:07:15.869 --> 00:07:15.879 align:start position:0%
subscribe I also have a free newsletter
 

00:07:15.879 --> 00:07:17.309 align:start position:0%
subscribe I also have a free newsletter
that<00:07:15.960><c> comes</c><00:07:16.120><c> out</c><00:07:16.360><c> every</c><00:07:16.560><c> Sunday</c><00:07:17.000><c> the</c><00:07:17.160><c> link</c>

00:07:17.309 --> 00:07:17.319 align:start position:0%
that comes out every Sunday the link
 

00:07:17.319 --> 00:07:18.589 align:start position:0%
that comes out every Sunday the link
will<00:07:17.400><c> be</c><00:07:17.520><c> in</c><00:07:17.560><c> the</c><00:07:17.639><c> description</c><00:07:18.000><c> below</c><00:07:18.360><c> also</c>

00:07:18.589 --> 00:07:18.599 align:start position:0%
will be in the description below also
 

00:07:18.599 --> 00:07:19.990 align:start position:0%
will be in the description below also
check<00:07:18.759><c> out</c><00:07:18.879><c> my</c><00:07:19.039><c> GitHub</c><00:07:19.520><c> if</c><00:07:19.599><c> you</c><00:07:19.720><c> have</c><00:07:19.840><c> any</c>

00:07:19.990 --> 00:07:20.000 align:start position:0%
check out my GitHub if you have any
 

00:07:20.000 --> 00:07:21.430 align:start position:0%
check out my GitHub if you have any
questions<00:07:20.280><c> or</c><00:07:20.440><c> comments</c><00:07:20.840><c> please</c><00:07:21.039><c> leave</c><00:07:21.240><c> them</c>

00:07:21.430 --> 00:07:21.440 align:start position:0%
questions or comments please leave them
 

00:07:21.440 --> 00:07:22.830 align:start position:0%
questions or comments please leave them
down<00:07:21.599><c> below</c><00:07:22.000><c> here</c><00:07:22.120><c> are</c><00:07:22.199><c> some</c><00:07:22.319><c> more</c><00:07:22.440><c> videos</c><00:07:22.680><c> on</c>

00:07:22.830 --> 00:07:22.840 align:start position:0%
down below here are some more videos on
 

00:07:22.840 --> 00:07:26.599 align:start position:0%
down below here are some more videos on
autogen<00:07:23.520><c> have</c><00:07:23.680><c> a</c><00:07:23.840><c> wonderful</c><00:07:24.240><c> day</c>

