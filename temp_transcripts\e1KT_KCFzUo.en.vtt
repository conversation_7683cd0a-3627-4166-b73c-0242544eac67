WEBVTT
Kind: captions
Language: en

00:00:01.839 --> 00:00:04.670 align:start position:0%
 
by<00:00:02.040><c> now</c><00:00:02.360><c> we</c><00:00:02.560><c> have</c><00:00:03.000><c> gazillion</c><00:00:03.600><c> ways</c><00:00:03.919><c> of</c><00:00:04.160><c> running</c>

00:00:04.670 --> 00:00:04.680 align:start position:0%
by now we have gazillion ways of running
 

00:00:04.680 --> 00:00:07.150 align:start position:0%
by now we have gazillion ways of running
these<00:00:04.920><c> large</c><00:00:05.240><c> language</c><00:00:05.640><c> models</c><00:00:06.040><c> locally</c><00:00:06.879><c> or</c>

00:00:07.150 --> 00:00:07.160 align:start position:0%
these large language models locally or
 

00:00:07.160 --> 00:00:10.830 align:start position:0%
these large language models locally or
through<00:00:08.080><c> API</c><00:00:09.080><c> we</c><00:00:09.320><c> have</c><00:00:09.760><c> equal</c><00:00:10.160><c> number</c><00:00:10.480><c> of</c>

00:00:10.830 --> 00:00:10.840 align:start position:0%
through API we have equal number of
 

00:00:10.840 --> 00:00:13.789 align:start position:0%
through API we have equal number of
tools<00:00:11.599><c> which</c><00:00:11.759><c> is</c><00:00:11.920><c> now</c><00:00:12.160><c> unlimited</c><00:00:12.840><c> almost</c><00:00:13.599><c> to</c>

00:00:13.789 --> 00:00:13.799 align:start position:0%
tools which is now unlimited almost to
 

00:00:13.799 --> 00:00:17.070 align:start position:0%
tools which is now unlimited almost to
run<00:00:14.559><c> these</c><00:00:14.799><c> models</c><00:00:15.240><c> on</c><00:00:15.440><c> our</c><00:00:15.719><c> local</c><00:00:16.119><c> systems</c>

00:00:17.070 --> 00:00:17.080 align:start position:0%
run these models on our local systems
 

00:00:17.080 --> 00:00:19.510 align:start position:0%
run these models on our local systems
and<00:00:17.279><c> we</c><00:00:17.439><c> have</c><00:00:17.680><c> covered</c><00:00:18.119><c> almost</c><00:00:18.560><c> all</c><00:00:18.760><c> of</c><00:00:19.000><c> them</c>

00:00:19.510 --> 00:00:19.520 align:start position:0%
and we have covered almost all of them
 

00:00:19.520 --> 00:00:22.590 align:start position:0%
and we have covered almost all of them
on<00:00:19.680><c> the</c><00:00:20.320><c> channel</c><00:00:21.320><c> in</c><00:00:21.519><c> this</c><00:00:21.720><c> video</c><00:00:22.199><c> I'm</c><00:00:22.400><c> going</c>

00:00:22.590 --> 00:00:22.600 align:start position:0%
on the channel in this video I'm going
 

00:00:22.600 --> 00:00:25.509 align:start position:0%
on the channel in this video I'm going
to<00:00:22.840><c> revisit</c><00:00:23.359><c> a</c><00:00:23.560><c> tool</c><00:00:24.279><c> which</c><00:00:24.439><c> I</c><00:00:24.599><c> covered</c><00:00:25.199><c> last</c>

00:00:25.509 --> 00:00:25.519 align:start position:0%
to revisit a tool which I covered last
 

00:00:25.519 --> 00:00:29.950 align:start position:0%
to revisit a tool which I covered last
year<00:00:25.800><c> when</c><00:00:25.960><c> it</c><00:00:26.119><c> was</c><00:00:27.160><c> released</c><00:00:28.160><c> very</c><00:00:28.599><c> very</c><00:00:29.599><c> uh</c>

00:00:29.950 --> 00:00:29.960 align:start position:0%
year when it was released very very uh
 

00:00:29.960 --> 00:00:32.510 align:start position:0%
year when it was released very very uh
early<00:00:30.240><c> days</c><00:00:30.480><c> at</c><00:00:30.679><c> that</c><00:00:30.880><c> time</c><00:00:31.640><c> and</c><00:00:32.000><c> I</c><00:00:32.160><c> just</c>

00:00:32.510 --> 00:00:32.520 align:start position:0%
early days at that time and I just
 

00:00:32.520 --> 00:00:34.709 align:start position:0%
early days at that time and I just
noticed<00:00:33.040><c> that</c><00:00:33.280><c> this</c><00:00:33.480><c> project</c><00:00:33.920><c> has</c><00:00:34.160><c> evolved</c><00:00:34.600><c> a</c>

00:00:34.709 --> 00:00:34.719 align:start position:0%
noticed that this project has evolved a
 

00:00:34.719 --> 00:00:37.510 align:start position:0%
noticed that this project has evolved a
lot<00:00:35.160><c> so</c><00:00:35.399><c> let's</c><00:00:35.640><c> try</c><00:00:35.879><c> to</c><00:00:36.079><c> see</c><00:00:36.760><c> where</c><00:00:36.960><c> it</c><00:00:37.160><c> has</c>

00:00:37.510 --> 00:00:37.520 align:start position:0%
lot so let's try to see where it has
 

00:00:37.520 --> 00:00:40.029 align:start position:0%
lot so let's try to see where it has
reached<00:00:37.960><c> to</c><00:00:38.840><c> I</c><00:00:39.000><c> actually</c><00:00:39.280><c> tried</c><00:00:39.559><c> to</c><00:00:39.680><c> search</c>

00:00:40.029 --> 00:00:40.039 align:start position:0%
reached to I actually tried to search
 

00:00:40.039 --> 00:00:42.709 align:start position:0%
reached to I actually tried to search
this<00:00:40.320><c> project</c><00:00:40.680><c> on</c><00:00:40.879><c> my</c><00:00:41.480><c> uh</c><00:00:41.600><c> Channel</c><00:00:42.079><c> as</c><00:00:42.480><c> where</c>

00:00:42.709 --> 00:00:42.719 align:start position:0%
this project on my uh Channel as where
 

00:00:42.719 --> 00:00:45.150 align:start position:0%
this project on my uh Channel as where
exactly<00:00:43.120><c> I</c><00:00:43.280><c> did</c><00:00:43.480><c> the</c><00:00:43.680><c> video</c><00:00:44.520><c> but</c><00:00:44.680><c> I</c><00:00:44.800><c> couldn't</c>

00:00:45.150 --> 00:00:45.160 align:start position:0%
exactly I did the video but I couldn't
 

00:00:45.160 --> 00:00:47.910 align:start position:0%
exactly I did the video but I couldn't
because<00:00:45.360><c> the</c><00:00:45.559><c> name</c><00:00:45.760><c> is</c><00:00:46.000><c> so</c><00:00:46.440><c> generic</c><00:00:47.440><c> and</c><00:00:47.760><c> that</c>

00:00:47.910 --> 00:00:47.920 align:start position:0%
because the name is so generic and that
 

00:00:47.920 --> 00:00:51.270 align:start position:0%
because the name is so generic and that
is<00:00:48.199><c> advantage</c><00:00:48.680><c> of</c><00:00:49.320><c> an</c><00:00:49.760><c> early</c><00:00:50.160><c> entrant</c><00:00:50.840><c> because</c>

00:00:51.270 --> 00:00:51.280 align:start position:0%
is advantage of an early entrant because
 

00:00:51.280 --> 00:00:52.910 align:start position:0%
is advantage of an early entrant because
they<00:00:51.399><c> were</c><00:00:51.600><c> able</c><00:00:51.800><c> to</c><00:00:51.960><c> get</c><00:00:52.199><c> this</c><00:00:52.359><c> name</c><00:00:52.680><c> so</c><00:00:52.840><c> if</c>

00:00:52.910 --> 00:00:52.920 align:start position:0%
they were able to get this name so if
 

00:00:52.920 --> 00:00:54.709 align:start position:0%
they were able to get this name so if
you<00:00:53.039><c> want</c><00:00:53.199><c> to</c><00:00:53.359><c> install</c><00:00:53.719><c> it</c><00:00:53.960><c> it</c><00:00:54.079><c> is</c><00:00:54.239><c> just</c><00:00:54.440><c> P</c>

00:00:54.709 --> 00:00:54.719 align:start position:0%
you want to install it it is just P
 

00:00:54.719 --> 00:00:58.110 align:start position:0%
you want to install it it is just P
install<00:00:55.160><c> llm</c><00:00:56.160><c> even</c><00:00:56.440><c> the</c><00:00:56.640><c> project</c><00:00:57.199><c> name</c><00:00:57.399><c> is</c><00:00:57.559><c> llm</c>

00:00:58.110 --> 00:00:58.120 align:start position:0%
install llm even the project name is llm
 

00:00:58.120 --> 00:01:00.830 align:start position:0%
install llm even the project name is llm
and<00:00:58.280><c> the</c><00:00:58.480><c> repo</c><00:00:58.840><c> name</c><00:00:59.000><c> is</c><00:00:59.160><c> llm</c><00:01:00.160><c> you</c><00:01:00.320><c> won't</c><00:01:00.600><c> find</c>

00:01:00.830 --> 00:01:00.840 align:start position:0%
and the repo name is llm you won't find
 

00:01:00.840 --> 00:01:05.109 align:start position:0%
and the repo name is llm you won't find
it<00:01:01.079><c> any</c><00:01:01.719><c> longer</c><00:01:02.120><c> on</c><00:01:02.359><c> GitHub</c><00:01:03.640><c> so</c><00:01:04.640><c> we</c><00:01:04.799><c> are</c><00:01:04.920><c> going</c>

00:01:05.109 --> 00:01:05.119 align:start position:0%
it any longer on GitHub so we are going
 

00:01:05.119 --> 00:01:07.670 align:start position:0%
it any longer on GitHub so we are going
to<00:01:05.280><c> install</c><00:01:05.840><c> this</c><00:01:06.200><c> locally</c><00:01:07.080><c> and</c><00:01:07.240><c> then</c><00:01:07.439><c> I</c><00:01:07.520><c> will</c>

00:01:07.670 --> 00:01:07.680 align:start position:0%
to install this locally and then I will
 

00:01:07.680 --> 00:01:09.469 align:start position:0%
to install this locally and then I will
show<00:01:08.000><c> you</c><00:01:08.159><c> how</c><00:01:08.320><c> you</c><00:01:08.439><c> can</c><00:01:08.600><c> use</c><00:01:08.799><c> it</c><00:01:08.960><c> with</c><00:01:09.159><c> local</c>

00:01:09.469 --> 00:01:09.479 align:start position:0%
show you how you can use it with local
 

00:01:09.479 --> 00:01:12.310 align:start position:0%
show you how you can use it with local
models<00:01:10.240><c> and</c><00:01:10.479><c> how</c><00:01:10.640><c> you</c><00:01:10.799><c> can</c><00:01:11.159><c> use</c><00:01:11.400><c> it</c><00:01:11.640><c> with</c><00:01:11.840><c> API</c>

00:01:12.310 --> 00:01:12.320 align:start position:0%
models and how you can use it with API
 

00:01:12.320 --> 00:01:15.109 align:start position:0%
models and how you can use it with API
based<00:01:12.640><c> models</c><00:01:13.520><c> the</c><00:01:13.720><c> good</c><00:01:13.920><c> thing</c><00:01:14.159><c> is</c><00:01:14.400><c> that</c><00:01:14.880><c> for</c>

00:01:15.109 --> 00:01:15.119 align:start position:0%
based models the good thing is that for
 

00:01:15.119 --> 00:01:17.550 align:start position:0%
based models the good thing is that for
the<00:01:15.280><c> local</c><00:01:15.600><c> models</c><00:01:16.080><c> it</c><00:01:16.400><c> segregates</c><00:01:17.280><c> those</c>

00:01:17.550 --> 00:01:17.560 align:start position:0%
the local models it segregates those
 

00:01:17.560 --> 00:01:20.990 align:start position:0%
the local models it segregates those
providers<00:01:18.040><c> of</c><00:01:18.200><c> the</c><00:01:18.360><c> model</c><00:01:18.720><c> like</c><00:01:19.280><c> gp4</c><00:01:20.079><c> all</c><00:01:20.799><c> and</c>

00:01:20.990 --> 00:01:21.000 align:start position:0%
providers of the model like gp4 all and
 

00:01:21.000 --> 00:01:23.390 align:start position:0%
providers of the model like gp4 all and
few<00:01:21.320><c> others</c><00:01:21.960><c> as</c><00:01:22.200><c> plugins</c><00:01:22.720><c> and</c><00:01:22.840><c> we</c><00:01:22.960><c> will</c><00:01:23.159><c> also</c>

00:01:23.390 --> 00:01:23.400 align:start position:0%
few others as plugins and we will also
 

00:01:23.400 --> 00:01:27.350 align:start position:0%
few others as plugins and we will also
see<00:01:23.600><c> how</c><00:01:23.720><c> to</c><00:01:24.240><c> get</c><00:01:24.360><c> it</c><00:01:24.640><c> installed</c><00:01:25.640><c> very</c><00:01:26.000><c> very</c>

00:01:27.350 --> 00:01:27.360 align:start position:0%
see how to get it installed very very
 

00:01:27.360 --> 00:01:29.749 align:start position:0%
see how to get it installed very very
easily<00:01:28.360><c> okay</c><00:01:28.520><c> so</c><00:01:28.720><c> before</c><00:01:28.960><c> I</c><00:01:29.079><c> move</c><00:01:29.280><c> forward</c><00:01:29.640><c> let</c>

00:01:29.749 --> 00:01:29.759 align:start position:0%
easily okay so before I move forward let
 

00:01:29.759 --> 00:01:31.590 align:start position:0%
easily okay so before I move forward let
me<00:01:29.960><c> me</c><00:01:30.040><c> introduce</c><00:01:30.439><c> you</c><00:01:30.560><c> to</c><00:01:30.799><c> the</c><00:01:30.960><c> sponsors</c><00:01:31.479><c> of</c>

00:01:31.590 --> 00:01:31.600 align:start position:0%
me me introduce you to the sponsors of
 

00:01:31.600 --> 00:01:34.510 align:start position:0%
me me introduce you to the sponsors of
the<00:01:31.759><c> video</c><00:01:32.119><c> who</c><00:01:32.280><c> are</c><00:01:32.439><c> agent</c><00:01:32.759><c> ql</c><00:01:33.560><c> agent</c><00:01:33.880><c> ql</c><00:01:34.240><c> is</c><00:01:34.360><c> a</c>

00:01:34.510 --> 00:01:34.520 align:start position:0%
the video who are agent ql agent ql is a
 

00:01:34.520 --> 00:01:37.550 align:start position:0%
the video who are agent ql agent ql is a
cury<00:01:34.920><c> language</c><00:01:35.680><c> that</c><00:01:35.840><c> turns</c><00:01:36.280><c> any</c><00:01:37.040><c> web</c><00:01:37.320><c> page</c>

00:01:37.550 --> 00:01:37.560 align:start position:0%
cury language that turns any web page
 

00:01:37.560 --> 00:01:40.270 align:start position:0%
cury language that turns any web page
into<00:01:37.799><c> a</c><00:01:38.000><c> data</c><00:01:38.280><c> source</c><00:01:39.119><c> with</c><00:01:39.320><c> its</c><00:01:39.560><c> python</c><00:01:39.880><c> SDK</c>

00:01:40.270 --> 00:01:40.280 align:start position:0%
into a data source with its python SDK
 

00:01:40.280 --> 00:01:42.510 align:start position:0%
into a data source with its python SDK
and<00:01:40.439><c> live</c><00:01:40.640><c> debugging</c><00:01:41.119><c> tool</c><00:01:41.759><c> you</c><00:01:41.920><c> can</c><00:01:42.119><c> scrape</c>

00:01:42.510 --> 00:01:42.520 align:start position:0%
and live debugging tool you can scrape
 

00:01:42.520 --> 00:01:44.990 align:start position:0%
and live debugging tool you can scrape
and<00:01:42.680><c> interact</c><00:01:43.119><c> with</c><00:01:43.320><c> web</c><00:01:43.560><c> content</c><00:01:44.320><c> agent</c><00:01:44.719><c> ql</c>

00:01:44.990 --> 00:01:45.000 align:start position:0%
and interact with web content agent ql
 

00:01:45.000 --> 00:01:47.149 align:start position:0%
and interact with web content agent ql
works<00:01:45.240><c> on</c><00:01:45.399><c> any</c><00:01:45.640><c> page</c><00:01:45.960><c> it</c><00:01:46.079><c> is</c><00:01:46.240><c> resilient</c><00:01:46.840><c> it</c><00:01:46.920><c> is</c>

00:01:47.149 --> 00:01:47.159 align:start position:0%
works on any page it is resilient it is
 

00:01:47.159 --> 00:01:49.429 align:start position:0%
works on any page it is resilient it is
reusable<00:01:48.159><c> and</c><00:01:48.320><c> it</c><00:01:48.479><c> structures</c><00:01:48.920><c> the</c><00:01:49.040><c> output</c>

00:01:49.429 --> 00:01:49.439 align:start position:0%
reusable and it structures the output
 

00:01:49.439 --> 00:01:51.550 align:start position:0%
reusable and it structures the output
according<00:01:49.759><c> to</c><00:01:49.920><c> the</c><00:01:50.079><c> shape</c><00:01:50.320><c> of</c><00:01:50.479><c> your</c><00:01:50.680><c> cery</c><00:01:51.399><c> and</c>

00:01:51.550 --> 00:01:51.560 align:start position:0%
according to the shape of your cery and
 

00:01:51.560 --> 00:01:53.270 align:start position:0%
according to the shape of your cery and
I<00:01:51.640><c> will</c><00:01:51.799><c> drop</c><00:01:52.040><c> the</c><00:01:52.159><c> link</c><00:01:52.360><c> to</c><00:01:52.520><c> their</c><00:01:52.719><c> website</c><00:01:53.079><c> in</c>

00:01:53.270 --> 00:01:53.280 align:start position:0%
I will drop the link to their website in
 

00:01:53.280 --> 00:01:54.429 align:start position:0%
I will drop the link to their website in
videos

00:01:54.429 --> 00:01:54.439 align:start position:0%
videos
 

00:01:54.439 --> 00:01:57.510 align:start position:0%
videos
description<00:01:55.439><c> okay</c><00:01:55.640><c> so</c><00:01:55.840><c> let's</c><00:01:56.159><c> get</c><00:01:57.119><c> to</c><00:01:57.320><c> my</c>

00:01:57.510 --> 00:01:57.520 align:start position:0%
description okay so let's get to my
 

00:01:57.520 --> 00:01:59.590 align:start position:0%
description okay so let's get to my
terminal<00:01:57.960><c> and</c><00:01:58.119><c> try</c><00:01:58.320><c> to</c><00:01:58.479><c> get</c><00:01:58.640><c> this</c><00:01:59.159><c> installed</c>

00:01:59.590 --> 00:01:59.600 align:start position:0%
terminal and try to get this installed
 

00:01:59.600 --> 00:02:29.990 align:start position:0%
terminal and try to get this installed
this<00:01:59.719><c> is</c><00:01:59.960><c> is</c><00:02:00.200><c> where</c><00:02:00.360><c> I'm</c><00:02:00.479><c> running</c><00:02:00.759><c> OB</c><00:02:01.039><c> 2</c>

00:02:29.990 --> 00:02:30.000 align:start position:0%
 
 

00:02:30.000 --> 00:02:33.270 align:start position:0%
 
is<00:02:30.400><c> done</c><00:02:31.400><c> so</c><00:02:31.680><c> first</c><00:02:32.040><c> let's</c><00:02:32.280><c> see</c><00:02:32.480><c> how</c><00:02:32.680><c> this</c><00:02:32.800><c> llm</c>

00:02:33.270 --> 00:02:33.280 align:start position:0%
is done so first let's see how this llm
 

00:02:33.280 --> 00:02:36.509 align:start position:0%
is done so first let's see how this llm
Library<00:02:33.760><c> works</c><00:02:34.120><c> with</c><00:02:34.480><c> the</c><00:02:35.319><c> local</c><00:02:35.680><c> models</c><00:02:36.319><c> and</c>

00:02:36.509 --> 00:02:36.519 align:start position:0%
Library works with the local models and
 

00:02:36.519 --> 00:02:38.790 align:start position:0%
Library works with the local models and
for<00:02:36.720><c> that</c><00:02:36.879><c> we</c><00:02:37.000><c> will</c><00:02:37.200><c> install</c><00:02:37.599><c> this</c><00:02:37.879><c> GPT</c><00:02:38.239><c> for</c>

00:02:38.790 --> 00:02:38.800 align:start position:0%
for that we will install this GPT for
 

00:02:38.800 --> 00:02:42.670 align:start position:0%
for that we will install this GPT for
all<00:02:39.360><c> plugin</c><00:02:40.319><c> so</c><00:02:40.519><c> I'm</c><00:02:41.239><c> only</c><00:02:41.519><c> doing</c><00:02:41.800><c> llm</c><00:02:42.319><c> install</c>

00:02:42.670 --> 00:02:42.680 align:start position:0%
all plugin so I'm only doing llm install
 

00:02:42.680 --> 00:02:44.309 align:start position:0%
all plugin so I'm only doing llm install
and<00:02:42.840><c> then</c><00:02:43.000><c> the</c><00:02:43.159><c> plugin</c><00:02:43.519><c> name</c><00:02:43.800><c> which</c><00:02:43.920><c> in</c><00:02:44.120><c> this</c>

00:02:44.309 --> 00:02:44.319 align:start position:0%
and then the plugin name which in this
 

00:02:44.319 --> 00:02:47.830 align:start position:0%
and then the plugin name which in this
case<00:02:44.560><c> is</c><00:02:45.159><c> GPT</c><00:02:45.599><c> for</c><00:02:46.440><c> all</c><00:02:46.879><c> this</c><00:02:47.200><c> this</c><00:02:47.319><c> is</c><00:02:47.440><c> another</c>

00:02:47.830 --> 00:02:47.840 align:start position:0%
case is GPT for all this this is another
 

00:02:47.840 --> 00:02:50.309 align:start position:0%
case is GPT for all this this is another
project<00:02:48.200><c> GPT</c><00:02:48.640><c> for</c><00:02:48.879><c> all</c><00:02:49.760><c> which</c><00:02:49.920><c> we</c><00:02:50.040><c> already</c>

00:02:50.309 --> 00:02:50.319 align:start position:0%
project GPT for all which we already
 

00:02:50.319 --> 00:02:52.110 align:start position:0%
project GPT for all which we already
have<00:02:50.480><c> covered</c><00:02:50.800><c> on</c><00:02:50.920><c> the</c><00:02:51.080><c> channel</c><00:02:51.519><c> and</c><00:02:51.680><c> it</c><00:02:51.840><c> lets</c>

00:02:52.110 --> 00:02:52.120 align:start position:0%
have covered on the channel and it lets
 

00:02:52.120 --> 00:02:56.710 align:start position:0%
have covered on the channel and it lets
you<00:02:52.319><c> run</c><00:02:53.000><c> the</c><00:02:53.159><c> language</c><00:02:53.560><c> models</c><00:02:54.440><c> locally</c><00:02:55.720><c> and</c>

00:02:56.710 --> 00:02:56.720 align:start position:0%
you run the language models locally and
 

00:02:56.720 --> 00:02:58.710 align:start position:0%
you run the language models locally and
because<00:02:57.080><c> this</c><00:02:57.239><c> llm</c><00:02:57.640><c> is</c><00:02:57.720><c> a</c><00:02:57.879><c> library</c><00:02:58.400><c> which</c>

00:02:58.710 --> 00:02:58.720 align:start position:0%
because this llm is a library which
 

00:02:58.720 --> 00:03:00.949 align:start position:0%
because this llm is a library which
gives<00:02:59.040><c> you</c><00:02:59.200><c> a</c><00:02:59.400><c> gateway</c><00:02:59.920><c> to</c><00:03:00.040><c> run</c><00:03:00.280><c> all</c><00:03:00.440><c> of</c><00:03:00.640><c> these</c>

00:03:00.949 --> 00:03:00.959 align:start position:0%
gives you a gateway to run all of these
 

00:03:00.959 --> 00:03:03.270 align:start position:0%
gives you a gateway to run all of these
providers<00:03:01.959><c> so</c><00:03:02.159><c> we</c><00:03:02.280><c> have</c><00:03:02.440><c> just</c><00:03:02.640><c> installed</c><00:03:03.120><c> that</c>

00:03:03.270 --> 00:03:03.280 align:start position:0%
providers so we have just installed that
 

00:03:03.280 --> 00:03:06.430 align:start position:0%
providers so we have just installed that
gp4<00:03:03.959><c> all</c><00:03:04.879><c> let's</c><00:03:05.080><c> see</c><00:03:05.360><c> which</c><00:03:05.519><c> models</c><00:03:05.920><c> are</c><00:03:06.159><c> now</c>

00:03:06.430 --> 00:03:06.440 align:start position:0%
gp4 all let's see which models are now
 

00:03:06.440 --> 00:03:08.589 align:start position:0%
gp4 all let's see which models are now
available<00:03:06.879><c> to</c><00:03:07.040><c> us</c><00:03:07.200><c> in</c><00:03:07.360><c> this</c><00:03:07.519><c> Library</c><00:03:08.440><c> by</c>

00:03:08.589 --> 00:03:08.599 align:start position:0%
available to us in this Library by
 

00:03:08.599 --> 00:03:11.589 align:start position:0%
available to us in this Library by
simply<00:03:08.920><c> doing</c><00:03:09.239><c> llm</c><00:03:09.720><c> models</c><00:03:10.239><c> list</c><00:03:11.239><c> so</c><00:03:11.480><c> these</c>

00:03:11.589 --> 00:03:11.599 align:start position:0%
simply doing llm models list so these
 

00:03:11.599 --> 00:03:13.350 align:start position:0%
simply doing llm models list so these
are<00:03:11.840><c> all</c><00:03:12.080><c> the</c><00:03:12.280><c> models</c><00:03:12.680><c> from</c><00:03:12.879><c> different</c>

00:03:13.350 --> 00:03:13.360 align:start position:0%
are all the models from different
 

00:03:13.360 --> 00:03:16.149 align:start position:0%
are all the models from different
providers<00:03:14.360><c> which</c><00:03:14.599><c> not</c><00:03:14.799><c> only</c><00:03:15.319><c> include</c><00:03:15.879><c> the</c>

00:03:16.149 --> 00:03:16.159 align:start position:0%
providers which not only include the
 

00:03:16.159 --> 00:03:18.789 align:start position:0%
providers which not only include the
local<00:03:17.120><c> but</c><00:03:17.280><c> also</c><00:03:17.560><c> the</c><00:03:17.680><c> open</c><00:03:17.959><c> source</c><00:03:18.640><c> and</c>

00:03:18.789 --> 00:03:18.799 align:start position:0%
local but also the open source and
 

00:03:18.799 --> 00:03:21.149 align:start position:0%
local but also the open source and
they're<00:03:19.000><c> all</c><00:03:19.200><c> from</c><00:03:19.400><c> the</c><00:03:19.560><c> GPD</c><00:03:20.440><c> for</c><00:03:20.760><c> all</c><00:03:21.000><c> the</c>

00:03:21.149 --> 00:03:21.159 align:start position:0%
they're all from the GPD for all the
 

00:03:21.159 --> 00:03:23.390 align:start position:0%
they're all from the GPD for all the
below<00:03:21.480><c> one</c><00:03:21.680><c> and</c><00:03:21.840><c> the</c><00:03:22.000><c> top</c><00:03:22.200><c> ones</c><00:03:22.440><c> are</c><00:03:22.640><c> from</c><00:03:23.120><c> open</c>

00:03:23.390 --> 00:03:23.400 align:start position:0%
below one and the top ones are from open
 

00:03:23.400 --> 00:03:28.149 align:start position:0%
below one and the top ones are from open
AI<00:03:24.040><c> provider</c><00:03:25.040><c> if</c><00:03:25.200><c> you</c><00:03:25.920><c> want</c><00:03:26.159><c> to</c><00:03:26.319><c> see</c><00:03:26.680><c> in</c><00:03:27.200><c> detail</c>

00:03:28.149 --> 00:03:28.159 align:start position:0%
AI provider if you want to see in detail
 

00:03:28.159 --> 00:03:31.830 align:start position:0%
AI provider if you want to see in detail
what<00:03:28.400><c> exactly</c><00:03:29.319><c> is</c><00:03:29.840><c> provision</c><00:03:30.280><c> in</c><00:03:30.720><c> that</c><00:03:31.720><c> you</c>

00:03:31.830 --> 00:03:31.840 align:start position:0%
what exactly is provision in that you
 

00:03:31.840 --> 00:03:33.390 align:start position:0%
what exactly is provision in that you
can<00:03:32.080><c> simply</c><00:03:32.400><c> do</c><00:03:32.599><c> the</c>

00:03:33.390 --> 00:03:33.400 align:start position:0%
can simply do the
 

00:03:33.400 --> 00:03:35.509 align:start position:0%
can simply do the
options<00:03:34.400><c> and</c><00:03:34.560><c> that</c><00:03:34.720><c> is</c><00:03:34.840><c> going</c><00:03:35.000><c> to</c><00:03:35.159><c> tell</c><00:03:35.319><c> you</c><00:03:35.439><c> a</c>

00:03:35.509 --> 00:03:35.519 align:start position:0%
options and that is going to tell you a
 

00:03:35.519 --> 00:03:37.509 align:start position:0%
options and that is going to tell you a
bit<00:03:35.720><c> about</c><00:03:36.120><c> hyper</c><00:03:36.519><c> parameters</c><00:03:37.000><c> that</c><00:03:37.200><c> okay</c><00:03:37.400><c> for</c>

00:03:37.509 --> 00:03:37.519 align:start position:0%
bit about hyper parameters that okay for
 

00:03:37.519 --> 00:03:40.190 align:start position:0%
bit about hyper parameters that okay for
example<00:03:37.879><c> if</c><00:03:37.959><c> you</c><00:03:38.040><c> want</c><00:03:38.200><c> to</c><00:03:38.360><c> use</c><00:03:38.720><c> this</c><00:03:39.640><c> it</c><00:03:39.799><c> is</c>

00:03:40.190 --> 00:03:40.200 align:start position:0%
example if you want to use this it is
 

00:03:40.200 --> 00:03:43.710 align:start position:0%
example if you want to use this it is
this<00:03:40.360><c> is</c><00:03:40.480><c> a</c><00:03:40.760><c> size</c><00:03:41.599><c> it</c><00:03:41.760><c> needs</c><00:03:42.159><c> this</c><00:03:42.360><c> much</c><00:03:42.720><c> vram</c>

00:03:43.710 --> 00:03:43.720 align:start position:0%
this is a size it needs this much vram
 

00:03:43.720 --> 00:03:45.630 align:start position:0%
this is a size it needs this much vram
and<00:03:43.920><c> then</c><00:03:44.200><c> these</c><00:03:44.360><c> are</c><00:03:44.560><c> the</c><00:03:44.720><c> hyper</c><00:03:45.080><c> parameters</c>

00:03:45.630 --> 00:03:45.640 align:start position:0%
and then these are the hyper parameters
 

00:03:45.640 --> 00:03:48.630 align:start position:0%
and then these are the hyper parameters
which<00:03:45.799><c> you</c><00:03:45.920><c> can</c><00:03:46.080><c> set</c><00:03:46.400><c> with</c><00:03:46.560><c> it</c><00:03:47.080><c> to</c><00:03:47.319><c> control</c><00:03:47.760><c> the</c>

00:03:48.630 --> 00:03:48.640 align:start position:0%
which you can set with it to control the
 

00:03:48.640 --> 00:03:51.630 align:start position:0%
which you can set with it to control the
output<00:03:49.640><c> okay</c><00:03:49.840><c> so</c><00:03:50.200><c> how</c><00:03:50.319><c> do</c><00:03:50.519><c> we</c><00:03:50.720><c> run</c><00:03:50.959><c> it</c><00:03:51.239><c> so</c><00:03:51.439><c> let</c>

00:03:51.630 --> 00:03:51.640 align:start position:0%
output okay so how do we run it so let
 

00:03:51.640 --> 00:03:55.149 align:start position:0%
output okay so how do we run it so let
me<00:03:52.120><c> show</c><00:03:52.400><c> you</c><00:03:53.079><c> how</c><00:03:53.280><c> can</c><00:03:53.519><c> we</c><00:03:54.000><c> do</c><00:03:54.319><c> the</c><00:03:54.480><c> entrance</c>

00:03:55.149 --> 00:03:55.159 align:start position:0%
me show you how can we do the entrance
 

00:03:55.159 --> 00:03:57.470 align:start position:0%
me show you how can we do the entrance
with<00:03:55.360><c> this</c><00:03:55.519><c> llm</c><00:03:55.959><c> library</c><00:03:56.799><c> with</c><00:03:56.959><c> the</c><00:03:57.159><c> local</c>

00:03:57.470 --> 00:03:57.480 align:start position:0%
with this llm library with the local
 

00:03:57.480 --> 00:03:59.789 align:start position:0%
with this llm library with the local
model<00:03:58.319><c> for</c><00:03:58.599><c> that</c><00:03:58.799><c> all</c><00:03:58.920><c> you</c><00:03:59.040><c> need</c><00:03:59.200><c> to</c><00:03:59.319><c> do</c><00:03:59.439><c> is</c><00:03:59.560><c> to</c>

00:03:59.789 --> 00:03:59.799 align:start position:0%
model for that all you need to do is to
 

00:03:59.799 --> 00:04:03.309 align:start position:0%
model for that all you need to do is to
run<00:04:00.040><c> this</c><00:04:00.159><c> command</c><00:04:00.560><c> like</c><00:04:01.040><c> LL</c><00:04:01.400><c> m-m</c><00:04:02.200><c> M</c><00:04:02.400><c> for</c><00:04:02.599><c> model</c>

00:04:03.309 --> 00:04:03.319 align:start position:0%
run this command like LL m-m M for model
 

00:04:03.319 --> 00:04:05.509 align:start position:0%
run this command like LL m-m M for model
then<00:04:03.519><c> the</c><00:04:03.640><c> model</c><00:04:04.000><c> name</c><00:04:04.640><c> and</c><00:04:04.840><c> you</c><00:04:04.959><c> can</c><00:04:05.200><c> pick</c><00:04:05.400><c> the</c>

00:04:05.509 --> 00:04:05.519 align:start position:0%
then the model name and you can pick the
 

00:04:05.519 --> 00:04:07.309 align:start position:0%
then the model name and you can pick the
model<00:04:05.879><c> from</c><00:04:06.200><c> the</c><00:04:06.519><c> output</c><00:04:06.920><c> which</c><00:04:07.040><c> I</c><00:04:07.200><c> just</c>

00:04:07.309 --> 00:04:07.319 align:start position:0%
model from the output which I just
 

00:04:07.319 --> 00:04:10.309 align:start position:0%
model from the output which I just
showed<00:04:07.640><c> you</c><00:04:08.439><c> and</c><00:04:08.640><c> then</c><00:04:09.439><c> your</c><00:04:09.760><c> prompt</c><00:04:10.159><c> like</c>

00:04:10.309 --> 00:04:10.319 align:start position:0%
showed you and then your prompt like
 

00:04:10.319 --> 00:04:12.750 align:start position:0%
showed you and then your prompt like
three<00:04:10.560><c> romantic</c><00:04:11.319><c> pickup</c><00:04:11.640><c> lines</c><00:04:11.959><c> to</c><00:04:12.159><c> say</c><00:04:12.360><c> to</c>

00:04:12.750 --> 00:04:12.760 align:start position:0%
three romantic pickup lines to say to
 

00:04:12.760 --> 00:04:16.909 align:start position:0%
three romantic pickup lines to say to
girls<00:04:13.760><c> or</c><00:04:14.000><c> to</c><00:04:14.200><c> boys</c>

00:04:16.909 --> 00:04:16.919 align:start position:0%
 
 

00:04:16.919 --> 00:04:19.390 align:start position:0%
 
depends<00:04:17.919><c> so</c><00:04:18.120><c> let's</c><00:04:18.359><c> wait</c><00:04:18.560><c> so</c><00:04:18.919><c> first</c><00:04:19.199><c> time</c>

00:04:19.390 --> 00:04:19.400 align:start position:0%
depends so let's wait so first time
 

00:04:19.400 --> 00:04:20.789 align:start position:0%
depends so let's wait so first time
whenever<00:04:19.720><c> you</c><00:04:19.840><c> run</c><00:04:20.040><c> it</c><00:04:20.239><c> it</c><00:04:20.320><c> is</c><00:04:20.440><c> going</c><00:04:20.639><c> to</c>

00:04:20.789 --> 00:04:20.799 align:start position:0%
whenever you run it it is going to
 

00:04:20.799 --> 00:04:23.110 align:start position:0%
whenever you run it it is going to
download<00:04:21.239><c> the</c><00:04:21.359><c> model</c><00:04:22.320><c> and</c><00:04:22.440><c> you</c><00:04:22.560><c> can</c><00:04:22.720><c> see</c><00:04:22.919><c> the</c>

00:04:23.110 --> 00:04:23.120 align:start position:0%
download the model and you can see the
 

00:04:23.120 --> 00:04:26.830 align:start position:0%
download the model and you can see the
size<00:04:23.840><c> here</c><00:04:24.840><c> so</c><00:04:25.080><c> let's</c><00:04:25.320><c> wait</c><00:04:25.560><c> for</c><00:04:25.720><c> it</c><00:04:26.440><c> and</c><00:04:26.680><c> by</c>

00:04:26.830 --> 00:04:26.840 align:start position:0%
size here so let's wait for it and by
 

00:04:26.840 --> 00:04:29.430 align:start position:0%
size here so let's wait for it and by
the<00:04:26.960><c> way</c><00:04:27.280><c> I</c><00:04:27.560><c> have</c><00:04:28.080><c> one</c><00:04:28.360><c> GPU</c><00:04:28.759><c> card</c><00:04:29.039><c> which</c><00:04:29.160><c> let</c><00:04:29.320><c> me</c>

00:04:29.430 --> 00:04:29.440 align:start position:0%
the way I have one GPU card which let me
 

00:04:29.440 --> 00:04:32.629 align:start position:0%
the way I have one GPU card which let me
quickly<00:04:29.960><c> show</c>

00:04:32.629 --> 00:04:32.639 align:start position:0%
 
 

00:04:32.639 --> 00:04:35.310 align:start position:0%
 
you<00:04:33.639><c> let</c><00:04:33.800><c> me</c><00:04:33.960><c> make</c><00:04:34.120><c> it</c><00:04:34.280><c> bit</c>

00:04:35.310 --> 00:04:35.320 align:start position:0%
you let me make it bit
 

00:04:35.320 --> 00:04:37.950 align:start position:0%
you let me make it bit
bigger<00:04:36.320><c> and</c><00:04:36.479><c> this</c><00:04:36.600><c> is</c><00:04:36.759><c> my</c><00:04:36.919><c> GPU</c><00:04:37.320><c> card</c><00:04:37.520><c> and</c><00:04:37.680><c> VD</c>

00:04:37.950 --> 00:04:37.960 align:start position:0%
bigger and this is my GPU card and VD
 

00:04:37.960 --> 00:04:41.950 align:start position:0%
bigger and this is my GPU card and VD
RTX<00:04:38.680><c> a6000</c><00:04:39.680><c> with</c><00:04:39.840><c> 48</c><00:04:40.280><c> GP</c><00:04:40.639><c> of</c><00:04:40.759><c> V</c><00:04:41.000><c> RAM</c><00:04:41.639><c> and</c><00:04:41.840><c> this</c>

00:04:41.950 --> 00:04:41.960 align:start position:0%
RTX a6000 with 48 GP of V RAM and this
 

00:04:41.960 --> 00:04:44.110 align:start position:0%
RTX a6000 with 48 GP of V RAM and this
is<00:04:42.240><c> courtesy</c><00:04:42.720><c> Mast</c><00:04:43.120><c> compute</c><00:04:43.800><c> who</c><00:04:43.960><c> are</c>

00:04:44.110 --> 00:04:44.120 align:start position:0%
is courtesy Mast compute who are
 

00:04:44.120 --> 00:04:46.870 align:start position:0%
is courtesy Mast compute who are
sponsoring<00:04:44.560><c> the</c><00:04:44.720><c> VM</c><00:04:45.000><c> and</c><00:04:45.160><c> GPU</c><00:04:45.520><c> for</c><00:04:45.800><c> this</c><00:04:46.000><c> video</c>

00:04:46.870 --> 00:04:46.880 align:start position:0%
sponsoring the VM and GPU for this video
 

00:04:46.880 --> 00:04:49.350 align:start position:0%
sponsoring the VM and GPU for this video
If<00:04:47.000><c> you're</c><00:04:47.199><c> looking</c><00:04:47.440><c> to</c><00:04:47.600><c> rent</c><00:04:47.800><c> a</c><00:04:47.960><c> GPU</c><00:04:48.560><c> on</c><00:04:49.080><c> very</c>

00:04:49.350 --> 00:04:49.360 align:start position:0%
If you're looking to rent a GPU on very
 

00:04:49.360 --> 00:04:51.790 align:start position:0%
If you're looking to rent a GPU on very
affordable<00:04:49.960><c> prices</c><00:04:50.960><c> I</c><00:04:51.080><c> will</c><00:04:51.240><c> drop</c><00:04:51.479><c> the</c><00:04:51.600><c> link</c>

00:04:51.790 --> 00:04:51.800 align:start position:0%
affordable prices I will drop the link
 

00:04:51.800 --> 00:04:54.110 align:start position:0%
affordable prices I will drop the link
to<00:04:51.960><c> their</c><00:04:52.160><c> website</c><00:04:52.479><c> in</c><00:04:52.639><c> videos</c><00:04:53.120><c> description</c>

00:04:54.110 --> 00:04:54.120 align:start position:0%
to their website in videos description
 

00:04:54.120 --> 00:04:55.950 align:start position:0%
to their website in videos description
I'm<00:04:54.320><c> also</c><00:04:54.560><c> going</c><00:04:54.720><c> to</c><00:04:54.919><c> give</c><00:04:55.080><c> you</c><00:04:55.240><c> a</c><00:04:55.440><c> coupon</c><00:04:55.759><c> code</c>

00:04:55.950 --> 00:04:55.960 align:start position:0%
I'm also going to give you a coupon code
 

00:04:55.960 --> 00:04:59.029 align:start position:0%
I'm also going to give you a coupon code
of<00:04:56.120><c> 50%</c><00:04:56.720><c> discount</c><00:04:57.080><c> on</c><00:04:57.919><c> range</c><00:04:58.240><c> of</c><00:04:58.440><c> GPU</c><00:04:58.880><c> so</c>

00:04:59.029 --> 00:04:59.039 align:start position:0%
of 50% discount on range of GPU so
 

00:04:59.039 --> 00:05:00.230 align:start position:0%
of 50% discount on range of GPU so
please<00:04:59.199><c> do</c><00:04:59.400><c> check</c>

00:05:00.230 --> 00:05:00.240 align:start position:0%
please do check
 

00:05:00.240 --> 00:05:04.550 align:start position:0%
please do check
amount<00:05:01.240><c> okay</c><00:05:01.479><c> so</c><00:05:01.880><c> let's</c><00:05:02.199><c> go</c><00:05:02.759><c> back</c><00:05:03.320><c> here</c><00:05:04.320><c> so</c>

00:05:04.550 --> 00:05:04.560 align:start position:0%
amount okay so let's go back here so
 

00:05:04.560 --> 00:05:07.390 align:start position:0%
amount okay so let's go back here so
this<00:05:04.680><c> is</c><00:05:04.840><c> my</c><00:05:05.039><c> GPU</c>

00:05:07.390 --> 00:05:07.400 align:start position:0%
this is my GPU
 

00:05:07.400 --> 00:05:12.430 align:start position:0%
this is my GPU
card<00:05:08.400><c> it</c><00:05:08.520><c> is</c><00:05:09.440><c> downloading</c><00:05:10.039><c> so</c><00:05:10.240><c> let's</c>

00:05:12.430 --> 00:05:12.440 align:start position:0%
card it is downloading so let's
 

00:05:12.440 --> 00:05:14.990 align:start position:0%
card it is downloading so let's
wait<00:05:13.440><c> so</c><00:05:13.639><c> the</c><00:05:13.759><c> model</c><00:05:14.039><c> is</c><00:05:14.199><c> downloaded</c><00:05:14.759><c> now</c><00:05:14.880><c> it</c>

00:05:14.990 --> 00:05:15.000 align:start position:0%
wait so the model is downloaded now it
 

00:05:15.000 --> 00:05:17.230 align:start position:0%
wait so the model is downloaded now it
is<00:05:15.160><c> verifying</c><00:05:15.680><c> the</c><00:05:15.800><c> check</c><00:05:16.080><c> sum</c><00:05:16.919><c> once</c><00:05:17.120><c> the</c>

00:05:17.230 --> 00:05:17.240 align:start position:0%
is verifying the check sum once the
 

00:05:17.240 --> 00:05:18.830 align:start position:0%
is verifying the check sum once the
check<00:05:17.479><c> sum</c><00:05:17.680><c> is</c><00:05:17.800><c> done</c><00:05:17.960><c> it</c><00:05:18.080><c> is</c><00:05:18.199><c> going</c><00:05:18.400><c> to</c><00:05:18.520><c> do</c><00:05:18.720><c> the</c>

00:05:18.830 --> 00:05:18.840 align:start position:0%
check sum is done it is going to do the
 

00:05:18.840 --> 00:05:21.230 align:start position:0%
check sum is done it is going to do the
inference<00:05:19.319><c> which</c><00:05:19.479><c> it</c><00:05:19.560><c> is</c><00:05:19.720><c> doing</c><00:05:20.120><c> right</c>

00:05:21.230 --> 00:05:21.240 align:start position:0%
inference which it is doing right
 

00:05:21.240 --> 00:05:23.670 align:start position:0%
inference which it is doing right
now<00:05:22.240><c> and</c><00:05:22.400><c> it</c><00:05:22.479><c> is</c><00:05:22.600><c> going</c><00:05:22.840><c> to</c><00:05:23.120><c> there</c><00:05:23.240><c> you</c><00:05:23.360><c> go</c><00:05:23.520><c> so</c>

00:05:23.670 --> 00:05:23.680 align:start position:0%
now and it is going to there you go so
 

00:05:23.680 --> 00:05:27.350 align:start position:0%
now and it is going to there you go so
it<00:05:23.759><c> is</c><00:05:23.919><c> giving</c><00:05:24.160><c> us</c><00:05:24.319><c> the</c><00:05:24.560><c> response</c>

00:05:27.350 --> 00:05:27.360 align:start position:0%
 
 

00:05:27.360 --> 00:05:34.670 align:start position:0%
 
back<00:05:28.360><c> look</c><00:05:28.560><c> at</c><00:05:28.800><c> that</c><00:05:29.039><c> line</c>

00:05:34.670 --> 00:05:34.680 align:start position:0%
 
 

00:05:34.680 --> 00:05:39.670 align:start position:0%
 
okay<00:05:35.000><c> second</c><00:05:35.280><c> one</c><00:05:35.520><c> is</c><00:05:35.720><c> I</c><00:05:35.840><c> don't</c>

00:05:39.670 --> 00:05:39.680 align:start position:0%
 
 

00:05:39.680 --> 00:05:43.469 align:start position:0%
 
know<00:05:40.680><c> well</c><00:05:40.960><c> I'm</c><00:05:41.160><c> not</c><00:05:41.479><c> sure</c><00:05:42.479><c> we</c><00:05:42.639><c> can</c><00:05:42.880><c> use</c><00:05:43.280><c> this</c>

00:05:43.469 --> 00:05:43.479 align:start position:0%
know well I'm not sure we can use this
 

00:05:43.479 --> 00:05:48.510 align:start position:0%
know well I'm not sure we can use this
one<00:05:44.280><c> anyway</c><00:05:45.360><c> so</c><00:05:46.360><c> but</c><00:05:46.600><c> quite</c><00:05:47.120><c> interesting</c><00:05:48.120><c> now</c>

00:05:48.510 --> 00:05:48.520 align:start position:0%
one anyway so but quite interesting now
 

00:05:48.520 --> 00:05:50.990 align:start position:0%
one anyway so but quite interesting now
if<00:05:48.600><c> you</c><00:05:48.759><c> want</c><00:05:49.000><c> to</c><00:05:49.600><c> instead</c><00:05:49.960><c> of</c><00:05:50.240><c> just</c><00:05:50.479><c> doing</c><00:05:50.800><c> a</c>

00:05:50.990 --> 00:05:51.000 align:start position:0%
if you want to instead of just doing a
 

00:05:51.000 --> 00:05:54.870 align:start position:0%
if you want to instead of just doing a
one<00:05:51.759><c> off</c><00:05:52.080><c> inference</c><00:05:52.639><c> you</c><00:05:52.759><c> can</c><00:05:53.000><c> even</c><00:05:53.680><c> do</c><00:05:53.960><c> the</c>

00:05:54.870 --> 00:05:54.880 align:start position:0%
one off inference you can even do the
 

00:05:54.880 --> 00:05:57.430 align:start position:0%
one off inference you can even do the
chat<00:05:55.280><c> with</c><00:05:55.440><c> the</c><00:05:55.639><c> model</c><00:05:56.639><c> with</c><00:05:56.800><c> something</c><00:05:57.199><c> like</c>

00:05:57.430 --> 00:05:57.440 align:start position:0%
chat with the model with something like
 

00:05:57.440 --> 00:06:00.110 align:start position:0%
chat with the model with something like
this<00:05:57.639><c> chat</c><00:05:58.039><c> llm</c><00:05:58.520><c> chat</c><00:05:58.840><c> and</c><00:05:59.000><c> then</c><00:05:59.120><c> your</c><00:05:59.280><c> model</c>

00:06:00.110 --> 00:06:00.120 align:start position:0%
this chat llm chat and then your model
 

00:06:00.120 --> 00:06:02.790 align:start position:0%
this chat llm chat and then your model
name<00:06:01.120><c> and</c><00:06:01.319><c> this</c><00:06:01.479><c> time</c><00:06:01.680><c> you</c><00:06:01.800><c> see</c><00:06:02.000><c> it</c><00:06:02.360><c> it</c><00:06:02.520><c> hasn't</c>

00:06:02.790 --> 00:06:02.800 align:start position:0%
name and this time you see it it hasn't
 

00:06:02.800 --> 00:06:04.390 align:start position:0%
name and this time you see it it hasn't
really<00:06:03.080><c> loaded</c><00:06:03.400><c> it</c><00:06:03.560><c> so</c><00:06:03.680><c> you</c><00:06:03.759><c> can</c><00:06:03.960><c> just</c><00:06:04.160><c> talk</c>

00:06:04.390 --> 00:06:04.400 align:start position:0%
really loaded it so you can just talk
 

00:06:04.400 --> 00:06:11.510 align:start position:0%
really loaded it so you can just talk
with<00:06:04.560><c> it</c><00:06:05.520><c> hello</c><00:06:06.000><c> what</c><00:06:06.199><c> is</c>

00:06:11.510 --> 00:06:11.520 align:start position:0%
 
 

00:06:11.520 --> 00:06:14.510 align:start position:0%
 
happiness<00:06:12.520><c> there</c><00:06:12.639><c> you</c>

00:06:14.510 --> 00:06:14.520 align:start position:0%
happiness there you
 

00:06:14.520 --> 00:06:17.670 align:start position:0%
happiness there you
go<00:06:15.520><c> so</c><00:06:15.680><c> you</c><00:06:15.800><c> see</c><00:06:15.960><c> it</c><00:06:16.039><c> is</c><00:06:16.199><c> not</c><00:06:16.560><c> talking</c><00:06:16.919><c> with</c>

00:06:17.670 --> 00:06:17.680 align:start position:0%
go so you see it is not talking with
 

00:06:17.680 --> 00:06:20.670 align:start position:0%
go so you see it is not talking with
you<00:06:18.680><c> you</c><00:06:18.840><c> can</c><00:06:19.599><c> it</c><00:06:19.759><c> is</c><00:06:19.919><c> a</c><00:06:20.039><c> simple</c><00:06:20.360><c> chat</c>

00:06:20.670 --> 00:06:20.680 align:start position:0%
you you can it is a simple chat
 

00:06:20.680 --> 00:06:22.469 align:start position:0%
you you can it is a simple chat
interface<00:06:21.160><c> which</c><00:06:21.280><c> you</c><00:06:21.400><c> can</c><00:06:21.560><c> use</c><00:06:21.880><c> just</c><00:06:22.080><c> like</c><00:06:22.319><c> we</c>

00:06:22.469 --> 00:06:22.479 align:start position:0%
interface which you can use just like we
 

00:06:22.479 --> 00:06:25.110 align:start position:0%
interface which you can use just like we
have<00:06:22.639><c> done</c><00:06:22.919><c> with</c><00:06:23.160><c> lot</c><00:06:23.319><c> of</c><00:06:23.479><c> other</c><00:06:24.280><c> tools</c><00:06:24.680><c> out</c>

00:06:25.110 --> 00:06:25.120 align:start position:0%
have done with lot of other tools out
 

00:06:25.120 --> 00:06:27.029 align:start position:0%
have done with lot of other tools out
there

00:06:27.029 --> 00:06:27.039 align:start position:0%
there
 

00:06:27.039 --> 00:06:29.870 align:start position:0%
there
now<00:06:28.039><c> if</c><00:06:28.160><c> you</c><00:06:28.319><c> want</c><00:06:28.520><c> to</c><00:06:28.680><c> use</c><00:06:28.919><c> it</c><00:06:29.160><c> with</c><00:06:29.639><c> some</c>

00:06:29.870 --> 00:06:29.880 align:start position:0%
now if you want to use it with some
 

00:06:29.880 --> 00:06:32.110 align:start position:0%
now if you want to use it with some
other<00:06:30.520><c> so</c><00:06:30.720><c> these</c><00:06:30.840><c> are</c><00:06:31.000><c> the</c><00:06:31.120><c> local</c><00:06:31.440><c> models</c><00:06:31.880><c> so</c>

00:06:32.110 --> 00:06:32.120 align:start position:0%
other so these are the local models so
 

00:06:32.120 --> 00:06:34.790 align:start position:0%
other so these are the local models so
but<00:06:32.280><c> if</c><00:06:32.360><c> you</c><00:06:32.479><c> are</c><00:06:32.680><c> interested</c><00:06:33.120><c> in</c><00:06:33.720><c> using</c><00:06:34.120><c> it</c>

00:06:34.790 --> 00:06:34.800 align:start position:0%
but if you are interested in using it
 

00:06:34.800 --> 00:06:39.070 align:start position:0%
but if you are interested in using it
with<00:06:35.800><c> open</c><00:06:36.199><c> a</c><00:06:36.479><c> model</c><00:06:37.160><c> like</c><00:06:37.400><c> API</c><00:06:37.880><c> based</c><00:06:38.199><c> ones</c><00:06:38.599><c> or</c>

00:06:39.070 --> 00:06:39.080 align:start position:0%
with open a model like API based ones or
 

00:06:39.080 --> 00:06:41.469 align:start position:0%
with open a model like API based ones or
anthropic<00:06:40.080><c> let</c><00:06:40.199><c> me</c><00:06:40.360><c> show</c><00:06:40.560><c> you</c><00:06:40.759><c> how</c><00:06:40.919><c> you</c><00:06:41.039><c> can</c><00:06:41.240><c> do</c>

00:06:41.469 --> 00:06:41.479 align:start position:0%
anthropic let me show you how you can do
 

00:06:41.479 --> 00:06:43.909 align:start position:0%
anthropic let me show you how you can do
that<00:06:42.080><c> okay</c><00:06:42.199><c> so</c><00:06:42.440><c> that</c><00:06:42.560><c> is</c><00:06:42.759><c> done</c><00:06:43.160><c> let</c><00:06:43.319><c> me</c><00:06:43.479><c> see</c><00:06:43.720><c> if</c>

00:06:43.909 --> 00:06:43.919 align:start position:0%
that okay so that is done let me see if
 

00:06:43.919 --> 00:06:47.469 align:start position:0%
that okay so that is done let me see if
I<00:06:44.240><c> exit</c><00:06:44.680><c> like</c><00:06:44.919><c> this</c><00:06:45.520><c> yep</c><00:06:46.520><c> okay</c><00:06:46.880><c> so</c><00:06:47.039><c> for</c><00:06:47.199><c> example</c>

00:06:47.469 --> 00:06:47.479 align:start position:0%
I exit like this yep okay so for example
 

00:06:47.479 --> 00:06:49.309 align:start position:0%
I exit like this yep okay so for example
if<00:06:47.599><c> you</c><00:06:47.720><c> want</c><00:06:47.880><c> to</c><00:06:48.039><c> talk</c><00:06:48.280><c> with</c><00:06:48.479><c> open</c><00:06:48.720><c> a</c><00:06:49.000><c> based</c>

00:06:49.309 --> 00:06:49.319 align:start position:0%
if you want to talk with open a based
 

00:06:49.319 --> 00:06:52.110 align:start position:0%
if you want to talk with open a based
model<00:06:49.680><c> so</c><00:06:49.919><c> you</c><00:06:50.039><c> can</c><00:06:50.280><c> set</c><00:06:50.680><c> the</c><00:06:50.880><c> open</c><00:06:51.160><c> A's</c><00:06:51.720><c> API</c>

00:06:52.110 --> 00:06:52.120 align:start position:0%
model so you can set the open A's API
 

00:06:52.120 --> 00:06:53.270 align:start position:0%
model so you can set the open A's API
key

00:06:53.270 --> 00:06:53.280 align:start position:0%
key
 

00:06:53.280 --> 00:06:55.710 align:start position:0%
key
first<00:06:54.280><c> let</c><00:06:54.400><c> me</c><00:06:54.599><c> paste</c><00:06:54.880><c> my</c><00:06:55.039><c> key</c><00:06:55.280><c> which</c><00:06:55.400><c> you</c><00:06:55.520><c> can</c>

00:06:55.710 --> 00:06:55.720 align:start position:0%
first let me paste my key which you can
 

00:06:55.720 --> 00:06:58.350 align:start position:0%
first let me paste my key which you can
obtain<00:06:56.080><c> from</c><00:06:56.360><c> platform.</c><00:06:57.120><c> open.com</c><00:06:58.080><c> and</c><00:06:58.240><c> that</c>

00:06:58.350 --> 00:06:58.360 align:start position:0%
obtain from platform. open.com and that
 

00:06:58.360 --> 00:07:01.270 align:start position:0%
obtain from platform. open.com and that
is<00:06:58.479><c> a</c><00:06:58.680><c> paid</c><00:06:59.000><c> option</c><00:06:59.280><c> let</c><00:06:59.759><c> paste</c><00:07:00.000><c> it</c>

00:07:01.270 --> 00:07:01.280 align:start position:0%
is a paid option let paste it
 

00:07:01.280 --> 00:07:04.110 align:start position:0%
is a paid option let paste it
here<00:07:02.280><c> so</c><00:07:02.560><c> you</c><00:07:02.720><c> have</c><00:07:02.960><c> pasted</c><00:07:03.400><c> it</c><00:07:03.840><c> and</c>

00:07:04.110 --> 00:07:04.120 align:start position:0%
here so you have pasted it and
 

00:07:04.120 --> 00:07:07.550 align:start position:0%
here so you have pasted it and
thankfully<00:07:04.520><c> it</c><00:07:04.639><c> hasn't</c><00:07:04.960><c> shown</c><00:07:05.319><c> the</c><00:07:05.720><c> key</c><00:07:06.720><c> and</c>

00:07:07.550 --> 00:07:07.560 align:start position:0%
thankfully it hasn't shown the key and
 

00:07:07.560 --> 00:07:10.150 align:start position:0%
thankfully it hasn't shown the key and
without<00:07:07.919><c> specifying</c><00:07:08.520><c> model</c><00:07:08.919><c> you</c><00:07:09.039><c> can</c><00:07:09.280><c> simply</c>

00:07:10.150 --> 00:07:10.160 align:start position:0%
without specifying model you can simply
 

00:07:10.160 --> 00:07:13.309 align:start position:0%
without specifying model you can simply
just<00:07:10.360><c> say</c><00:07:10.599><c> llm</c><00:07:11.520><c> and</c><00:07:11.759><c> your</c><00:07:12.599><c> prompt</c><00:07:12.960><c> like</c><00:07:13.160><c> three</c>

00:07:13.309 --> 00:07:13.319 align:start position:0%
just say llm and your prompt like three
 

00:07:13.319 --> 00:07:16.070 align:start position:0%
just say llm and your prompt like three
romantic<00:07:13.759><c> pickup</c><00:07:14.039><c> lines</c><00:07:14.280><c> to</c><00:07:14.400><c> say</c><00:07:14.560><c> to</c><00:07:14.879><c> boys</c><00:07:15.879><c> are</c>

00:07:16.070 --> 00:07:16.080 align:start position:0%
romantic pickup lines to say to boys are
 

00:07:16.080 --> 00:07:18.029 align:start position:0%
romantic pickup lines to say to boys are
you<00:07:16.199><c> a</c><00:07:16.319><c> magician</c><00:07:16.960><c> because</c><00:07:17.240><c> every</c><00:07:17.479><c> time</c><00:07:17.680><c> I</c><00:07:17.840><c> look</c>

00:07:18.029 --> 00:07:18.039 align:start position:0%
you a magician because every time I look
 

00:07:18.039 --> 00:07:20.670 align:start position:0%
you a magician because every time I look
at<00:07:18.199><c> you</c><00:07:18.360><c> everyone</c><00:07:18.720><c> else</c><00:07:19.000><c> disappears</c><00:07:19.840><c> very</c>

00:07:20.670 --> 00:07:20.680 align:start position:0%
at you everyone else disappears very
 

00:07:20.680 --> 00:07:23.110 align:start position:0%
at you everyone else disappears very
nice<00:07:21.680><c> do</c><00:07:21.840><c> you</c><00:07:22.000><c> believe</c><00:07:22.280><c> in</c><00:07:22.440><c> love</c><00:07:22.680><c> at</c><00:07:22.840><c> first</c>

00:07:23.110 --> 00:07:23.120 align:start position:0%
nice do you believe in love at first
 

00:07:23.120 --> 00:07:25.790 align:start position:0%
nice do you believe in love at first
sight<00:07:23.280><c> or</c><00:07:23.400><c> should</c><00:07:23.599><c> I</c><00:07:23.759><c> walk</c><00:07:24.039><c> by</c><00:07:25.039><c> again</c><00:07:25.400><c> that's</c><00:07:25.560><c> a</c>

00:07:25.790 --> 00:07:25.800 align:start position:0%
sight or should I walk by again that's a
 

00:07:25.800 --> 00:07:29.070 align:start position:0%
sight or should I walk by again that's a
nice<00:07:26.560><c> one</c><00:07:27.560><c> if</c><00:07:27.680><c> you</c><00:07:27.879><c> were</c><00:07:28.080><c> a</c><00:07:28.240><c> song</c><00:07:28.560><c> you'd</c><00:07:28.840><c> be</c><00:07:28.960><c> the</c>

00:07:29.070 --> 00:07:29.080 align:start position:0%
nice one if you were a song you'd be the
 

00:07:29.080 --> 00:07:30.469 align:start position:0%
nice one if you were a song you'd be the
best<00:07:29.280><c> TR</c><00:07:29.520><c> back</c><00:07:29.720><c> on</c><00:07:29.840><c> the</c>

00:07:30.469 --> 00:07:30.479 align:start position:0%
best TR back on the
 

00:07:30.479 --> 00:07:33.070 align:start position:0%
best TR back on the
album<00:07:31.479><c> okay</c><00:07:31.759><c> that</c><00:07:31.879><c> is</c><00:07:32.160><c> nice</c><00:07:32.520><c> and</c><00:07:32.680><c> then</c><00:07:32.800><c> feel</c>

00:07:33.070 --> 00:07:33.080 align:start position:0%
album okay that is nice and then feel
 

00:07:33.080 --> 00:07:35.230 align:start position:0%
album okay that is nice and then feel
free<00:07:33.319><c> to</c><00:07:33.440><c> adapt</c><00:07:33.759><c> them</c><00:07:33.879><c> to</c><00:07:34.080><c> your</c><00:07:34.319><c> style</c><00:07:34.960><c> so</c>

00:07:35.230 --> 00:07:35.240 align:start position:0%
free to adapt them to your style so
 

00:07:35.240 --> 00:07:39.629 align:start position:0%
free to adapt them to your style so
which<00:07:35.400><c> one</c><00:07:35.560><c> you</c><00:07:35.720><c> like</c><00:07:36.000><c> by</c><00:07:36.120><c> the</c><00:07:36.400><c> way</c>

00:07:39.629 --> 00:07:39.639 align:start position:0%
 
 

00:07:39.639 --> 00:07:42.550 align:start position:0%
 
okay<00:07:40.639><c> so</c><00:07:40.840><c> not</c><00:07:41.000><c> only</c><00:07:41.360><c> that</c><00:07:41.639><c> you</c><00:07:41.759><c> can</c><00:07:41.960><c> also</c><00:07:42.319><c> ask</c>

00:07:42.550 --> 00:07:42.560 align:start position:0%
okay so not only that you can also ask
 

00:07:42.560 --> 00:07:45.110 align:start position:0%
okay so not only that you can also ask
it<00:07:42.759><c> to</c><00:07:43.000><c> for</c><00:07:43.199><c> example</c><00:07:43.960><c> review</c><00:07:44.400><c> your</c><00:07:44.680><c> code</c><00:07:44.960><c> or</c>

00:07:45.110 --> 00:07:45.120 align:start position:0%
it to for example review your code or
 

00:07:45.120 --> 00:07:47.029 align:start position:0%
it to for example review your code or
something<00:07:45.479><c> from</c><00:07:45.680><c> a</c><00:07:45.840><c> file</c><00:07:46.520><c> or</c><00:07:46.680><c> you</c><00:07:46.800><c> could</c>

00:07:47.029 --> 00:07:47.039 align:start position:0%
something from a file or you could
 

00:07:47.039 --> 00:07:48.629 align:start position:0%
something from a file or you could
basically<00:07:47.360><c> do</c><00:07:47.520><c> the</c><00:07:47.680><c> file</c><00:07:47.960><c> manipulation</c>

00:07:48.629 --> 00:07:48.639 align:start position:0%
basically do the file manipulation
 

00:07:48.639 --> 00:07:52.029 align:start position:0%
basically do the file manipulation
something<00:07:49.000><c> like</c><00:07:49.360><c> this</c><00:07:50.360><c> maybe</c><00:07:51.120><c> this</c><00:07:51.240><c> is</c><00:07:51.360><c> a</c><00:07:51.560><c> file</c>

00:07:52.029 --> 00:07:52.039 align:start position:0%
something like this maybe this is a file
 

00:07:52.039 --> 00:07:54.990 align:start position:0%
something like this maybe this is a file
test.py<00:07:52.800><c> it's</c><00:07:52.919><c> a</c><00:07:53.080><c> python</c><00:07:53.479><c> script</c><00:07:54.240><c> I</c><00:07:54.319><c> am</c><00:07:54.560><c> piping</c>

00:07:54.990 --> 00:07:55.000 align:start position:0%
test.py it's a python script I am piping
 

00:07:55.000 --> 00:07:58.149 align:start position:0%
test.py it's a python script I am piping
it<00:07:55.319><c> with</c><00:07:55.520><c> this</c><00:07:55.720><c> pipe</c><00:07:56.000><c> sign</c><00:07:56.280><c> to</c><00:07:56.639><c> LL</c><00:07:56.960><c> m-s</c><00:07:57.720><c> as</c><00:07:57.919><c> is</c>

00:07:58.149 --> 00:07:58.159 align:start position:0%
it with this pipe sign to LL m-s as is
 

00:07:58.159 --> 00:08:00.830 align:start position:0%
it with this pipe sign to LL m-s as is
for<00:07:58.840><c> system</c><00:07:59.199><c> Pro</c><00:07:59.479><c> promp</c><00:07:59.720><c> and</c><00:07:59.879><c> the</c><00:08:00.000><c> prompt</c><00:08:00.360><c> is</c>

00:08:00.830 --> 00:08:00.840 align:start position:0%
for system Pro promp and the prompt is
 

00:08:00.840 --> 00:08:03.390 align:start position:0%
for system Pro promp and the prompt is
to<00:08:01.080><c> explain</c><00:08:01.560><c> this</c><00:08:01.759><c> code</c><00:08:02.680><c> so</c><00:08:02.840><c> it</c><00:08:02.960><c> is</c><00:08:03.080><c> going</c><00:08:03.240><c> to</c>

00:08:03.390 --> 00:08:03.400 align:start position:0%
to explain this code so it is going to
 

00:08:03.400 --> 00:08:06.430 align:start position:0%
to explain this code so it is going to
read<00:08:03.720><c> that</c><00:08:03.919><c> file</c><00:08:04.759><c> and</c><00:08:04.960><c> then</c><00:08:05.599><c> there</c><00:08:05.720><c> you</c><00:08:05.879><c> go</c><00:08:06.039><c> so</c>

00:08:06.430 --> 00:08:06.440 align:start position:0%
read that file and then there you go so
 

00:08:06.440 --> 00:08:08.149 align:start position:0%
read that file and then there you go so
yes<00:08:06.599><c> so</c><00:08:06.800><c> this</c><00:08:06.919><c> is</c><00:08:07.000><c> a</c><00:08:07.199><c> code</c><00:08:07.400><c> it</c><00:08:07.560><c> is</c><00:08:07.680><c> breaking</c><00:08:08.000><c> it</c>

00:08:08.149 --> 00:08:08.159 align:start position:0%
yes so this is a code it is breaking it
 

00:08:08.159 --> 00:08:09.510 align:start position:0%
yes so this is a code it is breaking it
down<00:08:08.319><c> and</c><00:08:08.479><c> all</c><00:08:08.639><c> that</c><00:08:08.840><c> stuff</c><00:08:09.080><c> you</c><00:08:09.159><c> can</c><00:08:09.360><c> of</c>

00:08:09.510 --> 00:08:09.520 align:start position:0%
down and all that stuff you can of
 

00:08:09.520 --> 00:08:12.430 align:start position:0%
down and all that stuff you can of
course<00:08:09.759><c> use</c><00:08:09.960><c> it</c><00:08:10.159><c> with</c><00:08:10.319><c> your</c><00:08:11.120><c> local</c><00:08:11.599><c> model</c><00:08:12.000><c> with</c>

00:08:12.430 --> 00:08:12.440 align:start position:0%
course use it with your local model with
 

00:08:12.440 --> 00:08:15.670 align:start position:0%
course use it with your local model with
llm<00:08:12.960><c> DM</c><00:08:13.520><c> switch</c><00:08:14.039><c> just</c><00:08:14.240><c> like</c><00:08:14.479><c> I</c><00:08:15.039><c> showed</c><00:08:15.479><c> you</c>

00:08:15.670 --> 00:08:15.680 align:start position:0%
llm DM switch just like I showed you
 

00:08:15.680 --> 00:08:17.629 align:start position:0%
llm DM switch just like I showed you
Above<00:08:16.400><c> So</c><00:08:16.520><c> if</c><00:08:16.599><c> you</c><00:08:16.759><c> think</c><00:08:16.960><c> about</c><00:08:17.199><c> it</c><00:08:17.360><c> you</c><00:08:17.440><c> can</c>

00:08:17.629 --> 00:08:17.639 align:start position:0%
Above So if you think about it you can
 

00:08:17.639 --> 00:08:20.589 align:start position:0%
Above So if you think about it you can
pass<00:08:17.840><c> it</c><00:08:18.000><c> on</c><00:08:18.280><c> any</c><00:08:18.560><c> file</c><00:08:19.240><c> like</c><00:08:19.560><c> maybe</c><00:08:19.919><c> a</c><00:08:20.080><c> text</c>

00:08:20.589 --> 00:08:20.599 align:start position:0%
pass it on any file like maybe a text
 

00:08:20.599 --> 00:08:22.950 align:start position:0%
pass it on any file like maybe a text
file<00:08:21.599><c> and</c><00:08:21.759><c> then</c><00:08:21.919><c> you</c><00:08:22.039><c> can</c><00:08:22.240><c> ask</c><00:08:22.479><c> it</c><00:08:22.639><c> to</c>

00:08:22.950 --> 00:08:22.960 align:start position:0%
file and then you can ask it to
 

00:08:22.960 --> 00:08:24.629 align:start position:0%
file and then you can ask it to
summarize<00:08:23.520><c> it</c><00:08:23.680><c> or</c><00:08:23.840><c> something</c><00:08:24.199><c> like</c><00:08:24.360><c> that</c>

00:08:24.629 --> 00:08:24.639 align:start position:0%
summarize it or something like that
 

00:08:24.639 --> 00:08:26.469 align:start position:0%
summarize it or something like that
there<00:08:24.759><c> are</c><00:08:24.919><c> lot</c><00:08:25.080><c> of</c><00:08:25.159><c> use</c><00:08:25.479><c> cases</c><00:08:25.840><c> depends</c><00:08:26.199><c> upon</c>

00:08:26.469 --> 00:08:26.479 align:start position:0%
there are lot of use cases depends upon
 

00:08:26.479 --> 00:08:29.550 align:start position:0%
there are lot of use cases depends upon
your<00:08:27.240><c> imagination</c><00:08:28.000><c> there</c><00:08:28.759><c> so</c><00:08:29.000><c> pretty</c><00:08:29.240><c> good</c>

00:08:29.550 --> 00:08:29.560 align:start position:0%
your imagination there so pretty good
 

00:08:29.560 --> 00:08:32.029 align:start position:0%
your imagination there so pretty good
tool<00:08:30.000><c> I</c><00:08:30.120><c> think</c><00:08:30.840><c> last</c><00:08:31.039><c> time</c><00:08:31.240><c> when</c><00:08:31.360><c> I</c><00:08:31.479><c> used</c><00:08:31.759><c> it</c>

00:08:32.029 --> 00:08:32.039 align:start position:0%
tool I think last time when I used it
 

00:08:32.039 --> 00:08:35.269 align:start position:0%
tool I think last time when I used it
more<00:08:32.240><c> than</c><00:08:32.519><c> a</c><00:08:32.800><c> year</c><00:08:33.039><c> ago</c><00:08:33.320><c> I</c><00:08:33.519><c> guess</c><00:08:34.279><c> it</c><00:08:34.680><c> were</c><00:08:35.039><c> few</c>

00:08:35.269 --> 00:08:35.279 align:start position:0%
more than a year ago I guess it were few
 

00:08:35.279 --> 00:08:36.750 align:start position:0%
more than a year ago I guess it were few
bucks<00:08:35.640><c> but</c><00:08:35.760><c> it</c><00:08:35.880><c> seems</c><00:08:36.159><c> that</c><00:08:36.320><c> it</c><00:08:36.519><c> has</c>

00:08:36.750 --> 00:08:36.760 align:start position:0%
bucks but it seems that it has
 

00:08:36.760 --> 00:08:39.630 align:start position:0%
bucks but it seems that it has
solidified<00:08:37.399><c> a</c><00:08:37.680><c> lot</c><00:08:38.680><c> I</c><00:08:38.760><c> will</c><00:08:38.919><c> drop</c><00:08:39.200><c> the</c><00:08:39.320><c> link</c><00:08:39.479><c> to</c>

00:08:39.630 --> 00:08:39.640 align:start position:0%
solidified a lot I will drop the link to
 

00:08:39.640 --> 00:08:41.149 align:start position:0%
solidified a lot I will drop the link to
it<00:08:39.760><c> in</c><00:08:39.919><c> video's</c><00:08:40.240><c> description</c><00:08:40.719><c> let</c><00:08:40.839><c> me</c><00:08:40.959><c> know</c>

00:08:41.149 --> 00:08:41.159 align:start position:0%
it in video's description let me know
 

00:08:41.159 --> 00:08:42.949 align:start position:0%
it in video's description let me know
what<00:08:41.240><c> do</c><00:08:41.320><c> you</c><00:08:41.519><c> think</c><00:08:42.279><c> if</c><00:08:42.399><c> you</c><00:08:42.560><c> like</c><00:08:42.760><c> the</c>

00:08:42.949 --> 00:08:42.959 align:start position:0%
what do you think if you like the
 

00:08:42.959 --> 00:08:45.190 align:start position:0%
what do you think if you like the
content<00:08:43.839><c> please</c><00:08:44.240><c> consider</c><00:08:44.640><c> subscribing</c><00:08:45.080><c> to</c>

00:08:45.190 --> 00:08:45.200 align:start position:0%
content please consider subscribing to
 

00:08:45.200 --> 00:08:46.470 align:start position:0%
content please consider subscribing to
the<00:08:45.360><c> channel</c><00:08:45.680><c> and</c><00:08:45.800><c> if</c><00:08:45.880><c> you're</c><00:08:46.120><c> already</c>

00:08:46.470 --> 00:08:46.480 align:start position:0%
the channel and if you're already
 

00:08:46.480 --> 00:08:49.150 align:start position:0%
the channel and if you're already
subscribed<00:08:47.480><c> please</c><00:08:48.160><c> um</c><00:08:48.320><c> share</c><00:08:48.560><c> it</c><00:08:48.720><c> among</c><00:08:48.959><c> your</c>

00:08:49.150 --> 00:08:49.160 align:start position:0%
subscribed please um share it among your
 

00:08:49.160 --> 00:08:51.030 align:start position:0%
subscribed please um share it among your
network<00:08:50.160><c> because</c><00:08:50.399><c> that</c><00:08:50.519><c> is</c><00:08:50.680><c> the</c><00:08:50.800><c> only</c>

00:08:51.030 --> 00:08:51.040 align:start position:0%
network because that is the only
 

00:08:51.040 --> 00:08:55.680 align:start position:0%
network because that is the only
marketing<00:08:51.560><c> I</c><00:08:51.720><c> do</c><00:08:52.120><c> thank</c><00:08:52.279><c> you</c><00:08:52.440><c> very</c><00:08:52.680><c> much</c>

