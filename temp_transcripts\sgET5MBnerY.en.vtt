WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:02.310 align:start position:0%
 
hey<00:00:00.160><c> and</c><00:00:00.280><c> welcome</c><00:00:00.520><c> back</c><00:00:00.640><c> to</c><00:00:00.880><c> day</c><00:00:01.120><c> 27</c><00:00:01.560><c> of</c><00:00:01.760><c> 31</c><00:00:02.200><c> we</c>

00:00:02.310 --> 00:00:02.320 align:start position:0%
hey and welcome back to day 27 of 31 we
 

00:00:02.320 --> 00:00:04.309 align:start position:0%
hey and welcome back to day 27 of 31 we
are<00:00:02.600><c> almost</c><00:00:02.919><c> done</c><00:00:03.199><c> but</c><00:00:03.320><c> today</c><00:00:03.679><c> I</c><00:00:03.800><c> have</c><00:00:04.080><c> a</c>

00:00:04.309 --> 00:00:04.319 align:start position:0%
are almost done but today I have a
 

00:00:04.319 --> 00:00:05.950 align:start position:0%
are almost done but today I have a
wonderful<00:00:04.799><c> update</c><00:00:05.160><c> that</c><00:00:05.279><c> came</c><00:00:05.440><c> from</c><00:00:05.600><c> LM</c>

00:00:05.950 --> 00:00:05.960 align:start position:0%
wonderful update that came from LM
 

00:00:05.960 --> 00:00:07.749 align:start position:0%
wonderful update that came from LM
Studio<00:00:06.480><c> I</c><00:00:06.600><c> talked</c><00:00:06.879><c> about</c><00:00:07.040><c> it</c><00:00:07.200><c> briefly</c><00:00:07.600><c> and</c>

00:00:07.749 --> 00:00:07.759 align:start position:0%
Studio I talked about it briefly and
 

00:00:07.759 --> 00:00:09.589 align:start position:0%
Studio I talked about it briefly and
yesterday's<00:00:08.280><c> video</c><00:00:08.719><c> but</c><00:00:08.840><c> what</c><00:00:08.960><c> I'm</c><00:00:09.240><c> talking</c>

00:00:09.589 --> 00:00:09.599 align:start position:0%
yesterday's video but what I'm talking
 

00:00:09.599 --> 00:00:12.749 align:start position:0%
yesterday's video but what I'm talking
about<00:00:09.880><c> is</c><00:00:10.080><c> multi</c><00:00:11.040><c> models</c><00:00:11.719><c> in</c><00:00:11.880><c> LM</c><00:00:12.200><c> studio</c><00:00:12.639><c> with</c>

00:00:12.749 --> 00:00:12.759 align:start position:0%
about is multi models in LM studio with
 

00:00:12.759 --> 00:00:14.669 align:start position:0%
about is multi models in LM studio with
auto<00:00:13.040><c> gem</c><00:00:13.400><c> no</c><00:00:13.559><c> I</c><00:00:13.639><c> didn't</c><00:00:13.880><c> mispronounce</c><00:00:14.519><c> that</c>

00:00:14.669 --> 00:00:14.679 align:start position:0%
auto gem no I didn't mispronounce that
 

00:00:14.679 --> 00:00:16.710 align:start position:0%
auto gem no I didn't mispronounce that
and<00:00:14.799><c> I</c><00:00:14.879><c> didn't</c><00:00:15.040><c> mean</c><00:00:15.200><c> to</c><00:00:15.280><c> say</c><00:00:15.519><c> multim</c><00:00:16.119><c> model</c><00:00:16.600><c> I</c>

00:00:16.710 --> 00:00:16.720 align:start position:0%
and I didn't mean to say multim model I
 

00:00:16.720 --> 00:00:18.790 align:start position:0%
and I didn't mean to say multim model I
meant<00:00:17.119><c> multimodel</c><00:00:18.080><c> now</c><00:00:18.240><c> what</c><00:00:18.359><c> that</c><00:00:18.439><c> means</c><00:00:18.680><c> in</c>

00:00:18.790 --> 00:00:18.800 align:start position:0%
meant multimodel now what that means in
 

00:00:18.800 --> 00:00:21.310 align:start position:0%
meant multimodel now what that means in
a<00:00:18.960><c> big</c><00:00:19.119><c> update</c><00:00:19.439><c> with</c><00:00:19.560><c> LM</c><00:00:19.880><c> Studio</c><00:00:20.359><c> recently</c><00:00:21.160><c> we</c>

00:00:21.310 --> 00:00:21.320 align:start position:0%
a big update with LM Studio recently we
 

00:00:21.320 --> 00:00:23.630 align:start position:0%
a big update with LM Studio recently we
can<00:00:21.439><c> now</c><00:00:21.720><c> have</c><00:00:22.119><c> more</c><00:00:22.320><c> than</c><00:00:22.560><c> one</c><00:00:22.880><c> model</c><00:00:23.240><c> running</c>

00:00:23.630 --> 00:00:23.640 align:start position:0%
can now have more than one model running
 

00:00:23.640 --> 00:00:25.589 align:start position:0%
can now have more than one model running
on<00:00:24.000><c> one</c><00:00:24.279><c> server</c><00:00:24.840><c> and</c><00:00:24.920><c> now</c><00:00:25.039><c> all</c><00:00:25.160><c> we</c><00:00:25.240><c> need</c><00:00:25.359><c> to</c><00:00:25.480><c> do</c>

00:00:25.589 --> 00:00:25.599 align:start position:0%
on one server and now all we need to do
 

00:00:25.599 --> 00:00:27.070 align:start position:0%
on one server and now all we need to do
is<00:00:25.720><c> adjust</c><00:00:26.000><c> the</c><00:00:26.199><c> model</c><00:00:26.519><c> property</c><00:00:26.840><c> in</c><00:00:26.920><c> the</c>

00:00:27.070 --> 00:00:27.080 align:start position:0%
is adjust the model property in the
 

00:00:27.080 --> 00:00:29.070 align:start position:0%
is adjust the model property in the
config<00:00:27.439><c> list</c><00:00:27.960><c> and</c><00:00:28.080><c> we</c><00:00:28.160><c> can</c><00:00:28.359><c> have</c><00:00:28.640><c> multiple</c>

00:00:29.070 --> 00:00:29.080 align:start position:0%
config list and we can have multiple
 

00:00:29.080 --> 00:00:30.870 align:start position:0%
config list and we can have multiple
config<00:00:29.439><c> lists</c><00:00:29.679><c> each</c><00:00:30.039><c> with</c><00:00:30.199><c> different</c><00:00:30.480><c> models</c>

00:00:30.870 --> 00:00:30.880 align:start position:0%
config lists each with different models
 

00:00:30.880 --> 00:00:32.709 align:start position:0%
config lists each with different models
loaded<00:00:31.400><c> from</c><00:00:31.560><c> LM</c><00:00:31.840><c> Studio</c><00:00:32.239><c> let's</c><00:00:32.399><c> take</c><00:00:32.520><c> a</c><00:00:32.599><c> look</c>

00:00:32.709 --> 00:00:32.719 align:start position:0%
loaded from LM Studio let's take a look
 

00:00:32.719 --> 00:00:34.030 align:start position:0%
loaded from LM Studio let's take a look
and<00:00:32.800><c> see</c><00:00:32.920><c> what</c><00:00:33.040><c> I</c><00:00:33.120><c> mean</c><00:00:33.520><c> okay</c><00:00:33.680><c> I'll</c><00:00:33.800><c> put</c><00:00:33.920><c> a</c>

00:00:34.030 --> 00:00:34.040 align:start position:0%
and see what I mean okay I'll put a
 

00:00:34.040 --> 00:00:35.389 align:start position:0%
and see what I mean okay I'll put a
video<00:00:34.239><c> in</c><00:00:34.360><c> the</c><00:00:34.480><c> description</c><00:00:34.920><c> where</c><00:00:35.079><c> I</c><00:00:35.239><c> talk</c>

00:00:35.389 --> 00:00:35.399 align:start position:0%
video in the description where I talk
 

00:00:35.399 --> 00:00:36.830 align:start position:0%
video in the description where I talk
about<00:00:35.520><c> LM</c><00:00:35.760><c> Studio</c><00:00:36.079><c> how</c><00:00:36.200><c> to</c><00:00:36.360><c> download</c><00:00:36.680><c> it</c>

00:00:36.830 --> 00:00:36.840 align:start position:0%
about LM Studio how to download it
 

00:00:36.840 --> 00:00:38.110 align:start position:0%
about LM Studio how to download it
install<00:00:37.120><c> it</c><00:00:37.200><c> and</c><00:00:37.320><c> get</c><00:00:37.399><c> it</c><00:00:37.520><c> running</c><00:00:37.800><c> and</c><00:00:38.000><c> kind</c>

00:00:38.110 --> 00:00:38.120 align:start position:0%
install it and get it running and kind
 

00:00:38.120 --> 00:00:40.069 align:start position:0%
install it and get it running and kind
of<00:00:38.440><c> working</c><00:00:38.719><c> your</c><00:00:38.879><c> way</c><00:00:39.040><c> around</c><00:00:39.280><c> it</c><00:00:39.600><c> but</c><00:00:39.760><c> in</c><00:00:39.879><c> the</c>

00:00:40.069 --> 00:00:40.079 align:start position:0%
of working your way around it but in the
 

00:00:40.079 --> 00:00:42.510 align:start position:0%
of working your way around it but in the
latest<00:00:40.600><c> update</c><00:00:41.160><c> we</c><00:00:41.280><c> have</c><00:00:41.399><c> the</c><00:00:41.559><c> multimodel</c>

00:00:42.510 --> 00:00:42.520 align:start position:0%
latest update we have the multimodel
 

00:00:42.520 --> 00:00:44.470 align:start position:0%
latest update we have the multimodel
sessions<00:00:43.000><c> where</c><00:00:43.120><c> you</c><00:00:43.200><c> can</c><00:00:43.399><c> load</c><00:00:43.800><c> impr</c><00:00:44.000><c> prompt</c>

00:00:44.470 --> 00:00:44.480 align:start position:0%
sessions where you can load impr prompt
 

00:00:44.480 --> 00:00:47.270 align:start position:0%
sessions where you can load impr prompt
multiple<00:00:45.000><c> local</c><00:00:45.360><c> models</c><00:00:46.199><c> simultaneously</c><00:00:47.120><c> so</c>

00:00:47.270 --> 00:00:47.280 align:start position:0%
multiple local models simultaneously so
 

00:00:47.280 --> 00:00:49.069 align:start position:0%
multiple local models simultaneously so
how<00:00:47.360><c> does</c><00:00:47.520><c> that</c><00:00:47.680><c> work</c><00:00:48.280><c> well</c><00:00:48.760><c> once</c><00:00:48.920><c> you</c>

00:00:49.069 --> 00:00:49.079 align:start position:0%
how does that work well once you
 

00:00:49.079 --> 00:00:50.709 align:start position:0%
how does that work well once you
download<00:00:49.480><c> and</c><00:00:49.600><c> install</c><00:00:49.879><c> it</c><00:00:50.000><c> and</c><00:00:50.120><c> run</c><00:00:50.360><c> Elm</c>

00:00:50.709 --> 00:00:50.719 align:start position:0%
download and install it and run Elm
 

00:00:50.719 --> 00:00:52.110 align:start position:0%
download and install it and run Elm
Studio<00:00:51.160><c> you'll</c><00:00:51.360><c> be</c><00:00:51.480><c> greeted</c><00:00:51.800><c> with</c><00:00:51.960><c> this</c>

00:00:52.110 --> 00:00:52.120 align:start position:0%
Studio you'll be greeted with this
 

00:00:52.120 --> 00:00:53.670 align:start position:0%
Studio you'll be greeted with this
screen<00:00:52.640><c> and</c><00:00:52.800><c> the</c><00:00:53.000><c> first</c><00:00:53.199><c> thing</c><00:00:53.320><c> you</c><00:00:53.440><c> need</c><00:00:53.559><c> to</c>

00:00:53.670 --> 00:00:53.680 align:start position:0%
screen and the first thing you need to
 

00:00:53.680 --> 00:00:55.990 align:start position:0%
screen and the first thing you need to
do<00:00:54.120><c> is</c><00:00:54.440><c> actually</c><00:00:54.840><c> download</c><00:00:55.399><c> at</c><00:00:55.520><c> least</c><00:00:55.760><c> two</c>

00:00:55.990 --> 00:00:56.000 align:start position:0%
do is actually download at least two
 

00:00:56.000 --> 00:00:58.470 align:start position:0%
do is actually download at least two
different<00:00:56.280><c> models</c><00:00:57.039><c> so</c><00:00:57.399><c> on</c><00:00:57.559><c> the</c><00:00:57.719><c> homepage</c><00:00:58.239><c> here</c>

00:00:58.470 --> 00:00:58.480 align:start position:0%
different models so on the homepage here
 

00:00:58.480 --> 00:01:00.430 align:start position:0%
different models so on the homepage here
you<00:00:58.600><c> can</c><00:00:58.800><c> just</c><00:00:59.280><c> look</c><00:00:59.440><c> at</c><00:00:59.600><c> some</c><00:00:59.760><c> of</c><00:01:00.079><c> these</c><00:01:00.239><c> here</c>

00:01:00.430 --> 00:01:00.440 align:start position:0%
you can just look at some of these here
 

00:01:00.440 --> 00:01:03.750 align:start position:0%
you can just look at some of these here
like<00:01:00.840><c> uh</c><00:01:01.079><c> like</c><00:01:01.239><c> Google's</c><00:01:01.800><c> F2</c><00:01:02.559><c> quen</c><00:01:03.440><c> um</c><00:01:03.559><c> you</c><00:01:03.640><c> can</c>

00:01:03.750 --> 00:01:03.760 align:start position:0%
like uh like Google's F2 quen um you can
 

00:01:03.760 --> 00:01:05.270 align:start position:0%
like uh like Google's F2 quen um you can
come<00:01:03.879><c> down</c><00:01:04.040><c> here</c><00:01:04.119><c> to</c><00:01:04.239><c> get</c><00:01:04.439><c> Zephyr</c><00:01:04.960><c> and</c><00:01:05.040><c> just</c><00:01:05.199><c> go</c>

00:01:05.270 --> 00:01:05.280 align:start position:0%
come down here to get Zephyr and just go
 

00:01:05.280 --> 00:01:06.990 align:start position:0%
come down here to get Zephyr and just go
ahead<00:01:05.439><c> and</c><00:01:05.560><c> download</c><00:01:05.880><c> a</c><00:01:05.960><c> couple</c><00:01:06.159><c> of</c><00:01:06.320><c> these</c><00:01:06.799><c> and</c>

00:01:06.990 --> 00:01:07.000 align:start position:0%
ahead and download a couple of these and
 

00:01:07.000 --> 00:01:09.350 align:start position:0%
ahead and download a couple of these and
then<00:01:07.479><c> there</c><00:01:07.680><c> is</c><00:01:07.960><c> a</c><00:01:08.119><c> new</c><00:01:08.360><c> tab</c><00:01:08.640><c> on</c><00:01:08.759><c> the</c><00:01:08.920><c> left</c><00:01:09.159><c> hand</c>

00:01:09.350 --> 00:01:09.360 align:start position:0%
then there is a new tab on the left hand
 

00:01:09.360 --> 00:01:11.429 align:start position:0%
then there is a new tab on the left hand
side<00:01:09.600><c> here</c><00:01:09.880><c> called</c><00:01:10.200><c> playground</c><00:01:10.840><c> so</c><00:01:11.159><c> you'll</c>

00:01:11.429 --> 00:01:11.439 align:start position:0%
side here called playground so you'll
 

00:01:11.439 --> 00:01:12.990 align:start position:0%
side here called playground so you'll
click<00:01:11.720><c> playground</c><00:01:12.320><c> you'll</c><00:01:12.479><c> see</c><00:01:12.680><c> that</c><00:01:12.840><c> they</c>

00:01:12.990 --> 00:01:13.000 align:start position:0%
click playground you'll see that they
 

00:01:13.000 --> 00:01:14.870 align:start position:0%
click playground you'll see that they
have<00:01:13.080><c> a</c><00:01:13.240><c> multimodel</c><00:01:14.080><c> session</c><00:01:14.439><c> here</c><00:01:14.680><c> just</c>

00:01:14.870 --> 00:01:14.880 align:start position:0%
have a multimodel session here just
 

00:01:14.880 --> 00:01:16.630 align:start position:0%
have a multimodel session here just
click<00:01:15.200><c> go</c><00:01:15.680><c> and</c><00:01:15.759><c> now</c><00:01:15.960><c> what</c><00:01:16.040><c> we</c><00:01:16.119><c> can</c><00:01:16.200><c> do</c><00:01:16.320><c> is</c><00:01:16.439><c> load</c>

00:01:16.630 --> 00:01:16.640 align:start position:0%
click go and now what we can do is load
 

00:01:16.640 --> 00:01:18.070 align:start position:0%
click go and now what we can do is load
a<00:01:16.720><c> couple</c><00:01:17.000><c> models</c><00:01:17.320><c> so</c><00:01:17.520><c> up</c><00:01:17.640><c> here</c><00:01:17.759><c> where</c><00:01:17.840><c> it</c><00:01:17.920><c> says</c>

00:01:18.070 --> 00:01:18.080 align:start position:0%
a couple models so up here where it says
 

00:01:18.080 --> 00:01:20.390 align:start position:0%
a couple models so up here where it says
select<00:01:18.360><c> models</c><00:01:18.640><c> to</c><00:01:18.799><c> load</c><00:01:19.479><c> choose</c><00:01:19.880><c> this</c><00:01:20.200><c> and</c>

00:01:20.390 --> 00:01:20.400 align:start position:0%
select models to load choose this and
 

00:01:20.400 --> 00:01:22.510 align:start position:0%
select models to load choose this and
I'm<00:01:20.520><c> going</c><00:01:20.640><c> to</c><00:01:20.799><c> choose</c><00:01:21.119><c> 5</c><00:01:21.400><c> 2</c><00:01:21.759><c> first</c><00:01:22.240><c> once</c><00:01:22.439><c> that</c>

00:01:22.510 --> 00:01:22.520 align:start position:0%
I'm going to choose 5 2 first once that
 

00:01:22.520 --> 00:01:24.109 align:start position:0%
I'm going to choose 5 2 first once that
one's<00:01:22.720><c> done</c><00:01:23.000><c> and</c><00:01:23.159><c> choose</c><00:01:23.439><c> another</c><00:01:23.680><c> one</c><00:01:24.000><c> I'm</c>

00:01:24.109 --> 00:01:24.119 align:start position:0%
one's done and choose another one I'm
 

00:01:24.119 --> 00:01:25.910 align:start position:0%
one's done and choose another one I'm
going<00:01:24.240><c> to</c><00:01:24.360><c> choose</c><00:01:24.600><c> stable</c><00:01:24.960><c> LM</c><00:01:25.320><c> Zephyr</c><00:01:25.759><c> cuz</c>

00:01:25.910 --> 00:01:25.920 align:start position:0%
going to choose stable LM Zephyr cuz
 

00:01:25.920 --> 00:01:27.749 align:start position:0%
going to choose stable LM Zephyr cuz
it's<00:01:26.079><c> also</c><00:01:26.280><c> a</c><00:01:26.439><c> smaller</c><00:01:26.799><c> model</c><00:01:27.320><c> okay</c><00:01:27.520><c> and</c><00:01:27.640><c> once</c>

00:01:27.749 --> 00:01:27.759 align:start position:0%
it's also a smaller model okay and once
 

00:01:27.759 --> 00:01:29.469 align:start position:0%
it's also a smaller model okay and once
you<00:01:27.960><c> have</c><00:01:28.159><c> that</c><00:01:28.320><c> done</c><00:01:28.759><c> then</c><00:01:28.920><c> just</c><00:01:29.119><c> come</c><00:01:29.320><c> over</c>

00:01:29.469 --> 00:01:29.479 align:start position:0%
you have that done then just come over
 

00:01:29.479 --> 00:01:30.830 align:start position:0%
you have that done then just come over
here<00:01:29.600><c> on</c><00:01:29.680><c> the</c><00:01:29.920><c> left</c><00:01:30.119><c> hand</c><00:01:30.280><c> side</c><00:01:30.439><c> and</c><00:01:30.600><c> click</c>

00:01:30.830 --> 00:01:30.840 align:start position:0%
here on the left hand side and click
 

00:01:30.840 --> 00:01:32.510 align:start position:0%
here on the left hand side and click
Start<00:01:31.159><c> server</c><00:01:31.640><c> and</c><00:01:31.759><c> we're</c><00:01:31.880><c> up</c><00:01:32.000><c> and</c><00:01:32.119><c> running</c>

00:01:32.510 --> 00:01:32.520 align:start position:0%
Start server and we're up and running
 

00:01:32.520 --> 00:01:33.950 align:start position:0%
Start server and we're up and running
now<00:01:32.640><c> let's</c><00:01:32.799><c> create</c><00:01:33.000><c> an</c><00:01:33.079><c> autogen</c><00:01:33.560><c> file</c><00:01:33.840><c> where</c>

00:01:33.950 --> 00:01:33.960 align:start position:0%
now let's create an autogen file where
 

00:01:33.960 --> 00:01:35.710 align:start position:0%
now let's create an autogen file where
we<00:01:34.079><c> can</c><00:01:34.240><c> see</c><00:01:34.799><c> how</c><00:01:34.960><c> we</c><00:01:35.119><c> have</c><00:01:35.320><c> two</c><00:01:35.479><c> different</c>

00:01:35.710 --> 00:01:35.720 align:start position:0%
we can see how we have two different
 

00:01:35.720 --> 00:01:37.870 align:start position:0%
we can see how we have two different
models<00:01:36.079><c> from</c><00:01:36.280><c> one</c><00:01:36.520><c> LM</c><00:01:36.920><c> Studio</c><00:01:37.360><c> software</c>

00:01:37.870 --> 00:01:37.880 align:start position:0%
models from one LM Studio software
 

00:01:37.880 --> 00:01:39.429 align:start position:0%
models from one LM Studio software
running<00:01:38.240><c> at</c><00:01:38.320><c> the</c><00:01:38.439><c> same</c><00:01:38.680><c> time</c><00:01:39.079><c> and</c><00:01:39.240><c> how</c><00:01:39.320><c> we</c>

00:01:39.429 --> 00:01:39.439 align:start position:0%
running at the same time and how we
 

00:01:39.439 --> 00:01:41.270 align:start position:0%
running at the same time and how we
distinguish<00:01:39.920><c> them</c><00:01:40.119><c> with</c><00:01:40.240><c> the</c><00:01:40.399><c> agents</c><00:01:41.000><c> so</c><00:01:41.159><c> what</c>

00:01:41.270 --> 00:01:41.280 align:start position:0%
distinguish them with the agents so what
 

00:01:41.280 --> 00:01:43.310 align:start position:0%
distinguish them with the agents so what
we'll<00:01:41.520><c> have</c><00:01:41.799><c> is</c><00:01:42.040><c> two</c><00:01:42.200><c> different</c><00:01:42.439><c> llm</c><00:01:42.880><c> configs</c>

00:01:43.310 --> 00:01:43.320 align:start position:0%
we'll have is two different llm configs
 

00:01:43.320 --> 00:01:44.630 align:start position:0%
we'll have is two different llm configs
I'm<00:01:43.399><c> going</c><00:01:43.520><c> to</c><00:01:43.600><c> name</c><00:01:43.799><c> the</c><00:01:43.920><c> first</c><00:01:44.119><c> one</c><00:01:44.280><c> Zephyr</c>

00:01:44.630 --> 00:01:44.640 align:start position:0%
I'm going to name the first one Zephyr
 

00:01:44.640 --> 00:01:46.950 align:start position:0%
I'm going to name the first one Zephyr
and<00:01:44.759><c> the</c><00:01:44.880><c> second</c><00:01:45.119><c> one</c><00:01:45.280><c> 5</c><00:01:45.600><c> 2</c><00:01:46.479><c> now</c><00:01:46.680><c> how</c><00:01:46.799><c> we</c>

00:01:46.950 --> 00:01:46.960 align:start position:0%
and the second one 5 2 now how we
 

00:01:46.960 --> 00:01:49.230 align:start position:0%
and the second one 5 2 now how we
distinguish<00:01:47.439><c> them</c><00:01:47.719><c> is</c><00:01:47.920><c> in</c><00:01:48.040><c> our</c><00:01:48.280><c> config</c><00:01:48.719><c> list</c>

00:01:49.230 --> 00:01:49.240 align:start position:0%
distinguish them is in our config list
 

00:01:49.240 --> 00:01:51.670 align:start position:0%
distinguish them is in our config list
we<00:01:49.360><c> have</c><00:01:49.479><c> a</c><00:01:49.600><c> model</c><00:01:50.000><c> base</c><00:01:50.280><c> URL</c><00:01:50.600><c> and</c><00:01:50.759><c> API</c><00:01:51.159><c> key</c>

00:01:51.670 --> 00:01:51.680 align:start position:0%
we have a model base URL and API key
 

00:01:51.680 --> 00:01:53.590 align:start position:0%
we have a model base URL and API key
with<00:01:51.799><c> the</c><00:01:51.920><c> new</c><00:01:52.040><c> autogen</c><00:01:52.560><c> update</c><00:01:53.119><c> we</c><00:01:53.200><c> can</c><00:01:53.320><c> say</c>

00:01:53.590 --> 00:01:53.600 align:start position:0%
with the new autogen update we can say
 

00:01:53.600 --> 00:01:55.550 align:start position:0%
with the new autogen update we can say
lm-<00:01:54.159><c> studio</c><00:01:54.479><c> for</c><00:01:54.600><c> the</c><00:01:54.719><c> API</c><00:01:55.079><c> key</c><00:01:55.280><c> so</c><00:01:55.439><c> it</c>

00:01:55.550 --> 00:01:55.560 align:start position:0%
lm- studio for the API key so it
 

00:01:55.560 --> 00:01:57.950 align:start position:0%
lm- studio for the API key so it
recognizes<00:01:56.159><c> LM</c><00:01:56.439><c> Studio</c><00:01:57.240><c> then</c><00:01:57.360><c> we</c><00:01:57.479><c> have</c><00:01:57.560><c> a</c><00:01:57.719><c> base</c>

00:01:57.950 --> 00:01:57.960 align:start position:0%
recognizes LM Studio then we have a base
 

00:01:57.960 --> 00:02:00.230 align:start position:0%
recognizes LM Studio then we have a base
URL<00:01:58.759><c> this</c><00:01:58.880><c> is</c><00:01:59.159><c> the</c><00:01:59.280><c> same</c><00:01:59.560><c> this</c><00:01:59.960><c> always</c><00:02:00.119><c> been</c>

00:02:00.230 --> 00:02:00.240 align:start position:0%
URL this is the same this always been
 

00:02:00.240 --> 00:02:02.510 align:start position:0%
URL this is the same this always been
the<00:02:00.320><c> same</c><00:02:00.439><c> for</c><00:02:00.560><c> LM</c><00:02:00.840><c> studio</c><00:02:01.799><c> now</c><00:02:02.000><c> we</c><00:02:02.119><c> have</c><00:02:02.280><c> a</c>

00:02:02.510 --> 00:02:02.520 align:start position:0%
the same for LM studio now we have a
 

00:02:02.520 --> 00:02:04.270 align:start position:0%
the same for LM studio now we have a
model<00:02:03.079><c> name</c><00:02:03.399><c> really</c><00:02:03.640><c> this</c><00:02:03.719><c> is</c><00:02:03.759><c> a</c><00:02:03.880><c> model</c>

00:02:04.270 --> 00:02:04.280 align:start position:0%
model name really this is a model
 

00:02:04.280 --> 00:02:06.830 align:start position:0%
model name really this is a model
identifier<00:02:05.200><c> from</c><00:02:05.439><c> the</c><00:02:05.560><c> model</c><00:02:05.799><c> that</c><00:02:05.920><c> we</c><00:02:06.079><c> loaded</c>

00:02:06.830 --> 00:02:06.840 align:start position:0%
identifier from the model that we loaded
 

00:02:06.840 --> 00:02:07.950 align:start position:0%
identifier from the model that we loaded
right<00:02:06.960><c> so</c><00:02:07.119><c> I'll</c><00:02:07.240><c> show</c><00:02:07.360><c> you</c><00:02:07.479><c> where</c><00:02:07.560><c> to</c><00:02:07.719><c> find</c>

00:02:07.950 --> 00:02:07.960 align:start position:0%
right so I'll show you where to find
 

00:02:07.960 --> 00:02:09.389 align:start position:0%
right so I'll show you where to find
this<00:02:08.119><c> in</c><00:02:08.239><c> a</c><00:02:08.319><c> minute</c><00:02:08.679><c> whichever</c><00:02:09.039><c> assistant</c>

00:02:09.389 --> 00:02:09.399 align:start position:0%
this in a minute whichever assistant
 

00:02:09.399 --> 00:02:11.750 align:start position:0%
this in a minute whichever assistant
agent<00:02:09.759><c> has</c><00:02:09.920><c> the</c><00:02:10.039><c> Zephyr</c><00:02:10.479><c> as</c><00:02:10.560><c> the</c><00:02:10.679><c> llm</c><00:02:11.120><c> config</c>

00:02:11.750 --> 00:02:11.760 align:start position:0%
agent has the Zephyr as the llm config
 

00:02:11.760 --> 00:02:13.470 align:start position:0%
agent has the Zephyr as the llm config
definition<00:02:12.520><c> they're</c><00:02:12.720><c> going</c><00:02:12.800><c> to</c><00:02:13.040><c> use</c><00:02:13.360><c> the</c>

00:02:13.470 --> 00:02:13.480 align:start position:0%
definition they're going to use the
 

00:02:13.480 --> 00:02:15.910 align:start position:0%
definition they're going to use the
Zephyr<00:02:13.920><c> model</c><00:02:14.280><c> now</c><00:02:14.400><c> for</c><00:02:14.640><c> 5</c><00:02:14.920><c> 2</c><00:02:15.160><c> another</c><00:02:15.440><c> llm</c>

00:02:15.910 --> 00:02:15.920 align:start position:0%
Zephyr model now for 5 2 another llm
 

00:02:15.920 --> 00:02:17.869 align:start position:0%
Zephyr model now for 5 2 another llm
config<00:02:16.599><c> we</c><00:02:16.760><c> have</c><00:02:16.879><c> the</c><00:02:17.000><c> config</c><00:02:17.360><c> list</c><00:02:17.640><c> and</c><00:02:17.760><c> then</c>

00:02:17.869 --> 00:02:17.879 align:start position:0%
config we have the config list and then
 

00:02:17.879 --> 00:02:19.630 align:start position:0%
config we have the config list and then
the<00:02:18.000><c> model</c><00:02:18.360><c> this</c><00:02:18.480><c> is</c><00:02:18.599><c> the</c><00:02:18.760><c> identifier</c><00:02:19.319><c> for</c><00:02:19.519><c> the</c>

00:02:19.630 --> 00:02:19.640 align:start position:0%
the model this is the identifier for the
 

00:02:19.640 --> 00:02:22.030 align:start position:0%
the model this is the identifier for the
f<00:02:19.959><c> 2</c><00:02:20.200><c> model</c><00:02:20.800><c> then</c><00:02:20.920><c> we</c><00:02:21.000><c> have</c><00:02:21.120><c> the</c><00:02:21.239><c> base</c><00:02:21.480><c> URL</c>

00:02:22.030 --> 00:02:22.040 align:start position:0%
f 2 model then we have the base URL
 

00:02:22.040 --> 00:02:23.750 align:start position:0%
f 2 model then we have the base URL
which<00:02:22.160><c> is</c><00:02:22.319><c> the</c><00:02:22.440><c> same</c><00:02:22.760><c> and</c><00:02:22.920><c> the</c><00:02:23.040><c> API</c><00:02:23.440><c> key</c><00:02:23.640><c> which</c>

00:02:23.750 --> 00:02:23.760 align:start position:0%
which is the same and the API key which
 

00:02:23.760 --> 00:02:26.630 align:start position:0%
which is the same and the API key which
is<00:02:23.959><c> also</c><00:02:24.200><c> the</c><00:02:24.319><c> same</c><00:02:25.000><c> also</c><00:02:25.879><c> uh</c><00:02:26.040><c> I</c><00:02:26.319><c> might</c><00:02:26.440><c> not</c>

00:02:26.630 --> 00:02:26.640 align:start position:0%
is also the same also uh I might not
 

00:02:26.640 --> 00:02:27.949 align:start position:0%
is also the same also uh I might not
mentioned<00:02:26.879><c> this</c><00:02:27.040><c> before</c><00:02:27.319><c> but</c><00:02:27.400><c> for</c><00:02:27.560><c> the</c><00:02:27.720><c> cache</c>

00:02:27.949 --> 00:02:27.959 align:start position:0%
mentioned this before but for the cache
 

00:02:27.959 --> 00:02:30.350 align:start position:0%
mentioned this before but for the cache
scene<00:02:28.560><c> you</c><00:02:28.680><c> can</c><00:02:28.879><c> set</c><00:02:29.120><c> this</c><00:02:29.239><c> to</c><00:02:29.440><c> none</c><00:02:30.040><c> meaning</c>

00:02:30.350 --> 00:02:30.360 align:start position:0%
scene you can set this to none meaning
 

00:02:30.360 --> 00:02:32.229 align:start position:0%
scene you can set this to none meaning
that<00:02:30.480><c> it</c><00:02:30.560><c> will</c><00:02:30.800><c> never</c><00:02:31.160><c> cash</c><00:02:31.400><c> your</c><00:02:31.560><c> results</c><00:02:31.920><c> and</c>

00:02:32.229 --> 00:02:32.239 align:start position:0%
that it will never cash your results and
 

00:02:32.239 --> 00:02:33.750 align:start position:0%
that it will never cash your results and
every<00:02:32.440><c> time</c><00:02:32.560><c> you</c><00:02:32.720><c> run</c><00:02:32.959><c> this</c><00:02:33.120><c> it'll</c><00:02:33.400><c> always</c><00:02:33.599><c> be</c>

00:02:33.750 --> 00:02:33.760 align:start position:0%
every time you run this it'll always be
 

00:02:33.760 --> 00:02:35.350 align:start position:0%
every time you run this it'll always be
different<00:02:34.239><c> I</c><00:02:34.319><c> have</c><00:02:34.440><c> two</c><00:02:34.599><c> agents</c><00:02:35.000><c> I</c><00:02:35.080><c> have</c><00:02:35.200><c> one</c>

00:02:35.350 --> 00:02:35.360 align:start position:0%
different I have two agents I have one
 

00:02:35.360 --> 00:02:36.990 align:start position:0%
different I have two agents I have one
named<00:02:35.599><c> Phil</c><00:02:35.879><c> who's</c><00:02:36.040><c> going</c><00:02:36.160><c> to</c><00:02:36.200><c> be</c><00:02:36.280><c> using</c><00:02:36.519><c> F2</c>

00:02:36.990 --> 00:02:37.000 align:start position:0%
named Phil who's going to be using F2
 

00:02:37.000 --> 00:02:38.550 align:start position:0%
named Phil who's going to be using F2
model<00:02:37.519><c> and</c><00:02:37.640><c> I</c><00:02:37.720><c> have</c><00:02:37.879><c> one</c><00:02:38.040><c> named</c><00:02:38.200><c> Zep</c><00:02:38.440><c> who's</c>

00:02:38.550 --> 00:02:38.560 align:start position:0%
model and I have one named Zep who's
 

00:02:38.560 --> 00:02:39.990 align:start position:0%
model and I have one named Zep who's
going<00:02:38.640><c> to</c><00:02:38.680><c> be</c><00:02:38.760><c> using</c><00:02:38.959><c> the</c><00:02:39.080><c> Zephyr</c><00:02:39.440><c> model</c><00:02:39.840><c> and</c>

00:02:39.990 --> 00:02:40.000 align:start position:0%
going to be using the Zephyr model and
 

00:02:40.000 --> 00:02:41.990 align:start position:0%
going to be using the Zephyr model and
then<00:02:40.280><c> I</c><00:02:40.360><c> just</c><00:02:40.519><c> have</c><00:02:40.760><c> Phil</c><00:02:41.200><c> initiate</c><00:02:41.560><c> a</c><00:02:41.720><c> chat</c>

00:02:41.990 --> 00:02:42.000 align:start position:0%
then I just have Phil initiate a chat
 

00:02:42.000 --> 00:02:44.830 align:start position:0%
then I just have Phil initiate a chat
with<00:02:42.159><c> Zep</c><00:02:42.840><c> saying</c><00:02:43.239><c> tell</c><00:02:43.440><c> me</c><00:02:43.560><c> a</c><00:02:43.720><c> joke</c><00:02:44.400><c> that's</c><00:02:44.560><c> it</c>

00:02:44.830 --> 00:02:44.840 align:start position:0%
with Zep saying tell me a joke that's it
 

00:02:44.840 --> 00:02:46.430 align:start position:0%
with Zep saying tell me a joke that's it
really<00:02:45.080><c> simple</c><00:02:45.560><c> but</c><00:02:45.720><c> this</c><00:02:45.840><c> is</c><00:02:45.959><c> going</c><00:02:46.080><c> to</c><00:02:46.200><c> show</c>

00:02:46.430 --> 00:02:46.440 align:start position:0%
really simple but this is going to show
 

00:02:46.440 --> 00:02:48.589 align:start position:0%
really simple but this is going to show
you<00:02:46.800><c> how</c><00:02:47.239><c> to</c><00:02:47.440><c> have</c><00:02:47.599><c> multiple</c><00:02:47.920><c> models</c><00:02:48.239><c> working</c>

00:02:48.589 --> 00:02:48.599 align:start position:0%
you how to have multiple models working
 

00:02:48.599 --> 00:02:50.430 align:start position:0%
you how to have multiple models working
together<00:02:48.879><c> in</c><00:02:49.040><c> one</c><00:02:49.239><c> LM</c><00:02:49.519><c> Studio</c><00:02:49.840><c> software</c><00:02:50.280><c> now</c>

00:02:50.430 --> 00:02:50.440 align:start position:0%
together in one LM Studio software now
 

00:02:50.440 --> 00:02:52.509 align:start position:0%
together in one LM Studio software now
as<00:02:50.560><c> I</c><00:02:50.680><c> said</c><00:02:51.040><c> we</c><00:02:51.159><c> have</c><00:02:51.280><c> to</c><00:02:51.400><c> get</c><00:02:51.560><c> this</c><00:02:51.840><c> identify</c>

00:02:52.509 --> 00:02:52.519 align:start position:0%
as I said we have to get this identify
 

00:02:52.519 --> 00:02:53.869 align:start position:0%
as I said we have to get this identify
here<00:02:52.760><c> let's</c><00:02:52.879><c> go</c><00:02:53.000><c> over</c><00:02:53.080><c> to</c><00:02:53.159><c> LM</c><00:02:53.400><c> studio</c><00:02:53.680><c> and</c><00:02:53.760><c> I'll</c>

00:02:53.869 --> 00:02:53.879 align:start position:0%
here let's go over to LM studio and I'll
 

00:02:53.879 --> 00:02:55.149 align:start position:0%
here let's go over to LM studio and I'll
show<00:02:54.040><c> you</c><00:02:54.159><c> where</c><00:02:54.280><c> to</c><00:02:54.440><c> get</c><00:02:54.560><c> that</c><00:02:54.760><c> so</c><00:02:54.959><c> back</c><00:02:55.040><c> at</c>

00:02:55.149 --> 00:02:55.159 align:start position:0%
show you where to get that so back at
 

00:02:55.159 --> 00:02:56.750 align:start position:0%
show you where to get that so back at
Elm<00:02:55.400><c> Studio</c><00:02:55.800><c> if</c><00:02:55.879><c> you</c><00:02:56.080><c> click</c><00:02:56.360><c> for</c><00:02:56.480><c> instance</c>

00:02:56.750 --> 00:02:56.760 align:start position:0%
Elm Studio if you click for instance
 

00:02:56.760 --> 00:02:58.670 align:start position:0%
Elm Studio if you click for instance
let's<00:02:56.920><c> just</c><00:02:57.080><c> click</c><00:02:57.319><c> 5</c><00:02:57.599><c> 2</c><00:02:58.200><c> over</c><00:02:58.400><c> here</c><00:02:58.560><c> there's</c>

00:02:58.670 --> 00:02:58.680 align:start position:0%
let's just click 5 2 over here there's
 

00:02:58.680 --> 00:03:01.110 align:start position:0%
let's just click 5 2 over here there's
an<00:02:58.879><c> API</c><00:02:59.280><c> model</c><00:02:59.800><c> identifier</c><00:03:00.440><c> you</c><00:03:00.560><c> can</c><00:03:00.760><c> just</c>

00:03:01.110 --> 00:03:01.120 align:start position:0%
an API model identifier you can just
 

00:03:01.120 --> 00:03:03.470 align:start position:0%
an API model identifier you can just
copy<00:03:01.480><c> this</c><00:03:01.879><c> and</c><00:03:02.000><c> the</c><00:03:02.159><c> same</c><00:03:02.360><c> thing</c><00:03:02.519><c> for</c><00:03:02.720><c> Zephyr</c>

00:03:03.470 --> 00:03:03.480 align:start position:0%
copy this and the same thing for Zephyr
 

00:03:03.480 --> 00:03:05.270 align:start position:0%
copy this and the same thing for Zephyr
you<00:03:03.599><c> can</c><00:03:03.840><c> just</c><00:03:04.239><c> look</c><00:03:04.400><c> at</c><00:03:04.519><c> the</c><00:03:04.640><c> API</c><00:03:05.040><c> model</c>

00:03:05.270 --> 00:03:05.280 align:start position:0%
you can just look at the API model
 

00:03:05.280 --> 00:03:07.070 align:start position:0%
you can just look at the API model
identifier<00:03:05.799><c> and</c><00:03:05.879><c> then</c><00:03:06.080><c> copy</c><00:03:06.440><c> that</c><00:03:06.640><c> as</c><00:03:06.799><c> well</c>

00:03:07.070 --> 00:03:07.080 align:start position:0%
identifier and then copy that as well
 

00:03:07.080 --> 00:03:09.030 align:start position:0%
identifier and then copy that as well
all<00:03:07.200><c> right</c><00:03:07.319><c> after</c><00:03:07.560><c> I</c><00:03:07.799><c> ran</c><00:03:08.040><c> it</c><00:03:08.440><c> as</c><00:03:08.640><c> we</c><00:03:08.720><c> can</c><00:03:08.840><c> see</c>

00:03:09.030 --> 00:03:09.040 align:start position:0%
all right after I ran it as we can see
 

00:03:09.040 --> 00:03:10.910 align:start position:0%
all right after I ran it as we can see
here<00:03:09.239><c> this</c><00:03:09.480><c> this</c><00:03:09.599><c> is</c><00:03:09.720><c> the</c><00:03:09.840><c> server</c><00:03:10.159><c> logs</c><00:03:10.680><c> for</c>

00:03:10.910 --> 00:03:10.920 align:start position:0%
here this this is the server logs for
 

00:03:10.920 --> 00:03:12.949 align:start position:0%
here this this is the server logs for
both<00:03:11.120><c> the</c><00:03:11.239><c> models</c><00:03:11.879><c> and</c><00:03:12.120><c> I</c><00:03:12.200><c> mean</c><00:03:12.319><c> it</c><00:03:12.480><c> worked</c>

00:03:12.949 --> 00:03:12.959 align:start position:0%
both the models and I mean it worked
 

00:03:12.959 --> 00:03:15.110 align:start position:0%
both the models and I mean it worked
right<00:03:13.120><c> here's</c><00:03:13.599><c> is</c><00:03:14.280><c> accumulating</c><00:03:14.799><c> all</c><00:03:14.920><c> the</c>

00:03:15.110 --> 00:03:15.120 align:start position:0%
right here's is accumulating all the
 

00:03:15.120 --> 00:03:18.430 align:start position:0%
right here's is accumulating all the
tokens<00:03:15.959><c> for</c><00:03:16.360><c> um</c><00:03:16.480><c> the</c><00:03:16.760><c> responses</c><00:03:17.760><c> back</c><00:03:17.920><c> to</c><00:03:18.159><c> each</c>

00:03:18.430 --> 00:03:18.440 align:start position:0%
tokens for um the responses back to each
 

00:03:18.440 --> 00:03:20.750 align:start position:0%
tokens for um the responses back to each
other<00:03:19.440><c> uh</c><00:03:19.640><c> but</c><00:03:19.799><c> here</c><00:03:19.920><c> we</c><00:03:20.080><c> go</c><00:03:20.280><c> like</c><00:03:20.400><c> so</c><00:03:20.599><c> it</c>

00:03:20.750 --> 00:03:20.760 align:start position:0%
other uh but here we go like so it
 

00:03:20.760 --> 00:03:22.830 align:start position:0%
other uh but here we go like so it
worked<00:03:21.239><c> right</c><00:03:21.319><c> so</c><00:03:21.480><c> this</c><00:03:21.599><c> is</c><00:03:21.720><c> just</c><00:03:22.200><c> you</c><00:03:22.319><c> can</c><00:03:22.720><c> go</c>

00:03:22.830 --> 00:03:22.840 align:start position:0%
worked right so this is just you can go
 

00:03:22.840 --> 00:03:25.630 align:start position:0%
worked right so this is just you can go
back<00:03:22.959><c> to</c><00:03:23.040><c> LM</c><00:03:23.319><c> Studio</c><00:03:24.159><c> look</c><00:03:24.440><c> and</c><00:03:24.560><c> you</c><00:03:24.680><c> can</c><00:03:24.879><c> see</c>

00:03:25.630 --> 00:03:25.640 align:start position:0%
back to LM Studio look and you can see
 

00:03:25.640 --> 00:03:27.470 align:start position:0%
back to LM Studio look and you can see
uh<00:03:25.959><c> how</c><00:03:26.120><c> the</c><00:03:26.239><c> interaction</c><00:03:26.720><c> went</c><00:03:27.120><c> okay</c><00:03:27.280><c> now</c><00:03:27.400><c> if</c>

00:03:27.470 --> 00:03:27.480 align:start position:0%
uh how the interaction went okay now if
 

00:03:27.480 --> 00:03:29.229 align:start position:0%
uh how the interaction went okay now if
we<00:03:27.560><c> go</c><00:03:27.760><c> back</c><00:03:27.879><c> to</c><00:03:27.959><c> our</c><00:03:28.159><c> IDE</c><00:03:28.720><c> and</c><00:03:28.840><c> look</c><00:03:29.000><c> at</c><00:03:29.120><c> what</c>

00:03:29.229 --> 00:03:29.239 align:start position:0%
we go back to our IDE and look at what
 

00:03:29.239 --> 00:03:30.990 align:start position:0%
we go back to our IDE and look at what
happened<00:03:29.519><c> here</c><00:03:30.000><c> we</c><00:03:30.159><c> say</c><00:03:30.360><c> Phil</c><00:03:30.680><c> started</c>

00:03:30.990 --> 00:03:31.000 align:start position:0%
happened here we say Phil started
 

00:03:31.000 --> 00:03:33.350 align:start position:0%
happened here we say Phil started
talking<00:03:31.239><c> to</c><00:03:31.400><c> Zep</c><00:03:31.799><c> tell</c><00:03:32.000><c> me</c><00:03:32.120><c> a</c><00:03:32.319><c> joke</c><00:03:33.040><c> why</c><00:03:33.159><c> did</c>

00:03:33.350 --> 00:03:33.360 align:start position:0%
talking to Zep tell me a joke why did
 

00:03:33.360 --> 00:03:35.350 align:start position:0%
talking to Zep tell me a joke why did
the<00:03:33.480><c> tomato</c><00:03:33.840><c> turn</c><00:03:34.159><c> red</c><00:03:34.519><c> because</c><00:03:34.760><c> it</c><00:03:34.879><c> saw</c><00:03:35.200><c> the</c>

00:03:35.350 --> 00:03:35.360 align:start position:0%
the tomato turn red because it saw the
 

00:03:35.360 --> 00:03:37.630 align:start position:0%
the tomato turn red because it saw the
salad<00:03:35.799><c> dressing</c><00:03:36.799><c> haha</c><00:03:37.400><c> They</c><00:03:37.480><c> had</c><00:03:37.560><c> the</c>

00:03:37.630 --> 00:03:37.640 align:start position:0%
salad dressing haha They had the
 

00:03:37.640 --> 00:03:40.390 align:start position:0%
salad dressing haha They had the
audience<00:03:38.040><c> laughing</c><00:03:39.040><c> great</c><00:03:39.519><c> okay</c><00:03:39.840><c> awesome</c>

00:03:40.390 --> 00:03:40.400 align:start position:0%
audience laughing great okay awesome
 

00:03:40.400 --> 00:03:41.990 align:start position:0%
audience laughing great okay awesome
what<00:03:40.599><c> happened</c><00:03:40.920><c> here</c><00:03:41.200><c> okay</c><00:03:41.319><c> so</c><00:03:41.560><c> again</c><00:03:41.760><c> let's</c>

00:03:41.990 --> 00:03:42.000 align:start position:0%
what happened here okay so again let's
 

00:03:42.000 --> 00:03:44.030 align:start position:0%
what happened here okay so again let's
review<00:03:42.400><c> what</c><00:03:42.599><c> just</c><00:03:42.840><c> happened</c><00:03:43.280><c> we</c><00:03:43.439><c> had</c><00:03:43.720><c> two</c>

00:03:44.030 --> 00:03:44.040 align:start position:0%
review what just happened we had two
 

00:03:44.040 --> 00:03:46.670 align:start position:0%
review what just happened we had two
separate<00:03:44.400><c> models</c><00:03:45.120><c> working</c><00:03:45.480><c> on</c><00:03:45.920><c> one</c><00:03:46.280><c> Elm</c>

00:03:46.670 --> 00:03:46.680 align:start position:0%
separate models working on one Elm
 

00:03:46.680 --> 00:03:48.670 align:start position:0%
separate models working on one Elm
Studio<00:03:47.120><c> software</c><00:03:47.560><c> running</c><00:03:48.120><c> it</c><00:03:48.200><c> was</c><00:03:48.400><c> open</c>

00:03:48.670 --> 00:03:48.680 align:start position:0%
Studio software running it was open
 

00:03:48.680 --> 00:03:51.030 align:start position:0%
Studio software running it was open
source<00:03:49.239><c> it</c><00:03:49.360><c> was</c><00:03:49.560><c> free</c><00:03:50.319><c> we</c><00:03:50.439><c> didn't</c><00:03:50.680><c> have</c><00:03:50.799><c> to</c>

00:03:51.030 --> 00:03:51.040 align:start position:0%
source it was free we didn't have to
 

00:03:51.040 --> 00:03:54.110 align:start position:0%
source it was free we didn't have to
worry<00:03:51.280><c> about</c><00:03:51.760><c> open</c><00:03:52.120><c> ai's</c><00:03:52.599><c> API</c><00:03:53.159><c> key</c><00:03:53.760><c> and</c><00:03:54.000><c> they</c>

00:03:54.110 --> 00:03:54.120 align:start position:0%
worry about open ai's API key and they
 

00:03:54.120 --> 00:03:55.949 align:start position:0%
worry about open ai's API key and they
could<00:03:54.360><c> talk</c><00:03:54.599><c> to</c><00:03:54.840><c> each</c><00:03:55.000><c> other</c><00:03:55.439><c> I</c><00:03:55.519><c> think</c><00:03:55.840><c> this</c>

00:03:55.949 --> 00:03:55.959 align:start position:0%
could talk to each other I think this
 

00:03:55.959 --> 00:03:57.670 align:start position:0%
could talk to each other I think this
was<00:03:56.159><c> a</c><00:03:56.400><c> huge</c><00:03:56.680><c> update</c><00:03:57.000><c> and</c><00:03:57.079><c> I</c><00:03:57.159><c> think</c><00:03:57.319><c> this</c><00:03:57.439><c> is</c>

00:03:57.670 --> 00:03:57.680 align:start position:0%
was a huge update and I think this is
 

00:03:57.680 --> 00:03:58.830 align:start position:0%
was a huge update and I think this is
really<00:03:57.879><c> going</c><00:03:58.000><c> to</c><00:03:58.079><c> help</c><00:03:58.239><c> out</c><00:03:58.439><c> especially</c><00:03:58.760><c> if</c>

00:03:58.830 --> 00:03:58.840 align:start position:0%
really going to help out especially if
 

00:03:58.840 --> 00:04:00.429 align:start position:0%
really going to help out especially if
you<00:03:58.920><c> liked</c><00:03:59.159><c> Elm</c><00:03:59.400><c> Studio</c><00:03:59.879><c> or</c><00:04:00.000><c> if</c><00:04:00.079><c> you</c><00:04:00.239><c> haven't</c>

00:04:00.429 --> 00:04:00.439 align:start position:0%
you liked Elm Studio or if you haven't
 

00:04:00.439 --> 00:04:02.589 align:start position:0%
you liked Elm Studio or if you haven't
tried<00:04:00.720><c> it</c><00:04:00.840><c> yet</c><00:04:01.040><c> I</c><00:04:01.200><c> recommend</c><00:04:01.879><c> you</c><00:04:02.159><c> downloading</c>

00:04:02.589 --> 00:04:02.599 align:start position:0%
tried it yet I recommend you downloading
 

00:04:02.599 --> 00:04:04.470 align:start position:0%
tried it yet I recommend you downloading
it<00:04:02.760><c> and</c><00:04:02.879><c> just</c><00:04:03.000><c> trying</c><00:04:03.400><c> it's</c><00:04:03.599><c> free</c><00:04:04.239><c> you</c><00:04:04.360><c> know</c>

00:04:04.470 --> 00:04:04.480 align:start position:0%
it and just trying it's free you know
 

00:04:04.480 --> 00:04:06.270 align:start position:0%
it and just trying it's free you know
they<00:04:04.560><c> don't</c><00:04:04.680><c> store</c><00:04:04.959><c> any</c><00:04:05.079><c> of</c><00:04:05.200><c> your</c><00:04:05.360><c> information</c>

00:04:06.270 --> 00:04:06.280 align:start position:0%
they don't store any of your information
 

00:04:06.280 --> 00:04:08.429 align:start position:0%
they don't store any of your information
this<00:04:06.640><c> you</c><00:04:06.760><c> can</c><00:04:06.879><c> use</c><00:04:07.120><c> all</c><00:04:07.439><c> open</c><00:04:07.879><c> source</c><00:04:08.159><c> local</c>

00:04:08.429 --> 00:04:08.439 align:start position:0%
this you can use all open source local
 

00:04:08.439 --> 00:04:10.030 align:start position:0%
this you can use all open source local
llms<00:04:09.319><c> if</c><00:04:09.400><c> you</c><00:04:09.519><c> have</c><00:04:09.599><c> any</c><00:04:09.720><c> comments</c><00:04:09.959><c> or</c>

00:04:10.030 --> 00:04:10.040 align:start position:0%
llms if you have any comments or
 

00:04:10.040 --> 00:04:11.309 align:start position:0%
llms if you have any comments or
anything<00:04:10.200><c> you</c><00:04:10.280><c> want</c><00:04:10.360><c> to</c><00:04:10.439><c> chat</c><00:04:10.640><c> about</c><00:04:11.120><c> leave</c>

00:04:11.309 --> 00:04:11.319 align:start position:0%
anything you want to chat about leave
 

00:04:11.319 --> 00:04:12.869 align:start position:0%
anything you want to chat about leave
them<00:04:11.519><c> down</c><00:04:11.680><c> the</c><00:04:11.799><c> section</c><00:04:12.120><c> below</c><00:04:12.640><c> thank</c><00:04:12.799><c> you</c>

00:04:12.869 --> 00:04:12.879 align:start position:0%
them down the section below thank you
 

00:04:12.879 --> 00:04:16.680 align:start position:0%
them down the section below thank you
for<00:04:13.120><c> watching</c><00:04:13.519><c> and</c><00:04:13.640><c> I'll</c><00:04:13.760><c> see</c><00:04:13.920><c> you</c><00:04:14.079><c> next</c><00:04:14.280><c> video</c>

