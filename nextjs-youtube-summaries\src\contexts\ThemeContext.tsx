'use client'

import { createContext, useContext, useEffect, useState } from 'react'

type Theme = 'light' | 'dark'

interface ThemeContextType {
  theme: Theme
  toggleTheme: () => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof window !== 'undefined') {
      const storedTheme = localStorage.getItem('theme') as Theme | null
      if (storedTheme) {
        return storedTheme
      }
      // Default to dark mode if no stored theme and no system preference or system prefers light
      // If system prefers dark, that will be handled by the useEffect below.
      // For the initial state, if nothing is set, let's default to dark.
      return 'dark'
    }
    return 'dark' // Default to dark on server or if window is not available
  })

  useEffect(() => {
    const root = window.document.documentElement
    const initialTheme = localStorage.getItem('theme') as Theme | null
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

    if (initialTheme) {
      setTheme(initialTheme)
      root.classList.toggle('dark', initialTheme === 'dark')
    } else if (systemPrefersDark) {
      setTheme('dark')
      root.classList.add('dark')
      localStorage.setItem('theme', 'dark')
    } else {
      // If no stored theme and system doesn't prefer dark, default to dark.
      setTheme('dark')
      root.classList.add('dark')
      localStorage.setItem('theme', 'dark')
    }
  }, [])

  useEffect(() => {
    const root = window.document.documentElement
    if (theme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
    localStorage.setItem('theme', theme)
  }, [theme])

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light')
  }

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
