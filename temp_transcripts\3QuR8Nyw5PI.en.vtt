WEBVTT
Kind: captions
Language: en

00:00:00.320 --> 00:00:02.149 align:start position:0%
 
hello<00:00:00.560><c> everyone</c><00:00:01.199><c> this</c><00:00:01.319><c> is</c><00:00:01.520><c> RI</c><00:00:01.800><c> from</c><00:00:01.920><c> Lama</c>

00:00:02.149 --> 00:00:02.159 align:start position:0%
hello everyone this is RI from Lama
 

00:00:02.159 --> 00:00:03.949 align:start position:0%
hello everyone this is RI from Lama
index<00:00:02.760><c> welcome</c><00:00:03.040><c> to</c><00:00:03.199><c> another</c><00:00:03.480><c> video</c><00:00:03.679><c> in</c><00:00:03.800><c> this</c>

00:00:03.949 --> 00:00:03.959 align:start position:0%
index welcome to another video in this
 

00:00:03.959 --> 00:00:05.789 align:start position:0%
index welcome to another video in this
series<00:00:04.240><c> of</c><00:00:04.400><c> videos</c><00:00:04.680><c> on</c><00:00:05.080><c> building</c><00:00:05.359><c> LM</c>

00:00:05.789 --> 00:00:05.799 align:start position:0%
series of videos on building LM
 

00:00:05.799 --> 00:00:08.669 align:start position:0%
series of videos on building LM
applications<00:00:06.799><c> with</c><00:00:06.960><c> Lama</c><00:00:07.240><c> index</c><00:00:07.480><c> and</c><00:00:07.679><c> cloud3</c>

00:00:08.669 --> 00:00:08.679 align:start position:0%
applications with Lama index and cloud3
 

00:00:08.679 --> 00:00:10.150 align:start position:0%
applications with Lama index and cloud3
in<00:00:08.800><c> this</c><00:00:08.960><c> video</c><00:00:09.200><c> we'll</c><00:00:09.400><c> look</c><00:00:09.559><c> into</c><00:00:09.800><c> building</c>

00:00:10.150 --> 00:00:10.160 align:start position:0%
in this video we'll look into building
 

00:00:10.160 --> 00:00:11.789 align:start position:0%
in this video we'll look into building
multimodel<00:00:10.719><c> applications</c><00:00:11.160><c> with</c><00:00:11.280><c> Lama</c><00:00:11.559><c> index</c>

00:00:11.789 --> 00:00:11.799 align:start position:0%
multimodel applications with Lama index
 

00:00:11.799 --> 00:00:15.070 align:start position:0%
multimodel applications with Lama index
and<00:00:12.240><c> cloud3</c><00:00:12.759><c> models</c><00:00:13.759><c> so</c><00:00:14.080><c> the</c><00:00:14.200><c> cloud3</c><00:00:14.759><c> models</c>

00:00:15.070 --> 00:00:15.080 align:start position:0%
and cloud3 models so the cloud3 models
 

00:00:15.080 --> 00:00:16.990 align:start position:0%
and cloud3 models so the cloud3 models
are<00:00:15.280><c> multimodel</c><00:00:15.799><c> in</c><00:00:16.000><c> nature</c><00:00:16.440><c> which</c><00:00:16.600><c> means</c>

00:00:16.990 --> 00:00:17.000 align:start position:0%
are multimodel in nature which means
 

00:00:17.000 --> 00:00:18.429 align:start position:0%
are multimodel in nature which means
they're<00:00:17.240><c> good</c><00:00:17.400><c> at</c><00:00:17.560><c> answering</c><00:00:17.920><c> queries</c><00:00:18.199><c> or</c>

00:00:18.429 --> 00:00:18.439 align:start position:0%
they're good at answering queries or
 

00:00:18.439 --> 00:00:20.750 align:start position:0%
they're good at answering queries or
text<00:00:18.760><c> and</c><00:00:19.000><c> image</c><00:00:19.320><c> modalities</c><00:00:20.320><c> so</c><00:00:20.480><c> in</c><00:00:20.640><c> this</c>

00:00:20.750 --> 00:00:20.760 align:start position:0%
text and image modalities so in this
 

00:00:20.760 --> 00:00:23.509 align:start position:0%
text and image modalities so in this
notebook<00:00:21.160><c> we'll</c><00:00:21.680><c> look</c><00:00:21.880><c> into</c><00:00:22.199><c> how</c><00:00:22.519><c> you</c><00:00:22.640><c> can</c><00:00:22.840><c> use</c>

00:00:23.509 --> 00:00:23.519 align:start position:0%
notebook we'll look into how you can use
 

00:00:23.519 --> 00:00:25.830 align:start position:0%
notebook we'll look into how you can use
these<00:00:23.680><c> models</c><00:00:24.119><c> for</c><00:00:24.439><c> processing</c><00:00:24.920><c> or</c><00:00:25.439><c> reasoning</c>

00:00:25.830 --> 00:00:25.840 align:start position:0%
these models for processing or reasoning
 

00:00:25.840 --> 00:00:29.070 align:start position:0%
these models for processing or reasoning
or<00:00:26.160><c> images</c><00:00:27.119><c> basically</c><00:00:27.880><c> understanding</c><00:00:28.439><c> uh</c><00:00:28.720><c> of</c>

00:00:29.070 --> 00:00:29.080 align:start position:0%
or images basically understanding uh of
 

00:00:29.080 --> 00:00:31.830 align:start position:0%
or images basically understanding uh of
images<00:00:30.359><c> right</c><00:00:30.720><c> so</c><00:00:31.039><c> we'll</c><00:00:31.240><c> see</c><00:00:31.480><c> what</c><00:00:31.599><c> are</c><00:00:31.720><c> the</c>

00:00:31.830 --> 00:00:31.840 align:start position:0%
images right so we'll see what are the
 

00:00:31.840 --> 00:00:34.350 align:start position:0%
images right so we'll see what are the
different<00:00:32.119><c> abstractions</c><00:00:32.640><c> available</c><00:00:32.960><c> and</c><00:00:33.360><c> how</c>

00:00:34.350 --> 00:00:34.360 align:start position:0%
different abstractions available and how
 

00:00:34.360 --> 00:00:35.549 align:start position:0%
different abstractions available and how
it<00:00:34.480><c> will</c><00:00:34.640><c> be</c><00:00:34.760><c> helpful</c><00:00:35.079><c> in</c><00:00:35.200><c> building</c>

00:00:35.549 --> 00:00:35.559 align:start position:0%
it will be helpful in building
 

00:00:35.559 --> 00:00:38.350 align:start position:0%
it will be helpful in building
multimodel<00:00:36.520><c> applications</c><00:00:37.520><c> so</c><00:00:37.719><c> we'll</c><00:00:38.040><c> start</c>

00:00:38.350 --> 00:00:38.360 align:start position:0%
multimodel applications so we'll start
 

00:00:38.360 --> 00:00:40.190 align:start position:0%
multimodel applications so we'll start
with<00:00:38.760><c> uh</c><00:00:38.879><c> necessary</c><00:00:39.360><c> installations</c><00:00:40.000><c> and</c>

00:00:40.190 --> 00:00:40.200 align:start position:0%
with uh necessary installations and
 

00:00:40.200 --> 00:00:42.950 align:start position:0%
with uh necessary installations and
setting<00:00:40.520><c> up</c><00:00:40.760><c> the</c><00:00:40.960><c> anthropic</c><00:00:41.559><c> Keys</c><00:00:42.239><c> as</c><00:00:42.399><c> usual</c>

00:00:42.950 --> 00:00:42.960 align:start position:0%
setting up the anthropic Keys as usual
 

00:00:42.960 --> 00:00:44.750 align:start position:0%
setting up the anthropic Keys as usual
and<00:00:43.120><c> then</c><00:00:43.239><c> we'll</c><00:00:43.520><c> download</c><00:00:44.160><c> uh</c><00:00:44.239><c> some</c><00:00:44.440><c> sample</c>

00:00:44.750 --> 00:00:44.760 align:start position:0%
and then we'll download uh some sample
 

00:00:44.760 --> 00:00:48.750 align:start position:0%
and then we'll download uh some sample
images<00:00:45.680><c> related</c><00:00:46.160><c> to</c><00:00:46.399><c> a</c><00:00:46.600><c> paper</c><00:00:46.920><c> card</c><00:00:47.360><c> of</c><00:00:47.800><c> uh</c>

00:00:48.750 --> 00:00:48.760 align:start position:0%
images related to a paper card of uh
 

00:00:48.760 --> 00:00:52.229 align:start position:0%
images related to a paper card of uh
Prometheus<00:00:49.440><c> paper</c><00:00:50.000><c> and</c><00:00:50.320><c> as</c><00:00:50.440><c> well</c><00:00:50.640><c> as</c><00:00:51.160><c> uh</c><00:00:51.680><c> some</c>

00:00:52.229 --> 00:00:52.239 align:start position:0%
Prometheus paper and as well as uh some
 

00:00:52.239 --> 00:00:56.470 align:start position:0%
Prometheus paper and as well as uh some
sample<00:00:52.719><c> image</c><00:00:53.160><c> related</c><00:00:53.480><c> to</c><00:00:53.640><c> stocks</c><00:00:54.640><c> right</c><00:00:55.480><c> so</c>

00:00:56.470 --> 00:00:56.480 align:start position:0%
sample image related to stocks right so
 

00:00:56.480 --> 00:00:59.110 align:start position:0%
sample image related to stocks right so
so<00:00:56.719><c> this</c><00:00:56.800><c> is</c><00:00:56.960><c> the</c><00:00:57.239><c> promises</c><00:00:57.760><c> paper</c><00:00:58.079><c> card</c><00:00:58.600><c> so</c><00:00:58.879><c> it</c>

00:00:59.110 --> 00:00:59.120 align:start position:0%
so this is the promises paper card so it
 

00:00:59.120 --> 00:01:01.590 align:start position:0%
so this is the promises paper card so it
has<00:00:59.600><c> uh</c><00:01:00.280><c> information</c><00:01:00.760><c> about</c><00:01:00.960><c> the</c><00:01:01.120><c> prom</c>

00:01:01.590 --> 00:01:01.600 align:start position:0%
has uh information about the prom
 

00:01:01.600 --> 00:01:05.310 align:start position:0%
has uh information about the prom
Prometheus<00:01:02.559><c> U</c><00:01:03.559><c> uh</c><00:01:03.960><c> paper</c><00:01:04.680><c> which</c><00:01:04.799><c> is</c><00:01:04.920><c> on</c>

00:01:05.310 --> 00:01:05.320 align:start position:0%
Prometheus U uh paper which is on
 

00:01:05.320 --> 00:01:07.109 align:start position:0%
Prometheus U uh paper which is on
evaluation<00:01:05.840><c> building</c><00:01:06.240><c> evaluation</c><00:01:06.720><c> models</c>

00:01:07.109 --> 00:01:07.119 align:start position:0%
evaluation building evaluation models
 

00:01:07.119 --> 00:01:08.270 align:start position:0%
evaluation building evaluation models
open<00:01:07.360><c> source</c>

00:01:08.270 --> 00:01:08.280 align:start position:0%
open source
 

00:01:08.280 --> 00:01:10.710 align:start position:0%
open source
models<00:01:09.280><c> so</c><00:01:09.479><c> it</c><00:01:09.640><c> has</c><00:01:09.799><c> details</c><00:01:10.200><c> about</c><00:01:10.479><c> the</c>

00:01:10.710 --> 00:01:10.720 align:start position:0%
models so it has details about the
 

00:01:10.720 --> 00:01:12.789 align:start position:0%
models so it has details about the
contributions<00:01:11.520><c> results</c><00:01:12.240><c> uh</c><00:01:12.360><c> feedback</c>

00:01:12.789 --> 00:01:12.799 align:start position:0%
contributions results uh feedback
 

00:01:12.799 --> 00:01:14.590 align:start position:0%
contributions results uh feedback
collection<00:01:13.360><c> insights</c><00:01:13.960><c> and</c><00:01:14.240><c> what</c><00:01:14.320><c> are</c><00:01:14.479><c> the</c>

00:01:14.590 --> 00:01:14.600 align:start position:0%
collection insights and what are the
 

00:01:14.600 --> 00:01:17.109 align:start position:0%
collection insights and what are the
technical<00:01:14.960><c> bits</c><00:01:15.360><c> and</c><00:01:16.360><c> um</c><00:01:16.600><c> basically</c><00:01:16.960><c> a</c>

00:01:17.109 --> 00:01:17.119 align:start position:0%
technical bits and um basically a
 

00:01:17.119 --> 00:01:19.510 align:start position:0%
technical bits and um basically a
summary<00:01:17.479><c> about</c><00:01:17.680><c> the</c><00:01:17.920><c> paper</c>

00:01:19.510 --> 00:01:19.520 align:start position:0%
summary about the paper
 

00:01:19.520 --> 00:01:22.670 align:start position:0%
summary about the paper
so<00:01:20.520><c> we</c><00:01:20.720><c> load</c><00:01:21.040><c> this</c><00:01:21.280><c> document</c><00:01:21.880><c> uh</c><00:01:22.040><c> image</c>

00:01:22.670 --> 00:01:22.680 align:start position:0%
so we load this document uh image
 

00:01:22.680 --> 00:01:25.789 align:start position:0%
so we load this document uh image
document<00:01:23.680><c> and</c><00:01:23.920><c> then</c><00:01:24.439><c> uh</c><00:01:24.640><c> we'll</c><00:01:24.880><c> set</c><00:01:25.079><c> up</c><00:01:25.320><c> the</c>

00:01:25.789 --> 00:01:25.799 align:start position:0%
document and then uh we'll set up the
 

00:01:25.799 --> 00:01:28.550 align:start position:0%
document and then uh we'll set up the
multimodel<00:01:26.799><c> abstraction</c><00:01:27.720><c> multimodel</c><00:01:28.240><c> LM</c>

00:01:28.550 --> 00:01:28.560 align:start position:0%
multimodel abstraction multimodel LM
 

00:01:28.560 --> 00:01:31.230 align:start position:0%
multimodel abstraction multimodel LM
basically<00:01:29.320><c> and</c><00:01:29.520><c> then</c><00:01:30.000><c> ask</c><00:01:30.200><c> it</c><00:01:30.360><c> to</c><00:01:30.880><c> describe</c>

00:01:31.230 --> 00:01:31.240 align:start position:0%
basically and then ask it to describe
 

00:01:31.240 --> 00:01:34.270 align:start position:0%
basically and then ask it to describe
the<00:01:31.360><c> image</c><00:01:31.759><c> on</c><00:01:32.000><c> this</c><00:01:32.560><c> document</c><00:01:33.560><c> so</c><00:01:33.880><c> describe</c>

00:01:34.270 --> 00:01:34.280 align:start position:0%
the image on this document so describe
 

00:01:34.280 --> 00:01:37.429 align:start position:0%
the image on this document so describe
the<00:01:34.399><c> image</c><00:01:34.600><c> as</c><00:01:34.720><c> an</c><00:01:34.880><c> alternative</c><00:01:35.479><c> text</c><00:01:36.280><c> so</c><00:01:37.280><c> it</c>

00:01:37.429 --> 00:01:37.439 align:start position:0%
the image as an alternative text so it
 

00:01:37.439 --> 00:01:40.870 align:start position:0%
the image as an alternative text so it
says<00:01:37.920><c> that</c><00:01:38.799><c> yeah</c><00:01:39.799><c> uh</c><00:01:39.920><c> it</c><00:01:40.079><c> describes</c><00:01:40.479><c> about</c><00:01:40.720><c> the</c>

00:01:40.870 --> 00:01:40.880 align:start position:0%
says that yeah uh it describes about the
 

00:01:40.880 --> 00:01:42.510 align:start position:0%
says that yeah uh it describes about the
contributions<00:01:41.520><c> it</c><00:01:41.680><c> describes</c><00:01:42.079><c> about</c><00:01:42.320><c> the</c>

00:01:42.510 --> 00:01:42.520 align:start position:0%
contributions it describes about the
 

00:01:42.520 --> 00:01:45.910 align:start position:0%
contributions it describes about the
feedback<00:01:43.040><c> collection</c><00:01:44.040><c> results</c><00:01:45.040><c> insights</c><00:01:45.640><c> and</c>

00:01:45.910 --> 00:01:45.920 align:start position:0%
feedback collection results insights and
 

00:01:45.920 --> 00:01:46.950 align:start position:0%
feedback collection results insights and
Technical

00:01:46.950 --> 00:01:46.960 align:start position:0%
Technical
 

00:01:46.960 --> 00:01:51.590 align:start position:0%
Technical
bits<00:01:47.960><c> uh</c><00:01:48.320><c> so</c><00:01:49.320><c> I</c><00:01:49.399><c> mean</c><00:01:49.960><c> uh</c><00:01:50.600><c> we</c><00:01:50.880><c> set</c><00:01:51.119><c> the</c><00:01:51.320><c> max</c>

00:01:51.590 --> 00:01:51.600 align:start position:0%
bits uh so I mean uh we set the max
 

00:01:51.600 --> 00:01:56.069 align:start position:0%
bits uh so I mean uh we set the max
tokens<00:01:51.920><c> as</c><00:01:52.159><c> 300</c><00:01:52.799><c> so</c><00:01:53.399><c> uh</c><00:01:53.680><c> just</c><00:01:54.280><c> Tran</c><00:01:54.920><c> it</c><00:01:55.159><c> but</c><00:01:55.960><c> uh</c>

00:01:56.069 --> 00:01:56.079 align:start position:0%
tokens as 300 so uh just Tran it but uh
 

00:01:56.079 --> 00:01:57.830 align:start position:0%
tokens as 300 so uh just Tran it but uh
it<00:01:56.159><c> was</c><00:01:56.320><c> generating</c><00:01:56.799><c> even</c><00:01:57.000><c> the</c><00:01:57.119><c> diagram</c><00:01:57.479><c> uses</c>

00:01:57.830 --> 00:01:57.840 align:start position:0%
it was generating even the diagram uses
 

00:01:57.840 --> 00:02:00.230 align:start position:0%
it was generating even the diagram uses
something<00:01:58.439><c> else</c><00:01:58.759><c> information</c><00:01:59.240><c> as</c><00:01:59.360><c> well</c><00:01:59.920><c> so</c>

00:02:00.230 --> 00:02:00.240 align:start position:0%
something else information as well so
 

00:02:00.240 --> 00:02:02.310 align:start position:0%
something else information as well so
it's<00:02:00.439><c> pretty</c><00:02:00.680><c> much</c><00:02:00.840><c> detailed</c><00:02:01.320><c> about</c><00:02:01.680><c> the</c>

00:02:02.310 --> 00:02:02.320 align:start position:0%
it's pretty much detailed about the
 

00:02:02.320 --> 00:02:04.749 align:start position:0%
it's pretty much detailed about the
image<00:02:03.320><c> um</c><00:02:03.960><c> uh</c>

00:02:04.749 --> 00:02:04.759 align:start position:0%
image um uh
 

00:02:04.759 --> 00:02:08.550 align:start position:0%
image um uh
and<00:02:05.759><c> uh</c><00:02:06.520><c> which</c><00:02:06.719><c> gives</c><00:02:07.079><c> much</c><00:02:07.280><c> more</c><00:02:07.680><c> uh</c><00:02:07.960><c> gr</c>

00:02:08.550 --> 00:02:08.560 align:start position:0%
and uh which gives much more uh gr
 

00:02:08.560 --> 00:02:11.270 align:start position:0%
and uh which gives much more uh gr
information<00:02:09.560><c> about</c><00:02:09.800><c> the</c><00:02:10.000><c> image</c><00:02:10.360><c> or</c><00:02:10.479><c> to</c><00:02:11.120><c> have</c>

00:02:11.270 --> 00:02:11.280 align:start position:0%
information about the image or to have
 

00:02:11.280 --> 00:02:14.030 align:start position:0%
information about the image or to have
an<00:02:11.680><c> understanding</c><00:02:12.000><c> of</c><00:02:12.239><c> of</c><00:02:12.360><c> the</c><00:02:12.520><c> image</c><00:02:13.360><c> right</c>

00:02:14.030 --> 00:02:14.040 align:start position:0%
an understanding of of the image right
 

00:02:14.040 --> 00:02:18.270 align:start position:0%
an understanding of of the image right
so<00:02:14.720><c> this</c><00:02:14.879><c> way</c><00:02:15.080><c> you</c><00:02:15.200><c> can</c><00:02:15.720><c> uh</c><00:02:16.720><c> may</c><00:02:17.360><c> use</c><00:02:17.760><c> l</c>

00:02:18.270 --> 00:02:18.280 align:start position:0%
so this way you can uh may use l
 

00:02:18.280 --> 00:02:23.150 align:start position:0%
so this way you can uh may use l
abstractions<00:02:19.040><c> to</c><00:02:19.879><c> easily</c><00:02:20.800><c> uh</c><00:02:21.280><c> have</c><00:02:21.640><c> an</c><00:02:21.840><c> image</c>

00:02:23.150 --> 00:02:23.160 align:start position:0%
abstractions to easily uh have an image
 

00:02:23.160 --> 00:02:25.390 align:start position:0%
abstractions to easily uh have an image
understanding<00:02:24.160><c> and</c><00:02:24.319><c> then</c><00:02:24.480><c> we'll</c><00:02:24.680><c> use</c><00:02:25.200><c> we'll</c>

00:02:25.390 --> 00:02:25.400 align:start position:0%
understanding and then we'll use we'll
 

00:02:25.400 --> 00:02:28.070 align:start position:0%
understanding and then we'll use we'll
see<00:02:26.160><c> um</c><00:02:26.560><c> how</c><00:02:26.760><c> you</c><00:02:26.879><c> can</c>

00:02:28.070 --> 00:02:28.080 align:start position:0%
see um how you can
 

00:02:28.080 --> 00:02:31.589 align:start position:0%
see um how you can
use<00:02:29.080><c> urls</c><00:02:29.840><c> to</c><00:02:30.000><c> reason</c><00:02:30.239><c> over</c><00:02:30.480><c> the</c><00:02:30.599><c> images</c><00:02:31.400><c> so</c>

00:02:31.589 --> 00:02:31.599 align:start position:0%
use urls to reason over the images so
 

00:02:31.599 --> 00:02:33.630 align:start position:0%
use urls to reason over the images so
this<00:02:31.680><c> is</c><00:02:31.840><c> an</c><00:02:32.000><c> image</c><00:02:32.360><c> from</c><00:02:32.840><c> um</c><00:02:33.080><c> anthropic</c>

00:02:33.630 --> 00:02:33.640 align:start position:0%
this is an image from um anthropic
 

00:02:33.640 --> 00:02:35.869 align:start position:0%
this is an image from um anthropic
website<00:02:34.200><c> itself</c><00:02:35.200><c> when</c><00:02:35.360><c> they</c><00:02:35.480><c> release</c><00:02:35.800><c> the</c>

00:02:35.869 --> 00:02:35.879 align:start position:0%
website itself when they release the
 

00:02:35.879 --> 00:02:39.229 align:start position:0%
website itself when they release the
model<00:02:36.360><c> Rel</c><00:02:36.680><c> to</c><00:02:36.840><c> Opus</c><00:02:37.280><c> on</c><00:02:37.519><c> it</c><00:02:37.640><c> and</c><00:02:37.920><c> Hau</c><00:02:38.840><c> and</c><00:02:39.080><c> how</c>

00:02:39.229 --> 00:02:39.239 align:start position:0%
model Rel to Opus on it and Hau and how
 

00:02:39.239 --> 00:02:41.830 align:start position:0%
model Rel to Opus on it and Hau and how
are<00:02:39.440><c> their</c><00:02:39.640><c> benchmarks</c><00:02:40.200><c> and</c><00:02:40.400><c> all</c>

00:02:41.830 --> 00:02:41.840 align:start position:0%
are their benchmarks and all
 

00:02:41.840 --> 00:02:45.030 align:start position:0%
are their benchmarks and all
so<00:02:42.840><c> uh</c><00:02:43.000><c> we'll</c><00:02:43.200><c> load</c><00:02:43.480><c> the</c><00:02:43.599><c> images</c><00:02:44.480><c> with</c><00:02:44.640><c> load</c>

00:02:45.030 --> 00:02:45.040 align:start position:0%
so uh we'll load the images with load
 

00:02:45.040 --> 00:02:48.309 align:start position:0%
so uh we'll load the images with load
uncore<00:02:45.480><c> image</c><00:02:45.720><c> uncore</c><00:02:46.120><c> URLs</c><00:02:47.080><c> and</c><00:02:47.319><c> then</c><00:02:47.959><c> ask</c><00:02:48.159><c> a</c>

00:02:48.309 --> 00:02:48.319 align:start position:0%
uncore image uncore URLs and then ask a
 

00:02:48.319 --> 00:02:51.750 align:start position:0%
uncore image uncore URLs and then ask a
query<00:02:48.680><c> about</c><00:02:48.959><c> it</c><00:02:49.879><c> so</c><00:02:50.239><c> it</c><00:02:50.440><c> says</c><00:02:51.080><c> the</c><00:02:51.440><c> shows</c><00:02:51.640><c> a</c>

00:02:51.750 --> 00:02:51.760 align:start position:0%
query about it so it says the shows a
 

00:02:51.760 --> 00:02:54.830 align:start position:0%
query about it so it says the shows a
table<00:02:52.040><c> comparing</c><00:02:52.400><c> the</c><00:02:52.519><c> benchmark</c><00:02:53.000><c> scores</c><00:02:53.959><c> of</c>

00:02:54.830 --> 00:02:54.840 align:start position:0%
table comparing the benchmark scores of
 

00:02:54.840 --> 00:02:58.949 align:start position:0%
table comparing the benchmark scores of
cloud<00:02:55.840><c> 3A</c><00:02:56.400><c> models</c><00:02:57.319><c> across</c><00:02:57.760><c> different</c><00:02:58.400><c> uh</c>

00:02:58.949 --> 00:02:58.959 align:start position:0%
cloud 3A models across different uh
 

00:02:58.959 --> 00:03:01.309 align:start position:0%
cloud 3A models across different uh
other<00:02:59.200><c> models</c>

00:03:01.309 --> 00:03:01.319 align:start position:0%
other models
 

00:03:01.319 --> 00:03:03.390 align:start position:0%
other models
and<00:03:01.519><c> then</c><00:03:01.720><c> what</c><00:03:01.840><c> are</c><00:03:02.000><c> the</c><00:03:02.159><c> different</c><00:03:02.800><c> uh</c><00:03:03.120><c> data</c>

00:03:03.390 --> 00:03:03.400 align:start position:0%
and then what are the different uh data
 

00:03:03.400 --> 00:03:07.430 align:start position:0%
and then what are the different uh data
set<00:03:04.120><c> uh</c><00:03:04.319><c> they</c><00:03:04.440><c> have</c><00:03:04.640><c> used</c><00:03:05.120><c> and</c><00:03:05.280><c> the</c><00:03:05.920><c> scores</c><00:03:06.920><c> and</c>

00:03:07.430 --> 00:03:07.440 align:start position:0%
set uh they have used and the scores and
 

00:03:07.440 --> 00:03:10.190 align:start position:0%
set uh they have used and the scores and
uh<00:03:07.879><c> and</c><00:03:08.080><c> they</c><00:03:08.239><c> so</c><00:03:09.040><c> it</c><00:03:09.319><c> summarizes</c><00:03:09.959><c> that</c><00:03:10.080><c> the</c>

00:03:10.190 --> 00:03:10.200 align:start position:0%
uh and they so it summarizes that the
 

00:03:10.200 --> 00:03:11.869 align:start position:0%
uh and they so it summarizes that the
cloud<00:03:10.480><c> A3</c><00:03:10.799><c> model</c><00:03:11.080><c> show</c><00:03:11.400><c> competive</c>

00:03:11.869 --> 00:03:11.879 align:start position:0%
cloud A3 model show competive
 

00:03:11.879 --> 00:03:13.509 align:start position:0%
cloud A3 model show competive
performance<00:03:12.280><c> compared</c><00:03:12.599><c> to</c><00:03:12.680><c> gbt</c><00:03:13.040><c> and</c><00:03:13.159><c> GD</c>

00:03:13.509 --> 00:03:13.519 align:start position:0%
performance compared to gbt and GD
 

00:03:13.519 --> 00:03:15.350 align:start position:0%
performance compared to gbt and GD
models<00:03:13.840><c> across</c><00:03:14.159><c> various</c>

00:03:15.350 --> 00:03:15.360 align:start position:0%
models across various
 

00:03:15.360 --> 00:03:17.910 align:start position:0%
models across various
benchmarks<00:03:16.360><c> right</c><00:03:16.640><c> so</c><00:03:16.959><c> and</c><00:03:17.120><c> it</c><00:03:17.280><c> even</c><00:03:17.519><c> mentions</c>

00:03:17.910 --> 00:03:17.920 align:start position:0%
benchmarks right so and it even mentions
 

00:03:17.920 --> 00:03:20.710 align:start position:0%
benchmarks right so and it even mentions
that<00:03:18.239><c> g</c><00:03:18.680><c> models</c><00:03:19.000><c> have</c><00:03:19.120><c> a</c><00:03:19.239><c> slight</c><00:03:19.760><c> advantage</c><00:03:20.400><c> or</c>

00:03:20.710 --> 00:03:20.720 align:start position:0%
that g models have a slight advantage or
 

00:03:20.720 --> 00:03:22.750 align:start position:0%
that g models have a slight advantage or
Edge<00:03:21.040><c> in</c><00:03:21.200><c> some</c><00:03:21.440><c> categories</c><00:03:21.879><c> like</c><00:03:22.040><c> under</c>

00:03:22.750 --> 00:03:22.760 align:start position:0%
Edge in some categories like under
 

00:03:22.760 --> 00:03:25.789 align:start position:0%
Edge in some categories like under
knowledge<00:03:23.760><c> and</c><00:03:23.959><c> math</c><00:03:24.280><c> problem</c><00:03:24.560><c> solving</c><00:03:25.560><c> right</c>

00:03:25.789 --> 00:03:25.799 align:start position:0%
knowledge and math problem solving right
 

00:03:25.799 --> 00:03:29.149 align:start position:0%
knowledge and math problem solving right
so<00:03:26.599><c> it</c><00:03:26.760><c> gave</c><00:03:27.040><c> some</c><00:03:27.440><c> uh</c><00:03:27.560><c> decent</c><00:03:28.000><c> details</c><00:03:28.560><c> about</c>

00:03:29.149 --> 00:03:29.159 align:start position:0%
so it gave some uh decent details about
 

00:03:29.159 --> 00:03:32.990 align:start position:0%
so it gave some uh decent details about
uh<00:03:30.000><c> uh</c><00:03:30.959><c> this</c><00:03:31.159><c> image</c><00:03:31.760><c> right</c><00:03:32.280><c> so</c><00:03:32.519><c> that's</c><00:03:32.680><c> how</c><00:03:32.879><c> you</c>

00:03:32.990 --> 00:03:33.000 align:start position:0%
uh uh this image right so that's how you
 

00:03:33.000 --> 00:03:36.030 align:start position:0%
uh uh this image right so that's how you
can<00:03:33.200><c> load</c><00:03:33.760><c> the</c><00:03:34.040><c> document</c><00:03:34.439><c> locally</c><00:03:35.040><c> or</c><00:03:35.280><c> maybe</c>

00:03:36.030 --> 00:03:36.040 align:start position:0%
can load the document locally or maybe
 

00:03:36.040 --> 00:03:39.630 align:start position:0%
can load the document locally or maybe
uh<00:03:36.680><c> using</c><00:03:36.959><c> an</c><00:03:37.120><c> image</c><00:03:37.400><c> URL</c><00:03:37.840><c> as</c><00:03:38.200><c> well</c><00:03:39.200><c> and</c><00:03:39.400><c> next</c>

00:03:39.630 --> 00:03:39.640 align:start position:0%
uh using an image URL as well and next
 

00:03:39.640 --> 00:03:42.390 align:start position:0%
uh using an image URL as well and next
we'll<00:03:39.879><c> see</c><00:03:40.239><c> how</c><00:03:40.439><c> can</c><00:03:40.599><c> you</c><00:03:41.360><c> uh</c><00:03:41.640><c> get</c><00:03:41.920><c> structured</c>

00:03:42.390 --> 00:03:42.400 align:start position:0%
we'll see how can you uh get structured
 

00:03:42.400 --> 00:03:45.030 align:start position:0%
we'll see how can you uh get structured
output<00:03:42.760><c> paring</c><00:03:43.080><c> from</c><00:03:43.239><c> an</c><00:03:43.439><c> image</c><00:03:44.120><c> so</c><00:03:44.640><c> here</c><00:03:44.840><c> is</c>

00:03:45.030 --> 00:03:45.040 align:start position:0%
output paring from an image so here is
 

00:03:45.040 --> 00:03:46.589 align:start position:0%
output paring from an image so here is
an

00:03:46.589 --> 00:03:46.599 align:start position:0%
an
 

00:03:46.599 --> 00:03:51.789 align:start position:0%
an
image<00:03:47.599><c> of</c><00:03:47.760><c> a</c><00:03:48.000><c> stock</c><00:03:48.640><c> uh</c><00:03:49.159><c> price</c><00:03:50.159><c> uh</c><00:03:50.519><c> probably</c><00:03:51.360><c> uh</c>

00:03:51.789 --> 00:03:51.799 align:start position:0%
image of a stock uh price uh probably uh
 

00:03:51.799 --> 00:03:54.670 align:start position:0%
image of a stock uh price uh probably uh
and<00:03:52.040><c> then</c><00:03:52.480><c> uh</c><00:03:53.159><c> we'll</c><00:03:53.400><c> see</c><00:03:53.840><c> we'll</c><00:03:54.079><c> extract</c><00:03:54.480><c> some</c>

00:03:54.670 --> 00:03:54.680 align:start position:0%
and then uh we'll see we'll extract some
 

00:03:54.680 --> 00:03:56.229 align:start position:0%
and then uh we'll see we'll extract some
specific<00:03:55.079><c> information</c><00:03:55.480><c> in</c><00:03:55.599><c> a</c><00:03:55.760><c> specific</c>

00:03:56.229 --> 00:03:56.239 align:start position:0%
specific information in a specific
 

00:03:56.239 --> 00:04:00.710 align:start position:0%
specific information in a specific
format<00:03:57.040><c> uh</c><00:03:57.239><c> by</c><00:03:57.720><c> uh</c><00:03:58.040><c> giving</c><00:03:59.040><c> pantic</c><00:04:00.079><c> schema</c>

00:04:00.710 --> 00:04:00.720 align:start position:0%
format uh by uh giving pantic schema
 

00:04:00.720 --> 00:04:04.710 align:start position:0%
format uh by uh giving pantic schema
right<00:04:01.079><c> so</c><00:04:01.480><c> here</c><00:04:02.159><c> we</c><00:04:02.640><c> have</c><00:04:03.640><c> ticker</c><00:04:04.040><c> info</c><00:04:04.519><c> which</c>

00:04:04.710 --> 00:04:04.720 align:start position:0%
right so here we have ticker info which
 

00:04:04.720 --> 00:04:06.710 align:start position:0%
right so here we have ticker info which
has<00:04:05.000><c> Direction</c><00:04:05.439><c> ticker</c><00:04:05.920><c> company</c><00:04:06.319><c> shares</c>

00:04:06.710 --> 00:04:06.720 align:start position:0%
has Direction ticker company shares
 

00:04:06.720 --> 00:04:09.270 align:start position:0%
has Direction ticker company shares
traded<00:04:07.200><c> percentage</c><00:04:07.560><c> of</c><00:04:07.760><c> total</c><00:04:08.040><c> EDF</c><00:04:08.519><c> and</c><00:04:08.680><c> all</c>

00:04:09.270 --> 00:04:09.280 align:start position:0%
traded percentage of total EDF and all
 

00:04:09.280 --> 00:04:11.069 align:start position:0%
traded percentage of total EDF and all
and<00:04:09.439><c> ticker</c><00:04:09.760><c> list</c><00:04:10.040><c> what</c><00:04:10.120><c> is</c><00:04:10.200><c> the</c><00:04:10.319><c> fund</c><00:04:10.599><c> and</c><00:04:10.760><c> the</c>

00:04:11.069 --> 00:04:11.079 align:start position:0%
and ticker list what is the fund and the
 

00:04:11.079 --> 00:04:14.830 align:start position:0%
and ticker list what is the fund and the
tickers<00:04:12.079><c> okay</c><00:04:12.720><c> so</c><00:04:12.920><c> we'll</c><00:04:13.400><c> Define</c><00:04:13.920><c> these</c><00:04:14.239><c> pent</c>

00:04:14.830 --> 00:04:14.840 align:start position:0%
tickers okay so we'll Define these pent
 

00:04:14.840 --> 00:04:19.830 align:start position:0%
tickers okay so we'll Define these pent
schema<00:04:15.480><c> and</c><00:04:15.760><c> then</c><00:04:16.239><c> uh</c><00:04:16.840><c> we</c><00:04:17.079><c> have</c><00:04:17.880><c> uh</c><00:04:18.840><c> multimodel</c>

00:04:19.830 --> 00:04:19.840 align:start position:0%
schema and then uh we have uh multimodel
 

00:04:19.840 --> 00:04:22.030 align:start position:0%
schema and then uh we have uh multimodel
uh<00:04:20.000><c> llm</c><00:04:20.479><c> completion</c><00:04:21.000><c> program</c><00:04:21.720><c> which</c><00:04:21.840><c> will</c>

00:04:22.030 --> 00:04:22.040 align:start position:0%
uh llm completion program which will
 

00:04:22.040 --> 00:04:25.469 align:start position:0%
uh llm completion program which will
help<00:04:22.280><c> you</c><00:04:22.400><c> to</c><00:04:23.360><c> uh</c><00:04:23.639><c> get</c><00:04:24.520><c> uh</c><00:04:24.800><c> the</c><00:04:25.000><c> answer</c><00:04:25.280><c> in</c><00:04:25.360><c> the</c>

00:04:25.469 --> 00:04:25.479 align:start position:0%
help you to uh get uh the answer in the
 

00:04:25.479 --> 00:04:28.150 align:start position:0%
help you to uh get uh the answer in the
Json<00:04:25.960><c> format</c><00:04:26.680><c> according</c><00:04:27.000><c> to</c><00:04:27.120><c> the</c><00:04:27.280><c> P</c><00:04:27.720><c> schema</c><00:04:28.040><c> we</c>

00:04:28.150 --> 00:04:28.160 align:start position:0%
Json format according to the P schema we
 

00:04:28.160 --> 00:04:31.870 align:start position:0%
Json format according to the P schema we
have<00:04:28.360><c> defined</c><00:04:29.280><c> so</c><00:04:29.800><c> we'll</c><00:04:30.759><c> Define</c><00:04:31.280><c> we</c><00:04:31.600><c> we</c><00:04:31.720><c> have</c>

00:04:31.870 --> 00:04:31.880 align:start position:0%
have defined so we'll Define we we have
 

00:04:31.880 --> 00:04:34.350 align:start position:0%
have defined so we'll Define we we have
sent<00:04:32.400><c> the</c><00:04:32.680><c> ticker</c><00:04:33.080><c> list</c><00:04:33.400><c> here</c><00:04:33.680><c> and</c><00:04:33.840><c> the</c><00:04:33.960><c> image</c>

00:04:34.350 --> 00:04:34.360 align:start position:0%
sent the ticker list here and the image
 

00:04:34.360 --> 00:04:37.909 align:start position:0%
sent the ticker list here and the image
documents<00:04:35.360><c> and</c><00:04:35.600><c> prompt</c><00:04:36.039><c> template</c><00:04:37.000><c> so</c><00:04:37.280><c> here</c><00:04:37.479><c> we</c>

00:04:37.909 --> 00:04:37.919 align:start position:0%
documents and prompt template so here we
 

00:04:37.919 --> 00:04:39.189 align:start position:0%
documents and prompt template so here we
ask<00:04:38.240><c> like</c><00:04:38.360><c> can</c><00:04:38.479><c> you</c><00:04:38.639><c> get</c><00:04:38.759><c> the</c><00:04:38.919><c> stock</c>

00:04:39.189 --> 00:04:39.199 align:start position:0%
ask like can you get the stock
 

00:04:39.199 --> 00:04:40.909 align:start position:0%
ask like can you get the stock
information<00:04:39.560><c> in</c><00:04:39.639><c> the</c><00:04:39.759><c> image</c><00:04:40.000><c> and</c><00:04:40.120><c> written</c><00:04:40.360><c> the</c>

00:04:40.909 --> 00:04:40.919 align:start position:0%
information in the image and written the
 

00:04:40.919 --> 00:04:45.070 align:start position:0%
information in the image and written the
answer<00:04:41.919><c> uh</c><00:04:42.160><c> pick</c><00:04:42.400><c> just</c><00:04:42.600><c> one</c><00:04:42.880><c> fun</c><00:04:43.560><c> fund</c><00:04:44.440><c> so</c><00:04:44.919><c> make</c>

00:04:45.070 --> 00:04:45.080 align:start position:0%
answer uh pick just one fun fund so make
 

00:04:45.080 --> 00:04:47.189 align:start position:0%
answer uh pick just one fun fund so make
sure<00:04:45.360><c> the</c><00:04:45.479><c> answer</c><00:04:45.840><c> in</c><00:04:46.000><c> Json</c><00:04:46.440><c> format</c><00:04:46.919><c> according</c>

00:04:47.189 --> 00:04:47.199 align:start position:0%
sure the answer in Json format according
 

00:04:47.199 --> 00:04:51.350 align:start position:0%
sure the answer in Json format according
to<00:04:47.400><c> Pon</c><00:04:48.199><c> schema</c><00:04:49.199><c> so</c><00:04:49.600><c> and</c><00:04:49.800><c> then</c>

00:04:51.350 --> 00:04:51.360 align:start position:0%
to Pon schema so and then
 

00:04:51.360 --> 00:04:54.909 align:start position:0%
to Pon schema so and then
you<00:04:52.360><c> call</c><00:04:52.600><c> the</c><00:04:52.720><c> LM</c><00:04:53.120><c> program</c><00:04:53.840><c> so</c><00:04:54.280><c> it</c><00:04:54.479><c> generates</c>

00:04:54.909 --> 00:04:54.919 align:start position:0%
you call the LM program so it generates
 

00:04:54.919 --> 00:04:56.350 align:start position:0%
you call the LM program so it generates
the<00:04:55.039><c> output</c><00:04:55.360><c> in</c>

00:04:56.350 --> 00:04:56.360 align:start position:0%
the output in
 

00:04:56.360 --> 00:05:00.830 align:start position:0%
the output in
the<00:04:57.360><c> um</c><00:04:58.280><c> Json</c><00:04:58.800><c> format</c><00:04:59.840><c> uh</c><00:05:00.039><c> because</c><00:05:00.479><c> we</c><00:05:00.639><c> have</c>

00:05:00.830 --> 00:05:00.840 align:start position:0%
the um Json format uh because we have
 

00:05:00.840 --> 00:05:04.070 align:start position:0%
the um Json format uh because we have
defined<00:05:01.479><c> uh</c><00:05:01.600><c> the</c><00:05:01.840><c> specific</c><00:05:02.639><c> uh</c><00:05:03.639><c> penting</c>

00:05:04.070 --> 00:05:04.080 align:start position:0%
defined uh the specific uh penting
 

00:05:04.080 --> 00:05:04.909 align:start position:0%
defined uh the specific uh penting
scheme

00:05:04.909 --> 00:05:04.919 align:start position:0%
scheme
 

00:05:04.919 --> 00:05:09.790 align:start position:0%
scheme
accordingly<00:05:06.000><c> so</c><00:05:07.000><c> that's</c><00:05:07.199><c> how</c><00:05:07.759><c> uh</c><00:05:08.000><c> it</c><00:05:08.759><c> uh</c><00:05:09.440><c> gave</c>

00:05:09.790 --> 00:05:09.800 align:start position:0%
accordingly so that's how uh it uh gave
 

00:05:09.800 --> 00:05:12.550 align:start position:0%
accordingly so that's how uh it uh gave
information<00:05:10.400><c> about</c><00:05:10.960><c> the</c><00:05:11.199><c> ticker</c><00:05:11.840><c> the</c><00:05:12.039><c> company</c>

00:05:12.550 --> 00:05:12.560 align:start position:0%
information about the ticker the company
 

00:05:12.560 --> 00:05:15.629 align:start position:0%
information about the ticker the company
and<00:05:12.680><c> the</c><00:05:12.800><c> shares</c><00:05:13.240><c> traded</c><00:05:14.240><c> and</c><00:05:14.479><c> various</c><00:05:14.800><c> other</c>

00:05:15.629 --> 00:05:15.639 align:start position:0%
and the shares traded and various other
 

00:05:15.639 --> 00:05:18.749 align:start position:0%
and the shares traded and various other
things<00:05:16.639><c> so</c><00:05:16.880><c> that's</c><00:05:17.080><c> how</c><00:05:17.280><c> you</c><00:05:17.400><c> can</c><00:05:18.000><c> uh</c><00:05:18.240><c> use</c>

00:05:18.749 --> 00:05:18.759 align:start position:0%
things so that's how you can uh use
 

00:05:18.759 --> 00:05:20.670 align:start position:0%
things so that's how you can uh use
anthropic<00:05:19.280><c> models</c><00:05:19.720><c> and</c><00:05:19.880><c> Lama</c><00:05:20.199><c> index</c><00:05:20.520><c> and</c>

00:05:20.670 --> 00:05:20.680 align:start position:0%
anthropic models and Lama index and
 

00:05:20.680 --> 00:05:23.110 align:start position:0%
anthropic models and Lama index and
build<00:05:21.199><c> uh</c><00:05:21.319><c> some</c><00:05:21.759><c> interesting</c><00:05:22.440><c> uh</c><00:05:22.560><c> image</c>

00:05:23.110 --> 00:05:23.120 align:start position:0%
build uh some interesting uh image
 

00:05:23.120 --> 00:05:24.870 align:start position:0%
build uh some interesting uh image
understanding<00:05:23.560><c> applications</c><00:05:24.560><c> hope</c><00:05:24.759><c> you</c>

00:05:24.870 --> 00:05:24.880 align:start position:0%
understanding applications hope you
 

00:05:24.880 --> 00:05:27.150 align:start position:0%
understanding applications hope you
found<00:05:25.400><c> uh</c><00:05:25.560><c> this</c><00:05:25.720><c> video</c><00:05:26.000><c> interesting</c><00:05:26.800><c> um</c><00:05:27.000><c> see</c>

00:05:27.150 --> 00:05:27.160 align:start position:0%
found uh this video interesting um see
 

00:05:27.160 --> 00:05:31.479 align:start position:0%
found uh this video interesting um see
you<00:05:27.240><c> in</c><00:05:27.319><c> the</c><00:05:27.479><c> next</c><00:05:27.880><c> video</c><00:05:28.880><c> thank</c><00:05:29.080><c> you</c>

