/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['var(--font-geist-sans)'],
        mono: ['var(--font-geist-mono)'],
      },
      colors: {
        background: {
          light: '#ffffff',
          dark: '#0a0a0a',
        },
        foreground: {
          light: '#171717',
          dark: '#ededed',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))', // For components using muted
          foreground: 'hsl(var(--muted-foreground))', // For components using muted-foreground
          light: '#f1f5f9', // Specific light muted
          dark: '#1e293b', // Specific dark muted
        },
        'muted-foreground': {
          light: '#64748b',
          dark: '#94a3b8',
        },
        border: {
          DEFAULT: 'hsl(var(--border))', // For components using border
          light: '#e2e8f0', // Specific light border
          dark: '#334155', // Specific dark border
        },
        // You can add more custom color definitions here
      },
    },
  },
  plugins: [],
};
