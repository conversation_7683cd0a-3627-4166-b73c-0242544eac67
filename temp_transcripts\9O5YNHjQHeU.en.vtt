WEBVTT
Kind: captions
Language: en

00:00:00.050 --> 00:00:06.430 align:start position:0%
 
[Music]

00:00:06.430 --> 00:00:06.440 align:start position:0%
 
 

00:00:06.440 --> 00:00:07.990 align:start position:0%
 
there<00:00:06.560><c> are</c><00:00:06.720><c> two</c><00:00:07.000><c> steps</c><00:00:07.319><c> to</c><00:00:07.480><c> any</c><00:00:07.640><c> model</c>

00:00:07.990 --> 00:00:08.000 align:start position:0%
there are two steps to any model
 

00:00:08.000 --> 00:00:09.830 align:start position:0%
there are two steps to any model
training<00:00:08.480><c> process</c><00:00:09.480><c> there's</c><00:00:09.679><c> the</c>

00:00:09.830 --> 00:00:09.840 align:start position:0%
training process there's the
 

00:00:09.840 --> 00:00:11.789 align:start position:0%
training process there's the
pre-training<00:00:10.480><c> process</c><00:00:11.360><c> and</c><00:00:11.519><c> there's</c><00:00:11.679><c> the</c>

00:00:11.789 --> 00:00:11.799 align:start position:0%
pre-training process and there's the
 

00:00:11.799 --> 00:00:13.829 align:start position:0%
pre-training process and there's the
fine-tuning<00:00:12.440><c> process</c><00:00:13.120><c> personally</c><00:00:13.559><c> I</c><00:00:13.639><c> will</c>

00:00:13.829 --> 00:00:13.839 align:start position:0%
fine-tuning process personally I will
 

00:00:13.839 --> 00:00:15.669 align:start position:0%
fine-tuning process personally I will
say<00:00:14.080><c> I</c><00:00:14.200><c> hate</c><00:00:14.360><c> the</c><00:00:14.480><c> phrase</c><00:00:14.759><c> fine-tuning</c>

00:00:15.669 --> 00:00:15.679 align:start position:0%
say I hate the phrase fine-tuning
 

00:00:15.679 --> 00:00:17.150 align:start position:0%
say I hate the phrase fine-tuning
because<00:00:16.000><c> often</c><00:00:16.160><c> fine</c><00:00:16.359><c> tuning</c><00:00:16.640><c> is</c><00:00:16.760><c> not</c><00:00:16.920><c> very</c>

00:00:17.150 --> 00:00:17.160 align:start position:0%
because often fine tuning is not very
 

00:00:17.160 --> 00:00:18.470 align:start position:0%
because often fine tuning is not very
fine<00:00:17.359><c> it</c><00:00:17.439><c> tends</c><00:00:17.680><c> to</c><00:00:17.840><c> actually</c><00:00:18.080><c> be</c><00:00:18.320><c> as</c>

00:00:18.470 --> 00:00:18.480 align:start position:0%
fine it tends to actually be as
 

00:00:18.480 --> 00:00:20.310 align:start position:0%
fine it tends to actually be as
intensive<00:00:18.840><c> or</c><00:00:19.000><c> more</c><00:00:19.160><c> intensive</c><00:00:19.560><c> than</c>

00:00:20.310 --> 00:00:20.320 align:start position:0%
intensive or more intensive than
 

00:00:20.320 --> 00:00:22.630 align:start position:0%
intensive or more intensive than
pre-training<00:00:21.320><c> but</c><00:00:21.480><c> pre-training</c><00:00:22.279><c> tends</c><00:00:22.519><c> to</c>

00:00:22.630 --> 00:00:22.640 align:start position:0%
pre-training but pre-training tends to
 

00:00:22.640 --> 00:00:24.150 align:start position:0%
pre-training but pre-training tends to
be<00:00:22.800><c> generic</c><00:00:23.119><c> and</c><00:00:23.279><c> self-supervised</c><00:00:24.000><c> or</c><00:00:24.080><c> just</c>

00:00:24.150 --> 00:00:24.160 align:start position:0%
be generic and self-supervised or just
 

00:00:24.160 --> 00:00:26.150 align:start position:0%
be generic and self-supervised or just
training<00:00:24.439><c> on</c><00:00:24.560><c> next</c><00:00:24.760><c> token</c><00:00:25.039><c> prediction</c><00:00:25.960><c> fine</c>

00:00:26.150 --> 00:00:26.160 align:start position:0%
training on next token prediction fine
 

00:00:26.160 --> 00:00:27.990 align:start position:0%
training on next token prediction fine
tuning<00:00:26.439><c> tends</c><00:00:26.640><c> to</c><00:00:26.760><c> be</c><00:00:26.920><c> much</c><00:00:27.160><c> more</c><00:00:27.640><c> with</c><00:00:27.800><c> a</c>

00:00:27.990 --> 00:00:28.000 align:start position:0%
tuning tends to be much more with a
 

00:00:28.000 --> 00:00:29.429 align:start position:0%
tuning tends to be much more with a
purpose<00:00:28.400><c> to</c><00:00:28.599><c> improve</c><00:00:28.920><c> the</c><00:00:29.000><c> model</c><00:00:29.199><c> on</c><00:00:29.320><c> a</c>

00:00:29.429 --> 00:00:29.439 align:start position:0%
purpose to improve the model on a
 

00:00:29.439 --> 00:00:32.069 align:start position:0%
purpose to improve the model on a
specific<00:00:30.039><c> task</c><00:00:30.279><c> on</c><00:00:30.400><c> a</c><00:00:30.519><c> specific</c><00:00:30.920><c> domain</c><00:00:31.880><c> on</c>

00:00:32.069 --> 00:00:32.079 align:start position:0%
specific task on a specific domain on
 

00:00:32.079 --> 00:00:33.470 align:start position:0%
specific task on a specific domain on
something<00:00:32.360><c> like</c><00:00:32.480><c> a</c><00:00:32.599><c> chat</c><00:00:32.880><c> or</c><00:00:33.040><c> instruction</c>

00:00:33.470 --> 00:00:33.480 align:start position:0%
something like a chat or instruction
 

00:00:33.480 --> 00:00:35.190 align:start position:0%
something like a chat or instruction
following<00:00:33.879><c> or</c><00:00:34.000><c> on</c><00:00:34.079><c> a</c><00:00:34.239><c> particular</c><00:00:34.640><c> language</c>

00:00:35.190 --> 00:00:35.200 align:start position:0%
following or on a particular language
 

00:00:35.200 --> 00:00:38.030 align:start position:0%
following or on a particular language
like<00:00:35.559><c> python</c><00:00:36.559><c> or</c><00:00:36.879><c> you</c><00:00:36.960><c> know</c><00:00:37.120><c> a</c><00:00:37.280><c> specific</c><00:00:37.960><c> you</c>

00:00:38.030 --> 00:00:38.040 align:start position:0%
like python or you know a specific you
 

00:00:38.040 --> 00:00:40.150 align:start position:0%
like python or you know a specific you
know<00:00:38.399><c> an</c><00:00:38.559><c> actual</c><00:00:38.800><c> human</c><00:00:39.079><c> language</c><00:00:39.440><c> as</c><00:00:39.600><c> well</c>

00:00:40.150 --> 00:00:40.160 align:start position:0%
know an actual human language as well
 

00:00:40.160 --> 00:00:41.510 align:start position:0%
know an actual human language as well
whatever<00:00:40.440><c> it</c><00:00:40.600><c> may</c><00:00:40.760><c> be</c><00:00:40.879><c> you</c><00:00:41.000><c> want</c><00:00:41.120><c> your</c><00:00:41.239><c> model</c>

00:00:41.510 --> 00:00:41.520 align:start position:0%
whatever it may be you want your model
 

00:00:41.520 --> 00:00:43.630 align:start position:0%
whatever it may be you want your model
to<00:00:41.680><c> learn</c><00:00:42.320><c> about</c><00:00:42.480><c> a</c><00:00:42.640><c> domain</c><00:00:43.120><c> learn</c><00:00:43.360><c> about</c><00:00:43.480><c> a</c>

00:00:43.630 --> 00:00:43.640 align:start position:0%
to learn about a domain learn about a
 

00:00:43.640 --> 00:00:44.500 align:start position:0%
to learn about a domain learn about a
task<00:00:43.840><c> or</c>

00:00:44.500 --> 00:00:44.510 align:start position:0%
task or
 

00:00:44.510 --> 00:00:50.590 align:start position:0%
task or
[Music]

00:00:50.590 --> 00:00:50.600 align:start position:0%
[Music]
 

00:00:50.600 --> 00:00:53.600 align:start position:0%
[Music]
both

