WEBVTT
Kind: captions
Language: en

00:00:00.030 --> 00:00:01.850 align:start position:0%
 
hey<00:00:00.329><c> and</c><00:00:00.659><c> welcome</c><00:00:00.989><c> back</c><00:00:01.020><c> to</c><00:00:01.230><c> another</c><00:00:01.260><c> HTML</c>

00:00:01.850 --> 00:00:01.860 align:start position:0%
hey and welcome back to another HTML
 

00:00:01.860 --> 00:00:03.949 align:start position:0%
hey and welcome back to another HTML
video<00:00:01.949><c> in</c><00:00:02.639><c> the</c><00:00:02.879><c> last</c><00:00:03.060><c> one</c><00:00:03.300><c> we</c><00:00:03.330><c> wouldn't</c><00:00:03.810><c> over</c>

00:00:03.949 --> 00:00:03.959 align:start position:0%
video in the last one we wouldn't over
 

00:00:03.959 --> 00:00:06.650 align:start position:0%
video in the last one we wouldn't over
the<00:00:04.049><c> HTML</c><00:00:04.470><c> introduction</c><00:00:05.040><c> we</c><00:00:05.970><c> coded</c><00:00:06.330><c> something</c>

00:00:06.650 --> 00:00:06.660 align:start position:0%
the HTML introduction we coded something
 

00:00:06.660 --> 00:00:07.940 align:start position:0%
the HTML introduction we coded something
and<00:00:06.899><c> then</c><00:00:07.020><c> we</c><00:00:07.109><c> saw</c><00:00:07.259><c> display</c><00:00:07.589><c> in</c><00:00:07.680><c> the</c><00:00:07.740><c> browser</c>

00:00:07.940 --> 00:00:07.950 align:start position:0%
and then we saw display in the browser
 

00:00:07.950 --> 00:00:10.280 align:start position:0%
and then we saw display in the browser
so<00:00:08.309><c> in</c><00:00:09.240><c> this</c><00:00:09.389><c> video</c><00:00:09.660><c> and</c><00:00:09.960><c> in</c><00:00:10.080><c> subsequent</c>

00:00:10.280 --> 00:00:10.290 align:start position:0%
so in this video and in subsequent
 

00:00:10.290 --> 00:00:13.220 align:start position:0%
so in this video and in subsequent
videos<00:00:10.820><c> we're</c><00:00:11.820><c> going</c><00:00:11.969><c> to</c><00:00:12.030><c> go</c><00:00:12.120><c> over</c><00:00:12.150><c> a</c><00:00:12.690><c> lot</c><00:00:13.139><c> of</c>

00:00:13.220 --> 00:00:13.230 align:start position:0%
videos we're going to go over a lot of
 

00:00:13.230 --> 00:00:16.760 align:start position:0%
videos we're going to go over a lot of
the<00:00:13.290><c> commonly</c><00:00:13.710><c> used</c><00:00:13.799><c> tanks</c><00:00:15.199><c> and</c><00:00:16.199><c> we're</c><00:00:16.410><c> going</c>

00:00:16.760 --> 00:00:16.770 align:start position:0%
the commonly used tanks and we're going
 

00:00:16.770 --> 00:00:17.840 align:start position:0%
the commonly used tanks and we're going
to<00:00:16.830><c> divide</c><00:00:17.190><c> them</c><00:00:17.340><c> up</c><00:00:17.430><c> into</c><00:00:17.580><c> certain</c>

00:00:17.840 --> 00:00:17.850 align:start position:0%
to divide them up into certain
 

00:00:17.850 --> 00:00:19.070 align:start position:0%
to divide them up into certain
categories<00:00:17.940><c> so</c><00:00:18.300><c> it's</c><00:00:18.449><c> either</c><00:00:18.600><c> easier</c><00:00:19.020><c> to</c>

00:00:19.070 --> 00:00:19.080 align:start position:0%
categories so it's either easier to
 

00:00:19.080 --> 00:00:20.269 align:start position:0%
categories so it's either easier to
remember<00:00:19.260><c> okay</c>

00:00:20.269 --> 00:00:20.279 align:start position:0%
remember okay
 

00:00:20.279 --> 00:00:22.700 align:start position:0%
remember okay
so<00:00:20.430><c> for</c><00:00:21.300><c> this</c><00:00:21.390><c> video</c><00:00:21.510><c> we're</c><00:00:22.380><c> gonna</c><00:00:22.470><c> go</c><00:00:22.560><c> over</c>

00:00:22.700 --> 00:00:22.710 align:start position:0%
so for this video we're gonna go over
 

00:00:22.710 --> 00:00:25.099 align:start position:0%
so for this video we're gonna go over
the<00:00:22.800><c> HTML</c><00:00:23.160><c> text</c><00:00:23.400><c> and</c><00:00:23.730><c> more</c><00:00:24.630><c> specifically</c>

00:00:25.099 --> 00:00:25.109 align:start position:0%
the HTML text and more specifically
 

00:00:25.109 --> 00:00:27.109 align:start position:0%
the HTML text and more specifically
we're<00:00:25.859><c> gonna</c><00:00:25.980><c> go</c><00:00:26.220><c> over</c><00:00:26.400><c> some</c><00:00:26.460><c> headings</c><00:00:27.000><c> and</c>

00:00:27.109 --> 00:00:27.119 align:start position:0%
we're gonna go over some headings and
 

00:00:27.119 --> 00:00:28.939 align:start position:0%
we're gonna go over some headings and
paragraph<00:00:27.449><c> tags</c><00:00:27.720><c> and</c><00:00:28.080><c> we're</c><00:00:28.680><c> gonna</c><00:00:28.769><c> go</c><00:00:28.920><c> over</c>

00:00:28.939 --> 00:00:28.949 align:start position:0%
paragraph tags and we're gonna go over
 

00:00:28.949 --> 00:00:30.290 align:start position:0%
paragraph tags and we're gonna go over
some<00:00:29.099><c> of</c><00:00:29.250><c> the</c><00:00:29.310><c> formatting</c><00:00:29.699><c> elements</c><00:00:29.910><c> that</c>

00:00:30.290 --> 00:00:30.300 align:start position:0%
some of the formatting elements that
 

00:00:30.300 --> 00:00:32.600 align:start position:0%
some of the formatting elements that
you'll<00:00:30.449><c> be</c><00:00:30.570><c> using</c><00:00:30.920><c> probably</c><00:00:31.920><c> in</c><00:00:32.130><c> your</c><00:00:32.219><c> future</c>

00:00:32.600 --> 00:00:32.610 align:start position:0%
you'll be using probably in your future
 

00:00:32.610 --> 00:00:35.870 align:start position:0%
you'll be using probably in your future
web<00:00:33.510><c> page</c><00:00:33.690><c> development</c><00:00:34.160><c> okay</c><00:00:35.160><c> so</c><00:00:35.219><c> let's</c><00:00:35.850><c> just</c>

00:00:35.870 --> 00:00:35.880 align:start position:0%
web page development okay so let's just
 

00:00:35.880 --> 00:00:37.910 align:start position:0%
web page development okay so let's just
get<00:00:36.059><c> started</c><00:00:36.180><c> with</c><00:00:36.390><c> programming</c><00:00:36.590><c> it's</c><00:00:37.590><c> easier</c>

00:00:37.910 --> 00:00:37.920 align:start position:0%
get started with programming it's easier
 

00:00:37.920 --> 00:00:40.040 align:start position:0%
get started with programming it's easier
to<00:00:38.040><c> kind</c><00:00:38.160><c> of</c><00:00:38.250><c> code</c><00:00:38.550><c> and</c><00:00:38.850><c> then</c><00:00:39.360><c> see</c><00:00:39.629><c> the</c><00:00:39.719><c> result</c>

00:00:40.040 --> 00:00:40.050 align:start position:0%
to kind of code and then see the result
 

00:00:40.050 --> 00:00:42.110 align:start position:0%
to kind of code and then see the result
so<00:00:40.980><c> you</c><00:00:41.010><c> get</c><00:00:41.340><c> understanding</c><00:00:41.579><c> of</c><00:00:41.879><c> what's</c><00:00:42.059><c> going</c>

00:00:42.110 --> 00:00:42.120 align:start position:0%
so you get understanding of what's going
 

00:00:42.120 --> 00:00:44.650 align:start position:0%
so you get understanding of what's going
on<00:00:42.469><c> so</c><00:00:43.469><c> the</c><00:00:43.590><c> first</c><00:00:43.710><c> thing</c><00:00:43.860><c> we're</c><00:00:43.950><c> gonna</c><00:00:44.040><c> do</c><00:00:44.190><c> is</c>

00:00:44.650 --> 00:00:44.660 align:start position:0%
on so the first thing we're gonna do is
 

00:00:44.660 --> 00:00:47.990 align:start position:0%
on so the first thing we're gonna do is
well<00:00:45.660><c> I</c><00:00:45.920><c> download</c><00:00:46.920><c> notepad</c><00:00:47.280><c> plus</c><00:00:47.460><c> plus</c><00:00:47.489><c> you</c>

00:00:47.990 --> 00:00:48.000 align:start position:0%
well I download notepad plus plus you
 

00:00:48.000 --> 00:00:50.209 align:start position:0%
well I download notepad plus plus you
can<00:00:48.660><c> certainly</c><00:00:48.809><c> use</c><00:00:49.020><c> just</c><00:00:49.649><c> rail</c><00:00:49.800><c> in</c><00:00:49.920><c> notepad</c>

00:00:50.209 --> 00:00:50.219 align:start position:0%
can certainly use just rail in notepad
 

00:00:50.219 --> 00:00:52.369 align:start position:0%
can certainly use just rail in notepad
or<00:00:50.460><c> any</c><00:00:50.879><c> default</c><00:00:51.480><c> text</c><00:00:51.750><c> editor</c><00:00:51.989><c> that</c><00:00:52.020><c> comes</c>

00:00:52.369 --> 00:00:52.379 align:start position:0%
or any default text editor that comes
 

00:00:52.379 --> 00:00:54.590 align:start position:0%
or any default text editor that comes
with<00:00:52.530><c> your</c><00:00:52.620><c> operating</c><00:00:52.980><c> system</c><00:00:53.300><c> but</c><00:00:54.300><c> I'll</c><00:00:54.449><c> have</c>

00:00:54.590 --> 00:00:54.600 align:start position:0%
with your operating system but I'll have
 

00:00:54.600 --> 00:00:56.869 align:start position:0%
with your operating system but I'll have
a<00:00:54.629><c> link</c><00:00:54.899><c> in</c><00:00:55.140><c> the</c><00:00:55.590><c> description</c><00:00:55.649><c> for</c><00:00:56.100><c> notepad</c>

00:00:56.869 --> 00:00:56.879 align:start position:0%
a link in the description for notepad
 

00:00:56.879 --> 00:00:59.599 align:start position:0%
a link in the description for notepad
plus<00:00:57.090><c> plus</c><00:00:57.120><c> so</c><00:00:57.390><c> you</c><00:00:57.449><c> can</c><00:00:57.600><c> download</c><00:00:58.340><c> it's</c><00:00:59.340><c> free</c>

00:00:59.599 --> 00:00:59.609 align:start position:0%
plus plus so you can download it's free
 

00:00:59.609 --> 00:01:02.270 align:start position:0%
plus plus so you can download it's free
and<00:01:00.050><c> what</c><00:01:01.050><c> it</c><00:01:01.199><c> can</c><00:01:01.379><c> do</c><00:01:01.590><c> is</c><00:01:01.920><c> it</c><00:01:02.039><c> can</c><00:01:02.070><c> offer</c>

00:01:02.270 --> 00:01:02.280 align:start position:0%
and what it can do is it can offer
 

00:01:02.280 --> 00:01:05.030 align:start position:0%
and what it can do is it can offer
syntax<00:01:02.879><c> highlighting</c><00:01:03.680><c> so</c><00:01:04.680><c> if</c><00:01:04.799><c> you</c><00:01:04.920><c> do</c>

00:01:05.030 --> 00:01:05.040 align:start position:0%
syntax highlighting so if you do
 

00:01:05.040 --> 00:01:07.789 align:start position:0%
syntax highlighting so if you do
download<00:01:05.400><c> it</c><00:01:05.580><c> and</c><00:01:05.700><c> then</c><00:01:06.210><c> open</c><00:01:06.360><c> it</c><00:01:06.510><c> up</c><00:01:06.799><c> that's</c>

00:01:07.789 --> 00:01:07.799 align:start position:0%
download it and then open it up that's
 

00:01:07.799 --> 00:01:10.010 align:start position:0%
download it and then open it up that's
the<00:01:07.920><c> top</c><00:01:08.130><c> here</c><00:01:08.400><c> all</c><00:01:08.640><c> you</c><00:01:08.790><c> have</c><00:01:08.880><c> to</c><00:01:08.970><c> do</c><00:01:09.150><c> is</c><00:01:09.420><c> go</c><00:01:09.960><c> to</c>

00:01:10.010 --> 00:01:10.020 align:start position:0%
the top here all you have to do is go to
 

00:01:10.020 --> 00:01:13.250 align:start position:0%
the top here all you have to do is go to
language<00:01:10.670><c> because</c><00:01:11.670><c> h's</c><00:01:12.119><c> and</c><00:01:12.810><c> click</c><00:01:12.990><c> on</c><00:01:13.110><c> HTML</c>

00:01:13.250 --> 00:01:13.260 align:start position:0%
language because h's and click on HTML
 

00:01:13.260 --> 00:01:15.890 align:start position:0%
language because h's and click on HTML
okay<00:01:14.010><c> and</c><00:01:14.250><c> then</c><00:01:14.460><c> at</c><00:01:15.090><c> the</c><00:01:15.240><c> bottom</c><00:01:15.390><c> left</c><00:01:15.720><c> here</c>

00:01:15.890 --> 00:01:15.900 align:start position:0%
okay and then at the bottom left here
 

00:01:15.900 --> 00:01:17.810 align:start position:0%
okay and then at the bottom left here
it'll<00:01:16.140><c> say</c><00:01:16.320><c> hypertext</c><00:01:17.009><c> markup</c><00:01:17.280><c> language</c><00:01:17.400><c> file</c>

00:01:17.810 --> 00:01:17.820 align:start position:0%
it'll say hypertext markup language file
 

00:01:17.820 --> 00:01:20.179 align:start position:0%
it'll say hypertext markup language file
so<00:01:18.119><c> you</c><00:01:18.180><c> know</c><00:01:18.299><c> it's</c><00:01:18.420><c> an</c><00:01:18.479><c> HTML</c><00:01:18.630><c> file</c><00:01:19.170><c> and</c><00:01:19.409><c> just</c>

00:01:20.179 --> 00:01:20.189 align:start position:0%
so you know it's an HTML file and just
 

00:01:20.189 --> 00:01:21.560 align:start position:0%
so you know it's an HTML file and just
kind<00:01:20.490><c> of</c><00:01:20.520><c> helps</c><00:01:20.759><c> out</c><00:01:20.880><c> whenever</c><00:01:21.390><c> you're</c>

00:01:21.560 --> 00:01:21.570 align:start position:0%
kind of helps out whenever you're
 

00:01:21.570 --> 00:01:23.330 align:start position:0%
kind of helps out whenever you're
whatever<00:01:22.350><c> you're</c><00:01:22.560><c> coding</c><00:01:22.860><c> in</c><00:01:23.070><c> that</c><00:01:23.189><c> language</c>

00:01:23.330 --> 00:01:23.340 align:start position:0%
whatever you're coding in that language
 

00:01:23.340 --> 00:01:28.429 align:start position:0%
whatever you're coding in that language
okay<00:01:24.619><c> so</c><00:01:25.619><c> let's</c><00:01:26.250><c> get</c><00:01:26.340><c> started</c><00:01:27.409><c> the</c><00:01:28.409><c> first</c>

00:01:28.429 --> 00:01:28.439 align:start position:0%
okay so let's get started the first
 

00:01:28.439 --> 00:01:33.469 align:start position:0%
okay so let's get started the first
thing<00:01:28.740><c> we</c><00:01:29.460><c> need</c><00:01:29.670><c> is</c><00:01:30.350><c> doctype</c><00:01:31.350><c> okay</c><00:01:31.890><c> and</c><00:01:32.479><c> then</c>

00:01:33.469 --> 00:01:33.479 align:start position:0%
thing we need is doctype okay and then
 

00:01:33.479 --> 00:01:36.620 align:start position:0%
thing we need is doctype okay and then
we're<00:01:33.840><c> gonna</c><00:01:33.930><c> have</c><00:01:34.170><c> HTML</c><00:01:35.130><c> the</c><00:01:36.119><c> opening</c><00:01:36.479><c> HTML</c>

00:01:36.620 --> 00:01:36.630 align:start position:0%
we're gonna have HTML the opening HTML
 

00:01:36.630 --> 00:01:41.870 align:start position:0%
we're gonna have HTML the opening HTML
tag<00:01:37.110><c> and</c><00:01:38.119><c> we</c><00:01:39.119><c> need</c><00:01:39.329><c> a</c><00:01:39.360><c> title</c><00:01:39.740><c> so</c><00:01:40.740><c> in</c><00:01:41.520><c> order</c><00:01:41.790><c> to</c>

00:01:41.870 --> 00:01:41.880 align:start position:0%
tag and we need a title so in order to
 

00:01:41.880 --> 00:01:44.870 align:start position:0%
tag and we need a title so in order to
do<00:01:41.970><c> that</c><00:01:42.060><c> we</c><00:01:42.240><c> need</c><00:01:42.420><c> a</c><00:01:42.450><c> head</c><00:01:42.720><c> tag</c><00:01:43.670><c> and</c><00:01:44.670><c> then</c>

00:01:44.870 --> 00:01:44.880 align:start position:0%
do that we need a head tag and then
 

00:01:44.880 --> 00:01:48.350 align:start position:0%
do that we need a head tag and then
we're<00:01:45.810><c> gonna</c><00:01:45.899><c> say</c><00:01:46.820><c> actually</c><00:01:47.820><c> indented</c>

00:01:48.350 --> 00:01:48.360 align:start position:0%
we're gonna say actually indented
 

00:01:48.360 --> 00:01:51.620 align:start position:0%
we're gonna say actually indented
however<00:01:49.200><c> it</c><00:01:49.259><c> doesn't</c><00:01:49.380><c> matter</c><00:01:49.560><c> but</c><00:01:50.630><c> indented</c>

00:01:51.620 --> 00:01:51.630 align:start position:0%
however it doesn't matter but indented
 

00:01:51.630 --> 00:01:54.110 align:start position:0%
however it doesn't matter but indented
over<00:01:51.780><c> too</c><00:01:52.020><c> far</c><00:01:52.229><c> but</c><00:01:52.380><c> uh</c><00:01:53.060><c> so</c>

00:01:54.110 --> 00:01:54.120 align:start position:0%
over too far but uh so
 

00:01:54.120 --> 00:01:57.380 align:start position:0%
over too far but uh so
then<00:01:54.300><c> in</c><00:01:54.540><c> the</c><00:01:54.990><c> head</c><00:01:55.170><c> tag</c><00:01:56.000><c> which</c><00:01:57.000><c> holds</c><00:01:57.300><c> the</c>

00:01:57.380 --> 00:01:57.390 align:start position:0%
then in the head tag which holds the
 

00:01:57.390 --> 00:01:59.240 align:start position:0%
then in the head tag which holds the
metadata<00:01:57.720><c> again</c><00:01:58.350><c> we're</c><00:01:58.890><c> going</c><00:01:58.980><c> to</c><00:01:59.040><c> have</c><00:01:59.160><c> our</c>

00:01:59.240 --> 00:01:59.250 align:start position:0%
metadata again we're going to have our
 

00:01:59.250 --> 00:02:01.840 align:start position:0%
metadata again we're going to have our
title<00:01:59.430><c> so</c><00:01:59.970><c> we're</c><00:02:00.450><c> gonna</c><00:02:00.540><c> say</c><00:02:00.840><c> let's</c><00:02:01.200><c> call</c><00:02:01.350><c> this</c>

00:02:01.840 --> 00:02:01.850 align:start position:0%
title so we're gonna say let's call this
 

00:02:01.850 --> 00:02:08.240 align:start position:0%
title so we're gonna say let's call this
text<00:02:02.850><c> demo</c><00:02:03.000><c> and</c><00:02:03.980><c> then</c><00:02:04.980><c> we</c><00:02:05.280><c> want</c><00:02:05.460><c> to</c><00:02:05.520><c> close</c><00:02:07.250><c> we</c>

00:02:08.240 --> 00:02:08.250 align:start position:0%
text demo and then we want to close we
 

00:02:08.250 --> 00:02:10.130 align:start position:0%
text demo and then we want to close we
want<00:02:08.400><c> to</c><00:02:08.460><c> close</c><00:02:08.640><c> the</c><00:02:08.940><c> opening</c><00:02:09.390><c> tag</c><00:02:09.600><c> with</c><00:02:10.110><c> the</c>

00:02:10.130 --> 00:02:10.140 align:start position:0%
want to close the opening tag with the
 

00:02:10.140 --> 00:02:12.890 align:start position:0%
want to close the opening tag with the
closing<00:02:10.440><c> title</c><00:02:10.770><c> tag</c><00:02:10.920><c> alright</c><00:02:11.430><c> and</c><00:02:11.880><c> then</c><00:02:12.690><c> we</c>

00:02:12.890 --> 00:02:12.900 align:start position:0%
closing title tag alright and then we
 

00:02:12.900 --> 00:02:14.420 align:start position:0%
closing title tag alright and then we
want<00:02:13.050><c> to</c><00:02:13.080><c> close</c><00:02:13.230><c> a</c><00:02:13.290><c> head</c><00:02:13.530><c> tag</c><00:02:13.770><c> cuz</c><00:02:14.070><c> we're</c><00:02:14.220><c> done</c>

00:02:14.420 --> 00:02:14.430 align:start position:0%
want to close a head tag cuz we're done
 

00:02:14.430 --> 00:02:20.510 align:start position:0%
want to close a head tag cuz we're done
with<00:02:14.580><c> that</c><00:02:17.180><c> okay</c><00:02:18.180><c> so</c><00:02:18.240><c> next</c><00:02:18.570><c> we</c><00:02:19.110><c> need</c><00:02:19.680><c> to</c>

00:02:20.510 --> 00:02:20.520 align:start position:0%
with that okay so next we need to
 

00:02:20.520 --> 00:02:22.460 align:start position:0%
with that okay so next we need to
display<00:02:20.730><c> our</c><00:02:20.970><c> content</c><00:02:21.480><c> and</c><00:02:21.630><c> again</c><00:02:21.930><c> that's</c><00:02:22.170><c> in</c>

00:02:22.460 --> 00:02:22.470 align:start position:0%
display our content and again that's in
 

00:02:22.470 --> 00:02:25.760 align:start position:0%
display our content and again that's in
the<00:02:22.620><c> body</c><00:02:22.950><c> tag</c><00:02:23.190><c> and</c><00:02:24.410><c> sometimes</c><00:02:25.410><c> what</c><00:02:25.560><c> you</c><00:02:25.650><c> can</c>

00:02:25.760 --> 00:02:25.770 align:start position:0%
the body tag and sometimes what you can
 

00:02:25.770 --> 00:02:30.650 align:start position:0%
the body tag and sometimes what you can
do<00:02:25.890><c> is</c><00:02:26.070><c> you</c><00:02:26.160><c> can</c><00:02:26.220><c> go</c><00:02:26.340><c> ahead</c><00:02:26.460><c> and</c><00:02:28.400><c> like</c><00:02:29.420><c> type</c><00:02:30.420><c> the</c>

00:02:30.650 --> 00:02:30.660 align:start position:0%
do is you can go ahead and like type the
 

00:02:30.660 --> 00:02:33.830 align:start position:0%
do is you can go ahead and like type the
closing<00:02:31.020><c> our</c><00:02:31.650><c> code</c><00:02:32.070><c> the</c><00:02:32.310><c> closing</c><00:02:32.880><c> tag</c><00:02:33.420><c> as</c><00:02:33.690><c> well</c>

00:02:33.830 --> 00:02:33.840 align:start position:0%
closing our code the closing tag as well
 

00:02:33.840 --> 00:02:36.850 align:start position:0%
closing our code the closing tag as well
and<00:02:34.140><c> then</c><00:02:34.380><c> just</c><00:02:35.040><c> go</c><00:02:35.190><c> up</c><00:02:35.250><c> and</c><00:02:35.400><c> like</c><00:02:35.550><c> finish</c>

00:02:36.850 --> 00:02:36.860 align:start position:0%
and then just go up and like finish
 

00:02:36.860 --> 00:02:41.870 align:start position:0%
and then just go up and like finish
everything<00:02:37.860><c> inside</c><00:02:38.070><c> of</c><00:02:38.130><c> it</c><00:02:38.250><c> okay</c><00:02:38.460><c> so</c><00:02:40.640><c> we</c><00:02:41.640><c> want</c>

00:02:41.870 --> 00:02:41.880 align:start position:0%
everything inside of it okay so we want
 

00:02:41.880 --> 00:02:44.300 align:start position:0%
everything inside of it okay so we want
to<00:02:41.940><c> have</c><00:02:42.060><c> all</c><00:02:42.240><c> the</c><00:02:42.360><c> header</c><00:02:42.480><c> tags</c><00:02:43.100><c> you</c><00:02:44.100><c> want</c><00:02:44.130><c> to</c>

00:02:44.300 --> 00:02:44.310 align:start position:0%
to have all the header tags you want to
 

00:02:44.310 --> 00:02:46.160 align:start position:0%
to have all the header tags you want to
see<00:02:44.400><c> how</c><00:02:44.490><c> they</c><00:02:44.610><c> look</c><00:02:44.760><c> so</c><00:02:45.090><c> we'll</c><00:02:45.930><c> start</c><00:02:46.080><c> out</c>

00:02:46.160 --> 00:02:46.170 align:start position:0%
see how they look so we'll start out
 

00:02:46.170 --> 00:02:49.780 align:start position:0%
see how they look so we'll start out
with<00:02:46.230><c> h1</c><00:02:46.710><c> and</c><00:02:47.000><c> we'll</c><00:02:48.000><c> just</c><00:02:48.120><c> call</c><00:02:48.270><c> this</c><00:02:48.450><c> heading</c>

00:02:49.780 --> 00:02:49.790 align:start position:0%
with h1 and we'll just call this heading
 

00:02:49.790 --> 00:02:55.400 align:start position:0%
with h1 and we'll just call this heading
heading<00:02:50.790><c> warm</c><00:02:51.440><c> and</c><00:02:52.910><c> then</c><00:02:53.910><c> close</c><00:02:54.600><c> it</c><00:02:54.630><c> off</c><00:02:54.870><c> okay</c>

00:02:55.400 --> 00:02:55.410 align:start position:0%
heading warm and then close it off okay
 

00:02:55.410 --> 00:02:58.040 align:start position:0%
heading warm and then close it off okay
so<00:02:56.010><c> we're</c><00:02:56.130><c> gonna</c><00:02:56.220><c> do</c><00:02:56.310><c> the</c><00:02:56.430><c> same</c><00:02:56.610><c> thing</c><00:02:57.050><c> I'm</c>

00:02:58.040 --> 00:02:58.050 align:start position:0%
so we're gonna do the same thing I'm
 

00:02:58.050 --> 00:03:02.330 align:start position:0%
so we're gonna do the same thing I'm
gonna<00:02:58.170><c> go</c><00:02:58.320><c> ahead</c><00:02:58.410><c> and</c><00:02:58.500><c> code</c><00:02:58.800><c> this</c><00:02:58.980><c> and</c><00:02:59.750><c> I</c><00:03:01.340><c> will</c>

00:03:02.330 --> 00:03:02.340 align:start position:0%
gonna go ahead and code this and I will
 

00:03:02.340 --> 00:03:05.449 align:start position:0%
gonna go ahead and code this and I will
come<00:03:02.459><c> back</c><00:03:02.489><c> whenever</c><00:03:02.790><c> I'm</c><00:03:02.970><c> done</c><00:03:03.800><c> okay</c><00:03:04.800><c> and</c><00:03:05.010><c> now</c>

00:03:05.449 --> 00:03:05.459 align:start position:0%
come back whenever I'm done okay and now
 

00:03:05.459 --> 00:03:07.220 align:start position:0%
come back whenever I'm done okay and now
we're<00:03:05.580><c> back</c><00:03:05.700><c> so</c><00:03:06.300><c> I</c><00:03:06.330><c> finished</c><00:03:06.540><c> creating</c><00:03:06.900><c> all</c>

00:03:07.220 --> 00:03:07.230 align:start position:0%
we're back so I finished creating all
 

00:03:07.230 --> 00:03:10.490 align:start position:0%
we're back so I finished creating all
six<00:03:07.470><c> lines</c><00:03:07.709><c> of</c><00:03:07.890><c> the</c><00:03:08.250><c> heading</c><00:03:08.459><c> tags</c><00:03:08.670><c> and</c><00:03:09.500><c> what</c>

00:03:10.490 --> 00:03:10.500 align:start position:0%
six lines of the heading tags and what
 

00:03:10.500 --> 00:03:12.590 align:start position:0%
six lines of the heading tags and what
we're<00:03:10.620><c> going</c><00:03:10.739><c> to</c><00:03:10.770><c> do</c><00:03:11.010><c> is</c><00:03:11.340><c> we're</c><00:03:12.270><c> going</c><00:03:12.300><c> to</c><00:03:12.450><c> go</c>

00:03:12.590 --> 00:03:12.600 align:start position:0%
we're going to do is we're going to go
 

00:03:12.600 --> 00:03:14.510 align:start position:0%
we're going to do is we're going to go
ahead<00:03:12.660><c> go</c><00:03:13.170><c> down</c><00:03:13.350><c> below</c><00:03:13.500><c> body</c><00:03:13.890><c> and</c><00:03:14.220><c> we're</c><00:03:14.400><c> gonna</c>

00:03:14.510 --> 00:03:14.520 align:start position:0%
ahead go down below body and we're gonna
 

00:03:14.520 --> 00:03:21.970 align:start position:0%
ahead go down below body and we're gonna
close<00:03:14.820><c> off</c><00:03:15.060><c> our</c><00:03:15.560><c> HTML</c><00:03:16.560><c> tag</c><00:03:17.239><c> okay</c>

00:03:21.970 --> 00:03:21.980 align:start position:0%
 
 

00:03:21.980 --> 00:03:25.059 align:start position:0%
 
go<00:03:22.159><c> ahead</c><00:03:22.310><c> this</c><00:03:22.550><c> isn't</c><00:03:22.879><c> look</c><00:03:23.720><c> too</c><00:03:24.620><c> pretty</c><00:03:24.830><c> but</c>

00:03:25.059 --> 00:03:25.069 align:start position:0%
go ahead this isn't look too pretty but
 

00:03:25.069 --> 00:03:29.410 align:start position:0%
go ahead this isn't look too pretty but
uh<00:03:25.989><c> we're</c><00:03:26.989><c> going</c><00:03:27.170><c> to</c><00:03:27.260><c> save</c><00:03:27.409><c> this</c><00:03:27.560><c> and</c><00:03:28.420><c> if</c>

00:03:29.410 --> 00:03:29.420 align:start position:0%
uh we're going to save this and if
 

00:03:29.420 --> 00:03:31.270 align:start position:0%
uh we're going to save this and if
you're<00:03:29.599><c> enough</c><00:03:29.780><c> I</c><00:03:29.870><c> plus</c><00:03:30.080><c> plus</c><00:03:30.110><c> save</c><00:03:31.099><c> guys</c>

00:03:31.270 --> 00:03:31.280 align:start position:0%
you're enough I plus plus save guys
 

00:03:31.280 --> 00:03:34.180 align:start position:0%
you're enough I plus plus save guys
doesn't<00:03:31.760><c> matter</c><00:03:31.959><c> but</c><00:03:32.959><c> just</c><00:03:33.200><c> go</c><00:03:33.319><c> to</c><00:03:33.379><c> save</c><00:03:33.799><c> as</c>

00:03:34.180 --> 00:03:34.190 align:start position:0%
doesn't matter but just go to save as
 

00:03:34.190 --> 00:03:38.350 align:start position:0%
doesn't matter but just go to save as
and<00:03:34.670><c> we're</c><00:03:34.819><c> gonna</c><00:03:34.970><c> do</c><00:03:36.340><c> I'll</c><00:03:37.340><c> already</c><00:03:37.640><c> had</c><00:03:38.150><c> one</c>

00:03:38.350 --> 00:03:38.360 align:start position:0%
and we're gonna do I'll already had one
 

00:03:38.360 --> 00:03:40.809 align:start position:0%
and we're gonna do I'll already had one
so<00:03:38.660><c> we're</c><00:03:38.810><c> gonna</c><00:03:38.930><c> do</c><00:03:39.170><c> text</c><00:03:39.590><c> call</c><00:03:40.459><c> whatever</c><00:03:40.730><c> you</c>

00:03:40.809 --> 00:03:40.819 align:start position:0%
so we're gonna do text call whatever you
 

00:03:40.819 --> 00:03:47.740 align:start position:0%
so we're gonna do text call whatever you
want<00:03:42.430><c> dot</c><00:03:43.510><c> HTML</c><00:03:45.489><c> okay</c><00:03:46.489><c> so</c><00:03:47.390><c> we're</c><00:03:47.629><c> done</c>

00:03:47.740 --> 00:03:47.750 align:start position:0%
want dot HTML okay so we're done
 

00:03:47.750 --> 00:03:52.420 align:start position:0%
want dot HTML okay so we're done
minimize<00:03:48.709><c> this</c><00:03:48.890><c> and</c><00:03:50.530><c> you'll</c><00:03:51.530><c> have</c><00:03:51.890><c> it'll</c>

00:03:52.420 --> 00:03:52.430 align:start position:0%
minimize this and you'll have it'll
 

00:03:52.430 --> 00:03:54.699 align:start position:0%
minimize this and you'll have it'll
create<00:03:52.670><c> the</c><00:03:52.879><c> HTML</c><00:03:53.060><c> file</c><00:03:53.510><c> on</c><00:03:53.750><c> the</c><00:03:53.930><c> desktop</c><00:03:54.319><c> for</c>

00:03:54.699 --> 00:03:54.709 align:start position:0%
create the HTML file on the desktop for
 

00:03:54.709 --> 00:03:59.640 align:start position:0%
create the HTML file on the desktop for
you<00:03:54.829><c> so</c><00:03:55.700><c> just</c><00:03:55.940><c> go</c><00:03:56.120><c> ahead</c><00:03:56.329><c> and</c><00:03:56.450><c> open</c><00:03:56.920><c> it</c><00:03:57.920><c> up</c><00:03:58.180><c> and</c>

00:03:59.640 --> 00:03:59.650 align:start position:0%
you so just go ahead and open it up and
 

00:03:59.650 --> 00:04:04.900 align:start position:0%
you so just go ahead and open it up and
let's<00:04:01.090><c> move</c><00:04:02.090><c> in</c><00:04:02.329><c> here</c><00:04:02.569><c> okay</c><00:04:03.049><c> so</c><00:04:03.670><c> as</c><00:04:04.670><c> you</c><00:04:04.730><c> can</c>

00:04:04.900 --> 00:04:04.910 align:start position:0%
let's move in here okay so as you can
 

00:04:04.910 --> 00:04:07.900 align:start position:0%
let's move in here okay so as you can
see<00:04:05.150><c> all</c><00:04:05.599><c> of</c><00:04:05.660><c> them</c><00:04:06.079><c> are</c><00:04:06.319><c> different</c><00:04:06.700><c> font</c><00:04:07.700><c> sizes</c>

00:04:07.900 --> 00:04:07.910 align:start position:0%
see all of them are different font sizes
 

00:04:07.910 --> 00:04:11.440 align:start position:0%
see all of them are different font sizes
and<00:04:08.299><c> they're</c><00:04:08.569><c> all</c><00:04:08.690><c> bold</c><00:04:09.310><c> so</c><00:04:10.310><c> heading</c><00:04:10.910><c> ones</c><00:04:11.239><c> the</c>

00:04:11.440 --> 00:04:11.450 align:start position:0%
and they're all bold so heading ones the
 

00:04:11.450 --> 00:04:14.250 align:start position:0%
and they're all bold so heading ones the
largest<00:04:11.840><c> heading</c><00:04:12.290><c> six</c><00:04:12.560><c> being</c><00:04:12.890><c> the</c><00:04:13.280><c> smallest</c>

00:04:14.250 --> 00:04:14.260 align:start position:0%
largest heading six being the smallest
 

00:04:14.260 --> 00:04:17.979 align:start position:0%
largest heading six being the smallest
it's<00:04:15.260><c> about</c><00:04:15.470><c> a</c><00:04:15.590><c> little</c><00:04:15.799><c> bit</c><00:04:15.980><c> okay</c><00:04:16.639><c> so</c><00:04:16.900><c> let's</c><00:04:17.900><c> go</c>

00:04:17.979 --> 00:04:17.989 align:start position:0%
it's about a little bit okay so let's go
 

00:04:17.989 --> 00:04:22.689 align:start position:0%
it's about a little bit okay so let's go
back<00:04:18.139><c> and</c><00:04:19.570><c> with</c><00:04:20.570><c> go</c><00:04:21.199><c> back</c><00:04:21.380><c> into</c><00:04:21.620><c> the</c><00:04:22.190><c> body</c><00:04:22.400><c> tag</c>

00:04:22.689 --> 00:04:22.699 align:start position:0%
back and with go back into the body tag
 

00:04:22.699 --> 00:04:26.500 align:start position:0%
back and with go back into the body tag
and<00:04:22.970><c> let's</c><00:04:23.840><c> write</c><00:04:24.139><c> a</c><00:04:24.410><c> P</c><00:04:25.280><c> tag</c><00:04:25.550><c> first</c><00:04:26.150><c> which</c>

00:04:26.500 --> 00:04:26.510 align:start position:0%
and let's write a P tag first which
 

00:04:26.510 --> 00:04:29.770 align:start position:0%
and let's write a P tag first which
stands<00:04:26.690><c> for</c><00:04:26.750><c> paragraph</c><00:04:27.110><c> again</c><00:04:27.349><c> and</c><00:04:28.780><c> doesn't</c>

00:04:29.770 --> 00:04:29.780 align:start position:0%
stands for paragraph again and doesn't
 

00:04:29.780 --> 00:04:31.570 align:start position:0%
stands for paragraph again and doesn't
matter<00:04:29.990><c> which</c><00:04:30.110><c> but</c><00:04:30.320><c> here</c><00:04:30.470><c> just</c><00:04:30.650><c> put</c><00:04:30.860><c> like</c><00:04:31.280><c> some</c>

00:04:31.570 --> 00:04:31.580 align:start position:0%
matter which but here just put like some
 

00:04:31.580 --> 00:04:38.500 align:start position:0%
matter which but here just put like some
small<00:04:32.210><c> sentence</c><00:04:35.320><c> and</c><00:04:36.320><c> then</c><00:04:36.889><c> of</c><00:04:37.760><c> course</c><00:04:37.849><c> as</c>

00:04:38.500 --> 00:04:38.510 align:start position:0%
small sentence and then of course as
 

00:04:38.510 --> 00:04:41.790 align:start position:0%
small sentence and then of course as
always<00:04:39.380><c> make</c><00:04:40.190><c> sure</c><00:04:40.220><c> that</c><00:04:40.400><c> you</c><00:04:41.060><c> close</c><00:04:41.240><c> it</c><00:04:41.389><c> I</c>

00:04:41.790 --> 00:04:41.800 align:start position:0%
always make sure that you close it I
 

00:04:41.800 --> 00:04:44.260 align:start position:0%
always make sure that you close it I
will<00:04:42.800><c> repeat</c><00:04:43.039><c> myself</c><00:04:43.070><c> a</c><00:04:43.430><c> lot</c><00:04:43.580><c> because</c>

00:04:44.260 --> 00:04:44.270 align:start position:0%
will repeat myself a lot because
 

00:04:44.270 --> 00:04:47.290 align:start position:0%
will repeat myself a lot because
repetition<00:04:45.229><c> is</c><00:04:45.380><c> great</c><00:04:45.590><c> and</c><00:04:46.000><c> it</c><00:04:47.000><c> will</c><00:04:47.120><c> help</c><00:04:47.180><c> you</c>

00:04:47.290 --> 00:04:47.300 align:start position:0%
repetition is great and it will help you
 

00:04:47.300 --> 00:04:49.270 align:start position:0%
repetition is great and it will help you
become<00:04:47.389><c> a</c><00:04:47.510><c> better</c><00:04:47.660><c> programmer</c><00:04:48.190><c> and</c><00:04:49.190><c> I'll</c>

00:04:49.270 --> 00:04:49.280 align:start position:0%
become a better programmer and I'll
 

00:04:49.280 --> 00:04:50.890 align:start position:0%
become a better programmer and I'll
always<00:04:49.520><c> try</c><00:04:50.000><c> to</c><00:04:50.030><c> like</c><00:04:50.180><c> remind</c><00:04:50.419><c> you</c><00:04:50.599><c> certain</c>

00:04:50.890 --> 00:04:50.900 align:start position:0%
always try to like remind you certain
 

00:04:50.900 --> 00:04:52.270 align:start position:0%
always try to like remind you certain
things<00:04:51.080><c> even</c><00:04:51.560><c> though</c><00:04:51.620><c> they</c><00:04:51.740><c> were</c><00:04:51.830><c> done</c><00:04:52.010><c> didn't</c>

00:04:52.270 --> 00:04:52.280 align:start position:0%
things even though they were done didn't
 

00:04:52.280 --> 00:04:55.990 align:start position:0%
things even though they were done didn't
simple<00:04:52.639><c> things</c><00:04:52.849><c> it's</c><00:04:53.210><c> still</c><00:04:53.599><c> like</c><00:04:55.000><c> get</c>

00:04:55.990 --> 00:04:56.000 align:start position:0%
simple things it's still like get
 

00:04:56.000 --> 00:05:01.600 align:start position:0%
simple things it's still like get
ingrained<00:04:56.419><c> in</c><00:04:57.410><c> your</c><00:04:57.740><c> head</c><00:04:57.979><c> okay</c><00:05:00.099><c> so</c><00:05:01.099><c> if</c><00:05:01.460><c> we</c>

00:05:01.600 --> 00:05:01.610 align:start position:0%
ingrained in your head okay so if we
 

00:05:01.610 --> 00:05:04.710 align:start position:0%
ingrained in your head okay so if we
save<00:05:01.789><c> this</c><00:05:01.940><c> just</c><00:05:02.240><c> go</c><00:05:02.539><c> ahead</c><00:05:02.660><c> and</c><00:05:02.750><c> save</c><00:05:02.870><c> this</c>

00:05:04.710 --> 00:05:04.720 align:start position:0%
save this just go ahead and save this
 

00:05:04.720 --> 00:05:10.750 align:start position:0%
save this just go ahead and save this
close<00:05:05.720><c> that</c><00:05:07.780><c> reopen</c><00:05:09.099><c> here</c><00:05:10.099><c> was</c><00:05:10.220><c> the</c><00:05:10.280><c> file</c><00:05:10.520><c> and</c>

00:05:10.750 --> 00:05:10.760 align:start position:0%
close that reopen here was the file and
 

00:05:10.760 --> 00:05:14.080 align:start position:0%
close that reopen here was the file and
there<00:05:11.720><c> is</c><00:05:11.840><c> the</c><00:05:11.960><c> paragraph</c><00:05:12.289><c> tag</c><00:05:12.500><c> and</c><00:05:12.970><c> as</c><00:05:13.970><c> you</c>

00:05:14.080 --> 00:05:14.090 align:start position:0%
there is the paragraph tag and as you
 

00:05:14.090 --> 00:05:16.420 align:start position:0%
there is the paragraph tag and as you
can<00:05:14.300><c> note</c><00:05:14.510><c> as</c><00:05:14.720><c> you</c><00:05:14.750><c> also</c><00:05:15.110><c> may</c><00:05:15.889><c> or</c><00:05:16.130><c> may</c><00:05:16.220><c> not</c><00:05:16.310><c> have</c>

00:05:16.420 --> 00:05:16.430 align:start position:0%
can note as you also may or may not have
 

00:05:16.430 --> 00:05:20.159 align:start position:0%
can note as you also may or may not have
noticed<00:05:16.729><c> that</c><00:05:17.169><c> each</c><00:05:18.169><c> of</c><00:05:18.199><c> these</c><00:05:18.470><c> so</c><00:05:18.860><c> far</c><00:05:19.220><c> have</c><00:05:19.940><c> a</c>

00:05:20.159 --> 00:05:20.169 align:start position:0%
noticed that each of these so far have a
 

00:05:20.169 --> 00:05:24.790 align:start position:0%
noticed that each of these so far have a
of<00:05:21.169><c> a</c><00:05:21.349><c> break</c><00:05:21.590><c> line</c><00:05:22.300><c> have</c><00:05:23.300><c> a</c><00:05:23.330><c> break</c><00:05:23.660><c> above</c><00:05:24.530><c> and</c>

00:05:24.790 --> 00:05:24.800 align:start position:0%
of a break line have a break above and
 

00:05:24.800 --> 00:05:34.280 align:start position:0%
of a break line have a break above and
below<00:05:25.160><c> them</c><00:05:26.229><c> so</c><00:05:27.430><c> if</c><00:05:28.430><c> I</c><00:05:28.610><c> were</c><00:05:28.760><c> to</c><00:05:28.789><c> just</c><00:05:30.370><c> copy</c>

00:05:34.280 --> 00:05:34.290 align:start position:0%
 
 

00:05:34.290 --> 00:05:36.300 align:start position:0%
 
see<00:05:35.290><c> put</c><00:05:35.590><c> that</c><00:05:35.740><c> three</c><00:05:35.919><c> times</c>

00:05:36.300 --> 00:05:36.310 align:start position:0%
see put that three times
 

00:05:36.310 --> 00:05:38.340 align:start position:0%
see put that three times
okay<00:05:36.550><c> just</c><00:05:36.850><c> copy</c><00:05:37.600><c> that</c><00:05:37.780><c> line</c><00:05:37.870><c> three</c><00:05:38.139><c> times</c>

00:05:38.340 --> 00:05:38.350 align:start position:0%
okay just copy that line three times
 

00:05:38.350 --> 00:05:40.710 align:start position:0%
okay just copy that line three times
without<00:05:38.590><c> any</c><00:05:38.860><c> tags</c><00:05:39.310><c> it's</c><00:05:39.699><c> just</c><00:05:40.210><c> text</c><00:05:40.449><c> at</c><00:05:40.600><c> this</c>

00:05:40.710 --> 00:05:40.720 align:start position:0%
without any tags it's just text at this
 

00:05:40.720 --> 00:05:46.790 align:start position:0%
without any tags it's just text at this
point<00:05:42.180><c> so</c><00:05:43.180><c> let's</c><00:05:43.210><c> close</c><00:05:43.930><c> that</c><00:05:45.180><c> reopen</c><00:05:46.180><c> it</c><00:05:46.300><c> as</c>

00:05:46.790 --> 00:05:46.800 align:start position:0%
point so let's close that reopen it as
 

00:05:46.800 --> 00:05:50.219 align:start position:0%
point so let's close that reopen it as
you<00:05:47.800><c> can</c><00:05:47.889><c> see</c><00:05:48.180><c> because</c><00:05:49.180><c> it's</c><00:05:49.330><c> just</c><00:05:49.570><c> text</c><00:05:49.870><c> we</c>

00:05:50.219 --> 00:05:50.229 align:start position:0%
you can see because it's just text we
 

00:05:50.229 --> 00:05:52.560 align:start position:0%
you can see because it's just text we
haven't<00:05:50.590><c> described</c><00:05:51.340><c> to</c><00:05:51.729><c> the</c><00:05:51.820><c> browser</c><00:05:52.060><c> how</c><00:05:52.540><c> I</c>

00:05:52.560 --> 00:05:52.570 align:start position:0%
haven't described to the browser how I
 

00:05:52.570 --> 00:05:55.140 align:start position:0%
haven't described to the browser how I
want<00:05:52.840><c> this</c><00:05:52.990><c> displayed</c><00:05:53.410><c> using</c><00:05:53.650><c> tags</c><00:05:54.150><c> even</c>

00:05:55.140 --> 00:05:55.150 align:start position:0%
want this displayed using tags even
 

00:05:55.150 --> 00:05:58.500 align:start position:0%
want this displayed using tags even
though<00:05:56.010><c> we</c><00:05:57.010><c> indent</c><00:05:57.370><c> into</c><00:05:57.639><c> the</c><00:05:57.729><c> next</c><00:05:58.000><c> line</c><00:05:58.210><c> in</c>

00:05:58.500 --> 00:05:58.510 align:start position:0%
though we indent into the next line in
 

00:05:58.510 --> 00:06:01.650 align:start position:0%
though we indent into the next line in
the<00:05:59.350><c> code</c><00:05:59.740><c> that</c><00:06:00.190><c> doesn't</c><00:06:01.060><c> matter</c><00:06:01.330><c> the</c><00:06:01.479><c> browser</c>

00:06:01.650 --> 00:06:01.660 align:start position:0%
the code that doesn't matter the browser
 

00:06:01.660 --> 00:06:04.740 align:start position:0%
the code that doesn't matter the browser
because<00:06:02.380><c> we</c><00:06:02.650><c> haven't</c><00:06:02.830><c> described</c><00:06:03.250><c> it</c><00:06:03.750><c> we</c>

00:06:04.740 --> 00:06:04.750 align:start position:0%
because we haven't described it we
 

00:06:04.750 --> 00:06:06.629 align:start position:0%
because we haven't described it we
haven't<00:06:04.960><c> given</c><00:06:05.080><c> a</c><00:06:05.169><c> description</c><00:06:05.380><c> to</c><00:06:06.040><c> go</c><00:06:06.370><c> to</c><00:06:06.400><c> the</c>

00:06:06.629 --> 00:06:06.639 align:start position:0%
haven't given a description to go to the
 

00:06:06.639 --> 00:06:08.730 align:start position:0%
haven't given a description to go to the
next<00:06:06.820><c> line</c><00:06:07.000><c> all</c><00:06:07.630><c> right</c><00:06:07.840><c> so</c><00:06:08.050><c> as</c><00:06:08.320><c> far</c><00:06:08.470><c> as</c><00:06:08.530><c> its</c>

00:06:08.730 --> 00:06:08.740 align:start position:0%
next line all right so as far as its
 

00:06:08.740 --> 00:06:11.010 align:start position:0%
next line all right so as far as its
concerned<00:06:08.800><c> there</c><00:06:09.729><c> are</c><00:06:10.060><c> no</c><00:06:10.240><c> white</c><00:06:10.389><c> spaces</c><00:06:10.660><c> and</c>

00:06:11.010 --> 00:06:11.020 align:start position:0%
concerned there are no white spaces and
 

00:06:11.020 --> 00:06:13.170 align:start position:0%
concerned there are no white spaces and
it's<00:06:11.860><c> just</c><00:06:12.520><c> putting</c><00:06:12.760><c> them</c><00:06:12.880><c> all</c><00:06:12.970><c> on</c><00:06:13.060><c> the</c><00:06:13.150><c> same</c>

00:06:13.170 --> 00:06:13.180 align:start position:0%
it's just putting them all on the same
 

00:06:13.180 --> 00:06:16.980 align:start position:0%
it's just putting them all on the same
line<00:06:13.570><c> okay</c><00:06:15.360><c> something</c><00:06:16.360><c> else</c><00:06:16.389><c> to</c><00:06:16.540><c> keep</c><00:06:16.660><c> in</c><00:06:16.750><c> mind</c>

00:06:16.980 --> 00:06:16.990 align:start position:0%
line okay something else to keep in mind
 

00:06:16.990 --> 00:06:19.820 align:start position:0%
line okay something else to keep in mind
also<00:06:18.210><c> all</c><00:06:19.210><c> right</c>

00:06:19.820 --> 00:06:19.830 align:start position:0%
also all right
 

00:06:19.830 --> 00:06:25.080 align:start position:0%
also all right
so<00:06:20.830><c> open</c><00:06:21.010><c> this</c><00:06:21.070><c> back</c><00:06:21.250><c> up</c><00:06:23.010><c> go</c><00:06:24.010><c> gonna</c><00:06:24.280><c> go</c><00:06:25.030><c> to</c>

00:06:25.080 --> 00:06:25.090 align:start position:0%
so open this back up go gonna go to
 

00:06:25.090 --> 00:06:28.860 align:start position:0%
so open this back up go gonna go to
those<00:06:25.300><c> and</c><00:06:26.370><c> the</c><00:06:27.370><c> next</c><00:06:27.580><c> thing</c><00:06:27.700><c> one</c><00:06:27.850><c> to</c><00:06:28.180><c> do</c><00:06:28.389><c> just</c>

00:06:28.860 --> 00:06:28.870 align:start position:0%
those and the next thing one to do just
 

00:06:28.870 --> 00:06:31.980 align:start position:0%
those and the next thing one to do just
go<00:06:28.990><c> over</c><00:06:29.200><c> a</c><00:06:29.229><c> few</c><00:06:29.320><c> of</c><00:06:29.590><c> them</c><00:06:29.740><c> are</c><00:06:30.520><c> the</c><00:06:30.990><c> text</c>

00:06:31.980 --> 00:06:31.990 align:start position:0%
go over a few of them are the text
 

00:06:31.990 --> 00:06:35.250 align:start position:0%
go over a few of them are the text
formatting<00:06:32.350><c> so</c><00:06:32.740><c> like</c><00:06:32.919><c> bold</c><00:06:33.840><c> italicized</c><00:06:34.840><c> and</c>

00:06:35.250 --> 00:06:35.260 align:start position:0%
formatting so like bold italicized and
 

00:06:35.260 --> 00:06:39.060 align:start position:0%
formatting so like bold italicized and
like<00:06:35.650><c> subscript</c><00:06:36.340><c> superscript</c><00:06:37.500><c> so</c><00:06:38.500><c> if</c><00:06:38.770><c> we</c><00:06:38.919><c> go</c>

00:06:39.060 --> 00:06:39.070 align:start position:0%
like subscript superscript so if we go
 

00:06:39.070 --> 00:06:42.450 align:start position:0%
like subscript superscript so if we go
over<00:06:39.100><c> to</c><00:06:39.600><c> example</c><00:06:40.600><c> let's</c><00:06:41.530><c> make</c><00:06:41.740><c> this</c><00:06:41.860><c> bold</c><00:06:42.160><c> and</c>

00:06:42.450 --> 00:06:42.460 align:start position:0%
over to example let's make this bold and
 

00:06:42.460 --> 00:06:45.990 align:start position:0%
over to example let's make this bold and
you<00:06:42.580><c> can</c><00:06:42.729><c> do</c><00:06:42.880><c> this</c><00:06:43.060><c> by</c><00:06:43.210><c> just</c><00:06:44.700><c> by</c><00:06:45.700><c> just</c><00:06:45.760><c> giving</c>

00:06:45.990 --> 00:06:46.000 align:start position:0%
you can do this by just by just giving
 

00:06:46.000 --> 00:06:48.830 align:start position:0%
you can do this by just by just giving
it<00:06:46.240><c> a</c><00:06:46.380><c> B</c><00:06:47.380><c> tag</c><00:06:47.680><c> and</c><00:06:48.220><c> for</c><00:06:48.400><c> bold</c>

00:06:48.830 --> 00:06:48.840 align:start position:0%
it a B tag and for bold
 

00:06:48.840 --> 00:06:54.600 align:start position:0%
it a B tag and for bold
let's<00:06:49.840><c> give</c><00:06:51.450><c> paragraph</c><00:06:52.450><c> and</c><00:06:52.840><c> I</c><00:06:53.770><c> tag</c><00:06:54.010><c> which</c>

00:06:54.600 --> 00:06:54.610 align:start position:0%
let's give paragraph and I tag which
 

00:06:54.610 --> 00:06:57.390 align:start position:0%
let's give paragraph and I tag which
stands<00:06:55.599><c> which</c><00:06:55.810><c> stands</c><00:06:56.770><c> for</c><00:06:56.860><c> it's</c><00:06:57.039><c> a</c><00:06:57.130><c> tell</c><00:06:57.310><c> us</c>

00:06:57.390 --> 00:06:57.400 align:start position:0%
stands which stands for it's a tell us
 

00:06:57.400 --> 00:07:00.469 align:start position:0%
stands which stands for it's a tell us
that<00:06:57.550><c> we're</c><00:06:57.700><c> tell</c><00:06:57.970><c> sizing</c><00:06:58.360><c> that</c><00:06:58.889><c> that</c><00:06:59.889><c> phrase</c>

00:07:00.469 --> 00:07:00.479 align:start position:0%
that we're tell sizing that that phrase
 

00:07:00.479 --> 00:07:04.890 align:start position:0%
that we're tell sizing that that phrase
or<00:07:01.479><c> in</c><00:07:01.539><c> this</c><00:07:01.599><c> case</c><00:07:01.780><c> word</c><00:07:03.389><c> okay</c><00:07:04.419><c> opening</c>

00:07:04.890 --> 00:07:04.900 align:start position:0%
or in this case word okay opening
 

00:07:04.900 --> 00:07:09.150 align:start position:0%
or in this case word okay opening
closing<00:07:05.110><c> tags</c><00:07:05.380><c> and</c><00:07:06.330><c> then</c><00:07:07.330><c> let's</c><00:07:07.660><c> do</c><00:07:07.990><c> let's</c><00:07:08.800><c> put</c>

00:07:09.150 --> 00:07:09.160 align:start position:0%
closing tags and then let's do let's put
 

00:07:09.160 --> 00:07:13.230 align:start position:0%
closing tags and then let's do let's put
tag<00:07:09.430><c> a</c><00:07:09.760><c> subscript</c><00:07:10.410><c> so</c><00:07:11.410><c> it</c><00:07:11.620><c> always</c><00:07:11.860><c> do</c><00:07:12.070><c> is</c><00:07:12.310><c> make</c>

00:07:13.230 --> 00:07:13.240 align:start position:0%
tag a subscript so it always do is make
 

00:07:13.240 --> 00:07:18.510 align:start position:0%
tag a subscript so it always do is make
a<00:07:13.270><c> sub</c><00:07:13.750><c> tag</c><00:07:14.260><c> and</c><00:07:14.590><c> then</c><00:07:15.010><c> close</c><00:07:15.220><c> it</c><00:07:16.139><c> and</c><00:07:17.520><c> then</c>

00:07:18.510 --> 00:07:18.520 align:start position:0%
a sub tag and then close it and then
 

00:07:18.520 --> 00:07:22.770 align:start position:0%
a sub tag and then close it and then
let's<00:07:18.789><c> go</c><00:07:18.970><c> over</c><00:07:19.000><c> to</c><00:07:20.910><c> skull</c><00:07:21.910><c> wood</c><00:07:22.090><c> beginning</c><00:07:22.450><c> to</c>

00:07:22.770 --> 00:07:22.780 align:start position:0%
let's go over to skull wood beginning to
 

00:07:22.780 --> 00:07:26.800 align:start position:0%
let's go over to skull wood beginning to
this

00:07:26.800 --> 00:07:26.810 align:start position:0%
 
 

00:07:26.810 --> 00:07:28.060 align:start position:0%
 
it<00:07:27.020><c> doesn't</c><00:07:27.200><c> actually</c><00:07:27.260><c> matter</c><00:07:27.470><c> what</c><00:07:27.710><c> word</c><00:07:27.920><c> you</c>

00:07:28.060 --> 00:07:28.070 align:start position:0%
it doesn't actually matter what word you
 

00:07:28.070 --> 00:07:30.129 align:start position:0%
it doesn't actually matter what word you
use<00:07:28.190><c> just</c><00:07:28.610><c> pick</c><00:07:28.820><c> one</c><00:07:29.030><c> and</c><00:07:29.330><c> just</c><00:07:29.840><c> so</c><00:07:29.930><c> you</c><00:07:30.020><c> see</c>

00:07:30.129 --> 00:07:30.139 align:start position:0%
use just pick one and just so you see
 

00:07:30.139 --> 00:07:32.710 align:start position:0%
use just pick one and just so you see
what<00:07:30.320><c> it</c><00:07:30.470><c> looks</c><00:07:30.620><c> like</c><00:07:30.770><c> and</c><00:07:31.120><c> this</c><00:07:32.120><c> is</c><00:07:32.180><c> for</c>

00:07:32.710 --> 00:07:32.720 align:start position:0%
what it looks like and this is for
 

00:07:32.720 --> 00:07:39.850 align:start position:0%
what it looks like and this is for
superscript<00:07:33.200><c> it's</c><00:07:33.710><c> just</c><00:07:33.920><c> SCP</c><00:07:38.080><c> okay</c><00:07:39.080><c> so</c><00:07:39.740><c> we've</c>

00:07:39.850 --> 00:07:39.860 align:start position:0%
superscript it's just SCP okay so we've
 

00:07:39.860 --> 00:07:44.200 align:start position:0%
superscript it's just SCP okay so we've
done<00:07:39.980><c> four</c><00:07:40.160><c> things</c><00:07:40.370><c> here</c><00:07:40.610><c> right</c><00:07:42.460><c> we</c><00:07:43.460><c> have</c><00:07:43.490><c> a</c>

00:07:44.200 --> 00:07:44.210 align:start position:0%
done four things here right we have a
 

00:07:44.210 --> 00:07:45.970 align:start position:0%
done four things here right we have a
superscript<00:07:44.690><c> on</c><00:07:44.900><c> one</c><00:07:45.500><c> word</c>

00:07:45.970 --> 00:07:45.980 align:start position:0%
superscript on one word
 

00:07:45.980 --> 00:07:48.310 align:start position:0%
superscript on one word
we<00:07:46.520><c> have</c><00:07:46.610><c> bold</c><00:07:46.880><c> around</c><00:07:47.300><c> the</c><00:07:47.389><c> example</c><00:07:47.780><c> we</c>

00:07:48.310 --> 00:07:48.320 align:start position:0%
we have bold around the example we
 

00:07:48.320 --> 00:07:51.220 align:start position:0%
we have bold around the example we
italicized<00:07:48.889><c> paragraph</c><00:07:49.370><c> and</c><00:07:49.610><c> we</c><00:07:50.230><c> subscript</c>

00:07:51.220 --> 00:07:51.230 align:start position:0%
italicized paragraph and we subscript
 

00:07:51.230 --> 00:07:57.159 align:start position:0%
italicized paragraph and we subscript
tag<00:07:52.300><c> so</c><00:07:53.300><c> if</c><00:07:53.389><c> we</c><00:07:53.510><c> save</c><00:07:53.720><c> this</c><00:07:55.750><c> reopen</c><00:07:56.750><c> our</c><00:07:56.870><c> tech</c>

00:07:57.159 --> 00:07:57.169 align:start position:0%
tag so if we save this reopen our tech
 

00:07:57.169 --> 00:08:01.630 align:start position:0%
tag so if we save this reopen our tech
AML<00:07:57.770><c> file</c><00:07:58.090><c> here</c><00:07:59.090><c> you</c><00:07:59.930><c> can</c><00:08:00.140><c> see</c><00:08:00.380><c> this</c><00:08:01.370><c> is</c>

00:08:01.630 --> 00:08:01.640 align:start position:0%
AML file here you can see this is
 

00:08:01.640 --> 00:08:04.570 align:start position:0%
AML file here you can see this is
superscript<00:08:02.210><c> example</c><00:08:02.990><c> is</c><00:08:03.169><c> bold</c><00:08:03.470><c> paragraph</c><00:08:04.430><c> is</c>

00:08:04.570 --> 00:08:04.580 align:start position:0%
superscript example is bold paragraph is
 

00:08:04.580 --> 00:08:08.320 align:start position:0%
superscript example is bold paragraph is
italicized<00:08:05.090><c> and</c><00:08:05.360><c> the</c><00:08:05.780><c> tag</c><00:08:06.050><c> is</c><00:08:06.380><c> a</c><00:08:07.330><c> subscript</c>

00:08:08.320 --> 00:08:08.330 align:start position:0%
italicized and the tag is a subscript
 

00:08:08.330 --> 00:08:11.260 align:start position:0%
italicized and the tag is a subscript
okay<00:08:09.080><c> there</c><00:08:10.040><c> there</c><00:08:10.370><c> budget</c><00:08:10.640><c> there</c><00:08:10.910><c> are</c><00:08:10.970><c> quite</c>

00:08:11.260 --> 00:08:11.270 align:start position:0%
okay there there budget there are quite
 

00:08:11.270 --> 00:08:14.200 align:start position:0%
okay there there budget there are quite
a<00:08:11.300><c> few</c><00:08:11.360><c> others</c><00:08:11.720><c> that</c><00:08:12.350><c> do</c><00:08:12.470><c> this</c><00:08:12.620><c> but</c><00:08:13.180><c> you</c><00:08:14.180><c> can</c>

00:08:14.200 --> 00:08:14.210 align:start position:0%
a few others that do this but you can
 

00:08:14.210 --> 00:08:16.450 align:start position:0%
a few others that do this but you can
always<00:08:14.480><c> look</c><00:08:14.870><c> them</c><00:08:15.050><c> up</c><00:08:15.140><c> if</c><00:08:15.320><c> you</c><00:08:15.440><c> need</c><00:08:15.620><c> to</c><00:08:15.830><c> but</c>

00:08:16.450 --> 00:08:16.460 align:start position:0%
always look them up if you need to but
 

00:08:16.460 --> 00:08:18.190 align:start position:0%
always look them up if you need to but
these<00:08:16.760><c> are</c><00:08:16.970><c> more</c><00:08:17.240><c> of</c><00:08:17.660><c> like</c><00:08:17.960><c> the</c><00:08:18.080><c> more</c>

00:08:18.190 --> 00:08:18.200 align:start position:0%
these are more of like the more
 

00:08:18.200 --> 00:08:20.320 align:start position:0%
these are more of like the more
important<00:08:18.530><c> ones</c><00:08:18.710><c> or</c><00:08:19.040><c> not</c><00:08:19.760><c> important</c><00:08:20.150><c> just</c>

00:08:20.320 --> 00:08:20.330 align:start position:0%
important ones or not important just
 

00:08:20.330 --> 00:08:22.990 align:start position:0%
important ones or not important just
commonly<00:08:21.110><c> used</c><00:08:21.290><c> ones</c><00:08:21.700><c> actually</c><00:08:22.700><c> use</c><00:08:22.880><c> in</c><00:08:22.970><c> the</c>

00:08:22.990 --> 00:08:23.000 align:start position:0%
commonly used ones actually use in the
 

00:08:23.000 --> 00:08:28.150 align:start position:0%
commonly used ones actually use in the
future<00:08:23.740><c> so</c><00:08:25.030><c> so</c><00:08:26.030><c> that's</c><00:08:26.150><c> it</c><00:08:26.389><c> for</c><00:08:26.900><c> this</c><00:08:27.650><c> text</c>

00:08:28.150 --> 00:08:28.160 align:start position:0%
future so so that's it for this text
 

00:08:28.160 --> 00:08:30.670 align:start position:0%
future so so that's it for this text
HTML<00:08:28.550><c> text</c><00:08:28.940><c> video</c><00:08:29.410><c> hope</c><00:08:30.410><c> you</c><00:08:30.500><c> learned</c>

00:08:30.670 --> 00:08:30.680 align:start position:0%
HTML text video hope you learned
 

00:08:30.680 --> 00:08:32.950 align:start position:0%
HTML text video hope you learned
something<00:08:30.890><c> and</c><00:08:31.089><c> the</c><00:08:32.089><c> next</c><00:08:32.120><c> one</c><00:08:32.510><c> we're</c><00:08:32.839><c> going</c>

00:08:32.950 --> 00:08:32.960 align:start position:0%
something and the next one we're going
 

00:08:32.960 --> 00:08:35.200 align:start position:0%
something and the next one we're going
to<00:08:33.020><c> learn</c><00:08:33.140><c> about</c><00:08:33.260><c> links</c><00:08:33.830><c> and</c><00:08:34.010><c> we're</c><00:08:34.969><c> actually</c>

00:08:35.200 --> 00:08:35.210 align:start position:0%
to learn about links and we're actually
 

00:08:35.210 --> 00:08:38.170 align:start position:0%
to learn about links and we're actually
be<00:08:35.330><c> making</c><00:08:35.510><c> a</c><00:08:35.750><c> few</c><00:08:36.370><c> HTML</c><00:08:37.370><c> files</c><00:08:37.610><c> and</c><00:08:37.969><c> they're</c>

00:08:38.170 --> 00:08:38.180 align:start position:0%
be making a few HTML files and they're
 

00:08:38.180 --> 00:08:40.560 align:start position:0%
be making a few HTML files and they're
going<00:08:38.300><c> to</c><00:08:38.390><c> be</c><00:08:38.450><c> linking</c><00:08:38.719><c> to</c><00:08:38.839><c> each</c><00:08:38.870><c> other</c><00:08:39.169><c> okay</c>

00:08:40.560 --> 00:08:40.570 align:start position:0%
going to be linking to each other okay
 

00:08:40.570 --> 00:08:43.300 align:start position:0%
going to be linking to each other okay
thank<00:08:41.570><c> you</c><00:08:41.599><c> for</c><00:08:41.719><c> watching</c><00:08:42.050><c> and</c><00:08:42.200><c> see</c><00:08:43.190><c> you</c><00:08:43.250><c> next</c>

00:08:43.300 --> 00:08:43.310 align:start position:0%
thank you for watching and see you next
 

00:08:43.310 --> 00:08:45.460 align:start position:0%
thank you for watching and see you next
time

