WEBVTT
Kind: captions
Language: en

00:00:00.730 --> 00:00:05.349 align:start position:0%
 
[Music]

00:00:05.349 --> 00:00:05.359 align:start position:0%
 
 

00:00:05.359 --> 00:00:07.150 align:start position:0%
 
let's<00:00:05.640><c> talk</c><00:00:05.920><c> how</c><00:00:06.040><c> to</c><00:00:06.240><c> create</c><00:00:06.680><c> effective</c>

00:00:07.150 --> 00:00:07.160 align:start position:0%
let's talk how to create effective
 

00:00:07.160 --> 00:00:09.509 align:start position:0%
let's talk how to create effective
prompts<00:00:08.000><c> for</c><00:00:08.200><c> large</c><00:00:08.519><c> language</c>

00:00:09.509 --> 00:00:09.519 align:start position:0%
prompts for large language
 

00:00:09.519 --> 00:00:12.150 align:start position:0%
prompts for large language
models<00:00:10.519><c> to</c><00:00:10.719><c> get</c><00:00:10.880><c> the</c><00:00:11.000><c> output</c><00:00:11.400><c> we</c><00:00:11.519><c> want</c><00:00:11.880><c> we</c><00:00:12.000><c> need</c>

00:00:12.150 --> 00:00:12.160 align:start position:0%
models to get the output we want we need
 

00:00:12.160 --> 00:00:15.430 align:start position:0%
models to get the output we want we need
to<00:00:12.360><c> give</c><00:00:12.519><c> the</c><00:00:12.759><c> model</c><00:00:13.759><c> A</c><00:00:14.040><c> well-crafted</c><00:00:14.440><c> input</c>

00:00:15.430 --> 00:00:15.440 align:start position:0%
to give the model A well-crafted input
 

00:00:15.440 --> 00:00:16.429 align:start position:0%
to give the model A well-crafted input
called<00:00:15.639><c> a</c>

00:00:16.429 --> 00:00:16.439 align:start position:0%
called a
 

00:00:16.439 --> 00:00:19.470 align:start position:0%
called a
prompt<00:00:17.439><c> the</c><00:00:17.560><c> way</c><00:00:17.760><c> we</c><00:00:17.920><c> create</c><00:00:18.240><c> prompts</c><00:00:19.119><c> can</c>

00:00:19.470 --> 00:00:19.480 align:start position:0%
prompt the way we create prompts can
 

00:00:19.480 --> 00:00:22.509 align:start position:0%
prompt the way we create prompts can
greatly<00:00:19.960><c> affect</c><00:00:20.400><c> the</c><00:00:20.800><c> outcome</c><00:00:21.800><c> we</c><00:00:22.000><c> aim</c><00:00:22.240><c> for</c>

00:00:22.509 --> 00:00:22.519 align:start position:0%
greatly affect the outcome we aim for
 

00:00:22.519 --> 00:00:24.550 align:start position:0%
greatly affect the outcome we aim for
high<00:00:22.760><c> quality</c><00:00:23.199><c> results</c><00:00:23.800><c> like</c><00:00:24.119><c> helpful</c>

00:00:24.550 --> 00:00:24.560 align:start position:0%
high quality results like helpful
 

00:00:24.560 --> 00:00:27.349 align:start position:0%
high quality results like helpful
answers<00:00:25.000><c> in</c><00:00:25.199><c> question</c><00:00:25.640><c> answering</c><00:00:26.160><c> tasks</c><00:00:27.119><c> or</c>

00:00:27.349 --> 00:00:27.359 align:start position:0%
answers in question answering tasks or
 

00:00:27.359 --> 00:00:29.429 align:start position:0%
answers in question answering tasks or
meaningful<00:00:27.840><c> summaries</c><00:00:28.480><c> for</c><00:00:28.679><c> summarization</c>

00:00:29.429 --> 00:00:29.439 align:start position:0%
meaningful summaries for summarization
 

00:00:29.439 --> 00:00:30.669 align:start position:0%
meaningful summaries for summarization
tasks

00:00:30.669 --> 00:00:30.679 align:start position:0%
tasks
 

00:00:30.679 --> 00:00:33.350 align:start position:0%
tasks
a<00:00:30.880><c> good</c><00:00:31.080><c> prompt</c><00:00:31.439><c> is</c><00:00:31.599><c> crucial</c><00:00:32.279><c> for</c><00:00:32.559><c> excellent</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
a good prompt is crucial for excellent
 

00:00:33.360 --> 00:00:35.549 align:start position:0%
a good prompt is crucial for excellent
output<00:00:34.360><c> many</c><00:00:34.640><c> prompt</c><00:00:35.000><c> engineering</c>

00:00:35.549 --> 00:00:35.559 align:start position:0%
output many prompt engineering
 

00:00:35.559 --> 00:00:38.150 align:start position:0%
output many prompt engineering
techniques<00:00:36.440><c> exist</c><00:00:37.239><c> and</c><00:00:37.399><c> we</c><00:00:37.520><c> will</c><00:00:37.680><c> discuss</c>

00:00:38.150 --> 00:00:38.160 align:start position:0%
techniques exist and we will discuss
 

00:00:38.160 --> 00:00:41.229 align:start position:0%
techniques exist and we will discuss
some<00:00:38.600><c> in</c><00:00:38.760><c> future</c><00:00:39.559><c> videos</c><00:00:40.559><c> I</c><00:00:40.719><c> recommend</c><00:00:41.079><c> a</c>

00:00:41.229 --> 00:00:41.239 align:start position:0%
some in future videos I recommend a
 

00:00:41.239 --> 00:00:44.069 align:start position:0%
some in future videos I recommend a
paper<00:00:41.600><c> called</c><00:00:41.920><c> teller</c><00:00:42.680><c> a</c><00:00:42.920><c> tool</c><00:00:43.480><c> for</c>

00:00:44.069 --> 00:00:44.079 align:start position:0%
paper called teller a tool for
 

00:00:44.079 --> 00:00:47.709 align:start position:0%
paper called teller a tool for
benchmarking<00:00:44.840><c> complex</c><00:00:45.320><c> tasks</c><00:00:46.199><c> using</c><00:00:46.840><c> uh</c><00:00:46.960><c> llm</c>

00:00:47.709 --> 00:00:47.719 align:start position:0%
benchmarking complex tasks using uh llm
 

00:00:47.719 --> 00:00:50.350 align:start position:0%
benchmarking complex tasks using uh llm
prompts<00:00:48.719><c> this</c><00:00:48.920><c> resource</c><00:00:49.680><c> introduces</c>

00:00:50.350 --> 00:00:50.360 align:start position:0%
prompts this resource introduces
 

00:00:50.360 --> 00:00:53.430 align:start position:0%
prompts this resource introduces
different<00:00:50.840><c> prompt</c><00:00:51.199><c> maturity</c><00:00:52.120><c> levels</c><00:00:53.120><c> level</c>

00:00:53.430 --> 00:00:53.440 align:start position:0%
different prompt maturity levels level
 

00:00:53.440 --> 00:00:56.389 align:start position:0%
different prompt maturity levels level
zero<00:00:54.079><c> is</c><00:00:54.239><c> no</c><00:00:54.600><c> directive</c><00:00:55.600><c> level</c><00:00:55.840><c> one</c><00:00:56.039><c> is</c><00:00:56.160><c> a</c>

00:00:56.389 --> 00:00:56.399 align:start position:0%
zero is no directive level one is a
 

00:00:56.399 --> 00:00:59.590 align:start position:0%
zero is no directive level one is a
simple<00:00:56.960><c> one-</c><00:00:57.280><c> sentence</c><00:00:58.280><c> instruction</c><00:00:59.280><c> we</c><00:00:59.440><c> can</c>

00:00:59.590 --> 00:00:59.600 align:start position:0%
simple one- sentence instruction we can
 

00:00:59.600 --> 00:01:02.430 align:start position:0%
simple one- sentence instruction we can
add<00:00:59.960><c> more</c><00:01:00.199><c> details</c><00:01:00.879><c> data</c><00:01:01.280><c> and</c><00:01:01.480><c> directives</c>

00:01:02.430 --> 00:01:02.440 align:start position:0%
add more details data and directives
 

00:01:02.440 --> 00:01:04.950 align:start position:0%
add more details data and directives
eventually<00:01:02.960><c> reaching</c><00:01:03.440><c> level</c><00:01:03.840><c> five</c><00:01:04.680><c> which</c><00:01:04.799><c> is</c>

00:01:04.950 --> 00:01:04.960 align:start position:0%
eventually reaching level five which is
 

00:01:04.960 --> 00:01:07.109 align:start position:0%
eventually reaching level five which is
a<00:01:05.199><c> complex</c><00:01:05.640><c> directive</c><00:01:06.320><c> with</c><00:01:06.439><c> a</c><00:01:06.640><c> high</c><00:01:06.880><c> level</c>

00:01:07.109 --> 00:01:07.119 align:start position:0%
a complex directive with a high level
 

00:01:07.119 --> 00:01:10.390 align:start position:0%
a complex directive with a high level
goal<00:01:07.880><c> description</c><00:01:08.880><c> a</c><00:01:09.119><c> detailed</c><00:01:09.880><c> bulleted</c>

00:01:10.390 --> 00:01:10.400 align:start position:0%
goal description a detailed bulleted
 

00:01:10.400 --> 00:01:13.870 align:start position:0%
goal description a detailed bulleted
list<00:01:10.759><c> of</c><00:01:11.240><c> subtasks</c><00:01:12.240><c> an</c><00:01:12.479><c> explicit</c><00:01:13.040><c> statement</c>

00:01:13.870 --> 00:01:13.880 align:start position:0%
list of subtasks an explicit statement
 

00:01:13.880 --> 00:01:17.429 align:start position:0%
list of subtasks an explicit statement
asking<00:01:14.280><c> the</c><00:01:14.439><c> llm</c><00:01:14.960><c> to</c><00:01:15.159><c> explain</c><00:01:15.640><c> its</c><00:01:16.439><c> output</c>

00:01:17.429 --> 00:01:17.439 align:start position:0%
asking the llm to explain its output
 

00:01:17.439 --> 00:01:20.350 align:start position:0%
asking the llm to explain its output
guidelines<00:01:18.080><c> on</c><00:01:18.280><c> output</c><00:01:18.759><c> evaluation</c><00:01:19.759><c> and</c><00:01:20.119><c> few</c>

00:01:20.350 --> 00:01:20.360 align:start position:0%
guidelines on output evaluation and few
 

00:01:20.360 --> 00:01:21.789 align:start position:0%
guidelines on output evaluation and few
short

00:01:21.789 --> 00:01:21.799 align:start position:0%
short
 

00:01:21.799 --> 00:01:25.429 align:start position:0%
short
examples<00:01:22.799><c> as</c><00:01:22.920><c> we</c><00:01:23.119><c> explore</c><00:01:23.640><c> llm</c><00:01:24.200><c> apis</c><00:01:25.040><c> we</c><00:01:25.159><c> will</c>

00:01:25.429 --> 00:01:25.439 align:start position:0%
examples as we explore llm apis we will
 

00:01:25.439 --> 00:01:27.830 align:start position:0%
examples as we explore llm apis we will
experiment<00:01:26.159><c> with</c><00:01:26.320><c> this</c><00:01:26.640><c> level</c><00:01:26.960><c> five</c><00:01:27.240><c> prompts</c>

00:01:27.830 --> 00:01:27.840 align:start position:0%
experiment with this level five prompts
 

00:01:27.840 --> 00:01:31.670 align:start position:0%
experiment with this level five prompts
to<00:01:28.040><c> get</c><00:01:28.200><c> the</c><00:01:28.360><c> best</c><00:01:28.840><c> results</c>

00:01:31.670 --> 00:01:31.680 align:start position:0%
to get the best results
 

00:01:31.680 --> 00:01:34.469 align:start position:0%
to get the best results
many<00:01:32.000><c> llm</c><00:01:32.520><c> providers</c><00:01:32.920><c> are</c><00:01:33.159><c> available</c><00:01:34.079><c> we</c><00:01:34.200><c> will</c>

00:01:34.469 --> 00:01:34.479 align:start position:0%
many llm providers are available we will
 

00:01:34.479 --> 00:01:37.710 align:start position:0%
many llm providers are available we will
mainly<00:01:34.840><c> use</c><00:01:35.200><c> open</c><00:01:35.520><c> AI</c><00:01:36.040><c> API</c><00:01:36.560><c> in</c><00:01:36.680><c> this</c><00:01:36.880><c> course</c>

00:01:37.710 --> 00:01:37.720 align:start position:0%
mainly use open AI API in this course
 

00:01:37.720 --> 00:01:40.310 align:start position:0%
mainly use open AI API in this course
but<00:01:38.079><c> I</c><00:01:38.240><c> encourage</c><00:01:38.840><c> exploring</c><00:01:39.479><c> other</c><00:01:39.840><c> options</c>

00:01:40.310 --> 00:01:40.320 align:start position:0%
but I encourage exploring other options
 

00:01:40.320 --> 00:01:44.230 align:start position:0%
but I encourage exploring other options
like<00:01:40.680><c> coher</c><00:01:41.520><c> anthropic</c><00:01:42.520><c> or</c><00:01:42.759><c> hugging</c><00:01:43.240><c> phas</c>

00:01:44.230 --> 00:01:44.240 align:start position:0%
like coher anthropic or hugging phas
 

00:01:44.240 --> 00:01:47.190 align:start position:0%
like coher anthropic or hugging phas
hugging<00:01:44.640><c> phas</c><00:01:45.159><c> hop</c><00:01:45.880><c> features</c><00:01:46.360><c> models</c><00:01:46.960><c> from</c>

00:01:47.190 --> 00:01:47.200 align:start position:0%
hugging phas hop features models from
 

00:01:47.200 --> 00:01:50.069 align:start position:0%
hugging phas hop features models from
companies<00:01:47.640><c> such</c><00:01:47.799><c> as</c><00:01:47.960><c> meta</c><00:01:48.479><c> mosl</c><00:01:49.320><c> Uther</c><00:01:49.880><c> and</c>

00:01:50.069 --> 00:01:50.079 align:start position:0%
companies such as meta mosl Uther and
 

00:01:50.079 --> 00:01:53.230 align:start position:0%
companies such as meta mosl Uther and
more<00:01:51.079><c> and</c><00:01:51.280><c> this</c><00:01:51.479><c> llm</c><00:01:52.079><c> diversity</c><00:01:52.719><c> helps</c><00:01:53.000><c> us</c>

00:01:53.230 --> 00:01:53.240 align:start position:0%
more and this llm diversity helps us
 

00:01:53.240 --> 00:01:58.830 align:start position:0%
more and this llm diversity helps us
choose<00:01:53.880><c> the</c><00:01:54.040><c> perfect</c><00:01:54.399><c> model</c><00:01:54.799><c> for</c><00:01:55.079><c> our</c>

00:01:58.830 --> 00:01:58.840 align:start position:0%
 
 

00:01:58.840 --> 00:02:01.840 align:start position:0%
 
tasks

