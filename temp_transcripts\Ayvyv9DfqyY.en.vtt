WEBVTT
Kind: captions
Language: en

00:00:06.799 --> 00:00:08.150 align:start position:0%
 
Hi<00:00:07.040><c> everyone,</c><00:00:07.359><c> just</c><00:00:07.600><c> want</c><00:00:07.680><c> to</c><00:00:07.759><c> give</c><00:00:07.839><c> a</c><00:00:08.000><c> quick</c>

00:00:08.150 --> 00:00:08.160 align:start position:0%
Hi everyone, just want to give a quick
 

00:00:08.160 --> 00:00:09.990 align:start position:0%
Hi everyone, just want to give a quick
update<00:00:08.480><c> on</c><00:00:08.720><c> markets.</c><00:00:09.120><c> Uh</c><00:00:09.360><c> we've</c><00:00:09.599><c> seen</c><00:00:09.679><c> a</c><00:00:09.920><c> lot</c>

00:00:09.990 --> 00:00:10.000 align:start position:0%
update on markets. Uh we've seen a lot
 

00:00:10.000 --> 00:00:12.310 align:start position:0%
update on markets. Uh we've seen a lot
of<00:00:10.080><c> volatility</c><00:00:10.719><c> since</c><00:00:11.040><c> March</c><00:00:11.599><c> 10th,</c><00:00:12.000><c> which</c><00:00:12.240><c> is</c>

00:00:12.310 --> 00:00:12.320 align:start position:0%
of volatility since March 10th, which is
 

00:00:12.320 --> 00:00:14.549 align:start position:0%
of volatility since March 10th, which is
the<00:00:12.480><c> high</c><00:00:12.639><c> on</c><00:00:12.800><c> the</c><00:00:12.960><c> continuous.</c><00:00:13.920><c> So</c><00:00:14.160><c> wanted</c><00:00:14.400><c> to</c>

00:00:14.549 --> 00:00:14.559 align:start position:0%
the high on the continuous. So wanted to
 

00:00:14.559 --> 00:00:15.669 align:start position:0%
the high on the continuous. So wanted to
go<00:00:14.719><c> through</c><00:00:14.880><c> what</c><00:00:15.040><c> our</c><00:00:15.200><c> models</c><00:00:15.440><c> have</c><00:00:15.519><c> been</c>

00:00:15.669 --> 00:00:15.679 align:start position:0%
go through what our models have been
 

00:00:15.679 --> 00:00:18.630 align:start position:0%
go through what our models have been
showing<00:00:16.080><c> going</c><00:00:16.400><c> all</c><00:00:16.640><c> the</c><00:00:16.800><c> way</c><00:00:16.880><c> back</c><00:00:17.600><c> to</c><00:00:18.320><c> uh</c><00:00:18.400><c> the</c>

00:00:18.630 --> 00:00:18.640 align:start position:0%
showing going all the way back to uh the
 

00:00:18.640 --> 00:00:20.870 align:start position:0%
showing going all the way back to uh the
report<00:00:18.800><c> we</c><00:00:19.039><c> put</c><00:00:19.119><c> out</c><00:00:19.359><c> on</c><00:00:19.520><c> March</c><00:00:19.920><c> 11th,</c><00:00:20.640><c> which</c>

00:00:20.870 --> 00:00:20.880 align:start position:0%
report we put out on March 11th, which
 

00:00:20.880 --> 00:00:22.870 align:start position:0%
report we put out on March 11th, which
was<00:00:21.039><c> right</c><00:00:21.279><c> near</c><00:00:21.439><c> the</c><00:00:21.600><c> high.</c><00:00:22.160><c> Kind</c><00:00:22.400><c> of</c><00:00:22.480><c> how</c><00:00:22.640><c> our</c>

00:00:22.870 --> 00:00:22.880 align:start position:0%
was right near the high. Kind of how our
 

00:00:22.880 --> 00:00:24.630 align:start position:0%
was right near the high. Kind of how our
models<00:00:23.199><c> have</c><00:00:23.359><c> adjusted</c><00:00:23.840><c> the</c><00:00:24.080><c> three</c><00:00:24.320><c> things</c>

00:00:24.630 --> 00:00:24.640 align:start position:0%
models have adjusted the three things
 

00:00:24.640 --> 00:00:25.830 align:start position:0%
models have adjusted the three things
we've<00:00:24.880><c> been</c><00:00:25.039><c> really</c><00:00:25.199><c> talking</c><00:00:25.439><c> to</c><00:00:25.600><c> clients</c>

00:00:25.830 --> 00:00:25.840 align:start position:0%
we've been really talking to clients
 

00:00:25.840 --> 00:00:28.070 align:start position:0%
we've been really talking to clients
about<00:00:26.080><c> watching</c><00:00:26.480><c> for</c><00:00:27.279><c> uh</c><00:00:27.439><c> through</c><00:00:27.760><c> the</c>

00:00:28.070 --> 00:00:28.080 align:start position:0%
about watching for uh through the
 

00:00:28.080 --> 00:00:30.230 align:start position:0%
about watching for uh through the
balance<00:00:28.400><c> of</c><00:00:28.480><c> the</c><00:00:28.640><c> summer.</c><00:00:29.760><c> So</c><00:00:29.920><c> what</c><00:00:30.080><c> I'm</c>

00:00:30.230 --> 00:00:30.240 align:start position:0%
balance of the summer. So what I'm
 

00:00:30.240 --> 00:00:32.389 align:start position:0%
balance of the summer. So what I'm
showing<00:00:30.480><c> now</c><00:00:30.720><c> it</c><00:00:30.880><c> is</c><00:00:31.119><c> the</c><00:00:31.359><c> report</c><00:00:31.599><c> we</c><00:00:31.920><c> put</c><00:00:32.079><c> out.</c>

00:00:32.389 --> 00:00:32.399 align:start position:0%
showing now it is the report we put out.
 

00:00:32.399 --> 00:00:34.310 align:start position:0%
showing now it is the report we put out.
This<00:00:32.559><c> was</c><00:00:32.719><c> as</c><00:00:32.960><c> of</c><00:00:33.120><c> March</c><00:00:33.360><c> 11th</c><00:00:33.680><c> but</c><00:00:33.840><c> as</c><00:00:33.920><c> a</c><00:00:34.160><c> March</c>

00:00:34.310 --> 00:00:34.320 align:start position:0%
This was as of March 11th but as a March
 

00:00:34.320 --> 00:00:36.470 align:start position:0%
This was as of March 11th but as a March
7th<00:00:34.680><c> settles</c><00:00:35.680><c> and</c><00:00:35.840><c> we</c><00:00:36.000><c> were</c><00:00:36.079><c> a</c><00:00:36.160><c> bit</c><00:00:36.239><c> of</c><00:00:36.320><c> an</c>

00:00:36.470 --> 00:00:36.480 align:start position:0%
7th settles and we were a bit of an
 

00:00:36.480 --> 00:00:38.310 align:start position:0%
7th settles and we were a bit of an
outlier<00:00:36.800><c> at</c><00:00:36.960><c> the</c><00:00:37.120><c> time</c><00:00:37.360><c> saying</c><00:00:37.600><c> that</c><00:00:37.840><c> at</c><00:00:38.160><c> the</c>

00:00:38.310 --> 00:00:38.320 align:start position:0%
outlier at the time saying that at the
 

00:00:38.320 --> 00:00:40.069 align:start position:0%
outlier at the time saying that at the
current<00:00:38.640><c> price</c><00:00:39.200><c> I</c><00:00:39.360><c> think</c><00:00:39.440><c> the</c><00:00:39.600><c> summer</c><00:00:39.840><c> strip</c>

00:00:40.069 --> 00:00:40.079 align:start position:0%
current price I think the summer strip
 

00:00:40.079 --> 00:00:42.709 align:start position:0%
current price I think the summer strip
is<00:00:40.239><c> trading</c><00:00:40.480><c> around</c><00:00:40.920><c> 465</c><00:00:41.920><c> and</c><00:00:42.160><c> at</c><00:00:42.320><c> that</c><00:00:42.480><c> price</c>

00:00:42.709 --> 00:00:42.719 align:start position:0%
is trading around 465 and at that price
 

00:00:42.719 --> 00:00:45.750 align:start position:0%
is trading around 465 and at that price
we<00:00:42.879><c> were</c><00:00:43.040><c> showing</c><00:00:43.760><c> over</c><00:00:44.079><c> a</c><00:00:44.320><c> 4.1</c><00:00:45.120><c> end</c><00:00:45.280><c> of</c><00:00:45.440><c> season</c>

00:00:45.750 --> 00:00:45.760 align:start position:0%
we were showing over a 4.1 end of season
 

00:00:45.760 --> 00:00:47.350 align:start position:0%
we were showing over a 4.1 end of season
October.<00:00:46.239><c> I</c><00:00:46.399><c> realized</c><00:00:46.640><c> we</c><00:00:46.800><c> were</c><00:00:46.879><c> a</c><00:00:47.039><c> bit</c><00:00:47.120><c> of</c><00:00:47.200><c> an</c>

00:00:47.350 --> 00:00:47.360 align:start position:0%
October. I realized we were a bit of an
 

00:00:47.360 --> 00:00:49.670 align:start position:0%
October. I realized we were a bit of an
outlier<00:00:47.840><c> at</c><00:00:48.079><c> that</c><00:00:48.239><c> point</c><00:00:49.039><c> but</c><00:00:49.200><c> our</c><00:00:49.440><c> models</c>

00:00:49.670 --> 00:00:49.680 align:start position:0%
outlier at that point but our models
 

00:00:49.680 --> 00:00:51.430 align:start position:0%
outlier at that point but our models
were<00:00:49.840><c> saying</c><00:00:50.079><c> the</c><00:00:50.320><c> market</c><00:00:50.480><c> was</c><00:00:50.719><c> vastly</c><00:00:51.120><c> overs</c>

00:00:51.430 --> 00:00:51.440 align:start position:0%
were saying the market was vastly overs
 

00:00:51.440 --> 00:00:55.990 align:start position:0%
were saying the market was vastly overs
supplied.<00:00:52.399><c> Well,</c><00:00:53.199><c> if</c><00:00:53.440><c> we</c><00:00:53.840><c> fast</c><00:00:54.160><c> forward</c><00:00:55.000><c> to</c>

00:00:55.990 --> 00:00:56.000 align:start position:0%
supplied. Well, if we fast forward to
 

00:00:56.000 --> 00:00:57.910 align:start position:0%
supplied. Well, if we fast forward to
now<00:00:56.320><c> the</c><00:00:56.640><c> report</c><00:00:56.879><c> that</c><00:00:57.120><c> went</c><00:00:57.360><c> out</c><00:00:57.600><c> with</c>

00:00:57.910 --> 00:00:57.920 align:start position:0%
now the report that went out with
 

00:00:57.920 --> 00:01:01.029 align:start position:0%
now the report that went out with
settles<00:00:58.399><c> as</c><00:00:58.559><c> of</c><00:00:58.879><c> April</c><00:00:59.399><c> 25th,</c><00:01:00.399><c> we've</c><00:01:00.719><c> actually</c>

00:01:01.029 --> 00:01:01.039 align:start position:0%
settles as of April 25th, we've actually
 

00:01:01.039 --> 00:01:02.869 align:start position:0%
settles as of April 25th, we've actually
seen<00:01:01.359><c> our</c><00:01:01.680><c> models</c><00:01:02.000><c> have</c><00:01:02.160><c> tightened</c><00:01:02.559><c> very</c>

00:01:02.869 --> 00:01:02.879 align:start position:0%
seen our models have tightened very
 

00:01:02.879 --> 00:01:05.509 align:start position:0%
seen our models have tightened very
significantly.<00:01:03.600><c> So,</c><00:01:04.239><c> uh</c><00:01:04.400><c> the</c><00:01:04.559><c> models</c><00:01:05.119><c> as</c><00:01:05.360><c> of</c>

00:01:05.509 --> 00:01:05.519 align:start position:0%
significantly. So, uh the models as of
 

00:01:05.519 --> 00:01:07.109 align:start position:0%
significantly. So, uh the models as of
March<00:01:05.760><c> 7th,</c><00:01:06.080><c> we're</c><00:01:06.320><c> correctly</c><00:01:06.799><c> identifying</c>

00:01:07.109 --> 00:01:07.119 align:start position:0%
March 7th, we're correctly identifying
 

00:01:07.119 --> 00:01:09.990 align:start position:0%
March 7th, we're correctly identifying
that<00:01:07.360><c> over</c><00:01:07.680><c> supply,</c><00:01:08.080><c> but</c><00:01:08.640><c> over</c><00:01:08.880><c> the</c><00:01:09.119><c> last</c><00:01:09.600><c> what</c>

00:01:09.990 --> 00:01:10.000 align:start position:0%
that over supply, but over the last what
 

00:01:10.000 --> 00:01:12.230 align:start position:0%
that over supply, but over the last what
that's<00:01:10.320><c> seven</c><00:01:10.560><c> weeks,</c><00:01:11.360><c> we've</c><00:01:11.680><c> seen</c><00:01:11.920><c> the</c>

00:01:12.230 --> 00:01:12.240 align:start position:0%
that's seven weeks, we've seen the
 

00:01:12.240 --> 00:01:14.350 align:start position:0%
that's seven weeks, we've seen the
summer<00:01:12.560><c> strip</c><00:01:13.040><c> sell</c><00:01:13.280><c> off</c><00:01:13.520><c> around</c>

00:01:14.350 --> 00:01:14.360 align:start position:0%
summer strip sell off around
 

00:01:14.360 --> 00:01:16.710 align:start position:0%
summer strip sell off around
a$130.<00:01:15.360><c> And</c><00:01:15.520><c> over</c><00:01:15.680><c> that</c><00:01:15.920><c> time,</c><00:01:16.159><c> our</c><00:01:16.400><c> models</c>

00:01:16.710 --> 00:01:16.720 align:start position:0%
a$130. And over that time, our models
 

00:01:16.720 --> 00:01:18.950 align:start position:0%
a$130. And over that time, our models
are<00:01:16.880><c> now</c><00:01:17.200><c> showing</c><00:01:17.520><c> fairly</c><00:01:17.920><c> balanced.</c><00:01:18.640><c> The</c>

00:01:18.950 --> 00:01:18.960 align:start position:0%
are now showing fairly balanced. The
 

00:01:18.960 --> 00:01:20.469 align:start position:0%
are now showing fairly balanced. The
problem<00:01:19.200><c> that</c><00:01:19.439><c> we're</c><00:01:19.600><c> highlighting</c><00:01:20.159><c> right</c>

00:01:20.469 --> 00:01:20.479 align:start position:0%
problem that we're highlighting right
 

00:01:20.479 --> 00:01:22.870 align:start position:0%
problem that we're highlighting right
now<00:01:20.799><c> though</c><00:01:21.520><c> is</c><00:01:21.759><c> that</c><00:01:22.000><c> the</c><00:01:22.159><c> tightening</c><00:01:22.560><c> in</c><00:01:22.720><c> the</c>

00:01:22.870 --> 00:01:22.880 align:start position:0%
now though is that the tightening in the
 

00:01:22.880 --> 00:01:24.469 align:start position:0%
now though is that the tightening in the
front<00:01:23.119><c> is</c><00:01:23.360><c> actually</c><00:01:23.600><c> not</c><00:01:23.840><c> incentivizing</c>

00:01:24.469 --> 00:01:24.479 align:start position:0%
front is actually not incentivizing
 

00:01:24.479 --> 00:01:26.469 align:start position:0%
front is actually not incentivizing
enough<00:01:24.799><c> production</c><00:01:25.280><c> growth.</c><00:01:25.840><c> And</c><00:01:26.000><c> so</c><00:01:26.240><c> that</c>

00:01:26.469 --> 00:01:26.479 align:start position:0%
enough production growth. And so that
 

00:01:26.479 --> 00:01:28.789 align:start position:0%
enough production growth. And so that
starts<00:01:26.799><c> paint</c><00:01:27.040><c> a</c><00:01:27.280><c> pretty</c><00:01:27.840><c> bullish</c><00:01:28.320><c> picture</c><00:01:28.560><c> as</c>

00:01:28.789 --> 00:01:28.799 align:start position:0%
starts paint a pretty bullish picture as
 

00:01:28.799 --> 00:01:31.630 align:start position:0%
starts paint a pretty bullish picture as
we<00:01:28.960><c> head</c><00:01:29.119><c> into</c><00:01:29.360><c> winter</c><00:01:29.680><c> with</c><00:01:29.920><c> the</c><00:01:30.080><c> growth</c><00:01:30.240><c> in</c>

00:01:31.630 --> 00:01:31.640 align:start position:0%
we head into winter with the growth in
 

00:01:31.640 --> 00:01:34.069 align:start position:0%
we head into winter with the growth in
LG.<00:01:32.640><c> Now</c><00:01:32.960><c> that's</c><00:01:33.200><c> kind</c><00:01:33.280><c> of</c><00:01:33.360><c> where</c><00:01:33.600><c> we've</c><00:01:33.840><c> been</c>

00:01:34.069 --> 00:01:34.079 align:start position:0%
LG. Now that's kind of where we've been
 

00:01:34.079 --> 00:01:35.350 align:start position:0%
LG. Now that's kind of where we've been
where<00:01:34.240><c> we</c><00:01:34.400><c> are</c><00:01:34.560><c> and</c><00:01:34.720><c> we</c><00:01:34.880><c> were</c><00:01:35.040><c> showing</c><00:01:35.200><c> this</c>

00:01:35.350 --> 00:01:35.360 align:start position:0%
where we are and we were showing this
 

00:01:35.360 --> 00:01:36.630 align:start position:0%
where we are and we were showing this
tightness.<00:01:35.840><c> This</c><00:01:36.000><c> would</c><00:01:36.079><c> have</c><00:01:36.159><c> been</c><00:01:36.320><c> as</c><00:01:36.479><c> of</c>

00:01:36.630 --> 00:01:36.640 align:start position:0%
tightness. This would have been as of
 

00:01:36.640 --> 00:01:38.550 align:start position:0%
tightness. This would have been as of
April<00:01:37.040><c> 25th</c><00:01:37.520><c> that</c><00:01:37.759><c> we</c><00:01:38.000><c> were</c><00:01:38.159><c> highlighting</c>

00:01:38.550 --> 00:01:38.560 align:start position:0%
April 25th that we were highlighting
 

00:01:38.560 --> 00:01:39.910 align:start position:0%
April 25th that we were highlighting
that<00:01:38.799><c> things</c><00:01:38.960><c> looked</c><00:01:39.200><c> a</c><00:01:39.360><c> little</c><00:01:39.520><c> too</c><00:01:39.680><c> tight.</c>

00:01:39.910 --> 00:01:39.920 align:start position:0%
that things looked a little too tight.
 

00:01:39.920 --> 00:01:41.749 align:start position:0%
that things looked a little too tight.
Since<00:01:40.159><c> then</c><00:01:40.400><c> we've</c><00:01:40.720><c> rebounded</c><00:01:41.200><c> and</c><00:01:41.360><c> rallied</c>

00:01:41.749 --> 00:01:41.759 align:start position:0%
Since then we've rebounded and rallied
 

00:01:41.759 --> 00:01:44.950 align:start position:0%
Since then we've rebounded and rallied
it<00:01:41.920><c> looks</c><00:01:42.079><c> like</c><00:01:42.240><c> about</c><00:01:42.640><c> 45</c><00:01:43.439><c> cents</c><00:01:44.240><c> as</c><00:01:44.479><c> of</c><00:01:44.720><c> right</c>

00:01:44.950 --> 00:01:44.960 align:start position:0%
it looks like about 45 cents as of right
 

00:01:44.960 --> 00:01:48.149 align:start position:0%
it looks like about 45 cents as of right
now.<00:01:45.840><c> So</c><00:01:46.479><c> as</c><00:01:46.799><c> of</c><00:01:47.280><c> the</c><00:01:47.439><c> current</c><00:01:47.680><c> price</c><00:01:48.000><c> strip</c>

00:01:48.149 --> 00:01:48.159 align:start position:0%
now. So as of the current price strip
 

00:01:48.159 --> 00:01:49.670 align:start position:0%
now. So as of the current price strip
and<00:01:48.320><c> the</c><00:01:48.479><c> models</c><00:01:48.720><c> we'll</c><00:01:48.960><c> send</c><00:01:49.119><c> out</c><00:01:49.280><c> today,</c>

00:01:49.670 --> 00:01:49.680 align:start position:0%
and the models we'll send out today,
 

00:01:49.680 --> 00:01:51.350 align:start position:0%
and the models we'll send out today,
it's<00:01:49.920><c> going</c><00:01:50.000><c> to</c><00:01:50.079><c> be</c><00:01:50.479><c> a</c><00:01:50.720><c> bit</c><00:01:50.799><c> higher</c><00:01:51.040><c> in</c><00:01:51.200><c> this</c>

00:01:51.350 --> 00:01:51.360 align:start position:0%
it's going to be a bit higher in this
 

00:01:51.360 --> 00:01:52.789 align:start position:0%
it's going to be a bit higher in this
and<00:01:51.439><c> we'll</c><00:01:51.680><c> see</c><00:01:51.759><c> some</c><00:01:51.920><c> loosening</c><00:01:52.479><c> coming</c><00:01:52.640><c> back</c>

00:01:52.789 --> 00:01:52.799 align:start position:0%
and we'll see some loosening coming back
 

00:01:52.799 --> 00:01:55.469 align:start position:0%
and we'll see some loosening coming back
in<00:01:53.040><c> the</c><00:01:53.200><c> market</c><00:01:53.439><c> as</c><00:01:53.920><c> we</c><00:01:54.159><c> needed</c><00:01:54.479><c> to</c>

00:01:55.469 --> 00:01:55.479 align:start position:0%
in the market as we needed to
 

00:01:55.479 --> 00:01:58.230 align:start position:0%
in the market as we needed to
see.<00:01:56.479><c> Now</c><00:01:56.799><c> there</c><00:01:56.960><c> are</c><00:01:57.119><c> three</c><00:01:57.439><c> kind</c><00:01:57.600><c> of</c><00:01:57.759><c> things</c>

00:01:58.230 --> 00:01:58.240 align:start position:0%
see. Now there are three kind of things
 

00:01:58.240 --> 00:01:59.830 align:start position:0%
see. Now there are three kind of things
I've<00:01:58.479><c> been</c><00:01:58.640><c> highlighting</c><00:01:59.119><c> to</c><00:01:59.280><c> clients</c><00:01:59.600><c> over</c>

00:01:59.830 --> 00:01:59.840 align:start position:0%
I've been highlighting to clients over
 

00:01:59.840 --> 00:02:02.870 align:start position:0%
I've been highlighting to clients over
this<00:02:00.079><c> time.</c><00:02:00.719><c> Um</c><00:02:01.119><c> the</c><00:02:01.439><c> first</c><00:02:01.759><c> is</c><00:02:02.000><c> that</c><00:02:02.560><c> we</c>

00:02:02.870 --> 00:02:02.880 align:start position:0%
this time. Um the first is that we
 

00:02:02.880 --> 00:02:06.870 align:start position:0%
this time. Um the first is that we
believe<00:02:03.280><c> that</c><00:02:03.840><c> 4.0</c><00:02:04.399><c> is</c><00:02:04.479><c> a</c><00:02:04.640><c> new</c><00:02:04.920><c> 3.8.</c><00:02:05.920><c> So</c><00:02:06.159><c> 3.8</c>

00:02:06.870 --> 00:02:06.880 align:start position:0%
believe that 4.0 is a new 3.8. So 3.8
 

00:02:06.880 --> 00:02:08.790 align:start position:0%
believe that 4.0 is a new 3.8. So 3.8
used<00:02:07.040><c> to</c><00:02:07.200><c> be</c><00:02:07.280><c> a</c><00:02:07.520><c> fairly</c><00:02:07.840><c> neutral</c><00:02:08.399><c> end</c><00:02:08.640><c> of</c>

00:02:08.790 --> 00:02:08.800 align:start position:0%
used to be a fairly neutral end of
 

00:02:08.800 --> 00:02:11.830 align:start position:0%
used to be a fairly neutral end of
season<00:02:09.599><c> as</c><00:02:09.840><c> you</c><00:02:10.000><c> can</c><00:02:10.160><c> see</c><00:02:10.720><c> uh</c><00:02:10.879><c> in</c><00:02:11.120><c> October.</c>

00:02:11.830 --> 00:02:11.840 align:start position:0%
season as you can see uh in October.
 

00:02:11.840 --> 00:02:14.070 align:start position:0%
season as you can see uh in October.
right<00:02:12.160><c> now.</c><00:02:12.560><c> Uh</c><00:02:13.040><c> when</c><00:02:13.200><c> we</c><00:02:13.360><c> ended</c><00:02:13.680><c> last</c>

00:02:14.070 --> 00:02:14.080 align:start position:0%
right now. Uh when we ended last
 

00:02:14.080 --> 00:02:17.190 align:start position:0%
right now. Uh when we ended last
October,<00:02:15.040><c> we</c><00:02:15.280><c> showed</c><00:02:15.520><c> a</c><00:02:15.760><c> 39</c><00:02:16.640><c> for</c><00:02:16.800><c> the</c><00:02:16.959><c> ending</c>

00:02:17.190 --> 00:02:17.200 align:start position:0%
October, we showed a 39 for the ending
 

00:02:17.200 --> 00:02:19.030 align:start position:0%
October, we showed a 39 for the ending
October<00:02:17.599><c> inventory.</c><00:02:18.160><c> Now,</c><00:02:18.640><c> you</c><00:02:18.879><c> see</c>

00:02:19.030 --> 00:02:19.040 align:start position:0%
October inventory. Now, you see
 

00:02:19.040 --> 00:02:21.309 align:start position:0%
October inventory. Now, you see
injections<00:02:19.520><c> for</c><00:02:19.680><c> the</c><00:02:19.840><c> first</c><00:02:20.000><c> two</c><00:02:20.239><c> weeks</c><00:02:20.400><c> of</c>

00:02:21.309 --> 00:02:21.319 align:start position:0%
injections for the first two weeks of
 

00:02:21.319 --> 00:02:23.589 align:start position:0%
injections for the first two weeks of
November.<00:02:22.319><c> And</c><00:02:22.480><c> so</c><00:02:22.640><c> with</c><00:02:22.879><c> those</c><00:02:23.040><c> injections,</c>

00:02:23.589 --> 00:02:23.599 align:start position:0%
November. And so with those injections,
 

00:02:23.599 --> 00:02:25.270 align:start position:0%
November. And so with those injections,
you<00:02:23.840><c> do</c><00:02:24.000><c> see</c><00:02:24.160><c> inventories</c><00:02:24.800><c> get</c><00:02:24.959><c> higher</c><00:02:25.200><c> than</c>

00:02:25.270 --> 00:02:25.280 align:start position:0%
you do see inventories get higher than
 

00:02:25.280 --> 00:02:27.430 align:start position:0%
you do see inventories get higher than
that,<00:02:25.520><c> but</c><00:02:26.080><c> the</c><00:02:26.480><c> 5-year</c><00:02:26.959><c> average</c><00:02:27.200><c> is</c>

00:02:27.430 --> 00:02:27.440 align:start position:0%
that, but the 5-year average is
 

00:02:27.440 --> 00:02:30.550 align:start position:0%
that, but the 5-year average is
somewhere<00:02:27.760><c> around</c><00:02:28.000><c> a</c><00:02:28.160><c> 3.8</c><00:02:28.800><c> end</c><00:02:29.040><c> of</c><00:02:29.200><c> season</c><00:02:29.599><c> for</c>

00:02:30.550 --> 00:02:30.560 align:start position:0%
somewhere around a 3.8 end of season for
 

00:02:30.560 --> 00:02:33.750 align:start position:0%
somewhere around a 3.8 end of season for
no<00:02:31.239><c> October.</c><00:02:32.239><c> But</c><00:02:32.400><c> we</c><00:02:32.560><c> believe</c><00:02:32.800><c> that</c><00:02:33.040><c> 4.0</c><00:02:33.599><c> is</c>

00:02:33.750 --> 00:02:33.760 align:start position:0%
no October. But we believe that 4.0 is
 

00:02:33.760 --> 00:02:35.750 align:start position:0%
no October. But we believe that 4.0 is
the<00:02:34.000><c> new</c><00:02:34.160><c> kind</c><00:02:34.319><c> of</c><00:02:34.480><c> neutral</c><00:02:34.959><c> standpoint.</c><00:02:35.519><c> Now,</c>

00:02:35.750 --> 00:02:35.760 align:start position:0%
the new kind of neutral standpoint. Now,
 

00:02:35.760 --> 00:02:37.350 align:start position:0%
the new kind of neutral standpoint. Now,
why<00:02:36.000><c> is</c><00:02:36.239><c> that?</c><00:02:36.480><c> You</c><00:02:36.720><c> can</c><00:02:36.800><c> look,</c><00:02:36.959><c> we</c><00:02:37.120><c> had</c><00:02:37.200><c> a</c>

00:02:37.350 --> 00:02:37.360 align:start position:0%
why is that? You can look, we had a
 

00:02:37.360 --> 00:02:39.030 align:start position:0%
why is that? You can look, we had a
fairly<00:02:37.680><c> normal</c><00:02:38.080><c> winter,</c><00:02:38.319><c> but</c><00:02:38.480><c> in</c><00:02:38.560><c> a</c><00:02:38.720><c> normal</c>

00:02:39.030 --> 00:02:39.040 align:start position:0%
fairly normal winter, but in a normal
 

00:02:39.040 --> 00:02:41.589 align:start position:0%
fairly normal winter, but in a normal
winter<00:02:39.920><c> we</c><00:02:40.239><c> saw</c><00:02:40.400><c> the</c><00:02:40.640><c> US</c><00:02:40.879><c> would</c><00:02:41.120><c> draw</c><00:02:41.280><c> around</c>

00:02:41.589 --> 00:02:41.599 align:start position:0%
winter we saw the US would draw around
 

00:02:41.599 --> 00:02:44.710 align:start position:0%
winter we saw the US would draw around
2.1<00:02:42.239><c> TCF,</c><00:02:42.800><c> which</c><00:02:42.959><c> is</c><00:02:43.120><c> a</c><00:02:43.280><c> larger</c><00:02:43.680><c> draw</c><00:02:44.400><c> than</c>

00:02:44.710 --> 00:02:44.720 align:start position:0%
2.1 TCF, which is a larger draw than
 

00:02:44.720 --> 00:02:47.589 align:start position:0%
2.1 TCF, which is a larger draw than
normal<00:02:45.360><c> by</c><00:02:45.680><c> about</c><00:02:45.920><c> a</c><00:02:46.080><c> 100</c><00:02:46.400><c> BCF</c><00:02:46.959><c> in</c><00:02:47.200><c> what</c><00:02:47.360><c> is</c>

00:02:47.589 --> 00:02:47.599 align:start position:0%
normal by about a 100 BCF in what is
 

00:02:47.599 --> 00:02:50.070 align:start position:0%
normal by about a 100 BCF in what is
what<00:02:47.760><c> was</c><00:02:47.920><c> a</c><00:02:48.080><c> fairly</c><00:02:48.400><c> normal</c><00:02:48.800><c> weather</c><00:02:49.120><c> winter.</c>

00:02:50.070 --> 00:02:50.080 align:start position:0%
what was a fairly normal weather winter.
 

00:02:50.080 --> 00:02:51.830 align:start position:0%
what was a fairly normal weather winter.
And<00:02:50.319><c> that's</c><00:02:50.640><c> primarily</c><00:02:51.120><c> because</c><00:02:51.360><c> the</c><00:02:51.599><c> large</c>

00:02:51.830 --> 00:02:51.840 align:start position:0%
And that's primarily because the large
 

00:02:51.840 --> 00:02:53.910 align:start position:0%
And that's primarily because the large
growth<00:02:52.000><c> in</c><00:02:52.239><c> LG</c><00:02:52.800><c> that</c><00:02:53.040><c> we're</c><00:02:53.280><c> seeing</c><00:02:53.519><c> more</c><00:02:53.680><c> and</c>

00:02:53.910 --> 00:02:53.920 align:start position:0%
growth in LG that we're seeing more and
 

00:02:53.920 --> 00:02:55.990 align:start position:0%
growth in LG that we're seeing more and
more<00:02:54.160><c> structural</c><00:02:54.560><c> demand.</c><00:02:55.360><c> Another</c><00:02:55.680><c> thing</c><00:02:55.760><c> to</c>

00:02:55.990 --> 00:02:56.000 align:start position:0%
more structural demand. Another thing to
 

00:02:56.000 --> 00:02:57.750 align:start position:0%
more structural demand. Another thing to
kind<00:02:56.080><c> of</c><00:02:56.160><c> highlight</c><00:02:56.480><c> is</c><00:02:56.720><c> this</c><00:02:56.879><c> past</c><00:02:57.200><c> January,</c>

00:02:57.750 --> 00:02:57.760 align:start position:0%
kind of highlight is this past January,
 

00:02:57.760 --> 00:03:00.229 align:start position:0%
kind of highlight is this past January,
we<00:02:58.080><c> saw</c><00:02:58.239><c> the</c><00:02:58.480><c> US</c><00:02:58.800><c> set</c><00:02:59.040><c> a</c><00:02:59.200><c> record</c><00:02:59.440><c> draw</c><00:02:59.760><c> with</c>

00:03:00.229 --> 00:03:00.239 align:start position:0%
we saw the US set a record draw with
 

00:03:00.239 --> 00:03:02.390 align:start position:0%
we saw the US set a record draw with
right<00:03:00.480><c> around</c><00:03:00.640><c> a</c><00:03:00.879><c> TCF</c><00:03:01.519><c> draw</c><00:03:01.760><c> for</c><00:03:01.920><c> a</c><00:03:02.080><c> single</c>

00:03:02.390 --> 00:03:02.400 align:start position:0%
right around a TCF draw for a single
 

00:03:02.400 --> 00:03:04.470 align:start position:0%
right around a TCF draw for a single
month,<00:03:02.720><c> which</c><00:03:02.879><c> is</c><00:03:03.360><c> pretty</c><00:03:03.680><c> phenomenal.</c><00:03:04.239><c> Now,</c>

00:03:04.470 --> 00:03:04.480 align:start position:0%
month, which is pretty phenomenal. Now,
 

00:03:04.480 --> 00:03:07.350 align:start position:0%
month, which is pretty phenomenal. Now,
it<00:03:04.560><c> was</c><00:03:04.720><c> a</c><00:03:04.879><c> very</c><00:03:05.120><c> cold</c><00:03:05.640><c> January,</c><00:03:06.640><c> but</c><00:03:07.040><c> that</c>

00:03:07.350 --> 00:03:07.360 align:start position:0%
it was a very cold January, but that
 

00:03:07.360 --> 00:03:09.110 align:start position:0%
it was a very cold January, but that
shows<00:03:07.599><c> you</c><00:03:07.760><c> what</c><00:03:08.000><c> can</c><00:03:08.159><c> happen</c><00:03:08.400><c> in</c><00:03:08.560><c> a</c><00:03:08.800><c> fairly</c>

00:03:09.110 --> 00:03:09.120 align:start position:0%
shows you what can happen in a fairly
 

00:03:09.120 --> 00:03:11.990 align:start position:0%
shows you what can happen in a fairly
cold<00:03:09.519><c> winter.</c><00:03:10.400><c> And</c><00:03:10.640><c> so,</c><00:03:10.959><c> with</c><00:03:11.440><c> now</c><00:03:11.760><c> the</c>

00:03:11.990 --> 00:03:12.000 align:start position:0%
cold winter. And so, with now the
 

00:03:12.000 --> 00:03:14.070 align:start position:0%
cold winter. And so, with now the
growing<00:03:12.319><c> LNG,</c><00:03:13.040><c> we're</c><00:03:13.280><c> we're</c><00:03:13.680><c> expecting</c>

00:03:14.070 --> 00:03:14.080 align:start position:0%
growing LNG, we're we're expecting
 

00:03:14.080 --> 00:03:16.149 align:start position:0%
growing LNG, we're we're expecting
larger<00:03:14.480><c> and</c><00:03:14.640><c> larger</c><00:03:14.959><c> draws</c><00:03:15.280><c> each</c><00:03:15.599><c> summer,</c>

00:03:16.149 --> 00:03:16.159 align:start position:0%
larger and larger draws each summer,
 

00:03:16.159 --> 00:03:18.309 align:start position:0%
larger and larger draws each summer,
which<00:03:16.400><c> is</c><00:03:16.560><c> going</c><00:03:16.640><c> to</c><00:03:16.800><c> make</c><00:03:17.519><c> the</c><00:03:17.760><c> market</c><00:03:18.080><c> the</c>

00:03:18.309 --> 00:03:18.319 align:start position:0%
which is going to make the market the
 

00:03:18.319 --> 00:03:20.630 align:start position:0%
which is going to make the market the
market<00:03:18.480><c> will</c><00:03:18.720><c> need</c><00:03:18.879><c> to</c><00:03:19.040><c> get</c><00:03:19.200><c> closer</c><00:03:19.599><c> to</c><00:03:19.760><c> a</c><00:03:19.920><c> 4TCF</c>

00:03:20.630 --> 00:03:20.640 align:start position:0%
market will need to get closer to a 4TCF
 

00:03:20.640 --> 00:03:23.750 align:start position:0%
market will need to get closer to a 4TCF
instead<00:03:20.879><c> of</c><00:03:20.959><c> a</c><00:03:21.120><c> 38</c><00:03:21.920><c> to</c><00:03:22.159><c> get</c><00:03:22.319><c> through</c><00:03:22.760><c> winter.</c>

00:03:23.750 --> 00:03:23.760 align:start position:0%
instead of a 38 to get through winter.
 

00:03:23.760 --> 00:03:25.670 align:start position:0%
instead of a 38 to get through winter.
Now,<00:03:24.000><c> what</c><00:03:24.159><c> have</c><00:03:24.319><c> we</c><00:03:24.480><c> seen</c><00:03:24.640><c> in</c><00:03:24.879><c> the</c><00:03:25.040><c> market</c>

00:03:25.670 --> 00:03:25.680 align:start position:0%
Now, what have we seen in the market
 

00:03:25.680 --> 00:03:28.309 align:start position:0%
Now, what have we seen in the market
over<00:03:26.000><c> the</c><00:03:26.239><c> last</c><00:03:27.200><c> 10</c><00:03:27.440><c> years?</c><00:03:27.760><c> And</c><00:03:27.920><c> why</c><00:03:28.080><c> is</c><00:03:28.159><c> this</c>

00:03:28.309 --> 00:03:28.319 align:start position:0%
over the last 10 years? And why is this
 

00:03:28.319 --> 00:03:30.309 align:start position:0%
over the last 10 years? And why is this
the<00:03:28.480><c> case?</c><00:03:28.720><c> Well,</c><00:03:28.959><c> over</c><00:03:29.040><c> the</c><00:03:29.200><c> last</c><00:03:29.760><c> 10</c><00:03:30.000><c> years,</c>

00:03:30.309 --> 00:03:30.319 align:start position:0%
the case? Well, over the last 10 years,
 

00:03:30.319 --> 00:03:33.030 align:start position:0%
the case? Well, over the last 10 years,
the<00:03:30.560><c> market's</c><00:03:30.959><c> grown</c><00:03:31.280><c> by</c><00:03:31.760><c> 60%.</c><00:03:32.480><c> So</c><00:03:32.720><c> we've</c><00:03:32.879><c> gone</c>

00:03:33.030 --> 00:03:33.040 align:start position:0%
the market's grown by 60%. So we've gone
 

00:03:33.040 --> 00:03:35.910 align:start position:0%
the market's grown by 60%. So we've gone
from<00:03:33.120><c> about</c><00:03:33.360><c> a</c><00:03:33.519><c> 65</c><00:03:34.080><c> BCF</c><00:03:34.480><c> a</c><00:03:34.640><c> day</c><00:03:34.879><c> market</c><00:03:35.440><c> up</c><00:03:35.680><c> to</c>

00:03:35.910 --> 00:03:35.920 align:start position:0%
from about a 65 BCF a day market up to
 

00:03:35.920 --> 00:03:38.390 align:start position:0%
from about a 65 BCF a day market up to
105<00:03:36.480><c> BCF</c><00:03:36.879><c> a</c><00:03:37.040><c> day</c><00:03:37.120><c> market.</c><00:03:37.440><c> So</c><00:03:37.519><c> that's</c><00:03:37.680><c> a</c><00:03:37.840><c> 60%</c>

00:03:38.390 --> 00:03:38.400 align:start position:0%
105 BCF a day market. So that's a 60%
 

00:03:38.400 --> 00:03:40.070 align:start position:0%
105 BCF a day market. So that's a 60%
growth.<00:03:38.959><c> When</c><00:03:39.200><c> we</c><00:03:39.280><c> think</c><00:03:39.440><c> about</c><00:03:39.680><c> storage</c>

00:03:40.070 --> 00:03:40.080 align:start position:0%
growth. When we think about storage
 

00:03:40.080 --> 00:03:42.710 align:start position:0%
growth. When we think about storage
capacity,<00:03:40.640><c> storage</c><00:03:41.120><c> hasn't</c><00:03:41.440><c> grown</c><00:03:42.159><c> really</c><00:03:42.480><c> in</c>

00:03:42.710 --> 00:03:42.720 align:start position:0%
capacity, storage hasn't grown really in
 

00:03:42.720 --> 00:03:45.270 align:start position:0%
capacity, storage hasn't grown really in
the<00:03:42.879><c> last</c><00:03:43.040><c> 10</c><00:03:43.360><c> years.</c><00:03:44.080><c> So</c><00:03:44.319><c> what</c><00:03:44.560><c> that</c><00:03:44.799><c> means</c><00:03:44.959><c> is</c>

00:03:45.270 --> 00:03:45.280 align:start position:0%
the last 10 years. So what that means is
 

00:03:45.280 --> 00:03:47.270 align:start position:0%
the last 10 years. So what that means is
the<00:03:45.519><c> market's</c><00:03:45.920><c> ability</c><00:03:46.239><c> to</c><00:03:46.480><c> transport</c><00:03:46.959><c> gas</c>

00:03:47.270 --> 00:03:47.280 align:start position:0%
the market's ability to transport gas
 

00:03:47.280 --> 00:03:49.910 align:start position:0%
the market's ability to transport gas
through<00:03:47.599><c> time</c><00:03:48.239><c> has</c><00:03:48.560><c> actually</c><00:03:48.799><c> diminished</c><00:03:49.680><c> as</c>

00:03:49.910 --> 00:03:49.920 align:start position:0%
through time has actually diminished as
 

00:03:49.920 --> 00:03:52.070 align:start position:0%
through time has actually diminished as
a<00:03:50.159><c> percentage</c><00:03:50.400><c> of</c><00:03:50.560><c> total</c><00:03:50.959><c> market</c><00:03:51.280><c> size.</c><00:03:51.920><c> And</c>

00:03:52.070 --> 00:03:52.080 align:start position:0%
a percentage of total market size. And
 

00:03:52.080 --> 00:03:54.550 align:start position:0%
a percentage of total market size. And
so<00:03:52.400><c> because</c><00:03:52.640><c> of</c><00:03:52.879><c> that,</c><00:03:53.440><c> we</c><00:03:53.760><c> have</c><00:03:53.920><c> to</c><00:03:54.159><c> utilize</c>

00:03:54.550 --> 00:03:54.560 align:start position:0%
so because of that, we have to utilize
 

00:03:54.560 --> 00:03:58.550 align:start position:0%
so because of that, we have to utilize
more<00:03:54.799><c> and</c><00:03:55.040><c> more</c><00:03:55.680><c> of</c><00:03:55.920><c> that</c><00:03:56.239><c> 4TCF</c><00:03:57.360><c> uh</c><00:03:57.519><c> roughly</c><00:03:58.000><c> of</c>

00:03:58.550 --> 00:03:58.560 align:start position:0%
more and more of that 4TCF uh roughly of
 

00:03:58.560 --> 00:04:01.589 align:start position:0%
more and more of that 4TCF uh roughly of
US<00:03:59.200><c> storage</c><00:03:59.599><c> capacity.</c><00:04:00.560><c> And</c><00:04:00.799><c> so</c><00:04:01.040><c> we're</c><00:04:01.200><c> we're</c>

00:04:01.589 --> 00:04:01.599 align:start position:0%
US storage capacity. And so we're we're
 

00:04:01.599 --> 00:04:03.589 align:start position:0%
US storage capacity. And so we're we're
utilizing<00:04:02.159><c> more</c><00:04:02.480><c> storage</c><00:04:02.879><c> capacity</c><00:04:03.360><c> even</c>

00:04:03.589 --> 00:04:03.599 align:start position:0%
utilizing more storage capacity even
 

00:04:03.599 --> 00:04:06.229 align:start position:0%
utilizing more storage capacity even
though<00:04:03.760><c> it's</c><00:04:03.920><c> a</c><00:04:04.159><c> smaller</c><00:04:04.560><c> percent</c><00:04:05.360><c> of</c><00:04:05.920><c> US</c>

00:04:06.229 --> 00:04:06.239 align:start position:0%
though it's a smaller percent of US
 

00:04:06.239 --> 00:04:09.910 align:start position:0%
though it's a smaller percent of US
market.<00:04:06.720><c> So</c><00:04:07.519><c> 4TCF</c><00:04:08.400><c> is</c><00:04:08.720><c> kind</c><00:04:08.879><c> of</c><00:04:08.959><c> where</c><00:04:09.519><c> my</c>

00:04:09.910 --> 00:04:09.920 align:start position:0%
market. So 4TCF is kind of where my
 

00:04:09.920 --> 00:04:11.990 align:start position:0%
market. So 4TCF is kind of where my
neutral<00:04:10.480><c> number</c><00:04:10.799><c> is</c><00:04:10.959><c> right</c><00:04:11.120><c> now.</c><00:04:11.280><c> As</c><00:04:11.519><c> I</c><00:04:11.680><c> see</c>

00:04:11.990 --> 00:04:12.000 align:start position:0%
neutral number is right now. As I see
 

00:04:12.000 --> 00:04:14.390 align:start position:0%
neutral number is right now. As I see
estimates<00:04:12.480><c> coming</c><00:04:12.799><c> below</c><00:04:13.120><c> that,</c><00:04:13.840><c> then</c><00:04:14.159><c> I</c>

00:04:14.390 --> 00:04:14.400 align:start position:0%
estimates coming below that, then I
 

00:04:14.400 --> 00:04:16.310 align:start position:0%
estimates coming below that, then I
would<00:04:14.480><c> argue</c><00:04:14.799><c> the</c><00:04:15.040><c> market</c><00:04:15.360><c> needs</c><00:04:15.680><c> to</c><00:04:15.840><c> loosen</c>

00:04:16.310 --> 00:04:16.320 align:start position:0%
would argue the market needs to loosen
 

00:04:16.320 --> 00:04:18.749 align:start position:0%
would argue the market needs to loosen
in<00:04:16.560><c> order</c><00:04:16.720><c> to</c><00:04:16.959><c> fill</c><00:04:17.359><c> completely</c><00:04:17.919><c> heading</c><00:04:18.160><c> into</c>

00:04:18.749 --> 00:04:18.759 align:start position:0%
in order to fill completely heading into
 

00:04:18.759 --> 00:04:21.189 align:start position:0%
in order to fill completely heading into
winter.<00:04:19.759><c> Now</c><00:04:19.919><c> what</c><00:04:20.160><c> we've</c><00:04:20.320><c> seen</c><00:04:20.560><c> to</c><00:04:20.720><c> remedy</c>

00:04:21.189 --> 00:04:21.199 align:start position:0%
winter. Now what we've seen to remedy
 

00:04:21.199 --> 00:04:23.830 align:start position:0%
winter. Now what we've seen to remedy
the<00:04:21.440><c> fact</c><00:04:21.600><c> that</c><00:04:22.400><c> storage</c><00:04:23.040><c> hasn't</c><00:04:23.360><c> grown</c><00:04:23.680><c> over</c>

00:04:23.830 --> 00:04:23.840 align:start position:0%
the fact that storage hasn't grown over
 

00:04:23.840 --> 00:04:25.909 align:start position:0%
the fact that storage hasn't grown over
the<00:04:23.919><c> last</c><00:04:24.080><c> 10</c><00:04:24.240><c> years</c><00:04:24.479><c> is</c><00:04:24.720><c> we've</c><00:04:24.960><c> seen</c><00:04:25.280><c> storage</c>

00:04:25.909 --> 00:04:25.919 align:start position:0%
the last 10 years is we've seen storage
 

00:04:25.919 --> 00:04:27.990 align:start position:0%
the last 10 years is we've seen storage
uh<00:04:26.000><c> we've</c><00:04:26.160><c> seen</c><00:04:26.320><c> producers</c><00:04:27.280><c> functioning</c><00:04:27.759><c> as</c>

00:04:27.990 --> 00:04:28.000 align:start position:0%
uh we've seen producers functioning as
 

00:04:28.000 --> 00:04:30.310 align:start position:0%
uh we've seen producers functioning as
synthetic<00:04:28.479><c> storage.</c><00:04:29.120><c> And</c><00:04:29.360><c> you</c><00:04:29.600><c> can</c><00:04:29.759><c> see</c><00:04:30.000><c> over</c>

00:04:30.310 --> 00:04:30.320 align:start position:0%
synthetic storage. And you can see over
 

00:04:30.320 --> 00:04:33.030 align:start position:0%
synthetic storage. And you can see over
the<00:04:30.560><c> last</c><00:04:31.040><c> year</c><00:04:31.680><c> what's</c><00:04:32.000><c> happened</c><00:04:32.560><c> is</c><00:04:32.800><c> when</c>

00:04:33.030 --> 00:04:33.040 align:start position:0%
the last year what's happened is when
 

00:04:33.040 --> 00:04:34.629 align:start position:0%
the last year what's happened is when
the<00:04:33.199><c> market</c><00:04:33.440><c> was</c><00:04:33.600><c> over</c><00:04:33.840><c> supplied</c><00:04:34.320><c> last</c>

00:04:34.629 --> 00:04:34.639 align:start position:0%
the market was over supplied last
 

00:04:34.639 --> 00:04:36.870 align:start position:0%
the market was over supplied last
February<00:04:34.960><c> and</c><00:04:35.120><c> we're</c><00:04:35.360><c> producing</c><00:04:35.680><c> on</c><00:04:36.000><c> 106</c><00:04:36.479><c> BCF</c>

00:04:36.870 --> 00:04:36.880 align:start position:0%
February and we're producing on 106 BCF
 

00:04:36.880 --> 00:04:39.430 align:start position:0%
February and we're producing on 106 BCF
a<00:04:37.120><c> day,</c><00:04:37.759><c> the</c><00:04:38.080><c> market</c><00:04:38.320><c> took</c><00:04:38.639><c> price</c><00:04:38.960><c> down</c><00:04:39.199><c> to</c>

00:04:39.430 --> 00:04:39.440 align:start position:0%
a day, the market took price down to
 

00:04:39.440 --> 00:04:42.230 align:start position:0%
a day, the market took price down to
around<00:04:39.800><c> a$150</c><00:04:40.800><c> and</c><00:04:41.040><c> over</c><00:04:41.360><c> the</c><00:04:41.600><c> next</c><00:04:41.919><c> four</c>

00:04:42.230 --> 00:04:42.240 align:start position:0%
around a$150 and over the next four
 

00:04:42.240 --> 00:04:44.950 align:start position:0%
around a$150 and over the next four
months<00:04:43.160><c> producers</c><00:04:44.160><c> reduced</c><00:04:44.479><c> production</c><00:04:44.800><c> by</c>

00:04:44.950 --> 00:04:44.960 align:start position:0%
months producers reduced production by
 

00:04:44.960 --> 00:04:48.230 align:start position:0%
months producers reduced production by
about<00:04:45.280><c> 7</c><00:04:45.520><c> BCF</c><00:04:46.000><c> a</c><00:04:46.160><c> day.</c><00:04:46.880><c> Then</c><00:04:47.360><c> markets</c><00:04:47.919><c> around</c>

00:04:48.230 --> 00:04:48.240 align:start position:0%
about 7 BCF a day. Then markets around
 

00:04:48.240 --> 00:04:50.950 align:start position:0%
about 7 BCF a day. Then markets around
May<00:04:48.560><c> and</c><00:04:48.720><c> June</c><00:04:49.280><c> rebounded</c><00:04:49.919><c> as</c><00:04:50.240><c> demand</c><00:04:50.720><c> started</c>

00:04:50.950 --> 00:04:50.960 align:start position:0%
May and June rebounded as demand started
 

00:04:50.960 --> 00:04:53.270 align:start position:0%
May and June rebounded as demand started
to<00:04:51.120><c> show</c><00:04:51.280><c> up</c><00:04:51.440><c> and</c><00:04:51.759><c> production</c><00:04:52.080><c> recovered.</c><00:04:53.120><c> And</c>

00:04:53.270 --> 00:04:53.280 align:start position:0%
to show up and production recovered. And
 

00:04:53.280 --> 00:04:55.590 align:start position:0%
to show up and production recovered. And
then<00:04:53.440><c> what</c><00:04:53.680><c> you</c><00:04:53.840><c> saw</c><00:04:53.919><c> in</c><00:04:54.160><c> November</c><00:04:54.800><c> was</c><00:04:55.120><c> as</c><00:04:55.360><c> we</c>

00:04:55.590 --> 00:04:55.600 align:start position:0%
then what you saw in November was as we
 

00:04:55.600 --> 00:04:57.830 align:start position:0%
then what you saw in November was as we
got<00:04:55.840><c> full,</c><00:04:56.560><c> we</c><00:04:56.720><c> got</c><00:04:56.880><c> into</c><00:04:57.120><c> November</c><00:04:57.440><c> and</c><00:04:57.680><c> there</c>

00:04:57.830 --> 00:04:57.840 align:start position:0%
got full, we got into November and there
 

00:04:57.840 --> 00:05:00.550 align:start position:0%
got full, we got into November and there
was<00:04:57.919><c> a</c><00:04:58.080><c> warm</c><00:04:58.800><c> uh</c><00:04:58.960><c> start</c><00:04:59.199><c> to</c><00:04:59.520><c> November</c><00:05:00.160><c> and</c><00:05:00.400><c> the</c>

00:05:00.550 --> 00:05:00.560 align:start position:0%
was a warm uh start to November and the
 

00:05:00.560 --> 00:05:02.629 align:start position:0%
was a warm uh start to November and the
cold<00:05:00.800><c> didn't</c><00:05:01.040><c> show</c><00:05:01.280><c> up</c><00:05:01.759><c> and</c><00:05:02.000><c> so</c><00:05:02.240><c> prices</c>

00:05:02.629 --> 00:05:02.639 align:start position:0%
cold didn't show up and so prices
 

00:05:02.639 --> 00:05:05.189 align:start position:0%
cold didn't show up and so prices
dropped<00:05:02.880><c> to</c><00:05:03.120><c> a$124</c><00:05:03.840><c> at</c><00:05:04.080><c> Henry</c><00:05:04.400><c> Hub.</c><00:05:04.880><c> I</c><00:05:05.120><c> think</c>

00:05:05.189 --> 00:05:05.199 align:start position:0%
dropped to a$124 at Henry Hub. I think
 

00:05:05.199 --> 00:05:07.270 align:start position:0%
dropped to a$124 at Henry Hub. I think
it<00:05:05.440><c> was</c><00:05:05.600><c> over</c><00:05:05.759><c> the</c><00:05:06.080><c> weekend</c><00:05:06.320><c> of</c><00:05:06.560><c> November</c><00:05:07.039><c> 10th</c>

00:05:07.270 --> 00:05:07.280 align:start position:0%
it was over the weekend of November 10th
 

00:05:07.280 --> 00:05:09.350 align:start position:0%
it was over the weekend of November 10th
through<00:05:07.520><c> the</c><00:05:07.680><c> 12th</c><00:05:08.400><c> and</c><00:05:08.639><c> over</c><00:05:08.880><c> a</c><00:05:09.039><c> period</c><00:05:09.280><c> of</c>

00:05:09.350 --> 00:05:09.360 align:start position:0%
through the 12th and over a period of
 

00:05:09.360 --> 00:05:11.590 align:start position:0%
through the 12th and over a period of
around<00:05:09.759><c> 14</c><00:05:10.160><c> days,</c><00:05:10.479><c> producers</c><00:05:10.960><c> curtailed</c>

00:05:11.590 --> 00:05:11.600 align:start position:0%
around 14 days, producers curtailed
 

00:05:11.600 --> 00:05:14.390 align:start position:0%
around 14 days, producers curtailed
three<00:05:11.840><c> BCF</c><00:05:12.240><c> a</c><00:05:12.400><c> day</c><00:05:12.560><c> of</c><00:05:12.840><c> production</c><00:05:13.840><c> into</c><00:05:14.160><c> that</c>

00:05:14.390 --> 00:05:14.400 align:start position:0%
three BCF a day of production into that
 

00:05:14.400 --> 00:05:16.390 align:start position:0%
three BCF a day of production into that
weekend<00:05:15.039><c> and</c><00:05:15.280><c> then</c><00:05:15.520><c> brought</c><00:05:15.680><c> it</c><00:05:15.919><c> back</c><00:05:16.000><c> on</c>

00:05:16.390 --> 00:05:16.400 align:start position:0%
weekend and then brought it back on
 

00:05:16.400 --> 00:05:18.790 align:start position:0%
weekend and then brought it back on
almost<00:05:16.880><c> immediately</c><00:05:17.759><c> as</c><00:05:18.000><c> that</c><00:05:18.240><c> cold</c><00:05:18.560><c> started</c>

00:05:18.790 --> 00:05:18.800 align:start position:0%
almost immediately as that cold started
 

00:05:18.800 --> 00:05:20.950 align:start position:0%
almost immediately as that cold started
to<00:05:18.880><c> show</c><00:05:18.960><c> up.</c><00:05:19.199><c> So</c><00:05:19.360><c> we're</c><00:05:19.600><c> seeing</c><00:05:20.240><c> with</c><00:05:20.560><c> lack</c><00:05:20.800><c> of</c>

00:05:20.950 --> 00:05:20.960 align:start position:0%
to show up. So we're seeing with lack of
 

00:05:20.960 --> 00:05:22.310 align:start position:0%
to show up. So we're seeing with lack of
build<00:05:21.199><c> out</c><00:05:21.360><c> of</c><00:05:21.520><c> storage</c><00:05:21.759><c> capacity,</c><00:05:22.160><c> we're</c>

00:05:22.310 --> 00:05:22.320 align:start position:0%
build out of storage capacity, we're
 

00:05:22.320 --> 00:05:25.029 align:start position:0%
build out of storage capacity, we're
seeing<00:05:22.560><c> producers</c><00:05:23.199><c> function</c><00:05:24.160><c> as</c><00:05:24.479><c> synthetic</c>

00:05:25.029 --> 00:05:25.039 align:start position:0%
seeing producers function as synthetic
 

00:05:25.039 --> 00:05:27.990 align:start position:0%
seeing producers function as synthetic
storage.<00:05:26.400><c> Now,</c><00:05:26.960><c> that</c><00:05:27.199><c> points</c><00:05:27.520><c> to</c><00:05:27.680><c> another</c>

00:05:27.990 --> 00:05:28.000 align:start position:0%
storage. Now, that points to another
 

00:05:28.000 --> 00:05:30.150 align:start position:0%
storage. Now, that points to another
thing<00:05:28.080><c> we're</c><00:05:28.400><c> really</c><00:05:28.720><c> watching</c><00:05:29.039><c> for</c><00:05:29.600><c> is</c><00:05:29.919><c> we're</c>

00:05:30.150 --> 00:05:30.160 align:start position:0%
thing we're really watching for is we're
 

00:05:30.160 --> 00:05:32.029 align:start position:0%
thing we're really watching for is we're
watching<00:05:30.560><c> for</c>

00:05:32.029 --> 00:05:32.039 align:start position:0%
watching for
 

00:05:32.039 --> 00:05:35.029 align:start position:0%
watching for
November.<00:05:33.039><c> I</c><00:05:33.360><c> expect</c><00:05:33.600><c> it</c><00:05:33.840><c> to</c><00:05:34.080><c> be</c><00:05:34.400><c> a</c><00:05:34.639><c> fairly</c>

00:05:35.029 --> 00:05:35.039 align:start position:0%
November. I expect it to be a fairly
 

00:05:35.039 --> 00:05:37.189 align:start position:0%
November. I expect it to be a fairly
volatile<00:05:35.600><c> month</c><00:05:35.840><c> going</c><00:05:36.160><c> forward</c><00:05:36.720><c> because</c><00:05:36.960><c> if</c>

00:05:37.189 --> 00:05:37.199 align:start position:0%
volatile month going forward because if
 

00:05:37.199 --> 00:05:40.469 align:start position:0%
volatile month going forward because if
we<00:05:37.280><c> get</c><00:05:37.440><c> to</c><00:05:37.720><c> 4TCF</c><00:05:38.720><c> at</c><00:05:38.960><c> the</c><00:05:39.120><c> end</c><00:05:39.199><c> of</c><00:05:39.360><c> winter</c><00:05:40.240><c> or</c>

00:05:40.469 --> 00:05:40.479 align:start position:0%
we get to 4TCF at the end of winter or
 

00:05:40.479 --> 00:05:42.469 align:start position:0%
we get to 4TCF at the end of winter or
at<00:05:40.639><c> the</c><00:05:40.800><c> end</c><00:05:40.880><c> of</c><00:05:41.039><c> October,</c><00:05:41.759><c> then</c><00:05:42.000><c> that</c><00:05:42.240><c> means</c>

00:05:42.469 --> 00:05:42.479 align:start position:0%
at the end of October, then that means
 

00:05:42.479 --> 00:05:44.550 align:start position:0%
at the end of October, then that means
that<00:05:42.639><c> if</c><00:05:42.800><c> we</c><00:05:42.880><c> have</c><00:05:42.960><c> a</c><00:05:43.199><c> slow</c><00:05:43.440><c> start</c><00:05:43.600><c> to</c><00:05:43.840><c> winter,</c>

00:05:44.550 --> 00:05:44.560 align:start position:0%
that if we have a slow start to winter,
 

00:05:44.560 --> 00:05:47.909 align:start position:0%
that if we have a slow start to winter,
then<00:05:45.120><c> producers</c><00:05:45.919><c> or</c><00:05:46.560><c> uh</c><00:05:46.800><c> the</c><00:05:47.120><c> market</c><00:05:47.440><c> won't</c><00:05:47.759><c> be</c>

00:05:47.909 --> 00:05:47.919 align:start position:0%
then producers or uh the market won't be
 

00:05:47.919 --> 00:05:49.830 align:start position:0%
then producers or uh the market won't be
able<00:05:48.160><c> to</c><00:05:48.400><c> inject</c><00:05:48.800><c> any</c><00:05:49.039><c> over</c><00:05:49.199><c> supply.</c><00:05:49.520><c> If</c><00:05:49.680><c> we</c>

00:05:49.830 --> 00:05:49.840 align:start position:0%
able to inject any over supply. If we
 

00:05:49.840 --> 00:05:51.830 align:start position:0%
able to inject any over supply. If we
have<00:05:49.919><c> a</c><00:05:50.080><c> a</c><00:05:50.479><c> slow</c><00:05:50.800><c> start</c><00:05:50.960><c> to</c><00:05:51.199><c> November,</c><00:05:51.600><c> we</c>

00:05:51.830 --> 00:05:51.840 align:start position:0%
have a a slow start to November, we
 

00:05:51.840 --> 00:05:54.070 align:start position:0%
have a a slow start to November, we
won't<00:05:52.000><c> be</c><00:05:52.080><c> able</c><00:05:52.240><c> to</c><00:05:52.400><c> inject</c><00:05:52.720><c> that</c><00:05:52.960><c> gas</c><00:05:53.759><c> into</c>

00:05:54.070 --> 00:05:54.080 align:start position:0%
won't be able to inject that gas into
 

00:05:54.080 --> 00:05:55.430 align:start position:0%
won't be able to inject that gas into
the<00:05:54.240><c> ground</c><00:05:54.400><c> in</c><00:05:54.639><c> November</c><00:05:54.960><c> because</c><00:05:55.120><c> we'll</c>

00:05:55.430 --> 00:05:55.440 align:start position:0%
the ground in November because we'll
 

00:05:55.440 --> 00:05:57.430 align:start position:0%
the ground in November because we'll
already<00:05:55.759><c> be</c><00:05:55.840><c> at</c><00:05:56.000><c> tank</c><00:05:56.240><c> tops.</c><00:05:56.800><c> And</c><00:05:56.960><c> so</c><00:05:57.199><c> you'll</c>

00:05:57.430 --> 00:05:57.440 align:start position:0%
already be at tank tops. And so you'll
 

00:05:57.440 --> 00:06:00.150 align:start position:0%
already be at tank tops. And so you'll
have<00:05:57.600><c> to</c><00:05:57.759><c> see</c><00:05:58.000><c> prices</c><00:05:58.560><c> drop</c><00:05:59.280><c> to</c><00:05:59.520><c> incentivize</c>

00:06:00.150 --> 00:06:00.160 align:start position:0%
have to see prices drop to incentivize
 

00:06:00.160 --> 00:06:01.350 align:start position:0%
have to see prices drop to incentivize
curtailments<00:06:00.720><c> because</c><00:06:00.880><c> we</c><00:06:01.039><c> won't</c><00:06:01.120><c> have</c><00:06:01.280><c> room</c>

00:06:01.350 --> 00:06:01.360 align:start position:0%
curtailments because we won't have room
 

00:06:01.360 --> 00:06:03.510 align:start position:0%
curtailments because we won't have room
in<00:06:01.600><c> storage.</c><00:06:02.080><c> Now,</c><00:06:02.320><c> on</c><00:06:02.479><c> the</c><00:06:02.639><c> other</c><00:06:02.800><c> side,</c>

00:06:03.510 --> 00:06:03.520 align:start position:0%
in storage. Now, on the other side,
 

00:06:03.520 --> 00:06:05.189 align:start position:0%
in storage. Now, on the other side,
let's<00:06:03.759><c> say</c><00:06:03.840><c> it's</c><00:06:04.080><c> an</c><00:06:04.240><c> early</c><00:06:04.479><c> start</c><00:06:04.560><c> to</c><00:06:04.800><c> winter</c>

00:06:05.189 --> 00:06:05.199 align:start position:0%
let's say it's an early start to winter
 

00:06:05.199 --> 00:06:07.909 align:start position:0%
let's say it's an early start to winter
because<00:06:05.600><c> we</c><00:06:05.840><c> need</c><00:06:06.160><c> every</c><00:06:06.479><c> bit</c><00:06:06.639><c> of</c><00:06:06.720><c> this</c><00:06:06.960><c> 4TCF</c>

00:06:07.909 --> 00:06:07.919 align:start position:0%
because we need every bit of this 4TCF
 

00:06:07.919 --> 00:06:10.230 align:start position:0%
because we need every bit of this 4TCF
to<00:06:08.160><c> make</c><00:06:08.319><c> it</c><00:06:08.479><c> through</c><00:06:08.720><c> winter</c><00:06:09.199><c> and</c><00:06:09.440><c> to</c><00:06:10.000><c> have</c>

00:06:10.230 --> 00:06:10.240 align:start position:0%
to make it through winter and to have
 

00:06:10.240 --> 00:06:13.270 align:start position:0%
to make it through winter and to have
enough<00:06:10.560><c> capacity</c><00:06:11.039><c> to</c><00:06:11.280><c> manage</c><00:06:11.520><c> a</c><00:06:11.759><c> cold</c><00:06:12.280><c> Q1.</c>

00:06:13.270 --> 00:06:13.280 align:start position:0%
enough capacity to manage a cold Q1.
 

00:06:13.280 --> 00:06:15.350 align:start position:0%
enough capacity to manage a cold Q1.
Storage<00:06:13.600><c> players</c><00:06:13.919><c> will</c><00:06:14.080><c> be</c><00:06:14.160><c> hesitant</c><00:06:14.560><c> to</c><00:06:14.880><c> draw</c>

00:06:15.350 --> 00:06:15.360 align:start position:0%
Storage players will be hesitant to draw
 

00:06:15.360 --> 00:06:17.110 align:start position:0%
Storage players will be hesitant to draw
if<00:06:15.680><c> winter</c><00:06:16.080><c> shows</c><00:06:16.319><c> up</c><00:06:16.479><c> early.</c><00:06:16.720><c> And</c><00:06:16.960><c> so</c>

00:06:17.110 --> 00:06:17.120 align:start position:0%
if winter shows up early. And so
 

00:06:17.120 --> 00:06:19.749 align:start position:0%
if winter shows up early. And so
November<00:06:17.520><c> will</c><00:06:17.759><c> have</c><00:06:17.840><c> to</c><00:06:18.000><c> price</c><00:06:18.240><c> up</c><00:06:18.880><c> to</c><00:06:19.120><c> Q1</c>

00:06:19.749 --> 00:06:19.759 align:start position:0%
November will have to price up to Q1
 

00:06:19.759 --> 00:06:22.150 align:start position:0%
November will have to price up to Q1
premium<00:06:20.400><c> prices</c><00:06:20.800><c> in</c><00:06:21.039><c> order</c><00:06:21.120><c> to</c><00:06:21.280><c> incentivize</c>

00:06:22.150 --> 00:06:22.160 align:start position:0%
premium prices in order to incentivize
 

00:06:22.160 --> 00:06:24.150 align:start position:0%
premium prices in order to incentivize
those<00:06:22.479><c> withdrawals</c><00:06:22.960><c> to</c><00:06:23.120><c> roll</c><00:06:23.360><c> forward.</c><00:06:23.919><c> So</c>

00:06:24.150 --> 00:06:24.160 align:start position:0%
those withdrawals to roll forward. So
 

00:06:24.160 --> 00:06:26.150 align:start position:0%
those withdrawals to roll forward. So
because<00:06:24.479><c> of</c><00:06:24.639><c> that</c><00:06:25.360><c> essentially</c><00:06:25.680><c> the</c><00:06:25.919><c> market</c>

00:06:26.150 --> 00:06:26.160 align:start position:0%
because of that essentially the market
 

00:06:26.160 --> 00:06:28.230 align:start position:0%
because of that essentially the market
in<00:06:26.400><c> short</c><00:06:26.720><c> won't</c><00:06:26.960><c> let</c><00:06:27.199><c> November</c><00:06:27.759><c> bounce</c><00:06:28.080><c> off</c>

00:06:28.230 --> 00:06:28.240 align:start position:0%
in short won't let November bounce off
 

00:06:28.240 --> 00:06:30.870 align:start position:0%
in short won't let November bounce off
of<00:06:28.400><c> storage</c><00:06:28.800><c> going</c><00:06:29.120><c> forward.</c><00:06:30.000><c> And</c><00:06:30.160><c> so</c><00:06:30.479><c> you'll</c>

00:06:30.870 --> 00:06:30.880 align:start position:0%
of storage going forward. And so you'll
 

00:06:30.880 --> 00:06:33.510 align:start position:0%
of storage going forward. And so you'll
have<00:06:30.960><c> to</c><00:06:31.199><c> see</c><00:06:31.440><c> either</c><00:06:31.919><c> a</c><00:06:32.240><c> very</c><00:06:32.639><c> normal</c><00:06:33.120><c> weather</c>

00:06:33.510 --> 00:06:33.520 align:start position:0%
have to see either a very normal weather
 

00:06:33.520 --> 00:06:36.309 align:start position:0%
have to see either a very normal weather
November<00:06:34.479><c> or</c><00:06:35.280><c> you're</c><00:06:35.600><c> going</c><00:06:35.680><c> to</c><00:06:35.759><c> see</c><00:06:35.919><c> a</c><00:06:36.160><c> good</c>

00:06:36.309 --> 00:06:36.319 align:start position:0%
November or you're going to see a good
 

00:06:36.319 --> 00:06:38.469 align:start position:0%
November or you're going to see a good
bit<00:06:36.479><c> of</c><00:06:36.639><c> volatility</c><00:06:37.520><c> as</c><00:06:37.840><c> November</c><00:06:38.240><c> is</c><00:06:38.400><c> going</c>

00:06:38.469 --> 00:06:38.479 align:start position:0%
bit of volatility as November is going
 

00:06:38.479 --> 00:06:40.950 align:start position:0%
bit of volatility as November is going
to<00:06:38.560><c> try</c><00:06:38.720><c> to</c><00:06:38.880><c> balance</c><00:06:39.280><c> without</c><00:06:39.800><c> storage.</c><00:06:40.800><c> The</c>

00:06:40.950 --> 00:06:40.960 align:start position:0%
to try to balance without storage. The
 

00:06:40.960 --> 00:06:43.430 align:start position:0%
to try to balance without storage. The
other<00:06:41.199><c> things</c><00:06:41.360><c> to</c><00:06:41.600><c> watch</c><00:06:41.759><c> for</c><00:06:42.800><c> um</c><00:06:43.120><c> you</c><00:06:43.199><c> know</c><00:06:43.360><c> if</c>

00:06:43.430 --> 00:06:43.440 align:start position:0%
other things to watch for um you know if
 

00:06:43.440 --> 00:06:44.629 align:start position:0%
other things to watch for um you know if
you're<00:06:43.600><c> a</c><00:06:43.759><c> client</c><00:06:44.000><c> you've</c><00:06:44.240><c> already</c><00:06:44.479><c> heard</c>

00:06:44.629 --> 00:06:44.639 align:start position:0%
you're a client you've already heard
 

00:06:44.639 --> 00:06:45.990 align:start position:0%
you're a client you've already heard
this.<00:06:44.880><c> If</c><00:06:45.039><c> you're</c><00:06:45.120><c> not</c><00:06:45.280><c> a</c><00:06:45.440><c> client,</c><00:06:45.600><c> maybe</c><00:06:45.840><c> this</c>

00:06:45.990 --> 00:06:46.000 align:start position:0%
this. If you're not a client, maybe this
 

00:06:46.000 --> 00:06:48.469 align:start position:0%
this. If you're not a client, maybe this
is<00:06:46.160><c> something</c><00:06:46.319><c> new.</c><00:06:47.039><c> Um,</c><00:06:47.440><c> but</c><00:06:47.840><c> we'll</c><00:06:48.160><c> continue</c>

00:06:48.469 --> 00:06:48.479 align:start position:0%
is something new. Um, but we'll continue
 

00:06:48.479 --> 00:06:50.309 align:start position:0%
is something new. Um, but we'll continue
to<00:06:48.639><c> update</c><00:06:48.960><c> you</c><00:06:49.120><c> as</c><00:06:49.360><c> things</c><00:06:49.600><c> go</c><00:06:49.759><c> forward.</c>

00:06:50.309 --> 00:06:50.319 align:start position:0%
to update you as things go forward.
 

00:06:50.319 --> 00:06:54.560 align:start position:0%
to update you as things go forward.
Thank<00:06:50.560><c> you.</c>

00:06:54.560 --> 00:06:54.570 align:start position:0%
 
 

00:06:54.570 --> 00:06:57.990 align:start position:0%
 
[Music]

