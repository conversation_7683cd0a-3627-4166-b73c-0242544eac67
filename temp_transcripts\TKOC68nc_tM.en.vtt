WEBVTT
Kind: captions
Language: en

00:00:00.480 --> 00:00:02.869 align:start position:0%
 
imagine<00:00:00.880><c> a</c><00:00:00.960><c> room</c><00:00:01.199><c> full</c><00:00:01.360><c> of</c><00:00:01.599><c> 23</c><00:00:02.080><c> people</c><00:00:02.560><c> and</c><00:00:02.720><c> you</c>

00:00:02.869 --> 00:00:02.879 align:start position:0%
imagine a room full of 23 people and you
 

00:00:02.879 --> 00:00:04.950 align:start position:0%
imagine a room full of 23 people and you
asked<00:00:03.199><c> each</c><00:00:03.360><c> of</c><00:00:03.520><c> them</c><00:00:03.679><c> their</c><00:00:03.840><c> birthday</c>

00:00:04.950 --> 00:00:04.960 align:start position:0%
asked each of them their birthday
 

00:00:04.960 --> 00:00:06.950 align:start position:0%
asked each of them their birthday
what<00:00:05.120><c> do</c><00:00:05.279><c> you</c><00:00:05.359><c> think</c><00:00:05.520><c> the</c><00:00:05.680><c> odds</c><00:00:06.480><c> that</c><00:00:06.720><c> any</c><00:00:06.879><c> of</c>

00:00:06.950 --> 00:00:06.960 align:start position:0%
what do you think the odds that any of
 

00:00:06.960 --> 00:00:09.350 align:start position:0%
what do you think the odds that any of
them<00:00:07.200><c> have</c><00:00:07.440><c> the</c><00:00:07.600><c> same</c><00:00:07.839><c> birthday</c><00:00:08.320><c> are</c>

00:00:09.350 --> 00:00:09.360 align:start position:0%
them have the same birthday are
 

00:00:09.360 --> 00:00:11.270 align:start position:0%
them have the same birthday are
well<00:00:09.760><c> as</c><00:00:10.000><c> probability</c><00:00:10.480><c> would</c><00:00:10.639><c> have</c><00:00:10.880><c> it</c><00:00:11.120><c> it's</c>

00:00:11.270 --> 00:00:11.280 align:start position:0%
well as probability would have it it's
 

00:00:11.280 --> 00:00:13.990 align:start position:0%
well as probability would have it it's
surprisingly<00:00:11.920><c> around</c><00:00:12.320><c> 50</c><00:00:12.719><c> percent</c>

00:00:13.990 --> 00:00:14.000 align:start position:0%
surprisingly around 50 percent
 

00:00:14.000 --> 00:00:16.150 align:start position:0%
surprisingly around 50 percent
sounds<00:00:14.320><c> kind</c><00:00:14.480><c> of</c><00:00:14.559><c> crazy</c><00:00:14.880><c> right</c><00:00:15.759><c> well</c><00:00:15.839><c> this</c><00:00:16.080><c> is</c>

00:00:16.150 --> 00:00:16.160 align:start position:0%
sounds kind of crazy right well this is
 

00:00:16.160 --> 00:00:18.230 align:start position:0%
sounds kind of crazy right well this is
known<00:00:16.400><c> as</c><00:00:16.480><c> the</c><00:00:16.640><c> vertical</c><00:00:17.199><c> paradox</c>

00:00:18.230 --> 00:00:18.240 align:start position:0%
known as the vertical paradox
 

00:00:18.240 --> 00:00:20.230 align:start position:0%
known as the vertical paradox
something<00:00:18.560><c> that</c><00:00:18.800><c> seems</c><00:00:19.199><c> counterintuitive</c>

00:00:20.230 --> 00:00:20.240 align:start position:0%
something that seems counterintuitive
 

00:00:20.240 --> 00:00:22.230 align:start position:0%
something that seems counterintuitive
but<00:00:20.400><c> is</c><00:00:20.560><c> in</c><00:00:20.800><c> fact</c><00:00:21.119><c> true</c>

00:00:22.230 --> 00:00:22.240 align:start position:0%
but is in fact true
 

00:00:22.240 --> 00:00:24.470 align:start position:0%
but is in fact true
well<00:00:22.800><c> how</c><00:00:23.039><c> about</c><00:00:23.279><c> we</c><00:00:23.519><c> code</c><00:00:23.920><c> this</c><00:00:24.080><c> problem</c>

00:00:24.470 --> 00:00:24.480 align:start position:0%
well how about we code this problem
 

00:00:24.480 --> 00:00:28.830 align:start position:0%
well how about we code this problem
ourselves<00:00:25.039><c> and</c><00:00:25.199><c> test</c><00:00:25.439><c> it</c><00:00:25.599><c> and</c><00:00:25.680><c> see</c><00:00:25.840><c> what</c><00:00:26.000><c> we</c>

00:00:28.830 --> 00:00:28.840 align:start position:0%
 
 

00:00:28.840 --> 00:00:32.069 align:start position:0%
 
get

00:00:32.069 --> 00:00:32.079 align:start position:0%
 
 

00:00:32.079 --> 00:00:33.430 align:start position:0%
 
let's<00:00:32.320><c> first</c><00:00:32.559><c> create</c><00:00:32.719><c> a</c><00:00:32.800><c> birthday</c><00:00:33.120><c> problem</c>

00:00:33.430 --> 00:00:33.440 align:start position:0%
let's first create a birthday problem
 

00:00:33.440 --> 00:00:35.430 align:start position:0%
let's first create a birthday problem
class<00:00:34.000><c> i've</c><00:00:34.239><c> already</c><00:00:34.559><c> created</c>

00:00:35.430 --> 00:00:35.440 align:start position:0%
class i've already created
 

00:00:35.440 --> 00:00:38.869 align:start position:0%
class i've already created
the<00:00:35.600><c> project</c><00:00:36.160><c> so</c><00:00:36.480><c> let's</c><00:00:36.719><c> get</c><00:00:36.960><c> on</c><00:00:37.040><c> with</c><00:00:37.280><c> coding</c>

00:00:38.869 --> 00:00:38.879 align:start position:0%
the project so let's get on with coding
 

00:00:38.879 --> 00:00:40.470 align:start position:0%
the project so let's get on with coding
in<00:00:39.040><c> the</c><00:00:39.120><c> class</c><00:00:39.440><c> let's</c><00:00:39.680><c> instantiate</c><00:00:40.160><c> a</c><00:00:40.239><c> couple</c>

00:00:40.470 --> 00:00:40.480 align:start position:0%
in the class let's instantiate a couple
 

00:00:40.480 --> 00:00:42.069 align:start position:0%
in the class let's instantiate a couple
of<00:00:40.559><c> fields</c><00:00:40.960><c> for</c><00:00:41.120><c> random</c><00:00:41.440><c> and</c><00:00:41.600><c> the</c><00:00:41.760><c> number</c><00:00:41.920><c> of</c>

00:00:42.069 --> 00:00:42.079 align:start position:0%
of fields for random and the number of
 

00:00:42.079 --> 00:00:45.110 align:start position:0%
of fields for random and the number of
days<00:00:42.399><c> in</c><00:00:42.559><c> a</c><00:00:42.840><c> year</c><00:00:43.920><c> we</c><00:00:44.079><c> don't</c><00:00:44.239><c> need</c><00:00:44.480><c> to</c><00:00:44.719><c> use</c><00:00:44.879><c> the</c>

00:00:45.110 --> 00:00:45.120 align:start position:0%
days in a year we don't need to use the
 

00:00:45.120 --> 00:00:46.950 align:start position:0%
days in a year we don't need to use the
actual<00:00:45.520><c> days</c><00:00:45.760><c> and</c><00:00:45.920><c> months</c><00:00:46.160><c> of</c><00:00:46.239><c> the</c><00:00:46.320><c> year</c><00:00:46.719><c> which</c>

00:00:46.950 --> 00:00:46.960 align:start position:0%
actual days and months of the year which
 

00:00:46.960 --> 00:00:48.630 align:start position:0%
actual days and months of the year which
we<00:00:47.200><c> could</c><00:00:47.520><c> but</c><00:00:47.760><c> instead</c><00:00:48.000><c> we</c><00:00:48.160><c> can</c><00:00:48.239><c> just</c><00:00:48.399><c> use</c><00:00:48.559><c> the</c>

00:00:48.630 --> 00:00:48.640 align:start position:0%
we could but instead we can just use the
 

00:00:48.640 --> 00:00:49.670 align:start position:0%
we could but instead we can just use the
numbers<00:00:49.120><c> 1</c><00:00:49.360><c> through</c>

00:00:49.670 --> 00:00:49.680 align:start position:0%
numbers 1 through
 

00:00:49.680 --> 00:00:52.869 align:start position:0%
numbers 1 through
365<00:00:51.199><c> to</c><00:00:51.360><c> denote</c><00:00:51.760><c> the</c><00:00:51.840><c> days</c><00:00:52.160><c> of</c><00:00:52.239><c> the</c><00:00:52.399><c> year</c><00:00:52.719><c> to</c>

00:00:52.869 --> 00:00:52.879 align:start position:0%
365 to denote the days of the year to
 

00:00:52.879 --> 00:00:54.709 align:start position:0%
365 to denote the days of the year to
make<00:00:53.120><c> this</c><00:00:53.360><c> much</c><00:00:53.600><c> simpler</c>

00:00:54.709 --> 00:00:54.719 align:start position:0%
make this much simpler
 

00:00:54.719 --> 00:00:55.750 align:start position:0%
make this much simpler
we<00:00:54.879><c> aren't</c><00:00:55.039><c> going</c><00:00:55.120><c> to</c><00:00:55.199><c> worry</c><00:00:55.360><c> about</c><00:00:55.440><c> a</c><00:00:55.520><c> leap</c>

00:00:55.750 --> 00:00:55.760 align:start position:0%
we aren't going to worry about a leap
 

00:00:55.760 --> 00:00:56.869 align:start position:0%
we aren't going to worry about a leap
year<00:00:55.920><c> either</c><00:00:56.160><c> because</c><00:00:56.320><c> it</c><00:00:56.399><c> won't</c><00:00:56.559><c> make</c><00:00:56.719><c> much</c>

00:00:56.869 --> 00:00:56.879 align:start position:0%
year either because it won't make much
 

00:00:56.879 --> 00:00:58.549 align:start position:0%
year either because it won't make much
of<00:00:57.039><c> a</c><00:00:57.120><c> difference</c><00:00:57.920><c> at</c><00:00:58.160><c> all</c>

00:00:58.549 --> 00:00:58.559 align:start position:0%
of a difference at all
 

00:00:58.559 --> 00:01:01.750 align:start position:0%
of a difference at all
using<00:00:58.960><c> just</c><00:00:59.199><c> another</c><00:00:59.520><c> day</c><00:01:01.039><c> so</c><00:01:01.280><c> let's</c><00:01:01.520><c> create</c>

00:01:01.750 --> 00:01:01.760 align:start position:0%
using just another day so let's create
 

00:01:01.760 --> 00:01:02.869 align:start position:0%
using just another day so let's create
the<00:01:01.920><c> main</c><00:01:02.160><c> method</c>

00:01:02.869 --> 00:01:02.879 align:start position:0%
the main method
 

00:01:02.879 --> 00:01:04.710 align:start position:0%
the main method
that<00:01:03.039><c> will</c><00:01:03.199><c> take</c><00:01:03.440><c> in</c><00:01:03.600><c> two</c><00:01:03.840><c> parameters</c><00:01:04.400><c> one</c><00:01:04.559><c> for</c>

00:01:04.710 --> 00:01:04.720 align:start position:0%
that will take in two parameters one for
 

00:01:04.720 --> 00:01:06.070 align:start position:0%
that will take in two parameters one for
the<00:01:04.799><c> number</c><00:01:05.040><c> of</c><00:01:05.199><c> birthdays</c><00:01:05.760><c> or</c>

00:01:06.070 --> 00:01:06.080 align:start position:0%
the number of birthdays or
 

00:01:06.080 --> 00:01:07.990 align:start position:0%
the number of birthdays or
people<00:01:06.880><c> and</c><00:01:07.040><c> another</c><00:01:07.360><c> for</c><00:01:07.520><c> the</c><00:01:07.600><c> number</c><00:01:07.840><c> of</c>

00:01:07.990 --> 00:01:08.000 align:start position:0%
people and another for the number of
 

00:01:08.000 --> 00:01:10.310 align:start position:0%
people and another for the number of
trials<00:01:08.400><c> we</c><00:01:08.640><c> will</c><00:01:08.799><c> use</c>

00:01:10.310 --> 00:01:10.320 align:start position:0%
trials we will use
 

00:01:10.320 --> 00:01:12.149 align:start position:0%
trials we will use
so<00:01:11.040><c> we're</c><00:01:11.280><c> going</c><00:01:11.360><c> to</c><00:01:11.439><c> iterate</c><00:01:11.840><c> over</c><00:01:12.000><c> the</c>

00:01:12.149 --> 00:01:12.159 align:start position:0%
so we're going to iterate over the
 

00:01:12.159 --> 00:01:13.990 align:start position:0%
so we're going to iterate over the
number<00:01:12.320><c> of</c><00:01:12.640><c> trials</c><00:01:13.119><c> and</c><00:01:13.280><c> each</c><00:01:13.520><c> time</c>

00:01:13.990 --> 00:01:14.000 align:start position:0%
number of trials and each time
 

00:01:14.000 --> 00:01:15.510 align:start position:0%
number of trials and each time
we<00:01:14.159><c> get</c><00:01:14.320><c> a</c><00:01:14.400><c> different</c><00:01:14.720><c> list</c><00:01:14.960><c> of</c><00:01:15.119><c> randomly</c>

00:01:15.510 --> 00:01:15.520 align:start position:0%
we get a different list of randomly
 

00:01:15.520 --> 00:01:17.190 align:start position:0%
we get a different list of randomly
generated<00:01:15.840><c> birthdays</c><00:01:16.240><c> to</c><00:01:16.400><c> use</c>

00:01:17.190 --> 00:01:17.200 align:start position:0%
generated birthdays to use
 

00:01:17.200 --> 00:01:18.950 align:start position:0%
generated birthdays to use
every<00:01:17.439><c> time</c><00:01:17.680><c> there's</c><00:01:17.840><c> a</c><00:01:17.920><c> duplicate</c><00:01:18.640><c> we</c><00:01:18.799><c> want</c>

00:01:18.950 --> 00:01:18.960 align:start position:0%
every time there's a duplicate we want
 

00:01:18.960 --> 00:01:20.870 align:start position:0%
every time there's a duplicate we want
to<00:01:19.040><c> add</c><00:01:19.200><c> one</c><00:01:19.520><c> to</c><00:01:19.680><c> the</c><00:01:19.759><c> success</c><00:01:20.320><c> counter</c>

00:01:20.870 --> 00:01:20.880 align:start position:0%
to add one to the success counter
 

00:01:20.880 --> 00:01:22.630 align:start position:0%
to add one to the success counter
because<00:01:21.119><c> this</c><00:01:21.280><c> is</c><00:01:21.360><c> what</c><00:01:21.520><c> we're</c><00:01:21.759><c> checking</c><00:01:22.080><c> for</c>

00:01:22.630 --> 00:01:22.640 align:start position:0%
because this is what we're checking for
 

00:01:22.640 --> 00:01:23.910 align:start position:0%
because this is what we're checking for
i<00:01:22.799><c> know</c><00:01:22.960><c> we</c><00:01:23.040><c> haven't</c><00:01:23.280><c> created</c><00:01:23.520><c> some</c><00:01:23.680><c> of</c><00:01:23.759><c> these</c>

00:01:23.910 --> 00:01:23.920 align:start position:0%
i know we haven't created some of these
 

00:01:23.920 --> 00:01:26.469 align:start position:0%
i know we haven't created some of these
methods<00:01:24.320><c> yet</c><00:01:24.640><c> but</c><00:01:24.799><c> we're</c><00:01:25.040><c> about</c><00:01:25.360><c> to</c>

00:01:26.469 --> 00:01:26.479 align:start position:0%
methods yet but we're about to
 

00:01:26.479 --> 00:01:28.870 align:start position:0%
methods yet but we're about to
this<00:01:26.720><c> is</c><00:01:26.799><c> the</c><00:01:26.960><c> get</c><00:01:27.200><c> random</c><00:01:27.600><c> birthdays</c><00:01:28.240><c> method</c>

00:01:28.870 --> 00:01:28.880 align:start position:0%
this is the get random birthdays method
 

00:01:28.880 --> 00:01:30.469 align:start position:0%
this is the get random birthdays method
that<00:01:29.040><c> will</c><00:01:29.200><c> randomly</c><00:01:29.600><c> generate</c><00:01:29.920><c> days</c><00:01:30.240><c> of</c><00:01:30.320><c> the</c>

00:01:30.469 --> 00:01:30.479 align:start position:0%
that will randomly generate days of the
 

00:01:30.479 --> 00:01:30.870 align:start position:0%
that will randomly generate days of the
year

00:01:30.870 --> 00:01:30.880 align:start position:0%
year
 

00:01:30.880 --> 00:01:32.870 align:start position:0%
year
and<00:01:31.200><c> take</c><00:01:31.439><c> in</c><00:01:31.600><c> a</c><00:01:31.759><c> parameter</c><00:01:32.240><c> to</c><00:01:32.320><c> denote</c><00:01:32.720><c> how</c>

00:01:32.870 --> 00:01:32.880 align:start position:0%
and take in a parameter to denote how
 

00:01:32.880 --> 00:01:34.710 align:start position:0%
and take in a parameter to denote how
many<00:01:33.119><c> people</c><00:01:33.439><c> or</c><00:01:33.680><c> birthdays</c>

00:01:34.710 --> 00:01:34.720 align:start position:0%
many people or birthdays
 

00:01:34.720 --> 00:01:36.789 align:start position:0%
many people or birthdays
will<00:01:34.880><c> be</c><00:01:35.119><c> used</c><00:01:35.920><c> to</c><00:01:36.320><c> help</c><00:01:36.560><c> with</c><00:01:36.640><c> this</c>

00:01:36.789 --> 00:01:36.799 align:start position:0%
will be used to help with this
 

00:01:36.799 --> 00:01:39.350 align:start position:0%
will be used to help with this
probability<00:01:37.520><c> so</c><00:01:37.600><c> if</c><00:01:37.759><c> the</c><00:01:37.840><c> argument</c><00:01:38.240><c> is</c><00:01:38.400><c> 23</c>

00:01:39.350 --> 00:01:39.360 align:start position:0%
probability so if the argument is 23
 

00:01:39.360 --> 00:01:41.670 align:start position:0%
probability so if the argument is 23
then<00:01:39.600><c> we're</c><00:01:39.840><c> going</c><00:01:40.000><c> to</c><00:01:40.240><c> randomly</c><00:01:40.640><c> generate</c><00:01:41.119><c> 23</c>

00:01:41.670 --> 00:01:41.680 align:start position:0%
then we're going to randomly generate 23
 

00:01:41.680 --> 00:01:43.510 align:start position:0%
then we're going to randomly generate 23
days<00:01:42.000><c> of</c><00:01:42.079><c> the</c><00:01:42.240><c> year</c>

00:01:43.510 --> 00:01:43.520 align:start position:0%
days of the year
 

00:01:43.520 --> 00:01:45.590 align:start position:0%
days of the year
this<00:01:43.759><c> next</c><00:01:44.000><c> method</c><00:01:44.720><c> is</c><00:01:44.880><c> what's</c><00:01:45.280><c> actually</c>

00:01:45.590 --> 00:01:45.600 align:start position:0%
this next method is what's actually
 

00:01:45.600 --> 00:01:47.190 align:start position:0%
this next method is what's actually
going<00:01:45.759><c> to</c><00:01:45.840><c> return</c><00:01:46.320><c> true</c><00:01:46.560><c> or</c><00:01:46.720><c> false</c>

00:01:47.190 --> 00:01:47.200 align:start position:0%
going to return true or false
 

00:01:47.200 --> 00:01:49.749 align:start position:0%
going to return true or false
if<00:01:47.360><c> there</c><00:01:47.520><c> are</c><00:01:47.680><c> duplicates</c><00:01:48.240><c> or</c><00:01:48.560><c> not</c><00:01:49.360><c> in</c><00:01:49.520><c> order</c>

00:01:49.749 --> 00:01:49.759 align:start position:0%
if there are duplicates or not in order
 

00:01:49.759 --> 00:01:51.350 align:start position:0%
if there are duplicates or not in order
to<00:01:49.840><c> actually</c><00:01:50.159><c> check</c><00:01:50.399><c> for</c><00:01:50.560><c> the</c><00:01:50.640><c> duplicate</c><00:01:51.200><c> we</c>

00:01:51.350 --> 00:01:51.360 align:start position:0%
to actually check for the duplicate we
 

00:01:51.360 --> 00:01:53.270 align:start position:0%
to actually check for the duplicate we
can<00:01:51.520><c> use</c><00:01:51.759><c> a</c><00:01:51.920><c> one-line</c><00:01:52.479><c> piece</c><00:01:52.720><c> of</c><00:01:52.880><c> code</c>

00:01:53.270 --> 00:01:53.280 align:start position:0%
can use a one-line piece of code
 

00:01:53.280 --> 00:01:55.270 align:start position:0%
can use a one-line piece of code
using<00:01:53.680><c> streams</c><00:01:54.320><c> which</c><00:01:54.479><c> were</c><00:01:54.640><c> introduced</c><00:01:55.119><c> in</c>

00:01:55.270 --> 00:01:55.280 align:start position:0%
using streams which were introduced in
 

00:01:55.280 --> 00:01:56.630 align:start position:0%
using streams which were introduced in
java<00:01:55.680><c> 8.</c>

00:01:56.630 --> 00:01:56.640 align:start position:0%
java 8.
 

00:01:56.640 --> 00:01:58.709 align:start position:0%
java 8.
you<00:01:56.799><c> can</c><00:01:56.880><c> certainly</c><00:01:57.200><c> use</c><00:01:57.439><c> two</c><00:01:57.840><c> for</c><00:01:58.079><c> loops</c><00:01:58.399><c> as</c><00:01:58.560><c> a</c>

00:01:58.709 --> 00:01:58.719 align:start position:0%
you can certainly use two for loops as a
 

00:01:58.719 --> 00:01:59.910 align:start position:0%
you can certainly use two for loops as a
brute<00:01:59.040><c> force</c><00:01:59.360><c> method</c>

00:01:59.910 --> 00:01:59.920 align:start position:0%
brute force method
 

00:01:59.920 --> 00:02:01.350 align:start position:0%
brute force method
and<00:02:00.000><c> that'll</c><00:02:00.399><c> work</c><00:02:00.799><c> but</c><00:02:01.040><c> your</c><00:02:01.200><c> time</c>

00:02:01.350 --> 00:02:01.360 align:start position:0%
and that'll work but your time
 

00:02:01.360 --> 00:02:03.270 align:start position:0%
and that'll work but your time
collection<00:02:01.759><c> is</c><00:02:01.920><c> going</c><00:02:02.000><c> to</c><00:02:02.079><c> be</c><00:02:02.399><c> n</c><00:02:02.719><c> squared</c>

00:02:03.270 --> 00:02:03.280 align:start position:0%
collection is going to be n squared
 

00:02:03.280 --> 00:02:03.990 align:start position:0%
collection is going to be n squared
which

00:02:03.990 --> 00:02:04.000 align:start position:0%
which
 

00:02:04.000 --> 00:02:06.149 align:start position:0%
which
isn't<00:02:04.479><c> great</c><00:02:05.119><c> but</c><00:02:05.360><c> if</c><00:02:05.439><c> you're</c><00:02:05.520><c> just</c><00:02:05.759><c> starting</c>

00:02:06.149 --> 00:02:06.159 align:start position:0%
isn't great but if you're just starting
 

00:02:06.159 --> 00:02:07.990 align:start position:0%
isn't great but if you're just starting
out<00:02:06.880><c> with</c><00:02:07.040><c> coding</c><00:02:07.439><c> or</c><00:02:07.520><c> understanding</c>

00:02:07.990 --> 00:02:08.000 align:start position:0%
out with coding or understanding
 

00:02:08.000 --> 00:02:11.110 align:start position:0%
out with coding or understanding
loops<00:02:08.479><c> then</c><00:02:08.879><c> by</c><00:02:09.119><c> all</c><00:02:09.280><c> means</c><00:02:10.080><c> use</c><00:02:10.399><c> nested</c><00:02:10.879><c> loops</c>

00:02:11.110 --> 00:02:11.120 align:start position:0%
loops then by all means use nested loops
 

00:02:11.120 --> 00:02:11.990 align:start position:0%
loops then by all means use nested loops
so<00:02:11.200><c> that</c><00:02:11.360><c> you</c><00:02:11.520><c> can</c><00:02:11.680><c> see</c>

00:02:11.990 --> 00:02:12.000 align:start position:0%
so that you can see
 

00:02:12.000 --> 00:02:13.510 align:start position:0%
so that you can see
exactly<00:02:12.319><c> what's</c><00:02:12.560><c> going</c><00:02:12.800><c> on</c><00:02:13.040><c> it</c><00:02:13.200><c> will</c><00:02:13.280><c> make</c>

00:02:13.510 --> 00:02:13.520 align:start position:0%
exactly what's going on it will make
 

00:02:13.520 --> 00:02:15.670 align:start position:0%
exactly what's going on it will make
more<00:02:13.680><c> sense</c><00:02:13.920><c> to</c><00:02:14.080><c> you</c><00:02:14.959><c> again</c><00:02:15.280><c> we're</c><00:02:15.440><c> just</c>

00:02:15.670 --> 00:02:15.680 align:start position:0%
more sense to you again we're just
 

00:02:15.680 --> 00:02:16.390 align:start position:0%
more sense to you again we're just
testing

00:02:16.390 --> 00:02:16.400 align:start position:0%
testing
 

00:02:16.400 --> 00:02:19.110 align:start position:0%
testing
this<00:02:16.640><c> isn't</c><00:02:16.879><c> about</c><00:02:17.360><c> how</c><00:02:17.599><c> well</c><00:02:18.000><c> you</c><00:02:18.160><c> can</c><00:02:18.400><c> code</c>

00:02:19.110 --> 00:02:19.120 align:start position:0%
this isn't about how well you can code
 

00:02:19.120 --> 00:02:20.790 align:start position:0%
this isn't about how well you can code
so<00:02:19.360><c> please</c><00:02:19.520><c> don't</c><00:02:19.680><c> judge</c><00:02:19.920><c> me</c>

00:02:20.790 --> 00:02:20.800 align:start position:0%
so please don't judge me
 

00:02:20.800 --> 00:02:23.510 align:start position:0%
so please don't judge me
so<00:02:20.959><c> with</c><00:02:21.200><c> the</c><00:02:21.440><c> stream</c><00:02:22.239><c> the</c><00:02:22.560><c> all</c><00:02:22.720><c> match</c><00:02:23.040><c> method</c>

00:02:23.510 --> 00:02:23.520 align:start position:0%
so with the stream the all match method
 

00:02:23.520 --> 00:02:24.710 align:start position:0%
so with the stream the all match method
is<00:02:23.680><c> also</c><00:02:24.000><c> what's</c><00:02:24.239><c> called</c>

00:02:24.710 --> 00:02:24.720 align:start position:0%
is also what's called
 

00:02:24.720 --> 00:02:27.510 align:start position:0%
is also what's called
a<00:02:24.959><c> short</c><00:02:25.360><c> circuit</c><00:02:25.760><c> method</c><00:02:26.239><c> so</c><00:02:26.480><c> the</c><00:02:26.640><c> first</c><00:02:26.959><c> time</c>

00:02:27.510 --> 00:02:27.520 align:start position:0%
a short circuit method so the first time
 

00:02:27.520 --> 00:02:27.830 align:start position:0%
a short circuit method so the first time
it's

00:02:27.830 --> 00:02:27.840 align:start position:0%
it's
 

00:02:27.840 --> 00:02:29.910 align:start position:0%
it's
this<00:02:28.160><c> tries</c><00:02:28.400><c> to</c><00:02:28.480><c> find</c><00:02:28.720><c> a</c><00:02:28.879><c> match</c><00:02:29.520><c> and</c><00:02:29.680><c> it's</c>

00:02:29.910 --> 00:02:29.920 align:start position:0%
this tries to find a match and it's
 

00:02:29.920 --> 00:02:31.670 align:start position:0%
this tries to find a match and it's
successful<00:02:30.720><c> it</c><00:02:30.800><c> will</c><00:02:30.959><c> stop</c><00:02:31.280><c> iterating</c>

00:02:31.670 --> 00:02:31.680 align:start position:0%
successful it will stop iterating
 

00:02:31.680 --> 00:02:33.190 align:start position:0%
successful it will stop iterating
through<00:02:31.840><c> the</c><00:02:32.000><c> rest</c><00:02:32.319><c> of</c><00:02:32.400><c> the</c><00:02:32.560><c> list</c>

00:02:33.190 --> 00:02:33.200 align:start position:0%
through the rest of the list
 

00:02:33.200 --> 00:02:34.790 align:start position:0%
through the rest of the list
some<00:02:33.360><c> other</c><00:02:33.599><c> methods</c><00:02:34.080><c> that</c><00:02:34.319><c> use</c><00:02:34.560><c> with</c><00:02:34.720><c> the</c>

00:02:34.790 --> 00:02:34.800 align:start position:0%
some other methods that use with the
 

00:02:34.800 --> 00:02:36.630 align:start position:0%
some other methods that use with the
stream<00:02:35.200><c> such</c><00:02:35.440><c> as</c><00:02:35.680><c> distinct</c>

00:02:36.630 --> 00:02:36.640 align:start position:0%
stream such as distinct
 

00:02:36.640 --> 00:02:39.190 align:start position:0%
stream such as distinct
and<00:02:36.879><c> count</c><00:02:37.440><c> will</c><00:02:37.599><c> go</c><00:02:37.840><c> through</c><00:02:38.080><c> the</c><00:02:38.160><c> whole</c><00:02:38.560><c> list</c>

00:02:39.190 --> 00:02:39.200 align:start position:0%
and count will go through the whole list
 

00:02:39.200 --> 00:02:39.990 align:start position:0%
and count will go through the whole list
which<00:02:39.440><c> is</c>

00:02:39.990 --> 00:02:40.000 align:start position:0%
which is
 

00:02:40.000 --> 00:02:42.070 align:start position:0%
which is
fine<00:02:40.800><c> but</c><00:02:41.040><c> we</c><00:02:41.200><c> can</c><00:02:41.360><c> make</c><00:02:41.519><c> this</c><00:02:41.680><c> a</c><00:02:41.680><c> little</c><00:02:41.840><c> bit</c>

00:02:42.070 --> 00:02:42.080 align:start position:0%
fine but we can make this a little bit
 

00:02:42.080 --> 00:02:43.910 align:start position:0%
fine but we can make this a little bit
better<00:02:42.720><c> that</c><00:02:42.959><c> the</c><00:02:43.040><c> first</c><00:02:43.280><c> time</c><00:02:43.519><c> it</c><00:02:43.599><c> finds</c><00:02:43.840><c> a</c>

00:02:43.910 --> 00:02:43.920 align:start position:0%
better that the first time it finds a
 

00:02:43.920 --> 00:02:44.309 align:start position:0%
better that the first time it finds a
match

00:02:44.309 --> 00:02:44.319 align:start position:0%
match
 

00:02:44.319 --> 00:02:46.229 align:start position:0%
match
it<00:02:44.480><c> stops</c><00:02:44.800><c> entering</c><00:02:45.040><c> through</c><00:02:45.200><c> the</c><00:02:45.360><c> list</c><00:02:46.000><c> so</c>

00:02:46.229 --> 00:02:46.239 align:start position:0%
it stops entering through the list so
 

00:02:46.239 --> 00:02:48.150 align:start position:0%
it stops entering through the list so
what<00:02:46.480><c> this</c><00:02:46.640><c> piece</c><00:02:46.879><c> of</c><00:02:46.959><c> code</c><00:02:47.200><c> is</c><00:02:47.280><c> saying</c><00:02:47.599><c> is</c>

00:02:48.150 --> 00:02:48.160 align:start position:0%
what this piece of code is saying is
 

00:02:48.160 --> 00:02:51.030 align:start position:0%
what this piece of code is saying is
if<00:02:48.400><c> it</c><00:02:48.560><c> can't</c><00:02:49.120><c> find</c><00:02:49.760><c> or</c><00:02:50.000><c> if</c><00:02:50.160><c> it</c><00:02:50.239><c> can't</c><00:02:50.560><c> add</c><00:02:50.800><c> an</c>

00:02:51.030 --> 00:02:51.040 align:start position:0%
if it can't find or if it can't add an
 

00:02:51.040 --> 00:02:51.990 align:start position:0%
if it can't find or if it can't add an
element

00:02:51.990 --> 00:02:52.000 align:start position:0%
element
 

00:02:52.000 --> 00:02:54.229 align:start position:0%
element
from<00:02:52.239><c> list</c><00:02:52.480><c> of</c><00:02:52.560><c> birthdays</c><00:02:53.120><c> to</c><00:02:53.360><c> the</c><00:02:53.519><c> hash</c><00:02:53.840><c> set</c>

00:02:54.229 --> 00:02:54.239 align:start position:0%
from list of birthdays to the hash set
 

00:02:54.239 --> 00:02:55.350 align:start position:0%
from list of birthdays to the hash set
then<00:02:54.400><c> return</c><00:02:54.800><c> true</c>

00:02:55.350 --> 00:02:55.360 align:start position:0%
then return true
 

00:02:55.360 --> 00:02:57.830 align:start position:0%
then return true
because<00:02:55.599><c> remember</c><00:02:56.160><c> hashset</c><00:02:57.040><c> cannot</c><00:02:57.519><c> have</c>

00:02:57.830 --> 00:02:57.840 align:start position:0%
because remember hashset cannot have
 

00:02:57.840 --> 00:02:58.710 align:start position:0%
because remember hashset cannot have
duplicates

00:02:58.710 --> 00:02:58.720 align:start position:0%
duplicates
 

00:02:58.720 --> 00:03:01.190 align:start position:0%
duplicates
so<00:02:58.959><c> if</c><00:02:59.120><c> it</c><00:02:59.200><c> gets</c><00:02:59.519><c> to</c><00:02:59.920><c> a</c><00:03:00.159><c> number</c><00:03:00.640><c> that</c><00:03:00.879><c> already</c>

00:03:01.190 --> 00:03:01.200 align:start position:0%
so if it gets to a number that already
 

00:03:01.200 --> 00:03:02.630 align:start position:0%
so if it gets to a number that already
exists<00:03:01.440><c> and</c><00:03:01.599><c> tries</c><00:03:01.840><c> to</c><00:03:02.000><c> add</c><00:03:02.159><c> it</c>

00:03:02.630 --> 00:03:02.640 align:start position:0%
exists and tries to add it
 

00:03:02.640 --> 00:03:04.949 align:start position:0%
exists and tries to add it
it<00:03:02.800><c> won't</c><00:03:03.040><c> be</c><00:03:03.200><c> able</c><00:03:03.360><c> to</c><00:03:03.920><c> and</c><00:03:04.080><c> so</c><00:03:04.400><c> then</c><00:03:04.800><c> it's</c>

00:03:04.949 --> 00:03:04.959 align:start position:0%
it won't be able to and so then it's
 

00:03:04.959 --> 00:03:07.110 align:start position:0%
it won't be able to and so then it's
going<00:03:05.120><c> to</c><00:03:05.280><c> return</c><00:03:05.760><c> true</c>

00:03:07.110 --> 00:03:07.120 align:start position:0%
going to return true
 

00:03:07.120 --> 00:03:09.030 align:start position:0%
going to return true
it<00:03:07.360><c> would</c><00:03:07.599><c> have</c><00:03:07.760><c> returned</c><00:03:08.319><c> false</c><00:03:08.720><c> if</c><00:03:08.879><c> we</c>

00:03:09.030 --> 00:03:09.040 align:start position:0%
it would have returned false if we
 

00:03:09.040 --> 00:03:10.390 align:start position:0%
it would have returned false if we
didn't<00:03:09.200><c> put</c><00:03:09.360><c> the</c><00:03:09.440><c> exclamation</c><00:03:09.920><c> in</c><00:03:10.080><c> front</c>

00:03:10.390 --> 00:03:10.400 align:start position:0%
didn't put the exclamation in front
 

00:03:10.400 --> 00:03:13.110 align:start position:0%
didn't put the exclamation in front
switching<00:03:10.879><c> the</c><00:03:11.040><c> boolean</c><00:03:11.440><c> expression</c><00:03:12.879><c> this</c>

00:03:13.110 --> 00:03:13.120 align:start position:0%
switching the boolean expression this
 

00:03:13.120 --> 00:03:15.030 align:start position:0%
switching the boolean expression this
last<00:03:13.360><c> method</c><00:03:13.760><c> is</c><00:03:13.840><c> a</c><00:03:13.920><c> simple</c><00:03:14.239><c> print</c><00:03:14.560><c> statement</c>

00:03:15.030 --> 00:03:15.040 align:start position:0%
last method is a simple print statement
 

00:03:15.040 --> 00:03:16.470 align:start position:0%
last method is a simple print statement
of<00:03:15.200><c> the</c><00:03:15.360><c> probability</c>

00:03:16.470 --> 00:03:16.480 align:start position:0%
of the probability
 

00:03:16.480 --> 00:03:19.270 align:start position:0%
of the probability
now<00:03:16.720><c> let's</c><00:03:16.959><c> test</c><00:03:17.280><c> it</c><00:03:18.480><c> so</c><00:03:18.720><c> let's</c><00:03:18.879><c> start</c><00:03:19.120><c> out</c>

00:03:19.270 --> 00:03:19.280 align:start position:0%
now let's test it so let's start out
 

00:03:19.280 --> 00:03:19.589 align:start position:0%
now let's test it so let's start out
with

00:03:19.589 --> 00:03:19.599 align:start position:0%
with
 

00:03:19.599 --> 00:03:21.910 align:start position:0%
with
10<00:03:20.239><c> 10</c><00:03:20.560><c> this</c><00:03:20.800><c> is</c><00:03:20.879><c> number</c><00:03:21.200><c> birthday</c><00:03:21.519><c> so</c><00:03:21.680><c> we</c><00:03:21.760><c> have</c>

00:03:21.910 --> 00:03:21.920 align:start position:0%
10 10 this is number birthday so we have
 

00:03:21.920 --> 00:03:22.869 align:start position:0%
10 10 this is number birthday so we have
10

00:03:22.869 --> 00:03:22.879 align:start position:0%
10
 

00:03:22.879 --> 00:03:24.789 align:start position:0%
10
birthdays<00:03:23.280><c> that</c><00:03:23.440><c> are</c><00:03:23.519><c> randomly</c><00:03:24.000><c> generated</c>

00:03:24.789 --> 00:03:24.799 align:start position:0%
birthdays that are randomly generated
 

00:03:24.799 --> 00:03:25.990 align:start position:0%
birthdays that are randomly generated
and<00:03:24.959><c> as</c><00:03:25.120><c> you</c><00:03:25.200><c> can</c><00:03:25.360><c> see</c><00:03:25.599><c> here</c>

00:03:25.990 --> 00:03:26.000 align:start position:0%
and as you can see here
 

00:03:26.000 --> 00:03:29.670 align:start position:0%
and as you can see here
most<00:03:26.239><c> of</c><00:03:26.400><c> these</c><00:03:26.799><c> are</c><00:03:27.440><c> around</c><00:03:28.480><c> upper</c>

00:03:29.670 --> 00:03:29.680 align:start position:0%
most of these are around upper
 

00:03:29.680 --> 00:03:33.270 align:start position:0%
most of these are around upper
upper<00:03:29.920><c> 11</c><00:03:30.239><c> percent</c><00:03:30.720><c> where</c><00:03:30.879><c> i</c><00:03:30.959><c> one</c><00:03:31.120><c> at</c><00:03:31.200><c> 12.41</c>

00:03:33.270 --> 00:03:33.280 align:start position:0%
upper 11 percent where i one at 12.41
 

00:03:33.280 --> 00:03:36.229 align:start position:0%
upper 11 percent where i one at 12.41
so<00:03:33.440><c> this</c><00:03:33.680><c> is</c><00:03:33.760><c> pretty</c><00:03:34.080><c> close</c><00:03:34.799><c> to</c><00:03:35.760><c> what</c><00:03:36.080><c> it</c>

00:03:36.229 --> 00:03:36.239 align:start position:0%
so this is pretty close to what it
 

00:03:36.239 --> 00:03:38.390 align:start position:0%
so this is pretty close to what it
should<00:03:36.480><c> be</c><00:03:36.640><c> which</c><00:03:36.799><c> is</c><00:03:36.879><c> 11.7</c><00:03:37.680><c> percent</c>

00:03:38.390 --> 00:03:38.400 align:start position:0%
should be which is 11.7 percent
 

00:03:38.400 --> 00:03:40.390 align:start position:0%
should be which is 11.7 percent
and<00:03:38.560><c> we</c><00:03:38.720><c> can</c><00:03:38.879><c> do</c><00:03:39.200><c> it</c><00:03:39.440><c> again</c><00:03:39.920><c> and</c><00:03:40.080><c> as</c><00:03:40.239><c> you</c><00:03:40.319><c> can</c>

00:03:40.390 --> 00:03:40.400 align:start position:0%
and we can do it again and as you can
 

00:03:40.400 --> 00:03:42.229 align:start position:0%
and we can do it again and as you can
see<00:03:40.560><c> most</c><00:03:40.799><c> of</c><00:03:40.879><c> these</c><00:03:41.120><c> are</c><00:03:41.360><c> hovering</c>

00:03:42.229 --> 00:03:42.239 align:start position:0%
see most of these are hovering
 

00:03:42.239 --> 00:03:45.350 align:start position:0%
see most of these are hovering
right<00:03:42.480><c> around</c><00:03:43.280><c> the</c><00:03:43.519><c> mid</c><00:03:43.760><c> to</c><00:03:43.920><c> upper</c><00:03:44.159><c> 11</c><00:03:45.120><c> now</c>

00:03:45.350 --> 00:03:45.360 align:start position:0%
right around the mid to upper 11 now
 

00:03:45.360 --> 00:03:46.789 align:start position:0%
right around the mid to upper 11 now
with<00:03:45.599><c> 20</c>

00:03:46.789 --> 00:03:46.799 align:start position:0%
with 20
 

00:03:46.799 --> 00:03:49.830 align:start position:0%
with 20
uh<00:03:47.040><c> we're</c><00:03:47.200><c> supposed</c><00:03:47.440><c> to</c><00:03:47.599><c> get</c><00:03:47.840><c> uh</c><00:03:48.080><c> 41.1</c><00:03:49.519><c> and</c><00:03:49.680><c> as</c>

00:03:49.830 --> 00:03:49.840 align:start position:0%
uh we're supposed to get uh 41.1 and as
 

00:03:49.840 --> 00:03:50.710 align:start position:0%
uh we're supposed to get uh 41.1 and as
you<00:03:49.920><c> can</c><00:03:50.000><c> see</c><00:03:50.239><c> here</c>

00:03:50.710 --> 00:03:50.720 align:start position:0%
you can see here
 

00:03:50.720 --> 00:03:53.990 align:start position:0%
you can see here
we're<00:03:51.040><c> pretty</c><00:03:51.440><c> close</c><00:03:52.159><c> uh</c><00:03:52.640><c> here</c><00:03:52.879><c> i'm</c><00:03:53.120><c> doing</c><00:03:53.519><c> 20</c>

00:03:53.990 --> 00:03:54.000 align:start position:0%
we're pretty close uh here i'm doing 20
 

00:03:54.000 --> 00:03:57.030 align:start position:0%
we're pretty close uh here i'm doing 20
000<00:03:54.640><c> trials</c><00:03:55.120><c> just</c><00:03:55.360><c> to</c><00:03:55.920><c> get</c><00:03:56.159><c> an</c><00:03:56.319><c> even</c><00:03:56.560><c> more</c>

00:03:57.030 --> 00:03:57.040 align:start position:0%
000 trials just to get an even more
 

00:03:57.040 --> 00:03:58.550 align:start position:0%
000 trials just to get an even more
accurate<00:03:57.519><c> representation</c><00:03:58.319><c> of</c><00:03:58.400><c> the</c>

00:03:58.550 --> 00:03:58.560 align:start position:0%
accurate representation of the
 

00:03:58.560 --> 00:04:00.470 align:start position:0%
accurate representation of the
probability<00:03:59.200><c> with</c><00:03:59.439><c> our</c><00:03:59.680><c> tests</c>

00:04:00.470 --> 00:04:00.480 align:start position:0%
probability with our tests
 

00:04:00.480 --> 00:04:03.270 align:start position:0%
probability with our tests
so<00:04:00.799><c> this</c><00:04:00.959><c> seems</c><00:04:01.680><c> to</c><00:04:01.840><c> be</c><00:04:02.080><c> pretty</c><00:04:02.239><c> good</c><00:04:02.799><c> now</c><00:04:03.120><c> for</c>

00:04:03.270 --> 00:04:03.280 align:start position:0%
so this seems to be pretty good now for
 

00:04:03.280 --> 00:04:05.110 align:start position:0%
so this seems to be pretty good now for
the<00:04:03.439><c> original</c><00:04:04.080><c> question</c><00:04:04.400><c> that</c><00:04:04.560><c> asked</c><00:04:04.799><c> in</c><00:04:04.959><c> the</c>

00:04:05.110 --> 00:04:05.120 align:start position:0%
the original question that asked in the
 

00:04:05.120 --> 00:04:05.589 align:start position:0%
the original question that asked in the
very

00:04:05.589 --> 00:04:05.599 align:start position:0%
very
 

00:04:05.599 --> 00:04:09.589 align:start position:0%
very
beginning<00:04:06.400><c> here's</c><00:04:06.959><c> 23.</c><00:04:08.000><c> when</c><00:04:08.239><c> we</c><00:04:08.400><c> run</c><00:04:08.640><c> this</c>

00:04:09.589 --> 00:04:09.599 align:start position:0%
beginning here's 23. when we run this
 

00:04:09.599 --> 00:04:13.030 align:start position:0%
beginning here's 23. when we run this
we<00:04:09.840><c> do</c><00:04:10.080><c> get</c><00:04:10.480><c> just</c><00:04:10.959><c> over</c><00:04:11.360><c> 50</c><00:04:12.480><c> the</c><00:04:12.720><c> actual</c>

00:04:13.030 --> 00:04:13.040 align:start position:0%
we do get just over 50 the actual
 

00:04:13.040 --> 00:04:13.990 align:start position:0%
we do get just over 50 the actual
probability

00:04:13.990 --> 00:04:14.000 align:start position:0%
probability
 

00:04:14.000 --> 00:04:16.390 align:start position:0%
probability
calls<00:04:14.400><c> for</c><00:04:14.560><c> fifty</c><00:04:14.879><c> point</c><00:04:15.280><c> fifty</c><00:04:15.680><c> point</c><00:04:16.079><c> seven</c>

00:04:16.390 --> 00:04:16.400 align:start position:0%
calls for fifty point fifty point seven
 

00:04:16.400 --> 00:04:17.110 align:start position:0%
calls for fifty point fifty point seven
percent

00:04:17.110 --> 00:04:17.120 align:start position:0%
percent
 

00:04:17.120 --> 00:04:18.550 align:start position:0%
percent
and<00:04:17.280><c> again</c><00:04:17.600><c> i</c><00:04:17.680><c> said</c><00:04:17.840><c> it</c><00:04:17.919><c> didn't</c><00:04:18.160><c> matter</c><00:04:18.400><c> about</c>

00:04:18.550 --> 00:04:18.560 align:start position:0%
and again i said it didn't matter about
 

00:04:18.560 --> 00:04:20.469 align:start position:0%
and again i said it didn't matter about
the<00:04:18.639><c> leap</c><00:04:18.880><c> year</c><00:04:19.120><c> if</c><00:04:19.280><c> we</c><00:04:19.359><c> added</c><00:04:19.680><c> a</c><00:04:19.759><c> leap</c><00:04:19.919><c> year</c>

00:04:20.469 --> 00:04:20.479 align:start position:0%
the leap year if we added a leap year
 

00:04:20.479 --> 00:04:22.310 align:start position:0%
the leap year if we added a leap year
then<00:04:20.639><c> the</c><00:04:20.799><c> probability</c><00:04:21.440><c> expected</c><00:04:21.919><c> is</c><00:04:22.000><c> fifty</c>

00:04:22.310 --> 00:04:22.320 align:start position:0%
then the probability expected is fifty
 

00:04:22.320 --> 00:04:23.670 align:start position:0%
then the probability expected is fifty
point<00:04:22.560><c> six</c><00:04:22.800><c> percent</c><00:04:23.120><c> so</c><00:04:23.280><c> it's</c>

00:04:23.670 --> 00:04:23.680 align:start position:0%
point six percent so it's
 

00:04:23.680 --> 00:04:26.710 align:start position:0%
point six percent so it's
negligible<00:04:24.479><c> okay</c><00:04:25.360><c> so</c><00:04:25.520><c> this</c><00:04:25.680><c> is</c><00:04:25.840><c> actually</c>

00:04:26.710 --> 00:04:26.720 align:start position:0%
negligible okay so this is actually
 

00:04:26.720 --> 00:04:29.110 align:start position:0%
negligible okay so this is actually
a<00:04:26.880><c> good</c><00:04:27.120><c> probability</c><00:04:28.160><c> so</c><00:04:28.400><c> it</c><00:04:28.560><c> seems</c><00:04:28.880><c> that</c><00:04:28.960><c> our</c>

00:04:29.110 --> 00:04:29.120 align:start position:0%
a good probability so it seems that our
 

00:04:29.120 --> 00:04:31.189 align:start position:0%
a good probability so it seems that our
test<00:04:29.440><c> is</c><00:04:29.680><c> accurately</c><00:04:30.160><c> representing</c>

00:04:31.189 --> 00:04:31.199 align:start position:0%
test is accurately representing
 

00:04:31.199 --> 00:04:35.030 align:start position:0%
test is accurately representing
the<00:04:31.440><c> probability</c><00:04:32.240><c> of</c><00:04:33.120><c> 23</c><00:04:33.680><c> people</c><00:04:33.919><c> in</c><00:04:34.080><c> a</c><00:04:34.160><c> room</c>

00:04:35.030 --> 00:04:35.040 align:start position:0%
the probability of 23 people in a room
 

00:04:35.040 --> 00:04:37.590 align:start position:0%
the probability of 23 people in a room
just<00:04:35.360><c> over</c><00:04:35.680><c> 50</c><00:04:36.000><c> percent</c><00:04:36.560><c> or</c><00:04:36.720><c> there's</c><00:04:36.960><c> a</c><00:04:37.040><c> 50</c>

00:04:37.590 --> 00:04:37.600 align:start position:0%
just over 50 percent or there's a 50
 

00:04:37.600 --> 00:04:38.710 align:start position:0%
just over 50 percent or there's a 50
probability<00:04:38.240><c> that</c>

00:04:38.710 --> 00:04:38.720 align:start position:0%
probability that
 

00:04:38.720 --> 00:04:40.629 align:start position:0%
probability that
at<00:04:38.880><c> least</c><00:04:39.120><c> two</c><00:04:39.360><c> people</c><00:04:39.680><c> are</c><00:04:39.840><c> going</c><00:04:40.160><c> to</c><00:04:40.320><c> have</c>

00:04:40.629 --> 00:04:40.639 align:start position:0%
at least two people are going to have
 

00:04:40.639 --> 00:04:42.390 align:start position:0%
at least two people are going to have
the<00:04:40.800><c> same</c><00:04:41.120><c> birthday</c><00:04:41.919><c> here</c>

00:04:42.390 --> 00:04:42.400 align:start position:0%
the same birthday here
 

00:04:42.400 --> 00:04:44.710 align:start position:0%
the same birthday here
i'm<00:04:42.560><c> going</c><00:04:42.639><c> to</c><00:04:42.800><c> have</c><00:04:43.040><c> 30</c><00:04:43.440><c> different</c><00:04:43.680><c> birthdays</c>

00:04:44.710 --> 00:04:44.720 align:start position:0%
i'm going to have 30 different birthdays
 

00:04:44.720 --> 00:04:46.150 align:start position:0%
i'm going to have 30 different birthdays
and<00:04:44.800><c> again</c><00:04:45.040><c> i'm</c><00:04:45.199><c> going</c><00:04:45.199><c> to</c><00:04:45.280><c> be</c><00:04:45.360><c> using</c><00:04:45.600><c> the</c><00:04:45.759><c> 20</c>

00:04:46.150 --> 00:04:46.160 align:start position:0%
and again i'm going to be using the 20
 

00:04:46.160 --> 00:04:48.310 align:start position:0%
and again i'm going to be using the 20
000<00:04:46.960><c> trial</c><00:04:47.440><c> just</c><00:04:47.600><c> to</c><00:04:47.680><c> get</c><00:04:47.840><c> a</c><00:04:48.000><c> better</c>

00:04:48.310 --> 00:04:48.320 align:start position:0%
000 trial just to get a better
 

00:04:48.320 --> 00:04:49.590 align:start position:0%
000 trial just to get a better
representation

00:04:49.590 --> 00:04:49.600 align:start position:0%
representation
 

00:04:49.600 --> 00:04:52.110 align:start position:0%
representation
these<00:04:49.840><c> are</c><00:04:50.080><c> about</c><00:04:50.320><c> between</c><00:04:50.800><c> seven</c><00:04:51.040><c> we</c><00:04:51.199><c> have</c>

00:04:52.110 --> 00:04:52.120 align:start position:0%
these are about between seven we have
 

00:04:52.120 --> 00:04:54.830 align:start position:0%
these are about between seven we have
70.195<00:04:53.199><c> is</c><00:04:53.360><c> the</c><00:04:53.440><c> lowest</c><00:04:53.759><c> and</c><00:04:53.840><c> then</c>

00:04:54.830 --> 00:04:54.840 align:start position:0%
70.195 is the lowest and then
 

00:04:54.840 --> 00:04:57.510 align:start position:0%
70.195 is the lowest and then
70.67<00:04:55.919><c> is</c><00:04:56.000><c> the</c><00:04:56.160><c> highest</c><00:04:56.560><c> and</c><00:04:56.639><c> we're</c><00:04:56.800><c> expecting</c>

00:04:57.510 --> 00:04:57.520 align:start position:0%
70.67 is the highest and we're expecting
 

00:04:57.520 --> 00:05:00.950 align:start position:0%
70.67 is the highest and we're expecting
70.6<00:04:58.240><c> percent</c><00:04:59.600><c> now</c><00:04:59.919><c> if</c><00:05:00.080><c> we</c><00:05:00.240><c> just</c><00:05:00.479><c> add</c>

00:05:00.950 --> 00:05:00.960 align:start position:0%
70.6 percent now if we just add
 

00:05:00.960 --> 00:05:04.710 align:start position:0%
70.6 percent now if we just add
10<00:05:01.280><c> more</c><00:05:01.680><c> birthdays</c><00:05:02.960><c> we're</c><00:05:03.520><c> expected</c>

00:05:04.710 --> 00:05:04.720 align:start position:0%
10 more birthdays we're expected
 

00:05:04.720 --> 00:05:06.590 align:start position:0%
10 more birthdays we're expected
through<00:05:05.039><c> probability</c><00:05:05.600><c> equations</c><00:05:06.240><c> to</c><00:05:06.400><c> get</c>

00:05:06.590 --> 00:05:06.600 align:start position:0%
through probability equations to get
 

00:05:06.600 --> 00:05:08.870 align:start position:0%
through probability equations to get
89.1<00:05:07.600><c> percent</c>

00:05:08.870 --> 00:05:08.880 align:start position:0%
89.1 percent
 

00:05:08.880 --> 00:05:11.029 align:start position:0%
89.1 percent
and<00:05:08.960><c> as</c><00:05:09.120><c> you</c><00:05:09.280><c> can</c><00:05:09.440><c> see</c><00:05:09.680><c> here</c><00:05:10.240><c> when</c><00:05:10.400><c> we</c><00:05:10.560><c> execute</c>

00:05:11.029 --> 00:05:11.039 align:start position:0%
and as you can see here when we execute
 

00:05:11.039 --> 00:05:12.070 align:start position:0%
and as you can see here when we execute
our<00:05:11.280><c> code</c>

00:05:12.070 --> 00:05:12.080 align:start position:0%
our code
 

00:05:12.080 --> 00:05:14.710 align:start position:0%
our code
with<00:05:12.320><c> 20</c><00:05:12.639><c> 000</c><00:05:12.960><c> trials</c><00:05:13.919><c> we</c><00:05:14.160><c> are</c><00:05:14.320><c> getting</c>

00:05:14.710 --> 00:05:14.720 align:start position:0%
with 20 000 trials we are getting
 

00:05:14.720 --> 00:05:15.270 align:start position:0%
with 20 000 trials we are getting
between

00:05:15.270 --> 00:05:15.280 align:start position:0%
between
 

00:05:15.280 --> 00:05:19.590 align:start position:0%
between
upper<00:05:15.680><c> 88</c><00:05:16.560><c> percent</c><00:05:17.120><c> to</c><00:05:17.840><c> lower</c><00:05:18.240><c> 89</c>

00:05:19.590 --> 00:05:19.600 align:start position:0%
upper 88 percent to lower 89
 

00:05:19.600 --> 00:05:21.270 align:start position:0%
upper 88 percent to lower 89
and<00:05:19.759><c> that's</c><00:05:20.400><c> a</c><00:05:20.560><c> pretty</c><00:05:20.880><c> accurate</c>

00:05:21.270 --> 00:05:21.280 align:start position:0%
and that's a pretty accurate
 

00:05:21.280 --> 00:05:23.029 align:start position:0%
and that's a pretty accurate
representation<00:05:22.080><c> so</c><00:05:22.320><c> we're</c><00:05:22.560><c> getting</c>

00:05:23.029 --> 00:05:23.039 align:start position:0%
representation so we're getting
 

00:05:23.039 --> 00:05:26.390 align:start position:0%
representation so we're getting
what<00:05:23.199><c> we</c><00:05:23.360><c> are</c><00:05:23.600><c> expecting</c><00:05:25.520><c> and</c><00:05:25.759><c> so</c><00:05:25.919><c> let's</c><00:05:26.160><c> add</c>

00:05:26.390 --> 00:05:26.400 align:start position:0%
what we are expecting and so let's add
 

00:05:26.400 --> 00:05:27.749 align:start position:0%
what we are expecting and so let's add
just<00:05:26.720><c> 10</c><00:05:26.960><c> more</c><00:05:27.280><c> people</c>

00:05:27.749 --> 00:05:27.759 align:start position:0%
just 10 more people
 

00:05:27.759 --> 00:05:30.870 align:start position:0%
just 10 more people
now<00:05:28.000><c> we</c><00:05:28.160><c> have</c><00:05:28.479><c> 50.</c><00:05:29.280><c> and</c><00:05:29.520><c> as</c><00:05:29.680><c> you</c><00:05:29.759><c> can</c><00:05:30.000><c> see</c><00:05:30.240><c> here</c>

00:05:30.870 --> 00:05:30.880 align:start position:0%
now we have 50. and as you can see here
 

00:05:30.880 --> 00:05:33.670 align:start position:0%
now we have 50. and as you can see here
we're<00:05:31.199><c> expecting</c><00:05:31.840><c> 97</c><00:05:32.800><c> and</c><00:05:33.039><c> we</c><00:05:33.199><c> are</c><00:05:33.360><c> getting</c>

00:05:33.670 --> 00:05:33.680 align:start position:0%
we're expecting 97 and we are getting
 

00:05:33.680 --> 00:05:35.670 align:start position:0%
we're expecting 97 and we are getting
just<00:05:33.919><c> about</c><00:05:34.240><c> 97</c>

00:05:35.670 --> 00:05:35.680 align:start position:0%
just about 97
 

00:05:35.680 --> 00:05:37.749 align:start position:0%
just about 97
and<00:05:36.080><c> finally</c><00:05:36.479><c> we're</c><00:05:36.639><c> going</c><00:05:36.720><c> to</c><00:05:36.880><c> do</c><00:05:37.120><c> 70.</c><00:05:37.520><c> we're</c>

00:05:37.749 --> 00:05:37.759 align:start position:0%
and finally we're going to do 70. we're
 

00:05:37.759 --> 00:05:41.029 align:start position:0%
and finally we're going to do 70. we're
expected<00:05:38.240><c> to</c><00:05:38.400><c> get</c><00:05:39.479><c> 99.9</c>

00:05:41.029 --> 00:05:41.039 align:start position:0%
expected to get 99.9
 

00:05:41.039 --> 00:05:44.230 align:start position:0%
expected to get 99.9
of<00:05:41.120><c> the</c><00:05:41.440><c> time</c><00:05:42.080><c> we're</c><00:05:42.240><c> gonna</c><00:05:42.560><c> have</c><00:05:42.960><c> at</c><00:05:43.199><c> least</c>

00:05:44.230 --> 00:05:44.240 align:start position:0%
of the time we're gonna have at least
 

00:05:44.240 --> 00:05:47.790 align:start position:0%
of the time we're gonna have at least
two<00:05:44.800><c> matching</c><00:05:45.360><c> birthdays</c><00:05:46.400><c> we</c><00:05:46.639><c> are</c><00:05:46.880><c> getting</c>

00:05:47.790 --> 00:05:47.800 align:start position:0%
two matching birthdays we are getting
 

00:05:47.800 --> 00:05:51.350 align:start position:0%
two matching birthdays we are getting
99.9<00:05:49.039><c> some</c><00:05:49.440><c> percent</c><00:05:50.240><c> it's</c><00:05:50.479><c> crazy</c>

00:05:51.350 --> 00:05:51.360 align:start position:0%
99.9 some percent it's crazy
 

00:05:51.360 --> 00:05:53.909 align:start position:0%
99.9 some percent it's crazy
that<00:05:51.520><c> going</c><00:05:51.919><c> from</c><00:05:52.479><c> uh</c><00:05:52.880><c> 23</c><00:05:53.360><c> people</c><00:05:53.680><c> which</c><00:05:53.840><c> is</c>

00:05:53.909 --> 00:05:53.919 align:start position:0%
that going from uh 23 people which is
 

00:05:53.919 --> 00:05:55.029 align:start position:0%
that going from uh 23 people which is
about<00:05:54.080><c> 50</c>

00:05:55.029 --> 00:05:55.039 align:start position:0%
about 50
 

00:05:55.039 --> 00:05:57.270 align:start position:0%
about 50
just<00:05:55.199><c> adding</c><00:05:55.600><c> seven</c><00:05:55.840><c> more</c><00:05:56.080><c> people</c><00:05:56.800><c> goes</c><00:05:57.039><c> up</c><00:05:57.120><c> to</c>

00:05:57.270 --> 00:05:57.280 align:start position:0%
just adding seven more people goes up to
 

00:05:57.280 --> 00:05:58.150 align:start position:0%
just adding seven more people goes up to
70

00:05:58.150 --> 00:05:58.160 align:start position:0%
70
 

00:05:58.160 --> 00:05:59.909 align:start position:0%
70
and<00:05:58.319><c> then</c><00:05:58.479><c> adding</c><00:05:58.800><c> 10</c><00:05:59.039><c> more</c><00:05:59.199><c> people</c><00:05:59.600><c> it</c><00:05:59.680><c> goes</c>

00:05:59.909 --> 00:05:59.919 align:start position:0%
and then adding 10 more people it goes
 

00:05:59.919 --> 00:06:01.830 align:start position:0%
and then adding 10 more people it goes
up<00:06:00.000><c> to</c><00:06:00.160><c> 89</c>

00:06:01.830 --> 00:06:01.840 align:start position:0%
up to 89
 

00:06:01.840 --> 00:06:03.830 align:start position:0%
up to 89
well<00:06:02.000><c> hope</c><00:06:02.160><c> you</c><00:06:02.319><c> enjoyed</c><00:06:02.560><c> this</c><00:06:02.720><c> video</c><00:06:03.440><c> if</c><00:06:03.680><c> you</c>

00:06:03.830 --> 00:06:03.840 align:start position:0%
well hope you enjoyed this video if you
 

00:06:03.840 --> 00:06:05.590 align:start position:0%
well hope you enjoyed this video if you
have<00:06:03.919><c> any</c><00:06:04.160><c> comments</c><00:06:04.639><c> about</c><00:06:04.880><c> this</c><00:06:05.120><c> please</c><00:06:05.440><c> put</c>

00:06:05.590 --> 00:06:05.600 align:start position:0%
have any comments about this please put
 

00:06:05.600 --> 00:06:07.749 align:start position:0%
have any comments about this please put
them<00:06:05.919><c> down</c><00:06:06.080><c> below</c><00:06:06.560><c> or</c><00:06:06.800><c> if</c><00:06:06.880><c> you</c><00:06:07.039><c> have</c><00:06:07.199><c> a</c><00:06:07.440><c> better</c>

00:06:07.749 --> 00:06:07.759 align:start position:0%
them down below or if you have a better
 

00:06:07.759 --> 00:06:08.390 align:start position:0%
them down below or if you have a better
way

00:06:08.390 --> 00:06:08.400 align:start position:0%
way
 

00:06:08.400 --> 00:06:10.950 align:start position:0%
way
or<00:06:08.639><c> a</c><00:06:08.880><c> simpler</c><00:06:09.199><c> way</c><00:06:09.600><c> of</c><00:06:10.160><c> executing</c><00:06:10.720><c> this</c>

00:06:10.950 --> 00:06:10.960 align:start position:0%
or a simpler way of executing this
 

00:06:10.960 --> 00:06:11.749 align:start position:0%
or a simpler way of executing this
program

00:06:11.749 --> 00:06:11.759 align:start position:0%
program
 

00:06:11.759 --> 00:06:13.350 align:start position:0%
program
and<00:06:11.919><c> i</c><00:06:12.000><c> would</c><00:06:12.160><c> love</c><00:06:12.400><c> to</c><00:06:12.560><c> see</c><00:06:12.720><c> how</c><00:06:12.960><c> you</c><00:06:13.120><c> would</c><00:06:13.199><c> do</c>

00:06:13.350 --> 00:06:13.360 align:start position:0%
and i would love to see how you would do
 

00:06:13.360 --> 00:06:23.749 align:start position:0%
and i would love to see how you would do
this<00:06:13.759><c> thank</c><00:06:13.919><c> you</c><00:06:14.000><c> for</c><00:06:18.840><c> watching</c>

00:06:23.749 --> 00:06:23.759 align:start position:0%
 
 

00:06:23.759 --> 00:06:25.840 align:start position:0%
 
you

