WEBVTT
Kind: captions
Language: en

00:00:01.079 --> 00:00:02.430 align:start position:0%
 
hey<00:00:01.280><c> there</c><00:00:01.520><c> it's</c><00:00:01.760><c> Matt</c><00:00:02.120><c> one</c><00:00:02.240><c> of</c><00:00:02.320><c> the</c>

00:00:02.430 --> 00:00:02.440 align:start position:0%
hey there it's <PERSON> one of the
 

00:00:02.440 --> 00:00:04.870 align:start position:0%
hey there it's Matt one of the
maintainers<00:00:03.080><c> of</c><00:00:03.400><c> olama</c><00:00:04.400><c> I</c><00:00:04.480><c> wanted</c><00:00:04.720><c> to</c>

00:00:04.870 --> 00:00:04.880 align:start position:0%
maintainers of olama I wanted to
 

00:00:04.880 --> 00:00:07.869 align:start position:0%
maintainers of olama I wanted to
introduce<00:00:05.319><c> you</c><00:00:05.480><c> to</c><00:00:05.680><c> the</c><00:00:05.839><c> chat</c><00:00:06.279><c> API</c><00:00:06.879><c> endpoint</c>

00:00:07.869 --> 00:00:07.879 align:start position:0%
introduce you to the chat API endpoint
 

00:00:07.879 --> 00:00:09.350 align:start position:0%
introduce you to the chat API endpoint
one<00:00:08.040><c> of</c><00:00:08.120><c> our</c><00:00:08.280><c> goals</c><00:00:08.519><c> has</c><00:00:08.679><c> always</c><00:00:08.840><c> been</c><00:00:09.000><c> to</c><00:00:09.160><c> make</c>

00:00:09.350 --> 00:00:09.360 align:start position:0%
one of our goals has always been to make
 

00:00:09.360 --> 00:00:11.629 align:start position:0%
one of our goals has always been to make
the<00:00:09.639><c> best</c><00:00:10.040><c> tool</c><00:00:10.440><c> for</c><00:00:10.639><c> users</c><00:00:11.000><c> and</c><00:00:11.160><c> developers</c>

00:00:11.629 --> 00:00:11.639 align:start position:0%
the best tool for users and developers
 

00:00:11.639 --> 00:00:14.150 align:start position:0%
the best tool for users and developers
working<00:00:11.960><c> with</c><00:00:12.200><c> AI</c><00:00:13.120><c> so</c><00:00:13.400><c> the</c><00:00:13.559><c> main</c><00:00:13.839><c> way</c><00:00:13.960><c> to</c>

00:00:14.150 --> 00:00:14.160 align:start position:0%
working with AI so the main way to
 

00:00:14.160 --> 00:00:15.749 align:start position:0%
working with AI so the main way to
generate<00:00:14.559><c> an</c><00:00:14.719><c> answer</c><00:00:15.040><c> from</c><00:00:15.200><c> a</c><00:00:15.360><c> model</c><00:00:15.639><c> has</c>

00:00:15.749 --> 00:00:15.759 align:start position:0%
generate an answer from a model has
 

00:00:15.759 --> 00:00:18.070 align:start position:0%
generate an answer from a model has
always<00:00:16.000><c> been</c><00:00:16.160><c> the</c><00:00:16.359><c> generate</c><00:00:16.840><c> endpoint</c><00:00:17.840><c> if</c><00:00:17.960><c> you</c>

00:00:18.070 --> 00:00:18.080 align:start position:0%
always been the generate endpoint if you
 

00:00:18.080 --> 00:00:21.670 align:start position:0%
always been the generate endpoint if you
don't<00:00:18.359><c> yet</c><00:00:18.720><c> have</c><00:00:19.080><c> olama</c><00:00:19.920><c> you</c><00:00:20.000><c> can</c><00:00:20.199><c> find</c><00:00:20.439><c> it</c><00:00:20.600><c> at</c>

00:00:21.670 --> 00:00:21.680 align:start position:0%
don't yet have olama you can find it at
 

00:00:21.680 --> 00:00:24.189 align:start position:0%
don't yet have olama you can find it at
ol.<00:00:22.680><c> click</c><00:00:22.920><c> the</c><00:00:23.080><c> download</c><00:00:23.519><c> button</c><00:00:24.000><c> and</c>

00:00:24.189 --> 00:00:24.199 align:start position:0%
ol. click the download button and
 

00:00:24.199 --> 00:00:27.349 align:start position:0%
ol. click the download button and
install<00:00:24.560><c> it</c><00:00:25.119><c> you'll</c><00:00:25.359><c> get</c><00:00:25.480><c> a</c><00:00:25.680><c> CLI</c><00:00:26.359><c> UI</c><00:00:27.039><c> and</c><00:00:27.160><c> a</c>

00:00:27.349 --> 00:00:27.359 align:start position:0%
install it you'll get a CLI UI and a
 

00:00:27.359 --> 00:00:29.990 align:start position:0%
install it you'll get a CLI UI and a
server<00:00:28.000><c> that</c><00:00:28.199><c> responds</c><00:00:28.679><c> on</c><00:00:28.840><c> a</c><00:00:29.000><c> set</c><00:00:29.240><c> of</c><00:00:29.400><c> restful</c>

00:00:29.990 --> 00:00:30.000 align:start position:0%
server that responds on a set of restful
 

00:00:30.000 --> 00:00:31.710 align:start position:0%
server that responds on a set of restful
endpoints<00:00:30.679><c> let's</c><00:00:30.840><c> take</c><00:00:30.960><c> a</c><00:00:31.080><c> look</c><00:00:31.240><c> at</c><00:00:31.320><c> the</c><00:00:31.439><c> RO</c>

00:00:31.710 --> 00:00:31.720 align:start position:0%
endpoints let's take a look at the RO
 

00:00:31.720 --> 00:00:33.869 align:start position:0%
endpoints let's take a look at the RO
endpoints<00:00:32.680><c> I'm</c><00:00:32.800><c> using</c><00:00:33.079><c> the</c><00:00:33.200><c> rest</c><00:00:33.559><c> client</c>

00:00:33.869 --> 00:00:33.879 align:start position:0%
endpoints I'm using the rest client
 

00:00:33.879 --> 00:00:37.110 align:start position:0%
endpoints I'm using the rest client
plugin<00:00:34.280><c> from</c><00:00:34.800><c> haa</c><00:00:35.559><c> Mau</c><00:00:36.320><c> I</c><00:00:36.480><c> probably</c><00:00:36.719><c> butchered</c>

00:00:37.110 --> 00:00:37.120 align:start position:0%
plugin from haa Mau I probably butchered
 

00:00:37.120 --> 00:00:40.670 align:start position:0%
plugin from haa Mau I probably butchered
the<00:00:37.239><c> name</c><00:00:37.480><c> but</c><00:00:37.640><c> the</c><00:00:37.760><c> plugin</c><00:00:38.440><c> is</c><00:00:39.440><c> amazing</c><00:00:40.440><c> I</c><00:00:40.520><c> can</c>

00:00:40.670 --> 00:00:40.680 align:start position:0%
the name but the plugin is amazing I can
 

00:00:40.680 --> 00:00:43.990 align:start position:0%
the name but the plugin is amazing I can
put<00:00:40.840><c> in</c><00:00:40.960><c> the</c><00:00:41.120><c> URL</c><00:00:41.680><c> which</c><00:00:41.800><c> is</c><00:00:42.039><c> usually</c>

00:00:43.990 --> 00:00:44.000 align:start position:0%
put in the URL which is usually
 

00:00:44.000 --> 00:00:47.670 align:start position:0%
put in the URL which is usually
HTTP<00:00:45.000><c> localhost</c><00:00:45.840><c> colon</c>

00:00:47.670 --> 00:00:47.680 align:start position:0%
HTTP localhost colon
 

00:00:47.680 --> 00:00:51.790 align:start position:0%
HTTP localhost colon
11434<00:00:48.680><c> API</c><00:00:49.480><c> generate</c><00:00:50.480><c> to</c><00:00:50.680><c> use</c><00:00:50.920><c> it</c><00:00:51.199><c> you</c><00:00:51.399><c> post</c><00:00:51.640><c> a</c>

00:00:51.790 --> 00:00:51.800 align:start position:0%
11434 API generate to use it you post a
 

00:00:51.800 --> 00:00:54.470 align:start position:0%
11434 API generate to use it you post a
Json<00:00:52.239><c> body</c><00:00:52.840><c> with</c><00:00:53.000><c> at</c><00:00:53.120><c> least</c><00:00:53.320><c> a</c><00:00:53.480><c> model</c><00:00:53.800><c> name</c><00:00:54.320><c> and</c>

00:00:54.470 --> 00:00:54.480 align:start position:0%
Json body with at least a model name and
 

00:00:54.480 --> 00:00:56.670 align:start position:0%
Json body with at least a model name and
the<00:00:54.680><c> prompt</c><00:00:55.600><c> and</c><00:00:55.719><c> the</c><00:00:55.840><c> output</c><00:00:56.199><c> is</c><00:00:56.399><c> either</c><00:00:56.559><c> a</c>

00:00:56.670 --> 00:00:56.680 align:start position:0%
the prompt and the output is either a
 

00:00:56.680 --> 00:00:58.950 align:start position:0%
the prompt and the output is either a
stream<00:00:57.000><c> of</c><00:00:57.120><c> Json</c><00:00:57.480><c> blobs</c><00:00:57.879><c> one</c><00:00:58.000><c> for</c><00:00:58.199><c> each</c><00:00:58.480><c> token</c>

00:00:58.950 --> 00:00:58.960 align:start position:0%
stream of Json blobs one for each token
 

00:00:58.960 --> 00:01:01.110 align:start position:0%
stream of Json blobs one for each token
in<00:00:59.079><c> the</c><00:00:59.199><c> output</c><00:00:59.960><c> or</c><00:01:00.239><c> a</c><00:01:00.359><c> single</c><00:01:00.640><c> blob</c><00:01:01.000><c> with</c>

00:01:01.110 --> 00:01:01.120 align:start position:0%
in the output or a single blob with
 

00:01:01.120 --> 00:01:02.990 align:start position:0%
in the output or a single blob with
everything<00:01:02.000><c> for</c><00:01:02.199><c> this</c><00:01:02.399><c> example</c><00:01:02.879><c> I'm</c>

00:01:02.990 --> 00:01:03.000 align:start position:0%
everything for this example I'm
 

00:01:03.000 --> 00:01:05.469 align:start position:0%
everything for this example I'm
streaming<00:01:03.480><c> so</c><00:01:03.760><c> here</c><00:01:03.920><c> are</c><00:01:04.199><c> all</c><00:01:04.400><c> the</c><00:01:04.519><c> Json</c><00:01:04.920><c> blobs</c>

00:01:05.469 --> 00:01:05.479 align:start position:0%
streaming so here are all the Json blobs
 

00:01:05.479 --> 00:01:07.910 align:start position:0%
streaming so here are all the Json blobs
with<00:01:05.600><c> a</c><00:01:05.799><c> response</c><00:01:06.200><c> that</c><00:01:06.320><c> is</c><00:01:06.439><c> the</c><00:01:06.600><c> latest</c><00:01:06.920><c> token</c>

00:01:07.910 --> 00:01:07.920 align:start position:0%
with a response that is the latest token
 

00:01:07.920 --> 00:01:10.149 align:start position:0%
with a response that is the latest token
the<00:01:08.080><c> last</c><00:01:08.280><c> blob</c><00:01:08.640><c> you</c><00:01:08.759><c> get</c><00:01:09.040><c> has</c><00:01:09.280><c> the</c><00:01:09.439><c> metrics</c>

00:01:10.149 --> 00:01:10.159 align:start position:0%
the last blob you get has the metrics
 

00:01:10.159 --> 00:01:12.830 align:start position:0%
the last blob you get has the metrics
about<00:01:10.799><c> the</c><00:01:11.040><c> generation</c><00:01:12.040><c> as</c><00:01:12.200><c> well</c><00:01:12.360><c> as</c><00:01:12.640><c> the</c>

00:01:12.830 --> 00:01:12.840 align:start position:0%
about the generation as well as the
 

00:01:12.840 --> 00:01:15.030 align:start position:0%
about the generation as well as the
context<00:01:13.640><c> as</c><00:01:13.759><c> an</c><00:01:13.920><c> embedding</c><00:01:14.600><c> which</c><00:01:14.720><c> is</c><00:01:14.880><c> an</c>

00:01:15.030 --> 00:01:15.040 align:start position:0%
context as an embedding which is an
 

00:01:15.040 --> 00:01:17.510 align:start position:0%
context as an embedding which is an
array<00:01:15.320><c> of</c><00:01:15.479><c> numbers</c><00:01:16.439><c> that</c><00:01:16.680><c> context</c><00:01:17.200><c> can</c><00:01:17.400><c> then</c>

00:01:17.510 --> 00:01:17.520 align:start position:0%
array of numbers that context can then
 

00:01:17.520 --> 00:01:19.550 align:start position:0%
array of numbers that context can then
be<00:01:17.680><c> fed</c><00:01:18.040><c> to</c><00:01:18.159><c> the</c><00:01:18.320><c> next</c><00:01:18.560><c> Call</c><00:01:18.880><c> of</c><00:01:19.000><c> the</c><00:01:19.119><c> generate</c>

00:01:19.550 --> 00:01:19.560 align:start position:0%
be fed to the next Call of the generate
 

00:01:19.560 --> 00:01:21.990 align:start position:0%
be fed to the next Call of the generate
endpoint<00:01:20.360><c> to</c><00:01:20.600><c> provide</c><00:01:20.920><c> continuity</c><00:01:21.680><c> and</c>

00:01:21.990 --> 00:01:22.000 align:start position:0%
endpoint to provide continuity and
 

00:01:22.000 --> 00:01:24.830 align:start position:0%
endpoint to provide continuity and
history<00:01:23.000><c> as</c><00:01:23.079><c> a</c><00:01:23.280><c> result</c><00:01:23.880><c> history</c><00:01:24.280><c> is</c><00:01:24.560><c> really</c>

00:01:24.830 --> 00:01:24.840 align:start position:0%
history as a result history is really
 

00:01:24.840 --> 00:01:27.109 align:start position:0%
history as a result history is really
easy<00:01:25.119><c> to</c><00:01:25.280><c> deal</c><00:01:25.560><c> with</c><00:01:25.880><c> but</c><00:01:26.439><c> that</c><00:01:26.560><c> comes</c><00:01:26.759><c> at</c><00:01:26.880><c> a</c>

00:01:27.109 --> 00:01:27.119 align:start position:0%
easy to deal with but that comes at a
 

00:01:27.119 --> 00:01:29.910 align:start position:0%
easy to deal with but that comes at a
cost<00:01:27.400><c> of</c><00:01:27.600><c> not</c><00:01:27.799><c> being</c><00:01:28.119><c> very</c><00:01:28.360><c> flexible</c><00:01:29.280><c> and</c><00:01:29.479><c> very</c>

00:01:29.910 --> 00:01:29.920 align:start position:0%
cost of not being very flexible and very
 

00:01:29.920 --> 00:01:32.749 align:start position:0%
cost of not being very flexible and very
different<00:01:30.200><c> from</c><00:01:30.400><c> how</c><00:01:30.680><c> other</c><00:01:30.920><c> tools</c><00:01:31.520><c> work</c><00:01:32.520><c> so</c>

00:01:32.749 --> 00:01:32.759 align:start position:0%
different from how other tools work so
 

00:01:32.759 --> 00:01:35.149 align:start position:0%
different from how other tools work so
we<00:01:32.880><c> added</c><00:01:33.119><c> a</c><00:01:33.280><c> chat</c><00:01:33.600><c> endpoint</c><00:01:34.200><c> as</c><00:01:34.320><c> well</c><00:01:35.040><c> you</c>

00:01:35.149 --> 00:01:35.159 align:start position:0%
we added a chat endpoint as well you
 

00:01:35.159 --> 00:01:37.389 align:start position:0%
we added a chat endpoint as well you
have<00:01:35.280><c> to</c><00:01:35.399><c> make</c><00:01:35.520><c> sure</c><00:01:35.759><c> you're</c><00:01:35.920><c> on</c><00:01:36.200><c> version</c>

00:01:37.389 --> 00:01:37.399 align:start position:0%
have to make sure you're on version
 

00:01:37.399 --> 00:01:40.550 align:start position:0%
have to make sure you're on version
0.114<00:01:38.399><c> of</c><00:01:38.560><c> AMA</c><00:01:39.040><c> to</c><00:01:39.159><c> see</c><00:01:39.439><c> this</c><00:01:40.119><c> for</c><00:01:40.360><c> this</c>

00:01:40.550 --> 00:01:40.560 align:start position:0%
0.114 of AMA to see this for this
 

00:01:40.560 --> 00:01:43.350 align:start position:0%
0.114 of AMA to see this for this
example<00:01:41.159><c> I'll</c><00:01:41.320><c> enter</c><00:01:41.600><c> the</c><00:01:41.759><c> URL</c><00:01:42.240><c> which</c><00:01:42.360><c> is</c>

00:01:43.350 --> 00:01:43.360 align:start position:0%
example I'll enter the URL which is
 

00:01:43.360 --> 00:01:46.709 align:start position:0%
example I'll enter the URL which is
HTTP<00:01:44.479><c> localhost</c><00:01:45.479><c> colon</c>

00:01:46.709 --> 00:01:46.719 align:start position:0%
HTTP localhost colon
 

00:01:46.719 --> 00:01:50.789 align:start position:0%
HTTP localhost colon
11434<00:01:47.840><c> API</c><00:01:48.840><c> chat</c><00:01:49.759><c> and</c><00:01:49.880><c> now</c><00:01:50.000><c> I</c><00:01:50.079><c> can</c><00:01:50.320><c> provide</c><00:01:50.600><c> the</c>

00:01:50.789 --> 00:01:50.799 align:start position:0%
11434 API chat and now I can provide the
 

00:01:50.799 --> 00:01:53.630 align:start position:0%
11434 API chat and now I can provide the
body<00:01:51.360><c> which</c><00:01:51.479><c> is</c><00:01:51.600><c> the</c><00:01:51.759><c> model</c><00:01:52.360><c> and</c><00:01:52.759><c> messages</c>

00:01:53.630 --> 00:01:53.640 align:start position:0%
body which is the model and messages
 

00:01:53.640 --> 00:01:56.590 align:start position:0%
body which is the model and messages
messages<00:01:54.240><c> is</c><00:01:54.360><c> an</c><00:01:54.560><c> array</c><00:01:54.920><c> of</c><00:01:55.240><c> objects</c><00:01:56.000><c> now</c><00:01:56.200><c> each</c>

00:01:56.590 --> 00:01:56.600 align:start position:0%
messages is an array of objects now each
 

00:01:56.600 --> 00:01:58.830 align:start position:0%
messages is an array of objects now each
object<00:01:57.000><c> has</c><00:01:57.119><c> a</c><00:01:57.280><c> role</c><00:01:57.759><c> which</c><00:01:57.880><c> can</c><00:01:58.039><c> be</c><00:01:58.159><c> a</c><00:01:58.320><c> user</c><00:01:58.680><c> or</c>

00:01:58.830 --> 00:01:58.840 align:start position:0%
object has a role which can be a user or
 

00:01:58.840 --> 00:02:01.350 align:start position:0%
object has a role which can be a user or
an<00:01:59.039><c> assistant</c><00:01:59.439><c> or</c><00:01:59.880><c> system</c><00:02:00.680><c> and</c><00:02:00.840><c> the</c><00:02:01.000><c> content</c>

00:02:01.350 --> 00:02:01.360 align:start position:0%
an assistant or system and the content
 

00:02:01.360 --> 00:02:04.069 align:start position:0%
an assistant or system and the content
is<00:02:01.520><c> the</c><00:02:01.680><c> prompt</c><00:02:02.079><c> or</c><00:02:02.280><c> the</c><00:02:02.439><c> answer</c><00:02:03.360><c> sending</c><00:02:03.840><c> this</c>

00:02:04.069 --> 00:02:04.079 align:start position:0%
is the prompt or the answer sending this
 

00:02:04.079 --> 00:02:06.709 align:start position:0%
is the prompt or the answer sending this
gets<00:02:04.320><c> me</c><00:02:04.520><c> the</c><00:02:04.640><c> stream</c><00:02:05.000><c> of</c><00:02:05.119><c> Json</c><00:02:05.520><c> blobs</c><00:02:06.039><c> again</c>

00:02:06.709 --> 00:02:06.719 align:start position:0%
gets me the stream of Json blobs again
 

00:02:06.719 --> 00:02:08.029 align:start position:0%
gets me the stream of Json blobs again
but<00:02:06.880><c> this</c><00:02:07.039><c> time</c><00:02:07.200><c> instead</c><00:02:07.439><c> of</c><00:02:07.520><c> seeing</c><00:02:07.840><c> a</c>

00:02:08.029 --> 00:02:08.039 align:start position:0%
but this time instead of seeing a
 

00:02:08.039 --> 00:02:10.589 align:start position:0%
but this time instead of seeing a
response<00:02:08.440><c> I</c><00:02:08.520><c> see</c><00:02:08.720><c> a</c><00:02:08.959><c> message</c><00:02:09.399><c> in</c><00:02:09.599><c> each</c><00:02:09.800><c> one</c><00:02:10.440><c> so</c>

00:02:10.589 --> 00:02:10.599 align:start position:0%
response I see a message in each one so
 

00:02:10.599 --> 00:02:13.350 align:start position:0%
response I see a message in each one so
let's<00:02:10.840><c> apply</c><00:02:11.160><c> this</c><00:02:11.319><c> to</c><00:02:11.680><c> a</c><00:02:11.840><c> simple</c><00:02:12.360><c> application</c>

00:02:13.350 --> 00:02:13.360 align:start position:0%
let's apply this to a simple application
 

00:02:13.360 --> 00:02:15.990 align:start position:0%
let's apply this to a simple application
here's<00:02:13.640><c> my</c><00:02:13.920><c> app</c><00:02:14.360><c> that</c><00:02:14.519><c> I</c><00:02:14.599><c> can</c><00:02:14.800><c> run</c><00:02:15.519><c> I'll</c><00:02:15.800><c> just</c>

00:02:15.990 --> 00:02:16.000 align:start position:0%
here's my app that I can run I'll just
 

00:02:16.000 --> 00:02:28.750 align:start position:0%
here's my app that I can run I'll just
use<00:02:16.239><c> the</c><00:02:16.360><c> command</c><00:02:16.760><c> npm</c><00:02:17.400><c> start</c><00:02:17.920><c> to</c><00:02:18.080><c> start</c><00:02:18.319><c> it</c>

00:02:28.750 --> 00:02:28.760 align:start position:0%
 
 

00:02:28.760 --> 00:02:31.589 align:start position:0%
 
up<00:02:29.920><c> now</c><00:02:30.080><c> I</c><00:02:30.160><c> can</c><00:02:30.319><c> press</c><00:02:30.519><c> enter</c><00:02:30.800><c> to</c><00:02:31.000><c> quit</c><00:02:31.400><c> and</c>

00:02:31.589 --> 00:02:31.599 align:start position:0%
up now I can press enter to quit and
 

00:02:31.599 --> 00:02:33.030 align:start position:0%
up now I can press enter to quit and
I've<00:02:31.720><c> chosen</c><00:02:31.959><c> to</c><00:02:32.080><c> show</c><00:02:32.319><c> you</c><00:02:32.480><c> the</c><00:02:32.599><c> entire</c>

00:02:33.030 --> 00:02:33.040 align:start position:0%
I've chosen to show you the entire
 

00:02:33.040 --> 00:02:35.949 align:start position:0%
I've chosen to show you the entire
history<00:02:33.400><c> formatted</c><00:02:34.080><c> as</c><00:02:34.280><c> the</c><00:02:34.400><c> messages</c><00:02:34.959><c> array</c>

00:02:35.949 --> 00:02:35.959 align:start position:0%
history formatted as the messages array
 

00:02:35.959 --> 00:02:38.070 align:start position:0%
history formatted as the messages array
so<00:02:36.120><c> let's</c><00:02:36.319><c> take</c><00:02:36.440><c> a</c><00:02:36.599><c> quick</c><00:02:36.840><c> look</c><00:02:37.080><c> at</c><00:02:37.200><c> the</c><00:02:37.319><c> code</c>

00:02:38.070 --> 00:02:38.080 align:start position:0%
so let's take a quick look at the code
 

00:02:38.080 --> 00:02:40.270 align:start position:0%
so let's take a quick look at the code
there's<00:02:38.360><c> a</c><00:02:38.519><c> bit</c><00:02:38.879><c> here</c><00:02:39.239><c> about</c><00:02:39.640><c> getting</c><00:02:39.920><c> user</c>

00:02:40.270 --> 00:02:40.280 align:start position:0%
there's a bit here about getting user
 

00:02:40.280 --> 00:02:42.790 align:start position:0%
there's a bit here about getting user
input<00:02:40.879><c> and</c><00:02:41.040><c> running</c><00:02:41.319><c> our</c><00:02:41.519><c> Loop</c><00:02:42.319><c> but</c><00:02:42.480><c> then</c><00:02:42.640><c> we</c>

00:02:42.790 --> 00:02:42.800 align:start position:0%
input and running our Loop but then we
 

00:02:42.800 --> 00:02:45.110 align:start position:0%
input and running our Loop but then we
put<00:02:43.000><c> the</c><00:02:43.200><c> input</c><00:02:43.640><c> into</c><00:02:43.879><c> a</c><00:02:44.040><c> message</c><00:02:44.560><c> and</c><00:02:44.800><c> pass</c>

00:02:45.110 --> 00:02:45.120 align:start position:0%
put the input into a message and pass
 

00:02:45.120 --> 00:02:47.190 align:start position:0%
put the input into a message and pass
that<00:02:45.280><c> to</c><00:02:45.440><c> the</c><00:02:45.560><c> chat</c><00:02:45.920><c> function</c><00:02:46.760><c> my</c><00:02:46.920><c> chat</c>

00:02:47.190 --> 00:02:47.200 align:start position:0%
that to the chat function my chat
 

00:02:47.200 --> 00:02:48.990 align:start position:0%
that to the chat function my chat
function<00:02:47.519><c> spits</c><00:02:47.840><c> out</c><00:02:48.040><c> the</c><00:02:48.159><c> answer</c><00:02:48.680><c> as</c><00:02:48.800><c> a</c>

00:02:48.990 --> 00:02:49.000 align:start position:0%
function spits out the answer as a
 

00:02:49.000 --> 00:02:51.190 align:start position:0%
function spits out the answer as a
message<00:02:49.720><c> and</c><00:02:49.840><c> I</c><00:02:50.000><c> push</c><00:02:50.239><c> that</c><00:02:50.360><c> to</c><00:02:50.519><c> the</c><00:02:50.720><c> messages</c>

00:02:51.190 --> 00:02:51.200 align:start position:0%
message and I push that to the messages
 

00:02:51.200 --> 00:02:53.910 align:start position:0%
message and I push that to the messages
array<00:02:52.080><c> and</c><00:02:52.159><c> then</c><00:02:52.319><c> it</c><00:02:52.519><c> repeats</c><00:02:53.319><c> so</c><00:02:53.560><c> let's</c><00:02:53.800><c> look</c>

00:02:53.910 --> 00:02:53.920 align:start position:0%
array and then it repeats so let's look
 

00:02:53.920 --> 00:02:55.750 align:start position:0%
array and then it repeats so let's look
at<00:02:54.000><c> the</c><00:02:54.120><c> chat</c><00:02:54.360><c> function</c><00:02:55.200><c> this</c><00:02:55.360><c> makes</c><00:02:55.560><c> a</c>

00:02:55.750 --> 00:02:55.760 align:start position:0%
at the chat function this makes a
 

00:02:55.760 --> 00:02:57.830 align:start position:0%
at the chat function this makes a
request<00:02:56.000><c> to</c><00:02:56.159><c> the</c><00:02:56.280><c> endpoint</c><00:02:56.959><c> I</c><00:02:57.080><c> get</c><00:02:57.239><c> a</c><00:02:57.360><c> reader</c>

00:02:57.830 --> 00:02:57.840 align:start position:0%
request to the endpoint I get a reader
 

00:02:57.840 --> 00:02:59.910 align:start position:0%
request to the endpoint I get a reader
and<00:02:58.040><c> cycle</c><00:02:58.440><c> through</c><00:02:58.680><c> the</c><00:02:58.800><c> stream</c><00:02:59.159><c> of</c><00:02:59.360><c> respons</c>

00:02:59.910 --> 00:02:59.920 align:start position:0%
and cycle through the stream of respons
 

00:02:59.920 --> 00:03:02.869 align:start position:0%
and cycle through the stream of respons
es<00:03:00.760><c> for</c><00:03:00.959><c> each</c><00:03:01.159><c> one</c><00:03:01.560><c> I</c><00:03:01.720><c> get</c><00:03:01.959><c> the</c><00:03:02.239><c> content</c><00:03:02.599><c> of</c><00:03:02.720><c> the</c>

00:03:02.869 --> 00:03:02.879 align:start position:0%
es for each one I get the content of the
 

00:03:02.879 --> 00:03:05.670 align:start position:0%
es for each one I get the content of the
message<00:03:03.280><c> and</c><00:03:03.440><c> spit</c><00:03:03.720><c> it</c><00:03:03.879><c> out</c><00:03:04.760><c> I</c><00:03:04.920><c> also</c><00:03:05.159><c> add</c><00:03:05.319><c> it</c><00:03:05.440><c> to</c>

00:03:05.670 --> 00:03:05.680 align:start position:0%
message and spit it out I also add it to
 

00:03:05.680 --> 00:03:08.630 align:start position:0%
message and spit it out I also add it to
the<00:03:05.840><c> full</c><00:03:06.159><c> content</c><00:03:06.560><c> so</c><00:03:06.840><c> far</c><00:03:07.720><c> finally</c><00:03:08.159><c> I</c><00:03:08.280><c> output</c>

00:03:08.630 --> 00:03:08.640 align:start position:0%
the full content so far finally I output
 

00:03:08.640 --> 00:03:11.270 align:start position:0%
the full content so far finally I output
the<00:03:08.720><c> full</c><00:03:08.959><c> message</c><00:03:09.440><c> and</c><00:03:09.640><c> that's</c><00:03:10.120><c> really</c><00:03:10.519><c> it</c>

00:03:11.270 --> 00:03:11.280 align:start position:0%
the full message and that's really it
 

00:03:11.280 --> 00:03:12.710 align:start position:0%
the full message and that's really it
the<00:03:11.440><c> python</c><00:03:11.760><c> version</c><00:03:12.040><c> is</c><00:03:12.239><c> pretty</c><00:03:12.440><c> much</c><00:03:12.599><c> the</c>

00:03:12.710 --> 00:03:12.720 align:start position:0%
the python version is pretty much the
 

00:03:12.720 --> 00:03:14.670 align:start position:0%
the python version is pretty much the
same<00:03:12.959><c> thing</c><00:03:13.480><c> I</c><00:03:13.560><c> have</c><00:03:13.680><c> a</c><00:03:13.799><c> loop</c><00:03:14.239><c> that</c><00:03:14.400><c> takes</c>

00:03:14.670 --> 00:03:14.680 align:start position:0%
same thing I have a loop that takes
 

00:03:14.680 --> 00:03:17.070 align:start position:0%
same thing I have a loop that takes
input<00:03:15.280><c> and</c><00:03:15.400><c> then</c><00:03:15.599><c> add</c><00:03:15.879><c> that</c><00:03:16.000><c> to</c><00:03:16.159><c> a</c><00:03:16.319><c> message</c><00:03:16.959><c> and</c>

00:03:17.070 --> 00:03:17.080 align:start position:0%
input and then add that to a message and
 

00:03:17.080 --> 00:03:18.710 align:start position:0%
input and then add that to a message and
send<00:03:17.400><c> all</c><00:03:17.640><c> the</c><00:03:17.760><c> messages</c><00:03:18.159><c> to</c><00:03:18.319><c> the</c><00:03:18.440><c> chat</c>

00:03:18.710 --> 00:03:18.720 align:start position:0%
send all the messages to the chat
 

00:03:18.720 --> 00:03:21.030 align:start position:0%
send all the messages to the chat
function<00:03:19.560><c> that</c><00:03:19.760><c> function</c><00:03:20.159><c> outputs</c><00:03:20.560><c> a</c><00:03:20.720><c> message</c>

00:03:21.030 --> 00:03:21.040 align:start position:0%
function that function outputs a message
 

00:03:21.040 --> 00:03:23.789 align:start position:0%
function that function outputs a message
to<00:03:21.319><c> the</c><00:03:21.519><c> list</c><00:03:21.760><c> of</c><00:03:21.920><c> messages</c><00:03:22.519><c> and</c><00:03:22.680><c> I</c><00:03:22.879><c> repeat</c><00:03:23.720><c> the</c>

00:03:23.789 --> 00:03:23.799 align:start position:0%
to the list of messages and I repeat the
 

00:03:23.799 --> 00:03:25.110 align:start position:0%
to the list of messages and I repeat the
chat<00:03:24.040><c> function</c><00:03:24.360><c> makes</c><00:03:24.519><c> a</c><00:03:24.680><c> call</c><00:03:24.879><c> to</c><00:03:25.040><c> the</c>

00:03:25.110 --> 00:03:25.120 align:start position:0%
chat function makes a call to the
 

00:03:25.120 --> 00:03:26.949 align:start position:0%
chat function makes a call to the
endpoint<00:03:25.680><c> passing</c><00:03:26.000><c> the</c><00:03:26.120><c> model</c><00:03:26.680><c> and</c><00:03:26.840><c> the</c>

00:03:26.949 --> 00:03:26.959 align:start position:0%
endpoint passing the model and the
 

00:03:26.959 --> 00:03:28.949 align:start position:0%
endpoint passing the model and the
messages<00:03:27.799><c> and</c><00:03:27.879><c> then</c><00:03:28.000><c> I</c><00:03:28.120><c> cycle</c><00:03:28.519><c> through</c><00:03:28.760><c> all</c>

00:03:28.949 --> 00:03:28.959 align:start position:0%
messages and then I cycle through all
 

00:03:28.959 --> 00:03:30.990 align:start position:0%
messages and then I cycle through all
the<00:03:29.120><c> responses</c><00:03:29.760><c> pulling</c><00:03:30.080><c> out</c><00:03:30.200><c> the</c><00:03:30.360><c> content</c><00:03:30.840><c> of</c>

00:03:30.990 --> 00:03:31.000 align:start position:0%
the responses pulling out the content of
 

00:03:31.000 --> 00:03:32.750 align:start position:0%
the responses pulling out the content of
each<00:03:31.239><c> message</c><00:03:31.879><c> which</c><00:03:32.080><c> gets</c><00:03:32.280><c> added</c><00:03:32.519><c> to</c><00:03:32.680><c> the</c>

00:03:32.750 --> 00:03:32.760 align:start position:0%
each message which gets added to the
 

00:03:32.760 --> 00:03:35.149 align:start position:0%
each message which gets added to the
output<00:03:33.319><c> and</c><00:03:33.480><c> printed</c><00:03:33.799><c> to</c><00:03:33.959><c> the</c><00:03:34.080><c> screen</c><00:03:35.040><c> the</c>

00:03:35.149 --> 00:03:35.159 align:start position:0%
output and printed to the screen the
 

00:03:35.159 --> 00:03:36.789 align:start position:0%
output and printed to the screen the
full<00:03:35.439><c> message</c><00:03:35.840><c> then</c><00:03:36.000><c> gets</c><00:03:36.360><c> returned</c><00:03:36.599><c> to</c><00:03:36.680><c> the</c>

00:03:36.789 --> 00:03:36.799 align:start position:0%
full message then gets returned to the
 

00:03:36.799 --> 00:03:39.509 align:start position:0%
full message then gets returned to the
main<00:03:37.080><c> function</c><00:03:38.080><c> and</c><00:03:38.239><c> that's</c><00:03:38.439><c> it</c><00:03:39.000><c> you</c><00:03:39.120><c> can</c><00:03:39.239><c> see</c>

00:03:39.509 --> 00:03:39.519 align:start position:0%
main function and that's it you can see
 

00:03:39.519 --> 00:03:41.190 align:start position:0%
main function and that's it you can see
that<00:03:39.720><c> working</c><00:03:40.040><c> with</c><00:03:40.200><c> the</c><00:03:40.319><c> chat</c><00:03:40.560><c> endpoint</c><00:03:41.000><c> is</c>

00:03:41.190 --> 00:03:41.200 align:start position:0%
that working with the chat endpoint is
 

00:03:41.200 --> 00:03:44.110 align:start position:0%
that working with the chat endpoint is
easy<00:03:41.920><c> but</c><00:03:42.080><c> maybe</c><00:03:42.480><c> a</c><00:03:42.680><c> a</c><00:03:42.920><c> touch</c><00:03:43.200><c> more</c><00:03:43.480><c> work</c><00:03:43.920><c> than</c>

00:03:44.110 --> 00:03:44.120 align:start position:0%
easy but maybe a a touch more work than
 

00:03:44.120 --> 00:03:47.149 align:start position:0%
easy but maybe a a touch more work than
the<00:03:44.280><c> generate</c><00:03:45.040><c> endpoint</c><00:03:46.040><c> that</c><00:03:46.280><c> extra</c><00:03:46.640><c> work</c>

00:03:47.149 --> 00:03:47.159 align:start position:0%
the generate endpoint that extra work
 

00:03:47.159 --> 00:03:49.229 align:start position:0%
the generate endpoint that extra work
comes<00:03:47.480><c> with</c><00:03:47.680><c> the</c><00:03:47.879><c> benefit</c><00:03:48.439><c> of</c><00:03:48.599><c> being</c><00:03:48.879><c> able</c><00:03:49.120><c> to</c>

00:03:49.229 --> 00:03:49.239 align:start position:0%
comes with the benefit of being able to
 

00:03:49.239 --> 00:03:51.630 align:start position:0%
comes with the benefit of being able to
do<00:03:49.640><c> more</c><00:03:50.000><c> with</c><00:03:50.159><c> the</c><00:03:50.319><c> history</c><00:03:51.120><c> for</c><00:03:51.280><c> instance</c>

00:03:51.630 --> 00:03:51.640 align:start position:0%
do more with the history for instance
 

00:03:51.640 --> 00:03:53.229 align:start position:0%
do more with the history for instance
you<00:03:51.760><c> can</c><00:03:52.000><c> decide</c><00:03:52.239><c> to</c><00:03:52.400><c> keep</c><00:03:52.640><c> the</c><00:03:52.799><c> last</c><00:03:53.040><c> five</c>

00:03:53.229 --> 00:03:53.239 align:start position:0%
you can decide to keep the last five
 

00:03:53.239 --> 00:03:55.110 align:start position:0%
you can decide to keep the last five
messages<00:03:53.640><c> in</c><00:03:53.799><c> full</c><00:03:54.079><c> and</c><00:03:54.280><c> summarize</c><00:03:54.799><c> anything</c>

00:03:55.110 --> 00:03:55.120 align:start position:0%
messages in full and summarize anything
 

00:03:55.120 --> 00:03:57.470 align:start position:0%
messages in full and summarize anything
older<00:03:55.959><c> or</c><00:03:56.159><c> maybe</c><00:03:56.400><c> you</c><00:03:56.640><c> keep</c><00:03:56.920><c> any</c><00:03:57.159><c> short</c>

00:03:57.470 --> 00:03:57.480 align:start position:0%
older or maybe you keep any short
 

00:03:57.480 --> 00:03:59.229 align:start position:0%
older or maybe you keep any short
messages<00:03:58.000><c> and</c><00:03:58.159><c> summarize</c><00:03:58.640><c> the</c><00:03:58.760><c> outputs</c><00:03:59.120><c> with</c>

00:03:59.229 --> 00:03:59.239 align:start position:0%
messages and summarize the outputs with
 

00:03:59.239 --> 00:04:01.630 align:start position:0%
messages and summarize the outputs with
more<00:03:59.400><c> than</c><00:03:59.799><c> 100</c><00:04:00.000><c> words</c><00:04:00.959><c> some</c><00:04:01.159><c> folks</c><00:04:01.400><c> are</c>

00:04:01.630 --> 00:04:01.640 align:start position:0%
more than 100 words some folks are
 

00:04:01.640 --> 00:04:03.390 align:start position:0%
more than 100 words some folks are
finding<00:04:02.120><c> that</c><00:04:02.280><c> it</c><00:04:02.480><c> allows</c><00:04:02.920><c> alternate</c>

00:04:03.390 --> 00:04:03.400 align:start position:0%
finding that it allows alternate
 

00:04:03.400 --> 00:04:04.910 align:start position:0%
finding that it allows alternate
techniques<00:04:03.920><c> like</c><00:04:04.120><c> Chain</c><00:04:04.360><c> of</c><00:04:04.640><c> Thought</c>

00:04:04.910 --> 00:04:04.920 align:start position:0%
techniques like Chain of Thought
 

00:04:04.920 --> 00:04:07.630 align:start position:0%
techniques like Chain of Thought
prompting<00:04:05.599><c> to</c><00:04:05.760><c> be</c><00:04:05.920><c> easier</c><00:04:06.879><c> I</c><00:04:07.040><c> think</c><00:04:07.280><c> all</c><00:04:07.439><c> of</c>

00:04:07.630 --> 00:04:07.640 align:start position:0%
prompting to be easier I think all of
 

00:04:07.640 --> 00:04:10.429 align:start position:0%
prompting to be easier I think all of
that<00:04:07.879><c> is</c><00:04:08.120><c> pretty</c><00:04:08.400><c> cool</c><00:04:09.319><c> I</c><00:04:09.480><c> can't</c><00:04:09.720><c> wait</c><00:04:09.879><c> to</c><00:04:10.040><c> see</c>

00:04:10.429 --> 00:04:10.439 align:start position:0%
that is pretty cool I can't wait to see
 

00:04:10.439 --> 00:04:11.949 align:start position:0%
that is pretty cool I can't wait to see
what<00:04:10.560><c> you</c><00:04:10.720><c> do</c><00:04:10.879><c> with</c><00:04:11.000><c> it</c><00:04:11.280><c> and</c><00:04:11.519><c> hope</c><00:04:11.680><c> you</c><00:04:11.799><c> will</c>

00:04:11.949 --> 00:04:11.959 align:start position:0%
what you do with it and hope you will
 

00:04:11.959 --> 00:04:14.350 align:start position:0%
what you do with it and hope you will
share<00:04:12.200><c> it</c><00:04:12.439><c> with</c><00:04:12.640><c> us</c><00:04:12.959><c> in</c><00:04:13.200><c> our</c><00:04:13.439><c> Discord</c><00:04:14.239><c> which</c>

00:04:14.350 --> 00:04:14.360 align:start position:0%
share it with us in our Discord which
 

00:04:14.360 --> 00:04:15.990 align:start position:0%
share it with us in our Discord which
you<00:04:14.480><c> can</c><00:04:14.680><c> find</c><00:04:14.920><c> at</c>

00:04:15.990 --> 00:04:16.000 align:start position:0%
you can find at
 

00:04:16.000 --> 00:04:18.909 align:start position:0%
you can find at
discord.gg<00:04:17.079><c> oama</c><00:04:18.079><c> thanks</c><00:04:18.359><c> so</c><00:04:18.519><c> much</c><00:04:18.720><c> for</c>

00:04:18.909 --> 00:04:18.919 align:start position:0%
discord.gg oama thanks so much for
 

00:04:18.919 --> 00:04:28.590 align:start position:0%
discord.gg oama thanks so much for
watching

00:04:28.590 --> 00:04:28.600 align:start position:0%
 
 

00:04:28.600 --> 00:04:31.600 align:start position:0%
 
goodbye

