WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.589 align:start position:0%
 
it's<00:00:00.120><c> just</c><00:00:00.320><c> Thursday</c><00:00:00.799><c> and</c><00:00:00.919><c> we</c><00:00:01.040><c> have</c><00:00:01.160><c> a</c><00:00:01.280><c> lot</c><00:00:01.439><c> of</c>

00:00:01.589 --> 00:00:01.599 align:start position:0%
it's just Thursday and we have a lot of
 

00:00:01.599 --> 00:00:03.550 align:start position:0%
it's just Thursday and we have a lot of
interesting<00:00:02.000><c> AI</c><00:00:02.320><c> news</c><00:00:02.560><c> to</c><00:00:02.720><c> talk</c><00:00:02.879><c> about</c><00:00:03.199><c> I've</c>

00:00:03.550 --> 00:00:03.560 align:start position:0%
interesting AI news to talk about I've
 

00:00:03.560 --> 00:00:05.710 align:start position:0%
interesting AI news to talk about I've
handpicked<00:00:04.240><c> six</c><00:00:04.520><c> news</c><00:00:04.880><c> for</c><00:00:05.040><c> us</c><00:00:05.160><c> to</c><00:00:05.359><c> quickly</c>

00:00:05.710 --> 00:00:05.720 align:start position:0%
handpicked six news for us to quickly
 

00:00:05.720 --> 00:00:07.670 align:start position:0%
handpicked six news for us to quickly
discuss<00:00:06.120><c> and</c><00:00:06.720><c> understand</c><00:00:07.120><c> what</c><00:00:07.240><c> are</c><00:00:07.359><c> the</c><00:00:07.520><c> new</c>

00:00:07.670 --> 00:00:07.680 align:start position:0%
discuss and understand what are the new
 

00:00:07.680 --> 00:00:10.990 align:start position:0%
discuss and understand what are the new
things<00:00:08.280><c> the</c><00:00:08.480><c> first</c><00:00:08.719><c> one</c><00:00:09.080><c> is</c><00:00:09.320><c> ideogram</c><00:00:10.000><c> 2.0</c>

00:00:10.990 --> 00:00:11.000 align:start position:0%
things the first one is ideogram 2.0
 

00:00:11.000 --> 00:00:13.669 align:start position:0%
things the first one is ideogram 2.0
ideogram<00:00:11.799><c> was</c><00:00:12.120><c> already</c><00:00:12.599><c> the</c><00:00:12.840><c> best</c><00:00:13.280><c> text</c>

00:00:13.669 --> 00:00:13.679 align:start position:0%
ideogram was already the best text
 

00:00:13.679 --> 00:00:16.310 align:start position:0%
ideogram was already the best text
rendering<00:00:14.360><c> AI</c><00:00:14.799><c> image</c><00:00:15.120><c> generation</c><00:00:15.599><c> model</c><00:00:16.199><c> what</c>

00:00:16.310 --> 00:00:16.320 align:start position:0%
rendering AI image generation model what
 

00:00:16.320 --> 00:00:18.429 align:start position:0%
rendering AI image generation model what
do<00:00:16.440><c> I</c><00:00:16.560><c> mean</c><00:00:17.039><c> so</c><00:00:17.199><c> if</c><00:00:17.279><c> you</c><00:00:17.400><c> have</c><00:00:17.520><c> got</c><00:00:17.680><c> an</c><00:00:17.880><c> AI</c><00:00:18.119><c> M</c>

00:00:18.429 --> 00:00:18.439 align:start position:0%
do I mean so if you have got an AI M
 

00:00:18.439 --> 00:00:20.790 align:start position:0%
do I mean so if you have got an AI M
generation<00:00:18.960><c> model</c><00:00:19.480><c> and</c><00:00:19.640><c> you</c><00:00:19.800><c> need</c><00:00:20.240><c> text</c>

00:00:20.790 --> 00:00:20.800 align:start position:0%
generation model and you need text
 

00:00:20.800 --> 00:00:23.109 align:start position:0%
generation model and you need text
inside<00:00:21.279><c> that</c><00:00:21.680><c> this</c><00:00:21.840><c> was</c><00:00:22.080><c> always</c><00:00:22.359><c> a</c><00:00:22.519><c> channel</c>

00:00:23.109 --> 00:00:23.119 align:start position:0%
inside that this was always a channel
 

00:00:23.119 --> 00:00:25.390 align:start position:0%
inside that this was always a channel
challenge<00:00:23.840><c> but</c><00:00:24.080><c> ideogram</c><00:00:24.720><c> was</c><00:00:24.960><c> always</c><00:00:25.199><c> the</c>

00:00:25.390 --> 00:00:25.400 align:start position:0%
challenge but ideogram was always the
 

00:00:25.400 --> 00:00:27.669 align:start position:0%
challenge but ideogram was always the
best<00:00:25.840><c> until</c><00:00:26.119><c> flux</c><00:00:26.560><c> came</c><00:00:26.720><c> in</c><00:00:27.160><c> but</c><00:00:27.320><c> now</c><00:00:27.519><c> what</c>

00:00:27.669 --> 00:00:27.679 align:start position:0%
best until flux came in but now what
 

00:00:27.679 --> 00:00:29.429 align:start position:0%
best until flux came in but now what
ideogram<00:00:28.279><c> has</c><00:00:28.439><c> also</c><00:00:28.640><c> done</c><00:00:28.880><c> is</c><00:00:29.240><c> they've</c>

00:00:29.429 --> 00:00:29.439 align:start position:0%
ideogram has also done is they've
 

00:00:29.439 --> 00:00:30.669 align:start position:0%
ideogram has also done is they've
released<00:00:29.759><c> a</c><00:00:29.800><c> new</c><00:00:29.960><c> newer</c><00:00:30.199><c> version</c><00:00:30.439><c> of</c><00:00:30.560><c> the</c>

00:00:30.669 --> 00:00:30.679 align:start position:0%
released a new newer version of the
 

00:00:30.679 --> 00:00:33.470 align:start position:0%
released a new newer version of the
model<00:00:31.320><c> which</c><00:00:31.519><c> has</c><00:00:31.720><c> got</c><00:00:32.040><c> the</c><00:00:32.559><c> most</c><00:00:33.000><c> advanced</c>

00:00:33.470 --> 00:00:33.480 align:start position:0%
model which has got the most advanced
 

00:00:33.480 --> 00:00:36.110 align:start position:0%
model which has got the most advanced
text<00:00:33.680><c> to</c><00:00:33.879><c> image</c><00:00:34.200><c> model</c><00:00:35.200><c> they're</c><00:00:35.399><c> making</c><00:00:35.719><c> this</c>

00:00:36.110 --> 00:00:36.120 align:start position:0%
text to image model they're making this
 

00:00:36.120 --> 00:00:38.150 align:start position:0%
text to image model they're making this
available<00:00:36.559><c> for</c><00:00:36.760><c> free</c><00:00:37.040><c> for</c><00:00:37.239><c> all</c><00:00:37.440><c> their</c><00:00:37.640><c> users</c>

00:00:38.150 --> 00:00:38.160 align:start position:0%
available for free for all their users
 

00:00:38.160 --> 00:00:39.950 align:start position:0%
available for free for all their users
at<00:00:38.320><c> this</c><00:00:38.520><c> point</c><00:00:38.920><c> so</c><00:00:39.079><c> you</c><00:00:39.200><c> can</c><00:00:39.360><c> go</c><00:00:39.520><c> see</c><00:00:39.760><c> their</c>

00:00:39.950 --> 00:00:39.960 align:start position:0%
at this point so you can go see their
 

00:00:39.960 --> 00:00:41.670 align:start position:0%
at this point so you can go see their
demo<00:00:40.239><c> it's</c><00:00:40.399><c> pretty</c><00:00:40.680><c> interesting</c><00:00:41.239><c> about</c><00:00:41.520><c> what</c>

00:00:41.670 --> 00:00:41.680 align:start position:0%
demo it's pretty interesting about what
 

00:00:41.680 --> 00:00:43.029 align:start position:0%
demo it's pretty interesting about what
kind<00:00:41.800><c> of</c><00:00:41.920><c> things</c><00:00:42.120><c> that</c><00:00:42.239><c> they're</c><00:00:42.440><c> doing</c><00:00:42.920><c> we</c>

00:00:43.029 --> 00:00:43.039 align:start position:0%
kind of things that they're doing we
 

00:00:43.039 --> 00:00:44.350 align:start position:0%
kind of things that they're doing we
don't<00:00:43.200><c> have</c><00:00:43.320><c> a</c><00:00:43.440><c> lot</c><00:00:43.559><c> of</c><00:00:43.680><c> information</c><00:00:44.120><c> about</c>

00:00:44.350 --> 00:00:44.360 align:start position:0%
don't have a lot of information about
 

00:00:44.360 --> 00:00:46.709 align:start position:0%
don't have a lot of information about
their<00:00:44.559><c> Technologies</c><00:00:45.480><c> but</c><00:00:45.800><c> this</c><00:00:46.000><c> is</c><00:00:46.280><c> probably</c>

00:00:46.709 --> 00:00:46.719 align:start position:0%
their Technologies but this is probably
 

00:00:46.719 --> 00:00:50.150 align:start position:0%
their Technologies but this is probably
the<00:00:47.039><c> closest</c><00:00:48.039><c> reality</c><00:00:48.960><c> or</c><00:00:49.239><c> realism</c><00:00:49.800><c> looking</c>

00:00:50.150 --> 00:00:50.160 align:start position:0%
the closest reality or realism looking
 

00:00:50.160 --> 00:00:51.790 align:start position:0%
the closest reality or realism looking
model<00:00:50.719><c> to</c><00:00:50.879><c> the</c><00:00:51.000><c> best</c><00:00:51.239><c> model</c><00:00:51.480><c> that</c><00:00:51.600><c> is</c>

00:00:51.790 --> 00:00:51.800 align:start position:0%
model to the best model that is
 

00:00:51.800 --> 00:00:54.830 align:start position:0%
model to the best model that is
available<00:00:52.800><c> the</c><00:00:53.000><c> second</c><00:00:53.359><c> one</c><00:00:53.719><c> is</c><00:00:54.039><c> anthropic</c>

00:00:54.830 --> 00:00:54.840 align:start position:0%
available the second one is anthropic
 

00:00:54.840 --> 00:00:56.709 align:start position:0%
available the second one is anthropic
has<00:00:55.239><c> launched</c><00:00:55.600><c> a</c><00:00:55.719><c> bunch</c><00:00:55.920><c> of</c><00:00:56.160><c> interesting</c>

00:00:56.709 --> 00:00:56.719 align:start position:0%
has launched a bunch of interesting
 

00:00:56.719 --> 00:00:58.990 align:start position:0%
has launched a bunch of interesting
courses<00:00:57.480><c> so</c><00:00:57.640><c> they've</c><00:00:57.879><c> launched</c><00:00:58.239><c> four</c><00:00:58.559><c> courses</c>

00:00:58.990 --> 00:00:59.000 align:start position:0%
courses so they've launched four courses
 

00:00:59.000 --> 00:01:01.470 align:start position:0%
courses so they've launched four courses
so<00:00:59.199><c> far</c><00:00:59.600><c> so</c><00:01:00.000><c> they</c><00:01:00.079><c> have</c><00:01:00.199><c> an</c><00:01:00.359><c> anthropic</c><00:01:01.000><c> APA</c>

00:01:01.470 --> 00:01:01.480 align:start position:0%
so far so they have an anthropic APA
 

00:01:01.480 --> 00:01:03.229 align:start position:0%
so far so they have an anthropic APA
fundamental<00:01:02.000><c> course</c><00:01:02.559><c> they</c><00:01:02.719><c> have</c><00:01:02.800><c> a</c><00:01:02.960><c> prompt</c>

00:01:03.229 --> 00:01:03.239 align:start position:0%
fundamental course they have a prompt
 

00:01:03.239 --> 00:01:05.310 align:start position:0%
fundamental course they have a prompt
engineering<00:01:03.760><c> interactive</c><00:01:04.239><c> tutorial</c><00:01:04.680><c> course</c>

00:01:05.310 --> 00:01:05.320 align:start position:0%
engineering interactive tutorial course
 

00:01:05.320 --> 00:01:07.710 align:start position:0%
engineering interactive tutorial course
real<00:01:05.640><c> world</c><00:01:05.960><c> prompting</c><00:01:06.360><c> course</c><00:01:06.960><c> tool</c><00:01:07.400><c> use</c>

00:01:07.710 --> 00:01:07.720 align:start position:0%
real world prompting course tool use
 

00:01:07.720 --> 00:01:10.429 align:start position:0%
real world prompting course tool use
course<00:01:08.400><c> out</c><00:01:08.560><c> of</c><00:01:08.799><c> these</c><00:01:09.439><c> the</c><00:01:09.600><c> second</c><00:01:09.880><c> and</c><00:01:10.119><c> third</c>

00:01:10.429 --> 00:01:10.439 align:start position:0%
course out of these the second and third
 

00:01:10.439 --> 00:01:12.390 align:start position:0%
course out of these the second and third
are<00:01:10.720><c> completely</c><00:01:11.240><c> new</c><00:01:11.640><c> just</c><00:01:11.799><c> got</c><00:01:12.000><c> launched</c><00:01:12.320><c> a</c>

00:01:12.390 --> 00:01:12.400 align:start position:0%
are completely new just got launched a
 

00:01:12.400 --> 00:01:15.070 align:start position:0%
are completely new just got launched a
couple<00:01:12.600><c> of</c><00:01:12.759><c> hours</c><00:01:13.040><c> or</c><00:01:13.280><c> days</c><00:01:13.600><c> back</c><00:01:14.600><c> the</c><00:01:14.759><c> prompt</c>

00:01:15.070 --> 00:01:15.080 align:start position:0%
couple of hours or days back the prompt
 

00:01:15.080 --> 00:01:17.550 align:start position:0%
couple of hours or days back the prompt
engineering<00:01:15.960><c> interactive</c><00:01:16.479><c> tutorial</c><00:01:17.280><c> will</c>

00:01:17.550 --> 00:01:17.560 align:start position:0%
engineering interactive tutorial will
 

00:01:17.560 --> 00:01:19.670 align:start position:0%
engineering interactive tutorial will
help<00:01:17.759><c> you</c><00:01:18.360><c> understand</c><00:01:18.759><c> how</c><00:01:18.920><c> to</c><00:01:19.159><c> design</c><00:01:19.520><c> the</c>

00:01:19.670 --> 00:01:19.680 align:start position:0%
help you understand how to design the
 

00:01:19.680 --> 00:01:21.670 align:start position:0%
help you understand how to design the
basic<00:01:20.079><c> prompt</c><00:01:20.680><c> it</c><00:01:20.799><c> has</c><00:01:21.000><c> all</c><00:01:21.200><c> the</c><00:01:21.320><c> things</c><00:01:21.520><c> so</c>

00:01:21.670 --> 00:01:21.680 align:start position:0%
basic prompt it has all the things so
 

00:01:21.680 --> 00:01:23.469 align:start position:0%
basic prompt it has all the things so
you<00:01:21.759><c> can</c><00:01:22.000><c> go</c><00:01:22.280><c> here</c><00:01:22.520><c> and</c><00:01:22.680><c> then</c><00:01:22.840><c> start</c><00:01:23.119><c> learning</c>

00:01:23.469 --> 00:01:23.479 align:start position:0%
you can go here and then start learning
 

00:01:23.479 --> 00:01:25.270 align:start position:0%
you can go here and then start learning
it<00:01:23.680><c> so</c><00:01:24.119><c> all</c><00:01:24.240><c> you</c><00:01:24.360><c> have</c><00:01:24.479><c> to</c><00:01:24.640><c> do</c><00:01:24.799><c> is</c><00:01:24.920><c> you</c><00:01:25.000><c> have</c><00:01:25.119><c> to</c>

00:01:25.270 --> 00:01:25.280 align:start position:0%
it so all you have to do is you have to
 

00:01:25.280 --> 00:01:27.310 align:start position:0%
it so all you have to do is you have to
click<00:01:25.600><c> this</c><00:01:25.960><c> and</c><00:01:26.159><c> then</c><00:01:26.400><c> you</c><00:01:26.520><c> will</c><00:01:26.720><c> be</c><00:01:26.920><c> able</c><00:01:27.119><c> to</c>

00:01:27.310 --> 00:01:27.320 align:start position:0%
click this and then you will be able to
 

00:01:27.320 --> 00:01:29.469 align:start position:0%
click this and then you will be able to
start<00:01:27.799><c> learning</c><00:01:28.479><c> The</c><00:01:28.680><c> Prompt</c><00:01:29.000><c> engineering</c>

00:01:29.469 --> 00:01:29.479 align:start position:0%
start learning The Prompt engineering
 

00:01:29.479 --> 00:01:31.910 align:start position:0%
start learning The Prompt engineering
course<00:01:30.040><c> that</c><00:01:30.240><c> anthropic</c><00:01:30.920><c> has</c><00:01:31.119><c> put</c><00:01:31.439><c> together</c>

00:01:31.910 --> 00:01:31.920 align:start position:0%
course that anthropic has put together
 

00:01:31.920 --> 00:01:33.870 align:start position:0%
course that anthropic has put together
and<00:01:32.079><c> also</c><00:01:32.280><c> if</c><00:01:32.360><c> you</c><00:01:32.520><c> want</c><00:01:32.680><c> to</c><00:01:33.119><c> use</c><00:01:33.680><c> the</c>

00:01:33.870 --> 00:01:33.880 align:start position:0%
and also if you want to use the
 

00:01:33.880 --> 00:01:35.950 align:start position:0%
and also if you want to use the
prompting<00:01:34.560><c> much</c><00:01:34.840><c> Beyond</c><00:01:35.439><c> then</c><00:01:35.680><c> what</c><00:01:35.799><c> the</c>

00:01:35.950 --> 00:01:35.960 align:start position:0%
prompting much Beyond then what the
 

00:01:35.960 --> 00:01:37.310 align:start position:0%
prompting much Beyond then what the
basic<00:01:36.240><c> one</c><00:01:36.439><c> is</c><00:01:36.600><c> for</c><00:01:36.720><c> example</c><00:01:37.040><c> you</c><00:01:37.119><c> are</c><00:01:37.200><c> a</c>

00:01:37.310 --> 00:01:37.320 align:start position:0%
basic one is for example you are a
 

00:01:37.320 --> 00:01:38.990 align:start position:0%
basic one is for example you are a
developer<00:01:37.720><c> and</c><00:01:37.880><c> you</c><00:01:38.000><c> want</c><00:01:38.119><c> to</c><00:01:38.200><c> use</c><00:01:38.399><c> it</c><00:01:38.880><c> then</c>

00:01:38.990 --> 00:01:39.000 align:start position:0%
developer and you want to use it then
 

00:01:39.000 --> 00:01:41.190 align:start position:0%
developer and you want to use it then
you<00:01:39.119><c> can</c><00:01:39.320><c> go</c><00:01:39.479><c> ahead</c><00:01:39.720><c> with</c><00:01:39.880><c> your</c><00:01:40.360><c> real</c><00:01:40.720><c> world</c>

00:01:41.190 --> 00:01:41.200 align:start position:0%
you can go ahead with your real world
 

00:01:41.200 --> 00:01:43.310 align:start position:0%
you can go ahead with your real world
prompt<00:01:41.799><c> prompting</c><00:01:42.240><c> course</c><00:01:42.799><c> and</c><00:01:42.920><c> you</c><00:01:43.000><c> have</c><00:01:43.159><c> got</c>

00:01:43.310 --> 00:01:43.320 align:start position:0%
prompt prompting course and you have got
 

00:01:43.320 --> 00:01:45.270 align:start position:0%
prompt prompting course and you have got
a<00:01:43.399><c> bunch</c><00:01:43.640><c> of</c><00:01:43.799><c> information</c><00:01:44.280><c> like</c><00:01:44.399><c> for</c><00:01:44.560><c> example</c>

00:01:45.270 --> 00:01:45.280 align:start position:0%
a bunch of information like for example
 

00:01:45.280 --> 00:01:47.069 align:start position:0%
a bunch of information like for example
medical<00:01:45.759><c> prompt</c><00:01:46.159><c> walk</c><00:01:46.479><c> through</c><00:01:46.680><c> so</c><00:01:46.799><c> you</c><00:01:46.880><c> can</c>

00:01:47.069 --> 00:01:47.079 align:start position:0%
medical prompt walk through so you can
 

00:01:47.079 --> 00:01:48.870 align:start position:0%
medical prompt walk through so you can
go<00:01:47.320><c> here</c><00:01:47.479><c> and</c><00:01:47.680><c> then</c><00:01:47.840><c> see</c><00:01:48.439><c> how</c><00:01:48.640><c> they've</c>

00:01:48.870 --> 00:01:48.880 align:start position:0%
go here and then see how they've
 

00:01:48.880 --> 00:01:50.310 align:start position:0%
go here and then see how they've
designed<00:01:49.200><c> the</c><00:01:49.360><c> prompt</c><00:01:49.680><c> and</c><00:01:49.880><c> what</c><00:01:50.000><c> kind</c><00:01:50.159><c> of</c>

00:01:50.310 --> 00:01:50.320 align:start position:0%
designed the prompt and what kind of
 

00:01:50.320 --> 00:01:51.830 align:start position:0%
designed the prompt and what kind of
things<00:01:50.479><c> are</c><00:01:50.759><c> there</c><00:01:50.920><c> and</c><00:01:51.079><c> you</c><00:01:51.159><c> can</c><00:01:51.320><c> see</c><00:01:51.520><c> all</c><00:01:51.680><c> the</c>

00:01:51.830 --> 00:01:51.840 align:start position:0%
things are there and you can see all the
 

00:01:51.840 --> 00:01:53.590 align:start position:0%
things are there and you can see all the
details<00:01:52.320><c> there</c><00:01:52.960><c> it's</c><00:01:53.200><c> it's</c><00:01:53.320><c> a</c><00:01:53.439><c> very</c>

00:01:53.590 --> 00:01:53.600 align:start position:0%
details there it's it's a very
 

00:01:53.600 --> 00:01:55.749 align:start position:0%
details there it's it's a very
interesting<00:01:54.079><c> to</c><00:01:54.200><c> see</c><00:01:54.640><c> how</c><00:01:55.079><c> um</c><00:01:55.280><c> you</c><00:01:55.399><c> know</c><00:01:55.560><c> let's</c>

00:01:55.749 --> 00:01:55.759 align:start position:0%
interesting to see how um you know let's
 

00:01:55.759 --> 00:01:57.870 align:start position:0%
interesting to see how um you know let's
say<00:01:56.240><c> companies</c><00:01:56.719><c> like</c><00:01:56.920><c> these</c><00:01:57.240><c> actually</c><00:01:57.560><c> build</c>

00:01:57.870 --> 00:01:57.880 align:start position:0%
say companies like these actually build
 

00:01:57.880 --> 00:01:59.950 align:start position:0%
say companies like these actually build
the<00:01:58.039><c> prompts</c><00:01:58.439><c> definitely</c><00:01:58.799><c> check</c><00:01:59.000><c> out</c><00:01:59.680><c> the</c><00:01:59.880><c> the</c>

00:01:59.950 --> 00:01:59.960 align:start position:0%
the prompts definitely check out the the
 

00:01:59.960 --> 00:02:03.389 align:start position:0%
the prompts definitely check out the the
third<00:02:00.200><c> one</c><00:02:00.399><c> is</c><00:02:00.960><c> a</c><00:02:01.200><c> new</c><00:02:01.719><c> text</c><00:02:02.159><c> to</c><00:02:02.640><c> video</c>

00:02:03.389 --> 00:02:03.399 align:start position:0%
third one is a new text to video
 

00:02:03.399 --> 00:02:05.190 align:start position:0%
third one is a new text to video
application<00:02:04.240><c> so</c><00:02:04.439><c> we've</c><00:02:04.680><c> got</c><00:02:04.799><c> a</c><00:02:04.880><c> bunch</c><00:02:05.039><c> of</c>

00:02:05.190 --> 00:02:05.200 align:start position:0%
application so we've got a bunch of
 

00:02:05.200 --> 00:02:06.350 align:start position:0%
application so we've got a bunch of
applications<00:02:05.640><c> like</c><00:02:05.799><c> this</c><00:02:05.920><c> in</c><00:02:06.039><c> the</c><00:02:06.119><c> same</c>

00:02:06.350 --> 00:02:06.360 align:start position:0%
applications like this in the same
 

00:02:06.360 --> 00:02:09.949 align:start position:0%
applications like this in the same
category<00:02:06.719><c> we've</c><00:02:06.920><c> got</c><00:02:07.079><c> like</c><00:02:07.280><c> gen</c><00:02:08.280><c> um</c><00:02:09.200><c> Runway</c><00:02:09.840><c> uh</c>

00:02:09.949 --> 00:02:09.959 align:start position:0%
category we've got like gen um Runway uh
 

00:02:09.959 --> 00:02:12.470 align:start position:0%
category we've got like gen um Runway uh
Runway<00:02:10.759><c> gen</c><00:02:11.039><c> model</c><00:02:11.520><c> and</c><00:02:11.800><c> we</c><00:02:11.920><c> have</c><00:02:12.080><c> got</c><00:02:12.200><c> a</c><00:02:12.280><c> bunch</c>

00:02:12.470 --> 00:02:12.480 align:start position:0%
Runway gen model and we have got a bunch
 

00:02:12.480 --> 00:02:14.550 align:start position:0%
Runway gen model and we have got a bunch
of<00:02:12.640><c> other</c><00:02:12.800><c> models</c><00:02:13.160><c> in</c><00:02:13.280><c> the</c><00:02:13.440><c> same</c><00:02:13.640><c> thing</c><00:02:14.239><c> open</c>

00:02:14.550 --> 00:02:14.560 align:start position:0%
of other models in the same thing open
 

00:02:14.560 --> 00:02:17.190 align:start position:0%
of other models in the same thing open
AI<00:02:14.879><c> Sora</c><00:02:15.400><c> has</c><00:02:15.560><c> never</c><00:02:15.879><c> seen</c><00:02:16.400><c> the</c><00:02:16.519><c> day</c><00:02:16.680><c> of</c><00:02:16.879><c> light</c>

00:02:17.190 --> 00:02:17.200 align:start position:0%
AI Sora has never seen the day of light
 

00:02:17.200 --> 00:02:19.430 align:start position:0%
AI Sora has never seen the day of light
or<00:02:17.440><c> light</c><00:02:17.640><c> of</c><00:02:17.840><c> day</c><00:02:18.400><c> but</c><00:02:18.560><c> if</c><00:02:18.680><c> you</c><00:02:18.800><c> see</c><00:02:19.080><c> this</c><00:02:19.239><c> is</c>

00:02:19.430 --> 00:02:19.440 align:start position:0%
or light of day but if you see this is
 

00:02:19.440 --> 00:02:20.710 align:start position:0%
or light of day but if you see this is
this<00:02:19.519><c> is</c><00:02:19.680><c> quite</c><00:02:19.879><c> realistic</c><00:02:20.519><c> and</c><00:02:20.599><c> the</c>

00:02:20.710 --> 00:02:20.720 align:start position:0%
this is quite realistic and the
 

00:02:20.720 --> 00:02:22.430 align:start position:0%
this is quite realistic and the
surprising<00:02:21.200><c> thing</c><00:02:21.400><c> here</c><00:02:21.519><c> is</c><00:02:21.680><c> that</c><00:02:22.160><c> this</c><00:02:22.280><c> is</c>

00:02:22.430 --> 00:02:22.440 align:start position:0%
surprising thing here is that this is
 

00:02:22.440 --> 00:02:24.270 align:start position:0%
surprising thing here is that this is
just<00:02:22.599><c> a</c><00:02:22.760><c> team</c><00:02:22.959><c> of</c><00:02:23.080><c> four</c><00:02:23.280><c> or</c><00:02:23.480><c> five</c><00:02:23.720><c> people</c><00:02:24.080><c> and</c>

00:02:24.270 --> 00:02:24.280 align:start position:0%
just a team of four or five people and
 

00:02:24.280 --> 00:02:26.110 align:start position:0%
just a team of four or five people and
look<00:02:24.440><c> at</c><00:02:24.599><c> the</c><00:02:24.840><c> realism</c><00:02:25.400><c> look</c><00:02:25.519><c> at</c><00:02:25.640><c> the</c><00:02:25.720><c> images</c>

00:02:26.110 --> 00:02:26.120 align:start position:0%
look at the realism look at the images
 

00:02:26.120 --> 00:02:27.830 align:start position:0%
look at the realism look at the images
that<00:02:26.200><c> they've</c><00:02:26.400><c> designed</c><00:02:27.080><c> I</c><00:02:27.200><c> think</c><00:02:27.440><c> right</c><00:02:27.599><c> now</c>

00:02:27.830 --> 00:02:27.840 align:start position:0%
that they've designed I think right now
 

00:02:27.840 --> 00:02:30.270 align:start position:0%
that they've designed I think right now
it's<00:02:28.120><c> just</c><00:02:28.319><c> like</c><00:02:28.519><c> you</c><00:02:28.680><c> have</c><00:02:28.920><c> got</c><00:02:29.319><c> only</c><00:02:29.599><c> two</c><00:02:29.920><c> two</c>

00:02:30.270 --> 00:02:30.280 align:start position:0%
it's just like you have got only two two
 

00:02:30.280 --> 00:02:32.949 align:start position:0%
it's just like you have got only two two
um<00:02:30.480><c> options</c><00:02:31.120><c> two</c><00:02:31.599><c> credits</c><00:02:32.080><c> left</c><00:02:32.400><c> every</c><00:02:32.640><c> single</c>

00:02:32.949 --> 00:02:32.959 align:start position:0%
um options two credits left every single
 

00:02:32.959 --> 00:02:35.030 align:start position:0%
um options two credits left every single
day<00:02:33.560><c> but</c><00:02:33.760><c> you</c><00:02:33.879><c> don't</c><00:02:34.120><c> have</c><00:02:34.239><c> to</c><00:02:34.400><c> wait</c><00:02:34.800><c> uh</c><00:02:34.879><c> it's</c>

00:02:35.030 --> 00:02:35.040 align:start position:0%
day but you don't have to wait uh it's
 

00:02:35.040 --> 00:02:37.270 align:start position:0%
day but you don't have to wait uh it's
free<00:02:35.440><c> you</c><00:02:35.560><c> can</c><00:02:35.760><c> just</c><00:02:35.920><c> go</c><00:02:36.080><c> ahead</c><00:02:36.280><c> and</c><00:02:36.440><c> then</c><00:02:36.599><c> say</c>

00:02:37.270 --> 00:02:37.280 align:start position:0%
free you can just go ahead and then say
 

00:02:37.280 --> 00:02:40.270 align:start position:0%
free you can just go ahead and then say
let's<00:02:37.480><c> say</c><00:02:37.760><c> a</c><00:02:37.959><c> woman</c><00:02:38.760><c> uh</c><00:02:38.959><c> with</c><00:02:39.159><c> a</c>

00:02:40.270 --> 00:02:40.280 align:start position:0%
let's say a woman uh with a
 

00:02:40.280 --> 00:02:45.309 align:start position:0%
let's say a woman uh with a
computer<00:02:41.280><c> um</c><00:02:42.080><c> in</c><00:02:42.560><c> um</c><00:02:42.959><c> at</c><00:02:43.560><c> Starbucks</c><00:02:44.560><c> let's</c><00:02:44.760><c> see</c>

00:02:45.309 --> 00:02:45.319 align:start position:0%
computer um in um at Starbucks let's see
 

00:02:45.319 --> 00:02:46.790 align:start position:0%
computer um in um at Starbucks let's see
I've<00:02:45.519><c> just</c><00:02:45.640><c> tried</c><00:02:45.959><c> to</c><00:02:46.080><c> generate</c><00:02:46.519><c> this</c><00:02:46.680><c> it</c>

00:02:46.790 --> 00:02:46.800 align:start position:0%
I've just tried to generate this it
 

00:02:46.800 --> 00:02:48.869 align:start position:0%
I've just tried to generate this it
might<00:02:46.959><c> take</c><00:02:47.120><c> a</c><00:02:47.239><c> little</c><00:02:47.400><c> bit</c><00:02:47.560><c> of</c><00:02:47.760><c> time</c><00:02:48.599><c> but</c><00:02:48.760><c> the</c>

00:02:48.869 --> 00:02:48.879 align:start position:0%
might take a little bit of time but the
 

00:02:48.879 --> 00:02:51.630 align:start position:0%
might take a little bit of time but the
idea<00:02:49.200><c> here</c><00:02:49.360><c> is</c><00:02:49.519><c> that</c><00:02:50.040><c> you</c><00:02:50.200><c> can</c><00:02:50.640><c> generate</c>

00:02:51.630 --> 00:02:51.640 align:start position:0%
idea here is that you can generate
 

00:02:51.640 --> 00:02:54.910 align:start position:0%
idea here is that you can generate
videos<00:02:52.519><c> within</c><00:02:52.840><c> just</c><00:02:53.000><c> a</c><00:02:53.200><c> click</c><00:02:53.879><c> and</c><00:02:54.319><c> a</c><00:02:54.440><c> team</c><00:02:54.720><c> of</c>

00:02:54.910 --> 00:02:54.920 align:start position:0%
videos within just a click and a team of
 

00:02:54.920 --> 00:02:56.350 align:start position:0%
videos within just a click and a team of
five<00:02:55.280><c> creating</c><00:02:55.680><c> something</c><00:02:56.000><c> like</c><00:02:56.159><c> this</c>

00:02:56.350 --> 00:02:56.360 align:start position:0%
five creating something like this
 

00:02:56.360 --> 00:02:57.990 align:start position:0%
five creating something like this
completely<00:02:56.760><c> astonishing</c><00:02:57.280><c> imagine</c><00:02:57.599><c> like</c><00:02:57.760><c> open</c>

00:02:57.990 --> 00:02:58.000 align:start position:0%
completely astonishing imagine like open
 

00:02:58.000 --> 00:02:59.830 align:start position:0%
completely astonishing imagine like open
AI<00:02:58.319><c> with</c><00:02:58.760><c> all</c><00:02:58.879><c> the</c><00:02:59.000><c> fun</c><00:02:59.239><c> and</c><00:02:59.400><c> everything</c>

00:02:59.830 --> 00:02:59.840 align:start position:0%
AI with all the fun and everything
 

00:02:59.840 --> 00:03:01.110 align:start position:0%
AI with all the fun and everything
they've<00:03:00.040><c> done</c><00:03:00.239><c> we'll</c><00:03:00.480><c> come</c><00:03:00.640><c> back</c><00:03:00.760><c> to</c><00:03:00.920><c> this</c>

00:03:01.110 --> 00:03:01.120 align:start position:0%
they've done we'll come back to this
 

00:03:01.120 --> 00:03:03.470 align:start position:0%
they've done we'll come back to this
again<00:03:01.879><c> but</c><00:03:02.080><c> then</c><00:03:02.480><c> once</c><00:03:02.760><c> again</c><00:03:03.040><c> we</c><00:03:03.200><c> have</c>

00:03:03.470 --> 00:03:03.480 align:start position:0%
again but then once again we have
 

00:03:03.480 --> 00:03:06.190 align:start position:0%
again but then once again we have
another<00:03:04.239><c> Tex</c><00:03:04.560><c> to</c><00:03:04.760><c> video</c><00:03:05.159><c> model</c><00:03:05.680><c> which</c><00:03:05.799><c> is</c><00:03:06.040><c> a</c>

00:03:06.190 --> 00:03:06.200 align:start position:0%
another Tex to video model which is a
 

00:03:06.200 --> 00:03:09.070 align:start position:0%
another Tex to video model which is a
dream<00:03:06.560><c> machine</c><00:03:07.239><c> 1.5</c><00:03:08.239><c> this</c><00:03:08.360><c> is</c><00:03:08.519><c> the</c><00:03:08.720><c> newer</c>

00:03:09.070 --> 00:03:09.080 align:start position:0%
dream machine 1.5 this is the newer
 

00:03:09.080 --> 00:03:10.670 align:start position:0%
dream machine 1.5 this is the newer
version<00:03:09.400><c> of</c><00:03:09.599><c> dream</c><00:03:09.840><c> machine</c><00:03:10.200><c> that</c><00:03:10.360><c> already</c>

00:03:10.670 --> 00:03:10.680 align:start position:0%
version of dream machine that already
 

00:03:10.680 --> 00:03:13.949 align:start position:0%
version of dream machine that already
they<00:03:10.840><c> have</c><00:03:11.040><c> got</c><00:03:11.519><c> Luma</c><00:03:12.080><c> AI</c><00:03:12.519><c> Labs</c><00:03:13.040><c> is</c><00:03:13.440><c> where</c><00:03:13.840><c> the</c>

00:03:13.949 --> 00:03:13.959 align:start position:0%
they have got Luma AI Labs is where the
 

00:03:13.959 --> 00:03:15.630 align:start position:0%
they have got Luma AI Labs is where the
dream<00:03:14.200><c> machine</c><00:03:14.519><c> you</c><00:03:14.640><c> can</c><00:03:14.760><c> just</c><00:03:14.959><c> give</c><00:03:15.120><c> an</c><00:03:15.280><c> image</c>

00:03:15.630 --> 00:03:15.640 align:start position:0%
dream machine you can just give an image
 

00:03:15.640 --> 00:03:18.390 align:start position:0%
dream machine you can just give an image
and<00:03:15.840><c> it</c><00:03:15.959><c> can</c><00:03:16.200><c> create</c><00:03:16.680><c> a</c><00:03:16.879><c> video</c><00:03:17.239><c> out</c><00:03:17.400><c> of</c><00:03:17.560><c> it</c><00:03:18.280><c> this</c>

00:03:18.390 --> 00:03:18.400 align:start position:0%
and it can create a video out of it this
 

00:03:18.400 --> 00:03:21.350 align:start position:0%
and it can create a video out of it this
is<00:03:18.959><c> astonishing</c><00:03:19.680><c> people</c><00:03:20.000><c> have</c><00:03:20.200><c> gone</c><00:03:20.519><c> Bonkers</c>

00:03:21.350 --> 00:03:21.360 align:start position:0%
is astonishing people have gone Bonkers
 

00:03:21.360 --> 00:03:23.309 align:start position:0%
is astonishing people have gone Bonkers
completely<00:03:21.840><c> crazy</c><00:03:22.200><c> with</c><00:03:22.400><c> this</c><00:03:22.680><c> so</c><00:03:23.040><c> all</c><00:03:23.200><c> you</c>

00:03:23.309 --> 00:03:23.319 align:start position:0%
completely crazy with this so all you
 

00:03:23.319 --> 00:03:25.630 align:start position:0%
completely crazy with this so all you
have<00:03:23.440><c> to</c><00:03:23.560><c> do</c><00:03:23.720><c> is</c><00:03:23.920><c> go</c><00:03:24.239><c> give</c><00:03:24.480><c> an</c><00:03:24.640><c> input</c><00:03:24.959><c> image</c><00:03:25.480><c> or</c>

00:03:25.630 --> 00:03:25.640 align:start position:0%
have to do is go give an input image or
 

00:03:25.640 --> 00:03:28.110 align:start position:0%
have to do is go give an input image or
it<00:03:25.799><c> Tak</c><00:03:26.080><c> prompt</c><00:03:26.640><c> and</c><00:03:26.799><c> then</c><00:03:27.000><c> it</c><00:03:27.159><c> will</c><00:03:27.480><c> start</c>

00:03:28.110 --> 00:03:28.120 align:start position:0%
it Tak prompt and then it will start
 

00:03:28.120 --> 00:03:29.470 align:start position:0%
it Tak prompt and then it will start
animating<00:03:28.599><c> it</c><00:03:28.760><c> it</c><00:03:28.840><c> will</c><00:03:28.959><c> start</c><00:03:29.159><c> creating</c>

00:03:29.470 --> 00:03:29.480 align:start position:0%
animating it it will start creating
 

00:03:29.480 --> 00:03:31.429 align:start position:0%
animating it it will start creating
video<00:03:29.959><c> out</c><00:03:30.120><c> of</c><00:03:30.239><c> it</c><00:03:30.720><c> and</c><00:03:31.000><c> they've</c><00:03:31.200><c> got</c><00:03:31.360><c> the</c>

00:03:31.429 --> 00:03:31.439 align:start position:0%
video out of it and they've got the
 

00:03:31.439 --> 00:03:33.229 align:start position:0%
video out of it and they've got the
newer<00:03:31.760><c> version</c><00:03:32.040><c> of</c><00:03:32.159><c> it</c><00:03:32.360><c> and</c><00:03:32.599><c> Al</c><00:03:32.840><c> they</c><00:03:33.000><c> also</c>

00:03:33.229 --> 00:03:33.239 align:start position:0%
newer version of it and Al they also
 

00:03:33.239 --> 00:03:34.830 align:start position:0%
newer version of it and Al they also
have<00:03:33.400><c> some</c><00:03:33.560><c> free</c><00:03:33.799><c> credits</c><00:03:34.200><c> for</c><00:03:34.360><c> you</c><00:03:34.439><c> to</c><00:03:34.640><c> try</c>

00:03:34.830 --> 00:03:34.840 align:start position:0%
have some free credits for you to try
 

00:03:34.840 --> 00:03:36.910 align:start position:0%
have some free credits for you to try
out<00:03:35.040><c> every</c><00:03:35.239><c> single</c><00:03:35.560><c> day</c><00:03:36.120><c> definitely</c><00:03:36.599><c> try</c><00:03:36.840><c> it</c>

00:03:36.910 --> 00:03:36.920 align:start position:0%
out every single day definitely try it
 

00:03:36.920 --> 00:03:38.350 align:start position:0%
out every single day definitely try it
out<00:03:37.080><c> if</c><00:03:37.159><c> you</c><00:03:37.239><c> have</c><00:03:37.360><c> not</c><00:03:37.480><c> tried</c><00:03:37.760><c> out</c><00:03:37.920><c> Luma</c><00:03:38.280><c> you</c>

00:03:38.350 --> 00:03:38.360 align:start position:0%
out if you have not tried out Luma you
 

00:03:38.360 --> 00:03:40.350 align:start position:0%
out if you have not tried out Luma you
should<00:03:38.519><c> definitely</c><00:03:38.959><c> try</c><00:03:39.159><c> out</c><00:03:39.720><c> this</c><00:03:39.840><c> is</c><00:03:40.120><c> quite</c>

00:03:40.350 --> 00:03:40.360 align:start position:0%
should definitely try out this is quite
 

00:03:40.360 --> 00:03:42.750 align:start position:0%
should definitely try out this is quite
insane<00:03:40.720><c> and</c><00:03:41.000><c> amazing</c><00:03:42.000><c> the</c><00:03:42.120><c> other</c><00:03:42.280><c> two</c><00:03:42.480><c> news</c>

00:03:42.750 --> 00:03:42.760 align:start position:0%
insane and amazing the other two news
 

00:03:42.760 --> 00:03:44.470 align:start position:0%
insane and amazing the other two news
are<00:03:42.959><c> something</c><00:03:43.239><c> that</c><00:03:43.360><c> we</c><00:03:43.439><c> have</c><00:03:43.560><c> already</c><00:03:43.799><c> seen</c>

00:03:44.470 --> 00:03:44.480 align:start position:0%
are something that we have already seen
 

00:03:44.480 --> 00:03:46.949 align:start position:0%
are something that we have already seen
one<00:03:44.640><c> is</c><00:03:45.040><c> the</c><00:03:45.200><c> GPD</c><00:03:45.760><c> 40</c><00:03:46.120><c> fine</c><00:03:46.400><c> tuning</c><00:03:46.720><c> is</c>

00:03:46.949 --> 00:03:46.959 align:start position:0%
one is the GPD 40 fine tuning is
 

00:03:46.959 --> 00:03:49.229 align:start position:0%
one is the GPD 40 fine tuning is
available<00:03:47.400><c> so</c><00:03:47.680><c> if</c><00:03:47.760><c> you</c><00:03:47.879><c> were</c><00:03:48.080><c> to</c><00:03:48.680><c> improve</c><00:03:49.120><c> the</c>

00:03:49.229 --> 00:03:49.239 align:start position:0%
available so if you were to improve the
 

00:03:49.239 --> 00:03:50.990 align:start position:0%
available so if you were to improve the
steerability<00:03:49.640><c> of</c><00:03:50.000><c> the</c><00:03:50.120><c> model</c><00:03:50.400><c> if</c><00:03:50.480><c> you</c><00:03:50.599><c> were</c><00:03:50.799><c> to</c>

00:03:50.990 --> 00:03:51.000 align:start position:0%
steerability of the model if you were to
 

00:03:51.000 --> 00:03:53.030 align:start position:0%
steerability of the model if you were to
give<00:03:51.400><c> domain</c><00:03:51.879><c> specific</c><00:03:52.280><c> instructions</c><00:03:52.760><c> to</c><00:03:52.920><c> the</c>

00:03:53.030 --> 00:03:53.040 align:start position:0%
give domain specific instructions to the
 

00:03:53.040 --> 00:03:55.030 align:start position:0%
give domain specific instructions to the
model<00:03:53.599><c> then</c><00:03:53.799><c> you</c><00:03:53.920><c> should</c><00:03:54.159><c> definitely</c><00:03:54.599><c> explore</c>

00:03:55.030 --> 00:03:55.040 align:start position:0%
model then you should definitely explore
 

00:03:55.040 --> 00:03:58.110 align:start position:0%
model then you should definitely explore
the<00:03:55.200><c> fine</c><00:03:55.439><c> tuning</c><00:03:56.280><c> the</c><00:03:56.879><c> the</c><00:03:57.159><c> punch</c><00:03:57.560><c> punch</c><00:03:57.840><c> line</c>

00:03:58.110 --> 00:03:58.120 align:start position:0%
the fine tuning the the punch punch line
 

00:03:58.120 --> 00:04:00.589 align:start position:0%
the fine tuning the the punch punch line
here<00:03:58.280><c> is</c><00:03:58.439><c> that</c><00:03:58.599><c> GPD</c><00:03:58.959><c> 40</c><00:03:59.480><c> f</c><00:03:59.879><c> tuning</c><00:04:00.200><c> is</c><00:04:00.319><c> going</c><00:04:00.480><c> to</c>

00:04:00.589 --> 00:04:00.599 align:start position:0%
here is that GPD 40 f tuning is going to
 

00:04:00.599 --> 00:04:03.670 align:start position:0%
here is that GPD 40 f tuning is going to
be<00:04:00.760><c> free</c><00:04:01.599><c> up</c><00:04:01.799><c> till</c><00:04:02.159><c> 1</c><00:04:02.439><c> million</c><00:04:02.799><c> tokens</c><00:04:03.519><c> uh</c>

00:04:03.670 --> 00:04:03.680 align:start position:0%
be free up till 1 million tokens uh
 

00:04:03.680 --> 00:04:06.949 align:start position:0%
be free up till 1 million tokens uh
every<00:04:03.920><c> single</c><00:04:04.319><c> day</c><00:04:04.680><c> up</c><00:04:04.840><c> till</c><00:04:05.640><c> 20</c><00:04:06.439><c> uh</c><00:04:06.560><c> September</c>

00:04:06.949 --> 00:04:06.959 align:start position:0%
every single day up till 20 uh September
 

00:04:06.959 --> 00:04:09.710 align:start position:0%
every single day up till 20 uh September
23rd<00:04:07.959><c> and</c><00:04:08.159><c> GPD</c><00:04:08.560><c> 40</c><00:04:08.920><c> mini</c><00:04:09.239><c> they're</c><00:04:09.439><c> going</c><00:04:09.599><c> to</c>

00:04:09.710 --> 00:04:09.720 align:start position:0%
23rd and GPD 40 mini they're going to
 

00:04:09.720 --> 00:04:12.390 align:start position:0%
23rd and GPD 40 mini they're going to
give<00:04:09.959><c> 2</c><00:04:10.159><c> million</c><00:04:10.480><c> tokens</c><00:04:10.920><c> every</c><00:04:11.159><c> day</c><00:04:11.400><c> for</c><00:04:11.599><c> free</c>

00:04:12.390 --> 00:04:12.400 align:start position:0%
give 2 million tokens every day for free
 

00:04:12.400 --> 00:04:14.229 align:start position:0%
give 2 million tokens every day for free
the<00:04:12.519><c> catch</c><00:04:12.799><c> here</c><00:04:13.000><c> is</c><00:04:13.159><c> though</c><00:04:13.560><c> you</c><00:04:13.680><c> can</c><00:04:13.959><c> find</c>

00:04:14.229 --> 00:04:14.239 align:start position:0%
the catch here is though you can find
 

00:04:14.239 --> 00:04:15.589 align:start position:0%
the catch here is though you can find
you<00:04:14.400><c> in</c><00:04:14.480><c> the</c><00:04:14.560><c> model</c><00:04:14.799><c> but</c><00:04:14.920><c> you</c><00:04:15.000><c> cannot</c><00:04:15.239><c> download</c>

00:04:15.589 --> 00:04:15.599 align:start position:0%
you in the model but you cannot download
 

00:04:15.599 --> 00:04:17.629 align:start position:0%
you in the model but you cannot download
the<00:04:15.760><c> model</c><00:04:16.000><c> of</c><00:04:16.120><c> course</c><00:04:16.280><c> it</c><00:04:16.400><c> is</c><00:04:16.560><c> open</c><00:04:16.799><c> a</c><00:04:17.320><c> so</c><00:04:17.519><c> you</c>

00:04:17.629 --> 00:04:17.639 align:start position:0%
the model of course it is open a so you
 

00:04:17.639 --> 00:04:19.349 align:start position:0%
the model of course it is open a so you
would<00:04:17.880><c> have</c><00:04:18.040><c> to</c><00:04:18.199><c> end</c><00:04:18.359><c> up</c><00:04:18.639><c> hosting</c><00:04:19.000><c> the</c><00:04:19.120><c> model</c>

00:04:19.349 --> 00:04:19.359 align:start position:0%
would have to end up hosting the model
 

00:04:19.359 --> 00:04:21.069 align:start position:0%
would have to end up hosting the model
on<00:04:19.519><c> open</c><00:04:19.759><c> aai</c><00:04:20.079><c> and</c><00:04:20.199><c> if</c><00:04:20.320><c> you</c><00:04:20.400><c> were</c><00:04:20.600><c> to</c><00:04:20.759><c> end</c><00:04:20.919><c> up</c>

00:04:21.069 --> 00:04:21.079 align:start position:0%
on open aai and if you were to end up
 

00:04:21.079 --> 00:04:23.150 align:start position:0%
on open aai and if you were to end up
hosting<00:04:21.440><c> the</c><00:04:21.560><c> model</c><00:04:21.759><c> on</c><00:04:21.919><c> open</c><00:04:22.199><c> AI</c><00:04:22.880><c> then</c><00:04:23.040><c> the</c>

00:04:23.150 --> 00:04:23.160 align:start position:0%
hosting the model on open AI then the
 

00:04:23.160 --> 00:04:25.510 align:start position:0%
hosting the model on open AI then the
model<00:04:23.400><c> is</c><00:04:23.520><c> going</c><00:04:23.720><c> to</c><00:04:23.919><c> cost</c><00:04:24.280><c> the</c><00:04:24.400><c> GPD</c><00:04:24.840><c> 40</c><00:04:25.280><c> model</c>

00:04:25.510 --> 00:04:25.520 align:start position:0%
model is going to cost the GPD 40 model
 

00:04:25.520 --> 00:04:28.990 align:start position:0%
model is going to cost the GPD 40 model
is<00:04:25.639><c> going</c><00:04:25.800><c> to</c><00:04:25.960><c> cost</c><00:04:26.520><c> 3.75</c><00:04:27.520><c> million</c><00:04:28.440><c> um</c><00:04:28.840><c> per</c>

00:04:28.990 --> 00:04:29.000 align:start position:0%
is going to cost 3.75 million um per
 

00:04:29.000 --> 00:04:30.590 align:start position:0%
is going to cost 3.75 million um per
million<00:04:29.440><c> sorry</c><00:04:29.720><c> sry</c>

00:04:30.590 --> 00:04:30.600 align:start position:0%
million sorry sry
 

00:04:30.600 --> 00:04:33.950 align:start position:0%
million sorry sry
$3.75<00:04:31.600><c> per</c><00:04:31.840><c> million</c><00:04:32.720><c> of</c><00:04:32.960><c> tokens</c><00:04:33.360><c> input</c><00:04:33.840><c> and</c>

00:04:33.950 --> 00:04:33.960 align:start position:0%
$3.75 per million of tokens input and
 

00:04:33.960 --> 00:04:36.790 align:start position:0%
$3.75 per million of tokens input and
for<00:04:34.160><c> output</c><00:04:34.520><c> it</c><00:04:34.600><c> is</c><00:04:34.800><c> 15</c><00:04:35.400><c> per</c><00:04:35.560><c> million</c><00:04:36.320><c> and</c><00:04:36.639><c> in</c>

00:04:36.790 --> 00:04:36.800 align:start position:0%
for output it is 15 per million and in
 

00:04:36.800 --> 00:04:40.029 align:start position:0%
for output it is 15 per million and in
terms<00:04:37.120><c> of</c><00:04:37.320><c> G</c><00:04:37.919><c> gp4</c><00:04:38.520><c> oh</c><00:04:38.759><c> mini</c><00:04:39.479><c> which</c><00:04:39.680><c> probably</c>

00:04:40.029 --> 00:04:40.039 align:start position:0%
terms of G gp4 oh mini which probably
 

00:04:40.039 --> 00:04:42.629 align:start position:0%
terms of G gp4 oh mini which probably
would<00:04:40.240><c> be</c><00:04:40.639><c> the</c><00:04:41.440><c> model</c><00:04:41.720><c> for</c><00:04:41.880><c> a</c><00:04:42.000><c> lot</c><00:04:42.120><c> of</c><00:04:42.240><c> use</c>

00:04:42.629 --> 00:04:42.639 align:start position:0%
would be the model for a lot of use
 

00:04:42.639 --> 00:04:45.390 align:start position:0%
would be the model for a lot of use
cases<00:04:43.639><c> uh</c><00:04:43.800><c> this</c><00:04:43.919><c> is</c><00:04:44.199><c> much</c><00:04:44.520><c> cheaper</c><00:04:45.160><c> so</c><00:04:45.320><c> you</c>

00:04:45.390 --> 00:04:45.400 align:start position:0%
cases uh this is much cheaper so you
 

00:04:45.400 --> 00:04:47.870 align:start position:0%
cases uh this is much cheaper so you
should<00:04:45.680><c> definitely</c><00:04:46.320><c> try</c><00:04:46.600><c> out</c><00:04:47.039><c> uh</c><00:04:47.160><c> this</c><00:04:47.400><c> option</c>

00:04:47.870 --> 00:04:47.880 align:start position:0%
should definitely try out uh this option
 

00:04:47.880 --> 00:04:49.550 align:start position:0%
should definitely try out uh this option
uh<00:04:47.960><c> if</c><00:04:48.080><c> you</c><00:04:48.160><c> were</c><00:04:48.360><c> to</c><00:04:48.560><c> try</c><00:04:48.759><c> out</c><00:04:48.919><c> fine</c><00:04:49.160><c> tuning</c><00:04:49.440><c> I</c>

00:04:49.550 --> 00:04:49.560 align:start position:0%
uh if you were to try out fine tuning I
 

00:04:49.560 --> 00:04:51.150 align:start position:0%
uh if you were to try out fine tuning I
might<00:04:49.840><c> probably</c><00:04:50.120><c> release</c><00:04:50.400><c> a</c><00:04:50.560><c> tutorial</c><00:04:50.960><c> on</c>

00:04:51.150 --> 00:04:51.160 align:start position:0%
might probably release a tutorial on
 

00:04:51.160 --> 00:04:52.909 align:start position:0%
might probably release a tutorial on
this<00:04:51.759><c> and</c><00:04:51.840><c> then</c><00:04:52.000><c> the</c><00:04:52.120><c> final</c><00:04:52.360><c> one</c><00:04:52.520><c> is</c><00:04:52.639><c> something</c>

00:04:52.909 --> 00:04:52.919 align:start position:0%
this and then the final one is something
 

00:04:52.919 --> 00:04:55.950 align:start position:0%
this and then the final one is something
that<00:04:53.320><c> we</c><00:04:53.479><c> just</c><00:04:53.759><c> recently</c><00:04:54.199><c> saw</c><00:04:54.560><c> this</c><00:04:54.680><c> is</c><00:04:54.960><c> v0</c>

00:04:55.950 --> 00:04:55.960 align:start position:0%
that we just recently saw this is v0
 

00:04:55.960 --> 00:04:58.230 align:start position:0%
that we just recently saw this is v0
it's<00:04:56.160><c> like</c><00:04:56.320><c> the</c><00:04:56.440><c> version</c><00:04:56.759><c> one</c><00:04:57.039><c> MVP</c><00:04:57.800><c> building</c>

00:04:58.230 --> 00:04:58.240 align:start position:0%
it's like the version one MVP building
 

00:04:58.240 --> 00:05:00.350 align:start position:0%
it's like the version one MVP building
application<00:04:58.960><c> of</c><00:04:59.400><c> where</c>

00:05:00.350 --> 00:05:00.360 align:start position:0%
application of where
 

00:05:00.360 --> 00:05:02.029 align:start position:0%
application of where
and<00:05:00.639><c> this</c><00:05:00.720><c> is</c><00:05:00.960><c> excellent</c><00:05:01.400><c> you</c><00:05:01.479><c> can</c><00:05:01.639><c> build</c><00:05:01.919><c> a</c>

00:05:02.029 --> 00:05:02.039 align:start position:0%
and this is excellent you can build a
 

00:05:02.039 --> 00:05:03.629 align:start position:0%
and this is excellent you can build a
lot<00:05:02.240><c> of</c><00:05:02.400><c> things</c><00:05:02.600><c> ton</c><00:05:02.800><c> of</c><00:05:02.960><c> things</c><00:05:03.199><c> and</c><00:05:03.479><c> I've</c>

00:05:03.629 --> 00:05:03.639 align:start position:0%
lot of things ton of things and I've
 

00:05:03.639 --> 00:05:05.270 align:start position:0%
lot of things ton of things and I've
already<00:05:03.960><c> got</c><00:05:04.120><c> a</c><00:05:04.240><c> dedicated</c><00:05:04.680><c> video</c><00:05:04.880><c> for</c><00:05:05.080><c> this</c>

00:05:05.270 --> 00:05:05.280 align:start position:0%
already got a dedicated video for this
 

00:05:05.280 --> 00:05:07.070 align:start position:0%
already got a dedicated video for this
I'll<00:05:05.520><c> link</c><00:05:05.720><c> it</c><00:05:05.800><c> in</c><00:05:05.880><c> the</c><00:05:06.000><c> YouTube</c><00:05:06.280><c> description</c>

00:05:07.070 --> 00:05:07.080 align:start position:0%
I'll link it in the YouTube description
 

00:05:07.080 --> 00:05:08.629 align:start position:0%
I'll link it in the YouTube description
but<00:05:07.199><c> I'm</c><00:05:07.360><c> going</c><00:05:07.520><c> to</c><00:05:07.759><c> double</c><00:05:08.080><c> down</c><00:05:08.240><c> on</c><00:05:08.440><c> this</c>

00:05:08.629 --> 00:05:08.639 align:start position:0%
but I'm going to double down on this
 

00:05:08.639 --> 00:05:10.029 align:start position:0%
but I'm going to double down on this
particular<00:05:09.039><c> thing</c><00:05:09.240><c> and</c><00:05:09.360><c> then</c><00:05:09.560><c> go</c><00:05:09.720><c> ahead</c><00:05:09.919><c> and</c>

00:05:10.029 --> 00:05:10.039 align:start position:0%
particular thing and then go ahead and
 

00:05:10.039 --> 00:05:12.029 align:start position:0%
particular thing and then go ahead and
then<00:05:10.199><c> create</c><00:05:10.440><c> like</c><00:05:10.560><c> a</c><00:05:10.639><c> full-fledged</c><00:05:11.160><c> either</c><00:05:11.880><c> a</c>

00:05:12.029 --> 00:05:12.039 align:start position:0%
then create like a full-fledged either a
 

00:05:12.039 --> 00:05:14.310 align:start position:0%
then create like a full-fledged either a
product<00:05:12.320><c> or</c><00:05:12.440><c> a</c><00:05:12.560><c> tool</c><00:05:12.800><c> or</c><00:05:12.919><c> a</c><00:05:13.039><c> SAS</c><00:05:13.400><c> using</c><00:05:13.720><c> this</c><00:05:14.039><c> so</c>

00:05:14.310 --> 00:05:14.320 align:start position:0%
product or a tool or a SAS using this so
 

00:05:14.320 --> 00:05:16.870 align:start position:0%
product or a tool or a SAS using this so
stay<00:05:14.560><c> tuned</c><00:05:15.360><c> and</c><00:05:15.680><c> if</c><00:05:15.800><c> you</c><00:05:15.919><c> were</c><00:05:16.120><c> to</c><00:05:16.280><c> see</c><00:05:16.759><c> this</c>

00:05:16.870 --> 00:05:16.880 align:start position:0%
stay tuned and if you were to see this
 

00:05:16.880 --> 00:05:20.230 align:start position:0%
stay tuned and if you were to see this
is<00:05:17.520><c> a</c><00:05:17.680><c> woman</c><00:05:18.000><c> with</c><00:05:18.120><c> a</c><00:05:18.319><c> computer</c><00:05:19.000><c> at</c><00:05:19.319><c> Starbucks</c>

00:05:20.230 --> 00:05:20.240 align:start position:0%
is a woman with a computer at Starbucks
 

00:05:20.240 --> 00:05:22.870 align:start position:0%
is a woman with a computer at Starbucks
there's<00:05:20.479><c> a</c><00:05:20.759><c> sticker</c><00:05:21.759><c> um</c><00:05:22.080><c> maybe</c><00:05:22.479><c> but</c><00:05:22.680><c> there</c><00:05:22.759><c> is</c>

00:05:22.870 --> 00:05:22.880 align:start position:0%
there's a sticker um maybe but there is
 

00:05:22.880 --> 00:05:25.950 align:start position:0%
there's a sticker um maybe but there is
a<00:05:23.000><c> Starbucks</c><00:05:23.479><c> logo</c><00:05:24.000><c> there's</c><00:05:24.199><c> a</c><00:05:24.479><c> coffee</c><00:05:25.479><c> and</c><00:05:25.840><c> uh</c>

00:05:25.950 --> 00:05:25.960 align:start position:0%
a Starbucks logo there's a coffee and uh
 

00:05:25.960 --> 00:05:27.909 align:start position:0%
a Starbucks logo there's a coffee and uh
she's<00:05:26.199><c> saying</c><00:05:26.479><c> something</c><00:05:26.840><c> like</c><00:05:27.039><c> I</c><00:05:27.120><c> don't</c><00:05:27.280><c> know</c>

00:05:27.909 --> 00:05:27.919 align:start position:0%
she's saying something like I don't know
 

00:05:27.919 --> 00:05:29.950 align:start position:0%
she's saying something like I don't know
she's<00:05:28.199><c> maybe</c><00:05:28.639><c> watching</c><00:05:28.960><c> my</c><00:05:29.160><c> video</c><00:05:29.440><c> and</c><00:05:29.720><c> then</c>

00:05:29.950 --> 00:05:29.960 align:start position:0%
she's maybe watching my video and then
 

00:05:29.960 --> 00:05:32.629 align:start position:0%
she's maybe watching my video and then
thinking<00:05:30.319><c> about</c><00:05:30.560><c> it</c><00:05:31.160><c> so</c><00:05:31.840><c> all</c><00:05:32.080><c> we</c><00:05:32.240><c> had</c><00:05:32.360><c> to</c><00:05:32.479><c> do</c>

00:05:32.629 --> 00:05:32.639 align:start position:0%
thinking about it so all we had to do
 

00:05:32.639 --> 00:05:34.270 align:start position:0%
thinking about it so all we had to do
was<00:05:32.919><c> give</c><00:05:33.080><c> a</c><00:05:33.199><c> very</c><00:05:33.400><c> simple</c><00:05:33.680><c> prompt</c><00:05:34.000><c> not</c><00:05:34.120><c> even</c>

00:05:34.270 --> 00:05:34.280 align:start position:0%
was give a very simple prompt not even
 

00:05:34.280 --> 00:05:35.590 align:start position:0%
was give a very simple prompt not even
detailed<00:05:34.600><c> prompt</c><00:05:34.919><c> and</c><00:05:35.039><c> this</c><00:05:35.120><c> is</c><00:05:35.319><c> quite</c>

00:05:35.590 --> 00:05:35.600 align:start position:0%
detailed prompt and this is quite
 

00:05:35.600 --> 00:05:37.749 align:start position:0%
detailed prompt and this is quite
excellent<00:05:36.360><c> the</c><00:05:36.520><c> fact</c><00:05:36.800><c> that</c><00:05:37.039><c> uh</c><00:05:37.160><c> it</c><00:05:37.240><c> managed</c><00:05:37.600><c> to</c>

00:05:37.749 --> 00:05:37.759 align:start position:0%
excellent the fact that uh it managed to
 

00:05:37.759 --> 00:05:40.790 align:start position:0%
excellent the fact that uh it managed to
generate<00:05:38.160><c> this</c><00:05:38.680><c> I</c><00:05:38.840><c> hope</c><00:05:39.240><c> the</c><00:05:39.960><c> brief</c><00:05:40.319><c> news</c><00:05:40.639><c> was</c>

00:05:40.790 --> 00:05:40.800 align:start position:0%
generate this I hope the brief news was
 

00:05:40.800 --> 00:05:42.070 align:start position:0%
generate this I hope the brief news was
helpful<00:05:41.120><c> to</c><00:05:41.240><c> you</c><00:05:41.440><c> if</c><00:05:41.520><c> you</c><00:05:41.639><c> have</c><00:05:41.759><c> any</c><00:05:41.919><c> other</c>

00:05:42.070 --> 00:05:42.080 align:start position:0%
helpful to you if you have any other
 

00:05:42.080 --> 00:05:43.029 align:start position:0%
helpful to you if you have any other
question<00:05:42.360><c> let</c><00:05:42.479><c> me</c><00:05:42.560><c> know</c><00:05:42.639><c> in</c><00:05:42.720><c> the</c><00:05:42.800><c> comment</c>

00:05:43.029 --> 00:05:43.039 align:start position:0%
question let me know in the comment
 

00:05:43.039 --> 00:05:44.510 align:start position:0%
question let me know in the comment
section<00:05:43.319><c> see</c><00:05:43.479><c> you</c><00:05:43.560><c> in</c><00:05:43.639><c> another</c><00:05:43.880><c> video</c><00:05:44.319><c> Happy</c>

00:05:44.510 --> 00:05:44.520 align:start position:0%
section see you in another video Happy
 

00:05:44.520 --> 00:05:47.039 align:start position:0%
section see you in another video Happy
prompting

