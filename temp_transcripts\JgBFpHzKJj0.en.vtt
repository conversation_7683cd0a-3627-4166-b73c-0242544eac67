WEBVTT
Kind: captions
Language: en

00:00:00.179 --> 00:00:02.629 align:start position:0%
 
okay<00:00:00.719><c> welcome</c><00:00:01.140><c> back</c><00:00:01.319><c> to</c><00:00:01.500><c> another</c><00:00:01.620><c> video</c><00:00:01.979><c> and</c>

00:00:02.629 --> 00:00:02.639 align:start position:0%
okay welcome back to another video and
 

00:00:02.639 --> 00:00:04.789 align:start position:0%
okay welcome back to another video and
today<00:00:02.940><c> we're</c><00:00:03.300><c> going</c><00:00:03.480><c> to</c><00:00:03.600><c> be</c><00:00:03.720><c> going</c><00:00:03.899><c> over</c><00:00:04.200><c> an</c>

00:00:04.789 --> 00:00:04.799 align:start position:0%
today we're going to be going over an
 

00:00:04.799 --> 00:00:07.550 align:start position:0%
today we're going to be going over an
example<00:00:05.160><c> uh</c><00:00:05.640><c> for</c><00:00:05.819><c> Big</c><00:00:06.000><c> O</c><00:00:06.180><c> notation</c><00:00:06.600><c> in</c><00:00:07.379><c> the</c>

00:00:07.550 --> 00:00:07.560 align:start position:0%
example uh for Big O notation in the
 

00:00:07.560 --> 00:00:10.850 align:start position:0%
example uh for Big O notation in the
ctci<00:00:08.400><c> book</c><00:00:08.700><c> and</c><00:00:09.540><c> this</c><00:00:09.900><c> time</c><00:00:10.080><c> this</c><00:00:10.500><c> is</c><00:00:10.620><c> a</c><00:00:10.740><c> little</c>

00:00:10.850 --> 00:00:10.860 align:start position:0%
ctci book and this time this is a little
 

00:00:10.860 --> 00:00:11.990 align:start position:0%
ctci book and this time this is a little
tricky

00:00:11.990 --> 00:00:12.000 align:start position:0%
tricky
 

00:00:12.000 --> 00:00:14.749 align:start position:0%
tricky
it's<00:00:12.240><c> a</c><00:00:12.480><c> nested</c><00:00:12.840><c> for</c><00:00:12.960><c> Loop</c><00:00:13.380><c> however</c><00:00:14.040><c> the</c><00:00:14.580><c> inner</c>

00:00:14.749 --> 00:00:14.759 align:start position:0%
it's a nested for Loop however the inner
 

00:00:14.759 --> 00:00:17.450 align:start position:0%
it's a nested for Loop however the inner
for<00:00:14.880><c> Loop</c><00:00:15.240><c> says</c><00:00:15.540><c> int</c><00:00:16.020><c> J</c><00:00:16.260><c> equals</c><00:00:16.680><c> I</c><00:00:16.920><c> plus</c><00:00:17.160><c> one</c>

00:00:17.450 --> 00:00:17.460 align:start position:0%
for Loop says int J equals I plus one
 

00:00:17.460 --> 00:00:19.970 align:start position:0%
for Loop says int J equals I plus one
all<00:00:18.060><c> right</c><00:00:18.300><c> let's</c><00:00:18.660><c> get</c><00:00:18.900><c> into</c><00:00:19.020><c> it</c>

00:00:19.970 --> 00:00:19.980 align:start position:0%
all right let's get into it
 

00:00:19.980 --> 00:00:22.189 align:start position:0%
all right let's get into it
okay<00:00:20.340><c> so</c><00:00:20.580><c> before</c><00:00:20.760><c> we</c><00:00:20.939><c> get</c><00:00:21.119><c> into</c><00:00:21.359><c> the</c><00:00:21.600><c> problem</c><00:00:21.779><c> I</c>

00:00:22.189 --> 00:00:22.199 align:start position:0%
okay so before we get into the problem I
 

00:00:22.199 --> 00:00:24.109 align:start position:0%
okay so before we get into the problem I
want<00:00:22.320><c> to</c><00:00:22.439><c> go</c><00:00:22.560><c> over</c><00:00:22.740><c> two</c><00:00:23.400><c> other</c><00:00:23.640><c> examples</c>

00:00:24.109 --> 00:00:24.119 align:start position:0%
want to go over two other examples
 

00:00:24.119 --> 00:00:25.910 align:start position:0%
want to go over two other examples
really<00:00:24.480><c> quick</c><00:00:24.660><c> that</c><00:00:24.900><c> I've</c><00:00:25.019><c> done</c><00:00:25.260><c> in</c><00:00:25.500><c> other</c>

00:00:25.910 --> 00:00:25.920 align:start position:0%
really quick that I've done in other
 

00:00:25.920 --> 00:00:29.089 align:start position:0%
really quick that I've done in other
videos<00:00:26.480><c> so</c><00:00:27.480><c> the</c><00:00:27.720><c> first</c><00:00:27.840><c> one</c><00:00:28.019><c> here</c><00:00:28.199><c> is</c><00:00:28.560><c> we</c><00:00:28.920><c> have</c>

00:00:29.089 --> 00:00:29.099 align:start position:0%
videos so the first one here is we have
 

00:00:29.099 --> 00:00:32.030 align:start position:0%
videos so the first one here is we have
two<00:00:29.220><c> for</c><00:00:29.460><c> Loops</c><00:00:29.820><c> but</c><00:00:30.420><c> they</c><00:00:31.140><c> occur</c><00:00:31.439><c> one</c><00:00:31.740><c> after</c>

00:00:32.030 --> 00:00:32.040 align:start position:0%
two for Loops but they occur one after
 

00:00:32.040 --> 00:00:34.610 align:start position:0%
two for Loops but they occur one after
the<00:00:32.399><c> other</c><00:00:32.520><c> so</c><00:00:33.480><c> we</c><00:00:33.780><c> have</c><00:00:33.960><c> you</c><00:00:34.380><c> know</c><00:00:34.500><c> a</c><00:00:34.559><c> couple</c>

00:00:34.610 --> 00:00:34.620 align:start position:0%
the other so we have you know a couple
 

00:00:34.620 --> 00:00:36.729 align:start position:0%
the other so we have you know a couple
constant<00:00:35.160><c> operations</c><00:00:35.520><c> we</c><00:00:35.880><c> don't</c><00:00:36.120><c> care</c><00:00:36.420><c> about</c>

00:00:36.729 --> 00:00:36.739 align:start position:0%
constant operations we don't care about
 

00:00:36.739 --> 00:00:38.990 align:start position:0%
constant operations we don't care about
then<00:00:37.739><c> we</c><00:00:37.920><c> had</c><00:00:37.980><c> this</c><00:00:38.160><c> for</c><00:00:38.340><c> Loop</c><00:00:38.579><c> that</c><00:00:38.700><c> goes</c><00:00:38.880><c> from</c>

00:00:38.990 --> 00:00:39.000 align:start position:0%
then we had this for Loop that goes from
 

00:00:39.000 --> 00:00:40.790 align:start position:0%
then we had this for Loop that goes from
zero<00:00:39.300><c> to</c><00:00:39.480><c> the</c><00:00:39.660><c> length</c><00:00:39.899><c> of</c><00:00:40.079><c> the</c><00:00:40.200><c> array</c><00:00:40.440><c> whatever</c>

00:00:40.790 --> 00:00:40.800 align:start position:0%
zero to the length of the array whatever
 

00:00:40.800 --> 00:00:43.250 align:start position:0%
zero to the length of the array whatever
that<00:00:41.160><c> is</c><00:00:41.340><c> okay</c><00:00:41.700><c> and</c><00:00:41.940><c> we</c><00:00:42.180><c> just</c><00:00:42.360><c> have</c><00:00:42.840><c> a</c><00:00:43.020><c> constant</c>

00:00:43.250 --> 00:00:43.260 align:start position:0%
that is okay and we just have a constant
 

00:00:43.260 --> 00:00:46.430 align:start position:0%
that is okay and we just have a constant
operation<00:00:43.620><c> inside</c><00:00:44.399><c> that</c><00:00:44.820><c> adds</c><00:00:45.239><c> so</c><00:00:45.899><c> this</c><00:00:46.260><c> is</c>

00:00:46.430 --> 00:00:46.440 align:start position:0%
operation inside that adds so this is
 

00:00:46.440 --> 00:00:50.869 align:start position:0%
operation inside that adds so this is
Big<00:00:46.860><c> O</c><00:00:47.219><c> of</c><00:00:47.820><c> n</c><00:00:48.180><c> because</c><00:00:48.899><c> we</c><00:00:49.260><c> go</c><00:00:49.379><c> from</c><00:00:49.559><c> 0</c><00:00:49.879><c> to</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
Big O of n because we go from 0 to
 

00:00:50.879 --> 00:00:52.430 align:start position:0%
Big O of n because we go from 0 to
whatever<00:00:51.300><c> the</c><00:00:51.600><c> length</c><00:00:51.840><c> of</c><00:00:51.960><c> the</c><00:00:52.020><c> array</c><00:00:52.260><c> is</c>

00:00:52.430 --> 00:00:52.440 align:start position:0%
whatever the length of the array is
 

00:00:52.440 --> 00:00:54.650 align:start position:0%
whatever the length of the array is
which<00:00:52.680><c> we're</c><00:00:52.860><c> going</c><00:00:52.980><c> to</c><00:00:53.100><c> call</c><00:00:53.219><c> n</c><00:00:53.460><c> okay</c><00:00:53.940><c> so</c><00:00:54.480><c> then</c>

00:00:54.650 --> 00:00:54.660 align:start position:0%
which we're going to call n okay so then
 

00:00:54.660 --> 00:00:56.330 align:start position:0%
which we're going to call n okay so then
after<00:00:54.840><c> this</c><00:00:55.079><c> for</c><00:00:55.260><c> Loop</c><00:00:55.559><c> is</c><00:00:55.680><c> finished</c><00:00:55.920><c> then</c>

00:00:56.330 --> 00:00:56.340 align:start position:0%
after this for Loop is finished then
 

00:00:56.340 --> 00:00:58.850 align:start position:0%
after this for Loop is finished then
then<00:00:56.579><c> we</c><00:00:56.879><c> have</c><00:00:57.000><c> another</c><00:00:57.180><c> one</c><00:00:57.899><c> and</c><00:00:58.440><c> this</c><00:00:58.620><c> is</c><00:00:58.739><c> the</c>

00:00:58.850 --> 00:00:58.860 align:start position:0%
then we have another one and this is the
 

00:00:58.860 --> 00:01:00.310 align:start position:0%
then we have another one and this is the
same<00:00:59.039><c> thing</c><00:00:59.160><c> except</c><00:00:59.460><c> we</c><00:00:59.699><c> just</c><00:00:59.820><c> have</c>

00:01:00.310 --> 00:01:00.320 align:start position:0%
same thing except we just have
 

00:01:00.320 --> 00:01:02.569 align:start position:0%
same thing except we just have
multiplication<00:01:01.320><c> on</c><00:01:01.620><c> the</c><00:01:01.739><c> inside</c><00:01:01.800><c> of</c><00:01:02.280><c> the</c><00:01:02.460><c> for</c>

00:01:02.569 --> 00:01:02.579 align:start position:0%
multiplication on the inside of the for
 

00:01:02.579 --> 00:01:06.469 align:start position:0%
multiplication on the inside of the for
Loop<00:01:02.879><c> so</c><00:01:03.000><c> this</c><00:01:03.239><c> is</c><00:01:03.420><c> also</c><00:01:03.719><c> being</c><00:01:04.140><c> o</c><00:01:04.500><c> of</c><00:01:04.739><c> n</c>

00:01:06.469 --> 00:01:06.479 align:start position:0%
Loop so this is also being o of n
 

00:01:06.479 --> 00:01:09.170 align:start position:0%
Loop so this is also being o of n
now<00:01:07.020><c> what</c><00:01:07.500><c> we</c><00:01:07.619><c> do</c><00:01:07.799><c> here</c><00:01:07.979><c> is</c><00:01:08.220><c> we</c><00:01:08.460><c> just</c><00:01:08.640><c> add</c><00:01:08.880><c> these</c>

00:01:09.170 --> 00:01:09.180 align:start position:0%
now what we do here is we just add these
 

00:01:09.180 --> 00:01:10.670 align:start position:0%
now what we do here is we just add these
we<00:01:09.420><c> don't</c><00:01:09.540><c> multiply</c><00:01:09.900><c> them</c><00:01:10.080><c> we</c><00:01:10.320><c> add</c><00:01:10.500><c> them</c>

00:01:10.670 --> 00:01:10.680 align:start position:0%
we don't multiply them we add them
 

00:01:10.680 --> 00:01:14.270 align:start position:0%
we don't multiply them we add them
because<00:01:10.920><c> they</c><00:01:11.220><c> occur</c><00:01:11.640><c> one</c><00:01:12.000><c> after</c><00:01:12.360><c> the</c><00:01:12.659><c> other</c>

00:01:14.270 --> 00:01:14.280 align:start position:0%
because they occur one after the other
 

00:01:14.280 --> 00:01:18.410 align:start position:0%
because they occur one after the other
so<00:01:14.700><c> we</c><00:01:14.880><c> have</c><00:01:15.060><c> n</c><00:01:15.600><c> plus</c><00:01:16.080><c> n</c><00:01:16.500><c> which</c><00:01:17.159><c> is</c><00:01:17.280><c> equal</c><00:01:17.460><c> to</c><00:01:17.700><c> 2N</c>

00:01:18.410 --> 00:01:18.420 align:start position:0%
so we have n plus n which is equal to 2N
 

00:01:18.420 --> 00:01:20.390 align:start position:0%
so we have n plus n which is equal to 2N
however<00:01:19.200><c> we</c><00:01:19.380><c> don't</c><00:01:19.500><c> care</c><00:01:19.619><c> about</c><00:01:19.680><c> the</c><00:01:19.920><c> two</c><00:01:20.040><c> so</c>

00:01:20.390 --> 00:01:20.400 align:start position:0%
however we don't care about the two so
 

00:01:20.400 --> 00:01:24.890 align:start position:0%
however we don't care about the two so
we<00:01:20.580><c> drop</c><00:01:20.820><c> it</c><00:01:21.060><c> and</c><00:01:21.540><c> we</c><00:01:21.659><c> just</c><00:01:21.840><c> have</c><00:01:22.080><c> Big</c><00:01:22.500><c> O</c><00:01:22.920><c> of</c><00:01:23.580><c> n</c>

00:01:24.890 --> 00:01:24.900 align:start position:0%
we drop it and we just have Big O of n
 

00:01:24.900 --> 00:01:27.050 align:start position:0%
we drop it and we just have Big O of n
all<00:01:25.380><c> right</c><00:01:25.500><c> now</c><00:01:26.159><c> moving</c><00:01:26.400><c> on</c><00:01:26.640><c> to</c><00:01:26.759><c> the</c><00:01:26.939><c> next</c>

00:01:27.050 --> 00:01:27.060 align:start position:0%
all right now moving on to the next
 

00:01:27.060 --> 00:01:31.010 align:start position:0%
all right now moving on to the next
example<00:01:27.479><c> we</c><00:01:28.259><c> have</c><00:01:28.439><c> a</c><00:01:28.860><c> nested</c><00:01:29.280><c> Loop</c><00:01:29.880><c> so</c><00:01:30.600><c> both</c><00:01:30.840><c> of</c>

00:01:31.010 --> 00:01:31.020 align:start position:0%
example we have a nested Loop so both of
 

00:01:31.020 --> 00:01:32.870 align:start position:0%
example we have a nested Loop so both of
these<00:01:31.140><c> Loops</c><00:01:31.500><c> go</c><00:01:31.740><c> from</c><00:01:31.920><c> zero</c><00:01:32.280><c> to</c><00:01:32.460><c> the</c><00:01:32.640><c> length</c>

00:01:32.870 --> 00:01:32.880 align:start position:0%
these Loops go from zero to the length
 

00:01:32.880 --> 00:01:35.390 align:start position:0%
these Loops go from zero to the length
of<00:01:33.000><c> the</c><00:01:33.119><c> array</c><00:01:33.420><c> so</c><00:01:33.960><c> the</c><00:01:34.140><c> outer</c><00:01:34.380><c> one</c><00:01:34.500><c> is</c><00:01:34.799><c> Big</c><00:01:35.100><c> O</c>

00:01:35.390 --> 00:01:35.400 align:start position:0%
of the array so the outer one is Big O
 

00:01:35.400 --> 00:01:40.069 align:start position:0%
of the array so the outer one is Big O
of<00:01:36.060><c> N</c><00:01:36.299><c> and</c><00:01:37.140><c> the</c><00:01:37.320><c> inner</c><00:01:37.619><c> is</c><00:01:37.979><c> also</c><00:01:38.460><c> the</c><00:01:38.939><c> Big</c><00:01:39.299><c> O</c><00:01:39.659><c> of</c>

00:01:40.069 --> 00:01:40.079 align:start position:0%
of N and the inner is also the Big O of
 

00:01:40.079 --> 00:01:40.850 align:start position:0%
of N and the inner is also the Big O of
n

00:01:40.850 --> 00:01:40.860 align:start position:0%
n
 

00:01:40.860 --> 00:01:42.950 align:start position:0%
n
and<00:01:41.159><c> we</c><00:01:41.400><c> just</c><00:01:41.520><c> have</c><00:01:41.700><c> constant</c><00:01:42.180><c> operation</c><00:01:42.600><c> for</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
and we just have constant operation for
 

00:01:42.960 --> 00:01:45.410 align:start position:0%
and we just have constant operation for
printing<00:01:43.439><c> that</c><00:01:43.979><c> doesn't</c><00:01:44.220><c> matter</c><00:01:44.520><c> so</c><00:01:45.060><c> what</c><00:01:45.299><c> we</c>

00:01:45.410 --> 00:01:45.420 align:start position:0%
printing that doesn't matter so what we
 

00:01:45.420 --> 00:01:47.330 align:start position:0%
printing that doesn't matter so what we
have<00:01:45.600><c> here</c><00:01:45.900><c> is</c><00:01:46.380><c> n</c>

00:01:47.330 --> 00:01:47.340 align:start position:0%
have here is n
 

00:01:47.340 --> 00:01:51.410 align:start position:0%
have here is n
times<00:01:47.880><c> n</c><00:01:48.420><c> because</c><00:01:49.399><c> the</c><00:01:50.399><c> inner</c><00:01:50.700><c> loop</c><00:01:50.939><c> goes</c><00:01:51.299><c> from</c>

00:01:51.410 --> 00:01:51.420 align:start position:0%
times n because the inner loop goes from
 

00:01:51.420 --> 00:01:54.770 align:start position:0%
times n because the inner loop goes from
zero<00:01:51.780><c> to</c><00:01:51.840><c> delayed</c><00:01:52.500><c> to</c><00:01:52.560><c> the</c><00:01:52.680><c> array</c><00:01:52.920><c> n</c><00:01:53.640><c> and</c><00:01:54.360><c> we</c><00:01:54.479><c> do</c>

00:01:54.770 --> 00:01:54.780 align:start position:0%
zero to delayed to the array n and we do
 

00:01:54.780 --> 00:01:59.690 align:start position:0%
zero to delayed to the array n and we do
that<00:01:55.040><c> n</c><00:01:56.040><c> times</c><00:01:56.399><c> so</c><00:01:57.299><c> we</c><00:01:57.479><c> do</c><00:01:57.659><c> n</c><00:01:57.899><c> times</c><00:01:58.140><c> n</c><00:01:58.579><c> which</c><00:01:59.579><c> is</c>

00:01:59.690 --> 00:01:59.700 align:start position:0%
that n times so we do n times n which is
 

00:01:59.700 --> 00:02:03.889 align:start position:0%
that n times so we do n times n which is
equal<00:01:59.939><c> to</c><00:02:00.240><c> Big</c><00:02:01.020><c> O</c><00:02:01.380><c> of</c><00:02:01.920><c> N</c><00:02:02.340><c> squared</c>

00:02:03.889 --> 00:02:03.899 align:start position:0%
equal to Big O of N squared
 

00:02:03.899 --> 00:02:06.230 align:start position:0%
equal to Big O of N squared
okay<00:02:04.439><c> and</c><00:02:04.619><c> this</c><00:02:04.740><c> leads</c><00:02:04.979><c> us</c><00:02:05.100><c> into</c><00:02:05.340><c> our</c><00:02:05.939><c> actual</c>

00:02:06.230 --> 00:02:06.240 align:start position:0%
okay and this leads us into our actual
 

00:02:06.240 --> 00:02:08.749 align:start position:0%
okay and this leads us into our actual
example<00:02:06.600><c> and</c><00:02:07.500><c> the</c><00:02:07.920><c> difference</c><00:02:08.099><c> here</c><00:02:08.399><c> between</c>

00:02:08.749 --> 00:02:08.759 align:start position:0%
example and the difference here between
 

00:02:08.759 --> 00:02:11.210 align:start position:0%
example and the difference here between
this<00:02:09.300><c> and</c><00:02:09.479><c> the</c><00:02:09.720><c> previous</c><00:02:09.840><c> one</c><00:02:10.259><c> is</c><00:02:10.800><c> they</c><00:02:11.099><c> both</c>

00:02:11.210 --> 00:02:11.220 align:start position:0%
this and the previous one is they both
 

00:02:11.220 --> 00:02:12.890 align:start position:0%
this and the previous one is they both
look<00:02:11.400><c> like</c><00:02:11.520><c> nested</c><00:02:12.000><c> for</c><00:02:12.120><c> Loops</c><00:02:12.420><c> which</c><00:02:12.660><c> they</c>

00:02:12.890 --> 00:02:12.900 align:start position:0%
look like nested for Loops which they
 

00:02:12.900 --> 00:02:16.250 align:start position:0%
look like nested for Loops which they
are<00:02:13.140><c> however</c><00:02:13.940><c> on</c><00:02:14.940><c> the</c><00:02:15.180><c> inner</c><00:02:15.540><c> for</c><00:02:15.720><c> Loop</c><00:02:16.020><c> it</c>

00:02:16.250 --> 00:02:16.260 align:start position:0%
are however on the inner for Loop it
 

00:02:16.260 --> 00:02:18.710 align:start position:0%
are however on the inner for Loop it
says<00:02:16.560><c> J</c><00:02:16.860><c> equals</c><00:02:17.340><c> I</c><00:02:17.580><c> plus</c><00:02:17.760><c> one</c>

00:02:18.710 --> 00:02:18.720 align:start position:0%
says J equals I plus one
 

00:02:18.720 --> 00:02:21.470 align:start position:0%
says J equals I plus one
now<00:02:19.200><c> it's</c><00:02:20.160><c> pretty</c><00:02:20.340><c> obvious</c><00:02:20.700><c> it</c><00:02:21.120><c> when</c><00:02:21.300><c> an</c>

00:02:21.470 --> 00:02:21.480 align:start position:0%
now it's pretty obvious it when an
 

00:02:21.480 --> 00:02:22.850 align:start position:0%
now it's pretty obvious it when an
interview<00:02:21.599><c> interviewer</c><00:02:22.319><c> asks</c><00:02:22.620><c> you</c><00:02:22.620><c> something</c>

00:02:22.850 --> 00:02:22.860 align:start position:0%
interview interviewer asks you something
 

00:02:22.860 --> 00:02:25.970 align:start position:0%
interview interviewer asks you something
like<00:02:23.160><c> this</c><00:02:23.400><c> and</c><00:02:24.120><c> you</c><00:02:24.300><c> see</c><00:02:24.540><c> this</c><00:02:24.840><c> there's</c><00:02:25.739><c> some</c>

00:02:25.970 --> 00:02:25.980 align:start position:0%
like this and you see this there's some
 

00:02:25.980 --> 00:02:28.910 align:start position:0%
like this and you see this there's some
sort<00:02:26.220><c> of</c><00:02:26.340><c> potential</c><00:02:27.000><c> trick</c><00:02:27.420><c> here</c>

00:02:28.910 --> 00:02:28.920 align:start position:0%
sort of potential trick here
 

00:02:28.920 --> 00:02:30.650 align:start position:0%
sort of potential trick here
um<00:02:28.980><c> or</c><00:02:29.280><c> they</c><00:02:29.760><c> are</c><00:02:30.000><c> really</c><00:02:30.180><c> just</c><00:02:30.360><c> trying</c><00:02:30.540><c> to</c>

00:02:30.650 --> 00:02:30.660 align:start position:0%
um or they are really just trying to
 

00:02:30.660 --> 00:02:32.210 align:start position:0%
um or they are really just trying to
find<00:02:30.840><c> out</c><00:02:30.959><c> your</c><00:02:31.140><c> thought</c><00:02:31.319><c> process</c><00:02:31.680><c> on</c><00:02:31.920><c> how</c><00:02:32.099><c> you</c>

00:02:32.210 --> 00:02:32.220 align:start position:0%
find out your thought process on how you
 

00:02:32.220 --> 00:02:34.430 align:start position:0%
find out your thought process on how you
would<00:02:32.400><c> solve</c><00:02:32.700><c> this</c><00:02:32.879><c> problem</c><00:02:33.180><c> now</c><00:02:34.080><c> it</c><00:02:34.260><c> does</c>

00:02:34.430 --> 00:02:34.440 align:start position:0%
would solve this problem now it does
 

00:02:34.440 --> 00:02:35.750 align:start position:0%
would solve this problem now it does
seem<00:02:34.680><c> like</c><00:02:34.800><c> you</c><00:02:35.040><c> know</c><00:02:35.099><c> the</c><00:02:35.220><c> answer</c><00:02:35.340><c> is</c><00:02:35.580><c> just</c>

00:02:35.750 --> 00:02:35.760 align:start position:0%
seem like you know the answer is just
 

00:02:35.760 --> 00:02:38.390 align:start position:0%
seem like you know the answer is just
Big<00:02:35.879><c> O</c><00:02:36.060><c> of</c><00:02:36.180><c> N</c><00:02:36.239><c> squared</c><00:02:36.660><c> which</c><00:02:37.560><c> what</c><00:02:38.040><c> I</c><00:02:38.220><c> would</c>

00:02:38.390 --> 00:02:38.400 align:start position:0%
Big O of N squared which what I would
 

00:02:38.400 --> 00:02:40.550 align:start position:0%
Big O of N squared which what I would
come<00:02:38.580><c> to</c><00:02:38.760><c> in</c><00:02:39.480><c> my</c><00:02:39.660><c> head</c><00:02:39.780><c> if</c><00:02:40.020><c> I</c><00:02:40.140><c> was</c><00:02:40.260><c> asked</c><00:02:40.500><c> this</c>

00:02:40.550 --> 00:02:40.560 align:start position:0%
come to in my head if I was asked this
 

00:02:40.560 --> 00:02:42.890 align:start position:0%
come to in my head if I was asked this
an<00:02:40.739><c> interview</c><00:02:40.920><c> however</c><00:02:41.760><c> I</c><00:02:42.360><c> know</c><00:02:42.540><c> that</c><00:02:42.720><c> they</c>

00:02:42.890 --> 00:02:42.900 align:start position:0%
an interview however I know that they
 

00:02:42.900 --> 00:02:45.350 align:start position:0%
an interview however I know that they
also<00:02:43.140><c> want</c><00:02:43.440><c> to</c><00:02:43.560><c> see</c><00:02:43.980><c> how</c><00:02:44.459><c> I</c><00:02:44.940><c> would</c><00:02:45.060><c> come</c><00:02:45.239><c> up</c>

00:02:45.350 --> 00:02:45.360 align:start position:0%
also want to see how I would come up
 

00:02:45.360 --> 00:02:48.710 align:start position:0%
also want to see how I would come up
with<00:02:45.720><c> that</c><00:02:45.959><c> solution</c><00:02:46.340><c> so</c><00:02:47.340><c> what</c><00:02:47.879><c> I</c><00:02:48.060><c> would</c><00:02:48.180><c> do</c><00:02:48.300><c> in</c>

00:02:48.710 --> 00:02:48.720 align:start position:0%
with that solution so what I would do in
 

00:02:48.720 --> 00:02:50.750 align:start position:0%
with that solution so what I would do in
this<00:02:48.900><c> situation</c><00:02:49.260><c> is</c><00:02:49.739><c> well</c><00:02:50.220><c> we</c><00:02:50.400><c> know</c><00:02:50.580><c> it</c><00:02:50.640><c> takes</c>

00:02:50.750 --> 00:02:50.760 align:start position:0%
this situation is well we know it takes
 

00:02:50.760 --> 00:02:53.570 align:start position:0%
this situation is well we know it takes
an<00:02:50.940><c> array</c><00:02:51.239><c> how</c><00:02:51.660><c> about</c><00:02:51.780><c> we</c><00:02:51.959><c> just</c><00:02:52.319><c> create</c><00:02:53.220><c> a</c>

00:02:53.570 --> 00:02:53.580 align:start position:0%
an array how about we just create a
 

00:02:53.580 --> 00:02:56.210 align:start position:0%
an array how about we just create a
simple<00:02:53.700><c> array</c><00:02:54.120><c> with</c><00:02:54.959><c> four</c><00:02:55.260><c> elements</c><00:02:55.739><c> and</c><00:02:56.099><c> then</c>

00:02:56.210 --> 00:02:56.220 align:start position:0%
simple array with four elements and then
 

00:02:56.220 --> 00:02:58.369 align:start position:0%
simple array with four elements and then
just<00:02:56.459><c> go</c><00:02:56.940><c> through</c><00:02:57.180><c> these</c><00:02:57.599><c> loops</c><00:02:57.900><c> and</c><00:02:58.140><c> see</c><00:02:58.260><c> what</c>

00:02:58.369 --> 00:02:58.379 align:start position:0%
just go through these loops and see what
 

00:02:58.379 --> 00:03:00.229 align:start position:0%
just go through these loops and see what
happens<00:02:58.560><c> see</c><00:02:59.220><c> if</c><00:02:59.340><c> there's</c><00:02:59.459><c> some</c><00:02:59.640><c> pattern</c><00:03:00.000><c> a</c>

00:03:00.229 --> 00:03:00.239 align:start position:0%
happens see if there's some pattern a
 

00:03:00.239 --> 00:03:01.850 align:start position:0%
happens see if there's some pattern a
lot<00:03:00.360><c> of</c><00:03:00.420><c> times</c><00:03:00.540><c> there's</c><00:03:01.080><c> a</c><00:03:01.379><c> pattern</c><00:03:01.680><c> to</c>

00:03:01.850 --> 00:03:01.860 align:start position:0%
lot of times there's a pattern to
 

00:03:01.860 --> 00:03:04.130 align:start position:0%
lot of times there's a pattern to
something<00:03:02.099><c> and</c><00:03:03.060><c> that's</c><00:03:03.360><c> going</c><00:03:03.540><c> to</c><00:03:03.660><c> help</c><00:03:03.900><c> you</c>

00:03:04.130 --> 00:03:04.140 align:start position:0%
something and that's going to help you
 

00:03:04.140 --> 00:03:06.110 align:start position:0%
something and that's going to help you
figure<00:03:04.620><c> out</c><00:03:04.860><c> what</c><00:03:05.400><c> they're</c><00:03:05.700><c> trying</c><00:03:05.879><c> to</c><00:03:06.000><c> get</c>

00:03:06.110 --> 00:03:06.120 align:start position:0%
figure out what they're trying to get
 

00:03:06.120 --> 00:03:08.630 align:start position:0%
figure out what they're trying to get
from<00:03:06.420><c> you</c><00:03:06.599><c> okay</c><00:03:07.080><c> so</c><00:03:07.319><c> let's</c><00:03:07.500><c> just</c><00:03:07.620><c> do</c><00:03:07.800><c> this</c><00:03:07.920><c> so</c>

00:03:08.630 --> 00:03:08.640 align:start position:0%
from you okay so let's just do this so
 

00:03:08.640 --> 00:03:12.589 align:start position:0%
from you okay so let's just do this so
array<00:03:09.480><c> is</c><00:03:10.319><c> equal</c><00:03:10.500><c> to</c><00:03:10.800><c> let's</c><00:03:11.640><c> just</c><00:03:11.879><c> give</c><00:03:12.000><c> it</c><00:03:12.180><c> one</c>

00:03:12.589 --> 00:03:12.599 align:start position:0%
array is equal to let's just give it one
 

00:03:12.599 --> 00:03:15.949 align:start position:0%
array is equal to let's just give it one
two<00:03:12.959><c> three</c><00:03:13.800><c> and</c><00:03:14.280><c> four</c><00:03:14.700><c> and</c><00:03:15.420><c> this</c><00:03:15.599><c> means</c><00:03:15.840><c> that</c>

00:03:15.949 --> 00:03:15.959 align:start position:0%
two three and four and this means that
 

00:03:15.959 --> 00:03:17.330 align:start position:0%
two three and four and this means that
the<00:03:16.080><c> length</c><00:03:16.319><c> of</c><00:03:16.379><c> the</c><00:03:16.500><c> array</c>

00:03:17.330 --> 00:03:17.340 align:start position:0%
the length of the array
 

00:03:17.340 --> 00:03:21.110 align:start position:0%
the length of the array
or<00:03:18.000><c> n</c><00:03:18.300><c> is</c><00:03:18.599><c> equal</c><00:03:18.840><c> to</c><00:03:19.019><c> 4.</c><00:03:19.500><c> for</c><00:03:19.980><c> n</c><00:03:20.280><c> i</c><00:03:20.519><c> equals</c><00:03:20.819><c> zero</c>

00:03:21.110 --> 00:03:21.120 align:start position:0%
or n is equal to 4. for n i equals zero
 

00:03:21.120 --> 00:03:22.790 align:start position:0%
or n is equal to 4. for n i equals zero
so<00:03:21.300><c> we</c><00:03:21.420><c> start</c><00:03:21.540><c> out</c><00:03:21.720><c> zero</c><00:03:22.080><c> then</c><00:03:22.260><c> we</c><00:03:22.379><c> go</c><00:03:22.560><c> to</c><00:03:22.620><c> the</c>

00:03:22.790 --> 00:03:22.800 align:start position:0%
so we start out zero then we go to the
 

00:03:22.800 --> 00:03:25.190 align:start position:0%
so we start out zero then we go to the
inner<00:03:23.159><c> for</c><00:03:23.280><c> Loop</c><00:03:23.640><c> J</c><00:03:24.060><c> is</c><00:03:24.239><c> equal</c><00:03:24.360><c> to</c><00:03:24.540><c> I</c><00:03:24.720><c> plus</c><00:03:24.959><c> one</c>

00:03:25.190 --> 00:03:25.200 align:start position:0%
inner for Loop J is equal to I plus one
 

00:03:25.200 --> 00:03:27.649 align:start position:0%
inner for Loop J is equal to I plus one
or<00:03:25.560><c> J</c><00:03:25.860><c> is</c><00:03:25.980><c> equal</c><00:03:26.159><c> to</c><00:03:26.340><c> zero</c><00:03:26.879><c> plus</c><00:03:27.000><c> one</c><00:03:27.239><c> so</c>

00:03:27.649 --> 00:03:27.659 align:start position:0%
or J is equal to zero plus one so
 

00:03:27.659 --> 00:03:30.649 align:start position:0%
or J is equal to zero plus one so
initially<00:03:28.140><c> I</c><00:03:28.560><c> zero</c><00:03:29.040><c> J</c><00:03:29.340><c> is</c><00:03:29.519><c> one</c><00:03:29.700><c> and</c><00:03:30.300><c> then</c><00:03:30.420><c> we</c>

00:03:30.649 --> 00:03:30.659 align:start position:0%
initially I zero J is one and then we
 

00:03:30.659 --> 00:03:34.550 align:start position:0%
initially I zero J is one and then we
just<00:03:30.900><c> simply</c><00:03:31.500><c> print</c><00:03:32.159><c> I</c><00:03:32.640><c> comma</c><00:03:33.300><c> the</c><00:03:33.480><c> print</c><00:03:33.659><c> J</c>

00:03:34.550 --> 00:03:34.560 align:start position:0%
just simply print I comma the print J
 

00:03:34.560 --> 00:03:38.330 align:start position:0%
just simply print I comma the print J
so<00:03:34.739><c> I</c><00:03:35.400><c> zero</c><00:03:35.940><c> J</c><00:03:36.659><c> is</c><00:03:36.900><c> one</c><00:03:37.080><c> all</c><00:03:37.800><c> right</c><00:03:37.920><c> now</c><00:03:38.099><c> we</c>

00:03:38.330 --> 00:03:38.340 align:start position:0%
so I zero J is one all right now we
 

00:03:38.340 --> 00:03:40.729 align:start position:0%
so I zero J is one all right now we
increment<00:03:38.700><c> J</c><00:03:38.940><c> on</c><00:03:39.300><c> the</c><00:03:39.480><c> inner</c><00:03:39.720><c> loop</c><00:03:39.959><c> J</c><00:03:40.379><c> goes</c>

00:03:40.729 --> 00:03:40.739 align:start position:0%
increment J on the inner loop J goes
 

00:03:40.739 --> 00:03:43.610 align:start position:0%
increment J on the inner loop J goes
from<00:03:40.799><c> one</c><00:03:41.040><c> to</c><00:03:41.220><c> two</c><00:03:41.400><c> two</c><00:03:41.819><c> is</c><00:03:42.060><c> less</c><00:03:42.239><c> than</c><00:03:42.420><c> four</c><00:03:42.720><c> so</c>

00:03:43.610 --> 00:03:43.620 align:start position:0%
from one to two two is less than four so
 

00:03:43.620 --> 00:03:47.390 align:start position:0%
from one to two two is less than four so
now<00:03:43.920><c> we</c><00:03:44.159><c> print</c><00:03:44.400><c> out</c><00:03:45.080><c> zero</c><00:03:46.080><c> two</c><00:03:46.560><c> because</c><00:03:46.860><c> I</c><00:03:47.220><c> is</c>

00:03:47.390 --> 00:03:47.400 align:start position:0%
now we print out zero two because I is
 

00:03:47.400 --> 00:03:51.410 align:start position:0%
now we print out zero two because I is
still<00:03:47.760><c> zero</c><00:03:48.720><c> we</c><00:03:49.260><c> increment</c><00:03:49.620><c> J</c><00:03:49.860><c> again</c><00:03:50.099><c> J</c><00:03:50.580><c> is</c><00:03:50.819><c> 3</c><00:03:51.060><c> 3</c>

00:03:51.410 --> 00:03:51.420 align:start position:0%
still zero we increment J again J is 3 3
 

00:03:51.420 --> 00:03:53.630 align:start position:0%
still zero we increment J again J is 3 3
is<00:03:51.659><c> still</c><00:03:51.780><c> less</c><00:03:52.019><c> than</c><00:03:52.200><c> four</c><00:03:52.500><c> so</c><00:03:53.040><c> now</c><00:03:53.220><c> we</c><00:03:53.459><c> print</c>

00:03:53.630 --> 00:03:53.640 align:start position:0%
is still less than four so now we print
 

00:03:53.640 --> 00:03:55.309 align:start position:0%
is still less than four so now we print
out<00:03:53.940><c> zero</c>

00:03:55.309 --> 00:03:55.319 align:start position:0%
out zero
 

00:03:55.319 --> 00:03:56.690 align:start position:0%
out zero
three

00:03:56.690 --> 00:03:56.700 align:start position:0%
three
 

00:03:56.700 --> 00:03:59.509 align:start position:0%
three
well<00:03:57.239><c> three</c><00:03:57.540><c> we</c><00:03:57.840><c> increment</c><00:03:58.200><c> J</c><00:03:58.379><c> again</c><00:03:58.620><c> J</c><00:03:59.159><c> goes</c>

00:03:59.509 --> 00:03:59.519 align:start position:0%
well three we increment J again J goes
 

00:03:59.519 --> 00:04:01.369 align:start position:0%
well three we increment J again J goes
from<00:03:59.580><c> three</c><00:03:59.760><c> to</c><00:04:00.000><c> four</c><00:04:00.180><c> four</c><00:04:00.599><c> is</c><00:04:00.840><c> not</c><00:04:01.019><c> less</c><00:04:01.200><c> than</c>

00:04:01.369 --> 00:04:01.379 align:start position:0%
from three to four four is not less than
 

00:04:01.379 --> 00:04:03.589 align:start position:0%
from three to four four is not less than
four<00:04:01.560><c> so</c><00:04:02.159><c> we're</c><00:04:02.340><c> done</c><00:04:02.580><c> here</c>

00:04:03.589 --> 00:04:03.599 align:start position:0%
four so we're done here
 

00:04:03.599 --> 00:04:05.930 align:start position:0%
four so we're done here
we<00:04:03.959><c> we</c><00:04:04.019><c> go</c><00:04:04.440><c> from</c><00:04:04.560><c> the</c><00:04:04.739><c> inner</c><00:04:05.280><c> loop</c><00:04:05.519><c> back</c><00:04:05.700><c> to</c><00:04:05.819><c> the</c>

00:04:05.930 --> 00:04:05.940 align:start position:0%
we we go from the inner loop back to the
 

00:04:05.940 --> 00:04:08.210 align:start position:0%
we we go from the inner loop back to the
outer<00:04:06.239><c> loop</c><00:04:06.480><c> we</c><00:04:06.780><c> increment</c><00:04:07.080><c> I</c><00:04:07.379><c> so</c><00:04:07.620><c> now</c><00:04:07.799><c> I</c><00:04:08.040><c> is</c>

00:04:08.210 --> 00:04:08.220 align:start position:0%
outer loop we increment I so now I is
 

00:04:08.220 --> 00:04:11.210 align:start position:0%
outer loop we increment I so now I is
one<00:04:08.480><c> now</c><00:04:09.480><c> on</c><00:04:09.780><c> the</c><00:04:09.900><c> inner</c><00:04:10.140><c> loop</c><00:04:10.379><c> J</c><00:04:10.620><c> is</c><00:04:10.799><c> equal</c><00:04:10.980><c> to</c>

00:04:11.210 --> 00:04:11.220 align:start position:0%
one now on the inner loop J is equal to
 

00:04:11.220 --> 00:04:14.030 align:start position:0%
one now on the inner loop J is equal to
I<00:04:11.819><c> plus</c><00:04:12.060><c> one</c><00:04:12.299><c> or</c><00:04:12.540><c> one</c><00:04:12.780><c> plus</c><00:04:12.959><c> one</c><00:04:13.260><c> so</c><00:04:13.560><c> J</c><00:04:13.739><c> is</c><00:04:13.920><c> equal</c>

00:04:14.030 --> 00:04:14.040 align:start position:0%
I plus one or one plus one so J is equal
 

00:04:14.040 --> 00:04:16.550 align:start position:0%
I plus one or one plus one so J is equal
to<00:04:14.159><c> two</c><00:04:14.340><c> on</c><00:04:14.580><c> the</c><00:04:14.760><c> first</c><00:04:14.819><c> time</c><00:04:15.060><c> through</c><00:04:15.299><c> so</c><00:04:16.079><c> I</c><00:04:16.380><c> is</c>

00:04:16.550 --> 00:04:16.560 align:start position:0%
to two on the first time through so I is
 

00:04:16.560 --> 00:04:21.710 align:start position:0%
to two on the first time through so I is
one<00:04:16.919><c> J</c><00:04:17.699><c> is</c><00:04:17.940><c> 2.</c><00:04:18.720><c> well</c><00:04:19.500><c> we</c><00:04:20.100><c> increment</c><00:04:20.579><c> J</c><00:04:20.940><c> J</c><00:04:21.359><c> goes</c>

00:04:21.710 --> 00:04:21.720 align:start position:0%
one J is 2. well we increment J J goes
 

00:04:21.720 --> 00:04:23.510 align:start position:0%
one J is 2. well we increment J J goes
from<00:04:21.780><c> two</c><00:04:21.959><c> to</c><00:04:22.079><c> three</c><00:04:22.260><c> three</c><00:04:23.040><c> is</c><00:04:23.280><c> still</c><00:04:23.400><c> less</c>

00:04:23.510 --> 00:04:23.520 align:start position:0%
from two to three three is still less
 

00:04:23.520 --> 00:04:25.790 align:start position:0%
from two to three three is still less
than<00:04:23.699><c> four</c><00:04:23.880><c> so</c><00:04:24.240><c> now</c><00:04:24.419><c> we</c><00:04:24.600><c> have</c><00:04:24.780><c> one</c>

00:04:25.790 --> 00:04:25.800 align:start position:0%
than four so now we have one
 

00:04:25.800 --> 00:04:27.650 align:start position:0%
than four so now we have one
three

00:04:27.650 --> 00:04:27.660 align:start position:0%
three
 

00:04:27.660 --> 00:04:29.870 align:start position:0%
three
we<00:04:27.960><c> increment</c><00:04:28.259><c> J</c><00:04:28.500><c> again</c><00:04:28.680><c> it</c><00:04:28.979><c> goes</c><00:04:29.280><c> from</c><00:04:29.460><c> three</c>

00:04:29.870 --> 00:04:29.880 align:start position:0%
we increment J again it goes from three
 

00:04:29.880 --> 00:04:32.990 align:start position:0%
we increment J again it goes from three
to<00:04:30.180><c> four</c><00:04:30.479><c> well</c><00:04:31.320><c> now</c><00:04:31.620><c> J</c><00:04:31.860><c> is</c><00:04:32.040><c> four</c><00:04:32.220><c> four</c><00:04:32.580><c> is</c><00:04:32.820><c> not</c>

00:04:32.990 --> 00:04:33.000 align:start position:0%
to four well now J is four four is not
 

00:04:33.000 --> 00:04:34.969 align:start position:0%
to four well now J is four four is not
less<00:04:33.240><c> than</c><00:04:33.419><c> four</c><00:04:33.720><c> so</c><00:04:34.259><c> we're</c><00:04:34.500><c> done</c><00:04:34.740><c> with</c><00:04:34.860><c> the</c>

00:04:34.969 --> 00:04:34.979 align:start position:0%
less than four so we're done with the
 

00:04:34.979 --> 00:04:37.189 align:start position:0%
less than four so we're done with the
inner<00:04:35.160><c> loop</c><00:04:35.400><c> go</c><00:04:35.699><c> back</c><00:04:35.820><c> to</c><00:04:35.940><c> the</c><00:04:36.060><c> outer</c><00:04:36.300><c> loop</c><00:04:36.540><c> we</c>

00:04:37.189 --> 00:04:37.199 align:start position:0%
inner loop go back to the outer loop we
 

00:04:37.199 --> 00:04:39.409 align:start position:0%
inner loop go back to the outer loop we
increment<00:04:37.560><c> I</c><00:04:37.800><c> which</c><00:04:38.160><c> it</c><00:04:38.340><c> goes</c><00:04:38.580><c> from</c><00:04:38.759><c> one</c><00:04:39.180><c> to</c>

00:04:39.409 --> 00:04:39.419 align:start position:0%
increment I which it goes from one to
 

00:04:39.419 --> 00:04:40.550 align:start position:0%
increment I which it goes from one to
two

00:04:40.550 --> 00:04:40.560 align:start position:0%
two
 

00:04:40.560 --> 00:04:42.650 align:start position:0%
two
and<00:04:40.919><c> then</c><00:04:41.040><c> on</c><00:04:41.220><c> the</c><00:04:41.340><c> inner</c><00:04:41.580><c> loop</c><00:04:41.820><c> J</c><00:04:42.060><c> is</c><00:04:42.240><c> equal</c><00:04:42.419><c> to</c>

00:04:42.650 --> 00:04:42.660 align:start position:0%
and then on the inner loop J is equal to
 

00:04:42.660 --> 00:04:45.469 align:start position:0%
and then on the inner loop J is equal to
I<00:04:43.139><c> plus</c><00:04:43.380><c> 1</c><00:04:43.620><c> or</c><00:04:43.919><c> J</c><00:04:44.100><c> is</c><00:04:44.220><c> equal</c><00:04:44.400><c> to</c><00:04:44.580><c> two</c><00:04:44.880><c> plus</c><00:04:45.120><c> one</c>

00:04:45.469 --> 00:04:45.479 align:start position:0%
I plus 1 or J is equal to two plus one
 

00:04:45.479 --> 00:04:46.969 align:start position:0%
I plus 1 or J is equal to two plus one
so

00:04:46.969 --> 00:04:46.979 align:start position:0%
so
 

00:04:46.979 --> 00:04:49.249 align:start position:0%
so
now<00:04:47.639><c> on</c><00:04:47.820><c> the</c><00:04:48.000><c> first</c><00:04:48.120><c> time</c><00:04:48.360><c> through</c><00:04:48.840><c> the</c><00:04:49.020><c> inner</c>

00:04:49.249 --> 00:04:49.259 align:start position:0%
now on the first time through the inner
 

00:04:49.259 --> 00:04:53.210 align:start position:0%
now on the first time through the inner
loop<00:04:49.500><c> in</c><00:04:49.800><c> this</c><00:04:49.919><c> iteration</c><00:04:50.340><c> we</c><00:04:51.240><c> have</c><00:04:51.479><c> two</c>

00:04:53.210 --> 00:04:53.220 align:start position:0%
loop in this iteration we have two
 

00:04:53.220 --> 00:04:54.530 align:start position:0%
loop in this iteration we have two
three

00:04:54.530 --> 00:04:54.540 align:start position:0%
three
 

00:04:54.540 --> 00:04:56.330 align:start position:0%
three
okay<00:04:54.960><c> because</c><00:04:55.139><c> J</c><00:04:55.380><c> is</c><00:04:55.500><c> equal</c><00:04:55.680><c> to</c><00:04:55.860><c> three</c><00:04:56.040><c> two</c>

00:04:56.330 --> 00:04:56.340 align:start position:0%
okay because J is equal to three two
 

00:04:56.340 --> 00:04:57.290 align:start position:0%
okay because J is equal to three two
plus<00:04:56.520><c> one</c>

00:04:57.290 --> 00:04:57.300 align:start position:0%
plus one
 

00:04:57.300 --> 00:04:59.570 align:start position:0%
plus one
and<00:04:57.720><c> we</c><00:04:57.900><c> increment</c><00:04:58.199><c> J</c><00:04:58.500><c> J</c><00:04:58.740><c> goes</c><00:04:59.100><c> from</c><00:04:59.160><c> three</c><00:04:59.400><c> to</c>

00:04:59.570 --> 00:04:59.580 align:start position:0%
and we increment J J goes from three to
 

00:04:59.580 --> 00:05:02.090 align:start position:0%
and we increment J J goes from three to
four<00:04:59.840><c> however</c><00:05:00.840><c> four</c><00:05:01.139><c> is</c><00:05:01.380><c> still</c><00:05:01.560><c> not</c><00:05:01.680><c> less</c><00:05:01.919><c> than</c>

00:05:02.090 --> 00:05:02.100 align:start position:0%
four however four is still not less than
 

00:05:02.100 --> 00:05:04.249 align:start position:0%
four however four is still not less than
four<00:05:02.520><c> so</c><00:05:03.240><c> we're</c><00:05:03.419><c> done</c><00:05:03.600><c> with</c><00:05:03.720><c> the</c><00:05:03.840><c> inner</c><00:05:04.080><c> loop</c>

00:05:04.249 --> 00:05:04.259 align:start position:0%
four so we're done with the inner loop
 

00:05:04.259 --> 00:05:06.170 align:start position:0%
four so we're done with the inner loop
go<00:05:04.500><c> back</c><00:05:04.740><c> out</c><00:05:04.860><c> to</c><00:05:04.979><c> the</c><00:05:05.100><c> Outer</c><00:05:05.400><c> Loop</c>

00:05:06.170 --> 00:05:06.180 align:start position:0%
go back out to the Outer Loop
 

00:05:06.180 --> 00:05:08.570 align:start position:0%
go back out to the Outer Loop
I<00:05:06.540><c> is</c><00:05:06.720><c> now</c><00:05:06.840><c> equal</c><00:05:07.020><c> to</c><00:05:07.259><c> 3</c><00:05:07.560><c> because</c><00:05:07.860><c> increment</c><00:05:08.340><c> at</c>

00:05:08.570 --> 00:05:08.580 align:start position:0%
I is now equal to 3 because increment at
 

00:05:08.580 --> 00:05:09.350 align:start position:0%
I is now equal to 3 because increment at
one

00:05:09.350 --> 00:05:09.360 align:start position:0%
one
 

00:05:09.360 --> 00:05:11.930 align:start position:0%
one
we<00:05:09.780><c> go</c><00:05:09.900><c> to</c><00:05:10.020><c> the</c><00:05:10.139><c> inner</c><00:05:10.380><c> inner</c><00:05:10.740><c> loop</c><00:05:10.979><c> J</c><00:05:11.580><c> is</c><00:05:11.820><c> equal</c>

00:05:11.930 --> 00:05:11.940 align:start position:0%
we go to the inner inner loop J is equal
 

00:05:11.940 --> 00:05:14.930 align:start position:0%
we go to the inner inner loop J is equal
to<00:05:12.180><c> I</c><00:05:12.900><c> plus</c><00:05:13.139><c> one</c><00:05:13.380><c> or</c><00:05:13.680><c> three</c><00:05:13.979><c> plus</c><00:05:14.220><c> one</c><00:05:14.460><c> which</c><00:05:14.759><c> is</c>

00:05:14.930 --> 00:05:14.940 align:start position:0%
to I plus one or three plus one which is
 

00:05:14.940 --> 00:05:17.150 align:start position:0%
to I plus one or three plus one which is
four<00:05:15.240><c> well</c><00:05:15.900><c> four</c><00:05:16.139><c> is</c><00:05:16.320><c> not</c><00:05:16.440><c> less</c><00:05:16.620><c> than</c><00:05:16.740><c> four</c><00:05:16.919><c> so</c>

00:05:17.150 --> 00:05:17.160 align:start position:0%
four well four is not less than four so
 

00:05:17.160 --> 00:05:19.010 align:start position:0%
four well four is not less than four so
we<00:05:17.340><c> don't</c><00:05:17.400><c> put</c><00:05:17.580><c> anything</c><00:05:17.880><c> out</c>

00:05:19.010 --> 00:05:19.020 align:start position:0%
we don't put anything out
 

00:05:19.020 --> 00:05:20.810 align:start position:0%
we don't put anything out
um this<00:05:19.320><c> iteration</c>

00:05:20.810 --> 00:05:20.820 align:start position:0%
um this iteration
 

00:05:20.820 --> 00:05:23.170 align:start position:0%
um this iteration
and<00:05:21.419><c> then</c><00:05:21.600><c> we</c><00:05:21.960><c> go</c><00:05:22.139><c> back</c><00:05:22.259><c> up</c><00:05:22.440><c> to</c><00:05:22.560><c> the</c><00:05:22.680><c> outer</c><00:05:22.919><c> loop</c>

00:05:23.170 --> 00:05:23.180 align:start position:0%
and then we go back up to the outer loop
 

00:05:23.180 --> 00:05:25.969 align:start position:0%
and then we go back up to the outer loop
I<00:05:24.180><c> goes</c><00:05:24.479><c> from</c><00:05:24.600><c> three</c><00:05:24.780><c> to</c><00:05:24.900><c> four</c><00:05:25.080><c> well</c><00:05:25.440><c> four</c><00:05:25.740><c> is</c>

00:05:25.969 --> 00:05:25.979 align:start position:0%
I goes from three to four well four is
 

00:05:25.979 --> 00:05:28.010 align:start position:0%
I goes from three to four well four is
not<00:05:26.100><c> less</c><00:05:26.340><c> than</c><00:05:26.460><c> four</c><00:05:26.699><c> so</c><00:05:27.120><c> this</c><00:05:27.419><c> means</c><00:05:27.660><c> we</c><00:05:27.840><c> are</c>

00:05:28.010 --> 00:05:28.020 align:start position:0%
not less than four so this means we are
 

00:05:28.020 --> 00:05:30.170 align:start position:0%
not less than four so this means we are
done<00:05:28.259><c> okay</c><00:05:28.800><c> so</c><00:05:29.220><c> now</c><00:05:29.699><c> we're</c><00:05:29.880><c> finished</c>

00:05:30.170 --> 00:05:30.180 align:start position:0%
done okay so now we're finished
 

00:05:30.180 --> 00:05:32.810 align:start position:0%
done okay so now we're finished
analyzing<00:05:31.080><c> the</c><00:05:31.259><c> array</c><00:05:31.500><c> so</c><00:05:32.220><c> we</c><00:05:32.340><c> printed</c><00:05:32.699><c> out</c>

00:05:32.810 --> 00:05:32.820 align:start position:0%
analyzing the array so we printed out
 

00:05:32.820 --> 00:05:36.110 align:start position:0%
analyzing the array so we printed out
six<00:05:33.240><c> here</c><00:05:33.780><c> unordered</c><00:05:34.440><c> pairs</c><00:05:34.800><c> but</c><00:05:35.400><c> we</c><00:05:35.699><c> missed</c>

00:05:36.110 --> 00:05:36.120 align:start position:0%
six here unordered pairs but we missed
 

00:05:36.120 --> 00:05:37.610 align:start position:0%
six here unordered pairs but we missed
some<00:05:36.300><c> we</c><00:05:36.600><c> didn't</c><00:05:36.720><c> print</c><00:05:36.960><c> out</c><00:05:37.139><c> all</c><00:05:37.380><c> of</c><00:05:37.500><c> them</c>

00:05:37.610 --> 00:05:37.620 align:start position:0%
some we didn't print out all of them
 

00:05:37.620 --> 00:05:40.070 align:start position:0%
some we didn't print out all of them
right<00:05:38.100><c> like</c><00:05:38.880><c> here</c><00:05:39.300><c> we</c><00:05:39.600><c> could</c><00:05:39.720><c> have</c><00:05:39.840><c> printed</c>

00:05:40.070 --> 00:05:40.080 align:start position:0%
right like here we could have printed
 

00:05:40.080 --> 00:05:41.990 align:start position:0%
right like here we could have printed
out<00:05:40.139><c> at</c><00:05:40.320><c> one</c><00:05:40.440><c> one</c><00:05:40.680><c> we</c><00:05:41.280><c> couldn't</c><00:05:41.460><c> print</c><00:05:41.639><c> out</c><00:05:41.759><c> two</c>

00:05:41.990 --> 00:05:42.000 align:start position:0%
out at one one we couldn't print out two
 

00:05:42.000 --> 00:05:44.930 align:start position:0%
out at one one we couldn't print out two
one<00:05:42.240><c> two</c><00:05:42.660><c> two</c><00:05:42.900><c> and</c><00:05:43.560><c> on</c><00:05:43.800><c> that</c><00:05:43.979><c> last</c><00:05:44.280><c> iteration</c>

00:05:44.930 --> 00:05:44.940 align:start position:0%
one two two and on that last iteration
 

00:05:44.940 --> 00:05:48.650 align:start position:0%
one two two and on that last iteration
or<00:05:45.660><c> I</c><00:05:45.900><c> is</c><00:05:46.080><c> equal</c><00:05:46.199><c> to</c><00:05:46.380><c> three</c><00:05:46.820><c> J</c><00:05:47.820><c> it</c><00:05:48.180><c> was</c><00:05:48.360><c> equal</c><00:05:48.479><c> to</c>

00:05:48.650 --> 00:05:48.660 align:start position:0%
or I is equal to three J it was equal to
 

00:05:48.660 --> 00:05:50.990 align:start position:0%
or I is equal to three J it was equal to
three<00:05:48.900><c> plus</c><00:05:49.080><c> one</c><00:05:49.380><c> which</c><00:05:50.160><c> is</c><00:05:50.340><c> four</c><00:05:50.520><c> so</c><00:05:50.820><c> we</c>

00:05:50.990 --> 00:05:51.000 align:start position:0%
three plus one which is four so we
 

00:05:51.000 --> 00:05:52.850 align:start position:0%
three plus one which is four so we
didn't<00:05:51.120><c> print</c><00:05:51.360><c> out</c><00:05:51.539><c> anything</c><00:05:51.900><c> here</c><00:05:52.440><c> so</c><00:05:52.680><c> we</c>

00:05:52.850 --> 00:05:52.860 align:start position:0%
didn't print out anything here so we
 

00:05:52.860 --> 00:05:54.230 align:start position:0%
didn't print out anything here so we
missed<00:05:53.220><c> out</c><00:05:53.340><c> on</c>

00:05:54.230 --> 00:05:54.240 align:start position:0%
missed out on
 

00:05:54.240 --> 00:05:58.010 align:start position:0%
missed out on
three<00:05:55.080><c> uh</c><00:05:55.380><c> three</c><00:05:55.740><c> unordered</c><00:05:56.639><c> pairs</c><00:05:56.880><c> here</c>

00:05:58.010 --> 00:05:58.020 align:start position:0%
three uh three unordered pairs here
 

00:05:58.020 --> 00:06:00.170 align:start position:0%
three uh three unordered pairs here
so<00:05:58.320><c> what</c><00:05:58.560><c> it</c><00:05:58.680><c> looks</c><00:05:58.860><c> like</c><00:05:58.919><c> to</c><00:05:59.100><c> me</c><00:05:59.280><c> is</c><00:05:59.639><c> we</c><00:06:00.060><c> have</c>

00:06:00.170 --> 00:06:00.180 align:start position:0%
so what it looks like to me is we have
 

00:06:00.180 --> 00:06:02.090 align:start position:0%
so what it looks like to me is we have
six<00:06:00.419><c> that</c><00:06:00.720><c> we</c><00:06:00.900><c> printed</c><00:06:01.199><c> six</c><00:06:01.500><c> that</c><00:06:01.740><c> we</c><00:06:01.919><c> didn't</c>

00:06:02.090 --> 00:06:02.100 align:start position:0%
six that we printed six that we didn't
 

00:06:02.100 --> 00:06:03.650 align:start position:0%
six that we printed six that we didn't
so<00:06:02.580><c> it</c><00:06:02.639><c> looks</c><00:06:02.820><c> like</c><00:06:02.880><c> we</c><00:06:03.000><c> just</c><00:06:03.120><c> print</c><00:06:03.300><c> out</c><00:06:03.479><c> about</c>

00:06:03.650 --> 00:06:03.660 align:start position:0%
so it looks like we just print out about
 

00:06:03.660 --> 00:06:07.610 align:start position:0%
so it looks like we just print out about
half<00:06:04.020><c> of</c><00:06:04.199><c> them</c><00:06:04.380><c> okay</c><00:06:04.979><c> well</c><00:06:05.460><c> we</c><00:06:06.180><c> have</c><00:06:06.419><c> uh</c><00:06:07.320><c> two</c>

00:06:07.610 --> 00:06:07.620 align:start position:0%
half of them okay well we have uh two
 

00:06:07.620 --> 00:06:11.270 align:start position:0%
half of them okay well we have uh two
two<00:06:07.979><c> Loops</c><00:06:08.400><c> we</c><00:06:09.000><c> could</c><00:06:09.180><c> you</c><00:06:09.600><c> know</c><00:06:09.660><c> big</c><00:06:09.840><c> of</c><00:06:10.080><c> n</c><00:06:10.560><c> big</c>

00:06:11.270 --> 00:06:11.280 align:start position:0%
two Loops we could you know big of n big
 

00:06:11.280 --> 00:06:14.090 align:start position:0%
two Loops we could you know big of n big
Over<00:06:11.520><c> N</c><00:06:11.820><c> or</c><00:06:12.180><c> you</c><00:06:12.300><c> could</c><00:06:12.419><c> kind</c><00:06:12.660><c> of</c><00:06:12.780><c> say</c><00:06:12.960><c> like</c><00:06:13.320><c> uh</c>

00:06:14.090 --> 00:06:14.100 align:start position:0%
Over N or you could kind of say like uh
 

00:06:14.100 --> 00:06:17.090 align:start position:0%
Over N or you could kind of say like uh
some<00:06:14.340><c> grid</c><00:06:14.699><c> here</c><00:06:14.940><c> where</c><00:06:15.720><c> we</c><00:06:16.320><c> only</c><00:06:16.440><c> print</c><00:06:16.800><c> out</c>

00:06:17.090 --> 00:06:17.100 align:start position:0%
some grid here where we only print out
 

00:06:17.100 --> 00:06:19.490 align:start position:0%
some grid here where we only print out
half<00:06:17.759><c> of</c><00:06:17.940><c> them</c><00:06:18.060><c> initially</c><00:06:18.780><c> I</c><00:06:19.080><c> could</c><00:06:19.199><c> just</c><00:06:19.320><c> say</c>

00:06:19.490 --> 00:06:19.500 align:start position:0%
half of them initially I could just say
 

00:06:19.500 --> 00:06:22.189 align:start position:0%
half of them initially I could just say
oh<00:06:19.680><c> well</c><00:06:19.919><c> this</c><00:06:20.220><c> is</c><00:06:20.400><c> Big</c><00:06:20.580><c> O</c><00:06:20.759><c> N</c><00:06:21.180><c> the</c><00:06:21.660><c> inner</c><00:06:21.960><c> loop</c>

00:06:22.189 --> 00:06:22.199 align:start position:0%
oh well this is Big O N the inner loop
 

00:06:22.199 --> 00:06:25.730 align:start position:0%
oh well this is Big O N the inner loop
is<00:06:22.380><c> Big</c><00:06:22.620><c> O</c><00:06:22.800><c> of</c><00:06:23.039><c> n</c><00:06:23.400><c> so</c><00:06:24.240><c> you</c><00:06:24.360><c> could</c><00:06:24.539><c> just</c><00:06:24.720><c> say</c><00:06:24.960><c> we</c>

00:06:25.730 --> 00:06:25.740 align:start position:0%
is Big O of n so you could just say we
 

00:06:25.740 --> 00:06:29.270 align:start position:0%
is Big O of n so you could just say we
have<00:06:25.919><c> n</c><00:06:26.639><c> times</c><00:06:27.419><c> n</c><00:06:27.900><c> but</c><00:06:28.440><c> we</c><00:06:28.620><c> only</c><00:06:28.800><c> printed</c><00:06:29.100><c> about</c>

00:06:29.270 --> 00:06:29.280 align:start position:0%
have n times n but we only printed about
 

00:06:29.280 --> 00:06:32.029 align:start position:0%
have n times n but we only printed about
half<00:06:29.580><c> of</c><00:06:29.819><c> them</c><00:06:29.940><c> so</c><00:06:30.660><c> this</c><00:06:30.840><c> is</c><00:06:30.960><c> equal</c><00:06:31.139><c> to</c><00:06:31.380><c> N</c>

00:06:32.029 --> 00:06:32.039 align:start position:0%
half of them so this is equal to N
 

00:06:32.039 --> 00:06:34.070 align:start position:0%
half of them so this is equal to N
squared<00:06:32.580><c> divided</c><00:06:32.940><c> by</c><00:06:33.120><c> two</c>

00:06:34.070 --> 00:06:34.080 align:start position:0%
squared divided by two
 

00:06:34.080 --> 00:06:37.249 align:start position:0%
squared divided by two
uh<00:06:34.979><c> the</c><00:06:35.400><c> exponent</c><00:06:35.819><c> takes</c><00:06:36.120><c> precedence</c><00:06:36.539><c> here</c><00:06:36.780><c> so</c>

00:06:37.249 --> 00:06:37.259 align:start position:0%
uh the exponent takes precedence here so
 

00:06:37.259 --> 00:06:39.969 align:start position:0%
uh the exponent takes precedence here so
we<00:06:37.500><c> get</c><00:06:37.740><c> rid</c><00:06:37.979><c> of</c><00:06:38.160><c> the</c><00:06:38.220><c> division</c><00:06:38.699><c> and</c><00:06:39.419><c> just</c><00:06:39.600><c> say</c>

00:06:39.969 --> 00:06:39.979 align:start position:0%
we get rid of the division and just say
 

00:06:39.979 --> 00:06:44.809 align:start position:0%
we get rid of the division and just say
Big<00:06:40.979><c> O</c><00:06:41.340><c> of</c><00:06:42.180><c> N</c><00:06:42.600><c> squared</c><00:06:43.319><c> now</c><00:06:44.039><c> the</c><00:06:44.460><c> inner</c><00:06:44.699><c> for</c>

00:06:44.809 --> 00:06:44.819 align:start position:0%
Big O of N squared now the inner for
 

00:06:44.819 --> 00:06:47.270 align:start position:0%
Big O of N squared now the inner for
Loop<00:06:45.120><c> isn't</c><00:06:45.360><c> exactly</c><00:06:45.660><c> big</c><00:06:45.840><c> of</c><00:06:45.960><c> N</c><00:06:46.259><c> and</c><00:06:46.919><c> you</c>

00:06:47.270 --> 00:06:47.280 align:start position:0%
Loop isn't exactly big of N and you
 

00:06:47.280 --> 00:06:49.610 align:start position:0%
Loop isn't exactly big of N and you
could<00:06:47.340><c> say</c><00:06:47.639><c> that</c><00:06:47.819><c> and</c><00:06:48.240><c> it</c><00:06:48.479><c> doesn't</c><00:06:48.600><c> matter</c><00:06:48.900><c> it</c>

00:06:49.610 --> 00:06:49.620 align:start position:0%
could say that and it doesn't matter it
 

00:06:49.620 --> 00:06:50.510 align:start position:0%
could say that and it doesn't matter it
doesn't<00:06:49.740><c> matter</c><00:06:49.860><c> if</c><00:06:50.039><c> you</c><00:06:50.160><c> just</c><00:06:50.280><c> still</c><00:06:50.400><c> say</c>

00:06:50.510 --> 00:06:50.520 align:start position:0%
doesn't matter if you just still say
 

00:06:50.520 --> 00:06:53.450 align:start position:0%
doesn't matter if you just still say
it's<00:06:50.699><c> big</c><00:06:50.880><c> of</c><00:06:51.060><c> n</c><00:06:51.300><c> right</c><00:06:51.720><c> we</c><00:06:52.620><c> went</c><00:06:52.800><c> through</c><00:06:53.100><c> an</c>

00:06:53.450 --> 00:06:53.460 align:start position:0%
it's big of n right we went through an
 

00:06:53.460 --> 00:06:56.150 align:start position:0%
it's big of n right we went through an
example<00:06:53.819><c> and</c><00:06:54.539><c> you</c><00:06:54.660><c> could</c><00:06:54.780><c> say</c><00:06:55.020><c> well</c><00:06:55.500><c> this</c>

00:06:56.150 --> 00:06:56.160 align:start position:0%
example and you could say well this
 

00:06:56.160 --> 00:06:57.890 align:start position:0%
example and you could say well this
still<00:06:56.400><c> is</c><00:06:56.639><c> Big</c><00:06:56.819><c> of</c><00:06:56.940><c> N</c><00:06:57.120><c> squared</c><00:06:57.479><c> but</c><00:06:57.660><c> we're</c><00:06:57.720><c> only</c>

00:06:57.890 --> 00:06:57.900 align:start position:0%
still is Big of N squared but we're only
 

00:06:57.900 --> 00:06:59.629 align:start position:0%
still is Big of N squared but we're only
printing<00:06:58.259><c> them</c><00:06:58.380><c> out</c><00:06:58.560><c> about</c><00:06:58.860><c> half</c><00:06:59.280><c> of</c><00:06:59.460><c> them</c>

00:06:59.629 --> 00:06:59.639 align:start position:0%
printing them out about half of them
 

00:06:59.639 --> 00:07:01.969 align:start position:0%
printing them out about half of them
okay<00:07:00.479><c> so</c><00:07:00.900><c> that's</c><00:07:01.199><c> one</c><00:07:01.440><c> way</c><00:07:01.560><c> to</c><00:07:01.680><c> do</c><00:07:01.800><c> this</c>

00:07:01.969 --> 00:07:01.979 align:start position:0%
okay so that's one way to do this
 

00:07:01.979 --> 00:07:04.730 align:start position:0%
okay so that's one way to do this
however<00:07:02.759><c> another</c><00:07:03.240><c> way</c><00:07:03.539><c> the</c><00:07:04.139><c> more</c><00:07:04.380><c> math</c>

00:07:04.730 --> 00:07:04.740 align:start position:0%
however another way the more math
 

00:07:04.740 --> 00:07:07.610 align:start position:0%
however another way the more math
oriented<00:07:05.340><c> way</c><00:07:05.520><c> to</c><00:07:05.759><c> think</c><00:07:06.060><c> about</c><00:07:06.300><c> this</c><00:07:06.660><c> is</c><00:07:07.319><c> the</c>

00:07:07.610 --> 00:07:07.620 align:start position:0%
oriented way to think about this is the
 

00:07:07.620 --> 00:07:10.370 align:start position:0%
oriented way to think about this is the
outer<00:07:07.860><c> loop</c><00:07:08.160><c> runs</c><00:07:08.580><c> n</c><00:07:09.240><c> times</c>

00:07:10.370 --> 00:07:10.380 align:start position:0%
outer loop runs n times
 

00:07:10.380 --> 00:07:12.650 align:start position:0%
outer loop runs n times
so<00:07:10.860><c> big</c><00:07:11.100><c> of</c><00:07:11.280><c> n</c><00:07:11.460><c> however</c><00:07:11.880><c> the</c><00:07:12.120><c> inner</c><00:07:12.360><c> loop</c>

00:07:12.650 --> 00:07:12.660 align:start position:0%
so big of n however the inner loop
 

00:07:12.660 --> 00:07:17.210 align:start position:0%
so big of n however the inner loop
actually<00:07:13.080><c> runs</c><00:07:13.680><c> n</c><00:07:14.280><c> minus</c><00:07:14.819><c> one</c><00:07:15.240><c> times</c><00:07:15.539><c> okay</c><00:07:16.500><c> so</c>

00:07:17.210 --> 00:07:17.220 align:start position:0%
actually runs n minus one times okay so
 

00:07:17.220 --> 00:07:19.430 align:start position:0%
actually runs n minus one times okay so
the<00:07:17.460><c> reason</c><00:07:17.580><c> is</c><00:07:17.880><c> if</c><00:07:18.360><c> n</c><00:07:18.539><c> equals</c><00:07:18.840><c> four</c><00:07:18.960><c> like</c><00:07:19.319><c> we</c>

00:07:19.430 --> 00:07:19.440 align:start position:0%
the reason is if n equals four like we
 

00:07:19.440 --> 00:07:22.010 align:start position:0%
the reason is if n equals four like we
have<00:07:19.560><c> here</c><00:07:19.800><c> the</c><00:07:20.460><c> outer</c><00:07:20.819><c> loop</c><00:07:21.060><c> runs</c><00:07:21.479><c> four</c><00:07:21.720><c> times</c>

00:07:22.010 --> 00:07:22.020 align:start position:0%
have here the outer loop runs four times
 

00:07:22.020 --> 00:07:24.770 align:start position:0%
have here the outer loop runs four times
uh<00:07:22.979><c> the</c><00:07:23.160><c> first</c><00:07:23.280><c> time</c><00:07:23.460><c> through</c><00:07:23.699><c> but</c><00:07:24.419><c> the</c><00:07:24.539><c> inner</c>

00:07:24.770 --> 00:07:24.780 align:start position:0%
uh the first time through but the inner
 

00:07:24.780 --> 00:07:27.050 align:start position:0%
uh the first time through but the inner
loop<00:07:24.960><c> only</c><00:07:25.139><c> runs</c><00:07:25.620><c> three</c><00:07:26.099><c> times</c><00:07:26.400><c> because</c><00:07:26.819><c> it</c>

00:07:27.050 --> 00:07:27.060 align:start position:0%
loop only runs three times because it
 

00:07:27.060 --> 00:07:29.330 align:start position:0%
loop only runs three times because it
starts<00:07:27.360><c> out</c><00:07:27.419><c> at</c><00:07:27.599><c> I</c><00:07:27.780><c> plus</c><00:07:27.960><c> one</c><00:07:28.319><c> all</c><00:07:29.099><c> right</c><00:07:29.160><c> so</c>

00:07:29.330 --> 00:07:29.340 align:start position:0%
starts out at I plus one all right so
 

00:07:29.340 --> 00:07:32.270 align:start position:0%
starts out at I plus one all right so
this<00:07:29.639><c> would</c><00:07:30.240><c> mean</c><00:07:30.500><c> this</c><00:07:31.500><c> is</c><00:07:31.620><c> equal</c><00:07:31.800><c> to</c><00:07:32.039><c> the</c>

00:07:32.270 --> 00:07:32.280 align:start position:0%
this would mean this is equal to the
 

00:07:32.280 --> 00:07:34.490 align:start position:0%
this would mean this is equal to the
equation<00:07:32.639><c> would</c><00:07:33.000><c> be</c><00:07:33.180><c> n</c>

00:07:34.490 --> 00:07:34.500 align:start position:0%
equation would be n
 

00:07:34.500 --> 00:07:38.210 align:start position:0%
equation would be n
times<00:07:35.120><c> n</c><00:07:36.120><c> minus</c><00:07:36.599><c> 1.</c>

00:07:38.210 --> 00:07:38.220 align:start position:0%
times n minus 1.
 

00:07:38.220 --> 00:07:39.589 align:start position:0%
times n minus 1.
okay

00:07:39.589 --> 00:07:39.599 align:start position:0%
okay
 

00:07:39.599 --> 00:07:42.290 align:start position:0%
okay
however<00:07:40.139><c> if</c><00:07:40.560><c> we</c><00:07:40.740><c> know</c><00:07:41.099><c> that</c><00:07:41.580><c> n</c><00:07:41.759><c> is</c><00:07:41.940><c> equal</c><00:07:42.120><c> to</c>

00:07:42.290 --> 00:07:42.300 align:start position:0%
however if we know that n is equal to
 

00:07:42.300 --> 00:07:45.770 align:start position:0%
however if we know that n is equal to
four<00:07:42.660><c> there</c><00:07:43.560><c> are</c><00:07:43.740><c> six</c><00:07:43.979><c> pairs</c><00:07:44.580><c> so</c><00:07:45.240><c> if</c><00:07:45.360><c> we</c><00:07:45.539><c> just</c>

00:07:45.770 --> 00:07:45.780 align:start position:0%
four there are six pairs so if we just
 

00:07:45.780 --> 00:07:49.850 align:start position:0%
four there are six pairs so if we just
plug<00:07:46.080><c> in</c><00:07:46.259><c> 4</c><00:07:46.680><c> here</c><00:07:46.979><c> we</c><00:07:47.940><c> know</c><00:07:48.120><c> that</c><00:07:48.419><c> this</c><00:07:48.599><c> is</c><00:07:48.860><c> four</c>

00:07:49.850 --> 00:07:49.860 align:start position:0%
plug in 4 here we know that this is four
 

00:07:49.860 --> 00:07:52.790 align:start position:0%
plug in 4 here we know that this is four
times<00:07:50.639><c> three</c><00:07:51.060><c> which</c><00:07:51.360><c> is</c><00:07:51.479><c> equal</c><00:07:51.660><c> to</c><00:07:51.840><c> 12</c><00:07:52.199><c> that's</c>

00:07:52.790 --> 00:07:52.800 align:start position:0%
times three which is equal to 12 that's
 

00:07:52.800 --> 00:07:55.309 align:start position:0%
times three which is equal to 12 that's
incorrect<00:07:53.580><c> because</c><00:07:54.300><c> we</c><00:07:54.599><c> have</c><00:07:54.660><c> to</c><00:07:54.780><c> divide</c><00:07:55.080><c> by</c>

00:07:55.309 --> 00:07:55.319 align:start position:0%
incorrect because we have to divide by
 

00:07:55.319 --> 00:07:58.490 align:start position:0%
incorrect because we have to divide by
two<00:07:55.680><c> so</c><00:07:56.520><c> the</c><00:07:56.880><c> full</c><00:07:57.539><c> equation</c><00:07:57.840><c> would</c><00:07:58.139><c> be</c><00:07:58.319><c> n</c>

00:07:58.490 --> 00:07:58.500 align:start position:0%
two so the full equation would be n
 

00:07:58.500 --> 00:08:02.330 align:start position:0%
two so the full equation would be n
times<00:07:58.740><c> n</c><00:07:58.979><c> minus</c><00:07:59.340><c> 1</c><00:07:59.539><c> divided</c><00:08:00.539><c> by</c><00:08:00.720><c> two</c>

00:08:02.330 --> 00:08:02.340 align:start position:0%
times n minus 1 divided by two
 

00:08:02.340 --> 00:08:05.510 align:start position:0%
times n minus 1 divided by two
and<00:08:02.819><c> then</c><00:08:03.000><c> divided</c><00:08:03.240><c> by</c><00:08:03.419><c> 2</c><00:08:03.660><c> would</c><00:08:03.900><c> be</c><00:08:04.080><c> six</c>

00:08:05.510 --> 00:08:05.520 align:start position:0%
and then divided by 2 would be six
 

00:08:05.520 --> 00:08:09.290 align:start position:0%
and then divided by 2 would be six
okay<00:08:06.120><c> so</c><00:08:06.840><c> let's</c><00:08:07.020><c> take</c><00:08:07.259><c> this</c><00:08:07.560><c> up</c><00:08:07.860><c> here</c><00:08:08.160><c> n</c><00:08:09.060><c> times</c>

00:08:09.290 --> 00:08:09.300 align:start position:0%
okay so let's take this up here n times
 

00:08:09.300 --> 00:08:13.070 align:start position:0%
okay so let's take this up here n times
n<00:08:09.539><c> minus</c><00:08:09.900><c> 1</c><00:08:10.020><c> is</c><00:08:10.380><c> just</c><00:08:10.620><c> N</c><00:08:11.039><c> squared</c><00:08:11.639><c> minus</c><00:08:12.240><c> n</c><00:08:12.479><c> over</c>

00:08:13.070 --> 00:08:13.080 align:start position:0%
n minus 1 is just N squared minus n over
 

00:08:13.080 --> 00:08:15.830 align:start position:0%
n minus 1 is just N squared minus n over
two<00:08:13.500><c> again</c><00:08:14.280><c> the</c><00:08:14.639><c> exponent</c><00:08:15.180><c> always</c><00:08:15.419><c> takes</c>

00:08:15.830 --> 00:08:15.840 align:start position:0%
two again the exponent always takes
 

00:08:15.840 --> 00:08:17.870 align:start position:0%
two again the exponent always takes
precedence<00:08:16.259><c> so</c><00:08:16.740><c> you</c><00:08:16.919><c> get</c><00:08:17.039><c> rid</c><00:08:17.220><c> of</c><00:08:17.340><c> the</c><00:08:17.400><c> minus</c><00:08:17.699><c> n</c>

00:08:17.870 --> 00:08:17.880 align:start position:0%
precedence so you get rid of the minus n
 

00:08:17.880 --> 00:08:20.270 align:start position:0%
precedence so you get rid of the minus n
get<00:08:18.419><c> rid</c><00:08:18.539><c> of</c><00:08:18.720><c> the</c><00:08:18.840><c> two</c><00:08:19.080><c> and</c><00:08:19.680><c> you're</c><00:08:19.800><c> again</c><00:08:20.039><c> just</c>

00:08:20.270 --> 00:08:20.280 align:start position:0%
get rid of the two and you're again just
 

00:08:20.280 --> 00:08:22.790 align:start position:0%
get rid of the two and you're again just
left<00:08:20.460><c> with</c><00:08:20.699><c> N</c><00:08:21.060><c> squared</c><00:08:21.599><c> okay</c><00:08:22.080><c> so</c><00:08:22.440><c> I</c><00:08:22.500><c> hope</c><00:08:22.680><c> that</c>

00:08:22.790 --> 00:08:22.800 align:start position:0%
left with N squared okay so I hope that
 

00:08:22.800 --> 00:08:25.129 align:start position:0%
left with N squared okay so I hope that
made<00:08:22.979><c> sense</c><00:08:23.180><c> in</c><00:08:24.180><c> any</c><00:08:24.360><c> situation</c><00:08:24.720><c> where</c><00:08:24.960><c> you're</c>

00:08:25.129 --> 00:08:25.139 align:start position:0%
made sense in any situation where you're
 

00:08:25.139 --> 00:08:26.450 align:start position:0%
made sense in any situation where you're
not<00:08:25.379><c> exactly</c><00:08:25.680><c> sure</c><00:08:25.800><c> what</c><00:08:25.979><c> to</c><00:08:26.160><c> think</c><00:08:26.280><c> because</c>

00:08:26.450 --> 00:08:26.460 align:start position:0%
not exactly sure what to think because
 

00:08:26.460 --> 00:08:28.550 align:start position:0%
not exactly sure what to think because
again<00:08:26.699><c> in</c><00:08:27.360><c> a</c><00:08:27.479><c> real</c><00:08:27.539><c> situation</c><00:08:27.960><c> you're</c><00:08:28.440><c> going</c>

00:08:28.550 --> 00:08:28.560 align:start position:0%
again in a real situation you're going
 

00:08:28.560 --> 00:08:30.230 align:start position:0%
again in a real situation you're going
to<00:08:28.680><c> be</c><00:08:28.740><c> nervous</c><00:08:29.099><c> you</c><00:08:29.699><c> might</c><00:08:29.819><c> not</c><00:08:30.000><c> be</c><00:08:30.120><c> able</c><00:08:30.180><c> to</c>

00:08:30.230 --> 00:08:30.240 align:start position:0%
to be nervous you might not be able to
 

00:08:30.240 --> 00:08:32.690 align:start position:0%
to be nervous you might not be able to
think<00:08:30.479><c> clearly</c><00:08:31.440><c> either</c><00:08:31.979><c> there's</c><00:08:32.339><c> a</c><00:08:32.520><c> Brute</c>

00:08:32.690 --> 00:08:32.700 align:start position:0%
think clearly either there's a Brute
 

00:08:32.700 --> 00:08:34.790 align:start position:0%
think clearly either there's a Brute
Force<00:08:33.000><c> way</c><00:08:33.180><c> on</c><00:08:33.899><c> a</c><00:08:34.080><c> lot</c><00:08:34.140><c> of</c><00:08:34.260><c> problems</c><00:08:34.500><c> you</c><00:08:34.680><c> can</c>

00:08:34.790 --> 00:08:34.800 align:start position:0%
Force way on a lot of problems you can
 

00:08:34.800 --> 00:08:36.409 align:start position:0%
Force way on a lot of problems you can
start<00:08:34.979><c> out</c><00:08:35.159><c> that</c><00:08:35.339><c> way</c><00:08:35.459><c> just</c><00:08:35.700><c> to</c><00:08:35.880><c> help</c><00:08:36.000><c> get</c><00:08:36.300><c> your</c>

00:08:36.409 --> 00:08:36.419 align:start position:0%
start out that way just to help get your
 

00:08:36.419 --> 00:08:39.649 align:start position:0%
start out that way just to help get your
mind<00:08:36.539><c> going</c><00:08:36.779><c> and</c><00:08:37.680><c> or</c><00:08:37.919><c> just</c><00:08:38.399><c> come</c><00:08:39.120><c> up</c><00:08:39.240><c> with</c><00:08:39.419><c> an</c>

00:08:39.649 --> 00:08:39.659 align:start position:0%
mind going and or just come up with an
 

00:08:39.659 --> 00:08:41.990 align:start position:0%
mind going and or just come up with an
example<00:08:40.020><c> for</c><00:08:40.380><c> a</c><00:08:40.560><c> parameter</c><00:08:41.039><c> and</c><00:08:41.640><c> then</c><00:08:41.760><c> just</c>

00:08:41.990 --> 00:08:42.000 align:start position:0%
example for a parameter and then just
 

00:08:42.000 --> 00:08:44.269 align:start position:0%
example for a parameter and then just
run<00:08:42.599><c> through</c><00:08:42.839><c> the</c><00:08:43.200><c> method</c><00:08:43.500><c> and</c><00:08:43.680><c> see</c><00:08:43.919><c> what</c><00:08:44.099><c> it</c>

00:08:44.269 --> 00:08:44.279 align:start position:0%
run through the method and see what it
 

00:08:44.279 --> 00:08:46.250 align:start position:0%
run through the method and see what it
prints<00:08:44.520><c> out</c><00:08:44.640><c> okay</c><00:08:45.000><c> or</c><00:08:45.240><c> whatever</c><00:08:45.420><c> the</c><00:08:45.959><c> result</c>

00:08:46.250 --> 00:08:46.260 align:start position:0%
prints out okay or whatever the result
 

00:08:46.260 --> 00:08:49.009 align:start position:0%
prints out okay or whatever the result
is<00:08:46.560><c> and</c><00:08:47.160><c> then</c><00:08:47.339><c> be</c><00:08:47.580><c> like</c><00:08:47.820><c> kind</c><00:08:48.360><c> of</c><00:08:48.480><c> analyze</c><00:08:48.899><c> it</c>

00:08:49.009 --> 00:08:49.019 align:start position:0%
is and then be like kind of analyze it
 

00:08:49.019 --> 00:08:51.470 align:start position:0%
is and then be like kind of analyze it
and<00:08:49.260><c> here</c><00:08:49.860><c> you</c><00:08:50.160><c> can</c><00:08:50.220><c> see</c><00:08:50.399><c> that</c><00:08:50.700><c> well</c><00:08:51.180><c> we</c><00:08:51.360><c> only</c>

00:08:51.470 --> 00:08:51.480 align:start position:0%
and here you can see that well we only
 

00:08:51.480 --> 00:08:54.110 align:start position:0%
and here you can see that well we only
printed<00:08:51.839><c> out</c><00:08:51.899><c> half</c><00:08:52.200><c> of</c><00:08:52.380><c> them</c><00:08:52.560><c> so</c><00:08:53.279><c> we</c><00:08:53.519><c> have</c><00:08:53.760><c> a</c>

00:08:54.110 --> 00:08:54.120 align:start position:0%
printed out half of them so we have a
 

00:08:54.120 --> 00:08:56.750 align:start position:0%
printed out half of them so we have a
nested<00:08:54.480><c> for</c><00:08:54.600><c> Loop</c><00:08:54.959><c> divided</c><00:08:55.680><c> by</c><00:08:55.920><c> two</c><00:08:56.100><c> it</c><00:08:56.580><c> still</c>

00:08:56.750 --> 00:08:56.760 align:start position:0%
nested for Loop divided by two it still
 

00:08:56.760 --> 00:08:59.090 align:start position:0%
nested for Loop divided by two it still
comes<00:08:57.000><c> out</c><00:08:57.060><c> to</c><00:08:57.180><c> be</c><00:08:57.240><c> N</c><00:08:57.420><c> squared</c><00:08:57.899><c> but</c><00:08:58.500><c> we</c><00:08:58.860><c> went</c>

00:08:59.090 --> 00:08:59.100 align:start position:0%
comes out to be N squared but we went
 

00:08:59.100 --> 00:09:00.230 align:start position:0%
comes out to be N squared but we went
through<00:08:59.220><c> the</c><00:08:59.399><c> process</c><00:08:59.640><c> and</c><00:08:59.880><c> now</c><00:09:00.060><c> the</c>

00:09:00.230 --> 00:09:00.240 align:start position:0%
through the process and now the
 

00:09:00.240 --> 00:09:02.870 align:start position:0%
through the process and now the
interviewer<00:09:00.660><c> knows</c><00:09:01.160><c> uh</c><00:09:02.160><c> what</c><00:09:02.519><c> we</c><00:09:02.700><c> were</c>

00:09:02.870 --> 00:09:02.880 align:start position:0%
interviewer knows uh what we were
 

00:09:02.880 --> 00:09:05.150 align:start position:0%
interviewer knows uh what we were
thinking<00:09:03.180><c> to</c><00:09:03.480><c> come</c><00:09:03.600><c> up</c><00:09:03.779><c> with</c><00:09:04.140><c> the</c><00:09:04.380><c> solution</c><00:09:04.680><c> so</c>

00:09:05.150 --> 00:09:05.160 align:start position:0%
thinking to come up with the solution so
 

00:09:05.160 --> 00:09:07.250 align:start position:0%
thinking to come up with the solution so
thank<00:09:05.399><c> you</c><00:09:05.399><c> for</c><00:09:05.820><c> watching</c><00:09:06.180><c> I</c><00:09:06.720><c> appreciate</c><00:09:07.140><c> it</c>

00:09:07.250 --> 00:09:07.260 align:start position:0%
thank you for watching I appreciate it
 

00:09:07.260 --> 00:09:10.009 align:start position:0%
thank you for watching I appreciate it
if<00:09:07.440><c> you</c><00:09:07.620><c> have</c><00:09:07.800><c> any</c><00:09:08.040><c> questions</c><00:09:08.540><c> put</c><00:09:09.540><c> them</c><00:09:09.720><c> into</c>

00:09:10.009 --> 00:09:10.019 align:start position:0%
if you have any questions put them into
 

00:09:10.019 --> 00:09:11.449 align:start position:0%
if you have any questions put them into
the<00:09:10.320><c> comments</c><00:09:10.740><c> below</c><00:09:10.980><c> or</c><00:09:11.100><c> if</c><00:09:11.279><c> you</c><00:09:11.339><c> have</c>

00:09:11.449 --> 00:09:11.459 align:start position:0%
the comments below or if you have
 

00:09:11.459 --> 00:09:14.509 align:start position:0%
the comments below or if you have
anything<00:09:11.640><c> else</c><00:09:11.940><c> that</c><00:09:12.240><c> you</c><00:09:12.480><c> would</c><00:09:12.660><c> like</c><00:09:13.320><c> to</c><00:09:14.100><c> or</c>

00:09:14.509 --> 00:09:14.519 align:start position:0%
anything else that you would like to or
 

00:09:14.519 --> 00:09:16.490 align:start position:0%
anything else that you would like to or
have<00:09:14.700><c> you</c><00:09:14.880><c> like</c><00:09:15.180><c> to</c><00:09:15.300><c> have</c><00:09:15.420><c> me</c><00:09:15.540><c> go</c><00:09:15.779><c> over</c><00:09:15.959><c> to</c><00:09:16.380><c> help</c>

00:09:16.490 --> 00:09:16.500 align:start position:0%
have you like to have me go over to help
 

00:09:16.500 --> 00:09:18.170 align:start position:0%
have you like to have me go over to help
you<00:09:16.680><c> understand</c><00:09:17.160><c> a</c><00:09:17.519><c> little</c><00:09:17.640><c> bit</c><00:09:17.760><c> better</c><00:09:17.880><c> or</c><00:09:18.060><c> at</c>

00:09:18.170 --> 00:09:18.180 align:start position:0%
you understand a little bit better or at
 

00:09:18.180 --> 00:09:20.930 align:start position:0%
you understand a little bit better or at
least<00:09:18.240><c> have</c><00:09:18.420><c> a</c><00:09:18.600><c> different</c><00:09:18.779><c> perspective</c><00:09:19.440><c> okay</c>

00:09:20.930 --> 00:09:20.940 align:start position:0%
least have a different perspective okay
 

00:09:20.940 --> 00:09:21.769 align:start position:0%
least have a different perspective okay
um

00:09:21.769 --> 00:09:21.779 align:start position:0%
um
 

00:09:21.779 --> 00:09:24.230 align:start position:0%
um
put<00:09:22.200><c> it</c><00:09:22.260><c> in</c><00:09:22.380><c> the</c><00:09:22.500><c> comments</c><00:09:22.800><c> below</c><00:09:23.040><c> and</c><00:09:23.760><c> I'll</c>

00:09:24.230 --> 00:09:24.240 align:start position:0%
put it in the comments below and I'll
 

00:09:24.240 --> 00:09:25.250 align:start position:0%
put it in the comments below and I'll
read<00:09:24.420><c> it</c><00:09:24.480><c> and</c><00:09:24.600><c> I'll</c><00:09:24.660><c> try</c><00:09:24.839><c> to</c><00:09:24.899><c> help</c><00:09:25.019><c> you</c><00:09:25.140><c> out</c>

00:09:25.250 --> 00:09:25.260 align:start position:0%
read it and I'll try to help you out
 

00:09:25.260 --> 00:09:26.990 align:start position:0%
read it and I'll try to help you out
okay<00:09:25.560><c> thank</c><00:09:25.980><c> you</c><00:09:26.100><c> for</c><00:09:26.220><c> watching</c><00:09:26.459><c> I'll</c><00:09:26.700><c> see</c><00:09:26.880><c> you</c>

00:09:26.990 --> 00:09:27.000 align:start position:0%
okay thank you for watching I'll see you
 

00:09:27.000 --> 00:09:29.300 align:start position:0%
okay thank you for watching I'll see you
next<00:09:27.120><c> time</c>

