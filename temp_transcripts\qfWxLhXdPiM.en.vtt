WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:02.470 align:start position:0%
 
hello<00:00:00.480><c> welcome</c><00:00:00.880><c> back</c><00:00:01.839><c> the</c><00:00:01.959><c> next</c><00:00:02.159><c> thing</c><00:00:02.280><c> I</c><00:00:02.399><c> want</c>

00:00:02.470 --> 00:00:02.480 align:start position:0%
hello welcome back the next thing I want
 

00:00:02.480 --> 00:00:05.030 align:start position:0%
hello welcome back the next thing I want
to<00:00:02.639><c> talk</c><00:00:02.840><c> to</c><00:00:02.960><c> you</c><00:00:03.120><c> about</c><00:00:03.760><c> is</c><00:00:04.400><c> automation</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
to talk to you about is automation
 

00:00:05.040 --> 00:00:07.870 align:start position:0%
to talk to you about is automation
design<00:00:05.480><c> patterns</c><00:00:06.200><c> so</c><00:00:06.359><c> what</c><00:00:06.480><c> I</c><00:00:06.560><c> mean</c><00:00:06.799><c> by</c><00:00:07.080><c> this</c>

00:00:07.870 --> 00:00:07.880 align:start position:0%
design patterns so what I mean by this
 

00:00:07.880 --> 00:00:12.070 align:start position:0%
design patterns so what I mean by this
is<00:00:08.880><c> when</c><00:00:09.080><c> to</c><00:00:09.280><c> use</c><00:00:09.599><c> web</c><00:00:09.920><c> Hooks</c><00:00:10.519><c> and</c><00:00:10.880><c> when</c><00:00:11.440><c> to</c><00:00:11.679><c> use</c>

00:00:12.070 --> 00:00:12.080 align:start position:0%
is when to use web Hooks and when to use
 

00:00:12.080 --> 00:00:14.430 align:start position:0%
is when to use web Hooks and when to use
launch<00:00:13.080><c> because</c><00:00:13.400><c> those</c><00:00:13.519><c> are</c><00:00:13.759><c> two</c><00:00:14.000><c> types</c><00:00:14.240><c> of</c>

00:00:14.430 --> 00:00:14.440 align:start position:0%
launch because those are two types of
 

00:00:14.440 --> 00:00:16.150 align:start position:0%
launch because those are two types of
automation<00:00:15.240><c> that</c><00:00:15.360><c> you</c><00:00:15.480><c> have</c><00:00:15.559><c> learned</c><00:00:15.839><c> about</c>

00:00:16.150 --> 00:00:16.160 align:start position:0%
automation that you have learned about
 

00:00:16.160 --> 00:00:18.590 align:start position:0%
automation that you have learned about
and<00:00:16.240><c> you</c><00:00:16.359><c> might</c><00:00:16.520><c> be</c><00:00:17.199><c> wondering</c><00:00:18.199><c> when</c><00:00:18.400><c> should</c>

00:00:18.590 --> 00:00:18.600 align:start position:0%
and you might be wondering when should
 

00:00:18.600 --> 00:00:20.670 align:start position:0%
and you might be wondering when should
you<00:00:18.760><c> use</c><00:00:19.000><c> one</c><00:00:19.160><c> over</c><00:00:19.439><c> the</c><00:00:19.600><c> other</c><00:00:20.359><c> so</c><00:00:20.519><c> I'm</c><00:00:20.600><c> going</c>

00:00:20.670 --> 00:00:20.680 align:start position:0%
you use one over the other so I'm going
 

00:00:20.680 --> 00:00:23.230 align:start position:0%
you use one over the other so I'm going
to<00:00:20.800><c> talk</c><00:00:20.960><c> a</c><00:00:21.080><c> little</c><00:00:21.279><c> bit</c><00:00:21.480><c> about</c>

00:00:23.230 --> 00:00:23.240 align:start position:0%
to talk a little bit about
 

00:00:23.240 --> 00:00:26.269 align:start position:0%
to talk a little bit about
that<00:00:24.240><c> so</c><00:00:24.560><c> in</c><00:00:24.720><c> this</c><00:00:24.880><c> slide</c><00:00:25.279><c> I'm</c><00:00:25.359><c> going</c><00:00:25.480><c> to</c><00:00:25.680><c> do</c>

00:00:26.269 --> 00:00:26.279 align:start position:0%
that so in this slide I'm going to do
 

00:00:26.279 --> 00:00:28.109 align:start position:0%
that so in this slide I'm going to do
kind<00:00:26.359><c> of</c><00:00:26.519><c> a</c><00:00:26.679><c> comparison</c><00:00:27.359><c> of</c><00:00:27.560><c> web</c><00:00:27.800><c> Hooks</c><00:00:28.000><c> and</c>

00:00:28.109 --> 00:00:28.119 align:start position:0%
kind of a comparison of web Hooks and
 

00:00:28.119 --> 00:00:30.150 align:start position:0%
kind of a comparison of web Hooks and
launch<00:00:28.560><c> just</c><00:00:28.920><c> just</c><00:00:29.080><c> to</c><00:00:29.359><c> give</c><00:00:29.519><c> you</c><00:00:29.720><c> some</c>

00:00:30.150 --> 00:00:30.160 align:start position:0%
launch just just to give you some
 

00:00:30.160 --> 00:00:33.869 align:start position:0%
launch just just to give you some
background<00:00:30.599><c> and</c><00:00:30.720><c> some</c><00:00:30.960><c> grounding</c><00:00:31.920><c> so</c><00:00:32.160><c> just</c><00:00:32.640><c> to</c>

00:00:33.869 --> 00:00:33.879 align:start position:0%
background and some grounding so just to
 

00:00:33.879 --> 00:00:37.670 align:start position:0%
background and some grounding so just to
review<00:00:34.879><c> web</c><00:00:35.200><c> Hooks</c><00:00:35.600><c> and</c><00:00:36.040><c> launch</c><00:00:37.040><c> can</c><00:00:37.280><c> both</c><00:00:37.520><c> be</c>

00:00:37.670 --> 00:00:37.680 align:start position:0%
review web Hooks and launch can both be
 

00:00:37.680 --> 00:00:40.270 align:start position:0%
review web Hooks and launch can both be
triggered<00:00:38.160><c> by</c><00:00:38.320><c> an</c><00:00:38.559><c> event</c><00:00:39.280><c> and</c><00:00:39.440><c> that</c><00:00:39.680><c> event</c><00:00:40.079><c> can</c>

00:00:40.270 --> 00:00:40.280 align:start position:0%
triggered by an event and that event can
 

00:00:40.280 --> 00:00:42.709 align:start position:0%
triggered by an event and that event can
be<00:00:40.520><c> some</c><00:00:40.760><c> kind</c><00:00:40.920><c> of</c><00:00:41.239><c> action</c><00:00:41.920><c> in</c><00:00:42.039><c> the</c><00:00:42.200><c> model</c>

00:00:42.709 --> 00:00:42.719 align:start position:0%
be some kind of action in the model
 

00:00:42.719 --> 00:00:45.549 align:start position:0%
be some kind of action in the model
registry<00:00:43.719><c> such</c><00:00:43.960><c> as</c><00:00:44.360><c> adding</c><00:00:44.640><c> a</c><00:00:44.879><c> tag</c><00:00:45.160><c> or</c><00:00:45.320><c> adding</c>

00:00:45.549 --> 00:00:45.559 align:start position:0%
registry such as adding a tag or adding
 

00:00:45.559 --> 00:00:48.510 align:start position:0%
registry such as adding a tag or adding
an<00:00:45.719><c> artifact</c><00:00:46.360><c> so</c><00:00:46.520><c> on</c><00:00:46.680><c> and</c><00:00:46.879><c> so</c><00:00:47.239><c> forth</c><00:00:48.239><c> now</c><00:00:48.399><c> if</c>

00:00:48.510 --> 00:00:48.520 align:start position:0%
an artifact so on and so forth now if
 

00:00:48.520 --> 00:00:49.750 align:start position:0%
an artifact so on and so forth now if
you

00:00:49.750 --> 00:00:49.760 align:start position:0%
you
 

00:00:49.760 --> 00:00:52.990 align:start position:0%
you
recall<00:00:50.760><c> when</c><00:00:51.079><c> in</c><00:00:51.320><c> web</c><00:00:51.680><c> hooks</c><00:00:52.520><c> when</c><00:00:52.680><c> you</c><00:00:52.840><c> have</c>

00:00:52.990 --> 00:00:53.000 align:start position:0%
recall when in web hooks when you have
 

00:00:53.000 --> 00:00:57.069 align:start position:0%
recall when in web hooks when you have
an<00:00:53.680><c> event</c><00:00:54.680><c> what</c><00:00:54.879><c> happens</c><00:00:55.359><c> is</c><00:00:56.120><c> it</c><00:00:56.359><c> sends</c><00:00:56.840><c> a</c>

00:00:57.069 --> 00:00:57.079 align:start position:0%
an event what happens is it sends a
 

00:00:57.079 --> 00:01:01.830 align:start position:0%
an event what happens is it sends a
payload<00:00:57.840><c> and</c><00:00:57.960><c> a</c><00:00:58.160><c> payload</c><00:00:58.800><c> is</c><00:01:00.239><c> just</c><00:01:00.800><c> a</c><00:01:01.320><c> a</c><00:01:01.719><c> kind</c>

00:01:01.830 --> 00:01:01.840 align:start position:0%
payload and a payload is just a a kind
 

00:01:01.840 --> 00:01:03.470 align:start position:0%
payload and a payload is just a a kind
of<00:01:01.960><c> a</c><00:01:02.199><c> package</c><00:01:02.480><c> of</c><00:01:02.680><c> information</c><00:01:03.239><c> you</c><00:01:03.320><c> can</c>

00:01:03.470 --> 00:01:03.480 align:start position:0%
of a package of information you can
 

00:01:03.480 --> 00:01:06.630 align:start position:0%
of a package of information you can
think<00:01:03.640><c> of</c><00:01:03.760><c> it</c><00:01:03.920><c> as</c><00:01:04.080><c> a</c><00:01:04.720><c> Json</c><00:01:05.720><c> um</c><00:01:05.920><c> that</c><00:01:06.200><c> has</c>

00:01:06.630 --> 00:01:06.640 align:start position:0%
think of it as a Json um that has
 

00:01:06.640 --> 00:01:08.990 align:start position:0%
think of it as a Json um that has
information<00:01:07.479><c> and</c><00:01:07.600><c> the</c><00:01:07.759><c> information</c><00:01:08.640><c> are</c>

00:01:08.990 --> 00:01:09.000 align:start position:0%
information and the information are
 

00:01:09.000 --> 00:01:12.590 align:start position:0%
information and the information are
things<00:01:09.439><c> related</c><00:01:10.280><c> to</c><00:01:11.280><c> uh</c><00:01:11.439><c> weights</c><00:01:11.759><c> and</c><00:01:11.960><c> biases</c>

00:01:12.590 --> 00:01:12.600 align:start position:0%
things related to uh weights and biases
 

00:01:12.600 --> 00:01:15.230 align:start position:0%
things related to uh weights and biases
metadata<00:01:13.560><c> in</c><00:01:13.960><c> in</c><00:01:14.280><c> in</c><00:01:14.560><c> the</c>

00:01:15.230 --> 00:01:15.240 align:start position:0%
metadata in in in the
 

00:01:15.240 --> 00:01:17.190 align:start position:0%
metadata in in in the
artifact<00:01:16.240><c> in</c><00:01:16.400><c> the</c>

00:01:17.190 --> 00:01:17.200 align:start position:0%
artifact in the
 

00:01:17.200 --> 00:01:18.789 align:start position:0%
artifact in the
registry

00:01:18.789 --> 00:01:18.799 align:start position:0%
registry
 

00:01:18.799 --> 00:01:22.590 align:start position:0%
registry
and<00:01:19.799><c> your</c><00:01:20.320><c> web</c><00:01:20.600><c> server</c><00:01:21.280><c> receives</c><00:01:22.000><c> this</c><00:01:22.240><c> web</c>

00:01:22.590 --> 00:01:22.600 align:start position:0%
and your web server receives this web
 

00:01:22.600 --> 00:01:25.230 align:start position:0%
and your web server receives this web
hook<00:01:23.600><c> and</c><00:01:23.720><c> we</c><00:01:23.799><c> can</c><00:01:24.079><c> call</c><00:01:24.320><c> this</c><00:01:24.560><c> web</c><00:01:24.759><c> server</c><00:01:25.079><c> a</c>

00:01:25.230 --> 00:01:25.240 align:start position:0%
hook and we can call this web server a
 

00:01:25.240 --> 00:01:27.710 align:start position:0%
hook and we can call this web server a
consumer<00:01:26.040><c> now</c><00:01:26.200><c> it's</c><00:01:26.320><c> not</c><00:01:26.600><c> always</c><00:01:27.079><c> strictly</c><00:01:27.520><c> a</c>

00:01:27.710 --> 00:01:27.720 align:start position:0%
consumer now it's not always strictly a
 

00:01:27.720 --> 00:01:31.550 align:start position:0%
consumer now it's not always strictly a
web<00:01:28.439><c> server</c><00:01:29.439><c> that's</c><00:01:29.600><c> why</c><00:01:29.960><c> called</c><00:01:30.159><c> it</c><00:01:30.400><c> consumer</c>

00:01:31.550 --> 00:01:31.560 align:start position:0%
web server that's why called it consumer
 

00:01:31.560 --> 00:01:35.469 align:start position:0%
web server that's why called it consumer
because<00:01:32.560><c> web</c><00:01:32.880><c> hooks</c><00:01:33.439><c> are</c><00:01:33.920><c> very</c><00:01:34.320><c> common</c><00:01:35.320><c> when</c>

00:01:35.469 --> 00:01:35.479 align:start position:0%
because web hooks are very common when
 

00:01:35.479 --> 00:01:38.350 align:start position:0%
because web hooks are very common when
we<00:01:35.640><c> talk</c><00:01:35.840><c> about</c><00:01:36.079><c> developer</c><00:01:36.560><c> tools</c><00:01:36.960><c> so</c><00:01:37.240><c> one</c><00:01:38.240><c> uh</c>

00:01:38.350 --> 00:01:38.360 align:start position:0%
we talk about developer tools so one uh
 

00:01:38.360 --> 00:01:40.429 align:start position:0%
we talk about developer tools so one uh
there's<00:01:38.640><c> many</c><00:01:38.960><c> different</c><00:01:39.360><c> tools</c><00:01:40.079><c> that</c><00:01:40.240><c> can</c>

00:01:40.429 --> 00:01:40.439 align:start position:0%
there's many different tools that can
 

00:01:40.439 --> 00:01:41.950 align:start position:0%
there's many different tools that can
receive<00:01:40.759><c> a</c><00:01:40.920><c> web</c><00:01:41.159><c> hook</c><00:01:41.399><c> doesn't</c><00:01:41.680><c> have</c><00:01:41.759><c> to</c>

00:01:41.950 --> 00:01:41.960 align:start position:0%
receive a web hook doesn't have to
 

00:01:41.960 --> 00:01:45.709 align:start position:0%
receive a web hook doesn't have to
strictly<00:01:42.360><c> be</c><00:01:42.479><c> a</c><00:01:42.640><c> web</c><00:01:43.240><c> server</c><00:01:44.360><c> and</c><00:01:45.360><c> uh</c><00:01:45.479><c> one</c>

00:01:45.709 --> 00:01:45.719 align:start position:0%
strictly be a web server and uh one
 

00:01:45.719 --> 00:01:47.630 align:start position:0%
strictly be a web server and uh one
example<00:01:46.119><c> of</c><00:01:46.240><c> that</c><00:01:46.320><c> is</c><00:01:46.520><c> GitHub</c><00:01:46.880><c> actions</c><00:01:47.520><c> you</c>

00:01:47.630 --> 00:01:47.640 align:start position:0%
example of that is GitHub actions you
 

00:01:47.640 --> 00:01:51.429 align:start position:0%
example of that is GitHub actions you
can<00:01:48.040><c> have</c><00:01:49.040><c> weights</c><00:01:49.320><c> and</c><00:01:49.479><c> biases</c><00:01:50.479><c> send</c><00:01:51.000><c> events</c>

00:01:51.429 --> 00:01:51.439 align:start position:0%
can have weights and biases send events
 

00:01:51.439 --> 00:01:52.709 align:start position:0%
can have weights and biases send events
to<00:01:51.759><c> GitHub</c>

00:01:52.709 --> 00:01:52.719 align:start position:0%
to GitHub
 

00:01:52.719 --> 00:01:56.749 align:start position:0%
to GitHub
actions<00:01:53.880><c> now</c><00:01:54.880><c> with</c><00:01:55.119><c> web</c><00:01:55.399><c> hooks</c><00:01:56.079><c> you</c><00:01:56.280><c> are</c>

00:01:56.749 --> 00:01:56.759 align:start position:0%
actions now with web hooks you are
 

00:01:56.759 --> 00:01:59.469 align:start position:0%
actions now with web hooks you are
responsible<00:01:57.759><c> for</c><00:01:58.159><c> handling</c><00:01:58.680><c> the</c><00:01:58.880><c> queue</c><00:01:59.360><c> and</c>

00:01:59.469 --> 00:01:59.479 align:start position:0%
responsible for handling the queue and
 

00:01:59.479 --> 00:02:03.910 align:start position:0%
responsible for handling the queue and
what<00:01:59.600><c> is</c><00:02:00.039><c> q</c><00:02:00.799><c> q</c><00:02:01.159><c> is</c><00:02:01.439><c> just</c><00:02:02.399><c> kind</c><00:02:02.560><c> of</c><00:02:02.799><c> a</c><00:02:03.119><c> a</c><00:02:03.280><c> holding</c>

00:02:03.910 --> 00:02:03.920 align:start position:0%
what is q q is just kind of a a holding
 

00:02:03.920 --> 00:02:06.830 align:start position:0%
what is q q is just kind of a a holding
area<00:02:04.920><c> where</c><00:02:05.200><c> you</c><00:02:05.479><c> have</c><00:02:05.880><c> all</c><00:02:06.039><c> of</c><00:02:06.200><c> your</c><00:02:06.479><c> jobs</c>

00:02:06.830 --> 00:02:06.840 align:start position:0%
area where you have all of your jobs
 

00:02:06.840 --> 00:02:10.390 align:start position:0%
area where you have all of your jobs
that<00:02:06.960><c> you</c><00:02:07.039><c> want</c><00:02:07.200><c> to</c><00:02:07.599><c> execute</c><00:02:08.599><c> and</c><00:02:08.720><c> so</c><00:02:09.039><c> with</c><00:02:09.280><c> web</c>

00:02:10.390 --> 00:02:10.400 align:start position:0%
that you want to execute and so with web
 

00:02:10.400 --> 00:02:13.110 align:start position:0%
that you want to execute and so with web
hooks<00:02:11.400><c> weights</c><00:02:11.680><c> and</c><00:02:11.800><c> biases</c><00:02:12.319><c> fires</c><00:02:12.720><c> the</c><00:02:12.879><c> web</c>

00:02:13.110 --> 00:02:13.120 align:start position:0%
hooks weights and biases fires the web
 

00:02:13.120 --> 00:02:15.430 align:start position:0%
hooks weights and biases fires the web
hooks<00:02:14.040><c> gives</c><00:02:14.400><c> you</c><00:02:14.720><c> get</c><00:02:14.879><c> you</c><00:02:15.000><c> receive</c><00:02:15.319><c> the</c>

00:02:15.430 --> 00:02:15.440 align:start position:0%
hooks gives you get you receive the
 

00:02:15.440 --> 00:02:17.949 align:start position:0%
hooks gives you get you receive the
payload<00:02:16.400><c> and</c><00:02:16.560><c> then</c><00:02:16.800><c> you</c><00:02:16.920><c> know</c><00:02:17.160><c> if</c><00:02:17.400><c> if</c><00:02:17.519><c> you're</c>

00:02:17.949 --> 00:02:17.959 align:start position:0%
payload and then you know if if you're
 

00:02:17.959 --> 00:02:20.190 align:start position:0%
payload and then you know if if you're
receiving<00:02:18.200><c> many</c><00:02:18.519><c> different</c><00:02:19.040><c> payloads</c><00:02:20.040><c> it's</c>

00:02:20.190 --> 00:02:20.200 align:start position:0%
receiving many different payloads it's
 

00:02:20.200 --> 00:02:23.350 align:start position:0%
receiving many different payloads it's
up<00:02:20.360><c> to</c><00:02:20.560><c> you</c><00:02:20.800><c> to</c><00:02:21.200><c> implement</c><00:02:22.200><c> how</c><00:02:22.480><c> to</c><00:02:22.959><c> process</c>

00:02:23.350 --> 00:02:23.360 align:start position:0%
up to you to implement how to process
 

00:02:23.360 --> 00:02:24.229 align:start position:0%
up to you to implement how to process
those

00:02:24.229 --> 00:02:24.239 align:start position:0%
those
 

00:02:24.239 --> 00:02:27.990 align:start position:0%
those
payloads<00:02:25.239><c> uh</c><00:02:25.800><c> you</c><00:02:25.959><c> know</c><00:02:26.280><c> in</c><00:02:26.480><c> what</c><00:02:26.800><c> order</c><00:02:27.800><c> and</c>

00:02:27.990 --> 00:02:28.000 align:start position:0%
payloads uh you know in what order and
 

00:02:28.000 --> 00:02:30.390 align:start position:0%
payloads uh you know in what order and
keeping<00:02:28.400><c> track</c><00:02:28.720><c> of</c><00:02:28.959><c> those</c><00:02:29.200><c> payloads</c>

00:02:30.390 --> 00:02:30.400 align:start position:0%
keeping track of those payloads
 

00:02:30.400 --> 00:02:32.750 align:start position:0%
keeping track of those payloads
and<00:02:30.800><c> really</c><00:02:31.200><c> like</c><00:02:31.640><c> the</c><00:02:31.840><c> the</c><00:02:31.959><c> idea</c><00:02:32.239><c> behind</c><00:02:32.480><c> web</c>

00:02:32.750 --> 00:02:32.760 align:start position:0%
and really like the the idea behind web
 

00:02:32.760 --> 00:02:35.949 align:start position:0%
and really like the the idea behind web
hooks<00:02:33.040><c> is</c><00:02:33.200><c> not</c><00:02:33.959><c> really</c><00:02:34.959><c> to</c><00:02:35.239><c> have</c><00:02:35.360><c> your</c><00:02:35.480><c> own</c><00:02:35.720><c> web</c>

00:02:35.949 --> 00:02:35.959 align:start position:0%
hooks is not really to have your own web
 

00:02:35.959 --> 00:02:39.470 align:start position:0%
hooks is not really to have your own web
server<00:02:36.879><c> to</c><00:02:37.560><c> and</c><00:02:37.800><c> and</c><00:02:38.560><c> uh</c><00:02:38.720><c> manage</c><00:02:39.080><c> all</c><00:02:39.319><c> that</c>

00:02:39.470 --> 00:02:39.480 align:start position:0%
server to and and uh manage all that
 

00:02:39.480 --> 00:02:42.550 align:start position:0%
server to and and uh manage all that
yourself<00:02:40.040><c> most</c><00:02:40.200><c> of</c><00:02:40.360><c> the</c><00:02:40.720><c> time</c><00:02:41.720><c> the</c><00:02:41.879><c> real</c><00:02:42.159><c> idea</c>

00:02:42.550 --> 00:02:42.560 align:start position:0%
yourself most of the time the real idea
 

00:02:42.560 --> 00:02:44.710 align:start position:0%
yourself most of the time the real idea
is<00:02:42.840><c> to</c><00:02:43.040><c> integrate</c><00:02:43.440><c> with</c><00:02:43.599><c> third</c><00:02:43.840><c> parties</c><00:02:44.560><c> uh</c>

00:02:44.710 --> 00:02:44.720 align:start position:0%
is to integrate with third parties uh
 

00:02:44.720 --> 00:02:48.830 align:start position:0%
is to integrate with third parties uh
third<00:02:45.000><c> party</c><00:02:45.280><c> software</c><00:02:45.720><c> like</c><00:02:46.680><c> GitHub</c><00:02:47.440><c> actions</c>

00:02:48.830 --> 00:02:48.840 align:start position:0%
third party software like GitHub actions
 

00:02:48.840 --> 00:02:50.949 align:start position:0%
third party software like GitHub actions
and<00:02:49.840><c> the</c><00:02:50.000><c> other</c><00:02:50.200><c> thing</c><00:02:50.319><c> you</c><00:02:50.400><c> can</c><00:02:50.480><c> do</c><00:02:50.599><c> with</c><00:02:50.760><c> web</c>

00:02:50.949 --> 00:02:50.959 align:start position:0%
and the other thing you can do with web
 

00:02:50.959 --> 00:02:52.910 align:start position:0%
and the other thing you can do with web
hooks<00:02:51.200><c> is</c><00:02:51.319><c> you</c><00:02:51.400><c> can</c><00:02:51.599><c> build</c><00:02:51.959><c> tools</c><00:02:52.440><c> on</c><00:02:52.640><c> top</c><00:02:52.800><c> of</c>

00:02:52.910 --> 00:02:52.920 align:start position:0%
hooks is you can build tools on top of
 

00:02:52.920 --> 00:02:54.589 align:start position:0%
hooks is you can build tools on top of
weights<00:02:53.120><c> and</c><00:02:53.280><c> biases</c><00:02:53.720><c> so</c><00:02:53.920><c> web</c><00:02:54.080><c> books</c><00:02:54.280><c> is</c><00:02:54.519><c> kind</c>

00:02:54.589 --> 00:02:54.599 align:start position:0%
weights and biases so web books is kind
 

00:02:54.599 --> 00:02:56.910 align:start position:0%
weights and biases so web books is kind
of<00:02:54.720><c> a</c><00:02:54.879><c> general</c><00:02:55.440><c> purpose</c>

00:02:56.910 --> 00:02:56.920 align:start position:0%
of a general purpose
 

00:02:56.920 --> 00:03:00.509 align:start position:0%
of a general purpose
flexible<00:02:58.000><c> um</c><00:02:59.000><c> kind</c><00:02:59.120><c> of</c><00:02:59.599><c> par</c><00:02:59.840><c> Paradigm</c><00:03:00.319><c> that</c>

00:03:00.509 --> 00:03:00.519 align:start position:0%
flexible um kind of par Paradigm that
 

00:03:00.519 --> 00:03:03.229 align:start position:0%
flexible um kind of par Paradigm that
allows<00:03:00.879><c> you</c><00:03:01.239><c> to</c><00:03:02.239><c> kind</c><00:03:02.360><c> of</c><00:03:02.560><c> do</c><00:03:02.800><c> anything</c><00:03:03.120><c> you</c>

00:03:03.229 --> 00:03:03.239 align:start position:0%
allows you to kind of do anything you
 

00:03:03.239 --> 00:03:05.710 align:start position:0%
allows you to kind of do anything you
want<00:03:03.480><c> is</c><00:03:03.680><c> very</c><00:03:04.120><c> flexible</c><00:03:05.120><c> you</c><00:03:05.239><c> know</c><00:03:05.440><c> you</c><00:03:05.599><c> have</c>

00:03:05.710 --> 00:03:05.720 align:start position:0%
want is very flexible you know you have
 

00:03:05.720 --> 00:03:07.869 align:start position:0%
want is very flexible you know you have
to<00:03:05.959><c> catch</c><00:03:06.280><c> this</c><00:03:06.480><c> payload</c><00:03:06.959><c> and</c><00:03:07.080><c> based</c><00:03:07.319><c> on</c><00:03:07.440><c> the</c>

00:03:07.869 --> 00:03:07.879 align:start position:0%
to catch this payload and based on the
 

00:03:07.879 --> 00:03:10.750 align:start position:0%
to catch this payload and based on the
payload<00:03:08.879><c> you</c><00:03:09.159><c> decide</c><00:03:09.519><c> what</c><00:03:09.640><c> to</c><00:03:09.799><c> do</c><00:03:10.519><c> um</c><00:03:10.640><c> you</c>

00:03:10.750 --> 00:03:10.760 align:start position:0%
payload you decide what to do um you
 

00:03:10.760 --> 00:03:13.630 align:start position:0%
payload you decide what to do um you
know<00:03:10.959><c> you</c><00:03:11.120><c> can</c><00:03:11.400><c> download</c><00:03:11.840><c> the</c><00:03:12.400><c> artifacts</c><00:03:13.400><c> you</c>

00:03:13.630 --> 00:03:13.640 align:start position:0%
know you can download the artifacts you
 

00:03:13.640 --> 00:03:18.350 align:start position:0%
know you can download the artifacts you
can<00:03:14.599><c> you</c><00:03:14.720><c> know</c><00:03:15.000><c> do</c><00:03:15.280><c> whatever</c><00:03:15.640><c> you</c><00:03:15.879><c> need</c><00:03:16.080><c> to</c><00:03:16.599><c> do</c>

00:03:18.350 --> 00:03:18.360 align:start position:0%
can you know do whatever you need to do
 

00:03:18.360 --> 00:03:21.990 align:start position:0%
can you know do whatever you need to do
now<00:03:19.360><c> launch</c><00:03:19.840><c> is</c><00:03:19.959><c> a</c><00:03:20.080><c> bit</c><00:03:20.640><c> different</c><00:03:21.640><c> so</c><00:03:21.840><c> the</c>

00:03:21.990 --> 00:03:22.000 align:start position:0%
now launch is a bit different so the
 

00:03:22.000 --> 00:03:24.630 align:start position:0%
now launch is a bit different so the
idea<00:03:22.560><c> is</c><00:03:22.760><c> that</c><00:03:23.319><c> in</c><00:03:23.519><c> weights</c><00:03:23.799><c> and</c>

00:03:24.630 --> 00:03:24.640 align:start position:0%
idea is that in weights and
 

00:03:24.640 --> 00:03:28.509 align:start position:0%
idea is that in weights and
biases<00:03:25.640><c> you</c><00:03:26.200><c> in</c><00:03:26.440><c> response</c><00:03:26.799><c> to</c><00:03:26.959><c> an</c><00:03:27.280><c> event</c><00:03:28.280><c> a</c><00:03:28.400><c> lot</c>

00:03:28.509 --> 00:03:28.519 align:start position:0%
biases you in response to an event a lot
 

00:03:28.519 --> 00:03:30.670 align:start position:0%
biases you in response to an event a lot
of<00:03:28.640><c> times</c><00:03:28.879><c> you</c><00:03:29.000><c> want</c><00:03:29.159><c> to</c><00:03:29.360><c> run</c><00:03:29.840><c> code</c><00:03:30.400><c> and</c><00:03:30.519><c> you</c>

00:03:30.670 --> 00:03:30.680 align:start position:0%
of times you want to run code and you
 

00:03:30.680 --> 00:03:32.229 align:start position:0%
of times you want to run code and you
want<00:03:30.879><c> and</c><00:03:31.159><c> specifically</c><00:03:31.720><c> you</c><00:03:31.840><c> might</c><00:03:32.000><c> want</c><00:03:32.120><c> to</c>

00:03:32.229 --> 00:03:32.239 align:start position:0%
want and specifically you might want to
 

00:03:32.239 --> 00:03:35.670 align:start position:0%
want and specifically you might want to
run<00:03:32.519><c> training</c><00:03:32.959><c> code</c><00:03:33.239><c> or</c><00:03:33.480><c> evaluation</c><00:03:34.519><c> code</c><00:03:35.519><c> um</c>

00:03:35.670 --> 00:03:35.680 align:start position:0%
run training code or evaluation code um
 

00:03:35.680 --> 00:03:37.110 align:start position:0%
run training code or evaluation code um
and<00:03:35.799><c> you</c><00:03:35.879><c> might</c><00:03:36.080><c> want</c><00:03:36.200><c> to</c><00:03:36.599><c> you</c><00:03:36.680><c> know</c><00:03:36.840><c> do</c><00:03:37.000><c> a</c>

00:03:37.110 --> 00:03:37.120 align:start position:0%
and you might want to you know do a
 

00:03:37.120 --> 00:03:39.429 align:start position:0%
and you might want to you know do a
training<00:03:37.439><c> run</c><00:03:38.000><c> and</c><00:03:38.239><c> you</c><00:03:38.360><c> know</c><00:03:38.680><c> instead</c><00:03:39.040><c> of</c>

00:03:39.429 --> 00:03:39.439 align:start position:0%
training run and you know instead of
 

00:03:39.439 --> 00:03:41.869 align:start position:0%
training run and you know instead of
having<00:03:39.680><c> to</c><00:03:39.879><c> orchestrate</c><00:03:40.480><c> this</c><00:03:40.680><c> yourself</c><00:03:41.680><c> for</c>

00:03:41.869 --> 00:03:41.879 align:start position:0%
having to orchestrate this yourself for
 

00:03:41.879 --> 00:03:45.470 align:start position:0%
having to orchestrate this yourself for
example<00:03:42.680><c> having</c><00:03:42.879><c> a</c><00:03:43.080><c> web</c><00:03:43.400><c> server</c><00:03:44.480><c> catching</c>

00:03:45.470 --> 00:03:45.480 align:start position:0%
example having a web server catching
 

00:03:45.480 --> 00:03:48.710 align:start position:0%
example having a web server catching
this<00:03:45.799><c> kind</c><00:03:45.959><c> of</c><00:03:46.560><c> a</c><00:03:46.720><c> payload</c><00:03:47.480><c> and</c><00:03:47.680><c> then</c><00:03:48.280><c> managing</c>

00:03:48.710 --> 00:03:48.720 align:start position:0%
this kind of a payload and then managing
 

00:03:48.720 --> 00:03:52.270 align:start position:0%
this kind of a payload and then managing
your<00:03:48.879><c> own</c><00:03:49.159><c> queue</c><00:03:50.080><c> and</c><00:03:50.280><c> then</c><00:03:50.879><c> uh</c><00:03:51.280><c> finally</c>

00:03:52.270 --> 00:03:52.280 align:start position:0%
your own queue and then uh finally
 

00:03:52.280 --> 00:03:54.110 align:start position:0%
your own queue and then uh finally
running<00:03:52.720><c> some</c><00:03:53.000><c> code</c><00:03:53.519><c> let's</c><00:03:53.760><c> say</c><00:03:53.920><c> for</c><00:03:54.040><c> a</c>

00:03:54.110 --> 00:03:54.120 align:start position:0%
running some code let's say for a
 

00:03:54.120 --> 00:03:56.509 align:start position:0%
running some code let's say for a
training<00:03:54.560><c> evaluation</c><00:03:55.040><c> run</c><00:03:55.560><c> launch</c><00:03:56.079><c> packages</c>

00:03:56.509 --> 00:03:56.519 align:start position:0%
training evaluation run launch packages
 

00:03:56.519 --> 00:03:59.149 align:start position:0%
training evaluation run launch packages
that<00:03:56.720><c> up</c><00:03:57.040><c> very</c><00:03:57.280><c> nicely</c><00:03:58.200><c> so</c><00:03:58.480><c> you</c><00:03:58.599><c> don't</c><00:03:58.879><c> have</c>

00:03:59.149 --> 00:03:59.159 align:start position:0%
that up very nicely so you don't have
 

00:03:59.159 --> 00:04:01.149 align:start position:0%
that up very nicely so you don't have
you<00:03:59.280><c> have</c><00:03:59.360><c> to</c><00:03:59.439><c> do</c><00:03:59.879><c> l</c><00:04:00.200><c> so</c><00:04:00.480><c> in</c><00:04:00.599><c> the</c><00:04:00.760><c> case</c><00:04:01.040><c> where</c>

00:04:01.149 --> 00:04:01.159 align:start position:0%
you have to do l so in the case where
 

00:04:01.159 --> 00:04:03.030 align:start position:0%
you have to do l so in the case where
you<00:04:01.280><c> want</c><00:04:01.439><c> to</c><00:04:01.959><c> do</c><00:04:02.120><c> a</c><00:04:02.239><c> training</c><00:04:02.599><c> run</c><00:04:02.840><c> or</c>

00:04:03.030 --> 00:04:03.040 align:start position:0%
you want to do a training run or
 

00:04:03.040 --> 00:04:05.789 align:start position:0%
you want to do a training run or
evaluation<00:04:03.640><c> run</c><00:04:04.599><c> the</c><00:04:04.680><c> way</c><00:04:04.840><c> that</c><00:04:05.000><c> launch</c><00:04:05.360><c> works</c>

00:04:05.789 --> 00:04:05.799 align:start position:0%
evaluation run the way that launch works
 

00:04:05.799 --> 00:04:09.710 align:start position:0%
evaluation run the way that launch works
is<00:04:06.680><c> when</c><00:04:06.879><c> an</c><00:04:07.120><c> event</c><00:04:07.439><c> is</c><00:04:07.799><c> triggered</c><00:04:08.799><c> that</c><00:04:09.200><c> event</c>

00:04:09.710 --> 00:04:09.720 align:start position:0%
is when an event is triggered that event
 

00:04:09.720 --> 00:04:12.190 align:start position:0%
is when an event is triggered that event
is<00:04:09.959><c> given</c><00:04:10.519><c> is</c><00:04:10.680><c> put</c><00:04:10.920><c> in</c><00:04:11.040><c> a</c><00:04:11.239><c> Quee</c><00:04:11.799><c> and</c><00:04:11.920><c> weights</c>

00:04:12.190 --> 00:04:12.200 align:start position:0%
is given is put in a Quee and weights
 

00:04:12.200 --> 00:04:16.030 align:start position:0%
is given is put in a Quee and weights
and<00:04:12.360><c> biases</c><00:04:13.239><c> has</c><00:04:13.560><c> this</c><00:04:14.040><c> cue</c><00:04:15.040><c> and</c><00:04:15.239><c> essentially</c>

00:04:16.030 --> 00:04:16.040 align:start position:0%
and biases has this cue and essentially
 

00:04:16.040 --> 00:04:19.189 align:start position:0%
and biases has this cue and essentially
uh<00:04:16.160><c> what</c><00:04:16.280><c> you</c><00:04:16.400><c> do</c><00:04:16.720><c> is</c><00:04:17.519><c> on</c><00:04:18.199><c> the</c><00:04:18.440><c> compute</c><00:04:18.840><c> of</c><00:04:19.000><c> your</c>

00:04:19.189 --> 00:04:19.199 align:start position:0%
uh what you do is on the compute of your
 

00:04:19.199 --> 00:04:22.189 align:start position:0%
uh what you do is on the compute of your
choice<00:04:19.759><c> whether</c><00:04:20.079><c> that's</c><00:04:20.840><c> a</c><00:04:21.000><c> VM</c><00:04:21.680><c> a</c><00:04:21.840><c> local</c>

00:04:22.189 --> 00:04:22.199 align:start position:0%
choice whether that's a VM a local
 

00:04:22.199 --> 00:04:23.430 align:start position:0%
choice whether that's a VM a local
machine

00:04:23.430 --> 00:04:23.440 align:start position:0%
machine
 

00:04:23.440 --> 00:04:25.590 align:start position:0%
machine
kubernetes<00:04:24.440><c> so</c><00:04:24.600><c> on</c><00:04:24.759><c> and</c><00:04:24.880><c> so</c><00:04:25.040><c> forth</c><00:04:25.320><c> wherever</c>

00:04:25.590 --> 00:04:25.600 align:start position:0%
kubernetes so on and so forth wherever
 

00:04:25.600 --> 00:04:27.870 align:start position:0%
kubernetes so on and so forth wherever
your<00:04:25.800><c> compute</c><00:04:26.199><c> is</c><00:04:26.720><c> you</c><00:04:26.919><c> run</c><00:04:27.240><c> agents</c><00:04:27.680><c> and</c>

00:04:27.870 --> 00:04:27.880 align:start position:0%
your compute is you run agents and
 

00:04:27.880 --> 00:04:30.230 align:start position:0%
your compute is you run agents and
agents<00:04:28.360><c> consume</c><00:04:28.960><c> from</c><00:04:29.120><c> the</c><00:04:29.280><c> queue</c><00:04:29.759><c> so</c><00:04:29.919><c> it's</c><00:04:30.039><c> a</c>

00:04:30.230 --> 00:04:30.240 align:start position:0%
agents consume from the queue so it's a
 

00:04:30.240 --> 00:04:31.990 align:start position:0%
agents consume from the queue so it's a
kind<00:04:30.360><c> of</c><00:04:30.479><c> a</c><00:04:30.639><c> different</c><00:04:31.000><c> consumer</c><00:04:31.720><c> it's</c>

00:04:31.990 --> 00:04:32.000 align:start position:0%
kind of a different consumer it's
 

00:04:32.000 --> 00:04:35.469 align:start position:0%
kind of a different consumer it's
constantly<00:04:33.000><c> pulling</c><00:04:33.479><c> the</c><00:04:33.680><c> queue</c><00:04:34.680><c> and</c><00:04:35.199><c> it's</c>

00:04:35.469 --> 00:04:35.479 align:start position:0%
constantly pulling the queue and it's
 

00:04:35.479 --> 00:04:36.790 align:start position:0%
constantly pulling the queue and it's
grabbing<00:04:35.919><c> things</c><00:04:36.120><c> from</c><00:04:36.240><c> the</c><00:04:36.400><c> queue</c><00:04:36.639><c> so</c><00:04:36.720><c> you</c>

00:04:36.790 --> 00:04:36.800 align:start position:0%
grabbing things from the queue so you
 

00:04:36.800 --> 00:04:39.310 align:start position:0%
grabbing things from the queue so you
don't<00:04:36.919><c> have</c><00:04:37.039><c> to</c><00:04:37.120><c> manage</c><00:04:37.479><c> the</c><00:04:37.880><c> queue</c><00:04:38.880><c> um</c><00:04:39.039><c> things</c>

00:04:39.310 --> 00:04:39.320 align:start position:0%
don't have to manage the queue um things
 

00:04:39.320 --> 00:04:41.990 align:start position:0%
don't have to manage the queue um things
can<00:04:39.479><c> get</c><00:04:39.639><c> pushed</c><00:04:39.880><c> into</c><00:04:40.120><c> the</c><00:04:40.280><c> queue</c><00:04:40.919><c> and</c><00:04:41.199><c> agents</c>

00:04:41.990 --> 00:04:42.000 align:start position:0%
can get pushed into the queue and agents
 

00:04:42.000 --> 00:04:44.390 align:start position:0%
can get pushed into the queue and agents
can<00:04:43.000><c> can</c><00:04:43.199><c> kind</c><00:04:43.320><c> of</c><00:04:43.479><c> pull</c><00:04:43.720><c> from</c><00:04:43.919><c> the</c><00:04:44.039><c> queue</c><00:04:44.280><c> and</c>

00:04:44.390 --> 00:04:44.400 align:start position:0%
can can kind of pull from the queue and
 

00:04:44.400 --> 00:04:46.550 align:start position:0%
can can kind of pull from the queue and
that's<00:04:44.600><c> very</c><00:04:44.880><c> con</c><00:04:45.320><c> convenient</c><00:04:46.039><c> and</c><00:04:46.120><c> the</c><00:04:46.280><c> idea</c>

00:04:46.550 --> 00:04:46.560 align:start position:0%
that's very con convenient and the idea
 

00:04:46.560 --> 00:04:48.909 align:start position:0%
that's very con convenient and the idea
behind<00:04:46.800><c> launch</c><00:04:47.240><c> is</c><00:04:47.560><c> is</c><00:04:47.840><c> again</c><00:04:48.440><c> it's</c><00:04:48.600><c> meant</c><00:04:48.800><c> for</c>

00:04:48.909 --> 00:04:48.919 align:start position:0%
behind launch is is again it's meant for
 

00:04:48.919 --> 00:04:52.710 align:start position:0%
behind launch is is again it's meant for
training<00:04:49.240><c> and</c><00:04:49.400><c> eval</c><00:04:49.880><c> runs</c><00:04:51.360><c> and</c><00:04:52.360><c> you</c><00:04:52.479><c> know</c>

00:04:52.710 --> 00:04:52.720 align:start position:0%
training and eval runs and you know
 

00:04:52.720 --> 00:04:55.270 align:start position:0%
training and eval runs and you know
agents<00:04:53.120><c> are</c><00:04:53.400><c> designed</c><00:04:53.880><c> to</c><00:04:54.080><c> run</c><00:04:54.440><c> Docker</c><00:04:54.960><c> or</c>

00:04:55.270 --> 00:04:55.280 align:start position:0%
agents are designed to run Docker or
 

00:04:55.280 --> 00:04:57.510 align:start position:0%
agents are designed to run Docker or
python<00:04:55.720><c> code</c><00:04:56.240><c> on</c><00:04:56.360><c> the</c><00:04:56.520><c> computer</c><00:04:56.840><c> of</c><00:04:56.960><c> your</c>

00:04:57.510 --> 00:04:57.520 align:start position:0%
python code on the computer of your
 

00:04:57.520 --> 00:05:00.150 align:start position:0%
python code on the computer of your
choice<00:04:58.520><c> and</c><00:04:59.199><c> you</c><00:04:59.320><c> know</c><00:04:59.479><c> if</c><00:04:59.680><c> if</c><00:04:59.759><c> you</c><00:04:59.800><c> need</c><00:04:59.919><c> to</c><00:05:00.039><c> do</c>

00:05:00.150 --> 00:05:00.160 align:start position:0%
choice and you know if if you need to do
 

00:05:00.160 --> 00:05:03.670 align:start position:0%
choice and you know if if you need to do
a<00:05:00.240><c> training</c><00:05:00.759><c> run</c><00:05:01.759><c> or</c><00:05:02.120><c> evaluation</c><00:05:02.680><c> run</c><00:05:03.520><c> that's</c>

00:05:03.670 --> 00:05:03.680 align:start position:0%
a training run or evaluation run that's
 

00:05:03.680 --> 00:05:06.350 align:start position:0%
a training run or evaluation run that's
what<00:05:03.759><c> you</c><00:05:03.880><c> have</c><00:05:04.000><c> in</c><00:05:04.160><c> mind</c><00:05:04.880><c> then</c><00:05:05.720><c> you</c><00:05:05.880><c> should</c><00:05:06.160><c> go</c>

00:05:06.350 --> 00:05:06.360 align:start position:0%
what you have in mind then you should go
 

00:05:06.360 --> 00:05:10.350 align:start position:0%
what you have in mind then you should go
with<00:05:06.680><c> launch</c><00:05:07.680><c> if</c><00:05:08.000><c> you</c><00:05:09.000><c> are</c><00:05:09.240><c> trying</c><00:05:09.520><c> to</c><00:05:10.000><c> create</c>

00:05:10.350 --> 00:05:10.360 align:start position:0%
with launch if you are trying to create
 

00:05:10.360 --> 00:05:12.550 align:start position:0%
with launch if you are trying to create
some<00:05:10.639><c> tools</c><00:05:11.039><c> on</c><00:05:11.240><c> tops</c><00:05:11.440><c> of</c><00:05:11.600><c> weights</c><00:05:11.840><c> and</c><00:05:12.000><c> biases</c>

00:05:12.550 --> 00:05:12.560 align:start position:0%
some tools on tops of weights and biases
 

00:05:12.560 --> 00:05:14.230 align:start position:0%
some tools on tops of weights and biases
that<00:05:12.680><c> need</c><00:05:12.960><c> lots</c><00:05:13.240><c> of</c><00:05:13.520><c> you</c><00:05:13.639><c> know</c><00:05:13.759><c> you</c><00:05:13.840><c> need</c><00:05:14.039><c> lots</c>

00:05:14.230 --> 00:05:14.240 align:start position:0%
that need lots of you know you need lots
 

00:05:14.240 --> 00:05:17.110 align:start position:0%
that need lots of you know you need lots
of<00:05:14.360><c> flexibility</c><00:05:15.240><c> and</c><00:05:16.039><c> um</c><00:05:16.199><c> you</c><00:05:16.320><c> know</c><00:05:16.520><c> you</c><00:05:16.680><c> might</c>

00:05:17.110 --> 00:05:17.120 align:start position:0%
of flexibility and um you know you might
 

00:05:17.120 --> 00:05:19.150 align:start position:0%
of flexibility and um you know you might
want<00:05:17.320><c> to</c><00:05:17.440><c> do</c><00:05:17.680><c> other</c><00:05:17.919><c> things</c><00:05:18.520><c> or</c><00:05:18.759><c> you</c><00:05:18.880><c> trying</c><00:05:19.039><c> to</c>

00:05:19.150 --> 00:05:19.160 align:start position:0%
want to do other things or you trying to
 

00:05:19.160 --> 00:05:20.629 align:start position:0%
want to do other things or you trying to
integrate<00:05:19.520><c> with</c><00:05:19.600><c> a</c><00:05:19.720><c> third</c><00:05:19.960><c> party</c><00:05:20.199><c> service</c>

00:05:20.629 --> 00:05:20.639 align:start position:0%
integrate with a third party service
 

00:05:20.639 --> 00:05:23.070 align:start position:0%
integrate with a third party service
that<00:05:20.800><c> offers</c><00:05:21.199><c> web</c><00:05:21.479><c> hooks</c><00:05:21.840><c> then</c><00:05:22.000><c> use</c><00:05:22.240><c> web</c>

00:05:23.070 --> 00:05:23.080 align:start position:0%
that offers web hooks then use web
 

00:05:23.080 --> 00:05:25.230 align:start position:0%
that offers web hooks then use web
hooks<00:05:24.080><c> so</c><00:05:24.199><c> I</c><00:05:24.280><c> just</c><00:05:24.360><c> want</c><00:05:24.479><c> to</c><00:05:24.600><c> summarize</c><00:05:25.080><c> these</c>

00:05:25.230 --> 00:05:25.240 align:start position:0%
hooks so I just want to summarize these
 

00:05:25.240 --> 00:05:28.390 align:start position:0%
hooks so I just want to summarize these
points<00:05:25.520><c> in</c><00:05:25.680><c> this</c><00:05:25.840><c> slide</c><00:05:26.639><c> again</c><00:05:27.560><c> web</c><00:05:27.880><c> hooks</c><00:05:28.160><c> are</c>

00:05:28.390 --> 00:05:28.400 align:start position:0%
points in this slide again web hooks are
 

00:05:28.400 --> 00:05:30.230 align:start position:0%
points in this slide again web hooks are
about<00:05:28.960><c> integrating</c><00:05:29.440><c> with</c><00:05:29.720><c> third</c><00:05:29.919><c> party</c>

00:05:30.230 --> 00:05:30.240 align:start position:0%
about integrating with third party
 

00:05:30.240 --> 00:05:31.309 align:start position:0%
about integrating with third party
applications<00:05:30.840><c> that's</c><00:05:31.039><c> what</c><00:05:31.120><c> they're</c>

00:05:31.309 --> 00:05:31.319 align:start position:0%
applications that's what they're
 

00:05:31.319 --> 00:05:34.189 align:start position:0%
applications that's what they're
intended<00:05:31.759><c> for</c><00:05:32.360><c> you</c><00:05:32.479><c> have</c><00:05:32.600><c> to</c><00:05:32.720><c> manage</c><00:05:33.000><c> your</c><00:05:33.160><c> own</c>

00:05:34.189 --> 00:05:34.199 align:start position:0%
intended for you have to manage your own
 

00:05:34.199 --> 00:05:36.629 align:start position:0%
intended for you have to manage your own
queue<00:05:35.199><c> um</c><00:05:35.319><c> you</c><00:05:35.440><c> can</c><00:05:35.560><c> also</c><00:05:35.759><c> build</c><00:05:36.039><c> custom</c><00:05:36.360><c> tools</c>

00:05:36.629 --> 00:05:36.639 align:start position:0%
queue um you can also build custom tools
 

00:05:36.639 --> 00:05:38.150 align:start position:0%
queue um you can also build custom tools
and<00:05:36.840><c> applications</c><00:05:37.360><c> on</c><00:05:37.560><c> top</c><00:05:37.720><c> of</c><00:05:37.840><c> weights</c><00:05:38.039><c> and</c>

00:05:38.150 --> 00:05:38.160 align:start position:0%
and applications on top of weights and
 

00:05:38.160 --> 00:05:40.749 align:start position:0%
and applications on top of weights and
biases<00:05:38.600><c> with</c><00:05:38.759><c> web</c><00:05:39.039><c> hooks</c><00:05:40.000><c> um</c><00:05:40.120><c> it's</c><00:05:40.280><c> the</c><00:05:40.440><c> most</c>

00:05:40.749 --> 00:05:40.759 align:start position:0%
biases with web hooks um it's the most
 

00:05:40.759 --> 00:05:44.990 align:start position:0%
biases with web hooks um it's the most
flexible<00:05:41.319><c> general</c><00:05:41.840><c> purpose</c><00:05:42.840><c> sort</c><00:05:43.039><c> of</c><00:05:44.000><c> uh</c><00:05:44.880><c> kind</c>

00:05:44.990 --> 00:05:45.000 align:start position:0%
flexible general purpose sort of uh kind
 

00:05:45.000 --> 00:05:49.309 align:start position:0%
flexible general purpose sort of uh kind
of<00:05:45.400><c> communication</c><00:05:46.160><c> pattern</c><00:05:47.080><c> uh</c><00:05:47.639><c> or</c><00:05:48.080><c> it</c><00:05:48.240><c> is</c><00:05:48.520><c> a</c>

00:05:49.309 --> 00:05:49.319 align:start position:0%
of communication pattern uh or it is a
 

00:05:49.319 --> 00:05:51.550 align:start position:0%
of communication pattern uh or it is a
very<00:05:49.759><c> general</c><00:05:50.160><c> communication</c><00:05:50.759><c> pattern</c><00:05:51.440><c> when</c>

00:05:51.550 --> 00:05:51.560 align:start position:0%
very general communication pattern when
 

00:05:51.560 --> 00:05:53.350 align:start position:0%
very general communication pattern when
you<00:05:51.800><c> have</c><00:05:52.319><c> different</c><00:05:52.800><c> applications</c>

00:05:53.350 --> 00:05:53.360 align:start position:0%
you have different applications
 

00:05:53.360 --> 00:05:55.230 align:start position:0%
you have different applications
communicating<00:05:53.919><c> with</c><00:05:54.039><c> each</c><00:05:54.160><c> other</c><00:05:55.120><c> and</c>

00:05:55.230 --> 00:05:55.240 align:start position:0%
communicating with each other and
 

00:05:55.240 --> 00:05:57.189 align:start position:0%
communicating with each other and
there's<00:05:55.880><c> very</c><00:05:56.080><c> little</c><00:05:56.319><c> or</c><00:05:56.479><c> no</c><00:05:56.720><c> assumptions</c>

00:05:57.189 --> 00:05:57.199 align:start position:0%
there's very little or no assumptions
 

00:05:57.199 --> 00:06:00.629 align:start position:0%
there's very little or no assumptions
made<00:05:57.440><c> about</c><00:05:57.800><c> your</c><00:05:58.360><c> infrastructure</c><00:05:59.360><c> you</c><00:05:59.800><c> build</c>

00:06:00.629 --> 00:06:00.639 align:start position:0%
made about your infrastructure you build
 

00:06:00.639 --> 00:06:03.550 align:start position:0%
made about your infrastructure you build
things<00:06:01.560><c> the</c><00:06:01.680><c> way</c><00:06:01.800><c> you</c><00:06:01.919><c> need</c><00:06:02.120><c> to</c><00:06:02.240><c> build</c><00:06:02.479><c> it</c><00:06:03.080><c> and</c>

00:06:03.550 --> 00:06:03.560 align:start position:0%
things the way you need to build it and
 

00:06:03.560 --> 00:06:06.670 align:start position:0%
things the way you need to build it and
you<00:06:04.039><c> have</c><00:06:04.160><c> to</c><00:06:04.280><c> build</c><00:06:04.479><c> it</c><00:06:04.639><c> yourself</c><00:06:05.680><c> launch</c>

00:06:06.670 --> 00:06:06.680 align:start position:0%
you have to build it yourself launch
 

00:06:06.680 --> 00:06:08.830 align:start position:0%
you have to build it yourself launch
does<00:06:06.880><c> a</c><00:06:07.039><c> little</c><00:06:07.199><c> bit</c><00:06:07.360><c> more</c><00:06:07.599><c> for</c><00:06:07.880><c> you</c><00:06:08.319><c> so</c>

00:06:08.830 --> 00:06:08.840 align:start position:0%
does a little bit more for you so
 

00:06:08.840 --> 00:06:10.309 align:start position:0%
does a little bit more for you so
weights<00:06:09.120><c> and</c><00:06:09.440><c> with</c><00:06:09.560><c> launch</c><00:06:09.919><c> weights</c><00:06:10.160><c> and</c>

00:06:10.309 --> 00:06:10.319 align:start position:0%
weights and with launch weights and
 

00:06:10.319 --> 00:06:12.629 align:start position:0%
weights and with launch weights and
biases<00:06:10.720><c> manages</c><00:06:11.199><c> the</c><00:06:11.319><c> queue</c><00:06:11.560><c> for</c><00:06:11.759><c> you</c><00:06:12.560><c> there</c>

00:06:12.629 --> 00:06:12.639 align:start position:0%
biases manages the queue for you there
 

00:06:12.639 --> 00:06:14.990 align:start position:0%
biases manages the queue for you there
is<00:06:12.800><c> a</c><00:06:13.000><c> paved</c><00:06:13.479><c> path</c><00:06:13.880><c> for</c><00:06:14.080><c> running</c><00:06:14.479><c> Python</c><00:06:14.800><c> and</c>

00:06:14.990 --> 00:06:15.000 align:start position:0%
is a paved path for running Python and
 

00:06:15.000 --> 00:06:18.189 align:start position:0%
is a paved path for running Python and
Docker<00:06:15.520><c> code</c><00:06:16.520><c> uh</c><00:06:16.680><c> on</c><00:06:16.840><c> in</c><00:06:17.039><c> response</c><00:06:17.360><c> to</c><00:06:17.720><c> in</c>

00:06:18.189 --> 00:06:18.199 align:start position:0%
Docker code uh on in response to in
 

00:06:18.199 --> 00:06:21.469 align:start position:0%
Docker code uh on in response to in
events<00:06:19.199><c> uh</c><00:06:19.319><c> on</c><00:06:19.479><c> the</c><00:06:19.639><c> compute</c><00:06:20.039><c> of</c><00:06:20.199><c> your</c><00:06:20.479><c> choice</c>

00:06:21.469 --> 00:06:21.479 align:start position:0%
events uh on the compute of your choice
 

00:06:21.479 --> 00:06:23.909 align:start position:0%
events uh on the compute of your choice
my<00:06:21.919><c> and</c><00:06:22.120><c> again</c><00:06:22.520><c> this</c><00:06:22.680><c> compute</c><00:06:23.000><c> of</c><00:06:23.120><c> your</c><00:06:23.240><c> choice</c>

00:06:23.909 --> 00:06:23.919 align:start position:0%
my and again this compute of your choice
 

00:06:23.919 --> 00:06:27.510 align:start position:0%
my and again this compute of your choice
is<00:06:24.319><c> very</c><00:06:25.319><c> uh</c><00:06:25.639><c> General</c><00:06:26.560><c> can</c><00:06:26.720><c> run</c><00:06:26.880><c> it</c><00:06:27.080><c> anywhere</c>

00:06:27.510 --> 00:06:27.520 align:start position:0%
is very uh General can run it anywhere
 

00:06:27.520 --> 00:06:29.790 align:start position:0%
is very uh General can run it anywhere
including<00:06:28.199><c> kubernetes</c><00:06:29.199><c> and</c><00:06:29.360><c> things</c><00:06:29.599><c> things</c>

00:06:29.790 --> 00:06:29.800 align:start position:0%
including kubernetes and things things
 

00:06:29.800 --> 00:06:33.670 align:start position:0%
including kubernetes and things things
like<00:06:29.960><c> Sage</c><00:06:30.360><c> maker</c><00:06:31.080><c> for</c><00:06:31.280><c> AWS</c><00:06:31.960><c> and</c><00:06:32.160><c> vertex</c><00:06:32.720><c> for</c>

00:06:33.670 --> 00:06:33.680 align:start position:0%
like Sage maker for AWS and vertex for
 

00:06:33.680 --> 00:06:35.950 align:start position:0%
like Sage maker for AWS and vertex for
gcp<00:06:34.680><c> and</c><00:06:34.800><c> it's</c><00:06:35.000><c> really</c><00:06:35.199><c> meant</c><00:06:35.400><c> for</c><00:06:35.560><c> training</c>

00:06:35.950 --> 00:06:35.960 align:start position:0%
gcp and it's really meant for training
 

00:06:35.960 --> 00:06:38.990 align:start position:0%
gcp and it's really meant for training
or<00:06:36.199><c> evaluation</c><00:06:37.199><c> runs</c><00:06:38.199><c> so</c><00:06:38.360><c> I</c><00:06:38.520><c> hope</c><00:06:38.720><c> this</c>

00:06:38.990 --> 00:06:39.000 align:start position:0%
or evaluation runs so I hope this
 

00:06:39.000 --> 00:06:42.029 align:start position:0%
or evaluation runs so I hope this
clarifies<00:06:40.000><c> your</c><00:06:40.199><c> mental</c><00:06:40.599><c> model</c><00:06:41.120><c> of</c><00:06:41.680><c> when</c><00:06:41.880><c> to</c>

00:06:42.029 --> 00:06:42.039 align:start position:0%
clarifies your mental model of when to
 

00:06:42.039 --> 00:06:46.639 align:start position:0%
clarifies your mental model of when to
use<00:06:42.319><c> web</c><00:06:42.560><c> Hooks</c><00:06:42.919><c> and</c><00:06:43.080><c> when</c><00:06:43.240><c> to</c><00:06:43.400><c> use</c><00:06:43.639><c> launch</c>

