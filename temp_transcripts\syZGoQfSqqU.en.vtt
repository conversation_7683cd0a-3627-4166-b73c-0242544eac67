WEBVTT
Kind: captions
Language: en

00:00:00.199 --> 00:00:02.350 align:start position:0%
 
in<00:00:00.320><c> the</c><00:00:00.520><c> last</c><00:00:00.760><c> section</c><00:00:01.360><c> you</c><00:00:01.560><c> created</c><00:00:01.959><c> a</c><00:00:02.120><c> web</c>

00:00:02.350 --> 00:00:02.360 align:start position:0%
in the last section you created a web
 

00:00:02.360 --> 00:00:05.030 align:start position:0%
in the last section you created a web
hook<00:00:02.959><c> locally</c><00:00:03.959><c> by</c><00:00:04.120><c> now</c><00:00:04.359><c> you</c><00:00:04.480><c> should</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
hook locally by now you should
 

00:00:05.040 --> 00:00:07.590 align:start position:0%
hook locally by now you should
understand<00:00:05.240><c> the</c><00:00:05.400><c> basic</c><00:00:06.000><c> basics</c><00:00:06.319><c> of</c><00:00:06.480><c> web</c><00:00:06.759><c> hooks</c>

00:00:07.590 --> 00:00:07.600 align:start position:0%
understand the basic basics of web hooks
 

00:00:07.600 --> 00:00:10.549 align:start position:0%
understand the basic basics of web hooks
is<00:00:08.080><c> that</c><00:00:08.240><c> is</c><00:00:08.480><c> just</c><00:00:09.280><c> a</c><00:00:09.440><c> web</c><00:00:09.679><c> server</c><00:00:10.120><c> that</c><00:00:10.200><c> is</c>

00:00:10.549 --> 00:00:10.559 align:start position:0%
is that is just a web server that is
 

00:00:10.559 --> 00:00:13.509 align:start position:0%
is that is just a web server that is
receiving<00:00:10.759><c> a</c><00:00:10.920><c> request</c><00:00:11.719><c> that</c><00:00:11.920><c> then</c><00:00:12.120><c> you</c><00:00:12.280><c> can</c><00:00:13.280><c> in</c>

00:00:13.509 --> 00:00:13.519 align:start position:0%
receiving a request that then you can in
 

00:00:13.519 --> 00:00:15.629 align:start position:0%
receiving a request that then you can in
response<00:00:13.880><c> to</c><00:00:14.240><c> execute</c><00:00:14.719><c> functions</c><00:00:15.320><c> or</c><00:00:15.440><c> do</c>

00:00:15.629 --> 00:00:15.639 align:start position:0%
response to execute functions or do
 

00:00:15.639 --> 00:00:18.790 align:start position:0%
response to execute functions or do
anything<00:00:16.000><c> else</c><00:00:17.000><c> in</c><00:00:17.240><c> in</c><00:00:17.520><c> response</c>

00:00:18.790 --> 00:00:18.800 align:start position:0%
anything else in in response
 

00:00:18.800 --> 00:00:22.870 align:start position:0%
anything else in in response
to<00:00:19.800><c> the</c><00:00:19.960><c> next</c><00:00:20.320><c> step</c><00:00:21.320><c> towards</c><00:00:21.720><c> making</c><00:00:22.119><c> this</c><00:00:22.720><c> a</c>

00:00:22.870 --> 00:00:22.880 align:start position:0%
to the next step towards making this a
 

00:00:22.880 --> 00:00:24.670 align:start position:0%
to the next step towards making this a
little<00:00:23.080><c> bit</c><00:00:23.320><c> more</c><00:00:23.599><c> realistic</c><00:00:24.279><c> in</c><00:00:24.439><c> more</c>

00:00:24.670 --> 00:00:24.680 align:start position:0%
little bit more realistic in more
 

00:00:24.680 --> 00:00:26.950 align:start position:0%
little bit more realistic in more
production<00:00:25.000><c> grade</c><00:00:25.359><c> is</c><00:00:25.480><c> to</c><00:00:25.720><c> actually</c><00:00:26.320><c> host</c><00:00:26.720><c> the</c>

00:00:26.950 --> 00:00:26.960 align:start position:0%
production grade is to actually host the
 

00:00:26.960 --> 00:00:31.349 align:start position:0%
production grade is to actually host the
exact<00:00:27.480><c> same</c><00:00:28.320><c> web</c><00:00:28.599><c> hook</c><00:00:28.960><c> server</c><00:00:30.039><c> on</c><00:00:30.240><c> the</c><00:00:30.400><c> cloud</c>

00:00:31.349 --> 00:00:31.359 align:start position:0%
exact same web hook server on the cloud
 

00:00:31.359 --> 00:00:33.150 align:start position:0%
exact same web hook server on the cloud
and<00:00:31.480><c> the</c><00:00:31.640><c> easiest</c><00:00:32.000><c> way</c><00:00:32.119><c> to</c><00:00:32.279><c> do</c>

00:00:33.150 --> 00:00:33.160 align:start position:0%
and the easiest way to do
 

00:00:33.160 --> 00:00:35.510 align:start position:0%
and the easiest way to do
this<00:00:34.160><c> in</c><00:00:34.320><c> my</c><00:00:34.520><c> opinion</c><00:00:34.879><c> would</c><00:00:35.040><c> be</c><00:00:35.160><c> to</c><00:00:35.280><c> use</c>

00:00:35.510 --> 00:00:35.520 align:start position:0%
this in my opinion would be to use
 

00:00:35.520 --> 00:00:38.430 align:start position:0%
this in my opinion would be to use
something<00:00:35.800><c> like</c><00:00:35.920><c> a</c><00:00:36.040><c> modal</c><00:00:36.600><c> now</c><00:00:36.800><c> modal</c><00:00:37.719><c> is</c><00:00:37.960><c> a</c>

00:00:38.430 --> 00:00:38.440 align:start position:0%
something like a modal now modal is a
 

00:00:38.440 --> 00:00:40.069 align:start position:0%
something like a modal now modal is a
kind<00:00:38.559><c> of</c><00:00:38.760><c> serverless</c>

00:00:40.069 --> 00:00:40.079 align:start position:0%
kind of serverless
 

00:00:40.079 --> 00:00:42.190 align:start position:0%
kind of serverless
python<00:00:41.079><c> environment</c><00:00:41.680><c> that</c><00:00:41.760><c> is</c><00:00:41.920><c> geared</c>

00:00:42.190 --> 00:00:42.200 align:start position:0%
python environment that is geared
 

00:00:42.200 --> 00:00:43.709 align:start position:0%
python environment that is geared
towards<00:00:42.520><c> machine</c><00:00:42.800><c> learning</c>

00:00:43.709 --> 00:00:43.719 align:start position:0%
towards machine learning
 

00:00:43.719 --> 00:00:46.990 align:start position:0%
towards machine learning
engineers<00:00:44.719><c> and</c><00:00:45.160><c> basically</c><00:00:46.079><c> it's</c><00:00:46.280><c> like</c><00:00:46.440><c> Cloud</c>

00:00:46.990 --> 00:00:47.000 align:start position:0%
engineers and basically it's like Cloud
 

00:00:47.000 --> 00:00:49.270 align:start position:0%
engineers and basically it's like Cloud
functions<00:00:48.000><c> which</c><00:00:48.120><c> is</c><00:00:48.320><c> a</c><00:00:48.520><c> a</c><00:00:48.640><c> Google</c><00:00:48.920><c> Cloud</c>

00:00:49.270 --> 00:00:49.280 align:start position:0%
functions which is a a Google Cloud
 

00:00:49.280 --> 00:00:53.029 align:start position:0%
functions which is a a Google Cloud
product<00:00:50.120><c> but</c><00:00:50.360><c> really</c><00:00:50.879><c> it's</c><00:00:51.239><c> designed</c><00:00:52.039><c> for</c>

00:00:53.029 --> 00:00:53.039 align:start position:0%
product but really it's designed for
 

00:00:53.039 --> 00:00:55.310 align:start position:0%
product but really it's designed for
it's<00:00:53.239><c> it's</c><00:00:53.399><c> really</c><00:00:53.600><c> designed</c><00:00:54.039><c> for</c><00:00:54.399><c> python</c>

00:00:55.310 --> 00:00:55.320 align:start position:0%
it's it's really designed for python
 

00:00:55.320 --> 00:00:57.830 align:start position:0%
it's it's really designed for python
programmers<00:00:56.320><c> who</c><00:00:57.120><c> especially</c><00:00:57.559><c> machine</c>

00:00:57.830 --> 00:00:57.840 align:start position:0%
programmers who especially machine
 

00:00:57.840 --> 00:01:00.670 align:start position:0%
programmers who especially machine
learning<00:00:58.480><c> Engineers</c><00:00:59.440><c> who</c><00:00:59.559><c> want</c><00:00:59.719><c> to</c><00:01:00.039><c> deploy</c>

00:01:00.670 --> 00:01:00.680 align:start position:0%
learning Engineers who want to deploy
 

00:01:00.680 --> 00:01:03.830 align:start position:0%
learning Engineers who want to deploy
code<00:01:01.000><c> in</c><00:01:01.079><c> a</c><00:01:01.239><c> serverless</c><00:01:02.280><c> way</c><00:01:03.280><c> and</c><00:01:03.399><c> I</c><00:01:03.480><c> won't</c><00:01:03.719><c> go</c>

00:01:03.830 --> 00:01:03.840 align:start position:0%
code in a serverless way and I won't go
 

00:01:03.840 --> 00:01:06.550 align:start position:0%
code in a serverless way and I won't go
over<00:01:04.080><c> through</c><00:01:04.559><c> uh</c><00:01:05.040><c> go</c><00:01:05.439><c> over</c><00:01:05.720><c> modal</c><00:01:06.159><c> in</c><00:01:06.280><c> a</c><00:01:06.400><c> lot</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
over through uh go over modal in a lot
 

00:01:06.560 --> 00:01:09.310 align:start position:0%
over through uh go over modal in a lot
of<00:01:06.880><c> depth</c><00:01:07.880><c> um</c><00:01:08.320><c> all</c><00:01:08.479><c> it</c><00:01:08.560><c> will</c><00:01:08.720><c> say</c><00:01:08.880><c> is</c><00:01:09.119><c> very</c>

00:01:09.310 --> 00:01:09.320 align:start position:0%
of depth um all it will say is very
 

00:01:09.320 --> 00:01:10.710 align:start position:0%
of depth um all it will say is very
lightweight<00:01:09.799><c> it's</c><00:01:09.960><c> really</c><00:01:10.159><c> easy</c><00:01:10.360><c> to</c><00:01:10.520><c> get</c>

00:01:10.710 --> 00:01:10.720 align:start position:0%
lightweight it's really easy to get
 

00:01:10.720 --> 00:01:14.390 align:start position:0%
lightweight it's really easy to get
started<00:01:11.720><c> and</c><00:01:12.479><c> everybody</c><00:01:13.000><c> has</c><00:01:13.240><c> access</c><00:01:13.560><c> to</c><00:01:13.759><c> it</c>

00:01:14.390 --> 00:01:14.400 align:start position:0%
started and everybody has access to it
 

00:01:14.400 --> 00:01:16.670 align:start position:0%
started and everybody has access to it
you<00:01:14.520><c> can</c><00:01:14.799><c> sign</c><00:01:15.080><c> up</c><00:01:15.280><c> in</c><00:01:15.439><c> your</c><00:01:15.560><c> first</c><00:01:15.880><c> $30</c><00:01:16.479><c> of</c>

00:01:16.670 --> 00:01:16.680 align:start position:0%
you can sign up in your first $30 of
 

00:01:16.680 --> 00:01:18.510 align:start position:0%
you can sign up in your first $30 of
credits<00:01:16.960><c> are</c>

00:01:18.510 --> 00:01:18.520 align:start position:0%
credits are
 

00:01:18.520 --> 00:01:20.990 align:start position:0%
credits are
free<00:01:19.520><c> um</c><00:01:19.799><c> and</c><00:01:19.920><c> so</c><00:01:20.119><c> it</c><00:01:20.200><c> should</c><00:01:20.400><c> be</c><00:01:20.640><c> it</c><00:01:20.720><c> should</c><00:01:20.880><c> be</c>

00:01:20.990 --> 00:01:21.000 align:start position:0%
free um and so it should be it should be
 

00:01:21.000 --> 00:01:23.390 align:start position:0%
free um and so it should be it should be
fairly<00:01:21.280><c> straightforward</c><00:01:21.759><c> to</c><00:01:21.920><c> get</c><00:01:22.040><c> started</c><00:01:22.520><c> so</c>

00:01:23.390 --> 00:01:23.400 align:start position:0%
fairly straightforward to get started so
 

00:01:23.400 --> 00:01:24.429 align:start position:0%
fairly straightforward to get started so
we're<00:01:23.560><c> going</c><00:01:23.680><c> to</c><00:01:23.880><c> the</c><00:01:24.040><c> first</c><00:01:24.200><c> thing</c><00:01:24.320><c> we're</c>

00:01:24.429 --> 00:01:24.439 align:start position:0%
we're going to the first thing we're
 

00:01:24.439 --> 00:01:25.950 align:start position:0%
we're going to the first thing we're
going<00:01:24.560><c> to</c><00:01:24.680><c> do</c><00:01:24.880><c> is</c><00:01:25.000><c> we're</c><00:01:25.119><c> going</c><00:01:25.240><c> to</c><00:01:25.360><c> take</c><00:01:25.520><c> the</c>

00:01:25.950 --> 00:01:25.960 align:start position:0%
going to do is we're going to take the
 

00:01:25.960 --> 00:01:29.030 align:start position:0%
going to do is we're going to take the
code<00:01:26.960><c> uh</c><00:01:27.159><c> well</c><00:01:27.360><c> actually</c><00:01:27.640><c> let's</c><00:01:27.920><c> look</c><00:01:28.119><c> at</c><00:01:28.799><c> the</c>

00:01:29.030 --> 00:01:29.040 align:start position:0%
code uh well actually let's look at the
 

00:01:29.040 --> 00:01:31.149 align:start position:0%
code uh well actually let's look at the
GitHub<00:01:29.360><c> repo</c><00:01:30.280><c> so</c><00:01:30.439><c> if</c><00:01:30.520><c> you</c><00:01:30.640><c> haven't</c><00:01:30.840><c> cloned</c>

00:01:31.149 --> 00:01:31.159 align:start position:0%
GitHub repo so if you haven't cloned
 

00:01:31.159 --> 00:01:33.510 align:start position:0%
GitHub repo so if you haven't cloned
this<00:01:31.320><c> repo</c><00:01:32.280><c> uh</c><00:01:32.399><c> please</c><00:01:32.680><c> go</c><00:01:32.799><c> ahead</c><00:01:33.000><c> and</c><00:01:33.119><c> do</c><00:01:33.280><c> it</c>

00:01:33.510 --> 00:01:33.520 align:start position:0%
this repo uh please go ahead and do it
 

00:01:33.520 --> 00:01:36.469 align:start position:0%
this repo uh please go ahead and do it
it's<00:01:33.680><c> under</c><00:01:34.320><c> my</c><00:01:34.600><c> GitHub</c><00:01:34.920><c> username</c><00:01:35.439><c> haml</c>

00:01:36.469 --> 00:01:36.479 align:start position:0%
it's under my GitHub username haml
 

00:01:36.479 --> 00:01:39.950 align:start position:0%
it's under my GitHub username haml
SMU<00:01:37.479><c> and</c><00:01:37.720><c> this</c><00:01:37.880><c> is</c><00:01:38.119><c> W</c><00:01:38.360><c> DB</c><00:01:38.640><c> modal</c><00:01:38.960><c> web</c>

00:01:39.950 --> 00:01:39.960 align:start position:0%
SMU and this is W DB modal web
 

00:01:39.960 --> 00:01:43.389 align:start position:0%
SMU and this is W DB modal web
hook<00:01:40.960><c> uh</c><00:01:41.079><c> go</c><00:01:41.200><c> ahead</c><00:01:41.360><c> and</c><00:01:41.479><c> clone</c><00:01:41.759><c> the</c><00:01:42.320><c> repo</c><00:01:43.320><c> and</c>

00:01:43.389 --> 00:01:43.399 align:start position:0%
hook uh go ahead and clone the repo and
 

00:01:43.399 --> 00:01:45.749 align:start position:0%
hook uh go ahead and clone the repo and
in<00:01:43.560><c> this</c><00:01:43.680><c> repo</c><00:01:44.040><c> you</c><00:01:44.200><c> have</c><00:01:44.399><c> all</c><00:01:44.560><c> the</c><00:01:44.719><c> code</c><00:01:45.560><c> that</c>

00:01:45.749 --> 00:01:45.759 align:start position:0%
in this repo you have all the code that
 

00:01:45.759 --> 00:01:50.789 align:start position:0%
in this repo you have all the code that
I<00:01:46.000><c> will</c><00:01:46.360><c> use</c><00:01:47.360><c> in</c><00:01:47.560><c> this</c><00:01:48.000><c> in</c><00:01:48.159><c> this</c><00:01:48.439><c> uh</c><00:01:48.560><c> particular</c>

00:01:50.789 --> 00:01:50.799 align:start position:0%
I will use in this in this uh particular
 

00:01:50.799 --> 00:01:52.709 align:start position:0%
I will use in this in this uh particular
lesson<00:01:51.799><c> now</c><00:01:51.960><c> I'm</c><00:01:52.040><c> going</c><00:01:52.159><c> to</c><00:01:52.280><c> go</c><00:01:52.399><c> ahead</c><00:01:52.560><c> and</c>

00:01:52.709 --> 00:01:52.719 align:start position:0%
lesson now I'm going to go ahead and
 

00:01:52.719 --> 00:01:55.990 align:start position:0%
lesson now I'm going to go ahead and
open<00:01:52.920><c> the</c><00:01:53.040><c> readme</c><00:01:53.560><c> in</c><00:01:53.759><c> this</c><00:01:54.040><c> vs</c><00:01:54.560><c> code</c><00:01:55.560><c> editor</c>

00:01:55.990 --> 00:01:56.000 align:start position:0%
open the readme in this vs code editor
 

00:01:56.000 --> 00:01:58.149 align:start position:0%
open the readme in this vs code editor
so<00:01:56.200><c> you</c><00:01:56.320><c> can</c><00:01:56.719><c> we</c><00:01:56.840><c> can</c><00:01:57.039><c> both</c><00:01:57.280><c> see</c><00:01:57.479><c> it</c><00:01:57.640><c> in</c><00:01:57.840><c> bigger</c>

00:01:58.149 --> 00:01:58.159 align:start position:0%
so you can we can both see it in bigger
 

00:01:58.159 --> 00:01:59.950 align:start position:0%
so you can we can both see it in bigger
font

00:01:59.950 --> 00:01:59.960 align:start position:0%
font
 

00:01:59.960 --> 00:02:04.350 align:start position:0%
font
so<00:02:01.240><c> um</c><00:02:02.240><c> so</c><00:02:02.520><c> weights</c><00:02:03.200><c> uh</c><00:02:03.399><c> weights</c><00:02:03.600><c> and</c><00:02:03.960><c> so</c><00:02:04.159><c> this</c>

00:02:04.350 --> 00:02:04.360 align:start position:0%
so um so weights uh weights and so this
 

00:02:04.360 --> 00:02:06.830 align:start position:0%
so um so weights uh weights and so this
repo<00:02:04.840><c> is</c><00:02:04.960><c> about</c><00:02:05.360><c> the</c><00:02:05.479><c> integration</c><00:02:06.079><c> between</c><00:02:06.600><c> an</c>

00:02:06.830 --> 00:02:06.840 align:start position:0%
repo is about the integration between an
 

00:02:06.840 --> 00:02:09.389 align:start position:0%
repo is about the integration between an
example<00:02:07.560><c> integration</c><00:02:08.560><c> that</c><00:02:08.679><c> you</c><00:02:08.840><c> can</c><00:02:09.039><c> achieve</c>

00:02:09.389 --> 00:02:09.399 align:start position:0%
example integration that you can achieve
 

00:02:09.399 --> 00:02:11.750 align:start position:0%
example integration that you can achieve
with<00:02:09.560><c> web</c><00:02:09.840><c> hooks</c><00:02:10.200><c> with</c><00:02:10.360><c> weights</c><00:02:10.560><c> and</c><00:02:10.760><c> biases</c>

00:02:11.750 --> 00:02:11.760 align:start position:0%
with web hooks with weights and biases
 

00:02:11.760 --> 00:02:14.110 align:start position:0%
with web hooks with weights and biases
and<00:02:12.120><c> specifically</c><00:02:13.120><c> weights</c><00:02:13.360><c> and</c><00:02:13.520><c> biases</c><00:02:13.959><c> with</c>

00:02:14.110 --> 00:02:14.120 align:start position:0%
and specifically weights and biases with
 

00:02:14.120 --> 00:02:17.229 align:start position:0%
and specifically weights and biases with
modal<00:02:15.040><c> but</c><00:02:15.400><c> because</c><00:02:15.640><c> it's</c><00:02:15.920><c> web</c><00:02:16.160><c> hooks</c><00:02:17.000><c> it's</c>

00:02:17.229 --> 00:02:17.239 align:start position:0%
modal but because it's web hooks it's
 

00:02:17.239 --> 00:02:19.670 align:start position:0%
modal but because it's web hooks it's
really<00:02:17.720><c> a</c><00:02:17.840><c> minimal</c><00:02:18.360><c> example</c><00:02:19.000><c> of</c><00:02:19.160><c> gluing</c>

00:02:19.670 --> 00:02:19.680 align:start position:0%
really a minimal example of gluing
 

00:02:19.680 --> 00:02:21.990 align:start position:0%
really a minimal example of gluing
weights<00:02:19.959><c> and</c><00:02:20.160><c> bias</c><00:02:21.000><c> weights</c><00:02:21.239><c> and</c><00:02:21.360><c> biases</c><00:02:21.760><c> to</c>

00:02:21.990 --> 00:02:22.000 align:start position:0%
weights and bias weights and biases to
 

00:02:22.000 --> 00:02:24.070 align:start position:0%
weights and bias weights and biases to
anything<00:02:22.280><c> else</c><00:02:22.959><c> because</c><00:02:23.239><c> web</c><00:02:23.440><c> hooks</c><00:02:23.640><c> are</c><00:02:23.800><c> very</c>

00:02:24.070 --> 00:02:24.080 align:start position:0%
anything else because web hooks are very
 

00:02:24.080 --> 00:02:26.910 align:start position:0%
anything else because web hooks are very
general<00:02:24.599><c> as</c><00:02:24.720><c> we</c><00:02:24.879><c> discussed</c><00:02:25.319><c> before</c><00:02:26.120><c> web</c><00:02:26.400><c> hooks</c>

00:02:26.910 --> 00:02:26.920 align:start position:0%
general as we discussed before web hooks
 

00:02:26.920 --> 00:02:28.390 align:start position:0%
general as we discussed before web hooks
are<00:02:27.080><c> used</c><00:02:27.400><c> by</c><00:02:27.640><c> lots</c><00:02:27.879><c> of</c><00:02:28.080><c> different</c>

00:02:28.390 --> 00:02:28.400 align:start position:0%
are used by lots of different
 

00:02:28.400 --> 00:02:30.229 align:start position:0%
are used by lots of different
infrastructure

00:02:30.229 --> 00:02:30.239 align:start position:0%
infrastructure
 

00:02:30.239 --> 00:02:33.390 align:start position:0%
infrastructure
as<00:02:30.360><c> a</c><00:02:30.599><c> way</c><00:02:31.000><c> to</c><00:02:31.640><c> expose</c><00:02:32.640><c> a</c><00:02:32.840><c> communication</c>

00:02:33.390 --> 00:02:33.400 align:start position:0%
as a way to expose a communication
 

00:02:33.400 --> 00:02:35.830 align:start position:0%
as a way to expose a communication
Channel<00:02:33.760><c> with</c><00:02:33.920><c> the</c><00:02:34.000><c> outside</c><00:02:34.400><c> world</c>

00:02:35.830 --> 00:02:35.840 align:start position:0%
Channel with the outside world
 

00:02:35.840 --> 00:02:38.550 align:start position:0%
Channel with the outside world
and<00:02:36.840><c> and</c><00:02:37.080><c> and</c><00:02:37.239><c> this</c><00:02:37.360><c> is</c><00:02:37.519><c> something</c><00:02:38.239><c> this</c><00:02:38.319><c> is</c>

00:02:38.550 --> 00:02:38.560 align:start position:0%
and and and this is something this is
 

00:02:38.560 --> 00:02:41.550 align:start position:0%
and and and this is something this is
why<00:02:38.760><c> weights</c><00:02:39.000><c> and</c><00:02:39.159><c> biases</c><00:02:40.040><c> has</c><00:02:40.519><c> a</c><00:02:40.680><c> web</c><00:02:40.959><c> hook</c>

00:02:41.550 --> 00:02:41.560 align:start position:0%
why weights and biases has a web hook
 

00:02:41.560 --> 00:02:43.670 align:start position:0%
why weights and biases has a web hook
trigger<00:02:42.560><c> but</c><00:02:42.760><c> before</c><00:02:42.959><c> we</c><00:02:43.120><c> get</c><00:02:43.239><c> into</c><00:02:43.480><c> weights</c>

00:02:43.670 --> 00:02:43.680 align:start position:0%
trigger but before we get into weights
 

00:02:43.680 --> 00:02:46.949 align:start position:0%
trigger but before we get into weights
and<00:02:43.840><c> biases</c><00:02:44.760><c> we</c><00:02:44.879><c> want</c><00:02:45.040><c> to</c><00:02:45.239><c> actually</c><00:02:45.640><c> Host</c><00:02:46.400><c> this</c>

00:02:46.949 --> 00:02:46.959 align:start position:0%
and biases we want to actually Host this
 

00:02:46.959 --> 00:02:50.670 align:start position:0%
and biases we want to actually Host this
web<00:02:47.280><c> hook</c><00:02:47.560><c> server</c><00:02:49.040><c> somewhere</c><00:02:50.040><c> that</c><00:02:50.400><c> weights</c>

00:02:50.670 --> 00:02:50.680 align:start position:0%
web hook server somewhere that weights
 

00:02:50.680 --> 00:02:53.630 align:start position:0%
web hook server somewhere that weights
and<00:02:50.840><c> biases</c><00:02:51.360><c> can</c><00:02:51.920><c> access</c><00:02:52.920><c> and</c><00:02:53.040><c> so</c><00:02:53.200><c> to</c><00:02:53.360><c> do</c><00:02:53.519><c> that</c>

00:02:53.630 --> 00:02:53.640 align:start position:0%
and biases can access and so to do that
 

00:02:53.640 --> 00:02:55.990 align:start position:0%
and biases can access and so to do that
we're<00:02:53.760><c> going</c><00:02:53.879><c> to</c><00:02:54.000><c> have</c><00:02:54.080><c> to</c><00:02:54.159><c> put</c><00:02:54.280><c> it</c><00:02:54.360><c> on</c><00:02:54.480><c> the</c>

00:02:55.990 --> 00:02:56.000 align:start position:0%
we're going to have to put it on the
 

00:02:56.000 --> 00:02:58.190 align:start position:0%
we're going to have to put it on the
cloud<00:02:57.000><c> so</c><00:02:57.400><c> the</c><00:02:57.519><c> first</c><00:02:57.720><c> thing</c><00:02:57.840><c> you're</c><00:02:57.959><c> going</c><00:02:58.040><c> to</c>

00:02:58.190 --> 00:02:58.200 align:start position:0%
cloud so the first thing you're going to
 

00:02:58.200 --> 00:03:02.149 align:start position:0%
cloud so the first thing you're going to
want<00:02:58.280><c> to</c><00:02:58.480><c> do</c><00:02:59.040><c> is</c><00:02:59.519><c> PIP</c><00:02:59.879><c> install</c><00:03:00.800><c> modal</c><00:03:01.800><c> and</c><00:03:02.080><c> I</c>

00:03:02.149 --> 00:03:02.159 align:start position:0%
want to do is PIP install modal and I
 

00:03:02.159 --> 00:03:04.789 align:start position:0%
want to do is PIP install modal and I
would<00:03:02.440><c> create</c><00:03:02.680><c> a</c><00:03:02.800><c> virtual</c><00:03:03.159><c> environment</c><00:03:03.799><c> first</c>

00:03:04.789 --> 00:03:04.799 align:start position:0%
would create a virtual environment first
 

00:03:04.799 --> 00:03:08.630 align:start position:0%
would create a virtual environment first
um<00:03:05.000><c> you</c><00:03:05.120><c> know</c><00:03:05.599><c> using</c><00:03:06.599><c> p</c><00:03:06.920><c> pipm</c><00:03:07.560><c> or</c><00:03:08.080><c> my</c><00:03:08.280><c> favorite</c>

00:03:08.630 --> 00:03:08.640 align:start position:0%
um you know using p pipm or my favorite
 

00:03:08.640 --> 00:03:10.190 align:start position:0%
um you know using p pipm or my favorite
is<00:03:08.760><c> to</c><00:03:08.879><c> use</c>

00:03:10.190 --> 00:03:10.200 align:start position:0%
is to use
 

00:03:10.200 --> 00:03:13.910 align:start position:0%
is to use
cond<00:03:11.200><c> so</c><00:03:11.760><c> pip</c><00:03:12.040><c> install</c><00:03:12.400><c> modal</c><00:03:13.280><c> and</c><00:03:13.440><c> then</c><00:03:13.640><c> also</c>

00:03:13.910 --> 00:03:13.920 align:start position:0%
cond so pip install modal and then also
 

00:03:13.920 --> 00:03:17.630 align:start position:0%
cond so pip install modal and then also
install<00:03:14.319><c> the</c><00:03:14.480><c> mo</c><00:03:14.760><c> the</c><00:03:14.879><c> modal</c>

00:03:17.630 --> 00:03:17.640 align:start position:0%
 
 

00:03:17.640 --> 00:03:23.390 align:start position:0%
 
client<00:03:18.640><c> when</c><00:03:18.799><c> you</c><00:03:18.920><c> log</c><00:03:19.159><c> into</c><00:03:19.640><c> modal</c><00:03:20.640><c> um</c><00:03:21.400><c> you</c>

00:03:23.390 --> 00:03:23.400 align:start position:0%
client when you log into modal um you
 

00:03:23.400 --> 00:03:26.309 align:start position:0%
client when you log into modal um you
will<00:03:24.400><c> uh</c><00:03:24.599><c> once</c><00:03:24.760><c> you</c><00:03:24.959><c> create</c><00:03:25.200><c> an</c><00:03:25.400><c> account</c><00:03:26.120><c> the</c>

00:03:26.309 --> 00:03:26.319 align:start position:0%
will uh once you create an account the
 

00:03:26.319 --> 00:03:27.309 align:start position:0%
will uh once you create an account the
the<00:03:26.440><c> next</c><00:03:26.599><c> thing</c><00:03:26.720><c> you're</c><00:03:26.840><c> going</c><00:03:26.920><c> to</c><00:03:27.040><c> want</c><00:03:27.159><c> to</c>

00:03:27.309 --> 00:03:27.319 align:start position:0%
the next thing you're going to want to
 

00:03:27.319 --> 00:03:31.110 align:start position:0%
the next thing you're going to want to
do<00:03:28.000><c> is</c><00:03:28.120><c> to</c><00:03:28.280><c> create</c><00:03:28.519><c> a</c><00:03:28.680><c> secret</c>

00:03:31.110 --> 00:03:31.120 align:start position:0%
do is to create a secret
 

00:03:31.120 --> 00:03:33.670 align:start position:0%
do is to create a secret
so<00:03:31.280><c> if</c><00:03:31.360><c> you</c><00:03:31.840><c> recall</c><00:03:32.840><c> we</c><00:03:33.000><c> had</c><00:03:33.120><c> the</c><00:03:33.280><c> secret</c>

00:03:33.670 --> 00:03:33.680 align:start position:0%
so if you recall we had the secret
 

00:03:33.680 --> 00:03:36.390 align:start position:0%
so if you recall we had the secret
random<00:03:34.080><c> token</c><00:03:34.720><c> before</c><00:03:35.720><c> so</c><00:03:35.879><c> let's</c><00:03:36.080><c> go</c><00:03:36.200><c> ahead</c>

00:03:36.390 --> 00:03:36.400 align:start position:0%
random token before so let's go ahead
 

00:03:36.400 --> 00:03:38.270 align:start position:0%
random token before so let's go ahead
and<00:03:36.599><c> click</c><00:03:36.799><c> on</c><00:03:37.000><c> this</c><00:03:37.239><c> create</c><00:03:37.519><c> secret</c><00:03:38.000><c> it'll</c>

00:03:38.270 --> 00:03:38.280 align:start position:0%
and click on this create secret it'll
 

00:03:38.280 --> 00:03:40.350 align:start position:0%
and click on this create secret it'll
take<00:03:38.480><c> it'll</c><00:03:38.680><c> take</c><00:03:38.799><c> you</c><00:03:38.879><c> to</c><00:03:39.040><c> the</c><00:03:39.159><c> secrets</c>

00:03:40.350 --> 00:03:40.360 align:start position:0%
take it'll take you to the secrets
 

00:03:40.360 --> 00:03:44.990 align:start position:0%
take it'll take you to the secrets
area<00:03:41.360><c> and</c><00:03:41.519><c> when</c><00:03:41.640><c> you</c><00:03:41.799><c> click</c><00:03:42.519><c> create</c><00:03:42.840><c> new</c>

00:03:44.990 --> 00:03:45.000 align:start position:0%
area and when you click create new
 

00:03:45.000 --> 00:03:47.229 align:start position:0%
area and when you click create new
secret<00:03:46.000><c> and</c><00:03:46.439><c> um</c><00:03:46.519><c> what</c><00:03:46.640><c> we're</c><00:03:46.720><c> going</c><00:03:46.840><c> to</c><00:03:46.959><c> do</c><00:03:47.120><c> is</c>

00:03:47.229 --> 00:03:47.239 align:start position:0%
secret and um what we're going to do is
 

00:03:47.239 --> 00:03:49.149 align:start position:0%
secret and um what we're going to do is
we're<00:03:47.360><c> going</c><00:03:47.439><c> to</c><00:03:47.599><c> go</c><00:03:47.760><c> down</c><00:03:47.879><c> to</c>

00:03:49.149 --> 00:03:49.159 align:start position:0%
we're going to go down to
 

00:03:49.159 --> 00:03:51.550 align:start position:0%
we're going to go down to
custom<00:03:50.159><c> and</c><00:03:50.319><c> you</c><00:03:50.439><c> can</c><00:03:50.560><c> see</c><00:03:50.840><c> here</c><00:03:51.120><c> that</c><00:03:51.400><c> you</c>

00:03:51.550 --> 00:03:51.560 align:start position:0%
custom and you can see here that you
 

00:03:51.560 --> 00:03:53.190 align:start position:0%
custom and you can see here that you
might<00:03:51.760><c> be</c><00:03:51.920><c> wondering</c><00:03:52.400><c> there</c><00:03:52.519><c> is</c><00:03:52.640><c> a</c><00:03:52.879><c> there</c><00:03:52.959><c> is</c><00:03:53.079><c> a</c>

00:03:53.190 --> 00:03:53.200 align:start position:0%
might be wondering there is a there is a
 

00:03:53.200 --> 00:03:56.030 align:start position:0%
might be wondering there is a there is a
weights<00:03:53.439><c> and</c><00:03:53.959><c> bies</c><00:03:54.959><c> uh</c><00:03:55.079><c> secret</c><00:03:55.519><c> here</c><00:03:55.879><c> we're</c>

00:03:56.030 --> 00:03:56.040 align:start position:0%
weights and bies uh secret here we're
 

00:03:56.040 --> 00:03:58.309 align:start position:0%
weights and bies uh secret here we're
not<00:03:56.200><c> going</c><00:03:56.280><c> to</c><00:03:56.480><c> discuss</c><00:03:56.840><c> that</c><00:03:57.040><c> right</c><00:03:57.200><c> now</c><00:03:58.040><c> um</c>

00:03:58.309 --> 00:03:58.319 align:start position:0%
not going to discuss that right now um
 

00:03:58.319 --> 00:04:00.589 align:start position:0%
not going to discuss that right now um
we're<00:03:58.480><c> going</c><00:03:58.560><c> to</c><00:03:58.720><c> go</c><00:03:59.159><c> and</c><00:03:59.480><c> create</c><00:03:59.879><c> a</c>

00:04:00.589 --> 00:04:00.599 align:start position:0%
we're going to go and create a
 

00:04:00.599 --> 00:04:02.990 align:start position:0%
we're going to go and create a
custom<00:04:01.599><c> custom</c><00:04:01.879><c> secret</c><00:04:02.319><c> because</c><00:04:02.480><c> that's</c><00:04:02.799><c> like</c>

00:04:02.990 --> 00:04:03.000 align:start position:0%
custom custom secret because that's like
 

00:04:03.000 --> 00:04:05.190 align:start position:0%
custom custom secret because that's like
very

00:04:05.190 --> 00:04:05.200 align:start position:0%
very
 

00:04:05.200 --> 00:04:09.270 align:start position:0%
very
flexible<00:04:06.200><c> okay</c><00:04:06.799><c> I'm</c><00:04:06.920><c> going</c><00:04:07.000><c> to</c><00:04:07.439><c> uh</c><00:04:07.560><c> take</c><00:04:07.799><c> the</c><00:04:07.920><c> O</c>

00:04:09.270 --> 00:04:09.280 align:start position:0%
flexible okay I'm going to uh take the O
 

00:04:09.280 --> 00:04:11.630 align:start position:0%
flexible okay I'm going to uh take the O
token<00:04:10.280><c> which</c><00:04:10.480><c> is</c>

00:04:11.630 --> 00:04:11.640 align:start position:0%
token which is
 

00:04:11.640 --> 00:04:14.710 align:start position:0%
token which is
here<00:04:12.640><c> paste</c><00:04:12.879><c> it</c><00:04:13.159><c> there</c><00:04:13.879><c> and</c><00:04:14.000><c> then</c><00:04:14.200><c> this</c><00:04:14.360><c> secret</c>

00:04:14.710 --> 00:04:14.720 align:start position:0%
here paste it there and then this secret
 

00:04:14.720 --> 00:04:18.110 align:start position:0%
here paste it there and then this secret
random<00:04:15.120><c> token</c>

00:04:18.110 --> 00:04:18.120 align:start position:0%
 
 

00:04:18.120 --> 00:04:21.310 align:start position:0%
 
here<00:04:19.120><c> okay</c><00:04:19.359><c> and</c><00:04:19.519><c> then</c><00:04:19.720><c> I'm</c><00:04:19.840><c> going</c><00:04:19.959><c> to</c><00:04:20.079><c> say</c>

00:04:21.310 --> 00:04:21.320 align:start position:0%
here okay and then I'm going to say
 

00:04:21.320 --> 00:04:24.469 align:start position:0%
here okay and then I'm going to say
next<00:04:22.320><c> and</c><00:04:22.520><c> I'm</c><00:04:22.639><c> going</c><00:04:22.759><c> to</c><00:04:23.040><c> say</c><00:04:23.560><c> uh</c><00:04:24.160><c> the</c><00:04:24.360><c> the</c>

00:04:24.469 --> 00:04:24.479 align:start position:0%
next and I'm going to say uh the the
 

00:04:24.479 --> 00:04:29.870 align:start position:0%
next and I'm going to say uh the the
name<00:04:24.680><c> of</c><00:04:24.840><c> my</c><00:04:24.960><c> secret</c><00:04:25.520><c> is</c><00:04:26.520><c> my</c><00:04:27.080><c> random</c><00:04:27.520><c> Secret</c>

00:04:29.870 --> 00:04:29.880 align:start position:0%
name of my secret is my random Secret
 

00:04:29.880 --> 00:04:31.950 align:start position:0%
name of my secret is my random Secret
so<00:04:30.280><c> just</c><00:04:30.400><c> make</c><00:04:30.560><c> sure</c><00:04:30.680><c> you</c><00:04:30.840><c> get</c><00:04:31.000><c> that</c><00:04:31.240><c> right</c><00:04:31.759><c> for</c>

00:04:31.950 --> 00:04:31.960 align:start position:0%
so just make sure you get that right for
 

00:04:31.960 --> 00:04:33.230 align:start position:0%
so just make sure you get that right for
this<00:04:32.120><c> code</c><00:04:32.360><c> to</c>

00:04:33.230 --> 00:04:33.240 align:start position:0%
this code to
 

00:04:33.240 --> 00:04:36.790 align:start position:0%
this code to
work<00:04:34.240><c> it's</c><00:04:34.440><c> easy</c><00:04:34.639><c> to</c><00:04:34.800><c> make</c><00:04:34.919><c> a</c><00:04:35.080><c> mistake</c><00:04:35.800><c> there</c>

00:04:36.790 --> 00:04:36.800 align:start position:0%
work it's easy to make a mistake there
 

00:04:36.800 --> 00:04:39.749 align:start position:0%
work it's easy to make a mistake there
it'll<00:04:37.000><c> say</c><00:04:37.160><c> Seeker</c><00:04:37.479><c> is</c><00:04:38.120><c> created</c><00:04:39.120><c> um</c><00:04:39.479><c> you</c><00:04:39.600><c> can</c>

00:04:39.749 --> 00:04:39.759 align:start position:0%
it'll say Seeker is created um you can
 

00:04:39.759 --> 00:04:43.310 align:start position:0%
it'll say Seeker is created um you can
say<00:04:40.360><c> like</c><00:04:41.360><c> it</c><00:04:41.520><c> shows</c><00:04:41.800><c> you</c><00:04:41.960><c> a</c><00:04:42.080><c> minimal</c><00:04:42.880><c> example</c>

00:04:43.310 --> 00:04:43.320 align:start position:0%
say like it shows you a minimal example
 

00:04:43.320 --> 00:04:46.670 align:start position:0%
say like it shows you a minimal example
of<00:04:43.479><c> how</c><00:04:43.560><c> to</c><00:04:43.720><c> retrieve</c><00:04:44.080><c> the</c><00:04:44.400><c> secret</c><00:04:45.400><c> if</c><00:04:46.400><c> um</c><00:04:46.520><c> you</c>

00:04:46.670 --> 00:04:46.680 align:start position:0%
of how to retrieve the secret if um you
 

00:04:46.680 --> 00:04:49.909 align:start position:0%
of how to retrieve the secret if um you
end<00:04:46.840><c> up</c><00:04:47.759><c> we</c><00:04:47.840><c> will</c><00:04:48.039><c> look</c><00:04:48.160><c> at</c><00:04:48.320><c> the</c><00:04:48.440><c> code</c><00:04:48.759><c> soon</c><00:04:49.720><c> um</c>

00:04:49.909 --> 00:04:49.919 align:start position:0%
end up we will look at the code soon um
 

00:04:49.919 --> 00:04:51.550 align:start position:0%
end up we will look at the code soon um
but<00:04:50.039><c> you</c><00:04:50.120><c> will</c><00:04:50.240><c> see</c><00:04:50.520><c> this</c><00:04:50.639><c> inner</c><00:04:51.039><c> code</c><00:04:51.240><c> this</c><00:04:51.400><c> my</c>

00:04:51.550 --> 00:04:51.560 align:start position:0%
but you will see this inner code this my
 

00:04:51.560 --> 00:04:53.950 align:start position:0%
but you will see this inner code this my
random<00:04:51.880><c> secret</c><00:04:52.759><c> and</c><00:04:53.000><c> and</c><00:04:53.199><c> it</c><00:04:53.280><c> will</c><00:04:53.440><c> be</c><00:04:53.680><c> put</c><00:04:53.840><c> in</c>

00:04:53.950 --> 00:04:53.960 align:start position:0%
random secret and and it will be put in
 

00:04:53.960 --> 00:04:55.350 align:start position:0%
random secret and and it will be put in
the

00:04:55.350 --> 00:04:55.360 align:start position:0%
the
 

00:04:55.360 --> 00:04:58.029 align:start position:0%
the
environment<00:04:56.360><c> so</c><00:04:56.520><c> let's</c><00:04:56.759><c> go</c><00:04:56.880><c> ahead</c><00:04:57.080><c> and</c><00:04:57.320><c> open</c>

00:04:58.029 --> 00:04:58.039 align:start position:0%
environment so let's go ahead and open
 

00:04:58.039 --> 00:04:59.430 align:start position:0%
environment so let's go ahead and open
this<00:04:58.160><c> is</c><00:04:58.280><c> the</c><00:04:58.440><c> web</c><00:04:58.639><c> server</c><00:04:58.960><c> that</c><00:04:59.080><c> we</c><00:04:59.199><c> created</c>

00:04:59.430 --> 00:04:59.440 align:start position:0%
this is the web server that we created
 

00:04:59.440 --> 00:05:00.670 align:start position:0%
this is the web server that we created
in<00:04:59.520><c> the</c><00:04:59.880><c> last</c>

00:05:00.670 --> 00:05:00.680 align:start position:0%
in the last
 

00:05:00.680 --> 00:05:02.469 align:start position:0%
in the last
exercise<00:05:01.680><c> so</c><00:05:01.880><c> we're</c><00:05:02.000><c> going</c><00:05:02.120><c> to</c><00:05:02.199><c> have</c><00:05:02.320><c> to</c>

00:05:02.469 --> 00:05:02.479 align:start position:0%
exercise so we're going to have to
 

00:05:02.479 --> 00:05:04.870 align:start position:0%
exercise so we're going to have to
refactor<00:05:03.120><c> this</c><00:05:03.280><c> every</c><00:05:03.479><c> or</c><00:05:03.639><c> so</c><00:05:03.880><c> slightly</c><00:05:04.600><c> to</c><00:05:04.759><c> be</c>

00:05:04.870 --> 00:05:04.880 align:start position:0%
refactor this every or so slightly to be
 

00:05:04.880 --> 00:05:07.189 align:start position:0%
refactor this every or so slightly to be
able<00:05:05.039><c> to</c><00:05:05.199><c> put</c><00:05:05.320><c> it</c><00:05:05.479><c> on</c><00:05:05.800><c> modal</c><00:05:06.800><c> now</c><00:05:06.960><c> I'm</c><00:05:07.039><c> not</c>

00:05:07.189 --> 00:05:07.199 align:start position:0%
able to put it on modal now I'm not
 

00:05:07.199 --> 00:05:08.670 align:start position:0%
able to put it on modal now I'm not
going<00:05:07.280><c> to</c><00:05:07.400><c> go</c><00:05:07.520><c> deep</c><00:05:07.840><c> into</c>

00:05:08.670 --> 00:05:08.680 align:start position:0%
going to go deep into
 

00:05:08.680 --> 00:05:11.830 align:start position:0%
going to go deep into
modal<00:05:09.680><c> but</c><00:05:09.880><c> what</c><00:05:10.000><c> I'll</c><00:05:10.240><c> do</c><00:05:10.560><c> is</c><00:05:10.800><c> I'll</c><00:05:11.000><c> show</c><00:05:11.280><c> you</c>

00:05:11.830 --> 00:05:11.840 align:start position:0%
modal but what I'll do is I'll show you
 

00:05:11.840 --> 00:05:13.670 align:start position:0%
modal but what I'll do is I'll show you
a<00:05:12.080><c> diff</c>

00:05:13.670 --> 00:05:13.680 align:start position:0%
a diff
 

00:05:13.680 --> 00:05:16.150 align:start position:0%
a diff
between<00:05:14.680><c> the</c><00:05:14.919><c> fast</c><00:05:15.199><c> API</c><00:05:15.560><c> server</c><00:05:15.919><c> that</c><00:05:16.039><c> we</c>

00:05:16.150 --> 00:05:16.160 align:start position:0%
between the fast API server that we
 

00:05:16.160 --> 00:05:17.590 align:start position:0%
between the fast API server that we
created

00:05:17.590 --> 00:05:17.600 align:start position:0%
created
 

00:05:17.600 --> 00:05:20.469 align:start position:0%
created
before<00:05:18.600><c> and</c><00:05:18.960><c> in</c>

00:05:20.469 --> 00:05:20.479 align:start position:0%
before and in
 

00:05:20.479 --> 00:05:24.950 align:start position:0%
before and in
this<00:05:21.479><c> um</c><00:05:22.240><c> so</c><00:05:22.440><c> this</c><00:05:22.560><c> is</c><00:05:22.720><c> just</c><00:05:22.880><c> a</c>

00:05:24.950 --> 00:05:24.960 align:start position:0%
this um so this is just a
 

00:05:24.960 --> 00:05:28.029 align:start position:0%
this um so this is just a
diff<00:05:25.960><c> and</c><00:05:26.400><c> basically</c><00:05:26.880><c> what</c><00:05:27.000><c> you</c><00:05:27.120><c> see</c><00:05:27.400><c> here</c><00:05:27.720><c> is</c>

00:05:28.029 --> 00:05:28.039 align:start position:0%
diff and basically what you see here is
 

00:05:28.039 --> 00:05:30.830 align:start position:0%
diff and basically what you see here is
the<00:05:28.160><c> things</c><00:05:28.319><c> that</c><00:05:28.440><c> are</c><00:05:28.600><c> changed</c><00:05:29.199><c> is</c>

00:05:30.830 --> 00:05:30.840 align:start position:0%
the things that are changed is
 

00:05:30.840 --> 00:05:32.870 align:start position:0%
the things that are changed is
um<00:05:31.400><c> the</c><00:05:31.520><c> most</c><00:05:31.680><c> important</c><00:05:32.000><c> ones</c><00:05:32.199><c> is</c><00:05:32.560><c> we're</c>

00:05:32.870 --> 00:05:32.880 align:start position:0%
um the most important ones is we're
 

00:05:32.880 --> 00:05:36.390 align:start position:0%
um the most important ones is we're
importing<00:05:33.720><c> modal</c><00:05:34.720><c> some</c><00:05:35.280><c> uh</c><00:05:35.360><c> modal</c><00:05:35.759><c> utilities</c>

00:05:36.390 --> 00:05:36.400 align:start position:0%
importing modal some uh modal utilities
 

00:05:36.400 --> 00:05:37.909 align:start position:0%
importing modal some uh modal utilities
here<00:05:36.680><c> like</c><00:05:36.840><c> the</c>

00:05:37.909 --> 00:05:37.919 align:start position:0%
here like the
 

00:05:37.919 --> 00:05:40.870 align:start position:0%
here like the
secret<00:05:38.919><c> something</c><00:05:39.600><c> for</c><00:05:39.800><c> an</c><00:05:39.960><c> image</c><00:05:40.199><c> so</c><00:05:40.400><c> we</c><00:05:40.520><c> can</c>

00:05:40.870 --> 00:05:40.880 align:start position:0%
secret something for an image so we can
 

00:05:40.880 --> 00:05:44.110 align:start position:0%
secret something for an image so we can
build<00:05:41.080><c> a</c><00:05:41.240><c> Docker</c><00:05:41.759><c> image</c><00:05:42.759><c> we</c><00:05:42.880><c> are</c><00:05:43.680><c> uh</c><00:05:43.800><c> creating</c>

00:05:44.110 --> 00:05:44.120 align:start position:0%
build a Docker image we are uh creating
 

00:05:44.120 --> 00:05:46.710 align:start position:0%
build a Docker image we are uh creating
a<00:05:44.240><c> Docker</c><00:05:44.639><c> image</c><00:05:45.080><c> that</c><00:05:45.280><c> has</c><00:05:45.479><c> fast</c><00:05:45.880><c> API</c><00:05:46.280><c> in</c><00:05:46.440><c> it</c>

00:05:46.710 --> 00:05:46.720 align:start position:0%
a Docker image that has fast API in it
 

00:05:46.720 --> 00:05:48.629 align:start position:0%
a Docker image that has fast API in it
in<00:05:46.960><c> pantic</c><00:05:47.680><c> as</c>

00:05:48.629 --> 00:05:48.639 align:start position:0%
in pantic as
 

00:05:48.639 --> 00:05:51.950 align:start position:0%
in pantic as
well<00:05:49.639><c> and</c><00:05:49.800><c> we're</c><00:05:50.000><c> using</c><00:05:50.319><c> something</c><00:05:50.600><c> called</c><00:05:50.759><c> a</c>

00:05:51.950 --> 00:05:51.960 align:start position:0%
well and we're using something called a
 

00:05:51.960 --> 00:05:57.350 align:start position:0%
well and we're using something called a
stub<00:05:52.960><c> that's</c><00:05:53.199><c> just</c><00:05:53.400><c> to</c><00:05:54.319><c> um</c><00:05:54.560><c> kind</c><00:05:54.680><c> of</c><00:05:54.840><c> name</c><00:05:55.080><c> the</c>

00:05:57.350 --> 00:05:57.360 align:start position:0%
 
 

00:05:57.360 --> 00:06:00.350 align:start position:0%
 
application<00:05:58.360><c> and</c><00:05:58.560><c> instead</c><00:05:58.840><c> of</c><00:05:59.080><c> this</c><00:05:59.520><c> um</c><00:05:59.800><c> um</c>

00:06:00.350 --> 00:06:00.360 align:start position:0%
application and instead of this um um
 

00:06:00.360 --> 00:06:03.550 align:start position:0%
application and instead of this um um
this<00:06:00.560><c> like</c><00:06:00.800><c> Fast</c><00:06:01.400><c> API</c><00:06:02.400><c> uh</c><00:06:02.560><c> decorator</c><00:06:03.400><c> we're</c>

00:06:03.550 --> 00:06:03.560 align:start position:0%
this like Fast API uh decorator we're
 

00:06:03.560 --> 00:06:05.749 align:start position:0%
this like Fast API uh decorator we're
going<00:06:03.639><c> to</c><00:06:03.759><c> be</c><00:06:03.919><c> using</c><00:06:04.840><c> these</c><00:06:05.080><c> decorators</c><00:06:05.560><c> from</c>

00:06:05.749 --> 00:06:05.759 align:start position:0%
going to be using these decorators from
 

00:06:05.759 --> 00:06:08.950 align:start position:0%
going to be using these decorators from
modal<00:06:06.639><c> they</c><00:06:06.919><c> kind</c><00:06:07.000><c> of</c><00:06:07.120><c> do</c><00:06:07.319><c> the</c><00:06:07.400><c> same</c><00:06:07.759><c> thing</c><00:06:08.599><c> um</c>

00:06:08.950 --> 00:06:08.960 align:start position:0%
modal they kind of do the same thing um
 

00:06:08.960 --> 00:06:11.430 align:start position:0%
modal they kind of do the same thing um
basically<00:06:09.840><c> we</c><00:06:10.080><c> get</c><00:06:10.400><c> we</c><00:06:10.599><c> get</c><00:06:10.720><c> a</c><00:06:11.080><c> we</c><00:06:11.199><c> have</c><00:06:11.319><c> a</c>

00:06:11.430 --> 00:06:11.440 align:start position:0%
basically we get we get a we have a
 

00:06:11.440 --> 00:06:12.830 align:start position:0%
basically we get we get a we have a
special<00:06:11.720><c> decorator</c><00:06:12.160><c> that</c><00:06:12.280><c> allows</c><00:06:12.520><c> us</c><00:06:12.639><c> to</c><00:06:12.759><c> get</c>

00:06:12.830 --> 00:06:12.840 align:start position:0%
special decorator that allows us to get
 

00:06:12.840 --> 00:06:14.390 align:start position:0%
special decorator that allows us to get
the

00:06:14.390 --> 00:06:14.400 align:start position:0%
the
 

00:06:14.400 --> 00:06:18.390 align:start position:0%
the
secret<00:06:15.759><c> and</c><00:06:16.759><c> uh</c><00:06:16.919><c> this</c><00:06:17.120><c> web</c>

00:06:18.390 --> 00:06:18.400 align:start position:0%
secret and uh this web
 

00:06:18.400 --> 00:06:20.629 align:start position:0%
secret and uh this web
endpoint<00:06:19.400><c> the</c><00:06:19.479><c> rest</c><00:06:19.639><c> of</c><00:06:19.759><c> the</c><00:06:19.840><c> code</c><00:06:20.080><c> is</c><00:06:20.280><c> exactly</c>

00:06:20.629 --> 00:06:20.639 align:start position:0%
endpoint the rest of the code is exactly
 

00:06:20.639 --> 00:06:22.749 align:start position:0%
endpoint the rest of the code is exactly
the<00:06:20.759><c> same</c><00:06:21.560><c> now</c><00:06:21.759><c> you</c><00:06:21.840><c> don't</c><00:06:22.080><c> have</c><00:06:22.199><c> to</c><00:06:22.360><c> get</c><00:06:22.560><c> too</c>

00:06:22.749 --> 00:06:22.759 align:start position:0%
the same now you don't have to get too
 

00:06:22.759 --> 00:06:26.189 align:start position:0%
the same now you don't have to get too
hung<00:06:23.039><c> up</c><00:06:23.960><c> on</c><00:06:24.240><c> what</c><00:06:24.400><c> this</c><00:06:24.599><c> code</c>

00:06:26.189 --> 00:06:26.199 align:start position:0%
hung up on what this code
 

00:06:26.199 --> 00:06:28.510 align:start position:0%
hung up on what this code
does<00:06:27.199><c> a</c><00:06:27.280><c> lot</c><00:06:27.440><c> of</c><00:06:27.560><c> it</c><00:06:27.759><c> you</c><00:06:27.840><c> can</c><00:06:28.000><c> think</c><00:06:28.160><c> of</c><00:06:28.280><c> it</c><00:06:28.360><c> as</c>

00:06:28.510 --> 00:06:28.520 align:start position:0%
does a lot of it you can think of it as
 

00:06:28.520 --> 00:06:31.350 align:start position:0%
does a lot of it you can think of it as
boiler<00:06:28.880><c> plate</c><00:06:29.720><c> just</c><00:06:29.919><c> know</c><00:06:30.280><c> that</c><00:06:30.880><c> this</c><00:06:31.000><c> is</c><00:06:31.160><c> some</c>

00:06:31.350 --> 00:06:31.360 align:start position:0%
boiler plate just know that this is some
 

00:06:31.360 --> 00:06:33.589 align:start position:0%
boiler plate just know that this is some
minimal<00:06:31.759><c> change</c><00:06:32.199><c> that</c><00:06:32.360><c> allows</c><00:06:32.759><c> this</c><00:06:32.960><c> code</c><00:06:33.199><c> to</c>

00:06:33.589 --> 00:06:33.599 align:start position:0%
minimal change that allows this code to
 

00:06:33.599 --> 00:06:36.350 align:start position:0%
minimal change that allows this code to
be<00:06:33.840><c> be</c><00:06:34.039><c> deployed</c><00:06:34.479><c> to</c><00:06:34.599><c> the</c><00:06:34.840><c> cloud</c><00:06:35.840><c> and</c><00:06:36.039><c> instead</c>

00:06:36.350 --> 00:06:36.360 align:start position:0%
be be deployed to the cloud and instead
 

00:06:36.360 --> 00:06:38.990 align:start position:0%
be be deployed to the cloud and instead
of<00:06:36.759><c> hardcoding</c><00:06:37.759><c> the</c><00:06:38.039><c> environment</c><00:06:38.520><c> variable</c>

00:06:38.990 --> 00:06:39.000 align:start position:0%
of hardcoding the environment variable
 

00:06:39.000 --> 00:06:40.629 align:start position:0%
of hardcoding the environment variable
here<00:06:39.680><c> which</c><00:06:39.880><c> you</c><00:06:40.000><c> know</c><00:06:40.120><c> you</c><00:06:40.199><c> wouldn't</c><00:06:40.440><c> want</c><00:06:40.520><c> to</c>

00:06:40.629 --> 00:06:40.639 align:start position:0%
here which you know you wouldn't want to
 

00:06:40.639 --> 00:06:42.710 align:start position:0%
here which you know you wouldn't want to
do<00:06:40.759><c> in</c><00:06:40.880><c> real</c><00:06:41.120><c> life</c><00:06:41.759><c> this</c><00:06:41.880><c> is</c><00:06:42.160><c> actually</c><00:06:42.560><c> going</c>

00:06:42.710 --> 00:06:42.720 align:start position:0%
do in real life this is actually going
 

00:06:42.720 --> 00:06:45.070 align:start position:0%
do in real life this is actually going
to<00:06:42.840><c> be</c><00:06:43.000><c> pulled</c><00:06:43.319><c> from</c><00:06:43.479><c> the</c><00:06:44.080><c> environment</c>

00:06:45.070 --> 00:06:45.080 align:start position:0%
to be pulled from the environment
 

00:06:45.080 --> 00:06:46.749 align:start position:0%
to be pulled from the environment
specifically<00:06:45.639><c> this</c><00:06:45.759><c> a</c><00:06:46.039><c> token</c><00:06:46.319><c> environment</c>

00:06:46.749 --> 00:06:46.759 align:start position:0%
specifically this a token environment
 

00:06:46.759 --> 00:06:51.189 align:start position:0%
specifically this a token environment
variable<00:06:47.400><c> which</c><00:06:47.560><c> we</c><00:06:47.800><c> set</c><00:06:48.720><c> in</c><00:06:49.639><c> in</c>

00:06:51.189 --> 00:06:51.199 align:start position:0%
variable which we set in in
 

00:06:51.199 --> 00:06:55.790 align:start position:0%
variable which we set in in
modal<00:06:52.199><c> so</c><00:06:52.720><c> going</c><00:06:53.080><c> back</c><00:06:54.039><c> to</c><00:06:55.039><c> uh</c><00:06:55.199><c> let's</c><00:06:55.400><c> let's</c><00:06:55.599><c> go</c>

00:06:55.790 --> 00:06:55.800 align:start position:0%
modal so going back to uh let's let's go
 

00:06:55.800 --> 00:06:57.790 align:start position:0%
modal so going back to uh let's let's go
back<00:06:55.960><c> to</c><00:06:56.199><c> the</c><00:06:56.319><c> read</c>

00:06:57.790 --> 00:06:57.800 align:start position:0%
back to the read
 

00:06:57.800 --> 00:07:01.230 align:start position:0%
back to the read
me<00:06:58.800><c> and</c><00:06:59.639><c> let's</c><00:06:59.960><c> preview</c><00:07:00.360><c> the</c><00:07:00.520><c> re</c><00:07:00.680><c> read</c><00:07:00.960><c> me</c><00:07:01.120><c> so</c>

00:07:01.230 --> 00:07:01.240 align:start position:0%
me and let's preview the re read me so
 

00:07:01.240 --> 00:07:04.749 align:start position:0%
me and let's preview the re read me so
we<00:07:01.400><c> can</c><00:07:01.960><c> actually</c><00:07:02.280><c> see</c><00:07:02.960><c> it</c><00:07:03.960><c> let</c><00:07:04.080><c> me</c><00:07:04.319><c> put</c><00:07:04.479><c> it</c>

00:07:04.749 --> 00:07:04.759 align:start position:0%
we can actually see it let me put it
 

00:07:04.759 --> 00:07:07.710 align:start position:0%
we can actually see it let me put it
back<00:07:05.039><c> here</c><00:07:05.599><c> there</c><00:07:05.720><c> we</c><00:07:06.039><c> go</c><00:07:07.039><c> so</c><00:07:07.280><c> next</c><00:07:07.479><c> thing</c><00:07:07.639><c> we</c>

00:07:07.710 --> 00:07:07.720 align:start position:0%
back here there we go so next thing we
 

00:07:07.720 --> 00:07:09.309 align:start position:0%
back here there we go so next thing we
want<00:07:07.840><c> to</c><00:07:07.960><c> do</c><00:07:08.240><c> is</c>

00:07:09.309 --> 00:07:09.319 align:start position:0%
want to do is
 

00:07:09.319 --> 00:07:12.189 align:start position:0%
want to do is
deploy<00:07:10.319><c> this</c><00:07:10.720><c> modal</c><00:07:11.199><c> web</c><00:07:11.479><c> hook</c><00:07:11.720><c> so</c><00:07:11.879><c> let's</c><00:07:12.080><c> go</c>

00:07:12.189 --> 00:07:12.199 align:start position:0%
deploy this modal web hook so let's go
 

00:07:12.199 --> 00:07:15.150 align:start position:0%
deploy this modal web hook so let's go
ahead<00:07:12.360><c> and</c><00:07:12.520><c> do</c><00:07:12.720><c> that</c><00:07:13.520><c> we're</c><00:07:13.639><c> going</c><00:07:13.759><c> to</c><00:07:13.919><c> go</c><00:07:14.160><c> here</c>

00:07:15.150 --> 00:07:15.160 align:start position:0%
ahead and do that we're going to go here
 

00:07:15.160 --> 00:07:18.110 align:start position:0%
ahead and do that we're going to go here
and<00:07:16.160><c> I</c><00:07:16.280><c> will</c><00:07:16.599><c> just</c><00:07:16.879><c> go</c>

00:07:18.110 --> 00:07:18.120 align:start position:0%
and I will just go
 

00:07:18.120 --> 00:07:21.510 align:start position:0%
and I will just go
modal

00:07:21.510 --> 00:07:21.520 align:start position:0%
 
 

00:07:21.520 --> 00:07:26.909 align:start position:0%
 
deploy<00:07:22.520><c> um</c><00:07:22.759><c> and</c><00:07:22.879><c> then</c><00:07:23.080><c> it</c><00:07:23.160><c> will</c><00:07:23.319><c> be</c><00:07:23.879><c> server.py</c>

00:07:26.909 --> 00:07:26.919 align:start position:0%
 
 

00:07:26.919 --> 00:07:30.270 align:start position:0%
 
okay<00:07:27.919><c> that</c><00:07:28.039><c> was</c><00:07:28.280><c> fast</c><00:07:29.160><c> um</c><00:07:29.599><c> basically</c><00:07:30.080><c> what</c>

00:07:30.270 --> 00:07:30.280 align:start position:0%
okay that was fast um basically what
 

00:07:30.280 --> 00:07:32.629 align:start position:0%
okay that was fast um basically what
because<00:07:30.479><c> I've</c><00:07:30.680><c> deployed</c><00:07:31.080><c> this</c><00:07:31.520><c> before</c><00:07:32.520><c> U</c>

00:07:32.629 --> 00:07:32.639 align:start position:0%
because I've deployed this before U
 

00:07:32.639 --> 00:07:36.150 align:start position:0%
because I've deployed this before U
modal<00:07:33.120><c> actually</c><00:07:33.440><c> does</c><00:07:33.919><c> cache</c><00:07:34.919><c> things</c>

00:07:36.150 --> 00:07:36.160 align:start position:0%
modal actually does cache things
 

00:07:36.160 --> 00:07:39.510 align:start position:0%
modal actually does cache things
intelligently<00:07:37.240><c> so</c><00:07:38.240><c> uh</c><00:07:38.360><c> this</c><00:07:38.479><c> has</c><00:07:38.639><c> been</c>

00:07:39.510 --> 00:07:39.520 align:start position:0%
intelligently so uh this has been
 

00:07:39.520 --> 00:07:43.110 align:start position:0%
intelligently so uh this has been
deployed<00:07:40.520><c> you'll</c><00:07:40.720><c> get</c><00:07:40.879><c> two</c><00:07:41.120><c> links</c><00:07:42.120><c> this</c><00:07:42.319><c> link</c>

00:07:43.110 --> 00:07:43.120 align:start position:0%
deployed you'll get two links this link
 

00:07:43.120 --> 00:07:45.670 align:start position:0%
deployed you'll get two links this link
is<00:07:43.319><c> actually</c><00:07:43.639><c> the</c><00:07:43.960><c> Endo</c><00:07:44.960><c> of</c>

00:07:45.670 --> 00:07:45.680 align:start position:0%
is actually the Endo of
 

00:07:45.680 --> 00:07:48.550 align:start position:0%
is actually the Endo of
your<00:07:46.680><c> this</c><00:07:46.800><c> is</c><00:07:46.960><c> the</c><00:07:47.080><c> endpoint</c><00:07:47.960><c> of</c><00:07:48.120><c> your</c><00:07:48.280><c> web</c>

00:07:48.550 --> 00:07:48.560 align:start position:0%
your this is the endpoint of your web
 

00:07:48.560 --> 00:07:50.790 align:start position:0%
your this is the endpoint of your web
hook<00:07:49.199><c> instead</c><00:07:49.479><c> of</c><00:07:49.599><c> Local</c><00:07:49.960><c> Host</c><00:07:50.199><c> you</c><00:07:50.319><c> can</c><00:07:50.440><c> send</c>

00:07:50.790 --> 00:07:50.800 align:start position:0%
hook instead of Local Host you can send
 

00:07:50.800 --> 00:07:52.070 align:start position:0%
hook instead of Local Host you can send
payloads

00:07:52.070 --> 00:07:52.080 align:start position:0%
payloads
 

00:07:52.080 --> 00:07:54.390 align:start position:0%
payloads
there<00:07:53.080><c> and</c><00:07:53.199><c> then</c><00:07:53.479><c> this</c><00:07:53.759><c> is</c><00:07:54.000><c> actually</c><00:07:54.280><c> where</c>

00:07:54.390 --> 00:07:54.400 align:start position:0%
there and then this is actually where
 

00:07:54.400 --> 00:07:56.189 align:start position:0%
there and then this is actually where
you<00:07:54.479><c> can</c><00:07:54.680><c> look</c><00:07:54.800><c> at</c><00:07:54.919><c> the</c><00:07:55.039><c> log</c><00:07:55.400><c> so</c><00:07:55.599><c> this</c><00:07:56.039><c> where</c>

00:07:56.189 --> 00:07:56.199 align:start position:0%
you can look at the log so this where
 

00:07:56.199 --> 00:07:58.790 align:start position:0%
you can look at the log so this where
you<00:07:56.720><c> uh</c><00:07:56.879><c> where</c><00:07:57.000><c> you</c><00:07:57.280><c> view</c><00:07:57.560><c> the</c><00:07:57.800><c> deployment</c>

00:07:58.790 --> 00:07:58.800 align:start position:0%
you uh where you view the deployment
 

00:07:58.800 --> 00:08:01.749 align:start position:0%
you uh where you view the deployment
let's<00:07:59.000><c> click</c><00:07:59.159><c> on</c><00:07:59.280><c> that</c>

00:08:01.749 --> 00:08:01.759 align:start position:0%
 
 

00:08:01.759 --> 00:08:03.909 align:start position:0%
 
you<00:08:01.879><c> can</c><00:08:02.000><c> see</c><00:08:02.199><c> this</c><00:08:02.319><c> has</c><00:08:02.479><c> been</c><00:08:02.800><c> deployed</c><00:08:03.800><c> and</c>

00:08:03.909 --> 00:08:03.919 align:start position:0%
you can see this has been deployed and
 

00:08:03.919 --> 00:08:08.350 align:start position:0%
you can see this has been deployed and
then<00:08:04.039><c> we</c><00:08:04.159><c> can</c><00:08:04.280><c> go</c><00:08:04.400><c> to</c><00:08:04.680><c> logs</c><00:08:05.680><c> to</c><00:08:05.840><c> see</c><00:08:06.280><c> logs</c><00:08:07.360><c> now</c>

00:08:08.350 --> 00:08:08.360 align:start position:0%
then we can go to logs to see logs now
 

00:08:08.360 --> 00:08:10.710 align:start position:0%
then we can go to logs to see logs now
let's<00:08:08.680><c> click</c><00:08:08.879><c> into</c>

00:08:10.710 --> 00:08:10.720 align:start position:0%
let's click into
 

00:08:10.720 --> 00:08:13.869 align:start position:0%
let's click into
here<00:08:11.720><c> and</c><00:08:11.840><c> it's</c><00:08:11.960><c> listening</c><00:08:12.319><c> for</c>

00:08:13.869 --> 00:08:13.879 align:start position:0%
here and it's listening for
 

00:08:13.879 --> 00:08:17.790 align:start position:0%
here and it's listening for
logs<00:08:14.879><c> and</c><00:08:15.479><c> looks</c><00:08:15.720><c> like</c><00:08:15.879><c> there's</c><00:08:16.199><c> no</c><00:08:16.440><c> logs</c>

00:08:17.790 --> 00:08:17.800 align:start position:0%
logs and looks like there's no logs
 

00:08:17.800 --> 00:08:20.189 align:start position:0%
logs and looks like there's no logs
currently<00:08:18.800><c> um</c><00:08:19.159><c> so</c><00:08:19.400><c> next</c><00:08:19.560><c> thing</c><00:08:19.680><c> we</c><00:08:19.800><c> want</c><00:08:19.879><c> to</c><00:08:20.039><c> do</c>

00:08:20.189 --> 00:08:20.199 align:start position:0%
currently um so next thing we want to do
 

00:08:20.199 --> 00:08:24.270 align:start position:0%
currently um so next thing we want to do
is<00:08:20.360><c> send</c><00:08:20.560><c> a</c><00:08:20.759><c> payload</c><00:08:21.599><c> to</c><00:08:22.240><c> that</c><00:08:22.479><c> web</c>

00:08:24.270 --> 00:08:24.280 align:start position:0%
is send a payload to that web
 

00:08:24.280 --> 00:08:27.230 align:start position:0%
is send a payload to that web
endpoint<00:08:25.280><c> so</c><00:08:25.479><c> what</c><00:08:25.599><c> we</c><00:08:25.720><c> can</c><00:08:25.879><c> do</c><00:08:26.120><c> is</c><00:08:26.759><c> we</c><00:08:27.000><c> have</c>

00:08:27.230 --> 00:08:27.240 align:start position:0%
endpoint so what we can do is we have
 

00:08:27.240 --> 00:08:29.629 align:start position:0%
endpoint so what we can do is we have
this<00:08:27.440><c> URL</c>

00:08:29.629 --> 00:08:29.639 align:start position:0%
this URL
 

00:08:29.639 --> 00:08:33.190 align:start position:0%
this URL
and<00:08:29.800><c> we</c><00:08:29.919><c> can</c><00:08:30.080><c> use</c><00:08:30.360><c> that</c><00:08:30.520><c> same</c><00:08:31.039><c> curl</c><00:08:32.080><c> script</c><00:08:33.080><c> let</c>

00:08:33.190 --> 00:08:33.200 align:start position:0%
and we can use that same curl script let
 

00:08:33.200 --> 00:08:35.190 align:start position:0%
and we can use that same curl script let
me<00:08:33.360><c> just</c><00:08:33.560><c> remind</c><00:08:33.880><c> you</c><00:08:34.039><c> what's</c><00:08:34.200><c> in</c><00:08:34.320><c> that</c><00:08:34.479><c> curl</c>

00:08:35.190 --> 00:08:35.200 align:start position:0%
me just remind you what's in that curl
 

00:08:35.200 --> 00:08:37.870 align:start position:0%
me just remind you what's in that curl
uh<00:08:35.320><c> curl</c>

00:08:37.870 --> 00:08:37.880 align:start position:0%
 
 

00:08:37.880 --> 00:08:40.469 align:start position:0%
 
script<00:08:38.880><c> the</c><00:08:39.000><c> curl</c><00:08:39.320><c> script</c><00:08:39.640><c> is</c>

00:08:40.469 --> 00:08:40.479 align:start position:0%
script the curl script is
 

00:08:40.479 --> 00:08:43.269 align:start position:0%
script the curl script is
just<00:08:41.479><c> um</c><00:08:42.159><c> you</c><00:08:42.279><c> know</c><00:08:42.479><c> just</c><00:08:42.599><c> sending</c><00:08:42.880><c> a</c><00:08:43.000><c> curl</c>

00:08:43.269 --> 00:08:43.279 align:start position:0%
just um you know just sending a curl
 

00:08:43.279 --> 00:08:45.070 align:start position:0%
just um you know just sending a curl
command<00:08:43.599><c> to</c><00:08:43.760><c> a</c><00:08:43.880><c> URL</c><00:08:44.279><c> it's</c><00:08:44.399><c> the</c><00:08:44.560><c> same</c><00:08:44.800><c> command</c>

00:08:45.070 --> 00:08:45.080 align:start position:0%
command to a URL it's the same command
 

00:08:45.080 --> 00:08:47.110 align:start position:0%
command to a URL it's the same command
we<00:08:45.200><c> sent</c><00:08:45.399><c> to</c><00:08:45.560><c> Local</c><00:08:45.880><c> Host</c><00:08:46.240><c> and</c><00:08:46.560><c> but</c><00:08:46.680><c> instead</c><00:08:46.959><c> of</c>

00:08:47.110 --> 00:08:47.120 align:start position:0%
we sent to Local Host and but instead of
 

00:08:47.120 --> 00:08:49.670 align:start position:0%
we sent to Local Host and but instead of
Local<00:08:47.480><c> Host</c><00:08:48.480><c> instead</c><00:08:48.839><c> of</c><00:08:49.000><c> Local</c><00:08:49.320><c> Host</c><00:08:49.560><c> we're</c>

00:08:49.670 --> 00:08:49.680 align:start position:0%
Local Host instead of Local Host we're
 

00:08:49.680 --> 00:08:51.430 align:start position:0%
Local Host instead of Local Host we're
going<00:08:49.800><c> to</c><00:08:49.880><c> send</c><00:08:50.080><c> it</c><00:08:50.200><c> to</c><00:08:50.399><c> that</c><00:08:50.560><c> endpoint</c><00:08:51.080><c> so</c><00:08:51.279><c> to</c>

00:08:51.430 --> 00:08:51.440 align:start position:0%
going to send it to that endpoint so to
 

00:08:51.440 --> 00:08:53.710 align:start position:0%
going to send it to that endpoint so to
do<00:08:51.600><c> that</c><00:08:51.800><c> we'll</c><00:08:52.000><c> just</c><00:08:52.160><c> do</c>

00:08:53.710 --> 00:08:53.720 align:start position:0%
do that we'll just do
 

00:08:53.720 --> 00:08:57.190 align:start position:0%
do that we'll just do
CSH<00:08:54.720><c> um</c>

00:08:57.190 --> 00:08:57.200 align:start position:0%
 
 

00:08:57.200 --> 00:09:00.910 align:start position:0%
 
whoops<00:08:58.200><c> c.</c><00:08:58.720><c> sh</c>

00:09:00.910 --> 00:09:00.920 align:start position:0%
whoops c. sh
 

00:09:00.920 --> 00:09:04.509 align:start position:0%
whoops c. sh
and<00:09:01.600><c> this</c><00:09:02.399><c> end</c><00:09:02.760><c> point</c><00:09:03.519><c> sorry</c><00:09:03.920><c> this</c><00:09:04.079><c> end</c><00:09:04.440><c> this</c>

00:09:04.509 --> 00:09:04.519 align:start position:0%
and this end point sorry this end this
 

00:09:04.519 --> 00:09:05.829 align:start position:0%
and this end point sorry this end this
is<00:09:04.640><c> the</c><00:09:04.839><c> endpoint</c><00:09:05.279><c> right</c><00:09:05.399><c> here</c><00:09:05.560><c> the</c><00:09:05.640><c> one</c><00:09:05.760><c> that</c>

00:09:05.829 --> 00:09:05.839 align:start position:0%
is the endpoint right here the one that
 

00:09:05.839 --> 00:09:10.829 align:start position:0%
is the endpoint right here the one that
ends<00:09:06.040><c> in</c><00:09:06.440><c> run</c><00:09:07.440><c> don't</c><00:09:07.640><c> get</c><00:09:07.760><c> them</c>

00:09:10.829 --> 00:09:10.839 align:start position:0%
 
 

00:09:10.839 --> 00:09:14.030 align:start position:0%
 
confused<00:09:11.839><c> all</c><00:09:12.000><c> right</c><00:09:12.240><c> it's</c><00:09:12.399><c> going</c><00:09:12.519><c> to</c><00:09:13.040><c> send</c>

00:09:14.030 --> 00:09:14.040 align:start position:0%
confused all right it's going to send
 

00:09:14.040 --> 00:09:14.990 align:start position:0%
confused all right it's going to send
that

00:09:14.990 --> 00:09:15.000 align:start position:0%
that
 

00:09:15.000 --> 00:09:17.990 align:start position:0%
that
payload<00:09:16.000><c> aha</c><00:09:16.519><c> message</c><00:09:16.959><c> event</c><00:09:17.560><c> process</c>

00:09:17.990 --> 00:09:18.000 align:start position:0%
payload aha message event process
 

00:09:18.000 --> 00:09:20.230 align:start position:0%
payload aha message event process
successfully<00:09:18.560><c> we</c><00:09:18.680><c> got</c><00:09:18.880><c> that</c><00:09:19.079><c> back</c><00:09:19.720><c> and</c><00:09:19.839><c> we</c><00:09:20.000><c> go</c>

00:09:20.230 --> 00:09:20.240 align:start position:0%
successfully we got that back and we go
 

00:09:20.240 --> 00:09:23.630 align:start position:0%
successfully we got that back and we go
back<00:09:20.399><c> in</c><00:09:20.600><c> and</c><00:09:20.800><c> can</c><00:09:20.920><c> see</c><00:09:21.120><c> the</c><00:09:21.560><c> logs</c><00:09:22.560><c> you</c><00:09:22.680><c> can</c><00:09:22.880><c> see</c>

00:09:23.630 --> 00:09:23.640 align:start position:0%
back in and can see the logs you can see
 

00:09:23.640 --> 00:09:26.670 align:start position:0%
back in and can see the logs you can see
on<00:09:24.079><c> modal</c><00:09:25.079><c> the</c><00:09:25.279><c> exact</c><00:09:25.600><c> same</c><00:09:25.800><c> logs</c><00:09:26.079><c> are</c><00:09:26.240><c> printed</c>

00:09:26.670 --> 00:09:26.680 align:start position:0%
on modal the exact same logs are printed
 

00:09:26.680 --> 00:09:29.870 align:start position:0%
on modal the exact same logs are printed
out<00:09:27.680><c> so</c><00:09:27.920><c> this</c><00:09:28.040><c> is</c><00:09:28.800><c> exactly</c><00:09:29.200><c> the</c><00:09:29.519><c> same</c><00:09:29.680><c> thing</c>

00:09:29.870 --> 00:09:29.880 align:start position:0%
out so this is exactly the same thing
 

00:09:29.880 --> 00:09:31.269 align:start position:0%
out so this is exactly the same thing
that<00:09:29.959><c> you</c><00:09:30.079><c> did</c>

00:09:31.269 --> 00:09:31.279 align:start position:0%
that you did
 

00:09:31.279 --> 00:09:33.670 align:start position:0%
that you did
locally<00:09:32.279><c> but</c><00:09:32.680><c> this</c><00:09:32.800><c> is</c><00:09:32.959><c> now</c><00:09:33.120><c> running</c><00:09:33.360><c> in</c><00:09:33.480><c> the</c>

00:09:33.670 --> 00:09:33.680 align:start position:0%
locally but this is now running in the
 

00:09:33.680 --> 00:09:36.910 align:start position:0%
locally but this is now running in the
cloud<00:09:34.560><c> and</c><00:09:34.760><c> now</c><00:09:34.959><c> you</c><00:09:35.240><c> have</c><00:09:35.880><c> a</c><00:09:36.040><c> web</c><00:09:36.320><c> hook</c><00:09:36.519><c> server</c>

00:09:36.910 --> 00:09:36.920 align:start position:0%
cloud and now you have a web hook server
 

00:09:36.920 --> 00:09:39.069 align:start position:0%
cloud and now you have a web hook server
running<00:09:37.200><c> in</c><00:09:37.279><c> the</c><00:09:37.480><c> cloud</c><00:09:38.480><c> and</c><00:09:38.600><c> hopefully</c>

00:09:39.069 --> 00:09:39.079 align:start position:0%
running in the cloud and hopefully
 

00:09:39.079 --> 00:09:41.110 align:start position:0%
running in the cloud and hopefully
because<00:09:39.279><c> you've</c><00:09:39.440><c> done</c><00:09:39.640><c> this</c><00:09:40.000><c> locally</c><00:09:41.000><c> you</c>

00:09:41.110 --> 00:09:41.120 align:start position:0%
because you've done this locally you
 

00:09:41.120 --> 00:09:42.910 align:start position:0%
because you've done this locally you
should<00:09:41.360><c> have</c><00:09:41.480><c> a</c><00:09:41.720><c> good</c><00:09:41.880><c> mental</c><00:09:42.240><c> model</c><00:09:42.560><c> of</c><00:09:42.760><c> how</c>

00:09:42.910 --> 00:09:42.920 align:start position:0%
should have a good mental model of how
 

00:09:42.920 --> 00:09:45.030 align:start position:0%
should have a good mental model of how
this<00:09:43.040><c> is</c><00:09:43.200><c> working</c><00:09:43.560><c> and</c><00:09:43.680><c> what</c><00:09:43.800><c> is</c>

00:09:45.030 --> 00:09:45.040 align:start position:0%
this is working and what is
 

00:09:45.040 --> 00:09:47.829 align:start position:0%
this is working and what is
doing<00:09:46.040><c> the</c><00:09:46.200><c> next</c><00:09:46.399><c> thing</c><00:09:46.519><c> we're</c><00:09:46.640><c> going</c><00:09:46.760><c> to</c><00:09:46.920><c> do</c>

00:09:47.829 --> 00:09:47.839 align:start position:0%
doing the next thing we're going to do
 

00:09:47.839 --> 00:09:50.949 align:start position:0%
doing the next thing we're going to do
is<00:09:48.240><c> is</c><00:09:48.440><c> we</c><00:09:48.560><c> are</c><00:09:48.800><c> going</c><00:09:49.000><c> to</c><00:09:49.640><c> create</c><00:09:49.959><c> an</c>

00:09:50.949 --> 00:09:50.959 align:start position:0%
is is we are going to create an
 

00:09:50.959 --> 00:09:53.750 align:start position:0%
is is we are going to create an
automation<00:09:51.959><c> that</c><00:09:52.680><c> triggers</c><00:09:53.200><c> weights</c><00:09:53.519><c> and</c>

00:09:53.750 --> 00:09:53.760 align:start position:0%
automation that triggers weights and
 

00:09:53.760 --> 00:09:56.710 align:start position:0%
automation that triggers weights and
biases<00:09:54.680><c> to</c><00:09:54.839><c> send</c><00:09:55.440><c> the</c><00:09:55.560><c> web</c><00:09:55.839><c> hook</c><00:09:56.040><c> payload</c>

00:09:56.710 --> 00:09:56.720 align:start position:0%
biases to send the web hook payload
 

00:09:56.720 --> 00:09:58.630 align:start position:0%
biases to send the web hook payload
instead<00:09:57.000><c> of</c><00:09:57.240><c> you</c><00:09:57.480><c> sending</c><00:09:57.880><c> the</c><00:09:58.040><c> payload</c><00:09:58.480><c> from</c>

00:09:58.630 --> 00:09:58.640 align:start position:0%
instead of you sending the payload from
 

00:09:58.640 --> 00:10:00.389 align:start position:0%
instead of you sending the payload from
your<00:09:58.839><c> computer</c><00:09:59.560><c> and</c><00:09:59.760><c> that's</c><00:09:59.920><c> what</c><00:10:00.079><c> I'll</c><00:10:00.240><c> be</c>

00:10:00.389 --> 00:10:00.399 align:start position:0%
your computer and that's what I'll be
 

00:10:00.399 --> 00:10:03.959 align:start position:0%
your computer and that's what I'll be
going<00:10:00.600><c> over</c><00:10:00.959><c> next</c>

