WEBVTT
Kind: captions
Language: en

00:00:01.839 --> 00:00:03.429 align:start position:0%
 
in<00:00:02.000><c> this</c><00:00:02.240><c> video</c><00:00:02.639><c> we</c><00:00:02.800><c> will</c><00:00:03.040><c> explore</c><00:00:03.360><c> the</c>

00:00:03.429 --> 00:00:03.439 align:start position:0%
in this video we will explore the
 

00:00:03.439 --> 00:00:07.990 align:start position:0%
in this video we will explore the
agroclimatic<00:00:04.319><c> indicators</c><00:00:04.880><c> a</c><00:00:04.960><c> little</c><00:00:05.279><c> further</c>

00:00:07.990 --> 00:00:08.000 align:start position:0%
 
 

00:00:08.000 --> 00:00:09.990 align:start position:0%
 
with<00:00:08.160><c> climate</c><00:00:08.639><c> change</c><00:00:08.960><c> we</c><00:00:09.120><c> often</c><00:00:09.519><c> consider</c>

00:00:09.990 --> 00:00:10.000 align:start position:0%
with climate change we often consider
 

00:00:10.000 --> 00:00:12.230 align:start position:0%
with climate change we often consider
the<00:00:10.160><c> difference</c><00:00:10.559><c> between</c><00:00:10.880><c> 30-year</c><00:00:11.519><c> periods</c>

00:00:12.230 --> 00:00:12.240 align:start position:0%
the difference between 30-year periods
 

00:00:12.240 --> 00:00:14.150 align:start position:0%
the difference between 30-year periods
to<00:00:12.400><c> see</c><00:00:12.639><c> how</c><00:00:12.799><c> our</c><00:00:12.960><c> indicators</c><00:00:13.519><c> are</c><00:00:13.679><c> expected</c>

00:00:14.150 --> 00:00:14.160 align:start position:0%
to see how our indicators are expected
 

00:00:14.160 --> 00:00:17.269 align:start position:0%
to see how our indicators are expected
to<00:00:14.320><c> change</c><00:00:14.639><c> in</c><00:00:14.799><c> the</c><00:00:14.920><c> future</c>

00:00:17.269 --> 00:00:17.279 align:start position:0%
to change in the future
 

00:00:17.279 --> 00:00:19.189 align:start position:0%
to change in the future
in<00:00:17.359><c> this</c><00:00:17.600><c> video</c><00:00:18.320><c> we</c><00:00:18.560><c> are</c><00:00:18.640><c> making</c><00:00:19.039><c> an</c>

00:00:19.189 --> 00:00:19.199 align:start position:0%
in this video we are making an
 

00:00:19.199 --> 00:00:21.269 align:start position:0%
in this video we are making an
application<00:00:20.240><c> where</c><00:00:20.400><c> we</c><00:00:20.560><c> can</c><00:00:20.800><c> explore</c><00:00:21.119><c> the</c>

00:00:21.269 --> 00:00:21.279 align:start position:0%
application where we can explore the
 

00:00:21.279 --> 00:00:23.029 align:start position:0%
application where we can explore the
30-year<00:00:21.840><c> average</c><00:00:22.240><c> data</c>

00:00:23.029 --> 00:00:23.039 align:start position:0%
30-year average data
 

00:00:23.039 --> 00:00:26.470 align:start position:0%
30-year average data
for<00:00:23.279><c> different</c><00:00:23.680><c> world</c><00:00:24.000><c> regions</c><00:00:25.920><c> i</c><00:00:26.080><c> will</c><00:00:26.240><c> go</c>

00:00:26.470 --> 00:00:26.480 align:start position:0%
for different world regions i will go
 

00:00:26.480 --> 00:00:28.950 align:start position:0%
for different world regions i will go
through<00:00:26.720><c> a</c><00:00:26.800><c> simple</c><00:00:27.119><c> starter</c><00:00:27.599><c> script</c>

00:00:28.950 --> 00:00:28.960 align:start position:0%
through a simple starter script
 

00:00:28.960 --> 00:00:31.990 align:start position:0%
through a simple starter script
the<00:00:29.119><c> script</c><00:00:29.439><c> starts</c><00:00:29.760><c> with</c><00:00:29.920><c> two</c><00:00:30.160><c> dictionaries</c>

00:00:31.990 --> 00:00:32.000 align:start position:0%
the script starts with two dictionaries
 

00:00:32.000 --> 00:00:35.030 align:start position:0%
the script starts with two dictionaries
one<00:00:32.239><c> for</c><00:00:32.480><c> the</c><00:00:32.559><c> different</c><00:00:32.960><c> regions</c><00:00:34.640><c> and</c><00:00:34.800><c> one</c>

00:00:35.030 --> 00:00:35.040 align:start position:0%
one for the different regions and one
 

00:00:35.040 --> 00:00:37.670 align:start position:0%
one for the different regions and one
for<00:00:35.280><c> the</c><00:00:35.360><c> rcps</c>

00:00:37.670 --> 00:00:37.680 align:start position:0%
for the rcps
 

00:00:37.680 --> 00:00:40.869 align:start position:0%
for the rcps
these<00:00:38.000><c> are</c><00:00:38.160><c> loaded</c><00:00:38.640><c> in</c><00:00:38.719><c> the</c><00:00:38.879><c> drop</c><00:00:39.120><c> down</c><00:00:39.440><c> menus</c>

00:00:40.869 --> 00:00:40.879 align:start position:0%
these are loaded in the drop down menus
 

00:00:40.879 --> 00:00:44.630 align:start position:0%
these are loaded in the drop down menus
we<00:00:41.120><c> again</c><00:00:41.520><c> see</c><00:00:41.760><c> an</c><00:00:41.920><c> output</c><00:00:42.840><c> figure</c>

00:00:44.630 --> 00:00:44.640 align:start position:0%
we again see an output figure
 

00:00:44.640 --> 00:00:46.950 align:start position:0%
we again see an output figure
and<00:00:44.800><c> our</c><00:00:44.960><c> main</c><00:00:45.280><c> application</c><00:00:46.320><c> where</c><00:00:46.559><c> we</c><00:00:46.719><c> call</c>

00:00:46.950 --> 00:00:46.960 align:start position:0%
and our main application where we call
 

00:00:46.960 --> 00:00:47.830 align:start position:0%
and our main application where we call
our<00:00:47.200><c> rcp</c>

00:00:47.830 --> 00:00:47.840 align:start position:0%
our rcp
 

00:00:47.840 --> 00:00:51.270 align:start position:0%
our rcp
and<00:00:48.079><c> our</c><00:00:48.239><c> region</c>

00:00:51.270 --> 00:00:51.280 align:start position:0%
 
 

00:00:51.280 --> 00:00:54.549 align:start position:0%
 
we<00:00:51.520><c> are</c><00:00:51.600><c> retrieving</c><00:00:52.480><c> two</c><00:00:52.719><c> sets</c><00:00:53.039><c> of</c><00:00:53.199><c> data</c>

00:00:54.549 --> 00:00:54.559 align:start position:0%
we are retrieving two sets of data
 

00:00:54.559 --> 00:00:56.470 align:start position:0%
we are retrieving two sets of data
for<00:00:54.800><c> two</c><00:00:54.960><c> different</c><00:00:55.280><c> climate</c><00:00:55.760><c> models</c><00:00:56.160><c> because</c>

00:00:56.470 --> 00:00:56.480 align:start position:0%
for two different climate models because
 

00:00:56.480 --> 00:00:57.990 align:start position:0%
for two different climate models because
i<00:00:56.559><c> want</c><00:00:56.800><c> to</c><00:00:56.879><c> see</c><00:00:57.039><c> the</c><00:00:57.199><c> difference</c><00:00:57.600><c> between</c>

00:00:57.990 --> 00:00:58.000 align:start position:0%
i want to see the difference between
 

00:00:58.000 --> 00:01:01.110 align:start position:0%
i want to see the difference between
the<00:00:58.160><c> two</c><00:00:58.320><c> models</c>

00:01:01.110 --> 00:01:01.120 align:start position:0%
 
 

00:01:01.120 --> 00:01:03.270 align:start position:0%
 
then<00:01:01.359><c> i</c><00:01:01.520><c> use</c><00:01:01.840><c> cube</c><00:01:02.239><c> average</c><00:01:02.879><c> over</c><00:01:03.120><c> the</c>

00:01:03.270 --> 00:01:03.280 align:start position:0%
then i use cube average over the
 

00:01:03.280 --> 00:01:05.429 align:start position:0%
then i use cube average over the
dimension<00:01:04.000><c> time</c>

00:01:05.429 --> 00:01:05.439 align:start position:0%
dimension time
 

00:01:05.439 --> 00:01:08.950 align:start position:0%
dimension time
to<00:01:05.680><c> calculate</c><00:01:06.479><c> the</c><00:01:06.640><c> average</c><00:01:07.200><c> over</c><00:01:07.439><c> 30</c><00:01:07.840><c> years</c>

00:01:08.950 --> 00:01:08.960 align:start position:0%
to calculate the average over 30 years
 

00:01:08.960 --> 00:01:10.870 align:start position:0%
to calculate the average over 30 years
it<00:01:09.119><c> is</c><00:01:09.280><c> automatically</c><00:01:09.920><c> averaged</c><00:01:10.560><c> over</c><00:01:10.799><c> a</c><00:01:10.880><c> 30</c>

00:01:10.870 --> 00:01:10.880 align:start position:0%
it is automatically averaged over a 30
 

00:01:10.880 --> 00:01:12.310 align:start position:0%
it is automatically averaged over a 30
30-year<00:01:11.360><c> period</c>

00:01:12.310 --> 00:01:12.320 align:start position:0%
30-year period
 

00:01:12.320 --> 00:01:14.710 align:start position:0%
30-year period
because<00:01:12.880><c> i</c><00:01:13.119><c> retrieved</c><00:01:13.600><c> the</c><00:01:13.680><c> data</c><00:01:14.159><c> for</c><00:01:14.400><c> 30</c>

00:01:14.710 --> 00:01:14.720 align:start position:0%
because i retrieved the data for 30
 

00:01:14.720 --> 00:01:17.030 align:start position:0%
because i retrieved the data for 30
years

00:01:17.030 --> 00:01:17.040 align:start position:0%
years
 

00:01:17.040 --> 00:01:18.390 align:start position:0%
years
then<00:01:17.280><c> because</c><00:01:17.680><c> i</c><00:01:17.759><c> want</c><00:01:17.920><c> to</c><00:01:18.080><c> see</c><00:01:18.240><c> the</c>

00:01:18.390 --> 00:01:18.400 align:start position:0%
then because i want to see the
 

00:01:18.400 --> 00:01:21.109 align:start position:0%
then because i want to see the
difference<00:01:18.880><c> between</c><00:01:19.200><c> these</c><00:01:19.439><c> two</c><00:01:19.680><c> data</c><00:01:20.080><c> sets</c>

00:01:21.109 --> 00:01:21.119 align:start position:0%
difference between these two data sets
 

00:01:21.119 --> 00:01:23.109 align:start position:0%
difference between these two data sets
i<00:01:21.280><c> calculate</c><00:01:21.920><c> the</c><00:01:22.080><c> difference</c><00:01:22.479><c> with</c><00:01:22.720><c> a</c><00:01:22.799><c> simple</c>

00:01:23.109 --> 00:01:23.119 align:start position:0%
i calculate the difference with a simple
 

00:01:23.119 --> 00:01:26.630 align:start position:0%
i calculate the difference with a simple
minus<00:01:24.840><c> operator</c>

00:01:26.630 --> 00:01:26.640 align:start position:0%
minus operator
 

00:01:26.640 --> 00:01:28.950 align:start position:0%
minus operator
then<00:01:26.880><c> to</c><00:01:27.040><c> plot</c><00:01:27.360><c> these</c><00:01:27.600><c> data</c><00:01:28.479><c> i</c><00:01:28.720><c> add</c><00:01:28.880><c> a</c>

00:01:28.950 --> 00:01:28.960 align:start position:0%
then to plot these data i add a
 

00:01:28.960 --> 00:01:31.350 align:start position:0%
then to plot these data i add a
projection

00:01:31.350 --> 00:01:31.360 align:start position:0%
projection
 

00:01:31.360 --> 00:01:34.710 align:start position:0%
projection
which<00:01:31.600><c> is</c><00:01:31.840><c> called</c><00:01:32.159><c> in</c><00:01:32.240><c> the</c><00:01:32.400><c> figure</c><00:01:34.079><c> i</c><00:01:34.240><c> am</c><00:01:34.400><c> using</c>

00:01:34.710 --> 00:01:34.720 align:start position:0%
which is called in the figure i am using
 

00:01:34.720 --> 00:01:35.749 align:start position:0%
which is called in the figure i am using
one<00:01:35.040><c> figure</c>

00:01:35.749 --> 00:01:35.759 align:start position:0%
one figure
 

00:01:35.759 --> 00:01:39.109 align:start position:0%
one figure
to<00:01:35.920><c> plot</c><00:01:36.320><c> three</c><00:01:36.560><c> subfigures</c><00:01:38.479><c> apart</c><00:01:38.799><c> from</c><00:01:38.960><c> the</c>

00:01:39.109 --> 00:01:39.119 align:start position:0%
to plot three subfigures apart from the
 

00:01:39.119 --> 00:01:41.030 align:start position:0%
to plot three subfigures apart from the
projection<00:01:39.840><c> i'm</c><00:01:40.079><c> also</c><00:01:40.320><c> loading</c><00:01:40.640><c> the</c><00:01:40.799><c> region</c>

00:01:41.030 --> 00:01:41.040 align:start position:0%
projection i'm also loading the region
 

00:01:41.040 --> 00:01:43.590 align:start position:0%
projection i'm also loading the region
that<00:01:41.200><c> was</c><00:01:41.439><c> selected</c><00:01:41.840><c> in</c><00:01:42.000><c> the</c><00:01:42.079><c> drop</c><00:01:42.320><c> down</c><00:01:42.560><c> menu</c>

00:01:43.590 --> 00:01:43.600 align:start position:0%
that was selected in the drop down menu
 

00:01:43.600 --> 00:01:46.550 align:start position:0%
that was selected in the drop down menu
and<00:01:43.759><c> i'm</c><00:01:44.000><c> selecting</c><00:01:44.560><c> a</c><00:01:44.720><c> figure</c><00:01:45.119><c> size</c><00:01:46.320><c> this</c>

00:01:46.550 --> 00:01:46.560 align:start position:0%
and i'm selecting a figure size this
 

00:01:46.560 --> 00:01:48.710 align:start position:0%
and i'm selecting a figure size this
figure<00:01:46.960><c> size</c><00:01:47.280><c> will</c><00:01:47.439><c> not</c><00:01:47.600><c> matter</c><00:01:48.000><c> if</c><00:01:48.159><c> we</c><00:01:48.320><c> run</c><00:01:48.560><c> in</c>

00:01:48.710 --> 00:01:48.720 align:start position:0%
figure size will not matter if we run in
 

00:01:48.720 --> 00:01:51.910 align:start position:0%
figure size will not matter if we run in
line

00:01:51.910 --> 00:01:51.920 align:start position:0%
 
 

00:01:51.920 --> 00:01:54.389 align:start position:0%
 
but<00:01:52.159><c> if</c><00:01:52.320><c> you</c><00:01:52.479><c> open</c><00:01:52.720><c> the</c><00:01:52.880><c> application</c><00:01:53.920><c> in</c><00:01:54.079><c> a</c><00:01:54.159><c> new</c>

00:01:54.389 --> 00:01:54.399 align:start position:0%
but if you open the application in a new
 

00:01:54.399 --> 00:01:55.270 align:start position:0%
but if you open the application in a new
window

00:01:55.270 --> 00:01:55.280 align:start position:0%
window
 

00:01:55.280 --> 00:01:56.789 align:start position:0%
window
you<00:01:55.520><c> will</c><00:01:55.680><c> see</c><00:01:55.920><c> that</c><00:01:56.159><c> it</c><00:01:56.240><c> makes</c><00:01:56.479><c> a</c><00:01:56.560><c> big</c>

00:01:56.789 --> 00:01:56.799 align:start position:0%
you will see that it makes a big
 

00:01:56.799 --> 00:02:00.469 align:start position:0%
you will see that it makes a big
difference<00:01:57.280><c> what</c><00:01:57.520><c> figure</c><00:01:57.920><c> size</c><00:01:58.240><c> you</c><00:01:58.399><c> select</c>

00:02:00.469 --> 00:02:00.479 align:start position:0%
difference what figure size you select
 

00:02:00.479 --> 00:02:03.030 align:start position:0%
difference what figure size you select
by<00:02:00.719><c> selecting</c><00:02:01.200><c> the</c><00:02:01.360><c> subplots</c><00:02:02.159><c> i'm</c><00:02:02.479><c> ensuring</c>

00:02:03.030 --> 00:02:03.040 align:start position:0%
by selecting the subplots i'm ensuring
 

00:02:03.040 --> 00:02:05.510 align:start position:0%
by selecting the subplots i'm ensuring
that<00:02:03.360><c> all</c><00:02:03.520><c> my</c><00:02:03.759><c> three</c><00:02:04.079><c> figures</c>

00:02:05.510 --> 00:02:05.520 align:start position:0%
that all my three figures
 

00:02:05.520 --> 00:02:07.270 align:start position:0%
that all my three figures
use<00:02:05.759><c> the</c><00:02:05.920><c> same</c><00:02:06.159><c> projection</c><00:02:06.799><c> on</c><00:02:06.880><c> the</c><00:02:06.960><c> same</c>

00:02:07.270 --> 00:02:07.280 align:start position:0%
use the same projection on the same
 

00:02:07.280 --> 00:02:11.430 align:start position:0%
use the same projection on the same
extent<00:02:07.840><c> they</c><00:02:08.000><c> have</c><00:02:08.239><c> exactly</c><00:02:08.640><c> the</c><00:02:08.800><c> same</c><00:02:09.039><c> layout</c>

00:02:11.430 --> 00:02:11.440 align:start position:0%
extent they have exactly the same layout
 

00:02:11.440 --> 00:02:13.270 align:start position:0%
extent they have exactly the same layout
so<00:02:11.599><c> we</c><00:02:11.760><c> can</c><00:02:11.920><c> see</c><00:02:12.080><c> that</c><00:02:12.319><c> there</c><00:02:12.480><c> are</c><00:02:12.640><c> some</c><00:02:12.959><c> issues</c>

00:02:13.270 --> 00:02:13.280 align:start position:0%
so we can see that there are some issues
 

00:02:13.280 --> 00:02:15.589 align:start position:0%
so we can see that there are some issues
with<00:02:13.440><c> the</c><00:02:13.599><c> plot</c><00:02:13.840><c> i've</c><00:02:14.080><c> created</c>

00:02:15.589 --> 00:02:15.599 align:start position:0%
with the plot i've created
 

00:02:15.599 --> 00:02:17.350 align:start position:0%
with the plot i've created
for<00:02:15.760><c> one</c><00:02:16.000><c> the</c><00:02:16.160><c> scale</c><00:02:16.560><c> on</c><00:02:16.640><c> the</c><00:02:16.720><c> color</c><00:02:17.040><c> bar</c><00:02:17.280><c> of</c>

00:02:17.350 --> 00:02:17.360 align:start position:0%
for one the scale on the color bar of
 

00:02:17.360 --> 00:02:18.630 align:start position:0%
for one the scale on the color bar of
these<00:02:17.599><c> plots</c>

00:02:18.630 --> 00:02:18.640 align:start position:0%
these plots
 

00:02:18.640 --> 00:02:20.070 align:start position:0%
these plots
makes<00:02:18.879><c> it</c><00:02:19.040><c> hard</c><00:02:19.280><c> to</c><00:02:19.360><c> see</c><00:02:19.520><c> the</c><00:02:19.680><c> difference</c>

00:02:20.070 --> 00:02:20.080 align:start position:0%
makes it hard to see the difference
 

00:02:20.080 --> 00:02:22.309 align:start position:0%
makes it hard to see the difference
between<00:02:20.480><c> the</c><00:02:20.560><c> two</c><00:02:20.840><c> models</c>

00:02:22.309 --> 00:02:22.319 align:start position:0%
between the two models
 

00:02:22.319 --> 00:02:23.910 align:start position:0%
between the two models
the<00:02:22.480><c> script</c><00:02:22.959><c> takes</c><00:02:23.200><c> the</c><00:02:23.360><c> minimum</c><00:02:23.760><c> and</c><00:02:23.840><c> the</c>

00:02:23.910 --> 00:02:23.920 align:start position:0%
the script takes the minimum and the
 

00:02:23.920 --> 00:02:26.150 align:start position:0%
the script takes the minimum and the
maximum<00:02:24.400><c> value</c><00:02:24.800><c> of</c><00:02:24.959><c> the</c><00:02:25.120><c> data</c><00:02:25.520><c> to</c><00:02:25.680><c> create</c><00:02:25.920><c> this</c>

00:02:26.150 --> 00:02:26.160 align:start position:0%
maximum value of the data to create this
 

00:02:26.160 --> 00:02:27.350 align:start position:0%
maximum value of the data to create this
color<00:02:26.480><c> bar</c>

00:02:27.350 --> 00:02:27.360 align:start position:0%
color bar
 

00:02:27.360 --> 00:02:29.270 align:start position:0%
color bar
so<00:02:27.599><c> if</c><00:02:27.760><c> we</c><00:02:27.920><c> focus</c><00:02:28.239><c> on</c><00:02:28.319><c> europe</c><00:02:28.800><c> it</c><00:02:28.879><c> does</c><00:02:29.040><c> not</c>

00:02:29.270 --> 00:02:29.280 align:start position:0%
so if we focus on europe it does not
 

00:02:29.280 --> 00:02:30.710 align:start position:0%
so if we focus on europe it does not
show<00:02:29.520><c> the</c><00:02:29.599><c> detail</c><00:02:30.000><c> that</c><00:02:30.160><c> we</c><00:02:30.319><c> want</c>

00:02:30.710 --> 00:02:30.720 align:start position:0%
show the detail that we want
 

00:02:30.720 --> 00:02:32.710 align:start position:0%
show the detail that we want
it<00:02:30.879><c> can</c><00:02:31.040><c> also</c><00:02:31.200><c> be</c><00:02:31.360><c> that</c><00:02:31.599><c> the</c><00:02:31.680><c> color</c><00:02:32.000><c> scale</c><00:02:32.560><c> of</c>

00:02:32.710 --> 00:02:32.720 align:start position:0%
it can also be that the color scale of
 

00:02:32.720 --> 00:02:34.949 align:start position:0%
it can also be that the color scale of
the<00:02:33.120><c> first</c><00:02:33.519><c> and</c><00:02:33.599><c> the</c><00:02:33.760><c> second</c><00:02:34.160><c> plot</c><00:02:34.560><c> are</c><00:02:34.720><c> not</c>

00:02:34.949 --> 00:02:34.959 align:start position:0%
the first and the second plot are not
 

00:02:34.959 --> 00:02:37.350 align:start position:0%
the first and the second plot are not
exactly<00:02:35.360><c> the</c><00:02:35.519><c> same</c>

00:02:37.350 --> 00:02:37.360 align:start position:0%
exactly the same
 

00:02:37.360 --> 00:02:39.350 align:start position:0%
exactly the same
another<00:02:37.760><c> issue</c><00:02:38.239><c> is</c><00:02:38.400><c> that</c><00:02:38.560><c> the</c><00:02:38.640><c> plot</c><00:02:38.959><c> takes</c><00:02:39.200><c> the</c>

00:02:39.350 --> 00:02:39.360 align:start position:0%
another issue is that the plot takes the
 

00:02:39.360 --> 00:02:40.630 align:start position:0%
another issue is that the plot takes the
variable<00:02:39.920><c> long</c><00:02:40.160><c> name</c>

00:02:40.630 --> 00:02:40.640 align:start position:0%
variable long name
 

00:02:40.640 --> 00:02:43.270 align:start position:0%
variable long name
as<00:02:40.879><c> the</c><00:02:40.959><c> caption</c><00:02:41.440><c> of</c><00:02:41.519><c> the</c><00:02:41.680><c> color</c><00:02:42.000><c> bar</c><00:02:42.959><c> as</c><00:02:43.200><c> you</c>

00:02:43.270 --> 00:02:43.280 align:start position:0%
as the caption of the color bar as you
 

00:02:43.280 --> 00:02:45.110 align:start position:0%
as the caption of the color bar as you
can<00:02:43.440><c> see</c><00:02:43.680><c> that</c><00:02:43.920><c> is</c><00:02:44.080><c> not</c><00:02:44.239><c> very</c><00:02:44.560><c> suitable</c><00:02:45.040><c> in</c>

00:02:45.110 --> 00:02:45.120 align:start position:0%
can see that is not very suitable in
 

00:02:45.120 --> 00:02:47.190 align:start position:0%
can see that is not very suitable in
this<00:02:45.360><c> case</c>

00:02:47.190 --> 00:02:47.200 align:start position:0%
this case
 

00:02:47.200 --> 00:02:48.949 align:start position:0%
this case
we<00:02:47.360><c> can</c><00:02:47.519><c> change</c><00:02:47.840><c> all</c><00:02:48.000><c> this</c><00:02:48.319><c> with</c><00:02:48.560><c> the</c><00:02:48.720><c> peak</c>

00:02:48.949 --> 00:02:48.959 align:start position:0%
we can change all this with the peak
 

00:02:48.959 --> 00:02:54.150 align:start position:0%
we can change all this with the peak
color<00:02:49.280><c> mesh</c><00:02:49.599><c> quarks</c>

00:02:54.150 --> 00:02:54.160 align:start position:0%
 
 

00:02:54.160 --> 00:02:56.630 align:start position:0%
 
here<00:02:54.400><c> we</c><00:02:54.560><c> can</c><00:02:54.800><c> add</c><00:02:54.959><c> the</c><00:02:55.120><c> color</c><00:02:55.440><c> map</c><00:02:56.080><c> a</c><00:02:56.160><c> minimum</c>

00:02:56.630 --> 00:02:56.640 align:start position:0%
here we can add the color map a minimum
 

00:02:56.640 --> 00:02:57.990 align:start position:0%
here we can add the color map a minimum
and<00:02:56.800><c> a</c><00:02:56.879><c> maximum</c>

00:02:57.990 --> 00:02:58.000 align:start position:0%
and a maximum
 

00:02:58.000 --> 00:03:05.750 align:start position:0%
and a maximum
and<00:02:58.239><c> a</c><00:03:02.840><c> label</c>

00:03:05.750 --> 00:03:05.760 align:start position:0%
and a label
 

00:03:05.760 --> 00:03:07.589 align:start position:0%
and a label
you<00:03:05.920><c> can</c><00:03:06.080><c> now</c><00:03:06.319><c> see</c><00:03:06.560><c> that</c><00:03:06.720><c> the</c><00:03:06.879><c> plots</c><00:03:07.200><c> are</c><00:03:07.360><c> much</c>

00:03:07.589 --> 00:03:07.599 align:start position:0%
you can now see that the plots are much
 

00:03:07.599 --> 00:03:10.149 align:start position:0%
you can now see that the plots are much
clearer<00:03:08.239><c> so</c><00:03:08.400><c> in</c><00:03:08.480><c> this</c><00:03:08.720><c> example</c><00:03:09.280><c> i</c><00:03:09.440><c> have</c><00:03:09.599><c> chosen</c>

00:03:10.149 --> 00:03:10.159 align:start position:0%
clearer so in this example i have chosen
 

00:03:10.159 --> 00:03:13.110 align:start position:0%
clearer so in this example i have chosen
50<00:03:10.800><c> as</c><00:03:10.959><c> a</c><00:03:11.040><c> maximum</c><00:03:11.599><c> value</c><00:03:12.560><c> but</c><00:03:12.800><c> you</c><00:03:12.879><c> can</c>

00:03:13.110 --> 00:03:13.120 align:start position:0%
50 as a maximum value but you can
 

00:03:13.120 --> 00:03:13.910 align:start position:0%
50 as a maximum value but you can
imagine

00:03:13.910 --> 00:03:13.920 align:start position:0%
imagine
 

00:03:13.920 --> 00:03:16.470 align:start position:0%
imagine
that<00:03:14.080><c> this</c><00:03:14.319><c> maximum</c><00:03:14.879><c> is</c><00:03:15.040><c> too</c><00:03:15.280><c> low</c><00:03:15.920><c> if</c><00:03:16.080><c> we</c><00:03:16.319><c> are</c>

00:03:16.470 --> 00:03:16.480 align:start position:0%
that this maximum is too low if we are
 

00:03:16.480 --> 00:03:17.990 align:start position:0%
that this maximum is too low if we are
counting<00:03:17.040><c> consecutive</c><00:03:17.680><c> dry</c>

00:03:17.990 --> 00:03:18.000 align:start position:0%
counting consecutive dry
 

00:03:18.000 --> 00:03:21.190 align:start position:0%
counting consecutive dry
days<00:03:18.640><c> in</c><00:03:18.800><c> africa</c><00:03:19.599><c> the</c><00:03:19.760><c> static</c><00:03:20.239><c> maximum</c>

00:03:21.190 --> 00:03:21.200 align:start position:0%
days in africa the static maximum
 

00:03:21.200 --> 00:03:23.350 align:start position:0%
days in africa the static maximum
might<00:03:21.440><c> not</c><00:03:21.599><c> be</c><00:03:21.760><c> very</c><00:03:22.000><c> useful</c><00:03:22.720><c> so</c><00:03:22.879><c> i</c><00:03:23.040><c> want</c><00:03:23.200><c> to</c>

00:03:23.350 --> 00:03:23.360 align:start position:0%
might not be very useful so i want to
 

00:03:23.360 --> 00:03:25.350 align:start position:0%
might not be very useful so i want to
add<00:03:23.680><c> an</c><00:03:23.840><c> option</c><00:03:24.239><c> to</c><00:03:24.400><c> change</c><00:03:24.720><c> the</c><00:03:24.879><c> extent</c><00:03:25.280><c> of</c>

00:03:25.350 --> 00:03:25.360 align:start position:0%
add an option to change the extent of
 

00:03:25.360 --> 00:03:27.350 align:start position:0%
add an option to change the extent of
the<00:03:25.519><c> map</c>

00:03:27.350 --> 00:03:27.360 align:start position:0%
the map
 

00:03:27.360 --> 00:03:38.309 align:start position:0%
the map
i<00:03:27.519><c> do</c><00:03:27.760><c> this</c><00:03:28.000><c> by</c><00:03:28.239><c> adding</c><00:03:28.560><c> an</c><00:03:30.840><c> input</c>

00:03:38.309 --> 00:03:38.319 align:start position:0%
 
 

00:03:38.319 --> 00:03:42.869 align:start position:0%
 
i<00:03:38.480><c> need</c><00:03:38.640><c> to</c><00:03:38.879><c> add</c><00:03:39.440><c> the</c><00:03:39.599><c> maximum</c><00:03:40.720><c> this</c>

00:03:42.869 --> 00:03:42.879 align:start position:0%
i need to add the maximum this
 

00:03:42.879 --> 00:03:47.830 align:start position:0%
i need to add the maximum this
here<00:03:45.440><c> and</c><00:03:45.599><c> then</c><00:03:45.920><c> i</c><00:03:46.080><c> can</c><00:03:46.319><c> use</c><00:03:46.640><c> it</c>

00:03:47.830 --> 00:03:47.840 align:start position:0%
here and then i can use it
 

00:03:47.840 --> 00:03:56.149 align:start position:0%
here and then i can use it
for<00:03:48.080><c> vmax</c>

00:03:56.149 --> 00:03:56.159 align:start position:0%
 
 

00:03:56.159 --> 00:03:58.550 align:start position:0%
 
so<00:03:56.400><c> we</c><00:03:56.560><c> can</c><00:03:56.720><c> now</c><00:03:56.959><c> change</c><00:03:57.280><c> the</c><00:03:57.439><c> extent</c><00:03:58.239><c> over</c>

00:03:58.550 --> 00:03:58.560 align:start position:0%
so we can now change the extent over
 

00:03:58.560 --> 00:03:59.670 align:start position:0%
so we can now change the extent over
here

00:03:59.670 --> 00:03:59.680 align:start position:0%
here
 

00:03:59.680 --> 00:04:01.190 align:start position:0%
here
we<00:03:59.840><c> can</c><00:04:00.080><c> also</c><00:04:00.319><c> play</c><00:04:00.560><c> around</c><00:04:00.879><c> with</c><00:04:01.120><c> the</c>

00:04:01.190 --> 00:04:01.200 align:start position:0%
we can also play around with the
 

00:04:01.200 --> 00:04:03.190 align:start position:0%
we can also play around with the
projection

00:04:03.190 --> 00:04:03.200 align:start position:0%
projection
 

00:04:03.200 --> 00:04:13.030 align:start position:0%
projection
we<00:04:03.360><c> can</c><00:04:03.519><c> choose</c><00:04:03.840><c> a</c><00:04:03.920><c> different</c><00:04:04.319><c> one</c>

00:04:13.030 --> 00:04:13.040 align:start position:0%
 
 

00:04:13.040 --> 00:04:14.949 align:start position:0%
 
and<00:04:13.200><c> you</c><00:04:13.360><c> can</c><00:04:13.519><c> see</c><00:04:13.760><c> the</c><00:04:13.920><c> plots</c><00:04:14.319><c> look</c><00:04:14.480><c> slightly</c>

00:04:14.949 --> 00:04:14.959 align:start position:0%
and you can see the plots look slightly
 

00:04:14.959 --> 00:04:16.229 align:start position:0%
and you can see the plots look slightly
different

00:04:16.229 --> 00:04:16.239 align:start position:0%
different
 

00:04:16.239 --> 00:04:19.430 align:start position:0%
different
we<00:04:16.400><c> can</c><00:04:16.639><c> even</c><00:04:17.040><c> tweak</c><00:04:18.239><c> which</c><00:04:18.479><c> coordinates</c>

00:04:19.430 --> 00:04:19.440 align:start position:0%
we can even tweak which coordinates
 

00:04:19.440 --> 00:04:23.590 align:start position:0%
we can even tweak which coordinates
the<00:04:19.600><c> projection</c><00:04:20.160><c> is</c><00:04:20.320><c> centered</c><00:04:20.720><c> around</c>

00:04:23.590 --> 00:04:23.600 align:start position:0%
 
 

00:04:23.600 --> 00:04:29.430 align:start position:0%
 
by<00:04:23.840><c> adding</c><00:04:24.240><c> the</c><00:04:24.479><c> coordinates</c><00:04:25.440><c> per</c><00:04:26.840><c> region</c>

00:04:29.430 --> 00:04:29.440 align:start position:0%
by adding the coordinates per region
 

00:04:29.440 --> 00:04:43.430 align:start position:0%
by adding the coordinates per region
and<00:04:29.680><c> adding</c><00:04:30.080><c> them</c><00:04:32.000><c> into</c><00:04:32.240><c> the</c><00:04:40.840><c> projection</c>

00:04:43.430 --> 00:04:43.440 align:start position:0%
and adding them into the projection
 

00:04:43.440 --> 00:04:46.790 align:start position:0%
and adding them into the projection
the<00:04:44.440><c> cdsplot.geomap</c><00:04:45.520><c> function</c><00:04:46.160><c> is</c><00:04:46.320><c> only</c><00:04:46.560><c> one</c>

00:04:46.790 --> 00:04:46.800 align:start position:0%
the cdsplot.geomap function is only one
 

00:04:46.800 --> 00:04:48.230 align:start position:0%
the cdsplot.geomap function is only one
way<00:04:47.040><c> of</c><00:04:47.120><c> plotting</c><00:04:47.600><c> map</c>

00:04:48.230 --> 00:04:48.240 align:start position:0%
way of plotting map
 

00:04:48.240 --> 00:04:51.909 align:start position:0%
way of plotting map
another<00:04:48.720><c> option</c><00:04:49.280><c> is</c><00:04:49.440><c> the</c><00:04:49.600><c> map.plot</c><00:04:50.639><c> function</c>

00:04:51.909 --> 00:04:51.919 align:start position:0%
another option is the map.plot function
 

00:04:51.919 --> 00:04:54.830 align:start position:0%
another option is the map.plot function
i<00:04:52.080><c> will</c><00:04:52.240><c> add</c><00:04:52.479><c> this</c><00:04:52.800><c> one</c><00:04:53.120><c> in</c><00:04:53.280><c> a</c><00:04:53.360><c> different</c>

00:04:54.830 --> 00:04:54.840 align:start position:0%
i will add this one in a different
 

00:04:54.840 --> 00:05:04.629 align:start position:0%
i will add this one in a different
figure

00:05:04.629 --> 00:05:04.639 align:start position:0%
 
 

00:05:04.639 --> 00:05:07.430 align:start position:0%
 
as<00:05:04.880><c> you</c><00:05:05.039><c> can</c><00:05:05.280><c> see</c><00:05:06.160><c> this</c><00:05:06.320><c> type</c><00:05:06.560><c> of</c><00:05:06.800><c> plot</c><00:05:07.120><c> has</c><00:05:07.360><c> a</c>

00:05:07.430 --> 00:05:07.440 align:start position:0%
as you can see this type of plot has a
 

00:05:07.440 --> 00:05:09.510 align:start position:0%
as you can see this type of plot has a
different<00:05:07.759><c> way</c><00:05:08.000><c> of</c><00:05:08.080><c> defining</c><00:05:08.560><c> the</c><00:05:08.720><c> style</c>

00:05:09.510 --> 00:05:09.520 align:start position:0%
different way of defining the style
 

00:05:09.520 --> 00:05:12.310 align:start position:0%
different way of defining the style
so<00:05:09.759><c> in</c><00:05:09.840><c> this</c><00:05:10.080><c> specific</c><00:05:10.560><c> style</c><00:05:11.440><c> i'm</c><00:05:11.680><c> using</c><00:05:12.160><c> the</c>

00:05:12.310 --> 00:05:12.320 align:start position:0%
so in this specific style i'm using the
 

00:05:12.320 --> 00:05:13.909 align:start position:0%
so in this specific style i'm using the
contour<00:05:12.720><c> level</c><00:05:13.039><c> list</c>

00:05:13.909 --> 00:05:13.919 align:start position:0%
contour level list
 

00:05:13.919 --> 00:05:16.710 align:start position:0%
contour level list
with<00:05:14.080><c> the</c><00:05:14.240><c> numpy</c><00:05:14.800><c> arrange</c><00:05:15.199><c> function</c><00:05:16.240><c> to</c><00:05:16.479><c> use</c>

00:05:16.710 --> 00:05:16.720 align:start position:0%
with the numpy arrange function to use
 

00:05:16.720 --> 00:05:17.909 align:start position:0%
with the numpy arrange function to use
this<00:05:16.960><c> in</c><00:05:17.039><c> the</c><00:05:17.120><c> cds</c>

00:05:17.909 --> 00:05:17.919 align:start position:0%
this in the cds
 

00:05:17.919 --> 00:05:21.270 align:start position:0%
this in the cds
toolbox<00:05:18.800><c> i</c><00:05:18.960><c> have</c><00:05:19.199><c> to</c><00:05:19.840><c> import</c><00:05:20.479><c> the</c><00:05:20.720><c> numpy</c>

00:05:21.270 --> 00:05:21.280 align:start position:0%
toolbox i have to import the numpy
 

00:05:21.280 --> 00:05:22.469 align:start position:0%
toolbox i have to import the numpy
library

00:05:22.469 --> 00:05:22.479 align:start position:0%
library
 

00:05:22.479 --> 00:05:24.469 align:start position:0%
library
it<00:05:22.639><c> is</c><00:05:22.720><c> very</c><00:05:23.039><c> useful</c><00:05:23.440><c> that</c><00:05:23.600><c> we</c><00:05:23.759><c> can</c><00:05:24.000><c> use</c><00:05:24.240><c> some</c>

00:05:24.469 --> 00:05:24.479 align:start position:0%
it is very useful that we can use some
 

00:05:24.479 --> 00:05:26.310 align:start position:0%
it is very useful that we can use some
general<00:05:24.880><c> python</c><00:05:25.360><c> libraries</c>

00:05:26.310 --> 00:05:26.320 align:start position:0%
general python libraries
 

00:05:26.320 --> 00:05:35.110 align:start position:0%
general python libraries
in<00:05:26.479><c> the</c><00:05:26.560><c> cds</c><00:05:27.039><c> toolbox</c>

00:05:35.110 --> 00:05:35.120 align:start position:0%
 
 

00:05:35.120 --> 00:05:38.469 align:start position:0%
 
as<00:05:35.360><c> you</c><00:05:35.520><c> can</c><00:05:35.759><c> see</c><00:05:36.639><c> the</c><00:05:36.840><c> map.plot</c>

00:05:38.469 --> 00:05:38.479 align:start position:0%
as you can see the map.plot
 

00:05:38.479 --> 00:05:41.590 align:start position:0%
as you can see the map.plot
has<00:05:38.639><c> a</c><00:05:38.720><c> different</c><00:05:39.120><c> output</c><00:05:40.800><c> now</c><00:05:40.960><c> to</c><00:05:41.120><c> round</c><00:05:41.440><c> off</c>

00:05:41.590 --> 00:05:41.600 align:start position:0%
has a different output now to round off
 

00:05:41.600 --> 00:05:42.550 align:start position:0%
has a different output now to round off
this<00:05:41.759><c> application</c>

00:05:42.550 --> 00:05:42.560 align:start position:0%
this application
 

00:05:42.560 --> 00:05:46.070 align:start position:0%
this application
i<00:05:42.639><c> want</c><00:05:42.800><c> to</c><00:05:43.199><c> add</c><00:05:43.440><c> a</c><00:05:43.520><c> nice</c><00:05:43.840><c> caption</c>

00:05:46.070 --> 00:05:46.080 align:start position:0%
i want to add a nice caption
 

00:05:46.080 --> 00:05:52.150 align:start position:0%
i want to add a nice caption
for<00:05:46.240><c> this</c><00:05:46.560><c> i</c><00:05:46.720><c> add</c><00:05:46.880><c> a</c><00:05:50.840><c> markdown</c>

00:05:52.150 --> 00:05:52.160 align:start position:0%
for this i add a markdown
 

00:05:52.160 --> 00:05:55.350 align:start position:0%
for this i add a markdown
a<00:05:52.320><c> caption</c>

00:05:55.350 --> 00:05:55.360 align:start position:0%
 
 

00:05:55.360 --> 00:05:58.629 align:start position:0%
 
and<00:05:55.600><c> i</c><00:05:55.759><c> return</c><00:05:56.160><c> the</c><00:05:56.319><c> caption</c>

00:05:58.629 --> 00:05:58.639 align:start position:0%
and i return the caption
 

00:05:58.639 --> 00:06:00.230 align:start position:0%
and i return the caption
i<00:05:58.800><c> now</c><00:05:59.039><c> have</c><00:05:59.280><c> a</c><00:05:59.360><c> fully</c><00:05:59.680><c> functioning</c>

00:06:00.230 --> 00:06:00.240 align:start position:0%
i now have a fully functioning
 

00:06:00.240 --> 00:06:02.309 align:start position:0%
i now have a fully functioning
application<00:06:01.280><c> where</c><00:06:01.440><c> i</c><00:06:01.600><c> can</c><00:06:01.840><c> compare</c>

00:06:02.309 --> 00:06:02.319 align:start position:0%
application where i can compare
 

00:06:02.319 --> 00:06:04.390 align:start position:0%
application where i can compare
different<00:06:02.720><c> climate</c><00:06:03.120><c> model</c><00:06:03.520><c> outputs</c>

00:06:04.390 --> 00:06:04.400 align:start position:0%
different climate model outputs
 

00:06:04.400 --> 00:06:07.430 align:start position:0%
different climate model outputs
for<00:06:04.639><c> the</c><00:06:04.720><c> consecutive</c><00:06:05.360><c> dry</c><00:06:05.680><c> days</c><00:06:07.039><c> the</c><00:06:07.199><c> next</c>

00:06:07.430 --> 00:06:07.440 align:start position:0%
for the consecutive dry days the next
 

00:06:07.440 --> 00:06:07.909 align:start position:0%
for the consecutive dry days the next
step

00:06:07.909 --> 00:06:07.919 align:start position:0%
step
 

00:06:07.919 --> 00:06:10.230 align:start position:0%
step
is<00:06:08.080><c> to</c><00:06:08.240><c> work</c><00:06:08.560><c> with</c><00:06:08.720><c> multi-model</c><00:06:09.440><c> and</c><00:06:09.600><c> samples</c>

00:06:10.230 --> 00:06:10.240 align:start position:0%
is to work with multi-model and samples
 

00:06:10.240 --> 00:06:16.319 align:start position:0%
is to work with multi-model and samples
and<00:06:10.400><c> comparing</c><00:06:10.880><c> different</c><00:06:11.199><c> time</c><00:06:13.319><c> periods</c>

