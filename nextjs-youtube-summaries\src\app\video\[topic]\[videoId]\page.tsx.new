// filepath: c:\Users\<USER>\Dropbox\0 - Youtube Summaries\nextjs-youtube-summaries\src\app\video\[topic]\[videoId]\page.tsx
import { DatabaseService } from "@/lib/database";
import { 
    CopyIcon, 
    ExternalLinkIcon, 
    EyeIcon, 
    CalendarDaysIcon as Calendar, 
    ClockIcon as Clock,       
    PlayCircleIcon as Play, 
    MessageCircleIcon as MessageCircle 
} from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { formatDuration, formatViewCount, formatDate, truncateText, getEffectiveDate } from "@/lib/utils";
import { Metadata } from 'next';

interface VideoPageProps {
  params: { // No longer a Promise here, Next.js resolves it
    topic: string
    videoId: string
  }
}

// Helper to organize and render LLM response fields
const renderLlmResponse = (llmResponse: Record<string, unknown> | null) => {
  if (!llmResponse || Object.keys(llmResponse).length === 0) {
    return <p className="text-gray-500 dark:text-gray-400">No detailed summary information available.</p>;
  }

  // Define categories and their priority fields - customize based on your JSONB structure
  const categories: Record<string, string[]> = {
    "Insights": ['insights', 'key_insights', 'main_points', 'key_takeaways', 'key_points'],
    "Keywords": ['keywords', 'tags', 'topics', 'key_topics', 'subject_areas'],
    "Questions": ['questions', 'key_questions', 'questions_answered', 'faqs'],
    "Recommendations": ['recommendations', 'action_items', 'next_steps', 'action_points'],
    "Summary": ['summary', 'short_summary', 'tldr', 'overview'],
    "Analysis": ['analysis', 'conclusion', 'evaluation', 'perspective', 'sentiment', 'sentiment_analysis'],
    "Other": []
  };
  
  // Get all available keys
  const availableKeys = Object.keys(llmResponse);
  
  // Organize fields by category
  const organizedData: Record<string, Array<[string, unknown]>> = {};
  
  // Initialize categories
  Object.keys(categories).forEach(category => {
    organizedData[category] = [];
  });
  
  // Assign fields to their categories
  availableKeys.forEach(key => {
    // Skip these fields as they're redundant or not useful for display
    if (['original_transcript_summary', 'title', 'channel_name'].includes(key)) {
      return;
    }
    
    // Find which category this field belongs to
    let assigned = false;
    for (const [category, fields] of Object.entries(categories)) {
      if (fields.includes(key)) {
        organizedData[category].push([key, llmResponse[key]]);
        assigned = true;
        break;
      }
    }
    
    // If not assigned to any specific category, put in "Other"
    if (!assigned) {
      organizedData["Other"].push([key, llmResponse[key]]);
    }
  });
  
  // Customize display for specific fields
  const formatFieldValue = (key: string, value: unknown) => {
    if (value === null || value === undefined) return 'N/A';
    
    // Handle arrays nicely - especially for bullet points
    if (Array.isArray(value)) {
      return (
        <ul className="list-disc pl-5 space-y-1">
          {value.map((item, idx) => (
            <li key={idx} className="text-gray-800 dark:text-gray-200">
              {typeof item === 'object' ? JSON.stringify(item, null, 2) : String(item)}
            </li>
          ))}
        </ul>
      );
    }
    
    // Format objects (usually nested data)
    if (typeof value === 'object') {
      return (
        <pre className="bg-gray-50 dark:bg-gray-800 p-2 rounded-md overflow-auto text-xs">
          {JSON.stringify(value, null, 2)}
        </pre>
      );
    }
    
    // Format strings differently based on field type
    if (['insights', 'key_insights', 'main_points', 'key_takeaways', 'recommendations'].includes(key) && typeof value === 'string') {
      // If it looks like a bullet list but isn't properly formatted
      if (value.includes("- ") || value.includes("• ")) {
        const bulletPoints = value.split(/\n\s*[-•]\s+/);
        return (
          <ul className="list-disc pl-5 space-y-1">
            {bulletPoints.filter(Boolean).map((point, idx) => (
              <li key={idx} className="text-gray-800 dark:text-gray-200">{point.trim()}</li>
            ))}
          </ul>
        );
      }
    }
    
    // Add highlighting for important text
    if (['insights', 'key_insights', 'summary', 'tldr', 'key_takeaways', 'main_points', 'recommendations'].includes(key)) {
      return <div className="font-medium text-gray-900 dark:text-gray-100">{String(value)}</div>;
    }
    
    return <span className="text-gray-800 dark:text-gray-200">{String(value)}</span>;
  };
  
  // Format field names nicely
  const formatFieldName = (key: string) => {
    return key
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
  
  // Render each category that has fields
  return (
    <div className="space-y-6">
      {Object.entries(organizedData).map(([category, fields]) => {
        if (fields.length === 0) return null;
        
        return (
          <div key={category} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
            <div className="bg-gray-100 dark:bg-gray-800 px-4 py-2 border-b border-gray-200 dark:border-gray-700">
              <h3 className="font-medium text-gray-900 dark:text-gray-100">{category}</h3>
            </div>
            <dl className="divide-y divide-gray-200 dark:divide-gray-800">
              {fields.map(([key, value]) => (
                <div key={key} className="px-4 py-3 sm:grid sm:grid-cols-3 sm:gap-4">
                  <dt className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {formatFieldName(key)}
                  </dt>
                  <dd className="mt-1 text-sm sm:mt-0 sm:col-span-2 whitespace-pre-wrap">
                    {formatFieldValue(key, value)}
                  </dd>
                </div>
              ))}
            </dl>
          </div>
        );
      })}
    </div>
  );
};


export default async function VideoPage({ params }: VideoPageProps) {
  // In Next.js 14/15, we shouldn't need to await params, but the compiler is giving warnings
  // Using type assertion instead as a workaround
  const topic = String(params.topic);
  const videoId = String(params.videoId);
  const video = await DatabaseService.getVideoById(topic, videoId);

  if (!video) {
    notFound();
  }

  const youtubeEmbedUrl = video.video_id ? `https://www.youtube.com/embed/${video.video_id}` : null;

  return (
    <div className="max-w-6xl mx-auto p-4 sm:p-6 lg:p-8 space-y-8">
      {/* Video Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
          <Link href="/" className="hover:text-primary dark:hover:text-primary-light">Home</Link>
          <span>/</span>
          <span className="bg-muted text-muted-foreground px-2 py-1 rounded-full text-xs">
            {video.topic_category || 'Uncategorized'}
          </span>
        </div>
        
        <h1 className="text-3xl font-bold text-foreground leading-tight">
          {video.title}
        </h1>
        
        <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-muted-foreground">
          <span className="font-medium">{video.channel_name}</span>
          {video.view_count !== null && video.view_count !== undefined && (
            <div className="flex items-center gap-1">
              <EyeIcon className="w-4 h-4" />
              {formatViewCount(video.view_count)}
            </div>
          )}
          <div className="flex items-center gap-1">
            <Calendar className="w-4 h-4" />
            {formatDate(getEffectiveDate(video))}
          </div>
          {video.duration && (
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              {formatDuration(video.duration)}
            </div>
          )}
        </div>
      </div>

      {/* Video and AI Content - Reorganized Layout */}
      <div className="grid md:grid-cols-12 gap-8">
        {/* Left column - Video embed smaller */}
        <div className="md:col-span-4 space-y-4">
          {/* YouTube Embed - Made smaller */}
          {youtubeEmbedUrl ? (
            <div className="aspect-video overflow-hidden rounded-lg border border-border dark:border-gray-700 relative">
              <iframe
                width="100%"
                height="100%"
                src={youtubeEmbedUrl}
                title={video.title ?? "YouTube video player"}
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
                className="rounded-lg"
              ></iframe>
            </div>
          ) : (
             <div className="aspect-video overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 relative bg-muted flex items-center justify-center">
                <Play className="w-16 h-16 text-muted-foreground" />
                <p className="ml-2 text-muted-foreground">Video not available</p>
             </div>
          )}

          {/* Actions */}
          <div className="flex flex-wrap gap-3">
            {video.video_url && (
              <Link 
                href={video.video_url} 
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
              >
                <Play className="w-4 h-4 mr-2" />
                Watch on YouTube
              </Link>
            )}
            {video.channel_id && (
              <Link 
                href={`https://www.youtube.com/channel/${video.channel_id}`} 
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
              >
                <ExternalLinkIcon className="w-4 h-4 mr-2" />
                View Channel
              </Link>
            )}
          </div>
        </div>

        {/* Right column - AI Summary Details (larger) */}
        <div className="md:col-span-8 space-y-6">
          {/* AI Analysis - Enhanced */}
          <div>
            <h2 className="text-xl font-bold mb-4 flex items-center text-foreground">
              <MessageCircle className="w-5 h-5 mr-2" /> AI Analysis
            </h2>
            <div className="bg-card p-5 rounded-lg shadow-md border border-border">
              {/* Copy buttons at the top for convenience */}
              {video.llm_response && (
                <div className="mb-5 flex flex-wrap gap-2">
                  {typeof video.llm_response.insights === 'object' && Array.isArray(video.llm_response.insights) && (
                    <button 
                      onClick={() => navigator.clipboard.writeText(video.llm_response?.insights?.join('\n\n') ?? '')} 
                      className="flex-1 inline-flex items-center justify-center rounded-md border border-input bg-transparent px-3 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground"
                    >
                      <CopyIcon className="mr-2 h-4 w-4" />
                      Copy All Insights
                    </button>
                  )}
                  {typeof video.llm_response.recommendations === 'object' && Array.isArray(video.llm_response.recommendations) && (
                    <button 
                      onClick={() => navigator.clipboard.writeText(video.llm_response?.recommendations?.join('\n\n') ?? '')} 
                      className="flex-1 inline-flex items-center justify-center rounded-md border border-input bg-transparent px-3 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground"
                    >
                      <CopyIcon className="mr-2 h-4 w-4" />
                      Copy Recommendations
                    </button>
                  )}
                </div>
              )}
              
              {/* Full LLM Response data */}
              {renderLlmResponse(video.llm_response)}
            </div>
          </div>
        </div>
      </div>

      {/* Move transcript to the bottom of the page */}
      {video.transcript && (
        <div className="mt-10 border-t border-gray-200 dark:border-gray-700 pt-6">
          <h2 className="text-lg font-medium mb-2 flex items-center text-foreground">
            Video Transcript
          </h2>
          <details className="mt-1">
            <summary className="cursor-pointer font-medium text-primary hover:underline">
              View Full Transcript
            </summary>
            <div className="mt-2 p-4 bg-muted rounded-lg shadow max-h-[400px] overflow-y-auto border border-border">
              <p className="text-muted-foreground whitespace-pre-wrap text-sm">{video.transcript}</p>
            </div>
          </details>
        </div>
      )}
    </div>
  );
}

export async function generateMetadata({ params }: { params: { topic: string; videoId: string } }): Promise<Metadata> {
  // In Next.js 14/15, we shouldn't need to await params, but the compiler is giving warnings
  // Using type assertion instead as a workaround
  const topic = String(params.topic);
  const videoId = String(params.videoId);
  const video = await DatabaseService.getVideoById(topic, videoId);

  if (!video) {
    return {
      title: 'Video Not Found',
      description: 'This video could not be found or is not available.',
    };
  }

  let metaDescription = 'No summary available.';
  if (video.llm_response) {
    if (typeof video.llm_response.tldr === 'string') {
      metaDescription = video.llm_response.tldr;
    } else if (typeof video.llm_response.short_summary === 'string') {
      metaDescription = video.llm_response.short_summary;
    } else if (typeof video.llm_response.summary === 'string') {
      metaDescription = video.llm_response.summary;
    } else if (Array.isArray(video.llm_response.insights) && video.llm_response.insights.length > 0) {
      metaDescription = video.llm_response.insights[0];
    }
  }
  metaDescription = truncateText(metaDescription, 160);


  return {
    title: video.title ?? 'Video Summary',
    description: metaDescription,
    openGraph: {
      title: video.title ?? 'Video Summary',
      description: metaDescription,
      images: [
        {
          url: video.thumbnail_url ?? '/placeholder.svg',
          width: 1200,
          height: 630,
          alt: video.title ?? 'Video thumbnail',
        },
      ],
      type: 'video.other',
      url: `/video/${topic}/${videoId}`,
    },
  };
}
