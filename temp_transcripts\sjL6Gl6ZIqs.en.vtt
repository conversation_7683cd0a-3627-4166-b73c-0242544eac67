WEBVTT
Kind: captions
Language: en

00:00:01.800 --> 00:00:03.669 align:start position:0%
 
in<00:00:01.959><c> this</c><00:00:02.159><c> video</c><00:00:02.720><c> we're</c><00:00:02.919><c> going</c><00:00:03.040><c> to</c><00:00:03.199><c> see</c><00:00:03.439><c> if</c>

00:00:03.669 --> 00:00:03.679 align:start position:0%
in this video we're going to see if
 

00:00:03.679 --> 00:00:06.389 align:start position:0%
in this video we're going to see if
<PERSON><00:00:04.520><c> and</c><00:00:04.759><c> a</c><00:00:04.880><c> couple</c><00:00:05.080><c> of</c><00:00:05.240><c> friends</c><00:00:06.040><c> can</c><00:00:06.200><c> help</c>

00:00:06.389 --> 00:00:06.399 align:start position:0%
<PERSON> and a couple of friends can help
 

00:00:06.399 --> 00:00:08.750 align:start position:0%
Claude and a couple of friends can help
us<00:00:06.600><c> analyze</c><00:00:07.040><c> the</c><00:00:07.120><c> world</c><00:00:07.439><c> economy</c><00:00:08.120><c> in</c><00:00:08.320><c> a</c><00:00:08.480><c> matter</c>

00:00:08.750 --> 00:00:08.760 align:start position:0%
us analyze the world economy in a matter
 

00:00:08.760 --> 00:00:09.470 align:start position:0%
us analyze the world economy in a matter
of

00:00:09.470 --> 00:00:09.480 align:start position:0%
of
 

00:00:09.480 --> 00:00:12.789 align:start position:0%
of
minutes<00:00:10.480><c> okay</c><00:00:11.080><c> I've</c><00:00:11.320><c> asked</c><00:00:11.559><c> Claude</c><00:00:11.880><c> 3</c><00:00:12.240><c> Opus</c>

00:00:12.789 --> 00:00:12.799 align:start position:0%
minutes okay I've asked Claude 3 Opus
 

00:00:12.799 --> 00:00:15.070 align:start position:0%
minutes okay I've asked Claude 3 Opus
which<00:00:12.920><c> is</c><00:00:13.160><c> the</c><00:00:13.519><c> largest</c><00:00:13.960><c> model</c><00:00:14.240><c> in</c><00:00:14.440><c> anthropics</c>

00:00:15.070 --> 00:00:15.080 align:start position:0%
which is the largest model in anthropics
 

00:00:15.080 --> 00:00:17.750 align:start position:0%
which is the largest model in anthropics
new<00:00:15.360><c> Claude</c><00:00:15.719><c> 3</c><00:00:16.039><c> family</c><00:00:16.720><c> to</c><00:00:16.920><c> look</c><00:00:17.080><c> at</c><00:00:17.199><c> the</c><00:00:17.320><c> GDP</c>

00:00:17.750 --> 00:00:17.760 align:start position:0%
new Claude 3 family to look at the GDP
 

00:00:17.760 --> 00:00:19.990 align:start position:0%
new Claude 3 family to look at the GDP
trends<00:00:18.080><c> for</c><00:00:18.279><c> the</c><00:00:18.400><c> US</c><00:00:19.199><c> and</c><00:00:19.439><c> write</c><00:00:19.680><c> down</c><00:00:19.840><c> a</c>

00:00:19.990 --> 00:00:20.000 align:start position:0%
trends for the US and write down a
 

00:00:20.000 --> 00:00:22.950 align:start position:0%
trends for the US and write down a
markdown<00:00:20.640><c> table</c><00:00:21.199><c> of</c><00:00:21.359><c> what</c><00:00:21.480><c> it</c><00:00:21.720><c> sees</c><00:00:22.720><c> we've</c>

00:00:22.950 --> 00:00:22.960 align:start position:0%
markdown table of what it sees we've
 

00:00:22.960 --> 00:00:25.189 align:start position:0%
markdown table of what it sees we've
given<00:00:23.279><c> Opus</c><00:00:24.000><c> and</c><00:00:24.279><c> all</c><00:00:24.439><c> the</c><00:00:24.560><c> other</c><00:00:24.720><c> models</c><00:00:25.080><c> in</c>

00:00:25.189 --> 00:00:25.199 align:start position:0%
given Opus and all the other models in
 

00:00:25.199 --> 00:00:27.870 align:start position:0%
given Opus and all the other models in
the<00:00:25.320><c> clae</c><00:00:25.599><c> 3</c><00:00:25.880><c> family</c><00:00:26.640><c> extensive</c><00:00:27.279><c> training</c><00:00:27.679><c> on</c>

00:00:27.870 --> 00:00:27.880 align:start position:0%
the clae 3 family extensive training on
 

00:00:27.880 --> 00:00:29.429 align:start position:0%
the clae 3 family extensive training on
tool<00:00:28.160><c> use</c><00:00:28.480><c> and</c><00:00:28.599><c> one</c><00:00:28.679><c> of</c><00:00:28.760><c> the</c><00:00:28.840><c> major</c><00:00:29.080><c> tools</c><00:00:29.320><c> it's</c>

00:00:29.429 --> 00:00:29.439 align:start position:0%
tool use and one of the major tools it's
 

00:00:29.439 --> 00:00:32.350 align:start position:0%
tool use and one of the major tools it's
using<00:00:29.679><c> is</c><00:00:29.800><c> this</c><00:00:30.320><c> web</c><00:00:30.599><c> view</c><00:00:30.840><c> tool</c><00:00:31.759><c> it</c><00:00:31.880><c> goes</c><00:00:32.079><c> to</c><00:00:32.200><c> a</c>

00:00:32.350 --> 00:00:32.360 align:start position:0%
using is this web view tool it goes to a
 

00:00:32.360 --> 00:00:34.790 align:start position:0%
using is this web view tool it goes to a
URL<00:00:33.040><c> looks</c><00:00:33.280><c> at</c><00:00:33.399><c> what's</c><00:00:33.600><c> on</c><00:00:33.719><c> the</c><00:00:33.879><c> page</c><00:00:34.559><c> and</c>

00:00:34.790 --> 00:00:34.800 align:start position:0%
URL looks at what's on the page and
 

00:00:34.800 --> 00:00:36.830 align:start position:0%
URL looks at what's on the page and
because<00:00:35.000><c> it's</c><00:00:35.160><c> multimodal</c><00:00:36.079><c> it</c><00:00:36.239><c> can</c><00:00:36.399><c> use</c><00:00:36.680><c> the</c>

00:00:36.830 --> 00:00:36.840 align:start position:0%
because it's multimodal it can use the
 

00:00:36.840 --> 00:00:38.549 align:start position:0%
because it's multimodal it can use the
information<00:00:37.280><c> on</c><00:00:37.480><c> that</c><00:00:37.640><c> page</c><00:00:38.079><c> to</c><00:00:38.239><c> solve</c>

00:00:38.549 --> 00:00:38.559 align:start position:0%
information on that page to solve
 

00:00:38.559 --> 00:00:41.869 align:start position:0%
information on that page to solve
complex<00:00:39.280><c> problems</c><00:00:40.280><c> so</c><00:00:40.520><c> here's</c><00:00:40.680><c> the</c><00:00:40.879><c> markdown</c>

00:00:41.869 --> 00:00:41.879 align:start position:0%
complex problems so here's the markdown
 

00:00:41.879 --> 00:00:43.630 align:start position:0%
complex problems so here's the markdown
and<00:00:42.039><c> it's</c><00:00:42.200><c> important</c><00:00:42.520><c> to</c><00:00:42.680><c> note</c><00:00:42.960><c> that</c><00:00:43.320><c> CLA</c>

00:00:43.630 --> 00:00:43.640 align:start position:0%
and it's important to note that CLA
 

00:00:43.640 --> 00:00:45.069 align:start position:0%
and it's important to note that CLA
doesn't<00:00:43.920><c> have</c><00:00:44.120><c> direct</c><00:00:44.520><c> access</c><00:00:44.800><c> to</c><00:00:44.960><c> these</c>

00:00:45.069 --> 00:00:45.079 align:start position:0%
doesn't have direct access to these
 

00:00:45.079 --> 00:00:46.830 align:start position:0%
doesn't have direct access to these
numbers<00:00:45.600><c> it's</c><00:00:45.760><c> literally</c><00:00:46.199><c> looking</c><00:00:46.480><c> at</c><00:00:46.640><c> the</c>

00:00:46.830 --> 00:00:46.840 align:start position:0%
numbers it's literally looking at the
 

00:00:46.840 --> 00:00:48.590 align:start position:0%
numbers it's literally looking at the
same<00:00:47.079><c> browser</c><00:00:47.480><c> you</c><00:00:47.559><c> and</c><00:00:47.680><c> I</c><00:00:47.840><c> are</c><00:00:47.960><c> seeing</c>

00:00:48.590 --> 00:00:48.600 align:start position:0%
same browser you and I are seeing
 

00:00:48.600 --> 00:00:50.630 align:start position:0%
same browser you and I are seeing
looking<00:00:48.840><c> at</c><00:00:48.960><c> the</c><00:00:49.079><c> trend</c><00:00:49.440><c> line</c><00:00:50.039><c> and</c><00:00:50.199><c> trying</c><00:00:50.440><c> to</c>

00:00:50.630 --> 00:00:50.640 align:start position:0%
looking at the trend line and trying to
 

00:00:50.640 --> 00:00:52.869 align:start position:0%
looking at the trend line and trying to
estimate<00:00:51.039><c> what</c><00:00:51.160><c> the</c><00:00:51.320><c> exact</c><00:00:51.600><c> numbers</c>

00:00:52.869 --> 00:00:52.879 align:start position:0%
estimate what the exact numbers
 

00:00:52.879 --> 00:00:56.150 align:start position:0%
estimate what the exact numbers
are<00:00:53.879><c> let's</c><00:00:54.079><c> see</c><00:00:54.239><c> how</c><00:00:54.520><c> accurate</c><00:00:54.840><c> it</c><00:00:55.000><c> was</c><00:00:55.960><c> we've</c>

00:00:56.150 --> 00:00:56.160 align:start position:0%
are let's see how accurate it was we've
 

00:00:56.160 --> 00:00:58.150 align:start position:0%
are let's see how accurate it was we've
asked<00:00:56.320><c> the</c><00:00:56.440><c> model</c><00:00:56.680><c> to</c><00:00:57.039><c> create</c><00:00:57.280><c> a</c><00:00:57.480><c> plot</c><00:00:57.879><c> of</c><00:00:58.000><c> the</c>

00:00:58.150 --> 00:00:58.160 align:start position:0%
asked the model to create a plot of the
 

00:00:58.160 --> 00:01:00.389 align:start position:0%
asked the model to create a plot of the
data<00:00:58.800><c> and</c><00:00:58.920><c> it's</c><00:00:59.079><c> used</c><00:00:59.280><c> the</c><00:00:59.440><c> second</c><00:00:59.640><c> tool</c><00:01:00.079><c> this</c>

00:01:00.389 --> 00:01:00.399 align:start position:0%
data and it's used the second tool this
 

00:01:00.399 --> 00:01:02.950 align:start position:0%
data and it's used the second tool this
python<00:01:00.840><c> interpreter</c><00:01:01.760><c> to</c><00:01:02.000><c> write</c><00:01:02.199><c> out</c><00:01:02.359><c> the</c><00:01:02.519><c> code</c>

00:01:02.950 --> 00:01:02.960 align:start position:0%
python interpreter to write out the code
 

00:01:02.960 --> 00:01:05.230 align:start position:0%
python interpreter to write out the code
and<00:01:03.120><c> then</c><00:01:03.320><c> render</c><00:01:03.719><c> the</c><00:01:03.840><c> image</c><00:01:04.239><c> for</c><00:01:04.400><c> us</c><00:01:04.559><c> to</c>

00:01:05.230 --> 00:01:05.240 align:start position:0%
and then render the image for us to
 

00:01:05.240 --> 00:01:08.550 align:start position:0%
and then render the image for us to
check<00:01:06.240><c> and</c><00:01:06.479><c> here's</c><00:01:06.680><c> the</c><00:01:06.840><c> image</c><00:01:07.759><c> look</c><00:01:08.280><c> it's</c>

00:01:08.550 --> 00:01:08.560 align:start position:0%
check and here's the image look it's
 

00:01:08.560 --> 00:01:10.670 align:start position:0%
check and here's the image look it's
actually<00:01:09.200><c> added</c><00:01:09.560><c> helpful</c><00:01:09.920><c> little</c><00:01:10.119><c> tool</c><00:01:10.439><c> tip</c>

00:01:10.670 --> 00:01:10.680 align:start position:0%
actually added helpful little tool tip
 

00:01:10.680 --> 00:01:13.070 align:start position:0%
actually added helpful little tool tip
animations<00:01:11.520><c> to</c><00:01:11.799><c> explain</c><00:01:12.439><c> some</c><00:01:12.560><c> of</c><00:01:12.720><c> the</c><00:01:12.799><c> major</c>

00:01:13.070 --> 00:01:13.080 align:start position:0%
animations to explain some of the major
 

00:01:13.080 --> 00:01:15.030 align:start position:0%
animations to explain some of the major
Peaks<00:01:13.360><c> and</c><00:01:13.479><c> troughs</c><00:01:14.119><c> in</c><00:01:14.240><c> the</c><00:01:14.400><c> last</c><00:01:14.640><c> decade</c><00:01:14.920><c> or</c>

00:01:15.030 --> 00:01:15.040 align:start position:0%
Peaks and troughs in the last decade or
 

00:01:15.040 --> 00:01:18.030 align:start position:0%
Peaks and troughs in the last decade or
two<00:01:15.280><c> of</c><00:01:15.400><c> the</c><00:01:15.479><c> US</c><00:01:16.240><c> economy</c><00:01:17.240><c> and</c><00:01:17.400><c> we</c><00:01:17.479><c> can</c><00:01:17.680><c> compare</c>

00:01:18.030 --> 00:01:18.040 align:start position:0%
two of the US economy and we can compare
 

00:01:18.040 --> 00:01:20.550 align:start position:0%
two of the US economy and we can compare
that<00:01:18.240><c> graph</c><00:01:18.600><c> with</c><00:01:18.799><c> the</c><00:01:19.040><c> actual</c><00:01:19.520><c> data</c><00:01:20.320><c> and</c><00:01:20.439><c> it</c>

00:01:20.550 --> 00:01:20.560 align:start position:0%
that graph with the actual data and it
 

00:01:20.560 --> 00:01:21.910 align:start position:0%
that graph with the actual data and it
turns<00:01:20.799><c> out</c><00:01:21.040><c> it's</c><00:01:21.200><c> pretty</c><00:01:21.400><c> close</c><00:01:21.720><c> it's</c>

00:01:21.910 --> 00:01:21.920 align:start position:0%
turns out it's pretty close it's
 

00:01:21.920 --> 00:01:24.950 align:start position:0%
turns out it's pretty close it's
actually<00:01:22.200><c> within</c><00:01:22.960><c> 5%</c>

00:01:24.950 --> 00:01:24.960 align:start position:0%
actually within 5%
 

00:01:24.960 --> 00:01:27.190 align:start position:0%
actually within 5%
accuracy<00:01:25.960><c> and</c><00:01:26.159><c> by</c><00:01:26.280><c> the</c><00:01:26.400><c> way</c><00:01:26.799><c> claud's</c>

00:01:27.190 --> 00:01:27.200 align:start position:0%
accuracy and by the way claud's
 

00:01:27.200 --> 00:01:28.789 align:start position:0%
accuracy and by the way claud's
transcription<00:01:27.799><c> here</c><00:01:28.040><c> isn't</c><00:01:28.360><c> just</c><00:01:28.520><c> coming</c>

00:01:28.789 --> 00:01:28.799 align:start position:0%
transcription here isn't just coming
 

00:01:28.799 --> 00:01:30.870 align:start position:0%
transcription here isn't just coming
from<00:01:29.000><c> its</c><00:01:29.280><c> pre-existing</c><00:01:30.079><c> knowledge</c><00:01:30.400><c> of</c><00:01:30.560><c> US</c>

00:01:30.870 --> 00:01:30.880 align:start position:0%
from its pre-existing knowledge of US
 

00:01:30.880 --> 00:01:33.350 align:start position:0%
from its pre-existing knowledge of US
GDP<00:01:31.880><c> we</c><00:01:32.000><c> tried</c><00:01:32.240><c> it</c><00:01:32.360><c> with</c><00:01:32.439><c> a</c><00:01:32.600><c> large</c><00:01:32.880><c> sample</c><00:01:33.200><c> of</c>

00:01:33.350 --> 00:01:33.360 align:start position:0%
GDP we tried it with a large sample of
 

00:01:33.360 --> 00:01:35.789 align:start position:0%
GDP we tried it with a large sample of
madeup<00:01:33.799><c> GDP</c><00:01:34.280><c> graphs</c><00:01:34.960><c> and</c><00:01:35.079><c> its</c><00:01:35.240><c> transcription</c>

00:01:35.789 --> 00:01:35.799 align:start position:0%
madeup GDP graphs and its transcription
 

00:01:35.799 --> 00:01:40.350 align:start position:0%
madeup GDP graphs and its transcription
accuracy<00:01:36.439><c> was</c><00:01:36.640><c> within</c><00:01:37.040><c> 11%</c><00:01:37.960><c> on</c>

00:01:40.350 --> 00:01:40.360 align:start position:0%
 
 

00:01:40.360 --> 00:01:43.030 align:start position:0%
 
average<00:01:41.360><c> next</c><00:01:41.840><c> we</c><00:01:42.000><c> asked</c><00:01:42.200><c> the</c><00:01:42.280><c> model</c><00:01:42.560><c> to</c><00:01:42.799><c> do</c>

00:01:43.030 --> 00:01:43.040 align:start position:0%
average next we asked the model to do
 

00:01:43.040 --> 00:01:45.429 align:start position:0%
average next we asked the model to do
some<00:01:43.320><c> statistical</c><00:01:43.880><c> analysis</c><00:01:44.719><c> projecting</c><00:01:45.240><c> out</c>

00:01:45.429 --> 00:01:45.439 align:start position:0%
some statistical analysis projecting out
 

00:01:45.439 --> 00:01:47.870 align:start position:0%
some statistical analysis projecting out
into<00:01:45.640><c> the</c><00:01:45.719><c> future</c><00:01:46.360><c> performing</c><00:01:46.880><c> simulations</c>

00:01:47.870 --> 00:01:47.880 align:start position:0%
into the future performing simulations
 

00:01:47.880 --> 00:01:49.950 align:start position:0%
into the future performing simulations
to<00:01:48.040><c> see</c><00:01:48.320><c> where</c><00:01:48.479><c> the</c><00:01:48.600><c> GDP</c><00:01:49.079><c> of</c><00:01:49.159><c> the</c><00:01:49.280><c> US</c><00:01:49.719><c> might</c>

00:01:49.950 --> 00:01:49.960 align:start position:0%
to see where the GDP of the US might
 

00:01:49.960 --> 00:01:52.069 align:start position:0%
to see where the GDP of the US might
head<00:01:50.840><c> and</c><00:01:50.960><c> we</c><00:01:51.040><c> can</c><00:01:51.159><c> see</c><00:01:51.320><c> that</c><00:01:51.439><c> it's</c><00:01:51.600><c> run</c><00:01:51.920><c> this</c>

00:01:52.069 --> 00:01:52.079 align:start position:0%
head and we can see that it's run this
 

00:01:52.079 --> 00:01:54.270 align:start position:0%
head and we can see that it's run this
analysis<00:01:52.520><c> using</c><00:01:52.840><c> Python</c><00:01:53.520><c> and</c><00:01:53.640><c> it's</c><00:01:53.840><c> able</c><00:01:54.040><c> to</c>

00:01:54.270 --> 00:01:54.280 align:start position:0%
analysis using Python and it's able to
 

00:01:54.280 --> 00:01:56.789 align:start position:0%
analysis using Python and it's able to
perform<00:01:54.799><c> these</c><00:01:54.960><c> Monte</c><00:01:55.399><c> Carlo</c><00:01:55.799><c> simulations</c><00:01:56.640><c> to</c>

00:01:56.789 --> 00:01:56.799 align:start position:0%
perform these Monte Carlo simulations to
 

00:01:56.799 --> 00:01:59.109 align:start position:0%
perform these Monte Carlo simulations to
see<00:01:57.039><c> what</c><00:01:57.200><c> the</c><00:01:57.360><c> range</c><00:01:57.680><c> of</c><00:01:57.880><c> GDP</c><00:01:58.520><c> possibilities</c>

00:01:59.109 --> 00:01:59.119 align:start position:0%
see what the range of GDP possibilities
 

00:01:59.119 --> 00:02:02.670 align:start position:0%
see what the range of GDP possibilities
might<00:01:59.280><c> look</c><00:01:59.520><c> like</c><00:01:59.840><c> for</c><00:02:00.039><c> the</c><00:02:00.159><c> next</c><00:02:00.399><c> decade</c><00:02:00.680><c> or</c>

00:02:02.670 --> 00:02:02.680 align:start position:0%
might look like for the next decade or
 

00:02:02.680 --> 00:02:06.109 align:start position:0%
might look like for the next decade or
so<00:02:03.680><c> but</c><00:02:03.920><c> I</c><00:02:04.039><c> wonder</c><00:02:04.479><c> if</c><00:02:04.600><c> we</c><00:02:04.719><c> can</c><00:02:04.920><c> go</c><00:02:05.159><c> further</c>

00:02:06.109 --> 00:02:06.119 align:start position:0%
so but I wonder if we can go further
 

00:02:06.119 --> 00:02:07.550 align:start position:0%
so but I wonder if we can go further
we're<00:02:06.280><c> going</c><00:02:06.399><c> to</c><00:02:06.520><c> get</c><00:02:06.680><c> the</c><00:02:06.759><c> model</c><00:02:07.039><c> to</c><00:02:07.200><c> analyze</c>

00:02:07.550 --> 00:02:07.560 align:start position:0%
we're going to get the model to analyze
 

00:02:07.560 --> 00:02:09.949 align:start position:0%
we're going to get the model to analyze
a<00:02:07.719><c> more</c><00:02:07.960><c> complicated</c><00:02:08.520><c> question</c><00:02:09.119><c> that</c><00:02:09.239><c> is</c><00:02:09.720><c> how</c>

00:02:09.949 --> 00:02:09.959 align:start position:0%
a more complicated question that is how
 

00:02:09.959 --> 00:02:12.110 align:start position:0%
a more complicated question that is how
GDP<00:02:10.479><c> might</c><00:02:10.679><c> change</c><00:02:11.239><c> across</c><00:02:11.680><c> all</c><00:02:11.879><c> of</c><00:02:12.000><c> the</c>

00:02:12.110 --> 00:02:12.120 align:start position:0%
GDP might change across all of the
 

00:02:12.120 --> 00:02:14.150 align:start position:0%
GDP might change across all of the
biggest<00:02:12.400><c> world</c><00:02:12.680><c> economies</c><00:02:13.640><c> and</c><00:02:13.720><c> then</c><00:02:13.840><c> to</c><00:02:14.000><c> help</c>

00:02:14.150 --> 00:02:14.160 align:start position:0%
biggest world economies and then to help
 

00:02:14.160 --> 00:02:15.830 align:start position:0%
biggest world economies and then to help
it<00:02:14.319><c> do</c><00:02:14.519><c> that</c><00:02:14.959><c> we're</c><00:02:15.120><c> going</c><00:02:15.239><c> to</c><00:02:15.360><c> give</c><00:02:15.480><c> it</c><00:02:15.640><c> one</c>

00:02:15.830 --> 00:02:15.840 align:start position:0%
it do that we're going to give it one
 

00:02:15.840 --> 00:02:19.430 align:start position:0%
it do that we're going to give it one
more<00:02:16.040><c> tool</c><00:02:16.360><c> called</c><00:02:16.920><c> dispatch</c><00:02:17.680><c> sub</c><00:02:18.440><c> agents</c>

00:02:19.430 --> 00:02:19.440 align:start position:0%
more tool called dispatch sub agents
 

00:02:19.440 --> 00:02:21.390 align:start position:0%
more tool called dispatch sub agents
this<00:02:19.599><c> basically</c><00:02:19.959><c> allows</c><00:02:20.239><c> the</c><00:02:20.360><c> model</c><00:02:20.800><c> to</c><00:02:21.040><c> break</c>

00:02:21.390 --> 00:02:21.400 align:start position:0%
this basically allows the model to break
 

00:02:21.400 --> 00:02:23.390 align:start position:0%
this basically allows the model to break
down<00:02:21.680><c> the</c><00:02:21.920><c> problem</c><00:02:22.360><c> into</c><00:02:22.640><c> lots</c><00:02:22.879><c> of</c><00:02:23.080><c> sub</c>

00:02:23.390 --> 00:02:23.400 align:start position:0%
down the problem into lots of sub
 

00:02:23.400 --> 00:02:25.430 align:start position:0%
down the problem into lots of sub
problems<00:02:23.959><c> and</c><00:02:24.080><c> then</c><00:02:24.239><c> write</c><00:02:24.599><c> prompts</c><00:02:25.120><c> for</c>

00:02:25.430 --> 00:02:25.440 align:start position:0%
problems and then write prompts for
 

00:02:25.440 --> 00:02:27.509 align:start position:0%
problems and then write prompts for
other<00:02:25.760><c> versions</c><00:02:26.200><c> of</c><00:02:26.360><c> itself</c><00:02:27.000><c> to</c><00:02:27.160><c> help</c><00:02:27.400><c> pick</c>

00:02:27.509 --> 00:02:27.519 align:start position:0%
other versions of itself to help pick
 

00:02:27.519 --> 00:02:30.190 align:start position:0%
other versions of itself to help pick
out<00:02:27.680><c> the</c><00:02:27.959><c> slack</c><00:02:28.959><c> the</c><00:02:29.080><c> models</c><00:02:29.560><c> can</c><00:02:29.920><c> then</c>

00:02:30.190 --> 00:02:30.200 align:start position:0%
out the slack the models can then
 

00:02:30.200 --> 00:02:32.390 align:start position:0%
out the slack the models can then
complete<00:02:30.560><c> a</c><00:02:30.680><c> more</c><00:02:30.920><c> complex</c><00:02:31.440><c> task</c><00:02:32.040><c> by</c><00:02:32.200><c> all</c>

00:02:32.390 --> 00:02:32.400 align:start position:0%
complete a more complex task by all
 

00:02:32.400 --> 00:02:34.670 align:start position:0%
complete a more complex task by all
working<00:02:32.959><c> together</c><00:02:33.959><c> here</c><00:02:34.120><c> you</c><00:02:34.200><c> can</c><00:02:34.319><c> see</c><00:02:34.519><c> it's</c>

00:02:34.670 --> 00:02:34.680 align:start position:0%
working together here you can see it's
 

00:02:34.680 --> 00:02:36.470 align:start position:0%
working together here you can see it's
written<00:02:34.920><c> this</c><00:02:35.280><c> prompt</c><00:02:35.640><c> and</c><00:02:35.800><c> given</c><00:02:36.239><c> very</c>

00:02:36.470 --> 00:02:36.480 align:start position:0%
written this prompt and given very
 

00:02:36.480 --> 00:02:38.270 align:start position:0%
written this prompt and given very
precise<00:02:36.879><c> instructions</c><00:02:37.640><c> that</c><00:02:37.840><c> it</c><00:02:37.920><c> wants</c><00:02:38.160><c> the</c>

00:02:38.270 --> 00:02:38.280 align:start position:0%
precise instructions that it wants the
 

00:02:38.280 --> 00:02:40.630 align:start position:0%
precise instructions that it wants the
other<00:02:38.519><c> models</c><00:02:38.879><c> to</c><00:02:39.000><c> follow</c><00:02:39.879><c> including</c><00:02:40.480><c> a</c>

00:02:40.630 --> 00:02:40.640 align:start position:0%
other models to follow including a
 

00:02:40.640 --> 00:02:42.670 align:start position:0%
other models to follow including a
format<00:02:41.000><c> for</c><00:02:41.159><c> the</c><00:02:41.319><c> data</c><00:02:41.720><c> that</c><00:02:41.840><c> it's</c><00:02:41.959><c> hoping</c><00:02:42.200><c> to</c>

00:02:42.670 --> 00:02:42.680 align:start position:0%
format for the data that it's hoping to
 

00:02:42.680 --> 00:02:45.149 align:start position:0%
format for the data that it's hoping to
return<00:02:43.680><c> it's</c><00:02:43.959><c> dispatched</c><00:02:44.440><c> a</c><00:02:44.560><c> version</c><00:02:44.840><c> of</c><00:02:45.000><c> this</c>

00:02:45.149 --> 00:02:45.159 align:start position:0%
return it's dispatched a version of this
 

00:02:45.159 --> 00:02:46.750 align:start position:0%
return it's dispatched a version of this
prompt<00:02:45.519><c> to</c><00:02:45.800><c> one</c><00:02:46.000><c> model</c><00:02:46.280><c> that's</c><00:02:46.440><c> going</c><00:02:46.519><c> to</c><00:02:46.640><c> look</c>

00:02:46.750 --> 00:02:46.760 align:start position:0%
prompt to one model that's going to look
 

00:02:46.760 --> 00:02:49.949 align:start position:0%
prompt to one model that's going to look
at<00:02:46.920><c> the</c><00:02:47.080><c> US</c><00:02:47.840><c> one</c><00:02:48.000><c> for</c><00:02:48.280><c> China</c><00:02:48.800><c> One</c><00:02:48.959><c> for</c><00:02:49.239><c> Germany</c>

00:02:49.949 --> 00:02:49.959 align:start position:0%
at the US one for China One for Germany
 

00:02:49.959 --> 00:02:53.350 align:start position:0%
at the US one for China One for Germany
Japan<00:02:50.640><c> and</c><00:02:50.800><c> so</c><00:02:51.360><c> on</c><00:02:52.360><c> we</c><00:02:52.440><c> can</c><00:02:52.560><c> see</c><00:02:52.720><c> in</c><00:02:52.879><c> these</c>

00:02:53.350 --> 00:02:53.360 align:start position:0%
Japan and so on we can see in these
 

00:02:53.360 --> 00:02:55.710 align:start position:0%
Japan and so on we can see in these
progress<00:02:53.800><c> bars</c><00:02:54.480><c> that</c><00:02:54.640><c> the</c><00:02:54.720><c> sub</c><00:02:54.959><c> agent</c><00:02:55.200><c> models</c>

00:02:55.710 --> 00:02:55.720 align:start position:0%
progress bars that the sub agent models
 

00:02:55.720 --> 00:02:58.070 align:start position:0%
progress bars that the sub agent models
are<00:02:55.920><c> now</c><00:02:56.159><c> completing</c><00:02:56.640><c> the</c><00:02:56.800><c> set</c><00:02:57.120><c> task</c><00:02:57.640><c> for</c><00:02:57.879><c> each</c>

00:02:58.070 --> 00:02:58.080 align:start position:0%
are now completing the set task for each
 

00:02:58.080 --> 00:03:00.470 align:start position:0%
are now completing the set task for each
of<00:02:58.200><c> the</c><00:02:58.360><c> individual</c><00:02:58.840><c> economies</c>

00:03:00.470 --> 00:03:00.480 align:start position:0%
of the individual economies
 

00:03:00.480 --> 00:03:02.509 align:start position:0%
of the individual economies
they're<00:03:00.680><c> going</c><00:03:00.840><c> to</c><00:03:01.000><c> the</c><00:03:01.120><c> relevant</c><00:03:01.640><c> web</c><00:03:01.879><c> pages</c>

00:03:02.509 --> 00:03:02.519 align:start position:0%
they're going to the relevant web pages
 

00:03:02.519 --> 00:03:04.070 align:start position:0%
they're going to the relevant web pages
they're<00:03:02.720><c> getting</c><00:03:02.959><c> the</c><00:03:03.120><c> information</c><00:03:03.920><c> they're</c>

00:03:04.070 --> 00:03:04.080 align:start position:0%
they're getting the information they're
 

00:03:04.080 --> 00:03:06.070 align:start position:0%
they're getting the information they're
running<00:03:04.360><c> the</c><00:03:04.519><c> code</c><00:03:04.760><c> to</c><00:03:04.920><c> analyze</c><00:03:05.360><c> it</c><00:03:05.720><c> just</c><00:03:05.920><c> like</c>

00:03:06.070 --> 00:03:06.080 align:start position:0%
running the code to analyze it just like
 

00:03:06.080 --> 00:03:08.630 align:start position:0%
running the code to analyze it just like
we<00:03:06.200><c> saw</c><00:03:06.640><c> in</c><00:03:06.799><c> the</c><00:03:06.959><c> previous</c><00:03:07.319><c> US</c><00:03:07.760><c> example</c><00:03:08.400><c> but</c>

00:03:08.630 --> 00:03:08.640 align:start position:0%
we saw in the previous US example but
 

00:03:08.640 --> 00:03:11.070 align:start position:0%
we saw in the previous US example but
all<00:03:08.840><c> in</c>

00:03:11.070 --> 00:03:11.080 align:start position:0%
all in
 

00:03:11.080 --> 00:03:13.750 align:start position:0%
all in
parallel<00:03:12.080><c> let's</c><00:03:12.319><c> just</c><00:03:12.480><c> skip</c><00:03:12.799><c> forward</c><00:03:13.360><c> to</c><00:03:13.519><c> see</c>

00:03:13.750 --> 00:03:13.760 align:start position:0%
parallel let's just skip forward to see
 

00:03:13.760 --> 00:03:16.350 align:start position:0%
parallel let's just skip forward to see
what<00:03:13.879><c> the</c><00:03:14.000><c> model</c><00:03:14.400><c> produced</c><00:03:15.360><c> you</c><00:03:15.480><c> can</c><00:03:15.720><c> see</c><00:03:15.920><c> it's</c>

00:03:16.350 --> 00:03:16.360 align:start position:0%
what the model produced you can see it's
 

00:03:16.360 --> 00:03:18.869 align:start position:0%
what the model produced you can see it's
run<00:03:16.640><c> the</c><00:03:16.840><c> analysis</c><00:03:17.599><c> it's</c><00:03:17.799><c> produced</c><00:03:18.200><c> a</c><00:03:18.640><c> pre-</c>

00:03:18.869 --> 00:03:18.879 align:start position:0%
run the analysis it's produced a pre-
 

00:03:18.879 --> 00:03:21.070 align:start position:0%
run the analysis it's produced a pre-
and<00:03:19.120><c> post</c><00:03:19.519><c> pie</c><00:03:19.799><c> chart</c><00:03:20.120><c> of</c><00:03:20.280><c> how</c><00:03:20.400><c> it</c><00:03:20.599><c> expects</c><00:03:21.000><c> the</c>

00:03:21.070 --> 00:03:21.080 align:start position:0%
and post pie chart of how it expects the
 

00:03:21.080 --> 00:03:24.270 align:start position:0%
and post pie chart of how it expects the
world<00:03:21.319><c> economy</c><00:03:21.680><c> to</c><00:03:21.840><c> look</c><00:03:22.560><c> in</c><00:03:22.760><c> 2030</c><00:03:23.440><c> versus</c>

00:03:24.270 --> 00:03:24.280 align:start position:0%
world economy to look in 2030 versus
 

00:03:24.280 --> 00:03:26.589 align:start position:0%
world economy to look in 2030 versus
2020<00:03:25.280><c> and</c><00:03:25.440><c> it's</c><00:03:25.599><c> given</c><00:03:25.840><c> us</c><00:03:26.159><c> a</c><00:03:26.280><c> written</c>

00:03:26.589 --> 00:03:26.599 align:start position:0%
2020 and it's given us a written
 

00:03:26.599 --> 00:03:28.509 align:start position:0%
2020 and it's given us a written
analysis<00:03:27.040><c> too</c><00:03:27.280><c> where</c><00:03:27.400><c> it</c><00:03:27.519><c> makes</c><00:03:28.000><c> variable</c>

00:03:28.509 --> 00:03:28.519 align:start position:0%
analysis too where it makes variable
 

00:03:28.519 --> 00:03:29.869 align:start position:0%
analysis too where it makes variable
predictions<00:03:29.040><c> that</c><00:03:29.200><c> relate</c><00:03:29.439><c> to</c><00:03:29.760><c> the</c>

00:03:29.869 --> 00:03:29.879 align:start position:0%
predictions that relate to the
 

00:03:29.879 --> 00:03:32.149 align:start position:0%
predictions that relate to the
statistical<00:03:30.640><c> analysis</c><00:03:31.200><c> that</c><00:03:31.319><c> it</c><00:03:31.439><c> ran</c><00:03:32.000><c> it's</c>

00:03:32.149 --> 00:03:32.159 align:start position:0%
statistical analysis that it ran it's
 

00:03:32.159 --> 00:03:34.309 align:start position:0%
statistical analysis that it ran it's
telling<00:03:32.480><c> us</c><00:03:32.640><c> that</c><00:03:32.760><c> it</c><00:03:32.879><c> thinks</c><00:03:33.159><c> the</c><00:03:33.319><c> GDP</c><00:03:33.760><c> share</c>

00:03:34.309 --> 00:03:34.319 align:start position:0%
telling us that it thinks the GDP share
 

00:03:34.319 --> 00:03:36.470 align:start position:0%
telling us that it thinks the GDP share
of<00:03:34.519><c> particular</c><00:03:34.879><c> economies</c><00:03:35.360><c> will</c><00:03:35.560><c> change</c><00:03:36.280><c> and</c>

00:03:36.470 --> 00:03:36.480 align:start position:0%
of particular economies will change and
 

00:03:36.480 --> 00:03:40.030 align:start position:0%
of particular economies will change and
which<00:03:36.640><c> ones</c><00:03:36.879><c> will</c><00:03:37.040><c> be</c><00:03:37.439><c> larger</c><00:03:37.959><c> or</c><00:03:38.159><c> smaller</c><00:03:38.799><c> by</c>

00:03:40.030 --> 00:03:40.040 align:start position:0%
which ones will be larger or smaller by
 

00:03:40.040 --> 00:03:42.750 align:start position:0%
which ones will be larger or smaller by
2030<00:03:41.040><c> so</c><00:03:41.239><c> there</c><00:03:41.360><c> we</c><00:03:41.519><c> have</c><00:03:41.640><c> it</c><00:03:42.120><c> complex</c>

00:03:42.750 --> 00:03:42.760 align:start position:0%
2030 so there we have it complex
 

00:03:42.760 --> 00:03:45.830 align:start position:0%
2030 so there we have it complex
multi-step<00:03:43.640><c> multimodal</c><00:03:44.360><c> analysis</c><00:03:45.239><c> run</c><00:03:45.519><c> by</c><00:03:45.720><c> a</c>

00:03:45.830 --> 00:03:45.840 align:start position:0%
multi-step multimodal analysis run by a
 

00:03:45.840 --> 00:03:48.429 align:start position:0%
multi-step multimodal analysis run by a
model<00:03:46.319><c> that</c><00:03:46.480><c> can</c><00:03:46.680><c> create</c><00:03:47.080><c> sub</c><00:03:47.360><c> agents</c><00:03:47.959><c> to</c><00:03:48.159><c> get</c>

00:03:48.429 --> 00:03:48.439 align:start position:0%
model that can create sub agents to get
 

00:03:48.439 --> 00:03:51.030 align:start position:0%
model that can create sub agents to get
even<00:03:48.720><c> more</c><00:03:49.040><c> tasks</c><00:03:49.480><c> running</c><00:03:49.799><c> in</c><00:03:50.040><c> parallel</c>

00:03:51.030 --> 00:03:51.040 align:start position:0%
even more tasks running in parallel
 

00:03:51.040 --> 00:03:52.670 align:start position:0%
even more tasks running in parallel
we're<00:03:51.280><c> excited</c><00:03:51.640><c> to</c><00:03:51.760><c> see</c><00:03:52.000><c> what</c><00:03:52.159><c> you</c><00:03:52.400><c> our</c>

00:03:52.670 --> 00:03:52.680 align:start position:0%
we're excited to see what you our
 

00:03:52.680 --> 00:03:55.350 align:start position:0%
we're excited to see what you our
customers<00:03:53.480><c> can</c><00:03:53.760><c> do</c><00:03:54.239><c> with</c><00:03:54.439><c> these</c><00:03:54.680><c> Advanced</c>

00:03:55.350 --> 00:03:55.360 align:start position:0%
customers can do with these Advanced
 

00:03:55.360 --> 00:03:58.069 align:start position:0%
customers can do with these Advanced
Claude<00:03:55.760><c> 3</c>

00:03:58.069 --> 00:03:58.079 align:start position:0%
Claude 3
 

00:03:58.079 --> 00:04:01.079 align:start position:0%
Claude 3
capabilities

