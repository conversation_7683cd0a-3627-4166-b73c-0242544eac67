CREATE OR REPLACE FUNCTION harmonize_llm_response_jsonb_v2(input_jsonb JSONB)
RETURNS JSONB AS $$
DECLARE
    output_jsonb JSONB := '''{"insights": [], "keywords": [], "questions": [], "recommendations": []}'''::JSONB;
    key TEXT;
    value JSONB;
    text_value TEXT;
    processed_keys TEXT[] := '{}'; -- To track keys handled by primary mapping

    -- Helper to add string or elements of a string array to a target jsonb array, ensuring elements are distinct strings
    PROCEDURE add_to_list(target_key TEXT, item JSONB) IS
        elem TEXT;
        current_list JSONB;
        temp_list TEXT[] := '{}';
    BEGIN
        current_list := output_jsonb->target_key;
        IF jsonb_typeof(current_list) != 'array' THEN
            current_list := '''[]'''::jsonb; -- Initialize if not an array
        END IF;

        -- Add existing elements from current_list to temp_list
        FOR elem IN SELECT * FROM jsonb_array_elements_text(current_list) LOOP
            temp_list := array_append(temp_list, elem);
        END LOOP;

        IF jsonb_typeof(item) = 'string' THEN
            text_value := item->>0; -- Get string value
            IF NOT (text_value = ANY(temp_list)) THEN
                temp_list := array_append(temp_list, text_value);
            END IF;
        ELSIF jsonb_typeof(item) = 'array' THEN
            FOR elem IN SELECT * FROM jsonb_array_elements_text(item) LOOP
                IF NOT (elem = ANY(temp_list)) THEN
                    temp_list := array_append(temp_list, elem);
                END IF;
            END LOOP;
        END IF;
        output_jsonb := jsonb_set(output_jsonb, ARRAY[target_key], to_jsonb(temp_list));
    END;

BEGIN
    IF input_jsonb IS NULL OR jsonb_typeof(input_jsonb) != 'object' THEN
        RETURN input_jsonb;
    END IF;

    FOR key, value IN SELECT * FROM jsonb_each(input_jsonb)
    LOOP
        -- Primary Mappings
        IF lower(key) IN ('insights', 'incites', 'inights', 'summary', 'analysis', 'additional_notes', 'additional-insights', 'further-insights', 'moreinsights', 'expert_analysis', 'thoughts', 'additional information', 'additional_info', 'additional_comments', 'concepts', 'key_expert_quotes', 'specific_data', 'technical_terms', 'industry_trends', 'conclusion', 'key_takeaways', 'key_points_to_remember', 'notes', 'further research', 'answers', 'additional', 'key_challenges', 'reasons') THEN
            add_to_list('insights', value);
            processed_keys := array_append(processed_keys, key);
        ELSIF lower(key) = 'keywords' THEN
            add_to_list('keywords', value);
            processed_keys := array_append(processed_keys, key);
        ELSIF lower(key) IN ('questions', 'additional_questions', 'questions_to_explore', 'investment questions', 'questions-to-ask', 'questions_for_expert', 'questions_from_viewer', 'questions for further exploration', 'questions-to-encourage-critical-thinking') THEN
            add_to_list('questions', value);
            processed_keys := array_append(processed_keys, key);
        ELSIF lower(key) IN ('recommendations', 'actions', 'actionable advice', 'action_items', 'actionable steps', 'more-recommendations', 'additional_recommendations', 'next_steps') THEN
            add_to_list('recommendations', value);
            processed_keys := array_append(processed_keys, key);
        END IF;

        -- If the key itself is a question (heuristic: ends with '?' and is reasonably long)
        IF key LIKE '%?' AND length(key) > 15 AND NOT (key = ANY(processed_keys)) THEN
            add_to_list('questions', to_jsonb(key));
            -- Do not add to processed_keys here for this specific case,
            -- to allow it to be caught by unmapped logic if it wasn't a primary key and needs stringification.
            -- However, if it *was* a primary key (e.g. "Questions?"), it would already be in processed_keys.
            -- This ensures question-keys are added to questions list, and if they are not primary mapped,
            -- they will also be added to insights as "key: value" by the secondary mapping.
        END IF;
    END LOOP;

    -- Secondary Mapping for any remaining unmapped keys (append to insights)
    FOR key, value IN SELECT * FROM jsonb_each(input_jsonb)
    LOOP
        IF NOT (key = ANY(processed_keys)) THEN
            -- Check if the key was already added as a question string to avoid "QuestionKey?: [..., "QuestionKey?"]" in insights
            DECLARE
                is_already_question_key_in_questions_list BOOLEAN := FALSE;
                q_item TEXT;
            BEGIN
                FOR q_item IN SELECT * FROM jsonb_array_elements_text(output_jsonb->'questions') LOOP
                    IF q_item = key THEN
                        is_already_question_key_in_questions_list := TRUE;
                        EXIT;
                    END IF;
                END LOOP;

                IF NOT is_already_question_key_in_questions_list THEN
                     -- Ensure value is treated as text, even if it's a JSON literal like 'null'
                    add_to_list('insights', to_jsonb(concat(key, ': ', value::TEXT)));
                END IF;
            END;
        END IF;
    END LOOP;
    RETURN output_jsonb;
END;
$$ LANGUAGE plpgsql;

-- To apply this function to all relevant tables:
-- (This part is for documentation in the .sql file, not for immediate execution by the user)
/*
DO $$
DECLARE
    target_table_name TEXT;
    youtube_tables TEXT[] := ARRAY[
        'youtube_startups',
        'youtube_artificial_intelligence',
        'youtube_sustainability',
        'youtube_financial_markets',
        'youtube_gme',
        'youtube_general',
        'youtube_legal',
        'youtube_renewable_energy'
    ];
BEGIN
    FOREACH target_table_name IN ARRAY youtube_tables
    LOOP
        RAISE NOTICE 'Processing table: %', target_table_name;
        EXECUTE format(
            'UPDATE public.%I SET llm_response = harmonize_llm_response_jsonb_v2(llm_response) WHERE llm_response IS NOT NULL AND jsonb_typeof(llm_response) = ''object'';',
            target_table_name
        );
    END LOOP;
END $$;
*/
