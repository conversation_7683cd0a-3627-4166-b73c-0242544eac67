WEBVTT
Kind: captions
Language: en

00:00:00.960 --> 00:00:02.990 align:start position:0%
 
today<00:00:01.400><c> we're</c><00:00:01.640><c> excited</c><00:00:01.959><c> to</c><00:00:02.120><c> introduce</c><00:00:02.600><c> llama</c>

00:00:02.990 --> 00:00:03.000 align:start position:0%
today we're excited to introduce llama
 

00:00:03.000 --> 00:00:05.390 align:start position:0%
today we're excited to introduce llama
Cloud<00:00:03.679><c> a</c><00:00:03.840><c> manage</c><00:00:04.279><c> parsing</c><00:00:04.759><c> ingestion</c><00:00:05.240><c> and</c>

00:00:05.390 --> 00:00:05.400 align:start position:0%
Cloud a manage parsing ingestion and
 

00:00:05.400 --> 00:00:07.349 align:start position:0%
Cloud a manage parsing ingestion and
retrieval<00:00:05.879><c> service</c><00:00:06.600><c> designed</c><00:00:06.960><c> to</c><00:00:07.120><c> bring</c>

00:00:07.349 --> 00:00:07.359 align:start position:0%
retrieval service designed to bring
 

00:00:07.359 --> 00:00:09.430 align:start position:0%
retrieval service designed to bring
production<00:00:07.759><c> grade</c><00:00:08.120><c> context</c><00:00:08.559><c> augmentation</c><00:00:09.280><c> to</c>

00:00:09.430 --> 00:00:09.440 align:start position:0%
production grade context augmentation to
 

00:00:09.440 --> 00:00:12.190 align:start position:0%
production grade context augmentation to
your<00:00:09.599><c> llm</c><00:00:10.000><c> and</c><00:00:10.160><c> rag</c><00:00:10.599><c> applications</c><00:00:11.599><c> spend</c><00:00:11.960><c> less</c>

00:00:12.190 --> 00:00:12.200 align:start position:0%
your llm and rag applications spend less
 

00:00:12.200 --> 00:00:14.589 align:start position:0%
your llm and rag applications spend less
time<00:00:12.440><c> on</c><00:00:12.799><c> data</c><00:00:13.040><c> wrangling</c><00:00:13.639><c> and</c><00:00:13.799><c> more</c><00:00:14.080><c> time</c><00:00:14.400><c> on</c>

00:00:14.589 --> 00:00:14.599 align:start position:0%
time on data wrangling and more time on
 

00:00:14.599 --> 00:00:16.990 align:start position:0%
time on data wrangling and more time on
the<00:00:14.719><c> business</c><00:00:15.080><c> logic</c><00:00:16.000><c> llac</c><00:00:16.320><c> clav</c><00:00:16.560><c> launches</c>

00:00:16.990 --> 00:00:17.000 align:start position:0%
the business logic llac clav launches
 

00:00:17.000 --> 00:00:19.349 align:start position:0%
the business logic llac clav launches
with<00:00:17.240><c> two</c><00:00:17.520><c> main</c><00:00:17.840><c> components</c><00:00:18.560><c> llama</c><00:00:19.000><c> parse</c>

00:00:19.349 --> 00:00:19.359 align:start position:0%
with two main components llama parse
 

00:00:19.359 --> 00:00:21.310 align:start position:0%
with two main components llama parse
which<00:00:19.480><c> is</c><00:00:19.720><c> proprietary</c><00:00:20.359><c> document</c><00:00:20.800><c> parsing</c>

00:00:21.310 --> 00:00:21.320 align:start position:0%
which is proprietary document parsing
 

00:00:21.320 --> 00:00:23.189 align:start position:0%
which is proprietary document parsing
that's<00:00:21.600><c> very</c><00:00:21.840><c> very</c><00:00:22.080><c> good</c><00:00:22.279><c> at</c><00:00:22.480><c> parsing</c>

00:00:23.189 --> 00:00:23.199 align:start position:0%
that's very very good at parsing
 

00:00:23.199 --> 00:00:25.950 align:start position:0%
that's very very good at parsing
embedded<00:00:23.640><c> tables</c><00:00:24.320><c> with</c><00:00:24.599><c> complex</c><00:00:25.080><c> documents</c>

00:00:25.950 --> 00:00:25.960 align:start position:0%
embedded tables with complex documents
 

00:00:25.960 --> 00:00:27.550 align:start position:0%
embedded tables with complex documents
as<00:00:26.080><c> well</c><00:00:26.240><c> as</c><00:00:26.439><c> manage</c><00:00:26.800><c> injection</c><00:00:27.400><c> and</c>

00:00:27.550 --> 00:00:27.560 align:start position:0%
as well as manage injection and
 

00:00:27.560 --> 00:00:29.150 align:start position:0%
as well as manage injection and
retrieval<00:00:28.000><c> API</c><00:00:28.359><c> for</c><00:00:28.480><c> the</c><00:00:28.560><c> rest</c><00:00:28.760><c> of</c><00:00:28.880><c> llama</c>

00:00:29.150 --> 00:00:29.160 align:start position:0%
retrieval API for the rest of llama
 

00:00:29.160 --> 00:00:32.150 align:start position:0%
retrieval API for the rest of llama
cloud<00:00:30.000><c> so</c><00:00:30.160><c> let's</c><00:00:30.320><c> dig</c><00:00:30.560><c> into</c><00:00:30.800><c> it</c><00:00:31.720><c> let's</c><00:00:31.920><c> first</c>

00:00:32.150 --> 00:00:32.160 align:start position:0%
cloud so let's dig into it let's first
 

00:00:32.160 --> 00:00:35.229 align:start position:0%
cloud so let's dig into it let's first
go<00:00:32.320><c> into</c><00:00:32.719><c> llama</c><00:00:33.120><c> parse</c><00:00:33.920><c> we'll</c><00:00:34.360><c> drag</c><00:00:34.600><c> and</c><00:00:34.800><c> drop</c>

00:00:35.229 --> 00:00:35.239 align:start position:0%
go into llama parse we'll drag and drop
 

00:00:35.239 --> 00:00:39.470 align:start position:0%
go into llama parse we'll drag and drop
a<00:00:35.520><c> uber</c><00:00:35.840><c> 10q</c><00:00:36.360><c> filing</c><00:00:36.960><c> right</c>

00:00:39.470 --> 00:00:39.480 align:start position:0%
 
 

00:00:39.480 --> 00:00:42.310 align:start position:0%
 
here<00:00:40.480><c> and</c><00:00:40.640><c> as</c><00:00:40.719><c> you</c><00:00:40.840><c> can</c><00:00:41.000><c> see</c><00:00:41.559><c> we</c><00:00:41.760><c> are</c><00:00:41.960><c> able</c><00:00:42.160><c> to</c>

00:00:42.310 --> 00:00:42.320 align:start position:0%
here and as you can see we are able to
 

00:00:42.320 --> 00:00:45.069 align:start position:0%
here and as you can see we are able to
have<00:00:42.440><c> a</c><00:00:42.559><c> PDF</c><00:00:43.000><c> preview</c><00:00:43.520><c> of</c><00:00:43.680><c> the</c>

00:00:45.069 --> 00:00:45.079 align:start position:0%
have a PDF preview of the
 

00:00:45.079 --> 00:00:47.590 align:start position:0%
have a PDF preview of the
document<00:00:46.079><c> and</c><00:00:46.280><c> here</c><00:00:46.480><c> we</c><00:00:46.559><c> can</c><00:00:46.719><c> see</c><00:00:47.199><c> all</c><00:00:47.440><c> the</c>

00:00:47.590 --> 00:00:47.600 align:start position:0%
document and here we can see all the
 

00:00:47.600 --> 00:00:49.869 align:start position:0%
document and here we can see all the
messy<00:00:48.000><c> tables</c><00:00:48.399><c> within</c><00:00:48.640><c> the</c><00:00:48.760><c> document</c><00:00:49.760><c> and</c>

00:00:49.869 --> 00:00:49.879 align:start position:0%
messy tables within the document and
 

00:00:49.879 --> 00:00:52.549 align:start position:0%
messy tables within the document and
then<00:00:50.039><c> we</c><00:00:50.120><c> can</c><00:00:50.360><c> go</c><00:00:50.559><c> in</c><00:00:51.520><c> and</c><00:00:51.680><c> take</c><00:00:51.800><c> a</c><00:00:52.000><c> look</c><00:00:52.199><c> at</c><00:00:52.359><c> our</c>

00:00:52.549 --> 00:00:52.559 align:start position:0%
then we can go in and take a look at our
 

00:00:52.559 --> 00:00:55.310 align:start position:0%
then we can go in and take a look at our
par<00:00:53.000><c> markdown</c><00:00:53.920><c> representation</c><00:00:54.920><c> and</c><00:00:55.120><c> this</c><00:00:55.199><c> is</c>

00:00:55.310 --> 00:00:55.320 align:start position:0%
par markdown representation and this is
 

00:00:55.320 --> 00:00:57.630 align:start position:0%
par markdown representation and this is
a<00:00:55.559><c> result</c><00:00:56.199><c> of</c><00:00:56.480><c> our</c><00:00:56.719><c> proprietary</c><00:00:57.280><c> document</c>

00:00:57.630 --> 00:00:57.640 align:start position:0%
a result of our proprietary document
 

00:00:57.640 --> 00:01:00.029 align:start position:0%
a result of our proprietary document
parsing<00:00:58.239><c> and</c><00:00:58.359><c> you</c><00:00:58.480><c> see</c><00:00:58.680><c> the</c><00:00:58.840><c> tables</c><00:00:59.239><c> are</c><00:00:59.519><c> very</c>

00:01:00.029 --> 00:01:00.039 align:start position:0%
parsing and you see the tables are very
 

00:01:00.039 --> 00:01:03.069 align:start position:0%
parsing and you see the tables are very
very<00:01:00.280><c> cleanly</c><00:01:01.079><c> extracted</c><00:01:02.079><c> um</c><00:01:02.440><c> and</c><00:01:02.800><c> you</c><00:01:02.920><c> know</c>

00:01:03.069 --> 00:01:03.079 align:start position:0%
very cleanly extracted um and you know
 

00:01:03.079 --> 00:01:05.030 align:start position:0%
very cleanly extracted um and you know
all<00:01:03.199><c> the</c><00:01:03.359><c> spatial</c><00:01:03.680><c> formatting</c><00:01:04.119><c> is</c><00:01:04.320><c> preserved</c>

00:01:05.030 --> 00:01:05.040 align:start position:0%
all the spatial formatting is preserved
 

00:01:05.040 --> 00:01:06.870 align:start position:0%
all the spatial formatting is preserved
and<00:01:05.199><c> this</c><00:01:05.479><c> directly</c><00:01:06.040><c> integrates</c><00:01:06.600><c> with</c><00:01:06.720><c> the</c>

00:01:06.870 --> 00:01:06.880 align:start position:0%
and this directly integrates with the
 

00:01:06.880 --> 00:01:08.950 align:start position:0%
and this directly integrates with the
rest<00:01:07.040><c> of</c><00:01:07.200><c> L</c><00:01:07.439><c> index</c><00:01:07.799><c> indexing</c><00:01:08.320><c> and</c><00:01:08.479><c> retrieval</c>

00:01:08.950 --> 00:01:08.960 align:start position:0%
rest of L index indexing and retrieval
 

00:01:08.960 --> 00:01:11.350 align:start position:0%
rest of L index indexing and retrieval
abstractions<00:01:09.799><c> so</c><00:01:09.960><c> I</c><00:01:10.040><c> can</c><00:01:10.159><c> show</c><00:01:10.280><c> you</c><00:01:10.479><c> right</c>

00:01:11.350 --> 00:01:11.360 align:start position:0%
abstractions so I can show you right
 

00:01:11.360 --> 00:01:13.950 align:start position:0%
abstractions so I can show you right
here<00:01:12.360><c> you</c><00:01:12.439><c> can</c><00:01:12.640><c> easily</c><00:01:13.159><c> use</c><00:01:13.439><c> this</c><00:01:13.680><c> as</c><00:01:13.799><c> a</c>

00:01:13.950 --> 00:01:13.960 align:start position:0%
here you can easily use this as a
 

00:01:13.960 --> 00:01:18.190 align:start position:0%
here you can easily use this as a
document<00:01:14.280><c> loader</c><00:01:15.280><c> look</c><00:01:15.439><c> at</c><00:01:15.560><c> the</c><00:01:15.680><c> represented</c>

00:01:18.190 --> 00:01:18.200 align:start position:0%
 
 

00:01:18.200 --> 00:01:21.230 align:start position:0%
 
outputs<00:01:19.200><c> and</c><00:01:19.479><c> then</c><00:01:20.479><c> go</c><00:01:20.640><c> all</c><00:01:20.759><c> the</c><00:01:20.840><c> way</c><00:01:20.960><c> to</c><00:01:21.119><c> the</c>

00:01:21.230 --> 00:01:21.240 align:start position:0%
outputs and then go all the way to the
 

00:01:21.240 --> 00:01:23.590 align:start position:0%
outputs and then go all the way to the
end<00:01:21.840><c> and</c><00:01:22.040><c> ask</c><00:01:22.200><c> a</c><00:01:22.439><c> question</c><00:01:22.920><c> over</c><00:01:23.240><c> certain</c>

00:01:23.590 --> 00:01:23.600 align:start position:0%
end and ask a question over certain
 

00:01:23.600 --> 00:01:25.749 align:start position:0%
end and ask a question over certain
tables<00:01:24.079><c> within</c><00:01:24.320><c> this</c><00:01:24.479><c> document</c><00:01:25.240><c> for</c><00:01:25.400><c> instance</c>

00:01:25.749 --> 00:01:25.759 align:start position:0%
tables within this document for instance
 

00:01:25.759 --> 00:01:27.510 align:start position:0%
tables within this document for instance
what<00:01:25.880><c> is</c><00:01:25.960><c> the</c><00:01:26.159><c> cash</c><00:01:26.400><c> paid</c><00:01:26.600><c> for</c><00:01:26.759><c> income</c><00:01:27.119><c> taxes</c>

00:01:27.510 --> 00:01:27.520 align:start position:0%
what is the cash paid for income taxes
 

00:01:27.520 --> 00:01:30.870 align:start position:0%
what is the cash paid for income taxes
net<00:01:27.680><c> of</c><00:01:27.880><c> refunds</c><00:01:28.840><c> and</c><00:01:29.000><c> then</c><00:01:29.520><c> the</c><00:01:29.920><c> answer</c><00:01:30.280><c> is</c><00:01:30.479><c> 22</c>

00:01:30.870 --> 00:01:30.880 align:start position:0%
net of refunds and then the answer is 22
 

00:01:30.880 --> 00:01:32.670 align:start position:0%
net of refunds and then the answer is 22
in<00:01:30.960><c> the</c><00:01:31.079><c> first</c><00:01:31.280><c> period</c><00:01:31.600><c> and</c><00:01:31.720><c> 41</c><00:01:32.159><c> in</c><00:01:32.280><c> the</c><00:01:32.399><c> second</c>

00:01:32.670 --> 00:01:32.680 align:start position:0%
in the first period and 41 in the second
 

00:01:32.680 --> 00:01:35.230 align:start position:0%
in the first period and 41 in the second
period<00:01:33.360><c> and</c><00:01:33.560><c> that</c><00:01:33.640><c> is</c><00:01:33.920><c> exactly</c><00:01:34.680><c> this</c><00:01:34.880><c> answer</c>

00:01:35.230 --> 00:01:35.240 align:start position:0%
period and that is exactly this answer
 

00:01:35.240 --> 00:01:37.149 align:start position:0%
period and that is exactly this answer
right<00:01:35.439><c> here</c><00:01:35.720><c> 22</c><00:01:36.399><c> and</c>

00:01:37.149 --> 00:01:37.159 align:start position:0%
right here 22 and
 

00:01:37.159 --> 00:01:40.109 align:start position:0%
right here 22 and
41<00:01:38.159><c> and</c><00:01:38.280><c> so</c><00:01:38.479><c> you</c><00:01:38.600><c> can</c><00:01:38.759><c> now</c><00:01:39.000><c> ask</c><00:01:39.280><c> questions</c><00:01:39.720><c> over</c>

00:01:40.109 --> 00:01:40.119 align:start position:0%
41 and so you can now ask questions over
 

00:01:40.119 --> 00:01:42.109 align:start position:0%
41 and so you can now ask questions over
both<00:01:40.520><c> tabular</c><00:01:41.119><c> and</c><00:01:41.280><c> unstructured</c><00:01:41.880><c> data</c>

00:01:42.109 --> 00:01:42.119 align:start position:0%
both tabular and unstructured data
 

00:01:42.119 --> 00:01:44.030 align:start position:0%
both tabular and unstructured data
Within<00:01:42.360><c> These</c><00:01:42.560><c> complex</c><00:01:43.000><c> documents</c><00:01:43.720><c> and</c><00:01:43.840><c> a</c><00:01:43.920><c> lot</c>

00:01:44.030 --> 00:01:44.040 align:start position:0%
Within These complex documents and a lot
 

00:01:44.040 --> 00:01:45.870 align:start position:0%
Within These complex documents and a lot
of<00:01:44.159><c> this</c><00:01:44.280><c> simply</c><00:01:44.600><c> wasn't</c><00:01:45.240><c> possible</c><00:01:45.560><c> before</c>

00:01:45.870 --> 00:01:45.880 align:start position:0%
of this simply wasn't possible before
 

00:01:45.880 --> 00:01:48.709 align:start position:0%
of this simply wasn't possible before
with<00:01:46.079><c> naiv</c><00:01:46.840><c> rag</c><00:01:47.840><c> the</c><00:01:48.000><c> next</c><00:01:48.200><c> piece</c><00:01:48.479><c> really</c>

00:01:48.709 --> 00:01:48.719 align:start position:0%
with naiv rag the next piece really
 

00:01:48.719 --> 00:01:52.149 align:start position:0%
with naiv rag the next piece really
quick<00:01:49.399><c> is</c><00:01:49.719><c> llama</c><00:01:50.119><c> Cloud</c><00:01:50.680><c> which</c><00:01:51.000><c> is</c><00:01:51.320><c> our</c><00:01:51.759><c> manage</c>

00:01:52.149 --> 00:01:52.159 align:start position:0%
quick is llama Cloud which is our manage
 

00:01:52.159 --> 00:01:55.550 align:start position:0%
quick is llama Cloud which is our manage
inje<00:01:52.799><c> and</c><00:01:53.000><c> retrieval</c><00:01:53.520><c> API</c><00:01:54.520><c> you</c><00:01:54.640><c> can</c><00:01:55.079><c> declare</c>

00:01:55.550 --> 00:01:55.560 align:start position:0%
inje and retrieval API you can declare
 

00:01:55.560 --> 00:01:57.630 align:start position:0%
inje and retrieval API you can declare
different<00:01:55.880><c> indexes</c><00:01:56.479><c> and</c><00:01:56.640><c> inje</c><00:01:57.119><c> pipelines</c>

00:01:57.630 --> 00:01:57.640 align:start position:0%
different indexes and inje pipelines
 

00:01:57.640 --> 00:01:58.830 align:start position:0%
different indexes and inje pipelines
into<00:01:57.920><c> these</c>

00:01:58.830 --> 00:01:58.840 align:start position:0%
into these
 

00:01:58.840 --> 00:02:02.630 align:start position:0%
into these
indexes<00:01:59.840><c> let's</c><00:02:00.039><c> click</c><00:02:00.240><c> into</c><00:02:00.960><c> one</c><00:02:01.960><c> you</c><00:02:02.200><c> get</c><00:02:02.439><c> an</c>

00:02:02.630 --> 00:02:02.640 align:start position:0%
indexes let's click into one you get an
 

00:02:02.640 --> 00:02:04.830 align:start position:0%
indexes let's click into one you get an
API<00:02:03.200><c> where</c><00:02:03.320><c> you</c><00:02:03.439><c> can</c><00:02:03.799><c> retrieve</c><00:02:04.280><c> the</c><00:02:04.439><c> relevant</c>

00:02:04.830 --> 00:02:04.840 align:start position:0%
API where you can retrieve the relevant
 

00:02:04.840 --> 00:02:07.069 align:start position:0%
API where you can retrieve the relevant
context<00:02:05.240><c> from</c><00:02:05.439><c> this</c><00:02:05.600><c> API</c><00:02:06.159><c> it</c><00:02:06.320><c> integrates</c><00:02:06.920><c> with</c>

00:02:07.069 --> 00:02:07.079 align:start position:0%
context from this API it integrates with
 

00:02:07.079 --> 00:02:09.109 align:start position:0%
context from this API it integrates with
the<00:02:07.200><c> 40</c><00:02:07.560><c> plus</c><00:02:07.799><c> storage</c><00:02:08.119><c> Integrations</c><00:02:08.840><c> that</c><00:02:08.959><c> we</c>

00:02:09.109 --> 00:02:09.119 align:start position:0%
the 40 plus storage Integrations that we
 

00:02:09.119 --> 00:02:21.229 align:start position:0%
the 40 plus storage Integrations that we
have<00:02:09.280><c> within</c><00:02:09.560><c> L</c><00:02:09.879><c> index</c><00:02:10.879><c> you</c><00:02:11.000><c> can</c><00:02:11.120><c> get</c><00:02:11.239><c> a</c><00:02:11.480><c> live</c>

00:02:21.229 --> 00:02:21.239 align:start position:0%
 
 

00:02:21.239 --> 00:02:23.550 align:start position:0%
 
preview<00:02:22.239><c> and</c><00:02:22.440><c> immediately</c><00:02:22.959><c> visualize</c><00:02:23.440><c> the</c>

00:02:23.550 --> 00:02:23.560 align:start position:0%
preview and immediately visualize the
 

00:02:23.560 --> 00:02:26.350 align:start position:0%
preview and immediately visualize the
retrieve

00:02:26.350 --> 00:02:26.360 align:start position:0%
 
 

00:02:26.360 --> 00:02:28.790 align:start position:0%
 
results<00:02:27.360><c> you</c><00:02:27.480><c> can</c><00:02:27.680><c> look</c><00:02:27.840><c> at</c><00:02:28.000><c> the</c><00:02:28.160><c> files</c><00:02:28.720><c> that</c>

00:02:28.790 --> 00:02:28.800 align:start position:0%
results you can look at the files that
 

00:02:28.800 --> 00:02:30.470 align:start position:0%
results you can look at the files that
you<00:02:28.959><c> have</c><00:02:29.120><c> and</c><00:02:29.280><c> add</c><00:02:29.480><c> new</c><00:02:29.599><c> ones</c>

00:02:30.470 --> 00:02:30.480 align:start position:0%
you have and add new ones
 

00:02:30.480 --> 00:02:32.270 align:start position:0%
you have and add new ones
and<00:02:30.599><c> you</c><00:02:30.680><c> can</c><00:02:30.879><c> edit</c><00:02:31.120><c> the</c><00:02:31.239><c> parameter</c><00:02:31.680><c> settings</c>

00:02:32.270 --> 00:02:32.280 align:start position:0%
and you can edit the parameter settings
 

00:02:32.280 --> 00:02:34.869 align:start position:0%
and you can edit the parameter settings
and<00:02:32.560><c> actually</c><00:02:32.920><c> version</c><00:02:33.360><c> every</c><00:02:33.640><c> update</c><00:02:34.560><c> and</c><00:02:34.720><c> of</c>

00:02:34.869 --> 00:02:34.879 align:start position:0%
and actually version every update and of
 

00:02:34.879 --> 00:02:36.350 align:start position:0%
and actually version every update and of
course<00:02:35.200><c> this</c><00:02:35.360><c> deeply</c><00:02:35.680><c> integrates</c><00:02:36.160><c> with</c><00:02:36.239><c> the</c>

00:02:36.350 --> 00:02:36.360 align:start position:0%
course this deeply integrates with the
 

00:02:36.360 --> 00:02:38.470 align:start position:0%
course this deeply integrates with the
open<00:02:36.560><c> source</c><00:02:36.840><c> Library</c><00:02:37.560><c> you</c><00:02:37.720><c> can</c><00:02:38.000><c> look</c><00:02:38.160><c> at</c><00:02:38.360><c> all</c>

00:02:38.470 --> 00:02:38.480 align:start position:0%
open source Library you can look at all
 

00:02:38.480 --> 00:02:39.949 align:start position:0%
open source Library you can look at all
the<00:02:38.640><c> Transformations</c><00:02:39.400><c> like</c><00:02:39.599><c> send</c><00:02:39.800><c> and</c>

00:02:39.949 --> 00:02:39.959 align:start position:0%
the Transformations like send and
 

00:02:39.959 --> 00:02:42.350 align:start position:0%
the Transformations like send and
splitting<00:02:40.480><c> the</c><00:02:40.599><c> embeding</c><00:02:41.000><c> model</c><00:02:41.959><c> look</c><00:02:42.120><c> at</c><00:02:42.239><c> all</c>

00:02:42.350 --> 00:02:42.360 align:start position:0%
splitting the embeding model look at all
 

00:02:42.360 --> 00:02:43.670 align:start position:0%
splitting the embeding model look at all
the<00:02:42.480><c> retrieval</c>

00:02:43.670 --> 00:02:43.680 align:start position:0%
the retrieval
 

00:02:43.680 --> 00:02:46.630 align:start position:0%
the retrieval
parameters<00:02:44.680><c> and</c><00:02:44.840><c> prompt</c><00:02:45.360><c> templates</c><00:02:46.360><c> and</c><00:02:46.519><c> of</c>

00:02:46.630 --> 00:02:46.640 align:start position:0%
parameters and prompt templates and of
 

00:02:46.640 --> 00:02:48.350 align:start position:0%
parameters and prompt templates and of
course<00:02:47.080><c> the</c><00:02:47.239><c> evaluation</c>

00:02:48.350 --> 00:02:48.360 align:start position:0%
course the evaluation
 

00:02:48.360 --> 00:02:51.509 align:start position:0%
course the evaluation
modules<00:02:49.480><c> so</c><00:02:50.480><c> please</c><00:02:50.720><c> feel</c><00:02:50.879><c> free</c><00:02:51.080><c> to</c><00:02:51.200><c> check</c><00:02:51.360><c> out</c>

00:02:51.509 --> 00:02:51.519 align:start position:0%
modules so please feel free to check out
 

00:02:51.519 --> 00:02:53.470 align:start position:0%
modules so please feel free to check out
the<00:02:51.640><c> blog</c><00:02:51.920><c> post</c><00:02:52.360><c> we'll</c><00:02:52.640><c> have</c><00:02:53.040><c> much</c><00:02:53.239><c> more</c>

00:02:53.470 --> 00:02:53.480 align:start position:0%
the blog post we'll have much more
 

00:02:53.480 --> 00:02:55.550 align:start position:0%
the blog post we'll have much more
in-depth<00:02:53.920><c> videos</c><00:02:54.280><c> exploring</c><00:02:54.680><c> llama</c><00:02:55.000><c> person</c>

00:02:55.550 --> 00:02:55.560 align:start position:0%
in-depth videos exploring llama person
 

00:02:55.560 --> 00:02:58.190 align:start position:0%
in-depth videos exploring llama person
llama<00:02:55.879><c> Cloud</c><00:02:56.360><c> uh</c><00:02:56.480><c> coming</c><00:02:56.720><c> up</c><00:02:56.840><c> shortly</c><00:02:57.599><c> and</c>

00:02:58.190 --> 00:02:58.200 align:start position:0%
llama Cloud uh coming up shortly and
 

00:02:58.200 --> 00:03:01.760 align:start position:0%
llama Cloud uh coming up shortly and
stay<00:02:58.760><c> tuned</c>

