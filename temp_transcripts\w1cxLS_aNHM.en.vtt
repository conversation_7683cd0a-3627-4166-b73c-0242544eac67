WEBVTT
Kind: captions
Language: en

00:00:00.030 --> 00:00:02.659 align:start position:0%
 
-<00:00:00.149><c> intercom</c><00:00:00.510><c> tutorial</c><00:00:01.380><c> series</c><00:00:01.620><c> this</c><00:00:02.010><c> is</c><00:00:02.070><c> part</c>

00:00:02.659 --> 00:00:02.669 align:start position:0%
- intercom tutorial series this is part
 

00:00:02.669 --> 00:00:04.820 align:start position:0%
- intercom tutorial series this is part
number<00:00:02.730><c> three</c><00:00:03.360><c> and</c><00:00:03.720><c> in</c><00:00:04.230><c> this</c><00:00:04.319><c> one</c><00:00:04.560><c> we're</c><00:00:04.740><c> gonna</c>

00:00:04.820 --> 00:00:04.830 align:start position:0%
number three and in this one we're gonna
 

00:00:04.830 --> 00:00:07.220 align:start position:0%
number three and in this one we're gonna
actually<00:00:05.130><c> import</c><00:00:05.910><c> our</c><00:00:06.120><c> user</c><00:00:06.420><c> data</c><00:00:06.569><c> that</c><00:00:07.109><c> we've</c>

00:00:07.220 --> 00:00:07.230 align:start position:0%
actually import our user data that we've
 

00:00:07.230 --> 00:00:09.589 align:start position:0%
actually import our user data that we've
taken<00:00:07.529><c> from</c><00:00:07.740><c> the</c><00:00:07.859><c> meta</c><00:00:08.040><c> base</c><00:00:08.280><c> in</c><00:00:09.000><c> the</c><00:00:09.179><c> previous</c>

00:00:09.589 --> 00:00:09.599 align:start position:0%
taken from the meta base in the previous
 

00:00:09.599 --> 00:00:12.290 align:start position:0%
taken from the meta base in the previous
video<00:00:09.990><c> all</c><00:00:10.860><c> we</c><00:00:11.070><c> need</c><00:00:11.219><c> is</c><00:00:11.309><c> just</c><00:00:11.370><c> a</c><00:00:11.580><c> CSV</c><00:00:12.030><c> file</c>

00:00:12.290 --> 00:00:12.300 align:start position:0%
video all we need is just a CSV file
 

00:00:12.300 --> 00:00:14.780 align:start position:0%
video all we need is just a CSV file
with<00:00:12.540><c> all</c><00:00:12.690><c> of</c><00:00:12.809><c> our</c><00:00:12.900><c> client</c><00:00:13.349><c> data</c><00:00:13.590><c> and</c><00:00:13.920><c> we're</c>

00:00:14.780 --> 00:00:14.790 align:start position:0%
with all of our client data and we're
 

00:00:14.790 --> 00:00:16.640 align:start position:0%
with all of our client data and we're
gonna<00:00:14.880><c> go</c><00:00:15.120><c> through</c><00:00:15.299><c> a</c><00:00:15.389><c> quick</c><00:00:15.650><c> questionnaire</c>

00:00:16.640 --> 00:00:16.650 align:start position:0%
gonna go through a quick questionnaire
 

00:00:16.650 --> 00:00:19.189 align:start position:0%
gonna go through a quick questionnaire
really<00:00:17.340><c> simple</c><00:00:17.760><c> and</c><00:00:18.029><c> it</c><00:00:18.750><c> says</c><00:00:18.930><c> we're</c><00:00:19.080><c> gonna</c>

00:00:19.189 --> 00:00:19.199 align:start position:0%
really simple and it says we're gonna
 

00:00:19.199 --> 00:00:21.740 align:start position:0%
really simple and it says we're gonna
import<00:00:19.560><c> our</c><00:00:19.680><c> users</c><00:00:20.070><c> and</c><00:00:20.460><c> select</c><00:00:21.270><c> a</c><00:00:21.539><c> file</c><00:00:21.720><c> from</c>

00:00:21.740 --> 00:00:21.750 align:start position:0%
import our users and select a file from
 

00:00:21.750 --> 00:00:25.040 align:start position:0%
import our users and select a file from
your<00:00:21.990><c> computer</c><00:00:22.519><c> and</c><00:00:23.519><c> we're</c><00:00:24.210><c> gonna</c><00:00:24.330><c> take</c><00:00:24.630><c> what</c>

00:00:25.040 --> 00:00:25.050 align:start position:0%
your computer and we're gonna take what
 

00:00:25.050 --> 00:00:28.580 align:start position:0%
your computer and we're gonna take what
we<00:00:25.199><c> had</c><00:00:25.380><c> here</c><00:00:25.740><c> and</c><00:00:26.670><c> we</c><00:00:26.970><c> press</c><00:00:27.180><c> upload</c><00:00:27.590><c> pretty</c>

00:00:28.580 --> 00:00:28.590 align:start position:0%
we had here and we press upload pretty
 

00:00:28.590 --> 00:00:31.820 align:start position:0%
we had here and we press upload pretty
simple<00:00:29.010><c> so</c><00:00:29.160><c> far</c><00:00:29.779><c> and</c><00:00:30.779><c> it's</c><00:00:31.080><c> asked</c><00:00:31.619><c> us</c><00:00:31.679><c> a</c>

00:00:31.820 --> 00:00:31.830 align:start position:0%
simple so far and it's asked us a
 

00:00:31.830 --> 00:00:33.530 align:start position:0%
simple so far and it's asked us a
question<00:00:31.859><c> on</c><00:00:32.369><c> the</c><00:00:32.460><c> top</c><00:00:32.669><c> is</c><00:00:32.820><c> that</c><00:00:32.940><c> which</c><00:00:33.180><c> column</c>

00:00:33.530 --> 00:00:33.540 align:start position:0%
question on the top is that which column
 

00:00:33.540 --> 00:00:35.840 align:start position:0%
question on the top is that which column
contains<00:00:33.960><c> the</c><00:00:34.170><c> emails</c><00:00:34.620><c> of</c><00:00:34.829><c> your</c><00:00:35.040><c> user</c><00:00:35.340><c> stone</c>

00:00:35.840 --> 00:00:35.850 align:start position:0%
contains the emails of your user stone
 

00:00:35.850 --> 00:00:38.569 align:start position:0%
contains the emails of your user stone
that<00:00:36.540><c> one</c><00:00:36.840><c> is</c><00:00:37.079><c> this</c><00:00:37.469><c> column</c><00:00:37.770><c> here</c><00:00:38.040><c> select</c><00:00:38.280><c> this</c>

00:00:38.569 --> 00:00:38.579 align:start position:0%
that one is this column here select this
 

00:00:38.579 --> 00:00:42.950 align:start position:0%
that one is this column here select this
column<00:00:38.820><c> column</c><00:00:39.360><c> four</c><00:00:40.910><c> yes</c><00:00:41.910><c> that</c><00:00:42.480><c> is</c><00:00:42.660><c> the</c><00:00:42.780><c> email</c>

00:00:42.950 --> 00:00:42.960 align:start position:0%
column column four yes that is the email
 

00:00:42.960 --> 00:00:46.430 align:start position:0%
column column four yes that is the email
column<00:00:43.410><c> with</c><00:00:43.710><c> a</c><00:00:43.820><c> data</c><00:00:44.820><c> type</c><00:00:45.090><c> of</c><00:00:45.329><c> text</c><00:00:45.719><c> confirm</c>

00:00:46.430 --> 00:00:46.440 align:start position:0%
column with a data type of text confirm
 

00:00:46.440 --> 00:00:48.979 align:start position:0%
column with a data type of text confirm
which<00:00:47.250><c> column</c><00:00:47.550><c> contains</c><00:00:47.850><c> your</c><00:00:47.969><c> user</c><00:00:48.180><c> IDs</c><00:00:48.690><c> and</c>

00:00:48.979 --> 00:00:48.989 align:start position:0%
which column contains your user IDs and
 

00:00:48.989 --> 00:00:51.130 align:start position:0%
which column contains your user IDs and
that's<00:00:49.140><c> this</c><00:00:49.379><c> one</c><00:00:49.559><c> over</c><00:00:49.739><c> here</c><00:00:49.890><c> the</c><00:00:50.039><c> client</c><00:00:50.280><c> ID</c>

00:00:51.130 --> 00:00:51.140 align:start position:0%
that's this one over here the client ID
 

00:00:51.140 --> 00:00:54.380 align:start position:0%
that's this one over here the client ID
okay<00:00:52.140><c> so</c><00:00:52.199><c> that</c><00:00:52.500><c> this</c><00:00:52.680><c> column</c><00:00:53.129><c> which</c><00:00:53.460><c> is</c><00:00:53.730><c> texts</c>

00:00:54.380 --> 00:00:54.390 align:start position:0%
okay so that this column which is texts
 

00:00:54.390 --> 00:00:57.529 align:start position:0%
okay so that this column which is texts
okay<00:00:54.989><c> user</c><00:00:55.379><c> ID</c><00:00:55.620><c> confirm</c><00:00:56.100><c> which</c><00:00:57.090><c> one</c><00:00:57.270><c> contains</c>

00:00:57.529 --> 00:00:57.539 align:start position:0%
okay user ID confirm which one contains
 

00:00:57.539 --> 00:01:00.080 align:start position:0%
okay user ID confirm which one contains
the<00:00:57.719><c> phone</c><00:00:57.989><c> names</c><00:00:58.289><c> of</c><00:00:58.620><c> your</c><00:00:58.770><c> users</c><00:00:59.190><c> okay</c>

00:01:00.080 --> 00:01:00.090 align:start position:0%
the phone names of your users okay
 

00:01:00.090 --> 00:01:03.799 align:start position:0%
the phone names of your users okay
phone<00:01:01.050><c> names</c><00:01:01.289><c> of</c><00:01:01.469><c> your</c><00:01:01.620><c> users</c><00:01:02.420><c> client</c><00:01:03.420><c> name</c>

00:01:03.799 --> 00:01:03.809 align:start position:0%
phone names of your users client name
 

00:01:03.809 --> 00:01:08.630 align:start position:0%
phone names of your users client name
there<00:01:04.439><c> it</c><00:01:04.530><c> is</c><00:01:05.840><c> okay</c><00:01:06.840><c> we</c><00:01:07.229><c> haven't</c><00:01:07.439><c> passed</c><00:01:08.220><c> that</c>

00:01:08.630 --> 00:01:08.640 align:start position:0%
there it is okay we haven't passed that
 

00:01:08.640 --> 00:01:10.460 align:start position:0%
there it is okay we haven't passed that
of<00:01:08.909><c> clients</c><00:01:09.390><c> but</c><00:01:09.540><c> hopefully</c><00:01:09.900><c> we</c><00:01:10.049><c> will</c><00:01:10.229><c> in</c><00:01:10.409><c> the</c>

00:01:10.460 --> 00:01:10.470 align:start position:0%
of clients but hopefully we will in the
 

00:01:10.470 --> 00:01:12.980 align:start position:0%
of clients but hopefully we will in the
future<00:01:10.500><c> confirm</c><00:01:11.430><c> that</c><00:01:12.270><c> just</c><00:01:12.479><c> means</c><00:01:12.659><c> we</c><00:01:12.780><c> can't</c>

00:01:12.980 --> 00:01:12.990 align:start position:0%
future confirm that just means we can't
 

00:01:12.990 --> 00:01:14.810 align:start position:0%
future confirm that just means we can't
use<00:01:13.080><c> that</c><00:01:13.439><c> variable</c><00:01:13.830><c> within</c><00:01:14.400><c> all</c><00:01:14.549><c> of</c><00:01:14.580><c> our</c>

00:01:14.810 --> 00:01:14.820 align:start position:0%
use that variable within all of our
 

00:01:14.820 --> 00:01:18.289 align:start position:0%
use that variable within all of our
campaigns<00:01:15.920><c> okay</c><00:01:16.950><c> and</c><00:01:17.250><c> we</c><00:01:17.670><c> found</c><00:01:17.850><c> contains</c><00:01:18.210><c> the</c>

00:01:18.289 --> 00:01:18.299 align:start position:0%
campaigns okay and we found contains the
 

00:01:18.299 --> 00:01:21.109 align:start position:0%
campaigns okay and we found contains the
sign<00:01:18.570><c> updates</c><00:01:18.960><c> yes</c><00:01:19.650><c> select</c><00:01:20.369><c> this</c><00:01:20.490><c> column</c><00:01:20.700><c> sign</c>

00:01:21.109 --> 00:01:21.119 align:start position:0%
sign updates yes select this column sign
 

00:01:21.119 --> 00:01:22.640 align:start position:0%
sign updates yes select this column sign
updates<00:01:21.479><c> of</c><00:01:21.600><c> your</c><00:01:21.630><c> users</c><00:01:21.990><c> some</c><00:01:22.229><c> of</c><00:01:22.320><c> them</c><00:01:22.439><c> do</c><00:01:22.619><c> I</c>

00:01:22.640 --> 00:01:22.650 align:start position:0%
updates of your users some of them do I
 

00:01:22.650 --> 00:01:23.660 align:start position:0%
updates of your users some of them do I
have<00:01:22.860><c> a</c><00:01:22.890><c> date</c>

00:01:23.660 --> 00:01:23.670 align:start position:0%
have a date
 

00:01:23.670 --> 00:01:27.020 align:start position:0%
have a date
as<00:01:24.509><c> you</c><00:01:24.750><c> can</c><00:01:24.900><c> see</c><00:01:25.250><c> which</c><00:01:26.250><c> contains</c><00:01:26.820><c> their</c>

00:01:27.020 --> 00:01:27.030 align:start position:0%
as you can see which contains their
 

00:01:27.030 --> 00:01:30.319 align:start position:0%
as you can see which contains their
phone<00:01:27.270><c> numbers</c><00:01:28.549><c> skip</c><00:01:29.549><c> this</c><00:01:29.729><c> step</c><00:01:29.939><c> because</c><00:01:30.090><c> we</c>

00:01:30.319 --> 00:01:30.329 align:start position:0%
phone numbers skip this step because we
 

00:01:30.329 --> 00:01:34.039 align:start position:0%
phone numbers skip this step because we
don't<00:01:30.509><c> have</c><00:01:30.570><c> that</c><00:01:30.770><c> and</c><00:01:32.750><c> additional</c><00:01:33.750><c> columns</c>

00:01:34.039 --> 00:01:34.049 align:start position:0%
don't have that and additional columns
 

00:01:34.049 --> 00:01:36.100 align:start position:0%
don't have that and additional columns
will<00:01:34.200><c> be</c><00:01:34.320><c> imported</c><00:01:34.710><c> as</c><00:01:34.799><c> custom</c><00:01:35.280><c> attributes</c><00:01:35.790><c> in</c>

00:01:36.100 --> 00:01:36.110 align:start position:0%
will be imported as custom attributes in
 

00:01:36.110 --> 00:01:38.630 align:start position:0%
will be imported as custom attributes in
intercom<00:01:37.110><c> select</c><00:01:37.560><c> intercom</c><00:01:38.100><c> select</c><00:01:38.520><c> this</c>

00:01:38.630 --> 00:01:38.640 align:start position:0%
intercom select intercom select this
 

00:01:38.640 --> 00:01:40.969 align:start position:0%
intercom select intercom select this
column<00:01:39.140><c> custom</c><00:01:40.140><c> data</c><00:01:40.380><c> edge</c><00:01:40.590><c> should</c><00:01:40.829><c> you</c>

00:01:40.969 --> 00:01:40.979 align:start position:0%
column custom data edge should you
 

00:01:40.979 --> 00:01:43.310 align:start position:0%
column custom data edge should you
create<00:01:41.340><c> a</c><00:01:41.400><c> new</c><00:01:41.610><c> custom</c><00:01:42.030><c> attribute</c><00:01:42.479><c> by</c><00:01:43.259><c> an</c>

00:01:43.310 --> 00:01:43.320 align:start position:0%
create a new custom attribute by an
 

00:01:43.320 --> 00:01:46.490 align:start position:0%
create a new custom attribute by an
analytics<00:01:43.890><c> code</c><00:01:44.159><c> which</c><00:01:44.520><c> is</c><00:01:44.790><c> a</c><00:01:45.390><c> text</c><00:01:46.110><c> and</c>

00:01:46.490 --> 00:01:46.500 align:start position:0%
analytics code which is a text and
 

00:01:46.500 --> 00:01:50.060 align:start position:0%
analytics code which is a text and
confirm<00:01:47.189><c> and</c><00:01:48.180><c> client</c><00:01:49.110><c> password</c><00:01:49.590><c> so</c><00:01:49.770><c> that</c><00:01:49.890><c> this</c>

00:01:50.060 --> 00:01:50.070 align:start position:0%
confirm and client password so that this
 

00:01:50.070 --> 00:01:55.010 align:start position:0%
confirm and client password so that this
column<00:01:50.520><c> which</c><00:01:51.360><c> is</c><00:01:53.360><c> get</c><00:01:54.360><c> a</c><00:01:54.390><c> new</c><00:01:54.570><c> custom</c>

00:01:55.010 --> 00:01:55.020 align:start position:0%
column which is get a new custom
 

00:01:55.020 --> 00:01:56.480 align:start position:0%
column which is get a new custom
attribute<00:01:55.439><c> oh</c><00:01:55.560><c> you</c><00:01:55.770><c> know</c><00:01:55.890><c> what</c><00:01:56.040><c> could</c><00:01:56.340><c> be</c>

00:01:56.480 --> 00:01:56.490 align:start position:0%
attribute oh you know what could be
 

00:01:56.490 --> 00:01:58.819 align:start position:0%
attribute oh you know what could be
helpful<00:01:56.909><c> if</c><00:01:57.030><c> clients</c><00:01:57.479><c> want</c><00:01:57.869><c> to</c><00:01:58.350><c> reset</c><00:01:58.680><c> their</c>

00:01:58.819 --> 00:01:58.829 align:start position:0%
helpful if clients want to reset their
 

00:01:58.829 --> 00:02:00.800 align:start position:0%
helpful if clients want to reset their
password<00:01:59.280><c> or</c><00:01:59.460><c> find</c><00:01:59.850><c> that</c><00:02:00.000><c> get</c><00:02:00.299><c> their</c><00:02:00.420><c> password</c>

00:02:00.800 --> 00:02:00.810 align:start position:0%
password or find that get their password
 

00:02:00.810 --> 00:02:06.020 align:start position:0%
password or find that get their password
back<00:02:00.990><c> get</c><00:02:01.770><c> a</c><00:02:01.799><c> new</c><00:02:02.040><c> custom</c><00:02:02.430><c> attribute</c><00:02:03.770><c> yes</c><00:02:05.030><c> and</c>

00:02:06.020 --> 00:02:06.030 align:start position:0%
back get a new custom attribute yes and
 

00:02:06.030 --> 00:02:08.540 align:start position:0%
back get a new custom attribute yes and
I<00:02:06.390><c> was</c><00:02:06.509><c> gonna</c><00:02:06.659><c> be</c><00:02:06.930><c> text</c><00:02:07.469><c> client</c><00:02:07.890><c> password</c><00:02:08.369><c> and</c>

00:02:08.540 --> 00:02:08.550 align:start position:0%
I was gonna be text client password and
 

00:02:08.550 --> 00:02:11.580 align:start position:0%
I was gonna be text client password and
confirm<00:02:09.709><c> okay</c>

00:02:11.580 --> 00:02:11.590 align:start position:0%
confirm okay
 

00:02:11.590 --> 00:02:14.800 align:start position:0%
confirm okay
and<00:02:12.590><c> we</c><00:02:13.010><c> are</c><00:02:13.099><c> going</c><00:02:13.280><c> to</c><00:02:13.340><c> import</c><00:02:13.700><c> our</c><00:02:13.849><c> users</c><00:02:14.269><c> and</c>

00:02:14.800 --> 00:02:14.810 align:start position:0%
and we are going to import our users and
 

00:02:14.810 --> 00:02:20.050 align:start position:0%
and we are going to import our users and
we<00:02:14.870><c> are</c><00:02:15.140><c> gonna</c><00:02:15.319><c> go</c><00:02:15.500><c> to</c><00:02:15.530><c> imports</c><00:02:16.690><c> that</c><00:02:17.690><c> it</c><00:02:19.060><c> use</c>

00:02:20.050 --> 00:02:20.060 align:start position:0%
we are gonna go to imports that it use
 

00:02:20.060 --> 00:02:24.849 align:start position:0%
we are gonna go to imports that it use
that<00:02:20.269><c> that's</c><00:02:20.420><c> all</c><00:02:20.599><c> we</c><00:02:20.629><c> need</c><00:02:20.900><c> to</c><00:02:21.110><c> do</c><00:02:21.849><c> okay</c>

