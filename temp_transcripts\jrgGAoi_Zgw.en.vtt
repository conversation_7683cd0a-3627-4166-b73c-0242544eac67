WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.270 align:start position:0%
 
all<00:00:00.199><c> right</c><00:00:00.440><c> we're</c><00:00:00.599><c> going</c><00:00:00.680><c> to</c><00:00:00.760><c> jump</c><00:00:01.000><c> right</c><00:00:01.120><c> into</c>

00:00:01.270 --> 00:00:01.280 align:start position:0%
all right we're going to jump right into
 

00:00:01.280 --> 00:00:04.430 align:start position:0%
all right we're going to jump right into
this<00:00:01.439><c> video</c><00:00:01.760><c> this</c><00:00:01.839><c> is</c><00:00:02.080><c> day</c><00:00:02.320><c> 30</c><00:00:02.800><c> of</c><00:00:03.080><c> 31</c><00:00:04.000><c> almost</c>

00:00:04.430 --> 00:00:04.440 align:start position:0%
this video this is day 30 of 31 almost
 

00:00:04.440 --> 00:00:05.869 align:start position:0%
this video this is day 30 of 31 almost
there<00:00:05.000><c> and</c><00:00:05.120><c> in</c><00:00:05.240><c> this</c><00:00:05.400><c> video</c><00:00:05.600><c> I'm</c><00:00:05.680><c> going</c><00:00:05.799><c> to</c>

00:00:05.869 --> 00:00:05.879 align:start position:0%
there and in this video I'm going to
 

00:00:05.879 --> 00:00:07.590 align:start position:0%
there and in this video I'm going to
show<00:00:06.000><c> you</c><00:00:06.160><c> how</c><00:00:06.279><c> to</c><00:00:06.480><c> retrieve</c><00:00:07.000><c> data</c><00:00:07.399><c> from</c>

00:00:07.590 --> 00:00:07.600 align:start position:0%
show you how to retrieve data from
 

00:00:07.600 --> 00:00:10.230 align:start position:0%
show you how to retrieve data from
Wikipedia<00:00:08.559><c> load</c><00:00:08.840><c> it</c><00:00:08.960><c> into</c><00:00:09.120><c> a</c><00:00:09.240><c> vector</c><00:00:09.599><c> database</c>

00:00:10.230 --> 00:00:10.240 align:start position:0%
Wikipedia load it into a vector database
 

00:00:10.240 --> 00:00:12.350 align:start position:0%
Wikipedia load it into a vector database
and<00:00:10.360><c> then</c><00:00:10.599><c> have</c><00:00:10.920><c> a</c><00:00:11.240><c> local</c><00:00:11.599><c> server</c><00:00:11.960><c> running</c><00:00:12.240><c> an</c>

00:00:12.350 --> 00:00:12.360 align:start position:0%
and then have a local server running an
 

00:00:12.360 --> 00:00:14.629 align:start position:0%
and then have a local server running an
open<00:00:12.599><c> source</c><00:00:12.840><c> llm</c><00:00:13.599><c> ask</c><00:00:13.799><c> it</c><00:00:13.920><c> a</c><00:00:14.080><c> question</c><00:00:14.400><c> so</c>

00:00:14.629 --> 00:00:14.639 align:start position:0%
open source llm ask it a question so
 

00:00:14.639 --> 00:00:17.070 align:start position:0%
open source llm ask it a question so
here<00:00:14.960><c> I</c><00:00:15.280><c> also</c><00:00:15.519><c> have</c><00:00:15.679><c> a</c><00:00:15.759><c> you</c><00:00:16.080><c> streamlet</c><00:00:16.480><c> UI</c><00:00:16.880><c> to</c>

00:00:17.070 --> 00:00:17.080 align:start position:0%
here I also have a you streamlet UI to
 

00:00:17.080 --> 00:00:19.310 align:start position:0%
here I also have a you streamlet UI to
run<00:00:17.320><c> this</c><00:00:17.720><c> so</c><00:00:17.960><c> the</c><00:00:18.080><c> wi</c><00:00:18.400><c> search</c><00:00:18.760><c> I'll</c><00:00:18.960><c> just</c><00:00:19.119><c> have</c>

00:00:19.310 --> 00:00:19.320 align:start position:0%
run this so the wi search I'll just have
 

00:00:19.320 --> 00:00:21.390 align:start position:0%
run this so the wi search I'll just have
say<00:00:19.560><c> sam</c><00:00:19.840><c> timman</c><00:00:20.600><c> the</c><00:00:20.720><c> question</c><00:00:21.000><c> is</c><00:00:21.119><c> to</c><00:00:21.279><c> give</c>

00:00:21.390 --> 00:00:21.400 align:start position:0%
say sam timman the question is to give
 

00:00:21.400 --> 00:00:23.230 align:start position:0%
say sam timman the question is to give
me<00:00:21.519><c> a</c><00:00:21.640><c> summary</c><00:00:22.039><c> of</c><00:00:22.160><c> Sam</c><00:00:22.439><c> timman</c><00:00:22.960><c> and</c><00:00:23.039><c> then</c><00:00:23.160><c> you</c>

00:00:23.230 --> 00:00:23.240 align:start position:0%
me a summary of Sam timman and then you
 

00:00:23.240 --> 00:00:25.109 align:start position:0%
me a summary of Sam timman and then you
just<00:00:23.359><c> click</c><00:00:23.560><c> this</c><00:00:23.680><c> button</c><00:00:23.880><c> to</c><00:00:24.039><c> retrieve</c><00:00:24.680><c> wiked</c>

00:00:25.109 --> 00:00:25.119 align:start position:0%
just click this button to retrieve wiked
 

00:00:25.119 --> 00:00:26.990 align:start position:0%
just click this button to retrieve wiked
data<00:00:25.359><c> and</c><00:00:25.519><c> ask</c><00:00:25.760><c> away</c><00:00:26.240><c> and</c><00:00:26.400><c> from</c><00:00:26.519><c> the</c><00:00:26.640><c> data</c><00:00:26.880><c> I</c>

00:00:26.990 --> 00:00:27.000 align:start position:0%
data and ask away and from the data I
 

00:00:27.000 --> 00:00:29.509 align:start position:0%
data and ask away and from the data I
got<00:00:27.279><c> from</c><00:00:27.720><c> Wikipedia</c><00:00:28.720><c> it</c><00:00:28.880><c> gives</c><00:00:29.080><c> me</c><00:00:29.400><c> the</c>

00:00:29.509 --> 00:00:29.519 align:start position:0%
got from Wikipedia it gives me the
 

00:00:29.519 --> 00:00:32.069 align:start position:0%
got from Wikipedia it gives me the
summary<00:00:30.240><c> of</c><00:00:30.439><c> Sam</c><00:00:30.800><c> ultimate</c><00:00:31.400><c> now</c><00:00:31.759><c> let's</c><00:00:31.920><c> see</c>

00:00:32.069 --> 00:00:32.079 align:start position:0%
summary of Sam ultimate now let's see
 

00:00:32.079 --> 00:00:33.470 align:start position:0%
summary of Sam ultimate now let's see
how<00:00:32.160><c> I</c><00:00:32.279><c> did</c><00:00:32.439><c> this</c><00:00:32.719><c> well</c><00:00:32.840><c> the</c><00:00:32.960><c> first</c><00:00:33.120><c> thing</c><00:00:33.320><c> I</c>

00:00:33.470 --> 00:00:33.480 align:start position:0%
how I did this well the first thing I
 

00:00:33.480 --> 00:00:36.350 align:start position:0%
how I did this well the first thing I
did<00:00:33.840><c> was</c><00:00:34.079><c> I</c><00:00:34.200><c> run</c><00:00:34.559><c> LM</c><00:00:34.920><c> Studio</c><00:00:35.800><c> I</c><00:00:36.000><c> on</c><00:00:36.160><c> the</c>

00:00:36.350 --> 00:00:36.360 align:start position:0%
did was I run LM Studio I on the
 

00:00:36.360 --> 00:00:39.110 align:start position:0%
did was I run LM Studio I on the
homepage<00:00:37.239><c> downloaded</c><00:00:37.760><c> a</c><00:00:38.000><c> F2</c><00:00:38.440><c> model</c><00:00:38.840><c> because</c>

00:00:39.110 --> 00:00:39.120 align:start position:0%
homepage downloaded a F2 model because
 

00:00:39.120 --> 00:00:40.510 align:start position:0%
homepage downloaded a F2 model because
my<00:00:39.239><c> machine</c><00:00:39.520><c> can</c><00:00:39.680><c> handle</c><00:00:39.960><c> that</c><00:00:40.239><c> after</c><00:00:40.399><c> I</c>

00:00:40.510 --> 00:00:40.520 align:start position:0%
my machine can handle that after I
 

00:00:40.520 --> 00:00:42.350 align:start position:0%
my machine can handle that after I
downloaded<00:00:40.960><c> it</c><00:00:41.120><c> I</c><00:00:41.200><c> went</c><00:00:41.360><c> to</c><00:00:41.520><c> the</c><00:00:41.680><c> local</c><00:00:42.000><c> server</c>

00:00:42.350 --> 00:00:42.360 align:start position:0%
downloaded it I went to the local server
 

00:00:42.360 --> 00:00:45.110 align:start position:0%
downloaded it I went to the local server
tab<00:00:42.600><c> on</c><00:00:42.800><c> left</c><00:00:43.039><c> hand</c><00:00:43.280><c> side</c><00:00:43.680><c> loaded</c><00:00:44.480><c> the</c><00:00:44.600><c> f</c><00:00:44.879><c> 2</c>

00:00:45.110 --> 00:00:45.120 align:start position:0%
tab on left hand side loaded the f 2
 

00:00:45.120 --> 00:00:47.350 align:start position:0%
tab on left hand side loaded the f 2
model<00:00:45.559><c> and</c><00:00:45.680><c> then</c><00:00:45.960><c> started</c><00:00:46.280><c> the</c><00:00:46.440><c> server</c><00:00:47.079><c> and</c><00:00:47.160><c> so</c>

00:00:47.350 --> 00:00:47.360 align:start position:0%
model and then started the server and so
 

00:00:47.360 --> 00:00:48.750 align:start position:0%
model and then started the server and so
now<00:00:47.480><c> I</c><00:00:47.559><c> have</c><00:00:47.640><c> a</c><00:00:47.800><c> local</c><00:00:48.039><c> server</c><00:00:48.360><c> running</c><00:00:48.640><c> an</c>

00:00:48.750 --> 00:00:48.760 align:start position:0%
now I have a local server running an
 

00:00:48.760 --> 00:00:51.630 align:start position:0%
now I have a local server running an
open<00:00:49.000><c> source</c><00:00:49.239><c> llm</c><00:00:50.039><c> that</c><00:00:50.160><c> I</c><00:00:50.239><c> can</c><00:00:50.520><c> use</c><00:00:51.120><c> for</c><00:00:51.520><c> the</c>

00:00:51.630 --> 00:00:51.640 align:start position:0%
open source llm that I can use for the
 

00:00:51.640 --> 00:00:53.229 align:start position:0%
open source llm that I can use for the
text<00:00:51.920><c> generation</c><00:00:52.440><c> and</c><00:00:52.559><c> now</c><00:00:52.760><c> as</c><00:00:52.879><c> far</c><00:00:53.000><c> as</c><00:00:53.120><c> the</c>

00:00:53.229 --> 00:00:53.239 align:start position:0%
text generation and now as far as the
 

00:00:53.239 --> 00:00:55.029 align:start position:0%
text generation and now as far as the
code<00:00:53.440><c> goes</c><00:00:53.680><c> I</c><00:00:53.760><c> only</c><00:00:53.960><c> have</c><00:00:54.079><c> one</c><00:00:54.280><c> file</c><00:00:54.600><c> main.</c>

00:00:55.029 --> 00:00:55.039 align:start position:0%
code goes I only have one file main.
 

00:00:55.039 --> 00:00:57.189 align:start position:0%
code goes I only have one file main.
python<00:00:55.359><c> file</c><00:00:55.600><c> which</c><00:00:55.719><c> has</c><00:00:55.879><c> the</c><00:00:56.039><c> autogen</c><00:00:57.039><c> and</c>

00:00:57.189 --> 00:00:57.199 align:start position:0%
python file which has the autogen and
 

00:00:57.199 --> 00:00:58.910 align:start position:0%
python file which has the autogen and
the<00:00:57.320><c> streamlit</c><00:00:57.960><c> all</c><00:00:58.199><c> here</c><00:00:58.440><c> so</c><00:00:58.600><c> there's</c><00:00:58.800><c> quite</c>

00:00:58.910 --> 00:00:58.920 align:start position:0%
the streamlit all here so there's quite
 

00:00:58.920 --> 00:01:01.189 align:start position:0%
the streamlit all here so there's quite
a<00:00:59.000><c> few</c><00:00:59.239><c> Lang</c><00:00:59.519><c> chain</c><00:01:00.280><c> uh</c><00:01:00.399><c> Imports</c><00:01:00.840><c> here</c><00:01:01.039><c> I'll</c>

00:01:01.189 --> 00:01:01.199 align:start position:0%
a few Lang chain uh Imports here I'll
 

00:01:01.199 --> 00:01:02.430 align:start position:0%
a few Lang chain uh Imports here I'll
have<00:01:01.320><c> a</c><00:01:01.480><c> requirement</c><00:01:01.920><c> so</c><00:01:02.079><c> you</c><00:01:02.160><c> don't</c><00:01:02.239><c> have</c><00:01:02.359><c> to</c>

00:01:02.430 --> 00:01:02.440 align:start position:0%
have a requirement so you don't have to
 

00:01:02.440 --> 00:01:03.990 align:start position:0%
have a requirement so you don't have to
worry<00:01:02.640><c> about</c><00:01:02.840><c> that</c><00:01:02.960><c> so</c><00:01:03.079><c> you</c><00:01:03.160><c> can</c><00:01:03.320><c> just</c><00:01:03.480><c> install</c>

00:01:03.990 --> 00:01:04.000 align:start position:0%
worry about that so you can just install
 

00:01:04.000 --> 00:01:05.630 align:start position:0%
worry about that so you can just install
from<00:01:04.199><c> there</c><00:01:04.360><c> so</c><00:01:04.519><c> basically</c><00:01:04.839><c> I</c><00:01:04.920><c> set</c><00:01:05.119><c> the</c><00:01:05.280><c> title</c>

00:01:05.630 --> 00:01:05.640 align:start position:0%
from there so basically I set the title
 

00:01:05.640 --> 00:01:08.070 align:start position:0%
from there so basically I set the title
just<00:01:05.760><c> to</c><00:01:05.920><c> call</c><00:01:06.119><c> wiot</c><00:01:06.720><c> for</c><00:01:07.080><c> the</c><00:01:07.200><c> streamlit</c><00:01:07.720><c> UI</c>

00:01:08.070 --> 00:01:08.080 align:start position:0%
just to call wiot for the streamlit UI
 

00:01:08.080 --> 00:01:10.510 align:start position:0%
just to call wiot for the streamlit UI
so<00:01:08.280><c> with</c><00:01:08.479><c> st.</c><00:01:09.159><c> sidebar</c><00:01:09.560><c> gives</c><00:01:09.720><c> me</c><00:01:09.920><c> a</c><00:01:10.040><c> sidebar</c>

00:01:10.510 --> 00:01:10.520 align:start position:0%
so with st. sidebar gives me a sidebar
 

00:01:10.520 --> 00:01:12.990 align:start position:0%
so with st. sidebar gives me a sidebar
which<00:01:10.640><c> is</c><00:01:10.799><c> where</c><00:01:11.360><c> you</c><00:01:11.799><c> type</c><00:01:12.040><c> in</c><00:01:12.400><c> who</c><00:01:12.720><c> you</c><00:01:12.840><c> want</c>

00:01:12.990 --> 00:01:13.000 align:start position:0%
which is where you type in who you want
 

00:01:13.000 --> 00:01:15.230 align:start position:0%
which is where you type in who you want
to<00:01:13.119><c> search</c><00:01:13.400><c> on</c><00:01:13.560><c> Wikipedia</c><00:01:14.439><c> by</c><00:01:14.560><c> default</c><00:01:14.920><c> I</c><00:01:15.040><c> just</c>

00:01:15.230 --> 00:01:15.240 align:start position:0%
to search on Wikipedia by default I just
 

00:01:15.240 --> 00:01:16.469 align:start position:0%
to search on Wikipedia by default I just
have<00:01:15.360><c> it</c><00:01:15.479><c> Bill</c><00:01:15.680><c> Gates</c><00:01:15.960><c> to</c><00:01:16.040><c> have</c><00:01:16.159><c> some</c>

00:01:16.469 --> 00:01:16.479 align:start position:0%
have it Bill Gates to have some
 

00:01:16.479 --> 00:01:19.390 align:start position:0%
have it Bill Gates to have some
placeholder<00:01:17.479><c> so</c><00:01:17.680><c> if</c><00:01:17.880><c> the</c><00:01:18.000><c> user</c><00:01:18.320><c> input</c><00:01:18.720><c> is</c><00:01:18.960><c> not</c>

00:01:19.390 --> 00:01:19.400 align:start position:0%
placeholder so if the user input is not
 

00:01:19.400 --> 00:01:21.469 align:start position:0%
placeholder so if the user input is not
none<00:01:20.119><c> meaning</c><00:01:20.560><c> this</c><00:01:20.680><c> is</c><00:01:20.880><c> confirmation</c><00:01:21.320><c> that</c>

00:01:21.469 --> 00:01:21.479 align:start position:0%
none meaning this is confirmation that
 

00:01:21.479 --> 00:01:23.390 align:start position:0%
none meaning this is confirmation that
you<00:01:21.680><c> have</c><00:01:21.960><c> something</c><00:01:22.439><c> it</c><00:01:22.560><c> just</c><00:01:22.680><c> says</c><00:01:23.159><c> it</c><00:01:23.240><c> just</c>

00:01:23.390 --> 00:01:23.400 align:start position:0%
you have something it just says it just
 

00:01:23.400 --> 00:01:25.190 align:start position:0%
you have something it just says it just
gives<00:01:23.560><c> you</c><00:01:23.720><c> a</c><00:01:23.840><c> written</c><00:01:24.119><c> message</c><00:01:24.759><c> okay</c><00:01:24.880><c> so</c><00:01:25.079><c> then</c>

00:01:25.190 --> 00:01:25.200 align:start position:0%
gives you a written message okay so then
 

00:01:25.200 --> 00:01:26.950 align:start position:0%
gives you a written message okay so then
I<00:01:25.360><c> have</c><00:01:25.640><c> a</c><00:01:25.880><c> button</c><00:01:26.280><c> that</c><00:01:26.360><c> I</c><00:01:26.439><c> want</c><00:01:26.560><c> to</c><00:01:26.640><c> create</c><00:01:26.880><c> on</c>

00:01:26.950 --> 00:01:26.960 align:start position:0%
I have a button that I want to create on
 

00:01:26.960 --> 00:01:29.030 align:start position:0%
I have a button that I want to create on
the<00:01:27.159><c> sidebar</c><00:01:27.920><c> and</c><00:01:28.040><c> then</c><00:01:28.159><c> I</c><00:01:28.280><c> have</c><00:01:28.400><c> a</c><00:01:28.600><c> question</c>

00:01:29.030 --> 00:01:29.040 align:start position:0%
the sidebar and then I have a question
 

00:01:29.040 --> 00:01:32.069 align:start position:0%
the sidebar and then I have a question
on<00:01:29.280><c> the</c><00:01:29.479><c> main</c><00:01:30.280><c> uh</c><00:01:30.400><c> on</c><00:01:30.520><c> the</c><00:01:30.680><c> main</c><00:01:30.960><c> area</c><00:01:31.759><c> uh</c><00:01:31.920><c> that</c>

00:01:32.069 --> 00:01:32.079 align:start position:0%
on the main uh on the main area uh that
 

00:01:32.079 --> 00:01:34.190 align:start position:0%
on the main uh on the main area uh that
we<00:01:32.240><c> want</c><00:01:32.439><c> to</c><00:01:32.759><c> actually</c><00:01:33.240><c> ask</c><00:01:33.720><c> with</c><00:01:33.840><c> the</c><00:01:33.960><c> vector</c>

00:01:34.190 --> 00:01:34.200 align:start position:0%
we want to actually ask with the vector
 

00:01:34.200 --> 00:01:36.429 align:start position:0%
we want to actually ask with the vector
database<00:01:34.640><c> to</c><00:01:34.880><c> retrieve</c><00:01:35.399><c> information</c><00:01:35.840><c> for</c><00:01:36.040><c> us</c>

00:01:36.429 --> 00:01:36.439 align:start position:0%
database to retrieve information for us
 

00:01:36.439 --> 00:01:38.510 align:start position:0%
database to retrieve information for us
so<00:01:36.640><c> if</c><00:01:36.720><c> you</c><00:01:36.920><c> click</c><00:01:37.200><c> the</c><00:01:37.399><c> button</c><00:01:38.040><c> and</c><00:01:38.320><c> the</c>

00:01:38.510 --> 00:01:38.520 align:start position:0%
so if you click the button and the
 

00:01:38.520 --> 00:01:40.710 align:start position:0%
so if you click the button and the
length<00:01:39.200><c> of</c><00:01:39.520><c> this</c><00:01:39.799><c> question</c><00:01:40.159><c> that</c><00:01:40.280><c> you</c><00:01:40.439><c> have</c><00:01:40.560><c> is</c>

00:01:40.710 --> 00:01:40.720 align:start position:0%
length of this question that you have is
 

00:01:40.720 --> 00:01:42.789 align:start position:0%
length of this question that you have is
greater<00:01:41.000><c> than</c><00:01:41.200><c> zero</c><00:01:42.079><c> then</c><00:01:42.399><c> we're</c><00:01:42.560><c> going</c><00:01:42.640><c> to</c>

00:01:42.789 --> 00:01:42.799 align:start position:0%
greater than zero then we're going to
 

00:01:42.799 --> 00:01:44.749 align:start position:0%
greater than zero then we're going to
start<00:01:43.079><c> loading</c><00:01:43.960><c> um</c><00:01:44.119><c> information</c><00:01:44.600><c> from</c>

00:01:44.749 --> 00:01:44.759 align:start position:0%
start loading um information from
 

00:01:44.759 --> 00:01:46.190 align:start position:0%
start loading um information from
Wikipedia<00:01:45.360><c> so</c><00:01:45.479><c> as</c><00:01:45.560><c> you</c><00:01:45.640><c> can</c><00:01:45.719><c> see</c><00:01:45.920><c> here</c><00:01:46.079><c> there's</c>

00:01:46.190 --> 00:01:46.200 align:start position:0%
Wikipedia so as you can see here there's
 

00:01:46.200 --> 00:01:48.270 align:start position:0%
Wikipedia so as you can see here there's
a<00:01:46.360><c> Wikipedia</c><00:01:46.920><c> loader</c><00:01:47.719><c> where</c><00:01:47.840><c> we</c><00:01:48.000><c> take</c><00:01:48.159><c> the</c>

00:01:48.270 --> 00:01:48.280 align:start position:0%
a Wikipedia loader where we take the
 

00:01:48.280 --> 00:01:50.270 align:start position:0%
a Wikipedia loader where we take the
user<00:01:48.600><c> input</c><00:01:49.280><c> which</c><00:01:49.439><c> is</c><00:01:49.719><c> who</c><00:01:49.880><c> we</c><00:01:50.040><c> want</c><00:01:50.159><c> to</c>

00:01:50.270 --> 00:01:50.280 align:start position:0%
user input which is who we want to
 

00:01:50.280 --> 00:01:53.190 align:start position:0%
user input which is who we want to
search<00:01:50.600><c> for</c><00:01:50.920><c> so</c><00:01:51.200><c> by</c><00:01:51.320><c> default</c><00:01:51.640><c> Bill</c><00:01:51.880><c> Gates</c><00:01:52.759><c> um</c><00:01:53.119><c> I</c>

00:01:53.190 --> 00:01:53.200 align:start position:0%
search for so by default Bill Gates um I
 

00:01:53.200 --> 00:01:55.670 align:start position:0%
search for so by default Bill Gates um I
have<00:01:53.320><c> it</c><00:01:53.479><c> set</c><00:01:53.680><c> to</c><00:01:53.880><c> just</c><00:01:54.079><c> a</c><00:01:54.240><c> load</c><00:01:54.759><c> a</c><00:01:55.079><c> maximum</c><00:01:55.479><c> of</c>

00:01:55.670 --> 00:01:55.680 align:start position:0%
have it set to just a load a maximum of
 

00:01:55.680 --> 00:01:58.069 align:start position:0%
have it set to just a load a maximum of
two<00:01:55.960><c> documents</c><00:01:56.840><c> and</c><00:01:56.960><c> this</c><00:01:57.079><c> is</c><00:01:57.200><c> an</c><00:01:57.399><c> important</c>

00:01:58.069 --> 00:01:58.079 align:start position:0%
two documents and this is an important
 

00:01:58.079 --> 00:02:00.190 align:start position:0%
two documents and this is an important
property<00:01:58.439><c> here</c><00:01:58.640><c> the</c><00:01:58.840><c> doc</c><00:01:59.280><c> content</c><00:01:59.880><c> characters</c>

00:02:00.190 --> 00:02:00.200 align:start position:0%
property here the doc content characters
 

00:02:00.200 --> 00:02:02.590 align:start position:0%
property here the doc content characters
Max<00:02:00.520><c> this</c><00:02:00.600><c> has</c><00:02:00.840><c> changed</c><00:02:01.399><c> name</c><00:02:01.680><c> so</c><00:02:01.880><c> if</c><00:02:02.039><c> you</c><00:02:02.439><c> had</c>

00:02:02.590 --> 00:02:02.600 align:start position:0%
Max this has changed name so if you had
 

00:02:02.600 --> 00:02:04.830 align:start position:0%
Max this has changed name so if you had
tried<00:02:02.880><c> this</c><00:02:03.079><c> like</c><00:02:03.200><c> in</c><00:02:03.280><c> a</c><00:02:03.479><c> previous</c><00:02:03.840><c> version</c><00:02:04.640><c> um</c>

00:02:04.830 --> 00:02:04.840 align:start position:0%
tried this like in a previous version um
 

00:02:04.840 --> 00:02:07.190 align:start position:0%
tried this like in a previous version um
it<00:02:04.920><c> won't</c><00:02:05.159><c> work</c><00:02:05.439><c> because</c><00:02:05.719><c> that</c><00:02:05.920><c> property</c><00:02:06.840><c> um</c>

00:02:07.190 --> 00:02:07.200 align:start position:0%
it won't work because that property um
 

00:02:07.200 --> 00:02:10.229 align:start position:0%
it won't work because that property um
has<00:02:07.520><c> changed</c><00:02:07.960><c> its</c><00:02:08.200><c> name</c><00:02:08.640><c> which</c><00:02:09.200><c> happens</c><00:02:10.039><c> um</c>

00:02:10.229 --> 00:02:10.239 align:start position:0%
has changed its name which happens um
 

00:02:10.239 --> 00:02:12.869 align:start position:0%
has changed its name which happens um
with<00:02:10.440><c> AI</c><00:02:10.879><c> it</c><00:02:11.039><c> set</c><00:02:11.200><c> it</c><00:02:11.280><c> to</c><00:02:11.440><c> 10,000</c><00:02:12.440><c> which</c><00:02:12.560><c> means</c>

00:02:12.869 --> 00:02:12.879 align:start position:0%
with AI it set it to 10,000 which means
 

00:02:12.879 --> 00:02:15.309 align:start position:0%
with AI it set it to 10,000 which means
is<00:02:13.160><c> whenever</c><00:02:13.440><c> it</c><00:02:13.560><c> searches</c><00:02:14.000><c> for</c><00:02:14.519><c> Bill</c><00:02:14.760><c> Gates</c>

00:02:15.309 --> 00:02:15.319 align:start position:0%
is whenever it searches for Bill Gates
 

00:02:15.319 --> 00:02:17.390 align:start position:0%
is whenever it searches for Bill Gates
the<00:02:15.519><c> first</c><00:02:16.080><c> Wikipedia</c><00:02:16.560><c> page</c><00:02:16.760><c> that</c><00:02:16.879><c> it</c><00:02:17.000><c> find</c>

00:02:17.390 --> 00:02:17.400 align:start position:0%
the first Wikipedia page that it find
 

00:02:17.400 --> 00:02:19.309 align:start position:0%
the first Wikipedia page that it find
it's<00:02:17.480><c> going</c><00:02:17.599><c> to</c><00:02:17.760><c> only</c><00:02:17.959><c> retrieve</c><00:02:18.360><c> the</c><00:02:18.480><c> first</c><00:02:19.120><c> by</c>

00:02:19.309 --> 00:02:19.319 align:start position:0%
it's going to only retrieve the first by
 

00:02:19.319 --> 00:02:21.869 align:start position:0%
it's going to only retrieve the first by
default<00:02:19.680><c> I</c><00:02:19.800><c> believe</c><00:02:19.959><c> it's</c><00:02:20.160><c> 4,000</c><00:02:21.120><c> so</c><00:02:21.319><c> I</c><00:02:21.480><c> set</c><00:02:21.720><c> a</c>

00:02:21.869 --> 00:02:21.879 align:start position:0%
default I believe it's 4,000 so I set a
 

00:02:21.879 --> 00:02:23.270 align:start position:0%
default I believe it's 4,000 so I set a
10,000<00:02:22.280><c> so</c><00:02:22.400><c> it</c><00:02:22.440><c> could</c><00:02:22.599><c> retrieve</c><00:02:23.000><c> more</c>

00:02:23.270 --> 00:02:23.280 align:start position:0%
10,000 so it could retrieve more
 

00:02:23.280 --> 00:02:25.430 align:start position:0%
10,000 so it could retrieve more
information<00:02:23.959><c> from</c><00:02:24.400><c> that</c><00:02:24.560><c> first</c><00:02:24.879><c> Wikipedia</c>

00:02:25.430 --> 00:02:25.440 align:start position:0%
information from that first Wikipedia
 

00:02:25.440 --> 00:02:26.990 align:start position:0%
information from that first Wikipedia
page<00:02:25.760><c> and</c><00:02:25.879><c> we're</c><00:02:26.000><c> going</c><00:02:26.080><c> to</c><00:02:26.200><c> load</c><00:02:26.560><c> that</c><00:02:26.879><c> and</c>

00:02:26.990 --> 00:02:27.000 align:start position:0%
page and we're going to load that and
 

00:02:27.000 --> 00:02:28.030 align:start position:0%
page and we're going to load that and
then<00:02:27.080><c> we</c><00:02:27.200><c> have</c><00:02:27.319><c> a</c><00:02:27.440><c> then</c><00:02:27.519><c> I</c><00:02:27.640><c> have</c><00:02:27.720><c> a</c><00:02:27.840><c> text</c>

00:02:28.030 --> 00:02:28.040 align:start position:0%
then we have a then I have a text
 

00:02:28.040 --> 00:02:29.550 align:start position:0%
then we have a then I have a text
splitter<00:02:28.440><c> where</c><00:02:28.599><c> I</c><00:02:28.720><c> want</c><00:02:28.879><c> to</c><00:02:29.040><c> chunk</c><00:02:29.319><c> all</c><00:02:29.440><c> the</c>

00:02:29.550 --> 00:02:29.560 align:start position:0%
splitter where I want to chunk all the
 

00:02:29.560 --> 00:02:31.589 align:start position:0%
splitter where I want to chunk all the
text<00:02:29.959><c> into</c><00:02:30.239><c> 1,000</c><00:02:30.760><c> characters</c><00:02:31.239><c> that</c><00:02:31.400><c> I'm</c>

00:02:31.589 --> 00:02:31.599 align:start position:0%
text into 1,000 characters that I'm
 

00:02:31.599 --> 00:02:33.830 align:start position:0%
text into 1,000 characters that I'm
actually<00:02:32.400><c> splitting</c><00:02:33.280><c> uh</c><00:02:33.360><c> all</c><00:02:33.519><c> of</c><00:02:33.640><c> the</c>

00:02:33.830 --> 00:02:33.840 align:start position:0%
actually splitting uh all of the
 

00:02:33.840 --> 00:02:36.030 align:start position:0%
actually splitting uh all of the
documents<00:02:34.319><c> so</c><00:02:34.519><c> we</c><00:02:34.640><c> have</c><00:02:35.200><c> more</c><00:02:35.519><c> documents</c>

00:02:36.030 --> 00:02:36.040 align:start position:0%
documents so we have more documents
 

00:02:36.040 --> 00:02:38.790 align:start position:0%
documents so we have more documents
which<00:02:36.160><c> is</c><00:02:36.319><c> going</c><00:02:36.480><c> to</c><00:02:36.760><c> allow</c><00:02:37.200><c> for</c><00:02:37.800><c> um</c><00:02:38.080><c> better</c><00:02:38.519><c> V</c>

00:02:38.790 --> 00:02:38.800 align:start position:0%
which is going to allow for um better V
 

00:02:38.800 --> 00:02:40.309 align:start position:0%
which is going to allow for um better V
uh<00:02:38.920><c> similary</c><00:02:39.360><c> searching</c><00:02:39.760><c> so</c><00:02:39.920><c> now</c><00:02:40.000><c> I</c><00:02:40.080><c> have</c><00:02:40.159><c> a</c>

00:02:40.309 --> 00:02:40.319 align:start position:0%
uh similary searching so now I have a
 

00:02:40.319 --> 00:02:43.350 align:start position:0%
uh similary searching so now I have a
docs<00:02:40.640><c> variable</c><00:02:41.480><c> we</c><00:02:41.640><c> created</c><00:02:41.959><c> a</c><00:02:42.120><c> chroma</c><00:02:42.879><c> uh</c><00:02:43.000><c> DB</c>

00:02:43.350 --> 00:02:43.360 align:start position:0%
docs variable we created a chroma uh DB
 

00:02:43.360 --> 00:02:45.710 align:start position:0%
docs variable we created a chroma uh DB
store<00:02:43.760><c> I'm</c><00:02:43.879><c> using</c><00:02:44.239><c> hugging</c><00:02:44.640><c> face</c><00:02:45.000><c> embeddings</c>

00:02:45.710 --> 00:02:45.720 align:start position:0%
store I'm using hugging face embeddings
 

00:02:45.720 --> 00:02:47.430 align:start position:0%
store I'm using hugging face embeddings
then<00:02:45.800><c> you</c><00:02:46.000><c> add</c><00:02:46.159><c> the</c><00:02:46.319><c> documents</c><00:02:46.800><c> to</c><00:02:47.000><c> the</c><00:02:47.159><c> vector</c>

00:02:47.430 --> 00:02:47.440 align:start position:0%
then you add the documents to the vector
 

00:02:47.440 --> 00:02:49.270 align:start position:0%
then you add the documents to the vector
store<00:02:48.000><c> and</c><00:02:48.120><c> then</c><00:02:48.239><c> we</c><00:02:48.360><c> start</c><00:02:48.599><c> our</c><00:02:48.800><c> retrieval</c>

00:02:49.270 --> 00:02:49.280 align:start position:0%
store and then we start our retrieval
 

00:02:49.280 --> 00:02:51.910 align:start position:0%
store and then we start our retrieval
chain<00:02:49.680><c> so</c><00:02:49.840><c> I</c><00:02:49.920><c> know</c><00:02:50.239><c> that</c><00:02:50.800><c> you</c><00:02:50.959><c> see</c><00:02:51.239><c> open</c><00:02:51.519><c> AI</c>

00:02:51.910 --> 00:02:51.920 align:start position:0%
chain so I know that you see open AI
 

00:02:51.920 --> 00:02:54.270 align:start position:0%
chain so I know that you see open AI
here<00:02:52.360><c> this</c><00:02:52.720><c> it</c><00:02:52.840><c> allows</c><00:02:53.159><c> us</c><00:02:53.519><c> is</c><00:02:53.680><c> like</c><00:02:53.760><c> a</c><00:02:53.959><c> wrapper</c>

00:02:54.270 --> 00:02:54.280 align:start position:0%
here this it allows us is like a wrapper
 

00:02:54.280 --> 00:02:57.910 align:start position:0%
here this it allows us is like a wrapper
so<00:02:54.440><c> that</c><00:02:54.599><c> we</c><00:02:54.760><c> can</c><00:02:55.400><c> um</c><00:02:55.800><c> use</c><00:02:56.280><c> a</c><00:02:56.480><c> base</c><00:02:56.800><c> URL</c><00:02:57.640><c> we</c><00:02:57.760><c> can</c>

00:02:57.910 --> 00:02:57.920 align:start position:0%
so that we can um use a base URL we can
 

00:02:57.920 --> 00:03:00.550 align:start position:0%
so that we can um use a base URL we can
use<00:02:58.159><c> a</c><00:02:58.319><c> model</c><00:02:58.840><c> we</c><00:02:58.920><c> can</c><00:02:59.040><c> use</c><00:02:59.239><c> the</c><00:02:59.360><c> API</c><00:03:00.000><c> key</c><00:03:00.400><c> so</c>

00:03:00.550 --> 00:03:00.560 align:start position:0%
use a model we can use the API key so
 

00:03:00.560 --> 00:03:02.550 align:start position:0%
use a model we can use the API key so
that<00:03:00.720><c> if</c><00:03:00.840><c> you</c><00:03:01.120><c> want</c><00:03:01.760><c> if</c><00:03:01.879><c> you</c><00:03:02.040><c> don't</c><00:03:02.280><c> want</c><00:03:02.400><c> to</c>

00:03:02.550 --> 00:03:02.560 align:start position:0%
that if you want if you don't want to
 

00:03:02.560 --> 00:03:06.309 align:start position:0%
that if you want if you don't want to
use<00:03:03.080><c> API</c><00:03:04.080><c> um</c><00:03:04.200><c> open</c><00:03:04.440><c> ai's</c><00:03:04.799><c> API</c><00:03:05.599><c> then</c><00:03:05.879><c> you</c><00:03:06.000><c> can</c><00:03:06.159><c> do</c>

00:03:06.309 --> 00:03:06.319 align:start position:0%
use API um open ai's API then you can do
 

00:03:06.319 --> 00:03:07.589 align:start position:0%
use API um open ai's API then you can do
something<00:03:06.560><c> local</c><00:03:06.799><c> with</c><00:03:06.879><c> LM</c><00:03:07.120><c> studio</c><00:03:07.440><c> and</c><00:03:07.519><c> this</c>

00:03:07.589 --> 00:03:07.599 align:start position:0%
something local with LM studio and this
 

00:03:07.599 --> 00:03:10.270 align:start position:0%
something local with LM studio and this
is<00:03:07.840><c> exactly</c><00:03:08.200><c> what</c><00:03:08.319><c> I'm</c><00:03:08.480><c> doing</c><00:03:08.920><c> here</c><00:03:09.560><c> I'm</c><00:03:09.720><c> using</c>

00:03:10.270 --> 00:03:10.280 align:start position:0%
is exactly what I'm doing here I'm using
 

00:03:10.280 --> 00:03:12.589 align:start position:0%
is exactly what I'm doing here I'm using
I'm<00:03:10.400><c> setting</c><00:03:10.720><c> the</c><00:03:10.879><c> base</c><00:03:11.159><c> URL</c><00:03:11.599><c> to</c><00:03:11.799><c> LM</c><00:03:12.120><c> Studio's</c>

00:03:12.589 --> 00:03:12.599 align:start position:0%
I'm setting the base URL to LM Studio's
 

00:03:12.599 --> 00:03:14.869 align:start position:0%
I'm setting the base URL to LM Studio's
local<00:03:12.959><c> server</c><00:03:13.440><c> URL</c><00:03:14.200><c> and</c><00:03:14.319><c> if</c><00:03:14.400><c> you</c><00:03:14.519><c> were</c><00:03:14.640><c> using</c>

00:03:14.869 --> 00:03:14.879 align:start position:0%
local server URL and if you were using
 

00:03:14.879 --> 00:03:17.070 align:start position:0%
local server URL and if you were using
oama<00:03:15.519><c> then</c><00:03:15.640><c> you</c><00:03:15.720><c> would</c><00:03:15.959><c> also</c><00:03:16.239><c> put</c><00:03:16.480><c> the</c><00:03:16.680><c> model</c>

00:03:17.070 --> 00:03:17.080 align:start position:0%
oama then you would also put the model
 

00:03:17.080 --> 00:03:18.509 align:start position:0%
oama then you would also put the model
property<00:03:17.480><c> here</c><00:03:17.680><c> as</c><00:03:17.799><c> well</c><00:03:18.080><c> cuz</c><00:03:18.200><c> you</c><00:03:18.280><c> need</c><00:03:18.440><c> the</c>

00:03:18.509 --> 00:03:18.519 align:start position:0%
property here as well cuz you need the
 

00:03:18.519 --> 00:03:20.550 align:start position:0%
property here as well cuz you need the
model<00:03:18.720><c> for</c><00:03:18.879><c> olama</c><00:03:19.640><c> we</c><00:03:19.760><c> have</c><00:03:19.840><c> a</c><00:03:19.959><c> retriever</c><00:03:20.400><c> for</c>

00:03:20.550 --> 00:03:20.560 align:start position:0%
model for olama we have a retriever for
 

00:03:20.560 --> 00:03:22.030 align:start position:0%
model for olama we have a retriever for
the<00:03:20.680><c> vector</c><00:03:20.920><c> store</c><00:03:21.400><c> and</c><00:03:21.519><c> then</c><00:03:21.599><c> we</c><00:03:21.720><c> set</c><00:03:21.879><c> our</c>

00:03:22.030 --> 00:03:22.040 align:start position:0%
the vector store and then we set our
 

00:03:22.040 --> 00:03:24.229 align:start position:0%
the vector store and then we set our
memory<00:03:22.319><c> buffer</c><00:03:22.840><c> here</c><00:03:23.080><c> what</c><00:03:23.159><c> is</c><00:03:23.319><c> happening</c><00:03:23.959><c> is</c>

00:03:24.229 --> 00:03:24.239 align:start position:0%
memory buffer here what is happening is
 

00:03:24.239 --> 00:03:26.190 align:start position:0%
memory buffer here what is happening is
we're<00:03:24.480><c> taking</c><00:03:24.799><c> the</c><00:03:25.040><c> response</c><00:03:25.640><c> so</c><00:03:25.840><c> we're</c>

00:03:26.190 --> 00:03:26.200 align:start position:0%
we're taking the response so we're
 

00:03:26.200 --> 00:03:28.229 align:start position:0%
we're taking the response so we're
getting<00:03:26.760><c> the</c><00:03:26.959><c> question</c><00:03:27.280><c> so</c><00:03:27.480><c> this</c><00:03:27.640><c> QA</c><00:03:28.040><c> here</c>

00:03:28.229 --> 00:03:28.239 align:start position:0%
getting the question so this QA here
 

00:03:28.239 --> 00:03:29.509 align:start position:0%
getting the question so this QA here
right<00:03:28.400><c> this</c><00:03:28.560><c> was</c><00:03:28.920><c> this</c><00:03:29.040><c> was</c><00:03:29.159><c> the</c><00:03:29.280><c> question</c>

00:03:29.509 --> 00:03:29.519 align:start position:0%
right this was this was the question
 

00:03:29.519 --> 00:03:30.270 align:start position:0%
right this was this was the question
answer

00:03:30.270 --> 00:03:30.280 align:start position:0%
answer
 

00:03:30.280 --> 00:03:32.429 align:start position:0%
answer
so<00:03:30.439><c> we</c><00:03:30.560><c> want</c><00:03:30.680><c> to</c><00:03:30.760><c> take</c><00:03:30.879><c> the</c><00:03:31.080><c> response</c><00:03:31.799><c> from</c><00:03:32.319><c> uh</c>

00:03:32.429 --> 00:03:32.439 align:start position:0%
so we want to take the response from uh
 

00:03:32.439 --> 00:03:34.030 align:start position:0%
so we want to take the response from uh
the<00:03:32.560><c> question</c><00:03:32.840><c> that</c><00:03:32.920><c> we're</c><00:03:33.080><c> going</c><00:03:33.159><c> to</c><00:03:33.400><c> ask</c><00:03:33.760><c> to</c>

00:03:34.030 --> 00:03:34.040 align:start position:0%
the question that we're going to ask to
 

00:03:34.040 --> 00:03:35.789 align:start position:0%
the question that we're going to ask to
the<00:03:34.200><c> vector</c><00:03:34.519><c> database</c><00:03:35.120><c> and</c><00:03:35.239><c> then</c><00:03:35.400><c> this</c><00:03:35.519><c> is</c>

00:03:35.789 --> 00:03:35.799 align:start position:0%
the vector database and then this is
 

00:03:35.799 --> 00:03:38.229 align:start position:0%
the vector database and then this is
just<00:03:36.000><c> some</c><00:03:36.439><c> Json</c><00:03:37.040><c> to</c><00:03:37.159><c> help</c><00:03:37.400><c> it</c><00:03:37.599><c> like</c><00:03:38.120><c> the</c>

00:03:38.229 --> 00:03:38.239 align:start position:0%
just some Json to help it like the
 

00:03:38.239 --> 00:03:39.630 align:start position:0%
just some Json to help it like the
output<00:03:38.640><c> be</c><00:03:38.760><c> a</c><00:03:38.840><c> little</c><00:03:39.000><c> cleaner</c><00:03:39.400><c> that's</c><00:03:39.519><c> all</c>

00:03:39.630 --> 00:03:39.640 align:start position:0%
output be a little cleaner that's all
 

00:03:39.640 --> 00:03:41.470 align:start position:0%
output be a little cleaner that's all
this<00:03:39.760><c> is</c><00:03:39.840><c> doing</c><00:03:40.040><c> so</c><00:03:40.159><c> the</c><00:03:40.280><c> final</c><00:03:40.560><c> output</c><00:03:40.959><c> so</c><00:03:41.200><c> if</c>

00:03:41.470 --> 00:03:41.480 align:start position:0%
this is doing so the final output so if
 

00:03:41.480 --> 00:03:43.589 align:start position:0%
this is doing so the final output so if
there<00:03:41.640><c> is</c><00:03:41.799><c> a</c><00:03:42.040><c> response</c><00:03:42.799><c> we're</c><00:03:42.959><c> going</c><00:03:43.080><c> to</c><00:03:43.280><c> print</c>

00:03:43.589 --> 00:03:43.599 align:start position:0%
there is a response we're going to print
 

00:03:43.599 --> 00:03:45.869 align:start position:0%
there is a response we're going to print
that<00:03:43.799><c> response</c><00:03:44.360><c> just</c><00:03:44.959><c> um</c><00:03:45.159><c> in</c><00:03:45.319><c> your</c><00:03:45.439><c> terminal</c>

00:03:45.869 --> 00:03:45.879 align:start position:0%
that response just um in your terminal
 

00:03:45.879 --> 00:03:46.949 align:start position:0%
that response just um in your terminal
just<00:03:45.959><c> so</c><00:03:46.080><c> you</c><00:03:46.159><c> can</c><00:03:46.280><c> see</c><00:03:46.480><c> that</c><00:03:46.680><c> what</c><00:03:46.799><c> it's</c>

00:03:46.949 --> 00:03:46.959 align:start position:0%
just so you can see that what it's
 

00:03:46.959 --> 00:03:49.710 align:start position:0%
just so you can see that what it's
actually<00:03:47.200><c> Printing</c><00:03:48.080><c> and</c><00:03:48.319><c> we</c><00:03:48.439><c> want</c><00:03:48.640><c> to</c><00:03:49.040><c> Output</c>

00:03:49.710 --> 00:03:49.720 align:start position:0%
actually Printing and we want to Output
 

00:03:49.720 --> 00:03:51.869 align:start position:0%
actually Printing and we want to Output
the<00:03:49.920><c> answer</c><00:03:50.360><c> into</c><00:03:50.599><c> a</c><00:03:50.799><c> text</c><00:03:51.120><c> area</c><00:03:51.640><c> and</c><00:03:51.720><c> then</c>

00:03:51.869 --> 00:03:51.879 align:start position:0%
the answer into a text area and then
 

00:03:51.879 --> 00:03:53.350 align:start position:0%
the answer into a text area and then
finally<00:03:52.200><c> right</c><00:03:52.439><c> here</c><00:03:52.560><c> is</c><00:03:52.720><c> your</c><00:03:52.840><c> summary</c><00:03:53.200><c> to</c>

00:03:53.350 --> 00:03:53.360 align:start position:0%
finally right here is your summary to
 

00:03:53.360 --> 00:03:55.270 align:start position:0%
finally right here is your summary to
confirm<00:03:53.840><c> that</c><00:03:54.079><c> it</c><00:03:54.239><c> worked</c><00:03:54.720><c> and</c><00:03:54.840><c> now</c><00:03:55.000><c> in</c><00:03:55.079><c> order</c>

00:03:55.270 --> 00:03:55.280 align:start position:0%
confirm that it worked and now in order
 

00:03:55.280 --> 00:03:57.030 align:start position:0%
confirm that it worked and now in order
to<00:03:55.519><c> run</c><00:03:55.720><c> this</c><00:03:55.879><c> because</c><00:03:56.079><c> it's</c><00:03:56.239><c> a</c><00:03:56.360><c> streamlit</c>

00:03:57.030 --> 00:03:57.040 align:start position:0%
to run this because it's a streamlit
 

00:03:57.040 --> 00:03:58.589 align:start position:0%
to run this because it's a streamlit
application<00:03:57.840><c> first</c><00:03:58.040><c> off</c><00:03:58.159><c> you</c><00:03:58.239><c> want</c><00:03:58.360><c> to</c><00:03:58.480><c> make</c>

00:03:58.589 --> 00:03:58.599 align:start position:0%
application first off you want to make
 

00:03:58.599 --> 00:04:00.309 align:start position:0%
application first off you want to make
sure<00:03:58.760><c> you're</c><00:03:58.959><c> in</c><00:03:59.120><c> the</c><00:03:59.280><c> correct</c><00:03:59.760><c> directory</c><00:04:00.120><c> so</c>

00:04:00.309 --> 00:04:00.319 align:start position:0%
sure you're in the correct directory so
 

00:04:00.319 --> 00:04:01.830 align:start position:0%
sure you're in the correct directory so
here<00:04:00.720><c> you</c><00:04:00.799><c> want</c><00:04:00.920><c> to</c><00:04:01.079><c> make</c><00:04:01.239><c> sure</c><00:04:01.560><c> that</c><00:04:01.680><c> you're</c>

00:04:01.830 --> 00:04:01.840 align:start position:0%
here you want to make sure that you're
 

00:04:01.840 --> 00:04:05.069 align:start position:0%
here you want to make sure that you're
in<00:04:02.120><c> day</c><00:04:02.400><c> 30</c><00:04:03.079><c> W</c><00:04:03.280><c> Wiki</c><00:04:03.599><c> bot</c><00:04:04.400><c> once</c><00:04:04.560><c> you're</c><00:04:04.799><c> in</c>

00:04:05.069 --> 00:04:05.079 align:start position:0%
in day 30 W Wiki bot once you're in
 

00:04:05.079 --> 00:04:08.830 align:start position:0%
in day 30 W Wiki bot once you're in
there<00:04:05.519><c> then</c><00:04:05.640><c> you</c><00:04:05.799><c> type</c><00:04:06.200><c> streamlet</c><00:04:07.200><c> run</c>

00:04:08.830 --> 00:04:08.840 align:start position:0%
there then you type streamlet run
 

00:04:08.840 --> 00:04:10.830 align:start position:0%
there then you type streamlet run
main.py<00:04:09.840><c> and</c><00:04:09.959><c> then</c><00:04:10.079><c> when</c><00:04:10.280><c> this</c><00:04:10.400><c> run</c><00:04:10.680><c> this</c><00:04:10.760><c> is</c>

00:04:10.830 --> 00:04:10.840 align:start position:0%
main.py and then when this run this is
 

00:04:10.840 --> 00:04:12.910 align:start position:0%
main.py and then when this run this is
going<00:04:10.920><c> to</c><00:04:11.000><c> spin</c><00:04:11.280><c> up</c><00:04:11.439><c> a</c><00:04:11.599><c> local</c><00:04:11.879><c> server</c><00:04:12.239><c> for</c><00:04:12.439><c> you</c>

00:04:12.910 --> 00:04:12.920 align:start position:0%
going to spin up a local server for you
 

00:04:12.920 --> 00:04:14.710 align:start position:0%
going to spin up a local server for you
okay<00:04:13.040><c> so</c><00:04:13.200><c> here</c><00:04:13.319><c> it's</c><00:04:13.480><c> already</c><00:04:13.720><c> doing</c><00:04:14.000><c> it</c><00:04:14.599><c> and</c>

00:04:14.710 --> 00:04:14.720 align:start position:0%
okay so here it's already doing it and
 

00:04:14.720 --> 00:04:18.030 align:start position:0%
okay so here it's already doing it and
then<00:04:15.079><c> here</c><00:04:15.239><c> it</c><00:04:15.360><c> is</c><00:04:16.000><c> so</c><00:04:16.120><c> if</c><00:04:16.239><c> we</c><00:04:16.400><c> go</c><00:04:16.720><c> back</c><00:04:17.680><c> right</c>

00:04:18.030 --> 00:04:18.040 align:start position:0%
then here it is so if we go back right
 

00:04:18.040 --> 00:04:19.430 align:start position:0%
then here it is so if we go back right
um<00:04:18.199><c> You</c><00:04:18.320><c> can</c><00:04:18.440><c> view</c><00:04:18.600><c> your</c><00:04:18.680><c> Stream</c><00:04:19.120><c> app</c><00:04:19.239><c> in</c><00:04:19.320><c> your</c>

00:04:19.430 --> 00:04:19.440 align:start position:0%
um You can view your Stream app in your
 

00:04:19.440 --> 00:04:21.390 align:start position:0%
um You can view your Stream app in your
browser<00:04:20.000><c> and</c><00:04:20.199><c> this</c><00:04:20.359><c> is</c><00:04:20.680><c> where</c><00:04:20.880><c> we</c><00:04:20.959><c> went</c><00:04:21.239><c> so</c>

00:04:21.390 --> 00:04:21.400 align:start position:0%
browser and this is where we went so
 

00:04:21.400 --> 00:04:23.030 align:start position:0%
browser and this is where we went so
then<00:04:21.479><c> we</c><00:04:21.560><c> can</c><00:04:21.720><c> modify</c><00:04:22.120><c> this</c><00:04:22.560><c> uh</c><00:04:22.720><c> give</c><00:04:22.840><c> it</c><00:04:22.919><c> a</c>

00:04:23.030 --> 00:04:23.040 align:start position:0%
then we can modify this uh give it a
 

00:04:23.040 --> 00:04:24.550 align:start position:0%
then we can modify this uh give it a
different<00:04:23.320><c> Wikipedia</c><00:04:23.800><c> search</c><00:04:24.040><c> for</c><00:04:24.280><c> Alan</c>

00:04:24.550 --> 00:04:24.560 align:start position:0%
different Wikipedia search for Alan
 

00:04:24.560 --> 00:04:26.189 align:start position:0%
different Wikipedia search for Alan
Turing<00:04:25.080><c> and</c><00:04:25.160><c> then</c><00:04:25.320><c> ask</c><00:04:25.479><c> a</c><00:04:25.639><c> question</c><00:04:25.919><c> how</c><00:04:26.040><c> old</c>

00:04:26.189 --> 00:04:26.199 align:start position:0%
Turing and then ask a question how old
 

00:04:26.199 --> 00:04:27.710 align:start position:0%
Turing and then ask a question how old
was<00:04:26.320><c> he</c><00:04:26.400><c> when</c><00:04:26.479><c> he</c><00:04:26.600><c> passed</c><00:04:26.840><c> away</c><00:04:27.400><c> okay</c><00:04:27.520><c> so</c><00:04:27.639><c> this</c>

00:04:27.710 --> 00:04:27.720 align:start position:0%
was he when he passed away okay so this
 

00:04:27.720 --> 00:04:29.029 align:start position:0%
was he when he passed away okay so this
was<00:04:27.880><c> just</c><00:04:28.000><c> kind</c><00:04:28.080><c> of</c><00:04:28.160><c> a</c><00:04:28.280><c> simple</c><00:04:28.560><c> introduction</c>

00:04:29.029 --> 00:04:29.039 align:start position:0%
was just kind of a simple introduction
 

00:04:29.039 --> 00:04:31.629 align:start position:0%
was just kind of a simple introduction
into<00:04:29.360><c> another</c><00:04:29.720><c> another</c><00:04:30.120><c> way</c><00:04:30.800><c> of</c><00:04:31.000><c> loading</c><00:04:31.360><c> data</c>

00:04:31.629 --> 00:04:31.639 align:start position:0%
into another another way of loading data
 

00:04:31.639 --> 00:04:33.390 align:start position:0%
into another another way of loading data
into<00:04:31.759><c> a</c><00:04:31.880><c> vector</c><00:04:32.120><c> database</c><00:04:32.880><c> and</c><00:04:33.039><c> you</c><00:04:33.120><c> can</c><00:04:33.240><c> do</c>

00:04:33.390 --> 00:04:33.400 align:start position:0%
into a vector database and you can do
 

00:04:33.400 --> 00:04:35.430 align:start position:0%
into a vector database and you can do
this<00:04:33.639><c> All</c><00:04:33.880><c> locally</c><00:04:34.360><c> remember</c><00:04:34.600><c> I</c><00:04:34.720><c> just</c><00:04:34.840><c> used</c><00:04:35.120><c> LM</c>

00:04:35.430 --> 00:04:35.440 align:start position:0%
this All locally remember I just used LM
 

00:04:35.440 --> 00:04:38.150 align:start position:0%
this All locally remember I just used LM
Studio<00:04:36.039><c> using</c><00:04:36.320><c> a</c><00:04:36.520><c> simple</c><00:04:36.960><c> a</c><00:04:37.080><c> very</c><00:04:37.280><c> small</c><00:04:37.680><c> 52</c>

00:04:38.150 --> 00:04:38.160 align:start position:0%
Studio using a simple a very small 52
 

00:04:38.160 --> 00:04:39.430 align:start position:0%
Studio using a simple a very small 52
model<00:04:38.520><c> let</c><00:04:38.600><c> me</c><00:04:38.720><c> know</c><00:04:38.800><c> what</c><00:04:38.880><c> you</c><00:04:39.039><c> tried</c><00:04:39.280><c> in</c><00:04:39.360><c> the</c>

00:04:39.430 --> 00:04:39.440 align:start position:0%
model let me know what you tried in the
 

00:04:39.440 --> 00:04:41.070 align:start position:0%
model let me know what you tried in the
comment<00:04:39.680><c> section</c><00:04:39.919><c> down</c><00:04:40.080><c> below</c><00:04:40.680><c> thank</c><00:04:40.840><c> you</c><00:04:40.919><c> for</c>

00:04:41.070 --> 00:04:41.080 align:start position:0%
comment section down below thank you for
 

00:04:41.080 --> 00:04:44.600 align:start position:0%
comment section down below thank you for
watching<00:04:41.560><c> I'll</c><00:04:41.720><c> see</c><00:04:41.880><c> you</c><00:04:42.039><c> next</c><00:04:42.240><c> video</c>

