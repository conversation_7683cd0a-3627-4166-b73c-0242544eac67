WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:03.249 align:start position:0%
 
okay<00:00:00.599><c> in</c><00:00:01.079><c> this</c><00:00:01.260><c> video</c><00:00:01.439><c> I'm</c><00:00:01.920><c> gonna</c><00:00:02.100><c> look</c><00:00:02.460><c> at</c><00:00:02.760><c> the</c>

00:00:03.249 --> 00:00:03.259 align:start position:0%
okay in this video I'm gonna look at the
 

00:00:03.259 --> 00:00:06.170 align:start position:0%
okay in this video I'm gonna look at the
Transformers<00:00:04.259><c> agent</c><00:00:05.040><c> so</c><00:00:05.339><c> this</c><00:00:05.580><c> is</c><00:00:05.700><c> a</c><00:00:05.940><c> new</c>

00:00:06.170 --> 00:00:06.180 align:start position:0%
Transformers agent so this is a new
 

00:00:06.180 --> 00:00:07.309 align:start position:0%
Transformers agent so this is a new
thing<00:00:06.359><c> that</c><00:00:06.480><c> came</c><00:00:06.660><c> out</c><00:00:06.779><c> this</c><00:00:06.960><c> week</c><00:00:07.140><c> from</c>

00:00:07.309 --> 00:00:07.319 align:start position:0%
thing that came out this week from
 

00:00:07.319 --> 00:00:09.770 align:start position:0%
thing that came out this week from
hugging<00:00:07.740><c> face</c><00:00:08.059><c> at</c><00:00:09.059><c> first</c><00:00:09.300><c> I</c><00:00:09.420><c> wasn't</c><00:00:09.540><c> sure</c>

00:00:09.770 --> 00:00:09.780 align:start position:0%
hugging face at first I wasn't sure
 

00:00:09.780 --> 00:00:11.690 align:start position:0%
hugging face at first I wasn't sure
exactly<00:00:10.139><c> what</c><00:00:10.320><c> it</c><00:00:10.500><c> was</c><00:00:10.679><c> but</c><00:00:11.219><c> then</c><00:00:11.340><c> having</c><00:00:11.519><c> a</c>

00:00:11.690 --> 00:00:11.700 align:start position:0%
exactly what it was but then having a
 

00:00:11.700 --> 00:00:13.850 align:start position:0%
exactly what it was but then having a
look<00:00:11.820><c> at</c><00:00:12.000><c> it</c><00:00:12.179><c> it's</c><00:00:12.719><c> kind</c><00:00:12.960><c> of</c><00:00:13.019><c> cool</c><00:00:13.139><c> and</c><00:00:13.500><c> I</c><00:00:13.740><c> think</c>

00:00:13.850 --> 00:00:13.860 align:start position:0%
look at it it's kind of cool and I think
 

00:00:13.860 --> 00:00:15.890 align:start position:0%
look at it it's kind of cool and I think
it's<00:00:14.040><c> definitely</c><00:00:14.340><c> what</c><00:00:14.580><c> having</c><00:00:15.179><c> a</c><00:00:15.599><c> video</c>

00:00:15.890 --> 00:00:15.900 align:start position:0%
it's definitely what having a video
 

00:00:15.900 --> 00:00:17.689 align:start position:0%
it's definitely what having a video
about<00:00:16.199><c> it</c><00:00:16.500><c> and</c><00:00:16.680><c> looking</c><00:00:17.100><c> at</c><00:00:17.340><c> what</c><00:00:17.580><c> it's</c>

00:00:17.689 --> 00:00:17.699 align:start position:0%
about it and looking at what it's
 

00:00:17.699 --> 00:00:20.870 align:start position:0%
about it and looking at what it's
actually<00:00:17.880><c> doing</c><00:00:18.320><c> so</c><00:00:19.320><c> in</c><00:00:20.100><c> some</c><00:00:20.220><c> ways</c><00:00:20.460><c> this</c><00:00:20.699><c> is</c>

00:00:20.870 --> 00:00:20.880 align:start position:0%
actually doing so in some ways this is
 

00:00:20.880 --> 00:00:23.689 align:start position:0%
actually doing so in some ways this is
doing<00:00:21.180><c> some</c><00:00:22.020><c> things</c><00:00:22.320><c> that</c><00:00:22.680><c> are</c><00:00:22.859><c> very</c><00:00:23.100><c> similar</c>

00:00:23.689 --> 00:00:23.699 align:start position:0%
doing some things that are very similar
 

00:00:23.699 --> 00:00:26.810 align:start position:0%
doing some things that are very similar
to<00:00:24.119><c> launching</c><00:00:24.960><c> tools</c><00:00:25.500><c> or</c><00:00:25.800><c> actually</c><00:00:26.160><c> to</c><00:00:26.519><c> the</c>

00:00:26.810 --> 00:00:26.820 align:start position:0%
to launching tools or actually to the
 

00:00:26.820 --> 00:00:30.109 align:start position:0%
to launching tools or actually to the
paper<00:00:27.080><c> tool</c><00:00:28.080><c> former</c><00:00:28.500><c> where</c><00:00:29.340><c> it's</c><00:00:29.820><c> basically</c>

00:00:30.109 --> 00:00:30.119 align:start position:0%
paper tool former where it's basically
 

00:00:30.119 --> 00:00:32.930 align:start position:0%
paper tool former where it's basically
making<00:00:30.480><c> an</c><00:00:30.960><c> agent</c><00:00:31.320><c> that</c><00:00:31.740><c> can</c><00:00:31.980><c> call</c><00:00:32.340><c> different</c>

00:00:32.930 --> 00:00:32.940 align:start position:0%
making an agent that can call different
 

00:00:32.940 --> 00:00:36.049 align:start position:0%
making an agent that can call different
tools<00:00:33.540><c> and</c><00:00:34.079><c> use</c><00:00:34.260><c> different</c><00:00:34.559><c> tools</c><00:00:35.100><c> the</c><00:00:35.880><c> key</c>

00:00:36.049 --> 00:00:36.059 align:start position:0%
tools and use different tools the key
 

00:00:36.059 --> 00:00:37.610 align:start position:0%
tools and use different tools the key
thing<00:00:36.239><c> here</c><00:00:36.480><c> though</c><00:00:36.660><c> is</c><00:00:36.960><c> that</c><00:00:37.140><c> the</c><00:00:37.320><c> tools</c>

00:00:37.610 --> 00:00:37.620 align:start position:0%
thing here though is that the tools
 

00:00:37.620 --> 00:00:40.310 align:start position:0%
thing here though is that the tools
themselves<00:00:38.040><c> are</c><00:00:38.760><c> actual</c><00:00:39.180><c> different</c><00:00:39.600><c> models</c>

00:00:40.310 --> 00:00:40.320 align:start position:0%
themselves are actual different models
 

00:00:40.320 --> 00:00:43.069 align:start position:0%
themselves are actual different models
on<00:00:40.620><c> the</c><00:00:40.739><c> hugging</c><00:00:41.040><c> face</c><00:00:41.280><c> Hub</c><00:00:41.579><c> not</c><00:00:42.480><c> necessarily</c>

00:00:43.069 --> 00:00:43.079 align:start position:0%
on the hugging face Hub not necessarily
 

00:00:43.079 --> 00:00:44.630 align:start position:0%
on the hugging face Hub not necessarily
made<00:00:43.260><c> by</c><00:00:43.440><c> hanging</c><00:00:43.800><c> face</c><00:00:43.920><c> themselves</c><00:00:44.399><c> but</c>

00:00:44.630 --> 00:00:44.640 align:start position:0%
made by hanging face themselves but
 

00:00:44.640 --> 00:00:47.750 align:start position:0%
made by hanging face themselves but
hosted<00:00:45.059><c> on</c><00:00:45.300><c> the</c><00:00:45.480><c> Hub</c><00:00:45.780><c> and</c><00:00:46.440><c> can</c><00:00:46.800><c> be</c><00:00:47.040><c> used</c><00:00:47.219><c> with</c>

00:00:47.750 --> 00:00:47.760 align:start position:0%
hosted on the Hub and can be used with
 

00:00:47.760 --> 00:00:51.290 align:start position:0%
hosted on the Hub and can be used with
the<00:00:48.440><c> Transformers</c><00:00:49.440><c> Library</c><00:00:50.219><c> here</c>

00:00:51.290 --> 00:00:51.300 align:start position:0%
the Transformers Library here
 

00:00:51.300 --> 00:00:54.170 align:start position:0%
the Transformers Library here
so<00:00:51.960><c> I'll</c><00:00:52.320><c> go</c><00:00:52.500><c> through</c><00:00:52.680><c> the</c><00:00:52.980><c> The</c><00:00:53.399><c> Notebook</c><00:00:53.879><c> but</c>

00:00:54.170 --> 00:00:54.180 align:start position:0%
so I'll go through the The Notebook but
 

00:00:54.180 --> 00:00:55.910 align:start position:0%
so I'll go through the The Notebook but
just<00:00:54.360><c> to</c><00:00:54.539><c> sort</c><00:00:54.719><c> of</c><00:00:54.780><c> show</c><00:00:55.020><c> you</c><00:00:55.199><c> okay</c><00:00:55.559><c> what</c>

00:00:55.910 --> 00:00:55.920 align:start position:0%
just to sort of show you okay what
 

00:00:55.920 --> 00:00:57.709 align:start position:0%
just to sort of show you okay what
actually<00:00:56.100><c> is</c><00:00:56.399><c> going</c><00:00:56.640><c> on</c><00:00:56.940><c> here</c><00:00:57.180><c> they've</c><00:00:57.480><c> got</c><00:00:57.600><c> a</c>

00:00:57.709 --> 00:00:57.719 align:start position:0%
actually is going on here they've got a
 

00:00:57.719 --> 00:01:00.410 align:start position:0%
actually is going on here they've got a
nice<00:00:57.899><c> little</c><00:00:58.020><c> diagram</c><00:00:58.440><c> here</c><00:00:58.800><c> of</c><00:00:59.640><c> where</c><00:00:59.940><c> you</c>

00:01:00.410 --> 00:01:00.420 align:start position:0%
nice little diagram here of where you
 

00:01:00.420 --> 00:01:03.170 align:start position:0%
nice little diagram here of where you
basically<00:01:00.719><c> put</c><00:01:01.079><c> in</c><00:01:01.320><c> an</c><00:01:01.739><c> instruction</c><00:01:02.180><c> which</c>

00:01:03.170 --> 00:01:03.180 align:start position:0%
basically put in an instruction which
 

00:01:03.180 --> 00:01:06.950 align:start position:0%
basically put in an instruction which
then<00:01:03.359><c> gets</c><00:01:03.780><c> inserted</c><00:01:04.379><c> into</c><00:01:05.000><c> a</c><00:01:06.000><c> prompt</c><00:01:06.479><c> just</c>

00:01:06.950 --> 00:01:06.960 align:start position:0%
then gets inserted into a prompt just
 

00:01:06.960 --> 00:01:09.590 align:start position:0%
then gets inserted into a prompt just
like<00:01:07.140><c> in</c><00:01:07.500><c> Lang</c><00:01:07.860><c> chain</c><00:01:08.220><c> and</c><00:01:08.820><c> then</c><00:01:08.939><c> as</c><00:01:09.240><c> well</c><00:01:09.420><c> as</c>

00:01:09.590 --> 00:01:09.600 align:start position:0%
like in Lang chain and then as well as
 

00:01:09.600 --> 00:01:11.450 align:start position:0%
like in Lang chain and then as well as
the<00:01:10.020><c> instruction</c><00:01:10.260><c> getting</c><00:01:10.740><c> inserted</c><00:01:11.280><c> in</c>

00:01:11.450 --> 00:01:11.460 align:start position:0%
the instruction getting inserted in
 

00:01:11.460 --> 00:01:13.789 align:start position:0%
the instruction getting inserted in
there<00:01:11.640><c> there's</c><00:01:12.180><c> also</c><00:01:12.540><c> a</c><00:01:12.720><c> bunch</c><00:01:12.840><c> of</c><00:01:13.020><c> tools</c><00:01:13.560><c> in</c>

00:01:13.789 --> 00:01:13.799 align:start position:0%
there there's also a bunch of tools in
 

00:01:13.799 --> 00:01:16.250 align:start position:0%
there there's also a bunch of tools in
there<00:01:13.979><c> so</c><00:01:14.580><c> this</c><00:01:14.939><c> is</c><00:01:15.060><c> a</c><00:01:15.299><c> good</c><00:01:15.479><c> example</c><00:01:15.780><c> of</c><00:01:15.960><c> using</c>

00:01:16.250 --> 00:01:16.260 align:start position:0%
there so this is a good example of using
 

00:01:16.260 --> 00:01:18.830 align:start position:0%
there so this is a good example of using
quite<00:01:16.560><c> a</c><00:01:16.680><c> lot</c><00:01:16.799><c> of</c><00:01:16.920><c> tools</c><00:01:17.340><c> we'll</c><00:01:17.640><c> see</c><00:01:17.880><c> as</c><00:01:18.540><c> we</c><00:01:18.720><c> go</c>

00:01:18.830 --> 00:01:18.840 align:start position:0%
quite a lot of tools we'll see as we go
 

00:01:18.840 --> 00:01:21.710 align:start position:0%
quite a lot of tools we'll see as we go
through<00:01:19.020><c> this</c><00:01:19.380><c> and</c><00:01:20.100><c> then</c><00:01:20.220><c> those</c><00:01:20.580><c> tools</c><00:01:21.119><c> are</c>

00:01:21.710 --> 00:01:21.720 align:start position:0%
through this and then those tools are
 

00:01:21.720 --> 00:01:23.810 align:start position:0%
through this and then those tools are
basically<00:01:22.140><c> injected</c><00:01:22.740><c> in</c><00:01:22.920><c> there</c>

00:01:23.810 --> 00:01:23.820 align:start position:0%
basically injected in there
 

00:01:23.820 --> 00:01:27.350 align:start position:0%
basically injected in there
the<00:01:24.540><c> agent</c><00:01:24.960><c> can</c><00:01:25.500><c> either</c><00:01:25.799><c> be</c><00:01:26.159><c> you</c><00:01:26.580><c> know</c><00:01:26.700><c> open</c><00:01:26.820><c> AI</c>

00:01:27.350 --> 00:01:27.360 align:start position:0%
the agent can either be you know open AI
 

00:01:27.360 --> 00:01:29.870 align:start position:0%
the agent can either be you know open AI
bot<00:01:28.080><c> or</c><00:01:28.259><c> it</c><00:01:28.439><c> could</c><00:01:28.619><c> be</c><00:01:28.860><c> one</c><00:01:29.280><c> of</c><00:01:29.340><c> the</c><00:01:29.460><c> ones</c><00:01:29.700><c> from</c>

00:01:29.870 --> 00:01:29.880 align:start position:0%
bot or it could be one of the ones from
 

00:01:29.880 --> 00:01:32.810 align:start position:0%
bot or it could be one of the ones from
the<00:01:30.119><c> hugging</c><00:01:30.420><c> face</c><00:01:30.600><c> Hub</c><00:01:30.900><c> so</c><00:01:31.320><c> it</c><00:01:31.500><c> could</c><00:01:31.680><c> be</c><00:01:31.920><c> the</c>

00:01:32.810 --> 00:01:32.820 align:start position:0%
the hugging face Hub so it could be the
 

00:01:32.820 --> 00:01:36.230 align:start position:0%
the hugging face Hub so it could be the
open<00:01:33.000><c> assistant</c><00:01:33.740><c> or</c><00:01:34.740><c> Star</c><00:01:35.100><c> coder</c><00:01:35.579><c> that</c><00:01:36.000><c> seems</c>

00:01:36.230 --> 00:01:36.240 align:start position:0%
open assistant or Star coder that seems
 

00:01:36.240 --> 00:01:37.670 align:start position:0%
open assistant or Star coder that seems
to<00:01:36.299><c> be</c><00:01:36.360><c> a</c><00:01:36.479><c> good</c><00:01:36.600><c> one</c><00:01:36.780><c> uh</c><00:01:37.140><c> that</c><00:01:37.320><c> they're</c><00:01:37.439><c> using</c>

00:01:37.670 --> 00:01:37.680 align:start position:0%
to be a good one uh that they're using
 

00:01:37.680 --> 00:01:40.310 align:start position:0%
to be a good one uh that they're using
for<00:01:37.920><c> this</c><00:01:38.100><c> which</c><00:01:38.400><c> is</c><00:01:38.579><c> a</c><00:01:39.060><c> good</c><00:01:39.240><c> sign</c><00:01:39.600><c> that</c>

00:01:40.310 --> 00:01:40.320 align:start position:0%
for this which is a good sign that
 

00:01:40.320 --> 00:01:41.990 align:start position:0%
for this which is a good sign that
there's<00:01:40.560><c> now</c><00:01:40.799><c> open</c><00:01:41.100><c> source</c><00:01:41.520><c> things</c><00:01:41.579><c> that</c><00:01:41.820><c> can</c>

00:01:41.990 --> 00:01:42.000 align:start position:0%
there's now open source things that can
 

00:01:42.000 --> 00:01:42.950 align:start position:0%
there's now open source things that can
do<00:01:42.119><c> that</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
do that
 

00:01:42.960 --> 00:01:45.050 align:start position:0%
do that
and<00:01:43.439><c> then</c><00:01:43.560><c> basically</c><00:01:44.100><c> it</c><00:01:44.340><c> will</c><00:01:44.460><c> decide</c><00:01:44.759><c> what</c>

00:01:45.050 --> 00:01:45.060 align:start position:0%
and then basically it will decide what
 

00:01:45.060 --> 00:01:47.210 align:start position:0%
and then basically it will decide what
tool<00:01:45.299><c> to</c><00:01:45.420><c> use</c><00:01:45.600><c> the</c><00:01:45.960><c> tool</c><00:01:46.320><c> will</c><00:01:46.439><c> then</c><00:01:46.740><c> basically</c>

00:01:47.210 --> 00:01:47.220 align:start position:0%
tool to use the tool will then basically
 

00:01:47.220 --> 00:01:51.410 align:start position:0%
tool to use the tool will then basically
just<00:01:47.700><c> interface</c><00:01:48.299><c> with</c><00:01:48.540><c> python</c><00:01:49.140><c> run</c><00:01:50.040><c> using</c><00:01:51.000><c> the</c>

00:01:51.410 --> 00:01:51.420 align:start position:0%
just interface with python run using the
 

00:01:51.420 --> 00:01:53.990 align:start position:0%
just interface with python run using the
input<00:01:51.840><c> and</c><00:01:52.619><c> then</c><00:01:52.740><c> generate</c><00:01:53.280><c> some</c><00:01:53.640><c> kind</c><00:01:53.820><c> of</c>

00:01:53.990 --> 00:01:54.000 align:start position:0%
input and then generate some kind of
 

00:01:54.000 --> 00:01:56.690 align:start position:0%
input and then generate some kind of
output<00:01:54.479><c> so</c><00:01:55.439><c> let's</c><00:01:55.799><c> jump</c><00:01:56.040><c> in</c><00:01:56.159><c> and</c><00:01:56.280><c> have</c><00:01:56.460><c> a</c><00:01:56.579><c> look</c>

00:01:56.690 --> 00:01:56.700 align:start position:0%
output so let's jump in and have a look
 

00:01:56.700 --> 00:01:59.510 align:start position:0%
output so let's jump in and have a look
at<00:01:56.880><c> you</c><00:01:57.180><c> know</c><00:01:57.240><c> at</c><00:01:57.420><c> the</c><00:01:57.659><c> code</c><00:01:57.840><c> so</c><00:01:58.380><c> I</c><00:01:58.979><c> basically</c>

00:01:59.510 --> 00:01:59.520 align:start position:0%
at you know at the code so I basically
 

00:01:59.520 --> 00:02:01.429 align:start position:0%
at you know at the code so I basically
pulled<00:01:59.939><c> apart</c><00:02:00.180><c> their</c><00:02:00.479><c> demo</c><00:02:00.960><c> and</c><00:02:01.140><c> sort</c><00:02:01.380><c> of</c>

00:02:01.429 --> 00:02:01.439 align:start position:0%
pulled apart their demo and sort of
 

00:02:01.439 --> 00:02:03.410 align:start position:0%
pulled apart their demo and sort of
played<00:02:01.680><c> around</c><00:02:01.799><c> with</c><00:02:02.040><c> it</c><00:02:02.220><c> a</c><00:02:02.399><c> bit</c><00:02:02.520><c> just</c><00:02:03.180><c> so</c><00:02:03.240><c> I</c>

00:02:03.410 --> 00:02:03.420 align:start position:0%
played around with it a bit just so I
 

00:02:03.420 --> 00:02:05.630 align:start position:0%
played around with it a bit just so I
could<00:02:03.479><c> see</c><00:02:03.659><c> exactly</c><00:02:04.079><c> what's</c><00:02:04.320><c> going</c><00:02:04.619><c> on</c><00:02:04.799><c> here</c>

00:02:05.630 --> 00:02:05.640 align:start position:0%
could see exactly what's going on here
 

00:02:05.640 --> 00:02:07.730 align:start position:0%
could see exactly what's going on here
you<00:02:06.420><c> will</c><00:02:06.540><c> need</c><00:02:06.780><c> to</c><00:02:06.899><c> install</c><00:02:07.079><c> a</c><00:02:07.439><c> number</c><00:02:07.560><c> of</c>

00:02:07.730 --> 00:02:07.740 align:start position:0%
you will need to install a number of
 

00:02:07.740 --> 00:02:10.070 align:start position:0%
you will need to install a number of
libraries<00:02:08.280><c> when</c><00:02:08.580><c> you're</c><00:02:08.700><c> doing</c><00:02:08.940><c> it</c><00:02:09.179><c> the</c>

00:02:10.070 --> 00:02:10.080 align:start position:0%
libraries when you're doing it the
 

00:02:10.080 --> 00:02:12.589 align:start position:0%
libraries when you're doing it the
challenge<00:02:10.280><c> is</c><00:02:11.280><c> that</c><00:02:11.640><c> if</c><00:02:12.000><c> you</c><00:02:12.120><c> don't</c><00:02:12.300><c> have</c>

00:02:12.589 --> 00:02:12.599 align:start position:0%
challenge is that if you don't have
 

00:02:12.599 --> 00:02:15.410 align:start position:0%
challenge is that if you don't have
these<00:02:12.959><c> in</c><00:02:13.140><c> you</c><00:02:14.099><c> can't</c><00:02:14.220><c> use</c><00:02:14.459><c> any</c><00:02:14.700><c> of</c><00:02:14.879><c> the</c><00:02:15.000><c> models</c>

00:02:15.410 --> 00:02:15.420 align:start position:0%
these in you can't use any of the models
 

00:02:15.420 --> 00:02:17.630 align:start position:0%
these in you can't use any of the models
that<00:02:15.720><c> would</c><00:02:15.900><c> rely</c><00:02:16.260><c> on</c><00:02:16.500><c> these</c><00:02:16.800><c> so</c><00:02:17.099><c> for</c><00:02:17.400><c> example</c>

00:02:17.630 --> 00:02:17.640 align:start position:0%
that would rely on these so for example
 

00:02:17.640 --> 00:02:20.089 align:start position:0%
that would rely on these so for example
image<00:02:18.360><c> generation</c><00:02:18.840><c> is</c><00:02:19.560><c> going</c><00:02:19.739><c> to</c><00:02:19.739><c> be</c><00:02:19.860><c> using</c>

00:02:20.089 --> 00:02:20.099 align:start position:0%
image generation is going to be using
 

00:02:20.099 --> 00:02:22.130 align:start position:0%
image generation is going to be using
diffusion<00:02:20.640><c> models</c><00:02:21.180><c> which</c><00:02:21.660><c> is</c><00:02:21.840><c> going</c><00:02:22.020><c> to</c><00:02:22.080><c> be</c>

00:02:22.130 --> 00:02:22.140 align:start position:0%
diffusion models which is going to be
 

00:02:22.140 --> 00:02:24.130 align:start position:0%
diffusion models which is going to be
using<00:02:22.440><c> the</c><00:02:22.620><c> diffusers</c><00:02:23.160><c> library</c><00:02:23.459><c> in</c><00:02:23.879><c> there</c>

00:02:24.130 --> 00:02:24.140 align:start position:0%
using the diffusers library in there
 

00:02:24.140 --> 00:02:26.869 align:start position:0%
using the diffusers library in there
same<00:02:25.140><c> for</c><00:02:25.560><c> some</c><00:02:26.099><c> of</c><00:02:26.220><c> the</c><00:02:26.280><c> other</c><00:02:26.400><c> things</c><00:02:26.640><c> too</c>

00:02:26.869 --> 00:02:26.879 align:start position:0%
same for some of the other things too
 

00:02:26.879 --> 00:02:28.190 align:start position:0%
same for some of the other things too
obviously<00:02:27.300><c> you</c><00:02:27.480><c> need</c><00:02:27.599><c> the</c><00:02:27.780><c> Transformers</c>

00:02:28.190 --> 00:02:28.200 align:start position:0%
obviously you need the Transformers
 

00:02:28.200 --> 00:02:30.110 align:start position:0%
obviously you need the Transformers
library<00:02:28.440><c> to</c><00:02:28.739><c> get</c><00:02:28.920><c> this</c><00:02:29.160><c> going</c>

00:02:30.110 --> 00:02:30.120 align:start position:0%
library to get this going
 

00:02:30.120 --> 00:02:33.589 align:start position:0%
library to get this going
so<00:02:30.720><c> you</c><00:02:31.020><c> can</c><00:02:31.140><c> use</c><00:02:31.440><c> open</c><00:02:31.860><c> AI</c><00:02:32.459><c> or</c><00:02:32.940><c> you</c><00:02:33.180><c> can</c><00:02:33.300><c> use</c>

00:02:33.589 --> 00:02:33.599 align:start position:0%
so you can use open AI or you can use
 

00:02:33.599 --> 00:02:36.530 align:start position:0%
so you can use open AI or you can use
their<00:02:34.140><c> own</c><00:02:34.319><c> internal</c><00:02:34.800><c> models</c><00:02:35.280><c> so</c><00:02:35.940><c> I'll</c><00:02:36.360><c> jump</c>

00:02:36.530 --> 00:02:36.540 align:start position:0%
their own internal models so I'll jump
 

00:02:36.540 --> 00:02:39.589 align:start position:0%
their own internal models so I'll jump
around<00:02:36.720><c> a</c><00:02:36.959><c> bit</c><00:02:37.020><c> or</c><00:02:37.680><c> you</c><00:02:37.800><c> can</c><00:02:38.400><c> you</c><00:02:39.060><c> so</c><00:02:39.420><c> you</c><00:02:39.540><c> can</c>

00:02:39.589 --> 00:02:39.599 align:start position:0%
around a bit or you can you so you can
 

00:02:39.599 --> 00:02:42.410 align:start position:0%
around a bit or you can you so you can
use<00:02:39.780><c> the</c><00:02:40.080><c> star</c><00:02:40.200><c> coder</c><00:02:40.739><c> model</c><00:02:40.860><c> or</c><00:02:41.760><c> you</c><00:02:41.940><c> can</c><00:02:42.060><c> use</c>

00:02:42.410 --> 00:02:42.420 align:start position:0%
use the star coder model or you can use
 

00:02:42.420 --> 00:02:45.710 align:start position:0%
use the star coder model or you can use
the<00:02:43.019><c> open</c><00:02:43.200><c> Assistant</c><00:02:43.560><c> model</c><00:02:43.980><c> if</c><00:02:44.400><c> you've</c><00:02:44.760><c> gone</c>

00:02:45.710 --> 00:02:45.720 align:start position:0%
the open Assistant model if you've gone
 

00:02:45.720 --> 00:02:47.509 align:start position:0%
the open Assistant model if you've gone
for<00:02:46.019><c> that</c><00:02:46.140><c> so</c><00:02:46.739><c> when</c><00:02:46.860><c> you're</c><00:02:46.980><c> setting</c><00:02:47.280><c> it</c><00:02:47.400><c> up</c>

00:02:47.509 --> 00:02:47.519 align:start position:0%
for that so when you're setting it up
 

00:02:47.519 --> 00:02:49.910 align:start position:0%
for that so when you're setting it up
you<00:02:47.760><c> either</c><00:02:47.879><c> put</c><00:02:48.180><c> in</c><00:02:48.300><c> your</c><00:02:48.660><c> open</c><00:02:48.780><c> AI</c><00:02:49.260><c> key</c><00:02:49.620><c> here</c>

00:02:49.910 --> 00:02:49.920 align:start position:0%
you either put in your open AI key here
 

00:02:49.920 --> 00:02:52.369 align:start position:0%
you either put in your open AI key here
or<00:02:50.700><c> you're</c><00:02:50.879><c> putting</c><00:02:51.300><c> you</c><00:02:51.780><c> know</c><00:02:51.840><c> using</c><00:02:52.200><c> the</c>

00:02:52.369 --> 00:02:52.379 align:start position:0%
or you're putting you know using the
 

00:02:52.379 --> 00:02:54.290 align:start position:0%
or you're putting you know using the
hugging<00:02:52.680><c> face</c><00:02:52.800><c> Hub</c><00:02:53.099><c> you</c><00:02:53.280><c> put</c><00:02:53.459><c> in</c><00:02:53.640><c> your</c><00:02:53.879><c> face</c>

00:02:54.290 --> 00:02:54.300 align:start position:0%
hugging face Hub you put in your face
 

00:02:54.300 --> 00:02:57.290 align:start position:0%
hugging face Hub you put in your face
Hub<00:02:54.540><c> token</c><00:02:55.080><c> in</c><00:02:55.319><c> there</c><00:02:55.560><c> as</c><00:02:56.099><c> well</c><00:02:56.280><c> you</c><00:02:57.000><c> will</c><00:02:57.180><c> need</c>

00:02:57.290 --> 00:02:57.300 align:start position:0%
Hub token in there as well you will need
 

00:02:57.300 --> 00:02:59.390 align:start position:0%
Hub token in there as well you will need
some<00:02:57.540><c> little</c><00:02:57.720><c> functions</c><00:02:58.379><c> to</c><00:02:58.560><c> play</c><00:02:58.739><c> back</c><00:02:58.920><c> audio</c>

00:02:59.390 --> 00:02:59.400 align:start position:0%
some little functions to play back audio
 

00:02:59.400 --> 00:03:02.570 align:start position:0%
some little functions to play back audio
to<00:03:00.120><c> do</c><00:03:00.300><c> things</c><00:03:00.480><c> like</c><00:03:00.720><c> that</c><00:03:01.099><c> once</c><00:03:02.099><c> you've</c><00:03:02.459><c> got</c>

00:03:02.570 --> 00:03:02.580 align:start position:0%
to do things like that once you've got
 

00:03:02.580 --> 00:03:04.729 align:start position:0%
to do things like that once you've got
that<00:03:02.819><c> you</c><00:03:03.120><c> basically</c><00:03:03.420><c> just</c><00:03:03.660><c> initialize</c><00:03:04.319><c> the</c>

00:03:04.729 --> 00:03:04.739 align:start position:0%
that you basically just initialize the
 

00:03:04.739 --> 00:03:07.550 align:start position:0%
that you basically just initialize the
agent<00:03:05.160><c> that</c><00:03:05.459><c> for</c><00:03:05.819><c> whatever</c><00:03:05.940><c> you're</c><00:03:06.360><c> using</c><00:03:06.780><c> so</c>

00:03:07.550 --> 00:03:07.560 align:start position:0%
agent that for whatever you're using so
 

00:03:07.560 --> 00:03:09.229 align:start position:0%
agent that for whatever you're using so
in<00:03:08.160><c> testing</c>

00:03:09.229 --> 00:03:09.239 align:start position:0%
in testing
 

00:03:09.239 --> 00:03:12.649 align:start position:0%
in testing
I<00:03:09.780><c> tested</c><00:03:10.200><c> very</c><00:03:10.379><c> briefly</c><00:03:10.819><c> you</c><00:03:11.819><c> know</c><00:03:11.879><c> and</c><00:03:12.480><c> I</c>

00:03:12.649 --> 00:03:12.659 align:start position:0%
I tested very briefly you know and I
 

00:03:12.659 --> 00:03:14.449 align:start position:0%
I tested very briefly you know and I
found<00:03:12.720><c> that</c><00:03:12.959><c> sort</c><00:03:13.140><c> of</c><00:03:13.260><c> like</c><00:03:13.379><c> the</c><00:03:13.680><c> open</c><00:03:13.860><c> AI</c><00:03:14.280><c> one</c>

00:03:14.449 --> 00:03:14.459 align:start position:0%
found that sort of like the open AI one
 

00:03:14.459 --> 00:03:17.089 align:start position:0%
found that sort of like the open AI one
seems<00:03:14.819><c> to</c><00:03:14.940><c> be</c><00:03:15.060><c> going</c><00:03:15.720><c> better</c><00:03:16.019><c> they</c><00:03:16.800><c> kind</c><00:03:16.980><c> of</c>

00:03:17.089 --> 00:03:17.099 align:start position:0%
seems to be going better they kind of
 

00:03:17.099 --> 00:03:19.550 align:start position:0%
seems to be going better they kind of
recommend<00:03:17.819><c> that</c><00:03:18.000><c> although</c><00:03:18.720><c> I</c><00:03:18.900><c> also</c><00:03:19.140><c> tried</c><00:03:19.379><c> the</c>

00:03:19.550 --> 00:03:19.560 align:start position:0%
recommend that although I also tried the
 

00:03:19.560 --> 00:03:21.170 align:start position:0%
recommend that although I also tried the
star<00:03:19.920><c> coder</c><00:03:20.340><c> one</c><00:03:20.459><c> and</c><00:03:20.640><c> it</c><00:03:20.760><c> seemed</c><00:03:21.000><c> to</c><00:03:21.120><c> be</c>

00:03:21.170 --> 00:03:21.180 align:start position:0%
star coder one and it seemed to be
 

00:03:21.180 --> 00:03:23.930 align:start position:0%
star coder one and it seemed to be
working<00:03:21.360><c> as</c><00:03:22.260><c> well</c><00:03:22.379><c> so</c><00:03:22.860><c> all</c><00:03:23.519><c> right</c><00:03:23.580><c> let's</c><00:03:23.760><c> look</c>

00:03:23.930 --> 00:03:23.940 align:start position:0%
working as well so all right let's look
 

00:03:23.940 --> 00:03:25.850 align:start position:0%
working as well so all right let's look
at<00:03:24.120><c> what</c><00:03:24.360><c> you</c><00:03:24.480><c> do</c><00:03:24.659><c> you</c><00:03:24.959><c> basically</c><00:03:25.260><c> initialize</c>

00:03:25.850 --> 00:03:25.860 align:start position:0%
at what you do you basically initialize
 

00:03:25.860 --> 00:03:28.190 align:start position:0%
at what you do you basically initialize
your<00:03:26.220><c> agent</c><00:03:26.580><c> here</c><00:03:27.000><c> so</c><00:03:27.300><c> that's</c><00:03:27.540><c> just</c><00:03:27.840><c> basically</c>

00:03:28.190 --> 00:03:28.200 align:start position:0%
your agent here so that's just basically
 

00:03:28.200 --> 00:03:31.009 align:start position:0%
your agent here so that's just basically
using<00:03:28.800><c> the</c><00:03:29.340><c> large</c><00:03:29.519><c> language</c><00:03:29.760><c> model</c><00:03:30.180><c> via</c><00:03:30.780><c> an</c>

00:03:31.009 --> 00:03:31.019 align:start position:0%
using the large language model via an
 

00:03:31.019 --> 00:03:33.470 align:start position:0%
using the large language model via an
API<00:03:31.500><c> so</c><00:03:32.099><c> you're</c><00:03:32.280><c> not</c><00:03:32.459><c> actually</c><00:03:32.700><c> installing</c>

00:03:33.470 --> 00:03:33.480 align:start position:0%
API so you're not actually installing
 

00:03:33.480 --> 00:03:35.330 align:start position:0%
API so you're not actually installing
that<00:03:33.780><c> model</c><00:03:33.959><c> even</c><00:03:34.319><c> if</c><00:03:34.560><c> you're</c><00:03:34.680><c> using</c><00:03:35.040><c> star</c>

00:03:35.330 --> 00:03:35.340 align:start position:0%
that model even if you're using star
 

00:03:35.340 --> 00:03:37.850 align:start position:0%
that model even if you're using star
coder<00:03:35.879><c> or</c><00:03:36.120><c> if</c><00:03:36.300><c> you're</c><00:03:36.480><c> using</c><00:03:36.900><c> uh</c><00:03:37.620><c> open</c>

00:03:37.850 --> 00:03:37.860 align:start position:0%
coder or if you're using uh open
 

00:03:37.860 --> 00:03:40.009 align:start position:0%
coder or if you're using uh open
assistance<00:03:38.519><c> model</c><00:03:38.940><c> in</c><00:03:39.239><c> here</c><00:03:39.480><c> you're</c><00:03:39.959><c> not</c>

00:03:40.009 --> 00:03:40.019 align:start position:0%
assistance model in here you're not
 

00:03:40.019 --> 00:03:41.930 align:start position:0%
assistance model in here you're not
including<00:03:40.560><c> installing</c><00:03:41.099><c> that</c><00:03:41.280><c> locally</c><00:03:41.640><c> you're</c>

00:03:41.930 --> 00:03:41.940 align:start position:0%
including installing that locally you're
 

00:03:41.940 --> 00:03:44.750 align:start position:0%
including installing that locally you're
calling<00:03:42.360><c> that</c><00:03:42.480><c> by</c><00:03:43.019><c> an</c><00:03:43.200><c> API</c><00:03:43.620><c> call</c><00:03:43.860><c> and</c><00:03:44.519><c> then</c><00:03:44.580><c> you</c>

00:03:44.750 --> 00:03:44.760 align:start position:0%
calling that by an API call and then you
 

00:03:44.760 --> 00:03:46.610 align:start position:0%
calling that by an API call and then you
can<00:03:44.819><c> see</c><00:03:45.000><c> we'll</c><00:03:45.239><c> basically</c><00:03:45.659><c> just</c><00:03:45.840><c> say</c><00:03:46.080><c> agent</c>

00:03:46.610 --> 00:03:46.620 align:start position:0%
can see we'll basically just say agent
 

00:03:46.620 --> 00:03:49.850 align:start position:0%
can see we'll basically just say agent
run<00:03:46.920><c> and</c><00:03:47.340><c> then</c><00:03:47.400><c> we</c><00:03:47.640><c> pass</c><00:03:47.940><c> in</c><00:03:48.180><c> an</c><00:03:48.659><c> instruction</c>

00:03:49.850 --> 00:03:49.860 align:start position:0%
run and then we pass in an instruction
 

00:03:49.860 --> 00:03:52.610 align:start position:0%
run and then we pass in an instruction
so<00:03:50.340><c> here</c><00:03:50.519><c> I've</c><00:03:50.700><c> got</c><00:03:50.879><c> agent</c><00:03:51.659><c> run</c><00:03:51.840><c> generate</c><00:03:52.500><c> an</c>

00:03:52.610 --> 00:03:52.620 align:start position:0%
so here I've got agent run generate an
 

00:03:52.620 --> 00:03:54.949 align:start position:0%
so here I've got agent run generate an
image<00:03:52.920><c> of</c><00:03:53.159><c> a</c><00:03:53.340><c> Maine</c><00:03:54.000><c> uh</c><00:03:54.540><c> gray</c><00:03:54.780><c> cat</c>

00:03:54.949 --> 00:03:54.959 align:start position:0%
image of a Maine uh gray cat
 

00:03:54.959 --> 00:03:57.530 align:start position:0%
image of a Maine uh gray cat
sitting<00:03:55.440><c> down</c><00:03:55.620><c> resting</c><00:03:56.159><c> and</c><00:03:56.940><c> you</c><00:03:57.180><c> can</c><00:03:57.299><c> see</c>

00:03:57.530 --> 00:03:57.540 align:start position:0%
sitting down resting and you can see
 

00:03:57.540 --> 00:03:59.869 align:start position:0%
sitting down resting and you can see
what<00:03:57.840><c> it</c><00:03:58.080><c> actually</c><00:03:58.200><c> does</c><00:03:58.560><c> so</c><00:03:59.040><c> it</c><00:03:59.580><c> will</c><00:03:59.700><c> go</c>

00:03:59.869 --> 00:03:59.879 align:start position:0%
what it actually does so it will go
 

00:03:59.879 --> 00:04:01.789 align:start position:0%
what it actually does so it will go
through<00:04:00.120><c> and</c><00:04:00.540><c> it</c><00:04:00.720><c> will</c><00:04:00.840><c> basically</c><00:04:01.200><c> do</c><00:04:01.620><c> the</c>

00:04:01.789 --> 00:04:01.799 align:start position:0%
through and it will basically do the
 

00:04:01.799 --> 00:04:03.949 align:start position:0%
through and it will basically do the
explanation<00:04:02.280><c> to</c><00:04:02.459><c> itself</c><00:04:02.819><c> so</c><00:04:03.120><c> this</c><00:04:03.299><c> is</c><00:04:03.480><c> exactly</c>

00:04:03.949 --> 00:04:03.959 align:start position:0%
explanation to itself so this is exactly
 

00:04:03.959 --> 00:04:07.009 align:start position:0%
explanation to itself so this is exactly
like<00:04:04.319><c> Lang</c><00:04:05.099><c> chains</c><00:04:05.760><c> sort</c><00:04:06.180><c> of</c><00:04:06.239><c> agents</c><00:04:06.840><c> and</c>

00:04:07.009 --> 00:04:07.019 align:start position:0%
like Lang chains sort of agents and
 

00:04:07.019 --> 00:04:10.850 align:start position:0%
like Lang chains sort of agents and
tools<00:04:07.379><c> which</c><00:04:08.340><c> comes</c><00:04:09.000><c> from</c><00:04:09.299><c> the</c><00:04:10.019><c> react</c><00:04:10.379><c> paper</c>

00:04:10.850 --> 00:04:10.860 align:start position:0%
tools which comes from the react paper
 

00:04:10.860 --> 00:04:13.070 align:start position:0%
tools which comes from the react paper
comes<00:04:11.519><c> from</c><00:04:11.640><c> Tool</c><00:04:12.060><c> former</c><00:04:12.420><c> paper</c><00:04:12.599><c> comes</c><00:04:12.959><c> from</c>

00:04:13.070 --> 00:04:13.080 align:start position:0%
comes from Tool former paper comes from
 

00:04:13.080 --> 00:04:14.750 align:start position:0%
comes from Tool former paper comes from
a<00:04:13.200><c> number</c><00:04:13.319><c> of</c><00:04:13.500><c> different</c><00:04:13.739><c> sort</c><00:04:14.340><c> of</c><00:04:14.459><c> papers</c>

00:04:14.750 --> 00:04:14.760 align:start position:0%
a number of different sort of papers
 

00:04:14.760 --> 00:04:17.030 align:start position:0%
a number of different sort of papers
putting<00:04:15.120><c> all</c><00:04:15.239><c> this</c><00:04:15.420><c> together</c><00:04:15.659><c> but</c><00:04:16.620><c> we</c><00:04:16.739><c> put</c><00:04:16.919><c> the</c>

00:04:17.030 --> 00:04:17.040 align:start position:0%
putting all this together but we put the
 

00:04:17.040 --> 00:04:18.469 align:start position:0%
putting all this together but we put the
same<00:04:17.160><c> sort</c><00:04:17.340><c> of</c><00:04:17.400><c> thing</c><00:04:17.519><c> so</c><00:04:18.000><c> it</c><00:04:18.180><c> basically</c>

00:04:18.469 --> 00:04:18.479 align:start position:0%
same sort of thing so it basically
 

00:04:18.479 --> 00:04:19.909 align:start position:0%
same sort of thing so it basically
explains<00:04:18.780><c> to</c><00:04:18.900><c> itself</c><00:04:19.139><c> I</c><00:04:19.380><c> will</c><00:04:19.560><c> use</c><00:04:19.739><c> the</c>

00:04:19.909 --> 00:04:19.919 align:start position:0%
explains to itself I will use the
 

00:04:19.919 --> 00:04:22.730 align:start position:0%
explains to itself I will use the
following<00:04:20.220><c> tool</c><00:04:20.639><c> image</c><00:04:21.359><c> generator</c><00:04:21.840><c> to</c>

00:04:22.730 --> 00:04:22.740 align:start position:0%
following tool image generator to
 

00:04:22.740 --> 00:04:24.350 align:start position:0%
following tool image generator to
generate<00:04:23.040><c> an</c><00:04:23.280><c> image</c><00:04:23.520><c> according</c><00:04:24.060><c> to</c><00:04:24.180><c> the</c>

00:04:24.350 --> 00:04:24.360 align:start position:0%
generate an image according to the
 

00:04:24.360 --> 00:04:26.950 align:start position:0%
generate an image according to the
prompt<00:04:24.720><c> and</c><00:04:25.259><c> then</c><00:04:25.380><c> so</c><00:04:25.680><c> then</c><00:04:25.979><c> it</c><00:04:26.220><c> basically</c>

00:04:26.950 --> 00:04:26.960 align:start position:0%
prompt and then so then it basically
 

00:04:26.960 --> 00:04:29.990 align:start position:0%
prompt and then so then it basically
downloads<00:04:27.960><c> that</c><00:04:28.320><c> and</c><00:04:28.800><c> it's</c><00:04:28.979><c> passing</c><00:04:29.400><c> in</c><00:04:29.580><c> okay</c>

00:04:29.990 --> 00:04:30.000 align:start position:0%
downloads that and it's passing in okay
 

00:04:30.000 --> 00:04:32.870 align:start position:0%
downloads that and it's passing in okay
image<00:04:30.540><c> generator</c><00:04:30.960><c> prompt</c><00:04:31.500><c> equals</c><00:04:31.919><c> mancoon</c>

00:04:32.870 --> 00:04:32.880 align:start position:0%
image generator prompt equals mancoon
 

00:04:32.880 --> 00:04:35.629 align:start position:0%
image generator prompt equals mancoon
gray<00:04:33.120><c> cat</c><00:04:33.380><c> sitting</c><00:04:34.380><c> down</c><00:04:34.500><c> resting</c><00:04:35.100><c> so</c><00:04:35.520><c> you</c>

00:04:35.629 --> 00:04:35.639 align:start position:0%
gray cat sitting down resting so you
 

00:04:35.639 --> 00:04:37.610 align:start position:0%
gray cat sitting down resting so you
notice<00:04:35.820><c> that</c><00:04:36.000><c> it</c><00:04:36.120><c> managed</c><00:04:36.419><c> to</c><00:04:36.540><c> delete</c><00:04:37.020><c> the</c>

00:04:37.610 --> 00:04:37.620 align:start position:0%
notice that it managed to delete the
 

00:04:37.620 --> 00:04:39.469 align:start position:0%
notice that it managed to delete the
first<00:04:37.919><c> part</c><00:04:38.160><c> of</c><00:04:38.340><c> the</c><00:04:38.460><c> prompt</c><00:04:38.759><c> two</c><00:04:39.000><c> which</c><00:04:39.300><c> is</c>

00:04:39.469 --> 00:04:39.479 align:start position:0%
first part of the prompt two which is
 

00:04:39.479 --> 00:04:40.969 align:start position:0%
first part of the prompt two which is
kind<00:04:39.600><c> of</c><00:04:39.720><c> cool</c><00:04:39.840><c> right</c><00:04:40.080><c> that</c><00:04:40.320><c> it's</c><00:04:40.620><c> understood</c>

00:04:40.969 --> 00:04:40.979 align:start position:0%
kind of cool right that it's understood
 

00:04:40.979 --> 00:04:43.370 align:start position:0%
kind of cool right that it's understood
that<00:04:41.340><c> that</c><00:04:41.520><c> was</c><00:04:41.699><c> the</c><00:04:42.060><c> instruction</c><00:04:42.180><c> to</c><00:04:43.020><c> get</c>

00:04:43.370 --> 00:04:43.380 align:start position:0%
that that was the instruction to get
 

00:04:43.380 --> 00:04:45.469 align:start position:0%
that that was the instruction to get
this<00:04:43.740><c> module</c><00:04:44.160><c> not</c><00:04:44.460><c> the</c><00:04:44.699><c> instruction</c><00:04:44.820><c> for</c>

00:04:45.469 --> 00:04:45.479 align:start position:0%
this module not the instruction for
 

00:04:45.479 --> 00:04:48.950 align:start position:0%
this module not the instruction for
passing<00:04:46.139><c> into</c><00:04:46.380><c> this</c><00:04:46.800><c> module</c><00:04:47.639><c> okay</c><00:04:48.240><c> it</c>

00:04:48.950 --> 00:04:48.960 align:start position:0%
passing into this module okay it
 

00:04:48.960 --> 00:04:52.310 align:start position:0%
passing into this module okay it
downloads<00:04:49.500><c> a</c><00:04:49.860><c> model</c><00:04:50.419><c> that</c><00:04:51.419><c> text</c><00:04:51.840><c> to</c><00:04:52.020><c> image</c>

00:04:52.310 --> 00:04:52.320 align:start position:0%
downloads a model that text to image
 

00:04:52.320 --> 00:04:53.870 align:start position:0%
downloads a model that text to image
model<00:04:52.440><c> I'm</c><00:04:52.740><c> not</c><00:04:52.919><c> sure</c><00:04:53.040><c> what</c><00:04:53.220><c> it</c><00:04:53.460><c> is</c><00:04:53.580><c> I'm</c><00:04:53.759><c> pretty</c>

00:04:53.870 --> 00:04:53.880 align:start position:0%
model I'm not sure what it is I'm pretty
 

00:04:53.880 --> 00:04:55.790 align:start position:0%
model I'm not sure what it is I'm pretty
sure<00:04:54.000><c> it's</c><00:04:54.180><c> one</c><00:04:54.720><c> of</c><00:04:54.780><c> the</c><00:04:54.900><c> diffusion</c><00:04:55.380><c> models</c>

00:04:55.790 --> 00:04:55.800 align:start position:0%
sure it's one of the diffusion models
 

00:04:55.800 --> 00:04:58.129 align:start position:0%
sure it's one of the diffusion models
that<00:04:56.160><c> they've</c><00:04:56.520><c> got</c><00:04:56.699><c> going</c><00:04:56.880><c> here</c><00:04:57.120><c> and</c><00:04:57.900><c> sure</c>

00:04:58.129 --> 00:04:58.139 align:start position:0%
that they've got going here and sure
 

00:04:58.139 --> 00:05:00.530 align:start position:0%
that they've got going here and sure
enough<00:04:58.259><c> it</c><00:04:58.800><c> makes</c><00:04:58.979><c> this</c><00:04:59.280><c> a</c><00:04:59.699><c> picture</c><00:04:59.940><c> no</c><00:05:00.300><c> this</c>

00:05:00.530 --> 00:05:00.540 align:start position:0%
enough it makes this a picture no this
 

00:05:00.540 --> 00:05:02.510 align:start position:0%
enough it makes this a picture no this
one<00:05:00.780><c> perhaps</c><00:05:01.440><c> wasn't</c><00:05:01.560><c> the</c><00:05:01.800><c> best</c><00:05:01.979><c> one</c><00:05:02.160><c> that</c><00:05:02.340><c> it</c>

00:05:02.510 --> 00:05:02.520 align:start position:0%
one perhaps wasn't the best one that it
 

00:05:02.520 --> 00:05:04.969 align:start position:0%
one perhaps wasn't the best one that it
made<00:05:02.699><c> early</c><00:05:03.120><c> on</c><00:05:03.479><c> but</c><00:05:03.780><c> anyway</c><00:05:04.080><c> it's</c><00:05:04.500><c> we've</c><00:05:04.860><c> got</c>

00:05:04.969 --> 00:05:04.979 align:start position:0%
made early on but anyway it's we've got
 

00:05:04.979 --> 00:05:08.030 align:start position:0%
made early on but anyway it's we've got
our<00:05:05.220><c> Maine</c><00:05:06.120><c> gray</c><00:05:06.600><c> cat</c><00:05:06.900><c> sitting</c><00:05:07.800><c> there</c>

00:05:08.030 --> 00:05:08.040 align:start position:0%
our Maine gray cat sitting there
 

00:05:08.040 --> 00:05:11.990 align:start position:0%
our Maine gray cat sitting there
slightly<00:05:08.940><c> dodgy</c><00:05:09.479><c> eyes</c><00:05:09.860><c> and</c><00:05:10.860><c> so</c><00:05:11.160><c> now</c><00:05:11.639><c> you</c><00:05:11.880><c> could</c>

00:05:11.990 --> 00:05:12.000 align:start position:0%
slightly dodgy eyes and so now you could
 

00:05:12.000 --> 00:05:13.909 align:start position:0%
slightly dodgy eyes and so now you could
you<00:05:12.300><c> know</c><00:05:12.360><c> that</c><00:05:12.600><c> this</c><00:05:12.780><c> is</c><00:05:12.960><c> a</c><00:05:13.199><c> one-off</c><00:05:13.560><c> sort</c><00:05:13.800><c> of</c>

00:05:13.909 --> 00:05:13.919 align:start position:0%
you know that this is a one-off sort of
 

00:05:13.919 --> 00:05:17.150 align:start position:0%
you know that this is a one-off sort of
instruction<00:05:14.160><c> so</c><00:05:14.820><c> when</c><00:05:15.000><c> you</c><00:05:15.180><c> do</c><00:05:15.360><c> agent</c><00:05:15.900><c> run</c>

00:05:17.150 --> 00:05:17.160 align:start position:0%
instruction so when you do agent run
 

00:05:17.160 --> 00:05:19.189 align:start position:0%
instruction so when you do agent run
whenever<00:05:17.759><c> you</c><00:05:17.880><c> do</c><00:05:18.120><c> agent</c><00:05:18.479><c> run</c><00:05:18.660><c> you're</c><00:05:19.020><c> just</c>

00:05:19.189 --> 00:05:19.199 align:start position:0%
whenever you do agent run you're just
 

00:05:19.199 --> 00:05:20.870 align:start position:0%
whenever you do agent run you're just
doing<00:05:19.380><c> a</c><00:05:19.620><c> sort</c><00:05:19.800><c> of</c><00:05:19.919><c> like</c><00:05:20.040><c> a</c><00:05:20.340><c> zero</c><00:05:20.699><c> shot</c>

00:05:20.870 --> 00:05:20.880 align:start position:0%
doing a sort of like a zero shot
 

00:05:20.880 --> 00:05:24.529 align:start position:0%
doing a sort of like a zero shot
instruction<00:05:21.419><c> for</c><00:05:22.380><c> this</c><00:05:22.620><c> now</c><00:05:23.280><c> it</c><00:05:23.880><c> can</c><00:05:24.360><c> actually</c>

00:05:24.529 --> 00:05:24.539 align:start position:0%
instruction for this now it can actually
 

00:05:24.539 --> 00:05:26.570 align:start position:0%
instruction for this now it can actually
do<00:05:24.960><c> when</c><00:05:25.500><c> you're</c><00:05:25.620><c> doing</c><00:05:25.860><c> this</c><00:05:26.039><c> it</c><00:05:26.400><c> will</c>

00:05:26.570 --> 00:05:26.580 align:start position:0%
do when you're doing this it will
 

00:05:26.580 --> 00:05:29.749 align:start position:0%
do when you're doing this it will
interpret<00:05:27.180><c> if</c><00:05:27.780><c> it</c><00:05:27.960><c> needs</c><00:05:28.320><c> multiple</c><00:05:28.860><c> tools</c><00:05:29.400><c> to</c>

00:05:29.749 --> 00:05:29.759 align:start position:0%
interpret if it needs multiple tools to
 

00:05:29.759 --> 00:05:32.390 align:start position:0%
interpret if it needs multiple tools to
do<00:05:29.940><c> this</c><00:05:30.120><c> so</c><00:05:30.419><c> for</c><00:05:30.600><c> example</c><00:05:30.840><c> here</c><00:05:31.139><c> I</c><00:05:31.320><c> say</c><00:05:31.500><c> I</c><00:05:32.100><c> read</c>

00:05:32.390 --> 00:05:32.400 align:start position:0%
do this so for example here I say I read
 

00:05:32.400 --> 00:05:36.409 align:start position:0%
do this so for example here I say I read
out<00:05:32.820><c> loud</c><00:05:33.479><c> the</c><00:05:34.020><c> summary</c><00:05:34.320><c> of</c><00:05:34.560><c> techcrunch.com</c>

00:05:36.409 --> 00:05:36.419 align:start position:0%
out loud the summary of techcrunch.com
 

00:05:36.419 --> 00:05:38.390 align:start position:0%
out loud the summary of techcrunch.com
and<00:05:37.020><c> the</c><00:05:37.440><c> other</c><00:05:37.620><c> thing</c><00:05:37.740><c> I've</c><00:05:37.919><c> changed</c><00:05:38.280><c> here</c>

00:05:38.390 --> 00:05:38.400 align:start position:0%
and the other thing I've changed here
 

00:05:38.400 --> 00:05:41.029 align:start position:0%
and the other thing I've changed here
now<00:05:38.639><c> is</c><00:05:38.759><c> I</c><00:05:38.940><c> put</c><00:05:39.120><c> return</c><00:05:39.419><c> code</c><00:05:39.840><c> equals</c><00:05:40.380><c> true</c><00:05:40.560><c> so</c>

00:05:41.029 --> 00:05:41.039 align:start position:0%
now is I put return code equals true so
 

00:05:41.039 --> 00:05:43.189 align:start position:0%
now is I put return code equals true so
that<00:05:41.160><c> we</c><00:05:41.280><c> can</c><00:05:41.400><c> actually</c><00:05:41.580><c> sort</c><00:05:41.820><c> of</c><00:05:42.000><c> see</c><00:05:42.419><c> what</c>

00:05:43.189 --> 00:05:43.199 align:start position:0%
that we can actually sort of see what
 

00:05:43.199 --> 00:05:45.409 align:start position:0%
that we can actually sort of see what
it's<00:05:43.380><c> actually</c><00:05:43.560><c> doing</c><00:05:43.979><c> in</c><00:05:44.340><c> here</c><00:05:44.520><c> when</c><00:05:45.060><c> we</c><00:05:45.240><c> do</c>

00:05:45.409 --> 00:05:45.419 align:start position:0%
it's actually doing in here when we do
 

00:05:45.419 --> 00:05:47.629 align:start position:0%
it's actually doing in here when we do
this<00:05:45.600><c> so</c><00:05:45.960><c> okay</c><00:05:46.259><c> it's</c><00:05:46.560><c> got</c><00:05:46.740><c> its</c><00:05:47.039><c> explanation</c>

00:05:47.629 --> 00:05:47.639 align:start position:0%
this so okay it's got its explanation
 

00:05:47.639 --> 00:05:50.390 align:start position:0%
this so okay it's got its explanation
that<00:05:48.539><c> I</c><00:05:48.780><c> will</c><00:05:48.900><c> use</c><00:05:49.080><c> the</c><00:05:49.320><c> following</c><00:05:49.680><c> tools</c><00:05:50.039><c> text</c>

00:05:50.390 --> 00:05:50.400 align:start position:0%
that I will use the following tools text
 

00:05:50.400 --> 00:05:52.850 align:start position:0%
that I will use the following tools text
downloader<00:05:51.120><c> to</c><00:05:51.840><c> download</c><00:05:51.960><c> the</c><00:05:52.259><c> text</c><00:05:52.380><c> from</c><00:05:52.620><c> the</c>

00:05:52.850 --> 00:05:52.860 align:start position:0%
downloader to download the text from the
 

00:05:52.860 --> 00:05:55.670 align:start position:0%
downloader to download the text from the
website<00:05:53.479><c> summarizer</c><00:05:54.479><c> to</c><00:05:54.900><c> create</c><00:05:55.080><c> a</c><00:05:55.380><c> summary</c>

00:05:55.670 --> 00:05:55.680 align:start position:0%
website summarizer to create a summary
 

00:05:55.680 --> 00:05:58.249 align:start position:0%
website summarizer to create a summary
of<00:05:55.919><c> the</c><00:05:56.039><c> text</c><00:05:56.220><c> and</c><00:05:56.940><c> text</c><00:05:57.180><c> reader</c><00:05:57.660><c> to</c><00:05:57.900><c> read</c><00:05:58.080><c> it</c>

00:05:58.249 --> 00:05:58.259 align:start position:0%
of the text and text reader to read it
 

00:05:58.259 --> 00:06:01.129 align:start position:0%
of the text and text reader to read it
out<00:05:58.440><c> aloud</c><00:05:58.919><c> then</c><00:05:59.759><c> we</c><00:05:59.940><c> can</c><00:06:00.060><c> see</c><00:06:00.240><c> that</c><00:06:00.479><c> the</c><00:06:00.840><c> code</c>

00:06:01.129 --> 00:06:01.139 align:start position:0%
out aloud then we can see that the code
 

00:06:01.139 --> 00:06:04.430 align:start position:0%
out aloud then we can see that the code
generated<00:06:01.680><c> is</c><00:06:02.220><c> going</c><00:06:02.400><c> to</c><00:06:02.580><c> be</c><00:06:02.880><c> text</c><00:06:03.720><c> downloader</c>

00:06:04.430 --> 00:06:04.440 align:start position:0%
generated is going to be text downloader
 

00:06:04.440 --> 00:06:06.590 align:start position:0%
generated is going to be text downloader
and<00:06:04.979><c> it's</c><00:06:05.100><c> getting</c><00:06:05.340><c> it</c><00:06:05.580><c> from</c><00:06:05.759><c> techcrunch.com</c>

00:06:06.590 --> 00:06:06.600 align:start position:0%
and it's getting it from techcrunch.com
 

00:06:06.600 --> 00:06:10.610 align:start position:0%
and it's getting it from techcrunch.com
I<00:06:07.440><c> deliberately</c><00:06:08.039><c> left</c><00:06:08.460><c> out</c><00:06:08.699><c> the</c><00:06:09.120><c> https</c><00:06:09.900><c> to</c><00:06:10.500><c> see</c>

00:06:10.610 --> 00:06:10.620 align:start position:0%
I deliberately left out the https to see
 

00:06:10.620 --> 00:06:12.529 align:start position:0%
I deliberately left out the https to see
would<00:06:10.860><c> it</c><00:06:11.039><c> get</c><00:06:11.160><c> it</c><00:06:11.280><c> yes</c><00:06:11.699><c> it's</c><00:06:12.060><c> gotten</c><00:06:12.419><c> that</c>

00:06:12.529 --> 00:06:12.539 align:start position:0%
would it get it yes it's gotten that
 

00:06:12.539 --> 00:06:14.870 align:start position:0%
would it get it yes it's gotten that
quite<00:06:12.720><c> nicely</c><00:06:13.139><c> it</c><00:06:13.800><c> understands</c><00:06:14.280><c> then</c><00:06:14.460><c> that</c><00:06:14.699><c> it</c>

00:06:14.870 --> 00:06:14.880 align:start position:0%
quite nicely it understands then that it
 

00:06:14.880 --> 00:06:16.790 align:start position:0%
quite nicely it understands then that it
needs<00:06:15.120><c> to</c><00:06:15.180><c> basically</c><00:06:15.600><c> summarize</c><00:06:16.199><c> that</c><00:06:16.560><c> text</c>

00:06:16.790 --> 00:06:16.800 align:start position:0%
needs to basically summarize that text
 

00:06:16.800 --> 00:06:20.629 align:start position:0%
needs to basically summarize that text
it<00:06:17.639><c> actually</c><00:06:18.060><c> prints</c><00:06:18.720><c> out</c><00:06:19.020><c> a</c><00:06:19.560><c> summary</c><00:06:20.039><c> here</c><00:06:20.400><c> if</c>

00:06:20.629 --> 00:06:20.639 align:start position:0%
it actually prints out a summary here if
 

00:06:20.639 --> 00:06:22.670 align:start position:0%
it actually prints out a summary here if
we<00:06:20.759><c> see</c><00:06:20.940><c> here</c><00:06:21.240><c> the</c><00:06:21.539><c> summary</c><00:06:22.020><c> being</c><00:06:22.259><c> printed</c>

00:06:22.670 --> 00:06:22.680 align:start position:0%
we see here the summary being printed
 

00:06:22.680 --> 00:06:26.950 align:start position:0%
we see here the summary being printed
out<00:06:22.860><c> and</c><00:06:23.699><c> then</c><00:06:23.940><c> it</c><00:06:24.720><c> basically</c><00:06:25.199><c> downloaded</c><00:06:25.979><c> a</c>

00:06:26.950 --> 00:06:26.960 align:start position:0%
out and then it basically downloaded a
 

00:06:26.960 --> 00:06:29.749 align:start position:0%
out and then it basically downloaded a
text-to-speech<00:06:27.960><c> T5</c><00:06:28.440><c> model</c>

00:06:29.749 --> 00:06:29.759 align:start position:0%
text-to-speech T5 model
 

00:06:29.759 --> 00:06:32.029 align:start position:0%
text-to-speech T5 model
and<00:06:30.419><c> that's</c><00:06:30.660><c> what</c><00:06:30.840><c> we've</c><00:06:31.080><c> got</c><00:06:31.199><c> here</c><00:06:31.440><c> so</c><00:06:31.680><c> if</c><00:06:31.860><c> we</c>

00:06:32.029 --> 00:06:32.039 align:start position:0%
and that's what we've got here so if we
 

00:06:32.039 --> 00:06:33.590 align:start position:0%
and that's what we've got here so if we
you<00:06:32.340><c> can</c><00:06:32.520><c> see</c><00:06:32.639><c> here</c><00:06:32.819><c> we've</c><00:06:33.000><c> got</c><00:06:33.120><c> this</c><00:06:33.360><c> you</c><00:06:33.539><c> know</c>

00:06:33.590 --> 00:06:33.600 align:start position:0%
you can see here we've got this you know
 

00:06:33.600 --> 00:06:36.050 align:start position:0%
you can see here we've got this you know
Google<00:06:33.840><c> I</c><00:06:34.139><c> O</c><00:06:34.380><c> is</c><00:06:34.620><c> a</c><00:06:34.800><c> wrap</c><00:06:34.979><c> bring</c><00:06:35.520><c> that</c><00:06:35.699><c> back</c><00:06:35.940><c> to</c>

00:06:36.050 --> 00:06:36.060 align:start position:0%
Google I O is a wrap bring that back to
 

00:06:36.060 --> 00:06:37.969 align:start position:0%
Google I O is a wrap bring that back to
the<00:06:36.240><c> start</c><00:06:36.419><c> and</c><00:06:36.780><c> if</c><00:06:36.900><c> I</c><00:06:37.020><c> press</c><00:06:37.199><c> play</c><00:06:37.440><c> we</c><00:06:37.860><c> can</c>

00:06:37.969 --> 00:06:37.979 align:start position:0%
the start and if I press play we can
 

00:06:37.979 --> 00:06:40.610 align:start position:0%
the start and if I press play we can
listen<00:06:38.100><c> to</c><00:06:38.340><c> it</c><00:06:38.580><c> Google</c><00:06:39.060><c> I</c><00:06:39.419><c> O</c><00:06:39.600><c> is</c><00:06:39.900><c> a</c><00:06:40.020><c> wrap</c><00:06:40.259><c> Elon</c>

00:06:40.610 --> 00:06:40.620 align:start position:0%
listen to it Google I O is a wrap Elon
 

00:06:40.620 --> 00:06:41.450 align:start position:0%
listen to it Google I O is a wrap Elon
Musk<00:06:40.800><c> has</c>

00:06:41.450 --> 00:06:41.460 align:start position:0%
Musk has
 

00:06:41.460 --> 00:06:43.550 align:start position:0%
Musk has
the<00:06:41.759><c> CEO</c><00:06:42.060><c> for</c><00:06:42.240><c> Twitter</c><00:06:42.419><c> dungeons</c><00:06:43.259><c> dragons</c>

00:06:43.550 --> 00:06:43.560 align:start position:0%
the CEO for Twitter dungeons dragons
 

00:06:43.560 --> 00:06:46.430 align:start position:0%
the CEO for Twitter dungeons dragons
gets<00:06:44.100><c> its</c><00:06:44.340><c> very</c><00:06:44.400><c> own</c><00:06:44.580><c> streaming</c><00:06:45.060><c> Channel</c>

00:06:46.430 --> 00:06:46.440 align:start position:0%
gets its very own streaming Channel
 

00:06:46.440 --> 00:06:47.990 align:start position:0%
gets its very own streaming Channel
and<00:06:46.800><c> it's</c><00:06:46.919><c> basically</c><00:06:47.220><c> just</c><00:06:47.400><c> reading</c><00:06:47.699><c> out</c><00:06:47.819><c> the</c>

00:06:47.990 --> 00:06:48.000 align:start position:0%
and it's basically just reading out the
 

00:06:48.000 --> 00:06:50.809 align:start position:0%
and it's basically just reading out the
headlines<00:06:48.419><c> that</c><00:06:49.080><c> were</c><00:06:49.259><c> on</c><00:06:49.440><c> that</c><00:06:49.680><c> page</c><00:06:49.979><c> still</c>

00:06:50.809 --> 00:06:50.819 align:start position:0%
headlines that were on that page still
 

00:06:50.819 --> 00:06:52.249 align:start position:0%
headlines that were on that page still
pretty<00:06:51.000><c> cool</c><00:06:51.180><c> though</c><00:06:51.419><c> that</c><00:06:51.660><c> we</c><00:06:51.780><c> were</c><00:06:51.960><c> able</c><00:06:52.199><c> to</c>

00:06:52.249 --> 00:06:52.259 align:start position:0%
pretty cool though that we were able to
 

00:06:52.259 --> 00:06:54.890 align:start position:0%
pretty cool though that we were able to
do<00:06:52.500><c> all</c><00:06:52.800><c> of</c><00:06:52.919><c> this</c><00:06:53.220><c> with</c><00:06:54.060><c> just</c><00:06:54.360><c> one</c><00:06:54.660><c> little</c>

00:06:54.890 --> 00:06:54.900 align:start position:0%
do all of this with just one little
 

00:06:54.900 --> 00:06:56.990 align:start position:0%
do all of this with just one little
string<00:06:55.380><c> saying</c><00:06:55.800><c> read</c><00:06:56.039><c> out</c><00:06:56.280><c> loud</c><00:06:56.460><c> the</c><00:06:56.699><c> summary</c>

00:06:56.990 --> 00:06:57.000 align:start position:0%
string saying read out loud the summary
 

00:06:57.000 --> 00:06:59.570 align:start position:0%
string saying read out loud the summary
of<00:06:57.240><c> techcrunch.com</c><00:06:58.160><c> now</c><00:06:59.160><c> their</c>

00:06:59.570 --> 00:06:59.580 align:start position:0%
of techcrunch.com now their
 

00:06:59.580 --> 00:07:01.249 align:start position:0%
of techcrunch.com now their
text-to-speech<00:07:00.300><c> you</c><00:07:00.419><c> could</c><00:07:00.539><c> imagine</c><00:07:00.720><c> you</c>

00:07:01.249 --> 00:07:01.259 align:start position:0%
text-to-speech you could imagine you
 

00:07:01.259 --> 00:07:03.050 align:start position:0%
text-to-speech you could imagine you
could<00:07:01.440><c> look</c><00:07:02.039><c> at</c><00:07:02.160><c> writing</c><00:07:02.520><c> your</c><00:07:02.639><c> own</c><00:07:02.759><c> tools</c>

00:07:03.050 --> 00:07:03.060 align:start position:0%
could look at writing your own tools
 

00:07:03.060 --> 00:07:04.550 align:start position:0%
could look at writing your own tools
later<00:07:03.240><c> on</c><00:07:03.479><c> but</c><00:07:03.660><c> you</c><00:07:03.780><c> could</c><00:07:03.900><c> imagine</c><00:07:04.020><c> that</c><00:07:04.319><c> if</c>

00:07:04.550 --> 00:07:04.560 align:start position:0%
later on but you could imagine that if
 

00:07:04.560 --> 00:07:06.890 align:start position:0%
later on but you could imagine that if
you<00:07:04.680><c> wanted</c><00:07:04.860><c> to</c><00:07:05.100><c> use</c><00:07:05.280><c> a</c><00:07:05.580><c> better</c><00:07:05.759><c> TTS</c><00:07:06.660><c> system</c>

00:07:06.890 --> 00:07:06.900 align:start position:0%
you wanted to use a better TTS system
 

00:07:06.900 --> 00:07:09.230 align:start position:0%
you wanted to use a better TTS system
you<00:07:07.199><c> could</c><00:07:07.380><c> certainly</c><00:07:07.740><c> ping</c><00:07:08.520><c> something</c><00:07:08.819><c> and</c>

00:07:09.230 --> 00:07:09.240 align:start position:0%
you could certainly ping something and
 

00:07:09.240 --> 00:07:12.170 align:start position:0%
you could certainly ping something and
write<00:07:09.360><c> a</c><00:07:09.539><c> tool</c><00:07:09.780><c> to</c><00:07:09.900><c> actually</c><00:07:10.139><c> do</c><00:07:10.500><c> that</c><00:07:10.740><c> in</c><00:07:11.340><c> here</c>

00:07:12.170 --> 00:07:12.180 align:start position:0%
write a tool to actually do that in here
 

00:07:12.180 --> 00:07:14.689 align:start position:0%
write a tool to actually do that in here
so<00:07:12.840><c> this</c><00:07:13.199><c> is</c><00:07:13.259><c> some</c><00:07:13.500><c> of</c><00:07:13.620><c> the</c><00:07:13.740><c> basic</c><00:07:14.100><c> use</c><00:07:14.340><c> of</c><00:07:14.520><c> it</c>

00:07:14.689 --> 00:07:14.699 align:start position:0%
so this is some of the basic use of it
 

00:07:14.699 --> 00:07:17.390 align:start position:0%
so this is some of the basic use of it
now<00:07:15.419><c> they</c><00:07:15.780><c> have</c><00:07:15.960><c> a</c><00:07:16.139><c> second</c><00:07:16.319><c> way</c><00:07:16.800><c> of</c><00:07:16.919><c> doing</c><00:07:17.100><c> it</c>

00:07:17.390 --> 00:07:17.400 align:start position:0%
now they have a second way of doing it
 

00:07:17.400 --> 00:07:19.610 align:start position:0%
now they have a second way of doing it
where<00:07:17.759><c> they</c><00:07:18.180><c> have</c><00:07:18.300><c> it</c><00:07:18.539><c> in</c><00:07:18.840><c> chat</c><00:07:19.020><c> mode</c><00:07:19.259><c> so</c><00:07:19.500><c> they</c>

00:07:19.610 --> 00:07:19.620 align:start position:0%
where they have it in chat mode so they
 

00:07:19.620 --> 00:07:22.010 align:start position:0%
where they have it in chat mode so they
talk<00:07:19.740><c> about</c><00:07:19.860><c> you</c><00:07:20.160><c> know</c><00:07:20.280><c> that</c><00:07:20.520><c> that</c><00:07:20.940><c> run</c><00:07:21.479><c> mode</c>

00:07:22.010 --> 00:07:22.020 align:start position:0%
talk about you know that that run mode
 

00:07:22.020 --> 00:07:24.469 align:start position:0%
talk about you know that that run mode
doesn't<00:07:22.800><c> keep</c><00:07:23.039><c> a</c><00:07:23.280><c> memory</c><00:07:23.580><c> across</c><00:07:24.000><c> this</c><00:07:24.240><c> so</c>

00:07:24.469 --> 00:07:24.479 align:start position:0%
doesn't keep a memory across this so
 

00:07:24.479 --> 00:07:26.990 align:start position:0%
doesn't keep a memory across this so
this<00:07:24.840><c> is</c><00:07:24.960><c> sort</c><00:07:25.440><c> of</c><00:07:25.560><c> like</c><00:07:25.800><c> the</c><00:07:26.099><c> the</c><00:07:26.340><c> zero</c><00:07:26.819><c> shot</c>

00:07:26.990 --> 00:07:27.000 align:start position:0%
this is sort of like the the zero shot
 

00:07:27.000 --> 00:07:30.290 align:start position:0%
this is sort of like the the zero shot
agents<00:07:27.960><c> where</c><00:07:28.800><c> we</c><00:07:29.460><c> don't</c><00:07:29.580><c> have</c><00:07:29.759><c> any</c><00:07:29.940><c> sort</c><00:07:30.180><c> of</c>

00:07:30.290 --> 00:07:30.300 align:start position:0%
agents where we don't have any sort of
 

00:07:30.300 --> 00:07:32.210 align:start position:0%
agents where we don't have any sort of
memory<00:07:30.660><c> conversation</c><00:07:31.319><c> memory</c><00:07:31.740><c> going</c><00:07:31.979><c> on</c>

00:07:32.210 --> 00:07:32.220 align:start position:0%
memory conversation memory going on
 

00:07:32.220 --> 00:07:34.730 align:start position:0%
memory conversation memory going on
whereas<00:07:32.819><c> chat</c><00:07:33.240><c> does</c><00:07:33.900><c> have</c><00:07:34.080><c> the</c><00:07:34.319><c> conversation</c>

00:07:34.730 --> 00:07:34.740 align:start position:0%
whereas chat does have the conversation
 

00:07:34.740 --> 00:07:37.430 align:start position:0%
whereas chat does have the conversation
memory<00:07:35.280><c> going</c><00:07:35.580><c> on</c><00:07:35.819><c> now</c><00:07:36.360><c> they</c><00:07:36.539><c> talk</c><00:07:36.720><c> about</c><00:07:36.900><c> that</c>

00:07:37.430 --> 00:07:37.440 align:start position:0%
memory going on now they talk about that
 

00:07:37.440 --> 00:07:40.070 align:start position:0%
memory going on now they talk about that
the<00:07:38.099><c> run</c><00:07:38.699><c> is</c><00:07:39.000><c> sort</c><00:07:39.180><c> of</c><00:07:39.300><c> better</c><00:07:39.479><c> for</c><00:07:39.840><c> things</c>

00:07:40.070 --> 00:07:40.080 align:start position:0%
the run is sort of better for things
 

00:07:40.080 --> 00:07:43.010 align:start position:0%
the run is sort of better for things
where<00:07:40.440><c> you've</c><00:07:40.680><c> got</c><00:07:40.860><c> multiple</c><00:07:41.340><c> operations</c><00:07:42.020><c> at</c>

00:07:43.010 --> 00:07:43.020 align:start position:0%
where you've got multiple operations at
 

00:07:43.020 --> 00:07:45.170 align:start position:0%
where you've got multiple operations at
once<00:07:43.319><c> and</c><00:07:43.860><c> chat</c><00:07:44.099><c> tends</c><00:07:44.520><c> to</c><00:07:44.580><c> work</c><00:07:44.699><c> better</c><00:07:44.940><c> when</c>

00:07:45.170 --> 00:07:45.180 align:start position:0%
once and chat tends to work better when
 

00:07:45.180 --> 00:07:47.689 align:start position:0%
once and chat tends to work better when
you're<00:07:45.300><c> doing</c><00:07:45.539><c> individual</c><00:07:46.500><c> instructions</c><00:07:47.340><c> one</c>

00:07:47.689 --> 00:07:47.699 align:start position:0%
you're doing individual instructions one
 

00:07:47.699 --> 00:07:50.029 align:start position:0%
you're doing individual instructions one
at<00:07:47.880><c> a</c><00:07:48.060><c> time</c><00:07:48.180><c> kind</c><00:07:48.539><c> of</c><00:07:48.660><c> thing</c><00:07:48.780><c> so</c><00:07:49.380><c> we</c><00:07:49.560><c> do</c><00:07:49.740><c> the</c>

00:07:50.029 --> 00:07:50.039 align:start position:0%
at a time kind of thing so we do the
 

00:07:50.039 --> 00:07:53.089 align:start position:0%
at a time kind of thing so we do the
agent<00:07:50.759><c> Chat</c><00:07:51.120><c> Show</c><00:07:51.780><c> me</c><00:07:51.960><c> an</c><00:07:52.080><c> image</c><00:07:52.319><c> of</c><00:07:52.560><c> a</c><00:07:52.740><c> ginger</c>

00:07:53.089 --> 00:07:53.099 align:start position:0%
agent Chat Show me an image of a ginger
 

00:07:53.099 --> 00:07:55.850 align:start position:0%
agent Chat Show me an image of a ginger
Maine<00:07:53.699><c> cat</c><00:07:53.940><c> and</c><00:07:54.720><c> you</c><00:07:54.960><c> can</c><00:07:55.020><c> see</c><00:07:55.259><c> that</c><00:07:55.500><c> the</c>

00:07:55.850 --> 00:07:55.860 align:start position:0%
Maine cat and you can see that the
 

00:07:55.860 --> 00:07:58.070 align:start position:0%
Maine cat and you can see that the
output<00:07:56.220><c> that</c><00:07:56.520><c> it's</c><00:07:56.759><c> doing</c><00:07:57.120><c> is</c><00:07:57.419><c> getting</c><00:07:57.660><c> the</c>

00:07:58.070 --> 00:07:58.080 align:start position:0%
output that it's doing is getting the
 

00:07:58.080 --> 00:07:59.870 align:start position:0%
output that it's doing is getting the
image<00:07:58.380><c> generated</c><00:07:58.800><c> just</c><00:07:59.099><c> like</c><00:07:59.220><c> before</c><00:07:59.460><c> it</c>

00:07:59.870 --> 00:07:59.880 align:start position:0%
image generated just like before it
 

00:07:59.880 --> 00:08:01.730 align:start position:0%
image generated just like before it
generates<00:08:00.300><c> the</c><00:08:00.539><c> cat</c><00:08:00.720><c> it</c><00:08:01.380><c> didn't</c><00:08:01.500><c> actually</c>

00:08:01.730 --> 00:08:01.740 align:start position:0%
generates the cat it didn't actually
 

00:08:01.740 --> 00:08:03.230 align:start position:0%
generates the cat it didn't actually
need<00:08:01.919><c> to</c><00:08:02.039><c> load</c><00:08:02.280><c> the</c><00:08:02.400><c> model</c><00:08:02.580><c> in</c><00:08:02.819><c> this</c><00:08:03.060><c> time</c>

00:08:03.230 --> 00:08:03.240 align:start position:0%
need to load the model in this time
 

00:08:03.240 --> 00:08:04.610 align:start position:0%
need to load the model in this time
because<00:08:03.419><c> it's</c><00:08:03.599><c> already</c><00:08:03.780><c> had</c><00:08:04.020><c> the</c><00:08:04.199><c> model</c><00:08:04.319><c> in</c>

00:08:04.610 --> 00:08:04.620 align:start position:0%
because it's already had the model in
 

00:08:04.620 --> 00:08:06.710 align:start position:0%
because it's already had the model in
there<00:08:04.800><c> so</c><00:08:05.400><c> that's</c><00:08:05.880><c> that's</c><00:08:06.120><c> worked</c><00:08:06.479><c> out</c><00:08:06.599><c> well</c>

00:08:06.710 --> 00:08:06.720 align:start position:0%
there so that's that's worked out well
 

00:08:06.720 --> 00:08:09.230 align:start position:0%
there so that's that's worked out well
so<00:08:06.960><c> there's</c><00:08:07.199><c> our</c><00:08:07.440><c> cat</c><00:08:07.740><c> we</c><00:08:08.280><c> can</c><00:08:08.400><c> also</c><00:08:08.699><c> transform</c>

00:08:09.230 --> 00:08:09.240 align:start position:0%
so there's our cat we can also transform
 

00:08:09.240 --> 00:08:11.689 align:start position:0%
so there's our cat we can also transform
images<00:08:09.539><c> so</c><00:08:10.020><c> it's</c><00:08:10.139><c> got</c><00:08:10.259><c> a</c><00:08:10.440><c> module</c><00:08:10.800><c> tool</c><00:08:11.099><c> or</c><00:08:11.520><c> a</c>

00:08:11.689 --> 00:08:11.699 align:start position:0%
images so it's got a module tool or a
 

00:08:11.699 --> 00:08:13.490 align:start position:0%
images so it's got a module tool or a
tool<00:08:11.819><c> to</c><00:08:11.940><c> trans</c><00:08:12.180><c> storm</c><00:08:12.539><c> images</c><00:08:12.840><c> so</c><00:08:13.139><c> if</c><00:08:13.259><c> I</c><00:08:13.380><c> say</c>

00:08:13.490 --> 00:08:13.500 align:start position:0%
tool to trans storm images so if I say
 

00:08:13.500 --> 00:08:15.170 align:start position:0%
tool to trans storm images so if I say
transform<00:08:14.160><c> the</c><00:08:14.340><c> image</c><00:08:14.580><c> so</c><00:08:14.819><c> that</c><00:08:15.000><c> the</c>

00:08:15.170 --> 00:08:15.180 align:start position:0%
transform the image so that the
 

00:08:15.180 --> 00:08:17.629 align:start position:0%
transform the image so that the
background<00:08:15.479><c> is</c><00:08:15.780><c> in</c><00:08:16.020><c> the</c><00:08:16.199><c> snow</c>

00:08:17.629 --> 00:08:17.639 align:start position:0%
background is in the snow
 

00:08:17.639 --> 00:08:20.270 align:start position:0%
background is in the snow
it<00:08:18.360><c> does</c><00:08:18.599><c> an</c><00:08:18.840><c> okay</c><00:08:19.020><c> job</c><00:08:19.379><c> it</c><00:08:19.620><c> seems</c><00:08:19.979><c> to</c><00:08:20.099><c> have</c>

00:08:20.270 --> 00:08:20.280 align:start position:0%
it does an okay job it seems to have
 

00:08:20.280 --> 00:08:23.029 align:start position:0%
it does an okay job it seems to have
just<00:08:21.000><c> Overexposed</c><00:08:21.960><c> it</c><00:08:22.080><c> but</c><00:08:22.259><c> we've</c><00:08:22.680><c> got</c><00:08:22.800><c> the</c>

00:08:23.029 --> 00:08:23.039 align:start position:0%
just Overexposed it but we've got the
 

00:08:23.039 --> 00:08:25.070 align:start position:0%
just Overexposed it but we've got the
white<00:08:23.220><c> sort</c><00:08:23.580><c> of</c><00:08:23.699><c> snowy</c><00:08:24.060><c> look</c><00:08:24.180><c> I</c><00:08:24.599><c> guess</c><00:08:24.780><c> a</c><00:08:24.960><c> bit</c>

00:08:25.070 --> 00:08:25.080 align:start position:0%
white sort of snowy look I guess a bit
 

00:08:25.080 --> 00:08:26.869 align:start position:0%
white sort of snowy look I guess a bit
more<00:08:25.259><c> play</c><00:08:25.860><c> around</c><00:08:26.039><c> with</c><00:08:26.220><c> it</c><00:08:26.400><c> yourself</c><00:08:26.520><c> and</c>

00:08:26.869 --> 00:08:26.879 align:start position:0%
more play around with it yourself and
 

00:08:26.879 --> 00:08:29.150 align:start position:0%
more play around with it yourself and
see<00:08:27.060><c> what</c><00:08:27.240><c> what</c><00:08:27.419><c> it</c><00:08:27.660><c> can</c><00:08:27.780><c> do</c><00:08:27.960><c> it</c><00:08:28.620><c> can</c><00:08:28.800><c> also</c><00:08:28.979><c> ask</c>

00:08:29.150 --> 00:08:29.160 align:start position:0%
see what what it can do it can also ask
 

00:08:29.160 --> 00:08:31.249 align:start position:0%
see what what it can do it can also ask
you<00:08:29.340><c> to</c><00:08:29.580><c> make</c><00:08:29.759><c> a</c><00:08:30.060><c> mask</c><00:08:30.240><c> of</c><00:08:30.539><c> something</c><00:08:30.780><c> and</c><00:08:31.139><c> it</c>

00:08:31.249 --> 00:08:31.259 align:start position:0%
you to make a mask of something and it
 

00:08:31.259 --> 00:08:33.469 align:start position:0%
you to make a mask of something and it
will<00:08:31.379><c> make</c><00:08:31.500><c> a</c><00:08:31.740><c> mask</c><00:08:31.919><c> of</c><00:08:32.219><c> it</c><00:08:32.399><c> so</c><00:08:33.120><c> that's</c>

00:08:33.469 --> 00:08:33.479 align:start position:0%
will make a mask of it so that's
 

00:08:33.479 --> 00:08:35.029 align:start position:0%
will make a mask of it so that's
something<00:08:33.719><c> that</c><00:08:33.959><c> you</c><00:08:34.080><c> could</c><00:08:34.200><c> use</c><00:08:34.380><c> too</c><00:08:34.620><c> if</c><00:08:34.860><c> you</c>

00:08:35.029 --> 00:08:35.039 align:start position:0%
something that you could use too if you
 

00:08:35.039 --> 00:08:37.130 align:start position:0%
something that you could use too if you
wanted<00:08:35.219><c> to</c><00:08:35.520><c> if</c><00:08:36.180><c> we're</c><00:08:36.300><c> going</c><00:08:36.479><c> to</c><00:08:36.719><c> sort</c><00:08:37.020><c> of</c>

00:08:37.130 --> 00:08:37.140 align:start position:0%
wanted to if we're going to sort of
 

00:08:37.140 --> 00:08:39.469 align:start position:0%
wanted to if we're going to sort of
terminate<00:08:37.680><c> the</c><00:08:37.979><c> memory</c><00:08:38.339><c> from</c><00:08:38.880><c> a</c><00:08:39.000><c> chat</c><00:08:39.180><c> session</c>

00:08:39.469 --> 00:08:39.479 align:start position:0%
terminate the memory from a chat session
 

00:08:39.479 --> 00:08:41.870 align:start position:0%
terminate the memory from a chat session
we<00:08:40.200><c> just</c><00:08:40.380><c> say</c><00:08:40.560><c> prepare</c><00:08:40.919><c> for</c><00:08:41.159><c> a</c><00:08:41.279><c> new</c><00:08:41.459><c> chat</c><00:08:41.700><c> and</c>

00:08:41.870 --> 00:08:41.880 align:start position:0%
we just say prepare for a new chat and
 

00:08:41.880 --> 00:08:43.909 align:start position:0%
we just say prepare for a new chat and
that<00:08:42.060><c> will</c><00:08:42.180><c> basically</c><00:08:42.479><c> just</c><00:08:42.719><c> re-init</c><00:08:43.560><c> the</c>

00:08:43.909 --> 00:08:43.919 align:start position:0%
that will basically just re-init the
 

00:08:43.919 --> 00:08:46.130 align:start position:0%
that will basically just re-init the
memory<00:08:44.219><c> so</c><00:08:44.880><c> that</c><00:08:45.120><c> there's</c><00:08:45.360><c> nothing</c><00:08:45.660><c> in</c><00:08:45.959><c> there</c>

00:08:46.130 --> 00:08:46.140 align:start position:0%
memory so that there's nothing in there
 

00:08:46.140 --> 00:08:48.470 align:start position:0%
memory so that there's nothing in there
now<00:08:46.500><c> I</c><00:08:46.740><c> could</c><00:08:46.860><c> have</c><00:08:46.980><c> come</c><00:08:47.339><c> in</c><00:08:47.519><c> here</c><00:08:47.700><c> we'll</c><00:08:48.120><c> see</c>

00:08:48.470 --> 00:08:48.480 align:start position:0%
now I could have come in here we'll see
 

00:08:48.480 --> 00:08:50.449 align:start position:0%
now I could have come in here we'll see
in<00:08:48.720><c> a</c><00:08:48.839><c> second</c><00:08:49.019><c> for</c><00:08:49.500><c> another</c><00:08:49.980><c> thing</c><00:08:50.220><c> but</c><00:08:50.339><c> I</c>

00:08:50.449 --> 00:08:50.459 align:start position:0%
in a second for another thing but I
 

00:08:50.459 --> 00:08:52.190 align:start position:0%
in a second for another thing but I
could<00:08:50.580><c> come</c><00:08:50.820><c> in</c><00:08:50.940><c> here</c><00:08:51.060><c> and</c><00:08:51.600><c> asked</c><00:08:51.839><c> to</c><00:08:51.899><c> then</c><00:08:52.019><c> to</c>

00:08:52.190 --> 00:08:52.200 align:start position:0%
could come in here and asked to then to
 

00:08:52.200 --> 00:08:55.250 align:start position:0%
could come in here and asked to then to
write<00:08:52.320><c> a</c><00:08:52.500><c> description</c><00:08:52.860><c> about</c><00:08:53.220><c> this</c><00:08:53.700><c> image</c><00:08:54.260><c> and</c>

00:08:55.250 --> 00:08:55.260 align:start position:0%
write a description about this image and
 

00:08:55.260 --> 00:08:57.590 align:start position:0%
write a description about this image and
it<00:08:55.920><c> would</c><00:08:56.100><c> have</c><00:08:56.220><c> remembered</c><00:08:56.640><c> that</c><00:08:56.940><c> this</c><00:08:57.180><c> image</c>

00:08:57.590 --> 00:08:57.600 align:start position:0%
it would have remembered that this image
 

00:08:57.600 --> 00:09:00.889 align:start position:0%
it would have remembered that this image
was<00:08:57.959><c> what</c><00:08:58.260><c> we</c><00:08:58.440><c> had</c><00:08:58.620><c> in</c><00:08:59.279><c> memory</c><00:08:59.760><c> because</c><00:09:00.180><c> of</c><00:09:00.660><c> the</c>

00:09:00.889 --> 00:09:00.899 align:start position:0%
was what we had in memory because of the
 

00:09:00.899 --> 00:09:03.590 align:start position:0%
was what we had in memory because of the
chat<00:09:01.140><c> memory</c><00:09:01.740><c> system</c><00:09:02.040><c> in</c><00:09:02.640><c> there</c><00:09:02.820><c> so</c><00:09:03.240><c> going</c>

00:09:03.590 --> 00:09:03.600 align:start position:0%
chat memory system in there so going
 

00:09:03.600 --> 00:09:05.630 align:start position:0%
chat memory system in there so going
from<00:09:03.779><c> one</c><00:09:04.019><c> task</c><00:09:04.200><c> to</c><00:09:04.500><c> the</c><00:09:04.620><c> other</c><00:09:04.740><c> this</c><00:09:05.339><c> memory</c>

00:09:05.630 --> 00:09:05.640 align:start position:0%
from one task to the other this memory
 

00:09:05.640 --> 00:09:08.750 align:start position:0%
from one task to the other this memory
can<00:09:05.820><c> come</c><00:09:06.060><c> in</c><00:09:06.180><c> useful</c><00:09:07.019><c> so</c><00:09:07.620><c> the</c><00:09:07.800><c> list</c><00:09:08.100><c> of</c><00:09:08.279><c> tools</c>

00:09:08.750 --> 00:09:08.760 align:start position:0%
can come in useful so the list of tools
 

00:09:08.760 --> 00:09:10.790 align:start position:0%
can come in useful so the list of tools
that<00:09:09.000><c> they've</c><00:09:09.240><c> got</c><00:09:09.420><c> is</c><00:09:09.959><c> quite</c><00:09:10.140><c> a</c><00:09:10.320><c> decent</c><00:09:10.560><c> list</c>

00:09:10.790 --> 00:09:10.800 align:start position:0%
that they've got is quite a decent list
 

00:09:10.800 --> 00:09:13.490 align:start position:0%
that they've got is quite a decent list
so<00:09:11.399><c> they've</c><00:09:12.180><c> got</c><00:09:12.300><c> this</c><00:09:12.720><c> is</c><00:09:12.839><c> just</c><00:09:12.959><c> taking</c><00:09:13.080><c> from</c>

00:09:13.490 --> 00:09:13.500 align:start position:0%
so they've got this is just taking from
 

00:09:13.500 --> 00:09:15.650 align:start position:0%
so they've got this is just taking from
their<00:09:13.800><c> demo</c><00:09:14.220><c> they've</c><00:09:14.700><c> got</c><00:09:14.820><c> document</c><00:09:15.180><c> question</c>

00:09:15.650 --> 00:09:15.660 align:start position:0%
their demo they've got document question
 

00:09:15.660 --> 00:09:17.449 align:start position:0%
their demo they've got document question
answering<00:09:16.500><c> they've</c><00:09:16.860><c> got</c><00:09:16.980><c> a</c><00:09:17.100><c> whole</c><00:09:17.220><c> bunch</c><00:09:17.339><c> of</c>

00:09:17.449 --> 00:09:17.459 align:start position:0%
answering they've got a whole bunch of
 

00:09:17.459 --> 00:09:18.650 align:start position:0%
answering they've got a whole bunch of
different<00:09:17.519><c> things</c><00:09:17.760><c> like</c><00:09:17.940><c> that</c><00:09:18.120><c> they've</c><00:09:18.480><c> got</c>

00:09:18.650 --> 00:09:18.660 align:start position:0%
different things like that they've got
 

00:09:18.660 --> 00:09:20.690 align:start position:0%
different things like that they've got
uh<00:09:19.260><c> blip</c><00:09:19.740><c> and</c><00:09:19.980><c> these</c><00:09:20.100><c> are</c><00:09:20.220><c> actually</c><00:09:20.339><c> showing</c>

00:09:20.690 --> 00:09:20.700 align:start position:0%
uh blip and these are actually showing
 

00:09:20.700 --> 00:09:22.790 align:start position:0%
uh blip and these are actually showing
the<00:09:20.880><c> models</c><00:09:21.300><c> that</c><00:09:21.420><c> they're</c><00:09:21.600><c> using</c><00:09:21.899><c> so</c><00:09:22.560><c> for</c>

00:09:22.790 --> 00:09:22.800 align:start position:0%
the models that they're using so for
 

00:09:22.800 --> 00:09:24.470 align:start position:0%
the models that they're using so for
text<00:09:23.220><c> question</c><00:09:23.519><c> answering</c><00:09:24.000><c> they're</c><00:09:24.180><c> using</c>

00:09:24.470 --> 00:09:24.480 align:start position:0%
text question answering they're using
 

00:09:24.480 --> 00:09:26.389 align:start position:0%
text question answering they're using
the<00:09:24.600><c> flan</c><00:09:24.959><c> T5</c><00:09:25.380><c> model</c>

00:09:26.389 --> 00:09:26.399 align:start position:0%
the flan T5 model
 

00:09:26.399 --> 00:09:28.190 align:start position:0%
the flan T5 model
I'm<00:09:26.940><c> not</c><00:09:27.060><c> sure</c><00:09:27.240><c> which</c><00:09:27.480><c> size</c><00:09:27.660><c> one</c><00:09:27.959><c> they're</c>

00:09:28.190 --> 00:09:28.200 align:start position:0%
I'm not sure which size one they're
 

00:09:28.200 --> 00:09:31.310 align:start position:0%
I'm not sure which size one they're
using<00:09:28.500><c> but</c><00:09:28.800><c> they're</c><00:09:29.339><c> using</c><00:09:29.700><c> blip</c><00:09:30.240><c> for</c><00:09:30.720><c> image</c>

00:09:31.310 --> 00:09:31.320 align:start position:0%
using but they're using blip for image
 

00:09:31.320 --> 00:09:33.829 align:start position:0%
using but they're using blip for image
captioning<00:09:32.000><c> they've</c><00:09:33.000><c> got</c><00:09:33.120><c> you</c><00:09:33.420><c> know</c><00:09:33.480><c> speech</c>

00:09:33.829 --> 00:09:33.839 align:start position:0%
captioning they've got you know speech
 

00:09:33.839 --> 00:09:35.329 align:start position:0%
captioning they've got you know speech
to<00:09:33.959><c> text</c><00:09:34.140><c> with</c><00:09:34.440><c> whisper</c><00:09:34.920><c> and</c><00:09:35.160><c> then</c>

00:09:35.329 --> 00:09:35.339 align:start position:0%
to text with whisper and then
 

00:09:35.339 --> 00:09:37.970 align:start position:0%
to text with whisper and then
text-to-speech<00:09:36.120><c> with</c><00:09:36.360><c> the</c><00:09:36.540><c> speech</c><00:09:36.899><c> T5</c><00:09:37.380><c> is</c>

00:09:37.970 --> 00:09:37.980 align:start position:0%
text-to-speech with the speech T5 is
 

00:09:37.980 --> 00:09:40.550 align:start position:0%
text-to-speech with the speech T5 is
what<00:09:38.220><c> we</c><00:09:38.339><c> were</c><00:09:38.459><c> using</c><00:09:38.700><c> before</c><00:09:39.080><c> I</c><00:09:40.080><c> and</c><00:09:40.320><c> we</c><00:09:40.500><c> can</c>

00:09:40.550 --> 00:09:40.560 align:start position:0%
what we were using before I and we can
 

00:09:40.560 --> 00:09:41.630 align:start position:0%
what we were using before I and we can
see<00:09:40.680><c> that</c><00:09:40.800><c> it's</c><00:09:41.040><c> a</c><00:09:41.160><c> number</c><00:09:41.279><c> of</c><00:09:41.399><c> different</c>

00:09:41.630 --> 00:09:41.640 align:start position:0%
see that it's a number of different
 

00:09:41.640 --> 00:09:43.610 align:start position:0%
see that it's a number of different
things<00:09:41.940><c> that</c><00:09:42.240><c> they've</c><00:09:42.420><c> got</c><00:09:42.600><c> going</c><00:09:42.839><c> on</c><00:09:43.200><c> there</c>

00:09:43.610 --> 00:09:43.620 align:start position:0%
things that they've got going on there
 

00:09:43.620 --> 00:09:45.889 align:start position:0%
things that they've got going on there
and<00:09:44.040><c> they've</c><00:09:44.279><c> also</c><00:09:44.459><c> got</c><00:09:44.640><c> community</c><00:09:45.180><c> based</c>

00:09:45.889 --> 00:09:45.899 align:start position:0%
and they've also got community based
 

00:09:45.899 --> 00:09:48.949 align:start position:0%
and they've also got community based
tools<00:09:46.200><c> so</c><00:09:46.620><c> this</c><00:09:47.100><c> text</c><00:09:47.339><c> downloader</c><00:09:48.060><c> is</c>

00:09:48.949 --> 00:09:48.959 align:start position:0%
tools so this text downloader is
 

00:09:48.959 --> 00:09:50.990 align:start position:0%
tools so this text downloader is
actually<00:09:49.080><c> a</c><00:09:49.380><c> community</c><00:09:49.560><c> based</c><00:09:50.279><c> tool</c><00:09:50.580><c> and</c><00:09:50.880><c> you</c>

00:09:50.990 --> 00:09:51.000 align:start position:0%
actually a community based tool and you
 

00:09:51.000 --> 00:09:52.550 align:start position:0%
actually a community based tool and you
can<00:09:51.240><c> imagine</c><00:09:51.420><c> that</c><00:09:51.839><c> there's</c><00:09:51.959><c> going</c><00:09:52.140><c> to</c><00:09:52.260><c> be</c><00:09:52.380><c> a</c>

00:09:52.550 --> 00:09:52.560 align:start position:0%
can imagine that there's going to be a
 

00:09:52.560 --> 00:09:54.590 align:start position:0%
can imagine that there's going to be a
lot<00:09:52.680><c> more</c><00:09:53.040><c> community-based</c><00:09:54.000><c> tools</c><00:09:54.300><c> going</c>

00:09:54.590 --> 00:09:54.600 align:start position:0%
lot more community-based tools going
 

00:09:54.600 --> 00:09:56.930 align:start position:0%
lot more community-based tools going
forward<00:09:54.899><c> over</c><00:09:55.740><c> the</c><00:09:55.980><c> next</c><00:09:56.160><c> few</c><00:09:56.399><c> weeks</c><00:09:56.760><c> or</c>

00:09:56.930 --> 00:09:56.940 align:start position:0%
forward over the next few weeks or
 

00:09:56.940 --> 00:09:59.329 align:start position:0%
forward over the next few weeks or
months<00:09:57.480><c> or</c><00:09:57.600><c> so</c><00:09:57.779><c> so</c>

00:09:59.329 --> 00:09:59.339 align:start position:0%
months or so so
 

00:09:59.339 --> 00:10:01.370 align:start position:0%
months or so so
start<00:09:59.880><c> out</c><00:10:00.120><c> again</c><00:10:00.360><c> and</c><00:10:00.720><c> I</c><00:10:00.839><c> want</c><00:10:00.959><c> to</c><00:10:01.080><c> sort</c><00:10:01.260><c> of</c>

00:10:01.370 --> 00:10:01.380 align:start position:0%
start out again and I want to sort of
 

00:10:01.380 --> 00:10:03.230 align:start position:0%
start out again and I want to sort of
try<00:10:01.500><c> something</c><00:10:01.740><c> out</c><00:10:02.040><c> so</c><00:10:02.459><c> what</c><00:10:02.820><c> I</c><00:10:03.000><c> thought</c><00:10:03.060><c> is</c>

00:10:03.230 --> 00:10:03.240 align:start position:0%
try something out so what I thought is
 

00:10:03.240 --> 00:10:06.230 align:start position:0%
try something out so what I thought is
let's<00:10:03.420><c> do</c><00:10:03.720><c> some</c><00:10:04.080><c> q</c><00:10:04.620><c> a</c><00:10:04.820><c> where</c><00:10:05.820><c> we</c><00:10:06.060><c> just</c>

00:10:06.230 --> 00:10:06.240 align:start position:0%
let's do some q a where we just
 

00:10:06.240 --> 00:10:08.210 align:start position:0%
let's do some q a where we just
basically<00:10:06.540><c> say</c><00:10:06.779><c> you</c><00:10:07.140><c> know</c><00:10:07.200><c> who</c><00:10:07.500><c> is</c><00:10:07.680><c> this</c><00:10:07.860><c> so</c><00:10:08.100><c> we</c>

00:10:08.210 --> 00:10:08.220 align:start position:0%
basically say you know who is this so we
 

00:10:08.220 --> 00:10:09.949 align:start position:0%
basically say you know who is this so we
ask<00:10:08.339><c> it</c><00:10:08.519><c> about</c><00:10:08.640><c> something</c><00:10:08.880><c> on</c><00:10:09.300><c> the</c><00:10:09.420><c> TechCrunch</c>

00:10:09.949 --> 00:10:09.959 align:start position:0%
ask it about something on the TechCrunch
 

00:10:09.959 --> 00:10:12.829 align:start position:0%
ask it about something on the TechCrunch
site<00:10:10.260><c> so</c><00:10:10.500><c> I've</c><00:10:10.680><c> got</c><00:10:10.740><c> a</c><00:10:10.980><c> URL</c><00:10:11.519><c> for</c><00:10:12.120><c> an</c><00:10:12.360><c> article</c>

00:10:12.829 --> 00:10:12.839 align:start position:0%
site so I've got a URL for an article
 

00:10:12.839 --> 00:10:16.370 align:start position:0%
site so I've got a URL for an article
there<00:10:13.140><c> which</c><00:10:13.980><c> was</c><00:10:14.220><c> talking</c><00:10:14.459><c> about</c><00:10:14.720><c> Elon</c><00:10:15.720><c> Musk</c>

00:10:16.370 --> 00:10:16.380 align:start position:0%
there which was talking about Elon Musk
 

00:10:16.380 --> 00:10:19.670 align:start position:0%
there which was talking about Elon Musk
appointing<00:10:16.980><c> a</c><00:10:17.160><c> new</c><00:10:17.339><c> CEO</c><00:10:17.940><c> for</c><00:10:18.180><c> Twitter</c><00:10:18.360><c> so</c><00:10:19.320><c> I</c>

00:10:19.670 --> 00:10:19.680 align:start position:0%
appointing a new CEO for Twitter so I
 

00:10:19.680 --> 00:10:22.070 align:start position:0%
appointing a new CEO for Twitter so I
basically<00:10:20.040><c> just</c><00:10:20.339><c> passed</c><00:10:20.700><c> in</c><00:10:20.820><c> that</c><00:10:21.300><c> URL</c><00:10:21.899><c> and</c>

00:10:22.070 --> 00:10:22.080 align:start position:0%
basically just passed in that URL and
 

00:10:22.080 --> 00:10:23.930 align:start position:0%
basically just passed in that URL and
said<00:10:22.260><c> who</c><00:10:22.500><c> is</c><00:10:22.620><c> the</c><00:10:22.800><c> possible</c><00:10:23.160><c> news</c><00:10:23.640><c> Twitter</c>

00:10:23.930 --> 00:10:23.940 align:start position:0%
said who is the possible news Twitter
 

00:10:23.940 --> 00:10:27.050 align:start position:0%
said who is the possible news Twitter
CEO<00:10:24.660><c> based</c><00:10:25.440><c> on</c><00:10:25.500><c> this</c><00:10:25.740><c> article</c><00:10:26.279><c> at</c><00:10:26.580><c> and</c><00:10:26.940><c> then</c>

00:10:27.050 --> 00:10:27.060 align:start position:0%
CEO based on this article at and then
 

00:10:27.060 --> 00:10:30.050 align:start position:0%
CEO based on this article at and then
put<00:10:27.240><c> the</c><00:10:27.360><c> URL</c><00:10:27.779><c> in</c><00:10:28.019><c> there</c><00:10:28.680><c> and</c><00:10:29.399><c> it's</c><00:10:29.700><c> done</c><00:10:29.880><c> a</c>

00:10:30.050 --> 00:10:30.060 align:start position:0%
put the URL in there and it's done a
 

00:10:30.060 --> 00:10:32.509 align:start position:0%
put the URL in there and it's done a
great<00:10:30.180><c> job</c><00:10:30.420><c> of</c><00:10:30.720><c> working</c><00:10:30.959><c> out</c><00:10:31.260><c> that</c><00:10:31.680><c> okay</c><00:10:32.100><c> I</c>

00:10:32.509 --> 00:10:32.519 align:start position:0%
great job of working out that okay I
 

00:10:32.519 --> 00:10:34.009 align:start position:0%
great job of working out that okay I
need<00:10:32.640><c> to</c><00:10:32.760><c> use</c><00:10:32.880><c> the</c><00:10:33.120><c> text</c><00:10:33.240><c> downloader</c><00:10:33.779><c> to</c><00:10:33.899><c> go</c>

00:10:34.009 --> 00:10:34.019 align:start position:0%
need to use the text downloader to go
 

00:10:34.019 --> 00:10:36.889 align:start position:0%
need to use the text downloader to go
and<00:10:34.200><c> get</c><00:10:34.380><c> the</c><00:10:34.620><c> text</c><00:10:34.800><c> I</c><00:10:35.519><c> then</c><00:10:35.940><c> need</c><00:10:36.300><c> to</c><00:10:36.480><c> use</c><00:10:36.660><c> the</c>

00:10:36.889 --> 00:10:36.899 align:start position:0%
and get the text I then need to use the
 

00:10:36.899 --> 00:10:40.250 align:start position:0%
and get the text I then need to use the
text<00:10:37.080><c> QA</c><00:10:37.740><c> to</c><00:10:38.399><c> answer</c><00:10:38.580><c> this</c><00:10:39.000><c> question</c><00:10:39.240><c> so</c><00:10:39.839><c> the</c>

00:10:40.250 --> 00:10:40.260 align:start position:0%
text QA to answer this question so the
 

00:10:40.260 --> 00:10:42.170 align:start position:0%
text QA to answer this question so the
QA<00:10:40.620><c> model</c><00:10:40.860><c> it</c><00:10:41.100><c> had</c><00:10:41.279><c> didn't</c><00:10:41.519><c> have</c><00:10:41.760><c> downloaded</c>

00:10:42.170 --> 00:10:42.180 align:start position:0%
QA model it had didn't have downloaded
 

00:10:42.180 --> 00:10:44.210 align:start position:0%
QA model it had didn't have downloaded
so<00:10:42.420><c> it</c><00:10:42.600><c> comes</c><00:10:42.839><c> along</c><00:10:42.959><c> and</c><00:10:43.260><c> downloads</c><00:10:43.920><c> a</c><00:10:44.040><c> model</c>

00:10:44.210 --> 00:10:44.220 align:start position:0%
so it comes along and downloads a model
 

00:10:44.220 --> 00:10:47.030 align:start position:0%
so it comes along and downloads a model
there<00:10:44.600><c> and</c><00:10:45.600><c> then</c><00:10:45.779><c> sure</c><00:10:46.140><c> enough</c><00:10:46.320><c> it's</c><00:10:46.680><c> able</c>

00:10:47.030 --> 00:10:47.040 align:start position:0%
there and then sure enough it's able
 

00:10:47.040 --> 00:10:49.370 align:start position:0%
there and then sure enough it's able
then<00:10:47.279><c> to</c><00:10:47.579><c> work</c><00:10:47.760><c> out</c><00:10:48.000><c> what</c><00:10:48.420><c> what</c><00:10:48.480><c> does</c><00:10:48.899><c> it</c><00:10:49.140><c> pass</c>

00:10:49.370 --> 00:10:49.380 align:start position:0%
then to work out what what does it pass
 

00:10:49.380 --> 00:10:52.130 align:start position:0%
then to work out what what does it pass
to<00:10:49.680><c> the</c><00:10:49.980><c> the</c><00:10:50.519><c> QA</c><00:10:50.940><c> module</c><00:10:51.360><c> who</c><00:10:51.600><c> is</c><00:10:51.779><c> the</c><00:10:51.899><c> possible</c>

00:10:52.130 --> 00:10:52.140 align:start position:0%
to the the QA module who is the possible
 

00:10:52.140 --> 00:10:55.069 align:start position:0%
to the the QA module who is the possible
new<00:10:52.440><c> Twitter</c><00:10:52.740><c> CEO</c><00:10:53.519><c> and</c><00:10:54.240><c> then</c><00:10:54.360><c> passing</c><00:10:54.720><c> in</c><00:10:54.839><c> the</c>

00:10:55.069 --> 00:10:55.079 align:start position:0%
new Twitter CEO and then passing in the
 

00:10:55.079 --> 00:10:56.150 align:start position:0%
new Twitter CEO and then passing in the
text

00:10:56.150 --> 00:10:56.160 align:start position:0%
text
 

00:10:56.160 --> 00:10:58.430 align:start position:0%
text
sure<00:10:56.700><c> enough</c><00:10:56.880><c> it</c><00:10:57.360><c> got</c><00:10:57.540><c> it</c><00:10:57.720><c> right</c><00:10:57.899><c> if</c><00:10:58.140><c> we</c><00:10:58.320><c> look</c>

00:10:58.430 --> 00:10:58.440 align:start position:0%
sure enough it got it right if we look
 

00:10:58.440 --> 00:11:01.130 align:start position:0%
sure enough it got it right if we look
at<00:10:58.680><c> the</c><00:10:58.980><c> article</c><00:10:59.339><c> it</c><00:10:59.579><c> does</c><00:10:59.760><c> talk</c><00:11:00.000><c> about</c><00:11:00.180><c> this</c>

00:11:01.130 --> 00:11:01.140 align:start position:0%
at the article it does talk about this
 

00:11:01.140 --> 00:11:04.190 align:start position:0%
at the article it does talk about this
woman<00:11:01.320><c> who</c><00:11:01.800><c> is</c><00:11:01.980><c> the</c><00:11:02.160><c> NBC</c><00:11:03.120><c> Universal</c><00:11:03.839><c> head</c><00:11:04.019><c> of</c>

00:11:04.190 --> 00:11:04.200 align:start position:0%
woman who is the NBC Universal head of
 

00:11:04.200 --> 00:11:07.670 align:start position:0%
woman who is the NBC Universal head of
advertising<00:11:04.880><c> and</c><00:11:05.880><c> she's</c><00:11:06.660><c> supposedly</c><00:11:07.440><c> the</c>

00:11:07.670 --> 00:11:07.680 align:start position:0%
advertising and she's supposedly the
 

00:11:07.680 --> 00:11:10.430 align:start position:0%
advertising and she's supposedly the
next<00:11:07.800><c> Twitter</c><00:11:08.040><c> CEO</c><00:11:08.760><c> for</c><00:11:09.300><c> that</c><00:11:09.540><c> now</c><00:11:10.019><c> I</c><00:11:10.380><c> wanted</c>

00:11:10.430 --> 00:11:10.440 align:start position:0%
next Twitter CEO for that now I wanted
 

00:11:10.440 --> 00:11:13.130 align:start position:0%
next Twitter CEO for that now I wanted
to<00:11:10.620><c> see</c><00:11:10.740><c> like</c><00:11:10.980><c> would</c><00:11:11.220><c> it</c><00:11:11.459><c> know</c><00:11:11.820><c> that</c><00:11:12.120><c> okay</c><00:11:12.480><c> it</c>

00:11:13.130 --> 00:11:13.140 align:start position:0%
to see like would it know that okay it
 

00:11:13.140 --> 00:11:14.630 align:start position:0%
to see like would it know that okay it
turns<00:11:13.560><c> out</c><00:11:13.620><c> the</c><00:11:13.740><c> article</c><00:11:14.040><c> when</c><00:11:14.279><c> I</c><00:11:14.399><c> went</c><00:11:14.519><c> and</c>

00:11:14.630 --> 00:11:14.640 align:start position:0%
turns out the article when I went and
 

00:11:14.640 --> 00:11:17.630 align:start position:0%
turns out the article when I went and
looked<00:11:14.820><c> at</c><00:11:14.940><c> it</c><00:11:15.060><c> it</c><00:11:15.300><c> mentions</c><00:11:15.720><c> both</c><00:11:16.620><c> the</c><00:11:17.459><c> future</c>

00:11:17.630 --> 00:11:17.640 align:start position:0%
looked at it it mentions both the future
 

00:11:17.640 --> 00:11:20.569 align:start position:0%
looked at it it mentions both the future
CEO<00:11:18.300><c> being</c><00:11:18.600><c> this</c><00:11:18.899><c> person</c><00:11:19.160><c> Elon</c><00:11:20.160><c> as</c><00:11:20.399><c> the</c>

00:11:20.569 --> 00:11:20.579 align:start position:0%
CEO being this person Elon as the
 

00:11:20.579 --> 00:11:23.389 align:start position:0%
CEO being this person Elon as the
current<00:11:20.760><c> CEO</c><00:11:21.240><c> and</c><00:11:21.839><c> the</c><00:11:22.200><c> former</c><00:11:22.500><c> CEO</c><00:11:22.860><c> from</c><00:11:23.100><c> when</c>

00:11:23.389 --> 00:11:23.399 align:start position:0%
current CEO and the former CEO from when
 

00:11:23.399 --> 00:11:26.630 align:start position:0%
current CEO and the former CEO from when
before<00:11:24.000><c> Elon</c><00:11:24.839><c> bought</c><00:11:25.140><c> this</c><00:11:25.380><c> so</c><00:11:25.860><c> we</c><00:11:26.040><c> ask</c><00:11:26.279><c> it</c><00:11:26.459><c> who</c>

00:11:26.630 --> 00:11:26.640 align:start position:0%
before Elon bought this so we ask it who
 

00:11:26.640 --> 00:11:28.550 align:start position:0%
before Elon bought this so we ask it who
was<00:11:26.760><c> the</c><00:11:26.940><c> former</c><00:11:27.240><c> Twitter</c><00:11:27.420><c> CEO</c><00:11:28.019><c> based</c><00:11:28.380><c> on</c><00:11:28.440><c> the</c>

00:11:28.550 --> 00:11:28.560 align:start position:0%
was the former Twitter CEO based on the
 

00:11:28.560 --> 00:11:31.550 align:start position:0%
was the former Twitter CEO based on the
article<00:11:28.920><c> it</c><00:11:29.459><c> gets</c><00:11:29.820><c> that</c><00:11:30.000><c> right</c><00:11:30.300><c> as</c><00:11:30.839><c> well</c><00:11:31.019><c> then</c>

00:11:31.550 --> 00:11:31.560 align:start position:0%
article it gets that right as well then
 

00:11:31.560 --> 00:11:34.009 align:start position:0%
article it gets that right as well then
it<00:11:31.740><c> gets</c><00:11:31.980><c> Prague</c><00:11:32.399><c> I</c><00:11:32.760><c> grow</c><00:11:33.000><c> well</c><00:11:33.240><c> correct</c><00:11:33.839><c> there</c>

00:11:34.009 --> 00:11:34.019 align:start position:0%
it gets Prague I grow well correct there
 

00:11:34.019 --> 00:11:35.870 align:start position:0%
it gets Prague I grow well correct there
but<00:11:34.620><c> then</c><00:11:34.800><c> when</c><00:11:34.920><c> I</c><00:11:35.040><c> ask</c><00:11:35.160><c> it</c><00:11:35.279><c> who's</c><00:11:35.519><c> the</c><00:11:35.700><c> current</c>

00:11:35.870 --> 00:11:35.880 align:start position:0%
but then when I ask it who's the current
 

00:11:35.880 --> 00:11:38.269 align:start position:0%
but then when I ask it who's the current
Twitter<00:11:36.180><c> CEO</c><00:11:36.839><c> based</c><00:11:37.200><c> on</c><00:11:37.260><c> the</c><00:11:37.440><c> article</c><00:11:37.800><c> it</c>

00:11:38.269 --> 00:11:38.279 align:start position:0%
Twitter CEO based on the article it
 

00:11:38.279 --> 00:11:39.530 align:start position:0%
Twitter CEO based on the article it
didn't<00:11:38.399><c> get</c><00:11:38.640><c> that</c><00:11:38.760><c> right</c><00:11:39.000><c> and</c><00:11:39.420><c> that's</c>

00:11:39.530 --> 00:11:39.540 align:start position:0%
didn't get that right and that's
 

00:11:39.540 --> 00:11:41.150 align:start position:0%
didn't get that right and that's
basically<00:11:39.899><c> just</c><00:11:40.140><c> the</c><00:11:40.320><c> limitation</c><00:11:40.680><c> of</c><00:11:40.920><c> the</c>

00:11:41.150 --> 00:11:41.160 align:start position:0%
basically just the limitation of the
 

00:11:41.160 --> 00:11:44.090 align:start position:0%
basically just the limitation of the
model<00:11:41.339><c> for</c><00:11:42.120><c> this</c><00:11:42.600><c> so</c><00:11:43.200><c> that's</c><00:11:43.380><c> nothing</c><00:11:43.680><c> to</c><00:11:43.980><c> do</c>

00:11:44.090 --> 00:11:44.100 align:start position:0%
model for this so that's nothing to do
 

00:11:44.100 --> 00:11:47.269 align:start position:0%
model for this so that's nothing to do
so<00:11:44.579><c> so</c><00:11:44.820><c> this</c><00:11:45.120><c> is</c><00:11:45.300><c> where</c><00:11:45.660><c> the</c><00:11:46.500><c> Transformer</c>

00:11:47.269 --> 00:11:47.279 align:start position:0%
so so this is where the Transformer
 

00:11:47.279 --> 00:11:49.490 align:start position:0%
so so this is where the Transformer
agent<00:11:47.820><c> is</c><00:11:48.240><c> getting</c><00:11:48.540><c> everything</c><00:11:48.899><c> correct</c>

00:11:49.490 --> 00:11:49.500 align:start position:0%
agent is getting everything correct
 

00:11:49.500 --> 00:11:51.650 align:start position:0%
agent is getting everything correct
there<00:11:49.740><c> but</c><00:11:50.279><c> because</c><00:11:50.459><c> it's</c><00:11:50.820><c> using</c><00:11:51.240><c> certain</c>

00:11:51.650 --> 00:11:51.660 align:start position:0%
there but because it's using certain
 

00:11:51.660 --> 00:11:54.410 align:start position:0%
there but because it's using certain
models<00:11:52.440><c> it's</c><00:11:52.740><c> not</c><00:11:52.920><c> using</c><00:11:53.279><c> the</c><00:11:53.579><c> actual</c><00:11:53.940><c> large</c>

00:11:54.410 --> 00:11:54.420 align:start position:0%
models it's not using the actual large
 

00:11:54.420 --> 00:11:56.630 align:start position:0%
models it's not using the actual large
language<00:11:54.959><c> model</c><00:11:55.380><c> for</c><00:11:55.680><c> doing</c><00:11:55.860><c> this</c><00:11:56.160><c> for</c>

00:11:56.630 --> 00:11:56.640 align:start position:0%
language model for doing this for
 

00:11:56.640 --> 00:11:58.550 align:start position:0%
language model for doing this for
example<00:11:56.940><c> meaning</c><00:11:57.480><c> for</c><00:11:57.779><c> doing</c><00:11:57.959><c> the</c><00:11:58.380><c> question</c>

00:11:58.550 --> 00:11:58.560 align:start position:0%
example meaning for doing the question
 

00:11:58.560 --> 00:12:00.949 align:start position:0%
example meaning for doing the question
answering<00:11:59.100><c> we're</c><00:11:59.339><c> not</c><00:11:59.519><c> using</c><00:11:59.940><c> open</c><00:12:00.240><c> AI</c><00:12:00.720><c> for</c>

00:12:00.949 --> 00:12:00.959 align:start position:0%
answering we're not using open AI for
 

00:12:00.959 --> 00:12:02.630 align:start position:0%
answering we're not using open AI for
that<00:12:01.079><c> we're</c><00:12:01.320><c> just</c><00:12:01.500><c> using</c><00:12:01.740><c> open</c><00:12:01.860><c> AI</c><00:12:02.279><c> for</c><00:12:02.519><c> the</c>

00:12:02.630 --> 00:12:02.640 align:start position:0%
that we're just using open AI for the
 

00:12:02.640 --> 00:12:05.569 align:start position:0%
that we're just using open AI for the
agent<00:12:03.060><c> part</c><00:12:03.300><c> here</c><00:12:04.079><c> I</c><00:12:04.740><c> try</c><00:12:04.920><c> to</c><00:12:05.100><c> do</c><00:12:05.220><c> translate</c>

00:12:05.569 --> 00:12:05.579 align:start position:0%
agent part here I try to do translate
 

00:12:05.579 --> 00:12:09.170 align:start position:0%
agent part here I try to do translate
the<00:12:05.940><c> title</c><00:12:06.300><c> uh</c><00:12:06.839><c> of</c><00:12:07.260><c> the</c><00:12:07.380><c> article</c><00:12:07.680><c> into</c><00:12:07.920><c> French</c>

00:12:09.170 --> 00:12:09.180 align:start position:0%
the title uh of the article into French
 

00:12:09.180 --> 00:12:11.269 align:start position:0%
the title uh of the article into French
it<00:12:09.720><c> seemed</c><00:12:10.019><c> to</c><00:12:10.079><c> understand</c><00:12:10.320><c> what</c><00:12:10.980><c> it</c><00:12:11.100><c> should</c>

00:12:11.269 --> 00:12:11.279 align:start position:0%
it seemed to understand what it should
 

00:12:11.279 --> 00:12:13.730 align:start position:0%
it seemed to understand what it should
be<00:12:11.459><c> but</c><00:12:12.060><c> it</c><00:12:12.240><c> should</c><00:12:12.360><c> use</c><00:12:12.540><c> translator</c><00:12:13.200><c> for</c>

00:12:13.730 --> 00:12:13.740 align:start position:0%
be but it should use translator for
 

00:12:13.740 --> 00:12:15.710 align:start position:0%
be but it should use translator for
whatever<00:12:13.920><c> reason</c><00:12:14.339><c> I</c><00:12:14.820><c> was</c><00:12:15.120><c> getting</c><00:12:15.300><c> errors</c>

00:12:15.710 --> 00:12:15.720 align:start position:0%
whatever reason I was getting errors
 

00:12:15.720 --> 00:12:17.870 align:start position:0%
whatever reason I was getting errors
with<00:12:15.899><c> that</c><00:12:16.079><c> so</c><00:12:16.320><c> that</c><00:12:16.620><c> didn't</c><00:12:16.740><c> seem</c><00:12:17.100><c> to</c><00:12:17.220><c> work</c><00:12:17.459><c> I</c>

00:12:17.870 --> 00:12:17.880 align:start position:0%
with that so that didn't seem to work I
 

00:12:17.880 --> 00:12:19.550 align:start position:0%
with that so that didn't seem to work I
want<00:12:18.000><c> to</c><00:12:18.060><c> ask</c><00:12:18.180><c> it</c><00:12:18.360><c> to</c><00:12:18.540><c> summarize</c><00:12:18.839><c> the</c><00:12:19.079><c> article</c>

00:12:19.550 --> 00:12:19.560 align:start position:0%
want to ask it to summarize the article
 

00:12:19.560 --> 00:12:21.410 align:start position:0%
want to ask it to summarize the article
for<00:12:19.800><c> me</c><00:12:19.980><c> sure</c><00:12:20.519><c> enough</c><00:12:20.700><c> it</c><00:12:21.000><c> actually</c><00:12:21.060><c> gives</c><00:12:21.360><c> a</c>

00:12:21.410 --> 00:12:21.420 align:start position:0%
for me sure enough it actually gives a
 

00:12:21.420 --> 00:12:23.329 align:start position:0%
for me sure enough it actually gives a
pretty<00:12:21.600><c> decent</c><00:12:21.959><c> summary</c><00:12:22.320><c> right</c><00:12:22.620><c> uh</c><00:12:23.040><c> summary</c>

00:12:23.329 --> 00:12:23.339 align:start position:0%
pretty decent summary right uh summary
 

00:12:23.339 --> 00:12:25.310 align:start position:0%
pretty decent summary right uh summary
Elon<00:12:23.820><c> Musk</c><00:12:24.120><c> announced</c><00:12:24.480><c> that</c><00:12:24.600><c> he</c><00:12:24.779><c> has</c><00:12:24.959><c> found</c><00:12:25.140><c> a</c>

00:12:25.310 --> 00:12:25.320 align:start position:0%
Elon Musk announced that he has found a
 

00:12:25.320 --> 00:12:27.290 align:start position:0%
Elon Musk announced that he has found a
new<00:12:25.500><c> CEO</c><00:12:25.860><c> for</c><00:12:26.040><c> Twitter</c><00:12:26.220><c> the</c><00:12:26.579><c> new</c><00:12:26.760><c> CEO</c><00:12:27.120><c> is</c>

00:12:27.290 --> 00:12:27.300 align:start position:0%
new CEO for Twitter the new CEO is
 

00:12:27.300 --> 00:12:30.050 align:start position:0%
new CEO for Twitter the new CEO is
expected<00:12:27.720><c> to</c><00:12:27.839><c> start</c><00:12:28.079><c> in</c><00:12:28.380><c> six</c><00:12:28.560><c> weeks</c><00:12:28.980><c> it's</c><00:12:29.880><c> got</c>

00:12:30.050 --> 00:12:30.060 align:start position:0%
expected to start in six weeks it's got
 

00:12:30.060 --> 00:12:31.670 align:start position:0%
expected to start in six weeks it's got
some<00:12:30.240><c> interest</c><00:12:30.480><c> the</c><00:12:31.019><c> information</c><00:12:31.260><c> there</c>

00:12:31.670 --> 00:12:31.680 align:start position:0%
some interest the information there
 

00:12:31.680 --> 00:12:33.650 align:start position:0%
some interest the information there
although<00:12:32.220><c> I</c><00:12:32.940><c> don't</c><00:12:33.060><c> think</c><00:12:33.300><c> it</c><00:12:33.480><c> actually</c>

00:12:33.650 --> 00:12:33.660 align:start position:0%
although I don't think it actually
 

00:12:33.660 --> 00:12:35.930 align:start position:0%
although I don't think it actually
mentions<00:12:34.079><c> the</c><00:12:34.320><c> person's</c><00:12:34.860><c> name</c><00:12:35.160><c> of</c><00:12:35.579><c> the</c><00:12:35.820><c> woman</c>

00:12:35.930 --> 00:12:35.940 align:start position:0%
mentions the person's name of the woman
 

00:12:35.940 --> 00:12:38.690 align:start position:0%
mentions the person's name of the woman
who's<00:12:36.300><c> actually</c><00:12:36.540><c> starting</c><00:12:37.320><c> in</c><00:12:37.620><c> that</c><00:12:37.800><c> role</c>

00:12:38.690 --> 00:12:38.700 align:start position:0%
who's actually starting in that role
 

00:12:38.700 --> 00:12:41.690 align:start position:0%
who's actually starting in that role
okay<00:12:39.240><c> so</c><00:12:39.660><c> then</c><00:12:39.839><c> the</c><00:12:40.079><c> next</c><00:12:40.200><c> part</c><00:12:40.560><c> is</c><00:12:41.220><c> I'm</c><00:12:41.519><c> just</c>

00:12:41.690 --> 00:12:41.700 align:start position:0%
okay so then the next part is I'm just
 

00:12:41.700 --> 00:12:44.509 align:start position:0%
okay so then the next part is I'm just
reinitializing<00:12:42.600><c> it</c><00:12:42.779><c> and</c><00:12:43.560><c> this</c><00:12:44.100><c> has</c><00:12:44.279><c> basically</c>

00:12:44.509 --> 00:12:44.519 align:start position:0%
reinitializing it and this has basically
 

00:12:44.519 --> 00:12:47.389 align:start position:0%
reinitializing it and this has basically
just<00:12:44.700><c> taken</c><00:12:45.060><c> from</c><00:12:45.300><c> their</c><00:12:45.600><c> demo</c><00:12:46.139><c> and</c><00:12:46.920><c> this</c><00:12:47.339><c> is</c>

00:12:47.389 --> 00:12:47.399 align:start position:0%
just taken from their demo and this is
 

00:12:47.399 --> 00:12:50.030 align:start position:0%
just taken from their demo and this is
showing<00:12:47.760><c> how</c><00:12:48.000><c> you</c><00:12:48.180><c> would</c><00:12:48.360><c> add</c><00:12:48.720><c> a</c><00:12:49.079><c> new</c><00:12:49.200><c> tool</c><00:12:49.620><c> so</c>

00:12:50.030 --> 00:12:50.040 align:start position:0%
showing how you would add a new tool so
 

00:12:50.040 --> 00:12:52.850 align:start position:0%
showing how you would add a new tool so
it's<00:12:50.760><c> it's</c><00:12:51.060><c> pretty</c><00:12:51.420><c> simple</c><00:12:51.720><c> but</c><00:12:52.320><c> what</c><00:12:52.500><c> I</c>

00:12:52.850 --> 00:12:52.860 align:start position:0%
it's it's pretty simple but what I
 

00:12:52.860 --> 00:12:55.730 align:start position:0%
it's it's pretty simple but what I
wanted<00:12:52.980><c> to</c><00:12:53.160><c> sort</c><00:12:53.339><c> of</c><00:12:53.519><c> show</c><00:12:53.700><c> you</c><00:12:53.880><c> was</c><00:12:54.779><c> how</c><00:12:55.500><c> you</c>

00:12:55.730 --> 00:12:55.740 align:start position:0%
wanted to sort of show you was how you
 

00:12:55.740 --> 00:12:57.650 align:start position:0%
wanted to sort of show you was how you
know<00:12:55.800><c> it's</c><00:12:56.160><c> basically</c><00:12:56.519><c> so</c><00:12:56.760><c> this</c><00:12:57.000><c> is</c><00:12:57.120><c> try</c><00:12:57.540><c> to</c>

00:12:57.650 --> 00:12:57.660 align:start position:0%
know it's basically so this is try to
 

00:12:57.660 --> 00:12:59.090 align:start position:0%
know it's basically so this is try to
make<00:12:57.779><c> a</c><00:12:57.959><c> tool</c><00:12:58.200><c> where</c><00:12:58.320><c> you</c><00:12:58.440><c> can</c><00:12:58.560><c> just</c><00:12:58.620><c> say</c><00:12:58.800><c> get</c>

00:12:59.090 --> 00:12:59.100 align:start position:0%
make a tool where you can just say get
 

00:12:59.100 --> 00:13:01.670 align:start position:0%
make a tool where you can just say get
me<00:12:59.279><c> a</c><00:12:59.459><c> picture</c><00:12:59.639><c> of</c><00:12:59.880><c> a</c><00:13:00.000><c> cat</c><00:13:00.180><c> and</c><00:13:01.019><c> it</c><00:13:01.200><c> turns</c><00:13:01.620><c> out</c>

00:13:01.670 --> 00:13:01.680 align:start position:0%
me a picture of a cat and it turns out
 

00:13:01.680 --> 00:13:03.290 align:start position:0%
me a picture of a cat and it turns out
that<00:13:01.920><c> I</c><00:13:02.160><c> didn't</c><00:13:02.279><c> realize</c><00:13:02.579><c> there's</c><00:13:02.760><c> a</c><00:13:02.940><c> website</c>

00:13:03.290 --> 00:13:03.300 align:start position:0%
that I didn't realize there's a website
 

00:13:03.300 --> 00:13:06.769 align:start position:0%
that I didn't realize there's a website
which<00:13:03.899><c> is</c><00:13:04.019><c> cat</c><00:13:04.380><c> as</c><00:13:04.740><c> a</c><00:13:04.980><c> service</c><00:13:05.160><c> API</c><00:13:06.120><c> which</c>

00:13:06.769 --> 00:13:06.779 align:start position:0%
which is cat as a service API which
 

00:13:06.779 --> 00:13:09.050 align:start position:0%
which is cat as a service API which
streams<00:13:07.200><c> pictures</c><00:13:07.440><c> of</c><00:13:07.800><c> cats</c><00:13:08.279><c> you</c><00:13:08.820><c> can</c><00:13:08.940><c> just</c>

00:13:09.050 --> 00:13:09.060 align:start position:0%
streams pictures of cats you can just
 

00:13:09.060 --> 00:13:11.750 align:start position:0%
streams pictures of cats you can just
ping<00:13:09.300><c> it</c><00:13:09.480><c> and</c><00:13:09.660><c> it</c><00:13:09.839><c> will</c><00:13:09.899><c> ping</c><00:13:10.380><c> back</c><00:13:10.560><c> a</c><00:13:10.740><c> cat</c>

00:13:11.750 --> 00:13:11.760 align:start position:0%
ping it and it will ping back a cat
 

00:13:11.760 --> 00:13:14.150 align:start position:0%
ping it and it will ping back a cat
all<00:13:12.300><c> right</c><00:13:12.420><c> and</c><00:13:13.200><c> you</c><00:13:13.380><c> can</c><00:13:13.500><c> see</c><00:13:13.680><c> that</c><00:13:13.860><c> they</c>

00:13:14.150 --> 00:13:14.160 align:start position:0%
all right and you can see that they
 

00:13:14.160 --> 00:13:16.870 align:start position:0%
all right and you can see that they
basically<00:13:14.519><c> uh</c><00:13:15.180><c> just</c><00:13:15.420><c> turned</c><00:13:15.839><c> that</c><00:13:16.139><c> request</c>

00:13:16.870 --> 00:13:16.880 align:start position:0%
basically uh just turned that request
 

00:13:16.880 --> 00:13:21.889 align:start position:0%
basically uh just turned that request
here<00:13:18.139><c> into</c><00:13:19.139><c> a</c><00:13:19.740><c> class</c><00:13:20.220><c> and</c><00:13:20.760><c> into</c><00:13:20.940><c> a</c><00:13:21.300><c> tool</c><00:13:21.600><c> that</c>

00:13:21.889 --> 00:13:21.899 align:start position:0%
here into a class and into a tool that
 

00:13:21.899 --> 00:13:24.590 align:start position:0%
here into a class and into a tool that
you<00:13:22.019><c> can</c><00:13:22.139><c> basically</c><00:13:22.440><c> use</c><00:13:22.800><c> for</c><00:13:23.100><c> fetching</c><00:13:24.060><c> so</c>

00:13:24.590 --> 00:13:24.600 align:start position:0%
you can basically use for fetching so
 

00:13:24.600 --> 00:13:26.210 align:start position:0%
you can basically use for fetching so
this<00:13:25.019><c> is</c><00:13:25.079><c> just</c><00:13:25.260><c> basically</c><00:13:25.500><c> putting</c><00:13:25.920><c> it</c><00:13:25.980><c> back</c>

00:13:26.210 --> 00:13:26.220 align:start position:0%
this is just basically putting it back
 

00:13:26.220 --> 00:13:28.310 align:start position:0%
this is just basically putting it back
now<00:13:26.700><c> the</c><00:13:27.060><c> key</c><00:13:27.180><c> thing</c><00:13:27.300><c> here</c><00:13:27.540><c> is</c><00:13:27.720><c> that</c><00:13:27.959><c> they</c><00:13:28.200><c> need</c>

00:13:28.310 --> 00:13:28.320 align:start position:0%
now the key thing here is that they need
 

00:13:28.320 --> 00:13:30.590 align:start position:0%
now the key thing here is that they need
to<00:13:28.500><c> make</c><00:13:28.740><c> this</c><00:13:28.980><c> into</c><00:13:29.220><c> a</c><00:13:29.519><c> tool</c><00:13:29.820><c> so</c><00:13:30.060><c> this</c><00:13:30.360><c> is</c>

00:13:30.590 --> 00:13:30.600 align:start position:0%
to make this into a tool so this is
 

00:13:30.600 --> 00:13:32.870 align:start position:0%
to make this into a tool so this is
almost<00:13:30.899><c> identical</c><00:13:31.620><c> to</c><00:13:31.860><c> what</c><00:13:32.040><c> we</c><00:13:32.220><c> do</c><00:13:32.399><c> in</c><00:13:32.700><c> Lang</c>

00:13:32.870 --> 00:13:32.880 align:start position:0%
almost identical to what we do in Lang
 

00:13:32.880 --> 00:13:35.449 align:start position:0%
almost identical to what we do in Lang
chain<00:13:33.180><c> is</c><00:13:34.019><c> that</c><00:13:34.320><c> you</c><00:13:34.740><c> need</c><00:13:34.860><c> to</c><00:13:34.980><c> have</c><00:13:35.100><c> a</c><00:13:35.339><c> name</c>

00:13:35.449 --> 00:13:35.459 align:start position:0%
chain is that you need to have a name
 

00:13:35.459 --> 00:13:37.250 align:start position:0%
chain is that you need to have a name
for<00:13:35.760><c> a</c><00:13:35.880><c> tool</c><00:13:36.180><c> and</c><00:13:36.480><c> then</c><00:13:36.600><c> you</c><00:13:36.720><c> need</c><00:13:36.899><c> to</c><00:13:36.959><c> have</c><00:13:37.079><c> a</c>

00:13:37.250 --> 00:13:37.260 align:start position:0%
for a tool and then you need to have a
 

00:13:37.260 --> 00:13:38.750 align:start position:0%
for a tool and then you need to have a
description<00:13:37.620><c> for</c><00:13:37.980><c> the</c><00:13:38.100><c> tool</c><00:13:38.399><c> and</c><00:13:38.579><c> the</c>

00:13:38.750 --> 00:13:38.760 align:start position:0%
description for the tool and the
 

00:13:38.760 --> 00:13:42.230 align:start position:0%
description for the tool and the
description<00:13:39.120><c> for</c><00:13:39.480><c> the</c><00:13:39.660><c> tool</c><00:13:40.019><c> is</c><00:13:40.740><c> what</c><00:13:41.279><c> the</c><00:13:41.760><c> the</c>

00:13:42.230 --> 00:13:42.240 align:start position:0%
description for the tool is what the the
 

00:13:42.240 --> 00:13:45.290 align:start position:0%
description for the tool is what the the
sort<00:13:42.540><c> of</c><00:13:42.660><c> agent</c><00:13:43.139><c> model</c><00:13:43.519><c> looks</c><00:13:44.519><c> at</c><00:13:44.639><c> to</c><00:13:44.940><c> decide</c>

00:13:45.290 --> 00:13:45.300 align:start position:0%
sort of agent model looks at to decide
 

00:13:45.300 --> 00:13:47.569 align:start position:0%
sort of agent model looks at to decide
whether<00:13:45.600><c> it</c><00:13:45.779><c> uses</c><00:13:46.079><c> the</c><00:13:46.320><c> tool</c><00:13:46.560><c> or</c><00:13:46.680><c> not</c><00:13:46.860><c> so</c><00:13:47.160><c> this</c>

00:13:47.569 --> 00:13:47.579 align:start position:0%
whether it uses the tool or not so this
 

00:13:47.579 --> 00:13:49.670 align:start position:0%
whether it uses the tool or not so this
is<00:13:47.880><c> a</c><00:13:48.060><c> tool</c><00:13:48.300><c> that</c><00:13:48.420><c> fetches</c><00:13:48.779><c> an</c><00:13:49.079><c> actual</c><00:13:49.320><c> image</c>

00:13:49.670 --> 00:13:49.680 align:start position:0%
is a tool that fetches an actual image
 

00:13:49.680 --> 00:13:52.790 align:start position:0%
is a tool that fetches an actual image
of<00:13:50.040><c> a</c><00:13:50.399><c> cat</c><00:13:50.639><c> online</c><00:13:51.060><c> it</c><00:13:51.720><c> takes</c><00:13:51.959><c> no</c><00:13:52.200><c> input</c><00:13:52.560><c> and</c>

00:13:52.790 --> 00:13:52.800 align:start position:0%
of a cat online it takes no input and
 

00:13:52.800 --> 00:13:55.730 align:start position:0%
of a cat online it takes no input and
Returns<00:13:53.220><c> the</c><00:13:53.399><c> image</c><00:13:53.639><c> of</c><00:13:53.820><c> a</c><00:13:54.000><c> cat</c><00:13:54.180><c> so</c><00:13:55.079><c> that</c><00:13:55.380><c> shows</c>

00:13:55.730 --> 00:13:55.740 align:start position:0%
Returns the image of a cat so that shows
 

00:13:55.740 --> 00:13:58.370 align:start position:0%
Returns the image of a cat so that shows
how<00:13:55.980><c> it's</c><00:13:56.220><c> basically</c><00:13:56.579><c> put</c><00:13:56.880><c> together</c><00:13:57.120><c> if</c><00:13:58.019><c> we</c>

00:13:58.370 --> 00:13:58.380 align:start position:0%
how it's basically put together if we
 

00:13:58.380 --> 00:13:59.930 align:start position:0%
how it's basically put together if we
just<00:13:58.500><c> run</c><00:13:58.620><c> the</c><00:13:58.800><c> tool</c><00:13:59.100><c> we</c><00:13:59.220><c> can</c><00:13:59.399><c> see</c><00:13:59.519><c> that</c><00:13:59.700><c> okay</c>

00:13:59.930 --> 00:13:59.940 align:start position:0%
just run the tool we can see that okay
 

00:13:59.940 --> 00:14:03.050 align:start position:0%
just run the tool we can see that okay
you<00:14:00.540><c> know</c><00:14:00.600><c> it</c><00:14:00.899><c> will</c><00:14:01.019><c> run</c><00:14:01.320><c> if</c><00:14:02.040><c> we</c><00:14:02.160><c> run</c><00:14:02.399><c> the</c><00:14:02.639><c> tool</c>

00:14:03.050 --> 00:14:03.060 align:start position:0%
you know it will run if we run the tool
 

00:14:03.060 --> 00:14:07.250 align:start position:0%
you know it will run if we run the tool
from<00:14:03.540><c> from</c><00:14:04.380><c> an</c><00:14:04.920><c> actual</c><00:14:05.399><c> agent</c><00:14:06.180><c> so</c><00:14:06.899><c> if</c><00:14:07.019><c> we</c><00:14:07.139><c> now</c>

00:14:07.250 --> 00:14:07.260 align:start position:0%
from from an actual agent so if we now
 

00:14:07.260 --> 00:14:08.990 align:start position:0%
from from an actual agent so if we now
say<00:14:07.440><c> fetch</c><00:14:07.740><c> an</c><00:14:07.800><c> image</c><00:14:07.980><c> of</c><00:14:08.100><c> a</c><00:14:08.220><c> cat</c><00:14:08.339><c> online</c><00:14:08.579><c> and</c>

00:14:08.990 --> 00:14:09.000 align:start position:0%
say fetch an image of a cat online and
 

00:14:09.000 --> 00:14:11.569 align:start position:0%
say fetch an image of a cat online and
caption<00:14:09.300><c> it</c><00:14:09.540><c> for</c><00:14:09.720><c> me</c><00:14:09.899><c> here</c><00:14:10.860><c> it's</c><00:14:11.100><c> basically</c>

00:14:11.569 --> 00:14:11.579 align:start position:0%
caption it for me here it's basically
 

00:14:11.579 --> 00:14:14.629 align:start position:0%
caption it for me here it's basically
getting<00:14:12.120><c> the</c><00:14:12.480><c> image</c><00:14:12.959><c> and</c><00:14:13.920><c> then</c><00:14:14.160><c> it's</c><00:14:14.459><c> just</c>

00:14:14.629 --> 00:14:14.639 align:start position:0%
getting the image and then it's just
 

00:14:14.639 --> 00:14:17.150 align:start position:0%
getting the image and then it's just
using<00:14:15.120><c> the</c><00:14:15.480><c> blip</c><00:14:15.839><c> model</c><00:14:16.019><c> I'm</c><00:14:16.320><c> pretty</c><00:14:16.560><c> sure</c><00:14:16.680><c> to</c>

00:14:17.150 --> 00:14:17.160 align:start position:0%
using the blip model I'm pretty sure to
 

00:14:17.160 --> 00:14:19.670 align:start position:0%
using the blip model I'm pretty sure to
write<00:14:17.519><c> the</c><00:14:17.820><c> caption</c><00:14:18.180><c> of</c><00:14:18.420><c> a</c><00:14:18.600><c> cat</c><00:14:18.899><c> called</c><00:14:19.200><c> on</c><00:14:19.560><c> a</c>

00:14:19.670 --> 00:14:19.680 align:start position:0%
write the caption of a cat called on a
 

00:14:19.680 --> 00:14:22.610 align:start position:0%
write the caption of a cat called on a
pillow<00:14:20.100><c> so</c><00:14:20.880><c> just</c><00:14:21.240><c> quickly</c><00:14:21.600><c> looking</c><00:14:21.899><c> at</c><00:14:22.260><c> there</c>

00:14:22.610 --> 00:14:22.620 align:start position:0%
pillow so just quickly looking at there
 

00:14:22.620 --> 00:14:24.949 align:start position:0%
pillow so just quickly looking at there
at<00:14:23.160><c> the</c><00:14:23.339><c> source</c><00:14:23.579><c> code</c><00:14:23.700><c> so</c><00:14:24.120><c> so</c><00:14:24.540><c> you</c><00:14:24.779><c> know</c><00:14:24.779><c> I</c>

00:14:24.949 --> 00:14:24.959 align:start position:0%
at the source code so so you know I
 

00:14:24.959 --> 00:14:26.269 align:start position:0%
at the source code so so you know I
wanted<00:14:25.019><c> to</c><00:14:25.200><c> see</c><00:14:25.320><c> like</c><00:14:25.500><c> are</c><00:14:25.800><c> they</c><00:14:25.980><c> using</c><00:14:26.220><c> the</c>

00:14:26.269 --> 00:14:26.279 align:start position:0%
wanted to see like are they using the
 

00:14:26.279 --> 00:14:28.069 align:start position:0%
wanted to see like are they using the
name<00:14:26.519><c> chain</c><00:14:26.760><c> to</c><00:14:27.000><c> do</c><00:14:27.060><c> this</c><00:14:27.180><c> what</c><00:14:27.540><c> are</c><00:14:27.660><c> they</c><00:14:27.779><c> how</c>

00:14:28.069 --> 00:14:28.079 align:start position:0%
name chain to do this what are they how
 

00:14:28.079 --> 00:14:29.870 align:start position:0%
name chain to do this what are they how
are<00:14:28.260><c> they</c><00:14:28.380><c> putting</c><00:14:28.740><c> this</c><00:14:28.800><c> together</c><00:14:29.040><c> so</c><00:14:29.760><c> it</c>

00:14:29.870 --> 00:14:29.880 align:start position:0%
are they putting this together so it
 

00:14:29.880 --> 00:14:31.610 align:start position:0%
are they putting this together so it
turns<00:14:30.120><c> out</c><00:14:30.180><c> they're</c><00:14:30.360><c> not</c><00:14:30.779><c> using</c><00:14:31.019><c> Lang</c><00:14:31.260><c> chain</c>

00:14:31.610 --> 00:14:31.620 align:start position:0%
turns out they're not using Lang chain
 

00:14:31.620 --> 00:14:35.090 align:start position:0%
turns out they're not using Lang chain
in<00:14:32.040><c> here</c><00:14:32.220><c> they've</c><00:14:33.060><c> got</c><00:14:33.300><c> their</c><00:14:33.779><c> own</c><00:14:34.079><c> system</c><00:14:34.740><c> of</c>

00:14:35.090 --> 00:14:35.100 align:start position:0%
in here they've got their own system of
 

00:14:35.100 --> 00:14:37.250 align:start position:0%
in here they've got their own system of
doing<00:14:35.519><c> things</c><00:14:35.820><c> so</c><00:14:36.600><c> they've</c><00:14:37.019><c> basically</c>

00:14:37.250 --> 00:14:37.260 align:start position:0%
doing things so they've basically
 

00:14:37.260 --> 00:14:39.410 align:start position:0%
doing things so they've basically
created<00:14:37.800><c> this</c><00:14:38.459><c> sort</c><00:14:38.639><c> of</c><00:14:38.700><c> tools</c><00:14:39.060><c> section</c>

00:14:39.410 --> 00:14:39.420 align:start position:0%
created this sort of tools section
 

00:14:39.420 --> 00:14:42.650 align:start position:0%
created this sort of tools section
inside<00:14:40.040><c> transformers</c><00:14:41.040><c> now</c><00:14:41.699><c> that</c><00:14:42.300><c> has</c><00:14:42.420><c> the</c>

00:14:42.650 --> 00:14:42.660 align:start position:0%
inside transformers now that has the
 

00:14:42.660 --> 00:14:45.110 align:start position:0%
inside transformers now that has the
agents<00:14:43.199><c> and</c><00:14:43.620><c> then</c><00:14:43.740><c> has</c><00:14:43.980><c> the</c><00:14:44.220><c> various</c><00:14:44.579><c> tools</c>

00:14:45.110 --> 00:14:45.120 align:start position:0%
agents and then has the various tools
 

00:14:45.120 --> 00:14:48.470 align:start position:0%
agents and then has the various tools
each<00:14:46.019><c> one's</c><00:14:46.440><c> it's</c><00:14:46.800><c> sitting</c><00:14:47.160><c> in</c><00:14:47.279><c> here</c><00:14:47.459><c> so</c><00:14:47.820><c> if</c><00:14:48.300><c> we</c>

00:14:48.470 --> 00:14:48.480 align:start position:0%
each one's it's sitting in here so if we
 

00:14:48.480 --> 00:14:51.470 align:start position:0%
each one's it's sitting in here so if we
look<00:14:48.660><c> at</c><00:14:48.899><c> the</c><00:14:49.260><c> agents</c><00:14:49.860><c> we</c><00:14:50.459><c> can</c><00:14:50.639><c> see</c><00:14:50.880><c> that</c><00:14:51.120><c> we've</c>

00:14:51.470 --> 00:14:51.480 align:start position:0%
look at the agents we can see that we've
 

00:14:51.480 --> 00:14:53.389 align:start position:0%
look at the agents we can see that we've
got<00:14:51.600><c> a</c><00:14:51.779><c> whole</c><00:14:51.839><c> bunch</c><00:14:51.959><c> of</c><00:14:52.139><c> code</c><00:14:52.380><c> in</c><00:14:52.620><c> there</c><00:14:52.860><c> for</c>

00:14:53.389 --> 00:14:53.399 align:start position:0%
got a whole bunch of code in there for
 

00:14:53.399 --> 00:14:55.370 align:start position:0%
got a whole bunch of code in there for
pinging<00:14:54.180><c> it</c><00:14:54.300><c> and</c><00:14:54.660><c> really</c><00:14:54.839><c> probably</c><00:14:55.019><c> what's</c>

00:14:55.370 --> 00:14:55.380 align:start position:0%
pinging it and really probably what's
 

00:14:55.380 --> 00:14:58.790 align:start position:0%
pinging it and really probably what's
more<00:14:55.680><c> interesting</c><00:14:56.279><c> is</c><00:14:57.060><c> if</c><00:14:57.300><c> we</c><00:14:57.480><c> look</c><00:14:57.720><c> into</c><00:14:58.260><c> the</c>

00:14:58.790 --> 00:14:58.800 align:start position:0%
more interesting is if we look into the
 

00:14:58.800 --> 00:15:02.269 align:start position:0%
more interesting is if we look into the
prompts<00:14:59.279><c> so</c><00:15:00.060><c> this</c><00:15:00.360><c> is</c><00:15:00.480><c> the</c><00:15:00.720><c> tool</c><00:15:01.139><c> prompts</c><00:15:01.680><c> for</c>

00:15:02.269 --> 00:15:02.279 align:start position:0%
prompts so this is the tool prompts for
 

00:15:02.279 --> 00:15:03.530 align:start position:0%
prompts so this is the tool prompts for
what<00:15:02.459><c> it's</c><00:15:02.579><c> doing</c><00:15:02.760><c> so</c><00:15:02.940><c> we</c><00:15:03.120><c> can</c><00:15:03.240><c> see</c><00:15:03.360><c> that</c>

00:15:03.530 --> 00:15:03.540 align:start position:0%
what it's doing so we can see that
 

00:15:03.540 --> 00:15:06.230 align:start position:0%
what it's doing so we can see that
there's<00:15:03.720><c> a</c><00:15:03.959><c> run</c><00:15:04.139><c> prompt</c><00:15:04.680><c> for</c><00:15:05.339><c> this</c><00:15:05.519><c> and</c><00:15:05.820><c> for</c>

00:15:06.230 --> 00:15:06.240 align:start position:0%
there's a run prompt for this and for
 

00:15:06.240 --> 00:15:07.430 align:start position:0%
there's a run prompt for this and for
the<00:15:06.360><c> Dell</c><00:15:06.540><c> I</c><00:15:06.660><c> think</c><00:15:06.779><c> you'll</c><00:15:06.899><c> see</c><00:15:07.019><c> that</c><00:15:07.199><c> there's</c>

00:15:07.430 --> 00:15:07.440 align:start position:0%
the Dell I think you'll see that there's
 

00:15:07.440 --> 00:15:09.769 align:start position:0%
the Dell I think you'll see that there's
a<00:15:07.740><c> a</c><00:15:08.040><c> chat</c><00:15:08.339><c> prompt</c><00:15:08.699><c> as</c><00:15:08.940><c> well</c>

00:15:09.769 --> 00:15:09.779 align:start position:0%
a a chat prompt as well
 

00:15:09.779 --> 00:15:11.329 align:start position:0%
a a chat prompt as well
so<00:15:10.199><c> when</c><00:15:10.320><c> we</c><00:15:10.440><c> look</c><00:15:10.560><c> at</c><00:15:10.680><c> the</c><00:15:10.800><c> prompt</c><00:15:11.100><c> it's</c>

00:15:11.329 --> 00:15:11.339 align:start position:0%
so when we look at the prompt it's
 

00:15:11.339 --> 00:15:12.970 align:start position:0%
so when we look at the prompt it's
basically<00:15:11.639><c> I</c><00:15:11.940><c> will</c><00:15:12.060><c> ask</c><00:15:12.240><c> you</c><00:15:12.420><c> to</c><00:15:12.720><c> perform</c>

00:15:12.970 --> 00:15:12.980 align:start position:0%
basically I will ask you to perform
 

00:15:12.980 --> 00:15:15.350 align:start position:0%
basically I will ask you to perform
tasks<00:15:13.980><c> your</c><00:15:14.279><c> job</c><00:15:14.399><c> is</c><00:15:14.579><c> to</c><00:15:14.699><c> come</c><00:15:14.820><c> up</c><00:15:14.940><c> with</c><00:15:15.120><c> a</c>

00:15:15.350 --> 00:15:15.360 align:start position:0%
tasks your job is to come up with a
 

00:15:15.360 --> 00:15:17.930 align:start position:0%
tasks your job is to come up with a
series<00:15:15.540><c> of</c><00:15:15.779><c> simple</c><00:15:16.199><c> commands</c>

00:15:17.930 --> 00:15:17.940 align:start position:0%
series of simple commands
 

00:15:17.940 --> 00:15:20.569 align:start position:0%
series of simple commands
and<00:15:18.060><c> close</c><00:15:18.540><c> that</c><00:15:18.839><c> in</c><00:15:19.620><c> Python</c><00:15:20.100><c> that</c><00:15:20.339><c> will</c><00:15:20.459><c> help</c>

00:15:20.569 --> 00:15:20.579 align:start position:0%
and close that in Python that will help
 

00:15:20.579 --> 00:15:23.750 align:start position:0%
and close that in Python that will help
perform<00:15:20.880><c> a</c><00:15:21.300><c> task</c><00:15:21.480><c> so</c><00:15:22.260><c> and</c><00:15:22.500><c> then</c><00:15:22.680><c> it</c><00:15:23.220><c> goes</c><00:15:23.639><c> on</c>

00:15:23.750 --> 00:15:23.760 align:start position:0%
perform a task so and then it goes on
 

00:15:23.760 --> 00:15:25.850 align:start position:0%
perform a task so and then it goes on
and<00:15:24.000><c> then</c><00:15:24.180><c> they're</c><00:15:24.540><c> injecting</c><00:15:25.139><c> all</c><00:15:25.380><c> the</c><00:15:25.500><c> tools</c>

00:15:25.850 --> 00:15:25.860 align:start position:0%
and then they're injecting all the tools
 

00:15:25.860 --> 00:15:29.030 align:start position:0%
and then they're injecting all the tools
in<00:15:26.100><c> here</c><00:15:26.279><c> so</c><00:15:27.240><c> this</c><00:15:27.660><c> is</c><00:15:28.019><c> the</c><00:15:28.320><c> same</c><00:15:28.440><c> as</c><00:15:28.680><c> what</c><00:15:28.920><c> we</c>

00:15:29.030 --> 00:15:29.040 align:start position:0%
in here so this is the same as what we
 

00:15:29.040 --> 00:15:31.610 align:start position:0%
in here so this is the same as what we
would<00:15:29.220><c> do</c><00:15:29.459><c> in</c><00:15:29.940><c> Lang</c><00:15:30.180><c> chain</c><00:15:30.540><c> where</c><00:15:31.260><c> we</c><00:15:31.440><c> would</c>

00:15:31.610 --> 00:15:31.620 align:start position:0%
would do in Lang chain where we would
 

00:15:31.620 --> 00:15:33.650 align:start position:0%
would do in Lang chain where we would
basically<00:15:31.980><c> inject</c><00:15:32.399><c> the</c><00:15:32.639><c> tools</c><00:15:33.000><c> into</c><00:15:33.360><c> the</c>

00:15:33.650 --> 00:15:33.660 align:start position:0%
basically inject the tools into the
 

00:15:33.660 --> 00:15:35.530 align:start position:0%
basically inject the tools into the
agent<00:15:34.079><c> that</c><00:15:34.620><c> then</c><00:15:34.860><c> we're</c><00:15:35.040><c> going</c><00:15:35.220><c> to</c><00:15:35.339><c> use</c>

00:15:35.530 --> 00:15:35.540 align:start position:0%
agent that then we're going to use
 

00:15:35.540 --> 00:15:38.509 align:start position:0%
agent that then we're going to use
they've<00:15:36.540><c> then</c><00:15:36.660><c> got</c><00:15:37.019><c> some</c><00:15:37.440><c> some</c><00:15:37.920><c> in</c><00:15:38.220><c> context</c>

00:15:38.509 --> 00:15:38.519 align:start position:0%
they've then got some some in context
 

00:15:38.519 --> 00:15:40.670 align:start position:0%
they've then got some some in context
learning<00:15:39.000><c> to</c><00:15:39.300><c> basically</c><00:15:39.600><c> give</c><00:15:39.899><c> some</c><00:15:40.139><c> examples</c>

00:15:40.670 --> 00:15:40.680 align:start position:0%
learning to basically give some examples
 

00:15:40.680 --> 00:15:43.610 align:start position:0%
learning to basically give some examples
for<00:15:41.279><c> different</c><00:15:41.519><c> things</c><00:15:41.820><c> that</c><00:15:42.180><c> like</c><00:15:42.360><c> okay</c><00:15:42.720><c> for</c>

00:15:43.610 --> 00:15:43.620 align:start position:0%
for different things that like okay for
 

00:15:43.620 --> 00:15:47.090 align:start position:0%
for different things that like okay for
this<00:15:43.980><c> kind</c><00:15:44.220><c> of</c><00:15:44.399><c> input</c><00:15:45.120><c> this</c><00:15:45.839><c> is</c><00:15:45.959><c> the</c><00:15:46.320><c> the</c><00:15:46.560><c> where</c>

00:15:47.090 --> 00:15:47.100 align:start position:0%
this kind of input this is the the where
 

00:15:47.100 --> 00:15:49.430 align:start position:0%
this kind of input this is the the where
you<00:15:47.279><c> would</c><00:15:47.399><c> phrase</c><00:15:47.699><c> the</c><00:15:47.940><c> output</c><00:15:48.300><c> and</c><00:15:48.959><c> this</c><00:15:49.380><c> is</c>

00:15:49.430 --> 00:15:49.440 align:start position:0%
you would phrase the output and this is
 

00:15:49.440 --> 00:15:51.889 align:start position:0%
you would phrase the output and this is
the<00:15:49.680><c> actual</c><00:15:50.040><c> code</c><00:15:50.339><c> that</c><00:15:50.639><c> you</c><00:15:50.760><c> would</c><00:15:50.880><c> write</c><00:15:51.120><c> to</c>

00:15:51.889 --> 00:15:51.899 align:start position:0%
the actual code that you would write to
 

00:15:51.899 --> 00:15:53.030 align:start position:0%
the actual code that you would write to
do<00:15:52.079><c> that</c>

00:15:53.030 --> 00:15:53.040 align:start position:0%
do that
 

00:15:53.040 --> 00:15:54.470 align:start position:0%
do that
so<00:15:53.459><c> they've</c><00:15:53.639><c> got</c><00:15:53.760><c> you</c><00:15:53.940><c> know</c><00:15:54.060><c> a</c><00:15:54.180><c> bunch</c><00:15:54.360><c> of</c>

00:15:54.470 --> 00:15:54.480 align:start position:0%
so they've got you know a bunch of
 

00:15:54.480 --> 00:15:57.110 align:start position:0%
so they've got you know a bunch of
examples<00:15:55.019><c> in</c><00:15:55.320><c> there</c><00:15:55.560><c> and</c><00:15:56.519><c> then</c><00:15:56.639><c> they've</c><00:15:57.000><c> got</c>

00:15:57.110 --> 00:15:57.120 align:start position:0%
examples in there and then they've got
 

00:15:57.120 --> 00:15:59.329 align:start position:0%
examples in there and then they've got
the<00:15:57.300><c> format</c><00:15:57.600><c> for</c><00:15:57.839><c> just</c><00:15:58.079><c> outputting</c><00:15:58.740><c> putting</c>

00:15:59.329 --> 00:15:59.339 align:start position:0%
the format for just outputting putting
 

00:15:59.339 --> 00:16:01.670 align:start position:0%
the format for just outputting putting
the<00:15:59.459><c> original</c><00:15:59.579><c> task</c><00:16:00.000><c> in</c><00:16:00.540><c> that</c><00:16:01.019><c> the</c><00:16:01.199><c> users</c><00:16:01.500><c> put</c>

00:16:01.670 --> 00:16:01.680 align:start position:0%
the original task in that the users put
 

00:16:01.680 --> 00:16:03.949 align:start position:0%
the original task in that the users put
in<00:16:01.860><c> there</c><00:16:02.040><c> and</c><00:16:02.760><c> then</c><00:16:02.940><c> it</c><00:16:03.360><c> basically</c><00:16:03.660><c> just</c>

00:16:03.949 --> 00:16:03.959 align:start position:0%
in there and then it basically just
 

00:16:03.959 --> 00:16:06.170 align:start position:0%
in there and then it basically just
outputs<00:16:04.500><c> the</c><00:16:04.680><c> result</c><00:16:04.860><c> okay</c><00:16:05.399><c> so</c><00:16:05.639><c> this</c><00:16:05.880><c> is</c><00:16:06.000><c> the</c>

00:16:06.170 --> 00:16:06.180 align:start position:0%
outputs the result okay so this is the
 

00:16:06.180 --> 00:16:08.509 align:start position:0%
outputs the result okay so this is the
chat<00:16:06.420><c> one</c><00:16:06.720><c> this</c><00:16:07.320><c> is</c><00:16:07.440><c> pretty</c><00:16:07.740><c> similar</c><00:16:08.220><c> it's</c>

00:16:08.509 --> 00:16:08.519 align:start position:0%
chat one this is pretty similar it's
 

00:16:08.519 --> 00:16:11.150 align:start position:0%
chat one this is pretty similar it's
maybe<00:16:08.820><c> a</c><00:16:09.060><c> little</c><00:16:09.180><c> bit</c><00:16:09.360><c> different</c><00:16:09.600><c> in</c><00:16:10.320><c> here</c><00:16:10.500><c> we</c>

00:16:11.150 --> 00:16:11.160 align:start position:0%
maybe a little bit different in here we
 

00:16:11.160 --> 00:16:13.129 align:start position:0%
maybe a little bit different in here we
can<00:16:11.279><c> see</c><00:16:11.459><c> that</c><00:16:11.699><c> we've</c><00:16:12.060><c> got</c><00:16:12.240><c> them</c><00:16:12.540><c> injecting</c>

00:16:13.129 --> 00:16:13.139 align:start position:0%
can see that we've got them injecting
 

00:16:13.139 --> 00:16:15.590 align:start position:0%
can see that we've got them injecting
the<00:16:13.440><c> tools</c><00:16:13.800><c> in</c><00:16:14.220><c> there</c><00:16:14.459><c> and</c><00:16:15.060><c> we've</c><00:16:15.240><c> also</c><00:16:15.480><c> got</c>

00:16:15.590 --> 00:16:15.600 align:start position:0%
the tools in there and we've also got
 

00:16:15.600 --> 00:16:18.170 align:start position:0%
the tools in there and we've also got
the<00:16:15.779><c> Ico</c><00:16:16.139><c> learning</c><00:16:16.680><c> going</c><00:16:16.920><c> on</c><00:16:17.220><c> in</c><00:16:17.399><c> there</c>

00:16:18.170 --> 00:16:18.180 align:start position:0%
the Ico learning going on in there
 

00:16:18.180 --> 00:16:20.689 align:start position:0%
the Ico learning going on in there
so<00:16:18.959><c> overall</c><00:16:19.560><c> it's</c><00:16:19.920><c> it's</c><00:16:20.160><c> an</c><00:16:20.339><c> interesting</c>

00:16:20.689 --> 00:16:20.699 align:start position:0%
so overall it's it's an interesting
 

00:16:20.699 --> 00:16:22.490 align:start position:0%
so overall it's it's an interesting
project<00:16:21.000><c> it's</c><00:16:21.540><c> certainly</c><00:16:21.899><c> worth</c><00:16:22.079><c> having</c><00:16:22.320><c> a</c>

00:16:22.490 --> 00:16:22.500 align:start position:0%
project it's certainly worth having a
 

00:16:22.500 --> 00:16:24.230 align:start position:0%
project it's certainly worth having a
play<00:16:22.680><c> with</c><00:16:22.920><c> just</c><00:16:23.220><c> to</c><00:16:23.399><c> sort</c><00:16:23.579><c> of</c><00:16:23.699><c> see</c><00:16:23.820><c> what</c><00:16:24.120><c> it</c>

00:16:24.230 --> 00:16:24.240 align:start position:0%
play with just to sort of see what it
 

00:16:24.240 --> 00:16:27.050 align:start position:0%
play with just to sort of see what it
can<00:16:24.420><c> do</c><00:16:24.660><c> and</c><00:16:25.620><c> it's</c><00:16:26.100><c> also</c><00:16:26.459><c> interesting</c><00:16:26.820><c> to</c>

00:16:27.050 --> 00:16:27.060 align:start position:0%
can do and it's also interesting to
 

00:16:27.060 --> 00:16:29.569 align:start position:0%
can do and it's also interesting to
think<00:16:27.180><c> that</c><00:16:27.480><c> you</c><00:16:27.839><c> could</c><00:16:28.019><c> basically</c><00:16:28.620><c> take</c><00:16:29.339><c> any</c>

00:16:29.569 --> 00:16:29.579 align:start position:0%
think that you could basically take any
 

00:16:29.579 --> 00:16:31.490 align:start position:0%
think that you could basically take any
of<00:16:29.820><c> these</c><00:16:30.120><c> tools</c><00:16:30.480><c> and</c><00:16:30.839><c> use</c><00:16:30.959><c> them</c><00:16:31.139><c> for</c><00:16:31.320><c> Lang</c>

00:16:31.490 --> 00:16:31.500 align:start position:0%
of these tools and use them for Lang
 

00:16:31.500 --> 00:16:33.350 align:start position:0%
of these tools and use them for Lang
chain<00:16:31.800><c> as</c><00:16:32.040><c> well</c><00:16:32.220><c> it</c><00:16:32.699><c> wouldn't</c><00:16:32.880><c> be</c><00:16:33.060><c> that</c>

00:16:33.350 --> 00:16:33.360 align:start position:0%
chain as well it wouldn't be that
 

00:16:33.360 --> 00:16:36.170 align:start position:0%
chain as well it wouldn't be that
difficult<00:16:33.779><c> to</c><00:16:34.260><c> write</c><00:16:34.500><c> these</c><00:16:34.920><c> as</c><00:16:35.399><c> custom</c><00:16:35.639><c> tools</c>

00:16:36.170 --> 00:16:36.180 align:start position:0%
difficult to write these as custom tools
 

00:16:36.180 --> 00:16:38.210 align:start position:0%
difficult to write these as custom tools
for<00:16:36.420><c> something</c><00:16:36.600><c> in</c><00:16:36.959><c> Lang</c><00:16:37.139><c> chain</c><00:16:37.440><c> so</c><00:16:37.980><c> if</c><00:16:38.100><c> you</c>

00:16:38.210 --> 00:16:38.220 align:start position:0%
for something in Lang chain so if you
 

00:16:38.220 --> 00:16:39.410 align:start position:0%
for something in Lang chain so if you
did<00:16:38.339><c> want</c><00:16:38.519><c> to</c><00:16:38.639><c> make</c><00:16:38.699><c> something</c><00:16:38.940><c> for</c><00:16:39.240><c> Lang</c>

00:16:39.410 --> 00:16:39.420 align:start position:0%
did want to make something for Lang
 

00:16:39.420 --> 00:16:41.389 align:start position:0%
did want to make something for Lang
Chang<00:16:39.720><c> where</c><00:16:40.079><c> you</c><00:16:40.199><c> were</c><00:16:40.440><c> basically</c><00:16:40.860><c> doing</c>

00:16:41.389 --> 00:16:41.399 align:start position:0%
Chang where you were basically doing
 

00:16:41.399 --> 00:16:43.910 align:start position:0%
Chang where you were basically doing
text<00:16:41.759><c> to</c><00:16:42.000><c> speech</c><00:16:42.420><c> or</c><00:16:42.660><c> speech</c><00:16:43.079><c> to</c><00:16:43.139><c> text</c><00:16:43.380><c> or</c>

00:16:43.910 --> 00:16:43.920 align:start position:0%
text to speech or speech to text or
 

00:16:43.920 --> 00:16:46.670 align:start position:0%
text to speech or speech to text or
something<00:16:44.759><c> like</c><00:16:45.180><c> that</c><00:16:45.420><c> that</c><00:16:45.899><c> maybe</c><00:16:46.139><c> there</c>

00:16:46.670 --> 00:16:46.680 align:start position:0%
something like that that maybe there
 

00:16:46.680 --> 00:16:48.530 align:start position:0%
something like that that maybe there
isn't<00:16:46.980><c> a</c><00:16:47.160><c> clear</c><00:16:47.279><c> off-the-shelf</c><00:16:48.000><c> tool</c><00:16:48.360><c> already</c>

00:16:48.530 --> 00:16:48.540 align:start position:0%
isn't a clear off-the-shelf tool already
 

00:16:48.540 --> 00:16:51.170 align:start position:0%
isn't a clear off-the-shelf tool already
this<00:16:49.259><c> would</c><00:16:49.380><c> give</c><00:16:49.560><c> you</c><00:16:49.680><c> a</c><00:16:49.860><c> good</c><00:16:49.920><c> way</c><00:16:50.220><c> of</c>

00:16:51.170 --> 00:16:51.180 align:start position:0%
this would give you a good way of
 

00:16:51.180 --> 00:16:53.689 align:start position:0%
this would give you a good way of
seeing<00:16:51.959><c> how</c><00:16:52.139><c> to</c><00:16:52.320><c> sort</c><00:16:52.500><c> of</c><00:16:52.620><c> write</c><00:16:52.800><c> a</c><00:16:53.040><c> tool</c><00:16:53.339><c> that</c>

00:16:53.689 --> 00:16:53.699 align:start position:0%
seeing how to sort of write a tool that
 

00:16:53.699 --> 00:16:55.310 align:start position:0%
seeing how to sort of write a tool that
could<00:16:53.940><c> actually</c><00:16:54.180><c> do</c><00:16:54.540><c> that</c>

00:16:55.310 --> 00:16:55.320 align:start position:0%
could actually do that
 

00:16:55.320 --> 00:16:57.230 align:start position:0%
could actually do that
anyway<00:16:55.740><c> have</c><00:16:56.279><c> a</c><00:16:56.459><c> play</c><00:16:56.519><c> with</c><00:16:56.699><c> the</c><00:16:56.880><c> notebook</c>

00:16:57.230 --> 00:16:57.240 align:start position:0%
anyway have a play with the notebook
 

00:16:57.240 --> 00:16:59.030 align:start position:0%
anyway have a play with the notebook
it's<00:16:57.779><c> definitely</c><00:16:58.019><c> a</c><00:16:58.199><c> fun</c><00:16:58.380><c> thing</c><00:16:58.620><c> to</c><00:16:58.800><c> sort</c><00:16:58.920><c> of</c>

00:16:59.030 --> 00:16:59.040 align:start position:0%
it's definitely a fun thing to sort of
 

00:16:59.040 --> 00:17:00.530 align:start position:0%
it's definitely a fun thing to sort of
look<00:16:59.160><c> at</c><00:16:59.339><c> there</c><00:16:59.880><c> are</c><00:17:00.000><c> definitely</c><00:17:00.180><c> some</c><00:17:00.360><c> real</c>

00:17:00.530 --> 00:17:00.540 align:start position:0%
look at there are definitely some real
 

00:17:00.540 --> 00:17:01.970 align:start position:0%
look at there are definitely some real
world<00:17:00.720><c> uses</c><00:17:01.139><c> here</c><00:17:01.320><c> with</c><00:17:01.560><c> the</c><00:17:01.680><c> document</c>

00:17:01.970 --> 00:17:01.980 align:start position:0%
world uses here with the document
 

00:17:01.980 --> 00:17:03.889 align:start position:0%
world uses here with the document
question<00:17:02.279><c> understanding</c><00:17:03.060><c> and</c><00:17:03.240><c> those</c><00:17:03.600><c> kind</c><00:17:03.779><c> of</c>

00:17:03.889 --> 00:17:03.899 align:start position:0%
question understanding and those kind of
 

00:17:03.899 --> 00:17:06.770 align:start position:0%
question understanding and those kind of
things<00:17:04.020><c> in</c><00:17:04.500><c> here</c><00:17:04.679><c> as</c><00:17:05.640><c> always</c><00:17:05.819><c> if</c><00:17:06.480><c> you've</c><00:17:06.660><c> got</c>

00:17:06.770 --> 00:17:06.780 align:start position:0%
things in here as always if you've got
 

00:17:06.780 --> 00:17:08.390 align:start position:0%
things in here as always if you've got
any<00:17:06.959><c> questions</c><00:17:07.260><c> please</c><00:17:07.740><c> put</c><00:17:07.980><c> them</c><00:17:08.100><c> in</c><00:17:08.280><c> the</c>

00:17:08.390 --> 00:17:08.400 align:start position:0%
any questions please put them in the
 

00:17:08.400 --> 00:17:10.610 align:start position:0%
any questions please put them in the
comments<00:17:08.699><c> below</c><00:17:09.120><c> if</c><00:17:09.900><c> you</c><00:17:10.079><c> like</c><00:17:10.199><c> the</c><00:17:10.380><c> video</c>

00:17:10.610 --> 00:17:10.620 align:start position:0%
comments below if you like the video
 

00:17:10.620 --> 00:17:13.130 align:start position:0%
comments below if you like the video
please<00:17:11.040><c> click</c><00:17:11.400><c> like</c><00:17:11.520><c> And</c><00:17:11.760><c> subscribe</c><00:17:12.240><c> I</c><00:17:12.900><c> will</c>

00:17:13.130 --> 00:17:13.140 align:start position:0%
please click like And subscribe I will
 

00:17:13.140 --> 00:17:15.049 align:start position:0%
please click like And subscribe I will
talk<00:17:13.559><c> to</c><00:17:13.679><c> you</c><00:17:13.740><c> in</c><00:17:13.860><c> the</c><00:17:13.980><c> next</c><00:17:14.100><c> video</c><00:17:14.339><c> bye</c><00:17:14.939><c> for</c>

00:17:15.049 --> 00:17:15.059 align:start position:0%
talk to you in the next video bye for
 

00:17:15.059 --> 00:17:17.240 align:start position:0%
talk to you in the next video bye for
now

