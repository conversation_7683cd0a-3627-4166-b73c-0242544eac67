WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.510 align:start position:0%
 
many<00:00:00.320><c> Engineers</c><00:00:00.840><c> think</c><00:00:01.000><c> when</c><00:00:01.120><c> they</c><00:00:01.240><c> get</c><00:00:01.360><c> into</c>

00:00:01.510 --> 00:00:01.520 align:start position:0%
many Engineers think when they get into
 

00:00:01.520 --> 00:00:02.750 align:start position:0%
many Engineers think when they get into
the<00:00:01.599><c> field</c><00:00:01.920><c> they</c><00:00:02.000><c> will</c><00:00:02.120><c> create</c><00:00:02.360><c> the</c><00:00:02.480><c> most</c>

00:00:02.750 --> 00:00:02.760 align:start position:0%
the field they will create the most
 

00:00:02.760 --> 00:00:04.870 align:start position:0%
the field they will create the most
updated<00:00:03.159><c> Services</c><00:00:03.800><c> libraries</c><00:00:04.560><c> use</c><00:00:04.799><c> the</c>

00:00:04.870 --> 00:00:04.880 align:start position:0%
updated Services libraries use the
 

00:00:04.880 --> 00:00:06.869 align:start position:0%
updated Services libraries use the
newest<00:00:05.200><c> languages</c><00:00:05.680><c> and</c><00:00:05.799><c> start</c><00:00:06.000><c> a</c><00:00:06.200><c> brand</c><00:00:06.560><c> new</c>

00:00:06.869 --> 00:00:06.879 align:start position:0%
newest languages and start a brand new
 

00:00:06.879 --> 00:00:09.390 align:start position:0%
newest languages and start a brand new
project<00:00:07.520><c> well</c><00:00:07.720><c> the</c><00:00:08.000><c> problem</c><00:00:08.320><c> is</c><00:00:08.840><c> this</c><00:00:09.080><c> isn't</c>

00:00:09.390 --> 00:00:09.400 align:start position:0%
project well the problem is this isn't
 

00:00:09.400 --> 00:00:11.709 align:start position:0%
project well the problem is this isn't
always<00:00:09.679><c> the</c><00:00:09.880><c> case</c><00:00:10.559><c> realistically</c><00:00:11.360><c> besides</c>

00:00:11.709 --> 00:00:11.719 align:start position:0%
always the case realistically besides
 

00:00:11.719 --> 00:00:13.789 align:start position:0%
always the case realistically besides
startups<00:00:12.360><c> you</c><00:00:12.440><c> will</c><00:00:12.639><c> modify</c><00:00:13.160><c> existing</c><00:00:13.519><c> code</c>

00:00:13.789 --> 00:00:13.799 align:start position:0%
startups you will modify existing code
 

00:00:13.799 --> 00:00:16.390 align:start position:0%
startups you will modify existing code
bases<00:00:14.320><c> adding</c><00:00:14.599><c> onto</c><00:00:14.920><c> some</c><00:00:15.200><c> functionality</c><00:00:16.160><c> but</c>

00:00:16.390 --> 00:00:16.400 align:start position:0%
bases adding onto some functionality but
 

00:00:16.400 --> 00:00:18.670 align:start position:0%
bases adding onto some functionality but
at<00:00:16.560><c> the</c><00:00:16.720><c> same</c><00:00:17.080><c> time</c><00:00:17.600><c> you</c><00:00:17.760><c> can't</c><00:00:18.039><c> change</c><00:00:18.480><c> what</c>

00:00:18.670 --> 00:00:18.680 align:start position:0%
at the same time you can't change what
 

00:00:18.680 --> 00:00:20.509 align:start position:0%
at the same time you can't change what
already<00:00:19.039><c> Works</c><00:00:19.640><c> no</c><00:00:19.800><c> matter</c><00:00:20.080><c> how</c><00:00:20.199><c> much</c><00:00:20.400><c> you</c>

00:00:20.509 --> 00:00:20.519 align:start position:0%
already Works no matter how much you
 

00:00:20.519 --> 00:00:22.550 align:start position:0%
already Works no matter how much you
want<00:00:20.680><c> to</c><00:00:20.840><c> refactor</c><00:00:21.600><c> so</c><00:00:21.760><c> I</c><00:00:21.880><c> think</c><00:00:22.000><c> a</c><00:00:22.080><c> realist</c>

00:00:22.550 --> 00:00:22.560 align:start position:0%
want to refactor so I think a realist
 

00:00:22.560 --> 00:00:24.670 align:start position:0%
want to refactor so I think a realist
example<00:00:22.880><c> for</c><00:00:23.119><c> an</c><00:00:23.279><c> AI</c><00:00:23.640><c> agent</c><00:00:24.119><c> is</c><00:00:24.240><c> to</c><00:00:24.359><c> have</c><00:00:24.480><c> an</c>

00:00:24.670 --> 00:00:24.680 align:start position:0%
example for an AI agent is to have an
 

00:00:24.680 --> 00:00:25.990 align:start position:0%
example for an AI agent is to have an
existing<00:00:25.000><c> codebase</c><00:00:25.400><c> and</c><00:00:25.560><c> have</c><00:00:25.640><c> it</c><00:00:25.800><c> either</c>

00:00:25.990 --> 00:00:26.000 align:start position:0%
existing codebase and have it either
 

00:00:26.000 --> 00:00:28.710 align:start position:0%
existing codebase and have it either
modify<00:00:26.800><c> or</c><00:00:27.039><c> add</c><00:00:27.279><c> on</c><00:00:27.519><c> to</c><00:00:27.880><c> what's</c><00:00:28.080><c> already</c><00:00:28.400><c> there</c>

00:00:28.710 --> 00:00:28.720 align:start position:0%
modify or add on to what's already there
 

00:00:28.720 --> 00:00:30.189 align:start position:0%
modify or add on to what's already there
I'm<00:00:28.840><c> going</c><00:00:28.920><c> to</c><00:00:29.000><c> be</c><00:00:29.119><c> using</c><00:00:29.320><c> autogen</c><00:00:29.800><c> which</c><00:00:30.000><c> is</c><00:00:30.080><c> a</c>

00:00:30.189 --> 00:00:30.199 align:start position:0%
I'm going to be using autogen which is a
 

00:00:30.199 --> 00:00:31.910 align:start position:0%
I'm going to be using autogen which is a
multi-agent<00:00:30.800><c> framework</c><00:00:31.199><c> meaning</c><00:00:31.480><c> we</c><00:00:31.560><c> can</c><00:00:31.679><c> use</c>

00:00:31.910 --> 00:00:31.920 align:start position:0%
multi-agent framework meaning we can use
 

00:00:31.920 --> 00:00:33.830 align:start position:0%
multi-agent framework meaning we can use
more<00:00:32.119><c> than</c><00:00:32.279><c> one</c><00:00:32.640><c> agent</c><00:00:32.960><c> to</c><00:00:33.160><c> fix</c><00:00:33.360><c> a</c><00:00:33.559><c> problem</c>

00:00:33.830 --> 00:00:33.840 align:start position:0%
more than one agent to fix a problem
 

00:00:33.840 --> 00:00:35.549 align:start position:0%
more than one agent to fix a problem
it'll<00:00:34.000><c> be</c><00:00:34.079><c> able</c><00:00:34.239><c> to</c><00:00:34.399><c> list</c><00:00:34.640><c> directories</c><00:00:35.399><c> look</c>

00:00:35.549 --> 00:00:35.559 align:start position:0%
it'll be able to list directories look
 

00:00:35.559 --> 00:00:38.150 align:start position:0%
it'll be able to list directories look
at<00:00:35.800><c> files</c><00:00:36.360><c> modify</c><00:00:37.000><c> a</c><00:00:37.160><c> file</c><00:00:37.520><c> and</c><00:00:37.640><c> then</c><00:00:37.760><c> also</c>

00:00:38.150 --> 00:00:38.160 align:start position:0%
at files modify a file and then also
 

00:00:38.160 --> 00:00:40.549 align:start position:0%
at files modify a file and then also
create<00:00:38.480><c> a</c><00:00:38.640><c> new</c><00:00:38.920><c> file</c><00:00:39.360><c> if</c><00:00:39.600><c> needed</c><00:00:40.160><c> okay</c><00:00:40.320><c> what</c><00:00:40.399><c> I</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
create a new file if needed okay what I
 

00:00:40.559 --> 00:00:42.389 align:start position:0%
create a new file if needed okay what I
have<00:00:40.760><c> here</c><00:00:40.960><c> is</c><00:00:41.039><c> a</c><00:00:41.200><c> main</c><00:00:41.520><c> python</c><00:00:41.920><c> file</c><00:00:42.239><c> that's</c>

00:00:42.389 --> 00:00:42.399 align:start position:0%
have here is a main python file that's
 

00:00:42.399 --> 00:00:44.709 align:start position:0%
have here is a main python file that's
set<00:00:42.600><c> up</c><00:00:42.719><c> to</c><00:00:42.920><c> run</c><00:00:43.239><c> autogen</c><00:00:44.200><c> it's</c><00:00:44.320><c> going</c><00:00:44.440><c> to</c><00:00:44.559><c> have</c>

00:00:44.709 --> 00:00:44.719 align:start position:0%
set up to run autogen it's going to have
 

00:00:44.719 --> 00:00:46.790 align:start position:0%
set up to run autogen it's going to have
a<00:00:44.920><c> default</c><00:00:45.399><c> path</c><00:00:45.600><c> for</c><00:00:45.800><c> the</c><00:00:45.960><c> backend</c><00:00:46.320><c> directory</c>

00:00:46.790 --> 00:00:46.800 align:start position:0%
a default path for the backend directory
 

00:00:46.800 --> 00:00:49.430 align:start position:0%
a default path for the backend directory
where<00:00:47.120><c> we</c><00:00:47.320><c> have</c><00:00:47.520><c> a</c><00:00:47.719><c> sample</c><00:00:48.120><c> code</c><00:00:48.520><c> located</c><00:00:49.079><c> here</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
where we have a sample code located here
 

00:00:49.440 --> 00:00:51.709 align:start position:0%
where we have a sample code located here
called<00:00:49.719><c> main.py</c><00:00:50.719><c> as</c><00:00:50.879><c> well</c><00:00:51.199><c> once</c><00:00:51.360><c> we</c><00:00:51.480><c> set</c><00:00:51.640><c> up</c>

00:00:51.709 --> 00:00:51.719 align:start position:0%
called main.py as well once we set up
 

00:00:51.719 --> 00:00:53.510 align:start position:0%
called main.py as well once we set up
all<00:00:51.840><c> the</c><00:00:51.960><c> configurations</c><00:00:52.879><c> I</c><00:00:53.039><c> basically</c><00:00:53.399><c> just</c>

00:00:53.510 --> 00:00:53.520 align:start position:0%
all the configurations I basically just
 

00:00:53.520 --> 00:00:55.869 align:start position:0%
all the configurations I basically just
created<00:00:53.840><c> an</c><00:00:54.079><c> engineer</c><00:00:54.640><c> agent</c><00:00:55.160><c> and</c><00:00:55.320><c> a</c><00:00:55.480><c> user</c>

00:00:55.869 --> 00:00:55.879 align:start position:0%
created an engineer agent and a user
 

00:00:55.879 --> 00:00:58.790 align:start position:0%
created an engineer agent and a user
proxy<00:00:56.359><c> agent</c><00:00:56.879><c> okay</c><00:00:57.079><c> these</c><00:00:57.239><c> are</c><00:00:57.559><c> simple</c><00:00:57.920><c> agents</c>

00:00:58.790 --> 00:00:58.800 align:start position:0%
proxy agent okay these are simple agents
 

00:00:58.800 --> 00:01:00.910 align:start position:0%
proxy agent okay these are simple agents
but<00:00:59.239><c> the</c><00:00:59.399><c> real</c><00:00:59.640><c> key</c><00:01:00.120><c> to</c><00:01:00.280><c> this</c><00:01:00.440><c> is</c><00:01:00.600><c> all</c><00:01:00.760><c> the</c>

00:01:00.910 --> 00:01:00.920 align:start position:0%
but the real key to this is all the
 

00:01:00.920 --> 00:01:03.509 align:start position:0%
but the real key to this is all the
tools<00:01:01.600><c> that</c><00:01:01.760><c> we</c><00:01:01.920><c> have</c><00:01:02.120><c> created</c><00:01:02.920><c> so</c><00:01:03.120><c> we</c><00:01:03.320><c> have</c>

00:01:03.509 --> 00:01:03.519 align:start position:0%
tools that we have created so we have
 

00:01:03.519 --> 00:01:06.630 align:start position:0%
tools that we have created so we have
one<00:01:03.719><c> to</c><00:01:04.040><c> list</c><00:01:04.479><c> the</c><00:01:04.720><c> directories</c><00:01:05.720><c> one</c><00:01:06.080><c> to</c><00:01:06.320><c> see</c>

00:01:06.630 --> 00:01:06.640 align:start position:0%
one to list the directories one to see
 

00:01:06.640 --> 00:01:09.910 align:start position:0%
one to list the directories one to see
the<00:01:06.880><c> files</c><00:01:07.520><c> in</c><00:01:07.680><c> that</c><00:01:07.960><c> directory</c><00:01:08.960><c> and</c><00:01:09.280><c> want</c><00:01:09.520><c> to</c>

00:01:09.910 --> 00:01:09.920 align:start position:0%
the files in that directory and want to
 

00:01:09.920 --> 00:01:13.109 align:start position:0%
the files in that directory and want to
modify<00:01:10.320><c> the</c><00:01:10.479><c> code</c><00:01:11.200><c> and</c><00:01:11.520><c> also</c><00:01:12.000><c> create</c><00:01:12.520><c> files</c>

00:01:13.109 --> 00:01:13.119 align:start position:0%
modify the code and also create files
 

00:01:13.119 --> 00:01:15.910 align:start position:0%
modify the code and also create files
with<00:01:13.400><c> code</c><00:01:14.159><c> if</c><00:01:14.479><c> we</c><00:01:14.640><c> need</c><00:01:14.880><c> to</c><00:01:15.400><c> okay</c><00:01:15.600><c> so</c><00:01:15.759><c> there</c>

00:01:15.910 --> 00:01:15.920 align:start position:0%
with code if we need to okay so there
 

00:01:15.920 --> 00:01:18.670 align:start position:0%
with code if we need to okay so there
are<00:01:16.119><c> four</c><00:01:16.680><c> tools</c><00:01:17.080><c> here</c><00:01:17.360><c> now</c><00:01:17.520><c> when</c><00:01:17.640><c> we</c><00:01:17.799><c> go</c><00:01:18.000><c> to</c>

00:01:18.670 --> 00:01:18.680 align:start position:0%
are four tools here now when we go to
 

00:01:18.680 --> 00:01:20.710 align:start position:0%
are four tools here now when we go to
initiate<00:01:19.159><c> the</c><00:01:19.320><c> chat</c><00:01:19.720><c> with</c><00:01:19.880><c> these</c><00:01:20.040><c> AI</c><00:01:20.360><c> agents</c>

00:01:20.710 --> 00:01:20.720 align:start position:0%
initiate the chat with these AI agents
 

00:01:20.720 --> 00:01:22.510 align:start position:0%
initiate the chat with these AI agents
we<00:01:20.840><c> just</c><00:01:20.960><c> want</c><00:01:21.119><c> them</c><00:01:21.240><c> to</c><00:01:21.479><c> improve</c><00:01:21.960><c> the</c><00:01:22.119><c> app</c><00:01:22.280><c> in</c>

00:01:22.510 --> 00:01:22.520 align:start position:0%
we just want them to improve the app in
 

00:01:22.520 --> 00:01:25.230 align:start position:0%
we just want them to improve the app in
fast<00:01:22.840><c> API</c><00:01:23.680><c> for</c><00:01:23.880><c> now</c><00:01:24.240><c> just</c><00:01:24.439><c> check</c><00:01:24.759><c> all</c><00:01:25.000><c> the</c>

00:01:25.230 --> 00:01:25.240 align:start position:0%
fast API for now just check all the
 

00:01:25.240 --> 00:01:26.870 align:start position:0%
fast API for now just check all the
files<00:01:25.640><c> try</c><00:01:25.799><c> to</c><00:01:26.119><c> understand</c><00:01:26.240><c> it</c><00:01:26.360><c> and</c><00:01:26.520><c> wait</c><00:01:26.720><c> for</c>

00:01:26.870 --> 00:01:26.880 align:start position:0%
files try to understand it and wait for
 

00:01:26.880 --> 00:01:29.230 align:start position:0%
files try to understand it and wait for
the<00:01:27.079><c> next</c><00:01:27.479><c> instructions</c><00:01:28.240><c> which</c><00:01:28.479><c> I</c><00:01:28.560><c> will</c><00:01:28.840><c> give</c>

00:01:29.230 --> 00:01:29.240 align:start position:0%
the next instructions which I will give
 

00:01:29.240 --> 00:01:30.670 align:start position:0%
the next instructions which I will give
okay<00:01:29.360><c> so</c><00:01:29.479><c> the</c><00:01:29.560><c> first</c><00:01:29.680><c> thing</c><00:01:30.079><c> did</c><00:01:30.240><c> was</c><00:01:30.360><c> look</c><00:01:30.520><c> for</c>

00:01:30.670 --> 00:01:30.680 align:start position:0%
okay so the first thing did was look for
 

00:01:30.680 --> 00:01:32.630 align:start position:0%
okay so the first thing did was look for
the<00:01:30.799><c> tool</c><00:01:31.240><c> list</c><00:01:31.560><c> directory</c><00:01:32.000><c> so</c><00:01:32.159><c> we</c><00:01:32.280><c> have</c><00:01:32.479><c> all</c>

00:01:32.630 --> 00:01:32.640 align:start position:0%
the tool list directory so we have all
 

00:01:32.640 --> 00:01:35.030 align:start position:0%
the tool list directory so we have all
the<00:01:32.799><c> directories</c><00:01:33.600><c> now</c><00:01:33.759><c> what</c><00:01:33.840><c> I</c><00:01:33.920><c> want</c><00:01:34.079><c> to</c><00:01:34.280><c> do</c><00:01:34.799><c> is</c>

00:01:35.030 --> 00:01:35.040 align:start position:0%
the directories now what I want to do is
 

00:01:35.040 --> 00:01:38.830 align:start position:0%
the directories now what I want to do is
modify<00:01:36.040><c> this</c><00:01:36.360><c> code</c><00:01:36.799><c> this</c><00:01:36.960><c> API</c><00:01:37.640><c> request</c><00:01:38.240><c> first</c>

00:01:38.830 --> 00:01:38.840 align:start position:0%
modify this code this API request first
 

00:01:38.840 --> 00:01:40.749 align:start position:0%
modify this code this API request first
inside<00:01:39.280><c> of</c><00:01:39.399><c> the</c><00:01:39.560><c> backend</c><00:01:39.960><c> directory</c><00:01:40.479><c> so</c><00:01:40.640><c> now</c>

00:01:40.749 --> 00:01:40.759 align:start position:0%
inside of the backend directory so now
 

00:01:40.759 --> 00:01:42.350 align:start position:0%
inside of the backend directory so now
what<00:01:40.880><c> I</c><00:01:40.960><c> want</c><00:01:41.079><c> to</c><00:01:41.200><c> do</c><00:01:41.360><c> is</c><00:01:41.560><c> edit</c><00:01:41.920><c> the</c><00:01:42.079><c> fetch</c>

00:01:42.350 --> 00:01:42.360 align:start position:0%
what I want to do is edit the fetch
 

00:01:42.360 --> 00:01:44.230 align:start position:0%
what I want to do is edit the fetch
to-do<00:01:42.680><c> to</c><00:01:42.840><c> instead</c><00:01:43.079><c> retrieve</c><00:01:43.439><c> all</c><00:01:43.640><c> to-dos</c><00:01:44.000><c> and</c>

00:01:44.230 --> 00:01:44.240 align:start position:0%
to-do to instead retrieve all to-dos and
 

00:01:44.240 --> 00:01:45.990 align:start position:0%
to-do to instead retrieve all to-dos and
return<00:01:44.399><c> the</c><00:01:44.520><c> first</c><00:01:44.799><c> five</c><00:01:45.360><c> then</c><00:01:45.520><c> what</c><00:01:45.600><c> I</c><00:01:45.719><c> did</c><00:01:45.880><c> is</c>

00:01:45.990 --> 00:01:46.000 align:start position:0%
return the first five then what I did is
 

00:01:46.000 --> 00:01:49.069 align:start position:0%
return the first five then what I did is
use<00:01:46.200><c> the</c><00:01:46.360><c> tool</c><00:01:46.759><c> C</c><00:01:47.200><c> file</c><00:01:47.719><c> retrieved</c><00:01:48.360><c> the</c><00:01:48.520><c> main.</c>

00:01:49.069 --> 00:01:49.079 align:start position:0%
use the tool C file retrieved the main.
 

00:01:49.079 --> 00:01:51.069 align:start position:0%
use the tool C file retrieved the main.
python<00:01:49.520><c> file</c><00:01:50.200><c> I</c><00:01:50.360><c> come</c><00:01:50.560><c> down</c><00:01:50.759><c> here</c><00:01:50.880><c> a</c><00:01:50.960><c> little</c>

00:01:51.069 --> 00:01:51.079 align:start position:0%
python file I come down here a little
 

00:01:51.079 --> 00:01:53.510 align:start position:0%
python file I come down here a little
bit<00:01:51.200><c> and</c><00:01:51.320><c> the</c><00:01:51.520><c> response</c><00:01:52.399><c> is</c><00:01:52.680><c> this</c><00:01:52.799><c> is</c><00:01:52.960><c> the</c><00:01:53.119><c> tool</c>

00:01:53.510 --> 00:01:53.520 align:start position:0%
bit and the response is this is the tool
 

00:01:53.520 --> 00:01:56.069 align:start position:0%
bit and the response is this is the tool
called<00:01:53.960><c> C</c><00:01:54.320><c> file</c><00:01:54.600><c> so</c><00:01:54.840><c> it</c><00:01:55.119><c> looking</c><00:01:55.680><c> it's</c><00:01:55.840><c> looking</c>

00:01:56.069 --> 00:01:56.079 align:start position:0%
called C file so it looking it's looking
 

00:01:56.079 --> 00:01:58.350 align:start position:0%
called C file so it looking it's looking
at<00:01:56.360><c> everything</c><00:01:56.880><c> inside</c><00:01:57.320><c> of</c><00:01:57.520><c> that</c><00:01:57.759><c> file</c><00:01:58.119><c> then</c>

00:01:58.350 --> 00:01:58.360 align:start position:0%
at everything inside of that file then
 

00:01:58.360 --> 00:02:00.389 align:start position:0%
at everything inside of that file then
we<00:01:58.479><c> come</c><00:01:58.719><c> down</c><00:01:59.000><c> here</c><00:01:59.479><c> we</c><00:01:59.640><c> next</c><00:02:00.039><c> have</c><00:02:00.200><c> the</c>

00:02:00.389 --> 00:02:00.399 align:start position:0%
we come down here we next have the
 

00:02:00.399 --> 00:02:02.870 align:start position:0%
we come down here we next have the
modified<00:02:01.039><c> code</c><00:02:01.320><c> tool</c><00:02:01.960><c> with</c><00:02:02.159><c> starting</c><00:02:02.439><c> at</c><00:02:02.640><c> line</c>

00:02:02.870 --> 00:02:02.880 align:start position:0%
modified code tool with starting at line
 

00:02:02.880 --> 00:02:04.910 align:start position:0%
modified code tool with starting at line
seven<00:02:03.200><c> and</c><00:02:03.320><c> ending</c><00:02:03.600><c> at</c><00:02:03.759><c> line</c><00:02:04.039><c> 12</c><00:02:04.560><c> so</c><00:02:04.719><c> if</c><00:02:04.799><c> I</c>

00:02:04.910 --> 00:02:04.920 align:start position:0%
seven and ending at line 12 so if I
 

00:02:04.920 --> 00:02:06.270 align:start position:0%
seven and ending at line 12 so if I
bring<00:02:05.119><c> this</c><00:02:05.280><c> down</c><00:02:05.439><c> here</c><00:02:05.560><c> a</c><00:02:05.680><c> little</c><00:02:05.840><c> bit</c><00:02:06.159><c> you</c>

00:02:06.270 --> 00:02:06.280 align:start position:0%
bring this down here a little bit you
 

00:02:06.280 --> 00:02:07.870 align:start position:0%
bring this down here a little bit you
can<00:02:06.360><c> see</c><00:02:06.520><c> it's</c><00:02:06.640><c> basically</c><00:02:06.960><c> editing</c><00:02:07.600><c> this</c>

00:02:07.870 --> 00:02:07.880 align:start position:0%
can see it's basically editing this
 

00:02:07.880 --> 00:02:10.070 align:start position:0%
can see it's basically editing this
whole<00:02:08.520><c> uh</c><00:02:08.679><c> this</c><00:02:08.840><c> whole</c><00:02:09.319><c> request</c><00:02:09.840><c> it's</c><00:02:09.959><c> going</c>

00:02:10.070 --> 00:02:10.080 align:start position:0%
whole uh this whole request it's going
 

00:02:10.080 --> 00:02:12.070 align:start position:0%
whole uh this whole request it's going
to<00:02:10.319><c> fix</c><00:02:10.560><c> it</c><00:02:11.000><c> and</c><00:02:11.160><c> if</c><00:02:11.280><c> we</c><00:02:11.360><c> scroll</c><00:02:11.640><c> to</c><00:02:11.800><c> the</c><00:02:11.959><c> right</c>

00:02:12.070 --> 00:02:12.080 align:start position:0%
to fix it and if we scroll to the right
 

00:02:12.080 --> 00:02:13.589 align:start position:0%
to fix it and if we scroll to the right
a<00:02:12.160><c> little</c><00:02:12.319><c> bit</c><00:02:12.599><c> I</c><00:02:12.680><c> can</c><00:02:12.840><c> see</c><00:02:13.120><c> that</c><00:02:13.280><c> instead</c><00:02:13.520><c> of</c>

00:02:13.589 --> 00:02:13.599 align:start position:0%
a little bit I can see that instead of
 

00:02:13.599 --> 00:02:15.150 align:start position:0%
a little bit I can see that instead of
just<00:02:13.720><c> getting</c><00:02:13.920><c> one</c><00:02:14.120><c> to</c><00:02:14.280><c> do</c><00:02:14.480><c> it's</c><00:02:14.640><c> getting</c><00:02:15.000><c> all</c>

00:02:15.150 --> 00:02:15.160 align:start position:0%
just getting one to do it's getting all
 

00:02:15.160 --> 00:02:17.550 align:start position:0%
just getting one to do it's getting all
of<00:02:15.319><c> them</c><00:02:15.760><c> and</c><00:02:15.920><c> it's</c><00:02:16.040><c> going</c><00:02:16.160><c> to</c><00:02:16.440><c> return</c><00:02:17.239><c> uh</c><00:02:17.400><c> in</c>

00:02:17.550 --> 00:02:17.560 align:start position:0%
of them and it's going to return uh in
 

00:02:17.560 --> 00:02:19.589 align:start position:0%
of them and it's going to return uh in
shorthand<00:02:18.160><c> the</c><00:02:18.319><c> first</c><00:02:18.640><c> five</c><00:02:19.080><c> since</c><00:02:19.280><c> I'm</c><00:02:19.440><c> good</c>

00:02:19.589 --> 00:02:19.599 align:start position:0%
shorthand the first five since I'm good
 

00:02:19.599 --> 00:02:21.309 align:start position:0%
shorthand the first five since I'm good
with<00:02:19.760><c> that</c><00:02:20.040><c> I'm</c><00:02:20.120><c> just</c><00:02:20.239><c> going</c><00:02:20.319><c> to</c><00:02:20.480><c> press</c><00:02:20.760><c> enter</c>

00:02:21.309 --> 00:02:21.319 align:start position:0%
with that I'm just going to press enter
 

00:02:21.319 --> 00:02:23.990 align:start position:0%
with that I'm just going to press enter
and<00:02:21.440><c> then</c><00:02:21.640><c> from</c><00:02:21.959><c> the</c><00:02:22.120><c> tool</c><00:02:22.440><c> call</c><00:02:23.000><c> it</c><00:02:23.239><c> modified</c>

00:02:23.990 --> 00:02:24.000 align:start position:0%
and then from the tool call it modified
 

00:02:24.000 --> 00:02:25.630 align:start position:0%
and then from the tool call it modified
the<00:02:24.120><c> code</c><00:02:24.480><c> now</c><00:02:24.599><c> if</c><00:02:24.680><c> I</c><00:02:24.800><c> look</c><00:02:24.920><c> at</c><00:02:25.000><c> the</c><00:02:25.120><c> code</c><00:02:25.360><c> again</c>

00:02:25.630 --> 00:02:25.640 align:start position:0%
the code now if I look at the code again
 

00:02:25.640 --> 00:02:27.750 align:start position:0%
the code now if I look at the code again
you<00:02:25.720><c> can</c><00:02:25.840><c> see</c><00:02:26.080><c> it</c><00:02:26.239><c> changed</c><00:02:26.640><c> the</c><00:02:26.800><c> URL</c><00:02:27.400><c> and</c><00:02:27.599><c> is</c>

00:02:27.750 --> 00:02:27.760 align:start position:0%
you can see it changed the URL and is
 

00:02:27.760 --> 00:02:30.150 align:start position:0%
you can see it changed the URL and is
returning<00:02:28.319><c> the</c><00:02:28.519><c> first</c><00:02:29.000><c> five</c><00:02:29.560><c> now</c><00:02:29.920><c> what</c><00:02:30.000><c> I</c><00:02:30.040><c> want</c>

00:02:30.150 --> 00:02:30.160 align:start position:0%
returning the first five now what I want
 

00:02:30.160 --> 00:02:32.390 align:start position:0%
returning the first five now what I want
to<00:02:30.280><c> do</c><00:02:30.480><c> is</c><00:02:30.760><c> return</c><00:02:31.000><c> all</c><00:02:31.120><c> of</c><00:02:31.239><c> the</c><00:02:31.360><c> users</c><00:02:32.120><c> now</c><00:02:32.280><c> I</c>

00:02:32.390 --> 00:02:32.400 align:start position:0%
to do is return all of the users now I
 

00:02:32.400 --> 00:02:34.750 align:start position:0%
to do is return all of the users now I
need<00:02:32.680><c> another</c><00:02:33.000><c> API</c><00:02:33.440><c> call</c><00:02:33.720><c> to</c><00:02:34.040><c> return</c><00:02:34.360><c> all</c><00:02:34.599><c> of</c>

00:02:34.750 --> 00:02:34.760 align:start position:0%
need another API call to return all of
 

00:02:34.760 --> 00:02:36.830 align:start position:0%
need another API call to return all of
the<00:02:34.879><c> users</c><00:02:35.680><c> so</c><00:02:35.840><c> it</c><00:02:35.920><c> knows</c><00:02:36.200><c> that</c><00:02:36.319><c> we're</c><00:02:36.480><c> still</c>

00:02:36.830 --> 00:02:36.840 align:start position:0%
the users so it knows that we're still
 

00:02:36.840 --> 00:02:38.670 align:start position:0%
the users so it knows that we're still
looking<00:02:37.280><c> at</c><00:02:37.480><c> the</c><00:02:37.640><c> same</c><00:02:38.040><c> file</c><00:02:38.360><c> it's</c><00:02:38.480><c> going</c><00:02:38.599><c> to</c>

00:02:38.670 --> 00:02:38.680 align:start position:0%
looking at the same file it's going to
 

00:02:38.680 --> 00:02:40.630 align:start position:0%
looking at the same file it's going to
create<00:02:38.920><c> one</c><00:02:39.120><c> called</c><00:02:39.400><c> Fetch</c><00:02:39.800><c> users</c><00:02:40.440><c> I'm</c><00:02:40.519><c> not</c>

00:02:40.630 --> 00:02:40.640 align:start position:0%
create one called Fetch users I'm not
 

00:02:40.640 --> 00:02:41.710 align:start position:0%
create one called Fetch users I'm not
going<00:02:40.720><c> to</c><00:02:40.840><c> actually</c><00:02:41.040><c> look</c><00:02:41.239><c> through</c><00:02:41.440><c> all</c><00:02:41.599><c> that</c>

00:02:41.710 --> 00:02:41.720 align:start position:0%
going to actually look through all that
 

00:02:41.720 --> 00:02:43.229 align:start position:0%
going to actually look through all that
I'm<00:02:41.840><c> just</c><00:02:41.920><c> going</c><00:02:42.040><c> to</c><00:02:42.200><c> assume</c><00:02:42.480><c> that</c><00:02:42.640><c> this</c><00:02:43.120><c> this</c>

00:02:43.229 --> 00:02:43.239 align:start position:0%
I'm just going to assume that this this
 

00:02:43.239 --> 00:02:45.149 align:start position:0%
I'm just going to assume that this this
is<00:02:43.400><c> correct</c><00:02:43.959><c> so</c><00:02:44.159><c> let</c><00:02:44.280><c> me</c><00:02:44.480><c> press</c><00:02:44.720><c> enter</c><00:02:45.080><c> and</c>

00:02:45.149 --> 00:02:45.159 align:start position:0%
is correct so let me press enter and
 

00:02:45.159 --> 00:02:47.309 align:start position:0%
is correct so let me press enter and
then<00:02:45.280><c> we'll</c><00:02:45.519><c> see</c><00:02:46.000><c> if</c><00:02:46.120><c> it's</c><00:02:46.360><c> created</c><00:02:47.040><c> now</c><00:02:47.200><c> you</c>

00:02:47.309 --> 00:02:47.319 align:start position:0%
then we'll see if it's created now you
 

00:02:47.319 --> 00:02:49.229 align:start position:0%
then we'll see if it's created now you
can<00:02:47.440><c> see</c><00:02:47.680><c> that</c><00:02:47.879><c> said</c><00:02:48.280><c> file</c><00:02:48.720><c> created</c>

00:02:49.229 --> 00:02:49.239 align:start position:0%
can see that said file created
 

00:02:49.239 --> 00:02:51.110 align:start position:0%
can see that said file created
successfully<00:02:50.040><c> okay</c><00:02:50.159><c> so</c><00:02:50.319><c> what</c><00:02:50.480><c> happened</c><00:02:51.000><c> was</c>

00:02:51.110 --> 00:02:51.120 align:start position:0%
successfully okay so what happened was
 

00:02:51.120 --> 00:02:52.990 align:start position:0%
successfully okay so what happened was
it<00:02:51.239><c> actually</c><00:02:51.680><c> deleted</c><00:02:52.239><c> the</c><00:02:52.360><c> original</c><00:02:52.720><c> one</c><00:02:52.879><c> so</c>

00:02:52.990 --> 00:02:53.000 align:start position:0%
it actually deleted the original one so
 

00:02:53.000 --> 00:02:56.149 align:start position:0%
it actually deleted the original one so
I<00:02:53.120><c> asked</c><00:02:53.360><c> it</c><00:02:53.519><c> to</c><00:02:54.280><c> restore</c><00:02:55.000><c> the</c><00:02:55.200><c> original</c><00:02:55.920><c> and</c>

00:02:56.149 --> 00:02:56.159 align:start position:0%
I asked it to restore the original and
 

00:02:56.159 --> 00:02:58.190 align:start position:0%
I asked it to restore the original and
also<00:02:56.400><c> add</c><00:02:56.560><c> the</c><00:02:56.640><c> fetch</c><00:02:56.920><c> users</c><00:02:57.400><c> and</c><00:02:57.599><c> it</c><00:02:57.760><c> did</c><00:02:58.000><c> here</c>

00:02:58.190 --> 00:02:58.200 align:start position:0%
also add the fetch users and it did here
 

00:02:58.200 --> 00:03:00.190 align:start position:0%
also add the fetch users and it did here
so<00:02:58.319><c> if</c><00:02:58.400><c> we</c><00:02:58.599><c> open</c><00:02:58.840><c> up</c><00:02:58.959><c> this</c><00:02:59.080><c> main.</c><00:02:59.480><c> py</c><00:02:59.879><c> file</c><00:03:00.080><c> that</c>

00:03:00.190 --> 00:03:00.200 align:start position:0%
so if we open up this main. py file that
 

00:03:00.200 --> 00:03:02.309 align:start position:0%
so if we open up this main. py file that
you<00:03:00.319><c> see</c><00:03:01.120><c> it</c><00:03:01.319><c> did</c><00:03:01.640><c> it</c><00:03:01.800><c> we</c><00:03:01.920><c> still</c><00:03:02.080><c> have</c><00:03:02.200><c> the</c>

00:03:02.309 --> 00:03:02.319 align:start position:0%
you see it did it we still have the
 

00:03:02.319 --> 00:03:04.390 align:start position:0%
you see it did it we still have the
fetch<00:03:02.599><c> to-do</c><00:03:03.120><c> and</c><00:03:03.280><c> now</c><00:03:03.440><c> we</c><00:03:03.599><c> also</c><00:03:03.879><c> have</c><00:03:04.040><c> fetch</c>

00:03:04.390 --> 00:03:04.400 align:start position:0%
fetch to-do and now we also have fetch
 

00:03:04.400 --> 00:03:05.910 align:start position:0%
fetch to-do and now we also have fetch
users<00:03:05.040><c> well</c><00:03:05.200><c> now</c><00:03:05.319><c> let's</c><00:03:05.480><c> say</c><00:03:05.640><c> I</c><00:03:05.720><c> want</c><00:03:05.799><c> to</c>

00:03:05.910 --> 00:03:05.920 align:start position:0%
users well now let's say I want to
 

00:03:05.920 --> 00:03:07.430 align:start position:0%
users well now let's say I want to
deploy<00:03:06.239><c> this</c><00:03:06.440><c> well</c><00:03:06.680><c> guess</c><00:03:06.879><c> what</c><00:03:07.080><c> it's</c><00:03:07.280><c> not</c>

00:03:07.430 --> 00:03:07.440 align:start position:0%
deploy this well guess what it's not
 

00:03:07.440 --> 00:03:08.910 align:start position:0%
deploy this well guess what it's not
going<00:03:07.560><c> to</c><00:03:07.680><c> work</c><00:03:08.040><c> because</c><00:03:08.280><c> I</c><00:03:08.360><c> don't</c><00:03:08.519><c> have</c><00:03:08.640><c> new</c>

00:03:08.910 --> 00:03:08.920 align:start position:0%
going to work because I don't have new
 

00:03:08.920 --> 00:03:10.869 align:start position:0%
going to work because I don't have new
line<00:03:09.200><c> test</c><00:03:09.599><c> coverage</c><00:03:10.159><c> now</c><00:03:10.280><c> let's</c><00:03:10.440><c> ask</c><00:03:10.599><c> it</c><00:03:10.720><c> to</c>

00:03:10.869 --> 00:03:10.879 align:start position:0%
line test coverage now let's ask it to
 

00:03:10.879 --> 00:03:13.869 align:start position:0%
line test coverage now let's ask it to
create<00:03:11.159><c> test</c><00:03:11.799><c> for</c><00:03:12.280><c> these</c><00:03:12.480><c> new</c><00:03:12.760><c> API</c><00:03:13.159><c> calls</c><00:03:13.560><c> in</c><00:03:13.720><c> a</c>

00:03:13.869 --> 00:03:13.879 align:start position:0%
create test for these new API calls in a
 

00:03:13.879 --> 00:03:15.390 align:start position:0%
create test for these new API calls in a
different<00:03:14.159><c> file</c><00:03:14.519><c> so</c><00:03:14.640><c> we</c><00:03:14.720><c> want</c><00:03:14.840><c> to</c><00:03:14.920><c> create</c><00:03:15.159><c> test</c>

00:03:15.390 --> 00:03:15.400 align:start position:0%
different file so we want to create test
 

00:03:15.400 --> 00:03:18.390 align:start position:0%
different file so we want to create test
cases<00:03:15.840><c> for</c><00:03:16.239><c> each</c><00:03:16.599><c> of</c><00:03:16.959><c> these</c><00:03:17.319><c> API</c><00:03:17.799><c> calls</c><00:03:18.280><c> so</c>

00:03:18.390 --> 00:03:18.400 align:start position:0%
cases for each of these API calls so
 

00:03:18.400 --> 00:03:19.910 align:start position:0%
cases for each of these API calls so
it's<00:03:18.560><c> going</c><00:03:18.640><c> to</c><00:03:18.760><c> create</c><00:03:19.000><c> a</c><00:03:19.120><c> new</c><00:03:19.360><c> file</c><00:03:19.640><c> called</c>

00:03:19.910 --> 00:03:19.920 align:start position:0%
it's going to create a new file called
 

00:03:19.920 --> 00:03:22.390 align:start position:0%
it's going to create a new file called
test<00:03:20.440><c> main.py</c><00:03:21.440><c> and</c><00:03:21.599><c> then</c><00:03:21.799><c> has</c><00:03:21.920><c> all</c><00:03:22.080><c> the</c><00:03:22.239><c> code</c>

00:03:22.390 --> 00:03:22.400 align:start position:0%
test main.py and then has all the code
 

00:03:22.400 --> 00:03:24.350 align:start position:0%
test main.py and then has all the code
for<00:03:22.640><c> it</c><00:03:22.760><c> so</c><00:03:22.879><c> let's</c><00:03:23.040><c> just</c><00:03:23.200><c> hit</c><00:03:23.360><c> enter</c><00:03:23.920><c> file</c>

00:03:24.350 --> 00:03:24.360 align:start position:0%
for it so let's just hit enter file
 

00:03:24.360 --> 00:03:26.509 align:start position:0%
for it so let's just hit enter file
created<00:03:24.760><c> successfully</c><00:03:25.560><c> so</c><00:03:25.799><c> file</c><00:03:26.120><c> created</c>

00:03:26.509 --> 00:03:26.519 align:start position:0%
created successfully so file created
 

00:03:26.519 --> 00:03:28.270 align:start position:0%
created successfully so file created
successfully<00:03:27.519><c> and</c><00:03:27.640><c> it</c><00:03:27.720><c> says</c><00:03:27.879><c> that</c><00:03:28.080><c> create</c>

00:03:28.270 --> 00:03:28.280 align:start position:0%
successfully and it says that create
 

00:03:28.280 --> 00:03:29.470 align:start position:0%
successfully and it says that create
test<00:03:28.480><c> case</c><00:03:28.680><c> for</c><00:03:28.799><c> each</c><00:03:28.920><c> of</c><00:03:29.040><c> them</c><00:03:29.200><c> and</c><00:03:29.319><c> here</c><00:03:29.400><c> it</c>

00:03:29.470 --> 00:03:29.480 align:start position:0%
test case for each of them and here it
 

00:03:29.480 --> 00:03:32.390 align:start position:0%
test case for each of them and here it
is<00:03:29.799><c> testor</c><00:03:30.360><c> main.py</c><00:03:31.159><c> so</c><00:03:31.560><c> it</c><00:03:31.760><c> created</c><00:03:32.080><c> for</c><00:03:32.280><c> the</c>

00:03:32.390 --> 00:03:32.400 align:start position:0%
is testor main.py so it created for the
 

00:03:32.400 --> 00:03:35.190 align:start position:0%
is testor main.py so it created for the
fetch<00:03:32.680><c> too</c><00:03:33.200><c> and</c><00:03:33.480><c> the</c><00:03:33.720><c> fetch</c><00:03:34.280><c> users</c><00:03:35.000><c> and</c><00:03:35.120><c> this</c>

00:03:35.190 --> 00:03:35.200 align:start position:0%
fetch too and the fetch users and this
 

00:03:35.200 --> 00:03:37.149 align:start position:0%
fetch too and the fetch users and this
is<00:03:35.319><c> a</c><00:03:35.439><c> more</c><00:03:35.680><c> realistic</c><00:03:36.200><c> way</c><00:03:36.599><c> of</c><00:03:36.799><c> how</c><00:03:36.920><c> we</c><00:03:37.040><c> would</c>

00:03:37.149 --> 00:03:37.159 align:start position:0%
is a more realistic way of how we would
 

00:03:37.159 --> 00:03:39.309 align:start position:0%
is a more realistic way of how we would
want<00:03:37.319><c> an</c><00:03:37.519><c> AI</c><00:03:37.920><c> agent</c><00:03:38.400><c> act</c><00:03:38.640><c> as</c><00:03:38.720><c> a</c><00:03:38.879><c> software</c>

00:03:39.309 --> 00:03:39.319 align:start position:0%
want an AI agent act as a software
 

00:03:39.319 --> 00:03:41.190 align:start position:0%
want an AI agent act as a software
engineer<00:03:39.959><c> for</c><00:03:40.200><c> us</c><00:03:40.640><c> if</c><00:03:40.720><c> you</c><00:03:40.799><c> enjoyed</c><00:03:41.080><c> that</c>

00:03:41.190 --> 00:03:41.200 align:start position:0%
engineer for us if you enjoyed that
 

00:03:41.200 --> 00:03:42.830 align:start position:0%
engineer for us if you enjoyed that
video<00:03:41.360><c> and</c><00:03:41.439><c> you're</c><00:03:41.599><c> curious</c><00:03:41.879><c> about</c><00:03:42.080><c> autogen</c><00:03:42.720><c> I</c>

00:03:42.830 --> 00:03:42.840 align:start position:0%
video and you're curious about autogen I
 

00:03:42.840 --> 00:03:44.710 align:start position:0%
video and you're curious about autogen I
have<00:03:43.080><c> two</c><00:03:43.280><c> different</c><00:03:43.519><c> courses</c><00:03:44.000><c> right</c><00:03:44.200><c> here</c>

00:03:44.710 --> 00:03:44.720 align:start position:0%
have two different courses right here
 

00:03:44.720 --> 00:03:46.350 align:start position:0%
have two different courses right here
this<00:03:44.840><c> one</c><00:03:45.000><c> is</c><00:03:45.159><c> a</c><00:03:45.319><c> beginner</c><00:03:45.760><c> course</c><00:03:46.040><c> that</c><00:03:46.159><c> will</c>

00:03:46.350 --> 00:03:46.360 align:start position:0%
this one is a beginner course that will
 

00:03:46.360 --> 00:03:47.830 align:start position:0%
this one is a beginner course that will
introduce<00:03:46.760><c> you</c><00:03:46.879><c> to</c><00:03:47.159><c> everything</c><00:03:47.400><c> you</c><00:03:47.519><c> need</c><00:03:47.680><c> to</c>

00:03:47.830 --> 00:03:47.840 align:start position:0%
introduce you to everything you need to
 

00:03:47.840 --> 00:03:49.429 align:start position:0%
introduce you to everything you need to
know<00:03:48.120><c> about</c><00:03:48.360><c> autogen</c><00:03:49.040><c> thank</c><00:03:49.200><c> you</c><00:03:49.280><c> for</c>

00:03:49.429 --> 00:03:49.439 align:start position:0%
know about autogen thank you for
 

00:03:49.439 --> 00:03:53.000 align:start position:0%
know about autogen thank you for
watching<00:03:49.799><c> and</c><00:03:49.959><c> I'll</c><00:03:50.080><c> see</c><00:03:50.239><c> you</c><00:03:50.439><c> next</c><00:03:50.640><c> video</c>

