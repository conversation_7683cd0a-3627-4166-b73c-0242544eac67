#!/usr/bin/env python3
"""
Interactive test script for uploading files from dd_pdf,
checking caching implicitly, and performing Q&A using GenaiFileManager
and call_gemini_api.
"""

import asyncio
import logging
import os
import sys # Import sys
from pathlib import Path
from typing import List, Optional, Sequence, Union

import pydantic
import asyncpg
from dotenv import load_dotenv, find_dotenv

from google.genai import types
from google.genai.types import File
from rich.console import Console
from rich.prompt import Prompt, IntPrompt

# --- Path Setup --- Ensure the package root is in sys.path ---
# Get the directory of the current script (tests_v15)
script_dir = Path(__file__).resolve().parent
# Go up two levels to the 'python_backend' directory, which acts as the project root for imports
project_root_for_imports = script_dir.parent.parent
# Add this root directory to the start of the Python path
sys.path.insert(0, str(project_root_for_imports))

# Assume this script is run from the project root or adjust path accordingly
# Ensure the gemini_methods package is discoverable (e.g., via PYTHONPATH or project structure)
try:
    # Use absolute imports relative to the project_root_for_imports added to sys.path
    from deprecated_LangGraph_TDD_v14.gemini_methods.file_manager import GenaiFileManager
    from deprecated_LangGraph_TDD_v14.gemini_methods.genai_utils import call_gemini_api, DEFAULT_MODEL_NAME
except ImportError as e:
    print(f"Error importing gemini_methods: {e}")
    print("Ensure the script is run from a location where 'gemini_methods' is importable,")
    print("or adjust Python's path (e.g., set PYTHONPATH).")
    exit(1)

# --- Configuration ---
# Adjust this path relative to where the script is run, or use absolute path
PDF_DIRECTORY = Path("dd_pdf")
# Ensure this directory exists relative to the script execution location
# Go up 3 levels: tests_v15 -> LangGraph_TDD_v14 -> python_backend -> project_root
PROJECT_ROOT = Path(__file__).resolve().parents[3]
PDF_FULL_PATH = PROJECT_ROOT / PDF_DIRECTORY

# Load .env file explicitly for DATABASE_URL
load_dotenv(find_dotenv(filename='.env.local', raise_error_if_not_found=False) or find_dotenv(filename='.env', raise_error_if_not_found=False))
DATABASE_URL = os.getenv("DATABASE_URL")

MODEL_FOR_QA = DEFAULT_MODEL_NAME # Use the default from genai_utils

console = Console()
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Pydantic Schema for Q&A ---
class QAResponse(pydantic.BaseModel):
    answer: str

# --- Helper Function for Cleanup ---
async def cleanup_uploaded_files(file_manager: GenaiFileManager, files_to_delete: List[File]):
    """Deletes the provided list of files using the file manager."""
    if not files_to_delete:
        console.print("[yellow]No files to clean up.[/yellow]")
        return

    console.print("\n[bold cyan]Initiating cleanup of uploaded files...[/bold cyan]")
    cleanup_tasks = []
    file_names_for_deletion = []
    for file_obj in files_to_delete:
        if file_obj and file_obj.name: # Ensure we have a valid file object and ID
            file_names_for_deletion.append(file_obj.display_name or file_obj.name)
            cleanup_tasks.append(file_manager.delete_file(file_obj.name))
        else:
             console.print(f"[yellow]Warning: Skipping invalid file object during cleanup prep: {file_obj}[/yellow]")

    if not cleanup_tasks:
         console.print("[yellow]No valid file IDs found for cleanup.[/yellow]")
         return

    console.print(f"Attempting to delete {len(cleanup_tasks)} files concurrently...")
    try:
        results = await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        console.print("\nCleanup Results:")
        for i, result in enumerate(results):
            file_name = file_names_for_deletion[i]
            if isinstance(result, Exception):
                console.print(f"  {i+1}. [red]Error deleting {file_name}:[/red] {result}")
            elif result is True: # Assuming delete_file returns True on success
                console.print(f"  {i+1}. [green]Successfully deleted:[/green] {file_name}")
            else:
                 console.print(f"  {i+1}. [yellow]Deletion failed (returned non-True):[/yellow] {file_name}") # Adjust based on actual return

    except Exception as gather_err:
        console.print(f"[bold red]Error during asyncio.gather for cleanup: {gather_err}[/bold red]")

    console.print("[bold cyan]Cleanup complete.[/bold cyan]")

# --- Main Application Logic ---
async def main():
    console.print("[bold cyan]Interactive Gemini File Q&A Tester[/bold cyan]")
    console.print(f"Looking for PDF files in: [italic yellow]{PDF_FULL_PATH}[/italic yellow]")

    if not PDF_FULL_PATH.is_dir():
        console.print(f"[bold red]Error:[/bold red] Directory not found: {PDF_FULL_PATH}")
        return

    pdf_files = sorted([f for f in PDF_FULL_PATH.glob("*.pdf") if f.is_file()])

    if not pdf_files:
        console.print(f"[bold red]Error:[/bold red] No PDF files found in {PDF_FULL_PATH}")
        return

    console.print("\n[bold green]Available PDF Files:[/bold green]")
    for i, pdf_file in enumerate(pdf_files):
        console.print(f"  {i+1}: {pdf_file.name}")

    # --- File Selection ---
    selected_indices_str = Prompt.ask("\nEnter the numbers of the files to upload (comma-separated), or 'all'")
    selected_files: List[Path] = []

    if selected_indices_str.strip().lower() == 'all':
        selected_files = pdf_files
    else:
        try:
            indices = [int(i.strip()) - 1 for i in selected_indices_str.split(',') if i.strip()]
            selected_files = [pdf_files[i] for i in indices if 0 <= i < len(pdf_files)]
        except (ValueError, IndexError):
            console.print("[bold red]Error:[/bold red] Invalid selection.")
            return

    if not selected_files:
        console.print("[yellow]No files selected for upload.[/yellow]")
        return

    console.print("\n[bold blue]Selected files for upload:[/bold blue]")
    for f in selected_files:
        console.print(f"  - {f.name}")

    # --- File Upload ---
    console.print("\nInitializing File Manager...")
    file_manager = GenaiFileManager()
    uploaded_file_parts: List[types.Part] = []
    uploaded_file_names: List[str] = []
    successfully_uploaded_files: List[File] = [] # Store File objects for cleanup

    # Create tasks for all uploads
    upload_tasks = []
    for file_to_upload in selected_files:
        console.print(f"  Queueing upload for {file_to_upload.name}...")
        upload_tasks.append(file_manager.upload_file(file_path=file_to_upload))

    # Run uploads concurrently using asyncio.gather
    console.print(f"\nAttempting to upload {len(upload_tasks)} files concurrently...")
    try:
        # Results will be a list of Optional[File] or exceptions
        upload_results = await asyncio.gather(*upload_tasks, return_exceptions=True)
    except Exception as gather_err:
        console.print(f"[bold red]Error during asyncio.gather for uploads: {gather_err}[/bold red]")
        # Handle gather error if needed, maybe exit
        return

    # Process results
    console.print("\nProcessing upload results...")
    for i, result in enumerate(upload_results):
        file_to_upload = selected_files[i] # Get corresponding file path
        if isinstance(result, Exception):
            console.print(f"    [red]Error uploading {file_to_upload.name}: {result}[/red]")
        elif result is None:
            console.print(f"    [red]Failed (returned None):[/red] {file_to_upload.name}")
        elif isinstance(result, File):
            uploaded_info: File = result # Type hint for clarity
            # Check for required attributes before using them
            if uploaded_info.uri and uploaded_info.mime_type:
                console.print(f"    [green]Success:[/green] {file_to_upload.name} -> ID: {uploaded_info.name}, URI: {uploaded_info.uri}")
                # Create the Part for Q&A
                successfully_uploaded_files.append(uploaded_info) # Store for potential cleanup
                try:
                    file_part = types.Part.from_uri(file_uri=uploaded_info.uri, mime_type=uploaded_info.mime_type)
                    uploaded_file_parts.append(file_part)
                    # Safely get display name or fallback
                    display_name = uploaded_info.display_name or uploaded_info.name or "unknown_file"
                    uploaded_file_names.append(display_name)
                except Exception as part_err:
                     console.print(f"    [red]Error creating Part for {file_to_upload.name}: {part_err}[/red]")
            else:
                 console.print(f"    [yellow]Warning:[/yellow] Uploaded info for {file_to_upload.name} missing URI or MIME type. Skipping file for Q&A. Info: {uploaded_info}")
        else:
             # Should not happen if return_exceptions=True, but good practice
             console.print(f"    [red]Unknown upload result type for {file_to_upload.name}: {type(result)}[/red]")

    if not uploaded_file_parts:
        console.print("[bold red]Error:[/bold red] No files were successfully uploaded. Cannot proceed to Q&A.")
        # Optional: Add cleanup logic here if needed
        return

    console.print("\n--- File Upload Complete ---")
    console.print(f"Using model: [yellow]{MODEL_FOR_QA}[/yellow] for Q&A.")
    console.print("Files included in context:", ', '.join(uploaded_file_names))

    # Create asyncpg pool
    async with asyncpg.create_pool(DATABASE_URL) as pool:
        # --- Interactive Q&A Loop ---
        console.print("\nEnter your questions. Type [bold]'quit'[/bold] to exit, or [bold]'cleanup'[/bold] to delete uploaded files and exit.")

        while True:
            user_question = Prompt.ask("\n[bold magenta]Question[/bold magenta]")
            if user_question.lower() == 'quit':
                console.print("\n[bold cyan]Exiting without cleaning up files.[/bold cyan]")
                break
            elif user_question.lower() == 'cleanup':
                await cleanup_uploaded_files(file_manager, successfully_uploaded_files)
                break

            # Construct content for API call
            qa_content: List[Union[str, types.Part]] = []
            qa_content.extend(uploaded_file_parts) # Add all file parts
            # Append the user question as a simple string
            qa_content.append(user_question)

            console.print("\nCalling Gemini API...")
            api_result = await call_gemini_api(
                model_name=MODEL_FOR_QA,
                contents=qa_content,
                response_schema=QAResponse,
                db_pool=pool
            )

            # Unpack the tuple returned by call_gemini_api
            validated_model, input_tokens, output_tokens, cost = api_result
            
            # Check the first element (the validated Pydantic model)
            if validated_model and isinstance(validated_model, QAResponse):
                console.print(f"\n[bold green]Answer:[/bold green]\n{validated_model.answer}")
                # Optionally print token counts
                if input_tokens is not None and output_tokens is not None:
                     console.print(f"  (Tokens: Input={input_tokens}, Output={output_tokens})")
                if cost is not None:
                    console.print(f"  (Estimated Cost: ${cost:.6f})")
                else:
                    console.print("  (Cost: Not calculated)") 
            else:
                error_message = str(validated_model) if validated_model else "Unknown error"
                console.print(f"[yellow]Failed to get a valid answer: {error_message}[/yellow]")
                # Print tokens/cost if available even on failure
                if input_tokens is not None and output_tokens is not None:
                     console.print(f"  (Tokens: Input={input_tokens}, Output={output_tokens})")
                if cost is not None:
                    console.print(f"  (Estimated Cost: ${cost:.6f})")
                else:
                    console.print("  (Cost: Not calculated)")
 
        console.print("\n[bold green]Session ended.[/bold green]")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        console.print("\nExecution interrupted by user.")
