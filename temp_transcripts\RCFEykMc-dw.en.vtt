WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.730 align:start position:0%
 
hey<00:00:00.390><c> and</c><00:00:00.780><c> welcome</c><00:00:00.870><c> back</c><00:00:01.050><c> to</c><00:00:01.260><c> another</c><00:00:01.350><c> HTML</c>

00:00:01.730 --> 00:00:01.740 align:start position:0%
hey and welcome back to another HTML
 

00:00:01.740 --> 00:00:03.590 align:start position:0%
hey and welcome back to another HTML
video<00:00:01.920><c> and</c><00:00:02.280><c> this</c><00:00:02.790><c> time</c><00:00:02.970><c> we're</c><00:00:03.210><c> going</c><00:00:03.389><c> to</c>

00:00:03.590 --> 00:00:03.600 align:start position:0%
video and this time we're going to
 

00:00:03.600 --> 00:00:06.440 align:start position:0%
video and this time we're going to
actually<00:00:03.750><c> see</c><00:00:04.049><c> how</c><00:00:04.230><c> a</c><00:00:04.259><c> form</c><00:00:04.589><c> control</c><00:00:04.950><c> works</c><00:00:05.450><c> so</c>

00:00:06.440 --> 00:00:06.450 align:start position:0%
actually see how a form control works so
 

00:00:06.450 --> 00:00:08.810 align:start position:0%
actually see how a form control works so
in<00:00:07.200><c> the</c><00:00:07.290><c> last</c><00:00:07.440><c> video</c><00:00:07.740><c> we</c><00:00:08.069><c> explained</c><00:00:08.370><c> how</c><00:00:08.429><c> forms</c>

00:00:08.810 --> 00:00:08.820 align:start position:0%
in the last video we explained how forms
 

00:00:08.820 --> 00:00:11.209 align:start position:0%
in the last video we explained how forms
work<00:00:09.030><c> then</c><00:00:09.630><c> we</c><00:00:09.719><c> had</c><00:00:09.809><c> two</c><00:00:10.019><c> form</c><00:00:10.290><c> controls</c><00:00:10.650><c> we</c>

00:00:11.209 --> 00:00:11.219 align:start position:0%
work then we had two form controls we
 

00:00:11.219 --> 00:00:13.700 align:start position:0%
work then we had two form controls we
had<00:00:11.400><c> a</c><00:00:11.429><c> user</c><00:00:11.759><c> name</c><00:00:11.969><c> and</c><00:00:12.179><c> an</c><00:00:12.360><c> aye</c><00:00:12.540><c> vote</c><00:00:12.570><c> for</c><00:00:13.110><c> the</c>

00:00:13.700 --> 00:00:13.710 align:start position:0%
had a user name and an aye vote for the
 

00:00:13.710 --> 00:00:15.530 align:start position:0%
had a user name and an aye vote for the
user<00:00:13.860><c> name</c><00:00:14.009><c> was</c><00:00:14.280><c> a</c><00:00:14.309><c> text</c><00:00:14.730><c> form</c><00:00:14.969><c> control</c><00:00:15.299><c> and</c>

00:00:15.530 --> 00:00:15.540 align:start position:0%
user name was a text form control and
 

00:00:15.540 --> 00:00:18.920 align:start position:0%
user name was a text form control and
the<00:00:15.809><c> radio</c><00:00:16.379><c> button</c><00:00:16.650><c> was</c><00:00:16.859><c> a</c><00:00:17.100><c> radio</c><00:00:17.850><c> button</c><00:00:18.480><c> form</c>

00:00:18.920 --> 00:00:18.930 align:start position:0%
the radio button was a radio button form
 

00:00:18.930 --> 00:00:22.130 align:start position:0%
the radio button was a radio button form
control<00:00:19.640><c> so</c><00:00:20.640><c> let's</c><00:00:21.330><c> take</c><00:00:21.570><c> it</c><00:00:21.660><c> and</c><00:00:21.810><c> take</c><00:00:21.900><c> a</c><00:00:21.990><c> look</c>

00:00:22.130 --> 00:00:22.140 align:start position:0%
control so let's take it and take a look
 

00:00:22.140 --> 00:00:25.429 align:start position:0%
control so let's take it and take a look
at<00:00:22.199><c> this</c><00:00:22.350><c> we</c><00:00:23.310><c> have</c><00:00:23.510><c> we</c><00:00:24.510><c> start</c><00:00:24.689><c> out</c><00:00:24.840><c> with</c><00:00:24.960><c> a</c><00:00:25.019><c> form</c>

00:00:25.429 --> 00:00:25.439 align:start position:0%
at this we have we start out with a form
 

00:00:25.439 --> 00:00:27.830 align:start position:0%
at this we have we start out with a form
element<00:00:25.980><c> okay</c><00:00:26.460><c> that</c><00:00:27.000><c> takes</c><00:00:27.180><c> an</c><00:00:27.510><c> action</c>

00:00:27.830 --> 00:00:27.840 align:start position:0%
element okay that takes an action
 

00:00:27.840 --> 00:00:31.070 align:start position:0%
element okay that takes an action
attribute<00:00:28.140><c> the</c><00:00:29.010><c> action</c><00:00:29.460><c> attribute</c><00:00:29.810><c> this</c><00:00:30.810><c> is</c>

00:00:31.070 --> 00:00:31.080 align:start position:0%
attribute the action attribute this is
 

00:00:31.080 --> 00:00:32.930 align:start position:0%
attribute the action attribute this is
going<00:00:31.800><c> to</c><00:00:31.859><c> tell</c><00:00:31.980><c> us</c><00:00:32.009><c> where</c><00:00:32.610><c> we're</c><00:00:32.700><c> going</c><00:00:32.820><c> to</c><00:00:32.880><c> be</c>

00:00:32.930 --> 00:00:32.940 align:start position:0%
going to tell us where we're going to be
 

00:00:32.940 --> 00:00:34.940 align:start position:0%
going to tell us where we're going to be
redirected<00:00:33.329><c> to</c><00:00:33.690><c> when</c><00:00:34.410><c> you</c><00:00:34.500><c> hit</c><00:00:34.620><c> the</c><00:00:34.710><c> submit</c>

00:00:34.940 --> 00:00:34.950 align:start position:0%
redirected to when you hit the submit
 

00:00:34.950 --> 00:00:35.889 align:start position:0%
redirected to when you hit the submit
button

00:00:35.889 --> 00:00:35.899 align:start position:0%
button
 

00:00:35.899 --> 00:00:39.200 align:start position:0%
button
so<00:00:36.899><c> after</c><00:00:37.860><c> you</c><00:00:38.010><c> hit</c><00:00:38.160><c> the</c><00:00:38.280><c> submit</c><00:00:38.460><c> button</c><00:00:38.579><c> the</c>

00:00:39.200 --> 00:00:39.210 align:start position:0%
so after you hit the submit button the
 

00:00:39.210 --> 00:00:40.970 align:start position:0%
so after you hit the submit button the
server<00:00:39.480><c> does</c><00:00:40.050><c> something</c><00:00:40.260><c> to</c><00:00:40.320><c> information</c><00:00:40.800><c> and</c>

00:00:40.970 --> 00:00:40.980 align:start position:0%
server does something to information and
 

00:00:40.980 --> 00:00:42.160 align:start position:0%
server does something to information and
it<00:00:41.129><c> reads</c><00:00:41.309><c> directly</c><00:00:41.579><c> to</c><00:00:41.700><c> another</c><00:00:41.790><c> webpage</c>

00:00:42.160 --> 00:00:42.170 align:start position:0%
it reads directly to another webpage
 

00:00:42.170 --> 00:00:45.500 align:start position:0%
it reads directly to another webpage
this<00:00:43.170><c> is</c><00:00:43.410><c> a</c><00:00:43.469><c> web</c><00:00:43.649><c> page</c><00:00:43.890><c> whatever</c><00:00:44.820><c> is</c><00:00:44.969><c> defined</c>

00:00:45.500 --> 00:00:45.510 align:start position:0%
this is a web page whatever is defined
 

00:00:45.510 --> 00:00:47.330 align:start position:0%
this is a web page whatever is defined
here<00:00:45.780><c> is</c><00:00:45.930><c> the</c><00:00:46.079><c> web</c><00:00:46.230><c> page</c><00:00:46.260><c> it's</c><00:00:46.950><c> going</c><00:00:47.160><c> to</c><00:00:47.250><c> take</c>

00:00:47.330 --> 00:00:47.340 align:start position:0%
here is the web page it's going to take
 

00:00:47.340 --> 00:00:51.319 align:start position:0%
here is the web page it's going to take
you<00:00:47.460><c> to</c><00:00:48.469><c> we're</c><00:00:49.469><c> not</c><00:00:49.620><c> going</c><00:00:49.739><c> to</c><00:00:49.770><c> see</c><00:00:50.730><c> this</c><00:00:50.850><c> in</c><00:00:51.059><c> an</c>

00:00:51.319 --> 00:00:51.329 align:start position:0%
you to we're not going to see this in an
 

00:00:51.329 --> 00:00:53.930 align:start position:0%
you to we're not going to see this in an
example<00:00:51.449><c> because</c><00:00:51.899><c> this</c><00:00:52.469><c> is</c><00:00:52.620><c> for</c><00:00:52.800><c> HTML</c><00:00:53.219><c> not</c><00:00:53.430><c> PHP</c>

00:00:53.930 --> 00:00:53.940 align:start position:0%
example because this is for HTML not PHP
 

00:00:53.940 --> 00:00:56.209 align:start position:0%
example because this is for HTML not PHP
which<00:00:54.180><c> is</c><00:00:54.300><c> PHP</c><00:00:54.570><c> is</c><00:00:54.750><c> another</c><00:00:55.020><c> perk</c><00:00:55.890><c> which</c><00:00:56.100><c> is</c><00:00:56.190><c> a</c>

00:00:56.209 --> 00:00:56.219 align:start position:0%
which is PHP is another perk which is a
 

00:00:56.219 --> 00:00:57.470 align:start position:0%
which is PHP is another perk which is a
programming<00:00:56.610><c> language</c><00:00:56.640><c> not</c><00:00:57.180><c> a</c><00:00:57.210><c> markup</c>

00:00:57.470 --> 00:00:57.480 align:start position:0%
programming language not a markup
 

00:00:57.480 --> 00:01:00.260 align:start position:0%
programming language not a markup
language<00:00:57.809><c> and</c><00:00:57.930><c> that</c><00:00:58.500><c> can</c><00:00:58.680><c> that</c><00:00:59.129><c> invokes</c><00:00:59.820><c> logic</c>

00:01:00.260 --> 00:01:00.270 align:start position:0%
language and that can that invokes logic
 

00:01:00.270 --> 00:01:02.420 align:start position:0%
language and that can that invokes logic
so<00:01:00.539><c> it</c><00:01:01.140><c> can</c><00:01:01.320><c> take</c><00:01:01.500><c> do</c><00:01:01.710><c> something</c><00:01:02.100><c> with</c><00:01:02.340><c> the</c>

00:01:02.420 --> 00:01:02.430 align:start position:0%
so it can take do something with the
 

00:01:02.430 --> 00:01:03.860 align:start position:0%
so it can take do something with the
information<00:01:02.910><c> from</c><00:01:03.149><c> the</c><00:01:03.510><c> server</c>

00:01:03.860 --> 00:01:03.870 align:start position:0%
information from the server
 

00:01:03.870 --> 00:01:06.710 align:start position:0%
information from the server
I<00:01:04.790><c> want</c><00:01:05.790><c> to</c><00:01:05.850><c> worry</c><00:01:05.970><c> about</c><00:01:06.000><c> that</c><00:01:06.119><c> much</c><00:01:06.270><c> you</c><00:01:06.630><c> know</c>

00:01:06.710 --> 00:01:06.720 align:start position:0%
I want to worry about that much you know
 

00:01:06.720 --> 00:01:07.820 align:start position:0%
I want to worry about that much you know
worry<00:01:06.900><c> about</c><00:01:07.020><c> that</c><00:01:07.140><c> right</c><00:01:07.260><c> now</c><00:01:07.320><c> okay</c><00:01:07.409><c> just</c>

00:01:07.820 --> 00:01:07.830 align:start position:0%
worry about that right now okay just
 

00:01:07.830 --> 00:01:10.250 align:start position:0%
worry about that right now okay just
know<00:01:08.369><c> that</c><00:01:09.000><c> this</c><00:01:09.150><c> is</c><00:01:09.210><c> the</c><00:01:09.420><c> web</c><00:01:09.570><c> page</c><00:01:09.600><c> will</c><00:01:10.140><c> be</c>

00:01:10.250 --> 00:01:10.260 align:start position:0%
know that this is the web page will be
 

00:01:10.260 --> 00:01:13.039 align:start position:0%
know that this is the web page will be
read<00:01:10.530><c> about</c><00:01:10.710><c> redirected</c><00:01:11.340><c> to</c><00:01:11.840><c> after</c><00:01:12.840><c> you</c><00:01:12.930><c> the</c>

00:01:13.039 --> 00:01:13.049 align:start position:0%
read about redirected to after you the
 

00:01:13.049 --> 00:01:13.870 align:start position:0%
read about redirected to after you the
submit<00:01:13.320><c> button</c>

00:01:13.870 --> 00:01:13.880 align:start position:0%
submit button
 

00:01:13.880 --> 00:01:17.660 align:start position:0%
submit button
okay<00:01:14.880><c> so</c><00:01:15.229><c> we</c><00:01:16.229><c> have</c><00:01:16.320><c> paragraph</c><00:01:16.740><c> element</c><00:01:17.130><c> with</c><00:01:17.640><c> a</c>

00:01:17.660 --> 00:01:17.670 align:start position:0%
okay so we have paragraph element with a
 

00:01:17.670 --> 00:01:19.249 align:start position:0%
okay so we have paragraph element with a
user<00:01:17.880><c> name</c><00:01:17.970><c> which</c><00:01:18.270><c> is</c><00:01:18.390><c> really</c><00:01:18.720><c> just</c><00:01:18.900><c> a</c><00:01:18.960><c> label</c>

00:01:19.249 --> 00:01:19.259 align:start position:0%
user name which is really just a label
 

00:01:19.259 --> 00:01:21.859 align:start position:0%
user name which is really just a label
and<00:01:19.439><c> then</c><00:01:19.670><c> we</c><00:01:20.670><c> have</c><00:01:20.790><c> an</c><00:01:20.880><c> input</c><00:01:21.060><c> a</c><00:01:21.299><c> input</c>

00:01:21.859 --> 00:01:21.869 align:start position:0%
and then we have an input a input
 

00:01:21.869 --> 00:01:23.719 align:start position:0%
and then we have an input a input
element<00:01:22.320><c> this</c><00:01:22.470><c> is</c><00:01:22.590><c> important</c><00:01:23.070><c> so</c><00:01:23.460><c> it</c><00:01:23.549><c> takes</c>

00:01:23.719 --> 00:01:23.729 align:start position:0%
element this is important so it takes
 

00:01:23.729 --> 00:01:26.810 align:start position:0%
element this is important so it takes
two<00:01:23.880><c> attributes</c><00:01:24.299><c> type</c><00:01:24.810><c> and</c><00:01:25.020><c> name</c><00:01:25.200><c> type</c><00:01:25.950><c> that's</c>

00:01:26.810 --> 00:01:26.820 align:start position:0%
two attributes type and name type that's
 

00:01:26.820 --> 00:01:30.800 align:start position:0%
two attributes type and name type that's
the<00:01:27.290><c> well</c><00:01:28.290><c> the</c><00:01:28.560><c> type</c><00:01:28.740><c> of</c><00:01:29.450><c> form</c><00:01:30.450><c> control</c><00:01:30.720><c> that</c>

00:01:30.800 --> 00:01:30.810 align:start position:0%
the well the type of form control that
 

00:01:30.810 --> 00:01:31.999 align:start position:0%
the well the type of form control that
you're<00:01:30.869><c> gonna</c><00:01:31.020><c> have</c><00:01:31.170><c> in</c><00:01:31.350><c> this</c><00:01:31.439><c> case</c><00:01:31.619><c> where</c><00:01:31.890><c> me</c>

00:01:31.999 --> 00:01:32.009 align:start position:0%
you're gonna have in this case where me
 

00:01:32.009 --> 00:01:33.890 align:start position:0%
you're gonna have in this case where me
text<00:01:32.340><c> so</c><00:01:32.490><c> that</c><00:01:32.640><c> we</c><00:01:32.729><c> can</c><00:01:32.909><c> type</c><00:01:33.390><c> something</c><00:01:33.600><c> in</c>

00:01:33.890 --> 00:01:33.900 align:start position:0%
text so that we can type something in
 

00:01:33.900 --> 00:01:37.539 align:start position:0%
text so that we can type something in
and<00:01:34.170><c> then</c><00:01:34.890><c> the</c><00:01:35.009><c> name</c><00:01:35.220><c> attribute</c><00:01:35.869><c> that's</c><00:01:36.869><c> where</c>

00:01:37.539 --> 00:01:37.549 align:start position:0%
and then the name attribute that's where
 

00:01:37.549 --> 00:01:40.399 align:start position:0%
and then the name attribute that's where
we<00:01:38.549><c> have</c><00:01:38.700><c> the</c><00:01:38.850><c> name</c><00:01:39.060><c> value</c><00:01:39.299><c> pair</c><00:01:39.659><c> that</c><00:01:39.930><c> we</c><00:01:40.079><c> said</c>

00:01:40.399 --> 00:01:40.409 align:start position:0%
we have the name value pair that we said
 

00:01:40.409 --> 00:01:42.440 align:start position:0%
we have the name value pair that we said
in<00:01:40.500><c> step</c><00:01:40.770><c> two</c><00:01:41.009><c> of</c><00:01:41.310><c> how</c><00:01:41.640><c> forms</c><00:01:42.000><c> work</c><00:01:42.180><c> in</c><00:01:42.329><c> the</c>

00:01:42.440 --> 00:01:42.450 align:start position:0%
in step two of how forms work in the
 

00:01:42.450 --> 00:01:45.950 align:start position:0%
in step two of how forms work in the
last<00:01:42.600><c> video</c><00:01:43.399><c> this</c><00:01:44.399><c> is</c><00:01:45.060><c> what's</c><00:01:45.360><c> gonna</c><00:01:45.479><c> go</c><00:01:45.720><c> along</c>

00:01:45.950 --> 00:01:45.960 align:start position:0%
last video this is what's gonna go along
 

00:01:45.960 --> 00:01:49.370 align:start position:0%
last video this is what's gonna go along
with<00:01:46.290><c> whatever</c><00:01:46.649><c> we</c><00:01:47.009><c> select</c><00:01:47.700><c> or</c><00:01:47.909><c> input</c><00:01:48.600><c> in</c><00:01:48.810><c> the</c>

00:01:49.370 --> 00:01:49.380 align:start position:0%
with whatever we select or input in the
 

00:01:49.380 --> 00:01:51.620 align:start position:0%
with whatever we select or input in the
form<00:01:49.590><c> control</c><00:01:50.009><c> so</c><00:01:50.670><c> it's</c><00:01:50.880><c> gonna</c><00:01:51.030><c> be</c><00:01:51.210><c> the</c><00:01:51.360><c> name</c>

00:01:51.620 --> 00:01:51.630 align:start position:0%
form control so it's gonna be the name
 

00:01:51.630 --> 00:01:53.960 align:start position:0%
form control so it's gonna be the name
value<00:01:52.140><c> pair</c><00:01:52.740><c> so</c><00:01:53.340><c> in</c><00:01:53.430><c> this</c><00:01:53.520><c> case</c><00:01:53.700><c> it</c><00:01:53.850><c> would</c><00:01:53.909><c> be</c>

00:01:53.960 --> 00:01:53.970 align:start position:0%
value pair so in this case it would be
 

00:01:53.970 --> 00:01:55.789 align:start position:0%
value pair so in this case it would be
user<00:01:54.299><c> name</c><00:01:54.540><c> the</c><00:01:54.899><c> lowercase</c><00:01:55.470><c> and</c><00:01:55.740><c> then</c>

00:01:55.789 --> 00:01:55.799 align:start position:0%
user name the lowercase and then
 

00:01:55.799 --> 00:01:58.219 align:start position:0%
user name the lowercase and then
whatever<00:01:56.399><c> value</c><00:01:56.939><c> we</c><00:01:57.719><c> have</c><00:01:57.750><c> for</c><00:01:57.990><c> the</c><00:01:58.049><c> form</c>

00:01:58.219 --> 00:01:58.229 align:start position:0%
whatever value we have for the form
 

00:01:58.229 --> 00:02:02.630 align:start position:0%
whatever value we have for the form
control<00:01:58.820><c> okay</c><00:01:59.820><c> so</c><00:02:00.540><c> let's</c><00:02:00.689><c> start</c><00:02:00.869><c> coding</c><00:02:01.640><c> so</c>

00:02:02.630 --> 00:02:02.640 align:start position:0%
control okay so let's start coding so
 

00:02:02.640 --> 00:02:09.139 align:start position:0%
control okay so let's start coding so
first<00:02:03.119><c> we</c><00:02:03.299><c> need</c><00:02:03.409><c> is</c><00:02:04.409><c> doctype</c><00:02:06.259><c> then</c><00:02:07.259><c> we</c><00:02:07.530><c> need</c><00:02:08.149><c> an</c>

00:02:09.139 --> 00:02:09.149 align:start position:0%
first we need is doctype then we need an
 

00:02:09.149 --> 00:02:11.920 align:start position:0%
first we need is doctype then we need an
HTML<00:02:09.330><c> element</c><00:02:09.959><c> with</c><00:02:10.470><c> an</c><00:02:10.619><c> opening</c><00:02:10.979><c> and</c><00:02:11.069><c> closing</c>

00:02:11.920 --> 00:02:11.930 align:start position:0%
HTML element with an opening and closing
 

00:02:11.930 --> 00:02:14.830 align:start position:0%
HTML element with an opening and closing
and<00:02:12.920><c> I'm</c><00:02:13.400><c> going</c><00:02:13.520><c> to</c><00:02:13.550><c> change</c><00:02:13.820><c> this</c><00:02:14.090><c> so</c><00:02:14.480><c> that</c><00:02:14.510><c> I</c>

00:02:14.830 --> 00:02:14.840 align:start position:0%
and I'm going to change this so that I
 

00:02:14.840 --> 00:02:18.460 align:start position:0%
and I'm going to change this so that I
get<00:02:15.050><c> syntax</c><00:02:15.920><c> highlighting</c><00:02:16.810><c> then</c><00:02:17.810><c> we</c><00:02:17.930><c> need</c><00:02:18.110><c> a</c>

00:02:18.460 --> 00:02:18.470 align:start position:0%
get syntax highlighting then we need a
 

00:02:18.470 --> 00:02:22.390 align:start position:0%
get syntax highlighting then we need a
head<00:02:19.130><c> element</c><00:02:19.390><c> opening</c><00:02:20.390><c> closing</c><00:02:20.630><c> tag</c><00:02:21.250><c> we</c><00:02:22.250><c> need</c>

00:02:22.390 --> 00:02:22.400 align:start position:0%
head element opening closing tag we need
 

00:02:22.400 --> 00:02:24.970 align:start position:0%
head element opening closing tag we need
a<00:02:22.490><c> title</c><00:02:22.790><c> element</c><00:02:23.290><c> opening</c><00:02:24.290><c> and</c><00:02:24.380><c> closing</c><00:02:24.740><c> tag</c>

00:02:24.970 --> 00:02:24.980 align:start position:0%
a title element opening and closing tag
 

00:02:24.980 --> 00:02:31.090 align:start position:0%
a title element opening and closing tag
and<00:02:25.660><c> let's</c><00:02:26.660><c> call</c><00:02:26.870><c> this</c><00:02:28.570><c> form</c><00:02:29.570><c> them</c><00:02:30.100><c> very</c>

00:02:31.090 --> 00:02:31.100 align:start position:0%
and let's call this form them very
 

00:02:31.100 --> 00:02:33.270 align:start position:0%
and let's call this form them very
original<00:02:31.550><c> like</c><00:02:31.670><c> all</c><00:02:31.910><c> the</c><00:02:32.180><c> other</c><00:02:32.330><c> ones</c><00:02:32.540><c> I</c><00:02:32.660><c> use</c>

00:02:33.270 --> 00:02:33.280 align:start position:0%
original like all the other ones I use
 

00:02:33.280 --> 00:02:36.100 align:start position:0%
original like all the other ones I use
then<00:02:34.280><c> we</c><00:02:34.400><c> need</c><00:02:34.580><c> a</c><00:02:34.610><c> body</c><00:02:34.910><c> element</c><00:02:35.720><c> so</c><00:02:35.870><c> that</c><00:02:35.900><c> we</c>

00:02:36.100 --> 00:02:36.110 align:start position:0%
then we need a body element so that we
 

00:02:36.110 --> 00:02:38.290 align:start position:0%
then we need a body element so that we
can<00:02:36.290><c> display</c><00:02:36.500><c> something</c><00:02:37.010><c> on</c><00:02:37.400><c> this</c><00:02:37.640><c> on</c><00:02:37.880><c> to</c><00:02:38.150><c> the</c>

00:02:38.290 --> 00:02:38.300 align:start position:0%
can display something on this on to the
 

00:02:38.300 --> 00:02:43.180 align:start position:0%
can display something on this on to the
browser<00:02:40.120><c> so</c><00:02:41.120><c> let's</c><00:02:41.870><c> create</c><00:02:42.020><c> a</c><00:02:42.140><c> heading</c><00:02:42.950><c> one</c>

00:02:43.180 --> 00:02:43.190 align:start position:0%
browser so let's create a heading one
 

00:02:43.190 --> 00:02:47.890 align:start position:0%
browser so let's create a heading one
element<00:02:44.350><c> being</c><00:02:45.350><c> a</c><00:02:45.410><c> closing</c><00:02:45.800><c> tag</c><00:02:45.830><c> and</c><00:02:46.900><c> let's</c>

00:02:47.890 --> 00:02:47.900 align:start position:0%
element being a closing tag and let's
 

00:02:47.900 --> 00:02:51.100 align:start position:0%
element being a closing tag and let's
just<00:02:48.020><c> call</c><00:02:48.170><c> it</c><00:02:48.230><c> this</c><00:02:48.650><c> is</c><00:02:49.070><c> a</c><00:02:49.910><c> this</c><00:02:50.840><c> is</c><00:02:51.020><c> an</c>

00:02:51.100 --> 00:02:51.110 align:start position:0%
just call it this is a this is an
 

00:02:51.110 --> 00:02:59.160 align:start position:0%
just call it this is a this is an
example<00:02:51.470><c> of</c><00:02:52.510><c> a</c><00:02:53.510><c> text</c><00:02:53.990><c> form</c><00:02:54.620><c> control</c><00:02:56.440><c> all</c><00:02:57.440><c> right</c>

00:02:59.160 --> 00:02:59.170 align:start position:0%
example of a text form control all right
 

00:02:59.170 --> 00:03:01.300 align:start position:0%
example of a text form control all right
so<00:03:00.170><c> let's</c><00:03:00.380><c> let's</c><00:03:00.740><c> start</c><00:03:00.770><c> with</c><00:03:01.010><c> code</c><00:03:01.190><c> in</c><00:03:01.250><c> the</c>

00:03:01.300 --> 00:03:01.310 align:start position:0%
so let's let's start with code in the
 

00:03:01.310 --> 00:03:04.090 align:start position:0%
so let's let's start with code in the
actual<00:03:01.790><c> form</c><00:03:02.090><c> first</c><00:03:03.080><c> thing</c><00:03:03.230><c> we</c><00:03:03.290><c> need</c><00:03:03.500><c> is</c><00:03:03.920><c> the</c>

00:03:04.090 --> 00:03:04.100 align:start position:0%
actual form first thing we need is the
 

00:03:04.100 --> 00:03:07.690 align:start position:0%
actual form first thing we need is the
form<00:03:04.340><c> element</c><00:03:04.780><c> and</c><00:03:05.780><c> we</c><00:03:05.900><c> need</c><00:03:06.400><c> this</c><00:03:07.400><c> can't</c><00:03:07.610><c> do</c>

00:03:07.690 --> 00:03:07.700 align:start position:0%
form element and we need this can't do
 

00:03:07.700 --> 00:03:11.050 align:start position:0%
form element and we need this can't do
anything<00:03:07.880><c> right</c><00:03:08.120><c> now</c><00:03:08.300><c> but</c><00:03:09.230><c> we</c><00:03:09.980><c> just</c><00:03:10.070><c> need</c><00:03:10.340><c> to</c>

00:03:11.050 --> 00:03:11.060 align:start position:0%
anything right now but we just need to
 

00:03:11.060 --> 00:03:14.830 align:start position:0%
anything right now but we just need to
tell<00:03:11.540><c> the</c><00:03:12.260><c> format</c><00:03:12.680><c> hit</c><00:03:12.860><c> submit</c><00:03:13.600><c> that</c><00:03:14.600><c> we</c><00:03:14.690><c> need</c>

00:03:14.830 --> 00:03:14.840 align:start position:0%
tell the format hit submit that we need
 

00:03:14.840 --> 00:03:18.720 align:start position:0%
tell the format hit submit that we need
be<00:03:14.930><c> redirected</c><00:03:15.320><c> to</c><00:03:15.980><c> this</c><00:03:16.250><c> web</c><00:03:16.490><c> page</c><00:03:16.700><c> so</c><00:03:17.630><c> just</c>

00:03:18.720 --> 00:03:18.730 align:start position:0%
be redirected to this web page so just
 

00:03:18.730 --> 00:03:22.720 align:start position:0%
be redirected to this web page so just
for<00:03:19.730><c> now</c><00:03:19.850><c> it's</c><00:03:20.150><c> just</c><00:03:20.210><c> gonna</c><00:03:20.390><c> be</c><00:03:20.480><c> this</c><00:03:21.610><c> and</c><00:03:22.610><c> then</c>

00:03:22.720 --> 00:03:22.730 align:start position:0%
for now it's just gonna be this and then
 

00:03:22.730 --> 00:03:24.730 align:start position:0%
for now it's just gonna be this and then
we<00:03:22.790><c> need</c><00:03:22.910><c> a</c><00:03:22.970><c> closing</c><00:03:23.210><c> we</c><00:03:23.900><c> need</c><00:03:24.020><c> a</c><00:03:24.170><c> close</c>

00:03:24.730 --> 00:03:24.740 align:start position:0%
we need a closing we need a close
 

00:03:24.740 --> 00:03:31.050 align:start position:0%
we need a closing we need a close
closing<00:03:25.520><c> tag</c><00:03:25.700><c> for</c><00:03:25.970><c> the</c><00:03:26.210><c> element</c><00:03:27.760><c> all</c><00:03:28.760><c> right</c>

00:03:31.050 --> 00:03:31.060 align:start position:0%
 
 

00:03:31.060 --> 00:03:36.340 align:start position:0%
 
next<00:03:32.060><c> thing</c><00:03:32.180><c> is</c><00:03:32.740><c> we</c><00:03:33.740><c> need</c><00:03:35.200><c> you</c><00:03:36.200><c> can</c><00:03:36.230><c> use</c>

00:03:36.340 --> 00:03:36.350 align:start position:0%
next thing is we need you can use
 

00:03:36.350 --> 00:03:38.110 align:start position:0%
next thing is we need you can use
paragraph<00:03:36.830><c> like</c><00:03:37.130><c> it</c><00:03:37.280><c> doesn't</c><00:03:37.490><c> really</c><00:03:37.850><c> matter</c>

00:03:38.110 --> 00:03:38.120 align:start position:0%
paragraph like it doesn't really matter
 

00:03:38.120 --> 00:03:42.820 align:start position:0%
paragraph like it doesn't really matter
what<00:03:38.470><c> text</c><00:03:39.470><c> element</c><00:03:40.190><c> you</c><00:03:40.280><c> use</c><00:03:40.430><c> right</c><00:03:40.610><c> here</c><00:03:41.830><c> but</c>

00:03:42.820 --> 00:03:42.830 align:start position:0%
what text element you use right here but
 

00:03:42.830 --> 00:03:45.310 align:start position:0%
what text element you use right here but
we<00:03:42.890><c> just</c><00:03:42.950><c> need</c><00:03:43.130><c> an</c><00:03:43.250><c> opening</c><00:03:43.580><c> and</c><00:03:43.730><c> closing</c><00:03:44.320><c> I'm</c>

00:03:45.310 --> 00:03:45.320 align:start position:0%
we just need an opening and closing I'm
 

00:03:45.320 --> 00:03:46.480 align:start position:0%
we just need an opening and closing I'm
just<00:03:45.470><c> gonna</c><00:03:45.590><c> use</c><00:03:45.710><c> an</c><00:03:45.770><c> opening</c><00:03:46.070><c> and</c><00:03:46.160><c> closing</c>

00:03:46.480 --> 00:03:46.490 align:start position:0%
just gonna use an opening and closing
 

00:03:46.490 --> 00:03:49.510 align:start position:0%
just gonna use an opening and closing
paragraph<00:03:46.790><c> element</c><00:03:47.500><c> so</c><00:03:48.500><c> neither</c><00:03:49.280><c> the</c><00:03:49.400><c> the</c>

00:03:49.510 --> 00:03:49.520 align:start position:0%
paragraph element so neither the the
 

00:03:49.520 --> 00:03:54.300 align:start position:0%
paragraph element so neither the the
label<00:03:51.040><c> user</c><00:03:52.040><c> name</c><00:03:52.280><c> and</c><00:03:52.520><c> then</c><00:03:52.850><c> we're</c><00:03:52.970><c> need</c><00:03:53.150><c> an</c>

00:03:54.300 --> 00:03:54.310 align:start position:0%
label user name and then we're need an
 

00:03:54.310 --> 00:03:58.390 align:start position:0%
label user name and then we're need an
empty<00:03:55.310><c> tag</c><00:03:55.640><c> in</c><00:03:55.880><c> the</c><00:03:56.300><c> input</c><00:03:56.680><c> element</c><00:03:57.680><c> is</c><00:03:58.190><c> an</c>

00:03:58.390 --> 00:03:58.400 align:start position:0%
empty tag in the input element is an
 

00:03:58.400 --> 00:04:00.550 align:start position:0%
empty tag in the input element is an
empty<00:03:58.730><c> tag</c><00:03:58.970><c> okay</c><00:03:59.600><c> just</c><00:03:59.840><c> we</c><00:03:59.960><c> last</c><00:04:00.200><c> time</c><00:04:00.350><c> we</c><00:04:00.440><c> saw</c>

00:04:00.550 --> 00:04:00.560 align:start position:0%
empty tag okay just we last time we saw
 

00:04:00.560 --> 00:04:05.170 align:start position:0%
empty tag okay just we last time we saw
this<00:04:00.680><c> was</c><00:04:00.890><c> when</c><00:04:01.100><c> we</c><00:04:01.250><c> worked</c><00:04:01.940><c> on</c><00:04:02.030><c> images</c><00:04:04.150><c> so</c><00:04:05.150><c> we</c>

00:04:05.170 --> 00:04:05.180 align:start position:0%
this was when we worked on images so we
 

00:04:05.180 --> 00:04:08.470 align:start position:0%
this was when we worked on images so we
need<00:04:05.420><c> to</c><00:04:05.860><c> we</c><00:04:06.860><c> need</c><00:04:06.950><c> two</c><00:04:07.160><c> attributes</c><00:04:07.640><c> for</c><00:04:08.330><c> this</c>

00:04:08.470 --> 00:04:08.480 align:start position:0%
need to we need two attributes for this
 

00:04:08.480 --> 00:04:12.790 align:start position:0%
need to we need two attributes for this
empty<00:04:09.790><c> input</c><00:04:10.790><c> element</c><00:04:11.120><c> okay</c><00:04:11.500><c> for</c><00:04:12.500><c> this</c><00:04:12.590><c> input</c>

00:04:12.790 --> 00:04:12.800 align:start position:0%
empty input element okay for this input
 

00:04:12.800 --> 00:04:16.240 align:start position:0%
empty input element okay for this input
element<00:04:13.330><c> we</c><00:04:14.330><c> need</c><00:04:14.360><c> the</c><00:04:14.570><c> type</c><00:04:14.720><c> attribute</c><00:04:15.250><c> in</c>

00:04:16.240 --> 00:04:16.250 align:start position:0%
element we need the type attribute in
 

00:04:16.250 --> 00:04:18.280 align:start position:0%
element we need the type attribute in
this<00:04:16.430><c> case</c><00:04:16.640><c> we</c><00:04:16.790><c> want</c><00:04:16.970><c> we</c><00:04:17.180><c> want</c><00:04:17.210><c> a</c><00:04:17.600><c> text</c><00:04:18.080><c> form</c>

00:04:18.280 --> 00:04:18.290 align:start position:0%
this case we want we want a text form
 

00:04:18.290 --> 00:04:20.890 align:start position:0%
this case we want we want a text form
control<00:04:18.590><c> and</c><00:04:18.830><c> we</c><00:04:19.100><c> need</c><00:04:19.310><c> to</c><00:04:20.030><c> give</c><00:04:20.180><c> it</c><00:04:20.330><c> a</c><00:04:20.570><c> name</c>

00:04:20.890 --> 00:04:20.900 align:start position:0%
control and we need to give it a name
 

00:04:20.900 --> 00:04:22.600 align:start position:0%
control and we need to give it a name
and<00:04:21.260><c> we're</c><00:04:21.440><c> gonna</c><00:04:21.590><c> call</c><00:04:21.799><c> this</c><00:04:21.890><c> one</c><00:04:21.950><c> user</c><00:04:22.400><c> name</c>

00:04:22.600 --> 00:04:22.610 align:start position:0%
and we're gonna call this one user name
 

00:04:22.610 --> 00:04:25.190 align:start position:0%
and we're gonna call this one user name
this<00:04:23.479><c> is</c><00:04:23.660><c> you</c><00:04:24.020><c> can</c><00:04:24.200><c> really</c>

00:04:25.190 --> 00:04:25.200 align:start position:0%
this is you can really
 

00:04:25.200 --> 00:04:26.240 align:start position:0%
this is you can really
whatever<00:04:25.560><c> you</c><00:04:25.680><c> want</c><00:04:25.860><c> there</c><00:04:26.010><c> for</c><00:04:26.190><c> just</c>

00:04:26.240 --> 00:04:26.250 align:start position:0%
whatever you want there for just
 

00:04:26.250 --> 00:04:29.120 align:start position:0%
whatever you want there for just
simplicity<00:04:26.820><c> sake</c><00:04:27.110><c> this</c><00:04:28.110><c> is</c><00:04:28.290><c> a</c><00:04:28.320><c> username</c><00:04:28.800><c> field</c>

00:04:29.120 --> 00:04:29.130 align:start position:0%
simplicity sake this is a username field
 

00:04:29.130 --> 00:04:31.880 align:start position:0%
simplicity sake this is a username field
so<00:04:29.610><c> when</c><00:04:30.150><c> we</c><00:04:30.240><c> sent</c><00:04:30.420><c> to</c><00:04:30.540><c> a</c><00:04:30.570><c> server</c><00:04:30.810><c> I</c><00:04:31.140><c> wanted</c><00:04:31.530><c> to</c>

00:04:31.880 --> 00:04:31.890 align:start position:0%
so when we sent to a server I wanted to
 

00:04:31.890 --> 00:04:35.090 align:start position:0%
so when we sent to a server I wanted to
know<00:04:32.100><c> that</c><00:04:32.130><c> this</c><00:04:32.370><c> was</c><00:04:32.550><c> the</c><00:04:32.670><c> username</c><00:04:33.620><c> tied</c><00:04:34.620><c> to</c>

00:04:35.090 --> 00:04:35.100 align:start position:0%
know that this was the username tied to
 

00:04:35.100 --> 00:04:38.170 align:start position:0%
know that this was the username tied to
the<00:04:35.280><c> actual</c><00:04:35.730><c> value</c><00:04:36.150><c> that</c><00:04:36.390><c> will</c><00:04:36.720><c> be</c><00:04:36.750><c> inputting</c>

00:04:38.170 --> 00:04:38.180 align:start position:0%
the actual value that will be inputting
 

00:04:38.180 --> 00:04:42.830 align:start position:0%
the actual value that will be inputting
okay<00:04:39.180><c> that</c><00:04:40.110><c> was</c><00:04:40.200><c> a</c><00:04:40.260><c> lot</c><00:04:41.120><c> and</c><00:04:42.120><c> then</c><00:04:42.600><c> we're</c><00:04:42.720><c> gonna</c>

00:04:42.830 --> 00:04:42.840 align:start position:0%
okay that was a lot and then we're gonna
 

00:04:42.840 --> 00:04:45.890 align:start position:0%
okay that was a lot and then we're gonna
close<00:04:43.380><c> it</c><00:04:43.530><c> off</c><00:04:44.450><c> alright</c><00:04:45.450><c> cuz</c><00:04:45.630><c> this</c><00:04:45.720><c> is</c><00:04:45.810><c> an</c>

00:04:45.890 --> 00:04:45.900 align:start position:0%
close it off alright cuz this is an
 

00:04:45.900 --> 00:04:50.240 align:start position:0%
close it off alright cuz this is an
empty<00:04:46.110><c> tag</c><00:04:48.170><c> alright</c><00:04:49.170><c> so</c><00:04:49.320><c> next</c><00:04:49.860><c> thing</c><00:04:50.040><c> we</c><00:04:50.100><c> do</c><00:04:50.220><c> is</c>

00:04:50.240 --> 00:04:50.250 align:start position:0%
empty tag alright so next thing we do is
 

00:04:50.250 --> 00:04:51.830 align:start position:0%
empty tag alright so next thing we do is
gonna<00:04:50.520><c> say</c><00:04:50.760><c> this</c><00:04:50.970><c> I</c><00:04:51.090><c> want</c><00:04:51.270><c> to</c><00:04:51.330><c> see</c><00:04:51.630><c> what</c><00:04:51.780><c> this</c>

00:04:51.830 --> 00:04:51.840 align:start position:0%
gonna say this I want to see what this
 

00:04:51.840 --> 00:04:52.220 align:start position:0%
gonna say this I want to see what this
looks<00:04:52.020><c> like</c>

00:04:52.220 --> 00:04:52.230 align:start position:0%
looks like
 

00:04:52.230 --> 00:04:56.390 align:start position:0%
looks like
hopefully<00:04:52.590><c> you</c><00:04:52.710><c> works</c><00:04:54.050><c> so</c><00:04:55.050><c> just</c><00:04:55.710><c> name</c><00:04:56.280><c> it</c>

00:04:56.390 --> 00:04:56.400 align:start position:0%
hopefully you works so just name it
 

00:04:56.400 --> 00:04:58.360 align:start position:0%
hopefully you works so just name it
whatever<00:04:56.550><c> you</c><00:04:56.730><c> want</c><00:04:56.820><c> perform</c><00:04:57.270><c> demo</c><00:04:57.600><c> dot</c><00:04:57.780><c> HTML</c>

00:04:58.360 --> 00:04:58.370 align:start position:0%
whatever you want perform demo dot HTML
 

00:04:58.370 --> 00:05:04.310 align:start position:0%
whatever you want perform demo dot HTML
save<00:04:59.370><c> it</c><00:04:59.550><c> and</c><00:05:00.170><c> then</c><00:05:01.880><c> you</c><00:05:02.880><c> should</c><00:05:02.910><c> get</c><00:05:03.230><c> to</c><00:05:04.230><c> get</c>

00:05:04.310 --> 00:05:04.320 align:start position:0%
save it and then you should get to get
 

00:05:04.320 --> 00:05:06.110 align:start position:0%
save it and then you should get to get
another<00:05:04.440><c> file</c><00:05:04.740><c> it</c><00:05:05.070><c> might</c><00:05:05.400><c> not</c><00:05:05.580><c> you</c><00:05:05.790><c> might</c><00:05:05.820><c> have</c>

00:05:06.110 --> 00:05:06.120 align:start position:0%
another file it might not you might have
 

00:05:06.120 --> 00:05:12.050 align:start position:0%
another file it might not you might have
have<00:05:06.540><c> a</c><00:05:07.190><c> the</c><00:05:08.190><c> chrome</c><00:05:09.590><c> icon</c><00:05:10.590><c> but</c><00:05:11.370><c> you</c><00:05:11.910><c> have</c>

00:05:12.050 --> 00:05:12.060 align:start position:0%
have a the chrome icon but you have
 

00:05:12.060 --> 00:05:13.310 align:start position:0%
have a the chrome icon but you have
something<00:05:12.390><c> if</c><00:05:12.480><c> you</c><00:05:12.540><c> hover</c><00:05:12.660><c> over</c><00:05:12.780><c> it</c><00:05:12.960><c> it'll</c><00:05:13.170><c> say</c>

00:05:13.310 --> 00:05:13.320 align:start position:0%
something if you hover over it it'll say
 

00:05:13.320 --> 00:05:17.480 align:start position:0%
something if you hover over it it'll say
HTML<00:05:13.770><c> document</c><00:05:14.220><c> okay</c><00:05:14.690><c> so</c><00:05:15.690><c> let's</c><00:05:15.840><c> open</c><00:05:15.960><c> it</c><00:05:16.490><c> and</c>

00:05:17.480 --> 00:05:17.490 align:start position:0%
HTML document okay so let's open it and
 

00:05:17.490 --> 00:05:26.300 align:start position:0%
HTML document okay so let's open it and
here<00:05:17.670><c> it</c><00:05:17.760><c> is</c><00:05:22.820><c> so</c><00:05:24.050><c> we</c><00:05:25.050><c> have</c><00:05:25.230><c> our</c><00:05:25.560><c> heading</c><00:05:25.980><c> 1</c><00:05:26.100><c> tag</c>

00:05:26.300 --> 00:05:26.310 align:start position:0%
here it is so we have our heading 1 tag
 

00:05:26.310 --> 00:05:31.280 align:start position:0%
here it is so we have our heading 1 tag
and<00:05:27.710><c> bold</c><00:05:28.940><c> bigger</c><00:05:29.940><c> font</c><00:05:30.390><c> size</c><00:05:30.600><c> and</c><00:05:30.930><c> then</c><00:05:31.140><c> we</c>

00:05:31.280 --> 00:05:31.290 align:start position:0%
and bold bigger font size and then we
 

00:05:31.290 --> 00:05:33.950 align:start position:0%
and bold bigger font size and then we
have<00:05:31.410><c> the</c><00:05:31.560><c> username</c><00:05:31.800><c> label</c><00:05:32.430><c> and</c><00:05:32.640><c> then</c><00:05:33.510><c> we</c><00:05:33.870><c> have</c>

00:05:33.950 --> 00:05:33.960 align:start position:0%
have the username label and then we have
 

00:05:33.960 --> 00:05:36.320 align:start position:0%
have the username label and then we have
our<00:05:34.080><c> text</c><00:05:34.230><c> field</c><00:05:34.440><c> so</c><00:05:35.040><c> because</c><00:05:35.520><c> we</c><00:05:35.790><c> told</c><00:05:36.030><c> we</c>

00:05:36.320 --> 00:05:36.330 align:start position:0%
our text field so because we told we
 

00:05:36.330 --> 00:05:38.510 align:start position:0%
our text field so because we told we
said<00:05:36.540><c> that</c><00:05:36.660><c> this</c><00:05:36.720><c> is</c><00:05:36.780><c> gonna</c><00:05:36.930><c> be</c><00:05:37.020><c> a</c><00:05:37.110><c> text</c><00:05:37.650><c> form</c>

00:05:38.510 --> 00:05:38.520 align:start position:0%
said that this is gonna be a text form
 

00:05:38.520 --> 00:05:41.720 align:start position:0%
said that this is gonna be a text form
control<00:05:38.880><c> so</c><00:05:39.810><c> you</c><00:05:39.840><c> just</c><00:05:40.290><c> put</c><00:05:40.560><c> in</c><00:05:40.740><c> whatever</c><00:05:41.460><c> you</c>

00:05:41.720 --> 00:05:41.730 align:start position:0%
control so you just put in whatever you
 

00:05:41.730 --> 00:05:44.950 align:start position:0%
control so you just put in whatever you
wanted<00:05:41.970><c> I'm</c><00:05:42.870><c> gonna</c><00:05:43.140><c> put</c><00:05:43.260><c> it</c><00:05:43.320><c> in</c><00:05:43.350><c> Tyler</c><00:05:43.710><c> and</c>

00:05:44.950 --> 00:05:44.960 align:start position:0%
wanted I'm gonna put it in Tyler and
 

00:05:44.960 --> 00:05:47.120 align:start position:0%
wanted I'm gonna put it in Tyler and
typically<00:05:45.960><c> what</c><00:05:46.140><c> hit</c><00:05:46.260><c> submit</c><00:05:46.650><c> and</c><00:05:46.860><c> then</c><00:05:47.040><c> it</c>

00:05:47.120 --> 00:05:47.130 align:start position:0%
typically what hit submit and then it
 

00:05:47.130 --> 00:05:49.400 align:start position:0%
typically what hit submit and then it
will<00:05:47.220><c> redirect</c><00:05:47.460><c> you</c><00:05:47.820><c> to</c><00:05:47.850><c> the</c><00:05:48.090><c> page</c><00:05:48.480><c> that</c><00:05:48.930><c> we</c>

00:05:49.400 --> 00:05:49.410 align:start position:0%
will redirect you to the page that we
 

00:05:49.410 --> 00:05:52.340 align:start position:0%
will redirect you to the page that we
denoted<00:05:49.860><c> in</c><00:05:50.190><c> the</c><00:05:50.490><c> action</c><00:05:51.090><c> attribute</c><00:05:51.240><c> of</c><00:05:51.750><c> the</c>

00:05:52.340 --> 00:05:52.350 align:start position:0%
denoted in the action attribute of the
 

00:05:52.350 --> 00:05:56.800 align:start position:0%
denoted in the action attribute of the
form<00:05:52.680><c> element</c><00:05:53.670><c> okay</c><00:05:54.360><c> and</c><00:05:55.280><c> that's</c><00:05:56.280><c> how</c><00:05:56.640><c> you</c>

00:05:56.800 --> 00:05:56.810 align:start position:0%
form element okay and that's how you
 

00:05:56.810 --> 00:06:00.110 align:start position:0%
form element okay and that's how you
create<00:05:57.810><c> a</c><00:05:58.170><c> simple</c><00:05:58.500><c> form</c><00:05:58.680><c> control</c><00:05:59.190><c> okay</c><00:05:59.550><c> let's</c>

00:06:00.110 --> 00:06:00.120 align:start position:0%
create a simple form control okay let's
 

00:06:00.120 --> 00:06:02.210 align:start position:0%
create a simple form control okay let's
do<00:06:00.210><c> a</c><00:06:00.270><c> quick</c><00:06:00.480><c> recap</c><00:06:00.740><c> we</c><00:06:01.740><c> were</c><00:06:01.860><c> introduced</c><00:06:02.190><c> to</c>

00:06:02.210 --> 00:06:02.220 align:start position:0%
do a quick recap we were introduced to
 

00:06:02.220 --> 00:06:04.250 align:start position:0%
do a quick recap we were introduced to
the<00:06:02.400><c> form</c><00:06:02.640><c> element</c><00:06:03.000><c> this</c><00:06:03.600><c> is</c><00:06:03.660><c> what</c><00:06:03.930><c> you</c><00:06:04.050><c> need</c>

00:06:04.250 --> 00:06:04.260 align:start position:0%
the form element this is what you need
 

00:06:04.260 --> 00:06:08.900 align:start position:0%
the form element this is what you need
to<00:06:05.090><c> go</c><00:06:06.090><c> to</c><00:06:06.150><c> have</c><00:06:07.020><c> forms</c><00:06:07.710><c> on</c><00:06:07.920><c> your</c><00:06:07.950><c> webpage</c><00:06:08.160><c> the</c>

00:06:08.900 --> 00:06:08.910 align:start position:0%
to go to have forms on your webpage the
 

00:06:08.910 --> 00:06:11.200 align:start position:0%
to go to have forms on your webpage the
action<00:06:09.300><c> object</c><00:06:09.900><c> the</c><00:06:10.140><c> action</c><00:06:10.470><c> attribute</c><00:06:10.650><c> is</c>

00:06:11.200 --> 00:06:11.210 align:start position:0%
action object the action attribute is
 

00:06:11.210 --> 00:06:14.060 align:start position:0%
action object the action attribute is
what<00:06:12.210><c> you</c><00:06:12.390><c> tell</c><00:06:12.840><c> the</c><00:06:12.990><c> form</c><00:06:13.290><c> where</c><00:06:13.740><c> you</c><00:06:13.860><c> will</c><00:06:13.980><c> be</c>

00:06:14.060 --> 00:06:14.070 align:start position:0%
what you tell the form where you will be
 

00:06:14.070 --> 00:06:16.400 align:start position:0%
what you tell the form where you will be
redirected<00:06:14.730><c> to</c><00:06:15.030><c> after</c><00:06:15.750><c> this</c><00:06:15.960><c> form</c><00:06:16.260><c> is</c>

00:06:16.400 --> 00:06:16.410 align:start position:0%
redirected to after this form is
 

00:06:16.410 --> 00:06:18.980 align:start position:0%
redirected to after this form is
submitted<00:06:16.610><c> the</c><00:06:17.610><c> input</c><00:06:18.000><c> element</c><00:06:18.150><c> is</c><00:06:18.480><c> used</c><00:06:18.870><c> to</c>

00:06:18.980 --> 00:06:18.990 align:start position:0%
submitted the input element is used to
 

00:06:18.990 --> 00:06:20.510 align:start position:0%
submitted the input element is used to
create<00:06:19.230><c> the</c><00:06:19.470><c> form</c><00:06:19.680><c> control</c><00:06:20.040><c> which</c><00:06:20.340><c> is</c><00:06:20.370><c> the</c>

00:06:20.510 --> 00:06:20.520 align:start position:0%
create the form control which is the
 

00:06:20.520 --> 00:06:23.000 align:start position:0%
create the form control which is the
same<00:06:20.820><c> as</c><00:06:21.030><c> a</c><00:06:21.060><c> field</c><00:06:21.750><c> that</c><00:06:22.470><c> you'll</c><00:06:22.620><c> see</c><00:06:22.830><c> on</c><00:06:22.920><c> the</c>

00:06:23.000 --> 00:06:23.010 align:start position:0%
same as a field that you'll see on the
 

00:06:23.010 --> 00:06:25.120 align:start position:0%
same as a field that you'll see on the
web<00:06:23.160><c> page</c><00:06:23.340><c> in</c><00:06:23.490><c> this</c><00:06:23.580><c> case</c><00:06:23.820><c> we</c><00:06:24.420><c> used</c><00:06:24.660><c> user</c><00:06:24.840><c> name</c>

00:06:25.120 --> 00:06:25.130 align:start position:0%
web page in this case we used user name
 

00:06:25.130 --> 00:06:27.920 align:start position:0%
web page in this case we used user name
the<00:06:26.130><c> type</c><00:06:26.340><c> attribute</c><00:06:26.850><c> on</c><00:06:27.030><c> the</c><00:06:27.060><c> input</c><00:06:27.480><c> element</c>

00:06:27.920 --> 00:06:27.930 align:start position:0%
the type attribute on the input element
 

00:06:27.930 --> 00:06:31.340 align:start position:0%
the type attribute on the input element
is<00:06:28.460><c> what</c><00:06:29.460><c> kind</c><00:06:29.790><c> of</c><00:06:29.910><c> form</c><00:06:30.120><c> control</c><00:06:30.390><c> it's</c><00:06:31.110><c> gonna</c>

00:06:31.340 --> 00:06:31.350 align:start position:0%
is what kind of form control it's gonna
 

00:06:31.350 --> 00:06:36.440 align:start position:0%
is what kind of form control it's gonna
be<00:06:31.500><c> like</c><00:06:32.250><c> a</c><00:06:32.550><c> check</c><00:06:33.090><c> box</c><00:06:33.270><c> ready</c><00:06:34.020><c> button</c><00:06:34.350><c> text</c><00:06:35.450><c> be</c>

00:06:36.440 --> 00:06:36.450 align:start position:0%
be like a check box ready button text be
 

00:06:36.450 --> 00:06:37.820 align:start position:0%
be like a check box ready button text be
password<00:06:36.720><c> there's</c><00:06:37.380><c> there's</c><00:06:37.680><c> several</c>

00:06:37.820 --> 00:06:37.830 align:start position:0%
password there's there's several
 

00:06:37.830 --> 00:06:38.600 align:start position:0%
password there's there's several
different<00:06:37.920><c> there's</c>

00:06:38.600 --> 00:06:38.610 align:start position:0%
different there's
 

00:06:38.610 --> 00:06:40.399 align:start position:0%
different there's
several<00:06:38.789><c> different</c><00:06:39.000><c> kinds</c><00:06:39.270><c> then</c><00:06:40.110><c> the</c><00:06:40.229><c> name</c>

00:06:40.399 --> 00:06:40.409 align:start position:0%
several different kinds then the name
 

00:06:40.409 --> 00:06:42.529 align:start position:0%
several different kinds then the name
attribute<00:06:40.590><c> is</c><00:06:41.009><c> what</c><00:06:41.189><c> identifies</c><00:06:41.430><c> that</c><00:06:42.270><c> form</c>

00:06:42.529 --> 00:06:42.539 align:start position:0%
attribute is what identifies that form
 

00:06:42.539 --> 00:06:45.080 align:start position:0%
attribute is what identifies that form
control<00:06:42.930><c> with</c><00:06:43.530><c> the</c><00:06:43.949><c> value</c><00:06:44.340><c> that's</c><00:06:44.460><c> selected</c>

00:06:45.080 --> 00:06:45.090 align:start position:0%
control with the value that's selected
 

00:06:45.090 --> 00:06:50.689 align:start position:0%
control with the value that's selected
or<00:06:45.710><c> typed</c><00:06:46.710><c> in</c><00:06:47.599><c> alright</c><00:06:48.599><c> so</c><00:06:49.490><c> thank</c><00:06:50.490><c> you</c><00:06:50.550><c> for</c>

00:06:50.689 --> 00:06:50.699 align:start position:0%
or typed in alright so thank you for
 

00:06:50.699 --> 00:06:54.050 align:start position:0%
or typed in alright so thank you for
watching<00:06:51.030><c> and</c><00:06:51.240><c> I'll</c><00:06:51.629><c> see</c><00:06:51.659><c> you</c><00:06:51.810><c> next</c><00:06:51.990><c> time</c>

