WEBVTT
Kind: captions
Language: en

00:00:05.279 --> 00:00:06.309 align:start position:0%
 
hi<00:00:05.520><c> everyone</c>

00:00:06.309 --> 00:00:06.319 align:start position:0%
hi everyone
 

00:00:06.319 --> 00:00:08.230 align:start position:0%
hi everyone
welcome<00:00:06.640><c> to</c><00:00:06.799><c> the</c><00:00:06.960><c> words</c><00:00:07.359><c> text</c><00:00:07.680><c> analysis</c>

00:00:08.230 --> 00:00:08.240 align:start position:0%
welcome to the words text analysis
 

00:00:08.240 --> 00:00:09.350 align:start position:0%
welcome to the words text analysis
lightning<00:00:08.639><c> talk</c>

00:00:09.350 --> 00:00:09.360 align:start position:0%
lightning talk
 

00:00:09.360 --> 00:00:11.190 align:start position:0%
lightning talk
my<00:00:09.519><c> name</c><00:00:09.679><c> is</c><00:00:09.840><c> mcgraw</c><00:00:10.480><c> and</c><00:00:10.639><c> i'm</c><00:00:10.719><c> a</c><00:00:10.880><c> product</c>

00:00:11.190 --> 00:00:11.200 align:start position:0%
my name is mcgraw and i'm a product
 

00:00:11.200 --> 00:00:12.789 align:start position:0%
my name is mcgraw and i'm a product
manager<00:00:11.679><c> in</c><00:00:11.759><c> the</c><00:00:11.920><c> advanced</c><00:00:12.320><c> initiatives</c>

00:00:12.789 --> 00:00:12.799 align:start position:0%
manager in the advanced initiatives
 

00:00:12.799 --> 00:00:15.110 align:start position:0%
manager in the advanced initiatives
group<00:00:13.120><c> at</c><00:00:13.200><c> wharton</c><00:00:13.599><c> research</c><00:00:14.080><c> data</c><00:00:14.320><c> services</c>

00:00:15.110 --> 00:00:15.120 align:start position:0%
group at wharton research data services
 

00:00:15.120 --> 00:00:18.390 align:start position:0%
group at wharton research data services
or<00:00:15.599><c> as</c><00:00:15.759><c> we</c><00:00:15.839><c> call</c><00:00:16.080><c> ourselves</c><00:00:16.800><c> words</c>

00:00:18.390 --> 00:00:18.400 align:start position:0%
or as we call ourselves words
 

00:00:18.400 --> 00:00:20.470 align:start position:0%
or as we call ourselves words
a<00:00:18.480><c> little</c><00:00:18.720><c> background</c><00:00:19.199><c> on</c><00:00:19.359><c> words</c><00:00:20.000><c> we</c><00:00:20.240><c> are</c><00:00:20.320><c> a</c>

00:00:20.470 --> 00:00:20.480 align:start position:0%
a little background on words we are a
 

00:00:20.480 --> 00:00:21.670 align:start position:0%
a little background on words we are a
business<00:00:21.199><c> data</c>

00:00:21.670 --> 00:00:21.680 align:start position:0%
business data
 

00:00:21.680 --> 00:00:23.349 align:start position:0%
business data
and<00:00:21.840><c> research</c><00:00:22.320><c> platform</c><00:00:22.800><c> that</c><00:00:22.960><c> has</c><00:00:23.039><c> been</c><00:00:23.199><c> in</c>

00:00:23.349 --> 00:00:23.359 align:start position:0%
and research platform that has been in
 

00:00:23.359 --> 00:00:25.750 align:start position:0%
and research platform that has been in
existence<00:00:23.840><c> now</c><00:00:24.080><c> for</c><00:00:24.240><c> close</c><00:00:24.480><c> to</c><00:00:24.640><c> 30</c><00:00:24.960><c> years</c>

00:00:25.750 --> 00:00:25.760 align:start position:0%
existence now for close to 30 years
 

00:00:25.760 --> 00:00:27.910 align:start position:0%
existence now for close to 30 years
we<00:00:25.920><c> have</c><00:00:26.160><c> 500</c><00:00:26.640><c> plus</c><00:00:26.880><c> subscribers</c><00:00:27.599><c> in</c><00:00:27.680><c> more</c>

00:00:27.910 --> 00:00:27.920 align:start position:0%
we have 500 plus subscribers in more
 

00:00:27.920 --> 00:00:30.630 align:start position:0%
we have 500 plus subscribers in more
than<00:00:28.160><c> 35</c><00:00:28.840><c> countries</c><00:00:29.760><c> to</c><00:00:29.920><c> support</c><00:00:30.320><c> the</c><00:00:30.400><c> needs</c>

00:00:30.630 --> 00:00:30.640 align:start position:0%
than 35 countries to support the needs
 

00:00:30.640 --> 00:00:31.830 align:start position:0%
than 35 countries to support the needs
of<00:00:30.800><c> our</c><00:00:30.960><c> 75</c>

00:00:31.830 --> 00:00:31.840 align:start position:0%
of our 75
 

00:00:31.840 --> 00:00:34.790 align:start position:0%
of our 75
000<00:00:32.239><c> individual</c><00:00:32.719><c> users</c><00:00:33.600><c> we</c><00:00:33.840><c> strive</c><00:00:34.239><c> to</c><00:00:34.399><c> create</c>

00:00:34.790 --> 00:00:34.800 align:start position:0%
000 individual users we strive to create
 

00:00:34.800 --> 00:00:36.870 align:start position:0%
000 individual users we strive to create
added<00:00:35.120><c> value</c><00:00:35.520><c> by</c><00:00:35.680><c> developing</c><00:00:36.239><c> compelling</c>

00:00:36.870 --> 00:00:36.880 align:start position:0%
added value by developing compelling
 

00:00:36.880 --> 00:00:39.270 align:start position:0%
added value by developing compelling
research<00:00:37.360><c> and</c><00:00:37.520><c> analytics</c><00:00:38.079><c> applications</c>

00:00:39.270 --> 00:00:39.280 align:start position:0%
research and analytics applications
 

00:00:39.280 --> 00:00:41.030 align:start position:0%
research and analytics applications
i'm<00:00:39.520><c> lucky</c><00:00:39.920><c> to</c><00:00:40.079><c> be</c><00:00:40.239><c> part</c><00:00:40.480><c> of</c><00:00:40.640><c> one</c><00:00:40.800><c> of</c><00:00:40.879><c> these</c>

00:00:41.030 --> 00:00:41.040 align:start position:0%
i'm lucky to be part of one of these
 

00:00:41.040 --> 00:00:42.630 align:start position:0%
i'm lucky to be part of one of these
initiatives<00:00:41.760><c> which</c><00:00:41.920><c> we</c><00:00:42.160><c> call</c>

00:00:42.630 --> 00:00:42.640 align:start position:0%
initiatives which we call
 

00:00:42.640 --> 00:00:45.990 align:start position:0%
initiatives which we call
words<00:00:43.040><c> text</c><00:00:43.360><c> analysis</c>

00:00:45.990 --> 00:00:46.000 align:start position:0%
 
 

00:00:46.000 --> 00:00:48.229 align:start position:0%
 
words<00:00:46.320><c> text</c><00:00:46.640><c> analysis</c><00:00:47.200><c> provides</c><00:00:47.600><c> a</c><00:00:47.760><c> way</c><00:00:48.000><c> for</c>

00:00:48.229 --> 00:00:48.239 align:start position:0%
words text analysis provides a way for
 

00:00:48.239 --> 00:00:50.310 align:start position:0%
words text analysis provides a way for
researchers<00:00:48.960><c> and</c><00:00:49.200><c> students</c><00:00:49.520><c> to</c><00:00:49.680><c> do</c><00:00:49.920><c> natural</c>

00:00:50.310 --> 00:00:50.320 align:start position:0%
researchers and students to do natural
 

00:00:50.320 --> 00:00:51.590 align:start position:0%
researchers and students to do natural
language<00:00:50.800><c> processing</c>

00:00:51.590 --> 00:00:51.600 align:start position:0%
language processing
 

00:00:51.600 --> 00:00:54.709 align:start position:0%
language processing
or<00:00:51.920><c> nlp</c><00:00:53.039><c> on</c><00:00:53.199><c> a</c><00:00:53.280><c> variety</c><00:00:53.760><c> of</c><00:00:53.920><c> different</c><00:00:54.320><c> text</c>

00:00:54.709 --> 00:00:54.719 align:start position:0%
or nlp on a variety of different text
 

00:00:54.719 --> 00:00:58.389 align:start position:0%
or nlp on a variety of different text
sources<00:00:56.320><c> these</c><00:00:56.640><c> nlp</c><00:00:57.120><c> pipelines</c><00:00:57.680><c> include</c>

00:00:58.389 --> 00:00:58.399 align:start position:0%
sources these nlp pipelines include
 

00:00:58.399 --> 00:01:00.549 align:start position:0%
sources these nlp pipelines include
named<00:00:58.719><c> entity</c><00:00:59.039><c> recognition</c><00:01:00.160><c> sentiment</c>

00:01:00.549 --> 00:01:00.559 align:start position:0%
named entity recognition sentiment
 

00:01:00.559 --> 00:01:01.510 align:start position:0%
named entity recognition sentiment
analysis

00:01:01.510 --> 00:01:01.520 align:start position:0%
analysis
 

00:01:01.520 --> 00:01:04.390 align:start position:0%
analysis
and<00:01:01.680><c> topic</c><00:01:02.000><c> modeling</c><00:01:02.480><c> among</c><00:01:02.800><c> many</c><00:01:03.120><c> others</c><00:01:04.159><c> in</c>

00:01:04.390 --> 00:01:04.400 align:start position:0%
and topic modeling among many others in
 

00:01:04.400 --> 00:01:05.830 align:start position:0%
and topic modeling among many others in
addition<00:01:04.720><c> to</c><00:01:04.799><c> these</c><00:01:05.040><c> pre-built</c>

00:01:05.830 --> 00:01:05.840 align:start position:0%
addition to these pre-built
 

00:01:05.840 --> 00:01:08.630 align:start position:0%
addition to these pre-built
nlp<00:01:06.320><c> modules</c><00:01:07.119><c> the</c><00:01:07.360><c> platform</c><00:01:07.920><c> also</c><00:01:08.159><c> enables</c>

00:01:08.630 --> 00:01:08.640 align:start position:0%
nlp modules the platform also enables
 

00:01:08.640 --> 00:01:10.149 align:start position:0%
nlp modules the platform also enables
users<00:01:09.040><c> to</c><00:01:09.200><c> pre-process</c>

00:01:10.149 --> 00:01:10.159 align:start position:0%
users to pre-process
 

00:01:10.159 --> 00:01:12.230 align:start position:0%
users to pre-process
and<00:01:10.320><c> prepare</c><00:01:10.799><c> text</c><00:01:11.200><c> for</c><00:01:11.360><c> use</c><00:01:11.680><c> in</c><00:01:11.840><c> their</c><00:01:12.080><c> own</c>

00:01:12.230 --> 00:01:12.240 align:start position:0%
and prepare text for use in their own
 

00:01:12.240 --> 00:01:13.910 align:start position:0%
and prepare text for use in their own
development<00:01:12.799><c> environments</c><00:01:13.360><c> such</c><00:01:13.600><c> as</c>

00:01:13.910 --> 00:01:13.920 align:start position:0%
development environments such as
 

00:01:13.920 --> 00:01:17.190 align:start position:0%
development environments such as
r<00:01:14.400><c> and</c><00:01:14.640><c> jupiter</c><00:01:15.040><c> notebooks</c><00:01:16.479><c> words</c><00:01:16.880><c> also</c>

00:01:17.190 --> 00:01:17.200 align:start position:0%
r and jupiter notebooks words also
 

00:01:17.200 --> 00:01:17.670 align:start position:0%
r and jupiter notebooks words also
provide

00:01:17.670 --> 00:01:17.680 align:start position:0%
provide
 

00:01:17.680 --> 00:01:19.990 align:start position:0%
provide
several<00:01:18.080><c> different</c><00:01:18.479><c> text</c><00:01:18.880><c> sources</c><00:01:19.600><c> as</c><00:01:19.759><c> well</c>

00:01:19.990 --> 00:01:20.000 align:start position:0%
several different text sources as well
 

00:01:20.000 --> 00:01:22.070 align:start position:0%
several different text sources as well
as<00:01:20.159><c> the</c><00:01:20.240><c> ability</c><00:01:20.720><c> for</c><00:01:20.880><c> users</c><00:01:21.280><c> to</c><00:01:21.520><c> upload</c><00:01:21.840><c> their</c>

00:01:22.070 --> 00:01:22.080 align:start position:0%
as the ability for users to upload their
 

00:01:22.080 --> 00:01:23.350 align:start position:0%
as the ability for users to upload their
own<00:01:22.320><c> corpus</c>

00:01:23.350 --> 00:01:23.360 align:start position:0%
own corpus
 

00:01:23.360 --> 00:01:25.510 align:start position:0%
own corpus
in<00:01:23.520><c> this</c><00:01:23.759><c> talk</c><00:01:24.080><c> i'll</c><00:01:24.400><c> be</c><00:01:24.560><c> focusing</c><00:01:25.040><c> on</c><00:01:25.200><c> a</c><00:01:25.280><c> news</c>

00:01:25.510 --> 00:01:25.520 align:start position:0%
in this talk i'll be focusing on a news
 

00:01:25.520 --> 00:01:27.190 align:start position:0%
in this talk i'll be focusing on a news
corpus<00:01:26.000><c> from</c><00:01:26.240><c> our</c><00:01:26.320><c> data</c><00:01:26.640><c> partner</c>

00:01:27.190 --> 00:01:27.200 align:start position:0%
corpus from our data partner
 

00:01:27.200 --> 00:01:33.350 align:start position:0%
corpus from our data partner
lexisnexis

00:01:33.350 --> 00:01:33.360 align:start position:0%
 
 

00:01:33.360 --> 00:01:35.510 align:start position:0%
 
text<00:01:33.680><c> analysis</c><00:01:34.240><c> on</c><00:01:34.479><c> news</c><00:01:34.880><c> is</c><00:01:35.040><c> a</c><00:01:35.200><c> more</c>

00:01:35.510 --> 00:01:35.520 align:start position:0%
text analysis on news is a more
 

00:01:35.520 --> 00:01:37.350 align:start position:0%
text analysis on news is a more
difficult<00:01:36.000><c> research</c><00:01:36.560><c> task</c><00:01:36.880><c> than</c><00:01:36.960><c> you</c><00:01:37.119><c> might</c>

00:01:37.350 --> 00:01:37.360 align:start position:0%
difficult research task than you might
 

00:01:37.360 --> 00:01:38.390 align:start position:0%
difficult research task than you might
imagine

00:01:38.390 --> 00:01:38.400 align:start position:0%
imagine
 

00:01:38.400 --> 00:01:40.149 align:start position:0%
imagine
let's<00:01:38.640><c> look</c><00:01:38.799><c> at</c><00:01:38.880><c> a</c><00:01:39.040><c> hypothetical</c><00:01:39.759><c> support</c>

00:01:40.149 --> 00:01:40.159 align:start position:0%
let's look at a hypothetical support
 

00:01:40.159 --> 00:01:42.389 align:start position:0%
let's look at a hypothetical support
inquiry<00:01:40.640><c> that</c><00:01:40.799><c> we</c><00:01:40.960><c> might</c><00:01:41.119><c> get</c><00:01:41.360><c> at</c><00:01:41.439><c> words</c>

00:01:42.389 --> 00:01:42.399 align:start position:0%
inquiry that we might get at words
 

00:01:42.399 --> 00:01:45.590 align:start position:0%
inquiry that we might get at words
here<00:01:42.880><c> inez</c><00:01:43.360><c> sabato</c><00:01:44.159><c> an</c><00:01:44.399><c> mba</c><00:01:44.799><c> student</c>

00:01:45.590 --> 00:01:45.600 align:start position:0%
here inez sabato an mba student
 

00:01:45.600 --> 00:01:47.350 align:start position:0%
here inez sabato an mba student
is<00:01:45.840><c> trying</c><00:01:46.079><c> to</c><00:01:46.159><c> determine</c><00:01:46.640><c> the</c><00:01:46.799><c> sentiment</c><00:01:47.280><c> of</c>

00:01:47.350 --> 00:01:47.360 align:start position:0%
is trying to determine the sentiment of
 

00:01:47.360 --> 00:01:49.910 align:start position:0%
is trying to determine the sentiment of
news<00:01:47.680><c> articles</c><00:01:48.079><c> for</c><00:01:48.320><c> tesla</c><00:01:48.799><c> and</c><00:01:48.960><c> elon</c><00:01:49.360><c> musk</c><00:01:49.759><c> in</c>

00:01:49.910 --> 00:01:49.920 align:start position:0%
news articles for tesla and elon musk in
 

00:01:49.920 --> 00:01:52.149 align:start position:0%
news articles for tesla and elon musk in
the<00:01:50.000><c> year</c><00:01:50.320><c> 2020.</c>

00:01:52.149 --> 00:01:52.159 align:start position:0%
the year 2020.
 

00:01:52.159 --> 00:01:54.310 align:start position:0%
the year 2020.
she's<00:01:52.399><c> trying</c><00:01:52.640><c> to</c><00:01:52.799><c> understand</c><00:01:53.439><c> why</c><00:01:53.840><c> tesla's</c>

00:01:54.310 --> 00:01:54.320 align:start position:0%
she's trying to understand why tesla's
 

00:01:54.320 --> 00:01:56.310 align:start position:0%
she's trying to understand why tesla's
stock<00:01:54.640><c> price</c><00:01:55.040><c> shot</c><00:01:55.280><c> up</c><00:01:55.520><c> over</c><00:01:55.759><c> the</c><00:01:55.920><c> course</c><00:01:56.240><c> of</c>

00:01:56.310 --> 00:01:56.320 align:start position:0%
stock price shot up over the course of
 

00:01:56.320 --> 00:01:57.190 align:start position:0%
stock price shot up over the course of
the<00:01:56.479><c> year</c>

00:01:57.190 --> 00:01:57.200 align:start position:0%
the year
 

00:01:57.200 --> 00:01:59.109 align:start position:0%
the year
despite<00:01:57.680><c> what</c><00:01:57.840><c> she</c><00:01:58.000><c> perceived</c><00:01:58.479><c> to</c><00:01:58.560><c> be</c><00:01:58.799><c> very</c>

00:01:59.109 --> 00:01:59.119 align:start position:0%
despite what she perceived to be very
 

00:01:59.119 --> 00:02:01.190 align:start position:0%
despite what she perceived to be very
negative<00:01:59.520><c> sentiment</c><00:02:00.000><c> about</c><00:02:00.320><c> founder</c><00:02:00.799><c> elon</c>

00:02:01.190 --> 00:02:01.200 align:start position:0%
negative sentiment about founder elon
 

00:02:01.200 --> 00:02:01.910 align:start position:0%
negative sentiment about founder elon
musk

00:02:01.910 --> 00:02:01.920 align:start position:0%
musk
 

00:02:01.920 --> 00:02:04.469 align:start position:0%
musk
in<00:02:02.000><c> the</c><00:02:02.159><c> press</c><00:02:03.360><c> unable</c><00:02:03.759><c> to</c><00:02:03.840><c> find</c><00:02:04.159><c> anything</c>

00:02:04.469 --> 00:02:04.479 align:start position:0%
in the press unable to find anything
 

00:02:04.479 --> 00:02:06.789 align:start position:0%
in the press unable to find anything
helpful<00:02:04.880><c> in</c><00:02:04.960><c> lexisnexis</c><00:02:05.759><c> infectiva</c>

00:02:06.789 --> 00:02:06.799 align:start position:0%
helpful in lexisnexis infectiva
 

00:02:06.799 --> 00:02:11.670 align:start position:0%
helpful in lexisnexis infectiva
she<00:02:07.040><c> turns</c><00:02:07.360><c> to</c><00:02:07.520><c> words</c><00:02:07.840><c> to</c><00:02:08.000><c> see</c><00:02:08.160><c> if</c><00:02:08.319><c> we</c><00:02:08.399><c> can</c><00:02:08.560><c> help</c>

00:02:11.670 --> 00:02:11.680 align:start position:0%
 
 

00:02:11.680 --> 00:02:14.150 align:start position:0%
 
before<00:02:12.080><c> we</c><00:02:12.239><c> get</c><00:02:12.480><c> to</c><00:02:12.560><c> the</c><00:02:12.720><c> words</c><00:02:13.280><c> text</c><00:02:13.599><c> analysis</c>

00:02:14.150 --> 00:02:14.160 align:start position:0%
before we get to the words text analysis
 

00:02:14.160 --> 00:02:15.110 align:start position:0%
before we get to the words text analysis
solution

00:02:15.110 --> 00:02:15.120 align:start position:0%
solution
 

00:02:15.120 --> 00:02:16.949 align:start position:0%
solution
let's<00:02:15.360><c> see</c><00:02:15.520><c> what</c><00:02:15.680><c> happens</c><00:02:16.080><c> when</c><00:02:16.319><c> inez</c><00:02:16.720><c> tries</c>

00:02:16.949 --> 00:02:16.959 align:start position:0%
let's see what happens when inez tries
 

00:02:16.959 --> 00:02:19.110 align:start position:0%
let's see what happens when inez tries
to<00:02:17.120><c> accomplish</c><00:02:17.599><c> her</c><00:02:17.840><c> task</c><00:02:18.160><c> using</c><00:02:18.400><c> the</c><00:02:18.560><c> popular</c>

00:02:19.110 --> 00:02:19.120 align:start position:0%
to accomplish her task using the popular
 

00:02:19.120 --> 00:02:21.430 align:start position:0%
to accomplish her task using the popular
nexus<00:02:19.520><c> uni</c><00:02:19.840><c> platform</c>

00:02:21.430 --> 00:02:21.440 align:start position:0%
nexus uni platform
 

00:02:21.440 --> 00:02:24.949 align:start position:0%
nexus uni platform
she<00:02:21.760><c> enters</c><00:02:22.080><c> her</c><00:02:22.239><c> search</c><00:02:22.480><c> parameters</c><00:02:23.680><c> tesla</c>

00:02:24.949 --> 00:02:24.959 align:start position:0%
she enters her search parameters tesla
 

00:02:24.959 --> 00:02:27.589 align:start position:0%
she enters her search parameters tesla
adds<00:02:25.280><c> elon</c><00:02:25.680><c> musk</c><00:02:26.000><c> to</c><00:02:26.160><c> refine</c><00:02:26.560><c> the</c><00:02:26.720><c> search</c><00:02:27.440><c> and</c>

00:02:27.589 --> 00:02:27.599 align:start position:0%
adds elon musk to refine the search and
 

00:02:27.599 --> 00:02:29.670 align:start position:0%
adds elon musk to refine the search and
nexus<00:02:28.000><c> uni</c><00:02:28.319><c> generates</c><00:02:28.800><c> the</c><00:02:28.959><c> search</c><00:02:29.200><c> results</c>

00:02:29.670 --> 00:02:29.680 align:start position:0%
nexus uni generates the search results
 

00:02:29.680 --> 00:02:31.830 align:start position:0%
nexus uni generates the search results
in<00:02:29.760><c> this</c><00:02:30.000><c> case</c><00:02:30.319><c> around</c><00:02:30.640><c> 10</c><00:02:30.959><c> 000</c><00:02:31.280><c> articles</c><00:02:31.680><c> that</c>

00:02:31.830 --> 00:02:31.840 align:start position:0%
in this case around 10 000 articles that
 

00:02:31.840 --> 00:02:33.589 align:start position:0%
in this case around 10 000 articles that
match<00:02:32.080><c> her</c><00:02:32.239><c> criteria</c>

00:02:33.589 --> 00:02:33.599 align:start position:0%
match her criteria
 

00:02:33.599 --> 00:02:35.670 align:start position:0%
match her criteria
the<00:02:33.840><c> only</c><00:02:34.160><c> option</c><00:02:34.480><c> for</c><00:02:34.640><c> further</c><00:02:35.040><c> analysis</c><00:02:35.599><c> is</c>

00:02:35.670 --> 00:02:35.680 align:start position:0%
the only option for further analysis is
 

00:02:35.680 --> 00:02:37.509 align:start position:0%
the only option for further analysis is
to<00:02:35.920><c> download</c><00:02:36.319><c> the</c><00:02:36.480><c> articles</c><00:02:36.879><c> and</c><00:02:37.040><c> analyze</c>

00:02:37.509 --> 00:02:37.519 align:start position:0%
to download the articles and analyze
 

00:02:37.519 --> 00:02:38.869 align:start position:0%
to download the articles and analyze
them<00:02:37.760><c> manually</c>

00:02:38.869 --> 00:02:38.879 align:start position:0%
them manually
 

00:02:38.879 --> 00:02:40.949 align:start position:0%
them manually
but<00:02:39.040><c> the</c><00:02:39.280><c> issue</c><00:02:39.599><c> here</c><00:02:39.920><c> is</c><00:02:40.080><c> that</c><00:02:40.239><c> nexus</c><00:02:40.640><c> uni</c>

00:02:40.949 --> 00:02:40.959 align:start position:0%
but the issue here is that nexus uni
 

00:02:40.959 --> 00:02:42.869 align:start position:0%
but the issue here is that nexus uni
makes<00:02:41.280><c> this</c><00:02:41.599><c> prohibitively</c><00:02:42.239><c> difficult</c>

00:02:42.869 --> 00:02:42.879 align:start position:0%
makes this prohibitively difficult
 

00:02:42.879 --> 00:02:45.509 align:start position:0%
makes this prohibitively difficult
by<00:02:43.200><c> limiting</c><00:02:43.519><c> the</c><00:02:43.680><c> download</c><00:02:44.400><c> to</c><00:02:44.640><c> 100</c><00:02:45.120><c> articles</c>

00:02:45.509 --> 00:02:45.519 align:start position:0%
by limiting the download to 100 articles
 

00:02:45.519 --> 00:02:49.030 align:start position:0%
by limiting the download to 100 articles
at<00:02:45.680><c> a</c><00:02:45.760><c> time</c>

00:02:49.030 --> 00:02:49.040 align:start position:0%
 
 

00:02:49.040 --> 00:02:50.630 align:start position:0%
 
as<00:02:49.200><c> part</c><00:02:49.440><c> of</c><00:02:49.599><c> our</c><00:02:49.680><c> collaboration</c><00:02:50.400><c> with</c>

00:02:50.630 --> 00:02:50.640 align:start position:0%
as part of our collaboration with
 

00:02:50.640 --> 00:02:52.869 align:start position:0%
as part of our collaboration with
lexisnexis<00:02:51.599><c> they</c><00:02:51.760><c> have</c><00:02:51.840><c> delivered</c><00:02:52.239><c> towards</c>

00:02:52.869 --> 00:02:52.879 align:start position:0%
lexisnexis they have delivered towards
 

00:02:52.879 --> 00:02:54.790 align:start position:0%
lexisnexis they have delivered towards
10<00:02:53.200><c> years</c><00:02:53.519><c> of</c><00:02:53.599><c> historical</c><00:02:54.239><c> news</c>

00:02:54.790 --> 00:02:54.800 align:start position:0%
10 years of historical news
 

00:02:54.800 --> 00:02:56.949 align:start position:0%
10 years of historical news
with<00:02:54.959><c> a</c><00:02:55.040><c> plan</c><00:02:55.360><c> to</c><00:02:55.440><c> get</c><00:02:55.599><c> the</c><00:02:55.760><c> remaining</c><00:02:56.319><c> 30-plus</c>

00:02:56.949 --> 00:02:56.959 align:start position:0%
with a plan to get the remaining 30-plus
 

00:02:56.959 --> 00:02:59.350 align:start position:0%
with a plan to get the remaining 30-plus
year<00:02:57.360><c> archive</c><00:02:57.840><c> in</c><00:02:58.000><c> the</c><00:02:58.080><c> near</c><00:02:58.319><c> future</c>

00:02:59.350 --> 00:02:59.360 align:start position:0%
year archive in the near future
 

00:02:59.360 --> 00:03:01.110 align:start position:0%
year archive in the near future
that<00:02:59.519><c> will</c><00:02:59.760><c> ultimately</c><00:03:00.319><c> allow</c><00:03:00.560><c> researchers</c>

00:03:01.110 --> 00:03:01.120 align:start position:0%
that will ultimately allow researchers
 

00:03:01.120 --> 00:03:06.390 align:start position:0%
that will ultimately allow researchers
to<00:03:01.280><c> access</c><00:03:01.680><c> news</c><00:03:02.000><c> going</c><00:03:02.239><c> back</c><00:03:02.480><c> to</c><00:03:02.560><c> the</c><00:03:02.840><c> 1970s</c>

00:03:06.390 --> 00:03:06.400 align:start position:0%
 
 

00:03:06.400 --> 00:03:08.630 align:start position:0%
 
let's<00:03:06.640><c> see</c><00:03:06.800><c> what</c><00:03:06.959><c> inez's</c><00:03:07.519><c> research</c><00:03:08.000><c> use</c><00:03:08.239><c> case</c>

00:03:08.630 --> 00:03:08.640 align:start position:0%
let's see what inez's research use case
 

00:03:08.640 --> 00:03:10.790 align:start position:0%
let's see what inez's research use case
looks<00:03:08.879><c> like</c><00:03:09.200><c> on</c><00:03:09.360><c> the</c><00:03:09.519><c> words</c><00:03:09.920><c> text</c><00:03:10.239><c> analysis</c>

00:03:10.790 --> 00:03:10.800 align:start position:0%
looks like on the words text analysis
 

00:03:10.800 --> 00:03:11.910 align:start position:0%
looks like on the words text analysis
platform

00:03:11.910 --> 00:03:11.920 align:start position:0%
platform
 

00:03:11.920 --> 00:03:13.750 align:start position:0%
platform
you'll<00:03:12.080><c> see</c><00:03:12.239><c> that</c><00:03:12.400><c> it's</c><00:03:12.560><c> very</c><00:03:12.959><c> similar</c><00:03:13.440><c> until</c>

00:03:13.750 --> 00:03:13.760 align:start position:0%
you'll see that it's very similar until
 

00:03:13.760 --> 00:03:15.509 align:start position:0%
you'll see that it's very similar until
we<00:03:13.920><c> get</c><00:03:14.080><c> to</c><00:03:14.239><c> the</c><00:03:14.319><c> very</c><00:03:14.640><c> end</c>

00:03:15.509 --> 00:03:15.519 align:start position:0%
we get to the very end
 

00:03:15.519 --> 00:03:18.390 align:start position:0%
we get to the very end
inez<00:03:16.000><c> enters</c><00:03:16.400><c> her</c><00:03:16.560><c> search</c><00:03:16.879><c> parameters</c>

00:03:18.390 --> 00:03:18.400 align:start position:0%
inez enters her search parameters
 

00:03:18.400 --> 00:03:20.149 align:start position:0%
inez enters her search parameters
refines<00:03:18.879><c> her</c><00:03:19.120><c> search</c>

00:03:20.149 --> 00:03:20.159 align:start position:0%
refines her search
 

00:03:20.159 --> 00:03:22.470 align:start position:0%
refines her search
and<00:03:20.400><c> text</c><00:03:20.720><c> analysis</c><00:03:21.280><c> delivers</c><00:03:21.680><c> the</c><00:03:21.840><c> same</c><00:03:22.159><c> 10</c>

00:03:22.470 --> 00:03:22.480 align:start position:0%
and text analysis delivers the same 10
 

00:03:22.480 --> 00:03:25.589 align:start position:0%
and text analysis delivers the same 10
000<00:03:22.800><c> articles</c><00:03:23.200><c> that</c><00:03:23.280><c> we</c><00:03:23.440><c> saw</c><00:03:23.680><c> from</c><00:03:23.840><c> nexus</c><00:03:24.239><c> uni</c>

00:03:25.589 --> 00:03:25.599 align:start position:0%
000 articles that we saw from nexus uni
 

00:03:25.599 --> 00:03:28.390 align:start position:0%
000 articles that we saw from nexus uni
now<00:03:25.920><c> however</c><00:03:26.640><c> her</c><00:03:26.879><c> options</c><00:03:27.280><c> have</c><00:03:27.440><c> increased</c>

00:03:28.390 --> 00:03:28.400 align:start position:0%
now however her options have increased
 

00:03:28.400 --> 00:03:29.990 align:start position:0%
now however her options have increased
in<00:03:28.640><c> addition</c><00:03:28.959><c> to</c><00:03:29.120><c> being</c><00:03:29.360><c> able</c><00:03:29.519><c> to</c><00:03:29.599><c> download</c>

00:03:29.990 --> 00:03:30.000 align:start position:0%
in addition to being able to download
 

00:03:30.000 --> 00:03:32.710 align:start position:0%
in addition to being able to download
the<00:03:30.159><c> articles</c><00:03:30.720><c> in</c><00:03:30.879><c> an</c><00:03:31.040><c> unconstrained</c><00:03:31.840><c> manner</c>

00:03:32.710 --> 00:03:32.720 align:start position:0%
the articles in an unconstrained manner
 

00:03:32.720 --> 00:03:35.110 align:start position:0%
the articles in an unconstrained manner
she<00:03:32.879><c> may</c><00:03:33.120><c> also</c><00:03:33.519><c> use</c><00:03:33.840><c> one</c><00:03:34.080><c> of</c><00:03:34.159><c> the</c><00:03:34.239><c> many</c><00:03:34.640><c> nlp</c>

00:03:35.110 --> 00:03:35.120 align:start position:0%
she may also use one of the many nlp
 

00:03:35.120 --> 00:03:36.869 align:start position:0%
she may also use one of the many nlp
pipelines<00:03:35.680><c> that</c><00:03:35.840><c> words</c><00:03:36.159><c> has</c><00:03:36.400><c> built</c>

00:03:36.869 --> 00:03:36.879 align:start position:0%
pipelines that words has built
 

00:03:36.879 --> 00:03:40.070 align:start position:0%
pipelines that words has built
directly<00:03:37.599><c> into</c><00:03:37.840><c> the</c><00:03:38.000><c> text</c><00:03:38.319><c> analysis</c><00:03:38.799><c> solution</c>

00:03:40.070 --> 00:03:40.080 align:start position:0%
directly into the text analysis solution
 

00:03:40.080 --> 00:03:42.470 align:start position:0%
directly into the text analysis solution
now<00:03:40.239><c> let's</c><00:03:40.480><c> do</c><00:03:40.640><c> a</c><00:03:40.799><c> live</c><00:03:41.120><c> test</c><00:03:41.440><c> drive</c><00:03:41.840><c> of</c><00:03:42.080><c> words</c>

00:03:42.470 --> 00:03:42.480 align:start position:0%
now let's do a live test drive of words
 

00:03:42.480 --> 00:03:45.750 align:start position:0%
now let's do a live test drive of words
text<00:03:42.799><c> analysis</c>

00:03:45.750 --> 00:03:45.760 align:start position:0%
 
 

00:03:45.760 --> 00:03:47.589 align:start position:0%
 
let's<00:03:46.000><c> go</c><00:03:46.159><c> ahead</c><00:03:46.400><c> and</c><00:03:46.560><c> recreate</c><00:03:47.120><c> inez's</c>

00:03:47.589 --> 00:03:47.599 align:start position:0%
let's go ahead and recreate inez's
 

00:03:47.599 --> 00:03:50.630 align:start position:0%
let's go ahead and recreate inez's
research<00:03:48.080><c> use</c><00:03:48.319><c> case</c><00:03:48.640><c> in</c><00:03:48.720><c> the</c><00:03:48.879><c> live</c><00:03:49.120><c> system</c>

00:03:50.630 --> 00:03:50.640 align:start position:0%
research use case in the live system
 

00:03:50.640 --> 00:03:54.149 align:start position:0%
research use case in the live system
i<00:03:50.879><c> enter</c><00:03:51.200><c> tesla</c>

00:03:54.149 --> 00:03:54.159 align:start position:0%
 
 

00:03:54.159 --> 00:03:58.470 align:start position:0%
 
and<00:03:54.319><c> hit</c><00:03:54.560><c> search</c>

00:03:58.470 --> 00:03:58.480 align:start position:0%
 
 

00:03:58.480 --> 00:03:59.910 align:start position:0%
 
you<00:03:58.560><c> may</c><00:03:58.799><c> notice</c><00:03:59.040><c> that</c><00:03:59.200><c> it</c><00:03:59.280><c> takes</c><00:03:59.519><c> a</c><00:03:59.680><c> little</c>

00:03:59.910 --> 00:03:59.920 align:start position:0%
you may notice that it takes a little
 

00:03:59.920 --> 00:04:01.910 align:start position:0%
you may notice that it takes a little
bit<00:04:00.080><c> of</c><00:04:00.239><c> time</c><00:04:00.560><c> as</c><00:04:00.720><c> it</c><00:04:00.879><c> goes</c><00:04:01.120><c> through</c><00:04:01.360><c> 10</c><00:04:01.599><c> years</c>

00:04:01.910 --> 00:04:01.920 align:start position:0%
bit of time as it goes through 10 years
 

00:04:01.920 --> 00:04:02.789 align:start position:0%
bit of time as it goes through 10 years
of<00:04:02.080><c> news</c>

00:04:02.789 --> 00:04:02.799 align:start position:0%
of news
 

00:04:02.799 --> 00:04:10.949 align:start position:0%
of news
to<00:04:02.959><c> retrieve</c><00:04:03.439><c> the</c><00:04:03.599><c> articles</c><00:04:04.080><c> that</c><00:04:04.239><c> we</c><00:04:04.400><c> want</c>

00:04:10.949 --> 00:04:10.959 align:start position:0%
 
 

00:04:10.959 --> 00:04:13.110 align:start position:0%
 
on<00:04:11.120><c> the</c><00:04:11.280><c> results</c><00:04:11.680><c> page</c><00:04:12.400><c> we</c><00:04:12.560><c> are</c><00:04:12.720><c> given</c><00:04:12.959><c> the</c>

00:04:13.110 --> 00:04:13.120 align:start position:0%
on the results page we are given the
 

00:04:13.120 --> 00:04:14.070 align:start position:0%
on the results page we are given the
option<00:04:13.519><c> to</c><00:04:13.680><c> filter</c>

00:04:14.070 --> 00:04:14.080 align:start position:0%
option to filter
 

00:04:14.080 --> 00:04:16.870 align:start position:0%
option to filter
the<00:04:14.239><c> results</c><00:04:14.879><c> even</c><00:04:15.200><c> further</c><00:04:16.320><c> recall</c><00:04:16.720><c> that</c>

00:04:16.870 --> 00:04:16.880 align:start position:0%
the results even further recall that
 

00:04:16.880 --> 00:04:18.710 align:start position:0%
the results even further recall that
inez<00:04:17.280><c> wants</c><00:04:17.519><c> to</c><00:04:17.680><c> limit</c><00:04:17.919><c> the</c><00:04:18.079><c> articles</c><00:04:18.479><c> to</c>

00:04:18.710 --> 00:04:18.720 align:start position:0%
inez wants to limit the articles to
 

00:04:18.720 --> 00:04:20.789 align:start position:0%
inez wants to limit the articles to
those<00:04:19.040><c> that</c><00:04:19.359><c> specifically</c><00:04:20.079><c> mention</c>

00:04:20.789 --> 00:04:20.799 align:start position:0%
those that specifically mention
 

00:04:20.799 --> 00:04:31.110 align:start position:0%
those that specifically mention
elon<00:04:26.840><c> musk</c>

00:04:31.110 --> 00:04:31.120 align:start position:0%
 
 

00:04:31.120 --> 00:04:32.710 align:start position:0%
 
now<00:04:31.360><c> that</c><00:04:31.440><c> we</c><00:04:31.600><c> have</c><00:04:31.840><c> the</c><00:04:32.000><c> articles</c><00:04:32.400><c> that</c><00:04:32.560><c> we</c>

00:04:32.710 --> 00:04:32.720 align:start position:0%
now that we have the articles that we
 

00:04:32.720 --> 00:04:38.469 align:start position:0%
now that we have the articles that we
want<00:04:33.600><c> let's</c><00:04:33.840><c> select</c><00:04:34.240><c> a</c><00:04:34.320><c> few</c><00:04:34.560><c> for</c><00:04:34.720><c> processing</c>

00:04:38.469 --> 00:04:38.479 align:start position:0%
 
 

00:04:38.479 --> 00:04:40.629 align:start position:0%
 
i've<00:04:38.800><c> added</c><00:04:39.040><c> the</c><00:04:39.199><c> first</c><00:04:39.600><c> 10</c><00:04:39.919><c> articles</c><00:04:40.320><c> to</c><00:04:40.479><c> my</c>

00:04:40.629 --> 00:04:40.639 align:start position:0%
i've added the first 10 articles to my
 

00:04:40.639 --> 00:04:42.629 align:start position:0%
i've added the first 10 articles to my
text<00:04:40.960><c> analysis</c><00:04:41.520><c> cart</c>

00:04:42.629 --> 00:04:42.639 align:start position:0%
text analysis cart
 

00:04:42.639 --> 00:04:47.350 align:start position:0%
text analysis cart
i'll<00:04:42.880><c> click</c><00:04:43.199><c> the</c><00:04:43.360><c> cart</c><00:04:43.680><c> button</c><00:04:44.080><c> to</c><00:04:44.240><c> proceed</c>

00:04:47.350 --> 00:04:47.360 align:start position:0%
 
 

00:04:47.360 --> 00:04:50.070 align:start position:0%
 
on<00:04:47.520><c> this</c><00:04:47.840><c> page</c><00:04:48.320><c> we</c><00:04:48.479><c> can</c><00:04:48.720><c> review</c><00:04:49.199><c> or</c><00:04:49.360><c> delete</c><00:04:49.840><c> any</c>

00:04:50.070 --> 00:04:50.080 align:start position:0%
on this page we can review or delete any
 

00:04:50.080 --> 00:04:51.749 align:start position:0%
on this page we can review or delete any
articles<00:04:50.560><c> in</c><00:04:50.639><c> the</c><00:04:50.800><c> cart</c>

00:04:51.749 --> 00:04:51.759 align:start position:0%
articles in the cart
 

00:04:51.759 --> 00:04:53.990 align:start position:0%
articles in the cart
we<00:04:51.919><c> can</c><00:04:52.160><c> also</c><00:04:52.479><c> take</c><00:04:52.720><c> advanced</c><00:04:53.199><c> actions</c><00:04:53.759><c> such</c>

00:04:53.990 --> 00:04:54.000 align:start position:0%
we can also take advanced actions such
 

00:04:54.000 --> 00:04:56.710 align:start position:0%
we can also take advanced actions such
as<00:04:54.840><c> deduplication</c>

00:04:56.710 --> 00:04:56.720 align:start position:0%
as deduplication
 

00:04:56.720 --> 00:05:01.990 align:start position:0%
as deduplication
let's<00:04:56.960><c> proceed</c><00:04:57.360><c> to</c><00:04:57.520><c> text</c><00:04:57.840><c> analysis</c>

00:05:01.990 --> 00:05:02.000 align:start position:0%
 
 

00:05:02.000 --> 00:05:05.110 align:start position:0%
 
here<00:05:02.880><c> if</c><00:05:03.039><c> i</c><00:05:03.199><c> wanted</c><00:05:04.000><c> i</c><00:05:04.160><c> could</c><00:05:04.400><c> search</c><00:05:04.720><c> for</c>

00:05:05.110 --> 00:05:05.120 align:start position:0%
here if i wanted i could search for
 

00:05:05.120 --> 00:05:07.909 align:start position:0%
here if i wanted i could search for
and<00:05:05.280><c> add</c><00:05:05.600><c> additional</c><00:05:06.080><c> results</c><00:05:06.479><c> to</c><00:05:06.639><c> my</c><00:05:06.800><c> cart</c>

00:05:07.909 --> 00:05:07.919 align:start position:0%
and add additional results to my cart
 

00:05:07.919 --> 00:05:14.469 align:start position:0%
and add additional results to my cart
let's<00:05:08.160><c> just</c><00:05:08.400><c> continue</c><00:05:08.880><c> with</c><00:05:09.039><c> what</c><00:05:09.199><c> we</c><00:05:09.360><c> have</c>

00:05:14.469 --> 00:05:14.479 align:start position:0%
 
 

00:05:14.479 --> 00:05:16.790 align:start position:0%
 
on<00:05:14.639><c> this</c><00:05:14.880><c> page</c><00:05:15.759><c> you</c><00:05:15.919><c> can</c><00:05:16.080><c> see</c><00:05:16.240><c> the</c><00:05:16.479><c> growing</c>

00:05:16.790 --> 00:05:16.800 align:start position:0%
on this page you can see the growing
 

00:05:16.800 --> 00:05:19.350 align:start position:0%
on this page you can see the growing
list<00:05:17.120><c> of</c><00:05:17.280><c> nlp</c><00:05:17.840><c> pipelines</c><00:05:18.560><c> created</c><00:05:18.960><c> by</c><00:05:19.199><c> our</c>

00:05:19.350 --> 00:05:19.360 align:start position:0%
list of nlp pipelines created by our
 

00:05:19.360 --> 00:05:20.870 align:start position:0%
list of nlp pipelines created by our
engineers<00:05:19.840><c> at</c><00:05:20.000><c> words</c>

00:05:20.870 --> 00:05:20.880 align:start position:0%
engineers at words
 

00:05:20.880 --> 00:05:23.510 align:start position:0%
engineers at words
let's<00:05:21.199><c> choose</c><00:05:21.600><c> the</c><00:05:21.680><c> first</c><00:05:22.080><c> option</c><00:05:22.800><c> the</c><00:05:23.039><c> vader</c>

00:05:23.510 --> 00:05:23.520 align:start position:0%
let's choose the first option the vader
 

00:05:23.520 --> 00:05:24.070 align:start position:0%
let's choose the first option the vader
sentiment

00:05:24.070 --> 00:05:24.080 align:start position:0%
sentiment
 

00:05:24.080 --> 00:05:27.990 align:start position:0%
sentiment
algorithm

00:05:27.990 --> 00:05:28.000 align:start position:0%
 
 

00:05:28.000 --> 00:05:30.469 align:start position:0%
 
here<00:05:28.400><c> we</c><00:05:28.560><c> can</c><00:05:28.720><c> give</c><00:05:28.880><c> our</c><00:05:29.039><c> task</c><00:05:29.360><c> a</c><00:05:29.440><c> name</c><00:05:30.000><c> and</c><00:05:30.240><c> add</c>

00:05:30.469 --> 00:05:30.479 align:start position:0%
here we can give our task a name and add
 

00:05:30.479 --> 00:05:31.670 align:start position:0%
here we can give our task a name and add
any<00:05:30.639><c> notes</c>

00:05:31.670 --> 00:05:31.680 align:start position:0%
any notes
 

00:05:31.680 --> 00:05:33.749 align:start position:0%
any notes
i'll<00:05:32.000><c> also</c><00:05:32.320><c> receive</c><00:05:32.639><c> an</c><00:05:32.800><c> email</c><00:05:33.199><c> when</c><00:05:33.360><c> the</c><00:05:33.440><c> job</c>

00:05:33.749 --> 00:05:33.759 align:start position:0%
i'll also receive an email when the job
 

00:05:33.759 --> 00:05:35.189 align:start position:0%
i'll also receive an email when the job
is<00:05:33.919><c> complete</c>

00:05:35.189 --> 00:05:35.199 align:start position:0%
is complete
 

00:05:35.199 --> 00:05:42.950 align:start position:0%
is complete
let's<00:05:35.440><c> accept</c><00:05:35.840><c> the</c><00:05:36.000><c> defaults</c><00:05:36.720><c> and</c><00:05:36.880><c> continue</c>

00:05:42.950 --> 00:05:42.960 align:start position:0%
 
 

00:05:42.960 --> 00:05:44.950 align:start position:0%
 
the<00:05:43.120><c> text</c><00:05:43.440><c> analysis</c><00:05:44.080><c> job</c><00:05:44.400><c> has</c><00:05:44.560><c> now</c><00:05:44.800><c> been</c>

00:05:44.950 --> 00:05:44.960 align:start position:0%
the text analysis job has now been
 

00:05:44.960 --> 00:05:46.950 align:start position:0%
the text analysis job has now been
handed<00:05:45.280><c> off</c><00:05:45.440><c> to</c><00:05:45.600><c> our</c><00:05:45.840><c> process</c><00:05:46.240><c> handler</c><00:05:46.800><c> that</c>

00:05:46.950 --> 00:05:46.960 align:start position:0%
handed off to our process handler that
 

00:05:46.960 --> 00:05:47.670 align:start position:0%
handed off to our process handler that
we<00:05:47.199><c> call</c>

00:05:47.670 --> 00:05:47.680 align:start position:0%
we call
 

00:05:47.680 --> 00:05:50.310 align:start position:0%
we call
the<00:05:47.840><c> query</c><00:05:48.160><c> manager</c><00:05:49.440><c> query</c><00:05:49.759><c> manager</c>

00:05:50.310 --> 00:05:50.320 align:start position:0%
the query manager query manager
 

00:05:50.320 --> 00:05:52.230 align:start position:0%
the query manager query manager
leverages<00:05:50.880><c> the</c><00:05:51.039><c> same</c><00:05:51.360><c> powerful</c><00:05:51.919><c> grid</c>

00:05:52.230 --> 00:05:52.240 align:start position:0%
leverages the same powerful grid
 

00:05:52.240 --> 00:05:53.430 align:start position:0%
leverages the same powerful grid
computing<00:05:52.800><c> and</c><00:05:52.960><c> cloud</c>

00:05:53.430 --> 00:05:53.440 align:start position:0%
computing and cloud
 

00:05:53.440 --> 00:05:55.990 align:start position:0%
computing and cloud
infrastructure<00:05:54.479><c> that</c><00:05:54.720><c> powers</c><00:05:55.039><c> the</c><00:05:55.199><c> website</c>

00:05:55.990 --> 00:05:56.000 align:start position:0%
infrastructure that powers the website
 

00:05:56.000 --> 00:05:57.909 align:start position:0%
infrastructure that powers the website
for<00:05:56.240><c> the</c><00:05:56.400><c> thousands</c><00:05:56.800><c> of</c><00:05:56.960><c> queries</c><00:05:57.360><c> that</c><00:05:57.600><c> words</c>

00:05:57.909 --> 00:05:57.919 align:start position:0%
for the thousands of queries that words
 

00:05:57.919 --> 00:05:59.430 align:start position:0%
for the thousands of queries that words
handles<00:05:58.400><c> every</c><00:05:58.639><c> single</c><00:05:58.960><c> day</c>

00:05:59.430 --> 00:05:59.440 align:start position:0%
handles every single day
 

00:05:59.440 --> 00:06:02.790 align:start position:0%
handles every single day
from<00:05:59.680><c> all</c><00:05:59.919><c> over</c><00:06:00.160><c> the</c><00:06:00.240><c> world</c><00:06:02.240><c> when</c><00:06:02.400><c> this</c><00:06:02.639><c> is</c>

00:06:02.790 --> 00:06:02.800 align:start position:0%
from all over the world when this is
 

00:06:02.800 --> 00:06:03.670 align:start position:0%
from all over the world when this is
done

00:06:03.670 --> 00:06:03.680 align:start position:0%
done
 

00:06:03.680 --> 00:06:11.749 align:start position:0%
done
let's<00:06:04.000><c> view</c><00:06:04.160><c> the</c><00:06:04.840><c> report</c>

00:06:11.749 --> 00:06:11.759 align:start position:0%
 
 

00:06:11.759 --> 00:06:14.309 align:start position:0%
 
let's<00:06:12.000><c> first</c><00:06:12.240><c> get</c><00:06:12.479><c> a</c><00:06:12.639><c> visual</c><00:06:13.120><c> summary</c><00:06:14.000><c> of</c><00:06:14.160><c> our</c>

00:06:14.309 --> 00:06:14.319 align:start position:0%
let's first get a visual summary of our
 

00:06:14.319 --> 00:06:19.749 align:start position:0%
let's first get a visual summary of our
results

00:06:19.749 --> 00:06:19.759 align:start position:0%
 
 

00:06:19.759 --> 00:06:22.629 align:start position:0%
 
when<00:06:20.080><c> as</c><00:06:20.240><c> a</c><00:06:20.319><c> surprise</c><00:06:21.520><c> the</c><00:06:21.680><c> vader</c><00:06:22.080><c> output</c><00:06:22.479><c> has</c>

00:06:22.629 --> 00:06:22.639 align:start position:0%
when as a surprise the vader output has
 

00:06:22.639 --> 00:06:24.390 align:start position:0%
when as a surprise the vader output has
determined<00:06:23.199><c> that</c><00:06:23.360><c> the</c><00:06:23.520><c> news</c><00:06:23.840><c> articles</c><00:06:24.319><c> are</c>

00:06:24.390 --> 00:06:24.400 align:start position:0%
determined that the news articles are
 

00:06:24.400 --> 00:06:25.189 align:start position:0%
determined that the news articles are
about

00:06:25.189 --> 00:06:25.199 align:start position:0%
about
 

00:06:25.199 --> 00:06:28.230 align:start position:0%
about
90<00:06:26.240><c> positive</c><00:06:27.360><c> and</c><00:06:27.520><c> 10</c>

00:06:28.230 --> 00:06:28.240 align:start position:0%
90 positive and 10
 

00:06:28.240 --> 00:06:30.950 align:start position:0%
90 positive and 10
negative<00:06:29.520><c> we</c><00:06:29.680><c> can</c><00:06:29.919><c> examine</c><00:06:30.319><c> the</c><00:06:30.479><c> results</c><00:06:30.880><c> of</c>

00:06:30.950 --> 00:06:30.960 align:start position:0%
negative we can examine the results of
 

00:06:30.960 --> 00:06:32.830 align:start position:0%
negative we can examine the results of
the<00:06:31.199><c> individual</c><00:06:31.919><c> articles</c>

00:06:32.830 --> 00:06:32.840 align:start position:0%
the individual articles
 

00:06:32.840 --> 00:06:40.390 align:start position:0%
the individual articles
below

00:06:40.390 --> 00:06:40.400 align:start position:0%
 
 

00:06:40.400 --> 00:06:42.469 align:start position:0%
 
the<00:06:40.639><c> profiling</c><00:06:41.199><c> tab</c><00:06:41.600><c> tells</c><00:06:41.840><c> us</c><00:06:42.000><c> how</c><00:06:42.160><c> long</c><00:06:42.319><c> the</c>

00:06:42.469 --> 00:06:42.479 align:start position:0%
the profiling tab tells us how long the
 

00:06:42.479 --> 00:06:45.110 align:start position:0%
the profiling tab tells us how long the
process<00:06:42.960><c> took</c><00:06:43.199><c> to</c><00:06:43.360><c> run</c>

00:06:45.110 --> 00:06:45.120 align:start position:0%
process took to run
 

00:06:45.120 --> 00:06:47.830 align:start position:0%
process took to run
advanced<00:06:45.680><c> users</c><00:06:46.800><c> can</c><00:06:47.039><c> download</c><00:06:47.440><c> the</c><00:06:47.600><c> raw</c>

00:06:47.830 --> 00:06:47.840 align:start position:0%
advanced users can download the raw
 

00:06:47.840 --> 00:06:48.469 align:start position:0%
advanced users can download the raw
results

00:06:48.469 --> 00:06:48.479 align:start position:0%
results
 

00:06:48.479 --> 00:06:51.749 align:start position:0%
results
from<00:06:48.720><c> the</c><00:06:48.800><c> data</c><00:06:49.120><c> download</c><00:06:49.680><c> tab</c>

00:06:51.749 --> 00:06:51.759 align:start position:0%
from the data download tab
 

00:06:51.759 --> 00:06:54.710 align:start position:0%
from the data download tab
finally<00:06:52.880><c> the</c><00:06:53.039><c> requested</c><00:06:53.680><c> records</c><00:06:54.160><c> tab</c><00:06:54.479><c> gives</c>

00:06:54.710 --> 00:06:54.720 align:start position:0%
finally the requested records tab gives
 

00:06:54.720 --> 00:06:56.870 align:start position:0%
finally the requested records tab gives
us<00:06:54.880><c> a</c><00:06:55.039><c> precise</c><00:06:55.599><c> manifest</c><00:06:56.160><c> of</c><00:06:56.319><c> the</c><00:06:56.400><c> articles</c>

00:06:56.870 --> 00:06:56.880 align:start position:0%
us a precise manifest of the articles
 

00:06:56.880 --> 00:06:58.309 align:start position:0%
us a precise manifest of the articles
that<00:06:56.960><c> we</c><00:06:57.199><c> examined</c>

00:06:58.309 --> 00:06:58.319 align:start position:0%
that we examined
 

00:06:58.319 --> 00:07:00.230 align:start position:0%
that we examined
this<00:06:58.639><c> will</c><00:06:58.800><c> be</c><00:06:58.880><c> helpful</c><00:06:59.280><c> in</c><00:06:59.360><c> the</c><00:06:59.520><c> future</c><00:07:00.080><c> if</c>

00:07:00.230 --> 00:07:00.240 align:start position:0%
this will be helpful in the future if
 

00:07:00.240 --> 00:07:02.150 align:start position:0%
this will be helpful in the future if
anyone<00:07:00.639><c> wants</c><00:07:00.880><c> to</c><00:07:00.960><c> reproduce</c><00:07:01.599><c> inez's</c>

00:07:02.150 --> 00:07:02.160 align:start position:0%
anyone wants to reproduce inez's
 

00:07:02.160 --> 00:07:08.870 align:start position:0%
anyone wants to reproduce inez's
research

00:07:08.870 --> 00:07:08.880 align:start position:0%
 
 

00:07:08.880 --> 00:07:11.350 align:start position:0%
 
words<00:07:09.280><c> text</c><00:07:09.599><c> analysis</c><00:07:10.240><c> has</c><00:07:10.400><c> been</c><00:07:10.639><c> designed</c><00:07:11.199><c> so</c>

00:07:11.350 --> 00:07:11.360 align:start position:0%
words text analysis has been designed so
 

00:07:11.360 --> 00:07:12.309 align:start position:0%
words text analysis has been designed so
that<00:07:11.759><c> text</c>

00:07:12.309 --> 00:07:12.319 align:start position:0%
that text
 

00:07:12.319 --> 00:07:15.510 align:start position:0%
that text
sources<00:07:13.520><c> and</c><00:07:13.680><c> the</c><00:07:13.840><c> text</c><00:07:14.160><c> analysis</c><00:07:14.880><c> engine</c>

00:07:15.510 --> 00:07:15.520 align:start position:0%
sources and the text analysis engine
 

00:07:15.520 --> 00:07:18.390 align:start position:0%
sources and the text analysis engine
remain<00:07:16.000><c> decoupled</c><00:07:17.360><c> this</c><00:07:17.599><c> allows</c><00:07:17.919><c> our</c><00:07:18.080><c> data</c>

00:07:18.390 --> 00:07:18.400 align:start position:0%
remain decoupled this allows our data
 

00:07:18.400 --> 00:07:20.469 align:start position:0%
remain decoupled this allows our data
engineers<00:07:18.880><c> to</c><00:07:19.039><c> add</c><00:07:19.280><c> new</c><00:07:19.520><c> data</c><00:07:19.840><c> sources</c><00:07:20.319><c> in</c><00:07:20.400><c> a</c>

00:07:20.469 --> 00:07:20.479 align:start position:0%
engineers to add new data sources in a
 

00:07:20.479 --> 00:07:21.510 align:start position:0%
engineers to add new data sources in a
matter<00:07:20.720><c> of</c><00:07:20.960><c> hours</c>

00:07:21.510 --> 00:07:21.520 align:start position:0%
matter of hours
 

00:07:21.520 --> 00:07:24.469 align:start position:0%
matter of hours
and<00:07:21.680><c> not</c><00:07:22.000><c> days</c><00:07:22.319><c> or</c><00:07:22.479><c> weeks</c><00:07:23.680><c> text</c><00:07:24.000><c> sources</c>

00:07:24.469 --> 00:07:24.479 align:start position:0%
and not days or weeks text sources
 

00:07:24.479 --> 00:07:26.230 align:start position:0%
and not days or weeks text sources
currently<00:07:24.880><c> bundled</c><00:07:25.199><c> with</c><00:07:25.360><c> the</c><00:07:25.520><c> platform</c>

00:07:26.230 --> 00:07:26.240 align:start position:0%
currently bundled with the platform
 

00:07:26.240 --> 00:07:28.830 align:start position:0%
currently bundled with the platform
include<00:07:26.639><c> capital</c><00:07:27.039><c> iq</c><00:07:27.440><c> transcripts</c><00:07:28.560><c> key</c>

00:07:28.830 --> 00:07:28.840 align:start position:0%
include capital iq transcripts key
 

00:07:28.840 --> 00:07:31.510 align:start position:0%
include capital iq transcripts key
developments<00:07:29.840><c> and</c><00:07:30.080><c> audit</c><00:07:30.400><c> analytics</c>

00:07:31.510 --> 00:07:31.520 align:start position:0%
developments and audit analytics
 

00:07:31.520 --> 00:07:35.909 align:start position:0%
developments and audit analytics
and<00:07:31.919><c> the</c><00:07:32.080><c> list</c><00:07:32.400><c> is</c><00:07:32.479><c> growing</c>

00:07:35.909 --> 00:07:35.919 align:start position:0%
 
 

00:07:35.919 --> 00:07:37.909 align:start position:0%
 
believe<00:07:36.240><c> it</c><00:07:36.319><c> or</c><00:07:36.400><c> not</c><00:07:36.880><c> there's</c><00:07:37.199><c> even</c><00:07:37.520><c> more</c><00:07:37.759><c> to</c>

00:07:37.909 --> 00:07:37.919 align:start position:0%
believe it or not there's even more to
 

00:07:37.919 --> 00:07:39.670 align:start position:0%
believe it or not there's even more to
the<00:07:38.000><c> text</c><00:07:38.319><c> analysis</c><00:07:38.880><c> platform</c><00:07:39.360><c> than</c><00:07:39.520><c> i've</c>

00:07:39.670 --> 00:07:39.680 align:start position:0%
the text analysis platform than i've
 

00:07:39.680 --> 00:07:40.870 align:start position:0%
the text analysis platform than i've
shown<00:07:40.000><c> here</c>

00:07:40.870 --> 00:07:40.880 align:start position:0%
shown here
 

00:07:40.880 --> 00:07:42.950 align:start position:0%
shown here
to<00:07:41.039><c> learn</c><00:07:41.360><c> more</c><00:07:41.919><c> please</c><00:07:42.160><c> don't</c><00:07:42.400><c> hesitate</c><00:07:42.800><c> to</c>

00:07:42.950 --> 00:07:42.960 align:start position:0%
to learn more please don't hesitate to
 

00:07:42.960 --> 00:07:44.790 align:start position:0%
to learn more please don't hesitate to
reach<00:07:43.199><c> out</c><00:07:43.280><c> to</c><00:07:43.440><c> me</c><00:07:43.599><c> directly</c>

00:07:44.790 --> 00:07:44.800 align:start position:0%
reach out to me directly
 

00:07:44.800 --> 00:07:46.309 align:start position:0%
reach out to me directly
thanks<00:07:45.039><c> for</c><00:07:45.199><c> attending</c><00:07:45.599><c> the</c><00:07:45.680><c> words</c><00:07:46.000><c> text</c>

00:07:46.309 --> 00:07:46.319 align:start position:0%
thanks for attending the words text
 

00:07:46.319 --> 00:07:52.510 align:start position:0%
thanks for attending the words text
analysis<00:07:46.800><c> lightning</c><00:07:47.199><c> talk</c><00:07:47.759><c> and</c><00:07:48.000><c> have</c><00:07:48.160><c> a</c><00:07:48.319><c> great</c>

00:07:52.510 --> 00:07:52.520 align:start position:0%
 
 

00:07:52.520 --> 00:07:55.520 align:start position:0%
 
day

