import os
from supabase import create_client
import json
from datetime import datetime
import asyncio
from tenacity import retry, stop_after_attempt, wait_exponential
import tiktoken
from dotenv import load_dotenv
import time
import requests
import logging

load_dotenv()

# Initialize Supabase client
supabase = create_client(os.environ.get("SUPABASE_URL"), os.environ.get("SERVICE_ROLE_KEY"))

"""
This script is designed to review and validate AI-generated responses for video transcript analysis stored in a Supabase database. It performs the following main functions:

1. Connects to a Supabase database using environment variables for authentication.
2. Retrieves video transcripts and their corresponding AI-generated analyses from specified tables in the database.
3. Uses the Ollama API to generate a review of the saved AI responses, ensuring they meet specific criteria such as:
   - Correct JSON format
   - Presence of required keys (keywords, typical_questions, key_conclusions)
   - Appropriate content and relevance to the video's topic and the energy industry
4. Implements retry logic to handle potential API failures or rate limiting.
5. Updates the database with the review results, including approval status and reasons for rejection if applicable.
6. Truncates long transcripts to fit within the AI model's token limit.
7. Implements logging for monitoring the script's progress and debugging.

The script is designed to be robust, handling various edge cases and potential errors, and is optimized for processing large amounts of data efficiently. It serves as a quality control mechanism to ensure the AI-generated analyses meet the required standards before being used in further applications or research.
"""


# Constants
ROWS_PER_TABLE = 30000
tables_to_process = ["youtube_renewable_energy", "youtube_sustainability", "youtube_startups", "youtube_artificial_intelligence"]
chosen_model = "llama3.1"
MAX_TOKENS = 125000

# Define the prompt templates (keep the existing ones)

# Add a new validation prompt template for reviewing saved responses
review_validation_prompt_template = """
Review the following LLM response for a video transcript analysis. Determine if the response meets all the required criteria:

1. The response is in valid JSON format.
2. It contains exactly three keys: "keywords", "typical_questions", and "key_conclusions".
3. "keywords" is a list of up to 10 relevant keywords.
4. "typical_questions" is a list of 5-10 questions related to the video content.
5. "key_conclusions" is a list of up to 20 key points and conclusions, focusing on specific facts and quantitative data.
6. The content is relevant to the video's main topic and the energy industry.
7. The response is in English.

LLM Response:
{llm_response}

Video Information:
Channel: {channel_name}
Title: {title}
Published Date: {published_at}

Original Transcript:
{transcript}

Provide your response in the following JSON format:
{{
    "Approved": "YES" or "NO",
    "Reason": "Brief explanation of approval or rejection"
}}
"""

# Keep existing functions (validate_ollama_response)




def count_tokens(text):
    encoding = tiktoken.get_encoding("cl100k_base")
    return len(encoding.encode(text))


# Add or modify the truncate_transcript function
def truncate_transcript(transcript, max_tokens):
    encoding = tiktoken.get_encoding("cl100k_base")
    tokens = encoding.encode(transcript)
    if len(tokens) > max_tokens:
        return encoding.decode(tokens[:max_tokens])
    return transcript

@retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=4, max=10))
def review_saved_response(llm_response, channel_name, title, published_at, transcript):
    # Only truncate if the transcript exceeds MAX_TOKENS
    truncated_transcript = truncate_transcript(transcript, MAX_TOKENS) if count_tokens(transcript) > MAX_TOKENS else transcript
    review_prompt = review_validation_prompt_template.format(
        llm_response=llm_response,
        channel_name=channel_name,
        title=title,
        published_at=published_at,
        transcript=truncated_transcript
    )
    try:
        response = requests.post("http://localhost:11434/api/generate", json={
            "model": chosen_model,
            "prompt": review_prompt,
            "format": "json",
            "stream": False
        })
        result = response.json()
        validation_result = json.loads(result['response'])
        print(f"Review result: {validation_result}")
        return validation_result
    except Exception as e:
        print(f"An error occurred while reviewing saved response: {str(e)}")
        raise

def process_saved_response(row, table_name):
    print(f"\nReviewing saved response for video: {row['title']}")
    print(f"Video ID: {row['video_id']}")

    review_result = review_saved_response(
        row['llm_response'],
        row['channel_name'],
        row['title'],
        row['published_at'],
        row['transcript']
    )
    print(f"Review result: {review_result}")

    if review_result["Approved"] == "YES":
        print(f"Saved response for video_id: {row['video_id']} is approved.")
        supabase.table(table_name).update({
            "processed": True
        }).eq("id", row['id']).execute()
    else:
        print(f"Saved response for video_id: {row['video_id']} is not approved. Reprocessing...")
        process_row(row, table_name, reprocess=True)

def process_row(row, table_name, reprocess=False):
    if row['processed'] and not reprocess:
        print(f"Skipping already processed video: {row['title']}")
        return

    if row['transcript'] != "Transcript not available":
        truncated_transcript = truncate_transcript(row['transcript'], MAX_TOKENS) if count_tokens(row['transcript']) > MAX_TOKENS else row['transcript']
        prompt = prompt_template.format(
            channel_name=row['channel_name'],
            title=row['title'],
            published_at=row['published_at'],
            transcript=truncated_transcript
        )
        
        print(f"\nProcessing video: {row['title']}")
        print(f"Video ID: {row['video_id']}")
        
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                llm_response = query_ollama(prompt)
                current_datetime = datetime.now().isoformat()
                
                try:
                    json.loads(llm_response)
                    is_valid_json = True
                except json.JSONDecodeError:
                    is_valid_json = False
                    print(f"Warning: Response for video {row['video_id']} is not valid JSON")

                if is_valid_json:
                    validation_result = validate_ollama_response(llm_response, row['channel_name'], row['title'], row['published_at'])
                    
                    if validation_result["Approved"] == "YES":
                        supabase.table(table_name).update({
                            "llm_response": llm_response,
                            "llm_call_date": current_datetime,
                            "processed": True,
                            "review_result": json.dumps(validation_result)
                        }).eq("id", row['id']).execute()
                        print(f"Successfully processed video_id: {row['video_id']}")
                        return
                    else:
                        print(f"Response not approved for video_id: {row['video_id']}. Retrying...")
                else:
                    print(f"Invalid JSON response for video_id: {row['video_id']}. Retrying...")
            except Exception as e:
                print(f"Error processing video_id: {row['video_id']}. Attempt {attempt + 1}. Error: {str(e)}")
                if attempt == max_attempts - 1:
                    current_datetime = datetime.now().isoformat()
                    supabase.table(table_name).update({
                        "llm_response": f"Error: Failed to generate a valid response after {max_attempts} attempts",
                        "llm_call_date": current_datetime,
                        "processed": False,
                        "review_result": None
                    }).eq("id", row['id']).execute()
                    print(f"Failed to process video_id: {row['video_id']} after {max_attempts} attempts")
                    return
        
        # If we've exhausted all attempts
        supabase.table(table_name).update({
            "llm_response": "Error: Failed to generate an approved response after multiple attempts",
            "llm_call_date": current_datetime,
            "processed": False,
            "review_result": None
        }).eq("id", row['id']).execute()
        print(f"Failed to process video_id: {row['video_id']} after {max_attempts} attempts")

@retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=4, max=10))
def validate_ollama_response(llm_response, channel_name, title, published_at):
    validation_prompt = validation_prompt_template.format(
        llm_response=llm_response,
        channel_name=channel_name,
        title=title,
        published_at=published_at
    )
    try:
        response = requests.post("http://localhost:11434/api/generate", json={
            "model": validator_model,
            "prompt": validation_prompt,
            "format": "json",
            "stream": False
        })
        result = response.json()
        validation_result = json.loads(result['response'])
        print(f"Validation result: {validation_result}")
        return validation_result
    except Exception as e:
        print(f"An error occurred while validating Ollama response: {str(e)}")
        raise

@retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=4, max=10))
def query_ollama(prompt):
    try:
        response = requests.post("http://localhost:11434/api/generate", json={
            "model": chosen_model,
            "prompt": prompt,
            "format": "json",
            "stream": False
        })
        result = response.json()
        return result['response']
    except Exception as e:
        print(f"An error occurred while querying Ollama: {str(e)}")
        raise

def process_table(table_name):
    print(f"Processing table: {table_name}")
    page_size = 1000
    offset = 0
    processed_count = 0
    
    while True:
        response = supabase.table(table_name).select("*").range(offset, offset + page_size - 1).execute()
        rows = response.data
        
        if not rows:
            break
        
        for row in rows:
            if row['llm_response'] and row['llm_response'] != "Error: Failed to generate an approved response after multiple attempts":
                process_saved_response(row, table_name)
            else:
                process_row(row, table_name)
            processed_count += 1
            print(f"Processed {processed_count} rows. Immediately processing next row...")
        
        offset += len(rows)
        
        if len(rows) < page_size or (ROWS_PER_TABLE != -1 and offset >= ROWS_PER_TABLE):
            break
    
    print(f"Finished processing {processed_count} rows in table: {table_name}")

def main():
    start_time = time.time()
    
    for table_name in tables_to_process:
        table_start_time = time.time()
        process_table(table_name)
        table_end_time = time.time()
        table_time = table_end_time - table_start_time
        print(f"Time taken for table {table_name}: {table_time:.2f} seconds")
    
    end_time = time.time()
    total_time = end_time - start_time
    print(f"\nTotal time taken: {total_time:.2f} seconds")

if __name__ == "__main__":
    main()
