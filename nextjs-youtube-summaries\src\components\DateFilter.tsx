'use client'

import { useState } from 'react'
import { Calendar, X } from 'lucide-react'

interface DateFilterProps {
  startDate: string
  endDate: string
  onDateChange: (startDate: string, endDate: string) => void
  onClear: () => void
}

export function DateFilter({ startDate, endDate, onDateChange, onClear }: DateFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const handleStartDateChange = (date: string) => {
    onDateChange(date, endDate)
  }

  const handleEndDateChange = (date: string) => {
    onDateChange(startDate, date)
  }

  const getPresetDates = (days: number) => {
    const end = new Date()
    const start = new Date()
    start.setDate(start.getDate() - days)
    
    return {
      start: start.toISOString().split('T')[0],
      end: end.toISOString().split('T')[0]
    }
  }

  const applyPreset = (days: number) => {
    const { start, end } = getPresetDates(days)
    onDateChange(start, end)
  }

  const hasDateFilter = startDate || endDate

  return (
    <div className="relative">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors ${
          hasDateFilter 
            ? 'bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-900 dark:border-blue-700 dark:text-blue-300'
            : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700'
        }`}
      >
        <Calendar className="w-4 h-4" />
        <span className="text-sm font-medium">
          {hasDateFilter ? 'Date Filter Active' : 'Filter by Date'}
        </span>        {hasDateFilter && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              onClear()
            }}
            className="ml-1 hover:bg-blue-100 dark:hover:bg-blue-800 rounded p-1"
            title="Clear date filter"
            aria-label="Clear date filter"
          >
            <X className="w-3 h-3" />
          </button>
        )}
      </button>

      {isExpanded && (
        <div className="absolute top-full left-0 mt-2 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10 min-w-80">
          <div className="space-y-4">
            <h3 className="font-medium text-gray-900 dark:text-gray-100">Filter by Date Range</h3>
            
            {/* Preset buttons */}
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => applyPreset(7)}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded"
              >
                Last 7 days
              </button>
              <button
                onClick={() => applyPreset(30)}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded"
              >
                Last 30 days
              </button>
              <button
                onClick={() => applyPreset(90)}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded"
              >
                Last 3 months
              </button>
              <button
                onClick={() => applyPreset(365)}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded"
              >
                Last year
              </button>
            </div>

            {/* Custom date inputs */}
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Start Date
                </label>                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => handleStartDateChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  title="Select start date"
                  placeholder="Start date"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  End Date
                </label>                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => handleEndDateChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  title="Select end date"
                  placeholder="End date"
                />
              </div>
            </div>

            {/* Clear and Close buttons */}
            <div className="flex justify-between pt-2">
              <button
                onClick={onClear}
                className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                Clear filters
              </button>
              <button
                onClick={() => setIsExpanded(false)}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
