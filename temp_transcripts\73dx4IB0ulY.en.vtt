WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:01.790 align:start position:0%
 
hey<00:00:00.160><c> and</c><00:00:00.240><c> welcome</c><00:00:00.520><c> back</c><00:00:00.640><c> and</c><00:00:00.760><c> today</c><00:00:00.960><c> is</c><00:00:01.160><c> day</c><00:00:01.400><c> 28</c>

00:00:01.790 --> 00:00:01.800 align:start position:0%
hey and welcome back and today is day 28
 

00:00:01.800 --> 00:00:03.669 align:start position:0%
hey and welcome back and today is day 28
of<00:00:01.880><c> the</c><00:00:02.000><c> 31-day</c><00:00:02.480><c> challenge</c><00:00:03.040><c> and</c><00:00:03.159><c> today</c><00:00:03.399><c> is</c>

00:00:03.669 --> 00:00:03.679 align:start position:0%
of the 31-day challenge and today is
 

00:00:03.679 --> 00:00:04.749 align:start position:0%
of the 31-day challenge and today is
really<00:00:03.919><c> exciting</c><00:00:04.319><c> because</c><00:00:04.480><c> I'm</c><00:00:04.600><c> going</c><00:00:04.680><c> to</c>

00:00:04.749 --> 00:00:04.759 align:start position:0%
really exciting because I'm going to
 

00:00:04.759 --> 00:00:06.789 align:start position:0%
really exciting because I'm going to
show<00:00:04.920><c> you</c><00:00:05.040><c> how</c><00:00:05.160><c> to</c><00:00:05.279><c> use</c><00:00:05.520><c> cloud</c><00:00:05.879><c> 3</c><00:00:06.240><c> local</c><00:00:06.720><c> I'm</c>

00:00:06.789 --> 00:00:06.799 align:start position:0%
show you how to use cloud 3 local I'm
 

00:00:06.799 --> 00:00:07.389 align:start position:0%
show you how to use cloud 3 local I'm
going<00:00:06.879><c> to</c><00:00:06.919><c> show</c><00:00:07.000><c> you</c><00:00:07.040><c> how</c><00:00:07.120><c> to</c><00:00:07.160><c> use</c><00:00:07.279><c> their</c>

00:00:07.389 --> 00:00:07.399 align:start position:0%
going to show you how to use their
 

00:00:07.399 --> 00:00:08.790 align:start position:0%
going to show you how to use their
workbench<00:00:07.799><c> online</c><00:00:08.120><c> so</c><00:00:08.240><c> you</c><00:00:08.280><c> can</c><00:00:08.360><c> use</c><00:00:08.559><c> their</c>

00:00:08.790 --> 00:00:08.800 align:start position:0%
workbench online so you can use their
 

00:00:08.800 --> 00:00:10.749 align:start position:0%
workbench online so you can use their
second<00:00:09.080><c> most</c><00:00:09.280><c> intelligent</c><00:00:09.719><c> version</c><00:00:10.120><c> for</c><00:00:10.320><c> free</c>

00:00:10.749 --> 00:00:10.759 align:start position:0%
second most intelligent version for free
 

00:00:10.759 --> 00:00:12.870 align:start position:0%
second most intelligent version for free
how<00:00:10.840><c> to</c><00:00:11.000><c> generate</c><00:00:11.320><c> an</c><00:00:11.440><c> API</c><00:00:11.920><c> key</c><00:00:12.480><c> and</c><00:00:12.599><c> then</c><00:00:12.759><c> how</c>

00:00:12.870 --> 00:00:12.880 align:start position:0%
how to generate an API key and then how
 

00:00:12.880 --> 00:00:15.230 align:start position:0%
how to generate an API key and then how
to<00:00:13.120><c> use</c><00:00:13.320><c> it</c><00:00:13.559><c> locally</c><00:00:14.200><c> and</c><00:00:14.400><c> then</c><00:00:14.719><c> finally</c>

00:00:15.230 --> 00:00:15.240 align:start position:0%
to use it locally and then finally
 

00:00:15.240 --> 00:00:17.269 align:start position:0%
to use it locally and then finally
integrate<00:00:15.679><c> it</c><00:00:15.920><c> with</c><00:00:16.199><c> autogen</c><00:00:16.880><c> we</c><00:00:16.960><c> have</c><00:00:17.039><c> a</c><00:00:17.119><c> few</c>

00:00:17.269 --> 00:00:17.279 align:start position:0%
integrate it with autogen we have a few
 

00:00:17.279 --> 00:00:18.870 align:start position:0%
integrate it with autogen we have a few
things<00:00:17.400><c> to</c><00:00:17.520><c> do</c><00:00:17.720><c> so</c><00:00:18.039><c> let's</c><00:00:18.240><c> get</c><00:00:18.400><c> started</c><00:00:18.800><c> well</c>

00:00:18.870 --> 00:00:18.880 align:start position:0%
things to do so let's get started well
 

00:00:18.880 --> 00:00:19.670 align:start position:0%
things to do so let's get started well
the<00:00:18.960><c> first</c><00:00:19.119><c> thing</c><00:00:19.240><c> I</c><00:00:19.279><c> want</c><00:00:19.359><c> to</c><00:00:19.439><c> show</c><00:00:19.560><c> you</c>

00:00:19.670 --> 00:00:19.680 align:start position:0%
the first thing I want to show you
 

00:00:19.680 --> 00:00:22.269 align:start position:0%
the first thing I want to show you
really<00:00:19.880><c> quick</c><00:00:20.320><c> is</c><00:00:20.480><c> if</c><00:00:20.560><c> you</c><00:00:20.680><c> go</c><00:00:20.800><c> to</c><00:00:21.199><c> claw.</c><00:00:22.199><c> you</c>

00:00:22.269 --> 00:00:22.279 align:start position:0%
really quick is if you go to claw. you
 

00:00:22.279 --> 00:00:24.390 align:start position:0%
really quick is if you go to claw. you
can<00:00:22.439><c> use</c><00:00:22.680><c> their</c><00:00:22.960><c> second</c><00:00:23.279><c> most</c><00:00:23.560><c> intelligent</c>

00:00:24.390 --> 00:00:24.400 align:start position:0%
can use their second most intelligent
 

00:00:24.400 --> 00:00:26.790 align:start position:0%
can use their second most intelligent
model<00:00:25.039><c> called</c><00:00:25.359><c> clae</c><00:00:25.720><c> 3</c><00:00:26.039><c> Sonet</c><00:00:26.480><c> if</c><00:00:26.519><c> you</c><00:00:26.599><c> want</c><00:00:26.720><c> to</c>

00:00:26.790 --> 00:00:26.800 align:start position:0%
model called clae 3 Sonet if you want to
 

00:00:26.800 --> 00:00:29.150 align:start position:0%
model called clae 3 Sonet if you want to
use<00:00:27.000><c> Opus</c><00:00:27.519><c> then</c><00:00:27.720><c> you</c><00:00:27.960><c> have</c><00:00:28.119><c> to</c><00:00:28.400><c> upgrade</c><00:00:28.720><c> to</c><00:00:28.840><c> CLA</c>

00:00:29.150 --> 00:00:29.160 align:start position:0%
use Opus then you have to upgrade to CLA
 

00:00:29.160 --> 00:00:31.269 align:start position:0%
use Opus then you have to upgrade to CLA
Pro<00:00:29.679><c> but</c><00:00:30.039><c> just</c><00:00:30.240><c> use</c><00:00:30.439><c> son</c><00:00:30.679><c> it</c><00:00:30.800><c> it's</c><00:00:30.880><c> free</c><00:00:31.119><c> and</c>

00:00:31.269 --> 00:00:31.279 align:start position:0%
Pro but just use son it it's free and
 

00:00:31.279 --> 00:00:32.749 align:start position:0%
Pro but just use son it it's free and
this<00:00:31.359><c> is</c><00:00:31.480><c> your</c><00:00:31.599><c> first</c><00:00:31.840><c> time</c><00:00:32.040><c> here</c><00:00:32.360><c> it'll</c><00:00:32.599><c> ask</c>

00:00:32.749 --> 00:00:32.759 align:start position:0%
this is your first time here it'll ask
 

00:00:32.759 --> 00:00:35.110 align:start position:0%
this is your first time here it'll ask
you<00:00:32.880><c> to</c><00:00:33.079><c> sign</c><00:00:33.320><c> up</c><00:00:33.800><c> it's</c><00:00:33.960><c> free</c><00:00:34.320><c> just</c><00:00:34.719><c> sign</c><00:00:34.960><c> in</c>

00:00:35.110 --> 00:00:35.120 align:start position:0%
you to sign up it's free just sign in
 

00:00:35.120 --> 00:00:37.030 align:start position:0%
you to sign up it's free just sign in
with<00:00:35.280><c> some</c><00:00:35.520><c> email</c><00:00:36.040><c> and</c><00:00:36.160><c> then</c><00:00:36.280><c> once</c><00:00:36.440><c> you</c><00:00:36.680><c> sign</c>

00:00:37.030 --> 00:00:37.040 align:start position:0%
with some email and then once you sign
 

00:00:37.040 --> 00:00:38.590 align:start position:0%
with some email and then once you sign
in<00:00:37.280><c> you'll</c><00:00:37.480><c> be</c><00:00:37.600><c> greeted</c><00:00:37.960><c> with</c><00:00:38.079><c> this</c><00:00:38.200><c> screen</c><00:00:38.480><c> so</c>

00:00:38.590 --> 00:00:38.600 align:start position:0%
in you'll be greeted with this screen so
 

00:00:38.600 --> 00:00:39.830 align:start position:0%
in you'll be greeted with this screen so
I'm<00:00:38.680><c> just</c><00:00:38.760><c> going</c><00:00:38.840><c> to</c><00:00:38.960><c> ask</c><00:00:39.079><c> it</c><00:00:39.239><c> to</c><00:00:39.440><c> write</c><00:00:39.680><c> a</c>

00:00:39.830 --> 00:00:39.840 align:start position:0%
I'm just going to ask it to write a
 

00:00:39.840 --> 00:00:41.709 align:start position:0%
I'm just going to ask it to write a
snake<00:00:40.239><c> game</c><00:00:40.520><c> in</c><00:00:40.760><c> Python</c><00:00:41.200><c> and</c><00:00:41.320><c> then</c><00:00:41.480><c> it's</c><00:00:41.600><c> going</c>

00:00:41.709 --> 00:00:41.719 align:start position:0%
snake game in Python and then it's going
 

00:00:41.719 --> 00:00:44.110 align:start position:0%
snake game in Python and then it's going
to<00:00:42.000><c> go</c><00:00:42.160><c> ahead</c><00:00:42.600><c> and</c><00:00:42.920><c> come</c><00:00:43.160><c> here</c><00:00:43.480><c> and</c><00:00:43.760><c> write</c><00:00:44.000><c> it</c>

00:00:44.110 --> 00:00:44.120 align:start position:0%
to go ahead and come here and write it
 

00:00:44.120 --> 00:00:46.029 align:start position:0%
to go ahead and come here and write it
for<00:00:44.280><c> me</c><00:00:44.559><c> now</c><00:00:44.719><c> the</c><00:00:44.800><c> only</c><00:00:45.079><c> thing</c><00:00:45.320><c> here</c><00:00:45.760><c> is</c><00:00:45.960><c> it</c>

00:00:46.029 --> 00:00:46.039 align:start position:0%
for me now the only thing here is it
 

00:00:46.039 --> 00:00:47.549 align:start position:0%
for me now the only thing here is it
doesn't<00:00:46.239><c> have</c><00:00:46.360><c> the</c><00:00:46.480><c> ability</c><00:00:46.800><c> to</c><00:00:47.160><c> actually</c>

00:00:47.549 --> 00:00:47.559 align:start position:0%
doesn't have the ability to actually
 

00:00:47.559 --> 00:00:49.910 align:start position:0%
doesn't have the ability to actually
execute<00:00:48.039><c> the</c><00:00:48.199><c> code</c><00:00:48.440><c> that</c><00:00:48.559><c> is</c><00:00:48.719><c> generating</c><00:00:49.559><c> so</c>

00:00:49.910 --> 00:00:49.920 align:start position:0%
execute the code that is generating so
 

00:00:49.920 --> 00:00:51.310 align:start position:0%
execute the code that is generating so
you<00:00:50.000><c> would</c><00:00:50.199><c> have</c><00:00:50.280><c> to</c><00:00:50.520><c> copy</c><00:00:50.840><c> this</c><00:00:51.039><c> and</c><00:00:51.120><c> then</c>

00:00:51.310 --> 00:00:51.320 align:start position:0%
you would have to copy this and then
 

00:00:51.320 --> 00:00:52.750 align:start position:0%
you would have to copy this and then
test<00:00:51.520><c> it</c><00:00:51.719><c> locally</c><00:00:52.079><c> to</c><00:00:52.199><c> see</c><00:00:52.320><c> if</c><00:00:52.399><c> it</c><00:00:52.520><c> actually</c>

00:00:52.750 --> 00:00:52.760 align:start position:0%
test it locally to see if it actually
 

00:00:52.760 --> 00:00:54.349 align:start position:0%
test it locally to see if it actually
worked<00:00:53.320><c> and</c><00:00:53.399><c> then</c><00:00:53.520><c> if</c><00:00:53.600><c> it</c><00:00:53.680><c> didn't</c><00:00:53.879><c> you</c><00:00:54.160><c> come</c>

00:00:54.349 --> 00:00:54.359 align:start position:0%
worked and then if it didn't you come
 

00:00:54.359 --> 00:00:55.709 align:start position:0%
worked and then if it didn't you come
back<00:00:54.640><c> and</c><00:00:54.800><c> you</c><00:00:54.920><c> know</c><00:00:55.120><c> tell</c><00:00:55.280><c> it</c><00:00:55.440><c> what</c><00:00:55.559><c> didn't</c>

00:00:55.709 --> 00:00:55.719 align:start position:0%
back and you know tell it what didn't
 

00:00:55.719 --> 00:00:57.470 align:start position:0%
back and you know tell it what didn't
work<00:00:55.879><c> and</c><00:00:56.000><c> so</c><00:00:56.160><c> forth</c><00:00:56.399><c> kind</c><00:00:56.520><c> of</c><00:00:56.640><c> like</c><00:00:56.760><c> chat</c><00:00:56.960><c> GPT</c>

00:00:57.470 --> 00:00:57.480 align:start position:0%
work and so forth kind of like chat GPT
 

00:00:57.480 --> 00:00:58.869 align:start position:0%
work and so forth kind of like chat GPT
but<00:00:57.600><c> this</c><00:00:57.719><c> is</c><00:00:57.840><c> just</c><00:00:58.000><c> clawed</c><00:00:58.399><c> and</c><00:00:58.519><c> great</c><00:00:58.760><c> it</c>

00:00:58.869 --> 00:00:58.879 align:start position:0%
but this is just clawed and great it
 

00:00:58.879 --> 00:01:00.790 align:start position:0%
but this is just clawed and great it
worked<00:00:59.239><c> this</c><00:00:59.359><c> is</c><00:00:59.519><c> just</c><00:01:00.039><c> another</c><00:01:00.320><c> way</c><00:01:00.480><c> to</c><00:01:00.640><c> talk</c>

00:01:00.790 --> 00:01:00.800 align:start position:0%
worked this is just another way to talk
 

00:01:00.800 --> 00:01:02.750 align:start position:0%
worked this is just another way to talk
to<00:01:00.960><c> CLA</c><00:01:01.239><c> 3</c><00:01:01.760><c> now</c><00:01:02.000><c> let's</c><00:01:02.160><c> see</c><00:01:02.280><c> how</c><00:01:02.399><c> we</c><00:01:02.519><c> do</c><00:01:02.640><c> it</c>

00:01:02.750 --> 00:01:02.760 align:start position:0%
to CLA 3 now let's see how we do it
 

00:01:02.760 --> 00:01:04.189 align:start position:0%
to CLA 3 now let's see how we do it
locally<00:01:03.239><c> on</c><00:01:03.320><c> the</c><00:01:03.440><c> top</c><00:01:03.640><c> right</c><00:01:03.800><c> here</c><00:01:03.920><c> if</c><00:01:04.040><c> you</c>

00:01:04.189 --> 00:01:04.199 align:start position:0%
locally on the top right here if you
 

00:01:04.199 --> 00:01:07.310 align:start position:0%
locally on the top right here if you
click<00:01:04.519><c> your</c><00:01:05.040><c> account</c><00:01:05.640><c> you</c><00:01:05.799><c> go</c><00:01:05.960><c> to</c><00:01:06.240><c> API</c><00:01:06.799><c> console</c>

00:01:07.310 --> 00:01:07.320 align:start position:0%
click your account you go to API console
 

00:01:07.320 --> 00:01:09.190 align:start position:0%
click your account you go to API console
and<00:01:07.479><c> now</c><00:01:07.600><c> you'll</c><00:01:07.759><c> see</c><00:01:08.000><c> a</c><00:01:08.200><c> different</c><00:01:08.520><c> screen</c>

00:01:09.190 --> 00:01:09.200 align:start position:0%
and now you'll see a different screen
 

00:01:09.200 --> 00:01:11.190 align:start position:0%
and now you'll see a different screen
and<00:01:09.439><c> from</c><00:01:09.720><c> what</c><00:01:09.920><c> I've</c><00:01:10.080><c> seen</c><00:01:10.400><c> I've</c><00:01:10.640><c> watched</c><00:01:10.920><c> a</c>

00:01:11.190 --> 00:01:11.200 align:start position:0%
and from what I've seen I've watched a
 

00:01:11.200 --> 00:01:12.870 align:start position:0%
and from what I've seen I've watched a
couple<00:01:11.520><c> videos</c><00:01:11.840><c> and</c><00:01:12.119><c> some</c><00:01:12.280><c> of</c><00:01:12.360><c> the</c><00:01:12.520><c> research</c>

00:01:12.870 --> 00:01:12.880 align:start position:0%
couple videos and some of the research
 

00:01:12.880 --> 00:01:14.910 align:start position:0%
couple videos and some of the research
I've<00:01:13.000><c> done</c><00:01:13.200><c> online</c><00:01:13.880><c> I</c><00:01:14.040><c> tried</c><00:01:14.240><c> to</c><00:01:14.320><c> find</c><00:01:14.479><c> a</c><00:01:14.640><c> way</c>

00:01:14.910 --> 00:01:14.920 align:start position:0%
I've done online I tried to find a way
 

00:01:14.920 --> 00:01:17.749 align:start position:0%
I've done online I tried to find a way
where<00:01:15.320><c> we</c><00:01:15.400><c> could</c><00:01:15.560><c> do</c><00:01:15.759><c> this</c><00:01:16.080><c> completely</c><00:01:16.920><c> free</c>

00:01:17.749 --> 00:01:17.759 align:start position:0%
where we could do this completely free
 

00:01:17.759 --> 00:01:19.270 align:start position:0%
where we could do this completely free
with<00:01:17.920><c> one</c><00:01:18.040><c> of</c><00:01:18.159><c> the</c><00:01:18.360><c> versions</c><00:01:19.000><c> however</c><00:01:19.119><c> if</c><00:01:19.200><c> you</c>

00:01:19.270 --> 00:01:19.280 align:start position:0%
with one of the versions however if you
 

00:01:19.280 --> 00:01:21.149 align:start position:0%
with one of the versions however if you
want<00:01:19.360><c> to</c><00:01:19.439><c> use</c><00:01:19.600><c> the</c><00:01:19.759><c> API</c><00:01:20.240><c> locally</c><00:01:20.880><c> with</c><00:01:21.000><c> an</c>

00:01:21.149 --> 00:01:21.159 align:start position:0%
want to use the API locally with an
 

00:01:21.159 --> 00:01:23.789 align:start position:0%
want to use the API locally with an
agent<00:01:21.479><c> or</c><00:01:21.840><c> just</c><00:01:22.079><c> by</c><00:01:22.280><c> itself</c><00:01:23.040><c> uh</c><00:01:23.159><c> Standalone</c>

00:01:23.789 --> 00:01:23.799 align:start position:0%
agent or just by itself uh Standalone
 

00:01:23.799 --> 00:01:26.870 align:start position:0%
agent or just by itself uh Standalone
they<00:01:24.000><c> do</c><00:01:24.240><c> give</c><00:01:24.360><c> you</c><00:01:24.600><c> $5</c><00:01:25.320><c> of</c><00:01:25.520><c> free</c><00:01:25.960><c> credit</c><00:01:26.560><c> for</c>

00:01:26.870 --> 00:01:26.880 align:start position:0%
they do give you $5 of free credit for
 

00:01:26.880 --> 00:01:28.510 align:start position:0%
they do give you $5 of free credit for
each<00:01:27.119><c> email</c><00:01:27.439><c> that</c><00:01:27.560><c> you</c><00:01:27.640><c> sign</c><00:01:27.840><c> up</c><00:01:28.040><c> with</c><00:01:28.240><c> and</c><00:01:28.360><c> the</c>

00:01:28.510 --> 00:01:28.520 align:start position:0%
each email that you sign up with and the
 

00:01:28.520 --> 00:01:30.190 align:start position:0%
each email that you sign up with and the
subscriptions<00:01:29.000><c> are</c><00:01:29.159><c> monthly</c><00:01:29.600><c> or</c><00:01:29.759><c> you</c><00:01:30.040><c> can</c>

00:01:30.190 --> 00:01:30.200 align:start position:0%
subscriptions are monthly or you can
 

00:01:30.200 --> 00:01:31.550 align:start position:0%
subscriptions are monthly or you can
prepaid<00:01:30.680><c> like</c><00:01:30.840><c> kind</c><00:01:30.960><c> of</c><00:01:31.000><c> like</c><00:01:31.119><c> you</c><00:01:31.280><c> do</c><00:01:31.439><c> with</c>

00:01:31.550 --> 00:01:31.560 align:start position:0%
prepaid like kind of like you do with
 

00:01:31.560 --> 00:01:33.270 align:start position:0%
prepaid like kind of like you do with
chat<00:01:31.759><c> GPT</c><00:01:32.320><c> so</c><00:01:32.439><c> if</c><00:01:32.479><c> you</c><00:01:32.600><c> click</c><00:01:32.759><c> on</c><00:01:32.880><c> your</c><00:01:33.000><c> account</c>

00:01:33.270 --> 00:01:33.280 align:start position:0%
chat GPT so if you click on your account
 

00:01:33.280 --> 00:01:35.630 align:start position:0%
chat GPT so if you click on your account
again<00:01:33.720><c> and</c><00:01:33.880><c> then</c><00:01:34.119><c> go</c><00:01:34.280><c> to</c><00:01:34.600><c> plans</c><00:01:35.000><c> and</c><00:01:35.200><c> billing</c>

00:01:35.630 --> 00:01:35.640 align:start position:0%
again and then go to plans and billing
 

00:01:35.640 --> 00:01:37.550 align:start position:0%
again and then go to plans and billing
you'll<00:01:35.840><c> get</c><00:01:35.960><c> your</c><00:01:36.119><c> $5</c><00:01:36.799><c> of</c><00:01:36.920><c> free</c><00:01:37.200><c> credit</c><00:01:37.479><c> that</c>

00:01:37.550 --> 00:01:37.560 align:start position:0%
you'll get your $5 of free credit that
 

00:01:37.560 --> 00:01:40.710 align:start position:0%
you'll get your $5 of free credit that
you<00:01:37.640><c> can</c><00:01:37.799><c> claim</c><00:01:38.159><c> here</c><00:01:38.640><c> okay</c><00:01:38.920><c> now</c><00:01:39.119><c> I</c><00:01:39.240><c> have</c><00:01:39.479><c> $5</c><00:01:40.399><c> of</c>

00:01:40.710 --> 00:01:40.720 align:start position:0%
you can claim here okay now I have $5 of
 

00:01:40.720 --> 00:01:42.950 align:start position:0%
you can claim here okay now I have $5 of
credit<00:01:41.280><c> remaining</c><00:01:41.960><c> let's</c><00:01:42.240><c> try</c><00:01:42.399><c> and</c><00:01:42.600><c> test</c><00:01:42.840><c> this</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
credit remaining let's try and test this
 

00:01:42.960 --> 00:01:44.350 align:start position:0%
credit remaining let's try and test this
locally<00:01:43.560><c> so</c><00:01:43.720><c> the</c><00:01:43.799><c> first</c><00:01:43.960><c> thing</c><00:01:44.040><c> you</c><00:01:44.119><c> want</c><00:01:44.240><c> to</c>

00:01:44.350 --> 00:01:44.360 align:start position:0%
locally so the first thing you want to
 

00:01:44.360 --> 00:01:45.870 align:start position:0%
locally so the first thing you want to
do<00:01:44.520><c> is</c><00:01:44.680><c> go</c><00:01:44.840><c> to</c><00:01:45.000><c> their</c><00:01:45.200><c> docs</c><00:01:45.560><c> which</c><00:01:45.680><c> you</c><00:01:45.719><c> can</c>

00:01:45.870 --> 00:01:45.880 align:start position:0%
do is go to their docs which you can
 

00:01:45.880 --> 00:01:47.190 align:start position:0%
do is go to their docs which you can
find<00:01:46.000><c> at</c><00:01:46.079><c> the</c><00:01:46.240><c> top</c><00:01:46.439><c> right</c><00:01:46.640><c> here</c><00:01:47.000><c> then</c><00:01:47.119><c> they</c>

00:01:47.190 --> 00:01:47.200 align:start position:0%
find at the top right here then they
 

00:01:47.200 --> 00:01:48.830 align:start position:0%
find at the top right here then they
have<00:01:47.280><c> a</c><00:01:47.399><c> quick</c><00:01:47.680><c> start</c><00:01:47.840><c> guide</c><00:01:48.119><c> so</c><00:01:48.399><c> if</c><00:01:48.479><c> I</c><00:01:48.640><c> come</c>

00:01:48.830 --> 00:01:48.840 align:start position:0%
have a quick start guide so if I come
 

00:01:48.840 --> 00:01:51.310 align:start position:0%
have a quick start guide so if I come
down<00:01:49.040><c> here</c><00:01:49.200><c> to</c><00:01:49.360><c> send</c><00:01:49.600><c> your</c><00:01:49.799><c> first</c><00:01:50.159><c> API</c><00:01:50.680><c> request</c>

00:01:51.310 --> 00:01:51.320 align:start position:0%
down here to send your first API request
 

00:01:51.320 --> 00:01:53.389 align:start position:0%
down here to send your first API request
I'm<00:01:51.439><c> essentially</c><00:01:51.880><c> just</c><00:01:52.000><c> going</c><00:01:52.119><c> to</c><00:01:52.399><c> copy</c><00:01:52.719><c> this</c>

00:01:53.389 --> 00:01:53.399 align:start position:0%
I'm essentially just going to copy this
 

00:01:53.399 --> 00:01:54.870 align:start position:0%
I'm essentially just going to copy this
put<00:01:53.520><c> it</c><00:01:53.640><c> into</c><00:01:53.799><c> a</c><00:01:53.960><c> python</c><00:01:54.320><c> file</c><00:01:54.560><c> and</c><00:01:54.680><c> then</c><00:01:54.759><c> we're</c>

00:01:54.870 --> 00:01:54.880 align:start position:0%
put it into a python file and then we're
 

00:01:54.880 --> 00:01:56.149 align:start position:0%
put it into a python file and then we're
going<00:01:54.960><c> to</c><00:01:55.079><c> try</c><00:01:55.240><c> and</c><00:01:55.360><c> run</c><00:01:55.520><c> it</c><00:01:55.759><c> all</c><00:01:55.840><c> right</c><00:01:55.920><c> so</c><00:01:56.039><c> I</c>

00:01:56.149 --> 00:01:56.159 align:start position:0%
going to try and run it all right so I
 

00:01:56.159 --> 00:01:58.109 align:start position:0%
going to try and run it all right so I
copy<00:01:56.360><c> and</c><00:01:56.479><c> pasted</c><00:01:56.880><c> here</c><00:01:57.399><c> the</c><00:01:57.680><c> first</c><00:01:57.920><c> thing</c>

00:01:58.109 --> 00:01:58.119 align:start position:0%
copy and pasted here the first thing
 

00:01:58.119 --> 00:01:59.029 align:start position:0%
copy and pasted here the first thing
well<00:01:58.280><c> the</c><00:01:58.320><c> only</c><00:01:58.479><c> thing</c><00:01:58.560><c> you</c><00:01:58.640><c> really</c><00:01:58.759><c> need</c><00:01:58.920><c> to</c>

00:01:59.029 --> 00:01:59.039 align:start position:0%
well the only thing you really need to
 

00:01:59.039 --> 00:02:01.270 align:start position:0%
well the only thing you really need to
do<00:01:59.280><c> is</c><00:01:59.479><c> pip</c><00:01:59.680><c> and</c><00:02:00.200><c> anthropic</c><00:02:00.799><c> and</c><00:02:00.920><c> then</c><00:02:01.079><c> once</c>

00:02:01.270 --> 00:02:01.280 align:start position:0%
do is pip and anthropic and then once
 

00:02:01.280 --> 00:02:02.870 align:start position:0%
do is pip and anthropic and then once
that's<00:02:01.520><c> done</c><00:02:01.680><c> we're</c><00:02:01.880><c> ready</c><00:02:02.039><c> to</c><00:02:02.200><c> go</c><00:02:02.520><c> now</c><00:02:02.680><c> at</c><00:02:02.799><c> the</c>

00:02:02.870 --> 00:02:02.880 align:start position:0%
that's done we're ready to go now at the
 

00:02:02.880 --> 00:02:04.630 align:start position:0%
that's done we're ready to go now at the
time<00:02:03.000><c> of</c><00:02:03.119><c> this</c><00:02:03.280><c> video</c><00:02:03.640><c> the</c><00:02:03.840><c> versions</c><00:02:04.320><c> of</c><00:02:04.479><c> the</c>

00:02:04.630 --> 00:02:04.640 align:start position:0%
time of this video the versions of the
 

00:02:04.640 --> 00:02:06.749 align:start position:0%
time of this video the versions of the
models<00:02:05.119><c> might</c><00:02:05.360><c> change</c><00:02:05.840><c> so</c><00:02:06.119><c> if</c><00:02:06.240><c> you</c><00:02:06.360><c> want</c><00:02:06.560><c> to</c>

00:02:06.749 --> 00:02:06.759 align:start position:0%
models might change so if you want to
 

00:02:06.759 --> 00:02:08.630 align:start position:0%
models might change so if you want to
know<00:02:07.039><c> exactly</c><00:02:07.439><c> Which</c><00:02:07.640><c> models</c><00:02:08.000><c> you</c><00:02:08.119><c> can</c><00:02:08.280><c> choose</c>

00:02:08.630 --> 00:02:08.640 align:start position:0%
know exactly Which models you can choose
 

00:02:08.640 --> 00:02:11.150 align:start position:0%
know exactly Which models you can choose
from<00:02:09.319><c> if</c><00:02:09.440><c> you</c><00:02:09.679><c> go</c><00:02:09.879><c> into</c><00:02:10.160><c> the</c><00:02:10.399><c> create</c><00:02:10.800><c> method</c>

00:02:11.150 --> 00:02:11.160 align:start position:0%
from if you go into the create method
 

00:02:11.160 --> 00:02:14.110 align:start position:0%
from if you go into the create method
here<00:02:11.319><c> of</c><00:02:11.480><c> the</c><00:02:11.920><c> client</c><00:02:12.920><c> right</c><00:02:13.160><c> here</c><00:02:13.440><c> it</c><00:02:13.720><c> has</c><00:02:14.000><c> all</c>

00:02:14.110 --> 00:02:14.120 align:start position:0%
here of the client right here it has all
 

00:02:14.120 --> 00:02:15.910 align:start position:0%
here of the client right here it has all
the<00:02:14.280><c> models</c><00:02:14.680><c> that</c><00:02:14.800><c> you</c><00:02:14.920><c> can</c><00:02:15.080><c> choose</c><00:02:15.400><c> from</c><00:02:15.640><c> so</c>

00:02:15.910 --> 00:02:15.920 align:start position:0%
the models that you can choose from so
 

00:02:15.920 --> 00:02:17.670 align:start position:0%
the models that you can choose from so
actually<00:02:16.120><c> want</c><00:02:16.239><c> to</c><00:02:16.400><c> choose</c><00:02:16.959><c> the</c><00:02:17.160><c> Sonet</c>

00:02:17.670 --> 00:02:17.680 align:start position:0%
actually want to choose the Sonet
 

00:02:17.680 --> 00:02:19.790 align:start position:0%
actually want to choose the Sonet
because<00:02:18.160><c> I</c><00:02:18.280><c> know</c><00:02:18.760><c> that</c><00:02:18.920><c> it's</c><00:02:19.120><c> cheaper</c><00:02:19.640><c> than</c>

00:02:19.790 --> 00:02:19.800 align:start position:0%
because I know that it's cheaper than
 

00:02:19.800 --> 00:02:22.070 align:start position:0%
because I know that it's cheaper than
the<00:02:19.920><c> opus</c><00:02:20.640><c> okay</c><00:02:20.959><c> now</c><00:02:21.160><c> that</c><00:02:21.319><c> we</c><00:02:21.519><c> have</c><00:02:21.760><c> all</c><00:02:21.920><c> this</c>

00:02:22.070 --> 00:02:22.080 align:start position:0%
the opus okay now that we have all this
 

00:02:22.080 --> 00:02:24.869 align:start position:0%
the opus okay now that we have all this
set<00:02:22.280><c> up</c><00:02:22.480><c> let's</c><00:02:22.680><c> just</c><00:02:22.879><c> run</c><00:02:23.120><c> it</c><00:02:23.760><c> okay</c><00:02:24.120><c> cool</c><00:02:24.519><c> well</c>

00:02:24.869 --> 00:02:24.879 align:start position:0%
set up let's just run it okay cool well
 

00:02:24.879 --> 00:02:26.670 align:start position:0%
set up let's just run it okay cool well
it<00:02:25.000><c> finished</c><00:02:25.360><c> that</c><00:02:25.480><c> was</c><00:02:25.680><c> really</c><00:02:25.959><c> quick</c><00:02:26.360><c> so</c><00:02:26.599><c> it</c>

00:02:26.670 --> 00:02:26.680 align:start position:0%
it finished that was really quick so it
 

00:02:26.680 --> 00:02:28.990 align:start position:0%
it finished that was really quick so it
looks<00:02:26.840><c> like</c><00:02:26.959><c> it</c><00:02:27.040><c> has</c><00:02:27.160><c> a</c><00:02:27.440><c> Content</c><00:02:27.959><c> block</c><00:02:28.599><c> object</c>

00:02:28.990 --> 00:02:29.000 align:start position:0%
looks like it has a Content block object
 

00:02:29.000 --> 00:02:31.229 align:start position:0%
looks like it has a Content block object
with<00:02:29.120><c> a</c><00:02:29.280><c> text</c><00:02:30.000><c> says</c><00:02:30.239><c> feeling</c><00:02:30.599><c> well</c><00:02:30.879><c> grateful</c>

00:02:31.229 --> 00:02:31.239 align:start position:0%
with a text says feeling well grateful
 

00:02:31.239 --> 00:02:32.949 align:start position:0%
with a text says feeling well grateful
for<00:02:31.400><c> your</c><00:02:31.560><c> inquiry</c><00:02:32.000><c> I</c><00:02:32.120><c> am</c><00:02:32.239><c> so</c><00:02:32.400><c> it</c><00:02:32.480><c> talks</c><00:02:32.760><c> like</c>

00:02:32.949 --> 00:02:32.959 align:start position:0%
for your inquiry I am so it talks like
 

00:02:32.959 --> 00:02:35.670 align:start position:0%
for your inquiry I am so it talks like
Yoda<00:02:33.599><c> Okay</c><00:02:34.000><c> cool</c><00:02:34.480><c> so</c><00:02:34.680><c> now</c><00:02:34.800><c> I</c><00:02:34.920><c> know</c><00:02:35.080><c> I</c><00:02:35.160><c> can</c><00:02:35.400><c> get</c>

00:02:35.670 --> 00:02:35.680 align:start position:0%
Yoda Okay cool so now I know I can get
 

00:02:35.680 --> 00:02:37.430 align:start position:0%
Yoda Okay cool so now I know I can get
the<00:02:36.120><c> because</c><00:02:36.280><c> this</c><00:02:36.400><c> is</c><00:02:36.519><c> an</c><00:02:36.680><c> array</c><00:02:37.120><c> I</c><00:02:37.200><c> know</c><00:02:37.360><c> I</c>

00:02:37.430 --> 00:02:37.440 align:start position:0%
the because this is an array I know I
 

00:02:37.440 --> 00:02:39.750 align:start position:0%
the because this is an array I know I
can<00:02:37.560><c> get</c><00:02:37.680><c> the</c><00:02:38.040><c> first</c><00:02:38.760><c> uh</c><00:02:38.959><c> item</c><00:02:39.200><c> on</c><00:02:39.319><c> the</c><00:02:39.440><c> array</c>

00:02:39.750 --> 00:02:39.760 align:start position:0%
can get the first uh item on the array
 

00:02:39.760 --> 00:02:42.869 align:start position:0%
can get the first uh item on the array
and<00:02:39.840><c> then</c><00:02:40.400><c> text</c><00:02:41.120><c> okay</c><00:02:41.280><c> so</c><00:02:41.640><c> now</c><00:02:41.959><c> I</c><00:02:42.120><c> tried</c><00:02:42.480><c> it</c>

00:02:42.869 --> 00:02:42.879 align:start position:0%
and then text okay so now I tried it
 

00:02:42.879 --> 00:02:45.550 align:start position:0%
and then text okay so now I tried it
again<00:02:43.680><c> except</c><00:02:44.040><c> this</c><00:02:44.159><c> time</c><00:02:44.319><c> I</c><00:02:44.440><c> said</c><00:02:44.720><c> the</c><00:02:44.959><c> system</c>

00:02:45.550 --> 00:02:45.560 align:start position:0%
again except this time I said the system
 

00:02:45.560 --> 00:02:47.110 align:start position:0%
again except this time I said the system
responded<00:02:46.000><c> if</c><00:02:46.120><c> you</c><00:02:46.239><c> were</c><00:02:46.400><c> a</c><00:02:46.599><c> professional</c>

00:02:47.110 --> 00:02:47.120 align:start position:0%
responded if you were a professional
 

00:02:47.120 --> 00:02:49.390 align:start position:0%
responded if you were a professional
engineer<00:02:47.920><c> and</c><00:02:48.080><c> create</c><00:02:48.360><c> a</c><00:02:48.480><c> simple</c><00:02:48.800><c> game</c><00:02:49.000><c> of</c><00:02:49.159><c> tic</c>

00:02:49.390 --> 00:02:49.400 align:start position:0%
engineer and create a simple game of tic
 

00:02:49.400 --> 00:02:51.229 align:start position:0%
engineer and create a simple game of tic
tac<00:02:49.560><c> toe</c><00:02:49.840><c> for</c><00:02:50.000><c> me</c><00:02:50.159><c> in</c><00:02:50.400><c> Python</c><00:02:50.879><c> now</c><00:02:51.000><c> remember</c>

00:02:51.229 --> 00:02:51.239 align:start position:0%
tac toe for me in Python now remember
 

00:02:51.239 --> 00:02:52.390 align:start position:0%
tac toe for me in Python now remember
this<00:02:51.360><c> doesn't</c><00:02:51.599><c> actually</c><00:02:51.840><c> execute</c><00:02:52.159><c> it</c><00:02:52.280><c> this</c>

00:02:52.390 --> 00:02:52.400 align:start position:0%
this doesn't actually execute it this
 

00:02:52.400 --> 00:02:54.110 align:start position:0%
this doesn't actually execute it this
just<00:02:52.519><c> gives</c><00:02:52.680><c> you</c><00:02:52.800><c> the</c><00:02:52.959><c> code</c><00:02:53.400><c> but</c><00:02:53.720><c> it</c><00:02:53.920><c> went</c>

00:02:54.110 --> 00:02:54.120 align:start position:0%
just gives you the code but it went
 

00:02:54.120 --> 00:02:56.190 align:start position:0%
just gives you the code but it went
ahead<00:02:54.400><c> and</c><00:02:54.640><c> here</c><00:02:54.879><c> is</c><00:02:55.120><c> all</c><00:02:55.280><c> the</c><00:02:55.440><c> code</c><00:02:55.959><c> that</c><00:02:56.080><c> it</c>

00:02:56.190 --> 00:02:56.200 align:start position:0%
ahead and here is all the code that it
 

00:02:56.200 --> 00:02:57.949 align:start position:0%
ahead and here is all the code that it
printed<00:02:56.440><c> out</c><00:02:56.560><c> for</c><00:02:56.720><c> me</c><00:02:56.840><c> for</c><00:02:57.080><c> a</c><00:02:57.239><c> tic</c><00:02:57.519><c> tac</c><00:02:57.720><c> toe</c>

00:02:57.949 --> 00:02:57.959 align:start position:0%
printed out for me for a tic tac toe
 

00:02:57.959 --> 00:02:59.470 align:start position:0%
printed out for me for a tic tac toe
game<00:02:58.400><c> I'm</c><00:02:58.480><c> just</c><00:02:58.599><c> going</c><00:02:58.680><c> to</c><00:02:58.800><c> copy</c><00:02:59.000><c> and</c><00:02:59.120><c> paste</c><00:02:59.319><c> it</c>

00:02:59.470 --> 00:02:59.480 align:start position:0%
game I'm just going to copy and paste it
 

00:02:59.480 --> 00:03:01.110 align:start position:0%
game I'm just going to copy and paste it
just<00:02:59.640><c> as</c><00:02:59.840><c> to</c><00:02:59.920><c> see</c><00:03:00.040><c> if</c><00:03:00.120><c> it</c><00:03:00.239><c> works</c><00:03:00.720><c> I</c><00:03:00.800><c> mean</c><00:03:00.920><c> to</c><00:03:01.040><c> be</c>

00:03:01.110 --> 00:03:01.120 align:start position:0%
just as to see if it works I mean to be
 

00:03:01.120 --> 00:03:02.949 align:start position:0%
just as to see if it works I mean to be
honest<00:03:01.360><c> with</c><00:03:01.480><c> you</c><00:03:01.640><c> it</c><00:03:01.879><c> worked</c><00:03:02.360><c> on</c><00:03:02.480><c> the</c><00:03:02.680><c> first</c>

00:03:02.949 --> 00:03:02.959 align:start position:0%
honest with you it worked on the first
 

00:03:02.959 --> 00:03:04.869 align:start position:0%
honest with you it worked on the first
try<00:03:03.640><c> right</c><00:03:03.840><c> it</c><00:03:04.000><c> it</c><00:03:04.120><c> just</c><00:03:04.239><c> worked</c><00:03:04.480><c> I</c><00:03:04.560><c> just</c><00:03:04.680><c> copy</c>

00:03:04.869 --> 00:03:04.879 align:start position:0%
try right it it just worked I just copy
 

00:03:04.879 --> 00:03:06.670 align:start position:0%
try right it it just worked I just copy
and<00:03:05.000><c> pasted</c><00:03:05.239><c> the</c><00:03:05.360><c> code</c><00:03:05.599><c> and</c><00:03:05.680><c> it</c><00:03:05.799><c> was</c><00:03:06.000><c> fine</c><00:03:06.480><c> now</c>

00:03:06.670 --> 00:03:06.680 align:start position:0%
and pasted the code and it was fine now
 

00:03:06.680 --> 00:03:08.550 align:start position:0%
and pasted the code and it was fine now
let's<00:03:06.840><c> see</c><00:03:07.000><c> how</c><00:03:07.120><c> much</c><00:03:07.319><c> this</c><00:03:07.599><c> cost</c><00:03:08.120><c> so</c><00:03:08.280><c> you</c><00:03:08.360><c> see</c>

00:03:08.550 --> 00:03:08.560 align:start position:0%
let's see how much this cost so you see
 

00:03:08.560 --> 00:03:10.830 align:start position:0%
let's see how much this cost so you see
here<00:03:08.840><c> after</c><00:03:09.080><c> I</c><00:03:09.239><c> ran</c><00:03:09.760><c> just</c><00:03:10.040><c> I</c><00:03:10.360><c> two</c><00:03:10.519><c> different</c>

00:03:10.830 --> 00:03:10.840 align:start position:0%
here after I ran just I two different
 

00:03:10.840 --> 00:03:12.990 align:start position:0%
here after I ran just I two different
requests<00:03:11.400><c> I'll</c><00:03:11.519><c> look</c><00:03:11.680><c> at</c><00:03:11.840><c> how</c><00:03:11.920><c> many</c><00:03:12.159><c> tokens</c><00:03:12.840><c> I</c>

00:03:12.990 --> 00:03:13.000 align:start position:0%
requests I'll look at how many tokens I
 

00:03:13.000 --> 00:03:14.990 align:start position:0%
requests I'll look at how many tokens I
used<00:03:13.360><c> or</c><00:03:13.560><c> how</c><00:03:13.640><c> many</c><00:03:13.799><c> app</c><00:03:14.080><c> tokens</c><00:03:14.360><c> there</c><00:03:14.480><c> were</c>

00:03:14.990 --> 00:03:15.000 align:start position:0%
used or how many app tokens there were
 

00:03:15.000 --> 00:03:16.589 align:start position:0%
used or how many app tokens there were
but<00:03:15.159><c> this</c><00:03:15.319><c> cost</c><00:03:15.599><c> basically</c><00:03:15.920><c> a</c><00:03:16.120><c> scent</c><00:03:16.400><c> if</c><00:03:16.480><c> you</c>

00:03:16.589 --> 00:03:16.599 align:start position:0%
but this cost basically a scent if you
 

00:03:16.599 --> 00:03:19.589 align:start position:0%
but this cost basically a scent if you
go<00:03:16.720><c> to</c><00:03:16.840><c> the</c><00:03:16.959><c> logs</c><00:03:17.440><c> here</c><00:03:18.040><c> you</c><00:03:18.200><c> can</c><00:03:18.440><c> see</c><00:03:18.879><c> that</c><00:03:19.280><c> the</c>

00:03:19.589 --> 00:03:19.599 align:start position:0%
go to the logs here you can see that the
 

00:03:19.599 --> 00:03:21.830 align:start position:0%
go to the logs here you can see that the
one<00:03:19.760><c> I</c><00:03:19.959><c> just</c><00:03:20.159><c> did</c><00:03:20.319><c> for</c><00:03:20.480><c> the</c><00:03:20.640><c> game</c><00:03:20.799><c> was</c><00:03:20.959><c> 843</c>

00:03:21.830 --> 00:03:21.840 align:start position:0%
one I just did for the game was 843
 

00:03:21.840 --> 00:03:24.190 align:start position:0%
one I just did for the game was 843
tokens<00:03:22.599><c> and</c><00:03:22.760><c> the</c><00:03:22.959><c> first</c><00:03:23.200><c> one</c><00:03:23.360><c> was</c><00:03:23.519><c> 60</c><00:03:23.879><c> so</c>

00:03:24.190 --> 00:03:24.200 align:start position:0%
tokens and the first one was 60 so
 

00:03:24.200 --> 00:03:26.789 align:start position:0%
tokens and the first one was 60 so
almost<00:03:24.599><c> 1,000</c><00:03:25.480><c> well</c><00:03:25.760><c> 900</c><00:03:26.400><c> now</c><00:03:26.519><c> I'm</c><00:03:26.599><c> going</c><00:03:26.720><c> to</c>

00:03:26.789 --> 00:03:26.799 align:start position:0%
almost 1,000 well 900 now I'm going to
 

00:03:26.799 --> 00:03:28.910 align:start position:0%
almost 1,000 well 900 now I'm going to
try<00:03:26.959><c> it</c><00:03:27.080><c> with</c><00:03:27.319><c> Opus</c><00:03:28.000><c> and</c><00:03:28.159><c> see</c><00:03:28.319><c> how</c><00:03:28.480><c> much</c><00:03:28.720><c> that</c>

00:03:28.910 --> 00:03:28.920 align:start position:0%
try it with Opus and see how much that
 

00:03:28.920 --> 00:03:30.390 align:start position:0%
try it with Opus and see how much that
costs<00:03:29.439><c> Okay</c><00:03:29.560><c> so</c><00:03:29.840><c> now</c><00:03:29.920><c> we're</c><00:03:30.040><c> going</c><00:03:30.120><c> to</c><00:03:30.200><c> use</c>

00:03:30.390 --> 00:03:30.400 align:start position:0%
costs Okay so now we're going to use
 

00:03:30.400 --> 00:03:32.910 align:start position:0%
costs Okay so now we're going to use
Opus<00:03:30.959><c> their</c><00:03:31.239><c> best</c><00:03:31.560><c> model</c><00:03:32.120><c> and</c><00:03:32.439><c> and</c><00:03:32.560><c> I</c><00:03:32.640><c> want</c><00:03:32.799><c> it</c>

00:03:32.910 --> 00:03:32.920 align:start position:0%
Opus their best model and and I want it
 

00:03:32.920 --> 00:03:35.390 align:start position:0%
Opus their best model and and I want it
to<00:03:33.200><c> create</c><00:03:33.439><c> a</c><00:03:33.560><c> game</c><00:03:33.720><c> of</c><00:03:33.879><c> snake</c><00:03:34.280><c> for</c><00:03:34.480><c> me</c><00:03:35.040><c> well</c><00:03:35.319><c> I</c>

00:03:35.390 --> 00:03:35.400 align:start position:0%
to create a game of snake for me well I
 

00:03:35.400 --> 00:03:36.470 align:start position:0%
to create a game of snake for me well I
don't<00:03:35.519><c> know</c><00:03:35.640><c> if</c><00:03:35.720><c> you</c><00:03:35.799><c> can</c><00:03:35.959><c> see</c><00:03:36.159><c> this</c><00:03:36.280><c> let</c><00:03:36.400><c> me</c>

00:03:36.470 --> 00:03:36.480 align:start position:0%
don't know if you can see this let me
 

00:03:36.480 --> 00:03:38.710 align:start position:0%
don't know if you can see this let me
move<00:03:36.680><c> out</c><00:03:36.760><c> of</c><00:03:36.840><c> the</c><00:03:36.959><c> way</c><00:03:37.200><c> here</c><00:03:37.879><c> there</c><00:03:38.120><c> is</c><00:03:38.519><c> an</c>

00:03:38.710 --> 00:03:38.720 align:start position:0%
move out of the way here there is an
 

00:03:38.720 --> 00:03:42.190 align:start position:0%
move out of the way here there is an
error<00:03:39.120><c> code</c><00:03:39.439><c> 529</c><00:03:40.400><c> overloaded</c><00:03:41.000><c> error</c><00:03:41.319><c> so</c><00:03:41.640><c> as</c><00:03:41.799><c> of</c>

00:03:42.190 --> 00:03:42.200 align:start position:0%
error code 529 overloaded error so as of
 

00:03:42.200 --> 00:03:43.949 align:start position:0%
error code 529 overloaded error so as of
right<00:03:42.400><c> now</c><00:03:43.080><c> there</c><00:03:43.280><c> that</c><00:03:43.400><c> I</c><00:03:43.480><c> think</c><00:03:43.640><c> this</c><00:03:43.720><c> means</c>

00:03:43.949 --> 00:03:43.959 align:start position:0%
right now there that I think this means
 

00:03:43.959 --> 00:03:47.229 align:start position:0%
right now there that I think this means
that<00:03:44.120><c> their</c><00:03:44.480><c> API</c><00:03:45.480><c> is</c><00:03:45.760><c> overloaded</c><00:03:46.760><c> um</c><00:03:47.000><c> and</c><00:03:47.159><c> I</c>

00:03:47.229 --> 00:03:47.239 align:start position:0%
that their API is overloaded um and I
 

00:03:47.239 --> 00:03:49.229 align:start position:0%
that their API is overloaded um and I
need<00:03:47.400><c> to</c><00:03:47.560><c> wait</c><00:03:47.799><c> until</c><00:03:48.439><c> there's</c><00:03:48.680><c> not</c><00:03:48.879><c> as</c><00:03:49.040><c> many</c>

00:03:49.229 --> 00:03:49.239 align:start position:0%
need to wait until there's not as many
 

00:03:49.239 --> 00:03:52.670 align:start position:0%
need to wait until there's not as many
people<00:03:49.439><c> trying</c><00:03:49.640><c> to</c><00:03:49.799><c> run</c><00:03:50.360><c> the</c><00:03:50.560><c> clae</c><00:03:50.920><c> 3</c><00:03:51.200><c> Opus</c><00:03:51.879><c> API</c>

00:03:52.670 --> 00:03:52.680 align:start position:0%
people trying to run the clae 3 Opus API
 

00:03:52.680 --> 00:03:54.630 align:start position:0%
people trying to run the clae 3 Opus API
so<00:03:52.920><c> to</c><00:03:53.079><c> see</c><00:03:53.280><c> how</c><00:03:53.400><c> much</c><00:03:53.519><c> it</c><00:03:53.680><c> actually</c><00:03:53.959><c> costs</c><00:03:54.519><c> I</c>

00:03:54.630 --> 00:03:54.640 align:start position:0%
so to see how much it actually costs I
 

00:03:54.640 --> 00:03:55.990 align:start position:0%
so to see how much it actually costs I
guess<00:03:54.799><c> we're</c><00:03:54.959><c> going</c><00:03:55.079><c> to</c><00:03:55.200><c> have</c><00:03:55.319><c> to</c><00:03:55.480><c> wait</c>

00:03:55.990 --> 00:03:56.000 align:start position:0%
guess we're going to have to wait
 

00:03:56.000 --> 00:03:57.990 align:start position:0%
guess we're going to have to wait
however<00:03:56.239><c> let's</c><00:03:56.400><c> now</c><00:03:56.560><c> run</c><00:03:56.760><c> an</c><00:03:56.920><c> autogen</c><00:03:57.400><c> example</c>

00:03:57.990 --> 00:03:58.000 align:start position:0%
however let's now run an autogen example
 

00:03:58.000 --> 00:03:59.470 align:start position:0%
however let's now run an autogen example
all<00:03:58.120><c> right</c><00:03:58.319><c> now</c><00:03:58.439><c> let's</c><00:03:58.599><c> see</c><00:03:58.760><c> how</c><00:03:58.920><c> we</c><00:03:59.079><c> integrate</c>

00:03:59.470 --> 00:03:59.480 align:start position:0%
all right now let's see how we integrate
 

00:03:59.480 --> 00:04:01.910 align:start position:0%
all right now let's see how we integrate
it<00:03:59.599><c> with</c><00:03:59.799><c> with</c><00:03:59.959><c> autogen</c><00:04:00.519><c> to</c><00:04:00.720><c> work</c><00:04:01.079><c> locally</c>

00:04:01.910 --> 00:04:01.920 align:start position:0%
it with with autogen to work locally
 

00:04:01.920 --> 00:04:03.630 align:start position:0%
it with with autogen to work locally
well<00:04:02.120><c> the</c><00:04:02.239><c> first</c><00:04:02.400><c> thing</c><00:04:02.519><c> I</c><00:04:02.640><c> do</c><00:04:02.879><c> is</c><00:04:03.000><c> I</c><00:04:03.159><c> just</c><00:04:03.519><c> have</c>

00:04:03.630 --> 00:04:03.640 align:start position:0%
well the first thing I do is I just have
 

00:04:03.640 --> 00:04:05.229 align:start position:0%
well the first thing I do is I just have
a<00:04:03.760><c> couple</c><00:04:04.000><c> Imports</c><00:04:04.439><c> here</c><00:04:04.640><c> where</c><00:04:04.799><c> I</c><00:04:04.920><c> just</c><00:04:05.040><c> have</c>

00:04:05.229 --> 00:04:05.239 align:start position:0%
a couple Imports here where I just have
 

00:04:05.239 --> 00:04:07.069 align:start position:0%
a couple Imports here where I just have
the<00:04:05.319><c> assistant</c><00:04:05.680><c> agent</c><00:04:05.959><c> and</c><00:04:06.120><c> user</c><00:04:06.439><c> proxy</c><00:04:06.840><c> from</c>

00:04:07.069 --> 00:04:07.079 align:start position:0%
the assistant agent and user proxy from
 

00:04:07.079 --> 00:04:09.030 align:start position:0%
the assistant agent and user proxy from
autogen<00:04:08.040><c> the</c><00:04:08.159><c> only</c><00:04:08.319><c> thing</c><00:04:08.480><c> we</c><00:04:08.599><c> really</c><00:04:08.760><c> need</c><00:04:08.879><c> to</c>

00:04:09.030 --> 00:04:09.040 align:start position:0%
autogen the only thing we really need to
 

00:04:09.040 --> 00:04:11.750 align:start position:0%
autogen the only thing we really need to
do<00:04:09.400><c> is</c><00:04:09.599><c> set</c><00:04:09.879><c> the</c><00:04:10.159><c> anthropic</c><00:04:10.920><c> key</c><00:04:11.280><c> here</c><00:04:11.640><c> and</c>

00:04:11.750 --> 00:04:11.760 align:start position:0%
do is set the anthropic key here and
 

00:04:11.760 --> 00:04:14.509 align:start position:0%
do is set the anthropic key here and
then<00:04:11.920><c> I</c><00:04:12.000><c> have</c><00:04:12.120><c> an</c><00:04:12.280><c> llm</c><00:04:12.760><c> config</c><00:04:13.360><c> called</c><00:04:13.680><c> Claude</c>

00:04:14.509 --> 00:04:14.519 align:start position:0%
then I have an llm config called Claude
 

00:04:14.519 --> 00:04:17.469 align:start position:0%
then I have an llm config called Claude
where<00:04:14.799><c> the</c><00:04:15.000><c> model</c><00:04:15.640><c> I</c><00:04:15.920><c> give</c><00:04:16.199><c> the</c><00:04:16.400><c> actual</c><00:04:17.000><c> uh</c><00:04:17.120><c> CLA</c>

00:04:17.469 --> 00:04:17.479 align:start position:0%
where the model I give the actual uh CLA
 

00:04:17.479 --> 00:04:19.749 align:start position:0%
where the model I give the actual uh CLA
model<00:04:17.759><c> that</c><00:04:17.880><c> I</c><00:04:17.959><c> want</c><00:04:18.079><c> to</c><00:04:18.239><c> use</c><00:04:18.680><c> the</c><00:04:18.799><c> base</c><00:04:19.040><c> URL</c><00:04:19.600><c> is</c>

00:04:19.749 --> 00:04:19.759 align:start position:0%
model that I want to use the base URL is
 

00:04:19.759 --> 00:04:22.069 align:start position:0%
model that I want to use the base URL is
essentially<00:04:20.199><c> Local</c><00:04:20.560><c> Host</c><00:04:20.880><c> 4000</c><00:04:21.880><c> that's</c>

00:04:22.069 --> 00:04:22.079 align:start position:0%
essentially Local Host 4000 that's
 

00:04:22.079 --> 00:04:23.909 align:start position:0%
essentially Local Host 4000 that's
because<00:04:22.560><c> we're</c><00:04:22.720><c> going</c><00:04:22.840><c> to</c><00:04:23.000><c> be</c><00:04:23.160><c> integrating</c>

00:04:23.909 --> 00:04:23.919 align:start position:0%
because we're going to be integrating
 

00:04:23.919 --> 00:04:27.230 align:start position:0%
because we're going to be integrating
light<00:04:24.320><c> llm</c><00:04:25.320><c> to</c><00:04:25.520><c> run</c><00:04:25.800><c> a</c><00:04:25.960><c> local</c><00:04:26.320><c> server</c><00:04:26.919><c> so</c><00:04:27.120><c> that</c>

00:04:27.230 --> 00:04:27.240 align:start position:0%
light llm to run a local server so that
 

00:04:27.240 --> 00:04:29.790 align:start position:0%
light llm to run a local server so that
can<00:04:27.440><c> conform</c><00:04:28.000><c> to</c><00:04:28.160><c> the</c><00:04:28.280><c> open</c><00:04:28.600><c> ai's</c><00:04:29.039><c> API</c><00:04:29.479><c> which</c>

00:04:29.790 --> 00:04:29.800 align:start position:0%
can conform to the open ai's API which
 

00:04:29.800 --> 00:04:31.990 align:start position:0%
can conform to the open ai's API which
we<00:04:29.960><c> need</c><00:04:30.240><c> with</c><00:04:30.400><c> autogen</c><00:04:31.000><c> add</c><00:04:31.160><c> the</c><00:04:31.280><c> API</c><00:04:31.720><c> type</c>

00:04:31.990 --> 00:04:32.000 align:start position:0%
we need with autogen add the API type
 

00:04:32.000 --> 00:04:33.990 align:start position:0%
we need with autogen add the API type
and<00:04:32.160><c> the</c><00:04:32.320><c> API</c><00:04:32.800><c> key</c><00:04:33.160><c> again</c><00:04:33.400><c> here</c><00:04:33.720><c> again</c><00:04:33.880><c> we're</c>

00:04:33.990 --> 00:04:34.000 align:start position:0%
and the API key again here again we're
 

00:04:34.000 --> 00:04:36.469 align:start position:0%
and the API key again here again we're
not<00:04:34.160><c> using</c><00:04:34.479><c> open</c><00:04:34.759><c> ai's</c><00:04:35.280><c> API</c><00:04:36.120><c> but</c><00:04:36.240><c> you</c><00:04:36.360><c> just</c>

00:04:36.469 --> 00:04:36.479 align:start position:0%
not using open ai's API but you just
 

00:04:36.479 --> 00:04:38.110 align:start position:0%
not using open ai's API but you just
need<00:04:36.639><c> to</c><00:04:36.800><c> have</c><00:04:37.039><c> something</c><00:04:37.320><c> here</c><00:04:37.479><c> for</c><00:04:37.680><c> the</c><00:04:37.800><c> key</c>

00:04:38.110 --> 00:04:38.120 align:start position:0%
need to have something here for the key
 

00:04:38.120 --> 00:04:39.950 align:start position:0%
need to have something here for the key
cash<00:04:38.360><c> she</c><00:04:38.520><c> is</c><00:04:38.639><c> set</c><00:04:38.800><c> to</c><00:04:38.919><c> none</c><00:04:39.360><c> I</c><00:04:39.440><c> have</c><00:04:39.560><c> a</c><00:04:39.680><c> simple</c>

00:04:39.950 --> 00:04:39.960 align:start position:0%
cash she is set to none I have a simple
 

00:04:39.960 --> 00:04:41.670 align:start position:0%
cash she is set to none I have a simple
assistant<00:04:40.360><c> agent</c><00:04:40.600><c> using</c><00:04:40.880><c> the</c><00:04:41.000><c> cloud</c><00:04:41.280><c> LM</c>

00:04:41.670 --> 00:04:41.680 align:start position:0%
assistant agent using the cloud LM
 

00:04:41.680 --> 00:04:43.629 align:start position:0%
assistant agent using the cloud LM
config<00:04:42.080><c> that</c><00:04:42.199><c> we</c><00:04:42.360><c> just</c><00:04:42.560><c> created</c><00:04:43.280><c> I</c><00:04:43.400><c> have</c><00:04:43.520><c> a</c>

00:04:43.629 --> 00:04:43.639 align:start position:0%
config that we just created I have a
 

00:04:43.639 --> 00:04:46.150 align:start position:0%
config that we just created I have a
user<00:04:43.960><c> proxy</c><00:04:44.320><c> agent</c><00:04:44.840><c> we</c><00:04:45.000><c> do</c><00:04:45.199><c> need</c><00:04:45.440><c> to</c><00:04:45.680><c> set</c><00:04:45.919><c> a</c>

00:04:46.150 --> 00:04:46.160 align:start position:0%
user proxy agent we do need to set a
 

00:04:46.160 --> 00:04:48.310 align:start position:0%
user proxy agent we do need to set a
default<00:04:46.759><c> auto</c><00:04:47.120><c> reply</c><00:04:47.479><c> for</c><00:04:47.680><c> the</c><00:04:47.800><c> user</c><00:04:48.120><c> if</c><00:04:48.240><c> you</c>

00:04:48.310 --> 00:04:48.320 align:start position:0%
default auto reply for the user if you
 

00:04:48.320 --> 00:04:49.790 align:start position:0%
default auto reply for the user if you
don't<00:04:48.600><c> that</c><00:04:48.720><c> you'll</c><00:04:48.880><c> get</c><00:04:48.960><c> an</c><00:04:49.080><c> error</c><00:04:49.360><c> message</c>

00:04:49.790 --> 00:04:49.800 align:start position:0%
don't that you'll get an error message
 

00:04:49.800 --> 00:04:53.189 align:start position:0%
don't that you'll get an error message
that<00:04:50.000><c> anthropic</c><00:04:50.759><c> requires</c><00:04:51.520><c> you</c><00:04:51.680><c> to</c><00:04:52.000><c> have</c><00:04:52.360><c> some</c>

00:04:53.189 --> 00:04:53.199 align:start position:0%
that anthropic requires you to have some
 

00:04:53.199 --> 00:04:55.469 align:start position:0%
that anthropic requires you to have some
or<00:04:53.400><c> non</c><00:04:53.759><c> empty</c><00:04:54.240><c> content</c><00:04:54.600><c> message</c><00:04:55.080><c> similar</c><00:04:55.360><c> to</c>

00:04:55.469 --> 00:04:55.479 align:start position:0%
or non empty content message similar to
 

00:04:55.479 --> 00:04:57.230 align:start position:0%
or non empty content message similar to
when<00:04:55.600><c> you're</c><00:04:55.639><c> using</c><00:04:55.880><c> LM</c><00:04:56.199><c> Studio</c><00:04:56.759><c> you</c><00:04:56.880><c> need</c><00:04:57.039><c> to</c>

00:04:57.230 --> 00:04:57.240 align:start position:0%
when you're using LM Studio you need to
 

00:04:57.240 --> 00:04:59.189 align:start position:0%
when you're using LM Studio you need to
have<00:04:57.440><c> some</c><00:04:57.759><c> content</c><00:04:58.080><c> in</c><00:04:58.240><c> there</c><00:04:58.440><c> with</c><00:04:58.560><c> a</c><00:04:58.919><c> auto</c>

00:04:59.189 --> 00:04:59.199 align:start position:0%
have some content in there with a auto
 

00:04:59.199 --> 00:05:01.350 align:start position:0%
have some content in there with a auto
reply<00:04:59.479><c> for</c><00:04:59.720><c> from</c><00:04:59.800><c> the</c><00:04:59.919><c> user</c><00:05:00.479><c> and</c><00:05:00.560><c> then</c><00:05:00.800><c> similar</c>

00:05:01.350 --> 00:05:01.360 align:start position:0%
reply for from the user and then similar
 

00:05:01.360 --> 00:05:03.590 align:start position:0%
reply for from the user and then similar
to<00:05:01.759><c> a</c><00:05:01.840><c> couple</c><00:05:02.120><c> days</c><00:05:02.320><c> ago</c><00:05:02.680><c> maybe</c><00:05:02.919><c> yesterday</c><00:05:03.400><c> the</c>

00:05:03.590 --> 00:05:03.600 align:start position:0%
to a couple days ago maybe yesterday the
 

00:05:03.600 --> 00:05:05.510 align:start position:0%
to a couple days ago maybe yesterday the
video<00:05:04.000><c> where</c><00:05:04.199><c> we</c><00:05:04.360><c> just</c><00:05:04.520><c> have</c><00:05:04.680><c> it</c><00:05:04.840><c> tell</c><00:05:05.039><c> a</c><00:05:05.199><c> joke</c>

00:05:05.510 --> 00:05:05.520 align:start position:0%
video where we just have it tell a joke
 

00:05:05.520 --> 00:05:07.550 align:start position:0%
video where we just have it tell a joke
with<00:05:05.680><c> computers</c><00:05:06.440><c> okay</c><00:05:06.600><c> we're</c><00:05:06.759><c> not</c><00:05:07.199><c> quite</c>

00:05:07.550 --> 00:05:07.560 align:start position:0%
with computers okay we're not quite
 

00:05:07.560 --> 00:05:11.110 align:start position:0%
with computers okay we're not quite
there<00:05:07.919><c> yet</c><00:05:08.919><c> because</c><00:05:09.600><c> this</c><00:05:09.919><c> URL</c><00:05:10.680><c> this</c><00:05:10.759><c> is</c><00:05:11.000><c> kind</c>

00:05:11.110 --> 00:05:11.120 align:start position:0%
there yet because this URL this is kind
 

00:05:11.120 --> 00:05:13.029 align:start position:0%
there yet because this URL this is kind
of<00:05:11.320><c> what</c><00:05:11.479><c> was</c><00:05:11.800><c> really</c><00:05:12.039><c> needed</c><00:05:12.400><c> to</c><00:05:12.560><c> make</c><00:05:12.800><c> this</c>

00:05:13.029 --> 00:05:13.039 align:start position:0%
of what was really needed to make this
 

00:05:13.039 --> 00:05:14.950 align:start position:0%
of what was really needed to make this
work<00:05:13.479><c> is</c><00:05:13.639><c> we're</c><00:05:13.800><c> going</c><00:05:13.919><c> to</c><00:05:14.000><c> be</c><00:05:14.120><c> using</c><00:05:14.520><c> light</c>

00:05:14.950 --> 00:05:14.960 align:start position:0%
work is we're going to be using light
 

00:05:14.960 --> 00:05:17.710 align:start position:0%
work is we're going to be using light
llm<00:05:15.960><c> now</c><00:05:16.160><c> I</c><00:05:16.240><c> will</c><00:05:16.440><c> have</c><00:05:16.759><c> requirements</c><00:05:17.479><c> here</c>

00:05:17.710 --> 00:05:17.720 align:start position:0%
llm now I will have requirements here
 

00:05:17.720 --> 00:05:19.469 align:start position:0%
llm now I will have requirements here
because<00:05:18.280><c> there</c><00:05:18.440><c> has</c><00:05:18.639><c> been</c><00:05:18.880><c> updates</c><00:05:19.280><c> if</c><00:05:19.400><c> you</c>

00:05:19.469 --> 00:05:19.479 align:start position:0%
because there has been updates if you
 

00:05:19.479 --> 00:05:21.590 align:start position:0%
because there has been updates if you
used<00:05:19.759><c> light</c><00:05:19.960><c> LM</c><00:05:20.360><c> before</c><00:05:20.960><c> there</c><00:05:21.120><c> have</c><00:05:21.280><c> been</c>

00:05:21.590 --> 00:05:21.600 align:start position:0%
used light LM before there have been
 

00:05:21.600 --> 00:05:23.309 align:start position:0%
used light LM before there have been
updates<00:05:22.080><c> and</c><00:05:22.199><c> it</c><00:05:22.319><c> wasn't</c><00:05:22.639><c> working</c><00:05:22.919><c> for</c><00:05:23.080><c> me</c>

00:05:23.309 --> 00:05:23.319 align:start position:0%
updates and it wasn't working for me
 

00:05:23.319 --> 00:05:25.629 align:start position:0%
updates and it wasn't working for me
until<00:05:24.000><c> I</c><00:05:24.160><c> got</c><00:05:24.319><c> the</c><00:05:24.479><c> new</c><00:05:24.759><c> updates</c><00:05:25.240><c> so</c><00:05:25.400><c> they</c><00:05:25.520><c> have</c>

00:05:25.629 --> 00:05:25.639 align:start position:0%
until I got the new updates so they have
 

00:05:25.639 --> 00:05:27.790 align:start position:0%
until I got the new updates so they have
a<00:05:25.800><c> new</c><00:05:26.000><c> way</c><00:05:26.280><c> to</c><00:05:26.560><c> run</c><00:05:26.840><c> the</c><00:05:26.960><c> server</c><00:05:27.560><c> and</c><00:05:27.680><c> they</c>

00:05:27.790 --> 00:05:27.800 align:start position:0%
a new way to run the server and they
 

00:05:27.800 --> 00:05:29.909 align:start position:0%
a new way to run the server and they
have<00:05:27.880><c> a</c><00:05:28.080><c> new</c><00:05:28.400><c> install</c><00:05:28.840><c> package</c><00:05:29.280><c> to</c><00:05:29.440><c> use</c><00:05:29.720><c> use</c>

00:05:29.909 --> 00:05:29.919 align:start position:0%
have a new install package to use use
 

00:05:29.919 --> 00:05:31.430 align:start position:0%
have a new install package to use use
for<00:05:30.160><c> python</c><00:05:30.720><c> all</c><00:05:30.800><c> right</c><00:05:30.919><c> so</c><00:05:31.080><c> the</c><00:05:31.199><c> last</c><00:05:31.319><c> thing</c>

00:05:31.430 --> 00:05:31.440 align:start position:0%
for python all right so the last thing
 

00:05:31.440 --> 00:05:33.430 align:start position:0%
for python all right so the last thing
you<00:05:31.520><c> need</c><00:05:31.639><c> to</c><00:05:31.800><c> do</c><00:05:32.160><c> is</c><00:05:32.360><c> open</c><00:05:32.560><c> up</c><00:05:32.680><c> your</c><00:05:32.800><c> terminal</c>

00:05:33.430 --> 00:05:33.440 align:start position:0%
you need to do is open up your terminal
 

00:05:33.440 --> 00:05:36.469 align:start position:0%
you need to do is open up your terminal
and<00:05:33.639><c> type</c><00:05:33.840><c> in</c><00:05:34.160><c> light</c><00:05:34.520><c> llm</c><00:05:35.199><c> space</c><00:05:36.120><c> and</c><00:05:36.199><c> then</c><00:05:36.319><c> for</c>

00:05:36.469 --> 00:05:36.479 align:start position:0%
and type in light llm space and then for
 

00:05:36.479 --> 00:05:39.390 align:start position:0%
and type in light llm space and then for
the<00:05:36.639><c> model</c><00:05:37.080><c> we</c><00:05:37.160><c> want</c><00:05:37.319><c> to</c><00:05:37.440><c> use</c><00:05:37.800><c> Claude</c><00:05:38.240><c> 3</c><00:05:38.680><c> Sonet</c>

00:05:39.390 --> 00:05:39.400 align:start position:0%
the model we want to use Claude 3 Sonet
 

00:05:39.400 --> 00:05:41.830 align:start position:0%
the model we want to use Claude 3 Sonet
okay<00:05:39.639><c> and</c><00:05:39.759><c> then</c><00:05:39.919><c> once</c><00:05:40.080><c> you</c><00:05:40.280><c> press</c><00:05:40.680><c> enter</c><00:05:41.680><c> it's</c>

00:05:41.830 --> 00:05:41.840 align:start position:0%
okay and then once you press enter it's
 

00:05:41.840 --> 00:05:44.029 align:start position:0%
okay and then once you press enter it's
going<00:05:41.919><c> to</c><00:05:42.199><c> run</c><00:05:42.560><c> a</c><00:05:42.720><c> local</c><00:05:43.080><c> server</c><00:05:43.479><c> for</c><00:05:43.680><c> you</c>

00:05:44.029 --> 00:05:44.039 align:start position:0%
going to run a local server for you
 

00:05:44.039 --> 00:05:46.270 align:start position:0%
going to run a local server for you
using<00:05:44.400><c> light</c><00:05:44.720><c> llms</c><00:05:45.360><c> wrapper</c><00:05:45.840><c> and</c><00:05:45.960><c> as</c><00:05:46.039><c> you</c><00:05:46.160><c> can</c>

00:05:46.270 --> 00:05:46.280 align:start position:0%
using light llms wrapper and as you can
 

00:05:46.280 --> 00:05:49.629 align:start position:0%
using light llms wrapper and as you can
see<00:05:46.560><c> here</c><00:05:46.720><c> it's</c><00:05:46.880><c> running</c><00:05:47.319><c> on</c><00:05:47.560><c> Local</c><00:05:48.000><c> Host</c><00:05:48.639><c> 4000</c>

00:05:49.629 --> 00:05:49.639 align:start position:0%
see here it's running on Local Host 4000
 

00:05:49.639 --> 00:05:50.950 align:start position:0%
see here it's running on Local Host 4000
that's<00:05:49.800><c> what</c><00:05:49.919><c> we're</c><00:05:50.080><c> putting</c><00:05:50.400><c> in</c><00:05:50.600><c> here</c><00:05:50.800><c> for</c>

00:05:50.950 --> 00:05:50.960 align:start position:0%
that's what we're putting in here for
 

00:05:50.960 --> 00:05:53.749 align:start position:0%
that's what we're putting in here for
the<00:05:51.160><c> base</c><00:05:51.400><c> URL</c><00:05:51.759><c> to</c><00:05:51.880><c> get</c><00:05:52.039><c> this</c><00:05:52.160><c> to</c><00:05:52.319><c> run</c><00:05:53.160><c> now</c>

00:05:53.749 --> 00:05:53.759 align:start position:0%
the base URL to get this to run now
 

00:05:53.759 --> 00:05:55.430 align:start position:0%
the base URL to get this to run now
let's<00:05:53.960><c> just</c><00:05:54.080><c> run</c><00:05:54.280><c> it</c><00:05:54.400><c> to</c><00:05:54.520><c> see</c><00:05:54.680><c> if</c><00:05:54.759><c> it</c><00:05:54.880><c> works</c><00:05:55.319><c> so</c>

00:05:55.430 --> 00:05:55.440 align:start position:0%
let's just run it to see if it works so
 

00:05:55.440 --> 00:05:58.469 align:start position:0%
let's just run it to see if it works so
I'm<00:05:55.520><c> going</c><00:05:55.639><c> to</c><00:05:55.720><c> right</c><00:05:55.880><c> click</c><00:05:56.080><c> on</c><00:05:56.240><c> Main</c><00:05:57.000><c> hit</c><00:05:57.479><c> run</c>

00:05:58.469 --> 00:05:58.479 align:start position:0%
I'm going to right click on Main hit run
 

00:05:58.479 --> 00:06:00.230 align:start position:0%
I'm going to right click on Main hit run
and<00:05:58.680><c> I'll</c><00:05:58.840><c> just</c><00:05:58.960><c> do</c><00:05:59.120><c> this</c><00:05:59.319><c> live</c><00:05:59.800><c> with</c><00:05:59.919><c> you</c><00:06:00.080><c> so</c>

00:06:00.230 --> 00:06:00.240 align:start position:0%
and I'll just do this live with you so
 

00:06:00.240 --> 00:06:01.909 align:start position:0%
and I'll just do this live with you so
that<00:06:00.360><c> you</c><00:06:00.479><c> can</c><00:06:00.720><c> see</c><00:06:01.039><c> that</c><00:06:01.160><c> it's</c><00:06:01.440><c> actually</c>

00:06:01.909 --> 00:06:01.919 align:start position:0%
that you can see that it's actually
 

00:06:01.919 --> 00:06:04.390 align:start position:0%
that you can see that it's actually
working<00:06:02.560><c> so</c><00:06:02.800><c> we</c><00:06:03.000><c> get</c><00:06:03.440><c> the</c><00:06:03.560><c> user</c><00:06:03.880><c> proxy</c><00:06:04.160><c> to</c><00:06:04.280><c> the</c>

00:06:04.390 --> 00:06:04.400 align:start position:0%
working so we get the user proxy to the
 

00:06:04.400 --> 00:06:06.710 align:start position:0%
working so we get the user proxy to the
assistant<00:06:04.800><c> tell</c><00:06:05.039><c> me</c><00:06:05.199><c> a</c><00:06:05.360><c> joke</c><00:06:05.639><c> with</c><00:06:05.840><c> computers</c>

00:06:06.710 --> 00:06:06.720 align:start position:0%
assistant tell me a joke with computers
 

00:06:06.720 --> 00:06:08.390 align:start position:0%
assistant tell me a joke with computers
and<00:06:06.840><c> now</c><00:06:06.960><c> we're</c><00:06:07.080><c> just</c><00:06:07.199><c> going</c><00:06:07.319><c> to</c><00:06:07.440><c> wait</c><00:06:07.720><c> on</c><00:06:07.919><c> the</c>

00:06:08.390 --> 00:06:08.400 align:start position:0%
and now we're just going to wait on the
 

00:06:08.400 --> 00:06:10.710 align:start position:0%
and now we're just going to wait on the
oh<00:06:08.639><c> we</c><00:06:08.720><c> already</c><00:06:08.840><c> got</c><00:06:08.960><c> a</c><00:06:09.080><c> response</c><00:06:10.080><c> so</c><00:06:10.240><c> it</c><00:06:10.400><c> looks</c>

00:06:10.710 --> 00:06:10.720 align:start position:0%
oh we already got a response so it looks
 

00:06:10.720 --> 00:06:15.870 align:start position:0%
oh we already got a response so it looks
like<00:06:11.240><c> it</c><00:06:11.800><c> codes</c><00:06:12.360><c> a</c><00:06:12.560><c> joke</c><00:06:12.960><c> python</c><00:06:13.840><c> file</c><00:06:14.880><c> uh</c>

00:06:15.870 --> 00:06:15.880 align:start position:0%
like it codes a joke python file uh
 

00:06:15.880 --> 00:06:16.990 align:start position:0%
like it codes a joke python file uh
gives<00:06:16.000><c> you</c><00:06:16.120><c> the</c><00:06:16.199><c> punch</c><00:06:16.520><c> line</c><00:06:16.759><c> while</c><00:06:16.919><c> the</c>

00:06:16.990 --> 00:06:17.000 align:start position:0%
gives you the punch line while the
 

00:06:17.000 --> 00:06:18.830 align:start position:0%
gives you the punch line while the
output<00:06:17.280><c> is</c><00:06:17.639><c> why</c><00:06:17.880><c> the</c><00:06:17.960><c> computer</c><00:06:18.280><c> go</c><00:06:18.440><c> to</c><00:06:18.599><c> the</c>

00:06:18.830 --> 00:06:18.840 align:start position:0%
output is why the computer go to the
 

00:06:18.840 --> 00:06:22.230 align:start position:0%
output is why the computer go to the
doctor<00:06:19.360><c> because</c><00:06:19.560><c> it</c><00:06:19.680><c> had</c><00:06:19.800><c> a</c><00:06:20.000><c> virus</c><00:06:20.880><c> W</c><00:06:21.240><c> amazing</c>

00:06:22.230 --> 00:06:22.240 align:start position:0%
doctor because it had a virus W amazing
 

00:06:22.240 --> 00:06:23.710 align:start position:0%
doctor because it had a virus W amazing
uh<00:06:22.360><c> so</c><00:06:22.560><c> you</c><00:06:22.680><c> see</c><00:06:22.880><c> here</c><00:06:23.039><c> this</c><00:06:23.199><c> default</c><00:06:23.520><c> aut</c>

00:06:23.710 --> 00:06:23.720 align:start position:0%
uh so you see here this default aut
 

00:06:23.720 --> 00:06:25.350 align:start position:0%
uh so you see here this default aut
reply<00:06:24.000><c> if</c><00:06:24.120><c> this</c><00:06:24.240><c> wasn't</c><00:06:24.599><c> here</c><00:06:25.039><c> it</c><00:06:25.120><c> would</c><00:06:25.280><c> give</c>

00:06:25.350 --> 00:06:25.360 align:start position:0%
reply if this wasn't here it would give
 

00:06:25.360 --> 00:06:27.390 align:start position:0%
reply if this wasn't here it would give
us<00:06:25.479><c> an</c><00:06:25.599><c> error</c><00:06:25.880><c> message</c><00:06:26.560><c> uh</c><00:06:26.720><c> the</c><00:06:26.840><c> computer</c><00:06:27.160><c> joke</c>

00:06:27.390 --> 00:06:27.400 align:start position:0%
us an error message uh the computer joke
 

00:06:27.400 --> 00:06:30.629 align:start position:0%
us an error message uh the computer joke
did<00:06:27.520><c> not</c><00:06:27.639><c> land</c><00:06:28.000><c> well</c><00:06:28.960><c> okay</c><00:06:29.639><c> awesome</c><00:06:30.039><c> so</c><00:06:30.479><c> it</c>

00:06:30.629 --> 00:06:30.639 align:start position:0%
did not land well okay awesome so it
 

00:06:30.639 --> 00:06:32.870 align:start position:0%
did not land well okay awesome so it
works<00:06:31.080><c> right</c><00:06:31.400><c> this</c><00:06:31.560><c> is</c><00:06:31.919><c> how</c><00:06:32.400><c> we</c><00:06:32.599><c> get</c><00:06:32.720><c> it</c>

00:06:32.870 --> 00:06:32.880 align:start position:0%
works right this is how we get it
 

00:06:32.880 --> 00:06:35.430 align:start position:0%
works right this is how we get it
working<00:06:33.160><c> locally</c><00:06:33.720><c> now</c><00:06:33.919><c> let</c><00:06:34.080><c> me</c><00:06:34.599><c> see</c><00:06:35.080><c> how</c><00:06:35.240><c> it</c>

00:06:35.430 --> 00:06:35.440 align:start position:0%
working locally now let me see how it
 

00:06:35.440 --> 00:06:38.309 align:start position:0%
working locally now let me see how it
does<00:06:36.039><c> with</c><00:06:36.400><c> writing</c><00:06:36.800><c> an</c><00:06:37.000><c> actual</c><00:06:37.319><c> game</c><00:06:37.960><c> okay</c><00:06:38.080><c> so</c>

00:06:38.309 --> 00:06:38.319 align:start position:0%
does with writing an actual game okay so
 

00:06:38.319 --> 00:06:40.189 align:start position:0%
does with writing an actual game okay so
my<00:06:38.479><c> last</c><00:06:38.680><c> example</c><00:06:39.199><c> just</c><00:06:39.319><c> to</c><00:06:39.440><c> show</c><00:06:39.639><c> you</c><00:06:40.080><c> uh</c>

00:06:40.189 --> 00:06:40.199 align:start position:0%
my last example just to show you uh
 

00:06:40.199 --> 00:06:42.070 align:start position:0%
my last example just to show you uh
something<00:06:40.440><c> else</c><00:06:40.720><c> that</c><00:06:40.840><c> you</c><00:06:41.039><c> need</c><00:06:41.240><c> to</c><00:06:41.440><c> do</c><00:06:41.919><c> so</c>

00:06:42.070 --> 00:06:42.080 align:start position:0%
something else that you need to do so
 

00:06:42.080 --> 00:06:45.029 align:start position:0%
something else that you need to do so
that<00:06:42.280><c> you</c><00:06:43.360><c> maybe</c><00:06:44.360><c> will</c><00:06:44.759><c> understand</c><00:06:44.919><c> why</c>

00:06:45.029 --> 00:06:45.039 align:start position:0%
that you maybe will understand why
 

00:06:45.039 --> 00:06:46.430 align:start position:0%
that you maybe will understand why
something<00:06:45.240><c> didn't</c><00:06:45.479><c> work</c><00:06:45.840><c> is</c><00:06:45.960><c> I</c><00:06:46.080><c> had</c><00:06:46.160><c> to</c><00:06:46.280><c> write</c>

00:06:46.430 --> 00:06:46.440 align:start position:0%
something didn't work is I had to write
 

00:06:46.440 --> 00:06:48.189 align:start position:0%
something didn't work is I had to write
a<00:06:46.520><c> full</c><00:06:46.720><c> imple</c><00:06:47.160><c> implementation</c><00:06:47.720><c> of</c><00:06:47.800><c> the</c><00:06:47.919><c> snake</c>

00:06:48.189 --> 00:06:48.199 align:start position:0%
a full imple implementation of the snake
 

00:06:48.199 --> 00:06:50.990 align:start position:0%
a full imple implementation of the snake
game<00:06:48.360><c> in</c><00:06:48.560><c> Python</c><00:06:49.280><c> now</c><00:06:49.479><c> the</c><00:06:49.720><c> thing</c><00:06:50.080><c> is</c><00:06:50.680><c> I</c><00:06:50.759><c> think</c>

00:06:50.990 --> 00:06:51.000 align:start position:0%
game in Python now the thing is I think
 

00:06:51.000 --> 00:06:53.790 align:start position:0%
game in Python now the thing is I think
by<00:06:51.240><c> default</c><00:06:51.840><c> Claude</c><00:06:52.199><c> allows</c><00:06:52.680><c> 1,000</c><00:06:53.240><c> of</c><00:06:53.400><c> Max</c>

00:06:53.790 --> 00:06:53.800 align:start position:0%
by default Claude allows 1,000 of Max
 

00:06:53.800 --> 00:06:55.990 align:start position:0%
by default Claude allows 1,000 of Max
tokens<00:06:54.280><c> and</c><00:06:54.479><c> what</c><00:06:54.800><c> issue</c><00:06:55.160><c> I</c><00:06:55.319><c> ran</c><00:06:55.560><c> into</c><00:06:55.759><c> was</c><00:06:55.919><c> all</c>

00:06:55.990 --> 00:06:56.000 align:start position:0%
tokens and what issue I ran into was all
 

00:06:56.000 --> 00:06:57.629 align:start position:0%
tokens and what issue I ran into was all
of<00:06:56.039><c> a</c><00:06:56.120><c> sudden</c><00:06:56.319><c> the</c><00:06:56.440><c> code</c><00:06:56.680><c> just</c><00:06:56.840><c> wouldn't</c><00:06:57.080><c> be</c>

00:06:57.629 --> 00:06:57.639 align:start position:0%
of a sudden the code just wouldn't be
 

00:06:57.639 --> 00:07:01.070 align:start position:0%
of a sudden the code just wouldn't be
completed<00:06:58.639><c> so</c><00:06:59.319><c> and</c><00:06:59.560><c> in</c><00:06:59.639><c> order</c><00:06:59.840><c> to</c><00:07:00.160><c> fix</c><00:07:00.520><c> that</c><00:07:00.879><c> in</c>

00:07:01.070 --> 00:07:01.080 align:start position:0%
completed so and in order to fix that in
 

00:07:01.080 --> 00:07:04.029 align:start position:0%
completed so and in order to fix that in
your<00:07:01.680><c> uh</c><00:07:01.800><c> llm</c><00:07:02.240><c> config</c><00:07:02.639><c> that</c><00:07:02.800><c> I</c><00:07:03.000><c> named</c><00:07:03.360><c> Claude</c>

00:07:04.029 --> 00:07:04.039 align:start position:0%
your uh llm config that I named Claude
 

00:07:04.039 --> 00:07:06.309 align:start position:0%
your uh llm config that I named Claude
that<00:07:04.160><c> you</c><00:07:04.240><c> need</c><00:07:04.400><c> to</c><00:07:04.599><c> set</c><00:07:04.919><c> the</c><00:07:05.160><c> max</c><00:07:05.560><c> tokens</c><00:07:06.199><c> I</c>

00:07:06.309 --> 00:07:06.319 align:start position:0%
that you need to set the max tokens I
 

00:07:06.319 --> 00:07:07.350 align:start position:0%
that you need to set the max tokens I
said<00:07:06.520><c> to</c>

00:07:07.350 --> 00:07:07.360 align:start position:0%
said to
 

00:07:07.360 --> 00:07:10.909 align:start position:0%
said to
496<00:07:08.360><c> now</c><00:07:09.240><c> whenever</c><00:07:09.599><c> we</c><00:07:09.720><c> used</c><00:07:10.039><c> LM</c><00:07:10.360><c> Studio</c><00:07:10.840><c> you</c>

00:07:10.909 --> 00:07:10.919 align:start position:0%
496 now whenever we used LM Studio you
 

00:07:10.919 --> 00:07:12.469 align:start position:0%
496 now whenever we used LM Studio you
can<00:07:11.039><c> set</c><00:07:11.240><c> this</c><00:07:11.360><c> to</c><00:07:11.599><c> negative</c><00:07:11.919><c> one</c><00:07:12.280><c> and</c><00:07:12.360><c> then</c>

00:07:12.469 --> 00:07:12.479 align:start position:0%
can set this to negative one and then
 

00:07:12.479 --> 00:07:14.909 align:start position:0%
can set this to negative one and then
there's<00:07:12.720><c> like</c><00:07:12.919><c> basically</c><00:07:13.400><c> No</c><00:07:13.720><c> Limit</c><00:07:14.560><c> well</c><00:07:14.759><c> you</c>

00:07:14.909 --> 00:07:14.919 align:start position:0%
there's like basically No Limit well you
 

00:07:14.919 --> 00:07:16.990 align:start position:0%
there's like basically No Limit well you
can't<00:07:15.160><c> do</c><00:07:15.400><c> that</c><00:07:16.039><c> uh</c><00:07:16.160><c> you</c><00:07:16.240><c> can't</c><00:07:16.400><c> do</c><00:07:16.560><c> that</c><00:07:16.720><c> here</c>

00:07:16.990 --> 00:07:17.000 align:start position:0%
can't do that uh you can't do that here
 

00:07:17.000 --> 00:07:18.350 align:start position:0%
can't do that uh you can't do that here
right<00:07:17.080><c> if</c><00:07:17.160><c> you</c><00:07:17.240><c> say</c><00:07:17.479><c> negative</c><00:07:17.800><c> 1</c><00:07:18.120><c> it</c><00:07:18.240><c> just</c>

00:07:18.350 --> 00:07:18.360 align:start position:0%
right if you say negative 1 it just
 

00:07:18.360 --> 00:07:20.150 align:start position:0%
right if you say negative 1 it just
gives<00:07:18.520><c> you</c><00:07:18.639><c> an</c><00:07:18.720><c> error</c><00:07:19.039><c> saying</c><00:07:19.319><c> it</c><00:07:19.560><c> requires</c>

00:07:20.150 --> 00:07:20.160 align:start position:0%
gives you an error saying it requires
 

00:07:20.160 --> 00:07:22.350 align:start position:0%
gives you an error saying it requires
something<00:07:20.520><c> greater</c><00:07:20.879><c> than</c><00:07:21.440><c> like</c><00:07:21.680><c> uh</c><00:07:21.879><c> zero</c><00:07:22.199><c> or</c>

00:07:22.350 --> 00:07:22.360 align:start position:0%
something greater than like uh zero or
 

00:07:22.360 --> 00:07:26.710 align:start position:0%
something greater than like uh zero or
one<00:07:22.879><c> so</c><00:07:23.160><c> I</c><00:07:23.280><c> put</c><00:07:23.560><c> 496</c><00:07:24.479><c> here</c><00:07:25.000><c> and</c><00:07:25.120><c> then</c><00:07:25.720><c> finally</c>

00:07:26.710 --> 00:07:26.720 align:start position:0%
one so I put 496 here and then finally
 

00:07:26.720 --> 00:07:28.869 align:start position:0%
one so I put 496 here and then finally
um<00:07:27.000><c> you</c><00:07:27.160><c> know</c><00:07:27.960><c> if</c><00:07:28.080><c> you</c><00:07:28.160><c> can</c><00:07:28.280><c> see</c><00:07:28.520><c> here</c><00:07:28.759><c> I</c>

00:07:28.869 --> 00:07:28.879 align:start position:0%
um you know if you can see here I
 

00:07:28.879 --> 00:07:31.589 align:start position:0%
um you know if you can see here I
actually<00:07:29.160><c> have</c><00:07:29.280><c> the</c><00:07:29.599><c> full</c><00:07:29.919><c> code</c><00:07:30.319><c> for</c><00:07:30.759><c> a</c><00:07:30.919><c> snake</c>

00:07:31.589 --> 00:07:31.599 align:start position:0%
actually have the full code for a snake
 

00:07:31.599 --> 00:07:34.469 align:start position:0%
actually have the full code for a snake
game<00:07:32.560><c> but</c><00:07:32.879><c> before</c><00:07:33.599><c> most</c><00:07:33.800><c> of</c><00:07:33.960><c> this</c><00:07:34.080><c> would</c><00:07:34.240><c> be</c>

00:07:34.469 --> 00:07:34.479 align:start position:0%
game but before most of this would be
 

00:07:34.479 --> 00:07:37.029 align:start position:0%
game but before most of this would be
cut<00:07:34.759><c> off</c><00:07:35.400><c> and</c><00:07:35.520><c> that</c><00:07:35.639><c> was</c><00:07:35.879><c> because</c><00:07:36.720><c> I</c><00:07:36.800><c> couldn't</c>

00:07:37.029 --> 00:07:37.039 align:start position:0%
cut off and that was because I couldn't
 

00:07:37.039 --> 00:07:38.950 align:start position:0%
cut off and that was because I couldn't
figure<00:07:37.280><c> out</c><00:07:37.400><c> how</c><00:07:37.520><c> to</c><00:07:37.599><c> get</c><00:07:37.720><c> the</c><00:07:37.800><c> max</c><00:07:38.039><c> token</c><00:07:38.400><c> so</c>

00:07:38.950 --> 00:07:38.960 align:start position:0%
figure out how to get the max token so
 

00:07:38.960 --> 00:07:40.550 align:start position:0%
figure out how to get the max token so
just<00:07:39.199><c> make</c><00:07:39.360><c> sure</c><00:07:39.759><c> that</c><00:07:39.879><c> you</c><00:07:40.000><c> set</c><00:07:40.319><c> this</c>

00:07:40.550 --> 00:07:40.560 align:start position:0%
just make sure that you set this
 

00:07:40.560 --> 00:07:43.029 align:start position:0%
just make sure that you set this
property<00:07:41.160><c> with</c><00:07:41.280><c> your</c><00:07:41.440><c> llm</c><00:07:41.960><c> config</c><00:07:42.680><c> if</c><00:07:42.800><c> you</c>

00:07:43.029 --> 00:07:43.039 align:start position:0%
property with your llm config if you
 

00:07:43.039 --> 00:07:45.070 align:start position:0%
property with your llm config if you
think<00:07:43.199><c> you're</c><00:07:43.319><c> going</c><00:07:43.440><c> to</c><00:07:43.599><c> have</c><00:07:44.199><c> um</c><00:07:44.400><c> a</c><00:07:44.639><c> bigger</c>

00:07:45.070 --> 00:07:45.080 align:start position:0%
think you're going to have um a bigger
 

00:07:45.080 --> 00:07:47.189 align:start position:0%
think you're going to have um a bigger
amount<00:07:45.400><c> of</c><00:07:45.599><c> tokens</c><00:07:45.960><c> to</c><00:07:46.120><c> use</c><00:07:46.639><c> now</c><00:07:46.800><c> let's</c><00:07:46.960><c> see</c>

00:07:47.189 --> 00:07:47.199 align:start position:0%
amount of tokens to use now let's see
 

00:07:47.199 --> 00:07:49.149 align:start position:0%
amount of tokens to use now let's see
how<00:07:47.400><c> much</c><00:07:47.879><c> this</c><00:07:48.159><c> cost</c><00:07:48.680><c> as</c><00:07:48.759><c> you</c><00:07:48.840><c> can</c><00:07:48.919><c> see</c><00:07:49.080><c> here</c>

00:07:49.149 --> 00:07:49.159 align:start position:0%
how much this cost as you can see here
 

00:07:49.159 --> 00:07:50.629 align:start position:0%
how much this cost as you can see here
I'm<00:07:49.280><c> still</c><00:07:49.479><c> at</c>

00:07:50.629 --> 00:07:50.639 align:start position:0%
I'm still at
 

00:07:50.639 --> 00:07:54.589 align:start position:0%
I'm still at
$484<00:07:51.639><c> so</c><00:07:52.159><c> this</c><00:07:52.280><c> $5</c><00:07:53.159><c> is</c><00:07:53.479><c> going</c><00:07:53.560><c> to</c><00:07:53.960><c> be</c><00:07:54.120><c> used</c><00:07:54.440><c> a</c>

00:07:54.589 --> 00:07:54.599 align:start position:0%
$484 so this $5 is going to be used a
 

00:07:54.599 --> 00:07:56.990 align:start position:0%
$484 so this $5 is going to be used a
lot<00:07:54.879><c> right</c><00:07:55.000><c> I</c><00:07:55.080><c> haven't</c><00:07:55.319><c> used</c><00:07:55.639><c> Opus</c><00:07:56.199><c> their</c><00:07:56.599><c> best</c>

00:07:56.990 --> 00:07:57.000 align:start position:0%
lot right I haven't used Opus their best
 

00:07:57.000 --> 00:07:58.790 align:start position:0%
lot right I haven't used Opus their best
model<00:07:57.560><c> but</c><00:07:57.680><c> I'm</c><00:07:57.759><c> using</c><00:07:58.039><c> their</c><00:07:58.280><c> second</c><00:07:58.599><c> best</c>

00:07:58.790 --> 00:07:58.800 align:start position:0%
model but I'm using their second best
 

00:07:58.800 --> 00:08:01.309 align:start position:0%
model but I'm using their second best
and<00:07:58.919><c> it's</c><00:07:59.120><c> cost</c><00:07:59.759><c> pennies</c><00:08:00.319><c> so</c><00:08:00.759><c> that</c><00:08:00.960><c> in</c><00:08:01.080><c> the</c>

00:08:01.309 --> 00:08:01.319 align:start position:0%
and it's cost pennies so that in the
 

00:08:01.319 --> 00:08:03.189 align:start position:0%
and it's cost pennies so that in the
last<00:08:01.599><c> snake</c><00:08:02.000><c> game</c><00:08:02.159><c> that</c><00:08:02.280><c> I</c><00:08:02.440><c> just</c><00:08:02.599><c> created</c><00:08:03.120><c> it</c>

00:08:03.189 --> 00:08:03.199 align:start position:0%
last snake game that I just created it
 

00:08:03.199 --> 00:08:05.869 align:start position:0%
last snake game that I just created it
was<00:08:03.560><c> 188</c><00:08:04.560><c> tokens</c><00:08:05.199><c> which</c><00:08:05.280><c> is</c><00:08:05.440><c> why</c><00:08:05.520><c> I</c><00:08:05.639><c> wasn't</c>

00:08:05.869 --> 00:08:05.879 align:start position:0%
was 188 tokens which is why I wasn't
 

00:08:05.879 --> 00:08:08.670 align:start position:0%
was 188 tokens which is why I wasn't
getting<00:08:06.240><c> the</c><00:08:06.599><c> full</c><00:08:07.199><c> response</c><00:08:07.720><c> back</c><00:08:08.319><c> okay</c>

00:08:08.670 --> 00:08:08.680 align:start position:0%
getting the full response back okay
 

00:08:08.680 --> 00:08:11.149 align:start position:0%
getting the full response back okay
great<00:08:09.159><c> so</c><00:08:09.360><c> we</c><00:08:09.560><c> now</c><00:08:09.879><c> know</c><00:08:10.159><c> how</c><00:08:10.280><c> to</c><00:08:10.440><c> use</c><00:08:10.759><c> the</c>

00:08:11.149 --> 00:08:11.159 align:start position:0%
great so we now know how to use the
 

00:08:11.159 --> 00:08:14.189 align:start position:0%
great so we now know how to use the
dashboard<00:08:11.639><c> in</c><00:08:11.840><c> claw.</c><00:08:12.840><c> so</c><00:08:13.000><c> you</c><00:08:13.120><c> can</c><00:08:13.400><c> just</c><00:08:13.800><c> try</c>

00:08:14.189 --> 00:08:14.199 align:start position:0%
dashboard in claw. so you can just try
 

00:08:14.199 --> 00:08:16.270 align:start position:0%
dashboard in claw. so you can just try
Claude<00:08:14.560><c> Sonet</c><00:08:14.960><c> for</c><00:08:15.159><c> free</c><00:08:15.680><c> and</c><00:08:15.800><c> just</c><00:08:15.919><c> to</c><00:08:16.039><c> see</c>

00:08:16.270 --> 00:08:16.280 align:start position:0%
Claude Sonet for free and just to see
 

00:08:16.280 --> 00:08:18.070 align:start position:0%
Claude Sonet for free and just to see
how<00:08:16.440><c> it</c><00:08:16.680><c> works</c><00:08:16.960><c> or</c><00:08:17.120><c> see</c><00:08:17.319><c> how</c><00:08:17.440><c> it</c><00:08:17.599><c> reacts</c><00:08:17.960><c> to</c>

00:08:18.070 --> 00:08:18.080 align:start position:0%
how it works or see how it reacts to
 

00:08:18.080 --> 00:08:19.909 align:start position:0%
how it works or see how it reacts to
what<00:08:18.199><c> you're</c><00:08:18.400><c> asking</c><00:08:18.680><c> it</c><00:08:19.120><c> and</c><00:08:19.400><c> we</c><00:08:19.560><c> also</c><00:08:19.759><c> were</c>

00:08:19.909 --> 00:08:19.919 align:start position:0%
what you're asking it and we also were
 

00:08:19.919 --> 00:08:22.189 align:start position:0%
what you're asking it and we also were
able<00:08:20.080><c> to</c><00:08:20.199><c> use</c><00:08:20.360><c> the</c><00:08:20.560><c> API</c><00:08:21.400><c> just</c><00:08:21.520><c> to</c><00:08:21.639><c> do</c><00:08:21.759><c> a</c><00:08:21.879><c> simple</c>

00:08:22.189 --> 00:08:22.199 align:start position:0%
able to use the API just to do a simple
 

00:08:22.199 --> 00:08:25.309 align:start position:0%
able to use the API just to do a simple
test<00:08:22.479><c> locally</c><00:08:23.319><c> and</c><00:08:23.720><c> also</c><00:08:24.120><c> integrate</c><00:08:24.599><c> it</c><00:08:25.080><c> with</c>

00:08:25.309 --> 00:08:25.319 align:start position:0%
test locally and also integrate it with
 

00:08:25.319 --> 00:08:28.430 align:start position:0%
test locally and also integrate it with
autogen<00:08:25.919><c> using</c><00:08:26.240><c> light</c><00:08:26.599><c> llm</c><00:08:27.479><c> now</c><00:08:27.720><c> I</c><00:08:27.919><c> know</c><00:08:28.280><c> that</c>

00:08:28.430 --> 00:08:28.440 align:start position:0%
autogen using light llm now I know that
 

00:08:28.440 --> 00:08:30.830 align:start position:0%
autogen using light llm now I know that
it's<00:08:28.599><c> not</c><00:08:28.840><c> completely</c><00:08:29.560><c> free</c><00:08:30.240><c> but</c><00:08:30.479><c> as</c><00:08:30.599><c> You'</c>

00:08:30.830 --> 00:08:30.840 align:start position:0%
it's not completely free but as You'
 

00:08:30.840 --> 00:08:34.149 align:start position:0%
it's not completely free but as You'
seen<00:08:31.280><c> that</c><00:08:31.400><c> $5</c><00:08:32.240><c> of</c><00:08:32.440><c> free</c><00:08:32.839><c> credit</c><00:08:33.560><c> I</c><00:08:33.719><c> ended</c><00:08:33.959><c> up</c>

00:08:34.149 --> 00:08:34.159 align:start position:0%
seen that $5 of free credit I ended up
 

00:08:34.159 --> 00:08:36.829 align:start position:0%
seen that $5 of free credit I ended up
only<00:08:34.560><c> spending</c><00:08:35.080><c> 16</c><00:08:35.479><c> cents</c><00:08:35.839><c> and</c><00:08:36.000><c> I</c><00:08:36.240><c> honestly</c><00:08:36.680><c> in</c>

00:08:36.829 --> 00:08:36.839 align:start position:0%
only spending 16 cents and I honestly in
 

00:08:36.839 --> 00:08:38.870 align:start position:0%
only spending 16 cents and I honestly in
between<00:08:37.599><c> I</c><00:08:37.719><c> had</c><00:08:37.919><c> executed</c><00:08:38.320><c> a</c><00:08:38.399><c> lot</c><00:08:38.519><c> of</c><00:08:38.640><c> things</c>

00:08:38.870 --> 00:08:38.880 align:start position:0%
between I had executed a lot of things
 

00:08:38.880 --> 00:08:40.430 align:start position:0%
between I had executed a lot of things
cuz<00:08:39.080><c> I</c><00:08:39.159><c> was</c><00:08:39.320><c> running</c><00:08:39.640><c> into</c><00:08:39.880><c> issues</c><00:08:40.200><c> that</c><00:08:40.320><c> I</c>

00:08:40.430 --> 00:08:40.440 align:start position:0%
cuz I was running into issues that I
 

00:08:40.440 --> 00:08:42.350 align:start position:0%
cuz I was running into issues that I
didn't<00:08:40.599><c> run</c><00:08:40.800><c> into</c><00:08:41.240><c> like</c><00:08:41.440><c> offline</c><00:08:42.120><c> with</c>

00:08:42.350 --> 00:08:42.360 align:start position:0%
didn't run into like offline with
 

00:08:42.360 --> 00:08:44.990 align:start position:0%
didn't run into like offline with
another<00:08:42.719><c> email</c><00:08:43.560><c> and</c><00:08:43.800><c> so</c><00:08:44.240><c> I</c><00:08:44.320><c> went</c><00:08:44.640><c> and</c><00:08:44.680><c> fixed</c>

00:08:44.990 --> 00:08:45.000 align:start position:0%
another email and so I went and fixed
 

00:08:45.000 --> 00:08:46.509 align:start position:0%
another email and so I went and fixed
those<00:08:45.160><c> and</c><00:08:45.600><c> during</c><00:08:45.800><c> that</c><00:08:45.959><c> testing</c><00:08:46.240><c> you</c><00:08:46.360><c> know</c><00:08:46.440><c> I</c>

00:08:46.509 --> 00:08:46.519 align:start position:0%
those and during that testing you know I
 

00:08:46.519 --> 00:08:48.870 align:start position:0%
those and during that testing you know I
ran<00:08:46.720><c> it</c><00:08:46.959><c> a</c><00:08:47.160><c> good</c><00:08:47.399><c> bit</c><00:08:47.920><c> and</c><00:08:48.200><c> I</c><00:08:48.320><c> still</c><00:08:48.640><c> Barely</c>

00:08:48.870 --> 00:08:48.880 align:start position:0%
ran it a good bit and I still Barely
 

00:08:48.880 --> 00:08:50.990 align:start position:0%
ran it a good bit and I still Barely
Used<00:08:49.160><c> anything</c><00:08:49.440><c> it</c><00:08:49.600><c> cost</c><00:08:49.839><c> me</c><00:08:50.040><c> pennies</c><00:08:50.640><c> again</c>

00:08:50.990 --> 00:08:51.000 align:start position:0%
Used anything it cost me pennies again
 

00:08:51.000 --> 00:08:53.150 align:start position:0%
Used anything it cost me pennies again
that<00:08:51.160><c> was</c><00:08:51.279><c> with</c><00:08:51.440><c> the</c><00:08:51.720><c> second</c><00:08:52.440><c> version</c><00:08:53.040><c> the</c>

00:08:53.150 --> 00:08:53.160 align:start position:0%
that was with the second version the
 

00:08:53.160 --> 00:08:55.190 align:start position:0%
that was with the second version the
second<00:08:53.480><c> best</c><00:08:53.680><c> version</c><00:08:53.959><c> of</c><00:08:54.160><c> Claude</c><00:08:54.839><c> but</c><00:08:55.040><c> the</c>

00:08:55.190 --> 00:08:55.200 align:start position:0%
second best version of Claude but the
 

00:08:55.200 --> 00:08:57.310 align:start position:0%
second best version of Claude but the
first<00:08:55.920><c> uh</c><00:08:56.040><c> the</c><00:08:56.160><c> first</c><00:08:56.399><c> version</c><00:08:56.680><c> or</c><00:08:56.839><c> the</c><00:08:57.000><c> best</c>

00:08:57.310 --> 00:08:57.320 align:start position:0%
first uh the first version or the best
 

00:08:57.320 --> 00:08:59.590 align:start position:0%
first uh the first version or the best
most<00:08:57.519><c> intelligent</c><00:08:58.079><c> version</c><00:08:58.760><c> is</c><00:08:59.000><c> apparently</c>

00:08:59.590 --> 00:08:59.600 align:start position:0%
most intelligent version is apparently
 

00:08:59.600 --> 00:09:01.550 align:start position:0%
most intelligent version is apparently
being<00:08:59.800><c> overloaded</c><00:09:00.360><c> by</c><00:09:00.560><c> everybody</c><00:09:01.000><c> right</c><00:09:01.120><c> now</c>

00:09:01.550 --> 00:09:01.560 align:start position:0%
being overloaded by everybody right now
 

00:09:01.560 --> 00:09:02.949 align:start position:0%
being overloaded by everybody right now
so<00:09:01.760><c> I</c><00:09:01.839><c> couldn't</c><00:09:02.040><c> even</c><00:09:02.200><c> test</c><00:09:02.360><c> it</c><00:09:02.480><c> out</c><00:09:02.720><c> thank</c><00:09:02.880><c> you</c>

00:09:02.949 --> 00:09:02.959 align:start position:0%
so I couldn't even test it out thank you
 

00:09:02.959 --> 00:09:04.269 align:start position:0%
so I couldn't even test it out thank you
for<00:09:03.079><c> watching</c><00:09:03.320><c> let</c><00:09:03.440><c> me</c><00:09:03.519><c> know</c><00:09:03.640><c> in</c><00:09:03.720><c> the</c><00:09:03.920><c> comments</c>

00:09:04.269 --> 00:09:04.279 align:start position:0%
for watching let me know in the comments
 

00:09:04.279 --> 00:09:06.150 align:start position:0%
for watching let me know in the comments
down<00:09:04.440><c> below</c><00:09:04.839><c> what</c><00:09:04.959><c> you</c><00:09:05.120><c> thought</c><00:09:05.320><c> about</c><00:09:05.560><c> this</c>

00:09:06.150 --> 00:09:06.160 align:start position:0%
down below what you thought about this
 

00:09:06.160 --> 00:09:08.389 align:start position:0%
down below what you thought about this
if<00:09:06.279><c> it</c><00:09:06.440><c> works</c><00:09:06.720><c> for</c><00:09:06.880><c> you</c><00:09:07.200><c> try</c><00:09:07.519><c> it</c><00:09:07.680><c> out</c><00:09:08.120><c> you</c><00:09:08.240><c> just</c>

00:09:08.389 --> 00:09:08.399 align:start position:0%
if it works for you try it out you just
 

00:09:08.399 --> 00:09:10.509 align:start position:0%
if it works for you try it out you just
have<00:09:08.519><c> to</c><00:09:08.680><c> use</c><00:09:08.880><c> light</c><00:09:09.160><c> llm</c><00:09:09.640><c> I'll</c><00:09:09.800><c> have</c><00:09:10.120><c> links</c><00:09:10.399><c> in</c>

00:09:10.509 --> 00:09:10.519 align:start position:0%
have to use light llm I'll have links in
 

00:09:10.519 --> 00:09:12.870 align:start position:0%
have to use light llm I'll have links in
the<00:09:10.640><c> description</c><00:09:11.160><c> so</c><00:09:11.360><c> that</c><00:09:11.640><c> um</c><00:09:11.959><c> you</c><00:09:12.200><c> know</c><00:09:12.760><c> what</c>

00:09:12.870 --> 00:09:12.880 align:start position:0%
the description so that um you know what
 

00:09:12.880 --> 00:09:13.910 align:start position:0%
the description so that um you know what
you<00:09:12.959><c> need</c><00:09:13.120><c> to</c><00:09:13.240><c> do</c><00:09:13.480><c> and</c><00:09:13.600><c> I'll</c><00:09:13.720><c> have</c><00:09:13.839><c> the</c>

00:09:13.910 --> 00:09:13.920 align:start position:0%
you need to do and I'll have the
 

00:09:13.920 --> 00:09:15.150 align:start position:0%
you need to do and I'll have the
requirements.<00:09:14.519><c> tech</c><00:09:14.720><c> so</c><00:09:14.800><c> you</c><00:09:14.880><c> don't</c><00:09:15.000><c> have</c><00:09:15.079><c> to</c>

00:09:15.150 --> 00:09:15.160 align:start position:0%
requirements. tech so you don't have to
 

00:09:15.160 --> 00:09:17.150 align:start position:0%
requirements. tech so you don't have to
worry<00:09:15.399><c> about</c><00:09:15.800><c> all</c><00:09:15.959><c> the</c><00:09:16.120><c> installs</c><00:09:16.600><c> you</c><00:09:16.680><c> need</c><00:09:17.040><c> as</c>

00:09:17.150 --> 00:09:17.160 align:start position:0%
worry about all the installs you need as
 

00:09:17.160 --> 00:09:18.550 align:start position:0%
worry about all the installs you need as
always<00:09:17.519><c> thank</c><00:09:17.680><c> you</c><00:09:17.760><c> for</c><00:09:17.959><c> watching</c><00:09:18.279><c> I'll</c><00:09:18.440><c> see</c>

00:09:18.550 --> 00:09:18.560 align:start position:0%
always thank you for watching I'll see
 

00:09:18.560 --> 00:09:21.320 align:start position:0%
always thank you for watching I'll see
you<00:09:18.720><c> next</c><00:09:18.920><c> video</c><00:09:19.160><c> by</c>

