import { NextRequest, NextResponse } from 'next/server'
import { DatabaseService } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const topics = searchParams.get('topics')?.split(',').filter(Boolean) || []

    let videos
    if (topics.length > 0) {      // Get videos from specific topics
      const videoPromises = topics.map(topic => 
        DatabaseService.getVideosByTopic(topic, Math.ceil(limit / topics.length))
      )
      const videoArrays = await Promise.all(videoPromises)
      videos = videoArrays.flat()
        .sort((a, b) => {
          // Use fallback hierarchy: published_at -> created_at -> updated_at
          const getEffectiveDate = (video: any) => {
            if (video.published_at) return new Date(video.published_at).getTime();
            if (video.created_at) return new Date(video.created_at).getTime();
            if (video.updated_at) return new Date(video.updated_at).getTime();
            return 0;
          };

          const dateA = getEffectiveDate(a);
          const dateB = getEffectiveDate(b);
          return dateB - dateA;
        })
        .slice(0, limit)    } else {
      // Get recent videos across all topics
      console.log('🎯 Calling getRecentVideos with limit:', limit)
      videos = await DatabaseService.getRecentVideos(limit)
      console.log('🎯 getRecentVideos returned:', videos.length, 'videos')
      console.log('🎯 First video sample:', videos[0])
    }    const responseData = { 
      videos,
      count: videos.length,
      topics: topics.length > 0 ? topics : 'all'
    }
      console.log('🎯 Final API response structure:', { 
      videosCount: videos.length, 
      videosIsArray: Array.isArray(videos),
      firstVideoTitle: videos[0]?.title,
      actualResponseKeys: Object.keys(responseData)
    })

    return NextResponse.json(responseData)
  } catch (error) {
    console.error('Error in recent videos API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch recent videos' },
      { status: 500 }
    )
  }
}
