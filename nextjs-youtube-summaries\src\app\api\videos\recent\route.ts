import { NextRequest, NextResponse } from 'next/server'
import { DatabaseService } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const topics = searchParams.get('topics')?.split(',').filter(Boolean) || []

    let videos
    if (topics.length > 0) {      // Get videos from specific topics
      const videoPromises = topics.map(topic => 
        DatabaseService.getVideosByTopic(topic, Math.ceil(limit / topics.length))
      )
      const videoArrays = await Promise.all(videoPromises)
      videos = videoArrays.flat()
        .sort((a, b) => {
          // Handle data quality issue: ignore fake published_at dates that match created_at
          const isRealDateA = a.published_at && a.created_at && a.published_at !== a.created_at;
          const isRealDateB = b.published_at && b.created_at && b.published_at !== b.created_at;

          const dateA = isRealDateA ? new Date(a.published_at).getTime() : 0;
          const dateB = isRealDateB ? new Date(b.published_at).getTime() : 0;
          return dateB - dateA;
        })
        .slice(0, limit)    } else {
      // Get recent videos across all topics
      console.log('🎯 Calling getRecentVideos with limit:', limit)
      videos = await DatabaseService.getRecentVideos(limit)
      console.log('🎯 getRecentVideos returned:', videos.length, 'videos')
      console.log('🎯 First video sample:', videos[0])
    }    const responseData = { 
      videos,
      count: videos.length,
      topics: topics.length > 0 ? topics : 'all'
    }
      console.log('🎯 Final API response structure:', { 
      videosCount: videos.length, 
      videosIsArray: Array.isArray(videos),
      firstVideoTitle: videos[0]?.title,
      actualResponseKeys: Object.keys(responseData)
    })

    return NextResponse.json(responseData)
  } catch (error) {
    console.error('Error in recent videos API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch recent videos' },
      { status: 500 }
    )
  }
}
