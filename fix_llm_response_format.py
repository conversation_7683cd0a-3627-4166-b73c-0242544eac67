import os
import asyncio
import json
from supabase import create_client, Client
from dotenv import load_dotenv
from rich import print
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from datetime import datetime

load_dotenv()

console = Console()

# Initialize Supabase client - Using the simpler connection approach from the Ollama file
supabase = create_client(
    os.environ.get("SUPABASE_URL"),
    os.environ.get("SERVICE_ROLE_KEY")
)

# Tables to process
TABLES = [
    "youtube_artificial_intelligence",
    "youtube_renewable_energy",
    "youtube_gme",
    "youtube_sustainability",
    "youtube_startups",
    "youtube_financial_markets",
    "youtube_general",
    "youtube_legal"
]

async def process_table(table_name):
    print(f"\nProcessing table: {table_name}")
    
    try:
        # Get all rows with non-null llm_response
        response = supabase.table(table_name)\
            .select("*")\
            .not_.is_("llm_response", "null")\
            .eq("processed", "completed")\
            .execute()
        
        rows = response.data
        print(f"Found {len(rows)} rows with llm_response in {table_name}")
        
        processed = 0
        errors = 0
        skipped = 0
        
        for row in rows:
            try:
                llm_response = row.get('llm_response')
                
                # Skip if already in correct format
                if isinstance(llm_response, dict):
                    skipped += 1
                    continue
                
                # Convert string to dict
                if isinstance(llm_response, str):
                    llm_response_dict = json.loads(llm_response)
                    
                    # Update the database
                    supabase.table(table_name).update({
                        "llm_response": llm_response_dict
                    }).eq("id", row['id']).execute()
                    
                    processed += 1
                    # Print video_id instead of row id
                    print(f"Processed video: {row['video_id']} - {row['title'][:50]}...")
                
            except Exception as e:
                print(f"Error processing video {row['video_id']}: {str(e)}")
                errors += 1
            
            await asyncio.sleep(0.1)  # Small delay between rows
        
        print(f"\nStats for {table_name}:")
        print(f"Processed: {processed}")
        print(f"Skipped (already correct): {skipped}")
        print(f"Errors: {errors}")
        print(f"Total rows: {len(rows)}")
        
    except Exception as e:
        print(f"Error processing table {table_name}: {str(e)}")

async def main():
    start_time = datetime.now()
    print("[bold green]Starting LLM Response Format Fix[/bold green]")
    
    for table in TABLES:
        await process_table(table)
    
    end_time = datetime.now()
    duration = end_time - start_time
    print(f"\nTotal time taken: {duration}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nProcess interrupted by user")
    except Exception as e:
        print(f"Critical error: {str(e)}") 