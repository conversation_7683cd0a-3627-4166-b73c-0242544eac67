import { NextResponse } from 'next/server'
import { DatabaseService } from '@/lib/database'

export async function GET() {
  try {
    const topics = await DatabaseService.getTopicTables()
    
    return NextResponse.json({ 
      topics,
      count: topics.length
    })
  } catch (error) {
    console.error('Error in topics API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch topics' },
      { status: 500 }
    )
  }
}
