WEBVTT
Kind: captions
Language: en

00:00:01.040 --> 00:00:04.190 align:start position:0%
 
hello<00:00:01.319><c> everyone</c><00:00:02.200><c> Ravi</c><00:00:02.600><c> here</c><00:00:02.760><c> from</c><00:00:02.919><c> Lama</c><00:00:03.240><c> index</c>

00:00:04.190 --> 00:00:04.200 align:start position:0%
hello everyone Ravi here from <PERSON> index
 

00:00:04.200 --> 00:00:06.829 align:start position:0%
hello everyone Ravi here from <PERSON> index
welcome<00:00:04.520><c> to</c><00:00:04.759><c> this</c><00:00:04.960><c> video</c><00:00:05.240><c> series</c><00:00:05.560><c> on</c><00:00:06.040><c> um</c>

00:00:06.829 --> 00:00:06.839 align:start position:0%
welcome to this video series on um
 

00:00:06.839 --> 00:00:09.749 align:start position:0%
welcome to this video series on um
agents<00:00:07.839><c> so</c><00:00:08.200><c> to</c><00:00:08.400><c> start</c>

00:00:09.749 --> 00:00:09.759 align:start position:0%
agents so to start
 

00:00:09.759 --> 00:00:12.910 align:start position:0%
agents so to start
with<00:00:10.759><c> agents</c><00:00:11.599><c> which</c><00:00:11.759><c> we</c><00:00:11.920><c> call</c><00:00:12.200><c> data</c><00:00:12.480><c> agents</c><00:00:12.759><c> in</c>

00:00:12.910 --> 00:00:12.920 align:start position:0%
with agents which we call data agents in
 

00:00:12.920 --> 00:00:15.270 align:start position:0%
with agents which we call data agents in
Lama<00:00:13.200><c> index</c><00:00:13.880><c> are</c><00:00:14.400><c> specifically</c><00:00:14.879><c> knowledge</c>

00:00:15.270 --> 00:00:15.280 align:start position:0%
Lama index are specifically knowledge
 

00:00:15.280 --> 00:00:18.349 align:start position:0%
Lama index are specifically knowledge
workers<00:00:16.199><c> within</c><00:00:16.480><c> Lama</c><00:00:16.800><c> index</c><00:00:17.800><c> so</c><00:00:18.039><c> these</c><00:00:18.160><c> are</c>

00:00:18.349 --> 00:00:18.359 align:start position:0%
workers within Lama index so these are
 

00:00:18.359 --> 00:00:20.670 align:start position:0%
workers within Lama index so these are
designed<00:00:18.720><c> to</c><00:00:19.000><c> interact</c><00:00:19.480><c> with</c><00:00:19.800><c> various</c><00:00:20.400><c> types</c>

00:00:20.670 --> 00:00:20.680 align:start position:0%
designed to interact with various types
 

00:00:20.680 --> 00:00:23.509 align:start position:0%
designed to interact with various types
of<00:00:20.920><c> data</c><00:00:21.640><c> and</c><00:00:22.000><c> this</c><00:00:22.439><c> data</c><00:00:22.760><c> can</c><00:00:22.920><c> be</c>

00:00:23.509 --> 00:00:23.519 align:start position:0%
of data and this data can be
 

00:00:23.519 --> 00:00:26.470 align:start position:0%
of data and this data can be
unstructured<00:00:24.240><c> and</c><00:00:24.400><c> structured</c><00:00:25.279><c> data</c><00:00:26.279><c> and</c>

00:00:26.470 --> 00:00:26.480 align:start position:0%
unstructured and structured data and
 

00:00:26.480 --> 00:00:29.550 align:start position:0%
unstructured and structured data and
also<00:00:26.840><c> unlike</c><00:00:27.560><c> uh</c><00:00:27.840><c> query</c><00:00:28.199><c> engines</c><00:00:29.119><c> uh</c><00:00:29.279><c> in</c><00:00:29.400><c> a</c>

00:00:29.550 --> 00:00:29.560 align:start position:0%
also unlike uh query engines uh in a
 

00:00:29.560 --> 00:00:32.869 align:start position:0%
also unlike uh query engines uh in a
typical<00:00:30.039><c> rack</c><00:00:30.400><c> pipeline</c><00:00:31.199><c> data</c><00:00:31.759><c> agents</c>

00:00:32.869 --> 00:00:32.879 align:start position:0%
typical rack pipeline data agents
 

00:00:32.879 --> 00:00:35.389 align:start position:0%
typical rack pipeline data agents
can<00:00:33.879><c> dynamically</c><00:00:34.480><c> interact</c><00:00:34.920><c> with</c><00:00:35.120><c> data</c>

00:00:35.389 --> 00:00:35.399 align:start position:0%
can dynamically interact with data
 

00:00:35.399 --> 00:00:38.670 align:start position:0%
can dynamically interact with data
sources<00:00:36.399><c> and</c><00:00:36.879><c> even</c><00:00:37.200><c> external</c><00:00:37.600><c> API</c><00:00:38.040><c> sources</c><00:00:38.520><c> as</c>

00:00:38.670 --> 00:00:38.680 align:start position:0%
sources and even external API sources as
 

00:00:38.680 --> 00:00:42.510 align:start position:0%
sources and even external API sources as
well<00:00:39.680><c> so</c><00:00:40.079><c> they</c><00:00:40.239><c> can</c><00:00:40.520><c> ingest</c><00:00:41.039><c> new</c><00:00:41.360><c> data</c><00:00:42.039><c> and</c>

00:00:42.510 --> 00:00:42.520 align:start position:0%
well so they can ingest new data and
 

00:00:42.520 --> 00:00:44.350 align:start position:0%
well so they can ingest new data and
adapt<00:00:42.920><c> based</c><00:00:43.200><c> on</c><00:00:43.360><c> the</c><00:00:43.600><c> information</c><00:00:44.160><c> they</c>

00:00:44.350 --> 00:00:44.360 align:start position:0%
adapt based on the information they
 

00:00:44.360 --> 00:00:47.869 align:start position:0%
adapt based on the information they
process<00:00:44.879><c> from</c><00:00:45.120><c> all</c><00:00:45.399><c> these</c>

00:00:47.869 --> 00:00:47.879 align:start position:0%
process from all these
 

00:00:47.879 --> 00:00:52.110 align:start position:0%
process from all these
sources<00:00:48.879><c> so</c><00:00:49.760><c> in</c><00:00:50.160><c> simple</c><00:00:51.160><c> data</c><00:00:51.480><c> agents</c><00:00:51.879><c> have</c>

00:00:52.110 --> 00:00:52.120 align:start position:0%
sources so in simple data agents have
 

00:00:52.120 --> 00:00:55.349 align:start position:0%
sources so in simple data agents have
access<00:00:52.399><c> to</c><00:00:52.640><c> different</c><00:00:53.000><c> tools</c><00:00:53.719><c> like</c><00:00:54.280><c> uh</c><00:00:54.600><c> slack</c>

00:00:55.349 --> 00:00:55.359 align:start position:0%
access to different tools like uh slack
 

00:00:55.359 --> 00:00:59.509 align:start position:0%
access to different tools like uh slack
Gmail<00:00:56.359><c> or</c><00:00:57.000><c> uh</c><00:00:57.960><c> rag</c><00:00:58.320><c> tools</c><00:00:59.039><c> uh</c><00:00:59.160><c> which</c><00:00:59.280><c> are</c>

00:00:59.509 --> 00:00:59.519 align:start position:0%
Gmail or uh rag tools uh which are
 

00:00:59.519 --> 00:01:03.470 align:start position:0%
Gmail or uh rag tools uh which are
quering<00:01:00.000><c> engin</c><00:01:00.440><c> over</c><00:01:01.120><c> your</c><00:01:01.640><c> personal</c><00:01:02.239><c> data</c><00:01:03.239><c> uh</c>

00:01:03.470 --> 00:01:03.480 align:start position:0%
quering engin over your personal data uh
 

00:01:03.480 --> 00:01:07.270 align:start position:0%
quering engin over your personal data uh
like<00:01:04.199><c> basically</c><00:01:04.680><c> query</c><00:01:05.000><c> engine</c><00:01:05.479><c> tools</c><00:01:06.479><c> and</c>

00:01:07.270 --> 00:01:07.280 align:start position:0%
like basically query engine tools and
 

00:01:07.280 --> 00:01:09.429 align:start position:0%
like basically query engine tools and
these<00:01:07.759><c> agents</c><00:01:08.240><c> can</c><00:01:08.520><c> interact</c><00:01:08.960><c> with</c><00:01:09.159><c> these</c>

00:01:09.429 --> 00:01:09.439 align:start position:0%
these agents can interact with these
 

00:01:09.439 --> 00:01:11.630 align:start position:0%
these agents can interact with these
different<00:01:09.759><c> tools</c><00:01:10.320><c> to</c><00:01:10.600><c> complete</c><00:01:11.000><c> the</c><00:01:11.200><c> task</c>

00:01:11.630 --> 00:01:11.640 align:start position:0%
different tools to complete the task
 

00:01:11.640 --> 00:01:14.990 align:start position:0%
different tools to complete the task
based<00:01:11.920><c> on</c><00:01:12.119><c> the</c><00:01:12.280><c> user</c><00:01:13.040><c> message</c><00:01:14.040><c> right</c><00:01:14.680><c> so</c><00:01:14.880><c> this</c>

00:01:14.990 --> 00:01:15.000 align:start position:0%
based on the user message right so this
 

00:01:15.000 --> 00:01:17.870 align:start position:0%
based on the user message right so this
is<00:01:15.119><c> the</c><00:01:15.320><c> overall</c><00:01:15.759><c> idea</c><00:01:16.159><c> of</c><00:01:16.680><c> uh</c><00:01:17.000><c> data</c>

00:01:17.870 --> 00:01:17.880 align:start position:0%
is the overall idea of uh data
 

00:01:17.880 --> 00:01:21.830 align:start position:0%
is the overall idea of uh data
agents<00:01:18.880><c> and</c><00:01:19.080><c> then</c><00:01:19.799><c> uh</c><00:01:19.920><c> the</c><00:01:20.119><c> core</c><00:01:20.840><c> components</c>

00:01:21.830 --> 00:01:21.840 align:start position:0%
agents and then uh the core components
 

00:01:21.840 --> 00:01:24.749 align:start position:0%
agents and then uh the core components
uh<00:01:22.600><c> of</c><00:01:22.799><c> data</c><00:01:23.079><c> agents</c><00:01:23.400><c> are</c><00:01:23.880><c> one</c><00:01:24.159><c> is</c><00:01:24.640><c> uh</c>

00:01:24.749 --> 00:01:24.759 align:start position:0%
uh of data agents are one is uh
 

00:01:24.759 --> 00:01:28.510 align:start position:0%
uh of data agents are one is uh
reasoning<00:01:25.200><c> Loop</c><00:01:25.600><c> and</c><00:01:25.759><c> the</c><00:01:25.840><c> other</c><00:01:26.079><c> is</c><00:01:26.320><c> tool</c>

00:01:28.510 --> 00:01:28.520 align:start position:0%
reasoning Loop and the other is tool
 

00:01:28.520 --> 00:01:32.149 align:start position:0%
reasoning Loop and the other is tool
extractions<00:01:29.520><c> so</c><00:01:30.280><c> let's</c><00:01:30.479><c> see</c><00:01:30.799><c> how</c><00:01:31.079><c> it</c><00:01:31.240><c> works</c><00:01:31.880><c> so</c>

00:01:32.149 --> 00:01:32.159 align:start position:0%
extractions so let's see how it works so
 

00:01:32.159 --> 00:01:36.350 align:start position:0%
extractions so let's see how it works so
when<00:01:32.880><c> a</c><00:01:33.320><c> user</c><00:01:33.720><c> message</c><00:01:34.119><c> comes</c><00:01:34.640><c> to</c><00:01:34.880><c> an</c>

00:01:36.350 --> 00:01:36.360 align:start position:0%
when a user message comes to an
 

00:01:36.360 --> 00:01:39.389 align:start position:0%
when a user message comes to an
agent<00:01:37.360><c> it</c><00:01:37.560><c> basically</c><00:01:38.000><c> fetches</c><00:01:38.759><c> converation</c>

00:01:39.389 --> 00:01:39.399 align:start position:0%
agent it basically fetches converation
 

00:01:39.399 --> 00:01:41.590 align:start position:0%
agent it basically fetches converation
history<00:01:40.280><c> and</c><00:01:40.479><c> based</c><00:01:40.759><c> on</c><00:01:40.880><c> the</c><00:01:41.000><c> user</c><00:01:41.280><c> message</c>

00:01:41.590 --> 00:01:41.600 align:start position:0%
history and based on the user message
 

00:01:41.600 --> 00:01:44.030 align:start position:0%
history and based on the user message
and<00:01:41.799><c> conversation</c><00:01:42.479><c> history</c><00:01:43.479><c> uh</c><00:01:43.640><c> using</c>

00:01:44.030 --> 00:01:44.040 align:start position:0%
and conversation history uh using
 

00:01:44.040 --> 00:01:47.429 align:start position:0%
and conversation history uh using
reasoning<00:01:44.520><c> Loop</c><00:01:45.200><c> uh</c><00:01:45.360><c> it</c><00:01:45.520><c> selects</c><00:01:46.439><c> one</c><00:01:46.960><c> one</c><00:01:47.159><c> or</c>

00:01:47.429 --> 00:01:47.439 align:start position:0%
reasoning Loop uh it selects one one or
 

00:01:47.439 --> 00:01:49.830 align:start position:0%
reasoning Loop uh it selects one one or
more<00:01:47.799><c> tools</c><00:01:48.719><c> from</c><00:01:48.880><c> the</c><00:01:49.040><c> list</c><00:01:49.280><c> of</c><00:01:49.439><c> tools</c>

00:01:49.830 --> 00:01:49.840 align:start position:0%
more tools from the list of tools
 

00:01:49.840 --> 00:01:52.270 align:start position:0%
more tools from the list of tools
provided<00:01:50.479><c> to</c><00:01:50.680><c> complete</c><00:01:51.079><c> the</c><00:01:51.439><c> task</c><00:01:51.960><c> for</c><00:01:52.159><c> the</c>

00:01:52.270 --> 00:01:52.280 align:start position:0%
provided to complete the task for the
 

00:01:52.280 --> 00:01:55.230 align:start position:0%
provided to complete the task for the
given<00:01:52.560><c> user</c><00:01:53.159><c> message</c><00:01:54.159><c> so</c><00:01:54.360><c> this</c><00:01:54.479><c> is</c><00:01:54.680><c> the</c><00:01:54.920><c> whole</c>

00:01:55.230 --> 00:01:55.240 align:start position:0%
given user message so this is the whole
 

00:01:55.240 --> 00:01:58.149 align:start position:0%
given user message so this is the whole
end<00:01:55.479><c> to</c><00:01:55.560><c> end</c><00:01:55.799><c> workflow</c><00:01:56.240><c> for</c><00:01:56.479><c> any</c><00:01:57.000><c> agent</c>

00:01:58.149 --> 00:01:58.159 align:start position:0%
end to end workflow for any agent
 

00:01:58.159 --> 00:02:02.429 align:start position:0%
end to end workflow for any agent
abstraction<00:01:59.159><c> and</c><00:01:59.640><c> uh</c><00:02:00.200><c> so</c><00:02:01.200><c> here</c><00:02:02.000><c> reasoning</c>

00:02:02.429 --> 00:02:02.439 align:start position:0%
abstraction and uh so here reasoning
 

00:02:02.439 --> 00:02:05.950 align:start position:0%
abstraction and uh so here reasoning
Loop<00:02:02.880><c> is</c><00:02:03.719><c> um</c><00:02:04.399><c> decision</c><00:02:04.799><c> making</c><00:02:05.240><c> process</c><00:02:05.759><c> to</c>

00:02:05.950 --> 00:02:05.960 align:start position:0%
Loop is um decision making process to
 

00:02:05.960 --> 00:02:08.669 align:start position:0%
Loop is um decision making process to
determine<00:02:06.560><c> how</c><00:02:07.039><c> agent</c><00:02:07.479><c> interacts</c><00:02:08.160><c> with</c>

00:02:08.669 --> 00:02:08.679 align:start position:0%
determine how agent interacts with
 

00:02:08.679 --> 00:02:11.229 align:start position:0%
determine how agent interacts with
various<00:02:09.000><c> tools</c><00:02:09.360><c> provided</c><00:02:09.800><c> to</c>

00:02:11.229 --> 00:02:11.239 align:start position:0%
various tools provided to
 

00:02:11.239 --> 00:02:14.309 align:start position:0%
various tools provided to
agent<00:02:12.239><c> and</c><00:02:12.440><c> then</c><00:02:12.720><c> tool</c><00:02:13.120><c> abstractions</c><00:02:14.040><c> are</c>

00:02:14.309 --> 00:02:14.319 align:start position:0%
agent and then tool abstractions are
 

00:02:14.319 --> 00:02:16.509 align:start position:0%
agent and then tool abstractions are
basically<00:02:14.720><c> a</c><00:02:14.840><c> set</c><00:02:15.040><c> of</c><00:02:15.239><c> apis</c><00:02:15.720><c> or</c><00:02:16.000><c> tools</c><00:02:16.319><c> to</c>

00:02:16.509 --> 00:02:16.519 align:start position:0%
basically a set of apis or tools to
 

00:02:16.519 --> 00:02:20.350 align:start position:0%
basically a set of apis or tools to
fetch<00:02:17.080><c> data</c><00:02:17.560><c> or</c><00:02:18.519><c> alterate</c><00:02:19.080><c> making</c><00:02:19.480><c> inform</c>

00:02:20.350 --> 00:02:20.360 align:start position:0%
fetch data or alterate making inform
 

00:02:20.360 --> 00:02:23.070 align:start position:0%
fetch data or alterate making inform
decisions<00:02:20.800><c> based</c><00:02:21.040><c> on</c><00:02:21.160><c> the</c><00:02:21.440><c> task</c><00:02:22.440><c> uh</c><00:02:22.599><c> the</c><00:02:22.720><c> user</c>

00:02:23.070 --> 00:02:23.080 align:start position:0%
decisions based on the task uh the user
 

00:02:23.080 --> 00:02:24.750 align:start position:0%
decisions based on the task uh the user
provides<00:02:23.879><c> through</c><00:02:24.040><c> a</c>

00:02:24.750 --> 00:02:24.760 align:start position:0%
provides through a
 

00:02:24.760 --> 00:02:28.350 align:start position:0%
provides through a
message<00:02:25.760><c> right</c><00:02:26.400><c> so</c><00:02:26.800><c> on</c><00:02:27.000><c> the</c><00:02:27.280><c> reasoning</c><00:02:28.000><c> Loop</c>

00:02:28.350 --> 00:02:28.360 align:start position:0%
message right so on the reasoning Loop
 

00:02:28.360 --> 00:02:31.790 align:start position:0%
message right so on the reasoning Loop
end<00:02:29.319><c> currently</c><00:02:29.680><c> we</c><00:02:30.000><c> support</c><00:02:30.480><c> react</c><00:02:30.879><c> agent</c><00:02:31.560><c> and</c>

00:02:31.790 --> 00:02:31.800 align:start position:0%
end currently we support react agent and
 

00:02:31.800 --> 00:02:33.830 align:start position:0%
end currently we support react agent and
function<00:02:32.160><c> calling</c><00:02:32.560><c> agent</c><00:02:32.920><c> llm</c><00:02:33.319><c> compiler</c>

00:02:33.830 --> 00:02:33.840 align:start position:0%
function calling agent llm compiler
 

00:02:33.840 --> 00:02:37.990 align:start position:0%
function calling agent llm compiler
agent<00:02:34.680><c> so</c><00:02:34.879><c> react</c><00:02:35.319><c> agent</c><00:02:35.840><c> can</c><00:02:36.040><c> work</c><00:02:36.440><c> with</c><00:02:36.720><c> any</c><00:02:37.519><c> U</c>

00:02:37.990 --> 00:02:38.000 align:start position:0%
agent so react agent can work with any U
 

00:02:38.000 --> 00:02:41.110 align:start position:0%
agent so react agent can work with any U
chart<00:02:38.280><c> or</c><00:02:38.480><c> text</c><00:02:38.760><c> completion</c><00:02:39.720><c> element</c><00:02:40.319><c> point</c>

00:02:41.110 --> 00:02:41.120 align:start position:0%
chart or text completion element point
 

00:02:41.120 --> 00:02:43.190 align:start position:0%
chart or text completion element point
and<00:02:41.280><c> function</c><00:02:41.640><c> calling</c><00:02:42.239><c> agent</c><00:02:42.680><c> is</c><00:02:42.879><c> basically</c>

00:02:43.190 --> 00:02:43.200 align:start position:0%
and function calling agent is basically
 

00:02:43.200 --> 00:02:45.910 align:start position:0%
and function calling agent is basically
a<00:02:43.319><c> unified</c><00:02:43.760><c> abstraction</c><00:02:44.640><c> uh</c><00:02:44.800><c> that</c><00:02:44.920><c> uses</c><00:02:45.720><c> uh</c>

00:02:45.910 --> 00:02:45.920 align:start position:0%
a unified abstraction uh that uses uh
 

00:02:45.920 --> 00:02:47.430 align:start position:0%
a unified abstraction uh that uses uh
function<00:02:46.239><c> calling</c><00:02:46.760><c> capabilities</c><00:02:47.239><c> of</c>

00:02:47.430 --> 00:02:47.440 align:start position:0%
function calling capabilities of
 

00:02:47.440 --> 00:02:48.229 align:start position:0%
function calling capabilities of
different

00:02:48.229 --> 00:02:48.239 align:start position:0%
different
 

00:02:48.239 --> 00:02:52.430 align:start position:0%
different
llms<00:02:49.239><c> and</c><00:02:49.440><c> then</c><00:02:49.840><c> uh</c><00:02:50.040><c> llm</c><00:02:51.000><c> compiler</c><00:02:51.480><c> agent</c><00:02:52.239><c> uh</c>

00:02:52.430 --> 00:02:52.440 align:start position:0%
llms and then uh llm compiler agent uh
 

00:02:52.440 --> 00:02:55.390 align:start position:0%
llms and then uh llm compiler agent uh
enables<00:02:53.040><c> an</c><00:02:53.319><c> efficient</c><00:02:54.120><c> way</c><00:02:54.519><c> of</c><00:02:54.720><c> doing</c>

00:02:55.390 --> 00:02:55.400 align:start position:0%
enables an efficient way of doing
 

00:02:55.400 --> 00:02:58.509 align:start position:0%
enables an efficient way of doing
parallel<00:02:55.879><c> function</c><00:02:56.239><c> calling</c><00:02:56.800><c> with</c>

00:02:58.509 --> 00:02:58.519 align:start position:0%
parallel function calling with
 

00:02:58.519 --> 00:03:01.750 align:start position:0%
parallel function calling with
llms<00:02:59.519><c> and</c><00:02:59.680><c> then</c><00:02:59.879><c> then</c><00:03:00.159><c> on</c><00:03:00.840><c> tool</c><00:03:01.120><c> abstraction</c>

00:03:01.750 --> 00:03:01.760 align:start position:0%
llms and then then on tool abstraction
 

00:03:01.760 --> 00:03:04.750 align:start position:0%
llms and then then on tool abstraction
side<00:03:02.560><c> we</c><00:03:02.760><c> have</c><00:03:03.120><c> two</c><00:03:03.440><c> different</c><00:03:03.840><c> types</c><00:03:04.400><c> of</c>

00:03:04.750 --> 00:03:04.760 align:start position:0%
side we have two different types of
 

00:03:04.760 --> 00:03:07.589 align:start position:0%
side we have two different types of
tools<00:03:05.760><c> uh</c><00:03:05.840><c> so</c><00:03:06.080><c> function</c><00:03:06.480><c> tool</c><00:03:07.000><c> that</c><00:03:07.159><c> allows</c>

00:03:07.589 --> 00:03:07.599 align:start position:0%
tools uh so function tool that allows
 

00:03:07.599 --> 00:03:10.149 align:start position:0%
tools uh so function tool that allows
users<00:03:08.080><c> to</c><00:03:08.280><c> easily</c><00:03:08.640><c> convert</c><00:03:09.159><c> any</c><00:03:09.400><c> user</c><00:03:09.760><c> defined</c>

00:03:10.149 --> 00:03:10.159 align:start position:0%
users to easily convert any user defined
 

00:03:10.159 --> 00:03:12.949 align:start position:0%
users to easily convert any user defined
function<00:03:10.480><c> into</c><00:03:10.680><c> a</c><00:03:10.840><c> tool</c><00:03:11.799><c> and</c><00:03:12.040><c> then</c><00:03:12.319><c> qu</c><00:03:12.640><c> engine</c>

00:03:12.949 --> 00:03:12.959 align:start position:0%
function into a tool and then qu engine
 

00:03:12.959 --> 00:03:15.350 align:start position:0%
function into a tool and then qu engine
tool<00:03:13.840><c> is</c><00:03:14.000><c> something</c><00:03:14.400><c> that</c><00:03:14.640><c> perhaps</c><00:03:15.080><c> an</c>

00:03:15.350 --> 00:03:15.360 align:start position:0%
tool is something that perhaps an
 

00:03:15.360 --> 00:03:17.990 align:start position:0%
tool is something that perhaps an
existing<00:03:15.799><c> quiry</c><00:03:16.120><c> engine</c><00:03:16.599><c> or</c><00:03:17.400><c> basically</c><00:03:17.840><c> this</c>

00:03:17.990 --> 00:03:18.000 align:start position:0%
existing quiry engine or basically this
 

00:03:18.000 --> 00:03:20.110 align:start position:0%
existing quiry engine or basically this
is<00:03:18.720><c> specifically</c><00:03:19.200><c> converting</c><00:03:19.640><c> a</c><00:03:19.840><c> rack</c>

00:03:20.110 --> 00:03:20.120 align:start position:0%
is specifically converting a rack
 

00:03:20.120 --> 00:03:21.949 align:start position:0%
is specifically converting a rack
pipeline<00:03:20.599><c> into</c><00:03:20.840><c> a</c>

00:03:21.949 --> 00:03:21.959 align:start position:0%
pipeline into a
 

00:03:21.959 --> 00:03:25.830 align:start position:0%
pipeline into a
tool<00:03:22.959><c> and</c><00:03:23.120><c> then</c><00:03:23.319><c> we</c><00:03:23.480><c> have</c><00:03:24.040><c> uh</c><00:03:24.560><c> lot</c><00:03:24.760><c> of</c><00:03:24.920><c> tools</c>

00:03:25.830 --> 00:03:25.840 align:start position:0%
tool and then we have uh lot of tools
 

00:03:25.840 --> 00:03:28.110 align:start position:0%
tool and then we have uh lot of tools
available<00:03:26.640><c> in</c><00:03:26.799><c> lah</c><00:03:27.159><c> Hub</c><00:03:27.480><c> that</c><00:03:27.599><c> you</c><00:03:27.720><c> can</c><00:03:27.920><c> check</c>

00:03:28.110 --> 00:03:28.120 align:start position:0%
available in lah Hub that you can check
 

00:03:28.120 --> 00:03:31.110 align:start position:0%
available in lah Hub that you can check
out<00:03:29.040><c> uh</c><00:03:29.159><c> where</c><00:03:29.319><c> you</c><00:03:29.439><c> can</c><00:03:29.560><c> use</c><00:03:29.799><c> use</c><00:03:30.080><c> these</c><00:03:30.280><c> tools</c>

00:03:31.110 --> 00:03:31.120 align:start position:0%
out uh where you can use use these tools
 

00:03:31.120 --> 00:03:34.630 align:start position:0%
out uh where you can use use these tools
uh<00:03:31.319><c> and</c><00:03:31.560><c> then</c><00:03:32.040><c> uh</c><00:03:32.200><c> create</c><00:03:32.840><c> an</c><00:03:33.159><c> agent</c><00:03:34.159><c> framework</c>

00:03:34.630 --> 00:03:34.640 align:start position:0%
uh and then uh create an agent framework
 

00:03:34.640 --> 00:03:37.910 align:start position:0%
uh and then uh create an agent framework
for<00:03:35.080><c> yourself</c><00:03:36.080><c> like</c><00:03:36.560><c> as</c><00:03:36.680><c> I</c><00:03:36.840><c> mentioned</c><00:03:37.239><c> slack</c>

00:03:37.910 --> 00:03:37.920 align:start position:0%
for yourself like as I mentioned slack
 

00:03:37.920 --> 00:03:43.070 align:start position:0%
for yourself like as I mentioned slack
or<00:03:38.439><c> Gmail</c><00:03:39.439><c> or</c><00:03:40.159><c> any</c><00:03:40.439><c> Google</c><00:03:40.760><c> search</c><00:03:41.760><c> SARP</c><00:03:42.480><c> Bing</c>

00:03:43.070 --> 00:03:43.080 align:start position:0%
or Gmail or any Google search SARP Bing
 

00:03:43.080 --> 00:03:45.429 align:start position:0%
or Gmail or any Google search SARP Bing
uh<00:03:43.360><c> bing</c><00:03:44.080><c> as</c><00:03:44.280><c> well</c><00:03:44.680><c> so</c><00:03:44.879><c> there</c><00:03:45.000><c> are</c><00:03:45.120><c> a</c><00:03:45.200><c> lot</c><00:03:45.360><c> of</c>

00:03:45.429 --> 00:03:45.439 align:start position:0%
uh bing as well so there are a lot of
 

00:03:45.439 --> 00:03:47.830 align:start position:0%
uh bing as well so there are a lot of
tools<00:03:45.840><c> available</c><00:03:46.319><c> that</c><00:03:46.480><c> you</c><00:03:46.599><c> can</c><00:03:47.040><c> uh</c><00:03:47.200><c> make</c>

00:03:47.830 --> 00:03:47.840 align:start position:0%
tools available that you can uh make
 

00:03:47.840 --> 00:03:50.149 align:start position:0%
tools available that you can uh make
your<00:03:48.040><c> agent</c><00:03:48.400><c> interact</c><00:03:48.920><c> with</c><00:03:49.640><c> fetch</c><00:03:49.959><c> some</c>

00:03:50.149 --> 00:03:50.159 align:start position:0%
your agent interact with fetch some
 

00:03:50.159 --> 00:03:51.910 align:start position:0%
your agent interact with fetch some
information<00:03:50.599><c> or</c><00:03:50.799><c> process</c><00:03:51.120><c> some</c><00:03:51.360><c> information</c>

00:03:51.910 --> 00:03:51.920 align:start position:0%
information or process some information
 

00:03:51.920 --> 00:03:54.110 align:start position:0%
information or process some information
and<00:03:52.079><c> get</c><00:03:52.280><c> the</c><00:03:52.480><c> information</c><00:03:53.360><c> and</c><00:03:53.519><c> process</c><00:03:53.879><c> it</c>

00:03:54.110 --> 00:03:54.120 align:start position:0%
and get the information and process it
 

00:03:54.120 --> 00:03:57.990 align:start position:0%
and get the information and process it
again<00:03:55.120><c> right</c><00:03:56.040><c> so</c><00:03:57.040><c> you</c><00:03:57.159><c> can</c><00:03:57.319><c> check</c><00:03:57.640><c> lot</c><00:03:57.799><c> of</c>

00:03:57.990 --> 00:03:58.000 align:start position:0%
again right so you can check lot of
 

00:03:58.000 --> 00:04:00.149 align:start position:0%
again right so you can check lot of
these<00:03:58.200><c> different</c><00:03:58.439><c> tools</c><00:03:58.760><c> available</c><00:03:59.079><c> in</c><00:03:59.239><c> lahab</c>

00:04:00.149 --> 00:04:00.159 align:start position:0%
these different tools available in lahab
 

00:04:00.159 --> 00:04:03.429 align:start position:0%
these different tools available in lahab
uh<00:04:00.319><c> provided</c><00:04:00.959><c> in</c><00:04:01.040><c> the</c><00:04:01.200><c> link</c><00:04:01.519><c> here</c><00:04:02.239><c> and</c><00:04:02.480><c> then</c><00:04:03.280><c> in</c>

00:04:03.429 --> 00:04:03.439 align:start position:0%
uh provided in the link here and then in
 

00:04:03.439 --> 00:04:05.830 align:start position:0%
uh provided in the link here and then in
this<00:04:03.599><c> tutorial</c><00:04:04.000><c> series</c><00:04:04.640><c> uh</c><00:04:04.799><c> mainly</c><00:04:05.480><c> I'll</c><00:04:05.680><c> be</c>

00:04:05.830 --> 00:04:05.840 align:start position:0%
this tutorial series uh mainly I'll be
 

00:04:05.840 --> 00:04:09.270 align:start position:0%
this tutorial series uh mainly I'll be
concentrating<00:04:06.400><c> on</c><00:04:06.920><c> uh</c><00:04:07.360><c> react</c><00:04:07.840><c> agent</c><00:04:08.720><c> and</c><00:04:08.920><c> then</c>

00:04:09.270 --> 00:04:09.280 align:start position:0%
concentrating on uh react agent and then
 

00:04:09.280 --> 00:04:13.350 align:start position:0%
concentrating on uh react agent and then
function<00:04:09.680><c> calling</c><00:04:10.439><c> agent</c><00:04:11.439><c> uh</c><00:04:12.159><c> and</c><00:04:12.879><c> retrieval</c>

00:04:13.350 --> 00:04:13.360 align:start position:0%
function calling agent uh and retrieval
 

00:04:13.360 --> 00:04:14.910 align:start position:0%
function calling agent uh and retrieval
argumented<00:04:13.879><c> function</c><00:04:14.200><c> calling</c><00:04:14.519><c> agent</c>

00:04:14.910 --> 00:04:14.920 align:start position:0%
argumented function calling agent
 

00:04:14.920 --> 00:04:17.830 align:start position:0%
argumented function calling agent
wherein<00:04:15.400><c> you</c><00:04:15.560><c> have</c><00:04:15.959><c> set</c><00:04:16.160><c> of</c><00:04:16.359><c> tools</c><00:04:16.799><c> long</c><00:04:17.600><c> huge</c>

00:04:17.830 --> 00:04:17.840 align:start position:0%
wherein you have set of tools long huge
 

00:04:17.840 --> 00:04:19.990 align:start position:0%
wherein you have set of tools long huge
number<00:04:18.040><c> of</c><00:04:18.199><c> tools</c><00:04:18.959><c> and</c><00:04:19.120><c> based</c><00:04:19.400><c> on</c><00:04:19.519><c> the</c><00:04:19.639><c> user</c>

00:04:19.990 --> 00:04:20.000 align:start position:0%
number of tools and based on the user
 

00:04:20.000 --> 00:04:21.949 align:start position:0%
number of tools and based on the user
task<00:04:20.280><c> or</c><00:04:20.440><c> message</c><00:04:21.040><c> you</c><00:04:21.199><c> retrieve</c><00:04:21.600><c> some</c><00:04:21.759><c> of</c>

00:04:21.949 --> 00:04:21.959 align:start position:0%
task or message you retrieve some of
 

00:04:21.959 --> 00:04:24.749 align:start position:0%
task or message you retrieve some of
these<00:04:22.120><c> tools</c><00:04:22.600><c> and</c><00:04:22.880><c> compute</c><00:04:23.280><c> the</c><00:04:23.440><c> task</c><00:04:24.440><c> uh</c><00:04:24.560><c> so</c>

00:04:24.749 --> 00:04:24.759 align:start position:0%
these tools and compute the task uh so
 

00:04:24.759 --> 00:04:26.670 align:start position:0%
these tools and compute the task uh so
that's<00:04:24.960><c> how</c><00:04:25.440><c> retrial</c><00:04:25.880><c> argumented</c><00:04:26.400><c> function</c>

00:04:26.670 --> 00:04:26.680 align:start position:0%
that's how retrial argumented function
 

00:04:26.680 --> 00:04:29.430 align:start position:0%
that's how retrial argumented function
calling<00:04:27.040><c> agent</c><00:04:27.479><c> works</c><00:04:28.320><c> and</c><00:04:28.520><c> then</c><00:04:28.919><c> controlling</c>

00:04:29.430 --> 00:04:29.440 align:start position:0%
calling agent works and then controlling
 

00:04:29.440 --> 00:04:32.189 align:start position:0%
calling agent works and then controlling
agent<00:04:29.919><c> reasoning</c><00:04:30.280><c> Loop</c><00:04:30.880><c> so</c><00:04:31.280><c> sometimes</c><00:04:31.840><c> uh</c><00:04:32.039><c> you</c>

00:04:32.189 --> 00:04:32.199 align:start position:0%
agent reasoning Loop so sometimes uh you
 

00:04:32.199 --> 00:04:34.270 align:start position:0%
agent reasoning Loop so sometimes uh you
don't<00:04:32.479><c> want</c><00:04:32.880><c> your</c><00:04:33.080><c> tool</c><00:04:33.360><c> output</c><00:04:33.720><c> to</c><00:04:33.840><c> be</c><00:04:33.960><c> sent</c>

00:04:34.270 --> 00:04:34.280 align:start position:0%
don't want your tool output to be sent
 

00:04:34.280 --> 00:04:37.510 align:start position:0%
don't want your tool output to be sent
to<00:04:35.000><c> llm</c><00:04:36.000><c> uh</c><00:04:36.160><c> we'll</c><00:04:36.560><c> walk</c><00:04:36.800><c> through</c><00:04:37.000><c> an</c><00:04:37.199><c> ex</c><00:04:37.440><c> with</c>

00:04:37.510 --> 00:04:37.520 align:start position:0%
to llm uh we'll walk through an ex with
 

00:04:37.520 --> 00:04:40.390 align:start position:0%
to llm uh we'll walk through an ex with
an<00:04:37.919><c> example</c><00:04:38.919><c> uh</c><00:04:39.039><c> so</c><00:04:39.320><c> whenever</c><00:04:39.840><c> it</c><00:04:39.960><c> is</c><00:04:40.160><c> not</c>

00:04:40.390 --> 00:04:40.400 align:start position:0%
an example uh so whenever it is not
 

00:04:40.400 --> 00:04:43.469 align:start position:0%
an example uh so whenever it is not
needed<00:04:41.039><c> to</c><00:04:41.240><c> send</c><00:04:41.560><c> your</c><00:04:41.720><c> tool</c><00:04:41.960><c> output</c><00:04:42.400><c> to</c><00:04:43.000><c> um</c>

00:04:43.469 --> 00:04:43.479 align:start position:0%
needed to send your tool output to um
 

00:04:43.479 --> 00:04:46.950 align:start position:0%
needed to send your tool output to um
llm<00:04:44.479><c> we</c><00:04:44.720><c> basically</c><00:04:45.120><c> disable</c><00:04:45.800><c> uh</c><00:04:46.120><c> return</c><00:04:46.320><c> dict</c>

00:04:46.950 --> 00:04:46.960 align:start position:0%
llm we basically disable uh return dict
 

00:04:46.960 --> 00:04:49.189 align:start position:0%
llm we basically disable uh return dict
which<00:04:47.120><c> means</c><00:04:47.560><c> you</c><00:04:47.720><c> don't</c><00:04:48.440><c> uh</c><00:04:48.600><c> you</c><00:04:48.800><c> ask</c><00:04:49.080><c> the</c>

00:04:49.189 --> 00:04:49.199 align:start position:0%
which means you don't uh you ask the
 

00:04:49.199 --> 00:04:52.430 align:start position:0%
which means you don't uh you ask the
agent<00:04:49.560><c> not</c><00:04:49.720><c> to</c><00:04:49.880><c> send</c><00:04:50.160><c> your</c><00:04:51.160><c> output</c><00:04:51.639><c> to</c><00:04:51.840><c> the</c><00:04:52.240><c> uh</c>

00:04:52.430 --> 00:04:52.440 align:start position:0%
agent not to send your output to the uh
 

00:04:52.440 --> 00:04:56.710 align:start position:0%
agent not to send your output to the uh
tool<00:04:52.720><c> output</c><00:04:53.080><c> to</c><00:04:53.199><c> the</c><00:04:53.520><c> llm</c><00:04:54.680><c> um</c><00:04:55.680><c> right</c><00:04:56.199><c> so</c><00:04:56.520><c> and</c>

00:04:56.710 --> 00:04:56.720 align:start position:0%
tool output to the llm um right so and
 

00:04:56.720 --> 00:04:58.430 align:start position:0%
tool output to the llm um right so and
then<00:04:57.000><c> there</c><00:04:57.120><c> is</c><00:04:57.280><c> stepwise</c><00:04:57.800><c> controllable</c>

00:04:58.430 --> 00:04:58.440 align:start position:0%
then there is stepwise controllable
 

00:04:58.440 --> 00:05:02.150 align:start position:0%
then there is stepwise controllable
agent<00:04:58.880><c> as</c><00:04:59.039><c> well</c><00:04:59.479><c> um</c><00:04:59.800><c> which</c><00:05:00.000><c> is</c><00:05:00.720><c> uh</c><00:05:01.720><c> basically</c>

00:05:02.150 --> 00:05:02.160 align:start position:0%
agent as well um which is uh basically
 

00:05:02.160 --> 00:05:05.510 align:start position:0%
agent as well um which is uh basically
to<00:05:02.360><c> control</c><00:05:03.120><c> uh</c><00:05:03.280><c> The</c><00:05:03.400><c> Next</c><00:05:03.680><c> Step</c><00:05:04.479><c> taken</c><00:05:04.919><c> by</c><00:05:05.120><c> the</c>

00:05:05.510 --> 00:05:05.520 align:start position:0%
to control uh The Next Step taken by the
 

00:05:05.520 --> 00:05:08.469 align:start position:0%
to control uh The Next Step taken by the
agent<00:05:06.520><c> um</c><00:05:06.880><c> and</c><00:05:07.039><c> then</c><00:05:07.280><c> even</c><00:05:07.720><c> provide</c><00:05:08.000><c> a</c><00:05:08.160><c> human</c>

00:05:08.469 --> 00:05:08.479 align:start position:0%
agent um and then even provide a human
 

00:05:08.479 --> 00:05:11.590 align:start position:0%
agent um and then even provide a human
feedback<00:05:08.919><c> if</c><00:05:09.120><c> necessary</c><00:05:09.960><c> so</c><00:05:10.160><c> we</c><00:05:10.360><c> look</c><00:05:10.680><c> all</c><00:05:11.400><c> uh</c>

00:05:11.590 --> 00:05:11.600 align:start position:0%
feedback if necessary so we look all uh
 

00:05:11.600 --> 00:05:14.270 align:start position:0%
feedback if necessary so we look all uh
these<00:05:12.520><c> different</c><00:05:12.840><c> kind</c><00:05:13.039><c> of</c><00:05:13.240><c> Agents</c><00:05:14.039><c> with</c>

00:05:14.270 --> 00:05:14.280 align:start position:0%
these different kind of Agents with
 

00:05:14.280 --> 00:05:16.629 align:start position:0%
these different kind of Agents with
examples<00:05:14.840><c> with</c><00:05:15.039><c> different</c><00:05:15.320><c> llms</c><00:05:16.120><c> uh</c><00:05:16.240><c> with</c>

00:05:16.629 --> 00:05:16.639 align:start position:0%
examples with different llms uh with
 

00:05:16.639 --> 00:05:20.110 align:start position:0%
examples with different llms uh with
open<00:05:16.960><c> AA</c><00:05:17.280><c> mistal</c><00:05:17.960><c> and</c><00:05:18.759><c> anthropic</c><00:05:19.759><c> so</c><00:05:20.000><c> this</c>

00:05:20.110 --> 00:05:20.120 align:start position:0%
open AA mistal and anthropic so this
 

00:05:20.120 --> 00:05:22.550 align:start position:0%
open AA mistal and anthropic so this
will<00:05:20.240><c> be</c><00:05:20.360><c> an</c><00:05:20.560><c> interesting</c><00:05:21.080><c> series</c><00:05:21.479><c> on</c><00:05:21.720><c> agents</c>

00:05:22.550 --> 00:05:22.560 align:start position:0%
will be an interesting series on agents
 

00:05:22.560 --> 00:05:26.710 align:start position:0%
will be an interesting series on agents
um<00:05:23.360><c> do</c><00:05:23.680><c> watch</c><00:05:23.880><c> out</c><00:05:24.039><c> for</c><00:05:24.240><c> the</c><00:05:24.440><c> next</c><00:05:24.840><c> uh</c><00:05:25.440><c> video</c><00:05:26.440><c> um</c>

00:05:26.710 --> 00:05:26.720 align:start position:0%
um do watch out for the next uh video um
 

00:05:26.720 --> 00:05:31.639 align:start position:0%
um do watch out for the next uh video um
see<00:05:26.919><c> you</c><00:05:27.600><c> there</c><00:05:28.600><c> thank</c><00:05:28.800><c> you</c>

