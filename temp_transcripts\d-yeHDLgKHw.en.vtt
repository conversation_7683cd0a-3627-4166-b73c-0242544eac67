WEBVTT
Kind: captions
Language: en

00:00:00.480 --> 00:00:02.570 align:start position:0%
 
okay<00:00:01.140><c> in</c><00:00:01.620><c> this</c><00:00:01.740><c> video</c><00:00:01.860><c> we're</c><00:00:02.159><c> going</c><00:00:02.280><c> to</c><00:00:02.399><c> look</c>

00:00:02.570 --> 00:00:02.580 align:start position:0%
okay in this video we're going to look
 

00:00:02.580 --> 00:00:05.329 align:start position:0%
okay in this video we're going to look
at<00:00:02.760><c> using</c><00:00:03.240><c> the</c><00:00:03.419><c> summarization</c><00:00:04.080><c> Checker</c><00:00:04.740><c> and</c>

00:00:05.329 --> 00:00:05.339 align:start position:0%
at using the summarization Checker and
 

00:00:05.339 --> 00:00:07.130 align:start position:0%
at using the summarization Checker and
other<00:00:05.580><c> ways</c><00:00:05.880><c> that</c><00:00:06.060><c> you</c><00:00:06.240><c> can</c><00:00:06.359><c> deal</c><00:00:06.600><c> with</c>

00:00:07.130 --> 00:00:07.140 align:start position:0%
other ways that you can deal with
 

00:00:07.140 --> 00:00:09.589 align:start position:0%
other ways that you can deal with
hallucination<00:00:07.980><c> related</c><00:00:08.700><c> to</c><00:00:08.940><c> summarization</c>

00:00:09.589 --> 00:00:09.599 align:start position:0%
hallucination related to summarization
 

00:00:09.599 --> 00:00:12.110 align:start position:0%
hallucination related to summarization
in<00:00:10.080><c> the</c><00:00:10.200><c> large</c><00:00:10.320><c> language</c><00:00:10.559><c> models</c><00:00:11.160><c> so</c><00:00:11.880><c> you</c><00:00:12.000><c> can</c>

00:00:12.110 --> 00:00:12.120 align:start position:0%
in the large language models so you can
 

00:00:12.120 --> 00:00:13.789 align:start position:0%
in the large language models so you can
see<00:00:12.240><c> I've</c><00:00:12.420><c> just</c><00:00:12.540><c> got</c><00:00:12.660><c> normal</c><00:00:12.900><c> Imports</c><00:00:13.559><c> nothing</c>

00:00:13.789 --> 00:00:13.799 align:start position:0%
see I've just got normal Imports nothing
 

00:00:13.799 --> 00:00:15.169 align:start position:0%
see I've just got normal Imports nothing
different<00:00:14.280><c> there</c>

00:00:15.169 --> 00:00:15.179 align:start position:0%
different there
 

00:00:15.179 --> 00:00:17.570 align:start position:0%
different there
so<00:00:15.599><c> first</c><00:00:15.839><c> off</c><00:00:16.020><c> I'm</c><00:00:16.260><c> just</c><00:00:16.500><c> going</c><00:00:16.619><c> to</c><00:00:16.740><c> set</c><00:00:16.920><c> up</c><00:00:17.100><c> a</c>

00:00:17.570 --> 00:00:17.580 align:start position:0%
so first off I'm just going to set up a
 

00:00:17.580 --> 00:00:20.630 align:start position:0%
so first off I'm just going to set up a
very<00:00:17.760><c> simple</c><00:00:18.060><c> sort</c><00:00:18.660><c> of</c><00:00:18.900><c> idea</c><00:00:19.199><c> of</c><00:00:19.320><c> a</c><00:00:19.560><c> prompt</c><00:00:19.920><c> and</c>

00:00:20.630 --> 00:00:20.640 align:start position:0%
very simple sort of idea of a prompt and
 

00:00:20.640 --> 00:00:22.010 align:start position:0%
very simple sort of idea of a prompt and
this<00:00:21.060><c> is</c><00:00:21.119><c> going</c><00:00:21.240><c> to</c><00:00:21.300><c> be</c><00:00:21.420><c> like</c><00:00:21.539><c> just</c><00:00:21.779><c> getting</c>

00:00:22.010 --> 00:00:22.020 align:start position:0%
this is going to be like just getting
 

00:00:22.020 --> 00:00:23.810 align:start position:0%
this is going to be like just getting
some<00:00:22.320><c> facts</c><00:00:22.740><c> out</c><00:00:22.859><c> so</c><00:00:23.039><c> we</c><00:00:23.160><c> can</c><00:00:23.279><c> look</c><00:00:23.460><c> at</c><00:00:23.580><c> some</c>

00:00:23.810 --> 00:00:23.820 align:start position:0%
some facts out so we can look at some
 

00:00:23.820 --> 00:00:25.910 align:start position:0%
some facts out so we can look at some
things<00:00:23.939><c> so</c><00:00:24.359><c> rather</c><00:00:24.840><c> than</c><00:00:25.019><c> bringing</c><00:00:25.619><c> a</c><00:00:25.740><c> whole</c>

00:00:25.910 --> 00:00:25.920 align:start position:0%
things so rather than bringing a whole
 

00:00:25.920 --> 00:00:27.410 align:start position:0%
things so rather than bringing a whole
text<00:00:26.100><c> file</c><00:00:26.460><c> or</c><00:00:26.699><c> something</c><00:00:26.820><c> like</c><00:00:27.000><c> that</c><00:00:27.180><c> I've</c>

00:00:27.410 --> 00:00:27.420 align:start position:0%
text file or something like that I've
 

00:00:27.420 --> 00:00:29.810 align:start position:0%
text file or something like that I've
gone<00:00:27.599><c> for</c><00:00:27.840><c> an</c><00:00:28.019><c> article</c><00:00:28.439><c> so</c><00:00:29.099><c> this</c><00:00:29.340><c> is</c><00:00:29.460><c> an</c>

00:00:29.810 --> 00:00:29.820 align:start position:0%
gone for an article so this is an
 

00:00:29.820 --> 00:00:32.330 align:start position:0%
gone for an article so this is an
article<00:00:30.060><c> about</c><00:00:30.240><c> coinbase's</c><00:00:31.140><c> earnings</c><00:00:31.619><c> here</c>

00:00:32.330 --> 00:00:32.340 align:start position:0%
article about coinbase's earnings here
 

00:00:32.340 --> 00:00:33.770 align:start position:0%
article about coinbase's earnings here
and<00:00:32.640><c> you</c><00:00:32.880><c> can</c><00:00:33.000><c> go</c><00:00:33.120><c> through</c><00:00:33.300><c> and</c><00:00:33.420><c> have</c><00:00:33.600><c> a</c><00:00:33.660><c> look</c>

00:00:33.770 --> 00:00:33.780 align:start position:0%
and you can go through and have a look
 

00:00:33.780 --> 00:00:35.569 align:start position:0%
and you can go through and have a look
at<00:00:33.960><c> that</c><00:00:34.140><c> we</c><00:00:34.559><c> can</c><00:00:34.680><c> see</c><00:00:34.800><c> the</c><00:00:34.920><c> length</c><00:00:35.160><c> of</c><00:00:35.280><c> article</c>

00:00:35.569 --> 00:00:35.579 align:start position:0%
at that we can see the length of article
 

00:00:35.579 --> 00:00:37.549 align:start position:0%
at that we can see the length of article
in<00:00:35.760><c> the</c><00:00:35.880><c> number</c><00:00:36.000><c> of</c><00:00:36.180><c> characters</c><00:00:36.480><c> so</c><00:00:37.200><c> obviously</c>

00:00:37.549 --> 00:00:37.559 align:start position:0%
in the number of characters so obviously
 

00:00:37.559 --> 00:00:40.010 align:start position:0%
in the number of characters so obviously
any<00:00:37.739><c> summarizations</c><00:00:38.460><c> we</c><00:00:39.300><c> want</c><00:00:39.420><c> to</c><00:00:39.540><c> be</c><00:00:39.660><c> quite</c><00:00:39.840><c> a</c>

00:00:40.010 --> 00:00:40.020 align:start position:0%
any summarizations we want to be quite a
 

00:00:40.020 --> 00:00:41.630 align:start position:0%
any summarizations we want to be quite a
lot<00:00:40.140><c> less</c><00:00:40.680><c> than</c>

00:00:41.630 --> 00:00:41.640 align:start position:0%
lot less than
 

00:00:41.640 --> 00:00:43.310 align:start position:0%
lot less than
all<00:00:42.059><c> right</c><00:00:42.239><c> so</c><00:00:42.480><c> the</c><00:00:42.600><c> prompt</c><00:00:42.899><c> that</c><00:00:43.020><c> I'm</c><00:00:43.140><c> going</c>

00:00:43.310 --> 00:00:43.320 align:start position:0%
all right so the prompt that I'm going
 

00:00:43.320 --> 00:00:45.889 align:start position:0%
all right so the prompt that I'm going
to<00:00:43.440><c> use</c><00:00:43.680><c> is</c><00:00:44.399><c> basically</c><00:00:44.820><c> just</c><00:00:45.059><c> extract</c><00:00:45.480><c> the</c><00:00:45.719><c> key</c>

00:00:45.889 --> 00:00:45.899 align:start position:0%
to use is basically just extract the key
 

00:00:45.899 --> 00:00:48.290 align:start position:0%
to use is basically just extract the key
facts<00:00:46.320><c> out</c><00:00:46.500><c> of</c><00:00:46.680><c> this</c><00:00:47.040><c> text</c><00:00:47.219><c> don't</c><00:00:47.940><c> include</c>

00:00:48.290 --> 00:00:48.300 align:start position:0%
facts out of this text don't include
 

00:00:48.300 --> 00:00:51.049 align:start position:0%
facts out of this text don't include
opinions<00:00:48.780><c> give</c><00:00:49.620><c> each</c><00:00:49.920><c> fact</c><00:00:50.100><c> a</c><00:00:50.399><c> number</c><00:00:50.640><c> and</c>

00:00:51.049 --> 00:00:51.059 align:start position:0%
opinions give each fact a number and
 

00:00:51.059 --> 00:00:53.569 align:start position:0%
opinions give each fact a number and
keep<00:00:51.300><c> them</c><00:00:51.660><c> in</c><00:00:52.020><c> short</c><00:00:52.320><c> sentences</c><00:00:52.920><c> and</c><00:00:53.520><c> then</c>

00:00:53.569 --> 00:00:53.579 align:start position:0%
keep them in short sentences and then
 

00:00:53.579 --> 00:00:55.069 align:start position:0%
keep them in short sentences and then
we're<00:00:53.700><c> just</c><00:00:53.820><c> going</c><00:00:53.940><c> to</c><00:00:54.000><c> pass</c><00:00:54.180><c> in</c><00:00:54.360><c> the</c><00:00:54.660><c> article</c>

00:00:55.069 --> 00:00:55.079 align:start position:0%
we're just going to pass in the article
 

00:00:55.079 --> 00:00:58.130 align:start position:0%
we're just going to pass in the article
so<00:00:55.920><c> you</c><00:00:56.340><c> can</c><00:00:56.460><c> see</c><00:00:56.640><c> here</c><00:00:57.000><c> we're</c><00:00:57.180><c> doing</c><00:00:57.420><c> that</c><00:00:57.719><c> and</c>

00:00:58.130 --> 00:00:58.140 align:start position:0%
so you can see here we're doing that and
 

00:00:58.140 --> 00:00:59.810 align:start position:0%
so you can see here we're doing that and
we're<00:00:58.379><c> running</c><00:00:58.559><c> it</c><00:00:58.800><c> and</c><00:00:59.160><c> sure</c><00:00:59.340><c> enough</c><00:00:59.520><c> it</c>

00:00:59.810 --> 00:00:59.820 align:start position:0%
we're running it and sure enough it
 

00:00:59.820 --> 00:01:01.970 align:start position:0%
we're running it and sure enough it
gives<00:01:00.000><c> us</c><00:01:00.180><c> back</c><00:01:00.420><c> a</c><00:01:00.780><c> nice</c><00:01:00.899><c> list</c><00:01:01.199><c> of</c><00:01:01.440><c> facts</c><00:01:01.860><c> so</c>

00:01:01.970 --> 00:01:01.980 align:start position:0%
gives us back a nice list of facts so
 

00:01:01.980 --> 00:01:04.369 align:start position:0%
gives us back a nice list of facts so
we've<00:01:02.160><c> got</c><00:01:02.340><c> coinbase</c><00:01:02.879><c> released</c><00:01:03.600><c> it's</c><00:01:03.780><c> Q4</c>

00:01:04.369 --> 00:01:04.379 align:start position:0%
we've got coinbase released it's Q4
 

00:01:04.379 --> 00:01:07.550 align:start position:0%
we've got coinbase released it's Q4
earnings<00:01:05.339><c> and</c><00:01:06.119><c> this</c><00:01:06.360><c> is</c><00:01:06.479><c> often</c><00:01:06.840><c> a</c><00:01:07.140><c> much</c><00:01:07.320><c> better</c>

00:01:07.550 --> 00:01:07.560 align:start position:0%
earnings and this is often a much better
 

00:01:07.560 --> 00:01:10.670 align:start position:0%
earnings and this is often a much better
way<00:01:08.040><c> to</c><00:01:08.880><c> deal</c><00:01:09.420><c> with</c><00:01:09.720><c> sort</c><00:01:09.900><c> of</c><00:01:10.020><c> fact</c><00:01:10.200><c> checking</c>

00:01:10.670 --> 00:01:10.680 align:start position:0%
way to deal with sort of fact checking
 

00:01:10.680 --> 00:01:12.230 align:start position:0%
way to deal with sort of fact checking
and<00:01:10.979><c> you'll</c><00:01:11.100><c> see</c><00:01:11.220><c> that</c><00:01:11.340><c> in</c><00:01:11.580><c> the</c><00:01:11.700><c> summarization</c>

00:01:12.230 --> 00:01:12.240 align:start position:0%
and you'll see that in the summarization
 

00:01:12.240 --> 00:01:13.969 align:start position:0%
and you'll see that in the summarization
Checker<00:01:12.659><c> they</c><00:01:12.840><c> also</c><00:01:13.140><c> use</c><00:01:13.320><c> something</c><00:01:13.619><c> like</c>

00:01:13.969 --> 00:01:13.979 align:start position:0%
Checker they also use something like
 

00:01:13.979 --> 00:01:15.890 align:start position:0%
Checker they also use something like
this<00:01:14.220><c> the</c><00:01:14.760><c> reason</c><00:01:14.939><c> for</c><00:01:15.180><c> this</c><00:01:15.299><c> is</c><00:01:15.420><c> you</c><00:01:15.600><c> can</c><00:01:15.720><c> then</c>

00:01:15.890 --> 00:01:15.900 align:start position:0%
this the reason for this is you can then
 

00:01:15.900 --> 00:01:18.050 align:start position:0%
this the reason for this is you can then
basically<00:01:16.380><c> train</c><00:01:16.920><c> you</c><00:01:17.400><c> could</c><00:01:17.520><c> train</c><00:01:17.760><c> an</c>

00:01:18.050 --> 00:01:18.060 align:start position:0%
basically train you could train an
 

00:01:18.060 --> 00:01:20.690 align:start position:0%
basically train you could train an
external<00:01:18.479><c> model</c><00:01:18.720><c> to</c><00:01:19.439><c> look</c><00:01:19.560><c> at</c><00:01:19.740><c> the</c><00:01:20.100><c> two</c><00:01:20.280><c> or</c><00:01:20.640><c> you</c>

00:01:20.690 --> 00:01:20.700 align:start position:0%
external model to look at the two or you
 

00:01:20.700 --> 00:01:21.890 align:start position:0%
external model to look at the two or you
could<00:01:20.820><c> use</c><00:01:20.939><c> a</c><00:01:21.060><c> large</c><00:01:21.180><c> language</c><00:01:21.420><c> model</c><00:01:21.720><c> for</c>

00:01:21.890 --> 00:01:21.900 align:start position:0%
could use a large language model for
 

00:01:21.900 --> 00:01:24.170 align:start position:0%
could use a large language model for
this<00:01:22.080><c> as</c><00:01:22.200><c> well</c><00:01:22.380><c> we'll</c><00:01:22.860><c> see</c><00:01:23.100><c> but</c><00:01:23.759><c> you</c><00:01:23.880><c> can</c><00:01:24.000><c> also</c>

00:01:24.170 --> 00:01:24.180 align:start position:0%
this as well we'll see but you can also
 

00:01:24.180 --> 00:01:25.730 align:start position:0%
this as well we'll see but you can also
do<00:01:24.240><c> things</c><00:01:24.360><c> like</c><00:01:24.540><c> looking</c><00:01:24.720><c> up</c><00:01:24.960><c> graphs</c><00:01:25.560><c> and</c>

00:01:25.730 --> 00:01:25.740 align:start position:0%
do things like looking up graphs and
 

00:01:25.740 --> 00:01:26.990 align:start position:0%
do things like looking up graphs and
stuff<00:01:25.920><c> like</c><00:01:26.100><c> that</c><00:01:26.280><c> so</c><00:01:26.520><c> we'll</c><00:01:26.640><c> look</c><00:01:26.759><c> at</c><00:01:26.880><c> that</c>

00:01:26.990 --> 00:01:27.000 align:start position:0%
stuff like that so we'll look at that
 

00:01:27.000 --> 00:01:28.969 align:start position:0%
stuff like that so we'll look at that
right<00:01:27.299><c> at</c><00:01:27.479><c> the</c><00:01:27.600><c> end</c><00:01:27.720><c> as</c><00:01:27.900><c> well</c><00:01:28.080><c> we've</c><00:01:28.560><c> got</c><00:01:28.740><c> the</c>

00:01:28.969 --> 00:01:28.979 align:start position:0%
right at the end as well we've got the
 

00:01:28.979 --> 00:01:30.770 align:start position:0%
right at the end as well we've got the
facts<00:01:29.520><c> here</c><00:01:29.880><c> it's</c><00:01:30.240><c> very</c><00:01:30.420><c> importantly</c>

00:01:30.770 --> 00:01:30.780 align:start position:0%
facts here it's very importantly
 

00:01:30.780 --> 00:01:32.450 align:start position:0%
facts here it's very importantly
remember<00:01:31.020><c> that</c><00:01:31.320><c> when</c><00:01:31.740><c> you</c><00:01:31.860><c> do</c><00:01:31.979><c> something</c><00:01:32.159><c> like</c>

00:01:32.450 --> 00:01:32.460 align:start position:0%
remember that when you do something like
 

00:01:32.460 --> 00:01:34.910 align:start position:0%
remember that when you do something like
this<00:01:32.640><c> set</c><00:01:33.060><c> your</c><00:01:33.420><c> language</c><00:01:33.659><c> model</c><00:01:34.140><c> to</c><00:01:34.799><c> a</c>

00:01:34.910 --> 00:01:34.920 align:start position:0%
this set your language model to a
 

00:01:34.920 --> 00:01:36.890 align:start position:0%
this set your language model to a
temperature<00:01:35.159><c> of</c><00:01:35.520><c> zero</c><00:01:35.939><c> for</c><00:01:36.479><c> this</c><00:01:36.659><c> kind</c><00:01:36.780><c> of</c>

00:01:36.890 --> 00:01:36.900 align:start position:0%
temperature of zero for this kind of
 

00:01:36.900 --> 00:01:39.350 align:start position:0%
temperature of zero for this kind of
thing<00:01:37.500><c> so</c><00:01:38.100><c> let's</c><00:01:38.280><c> look</c><00:01:38.460><c> at</c><00:01:38.579><c> the</c><00:01:38.759><c> summarization</c>

00:01:39.350 --> 00:01:39.360 align:start position:0%
thing so let's look at the summarization
 

00:01:39.360 --> 00:01:41.390 align:start position:0%
thing so let's look at the summarization
Checker<00:01:39.900><c> Lang</c><00:01:40.079><c> chain</c><00:01:40.380><c> actually</c><00:01:40.619><c> has</c><00:01:41.100><c> a</c>

00:01:41.390 --> 00:01:41.400 align:start position:0%
Checker Lang chain actually has a
 

00:01:41.400 --> 00:01:44.450 align:start position:0%
Checker Lang chain actually has a
summarization<00:01:41.939><c> Checker</c><00:01:42.479><c> chain</c><00:01:42.720><c> built</c><00:01:43.380><c> in</c><00:01:43.500><c> and</c>

00:01:44.450 --> 00:01:44.460 align:start position:0%
summarization Checker chain built in and
 

00:01:44.460 --> 00:01:46.789 align:start position:0%
summarization Checker chain built in and
we<00:01:45.180><c> can</c><00:01:45.299><c> set</c><00:01:45.479><c> a</c><00:01:45.659><c> few</c><00:01:45.720><c> things</c><00:01:45.900><c> on</c><00:01:46.200><c> this</c><00:01:46.320><c> so</c><00:01:46.560><c> we</c>

00:01:46.789 --> 00:01:46.799 align:start position:0%
we can set a few things on this so we
 

00:01:46.799 --> 00:01:48.170 align:start position:0%
we can set a few things on this so we
set<00:01:46.920><c> up</c><00:01:47.100><c> our</c><00:01:47.220><c> language</c><00:01:47.400><c> model</c><00:01:47.759><c> with</c><00:01:48.000><c> a</c>

00:01:48.170 --> 00:01:48.180 align:start position:0%
set up our language model with a
 

00:01:48.180 --> 00:01:49.789 align:start position:0%
set up our language model with a
temperature<00:01:48.299><c> of</c><00:01:48.600><c> zero</c><00:01:49.020><c> we're</c><00:01:49.380><c> going</c><00:01:49.560><c> to</c><00:01:49.680><c> go</c>

00:01:49.789 --> 00:01:49.799 align:start position:0%
temperature of zero we're going to go
 

00:01:49.799 --> 00:01:51.710 align:start position:0%
temperature of zero we're going to go
for<00:01:49.920><c> verbose</c><00:01:50.460><c> equals</c><00:01:50.820><c> true</c><00:01:50.939><c> here</c><00:01:51.240><c> just</c><00:01:51.479><c> so</c><00:01:51.600><c> we</c>

00:01:51.710 --> 00:01:51.720 align:start position:0%
for verbose equals true here just so we
 

00:01:51.720 --> 00:01:53.090 align:start position:0%
for verbose equals true here just so we
can<00:01:51.840><c> see</c><00:01:51.960><c> the</c><00:01:52.140><c> outputs</c><00:01:52.619><c> and</c><00:01:52.740><c> understand</c>

00:01:53.090 --> 00:01:53.100 align:start position:0%
can see the outputs and understand
 

00:01:53.100 --> 00:01:55.190 align:start position:0%
can see the outputs and understand
what's<00:01:53.460><c> actually</c><00:01:53.700><c> going</c><00:01:54.000><c> on</c><00:01:54.240><c> and</c><00:01:54.899><c> then</c><00:01:54.960><c> we</c><00:01:55.079><c> can</c>

00:01:55.190 --> 00:01:55.200 align:start position:0%
what's actually going on and then we can
 

00:01:55.200 --> 00:01:56.990 align:start position:0%
what's actually going on and then we can
set<00:01:55.380><c> the</c><00:01:55.500><c> number</c><00:01:55.680><c> of</c><00:01:55.860><c> checks</c><00:01:56.340><c> that</c><00:01:56.520><c> we</c><00:01:56.700><c> want</c><00:01:56.820><c> it</c>

00:01:56.990 --> 00:01:57.000 align:start position:0%
set the number of checks that we want it
 

00:01:57.000 --> 00:01:58.670 align:start position:0%
set the number of checks that we want it
to<00:01:57.119><c> do</c><00:01:57.299><c> so</c><00:01:57.540><c> that's</c><00:01:57.659><c> basically</c><00:01:57.960><c> the</c><00:01:58.320><c> number</c><00:01:58.439><c> of</c>

00:01:58.670 --> 00:01:58.680 align:start position:0%
to do so that's basically the number of
 

00:01:58.680 --> 00:02:01.730 align:start position:0%
to do so that's basically the number of
passes<00:01:59.159><c> through</c><00:01:59.460><c> that</c><00:02:00.119><c> we</c><00:02:00.299><c> want</c><00:02:00.479><c> it</c><00:02:00.600><c> to</c><00:02:00.720><c> do</c><00:02:00.899><c> so</c>

00:02:01.730 --> 00:02:01.740 align:start position:0%
passes through that we want it to do so
 

00:02:01.740 --> 00:02:03.590 align:start position:0%
passes through that we want it to do so
we're<00:02:02.100><c> going</c><00:02:02.220><c> to</c><00:02:02.399><c> feed</c><00:02:02.520><c> in</c><00:02:02.759><c> the</c><00:02:02.939><c> article</c><00:02:03.299><c> the</c>

00:02:03.590 --> 00:02:03.600 align:start position:0%
we're going to feed in the article the
 

00:02:03.600 --> 00:02:05.870 align:start position:0%
we're going to feed in the article the
same<00:02:03.720><c> as</c><00:02:04.020><c> what</c><00:02:04.200><c> we</c><00:02:04.380><c> had</c><00:02:04.560><c> before</c><00:02:04.979><c> and</c><00:02:05.579><c> you</c><00:02:05.759><c> can</c>

00:02:05.870 --> 00:02:05.880 align:start position:0%
same as what we had before and you can
 

00:02:05.880 --> 00:02:07.969 align:start position:0%
same as what we had before and you can
see<00:02:06.000><c> that</c><00:02:06.180><c> okay</c><00:02:06.420><c> this</c><00:02:07.020><c> is</c><00:02:07.140><c> entering</c><00:02:07.619><c> the</c><00:02:07.740><c> chain</c>

00:02:07.969 --> 00:02:07.979 align:start position:0%
see that okay this is entering the chain
 

00:02:07.979 --> 00:02:10.669 align:start position:0%
see that okay this is entering the chain
we're<00:02:08.580><c> then</c><00:02:08.759><c> basically</c><00:02:09.119><c> using</c><00:02:09.660><c> their</c><00:02:10.259><c> prompt</c>

00:02:10.669 --> 00:02:10.679 align:start position:0%
we're then basically using their prompt
 

00:02:10.679 --> 00:02:13.309 align:start position:0%
we're then basically using their prompt
so<00:02:11.400><c> this</c><00:02:11.700><c> is</c><00:02:11.819><c> just</c><00:02:11.940><c> given</c><00:02:12.300><c> some</c><00:02:12.480><c> text</c><00:02:12.780><c> extract</c>

00:02:13.309 --> 00:02:13.319 align:start position:0%
so this is just given some text extract
 

00:02:13.319 --> 00:02:14.570 align:start position:0%
so this is just given some text extract
a<00:02:13.440><c> list</c><00:02:13.620><c> of</c><00:02:13.800><c> facts</c><00:02:14.040><c> so</c><00:02:14.160><c> they're</c><00:02:14.280><c> doing</c><00:02:14.400><c> the</c>

00:02:14.570 --> 00:02:14.580 align:start position:0%
a list of facts so they're doing the
 

00:02:14.580 --> 00:02:16.190 align:start position:0%
a list of facts so they're doing the
same<00:02:14.760><c> kind</c><00:02:14.940><c> of</c><00:02:15.060><c> thing</c><00:02:15.180><c> as</c><00:02:15.300><c> I</c><00:02:15.480><c> just</c><00:02:15.660><c> did</c><00:02:15.780><c> above</c>

00:02:16.190 --> 00:02:16.200 align:start position:0%
same kind of thing as I just did above
 

00:02:16.200 --> 00:02:18.290 align:start position:0%
same kind of thing as I just did above
format<00:02:16.980><c> your</c><00:02:17.160><c> output</c><00:02:17.459><c> is</c><00:02:17.640><c> bullet</c><00:02:17.940><c> points</c><00:02:18.239><c> or</c>

00:02:18.290 --> 00:02:18.300 align:start position:0%
format your output is bullet points or
 

00:02:18.300 --> 00:02:20.390 align:start position:0%
format your output is bullet points or
as<00:02:18.540><c> bulleted</c><00:02:19.020><c> list</c><00:02:19.200><c> and</c><00:02:19.800><c> then</c><00:02:19.920><c> there's</c><00:02:20.099><c> the</c>

00:02:20.390 --> 00:02:20.400 align:start position:0%
as bulleted list and then there's the
 

00:02:20.400 --> 00:02:22.610 align:start position:0%
as bulleted list and then there's the
article<00:02:20.760><c> being</c><00:02:21.000><c> passed</c><00:02:21.540><c> in</c><00:02:21.599><c> and</c><00:02:22.140><c> we</c><00:02:22.260><c> can</c><00:02:22.440><c> see</c>

00:02:22.610 --> 00:02:22.620 align:start position:0%
article being passed in and we can see
 

00:02:22.620 --> 00:02:24.530 align:start position:0%
article being passed in and we can see
the<00:02:22.800><c> output</c><00:02:23.160><c> from</c><00:02:23.459><c> this</c><00:02:23.700><c> is</c><00:02:23.940><c> actually</c><00:02:24.239><c> pretty</c>

00:02:24.530 --> 00:02:24.540 align:start position:0%
the output from this is actually pretty
 

00:02:24.540 --> 00:02:27.290 align:start position:0%
the output from this is actually pretty
similar<00:02:25.080><c> to</c><00:02:25.440><c> what</c><00:02:25.739><c> we</c><00:02:25.920><c> got</c><00:02:26.160><c> just</c><00:02:26.700><c> using</c><00:02:27.060><c> a</c>

00:02:27.290 --> 00:02:27.300 align:start position:0%
similar to what we got just using a
 

00:02:27.300 --> 00:02:29.390 align:start position:0%
similar to what we got just using a
straight<00:02:27.480><c> llm</c><00:02:28.260><c> chain</c><00:02:28.500><c> here</c><00:02:28.800><c> the</c><00:02:29.280><c> difference</c>

00:02:29.390 --> 00:02:29.400 align:start position:0%
straight llm chain here the difference
 

00:02:29.400 --> 00:02:31.729 align:start position:0%
straight llm chain here the difference
is<00:02:29.700><c> that</c><00:02:29.940><c> with</c><00:02:30.180><c> now</c><00:02:30.480><c> it's</c><00:02:31.020><c> passing</c><00:02:31.500><c> that</c>

00:02:31.729 --> 00:02:31.739 align:start position:0%
is that with now it's passing that
 

00:02:31.739 --> 00:02:34.670 align:start position:0%
is that with now it's passing that
output<00:02:32.160><c> into</c><00:02:32.640><c> a</c><00:02:33.060><c> new</c><00:02:33.239><c> chain</c><00:02:33.540><c> or</c><00:02:34.020><c> into</c><00:02:34.140><c> a</c><00:02:34.440><c> new</c>

00:02:34.670 --> 00:02:34.680 align:start position:0%
output into a new chain or into a new
 

00:02:34.680 --> 00:02:37.250 align:start position:0%
output into a new chain or into a new
llm<00:02:35.220><c> chain</c><00:02:35.520><c> where</c><00:02:36.300><c> it's</c><00:02:36.660><c> basically</c><00:02:36.959><c> setting</c>

00:02:37.250 --> 00:02:37.260 align:start position:0%
llm chain where it's basically setting
 

00:02:37.260 --> 00:02:39.229 align:start position:0%
llm chain where it's basically setting
the<00:02:37.319><c> code</c><00:02:37.440><c> contacts</c><00:02:38.220><c> that</c><00:02:38.400><c> you</c><00:02:38.580><c> are</c><00:02:38.700><c> an</c><00:02:38.879><c> expert</c>

00:02:39.229 --> 00:02:39.239 align:start position:0%
the code contacts that you are an expert
 

00:02:39.239 --> 00:02:41.750 align:start position:0%
the code contacts that you are an expert
fact<00:02:39.540><c> Checker</c><00:02:40.080><c> you've</c><00:02:40.560><c> been</c><00:02:40.680><c> hired</c><00:02:41.160><c> by</c><00:02:41.459><c> Major</c>

00:02:41.750 --> 00:02:41.760 align:start position:0%
fact Checker you've been hired by Major
 

00:02:41.760 --> 00:02:44.449 align:start position:0%
fact Checker you've been hired by Major
news<00:02:42.060><c> organization</c><00:02:42.840><c> to</c><00:02:43.500><c> fact</c><00:02:43.739><c> check</c><00:02:43.980><c> a</c><00:02:44.340><c> very</c>

00:02:44.449 --> 00:02:44.459 align:start position:0%
news organization to fact check a very
 

00:02:44.459 --> 00:02:46.790 align:start position:0%
news organization to fact check a very
important<00:02:44.940><c> story</c><00:02:45.300><c> and</c><00:02:45.959><c> then</c><00:02:46.080><c> here</c><00:02:46.319><c> is</c><00:02:46.500><c> a</c><00:02:46.680><c> list</c>

00:02:46.790 --> 00:02:46.800 align:start position:0%
important story and then here is a list
 

00:02:46.800 --> 00:02:49.729 align:start position:0%
important story and then here is a list
of<00:02:46.980><c> bullet</c><00:02:47.519><c> points</c><00:02:47.879><c> and</c><00:02:48.660><c> for</c><00:02:49.260><c> each</c><00:02:49.440><c> fat</c>

00:02:49.729 --> 00:02:49.739 align:start position:0%
of bullet points and for each fat
 

00:02:49.739 --> 00:02:51.770 align:start position:0%
of bullet points and for each fat
determine<00:02:50.340><c> whether</c><00:02:50.519><c> it</c><00:02:50.760><c> is</c><00:02:51.000><c> true</c><00:02:51.180><c> or</c><00:02:51.360><c> false</c>

00:02:51.770 --> 00:02:51.780 align:start position:0%
determine whether it is true or false
 

00:02:51.780 --> 00:02:54.170 align:start position:0%
determine whether it is true or false
about<00:02:52.379><c> the</c><00:02:52.680><c> subject</c><00:02:52.980><c> if</c><00:02:53.459><c> you</c><00:02:53.580><c> are</c><00:02:53.700><c> unable</c><00:02:54.000><c> to</c>

00:02:54.170 --> 00:02:54.180 align:start position:0%
about the subject if you are unable to
 

00:02:54.180 --> 00:02:55.910 align:start position:0%
about the subject if you are unable to
determine<00:02:54.540><c> the</c><00:02:54.720><c> fact</c><00:02:54.900><c> is</c><00:02:55.140><c> true</c><00:02:55.319><c> or</c><00:02:55.560><c> false</c>

00:02:55.910 --> 00:02:55.920 align:start position:0%
determine the fact is true or false
 

00:02:55.920 --> 00:02:59.150 align:start position:0%
determine the fact is true or false
output<00:02:56.640><c> undetermined</c><00:02:57.480><c> if</c><00:02:57.900><c> the</c><00:02:58.019><c> fact</c><00:02:58.140><c> is</c><00:02:58.319><c> false</c>

00:02:59.150 --> 00:02:59.160 align:start position:0%
output undetermined if the fact is false
 

00:02:59.160 --> 00:03:01.729 align:start position:0%
output undetermined if the fact is false
explain<00:02:59.340><c> why</c><00:02:59.940><c> and</c><00:03:00.720><c> we</c><00:03:01.019><c> can</c><00:03:01.140><c> see</c><00:03:01.260><c> that</c><00:03:01.500><c> sure</c>

00:03:01.729 --> 00:03:01.739 align:start position:0%
explain why and we can see that sure
 

00:03:01.739 --> 00:03:03.949 align:start position:0%
explain why and we can see that sure
enough<00:03:01.920><c> it</c><00:03:02.400><c> goes</c><00:03:02.760><c> through</c><00:03:02.940><c> and</c><00:03:03.540><c> it's</c><00:03:03.720><c> gone</c>

00:03:03.949 --> 00:03:03.959 align:start position:0%
enough it goes through and it's gone
 

00:03:03.959 --> 00:03:06.830 align:start position:0%
enough it goes through and it's gone
through<00:03:04.200><c> each</c><00:03:04.440><c> of</c><00:03:04.620><c> these</c><00:03:04.860><c> and</c><00:03:05.640><c> put</c><00:03:06.120><c> the</c><00:03:06.480><c> true</c>

00:03:06.830 --> 00:03:06.840 align:start position:0%
through each of these and put the true
 

00:03:06.840 --> 00:03:08.809 align:start position:0%
through each of these and put the true
next<00:03:07.140><c> to</c><00:03:07.319><c> each</c><00:03:07.560><c> of</c><00:03:07.680><c> these</c><00:03:07.860><c> because</c><00:03:08.099><c> the</c><00:03:08.400><c> facts</c>

00:03:08.809 --> 00:03:08.819 align:start position:0%
next to each of these because the facts
 

00:03:08.819 --> 00:03:10.309 align:start position:0%
next to each of these because the facts
I<00:03:08.940><c> think</c><00:03:09.060><c> in</c><00:03:09.239><c> this</c><00:03:09.360><c> article</c><00:03:09.780><c> are</c><00:03:09.959><c> reasonably</c>

00:03:10.309 --> 00:03:10.319 align:start position:0%
I think in this article are reasonably
 

00:03:10.319 --> 00:03:12.530 align:start position:0%
I think in this article are reasonably
simple<00:03:10.680><c> it's</c><00:03:11.220><c> gone</c><00:03:11.459><c> through</c><00:03:11.700><c> it</c><00:03:11.879><c> and</c><00:03:12.120><c> put</c><00:03:12.360><c> true</c>

00:03:12.530 --> 00:03:12.540 align:start position:0%
simple it's gone through it and put true
 

00:03:12.540 --> 00:03:14.270 align:start position:0%
simple it's gone through it and put true
next<00:03:12.840><c> to</c><00:03:13.019><c> each</c><00:03:13.140><c> of</c><00:03:13.260><c> these</c><00:03:13.500><c> now</c><00:03:13.860><c> here's</c><00:03:14.220><c> the</c>

00:03:14.270 --> 00:03:14.280 align:start position:0%
next to each of these now here's the
 

00:03:14.280 --> 00:03:15.830 align:start position:0%
next to each of these now here's the
thing<00:03:14.400><c> sometimes</c><00:03:14.760><c> you'll</c><00:03:15.060><c> find</c><00:03:15.239><c> that</c><00:03:15.480><c> it</c><00:03:15.599><c> does</c>

00:03:15.830 --> 00:03:15.840 align:start position:0%
thing sometimes you'll find that it does
 

00:03:15.840 --> 00:03:18.410 align:start position:0%
thing sometimes you'll find that it does
get<00:03:16.260><c> some</c><00:03:16.440><c> of</c><00:03:16.620><c> these</c><00:03:16.860><c> wrong</c><00:03:17.220><c> right</c><00:03:17.819><c> so</c><00:03:18.060><c> this</c><00:03:18.300><c> is</c>

00:03:18.410 --> 00:03:18.420 align:start position:0%
get some of these wrong right so this is
 

00:03:18.420 --> 00:03:21.050 align:start position:0%
get some of these wrong right so this is
not<00:03:18.599><c> a</c><00:03:18.780><c> hundred</c><00:03:19.019><c> percent</c><00:03:19.200><c> guaranteed</c><00:03:20.040><c> way</c><00:03:20.519><c> to</c>

00:03:21.050 --> 00:03:21.060 align:start position:0%
not a hundred percent guaranteed way to
 

00:03:21.060 --> 00:03:23.270 align:start position:0%
not a hundred percent guaranteed way to
stop<00:03:21.180><c> errors</c><00:03:21.659><c> but</c><00:03:21.900><c> it</c><00:03:22.200><c> will</c><00:03:22.379><c> stop</c><00:03:22.860><c> quite</c><00:03:23.159><c> a</c>

00:03:23.270 --> 00:03:23.280 align:start position:0%
stop errors but it will stop quite a
 

00:03:23.280 --> 00:03:25.130 align:start position:0%
stop errors but it will stop quite a
number<00:03:23.400><c> of</c><00:03:23.580><c> Errors</c><00:03:23.940><c> then</c><00:03:24.540><c> we</c><00:03:24.659><c> can</c><00:03:24.780><c> pass</c><00:03:24.959><c> it</c>

00:03:25.130 --> 00:03:25.140 align:start position:0%
number of Errors then we can pass it
 

00:03:25.140 --> 00:03:26.509 align:start position:0%
number of Errors then we can pass it
we're<00:03:25.379><c> passing</c><00:03:25.620><c> in</c><00:03:25.680><c> the</c><00:03:25.860><c> original</c><00:03:25.980><c> summary</c>

00:03:26.509 --> 00:03:26.519 align:start position:0%
we're passing in the original summary
 

00:03:26.519 --> 00:03:28.970 align:start position:0%
we're passing in the original summary
there<00:03:27.300><c> and</c><00:03:27.659><c> this</c><00:03:27.959><c> final</c><00:03:28.140><c> one</c><00:03:28.379><c> we're</c><00:03:28.620><c> basically</c>

00:03:28.970 --> 00:03:28.980 align:start position:0%
there and this final one we're basically
 

00:03:28.980 --> 00:03:30.589 align:start position:0%
there and this final one we're basically
saying<00:03:29.220><c> blush</c><00:03:29.580><c> some</c><00:03:29.819><c> assertions</c><00:03:30.180><c> that</c><00:03:30.420><c> have</c>

00:03:30.589 --> 00:03:30.599 align:start position:0%
saying blush some assertions that have
 

00:03:30.599 --> 00:03:32.809 align:start position:0%
saying blush some assertions that have
been<00:03:30.780><c> fact</c><00:03:31.140><c> checked</c><00:03:31.560><c> and</c><00:03:31.739><c> labeled</c><00:03:32.099><c> as</c><00:03:32.400><c> true</c><00:03:32.580><c> or</c>

00:03:32.809 --> 00:03:32.819 align:start position:0%
been fact checked and labeled as true or
 

00:03:32.819 --> 00:03:34.910 align:start position:0%
been fact checked and labeled as true or
false<00:03:33.060><c> if</c><00:03:33.360><c> the</c><00:03:33.540><c> answer</c><00:03:33.659><c> is</c><00:03:33.840><c> false</c><00:03:34.379><c> a</c>

00:03:34.910 --> 00:03:34.920 align:start position:0%
false if the answer is false a
 

00:03:34.920 --> 00:03:37.369 align:start position:0%
false if the answer is false a
suggestion<00:03:35.400><c> is</c><00:03:35.580><c> given</c><00:03:35.879><c> for</c><00:03:36.180><c> a</c><00:03:36.360><c> correction</c><00:03:36.720><c> now</c>

00:03:37.369 --> 00:03:37.379 align:start position:0%
suggestion is given for a correction now
 

00:03:37.379 --> 00:03:41.589 align:start position:0%
suggestion is given for a correction now
Now<00:03:38.040><c> using</c><00:03:38.700><c> those</c><00:03:39.060><c> and</c><00:03:39.840><c> using</c><00:03:40.379><c> the</c><00:03:40.620><c> original</c>

00:03:41.589 --> 00:03:41.599 align:start position:0%
Now using those and using the original
 

00:03:41.599 --> 00:03:44.570 align:start position:0%
Now using those and using the original
article<00:03:42.599><c> that</c><00:03:42.780><c> we've</c><00:03:42.959><c> passed</c><00:03:43.379><c> in</c><00:03:43.500><c> now</c><00:03:44.220><c> we're</c>

00:03:44.570 --> 00:03:44.580 align:start position:0%
article that we've passed in now we're
 

00:03:44.580 --> 00:03:47.030 align:start position:0%
article that we've passed in now we're
basically<00:03:45.000><c> using</c><00:03:45.780><c> these</c><00:03:46.140><c> check</c><00:03:46.319><c> decisions</c><00:03:46.739><c> to</c>

00:03:47.030 --> 00:03:47.040 align:start position:0%
basically using these check decisions to
 

00:03:47.040 --> 00:03:48.830 align:start position:0%
basically using these check decisions to
rewrite<00:03:47.340><c> the</c><00:03:47.640><c> original</c><00:03:47.760><c> summary</c><00:03:48.360><c> to</c><00:03:48.720><c> be</c>

00:03:48.830 --> 00:03:48.840 align:start position:0%
rewrite the original summary to be
 

00:03:48.840 --> 00:03:50.690 align:start position:0%
rewrite the original summary to be
completely<00:03:49.260><c> true</c><00:03:49.560><c> and</c><00:03:50.099><c> then</c><00:03:50.220><c> we</c><00:03:50.340><c> go</c><00:03:50.519><c> through</c>

00:03:50.690 --> 00:03:50.700 align:start position:0%
completely true and then we go through
 

00:03:50.700 --> 00:03:53.390 align:start position:0%
completely true and then we go through
and<00:03:51.239><c> then</c><00:03:51.360><c> finally</c><00:03:52.140><c> once</c><00:03:52.680><c> we've</c><00:03:52.860><c> got</c><00:03:52.980><c> that</c><00:03:53.220><c> out</c>

00:03:53.390 --> 00:03:53.400 align:start position:0%
and then finally once we've got that out
 

00:03:53.400 --> 00:03:55.550 align:start position:0%
and then finally once we've got that out
we've<00:03:54.060><c> got</c><00:03:54.180><c> just</c><00:03:54.540><c> the</c><00:03:54.900><c> language</c><00:03:55.019><c> model</c>

00:03:55.550 --> 00:03:55.560 align:start position:0%
we've got just the language model
 

00:03:55.560 --> 00:03:57.530 align:start position:0%
we've got just the language model
checking<00:03:56.159><c> again</c><00:03:56.400><c> how</c><00:03:56.819><c> it</c><00:03:56.940><c> does</c><00:03:57.180><c> the</c>

00:03:57.530 --> 00:03:57.540 align:start position:0%
checking again how it does the
 

00:03:57.540 --> 00:04:00.649 align:start position:0%
checking again how it does the
associations<00:03:58.319><c> to</c><00:03:59.159><c> check</c><00:03:59.340><c> these</c><00:03:59.819><c> as</c><00:04:00.360><c> we</c><00:04:00.540><c> go</c>

00:04:00.649 --> 00:04:00.659 align:start position:0%
associations to check these as we go
 

00:04:00.659 --> 00:04:02.750 align:start position:0%
associations to check these as we go
through<00:04:00.780><c> and</c><00:04:01.200><c> it's</c><00:04:01.379><c> done</c><00:04:01.799><c> each</c><00:04:02.340><c> of</c><00:04:02.519><c> these</c>

00:04:02.750 --> 00:04:02.760 align:start position:0%
through and it's done each of these
 

00:04:02.760 --> 00:04:04.490 align:start position:0%
through and it's done each of these
going<00:04:03.180><c> through</c><00:04:03.420><c> and</c><00:04:03.659><c> then</c><00:04:03.780><c> finally</c><00:04:04.080><c> it</c><00:04:04.260><c> gives</c>

00:04:04.490 --> 00:04:04.500 align:start position:0%
going through and then finally it gives
 

00:04:04.500 --> 00:04:06.710 align:start position:0%
going through and then finally it gives
us<00:04:04.680><c> out</c><00:04:04.920><c> our</c><00:04:05.400><c> summary</c><00:04:05.760><c> which</c><00:04:06.239><c> is</c><00:04:06.360><c> clearly</c>

00:04:06.710 --> 00:04:06.720 align:start position:0%
us out our summary which is clearly
 

00:04:06.720 --> 00:04:08.869 align:start position:0%
us out our summary which is clearly
about<00:04:06.959><c> a</c><00:04:07.260><c> third</c><00:04:07.440><c> as</c><00:04:08.040><c> long</c><00:04:08.159><c> as</c><00:04:08.280><c> it</c><00:04:08.459><c> was</c><00:04:08.580><c> before</c>

00:04:08.869 --> 00:04:08.879 align:start position:0%
about a third as long as it was before
 

00:04:08.879 --> 00:04:11.449 align:start position:0%
about a third as long as it was before
now<00:04:09.720><c> what</c><00:04:10.319><c> you</c><00:04:10.439><c> can</c><00:04:10.620><c> do</c><00:04:10.799><c> is</c><00:04:10.920><c> you</c><00:04:11.159><c> can</c><00:04:11.280><c> play</c>

00:04:11.449 --> 00:04:11.459 align:start position:0%
now what you can do is you can play
 

00:04:11.459 --> 00:04:13.429 align:start position:0%
now what you can do is you can play
around<00:04:11.640><c> with</c><00:04:12.000><c> these</c><00:04:12.239><c> prompts</c><00:04:12.599><c> so</c><00:04:12.900><c> let's</c><00:04:13.200><c> look</c>

00:04:13.429 --> 00:04:13.439 align:start position:0%
around with these prompts so let's look
 

00:04:13.439 --> 00:04:15.890 align:start position:0%
around with these prompts so let's look
at<00:04:13.620><c> the</c><00:04:13.799><c> prompts</c><00:04:14.220><c> here</c><00:04:14.459><c> right</c><00:04:14.879><c> so</c><00:04:15.299><c> we've</c><00:04:15.780><c> got</c>

00:04:15.890 --> 00:04:15.900 align:start position:0%
at the prompts here right so we've got
 

00:04:15.900 --> 00:04:17.870 align:start position:0%
at the prompts here right so we've got
multiple<00:04:16.320><c> chains</c><00:04:16.739><c> going</c><00:04:16.919><c> on</c><00:04:17.100><c> here</c><00:04:17.280><c> first</c><00:04:17.699><c> one</c>

00:04:17.870 --> 00:04:17.880 align:start position:0%
multiple chains going on here first one
 

00:04:17.880 --> 00:04:20.990 align:start position:0%
multiple chains going on here first one
we've<00:04:18.060><c> got</c><00:04:18.239><c> the</c><00:04:18.600><c> create</c><00:04:18.840><c> assertions</c><00:04:19.620><c> chain</c><00:04:20.220><c> so</c>

00:04:20.990 --> 00:04:21.000 align:start position:0%
we've got the create assertions chain so
 

00:04:21.000 --> 00:04:23.510 align:start position:0%
we've got the create assertions chain so
this<00:04:21.540><c> create</c><00:04:21.720><c> assertions</c><00:04:22.320><c> chain</c><00:04:22.560><c> here</c><00:04:22.860><c> you'll</c>

00:04:23.510 --> 00:04:23.520 align:start position:0%
this create assertions chain here you'll
 

00:04:23.520 --> 00:04:25.129 align:start position:0%
this create assertions chain here you'll
see<00:04:23.699><c> that</c><00:04:23.820><c> it's</c><00:04:24.120><c> got</c><00:04:24.360><c> a</c><00:04:24.479><c> very</c><00:04:24.600><c> simple</c><00:04:24.720><c> prompt</c>

00:04:25.129 --> 00:04:25.139 align:start position:0%
see that it's got a very simple prompt
 

00:04:25.139 --> 00:04:27.950 align:start position:0%
see that it's got a very simple prompt
just<00:04:25.380><c> give</c><00:04:25.680><c> some</c><00:04:25.919><c> text</c><00:04:26.220><c> extract</c><00:04:26.880><c> a</c><00:04:27.120><c> list</c><00:04:27.360><c> of</c>

00:04:27.950 --> 00:04:27.960 align:start position:0%
just give some text extract a list of
 

00:04:27.960 --> 00:04:30.469 align:start position:0%
just give some text extract a list of
facts<00:04:28.380><c> from</c><00:04:28.680><c> the</c><00:04:28.860><c> text</c><00:04:29.040><c> format</c><00:04:29.940><c> your</c><00:04:30.180><c> output</c>

00:04:30.469 --> 00:04:30.479 align:start position:0%
facts from the text format your output
 

00:04:30.479 --> 00:04:32.570 align:start position:0%
facts from the text format your output
as<00:04:30.720><c> a</c><00:04:30.840><c> bulleted</c><00:04:31.320><c> list</c><00:04:31.440><c> nothing</c><00:04:32.100><c> really</c>

00:04:32.570 --> 00:04:32.580 align:start position:0%
as a bulleted list nothing really
 

00:04:32.580 --> 00:04:34.610 align:start position:0%
as a bulleted list nothing really
unusual<00:04:33.120><c> there</c><00:04:33.360><c> the</c><00:04:33.840><c> next</c><00:04:33.960><c> one</c><00:04:34.139><c> is</c><00:04:34.380><c> the</c>

00:04:34.610 --> 00:04:34.620 align:start position:0%
unusual there the next one is the
 

00:04:34.620 --> 00:04:36.590 align:start position:0%
unusual there the next one is the
checking<00:04:34.979><c> the</c><00:04:35.160><c> assertions</c><00:04:35.699><c> so</c><00:04:36.240><c> I've</c><00:04:36.419><c> just</c>

00:04:36.590 --> 00:04:36.600 align:start position:0%
checking the assertions so I've just
 

00:04:36.600 --> 00:04:38.150 align:start position:0%
checking the assertions so I've just
copied<00:04:36.840><c> these</c><00:04:36.960><c> so</c><00:04:37.020><c> they're</c><00:04:37.139><c> just</c><00:04:37.320><c> easier</c><00:04:37.919><c> to</c>

00:04:38.150 --> 00:04:38.160 align:start position:0%
copied these so they're just easier to
 

00:04:38.160 --> 00:04:39.830 align:start position:0%
copied these so they're just easier to
read<00:04:38.340><c> here</c><00:04:38.820><c> we</c><00:04:39.060><c> can</c><00:04:39.180><c> see</c><00:04:39.300><c> that</c><00:04:39.419><c> just</c><00:04:39.660><c> is</c>

00:04:39.830 --> 00:04:39.840 align:start position:0%
read here we can see that just is
 

00:04:39.840 --> 00:04:41.990 align:start position:0%
read here we can see that just is
passing<00:04:40.139><c> in</c><00:04:40.259><c> the</c><00:04:40.440><c> assertions</c><00:04:40.860><c> and</c><00:04:41.520><c> then</c><00:04:41.639><c> we've</c>

00:04:41.990 --> 00:04:42.000 align:start position:0%
passing in the assertions and then we've
 

00:04:42.000 --> 00:04:44.510 align:start position:0%
passing in the assertions and then we've
got<00:04:42.120><c> the</c><00:04:42.479><c> revised</c><00:04:43.199><c> summary</c><00:04:43.800><c> prompt</c><00:04:44.280><c> where</c>

00:04:44.510 --> 00:04:44.520 align:start position:0%
got the revised summary prompt where
 

00:04:44.520 --> 00:04:47.150 align:start position:0%
got the revised summary prompt where
it's<00:04:44.699><c> basically</c><00:04:45.180><c> doing</c><00:04:45.900><c> the</c><00:04:46.139><c> revision</c><00:04:46.560><c> of</c>

00:04:47.150 --> 00:04:47.160 align:start position:0%
it's basically doing the revision of
 

00:04:47.160 --> 00:04:49.730 align:start position:0%
it's basically doing the revision of
this<00:04:47.460><c> and</c><00:04:48.300><c> then</c><00:04:48.419><c> finally</c><00:04:48.780><c> we've</c><00:04:49.139><c> got</c><00:04:49.320><c> the</c>

00:04:49.730 --> 00:04:49.740 align:start position:0%
this and then finally we've got the
 

00:04:49.740 --> 00:04:52.430 align:start position:0%
this and then finally we've got the
assertions<00:04:50.540><c> basically</c><00:04:51.540><c> whether</c><00:04:51.720><c> we're</c><00:04:52.020><c> using</c>

00:04:52.430 --> 00:04:52.440 align:start position:0%
assertions basically whether we're using
 

00:04:52.440 --> 00:04:54.950 align:start position:0%
assertions basically whether we're using
these<00:04:52.860><c> to</c><00:04:52.979><c> be</c><00:04:53.160><c> true</c><00:04:53.520><c> or</c><00:04:54.060><c> false</c><00:04:54.479><c> you're</c><00:04:54.780><c> just</c>

00:04:54.950 --> 00:04:54.960 align:start position:0%
these to be true or false you're just
 

00:04:54.960 --> 00:04:57.050 align:start position:0%
these to be true or false you're just
building<00:04:55.199><c> up</c><00:04:55.440><c> on</c><00:04:55.620><c> what</c><00:04:55.979><c> we</c><00:04:56.100><c> had</c><00:04:56.280><c> there</c><00:04:56.520><c> for</c>

00:04:57.050 --> 00:04:57.060 align:start position:0%
building up on what we had there for
 

00:04:57.060 --> 00:04:59.030 align:start position:0%
building up on what we had there for
example<00:04:57.300><c> if</c><00:04:57.660><c> it</c><00:04:57.840><c> says</c><00:04:58.139><c> that</c><00:04:58.320><c> okay</c><00:04:58.500><c> some</c><00:04:58.860><c> of</c>

00:04:59.030 --> 00:04:59.040 align:start position:0%
example if it says that okay some of
 

00:04:59.040 --> 00:05:01.430 align:start position:0%
example if it says that okay some of
them<00:04:59.100><c> are</c><00:04:59.340><c> false</c><00:04:59.639><c> but</c><00:04:59.820><c> one</c><00:05:00.060><c> is</c><00:05:00.240><c> true</c><00:05:00.540><c> then</c><00:05:01.020><c> it's</c>

00:05:01.430 --> 00:05:01.440 align:start position:0%
them are false but one is true then it's
 

00:05:01.440 --> 00:05:03.230 align:start position:0%
them are false but one is true then it's
still<00:05:01.620><c> going</c><00:05:01.740><c> to</c><00:05:01.860><c> be</c><00:05:01.919><c> false</c><00:05:02.340><c> in</c><00:05:02.699><c> this</c><00:05:02.880><c> case</c><00:05:03.000><c> we</c>

00:05:03.230 --> 00:05:03.240 align:start position:0%
still going to be false in this case we
 

00:05:03.240 --> 00:05:05.749 align:start position:0%
still going to be false in this case we
have<00:05:03.300><c> to</c><00:05:03.479><c> have</c><00:05:03.600><c> all</c><00:05:04.080><c> all</c><00:05:04.500><c> true</c><00:05:04.800><c> we've</c><00:05:05.340><c> prompted</c>

00:05:05.749 --> 00:05:05.759 align:start position:0%
have to have all all true we've prompted
 

00:05:05.759 --> 00:05:08.090 align:start position:0%
have to have all all true we've prompted
it<00:05:05.880><c> to</c><00:05:06.060><c> see</c><00:05:06.180><c> that</c><00:05:06.419><c> okay</c><00:05:06.720><c> unless</c><00:05:07.500><c> everything</c><00:05:07.800><c> is</c>

00:05:08.090 --> 00:05:08.100 align:start position:0%
it to see that okay unless everything is
 

00:05:08.100 --> 00:05:10.249 align:start position:0%
it to see that okay unless everything is
true<00:05:08.400><c> you</c><00:05:08.880><c> should</c><00:05:09.120><c> raise</c><00:05:09.300><c> an</c><00:05:09.540><c> error</c><00:05:09.840><c> and</c>

00:05:10.249 --> 00:05:10.259 align:start position:0%
true you should raise an error and
 

00:05:10.259 --> 00:05:12.050 align:start position:0%
true you should raise an error and
that's<00:05:10.440><c> finally</c><00:05:10.919><c> how</c><00:05:11.100><c> we</c><00:05:11.220><c> get</c><00:05:11.400><c> our</c><00:05:11.580><c> output</c>

00:05:12.050 --> 00:05:12.060 align:start position:0%
that's finally how we get our output
 

00:05:12.060 --> 00:05:14.210 align:start position:0%
that's finally how we get our output
that's<00:05:12.419><c> going</c><00:05:12.660><c> to</c><00:05:12.780><c> be</c><00:05:12.840><c> from</c><00:05:13.139><c> that</c><00:05:13.440><c> this</c><00:05:13.979><c> sort</c>

00:05:14.210 --> 00:05:14.220 align:start position:0%
that's going to be from that this sort
 

00:05:14.220 --> 00:05:15.350 align:start position:0%
that's going to be from that this sort
of<00:05:14.280><c> just</c><00:05:14.400><c> shows</c><00:05:14.639><c> you</c><00:05:14.699><c> how</c><00:05:14.880><c> it</c><00:05:14.940><c> works</c><00:05:15.180><c> it</c>

00:05:15.350 --> 00:05:15.360 align:start position:0%
of just shows you how it works it
 

00:05:15.360 --> 00:05:17.330 align:start position:0%
of just shows you how it works it
actually<00:05:15.540><c> works</c><00:05:15.960><c> pretty</c><00:05:16.139><c> nicely</c><00:05:16.500><c> I</c><00:05:16.740><c> find</c><00:05:16.979><c> for</c>

00:05:17.330 --> 00:05:17.340 align:start position:0%
actually works pretty nicely I find for
 

00:05:17.340 --> 00:05:19.129 align:start position:0%
actually works pretty nicely I find for
at<00:05:17.639><c> least</c><00:05:17.759><c> for</c><00:05:18.000><c> articles</c><00:05:18.540><c> and</c><00:05:18.780><c> stuff</c><00:05:18.960><c> like</c>

00:05:19.129 --> 00:05:19.139 align:start position:0%
at least for articles and stuff like
 

00:05:19.139 --> 00:05:22.790 align:start position:0%
at least for articles and stuff like
that<00:05:19.320><c> it</c><00:05:20.280><c> is</c><00:05:20.580><c> very</c><00:05:21.060><c> useful</c><00:05:21.479><c> for</c><00:05:22.259><c> doing</c><00:05:22.440><c> short</c>

00:05:22.790 --> 00:05:22.800 align:start position:0%
that it is very useful for doing short
 

00:05:22.800 --> 00:05:24.830 align:start position:0%
that it is very useful for doing short
summaries<00:05:23.280><c> now</c><00:05:23.460><c> I'm</c><00:05:23.639><c> not</c><00:05:23.759><c> sure</c><00:05:23.940><c> how</c><00:05:24.240><c> well</c><00:05:24.419><c> it</c>

00:05:24.830 --> 00:05:24.840 align:start position:0%
summaries now I'm not sure how well it
 

00:05:24.840 --> 00:05:27.590 align:start position:0%
summaries now I'm not sure how well it
will<00:05:24.960><c> work</c><00:05:25.259><c> for</c><00:05:26.100><c> very</c><00:05:26.580><c> large</c><00:05:26.940><c> summaries</c>

00:05:27.590 --> 00:05:27.600 align:start position:0%
will work for very large summaries
 

00:05:27.600 --> 00:05:29.629 align:start position:0%
will work for very large summaries
because<00:05:27.960><c> the</c><00:05:28.440><c> challenge</c><00:05:28.560><c> there</c><00:05:29.039><c> can</c><00:05:29.280><c> be</c><00:05:29.400><c> that</c>

00:05:29.629 --> 00:05:29.639 align:start position:0%
because the challenge there can be that
 

00:05:29.639 --> 00:05:31.730 align:start position:0%
because the challenge there can be that
facts<00:05:30.120><c> can</c><00:05:30.240><c> be</c><00:05:30.419><c> established</c><00:05:31.020><c> in</c><00:05:31.199><c> one</c><00:05:31.440><c> part</c><00:05:31.620><c> of</c>

00:05:31.730 --> 00:05:31.740 align:start position:0%
facts can be established in one part of
 

00:05:31.740 --> 00:05:33.290 align:start position:0%
facts can be established in one part of
a<00:05:31.919><c> book</c><00:05:32.039><c> and</c><00:05:32.340><c> then</c><00:05:32.460><c> referred</c><00:05:32.820><c> to</c><00:05:32.940><c> in</c><00:05:33.120><c> another</c>

00:05:33.290 --> 00:05:33.300 align:start position:0%
a book and then referred to in another
 

00:05:33.300 --> 00:05:35.689 align:start position:0%
a book and then referred to in another
part<00:05:33.600><c> of</c><00:05:33.840><c> book</c><00:05:34.020><c> that</c><00:05:34.620><c> raises</c><00:05:35.100><c> a</c><00:05:35.340><c> challenge</c>

00:05:35.689 --> 00:05:35.699 align:start position:0%
part of book that raises a challenge
 

00:05:35.699 --> 00:05:37.249 align:start position:0%
part of book that raises a challenge
when<00:05:36.060><c> you're</c><00:05:36.180><c> dealing</c><00:05:36.479><c> with</c><00:05:36.660><c> a</c><00:05:36.840><c> very</c><00:05:36.960><c> limited</c>

00:05:37.249 --> 00:05:37.259 align:start position:0%
when you're dealing with a very limited
 

00:05:37.259 --> 00:05:39.830 align:start position:0%
when you're dealing with a very limited
token<00:05:38.100><c> span</c><00:05:38.460><c> width</c><00:05:38.880><c> for</c><00:05:39.300><c> this</c><00:05:39.479><c> kind</c><00:05:39.600><c> of</c><00:05:39.720><c> thing</c>

00:05:39.830 --> 00:05:39.840 align:start position:0%
token span width for this kind of thing
 

00:05:39.840 --> 00:05:41.810 align:start position:0%
token span width for this kind of thing
there's<00:05:40.560><c> the</c><00:05:40.800><c> Checker</c><00:05:41.160><c> chain</c><00:05:41.340><c> just</c><00:05:41.580><c> so</c><00:05:41.699><c> you</c>

00:05:41.810 --> 00:05:41.820 align:start position:0%
there's the Checker chain just so you
 

00:05:41.820 --> 00:05:43.490 align:start position:0%
there's the Checker chain just so you
can<00:05:41.940><c> actually</c><00:05:42.060><c> have</c><00:05:42.300><c> a</c><00:05:42.419><c> look</c><00:05:42.539><c> and</c><00:05:42.780><c> see</c><00:05:43.020><c> all</c><00:05:43.380><c> the</c>

00:05:43.490 --> 00:05:43.500 align:start position:0%
can actually have a look and see all the
 

00:05:43.500 --> 00:05:45.529 align:start position:0%
can actually have a look and see all the
different<00:05:43.620><c> parts</c><00:05:43.919><c> of</c><00:05:44.160><c> the</c><00:05:44.400><c> chain</c><00:05:44.580><c> that</c><00:05:45.360><c> are</c>

00:05:45.529 --> 00:05:45.539 align:start position:0%
different parts of the chain that are
 

00:05:45.539 --> 00:05:47.749 align:start position:0%
different parts of the chain that are
there<00:05:45.720><c> and</c><00:05:46.139><c> then</c><00:05:46.259><c> lastly</c><00:05:46.560><c> I</c><00:05:46.680><c> just</c><00:05:46.860><c> put</c><00:05:47.460><c> in</c><00:05:47.580><c> a</c>

00:05:47.749 --> 00:05:47.759 align:start position:0%
there and then lastly I just put in a
 

00:05:47.759 --> 00:05:50.330 align:start position:0%
there and then lastly I just put in a
simple<00:05:47.940><c> way</c><00:05:48.240><c> another</c><00:05:48.780><c> trick</c><00:05:49.440><c> you</c><00:05:49.560><c> can</c><00:05:49.740><c> do</c><00:05:49.919><c> is</c>

00:05:50.330 --> 00:05:50.340 align:start position:0%
simple way another trick you can do is
 

00:05:50.340 --> 00:05:52.730 align:start position:0%
simple way another trick you can do is
make<00:05:50.639><c> these</c><00:05:51.000><c> into</c><00:05:51.120><c> triples</c><00:05:51.660><c> so</c><00:05:52.020><c> here</c><00:05:52.199><c> I've</c><00:05:52.440><c> put</c>

00:05:52.730 --> 00:05:52.740 align:start position:0%
make these into triples so here I've put
 

00:05:52.740 --> 00:05:55.790 align:start position:0%
make these into triples so here I've put
a<00:05:52.919><c> simple</c><00:05:53.520><c> prompt</c><00:05:54.180><c> that</c><00:05:54.840><c> takes</c><00:05:55.139><c> these</c><00:05:55.560><c> and</c>

00:05:55.790 --> 00:05:55.800 align:start position:0%
a simple prompt that takes these and
 

00:05:55.800 --> 00:05:57.950 align:start position:0%
a simple prompt that takes these and
puts<00:05:56.100><c> them</c><00:05:56.220><c> the</c><00:05:56.520><c> outputs</c><00:05:57.000><c> like</c><00:05:57.180><c> a</c><00:05:57.360><c> trip</c><00:05:57.600><c> a</c><00:05:57.840><c> set</c>

00:05:57.950 --> 00:05:57.960 align:start position:0%
puts them the outputs like a trip a set
 

00:05:57.960 --> 00:06:00.110 align:start position:0%
puts them the outputs like a trip a set
of<00:05:58.080><c> triples</c><00:05:58.440><c> for</c><00:05:58.680><c> a</c><00:05:58.800><c> Knowledge</c><00:05:59.100><c> Graph</c><00:05:59.520><c> and</c>

00:06:00.110 --> 00:06:00.120 align:start position:0%
of triples for a Knowledge Graph and
 

00:06:00.120 --> 00:06:01.850 align:start position:0%
of triples for a Knowledge Graph and
then<00:06:00.180><c> you</c><00:06:00.419><c> could</c><00:06:00.539><c> actually</c><00:06:00.720><c> use</c><00:06:01.199><c> a</c><00:06:01.500><c> Knowledge</c>

00:06:01.850 --> 00:06:01.860 align:start position:0%
then you could actually use a Knowledge
 

00:06:01.860 --> 00:06:03.830 align:start position:0%
then you could actually use a Knowledge
Graph<00:06:02.100><c> to</c><00:06:02.400><c> look</c><00:06:02.520><c> this</c><00:06:02.759><c> up</c><00:06:02.940><c> so</c><00:06:03.300><c> you</c><00:06:03.479><c> would</c><00:06:03.600><c> need</c>

00:06:03.830 --> 00:06:03.840 align:start position:0%
Graph to look this up so you would need
 

00:06:03.840 --> 00:06:07.189 align:start position:0%
Graph to look this up so you would need
to<00:06:04.080><c> reconcile</c><00:06:04.740><c> the</c><00:06:05.520><c> nodes</c><00:06:05.940><c> related</c><00:06:06.780><c> to</c><00:06:07.020><c> the</c>

00:06:07.189 --> 00:06:07.199 align:start position:0%
to reconcile the nodes related to the
 

00:06:07.199 --> 00:06:08.570 align:start position:0%
to reconcile the nodes related to the
nodes<00:06:07.500><c> that</c><00:06:07.740><c> you</c><00:06:07.860><c> have</c><00:06:07.979><c> on</c><00:06:08.160><c> your</c><00:06:08.340><c> knowledge</c>

00:06:08.570 --> 00:06:08.580 align:start position:0%
nodes that you have on your knowledge
 

00:06:08.580 --> 00:06:10.850 align:start position:0%
nodes that you have on your knowledge
graph<00:06:08.880><c> but</c><00:06:09.479><c> more</c><00:06:09.660><c> and</c><00:06:09.780><c> more</c><00:06:09.960><c> that</c><00:06:10.199><c> there</c><00:06:10.560><c> are</c>

00:06:10.850 --> 00:06:10.860 align:start position:0%
graph but more and more that there are
 

00:06:10.860 --> 00:06:12.230 align:start position:0%
graph but more and more that there are
things<00:06:11.039><c> out</c><00:06:11.280><c> there</c><00:06:11.400><c> with</c><00:06:11.699><c> this</c><00:06:11.880><c> kind</c><00:06:12.120><c> of</c>

00:06:12.230 --> 00:06:12.240 align:start position:0%
things out there with this kind of
 

00:06:12.240 --> 00:06:14.270 align:start position:0%
things out there with this kind of
information<00:06:12.479><c> so</c><00:06:13.139><c> you</c><00:06:13.320><c> could</c><00:06:13.440><c> use</c><00:06:13.740><c> a</c>

00:06:14.270 --> 00:06:14.280 align:start position:0%
information so you could use a
 

00:06:14.280 --> 00:06:17.390 align:start position:0%
information so you could use a
combination<00:06:14.759><c> of</c><00:06:15.419><c> the</c><00:06:15.780><c> original</c><00:06:15.900><c> article</c><00:06:16.680><c> and</c>

00:06:17.390 --> 00:06:17.400 align:start position:0%
combination of the original article and
 

00:06:17.400 --> 00:06:19.790 align:start position:0%
combination of the original article and
a<00:06:17.820><c> Knowledge</c><00:06:18.120><c> Graph</c><00:06:18.419><c> to</c><00:06:18.720><c> provide</c><00:06:19.080><c> a</c><00:06:19.259><c> response</c>

00:06:19.790 --> 00:06:19.800 align:start position:0%
a Knowledge Graph to provide a response
 

00:06:19.800 --> 00:06:22.370 align:start position:0%
a Knowledge Graph to provide a response
that<00:06:20.400><c> can</c><00:06:20.580><c> be</c><00:06:20.699><c> really</c><00:06:20.880><c> useful</c><00:06:21.240><c> for</c><00:06:21.600><c> things</c><00:06:21.840><c> for</c>

00:06:22.370 --> 00:06:22.380 align:start position:0%
that can be really useful for things for
 

00:06:22.380 --> 00:06:25.070 align:start position:0%
that can be really useful for things for
dealing<00:06:22.680><c> with</c><00:06:22.979><c> maybe</c><00:06:23.759><c> customer</c><00:06:24.120><c> inquiries</c><00:06:24.840><c> or</c>

00:06:25.070 --> 00:06:25.080 align:start position:0%
dealing with maybe customer inquiries or
 

00:06:25.080 --> 00:06:26.330 align:start position:0%
dealing with maybe customer inquiries or
something<00:06:25.199><c> like</c><00:06:25.440><c> that</c><00:06:25.620><c> where</c><00:06:25.979><c> you've</c><00:06:26.220><c> got</c>

00:06:26.330 --> 00:06:26.340 align:start position:0%
something like that where you've got
 

00:06:26.340 --> 00:06:28.010 align:start position:0%
something like that where you've got
some<00:06:26.639><c> sort</c><00:06:26.819><c> of</c><00:06:26.940><c> knowledge</c><00:06:27.300><c> you've</c><00:06:27.660><c> got</c><00:06:27.840><c> some</c>

00:06:28.010 --> 00:06:28.020 align:start position:0%
some sort of knowledge you've got some
 

00:06:28.020 --> 00:06:29.390 align:start position:0%
some sort of knowledge you've got some
sort<00:06:28.139><c> of</c><00:06:28.199><c> FAQ</c><00:06:28.680><c> and</c><00:06:28.919><c> then</c><00:06:29.039><c> you've</c><00:06:29.160><c> got</c><00:06:29.220><c> some</c>

00:06:29.390 --> 00:06:29.400 align:start position:0%
sort of FAQ and then you've got some
 

00:06:29.400 --> 00:06:31.189 align:start position:0%
sort of FAQ and then you've got some
sort<00:06:29.520><c> of</c><00:06:29.639><c> Knowledge</c><00:06:29.880><c> Graph</c><00:06:30.120><c> and</c><00:06:30.360><c> you</c><00:06:30.539><c> can</c><00:06:30.720><c> use</c>

00:06:31.189 --> 00:06:31.199 align:start position:0%
sort of Knowledge Graph and you can use
 

00:06:31.199 --> 00:06:33.469 align:start position:0%
sort of Knowledge Graph and you can use
both<00:06:31.380><c> of</c><00:06:31.560><c> them</c><00:06:31.740><c> to</c><00:06:32.100><c> provide</c><00:06:32.639><c> answers</c><00:06:33.060><c> as</c><00:06:33.300><c> well</c>

00:06:33.469 --> 00:06:33.479 align:start position:0%
both of them to provide answers as well
 

00:06:33.479 --> 00:06:35.510 align:start position:0%
both of them to provide answers as well
so<00:06:33.660><c> that's</c><00:06:33.780><c> not</c><00:06:33.960><c> just</c><00:06:34.139><c> summarization</c><00:06:34.860><c> for</c>

00:06:35.510 --> 00:06:35.520 align:start position:0%
so that's not just summarization for
 

00:06:35.520 --> 00:06:37.070 align:start position:0%
so that's not just summarization for
this<00:06:35.639><c> kind</c><00:06:35.819><c> of</c><00:06:35.940><c> thing</c><00:06:36.060><c> alright</c><00:06:36.720><c> if</c><00:06:36.900><c> you've</c>

00:06:37.070 --> 00:06:37.080 align:start position:0%
this kind of thing alright if you've
 

00:06:37.080 --> 00:06:39.170 align:start position:0%
this kind of thing alright if you've
been<00:06:37.259><c> any</c><00:06:37.560><c> questions</c><00:06:37.860><c> as</c><00:06:38.340><c> always</c><00:06:38.520><c> just</c><00:06:39.000><c> put</c>

00:06:39.170 --> 00:06:39.180 align:start position:0%
been any questions as always just put
 

00:06:39.180 --> 00:06:41.450 align:start position:0%
been any questions as always just put
them<00:06:39.360><c> in</c><00:06:39.600><c> the</c><00:06:40.139><c> comments</c><00:06:40.440><c> below</c><00:06:40.800><c> if</c><00:06:41.100><c> you</c><00:06:41.280><c> found</c>

00:06:41.450 --> 00:06:41.460 align:start position:0%
them in the comments below if you found
 

00:06:41.460 --> 00:06:43.490 align:start position:0%
them in the comments below if you found
this<00:06:41.580><c> useful</c><00:06:41.940><c> please</c><00:06:42.479><c> click</c><00:06:42.900><c> and</c><00:06:42.960><c> subscribe</c>

00:06:43.490 --> 00:06:43.500 align:start position:0%
this useful please click and subscribe
 

00:06:43.500 --> 00:06:46.880 align:start position:0%
this useful please click and subscribe
I'll<00:06:43.979><c> see</c><00:06:44.160><c> you</c><00:06:44.280><c> in</c><00:06:44.400><c> the</c><00:06:44.520><c> next</c><00:06:44.639><c> video</c>

