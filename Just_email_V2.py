import os
import asyncio
import logging
from datetime import datetime, timedelta, timezone
from supabase import create_client, Client
from dotenv import load_dotenv
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import json
import pytz
from sqlalchemy import create_engine, text

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

# Initialize Supabase client
supabase: Client = create_client(os.environ.get("SUPABASE_URL"), os.environ.get("SERVICE_ROLE_KEY"))

# Email configuration
SMTP_SERVER = "mail.privateemail.com"
SMTP_PORT = 465
EMAIL_USER = os.environ.get("EMAIL_USER")
EMAIL_PASSWORD = os.environ.get("EMAIL_PASSWORD")
FROM_EMAIL = os.environ.get("FROM_EMAIL")
TO_EMAIL = os.environ.get("TO_EMAIL")

# Tables to process
tables_to_process = ["youtube_artificial_intelligence","youtube_renewable_energy", "youtube_startups","youtube_gme","youtube_financial_markets","youtube_general","youtube_sustainability","youtube_legal"]

# Configuration switches
CHECK_EMAIL_SENT_COLUMN = False  # Set to True to enable email_sent column existence verification

async def process_row(row, table_name):
    if row['llm_response']:
        try:
            # Handle both cases: already parsed dict or string-encoded JSON
            llm_response = row['llm_response'] if isinstance(row['llm_response'], dict) else json.loads(row['llm_response'])
            
            logger.info(f"Processed llm_response for video: {row['title']} (ID: {row['video_id']})")
            
            # Check if the expected keys are present
            if 'insights' in llm_response and 'recommendations' in llm_response:
                return llm_response
            else:
                logger.warning(f"llm_response missing expected keys for video {row['video_id']}")
                return None
        except json.JSONDecodeError:
            logger.error(f"Failed to parse llm_response for video {row['video_id']}")
            return None
    else:
        logger.warning(f"No llm_response found for video {row['video_id']}")
        return None

async def ensure_email_sent_column(table_name):
    """
    Checks if email_sent column exists in the table, adds it if it doesn't
    """
    try:
        # Check if column exists by attempting to select it
        result = supabase.table(table_name).select("email_sent").limit(1).execute()
        logging.info(f"email_sent column already exists in {table_name}")
    except Exception as e:
        if "column" in str(e) and "does not exist" in str(e):
            try:
                # Use SQLAlchemy to execute the ALTER TABLE command
                engine = create_engine(os.environ.get('DIL_POSTGRES_CONNECTION_STRING'))
                with engine.connect() as connection:
                    sql = text(f"ALTER TABLE {table_name} ADD COLUMN IF NOT EXISTS email_sent BOOLEAN DEFAULT FALSE;")
                    connection.execute(sql)
                    connection.commit()
                engine.dispose()
                logging.info(f"Added email_sent column to {table_name}")
            except Exception as create_error:
                logging.error(f"Failed to create email_sent column in {table_name}: {str(create_error)}")
                raise

async def mark_email_sent(table_name, video_id):
    """
    Marks a video as having been sent in email
    """
    try:
        supabase.table(table_name).update({"email_sent": True}).eq("video_id", video_id).execute()
        logger.info(f"Marked video {video_id} as email sent in {table_name}")
    except Exception as e:
        logger.error(f"Failed to mark video {video_id} as sent in {table_name}: {str(e)}")

async def process_new_videos():
    summaries = {}
    total_videos_found = 0
    
    for table_name in tables_to_process:
        summaries[table_name] = []
        
        # Calculate the date 48 hours ago in UTC
        one_day_ago = datetime.now(timezone.utc) - timedelta(days=2)
        one_day_ago_str = one_day_ago.strftime("%Y-%m-%dT%H:%M:%SZ")
        
        logger.info(f"Querying videos from {table_name} published after {one_day_ago_str}")
        
        # Always check email_sent status to avoid duplicates
        result = supabase.table(table_name)\
            .select("*")\
            .eq("processed", "completed")\
            .eq("email_sent", False)\
            .gte("published_at", one_day_ago_str)\
            .execute()

        if not result.data:
            logger.info(f"No new unsent videos found in {table_name} after {one_day_ago_str}")
            continue
        
        logger.info(f"Found {len(result.data)} new unsent videos for table {table_name}")
        total_videos_found += len(result.data)
        
        for row in result.data:
            llm_response = await process_row(row, table_name)
            if llm_response:
                summaries[table_name].append({
                    "title": row['title'],
                    "channel_name": row['channel_name'],
                    "video_id": row['video_id'],
                    "llm_response": llm_response
                })
                # Always mark videos as sent to avoid duplicates
                await mark_email_sent(table_name, row['video_id'])
                logger.info(f"Processed and marked as sent: {row['title']} (ID: {row['video_id']}) from {table_name}")
            else:
                logger.warning(f"Failed to process video: {row['title']} (ID: {row['video_id']}) from {table_name}")
    
    return summaries

def format_email_body(summaries):
    # Add summary table at the top
    body = """
    <html>
    <body style="background-color: #2C2C2C; color: #E0E0E0; font-family: Arial, sans-serif; padding: 20px;">
    """
    
    # Create summary table
    body += """
    <div style="margin-bottom: 30px;">
        <h2 style='color: #FFFFFF;'>Summary of New Videos</h2>
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; background-color: #3C3C3C;">
            <thead>
                <tr style="background-color: #4CAF50; color: white;">
                    <th style="padding: 12px; text-align: left; border: 1px solid #4CAF50;">Category</th>
                    <th style="padding: 12px; text-align: center; border: 1px solid #4CAF50;">New Videos</th>
                </tr>
            </thead>
            <tbody>
    """
    
    total_videos = 0
    for table_name, videos in summaries.items():
        category = table_name.replace('youtube_', '').replace('_', ' ').title()
        video_count = len(videos)
        total_videos += video_count
        if video_count > 0:  # Only show categories with videos
            body += f"""
                <tr style="border: 1px solid #4CAF50;">
                    <td style="padding: 12px; border: 1px solid #4CAF50;">{category}</td>
                    <td style="padding: 12px; text-align: center; border: 1px solid #4CAF50;">{video_count}</td>
                </tr>
            """
    
    # Add total row
    body += f"""
                <tr style="background-color: #2C2C2C; font-weight: bold;">
                    <td style="padding: 12px; border: 1px solid #4CAF50;">Total</td>
                    <td style="padding: 12px; text-align: center; border: 1px solid #4CAF50;">{total_videos}</td>
                </tr>
            </tbody>
        </table>
    </div>
    """

    body += f"<h2 style='color: #FFFFFF;'>Recent YouTube Videos - {datetime.now().strftime('%Y-%m-%d')}</h2>"
    for table_name, videos in summaries.items():
        if videos:
            category = table_name.replace('youtube_', '').replace('_', ' ').title()
            body += f"<h3 style='color: #4CAF50;'>{category}</h3>"
            for video in videos:
                video_link = f"https://www.youtube.com/watch?v={video['video_id']}"
                thumbnail_url = f"https://img.youtube.com/vi/{video['video_id']}/mqdefault.jpg"
                body += f"""
                <div style='margin-bottom: 20px; border-left: 4px solid #4CAF50; padding-left: 15px; background-color: #3C3C3C;'>
                    <h3 style='margin-bottom: 5px;'>
                        <span style='color: #BDBDBD; font-size: 0.8em;'>{video['channel_name']}</span> - 
                        <a href='{video_link}' style='color: #4CAF50; text-decoration: none;'>{video['title']}</a>
                    </h3>
                    <a href='{video_link}'>
                        <img src='{thumbnail_url}' alt='Video thumbnail' style='width: 200px; height: auto; margin-bottom: 10px;'>
                    </a>
                """
                
                llm_response = video.get('llm_response', {})
                if isinstance(llm_response, dict):
                    keywords = llm_response.get('keywords', [])
                    if keywords:
                        body += f"<p style='font-size: 0.8em;'><strong style='color: #87CEEB;'>Keywords:</strong> {', '.join(keywords)}</p>"

                    insights = llm_response.get('insights', [])
                    if insights:
                        body += "<h5 style='color: #87CEEB; font-size: 1em; margin-bottom: 5px;'>Insights:</h5>"
                        body += "<ul style='color: #E0E0E0; font-size: 1em; margin-top: 0;'>"
                        for insight in insights:
                            body += f"<li>{insight}</li>"
                        body += "</ul>"

                    # recommendations = llm_response.get('recommendations', [])
                    # if recommendations:
                    #     body += "<h5 style='color: #87CEEB; font-size: 1em; margin-bottom: 5px;'>Recommendations:</h5>"
                    #     body += "<ul style='color: #E0E0E0; font-size: 1em; margin-top: 0;'>"
                    #     for recommendation in recommendations:
                    #         body += f"<li>{recommendation}</li>"
                    #     body += "</ul>"
                else:
                    body += "<p style='color: #FF6347;'><em>No valid LLM response available</em></p>"
                
                body += "</div>"
    
    body += "</body></html>"
    return body

def send_email(subject, body):
    msg = MIMEMultipart()
    msg['From'] = FROM_EMAIL
    msg['To'] = TO_EMAIL
    msg['Subject'] = subject
    msg.attach(MIMEText(body, 'html'))

    try:
        # Log email details
        logger.info(f"Sending email from {FROM_EMAIL} to {TO_EMAIL} with subject '{subject}'")

        with smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT) as server:
            server.login(EMAIL_USER, EMAIL_PASSWORD)
            server.sendmail(FROM_EMAIL, TO_EMAIL, msg.as_string())
        logger.info("Email sent successfully!")
    except smtplib.SMTPException as e:
        logger.error(f"SMTP error occurred while sending email: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred while sending email: {e}", exc_info=True)

async def main():
    try:
        # Only verify column existence if explicitly enabled
        if CHECK_EMAIL_SENT_COLUMN:
            logger.info("Verifying email_sent column existence in tables...")
            for table_name in tables_to_process:
                try:
                    await ensure_email_sent_column(table_name)
                except Exception as e:
                    logger.error(f"Failed to verify email_sent column in {table_name}: {str(e)}")
                    return
        
        summaries = await process_new_videos()
        total_videos = sum(len(videos) for videos in summaries.values())
        logger.info(f"Total processed videos: {total_videos}")
        
        if total_videos > 0:
            subject = f"Recent YouTube Videos - {datetime.now().strftime('%Y-%m-%d')}"
            body = format_email_body(summaries)
            send_email(subject, body)
            logger.info(f"Email sent with {total_videos} recent video summaries.")
        else:
            logger.warning("No new unsent videos found in the last 48 hours. Email not sent.")
    except Exception as e:
        logger.error(f"An error occurred in the main function: {str(e)}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
