WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.869 align:start position:0%
 
guys<00:00:00.210><c> we're</c><00:00:00.539><c> back</c><00:00:00.750><c> with</c><00:00:01.020><c> the</c><00:00:01.079><c> example</c><00:00:01.920><c> of</c><00:00:02.129><c> how</c>

00:00:02.869 --> 00:00:02.879 align:start position:0%
guys we're back with the example of how
 

00:00:02.879 --> 00:00:05.869 align:start position:0%
guys we're back with the example of how
to<00:00:02.939><c> upload</c><00:00:03.629><c> custom</c><00:00:04.049><c> user</c><00:00:04.830><c> data</c><00:00:05.220><c> attributes</c>

00:00:05.869 --> 00:00:05.879 align:start position:0%
to upload custom user data attributes
 

00:00:05.879 --> 00:00:08.780 align:start position:0%
to upload custom user data attributes
and<00:00:06.120><c> what</c><00:00:07.020><c> we</c><00:00:07.170><c> did</c><00:00:07.379><c> within</c><00:00:07.890><c> our</c><00:00:08.040><c> modern-day</c>

00:00:08.780 --> 00:00:08.790 align:start position:0%
and what we did within our modern-day
 

00:00:08.790 --> 00:00:11.480 align:start position:0%
and what we did within our modern-day
CMS<00:00:09.389><c> project</c><00:00:09.929><c> is</c><00:00:10.110><c> we</c><00:00:10.769><c> had</c><00:00:10.889><c> the</c><00:00:11.040><c> new</c><00:00:11.219><c> user</c>

00:00:11.480 --> 00:00:11.490 align:start position:0%
CMS project is we had the new user
 

00:00:11.490 --> 00:00:13.999 align:start position:0%
CMS project is we had the new user
function<00:00:12.120><c> sorry</c><00:00:12.960><c> we</c><00:00:13.080><c> have</c><00:00:13.170><c> the</c><00:00:13.290><c> run</c><00:00:13.500><c> method</c>

00:00:13.999 --> 00:00:14.009 align:start position:0%
function sorry we have the run method
 

00:00:14.009 --> 00:00:17.689 align:start position:0%
function sorry we have the run method
which<00:00:14.429><c> is</c><00:00:15.230><c> run</c><00:00:16.230><c> every</c><00:00:16.800><c> time</c><00:00:16.949><c> that</c><00:00:17.100><c> the</c><00:00:17.340><c> the</c>

00:00:17.689 --> 00:00:17.699 align:start position:0%
which is run every time that the the
 

00:00:17.699 --> 00:00:21.170 align:start position:0%
which is run every time that the the
browser<00:00:17.940><c> reloads</c><00:00:18.449><c> and</c><00:00:18.900><c> we</c><00:00:19.710><c> we</c><00:00:20.580><c> just</c><00:00:20.789><c> add</c><00:00:20.939><c> an</c>

00:00:21.170 --> 00:00:21.180 align:start position:0%
browser reloads and we we just add an
 

00:00:21.180 --> 00:00:23.900 align:start position:0%
browser reloads and we we just add an
upgrade<00:00:21.630><c> request</c><00:00:22.050><c> true</c><00:00:22.619><c> key</c><00:00:23.100><c> value</c><00:00:23.580><c> pair</c>

00:00:23.900 --> 00:00:23.910 align:start position:0%
upgrade request true key value pair
 

00:00:23.910 --> 00:00:26.029 align:start position:0%
upgrade request true key value pair
upgrade<00:00:24.420><c> request</c><00:00:24.750><c> again</c><00:00:25.439><c> we</c><00:00:25.560><c> did</c><00:00:25.710><c> this</c><00:00:25.740><c> in</c><00:00:25.949><c> our</c>

00:00:26.029 --> 00:00:26.039 align:start position:0%
upgrade request again we did this in our
 

00:00:26.039 --> 00:00:28.189 align:start position:0%
upgrade request again we did this in our
app<00:00:26.250><c> settings</c><00:00:26.670><c> on</c><00:00:26.820><c> actual</c><00:00:27.150><c> intercom</c><00:00:27.750><c> in</c><00:00:27.930><c> part</c>

00:00:28.189 --> 00:00:28.199 align:start position:0%
app settings on actual intercom in part
 

00:00:28.199 --> 00:00:29.540 align:start position:0%
app settings on actual intercom in part
7<00:00:28.650><c> I</c><00:00:28.800><c> want</c><00:00:28.949><c> part</c><00:00:29.160><c> 8</c>

00:00:29.540 --> 00:00:29.550 align:start position:0%
7 I want part 8
 

00:00:29.550 --> 00:00:33.440 align:start position:0%
7 I want part 8
we're<00:00:30.029><c> actually</c><00:00:31.550><c> going</c><00:00:32.550><c> ahead</c><00:00:32.790><c> and</c><00:00:33.210><c> just</c>

00:00:33.440 --> 00:00:33.450 align:start position:0%
we're actually going ahead and just
 

00:00:33.450 --> 00:00:36.110 align:start position:0%
we're actually going ahead and just
adding<00:00:34.079><c> that</c><00:00:34.110><c> key</c><00:00:34.860><c> value</c><00:00:35.280><c> pair</c><00:00:35.579><c> upgrade</c>

00:00:36.110 --> 00:00:36.120 align:start position:0%
adding that key value pair upgrade
 

00:00:36.120 --> 00:00:38.780 align:start position:0%
adding that key value pair upgrade
request<00:00:36.510><c> true</c><00:00:36.899><c> to</c><00:00:37.320><c> the</c><00:00:37.500><c> actual</c><00:00:37.890><c> user</c><00:00:38.160><c> object</c>

00:00:38.780 --> 00:00:38.790 align:start position:0%
request true to the actual user object
 

00:00:38.790 --> 00:00:40.880 align:start position:0%
request true to the actual user object
over<00:00:39.120><c> here</c><00:00:39.149><c> and</c><00:00:39.570><c> as</c><00:00:39.960><c> you</c><00:00:40.079><c> can</c><00:00:40.230><c> see</c><00:00:40.290><c> here</c><00:00:40.770><c> we</c>

00:00:40.880 --> 00:00:40.890 align:start position:0%
over here and as you can see here we
 

00:00:40.890 --> 00:00:43.069 align:start position:0%
over here and as you can see here we
have<00:00:41.070><c> the</c><00:00:41.610><c> results</c><00:00:42.030><c> of</c><00:00:42.120><c> a</c><00:00:42.210><c> search</c><00:00:42.450><c> the</c><00:00:42.690><c> upgrade</c>

00:00:43.069 --> 00:00:43.079 align:start position:0%
have the results of a search the upgrade
 

00:00:43.079 --> 00:00:45.229 align:start position:0%
have the results of a search the upgrade
route<00:00:43.290><c> where</c><00:00:43.680><c> the</c><00:00:43.829><c> upgrade</c><00:00:44.160><c> request</c><00:00:44.550><c> is</c><00:00:44.760><c> true</c>

00:00:45.229 --> 00:00:45.239 align:start position:0%
route where the upgrade request is true
 

00:00:45.239 --> 00:00:47.389 align:start position:0%
route where the upgrade request is true
by<00:00:45.870><c> now</c><00:00:46.079><c> there's</c><00:00:46.260><c> only</c><00:00:46.379><c> one</c><00:00:46.590><c> person</c><00:00:46.829><c> what's</c><00:00:47.219><c> up</c>

00:00:47.389 --> 00:00:47.399 align:start position:0%
by now there's only one person what's up
 

00:00:47.399 --> 00:00:49.369 align:start position:0%
by now there's only one person what's up
YouTube<00:00:47.789><c> developers</c><00:00:48.300><c> the</c><00:00:48.510><c> email</c><00:00:48.840><c> the</c><00:00:48.960><c> user</c><00:00:48.989><c> ID</c>

00:00:49.369 --> 00:00:49.379 align:start position:0%
YouTube developers the email the user ID
 

00:00:49.379 --> 00:00:52.670 align:start position:0%
YouTube developers the email the user ID
is<00:00:49.469><c> 105</c><00:00:49.980><c> we're</c><00:00:50.850><c> about</c><00:00:51.030><c> to</c><00:00:51.180><c> create</c><00:00:51.390><c> user</c><00:00:51.719><c> ID</c><00:00:52.020><c> 108</c>

00:00:52.670 --> 00:00:52.680 align:start position:0%
is 105 we're about to create user ID 108
 

00:00:52.680 --> 00:00:54.740 align:start position:0%
is 105 we're about to create user ID 108
and<00:00:53.520><c> that</c><00:00:53.699><c> happens</c><00:00:54.120><c> every</c><00:00:54.329><c> time</c><00:00:54.449><c> that</c><00:00:54.600><c> we</c>

00:00:54.740 --> 00:00:54.750 align:start position:0%
and that happens every time that we
 

00:00:54.750 --> 00:00:59.110 align:start position:0%
and that happens every time that we
refresh<00:00:55.469><c> the</c><00:00:55.680><c> page</c><00:00:55.890><c> here</c><00:00:57.230><c> let's</c><00:00:58.230><c> see</c><00:00:58.379><c> well</c>

00:00:59.110 --> 00:00:59.120 align:start position:0%
refresh the page here let's see well
 

00:00:59.120 --> 00:01:04.490 align:start position:0%
refresh the page here let's see well
here<00:01:00.120><c> we</c><00:01:00.239><c> go</c><00:01:00.449><c> social</c><00:01:01.109><c> app</c><00:01:01.230><c> is</c><00:01:01.440><c> loading</c><00:01:03.409><c> it's</c><00:01:04.409><c> a</c>

00:01:04.490 --> 00:01:04.500 align:start position:0%
here we go social app is loading it's a
 

00:01:04.500 --> 00:01:08.750 align:start position:0%
here we go social app is loading it's a
really<00:01:04.799><c> slow</c><00:01:05.100><c> computer</c><00:01:06.110><c> so</c><00:01:07.520><c> don't</c><00:01:08.520><c> have</c><00:01:08.610><c> to</c>

00:01:08.750 --> 00:01:08.760 align:start position:0%
really slow computer so don't have to
 

00:01:08.760 --> 00:01:09.620 align:start position:0%
really slow computer so don't have to
bear<00:01:08.970><c> with</c><00:01:09.210><c> me</c><00:01:09.270><c> here</c>

00:01:09.620 --> 00:01:09.630 align:start position:0%
bear with me here
 

00:01:09.630 --> 00:01:12.350 align:start position:0%
bear with me here
here<00:01:10.320><c> we</c><00:01:10.409><c> go</c><00:01:10.590><c> I</c><00:01:10.830><c> know</c><00:01:11.729><c> we</c><00:01:11.850><c> only</c><00:01:11.880><c> have</c><00:01:12.150><c> one</c>

00:01:12.350 --> 00:01:12.360 align:start position:0%
here we go I know we only have one
 

00:01:12.360 --> 00:01:14.390 align:start position:0%
here we go I know we only have one
person<00:01:12.600><c> which</c><00:01:12.930><c> matches</c><00:01:13.140><c> this</c><00:01:13.439><c> filter</c><00:01:13.920><c> so</c><00:01:14.369><c> you</c>

00:01:14.390 --> 00:01:14.400 align:start position:0%
person which matches this filter so you
 

00:01:14.400 --> 00:01:32.060 align:start position:0%
person which matches this filter so you
forget<00:01:14.729><c> another</c><00:01:30.200><c> crabber</c><00:01:31.200><c> own</c><00:01:31.500><c> is</c><00:01:31.650><c> still</c><00:01:31.890><c> only</c>

00:01:32.060 --> 00:01:32.070 align:start position:0%
forget another crabber own is still only
 

00:01:32.070 --> 00:01:36.219 align:start position:0%
forget another crabber own is still only
one<00:01:32.310><c> person</c><00:01:32.759><c> why</c><00:01:32.939><c> is</c><00:01:33.000><c> that</c><00:01:33.650><c> user</c><00:01:34.650><c> ID</c><00:01:34.829><c> is</c><00:01:35.009><c> 108</c>

00:01:36.219 --> 00:01:36.229 align:start position:0%
one person why is that user ID is 108
 

00:01:36.229 --> 00:01:42.800 align:start position:0%
one person why is that user ID is 108
him<00:01:37.259><c> we</c><00:01:37.650><c> need</c><00:01:37.799><c> another</c><00:01:38.100><c> name</c><00:01:38.340><c> here</c><00:01:38.670><c> this</c><00:01:39.570><c> is</c><00:01:41.810><c> Li</c>

00:01:42.800 --> 00:01:42.810 align:start position:0%
him we need another name here this is Li
 

00:01:42.810 --> 00:01:47.950 align:start position:0%
him we need another name here this is Li
XI<00:01:44.360><c> Li</c><00:01:45.360><c> XI</c><00:01:45.420><c> K</c><00:01:45.899><c> capiche</c>

00:01:47.950 --> 00:01:47.960 align:start position:0%
XI Li XI K capiche
 

00:01:47.960 --> 00:01:50.960 align:start position:0%
XI Li XI K capiche
okay<00:01:48.960><c> Li</c><00:01:49.170><c> XI</c><00:01:49.350><c> K</c><00:01:49.500><c> Capisce</c><00:01:49.979><c> that's</c><00:01:50.340><c> unique</c><00:01:50.700><c> right</c>

00:01:50.960 --> 00:01:50.970 align:start position:0%
okay Li XI K Capisce that's unique right
 

00:01:50.970 --> 00:01:56.149 align:start position:0%
okay Li XI K Capisce that's unique right
let's<00:01:51.360><c> probably</c><00:01:53.390><c> need</c><00:01:54.390><c> to</c><00:01:54.509><c> reload</c><00:01:54.960><c> below</c><00:01:55.950><c> the</c>

00:01:56.149 --> 00:01:56.159 align:start position:0%
let's probably need to reload below the
 

00:01:56.159 --> 00:01:59.630 align:start position:0%
let's probably need to reload below the
app<00:01:56.840><c> okay</c><00:01:57.840><c> here's</c><00:01:58.350><c> our</c><00:01:58.560><c> server</c><00:01:58.860><c> running</c><00:01:59.219><c> here</c>

00:01:59.630 --> 00:01:59.640 align:start position:0%
app okay here's our server running here
 

00:01:59.640 --> 00:02:04.860 align:start position:0%
app okay here's our server running here
i<00:02:00.409><c> wonderful</c><00:02:01.409><c> nodejs</c><00:02:02.070><c> server</c>

00:02:04.860 --> 00:02:04.870 align:start position:0%
 
 

00:02:04.870 --> 00:02:10.910 align:start position:0%
 
[Music]

00:02:10.910 --> 00:02:10.920 align:start position:0%
 
 

00:02:10.920 --> 00:02:13.949 align:start position:0%
 
because<00:02:11.920><c> the</c><00:02:12.010><c> way</c><00:02:12.129><c> that</c><00:02:12.159><c> intercom</c><00:02:12.959><c> functions</c>

00:02:13.949 --> 00:02:13.959 align:start position:0%
because the way that intercom functions
 

00:02:13.959 --> 00:02:15.390 align:start position:0%
because the way that intercom functions
is<00:02:14.110><c> that</c><00:02:14.140><c> if</c><00:02:14.349><c> it</c><00:02:14.439><c> sees</c><00:02:14.680><c> that</c><00:02:14.860><c> the</c><00:02:15.010><c> user</c><00:02:15.159><c> has</c><00:02:15.370><c> a</c>

00:02:15.390 --> 00:02:15.400 align:start position:0%
is that if it sees that the user has a
 

00:02:15.400 --> 00:02:17.640 align:start position:0%
is that if it sees that the user has a
specific<00:02:15.970><c> name</c><00:02:16.209><c> it's</c><00:02:16.390><c> gonna</c><00:02:16.540><c> say</c><00:02:17.230><c> oh</c><00:02:17.260><c> this</c>

00:02:17.640 --> 00:02:17.650 align:start position:0%
specific name it's gonna say oh this
 

00:02:17.650 --> 00:02:21.449 align:start position:0%
specific name it's gonna say oh this
specific<00:02:18.159><c> user</c><00:02:18.400><c> is</c><00:02:18.819><c> this</c><00:02:19.480><c> guy</c><00:02:19.689><c> here</c><00:02:20.200><c> so</c><00:02:20.560><c> that</c>

00:02:21.449 --> 00:02:21.459 align:start position:0%
specific user is this guy here so that
 

00:02:21.459 --> 00:02:24.660 align:start position:0%
specific user is this guy here so that
seems<00:02:21.790><c> to</c><00:02:21.970><c> be</c><00:02:22.500><c> what's</c><00:02:23.500><c> causing</c><00:02:23.769><c> the</c><00:02:24.220><c> mix-up</c>

00:02:24.660 --> 00:02:24.670 align:start position:0%
seems to be what's causing the mix-up
 

00:02:24.670 --> 00:02:26.970 align:start position:0%
seems to be what's causing the mix-up
should<00:02:25.180><c> be</c><00:02:25.329><c> getting</c><00:02:25.599><c> cream</c><00:02:25.900><c> of</c><00:02:26.049><c> 1346</c><00:02:26.530><c> at</c>

00:02:26.970 --> 00:02:26.980 align:start position:0%
should be getting cream of 1346 at
 

00:02:26.980 --> 00:02:30.360 align:start position:0%
should be getting cream of 1346 at
gmail.com<00:02:27.480><c> also</c><00:02:28.480><c> added</c><00:02:28.810><c> here</c><00:02:29.140><c> then</c><00:02:30.069><c> we'll</c><00:02:30.250><c> be</c>

00:02:30.360 --> 00:02:30.370 align:start position:0%
gmail.com also added here then we'll be
 

00:02:30.370 --> 00:02:34.979 align:start position:0%
gmail.com also added here then we'll be
able<00:02:30.459><c> to</c><00:02:30.610><c> send</c><00:02:30.909><c> him</c><00:02:31.090><c> a</c><00:02:31.209><c> custom</c><00:02:31.690><c> email</c><00:02:33.989><c> again</c>

00:02:34.979 --> 00:02:34.989 align:start position:0%
able to send him a custom email again
 

00:02:34.989 --> 00:02:38.059 align:start position:0%
able to send him a custom email again
intercom<00:02:35.590><c> refreshes</c><00:02:36.069><c> every</c><00:02:36.340><c> two</c><00:02:36.519><c> hours</c><00:02:36.549><c> and</c>

00:02:38.059 --> 00:02:38.069 align:start position:0%
intercom refreshes every two hours and
 

00:02:38.069 --> 00:02:41.250 align:start position:0%
intercom refreshes every two hours and
starts<00:02:39.069><c> asking</c><00:02:39.220><c> these</c><00:02:39.609><c> questions</c><00:02:40.109><c> when</c><00:02:41.109><c> it's</c>

00:02:41.250 --> 00:02:41.260 align:start position:0%
starts asking these questions when it's
 

00:02:41.260 --> 00:02:43.920 align:start position:0%
starts asking these questions when it's
running<00:02:41.530><c> at</c><00:02:41.650><c> smart</c><00:02:41.950><c> campaigns</c><00:02:42.900><c> awesome</c>

00:02:43.920 --> 00:02:43.930 align:start position:0%
running at smart campaigns awesome
 

00:02:43.930 --> 00:02:47.220 align:start position:0%
running at smart campaigns awesome
here's<00:02:44.470><c> crema</c><00:02:44.739><c> 1346</c><00:02:45.730><c> usually</c><00:02:46.060><c> I</c><00:02:46.090><c> give</c><00:02:46.269><c> 108</c><00:02:46.690><c> he</c>

00:02:47.220 --> 00:02:47.230 align:start position:0%
here's crema 1346 usually I give 108 he
 

00:02:47.230 --> 00:02:50.190 align:start position:0%
here's crema 1346 usually I give 108 he
was<00:02:47.379><c> added</c><00:02:47.709><c> beforehand</c><00:02:48.510><c> okay</c><00:02:49.510><c> so</c><00:02:49.840><c> that</c><00:02:49.989><c> takes</c>

00:02:50.190 --> 00:02:50.200 align:start position:0%
was added beforehand okay so that takes
 

00:02:50.200 --> 00:02:52.199 align:start position:0%
was added beforehand okay so that takes
care<00:02:50.349><c> of</c><00:02:50.500><c> that</c><00:02:50.620><c> and</c><00:02:50.980><c> now</c><00:02:51.250><c> we</c><00:02:51.310><c> want</c><00:02:51.730><c> to</c><00:02:51.819><c> build</c><00:02:52.000><c> a</c>

00:02:52.199 --> 00:02:52.209 align:start position:0%
care of that and now we want to build a
 

00:02:52.209 --> 00:02:55.530 align:start position:0%
care of that and now we want to build a
custom<00:02:52.889><c> message</c><00:02:53.889><c> right</c><00:02:54.430><c> which</c><00:02:54.760><c> targets</c><00:02:55.299><c> these</c>

00:02:55.530 --> 00:02:55.540 align:start position:0%
custom message right which targets these
 

00:02:55.540 --> 00:02:59.369 align:start position:0%
custom message right which targets these
specific<00:02:56.230><c> people</c><00:02:56.519><c> very</c><00:02:57.519><c> 13:46</c><00:02:58.480><c> Jeff</c><00:02:58.720><c> calm</c>

00:02:59.369 --> 00:02:59.379 align:start position:0%
specific people very 13:46 Jeff calm
 

00:02:59.379 --> 00:03:01.830 align:start position:0%
specific people very 13:46 Jeff calm
okay<00:02:59.799><c> and</c><00:03:00.040><c> this</c><00:03:00.159><c> is</c><00:03:00.220><c> for</c><00:03:00.970><c> example</c><00:03:01.090><c> anyone</c><00:03:01.690><c> that</c>

00:03:01.830 --> 00:03:01.840 align:start position:0%
okay and this is for example anyone that
 

00:03:01.840 --> 00:03:05.339 align:start position:0%
okay and this is for example anyone that
asked<00:03:02.260><c> to</c><00:03:02.349><c> upgrade</c><00:03:03.150><c> the</c><00:03:04.150><c> user</c><00:03:04.420><c> data</c><00:03:04.720><c> attribute</c>

00:03:05.339 --> 00:03:05.349 align:start position:0%
asked to upgrade the user data attribute
 

00:03:05.349 --> 00:03:07.080 align:start position:0%
asked to upgrade the user data attribute
is<00:03:05.470><c> this</c><00:03:05.920><c> person</c><00:03:06.310><c> wants</c><00:03:06.549><c> to</c><00:03:06.579><c> upgrade</c><00:03:06.790><c> we're</c>

00:03:07.080 --> 00:03:07.090 align:start position:0%
is this person wants to upgrade we're
 

00:03:07.090 --> 00:03:09.390 align:start position:0%
is this person wants to upgrade we're
gonna<00:03:07.180><c> create</c><00:03:07.480><c> a</c><00:03:07.569><c> smart</c><00:03:08.049><c> campaign</c><00:03:08.290><c> which</c><00:03:09.220><c> will</c>

00:03:09.390 --> 00:03:09.400 align:start position:0%
gonna create a smart campaign which will
 

00:03:09.400 --> 00:03:12.210 align:start position:0%
gonna create a smart campaign which will
throw<00:03:09.700><c> people</c><00:03:09.940><c> into</c><00:03:10.389><c> this</c><00:03:11.049><c> funnel</c><00:03:11.440><c> every</c><00:03:11.980><c> time</c>

00:03:12.210 --> 00:03:12.220 align:start position:0%
throw people into this funnel every time
 

00:03:12.220 --> 00:03:14.699 align:start position:0%
throw people into this funnel every time
that<00:03:12.519><c> they</c><00:03:13.359><c> press</c><00:03:13.629><c> the</c><00:03:13.810><c> upgrade</c><00:03:14.199><c> button</c><00:03:14.440><c> and</c>

00:03:14.699 --> 00:03:14.709 align:start position:0%
that they press the upgrade button and
 

00:03:14.709 --> 00:03:17.520 align:start position:0%
that they press the upgrade button and
here<00:03:14.980><c> is</c><00:03:15.190><c> a</c><00:03:15.810><c> campaign</c><00:03:16.810><c> that</c><00:03:17.019><c> we</c><00:03:17.169><c> will</c><00:03:17.349><c> be</c><00:03:17.500><c> using</c>

00:03:17.520 --> 00:03:17.530 align:start position:0%
here is a campaign that we will be using
 

00:03:17.530 --> 00:03:21.569 align:start position:0%
here is a campaign that we will be using
we<00:03:18.220><c> will</c><00:03:18.250><c> say</c><00:03:18.900><c> we</c><00:03:19.900><c> want</c><00:03:20.139><c> a</c><00:03:20.410><c> new</c><00:03:20.799><c> user</c><00:03:21.220><c> Auto</c>

00:03:21.569 --> 00:03:21.579 align:start position:0%
we will say we want a new user Auto
 

00:03:21.579 --> 00:03:24.390 align:start position:0%
we will say we want a new user Auto
message<00:03:22.150><c> okay</c><00:03:22.840><c> you</c><00:03:23.829><c> know</c><00:03:23.919><c> what</c><00:03:24.099><c> we</c><00:03:24.280><c> could</c>

00:03:24.390 --> 00:03:24.400 align:start position:0%
message okay you know what we could
 

00:03:24.400 --> 00:03:25.920 align:start position:0%
message okay you know what we could
either<00:03:24.519><c> do</c><00:03:24.699><c> it</c><00:03:24.730><c> through</c><00:03:24.849><c> a</c><00:03:25.000><c> campaign</c><00:03:25.510><c> in</c><00:03:25.810><c> this</c>

00:03:25.920 --> 00:03:25.930 align:start position:0%
either do it through a campaign in this
 

00:03:25.930 --> 00:03:27.839 align:start position:0%
either do it through a campaign in this
example<00:03:26.379><c> I'm</c><00:03:26.859><c> just</c><00:03:27.040><c> going</c><00:03:27.190><c> to</c><00:03:27.220><c> use</c><00:03:27.340><c> a</c><00:03:27.370><c> regular</c>

00:03:27.839 --> 00:03:27.849 align:start position:0%
example I'm just going to use a regular
 

00:03:27.849 --> 00:03:31.069 align:start position:0%
example I'm just going to use a regular
user<00:03:28.030><c> Auto</c><00:03:28.329><c> message</c><00:03:28.840><c> and</c><00:03:29.109><c> new</c><00:03:30.069><c> idle</c><00:03:30.400><c> message</c>

00:03:31.069 --> 00:03:31.079 align:start position:0%
user Auto message and new idle message
 

00:03:31.079 --> 00:03:38.390 align:start position:0%
user Auto message and new idle message
later<00:03:32.079><c> I</c><00:03:32.230><c> created</c><00:03:32.560><c> a</c><00:03:32.709><c> campaign</c><00:03:36.569><c> in</c><00:03:37.569><c> a</c><00:03:37.690><c> momento</c>

00:03:38.390 --> 00:03:38.400 align:start position:0%
later I created a campaign in a momento
 

00:03:38.400 --> 00:03:49.979 align:start position:0%
later I created a campaign in a momento
and<00:03:41.069><c> upgrade</c><00:03:44.340><c> upgrade</c><00:03:47.040><c> request</c><00:03:48.040><c> it</c><00:03:48.989><c> choose</c>

00:03:49.979 --> 00:03:49.989 align:start position:0%
and upgrade upgrade request it choose
 

00:03:49.989 --> 00:03:51.379 align:start position:0%
and upgrade upgrade request it choose
your<00:03:50.139><c> audience</c>

00:03:51.379 --> 00:03:51.389 align:start position:0%
your audience
 

00:03:51.389 --> 00:03:55.640 align:start position:0%
your audience
anyone<00:03:52.389><c> that</c><00:03:52.959><c> has</c><00:03:53.019><c> upgrade</c><00:03:53.680><c> requests</c>

00:03:55.640 --> 00:03:55.650 align:start position:0%
anyone that has upgrade requests
 

00:03:55.650 --> 00:04:00.780 align:start position:0%
anyone that has upgrade requests
requests<00:03:56.650><c> there</c><00:03:57.190><c> it</c><00:03:57.340><c> is</c><00:03:57.810><c> set</c><00:03:58.810><c> the</c><00:03:58.959><c> true</c><00:03:59.790><c> but</c>

00:04:00.780 --> 00:04:00.790 align:start position:0%
requests there it is set the true but
 

00:04:00.790 --> 00:04:02.069 align:start position:0%
requests there it is set the true but
I've<00:04:00.970><c> been</c><00:04:01.090><c> ready</c><00:04:01.329><c> boom</c><00:04:01.540><c> we've</c><00:04:01.720><c> got</c><00:04:01.870><c> two</c>

00:04:02.069 --> 00:04:02.079 align:start position:0%
I've been ready boom we've got two
 

00:04:02.079 --> 00:04:04.259 align:start position:0%
I've been ready boom we've got two
people<00:04:02.290><c> and</c><00:04:02.739><c> we</c><00:04:03.099><c> want</c><00:04:03.310><c> to</c><00:04:03.400><c> add</c><00:04:03.549><c> multiple</c>

00:04:04.259 --> 00:04:04.269 align:start position:0%
people and we want to add multiple
 

00:04:04.269 --> 00:04:08.219 align:start position:0%
people and we want to add multiple
filters<00:04:05.549><c> over</c><00:04:06.549><c> here</c><00:04:06.819><c> and</c><00:04:07.000><c> whose</c><00:04:07.419><c> user</c><00:04:07.659><c> ID</c><00:04:07.959><c> is</c>

00:04:08.219 --> 00:04:08.229 align:start position:0%
filters over here and whose user ID is
 

00:04:08.229 --> 00:04:09.479 align:start position:0%
filters over here and whose user ID is
108<00:04:08.680><c> okay</c>

00:04:09.479 --> 00:04:09.489 align:start position:0%
108 okay
 

00:04:09.489 --> 00:04:14.159 align:start position:0%
108 okay
and<00:04:09.669><c> whose</c><00:04:10.329><c> user</c><00:04:10.569><c> ID</c><00:04:10.840><c> is</c><00:04:11.079><c> not</c><00:04:11.500><c> user</c><00:04:12.489><c> ID</c><00:04:12.639><c> is</c><00:04:13.419><c> not</c>

00:04:14.159 --> 00:04:14.169 align:start position:0%
and whose user ID is not user ID is not
 

00:04:14.169 --> 00:04:18.580 align:start position:0%
and whose user ID is not user ID is not
105<00:04:15.720><c> it</c><00:04:16.720><c> is</c><00:04:17.169><c> not</c>

00:04:18.580 --> 00:04:18.590 align:start position:0%
105 it is not
 

00:04:18.590 --> 00:04:20.749 align:start position:0%
105 it is not
cancel<00:04:19.590><c> that</c><00:04:19.620><c> this</c><00:04:20.009><c> guy</c><00:04:20.280><c> because</c><00:04:20.549><c> I</c><00:04:20.669><c> don't</c>

00:04:20.749 --> 00:04:20.759 align:start position:0%
cancel that this guy because I don't
 

00:04:20.759 --> 00:04:23.390 align:start position:0%
cancel that this guy because I don't
have<00:04:21.000><c> access</c><00:04:21.329><c> to</c><00:04:21.479><c> that</c><00:04:21.569><c> email</c><00:04:22.099><c> and</c><00:04:23.099><c> I</c><00:04:23.280><c> don't</c>

00:04:23.390 --> 00:04:23.400 align:start position:0%
have access to that email and I don't
 

00:04:23.400 --> 00:04:25.939 align:start position:0%
have access to that email and I don't
want<00:04:23.639><c> the</c><00:04:23.789><c> intercom</c><00:04:24.289><c> domain</c><00:04:25.289><c> getting</c><00:04:25.710><c> bent</c>

00:04:25.939 --> 00:04:25.949 align:start position:0%
want the intercom domain getting bent
 

00:04:25.949 --> 00:04:26.869 align:start position:0%
want the intercom domain getting bent
and<00:04:26.250><c> there</c><00:04:26.580><c> we</c><00:04:26.729><c> are</c>

00:04:26.869 --> 00:04:26.879 align:start position:0%
and there we are
 

00:04:26.879 --> 00:04:29.510 align:start position:0%
and there we are
Leisha<00:04:27.120><c> capiche</c><00:04:27.539><c> capiche</c><00:04:28.229><c> Leisha</c><00:04:29.099><c> cake</c><00:04:29.400><c> to</c>

00:04:29.510 --> 00:04:29.520 align:start position:0%
Leisha capiche capiche Leisha cake to
 

00:04:29.520 --> 00:04:31.159 align:start position:0%
Leisha capiche capiche Leisha cake to
fish<00:04:29.699><c> is</c><00:04:29.849><c> the</c><00:04:29.969><c> only</c><00:04:30.180><c> one</c><00:04:30.330><c> here</c><00:04:30.629><c> here's</c><00:04:30.960><c> the</c>

00:04:31.159 --> 00:04:31.169 align:start position:0%
fish is the only one here here's the
 

00:04:31.169 --> 00:04:36.559 align:start position:0%
fish is the only one here here's the
content<00:04:32.539><c> okay</c><00:04:33.569><c> Oh</c><00:04:34.039><c> 433</c><00:04:35.039><c> the</c><00:04:35.789><c> content</c><00:04:36.180><c> is</c><00:04:36.389><c> gonna</c>

00:04:36.559 --> 00:04:36.569 align:start position:0%
content okay Oh 433 the content is gonna
 

00:04:36.569 --> 00:04:44.450 align:start position:0%
content okay Oh 433 the content is gonna
be<00:04:36.750><c> an</c><00:04:36.900><c> in-app</c><00:04:37.349><c> message</c><00:04:38.569><c> which</c><00:04:39.569><c> says</c><00:04:43.250><c> no</c><00:04:44.250><c> it's</c>

00:04:44.450 --> 00:04:44.460 align:start position:0%
be an in-app message which says no it's
 

00:04:44.460 --> 00:04:49.249 align:start position:0%
be an in-app message which says no it's
gonna<00:04:44.819><c> be</c><00:04:44.969><c> an</c><00:04:45.060><c> email</c><00:04:46.069><c> also</c><00:04:47.069><c> and</c><00:04:47.430><c> you</c><00:04:47.520><c> know</c><00:04:48.259><c> let</c>

00:04:49.249 --> 00:04:49.259 align:start position:0%
gonna be an email also and you know let
 

00:04:49.259 --> 00:04:51.740 align:start position:0%
gonna be an email also and you know let
the<00:04:49.409><c> init</c><00:04:49.650><c> messages</c><00:04:50.219><c> in</c><00:04:50.460><c> future</c><00:04:51.060><c> tutorial</c>

00:04:51.740 --> 00:04:51.750 align:start position:0%
the init messages in future tutorial
 

00:04:51.750 --> 00:04:54.279 align:start position:0%
the init messages in future tutorial
series<00:04:52.020><c> but</c><00:04:52.469><c> that's</c><00:04:52.650><c> pretty</c><00:04:52.889><c> cool</c><00:04:53.129><c> stuff</c><00:04:53.550><c> too</c>

00:04:54.279 --> 00:04:54.289 align:start position:0%
series but that's pretty cool stuff too
 

00:04:54.289 --> 00:04:57.080 align:start position:0%
series but that's pretty cool stuff too
okay<00:04:55.289><c> and</c><00:04:55.560><c> we're</c><00:04:56.219><c> gonna</c><00:04:56.340><c> write</c><00:04:56.550><c> the</c><00:04:56.639><c> content</c>

00:04:57.080 --> 00:04:57.090 align:start position:0%
okay and we're gonna write the content
 

00:04:57.090 --> 00:04:58.670 align:start position:0%
okay and we're gonna write the content
in<00:04:57.240><c> the</c><00:04:57.360><c> next</c><00:04:57.629><c> lesson</c><00:04:58.199><c> thanks</c><00:04:58.529><c> guys</c><00:04:58.650><c> for</c>

00:04:58.670 --> 00:04:58.680 align:start position:0%
in the next lesson thanks guys for
 

00:04:58.680 --> 00:05:02.029 align:start position:0%
in the next lesson thanks guys for
tuning<00:04:58.979><c> in</c><00:04:59.219><c> come</c><00:04:59.490><c> on</c><00:04:59.610><c> George</c>

