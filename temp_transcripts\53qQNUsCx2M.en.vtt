WEBVTT
Kind: captions
Language: en

00:00:02.320 --> 00:00:04.390 align:start position:0%
 
the<00:00:02.560><c> latest</c><00:00:02.960><c> release</c><00:00:03.280><c> of</c><00:00:03.360><c> quadrant</c><00:00:03.919><c> is</c><00:00:04.080><c> a</c><00:00:04.160><c> huge</c>

00:00:04.390 --> 00:00:04.400 align:start position:0%
the latest release of quadrant is a huge
 

00:00:04.400 --> 00:00:06.789 align:start position:0%
the latest release of quadrant is a huge
step<00:00:04.720><c> towards</c><00:00:05.120><c> scalability</c>

00:00:06.789 --> 00:00:06.799 align:start position:0%
step towards scalability
 

00:00:06.799 --> 00:00:08.950 align:start position:0%
step towards scalability
we<00:00:06.960><c> have</c><00:00:07.120><c> just</c><00:00:07.440><c> released</c><00:00:07.919><c> an</c><00:00:08.080><c> experimental</c>

00:00:08.950 --> 00:00:08.960 align:start position:0%
we have just released an experimental
 

00:00:08.960 --> 00:00:11.830 align:start position:0%
we have just released an experimental
distributed<00:00:09.679><c> mode</c><00:00:10.480><c> that</c><00:00:10.719><c> allows</c><00:00:11.120><c> you</c><00:00:11.280><c> to</c>

00:00:11.830 --> 00:00:11.840 align:start position:0%
distributed mode that allows you to
 

00:00:11.840 --> 00:00:14.950 align:start position:0%
distributed mode that allows you to
create<00:00:12.320><c> a</c><00:00:12.480><c> cluster</c><00:00:13.120><c> of</c><00:00:13.280><c> quadrant</c>

00:00:14.950 --> 00:00:14.960 align:start position:0%
create a cluster of quadrant
 

00:00:14.960 --> 00:00:18.070 align:start position:0%
create a cluster of quadrant
running<00:00:15.440><c> on</c><00:00:15.839><c> several</c><00:00:16.320><c> different</c><00:00:16.640><c> machines</c>

00:00:18.070 --> 00:00:18.080 align:start position:0%
running on several different machines
 

00:00:18.080 --> 00:00:19.750 align:start position:0%
running on several different machines
this<00:00:18.320><c> is</c><00:00:18.480><c> also</c><00:00:18.800><c> fairly</c><00:00:19.199><c> easy</c><00:00:19.520><c> to</c><00:00:19.600><c> be</c>

00:00:19.750 --> 00:00:19.760 align:start position:0%
this is also fairly easy to be
 

00:00:19.760 --> 00:00:22.070 align:start position:0%
this is also fairly easy to be
replicated<00:00:20.480><c> locally</c><00:00:21.119><c> if</c><00:00:21.279><c> you</c><00:00:21.439><c> are</c><00:00:21.600><c> familiar</c>

00:00:22.070 --> 00:00:22.080 align:start position:0%
replicated locally if you are familiar
 

00:00:22.080 --> 00:00:24.470 align:start position:0%
replicated locally if you are familiar
with<00:00:22.320><c> docker</c><00:00:22.720><c> and</c><00:00:22.800><c> docker</c><00:00:23.199><c> compose</c><00:00:24.160><c> you</c><00:00:24.320><c> can</c>

00:00:24.470 --> 00:00:24.480 align:start position:0%
with docker and docker compose you can
 

00:00:24.480 --> 00:00:27.189 align:start position:0%
with docker and docker compose you can
just<00:00:24.960><c> launch</c><00:00:25.359><c> your</c><00:00:25.680><c> own</c><00:00:25.920><c> cluster</c><00:00:26.560><c> within</c><00:00:26.960><c> just</c>

00:00:27.189 --> 00:00:27.199 align:start position:0%
just launch your own cluster within just
 

00:00:27.199 --> 00:00:28.470 align:start position:0%
just launch your own cluster within just
a<00:00:27.279><c> few</c><00:00:27.519><c> minutes</c>

00:00:28.470 --> 00:00:28.480 align:start position:0%
a few minutes
 

00:00:28.480 --> 00:00:30.470 align:start position:0%
a few minutes
and<00:00:28.800><c> in</c><00:00:28.880><c> this</c><00:00:29.039><c> tutorial</c><00:00:29.599><c> we</c><00:00:29.760><c> are</c><00:00:29.920><c> going</c><00:00:30.240><c> to</c>

00:00:30.470 --> 00:00:30.480 align:start position:0%
and in this tutorial we are going to
 

00:00:30.480 --> 00:00:33.350 align:start position:0%
and in this tutorial we are going to
cover<00:00:30.880><c> some</c><00:00:31.039><c> basics</c><00:00:32.079><c> and</c><00:00:32.320><c> show</c><00:00:32.559><c> you</c><00:00:32.960><c> how</c><00:00:33.120><c> to</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
cover some basics and show you how to
 

00:00:33.360 --> 00:00:37.430 align:start position:0%
cover some basics and show you how to
create<00:00:33.760><c> your</c><00:00:34.160><c> own</c><00:00:34.880><c> quadrant</c><00:00:35.440><c> cluster</c><00:00:36.000><c> locally</c>

00:00:37.430 --> 00:00:37.440 align:start position:0%
create your own quadrant cluster locally
 

00:00:37.440 --> 00:00:39.430 align:start position:0%
create your own quadrant cluster locally
we<00:00:37.600><c> are</c><00:00:37.760><c> going</c><00:00:37.920><c> to</c><00:00:38.079><c> be</c><00:00:38.239><c> using</c><00:00:38.480><c> docker</c><00:00:38.879><c> compose</c>

00:00:39.430 --> 00:00:39.440 align:start position:0%
we are going to be using docker compose
 

00:00:39.440 --> 00:00:42.150 align:start position:0%
we are going to be using docker compose
so<00:00:39.920><c> nothing</c><00:00:40.320><c> easier</c><00:00:40.719><c> than</c><00:00:40.960><c> just</c><00:00:41.600><c> setting</c><00:00:42.000><c> the</c>

00:00:42.150 --> 00:00:42.160 align:start position:0%
so nothing easier than just setting the
 

00:00:42.160 --> 00:00:43.350 align:start position:0%
so nothing easier than just setting the
project<00:00:42.559><c> up</c>

00:00:43.350 --> 00:00:43.360 align:start position:0%
project up
 

00:00:43.360 --> 00:00:45.510 align:start position:0%
project up
and<00:00:43.680><c> providing</c><00:00:44.160><c> some</c><00:00:44.399><c> configuration</c><00:00:45.280><c> in</c><00:00:45.360><c> the</c>

00:00:45.510 --> 00:00:45.520 align:start position:0%
and providing some configuration in the
 

00:00:45.520 --> 00:00:47.910 align:start position:0%
and providing some configuration in the
docker<00:00:45.840><c> composition</c><00:00:46.640><c> file</c><00:00:47.200><c> so</c><00:00:47.440><c> let</c><00:00:47.600><c> me</c><00:00:47.760><c> just</c>

00:00:47.910 --> 00:00:47.920 align:start position:0%
docker composition file so let me just
 

00:00:47.920 --> 00:00:49.190 align:start position:0%
docker composition file so let me just
do<00:00:48.079><c> it</c>

00:00:49.190 --> 00:00:49.200 align:start position:0%
do it
 

00:00:49.200 --> 00:00:51.590 align:start position:0%
do it
okay<00:00:49.680><c> so</c><00:00:50.079><c> first</c><00:00:50.320><c> of</c><00:00:50.480><c> all</c><00:00:50.640><c> i</c><00:00:50.879><c> probably</c><00:00:51.280><c> need</c><00:00:51.440><c> to</c>

00:00:51.590 --> 00:00:51.600 align:start position:0%
okay so first of all i probably need to
 

00:00:51.600 --> 00:00:53.510 align:start position:0%
okay so first of all i probably need to
provide<00:00:52.000><c> the</c><00:00:52.160><c> version</c>

00:00:53.510 --> 00:00:53.520 align:start position:0%
provide the version
 

00:00:53.520 --> 00:00:56.869 align:start position:0%
provide the version
in<00:00:53.680><c> my</c><00:00:54.000><c> case</c><00:00:54.239><c> this</c><00:00:54.480><c> is</c><00:00:54.559><c> going</c><00:00:54.800><c> to</c><00:00:54.960><c> be</c><00:00:55.199><c> 3.9</c>

00:00:56.869 --> 00:00:56.879 align:start position:0%
in my case this is going to be 3.9
 

00:00:56.879 --> 00:01:00.229 align:start position:0%
in my case this is going to be 3.9
and<00:00:57.120><c> then</c><00:00:57.520><c> declare</c><00:00:58.320><c> services</c>

00:01:00.229 --> 00:01:00.239 align:start position:0%
and then declare services
 

00:01:00.239 --> 00:01:02.150 align:start position:0%
and then declare services
if<00:01:00.399><c> i</c><00:01:00.480><c> wanted</c><00:01:00.879><c> to</c><00:01:01.120><c> have</c><00:01:01.359><c> just</c><00:01:01.600><c> a</c><00:01:01.760><c> single</c>

00:01:02.150 --> 00:01:02.160 align:start position:0%
if i wanted to have just a single
 

00:01:02.160 --> 00:01:04.789 align:start position:0%
if i wanted to have just a single
instance<00:01:02.640><c> of</c><00:01:02.800><c> quadrant</c><00:01:03.840><c> then</c><00:01:04.080><c> i</c><00:01:04.239><c> would</c><00:01:04.479><c> just</c>

00:01:04.789 --> 00:01:04.799 align:start position:0%
instance of quadrant then i would just
 

00:01:04.799 --> 00:01:08.070 align:start position:0%
instance of quadrant then i would just
provide<00:01:05.439><c> configuration</c><00:01:06.720><c> like</c><00:01:06.960><c> that</c>

00:01:08.070 --> 00:01:08.080 align:start position:0%
provide configuration like that
 

00:01:08.080 --> 00:01:09.830 align:start position:0%
provide configuration like that
i<00:01:08.159><c> will</c><00:01:08.560><c> call</c><00:01:09.040><c> my</c>

00:01:09.830 --> 00:01:09.840 align:start position:0%
i will call my
 

00:01:09.840 --> 00:01:11.670 align:start position:0%
i will call my
container<00:01:10.479><c> quadrant</c>

00:01:11.670 --> 00:01:11.680 align:start position:0%
container quadrant
 

00:01:11.680 --> 00:01:14.149 align:start position:0%
container quadrant
and<00:01:12.000><c> use</c><00:01:12.320><c> the</c><00:01:12.640><c> official</c><00:01:13.280><c> image</c><00:01:13.760><c> that</c><00:01:14.000><c> we</c>

00:01:14.149 --> 00:01:14.159 align:start position:0%
and use the official image that we
 

00:01:14.159 --> 00:01:16.070 align:start position:0%
and use the official image that we
provide

00:01:16.070 --> 00:01:16.080 align:start position:0%
provide
 

00:01:16.080 --> 00:01:18.630 align:start position:0%
provide
which<00:01:16.320><c> is</c><00:01:16.560><c> quadrant</c>

00:01:18.630 --> 00:01:18.640 align:start position:0%
which is quadrant
 

00:01:18.640 --> 00:01:22.550 align:start position:0%
which is quadrant
and<00:01:18.880><c> the</c><00:01:19.040><c> latest</c><00:01:19.520><c> version</c><00:01:19.920><c> which</c><00:01:20.159><c> is</c>

00:01:22.550 --> 00:01:22.560 align:start position:0%
 
 

00:01:22.560 --> 00:01:26.230 align:start position:0%
 
0.8.0

00:01:26.230 --> 00:01:26.240 align:start position:0%
 
 

00:01:26.240 --> 00:01:28.550 align:start position:0%
 
and<00:01:26.400><c> that</c><00:01:26.560><c> would</c><00:01:26.720><c> be</c><00:01:26.960><c> it</c><00:01:27.520><c> of</c><00:01:27.680><c> course</c><00:01:28.080><c> i</c><00:01:28.320><c> won't</c>

00:01:28.550 --> 00:01:28.560 align:start position:0%
and that would be it of course i won't
 

00:01:28.560 --> 00:01:31.429 align:start position:0%
and that would be it of course i won't
be<00:01:28.799><c> able</c><00:01:29.119><c> to</c><00:01:29.439><c> communicate</c><00:01:30.079><c> with</c><00:01:30.640><c> such</c><00:01:31.040><c> created</c>

00:01:31.429 --> 00:01:31.439 align:start position:0%
be able to communicate with such created
 

00:01:31.439 --> 00:01:33.350 align:start position:0%
be able to communicate with such created
instance<00:01:31.920><c> because</c><00:01:32.159><c> i</c><00:01:32.320><c> haven't</c><00:01:32.640><c> exposed</c><00:01:33.119><c> any</c>

00:01:33.350 --> 00:01:33.360 align:start position:0%
instance because i haven't exposed any
 

00:01:33.360 --> 00:01:34.310 align:start position:0%
instance because i haven't exposed any
parts

00:01:34.310 --> 00:01:34.320 align:start position:0%
parts
 

00:01:34.320 --> 00:01:35.109 align:start position:0%
parts
but

00:01:35.109 --> 00:01:35.119 align:start position:0%
but
 

00:01:35.119 --> 00:01:37.429 align:start position:0%
but
basically<00:01:35.680><c> this</c><00:01:35.920><c> is</c><00:01:36.079><c> going</c><00:01:36.320><c> to</c><00:01:36.479><c> work</c><00:01:37.119><c> but</c><00:01:37.280><c> i</c>

00:01:37.429 --> 00:01:37.439 align:start position:0%
basically this is going to work but i
 

00:01:37.439 --> 00:01:40.469 align:start position:0%
basically this is going to work but i
want<00:01:37.600><c> to</c><00:01:37.759><c> have</c><00:01:38.000><c> a</c><00:01:38.479><c> cluster</c><00:01:39.040><c> of</c><00:01:39.200><c> quadrant</c><00:01:40.240><c> not</c>

00:01:40.469 --> 00:01:40.479 align:start position:0%
want to have a cluster of quadrant not
 

00:01:40.479 --> 00:01:42.950 align:start position:0%
want to have a cluster of quadrant not
just<00:01:40.799><c> a</c><00:01:40.880><c> single</c><00:01:41.280><c> instance</c><00:01:42.079><c> so</c><00:01:42.240><c> definitely</c><00:01:42.799><c> i</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
just a single instance so definitely i
 

00:01:42.960 --> 00:01:45.910 align:start position:0%
just a single instance so definitely i
need<00:01:43.200><c> to</c><00:01:43.360><c> create</c><00:01:43.840><c> some</c><00:01:44.240><c> more</c><00:01:44.560><c> containers</c><00:01:45.520><c> so</c>

00:01:45.910 --> 00:01:45.920 align:start position:0%
need to create some more containers so
 

00:01:45.920 --> 00:01:47.270 align:start position:0%
need to create some more containers so
let's<00:01:46.159><c> just</c><00:01:46.479><c> call</c>

00:01:47.270 --> 00:01:47.280 align:start position:0%
let's just call
 

00:01:47.280 --> 00:01:54.230 align:start position:0%
let's just call
this<00:01:47.520><c> first</c><00:01:47.840><c> one</c><00:01:48.399><c> quadrant</c><00:01:49.040><c> primary</c>

00:01:54.230 --> 00:01:54.240 align:start position:0%
 
 

00:01:54.240 --> 00:01:56.870 align:start position:0%
 
and<00:01:54.479><c> it</c><00:01:54.560><c> would</c><00:01:54.720><c> be</c><00:01:54.880><c> good</c><00:01:55.360><c> to</c><00:01:55.600><c> expose</c><00:01:56.240><c> the</c><00:01:56.640><c> port</c>

00:01:56.870 --> 00:01:56.880 align:start position:0%
and it would be good to expose the port
 

00:01:56.880 --> 00:01:59.990 align:start position:0%
and it would be good to expose the port
which<00:01:57.119><c> is</c><00:01:57.200><c> typically</c><00:01:57.840><c> used</c><00:01:58.240><c> for</c><00:01:58.479><c> http</c><00:01:59.360><c> api</c>

00:01:59.990 --> 00:02:00.000 align:start position:0%
which is typically used for http api
 

00:02:00.000 --> 00:02:01.670 align:start position:0%
which is typically used for http api
communication

00:02:01.670 --> 00:02:01.680 align:start position:0%
communication
 

00:02:01.680 --> 00:02:04.149 align:start position:0%
communication
so<00:02:01.920><c> i</c><00:02:02.079><c> will</c><00:02:02.240><c> just</c><00:02:02.399><c> do</c><00:02:02.560><c> that</c>

00:02:04.149 --> 00:02:04.159 align:start position:0%
so i will just do that
 

00:02:04.159 --> 00:02:08.469 align:start position:0%
so i will just do that
this<00:02:04.399><c> is</c><00:02:04.560><c> part</c><00:02:04.880><c> number</c><00:02:05.600><c> six</c><00:02:06.399><c> and</c><00:02:06.799><c> triple</c><00:02:07.280><c> three</c>

00:02:08.469 --> 00:02:08.479 align:start position:0%
this is part number six and triple three
 

00:02:08.479 --> 00:02:11.510 align:start position:0%
this is part number six and triple three
and<00:02:08.720><c> i</c><00:02:08.879><c> will</c><00:02:09.119><c> map</c><00:02:09.360><c> it</c><00:02:09.679><c> to</c><00:02:09.920><c> the</c><00:02:10.160><c> same</c><00:02:10.720><c> port</c><00:02:11.120><c> on</c><00:02:11.280><c> my</c>

00:02:11.510 --> 00:02:11.520 align:start position:0%
and i will map it to the same port on my
 

00:02:11.520 --> 00:02:13.030 align:start position:0%
and i will map it to the same port on my
machine

00:02:13.030 --> 00:02:13.040 align:start position:0%
machine
 

00:02:13.040 --> 00:02:14.630 align:start position:0%
machine
okay<00:02:13.360><c> so</c><00:02:13.599><c> right</c><00:02:13.840><c> now</c>

00:02:14.630 --> 00:02:14.640 align:start position:0%
okay so right now
 

00:02:14.640 --> 00:02:17.270 align:start position:0%
okay so right now
i<00:02:14.879><c> have</c><00:02:15.120><c> a</c><00:02:15.360><c> fully</c><00:02:15.680><c> operating</c><00:02:16.239><c> configuration</c>

00:02:17.270 --> 00:02:17.280 align:start position:0%
i have a fully operating configuration
 

00:02:17.280 --> 00:02:20.550 align:start position:0%
i have a fully operating configuration
for<00:02:17.599><c> a</c><00:02:17.680><c> single</c><00:02:18.160><c> instance</c><00:02:18.959><c> of</c><00:02:19.360><c> quadrant</c>

00:02:20.550 --> 00:02:20.560 align:start position:0%
for a single instance of quadrant
 

00:02:20.560 --> 00:02:24.869 align:start position:0%
for a single instance of quadrant
with<00:02:21.040><c> an</c><00:02:21.280><c> hdp</c><00:02:21.920><c> api</c><00:02:22.640><c> protocol</c><00:02:23.200><c> exposed</c>

00:02:24.869 --> 00:02:24.879 align:start position:0%
with an hdp api protocol exposed
 

00:02:24.879 --> 00:02:26.270 align:start position:0%
with an hdp api protocol exposed
via

00:02:26.270 --> 00:02:26.280 align:start position:0%
via
 

00:02:26.280 --> 00:02:28.390 align:start position:0%
via
633<00:02:27.360><c> port</c>

00:02:28.390 --> 00:02:28.400 align:start position:0%
633 port
 

00:02:28.400 --> 00:02:30.470 align:start position:0%
633 port
but<00:02:28.640><c> this</c><00:02:28.800><c> is</c><00:02:28.879><c> not</c><00:02:29.120><c> enough</c><00:02:29.520><c> i</c><00:02:29.760><c> also</c><00:02:30.080><c> want</c><00:02:30.319><c> to</c>

00:02:30.470 --> 00:02:30.480 align:start position:0%
but this is not enough i also want to
 

00:02:30.480 --> 00:02:33.110 align:start position:0%
but this is not enough i also want to
have<00:02:30.800><c> another</c><00:02:31.360><c> instance</c><00:02:32.239><c> running</c><00:02:32.640><c> within</c><00:02:32.959><c> the</c>

00:02:33.110 --> 00:02:33.120 align:start position:0%
have another instance running within the
 

00:02:33.120 --> 00:02:34.470 align:start position:0%
have another instance running within the
same<00:02:33.360><c> cluster</c>

00:02:34.470 --> 00:02:34.480 align:start position:0%
same cluster
 

00:02:34.480 --> 00:02:36.470 align:start position:0%
same cluster
so<00:02:34.800><c> i</c><00:02:34.959><c> can</c><00:02:35.120><c> just</c><00:02:35.440><c> copy</c>

00:02:36.470 --> 00:02:36.480 align:start position:0%
so i can just copy
 

00:02:36.480 --> 00:02:39.910 align:start position:0%
so i can just copy
part<00:02:36.879><c> of</c><00:02:36.959><c> that</c><00:02:37.200><c> configuration</c>

00:02:39.910 --> 00:02:39.920 align:start position:0%
part of that configuration
 

00:02:39.920 --> 00:02:42.150 align:start position:0%
part of that configuration
and<00:02:40.319><c> just</c><00:02:40.640><c> rename</c><00:02:41.040><c> it</c><00:02:41.280><c> to</c>

00:02:42.150 --> 00:02:42.160 align:start position:0%
and just rename it to
 

00:02:42.160 --> 00:02:46.390 align:start position:0%
and just rename it to
quadrant<00:02:42.800><c> secondary</c>

00:02:46.390 --> 00:02:46.400 align:start position:0%
 
 

00:02:46.400 --> 00:02:49.030 align:start position:0%
 
of<00:02:46.560><c> course</c><00:02:46.800><c> i</c><00:02:46.959><c> cannot</c><00:02:47.360><c> map</c><00:02:48.000><c> this</c><00:02:48.400><c> secondary</c>

00:02:49.030 --> 00:02:49.040 align:start position:0%
of course i cannot map this secondary
 

00:02:49.040 --> 00:02:51.430 align:start position:0%
of course i cannot map this secondary
instance<00:02:49.440><c> to</c><00:02:49.519><c> the</c><00:02:49.680><c> same</c><00:02:50.080><c> port</c><00:02:50.800><c> like</c><00:02:50.959><c> the</c><00:02:51.120><c> first</c>

00:02:51.430 --> 00:02:51.440 align:start position:0%
instance to the same port like the first
 

00:02:51.440 --> 00:02:53.750 align:start position:0%
instance to the same port like the first
one<00:02:51.599><c> but</c><00:02:51.840><c> it</c><00:02:52.000><c> doesn't</c><00:02:52.239><c> really</c><00:02:52.480><c> matter</c><00:02:53.280><c> in</c><00:02:53.440><c> our</c>

00:02:53.750 --> 00:02:53.760 align:start position:0%
one but it doesn't really matter in our
 

00:02:53.760 --> 00:02:54.630 align:start position:0%
one but it doesn't really matter in our
case

00:02:54.630 --> 00:02:54.640 align:start position:0%
case
 

00:02:54.640 --> 00:02:56.630 align:start position:0%
case
a<00:02:54.800><c> single</c><00:02:55.280><c> point</c><00:02:55.599><c> of</c><00:02:55.760><c> communication</c><00:02:56.480><c> is</c>

00:02:56.630 --> 00:02:56.640 align:start position:0%
a single point of communication is
 

00:02:56.640 --> 00:02:57.990 align:start position:0%
a single point of communication is
enough

00:02:57.990 --> 00:02:58.000 align:start position:0%
enough
 

00:02:58.000 --> 00:02:59.190 align:start position:0%
enough
but<00:02:58.159><c> okay</c>

00:02:59.190 --> 00:02:59.200 align:start position:0%
but okay
 

00:02:59.200 --> 00:03:01.270 align:start position:0%
but okay
right<00:02:59.440><c> now</c><00:02:59.680><c> we</c><00:02:59.840><c> have</c><00:03:00.000><c> a</c><00:03:00.080><c> configuration</c><00:03:01.040><c> of</c>

00:03:01.270 --> 00:03:01.280 align:start position:0%
right now we have a configuration of
 

00:03:01.280 --> 00:03:03.830 align:start position:0%
right now we have a configuration of
docker<00:03:01.680><c> compose</c><00:03:02.159><c> that</c><00:03:02.400><c> will</c><00:03:02.800><c> launch</c><00:03:03.519><c> two</c>

00:03:03.830 --> 00:03:03.840 align:start position:0%
docker compose that will launch two
 

00:03:03.840 --> 00:03:05.509 align:start position:0%
docker compose that will launch two
different<00:03:04.319><c> containers</c>

00:03:05.509 --> 00:03:05.519 align:start position:0%
different containers
 

00:03:05.519 --> 00:03:06.390 align:start position:0%
different containers
but

00:03:06.390 --> 00:03:06.400 align:start position:0%
but
 

00:03:06.400 --> 00:03:08.869 align:start position:0%
but
none<00:03:06.640><c> of</c><00:03:06.800><c> them</c><00:03:07.120><c> would</c><00:03:07.519><c> even</c><00:03:07.840><c> know</c><00:03:08.239><c> about</c><00:03:08.640><c> the</c>

00:03:08.869 --> 00:03:08.879 align:start position:0%
none of them would even know about the
 

00:03:08.879 --> 00:03:10.710 align:start position:0%
none of them would even know about the
existence<00:03:09.519><c> of</c><00:03:09.599><c> the</c><00:03:09.840><c> other</c>

00:03:10.710 --> 00:03:10.720 align:start position:0%
existence of the other
 

00:03:10.720 --> 00:03:13.430 align:start position:0%
existence of the other
so<00:03:11.040><c> they</c><00:03:11.200><c> won't</c><00:03:11.360><c> be</c><00:03:11.599><c> able</c><00:03:11.840><c> to</c><00:03:12.000><c> communicate</c>

00:03:13.430 --> 00:03:13.440 align:start position:0%
so they won't be able to communicate
 

00:03:13.440 --> 00:03:15.990 align:start position:0%
so they won't be able to communicate
and<00:03:13.920><c> we</c><00:03:14.080><c> need</c><00:03:14.319><c> to</c><00:03:14.720><c> fix</c><00:03:15.040><c> that</c><00:03:15.360><c> as</c><00:03:15.599><c> soon</c><00:03:15.840><c> as</c>

00:03:15.990 --> 00:03:16.000 align:start position:0%
and we need to fix that as soon as
 

00:03:16.000 --> 00:03:17.030 align:start position:0%
and we need to fix that as soon as
possible

00:03:17.030 --> 00:03:17.040 align:start position:0%
possible
 

00:03:17.040 --> 00:03:19.910 align:start position:0%
possible
basically<00:03:17.519><c> quadrant</c><00:03:18.480><c> accepts</c><00:03:19.120><c> an</c><00:03:19.280><c> additional</c>

00:03:19.910 --> 00:03:19.920 align:start position:0%
basically quadrant accepts an additional
 

00:03:19.920 --> 00:03:22.790 align:start position:0%
basically quadrant accepts an additional
attribute

00:03:22.790 --> 00:03:22.800 align:start position:0%
 
 

00:03:22.800 --> 00:03:25.430 align:start position:0%
 
but<00:03:23.200><c> there</c><00:03:23.440><c> is</c><00:03:23.599><c> also</c><00:03:23.920><c> another</c><00:03:24.400><c> thing</c>

00:03:25.430 --> 00:03:25.440 align:start position:0%
but there is also another thing
 

00:03:25.440 --> 00:03:28.789 align:start position:0%
but there is also another thing
we<00:03:25.760><c> need</c><00:03:26.000><c> to</c><00:03:26.560><c> let</c><00:03:26.879><c> quadrant</c><00:03:27.519><c> know</c><00:03:28.239><c> that</c><00:03:28.560><c> this</c>

00:03:28.789 --> 00:03:28.799 align:start position:0%
we need to let quadrant know that this
 

00:03:28.799 --> 00:03:31.430 align:start position:0%
we need to let quadrant know that this
is<00:03:28.959><c> going</c><00:03:29.280><c> to</c><00:03:29.440><c> be</c><00:03:29.680><c> launched</c><00:03:30.239><c> in</c>

00:03:31.430 --> 00:03:31.440 align:start position:0%
is going to be launched in
 

00:03:31.440 --> 00:03:32.869 align:start position:0%
is going to be launched in
cluster<00:03:31.920><c> mode</c>

00:03:32.869 --> 00:03:32.879 align:start position:0%
cluster mode
 

00:03:32.879 --> 00:03:34.869 align:start position:0%
cluster mode
and<00:03:33.200><c> this</c><00:03:33.440><c> can</c><00:03:33.599><c> be</c><00:03:33.760><c> done</c><00:03:34.159><c> in</c><00:03:34.319><c> two</c><00:03:34.480><c> different</c>

00:03:34.869 --> 00:03:34.879 align:start position:0%
and this can be done in two different
 

00:03:34.879 --> 00:03:37.830 align:start position:0%
and this can be done in two different
ways<00:03:35.440><c> first</c><00:03:35.760><c> of</c><00:03:35.920><c> all</c><00:03:36.080><c> we</c><00:03:36.159><c> can</c><00:03:36.480><c> provide</c><00:03:37.120><c> it</c><00:03:37.360><c> as</c><00:03:37.599><c> a</c>

00:03:37.830 --> 00:03:37.840 align:start position:0%
ways first of all we can provide it as a
 

00:03:37.840 --> 00:03:40.229 align:start position:0%
ways first of all we can provide it as a
configuration<00:03:38.720><c> entry</c><00:03:39.280><c> if</c><00:03:39.440><c> we</c><00:03:39.760><c> change</c>

00:03:40.229 --> 00:03:40.239 align:start position:0%
configuration entry if we change
 

00:03:40.239 --> 00:03:42.710 align:start position:0%
configuration entry if we change
anything<00:03:40.879><c> in</c><00:03:41.280><c> default</c><00:03:41.760><c> configuration</c><00:03:42.560><c> which</c>

00:03:42.710 --> 00:03:42.720 align:start position:0%
anything in default configuration which
 

00:03:42.720 --> 00:03:45.350 align:start position:0%
anything in default configuration which
is<00:03:42.799><c> typically</c><00:03:43.440><c> yaml</c><00:03:43.920><c> file</c><00:03:44.560><c> then</c><00:03:44.720><c> we</c><00:03:44.879><c> can</c><00:03:45.120><c> also</c>

00:03:45.350 --> 00:03:45.360 align:start position:0%
is typically yaml file then we can also
 

00:03:45.360 --> 00:03:47.589 align:start position:0%
is typically yaml file then we can also
do<00:03:45.519><c> that</c><00:03:46.000><c> but</c><00:03:46.239><c> in</c><00:03:46.400><c> our</c><00:03:46.720><c> case</c><00:03:47.040><c> we</c><00:03:47.200><c> are</c><00:03:47.360><c> just</c>

00:03:47.589 --> 00:03:47.599 align:start position:0%
do that but in our case we are just
 

00:03:47.599 --> 00:03:50.470 align:start position:0%
do that but in our case we are just
starting<00:03:48.239><c> very</c><00:03:48.480><c> simple</c><00:03:49.360><c> so</c><00:03:49.519><c> we</c><00:03:49.680><c> can</c><00:03:50.080><c> use</c><00:03:50.319><c> the</c>

00:03:50.470 --> 00:03:50.480 align:start position:0%
starting very simple so we can use the
 

00:03:50.480 --> 00:03:53.509 align:start position:0%
starting very simple so we can use the
second<00:03:50.799><c> approach</c><00:03:51.280><c> which</c><00:03:51.519><c> is</c><00:03:51.920><c> to</c><00:03:52.480><c> provide</c><00:03:53.200><c> an</c>

00:03:53.509 --> 00:03:53.519 align:start position:0%
second approach which is to provide an
 

00:03:53.519 --> 00:03:56.070 align:start position:0%
second approach which is to provide an
environmental<00:03:54.239><c> variable</c><00:03:55.120><c> that</c><00:03:55.360><c> will</c>

00:03:56.070 --> 00:03:56.080 align:start position:0%
environmental variable that will
 

00:03:56.080 --> 00:03:58.949 align:start position:0%
environmental variable that will
actually<00:03:56.480><c> do</c><00:03:57.120><c> the</c><00:03:57.360><c> same</c><00:03:57.680><c> thing</c>

00:03:58.949 --> 00:03:58.959 align:start position:0%
actually do the same thing
 

00:03:58.959 --> 00:04:01.910 align:start position:0%
actually do the same thing
so<00:03:59.360><c> we</c><00:03:59.519><c> can</c><00:03:59.680><c> just</c><00:04:00.000><c> start</c><00:04:00.319><c> right</c><00:04:00.560><c> away</c>

00:04:01.910 --> 00:04:01.920 align:start position:0%
so we can just start right away
 

00:04:01.920 --> 00:04:05.110 align:start position:0%
so we can just start right away
by<00:04:02.159><c> providing</c><00:04:02.799><c> an</c><00:04:03.120><c> environment</c><00:04:04.239><c> part</c><00:04:04.799><c> of</c><00:04:04.959><c> the</c>

00:04:05.110 --> 00:04:05.120 align:start position:0%
by providing an environment part of the
 

00:04:05.120 --> 00:04:09.190 align:start position:0%
by providing an environment part of the
file<00:04:05.760><c> and</c><00:04:05.920><c> the</c><00:04:06.080><c> variable</c><00:04:06.560><c> is</c><00:04:06.720><c> called</c><00:04:07.040><c> quadrant</c>

00:04:09.190 --> 00:04:09.200 align:start position:0%
file and the variable is called quadrant
 

00:04:09.200 --> 00:04:12.229 align:start position:0%
file and the variable is called quadrant
double<00:04:09.599><c> underscore</c><00:04:10.560><c> cluster</c>

00:04:12.229 --> 00:04:12.239 align:start position:0%
double underscore cluster
 

00:04:12.239 --> 00:04:15.190 align:start position:0%
double underscore cluster
double<00:04:12.560><c> underscore</c><00:04:13.680><c> enabled</c>

00:04:15.190 --> 00:04:15.200 align:start position:0%
double underscore enabled
 

00:04:15.200 --> 00:04:18.229 align:start position:0%
double underscore enabled
and<00:04:15.439><c> that</c><00:04:15.760><c> has</c><00:04:15.920><c> to</c><00:04:16.079><c> be</c><00:04:16.320><c> set</c>

00:04:18.229 --> 00:04:18.239 align:start position:0%
and that has to be set
 

00:04:18.239 --> 00:04:21.349 align:start position:0%
and that has to be set
to<00:04:18.479><c> true</c>

00:04:21.349 --> 00:04:21.359 align:start position:0%
 
 

00:04:21.359 --> 00:04:24.070 align:start position:0%
 
okay<00:04:21.919><c> but</c><00:04:22.160><c> the</c><00:04:22.320><c> same</c><00:04:22.560><c> has</c><00:04:22.800><c> to</c><00:04:22.960><c> be</c><00:04:23.120><c> done</c><00:04:23.680><c> for</c><00:04:23.919><c> the</c>

00:04:24.070 --> 00:04:24.080 align:start position:0%
okay but the same has to be done for the
 

00:04:24.080 --> 00:04:25.430 align:start position:0%
okay but the same has to be done for the
other<00:04:24.400><c> instance</c>

00:04:25.430 --> 00:04:25.440 align:start position:0%
other instance
 

00:04:25.440 --> 00:04:29.270 align:start position:0%
other instance
so<00:04:25.840><c> let</c><00:04:26.080><c> me</c><00:04:26.160><c> just</c><00:04:26.400><c> copy</c><00:04:26.840><c> it</c><00:04:28.240><c> and</c><00:04:28.479><c> right</c><00:04:28.800><c> now</c>

00:04:29.270 --> 00:04:29.280 align:start position:0%
so let me just copy it and right now
 

00:04:29.280 --> 00:04:31.030 align:start position:0%
so let me just copy it and right now
they<00:04:29.520><c> are</c><00:04:30.000><c> both</c>

00:04:31.030 --> 00:04:31.040 align:start position:0%
they are both
 

00:04:31.040 --> 00:04:32.950 align:start position:0%
they are both
aware<00:04:31.520><c> of</c><00:04:31.680><c> the</c><00:04:31.840><c> fact</c><00:04:32.080><c> that</c><00:04:32.320><c> they</c><00:04:32.560><c> will</c><00:04:32.800><c> be</c>

00:04:32.950 --> 00:04:32.960 align:start position:0%
aware of the fact that they will be
 

00:04:32.960 --> 00:04:34.550 align:start position:0%
aware of the fact that they will be
launched<00:04:33.440><c> in</c><00:04:33.600><c> a</c>

00:04:34.550 --> 00:04:34.560 align:start position:0%
launched in a
 

00:04:34.560 --> 00:04:36.310 align:start position:0%
launched in a
distributed<00:04:35.280><c> mode</c>

00:04:36.310 --> 00:04:36.320 align:start position:0%
distributed mode
 

00:04:36.320 --> 00:04:38.070 align:start position:0%
distributed mode
but<00:04:36.560><c> still</c><00:04:36.960><c> they</c><00:04:37.199><c> don't</c><00:04:37.440><c> know</c><00:04:37.759><c> how</c><00:04:37.919><c> to</c>

00:04:38.070 --> 00:04:38.080 align:start position:0%
but still they don't know how to
 

00:04:38.080 --> 00:04:40.070 align:start position:0%
but still they don't know how to
communicate<00:04:38.639><c> with</c><00:04:38.880><c> each</c><00:04:39.040><c> other</c>

00:04:40.070 --> 00:04:40.080 align:start position:0%
communicate with each other
 

00:04:40.080 --> 00:04:42.710 align:start position:0%
communicate with each other
and<00:04:40.240><c> we</c><00:04:40.400><c> need</c><00:04:40.639><c> to</c><00:04:40.800><c> provide</c><00:04:41.280><c> a</c><00:04:41.360><c> way</c><00:04:41.759><c> of</c><00:04:42.320><c> letting</c>

00:04:42.710 --> 00:04:42.720 align:start position:0%
and we need to provide a way of letting
 

00:04:42.720 --> 00:04:44.310 align:start position:0%
and we need to provide a way of letting
them<00:04:42.960><c> know</c><00:04:43.280><c> that</c><00:04:43.440><c> they</c><00:04:43.680><c> should</c><00:04:43.840><c> be</c><00:04:44.000><c> working</c>

00:04:44.310 --> 00:04:44.320 align:start position:0%
them know that they should be working
 

00:04:44.320 --> 00:04:46.950 align:start position:0%
them know that they should be working
together<00:04:45.280><c> and</c><00:04:45.520><c> quadrant</c><00:04:46.160><c> does</c><00:04:46.400><c> it</c><00:04:46.639><c> by</c>

00:04:46.950 --> 00:04:46.960 align:start position:0%
together and quadrant does it by
 

00:04:46.960 --> 00:04:49.830 align:start position:0%
together and quadrant does it by
providing<00:04:47.840><c> some</c><00:04:48.240><c> attributes</c><00:04:48.880><c> to</c><00:04:49.040><c> the</c><00:04:49.360><c> default</c>

00:04:49.830 --> 00:04:49.840 align:start position:0%
providing some attributes to the default
 

00:04:49.840 --> 00:04:52.150 align:start position:0%
providing some attributes to the default
executable<00:04:50.639><c> file</c>

00:04:52.150 --> 00:04:52.160 align:start position:0%
executable file
 

00:04:52.160 --> 00:04:54.790 align:start position:0%
executable file
we<00:04:52.320><c> can</c><00:04:52.560><c> override</c><00:04:53.280><c> the</c><00:04:53.600><c> default</c><00:04:54.000><c> behavior</c><00:04:54.560><c> by</c>

00:04:54.790 --> 00:04:54.800 align:start position:0%
we can override the default behavior by
 

00:04:54.800 --> 00:05:00.070 align:start position:0%
we can override the default behavior by
providing<00:04:55.360><c> a</c><00:04:55.520><c> comment</c>

00:05:00.070 --> 00:05:00.080 align:start position:0%
 
 

00:05:00.080 --> 00:05:01.909 align:start position:0%
 
and<00:05:00.639><c> by</c><00:05:00.800><c> default</c>

00:05:01.909 --> 00:05:01.919 align:start position:0%
and by default
 

00:05:01.919 --> 00:05:04.070 align:start position:0%
and by default
it<00:05:02.080><c> will</c><00:05:02.320><c> just</c>

00:05:04.070 --> 00:05:04.080 align:start position:0%
it will just
 

00:05:04.080 --> 00:05:05.189 align:start position:0%
it will just
execute

00:05:05.189 --> 00:05:05.199 align:start position:0%
execute
 

00:05:05.199 --> 00:05:07.270 align:start position:0%
execute
quadrant<00:05:05.840><c> binary</c>

00:05:07.270 --> 00:05:07.280 align:start position:0%
quadrant binary
 

00:05:07.280 --> 00:05:09.990 align:start position:0%
quadrant binary
but<00:05:07.440><c> right</c><00:05:07.600><c> now</c><00:05:07.759><c> we</c><00:05:07.919><c> need</c><00:05:08.080><c> to</c><00:05:08.320><c> provide</c><00:05:09.039><c> an</c><00:05:09.199><c> url</c>

00:05:09.990 --> 00:05:10.000 align:start position:0%
but right now we need to provide an url
 

00:05:10.000 --> 00:05:13.510 align:start position:0%
but right now we need to provide an url
to<00:05:10.160><c> this</c><00:05:10.479><c> first</c><00:05:10.880><c> instance</c><00:05:11.680><c> i</c><00:05:12.080><c> called</c><00:05:12.639><c> those</c>

00:05:13.510 --> 00:05:13.520 align:start position:0%
to this first instance i called those
 

00:05:13.520 --> 00:05:15.909 align:start position:0%
to this first instance i called those
containers<00:05:14.160><c> primary</c><00:05:14.800><c> and</c><00:05:14.960><c> secondary</c><00:05:15.680><c> but</c>

00:05:15.909 --> 00:05:15.919 align:start position:0%
containers primary and secondary but
 

00:05:15.919 --> 00:05:18.070 align:start position:0%
containers primary and secondary but
there<00:05:16.080><c> is</c><00:05:16.320><c> no</c><00:05:16.560><c> master</c><00:05:17.120><c> slave</c><00:05:17.680><c> like</c>

00:05:18.070 --> 00:05:18.080 align:start position:0%
there is no master slave like
 

00:05:18.080 --> 00:05:20.550 align:start position:0%
there is no master slave like
communication<00:05:18.800><c> between</c><00:05:19.280><c> those</c><00:05:19.680><c> two</c>

00:05:20.550 --> 00:05:20.560 align:start position:0%
communication between those two
 

00:05:20.560 --> 00:05:22.710 align:start position:0%
communication between those two
they<00:05:20.800><c> are</c><00:05:20.960><c> going</c><00:05:21.199><c> to</c><00:05:21.440><c> choose</c><00:05:21.759><c> the</c><00:05:21.919><c> leader</c>

00:05:22.710 --> 00:05:22.720 align:start position:0%
they are going to choose the leader
 

00:05:22.720 --> 00:05:24.790 align:start position:0%
they are going to choose the leader
internally<00:05:23.360><c> on</c><00:05:23.440><c> their</c><00:05:23.680><c> own</c>

00:05:24.790 --> 00:05:24.800 align:start position:0%
internally on their own
 

00:05:24.800 --> 00:05:27.749 align:start position:0%
internally on their own
but<00:05:25.360><c> since</c><00:05:25.759><c> this</c><00:05:26.240><c> first</c><00:05:26.639><c> instance</c><00:05:27.199><c> has</c><00:05:27.440><c> a</c>

00:05:27.749 --> 00:05:27.759 align:start position:0%
but since this first instance has a
 

00:05:27.759 --> 00:05:30.390 align:start position:0%
but since this first instance has a
specific<00:05:28.560><c> way</c><00:05:28.800><c> of</c><00:05:28.960><c> launching</c><00:05:29.440><c> that</c>

00:05:30.390 --> 00:05:30.400 align:start position:0%
specific way of launching that
 

00:05:30.400 --> 00:05:31.830 align:start position:0%
specific way of launching that
it<00:05:30.560><c> has</c><00:05:30.800><c> to</c><00:05:30.960><c> be</c>

00:05:31.830 --> 00:05:31.840 align:start position:0%
it has to be
 

00:05:31.840 --> 00:05:34.310 align:start position:0%
it has to be
executed<00:05:32.880><c> first</c><00:05:33.360><c> and</c><00:05:33.520><c> then</c><00:05:33.759><c> we</c><00:05:33.840><c> can</c><00:05:34.000><c> just</c>

00:05:34.310 --> 00:05:34.320 align:start position:0%
executed first and then we can just
 

00:05:34.320 --> 00:05:37.590 align:start position:0%
executed first and then we can just
create<00:05:35.039><c> as</c><00:05:35.280><c> many</c><00:05:35.840><c> secondary</c><00:05:36.479><c> instances</c><00:05:37.280><c> as</c>

00:05:37.590 --> 00:05:37.600 align:start position:0%
create as many secondary instances as
 

00:05:37.600 --> 00:05:39.270 align:start position:0%
create as many secondary instances as
you<00:05:37.759><c> want</c><00:05:38.000><c> to</c>

00:05:39.270 --> 00:05:39.280 align:start position:0%
you want to
 

00:05:39.280 --> 00:05:41.990 align:start position:0%
you want to
so<00:05:39.440><c> this</c><00:05:39.680><c> first</c><00:05:40.000><c> instance</c><00:05:40.639><c> is</c><00:05:41.280><c> in</c><00:05:41.520><c> some</c><00:05:41.759><c> way</c>

00:05:41.990 --> 00:05:42.000 align:start position:0%
so this first instance is in some way
 

00:05:42.000 --> 00:05:44.550 align:start position:0%
so this first instance is in some way
special<00:05:42.800><c> and</c><00:05:43.039><c> we</c><00:05:43.120><c> need</c><00:05:43.280><c> to</c><00:05:43.520><c> provide</c><00:05:43.919><c> the</c><00:05:44.080><c> url</c>

00:05:44.550 --> 00:05:44.560 align:start position:0%
special and we need to provide the url
 

00:05:44.560 --> 00:05:47.270 align:start position:0%
special and we need to provide the url
to<00:05:44.800><c> itself</c>

00:05:47.270 --> 00:05:47.280 align:start position:0%
to itself
 

00:05:47.280 --> 00:05:49.510 align:start position:0%
to itself
since<00:05:47.600><c> we</c><00:05:47.759><c> are</c><00:05:47.919><c> using</c><00:05:48.160><c> docker</c><00:05:48.479><c> compose</c><00:05:49.280><c> we</c>

00:05:49.510 --> 00:05:49.520 align:start position:0%
since we are using docker compose we
 

00:05:49.520 --> 00:05:51.189 align:start position:0%
since we are using docker compose we
already<00:05:49.840><c> know</c><00:05:50.080><c> the</c><00:05:50.240><c> hostname</c><00:05:50.880><c> of</c><00:05:51.039><c> this</c>

00:05:51.189 --> 00:05:51.199 align:start position:0%
already know the hostname of this
 

00:05:51.199 --> 00:05:53.749 align:start position:0%
already know the hostname of this
particular<00:05:51.759><c> container</c><00:05:52.720><c> and</c><00:05:52.880><c> the</c><00:05:53.120><c> protocol</c>

00:05:53.749 --> 00:05:53.759 align:start position:0%
particular container and the protocol
 

00:05:53.759 --> 00:05:55.590 align:start position:0%
particular container and the protocol
that<00:05:54.000><c> they</c><00:05:54.320><c> use</c><00:05:54.479><c> to</c><00:05:54.639><c> communicate</c><00:05:55.199><c> with</c><00:05:55.440><c> each</c>

00:05:55.590 --> 00:05:55.600 align:start position:0%
that they use to communicate with each
 

00:05:55.600 --> 00:05:59.270 align:start position:0%
that they use to communicate with each
other<00:05:55.919><c> is</c><00:05:56.080><c> http</c><00:05:57.120><c> so</c><00:05:57.440><c> it</c><00:05:57.600><c> has</c><00:05:57.840><c> to</c><00:05:58.000><c> be</c><00:05:58.240><c> set</c><00:05:58.479><c> up</c>

00:05:59.270 --> 00:05:59.280 align:start position:0%
other is http so it has to be set up
 

00:05:59.280 --> 00:06:01.830 align:start position:0%
other is http so it has to be set up
to<00:05:59.600><c> quadrant</c><00:06:00.240><c> primary</c>

00:06:01.830 --> 00:06:01.840 align:start position:0%
to quadrant primary
 

00:06:01.840 --> 00:06:03.590 align:start position:0%
to quadrant primary
with<00:06:02.080><c> the</c><00:06:02.319><c> port</c>

00:06:03.590 --> 00:06:03.600 align:start position:0%
with the port
 

00:06:03.600 --> 00:06:07.189 align:start position:0%
with the port
which<00:06:03.840><c> is</c><00:06:04.319><c> 6</c><00:06:04.800><c> 3</c><00:06:05.120><c> 3</c><00:06:05.440><c> 5</c><00:06:06.240><c> which</c><00:06:06.479><c> is</c><00:06:06.560><c> the</c><00:06:06.720><c> default</c>

00:06:07.189 --> 00:06:07.199 align:start position:0%
which is 6 3 3 5 which is the default
 

00:06:07.199 --> 00:06:08.230 align:start position:0%
which is 6 3 3 5 which is the default
part

00:06:08.230 --> 00:06:08.240 align:start position:0%
part
 

00:06:08.240 --> 00:06:11.110 align:start position:0%
part
of<00:06:08.800><c> communication</c><00:06:09.600><c> between</c><00:06:10.160><c> all</c><00:06:10.319><c> the</c>

00:06:11.110 --> 00:06:11.120 align:start position:0%
of communication between all the
 

00:06:11.120 --> 00:06:12.950 align:start position:0%
of communication between all the
instances<00:06:11.840><c> of</c><00:06:11.919><c> quadrant</c><00:06:12.479><c> running</c><00:06:12.800><c> in</c><00:06:12.880><c> the</c>

00:06:12.950 --> 00:06:12.960 align:start position:0%
instances of quadrant running in the
 

00:06:12.960 --> 00:06:14.309 align:start position:0%
instances of quadrant running in the
same<00:06:13.199><c> cluster</c>

00:06:14.309 --> 00:06:14.319 align:start position:0%
same cluster
 

00:06:14.319 --> 00:06:16.950 align:start position:0%
same cluster
and<00:06:14.479><c> this</c><00:06:14.720><c> is</c><00:06:14.880><c> it</c><00:06:15.440><c> at</c><00:06:15.600><c> least</c><00:06:15.919><c> for</c><00:06:16.160><c> the</c><00:06:16.400><c> primary</c>

00:06:16.950 --> 00:06:16.960 align:start position:0%
and this is it at least for the primary
 

00:06:16.960 --> 00:06:18.710 align:start position:0%
and this is it at least for the primary
instance

00:06:18.710 --> 00:06:18.720 align:start position:0%
instance
 

00:06:18.720 --> 00:06:21.670 align:start position:0%
instance
but<00:06:19.199><c> still</c><00:06:19.520><c> the</c><00:06:19.680><c> secondary</c><00:06:20.240><c> one</c><00:06:20.720><c> doesn't</c><00:06:21.120><c> know</c>

00:06:21.670 --> 00:06:21.680 align:start position:0%
but still the secondary one doesn't know
 

00:06:21.680 --> 00:06:24.390 align:start position:0%
but still the secondary one doesn't know
how<00:06:21.919><c> to</c><00:06:22.319><c> reach</c><00:06:22.639><c> the</c><00:06:22.800><c> first</c><00:06:23.120><c> one</c><00:06:23.520><c> and</c><00:06:23.759><c> we</c><00:06:24.000><c> also</c>

00:06:24.390 --> 00:06:24.400 align:start position:0%
how to reach the first one and we also
 

00:06:24.400 --> 00:06:27.189 align:start position:0%
how to reach the first one and we also
need<00:06:24.639><c> to</c><00:06:24.880><c> change</c><00:06:25.280><c> the</c><00:06:25.440><c> default</c><00:06:26.000><c> parameters</c><00:06:27.039><c> of</c>

00:06:27.189 --> 00:06:27.199 align:start position:0%
need to change the default parameters of
 

00:06:27.199 --> 00:06:30.309 align:start position:0%
need to change the default parameters of
the<00:06:27.440><c> quadrant</c><00:06:28.400><c> executable</c><00:06:29.199><c> file</c>

00:06:30.309 --> 00:06:30.319 align:start position:0%
the quadrant executable file
 

00:06:30.319 --> 00:06:32.550 align:start position:0%
the quadrant executable file
and<00:06:30.479><c> this</c><00:06:30.720><c> is</c><00:06:30.880><c> done</c><00:06:31.199><c> in</c><00:06:31.440><c> a</c><00:06:31.520><c> similar</c><00:06:32.080><c> way</c><00:06:32.319><c> but</c>

00:06:32.550 --> 00:06:32.560 align:start position:0%
and this is done in a similar way but
 

00:06:32.560 --> 00:06:35.430 align:start position:0%
and this is done in a similar way but
instead<00:06:32.960><c> of</c><00:06:33.039><c> url</c><00:06:33.600><c> we</c><00:06:33.759><c> provide</c><00:06:34.720><c> bootstrap</c>

00:06:35.430 --> 00:06:35.440 align:start position:0%
instead of url we provide bootstrap
 

00:06:35.440 --> 00:06:37.029 align:start position:0%
instead of url we provide bootstrap
parameter

00:06:37.029 --> 00:06:37.039 align:start position:0%
parameter
 

00:06:37.039 --> 00:06:39.749 align:start position:0%
parameter
and<00:06:37.199><c> this</c><00:06:37.440><c> is</c><00:06:37.600><c> it</c><00:06:38.000><c> at</c><00:06:38.240><c> least</c><00:06:38.479><c> the</c><00:06:38.560><c> basic</c><00:06:38.960><c> step</c>

00:06:39.749 --> 00:06:39.759 align:start position:0%
and this is it at least the basic step
 

00:06:39.759 --> 00:06:42.950 align:start position:0%
and this is it at least the basic step
we<00:06:40.080><c> are</c><00:06:40.400><c> right</c><00:06:40.720><c> now</c><00:06:41.039><c> able</c><00:06:41.440><c> to</c><00:06:41.840><c> run</c><00:06:42.160><c> the</c>

00:06:42.950 --> 00:06:42.960 align:start position:0%
we are right now able to run the
 

00:06:42.960 --> 00:06:46.150 align:start position:0%
we are right now able to run the
primary<00:06:43.520><c> and</c><00:06:43.680><c> secondary</c><00:06:44.319><c> instances</c><00:06:45.360><c> and</c><00:06:45.680><c> they</c>

00:06:46.150 --> 00:06:46.160 align:start position:0%
primary and secondary instances and they
 

00:06:46.160 --> 00:06:48.150 align:start position:0%
primary and secondary instances and they
should<00:06:46.400><c> be</c><00:06:46.639><c> already</c><00:06:47.039><c> able</c><00:06:47.360><c> to</c><00:06:47.520><c> communicate</c>

00:06:48.150 --> 00:06:48.160 align:start position:0%
should be already able to communicate
 

00:06:48.160 --> 00:06:49.589 align:start position:0%
should be already able to communicate
with<00:06:48.319><c> each</c><00:06:48.479><c> other</c>

00:06:49.589 --> 00:06:49.599 align:start position:0%
with each other
 

00:06:49.599 --> 00:06:52.230 align:start position:0%
with each other
so<00:06:50.000><c> let's</c><00:06:50.160><c> just</c><00:06:50.400><c> do</c><00:06:50.560><c> it</c><00:06:50.639><c> step</c><00:06:51.120><c> by</c><00:06:51.280><c> step</c>

00:06:52.230 --> 00:06:52.240 align:start position:0%
so let's just do it step by step
 

00:06:52.240 --> 00:06:53.830 align:start position:0%
so let's just do it step by step
first<00:06:52.560><c> of</c><00:06:52.720><c> all</c><00:06:52.960><c> i</c><00:06:53.039><c> will</c><00:06:53.199><c> just</c><00:06:53.440><c> open</c><00:06:53.680><c> the</c>

00:06:53.830 --> 00:06:53.840 align:start position:0%
first of all i will just open the
 

00:06:53.840 --> 00:06:55.350 align:start position:0%
first of all i will just open the
terminal<00:06:54.319><c> window</c>

00:06:55.350 --> 00:06:55.360 align:start position:0%
terminal window
 

00:06:55.360 --> 00:06:56.150 align:start position:0%
terminal window
and

00:06:56.150 --> 00:06:56.160 align:start position:0%
and
 

00:06:56.160 --> 00:06:57.909 align:start position:0%
and
create

00:06:57.909 --> 00:06:57.919 align:start position:0%
create
 

00:06:57.919 --> 00:07:00.070 align:start position:0%
create
quadrant<00:06:58.639><c> primary</c>

00:07:00.070 --> 00:07:00.080 align:start position:0%
quadrant primary
 

00:07:00.080 --> 00:07:04.550 align:start position:0%
quadrant primary
instance

00:07:04.550 --> 00:07:04.560 align:start position:0%
 
 

00:07:04.560 --> 00:07:06.870 align:start position:0%
 
okay<00:07:04.880><c> it</c><00:07:05.039><c> has</c><00:07:05.440><c> already</c><00:07:05.759><c> started</c><00:07:06.400><c> this</c><00:07:06.639><c> is</c>

00:07:06.870 --> 00:07:06.880 align:start position:0%
okay it has already started this is
 

00:07:06.880 --> 00:07:10.550 align:start position:0%
okay it has already started this is
fairly<00:07:07.360><c> quick</c>

00:07:10.550 --> 00:07:10.560 align:start position:0%
 
 

00:07:10.560 --> 00:07:12.710 align:start position:0%
 
right<00:07:10.800><c> now</c><00:07:11.199><c> i</c><00:07:11.360><c> can</c><00:07:11.520><c> just</c><00:07:11.759><c> create</c><00:07:12.160><c> another</c>

00:07:12.710 --> 00:07:12.720 align:start position:0%
right now i can just create another
 

00:07:12.720 --> 00:07:15.110 align:start position:0%
right now i can just create another
instance<00:07:13.759><c> of</c><00:07:14.000><c> quadrant</c><00:07:14.479><c> this</c><00:07:14.720><c> time</c><00:07:14.960><c> of</c>

00:07:15.110 --> 00:07:15.120 align:start position:0%
instance of quadrant this time of
 

00:07:15.120 --> 00:07:16.950 align:start position:0%
instance of quadrant this time of
quadrant<00:07:15.680><c> secondary</c>

00:07:16.950 --> 00:07:16.960 align:start position:0%
quadrant secondary
 

00:07:16.960 --> 00:07:17.670 align:start position:0%
quadrant secondary
so

00:07:17.670 --> 00:07:17.680 align:start position:0%
so
 

00:07:17.680 --> 00:07:20.469 align:start position:0%
so
by<00:07:17.919><c> using</c><00:07:18.240><c> docker</c><00:07:18.639><c> compose</c><00:07:19.360><c> app</c>

00:07:20.469 --> 00:07:20.479 align:start position:0%
by using docker compose app
 

00:07:20.479 --> 00:07:23.270 align:start position:0%
by using docker compose app
i<00:07:20.720><c> can</c>

00:07:23.270 --> 00:07:23.280 align:start position:0%
 
 

00:07:23.280 --> 00:07:29.430 align:start position:0%
 
run<00:07:23.840><c> the</c><00:07:24.080><c> second</c><00:07:24.479><c> container</c>

00:07:29.430 --> 00:07:29.440 align:start position:0%
 
 

00:07:29.440 --> 00:07:30.950 align:start position:0%
 
and<00:07:29.599><c> this</c><00:07:29.840><c> is</c><00:07:30.000><c> it</c>

00:07:30.950 --> 00:07:30.960 align:start position:0%
and this is it
 

00:07:30.960 --> 00:07:32.870 align:start position:0%
and this is it
internally

00:07:32.870 --> 00:07:32.880 align:start position:0%
internally
 

00:07:32.880 --> 00:07:35.430 align:start position:0%
internally
they<00:07:33.520><c> were</c><00:07:33.759><c> already</c><00:07:34.400><c> communicating</c><00:07:35.120><c> to</c><00:07:35.280><c> each</c>

00:07:35.430 --> 00:07:35.440 align:start position:0%
they were already communicating to each
 

00:07:35.440 --> 00:07:38.950 align:start position:0%
they were already communicating to each
other<00:07:35.919><c> and</c><00:07:36.319><c> have</c><00:07:36.720><c> chosen</c><00:07:37.199><c> the</c><00:07:37.360><c> leader</c>

00:07:38.950 --> 00:07:38.960 align:start position:0%
other and have chosen the leader
 

00:07:38.960 --> 00:07:40.469 align:start position:0%
other and have chosen the leader
right<00:07:39.199><c> now</c><00:07:39.520><c> we</c><00:07:39.680><c> could</c>

00:07:40.469 --> 00:07:40.479 align:start position:0%
right now we could
 

00:07:40.479 --> 00:07:43.029 align:start position:0%
right now we could
let's<00:07:40.800><c> say</c><00:07:41.360><c> check</c><00:07:41.919><c> the</c><00:07:42.080><c> status</c><00:07:42.639><c> of</c><00:07:42.800><c> our</c>

00:07:43.029 --> 00:07:43.039 align:start position:0%
let's say check the status of our
 

00:07:43.039 --> 00:07:44.390 align:start position:0%
let's say check the status of our
cluster

00:07:44.390 --> 00:07:44.400 align:start position:0%
cluster
 

00:07:44.400 --> 00:07:46.629 align:start position:0%
cluster
remember<00:07:44.800><c> that</c><00:07:44.960><c> we</c><00:07:45.199><c> exposed</c><00:07:45.759><c> this</c><00:07:46.080><c> port</c><00:07:46.479><c> of</c>

00:07:46.629 --> 00:07:46.639 align:start position:0%
remember that we exposed this port of
 

00:07:46.639 --> 00:07:48.070 align:start position:0%
remember that we exposed this port of
the<00:07:46.960><c> first</c>

00:07:48.070 --> 00:07:48.080 align:start position:0%
the first
 

00:07:48.080 --> 00:07:49.110 align:start position:0%
the first
instance

00:07:49.110 --> 00:07:49.120 align:start position:0%
instance
 

00:07:49.120 --> 00:07:52.309 align:start position:0%
instance
so<00:07:49.520><c> right</c><00:07:49.759><c> now</c><00:07:50.240><c> just</c><00:07:50.479><c> by</c><00:07:50.639><c> using</c><00:07:51.120><c> curl</c><00:07:51.919><c> i</c><00:07:52.080><c> should</c>

00:07:52.309 --> 00:07:52.319 align:start position:0%
so right now just by using curl i should
 

00:07:52.319 --> 00:07:54.390 align:start position:0%
so right now just by using curl i should
be<00:07:52.560><c> able</c><00:07:52.879><c> to</c><00:07:53.280><c> check</c><00:07:53.520><c> the</c><00:07:53.680><c> status</c><00:07:54.160><c> of</c><00:07:54.240><c> the</c>

00:07:54.390 --> 00:07:54.400 align:start position:0%
be able to check the status of the
 

00:07:54.400 --> 00:07:56.390 align:start position:0%
be able to check the status of the
cluster

00:07:56.390 --> 00:07:56.400 align:start position:0%
cluster
 

00:07:56.400 --> 00:07:59.510 align:start position:0%
cluster
it<00:07:56.560><c> is</c><00:07:56.800><c> mapped</c><00:07:57.120><c> to</c><00:07:57.360><c> localhost</c>

00:07:59.510 --> 00:07:59.520 align:start position:0%
it is mapped to localhost
 

00:07:59.520 --> 00:08:01.749 align:start position:0%
it is mapped to localhost
and<00:07:59.759><c> there</c><00:07:59.919><c> is</c><00:08:00.160><c> a</c><00:08:00.560><c> brand</c><00:08:00.960><c> new</c><00:08:01.199><c> cluster</c>

00:08:01.749 --> 00:08:01.759 align:start position:0%
and there is a brand new cluster
 

00:08:01.759 --> 00:08:04.629 align:start position:0%
and there is a brand new cluster
endpoint<00:08:02.560><c> exposing</c><00:08:03.120><c> some</c><00:08:03.360><c> details</c>

00:08:04.629 --> 00:08:04.639 align:start position:0%
endpoint exposing some details
 

00:08:04.639 --> 00:08:06.150 align:start position:0%
endpoint exposing some details
as<00:08:04.879><c> you</c><00:08:04.960><c> may</c><00:08:05.199><c> see</c>

00:08:06.150 --> 00:08:06.160 align:start position:0%
as you may see
 

00:08:06.160 --> 00:08:09.189 align:start position:0%
as you may see
we<00:08:06.400><c> have</c><00:08:06.960><c> all</c><00:08:07.199><c> our</c><00:08:07.520><c> peers</c><00:08:07.919><c> listed</c>

00:08:09.189 --> 00:08:09.199 align:start position:0%
we have all our peers listed
 

00:08:09.199 --> 00:08:12.790 align:start position:0%
we have all our peers listed
and<00:08:09.520><c> the</c><00:08:09.680><c> leader</c><00:08:10.080><c> has</c><00:08:10.240><c> been</c><00:08:10.840><c> selected</c><00:08:12.000><c> but</c>

00:08:12.790 --> 00:08:12.800 align:start position:0%
and the leader has been selected but
 

00:08:12.800 --> 00:08:15.589 align:start position:0%
and the leader has been selected but
if<00:08:12.960><c> i</c><00:08:13.120><c> wanted</c><00:08:13.440><c> to</c><00:08:13.520><c> have</c><00:08:13.840><c> another</c><00:08:14.400><c> instance</c><00:08:15.039><c> of</c>

00:08:15.589 --> 00:08:15.599 align:start position:0%
if i wanted to have another instance of
 

00:08:15.599 --> 00:08:17.430 align:start position:0%
if i wanted to have another instance of
quadrant<00:08:16.160><c> secondary</c>

00:08:17.430 --> 00:08:17.440 align:start position:0%
quadrant secondary
 

00:08:17.440 --> 00:08:19.589 align:start position:0%
quadrant secondary
there<00:08:17.599><c> is</c><00:08:17.759><c> nothing</c><00:08:18.240><c> easier</c><00:08:18.560><c> than</c><00:08:18.800><c> that</c><00:08:19.199><c> i</c><00:08:19.440><c> can</c>

00:08:19.589 --> 00:08:19.599 align:start position:0%
there is nothing easier than that i can
 

00:08:19.599 --> 00:08:22.390 align:start position:0%
there is nothing easier than that i can
just<00:08:20.080><c> use</c><00:08:20.319><c> docker</c><00:08:20.639><c> compose</c><00:08:21.360><c> up</c>

00:08:22.390 --> 00:08:22.400 align:start position:0%
just use docker compose up
 

00:08:22.400 --> 00:08:24.869 align:start position:0%
just use docker compose up
this<00:08:22.639><c> time</c><00:08:23.120><c> scale</c><00:08:23.520><c> it</c>

00:08:24.869 --> 00:08:24.879 align:start position:0%
this time scale it
 

00:08:24.879 --> 00:08:28.550 align:start position:0%
this time scale it
to<00:08:25.360><c> two</c><00:08:25.680><c> instances</c><00:08:26.639><c> of</c><00:08:26.879><c> quadrant</c>

00:08:28.550 --> 00:08:28.560 align:start position:0%
to two instances of quadrant
 

00:08:28.560 --> 00:08:32.630 align:start position:0%
to two instances of quadrant
secondary

00:08:32.630 --> 00:08:32.640 align:start position:0%
 
 

00:08:32.640 --> 00:08:33.990 align:start position:0%
 
and<00:08:32.800><c> right</c><00:08:33.039><c> now</c>

00:08:33.990 --> 00:08:34.000 align:start position:0%
and right now
 

00:08:34.000 --> 00:08:36.310 align:start position:0%
and right now
i<00:08:34.159><c> should</c><00:08:34.399><c> have</c><00:08:34.800><c> three</c><00:08:35.120><c> different</c><00:08:35.519><c> instances</c>

00:08:36.310 --> 00:08:36.320 align:start position:0%
i should have three different instances
 

00:08:36.320 --> 00:08:40.230 align:start position:0%
i should have three different instances
running<00:08:36.800><c> within</c><00:08:37.120><c> the</c><00:08:37.279><c> same</c><00:08:37.599><c> cluster</c>

00:08:40.230 --> 00:08:40.240 align:start position:0%
running within the same cluster
 

00:08:40.240 --> 00:08:41.029 align:start position:0%
running within the same cluster
so

00:08:41.029 --> 00:08:41.039 align:start position:0%
so
 

00:08:41.039 --> 00:08:43.350 align:start position:0%
so
let's<00:08:41.279><c> just</c><00:08:41.519><c> use</c><00:08:41.839><c> docker</c><00:08:42.240><c> stats</c><00:08:42.640><c> in</c><00:08:42.800><c> order</c><00:08:43.120><c> to</c>

00:08:43.350 --> 00:08:43.360 align:start position:0%
let's just use docker stats in order to
 

00:08:43.360 --> 00:08:46.230 align:start position:0%
let's just use docker stats in order to
see<00:08:43.680><c> whether</c><00:08:44.080><c> they</c><00:08:44.320><c> are</c><00:08:44.480><c> really</c><00:08:44.880><c> running</c>

00:08:46.230 --> 00:08:46.240 align:start position:0%
see whether they are really running
 

00:08:46.240 --> 00:08:52.470 align:start position:0%
see whether they are really running
at<00:08:46.399><c> the</c><00:08:46.560><c> same</c><00:08:46.800><c> time</c>

00:08:52.470 --> 00:08:52.480 align:start position:0%
 
 

00:08:52.480 --> 00:08:54.710 align:start position:0%
 
okay<00:08:53.200><c> we</c><00:08:53.360><c> have</c><00:08:53.600><c> our</c><00:08:53.839><c> three</c><00:08:54.080><c> containers</c>

00:08:54.710 --> 00:08:54.720 align:start position:0%
okay we have our three containers
 

00:08:54.720 --> 00:08:55.670 align:start position:0%
okay we have our three containers
running

00:08:55.670 --> 00:08:55.680 align:start position:0%
running
 

00:08:55.680 --> 00:08:58.230 align:start position:0%
running
we<00:08:55.920><c> still</c><00:08:56.160><c> do</c><00:08:56.320><c> not</c><00:08:56.640><c> know</c><00:08:56.959><c> if</c><00:08:57.279><c> they</c><00:08:57.760><c> really</c>

00:08:58.230 --> 00:08:58.240 align:start position:0%
we still do not know if they really
 

00:08:58.240 --> 00:09:00.550 align:start position:0%
we still do not know if they really
operate<00:08:58.800><c> in</c><00:08:58.959><c> a</c><00:08:59.040><c> distributed</c><00:08:59.760><c> mode</c>

00:09:00.550 --> 00:09:00.560 align:start position:0%
operate in a distributed mode
 

00:09:00.560 --> 00:09:02.389 align:start position:0%
operate in a distributed mode
so<00:09:01.040><c> we</c><00:09:01.200><c> should</c><00:09:01.360><c> probably</c><00:09:01.760><c> start</c><00:09:02.160><c> with</c>

00:09:02.389 --> 00:09:02.399 align:start position:0%
so we should probably start with
 

00:09:02.399 --> 00:09:04.150 align:start position:0%
so we should probably start with
creating<00:09:02.880><c> a</c><00:09:02.959><c> collection</c>

00:09:04.150 --> 00:09:04.160 align:start position:0%
creating a collection
 

00:09:04.160 --> 00:09:07.670 align:start position:0%
creating a collection
and<00:09:04.399><c> then</c><00:09:04.800><c> putting</c><00:09:05.279><c> some</c><00:09:05.920><c> points</c><00:09:06.399><c> into</c><00:09:06.640><c> that</c>

00:09:07.670 --> 00:09:07.680 align:start position:0%
and then putting some points into that
 

00:09:07.680 --> 00:09:10.070 align:start position:0%
and then putting some points into that
and<00:09:07.839><c> i</c><00:09:07.920><c> have</c><00:09:08.160><c> already</c><00:09:08.560><c> prepared</c><00:09:09.200><c> some</c>

00:09:10.070 --> 00:09:10.080 align:start position:0%
and i have already prepared some
 

00:09:10.080 --> 00:09:12.150 align:start position:0%
and i have already prepared some
utility<00:09:10.640><c> scripts</c><00:09:11.120><c> for</c><00:09:11.279><c> that</c>

00:09:12.150 --> 00:09:12.160 align:start position:0%
utility scripts for that
 

00:09:12.160 --> 00:09:15.590 align:start position:0%
utility scripts for that
the<00:09:12.320><c> first</c><00:09:12.640><c> one</c><00:09:12.880><c> which</c><00:09:13.120><c> is</c><00:09:13.279><c> create</c><00:09:13.680><c> collection</c>

00:09:15.590 --> 00:09:15.600 align:start position:0%
the first one which is create collection
 

00:09:15.600 --> 00:09:18.949 align:start position:0%
the first one which is create collection
just<00:09:16.160><c> sends</c><00:09:16.720><c> a</c><00:09:17.120><c> put</c><00:09:17.440><c> request</c><00:09:17.920><c> to</c><00:09:18.240><c> our</c>

00:09:18.949 --> 00:09:18.959 align:start position:0%
just sends a put request to our
 

00:09:18.959 --> 00:09:21.430 align:start position:0%
just sends a put request to our
collections<00:09:19.760><c> endpoint</c><00:09:20.720><c> and</c><00:09:21.040><c> create</c><00:09:21.360><c> a</c>

00:09:21.430 --> 00:09:21.440 align:start position:0%
collections endpoint and create a
 

00:09:21.440 --> 00:09:23.670 align:start position:0%
collections endpoint and create a
connection<00:09:21.920><c> with</c><00:09:22.080><c> a</c><00:09:22.160><c> configuration</c><00:09:22.959><c> provided</c>

00:09:23.670 --> 00:09:23.680 align:start position:0%
connection with a configuration provided
 

00:09:23.680 --> 00:09:25.430 align:start position:0%
connection with a configuration provided
as<00:09:23.839><c> a</c><00:09:24.080><c> json</c><00:09:24.560><c> file</c>

00:09:25.430 --> 00:09:25.440 align:start position:0%
as a json file
 

00:09:25.440 --> 00:09:27.509 align:start position:0%
as a json file
and<00:09:25.680><c> it</c><00:09:25.920><c> just</c><00:09:26.160><c> creates</c><00:09:26.560><c> a</c><00:09:26.640><c> connection</c><00:09:27.120><c> with</c>

00:09:27.509 --> 00:09:27.519 align:start position:0%
and it just creates a connection with
 

00:09:27.519 --> 00:09:30.150 align:start position:0%
and it just creates a connection with
given<00:09:27.839><c> configuration</c>

00:09:30.150 --> 00:09:30.160 align:start position:0%
given configuration
 

00:09:30.160 --> 00:09:31.269 align:start position:0%
given configuration
okay

00:09:31.269 --> 00:09:31.279 align:start position:0%
okay
 

00:09:31.279 --> 00:09:37.110 align:start position:0%
okay
let's<00:09:31.519><c> just</c><00:09:32.000><c> open</c><00:09:32.320><c> another</c><00:09:33.040><c> terminal</c><00:09:33.519><c> window</c>

00:09:37.110 --> 00:09:37.120 align:start position:0%
 
 

00:09:37.120 --> 00:09:39.509 align:start position:0%
 
and<00:09:37.360><c> see</c><00:09:37.680><c> whether</c><00:09:38.080><c> our</c><00:09:38.399><c> collection</c><00:09:38.959><c> can</c><00:09:39.279><c> be</c>

00:09:39.509 --> 00:09:39.519 align:start position:0%
and see whether our collection can be
 

00:09:39.519 --> 00:09:47.829 align:start position:0%
and see whether our collection can be
easily<00:09:40.080><c> created</c>

00:09:47.829 --> 00:09:47.839 align:start position:0%
 
 

00:09:47.839 --> 00:09:49.430 align:start position:0%
 
okay

00:09:49.430 --> 00:09:49.440 align:start position:0%
okay
 

00:09:49.440 --> 00:09:52.870 align:start position:0%
okay
yeah<00:09:50.240><c> we</c><00:09:50.399><c> have</c><00:09:50.560><c> just</c><00:09:50.800><c> created</c><00:09:51.200><c> a</c><00:09:51.279><c> collection</c>

00:09:52.870 --> 00:09:52.880 align:start position:0%
yeah we have just created a collection
 

00:09:52.880 --> 00:09:55.030 align:start position:0%
yeah we have just created a collection
and<00:09:53.120><c> right</c><00:09:53.360><c> now</c><00:09:53.839><c> we</c><00:09:54.000><c> can</c><00:09:54.160><c> just</c><00:09:54.480><c> put</c><00:09:54.800><c> some</c>

00:09:55.030 --> 00:09:55.040 align:start position:0%
and right now we can just put some
 

00:09:55.040 --> 00:09:57.030 align:start position:0%
and right now we can just put some
points<00:09:55.600><c> into</c><00:09:55.920><c> it</c>

00:09:57.030 --> 00:09:57.040 align:start position:0%
points into it
 

00:09:57.040 --> 00:09:59.430 align:start position:0%
points into it
there<00:09:57.200><c> is</c><00:09:57.360><c> another</c><00:09:57.760><c> script</c><00:09:58.320><c> created</c><00:09:58.959><c> just</c><00:09:59.279><c> to</c>

00:09:59.430 --> 00:09:59.440 align:start position:0%
there is another script created just to
 

00:09:59.440 --> 00:10:02.790 align:start position:0%
there is another script created just to
upload<00:10:00.080><c> points</c><00:10:00.880><c> from</c><00:10:01.200><c> another</c><00:10:01.760><c> json</c><00:10:02.240><c> file</c>

00:10:02.790 --> 00:10:02.800 align:start position:0%
upload points from another json file
 

00:10:02.800 --> 00:10:06.150 align:start position:0%
upload points from another json file
which<00:10:03.040><c> is</c><00:10:03.920><c> right</c><00:10:04.240><c> here</c>

00:10:06.150 --> 00:10:06.160 align:start position:0%
which is right here
 

00:10:06.160 --> 00:10:08.630 align:start position:0%
which is right here
this<00:10:06.399><c> file</c><00:10:06.720><c> has</c><00:10:07.120><c> about</c><00:10:07.839><c> seven</c><00:10:08.399><c> eight</c>

00:10:08.630 --> 00:10:08.640 align:start position:0%
this file has about seven eight
 

00:10:08.640 --> 00:10:09.829 align:start position:0%
this file has about seven eight
megabytes

00:10:09.829 --> 00:10:09.839 align:start position:0%
megabytes
 

00:10:09.839 --> 00:10:12.630 align:start position:0%
megabytes
and<00:10:10.079><c> has</c><00:10:10.640><c> some</c><00:10:10.880><c> numerical</c><00:10:11.519><c> vectors</c><00:10:12.160><c> along</c>

00:10:12.630 --> 00:10:12.640 align:start position:0%
and has some numerical vectors along
 

00:10:12.640 --> 00:10:14.630 align:start position:0%
and has some numerical vectors along
with<00:10:12.880><c> some</c><00:10:13.200><c> additional</c><00:10:13.760><c> features</c><00:10:14.160><c> but</c><00:10:14.320><c> we</c><00:10:14.480><c> are</c>

00:10:14.630 --> 00:10:14.640 align:start position:0%
with some additional features but we are
 

00:10:14.640 --> 00:10:17.269 align:start position:0%
with some additional features but we are
not<00:10:14.800><c> going</c><00:10:15.040><c> to</c><00:10:15.200><c> be</c><00:10:15.360><c> using</c><00:10:16.079><c> uh</c>

00:10:17.269 --> 00:10:17.279 align:start position:0%
not going to be using uh
 

00:10:17.279 --> 00:10:19.590 align:start position:0%
not going to be using uh
this<00:10:17.600><c> extensive</c><00:10:18.480><c> filtering</c><00:10:18.959><c> mechanism</c><00:10:19.519><c> of</c>

00:10:19.590 --> 00:10:19.600 align:start position:0%
this extensive filtering mechanism of
 

00:10:19.600 --> 00:10:21.829 align:start position:0%
this extensive filtering mechanism of
quadrant<00:10:20.640><c> right</c><00:10:20.959><c> now</c><00:10:21.200><c> we</c><00:10:21.360><c> are</c><00:10:21.600><c> only</c>

00:10:21.829 --> 00:10:21.839 align:start position:0%
quadrant right now we are only
 

00:10:21.839 --> 00:10:24.150 align:start position:0%
quadrant right now we are only
interested<00:10:22.399><c> to</c><00:10:22.560><c> see</c><00:10:23.200><c> how</c><00:10:23.360><c> this</c><00:10:23.600><c> is</c><00:10:23.760><c> going</c><00:10:24.000><c> to</c>

00:10:24.150 --> 00:10:24.160 align:start position:0%
interested to see how this is going to
 

00:10:24.160 --> 00:10:26.389 align:start position:0%
interested to see how this is going to
be<00:10:24.320><c> spread</c><00:10:24.720><c> across</c><00:10:25.120><c> our</c><00:10:25.360><c> cluster</c>

00:10:26.389 --> 00:10:26.399 align:start position:0%
be spread across our cluster
 

00:10:26.399 --> 00:10:30.310 align:start position:0%
be spread across our cluster
so<00:10:27.040><c> let's</c><00:10:27.279><c> just</c><00:10:27.600><c> call</c><00:10:27.920><c> this</c><00:10:28.240><c> another</c><00:10:28.640><c> script</c>

00:10:30.310 --> 00:10:30.320 align:start position:0%
so let's just call this another script
 

00:10:30.320 --> 00:10:35.430 align:start position:0%
so let's just call this another script
and<00:10:30.560><c> upload</c><00:10:31.200><c> all</c><00:10:31.360><c> those</c><00:10:31.760><c> points</c>

00:10:35.430 --> 00:10:35.440 align:start position:0%
 
 

00:10:35.440 --> 00:10:38.230 align:start position:0%
 
and<00:10:35.600><c> see</c><00:10:35.839><c> docker</c><00:10:36.240><c> stats</c><00:10:37.040><c> and</c><00:10:37.279><c> the</c><00:10:37.519><c> memory</c>

00:10:38.230 --> 00:10:38.240 align:start position:0%
and see docker stats and the memory
 

00:10:38.240 --> 00:10:42.550 align:start position:0%
and see docker stats and the memory
usage<00:10:38.800><c> of</c><00:10:39.120><c> all</c><00:10:39.440><c> of</c><00:10:39.600><c> those</c><00:10:40.320><c> containers</c>

00:10:42.550 --> 00:10:42.560 align:start position:0%
usage of all of those containers
 

00:10:42.560 --> 00:10:44.150 align:start position:0%
usage of all of those containers
as<00:10:42.800><c> you</c><00:10:42.880><c> may</c><00:10:43.120><c> see</c>

00:10:44.150 --> 00:10:44.160 align:start position:0%
as you may see
 

00:10:44.160 --> 00:10:46.470 align:start position:0%
as you may see
all<00:10:44.399><c> of</c><00:10:44.560><c> them</c><00:10:45.040><c> have</c><00:10:45.519><c> received</c>

00:10:46.470 --> 00:10:46.480 align:start position:0%
all of them have received
 

00:10:46.480 --> 00:10:49.030 align:start position:0%
all of them have received
some<00:10:47.040><c> of</c><00:10:47.279><c> those</c><00:10:47.680><c> points</c><00:10:48.079><c> that</c><00:10:48.320><c> we</c>

00:10:49.030 --> 00:10:49.040 align:start position:0%
some of those points that we
 

00:10:49.040 --> 00:10:51.590 align:start position:0%
some of those points that we
put<00:10:49.360><c> into</c><00:10:49.680><c> the</c><00:10:49.920><c> api</c>

00:10:51.590 --> 00:10:51.600 align:start position:0%
put into the api
 

00:10:51.600 --> 00:10:53.990 align:start position:0%
put into the api
it's<00:10:52.079><c> not</c><00:10:52.399><c> equally</c><00:10:52.880><c> spread</c><00:10:53.279><c> across</c><00:10:53.680><c> all</c><00:10:53.839><c> the</c>

00:10:53.990 --> 00:10:54.000 align:start position:0%
it's not equally spread across all the
 

00:10:54.000 --> 00:10:54.870 align:start position:0%
it's not equally spread across all the
nodes

00:10:54.870 --> 00:10:54.880 align:start position:0%
nodes
 

00:10:54.880 --> 00:10:55.990 align:start position:0%
nodes
but<00:10:55.120><c> still</c>

00:10:55.990 --> 00:10:56.000 align:start position:0%
but still
 

00:10:56.000 --> 00:10:58.630 align:start position:0%
but still
if<00:10:56.240><c> we</c><00:10:56.320><c> had</c><00:10:56.640><c> just</c><00:10:56.880><c> a</c><00:10:56.959><c> single</c><00:10:57.360><c> instance</c><00:10:58.240><c> it</c><00:10:58.399><c> will</c>

00:10:58.630 --> 00:10:58.640 align:start position:0%
if we had just a single instance it will
 

00:10:58.640 --> 00:11:01.910 align:start position:0%
if we had just a single instance it will
contain<00:10:59.279><c> all</c><00:10:59.519><c> the</c><00:10:59.760><c> points</c><00:11:00.480><c> sent</c><00:11:00.800><c> to</c><00:11:01.040><c> it</c><00:11:01.680><c> right</c>

00:11:01.910 --> 00:11:01.920 align:start position:0%
contain all the points sent to it right
 

00:11:01.920 --> 00:11:02.710 align:start position:0%
contain all the points sent to it right
now

00:11:02.710 --> 00:11:02.720 align:start position:0%
now
 

00:11:02.720 --> 00:11:05.110 align:start position:0%
now
it<00:11:02.959><c> is</c><00:11:03.120><c> just</c><00:11:03.360><c> spread</c><00:11:03.839><c> across</c><00:11:04.399><c> all</c><00:11:04.640><c> of</c><00:11:04.800><c> all</c><00:11:04.959><c> the</c>

00:11:05.110 --> 00:11:05.120 align:start position:0%
it is just spread across all of all the
 

00:11:05.120 --> 00:11:06.389 align:start position:0%
it is just spread across all of all the
three<00:11:05.360><c> ones</c>

00:11:06.389 --> 00:11:06.399 align:start position:0%
three ones
 

00:11:06.399 --> 00:11:09.269 align:start position:0%
three ones
and<00:11:06.959><c> we</c><00:11:07.200><c> have</c><00:11:07.440><c> it</c><00:11:07.600><c> we</c><00:11:07.760><c> have</c><00:11:08.000><c> a</c><00:11:08.079><c> fully</c><00:11:08.399><c> operating</c>

00:11:09.269 --> 00:11:09.279 align:start position:0%
and we have it we have a fully operating
 

00:11:09.279 --> 00:11:12.230 align:start position:0%
and we have it we have a fully operating
quadrant<00:11:09.920><c> cluster</c><00:11:10.880><c> done</c><00:11:11.200><c> within</c><00:11:11.680><c> just</c><00:11:11.920><c> a</c><00:11:12.000><c> few</c>

00:11:12.230 --> 00:11:12.240 align:start position:0%
quadrant cluster done within just a few
 

00:11:12.240 --> 00:11:15.910 align:start position:0%
quadrant cluster done within just a few
minutes<00:11:12.959><c> using</c><00:11:13.360><c> docker</c><00:11:13.760><c> compose</c>

00:11:15.910 --> 00:11:15.920 align:start position:0%
minutes using docker compose
 

00:11:15.920 --> 00:11:17.110 align:start position:0%
minutes using docker compose
this<00:11:16.079><c> remember</c><00:11:16.480><c> this</c><00:11:16.640><c> is</c><00:11:16.720><c> still</c><00:11:16.959><c> an</c>

00:11:17.110 --> 00:11:17.120 align:start position:0%
this remember this is still an
 

00:11:17.120 --> 00:11:19.509 align:start position:0%
this remember this is still an
experimental<00:11:17.839><c> mode</c><00:11:18.160><c> so</c><00:11:18.560><c> if</c><00:11:18.720><c> you</c><00:11:18.880><c> face</c><00:11:19.200><c> any</c>

00:11:19.509 --> 00:11:19.519 align:start position:0%
experimental mode so if you face any
 

00:11:19.519 --> 00:11:21.509 align:start position:0%
experimental mode so if you face any
issues<00:11:19.839><c> please</c><00:11:20.079><c> let</c><00:11:20.240><c> us</c><00:11:20.399><c> know</c><00:11:20.720><c> using</c><00:11:21.040><c> our</c>

00:11:21.509 --> 00:11:21.519 align:start position:0%
issues please let us know using our
 

00:11:21.519 --> 00:11:26.920 align:start position:0%
issues please let us know using our
discord<00:11:22.079><c> community</c><00:11:22.880><c> or</c><00:11:23.120><c> just</c><00:11:23.440><c> github</c><00:11:23.920><c> issues</c>

