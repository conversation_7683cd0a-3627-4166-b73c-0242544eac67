WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.869 align:start position:0%
 
hi<00:00:00.480><c> everyone</c><00:00:00.659><c> I'm</c><00:00:01.439><c> kadesha</c><00:00:02.220><c> and</c><00:00:02.520><c> today</c><00:00:02.639><c> we'll</c>

00:00:02.869 --> 00:00:02.879 align:start position:0%
hi everyone I'm kadesha and today we'll
 

00:00:02.879 --> 00:00:04.670 align:start position:0%
hi everyone I'm kadesha and today we'll
be<00:00:03.060><c> looking</c><00:00:03.179><c> at</c><00:00:03.419><c> how</c><00:00:03.720><c> to</c><00:00:03.840><c> use</c><00:00:04.020><c> github's</c>

00:00:04.670 --> 00:00:04.680 align:start position:0%
be looking at how to use github's
 

00:00:04.680 --> 00:00:07.150 align:start position:0%
be looking at how to use github's
deployment<00:00:05.040><c> protection</c><00:00:05.460><c> rules</c><00:00:05.940><c> with</c><00:00:06.600><c> Century</c>

00:00:07.150 --> 00:00:07.160 align:start position:0%
deployment protection rules with Century
 

00:00:07.160 --> 00:00:09.950 align:start position:0%
deployment protection rules with Century
I<00:00:08.160><c> have</c><00:00:08.340><c> director</c><00:00:08.760><c> of</c><00:00:09.000><c> developer</c><00:00:09.599><c> relations</c>

00:00:09.950 --> 00:00:09.960 align:start position:0%
I have director of developer relations
 

00:00:09.960 --> 00:00:12.709 align:start position:0%
I have director of developer relations
Sarah<00:00:10.559><c> here</c><00:00:10.860><c> with</c><00:00:11.099><c> me</c><00:00:11.219><c> from</c><00:00:11.580><c> Century</c><00:00:12.059><c> who</c><00:00:12.599><c> will</c>

00:00:12.709 --> 00:00:12.719 align:start position:0%
Sarah here with me from Century who will
 

00:00:12.719 --> 00:00:14.209 align:start position:0%
Sarah here with me from Century who will
show<00:00:12.900><c> us</c><00:00:13.019><c> how</c><00:00:13.320><c> we</c><00:00:13.500><c> can</c><00:00:13.620><c> increase</c><00:00:13.860><c> the</c>

00:00:14.209 --> 00:00:14.219 align:start position:0%
show us how we can increase the
 

00:00:14.219 --> 00:00:16.310 align:start position:0%
show us how we can increase the
reliability<00:00:14.700><c> of</c><00:00:15.059><c> our</c><00:00:15.179><c> deployment</c><00:00:15.660><c> pipelines</c>

00:00:16.310 --> 00:00:16.320 align:start position:0%
reliability of our deployment pipelines
 

00:00:16.320 --> 00:00:19.970 align:start position:0%
reliability of our deployment pipelines
with<00:00:17.160><c> their</c><00:00:17.340><c> new</c><00:00:17.460><c> GitHub</c><00:00:17.880><c> app</c><00:00:18.119><c> Sarah</c><00:00:19.080><c> over</c><00:00:19.740><c> to</c>

00:00:19.970 --> 00:00:19.980 align:start position:0%
with their new GitHub app Sarah over to
 

00:00:19.980 --> 00:00:20.750 align:start position:0%
with their new GitHub app Sarah over to
you

00:00:20.750 --> 00:00:20.760 align:start position:0%
you
 

00:00:20.760 --> 00:00:23.570 align:start position:0%
you
thank<00:00:21.180><c> you</c><00:00:21.300><c> kadisha</c><00:00:21.960><c> I'm</c><00:00:22.560><c> really</c><00:00:22.800><c> excited</c><00:00:23.279><c> to</c>

00:00:23.570 --> 00:00:23.580 align:start position:0%
thank you kadisha I'm really excited to
 

00:00:23.580 --> 00:00:25.790 align:start position:0%
thank you kadisha I'm really excited to
tell<00:00:23.760><c> you</c><00:00:23.939><c> a</c><00:00:24.240><c> little</c><00:00:24.300><c> bit</c><00:00:24.480><c> about</c><00:00:24.779><c> the</c><00:00:25.380><c> century</c>

00:00:25.790 --> 00:00:25.800 align:start position:0%
tell you a little bit about the century
 

00:00:25.800 --> 00:00:27.650 align:start position:0%
tell you a little bit about the century
GitHub<00:00:26.279><c> deployment</c><00:00:26.820><c> Gates</c><00:00:27.119><c> integration</c>

00:00:27.650 --> 00:00:27.660 align:start position:0%
GitHub deployment Gates integration
 

00:00:27.660 --> 00:00:28.910 align:start position:0%
GitHub deployment Gates integration
today

00:00:28.910 --> 00:00:28.920 align:start position:0%
today
 

00:00:28.920 --> 00:00:31.609 align:start position:0%
today
here's<00:00:29.519><c> the</c><00:00:29.699><c> thing</c><00:00:29.820><c> we've</c><00:00:30.779><c> become</c><00:00:31.080><c> used</c><00:00:31.439><c> to</c>

00:00:31.609 --> 00:00:31.619 align:start position:0%
here's the thing we've become used to
 

00:00:31.619 --> 00:00:33.530 align:start position:0%
here's the thing we've become used to
contributing<00:00:32.220><c> to</c><00:00:32.520><c> large</c><00:00:32.759><c> code</c><00:00:33.059><c> bases</c>

00:00:33.530 --> 00:00:33.540 align:start position:0%
contributing to large code bases
 

00:00:33.540 --> 00:00:35.810 align:start position:0%
contributing to large code bases
collaboratively<00:00:34.500><c> with</c><00:00:34.980><c> not</c><00:00:35.280><c> only</c><00:00:35.399><c> multiple</c>

00:00:35.810 --> 00:00:35.820 align:start position:0%
collaboratively with not only multiple
 

00:00:35.820 --> 00:00:38.690 align:start position:0%
collaboratively with not only multiple
developers<00:00:36.420><c> but</c><00:00:37.079><c> oftentimes</c><00:00:37.620><c> multiple</c><00:00:38.160><c> teams</c>

00:00:38.690 --> 00:00:38.700 align:start position:0%
developers but oftentimes multiple teams
 

00:00:38.700 --> 00:00:41.990 align:start position:0%
developers but oftentimes multiple teams
and<00:00:39.420><c> doing</c><00:00:39.719><c> so</c><00:00:39.960><c> quickly</c><00:00:40.760><c> shipping</c><00:00:41.760><c> code</c>

00:00:41.990 --> 00:00:42.000 align:start position:0%
and doing so quickly shipping code
 

00:00:42.000 --> 00:00:44.090 align:start position:0%
and doing so quickly shipping code
quickly

00:00:44.090 --> 00:00:44.100 align:start position:0%
quickly
 

00:00:44.100 --> 00:00:47.330 align:start position:0%
quickly
the<00:00:44.640><c> biggest</c><00:00:44.760><c> risk</c><00:00:45.300><c> here</c><00:00:45.480><c> is</c><00:00:46.020><c> that</c><00:00:46.320><c> errors</c><00:00:46.860><c> are</c>

00:00:47.330 --> 00:00:47.340 align:start position:0%
the biggest risk here is that errors are
 

00:00:47.340 --> 00:00:50.270 align:start position:0%
the biggest risk here is that errors are
also<00:00:47.760><c> likely</c><00:00:48.239><c> to</c><00:00:48.539><c> be</c><00:00:48.780><c> shipped</c><00:00:49.200><c> quickly</c><00:00:49.620><c> and</c><00:00:50.039><c> we</c>

00:00:50.270 --> 00:00:50.280 align:start position:0%
also likely to be shipped quickly and we
 

00:00:50.280 --> 00:00:52.490 align:start position:0%
also likely to be shipped quickly and we
need<00:00:50.460><c> to</c><00:00:50.820><c> just</c><00:00:51.300><c> as</c><00:00:51.420><c> quickly</c><00:00:51.719><c> as</c><00:00:51.899><c> we</c><00:00:52.020><c> ship</c><00:00:52.260><c> that</c>

00:00:52.490 --> 00:00:52.500 align:start position:0%
need to just as quickly as we ship that
 

00:00:52.500 --> 00:00:55.850 align:start position:0%
need to just as quickly as we ship that
code<00:00:53.120><c> identify</c><00:00:54.120><c> detect</c><00:00:54.719><c> and</c><00:00:55.079><c> fix</c><00:00:55.320><c> those</c>

00:00:55.850 --> 00:00:55.860 align:start position:0%
code identify detect and fix those
 

00:00:55.860 --> 00:00:58.790 align:start position:0%
code identify detect and fix those
errors<00:00:56.340><c> to</c><00:00:57.180><c> help</c><00:00:57.300><c> ensure</c><00:00:57.719><c> that</c><00:00:58.020><c> your</c><00:00:58.320><c> code</c><00:00:58.500><c> is</c>

00:00:58.790 --> 00:00:58.800 align:start position:0%
errors to help ensure that your code is
 

00:00:58.800 --> 00:01:00.650 align:start position:0%
errors to help ensure that your code is
performant<00:00:59.340><c> and</c><00:00:59.579><c> reliable</c><00:01:00.059><c> while</c><00:01:00.480><c> you're</c>

00:01:00.650 --> 00:01:00.660 align:start position:0%
performant and reliable while you're
 

00:01:00.660 --> 00:01:01.970 align:start position:0%
performant and reliable while you're
deploying<00:01:01.140><c> code</c>

00:01:01.970 --> 00:01:01.980 align:start position:0%
deploying code
 

00:01:01.980 --> 00:01:04.729 align:start position:0%
deploying code
Centric<00:01:02.699><c> and</c><00:01:03.000><c> GitHub</c><00:01:03.359><c> partnered</c><00:01:03.960><c> to</c><00:01:04.199><c> build</c><00:01:04.379><c> a</c>

00:01:04.729 --> 00:01:04.739 align:start position:0%
Centric and GitHub partnered to build a
 

00:01:04.739 --> 00:01:07.609 align:start position:0%
Centric and GitHub partnered to build a
bridge<00:01:04.920><c> between</c><00:01:05.460><c> your</c><00:01:05.820><c> CI</c><00:01:06.180><c> CD</c><00:01:06.479><c> workflow</c><00:01:06.960><c> and</c>

00:01:07.609 --> 00:01:07.619 align:start position:0%
bridge between your CI CD workflow and
 

00:01:07.619 --> 00:01:09.289 align:start position:0%
bridge between your CI CD workflow and
your<00:01:07.920><c> favorite</c><00:01:08.159><c> application</c><00:01:08.760><c> monitoring</c>

00:01:09.289 --> 00:01:09.299 align:start position:0%
your favorite application monitoring
 

00:01:09.299 --> 00:01:11.990 align:start position:0%
your favorite application monitoring
tool<00:01:09.600><c> which</c><00:01:09.900><c> is</c><00:01:10.080><c> Sentry</c><00:01:10.740><c> of</c><00:01:11.040><c> course</c>

00:01:11.990 --> 00:01:12.000 align:start position:0%
tool which is Sentry of course
 

00:01:12.000 --> 00:01:14.450 align:start position:0%
tool which is Sentry of course
Sentry<00:01:12.659><c> helps</c><00:01:13.020><c> you</c><00:01:13.080><c> detect</c><00:01:13.380><c> new</c><00:01:13.680><c> issues</c><00:01:14.159><c> after</c>

00:01:14.450 --> 00:01:14.460 align:start position:0%
Sentry helps you detect new issues after
 

00:01:14.460 --> 00:01:16.190 align:start position:0%
Sentry helps you detect new issues after
a<00:01:14.700><c> release</c><00:01:14.820><c> and</c><00:01:15.180><c> with</c><00:01:15.360><c> the</c><00:01:15.540><c> deployment</c><00:01:15.840><c> gate</c>

00:01:16.190 --> 00:01:16.200 align:start position:0%
a release and with the deployment gate
 

00:01:16.200 --> 00:01:18.350 align:start position:0%
a release and with the deployment gate
integration<00:01:16.560><c> with</c><00:01:16.860><c> GitHub</c><00:01:17.220><c> you</c><00:01:17.820><c> can</c><00:01:17.939><c> now</c><00:01:18.119><c> use</c>

00:01:18.350 --> 00:01:18.360 align:start position:0%
integration with GitHub you can now use
 

00:01:18.360 --> 00:01:20.210 align:start position:0%
integration with GitHub you can now use
sentries<00:01:18.780><c> monitoring</c><00:01:19.320><c> insights</c><00:01:19.860><c> in</c><00:01:20.100><c> your</c>

00:01:20.210 --> 00:01:20.220 align:start position:0%
sentries monitoring insights in your
 

00:01:20.220 --> 00:01:22.310 align:start position:0%
sentries monitoring insights in your
deployment<00:01:20.580><c> pipelines</c><00:01:21.180><c> to</c><00:01:21.840><c> automatically</c>

00:01:22.310 --> 00:01:22.320 align:start position:0%
deployment pipelines to automatically
 

00:01:22.320 --> 00:01:25.190 align:start position:0%
deployment pipelines to automatically
spot<00:01:22.740><c> errors</c><00:01:23.220><c> and</c><00:01:23.520><c> revert</c><00:01:23.880><c> bad</c><00:01:24.119><c> rollouts</c>

00:01:25.190 --> 00:01:25.200 align:start position:0%
spot errors and revert bad rollouts
 

00:01:25.200 --> 00:01:27.230 align:start position:0%
spot errors and revert bad rollouts
before<00:01:25.619><c> this</c><00:01:25.860><c> integration</c><00:01:26.280><c> developers</c><00:01:26.820><c> using</c>

00:01:27.230 --> 00:01:27.240 align:start position:0%
before this integration developers using
 

00:01:27.240 --> 00:01:29.570 align:start position:0%
before this integration developers using
GitHub<00:01:27.659><c> would</c><00:01:28.200><c> typically</c><00:01:28.680><c> use</c><00:01:29.040><c> the</c><00:01:29.280><c> built-in</c>

00:01:29.570 --> 00:01:29.580 align:start position:0%
GitHub would typically use the built-in
 

00:01:29.580 --> 00:01:31.550 align:start position:0%
GitHub would typically use the built-in
environment<00:01:30.000><c> protection</c><00:01:30.360><c> rules</c><00:01:30.840><c> to</c><00:01:31.320><c> require</c>

00:01:31.550 --> 00:01:31.560 align:start position:0%
environment protection rules to require
 

00:01:31.560 --> 00:01:34.490 align:start position:0%
environment protection rules to require
a<00:01:32.159><c> manual</c><00:01:32.460><c> approval</c><00:01:33.000><c> delay</c><00:01:33.659><c> a</c><00:01:33.840><c> job</c><00:01:34.020><c> or</c>

00:01:34.490 --> 00:01:34.500 align:start position:0%
a manual approval delay a job or
 

00:01:34.500 --> 00:01:35.870 align:start position:0%
a manual approval delay a job or
restrict<00:01:34.920><c> the</c><00:01:35.159><c> environment</c><00:01:35.460><c> to</c><00:01:35.700><c> certain</c>

00:01:35.870 --> 00:01:35.880 align:start position:0%
restrict the environment to certain
 

00:01:35.880 --> 00:01:38.450 align:start position:0%
restrict the environment to certain
branches<00:01:36.360><c> there</c><00:01:37.320><c> was</c><00:01:37.500><c> no</c><00:01:37.860><c> support</c><00:01:38.159><c> for</c>

00:01:38.450 --> 00:01:38.460 align:start position:0%
branches there was no support for
 

00:01:38.460 --> 00:01:39.950 align:start position:0%
branches there was no support for
developers<00:01:38.939><c> to</c><00:01:39.240><c> create</c><00:01:39.360><c> and</c><00:01:39.600><c> Implement</c>

00:01:39.950 --> 00:01:39.960 align:start position:0%
developers to create and Implement
 

00:01:39.960 --> 00:01:41.870 align:start position:0%
developers to create and Implement
custom<00:01:40.320><c> roles</c><00:01:40.799><c> that</c><00:01:41.220><c> can</c><00:01:41.400><c> be</c><00:01:41.520><c> configured</c>

00:01:41.870 --> 00:01:41.880 align:start position:0%
custom roles that can be configured
 

00:01:41.880 --> 00:01:43.789 align:start position:0%
custom roles that can be configured
within<00:01:42.360><c> their</c><00:01:42.540><c> deployment</c><00:01:42.840><c> workflows</c><00:01:43.439><c> to</c>

00:01:43.789 --> 00:01:43.799 align:start position:0%
within their deployment workflows to
 

00:01:43.799 --> 00:01:45.710 align:start position:0%
within their deployment workflows to
safely<00:01:44.100><c> promote</c><00:01:44.579><c> deployments</c><00:01:45.240><c> to</c><00:01:45.420><c> production</c>

00:01:45.710 --> 00:01:45.720 align:start position:0%
safely promote deployments to production
 

00:01:45.720 --> 00:01:46.969 align:start position:0%
safely promote deployments to production
environments

00:01:46.969 --> 00:01:46.979 align:start position:0%
environments
 

00:01:46.979 --> 00:01:49.910 align:start position:0%
environments
now<00:01:47.820><c> GitHub</c><00:01:48.479><c> Enterprise</c><00:01:48.720><c> cloud</c><00:01:49.200><c> and</c><00:01:49.500><c> GitHub</c>

00:01:49.910 --> 00:01:49.920 align:start position:0%
now GitHub Enterprise cloud and GitHub
 

00:01:49.920 --> 00:01:52.310 align:start position:0%
now GitHub Enterprise cloud and GitHub
server<00:01:50.340><c> customers</c><00:01:50.759><c> have</c><00:01:51.240><c> an</c><00:01:51.479><c> extensible</c><00:01:51.899><c> way</c>

00:01:52.310 --> 00:01:52.320 align:start position:0%
server customers have an extensible way
 

00:01:52.320 --> 00:01:54.770 align:start position:0%
server customers have an extensible way
to<00:01:52.680><c> create</c><00:01:52.860><c> their</c><00:01:53.399><c> own</c><00:01:53.579><c> rules</c><00:01:54.060><c> to</c><00:01:54.659><c> control</c>

00:01:54.770 --> 00:01:54.780 align:start position:0%
to create their own rules to control
 

00:01:54.780 --> 00:01:56.929 align:start position:0%
to create their own rules to control
deployment<00:01:55.380><c> workflows</c><00:01:55.979><c> through</c><00:01:56.460><c> deployment</c>

00:01:56.929 --> 00:01:56.939 align:start position:0%
deployment workflows through deployment
 

00:01:56.939 --> 00:01:59.389 align:start position:0%
deployment workflows through deployment
protection<00:01:57.360><c> Rules</c><00:01:57.720><c> by</c><00:01:58.560><c> configuring</c><00:01:59.100><c> these</c>

00:01:59.389 --> 00:01:59.399 align:start position:0%
protection Rules by configuring these
 

00:01:59.399 --> 00:02:01.370 align:start position:0%
protection Rules by configuring these
rules<00:01:59.640><c> you</c><00:02:00.000><c> can</c><00:02:00.060><c> set</c><00:02:00.240><c> up</c><00:02:00.420><c> quality</c><00:02:00.780><c> Gates</c><00:02:01.079><c> on</c>

00:02:01.370 --> 00:02:01.380 align:start position:0%
rules you can set up quality Gates on
 

00:02:01.380 --> 00:02:03.109 align:start position:0%
rules you can set up quality Gates on
every<00:02:01.619><c> deployment</c><00:02:02.040><c> environment</c><00:02:02.520><c> stopping</c>

00:02:03.109 --> 00:02:03.119 align:start position:0%
every deployment environment stopping
 

00:02:03.119 --> 00:02:05.149 align:start position:0%
every deployment environment stopping
deployments<00:02:03.600><c> that</c><00:02:03.840><c> do</c><00:02:04.140><c> not</c><00:02:04.320><c> meet</c><00:02:04.619><c> specific</c>

00:02:05.149 --> 00:02:05.159 align:start position:0%
deployments that do not meet specific
 

00:02:05.159 --> 00:02:07.730 align:start position:0%
deployments that do not meet specific
quality<00:02:05.759><c> criteria</c><00:02:06.360><c> with</c><00:02:06.899><c> the</c><00:02:07.079><c> century</c><00:02:07.380><c> GitHub</c>

00:02:07.730 --> 00:02:07.740 align:start position:0%
quality criteria with the century GitHub
 

00:02:07.740 --> 00:02:09.529 align:start position:0%
quality criteria with the century GitHub
deployment<00:02:08.099><c> gate</c><00:02:08.459><c> integration</c><00:02:08.880><c> installed</c>

00:02:09.529 --> 00:02:09.539 align:start position:0%
deployment gate integration installed
 

00:02:09.539 --> 00:02:11.750 align:start position:0%
deployment gate integration installed
you<00:02:10.140><c> can</c><00:02:10.259><c> set</c><00:02:10.380><c> up</c><00:02:10.500><c> a</c><00:02:10.679><c> rule</c><00:02:10.920><c> that</c><00:02:11.160><c> will</c><00:02:11.340><c> block</c><00:02:11.520><c> a</c>

00:02:11.750 --> 00:02:11.760 align:start position:0%
you can set up a rule that will block a
 

00:02:11.760 --> 00:02:14.210 align:start position:0%
you can set up a rule that will block a
rollout<00:02:12.120><c> from</c><00:02:12.300><c> continuing</c><00:02:12.720><c> GitHub</c><00:02:13.200><c> if</c><00:02:13.920><c> a</c><00:02:14.099><c> new</c>

00:02:14.210 --> 00:02:14.220 align:start position:0%
rollout from continuing GitHub if a new
 

00:02:14.220 --> 00:02:16.729 align:start position:0%
rollout from continuing GitHub if a new
issue<00:02:14.520><c> is</c><00:02:14.700><c> detected</c><00:02:15.120><c> by</c><00:02:15.540><c> Sentry</c><00:02:16.020><c> in</c><00:02:16.560><c> the</c>

00:02:16.729 --> 00:02:16.739 align:start position:0%
issue is detected by Sentry in the
 

00:02:16.739 --> 00:02:17.570 align:start position:0%
issue is detected by Sentry in the
release

00:02:17.570 --> 00:02:17.580 align:start position:0%
release
 

00:02:17.580 --> 00:02:19.430 align:start position:0%
release
depending<00:02:18.300><c> on</c><00:02:18.480><c> how</c><00:02:18.720><c> your</c><00:02:18.959><c> pipeline</c><00:02:19.319><c> is</c>

00:02:19.430 --> 00:02:19.440 align:start position:0%
depending on how your pipeline is
 

00:02:19.440 --> 00:02:20.869 align:start position:0%
depending on how your pipeline is
written<00:02:19.680><c> you</c><00:02:19.980><c> can</c><00:02:20.040><c> either</c><00:02:20.220><c> prevent</c><00:02:20.700><c> the</c>

00:02:20.869 --> 00:02:20.879 align:start position:0%
written you can either prevent the
 

00:02:20.879 --> 00:02:22.670 align:start position:0%
written you can either prevent the
rollout<00:02:21.239><c> from</c><00:02:21.540><c> expanding</c><00:02:22.020><c> if</c><00:02:22.260><c> an</c><00:02:22.379><c> error</c><00:02:22.620><c> is</c>

00:02:22.670 --> 00:02:22.680 align:start position:0%
rollout from expanding if an error is
 

00:02:22.680 --> 00:02:25.250 align:start position:0%
rollout from expanding if an error is
detected<00:02:23.120><c> or</c><00:02:24.120><c> revert</c><00:02:24.720><c> the</c><00:02:24.900><c> deployment</c>

00:02:25.250 --> 00:02:25.260 align:start position:0%
detected or revert the deployment
 

00:02:25.260 --> 00:02:27.650 align:start position:0%
detected or revert the deployment
altogether<00:02:26.040><c> this</c><00:02:26.760><c> capability</c><00:02:27.120><c> can</c><00:02:27.480><c> be</c>

00:02:27.650 --> 00:02:27.660 align:start position:0%
altogether this capability can be
 

00:02:27.660 --> 00:02:29.330 align:start position:0%
altogether this capability can be
especially<00:02:28.200><c> helpful</c><00:02:28.560><c> during</c><00:02:28.800><c> a</c><00:02:29.099><c> canary</c>

00:02:29.330 --> 00:02:29.340 align:start position:0%
especially helpful during a canary
 

00:02:29.340 --> 00:02:31.309 align:start position:0%
especially helpful during a canary
release<00:02:29.640><c> when</c><00:02:30.239><c> you're</c><00:02:30.420><c> scaling</c><00:02:30.780><c> up</c><00:02:30.840><c> a</c><00:02:31.020><c> rollout</c>

00:02:31.309 --> 00:02:31.319 align:start position:0%
release when you're scaling up a rollout
 

00:02:31.319 --> 00:02:33.410 align:start position:0%
release when you're scaling up a rollout
and<00:02:31.560><c> need</c><00:02:31.739><c> to</c><00:02:31.920><c> immediately</c><00:02:32.400><c> roll</c><00:02:32.879><c> back</c><00:02:33.120><c> the</c>

00:02:33.410 --> 00:02:33.420 align:start position:0%
and need to immediately roll back the
 

00:02:33.420 --> 00:02:36.290 align:start position:0%
and need to immediately roll back the
release<00:02:33.599><c> if</c><00:02:34.140><c> anything</c><00:02:34.440><c> breaks</c><00:02:35.459><c> if</c><00:02:36.000><c> there</c><00:02:36.180><c> are</c>

00:02:36.290 --> 00:02:36.300 align:start position:0%
release if anything breaks if there are
 

00:02:36.300 --> 00:02:38.750 align:start position:0%
release if anything breaks if there are
new<00:02:36.480><c> issues</c><00:02:36.780><c> the</c><00:02:37.080><c> gate</c><00:02:37.260><c> will</c><00:02:37.440><c> be</c><00:02:37.560><c> rejected</c><00:02:37.980><c> if</c>

00:02:38.750 --> 00:02:38.760 align:start position:0%
new issues the gate will be rejected if
 

00:02:38.760 --> 00:02:40.369 align:start position:0%
new issues the gate will be rejected if
there<00:02:38.940><c> are</c><00:02:39.060><c> no</c><00:02:39.239><c> issues</c><00:02:39.660><c> the</c><00:02:39.900><c> gate</c><00:02:40.140><c> will</c><00:02:40.260><c> be</c>

00:02:40.369 --> 00:02:40.379 align:start position:0%
there are no issues the gate will be
 

00:02:40.379 --> 00:02:42.770 align:start position:0%
there are no issues the gate will be
approved<00:02:40.739><c> and</c><00:02:40.920><c> the</c><00:02:41.099><c> rollout</c><00:02:41.400><c> will</c><00:02:41.640><c> continue</c>

00:02:42.770 --> 00:02:42.780 align:start position:0%
approved and the rollout will continue
 

00:02:42.780 --> 00:02:44.869 align:start position:0%
approved and the rollout will continue
in<00:02:43.319><c> Century</c><00:02:43.680><c> you</c><00:02:44.099><c> can</c><00:02:44.160><c> look</c><00:02:44.340><c> at</c><00:02:44.580><c> the</c><00:02:44.760><c> new</c>

00:02:44.869 --> 00:02:44.879 align:start position:0%
in Century you can look at the new
 

00:02:44.879 --> 00:02:46.670 align:start position:0%
in Century you can look at the new
issues<00:02:45.300><c> count</c><00:02:45.480><c> for</c><00:02:45.780><c> a</c><00:02:45.900><c> release</c><00:02:46.080><c> within</c><00:02:46.500><c> a</c>

00:02:46.670 --> 00:02:46.680 align:start position:0%
issues count for a release within a
 

00:02:46.680 --> 00:02:47.449 align:start position:0%
issues count for a release within a
project

00:02:47.449 --> 00:02:47.459 align:start position:0%
project
 

00:02:47.459 --> 00:02:49.309 align:start position:0%
project
if<00:02:47.940><c> there</c><00:02:48.180><c> are</c><00:02:48.300><c> any</c><00:02:48.480><c> within</c><00:02:48.840><c> the</c><00:02:48.959><c> configured</c>

00:02:49.309 --> 00:02:49.319 align:start position:0%
if there are any within the configured
 

00:02:49.319 --> 00:02:51.050 align:start position:0%
if there are any within the configured
time<00:02:49.500><c> window</c><00:02:49.680><c> the</c><00:02:50.040><c> gate</c><00:02:50.220><c> will</c><00:02:50.400><c> automatically</c>

00:02:51.050 --> 00:02:51.060 align:start position:0%
time window the gate will automatically
 

00:02:51.060 --> 00:02:52.970 align:start position:0%
time window the gate will automatically
be<00:02:51.239><c> rejected</c><00:02:51.720><c> to</c><00:02:52.019><c> get</c><00:02:52.140><c> started</c><00:02:52.440><c> head</c><00:02:52.739><c> to</c><00:02:52.920><c> your</c>

00:02:52.970 --> 00:02:52.980 align:start position:0%
be rejected to get started head to your
 

00:02:52.980 --> 00:02:54.830 align:start position:0%
be rejected to get started head to your
Sentry<00:02:53.340><c> organization</c><00:02:53.879><c> settings</c><00:02:54.180><c> and</c><00:02:54.599><c> click</c>

00:02:54.830 --> 00:02:54.840 align:start position:0%
Sentry organization settings and click
 

00:02:54.840 --> 00:02:57.229 align:start position:0%
Sentry organization settings and click
on<00:02:54.959><c> Integrations</c><00:02:55.560><c> search</c><00:02:56.459><c> for</c><00:02:56.700><c> the</c><00:02:56.879><c> GitHub</c>

00:02:57.229 --> 00:02:57.239 align:start position:0%
on Integrations search for the GitHub
 

00:02:57.239 --> 00:02:59.390 align:start position:0%
on Integrations search for the GitHub
deployment<00:02:57.720><c> Gates</c><00:02:58.140><c> integration</c><00:02:58.739><c> agree</c><00:02:59.280><c> to</c>

00:02:59.390 --> 00:02:59.400 align:start position:0%
deployment Gates integration agree to
 

00:02:59.400 --> 00:03:01.190 align:start position:0%
deployment Gates integration agree to
the<00:02:59.519><c> terms</c><00:02:59.760><c> and</c><00:02:59.879><c> conditions</c><00:03:00.360><c> accept</c><00:03:00.959><c> and</c>

00:03:01.190 --> 00:03:01.200 align:start position:0%
the terms and conditions accept and
 

00:03:01.200 --> 00:03:03.110 align:start position:0%
the terms and conditions accept and
install<00:03:01.379><c> the</c><00:03:01.680><c> integration</c><00:03:02.040><c> sign</c><00:03:02.819><c> in</c><00:03:03.000><c> to</c>

00:03:03.110 --> 00:03:03.120 align:start position:0%
install the integration sign in to
 

00:03:03.120 --> 00:03:04.790 align:start position:0%
install the integration sign in to
GitHub<00:03:03.420><c> and</c><00:03:03.780><c> install</c><00:03:03.900><c> the</c><00:03:04.200><c> Sentry</c><00:03:04.500><c> deployment</c>

00:03:04.790 --> 00:03:04.800 align:start position:0%
GitHub and install the Sentry deployment
 

00:03:04.800 --> 00:03:06.229 align:start position:0%
GitHub and install the Sentry deployment
Gates<00:03:05.099><c> application</c>

00:03:06.229 --> 00:03:06.239 align:start position:0%
Gates application
 

00:03:06.239 --> 00:03:07.790 align:start position:0%
Gates application
from<00:03:06.780><c> there</c><00:03:06.959><c> you</c><00:03:07.260><c> just</c><00:03:07.319><c> select</c><00:03:07.620><c> the</c>

00:03:07.790 --> 00:03:07.800 align:start position:0%
from there you just select the
 

00:03:07.800 --> 00:03:10.430 align:start position:0%
from there you just select the
repositories<00:03:08.400><c> that</c><00:03:08.700><c> you</c><00:03:08.879><c> want</c><00:03:09.060><c> to</c><00:03:09.180><c> enable</c><00:03:09.540><c> and</c>

00:03:10.430 --> 00:03:10.440 align:start position:0%
repositories that you want to enable and
 

00:03:10.440 --> 00:03:12.410 align:start position:0%
repositories that you want to enable and
in<00:03:10.739><c> the</c><00:03:10.860><c> repository</c><00:03:11.400><c> settings</c><00:03:11.700><c> for</c><00:03:12.120><c> each</c><00:03:12.300><c> of</c>

00:03:12.410 --> 00:03:12.420 align:start position:0%
in the repository settings for each of
 

00:03:12.420 --> 00:03:14.869 align:start position:0%
in the repository settings for each of
those<00:03:12.540><c> repositories</c><00:03:13.260><c> click</c><00:03:13.980><c> on</c><00:03:14.159><c> environments</c>

00:03:14.869 --> 00:03:14.879 align:start position:0%
those repositories click on environments
 

00:03:14.879 --> 00:03:17.089 align:start position:0%
those repositories click on environments
in<00:03:15.120><c> the</c><00:03:15.239><c> left</c><00:03:15.420><c> nav</c><00:03:15.659><c> and</c><00:03:16.560><c> select</c><00:03:16.980><c> the</c>

00:03:17.089 --> 00:03:17.099 align:start position:0%
in the left nav and select the
 

00:03:17.099 --> 00:03:18.710 align:start position:0%
in the left nav and select the
environment<00:03:17.459><c> to</c><00:03:17.700><c> configure</c>

00:03:18.710 --> 00:03:18.720 align:start position:0%
environment to configure
 

00:03:18.720 --> 00:03:20.449 align:start position:0%
environment to configure
turn<00:03:19.080><c> on</c><00:03:19.260><c> the</c><00:03:19.440><c> protection</c><00:03:19.739><c> rule</c><00:03:20.159><c> for</c><00:03:20.280><c> this</c>

00:03:20.449 --> 00:03:20.459 align:start position:0%
turn on the protection rule for this
 

00:03:20.459 --> 00:03:21.949 align:start position:0%
turn on the protection rule for this
application<00:03:20.819><c> and</c><00:03:21.239><c> then</c><00:03:21.360><c> save</c><00:03:21.599><c> the</c><00:03:21.840><c> settings</c>

00:03:21.949 --> 00:03:21.959 align:start position:0%
application and then save the settings
 

00:03:21.959 --> 00:03:23.509 align:start position:0%
application and then save the settings
from<00:03:22.500><c> here</c><00:03:22.620><c> you</c><00:03:22.920><c> can</c><00:03:22.980><c> trigger</c><00:03:23.280><c> a</c><00:03:23.400><c> new</c>

00:03:23.509 --> 00:03:23.519 align:start position:0%
from here you can trigger a new
 

00:03:23.519 --> 00:03:25.550 align:start position:0%
from here you can trigger a new
deployment<00:03:23.879><c> when</c><00:03:24.420><c> you</c><00:03:24.599><c> push</c><00:03:24.780><c> this</c><00:03:25.080><c> out</c><00:03:25.319><c> to</c><00:03:25.440><c> the</c>

00:03:25.550 --> 00:03:25.560 align:start position:0%
deployment when you push this out to the
 

00:03:25.560 --> 00:03:27.350 align:start position:0%
deployment when you push this out to the
repository<00:03:26.099><c> it</c><00:03:26.580><c> will</c><00:03:26.700><c> trigger</c><00:03:26.940><c> the</c><00:03:27.060><c> GitHub</c>

00:03:27.350 --> 00:03:27.360 align:start position:0%
repository it will trigger the GitHub
 

00:03:27.360 --> 00:03:29.570 align:start position:0%
repository it will trigger the GitHub
deployment<00:03:27.780><c> workflow</c><00:03:28.560><c> you</c><00:03:29.040><c> can</c><00:03:29.159><c> also</c>

00:03:29.570 --> 00:03:29.580 align:start position:0%
deployment workflow you can also
 

00:03:29.580 --> 00:03:31.490 align:start position:0%
deployment workflow you can also
configure<00:03:30.000><c> the</c><00:03:30.239><c> wait</c><00:03:30.420><c> times</c><00:03:30.659><c> to</c><00:03:31.019><c> see</c><00:03:31.140><c> if</c><00:03:31.319><c> there</c>

00:03:31.490 --> 00:03:31.500 align:start position:0%
configure the wait times to see if there
 

00:03:31.500 --> 00:03:33.770 align:start position:0%
configure the wait times to see if there
are<00:03:31.620><c> any</c><00:03:31.800><c> new</c><00:03:32.040><c> errors</c><00:03:32.459><c> before</c><00:03:33.000><c> continuing</c><00:03:33.540><c> the</c>

00:03:33.770 --> 00:03:33.780 align:start position:0%
are any new errors before continuing the
 

00:03:33.780 --> 00:03:34.610 align:start position:0%
are any new errors before continuing the
deployment

00:03:34.610 --> 00:03:34.620 align:start position:0%
deployment
 

00:03:34.620 --> 00:03:36.290 align:start position:0%
deployment
for<00:03:35.159><c> more</c><00:03:35.340><c> information</c><00:03:35.519><c> on</c><00:03:35.940><c> how</c><00:03:36.120><c> to</c><00:03:36.180><c> get</c>

00:03:36.290 --> 00:03:36.300 align:start position:0%
for more information on how to get
 

00:03:36.300 --> 00:03:37.850 align:start position:0%
for more information on how to get
started<00:03:36.659><c> and</c><00:03:36.959><c> how</c><00:03:37.140><c> it</c><00:03:37.200><c> works</c><00:03:37.440><c> you</c><00:03:37.560><c> can</c><00:03:37.680><c> check</c>

00:03:37.850 --> 00:03:37.860 align:start position:0%
started and how it works you can check
 

00:03:37.860 --> 00:03:39.410 align:start position:0%
started and how it works you can check
out<00:03:37.980><c> that</c><00:03:38.220><c> demo</c><00:03:38.580><c> application</c><00:03:39.000><c> which</c><00:03:39.300><c> is</c>

00:03:39.410 --> 00:03:39.420 align:start position:0%
out that demo application which is
 

00:03:39.420 --> 00:03:41.270 align:start position:0%
out that demo application which is
linked<00:03:39.720><c> down</c><00:03:39.959><c> in</c><00:03:40.080><c> the</c><00:03:40.200><c> description</c><00:03:40.440><c> below</c><00:03:40.799><c> by</c>

00:03:41.270 --> 00:03:41.280 align:start position:0%
linked down in the description below by
 

00:03:41.280 --> 00:03:43.070 align:start position:0%
linked down in the description below by
integrating<00:03:41.700><c> deployment</c><00:03:42.180><c> rules</c><00:03:42.480><c> with</c><00:03:42.659><c> Sentry</c>

00:03:43.070 --> 00:03:43.080 align:start position:0%
integrating deployment rules with Sentry
 

00:03:43.080 --> 00:03:44.750 align:start position:0%
integrating deployment rules with Sentry
you<00:03:43.319><c> can</c><00:03:43.500><c> stop</c><00:03:43.860><c> worrying</c><00:03:44.280><c> about</c><00:03:44.519><c> the</c>

00:03:44.750 --> 00:03:44.760 align:start position:0%
you can stop worrying about the
 

00:03:44.760 --> 00:03:47.149 align:start position:0%
you can stop worrying about the
reliability<00:03:45.239><c> of</c><00:03:45.599><c> the</c><00:03:45.720><c> release</c><00:03:45.900><c> process</c><00:03:46.379><c> and</c>

00:03:47.149 --> 00:03:47.159 align:start position:0%
reliability of the release process and
 

00:03:47.159 --> 00:03:50.449 align:start position:0%
reliability of the release process and
ensure<00:03:47.580><c> that</c><00:03:48.000><c> only</c><00:03:48.420><c> high</c><00:03:48.840><c> quality</c><00:03:49.459><c> thoroughly</c>

00:03:50.449 --> 00:03:50.459 align:start position:0%
ensure that only high quality thoroughly
 

00:03:50.459 --> 00:03:52.850 align:start position:0%
ensure that only high quality thoroughly
tested<00:03:51.060><c> code</c><00:03:51.420><c> reaches</c><00:03:52.019><c> production</c><00:03:52.379><c> the</c>

00:03:52.850 --> 00:03:52.860 align:start position:0%
tested code reaches production the
 

00:03:52.860 --> 00:03:55.009 align:start position:0%
tested code reaches production the
result<00:03:53.040><c> better</c><00:03:53.760><c> overall</c><00:03:54.239><c> development</c><00:03:54.720><c> life</c>

00:03:55.009 --> 00:03:55.019 align:start position:0%
result better overall development life
 

00:03:55.019 --> 00:03:58.190 align:start position:0%
result better overall development life
cycle<00:03:55.319><c> with</c><00:03:55.980><c> fewer</c><00:03:56.459><c> bugs</c><00:03:56.879><c> better</c><00:03:57.180><c> testing</c><00:03:57.720><c> and</c>

00:03:58.190 --> 00:03:58.200 align:start position:0%
cycle with fewer bugs better testing and
 

00:03:58.200 --> 00:04:00.350 align:start position:0%
cycle with fewer bugs better testing and
more<00:03:58.440><c> reliable</c><00:03:58.860><c> code</c><00:03:59.159><c> thank</c><00:03:59.700><c> you</c><00:03:59.760><c> Sarah</c><00:03:59.940><c> for</c>

00:04:00.350 --> 00:04:00.360 align:start position:0%
more reliable code thank you Sarah for
 

00:04:00.360 --> 00:04:01.970 align:start position:0%
more reliable code thank you Sarah for
showing<00:04:00.599><c> us</c><00:04:00.720><c> how</c><00:04:01.019><c> to</c><00:04:01.140><c> make</c><00:04:01.260><c> our</c><00:04:01.500><c> deployment</c>

00:04:01.970 --> 00:04:01.980 align:start position:0%
showing us how to make our deployment
 

00:04:01.980 --> 00:04:04.910 align:start position:0%
showing us how to make our deployment
processes<00:04:02.519><c> more</c><00:04:02.819><c> robust</c><00:04:03.299><c> with</c><00:04:03.959><c> Century's</c><00:04:04.560><c> new</c>

00:04:04.910 --> 00:04:04.920 align:start position:0%
processes more robust with Century's new
 

00:04:04.920 --> 00:04:06.949 align:start position:0%
processes more robust with Century's new
GitHub<00:04:05.340><c> app</c><00:04:05.580><c> if</c><00:04:06.180><c> you</c><00:04:06.299><c> have</c><00:04:06.420><c> questions</c><00:04:06.659><c> about</c>

00:04:06.949 --> 00:04:06.959 align:start position:0%
GitHub app if you have questions about
 

00:04:06.959 --> 00:04:08.930 align:start position:0%
GitHub app if you have questions about
this<00:04:07.260><c> feature</c><00:04:07.680><c> be</c><00:04:08.220><c> sure</c><00:04:08.400><c> to</c><00:04:08.519><c> check</c><00:04:08.640><c> out</c><00:04:08.760><c> the</c>

00:04:08.930 --> 00:04:08.940 align:start position:0%
this feature be sure to check out the
 

00:04:08.940 --> 00:04:10.429 align:start position:0%
this feature be sure to check out the
GitHub<00:04:09.239><c> community</c><00:04:09.540><c> and</c><00:04:09.900><c> leave</c><00:04:10.080><c> us</c><00:04:10.260><c> your</c>

00:04:10.429 --> 00:04:10.439 align:start position:0%
GitHub community and leave us your
 

00:04:10.439 --> 00:04:12.710 align:start position:0%
GitHub community and leave us your
comments<00:04:10.860><c> and</c><00:04:10.980><c> feedback</c><00:04:11.400><c> like</c><00:04:12.180><c> this</c><00:04:12.480><c> video</c>

00:04:12.710 --> 00:04:12.720 align:start position:0%
comments and feedback like this video
 

00:04:12.720 --> 00:04:14.809 align:start position:0%
comments and feedback like this video
share<00:04:13.560><c> it</c><00:04:13.680><c> with</c><00:04:13.799><c> the</c><00:04:13.980><c> dev</c><00:04:14.159><c> who</c><00:04:14.400><c> will</c><00:04:14.519><c> find</c><00:04:14.640><c> it</c>

00:04:14.809 --> 00:04:14.819 align:start position:0%
share it with the dev who will find it
 

00:04:14.819 --> 00:04:17.030 align:start position:0%
share it with the dev who will find it
helpful<00:04:15.180><c> and</c><00:04:15.840><c> subscribe</c><00:04:16.320><c> to</c><00:04:16.500><c> our</c><00:04:16.620><c> channel</c><00:04:16.799><c> so</c>

00:04:17.030 --> 00:04:17.040 align:start position:0%
helpful and subscribe to our channel so
 

00:04:17.040 --> 00:04:18.770 align:start position:0%
helpful and subscribe to our channel so
you<00:04:17.220><c> don't</c><00:04:17.400><c> miss</c><00:04:17.639><c> any</c><00:04:17.880><c> of</c><00:04:18.060><c> our</c><00:04:18.180><c> future</c><00:04:18.419><c> videos</c>

00:04:18.770 --> 00:04:18.780 align:start position:0%
you don't miss any of our future videos
 

00:04:18.780 --> 00:04:22.340 align:start position:0%
you don't miss any of our future videos
until<00:04:19.680><c> next</c><00:04:20.040><c> time</c>

