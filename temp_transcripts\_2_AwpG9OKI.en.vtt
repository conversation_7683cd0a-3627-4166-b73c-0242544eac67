WEBVTT
Kind: captions
Language: en

00:00:00.310 --> 00:00:04.710 align:start position:0%
 
[Music]

00:00:04.710 --> 00:00:04.720 align:start position:0%
 
 

00:00:04.720 --> 00:00:08.950 align:start position:0%
 
hello<00:00:05.040><c> everyone</c><00:00:06.000><c> welcome</c><00:00:06.440><c> to</c><00:00:06.839><c> nlp's</c><00:00:07.840><c> Summit</c><00:00:08.840><c> I</c>

00:00:08.950 --> 00:00:08.960 align:start position:0%
hello everyone welcome to nlp's Summit I
 

00:00:08.960 --> 00:00:12.150 align:start position:0%
hello everyone welcome to nlp's Summit I
am<00:00:09.400><c> honored</c><00:00:09.920><c> to</c><00:00:10.440><c> one</c><00:00:10.599><c> of</c><00:00:10.800><c> the</c><00:00:11.200><c> presenter</c><00:00:11.920><c> in</c>

00:00:12.150 --> 00:00:12.160 align:start position:0%
am honored to one of the presenter in
 

00:00:12.160 --> 00:00:18.070 align:start position:0%
am honored to one of the presenter in
the<00:00:12.880><c> NLP</c><00:00:14.280><c> 2024</c><00:00:15.680><c> Summit</c><00:00:16.680><c> uh</c><00:00:17.160><c> in</c><00:00:17.480><c> this</c>

00:00:18.070 --> 00:00:18.080 align:start position:0%
the NLP 2024 Summit uh in this
 

00:00:18.080 --> 00:00:20.550 align:start position:0%
the NLP 2024 Summit uh in this
presentation<00:00:18.760><c> or</c><00:00:18.960><c> in</c><00:00:19.160><c> this</c><00:00:19.600><c> session</c><00:00:20.279><c> what</c><00:00:20.439><c> I</c>

00:00:20.550 --> 00:00:20.560 align:start position:0%
presentation or in this session what I
 

00:00:20.560 --> 00:00:23.670 align:start position:0%
presentation or in this session what I
will<00:00:20.800><c> do</c><00:00:21.080><c> is</c><00:00:21.240><c> that</c><00:00:21.480><c> I</c><00:00:21.560><c> will</c><00:00:22.560><c> go</c><00:00:22.840><c> through</c><00:00:23.160><c> an</c>

00:00:23.670 --> 00:00:23.680 align:start position:0%
will do is that I will go through an
 

00:00:23.680 --> 00:00:27.710 align:start position:0%
will do is that I will go through an
agentic<00:00:24.680><c> workflow</c><00:00:25.439><c> in</c><00:00:26.160><c> healthcare</c><00:00:27.160><c> which</c><00:00:27.480><c> we</c>

00:00:27.710 --> 00:00:27.720 align:start position:0%
agentic workflow in healthcare which we
 

00:00:27.720 --> 00:00:31.830 align:start position:0%
agentic workflow in healthcare which we
are<00:00:28.679><c> using</c><00:00:29.240><c> on</c><00:00:29.439><c> our</c><00:00:30.000><c> Oracle</c><00:00:30.519><c> Cloud</c>

00:00:31.830 --> 00:00:31.840 align:start position:0%
are using on our Oracle Cloud
 

00:00:31.840 --> 00:00:35.350 align:start position:0%
are using on our Oracle Cloud
infrastructure<00:00:32.840><c> with</c><00:00:33.320><c> John</c><00:00:33.800><c> Snow</c><00:00:34.320><c> labs</c><00:00:35.040><c> and</c>

00:00:35.350 --> 00:00:35.360 align:start position:0%
infrastructure with John Snow labs and
 

00:00:35.360 --> 00:00:40.750 align:start position:0%
infrastructure with John Snow labs and
some<00:00:36.520><c> open-source</c><00:00:37.520><c> llms</c><00:00:38.239><c> and</c><00:00:38.559><c> slms</c><00:00:39.280><c> as</c><00:00:39.760><c> in</c>

00:00:40.750 --> 00:00:40.760 align:start position:0%
some open-source llms and slms as in
 

00:00:40.760 --> 00:00:44.229 align:start position:0%
some open-source llms and slms as in
large<00:00:41.120><c> language</c><00:00:41.640><c> models</c><00:00:42.079><c> and</c><00:00:42.360><c> small</c><00:00:43.120><c> language</c>

00:00:44.229 --> 00:00:44.239 align:start position:0%
large language models and small language
 

00:00:44.239 --> 00:00:48.869 align:start position:0%
large language models and small language
models<00:00:45.239><c> I</c><00:00:45.559><c> work</c><00:00:46.039><c> for</c><00:00:46.680><c> AI</c><00:00:47.199><c> Solutions</c><00:00:47.840><c> team</c><00:00:48.120><c> in</c>

00:00:48.869 --> 00:00:48.879 align:start position:0%
models I work for AI Solutions team in
 

00:00:48.879 --> 00:00:53.590 align:start position:0%
models I work for AI Solutions team in
Oracle<00:00:49.879><c> Cloud</c><00:00:50.680><c> engineering</c><00:00:51.680><c> and</c><00:00:52.399><c> I</c><00:00:52.840><c> and</c><00:00:53.359><c> my</c>

00:00:53.590 --> 00:00:53.600 align:start position:0%
Oracle Cloud engineering and I and my
 

00:00:53.600 --> 00:00:57.509 align:start position:0%
Oracle Cloud engineering and I and my
team<00:00:54.160><c> we</c><00:00:54.680><c> focus</c><00:00:55.120><c> on</c><00:00:55.520><c> AI</c><00:00:56.359><c> Solutions</c>

00:00:57.509 --> 00:00:57.519 align:start position:0%
team we focus on AI Solutions
 

00:00:57.519 --> 00:01:00.790 align:start position:0%
team we focus on AI Solutions
automations<00:00:58.519><c> and</c><00:00:58.800><c> working</c><00:00:59.320><c> with</c>

00:01:00.790 --> 00:01:00.800 align:start position:0%
automations and working with
 

00:01:00.800 --> 00:01:05.070 align:start position:0%
automations and working with
generative<00:01:01.519><c> AI</c><00:01:01.960><c> stuff</c><00:01:03.159><c> across</c><00:01:04.159><c> Industries</c>

00:01:05.070 --> 00:01:05.080 align:start position:0%
generative AI stuff across Industries
 

00:01:05.080 --> 00:01:09.789 align:start position:0%
generative AI stuff across Industries
but<00:01:05.280><c> our</c><00:01:05.600><c> main</c><00:01:06.080><c> focus</c><00:01:06.439><c> is</c><00:01:06.680><c> of</c><00:01:06.840><c> course</c>

00:01:09.789 --> 00:01:09.799 align:start position:0%
 
 

00:01:09.799 --> 00:01:14.630 align:start position:0%
 
Healthcare<00:01:11.280><c> okay</c><00:01:12.280><c> so</c><00:01:12.799><c> what</c><00:01:12.960><c> we</c><00:01:13.119><c> will</c><00:01:13.360><c> do</c><00:01:13.960><c> today</c>

00:01:14.630 --> 00:01:14.640 align:start position:0%
Healthcare okay so what we will do today
 

00:01:14.640 --> 00:01:18.469 align:start position:0%
Healthcare okay so what we will do today
we<00:01:14.799><c> will</c><00:01:15.799><c> uh</c><00:01:16.479><c> basically</c><00:01:17.200><c> go</c><00:01:17.439><c> through</c><00:01:17.759><c> four</c>

00:01:18.469 --> 00:01:18.479 align:start position:0%
we will uh basically go through four
 

00:01:18.479 --> 00:01:21.710 align:start position:0%
we will uh basically go through four
sections<00:01:19.479><c> at</c><00:01:19.720><c> first</c><00:01:20.079><c> we</c><00:01:20.360><c> will</c><00:01:21.360><c> understand</c>

00:01:21.710 --> 00:01:21.720 align:start position:0%
sections at first we will understand
 

00:01:21.720 --> 00:01:25.390 align:start position:0%
sections at first we will understand
what<00:01:21.880><c> is</c><00:01:22.000><c> a</c><00:01:22.479><c> multi-agent</c><00:01:23.920><c> system</c><00:01:24.920><c> then</c>

00:01:25.390 --> 00:01:25.400 align:start position:0%
what is a multi-agent system then
 

00:01:25.400 --> 00:01:28.390 align:start position:0%
what is a multi-agent system then
application<00:01:26.000><c> of</c><00:01:26.479><c> multi-agent</c><00:01:27.400><c> system</c><00:01:27.799><c> in</c>

00:01:28.390 --> 00:01:28.400 align:start position:0%
application of multi-agent system in
 

00:01:28.400 --> 00:01:31.190 align:start position:0%
application of multi-agent system in
healthcare<00:01:29.400><c> third</c><00:01:29.680><c> section</c><00:01:30.200><c> will</c><00:01:30.479><c> focus</c><00:01:30.880><c> on</c>

00:01:31.190 --> 00:01:31.200 align:start position:0%
healthcare third section will focus on
 

00:01:31.200 --> 00:01:34.230 align:start position:0%
healthcare third section will focus on
some<00:01:31.400><c> of</c><00:01:31.600><c> the</c><00:01:31.880><c> benefits</c><00:01:32.399><c> and</c><00:01:32.960><c> challenges</c><00:01:33.960><c> and</c>

00:01:34.230 --> 00:01:34.240 align:start position:0%
some of the benefits and challenges and
 

00:01:34.240 --> 00:01:38.789 align:start position:0%
some of the benefits and challenges and
the<00:01:34.759><c> fourth</c><00:01:35.280><c> section</c><00:01:36.040><c> will</c><00:01:36.720><c> be</c><00:01:37.720><c> uh</c><00:01:38.280><c> focusing</c>

00:01:38.789 --> 00:01:38.799 align:start position:0%
the fourth section will be uh focusing
 

00:01:38.799 --> 00:01:42.670 align:start position:0%
the fourth section will be uh focusing
on<00:01:39.240><c> how</c><00:01:40.079><c> how</c><00:01:40.439><c> we</c><00:01:40.600><c> can</c><00:01:40.960><c> set</c><00:01:41.159><c> up</c><00:01:41.360><c> a</c><00:01:41.759><c> multi-agent</c>

00:01:42.670 --> 00:01:42.680 align:start position:0%
on how how we can set up a multi-agent
 

00:01:42.680 --> 00:01:46.950 align:start position:0%
on how how we can set up a multi-agent
system<00:01:43.079><c> on</c><00:01:43.280><c> Oracle</c><00:01:43.799><c> Cloud</c>

00:01:46.950 --> 00:01:46.960 align:start position:0%
 
 

00:01:46.960 --> 00:01:49.630 align:start position:0%
 
infrastructure<00:01:47.960><c> so</c><00:01:48.600><c> understanding</c><00:01:48.920><c> a</c>

00:01:49.630 --> 00:01:49.640 align:start position:0%
infrastructure so understanding a
 

00:01:49.640 --> 00:01:52.910 align:start position:0%
infrastructure so understanding a
multi-agent<00:01:50.640><c> system</c><00:01:51.399><c> so</c><00:01:52.240><c> what</c><00:01:52.360><c> is</c><00:01:52.520><c> a</c>

00:01:52.910 --> 00:01:52.920 align:start position:0%
multi-agent system so what is a
 

00:01:52.920 --> 00:01:55.190 align:start position:0%
multi-agent system so what is a
multi-agent<00:01:53.880><c> system</c><00:01:54.280><c> or</c><00:01:54.439><c> in</c><00:01:54.560><c> the</c><00:01:54.719><c> industry</c>

00:01:55.190 --> 00:01:55.200 align:start position:0%
multi-agent system or in the industry
 

00:01:55.200 --> 00:01:59.310 align:start position:0%
multi-agent system or in the industry
known<00:01:55.520><c> as</c><00:01:56.320><c> mass</c><00:01:57.240><c> it</c><00:01:57.520><c> actually</c><00:01:58.320><c> involving</c>

00:01:59.310 --> 00:01:59.320 align:start position:0%
known as mass it actually involving
 

00:01:59.320 --> 00:02:00.429 align:start position:0%
known as mass it actually involving
multi

00:02:00.429 --> 00:02:00.439 align:start position:0%
multi
 

00:02:00.439 --> 00:02:04.630 align:start position:0%
multi
intelligent<00:02:01.280><c> agents</c><00:02:02.280><c> they</c><00:02:02.439><c> can</c><00:02:02.840><c> be</c><00:02:03.840><c> slms</c><00:02:04.520><c> they</c>

00:02:04.630 --> 00:02:04.640 align:start position:0%
intelligent agents they can be slms they
 

00:02:04.640 --> 00:02:08.270 align:start position:0%
intelligent agents they can be slms they
can<00:02:04.799><c> be</c><00:02:05.479><c> small</c><00:02:06.479><c> language</c><00:02:07.000><c> models</c><00:02:07.479><c> or</c><00:02:07.719><c> they</c><00:02:07.880><c> can</c>

00:02:08.270 --> 00:02:08.280 align:start position:0%
can be small language models or they can
 

00:02:08.280 --> 00:02:12.030 align:start position:0%
can be small language models or they can
be<00:02:09.280><c> large</c><00:02:09.679><c> language</c><00:02:10.119><c> models</c><00:02:10.560><c> and</c><00:02:10.759><c> other</c><00:02:11.080><c> AI</c>

00:02:12.030 --> 00:02:12.040 align:start position:0%
be large language models and other AI
 

00:02:12.040 --> 00:02:15.390 align:start position:0%
be large language models and other AI
models<00:02:13.040><c> interacting</c><00:02:13.840><c> with</c><00:02:14.160><c> each</c><00:02:14.360><c> other</c><00:02:14.840><c> to</c>

00:02:15.390 --> 00:02:15.400 align:start position:0%
models interacting with each other to
 

00:02:15.400 --> 00:02:18.790 align:start position:0%
models interacting with each other to
solve<00:02:16.239><c> problems</c><00:02:17.120><c> that</c><00:02:17.280><c> are</c><00:02:17.959><c> beyond</c><00:02:18.440><c> the</c>

00:02:18.790 --> 00:02:18.800 align:start position:0%
solve problems that are beyond the
 

00:02:18.800 --> 00:02:21.670 align:start position:0%
solve problems that are beyond the
individual<00:02:19.800><c> capabilities</c><00:02:20.800><c> or</c><00:02:21.040><c> even</c>

00:02:21.670 --> 00:02:21.680 align:start position:0%
individual capabilities or even
 

00:02:21.680 --> 00:02:25.150 align:start position:0%
individual capabilities or even
knowledge<00:02:22.160><c> of</c><00:02:22.480><c> each</c><00:02:22.879><c> agent</c><00:02:23.720><c> so</c><00:02:24.280><c> basically</c>

00:02:25.150 --> 00:02:25.160 align:start position:0%
knowledge of each agent so basically
 

00:02:25.160 --> 00:02:29.150 align:start position:0%
knowledge of each agent so basically
multiple<00:02:25.800><c> agents</c><00:02:26.239><c> are</c><00:02:26.519><c> working</c><00:02:27.879><c> together</c><00:02:28.879><c> and</c>

00:02:29.150 --> 00:02:29.160 align:start position:0%
multiple agents are working together and
 

00:02:29.160 --> 00:02:33.910 align:start position:0%
multiple agents are working together and
solving<00:02:29.640><c> a</c><00:02:30.160><c> complex</c><00:02:31.400><c> Problem</c><00:02:32.400><c> by</c><00:02:33.239><c> breaking</c><00:02:33.680><c> it</c>

00:02:33.910 --> 00:02:33.920 align:start position:0%
solving a complex Problem by breaking it
 

00:02:33.920 --> 00:02:37.630 align:start position:0%
solving a complex Problem by breaking it
up<00:02:34.200><c> into</c><00:02:34.599><c> simpler</c><00:02:35.400><c> tasks</c><00:02:36.400><c> and</c><00:02:36.599><c> then</c><00:02:37.000><c> focusing</c>

00:02:37.630 --> 00:02:37.640 align:start position:0%
up into simpler tasks and then focusing
 

00:02:37.640 --> 00:02:40.949 align:start position:0%
up into simpler tasks and then focusing
and<00:02:37.800><c> then</c><00:02:38.160><c> each</c><00:02:38.440><c> agent</c><00:02:38.879><c> is</c><00:02:39.159><c> focusing</c><00:02:39.720><c> on</c><00:02:40.280><c> a</c>

00:02:40.949 --> 00:02:40.959 align:start position:0%
and then each agent is focusing on a
 

00:02:40.959 --> 00:02:45.710 align:start position:0%
and then each agent is focusing on a
single<00:02:42.440><c> task</c><00:02:43.440><c> so</c><00:02:43.959><c> some</c><00:02:44.200><c> of</c><00:02:44.720><c> the</c>

00:02:45.710 --> 00:02:45.720 align:start position:0%
single task so some of the
 

00:02:45.720 --> 00:02:49.550 align:start position:0%
single task so some of the
characteristics<00:02:46.640><c> of</c><00:02:46.840><c> a</c><00:02:47.560><c> Alti</c><00:02:48.000><c> agent</c><00:02:48.760><c> systems</c>

00:02:49.550 --> 00:02:49.560 align:start position:0%
characteristics of a Alti agent systems
 

00:02:49.560 --> 00:02:50.990 align:start position:0%
characteristics of a Alti agent systems
are

00:02:50.990 --> 00:02:51.000 align:start position:0%
are
 

00:02:51.000 --> 00:02:55.030 align:start position:0%
are
autonomy<00:02:52.239><c> reactivity</c><00:02:53.239><c> how</c><00:02:53.440><c> it</c><00:02:53.599><c> is</c><00:02:54.519><c> reacting</c>

00:02:55.030 --> 00:02:55.040 align:start position:0%
autonomy reactivity how it is reacting
 

00:02:55.040 --> 00:02:56.990 align:start position:0%
autonomy reactivity how it is reacting
to<00:02:55.239><c> a</c><00:02:55.440><c> prompt</c><00:02:55.840><c> or</c><00:02:56.040><c> to</c><00:02:56.200><c> an</c>

00:02:56.990 --> 00:02:57.000 align:start position:0%
to a prompt or to an
 

00:02:57.000 --> 00:03:00.589 align:start position:0%
to a prompt or to an
instruction<00:02:58.000><c> uh</c><00:02:58.840><c> proactiveness</c><00:02:59.920><c> how</c><00:03:00.159><c> it</c><00:03:00.280><c> is</c>

00:03:00.589 --> 00:03:00.599 align:start position:0%
instruction uh proactiveness how it is
 

00:03:00.599 --> 00:03:03.630 align:start position:0%
instruction uh proactiveness how it is
going<00:03:01.000><c> and</c><00:03:01.480><c> solving</c><00:03:02.480><c> something</c><00:03:03.080><c> that</c><00:03:03.319><c> hasn't</c>

00:03:03.630 --> 00:03:03.640 align:start position:0%
going and solving something that hasn't
 

00:03:03.640 --> 00:03:07.309 align:start position:0%
going and solving something that hasn't
been<00:03:03.959><c> asked</c><00:03:04.319><c> for</c><00:03:05.120><c> but</c><00:03:05.560><c> it</c><00:03:05.799><c> has</c><00:03:06.239><c> memorized</c><00:03:07.000><c> from</c>

00:03:07.309 --> 00:03:07.319 align:start position:0%
been asked for but it has memorized from
 

00:03:07.319 --> 00:03:08.390 align:start position:0%
been asked for but it has memorized from
earlier

00:03:08.390 --> 00:03:08.400 align:start position:0%
earlier
 

00:03:08.400 --> 00:03:12.070 align:start position:0%
earlier
interactions<00:03:09.400><c> then</c><00:03:09.680><c> the</c><00:03:10.120><c> social</c><00:03:10.920><c> ability</c>

00:03:12.070 --> 00:03:12.080 align:start position:0%
interactions then the social ability
 

00:03:12.080 --> 00:03:16.149 align:start position:0%
interactions then the social ability
maintaining<00:03:13.080><c> of</c><00:03:13.799><c> facade</c><00:03:14.799><c> as</c><00:03:14.959><c> if</c><00:03:15.200><c> the</c><00:03:15.400><c> user</c><00:03:15.959><c> is</c>

00:03:16.149 --> 00:03:16.159 align:start position:0%
maintaining of facade as if the user is
 

00:03:16.159 --> 00:03:19.589 align:start position:0%
maintaining of facade as if the user is
interacting<00:03:16.799><c> with</c><00:03:16.920><c> a</c><00:03:17.239><c> human</c><00:03:17.799><c> agent</c><00:03:18.799><c> and</c><00:03:19.080><c> the</c>

00:03:19.589 --> 00:03:19.599 align:start position:0%
interacting with a human agent and the
 

00:03:19.599 --> 00:03:22.070 align:start position:0%
interacting with a human agent and the
ability<00:03:20.080><c> to</c><00:03:20.440><c> adapt</c><00:03:20.840><c> to</c><00:03:21.080><c> changing</c>

00:03:22.070 --> 00:03:22.080 align:start position:0%
ability to adapt to changing
 

00:03:22.080 --> 00:03:24.949 align:start position:0%
ability to adapt to changing
environments<00:03:23.080><c> and</c><00:03:23.480><c> making</c><00:03:24.000><c> them</c><00:03:24.400><c> suitable</c>

00:03:24.949 --> 00:03:24.959 align:start position:0%
environments and making them suitable
 

00:03:24.959 --> 00:03:27.670 align:start position:0%
environments and making them suitable
for<00:03:25.680><c> complex</c><00:03:26.680><c> problem</c>

00:03:27.670 --> 00:03:27.680 align:start position:0%
for complex problem
 

00:03:27.680 --> 00:03:32.190 align:start position:0%
for complex problem
solving<00:03:28.680><c> uh</c><00:03:28.879><c> some</c><00:03:29.519><c> type</c><00:03:29.799><c> typ</c><00:03:30.080><c> of</c><00:03:30.920><c> Agents</c><00:03:31.920><c> can</c>

00:03:32.190 --> 00:03:32.200 align:start position:0%
solving uh some type typ of Agents can
 

00:03:32.200 --> 00:03:34.550 align:start position:0%
solving uh some type typ of Agents can
include<00:03:32.920><c> a</c>

00:03:34.550 --> 00:03:34.560 align:start position:0%
include a
 

00:03:34.560 --> 00:03:37.470 align:start position:0%
include a
collaborative<00:03:35.560><c> system</c><00:03:36.200><c> a</c><00:03:36.799><c> competitive</c>

00:03:37.470 --> 00:03:37.480 align:start position:0%
collaborative system a competitive
 

00:03:37.480 --> 00:03:41.390 align:start position:0%
collaborative system a competitive
system<00:03:38.080><c> a</c><00:03:38.599><c> self-interested</c><00:03:39.599><c> agents</c><00:03:40.200><c> like</c><00:03:40.599><c> uh</c>

00:03:41.390 --> 00:03:41.400 align:start position:0%
system a self-interested agents like uh
 

00:03:41.400 --> 00:03:43.550 align:start position:0%
system a self-interested agents like uh
large<00:03:41.799><c> language</c><00:03:42.239><c> models</c><00:03:42.720><c> small</c><00:03:43.120><c> language</c>

00:03:43.550 --> 00:03:43.560 align:start position:0%
large language models small language
 

00:03:43.560 --> 00:03:48.110 align:start position:0%
large language models small language
models<00:03:44.560><c> and</c><00:03:45.239><c> task</c><00:03:46.000><c> specific</c><00:03:47.000><c> automation</c><00:03:47.879><c> like</c>

00:03:48.110 --> 00:03:48.120 align:start position:0%
models and task specific automation like
 

00:03:48.120 --> 00:03:50.070 align:start position:0%
models and task specific automation like
a<00:03:48.319><c> running</c><00:03:48.680><c> and</c>

00:03:50.070 --> 00:03:50.080 align:start position:0%
a running and
 

00:03:50.080 --> 00:03:54.429 align:start position:0%
a running and
optimization<00:03:51.080><c> um</c><00:03:51.480><c> model</c><00:03:52.120><c> or</c><00:03:53.000><c> or</c><00:03:53.760><c> running</c><00:03:54.079><c> an</c>

00:03:54.429 --> 00:03:54.439 align:start position:0%
optimization um model or or running an
 

00:03:54.439 --> 00:04:00.910 align:start position:0%
optimization um model or or running an
algorithm<00:03:55.159><c> to</c><00:03:55.799><c> simulate</c><00:03:57.159><c> molecular</c><00:03:58.159><c> uh</c>

00:04:00.910 --> 00:04:00.920 align:start position:0%
algorithm to simulate molecular uh
 

00:04:00.920 --> 00:04:03.949 align:start position:0%
algorithm to simulate molecular uh
reactions<00:04:01.920><c> you</c><00:04:02.040><c> know</c><00:04:02.439><c> so</c><00:04:02.760><c> each</c><00:04:03.280><c> contributing</c>

00:04:03.949 --> 00:04:03.959 align:start position:0%
reactions you know so each contributing
 

00:04:03.959 --> 00:04:08.990 align:start position:0%
reactions you know so each contributing
to<00:04:04.159><c> the</c><00:04:04.640><c> overall</c><00:04:05.760><c> systems</c>

00:04:08.990 --> 00:04:09.000 align:start position:0%
 
 

00:04:09.000 --> 00:04:15.069 align:start position:0%
 
functionality<00:04:10.319><c> so</c><00:04:11.640><c> um</c><00:04:12.640><c> here</c><00:04:12.840><c> is</c><00:04:13.079><c> a</c><00:04:14.079><c> the</c>

00:04:15.069 --> 00:04:15.079 align:start position:0%
functionality so um here is a the
 

00:04:15.079 --> 00:04:17.430 align:start position:0%
functionality so um here is a the
language<00:04:15.760><c> models</c><00:04:16.280><c> how</c><00:04:16.560><c> they</c><00:04:16.639><c> are</c><00:04:16.919><c> interacting</c>

00:04:17.430 --> 00:04:17.440 align:start position:0%
language models how they are interacting
 

00:04:17.440 --> 00:04:21.150 align:start position:0%
language models how they are interacting
in<00:04:17.600><c> a</c><00:04:17.959><c> multi-agent</c><00:04:19.040><c> system</c><00:04:20.040><c> so</c><00:04:20.440><c> what</c><00:04:20.600><c> is</c><00:04:20.840><c> the</c>

00:04:21.150 --> 00:04:21.160 align:start position:0%
in a multi-agent system so what is the
 

00:04:21.160 --> 00:04:25.629 align:start position:0%
in a multi-agent system so what is the
role<00:04:21.479><c> of</c><00:04:21.759><c> the</c><00:04:22.479><c> language</c><00:04:22.960><c> model</c><00:04:24.080><c> so</c><00:04:25.080><c> I</c><00:04:25.160><c> am</c>

00:04:25.629 --> 00:04:25.639 align:start position:0%
role of the language model so I am
 

00:04:25.639 --> 00:04:30.710 align:start position:0%
role of the language model so I am
taking<00:04:26.000><c> the</c><00:04:26.440><c> example</c><00:04:26.960><c> of</c><00:04:27.520><c> JSL</c><00:04:28.520><c> MedEx</c><00:04:29.919><c> billion</c>

00:04:30.710 --> 00:04:30.720 align:start position:0%
taking the example of JSL MedEx billion
 

00:04:30.720 --> 00:04:34.430 align:start position:0%
taking the example of JSL MedEx billion
parameter<00:04:31.720><c> language</c><00:04:32.240><c> model</c><00:04:32.800><c> from</c><00:04:33.199><c> Jon</c><00:04:33.600><c> Snow</c>

00:04:34.430 --> 00:04:34.440 align:start position:0%
parameter language model from Jon Snow
 

00:04:34.440 --> 00:04:37.749 align:start position:0%
parameter language model from Jon Snow
Labs<00:04:35.360><c> it</c><00:04:35.560><c> has</c><00:04:35.759><c> the</c><00:04:36.120><c> ability</c><00:04:36.639><c> to</c><00:04:37.080><c> process</c><00:04:37.479><c> and</c>

00:04:37.749 --> 00:04:37.759 align:start position:0%
Labs it has the ability to process and
 

00:04:37.759 --> 00:04:41.070 align:start position:0%
Labs it has the ability to process and
generate<00:04:38.280><c> humanik</c><00:04:39.000><c> text</c><00:04:40.000><c> making</c><00:04:40.440><c> them</c>

00:04:41.070 --> 00:04:41.080 align:start position:0%
generate humanik text making them
 

00:04:41.080 --> 00:04:45.350 align:start position:0%
generate humanik text making them
valuable<00:04:42.080><c> components</c><00:04:42.759><c> of</c><00:04:43.000><c> the</c><00:04:43.680><c> overall</c><00:04:44.680><c> mass</c>

00:04:45.350 --> 00:04:45.360 align:start position:0%
valuable components of the overall mass
 

00:04:45.360 --> 00:04:48.550 align:start position:0%
valuable components of the overall mass
for<00:04:46.039><c> natural</c><00:04:46.560><c> language</c><00:04:47.400><c> understanding</c><00:04:47.960><c> and</c>

00:04:48.550 --> 00:04:48.560 align:start position:0%
for natural language understanding and
 

00:04:48.560 --> 00:04:52.270 align:start position:0%
for natural language understanding and
generation<00:04:49.560><c> it</c><00:04:49.680><c> is</c><00:04:49.919><c> used</c><00:04:50.720><c> for</c>

00:04:52.270 --> 00:04:52.280 align:start position:0%
generation it is used for
 

00:04:52.280 --> 00:04:55.469 align:start position:0%
generation it is used for
summarization<00:04:53.280><c> it</c><00:04:53.440><c> can</c><00:04:53.759><c> be</c><00:04:54.160><c> also</c><00:04:54.720><c> used</c><00:04:55.120><c> for</c>

00:04:55.469 --> 00:04:55.479 align:start position:0%
summarization it can be also used for
 

00:04:55.479 --> 00:05:00.310 align:start position:0%
summarization it can be also used for
some<00:04:56.400><c> decision</c><00:04:57.160><c> making</c><00:04:58.039><c> down</c><00:04:58.360><c> the</c><00:04:59.280><c> line</c>

00:05:00.310 --> 00:05:00.320 align:start position:0%
some decision making down the line
 

00:05:00.320 --> 00:05:04.909 align:start position:0%
some decision making down the line
so<00:05:00.960><c> advantages</c><00:05:01.680><c> of</c><00:05:01.960><c> llm</c><00:05:02.520><c> and</c><00:05:03.320><c> slm</c><00:05:04.320><c> are</c><00:05:04.680><c> that</c>

00:05:04.909 --> 00:05:04.919 align:start position:0%
so advantages of llm and slm are that
 

00:05:04.919 --> 00:05:08.469 align:start position:0%
so advantages of llm and slm are that
they<00:05:05.080><c> can</c><00:05:05.479><c> perform</c><00:05:06.160><c> tasks</c><00:05:06.880><c> more</c><00:05:07.680><c> precisely</c>

00:05:08.469 --> 00:05:08.479 align:start position:0%
they can perform tasks more precisely
 

00:05:08.479 --> 00:05:11.909 align:start position:0%
they can perform tasks more precisely
and<00:05:09.240><c> quickly</c><00:05:09.759><c> than</c><00:05:10.000><c> any</c><00:05:10.320><c> human</c><00:05:10.919><c> agents</c>

00:05:11.909 --> 00:05:11.919 align:start position:0%
and quickly than any human agents
 

00:05:11.919 --> 00:05:15.670 align:start position:0%
and quickly than any human agents
enhancing<00:05:12.600><c> the</c><00:05:12.919><c> overall</c><00:05:13.800><c> efficiency</c><00:05:14.520><c> of</c><00:05:14.800><c> the</c>

00:05:15.670 --> 00:05:15.680 align:start position:0%
enhancing the overall efficiency of the
 

00:05:15.680 --> 00:05:17.510 align:start position:0%
enhancing the overall efficiency of the
Alti<00:05:16.000><c> agent</c>

00:05:17.510 --> 00:05:17.520 align:start position:0%
Alti agent
 

00:05:17.520 --> 00:05:21.670 align:start position:0%
Alti agent
system<00:05:18.520><c> of</c><00:05:18.720><c> course</c><00:05:19.039><c> it</c><00:05:19.479><c> also</c><00:05:20.039><c> has</c><00:05:20.360><c> some</c>

00:05:21.670 --> 00:05:21.680 align:start position:0%
system of course it also has some
 

00:05:21.680 --> 00:05:25.150 align:start position:0%
system of course it also has some
challenges<00:05:22.680><c> uh</c><00:05:22.960><c> these</c><00:05:23.240><c> agents</c><00:05:24.080><c> require</c>

00:05:25.150 --> 00:05:25.160 align:start position:0%
challenges uh these agents require
 

00:05:25.160 --> 00:05:27.950 align:start position:0%
challenges uh these agents require
careful<00:05:26.160><c> management</c><00:05:27.039><c> within</c><00:05:27.479><c> the</c>

00:05:27.950 --> 00:05:27.960 align:start position:0%
careful management within the
 

00:05:27.960 --> 00:05:30.790 align:start position:0%
careful management within the
multi-agent<00:05:28.960><c> system</c><00:05:29.319><c> or</c><00:05:29.759><c> within</c><00:05:30.400><c> what</c><00:05:30.600><c> we</c>

00:05:30.790 --> 00:05:30.800 align:start position:0%
multi-agent system or within what we
 

00:05:30.800 --> 00:05:32.749 align:start position:0%
multi-agent system or within what we
call<00:05:31.080><c> the</c><00:05:31.400><c> agentic</c>

00:05:32.749 --> 00:05:32.759 align:start position:0%
call the agentic
 

00:05:32.759 --> 00:05:35.909 align:start position:0%
call the agentic
workflow<00:05:33.759><c> uh</c><00:05:33.960><c> to</c><00:05:34.440><c> ensure</c><00:05:34.960><c> Optimal</c>

00:05:35.909 --> 00:05:35.919 align:start position:0%
workflow uh to ensure Optimal
 

00:05:35.919 --> 00:05:39.430 align:start position:0%
workflow uh to ensure Optimal
Performance<00:05:36.840><c> and</c><00:05:37.520><c> ethical</c>

00:05:39.430 --> 00:05:39.440 align:start position:0%
Performance and ethical
 

00:05:39.440 --> 00:05:43.309 align:start position:0%
Performance and ethical
usage<00:05:40.440><c> so</c><00:05:40.720><c> here</c><00:05:40.919><c> is</c><00:05:41.440><c> just</c><00:05:41.680><c> an</c><00:05:42.240><c> example</c><00:05:42.960><c> how</c><00:05:43.199><c> we</c>

00:05:43.309 --> 00:05:43.319 align:start position:0%
usage so here is just an example how we
 

00:05:43.319 --> 00:05:46.870 align:start position:0%
usage so here is just an example how we
are<00:05:43.560><c> using</c><00:05:43.919><c> an</c><00:05:44.440><c> agentic</c><00:05:45.039><c> workflow</c><00:05:46.000><c> to</c><00:05:46.319><c> enable</c>

00:05:46.870 --> 00:05:46.880 align:start position:0%
are using an agentic workflow to enable
 

00:05:46.880 --> 00:05:49.870 align:start position:0%
are using an agentic workflow to enable
a<00:05:47.039><c> realtime</c><00:05:48.000><c> decision</c><00:05:48.440><c> making</c><00:05:48.919><c> and</c><00:05:49.199><c> strategic</c>

00:05:49.870 --> 00:05:49.880 align:start position:0%
a realtime decision making and strategic
 

00:05:49.880 --> 00:05:52.909 align:start position:0%
a realtime decision making and strategic
planning<00:05:50.880><c> and</c><00:05:51.160><c> also</c><00:05:51.479><c> to</c><00:05:52.080><c> create</c>

00:05:52.909 --> 00:05:52.919 align:start position:0%
planning and also to create
 

00:05:52.919 --> 00:05:57.590 align:start position:0%
planning and also to create
summarization<00:05:53.919><c> for</c><00:05:54.560><c> from</c><00:05:55.400><c> uh</c><00:05:56.039><c> doctor's</c><00:05:57.039><c> notes</c>

00:05:57.590 --> 00:05:57.600 align:start position:0%
summarization for from uh doctor's notes
 

00:05:57.600 --> 00:06:01.510 align:start position:0%
summarization for from uh doctor's notes
and<00:05:58.000><c> entity</c><00:05:59.000><c> recognition</c><00:05:59.960><c> so</c><00:06:00.280><c> user</c><00:06:01.000><c> provides</c>

00:06:01.510 --> 00:06:01.520 align:start position:0%
and entity recognition so user provides
 

00:06:01.520 --> 00:06:05.909 align:start position:0%
and entity recognition so user provides
the<00:06:01.800><c> input</c><00:06:02.520><c> to</c><00:06:03.199><c> slm</c><00:06:03.960><c> aerator</c><00:06:04.960><c> and</c><00:06:05.280><c> then</c><00:06:05.560><c> it</c>

00:06:05.909 --> 00:06:05.919 align:start position:0%
the input to slm aerator and then it
 

00:06:05.919 --> 00:06:07.390 align:start position:0%
the input to slm aerator and then it
goes<00:06:06.280><c> through</c>

00:06:07.390 --> 00:06:07.400 align:start position:0%
goes through
 

00:06:07.400 --> 00:06:12.749 align:start position:0%
goes through
various<00:06:08.440><c> uh</c><00:06:09.440><c> agents</c><00:06:10.280><c> to</c><00:06:10.599><c> get</c><00:06:10.960><c> the</c><00:06:11.560><c> output</c><00:06:12.319><c> and</c>

00:06:12.749 --> 00:06:12.759 align:start position:0%
various uh agents to get the output and
 

00:06:12.759 --> 00:06:17.029 align:start position:0%
various uh agents to get the output and
in<00:06:13.240><c> a</c><00:06:14.240><c> in</c><00:06:14.360><c> a</c><00:06:14.840><c> future</c><00:06:15.280><c> slide</c><00:06:15.840><c> I</c><00:06:15.919><c> will</c><00:06:16.240><c> go</c><00:06:16.680><c> into</c>

00:06:17.029 --> 00:06:17.039 align:start position:0%
in a in a future slide I will go into
 

00:06:17.039 --> 00:06:21.469 align:start position:0%
in a in a future slide I will go into
more<00:06:17.720><c> depth</c><00:06:18.120><c> and</c><00:06:18.360><c> how</c><00:06:18.599><c> we</c><00:06:18.720><c> can</c><00:06:19.199><c> run</c><00:06:19.440><c> it</c><00:06:19.599><c> on</c><00:06:20.479><c> oci</c>

00:06:21.469 --> 00:06:21.479 align:start position:0%
more depth and how we can run it on oci
 

00:06:21.479 --> 00:06:24.790 align:start position:0%
more depth and how we can run it on oci
here<00:06:21.759><c> I</c><00:06:21.960><c> just</c><00:06:22.240><c> have</c><00:06:22.400><c> it</c><00:06:22.680><c> as</c><00:06:22.800><c> an</c>

00:06:24.790 --> 00:06:24.800 align:start position:0%
here I just have it as an
 

00:06:24.800 --> 00:06:30.390 align:start position:0%
here I just have it as an
example<00:06:26.039><c> so</c><00:06:27.039><c> what</c><00:06:27.199><c> is</c><00:06:27.360><c> a</c><00:06:28.120><c> agentic</c><00:06:29.080><c> workflow</c>

00:06:30.390 --> 00:06:30.400 align:start position:0%
example so what is a agentic workflow
 

00:06:30.400 --> 00:06:33.150 align:start position:0%
example so what is a agentic workflow
so<00:06:30.720><c> an</c><00:06:31.400><c> agentic</c>

00:06:33.150 --> 00:06:33.160 align:start position:0%
so an agentic
 

00:06:33.160 --> 00:06:37.550 align:start position:0%
so an agentic
workflow<00:06:34.160><c> uh</c><00:06:34.479><c> is</c><00:06:34.880><c> the</c><00:06:35.360><c> first</c><00:06:35.720><c> step</c><00:06:36.199><c> is</c><00:06:36.560><c> to</c>

00:06:37.550 --> 00:06:37.560 align:start position:0%
workflow uh is the first step is to
 

00:06:37.560 --> 00:06:41.390 align:start position:0%
workflow uh is the first step is to
Define<00:06:37.960><c> it</c><00:06:38.880><c> right</c><00:06:39.240><c> so</c><00:06:39.680><c> this</c><00:06:39.919><c> workflow</c><00:06:40.560><c> using</c>

00:06:41.390 --> 00:06:41.400 align:start position:0%
Define it right so this workflow using
 

00:06:41.400 --> 00:06:44.550 align:start position:0%
Define it right so this workflow using
mass<00:06:42.160><c> focuses</c><00:06:42.840><c> on</c><00:06:43.479><c> coordination</c><00:06:44.199><c> and</c>

00:06:44.550 --> 00:06:44.560 align:start position:0%
mass focuses on coordination and
 

00:06:44.560 --> 00:06:47.469 align:start position:0%
mass focuses on coordination and
collaboration<00:06:45.319><c> of</c><00:06:45.800><c> multiple</c><00:06:46.479><c> agents</c><00:06:47.199><c> to</c>

00:06:47.469 --> 00:06:47.479 align:start position:0%
collaboration of multiple agents to
 

00:06:47.479 --> 00:06:51.350 align:start position:0%
collaboration of multiple agents to
achieve<00:06:48.240><c> specific</c><00:06:48.880><c> goals</c><00:06:49.240><c> or</c><00:06:50.120><c> tasks</c><00:06:51.120><c> then</c>

00:06:51.350 --> 00:06:51.360 align:start position:0%
achieve specific goals or tasks then
 

00:06:51.360 --> 00:06:53.710 align:start position:0%
achieve specific goals or tasks then
what<00:06:51.520><c> we</c><00:06:51.720><c> do</c><00:06:52.039><c> is</c><00:06:52.240><c> that</c><00:06:52.440><c> we</c><00:06:52.639><c> actually</c><00:06:53.080><c> optimize</c>

00:06:53.710 --> 00:06:53.720 align:start position:0%
what we do is that we actually optimize
 

00:06:53.720 --> 00:06:57.670 align:start position:0%
what we do is that we actually optimize
the<00:06:54.520><c> performance</c><00:06:55.520><c> so</c><00:06:56.000><c> a</c><00:06:56.520><c> focused</c><00:06:57.080><c> workflow</c>

00:06:57.670 --> 00:06:57.680 align:start position:0%
the performance so a focused workflow
 

00:06:57.680 --> 00:07:01.790 align:start position:0%
the performance so a focused workflow
can<00:06:58.280><c> lead</c><00:06:58.520><c> to</c><00:06:59.080><c> better</c><00:06:59.960><c> performance</c><00:07:00.960><c> by</c>

00:07:01.790 --> 00:07:01.800 align:start position:0%
can lead to better performance by
 

00:07:01.800 --> 00:07:05.430 align:start position:0%
can lead to better performance by
directing<00:07:02.520><c> slm</c><00:07:03.080><c> based</c><00:07:03.520><c> agents</c><00:07:04.000><c> to</c><00:07:04.639><c> prioritize</c>

00:07:05.430 --> 00:07:05.440 align:start position:0%
directing slm based agents to prioritize
 

00:07:05.440 --> 00:07:09.790 align:start position:0%
directing slm based agents to prioritize
and<00:07:05.840><c> specialize</c><00:07:06.520><c> in</c><00:07:07.000><c> specific</c><00:07:08.080><c> task</c><00:07:09.080><c> we</c><00:07:09.520><c> make</c>

00:07:09.790 --> 00:07:09.800 align:start position:0%
and specialize in specific task we make
 

00:07:09.800 --> 00:07:13.309 align:start position:0%
and specialize in specific task we make
sure<00:07:10.280><c> that</c><00:07:10.440><c> we</c><00:07:10.599><c> are</c><00:07:10.960><c> not</c><00:07:11.280><c> using</c><00:07:12.120><c> one</c><00:07:12.680><c> model</c><00:07:13.120><c> to</c>

00:07:13.309 --> 00:07:13.319 align:start position:0%
sure that we are not using one model to
 

00:07:13.319 --> 00:07:19.230 align:start position:0%
sure that we are not using one model to
do<00:07:14.000><c> multiple</c><00:07:15.000><c> task</c><00:07:15.919><c> so</c><00:07:16.160><c> what</c><00:07:16.360><c> we</c><00:07:16.520><c> do</c><00:07:17.240><c> that</c><00:07:17.479><c> we</c>

00:07:19.230 --> 00:07:19.240 align:start position:0%
do multiple task so what we do that we
 

00:07:19.240 --> 00:07:22.670 align:start position:0%
do multiple task so what we do that we
actually<00:07:20.240><c> uh</c><00:07:20.520><c> isolate</c><00:07:21.120><c> the</c><00:07:21.599><c> models</c><00:07:22.160><c> only</c><00:07:22.440><c> to</c>

00:07:22.670 --> 00:07:22.680 align:start position:0%
actually uh isolate the models only to
 

00:07:22.680 --> 00:07:26.189 align:start position:0%
actually uh isolate the models only to
focus<00:07:23.000><c> on</c><00:07:23.319><c> specific</c><00:07:24.319><c> tasks</c><00:07:24.960><c> and</c><00:07:25.240><c> then</c><00:07:25.520><c> we</c><00:07:25.840><c> tie</c>

00:07:26.189 --> 00:07:26.199 align:start position:0%
focus on specific tasks and then we tie
 

00:07:26.199 --> 00:07:30.029 align:start position:0%
focus on specific tasks and then we tie
them<00:07:26.879><c> together</c><00:07:27.360><c> with</c><00:07:27.960><c> automation</c><00:07:28.960><c> using</c>

00:07:30.029 --> 00:07:30.039 align:start position:0%
them together with automation using
 

00:07:30.039 --> 00:07:33.990 align:start position:0%
them together with automation using
our<00:07:30.720><c> workflow</c><00:07:31.560><c> tools</c><00:07:32.440><c> so</c><00:07:32.720><c> that</c><00:07:33.000><c> they</c><00:07:33.280><c> all</c><00:07:33.639><c> work</c>

00:07:33.990 --> 00:07:34.000 align:start position:0%
our workflow tools so that they all work
 

00:07:34.000 --> 00:07:36.390 align:start position:0%
our workflow tools so that they all work
in<00:07:34.160><c> a</c><00:07:34.759><c> in</c>

00:07:36.390 --> 00:07:36.400 align:start position:0%
in a in
 

00:07:36.400 --> 00:07:38.990 align:start position:0%
in a in
coordination<00:07:37.400><c> uh</c><00:07:37.639><c> some</c><00:07:37.800><c> of</c><00:07:38.000><c> the</c><00:07:38.280><c> key</c>

00:07:38.990 --> 00:07:39.000 align:start position:0%
coordination uh some of the key
 

00:07:39.000 --> 00:07:41.629 align:start position:0%
coordination uh some of the key
considerations<00:07:40.000><c> is</c><00:07:40.240><c> that</c><00:07:40.680><c> designing</c><00:07:41.240><c> an</c>

00:07:41.629 --> 00:07:41.639 align:start position:0%
considerations is that designing an
 

00:07:41.639 --> 00:07:45.309 align:start position:0%
considerations is that designing an
effective<00:07:42.160><c> agentic</c><00:07:42.800><c> workflow</c><00:07:44.080><c> involves</c>

00:07:45.309 --> 00:07:45.319 align:start position:0%
effective agentic workflow involves
 

00:07:45.319 --> 00:07:48.469 align:start position:0%
effective agentic workflow involves
balancing<00:07:46.319><c> the</c><00:07:46.639><c> autonomy</c><00:07:47.240><c> of</c><00:07:47.400><c> the</c><00:07:47.560><c> individual</c>

00:07:48.469 --> 00:07:48.479 align:start position:0%
balancing the autonomy of the individual
 

00:07:48.479 --> 00:07:51.469 align:start position:0%
balancing the autonomy of the individual
agents<00:07:49.159><c> with</c><00:07:49.400><c> the</c><00:07:49.759><c> collective</c><00:07:50.400><c> goal</c><00:07:50.720><c> of</c><00:07:50.919><c> the</c>

00:07:51.469 --> 00:07:51.479 align:start position:0%
agents with the collective goal of the
 

00:07:51.479 --> 00:07:54.230 align:start position:0%
agents with the collective goal of the
multi-agent<00:07:52.479><c> system</c><00:07:53.199><c> so</c><00:07:53.479><c> we</c><00:07:53.639><c> have</c><00:07:53.800><c> to</c><00:07:54.039><c> make</c>

00:07:54.230 --> 00:07:54.240 align:start position:0%
multi-agent system so we have to make
 

00:07:54.240 --> 00:07:57.390 align:start position:0%
multi-agent system so we have to make
sure<00:07:54.680><c> that</c><00:07:54.840><c> all</c><00:07:55.039><c> the</c><00:07:55.360><c> individual</c><00:07:56.159><c> agents</c><00:07:56.680><c> are</c>

00:07:57.390 --> 00:07:57.400 align:start position:0%
sure that all the individual agents are
 

00:07:57.400 --> 00:08:01.189 align:start position:0%
sure that all the individual agents are
working<00:07:57.879><c> in</c><00:07:58.000><c> a</c><00:07:58.280><c> way</c><00:07:58.879><c> that</c><00:07:59.520><c> you</c><00:07:59.960><c> achieve</c><00:08:00.560><c> the</c>

00:08:01.189 --> 00:08:01.199 align:start position:0%
working in a way that you achieve the
 

00:08:01.199 --> 00:08:05.710 align:start position:0%
working in a way that you achieve the
desired

00:08:05.710 --> 00:08:05.720 align:start position:0%
 
 

00:08:05.720 --> 00:08:10.710 align:start position:0%
 
outcome<00:08:06.720><c> here</c><00:08:07.560><c> um</c><00:08:08.560><c> is</c><00:08:09.000><c> so</c><00:08:09.319><c> we</c><00:08:09.440><c> will</c><00:08:09.879><c> move</c><00:08:10.159><c> to</c>

00:08:10.710 --> 00:08:10.720 align:start position:0%
outcome here um is so we will move to
 

00:08:10.720 --> 00:08:12.990 align:start position:0%
outcome here um is so we will move to
section<00:08:11.159><c> two</c><00:08:11.680><c> which</c><00:08:11.800><c> is</c><00:08:12.120><c> application</c><00:08:12.680><c> of</c>

00:08:12.990 --> 00:08:13.000 align:start position:0%
section two which is application of
 

00:08:13.000 --> 00:08:15.270 align:start position:0%
section two which is application of
multi-agent<00:08:14.000><c> systems</c><00:08:14.520><c> in</c>

00:08:15.270 --> 00:08:15.280 align:start position:0%
multi-agent systems in
 

00:08:15.280 --> 00:08:17.110 align:start position:0%
multi-agent systems in
healthcare

00:08:17.110 --> 00:08:17.120 align:start position:0%
healthcare
 

00:08:17.120 --> 00:08:21.149 align:start position:0%
healthcare
so<00:08:18.120><c> one</c><00:08:18.280><c> of</c><00:08:18.440><c> the</c><00:08:18.599><c> use</c><00:08:18.960><c> case</c><00:08:19.240><c> is</c><00:08:20.159><c> enhancing</c>

00:08:21.149 --> 00:08:21.159 align:start position:0%
so one of the use case is enhancing
 

00:08:21.159 --> 00:08:25.309 align:start position:0%
so one of the use case is enhancing
doctor<00:08:21.879><c> patient</c><00:08:22.720><c> interactions</c><00:08:23.879><c> so</c><00:08:24.879><c> we</c><00:08:25.080><c> can</c>

00:08:25.309 --> 00:08:25.319 align:start position:0%
doctor patient interactions so we can
 

00:08:25.319 --> 00:08:27.990 align:start position:0%
doctor patient interactions so we can
use<00:08:25.879><c> natural</c><00:08:26.479><c> language</c>

00:08:27.990 --> 00:08:28.000 align:start position:0%
use natural language
 

00:08:28.000 --> 00:08:31.990 align:start position:0%
use natural language
processing<00:08:29.000><c> with</c><00:08:29.240><c> our</c><00:08:29.639><c> llm</c><00:08:30.319><c> slm</c><00:08:30.879><c> based</c><00:08:31.319><c> agents</c>

00:08:31.990 --> 00:08:32.000 align:start position:0%
processing with our llm slm based agents
 

00:08:32.000 --> 00:08:33.310 align:start position:0%
processing with our llm slm based agents
which<00:08:32.240><c> can</c>

00:08:33.310 --> 00:08:33.320 align:start position:0%
which can
 

00:08:33.320 --> 00:08:36.949 align:start position:0%
which can
facilate<00:08:34.440><c> natural</c><00:08:35.440><c> language</c><00:08:36.120><c> processing</c><00:08:36.719><c> to</c>

00:08:36.949 --> 00:08:36.959 align:start position:0%
facilate natural language processing to
 

00:08:36.959 --> 00:08:39.269 align:start position:0%
facilate natural language processing to
improve<00:08:37.560><c> doctor</c><00:08:38.039><c> patient</c><00:08:38.519><c> interactions</c>

00:08:39.269 --> 00:08:39.279 align:start position:0%
improve doctor patient interactions
 

00:08:39.279 --> 00:08:43.990 align:start position:0%
improve doctor patient interactions
enabling<00:08:39.880><c> more</c><00:08:40.560><c> efficient</c><00:08:41.080><c> and</c><00:08:41.880><c> accurate</c>

00:08:43.990 --> 00:08:44.000 align:start position:0%
enabling more efficient and accurate
 

00:08:44.000 --> 00:08:46.870 align:start position:0%
enabling more efficient and accurate
communication<00:08:45.000><c> so</c><00:08:45.480><c> one</c><00:08:45.640><c> of</c><00:08:45.800><c> the</c><00:08:45.959><c> use</c><00:08:46.279><c> case</c><00:08:46.519><c> is</c>

00:08:46.870 --> 00:08:46.880 align:start position:0%
communication so one of the use case is
 

00:08:46.880 --> 00:08:51.030 align:start position:0%
communication so one of the use case is
Medical<00:08:47.839><c> Data</c><00:08:48.800><c> analysis</c><00:08:49.800><c> we</c><00:08:49.920><c> are</c><00:08:50.240><c> utilizing</c>

00:08:51.030 --> 00:08:51.040 align:start position:0%
Medical Data analysis we are utilizing
 

00:08:51.040 --> 00:08:54.829 align:start position:0%
Medical Data analysis we are utilizing
llm<00:08:51.600><c> for</c><00:08:51.920><c> analyzing</c><00:08:52.760><c> medical</c><00:08:53.760><c> data</c><00:08:54.480><c> which</c><00:08:54.680><c> in</c>

00:08:54.829 --> 00:08:54.839 align:start position:0%
llm for analyzing medical data which in
 

00:08:54.839 --> 00:08:58.110 align:start position:0%
llm for analyzing medical data which in
turn<00:08:55.240><c> can</c><00:08:55.560><c> assist</c><00:08:56.360><c> doctors</c><00:08:56.880><c> in</c><00:08:57.360><c> diagnosing</c>

00:08:58.110 --> 00:08:58.120 align:start position:0%
turn can assist doctors in diagnosing
 

00:08:58.120 --> 00:09:01.870 align:start position:0%
turn can assist doctors in diagnosing
and<00:08:58.920><c> treating</c><00:08:59.760><c> patients</c><00:09:00.720><c> and</c><00:09:01.079><c> leading</c><00:09:01.440><c> to</c><00:09:01.680><c> an</c>

00:09:01.870 --> 00:09:01.880 align:start position:0%
and treating patients and leading to an
 

00:09:01.880 --> 00:09:03.750 align:start position:0%
and treating patients and leading to an
enhanced<00:09:02.440><c> Healthcare</c>

00:09:03.750 --> 00:09:03.760 align:start position:0%
enhanced Healthcare
 

00:09:03.760 --> 00:09:06.670 align:start position:0%
enhanced Healthcare
outcomes<00:09:04.760><c> uh</c><00:09:04.880><c> we</c><00:09:05.000><c> are</c><00:09:05.240><c> also</c><00:09:05.640><c> using</c><00:09:06.240><c> the</c>

00:09:06.670 --> 00:09:06.680 align:start position:0%
outcomes uh we are also using the
 

00:09:06.680 --> 00:09:11.190 align:start position:0%
outcomes uh we are also using the
chatbot<00:09:07.440><c> from</c><00:09:07.920><c> John</c><00:09:08.399><c> Snow</c><00:09:09.399><c> lab</c><00:09:09.959><c> on</c><00:09:10.360><c> and</c><00:09:10.560><c> oci</c>

00:09:11.190 --> 00:09:11.200 align:start position:0%
chatbot from John Snow lab on and oci
 

00:09:11.200 --> 00:09:16.190 align:start position:0%
chatbot from John Snow lab on and oci
also<00:09:11.600><c> had</c><00:09:11.880><c> its</c><00:09:12.480><c> own</c><00:09:13.120><c> Oda</c><00:09:14.240><c> Oracle</c><00:09:15.240><c> digital</c>

00:09:16.190 --> 00:09:16.200 align:start position:0%
also had its own Oda Oracle digital
 

00:09:16.200 --> 00:09:19.670 align:start position:0%
also had its own Oda Oracle digital
assistant<00:09:17.200><c> and</c><00:09:17.440><c> we</c><00:09:17.720><c> actually</c><00:09:18.560><c> fine-tune</c><00:09:19.320><c> them</c>

00:09:19.670 --> 00:09:19.680 align:start position:0%
assistant and we actually fine-tune them
 

00:09:19.680 --> 00:09:23.509 align:start position:0%
assistant and we actually fine-tune them
on<00:09:20.160><c> medical</c><00:09:20.880><c> data</c><00:09:21.320><c> and</c><00:09:21.519><c> use</c><00:09:21.880><c> them</c><00:09:22.120><c> as</c><00:09:22.519><c> virtual</c>

00:09:23.509 --> 00:09:23.519 align:start position:0%
on medical data and use them as virtual
 

00:09:23.519 --> 00:09:26.949 align:start position:0%
on medical data and use them as virtual
Health<00:09:24.320><c> assistance</c><00:09:25.000><c> and</c><00:09:25.200><c> then</c><00:09:25.959><c> integrating</c>

00:09:26.949 --> 00:09:26.959 align:start position:0%
Health assistance and then integrating
 

00:09:26.959 --> 00:09:30.269 align:start position:0%
Health assistance and then integrating
them<00:09:27.720><c> with</c><00:09:28.079><c> patient</c><00:09:28.560><c> care</c><00:09:29.040><c> system</c><00:09:29.360><c> system</c><00:09:29.800><c> can</c>

00:09:30.269 --> 00:09:30.279 align:start position:0%
them with patient care system system can
 

00:09:30.279 --> 00:09:33.110 align:start position:0%
them with patient care system system can
provide<00:09:30.880><c> patients</c><00:09:31.440><c> with</c><00:09:32.120><c> personalized</c>

00:09:33.110 --> 00:09:33.120 align:start position:0%
provide patients with personalized
 

00:09:33.120 --> 00:09:38.910 align:start position:0%
provide patients with personalized
healthare<00:09:33.720><c> guidance</c><00:09:34.519><c> and</c>

00:09:38.910 --> 00:09:38.920 align:start position:0%
 
 

00:09:38.920 --> 00:09:42.509 align:start position:0%
 
support<00:09:39.920><c> the</c><00:09:40.240><c> Second</c><00:09:40.600><c> Use</c><00:09:40.959><c> case</c><00:09:41.200><c> is</c><00:09:41.680><c> medical</c>

00:09:42.509 --> 00:09:42.519 align:start position:0%
support the Second Use case is medical
 

00:09:42.519 --> 00:09:45.829 align:start position:0%
support the Second Use case is medical
research<00:09:43.000><c> and</c><00:09:43.680><c> knowledge</c><00:09:44.600><c> generation</c><00:09:45.600><c> we</c>

00:09:45.829 --> 00:09:45.839 align:start position:0%
research and knowledge generation we
 

00:09:45.839 --> 00:09:51.150 align:start position:0%
research and knowledge generation we
have<00:09:46.399><c> used</c><00:09:47.000><c> JSL</c><00:09:47.880><c> MMX</c><00:09:48.800><c> to</c><00:09:49.480><c> do</c><00:09:50.160><c> literature</c>

00:09:51.150 --> 00:09:51.160 align:start position:0%
have used JSL MMX to do literature
 

00:09:51.160 --> 00:09:53.750 align:start position:0%
have used JSL MMX to do literature
review<00:09:52.120><c> so</c><00:09:52.440><c> these</c><00:09:52.640><c> agents</c><00:09:53.079><c> can</c><00:09:53.360><c> actually</c>

00:09:53.750 --> 00:09:53.760 align:start position:0%
review so these agents can actually
 

00:09:53.760 --> 00:09:56.470 align:start position:0%
review so these agents can actually
assist<00:09:54.360><c> doctors</c><00:09:54.959><c> in</c><00:09:55.440><c> conducting</c>

00:09:56.470 --> 00:09:56.480 align:start position:0%
assist doctors in conducting
 

00:09:56.480 --> 00:09:59.150 align:start position:0%
assist doctors in conducting
comprehensive<00:09:57.480><c> literature</c><00:09:58.240><c> reviews</c><00:09:58.720><c> to</c><00:09:58.920><c> stay</c>

00:09:59.150 --> 00:09:59.160 align:start position:0%
comprehensive literature reviews to stay
 

00:09:59.160 --> 00:10:02.069 align:start position:0%
comprehensive literature reviews to stay
up<00:09:59.399><c> updated</c><00:10:00.279><c> with</c><00:10:00.480><c> the</c><00:10:00.839><c> latest</c><00:10:01.440><c> medical</c>

00:10:02.069 --> 00:10:02.079 align:start position:0%
up updated with the latest medical
 

00:10:02.079 --> 00:10:03.470 align:start position:0%
up updated with the latest medical
research<00:10:02.519><c> and</c>

00:10:03.470 --> 00:10:03.480 align:start position:0%
research and
 

00:10:03.480 --> 00:10:07.509 align:start position:0%
research and
advancement<00:10:04.480><c> then</c><00:10:05.040><c> we</c><00:10:05.200><c> can</c><00:10:05.480><c> use</c><00:10:05.920><c> these</c><00:10:06.800><c> llms</c>

00:10:07.509 --> 00:10:07.519 align:start position:0%
advancement then we can use these llms
 

00:10:07.519 --> 00:10:11.350 align:start position:0%
advancement then we can use these llms
for<00:10:07.880><c> generating</c><00:10:08.839><c> medical</c><00:10:09.760><c> content</c><00:10:10.760><c> which</c><00:10:10.959><c> can</c>

00:10:11.350 --> 00:10:11.360 align:start position:0%
for generating medical content which can
 

00:10:11.360 --> 00:10:15.230 align:start position:0%
for generating medical content which can
Aid<00:10:11.800><c> in</c><00:10:12.240><c> creating</c><00:10:13.200><c> educational</c><00:10:14.240><c> materials</c>

00:10:15.230 --> 00:10:15.240 align:start position:0%
Aid in creating educational materials
 

00:10:15.240 --> 00:10:18.030 align:start position:0%
Aid in creating educational materials
research<00:10:15.760><c> paper</c><00:10:16.120><c> and</c><00:10:16.560><c> patient</c>

00:10:18.030 --> 00:10:18.040 align:start position:0%
research paper and patient
 

00:10:18.040 --> 00:10:23.590 align:start position:0%
research paper and patient
resources<00:10:19.519><c> and</c><00:10:20.959><c> lastly</c><00:10:21.959><c> uh</c><00:10:22.120><c> with</c><00:10:22.480><c> John</c><00:10:22.920><c> SN</c>

00:10:23.590 --> 00:10:23.600 align:start position:0%
resources and lastly uh with John SN
 

00:10:23.600 --> 00:10:27.069 align:start position:0%
resources and lastly uh with John SN
Labs<00:10:24.079><c> we</c><00:10:24.240><c> have</c><00:10:24.399><c> seen</c><00:10:24.880><c> that</c><00:10:25.120><c> how</c><00:10:25.880><c> these</c><00:10:26.200><c> llms</c>

00:10:27.069 --> 00:10:27.079 align:start position:0%
Labs we have seen that how these llms
 

00:10:27.079 --> 00:10:29.829 align:start position:0%
Labs we have seen that how these llms
can<00:10:27.240><c> be</c><00:10:27.480><c> integrated</c><00:10:28.440><c> into</c><00:10:28.959><c> Clinic</c><00:10:29.320><c> iCal</c>

00:10:29.829 --> 00:10:29.839 align:start position:0%
can be integrated into Clinic iCal
 

00:10:29.839 --> 00:10:32.670 align:start position:0%
can be integrated into Clinic iCal
decision<00:10:30.640><c> support</c><00:10:31.160><c> systems</c><00:10:32.160><c> which</c><00:10:32.360><c> can</c>

00:10:32.670 --> 00:10:32.680 align:start position:0%
decision support systems which can
 

00:10:32.680 --> 00:10:35.509 align:start position:0%
decision support systems which can
provide<00:10:33.320><c> doctors</c><00:10:33.959><c> with</c><00:10:34.519><c> evidence-based</c>

00:10:35.509 --> 00:10:35.519 align:start position:0%
provide doctors with evidence-based
 

00:10:35.519 --> 00:10:42.230 align:start position:0%
provide doctors with evidence-based
recommendations<00:10:36.519><c> for</c><00:10:36.880><c> their</c>

00:10:42.230 --> 00:10:42.240 align:start position:0%
 
 

00:10:42.240 --> 00:10:45.470 align:start position:0%
 
patients<00:10:43.279><c> another</c>

00:10:45.470 --> 00:10:45.480 align:start position:0%
patients another
 

00:10:45.480 --> 00:10:49.550 align:start position:0%
patients another
uh<00:10:46.480><c> way</c><00:10:46.880><c> we</c><00:10:47.360><c> use</c><00:10:47.959><c> them</c><00:10:48.320><c> in</c><00:10:48.519><c> our</c><00:10:48.839><c> Healthcare</c>

00:10:49.550 --> 00:10:49.560 align:start position:0%
uh way we use them in our Healthcare
 

00:10:49.560 --> 00:10:51.110 align:start position:0%
uh way we use them in our Healthcare
operations<00:10:50.160><c> and</c>

00:10:51.110 --> 00:10:51.120 align:start position:0%
operations and
 

00:10:51.120 --> 00:10:53.389 align:start position:0%
operations and
administrations<00:10:52.120><c> is</c><00:10:52.360><c> that</c><00:10:52.560><c> we</c><00:10:52.720><c> actually</c><00:10:53.040><c> use</c>

00:10:53.389 --> 00:10:53.399 align:start position:0%
administrations is that we actually use
 

00:10:53.399 --> 00:10:56.509 align:start position:0%
administrations is that we actually use
these<00:10:53.720><c> agents</c><00:10:54.160><c> to</c><00:10:54.399><c> Auto</c><00:10:55.040><c> to</c><00:10:55.360><c> automate</c>

00:10:56.509 --> 00:10:56.519 align:start position:0%
these agents to Auto to automate
 

00:10:56.519 --> 00:10:59.870 align:start position:0%
these agents to Auto to automate
workflow<00:10:57.519><c> so</c><00:10:58.079><c> these</c><00:10:58.320><c> agents</c><00:10:58.800><c> can</c><00:10:59.279><c> automate</c>

00:10:59.870 --> 00:10:59.880 align:start position:0%
workflow so these agents can automate
 

00:10:59.880 --> 00:11:03.430 align:start position:0%
workflow so these agents can automate
administrative<00:11:00.760><c> tasks</c><00:11:01.720><c> such</c><00:11:02.000><c> as</c><00:11:02.720><c> appointment</c>

00:11:03.430 --> 00:11:03.440 align:start position:0%
administrative tasks such as appointment
 

00:11:03.440 --> 00:11:07.110 align:start position:0%
administrative tasks such as appointment
scheduling<00:11:04.440><c> medical</c><00:11:04.959><c> record</c><00:11:06.120><c> management</c>

00:11:07.110 --> 00:11:07.120 align:start position:0%
scheduling medical record management
 

00:11:07.120 --> 00:11:09.790 align:start position:0%
scheduling medical record management
billing<00:11:07.680><c> processes</c><00:11:08.279><c> and</c><00:11:08.760><c> streamlining</c>

00:11:09.790 --> 00:11:09.800 align:start position:0%
billing processes and streamlining
 

00:11:09.800 --> 00:11:12.150 align:start position:0%
billing processes and streamlining
Healthcare

00:11:12.150 --> 00:11:12.160 align:start position:0%
Healthcare
 

00:11:12.160 --> 00:11:15.750 align:start position:0%
Healthcare
operations<00:11:13.160><c> we</c><00:11:13.399><c> also</c><00:11:13.839><c> have</c><00:11:14.279><c> a</c><00:11:14.519><c> resource</c>

00:11:15.750 --> 00:11:15.760 align:start position:0%
operations we also have a resource
 

00:11:15.760 --> 00:11:20.590 align:start position:0%
operations we also have a resource
allocation<00:11:17.120><c> this</c><00:11:18.120><c> by</c><00:11:18.399><c> utilizing</c><00:11:19.160><c> this</c>

00:11:20.590 --> 00:11:20.600 align:start position:0%
allocation this by utilizing this
 

00:11:20.600 --> 00:11:24.590 align:start position:0%
allocation this by utilizing this
llms<00:11:21.600><c> uh</c><00:11:22.120><c> uh</c><00:11:22.279><c> they</c><00:11:22.440><c> can</c><00:11:22.680><c> actually</c><00:11:23.600><c> do</c>

00:11:24.590 --> 00:11:24.600 align:start position:0%
llms uh uh they can actually do
 

00:11:24.600 --> 00:11:27.990 align:start position:0%
llms uh uh they can actually do
Predictive<00:11:25.600><c> Analytics</c><00:11:26.440><c> and</c><00:11:26.680><c> can</c><00:11:27.000><c> optimize</c><00:11:27.800><c> a</c>

00:11:27.990 --> 00:11:28.000 align:start position:0%
Predictive Analytics and can optimize a
 

00:11:28.000 --> 00:11:29.829 align:start position:0%
Predictive Analytics and can optimize a
resource<00:11:28.519><c> allocation</c><00:11:29.079><c> with</c><00:11:29.320><c> within</c><00:11:29.600><c> the</c>

00:11:29.829 --> 00:11:29.839 align:start position:0%
resource allocation with within the
 

00:11:29.839 --> 00:11:31.150 align:start position:0%
resource allocation with within the
healthcare

00:11:31.150 --> 00:11:31.160 align:start position:0%
healthcare
 

00:11:31.160 --> 00:11:35.269 align:start position:0%
healthcare
facilities<00:11:32.160><c> and</c><00:11:33.040><c> thus</c><00:11:33.560><c> improving</c><00:11:34.560><c> efficiency</c>

00:11:35.269 --> 00:11:35.279 align:start position:0%
facilities and thus improving efficiency
 

00:11:35.279 --> 00:11:37.870 align:start position:0%
facilities and thus improving efficiency
and<00:11:35.959><c> cost</c>

00:11:37.870 --> 00:11:37.880 align:start position:0%
and cost
 

00:11:37.880 --> 00:11:41.990 align:start position:0%
and cost
Effectiveness<00:11:38.920><c> and</c><00:11:39.920><c> lastly</c><00:11:40.519><c> we</c><00:11:40.720><c> have</c><00:11:41.000><c> used</c>

00:11:41.990 --> 00:11:42.000 align:start position:0%
Effectiveness and lastly we have used
 

00:11:42.000 --> 00:11:44.790 align:start position:0%
Effectiveness and lastly we have used
large<00:11:42.399><c> language</c><00:11:43.240><c> models</c><00:11:43.800><c> in</c><00:11:43.959><c> the</c><00:11:44.160><c> field</c><00:11:44.480><c> of</c>

00:11:44.790 --> 00:11:44.800 align:start position:0%
large language models in the field of
 

00:11:44.800 --> 00:11:48.110 align:start position:0%
large language models in the field of
Regulatory<00:11:46.079><c> Compliance</c><00:11:47.079><c> these</c><00:11:47.320><c> agents</c><00:11:47.800><c> can</c>

00:11:48.110 --> 00:11:48.120 align:start position:0%
Regulatory Compliance these agents can
 

00:11:48.120 --> 00:11:51.949 align:start position:0%
Regulatory Compliance these agents can
assist<00:11:48.480><c> in</c><00:11:49.000><c> ensuring</c><00:11:50.320><c> Regulatory</c><00:11:51.320><c> Compliance</c>

00:11:51.949 --> 00:11:51.959 align:start position:0%
assist in ensuring Regulatory Compliance
 

00:11:51.959 --> 00:11:54.550 align:start position:0%
assist in ensuring Regulatory Compliance
by<00:11:52.200><c> analyzing</c><00:11:52.800><c> Healthcare</c><00:11:53.519><c> policies</c><00:11:54.079><c> and</c>

00:11:54.550 --> 00:11:54.560 align:start position:0%
by analyzing Healthcare policies and
 

00:11:54.560 --> 00:12:00.790 align:start position:0%
by analyzing Healthcare policies and
guidelines<00:11:55.560><c> to</c><00:11:56.160><c> support</c><00:11:57.040><c> decision</c><00:11:57.800><c> making</c>

00:12:00.790 --> 00:12:00.800 align:start position:0%
guidelines to support decision making
 

00:12:00.800 --> 00:12:03.990 align:start position:0%
guidelines to support decision making
now<00:12:01.240><c> moving</c><00:12:01.639><c> to</c><00:12:02.240><c> section</c><00:12:02.760><c> three</c><00:12:03.279><c> we</c><00:12:03.399><c> will</c><00:12:03.639><c> see</c>

00:12:03.990 --> 00:12:04.000 align:start position:0%
now moving to section three we will see
 

00:12:04.000 --> 00:12:06.069 align:start position:0%
now moving to section three we will see
what<00:12:04.120><c> are</c><00:12:04.360><c> some</c><00:12:04.519><c> of</c><00:12:04.720><c> the</c><00:12:05.160><c> benefits</c><00:12:05.720><c> and</c>

00:12:06.069 --> 00:12:06.079 align:start position:0%
what are some of the benefits and
 

00:12:06.079 --> 00:12:12.389 align:start position:0%
what are some of the benefits and
challenges<00:12:06.680><c> of</c><00:12:06.839><c> a</c><00:12:07.720><c> multi-agent</c><00:12:08.720><c> system</c><00:12:09.160><c> in</c>

00:12:12.389 --> 00:12:12.399 align:start position:0%
 
 

00:12:12.399 --> 00:12:16.509 align:start position:0%
 
healthcare<00:12:13.399><c> so</c><00:12:14.120><c> some</c><00:12:14.360><c> of</c><00:12:14.560><c> the</c><00:12:15.000><c> advantages</c><00:12:15.800><c> of</c>

00:12:16.509 --> 00:12:16.519 align:start position:0%
healthcare so some of the advantages of
 

00:12:16.519 --> 00:12:19.069 align:start position:0%
healthcare so some of the advantages of
agentic<00:12:17.399><c> workflow</c><00:12:17.959><c> in</c><00:12:18.199><c> healthcare</c><00:12:18.760><c> are</c>

00:12:19.069 --> 00:12:19.079 align:start position:0%
agentic workflow in healthcare are
 

00:12:19.079 --> 00:12:22.550 align:start position:0%
agentic workflow in healthcare are
enhanced<00:12:20.320><c> efficiency</c><00:12:21.320><c> the</c><00:12:21.600><c> use</c><00:12:21.959><c> of</c><00:12:22.279><c> these</c>

00:12:22.550 --> 00:12:22.560 align:start position:0%
enhanced efficiency the use of these
 

00:12:22.560 --> 00:12:24.990 align:start position:0%
enhanced efficiency the use of these
agents<00:12:23.199><c> in</c><00:12:23.399><c> healthcare</c><00:12:23.959><c> can</c><00:12:24.320><c> lead</c><00:12:24.600><c> to</c>

00:12:24.990 --> 00:12:25.000 align:start position:0%
agents in healthcare can lead to
 

00:12:25.000 --> 00:12:28.590 align:start position:0%
agents in healthcare can lead to
improved<00:12:25.760><c> operational</c><00:12:27.040><c> efficiency</c><00:12:28.040><c> reduced</c>

00:12:28.590 --> 00:12:28.600 align:start position:0%
improved operational efficiency reduced
 

00:12:28.600 --> 00:12:31.829 align:start position:0%
improved operational efficiency reduced
Administration<00:12:29.639><c> burden</c><00:12:30.600><c> and</c><00:12:31.040><c> faster</c>

00:12:31.829 --> 00:12:31.839 align:start position:0%
Administration burden and faster
 

00:12:31.839 --> 00:12:34.389 align:start position:0%
Administration burden and faster
decision<00:12:32.320><c> making</c>

00:12:34.389 --> 00:12:34.399 align:start position:0%
decision making
 

00:12:34.399 --> 00:12:36.870 align:start position:0%
decision making
processes<00:12:35.399><c> another</c><00:12:35.800><c> Advantage</c><00:12:36.399><c> is</c>

00:12:36.870 --> 00:12:36.880 align:start position:0%
processes another Advantage is
 

00:12:36.880 --> 00:12:41.110 align:start position:0%
processes another Advantage is
personalized<00:12:37.880><c> care</c><00:12:38.839><c> we</c><00:12:39.000><c> can</c><00:12:39.519><c> leverage</c><00:12:40.480><c> llms</c>

00:12:41.110 --> 00:12:41.120 align:start position:0%
personalized care we can leverage llms
 

00:12:41.120 --> 00:12:44.750 align:start position:0%
personalized care we can leverage llms
for<00:12:42.000><c> patient</c><00:12:42.560><c> data</c><00:12:43.079><c> analysis</c><00:12:44.040><c> Health</c><00:12:44.360><c> Care</c>

00:12:44.750 --> 00:12:44.760 align:start position:0%
for patient data analysis Health Care
 

00:12:44.760 --> 00:12:47.430 align:start position:0%
for patient data analysis Health Care
Providers<00:12:45.440><c> can</c><00:12:45.839><c> deliver</c><00:12:46.480><c> personalized</c>

00:12:47.430 --> 00:12:47.440 align:start position:0%
Providers can deliver personalized
 

00:12:47.440 --> 00:12:51.030 align:start position:0%
Providers can deliver personalized
treatment<00:12:48.079><c> plans</c><00:12:49.079><c> and</c><00:12:49.519><c> interventions</c><00:12:50.519><c> tailor</c>

00:12:51.030 --> 00:12:51.040 align:start position:0%
treatment plans and interventions tailor
 

00:12:51.040 --> 00:12:54.150 align:start position:0%
treatment plans and interventions tailor
to<00:12:51.360><c> individual</c><00:12:52.199><c> patient</c><00:12:52.639><c> needs</c><00:12:53.600><c> and</c><00:12:53.800><c> then</c><00:12:54.000><c> of</c>

00:12:54.150 --> 00:12:54.160 align:start position:0%
to individual patient needs and then of
 

00:12:54.160 --> 00:12:58.189 align:start position:0%
to individual patient needs and then of
course<00:12:54.800><c> the</c><00:12:55.800><c> knowledge</c><00:12:56.760><c> expansion</c><00:12:57.760><c> Alti</c>

00:12:58.189 --> 00:12:58.199 align:start position:0%
course the knowledge expansion Alti
 

00:12:58.199 --> 00:13:01.590 align:start position:0%
course the knowledge expansion Alti
agent<00:12:58.720><c> system</c><00:12:59.440><c> enable</c><00:13:00.040><c> continuous</c><00:13:01.000><c> learning</c>

00:13:01.590 --> 00:13:01.600 align:start position:0%
agent system enable continuous learning
 

00:13:01.600 --> 00:13:04.189 align:start position:0%
agent system enable continuous learning
and<00:13:02.040><c> knowledge</c><00:13:02.639><c> expansion</c><00:13:03.639><c> within</c><00:13:03.959><c> the</c>

00:13:04.189 --> 00:13:04.199 align:start position:0%
and knowledge expansion within the
 

00:13:04.199 --> 00:13:06.829 align:start position:0%
and knowledge expansion within the
healthcare<00:13:04.880><c> domain</c><00:13:05.680><c> contributing</c><00:13:06.399><c> to</c>

00:13:06.829 --> 00:13:06.839 align:start position:0%
healthcare domain contributing to
 

00:13:06.839 --> 00:13:08.629 align:start position:0%
healthcare domain contributing to
ongoing

00:13:08.629 --> 00:13:08.639 align:start position:0%
ongoing
 

00:13:08.639 --> 00:13:11.629 align:start position:0%
ongoing
advancement<00:13:09.639><c> some</c><00:13:09.880><c> of</c><00:13:10.079><c> the</c>

00:13:11.629 --> 00:13:11.639 align:start position:0%
advancement some of the
 

00:13:11.639 --> 00:13:15.430 align:start position:0%
advancement some of the
challenges<00:13:12.639><c> and</c><00:13:13.240><c> ethical</c><00:13:14.440><c> considerations</c>

00:13:15.430 --> 00:13:15.440 align:start position:0%
challenges and ethical considerations
 

00:13:15.440 --> 00:13:19.110 align:start position:0%
challenges and ethical considerations
are<00:13:15.720><c> around</c><00:13:16.279><c> data</c><00:13:16.680><c> privacy</c><00:13:17.160><c> and</c><00:13:17.920><c> security</c><00:13:18.920><c> so</c>

00:13:19.110 --> 00:13:19.120 align:start position:0%
are around data privacy and security so
 

00:13:19.120 --> 00:13:22.949 align:start position:0%
are around data privacy and security so
integrating<00:13:19.800><c> llm</c><00:13:20.360><c> based</c><00:13:20.880><c> agents</c><00:13:21.959><c> requires</c>

00:13:22.949 --> 00:13:22.959 align:start position:0%
integrating llm based agents requires
 

00:13:22.959 --> 00:13:25.990 align:start position:0%
integrating llm based agents requires
robust<00:13:23.959><c> data</c><00:13:24.360><c> privacy</c><00:13:24.920><c> and</c><00:13:25.320><c> security</c>

00:13:25.990 --> 00:13:26.000 align:start position:0%
robust data privacy and security
 

00:13:26.000 --> 00:13:29.430 align:start position:0%
robust data privacy and security
measures<00:13:26.920><c> to</c><00:13:27.320><c> safeguard</c><00:13:28.120><c> sensitive</c><00:13:29.120><c> patient</c>

00:13:29.430 --> 00:13:29.440 align:start position:0%
measures to safeguard sensitive patient
 

00:13:29.440 --> 00:13:33.030 align:start position:0%
measures to safeguard sensitive patient
information<00:13:30.240><c> and</c><00:13:30.760><c> prevent</c><00:13:31.320><c> unauthorized</c>

00:13:33.030 --> 00:13:33.040 align:start position:0%
information and prevent unauthorized
 

00:13:33.040 --> 00:13:37.870 align:start position:0%
information and prevent unauthorized
access<00:13:34.480><c> uh</c><00:13:35.480><c> there</c><00:13:35.639><c> are</c><00:13:36.079><c> also</c><00:13:36.600><c> a</c><00:13:36.800><c> risks</c><00:13:37.279><c> around</c>

00:13:37.870 --> 00:13:37.880 align:start position:0%
access uh there are also a risks around
 

00:13:37.880 --> 00:13:41.350 align:start position:0%
access uh there are also a risks around
bias<00:13:38.240><c> and</c><00:13:38.839><c> fairness</c><00:13:39.839><c> addressing</c><00:13:40.680><c> potential</c>

00:13:41.350 --> 00:13:41.360 align:start position:0%
bias and fairness addressing potential
 

00:13:41.360 --> 00:13:45.110 align:start position:0%
bias and fairness addressing potential
biases<00:13:41.920><c> in</c><00:13:42.120><c> llm</c><00:13:42.760><c> generated</c><00:13:43.760><c> outputs</c><00:13:44.399><c> is</c>

00:13:45.110 --> 00:13:45.120 align:start position:0%
biases in llm generated outputs is
 

00:13:45.120 --> 00:13:48.030 align:start position:0%
biases in llm generated outputs is
crucial<00:13:45.720><c> to</c><00:13:45.920><c> ensure</c><00:13:46.399><c> fair</c><00:13:46.720><c> and</c><00:13:47.279><c> Equitable</c>

00:13:48.030 --> 00:13:48.040 align:start position:0%
crucial to ensure fair and Equitable
 

00:13:48.040 --> 00:13:51.030 align:start position:0%
crucial to ensure fair and Equitable
Health<00:13:48.360><c> Care</c><00:13:48.519><c> outcomes</c><00:13:49.120><c> for</c><00:13:49.680><c> diverse</c><00:13:50.440><c> patient</c>

00:13:51.030 --> 00:13:51.040 align:start position:0%
Health Care outcomes for diverse patient
 

00:13:51.040 --> 00:13:53.829 align:start position:0%
Health Care outcomes for diverse patient
populations<00:13:52.040><c> and</c><00:13:52.279><c> also</c><00:13:52.759><c> adhering</c><00:13:53.399><c> to</c>

00:13:53.829 --> 00:13:53.839 align:start position:0%
populations and also adhering to
 

00:13:53.839 --> 00:13:57.470 align:start position:0%
populations and also adhering to
healthcare<00:13:54.839><c> regulations</c><00:13:55.560><c> and</c><00:13:56.560><c> ethical</c>

00:13:57.470 --> 00:13:57.480 align:start position:0%
healthcare regulations and ethical
 

00:13:57.480 --> 00:14:00.550 align:start position:0%
healthcare regulations and ethical
guidelines<00:13:58.480><c> when</c><00:13:59.120><c> deploying</c><00:13:59.560><c> llm</c><00:14:00.079><c> based</c>

00:14:00.550 --> 00:14:00.560 align:start position:0%
guidelines when deploying llm based
 

00:14:00.560 --> 00:14:04.670 align:start position:0%
guidelines when deploying llm based
agents<00:14:01.560><c> is</c><00:14:02.040><c> essential</c><00:14:02.680><c> to</c><00:14:03.560><c> maintain</c><00:14:04.120><c> the</c>

00:14:04.670 --> 00:14:04.680 align:start position:0%
agents is essential to maintain the
 

00:14:04.680 --> 00:14:08.430 align:start position:0%
agents is essential to maintain the
patient<00:14:05.680><c> uh</c><00:14:05.880><c> trust</c><00:14:06.279><c> and</c>

00:14:08.430 --> 00:14:08.440 align:start position:0%
patient uh trust and
 

00:14:08.440 --> 00:14:11.990 align:start position:0%
patient uh trust and
safety<00:14:09.440><c> so</c><00:14:09.720><c> some</c><00:14:09.959><c> of</c><00:14:10.160><c> the</c><00:14:10.600><c> future</c><00:14:11.160><c> outlook</c><00:14:11.600><c> and</c>

00:14:11.990 --> 00:14:12.000 align:start position:0%
safety so some of the future outlook and
 

00:14:12.000 --> 00:14:13.749 align:start position:0%
safety so some of the future outlook and
adoption

00:14:13.749 --> 00:14:13.759 align:start position:0%
adoption
 

00:14:13.759 --> 00:14:18.110 align:start position:0%
adoption
considerations<00:14:14.759><c> so</c><00:14:15.120><c> one</c><00:14:15.360><c> is</c><00:14:16.000><c> technological</c>

00:14:18.110 --> 00:14:18.120 align:start position:0%
considerations so one is technological
 

00:14:18.120 --> 00:14:21.590 align:start position:0%
considerations so one is technological
integration<00:14:19.120><c> uh</c><00:14:19.880><c> the</c><00:14:20.279><c> future</c><00:14:20.720><c> adaption</c><00:14:21.240><c> of</c>

00:14:21.590 --> 00:14:21.600 align:start position:0%
integration uh the future adaption of
 

00:14:21.600 --> 00:14:25.949 align:start position:0%
integration uh the future adaption of
multi-agent<00:14:22.399><c> agents</c><00:14:22.839><c> in</c><00:14:23.279><c> healthcare</c><00:14:24.199><c> will</c><00:14:25.199><c> uh</c>

00:14:25.949 --> 00:14:25.959 align:start position:0%
multi-agent agents in healthcare will uh
 

00:14:25.959 --> 00:14:28.269 align:start position:0%
multi-agent agents in healthcare will uh
involve<00:14:26.519><c> seamless</c><00:14:27.160><c> integration</c><00:14:28.000><c> with</c>

00:14:28.269 --> 00:14:28.279 align:start position:0%
involve seamless integration with
 

00:14:28.279 --> 00:14:30.470 align:start position:0%
involve seamless integration with
existing<00:14:28.680><c> healthare</c><00:14:29.360><c> systems</c><00:14:29.759><c> and</c>

00:14:30.470 --> 00:14:30.480 align:start position:0%
existing healthare systems and
 

00:14:30.480 --> 00:14:32.150 align:start position:0%
existing healthare systems and
interoperability

00:14:32.150 --> 00:14:32.160 align:start position:0%
interoperability
 

00:14:32.160 --> 00:14:35.150 align:start position:0%
interoperability
standards<00:14:33.160><c> we</c><00:14:33.360><c> have</c><00:14:33.519><c> to</c><00:14:34.000><c> consider</c><00:14:34.680><c> training</c>

00:14:35.150 --> 00:14:35.160 align:start position:0%
standards we have to consider training
 

00:14:35.160 --> 00:14:37.350 align:start position:0%
standards we have to consider training
and<00:14:35.639><c> education</c><00:14:36.320><c> how</c><00:14:36.560><c> Healthcare</c>

00:14:37.350 --> 00:14:37.360 align:start position:0%
and education how Healthcare
 

00:14:37.360 --> 00:14:40.269 align:start position:0%
and education how Healthcare
professionals<00:14:38.079><c> will</c><00:14:38.480><c> require</c><00:14:39.480><c> specialized</c>

00:14:40.269 --> 00:14:40.279 align:start position:0%
professionals will require specialized
 

00:14:40.279 --> 00:14:45.350 align:start position:0%
professionals will require specialized
training<00:14:40.800><c> and</c><00:14:41.560><c> education</c><00:14:42.560><c> and</c><00:14:43.600><c> lastly</c><00:14:44.600><c> and</c>

00:14:45.350 --> 00:14:45.360 align:start position:0%
training and education and lastly and
 

00:14:45.360 --> 00:14:48.710 align:start position:0%
training and education and lastly and
most<00:14:45.680><c> importantly</c><00:14:46.399><c> the</c><00:14:46.800><c> ethical</c><00:14:47.800><c> governance</c>

00:14:48.710 --> 00:14:48.720 align:start position:0%
most importantly the ethical governance
 

00:14:48.720 --> 00:14:51.230 align:start position:0%
most importantly the ethical governance
establishing<00:14:49.639><c> ethical</c><00:14:50.480><c> governance</c>

00:14:51.230 --> 00:14:51.240 align:start position:0%
establishing ethical governance
 

00:14:51.240 --> 00:14:54.310 align:start position:0%
establishing ethical governance
Frameworks<00:14:51.959><c> for</c><00:14:52.360><c> multi-</c><00:14:52.759><c> agent</c><00:14:53.360><c> llm</c><00:14:54.079><c> in</c>

00:14:54.310 --> 00:14:54.320 align:start position:0%
Frameworks for multi- agent llm in
 

00:14:54.320 --> 00:14:56.870 align:start position:0%
Frameworks for multi- agent llm in
healthcare<00:14:54.880><c> is</c>

00:14:56.870 --> 00:14:56.880 align:start position:0%
healthcare is
 

00:14:56.880 --> 00:15:00.150 align:start position:0%
healthcare is
imperative<00:14:57.880><c> next</c><00:14:58.160><c> section</c><00:14:58.600><c> is</c><00:14:59.360><c> leveraging</c>

00:15:00.150 --> 00:15:00.160 align:start position:0%
imperative next section is leveraging
 

00:15:00.160 --> 00:15:03.430 align:start position:0%
imperative next section is leveraging
Oracle<00:15:00.720><c> Cloud</c><00:15:01.240><c> infrastructure</c><00:15:02.240><c> oci</c><00:15:02.800><c> for</c>

00:15:03.430 --> 00:15:03.440 align:start position:0%
Oracle Cloud infrastructure oci for
 

00:15:03.440 --> 00:15:05.189 align:start position:0%
Oracle Cloud infrastructure oci for
agentic

00:15:05.189 --> 00:15:05.199 align:start position:0%
agentic
 

00:15:05.199 --> 00:15:09.230 align:start position:0%
agentic
workflow<00:15:06.199><c> so</c><00:15:06.519><c> here</c><00:15:06.680><c> is</c><00:15:06.839><c> the</c><00:15:07.399><c> agentic</c><00:15:08.399><c> workflow</c>

00:15:09.230 --> 00:15:09.240 align:start position:0%
workflow so here is the agentic workflow
 

00:15:09.240 --> 00:15:11.990 align:start position:0%
workflow so here is the agentic workflow
and<00:15:09.519><c> we</c><00:15:09.720><c> have</c><00:15:09.959><c> this</c><00:15:10.240><c> workflow</c><00:15:10.800><c> to</c><00:15:11.160><c> enable</c><00:15:11.839><c> a</c>

00:15:11.990 --> 00:15:12.000 align:start position:0%
and we have this workflow to enable a
 

00:15:12.000 --> 00:15:14.590 align:start position:0%
and we have this workflow to enable a
realtime<00:15:12.759><c> decision</c><00:15:13.199><c> making</c><00:15:13.560><c> and</c><00:15:13.800><c> strategic</c>

00:15:14.590 --> 00:15:14.600 align:start position:0%
realtime decision making and strategic
 

00:15:14.600 --> 00:15:17.509 align:start position:0%
realtime decision making and strategic
planning<00:15:15.560><c> as</c><00:15:15.720><c> well</c><00:15:15.959><c> as</c><00:15:16.519><c> creating</c>

00:15:17.509 --> 00:15:17.519 align:start position:0%
planning as well as creating
 

00:15:17.519 --> 00:15:21.470 align:start position:0%
planning as well as creating
summarization<00:15:18.440><c> from</c><00:15:19.199><c> notes</c><00:15:20.040><c> and</c><00:15:20.399><c> entity</c>

00:15:21.470 --> 00:15:21.480 align:start position:0%
summarization from notes and entity
 

00:15:21.480 --> 00:15:25.069 align:start position:0%
summarization from notes and entity
recognition<00:15:22.480><c> the</c><00:15:22.680><c> user</c><00:15:23.320><c> as</c><00:15:23.480><c> in</c><00:15:23.639><c> a</c><00:15:24.160><c> medical</c><00:15:24.800><c> or</c>

00:15:25.069 --> 00:15:25.079 align:start position:0%
recognition the user as in a medical or
 

00:15:25.079 --> 00:15:27.550 align:start position:0%
recognition the user as in a medical or
Healthcare<00:15:26.000><c> professional</c>

00:15:27.550 --> 00:15:27.560 align:start position:0%
Healthcare professional
 

00:15:27.560 --> 00:15:32.710 align:start position:0%
Healthcare professional
doctor<00:15:28.560><c> nurs</c><00:15:29.120><c> say</c><00:15:29.880><c> they</c><00:15:30.480><c> provide</c><00:15:31.079><c> a</c><00:15:31.880><c> natural</c>

00:15:32.710 --> 00:15:32.720 align:start position:0%
doctor nurs say they provide a natural
 

00:15:32.720 --> 00:15:35.590 align:start position:0%
doctor nurs say they provide a natural
language<00:15:33.240><c> based</c><00:15:33.800><c> input</c><00:15:34.720><c> which</c><00:15:35.040><c> goes</c><00:15:35.360><c> through</c>

00:15:35.590 --> 00:15:35.600 align:start position:0%
language based input which goes through
 

00:15:35.600 --> 00:15:39.470 align:start position:0%
language based input which goes through
a<00:15:35.839><c> small</c><00:15:36.720><c> language</c><00:15:37.480><c> model</c><00:15:38.279><c> which</c><00:15:38.759><c> moderate</c>

00:15:39.470 --> 00:15:39.480 align:start position:0%
a small language model which moderate
 

00:15:39.480 --> 00:15:42.350 align:start position:0%
a small language model which moderate
that<00:15:39.759><c> input</c><00:15:40.319><c> to</c><00:15:40.720><c> make</c><00:15:41.120><c> sure</c><00:15:41.880><c> that</c><00:15:42.000><c> it</c><00:15:42.160><c> is</c>

00:15:42.350 --> 00:15:42.360 align:start position:0%
that input to make sure that it is
 

00:15:42.360 --> 00:15:44.990 align:start position:0%
that input to make sure that it is
following<00:15:42.800><c> the</c><00:15:43.000><c> ethical</c><00:15:43.839><c> guidelines</c><00:15:44.399><c> and</c><00:15:44.600><c> the</c>

00:15:44.990 --> 00:15:45.000 align:start position:0%
following the ethical guidelines and the
 

00:15:45.000 --> 00:15:48.470 align:start position:0%
following the ethical guidelines and the
regulatory<00:15:45.839><c> guidelines</c><00:15:46.759><c> it</c><00:15:47.000><c> goes</c><00:15:47.319><c> to</c><00:15:47.519><c> a</c><00:15:48.079><c> large</c>

00:15:48.470 --> 00:15:48.480 align:start position:0%
regulatory guidelines it goes to a large
 

00:15:48.480 --> 00:15:52.350 align:start position:0%
regulatory guidelines it goes to a large
language<00:15:49.000><c> based</c><00:15:49.720><c> decision</c><00:15:50.720><c> router</c><00:15:51.519><c> for</c><00:15:51.839><c> us</c><00:15:52.199><c> we</c>

00:15:52.350 --> 00:15:52.360 align:start position:0%
language based decision router for us we
 

00:15:52.360 --> 00:15:57.030 align:start position:0%
language based decision router for us we
are<00:15:52.600><c> actually</c><00:15:53.560><c> using</c><00:15:54.560><c> um</c><00:15:55.519><c> slm</c><00:15:56.440><c> which</c><00:15:56.600><c> is</c>

00:15:57.030 --> 00:15:57.040 align:start position:0%
are actually using um slm which is
 

00:15:57.040 --> 00:16:01.309 align:start position:0%
are actually using um slm which is
Mistral<00:15:58.040><c> open</c><00:15:58.399><c> source</c><00:15:59.360><c> and</c><00:15:59.759><c> then</c><00:16:00.759><c> depending</c>

00:16:01.309 --> 00:16:01.319 align:start position:0%
Mistral open source and then depending
 

00:16:01.319 --> 00:16:05.629 align:start position:0%
Mistral open source and then depending
upon<00:16:01.880><c> the</c><00:16:02.720><c> domain</c><00:16:03.720><c> the</c><00:16:04.440><c> the</c><00:16:04.800><c> input</c><00:16:05.319><c> is</c>

00:16:05.629 --> 00:16:05.639 align:start position:0%
upon the domain the the input is
 

00:16:05.639 --> 00:16:10.629 align:start position:0%
upon the domain the the input is
aligning<00:16:06.240><c> with</c><00:16:06.560><c> it</c><00:16:06.759><c> can</c><00:16:07.040><c> send</c><00:16:07.360><c> it</c><00:16:07.759><c> to</c><00:16:09.000><c> uh</c><00:16:10.000><c> Lama</c>

00:16:10.629 --> 00:16:10.639 align:start position:0%
aligning with it can send it to uh Lama
 

00:16:10.639 --> 00:16:14.749 align:start position:0%
aligning with it can send it to uh Lama
3.1<00:16:11.600><c> for</c><00:16:12.160><c> data</c><00:16:12.880><c> extraction</c><00:16:13.880><c> as</c><00:16:14.040><c> well</c><00:16:14.279><c> as</c><00:16:14.560><c> it</c>

00:16:14.749 --> 00:16:14.759 align:start position:0%
3.1 for data extraction as well as it
 

00:16:14.759 --> 00:16:21.030 align:start position:0%
3.1 for data extraction as well as it
can<00:16:15.560><c> send</c><00:16:15.920><c> it</c><00:16:16.120><c> to</c><00:16:16.600><c> a</c><00:16:17.120><c> JSL</c><00:16:18.120><c> John</c><00:16:18.480><c> Snow</c><00:16:18.839><c> lab</c><00:16:19.720><c> nmax</c>

00:16:21.030 --> 00:16:21.040 align:start position:0%
can send it to a JSL John Snow lab nmax
 

00:16:21.040 --> 00:16:26.230 align:start position:0%
can send it to a JSL John Snow lab nmax
8B<00:16:22.360><c> parameter</c><00:16:23.360><c> language</c><00:16:24.160><c> model</c><00:16:25.160><c> they</c><00:16:25.759><c> both</c>

00:16:26.230 --> 00:16:26.240 align:start position:0%
8B parameter language model they both
 

00:16:26.240 --> 00:16:30.030 align:start position:0%
8B parameter language model they both
have<00:16:27.040><c> uh</c><00:16:27.279><c> integration</c><00:16:28.199><c> with</c><00:16:28.920><c> database</c><00:16:29.839><c> and</c>

00:16:30.030 --> 00:16:30.040 align:start position:0%
have uh integration with database and
 

00:16:30.040 --> 00:16:33.150 align:start position:0%
have uh integration with database and
they<00:16:30.199><c> can</c><00:16:30.480><c> actually</c><00:16:31.000><c> go</c><00:16:31.519><c> and</c><00:16:31.920><c> extract</c><00:16:32.560><c> data</c>

00:16:33.150 --> 00:16:33.160 align:start position:0%
they can actually go and extract data
 

00:16:33.160 --> 00:16:36.069 align:start position:0%
they can actually go and extract data
and<00:16:33.360><c> then</c><00:16:33.639><c> they</c><00:16:33.800><c> can</c><00:16:34.160><c> act</c><00:16:34.440><c> upon</c><00:16:34.800><c> that</c><00:16:35.160><c> data</c>

00:16:36.069 --> 00:16:36.079 align:start position:0%
and then they can act upon that data
 

00:16:36.079 --> 00:16:40.629 align:start position:0%
and then they can act upon that data
then<00:16:36.519><c> the</c><00:16:36.920><c> JSL</c><00:16:37.839><c> models</c><00:16:38.680><c> sends</c><00:16:39.199><c> the</c><00:16:39.560><c> reply</c><00:16:40.040><c> or</c>

00:16:40.629 --> 00:16:40.639 align:start position:0%
then the JSL models sends the reply or
 

00:16:40.639 --> 00:16:43.910 align:start position:0%
then the JSL models sends the reply or
responds<00:16:41.319><c> back</c><00:16:41.519><c> to</c><00:16:41.720><c> the</c><00:16:42.079><c> moderator</c><00:16:42.800><c> and</c><00:16:43.160><c> which</c>

00:16:43.910 --> 00:16:43.920 align:start position:0%
responds back to the moderator and which
 

00:16:43.920 --> 00:16:48.230 align:start position:0%
responds back to the moderator and which
then<00:16:44.639><c> relas</c><00:16:45.240><c> that</c><00:16:45.800><c> her</c><00:16:46.079><c> response</c><00:16:46.639><c> if</c><00:16:46.880><c> it</c><00:16:47.800><c> is</c>

00:16:48.230 --> 00:16:48.240 align:start position:0%
then relas that her response if it is
 

00:16:48.240 --> 00:16:51.470 align:start position:0%
then relas that her response if it is
within<00:16:48.720><c> all</c><00:16:48.959><c> the</c><00:16:49.759><c> Regulatory</c><00:16:50.399><c> and</c><00:16:50.759><c> ethical</c>

00:16:51.470 --> 00:16:51.480 align:start position:0%
within all the Regulatory and ethical
 

00:16:51.480 --> 00:16:56.870 align:start position:0%
within all the Regulatory and ethical
parameters<00:16:52.480><c> to</c><00:16:52.720><c> the</c><00:16:53.319><c> user</c><00:16:54.279><c> or</c><00:16:54.639><c> to</c><00:16:54.880><c> the</c>

00:16:56.870 --> 00:16:56.880 align:start position:0%
parameters to the user or to the
 

00:16:56.880 --> 00:16:59.590 align:start position:0%
parameters to the user or to the
application<00:16:57.880><c> this</c><00:16:58.040><c> is</c><00:16:58.199><c> a</c><00:16:58.560><c> our</c><00:16:58.959><c> reference</c>

00:16:59.590 --> 00:16:59.600 align:start position:0%
application this is a our reference
 

00:16:59.600 --> 00:17:03.029 align:start position:0%
application this is a our reference
architecture<00:17:00.440><c> on</c><00:17:01.000><c> Oracle</c><00:17:01.800><c> Cloud</c>

00:17:03.029 --> 00:17:03.039 align:start position:0%
architecture on Oracle Cloud
 

00:17:03.039 --> 00:17:06.189 align:start position:0%
architecture on Oracle Cloud
infrastructure<00:17:04.039><c> basically</c><00:17:04.559><c> we</c><00:17:04.679><c> are</c><00:17:04.880><c> using</c>

00:17:06.189 --> 00:17:06.199 align:start position:0%
infrastructure basically we are using
 

00:17:06.199 --> 00:17:10.949 align:start position:0%
infrastructure basically we are using
Oracle<00:17:07.199><c> kubernetes</c><00:17:08.439><c> engine</c><00:17:09.439><c> and</c><00:17:10.280><c> and</c><00:17:10.520><c> we</c><00:17:10.679><c> are</c>

00:17:10.949 --> 00:17:10.959 align:start position:0%
Oracle kubernetes engine and and we are
 

00:17:10.959 --> 00:17:14.949 align:start position:0%
Oracle kubernetes engine and and we are
using<00:17:11.559><c> Nvidia</c><00:17:12.559><c> gpus</c><00:17:13.559><c> they</c><00:17:13.720><c> can</c><00:17:14.120><c> run</c><00:17:14.360><c> on</c><00:17:14.600><c> both</c>

00:17:14.949 --> 00:17:14.959 align:start position:0%
using Nvidia gpus they can run on both
 

00:17:14.959 --> 00:17:19.189 align:start position:0%
using Nvidia gpus they can run on both
Nvidia<00:17:15.559><c> and</c><00:17:15.880><c> AMD</c><00:17:16.679><c> gpus</c><00:17:17.319><c> for</c><00:17:17.839><c> inference</c><00:17:18.760><c> and</c><00:17:18.959><c> we</c>

00:17:19.189 --> 00:17:19.199 align:start position:0%
Nvidia and AMD gpus for inference and we
 

00:17:19.199 --> 00:17:23.549 align:start position:0%
Nvidia and AMD gpus for inference and we
also<00:17:19.600><c> have</c><00:17:19.839><c> some</c><00:17:20.640><c> CPU</c><00:17:21.199><c> Bas</c><00:17:21.480><c> some</c><00:17:22.039><c> HPC</c><00:17:23.039><c> CPU</c>

00:17:23.549 --> 00:17:23.559 align:start position:0%
also have some CPU Bas some HPC CPU
 

00:17:23.559 --> 00:17:26.150 align:start position:0%
also have some CPU Bas some HPC CPU
based

00:17:26.150 --> 00:17:26.160 align:start position:0%
based
 

00:17:26.160 --> 00:17:30.110 align:start position:0%
based
occurrences<00:17:27.160><c> so</c><00:17:27.880><c> advantages</c><00:17:28.480><c> of</c><00:17:28.919><c> oci</c><00:17:29.520><c> for</c>

00:17:30.110 --> 00:17:30.120 align:start position:0%
occurrences so advantages of oci for
 

00:17:30.120 --> 00:17:33.430 align:start position:0%
occurrences so advantages of oci for
agentic<00:17:30.840><c> workflow</c><00:17:31.480><c> is</c><00:17:31.840><c> robust</c>

00:17:33.430 --> 00:17:33.440 align:start position:0%
agentic workflow is robust
 

00:17:33.440 --> 00:17:36.430 align:start position:0%
agentic workflow is robust
infrastructure<00:17:34.440><c> scalability</c><00:17:35.240><c> and</c>

00:17:36.430 --> 00:17:36.440 align:start position:0%
infrastructure scalability and
 

00:17:36.440 --> 00:17:40.750 align:start position:0%
infrastructure scalability and
performance<00:17:37.440><c> and</c><00:17:37.880><c> security</c><00:17:38.440><c> and</c><00:17:39.760><c> compliance</c>

00:17:40.750 --> 00:17:40.760 align:start position:0%
performance and security and compliance
 

00:17:40.760 --> 00:17:45.230 align:start position:0%
performance and security and compliance
and<00:17:41.480><c> uh</c><00:17:42.400><c> lastly</c><00:17:43.400><c> we</c><00:17:43.559><c> have</c><00:17:43.960><c> integrated</c><00:17:44.960><c> this</c>

00:17:45.230 --> 00:17:45.240 align:start position:0%
and uh lastly we have integrated this
 

00:17:45.240 --> 00:17:49.549 align:start position:0%
and uh lastly we have integrated this
with<00:17:45.559><c> Healthcare</c><00:17:46.840><c> workflows</c><00:17:47.840><c> we</c><00:17:48.640><c> we</c><00:17:49.080><c> provide</c>

00:17:49.549 --> 00:17:49.559 align:start position:0%
with Healthcare workflows we we provide
 

00:17:49.559 --> 00:17:52.710 align:start position:0%
with Healthcare workflows we we provide
a<00:17:49.840><c> we</c><00:17:50.039><c> means</c><00:17:50.360><c> oci</c><00:17:51.120><c> provides</c><00:17:51.480><c> a</c><00:17:51.840><c> flexible</c>

00:17:52.710 --> 00:17:52.720 align:start position:0%
a we means oci provides a flexible
 

00:17:52.720 --> 00:17:55.870 align:start position:0%
a we means oci provides a flexible
compute<00:17:53.320><c> autot</c><00:17:53.640><c> tuning</c><00:17:54.039><c> storage</c><00:17:54.880><c> load</c><00:17:55.480><c> data</c>

00:17:55.870 --> 00:17:55.880 align:start position:0%
compute autot tuning storage load data
 

00:17:55.880 --> 00:17:56.950 align:start position:0%
compute autot tuning storage load data
eress

00:17:56.950 --> 00:17:56.960 align:start position:0%
eress
 

00:17:56.960 --> 00:17:59.390 align:start position:0%
eress
fees<00:17:57.960><c> facilitating</c><00:17:58.520><c> the</c><00:17:58.840><c> seamless</c>

00:17:59.390 --> 00:17:59.400 align:start position:0%
fees facilitating the seamless
 

00:17:59.400 --> 00:18:02.470 align:start position:0%
fees facilitating the seamless
integration<00:18:00.400><c> of</c><00:18:00.640><c> the</c><00:18:00.960><c> multi-agent</c><00:18:01.760><c> llm</c>

00:18:02.470 --> 00:18:02.480 align:start position:0%
integration of the multi-agent llm
 

00:18:02.480 --> 00:18:04.870 align:start position:0%
integration of the multi-agent llm
workflows<00:18:03.400><c> within</c><00:18:03.720><c> the</c><00:18:03.960><c> healthcare</c>

00:18:04.870 --> 00:18:04.880 align:start position:0%
workflows within the healthcare
 

00:18:04.880 --> 00:18:08.630 align:start position:0%
workflows within the healthcare
operations<00:18:05.720><c> we</c><00:18:06.200><c> also</c><00:18:06.559><c> have</c><00:18:07.159><c> llm</c><00:18:07.720><c> based</c>

00:18:08.630 --> 00:18:08.640 align:start position:0%
operations we also have llm based
 

00:18:08.640 --> 00:18:11.470 align:start position:0%
operations we also have llm based
applications<00:18:09.520><c> which</c><00:18:09.760><c> enables</c><00:18:10.320><c> workflow</c>

00:18:11.470 --> 00:18:11.480 align:start position:0%
applications which enables workflow
 

00:18:11.480 --> 00:18:13.669 align:start position:0%
applications which enables workflow
automation<00:18:12.480><c> resource</c><00:18:13.000><c> allocation</c>

00:18:13.669 --> 00:18:13.679 align:start position:0%
automation resource allocation
 

00:18:13.679 --> 00:18:17.630 align:start position:0%
automation resource allocation
optimization<00:18:14.679><c> and</c><00:18:15.280><c> Regulatory</c><00:18:16.640><c> Compliance</c>

00:18:17.630 --> 00:18:17.640 align:start position:0%
optimization and Regulatory Compliance
 

00:18:17.640 --> 00:18:20.390 align:start position:0%
optimization and Regulatory Compliance
lastly<00:18:18.200><c> we</c><00:18:18.360><c> have</c><00:18:18.720><c> continuous</c><00:18:19.480><c> support</c><00:18:19.880><c> and</c>

00:18:20.390 --> 00:18:20.400 align:start position:0%
lastly we have continuous support and
 

00:18:20.400 --> 00:18:23.789 align:start position:0%
lastly we have continuous support and
monitoring<00:18:21.280><c> we</c><00:18:21.480><c> offer</c><00:18:22.320><c> free</c><00:18:22.799><c> tools</c><00:18:23.200><c> and</c>

00:18:23.789 --> 00:18:23.799 align:start position:0%
monitoring we offer free tools and
 

00:18:23.799 --> 00:18:26.070 align:start position:0%
monitoring we offer free tools and
reports<00:18:24.240><c> to</c><00:18:24.640><c> support</c><00:18:25.080><c> the</c><00:18:25.320><c> continuous</c>

00:18:26.070 --> 00:18:26.080 align:start position:0%
reports to support the continuous
 

00:18:26.080 --> 00:18:28.470 align:start position:0%
reports to support the continuous
monitoring<00:18:26.640><c> and</c><00:18:26.840><c> optimization</c><00:18:27.600><c> of</c><00:18:28.080><c> Alti</c>

00:18:28.470 --> 00:18:28.480 align:start position:0%
monitoring and optimization of Alti
 

00:18:28.480 --> 00:18:33.669 align:start position:0%
monitoring and optimization of Alti
agent

00:18:33.669 --> 00:18:33.679 align:start position:0%
 
 

00:18:33.679 --> 00:18:38.350 align:start position:0%
 
workflows<00:18:35.000><c> lastly</c><00:18:36.000><c> oci</c><00:18:36.480><c> is</c><00:18:36.600><c> a</c><00:18:36.880><c> future</c><00:18:37.679><c> ready</c>

00:18:38.350 --> 00:18:38.360 align:start position:0%
workflows lastly oci is a future ready
 

00:18:38.360 --> 00:18:41.070 align:start position:0%
workflows lastly oci is a future ready
infrastructure<00:18:39.280><c> for</c><00:18:39.679><c> Innovation</c><00:18:40.320><c> and</c>

00:18:41.070 --> 00:18:41.080 align:start position:0%
infrastructure for Innovation and
 

00:18:41.080 --> 00:18:43.830 align:start position:0%
infrastructure for Innovation and
adaptability<00:18:42.080><c> training</c><00:18:42.559><c> and</c><00:18:43.039><c> education</c><00:18:43.640><c> and</c>

00:18:43.830 --> 00:18:43.840 align:start position:0%
adaptability training and education and
 

00:18:43.840 --> 00:18:47.390 align:start position:0%
adaptability training and education and
also<00:18:44.120><c> for</c><00:18:44.840><c> ethical</c>

00:18:47.390 --> 00:18:47.400 align:start position:0%
also for ethical
 

00:18:47.400 --> 00:18:50.590 align:start position:0%
also for ethical
governments<00:18:48.400><c> uh</c><00:18:48.720><c> if</c><00:18:48.840><c> you</c><00:18:49.039><c> have</c><00:18:49.240><c> any</c><00:18:50.039><c> questions</c>

00:18:50.590 --> 00:18:50.600 align:start position:0%
governments uh if you have any questions
 

00:18:50.600 --> 00:18:56.039 align:start position:0%
governments uh if you have any questions
I<00:18:50.720><c> will</c><00:18:51.200><c> answer</c><00:18:52.080><c> them</c><00:18:53.080><c> real</c><00:18:53.400><c> time</c>

