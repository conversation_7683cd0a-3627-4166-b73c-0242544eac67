WEBVTT
Kind: captions
Language: en

00:00:00.520 --> 00:00:03.310 align:start position:0%
 
and<00:00:00.799><c> howdy</c><00:00:01.280><c> guys</c><00:00:01.640><c> I</c><00:00:01.800><c> believe</c><00:00:02.240><c> I</c><00:00:02.360><c> have</c><00:00:02.639><c> cracked</c>

00:00:03.310 --> 00:00:03.320 align:start position:0%
and howdy guys I believe I have cracked
 

00:00:03.320 --> 00:00:06.030 align:start position:0%
and howdy guys I believe I have cracked
this<00:00:03.520><c> little</c><00:00:03.840><c> riddle</c><00:00:04.319><c> over</c><00:00:04.600><c> here</c><00:00:05.520><c> uh</c><00:00:05.680><c> just</c><00:00:05.839><c> to</c>

00:00:06.030 --> 00:00:06.040 align:start position:0%
this little riddle over here uh just to
 

00:00:06.040 --> 00:00:07.990 align:start position:0%
this little riddle over here uh just to
remind<00:00:06.359><c> you</c><00:00:06.600><c> guys</c><00:00:06.879><c> the</c><00:00:07.040><c> issue</c><00:00:07.359><c> that</c><00:00:07.600><c> were</c><00:00:07.879><c> that</c>

00:00:07.990 --> 00:00:08.000 align:start position:0%
remind you guys the issue that were that
 

00:00:08.000 --> 00:00:09.470 align:start position:0%
remind you guys the issue that were that
we<00:00:08.120><c> were</c><00:00:08.280><c> struggling</c><00:00:08.719><c> with</c><00:00:08.880><c> in</c><00:00:09.000><c> the</c><00:00:09.160><c> last</c>

00:00:09.470 --> 00:00:09.480 align:start position:0%
we were struggling with in the last
 

00:00:09.480 --> 00:00:12.990 align:start position:0%
we were struggling with in the last
video<00:00:10.240><c> is</c><00:00:10.440><c> that</c><00:00:10.719><c> the</c><00:00:11.200><c> varar</c><00:00:12.200><c> data</c><00:00:12.559><c> type</c><00:00:12.840><c> was</c>

00:00:12.990 --> 00:00:13.000 align:start position:0%
video is that the varar data type was
 

00:00:13.000 --> 00:00:15.150 align:start position:0%
video is that the varar data type was
not<00:00:13.240><c> getting</c><00:00:13.679><c> correctly</c><00:00:14.160><c> converted</c><00:00:14.639><c> into</c><00:00:14.920><c> a</c>

00:00:15.150 --> 00:00:15.160 align:start position:0%
not getting correctly converted into a
 

00:00:15.160 --> 00:00:18.870 align:start position:0%
not getting correctly converted into a
date<00:00:15.679><c> data</c><00:00:16.160><c> type</c><00:00:17.080><c> and</c><00:00:17.480><c> the</c><00:00:17.640><c> issue</c><00:00:18.080><c> was</c><00:00:18.439><c> because</c>

00:00:18.870 --> 00:00:18.880 align:start position:0%
date data type and the issue was because
 

00:00:18.880 --> 00:00:21.830 align:start position:0%
date data type and the issue was because
within<00:00:19.480><c> the</c><00:00:19.680><c> data</c><00:00:20.119><c> over</c><00:00:20.400><c> here</c><00:00:20.640><c> that</c><00:00:20.800><c> we</c><00:00:21.039><c> have</c>

00:00:21.830 --> 00:00:21.840 align:start position:0%
within the data over here that we have
 

00:00:21.840 --> 00:00:25.950 align:start position:0%
within the data over here that we have
it's<00:00:22.080><c> not</c><00:00:22.680><c> um</c><00:00:23.680><c> uh</c><00:00:24.160><c> zamp</c><00:00:24.800><c> wasn't</c><00:00:25.119><c> able</c><00:00:25.359><c> to</c>

00:00:25.950 --> 00:00:25.960 align:start position:0%
it's not um uh zamp wasn't able to
 

00:00:25.960 --> 00:00:28.269 align:start position:0%
it's not um uh zamp wasn't able to
understand<00:00:26.320><c> what</c><00:00:27.119><c> understand</c><00:00:27.320><c> the</c><00:00:27.519><c> format</c><00:00:28.080><c> so</c>

00:00:28.269 --> 00:00:28.279 align:start position:0%
understand what understand the format so
 

00:00:28.279 --> 00:00:30.390 align:start position:0%
understand what understand the format so
what<00:00:28.400><c> I</c><00:00:28.519><c> did</c><00:00:28.800><c> over</c><00:00:29.080><c> here</c><00:00:29.560><c> within</c><00:00:29.800><c> the</c><00:00:30.119><c> Excel</c>

00:00:30.390 --> 00:00:30.400 align:start position:0%
what I did over here within the Excel
 

00:00:30.400 --> 00:00:34.310 align:start position:0%
what I did over here within the Excel
sheet<00:00:31.000><c> itself</c><00:00:32.000><c> is</c><00:00:32.399><c> I'll</c><00:00:32.680><c> go</c><00:00:32.960><c> I</c><00:00:33.239><c> highlight</c><00:00:33.879><c> the</c>

00:00:34.310 --> 00:00:34.320 align:start position:0%
sheet itself is I'll go I highlight the
 

00:00:34.320 --> 00:00:38.069 align:start position:0%
sheet itself is I'll go I highlight the
the<00:00:34.559><c> column</c><00:00:35.200><c> I'll</c><00:00:35.360><c> go</c><00:00:35.520><c> into</c><00:00:36.040><c> format</c><00:00:36.879><c> I'll</c><00:00:37.160><c> go</c>

00:00:38.069 --> 00:00:38.079 align:start position:0%
the column I'll go into format I'll go
 

00:00:38.079 --> 00:00:43.510 align:start position:0%
the column I'll go into format I'll go
into<00:00:39.079><c> uh</c><00:00:39.399><c> text</c><00:00:40.399><c> let's</c><00:00:40.640><c> see</c><00:00:41.320><c> uh</c><00:00:41.480><c> let's</c><00:00:41.879><c> see</c><00:00:42.879><c> uh</c>

00:00:43.510 --> 00:00:43.520 align:start position:0%
into uh text let's see uh let's see uh
 

00:00:43.520 --> 00:00:46.670 align:start position:0%
into uh text let's see uh let's see uh
number<00:00:44.520><c> and</c><00:00:44.640><c> then</c><00:00:44.840><c> more</c><00:00:45.239><c> formats</c><00:00:46.079><c> more</c><00:00:46.399><c> data</c>

00:00:46.670 --> 00:00:46.680 align:start position:0%
number and then more formats more data
 

00:00:46.680 --> 00:00:49.470 align:start position:0%
number and then more formats more data
and<00:00:47.000><c> time</c><00:00:47.600><c> formats</c><00:00:48.600><c> and</c><00:00:48.680><c> then</c><00:00:48.840><c> I'll</c><00:00:49.079><c> change</c><00:00:49.320><c> it</c>

00:00:49.470 --> 00:00:49.480 align:start position:0%
and time formats and then I'll change it
 

00:00:49.480 --> 00:00:51.430 align:start position:0%
and time formats and then I'll change it
into<00:00:49.840><c> this</c><00:00:50.039><c> type</c><00:00:50.239><c> of</c><00:00:50.399><c> format</c><00:00:50.879><c> all</c><00:00:51.039><c> right</c><00:00:51.280><c> on</c>

00:00:51.430 --> 00:00:51.440 align:start position:0%
into this type of format all right on
 

00:00:51.440 --> 00:00:53.670 align:start position:0%
into this type of format all right on
the<00:00:51.559><c> one</c><00:00:51.800><c> hand</c><00:00:52.280><c> we're</c><00:00:52.520><c> losing</c><00:00:53.079><c> the</c><00:00:53.320><c> time</c>

00:00:53.670 --> 00:00:53.680 align:start position:0%
the one hand we're losing the time
 

00:00:53.680 --> 00:00:55.750 align:start position:0%
the one hand we're losing the time
aspect<00:00:54.160><c> but</c><00:00:54.359><c> that's</c><00:00:54.680><c> okay</c><00:00:55.000><c> most</c><00:00:55.239><c> people</c><00:00:55.480><c> don't</c>

00:00:55.750 --> 00:00:55.760 align:start position:0%
aspect but that's okay most people don't
 

00:00:55.760 --> 00:00:58.029 align:start position:0%
aspect but that's okay most people don't
really<00:00:56.079><c> need</c><00:00:56.399><c> the</c><00:00:56.640><c> time</c><00:00:57.440><c> within</c><00:00:57.800><c> their</c>

00:00:58.029 --> 00:00:58.039 align:start position:0%
really need the time within their
 

00:00:58.039 --> 00:01:00.229 align:start position:0%
really need the time within their
database

00:01:00.229 --> 00:01:00.239 align:start position:0%
database
 

00:01:00.239 --> 00:01:02.830 align:start position:0%
database
uh<00:01:00.519><c> in</c><00:01:00.680><c> this</c><00:01:00.920><c> case</c><00:01:01.399><c> the</c><00:01:01.559><c> having</c><00:01:01.840><c> the</c><00:01:02.000><c> day</c><00:01:02.239><c> is</c>

00:01:02.830 --> 00:01:02.840 align:start position:0%
uh in this case the having the day is
 

00:01:02.840 --> 00:01:06.550 align:start position:0%
uh in this case the having the day is
enough<00:01:03.840><c> um</c><00:01:04.439><c> all</c><00:01:04.600><c> right</c><00:01:04.839><c> so</c><00:01:05.560><c> and</c><00:01:05.840><c> also</c><00:01:06.320><c> this</c><00:01:06.439><c> is</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
enough um all right so and also this is
 

00:01:06.560 --> 00:01:08.310 align:start position:0%
enough um all right so and also this is
a<00:01:06.720><c> good</c><00:01:06.960><c> example</c><00:01:07.320><c> of</c><00:01:07.520><c> not</c><00:01:07.759><c> being</c><00:01:08.080><c> a</c>

00:01:08.310 --> 00:01:08.320 align:start position:0%
a good example of not being a
 

00:01:08.320 --> 00:01:10.830 align:start position:0%
a good example of not being a
perfectionist<00:01:09.200><c> on</c><00:01:09.360><c> the</c><00:01:09.520><c> one</c><00:01:09.720><c> hand</c><00:01:10.119><c> we</c><00:01:10.280><c> need</c><00:01:10.720><c> we</c>

00:01:10.830 --> 00:01:10.840 align:start position:0%
perfectionist on the one hand we need we
 

00:01:10.840 --> 00:01:14.109 align:start position:0%
perfectionist on the one hand we need we
know<00:01:11.200><c> that</c><00:01:11.400><c> zamp</c><00:01:11.840><c> needs</c><00:01:12.240><c> this</c><00:01:12.799><c> this</c><00:01:13.119><c> data</c><00:01:13.799><c> this</c>

00:01:14.109 --> 00:01:14.119 align:start position:0%
know that zamp needs this this data this
 

00:01:14.119 --> 00:01:16.710 align:start position:0%
know that zamp needs this this data this
column<00:01:15.080><c> these</c><00:01:15.280><c> date</c><00:01:15.600><c> columns</c><00:01:15.960><c> in</c><00:01:16.040><c> a</c><00:01:16.240><c> specific</c>

00:01:16.710 --> 00:01:16.720 align:start position:0%
column these date columns in a specific
 

00:01:16.720 --> 00:01:18.789 align:start position:0%
column these date columns in a specific
format<00:01:17.280><c> on</c><00:01:17.439><c> the</c><00:01:17.560><c> other</c><00:01:17.799><c> hand</c><00:01:18.159><c> we're</c><00:01:18.360><c> going</c><00:01:18.479><c> to</c>

00:01:18.789 --> 00:01:18.799 align:start position:0%
format on the other hand we're going to
 

00:01:18.799 --> 00:01:22.670 align:start position:0%
format on the other hand we're going to
end<00:01:19.000><c> up</c><00:01:19.200><c> losing</c><00:01:19.640><c> our</c><00:01:20.159><c> day</c><00:01:21.159><c> uh</c><00:01:21.320><c> our</c><00:01:21.720><c> time</c><00:01:22.320><c> our</c>

00:01:22.670 --> 00:01:22.680 align:start position:0%
end up losing our day uh our time our
 

00:01:22.680 --> 00:01:25.190 align:start position:0%
end up losing our day uh our time our
time<00:01:23.119><c> data</c><00:01:23.520><c> from</c><00:01:23.799><c> the</c><00:01:24.280><c> from</c><00:01:24.479><c> the</c><00:01:24.680><c> data</c><00:01:25.040><c> but</c>

00:01:25.190 --> 00:01:25.200 align:start position:0%
time data from the from the data but
 

00:01:25.200 --> 00:01:27.069 align:start position:0%
time data from the from the data but
that's<00:01:25.520><c> okay</c><00:01:25.920><c> we</c><00:01:26.040><c> just</c><00:01:26.200><c> need</c><00:01:26.360><c> to</c><00:01:26.520><c> move</c><00:01:26.759><c> forward</c>

00:01:27.069 --> 00:01:27.079 align:start position:0%
that's okay we just need to move forward
 

00:01:27.079 --> 00:01:29.429 align:start position:0%
that's okay we just need to move forward
and<00:01:27.280><c> keep</c><00:01:27.479><c> plugging</c><00:01:27.840><c> on</c><00:01:28.200><c> so</c><00:01:28.960><c> uh</c><00:01:29.079><c> and</c><00:01:29.200><c> don't</c>

00:01:29.429 --> 00:01:29.439 align:start position:0%
and keep plugging on so uh and don't
 

00:01:29.439 --> 00:01:30.710 align:start position:0%
and keep plugging on so uh and don't
worry<00:01:29.720><c> at</c><00:01:29.960><c> the</c><00:01:30.040><c> end</c><00:01:30.200><c> is</c><00:01:30.320><c> going</c><00:01:30.439><c> to</c><00:01:30.520><c> be</c>

00:01:30.710 --> 00:01:30.720 align:start position:0%
worry at the end is going to be
 

00:01:30.720 --> 00:01:32.950 align:start position:0%
worry at the end is going to be
worthwhile<00:01:31.479><c> all</c><00:01:31.600><c> right</c><00:01:31.759><c> so</c><00:01:32.000><c> this</c><00:01:32.079><c> is</c><00:01:32.320><c> the</c><00:01:32.520><c> data</c>

00:01:32.950 --> 00:01:32.960 align:start position:0%
worthwhile all right so this is the data
 

00:01:32.960 --> 00:01:35.710 align:start position:0%
worthwhile all right so this is the data
that<00:01:33.200><c> my</c><00:01:33.360><c> SQL</c><00:01:34.200><c> understands</c><00:01:34.600><c> year</c><00:01:35.159><c> month</c><00:01:35.520><c> and</c>

00:01:35.710 --> 00:01:35.720 align:start position:0%
that my SQL understands year month and
 

00:01:35.720 --> 00:01:38.149 align:start position:0%
that my SQL understands year month and
day<00:01:36.040><c> I'm</c><00:01:36.119><c> going</c><00:01:36.240><c> to</c><00:01:36.399><c> press</c><00:01:36.799><c> apply</c><00:01:37.240><c> over</c><00:01:37.520><c> here</c>

00:01:38.149 --> 00:01:38.159 align:start position:0%
day I'm going to press apply over here
 

00:01:38.159 --> 00:01:40.109 align:start position:0%
day I'm going to press apply over here
and<00:01:38.360><c> as</c><00:01:38.439><c> you</c><00:01:38.560><c> can</c><00:01:38.680><c> see</c><00:01:38.840><c> it</c><00:01:39.000><c> changes</c><00:01:39.479><c> all</c><00:01:39.720><c> the</c>

00:01:40.109 --> 00:01:40.119 align:start position:0%
and as you can see it changes all the
 

00:01:40.119 --> 00:01:42.870 align:start position:0%
and as you can see it changes all the
all<00:01:40.560><c> all</c><00:01:40.759><c> the</c><00:01:40.920><c> rows</c><00:01:41.520><c> to</c><00:01:41.720><c> that</c><00:01:41.920><c> specific</c><00:01:42.399><c> format</c>

00:01:42.870 --> 00:01:42.880 align:start position:0%
all all the rows to that specific format
 

00:01:42.880 --> 00:01:45.030 align:start position:0%
all all the rows to that specific format
we're<00:01:43.000><c> going</c><00:01:43.119><c> to</c><00:01:43.240><c> do</c><00:01:43.479><c> that</c><00:01:43.680><c> same</c><00:01:44.079><c> process</c><00:01:44.799><c> for</c>

00:01:45.030 --> 00:01:45.040 align:start position:0%
we're going to do that same process for
 

00:01:45.040 --> 00:01:47.270 align:start position:0%
we're going to do that same process for
all<00:01:45.200><c> of</c><00:01:45.320><c> our</c><00:01:45.520><c> date</c><00:01:45.960><c> formats</c><00:01:46.600><c> all</c><00:01:46.759><c> right</c><00:01:47.079><c> let's</c>

00:01:47.270 --> 00:01:47.280 align:start position:0%
all of our date formats all right let's
 

00:01:47.280 --> 00:01:48.389 align:start position:0%
all of our date formats all right let's
do

00:01:48.389 --> 00:01:48.399 align:start position:0%
do
 

00:01:48.399 --> 00:01:51.789 align:start position:0%
do
format<00:01:49.399><c> uh</c><00:01:49.880><c> what</c><00:01:50.079><c> kind</c><00:01:50.280><c> of</c><00:01:50.719><c> format</c><00:01:51.119><c> do</c><00:01:51.280><c> we</c><00:01:51.439><c> want</c>

00:01:51.789 --> 00:01:51.799 align:start position:0%
format uh what kind of format do we want
 

00:01:51.799 --> 00:01:53.950 align:start position:0%
format uh what kind of format do we want
here<00:01:52.159><c> we</c><00:01:52.280><c> want</c><00:01:52.520><c> more</c><00:01:52.799><c> date</c><00:01:53.079><c> and</c><00:01:53.280><c> time</c><00:01:53.640><c> oh</c><00:01:53.799><c> wait</c>

00:01:53.950 --> 00:01:53.960 align:start position:0%
here we want more date and time oh wait
 

00:01:53.960 --> 00:01:55.590 align:start position:0%
here we want more date and time oh wait
a<00:01:54.119><c> second</c><00:01:54.439><c> do</c><00:01:54.560><c> we</c><00:01:54.680><c> have</c><00:01:54.880><c> that</c><00:01:55.079><c> date</c><00:01:55.360><c> ah</c><00:01:55.520><c> there</c>

00:01:55.590 --> 00:01:55.600 align:start position:0%
a second do we have that date ah there
 

00:01:55.600 --> 00:01:59.069 align:start position:0%
a second do we have that date ah there
it<00:01:55.719><c> is</c><00:01:55.920><c> date</c><00:01:56.399><c> time</c><00:01:57.439><c> 9:26</c><00:01:58.439><c> maybe</c><00:01:58.719><c> this</c><00:01:58.880><c> will</c>

00:01:59.069 --> 00:01:59.079 align:start position:0%
it is date time 9:26 maybe this will
 

00:01:59.079 --> 00:02:00.870 align:start position:0%
it is date time 9:26 maybe this will
work<00:01:59.640><c> okay</c><00:01:59.960><c> we're</c><00:02:00.079><c> going</c><00:02:00.159><c> to</c><00:02:00.280><c> double</c><00:02:00.560><c> check</c>

00:02:00.870 --> 00:02:00.880 align:start position:0%
work okay we're going to double check
 

00:02:00.880 --> 00:02:03.630 align:start position:0%
work okay we're going to double check
whether<00:02:01.119><c> this</c><00:02:01.360><c> actually</c><00:02:01.920><c> works</c><00:02:02.840><c> uh</c><00:02:03.000><c> no</c><00:02:03.280><c> we</c><00:02:03.520><c> it</c>

00:02:03.630 --> 00:02:03.640 align:start position:0%
whether this actually works uh no we it
 

00:02:03.640 --> 00:02:06.350 align:start position:0%
whether this actually works uh no we it
all<00:02:03.840><c> went</c><00:02:04.039><c> to</c><00:02:04.280><c> zero</c><00:02:04.680><c> anyways</c><00:02:05.079><c> so</c><00:02:05.280><c> forget</c><00:02:05.600><c> it</c>

00:02:06.350 --> 00:02:06.360 align:start position:0%
all went to zero anyways so forget it
 

00:02:06.360 --> 00:02:09.430 align:start position:0%
all went to zero anyways so forget it
contrl<00:02:06.880><c> z</c><00:02:07.320><c> z</c><00:02:07.960><c> contrl</c>

00:02:09.430 --> 00:02:09.440 align:start position:0%
contrl z z contrl
 

00:02:09.440 --> 00:02:12.630 align:start position:0%
contrl z z contrl
z<00:02:10.440><c> wait</c><00:02:10.560><c> a</c>

00:02:12.630 --> 00:02:12.640 align:start position:0%
z wait a
 

00:02:12.640 --> 00:02:17.470 align:start position:0%
z wait a
second<00:02:13.959><c> okay</c><00:02:14.959><c> all</c><00:02:15.160><c> right</c><00:02:15.760><c> uh</c><00:02:16.080><c> let's</c><00:02:16.280><c> see</c><00:02:16.519><c> over</c>

00:02:17.470 --> 00:02:17.480 align:start position:0%
second okay all right uh let's see over
 

00:02:17.480 --> 00:02:21.190 align:start position:0%
second okay all right uh let's see over
here<00:02:18.480><c> filter</c><00:02:18.920><c> by</c><00:02:19.360><c> conditions</c><00:02:19.920><c> no</c><00:02:20.360><c> this</c><00:02:20.920><c> right</c>

00:02:21.190 --> 00:02:21.200 align:start position:0%
here filter by conditions no this right
 

00:02:21.200 --> 00:02:23.589 align:start position:0%
here filter by conditions no this right
click<00:02:21.480><c> it</c><00:02:21.879><c> right</c><00:02:22.160><c> click</c><00:02:22.840><c> first</c><00:02:23.160><c> highlight</c><00:02:23.519><c> the</c>

00:02:23.589 --> 00:02:23.599 align:start position:0%
click it right click first highlight the
 

00:02:23.599 --> 00:02:26.390 align:start position:0%
click it right click first highlight the
row<00:02:24.000><c> right</c><00:02:24.280><c> click</c><00:02:24.640><c> the</c><00:02:25.000><c> the</c><00:02:25.239><c> title</c><00:02:26.239><c> you're</c>

00:02:26.390 --> 00:02:26.400 align:start position:0%
row right click the the title you're
 

00:02:26.400 --> 00:02:30.750 align:start position:0%
row right click the the title you're
going<00:02:26.519><c> to</c><00:02:26.760><c> press</c><00:02:27.560><c> over</c><00:02:27.920><c> here</c><00:02:28.680><c> again</c><00:02:29.080><c> format</c>

00:02:30.750 --> 00:02:30.760 align:start position:0%
going to press over here again format
 

00:02:30.760 --> 00:02:33.190 align:start position:0%
going to press over here again format
you're<00:02:30.959><c> going</c><00:02:31.080><c> to</c><00:02:31.640><c> press</c><00:02:32.640><c> what</c><00:02:32.840><c> type</c><00:02:33.040><c> of</c>

00:02:33.190 --> 00:02:33.200 align:start position:0%
you're going to press what type of
 

00:02:33.200 --> 00:02:37.030 align:start position:0%
you're going to press what type of
format<00:02:33.879><c> we</c><00:02:34.440><c> want</c><00:02:35.440><c> uh</c><00:02:36.000><c> whatever</c><00:02:36.400><c> format</c><00:02:36.840><c> we've</c>

00:02:37.030 --> 00:02:37.040 align:start position:0%
format we want uh whatever format we've
 

00:02:37.040 --> 00:02:38.149 align:start position:0%
format we want uh whatever format we've
been

00:02:38.149 --> 00:02:38.159 align:start position:0%
been
 

00:02:38.159 --> 00:02:41.949 align:start position:0%
been
using<00:02:39.159><c> okay</c><00:02:40.000><c> uh</c><00:02:40.159><c> let's</c><00:02:40.319><c> see</c><00:02:40.599><c> more</c><00:02:40.959><c> formats</c>

00:02:41.949 --> 00:02:41.959 align:start position:0%
using okay uh let's see more formats
 

00:02:41.959 --> 00:02:43.390 align:start position:0%
using okay uh let's see more formats
more<00:02:42.280><c> date</c><00:02:42.560><c> and</c>

00:02:43.390 --> 00:02:43.400 align:start position:0%
more date and
 

00:02:43.400 --> 00:02:47.630 align:start position:0%
more date and
time<00:02:44.400><c> yes</c><00:02:44.760><c> year</c><00:02:45.080><c> month</c><00:02:45.400><c> and</c><00:02:45.599><c> day</c><00:02:46.159><c> apply</c><00:02:46.879><c> good</c>

00:02:47.630 --> 00:02:47.640 align:start position:0%
time yes year month and day apply good
 

00:02:47.640 --> 00:02:49.030 align:start position:0%
time yes year month and day apply good
make<00:02:47.760><c> sure</c><00:02:47.959><c> that</c><00:02:48.120><c> everything</c><00:02:48.400><c> is</c><00:02:48.560><c> the</c><00:02:48.760><c> same</c>

00:02:49.030 --> 00:02:49.040 align:start position:0%
make sure that everything is the same
 

00:02:49.040 --> 00:02:51.830 align:start position:0%
make sure that everything is the same
format<00:02:49.760><c> and</c><00:02:50.080><c> go</c><00:02:50.360><c> all</c><00:02:50.519><c> the</c><00:02:50.640><c> way</c><00:02:50.879><c> down</c><00:02:51.239><c> to</c><00:02:51.680><c> the</c>

00:02:51.830 --> 00:02:51.840 align:start position:0%
format and go all the way down to the
 

00:02:51.840 --> 00:02:54.550 align:start position:0%
format and go all the way down to the
end<00:02:52.519><c> same</c><00:02:52.959><c> same</c><00:02:53.319><c> process</c><00:02:53.680><c> for</c><00:02:53.959><c> the</c><00:02:54.120><c> other</c><00:02:54.319><c> one</c>

00:02:54.550 --> 00:02:54.560 align:start position:0%
end same same process for the other one
 

00:02:54.560 --> 00:02:57.869 align:start position:0%
end same same process for the other one
click<00:02:54.840><c> the</c><00:02:55.040><c> install</c><00:02:55.680><c> time</c><00:02:56.680><c> right</c><00:02:57.000><c> click</c><00:02:57.239><c> it</c>

00:02:57.869 --> 00:02:57.879 align:start position:0%
click the install time right click it
 

00:02:57.879 --> 00:03:00.229 align:start position:0%
click the install time right click it
format

00:03:00.229 --> 00:03:00.239 align:start position:0%
format
 

00:03:00.239 --> 00:03:03.229 align:start position:0%
format
uh<00:03:01.159><c> number</c><00:03:02.159><c> more</c>

00:03:03.229 --> 00:03:03.239 align:start position:0%
uh number more
 

00:03:03.239 --> 00:03:07.390 align:start position:0%
uh number more
formats<00:03:04.239><c> more</c><00:03:04.519><c> data</c><00:03:04.799><c> and</c><00:03:05.040><c> time</c><00:03:05.360><c> formats</c><00:03:06.400><c> apply</c>

00:03:07.390 --> 00:03:07.400 align:start position:0%
formats more data and time formats apply
 

00:03:07.400 --> 00:03:10.789 align:start position:0%
formats more data and time formats apply
ah<00:03:07.560><c> wait</c><00:03:07.799><c> a</c><00:03:07.959><c> second</c><00:03:08.680><c> is</c><00:03:08.799><c> that</c><00:03:09.000><c> correct</c><00:03:09.560><c> 1900</c><00:03:10.560><c> no</c>

00:03:10.789 --> 00:03:10.799 align:start position:0%
ah wait a second is that correct 1900 no
 

00:03:10.799 --> 00:03:13.309 align:start position:0%
ah wait a second is that correct 1900 no
this<00:03:10.920><c> is</c><00:03:11.080><c> not</c><00:03:11.319><c> good</c><00:03:12.080><c> n</c><00:03:12.440><c> it</c><00:03:12.799><c> this</c><00:03:12.959><c> did</c><00:03:13.120><c> not</c>

00:03:13.309 --> 00:03:13.319 align:start position:0%
this is not good n it this did not
 

00:03:13.319 --> 00:03:15.270 align:start position:0%
this is not good n it this did not
happen<00:03:13.560><c> in</c><00:03:13.680><c> the</c><00:03:13.799><c> year</c><00:03:14.000><c> 1900</c><00:03:14.680><c> ah</c><00:03:14.879><c> okay</c><00:03:15.040><c> this</c><00:03:15.159><c> is</c>

00:03:15.270 --> 00:03:15.280 align:start position:0%
happen in the year 1900 ah okay this is
 

00:03:15.280 --> 00:03:16.990 align:start position:0%
happen in the year 1900 ah okay this is
a<00:03:15.440><c> different</c><00:03:15.799><c> type</c><00:03:15.959><c> of</c><00:03:16.080><c> format</c><00:03:16.519><c> click</c><00:03:16.760><c> to</c>

00:03:16.990 --> 00:03:17.000 align:start position:0%
a different type of format click to
 

00:03:17.000 --> 00:03:19.910 align:start position:0%
a different type of format click to
install<00:03:17.760><c> time</c><00:03:18.760><c> okay</c><00:03:19.000><c> this</c><00:03:19.120><c> is</c><00:03:19.400><c> kind</c><00:03:19.519><c> of</c>

00:03:19.910 --> 00:03:19.920 align:start position:0%
install time okay this is kind of
 

00:03:19.920 --> 00:03:22.630 align:start position:0%
install time okay this is kind of
complicated<00:03:20.920><c> can</c><00:03:21.080><c> we</c><00:03:21.720><c> can</c><00:03:21.840><c> we</c><00:03:22.040><c> transfer</c><00:03:22.519><c> this</c>

00:03:22.630 --> 00:03:22.640 align:start position:0%
complicated can we can we transfer this
 

00:03:22.640 --> 00:03:24.509 align:start position:0%
complicated can we can we transfer this
into<00:03:22.920><c> the</c><00:03:23.080><c> amount</c><00:03:23.280><c> of</c><00:03:23.480><c> seconds</c><00:03:23.959><c> or</c><00:03:24.159><c> something</c>

00:03:24.509 --> 00:03:24.519 align:start position:0%
into the amount of seconds or something
 

00:03:24.519 --> 00:03:26.470 align:start position:0%
into the amount of seconds or something
like<00:03:24.720><c> that</c><00:03:25.400><c> I</c><00:03:25.480><c> don't</c><00:03:25.640><c> know</c><00:03:25.760><c> if</c><00:03:25.879><c> SQL</c><00:03:26.239><c> is</c><00:03:26.360><c> going</c>

00:03:26.470 --> 00:03:26.480 align:start position:0%
like that I don't know if SQL is going
 

00:03:26.480 --> 00:03:28.750 align:start position:0%
like that I don't know if SQL is going
to<00:03:26.680><c> pick</c><00:03:26.920><c> this</c><00:03:27.120><c> up</c><00:03:27.840><c> it's</c><00:03:28.000><c> not</c><00:03:28.200><c> the</c><00:03:28.319><c> end</c><00:03:28.519><c> of</c><00:03:28.640><c> the</c>

00:03:28.750 --> 00:03:28.760 align:start position:0%
to pick this up it's not the end of the
 

00:03:28.760 --> 00:03:30.990 align:start position:0%
to pick this up it's not the end of the
world<00:03:29.120><c> it's</c><00:03:29.840><c> most</c><00:03:30.080><c> data</c><00:03:30.360><c> types</c><00:03:30.599><c> are</c><00:03:30.720><c> not</c><00:03:30.879><c> going</c>

00:03:30.990 --> 00:03:31.000 align:start position:0%
world it's most data types are not going
 

00:03:31.000 --> 00:03:33.110 align:start position:0%
world it's most data types are not going
to<00:03:31.200><c> have</c><00:03:31.519><c> these</c><00:03:31.760><c> types</c><00:03:31.959><c> of</c><00:03:32.159><c> crazy</c><00:03:32.519><c> formats</c>

00:03:33.110 --> 00:03:33.120 align:start position:0%
to have these types of crazy formats
 

00:03:33.120 --> 00:03:35.830 align:start position:0%
to have these types of crazy formats
they'll<00:03:33.360><c> usually</c><00:03:33.799><c> be</c><00:03:34.799><c> you</c><00:03:34.920><c> know</c><00:03:35.159><c> standardized</c>

00:03:35.830 --> 00:03:35.840 align:start position:0%
they'll usually be you know standardized
 

00:03:35.840 --> 00:03:40.710 align:start position:0%
they'll usually be you know standardized
and<00:03:36.040><c> you</c><00:03:36.159><c> might</c><00:03:36.319><c> not</c><00:03:36.519><c> even</c><00:03:36.760><c> have</c><00:03:37.000><c> this</c><00:03:37.319><c> era</c>

00:03:40.710 --> 00:03:40.720 align:start position:0%
 
 

00:03:40.720 --> 00:03:42.710 align:start position:0%
 
anyways<00:03:41.720><c> excuse</c>

00:03:42.710 --> 00:03:42.720 align:start position:0%
anyways excuse
 

00:03:42.720 --> 00:03:47.789 align:start position:0%
anyways excuse
me<00:03:43.879><c> okay</c><00:03:44.879><c> uh</c><00:03:45.159><c> let's</c><00:03:45.319><c> see</c><00:03:45.519><c> over</c><00:03:45.760><c> here</c>

00:03:47.789 --> 00:03:47.799 align:start position:0%
me okay uh let's see over here
 

00:03:47.799 --> 00:03:51.270 align:start position:0%
me okay uh let's see over here
format<00:03:48.799><c> this</c><00:03:48.920><c> is</c><00:03:49.040><c> a</c><00:03:49.280><c> number</c><00:03:50.159><c> what</c><00:03:50.319><c> kind</c><00:03:50.519><c> of</c>

00:03:51.270 --> 00:03:51.280 align:start position:0%
format this is a number what kind of
 

00:03:51.280 --> 00:03:55.949 align:start position:0%
format this is a number what kind of
what<00:03:51.439><c> kind</c><00:03:51.599><c> of</c><00:03:51.760><c> format</c><00:03:52.200><c> is</c><00:03:52.400><c> that</c><00:03:52.840><c> exactly</c><00:03:53.840><c> more</c>

00:03:55.949 --> 00:03:55.959 align:start position:0%
what kind of format is that exactly more
 

00:03:55.959 --> 00:03:59.309 align:start position:0%
what kind of format is that exactly more
formats<00:03:56.959><c> ah</c><00:03:57.280><c> duration</c><00:03:58.280><c> okay</c><00:03:58.879><c> you</c><00:03:59.040><c> know</c><00:03:59.200><c> what</c>

00:03:59.309 --> 00:03:59.319 align:start position:0%
formats ah duration okay you know what
 

00:03:59.319 --> 00:04:01.069 align:start position:0%
formats ah duration okay you know what
maybe<00:03:59.519><c> we</c><00:03:59.599><c> should</c><00:03:59.799><c> should</c><00:04:00.000><c> just</c><00:04:00.560><c> just</c><00:04:00.840><c> leave</c>

00:04:01.069 --> 00:04:01.079 align:start position:0%
maybe we should should just just leave
 

00:04:01.079 --> 00:04:03.710 align:start position:0%
maybe we should should just just leave
it<00:04:01.319><c> as</c><00:04:01.560><c> is</c><00:04:02.239><c> all</c><00:04:02.360><c> right</c><00:04:02.560><c> sounds</c><00:04:02.879><c> good</c><00:04:03.120><c> yala</c><00:04:03.480><c> so</c><00:04:03.640><c> I</c>

00:04:03.710 --> 00:04:03.720 align:start position:0%
it as is all right sounds good yala so I
 

00:04:03.720 --> 00:04:04.869 align:start position:0%
it as is all right sounds good yala so I
think<00:04:03.920><c> I</c><00:04:03.959><c> think</c><00:04:04.120><c> we</c><00:04:04.239><c> just</c><00:04:04.360><c> need</c><00:04:04.519><c> to</c><00:04:04.640><c> move</c>

00:04:04.869 --> 00:04:04.879 align:start position:0%
think I think we just need to move
 

00:04:04.879 --> 00:04:06.990 align:start position:0%
think I think we just need to move
forward<00:04:05.159><c> and</c><00:04:05.400><c> keep</c><00:04:05.879><c> keep</c><00:04:06.079><c> making</c><00:04:06.480><c> progress</c>

00:04:06.990 --> 00:04:07.000 align:start position:0%
forward and keep keep making progress
 

00:04:07.000 --> 00:04:11.350 align:start position:0%
forward and keep keep making progress
over<00:04:07.640><c> here</c><00:04:08.640><c> all</c><00:04:09.040><c> right</c><00:04:10.040><c> uh</c><00:04:10.439><c> this</c><00:04:10.560><c> is</c><00:04:10.760><c> all</c><00:04:11.040><c> good</c>

00:04:11.350 --> 00:04:11.360 align:start position:0%
over here all right uh this is all good
 

00:04:11.360 --> 00:04:13.110 align:start position:0%
over here all right uh this is all good
everything<00:04:11.640><c> looks</c><00:04:12.120><c> great</c><00:04:12.599><c> everything</c><00:04:12.879><c> looks</c>

00:04:13.110 --> 00:04:13.120 align:start position:0%
everything looks great everything looks
 

00:04:13.120 --> 00:04:16.430 align:start position:0%
everything looks great everything looks
Jiggy<00:04:13.480><c> baby</c><00:04:13.879><c> over</c><00:04:14.200><c> here</c><00:04:14.840><c> and</c><00:04:15.040><c> let's</c><00:04:15.239><c> do</c><00:04:15.440><c> file</c>

00:04:16.430 --> 00:04:16.440 align:start position:0%
Jiggy baby over here and let's do file
 

00:04:16.440 --> 00:04:19.710 align:start position:0%
Jiggy baby over here and let's do file
and<00:04:16.959><c> download</c><00:04:17.919><c> as</c><00:04:18.359><c> a</c>

00:04:19.710 --> 00:04:19.720 align:start position:0%
and download as a
 

00:04:19.720 --> 00:04:23.030 align:start position:0%
and download as a
CSV<00:04:20.720><c> okay</c><00:04:21.079><c> that's</c><00:04:21.239><c> going</c><00:04:21.359><c> to</c><00:04:21.479><c> download</c><00:04:21.840><c> as</c><00:04:21.959><c> a</c>

00:04:23.030 --> 00:04:23.040 align:start position:0%
CSV okay that's going to download as a
 

00:04:23.040 --> 00:04:27.030 align:start position:0%
CSV okay that's going to download as a
CSV<00:04:24.040><c> and</c><00:04:24.479><c> now</c><00:04:24.759><c> we</c><00:04:24.880><c> are</c><00:04:25.120><c> going</c><00:04:25.520><c> back</c><00:04:25.759><c> into</c><00:04:26.120><c> zamp</c>

00:04:27.030 --> 00:04:27.040 align:start position:0%
CSV and now we are going back into zamp
 

00:04:27.040 --> 00:04:30.029 align:start position:0%
CSV and now we are going back into zamp
we're<00:04:27.199><c> going</c><00:04:27.320><c> to</c><00:04:27.520><c> go</c><00:04:27.919><c> into</c><00:04:28.560><c> this</c><00:04:28.800><c> database</c>

00:04:30.029 --> 00:04:30.039 align:start position:0%
we're going to go into this database
 

00:04:30.039 --> 00:04:32.310 align:start position:0%
we're going to go into this database
okay<00:04:30.759><c> I</c><00:04:30.919><c> created</c><00:04:31.240><c> a</c><00:04:31.400><c> new</c><00:04:31.600><c> database</c><00:04:32.080><c> while</c><00:04:32.240><c> I</c>

00:04:32.310 --> 00:04:32.320 align:start position:0%
okay I created a new database while I
 

00:04:32.320 --> 00:04:34.230 align:start position:0%
okay I created a new database while I
was<00:04:32.440><c> playing</c><00:04:32.759><c> around</c><00:04:33.000><c> or</c><00:04:33.160><c> whatever</c><00:04:33.560><c> CSV</c><00:04:34.080><c> to</c>

00:04:34.230 --> 00:04:34.240 align:start position:0%
was playing around or whatever CSV to
 

00:04:34.240 --> 00:04:36.909 align:start position:0%
was playing around or whatever CSV to
SQL<00:04:34.639><c> demo</c><00:04:35.400><c> okay</c><00:04:35.880><c> you</c><00:04:36.039><c> have</c><00:04:36.240><c> this</c><00:04:36.400><c> table</c><00:04:36.720><c> I</c><00:04:36.800><c> want</c>

00:04:36.909 --> 00:04:36.919 align:start position:0%
SQL demo okay you have this table I want
 

00:04:36.919 --> 00:04:39.230 align:start position:0%
SQL demo okay you have this table I want
to<00:04:37.120><c> drop</c><00:04:37.520><c> this</c><00:04:37.840><c> table</c><00:04:38.520><c> cuz</c><00:04:38.720><c> it's</c><00:04:38.880><c> got</c><00:04:39.080><c> the</c>

00:04:39.230 --> 00:04:39.240 align:start position:0%
to drop this table cuz it's got the
 

00:04:39.240 --> 00:04:41.710 align:start position:0%
to drop this table cuz it's got the
wrong<00:04:39.600><c> data</c><00:04:40.080><c> I</c><00:04:40.280><c> exported</c><00:04:40.800><c> it</c><00:04:40.960><c> and</c><00:04:41.120><c> I</c><00:04:41.280><c> uploaded</c>

00:04:41.710 --> 00:04:41.720 align:start position:0%
wrong data I exported it and I uploaded
 

00:04:41.720 --> 00:04:43.430 align:start position:0%
wrong data I exported it and I uploaded
it<00:04:41.840><c> to</c><00:04:42.000><c> this</c><00:04:42.160><c> local</c><00:04:42.479><c> database</c><00:04:42.880><c> in</c><00:04:43.000><c> the</c><00:04:43.160><c> wrong</c>

00:04:43.430 --> 00:04:43.440 align:start position:0%
it to this local database in the wrong
 

00:04:43.440 --> 00:04:46.749 align:start position:0%
it to this local database in the wrong
format<00:04:43.919><c> so</c><00:04:44.120><c> I</c><00:04:44.320><c> do</c><00:04:45.120><c> I</c><00:04:45.240><c> dropped</c><00:04:45.759><c> all</c><00:04:45.960><c> the</c><00:04:46.160><c> tables</c>

00:04:46.749 --> 00:04:46.759 align:start position:0%
format so I do I dropped all the tables
 

00:04:46.759 --> 00:04:48.670 align:start position:0%
format so I do I dropped all the tables
I<00:04:46.880><c> do</c><00:04:47.120><c> choose</c>

00:04:48.670 --> 00:04:48.680 align:start position:0%
I do choose
 

00:04:48.680 --> 00:04:52.189 align:start position:0%
I do choose
file<00:04:49.680><c> the</c><00:04:49.840><c> most</c><00:04:50.120><c> recent</c><00:04:50.520><c> one</c><00:04:51.280><c> Excel</c><00:04:51.759><c> gives</c><00:04:51.960><c> you</c>

00:04:52.189 --> 00:04:52.199 align:start position:0%
file the most recent one Excel gives you
 

00:04:52.199 --> 00:04:54.830 align:start position:0%
file the most recent one Excel gives you
this<00:04:52.400><c> thing</c><00:04:52.600><c> of</c><00:04:52.960><c> Slash</c><00:04:53.440><c> two</c><00:04:54.000><c> so</c><00:04:54.280><c> that</c><00:04:54.560><c> that</c><00:04:54.680><c> way</c>

00:04:54.830 --> 00:04:54.840 align:start position:0%
this thing of Slash two so that that way
 

00:04:54.840 --> 00:04:57.070 align:start position:0%
this thing of Slash two so that that way
I<00:04:54.960><c> know</c><00:04:55.160><c> that</c><00:04:55.320><c> it</c><00:04:55.479><c> this</c><00:04:55.600><c> is</c><00:04:55.720><c> the</c><00:04:55.880><c> most</c><00:04:56.120><c> recent</c>

00:04:57.070 --> 00:04:57.080 align:start position:0%
I know that it this is the most recent
 

00:04:57.080 --> 00:05:00.870 align:start position:0%
I know that it this is the most recent
one<00:04:58.080><c> and</c><00:04:58.800><c> check</c><00:04:59.199><c> these</c><00:04:59.400><c> two</c>

00:05:00.870 --> 00:05:00.880 align:start position:0%
one and check these two
 

00:05:00.880 --> 00:05:03.629 align:start position:0%
one and check these two
go<00:05:01.880><c> we</c><00:05:02.039><c> already</c><00:05:02.320><c> discussed</c><00:05:02.880><c> those</c><00:05:03.080><c> two</c>

00:05:03.629 --> 00:05:03.639 align:start position:0%
go we already discussed those two
 

00:05:03.639 --> 00:05:09.590 align:start position:0%
go we already discussed those two
options<00:05:04.160><c> last</c><00:05:04.400><c> time</c><00:05:05.039><c> previous</c>

00:05:09.590 --> 00:05:09.600 align:start position:0%
 
 

00:05:09.600 --> 00:05:12.550 align:start position:0%
 
video<00:05:10.600><c> and</c><00:05:10.759><c> now</c><00:05:10.960><c> we</c><00:05:11.080><c> play</c><00:05:11.320><c> the</c><00:05:11.440><c> waiting</c>

00:05:12.550 --> 00:05:12.560 align:start position:0%
video and now we play the waiting
 

00:05:12.560 --> 00:05:16.390 align:start position:0%
video and now we play the waiting
game<00:05:13.560><c> Okay</c><00:05:13.919><c> cool</c><00:05:14.800><c> so</c><00:05:15.160><c> everything</c><00:05:15.440><c> is</c><00:05:15.680><c> in</c><00:05:15.919><c> there</c>

00:05:16.390 --> 00:05:16.400 align:start position:0%
game Okay cool so everything is in there
 

00:05:16.400 --> 00:05:19.550 align:start position:0%
game Okay cool so everything is in there
now<00:05:16.680><c> I</c><00:05:16.800><c> go</c><00:05:17.000><c> into</c><00:05:17.720><c> structure</c><00:05:18.720><c> of</c><00:05:18.960><c> that</c><00:05:19.199><c> table</c>

00:05:19.550 --> 00:05:19.560 align:start position:0%
now I go into structure of that table
 

00:05:19.560 --> 00:05:24.550 align:start position:0%
now I go into structure of that table
one<00:05:19.919><c> over</c><00:05:20.199><c> here</c><00:05:20.960><c> I</c><00:05:21.280><c> I</c><00:05:21.360><c> select</c><00:05:21.919><c> table</c><00:05:22.520><c> one</c><00:05:23.560><c> and</c>

00:05:24.550 --> 00:05:24.560 align:start position:0%
one over here I I select table one and
 

00:05:24.560 --> 00:05:27.590 align:start position:0%
one over here I I select table one and
ah<00:05:24.919><c> okay</c><00:05:25.440><c> in</c><00:05:26.000><c> Click</c><00:05:26.400><c> time</c><00:05:26.919><c> I</c><00:05:27.039><c> go</c><00:05:27.199><c> into</c>

00:05:27.590 --> 00:05:27.600 align:start position:0%
ah okay in Click time I go into
 

00:05:27.600 --> 00:05:30.550 align:start position:0%
ah okay in Click time I go into
structure<00:05:28.120><c> of</c><00:05:28.360><c> that</c><00:05:28.639><c> table</c><00:05:30.000><c> okay</c><00:05:30.240><c> everything</c>

00:05:30.550 --> 00:05:30.560 align:start position:0%
structure of that table okay everything
 

00:05:30.560 --> 00:05:33.590 align:start position:0%
structure of that table okay everything
is<00:05:30.680><c> in</c><00:05:30.840><c> varar</c><00:05:31.520><c> over</c><00:05:31.840><c> here</c><00:05:32.360><c> again</c><00:05:32.759><c> I</c><00:05:32.960><c> press</c>

00:05:33.590 --> 00:05:33.600 align:start position:0%
is in varar over here again I press
 

00:05:33.600 --> 00:05:37.029 align:start position:0%
is in varar over here again I press
change<00:05:34.600><c> I</c><00:05:34.919><c> do</c><00:05:35.120><c> not</c><00:05:35.360><c> need</c><00:05:35.639><c> this</c><00:05:35.840><c> length</c><00:05:36.360><c> values</c>

00:05:37.029 --> 00:05:37.039 align:start position:0%
change I do not need this length values
 

00:05:37.039 --> 00:05:40.189 align:start position:0%
change I do not need this length values
thing<00:05:37.560><c> I</c><00:05:37.680><c> need</c><00:05:37.840><c> to</c><00:05:38.000><c> change</c><00:05:38.440><c> everything</c><00:05:39.039><c> into</c><00:05:39.759><c> a</c>

00:05:40.189 --> 00:05:40.199 align:start position:0%
thing I need to change everything into a
 

00:05:40.199 --> 00:05:44.189 align:start position:0%
thing I need to change everything into a
date<00:05:40.919><c> oky</c><00:05:41.560><c> doie</c><00:05:42.560><c> and</c>

00:05:44.189 --> 00:05:44.199 align:start position:0%
date oky doie and
 

00:05:44.199 --> 00:05:46.990 align:start position:0%
date oky doie and
save<00:05:45.199><c> really</c><00:05:45.479><c> awesome</c><00:05:46.120><c> that's</c><00:05:46.319><c> changed</c><00:05:46.720><c> into</c>

00:05:46.990 --> 00:05:47.000 align:start position:0%
save really awesome that's changed into
 

00:05:47.000 --> 00:05:49.350 align:start position:0%
save really awesome that's changed into
a<00:05:47.160><c> date</c><00:05:47.440><c> we</c><00:05:47.560><c> go</c><00:05:47.800><c> back</c><00:05:48.000><c> into</c><00:05:48.360><c> browse</c><00:05:48.960><c> and</c><00:05:49.080><c> we</c><00:05:49.160><c> see</c>

00:05:49.350 --> 00:05:49.360 align:start position:0%
a date we go back into browse and we see
 

00:05:49.360 --> 00:05:51.309 align:start position:0%
a date we go back into browse and we see
that<00:05:49.520><c> everything</c><00:05:49.759><c> is</c><00:05:49.919><c> still</c><00:05:50.120><c> in</c><00:05:50.319><c> date</c><00:05:50.720><c> format</c>

00:05:51.309 --> 00:05:51.319 align:start position:0%
that everything is still in date format
 

00:05:51.319 --> 00:05:53.710 align:start position:0%
that everything is still in date format
right<00:05:51.440><c> so</c><00:05:51.560><c> it</c><00:05:51.639><c> doesn't</c><00:05:51.880><c> all</c><00:05:52.039><c> go</c><00:05:52.160><c> to</c><00:05:52.280><c> 0</c><00:05:52.639><c> 0</c><00:05:53.000><c> 0</c><00:05:53.520><c> and</c>

00:05:53.710 --> 00:05:53.720 align:start position:0%
right so it doesn't all go to 0 0 0 and
 

00:05:53.720 --> 00:05:55.309 align:start position:0%
right so it doesn't all go to 0 0 0 and
that<00:05:53.880><c> helps</c><00:05:54.080><c> us</c><00:05:54.360><c> validate</c><00:05:54.840><c> that</c><00:05:55.000><c> everything</c>

00:05:55.309 --> 00:05:55.319 align:start position:0%
that helps us validate that everything
 

00:05:55.319 --> 00:05:58.469 align:start position:0%
that helps us validate that everything
got<00:05:55.600><c> converted</c><00:05:56.479><c> correctly</c><00:05:57.479><c> I</c><00:05:57.600><c> go</c><00:05:57.880><c> back</c><00:05:58.080><c> into</c>

00:05:58.469 --> 00:05:58.479 align:start position:0%
got converted correctly I go back into
 

00:05:58.479 --> 00:06:00.430 align:start position:0%
got converted correctly I go back into
the<00:05:58.639><c> structure</c><00:05:59.199><c> column</c><00:05:59.880><c> and</c><00:06:00.000><c> I'm</c><00:06:00.120><c> going</c><00:06:00.199><c> to</c><00:06:00.319><c> do</c>

00:06:00.430 --> 00:06:00.440 align:start position:0%
the structure column and I'm going to do
 

00:06:00.440 --> 00:06:02.710 align:start position:0%
the structure column and I'm going to do
the<00:06:00.600><c> exact</c><00:06:00.919><c> same</c><00:06:01.240><c> process</c><00:06:01.560><c> for</c><00:06:01.800><c> all</c><00:06:01.960><c> of</c>

00:06:02.710 --> 00:06:02.720 align:start position:0%
the exact same process for all of
 

00:06:02.720 --> 00:06:05.950 align:start position:0%
the exact same process for all of
those<00:06:03.720><c> remove</c><00:06:04.160><c> length</c><00:06:04.639><c> turn</c><00:06:04.840><c> it</c><00:06:05.000><c> into</c><00:06:05.240><c> a</c><00:06:05.479><c> date</c>

00:06:05.950 --> 00:06:05.960 align:start position:0%
those remove length turn it into a date
 

00:06:05.960 --> 00:06:08.550 align:start position:0%
those remove length turn it into a date
all<00:06:06.080><c> right</c><00:06:06.319><c> pretty</c><00:06:06.599><c> simple</c><00:06:07.160><c> stuff</c><00:06:08.160><c> just</c><00:06:08.360><c> turn</c>

00:06:08.550 --> 00:06:08.560 align:start position:0%
all right pretty simple stuff just turn
 

00:06:08.560 --> 00:06:13.270 align:start position:0%
all right pretty simple stuff just turn
it<00:06:08.720><c> into</c><00:06:09.000><c> a</c><00:06:09.520><c> date</c><00:06:10.520><c> save</c><00:06:11.400><c> and</c><00:06:11.639><c> keep</c><00:06:12.160><c> keep</c><00:06:12.360><c> moving</c>

00:06:13.270 --> 00:06:13.280 align:start position:0%
it into a date save and keep keep moving
 

00:06:13.280 --> 00:06:18.309 align:start position:0%
it into a date save and keep keep moving
forward<00:06:14.280><c> okay</c>

00:06:18.309 --> 00:06:18.319 align:start position:0%
 
 

00:06:18.319 --> 00:06:22.270 align:start position:0%
 
change

00:06:22.270 --> 00:06:22.280 align:start position:0%
 
 

00:06:22.280 --> 00:06:25.950 align:start position:0%
 
date<00:06:23.280><c> okay</c>

00:06:25.950 --> 00:06:25.960 align:start position:0%
 
 

00:06:25.960 --> 00:06:28.469 align:start position:0%
 
save<00:06:26.960><c> all</c><00:06:27.120><c> right</c><00:06:27.280><c> so</c><00:06:27.520><c> click</c><00:06:27.800><c> time</c><00:06:28.080><c> install</c>

00:06:28.469 --> 00:06:28.479 align:start position:0%
save all right so click time install
 

00:06:28.479 --> 00:06:30.070 align:start position:0%
save all right so click time install
time<00:06:28.680><c> and</c><00:06:28.880><c> install</c><00:06:29.319><c> date</c><00:06:29.680><c> have</c><00:06:29.800><c> all</c><00:06:29.919><c> been</c>

00:06:30.070 --> 00:06:30.080 align:start position:0%
time and install date have all been
 

00:06:30.080 --> 00:06:32.150 align:start position:0%
time and install date have all been
changed<00:06:30.400><c> to</c><00:06:30.680><c> date</c><00:06:31.039><c> format</c><00:06:31.680><c> everything</c><00:06:31.960><c> else</c>

00:06:32.150 --> 00:06:32.160 align:start position:0%
changed to date format everything else
 

00:06:32.160 --> 00:06:35.350 align:start position:0%
changed to date format everything else
is<00:06:32.440><c> regular</c><00:06:32.960><c> text</c><00:06:33.960><c> that's</c><00:06:34.240><c> good</c><00:06:34.479><c> enough</c><00:06:34.960><c> City</c>

00:06:35.350 --> 00:06:35.360 align:start position:0%
is regular text that's good enough City
 

00:06:35.360 --> 00:06:37.550 align:start position:0%
is regular text that's good enough City
postal<00:06:35.759><c> code</c><00:06:36.039><c> device</c><00:06:36.400><c> type</c><00:06:36.639><c> OS</c><00:06:37.000><c> version</c><00:06:37.319><c> click</c>

00:06:37.550 --> 00:06:37.560 align:start position:0%
postal code device type OS version click
 

00:06:37.560 --> 00:06:38.830 align:start position:0%
postal code device type OS version click
to<00:06:37.759><c> install</c>

00:06:38.830 --> 00:06:38.840 align:start position:0%
to install
 

00:06:38.840 --> 00:06:41.430 align:start position:0%
to install
time<00:06:39.840><c> that's</c><00:06:40.080><c> really</c><00:06:40.319><c> a</c><00:06:40.520><c> number</c><00:06:40.960><c> maybe</c><00:06:41.240><c> there</c>

00:06:41.430 --> 00:06:41.440 align:start position:0%
time that's really a number maybe there
 

00:06:41.440 --> 00:06:44.270 align:start position:0%
time that's really a number maybe there
is<00:06:41.720><c> maybe</c><00:06:41.960><c> there's</c><00:06:42.160><c> a</c><00:06:42.319><c> duration</c><00:06:43.080><c> column</c><00:06:43.759><c> okay</c>

00:06:44.270 --> 00:06:44.280 align:start position:0%
is maybe there's a duration column okay
 

00:06:44.280 --> 00:06:47.189 align:start position:0%
is maybe there's a duration column okay
change<00:06:45.280><c> this</c><00:06:45.400><c> should</c><00:06:45.680><c> really</c><00:06:45.919><c> be</c><00:06:46.319><c> just</c><00:06:47.039><c> uh</c>

00:06:47.189 --> 00:06:47.199 align:start position:0%
change this should really be just uh
 

00:06:47.199 --> 00:06:48.430 align:start position:0%
change this should really be just uh
let's<00:06:47.400><c> see</c><00:06:47.599><c> I'm</c><00:06:47.720><c> just</c><00:06:47.840><c> going</c><00:06:47.960><c> to</c><00:06:48.080><c> change</c><00:06:48.280><c> it</c>

00:06:48.430 --> 00:06:48.440 align:start position:0%
let's see I'm just going to change it
 

00:06:48.440 --> 00:06:50.189 align:start position:0%
let's see I'm just going to change it
into<00:06:48.800><c> time</c><00:06:49.160><c> because</c><00:06:49.400><c> it</c><00:06:49.520><c> seems</c><00:06:49.759><c> like</c><00:06:49.880><c> it's</c><00:06:50.000><c> a</c>

00:06:50.189 --> 00:06:50.199 align:start position:0%
into time because it seems like it's a
 

00:06:50.199 --> 00:06:52.909 align:start position:0%
into time because it seems like it's a
duration<00:06:51.120><c> and</c><00:06:51.800><c> let's</c><00:06:51.960><c> see</c><00:06:52.120><c> if</c><00:06:52.240><c> the</c><00:06:52.400><c> data</c><00:06:52.680><c> comes</c>

00:06:52.909 --> 00:06:52.919 align:start position:0%
duration and let's see if the data comes
 

00:06:52.919 --> 00:06:54.790 align:start position:0%
duration and let's see if the data comes
out<00:06:53.280><c> okay</c>

00:06:54.790 --> 00:06:54.800 align:start position:0%
out okay
 

00:06:54.800 --> 00:06:57.710 align:start position:0%
out okay
save<00:06:55.800><c> all</c><00:06:55.960><c> right</c><00:06:56.479><c> good</c><00:06:56.680><c> enough</c><00:06:57.000><c> so</c><00:06:57.199><c> that's</c><00:06:57.440><c> the</c>

00:06:57.710 --> 00:06:57.720 align:start position:0%
save all right good enough so that's the
 

00:06:57.720 --> 00:07:00.110 align:start position:0%
save all right good enough so that's the
time<00:06:58.160><c> a</c><00:06:58.360><c> new</c><00:06:58.639><c> account</c><00:06:58.840><c> in</c><00:06:59.039><c> first</c><00:06:59.280><c> dep</c><00:06:59.800><c> it</c><00:06:59.919><c> is</c>

00:07:00.110 --> 00:07:00.120 align:start position:0%
time a new account in first dep it is
 

00:07:00.120 --> 00:07:03.230 align:start position:0%
time a new account in first dep it is
either<00:07:00.280><c> a</c><00:07:00.479><c> zero</c><00:07:00.800><c> or</c><00:07:00.960><c> a</c><00:07:01.160><c> one</c><00:07:02.160><c> good</c><00:07:02.400><c> enough</c><00:07:03.000><c> uh</c>

00:07:03.230 --> 00:07:03.240 align:start position:0%
either a zero or a one good enough uh
 

00:07:03.240 --> 00:07:04.950 align:start position:0%
either a zero or a one good enough uh
int<00:07:03.720><c> int</c><00:07:04.000><c> all</c><00:07:04.120><c> right</c><00:07:04.319><c> this</c><00:07:04.440><c> is</c><00:07:04.599><c> already</c>

00:07:04.950 --> 00:07:04.960 align:start position:0%
int int all right this is already
 

00:07:04.960 --> 00:07:07.230 align:start position:0%
int int all right this is already
translated<00:07:05.599><c> into</c><00:07:05.879><c> an</c><00:07:06.080><c> integer</c><00:07:06.800><c> so</c><00:07:07.000><c> that's</c>

00:07:07.230 --> 00:07:07.240 align:start position:0%
translated into an integer so that's
 

00:07:07.240 --> 00:07:09.350 align:start position:0%
translated into an integer so that's
perfect<00:07:08.160><c> all</c><00:07:08.280><c> right</c><00:07:08.560><c> guys</c><00:07:08.680><c> so</c><00:07:08.879><c> everything</c><00:07:09.160><c> is</c>

00:07:09.350 --> 00:07:09.360 align:start position:0%
perfect all right guys so everything is
 

00:07:09.360 --> 00:07:12.550 align:start position:0%
perfect all right guys so everything is
all<00:07:09.599><c> set</c><00:07:10.039><c> I</c><00:07:10.160><c> believe</c><00:07:10.599><c> we</c><00:07:10.720><c> can</c><00:07:10.960><c> now</c><00:07:11.360><c> export</c><00:07:12.000><c> this</c>

00:07:12.550 --> 00:07:12.560 align:start position:0%
all set I believe we can now export this
 

00:07:12.560 --> 00:07:15.749 align:start position:0%
all set I believe we can now export this
local<00:07:12.960><c> SQL</c><00:07:13.919><c> database</c><00:07:14.919><c> uh</c><00:07:15.080><c> wait</c><00:07:15.240><c> a</c><00:07:15.360><c> second</c><00:07:15.639><c> you</c>

00:07:15.749 --> 00:07:15.759 align:start position:0%
local SQL database uh wait a second you
 

00:07:15.759 --> 00:07:17.270 align:start position:0%
local SQL database uh wait a second you
know<00:07:15.879><c> what</c><00:07:16.199><c> before</c><00:07:16.440><c> we</c><00:07:16.560><c> do</c><00:07:16.759><c> that</c><00:07:16.919><c> I</c><00:07:17.000><c> want</c><00:07:17.120><c> to</c>

00:07:17.270 --> 00:07:17.280 align:start position:0%
know what before we do that I want to
 

00:07:17.280 --> 00:07:20.390 align:start position:0%
know what before we do that I want to
change<00:07:17.680><c> the</c><00:07:17.879><c> title</c><00:07:18.199><c> of</c><00:07:18.360><c> this</c><00:07:18.879><c> of</c><00:07:19.080><c> this</c><00:07:19.560><c> the</c><00:07:20.199><c> of</c>

00:07:20.390 --> 00:07:20.400 align:start position:0%
change the title of this of this the of
 

00:07:20.400 --> 00:07:22.430 align:start position:0%
change the title of this of this the of
this<00:07:20.639><c> table</c><00:07:21.599><c> because</c><00:07:21.840><c> you</c><00:07:21.960><c> can</c><00:07:22.080><c> see</c><00:07:22.280><c> it's</c>

00:07:22.430 --> 00:07:22.440 align:start position:0%
this table because you can see it's
 

00:07:22.440 --> 00:07:24.270 align:start position:0%
this table because you can see it's
called<00:07:22.759><c> table</c><00:07:23.160><c> space</c><00:07:23.599><c> one</c><00:07:23.919><c> that's</c><00:07:24.080><c> going</c><00:07:24.160><c> to</c>

00:07:24.270 --> 00:07:24.280 align:start position:0%
called table space one that's going to
 

00:07:24.280 --> 00:07:26.710 align:start position:0%
called table space one that's going to
be<00:07:24.360><c> a</c><00:07:24.520><c> pain</c><00:07:24.680><c> in</c><00:07:24.840><c> the</c><00:07:25.120><c> tus</c><00:07:26.120><c> so</c><00:07:26.360><c> I'm</c><00:07:26.479><c> just</c><00:07:26.599><c> going</c>

00:07:26.710 --> 00:07:26.720 align:start position:0%
be a pain in the tus so I'm just going
 

00:07:26.720 --> 00:07:29.070 align:start position:0%
be a pain in the tus so I'm just going
to<00:07:26.800><c> do</c><00:07:26.919><c> a</c><00:07:27.120><c> quick</c><00:07:27.360><c> Google</c><00:07:27.759><c> search</c><00:07:28.520><c> of</c><00:07:28.680><c> how</c><00:07:28.800><c> to</c>

00:07:29.070 --> 00:07:29.080 align:start position:0%
to do a quick Google search of how to
 

00:07:29.080 --> 00:07:31.070 align:start position:0%
to do a quick Google search of how to
let's<00:07:29.199><c> see</c><00:07:29.280><c> if</c><00:07:29.360><c> I</c><00:07:29.599><c> can</c><00:07:29.759><c> change</c><00:07:30.120><c> the</c><00:07:30.319><c> table</c><00:07:30.639><c> name</c>

00:07:31.070 --> 00:07:31.080 align:start position:0%
let's see if I can change the table name
 

00:07:31.080 --> 00:07:34.869 align:start position:0%
let's see if I can change the table name
here<00:07:31.680><c> I</c><00:07:31.879><c> can't</c><00:07:32.400><c> I</c><00:07:32.479><c> was</c><00:07:32.680><c> trying</c><00:07:33.520><c> to</c><00:07:34.520><c> uh</c><00:07:34.720><c> I'm</c>

00:07:34.869 --> 00:07:34.879 align:start position:0%
here I can't I was trying to uh I'm
 

00:07:34.879 --> 00:07:38.550 align:start position:0%
here I can't I was trying to uh I'm
doing<00:07:35.120><c> a</c><00:07:35.319><c> Google</c><00:07:35.639><c> search</c><00:07:36.560><c> uh</c><00:07:36.759><c> change</c><00:07:37.759><c> table</c>

00:07:38.550 --> 00:07:38.560 align:start position:0%
doing a Google search uh change table
 

00:07:38.560 --> 00:07:43.230 align:start position:0%
doing a Google search uh change table
name<00:07:39.240><c> in</c><00:07:39.599><c> SQL</c><00:07:40.400><c> okay</c><00:07:40.520><c> for</c><00:07:40.759><c> my</c><00:07:41.319><c> SQL</c><00:07:42.319><c> my</c><00:07:42.560><c> SQL</c>

00:07:43.230 --> 00:07:43.240 align:start position:0%
name in SQL okay for my SQL my SQL
 

00:07:43.240 --> 00:07:45.629 align:start position:0%
name in SQL okay for my SQL my SQL
rename<00:07:43.800><c> table</c><00:07:44.280><c> yes</c><00:07:44.520><c> rename</c><00:07:44.960><c> table</c><00:07:45.240><c> is</c><00:07:45.360><c> what</c><00:07:45.520><c> we</c>

00:07:45.629 --> 00:07:45.639 align:start position:0%
rename table yes rename table is what we
 

00:07:45.639 --> 00:07:48.070 align:start position:0%
rename table yes rename table is what we
want<00:07:45.759><c> to</c>

00:07:48.070 --> 00:07:48.080 align:start position:0%
 
 

00:07:48.080 --> 00:07:52.029 align:start position:0%
 
do<00:07:49.080><c> okay</c><00:07:49.440><c> rename</c><00:07:50.080><c> table</c><00:07:50.680><c> old</c><00:07:51.080><c> table</c><00:07:51.479><c> to</c><00:07:51.720><c> new</c>

00:07:52.029 --> 00:07:52.039 align:start position:0%
do okay rename table old table to new
 

00:07:52.039 --> 00:07:56.270 align:start position:0%
do okay rename table old table to new
table<00:07:52.560><c> perfect</c><00:07:53.560><c> contrl</c><00:07:54.199><c> C</c><00:07:55.199><c> zamp</c><00:07:55.599><c> allows</c><00:07:55.919><c> us</c><00:07:56.080><c> to</c>

00:07:56.270 --> 00:07:56.280 align:start position:0%
table perfect contrl C zamp allows us to
 

00:07:56.280 --> 00:07:57.270 align:start position:0%
table perfect contrl C zamp allows us to
do

00:07:57.270 --> 00:07:57.280 align:start position:0%
do
 

00:07:57.280 --> 00:08:00.710 align:start position:0%
do
that<00:07:58.280><c> rename</c><00:07:58.879><c> table</c>

00:08:00.710 --> 00:08:00.720 align:start position:0%
that rename table
 

00:08:00.720 --> 00:08:03.350 align:start position:0%
that rename table
table<00:08:01.680><c> okay</c><00:08:02.039><c> I'll</c><00:08:02.199><c> put</c><00:08:02.360><c> it</c><00:08:02.479><c> in</c>

00:08:03.350 --> 00:08:03.360 align:start position:0%
table okay I'll put it in
 

00:08:03.360 --> 00:08:06.909 align:start position:0%
table okay I'll put it in
quotations<00:08:04.360><c> table</c><00:08:05.240><c> one</c><00:08:05.919><c> two</c><00:08:06.479><c> what</c><00:08:06.560><c> do</c><00:08:06.680><c> we</c><00:08:06.800><c> want</c>

00:08:06.909 --> 00:08:06.919 align:start position:0%
quotations table one two what do we want
 

00:08:06.919 --> 00:08:10.469 align:start position:0%
quotations table one two what do we want
to<00:08:07.120><c> name</c><00:08:07.319><c> it</c><00:08:07.680><c> we</c><00:08:07.759><c> want</c><00:08:07.879><c> to</c><00:08:08.120><c> call</c><00:08:08.599><c> it</c><00:08:09.599><c> uh</c><00:08:09.960><c> uh</c>

00:08:10.469 --> 00:08:10.479 align:start position:0%
to name it we want to call it uh uh
 

00:08:10.479 --> 00:08:14.309 align:start position:0%
to name it we want to call it uh uh
let's<00:08:10.680><c> see</c><00:08:11.319><c> add</c><00:08:11.840><c> data</c><00:08:12.319><c> or</c><00:08:12.639><c> Roi</c><00:08:13.360><c> data</c>

00:08:14.309 --> 00:08:14.319 align:start position:0%
let's see add data or Roi data
 

00:08:14.319 --> 00:08:18.230 align:start position:0%
let's see add data or Roi data
conversion<00:08:15.319><c> uncore</c><00:08:16.039><c> data</c><00:08:16.919><c> okay</c><00:08:17.280><c> good</c><00:08:17.479><c> enough</c>

00:08:18.230 --> 00:08:18.240 align:start position:0%
conversion uncore data okay good enough
 

00:08:18.240 --> 00:08:21.510 align:start position:0%
conversion uncore data okay good enough
conversion<00:08:18.879><c> data</c><00:08:19.840><c> and</c><00:08:20.240><c> we</c><00:08:20.360><c> are</c><00:08:20.639><c> going</c><00:08:20.759><c> to</c><00:08:21.360><c> go</c>

00:08:21.510 --> 00:08:21.520 align:start position:0%
conversion data and we are going to go
 

00:08:21.520 --> 00:08:25.230 align:start position:0%
conversion data and we are going to go
ahead<00:08:21.919><c> and</c><00:08:22.280><c> run</c><00:08:23.039><c> it</c><00:08:24.039><c> schnikes</c><00:08:24.560><c> rename</c><00:08:24.960><c> table</c>

00:08:25.230 --> 00:08:25.240 align:start position:0%
ahead and run it schnikes rename table
 

00:08:25.240 --> 00:08:27.149 align:start position:0%
ahead and run it schnikes rename table
table<00:08:25.479><c> one</c><00:08:25.639><c> to</c><00:08:25.800><c> conversion</c><00:08:26.319><c> data</c><00:08:26.759><c> you</c><00:08:26.879><c> have</c><00:08:27.000><c> an</c>

00:08:27.149 --> 00:08:27.159 align:start position:0%
table one to conversion data you have an
 

00:08:27.159 --> 00:08:29.510 align:start position:0%
table one to conversion data you have an
error<00:08:27.479><c> in</c><00:08:27.599><c> your</c><00:08:27.800><c> SQL</c><00:08:28.400><c> syntax</c><00:08:29.000><c> check</c><00:08:29.199><c> the</c>

00:08:29.510 --> 00:08:29.520 align:start position:0%
error in your SQL syntax check the
 

00:08:29.520 --> 00:08:31.829 align:start position:0%
error in your SQL syntax check the
manual<00:08:29.800><c> that</c><00:08:29.960><c> corresponds</c><00:08:30.479><c> to</c><00:08:30.639><c> your</c>

00:08:31.829 --> 00:08:31.839 align:start position:0%
manual that corresponds to your
 

00:08:31.839 --> 00:08:34.909 align:start position:0%
manual that corresponds to your
blank<00:08:32.839><c> shks</c><00:08:33.599><c> okay</c><00:08:34.120><c> what</c><00:08:34.240><c> if</c><00:08:34.360><c> I</c><00:08:34.479><c> put</c><00:08:34.640><c> it</c><00:08:34.760><c> in</c>

00:08:34.909 --> 00:08:34.919 align:start position:0%
blank shks okay what if I put it in
 

00:08:34.919 --> 00:08:38.709 align:start position:0%
blank shks okay what if I put it in
single<00:08:35.360><c> quotes</c><00:08:35.919><c> is</c><00:08:36.039><c> that</c><00:08:36.240><c> going</c><00:08:36.320><c> to</c><00:08:36.519><c> fix</c>

00:08:38.709 --> 00:08:38.719 align:start position:0%
single quotes is that going to fix
 

00:08:38.719 --> 00:08:45.030 align:start position:0%
single quotes is that going to fix
it<00:08:39.719><c> to</c><00:08:40.159><c> conversion</c><00:08:40.839><c> data</c><00:08:41.519><c> and</c>

00:08:45.030 --> 00:08:45.040 align:start position:0%
 
 

00:08:45.040 --> 00:08:51.630 align:start position:0%
 
go<00:08:46.040><c> okay</c><00:08:46.440><c> my</c><00:08:46.800><c> SQL</c><00:08:47.959><c> rename</c><00:08:49.240><c> table</c><00:08:50.240><c> with</c>

00:08:51.630 --> 00:08:51.640 align:start position:0%
go okay my SQL rename table with
 

00:08:51.640 --> 00:08:54.630 align:start position:0%
go okay my SQL rename table with
space<00:08:52.640><c> rename</c><00:08:53.120><c> table</c><00:08:53.480><c> with</c><00:08:53.720><c> spaces</c><00:08:54.440><c> okay</c><00:08:54.560><c> how</c>

00:08:54.630 --> 00:08:54.640 align:start position:0%
space rename table with spaces okay how
 

00:08:54.640 --> 00:08:56.710 align:start position:0%
space rename table with spaces okay how
do<00:08:54.760><c> we</c><00:08:54.880><c> do</c><00:08:55.040><c> that</c><00:08:55.240><c> rename</c><00:08:55.680><c> table</c><00:08:56.200><c> ah</c><00:08:56.399><c> okay</c><00:08:56.600><c> the</c>

00:08:56.710 --> 00:08:56.720 align:start position:0%
do we do that rename table ah okay the
 

00:08:56.720 --> 00:09:00.350 align:start position:0%
do we do that rename table ah okay the
back<00:08:57.000><c> ticks</c><00:08:57.640><c> oh</c><00:08:57.920><c> okay</c><00:08:58.120><c> of</c><00:08:58.440><c> course</c><00:08:59.440><c> rename</c>

00:09:00.350 --> 00:09:00.360 align:start position:0%
back ticks oh okay of course rename
 

00:09:00.360 --> 00:09:02.310 align:start position:0%
back ticks oh okay of course rename
table<00:09:01.360><c> back</c>

00:09:02.310 --> 00:09:02.320 align:start position:0%
table back
 

00:09:02.320 --> 00:09:04.430 align:start position:0%
table back
tick<00:09:03.320><c> back</c>

00:09:04.430 --> 00:09:04.440 align:start position:0%
tick back
 

00:09:04.440 --> 00:09:08.470 align:start position:0%
tick back
tick<00:09:05.440><c> and</c><00:09:06.160><c> go</c><00:09:07.160><c> all</c><00:09:07.399><c> right</c><00:09:07.760><c> fine</c><00:09:08.240><c> now</c>

00:09:08.470 --> 00:09:08.480 align:start position:0%
tick and go all right fine now
 

00:09:08.480 --> 00:09:11.750 align:start position:0%
tick and go all right fine now
everything<00:09:08.800><c> is</c><00:09:09.079><c> good</c><00:09:10.000><c> uh</c><00:09:10.680><c> as</c><00:09:10.800><c> you</c><00:09:10.920><c> can</c><00:09:11.079><c> see</c>

00:09:11.750 --> 00:09:11.760 align:start position:0%
everything is good uh as you can see
 

00:09:11.760 --> 00:09:13.230 align:start position:0%
everything is good uh as you can see
this<00:09:11.880><c> is</c><00:09:12.040><c> going</c><00:09:12.160><c> to</c><00:09:12.279><c> be</c>

00:09:13.230 --> 00:09:13.240 align:start position:0%
this is going to be
 

00:09:13.240 --> 00:09:15.389 align:start position:0%
this is going to be
renamed<00:09:14.240><c> um</c><00:09:14.320><c> I'm</c><00:09:14.480><c> going</c><00:09:14.560><c> to</c><00:09:14.760><c> what</c><00:09:14.839><c> if</c><00:09:15.000><c> I</c>

00:09:15.389 --> 00:09:15.399 align:start position:0%
renamed um I'm going to what if I
 

00:09:15.399 --> 00:09:17.590 align:start position:0%
renamed um I'm going to what if I
minimize<00:09:15.800><c> and</c><00:09:15.920><c> then</c><00:09:16.160><c> re</c><00:09:16.480><c> all</c><00:09:16.600><c> right</c><00:09:17.160><c> it's</c><00:09:17.440><c> ah</c>

00:09:17.590 --> 00:09:17.600 align:start position:0%
minimize and then re all right it's ah
 

00:09:17.600 --> 00:09:18.750 align:start position:0%
minimize and then re all right it's ah
there<00:09:17.680><c> you</c><00:09:17.800><c> go</c><00:09:17.959><c> the</c><00:09:18.040><c> table's</c><00:09:18.399><c> name</c><00:09:18.560><c> is</c>

00:09:18.750 --> 00:09:18.760 align:start position:0%
there you go the table's name is
 

00:09:18.760 --> 00:09:20.670 align:start position:0%
there you go the table's name is
conversion<00:09:19.279><c> uncore</c><00:09:19.959><c> data</c><00:09:20.240><c> all</c><00:09:20.360><c> right</c><00:09:20.519><c> the</c>

00:09:20.670 --> 00:09:20.680 align:start position:0%
conversion uncore data all right the
 

00:09:20.680 --> 00:09:22.710 align:start position:0%
conversion uncore data all right the
less<00:09:21.120><c> spaces</c><00:09:21.600><c> we</c><00:09:21.760><c> have</c><00:09:21.920><c> in</c><00:09:22.079><c> the</c><00:09:22.279><c> data</c><00:09:22.560><c> the</c>

00:09:22.710 --> 00:09:22.720 align:start position:0%
less spaces we have in the data the
 

00:09:22.720 --> 00:09:28.550 align:start position:0%
less spaces we have in the data the
better<00:09:23.120><c> export</c><00:09:24.200><c> this</c><00:09:25.200><c> and</c><00:09:25.839><c> go</c><00:09:26.839><c> Okay</c><00:09:27.560><c> C</c><00:09:27.839><c> CSV</c><00:09:28.399><c> to</c>

00:09:28.550 --> 00:09:28.560 align:start position:0%
better export this and go Okay C CSV to
 

00:09:28.560 --> 00:09:31.550 align:start position:0%
better export this and go Okay C CSV to
SQL<00:09:29.040><c> demo</c><00:09:29.440><c> oh</c><00:09:29.880><c> okay</c><00:09:30.240><c> nice</c><00:09:30.760><c> all</c><00:09:30.880><c> right</c><00:09:31.279><c> so</c><00:09:31.440><c> we</c>

00:09:31.550 --> 00:09:31.560 align:start position:0%
SQL demo oh okay nice all right so we
 

00:09:31.560 --> 00:09:33.509 align:start position:0%
SQL demo oh okay nice all right so we
have<00:09:31.760><c> successfully</c><00:09:32.440><c> exported</c><00:09:32.959><c> the</c><00:09:33.079><c> data</c><00:09:33.399><c> in</c>

00:09:33.509 --> 00:09:33.519 align:start position:0%
have successfully exported the data in
 

00:09:33.519 --> 00:09:36.470 align:start position:0%
have successfully exported the data in
the<00:09:33.680><c> next</c><00:09:34.000><c> video</c><00:09:34.480><c> we're</c><00:09:34.920><c> into</c><00:09:35.600><c> we've</c><00:09:36.240><c> we've</c>

00:09:36.470 --> 00:09:36.480 align:start position:0%
the next video we're into we've we've
 

00:09:36.480 --> 00:09:40.110 align:start position:0%
the next video we're into we've we've
exported<00:09:36.880><c> the</c><00:09:36.959><c> data</c><00:09:37.200><c> into</c><00:09:37.399><c> a</c><00:09:37.560><c> SQL</c><00:09:38.200><c> file</c><00:09:39.200><c> um</c><00:09:39.920><c> and</c>

00:09:40.110 --> 00:09:40.120 align:start position:0%
exported the data into a SQL file um and
 

00:09:40.120 --> 00:09:42.389 align:start position:0%
exported the data into a SQL file um and
now<00:09:40.360><c> we</c><00:09:40.480><c> need</c><00:09:40.600><c> to</c><00:09:40.800><c> take</c><00:09:41.040><c> that</c><00:09:41.200><c> SQL</c><00:09:41.680><c> file</c><00:09:42.200><c> we're</c>

00:09:42.389 --> 00:09:42.399 align:start position:0%
now we need to take that SQL file we're
 

00:09:42.399 --> 00:09:44.230 align:start position:0%
now we need to take that SQL file we're
going<00:09:42.519><c> to</c><00:09:43.160><c> we're</c><00:09:43.320><c> going</c><00:09:43.440><c> to</c><00:09:43.600><c> take</c><00:09:43.800><c> all</c><00:09:44.040><c> that</c>

00:09:44.230 --> 00:09:44.240 align:start position:0%
going to we're going to take all that
 

00:09:44.240 --> 00:09:47.069 align:start position:0%
going to we're going to take all that
data<00:09:44.519><c> and</c><00:09:44.720><c> upload</c><00:09:45.079><c> it</c><00:09:45.480><c> to</c><00:09:45.680><c> our</c><00:09:45.959><c> Cloud</c><00:09:46.399><c> mySQL</c>

00:09:47.069 --> 00:09:47.079 align:start position:0%
data and upload it to our Cloud mySQL
 

00:09:47.079 --> 00:09:49.190 align:start position:0%
data and upload it to our Cloud mySQL
database<00:09:47.680><c> in</c><00:09:47.800><c> the</c><00:09:48.000><c> next</c><00:09:48.320><c> video</c><00:09:48.880><c> all</c><00:09:49.000><c> right</c>

00:09:49.190 --> 00:09:49.200 align:start position:0%
database in the next video all right
 

00:09:49.200 --> 00:09:51.350 align:start position:0%
database in the next video all right
guys<00:09:49.320><c> so</c><00:09:49.480><c> thanks</c><00:09:49.640><c> for</c><00:09:49.800><c> tuning</c><00:09:50.120><c> in</c><00:09:50.760><c> and</c><00:09:51.040><c> we</c><00:09:51.160><c> will</c>

00:09:51.350 --> 00:09:51.360 align:start position:0%
guys so thanks for tuning in and we will
 

00:09:51.360 --> 00:09:53.509 align:start position:0%
guys so thanks for tuning in and we will
see<00:09:51.600><c> you</c><00:09:52.000><c> in</c><00:09:52.320><c> the</c><00:09:52.519><c> next</c><00:09:52.760><c> one</c><00:09:53.040><c> a</c><00:09:53.160><c> lot</c><00:09:53.360><c> of</c>

00:09:53.509 --> 00:09:53.519 align:start position:0%
see you in the next one a lot of
 

00:09:53.519 --> 00:09:56.190 align:start position:0%
see you in the next one a lot of
exciting<00:09:53.959><c> stuff</c><00:09:54.279><c> coming</c><00:09:54.720><c> up</c><00:09:55.320><c> as</c><00:09:55.440><c> we</c><00:09:55.640><c> go</c><00:09:55.880><c> from</c>

00:09:56.190 --> 00:09:56.200 align:start position:0%
exciting stuff coming up as we go from
 

00:09:56.200 --> 00:10:01.320 align:start position:0%
exciting stuff coming up as we go from
space<00:09:57.040><c> uh</c><00:09:57.200><c> from</c><00:09:57.519><c> one</c><00:09:57.720><c> step</c><00:09:58.000><c> to</c><00:09:58.160><c> the</c><00:09:58.320><c> next</c>

