WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:02.310 align:start position:0%
 
alrighty<00:00:00.560><c> guys</c><00:00:00.799><c> so</c><00:00:01.120><c> now</c><00:00:01.520><c> that</c><00:00:01.680><c> we</c><00:00:01.839><c> understand</c>

00:00:02.310 --> 00:00:02.320 align:start position:0%
alrighty guys so now that we understand
 

00:00:02.320 --> 00:00:03.350 align:start position:0%
alrighty guys so now that we understand
what<00:00:02.480><c> we</c><00:00:02.639><c> want</c><00:00:02.800><c> to</c><00:00:02.960><c> do</c>

00:00:03.350 --> 00:00:03.360 align:start position:0%
what we want to do
 

00:00:03.360 --> 00:00:05.430 align:start position:0%
what we want to do
we<00:00:03.600><c> actually</c><00:00:04.080><c> need</c><00:00:04.240><c> to</c><00:00:04.400><c> go</c><00:00:04.560><c> ahead</c><00:00:04.880><c> and</c><00:00:04.960><c> start</c>

00:00:05.430 --> 00:00:05.440 align:start position:0%
we actually need to go ahead and start
 

00:00:05.440 --> 00:00:07.670 align:start position:0%
we actually need to go ahead and start
putting<00:00:05.759><c> together</c><00:00:06.160><c> our</c><00:00:06.319><c> ajax</c><00:00:06.960><c> request</c><00:00:07.440><c> over</c>

00:00:07.670 --> 00:00:07.680 align:start position:0%
putting together our ajax request over
 

00:00:07.680 --> 00:00:08.790 align:start position:0%
putting together our ajax request over
here<00:00:07.919><c> right</c><00:00:08.160><c> so</c>

00:00:08.790 --> 00:00:08.800 align:start position:0%
here right so
 

00:00:08.800 --> 00:00:10.629 align:start position:0%
here right so
uh<00:00:09.040><c> something</c><00:00:09.440><c> important</c><00:00:09.840><c> to</c><00:00:10.000><c> note</c><00:00:10.320><c> about</c><00:00:10.480><c> the</c>

00:00:10.629 --> 00:00:10.639 align:start position:0%
uh something important to note about the
 

00:00:10.639 --> 00:00:12.470 align:start position:0%
uh something important to note about the
admin<00:00:11.120><c> front</c><00:00:11.360><c> end</c><00:00:11.599><c> app</c><00:00:11.840><c> over</c><00:00:12.000><c> here</c><00:00:12.240><c> is</c><00:00:12.400><c> that</c>

00:00:12.470 --> 00:00:12.480 align:start position:0%
admin front end app over here is that
 

00:00:12.480 --> 00:00:13.669 align:start position:0%
admin front end app over here is that
you<00:00:12.639><c> have</c><00:00:12.799><c> a</c><00:00:12.960><c> folder</c><00:00:13.280><c> called</c>

00:00:13.669 --> 00:00:13.679 align:start position:0%
you have a folder called
 

00:00:13.679 --> 00:00:16.950 align:start position:0%
you have a folder called
actions<00:00:14.320><c> and</c><00:00:14.400><c> the</c><00:00:14.639><c> actions</c><00:00:15.519><c> contains</c><00:00:16.080><c> um</c>

00:00:16.950 --> 00:00:16.960 align:start position:0%
actions and the actions contains um
 

00:00:16.960 --> 00:00:19.029 align:start position:0%
actions and the actions contains um
all<00:00:17.119><c> the</c><00:00:17.279><c> different</c><00:00:17.680><c> functions</c><00:00:18.400><c> that</c><00:00:18.640><c> speak</c>

00:00:19.029 --> 00:00:19.039 align:start position:0%
all the different functions that speak
 

00:00:19.039 --> 00:00:20.390 align:start position:0%
all the different functions that speak
to<00:00:19.359><c> our</c><00:00:19.680><c> backend</c>

00:00:20.390 --> 00:00:20.400 align:start position:0%
to our backend
 

00:00:20.400 --> 00:00:23.189 align:start position:0%
to our backend
right<00:00:20.560><c> so</c><00:00:20.800><c> all</c><00:00:20.880><c> the</c><00:00:21.039><c> http</c><00:00:21.680><c> requests</c><00:00:22.400><c> right</c>

00:00:23.189 --> 00:00:23.199 align:start position:0%
right so all the http requests right
 

00:00:23.199 --> 00:00:24.070 align:start position:0%
right so all the http requests right
they<00:00:23.439><c> um</c>

00:00:24.070 --> 00:00:24.080 align:start position:0%
they um
 

00:00:24.080 --> 00:00:25.670 align:start position:0%
they um
they're<00:00:24.320><c> functions</c><00:00:24.800><c> that</c><00:00:24.960><c> are</c><00:00:25.039><c> defined</c><00:00:25.519><c> in</c>

00:00:25.670 --> 00:00:25.680 align:start position:0%
they're functions that are defined in
 

00:00:25.680 --> 00:00:28.310 align:start position:0%
they're functions that are defined in
here<00:00:26.240><c> right</c><00:00:26.560><c> within</c><00:00:26.800><c> the</c><00:00:27.039><c> auth</c><00:00:27.359><c> blog.js</c>

00:00:28.310 --> 00:00:28.320 align:start position:0%
here right within the auth blog.js
 

00:00:28.320 --> 00:00:30.470 align:start position:0%
here right within the auth blog.js
comment<00:00:28.880><c> tag</c><00:00:29.279><c> user</c><00:00:29.679><c> it's</c><00:00:29.920><c> probably</c><00:00:30.240><c> in</c><00:00:30.320><c> the</c>

00:00:30.470 --> 00:00:30.480 align:start position:0%
comment tag user it's probably in the
 

00:00:30.480 --> 00:00:31.429 align:start position:0%
comment tag user it's probably in the
user

00:00:31.429 --> 00:00:31.439 align:start position:0%
user
 

00:00:31.439 --> 00:00:34.069 align:start position:0%
user
uh<00:00:31.840><c> user</c><00:00:32.480><c> folder</c><00:00:32.880><c> over</c><00:00:33.120><c> there</c><00:00:33.440><c> and</c><00:00:33.600><c> we</c><00:00:33.760><c> define</c>

00:00:34.069 --> 00:00:34.079 align:start position:0%
uh user folder over there and we define
 

00:00:34.079 --> 00:00:35.750 align:start position:0%
uh user folder over there and we define
it<00:00:34.160><c> within</c><00:00:34.399><c> the</c><00:00:34.559><c> actions</c><00:00:34.960><c> and</c><00:00:35.120><c> then</c><00:00:35.360><c> within</c>

00:00:35.750 --> 00:00:35.760 align:start position:0%
it within the actions and then within
 

00:00:35.760 --> 00:00:37.110 align:start position:0%
it within the actions and then within
every<00:00:36.079><c> component</c>

00:00:37.110 --> 00:00:37.120 align:start position:0%
every component
 

00:00:37.120 --> 00:00:40.549 align:start position:0%
every component
we<00:00:37.360><c> have</c><00:00:37.600><c> this</c><00:00:37.920><c> over</c><00:00:38.239><c> here</c><00:00:38.719><c> ah</c><00:00:39.040><c> get</c><00:00:39.360><c> profile</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
we have this over here ah get profile
 

00:00:40.559 --> 00:00:43.510 align:start position:0%
we have this over here ah get profile
is<00:00:40.719><c> defined</c><00:00:41.200><c> within</c><00:00:41.520><c> our</c><00:00:41.680><c> action</c><00:00:42.160><c> slash</c><00:00:42.719><c> user</c>

00:00:43.510 --> 00:00:43.520 align:start position:0%
is defined within our action slash user
 

00:00:43.520 --> 00:00:44.069 align:start position:0%
is defined within our action slash user
folder

00:00:44.069 --> 00:00:44.079 align:start position:0%
folder
 

00:00:44.079 --> 00:00:45.670 align:start position:0%
folder
all<00:00:44.239><c> right</c><00:00:44.320><c> so</c><00:00:44.480><c> we</c><00:00:44.640><c> know</c><00:00:44.800><c> the</c><00:00:44.960><c> get</c><00:00:45.200><c> profile</c>

00:00:45.670 --> 00:00:45.680 align:start position:0%
all right so we know the get profile
 

00:00:45.680 --> 00:00:47.190 align:start position:0%
all right so we know the get profile
function<00:00:46.079><c> is</c><00:00:46.160><c> the</c><00:00:46.320><c> file</c><00:00:46.559><c> that</c><00:00:46.719><c> we</c><00:00:46.800><c> want</c><00:00:47.039><c> to</c>

00:00:47.190 --> 00:00:47.200 align:start position:0%
function is the file that we want to
 

00:00:47.200 --> 00:00:47.990 align:start position:0%
function is the file that we want to
update

00:00:47.990 --> 00:00:48.000 align:start position:0%
update
 

00:00:48.000 --> 00:00:49.830 align:start position:0%
update
because<00:00:48.399><c> we</c><00:00:48.559><c> need</c><00:00:48.719><c> to</c><00:00:48.879><c> pull</c><00:00:49.200><c> in</c><00:00:49.360><c> a</c><00:00:49.440><c> little</c><00:00:49.600><c> more</c>

00:00:49.830 --> 00:00:49.840 align:start position:0%
because we need to pull in a little more
 

00:00:49.840 --> 00:00:51.110 align:start position:0%
because we need to pull in a little more
data

00:00:51.110 --> 00:00:51.120 align:start position:0%
data
 

00:00:51.120 --> 00:00:54.470 align:start position:0%
data
um<00:00:51.760><c> and</c><00:00:52.320><c> yeah</c><00:00:53.039><c> let's</c><00:00:53.199><c> see</c><00:00:53.520><c> actually</c><00:00:54.160><c> if</c><00:00:54.320><c> we</c>

00:00:54.470 --> 00:00:54.480 align:start position:0%
um and yeah let's see actually if we
 

00:00:54.480 --> 00:00:56.229 align:start position:0%
um and yeah let's see actually if we
have<00:00:54.640><c> anything</c><00:00:54.960><c> in</c><00:00:55.120><c> the</c><00:00:55.199><c> logs</c><00:00:55.600><c> it's</c><00:00:55.760><c> possible</c>

00:00:56.229 --> 00:00:56.239 align:start position:0%
have anything in the logs it's possible
 

00:00:56.239 --> 00:00:57.189 align:start position:0%
have anything in the logs it's possible
that

00:00:57.189 --> 00:00:57.199 align:start position:0%
that
 

00:00:57.199 --> 00:00:59.189 align:start position:0%
that
because<00:00:57.920><c> we</c><00:00:58.079><c> just</c><00:00:58.239><c> looked</c><00:00:58.399><c> at</c><00:00:58.559><c> our</c><00:00:58.640><c> database</c>

00:00:59.189 --> 00:00:59.199 align:start position:0%
because we just looked at our database
 

00:00:59.199 --> 00:01:01.029 align:start position:0%
because we just looked at our database
over<00:00:59.359><c> here</c><00:00:59.520><c> and</c><00:00:59.600><c> we</c><00:00:59.760><c> see</c><00:00:59.920><c> that</c><00:01:00.079><c> for</c><00:01:00.239><c> this</c><00:01:00.480><c> store</c>

00:01:01.029 --> 00:01:01.039 align:start position:0%
over here and we see that for this store
 

00:01:01.039 --> 00:01:03.510 align:start position:0%
over here and we see that for this store
moderation<00:01:01.680><c> required</c><00:01:02.320><c> is</c><00:01:02.559><c> true</c><00:01:03.199><c> and</c><00:01:03.280><c> it's</c>

00:01:03.510 --> 00:01:03.520 align:start position:0%
moderation required is true and it's
 

00:01:03.520 --> 00:01:05.509 align:start position:0%
moderation required is true and it's
already<00:01:03.920><c> set</c><00:01:04.159><c> within</c><00:01:04.479><c> the</c><00:01:04.640><c> database</c>

00:01:05.509 --> 00:01:05.519 align:start position:0%
already set within the database
 

00:01:05.519 --> 00:01:08.789 align:start position:0%
already set within the database
right<00:01:06.880><c> so</c><00:01:07.040><c> what</c><00:01:07.200><c> that</c><00:01:07.360><c> means</c><00:01:07.680><c> is</c><00:01:08.080><c> let's</c><00:01:08.240><c> see</c><00:01:08.400><c> if</c>

00:01:08.789 --> 00:01:08.799 align:start position:0%
right so what that means is let's see if
 

00:01:08.799 --> 00:01:11.830 align:start position:0%
right so what that means is let's see if
that<00:01:09.040><c> moderation</c><00:01:09.600><c> required</c><00:01:11.040><c> column</c><00:01:11.439><c> is</c><00:01:11.600><c> also</c>

00:01:11.830 --> 00:01:11.840 align:start position:0%
that moderation required column is also
 

00:01:11.840 --> 00:01:13.750 align:start position:0%
that moderation required column is also
getting<00:01:12.159><c> pulled</c><00:01:12.400><c> to</c><00:01:12.560><c> the</c><00:01:12.640><c> front</c><00:01:12.960><c> end</c>

00:01:13.750 --> 00:01:13.760 align:start position:0%
getting pulled to the front end
 

00:01:13.760 --> 00:01:16.149 align:start position:0%
getting pulled to the front end
uh<00:01:14.000><c> that's</c><00:01:14.320><c> the</c><00:01:14.479><c> first</c><00:01:14.720><c> step</c><00:01:15.520><c> let's</c><00:01:15.680><c> see</c><00:01:15.920><c> over</c>

00:01:16.149 --> 00:01:16.159 align:start position:0%
uh that's the first step let's see over
 

00:01:16.159 --> 00:01:17.270 align:start position:0%
uh that's the first step let's see over
here<00:01:16.400><c> so</c>

00:01:17.270 --> 00:01:17.280 align:start position:0%
here so
 

00:01:17.280 --> 00:01:20.630 align:start position:0%
here so
okay<00:01:18.320><c> get</c><00:01:18.560><c> this</c><00:01:18.799><c> is</c><00:01:18.960><c> the</c><00:01:19.119><c> back</c><00:01:19.360><c> end</c><00:01:19.680><c> over</c><00:01:19.920><c> here</c>

00:01:20.630 --> 00:01:20.640 align:start position:0%
okay get this is the back end over here
 

00:01:20.640 --> 00:01:23.749 align:start position:0%
okay get this is the back end over here
get<00:01:20.880><c> api</c><00:01:21.439><c> slash</c><00:01:21.840><c> user</c><00:01:22.400><c> this</c><00:01:22.560><c> is</c><00:01:22.720><c> probably</c>

00:01:23.749 --> 00:01:23.759 align:start position:0%
get api slash user this is probably
 

00:01:23.759 --> 00:01:27.109 align:start position:0%
get api slash user this is probably
something<00:01:24.080><c> to</c><00:01:24.320><c> do</c><00:01:24.960><c> right</c>

00:01:27.109 --> 00:01:27.119 align:start position:0%
something to do right
 

00:01:27.119 --> 00:01:30.069 align:start position:0%
something to do right
over<00:01:27.439><c> here</c><00:01:28.159><c> props</c><00:01:28.640><c> and</c><00:01:28.799><c> settings</c><00:01:29.280><c> component</c>

00:01:30.069 --> 00:01:30.079 align:start position:0%
over here props and settings component
 

00:01:30.079 --> 00:01:30.550 align:start position:0%
over here props and settings component
okay

00:01:30.550 --> 00:01:30.560 align:start position:0%
okay
 

00:01:30.560 --> 00:01:32.550 align:start position:0%
okay
that<00:01:30.720><c> doesn't</c><00:01:30.960><c> really</c><00:01:31.200><c> tell</c><00:01:31.439><c> us</c><00:01:31.600><c> a</c><00:01:31.680><c> lot</c><00:01:32.320><c> okay</c>

00:01:32.550 --> 00:01:32.560 align:start position:0%
that doesn't really tell us a lot okay
 

00:01:32.560 --> 00:01:33.910 align:start position:0%
that doesn't really tell us a lot okay
what<00:01:32.799><c> we</c><00:01:32.880><c> need</c><00:01:33.040><c> to</c><00:01:33.200><c> do</c><00:01:33.360><c> is</c><00:01:33.520><c> we</c><00:01:33.600><c> need</c><00:01:33.759><c> to</c>

00:01:33.910 --> 00:01:33.920 align:start position:0%
what we need to do is we need to
 

00:01:33.920 --> 00:01:35.350 align:start position:0%
what we need to do is we need to
actually<00:01:34.640><c> log</c>

00:01:35.350 --> 00:01:35.360 align:start position:0%
actually log
 

00:01:35.360 --> 00:01:38.310 align:start position:0%
actually log
uh<00:01:35.600><c> we</c><00:01:35.759><c> need</c><00:01:35.920><c> to</c><00:01:36.000><c> actually</c><00:01:36.400><c> go</c><00:01:36.560><c> into</c><00:01:36.799><c> that</c><00:01:38.000><c> file</c>

00:01:38.310 --> 00:01:38.320 align:start position:0%
uh we need to actually go into that file
 

00:01:38.320 --> 00:01:40.069 align:start position:0%
uh we need to actually go into that file
now<00:01:38.640><c> just</c><00:01:38.880><c> for</c><00:01:39.040><c> the</c><00:01:39.200><c> sake</c><00:01:39.439><c> of</c><00:01:39.520><c> understanding</c>

00:01:40.069 --> 00:01:40.079 align:start position:0%
now just for the sake of understanding
 

00:01:40.079 --> 00:01:42.149 align:start position:0%
now just for the sake of understanding
the<00:01:40.159><c> repo</c><00:01:40.479><c> and</c><00:01:40.640><c> everything</c><00:01:41.360><c> import</c><00:01:41.840><c> get</c>

00:01:42.149 --> 00:01:42.159 align:start position:0%
the repo and everything import get
 

00:01:42.159 --> 00:01:44.469 align:start position:0%
the repo and everything import get
profile<00:01:42.720><c> from</c><00:01:43.040><c> action</c><00:01:43.439><c> slash</c><00:01:43.840><c> user</c><00:01:44.159><c> so</c><00:01:44.320><c> let's</c>

00:01:44.469 --> 00:01:44.479 align:start position:0%
profile from action slash user so let's
 

00:01:44.479 --> 00:01:46.310 align:start position:0%
profile from action slash user so let's
go<00:01:44.560><c> into</c><00:01:44.799><c> the</c><00:01:44.960><c> user</c><00:01:45.360><c> function</c><00:01:45.759><c> here</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
go into the user function here
 

00:01:46.320 --> 00:01:48.630 align:start position:0%
go into the user function here
then<00:01:46.479><c> we're</c><00:01:46.640><c> going</c><00:01:46.799><c> to</c><00:01:46.960><c> get</c><00:01:47.200><c> profile</c><00:01:48.399><c> and</c><00:01:48.479><c> the</c>

00:01:48.630 --> 00:01:48.640 align:start position:0%
then we're going to get profile and the
 

00:01:48.640 --> 00:01:50.069 align:start position:0%
then we're going to get profile and the
get<00:01:48.960><c> profile</c><00:01:49.439><c> function</c><00:01:49.840><c> is</c>

00:01:50.069 --> 00:01:50.079 align:start position:0%
get profile function is
 

00:01:50.079 --> 00:01:51.590 align:start position:0%
get profile function is
the<00:01:50.320><c> part</c><00:01:50.479><c> that</c><00:01:50.640><c> actually</c><00:01:51.040><c> talks</c><00:01:51.280><c> with</c><00:01:51.520><c> our</c>

00:01:51.590 --> 00:01:51.600 align:start position:0%
the part that actually talks with our
 

00:01:51.600 --> 00:01:53.510 align:start position:0%
the part that actually talks with our
back<00:01:51.920><c> end</c><00:01:52.079><c> in</c><00:01:52.240><c> our</c><00:01:52.399><c> database</c><00:01:52.960><c> right</c><00:01:53.200><c> so</c><00:01:53.360><c> it</c>

00:01:53.510 --> 00:01:53.520 align:start position:0%
back end in our database right so it
 

00:01:53.520 --> 00:01:54.149 align:start position:0%
back end in our database right so it
says

00:01:54.149 --> 00:01:54.159 align:start position:0%
says
 

00:01:54.159 --> 00:01:56.870 align:start position:0%
says
list<00:01:54.479><c> blog</c><00:01:54.880><c> ends</c><00:01:55.200><c> point</c><00:01:55.520><c> e</c><00:01:55.840><c> it</c><00:01:56.159><c> blogs</c><00:01:56.560><c> endpoint</c>

00:01:56.870 --> 00:01:56.880 align:start position:0%
list blog ends point e it blogs endpoint
 

00:01:56.880 --> 00:01:58.950 align:start position:0%
list blog ends point e it blogs endpoint
equals<00:01:57.280><c> api</c><00:01:57.840><c> slash</c><00:01:58.240><c> user</c><00:01:58.560><c> slash</c>

00:01:58.950 --> 00:01:58.960 align:start position:0%
equals api slash user slash
 

00:01:58.960 --> 00:02:02.550 align:start position:0%
equals api slash user slash
user<00:01:59.280><c> name</c><00:01:59.600><c> which</c><00:01:59.840><c> is</c><00:02:00.399><c> i</c><00:02:00.640><c> believe</c><00:02:01.040><c> just</c><00:02:01.280><c> um</c>

00:02:02.550 --> 00:02:02.560 align:start position:0%
user name which is i believe just um
 

00:02:02.560 --> 00:02:05.109 align:start position:0%
user name which is i believe just um
what<00:02:02.799><c> we</c><00:02:02.960><c> saw</c><00:02:03.200><c> over</c><00:02:03.520><c> here</c><00:02:04.399><c> we</c><00:02:04.560><c> see</c><00:02:04.799><c> over</c><00:02:04.960><c> here</c>

00:02:05.109 --> 00:02:05.119 align:start position:0%
what we saw over here we see over here
 

00:02:05.119 --> 00:02:06.389 align:start position:0%
what we saw over here we see over here
all<00:02:05.200><c> the</c><00:02:05.360><c> get</c><00:02:05.600><c> requests</c>

00:02:06.389 --> 00:02:06.399 align:start position:0%
all the get requests
 

00:02:06.399 --> 00:02:09.830 align:start position:0%
all the get requests
uh<00:02:06.640><c> get</c><00:02:06.960><c> api</c><00:02:07.360><c> slash</c><00:02:07.680><c> user</c><00:02:08.720><c> uh</c><00:02:09.119><c> whatever</c><00:02:09.520><c> the</c>

00:02:09.830 --> 00:02:09.840 align:start position:0%
uh get api slash user uh whatever the
 

00:02:09.840 --> 00:02:13.990 align:start position:0%
uh get api slash user uh whatever the
the<00:02:10.000><c> custom</c><00:02:10.479><c> url</c><00:02:12.040><c> tribapp.myshopify.com</c>

00:02:13.990 --> 00:02:14.000 align:start position:0%
the custom url tribapp.myshopify.com
 

00:02:14.000 --> 00:02:17.510 align:start position:0%
the custom url tribapp.myshopify.com
all<00:02:14.400><c> right</c><00:02:14.800><c> okay</c><00:02:15.440><c> and</c><00:02:15.599><c> then</c><00:02:15.840><c> return</c><00:02:16.640><c> return</c><00:02:17.120><c> it</c>

00:02:17.510 --> 00:02:17.520 align:start position:0%
all right okay and then return return it
 

00:02:17.520 --> 00:02:19.190 align:start position:0%
all right okay and then return return it
and<00:02:17.840><c> whatever</c><00:02:18.239><c> and</c><00:02:18.319><c> then</c><00:02:18.560><c> send</c><00:02:18.800><c> it</c><00:02:18.879><c> back</c><00:02:19.040><c> to</c>

00:02:19.190 --> 00:02:19.200 align:start position:0%
and whatever and then send it back to
 

00:02:19.200 --> 00:02:21.110 align:start position:0%
and whatever and then send it back to
the<00:02:19.360><c> component</c><00:02:19.920><c> and</c><00:02:20.080><c> all</c><00:02:20.319><c> right</c><00:02:20.560><c> but</c>

00:02:21.110 --> 00:02:21.120 align:start position:0%
the component and all right but
 

00:02:21.120 --> 00:02:22.630 align:start position:0%
the component and all right but
now<00:02:21.280><c> we</c><00:02:21.440><c> actually</c><00:02:21.760><c> want</c><00:02:21.920><c> to</c><00:02:22.080><c> go</c><00:02:22.239><c> into</c><00:02:22.480><c> our</c>

00:02:22.630 --> 00:02:22.640 align:start position:0%
now we actually want to go into our
 

00:02:22.640 --> 00:02:24.550 align:start position:0%
now we actually want to go into our
database<00:02:23.200><c> over</c><00:02:23.440><c> here</c>

00:02:24.550 --> 00:02:24.560 align:start position:0%
database over here
 

00:02:24.560 --> 00:02:25.670 align:start position:0%
database over here
the<00:02:24.640><c> first</c><00:02:24.800><c> thing</c><00:02:24.959><c> we're</c><00:02:25.120><c> going</c><00:02:25.200><c> to</c><00:02:25.280><c> do</c><00:02:25.440><c> in</c><00:02:25.520><c> the</c>

00:02:25.670 --> 00:02:25.680 align:start position:0%
the first thing we're going to do in the
 

00:02:25.680 --> 00:02:27.510 align:start position:0%
the first thing we're going to do in the
back<00:02:25.920><c> end</c><00:02:26.160><c> is</c><00:02:26.239><c> we're</c><00:02:26.400><c> going</c><00:02:26.480><c> to</c><00:02:26.560><c> check</c><00:02:26.800><c> out</c><00:02:26.879><c> the</c>

00:02:27.510 --> 00:02:27.520 align:start position:0%
back end is we're going to check out the
 

00:02:27.520 --> 00:02:28.630 align:start position:0%
back end is we're going to check out the
routes<00:02:27.920><c> folder</c>

00:02:28.630 --> 00:02:28.640 align:start position:0%
routes folder
 

00:02:28.640 --> 00:02:31.670 align:start position:0%
routes folder
okay<00:02:29.360><c> and</c><00:02:29.440><c> the</c><00:02:29.599><c> routes</c><00:02:29.920><c> folder</c><00:02:30.239><c> is</c><00:02:30.400><c> api</c><00:02:31.280><c> user</c>

00:02:31.670 --> 00:02:31.680 align:start position:0%
okay and the routes folder is api user
 

00:02:31.680 --> 00:02:34.229 align:start position:0%
okay and the routes folder is api user
slash<00:02:32.080><c> user</c><00:02:32.480><c> name</c><00:02:32.720><c> okay</c><00:02:33.040><c> so</c><00:02:33.280><c> routes</c>

00:02:34.229 --> 00:02:34.239 align:start position:0%
slash user name okay so routes
 

00:02:34.239 --> 00:02:36.390 align:start position:0%
slash user name okay so routes
and<00:02:34.319><c> then</c><00:02:34.640><c> user</c><00:02:35.440><c> okay</c><00:02:35.840><c> and</c><00:02:36.000><c> it</c><00:02:36.080><c> says</c><00:02:36.239><c> every</c>

00:02:36.390 --> 00:02:36.400 align:start position:0%
and then user okay and it says every
 

00:02:36.400 --> 00:02:38.150 align:start position:0%
and then user okay and it says every
time<00:02:36.560><c> that</c><00:02:36.720><c> someone</c><00:02:36.959><c> has</c><00:02:37.200><c> slash</c><00:02:37.519><c> user</c><00:02:37.840><c> slash</c>

00:02:38.150 --> 00:02:38.160 align:start position:0%
time that someone has slash user slash
 

00:02:38.160 --> 00:02:39.190 align:start position:0%
time that someone has slash user slash
username

00:02:39.190 --> 00:02:39.200 align:start position:0%
username
 

00:02:39.200 --> 00:02:41.910 align:start position:0%
username
run<00:02:39.440><c> the</c><00:02:39.680><c> settings</c><00:02:40.160><c> page</c><00:02:40.640><c> function</c><00:02:41.280><c> over</c><00:02:41.599><c> here</c>

00:02:41.910 --> 00:02:41.920 align:start position:0%
run the settings page function over here
 

00:02:41.920 --> 00:02:44.070 align:start position:0%
run the settings page function over here
which<00:02:42.160><c> is</c><00:02:42.319><c> from</c><00:02:42.480><c> the</c><00:02:42.640><c> backend</c><00:02:43.200><c> so</c><00:02:43.360><c> that's</c><00:02:43.680><c> also</c>

00:02:44.070 --> 00:02:44.080 align:start position:0%
which is from the backend so that's also
 

00:02:44.080 --> 00:02:44.790 align:start position:0%
which is from the backend so that's also
again

00:02:44.790 --> 00:02:44.800 align:start position:0%
again
 

00:02:44.800 --> 00:02:46.470 align:start position:0%
again
that<00:02:45.040><c> actually</c><00:02:45.440><c> gets</c><00:02:45.680><c> pulled</c><00:02:46.000><c> in</c><00:02:46.160><c> from</c><00:02:46.400><c> our</c>

00:02:46.470 --> 00:02:46.480 align:start position:0%
that actually gets pulled in from our
 

00:02:46.480 --> 00:02:47.990 align:start position:0%
that actually gets pulled in from our
controllers<00:02:47.280><c> over</c><00:02:47.440><c> here</c><00:02:47.599><c> so</c><00:02:47.760><c> you</c><00:02:47.840><c> see</c>

00:02:47.990 --> 00:02:48.000 align:start position:0%
controllers over here so you see
 

00:02:48.000 --> 00:02:49.509 align:start position:0%
controllers over here so you see
everything<00:02:48.319><c> is</c><00:02:48.480><c> very</c><00:02:48.720><c> very</c><00:02:48.959><c> neat</c><00:02:49.280><c> very</c>

00:02:49.509 --> 00:02:49.519 align:start position:0%
everything is very very neat very
 

00:02:49.519 --> 00:02:49.990 align:start position:0%
everything is very very neat very
organized

00:02:49.990 --> 00:02:50.000 align:start position:0%
organized
 

00:02:50.000 --> 00:02:52.869 align:start position:0%
organized
require<00:02:50.480><c> sign</c><00:02:50.720><c> in</c><00:02:51.120><c> auth</c><00:02:51.440><c> middleware</c><00:02:52.400><c> uh</c><00:02:52.720><c> let's</c>

00:02:52.869 --> 00:02:52.879 align:start position:0%
require sign in auth middleware uh let's
 

00:02:52.879 --> 00:02:55.110 align:start position:0%
require sign in auth middleware uh let's
see<00:02:53.120><c> ah</c><00:02:53.280><c> here's</c><00:02:53.599><c> settings</c><00:02:54.000><c> page</c><00:02:54.319><c> over</c><00:02:54.480><c> here</c>

00:02:55.110 --> 00:02:55.120 align:start position:0%
see ah here's settings page over here
 

00:02:55.120 --> 00:02:57.270 align:start position:0%
see ah here's settings page over here
settings<00:02:55.599><c> page</c><00:02:55.840><c> function</c><00:02:56.480><c> is</c><00:02:56.640><c> getting</c><00:02:56.959><c> called</c>

00:02:57.270 --> 00:02:57.280 align:start position:0%
settings page function is getting called
 

00:02:57.280 --> 00:02:58.869 align:start position:0%
settings page function is getting called
in<00:02:57.440><c> from</c><00:02:57.680><c> controller</c><00:02:58.319><c> slash</c>

00:02:58.869 --> 00:02:58.879 align:start position:0%
in from controller slash
 

00:02:58.879 --> 00:03:02.550 align:start position:0%
in from controller slash
user<00:02:59.519><c> okay</c><00:02:59.920><c> cool</c><00:03:00.640><c> so</c><00:03:01.120><c> uh</c><00:03:01.519><c> once</c><00:03:01.760><c> we</c><00:03:02.080><c> understand</c>

00:03:02.550 --> 00:03:02.560 align:start position:0%
user okay cool so uh once we understand
 

00:03:02.560 --> 00:03:04.229 align:start position:0%
user okay cool so uh once we understand
that<00:03:02.720><c> within</c><00:03:03.120><c> our</c><00:03:03.200><c> routes</c><00:03:03.599><c> fold</c>

00:03:04.229 --> 00:03:04.239 align:start position:0%
that within our routes fold
 

00:03:04.239 --> 00:03:06.869 align:start position:0%
that within our routes fold
routes<00:03:05.680><c> something</c><00:03:06.000><c> important</c><00:03:06.319><c> to</c><00:03:06.480><c> note</c><00:03:06.640><c> about</c>

00:03:06.869 --> 00:03:06.879 align:start position:0%
routes something important to note about
 

00:03:06.879 --> 00:03:08.550 align:start position:0%
routes something important to note about
our<00:03:07.040><c> admin</c><00:03:07.360><c> back</c><00:03:07.599><c> and</c><00:03:07.680><c> within</c><00:03:08.000><c> our</c><00:03:08.239><c> routes</c>

00:03:08.550 --> 00:03:08.560 align:start position:0%
our admin back and within our routes
 

00:03:08.560 --> 00:03:09.110 align:start position:0%
our admin back and within our routes
folder

00:03:09.110 --> 00:03:09.120 align:start position:0%
folder
 

00:03:09.120 --> 00:03:12.470 align:start position:0%
folder
it<00:03:09.280><c> contains</c><00:03:10.640><c> um</c><00:03:11.840><c> it's</c><00:03:12.000><c> just</c>

00:03:12.470 --> 00:03:12.480 align:start position:0%
it contains um it's just
 

00:03:12.480 --> 00:03:14.550 align:start position:0%
it contains um it's just
exactly<00:03:12.800><c> what</c><00:03:12.959><c> it</c><00:03:13.120><c> sounds</c><00:03:13.440><c> like</c><00:03:13.760><c> it's</c><00:03:14.080><c> routing</c>

00:03:14.550 --> 00:03:14.560 align:start position:0%
exactly what it sounds like it's routing
 

00:03:14.560 --> 00:03:16.710 align:start position:0%
exactly what it sounds like it's routing
the<00:03:14.720><c> request</c><00:03:15.280><c> to</c><00:03:15.440><c> the</c><00:03:15.599><c> right</c><00:03:15.920><c> controller</c>

00:03:16.710 --> 00:03:16.720 align:start position:0%
the request to the right controller
 

00:03:16.720 --> 00:03:19.670 align:start position:0%
the request to the right controller
that<00:03:16.959><c> kind</c><00:03:17.120><c> of</c><00:03:17.280><c> thing</c><00:03:18.000><c> meaning</c><00:03:18.720><c> um</c><00:03:19.280><c> the</c><00:03:19.440><c> route</c>

00:03:19.670 --> 00:03:19.680 align:start position:0%
that kind of thing meaning um the route
 

00:03:19.680 --> 00:03:20.070 align:start position:0%
that kind of thing meaning um the route
says

00:03:20.070 --> 00:03:20.080 align:start position:0%
says
 

00:03:20.080 --> 00:03:22.550 align:start position:0%
says
every<00:03:20.239><c> time</c><00:03:20.400><c> that</c><00:03:20.560><c> someone</c><00:03:20.879><c> hits</c><00:03:21.120><c> this</c><00:03:21.440><c> route</c>

00:03:22.550 --> 00:03:22.560 align:start position:0%
every time that someone hits this route
 

00:03:22.560 --> 00:03:24.070 align:start position:0%
every time that someone hits this route
then<00:03:22.800><c> i</c><00:03:22.959><c> should</c><00:03:23.120><c> run</c><00:03:23.360><c> the</c><00:03:23.519><c> following</c>

00:03:24.070 --> 00:03:24.080 align:start position:0%
then i should run the following
 

00:03:24.080 --> 00:03:26.229 align:start position:0%
then i should run the following
functionality<00:03:24.959><c> the</c><00:03:25.120><c> functionality</c><00:03:25.920><c> itself</c>

00:03:26.229 --> 00:03:26.239 align:start position:0%
functionality the functionality itself
 

00:03:26.239 --> 00:03:28.229 align:start position:0%
functionality the functionality itself
is<00:03:26.400><c> not</c><00:03:26.640><c> located</c><00:03:27.120><c> here</c><00:03:27.360><c> the</c><00:03:27.519><c> functionality</c>

00:03:28.229 --> 00:03:28.239 align:start position:0%
is not located here the functionality
 

00:03:28.239 --> 00:03:29.509 align:start position:0%
is not located here the functionality
and<00:03:28.400><c> the</c><00:03:28.480><c> logic</c><00:03:28.959><c> of</c><00:03:29.120><c> it</c>

00:03:29.509 --> 00:03:29.519 align:start position:0%
and the logic of it
 

00:03:29.519 --> 00:03:31.430 align:start position:0%
and the logic of it
is<00:03:29.680><c> actually</c><00:03:30.159><c> within</c><00:03:30.480><c> the</c><00:03:30.560><c> controllers</c>

00:03:31.430 --> 00:03:31.440 align:start position:0%
is actually within the controllers
 

00:03:31.440 --> 00:03:33.110 align:start position:0%
is actually within the controllers
folder<00:03:32.000><c> over</c><00:03:32.159><c> here</c><00:03:32.319><c> so</c><00:03:32.480><c> i'm</c><00:03:32.560><c> going</c><00:03:32.640><c> to</c><00:03:32.720><c> go</c><00:03:32.879><c> into</c>

00:03:33.110 --> 00:03:33.120 align:start position:0%
folder over here so i'm going to go into
 

00:03:33.120 --> 00:03:34.229 align:start position:0%
folder over here so i'm going to go into
controllers

00:03:34.229 --> 00:03:34.239 align:start position:0%
controllers
 

00:03:34.239 --> 00:03:36.869 align:start position:0%
controllers
and<00:03:34.400><c> you</c><00:03:34.560><c> see</c><00:03:34.720><c> that</c><00:03:34.879><c> within</c><00:03:35.280><c> our</c><00:03:35.440><c> routes</c><00:03:35.840><c> file</c>

00:03:36.869 --> 00:03:36.879 align:start position:0%
and you see that within our routes file
 

00:03:36.879 --> 00:03:37.350 align:start position:0%
and you see that within our routes file
um

00:03:37.350 --> 00:03:37.360 align:start position:0%
um
 

00:03:37.360 --> 00:03:40.869 align:start position:0%
um
i<00:03:38.000><c> we</c><00:03:38.319><c> import</c><00:03:38.959><c> all</c><00:03:39.200><c> of</c><00:03:39.519><c> the</c><00:03:39.760><c> uh</c><00:03:40.080><c> all</c><00:03:40.319><c> of</c><00:03:40.400><c> these</c>

00:03:40.869 --> 00:03:40.879 align:start position:0%
i we import all of the uh all of these
 

00:03:40.879 --> 00:03:43.430 align:start position:0%
i we import all of the uh all of these
the<00:03:41.360><c> the</c><00:03:41.519><c> actual</c><00:03:42.000><c> logic</c><00:03:42.480><c> of</c><00:03:42.640><c> it</c><00:03:42.959><c> all</c><00:03:43.120><c> right</c><00:03:43.360><c> and</c>

00:03:43.430 --> 00:03:43.440 align:start position:0%
the the actual logic of it all right and
 

00:03:43.440 --> 00:03:44.869 align:start position:0%
the the actual logic of it all right and
then<00:03:43.760><c> it</c><00:03:43.840><c> says</c><00:03:44.080><c> every</c><00:03:44.239><c> time</c><00:03:44.400><c> someone</c><00:03:44.640><c> hits</c>

00:03:44.869 --> 00:03:44.879 align:start position:0%
then it says every time someone hits
 

00:03:44.879 --> 00:03:46.869 align:start position:0%
then it says every time someone hits
this<00:03:45.120><c> run</c><00:03:45.360><c> the</c><00:03:45.519><c> settings</c><00:03:45.920><c> page</c>

00:03:46.869 --> 00:03:46.879 align:start position:0%
this run the settings page
 

00:03:46.879 --> 00:03:48.390 align:start position:0%
this run the settings page
function<00:03:47.519><c> that's</c><00:03:47.760><c> defined</c><00:03:48.159><c> in</c><00:03:48.239><c> the</c>

00:03:48.390 --> 00:03:48.400 align:start position:0%
function that's defined in the
 

00:03:48.400 --> 00:03:50.710 align:start position:0%
function that's defined in the
controller<00:03:49.040><c> slash</c><00:03:49.440><c> user</c>

00:03:50.710 --> 00:03:50.720 align:start position:0%
controller slash user
 

00:03:50.720 --> 00:03:52.229 align:start position:0%
controller slash user
okay<00:03:51.120><c> and</c><00:03:51.280><c> what's</c><00:03:51.599><c> the</c><00:03:51.680><c> name</c><00:03:51.920><c> of</c><00:03:52.000><c> it</c><00:03:52.159><c> it's</c>

00:03:52.229 --> 00:03:52.239 align:start position:0%
okay and what's the name of it it's
 

00:03:52.239 --> 00:03:54.149 align:start position:0%
okay and what's the name of it it's
called<00:03:52.480><c> settings</c><00:03:52.879><c> page</c><00:03:53.200><c> ah</c><00:03:53.599><c> cool</c>

00:03:54.149 --> 00:03:54.159 align:start position:0%
called settings page ah cool
 

00:03:54.159 --> 00:03:55.990 align:start position:0%
called settings page ah cool
so<00:03:54.400><c> it</c><00:03:54.480><c> looks</c><00:03:54.640><c> like</c><00:03:54.879><c> x</c><00:03:55.439><c> and</c><00:03:55.519><c> this</c><00:03:55.760><c> is</c><00:03:55.840><c> the</c>

00:03:55.990 --> 00:03:56.000 align:start position:0%
so it looks like x and this is the
 

00:03:56.000 --> 00:03:57.670 align:start position:0%
so it looks like x and this is the
actual<00:03:56.480><c> function</c><00:03:56.799><c> that</c><00:03:56.959><c> we're</c><00:03:57.120><c> looking</c><00:03:57.439><c> for</c>

00:03:57.670 --> 00:03:57.680 align:start position:0%
actual function that we're looking for
 

00:03:57.680 --> 00:03:59.670 align:start position:0%
actual function that we're looking for
it<00:03:57.760><c> says</c><00:03:58.360><c> shop.find1</c>

00:03:59.670 --> 00:03:59.680 align:start position:0%
it says shop.find1
 

00:03:59.680 --> 00:04:01.750 align:start position:0%
it says shop.find1
where<00:03:59.840><c> the</c><00:04:00.000><c> shopify</c><00:04:00.560><c> underscore</c><00:04:01.120><c> domain</c><00:04:01.599><c> is</c>

00:04:01.750 --> 00:04:01.760 align:start position:0%
where the shopify underscore domain is
 

00:04:01.760 --> 00:04:02.710 align:start position:0%
where the shopify underscore domain is
the<00:04:01.920><c> shop</c><00:04:02.319><c> name</c>

00:04:02.710 --> 00:04:02.720 align:start position:0%
the shop name
 

00:04:02.720 --> 00:04:04.550 align:start position:0%
the shop name
and<00:04:02.879><c> the</c><00:04:03.040><c> shop</c><00:04:03.280><c> name</c><00:04:03.519><c> is</c><00:04:03.680><c> whatever</c><00:04:04.000><c> was</c><00:04:04.159><c> passed</c>

00:04:04.550 --> 00:04:04.560 align:start position:0%
and the shop name is whatever was passed
 

00:04:04.560 --> 00:04:06.869 align:start position:0%
and the shop name is whatever was passed
in<00:04:05.120><c> within</c><00:04:05.439><c> the</c><00:04:05.599><c> url</c>

00:04:06.869 --> 00:04:06.879 align:start position:0%
in within the url
 

00:04:06.879 --> 00:04:09.910 align:start position:0%
in within the url
execute<00:04:07.439><c> that</c><00:04:07.840><c> and</c><00:04:08.640><c> if</c><00:04:08.799><c> you</c><00:04:08.879><c> get</c><00:04:09.120><c> an</c><00:04:09.280><c> error</c><00:04:09.680><c> log</c>

00:04:09.910 --> 00:04:09.920 align:start position:0%
execute that and if you get an error log
 

00:04:09.920 --> 00:04:12.149 align:start position:0%
execute that and if you get an error log
the<00:04:10.080><c> error</c><00:04:10.319><c> whatever</c><00:04:10.799><c> if</c><00:04:10.959><c> you</c><00:04:11.120><c> have</c><00:04:11.360><c> a</c><00:04:11.519><c> shop</c>

00:04:12.149 --> 00:04:12.159 align:start position:0%
the error whatever if you have a shop
 

00:04:12.159 --> 00:04:14.550 align:start position:0%
the error whatever if you have a shop
then<00:04:12.319><c> return</c><00:04:12.720><c> the</c><00:04:12.879><c> shop</c><00:04:13.200><c> so</c><00:04:13.519><c> actually</c><00:04:14.400><c> the</c>

00:04:14.550 --> 00:04:14.560 align:start position:0%
then return the shop so actually the
 

00:04:14.560 --> 00:04:15.270 align:start position:0%
then return the shop so actually the
moderate

00:04:15.270 --> 00:04:15.280 align:start position:0%
moderate
 

00:04:15.280 --> 00:04:18.069 align:start position:0%
moderate
we<00:04:15.519><c> understand</c><00:04:16.079><c> that</c><00:04:16.639><c> uh</c><00:04:16.959><c> it's</c><00:04:17.199><c> a</c><00:04:17.280><c> select</c><00:04:17.759><c> all</c>

00:04:18.069 --> 00:04:18.079 align:start position:0%
we understand that uh it's a select all
 

00:04:18.079 --> 00:04:19.189 align:start position:0%
we understand that uh it's a select all
query<00:04:18.560><c> here</c>

00:04:19.189 --> 00:04:19.199 align:start position:0%
query here
 

00:04:19.199 --> 00:04:20.789 align:start position:0%
query here
it<00:04:19.359><c> means</c><00:04:19.600><c> that</c><00:04:19.759><c> every</c><00:04:20.079><c> we</c><00:04:20.160><c> don't</c><00:04:20.320><c> need</c><00:04:20.479><c> to</c><00:04:20.560><c> do</c>

00:04:20.789 --> 00:04:20.799 align:start position:0%
it means that every we don't need to do
 

00:04:20.799 --> 00:04:22.629 align:start position:0%
it means that every we don't need to do
anything<00:04:21.280><c> in</c><00:04:21.440><c> the</c><00:04:21.519><c> back</c><00:04:21.759><c> end</c><00:04:22.000><c> for</c><00:04:22.160><c> this</c>

00:04:22.629 --> 00:04:22.639 align:start position:0%
anything in the back end for this
 

00:04:22.639 --> 00:04:25.189 align:start position:0%
anything in the back end for this
at<00:04:22.800><c> this</c><00:04:23.120><c> point</c><00:04:23.520><c> all</c><00:04:23.680><c> right</c><00:04:24.479><c> uh</c><00:04:24.720><c> so</c><00:04:24.880><c> that's</c>

00:04:25.189 --> 00:04:25.199 align:start position:0%
at this point all right uh so that's
 

00:04:25.199 --> 00:04:27.270 align:start position:0%
at this point all right uh so that's
very<00:04:25.520><c> good</c>

00:04:27.270 --> 00:04:27.280 align:start position:0%
very good
 

00:04:27.280 --> 00:04:29.909 align:start position:0%
very good
um<00:04:28.080><c> uh</c><00:04:28.400><c> everything</c><00:04:28.800><c> is</c><00:04:28.960><c> just</c><00:04:29.199><c> just</c><00:04:29.440><c> gonna</c><00:04:29.680><c> run</c>

00:04:29.909 --> 00:04:29.919 align:start position:0%
um uh everything is just just gonna run
 

00:04:29.919 --> 00:04:31.590 align:start position:0%
um uh everything is just just gonna run
exactly<00:04:30.400><c> as</c><00:04:30.560><c> it</c><00:04:30.800><c> is</c>

00:04:31.590 --> 00:04:31.600 align:start position:0%
exactly as it is
 

00:04:31.600 --> 00:04:33.270 align:start position:0%
exactly as it is
and<00:04:31.840><c> uh</c><00:04:32.080><c> yeah</c><00:04:32.320><c> everything</c><00:04:32.720><c> everything</c><00:04:33.120><c> is</c>

00:04:33.270 --> 00:04:33.280 align:start position:0%
and uh yeah everything everything is
 

00:04:33.280 --> 00:04:34.629 align:start position:0%
and uh yeah everything everything is
great<00:04:33.600><c> what</c><00:04:34.080><c> now</c>

00:04:34.629 --> 00:04:34.639 align:start position:0%
great what now
 

00:04:34.639 --> 00:04:36.310 align:start position:0%
great what now
if<00:04:34.800><c> we</c><00:04:34.960><c> look</c><00:04:35.199><c> into</c><00:04:35.440><c> our</c><00:04:35.600><c> actual</c><00:04:35.840><c> database</c>

00:04:36.310 --> 00:04:36.320 align:start position:0%
if we look into our actual database
 

00:04:36.320 --> 00:04:38.790 align:start position:0%
if we look into our actual database
record<00:04:36.639><c> we</c><00:04:36.720><c> see</c><00:04:36.880><c> here</c><00:04:37.360><c> moderation</c><00:04:38.080><c> required</c>

00:04:38.790 --> 00:04:38.800 align:start position:0%
record we see here moderation required
 

00:04:38.800 --> 00:04:41.590 align:start position:0%
record we see here moderation required
is<00:04:38.960><c> set</c><00:04:39.199><c> to</c><00:04:39.440><c> true</c><00:04:39.919><c> why</c><00:04:40.240><c> is</c><00:04:40.479><c> that</c><00:04:40.800><c> actually</c><00:04:41.440><c> and</c>

00:04:41.590 --> 00:04:41.600 align:start position:0%
is set to true why is that actually and
 

00:04:41.600 --> 00:04:42.870 align:start position:0%
is set to true why is that actually and
the<00:04:41.680><c> reason</c><00:04:42.080><c> is</c><00:04:42.240><c> because</c>

00:04:42.870 --> 00:04:42.880 align:start position:0%
the reason is because
 

00:04:42.880 --> 00:04:44.870 align:start position:0%
the reason is because
within<00:04:43.280><c> our</c><00:04:43.360><c> admin</c><00:04:43.759><c> backend</c><00:04:44.240><c> we</c><00:04:44.320><c> also</c><00:04:44.560><c> have</c><00:04:44.720><c> a</c>

00:04:44.870 --> 00:04:44.880 align:start position:0%
within our admin backend we also have a
 

00:04:44.880 --> 00:04:47.270 align:start position:0%
within our admin backend we also have a
models<00:04:45.840><c> folder</c><00:04:46.320><c> over</c><00:04:46.560><c> here</c>

00:04:47.270 --> 00:04:47.280 align:start position:0%
models folder over here
 

00:04:47.280 --> 00:04:49.350 align:start position:0%
models folder over here
let's<00:04:47.520><c> actually</c><00:04:48.000><c> open</c><00:04:48.320><c> up</c><00:04:48.479><c> the</c><00:04:48.639><c> shop</c><00:04:48.960><c> record</c>

00:04:49.350 --> 00:04:49.360 align:start position:0%
let's actually open up the shop record
 

00:04:49.360 --> 00:04:51.430 align:start position:0%
let's actually open up the shop record
here<00:04:49.840><c> i'm</c><00:04:50.000><c> going</c><00:04:50.080><c> to</c><00:04:50.160><c> double</c><00:04:50.479><c> click</c><00:04:50.800><c> it</c>

00:04:51.430 --> 00:04:51.440 align:start position:0%
here i'm going to double click it
 

00:04:51.440 --> 00:04:55.110 align:start position:0%
here i'm going to double click it
and<00:04:52.160><c> we</c><00:04:52.560><c> we</c><00:04:52.720><c> just</c><00:04:52.960><c> added</c><00:04:53.600><c> a</c><00:04:53.840><c> new</c><00:04:54.320><c> field</c><00:04:54.800><c> here</c>

00:04:55.110 --> 00:04:55.120 align:start position:0%
and we we just added a new field here
 

00:04:55.120 --> 00:04:56.310 align:start position:0%
and we we just added a new field here
let's<00:04:55.280><c> see</c><00:04:55.440><c> where</c><00:04:55.680><c> is</c><00:04:55.840><c> it</c>

00:04:56.310 --> 00:04:56.320 align:start position:0%
let's see where is it
 

00:04:56.320 --> 00:04:59.030 align:start position:0%
let's see where is it
moderation<00:04:56.960><c> required</c><00:04:57.520><c> ah</c><00:04:57.759><c> it</c><00:04:57.919><c> must</c><00:04:58.160><c> have</c><00:04:58.240><c> been</c>

00:04:59.030 --> 00:04:59.040 align:start position:0%
moderation required ah it must have been
 

00:04:59.040 --> 00:04:59.990 align:start position:0%
moderation required ah it must have been
um

00:04:59.990 --> 00:05:00.000 align:start position:0%
um
 

00:05:00.000 --> 00:05:02.710 align:start position:0%
um
let's<00:05:00.240><c> see</c><00:05:01.120><c> it's</c><00:05:01.360><c> actually</c><00:05:01.759><c> not</c><00:05:02.000><c> here</c>

00:05:02.710 --> 00:05:02.720 align:start position:0%
let's see it's actually not here
 

00:05:02.720 --> 00:05:04.310 align:start position:0%
let's see it's actually not here
moderation

00:05:04.310 --> 00:05:04.320 align:start position:0%
moderation
 

00:05:04.320 --> 00:05:06.950 align:start position:0%
moderation
okay<00:05:04.720><c> interesting</c><00:05:05.120><c> it's</c><00:05:05.360><c> not</c><00:05:05.600><c> here</c><00:05:06.240><c> that</c><00:05:06.720><c> it</c>

00:05:06.950 --> 00:05:06.960 align:start position:0%
okay interesting it's not here that it
 

00:05:06.960 --> 00:05:08.310 align:start position:0%
okay interesting it's not here that it
got<00:05:07.199><c> propagated</c>

00:05:08.310 --> 00:05:08.320 align:start position:0%
got propagated
 

00:05:08.320 --> 00:05:12.390 align:start position:0%
got propagated
actually<00:05:09.120><c> probably</c><00:05:09.520><c> when</c><00:05:09.680><c> we</c><00:05:09.919><c> ran</c><00:05:10.720><c> our</c><00:05:11.280><c> um</c>

00:05:12.390 --> 00:05:12.400 align:start position:0%
actually probably when we ran our um
 

00:05:12.400 --> 00:05:14.629 align:start position:0%
actually probably when we ran our um
our<00:05:12.720><c> proxy</c><00:05:13.280><c> app</c><00:05:13.520><c> over</c><00:05:13.759><c> here</c><00:05:14.160><c> right</c><00:05:14.320><c> so</c><00:05:14.479><c> when</c>

00:05:14.629 --> 00:05:14.639 align:start position:0%
our proxy app over here right so when
 

00:05:14.639 --> 00:05:16.710 align:start position:0%
our proxy app over here right so when
this<00:05:14.960><c> proxy</c><00:05:15.440><c> app</c><00:05:15.680><c> loaded</c>

00:05:16.710 --> 00:05:16.720 align:start position:0%
this proxy app loaded
 

00:05:16.720 --> 00:05:19.270 align:start position:0%
this proxy app loaded
uh<00:05:17.120><c> i</c><00:05:17.199><c> guess</c><00:05:17.440><c> it</c><00:05:17.680><c> also</c><00:05:18.000><c> loaded</c><00:05:18.800><c> something</c>

00:05:19.270 --> 00:05:19.280 align:start position:0%
uh i guess it also loaded something
 

00:05:19.280 --> 00:05:21.270 align:start position:0%
uh i guess it also loaded something
within<00:05:19.680><c> our</c><00:05:19.840><c> proxy</c><00:05:20.400><c> app</c><00:05:20.639><c> that</c><00:05:20.800><c> i</c><00:05:20.880><c> was</c><00:05:21.039><c> doing</c>

00:05:21.270 --> 00:05:21.280 align:start position:0%
within our proxy app that i was doing
 

00:05:21.280 --> 00:05:23.350 align:start position:0%
within our proxy app that i was doing
the<00:05:21.440><c> other</c><00:05:21.600><c> day</c><00:05:21.919><c> proxy</c>

00:05:23.350 --> 00:05:23.360 align:start position:0%
the other day proxy
 

00:05:23.360 --> 00:05:26.790 align:start position:0%
the other day proxy
models<00:05:24.160><c> and</c><00:05:24.320><c> then</c><00:05:24.960><c> shop</c>

00:05:26.790 --> 00:05:26.800 align:start position:0%
models and then shop
 

00:05:26.800 --> 00:05:29.990 align:start position:0%
models and then shop
okay<00:05:27.520><c> uh</c><00:05:28.400><c> where's</c><00:05:28.639><c> moderation</c><00:05:29.280><c> required</c>

00:05:29.990 --> 00:05:30.000 align:start position:0%
okay uh where's moderation required
 

00:05:30.000 --> 00:05:32.390 align:start position:0%
okay uh where's moderation required
this<00:05:30.240><c> is</c><00:05:30.400><c> weird</c><00:05:31.280><c> oh</c><00:05:31.520><c> maybe</c><00:05:31.759><c> it's</c><00:05:31.919><c> in</c><00:05:32.000><c> the</c><00:05:32.160><c> front</c>

00:05:32.390 --> 00:05:32.400 align:start position:0%
this is weird oh maybe it's in the front
 

00:05:32.400 --> 00:05:35.350 align:start position:0%
this is weird oh maybe it's in the front
end<00:05:32.639><c> hold</c><00:05:32.960><c> on</c><00:05:33.120><c> let's</c><00:05:33.280><c> go</c><00:05:33.440><c> into</c><00:05:33.680><c> models</c><00:05:34.160><c> here</c>

00:05:35.350 --> 00:05:35.360 align:start position:0%
end hold on let's go into models here
 

00:05:35.360 --> 00:05:39.350 align:start position:0%
end hold on let's go into models here
pages<00:05:36.000><c> models</c><00:05:37.039><c> shop</c>

00:05:39.350 --> 00:05:39.360 align:start position:0%
pages models shop
 

00:05:39.360 --> 00:05:41.110 align:start position:0%
pages models shop
moderation<00:05:39.919><c> required</c><00:05:40.320><c> ah</c><00:05:40.479><c> there</c><00:05:40.639><c> it</c><00:05:40.800><c> is</c><00:05:40.880><c> okay</c>

00:05:41.110 --> 00:05:41.120 align:start position:0%
moderation required ah there it is okay
 

00:05:41.120 --> 00:05:43.590 align:start position:0%
moderation required ah there it is okay
moderation<00:05:41.759><c> required</c><00:05:42.320><c> is</c><00:05:42.479><c> set</c><00:05:42.720><c> within</c>

00:05:43.590 --> 00:05:43.600 align:start position:0%
moderation required is set within
 

00:05:43.600 --> 00:05:46.310 align:start position:0%
moderation required is set within
within<00:05:44.000><c> the</c><00:05:44.160><c> admin</c><00:05:44.639><c> front</c><00:05:45.039><c> end</c><00:05:45.600><c> and</c><00:05:46.000><c> it's</c><00:05:46.160><c> got</c>

00:05:46.310 --> 00:05:46.320 align:start position:0%
within the admin front end and it's got
 

00:05:46.320 --> 00:05:48.550 align:start position:0%
within the admin front end and it's got
a<00:05:46.400><c> type</c><00:05:46.720><c> of</c><00:05:46.880><c> boolean</c><00:05:47.440><c> and</c><00:05:47.520><c> the</c><00:05:47.680><c> default</c><00:05:48.160><c> is</c><00:05:48.320><c> to</c>

00:05:48.550 --> 00:05:48.560 align:start position:0%
a type of boolean and the default is to
 

00:05:48.560 --> 00:05:49.270 align:start position:0%
a type of boolean and the default is to
is<00:05:48.880><c> true</c>

00:05:49.270 --> 00:05:49.280 align:start position:0%
is true
 

00:05:49.280 --> 00:05:52.070 align:start position:0%
is true
so<00:05:49.919><c> even</c><00:05:50.160><c> though</c><00:05:50.400><c> we</c><00:05:50.560><c> have</c><00:05:50.720><c> an</c><00:05:50.880><c> explicit</c><00:05:51.759><c> just</c>

00:05:52.070 --> 00:05:52.080 align:start position:0%
so even though we have an explicit just
 

00:05:52.080 --> 00:05:54.629 align:start position:0%
so even though we have an explicit just
adding<00:05:52.560><c> that</c><00:05:52.800><c> into</c><00:05:53.039><c> the</c><00:05:53.199><c> models</c><00:05:53.680><c> file</c>

00:05:54.629 --> 00:05:54.639 align:start position:0%
adding that into the models file
 

00:05:54.639 --> 00:05:57.670 align:start position:0%
adding that into the models file
actually<00:05:55.280><c> set</c><00:05:55.440><c> that</c><00:05:55.680><c> moderation</c><00:05:56.400><c> required</c><00:05:57.360><c> to</c>

00:05:57.670 --> 00:05:57.680 align:start position:0%
actually set that moderation required to
 

00:05:57.680 --> 00:06:00.230 align:start position:0%
actually set that moderation required to
default<00:05:58.240><c> as</c><00:05:58.479><c> true</c><00:05:58.720><c> so</c><00:05:58.880><c> that's</c><00:05:59.039><c> good</c><00:05:59.360><c> it</c><00:05:59.440><c> means</c>

00:06:00.230 --> 00:06:00.240 align:start position:0%
default as true so that's good it means
 

00:06:00.240 --> 00:06:02.710 align:start position:0%
default as true so that's good it means
it<00:06:00.400><c> means</c><00:06:00.720><c> that</c><00:06:00.960><c> um</c><00:06:01.600><c> you</c><00:06:01.680><c> know</c><00:06:02.080><c> we</c><00:06:02.400><c> we</c><00:06:02.560><c> don't</c>

00:06:02.710 --> 00:06:02.720 align:start position:0%
it means that um you know we we don't
 

00:06:02.720 --> 00:06:04.309 align:start position:0%
it means that um you know we we don't
need<00:06:02.880><c> to</c><00:06:03.039><c> really</c>

00:06:04.309 --> 00:06:04.319 align:start position:0%
need to really
 

00:06:04.319 --> 00:06:06.469 align:start position:0%
need to really
do<00:06:04.560><c> we</c><00:06:04.720><c> don't</c><00:06:04.880><c> need</c><00:06:05.039><c> to</c><00:06:05.120><c> handle</c><00:06:05.440><c> cases</c><00:06:06.000><c> where</c>

00:06:06.469 --> 00:06:06.479 align:start position:0%
do we don't need to handle cases where
 

00:06:06.479 --> 00:06:08.309 align:start position:0%
do we don't need to handle cases where
where<00:06:06.639><c> there's</c><00:06:06.880><c> no</c><00:06:07.120><c> moderation</c><00:06:07.759><c> required</c>

00:06:08.309 --> 00:06:08.319 align:start position:0%
where there's no moderation required
 

00:06:08.319 --> 00:06:10.070 align:start position:0%
where there's no moderation required
column<00:06:08.639><c> on</c><00:06:08.800><c> the</c><00:06:08.880><c> shop</c><00:06:09.199><c> record</c>

00:06:10.070 --> 00:06:10.080 align:start position:0%
column on the shop record
 

00:06:10.080 --> 00:06:12.469 align:start position:0%
column on the shop record
so<00:06:10.240><c> that's</c><00:06:10.560><c> great</c><00:06:10.960><c> and</c><00:06:11.280><c> uh</c><00:06:11.520><c> yeah</c><00:06:11.759><c> so</c><00:06:12.160><c> just</c><00:06:12.319><c> the</c>

00:06:12.469 --> 00:06:12.479 align:start position:0%
so that's great and uh yeah so just the
 

00:06:12.479 --> 00:06:13.270 align:start position:0%
so that's great and uh yeah so just the
fact<00:06:12.720><c> that</c><00:06:12.880><c> you</c>

00:06:13.270 --> 00:06:13.280 align:start position:0%
fact that you
 

00:06:13.280 --> 00:06:15.830 align:start position:0%
fact that you
that<00:06:13.600><c> you</c><00:06:13.759><c> go</c><00:06:13.919><c> into</c><00:06:14.160><c> your</c><00:06:14.240><c> models</c><00:06:14.720><c> file</c><00:06:15.600><c> and</c>

00:06:15.830 --> 00:06:15.840 align:start position:0%
that you go into your models file and
 

00:06:15.840 --> 00:06:16.950 align:start position:0%
that you go into your models file and
you<00:06:16.160><c> set</c><00:06:16.319><c> a</c><00:06:16.479><c> new</c>

00:06:16.950 --> 00:06:16.960 align:start position:0%
you set a new
 

00:06:16.960 --> 00:06:20.070 align:start position:0%
you set a new
a<00:06:17.120><c> new</c><00:06:17.600><c> column</c><00:06:18.960><c> a</c><00:06:19.120><c> new</c><00:06:19.360><c> field</c><00:06:19.680><c> on</c>

00:06:20.070 --> 00:06:20.080 align:start position:0%
a new column a new field on
 

00:06:20.080 --> 00:06:22.710 align:start position:0%
a new column a new field on
on<00:06:20.319><c> this</c><00:06:20.560><c> collection</c><00:06:21.520><c> then</c><00:06:21.759><c> it</c><00:06:22.160><c> automatically</c>

00:06:22.710 --> 00:06:22.720 align:start position:0%
on this collection then it automatically
 

00:06:22.720 --> 00:06:24.710 align:start position:0%
on this collection then it automatically
sets<00:06:22.960><c> moderation</c><00:06:23.520><c> required</c><00:06:23.919><c> equals</c><00:06:24.240><c> true</c>

00:06:24.710 --> 00:06:24.720 align:start position:0%
sets moderation required equals true
 

00:06:24.720 --> 00:06:27.749 align:start position:0%
sets moderation required equals true
and<00:06:25.199><c> what</c><00:06:25.360><c> that</c><00:06:25.600><c> means</c><00:06:25.919><c> is</c><00:06:26.800><c> uh</c><00:06:27.199><c> everything</c><00:06:27.600><c> is</c>

00:06:27.749 --> 00:06:27.759 align:start position:0%
and what that means is uh everything is
 

00:06:27.759 --> 00:06:29.029 align:start position:0%
and what that means is uh everything is
pretty<00:06:27.919><c> much</c><00:06:28.160><c> all</c><00:06:28.400><c> plugged</c><00:06:28.800><c> in</c>

00:06:29.029 --> 00:06:29.039 align:start position:0%
pretty much all plugged in
 

00:06:29.039 --> 00:06:32.309 align:start position:0%
pretty much all plugged in
over<00:06:29.280><c> here</c><00:06:29.919><c> uh</c><00:06:30.240><c> let's</c><00:06:30.479><c> go</c><00:06:31.039><c> into</c>

00:06:32.309 --> 00:06:32.319 align:start position:0%
over here uh let's go into
 

00:06:32.319 --> 00:06:33.830 align:start position:0%
over here uh let's go into
and<00:06:32.400><c> that</c><00:06:32.639><c> means</c><00:06:32.960><c> that</c><00:06:33.280><c> you</c><00:06:33.360><c> know</c><00:06:33.520><c> we</c><00:06:33.680><c> can</c>

00:06:33.830 --> 00:06:33.840 align:start position:0%
and that means that you know we can
 

00:06:33.840 --> 00:06:35.350 align:start position:0%
and that means that you know we can
close<00:06:34.160><c> all</c><00:06:34.319><c> this</c><00:06:34.479><c> stuff</c><00:06:34.800><c> over</c><00:06:35.039><c> here</c><00:06:35.199><c> because</c>

00:06:35.350 --> 00:06:35.360 align:start position:0%
close all this stuff over here because
 

00:06:35.360 --> 00:06:38.150 align:start position:0%
close all this stuff over here because
it's<00:06:35.440><c> starting</c><00:06:35.759><c> to</c><00:06:35.840><c> get</c><00:06:36.000><c> confusing</c>

00:06:38.150 --> 00:06:38.160 align:start position:0%
it's starting to get confusing
 

00:06:38.160 --> 00:06:39.990 align:start position:0%
it's starting to get confusing
what<00:06:38.319><c> we</c><00:06:38.479><c> need</c><00:06:38.639><c> to</c><00:06:38.720><c> do</c><00:06:38.960><c> now</c><00:06:39.199><c> is</c><00:06:39.440><c> actually</c><00:06:39.840><c> go</c>

00:06:39.990 --> 00:06:40.000 align:start position:0%
what we need to do now is actually go
 

00:06:40.000 --> 00:06:41.590 align:start position:0%
what we need to do now is actually go
into<00:06:40.240><c> our</c><00:06:40.400><c> admin</c><00:06:40.880><c> front</c><00:06:41.199><c> end</c>

00:06:41.590 --> 00:06:41.600 align:start position:0%
into our admin front end
 

00:06:41.600 --> 00:06:44.790 align:start position:0%
into our admin front end
and<00:06:41.759><c> go</c><00:06:42.000><c> back</c><00:06:42.560><c> into</c><00:06:43.840><c> pages</c>

00:06:44.790 --> 00:06:44.800 align:start position:0%
and go back into pages
 

00:06:44.800 --> 00:06:49.270 align:start position:0%
and go back into pages
and<00:06:44.960><c> then</c><00:06:45.280><c> settings</c><00:06:46.840><c> index</c>

00:06:49.270 --> 00:06:49.280 align:start position:0%
and then settings index
 

00:06:49.280 --> 00:06:51.749 align:start position:0%
and then settings index
and<00:06:49.759><c> uh</c><00:06:50.000><c> yeah</c><00:06:50.240><c> and</c><00:06:50.319><c> we</c><00:06:50.560><c> know</c><00:06:50.880><c> over</c><00:06:51.120><c> here</c><00:06:51.440><c> set</c>

00:06:51.749 --> 00:06:51.759 align:start position:0%
and uh yeah and we know over here set
 

00:06:51.759 --> 00:06:53.270 align:start position:0%
and uh yeah and we know over here set
moderation<00:06:52.400><c> required</c>

00:06:53.270 --> 00:06:53.280 align:start position:0%
moderation required
 

00:06:53.280 --> 00:06:56.469 align:start position:0%
moderation required
by<00:06:53.759><c> is</c><00:06:54.160><c> data</c><00:06:54.479><c> dot</c><00:06:54.720><c> moderation</c><00:06:55.440><c> required</c>

00:06:56.469 --> 00:06:56.479 align:start position:0%
by is data dot moderation required
 

00:06:56.479 --> 00:06:59.350 align:start position:0%
by is data dot moderation required
okay<00:06:57.280><c> i'm</c><00:06:57.440><c> just</c><00:06:57.599><c> going</c><00:06:57.759><c> to</c><00:06:57.840><c> leave</c><00:06:58.080><c> it</c><00:06:58.240><c> as</c><00:06:58.479><c> that</c>

00:06:59.350 --> 00:06:59.360 align:start position:0%
okay i'm just going to leave it as that
 

00:06:59.360 --> 00:06:59.990 align:start position:0%
okay i'm just going to leave it as that
and

00:06:59.990 --> 00:07:00.000 align:start position:0%
and
 

00:07:00.000 --> 00:07:01.350 align:start position:0%
and
because<00:07:00.160><c> we</c><00:07:00.319><c> don't</c><00:07:00.479><c> need</c><00:07:00.800><c> we</c><00:07:00.960><c> don't</c><00:07:01.120><c> need</c><00:07:01.280><c> to</c>

00:07:01.350 --> 00:07:01.360 align:start position:0%
because we don't need we don't need to
 

00:07:01.360 --> 00:07:03.189 align:start position:0%
because we don't need we don't need to
handle<00:07:01.680><c> cases</c><00:07:02.000><c> where</c><00:07:02.240><c> moderation</c><00:07:02.800><c> required</c>

00:07:03.189 --> 00:07:03.199 align:start position:0%
handle cases where moderation required
 

00:07:03.199 --> 00:07:04.070 align:start position:0%
handle cases where moderation required
is<00:07:03.360><c> null</c>

00:07:04.070 --> 00:07:04.080 align:start position:0%
is null
 

00:07:04.080 --> 00:07:06.070 align:start position:0%
is null
this<00:07:04.319><c> function</c><00:07:04.720><c> as</c><00:07:04.880><c> we</c><00:07:05.039><c> recall</c><00:07:05.440><c> it</c><00:07:05.599><c> says</c><00:07:05.840><c> get</c>

00:07:06.070 --> 00:07:06.080 align:start position:0%
this function as we recall it says get
 

00:07:06.080 --> 00:07:08.710 align:start position:0%
this function as we recall it says get
profile<00:07:06.479><c> says</c><00:07:06.720><c> go</c><00:07:06.880><c> back</c><00:07:07.120><c> into</c><00:07:07.360><c> the</c><00:07:07.520><c> database</c>

00:07:08.710 --> 00:07:08.720 align:start position:0%
profile says go back into the database
 

00:07:08.720 --> 00:07:10.870 align:start position:0%
profile says go back into the database
get<00:07:08.960><c> the</c><00:07:09.120><c> data</c><00:07:09.440><c> about</c><00:07:09.759><c> the</c><00:07:09.919><c> current</c><00:07:10.319><c> shop</c>

00:07:10.870 --> 00:07:10.880 align:start position:0%
get the data about the current shop
 

00:07:10.880 --> 00:07:12.070 align:start position:0%
get the data about the current shop
based<00:07:11.199><c> off</c><00:07:11.440><c> of</c><00:07:11.599><c> that</c>

00:07:12.070 --> 00:07:12.080 align:start position:0%
based off of that
 

00:07:12.080 --> 00:07:14.950 align:start position:0%
based off of that
based<00:07:12.319><c> off</c><00:07:12.560><c> of</c><00:07:12.720><c> the</c><00:07:12.960><c> uh</c><00:07:13.199><c> the</c><00:07:13.360><c> shop</c><00:07:13.759><c> url</c><00:07:14.800><c> and</c>

00:07:14.950 --> 00:07:14.960 align:start position:0%
based off of the uh the shop url and
 

00:07:14.960 --> 00:07:16.070 align:start position:0%
based off of the uh the shop url and
then<00:07:15.199><c> set</c><00:07:15.440><c> all</c><00:07:15.599><c> that</c>

00:07:16.070 --> 00:07:16.080 align:start position:0%
then set all that
 

00:07:16.080 --> 00:07:18.469 align:start position:0%
then set all that
all<00:07:16.240><c> the</c><00:07:16.400><c> default</c><00:07:16.960><c> settings</c><00:07:17.360><c> of</c><00:07:17.520><c> the</c><00:07:17.759><c> shop</c><00:07:18.240><c> uh</c>

00:07:18.469 --> 00:07:18.479 align:start position:0%
all the default settings of the shop uh
 

00:07:18.479 --> 00:07:20.150 align:start position:0%
all the default settings of the shop uh
within<00:07:18.800><c> the</c><00:07:18.960><c> state</c><00:07:19.280><c> of</c><00:07:19.360><c> the</c><00:07:19.520><c> component</c>

00:07:20.150 --> 00:07:20.160 align:start position:0%
within the state of the component
 

00:07:20.160 --> 00:07:22.710 align:start position:0%
within the state of the component
all<00:07:20.240><c> right</c><00:07:20.400><c> so</c><00:07:20.560><c> that's</c><00:07:20.800><c> already</c><00:07:21.120><c> being</c><00:07:21.440><c> set</c><00:07:22.240><c> uh</c>

00:07:22.710 --> 00:07:22.720 align:start position:0%
all right so that's already being set uh
 

00:07:22.720 --> 00:07:24.390 align:start position:0%
all right so that's already being set uh
and<00:07:22.880><c> everything</c><00:07:23.199><c> is</c><00:07:23.360><c> all</c><00:07:23.520><c> good</c><00:07:23.759><c> with</c><00:07:23.919><c> that</c>

00:07:24.390 --> 00:07:24.400 align:start position:0%
and everything is all good with that
 

00:07:24.400 --> 00:07:27.990 align:start position:0%
and everything is all good with that
um<00:07:26.080><c> now</c><00:07:26.560><c> now</c><00:07:26.720><c> that</c><00:07:26.800><c> we're</c><00:07:27.039><c> already</c><00:07:27.360><c> here</c><00:07:27.680><c> maybe</c>

00:07:27.990 --> 00:07:28.000 align:start position:0%
um now now that we're already here maybe
 

00:07:28.000 --> 00:07:29.270 align:start position:0%
um now now that we're already here maybe
we<00:07:28.160><c> should</c><00:07:28.319><c> actually</c><00:07:28.880><c> go</c>

00:07:29.270 --> 00:07:29.280 align:start position:0%
we should actually go
 

00:07:29.280 --> 00:07:32.390 align:start position:0%
we should actually go
into<00:07:30.560><c> this</c><00:07:30.800><c> function</c><00:07:31.280><c> toggle</c><00:07:31.759><c> approval</c>

00:07:32.390 --> 00:07:32.400 align:start position:0%
into this function toggle approval
 

00:07:32.400 --> 00:07:33.110 align:start position:0%
into this function toggle approval
settings

00:07:33.110 --> 00:07:33.120 align:start position:0%
settings
 

00:07:33.120 --> 00:07:35.029 align:start position:0%
settings
over<00:07:33.360><c> here</c><00:07:33.599><c> ah</c><00:07:33.919><c> we</c><00:07:34.080><c> actually</c><00:07:34.400><c> need</c><00:07:34.560><c> to</c><00:07:34.720><c> create</c>

00:07:35.029 --> 00:07:35.039 align:start position:0%
over here ah we actually need to create
 

00:07:35.039 --> 00:07:36.550 align:start position:0%
over here ah we actually need to create
a<00:07:35.120><c> new</c><00:07:35.360><c> function</c>

00:07:36.550 --> 00:07:36.560 align:start position:0%
a new function
 

00:07:36.560 --> 00:07:40.710 align:start position:0%
a new function
uh<00:07:36.960><c> now</c><00:07:37.680><c> um</c><00:07:39.039><c> and</c><00:07:39.360><c> that's</c><00:07:39.680><c> gonna</c><00:07:40.000><c> actually</c><00:07:40.479><c> talk</c>

00:07:40.710 --> 00:07:40.720 align:start position:0%
uh now um and that's gonna actually talk
 

00:07:40.720 --> 00:07:41.830 align:start position:0%
uh now um and that's gonna actually talk
with<00:07:40.880><c> our</c><00:07:41.039><c> database</c>

00:07:41.830 --> 00:07:41.840 align:start position:0%
with our database
 

00:07:41.840 --> 00:07:44.150 align:start position:0%
with our database
all<00:07:41.919><c> right</c><00:07:42.160><c> guys</c><00:07:42.319><c> so</c><00:07:43.039><c> uh</c><00:07:43.280><c> in</c><00:07:43.440><c> the</c><00:07:43.520><c> next</c><00:07:43.840><c> video</c>

00:07:44.150 --> 00:07:44.160 align:start position:0%
all right guys so uh in the next video
 

00:07:44.160 --> 00:07:44.950 align:start position:0%
all right guys so uh in the next video
now<00:07:44.319><c> that</c><00:07:44.479><c> we've</c>

00:07:44.950 --> 00:07:44.960 align:start position:0%
now that we've
 

00:07:44.960 --> 00:07:47.670 align:start position:0%
now that we've
uh<00:07:45.199><c> verified</c><00:07:45.919><c> that</c><00:07:46.160><c> our</c><00:07:46.879><c> that</c><00:07:47.039><c> our</c><00:07:47.199><c> state</c><00:07:47.520><c> is</c>

00:07:47.670 --> 00:07:47.680 align:start position:0%
uh verified that our that our state is
 

00:07:47.680 --> 00:07:49.110 align:start position:0%
uh verified that our that our state is
coming<00:07:47.919><c> in</c><00:07:48.160><c> correctly</c>

00:07:49.110 --> 00:07:49.120 align:start position:0%
coming in correctly
 

00:07:49.120 --> 00:07:51.990 align:start position:0%
coming in correctly
we<00:07:49.440><c> we're</c><00:07:49.680><c> gonna</c><00:07:49.840><c> go</c><00:07:50.080><c> back</c><00:07:50.319><c> into</c><00:07:50.560><c> the</c><00:07:50.720><c> actions</c>

00:07:51.990 --> 00:07:52.000 align:start position:0%
we we're gonna go back into the actions
 

00:07:52.000 --> 00:07:52.390 align:start position:0%
we we're gonna go back into the actions
uh

00:07:52.390 --> 00:07:52.400 align:start position:0%
uh
 

00:07:52.400 --> 00:07:55.430 align:start position:0%
uh
file<00:07:52.960><c> we're</c><00:07:53.120><c> gonna</c><00:07:53.360><c> go</c><00:07:53.680><c> into</c><00:07:54.400><c> user</c>

00:07:55.430 --> 00:07:55.440 align:start position:0%
file we're gonna go into user
 

00:07:55.440 --> 00:07:58.869 align:start position:0%
file we're gonna go into user
over<00:07:55.759><c> here</c><00:07:56.560><c> and</c><00:07:56.720><c> we</c><00:07:56.879><c> need</c><00:07:56.960><c> to</c><00:07:57.199><c> create</c><00:07:57.840><c> a</c><00:07:58.000><c> new</c>

00:07:58.869 --> 00:07:58.879 align:start position:0%
over here and we need to create a new
 

00:07:58.879 --> 00:08:01.589 align:start position:0%
over here and we need to create a new
a<00:07:58.960><c> new</c><00:07:59.280><c> file</c><00:08:00.080><c> a</c><00:08:00.400><c> new</c><00:08:00.639><c> function</c><00:08:01.039><c> let's</c><00:08:01.199><c> actually</c>

00:08:01.589 --> 00:08:01.599 align:start position:0%
a new file a new function let's actually
 

00:08:01.599 --> 00:08:03.510 align:start position:0%
a new file a new function let's actually
just<00:08:01.759><c> do</c><00:08:01.919><c> that</c><00:08:02.080><c> now</c><00:08:02.400><c> quickly</c>

00:08:03.510 --> 00:08:03.520 align:start position:0%
just do that now quickly
 

00:08:03.520 --> 00:08:06.629 align:start position:0%
just do that now quickly
okay<00:08:04.319><c> it's</c><00:08:04.400><c> going</c><00:08:04.479><c> to</c><00:08:04.639><c> be</c><00:08:04.720><c> called</c><00:08:04.960><c> get</c><00:08:05.280><c> profile</c>

00:08:06.629 --> 00:08:06.639 align:start position:0%
okay it's going to be called get profile
 

00:08:06.639 --> 00:08:09.270 align:start position:0%
okay it's going to be called get profile
now<00:08:06.960><c> it</c><00:08:07.120><c> should</c><00:08:07.280><c> be</c><00:08:07.440><c> a</c><00:08:07.520><c> post</c><00:08:07.840><c> request</c><00:08:08.800><c> or</c><00:08:08.960><c> a</c><00:08:09.039><c> put</c>

00:08:09.270 --> 00:08:09.280 align:start position:0%
now it should be a post request or a put
 

00:08:09.280 --> 00:08:09.830 align:start position:0%
now it should be a post request or a put
request

00:08:09.830 --> 00:08:09.840 align:start position:0%
request
 

00:08:09.840 --> 00:08:12.710 align:start position:0%
request
okay<00:08:10.160><c> update</c><00:08:11.199><c> we</c><00:08:11.440><c> have</c><00:08:11.520><c> the</c><00:08:11.759><c> update</c><00:08:12.240><c> function</c>

00:08:12.710 --> 00:08:12.720 align:start position:0%
okay update we have the update function
 

00:08:12.720 --> 00:08:15.350 align:start position:0%
okay update we have the update function
here

00:08:15.350 --> 00:08:15.360 align:start position:0%
 
 

00:08:15.360 --> 00:08:17.430 align:start position:0%
 
okay<00:08:15.840><c> that's</c><00:08:16.080><c> probably</c><00:08:16.879><c> probably</c><00:08:17.120><c> gets</c>

00:08:17.430 --> 00:08:17.440 align:start position:0%
okay that's probably probably gets
 

00:08:17.440 --> 00:08:19.350 align:start position:0%
okay that's probably probably gets
triggered<00:08:17.759><c> when</c><00:08:17.919><c> somebody</c><00:08:18.400><c> presses</c>

00:08:19.350 --> 00:08:19.360 align:start position:0%
triggered when somebody presses
 

00:08:19.360 --> 00:08:21.589 align:start position:0%
triggered when somebody presses
presses<00:08:19.919><c> save</c><00:08:20.479><c> but</c><00:08:20.639><c> we</c><00:08:20.720><c> want</c><00:08:20.879><c> to</c><00:08:20.960><c> create</c><00:08:21.280><c> a</c><00:08:21.360><c> new</c>

00:08:21.589 --> 00:08:21.599 align:start position:0%
presses save but we want to create a new
 

00:08:21.599 --> 00:08:23.110 align:start position:0%
presses save but we want to create a new
function<00:08:22.080><c> which</c><00:08:22.319><c> is</c><00:08:22.400><c> going</c><00:08:22.479><c> to</c><00:08:22.639><c> be</c>

00:08:23.110 --> 00:08:23.120 align:start position:0%
function which is going to be
 

00:08:23.120 --> 00:08:26.710 align:start position:0%
function which is going to be
called<00:08:23.520><c> set</c><00:08:23.840><c> moderation</c><00:08:25.360><c> okay</c><00:08:25.680><c> method</c><00:08:26.160><c> put</c>

00:08:26.710 --> 00:08:26.720 align:start position:0%
called set moderation okay method put
 

00:08:26.720 --> 00:08:31.029 align:start position:0%
called set moderation okay method put
method<00:08:27.199><c> get</c><00:08:27.599><c> yeah</c><00:08:29.199><c> chelsea</c>

00:08:31.029 --> 00:08:31.039 align:start position:0%
method get yeah chelsea
 

00:08:31.039 --> 00:08:35.909 align:start position:0%
method get yeah chelsea
okay<00:08:31.520><c> cool</c><00:08:32.159><c> one</c><00:08:32.399><c> sec</c><00:08:32.719><c> guys</c><00:08:34.000><c> cool</c>

00:08:35.909 --> 00:08:35.919 align:start position:0%
okay cool one sec guys cool
 

00:08:35.919 --> 00:08:40.870 align:start position:0%
okay cool one sec guys cool
set<00:08:38.839><c> moderation</c>

00:08:40.870 --> 00:08:40.880 align:start position:0%
set moderation
 

00:08:40.880 --> 00:08:42.790 align:start position:0%
set moderation
and<00:08:41.120><c> yada</c><00:08:41.440><c> yada</c><00:08:41.760><c> in</c><00:08:41.839><c> the</c><00:08:41.919><c> next</c><00:08:42.159><c> video</c><00:08:42.479><c> we're</c>

00:08:42.790 --> 00:08:42.800 align:start position:0%
and yada yada in the next video we're
 

00:08:42.800 --> 00:08:44.630 align:start position:0%
and yada yada in the next video we're
we're<00:08:43.039><c> gonna</c><00:08:43.360><c> we're</c><00:08:43.599><c> gonna</c><00:08:43.839><c> continue</c>

00:08:44.630 --> 00:08:44.640 align:start position:0%
we're gonna we're gonna continue
 

00:08:44.640 --> 00:08:46.230 align:start position:0%
we're gonna we're gonna continue
uh<00:08:44.880><c> if</c><00:08:45.040><c> you</c><00:08:45.120><c> have</c><00:08:45.200><c> any</c><00:08:45.440><c> questions</c><00:08:45.839><c> along</c><00:08:46.160><c> the</c>

00:08:46.230 --> 00:08:46.240 align:start position:0%
uh if you have any questions along the
 

00:08:46.240 --> 00:08:48.550 align:start position:0%
uh if you have any questions along the
way<00:08:46.560><c> because</c><00:08:46.959><c> uh</c><00:08:47.600><c> we've</c><00:08:47.839><c> done</c><00:08:48.000><c> a</c><00:08:48.080><c> good</c><00:08:48.240><c> amount</c>

00:08:48.550 --> 00:08:48.560 align:start position:0%
way because uh we've done a good amount
 

00:08:48.560 --> 00:08:49.190 align:start position:0%
way because uh we've done a good amount
here<00:08:48.800><c> in</c><00:08:48.880><c> this</c>

00:08:49.190 --> 00:08:49.200 align:start position:0%
here in this
 

00:08:49.200 --> 00:08:50.389 align:start position:0%
here in this
in<00:08:49.279><c> this</c><00:08:49.440><c> video</c><00:08:49.680><c> we</c><00:08:49.839><c> pretty</c><00:08:50.000><c> much</c><00:08:50.240><c> just</c>

00:08:50.389 --> 00:08:50.399 align:start position:0%
in this video we pretty much just
 

00:08:50.399 --> 00:08:52.389 align:start position:0%
in this video we pretty much just
clarified<00:08:50.959><c> the</c><00:08:51.040><c> structure</c><00:08:51.440><c> of</c><00:08:51.600><c> how</c><00:08:51.760><c> the</c><00:08:52.000><c> admin</c>

00:08:52.389 --> 00:08:52.399 align:start position:0%
clarified the structure of how the admin
 

00:08:52.399 --> 00:08:53.350 align:start position:0%
clarified the structure of how the admin
front<00:08:52.640><c> end</c><00:08:52.800><c> talks</c>

00:08:53.350 --> 00:08:53.360 align:start position:0%
front end talks
 

00:08:53.360 --> 00:08:55.110 align:start position:0%
front end talks
with<00:08:53.519><c> the</c><00:08:53.680><c> admin</c><00:08:54.080><c> back</c><00:08:54.320><c> end</c><00:08:54.560><c> and</c><00:08:54.720><c> back</c><00:08:54.959><c> and</c>

00:08:55.110 --> 00:08:55.120 align:start position:0%
with the admin back end and back and
 

00:08:55.120 --> 00:08:57.110 align:start position:0%
with the admin back end and back and
forth<00:08:55.440><c> and</c><00:08:55.600><c> how</c><00:08:55.760><c> we</c><00:08:55.920><c> would</c><00:08:56.080><c> go</c><00:08:56.320><c> about</c>

00:08:57.110 --> 00:08:57.120 align:start position:0%
forth and how we would go about
 

00:08:57.120 --> 00:09:00.790 align:start position:0%
forth and how we would go about
um<00:08:58.080><c> you</c><00:08:58.160><c> know</c><00:08:58.800><c> making</c><00:08:59.440><c> making</c><00:08:59.839><c> updates</c><00:09:00.399><c> uh</c>

00:09:00.790 --> 00:09:00.800 align:start position:0%
um you know making making updates uh
 

00:09:00.800 --> 00:09:02.150 align:start position:0%
um you know making making updates uh
from<00:09:01.120><c> the</c><00:09:01.279><c> front</c><00:09:01.519><c> end</c><00:09:01.680><c> alright</c><00:09:01.839><c> guys</c><00:09:02.000><c> so</c>

00:09:02.150 --> 00:09:02.160 align:start position:0%
from the front end alright guys so
 

00:09:02.160 --> 00:09:03.750 align:start position:0%
from the front end alright guys so
that's<00:09:02.399><c> the</c><00:09:02.480><c> game</c><00:09:02.720><c> plan</c><00:09:03.040><c> for</c><00:09:03.200><c> the</c><00:09:03.279><c> next</c><00:09:03.519><c> video</c>

00:09:03.750 --> 00:09:03.760 align:start position:0%
that's the game plan for the next video
 

00:09:03.760 --> 00:09:04.949 align:start position:0%
that's the game plan for the next video
if<00:09:03.839><c> you</c><00:09:03.920><c> have</c><00:09:04.000><c> any</c><00:09:04.160><c> questions</c><00:09:04.560><c> feel</c><00:09:04.720><c> free</c><00:09:04.880><c> to</c>

00:09:04.949 --> 00:09:04.959 align:start position:0%
if you have any questions feel free to
 

00:09:04.959 --> 00:09:05.670 align:start position:0%
if you have any questions feel free to
reach<00:09:05.200><c> out</c>

00:09:05.670 --> 00:09:05.680 align:start position:0%
reach out
 

00:09:05.680 --> 00:09:09.839 align:start position:0%
reach out
and<00:09:05.839><c> i'll</c><00:09:06.000><c> see</c><00:09:06.160><c> you</c><00:09:06.399><c> in</c><00:09:06.640><c> the</c><00:09:06.720><c> next</c><00:09:06.959><c> one</c>

