WEBVTT
Kind: captions
Language: en

00:00:06.240 --> 00:00:08.470 align:start position:0%
 
you<00:00:06.399><c> can</c><00:00:06.640><c> find</c><00:00:06.919><c> models</c><00:00:07.359><c> that</c><00:00:07.520><c> can</c><00:00:07.640><c> be</c><00:00:07.799><c> deployed</c>

00:00:08.470 --> 00:00:08.480 align:start position:0%
you can find models that can be deployed
 

00:00:08.480 --> 00:00:11.070 align:start position:0%
you can find models that can be deployed
as<00:00:08.800><c> private</c><00:00:09.200><c> API</c><00:00:09.599><c> endpoints</c><00:00:10.080><c> on</c><00:00:10.320><c> salemaker</c>

00:00:11.070 --> 00:00:11.080 align:start position:0%
as private API endpoints on salemaker
 

00:00:11.080 --> 00:00:16.230 align:start position:0%
as private API endpoints on salemaker
using<00:00:11.400><c> the</c><00:00:11.559><c> Johns</c><00:00:12.000><c> Labs</c>

00:00:16.230 --> 00:00:16.240 align:start position:0%
 
 

00:00:16.240 --> 00:00:19.029 align:start position:0%
 
website<00:00:17.240><c> today</c><00:00:17.680><c> I</c><00:00:17.760><c> am</c><00:00:17.960><c> going</c><00:00:18.119><c> to</c><00:00:18.320><c> deploy</c><00:00:18.760><c> our</c>

00:00:19.029 --> 00:00:19.039 align:start position:0%
website today I am going to deploy our
 

00:00:19.039 --> 00:00:21.509 align:start position:0%
website today I am going to deploy our
medical<00:00:19.400><c> llm</c><00:00:19.840><c> model</c><00:00:20.359><c> to</c><00:00:20.560><c> salemaker</c><00:00:21.199><c> as</c><00:00:21.320><c> a</c>

00:00:21.509 --> 00:00:21.519 align:start position:0%
medical llm model to salemaker as a
 

00:00:21.519 --> 00:00:24.189 align:start position:0%
medical llm model to salemaker as a
private<00:00:21.880><c> API</c>

00:00:24.189 --> 00:00:24.199 align:start position:0%
private API
 

00:00:24.199 --> 00:00:28.150 align:start position:0%
private API
endpoint<00:00:25.199><c> this</c><00:00:25.400><c> model</c><00:00:25.760><c> Medical</c><00:00:26.480><c> llm</c><00:00:27.480><c> exells</c>

00:00:28.150 --> 00:00:28.160 align:start position:0%
endpoint this model Medical llm exells
 

00:00:28.160 --> 00:00:31.230 align:start position:0%
endpoint this model Medical llm exells
in<00:00:28.720><c> answering</c><00:00:29.240><c> complex</c><00:00:29.960><c> IAL</c><00:00:30.320><c> questions</c><00:00:31.119><c> as</c>

00:00:31.230 --> 00:00:31.240 align:start position:0%
in answering complex IAL questions as
 

00:00:31.240 --> 00:00:33.830 align:start position:0%
in answering complex IAL questions as
well<00:00:31.480><c> as</c><00:00:31.920><c> summarizing</c><00:00:32.920><c> clinical</c><00:00:33.399><c> reports</c>

00:00:33.830 --> 00:00:33.840 align:start position:0%
well as summarizing clinical reports
 

00:00:33.840 --> 00:00:41.350 align:start position:0%
well as summarizing clinical reports
into<00:00:34.360><c> digestible</c>

00:00:41.350 --> 00:00:41.360 align:start position:0%
 
 

00:00:41.360 --> 00:00:43.950 align:start position:0%
 
summaries<00:00:42.360><c> to</c><00:00:42.559><c> start</c><00:00:42.920><c> the</c><00:00:43.079><c> deployment</c>

00:00:43.950 --> 00:00:43.960 align:start position:0%
summaries to start the deployment
 

00:00:43.960 --> 00:00:46.229 align:start position:0%
summaries to start the deployment
process<00:00:44.960><c> click</c><00:00:45.239><c> on</c><00:00:45.480><c> the</c><00:00:45.640><c> deploy</c><00:00:46.079><c> to</c>

00:00:46.229 --> 00:00:46.239 align:start position:0%
process click on the deploy to
 

00:00:46.239 --> 00:00:49.389 align:start position:0%
process click on the deploy to
salesmaker<00:00:46.879><c> button</c><00:00:47.280><c> available</c><00:00:47.719><c> on</c><00:00:47.879><c> the</c><00:00:48.039><c> model</c>

00:00:49.389 --> 00:00:49.399 align:start position:0%
salesmaker button available on the model
 

00:00:49.399 --> 00:00:52.389 align:start position:0%
salesmaker button available on the model
page<00:00:50.399><c> this</c><00:00:50.559><c> will</c><00:00:50.840><c> open</c><00:00:51.199><c> up</c><00:00:51.480><c> the</c><00:00:51.680><c> salesmaker</c>

00:00:52.389 --> 00:00:52.399 align:start position:0%
page this will open up the salesmaker
 

00:00:52.399 --> 00:00:56.430 align:start position:0%
page this will open up the salesmaker
marketplace<00:00:53.079><c> listing</c><00:00:53.440><c> for</c><00:00:53.680><c> the</c>

00:00:56.430 --> 00:00:56.440 align:start position:0%
 
 

00:00:56.440 --> 00:00:59.150 align:start position:0%
 
model<00:00:57.440><c> there</c><00:00:57.640><c> is</c><00:00:57.840><c> some</c><00:00:58.079><c> helpful</c><00:00:58.559><c> information</c>

00:00:59.150 --> 00:00:59.160 align:start position:0%
model there is some helpful information
 

00:00:59.160 --> 00:01:01.389 align:start position:0%
model there is some helpful information
about<00:00:59.440><c> the</c><00:00:59.559><c> model</c><00:01:00.000><c> on</c><00:01:00.120><c> the</c><00:01:00.239><c> listing</c>

00:01:01.389 --> 00:01:01.399 align:start position:0%
about the model on the listing
 

00:01:01.399 --> 00:01:04.350 align:start position:0%
about the model on the listing
page<00:01:02.399><c> on</c><00:01:02.600><c> the</c><00:01:02.719><c> US</c><00:01:03.160><c> section</c><00:01:03.480><c> you</c><00:01:03.600><c> can</c><00:01:03.840><c> find</c><00:01:04.159><c> the</c>

00:01:04.350 --> 00:01:04.360 align:start position:0%
page on the US section you can find the
 

00:01:04.360 --> 00:01:07.429 align:start position:0%
page on the US section you can find the
input<00:01:04.720><c> format</c><00:01:05.199><c> the</c><00:01:05.320><c> endpoint</c><00:01:06.280><c> expects</c><00:01:07.280><c> as</c>

00:01:07.429 --> 00:01:07.439 align:start position:0%
input format the endpoint expects as
 

00:01:07.439 --> 00:01:11.230 align:start position:0%
input format the endpoint expects as
well<00:01:07.600><c> as</c><00:01:07.759><c> the</c><00:01:07.960><c> output</c><00:01:08.320><c> format</c><00:01:08.799><c> the</c><00:01:09.000><c> endpoint</c>

00:01:11.230 --> 00:01:11.240 align:start position:0%
well as the output format the endpoint
 

00:01:11.240 --> 00:01:14.350 align:start position:0%
well as the output format the endpoint
returns<00:01:12.240><c> let's</c><00:01:13.000><c> click</c><00:01:13.240><c> on</c><00:01:13.479><c> the</c><00:01:13.720><c> continue</c><00:01:14.119><c> to</c>

00:01:14.350 --> 00:01:14.360 align:start position:0%
returns let's click on the continue to
 

00:01:14.360 --> 00:01:19.870 align:start position:0%
returns let's click on the continue to
subscribe<00:01:14.880><c> button</c><00:01:15.159><c> and</c><00:01:15.400><c> accept</c><00:01:15.759><c> the</c>

00:01:19.870 --> 00:01:19.880 align:start position:0%
 
 

00:01:19.880 --> 00:01:22.590 align:start position:0%
 
offer<00:01:20.880><c> once</c><00:01:21.159><c> you</c><00:01:21.360><c> are</c><00:01:21.640><c> subscribed</c><00:01:22.159><c> to</c><00:01:22.320><c> the</c>

00:01:22.590 --> 00:01:22.600 align:start position:0%
offer once you are subscribed to the
 

00:01:22.600 --> 00:01:24.950 align:start position:0%
offer once you are subscribed to the
product<00:01:23.600><c> click</c><00:01:23.840><c> on</c><00:01:24.079><c> the</c><00:01:24.320><c> continue</c><00:01:24.759><c> to</c>

00:01:24.950 --> 00:01:24.960 align:start position:0%
product click on the continue to
 

00:01:24.960 --> 00:01:27.030 align:start position:0%
product click on the continue to
configuration

00:01:27.030 --> 00:01:27.040 align:start position:0%
configuration
 

00:01:27.040 --> 00:01:30.350 align:start position:0%
configuration
button<00:01:28.040><c> to</c><00:01:28.240><c> launch</c><00:01:28.600><c> an</c><00:01:28.799><c> endpoint</c><00:01:29.439><c> say</c><00:01:30.000><c> maker</c>

00:01:30.350 --> 00:01:30.360 align:start position:0%
button to launch an endpoint say maker
 

00:01:30.360 --> 00:01:32.389 align:start position:0%
button to launch an endpoint say maker
offers<00:01:31.079><c> either</c><00:01:31.360><c> cloud</c><00:01:31.640><c> formation</c><00:01:32.200><c> or</c>

00:01:32.389 --> 00:01:32.399 align:start position:0%
offers either cloud formation or
 

00:01:32.399 --> 00:01:35.789 align:start position:0%
offers either cloud formation or
salemaker<00:01:33.000><c> console</c><00:01:33.479><c> or</c><00:01:33.640><c> using</c><00:01:33.960><c> AWS</c><00:01:34.520><c> CLI</c><00:01:35.159><c> to</c>

00:01:35.789 --> 00:01:35.799 align:start position:0%
salemaker console or using AWS CLI to
 

00:01:35.799 --> 00:01:40.550 align:start position:0%
salemaker console or using AWS CLI to
deploy<00:01:36.200><c> your</c>

00:01:40.550 --> 00:01:40.560 align:start position:0%
 
 

00:01:40.560 --> 00:01:43.590 align:start position:0%
 
endpoints<00:01:41.560><c> we</c><00:01:41.720><c> will</c><00:01:41.920><c> be</c><00:01:42.079><c> using</c><00:01:42.439><c> the</c><00:01:42.600><c> Saker</c>

00:01:43.590 --> 00:01:43.600 align:start position:0%
endpoints we will be using the Saker
 

00:01:43.600 --> 00:01:48.389 align:start position:0%
endpoints we will be using the Saker
console<00:01:44.600><c> to</c><00:01:44.840><c> create</c><00:01:45.159><c> a</c><00:01:45.360><c> realtime</c><00:01:45.960><c> inference</c>

00:01:48.389 --> 00:01:48.399 align:start position:0%
console to create a realtime inference
 

00:01:48.399 --> 00:01:50.830 align:start position:0%
console to create a realtime inference
endpoint<00:01:49.399><c> click</c><00:01:49.680><c> on</c><00:01:49.920><c> The</c><00:01:50.079><c> View</c><00:01:50.439><c> Amazon</c>

00:01:50.830 --> 00:01:50.840 align:start position:0%
endpoint click on The View Amazon
 

00:01:50.840 --> 00:01:53.670 align:start position:0%
endpoint click on The View Amazon
salemaker<00:01:51.479><c> button</c><00:01:51.799><c> to</c><00:01:52.000><c> start</c><00:01:52.280><c> creating</c><00:01:52.680><c> your</c>

00:01:53.670 --> 00:01:53.680 align:start position:0%
salemaker button to start creating your
 

00:01:53.680 --> 00:01:55.950 align:start position:0%
salemaker button to start creating your
endpoint<00:01:54.680><c> next</c><00:01:54.960><c> you</c><00:01:55.079><c> will</c><00:01:55.280><c> need</c><00:01:55.439><c> to</c><00:01:55.640><c> follow</c>

00:01:55.950 --> 00:01:55.960 align:start position:0%
endpoint next you will need to follow
 

00:01:55.960 --> 00:01:56.950 align:start position:0%
endpoint next you will need to follow
some

00:01:56.950 --> 00:01:56.960 align:start position:0%
some
 

00:01:56.960 --> 00:01:59.870 align:start position:0%
some
steps<00:01:57.960><c> the</c><00:01:58.200><c> first</c><00:01:58.520><c> step</c><00:01:58.840><c> is</c><00:01:58.960><c> to</c><00:01:59.200><c> create</c><00:01:59.479><c> a</c>

00:01:59.870 --> 00:01:59.880 align:start position:0%
steps the first step is to create a
 

00:01:59.880 --> 00:02:02.029 align:start position:0%
steps the first step is to create a
Deployable<00:02:00.439><c> model</c><00:02:00.840><c> from</c><00:02:01.079><c> the</c><00:02:01.200><c> model</c><00:02:01.600><c> package</c>

00:02:02.029 --> 00:02:02.039 align:start position:0%
Deployable model from the model package
 

00:02:02.039 --> 00:02:05.550 align:start position:0%
Deployable model from the model package
that<00:02:02.240><c> we</c><00:02:02.439><c> just</c><00:02:02.680><c> subscribed</c><00:02:03.719><c> to</c><00:02:04.719><c> to</c><00:02:04.960><c> do</c><00:02:05.280><c> this</c>

00:02:05.550 --> 00:02:05.560 align:start position:0%
that we just subscribed to to do this
 

00:02:05.560 --> 00:02:09.229 align:start position:0%
that we just subscribed to to do this
just<00:02:05.799><c> provide</c><00:02:06.159><c> a</c><00:02:06.320><c> model</c><00:02:06.719><c> name</c><00:02:07.719><c> and</c><00:02:08.039><c> use</c><00:02:08.599><c> an</c><00:02:08.879><c> IM</c>

00:02:09.229 --> 00:02:09.239 align:start position:0%
just provide a model name and use an IM
 

00:02:09.239 --> 00:02:11.470 align:start position:0%
just provide a model name and use an IM
role<00:02:09.560><c> that</c><00:02:09.759><c> has</c><00:02:10.080><c> Amazon</c><00:02:10.560><c> salemaker</c><00:02:11.160><c> full</c>

00:02:11.470 --> 00:02:11.480 align:start position:0%
role that has Amazon salemaker full
 

00:02:11.480 --> 00:02:14.150 align:start position:0%
role that has Amazon salemaker full
access

00:02:14.150 --> 00:02:14.160 align:start position:0%
 
 

00:02:14.160 --> 00:02:16.830 align:start position:0%
 
policy<00:02:15.160><c> now</c><00:02:15.480><c> click</c><00:02:15.680><c> on</c><00:02:15.959><c> the</c><00:02:16.120><c> next</c><00:02:16.360><c> button</c><00:02:16.680><c> to</c>

00:02:16.830 --> 00:02:16.840 align:start position:0%
policy now click on the next button to
 

00:02:16.840 --> 00:02:18.710 align:start position:0%
policy now click on the next button to
start<00:02:17.200><c> the</c><00:02:17.360><c> second</c>

00:02:18.710 --> 00:02:18.720 align:start position:0%
start the second
 

00:02:18.720 --> 00:02:21.670 align:start position:0%
start the second
step<00:02:19.720><c> the</c><00:02:19.879><c> second</c><00:02:20.280><c> step</c><00:02:20.560><c> is</c><00:02:20.680><c> to</c><00:02:20.920><c> create</c><00:02:21.319><c> an</c>

00:02:21.670 --> 00:02:21.680 align:start position:0%
step the second step is to create an
 

00:02:21.680 --> 00:02:28.309 align:start position:0%
step the second step is to create an
private<00:02:22.080><c> API</c><00:02:22.519><c> inpoint</c><00:02:23.200><c> from</c><00:02:23.440><c> our</c>

00:02:28.309 --> 00:02:28.319 align:start position:0%
 
 

00:02:28.319 --> 00:02:30.750 align:start position:0%
 
model<00:02:29.319><c> here</c><00:02:29.519><c> you</c><00:02:29.599><c> will</c><00:02:29.959><c> need</c><00:02:30.080><c> to</c><00:02:30.239><c> provide</c><00:02:30.560><c> the</c>

00:02:30.750 --> 00:02:30.760 align:start position:0%
model here you will need to provide the
 

00:02:30.760 --> 00:02:31.990 align:start position:0%
model here you will need to provide the
name<00:02:31.000><c> for</c><00:02:31.239><c> the</c>

00:02:31.990 --> 00:02:32.000 align:start position:0%
name for the
 

00:02:32.000 --> 00:02:34.589 align:start position:0%
name for the
endpoint<00:02:33.000><c> and</c><00:02:33.200><c> also</c><00:02:33.480><c> create</c><00:02:33.760><c> a</c><00:02:33.959><c> configuration</c>

00:02:34.589 --> 00:02:34.599 align:start position:0%
endpoint and also create a configuration
 

00:02:34.599 --> 00:02:39.869 align:start position:0%
endpoint and also create a configuration
for<00:02:34.840><c> the</c>

00:02:39.869 --> 00:02:39.879 align:start position:0%
 
 

00:02:39.879 --> 00:02:42.589 align:start position:0%
 
endpoint<00:02:40.879><c> you</c><00:02:41.000><c> can</c><00:02:41.280><c> configure</c><00:02:41.800><c> the</c><00:02:42.000><c> instance</c>

00:02:42.589 --> 00:02:42.599 align:start position:0%
endpoint you can configure the instance
 

00:02:42.599 --> 00:02:44.910 align:start position:0%
endpoint you can configure the instance
type<00:02:42.959><c> that</c><00:02:43.120><c> you</c><00:02:43.239><c> want</c><00:02:43.400><c> to</c><00:02:43.640><c> use</c><00:02:44.280><c> as</c><00:02:44.440><c> well</c><00:02:44.599><c> as</c><00:02:44.760><c> the</c>

00:02:44.910 --> 00:02:44.920 align:start position:0%
type that you want to use as well as the
 

00:02:44.920 --> 00:02:46.790 align:start position:0%
type that you want to use as well as the
number<00:02:45.280><c> of</c>

00:02:46.790 --> 00:02:46.800 align:start position:0%
number of
 

00:02:46.800 --> 00:02:49.030 align:start position:0%
number of
instances<00:02:47.800><c> once</c><00:02:48.040><c> you</c><00:02:48.159><c> are</c><00:02:48.440><c> happy</c><00:02:48.720><c> with</c><00:02:48.879><c> your</c>

00:02:49.030 --> 00:02:49.040 align:start position:0%
instances once you are happy with your
 

00:02:49.040 --> 00:02:51.309 align:start position:0%
instances once you are happy with your
endpoint<00:02:49.680><c> configuration</c><00:02:50.680><c> click</c><00:02:50.920><c> on</c><00:02:51.120><c> the</c>

00:02:51.309 --> 00:02:51.319 align:start position:0%
endpoint configuration click on the
 

00:02:51.319 --> 00:02:53.229 align:start position:0%
endpoint configuration click on the
create<00:02:51.640><c> endpoint</c><00:02:52.120><c> configuration</c><00:02:52.760><c> button</c><00:02:53.040><c> to</c>

00:02:53.229 --> 00:02:53.239 align:start position:0%
create endpoint configuration button to
 

00:02:53.239 --> 00:02:54.670 align:start position:0%
create endpoint configuration button to
save

00:02:54.670 --> 00:02:54.680 align:start position:0%
save
 

00:02:54.680 --> 00:02:58.070 align:start position:0%
save
it<00:02:55.680><c> now</c><00:02:55.959><c> click</c><00:02:56.159><c> on</c><00:02:56.360><c> the</c><00:02:56.560><c> submit</c><00:02:57.000><c> button</c><00:02:57.879><c> this</c>

00:02:58.070 --> 00:02:58.080 align:start position:0%
it now click on the submit button this
 

00:02:58.080 --> 00:03:00.910 align:start position:0%
it now click on the submit button this
creates<00:02:58.480><c> the</c><00:02:58.640><c> endpoint</c><00:03:00.040><c> and</c><00:03:00.200><c> it</c><00:03:00.400><c> takes</c><00:03:00.720><c> few</c>

00:03:00.910 --> 00:03:00.920 align:start position:0%
creates the endpoint and it takes few
 

00:03:00.920 --> 00:03:11.190 align:start position:0%
creates the endpoint and it takes few
minutes<00:03:01.239><c> for</c><00:03:01.400><c> the</c><00:03:01.560><c> service</c><00:03:01.920><c> to</c>

00:03:11.190 --> 00:03:11.200 align:start position:0%
 
 

00:03:11.200 --> 00:03:14.589 align:start position:0%
 
start<00:03:12.200><c> once</c><00:03:12.480><c> the</c><00:03:12.640><c> endpoint</c><00:03:13.239><c> is</c><00:03:13.519><c> active</c><00:03:14.440><c> you</c>

00:03:14.589 --> 00:03:14.599 align:start position:0%
start once the endpoint is active you
 

00:03:14.599 --> 00:03:17.750 align:start position:0%
start once the endpoint is active you
can<00:03:14.799><c> make</c><00:03:15.120><c> requests</c><00:03:15.599><c> to</c><00:03:15.959><c> it</c><00:03:16.560><c> either</c><00:03:16.879><c> using</c><00:03:17.200><c> AWS</c>

00:03:17.750 --> 00:03:17.760 align:start position:0%
can make requests to it either using AWS
 

00:03:17.760 --> 00:03:24.270 align:start position:0%
can make requests to it either using AWS
CLI<00:03:18.519><c> or</c><00:03:18.680><c> using</c><00:03:19.000><c> AWS</c>

00:03:24.270 --> 00:03:24.280 align:start position:0%
 
 

00:03:24.280 --> 00:03:27.670 align:start position:0%
 
SDK<00:03:25.280><c> here</c><00:03:26.080><c> I</c><00:03:26.159><c> am</c><00:03:26.400><c> going</c><00:03:26.640><c> to</c><00:03:26.920><c> use</c><00:03:27.200><c> a</c><00:03:27.400><c> Google</c>

00:03:27.670 --> 00:03:27.680 align:start position:0%
SDK here I am going to use a Google
 

00:03:27.680 --> 00:03:31.550 align:start position:0%
SDK here I am going to use a Google
collab<00:03:28.360><c> notebook</c><00:03:29.360><c> and</c><00:03:29.799><c> install</c><00:03:30.439><c> the</c><00:03:30.560><c> boto3</c>

00:03:31.550 --> 00:03:31.560 align:start position:0%
collab notebook and install the boto3
 

00:03:31.560 --> 00:03:35.070 align:start position:0%
collab notebook and install the boto3
package<00:03:32.400><c> which</c><00:03:32.560><c> is</c><00:03:32.799><c> the</c><00:03:33.080><c> python</c>

00:03:35.070 --> 00:03:35.080 align:start position:0%
package which is the python
 

00:03:35.080 --> 00:03:38.589 align:start position:0%
package which is the python
awsp<00:03:36.080><c> next</c><00:03:36.519><c> I</c><00:03:36.599><c> am</c><00:03:36.840><c> creating</c><00:03:37.760><c> a</c><00:03:38.000><c> salemaker</c>

00:03:38.589 --> 00:03:38.599 align:start position:0%
awsp next I am creating a salemaker
 

00:03:38.599 --> 00:03:41.869 align:start position:0%
awsp next I am creating a salemaker
runtime<00:03:39.080><c> client</c><00:03:39.760><c> using</c><00:03:40.120><c> my</c><00:03:40.360><c> temporary</c><00:03:40.840><c> AWS</c>

00:03:41.869 --> 00:03:41.879 align:start position:0%
runtime client using my temporary AWS
 

00:03:41.879 --> 00:03:44.309 align:start position:0%
runtime client using my temporary AWS
credentials<00:03:42.879><c> to</c><00:03:43.120><c> make</c><00:03:43.360><c> request</c><00:03:43.799><c> to</c><00:03:43.959><c> the</c>

00:03:44.309 --> 00:03:44.319 align:start position:0%
credentials to make request to the
 

00:03:44.319 --> 00:03:46.710 align:start position:0%
credentials to make request to the
endpoint<00:03:45.319><c> we</c><00:03:45.480><c> need</c><00:03:45.680><c> to</c><00:03:45.879><c> call</c><00:03:46.120><c> the</c><00:03:46.239><c> invoke</c>

00:03:46.710 --> 00:03:46.720 align:start position:0%
endpoint we need to call the invoke
 

00:03:46.720 --> 00:03:50.309 align:start position:0%
endpoint we need to call the invoke
endpoint<00:03:47.280><c> method</c><00:03:47.840><c> on</c><00:03:48.120><c> the</c><00:03:48.360><c> salemaker</c><00:03:48.959><c> runtime</c>

00:03:50.309 --> 00:03:50.319 align:start position:0%
endpoint method on the salemaker runtime
 

00:03:50.319 --> 00:03:53.229 align:start position:0%
endpoint method on the salemaker runtime
client<00:03:51.319><c> this</c><00:03:51.519><c> endpoint</c><00:03:52.120><c> takes</c><00:03:52.519><c> endpoint</c><00:03:53.000><c> name</c>

00:03:53.229 --> 00:03:53.239 align:start position:0%
client this endpoint takes endpoint name
 

00:03:53.239 --> 00:03:56.190 align:start position:0%
client this endpoint takes endpoint name
as<00:03:53.400><c> the</c><00:03:53.519><c> first</c><00:03:54.280><c> parameter</c><00:03:55.280><c> followed</c><00:03:55.720><c> by</c><00:03:55.959><c> the</c>

00:03:56.190 --> 00:03:56.200 align:start position:0%
as the first parameter followed by the
 

00:03:56.200 --> 00:03:59.270 align:start position:0%
as the first parameter followed by the
input<00:03:56.840><c> that</c><00:03:57.040><c> you</c><00:03:57.239><c> send</c><00:03:58.239><c> and</c><00:03:58.439><c> the</c><00:03:58.799><c> required</c>

00:03:59.270 --> 00:03:59.280 align:start position:0%
input that you send and the required
 

00:03:59.280 --> 00:04:02.149 align:start position:0%
input that you send and the required
content<00:03:59.799><c> type</c><00:04:00.079><c> and</c><00:04:00.480><c> M</c><00:04:00.799><c> types</c><00:04:01.560><c> we</c><00:04:01.640><c> will</c><00:04:01.840><c> use</c><00:04:02.040><c> the</c>

00:04:02.149 --> 00:04:02.159 align:start position:0%
content type and M types we will use the
 

00:04:02.159 --> 00:04:04.309 align:start position:0%
content type and M types we will use the
endpoint<00:04:02.599><c> name</c><00:04:02.879><c> that</c><00:04:03.040><c> we</c><00:04:03.239><c> just</c>

00:04:04.309 --> 00:04:04.319 align:start position:0%
endpoint name that we just
 

00:04:04.319 --> 00:04:06.710 align:start position:0%
endpoint name that we just
created<00:04:05.319><c> let's</c><00:04:05.640><c> look</c><00:04:05.799><c> at</c><00:04:06.079><c> the</c><00:04:06.360><c> question</c>

00:04:06.710 --> 00:04:06.720 align:start position:0%
created let's look at the question
 

00:04:06.720 --> 00:04:09.750 align:start position:0%
created let's look at the question
answering<00:04:07.239><c> capability</c><00:04:07.799><c> of</c><00:04:08.079><c> this</c><00:04:08.319><c> medical</c><00:04:08.680><c> llm</c>

00:04:09.750 --> 00:04:09.760 align:start position:0%
answering capability of this medical llm
 

00:04:09.760 --> 00:04:12.630 align:start position:0%
answering capability of this medical llm
endpoint<00:04:10.760><c> as</c><00:04:10.879><c> you</c><00:04:11.000><c> can</c><00:04:11.200><c> see</c><00:04:11.680><c> I</c><00:04:11.799><c> have</c><00:04:12.000><c> prepared</c>

00:04:12.630 --> 00:04:12.640 align:start position:0%
endpoint as you can see I have prepared
 

00:04:12.640 --> 00:04:16.069 align:start position:0%
endpoint as you can see I have prepared
a<00:04:13.439><c> Json</c><00:04:14.000><c> data</c><00:04:15.000><c> where</c><00:04:15.200><c> we</c><00:04:15.360><c> use</c><00:04:15.560><c> our</c><00:04:15.840><c> buil-in</c>

00:04:16.069 --> 00:04:16.079 align:start position:0%
a Json data where we use our buil-in
 

00:04:16.079 --> 00:04:18.789 align:start position:0%
a Json data where we use our buil-in
prom<00:04:16.639><c> template</c><00:04:17.040><c> the</c><00:04:17.199><c> open</c><00:04:17.440><c> book</c><00:04:17.680><c> QA</c><00:04:18.359><c> I</c><00:04:18.479><c> also</c>

00:04:18.789 --> 00:04:18.799 align:start position:0%
prom template the open book QA I also
 

00:04:18.799 --> 00:04:20.990 align:start position:0%
prom template the open book QA I also
provide<00:04:19.120><c> a</c><00:04:19.320><c> medical</c><00:04:19.799><c> context</c><00:04:20.479><c> about</c><00:04:20.799><c> the</c>

00:04:20.990 --> 00:04:21.000 align:start position:0%
provide a medical context about the
 

00:04:21.000 --> 00:04:23.230 align:start position:0%
provide a medical context about the
patient<00:04:21.799><c> and</c><00:04:21.959><c> I'm</c><00:04:22.160><c> asking</c><00:04:22.440><c> a</c><00:04:22.639><c> question</c><00:04:23.040><c> what</c>

00:04:23.230 --> 00:04:23.240 align:start position:0%
patient and I'm asking a question what
 

00:04:23.240 --> 00:04:25.230 align:start position:0%
patient and I'm asking a question what
symptoms<00:04:23.800><c> did</c><00:04:24.120><c> the</c><00:04:24.360><c> patient</c><00:04:24.720><c> experience</c>

00:04:25.230 --> 00:04:25.240 align:start position:0%
symptoms did the patient experience
 

00:04:25.240 --> 00:04:27.110 align:start position:0%
symptoms did the patient experience
after<00:04:25.520><c> the</c>

00:04:27.110 --> 00:04:27.120 align:start position:0%
after the
 

00:04:27.120 --> 00:04:29.990 align:start position:0%
after the
injury<00:04:28.120><c> now</c><00:04:28.440><c> I</c><00:04:28.680><c> call</c><00:04:29.000><c> the</c><00:04:29.120><c> endpoint</c><00:04:29.560><c> to</c><00:04:29.720><c> using</c>

00:04:29.990 --> 00:04:30.000 align:start position:0%
injury now I call the endpoint to using
 

00:04:30.000 --> 00:04:31.909 align:start position:0%
injury now I call the endpoint to using
the<00:04:30.160><c> query</c><00:04:30.440><c> endpoint</c><00:04:30.840><c> function</c><00:04:31.479><c> which</c>

00:04:31.909 --> 00:04:31.919 align:start position:0%
the query endpoint function which
 

00:04:31.919 --> 00:04:34.629 align:start position:0%
the query endpoint function which
internally<00:04:32.400><c> calls</c><00:04:32.759><c> the</c><00:04:32.880><c> invoke</c>

00:04:34.629 --> 00:04:34.639 align:start position:0%
internally calls the invoke
 

00:04:34.639 --> 00:04:37.350 align:start position:0%
internally calls the invoke
endpoint<00:04:35.639><c> as</c><00:04:35.759><c> you</c><00:04:35.919><c> can</c><00:04:36.120><c> see</c><00:04:36.600><c> the</c><00:04:36.800><c> response</c>

00:04:37.350 --> 00:04:37.360 align:start position:0%
endpoint as you can see the response
 

00:04:37.360 --> 00:04:39.629 align:start position:0%
endpoint as you can see the response
returned<00:04:37.560><c> by</c><00:04:37.800><c> the</c><00:04:37.960><c> end</c><00:04:38.280><c> point</c><00:04:38.520><c> is</c><00:04:38.800><c> pretty</c>

00:04:39.629 --> 00:04:39.639 align:start position:0%
returned by the end point is pretty
 

00:04:39.639 --> 00:04:42.590 align:start position:0%
returned by the end point is pretty
concise<00:04:40.639><c> so</c><00:04:41.080><c> its</c><00:04:41.320><c> response</c><00:04:41.800><c> is</c><00:04:42.000><c> based</c><00:04:42.280><c> on</c><00:04:42.440><c> the</c>

00:04:42.590 --> 00:04:42.600 align:start position:0%
concise so its response is based on the
 

00:04:42.600 --> 00:04:44.469 align:start position:0%
concise so its response is based on the
provided<00:04:43.080><c> context</c><00:04:43.560><c> the</c><00:04:43.720><c> patient</c><00:04:44.039><c> experienced</c>

00:04:44.469 --> 00:04:44.479 align:start position:0%
provided context the patient experienced
 

00:04:44.479 --> 00:04:46.590 align:start position:0%
provided context the patient experienced
SARP<00:04:44.840><c> pain</c><00:04:45.080><c> his</c><00:04:45.240><c> lower</c><00:04:45.680><c> back</c><00:04:45.880><c> soon</c><00:04:46.160><c> after</c>

00:04:46.590 --> 00:04:46.600 align:start position:0%
SARP pain his lower back soon after
 

00:04:46.600 --> 00:04:49.350 align:start position:0%
SARP pain his lower back soon after
injury<00:04:47.600><c> which</c><00:04:47.840><c> then</c><00:04:48.000><c> radiated</c><00:04:48.639><c> down</c><00:04:48.840><c> to</c><00:04:49.120><c> his</c>

00:04:49.350 --> 00:04:49.360 align:start position:0%
injury which then radiated down to his
 

00:04:49.360 --> 00:04:51.070 align:start position:0%
injury which then radiated down to his
right<00:04:49.600><c> buttock</c><00:04:50.120><c> all</c><00:04:50.320><c> the</c><00:04:50.440><c> way</c><00:04:50.639><c> down</c><00:04:50.800><c> to</c><00:04:50.919><c> the</c>

00:04:51.070 --> 00:04:51.080 align:start position:0%
right buttock all the way down to the
 

00:04:51.080 --> 00:04:53.430 align:start position:0%
right buttock all the way down to the
lateral<00:04:51.440><c> part</c><00:04:51.680><c> of</c><00:04:51.800><c> his</c><00:04:51.960><c> leg</c><00:04:52.199><c> Crossing</c><00:04:52.560><c> his</c>

00:04:53.430 --> 00:04:53.440 align:start position:0%
lateral part of his leg Crossing his
 

00:04:53.440 --> 00:04:56.270 align:start position:0%
lateral part of his leg Crossing his
knee<00:04:54.440><c> so</c><00:04:54.680><c> the</c><00:04:54.800><c> endpoint</c><00:04:55.479><c> has</c><00:04:55.759><c> taken</c><00:04:56.120><c> the</c>

00:04:56.270 --> 00:04:56.280 align:start position:0%
knee so the endpoint has taken the
 

00:04:56.280 --> 00:04:58.790 align:start position:0%
knee so the endpoint has taken the
medical<00:04:56.759><c> context</c><00:04:57.720><c> as</c><00:04:57.840><c> a</c><00:04:58.000><c> reference</c><00:04:58.400><c> point</c><00:04:58.600><c> to</c>

00:04:58.790 --> 00:04:58.800 align:start position:0%
medical context as a reference point to
 

00:04:58.800 --> 00:05:06.029 align:start position:0%
medical context as a reference point to
answer<00:04:59.080><c> the</c><00:04:59.280><c> question</c><00:04:59.960><c> uh</c><00:05:00.120><c> which</c><00:05:00.240><c> is</c><00:05:00.440><c> what</c><00:05:00.600><c> we</c>

00:05:06.029 --> 00:05:06.039 align:start position:0%
 
 

00:05:06.039 --> 00:05:09.189 align:start position:0%
 
want<00:05:07.039><c> we</c><00:05:07.199><c> can</c><00:05:07.400><c> also</c><00:05:07.720><c> send</c><00:05:07.960><c> a</c><00:05:08.120><c> custom</c><00:05:08.520><c> prompt</c><00:05:08.960><c> as</c>

00:05:09.189 --> 00:05:09.199 align:start position:0%
want we can also send a custom prompt as
 

00:05:09.199 --> 00:05:10.909 align:start position:0%
want we can also send a custom prompt as
part<00:05:09.360><c> of</c><00:05:09.520><c> the</c><00:05:09.680><c> input</c>

00:05:10.909 --> 00:05:10.919 align:start position:0%
part of the input
 

00:05:10.919 --> 00:05:14.070 align:start position:0%
part of the input
text<00:05:11.919><c> as</c><00:05:12.039><c> you</c><00:05:12.199><c> see</c><00:05:12.479><c> in</c><00:05:12.680><c> this</c><00:05:12.960><c> example</c><00:05:13.680><c> I</c><00:05:13.800><c> have</c>

00:05:14.070 --> 00:05:14.080 align:start position:0%
text as you see in this example I have
 

00:05:14.080 --> 00:05:16.950 align:start position:0%
text as you see in this example I have
instructed<00:05:14.680><c> the</c><00:05:14.800><c> llm</c><00:05:15.280><c> endpoint</c><00:05:16.280><c> to</c><00:05:16.560><c> answer</c>

00:05:16.950 --> 00:05:16.960 align:start position:0%
instructed the llm endpoint to answer
 

00:05:16.960 --> 00:05:19.029 align:start position:0%
instructed the llm endpoint to answer
the<00:05:17.120><c> following</c><00:05:17.560><c> questions</c><00:05:17.960><c> so</c><00:05:18.199><c> a</c><00:05:18.400><c> 5-year-old</c>

00:05:19.029 --> 00:05:19.039 align:start position:0%
the following questions so a 5-year-old
 

00:05:19.039 --> 00:05:21.550 align:start position:0%
the following questions so a 5-year-old
child<00:05:19.360><c> can</c><00:05:19.560><c> also</c>

00:05:21.550 --> 00:05:21.560 align:start position:0%
child can also
 

00:05:21.560 --> 00:05:23.550 align:start position:0%
child can also
understand<00:05:22.560><c> and</c><00:05:22.720><c> the</c><00:05:22.880><c> response</c><00:05:23.280><c> we</c><00:05:23.400><c> are</c>

00:05:23.550 --> 00:05:23.560 align:start position:0%
understand and the response we are
 

00:05:23.560 --> 00:05:26.749 align:start position:0%
understand and the response we are
getting<00:05:24.120><c> is</c><00:05:24.800><c> following</c><00:05:25.319><c> the</c><00:05:25.560><c> prompt</c><00:05:26.479><c> as</c><00:05:26.600><c> you</c>

00:05:26.749 --> 00:05:26.759 align:start position:0%
getting is following the prompt as you
 

00:05:26.759 --> 00:05:30.070 align:start position:0%
getting is following the prompt as you
see<00:05:27.360><c> from</c><00:05:27.560><c> the</c><00:05:27.720><c> response</c><00:05:28.440><c> uh</c><00:05:28.600><c> it's</c><00:05:28.840><c> saying</c><00:05:29.840><c> so</c>

00:05:30.070 --> 00:05:30.080 align:start position:0%
see from the response uh it's saying so
 

00:05:30.080 --> 00:05:32.029 align:start position:0%
see from the response uh it's saying so
you<00:05:30.240><c> know</c><00:05:30.520><c> how</c><00:05:30.919><c> sometimes</c><00:05:31.160><c> we</c><00:05:31.319><c> get</c><00:05:31.520><c> really</c><00:05:31.800><c> mad</c>

00:05:32.029 --> 00:05:32.039 align:start position:0%
you know how sometimes we get really mad
 

00:05:32.039 --> 00:05:35.150 align:start position:0%
you know how sometimes we get really mad
or<00:05:32.280><c> excited</c><00:05:32.720><c> or</c><00:05:32.919><c> our</c><00:05:33.240><c> hearts</c><00:05:33.560><c> beats</c><00:05:33.919><c> fast</c><00:05:34.880><c> well</c>

00:05:35.150 --> 00:05:35.160 align:start position:0%
or excited or our hearts beats fast well
 

00:05:35.160 --> 00:05:36.670 align:start position:0%
or excited or our hearts beats fast well
high<00:05:35.319><c> blood</c><00:05:35.600><c> pressure</c><00:05:35.919><c> is</c><00:05:36.080><c> when</c><00:05:36.240><c> your</c><00:05:36.400><c> heart</c>

00:05:36.670 --> 00:05:36.680 align:start position:0%
high blood pressure is when your heart
 

00:05:36.680 --> 00:05:39.390 align:start position:0%
high blood pressure is when your heart
beats<00:05:37.039><c> too</c><00:05:37.280><c> fast</c><00:05:37.560><c> all</c><00:05:37.800><c> the</c><00:05:38.000><c> time</c><00:05:38.919><c> it's</c><00:05:39.160><c> like</c>

00:05:39.390 --> 00:05:39.400 align:start position:0%
beats too fast all the time it's like
 

00:05:39.400 --> 00:05:40.990 align:start position:0%
beats too fast all the time it's like
having<00:05:39.680><c> a</c><00:05:39.840><c> super</c><00:05:40.199><c> fast</c><00:05:40.400><c> heartbeat</c><00:05:40.880><c> that</c>

00:05:40.990 --> 00:05:41.000 align:start position:0%
having a super fast heartbeat that
 

00:05:41.000 --> 00:05:43.790 align:start position:0%
having a super fast heartbeat that
doesn't<00:05:41.720><c> stop</c><00:05:42.720><c> and</c><00:05:42.919><c> this</c><00:05:43.080><c> lady</c><00:05:43.440><c> has</c><00:05:43.600><c> been</c>

00:05:43.790 --> 00:05:43.800 align:start position:0%
doesn't stop and this lady has been
 

00:05:43.800 --> 00:05:46.350 align:start position:0%
doesn't stop and this lady has been
having<00:05:44.120><c> for</c><00:05:44.280><c> 15</c><00:05:44.600><c> whole</c><00:05:44.840><c> years</c><00:05:45.680><c> that's</c><00:05:45.800><c> a</c><00:05:46.000><c> long</c>

00:05:46.350 --> 00:05:46.360 align:start position:0%
having for 15 whole years that's a long
 

00:05:46.360 --> 00:05:48.710 align:start position:0%
having for 15 whole years that's a long
of<00:05:46.639><c> time</c><00:05:47.199><c> C</c><00:05:47.520><c> this</c><00:05:47.680><c> helps</c><00:05:47.919><c> to</c><00:05:48.080><c> make</c><00:05:48.240><c> sure</c><00:05:48.479><c> her</c>

00:05:48.710 --> 00:05:48.720 align:start position:0%
of time C this helps to make sure her
 

00:05:48.720 --> 00:05:51.150 align:start position:0%
of time C this helps to make sure her
heart<00:05:48.960><c> isn't</c><00:05:49.520><c> working</c><00:05:49.919><c> too</c>

00:05:51.150 --> 00:05:51.160 align:start position:0%
heart isn't working too
 

00:05:51.160 --> 00:05:54.430 align:start position:0%
heart isn't working too
hard<00:05:52.160><c> yeah</c><00:05:52.440><c> so</c><00:05:53.440><c> uh</c><00:05:53.560><c> it's</c><00:05:53.800><c> answering</c><00:05:54.199><c> the</c>

00:05:54.430 --> 00:05:54.440 align:start position:0%
hard yeah so uh it's answering the
 

00:05:54.440 --> 00:05:57.469 align:start position:0%
hard yeah so uh it's answering the
question<00:05:55.199><c> uh</c><00:05:55.400><c> in</c><00:05:55.520><c> a</c><00:05:55.759><c> simplistic</c><00:05:56.400><c> way</c><00:05:57.000><c> like</c><00:05:57.319><c> I</c>

00:05:57.469 --> 00:05:57.479 align:start position:0%
question uh in a simplistic way like I
 

00:05:57.479 --> 00:06:02.830 align:start position:0%
question uh in a simplistic way like I
asked<00:05:57.800><c> so</c><00:05:58.039><c> that</c><00:05:58.160><c> a</c><00:05:58.360><c> fiveyear</c><00:05:59.240><c> old</c><00:05:59.639><c> child</c><00:05:59.960><c> can</c>

00:06:02.830 --> 00:06:02.840 align:start position:0%
 
 

00:06:02.840 --> 00:06:05.350 align:start position:0%
 
understand<00:06:03.840><c> next</c><00:06:04.520><c> let's</c><00:06:04.840><c> look</c><00:06:05.000><c> at</c><00:06:05.199><c> the</c>

00:06:05.350 --> 00:06:05.360 align:start position:0%
understand next let's look at the
 

00:06:05.360 --> 00:06:09.950 align:start position:0%
understand next let's look at the
summarizing<00:06:06.000><c> capabilities</c><00:06:06.599><c> of</c><00:06:06.759><c> the</c><00:06:06.880><c> llm</c>

00:06:09.950 --> 00:06:09.960 align:start position:0%
 
 

00:06:09.960 --> 00:06:12.390 align:start position:0%
 
endpoint<00:06:10.960><c> there</c><00:06:11.120><c> is</c><00:06:11.240><c> a</c><00:06:11.440><c> predefined</c><00:06:12.000><c> template</c>

00:06:12.390 --> 00:06:12.400 align:start position:0%
endpoint there is a predefined template
 

00:06:12.400 --> 00:06:14.990 align:start position:0%
endpoint there is a predefined template
called<00:06:12.680><c> summarization</c><00:06:13.520><c> that</c><00:06:13.680><c> you</c><00:06:13.840><c> can</c><00:06:14.400><c> send</c>

00:06:14.990 --> 00:06:15.000 align:start position:0%
called summarization that you can send
 

00:06:15.000 --> 00:06:17.390 align:start position:0%
called summarization that you can send
as<00:06:15.120><c> a</c><00:06:15.319><c> parameter</c><00:06:15.800><c> to</c><00:06:16.000><c> the</c><00:06:16.160><c> llm</c>

00:06:17.390 --> 00:06:17.400 align:start position:0%
as a parameter to the llm
 

00:06:17.400 --> 00:06:22.950 align:start position:0%
as a parameter to the llm
endpoint<00:06:18.400><c> to</c><00:06:18.599><c> summarize</c><00:06:19.120><c> your</c><00:06:19.319><c> clinical</c>

00:06:22.950 --> 00:06:22.960 align:start position:0%
 
 

00:06:22.960 --> 00:06:25.870 align:start position:0%
 
documents<00:06:23.960><c> next</c><00:06:24.280><c> I</c><00:06:24.360><c> want</c><00:06:24.560><c> to</c><00:06:24.919><c> summarize</c><00:06:25.560><c> this</c>

00:06:25.870 --> 00:06:25.880 align:start position:0%
documents next I want to summarize this
 

00:06:25.880 --> 00:06:29.029 align:start position:0%
documents next I want to summarize this
medical<00:06:26.880><c> article</c><00:06:27.360><c> on</c><00:06:27.680><c> covid</c><00:06:28.120><c> and</c><00:06:28.319><c> its</c><00:06:28.599><c> effect</c>

00:06:29.029 --> 00:06:29.039 align:start position:0%
medical article on covid and its effect
 

00:06:29.039 --> 00:06:32.870 align:start position:0%
medical article on covid and its effect
on<00:06:29.599><c> older</c><00:06:30.440><c> adults</c><00:06:30.880><c> on</c><00:06:31.280><c> Brazil</c><00:06:32.280><c> so</c><00:06:32.520><c> I'm</c><00:06:32.720><c> going</c>

00:06:32.870 --> 00:06:32.880 align:start position:0%
on older adults on Brazil so I'm going
 

00:06:32.880 --> 00:06:35.790 align:start position:0%
on older adults on Brazil so I'm going
to<00:06:33.039><c> send</c><00:06:33.400><c> this</c><00:06:33.639><c> entire</c><00:06:34.280><c> text</c><00:06:34.599><c> to</c><00:06:34.800><c> the</c><00:06:34.919><c> endpoint</c>

00:06:35.790 --> 00:06:35.800 align:start position:0%
to send this entire text to the endpoint
 

00:06:35.800 --> 00:06:38.309 align:start position:0%
to send this entire text to the endpoint
to<00:06:36.000><c> see</c><00:06:36.520><c> how</c><00:06:36.759><c> the</c><00:06:36.880><c> medical</c><00:06:37.199><c> LM</c><00:06:37.639><c> summarizes</c>

00:06:38.309 --> 00:06:38.319 align:start position:0%
to see how the medical LM summarizes
 

00:06:38.319 --> 00:06:40.469 align:start position:0%
to see how the medical LM summarizes
this

00:06:40.469 --> 00:06:40.479 align:start position:0%
this
 

00:06:40.479 --> 00:06:43.710 align:start position:0%
this
text<00:06:41.479><c> so</c><00:06:42.240><c> as</c><00:06:42.360><c> you</c><00:06:42.479><c> can</c><00:06:42.599><c> see</c><00:06:42.840><c> it</c><00:06:43.039><c> has</c><00:06:43.199><c> summarized</c>

00:06:43.710 --> 00:06:43.720 align:start position:0%
text so as you can see it has summarized
 

00:06:43.720 --> 00:06:46.070 align:start position:0%
text so as you can see it has summarized
the<00:06:43.880><c> text</c><00:06:44.360><c> uh</c><00:06:44.479><c> it's</c><00:06:44.639><c> saying</c><00:06:45.120><c> that</c><00:06:45.479><c> the</c>

00:06:46.070 --> 00:06:46.080 align:start position:0%
the text uh it's saying that the
 

00:06:46.080 --> 00:06:48.070 align:start position:0%
the text uh it's saying that the
document<00:06:46.520><c> discusses</c><00:06:47.360><c> the</c><00:06:47.520><c> clinical</c>

00:06:48.070 --> 00:06:48.080 align:start position:0%
document discusses the clinical
 

00:06:48.080 --> 00:06:50.230 align:start position:0%
document discusses the clinical
characteristics<00:06:48.840><c> of</c><00:06:48.960><c> older</c><00:06:49.280><c> adults</c><00:06:49.880><c> infected</c>

00:06:50.230 --> 00:06:50.240 align:start position:0%
characteristics of older adults infected
 

00:06:50.240 --> 00:06:51.670 align:start position:0%
characteristics of older adults infected
with<00:06:50.440><c> covid-19</c><00:06:50.960><c> in</c>

00:06:51.670 --> 00:06:51.680 align:start position:0%
with covid-19 in
 

00:06:51.680 --> 00:06:53.830 align:start position:0%
with covid-19 in
Brazil<00:06:52.680><c> uh</c><00:06:52.759><c> It</c><00:06:52.880><c> also</c><00:06:53.080><c> says</c><00:06:53.319><c> that</c><00:06:53.440><c> the</c><00:06:53.599><c> study</c>

00:06:53.830 --> 00:06:53.840 align:start position:0%
Brazil uh It also says that the study
 

00:06:53.840 --> 00:06:57.350 align:start position:0%
Brazil uh It also says that the study
analyzed<00:06:54.240><c> 1544</c><00:06:55.080><c> confirmed</c><00:06:55.440><c> cases</c><00:06:55.680><c> of</c>

00:06:57.350 --> 00:06:57.360 align:start position:0%
analyzed 1544 confirmed cases of
 

00:06:57.360 --> 00:06:59.869 align:start position:0%
analyzed 1544 confirmed cases of
covid-19<00:06:58.360><c> and</c><00:06:58.599><c> the</c><00:06:58.759><c> overall</c><00:06:59.120><c> studies</c><00:06:59.560><c> such</c><00:06:59.720><c> as</c>

00:06:59.869 --> 00:06:59.879 align:start position:0%
covid-19 and the overall studies such as
 

00:06:59.879 --> 00:07:01.189 align:start position:0%
covid-19 and the overall studies such as
that<00:07:00.000><c> the</c><00:07:00.120><c> certain</c><00:07:00.440><c> demographic</c><00:07:01.000><c> and</c>

00:07:01.189 --> 00:07:01.199 align:start position:0%
that the certain demographic and
 

00:07:01.199 --> 00:07:03.110 align:start position:0%
that the certain demographic and
clinical<00:07:01.720><c> characteristics</c><00:07:02.520><c> are</c><00:07:02.680><c> associated</c>

00:07:03.110 --> 00:07:03.120 align:start position:0%
clinical characteristics are associated
 

00:07:03.120 --> 00:07:05.150 align:start position:0%
clinical characteristics are associated
with<00:07:03.680><c> increase</c><00:07:04.120><c> risk</c><00:07:04.400><c> of</c><00:07:04.639><c> death</c><00:07:04.879><c> from</c>

00:07:05.150 --> 00:07:05.160 align:start position:0%
with increase risk of death from
 

00:07:05.160 --> 00:07:08.029 align:start position:0%
with increase risk of death from
covid-19<00:07:05.919><c> older</c><00:07:06.280><c> adults</c><00:07:06.960><c> including</c><00:07:07.400><c> a</c><00:07:07.720><c> sex</c>

00:07:08.029 --> 00:07:08.039 align:start position:0%
covid-19 older adults including a sex
 

00:07:08.039 --> 00:07:09.830 align:start position:0%
covid-19 older adults including a sex
and<00:07:08.199><c> various</c><00:07:08.520><c> health</c><00:07:08.919><c> condition</c><00:07:09.520><c> so</c><00:07:09.720><c> yeah</c>

00:07:09.830 --> 00:07:09.840 align:start position:0%
and various health condition so yeah
 

00:07:09.840 --> 00:07:14.629 align:start position:0%
and various health condition so yeah
it's<00:07:10.080><c> able</c><00:07:10.280><c> to</c><00:07:10.400><c> summarize</c><00:07:10.840><c> the</c>

00:07:14.629 --> 00:07:14.639 align:start position:0%
 
 

00:07:14.639 --> 00:07:17.670 align:start position:0%
 
text<00:07:15.639><c> we</c><00:07:15.759><c> can</c><00:07:16.000><c> also</c><00:07:16.199><c> send</c><00:07:16.479><c> an</c><00:07:16.680><c> array</c><00:07:17.000><c> of</c><00:07:17.280><c> text</c>

00:07:17.670 --> 00:07:17.680 align:start position:0%
text we can also send an array of text
 

00:07:17.680 --> 00:07:20.589 align:start position:0%
text we can also send an array of text
to<00:07:17.919><c> the</c><00:07:18.160><c> llm</c><00:07:18.800><c> endpoint</c><00:07:19.800><c> as</c><00:07:19.919><c> you</c><00:07:20.080><c> see</c><00:07:20.400><c> I'm</c>

00:07:20.589 --> 00:07:20.599 align:start position:0%
to the llm endpoint as you see I'm
 

00:07:20.599 --> 00:07:23.189 align:start position:0%
to the llm endpoint as you see I'm
sending<00:07:21.120><c> here</c><00:07:21.400><c> an</c><00:07:21.919><c> input</c><00:07:22.280><c> text</c><00:07:22.639><c> that</c><00:07:22.840><c> contains</c>

00:07:23.189 --> 00:07:23.199 align:start position:0%
sending here an input text that contains
 

00:07:23.199 --> 00:07:25.749 align:start position:0%
sending here an input text that contains
an<00:07:23.360><c> array</c><00:07:23.680><c> of</c><00:07:23.919><c> covid-19</c><00:07:24.560><c> article</c><00:07:25.280><c> and</c><00:07:25.479><c> when</c><00:07:25.599><c> I</c>

00:07:25.749 --> 00:07:25.759 align:start position:0%
an array of covid-19 article and when I
 

00:07:25.759 --> 00:07:27.469 align:start position:0%
an array of covid-19 article and when I
send<00:07:26.000><c> it</c><00:07:26.120><c> to</c><00:07:26.240><c> the</c><00:07:26.360><c> llm</c><00:07:26.759><c> endpoint</c><00:07:27.199><c> for</c>

00:07:27.469 --> 00:07:27.479 align:start position:0%
send it to the llm endpoint for
 

00:07:27.479 --> 00:07:30.070 align:start position:0%
send it to the llm endpoint for
summarization<00:07:28.479><c> it's</c><00:07:28.759><c> able</c><00:07:29.039><c> to</c><00:07:29.680><c> properly</c>

00:07:30.070 --> 00:07:30.080 align:start position:0%
summarization it's able to properly
 

00:07:30.080 --> 00:07:34.230 align:start position:0%
summarization it's able to properly
summarize<00:07:30.639><c> both</c><00:07:30.840><c> the</c>

00:07:34.230 --> 00:07:34.240 align:start position:0%
 
 

00:07:34.240 --> 00:07:38.309 align:start position:0%
 
text<00:07:35.240><c> similar</c><00:07:35.599><c> to</c><00:07:35.759><c> the</c><00:07:35.960><c> q&amp;</c><00:07:36.720><c> a</c><00:07:37.720><c> you</c><00:07:37.840><c> can</c><00:07:38.080><c> also</c>

00:07:38.309 --> 00:07:38.319 align:start position:0%
text similar to the q&amp; a you can also
 

00:07:38.319 --> 00:07:40.589 align:start position:0%
text similar to the q&amp; a you can also
send<00:07:38.680><c> custom</c><00:07:39.120><c> promps</c><00:07:39.520><c> for</c>

00:07:40.589 --> 00:07:40.599 align:start position:0%
send custom promps for
 

00:07:40.599 --> 00:07:42.830 align:start position:0%
send custom promps for
summarization<00:07:41.599><c> here</c><00:07:42.000><c> I</c><00:07:42.080><c> am</c><00:07:42.280><c> sending</c><00:07:42.680><c> the</c>

00:07:42.830 --> 00:07:42.840 align:start position:0%
summarization here I am sending the
 

00:07:42.840 --> 00:07:45.909 align:start position:0%
summarization here I am sending the
request<00:07:43.160><c> to</c><00:07:43.280><c> the</c><00:07:43.360><c> llm</c><00:07:43.840><c> endpoint</c><00:07:44.840><c> to</c><00:07:45.240><c> summarize</c>

00:07:45.909 --> 00:07:45.919 align:start position:0%
request to the llm endpoint to summarize
 

00:07:45.919 --> 00:07:48.550 align:start position:0%
request to the llm endpoint to summarize
this<00:07:46.080><c> medical</c><00:07:46.520><c> text</c><00:07:46.840><c> in</c><00:07:47.080><c> less</c><00:07:47.280><c> than</c><00:07:47.479><c> 30</c>

00:07:48.550 --> 00:07:48.560 align:start position:0%
this medical text in less than 30
 

00:07:48.560 --> 00:07:51.790 align:start position:0%
this medical text in less than 30
words<00:07:49.560><c> and</c><00:07:49.759><c> as</c><00:07:49.879><c> you</c><00:07:50.039><c> can</c><00:07:50.199><c> see</c><00:07:50.479><c> from</c><00:07:50.680><c> the</c><00:07:50.840><c> result</c>

00:07:51.790 --> 00:07:51.800 align:start position:0%
words and as you can see from the result
 

00:07:51.800 --> 00:07:54.670 align:start position:0%
words and as you can see from the result
the<00:07:52.039><c> response</c><00:07:52.680><c> for</c><00:07:52.919><c> the</c><00:07:53.080><c> same</c><00:07:53.479><c> document</c><00:07:54.479><c> is</c>

00:07:54.670 --> 00:07:54.680 align:start position:0%
the response for the same document is
 

00:07:54.680 --> 00:07:57.350 align:start position:0%
the response for the same document is
now<00:07:54.919><c> much</c><00:07:55.120><c> more</c><00:07:55.400><c> concise</c><00:07:55.960><c> and</c><00:07:56.680><c> less</c><00:07:56.879><c> than</c><00:07:57.039><c> 30</c>

00:07:57.350 --> 00:07:57.360 align:start position:0%
now much more concise and less than 30
 

00:07:57.360 --> 00:07:59.869 align:start position:0%
now much more concise and less than 30
words

00:07:59.869 --> 00:07:59.879 align:start position:0%
words
 

00:07:59.879 --> 00:08:02.149 align:start position:0%
words
so<00:08:00.199><c> this</c><00:08:00.319><c> is</c><00:08:00.520><c> how</c><00:08:00.800><c> real</c><00:08:01.080><c> time</c><00:08:01.280><c> inference</c><00:08:01.800><c> API</c>

00:08:02.149 --> 00:08:02.159 align:start position:0%
so this is how real time inference API
 

00:08:02.159 --> 00:08:04.710 align:start position:0%
so this is how real time inference API
end<00:08:02.400><c> points</c><00:08:02.720><c> work</c><00:08:02.960><c> for</c><00:08:03.240><c> this</c><00:08:03.440><c> model</c><00:08:03.720><c> on</c><00:08:03.879><c> AWS</c>

00:08:04.710 --> 00:08:04.720 align:start position:0%
end points work for this model on AWS
 

00:08:04.720 --> 00:08:07.189 align:start position:0%
end points work for this model on AWS
stemer<00:08:05.720><c> the</c><00:08:05.879><c> real</c><00:08:06.080><c> time</c><00:08:06.240><c> inference</c><00:08:06.680><c> is</c><00:08:06.800><c> useful</c>

00:08:07.189 --> 00:08:07.199 align:start position:0%
stemer the real time inference is useful
 

00:08:07.199 --> 00:08:09.629 align:start position:0%
stemer the real time inference is useful
when<00:08:07.360><c> you</c><00:08:07.520><c> have</c><00:08:07.680><c> a</c><00:08:07.840><c> few</c><00:08:08.080><c> input</c><00:08:08.479><c> data</c><00:08:09.039><c> and</c><00:08:09.479><c> the</c>

00:08:09.629 --> 00:08:09.639 align:start position:0%
when you have a few input data and the
 

00:08:09.639 --> 00:08:16.749 align:start position:0%
when you have a few input data and the
responses<00:08:10.159><c> need</c><00:08:10.319><c> to</c><00:08:10.479><c> be</c>

00:08:16.749 --> 00:08:16.759 align:start position:0%
 
 

00:08:16.759 --> 00:08:20.070 align:start position:0%
 
quick<00:08:17.759><c> AWS</c><00:08:18.479><c> automatically</c><00:08:19.199><c> tracks</c><00:08:19.599><c> the</c><00:08:19.720><c> usage</c>

00:08:20.070 --> 00:08:20.080 align:start position:0%
quick AWS automatically tracks the usage
 

00:08:20.080 --> 00:08:22.110 align:start position:0%
quick AWS automatically tracks the usage
of<00:08:20.199><c> your</c><00:08:20.360><c> endpoints</c><00:08:20.919><c> and</c><00:08:21.080><c> builds</c><00:08:21.440><c> you</c>

00:08:22.110 --> 00:08:22.120 align:start position:0%
of your endpoints and builds you
 

00:08:22.120 --> 00:08:24.469 align:start position:0%
of your endpoints and builds you
accordingly<00:08:23.120><c> so</c><00:08:23.479><c> when</c><00:08:23.639><c> you</c><00:08:23.759><c> are</c><00:08:23.919><c> done</c><00:08:24.159><c> using</c>

00:08:24.469 --> 00:08:24.479 align:start position:0%
accordingly so when you are done using
 

00:08:24.479 --> 00:08:26.710 align:start position:0%
accordingly so when you are done using
your<00:08:24.680><c> endpoint</c><00:08:25.240><c> it</c><00:08:25.400><c> might</c><00:08:25.599><c> be</c><00:08:25.800><c> wise</c><00:08:26.039><c> to</c><00:08:26.240><c> stop</c>

00:08:26.710 --> 00:08:26.720 align:start position:0%
your endpoint it might be wise to stop
 

00:08:26.720 --> 00:08:31.469 align:start position:0%
your endpoint it might be wise to stop
it<00:08:27.720><c> to</c><00:08:27.919><c> do</c><00:08:28.120><c> so</c><00:08:28.560><c> find</c><00:08:28.840><c> your</c><00:08:29.599><c> point</c><00:08:29.960><c> spage</c><00:08:30.400><c> on</c><00:08:30.960><c> AWS</c>

00:08:31.469 --> 00:08:31.479 align:start position:0%
it to do so find your point spage on AWS
 

00:08:31.479 --> 00:08:36.190 align:start position:0%
it to do so find your point spage on AWS
s<00:08:32.240><c> maker</c><00:08:33.240><c> from</c><00:08:33.599><c> there</c><00:08:33.800><c> you</c><00:08:33.919><c> can</c><00:08:34.120><c> delete</c><00:08:34.440><c> your</c>

00:08:36.190 --> 00:08:36.200 align:start position:0%
s maker from there you can delete your
 

00:08:36.200 --> 00:08:40.399 align:start position:0%
s maker from there you can delete your
endpoint<00:08:37.200><c> thank</c><00:08:37.399><c> you</c>

