WEBVTT
Kind: captions
Language: en

00:00:00.440 --> 00:00:05.150 align:start position:0%
 
[Music]

00:00:05.150 --> 00:00:05.160 align:start position:0%
 
 

00:00:05.160 --> 00:00:08.790 align:start position:0%
 
welcome<00:00:05.839><c> everyone</c><00:00:06.839><c> at</c><00:00:07.040><c> NLP</c><00:00:07.600><c> Summit</c><00:00:08.519><c> thank</c><00:00:08.679><c> you</c>

00:00:08.790 --> 00:00:08.800 align:start position:0%
welcome everyone at NLP Summit thank you
 

00:00:08.800 --> 00:00:11.350 align:start position:0%
welcome everyone at NLP Summit thank you
for<00:00:09.040><c> having</c><00:00:09.280><c> me</c><00:00:09.559><c> here</c><00:00:10.360><c> and</c><00:00:10.679><c> I'm</c><00:00:11.040><c> pretty</c>

00:00:11.350 --> 00:00:11.360 align:start position:0%
for having me here and I'm pretty
 

00:00:11.360 --> 00:00:14.509 align:start position:0%
for having me here and I'm pretty
excited<00:00:11.799><c> to</c><00:00:11.960><c> share</c><00:00:12.559><c> some</c><00:00:13.559><c> uh</c><00:00:14.240><c> some</c>

00:00:14.509 --> 00:00:14.519 align:start position:0%
excited to share some uh some
 

00:00:14.519 --> 00:00:17.710 align:start position:0%
excited to share some uh some
development<00:00:15.160><c> on</c><00:00:15.639><c> nsql</c><00:00:16.600><c> site</c><00:00:17.119><c> in</c><00:00:17.279><c> terms</c><00:00:17.520><c> of</c>

00:00:17.710 --> 00:00:17.720 align:start position:0%
development on nsql site in terms of
 

00:00:17.720 --> 00:00:19.910 align:start position:0%
development on nsql site in terms of
natural<00:00:18.080><c> language</c><00:00:18.480><c> processing</c><00:00:19.359><c> and</c><00:00:19.520><c> natural</c>

00:00:19.910 --> 00:00:19.920 align:start position:0%
natural language processing and natural
 

00:00:19.920 --> 00:00:22.150 align:start position:0%
natural language processing and natural
language<00:00:20.680><c> understanding</c><00:00:21.480><c> uh</c><00:00:21.600><c> my</c><00:00:21.720><c> name</c><00:00:21.880><c> is</c>

00:00:22.150 --> 00:00:22.160 align:start position:0%
language understanding uh my name is
 

00:00:22.160 --> 00:00:25.509 align:start position:0%
language understanding uh my name is
Dennis<00:00:22.600><c> I</c><00:00:22.680><c> am</c><00:00:22.800><c> COO</c><00:00:23.279><c> of</c><00:00:23.480><c> nlq</c><00:00:24.480><c> company</c><00:00:24.840><c> based</c><00:00:25.199><c> in</c>

00:00:25.509 --> 00:00:25.519 align:start position:0%
Dennis I am COO of nlq company based in
 

00:00:25.519 --> 00:00:29.109 align:start position:0%
Dennis I am COO of nlq company based in
England<00:00:26.519><c> and</c><00:00:26.960><c> my</c><00:00:27.119><c> team</c><00:00:27.480><c> consist</c><00:00:28.000><c> of</c><00:00:28.679><c> um</c><00:00:29.000><c> you</c>

00:00:29.109 --> 00:00:29.119 align:start position:0%
England and my team consist of um you
 

00:00:29.119 --> 00:00:31.350 align:start position:0%
England and my team consist of um you
know<00:00:29.400><c> multi</c>

00:00:31.350 --> 00:00:31.360 align:start position:0%
know multi
 

00:00:31.360 --> 00:00:34.950 align:start position:0%
know multi
disciplinary<00:00:32.360><c> professionals</c><00:00:33.520><c> from</c><00:00:34.520><c> AI</c>

00:00:34.950 --> 00:00:34.960 align:start position:0%
disciplinary professionals from AI
 

00:00:34.960 --> 00:00:37.549 align:start position:0%
disciplinary professionals from AI
machine<00:00:35.320><c> learning</c><00:00:36.000><c> technical</c><00:00:36.559><c> side</c><00:00:37.160><c> as</c><00:00:37.320><c> well</c>

00:00:37.549 --> 00:00:37.559 align:start position:0%
machine learning technical side as well
 

00:00:37.559 --> 00:00:41.350 align:start position:0%
machine learning technical side as well
as<00:00:38.559><c> uh</c><00:00:38.719><c> real</c><00:00:39.120><c> basically</c><00:00:39.640><c> doctors</c><00:00:40.320><c> who</c><00:00:40.760><c> has</c>

00:00:41.350 --> 00:00:41.360 align:start position:0%
as uh real basically doctors who has
 

00:00:41.360 --> 00:00:44.110 align:start position:0%
as uh real basically doctors who has
real<00:00:41.960><c> experience</c><00:00:42.960><c> with</c><00:00:43.120><c> the</c><00:00:43.360><c> patient</c><00:00:43.960><c> with</c>

00:00:44.110 --> 00:00:44.120 align:start position:0%
real experience with the patient with
 

00:00:44.120 --> 00:00:47.830 align:start position:0%
real experience with the patient with
the<00:00:44.360><c> treatment</c><00:00:45.719><c> patients</c><00:00:46.719><c> um</c><00:00:47.320><c> yeah</c><00:00:47.600><c> B</c>

00:00:47.830 --> 00:00:47.840 align:start position:0%
the treatment patients um yeah B
 

00:00:47.840 --> 00:00:49.990 align:start position:0%
the treatment patients um yeah B
basically<00:00:48.800><c> um</c><00:00:49.239><c> we</c>

00:00:49.990 --> 00:00:50.000 align:start position:0%
basically um we
 

00:00:50.000 --> 00:00:52.990 align:start position:0%
basically um we
did<00:00:51.000><c> a</c><00:00:51.160><c> couple</c><00:00:51.399><c> of</c><00:00:51.640><c> pretty</c><00:00:51.960><c> excited</c><00:00:52.480><c> projects</c>

00:00:52.990 --> 00:00:53.000 align:start position:0%
did a couple of pretty excited projects
 

00:00:53.000 --> 00:00:55.869 align:start position:0%
did a couple of pretty excited projects
and<00:00:53.120><c> learn</c><00:00:53.480><c> a</c><00:00:53.640><c> lot</c><00:00:54.000><c> of</c><00:00:54.239><c> from</c><00:00:54.520><c> them</c><00:00:55.280><c> one</c><00:00:55.440><c> of</c><00:00:55.600><c> this</c>

00:00:55.869 --> 00:00:55.879 align:start position:0%
and learn a lot of from them one of this
 

00:00:55.879 --> 00:01:01.389 align:start position:0%
and learn a lot of from them one of this
project<00:00:56.280><c> is</c><00:00:56.920><c> supporting</c><00:00:57.480><c> aen</c><00:00:57.840><c> uni</c><00:00:58.320><c> Clinic</c><00:00:59.320><c> uh</c>

00:01:01.389 --> 00:01:01.399 align:start position:0%
project is supporting aen uni Clinic uh
 

00:01:01.399 --> 00:01:05.590 align:start position:0%
project is supporting aen uni Clinic uh
for<00:01:02.399><c> for</c><00:01:02.960><c> making</c><00:01:03.960><c> uh</c><00:01:04.159><c> data</c><00:01:04.479><c> more</c><00:01:04.760><c> accessible</c>

00:01:05.590 --> 00:01:05.600 align:start position:0%
for for making uh data more accessible
 

00:01:05.600 --> 00:01:08.789 align:start position:0%
for for making uh data more accessible
for<00:01:06.600><c> like</c><00:01:06.799><c> Intensive</c><00:01:07.280><c> Care</c><00:01:07.479><c> Unit</c><00:01:07.759><c> datab</c><00:01:08.159><c> bases</c>

00:01:08.789 --> 00:01:08.799 align:start position:0%
for like Intensive Care Unit datab bases
 

00:01:08.799 --> 00:01:11.550 align:start position:0%
for like Intensive Care Unit datab bases
patients<00:01:09.200><c> record</c><00:01:10.119><c> accessible</c><00:01:11.119><c> uh</c><00:01:11.240><c> in</c>

00:01:11.550 --> 00:01:11.560 align:start position:0%
patients record accessible uh in
 

00:01:11.560 --> 00:01:13.950 align:start position:0%
patients record accessible uh in
different<00:01:12.240><c> areas</c><00:01:12.640><c> in</c><00:01:12.880><c> Germany</c><00:01:13.479><c> because</c><00:01:13.759><c> of</c>

00:01:13.950 --> 00:01:13.960 align:start position:0%
different areas in Germany because of
 

00:01:13.960 --> 00:01:17.390 align:start position:0%
different areas in Germany because of
the<00:01:14.119><c> vision</c><00:01:14.520><c> of</c><00:01:15.479><c> uh</c><00:01:15.680><c> German</c><00:01:16.280><c> Med</c>

00:01:17.390 --> 00:01:17.400 align:start position:0%
the vision of uh German Med
 

00:01:17.400 --> 00:01:20.630 align:start position:0%
the vision of uh German Med
German<00:01:18.400><c> policy</c><00:01:18.840><c> and</c><00:01:19.119><c> German</c><00:01:19.680><c> New</c><00:01:20.040><c> Politics</c>

00:01:20.630 --> 00:01:20.640 align:start position:0%
German policy and German New Politics
 

00:01:20.640 --> 00:01:23.749 align:start position:0%
German policy and German New Politics
that<00:01:20.960><c> all</c><00:01:21.159><c> the</c><00:01:22.040><c> databases</c><00:01:22.680><c> in</c><00:01:22.840><c> Germany</c><00:01:23.600><c> have</c>

00:01:23.749 --> 00:01:23.759 align:start position:0%
that all the databases in Germany have
 

00:01:23.759 --> 00:01:27.030 align:start position:0%
that all the databases in Germany have
to<00:01:23.960><c> be</c><00:01:24.119><c> soon</c><00:01:24.799><c> standardized</c><00:01:25.720><c> in</c><00:01:25.840><c> order</c><00:01:26.240><c> to</c><00:01:26.720><c> get</c>

00:01:27.030 --> 00:01:27.040 align:start position:0%
to be soon standardized in order to get
 

00:01:27.040 --> 00:01:30.429 align:start position:0%
to be soon standardized in order to get
ability<00:01:27.600><c> to</c><00:01:28.600><c> um</c><00:01:29.479><c> access</c>

00:01:30.429 --> 00:01:30.439 align:start position:0%
ability to um access
 

00:01:30.439 --> 00:01:32.190 align:start position:0%
ability to um access
the<00:01:30.560><c> same</c><00:01:30.880><c> patient</c><00:01:31.320><c> information</c><00:01:31.880><c> from</c>

00:01:32.190 --> 00:01:32.200 align:start position:0%
the same patient information from
 

00:01:32.200 --> 00:01:35.990 align:start position:0%
the same patient information from
different<00:01:33.119><c> regions</c><00:01:34.079><c> in</c><00:01:34.600><c> Germany</c><00:01:35.600><c> that</c><00:01:35.759><c> was</c>

00:01:35.990 --> 00:01:36.000 align:start position:0%
different regions in Germany that was
 

00:01:36.000 --> 00:01:38.910 align:start position:0%
different regions in Germany that was
excited<00:01:36.479><c> C</c><00:01:37.079><c> project</c><00:01:37.560><c> with</c><00:01:37.880><c> aen</c><00:01:38.159><c> uni</c><00:01:38.439><c> clinic</c>

00:01:38.910 --> 00:01:38.920 align:start position:0%
excited C project with aen uni clinic
 

00:01:38.920 --> 00:01:41.830 align:start position:0%
excited C project with aen uni clinic
and<00:01:39.119><c> we</c><00:01:39.280><c> help</c><00:01:39.560><c> them</c><00:01:39.720><c> to</c><00:01:39.840><c> build</c><00:01:40.159><c> AI</c><00:01:40.640><c> interface</c>

00:01:41.830 --> 00:01:41.840 align:start position:0%
and we help them to build AI interface
 

00:01:41.840 --> 00:01:44.870 align:start position:0%
and we help them to build AI interface
to<00:01:42.840><c> um</c><00:01:43.159><c> Intensive</c><00:01:43.680><c> Care</c><00:01:43.880><c> Unit</c>

00:01:44.870 --> 00:01:44.880 align:start position:0%
to um Intensive Care Unit
 

00:01:44.880 --> 00:01:48.149 align:start position:0%
to um Intensive Care Unit
databases<00:01:45.880><c> in</c><00:01:46.079><c> order</c><00:01:46.640><c> Physicians</c><00:01:47.640><c> or</c>

00:01:48.149 --> 00:01:48.159 align:start position:0%
databases in order Physicians or
 

00:01:48.159 --> 00:01:50.950 align:start position:0%
databases in order Physicians or
clinicians<00:01:49.040><c> or</c><00:01:49.320><c> head</c><00:01:49.520><c> of</c><00:01:49.759><c> departments</c><00:01:50.719><c> have</c>

00:01:50.950 --> 00:01:50.960 align:start position:0%
clinicians or head of departments have
 

00:01:50.960 --> 00:01:55.389 align:start position:0%
clinicians or head of departments have
ability<00:01:51.479><c> to</c><00:01:52.479><c> uh</c><00:01:52.759><c> quickly</c><00:01:53.200><c> receive</c><00:01:54.200><c> um</c><00:01:54.719><c> patient</c>

00:01:55.389 --> 00:01:55.399 align:start position:0%
ability to uh quickly receive um patient
 

00:01:55.399 --> 00:01:58.389 align:start position:0%
ability to uh quickly receive um patient
data<00:01:55.920><c> analytics</c><00:01:56.920><c> uh</c><00:01:57.079><c> the</c><00:01:57.200><c> another</c><00:01:57.640><c> project</c>

00:01:58.389 --> 00:01:58.399 align:start position:0%
data analytics uh the another project
 

00:01:58.399 --> 00:02:02.830 align:start position:0%
data analytics uh the another project
which<00:01:59.280><c> we</c><00:01:59.439><c> are</c><00:02:00.039><c> really</c><00:02:00.560><c> proud</c><00:02:01.560><c> on</c><00:02:01.960><c> this</c><00:02:02.079><c> is</c><00:02:02.360><c> a</c>

00:02:02.830 --> 00:02:02.840 align:start position:0%
which we are really proud on this is a
 

00:02:02.840 --> 00:02:05.270 align:start position:0%
which we are really proud on this is a
recent<00:02:03.240><c> research</c><00:02:03.600><c> and</c><00:02:03.759><c> development</c><00:02:04.280><c> project</c>

00:02:05.270 --> 00:02:05.280 align:start position:0%
recent research and development project
 

00:02:05.280 --> 00:02:07.550 align:start position:0%
recent research and development project
together<00:02:05.600><c> with</c><00:02:05.840><c> innovate</c><00:02:06.439><c> UK</c><00:02:06.920><c> here</c><00:02:07.119><c> in</c>

00:02:07.550 --> 00:02:07.560 align:start position:0%
together with innovate UK here in
 

00:02:07.560 --> 00:02:12.910 align:start position:0%
together with innovate UK here in
England<00:02:08.560><c> uh</c><00:02:08.879><c> we</c><00:02:09.840><c> um</c><00:02:10.599><c> created</c><00:02:11.520><c> we</c><00:02:11.720><c> developed</c><00:02:12.480><c> um</c>

00:02:12.910 --> 00:02:12.920 align:start position:0%
England uh we um created we developed um
 

00:02:12.920 --> 00:02:15.910 align:start position:0%
England uh we um created we developed um
AI<00:02:13.319><c> tool</c><00:02:13.800><c> which</c><00:02:14.319><c> helps</c><00:02:14.920><c> empowering</c>

00:02:15.910 --> 00:02:15.920 align:start position:0%
AI tool which helps empowering
 

00:02:15.920 --> 00:02:19.750 align:start position:0%
AI tool which helps empowering
Physicians<00:02:16.720><c> with</c><00:02:17.040><c> intuitive</c><00:02:18.040><c> AI</c>

00:02:19.750 --> 00:02:19.760 align:start position:0%
Physicians with intuitive AI
 

00:02:19.760 --> 00:02:22.270 align:start position:0%
Physicians with intuitive AI
interface<00:02:20.760><c> working</c><00:02:21.200><c> on</c><00:02:21.400><c> top</c><00:02:21.599><c> of</c><00:02:21.760><c> the</c><00:02:21.920><c> poorly</c>

00:02:22.270 --> 00:02:22.280 align:start position:0%
interface working on top of the poorly
 

00:02:22.280 --> 00:02:24.949 align:start position:0%
interface working on top of the poorly
accessible<00:02:22.720><c> clinical</c><00:02:23.200><c> data</c><00:02:24.000><c> to</c><00:02:24.239><c> inform</c><00:02:24.680><c> and</c>

00:02:24.949 --> 00:02:24.959 align:start position:0%
accessible clinical data to inform and
 

00:02:24.959 --> 00:02:27.150 align:start position:0%
accessible clinical data to inform and
accelerate<00:02:25.680><c> clinical</c><00:02:26.239><c> decision-</c><00:02:26.599><c> making</c>

00:02:27.150 --> 00:02:27.160 align:start position:0%
accelerate clinical decision- making
 

00:02:27.160 --> 00:02:30.670 align:start position:0%
accelerate clinical decision- making
processes<00:02:28.160><c> so</c><00:02:28.599><c> um</c><00:02:29.040><c> yeah</c><00:02:29.360><c> B</c><00:02:29.519><c> basically</c><00:02:30.280><c> let's</c>

00:02:30.670 --> 00:02:30.680 align:start position:0%
processes so um yeah B basically let's
 

00:02:30.680 --> 00:02:35.390 align:start position:0%
processes so um yeah B basically let's
get<00:02:30.920><c> back</c><00:02:31.480><c> uh</c><00:02:32.319><c> to</c><00:02:33.040><c> my</c><00:02:33.200><c> slides</c><00:02:33.840><c> and</c><00:02:34.200><c> um</c><00:02:34.800><c> yeah</c><00:02:35.120><c> so</c>

00:02:35.390 --> 00:02:35.400 align:start position:0%
get back uh to my slides and um yeah so
 

00:02:35.400 --> 00:02:37.470 align:start position:0%
get back uh to my slides and um yeah so
I<00:02:35.480><c> will</c><00:02:35.680><c> start</c><00:02:36.120><c> from</c><00:02:36.360><c> the</c><00:02:36.519><c> early</c><00:02:36.879><c> beginning</c><00:02:37.280><c> so</c>

00:02:37.470 --> 00:02:37.480 align:start position:0%
I will start from the early beginning so
 

00:02:37.480 --> 00:02:39.270 align:start position:0%
I will start from the early beginning so
you<00:02:37.680><c> can</c><00:02:37.920><c> better</c><00:02:38.480><c> understand</c><00:02:38.640><c> the</c><00:02:38.840><c> problem</c><00:02:39.159><c> we</c>

00:02:39.270 --> 00:02:39.280 align:start position:0%
you can better understand the problem we
 

00:02:39.280 --> 00:02:41.990 align:start position:0%
you can better understand the problem we
are<00:02:39.440><c> trying</c><00:02:39.800><c> to</c><00:02:40.040><c> solve</c><00:02:40.879><c> and</c><00:02:41.319><c> uh</c><00:02:41.560><c> yeah</c><00:02:41.800><c> the</c>

00:02:41.990 --> 00:02:42.000 align:start position:0%
are trying to solve and uh yeah the
 

00:02:42.000 --> 00:02:45.149 align:start position:0%
are trying to solve and uh yeah the
approach<00:02:42.680><c> which</c><00:02:43.680><c> we</c><00:02:43.800><c> are</c><00:02:44.000><c> going</c><00:02:44.360><c> to</c><00:02:44.640><c> pretend</c>

00:02:45.149 --> 00:02:45.159 align:start position:0%
approach which we are going to pretend
 

00:02:45.159 --> 00:02:47.949 align:start position:0%
approach which we are going to pretend
here<00:02:46.000><c> uh</c><00:02:46.200><c> and</c><00:02:46.480><c> how</c><00:02:46.800><c> from</c><00:02:47.040><c> the</c><00:02:47.319><c> technology</c>

00:02:47.949 --> 00:02:47.959 align:start position:0%
here uh and how from the technology
 

00:02:47.959 --> 00:02:50.030 align:start position:0%
here uh and how from the technology
perspective<00:02:48.519><c> we</c><00:02:48.640><c> are</c><00:02:49.000><c> we</c><00:02:49.120><c> are</c><00:02:49.239><c> solving</c><00:02:49.800><c> this</c>

00:02:50.030 --> 00:02:50.040 align:start position:0%
perspective we are we are solving this
 

00:02:50.040 --> 00:02:54.350 align:start position:0%
perspective we are we are solving this
problem<00:02:50.840><c> so</c><00:02:51.400><c> um</c><00:02:51.959><c> basically</c><00:02:52.959><c> our</c><00:02:53.319><c> mission</c><00:02:54.040><c> as</c><00:02:54.120><c> a</c>

00:02:54.350 --> 00:02:54.360 align:start position:0%
problem so um basically our mission as a
 

00:02:54.360 --> 00:02:57.470 align:start position:0%
problem so um basically our mission as a
company<00:02:55.040><c> to</c><00:02:55.280><c> solve</c><00:02:56.040><c> a</c><00:02:56.519><c> data</c><00:02:56.879><c> analytics</c>

00:02:57.470 --> 00:02:57.480 align:start position:0%
company to solve a data analytics
 

00:02:57.480 --> 00:02:59.830 align:start position:0%
company to solve a data analytics
bottleneck<00:02:58.159><c> problem</c><00:02:59.120><c> between</c><00:02:59.480><c> Technic</c>

00:02:59.830 --> 00:02:59.840 align:start position:0%
bottleneck problem between Technic
 

00:02:59.840 --> 00:03:02.630 align:start position:0%
bottleneck problem between Technic
Technical<00:03:00.159><c> and</c><00:03:00.680><c> non-technical</c><00:03:01.680><c> employees</c>

00:03:02.630 --> 00:03:02.640 align:start position:0%
Technical and non-technical employees
 

00:03:02.640 --> 00:03:05.789 align:start position:0%
Technical and non-technical employees
Physicians<00:03:03.319><c> and</c><00:03:03.599><c> head</c><00:03:03.799><c> of</c><00:03:04.440><c> departments</c><00:03:05.440><c> uh</c><00:03:05.599><c> as</c>

00:03:05.789 --> 00:03:05.799 align:start position:0%
Physicians and head of departments uh as
 

00:03:05.799 --> 00:03:09.229 align:start position:0%
Physicians and head of departments uh as
non<00:03:06.239><c> data</c><00:03:06.519><c> skilled</c><00:03:06.879><c> employees</c><00:03:07.519><c> like</c><00:03:08.440><c> data</c>

00:03:09.229 --> 00:03:09.239 align:start position:0%
non data skilled employees like data
 

00:03:09.239 --> 00:03:11.430 align:start position:0%
non data skilled employees like data
accessibility<00:03:10.239><c> uh</c><00:03:10.440><c> first</c><00:03:10.640><c> of</c><00:03:10.760><c> all</c><00:03:11.040><c> they</c><00:03:11.200><c> don't</c>

00:03:11.430 --> 00:03:11.440 align:start position:0%
accessibility uh first of all they don't
 

00:03:11.440 --> 00:03:15.350 align:start position:0%
accessibility uh first of all they don't
have<00:03:11.680><c> access</c><00:03:12.000><c> to</c><00:03:12.239><c> the</c><00:03:12.920><c> databases</c><00:03:13.920><c> and</c><00:03:14.599><c> the</c>

00:03:15.350 --> 00:03:15.360 align:start position:0%
have access to the databases and the
 

00:03:15.360 --> 00:03:17.309 align:start position:0%
have access to the databases and the
they<00:03:15.599><c> don't</c><00:03:15.920><c> have</c><00:03:16.120><c> access</c><00:03:16.360><c> to</c><00:03:16.480><c> the</c><00:03:16.680><c> databases</c>

00:03:17.309 --> 00:03:17.319 align:start position:0%
they don't have access to the databases
 

00:03:17.319 --> 00:03:18.830 align:start position:0%
they don't have access to the databases
because<00:03:17.640><c> they're</c><00:03:17.879><c> not</c><00:03:18.120><c> te</c><00:03:18.400><c> technically</c>

00:03:18.830 --> 00:03:18.840 align:start position:0%
because they're not te technically
 

00:03:18.840 --> 00:03:21.430 align:start position:0%
because they're not te technically
skilled<00:03:19.280><c> enough</c><00:03:19.879><c> in</c><00:03:20.000><c> order</c><00:03:20.440><c> to</c><00:03:21.000><c> you</c><00:03:21.120><c> know</c><00:03:21.239><c> to</c>

00:03:21.430 --> 00:03:21.440 align:start position:0%
skilled enough in order to you know to
 

00:03:21.440 --> 00:03:25.309 align:start position:0%
skilled enough in order to you know to
create<00:03:21.840><c> SQL</c><00:03:22.440><c> and</c><00:03:22.959><c> uh</c><00:03:23.640><c> um</c><00:03:23.879><c> execute</c><00:03:24.360><c> this</c><00:03:24.519><c> SQL</c><00:03:25.159><c> on</c>

00:03:25.309 --> 00:03:25.319 align:start position:0%
create SQL and uh um execute this SQL on
 

00:03:25.319 --> 00:03:28.710 align:start position:0%
create SQL and uh um execute this SQL on
top<00:03:25.480><c> of</c><00:03:25.599><c> the</c><00:03:25.760><c> database</c><00:03:26.280><c> so</c><00:03:26.920><c> basically</c><00:03:27.920><c> um</c><00:03:28.200><c> SQL</c>

00:03:28.710 --> 00:03:28.720 align:start position:0%
top of the database so basically um SQL
 

00:03:28.720 --> 00:03:30.509 align:start position:0%
top of the database so basically um SQL
analytics<00:03:29.280><c> is</c><00:03:29.439><c> very</c><00:03:29.840><c> complex</c><00:03:30.239><c> for</c>

00:03:30.509 --> 00:03:30.519 align:start position:0%
analytics is very complex for
 

00:03:30.519 --> 00:03:32.910 align:start position:0%
analytics is very complex for
non-technical<00:03:31.519><c> employees</c><00:03:32.519><c> they</c><00:03:32.680><c> have</c>

00:03:32.910 --> 00:03:32.920 align:start position:0%
non-technical employees they have
 

00:03:32.920 --> 00:03:35.229 align:start position:0%
non-technical employees they have
limited<00:03:33.480><c> accessibility</c><00:03:34.360><c> even</c><00:03:34.680><c> if</c><00:03:34.799><c> you</c><00:03:34.959><c> can</c>

00:03:35.229 --> 00:03:35.239 align:start position:0%
limited accessibility even if you can
 

00:03:35.239 --> 00:03:38.710 align:start position:0%
limited accessibility even if you can
provide<00:03:36.120><c> um</c><00:03:36.959><c> them</c><00:03:37.519><c> database</c><00:03:38.159><c> access</c><00:03:38.439><c> it</c><00:03:38.560><c> will</c>

00:03:38.710 --> 00:03:38.720 align:start position:0%
provide um them database access it will
 

00:03:38.720 --> 00:03:40.509 align:start position:0%
provide um them database access it will
be<00:03:38.879><c> useless</c><00:03:39.319><c> for</c><00:03:39.519><c> them</c><00:03:39.760><c> because</c><00:03:40.000><c> they</c><00:03:40.159><c> don't</c>

00:03:40.509 --> 00:03:40.519 align:start position:0%
be useless for them because they don't
 

00:03:40.519 --> 00:03:44.030 align:start position:0%
be useless for them because they don't
have<00:03:41.000><c> enough</c><00:03:41.319><c> skills</c><00:03:41.840><c> to</c><00:03:42.640><c> use</c><00:03:43.159><c> this</c><00:03:43.519><c> you</c><00:03:43.640><c> know</c>

00:03:44.030 --> 00:03:44.040 align:start position:0%
have enough skills to use this you know
 

00:03:44.040 --> 00:03:47.470 align:start position:0%
have enough skills to use this you know
access<00:03:44.360><c> to</c><00:03:44.480><c> the</c><00:03:44.799><c> database</c><00:03:45.799><c> uh</c><00:03:46.599><c> same</c><00:03:46.840><c> as</c><00:03:47.200><c> it</c><00:03:47.400><c> it</c>

00:03:47.470 --> 00:03:47.480 align:start position:0%
access to the database uh same as it it
 

00:03:47.480 --> 00:03:50.070 align:start position:0%
access to the database uh same as it it
will<00:03:47.840><c> be</c><00:03:48.159><c> really</c><00:03:48.519><c> time</c><00:03:48.879><c> consuming</c><00:03:49.519><c> for</c><00:03:49.840><c> for</c>

00:03:50.070 --> 00:03:50.080 align:start position:0%
will be really time consuming for for
 

00:03:50.080 --> 00:03:52.990 align:start position:0%
will be really time consuming for for
them<00:03:50.400><c> to</c><00:03:50.599><c> retrieve</c><00:03:51.120><c> data</c><00:03:51.640><c> even</c><00:03:51.920><c> if</c><00:03:52.159><c> they</c><00:03:52.319><c> can</c>

00:03:52.990 --> 00:03:53.000 align:start position:0%
them to retrieve data even if they can
 

00:03:53.000 --> 00:03:55.630 align:start position:0%
them to retrieve data even if they can
have<00:03:53.280><c> basic</c><00:03:53.760><c> SQL</c><00:03:54.439><c> nevertheless</c><00:03:55.239><c> they</c><00:03:55.439><c> they</c>

00:03:55.630 --> 00:03:55.640 align:start position:0%
have basic SQL nevertheless they they
 

00:03:55.640 --> 00:03:57.030 align:start position:0%
have basic SQL nevertheless they they
have<00:03:55.799><c> to</c>

00:03:57.030 --> 00:03:57.040 align:start position:0%
have to
 

00:03:57.040 --> 00:04:00.350 align:start position:0%
have to
execute<00:03:58.040><c> then</c><00:03:58.720><c> um</c><00:03:58.959><c> copy</c><00:03:59.280><c> all</c><00:03:59.439><c> the</c><00:03:59.799><c> data</c><00:04:00.040><c> to</c><00:04:00.159><c> the</c>

00:04:00.350 --> 00:04:00.360 align:start position:0%
execute then um copy all the data to the
 

00:04:00.360 --> 00:04:03.069 align:start position:0%
execute then um copy all the data to the
Excel<00:04:00.720><c> spreadsheet</c><00:04:01.360><c> and</c><00:04:01.560><c> then</c><00:04:02.000><c> start</c><00:04:02.640><c> doing</c>

00:04:03.069 --> 00:04:03.079 align:start position:0%
Excel spreadsheet and then start doing
 

00:04:03.079 --> 00:04:05.830 align:start position:0%
Excel spreadsheet and then start doing
data<00:04:03.360><c> analytics</c><00:04:03.959><c> within</c><00:04:04.200><c> the</c><00:04:04.519><c> Excel</c><00:04:05.519><c> which</c>

00:04:05.830 --> 00:04:05.840 align:start position:0%
data analytics within the Excel which
 

00:04:05.840 --> 00:04:08.710 align:start position:0%
data analytics within the Excel which
for<00:04:06.200><c> the</c><00:04:06.560><c> you</c><00:04:06.680><c> know</c><00:04:07.239><c> non-technical</c><00:04:08.239><c> employees</c>

00:04:08.710 --> 00:04:08.720 align:start position:0%
for the you know non-technical employees
 

00:04:08.720 --> 00:04:12.350 align:start position:0%
for the you know non-technical employees
is<00:04:08.879><c> not</c><00:04:09.159><c> you</c><00:04:09.280><c> know</c><00:04:09.599><c> super</c><00:04:10.599><c> um</c><00:04:11.560><c> convenient</c><00:04:12.200><c> to</c>

00:04:12.350 --> 00:04:12.360 align:start position:0%
is not you know super um convenient to
 

00:04:12.360 --> 00:04:18.390 align:start position:0%
is not you know super um convenient to
do<00:04:12.640><c> and</c><00:04:13.159><c> super</c><00:04:13.480><c> fast</c><00:04:14.480><c> um</c><00:04:15.319><c> also</c><00:04:16.239><c> um</c><00:04:17.079><c> yeah</c><00:04:17.519><c> one</c>

00:04:18.390 --> 00:04:18.400 align:start position:0%
do and super fast um also um yeah one
 

00:04:18.400 --> 00:04:20.310 align:start position:0%
do and super fast um also um yeah one
one<00:04:18.600><c> part</c><00:04:18.840><c> of</c><00:04:19.160><c> our</c><00:04:19.440><c> challenge</c><00:04:19.919><c> this</c><00:04:20.040><c> is</c><00:04:20.160><c> a</c>

00:04:20.310 --> 00:04:20.320 align:start position:0%
one part of our challenge this is a
 

00:04:20.320 --> 00:04:23.430 align:start position:0%
one part of our challenge this is a
dirty<00:04:20.720><c> data</c><00:04:21.199><c> which</c><00:04:21.359><c> is</c><00:04:21.720><c> exist</c><00:04:22.479><c> already</c><00:04:22.919><c> in</c>

00:04:23.430 --> 00:04:23.440 align:start position:0%
dirty data which is exist already in
 

00:04:23.440 --> 00:04:24.629 align:start position:0%
dirty data which is exist already in
most<00:04:23.759><c> of</c>

00:04:24.629 --> 00:04:24.639 align:start position:0%
most of
 

00:04:24.639 --> 00:04:28.150 align:start position:0%
most of
in<00:04:25.639><c> electronic</c><00:04:26.199><c> Healthcare</c><00:04:26.639><c> record</c><00:04:27.160><c> systems</c>

00:04:28.150 --> 00:04:28.160 align:start position:0%
in electronic Healthcare record systems
 

00:04:28.160 --> 00:04:31.430 align:start position:0%
in electronic Healthcare record systems
and<00:04:28.720><c> sometimes</c><00:04:29.720><c> there</c><00:04:29.880><c> are</c><00:04:30.039><c> some</c><00:04:30.440><c> missing</c>

00:04:31.430 --> 00:04:31.440 align:start position:0%
and sometimes there are some missing
 

00:04:31.440 --> 00:04:35.469 align:start position:0%
and sometimes there are some missing
data<00:04:32.160><c> or</c><00:04:32.680><c> some</c><00:04:32.919><c> inaccurate</c><00:04:33.560><c> data</c><00:04:34.360><c> and</c><00:04:34.919><c> yeah</c><00:04:35.080><c> so</c>

00:04:35.469 --> 00:04:35.479 align:start position:0%
data or some inaccurate data and yeah so
 

00:04:35.479 --> 00:04:37.870 align:start position:0%
data or some inaccurate data and yeah so
our<00:04:35.800><c> system</c><00:04:36.280><c> is</c><00:04:36.479><c> also</c><00:04:36.759><c> knows</c><00:04:37.080><c> how</c><00:04:37.240><c> to</c><00:04:37.440><c> deal</c>

00:04:37.870 --> 00:04:37.880 align:start position:0%
our system is also knows how to deal
 

00:04:37.880 --> 00:04:39.629 align:start position:0%
our system is also knows how to deal
with<00:04:38.080><c> you</c><00:04:38.199><c> know</c><00:04:38.440><c> with</c><00:04:38.600><c> the</c><00:04:38.800><c> dirty</c><00:04:39.199><c> data</c><00:04:39.479><c> in</c>

00:04:39.629 --> 00:04:39.639 align:start position:0%
with you know with the dirty data in
 

00:04:39.639 --> 00:04:43.749 align:start position:0%
with you know with the dirty data in
your<00:04:39.880><c> data</c><00:04:40.800><c> bases</c><00:04:41.800><c> um</c><00:04:42.240><c> yeah</c><00:04:42.520><c> basically</c><00:04:43.520><c> so</c>

00:04:43.749 --> 00:04:43.759 align:start position:0%
your data bases um yeah basically so
 

00:04:43.759 --> 00:04:46.990 align:start position:0%
your data bases um yeah basically so
many<00:04:44.080><c> data</c><00:04:44.919><c> very</c><00:04:45.440><c> limited</c><00:04:45.960><c> amount</c><00:04:46.360><c> of</c><00:04:46.680><c> data</c>

00:04:46.990 --> 00:04:47.000 align:start position:0%
many data very limited amount of data
 

00:04:47.000 --> 00:04:50.790 align:start position:0%
many data very limited amount of data
skilled<00:04:47.360><c> employees</c><00:04:48.320><c> and</c><00:04:48.759><c> uh</c><00:04:49.680><c> a</c><00:04:49.840><c> lot</c><00:04:50.039><c> of</c><00:04:50.639><c> head</c>

00:04:50.790 --> 00:04:50.800 align:start position:0%
skilled employees and uh a lot of head
 

00:04:50.800 --> 00:04:52.749 align:start position:0%
skilled employees and uh a lot of head
of<00:04:51.080><c> departments</c><00:04:51.759><c> Physicians</c><00:04:52.280><c> who</c><00:04:52.400><c> are</c><00:04:52.520><c> not</c>

00:04:52.749 --> 00:04:52.759 align:start position:0%
of departments Physicians who are not
 

00:04:52.759 --> 00:04:55.990 align:start position:0%
of departments Physicians who are not
data<00:04:53.280><c> skilled</c><00:04:54.280><c> enough</c><00:04:54.560><c> in</c><00:04:54.680><c> order</c><00:04:55.320><c> to</c><00:04:55.560><c> mine</c>

00:04:55.990 --> 00:04:56.000 align:start position:0%
data skilled enough in order to mine
 

00:04:56.000 --> 00:04:58.950 align:start position:0%
data skilled enough in order to mine
those<00:04:56.240><c> data</c><00:04:56.520><c> and</c><00:04:56.759><c> St</c><00:04:57.639><c> insights</c><00:04:58.120><c> from</c><00:04:58.360><c> a</c><00:04:58.520><c> lot</c><00:04:58.680><c> of</c>

00:04:58.950 --> 00:04:58.960 align:start position:0%
those data and St insights from a lot of
 

00:04:58.960 --> 00:05:02.870 align:start position:0%
those data and St insights from a lot of
patient<00:04:59.840><c> cords</c><00:05:00.840><c> um</c><00:05:01.280><c> yeah</c><00:05:01.560><c> basically</c><00:05:02.440><c> what</c><00:05:02.600><c> is</c>

00:05:02.870 --> 00:05:02.880 align:start position:0%
patient cords um yeah basically what is
 

00:05:02.880 --> 00:05:07.189 align:start position:0%
patient cords um yeah basically what is
our<00:05:03.639><c> approach</c><00:05:04.639><c> our</c><00:05:05.000><c> approach</c><00:05:05.960><c> um</c><00:05:06.600><c> it</c><00:05:06.759><c> is</c>

00:05:07.189 --> 00:05:07.199 align:start position:0%
our approach our approach um it is
 

00:05:07.199 --> 00:05:12.590 align:start position:0%
our approach our approach um it is
hybrid<00:05:08.240><c> um</c><00:05:09.240><c> from</c><00:05:09.960><c> Vector</c><00:05:10.639><c> database</c><00:05:11.639><c> of</c><00:05:12.440><c> which</c>

00:05:12.590 --> 00:05:12.600 align:start position:0%
hybrid um from Vector database of which
 

00:05:12.600 --> 00:05:16.629 align:start position:0%
hybrid um from Vector database of which
is<00:05:13.479><c> including</c><00:05:14.479><c> um</c><00:05:15.160><c> which</c><00:05:15.360><c> is</c><00:05:15.520><c> including</c>

00:05:16.629 --> 00:05:16.639 align:start position:0%
is including um which is including
 

00:05:16.639 --> 00:05:20.990 align:start position:0%
is including um which is including
metadata<00:05:17.639><c> embeddings</c><00:05:18.520><c> with</c><00:05:18.919><c> other</c><00:05:19.520><c> model</c><00:05:20.520><c> um</c>

00:05:20.990 --> 00:05:21.000 align:start position:0%
metadata embeddings with other model um
 

00:05:21.000 --> 00:05:24.469 align:start position:0%
metadata embeddings with other model um
we<00:05:21.160><c> store</c><00:05:21.600><c> all</c><00:05:21.840><c> this</c><00:05:22.280><c> met</c><00:05:23.280><c> dat</c><00:05:23.960><c> so</c><00:05:24.160><c> for</c><00:05:24.360><c> the</c>

00:05:24.469 --> 00:05:24.479 align:start position:0%
we store all this met dat so for the
 

00:05:24.479 --> 00:05:28.790 align:start position:0%
we store all this met dat so for the
metadata<00:05:25.120><c> I</c><00:05:25.199><c> mean</c><00:05:26.080><c> like</c><00:05:27.039><c> diagnosis</c><00:05:27.720><c> names</c><00:05:28.520><c> uh</c>

00:05:28.790 --> 00:05:28.800 align:start position:0%
metadata I mean like diagnosis names uh
 

00:05:28.800 --> 00:05:32.189 align:start position:0%
metadata I mean like diagnosis names uh
prescription<00:05:29.400><c> name</c>

00:05:32.189 --> 00:05:32.199 align:start position:0%
 
 

00:05:32.199 --> 00:05:36.029 align:start position:0%
 
everything<00:05:33.199><c> all</c><00:05:33.919><c> basically</c><00:05:34.880><c> text</c><00:05:35.520><c> format</c>

00:05:36.029 --> 00:05:36.039 align:start position:0%
everything all basically text format
 

00:05:36.039 --> 00:05:39.430 align:start position:0%
everything all basically text format
information<00:05:36.800><c> from</c><00:05:37.520><c> the</c><00:05:37.919><c> healthcare</c><00:05:38.479><c> database</c>

00:05:39.430 --> 00:05:39.440 align:start position:0%
information from the healthcare database
 

00:05:39.440 --> 00:05:42.870 align:start position:0%
information from the healthcare database
which<00:05:39.639><c> we</c><00:05:39.960><c> can</c><00:05:40.960><c> store</c><00:05:41.400><c> on</c><00:05:41.600><c> our</c><00:05:41.880><c> database</c><00:05:42.520><c> and</c>

00:05:42.870 --> 00:05:42.880 align:start position:0%
which we can store on our database and
 

00:05:42.880 --> 00:05:45.110 align:start position:0%
which we can store on our database and
when<00:05:43.160><c> we</c><00:05:43.280><c> are</c><00:05:43.400><c> doing</c><00:05:43.639><c> natural</c><00:05:44.000><c> language</c>

00:05:45.110 --> 00:05:45.120 align:start position:0%
when we are doing natural language
 

00:05:45.120 --> 00:05:48.230 align:start position:0%
when we are doing natural language
understanding<00:05:46.120><c> we</c><00:05:46.560><c> trying</c><00:05:47.000><c> to</c><00:05:47.280><c> compare</c><00:05:48.039><c> what</c>

00:05:48.230 --> 00:05:48.240 align:start position:0%
understanding we trying to compare what
 

00:05:48.240 --> 00:05:51.790 align:start position:0%
understanding we trying to compare what
we<00:05:48.440><c> have</c><00:05:48.720><c> in</c><00:05:49.240><c> in</c><00:05:50.240><c> and</c><00:05:50.479><c> users</c><00:05:50.919><c> question</c>

00:05:51.790 --> 00:05:51.800 align:start position:0%
we have in in and users question
 

00:05:51.800 --> 00:05:54.870 align:start position:0%
we have in in and users question
comparing<00:05:52.400><c> with</c><00:05:52.560><c> the</c><00:05:53.280><c> cus</c><00:05:54.080><c> similarity</c><00:05:54.680><c> to</c>

00:05:54.870 --> 00:05:54.880 align:start position:0%
comparing with the cus similarity to
 

00:05:54.880 --> 00:05:57.309 align:start position:0%
comparing with the cus similarity to
what<00:05:55.000><c> we</c><00:05:55.160><c> have</c><00:05:55.319><c> in</c><00:05:55.440><c> our</c><00:05:55.919><c> database</c><00:05:56.400><c> so</c><00:05:56.560><c> in</c><00:05:56.720><c> case</c>

00:05:57.309 --> 00:05:57.319 align:start position:0%
what we have in our database so in case
 

00:05:57.319 --> 00:06:00.670 align:start position:0%
what we have in our database so in case
some<00:05:57.600><c> small</c><00:05:58.560><c> typos</c><00:05:59.400><c> or</c><00:05:59.639><c> or</c><00:05:59.960><c> some</c><00:06:00.319><c> you</c><00:06:00.440><c> know</c>

00:06:00.670 --> 00:06:00.680 align:start position:0%
some small typos or or some you know
 

00:06:00.680 --> 00:06:03.950 align:start position:0%
some small typos or or some you know
some<00:06:01.319><c> very</c><00:06:01.560><c> close</c><00:06:02.080><c> meaning</c><00:06:03.080><c> uh</c><00:06:03.440><c> we</c><00:06:03.560><c> can</c><00:06:03.840><c> you</c>

00:06:03.950 --> 00:06:03.960 align:start position:0%
some very close meaning uh we can you
 

00:06:03.960 --> 00:06:08.070 align:start position:0%
some very close meaning uh we can you
know<00:06:04.160><c> detect</c><00:06:05.039><c> those</c><00:06:06.039><c> um</c><00:06:06.639><c> values</c><00:06:07.199><c> from</c><00:06:07.400><c> the</c>

00:06:08.070 --> 00:06:08.080 align:start position:0%
know detect those um values from the
 

00:06:08.080 --> 00:06:10.629 align:start position:0%
know detect those um values from the
metadata<00:06:08.840><c> and</c><00:06:09.840><c> uh</c>

00:06:10.629 --> 00:06:10.639 align:start position:0%
metadata and uh
 

00:06:10.639 --> 00:06:13.870 align:start position:0%
metadata and uh
present<00:06:11.639><c> and</c><00:06:12.160><c> actually</c><00:06:12.800><c> understand</c><00:06:13.400><c> the</c><00:06:13.599><c> end</c>

00:06:13.870 --> 00:06:13.880 align:start position:0%
present and actually understand the end
 

00:06:13.880 --> 00:06:18.430 align:start position:0%
present and actually understand the end
users<00:06:14.840><c> uh</c><00:06:15.360><c> question</c><00:06:15.919><c> better</c><00:06:16.919><c> um</c><00:06:17.319><c> also</c><00:06:18.160><c> we</c><00:06:18.319><c> we</c>

00:06:18.430 --> 00:06:18.440 align:start position:0%
users uh question better um also we we
 

00:06:18.440 --> 00:06:22.029 align:start position:0%
users uh question better um also we we
are<00:06:18.560><c> using</c><00:06:18.960><c> such</c><00:06:19.720><c> quite</c><00:06:20.240><c> powerful</c><00:06:21.240><c> um</c><00:06:21.639><c> natural</c>

00:06:22.029 --> 00:06:22.039 align:start position:0%
are using such quite powerful um natural
 

00:06:22.039 --> 00:06:23.469 align:start position:0%
are using such quite powerful um natural
language

00:06:23.469 --> 00:06:23.479 align:start position:0%
language
 

00:06:23.479 --> 00:06:27.150 align:start position:0%
language
processing<00:06:24.479><c> um</c><00:06:25.400><c> methods</c><00:06:26.080><c> like</c><00:06:26.599><c> intent</c>

00:06:27.150 --> 00:06:27.160 align:start position:0%
processing um methods like intent
 

00:06:27.160 --> 00:06:30.350 align:start position:0%
processing um methods like intent
detection<00:06:27.759><c> and</c><00:06:27.919><c> Slot</c><00:06:28.199><c> fing</c><00:06:29.160><c> so</c>

00:06:30.350 --> 00:06:30.360 align:start position:0%
detection and Slot fing so
 

00:06:30.360 --> 00:06:32.550 align:start position:0%
detection and Slot fing so
intent<00:06:30.880><c> DET</c><00:06:31.199><c> for</c><00:06:31.360><c> intent</c><00:06:31.759><c> detection</c><00:06:32.199><c> we</c><00:06:32.319><c> are</c>

00:06:32.550 --> 00:06:32.560 align:start position:0%
intent DET for intent detection we are
 

00:06:32.560 --> 00:06:35.029 align:start position:0%
intent DET for intent detection we are
using<00:06:32.840><c> large</c><00:06:33.160><c> language</c><00:06:33.520><c> model</c><00:06:34.280><c> same</c><00:06:34.479><c> as</c><00:06:34.639><c> s</c>

00:06:35.029 --> 00:06:35.039 align:start position:0%
using large language model same as s
 

00:06:35.039 --> 00:06:39.029 align:start position:0%
using large language model same as s
feeling<00:06:35.520><c> fing</c><00:06:36.400><c> for</c><00:06:37.039><c> intent</c><00:06:37.479><c> detection</c>

00:06:39.029 --> 00:06:39.039 align:start position:0%
feeling fing for intent detection
 

00:06:39.039 --> 00:06:41.909 align:start position:0%
feeling fing for intent detection
um<00:06:40.039><c> you</c><00:06:40.240><c> you</c><00:06:40.400><c> probably</c><00:06:41.039><c> have</c><00:06:41.199><c> a</c><00:06:41.319><c> lot</c><00:06:41.520><c> of</c>

00:06:41.909 --> 00:06:41.919 align:start position:0%
um you you probably have a lot of
 

00:06:41.919 --> 00:06:44.550 align:start position:0%
um you you probably have a lot of
different<00:06:42.639><c> uh</c><00:06:42.800><c> intents</c><00:06:43.560><c> within</c><00:06:43.919><c> your</c><00:06:44.160><c> use</c>

00:06:44.550 --> 00:06:44.560 align:start position:0%
different uh intents within your use
 

00:06:44.560 --> 00:06:47.950 align:start position:0%
different uh intents within your use
case<00:06:45.120><c> on</c><00:06:45.319><c> top</c><00:06:45.560><c> of</c><00:06:45.800><c> the</c><00:06:45.960><c> patient</c><00:06:46.479><c> records</c><00:06:47.479><c> and</c>

00:06:47.950 --> 00:06:47.960 align:start position:0%
case on top of the patient records and
 

00:06:47.960 --> 00:06:52.550 align:start position:0%
case on top of the patient records and
uh<00:06:48.440><c> all</c><00:06:48.639><c> of</c><00:06:49.080><c> these</c><00:06:50.440><c> culations</c><00:06:51.440><c> we</c><00:06:51.840><c> in</c><00:06:52.080><c> our</c>

00:06:52.550 --> 00:06:52.560 align:start position:0%
uh all of these culations we in our
 

00:06:52.560 --> 00:06:55.790 align:start position:0%
uh all of these culations we in our
logic<00:06:53.240><c> we</c><00:06:53.400><c> call</c><00:06:53.680><c> it</c><00:06:54.080><c> intents</c><00:06:55.080><c> and</c><00:06:55.360><c> you</c><00:06:55.520><c> have</c><00:06:55.639><c> a</c>

00:06:55.790 --> 00:06:55.800 align:start position:0%
logic we call it intents and you have a
 

00:06:55.800 --> 00:06:57.749 align:start position:0%
logic we call it intents and you have a
list<00:06:56.080><c> of</c><00:06:56.280><c> these</c><00:06:56.479><c> intents</c><00:06:57.080><c> so</c><00:06:57.440><c> if</c><00:06:57.560><c> you're</c>

00:06:57.749 --> 00:06:57.759 align:start position:0%
list of these intents so if you're
 

00:06:57.759 --> 00:06:59.830 align:start position:0%
list of these intents so if you're
asking<00:06:58.120><c> about</c><00:06:58.400><c> who</c><00:06:58.520><c> is</c><00:06:58.639><c> Justin</c><00:06:59.039><c> Bieber</c><00:06:59.400><c> like</c>

00:06:59.830 --> 00:06:59.840 align:start position:0%
asking about who is Justin Bieber like
 

00:06:59.840 --> 00:07:02.990 align:start position:0%
asking about who is Justin Bieber like
Global<00:07:00.680><c> AI</c><00:07:01.000><c> knowledge</c><00:07:01.360><c> base</c><00:07:01.879><c> our</c><00:07:02.160><c> system</c><00:07:02.479><c> will</c>

00:07:02.990 --> 00:07:03.000 align:start position:0%
Global AI knowledge base our system will
 

00:07:03.000 --> 00:07:07.070 align:start position:0%
Global AI knowledge base our system will
not<00:07:04.000><c> help</c><00:07:04.800><c> the</c><00:07:04.919><c> end</c><00:07:05.160><c> users</c><00:07:05.479><c> to</c><00:07:05.680><c> ask</c><00:07:05.919><c> this</c>

00:07:07.070 --> 00:07:07.080 align:start position:0%
not help the end users to ask this
 

00:07:07.080 --> 00:07:09.990 align:start position:0%
not help the end users to ask this
question<00:07:08.080><c> of</c><00:07:08.240><c> course</c><00:07:08.599><c> if</c><00:07:09.039><c> Justin</c><00:07:09.400><c> Bieber</c><00:07:09.840><c> is</c>

00:07:09.990 --> 00:07:10.000 align:start position:0%
question of course if Justin Bieber is
 

00:07:10.000 --> 00:07:13.070 align:start position:0%
question of course if Justin Bieber is
not<00:07:10.160><c> a</c><00:07:10.400><c> patient</c><00:07:10.919><c> of</c><00:07:11.120><c> for</c><00:07:11.440><c> your</c><00:07:11.919><c> hospital</c><00:07:12.919><c> uh</c>

00:07:13.070 --> 00:07:13.080 align:start position:0%
not a patient of for your hospital uh
 

00:07:13.080 --> 00:07:16.070 align:start position:0%
not a patient of for your hospital uh
but<00:07:13.440><c> nevertheless</c><00:07:14.440><c> um</c><00:07:14.840><c> intent</c>

00:07:16.070 --> 00:07:16.080 align:start position:0%
but nevertheless um intent
 

00:07:16.080 --> 00:07:18.990 align:start position:0%
but nevertheless um intent
detection<00:07:17.080><c> we</c><00:07:17.400><c> we</c><00:07:17.639><c> find</c><00:07:17.960><c> tun</c><00:07:18.319><c> large</c><00:07:18.720><c> open</c>

00:07:18.990 --> 00:07:19.000 align:start position:0%
detection we we find tun large open
 

00:07:19.000 --> 00:07:22.309 align:start position:0%
detection we we find tun large open
large<00:07:19.280><c> language</c><00:07:19.639><c> model</c><00:07:20.039><c> in</c><00:07:20.199><c> order</c><00:07:20.560><c> to</c><00:07:21.319><c> detect</c>

00:07:22.309 --> 00:07:22.319 align:start position:0%
large language model in order to detect
 

00:07:22.319 --> 00:07:26.029 align:start position:0%
large language model in order to detect
um<00:07:23.120><c> intent</c><00:07:24.120><c> from</c><00:07:24.479><c> the</c><00:07:24.639><c> end</c><00:07:24.879><c> user</c><00:07:25.280><c> question</c>

00:07:26.029 --> 00:07:26.039 align:start position:0%
um intent from the end user question
 

00:07:26.039 --> 00:07:28.710 align:start position:0%
um intent from the end user question
because<00:07:26.360><c> end</c><00:07:26.599><c> users</c><00:07:26.960><c> can</c><00:07:27.280><c> ask</c><00:07:28.080><c> the</c><00:07:28.280><c> same</c><00:07:28.599><c> the</c>

00:07:28.710 --> 00:07:28.720 align:start position:0%
because end users can ask the same the
 

00:07:28.720 --> 00:07:31.390 align:start position:0%
because end users can ask the same the
same<00:07:29.039><c> question</c><00:07:29.560><c> in</c><00:07:29.759><c> in</c><00:07:29.919><c> a</c><00:07:30.080><c> lot</c><00:07:30.280><c> of</c><00:07:30.440><c> multiple</c>

00:07:31.390 --> 00:07:31.400 align:start position:0%
same question in in a lot of multiple
 

00:07:31.400 --> 00:07:36.150 align:start position:0%
same question in in a lot of multiple
thing<00:07:32.080><c> like</c><00:07:33.240><c> options</c><00:07:34.240><c> and</c><00:07:34.479><c> variants</c><00:07:35.360><c> so</c><00:07:36.000><c> it</c>

00:07:36.150 --> 00:07:36.160 align:start position:0%
thing like options and variants so it
 

00:07:36.160 --> 00:07:38.990 align:start position:0%
thing like options and variants so it
will<00:07:36.479><c> help</c><00:07:37.080><c> you</c><00:07:37.479><c> also</c><00:07:37.759><c> to</c><00:07:38.000><c> detect</c><00:07:38.479><c> the</c><00:07:38.680><c> proper</c>

00:07:38.990 --> 00:07:39.000 align:start position:0%
will help you also to detect the proper
 

00:07:39.000 --> 00:07:41.869 align:start position:0%
will help you also to detect the proper
intent<00:07:40.000><c> uh</c><00:07:40.160><c> and</c><00:07:40.440><c> get</c><00:07:40.919><c> you</c><00:07:41.000><c> know</c><00:07:41.199><c> the</c><00:07:41.400><c> proper</c>

00:07:41.869 --> 00:07:41.879 align:start position:0%
intent uh and get you know the proper
 

00:07:41.879 --> 00:07:45.110 align:start position:0%
intent uh and get you know the proper
calculation<00:07:42.879><c> uh</c><00:07:42.960><c> for</c><00:07:43.199><c> the</c><00:07:43.319><c> end</c><00:07:43.800><c> users</c><00:07:44.800><c> based</c>

00:07:45.110 --> 00:07:45.120 align:start position:0%
calculation uh for the end users based
 

00:07:45.120 --> 00:07:47.550 align:start position:0%
calculation uh for the end users based
on<00:07:45.479><c> their</c><00:07:45.680><c> natural</c><00:07:46.080><c> language</c><00:07:46.520><c> question</c><00:07:47.360><c> uh</c>

00:07:47.550 --> 00:07:47.560 align:start position:0%
on their natural language question uh
 

00:07:47.560 --> 00:07:50.230 align:start position:0%
on their natural language question uh
same<00:07:47.879><c> same</c><00:07:48.479><c> similar</c><00:07:48.960><c> for</c><00:07:49.159><c> the</c><00:07:49.280><c> slot</c><00:07:49.560><c> feeding</c>

00:07:50.230 --> 00:07:50.240 align:start position:0%
same same similar for the slot feeding
 

00:07:50.240 --> 00:07:54.309 align:start position:0%
same same similar for the slot feeding
so<00:07:50.599><c> as</c><00:07:50.800><c> far</c><00:07:51.159><c> as</c><00:07:51.479><c> as</c><00:07:51.680><c> we</c><00:07:51.840><c> all</c><00:07:52.199><c> know</c><00:07:53.199><c> um</c><00:07:53.560><c> most</c><00:07:53.840><c> of</c>

00:07:54.309 --> 00:07:54.319 align:start position:0%
so as far as as we all know um most of
 

00:07:54.319 --> 00:07:57.110 align:start position:0%
so as far as as we all know um most of
you<00:07:54.479><c> know</c><00:07:54.879><c> the</c><00:07:55.240><c> database</c><00:07:55.879><c> engines</c><00:07:56.720><c> they</c><00:07:56.919><c> do</c>

00:07:57.110 --> 00:07:57.120 align:start position:0%
you know the database engines they do
 

00:07:57.120 --> 00:07:59.629 align:start position:0%
you know the database engines they do
need<00:07:57.440><c> you</c><00:07:57.599><c> know</c><00:07:57.840><c> the</c><00:07:58.080><c> specific</c><00:07:58.560><c> data</c><00:07:58.879><c> format</c>

00:07:59.629 --> 00:07:59.639 align:start position:0%
need you know the specific data format
 

00:07:59.639 --> 00:08:01.990 align:start position:0%
need you know the specific data format
and<00:07:59.840><c> they</c><00:08:00.000><c> work</c><00:08:00.319><c> only</c><00:08:00.599><c> with</c><00:08:00.759><c> a</c><00:08:01.000><c> specific</c><00:08:01.680><c> data</c>

00:08:01.990 --> 00:08:02.000 align:start position:0%
and they work only with a specific data
 

00:08:02.000 --> 00:08:04.589 align:start position:0%
and they work only with a specific data
format<00:08:02.440><c> so</c><00:08:03.280><c> with</c><00:08:03.400><c> a</c><00:08:03.560><c> slot</c><00:08:03.840><c> filling</c><00:08:04.280><c> large</c>

00:08:04.589 --> 00:08:04.599 align:start position:0%
format so with a slot filling large
 

00:08:04.599 --> 00:08:08.390 align:start position:0%
format so with a slot filling large
language<00:08:05.000><c> model</c><00:08:05.879><c> helps</c><00:08:06.560><c> us</c><00:08:07.560><c> to</c><00:08:07.840><c> first</c><00:08:08.080><c> of</c><00:08:08.240><c> all</c>

00:08:08.390 --> 00:08:08.400 align:start position:0%
language model helps us to first of all
 

00:08:08.400 --> 00:08:11.990 align:start position:0%
language model helps us to first of all
to<00:08:08.599><c> detect</c><00:08:09.560><c> uh</c><00:08:09.720><c> let's</c><00:08:09.919><c> say</c><00:08:10.159><c> period</c><00:08:10.599><c> intent</c><00:08:11.560><c> and</c>

00:08:11.990 --> 00:08:12.000 align:start position:0%
to detect uh let's say period intent and
 

00:08:12.000 --> 00:08:14.469 align:start position:0%
to detect uh let's say period intent and
uh<00:08:12.240><c> transform</c><00:08:12.879><c> this</c><00:08:13.080><c> period</c><00:08:13.479><c> intent</c><00:08:13.919><c> into</c><00:08:14.280><c> the</c>

00:08:14.469 --> 00:08:14.479 align:start position:0%
uh transform this period intent into the
 

00:08:14.479 --> 00:08:17.749 align:start position:0%
uh transform this period intent into the
proper<00:08:14.840><c> format</c><00:08:15.720><c> which</c><00:08:15.879><c> will</c><00:08:16.080><c> be</c><00:08:16.360><c> suitable</c><00:08:17.000><c> for</c>

00:08:17.749 --> 00:08:17.759 align:start position:0%
proper format which will be suitable for
 

00:08:17.759 --> 00:08:20.350 align:start position:0%
proper format which will be suitable for
SQL<00:08:18.319><c> interface</c><00:08:18.840><c> on</c><00:08:19.039><c> top</c><00:08:19.199><c> of</c><00:08:19.319><c> your</c>

00:08:20.350 --> 00:08:20.360 align:start position:0%
SQL interface on top of your
 

00:08:20.360 --> 00:08:23.070 align:start position:0%
SQL interface on top of your
database<00:08:21.360><c> yeah</c><00:08:21.520><c> and</c><00:08:21.759><c> B</c><00:08:22.080><c> basically</c><00:08:22.680><c> we</c><00:08:22.840><c> we</c><00:08:22.960><c> are</c>

00:08:23.070 --> 00:08:23.080 align:start position:0%
database yeah and B basically we we are
 

00:08:23.080 --> 00:08:26.909 align:start position:0%
database yeah and B basically we we are
using<00:08:23.599><c> on</c><00:08:23.800><c> our</c><00:08:24.120><c> back</c><00:08:24.360><c> end</c><00:08:25.360><c> um</c><00:08:26.360><c> something</c>

00:08:26.909 --> 00:08:26.919 align:start position:0%
using on our back end um something
 

00:08:26.919 --> 00:08:29.950 align:start position:0%
using on our back end um something
called<00:08:27.319><c> the</c><00:08:28.120><c> uh</c><00:08:28.360><c> hybrid</c><00:08:28.800><c> system</c>

00:08:29.950 --> 00:08:29.960 align:start position:0%
called the uh hybrid system
 

00:08:29.960 --> 00:08:32.269 align:start position:0%
called the uh hybrid system
together<00:08:30.360><c> with</c><00:08:30.960><c> you</c><00:08:31.080><c> know</c><00:08:31.280><c> Vector</c><00:08:31.720><c> database</c>

00:08:32.269 --> 00:08:32.279 align:start position:0%
together with you know Vector database
 

00:08:32.279 --> 00:08:33.350 align:start position:0%
together with you know Vector database
as<00:08:32.440><c> well</c>

00:08:33.350 --> 00:08:33.360 align:start position:0%
as well
 

00:08:33.360 --> 00:08:38.070 align:start position:0%
as well
as<00:08:34.640><c> structured</c><00:08:35.640><c> dat</c><00:08:35.959><c> database</c><00:08:36.880><c> and</c><00:08:37.320><c> our</c><00:08:37.680><c> model</c>

00:08:38.070 --> 00:08:38.080 align:start position:0%
as structured dat database and our model
 

00:08:38.080 --> 00:08:40.269 align:start position:0%
as structured dat database and our model
works<00:08:38.519><c> you</c><00:08:38.680><c> know</c>

00:08:40.269 --> 00:08:40.279 align:start position:0%
works you know
 

00:08:40.279 --> 00:08:43.350 align:start position:0%
works you know
um<00:08:41.279><c> both</c><00:08:41.680><c> with</c><00:08:41.919><c> with</c><00:08:42.159><c> both</c><00:08:42.399><c> databases</c><00:08:43.039><c> in</c><00:08:43.240><c> the</c>

00:08:43.350 --> 00:08:43.360 align:start position:0%
um both with with both databases in the
 

00:08:43.360 --> 00:08:45.790 align:start position:0%
um both with with both databases in the
same<00:08:43.640><c> time</c><00:08:44.320><c> in</c><00:08:44.480><c> order</c><00:08:44.880><c> to</c>

00:08:45.790 --> 00:08:45.800 align:start position:0%
same time in order to
 

00:08:45.800 --> 00:08:48.990 align:start position:0%
same time in order to
transform<00:08:46.800><c> natural</c><00:08:47.399><c> language</c><00:08:48.080><c> question</c><00:08:48.839><c> to</c>

00:08:48.990 --> 00:08:49.000 align:start position:0%
transform natural language question to
 

00:08:49.000 --> 00:08:51.990 align:start position:0%
transform natural language question to
the<00:08:49.160><c> structure</c><00:08:49.560><c> query</c><00:08:49.880><c> language</c><00:08:50.399><c> code</c><00:08:51.399><c> um</c>

00:08:51.990 --> 00:08:52.000 align:start position:0%
the structure query language code um
 

00:08:52.000 --> 00:08:54.550 align:start position:0%
the structure query language code um
yeah<00:08:52.200><c> so</c><00:08:52.560><c> basically</c><00:08:53.120><c> that's</c><00:08:53.680><c> our</c><00:08:54.040><c> approach</c>

00:08:54.550 --> 00:08:54.560 align:start position:0%
yeah so basically that's our approach
 

00:08:54.560 --> 00:08:56.990 align:start position:0%
yeah so basically that's our approach
how<00:08:54.720><c> we</c><00:08:54.839><c> are</c><00:08:54.959><c> doing</c><00:08:55.320><c> it</c><00:08:55.440><c> is</c><00:08:55.680><c> very</c><00:08:56.040><c> hybrid</c><00:08:56.800><c> we're</c>

00:08:56.990 --> 00:08:57.000 align:start position:0%
how we are doing it is very hybrid we're
 

00:08:57.000 --> 00:09:00.630 align:start position:0%
how we are doing it is very hybrid we're
using<00:08:57.360><c> the</c><00:08:57.640><c> pro</c><00:08:58.040><c> the</c><00:08:58.839><c> specific</c>

00:09:00.630 --> 00:09:00.640 align:start position:0%
using the pro the specific
 

00:09:00.640 --> 00:09:03.509 align:start position:0%
using the pro the specific
you<00:09:00.800><c> know</c><00:09:01.640><c> specific</c><00:09:02.480><c> technology</c><00:09:03.079><c> to</c><00:09:03.240><c> solve</c>

00:09:03.509 --> 00:09:03.519 align:start position:0%
you know specific technology to solve
 

00:09:03.519 --> 00:09:06.269 align:start position:0%
you know specific technology to solve
the<00:09:03.760><c> specific</c><00:09:04.519><c> needs</c><00:09:05.240><c> on</c>

00:09:06.269 --> 00:09:06.279 align:start position:0%
the specific needs on
 

00:09:06.279 --> 00:09:10.710 align:start position:0%
the specific needs on
our<00:09:07.279><c> on</c><00:09:07.560><c> our</c><00:09:07.839><c> you</c><00:09:07.959><c> know</c><00:09:08.800><c> on</c><00:09:09.000><c> our</c><00:09:09.360><c> algorithm</c><00:09:10.200><c> in</c>

00:09:10.710 --> 00:09:10.720 align:start position:0%
our on our you know on our algorithm in
 

00:09:10.720 --> 00:09:13.750 align:start position:0%
our on our you know on our algorithm in
order<00:09:11.160><c> to</c><00:09:11.680><c> help</c><00:09:12.640><c> um</c><00:09:12.880><c> non-technical</c>

00:09:13.750 --> 00:09:13.760 align:start position:0%
order to help um non-technical
 

00:09:13.760 --> 00:09:16.230 align:start position:0%
order to help um non-technical
Healthcare<00:09:14.480><c> employees</c><00:09:15.120><c> to</c><00:09:15.399><c> have</c><00:09:15.640><c> intuitive</c>

00:09:16.230 --> 00:09:16.240 align:start position:0%
Healthcare employees to have intuitive
 

00:09:16.240 --> 00:09:19.949 align:start position:0%
Healthcare employees to have intuitive
AI<00:09:16.560><c> interface</c><00:09:17.040><c> to</c><00:09:17.360><c> the</c><00:09:17.959><c> databases</c><00:09:18.959><c> um</c>

00:09:19.949 --> 00:09:19.959 align:start position:0%
AI interface to the databases um
 

00:09:19.959 --> 00:09:24.389 align:start position:0%
AI interface to the databases um
basically<00:09:21.399><c> what</c><00:09:22.399><c> what</c><00:09:23.399><c> our</c><00:09:23.640><c> mission</c><00:09:24.000><c> is</c><00:09:24.120><c> to</c>

00:09:24.389 --> 00:09:24.399 align:start position:0%
basically what what our mission is to
 

00:09:24.399 --> 00:09:27.030 align:start position:0%
basically what what our mission is to
empower<00:09:24.920><c> them</c><00:09:25.279><c> with</c><00:09:25.519><c> this</c><00:09:26.000><c> you</c><00:09:26.160><c> know</c><00:09:26.640><c> chat</c>

00:09:27.030 --> 00:09:27.040 align:start position:0%
empower them with this you know chat
 

00:09:27.040 --> 00:09:30.509 align:start position:0%
empower them with this you know chat
interface<00:09:27.800><c> so</c><00:09:28.079><c> they</c><00:09:28.240><c> can</c><00:09:28.959><c> get</c>

00:09:30.509 --> 00:09:30.519 align:start position:0%
interface so they can get
 

00:09:30.519 --> 00:09:33.470 align:start position:0%
interface so they can get
health<00:09:31.279><c> patient</c><00:09:31.680><c> Healthcare</c><00:09:32.240><c> records</c><00:09:33.120><c> data</c>

00:09:33.470 --> 00:09:33.480 align:start position:0%
health patient Healthcare records data
 

00:09:33.480 --> 00:09:38.030 align:start position:0%
health patient Healthcare records data
analytics<00:09:34.079><c> much</c><00:09:34.560><c> faster</c><00:09:35.560><c> um</c><00:09:36.480><c> yeah</c><00:09:36.800><c> so</c><00:09:37.560><c> how</c><00:09:37.720><c> it</c>

00:09:38.030 --> 00:09:38.040 align:start position:0%
analytics much faster um yeah so how it
 

00:09:38.040 --> 00:09:43.030 align:start position:0%
analytics much faster um yeah so how it
basically<00:09:38.800><c> Works</c><00:09:40.160><c> um</c><00:09:41.160><c> NLS</c><00:09:41.640><c> scll</c><00:09:41.959><c> enhance</c><00:09:42.720><c> data</c>

00:09:43.030 --> 00:09:43.040 align:start position:0%
basically Works um NLS scll enhance data
 

00:09:43.040 --> 00:09:46.829 align:start position:0%
basically Works um NLS scll enhance data
analytics<00:09:44.000><c> accessibility</c><00:09:45.000><c> for</c><00:09:45.560><c> clinician</c><00:09:46.200><c> by</c>

00:09:46.829 --> 00:09:46.839 align:start position:0%
analytics accessibility for clinician by
 

00:09:46.839 --> 00:09:49.750 align:start position:0%
analytics accessibility for clinician by
providing<00:09:47.360><c> comprehensive</c><00:09:48.120><c> Birds</c><00:09:48.480><c> IV</c><00:09:49.320><c> on</c><00:09:49.519><c> top</c>

00:09:49.750 --> 00:09:49.760 align:start position:0%
providing comprehensive Birds IV on top
 

00:09:49.760 --> 00:09:53.069 align:start position:0%
providing comprehensive Birds IV on top
of<00:09:50.160><c> all</c><00:09:50.360><c> the</c><00:09:50.560><c> data</c><00:09:50.880><c> you</c><00:09:51.040><c> have</c><00:09:52.040><c> uh</c><00:09:52.480><c> those</c><00:09:52.880><c> b</c>

00:09:53.069 --> 00:09:53.079 align:start position:0%
of all the data you have uh those b
 

00:09:53.079 --> 00:09:56.910 align:start position:0%
of all the data you have uh those b
birds<00:09:54.000><c> iew</c><00:09:54.519><c> can</c><00:09:54.880><c> be</c><00:09:55.480><c> accessed</c><00:09:56.079><c> through</c>

00:09:56.910 --> 00:09:56.920 align:start position:0%
birds iew can be accessed through
 

00:09:56.920 --> 00:10:00.870 align:start position:0%
birds iew can be accessed through
reports<00:09:57.720><c> data</c><00:09:58.120><c> visualizations</c><00:09:59.600><c> or</c><00:10:00.480><c> just</c><00:10:00.760><c> you</c>

00:10:00.870 --> 00:10:00.880 align:start position:0%
reports data visualizations or just you
 

00:10:00.880 --> 00:10:02.750 align:start position:0%
reports data visualizations or just you
know<00:10:01.320><c> the</c><00:10:01.519><c> the</c><00:10:01.640><c> whole</c>

00:10:02.750 --> 00:10:02.760 align:start position:0%
know the the whole
 

00:10:02.760 --> 00:10:07.750 align:start position:0%
know the the whole
questions<00:10:03.760><c> Birds</c><00:10:04.120><c> I</c><00:10:04.399><c> VI</c><00:10:04.560><c> on</c><00:10:04.760><c> top</c><00:10:04.959><c> of</c><00:10:05.560><c> all</c><00:10:06.760><c> EHR</c>

00:10:07.750 --> 00:10:07.760 align:start position:0%
questions Birds I VI on top of all EHR
 

00:10:07.760 --> 00:10:11.430 align:start position:0%
questions Birds I VI on top of all EHR
Systems<00:10:08.320><c> records</c><00:10:09.079><c> it's</c><00:10:09.399><c> quite</c><00:10:10.000><c> important</c><00:10:11.000><c> um</c>

00:10:11.430 --> 00:10:11.440 align:start position:0%
Systems records it's quite important um
 

00:10:11.440 --> 00:10:15.389 align:start position:0%
Systems records it's quite important um
as<00:10:12.200><c> you</c><00:10:12.680><c> might</c><00:10:13.680><c> have</c><00:10:13.920><c> only</c><00:10:14.320><c> this</c><00:10:14.560><c> interface</c>

00:10:15.389 --> 00:10:15.399 align:start position:0%
as you might have only this interface
 

00:10:15.399 --> 00:10:18.470 align:start position:0%
as you might have only this interface
because<00:10:15.920><c> a</c><00:10:16.040><c> lot</c><00:10:16.200><c> of</c><00:10:16.720><c> questions</c><00:10:17.720><c> uh</c><00:10:17.920><c> ad</c><00:10:18.200><c> hoc</c>

00:10:18.470 --> 00:10:18.480 align:start position:0%
because a lot of questions uh ad hoc
 

00:10:18.480 --> 00:10:20.590 align:start position:0%
because a lot of questions uh ad hoc
questions<00:10:19.000><c> which</c><00:10:19.120><c> is</c><00:10:19.480><c> very</c><00:10:19.800><c> different</c><00:10:20.279><c> from</c>

00:10:20.590 --> 00:10:20.600 align:start position:0%
questions which is very different from
 

00:10:20.600 --> 00:10:24.069 align:start position:0%
questions which is very different from
one<00:10:20.800><c> user</c><00:10:21.120><c> to</c><00:10:21.399><c> another</c><00:10:22.360><c> one</c><00:10:23.360><c> from</c><00:10:23.560><c> one</c><00:10:23.760><c> use</c>

00:10:24.069 --> 00:10:24.079 align:start position:0%
one user to another one from one use
 

00:10:24.079 --> 00:10:26.550 align:start position:0%
one user to another one from one use
case<00:10:24.240><c> to</c><00:10:24.440><c> another</c><00:10:24.720><c> one</c><00:10:25.240><c> so</c><00:10:25.640><c> Bas</c><00:10:25.920><c> basically</c><00:10:26.399><c> you</c>

00:10:26.550 --> 00:10:26.560 align:start position:0%
case to another one so Bas basically you
 

00:10:26.560 --> 00:10:29.509 align:start position:0%
case to another one so Bas basically you
get<00:10:27.120><c> um</c><00:10:27.279><c> birs</c><00:10:27.640><c> iew</c><00:10:28.079><c> on</c><00:10:28.320><c> top</c><00:10:28.519><c> of</c><00:10:28.720><c> the</c><00:10:28.920><c> data</c><00:10:29.399><c> as</c>

00:10:29.509 --> 00:10:29.519 align:start position:0%
get um birs iew on top of the data as
 

00:10:29.519 --> 00:10:31.910 align:start position:0%
get um birs iew on top of the data as
well<00:10:29.720><c> as</c><00:10:29.880><c> you</c><00:10:30.000><c> can</c><00:10:30.240><c> ask</c><00:10:30.880><c> very</c><00:10:31.200><c> ad</c><00:10:31.480><c> hoc</c><00:10:31.680><c> and</c>

00:10:31.910 --> 00:10:31.920 align:start position:0%
well as you can ask very ad hoc and
 

00:10:31.920 --> 00:10:34.430 align:start position:0%
well as you can ask very ad hoc and
specific<00:10:32.399><c> questions</c><00:10:33.320><c> for</c><00:10:33.760><c> some</c><00:10:34.120><c> some</c>

00:10:34.430 --> 00:10:34.440 align:start position:0%
specific questions for some some
 

00:10:34.440 --> 00:10:38.430 align:start position:0%
specific questions for some some
specific<00:10:35.160><c> things</c><00:10:35.920><c> so</c><00:10:36.399><c> um</c><00:10:36.760><c> for</c><00:10:37.279><c> example</c><00:10:38.279><c> you</c>

00:10:38.430 --> 00:10:38.440 align:start position:0%
specific things so um for example you
 

00:10:38.440 --> 00:10:43.590 align:start position:0%
specific things so um for example you
can<00:10:38.639><c> build</c><00:10:38.959><c> a</c><00:10:39.800><c> scut</c><00:10:40.639><c> chart</c><00:10:41.639><c> uh</c><00:10:42.040><c> for</c><00:10:43.040><c> dting</c>

00:10:43.590 --> 00:10:43.600 align:start position:0%
can build a scut chart uh for dting
 

00:10:43.600 --> 00:10:47.150 align:start position:0%
can build a scut chart uh for dting
cases<00:10:44.000><c> by</c><00:10:44.240><c> age</c><00:10:44.920><c> let's</c><00:10:45.120><c> say</c><00:10:45.720><c> in</c><00:10:45.959><c> case</c><00:10:46.760><c> um</c>

00:10:47.150 --> 00:10:47.160 align:start position:0%
cases by age let's say in case um
 

00:10:47.160 --> 00:10:49.710 align:start position:0%
cases by age let's say in case um
anything<00:10:47.800><c> is</c>

00:10:49.710 --> 00:10:49.720 align:start position:0%
anything is
 

00:10:49.720 --> 00:10:52.310 align:start position:0%
anything is
not<00:10:50.720><c> clear</c><00:10:51.120><c> enough</c><00:10:51.480><c> the</c><00:10:51.639><c> system</c><00:10:51.920><c> will</c><00:10:52.079><c> ask</c>

00:10:52.310 --> 00:10:52.320 align:start position:0%
not clear enough the system will ask
 

00:10:52.320 --> 00:10:55.509 align:start position:0%
not clear enough the system will ask
additional<00:10:52.839><c> questions</c><00:10:53.760><c> this</c><00:10:53.920><c> is</c><00:10:54.240><c> one</c><00:10:54.440><c> of</c><00:10:55.360><c> the</c>

00:10:55.509 --> 00:10:55.519 align:start position:0%
additional questions this is one of the
 

00:10:55.519 --> 00:10:58.509 align:start position:0%
additional questions this is one of the
most<00:10:55.760><c> important</c><00:10:56.200><c> features</c><00:10:57.079><c> of</c><00:10:57.320><c> our</c><00:10:57.680><c> model</c>

00:10:58.509 --> 00:10:58.519 align:start position:0%
most important features of our model
 

00:10:58.519 --> 00:11:01.110 align:start position:0%
most important features of our model
that<00:10:58.920><c> our</c><00:10:59.320><c> model</c><00:11:00.000><c> are</c><00:11:00.240><c> able</c><00:11:00.560><c> to</c><00:11:00.839><c> ask</c>

00:11:01.110 --> 00:11:01.120 align:start position:0%
that our model are able to ask
 

00:11:01.120 --> 00:11:04.030 align:start position:0%
that our model are able to ask
additional<00:11:01.720><c> questions</c><00:11:02.519><c> in</c><00:11:02.760><c> case</c><00:11:03.040><c> of</c><00:11:03.320><c> any</c>

00:11:04.030 --> 00:11:04.040 align:start position:0%
additional questions in case of any
 

00:11:04.040 --> 00:11:07.150 align:start position:0%
additional questions in case of any
ambiguities<00:11:05.040><c> detected</c><00:11:05.639><c> in</c><00:11:06.440><c> end</c><00:11:06.720><c> users</c>

00:11:07.150 --> 00:11:07.160 align:start position:0%
ambiguities detected in end users
 

00:11:07.160 --> 00:11:10.590 align:start position:0%
ambiguities detected in end users
questions<00:11:08.040><c> in</c><00:11:08.240><c> that</c><00:11:08.519><c> case</c><00:11:09.000><c> of</c><00:11:09.200><c> course</c><00:11:10.160><c> um</c><00:11:10.480><c> it</c>

00:11:10.590 --> 00:11:10.600 align:start position:0%
questions in that case of course um it
 

00:11:10.600 --> 00:11:12.430 align:start position:0%
questions in that case of course um it
is<00:11:10.720><c> a</c><00:11:10.959><c> question</c><00:11:11.320><c> about</c><00:11:11.720><c> because</c><00:11:12.000><c> we</c><00:11:12.160><c> have</c>

00:11:12.430 --> 00:11:12.440 align:start position:0%
is a question about because we have
 

00:11:12.440 --> 00:11:15.829 align:start position:0%
is a question about because we have
daring<00:11:12.959><c> as</c><00:11:13.120><c> a</c><00:11:13.279><c> reader</c><00:11:13.760><c> as</c><00:11:14.200><c> a</c><00:11:14.440><c> patient</c><00:11:15.279><c> and</c><00:11:15.560><c> two</c>

00:11:15.829 --> 00:11:15.839 align:start position:0%
daring as a reader as a patient and two
 

00:11:15.839 --> 00:11:17.990 align:start position:0%
daring as a reader as a patient and two
different<00:11:16.600><c> uh</c><00:11:16.839><c> patients</c><00:11:17.279><c> with</c><00:11:17.480><c> different</c><00:11:17.839><c> I</c>

00:11:17.990 --> 00:11:18.000 align:start position:0%
different uh patients with different I
 

00:11:18.000 --> 00:11:21.150 align:start position:0%
different uh patients with different I
guess<00:11:18.519><c> surnames</c><00:11:19.519><c> um</c><00:11:20.000><c> yeah</c><00:11:20.200><c> basically</c><00:11:20.680><c> once</c>

00:11:21.150 --> 00:11:21.160 align:start position:0%
guess surnames um yeah basically once
 

00:11:21.160 --> 00:11:23.190 align:start position:0%
guess surnames um yeah basically once
all<00:11:21.480><c> these</c><00:11:21.680><c> additional</c>

00:11:23.190 --> 00:11:23.200 align:start position:0%
all these additional
 

00:11:23.200 --> 00:11:26.670 align:start position:0%
all these additional
questions<00:11:24.200><c> responded</c><00:11:24.920><c> by</c><00:11:25.079><c> the</c><00:11:25.200><c> end</c><00:11:25.440><c> users</c><00:11:26.240><c> so</c>

00:11:26.670 --> 00:11:26.680 align:start position:0%
questions responded by the end users so
 

00:11:26.680 --> 00:11:29.590 align:start position:0%
questions responded by the end users so
we<00:11:26.839><c> can</c><00:11:27.839><c> generate</c><00:11:28.320><c> a</c><00:11:28.560><c> proper</c><00:11:28.920><c> and</c><00:11:29.320><c> accurate</c>

00:11:29.590 --> 00:11:29.600 align:start position:0%
we can generate a proper and accurate
 

00:11:29.600 --> 00:11:31.150 align:start position:0%
we can generate a proper and accurate
structure<00:11:29.959><c> query</c><00:11:30.240><c> language</c><00:11:30.560><c> code</c><00:11:30.800><c> in</c><00:11:30.920><c> order</c>

00:11:31.150 --> 00:11:31.160 align:start position:0%
structure query language code in order
 

00:11:31.160 --> 00:11:33.670 align:start position:0%
structure query language code in order
to<00:11:31.320><c> build</c><00:11:31.680><c> either</c><00:11:31.920><c> data</c><00:11:32.360><c> visualization</c><00:11:33.360><c> or</c>

00:11:33.670 --> 00:11:33.680 align:start position:0%
to build either data visualization or
 

00:11:33.680 --> 00:11:38.509 align:start position:0%
to build either data visualization or
calculation<00:11:34.360><c> report</c><00:11:35.200><c> so</c><00:11:35.959><c> here</c><00:11:36.360><c> the</c><00:11:37.079><c> um</c><00:11:37.639><c> brief</c>

00:11:38.509 --> 00:11:38.519 align:start position:0%
calculation report so here the um brief
 

00:11:38.519 --> 00:11:42.190 align:start position:0%
calculation report so here the um brief
sample<00:11:39.079><c> of</c><00:11:39.920><c> surgery</c><00:11:40.600><c> cases</c><00:11:41.040><c> analytics</c><00:11:41.920><c> so</c><00:11:42.120><c> you</c>

00:11:42.190 --> 00:11:42.200 align:start position:0%
sample of surgery cases analytics so you
 

00:11:42.200 --> 00:11:44.350 align:start position:0%
sample of surgery cases analytics so you
can<00:11:42.399><c> ask</c><00:11:42.760><c> like</c><00:11:42.920><c> show</c><00:11:43.160><c> me</c><00:11:43.320><c> a</c><00:11:43.440><c> bar</c><00:11:43.720><c> chart</c><00:11:44.079><c> with</c><00:11:44.240><c> a</c>

00:11:44.350 --> 00:11:44.360 align:start position:0%
can ask like show me a bar chart with a
 

00:11:44.360 --> 00:11:48.509 align:start position:0%
can ask like show me a bar chart with a
number<00:11:44.720><c> of</c><00:11:44.959><c> cases</c><00:11:45.399><c> by</c><00:11:45.560><c> readers</c><00:11:46.560><c> this</c><00:11:47.120><c> year</c><00:11:48.120><c> and</c>

00:11:48.509 --> 00:11:48.519 align:start position:0%
number of cases by readers this year and
 

00:11:48.519 --> 00:11:50.829 align:start position:0%
number of cases by readers this year and
uh<00:11:48.760><c> basically</c><00:11:49.560><c> everything</c><00:11:49.959><c> is</c><00:11:50.160><c> clear</c><00:11:50.480><c> so</c>

00:11:50.829 --> 00:11:50.839 align:start position:0%
uh basically everything is clear so
 

00:11:50.839 --> 00:11:52.750 align:start position:0%
uh basically everything is clear so
system<00:11:51.519><c> understand</c><00:11:51.800><c> everything</c><00:11:52.160><c> and</c><00:11:52.360><c> can</c>

00:11:52.750 --> 00:11:52.760 align:start position:0%
system understand everything and can
 

00:11:52.760 --> 00:11:54.150 align:start position:0%
system understand everything and can
build<00:11:53.000><c> your</c><00:11:53.320><c> data</c>

00:11:54.150 --> 00:11:54.160 align:start position:0%
build your data
 

00:11:54.160 --> 00:11:55.910 align:start position:0%
build your data
visualization<00:11:55.160><c> uh</c>

00:11:55.910 --> 00:11:55.920 align:start position:0%
visualization uh
 

00:11:55.920 --> 00:12:00.750 align:start position:0%
visualization uh
for<00:11:56.920><c> some</c><00:11:57.360><c> cases</c><00:11:57.839><c> we</c><00:11:58.040><c> have</c><00:11:58.639><c> um</c><00:11:59.560><c> developed</c><00:12:00.560><c> uh</c>

00:12:00.750 --> 00:12:00.760 align:start position:0%
for some cases we have um developed uh
 

00:12:00.760 --> 00:12:03.829 align:start position:0%
for some cases we have um developed uh
surgery<00:12:01.279><c> duration</c><00:12:02.120><c> analytics</c><00:12:03.120><c> for</c><00:12:03.360><c> the</c>

00:12:03.829 --> 00:12:03.839 align:start position:0%
surgery duration analytics for the
 

00:12:03.839 --> 00:12:06.750 align:start position:0%
surgery duration analytics for the
surgery<00:12:04.320><c> duration</c><00:12:04.839><c> analytics</c><00:12:05.399><c> it's</c><00:12:06.399><c> quite</c>

00:12:06.750 --> 00:12:06.760 align:start position:0%
surgery duration analytics it's quite
 

00:12:06.760 --> 00:12:09.110 align:start position:0%
surgery duration analytics it's quite
very<00:12:06.959><c> important</c><00:12:07.639><c> especially</c><00:12:08.079><c> for</c><00:12:08.600><c> surgery</c>

00:12:09.110 --> 00:12:09.120 align:start position:0%
very important especially for surgery
 

00:12:09.120 --> 00:12:12.910 align:start position:0%
very important especially for surgery
clinics<00:12:09.519><c> in</c><00:12:09.639><c> order</c><00:12:09.920><c> to</c><00:12:10.959><c> understand</c><00:12:11.959><c> um</c><00:12:12.480><c> start</c>

00:12:12.910 --> 00:12:12.920 align:start position:0%
clinics in order to understand um start
 

00:12:12.920 --> 00:12:16.350 align:start position:0%
clinics in order to understand um start
date<00:12:13.279><c> end</c><00:12:13.639><c> date</c><00:12:14.079><c> average</c><00:12:14.839><c> uh</c><00:12:15.199><c> time</c><00:12:15.519><c> for</c><00:12:16.199><c> the</c>

00:12:16.350 --> 00:12:16.360 align:start position:0%
date end date average uh time for the
 

00:12:16.360 --> 00:12:20.750 align:start position:0%
date end date average uh time for the
surgery<00:12:16.959><c> per</c><00:12:17.480><c> surgeron</c><00:12:18.320><c> per</c><00:12:18.800><c> reader</c><00:12:19.560><c> per</c><00:12:20.279><c> um</c>

00:12:20.750 --> 00:12:20.760 align:start position:0%
surgery per surgeron per reader per um
 

00:12:20.760 --> 00:12:24.389 align:start position:0%
surgery per surgeron per reader per um
per<00:12:21.600><c> Hospital</c><00:12:22.240><c> whatever</c><00:12:23.079><c> and</c><00:12:23.880><c> you</c><00:12:24.000><c> can</c>

00:12:24.389 --> 00:12:24.399 align:start position:0%
per Hospital whatever and you can
 

00:12:24.399 --> 00:12:26.030 align:start position:0%
per Hospital whatever and you can
compare<00:12:24.760><c> and</c><00:12:25.000><c> Benchmark</c><00:12:25.600><c> all</c><00:12:25.800><c> these</c>

00:12:26.030 --> 00:12:26.040 align:start position:0%
compare and Benchmark all these
 

00:12:26.040 --> 00:12:29.470 align:start position:0%
compare and Benchmark all these
indicators<00:12:26.920><c> related</c><00:12:27.360><c> to</c><00:12:27.680><c> average</c><00:12:28.360><c> pro</c><00:12:28.720><c> time</c>

00:12:29.470 --> 00:12:29.480 align:start position:0%
indicators related to average pro time
 

00:12:29.480 --> 00:12:31.430 align:start position:0%
indicators related to average pro time
for<00:12:29.880><c> the</c><00:12:30.120><c> specific</c>

00:12:31.430 --> 00:12:31.440 align:start position:0%
for the specific
 

00:12:31.440 --> 00:12:35.189 align:start position:0%
for the specific
hospitals<00:12:32.440><c> surgeries</c><00:12:33.240><c> and</c><00:12:33.600><c> readers</c><00:12:34.600><c> and</c><00:12:34.880><c> that</c>

00:12:35.189 --> 00:12:35.199 align:start position:0%
hospitals surgeries and readers and that
 

00:12:35.199 --> 00:12:39.069 align:start position:0%
hospitals surgeries and readers and that
particular<00:12:35.920><c> case</c><00:12:37.000><c> um</c><00:12:38.000><c> this</c><00:12:38.120><c> is</c><00:12:38.399><c> example</c><00:12:38.800><c> how</c>

00:12:39.069 --> 00:12:39.079 align:start position:0%
particular case um this is example how
 

00:12:39.079 --> 00:12:41.230 align:start position:0%
particular case um this is example how
user<00:12:39.440><c> can</c><00:12:39.639><c> ask</c><00:12:39.880><c> what</c><00:12:40.000><c> are</c><00:12:40.240><c> the</c><00:12:40.440><c> top</c><00:12:40.800><c> six</c>

00:12:41.230 --> 00:12:41.240 align:start position:0%
user can ask what are the top six
 

00:12:41.240 --> 00:12:44.189 align:start position:0%
user can ask what are the top six
hospital<00:12:41.760><c> for</c><00:12:42.000><c> the</c><00:12:42.440><c> largest</c><00:12:43.320><c> average</c><00:12:43.680><c> reader</c>

00:12:44.189 --> 00:12:44.199 align:start position:0%
hospital for the largest average reader
 

00:12:44.199 --> 00:12:47.189 align:start position:0%
hospital for the largest average reader
time<00:12:44.440><c> for</c><00:12:44.639><c> the</c><00:12:44.800><c> spine</c><00:12:45.240><c> surgery</c><00:12:46.240><c> and</c><00:12:46.760><c> yes</c><00:12:47.000><c> the</c>

00:12:47.189 --> 00:12:47.199 align:start position:0%
time for the spine surgery and yes the
 

00:12:47.199 --> 00:12:49.110 align:start position:0%
time for the spine surgery and yes the
system<00:12:47.920><c> understand</c><00:12:48.199><c> everything</c><00:12:48.639><c> basically</c>

00:12:49.110 --> 00:12:49.120 align:start position:0%
system understand everything basically
 

00:12:49.120 --> 00:12:52.509 align:start position:0%
system understand everything basically
no<00:12:49.360><c> additional</c><00:12:49.880><c> question</c><00:12:50.600><c> at</c><00:12:50.800><c> this</c><00:12:51.040><c> time</c><00:12:51.760><c> and</c>

00:12:52.509 --> 00:12:52.519 align:start position:0%
no additional question at this time and
 

00:12:52.519 --> 00:12:54.670 align:start position:0%
no additional question at this time and
the<00:12:52.920><c> system</c><00:12:53.480><c> creates</c><00:12:53.920><c> structure</c><00:12:54.360><c> query</c>

00:12:54.670 --> 00:12:54.680 align:start position:0%
the system creates structure query
 

00:12:54.680 --> 00:12:57.550 align:start position:0%
the system creates structure query
language<00:12:55.079><c> code</c><00:12:55.480><c> which</c><00:12:55.760><c> which</c><00:12:56.120><c> sorts</c><00:12:57.120><c> based</c><00:12:57.399><c> on</c>

00:12:57.550 --> 00:12:57.560 align:start position:0%
language code which which sorts based on
 

00:12:57.560 --> 00:13:01.430 align:start position:0%
language code which which sorts based on
the<00:12:57.800><c> average</c><00:12:58.680><c> uh</c><00:12:59.240><c> Pro</c><00:12:59.600><c> time</c><00:13:00.079><c> and</c><00:13:00.320><c> in</c><00:13:00.560><c> minutes</c>

00:13:01.430 --> 00:13:01.440 align:start position:0%
the average uh Pro time and in minutes
 

00:13:01.440 --> 00:13:04.389 align:start position:0%
the average uh Pro time and in minutes
and<00:13:01.880><c> uh</c><00:13:02.079><c> just</c><00:13:02.360><c> you</c><00:13:02.519><c> know</c><00:13:02.680><c> limit</c><00:13:03.480><c> with</c><00:13:03.600><c> the</c><00:13:03.839><c> top</c>

00:13:04.389 --> 00:13:04.399 align:start position:0%
and uh just you know limit with the top
 

00:13:04.399 --> 00:13:08.189 align:start position:0%
and uh just you know limit with the top
six<00:13:05.399><c> um</c><00:13:06.040><c> hospitals</c><00:13:06.680><c> and</c><00:13:06.880><c> providing</c><00:13:07.680><c> the</c><00:13:07.880><c> small</c>

00:13:08.189 --> 00:13:08.199 align:start position:0%
six um hospitals and providing the small
 

00:13:08.199 --> 00:13:10.949 align:start position:0%
six um hospitals and providing the small
table<00:13:08.680><c> just</c><00:13:08.959><c> within</c><00:13:09.320><c> the</c><00:13:09.560><c> end</c><00:13:09.839><c> user</c><00:13:10.160><c> interface</c>

00:13:10.949 --> 00:13:10.959 align:start position:0%
table just within the end user interface
 

00:13:10.959 --> 00:13:15.910 align:start position:0%
table just within the end user interface
to<00:13:11.839><c> head</c><00:13:12.000><c> of</c><00:13:12.279><c> department</c><00:13:13.240><c> or</c><00:13:13.600><c> to</c><00:13:14.480><c> physician</c><00:13:15.480><c> um</c>

00:13:15.910 --> 00:13:15.920 align:start position:0%
to head of department or to physician um
 

00:13:15.920 --> 00:13:19.030 align:start position:0%
to head of department or to physician um
in<00:13:16.160><c> case</c><00:13:16.560><c> those</c><00:13:16.880><c> tables</c><00:13:17.480><c> is</c><00:13:17.800><c> quite</c><00:13:18.079><c> big</c><00:13:18.839><c> we</c>

00:13:19.030 --> 00:13:19.040 align:start position:0%
in case those tables is quite big we
 

00:13:19.040 --> 00:13:21.430 align:start position:0%
in case those tables is quite big we
have<00:13:19.440><c> like</c><00:13:20.079><c> kind</c><00:13:20.279><c> of</c>

00:13:21.430 --> 00:13:21.440 align:start position:0%
have like kind of
 

00:13:21.440 --> 00:13:25.509 align:start position:0%
have like kind of
limitation<00:13:22.440><c> more</c><00:13:22.760><c> than</c><00:13:23.000><c> let's</c><00:13:23.360><c> say</c><00:13:24.000><c> 10</c><00:13:24.519><c> rows</c>

00:13:25.509 --> 00:13:25.519 align:start position:0%
limitation more than let's say 10 rows
 

00:13:25.519 --> 00:13:28.189 align:start position:0%
limitation more than let's say 10 rows
uh<00:13:25.720><c> we</c><00:13:26.079><c> just</c><00:13:26.360><c> generate</c><00:13:26.839><c> an</c><00:13:27.199><c> Excel</c><00:13:27.680><c> file</c><00:13:28.040><c> and</c>

00:13:28.189 --> 00:13:28.199 align:start position:0%
uh we just generate an Excel file and
 

00:13:28.199 --> 00:13:31.509 align:start position:0%
uh we just generate an Excel file and
send<00:13:28.519><c> this</c><00:13:28.760><c> Excel</c><00:13:29.360><c> file</c><00:13:29.639><c> with</c><00:13:29.760><c> a</c><00:13:29.920><c> link</c><00:13:30.240><c> so</c><00:13:31.160><c> um</c>

00:13:31.509 --> 00:13:31.519 align:start position:0%
send this Excel file with a link so um
 

00:13:31.519 --> 00:13:33.750 align:start position:0%
send this Excel file with a link so um
the<00:13:31.839><c> end</c><00:13:32.120><c> user</c><00:13:32.560><c> can</c><00:13:32.839><c> you</c><00:13:32.959><c> know</c><00:13:33.240><c> can</c><00:13:33.480><c> ask</c>

00:13:33.750 --> 00:13:33.760 align:start position:0%
the end user can you know can ask
 

00:13:33.760 --> 00:13:36.710 align:start position:0%
the end user can you know can ask
question<00:13:34.160><c> like</c><00:13:34.360><c> show</c><00:13:34.600><c> me</c><00:13:34.880><c> average</c><00:13:35.639><c> T</c><00:13:35.959><c> time</c><00:13:36.480><c> per</c>

00:13:36.710 --> 00:13:36.720 align:start position:0%
question like show me average T time per
 

00:13:36.720 --> 00:13:40.150 align:start position:0%
question like show me average T time per
day<00:13:36.880><c> by</c><00:13:37.079><c> reader</c><00:13:37.519><c> this</c><00:13:37.720><c> week</c><00:13:38.600><c> so</c><00:13:39.560><c> obviously</c>

00:13:40.150 --> 00:13:40.160 align:start position:0%
day by reader this week so obviously
 

00:13:40.160 --> 00:13:42.069 align:start position:0%
day by reader this week so obviously
there<00:13:40.320><c> are</c><00:13:40.440><c> a</c><00:13:40.560><c> lot</c><00:13:40.760><c> of</c><00:13:40.880><c> readers</c><00:13:41.480><c> and</c><00:13:41.760><c> it</c><00:13:41.880><c> will</c>

00:13:42.069 --> 00:13:42.079 align:start position:0%
there are a lot of readers and it will
 

00:13:42.079 --> 00:13:44.710 align:start position:0%
there are a lot of readers and it will
be<00:13:42.320><c> you</c><00:13:42.440><c> know</c><00:13:42.639><c> more</c><00:13:42.880><c> than</c><00:13:43.040><c> 10</c><00:13:43.320><c> rows</c><00:13:44.160><c> it's</c><00:13:44.440><c> not</c>

00:13:44.710 --> 00:13:44.720 align:start position:0%
be you know more than 10 rows it's not
 

00:13:44.720 --> 00:13:48.030 align:start position:0%
be you know more than 10 rows it's not
so<00:13:45.120><c> easy</c><00:13:45.440><c> to</c><00:13:46.160><c> make</c><00:13:46.600><c> data</c><00:13:46.920><c> analytics</c><00:13:47.600><c> within</c>

00:13:48.030 --> 00:13:48.040 align:start position:0%
so easy to make data analytics within
 

00:13:48.040 --> 00:13:50.150 align:start position:0%
so easy to make data analytics within
you<00:13:48.160><c> know</c><00:13:48.440><c> the</c><00:13:49.079><c> everything</c><00:13:49.600><c> within</c><00:13:49.880><c> the</c>

00:13:50.150 --> 00:13:50.160 align:start position:0%
you know the everything within the
 

00:13:50.160 --> 00:13:52.430 align:start position:0%
you know the everything within the
single<00:13:50.519><c> chat</c><00:13:50.880><c> interface</c><00:13:51.839><c> however</c><00:13:52.079><c> you</c><00:13:52.199><c> can</c>

00:13:52.430 --> 00:13:52.440 align:start position:0%
single chat interface however you can
 

00:13:52.440 --> 00:13:55.670 align:start position:0%
single chat interface however you can
open<00:13:52.880><c> you</c><00:13:53.079><c> know</c><00:13:53.759><c> this</c><00:13:54.040><c> report</c><00:13:54.600><c> and</c><00:13:54.800><c> dig</c><00:13:55.120><c> deeper</c>

00:13:55.670 --> 00:13:55.680 align:start position:0%
open you know this report and dig deeper
 

00:13:55.680 --> 00:13:57.509 align:start position:0%
open you know this report and dig deeper
in<00:13:56.240><c> with</c><00:13:56.440><c> your</c>

00:13:57.509 --> 00:13:57.519 align:start position:0%
in with your
 

00:13:57.519 --> 00:14:01.110 align:start position:0%
in with your
computer<00:13:58.519><c> then</c><00:13:58.839><c> then</c><00:13:59.120><c> another</c><00:13:59.800><c> um</c><00:14:00.800><c> quite</c>

00:14:01.110 --> 00:14:01.120 align:start position:0%
computer then then another um quite
 

00:14:01.120 --> 00:14:05.189 align:start position:0%
computer then then another um quite
important<00:14:01.680><c> feature</c><00:14:02.440><c> which</c><00:14:02.880><c> we</c><00:14:03.920><c> detected</c><00:14:04.920><c> from</c>

00:14:05.189 --> 00:14:05.199 align:start position:0%
important feature which we detected from
 

00:14:05.199 --> 00:14:07.509 align:start position:0%
important feature which we detected from
our<00:14:05.480><c> research</c><00:14:05.839><c> and</c><00:14:06.279><c> development</c><00:14:06.920><c> project</c>

00:14:07.509 --> 00:14:07.519 align:start position:0%
our research and development project
 

00:14:07.519 --> 00:14:12.189 align:start position:0%
our research and development project
with<00:14:08.240><c> inovate</c><00:14:08.800><c> UK</c><00:14:09.800><c> um</c><00:14:10.040><c> this</c><00:14:10.160><c> is</c><00:14:10.360><c> the</c><00:14:11.279><c> kind</c><00:14:11.480><c> of</c>

00:14:12.189 --> 00:14:12.199 align:start position:0%
with inovate UK um this is the kind of
 

00:14:12.199 --> 00:14:14.829 align:start position:0%
with inovate UK um this is the kind of
um<00:14:12.560><c> information</c><00:14:13.279><c> which</c><00:14:13.399><c> is</c><00:14:13.959><c> badly</c><00:14:14.320><c> needed</c>

00:14:14.829 --> 00:14:14.839 align:start position:0%
um information which is badly needed
 

00:14:14.839 --> 00:14:16.189 align:start position:0%
um information which is badly needed
especially<00:14:15.320><c> for</c><00:14:15.519><c> the</c>

00:14:16.189 --> 00:14:16.199 align:start position:0%
especially for the
 

00:14:16.199 --> 00:14:19.509 align:start position:0%
especially for the
Physicians<00:14:17.199><c> um</c><00:14:17.720><c> this</c><00:14:17.880><c> is</c><00:14:18.240><c> kind</c><00:14:18.480><c> of</c><00:14:18.839><c> Downstream</c>

00:14:19.509 --> 00:14:19.519 align:start position:0%
Physicians um this is kind of Downstream
 

00:14:19.519 --> 00:14:21.870 align:start position:0%
Physicians um this is kind of Downstream
complication<00:14:20.360><c> calculation</c><00:14:20.920><c> the</c><00:14:21.079><c> most</c><00:14:21.320><c> common</c>

00:14:21.870 --> 00:14:21.880 align:start position:0%
complication calculation the most common
 

00:14:21.880 --> 00:14:23.749 align:start position:0%
complication calculation the most common
Downstream

00:14:23.749 --> 00:14:23.759 align:start position:0%
Downstream
 

00:14:23.759 --> 00:14:27.870 align:start position:0%
Downstream
complications<00:14:24.759><c> uh</c><00:14:25.399><c> calculation</c><00:14:26.480><c> for</c><00:14:27.480><c> the</c>

00:14:27.870 --> 00:14:27.880 align:start position:0%
complications uh calculation for the
 

00:14:27.880 --> 00:14:30.910 align:start position:0%
complications uh calculation for the
patients<00:14:28.399><c> with</c><00:14:28.720><c> multi</c><00:14:29.320><c> multiple</c><00:14:30.320><c> chronical</c>

00:14:30.910 --> 00:14:30.920 align:start position:0%
patients with multi multiple chronical
 

00:14:30.920 --> 00:14:33.350 align:start position:0%
patients with multi multiple chronical
diseases<00:14:31.600><c> but</c><00:14:31.800><c> basically</c><00:14:32.199><c> with</c><00:14:32.320><c> the</c><00:14:32.480><c> multiple</c>

00:14:33.350 --> 00:14:33.360 align:start position:0%
diseases but basically with the multiple
 

00:14:33.360 --> 00:14:36.550 align:start position:0%
diseases but basically with the multiple
conditions<00:14:34.360><c> um</c><00:14:34.600><c> so</c><00:14:35.160><c> it</c><00:14:35.320><c> can</c><00:14:35.519><c> be</c><00:14:35.800><c> easily</c><00:14:36.199><c> done</c>

00:14:36.550 --> 00:14:36.560 align:start position:0%
conditions um so it can be easily done
 

00:14:36.560 --> 00:14:39.269 align:start position:0%
conditions um so it can be easily done
on<00:14:36.759><c> top</c><00:14:36.959><c> of</c><00:14:37.199><c> intensive</c><00:14:37.680><c> care</c><00:14:37.920><c> unit</c><00:14:38.519><c> database</c>

00:14:39.269 --> 00:14:39.279 align:start position:0%
on top of intensive care unit database
 

00:14:39.279 --> 00:14:42.829 align:start position:0%
on top of intensive care unit database
with<00:14:39.480><c> our</c><00:14:40.240><c> with</c><00:14:40.600><c> nlq</c><00:14:41.519><c> and</c><00:14:41.720><c> natural</c><00:14:42.079><c> language</c>

00:14:42.829 --> 00:14:42.839 align:start position:0%
with our with nlq and natural language
 

00:14:42.839 --> 00:14:44.670 align:start position:0%
with our with nlq and natural language
query

00:14:44.670 --> 00:14:44.680 align:start position:0%
query
 

00:14:44.680 --> 00:14:47.470 align:start position:0%
query
capabilities<00:14:45.680><c> uh</c><00:14:45.880><c> when</c><00:14:46.360><c> and</c><00:14:46.680><c> you</c><00:14:47.120><c> like</c>

00:14:47.470 --> 00:14:47.480 align:start position:0%
capabilities uh when and you like
 

00:14:47.480 --> 00:14:51.110 align:start position:0%
capabilities uh when and you like
Physicians<00:14:48.480><c> can</c><00:14:48.800><c> ask</c><00:14:49.040><c> a</c><00:14:49.240><c> question</c><00:14:49.880><c> like</c><00:14:50.880><c> just</c>

00:14:51.110 --> 00:14:51.120 align:start position:0%
Physicians can ask a question like just
 

00:14:51.120 --> 00:14:53.509 align:start position:0%
Physicians can ask a question like just
one<00:14:51.320><c> moment</c><00:14:51.759><c> I</c><00:14:51.880><c> will</c><00:14:52.279><c> show</c><00:14:52.560><c> you</c><00:14:52.880><c> just</c><00:14:53.199><c> once</c>

00:14:53.509 --> 00:14:53.519 align:start position:0%
one moment I will show you just once
 

00:14:53.519 --> 00:14:56.069 align:start position:0%
one moment I will show you just once
again<00:14:53.880><c> so</c><00:14:54.320><c> the</c><00:14:54.519><c> question</c><00:14:54.880><c> was</c><00:14:55.399><c> like</c><00:14:55.720><c> what</c><00:14:55.880><c> is</c>

00:14:56.069 --> 00:14:56.079 align:start position:0%
again so the question was like what is
 

00:14:56.079 --> 00:14:58.550 align:start position:0%
again so the question was like what is
the<00:14:57.079><c> most</c><00:14:57.399><c> common</c><00:14:57.680><c> seven</c><00:14:58.000><c> Downstream</c>

00:14:58.550 --> 00:14:58.560 align:start position:0%
the most common seven Downstream
 

00:14:58.560 --> 00:15:01.749 align:start position:0%
the most common seven Downstream
complication<00:14:59.040><c> Pati</c><00:14:59.399><c> for</c><00:14:59.560><c> the</c><00:14:59.759><c> male</c><00:15:00.759><c> patients</c>

00:15:01.749 --> 00:15:01.759 align:start position:0%
complication Pati for the male patients
 

00:15:01.759 --> 00:15:03.870 align:start position:0%
complication Pati for the male patients
with<00:15:01.959><c> pom</c>

00:15:03.870 --> 00:15:03.880 align:start position:0%
with pom
 

00:15:03.880 --> 00:15:07.230 align:start position:0%
with pom
Monas<00:15:04.880><c> NOA</c><00:15:05.720><c> so</c><00:15:06.079><c> B</c><00:15:06.320><c> basically</c><00:15:06.839><c> yeah</c><00:15:07.000><c> there</c><00:15:07.120><c> are</c>

00:15:07.230 --> 00:15:07.240 align:start position:0%
Monas NOA so B basically yeah there are
 

00:15:07.240 --> 00:15:10.389 align:start position:0%
Monas NOA so B basically yeah there are
a<00:15:07.360><c> lot</c><00:15:07.519><c> of</c><00:15:07.680><c> options</c><00:15:08.440><c> so</c><00:15:09.440><c> a</c><00:15:09.600><c> couple</c><00:15:09.920><c> more</c>

00:15:10.389 --> 00:15:10.399 align:start position:0%
a lot of options so a couple more
 

00:15:10.399 --> 00:15:14.990 align:start position:0%
a lot of options so a couple more
additional<00:15:11.360><c> questions</c><00:15:12.360><c> um</c><00:15:13.000><c> yeah</c><00:15:14.000><c> because</c>

00:15:14.990 --> 00:15:15.000 align:start position:0%
additional questions um yeah because
 

00:15:15.000 --> 00:15:17.829 align:start position:0%
additional questions um yeah because
medical<00:15:15.399><c> terminology</c><00:15:16.120><c> is</c><00:15:16.279><c> so</c><00:15:16.720><c> you</c><00:15:16.839><c> know</c><00:15:17.120><c> is</c><00:15:17.440><c> so</c>

00:15:17.829 --> 00:15:17.839 align:start position:0%
medical terminology is so you know is so
 

00:15:17.839 --> 00:15:21.150 align:start position:0%
medical terminology is so you know is so
complex<00:15:18.839><c> as</c><00:15:19.240><c> the</c><00:15:19.440><c> same</c>

00:15:21.150 --> 00:15:21.160 align:start position:0%
complex as the same
 

00:15:21.160 --> 00:15:25.949 align:start position:0%
complex as the same
um<00:15:22.160><c> the</c><00:15:22.519><c> same</c><00:15:23.519><c> um</c><00:15:24.040><c> word</c><00:15:24.639><c> can</c><00:15:25.240><c> have</c><00:15:25.560><c> different</c>

00:15:25.949 --> 00:15:25.959 align:start position:0%
um the same um word can have different
 

00:15:25.959 --> 00:15:27.990 align:start position:0%
um the same um word can have different
meanings<00:15:26.839><c> in</c><00:15:27.120><c> different</c><00:15:27.560><c> you</c><00:15:27.680><c> know</c>

00:15:27.990 --> 00:15:28.000 align:start position:0%
meanings in different you know
 

00:15:28.000 --> 00:15:30.590 align:start position:0%
meanings in different you know
departments<00:15:28.680><c> let's</c><00:15:29.079><c> say</c><00:15:29.440><c> and</c><00:15:29.560><c> so</c><00:15:29.839><c> every</c><00:15:30.120><c> time</c>

00:15:30.590 --> 00:15:30.600 align:start position:0%
departments let's say and so every time
 

00:15:30.600 --> 00:15:32.550 align:start position:0%
departments let's say and so every time
the<00:15:30.800><c> system</c><00:15:31.160><c> keeps</c><00:15:31.680><c> asking</c><00:15:32.040><c> additional</c>

00:15:32.550 --> 00:15:32.560 align:start position:0%
the system keeps asking additional
 

00:15:32.560 --> 00:15:35.389 align:start position:0%
the system keeps asking additional
questions<00:15:32.920><c> is</c><00:15:33.120><c> something</c><00:15:33.920><c> is</c><00:15:34.079><c> not</c><00:15:34.319><c> clear</c><00:15:35.040><c> so</c>

00:15:35.389 --> 00:15:35.399 align:start position:0%
questions is something is not clear so
 

00:15:35.399 --> 00:15:38.870 align:start position:0%
questions is something is not clear so
based<00:15:35.759><c> on</c><00:15:36.759><c> the</c><00:15:37.000><c> those</c><00:15:37.279><c> question</c><00:15:37.839><c> so</c><00:15:38.519><c> but</c><00:15:38.720><c> you</c>

00:15:38.870 --> 00:15:38.880 align:start position:0%
based on the those question so but you
 

00:15:38.880 --> 00:15:40.749 align:start position:0%
based on the those question so but you
you<00:15:38.959><c> are</c><00:15:39.079><c> not</c><00:15:39.240><c> limited</c><00:15:39.680><c> only</c><00:15:40.160><c> like</c><00:15:40.480><c> with</c><00:15:40.600><c> a</c>

00:15:40.749 --> 00:15:40.759 align:start position:0%
you are not limited only like with a
 

00:15:40.759 --> 00:15:44.829 align:start position:0%
you are not limited only like with a
male<00:15:41.480><c> female</c><00:15:42.480><c> uh</c><00:15:42.720><c> bacteria</c><00:15:43.519><c> infection</c><00:15:44.519><c> uh</c><00:15:44.639><c> you</c>

00:15:44.829 --> 00:15:44.839 align:start position:0%
male female uh bacteria infection uh you
 

00:15:44.839 --> 00:15:49.749 align:start position:0%
male female uh bacteria infection uh you
can<00:15:45.839><c> use</c><00:15:46.800><c> um</c><00:15:47.120><c> you</c><00:15:47.279><c> know</c><00:15:47.600><c> any</c><00:15:48.000><c> period</c><00:15:49.000><c> of</c><00:15:49.199><c> the</c>

00:15:49.749 --> 00:15:49.759 align:start position:0%
can use um you know any period of the
 

00:15:49.759 --> 00:15:53.030 align:start position:0%
can use um you know any period of the
stay<00:15:50.120><c> in</c><00:15:50.440><c> intensive</c><00:15:51.000><c> care</c><00:15:51.920><c> unit</c><00:15:52.279><c> in</c><00:15:52.480><c> emergency</c>

00:15:53.030 --> 00:15:53.040 align:start position:0%
stay in intensive care unit in emergency
 

00:15:53.040 --> 00:15:56.509 align:start position:0%
stay in intensive care unit in emergency
room<00:15:53.440><c> admit</c><00:15:54.440><c> uh</c><00:15:54.959><c> you</c><00:15:55.079><c> can</c><00:15:55.279><c> use</c><00:15:55.639><c> different</c>

00:15:56.509 --> 00:15:56.519 align:start position:0%
room admit uh you can use different
 

00:15:56.519 --> 00:15:59.030 align:start position:0%
room admit uh you can use different
prescriptions<00:15:57.519><c> which</c><00:15:58.040><c> which</c><00:15:58.319><c> those</c><00:15:58.600><c> patients</c>

00:15:59.030 --> 00:15:59.040 align:start position:0%
prescriptions which which those patients
 

00:15:59.040 --> 00:16:02.829 align:start position:0%
prescriptions which which those patients
has<00:15:59.519><c> and</c><00:15:59.680><c> what</c><00:15:59.800><c> is</c><00:15:59.920><c> the</c><00:16:00.319><c> main</c><00:16:00.639><c> idea</c><00:16:01.120><c> to</c><00:16:01.839><c> culate</c>

00:16:02.829 --> 00:16:02.839 align:start position:0%
has and what is the main idea to culate
 

00:16:02.839 --> 00:16:06.350 align:start position:0%
has and what is the main idea to culate
based<00:16:03.120><c> on</c><00:16:03.279><c> your</c><00:16:03.920><c> patient</c><00:16:04.920><c> uh</c><00:16:05.160><c> history</c><00:16:05.880><c> B</c><00:16:06.120><c> based</c>

00:16:06.350 --> 00:16:06.360 align:start position:0%
based on your patient uh history B based
 

00:16:06.360 --> 00:16:09.670 align:start position:0%
based on your patient uh history B based
on<00:16:06.519><c> the</c><00:16:06.880><c> hospital</c><00:16:07.399><c> patient</c><00:16:08.079><c> history</c><00:16:09.079><c> um</c>

00:16:09.670 --> 00:16:09.680 align:start position:0%
on the hospital patient history um
 

00:16:09.680 --> 00:16:12.670 align:start position:0%
on the hospital patient history um
calculate<00:16:10.199><c> the</c><00:16:10.639><c> common</c><00:16:11.639><c> uh</c><00:16:12.000><c> the</c><00:16:12.199><c> common</c>

00:16:12.670 --> 00:16:12.680 align:start position:0%
calculate the common uh the common
 

00:16:12.680 --> 00:16:15.430 align:start position:0%
calculate the common uh the common
patients<00:16:13.240><c> with</c><00:16:13.399><c> the</c><00:16:13.839><c> same</c><00:16:14.440><c> conditions</c><00:16:15.000><c> in</c><00:16:15.199><c> the</c>

00:16:15.430 --> 00:16:15.440 align:start position:0%
patients with the same conditions in the
 

00:16:15.440 --> 00:16:17.749 align:start position:0%
patients with the same conditions in the
past<00:16:16.160><c> and</c><00:16:16.519><c> just</c><00:16:16.920><c> to</c>

00:16:17.749 --> 00:16:17.759 align:start position:0%
past and just to
 

00:16:17.759 --> 00:16:20.350 align:start position:0%
past and just to
provide<00:16:18.759><c> valuable</c><00:16:19.279><c> information</c><00:16:19.839><c> to</c><00:16:20.040><c> the</c>

00:16:20.350 --> 00:16:20.360 align:start position:0%
provide valuable information to the
 

00:16:20.360 --> 00:16:24.990 align:start position:0%
provide valuable information to the
Physicians<00:16:21.360><c> about</c><00:16:21.800><c> the</c><00:16:22.440><c> top</c><00:16:22.680><c> seven</c><00:16:23.360><c> downam</c>

00:16:24.990 --> 00:16:25.000 align:start position:0%
Physicians about the top seven downam
 

00:16:25.000 --> 00:16:27.230 align:start position:0%
Physicians about the top seven downam
complications<00:16:26.000><c> uh</c><00:16:26.160><c> for</c><00:16:26.440><c> these</c><00:16:26.720><c> specific</c>

00:16:27.230 --> 00:16:27.240 align:start position:0%
complications uh for these specific
 

00:16:27.240 --> 00:16:29.829 align:start position:0%
complications uh for these specific
patient<00:16:27.720><c> they</c><00:16:27.880><c> they</c><00:16:28.040><c> treat</c><00:16:28.399><c> just</c><00:16:28.680><c> right</c><00:16:29.079><c> now</c>

00:16:29.829 --> 00:16:29.839 align:start position:0%
patient they they treat just right now
 

00:16:29.839 --> 00:16:32.470 align:start position:0%
patient they they treat just right now
this<00:16:30.040><c> information</c><00:16:30.600><c> can</c><00:16:30.920><c> help</c><00:16:31.480><c> Physicians</c>

00:16:32.470 --> 00:16:32.480 align:start position:0%
this information can help Physicians
 

00:16:32.480 --> 00:16:35.389 align:start position:0%
this information can help Physicians
either<00:16:32.800><c> to</c><00:16:33.160><c> avoid</c><00:16:33.959><c> or</c><00:16:34.240><c> mitigate</c><00:16:34.839><c> potential</c>

00:16:35.389 --> 00:16:35.399 align:start position:0%
either to avoid or mitigate potential
 

00:16:35.399 --> 00:16:38.509 align:start position:0%
either to avoid or mitigate potential
Downstream<00:16:36.120><c> complication</c><00:16:37.120><c> so</c><00:16:37.519><c> for</c><00:16:37.759><c> example</c>

00:16:38.509 --> 00:16:38.519 align:start position:0%
Downstream complication so for example
 

00:16:38.519 --> 00:16:42.110 align:start position:0%
Downstream complication so for example
those<00:16:38.880><c> patient</c><00:16:39.519><c> has</c><00:16:40.279><c> some</c><00:16:40.560><c> kind</c><00:16:40.720><c> of</c><00:16:41.120><c> surgery</c>

00:16:42.110 --> 00:16:42.120 align:start position:0%
those patient has some kind of surgery
 

00:16:42.120 --> 00:16:45.910 align:start position:0%
those patient has some kind of surgery
and<00:16:42.639><c> uh</c><00:16:43.040><c> he</c><00:16:43.399><c> he</c><00:16:43.639><c> he</c><00:16:43.800><c> stays</c><00:16:44.240><c> or</c><00:16:44.440><c> he</c><00:16:44.600><c> or</c><00:16:44.800><c> she</c><00:16:45.079><c> stays</c>

00:16:45.910 --> 00:16:45.920 align:start position:0%
and uh he he he stays or he or she stays
 

00:16:45.920 --> 00:16:48.870 align:start position:0%
and uh he he he stays or he or she stays
in<00:16:46.240><c> the</c><00:16:46.440><c> hospital</c><00:16:46.880><c> for</c><00:16:47.079><c> a</c><00:16:47.279><c> two</c><00:16:47.480><c> weeks</c><00:16:48.319><c> and</c>

00:16:48.870 --> 00:16:48.880 align:start position:0%
in the hospital for a two weeks and
 

00:16:48.880 --> 00:16:51.629 align:start position:0%
in the hospital for a two weeks and
based<00:16:49.160><c> on</c><00:16:49.600><c> calculation</c><00:16:50.560><c> we</c><00:16:50.720><c> we</c><00:16:50.880><c> know</c><00:16:51.199><c> that</c>

00:16:51.629 --> 00:16:51.639 align:start position:0%
based on calculation we we know that
 

00:16:51.639 --> 00:16:55.309 align:start position:0%
based on calculation we we know that
those<00:16:52.040><c> bacteria</c><00:16:53.000><c> will</c><00:16:53.240><c> be</c><00:16:53.800><c> the</c><00:16:54.000><c> most</c><00:16:54.360><c> probable</c>

00:16:55.309 --> 00:16:55.319 align:start position:0%
those bacteria will be the most probable
 

00:16:55.319 --> 00:16:57.949 align:start position:0%
those bacteria will be the most probable
probable<00:16:56.279><c> uh</c><00:16:56.399><c> Downstream</c><00:16:57.000><c> complication</c><00:16:57.639><c> for</c>

00:16:57.949 --> 00:16:57.959 align:start position:0%
probable uh Downstream complication for
 

00:16:57.959 --> 00:17:00.870 align:start position:0%
probable uh Downstream complication for
such<00:16:58.360><c> you</c><00:16:58.440><c> know</c><00:16:58.920><c> Hospital</c><00:16:59.399><c> stay</c><00:16:59.920><c> and</c><00:17:00.440><c> U</c>

00:17:00.870 --> 00:17:00.880 align:start position:0%
such you know Hospital stay and U
 

00:17:00.880 --> 00:17:04.789 align:start position:0%
such you know Hospital stay and U
surgery<00:17:01.480><c> type</c><00:17:01.839><c> let's</c><00:17:02.120><c> say</c><00:17:02.920><c> and</c><00:17:03.319><c> um</c><00:17:03.639><c> in</c><00:17:03.880><c> that</c>

00:17:04.789 --> 00:17:04.799 align:start position:0%
surgery type let's say and um in that
 

00:17:04.799 --> 00:17:08.110 align:start position:0%
surgery type let's say and um in that
case<00:17:05.799><c> physician</c><00:17:06.520><c> can</c><00:17:06.880><c> you</c><00:17:06.959><c> know</c><00:17:07.240><c> can</c><00:17:07.520><c> mitigate</c>

00:17:08.110 --> 00:17:08.120 align:start position:0%
case physician can you know can mitigate
 

00:17:08.120 --> 00:17:10.390 align:start position:0%
case physician can you know can mitigate
those<00:17:08.360><c> Downstream</c><00:17:08.919><c> complication</c><00:17:09.559><c> or</c><00:17:09.839><c> avoid</c>

00:17:10.390 --> 00:17:10.400 align:start position:0%
those Downstream complication or avoid
 

00:17:10.400 --> 00:17:14.470 align:start position:0%
those Downstream complication or avoid
if<00:17:11.360><c> if</c><00:17:11.799><c> make</c><00:17:12.160><c> some</c><00:17:12.839><c> other</c><00:17:13.240><c> prescription</c><00:17:13.959><c> to</c>

00:17:14.470 --> 00:17:14.480 align:start position:0%
if if make some other prescription to
 

00:17:14.480 --> 00:17:17.710 align:start position:0%
if if make some other prescription to
the<00:17:14.720><c> the</c><00:17:14.919><c> those</c><00:17:15.199><c> patients</c><00:17:16.079><c> this</c><00:17:16.799><c> this</c><00:17:17.240><c> timely</c>

00:17:17.710 --> 00:17:17.720 align:start position:0%
the the those patients this this timely
 

00:17:17.720 --> 00:17:21.230 align:start position:0%
the the those patients this this timely
alerts<00:17:18.480><c> enable</c><00:17:19.039><c> clinicians</c><00:17:20.039><c> to</c>

00:17:21.230 --> 00:17:21.240 align:start position:0%
alerts enable clinicians to
 

00:17:21.240 --> 00:17:24.750 align:start position:0%
alerts enable clinicians to
prevent<00:17:22.240><c> uh</c><00:17:22.360><c> and</c><00:17:22.520><c> mitigate</c><00:17:23.360><c> uh</c><00:17:23.799><c> complication</c>

00:17:24.750 --> 00:17:24.760 align:start position:0%
prevent uh and mitigate uh complication
 

00:17:24.760 --> 00:17:28.189 align:start position:0%
prevent uh and mitigate uh complication
quite<00:17:25.160><c> you</c><00:17:25.319><c> know</c><00:17:25.600><c> quite</c><00:17:25.959><c> fast</c><00:17:26.760><c> and</c><00:17:27.199><c> it</c><00:17:27.319><c> is</c><00:17:27.839><c> real</c>

00:17:28.189 --> 00:17:28.199 align:start position:0%
quite you know quite fast and it is real
 

00:17:28.199 --> 00:17:31.430 align:start position:0%
quite you know quite fast and it is real
add<00:17:28.480><c> value</c><00:17:28.880><c> to</c><00:17:29.080><c> their</c><00:17:29.280><c> work</c><00:17:30.160><c> and</c><00:17:30.760><c> that's</c><00:17:31.280><c> which</c>

00:17:31.430 --> 00:17:31.440 align:start position:0%
add value to their work and that's which
 

00:17:31.440 --> 00:17:34.430 align:start position:0%
add value to their work and that's which
is<00:17:31.720><c> ultimately</c><00:17:32.400><c> improving</c><00:17:32.960><c> the</c><00:17:33.200><c> patient</c>

00:17:34.430 --> 00:17:34.440 align:start position:0%
is ultimately improving the patient
 

00:17:34.440 --> 00:17:38.470 align:start position:0%
is ultimately improving the patient
outcomes<00:17:35.440><c> so</c><00:17:35.919><c> uh</c><00:17:36.440><c> yeah</c><00:17:37.440><c> we</c><00:17:37.760><c> we</c><00:17:37.880><c> are</c><00:17:38.000><c> building</c>

00:17:38.470 --> 00:17:38.480 align:start position:0%
outcomes so uh yeah we we are building
 

00:17:38.480 --> 00:17:43.270 align:start position:0%
outcomes so uh yeah we we are building
Enterprise<00:17:39.400><c> ready</c><00:17:40.280><c> software</c><00:17:41.160><c> so</c><00:17:42.039><c> uh</c><00:17:42.720><c> which</c><00:17:42.880><c> is</c>

00:17:43.270 --> 00:17:43.280 align:start position:0%
Enterprise ready software so uh which is
 

00:17:43.280 --> 00:17:47.950 align:start position:0%
Enterprise ready software so uh which is
you<00:17:43.440><c> know</c><00:17:44.440><c> a</c><00:17:44.559><c> bit</c><00:17:44.880><c> complex</c><00:17:45.840><c> in</c><00:17:46.000><c> order</c><00:17:46.520><c> to</c><00:17:47.520><c> um</c>

00:17:47.950 --> 00:17:47.960 align:start position:0%
you know a bit complex in order to um
 

00:17:47.960 --> 00:17:51.350 align:start position:0%
you know a bit complex in order to um
provide<00:17:48.360><c> the</c><00:17:48.600><c> full</c><00:17:49.600><c> security</c><00:17:50.600><c> full</c><00:17:50.960><c> data</c>

00:17:51.350 --> 00:17:51.360 align:start position:0%
provide the full security full data
 

00:17:51.360 --> 00:17:53.950 align:start position:0%
provide the full security full data
security<00:17:52.080><c> and</c><00:17:52.360><c> capability</c><00:17:52.960><c> to</c><00:17:53.120><c> connect</c><00:17:53.760><c> to</c>

00:17:53.950 --> 00:17:53.960 align:start position:0%
security and capability to connect to
 

00:17:53.960 --> 00:17:58.149 align:start position:0%
security and capability to connect to
the<00:17:54.240><c> different</c><00:17:55.039><c> uh</c><00:17:55.720><c> uh</c><00:17:55.919><c> data</c><00:17:56.240><c> sources</c><00:17:57.240><c> so</c><00:17:57.840><c> uh</c>

00:17:58.149 --> 00:17:58.159 align:start position:0%
the different uh uh data sources so uh
 

00:17:58.159 --> 00:18:00.470 align:start position:0%
the different uh uh data sources so uh
but<00:17:58.400><c> basically</c>

00:18:00.470 --> 00:18:00.480 align:start position:0%
but basically
 

00:18:00.480 --> 00:18:03.350 align:start position:0%
but basically
you<00:18:00.640><c> know</c><00:18:00.840><c> under</c><00:18:01.320><c> those</c><00:18:02.159><c> complex</c><00:18:02.600><c> software</c>

00:18:03.350 --> 00:18:03.360 align:start position:0%
you know under those complex software
 

00:18:03.360 --> 00:18:07.070 align:start position:0%
you know under those complex software
architecture<00:18:04.360><c> We</c><00:18:04.640><c> have</c><00:18:05.640><c> basically</c><00:18:06.159><c> API</c><00:18:06.960><c> which</c>

00:18:07.070 --> 00:18:07.080 align:start position:0%
architecture We have basically API which
 

00:18:07.080 --> 00:18:09.870 align:start position:0%
architecture We have basically API which
can<00:18:07.320><c> transform</c><00:18:08.000><c> natural</c><00:18:08.440><c> language</c><00:18:09.440><c> questions</c>

00:18:09.870 --> 00:18:09.880 align:start position:0%
can transform natural language questions
 

00:18:09.880 --> 00:18:12.310 align:start position:0%
can transform natural language questions
to<00:18:10.080><c> the</c><00:18:10.400><c> structure</c><00:18:10.840><c> query</c><00:18:11.159><c> language</c><00:18:11.600><c> code</c><00:18:12.159><c> and</c>

00:18:12.310 --> 00:18:12.320 align:start position:0%
to the structure query language code and
 

00:18:12.320 --> 00:18:14.510 align:start position:0%
to the structure query language code and
you<00:18:12.640><c> are</c><00:18:12.840><c> able</c><00:18:13.080><c> to</c><00:18:13.280><c> integrate</c><00:18:13.799><c> those</c><00:18:14.120><c> API</c>

00:18:14.510 --> 00:18:14.520 align:start position:0%
you are able to integrate those API
 

00:18:14.520 --> 00:18:17.789 align:start position:0%
you are able to integrate those API
whatever<00:18:14.960><c> you</c><00:18:15.120><c> you</c><00:18:15.240><c> want</c><00:18:16.039><c> of</c><00:18:16.200><c> course</c><00:18:16.600><c> we</c><00:18:17.320><c> know</c>

00:18:17.789 --> 00:18:17.799 align:start position:0%
whatever you you want of course we know
 

00:18:17.799 --> 00:18:20.909 align:start position:0%
whatever you you want of course we know
that<00:18:18.320><c> uh</c><00:18:18.559><c> most</c><00:18:18.799><c> of</c><00:18:18.960><c> the</c><00:18:19.400><c> hospitals</c><00:18:20.400><c> they</c>

00:18:20.909 --> 00:18:20.919 align:start position:0%
that uh most of the hospitals they
 

00:18:20.919 --> 00:18:23.510 align:start position:0%
that uh most of the hospitals they
asking<00:18:21.320><c> about</c><00:18:21.600><c> the</c><00:18:21.720><c> on</c><00:18:22.000><c> premise</c><00:18:22.520><c> Solutions</c>

00:18:23.510 --> 00:18:23.520 align:start position:0%
asking about the on premise Solutions
 

00:18:23.520 --> 00:18:26.070 align:start position:0%
asking about the on premise Solutions
and<00:18:23.760><c> basically</c><00:18:24.240><c> that's</c><00:18:24.679><c> fine</c><00:18:25.200><c> with</c><00:18:25.400><c> us</c><00:18:26.000><c> it</c>

00:18:26.070 --> 00:18:26.080 align:start position:0%
and basically that's fine with us it
 

00:18:26.080 --> 00:18:29.630 align:start position:0%
and basically that's fine with us it
will<00:18:26.240><c> be</c><00:18:26.440><c> like</c><00:18:26.600><c> a</c><00:18:27.240><c> microservice</c><00:18:28.240><c> uh</c><00:18:28.840><c> on</c><00:18:29.480><c> with</c>

00:18:29.630 --> 00:18:29.640 align:start position:0%
will be like a microservice uh on with
 

00:18:29.640 --> 00:18:32.710 align:start position:0%
will be like a microservice uh on with
the<00:18:30.120><c> AI</c><00:18:30.480><c> model</c><00:18:30.840><c> on</c><00:18:31.039><c> the</c><00:18:31.200><c> back</c><00:18:31.400><c> end</c><00:18:32.320><c> where</c><00:18:32.520><c> you</c>

00:18:32.710 --> 00:18:32.720 align:start position:0%
the AI model on the back end where you
 

00:18:32.720 --> 00:18:37.110 align:start position:0%
the AI model on the back end where you
can<00:18:33.640><c> basically</c><00:18:34.640><c> um</c><00:18:35.360><c> you</c><00:18:35.480><c> know</c><00:18:35.880><c> ask</c><00:18:36.480><c> question</c>

00:18:37.110 --> 00:18:37.120 align:start position:0%
can basically um you know ask question
 

00:18:37.120 --> 00:18:40.390 align:start position:0%
can basically um you know ask question
and<00:18:37.320><c> get</c><00:18:37.520><c> the</c><00:18:37.760><c> responses</c><00:18:38.360><c> using</c><00:18:38.799><c> like</c><00:18:39.799><c> like</c>

00:18:40.390 --> 00:18:40.400 align:start position:0%
and get the responses using like like
 

00:18:40.400 --> 00:18:43.310 align:start position:0%
and get the responses using like like
microservice<00:18:41.400><c> application</c><00:18:42.039><c> within</c><00:18:42.440><c> your</c>

00:18:43.310 --> 00:18:43.320 align:start position:0%
microservice application within your
 

00:18:43.320 --> 00:18:48.710 align:start position:0%
microservice application within your
hospital<00:18:43.840><c> it</c><00:18:44.640><c> ecosystem</c><00:18:45.640><c> um</c><00:18:46.480><c> yeah</c><00:18:47.200><c> so</c><00:18:48.200><c> again</c>

00:18:48.710 --> 00:18:48.720 align:start position:0%
hospital it ecosystem um yeah so again
 

00:18:48.720 --> 00:18:50.710 align:start position:0%
hospital it ecosystem um yeah so again
so

00:18:50.710 --> 00:18:50.720 align:start position:0%
so
 

00:18:50.720 --> 00:18:55.350 align:start position:0%
so
um<00:18:51.720><c> this</c><00:18:51.880><c> is</c><00:18:52.360><c> kind</c><00:18:52.520><c> of</c><00:18:53.200><c> approach</c><00:18:54.200><c> to</c>

00:18:55.350 --> 00:18:55.360 align:start position:0%
um this is kind of approach to
 

00:18:55.360 --> 00:18:58.789 align:start position:0%
um this is kind of approach to
making<00:18:56.360><c> AI</c><00:18:56.799><c> data</c><00:18:57.120><c> analytics</c><00:18:57.840><c> accessible</c><00:18:58.360><c> for</c>

00:18:58.789 --> 00:18:58.799 align:start position:0%
making AI data analytics accessible for
 

00:18:58.799 --> 00:19:02.549 align:start position:0%
making AI data analytics accessible for
non-<00:18:59.000><c> technical</c><00:18:59.520><c> employees</c><00:19:00.520><c> with</c><00:19:01.159><c> the</c>

00:19:02.549 --> 00:19:02.559 align:start position:0%
non- technical employees with the
 

00:19:02.559 --> 00:19:06.549 align:start position:0%
non- technical employees with the
generating<00:19:03.559><c> SQL</c><00:19:04.360><c> every</c><00:19:04.679><c> time</c><00:19:05.320><c> which</c><00:19:05.960><c> uh</c><00:19:06.440><c> you</c>

00:19:06.549 --> 00:19:06.559 align:start position:0%
generating SQL every time which uh you
 

00:19:06.559 --> 00:19:09.590 align:start position:0%
generating SQL every time which uh you
know<00:19:06.840><c> which</c><00:19:07.000><c> brings</c><00:19:07.559><c> more</c><00:19:07.919><c> and</c><00:19:08.720><c> different</c>

00:19:09.590 --> 00:19:09.600 align:start position:0%
know which brings more and different
 

00:19:09.600 --> 00:19:13.510 align:start position:0%
know which brings more and different
responses<00:19:10.559><c> to</c><00:19:10.799><c> the</c><00:19:11.679><c> users</c>

00:19:13.510 --> 00:19:13.520 align:start position:0%
responses to the users
 

00:19:13.520 --> 00:19:17.230 align:start position:0%
responses to the users
fingertips<00:19:14.520><c> um</c><00:19:15.000><c> yeah</c><00:19:15.200><c> so</c><00:19:15.600><c> this</c><00:19:15.720><c> is</c><00:19:16.640><c> basically</c>

00:19:17.230 --> 00:19:17.240 align:start position:0%
fingertips um yeah so this is basically
 

00:19:17.240 --> 00:19:20.350 align:start position:0%
fingertips um yeah so this is basically
what<00:19:18.039><c> what</c><00:19:18.240><c> it</c><00:19:18.480><c> looks</c><00:19:18.799><c> like</c><00:19:19.120><c> in</c><00:19:19.360><c> in</c><00:19:19.520><c> our</c><00:19:20.000><c> in</c><00:19:20.159><c> the</c>

00:19:20.350 --> 00:19:20.360 align:start position:0%
what what it looks like in in our in the
 

00:19:20.360 --> 00:19:23.270 align:start position:0%
what what it looks like in in our in the
standard<00:19:20.840><c> way</c><00:19:21.159><c> so</c><00:19:21.400><c> the</c><00:19:21.600><c> most</c><00:19:21.880><c> common</c><00:19:22.480><c> software</c>

00:19:23.270 --> 00:19:23.280 align:start position:0%
standard way so the most common software
 

00:19:23.280 --> 00:19:27.590 align:start position:0%
standard way so the most common software
architecture<00:19:24.280><c> uh</c><00:19:24.440><c> but</c><00:19:24.799><c> nevertheless</c><00:19:25.840><c> yeah</c><00:19:26.840><c> so</c>

00:19:27.590 --> 00:19:27.600 align:start position:0%
architecture uh but nevertheless yeah so
 

00:19:27.600 --> 00:19:31.190 align:start position:0%
architecture uh but nevertheless yeah so
um<00:19:28.520><c> the</c><00:19:28.760><c> that's</c><00:19:28.880><c> it</c><00:19:29.080><c> for</c><00:19:29.320><c> my</c><00:19:29.559><c> side</c><00:19:30.480><c> um</c><00:19:30.960><c> I'm</c>

00:19:31.190 --> 00:19:31.200 align:start position:0%
um the that's it for my side um I'm
 

00:19:31.200 --> 00:19:35.750 align:start position:0%
um the that's it for my side um I'm
pretty<00:19:31.480><c> sure</c><00:19:32.000><c> I'm</c><00:19:32.640><c> in</c><00:19:32.919><c> time</c><00:19:33.280><c> of</c><00:19:33.640><c> 20</c><00:19:34.280><c> minutes</c><00:19:35.280><c> um</c>

00:19:35.750 --> 00:19:35.760 align:start position:0%
pretty sure I'm in time of 20 minutes um
 

00:19:35.760 --> 00:19:37.909 align:start position:0%
pretty sure I'm in time of 20 minutes um
so<00:19:36.120><c> yeah</c><00:19:36.320><c> if</c><00:19:36.440><c> you</c><00:19:36.679><c> have</c><00:19:36.880><c> any</c><00:19:37.159><c> questions</c><00:19:37.640><c> feel</c>

00:19:37.909 --> 00:19:37.919 align:start position:0%
so yeah if you have any questions feel
 

00:19:37.919 --> 00:19:40.789 align:start position:0%
so yeah if you have any questions feel
free<00:19:38.200><c> to</c><00:19:38.440><c> contact</c><00:19:38.840><c> us</c><00:19:39.200><c> and</c><00:19:39.960><c> the</c><00:19:40.159><c> best</c><00:19:40.400><c> way</c><00:19:40.559><c> to</c>

00:19:40.789 --> 00:19:40.799 align:start position:0%
free to contact us and the best way to
 

00:19:40.799 --> 00:19:43.870 align:start position:0%
free to contact us and the best way to
predict<00:19:41.280><c> the</c><00:19:41.480><c> future</c><00:19:42.159><c> is</c><00:19:42.320><c> to</c><00:19:42.559><c> create</c><00:19:42.919><c> it</c><00:19:43.440><c> so</c><00:19:43.720><c> I</c>

00:19:43.870 --> 00:19:43.880 align:start position:0%
predict the future is to create it so I
 

00:19:43.880 --> 00:19:49.559 align:start position:0%
predict the future is to create it so I
hope<00:19:44.520><c> let's</c><00:19:44.919><c> create</c><00:19:45.640><c> the</c><00:19:45.960><c> future</c><00:19:46.720><c> together</c>

