WEBVTT
Kind: captions
Language: en

00:00:00.320 --> 00:00:03.949 align:start position:0%
 
oh<00:00:00.560><c> boy</c><00:00:01.079><c> another</c><00:00:01.439><c> video</c><00:00:02.320><c> strap</c><00:00:02.720><c> in</c><00:00:02.960><c> folks</c><00:00:03.840><c> this</c>

00:00:03.949 --> 00:00:03.959 align:start position:0%
oh boy another video strap in folks this
 

00:00:03.959 --> 00:00:06.630 align:start position:0%
oh boy another video strap in folks this
is<00:00:04.120><c> going</c><00:00:04.200><c> to</c><00:00:04.359><c> be</c><00:00:04.520><c> another</c><00:00:05.240><c> Humding<PERSON></c><00:00:06.240><c> is</c><00:00:06.399><c> that</c>

00:00:06.630 --> 00:00:06.640 align:start position:0%
is going to be another Humdinger is that
 

00:00:06.640 --> 00:00:08.910 align:start position:0%
is going to be another Humdinger is that
sarcasm<00:00:07.640><c> I</c><00:00:07.759><c> never</c><00:00:08.080><c> programmed</c><00:00:08.480><c> you</c><00:00:08.559><c> for</c>

00:00:08.910 --> 00:00:08.920 align:start position:0%
sarcasm I never programmed you for
 

00:00:08.920 --> 00:00:11.190 align:start position:0%
sarcasm I never programmed you for
sarcasm<00:00:09.920><c> well</c><00:00:10.080><c> you're</c><00:00:10.240><c> no</c>

00:00:11.190 --> 00:00:11.200 align:start position:0%
sarcasm well you're no
 

00:00:11.200 --> 00:00:14.230 align:start position:0%
sarcasm well you're no
programmer<00:00:12.200><c> I'm</c><00:00:12.360><c> no</c><00:00:12.679><c> Proctologist</c><00:00:13.639><c> either</c>

00:00:14.230 --> 00:00:14.240 align:start position:0%
programmer I'm no Proctologist either
 

00:00:14.240 --> 00:00:16.950 align:start position:0%
programmer I'm no Proctologist either
but<00:00:14.400><c> I</c><00:00:14.519><c> can</c><00:00:14.679><c> tell</c><00:00:14.879><c> when</c><00:00:15.040><c> I'm</c><00:00:15.200><c> working</c><00:00:15.480><c> with</c>

00:00:16.950 --> 00:00:16.960 align:start position:0%
but I can tell when I'm working with
 

00:00:16.960 --> 00:00:20.390 align:start position:0%
but I can tell when I'm working with
anole<00:00:17.960><c> you</c><00:00:18.119><c> win</c><00:00:18.439><c> this</c><00:00:18.760><c> round</c><00:00:19.760><c> to</c><00:00:19.880><c> be</c><00:00:20.080><c> fair</c>

00:00:20.390 --> 00:00:20.400 align:start position:0%
anole you win this round to be fair
 

00:00:20.400 --> 00:00:22.550 align:start position:0%
anole you win this round to be fair
you're<00:00:20.600><c> not</c><00:00:20.840><c> wrong</c><00:00:21.480><c> in</c><00:00:21.640><c> today's</c><00:00:22.039><c> video</c><00:00:22.359><c> we'll</c>

00:00:22.550 --> 00:00:22.560 align:start position:0%
you're not wrong in today's video we'll
 

00:00:22.560 --> 00:00:25.109 align:start position:0%
you're not wrong in today's video we'll
let<00:00:22.760><c> AI</c><00:00:23.119><c> do</c><00:00:23.320><c> all</c><00:00:23.519><c> the</c><00:00:23.840><c> programming</c><00:00:24.840><c> what</c><00:00:24.960><c> are</c>

00:00:25.109 --> 00:00:25.119 align:start position:0%
let AI do all the programming what are
 

00:00:25.119 --> 00:00:27.790 align:start position:0%
let AI do all the programming what are
we<00:00:25.279><c> working</c><00:00:25.720><c> with</c><00:00:26.560><c> Asian</c><00:00:26.840><c> cheerleaders</c><00:00:27.640><c> and</c>

00:00:27.790 --> 00:00:27.800 align:start position:0%
we working with Asian cheerleaders and
 

00:00:27.800 --> 00:00:30.070 align:start position:0%
we working with Asian cheerleaders and
boogers

00:00:30.070 --> 00:00:30.080 align:start position:0%
boogers
 

00:00:30.080 --> 00:00:32.590 align:start position:0%
boogers
you<00:00:30.279><c> had</c><00:00:30.439><c> me</c><00:00:30.599><c> at</c><00:00:30.880><c> Asian</c>

00:00:32.590 --> 00:00:32.600 align:start position:0%
you had me at Asian
 

00:00:32.600 --> 00:00:38.510 align:start position:0%
you had me at Asian
cheerleaders<00:00:33.600><c> hey</c><00:00:34.320><c> everybody</c><00:00:35.320><c> it's</c><00:00:35.800><c> time</c><00:00:36.800><c> a</c><00:00:37.520><c> i</c>

00:00:38.510 --> 00:00:38.520 align:start position:0%
cheerleaders hey everybody it's time a i
 

00:00:38.520 --> 00:00:42.590 align:start position:0%
cheerleaders hey everybody it's time a i
with

00:00:42.590 --> 00:00:42.600 align:start position:0%
 
 

00:00:42.600 --> 00:00:46.430 align:start position:0%
 
J<00:00:43.600><c> sounds</c><00:00:43.960><c> too</c><00:00:44.280><c> good</c><00:00:44.399><c> to</c><00:00:44.600><c> be</c><00:00:44.840><c> true</c><00:00:45.719><c> but</c><00:00:45.960><c> Jay</c><00:00:46.239><c> has</c>

00:00:46.430 --> 00:00:46.440 align:start position:0%
J sounds too good to be true but Jay has
 

00:00:46.440 --> 00:00:49.310 align:start position:0%
J sounds too good to be true but Jay has
got<00:00:46.760><c> some</c><00:00:47.120><c> tips</c><00:00:47.399><c> for</c><00:00:47.640><c> you</c><00:00:48.280><c> totally</c><00:00:48.719><c> free</c><00:00:49.120><c> you</c>

00:00:49.310 --> 00:00:49.320 align:start position:0%
got some tips for you totally free you
 

00:00:49.320 --> 00:00:52.150 align:start position:0%
got some tips for you totally free you
don't<00:00:49.680><c> have</c><00:00:49.800><c> to</c><00:00:50.079><c> pay</c><00:00:50.440><c> it's</c><00:00:50.680><c> time</c><00:00:50.879><c> for</c><00:00:51.199><c> AI</c><00:00:51.760><c> tips</c>

00:00:52.150 --> 00:00:52.160 align:start position:0%
don't have to pay it's time for AI tips
 

00:00:52.160 --> 00:00:56.150 align:start position:0%
don't have to pay it's time for AI tips
with<00:00:52.359><c> Jay</c><00:00:53.079><c> AI</c><00:00:53.440><c> tips</c><00:00:53.760><c> with</c><00:00:54.000><c> J</c><00:00:54.520><c> hoay</c><00:00:55.399><c> AI</c><00:00:55.760><c> tips</c>

00:00:56.150 --> 00:00:56.160 align:start position:0%
with Jay AI tips with J hoay AI tips
 

00:00:56.160 --> 00:01:02.509 align:start position:0%
with Jay AI tips with J hoay AI tips
with<00:00:56.719><c> j</c><00:00:57.719><c> a</c><00:00:57.960><c> i</c><00:00:58.280><c> a</c><00:00:58.480><c> i</c><00:00:58.840><c> a</c><00:00:59.000><c> i</c><00:00:59.280><c> a</c><00:00:59.519><c> i</c><00:01:00.079><c> AI</c><00:01:00.559><c> tips</c><00:01:00.920><c> with</c><00:01:01.359><c> j</c><00:01:02.359><c> in</c>

00:01:02.509 --> 00:01:02.519 align:start position:0%
with j a i a i a i a i AI tips with j in
 

00:01:02.519 --> 00:01:06.149 align:start position:0%
with j a i a i a i a i AI tips with j in
today's<00:01:02.920><c> video</c><00:01:03.800><c> how</c><00:01:04.239><c> not</c><00:01:04.559><c> to</c><00:01:04.799><c> program</c><00:01:05.199><c> with</c><00:01:05.360><c> AI</c>

00:01:06.149 --> 00:01:06.159 align:start position:0%
today's video how not to program with AI
 

00:01:06.159 --> 00:01:09.230 align:start position:0%
today's video how not to program with AI
also<00:01:06.799><c> a</c><00:01:07.000><c> special</c><00:01:07.320><c> guest</c><00:01:07.600><c> appearance</c><00:01:08.119><c> by</c><00:01:08.400><c> Asian</c>

00:01:09.230 --> 00:01:09.240 align:start position:0%
also a special guest appearance by Asian
 

00:01:09.240 --> 00:01:14.149 align:start position:0%
also a special guest appearance by Asian
cheerleaders<00:01:10.240><c> and</c><00:01:10.360><c> now</c><00:01:10.600><c> here's</c>

00:01:14.149 --> 00:01:14.159 align:start position:0%
 
 

00:01:14.159 --> 00:01:17.630 align:start position:0%
 
Jay<00:01:15.159><c> today</c><00:01:15.439><c> I'm</c><00:01:15.600><c> using</c><00:01:15.960><c> chat</c><00:01:16.280><c> GPT</c><00:01:17.200><c> instead</c><00:01:17.479><c> of</c>

00:01:17.630 --> 00:01:17.640 align:start position:0%
Jay today I'm using chat GPT instead of
 

00:01:17.640 --> 00:01:19.630 align:start position:0%
Jay today I'm using chat GPT instead of
Claude<00:01:18.479><c> because</c><00:01:18.720><c> I</c><00:01:18.799><c> want</c><00:01:19.119><c> thousands</c><00:01:19.479><c> of</c>

00:01:19.630 --> 00:01:19.640 align:start position:0%
Claude because I want thousands of
 

00:01:19.640 --> 00:01:21.350 align:start position:0%
Claude because I want thousands of
witnesses<00:01:20.079><c> when</c><00:01:20.240><c> I</c><00:01:20.360><c> complain</c><00:01:20.720><c> that</c><00:01:20.880><c> their</c><00:01:21.040><c> new</c>

00:01:21.350 --> 00:01:21.360 align:start position:0%
witnesses when I complain that their new
 

00:01:21.360 --> 00:01:23.870 align:start position:0%
witnesses when I complain that their new
canvas<00:01:21.759><c> feature</c><00:01:22.119><c> doesn't</c><00:01:22.400><c> work</c><00:01:22.600><c> for</c><00:01:22.759><c> me</c><00:01:23.720><c> it</c>

00:01:23.870 --> 00:01:23.880 align:start position:0%
canvas feature doesn't work for me it
 

00:01:23.880 --> 00:01:25.950 align:start position:0%
canvas feature doesn't work for me it
acts<00:01:24.119><c> like</c><00:01:24.280><c> the</c><00:01:24.360><c> same</c><00:01:24.560><c> old</c><00:01:24.840><c> chat</c>

00:01:25.950 --> 00:01:25.960 align:start position:0%
acts like the same old chat
 

00:01:25.960 --> 00:01:29.429 align:start position:0%
acts like the same old chat
GPT<00:01:26.960><c> still</c><00:01:27.520><c> it'll</c><00:01:27.799><c> work</c><00:01:27.960><c> for</c><00:01:28.119><c> our</c><00:01:28.439><c> purposes</c>

00:01:29.429 --> 00:01:29.439 align:start position:0%
GPT still it'll work for our purposes
 

00:01:29.439 --> 00:01:31.350 align:start position:0%
GPT still it'll work for our purposes
we're<00:01:29.600><c> going</c><00:01:29.720><c> to</c><00:01:29.920><c> to</c><00:01:30.000><c> let</c><00:01:30.240><c> AI</c><00:01:30.640><c> solve</c><00:01:30.920><c> a</c><00:01:31.119><c> problem</c>

00:01:31.350 --> 00:01:31.360 align:start position:0%
we're going to to let AI solve a problem
 

00:01:31.360 --> 00:01:33.069 align:start position:0%
we're going to to let AI solve a problem
for<00:01:31.520><c> us</c><00:01:31.680><c> by</c><00:01:31.840><c> writing</c><00:01:32.079><c> a</c><00:01:32.280><c> program</c><00:01:32.759><c> and</c><00:01:32.920><c> all</c>

00:01:33.069 --> 00:01:33.079 align:start position:0%
for us by writing a program and all
 

00:01:33.079 --> 00:01:35.550 align:start position:0%
for us by writing a program and all
we'll<00:01:33.320><c> use</c><00:01:33.560><c> is</c><00:01:33.680><c> our</c><00:01:33.840><c> browser</c><00:01:34.560><c> notepad</c><00:01:35.320><c> and</c><00:01:35.439><c> a</c>

00:01:35.550 --> 00:01:35.560 align:start position:0%
we'll use is our browser notepad and a
 

00:01:35.560 --> 00:01:38.710 align:start position:0%
we'll use is our browser notepad and a
command<00:01:36.079><c> prompt</c><00:01:37.079><c> what's</c><00:01:37.280><c> the</c><00:01:37.479><c> problem</c><00:01:38.280><c> lately</c>

00:01:38.710 --> 00:01:38.720 align:start position:0%
command prompt what's the problem lately
 

00:01:38.720 --> 00:01:40.350 align:start position:0%
command prompt what's the problem lately
every<00:01:38.920><c> time</c><00:01:39.079><c> I</c><00:01:39.159><c> go</c><00:01:39.280><c> to</c><00:01:39.439><c> download</c><00:01:39.799><c> a</c><00:01:39.920><c> photo</c><00:01:40.200><c> from</c>

00:01:40.350 --> 00:01:40.360 align:start position:0%
every time I go to download a photo from
 

00:01:40.360 --> 00:01:42.310 align:start position:0%
every time I go to download a photo from
the<00:01:40.479><c> Google</c><00:01:41.159><c> it</c><00:01:41.280><c> wants</c><00:01:41.479><c> to</c><00:01:41.600><c> send</c><00:01:41.799><c> it</c><00:01:41.920><c> to</c><00:01:42.040><c> me</c><00:01:42.240><c> as</c>

00:01:42.310 --> 00:01:42.320 align:start position:0%
the Google it wants to send it to me as
 

00:01:42.320 --> 00:01:45.310 align:start position:0%
the Google it wants to send it to me as
a<00:01:42.479><c> webp</c><00:01:43.040><c> file</c><00:01:43.840><c> but</c><00:01:43.960><c> when</c><00:01:44.079><c> I</c><00:01:44.200><c> go</c><00:01:44.360><c> to</c><00:01:44.600><c> upload</c><00:01:45.119><c> that</c>

00:01:45.310 --> 00:01:45.320 align:start position:0%
a webp file but when I go to upload that
 

00:01:45.320 --> 00:01:47.590 align:start position:0%
a webp file but when I go to upload that
same<00:01:45.560><c> file</c><00:01:46.320><c> most</c><00:01:46.560><c> sites</c><00:01:46.880><c> don't</c><00:01:47.079><c> want</c><00:01:47.280><c> it</c><00:01:47.399><c> in</c>

00:01:47.590 --> 00:01:47.600 align:start position:0%
same file most sites don't want it in
 

00:01:47.600 --> 00:01:50.190 align:start position:0%
same file most sites don't want it in
webp<00:01:48.119><c> format</c><00:01:48.880><c> so</c><00:01:49.079><c> I</c><00:01:49.200><c> waste</c><00:01:49.479><c> a</c><00:01:49.600><c> lot</c><00:01:49.719><c> of</c><00:01:49.920><c> time</c>

00:01:50.190 --> 00:01:50.200 align:start position:0%
webp format so I waste a lot of time
 

00:01:50.200 --> 00:01:52.469 align:start position:0%
webp format so I waste a lot of time
converting<00:01:50.719><c> files</c><00:01:51.640><c> I</c><00:01:51.759><c> feel</c><00:01:51.920><c> like</c><00:01:52.079><c> I</c><00:01:52.200><c> may</c><00:01:52.320><c> need</c>

00:01:52.469 --> 00:01:52.479 align:start position:0%
converting files I feel like I may need
 

00:01:52.479 --> 00:01:55.149 align:start position:0%
converting files I feel like I may need
to<00:01:52.600><c> see</c><00:01:52.799><c> a</c><00:01:52.880><c> few</c><00:01:53.079><c> more</c><00:01:53.680><c> examples</c><00:01:54.680><c> I'm</c><00:01:54.799><c> going</c><00:01:55.000><c> to</c>

00:01:55.149 --> 00:01:55.159 align:start position:0%
to see a few more examples I'm going to
 

00:01:55.159 --> 00:01:57.550 align:start position:0%
to see a few more examples I'm going to
ask<00:01:55.320><c> chat</c><00:01:55.600><c> GPT</c><00:01:56.200><c> to</c><00:01:56.399><c> program</c><00:01:56.759><c> a</c><00:01:56.920><c> solution</c><00:01:57.439><c> but</c>

00:01:57.550 --> 00:01:57.560 align:start position:0%
ask chat GPT to program a solution but
 

00:01:57.560 --> 00:01:59.029 align:start position:0%
ask chat GPT to program a solution but
to<00:01:57.799><c> present</c><00:01:58.079><c> it</c><00:01:58.360><c> as</c><00:01:58.520><c> though</c><00:01:58.680><c> they</c><00:01:58.759><c> were</c>

00:01:59.029 --> 00:01:59.039 align:start position:0%
to present it as though they were
 

00:01:59.039 --> 00:02:01.469 align:start position:0%
to present it as though they were
explaining<00:01:59.520><c> it</c><00:01:59.600><c> to</c><00:01:59.920><c> a</c><00:02:00.039><c> complete</c><00:02:00.479><c> novice</c>

00:02:01.469 --> 00:02:01.479 align:start position:0%
explaining it to a complete novice
 

00:02:01.479 --> 00:02:03.350 align:start position:0%
explaining it to a complete novice
somebody<00:02:01.799><c> who</c><00:02:01.960><c> knows</c><00:02:02.280><c> next</c><00:02:02.479><c> to</c><00:02:02.719><c> nothing</c><00:02:03.000><c> about</c>

00:02:03.350 --> 00:02:03.360 align:start position:0%
somebody who knows next to nothing about
 

00:02:03.360 --> 00:02:07.149 align:start position:0%
somebody who knows next to nothing about
programming<00:02:04.360><c> exactly</c><00:02:05.079><c> a</c><00:02:05.240><c> blank</c><00:02:05.600><c> slate</c><00:02:06.600><c> right</c>

00:02:07.149 --> 00:02:07.159 align:start position:0%
programming exactly a blank slate right
 

00:02:07.159 --> 00:02:09.510 align:start position:0%
programming exactly a blank slate right
totally<00:02:07.560><c> clueless</c><00:02:08.440><c> well</c><00:02:08.599><c> I</c><00:02:08.679><c> wouldn't</c><00:02:08.959><c> say</c><00:02:09.319><c> a</c>

00:02:09.510 --> 00:02:09.520 align:start position:0%
totally clueless well I wouldn't say a
 

00:02:09.520 --> 00:02:12.949 align:start position:0%
totally clueless well I wouldn't say a
complete<00:02:10.239><c> [&nbsp;__&nbsp;]</c><00:02:11.239><c> stop</c><00:02:11.520><c> it</c><00:02:12.440><c> I</c><00:02:12.520><c> want</c><00:02:12.720><c> this</c><00:02:12.840><c> to</c>

00:02:12.949 --> 00:02:12.959 align:start position:0%
complete [&nbsp;__&nbsp;] stop it I want this to
 

00:02:12.959 --> 00:02:14.750 align:start position:0%
complete [&nbsp;__&nbsp;] stop it I want this to
work<00:02:13.160><c> automatically</c><00:02:13.879><c> so</c><00:02:14.040><c> I'm</c><00:02:14.160><c> telling</c><00:02:14.440><c> chat</c>

00:02:14.750 --> 00:02:14.760 align:start position:0%
work automatically so I'm telling chat
 

00:02:14.760 --> 00:02:17.990 align:start position:0%
work automatically so I'm telling chat
GPT<00:02:15.360><c> to</c><00:02:15.560><c> instantly</c><00:02:16.080><c> convert</c><00:02:16.480><c> any</c><00:02:16.760><c> webp</c><00:02:17.319><c> files</c>

00:02:17.990 --> 00:02:18.000 align:start position:0%
GPT to instantly convert any webp files
 

00:02:18.000 --> 00:02:19.790 align:start position:0%
GPT to instantly convert any webp files
as<00:02:18.120><c> soon</c><00:02:18.319><c> as</c><00:02:18.480><c> they</c><00:02:18.640><c> hit</c><00:02:18.800><c> my</c><00:02:18.959><c> downloads</c>

00:02:19.790 --> 00:02:19.800 align:start position:0%
as soon as they hit my downloads
 

00:02:19.800 --> 00:02:22.070 align:start position:0%
as soon as they hit my downloads
directory<00:02:20.800><c> since</c><00:02:21.080><c> I've</c><00:02:21.280><c> also</c><00:02:21.560><c> asked</c><00:02:21.800><c> it</c><00:02:21.920><c> to</c>

00:02:22.070 --> 00:02:22.080 align:start position:0%
directory since I've also asked it to
 

00:02:22.080 --> 00:02:23.750 align:start position:0%
directory since I've also asked it to
answer<00:02:22.360><c> me</c><00:02:22.519><c> as</c><00:02:22.680><c> though</c><00:02:22.840><c> I</c><00:02:22.920><c> were</c>

00:02:23.750 --> 00:02:23.760 align:start position:0%
answer me as though I were
 

00:02:23.760 --> 00:02:27.949 align:start position:0%
answer me as though I were
a<00:02:24.760><c> a</c><00:02:24.959><c> novice</c><00:02:25.920><c> chat</c><00:02:26.239><c> GTP</c><00:02:27.120><c> has</c><00:02:27.319><c> provided</c><00:02:27.680><c> me</c>

00:02:27.949 --> 00:02:27.959 align:start position:0%
a a novice chat GTP has provided me
 

00:02:27.959 --> 00:02:30.350 align:start position:0%
a a novice chat GTP has provided me
step-by-step<00:02:28.720><c> instructions</c><00:02:29.400><c> for</c><00:02:29.599><c> set</c><00:02:30.080><c> up</c><00:02:30.200><c> my</c>

00:02:30.350 --> 00:02:30.360 align:start position:0%
step-by-step instructions for set up my
 

00:02:30.360 --> 00:02:32.309 align:start position:0%
step-by-step instructions for set up my
programming<00:02:30.879><c> environment</c><00:02:31.720><c> including</c><00:02:32.160><c> the</c>

00:02:32.309 --> 00:02:32.319 align:start position:0%
programming environment including the
 

00:02:32.319 --> 00:02:34.630 align:start position:0%
programming environment including the
instructions<00:02:32.840><c> for</c><00:02:33.080><c> installing</c><00:02:33.680><c> python</c><00:02:34.360><c> and</c><00:02:34.519><c> a</c>

00:02:34.630 --> 00:02:34.640 align:start position:0%
instructions for installing python and a
 

00:02:34.640 --> 00:02:37.350 align:start position:0%
instructions for installing python and a
couple<00:02:35.000><c> small</c><00:02:35.480><c> simple</c><00:02:35.879><c> supporting</c><00:02:36.400><c> libraries</c>

00:02:37.350 --> 00:02:37.360 align:start position:0%
couple small simple supporting libraries
 

00:02:37.360 --> 00:02:39.309 align:start position:0%
couple small simple supporting libraries
installing<00:02:38.000><c> those</c><00:02:38.319><c> is</c><00:02:38.560><c> just</c><00:02:38.680><c> a</c><00:02:38.840><c> matter</c><00:02:39.080><c> of</c>

00:02:39.309 --> 00:02:39.319 align:start position:0%
installing those is just a matter of
 

00:02:39.319 --> 00:02:41.149 align:start position:0%
installing those is just a matter of
copying<00:02:39.720><c> the</c><00:02:39.840><c> code</c><00:02:40.159><c> and</c><00:02:40.360><c> pasting</c><00:02:40.680><c> it</c><00:02:40.800><c> into</c><00:02:41.040><c> the</c>

00:02:41.149 --> 00:02:41.159 align:start position:0%
copying the code and pasting it into the
 

00:02:41.159 --> 00:02:44.030 align:start position:0%
copying the code and pasting it into the
command<00:02:41.519><c> line</c><00:02:41.720><c> of</c><00:02:41.840><c> a</c><00:02:41.959><c> command</c><00:02:42.360><c> prompt</c><00:02:43.040><c> window</c>

00:02:44.030 --> 00:02:44.040 align:start position:0%
command line of a command prompt window
 

00:02:44.040 --> 00:02:45.589 align:start position:0%
command line of a command prompt window
ignore<00:02:44.400><c> any</c><00:02:44.560><c> warnings</c><00:02:44.959><c> you</c><00:02:45.120><c> get</c><00:02:45.319><c> during</c>

00:02:45.589 --> 00:02:45.599 align:start position:0%
ignore any warnings you get during
 

00:02:45.599 --> 00:02:47.470 align:start position:0%
ignore any warnings you get during
installation<00:02:46.599><c> they're</c><00:02:46.840><c> like</c><00:02:47.040><c> those</c><00:02:47.280><c> red</c>

00:02:47.470 --> 00:02:47.480 align:start position:0%
installation they're like those red
 

00:02:47.480 --> 00:02:49.830 align:start position:0%
installation they're like those red
lights<00:02:47.800><c> on</c><00:02:47.959><c> the</c><00:02:48.120><c> dashboard</c><00:02:48.560><c> of</c><00:02:48.680><c> your</c><00:02:48.879><c> car</c><00:02:49.720><c> they</c>

00:02:49.830 --> 00:02:49.840 align:start position:0%
lights on the dashboard of your car they
 

00:02:49.840 --> 00:02:52.750 align:start position:0%
lights on the dashboard of your car they
mean<00:02:50.440><c> nothing</c><00:02:51.440><c> I'll</c><00:02:51.640><c> create</c><00:02:51.920><c> a</c><00:02:52.080><c> directory</c><00:02:52.519><c> for</c>

00:02:52.750 --> 00:02:52.760 align:start position:0%
mean nothing I'll create a directory for
 

00:02:52.760 --> 00:02:55.309 align:start position:0%
mean nothing I'll create a directory for
our<00:02:53.000><c> web</c><00:02:53.280><c> toping</c><00:02:53.760><c> program</c><00:02:54.560><c> and</c><00:02:54.720><c> pop</c><00:02:54.959><c> open</c><00:02:55.200><c> the</c>

00:02:55.309 --> 00:02:55.319 align:start position:0%
our web toping program and pop open the
 

00:02:55.319 --> 00:02:57.910 align:start position:0%
our web toping program and pop open the
notepad<00:02:55.959><c> text</c><00:02:56.280><c> editor</c><00:02:57.280><c> then</c><00:02:57.400><c> it's</c><00:02:57.599><c> just</c><00:02:57.760><c> a</c>

00:02:57.910 --> 00:02:57.920 align:start position:0%
notepad text editor then it's just a
 

00:02:57.920 --> 00:02:59.470 align:start position:0%
notepad text editor then it's just a
simple<00:02:58.159><c> matter</c><00:02:58.400><c> of</c><00:02:58.519><c> saving</c><00:02:58.840><c> our</c><00:02:59.080><c> file</c><00:02:59.319><c> and</c>

00:02:59.470 --> 00:02:59.480 align:start position:0%
simple matter of saving our file and
 

00:02:59.480 --> 00:03:02.470 align:start position:0%
simple matter of saving our file and
running<00:02:59.720><c> it</c><00:03:00.319><c> it</c><00:03:01.319><c> I'll</c><00:03:01.480><c> need</c><00:03:01.640><c> to</c><00:03:01.760><c> find</c><00:03:01.959><c> a</c><00:03:02.159><c> test</c>

00:03:02.470 --> 00:03:02.480 align:start position:0%
running it it I'll need to find a test
 

00:03:02.480 --> 00:03:04.630 align:start position:0%
running it it I'll need to find a test
image<00:03:02.760><c> to</c><00:03:03.120><c> O</c><00:03:03.280><c> Asian</c><00:03:03.519><c> cheerleaders</c><00:03:04.040><c> Asian</c>

00:03:04.630 --> 00:03:04.640 align:start position:0%
image to O Asian cheerleaders Asian
 

00:03:04.640 --> 00:03:07.229 align:start position:0%
image to O Asian cheerleaders Asian
cheerleaders<00:03:05.640><c> that</c><00:03:05.799><c> didn't</c><00:03:06.080><c> work</c><00:03:06.799><c> for</c><00:03:07.000><c> some</c>

00:03:07.229 --> 00:03:07.239 align:start position:0%
cheerleaders that didn't work for some
 

00:03:07.239 --> 00:03:09.509 align:start position:0%
cheerleaders that didn't work for some
reason<00:03:07.879><c> all</c><00:03:08.120><c> Asian</c><00:03:08.440><c> cheerleader</c><00:03:09.040><c> images</c><00:03:09.360><c> are</c>

00:03:09.509 --> 00:03:09.519 align:start position:0%
reason all Asian cheerleader images are
 

00:03:09.519 --> 00:03:11.990 align:start position:0%
reason all Asian cheerleader images are
stored<00:03:09.920><c> as</c><00:03:10.319><c> jpegs</c><00:03:11.319><c> let</c><00:03:11.400><c> me</c><00:03:11.560><c> search</c><00:03:11.799><c> for</c>

00:03:11.990 --> 00:03:12.000 align:start position:0%
stored as jpegs let me search for
 

00:03:12.000 --> 00:03:13.270 align:start position:0%
stored as jpegs let me search for
something<00:03:12.280><c> else</c>

00:03:13.270 --> 00:03:13.280 align:start position:0%
something else
 

00:03:13.280 --> 00:03:17.030 align:start position:0%
something else
instead<00:03:14.280><c> really</c><00:03:14.840><c> a</c><00:03:15.440><c> booger</c><00:03:16.440><c> well</c><00:03:16.599><c> I</c><00:03:16.720><c> had</c><00:03:16.840><c> to</c>

00:03:17.030 --> 00:03:17.040 align:start position:0%
instead really a booger well I had to
 

00:03:17.040 --> 00:03:22.789 align:start position:0%
instead really a booger well I had to
pick

00:03:22.789 --> 00:03:22.799 align:start position:0%
 
 

00:03:22.799 --> 00:03:25.509 align:start position:0%
 
something<00:03:23.799><c> yeah</c><00:03:24.760><c> you're</c><00:03:24.959><c> going</c><00:03:25.080><c> to</c><00:03:25.239><c> have</c><00:03:25.400><c> a</c>

00:03:25.509 --> 00:03:25.519 align:start position:0%
something yeah you're going to have a
 

00:03:25.519 --> 00:03:27.270 align:start position:0%
something yeah you're going to have a
million<00:03:25.920><c> subscribers</c><00:03:26.440><c> in</c><00:03:26.640><c> no</c><00:03:26.840><c> time</c><00:03:27.080><c> with</c>

00:03:27.270 --> 00:03:27.280 align:start position:0%
million subscribers in no time with
 

00:03:27.280 --> 00:03:29.509 align:start position:0%
million subscribers in no time with
material<00:03:27.799><c> like</c><00:03:28.040><c> that</c><00:03:28.959><c> it</c><00:03:29.080><c> didn't</c><00:03:29.319><c> work</c>

00:03:29.509 --> 00:03:29.519 align:start position:0%
material like that it didn't work
 

00:03:29.519 --> 00:03:31.070 align:start position:0%
material like that it didn't work
because<00:03:29.959><c> my</c><00:03:30.120><c> program</c><00:03:30.439><c> wasn't</c><00:03:30.799><c> actually</c>

00:03:31.070 --> 00:03:31.080 align:start position:0%
because my program wasn't actually
 

00:03:31.080 --> 00:03:33.350 align:start position:0%
because my program wasn't actually
running<00:03:31.959><c> here's</c><00:03:32.120><c> a</c><00:03:32.319><c> tip</c><00:03:32.480><c> for</c><00:03:32.640><c> all</c><00:03:32.760><c> you</c><00:03:32.920><c> novice</c>

00:03:33.350 --> 00:03:33.360 align:start position:0%
running here's a tip for all you novice
 

00:03:33.360 --> 00:03:35.390 align:start position:0%
running here's a tip for all you novice
programmers<00:03:33.879><c> out</c><00:03:34.120><c> there</c><00:03:34.640><c> in</c><00:03:34.799><c> order</c><00:03:35.040><c> for</c><00:03:35.200><c> your</c>

00:03:35.390 --> 00:03:35.400 align:start position:0%
programmers out there in order for your
 

00:03:35.400 --> 00:03:37.429 align:start position:0%
programmers out there in order for your
programs<00:03:35.840><c> to</c><00:03:36.000><c> work</c><00:03:36.519><c> you</c><00:03:36.760><c> have</c><00:03:36.879><c> to</c><00:03:37.159><c> actually</c>

00:03:37.429 --> 00:03:37.439 align:start position:0%
programs to work you have to actually
 

00:03:37.439 --> 00:03:40.949 align:start position:0%
programs to work you have to actually
run<00:03:37.959><c> them</c><00:03:38.959><c> you</c><00:03:39.200><c> my</c><00:03:39.360><c> friend</c><00:03:39.599><c> put</c><00:03:39.760><c> the</c><00:03:39.959><c> dumb</c><00:03:40.280><c> in</c>

00:03:40.949 --> 00:03:40.959 align:start position:0%
run them you my friend put the dumb in
 

00:03:40.959 --> 00:03:43.789 align:start position:0%
run them you my friend put the dumb in
wisdom<00:03:41.959><c> you're</c><00:03:42.280><c> welcome</c><00:03:43.280><c> now</c><00:03:43.480><c> that</c><00:03:43.599><c> our</c>

00:03:43.789 --> 00:03:43.799 align:start position:0%
wisdom you're welcome now that our
 

00:03:43.799 --> 00:03:45.589 align:start position:0%
wisdom you're welcome now that our
program's<00:03:44.239><c> running</c><00:03:44.760><c> we'll</c><00:03:45.000><c> try</c><00:03:45.159><c> to</c><00:03:45.319><c> save</c>

00:03:45.589 --> 00:03:45.599 align:start position:0%
program's running we'll try to save
 

00:03:45.599 --> 00:03:47.429 align:start position:0%
program's running we'll try to save
another

00:03:47.429 --> 00:03:47.439 align:start position:0%
another
 

00:03:47.439 --> 00:03:49.949 align:start position:0%
another
booger<00:03:48.439><c> and</c><00:03:48.599><c> we</c><00:03:48.720><c> have</c><00:03:48.840><c> an</c><00:03:49.000><c> error</c><00:03:49.599><c> not</c><00:03:49.760><c> a</c>

00:03:49.949 --> 00:03:49.959 align:start position:0%
booger and we have an error not a
 

00:03:49.959 --> 00:03:52.710 align:start position:0%
booger and we have an error not a
problem<00:03:50.319><c> though</c><00:03:50.760><c> since</c><00:03:51.159><c> chat</c><00:03:51.480><c> GPT</c><00:03:52.319><c> made</c><00:03:52.560><c> the</c>

00:03:52.710 --> 00:03:52.720 align:start position:0%
problem though since chat GPT made the
 

00:03:52.720 --> 00:03:56.190 align:start position:0%
problem though since chat GPT made the
error<00:03:53.280><c> chat</c><00:03:53.599><c> GPT</c><00:03:54.480><c> can</c><00:03:54.680><c> fix</c><00:03:54.959><c> the</c><00:03:55.120><c> error</c><00:03:55.959><c> over</c>

00:03:56.190 --> 00:03:56.200 align:start position:0%
error chat GPT can fix the error over
 

00:03:56.200 --> 00:03:58.270 align:start position:0%
error chat GPT can fix the error over
the<00:03:56.360><c> next</c><00:03:56.599><c> couple</c><00:03:56.920><c> minutes</c><00:03:57.360><c> I'll</c><00:03:57.599><c> cut</c><00:03:58.000><c> copy</c>

00:03:58.270 --> 00:03:58.280 align:start position:0%
the next couple minutes I'll cut copy
 

00:03:58.280 --> 00:04:00.630 align:start position:0%
the next couple minutes I'll cut copy
and<00:03:58.519><c> paste</c><00:03:58.799><c> the</c><00:03:58.920><c> code</c><00:03:59.200><c> written</c><00:03:59.439><c> by</c><00:03:59.799><c> our</c><00:04:00.000><c> Ai</c><00:04:00.439><c> and</c>

00:04:00.630 --> 00:04:00.640 align:start position:0%
and paste the code written by our Ai and
 

00:04:00.640 --> 00:04:03.630 align:start position:0%
and paste the code written by our Ai and
keep<00:04:00.959><c> retrying</c><00:04:01.640><c> until</c><00:04:01.879><c> it</c><00:04:02.480><c> works</c><00:04:03.480><c> and</c>

00:04:03.630 --> 00:04:03.640 align:start position:0%
keep retrying until it works and
 

00:04:03.640 --> 00:04:06.350 align:start position:0%
keep retrying until it works and
eventually<00:04:04.040><c> it</c><00:04:04.200><c> does</c><00:04:04.560><c> work</c><00:04:05.560><c> not</c><00:04:05.799><c> counting</c><00:04:06.159><c> the</c>

00:04:06.350 --> 00:04:06.360 align:start position:0%
eventually it does work not counting the
 

00:04:06.360 --> 00:04:08.550 align:start position:0%
eventually it does work not counting the
time<00:04:06.599><c> spent</c><00:04:06.920><c> redoing</c><00:04:07.400><c> screen</c><00:04:07.920><c> captures</c><00:04:08.319><c> for</c>

00:04:08.550 --> 00:04:08.560 align:start position:0%
time spent redoing screen captures for
 

00:04:08.560 --> 00:04:10.670 align:start position:0%
time spent redoing screen captures for
this<00:04:08.720><c> video</c><00:04:09.400><c> it</c><00:04:09.519><c> took</c><00:04:09.760><c> about</c><00:04:09.920><c> 8</c><00:04:10.200><c> minutes</c><00:04:10.519><c> to</c>

00:04:10.670 --> 00:04:10.680 align:start position:0%
this video it took about 8 minutes to
 

00:04:10.680 --> 00:04:13.789 align:start position:0%
this video it took about 8 minutes to
get<00:04:10.920><c> web</c><00:04:11.239><c> to</c><00:04:11.480><c> Ping</c><00:04:11.959><c> designed</c><00:04:12.599><c> built</c><00:04:13.159><c> tested</c>

00:04:13.789 --> 00:04:13.799 align:start position:0%
get web to Ping designed built tested
 

00:04:13.799 --> 00:04:16.909 align:start position:0%
get web to Ping designed built tested
installed<00:04:14.519><c> and</c><00:04:14.680><c> deployed</c><00:04:15.120><c> out</c><00:04:15.280><c> to</c><00:04:15.600><c> GitHub</c><00:04:16.600><c> all</c>

00:04:16.909 --> 00:04:16.919 align:start position:0%
installed and deployed out to GitHub all
 

00:04:16.919 --> 00:04:19.390 align:start position:0%
installed and deployed out to GitHub all
without<00:04:17.199><c> doing</c><00:04:17.560><c> any</c><00:04:17.919><c> actual</c><00:04:18.479><c> programming</c>

00:04:19.390 --> 00:04:19.400 align:start position:0%
without doing any actual programming
 

00:04:19.400 --> 00:04:21.830 align:start position:0%
without doing any actual programming
writing<00:04:19.880><c> any</c><00:04:20.160><c> code</c><00:04:20.600><c> or</c><00:04:20.799><c> even</c><00:04:21.079><c> opening</c><00:04:21.560><c> my</c>

00:04:21.830 --> 00:04:21.840 align:start position:0%
writing any code or even opening my
 

00:04:21.840 --> 00:04:23.870 align:start position:0%
writing any code or even opening my
visual<00:04:22.199><c> Studio</c><00:04:22.720><c> software</c><00:04:23.160><c> development</c>

00:04:23.870 --> 00:04:23.880 align:start position:0%
visual Studio software development
 

00:04:23.880 --> 00:04:27.430 align:start position:0%
visual Studio software development
environment<00:04:24.880><c> what</c><00:04:25.040><c> a</c><00:04:25.240><c> time</c><00:04:25.400><c> to</c><00:04:25.600><c> be</c><00:04:26.160><c> alive</c><00:04:27.160><c> not</c>

00:04:27.430 --> 00:04:27.440 align:start position:0%
environment what a time to be alive not
 

00:04:27.440 --> 00:04:30.870 align:start position:0%
environment what a time to be alive not
your<00:04:27.759><c> thing</c><00:04:28.520><c> cool</c><00:04:28.880><c> take</c><00:04:29.080><c> a</c><00:04:29.320><c> hike</c><00:04:30.080><c> otherwise</c>

00:04:30.870 --> 00:04:30.880 align:start position:0%
your thing cool take a hike otherwise
 

00:04:30.880 --> 00:04:35.469 align:start position:0%
your thing cool take a hike otherwise
subscribe<00:04:31.479><c> and</c><00:04:31.800><c> like</c><00:04:32.560><c> to</c><00:04:32.960><c> AI</c><00:04:33.479><c> tips</c><00:04:33.840><c> with</c>

00:04:35.469 --> 00:04:35.479 align:start position:0%
subscribe and like to AI tips with
 

00:04:35.479 --> 00:04:41.189 align:start position:0%
subscribe and like to AI tips with
J<00:04:36.479><c> AI</c><00:04:37.000><c> tips</c><00:04:37.320><c> with</c><00:04:37.520><c> J</c><00:04:38.320><c> hooray</c><00:04:38.880><c> a</c><00:04:39.039><c> i</c><00:04:39.400><c> a</c><00:04:39.600><c> i</c><00:04:39.960><c> a</c><00:04:40.120><c> i</c><00:04:40.440><c> a</c><00:04:40.639><c> i</c>

00:04:41.189 --> 00:04:41.199 align:start position:0%
J AI tips with J hooray a i a i a i a i
 

00:04:41.199 --> 00:04:43.230 align:start position:0%
J AI tips with J hooray a i a i a i a i
AI<00:04:41.639><c> tips</c><00:04:42.000><c> with</c>

00:04:43.230 --> 00:04:43.240 align:start position:0%
AI tips with
 

00:04:43.240 --> 00:04:45.950 align:start position:0%
AI tips with
J<00:04:44.240><c> AI</c><00:04:44.520><c> tips</c><00:04:44.759><c> with</c><00:04:44.880><c> J</c><00:04:45.080><c> is</c><00:04:45.199><c> a</c><00:04:45.360><c> copyrighted</c>

00:04:45.950 --> 00:04:45.960 align:start position:0%
J AI tips with J is a copyrighted
 

00:04:45.960 --> 00:04:47.150 align:start position:0%
J AI tips with J is a copyrighted
production<00:04:46.320><c> of</c>

00:04:47.150 --> 00:04:47.160 align:start position:0%
production of
 

00:04:47.160 --> 00:04:51.390 align:start position:0%
production of
j.g.<00:04:48.160><c> us</c><00:04:49.000><c> all</c><00:04:49.199><c> rights</c><00:04:49.479><c> reserved</c><00:04:49.960><c> by</c><00:04:50.560><c> AI</c><00:04:51.000><c> tips</c>

00:04:51.390 --> 00:04:51.400 align:start position:0%
j.g. us all rights reserved by AI tips
 

00:04:51.400 --> 00:04:54.600 align:start position:0%
j.g. us all rights reserved by AI tips
with<00:04:51.600><c> j</c>

