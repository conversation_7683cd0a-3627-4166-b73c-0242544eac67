#!/usr/bin/env python3
"""
Comprehensive Google GenAI Chat Manager Demo

This demo shows conversation memory functionality using Google GenAI's built-in chat client.
It includes basic chat, persistent memory, and context building examples.
"""

import asyncio
import os
import logging
import uuid
import json
from typing import Optional, List, Dict, Any
from datetime import datetime
from dataclasses import dataclass, field
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import required libraries
try:
    from dotenv import load_dotenv, find_dotenv
    load_dotenv(find_dotenv(filename='.env.local', raise_error_if_not_found=False) or 
                find_dotenv(filename='.env', raise_error_if_not_found=False))
    
    import google.genai as genai
    from google.genai import types
    from google.genai.types import HarmCategory, HarmBlockThreshold, SafetySettingDict
    
    print("✅ Successfully imported Google GenAI SDK")
except ImportError as e:
    print(f"❌ Error importing required libraries: {e}")
    print("Please install: pip install google-genai python-dotenv")
    exit(1)

# Chat configuration
DEFAULT_MODEL_NAME = "gemini-2.5-flash-preview-04-17"

DEFAULT_GENERATION_CONFIG = {
    "temperature": 0.10,
    "top_p": 0.95,
    "top_k": 40, 
    "max_output_tokens": 65536,
}

DEFAULT_SAFETY_SETTINGS = [
    types.SafetySettingDict(
        category=HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    types.SafetySettingDict(
        category=HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    types.SafetySettingDict(
        category=HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    types.SafetySettingDict(
        category=HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
]

@dataclass
class ChatMessage:
    """Represents a single message in the chat."""
    role: str  # 'user' or 'model'
    content: str
    timestamp: datetime = field(default_factory=datetime.now)
    token_count: Optional[int] = None
    cost: Optional[float] = None

@dataclass
class ChatSession:
    """Represents a chat session with conversation history."""
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    model_name: str = DEFAULT_MODEL_NAME
    system_instruction: Optional[str] = None
    messages: List[ChatMessage] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    total_cost: float = 0.0
    
    def add_message(self, role: str, content: str, token_count: Optional[int] = None, cost: Optional[float] = None):
        """Add a message to the session."""
        message = ChatMessage(role=role, content=content, token_count=token_count, cost=cost)
        self.messages.append(message)
        if cost:
            self.total_cost += cost
        return message
    
    def get_conversation_history(self) -> List[Dict[str, str]]:
        """Get conversation history in Google GenAI format."""
        history = []
        for msg in self.messages:
            history.append({
                "role": msg.role,
                "parts": [{"text": msg.content}]
            })
        return history

class SimpleChatManager:
    """Simplified chat manager for demonstration purposes."""
    
    def __init__(self):
        """Initialize the chat manager."""
        # Initialize Google GenAI client
        api_key = os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("API Key not found. Set GOOGLE_API_KEY or GEMINI_API_KEY environment variable.")
        
        self.client = genai.Client(api_key=api_key)
        self.sessions: Dict[str, ChatSession] = {}
        
        print(f"🤖 Chat Manager initialized with model: {DEFAULT_MODEL_NAME}")
    
    def create_session(self, system_instruction: Optional[str] = None) -> str:
        """Create a new chat session."""
        session = ChatSession(
            model_name=DEFAULT_MODEL_NAME,
            system_instruction=system_instruction
        )
        self.sessions[session.session_id] = session
        
        print(f"📝 Created new chat session: {session.session_id[:8]}...")
        return session.session_id
    
    async def send_message(self, session_id: str, user_message: str) -> str:
        """Send a message and get response with conversation memory."""
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found")
        
        session = self.sessions[session_id]
        
        # Add user message to history
        session.add_message("user", user_message)
        
        # Prepare conversation history for API
        conversation_history = session.get_conversation_history()
        
        # Prepare generation config
        config = DEFAULT_GENERATION_CONFIG.copy()
        config["safety_settings"] = DEFAULT_SAFETY_SETTINGS
        
        if session.system_instruction:
            config["system_instruction"] = session.system_instruction
        
        try:
            # Make API call with conversation history
            response = await self.client.aio.models.generate_content(
                model=session.model_name,
                contents=conversation_history,
                config=types.GenerateContentConfig(**config)
            )
            
            if not response or not response.text:
                raise ValueError("Empty response from model")
            
            # Add model response to history
            session.add_message("model", response.text)
            
            # Extract token usage if available
            input_tokens = getattr(response.usage_metadata, 'prompt_token_count', None) if hasattr(response, 'usage_metadata') else None
            output_tokens = getattr(response.usage_metadata, 'candidates_token_count', None) if hasattr(response, 'usage_metadata') else None
            
            print(f"💬 Exchange complete. Tokens: {input_tokens or 'N/A'} input, {output_tokens or 'N/A'} output")
            
            return response.text
            
        except Exception as e:
            logger.error(f"Error in chat exchange: {e}")
            raise
    
    def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """Get session summary."""
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found")
        
        session = self.sessions[session_id]
        return {
            "session_id": session.session_id,
            "model_name": session.model_name,
            "total_messages": len(session.messages),
            "total_cost": session.total_cost,
            "created_at": session.created_at,
            "last_message_at": session.messages[-1].timestamp if session.messages else session.created_at
        }

async def demo_basic_conversation():
    """Demo: Basic conversation with memory."""
    print("\n" + "="*60)
    print("🧠 DEMO 1: Basic Conversation with Memory")
    print("="*60)
    
    manager = SimpleChatManager()
    session_id = manager.create_session(
        system_instruction="You are a helpful assistant that remembers our conversation."
    )
    
    # First exchange
    print("\n👤 User: Hi! My name is Alice and I love programming in Python.")
    response1 = await manager.send_message(
        session_id, 
        "Hi! My name is Alice and I love programming in Python."
    )
    print(f"🤖 Assistant: {response1}")
    
    # Second exchange - should remember Alice's name
    print("\n👤 User: What programming languages would you recommend for someone like me?")
    response2 = await manager.send_message(
        session_id,
        "What programming languages would you recommend for someone like me?"
    )
    print(f"🤖 Assistant: {response2}")
    
    # Third exchange - test memory
    print("\n👤 User: Can you remind me what I told you about myself earlier?")
    response3 = await manager.send_message(
        session_id,
        "Can you remind me what I told you about myself earlier?"
    )
    print(f"🤖 Assistant: {response3}")
    
    # Show session summary
    summary = manager.get_session_summary(session_id)
    print(f"\n📊 Session Summary:")
    print(f"   • Messages: {summary['total_messages']}")
    print(f"   • Duration: {summary['created_at'].strftime('%H:%M:%S')} to {summary['last_message_at'].strftime('%H:%M:%S')}")

async def demo_context_building():
    """Demo: Context building across multiple exchanges."""
    print("\n" + "="*60)
    print("📚 DEMO 2: Context Building - Math Tutoring")
    print("="*60)
    
    manager = SimpleChatManager()
    session_id = manager.create_session(
        system_instruction="You are a helpful math tutor. Remember the student's progress and build on previous explanations."
    )
    
    exchanges = [
        "Hi! I'm struggling with algebra. Can you help me understand what variables are?",
        "That's helpful! Now can you give me an example using the variable concept you just explained?",
        "Perfect! Now, based on what we've discussed about variables, how would I solve: 2x + 5 = 11?",
        "Great! Can you now create a similar problem for me to practice, using the same concepts?"
    ]
    
    for i, message in enumerate(exchanges, 1):
        print(f"\n👤 Student Message {i}: {message}")
        response = await manager.send_message(session_id, message)
        print(f"🤖 Tutor Response {i}: {response}")
    
    # Show how context builds
    session = manager.sessions[session_id]
    print(f"\n📈 Context Growth: Started with {len(exchanges[0].split())} words, session now has {sum(len(msg.content.split()) for msg in session.messages)} total words")

async def demo_game_with_memory():
    """Demo: Playing a game that requires memory."""
    print("\n" + "="*60)
    print("🎮 DEMO 3: Tic-Tac-Toe Game with Memory")
    print("="*60)
    
    manager = SimpleChatManager()
    session_id = manager.create_session(
        system_instruction="You are playing tic-tac-toe. Keep track of the game state, show the board after each move, and play strategically. User is X, you are O."
    )
    
    moves = [
        "Let's play tic-tac-toe! I'll be X and you'll be O. I choose the center position (5).",
        "I'll take position 1 (top-left corner).",
        "I'll take position 9 (bottom-right corner).",
        "Good game! What was the final outcome?"
    ]
    
    for i, move in enumerate(moves, 1):
        print(f"\n👤 Player Move {i}: {move}")
        response = await manager.send_message(session_id, move)
        print(f"🤖 Game Response {i}: {response}")

async def demo_quick_chat():
    """Demo: Quick one-off chat without session management."""
    print("\n" + "="*60)
    print("⚡ DEMO 4: Quick Chat (No Session Management)")
    print("="*60)
    
    manager = SimpleChatManager()
    session_id = manager.create_session(
        system_instruction="You are a science teacher explaining complex topics simply."
    )
    
    print("\n👤 User: Explain quantum computing in simple terms.")
    response = await manager.send_message(
        session_id,
        "Explain quantum computing in simple terms."
    )
    print(f"🤖 Teacher: {response}")

async def main():
    """Run all demos."""
    print("🚀 Google GenAI Chat Manager - Conversation Memory Demo")
    print("Built using Google GenAI SDK's client.chats.create() equivalent functionality")
    
    # Check API key
    api_key = os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ Error: No API key found. Set GOOGLE_API_KEY or GEMINI_API_KEY environment variable.")
        print("   Example: export GOOGLE_API_KEY='your-api-key-here'")
        return
    
    try:
        print("✅ API key found, starting demos...")
        
        await demo_basic_conversation()
        await demo_context_building()
        await demo_game_with_memory()
        await demo_quick_chat()
        
        print("\n" + "="*60)
        print("✅ All demos completed successfully!")
        print("🎉 The chat system successfully maintains conversation memory")
        print("   across multiple API calls using Google GenAI's built-in features.")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        logger.exception("Demo error")
        return 1

if __name__ == "__main__":
    # Run the demo
    exit_code = asyncio.run(main())
    if exit_code:
        exit(exit_code) 