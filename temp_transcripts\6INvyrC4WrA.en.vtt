WEBVTT
Kind: captions
Language: en

00:00:00.160 --> 00:00:03.429 align:start position:0%
 
hello<00:00:00.399><c> everyone</c><00:00:01.000><c> Ravi</c><00:00:01.360><c> here</c><00:00:01.520><c> from</c><00:00:01.680><c> Lama</c><00:00:02.439><c> index</c>

00:00:03.429 --> 00:00:03.439 align:start position:0%
hello everyone Ravi here from <PERSON> index
 

00:00:03.439 --> 00:00:05.950 align:start position:0%
hello everyone Ravi here from <PERSON> index
welcome<00:00:03.760><c> to</c><00:00:04.040><c> another</c><00:00:04.359><c> video</c><00:00:04.720><c> in</c><00:00:04.920><c> the</c><00:00:05.520><c> agents</c>

00:00:05.950 --> 00:00:05.960 align:start position:0%
welcome to another video in the agents
 

00:00:05.960 --> 00:00:08.709 align:start position:0%
welcome to another video in the agents
tutorial<00:00:06.399><c> video</c><00:00:06.680><c> series</c><00:00:07.680><c> so</c><00:00:08.440><c> in</c><00:00:08.599><c> this</c>

00:00:08.709 --> 00:00:08.719 align:start position:0%
tutorial video series so in this
 

00:00:08.719 --> 00:00:11.190 align:start position:0%
tutorial video series so in this
notebook<00:00:09.440><c> uh</c><00:00:09.559><c> we'll</c><00:00:09.760><c> look</c><00:00:09.960><c> into</c><00:00:10.679><c> uh</c><00:00:10.840><c> function</c>

00:00:11.190 --> 00:00:11.200 align:start position:0%
notebook uh we'll look into uh function
 

00:00:11.200 --> 00:00:14.390 align:start position:0%
notebook uh we'll look into uh function
calling<00:00:11.799><c> agent</c><00:00:12.799><c> um</c><00:00:13.200><c> earlier</c><00:00:13.559><c> we</c><00:00:13.679><c> have</c><00:00:13.839><c> seen</c><00:00:14.280><c> uh</c>

00:00:14.390 --> 00:00:14.400 align:start position:0%
calling agent um earlier we have seen uh
 

00:00:14.400 --> 00:00:16.870 align:start position:0%
calling agent um earlier we have seen uh
react<00:00:14.759><c> agent</c><00:00:15.360><c> framework</c><00:00:16.199><c> how</c><00:00:16.320><c> you</c><00:00:16.440><c> can</c><00:00:16.600><c> use</c>

00:00:16.870 --> 00:00:16.880 align:start position:0%
react agent framework how you can use
 

00:00:16.880 --> 00:00:18.710 align:start position:0%
react agent framework how you can use
react<00:00:17.199><c> agent</c><00:00:17.439><c> framework</c><00:00:17.880><c> with</c><00:00:18.080><c> llama</c><00:00:18.400><c> index</c>

00:00:18.710 --> 00:00:18.720 align:start position:0%
react agent framework with llama index
 

00:00:18.720 --> 00:00:20.710 align:start position:0%
react agent framework with llama index
on<00:00:18.920><c> simple</c><00:00:19.199><c> calculator</c><00:00:19.680><c> tools</c><00:00:19.960><c> and</c><00:00:20.199><c> rack</c><00:00:20.480><c> wi</c>

00:00:20.710 --> 00:00:20.720 align:start position:0%
on simple calculator tools and rack wi
 

00:00:20.720 --> 00:00:23.790 align:start position:0%
on simple calculator tools and rack wi
engine<00:00:21.439><c> tools</c><00:00:22.439><c> um</c><00:00:22.800><c> so</c><00:00:23.000><c> react</c><00:00:23.320><c> agent</c><00:00:23.599><c> is</c><00:00:23.680><c> a</c>

00:00:23.790 --> 00:00:23.800 align:start position:0%
engine tools um so react agent is a
 

00:00:23.800 --> 00:00:25.910 align:start position:0%
engine tools um so react agent is a
three<00:00:24.039><c> step</c><00:00:24.240><c> framework</c><00:00:24.760><c> with</c>

00:00:25.910 --> 00:00:25.920 align:start position:0%
three step framework with
 

00:00:25.920 --> 00:00:29.509 align:start position:0%
three step framework with
uh<00:00:26.920><c> thought</c><00:00:27.480><c> action</c><00:00:27.920><c> and</c><00:00:28.119><c> observation</c><00:00:28.800><c> right</c>

00:00:29.509 --> 00:00:29.519 align:start position:0%
uh thought action and observation right
 

00:00:29.519 --> 00:00:32.429 align:start position:0%
uh thought action and observation right
uh<00:00:29.640><c> but</c><00:00:29.800><c> then</c><00:00:30.199><c> llms</c><00:00:30.679><c> are</c><00:00:30.880><c> capable</c><00:00:31.359><c> of</c><00:00:32.239><c> by</c>

00:00:32.429 --> 00:00:32.439 align:start position:0%
uh but then llms are capable of by
 

00:00:32.439 --> 00:00:35.910 align:start position:0%
uh but then llms are capable of by
default<00:00:33.320><c> uh</c><00:00:33.640><c> choosing</c><00:00:34.640><c> one</c><00:00:34.800><c> of</c><00:00:35.000><c> these</c><00:00:35.280><c> tools</c>

00:00:35.910 --> 00:00:35.920 align:start position:0%
default uh choosing one of these tools
 

00:00:35.920 --> 00:00:38.830 align:start position:0%
default uh choosing one of these tools
using<00:00:36.440><c> function</c><00:00:36.800><c> calling</c><00:00:37.239><c> capabilities</c><00:00:38.239><c> so</c><00:00:38.719><c> a</c>

00:00:38.830 --> 00:00:38.840 align:start position:0%
using function calling capabilities so a
 

00:00:38.840 --> 00:00:41.630 align:start position:0%
using function calling capabilities so a
function<00:00:39.200><c> calling</c><00:00:39.600><c> agent</c><00:00:40.079><c> provides</c><00:00:41.000><c> uh</c><00:00:41.239><c> this</c>

00:00:41.630 --> 00:00:41.640 align:start position:0%
function calling agent provides uh this
 

00:00:41.640 --> 00:00:45.590 align:start position:0%
function calling agent provides uh this
abstraction<00:00:42.600><c> over</c><00:00:43.600><c> um</c><00:00:43.920><c> the</c><00:00:44.840><c> llm</c><00:00:45.280><c> function</c>

00:00:45.590 --> 00:00:45.600 align:start position:0%
abstraction over um the llm function
 

00:00:45.600 --> 00:00:48.869 align:start position:0%
abstraction over um the llm function
calling<00:00:46.160><c> uh</c><00:00:46.640><c> capabilities</c><00:00:47.640><c> so</c><00:00:47.800><c> we'll</c><00:00:48.039><c> use</c><00:00:48.640><c> uh</c>

00:00:48.869 --> 00:00:48.879 align:start position:0%
calling uh capabilities so we'll use uh
 

00:00:48.879 --> 00:00:51.430 align:start position:0%
calling uh capabilities so we'll use uh
function<00:00:49.239><c> calling</c><00:00:49.800><c> agent</c><00:00:50.800><c> uh</c><00:00:50.920><c> instead</c><00:00:51.239><c> of</c>

00:00:51.430 --> 00:00:51.440 align:start position:0%
function calling agent uh instead of
 

00:00:51.440 --> 00:00:54.310 align:start position:0%
function calling agent uh instead of
react<00:00:51.840><c> agent</c><00:00:52.399><c> in</c><00:00:52.559><c> this</c><00:00:52.719><c> notebook</c><00:00:53.440><c> on</c><00:00:53.600><c> the</c><00:00:53.719><c> same</c>

00:00:54.310 --> 00:00:54.320 align:start position:0%
react agent in this notebook on the same
 

00:00:54.320 --> 00:00:56.549 align:start position:0%
react agent in this notebook on the same
uh<00:00:54.480><c> calculator</c><00:00:54.960><c> tools</c><00:00:55.320><c> and</c><00:00:55.520><c> qu</c><00:00:55.800><c> engine</c><00:00:56.079><c> tools</c>

00:00:56.549 --> 00:00:56.559 align:start position:0%
uh calculator tools and qu engine tools
 

00:00:56.559 --> 00:01:00.389 align:start position:0%
uh calculator tools and qu engine tools
and<00:00:56.719><c> then</c><00:00:56.879><c> see</c><00:00:57.320><c> how</c><00:00:58.239><c> the</c><00:00:58.559><c> agent</c><00:00:58.920><c> can</c><00:01:00.000><c> use</c><00:01:00.239><c> one</c>

00:01:00.389 --> 00:01:00.399 align:start position:0%
and then see how the agent can use one
 

00:01:00.399 --> 00:01:02.150 align:start position:0%
and then see how the agent can use one
of<00:01:00.559><c> the</c><00:01:00.680><c> tools</c><00:01:01.000><c> or</c><00:01:01.160><c> multiple</c><00:01:01.559><c> tools</c><00:01:01.920><c> to</c>

00:01:02.150 --> 00:01:02.160 align:start position:0%
of the tools or multiple tools to
 

00:01:02.160 --> 00:01:05.190 align:start position:0%
of the tools or multiple tools to
complete<00:01:02.559><c> user</c><00:01:03.039><c> task</c><00:01:03.399><c> or</c><00:01:03.640><c> message</c>

00:01:05.190 --> 00:01:05.200 align:start position:0%
complete user task or message
 

00:01:05.200 --> 00:01:07.550 align:start position:0%
complete user task or message
right<00:01:06.200><c> so</c><00:01:06.640><c> that</c><00:01:06.720><c> is</c><00:01:06.840><c> a</c><00:01:07.000><c> difference</c><00:01:07.320><c> basic</c>

00:01:07.550 --> 00:01:07.560 align:start position:0%
right so that is a difference basic
 

00:01:07.560 --> 00:01:08.910 align:start position:0%
right so that is a difference basic
difference<00:01:07.960><c> between</c><00:01:08.200><c> react</c><00:01:08.479><c> agent</c><00:01:08.759><c> and</c>

00:01:08.910 --> 00:01:08.920 align:start position:0%
difference between react agent and
 

00:01:08.920 --> 00:01:12.230 align:start position:0%
difference between react agent and
function<00:01:09.200><c> calling</c><00:01:09.560><c> agent</c><00:01:10.240><c> so</c><00:01:10.520><c> if</c><00:01:10.720><c> any</c><00:01:10.920><c> llm</c><00:01:11.400><c> has</c>

00:01:12.230 --> 00:01:12.240 align:start position:0%
function calling agent so if any llm has
 

00:01:12.240 --> 00:01:14.710 align:start position:0%
function calling agent so if any llm has
a<00:01:12.920><c> function</c><00:01:13.240><c> calling</c><00:01:13.600><c> capability</c><00:01:14.320><c> uh</c><00:01:14.439><c> you</c><00:01:14.520><c> can</c>

00:01:14.710 --> 00:01:14.720 align:start position:0%
a function calling capability uh you can
 

00:01:14.720 --> 00:01:16.950 align:start position:0%
a function calling capability uh you can
directly<00:01:15.040><c> use</c><00:01:15.240><c> function</c><00:01:15.560><c> calling</c><00:01:15.920><c> agent</c><00:01:16.759><c> and</c>

00:01:16.950 --> 00:01:16.960 align:start position:0%
directly use function calling agent and
 

00:01:16.960 --> 00:01:19.710 align:start position:0%
directly use function calling agent and
then<00:01:17.320><c> see</c><00:01:17.880><c> uh</c><00:01:18.000><c> how</c><00:01:18.159><c> it</c><00:01:18.320><c> performs</c><00:01:19.000><c> so</c><00:01:19.200><c> let's</c><00:01:19.600><c> uh</c>

00:01:19.710 --> 00:01:19.720 align:start position:0%
then see uh how it performs so let's uh
 

00:01:19.720 --> 00:01:21.590 align:start position:0%
then see uh how it performs so let's uh
get<00:01:19.880><c> started</c><00:01:20.200><c> with</c><00:01:20.360><c> it</c><00:01:21.000><c> we'll</c><00:01:21.200><c> start</c><00:01:21.439><c> with</c>

00:01:21.590 --> 00:01:21.600 align:start position:0%
get started with it we'll start with
 

00:01:21.600 --> 00:01:24.190 align:start position:0%
get started with it we'll start with
simple<00:01:21.920><c> calculator</c><00:01:22.360><c> tools</c><00:01:22.759><c> first</c><00:01:23.759><c> uh</c><00:01:23.960><c> we'll</c>

00:01:24.190 --> 00:01:24.200 align:start position:0%
simple calculator tools first uh we'll
 

00:01:24.200 --> 00:01:25.670 align:start position:0%
simple calculator tools first uh we'll
just

00:01:25.670 --> 00:01:25.680 align:start position:0%
just
 

00:01:25.680 --> 00:01:29.789 align:start position:0%
just
import<00:01:26.680><c> uh</c><00:01:26.880><c> the</c><00:01:27.320><c> necessary</c><00:01:28.320><c> modules</c><00:01:29.280><c> and</c><00:01:29.479><c> then</c>

00:01:29.789 --> 00:01:29.799 align:start position:0%
import uh the necessary modules and then
 

00:01:29.799 --> 00:01:30.590 align:start position:0%
import uh the necessary modules and then
uh

00:01:30.590 --> 00:01:30.600 align:start position:0%
uh
 

00:01:30.600 --> 00:01:33.789 align:start position:0%
uh
we'll<00:01:30.840><c> Define</c><00:01:31.159><c> all</c><00:01:31.360><c> these</c><00:01:31.920><c> tools</c><00:01:32.920><c> multiply</c>

00:01:33.789 --> 00:01:33.799 align:start position:0%
we'll Define all these tools multiply
 

00:01:33.799 --> 00:01:35.749 align:start position:0%
we'll Define all these tools multiply
add<00:01:34.240><c> subtract</c><00:01:34.799><c> whatever</c><00:01:35.119><c> we</c><00:01:35.200><c> have</c><00:01:35.399><c> defined</c>

00:01:35.749 --> 00:01:35.759 align:start position:0%
add subtract whatever we have defined
 

00:01:35.759 --> 00:01:41.590 align:start position:0%
add subtract whatever we have defined
earlier<00:01:36.280><c> as</c><00:01:36.520><c> well</c><00:01:37.520><c> and</c><00:01:37.720><c> then</c><00:01:38.680><c> uh</c><00:01:39.680><c> um</c><00:01:40.280><c> Define</c><00:01:41.119><c> uh</c>

00:01:41.590 --> 00:01:41.600 align:start position:0%
earlier as well and then uh um Define uh
 

00:01:41.600 --> 00:01:44.149 align:start position:0%
earlier as well and then uh um Define uh
create<00:01:42.000><c> fun</c><00:01:42.360><c> tools</c><00:01:42.759><c> on</c><00:01:43.119><c> top</c><00:01:43.320><c> of</c><00:01:43.479><c> these</c>

00:01:44.149 --> 00:01:44.159 align:start position:0%
create fun tools on top of these
 

00:01:44.159 --> 00:01:46.830 align:start position:0%
create fun tools on top of these
functions<00:01:45.159><c> multiply</c><00:01:45.759><c> tool</c><00:01:46.200><c> add</c><00:01:46.399><c> tool</c><00:01:46.640><c> and</c>

00:01:46.830 --> 00:01:46.840 align:start position:0%
functions multiply tool add tool and
 

00:01:46.840 --> 00:01:49.429 align:start position:0%
functions multiply tool add tool and
subtract<00:01:47.280><c> tool</c><00:01:48.280><c> we'll</c><00:01:48.560><c> experiment</c><00:01:49.000><c> with</c><00:01:49.240><c> all</c>

00:01:49.429 --> 00:01:49.439 align:start position:0%
subtract tool we'll experiment with all
 

00:01:49.439 --> 00:01:52.590 align:start position:0%
subtract tool we'll experiment with all
the<00:01:49.680><c> different</c><00:01:50.119><c> llms</c><00:01:50.840><c> open</c><00:01:51.079><c> a</c><00:01:51.280><c> gp4</c><00:01:52.119><c> and</c><00:01:52.320><c> then</c>

00:01:52.590 --> 00:01:52.600 align:start position:0%
the different llms open a gp4 and then
 

00:01:52.600 --> 00:01:56.749 align:start position:0%
the different llms open a gp4 and then
mral<00:01:53.040><c> a</c><00:01:53.880><c> Mr</c><00:01:54.200><c> Large</c><00:01:54.799><c> and</c><00:01:54.960><c> then</c><00:01:55.360><c> Sonet</c><00:01:56.000><c> anthropic</c>

00:01:56.749 --> 00:01:56.759 align:start position:0%
mral a Mr Large and then Sonet anthropic
 

00:01:56.759 --> 00:02:01.709 align:start position:0%
mral a Mr Large and then Sonet anthropic
llm<00:01:57.360><c> as</c><00:01:57.479><c> well</c><00:01:58.640><c> so</c><00:01:59.640><c> let's</c><00:02:00.079><c> set</c><00:02:00.280><c> up</c><00:02:00.719><c> the</c><00:02:00.960><c> llm</c><00:02:01.520><c> and</c>

00:02:01.709 --> 00:02:01.719 align:start position:0%
llm as well so let's set up the llm and
 

00:02:01.719 --> 00:02:05.190 align:start position:0%
llm as well so let's set up the llm and
then<00:02:02.240><c> so</c><00:02:03.240><c> to</c><00:02:03.439><c> create</c><00:02:04.079><c> an</c><00:02:04.280><c> agent</c><00:02:04.799><c> here</c><00:02:04.960><c> we'll</c>

00:02:05.190 --> 00:02:05.200 align:start position:0%
then so to create an agent here we'll
 

00:02:05.200 --> 00:02:07.749 align:start position:0%
then so to create an agent here we'll
use<00:02:05.560><c> function</c><00:02:05.960><c> calling</c><00:02:06.399><c> agent</c><00:02:06.719><c> worker</c><00:02:07.200><c> first</c>

00:02:07.749 --> 00:02:07.759 align:start position:0%
use function calling agent worker first
 

00:02:07.759 --> 00:02:10.990 align:start position:0%
use function calling agent worker first
which<00:02:08.039><c> takes</c><00:02:08.280><c> in</c><00:02:08.840><c> the</c><00:02:09.800><c> tools</c><00:02:10.200><c> available</c><00:02:10.800><c> and</c>

00:02:10.990 --> 00:02:11.000 align:start position:0%
which takes in the tools available and
 

00:02:11.000 --> 00:02:13.990 align:start position:0%
which takes in the tools available and
what<00:02:11.120><c> is</c><00:02:11.280><c> llm</c><00:02:11.680><c> to</c><00:02:11.760><c> be</c><00:02:12.040><c> used</c><00:02:13.040><c> and</c><00:02:13.280><c> whether</c>

00:02:13.990 --> 00:02:14.000 align:start position:0%
what is llm to be used and whether
 

00:02:14.000 --> 00:02:17.350 align:start position:0%
what is llm to be used and whether
parallel<00:02:14.760><c> uh</c><00:02:15.239><c> tooling</c><00:02:16.239><c> uh</c><00:02:16.599><c> is</c><00:02:16.840><c> possible</c><00:02:17.200><c> or</c>

00:02:17.350 --> 00:02:17.360 align:start position:0%
parallel uh tooling uh is possible or
 

00:02:17.360 --> 00:02:19.830 align:start position:0%
parallel uh tooling uh is possible or
not<00:02:17.599><c> with</c><00:02:17.800><c> this</c><00:02:18.120><c> agent</c><00:02:19.000><c> and</c><00:02:19.160><c> then</c><00:02:19.360><c> create</c><00:02:19.640><c> an</c>

00:02:19.830 --> 00:02:19.840 align:start position:0%
not with this agent and then create an
 

00:02:19.840 --> 00:02:22.150 align:start position:0%
not with this agent and then create an
agent<00:02:20.200><c> with</c><00:02:20.480><c> uh</c><00:02:20.640><c> agent</c><00:02:20.959><c> runner</c><00:02:21.599><c> on</c><00:02:21.800><c> top</c><00:02:21.959><c> of</c>

00:02:22.150 --> 00:02:22.160 align:start position:0%
agent with uh agent runner on top of
 

00:02:22.160 --> 00:02:25.990 align:start position:0%
agent with uh agent runner on top of
this<00:02:22.480><c> worker</c><00:02:23.480><c> so</c><00:02:24.440><c> let's</c><00:02:24.720><c> create</c>

00:02:25.990 --> 00:02:26.000 align:start position:0%
this worker so let's create
 

00:02:26.000 --> 00:02:29.990 align:start position:0%
this worker so let's create
that<00:02:27.000><c> and</c><00:02:27.200><c> then</c><00:02:28.200><c> let's</c><00:02:28.400><c> see</c><00:02:29.040><c> uh</c><00:02:29.319><c> have</c><00:02:29.599><c> this</c>

00:02:29.990 --> 00:02:30.000 align:start position:0%
that and then let's see uh have this
 

00:02:30.000 --> 00:02:35.869 align:start position:0%
that and then let's see uh have this
what<00:02:30.200><c> is</c><00:02:31.200><c> 20</c><00:02:31.560><c> +</c><00:02:32.280><c> 2</c><00:02:32.800><c> *</c><00:02:33.360><c> by</c><00:02:33.640><c> 4</c><00:02:34.640><c> uh</c><00:02:35.160><c> right</c><00:02:35.400><c> so</c><00:02:35.640><c> if</c><00:02:35.760><c> you</c>

00:02:35.869 --> 00:02:35.879 align:start position:0%
what is 20 + 2 * by 4 uh right so if you
 

00:02:35.879 --> 00:02:38.550 align:start position:0%
what is 20 + 2 * by 4 uh right so if you
see<00:02:36.480><c> yeah</c><00:02:37.239><c> it</c><00:02:37.400><c> is</c><00:02:37.640><c> calling</c><00:02:38.239><c> uh</c><00:02:38.400><c> these</c>

00:02:38.550 --> 00:02:38.560 align:start position:0%
see yeah it is calling uh these
 

00:02:38.560 --> 00:02:40.190 align:start position:0%
see yeah it is calling uh these
individual<00:02:39.040><c> functions</c><00:02:39.519><c> right</c><00:02:39.760><c> with</c><00:02:39.959><c> the</c>

00:02:40.190 --> 00:02:40.200 align:start position:0%
individual functions right with the
 

00:02:40.200 --> 00:02:43.309 align:start position:0%
individual functions right with the
necessary<00:02:41.200><c> parameters</c><00:02:42.120><c> right</c><00:02:42.640><c> so</c><00:02:42.920><c> first</c><00:02:43.120><c> is</c>

00:02:43.309 --> 00:02:43.319 align:start position:0%
necessary parameters right so first is
 

00:02:43.319 --> 00:02:46.270 align:start position:0%
necessary parameters right so first is
multiply<00:02:44.319><c> uh</c><00:02:44.480><c> 2</c><00:02:44.760><c> and</c><00:02:44.959><c> four</c><00:02:45.599><c> and</c><00:02:45.720><c> then</c><00:02:45.959><c> get</c><00:02:46.120><c> a</c>

00:02:46.270 --> 00:02:46.280 align:start position:0%
multiply uh 2 and four and then get a
 

00:02:46.280 --> 00:02:51.910 align:start position:0%
multiply uh 2 and four and then get a
result<00:02:46.599><c> of</c><00:02:46.800><c> 8</c><00:02:47.280><c> and</c><00:02:47.480><c> then</c><00:02:47.840><c> 20</c><00:02:48.599><c> +</c><00:02:49.120><c> 8</c><00:02:50.000><c> is</c><00:02:50.319><c> 8</c><00:02:50.760><c> 28</c><00:02:51.760><c> and</c>

00:02:51.910 --> 00:02:51.920 align:start position:0%
result of 8 and then 20 + 8 is 8 28 and
 

00:02:51.920 --> 00:02:56.030 align:start position:0%
result of 8 and then 20 + 8 is 8 28 and
then<00:02:52.319><c> finally</c><00:02:53.159><c> uh</c><00:02:53.360><c> gives</c><00:02:53.879><c> these</c><00:02:54.560><c> things</c><00:02:54.840><c> to</c><00:02:55.360><c> uh</c>

00:02:56.030 --> 00:02:56.040 align:start position:0%
then finally uh gives these things to uh
 

00:02:56.040 --> 00:02:58.710 align:start position:0%
then finally uh gives these things to uh
response<00:02:56.519><c> and</c><00:02:56.720><c> gives</c><00:02:57.000><c> the</c><00:02:57.159><c> result</c><00:02:57.560><c> as</c><00:02:58.000><c> uh</c><00:02:58.159><c> 28</c>

00:02:58.710 --> 00:02:58.720 align:start position:0%
response and gives the result as uh 28
 

00:02:58.720 --> 00:03:00.350 align:start position:0%
response and gives the result as uh 28
right<00:02:59.599><c> so</c>

00:03:00.350 --> 00:03:00.360 align:start position:0%
right so
 

00:03:00.360 --> 00:03:02.949 align:start position:0%
right so
so<00:03:01.360><c> yeah</c><00:03:01.599><c> that's</c>

00:03:02.949 --> 00:03:02.959 align:start position:0%
so yeah that's
 

00:03:02.959 --> 00:03:05.789 align:start position:0%
so yeah that's
it<00:03:03.959><c> similarly</c><00:03:04.599><c> Let's</c><00:03:04.959><c> uh</c><00:03:05.080><c> look</c><00:03:05.239><c> into</c><00:03:05.560><c> other</c>

00:03:05.789 --> 00:03:05.799 align:start position:0%
it similarly Let's uh look into other
 

00:03:05.799 --> 00:03:11.030 align:start position:0%
it similarly Let's uh look into other
example<00:03:06.280><c> as</c><00:03:06.599><c> well</c><00:03:07.599><c> um</c><00:03:08.480><c> yeah</c><00:03:08.760><c> it</c><00:03:09.360><c> called</c><00:03:10.360><c> and</c><00:03:10.599><c> 30</c>

00:03:11.030 --> 00:03:11.040 align:start position:0%
example as well um yeah it called and 30
 

00:03:11.040 --> 00:03:13.750 align:start position:0%
example as well um yeah it called and 30
with<00:03:11.159><c> the</c><00:03:11.280><c> arguments</c><00:03:11.879><c> and</c><00:03:12.040><c> then</c><00:03:12.519><c> got</c><00:03:12.840><c> 70</c><00:03:13.640><c> and</c>

00:03:13.750 --> 00:03:13.760 align:start position:0%
with the arguments and then got 70 and
 

00:03:13.760 --> 00:03:15.550 align:start position:0%
with the arguments and then got 70 and
then<00:03:13.920><c> 350</c><00:03:14.519><c> and</c><00:03:14.680><c> then</c>

00:03:15.550 --> 00:03:15.560 align:start position:0%
then 350 and then
 

00:03:15.560 --> 00:03:20.910 align:start position:0%
then 350 and then
390<00:03:16.560><c> and</c><00:03:17.239><c> give</c><00:03:17.440><c> the</c><00:03:17.599><c> final</c><00:03:18.480><c> response</c><00:03:19.680><c> so</c><00:03:20.680><c> yeah</c>

00:03:20.910 --> 00:03:20.920 align:start position:0%
390 and give the final response so yeah
 

00:03:20.920 --> 00:03:23.630 align:start position:0%
390 and give the final response so yeah
this<00:03:21.040><c> is</c><00:03:21.720><c> um</c><00:03:22.280><c> this</c><00:03:22.400><c> is</c><00:03:22.560><c> how</c><00:03:22.920><c> like</c><00:03:23.200><c> instead</c><00:03:23.480><c> of</c>

00:03:23.630 --> 00:03:23.640 align:start position:0%
this is um this is how like instead of
 

00:03:23.640 --> 00:03:25.390 align:start position:0%
this is um this is how like instead of
having<00:03:24.000><c> those</c><00:03:24.159><c> three-step</c><00:03:24.640><c> process</c><00:03:24.959><c> for</c>

00:03:25.390 --> 00:03:25.400 align:start position:0%
having those three-step process for
 

00:03:25.400 --> 00:03:27.869 align:start position:0%
having those three-step process for
every<00:03:25.680><c> iteration</c><00:03:26.680><c> it</c><00:03:26.879><c> just</c><00:03:27.200><c> called</c><00:03:27.680><c> these</c>

00:03:27.869 --> 00:03:27.879 align:start position:0%
every iteration it just called these
 

00:03:27.879 --> 00:03:30.309 align:start position:0%
every iteration it just called these
individual<00:03:28.400><c> functions</c><00:03:28.920><c> and</c><00:03:29.400><c> uh</c><00:03:29.840><c> got</c><00:03:30.040><c> things</c>

00:03:30.309 --> 00:03:30.319 align:start position:0%
individual functions and uh got things
 

00:03:30.319 --> 00:03:33.030 align:start position:0%
individual functions and uh got things
done<00:03:30.879><c> right</c><00:03:31.080><c> unlike</c><00:03:31.599><c> react</c><00:03:31.959><c> agent</c><00:03:32.200><c> framework</c>

00:03:33.030 --> 00:03:33.040 align:start position:0%
done right unlike react agent framework
 

00:03:33.040 --> 00:03:35.589 align:start position:0%
done right unlike react agent framework
let's<00:03:33.239><c> run</c><00:03:33.519><c> with</c><00:03:33.840><c> uh</c><00:03:34.000><c> other</c><00:03:34.239><c> llms</c><00:03:34.720><c> as</c><00:03:34.879><c> well</c><00:03:35.439><c> uh</c>

00:03:35.589 --> 00:03:35.599 align:start position:0%
let's run with uh other llms as well uh
 

00:03:35.599 --> 00:03:38.149 align:start position:0%
let's run with uh other llms as well uh
we'll<00:03:35.959><c> go</c><00:03:36.159><c> with</c><00:03:36.280><c> Sonet</c><00:03:36.760><c> anthropic</c><00:03:37.360><c> first</c>

00:03:38.149 --> 00:03:38.159 align:start position:0%
we'll go with Sonet anthropic first
 

00:03:38.159 --> 00:03:42.149 align:start position:0%
we'll go with Sonet anthropic first
create<00:03:38.480><c> an</c><00:03:38.720><c> agent</c><00:03:39.319><c> then</c><00:03:40.319><c> and</c><00:03:40.560><c> then</c>

00:03:42.149 --> 00:03:42.159 align:start position:0%
create an agent then and then
 

00:03:42.159 --> 00:03:44.509 align:start position:0%
create an agent then and then
start<00:03:43.159><c> um</c>

00:03:44.509 --> 00:03:44.519 align:start position:0%
start um
 

00:03:44.519 --> 00:03:46.910 align:start position:0%
start um
quering<00:03:45.519><c> um</c>

00:03:46.910 --> 00:03:46.920 align:start position:0%
quering um
 

00:03:46.920 --> 00:03:50.830 align:start position:0%
quering um
so<00:03:47.920><c> started</c><00:03:48.360><c> with</c><00:03:48.640><c> uh</c><00:03:48.760><c> multiplying</c><00:03:49.640><c> and</c><00:03:49.840><c> then</c>

00:03:50.830 --> 00:03:50.840 align:start position:0%
so started with uh multiplying and then
 

00:03:50.840 --> 00:03:56.069 align:start position:0%
so started with uh multiplying and then
uh<00:03:51.840><c> uh</c><00:03:52.040><c> and</c><00:03:52.239><c> then</c><00:03:52.760><c> uh</c><00:03:52.920><c> got</c><00:03:53.239><c> 28</c><00:03:53.680><c> as</c><00:03:54.120><c> response</c>

00:03:56.069 --> 00:03:56.079 align:start position:0%
uh uh and then uh got 28 as response
 

00:03:56.079 --> 00:04:00.830 align:start position:0%
uh uh and then uh got 28 as response
so<00:03:57.079><c> and</c><00:03:58.000><c> here</c><00:03:58.560><c> it</c><00:03:58.760><c> is</c><00:03:59.360><c> uh</c>

00:04:00.830 --> 00:04:00.840 align:start position:0%
so and here it is uh
 

00:04:00.840 --> 00:04:07.309 align:start position:0%
so and here it is uh
70<00:04:02.040><c> uh</c><00:04:03.040><c> and</c><00:04:03.280><c> then</c><00:04:03.799><c> uh</c><00:04:04.200><c> 350</c><00:04:05.200><c> and</c><00:04:05.439><c> then</c><00:04:06.360><c> 390</c><00:04:07.040><c> so</c>

00:04:07.309 --> 00:04:07.319 align:start position:0%
70 uh and then uh 350 and then 390 so
 

00:04:07.319 --> 00:04:08.869 align:start position:0%
70 uh and then uh 350 and then 390 so
final<00:04:07.640><c> response</c>

00:04:08.869 --> 00:04:08.879 align:start position:0%
final response
 

00:04:08.879 --> 00:04:14.270 align:start position:0%
final response
390<00:04:10.360><c> um</c><00:04:11.360><c> and</c><00:04:11.519><c> then</c><00:04:11.760><c> let's</c><00:04:12.079><c> continue</c><00:04:12.519><c> with</c><00:04:13.280><c> Mr</c>

00:04:14.270 --> 00:04:14.280 align:start position:0%
390 um and then let's continue with Mr
 

00:04:14.280 --> 00:04:18.990 align:start position:0%
390 um and then let's continue with Mr
so<00:04:14.799><c> let's</c><00:04:15.159><c> create</c><00:04:16.040><c> an</c><00:04:16.280><c> agent</c><00:04:17.040><c> first</c><00:04:18.040><c> and</c><00:04:18.320><c> then</c>

00:04:18.990 --> 00:04:19.000 align:start position:0%
so let's create an agent first and then
 

00:04:19.000 --> 00:04:23.150 align:start position:0%
so let's create an agent first and then
uh<00:04:19.919><c> as</c><00:04:20.239><c> the</c><00:04:20.479><c> same</c><00:04:20.799><c> question</c><00:04:21.600><c> uh</c><00:04:21.799><c> what</c><00:04:21.919><c> is</c><00:04:22.160><c> 20</c><00:04:22.720><c> +</c>

00:04:23.150 --> 00:04:23.160 align:start position:0%
uh as the same question uh what is 20 +
 

00:04:23.160 --> 00:04:27.150 align:start position:0%
uh as the same question uh what is 20 +
2<00:04:23.680><c> multiplied</c><00:04:24.199><c> by</c><00:04:24.520><c> four</c><00:04:25.520><c> so</c><00:04:26.360><c> it</c><00:04:26.600><c> first</c><00:04:26.840><c> use</c>

00:04:27.150 --> 00:04:27.160 align:start position:0%
2 multiplied by four so it first use
 

00:04:27.160 --> 00:04:30.510 align:start position:0%
2 multiplied by four so it first use
multiply<00:04:27.639><c> tool</c><00:04:28.080><c> and</c><00:04:28.280><c> then</c><00:04:28.680><c> addition</c><00:04:29.080><c> tool</c>

00:04:30.510 --> 00:04:30.520 align:start position:0%
multiply tool and then addition tool
 

00:04:30.520 --> 00:04:34.469 align:start position:0%
multiply tool and then addition tool
then<00:04:31.160><c> get</c><00:04:31.320><c> a</c><00:04:31.520><c> response</c><00:04:32.440><c> yeah</c><00:04:33.160><c> right</c><00:04:33.800><c> so</c><00:04:34.120><c> we</c><00:04:34.280><c> got</c>

00:04:34.469 --> 00:04:34.479 align:start position:0%
then get a response yeah right so we got
 

00:04:34.479 --> 00:04:35.909 align:start position:0%
then get a response yeah right so we got
the<00:04:34.600><c> result</c><00:04:34.880><c> as</c>

00:04:35.909 --> 00:04:35.919 align:start position:0%
the result as
 

00:04:35.919 --> 00:04:39.629 align:start position:0%
the result as
28<00:04:36.919><c> in</c><00:04:37.039><c> the</c><00:04:37.199><c> same</c><00:04:37.440><c> way</c><00:04:37.919><c> let's</c><00:04:38.320><c> uh</c><00:04:38.479><c> use</c><00:04:39.160><c> uh</c><00:04:39.320><c> rack</c>

00:04:39.629 --> 00:04:39.639 align:start position:0%
28 in the same way let's uh use uh rack
 

00:04:39.639 --> 00:04:43.189 align:start position:0%
28 in the same way let's uh use uh rack
tools<00:04:40.039><c> now</c><00:04:40.800><c> rack</c><00:04:41.120><c> qu</c><00:04:41.360><c> engine</c><00:04:41.639><c> tools</c><00:04:42.440><c> and</c><00:04:42.680><c> then</c>

00:04:43.189 --> 00:04:43.199 align:start position:0%
tools now rack qu engine tools and then
 

00:04:43.199 --> 00:04:45.510 align:start position:0%
tools now rack qu engine tools and then
uh<00:04:43.560><c> build</c><00:04:44.160><c> uh</c><00:04:44.280><c> use</c><00:04:44.520><c> function</c><00:04:44.880><c> calling</c><00:04:45.199><c> agent</c>

00:04:45.510 --> 00:04:45.520 align:start position:0%
uh build uh use function calling agent
 

00:04:45.520 --> 00:04:48.550 align:start position:0%
uh build uh use function calling agent
using<00:04:45.840><c> different</c><00:04:46.199><c> LMS</c><00:04:47.199><c> and</c><00:04:47.440><c> get</c><00:04:47.639><c> the</c>

00:04:48.550 --> 00:04:48.560 align:start position:0%
using different LMS and get the
 

00:04:48.560 --> 00:04:50.270 align:start position:0%
using different LMS and get the
response

00:04:50.270 --> 00:04:50.280 align:start position:0%
response
 

00:04:50.280 --> 00:04:55.469 align:start position:0%
response
so<00:04:51.280><c> here</c><00:04:52.160><c> uh</c><00:04:52.400><c> we</c><00:04:53.400><c> um</c><00:04:54.400><c> download</c><00:04:54.840><c> the</c><00:04:55.000><c> data</c><00:04:55.280><c> we'll</c>

00:04:55.469 --> 00:04:55.479 align:start position:0%
so here uh we um download the data we'll
 

00:04:55.479 --> 00:05:00.150 align:start position:0%
so here uh we um download the data we'll
use<00:04:55.800><c> same</c><00:04:56.160><c> over</c><00:04:56.919><c> 10K</c><00:04:57.520><c> and</c><00:04:57.880><c> lift</c><00:04:58.160><c> 10K</c><00:04:58.880><c> filings</c>

00:05:00.150 --> 00:05:00.160 align:start position:0%
use same over 10K and lift 10K filings
 

00:05:00.160 --> 00:05:02.270 align:start position:0%
use same over 10K and lift 10K filings
then<00:05:00.320><c> load</c><00:05:00.639><c> the</c><00:05:00.840><c> data</c><00:05:01.320><c> we'll</c><00:05:01.600><c> create</c><00:05:01.919><c> the</c>

00:05:02.270 --> 00:05:02.280 align:start position:0%
then load the data we'll create the
 

00:05:02.280 --> 00:05:07.629 align:start position:0%
then load the data we'll create the
index<00:05:03.280><c> and</c><00:05:03.840><c> uh</c><00:05:04.520><c> we'll</c><00:05:04.840><c> create</c><00:05:05.479><c> query</c>

00:05:07.629 --> 00:05:07.639 align:start position:0%
index and uh we'll create query
 

00:05:07.639 --> 00:05:10.150 align:start position:0%
index and uh we'll create query
engines<00:05:08.639><c> and</c><00:05:09.120><c> we'll</c><00:05:09.400><c> Define</c><00:05:09.759><c> both</c><00:05:10.000><c> these</c>

00:05:10.150 --> 00:05:10.160 align:start position:0%
engines and we'll Define both these
 

00:05:10.160 --> 00:05:12.870 align:start position:0%
engines and we'll Define both these
tools<00:05:11.000><c> Uber</c><00:05:11.280><c> 10K</c><00:05:11.639><c> filings</c><00:05:12.080><c> and</c><00:05:12.240><c> lift</c><00:05:12.520><c> 10K</c>

00:05:12.870 --> 00:05:12.880 align:start position:0%
tools Uber 10K filings and lift 10K
 

00:05:12.880 --> 00:05:15.510 align:start position:0%
tools Uber 10K filings and lift 10K
filings<00:05:13.720><c> provide</c><00:05:14.160><c> the</c><00:05:14.320><c> name</c><00:05:14.720><c> of</c><00:05:14.919><c> these</c><00:05:15.160><c> tools</c>

00:05:15.510 --> 00:05:15.520 align:start position:0%
filings provide the name of these tools
 

00:05:15.520 --> 00:05:18.469 align:start position:0%
filings provide the name of these tools
and<00:05:16.080><c> uh</c><00:05:16.240><c> proper</c><00:05:16.960><c> description</c><00:05:17.960><c> uh</c><00:05:18.120><c> which</c><00:05:18.240><c> will</c>

00:05:18.469 --> 00:05:18.479 align:start position:0%
and uh proper description uh which will
 

00:05:18.479 --> 00:05:21.469 align:start position:0%
and uh proper description uh which will
help<00:05:19.199><c> uh</c><00:05:19.759><c> the</c><00:05:20.759><c> function</c><00:05:21.120><c> calling</c>

00:05:21.469 --> 00:05:21.479 align:start position:0%
help uh the function calling
 

00:05:21.479 --> 00:05:23.990 align:start position:0%
help uh the function calling
capabilities<00:05:21.960><c> of</c><00:05:22.080><c> llm</c><00:05:22.440><c> to</c><00:05:22.600><c> use</c><00:05:23.240><c> the</c><00:05:23.400><c> right</c>

00:05:23.990 --> 00:05:24.000 align:start position:0%
capabilities of llm to use the right
 

00:05:24.000 --> 00:05:26.710 align:start position:0%
capabilities of llm to use the right
quir<00:05:24.319><c> engine</c><00:05:24.639><c> tool</c>

00:05:26.710 --> 00:05:26.720 align:start position:0%
quir engine tool
 

00:05:26.720 --> 00:05:32.469 align:start position:0%
quir engine tool
so<00:05:27.720><c> so</c><00:05:28.000><c> let's</c><00:05:28.680><c> uh</c><00:05:29.479><c> uh</c><00:05:29.880><c> uh</c><00:05:30.080><c> run</c><00:05:30.319><c> it</c><00:05:31.120><c> and</c><00:05:31.319><c> then</c>

00:05:32.469 --> 00:05:32.479 align:start position:0%
so so let's uh uh uh run it and then
 

00:05:32.479 --> 00:05:34.790 align:start position:0%
so so let's uh uh uh run it and then
we'll<00:05:33.479><c> have</c><00:05:33.680><c> the</c><00:05:33.800><c> same</c><00:05:34.039><c> question</c><00:05:34.360><c> compare</c><00:05:34.680><c> the</c>

00:05:34.790 --> 00:05:34.800 align:start position:0%
we'll have the same question compare the
 

00:05:34.800 --> 00:05:38.510 align:start position:0%
we'll have the same question compare the
revenue<00:05:35.160><c> growth</c><00:05:35.440><c> of</c><00:05:35.639><c> uber</c><00:05:35.919><c> and</c><00:05:36.120><c> lift</c><00:05:36.440><c> in</c>

00:05:38.510 --> 00:05:38.520 align:start position:0%
revenue growth of uber and lift in
 

00:05:38.520 --> 00:05:42.189 align:start position:0%
revenue growth of uber and lift in
2021<00:05:39.520><c> so</c><00:05:39.800><c> this</c><00:05:39.960><c> might</c><00:05:40.199><c> take</c><00:05:40.520><c> a</c><00:05:40.800><c> while</c><00:05:41.639><c> uh</c><00:05:41.800><c> it</c><00:05:42.000><c> by</c>

00:05:42.189 --> 00:05:42.199 align:start position:0%
2021 so this might take a while uh it by
 

00:05:42.199 --> 00:05:44.670 align:start position:0%
2021 so this might take a while uh it by
default<00:05:42.560><c> us</c><00:05:42.800><c> just</c><00:05:43.039><c> open</c><00:05:43.280><c> a</c>

00:05:44.670 --> 00:05:44.680 align:start position:0%
default us just open a
 

00:05:44.680 --> 00:05:49.909 align:start position:0%
default us just open a
embeddings<00:05:45.800><c> and</c><00:05:46.800><c> since</c><00:05:47.479><c> it</c><00:05:47.680><c> is</c><00:05:48.319><c> like</c><00:05:49.319><c> uh</c><00:05:49.479><c> some</c>

00:05:49.909 --> 00:05:49.919 align:start position:0%
embeddings and since it is like uh some
 

00:05:49.919 --> 00:05:52.950 align:start position:0%
embeddings and since it is like uh some
300<00:05:50.600><c> plus</c><00:05:50.880><c> pages</c><00:05:51.479><c> so</c><00:05:51.759><c> this</c><00:05:51.880><c> will</c><00:05:52.120><c> take</c><00:05:52.800><c> a</c>

00:05:52.950 --> 00:05:52.960 align:start position:0%
300 plus pages so this will take a
 

00:05:52.960 --> 00:05:57.629 align:start position:0%
300 plus pages so this will take a
minute<00:05:53.240><c> or</c>

00:05:57.629 --> 00:05:57.639 align:start position:0%
 
 

00:05:57.639 --> 00:06:00.469 align:start position:0%
 
so<00:05:58.639><c> yeah</c><00:05:58.800><c> I</c><00:05:58.919><c> think</c><00:05:59.160><c> it</c><00:05:59.280><c> is</c><00:05:59.400><c> done</c>

00:06:00.469 --> 00:06:00.479 align:start position:0%
so yeah I think it is done
 

00:06:00.479 --> 00:06:04.390 align:start position:0%
so yeah I think it is done
so<00:06:01.280><c> yes</c><00:06:01.840><c> and</c><00:06:02.039><c> then</c><00:06:02.319><c> we'll</c><00:06:02.840><c> create</c><00:06:03.840><c> with</c><00:06:04.120><c> open</c>

00:06:04.390 --> 00:06:04.400 align:start position:0%
so yes and then we'll create with open
 

00:06:04.400 --> 00:06:07.990 align:start position:0%
so yes and then we'll create with open
Ag<00:06:04.720><c> gbd4</c><00:06:05.720><c> and</c><00:06:05.960><c> then</c><00:06:06.840><c> then</c>

00:06:07.990 --> 00:06:08.000 align:start position:0%
Ag gbd4 and then then
 

00:06:08.000 --> 00:06:12.430 align:start position:0%
Ag gbd4 and then then
C<00:06:09.000><c> so</c><00:06:09.639><c> first</c><00:06:09.919><c> it</c><00:06:10.680><c> yeah</c><00:06:11.199><c> choose</c><00:06:11.759><c> Uber</c><00:06:12.120><c> 10K</c>

00:06:12.430 --> 00:06:12.440 align:start position:0%
C so first it yeah choose Uber 10K
 

00:06:12.440 --> 00:06:14.790 align:start position:0%
C so first it yeah choose Uber 10K
filings<00:06:12.880><c> with</c><00:06:13.240><c> Uber</c><00:06:13.720><c> what</c><00:06:13.840><c> is</c><00:06:14.039><c> Uber's</c><00:06:14.440><c> Revenue</c>

00:06:14.790 --> 00:06:14.800 align:start position:0%
filings with Uber what is Uber's Revenue
 

00:06:14.800 --> 00:06:17.510 align:start position:0%
filings with Uber what is Uber's Revenue
growth<00:06:15.120><c> and</c><00:06:15.240><c> then</c><00:06:15.400><c> lifts</c><00:06:15.759><c> Revenue</c><00:06:16.240><c> growth</c><00:06:17.240><c> got</c>

00:06:17.510 --> 00:06:17.520 align:start position:0%
growth and then lifts Revenue growth got
 

00:06:17.520 --> 00:06:19.189 align:start position:0%
growth and then lifts Revenue growth got
these<00:06:17.720><c> both</c><00:06:18.000><c> information</c><00:06:18.800><c> and</c><00:06:18.960><c> then</c>

00:06:19.189 --> 00:06:19.199 align:start position:0%
these both information and then
 

00:06:19.199 --> 00:06:22.189 align:start position:0%
these both information and then
generated<00:06:19.639><c> a</c><00:06:19.840><c> response</c>

00:06:22.189 --> 00:06:22.199 align:start position:0%
generated a response
 

00:06:22.199 --> 00:06:24.909 align:start position:0%
generated a response
accordingly<00:06:23.199><c> uh</c><00:06:23.400><c> so</c><00:06:23.680><c> similarly</c><00:06:24.199><c> let's</c><00:06:24.400><c> use</c>

00:06:24.909 --> 00:06:24.919 align:start position:0%
accordingly uh so similarly let's use
 

00:06:24.919 --> 00:06:26.670 align:start position:0%
accordingly uh so similarly let's use
anthropic

00:06:26.670 --> 00:06:26.680 align:start position:0%
anthropic
 

00:06:26.680 --> 00:06:29.950 align:start position:0%
anthropic
llm<00:06:27.680><c> for</c><00:06:27.919><c> the</c><00:06:28.080><c> same</c><00:06:28.400><c> query</c>

00:06:29.950 --> 00:06:29.960 align:start position:0%
llm for the same query
 

00:06:29.960 --> 00:06:34.029 align:start position:0%
llm for the same query
and<00:06:30.240><c> then</c><00:06:30.840><c> see</c><00:06:31.520><c> what</c><00:06:31.639><c> is</c><00:06:31.759><c> the</c><00:06:32.000><c> response</c>

00:06:34.029 --> 00:06:34.039 align:start position:0%
and then see what is the response
 

00:06:34.039 --> 00:06:35.990 align:start position:0%
and then see what is the response
here

00:06:35.990 --> 00:06:36.000 align:start position:0%
here
 

00:06:36.000 --> 00:06:40.230 align:start position:0%
here
so<00:06:37.000><c> it</c><00:06:37.240><c> used</c><00:06:37.880><c> Uber</c><00:06:38.240><c> 10K</c><00:06:38.800><c> filings</c><00:06:39.800><c> and</c><00:06:40.000><c> then</c>

00:06:40.230 --> 00:06:40.240 align:start position:0%
so it used Uber 10K filings and then
 

00:06:40.240 --> 00:06:43.909 align:start position:0%
so it used Uber 10K filings and then
lift<00:06:40.560><c> 10K</c><00:06:40.919><c> filings</c><00:06:41.680><c> and</c><00:06:42.680><c> got</c><00:06:42.960><c> a</c><00:06:43.199><c> final</c>

00:06:43.909 --> 00:06:43.919 align:start position:0%
lift 10K filings and got a final
 

00:06:43.919 --> 00:06:46.430 align:start position:0%
lift 10K filings and got a final
response<00:06:44.919><c> right</c><00:06:45.160><c> Uber</c><00:06:45.440><c> has</c><00:06:45.800><c> strong</c><00:06:46.039><c> Revenue</c>

00:06:46.430 --> 00:06:46.440 align:start position:0%
response right Uber has strong Revenue
 

00:06:46.440 --> 00:06:49.830 align:start position:0%
response right Uber has strong Revenue
growth<00:06:46.800><c> and</c><00:06:46.960><c> lift</c><00:06:47.919><c> and</c><00:06:48.160><c> then</c><00:06:48.440><c> sober</c><00:06:49.080><c> outpaced</c>

00:06:49.830 --> 00:06:49.840 align:start position:0%
growth and lift and then sober outpaced
 

00:06:49.840 --> 00:06:54.350 align:start position:0%
growth and lift and then sober outpaced
LIF<00:06:50.080><c> in</c><00:06:50.680><c> in</c><00:06:50.880><c> terms</c><00:06:51.199><c> of</c><00:06:51.840><c> year</c><00:06:52.759><c> or</c><00:06:53.080><c> year</c><00:06:53.319><c> Revenue</c>

00:06:54.350 --> 00:06:54.360 align:start position:0%
LIF in in terms of year or year Revenue
 

00:06:54.360 --> 00:06:58.270 align:start position:0%
LIF in in terms of year or year Revenue
growth<00:06:55.360><c> then</c><00:06:55.520><c> let's</c><00:06:55.720><c> use</c><00:06:55.960><c> Mr</c><00:06:56.319><c> a</c><00:06:57.080><c> for</c><00:06:57.280><c> the</c>

00:06:58.270 --> 00:06:58.280 align:start position:0%
growth then let's use Mr a for the
 

00:06:58.280 --> 00:07:01.749 align:start position:0%
growth then let's use Mr a for the
same<00:06:59.280><c> and</c><00:06:59.720><c> then</c><00:07:00.160><c> see</c><00:07:00.599><c> the</c>

00:07:01.749 --> 00:07:01.759 align:start position:0%
same and then see the
 

00:07:01.759 --> 00:07:05.629 align:start position:0%
same and then see the
response<00:07:02.840><c> so</c><00:07:03.840><c> it</c><00:07:04.039><c> got</c><00:07:04.360><c> output</c><00:07:04.759><c> of</c><00:07:05.000><c> lift</c><00:07:05.479><c> and</c>

00:07:05.629 --> 00:07:05.639 align:start position:0%
response so it got output of lift and
 

00:07:05.639 --> 00:07:10.029 align:start position:0%
response so it got output of lift and
then<00:07:06.240><c> Uber</c><00:07:07.240><c> and</c><00:07:07.400><c> then</c><00:07:08.319><c> final</c>

00:07:10.029 --> 00:07:10.039 align:start position:0%
then Uber and then final
 

00:07:10.039 --> 00:07:13.270 align:start position:0%
then Uber and then final
response<00:07:11.039><c> so</c><00:07:11.840><c> that's</c><00:07:12.000><c> how</c><00:07:12.280><c> you</c><00:07:12.400><c> can</c><00:07:12.599><c> use</c><00:07:13.080><c> uh</c>

00:07:13.270 --> 00:07:13.280 align:start position:0%
response so that's how you can use uh
 

00:07:13.280 --> 00:07:15.350 align:start position:0%
response so that's how you can use uh
function<00:07:13.639><c> calling</c><00:07:14.000><c> agent</c><00:07:14.360><c> instead</c><00:07:14.680><c> of</c><00:07:15.000><c> react</c>

00:07:15.350 --> 00:07:15.360 align:start position:0%
function calling agent instead of react
 

00:07:15.360 --> 00:07:19.230 align:start position:0%
function calling agent instead of react
agent<00:07:16.199><c> and</c><00:07:16.440><c> do</c><00:07:16.919><c> same</c><00:07:17.240><c> kind</c><00:07:17.520><c> of</c><00:07:18.000><c> uh</c><00:07:18.599><c> agentic</c>

00:07:19.230 --> 00:07:19.240 align:start position:0%
agent and do same kind of uh agentic
 

00:07:19.240 --> 00:07:22.029 align:start position:0%
agent and do same kind of uh agentic
task<00:07:20.000><c> right</c><00:07:20.960><c> um</c><00:07:21.199><c> do</c><00:07:21.479><c> explore</c><00:07:21.840><c> with</c><00:07:21.919><c> the</c>

00:07:22.029 --> 00:07:22.039 align:start position:0%
task right um do explore with the
 

00:07:22.039 --> 00:07:25.070 align:start position:0%
task right um do explore with the
notebook<00:07:22.960><c> and</c><00:07:23.639><c> see</c><00:07:23.840><c> you</c><00:07:23.919><c> in</c><00:07:24.039><c> the</c><00:07:24.160><c> next</c><00:07:24.400><c> video</c>

00:07:25.070 --> 00:07:25.080 align:start position:0%
notebook and see you in the next video
 

00:07:25.080 --> 00:07:27.960 align:start position:0%
notebook and see you in the next video
thank<00:07:25.319><c> you</c>

