WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.969 align:start position:0%
 
hey<00:00:00.210><c> and</c><00:00:00.630><c> welcome</c><00:00:00.719><c> to</c><00:00:00.870><c> another</c><00:00:00.989><c> HTML</c><00:00:01.500><c> video</c><00:00:01.560><c> in</c>

00:00:01.969 --> 00:00:01.979 align:start position:0%
hey and welcome to another HTML video in
 

00:00:01.979 --> 00:00:03.409 align:start position:0%
hey and welcome to another HTML video in
this<00:00:02.340><c> video</c><00:00:02.490><c> we're</c><00:00:02.970><c> going</c><00:00:03.060><c> to</c><00:00:03.149><c> be</c><00:00:03.210><c> talking</c>

00:00:03.409 --> 00:00:03.419 align:start position:0%
this video we're going to be talking
 

00:00:03.419 --> 00:00:06.200 align:start position:0%
this video we're going to be talking
about<00:00:03.510><c> HTML</c><00:00:04.110><c> lists</c><00:00:04.380><c> now</c><00:00:05.160><c> there</c><00:00:05.910><c> are</c><00:00:05.970><c> lots</c><00:00:06.060><c> of</c>

00:00:06.200 --> 00:00:06.210 align:start position:0%
about HTML lists now there are lots of
 

00:00:06.210 --> 00:00:08.210 align:start position:0%
about HTML lists now there are lots of
places<00:00:06.420><c> in</c><00:00:06.690><c> a</c><00:00:06.810><c> webpage</c><00:00:06.990><c> word</c><00:00:07.470><c> lists</c><00:00:07.770><c> are</c><00:00:07.859><c> used</c>

00:00:08.210 --> 00:00:08.220 align:start position:0%
places in a webpage word lists are used
 

00:00:08.220 --> 00:00:10.910 align:start position:0%
places in a webpage word lists are used
and<00:00:08.400><c> we're</c><00:00:09.120><c> going</c><00:00:09.269><c> to</c><00:00:09.389><c> go</c><00:00:09.510><c> over</c><00:00:09.540><c> a</c><00:00:10.170><c> family</c><00:00:10.679><c> the</c>

00:00:10.910 --> 00:00:10.920 align:start position:0%
and we're going to go over a family the
 

00:00:10.920 --> 00:00:14.120 align:start position:0%
and we're going to go over a family the
two<00:00:11.099><c> most</c><00:00:11.340><c> used</c><00:00:11.490><c> lists</c><00:00:12.330><c> and</c><00:00:12.630><c> in</c><00:00:12.990><c> an</c><00:00:13.410><c> HTML</c><00:00:13.889><c> web</c>

00:00:14.120 --> 00:00:14.130 align:start position:0%
two most used lists and in an HTML web
 

00:00:14.130 --> 00:00:17.000 align:start position:0%
two most used lists and in an HTML web
page<00:00:14.549><c> and</c><00:00:15.030><c> so</c><00:00:15.960><c> there's</c><00:00:16.199><c> two</c><00:00:16.230><c> are</c><00:00:16.590><c> ordered</c>

00:00:17.000 --> 00:00:17.010 align:start position:0%
page and so there's two are ordered
 

00:00:17.010 --> 00:00:20.179 align:start position:0%
page and so there's two are ordered
lists<00:00:17.310><c> which</c><00:00:18.439><c> are</c><00:00:19.439><c> generally</c><00:00:19.800><c> where</c><00:00:19.949><c> each</c>

00:00:20.179 --> 00:00:20.189 align:start position:0%
lists which are generally where each
 

00:00:20.189 --> 00:00:22.609 align:start position:0%
lists which are generally where each
number<00:00:20.460><c> or</c><00:00:20.970><c> each</c><00:00:21.150><c> item</c><00:00:21.539><c> is</c><00:00:21.960><c> numbered</c><00:00:22.410><c> in</c><00:00:22.590><c> the</c>

00:00:22.609 --> 00:00:22.619 align:start position:0%
number or each item is numbered in the
 

00:00:22.619 --> 00:00:24.259 align:start position:0%
number or each item is numbered in the
list<00:00:22.859><c> and</c><00:00:23.250><c> then</c><00:00:23.369><c> there's</c><00:00:23.550><c> unordered</c><00:00:24.119><c> lists</c>

00:00:24.259 --> 00:00:24.269 align:start position:0%
list and then there's unordered lists
 

00:00:24.269 --> 00:00:26.650 align:start position:0%
list and then there's unordered lists
and<00:00:24.570><c> that's</c><00:00:24.750><c> where</c><00:00:25.080><c> you</c><00:00:25.680><c> see</c><00:00:25.859><c> bullet</c><00:00:26.160><c> points</c>

00:00:26.650 --> 00:00:26.660 align:start position:0%
and that's where you see bullet points
 

00:00:26.660 --> 00:00:30.859 align:start position:0%
and that's where you see bullet points
for<00:00:27.660><c> the</c><00:00:27.750><c> list</c><00:00:27.960><c> okay</c><00:00:28.580><c> okay</c><00:00:29.580><c> so</c><00:00:29.750><c> we're</c><00:00:30.750><c> gonna</c>

00:00:30.859 --> 00:00:30.869 align:start position:0%
for the list okay okay so we're gonna
 

00:00:30.869 --> 00:00:32.450 align:start position:0%
for the list okay okay so we're gonna
get<00:00:30.960><c> right</c><00:00:31.140><c> into</c><00:00:31.289><c> it</c><00:00:31.410><c> and</c><00:00:31.710><c> here</c><00:00:32.009><c> is</c><00:00:32.130><c> an</c><00:00:32.189><c> example</c>

00:00:32.450 --> 00:00:32.460 align:start position:0%
get right into it and here is an example
 

00:00:32.460 --> 00:00:36.440 align:start position:0%
get right into it and here is an example
of<00:00:32.910><c> an</c><00:00:33.210><c> ordered</c><00:00:33.690><c> list</c><00:00:34.040><c> segment</c><00:00:35.040><c> of</c><00:00:35.100><c> code</c><00:00:35.309><c> so</c><00:00:36.210><c> as</c>

00:00:36.440 --> 00:00:36.450 align:start position:0%
of an ordered list segment of code so as
 

00:00:36.450 --> 00:00:38.660 align:start position:0%
of an ordered list segment of code so as
you<00:00:36.570><c> can</c><00:00:36.660><c> see</c><00:00:36.719><c> here</c><00:00:37.140><c> we</c><00:00:37.620><c> have</c><00:00:37.649><c> an</c><00:00:37.829><c> Al</c><00:00:38.129><c> tag</c>

00:00:38.660 --> 00:00:38.670 align:start position:0%
you can see here we have an Al tag
 

00:00:38.670 --> 00:00:40.729 align:start position:0%
you can see here we have an Al tag
stands<00:00:39.450><c> for</c><00:00:39.629><c> order</c><00:00:39.840><c> list</c><00:00:40.020><c> we</c><00:00:40.350><c> have</c><00:00:40.469><c> a</c><00:00:40.500><c> closing</c>

00:00:40.729 --> 00:00:40.739 align:start position:0%
stands for order list we have a closing
 

00:00:40.739 --> 00:00:43.250 align:start position:0%
stands for order list we have a closing
tag<00:00:40.860><c> for</c><00:00:41.010><c> it</c><00:00:41.340><c> and</c><00:00:41.489><c> then</c><00:00:41.969><c> there's</c><00:00:42.120><c> five</c><00:00:42.390><c> Li</c><00:00:42.930><c> tags</c>

00:00:43.250 --> 00:00:43.260 align:start position:0%
tag for it and then there's five Li tags
 

00:00:43.260 --> 00:00:45.110 align:start position:0%
tag for it and then there's five Li tags
with<00:00:43.739><c> their</c><00:00:43.950><c> respective</c><00:00:44.370><c> closing</c><00:00:44.670><c> tags</c><00:00:44.850><c> and</c>

00:00:45.110 --> 00:00:45.120 align:start position:0%
with their respective closing tags and
 

00:00:45.120 --> 00:00:47.959 align:start position:0%
with their respective closing tags and
Li<00:00:46.110><c> just</c><00:00:46.320><c> hands</c><00:00:46.469><c> for</c><00:00:46.620><c> list</c><00:00:46.800><c> item</c><00:00:47.160><c> so</c><00:00:47.760><c> you</c><00:00:47.820><c> have</c>

00:00:47.959 --> 00:00:47.969 align:start position:0%
Li just hands for list item so you have
 

00:00:47.969 --> 00:00:50.119 align:start position:0%
Li just hands for list item so you have
to<00:00:48.090><c> denote</c><00:00:48.510><c> what</c><00:00:48.930><c> kind</c><00:00:49.440><c> of</c><00:00:49.500><c> list</c><00:00:49.649><c> it</c><00:00:49.770><c> is</c><00:00:49.890><c> and</c>

00:00:50.119 --> 00:00:50.129 align:start position:0%
to denote what kind of list it is and
 

00:00:50.129 --> 00:00:56.060 align:start position:0%
to denote what kind of list it is and
then<00:00:50.280><c> all</c><00:00:50.879><c> of</c><00:00:51.300><c> the</c><00:00:51.949><c> items</c><00:00:52.949><c> or</c><00:00:54.949><c> whatever</c><00:00:55.949><c> you</c>

00:00:56.060 --> 00:00:56.070 align:start position:0%
then all of the items or whatever you
 

00:00:56.070 --> 00:00:58.099 align:start position:0%
then all of the items or whatever you
want<00:00:56.250><c> in</c><00:00:56.399><c> your</c><00:00:56.430><c> list</c><00:00:56.760><c> have</c><00:00:57.570><c> to</c><00:00:57.660><c> be</c><00:00:57.750><c> denoted</c><00:00:57.930><c> by</c>

00:00:58.099 --> 00:00:58.109 align:start position:0%
want in your list have to be denoted by
 

00:00:58.109 --> 00:01:03.950 align:start position:0%
want in your list have to be denoted by
Li<00:00:58.559><c> okay</c><00:00:59.390><c> so</c><00:01:01.280><c> we</c><00:01:02.280><c> have</c><00:01:02.460><c> our</c><00:01:02.489><c> again</c><00:01:03.359><c> this</c><00:01:03.539><c> is</c><00:01:03.719><c> I'm</c>

00:01:03.950 --> 00:01:03.960 align:start position:0%
Li okay so we have our again this is I'm
 

00:01:03.960 --> 00:01:06.380 align:start position:0%
Li okay so we have our again this is I'm
just<00:01:04.140><c> helping</c><00:01:04.409><c> you</c><00:01:04.760><c> with</c><00:01:05.760><c> the</c><00:01:05.850><c> terminology</c><00:01:06.360><c> a</c>

00:01:06.380 --> 00:01:06.390 align:start position:0%
just helping you with the terminology a
 

00:01:06.390 --> 00:01:08.060 align:start position:0%
just helping you with the terminology a
case<00:01:06.630><c> way</c><00:01:06.810><c> of</c><00:01:06.900><c> the</c><00:01:06.990><c> opening</c><00:01:07.409><c> tags</c><00:01:07.650><c> and</c><00:01:07.950><c> the</c>

00:01:08.060 --> 00:01:08.070 align:start position:0%
case way of the opening tags and the
 

00:01:08.070 --> 00:01:10.640 align:start position:0%
case way of the opening tags and the
closing<00:01:08.310><c> tags</c><00:01:08.640><c> and</c><00:01:08.939><c> if</c><00:01:09.780><c> you</c><00:01:09.960><c> were</c><00:01:10.080><c> to</c><00:01:10.320><c> code</c>

00:01:10.640 --> 00:01:10.650 align:start position:0%
closing tags and if you were to code
 

00:01:10.650 --> 00:01:12.830 align:start position:0%
closing tags and if you were to code
this<00:01:10.830><c> which</c><00:01:11.100><c> we</c><00:01:11.310><c> are</c><00:01:11.580><c> going</c><00:01:11.880><c> to</c><00:01:12.000><c> in</c><00:01:12.360><c> just</c><00:01:12.720><c> a</c>

00:01:12.830 --> 00:01:12.840 align:start position:0%
this which we are going to in just a
 

00:01:12.840 --> 00:01:15.170 align:start position:0%
this which we are going to in just a
minute<00:01:13.189><c> this</c><00:01:14.189><c> is</c><00:01:14.340><c> going</c><00:01:14.490><c> to</c><00:01:14.549><c> look</c><00:01:14.670><c> like</c><00:01:14.880><c> this</c>

00:01:15.170 --> 00:01:15.180 align:start position:0%
minute this is going to look like this
 

00:01:15.180 --> 00:01:17.810 align:start position:0%
minute this is going to look like this
in<00:01:15.479><c> the</c><00:01:15.810><c> web</c><00:01:16.080><c> browser</c><00:01:16.460><c> so</c><00:01:17.460><c> as</c><00:01:17.580><c> you</c><00:01:17.670><c> can</c><00:01:17.759><c> see</c>

00:01:17.810 --> 00:01:17.820 align:start position:0%
in the web browser so as you can see
 

00:01:17.820 --> 00:01:20.780 align:start position:0%
in the web browser so as you can see
because<00:01:18.299><c> this</c><00:01:18.390><c> is</c><00:01:18.570><c> an</c><00:01:18.720><c> ordered</c><00:01:19.140><c> list</c><00:01:19.580><c> is</c><00:01:20.580><c> this</c>

00:01:20.780 --> 00:01:20.790 align:start position:0%
because this is an ordered list is this
 

00:01:20.790 --> 00:01:23.200 align:start position:0%
because this is an ordered list is this
numbered<00:01:21.150><c> automatically</c><00:01:21.840><c> one</c><00:01:22.170><c> through</c><00:01:22.350><c> five</c>

00:01:23.200 --> 00:01:23.210 align:start position:0%
numbered automatically one through five
 

00:01:23.210 --> 00:01:27.499 align:start position:0%
numbered automatically one through five
all<00:01:24.210><c> right</c><00:01:24.390><c> so</c><00:01:24.630><c> let's</c><00:01:25.320><c> begin</c><00:01:25.439><c> coding</c><00:01:26.509><c> okay</c>

00:01:27.499 --> 00:01:27.509 align:start position:0%
all right so let's begin coding okay
 

00:01:27.509 --> 00:01:28.819 align:start position:0%
all right so let's begin coding okay
like<00:01:27.900><c> I</c><00:01:27.960><c> said</c><00:01:27.990><c> we're</c><00:01:28.259><c> gonna</c><00:01:28.320><c> get</c><00:01:28.530><c> right</c><00:01:28.650><c> into</c>

00:01:28.819 --> 00:01:28.829 align:start position:0%
like I said we're gonna get right into
 

00:01:28.829 --> 00:01:31.460 align:start position:0%
like I said we're gonna get right into
it<00:01:29.009><c> and</c><00:01:29.280><c> in</c><00:01:29.880><c> this</c><00:01:30.329><c> when</c><00:01:30.840><c> we</c><00:01:30.960><c> code</c><00:01:31.110><c> this</c><00:01:31.259><c> we're</c>

00:01:31.460 --> 00:01:31.470 align:start position:0%
it and in this when we code this we're
 

00:01:31.470 --> 00:01:33.980 align:start position:0%
it and in this when we code this we're
gonna<00:01:31.530><c> do</c><00:01:32.150><c> we're</c><00:01:33.150><c> gonna</c><00:01:33.210><c> do</c><00:01:33.420><c> an</c><00:01:33.509><c> or</c><00:01:33.659><c> unordered</c>

00:01:33.980 --> 00:01:33.990 align:start position:0%
gonna do we're gonna do an or unordered
 

00:01:33.990 --> 00:01:38.330 align:start position:0%
gonna do we're gonna do an or unordered
list<00:01:34.290><c> and</c><00:01:34.530><c> an</c><00:01:34.770><c> ordered</c><00:01:35.009><c> list</c><00:01:35.040><c> okay</c><00:01:35.700><c> so</c><00:01:37.340><c> again</c>

00:01:38.330 --> 00:01:38.340 align:start position:0%
list and an ordered list okay so again
 

00:01:38.340 --> 00:01:40.700 align:start position:0%
list and an ordered list okay so again
any<00:01:38.880><c> text</c><00:01:39.150><c> editor</c><00:01:39.360><c> will</c><00:01:39.479><c> work</c><00:01:39.720><c> notepad</c><00:01:40.560><c> or</c>

00:01:40.700 --> 00:01:40.710 align:start position:0%
any text editor will work notepad or
 

00:01:40.710 --> 00:01:41.899 align:start position:0%
any text editor will work notepad or
notepad<00:01:40.890><c> plus</c><00:01:41.130><c> plus</c><00:01:41.159><c> which</c><00:01:41.640><c> is</c><00:01:41.759><c> what</c><00:01:41.850><c> I'm</c>

00:01:41.899 --> 00:01:41.909 align:start position:0%
notepad plus plus which is what I'm
 

00:01:41.909 --> 00:01:44.149 align:start position:0%
notepad plus plus which is what I'm
gonna<00:01:42.000><c> be</c><00:01:42.060><c> using</c><00:01:42.350><c> okay</c><00:01:43.350><c> so</c><00:01:43.649><c> let's</c><00:01:43.829><c> start</c><00:01:44.009><c> out</c>

00:01:44.149 --> 00:01:44.159 align:start position:0%
gonna be using okay so let's start out
 

00:01:44.159 --> 00:01:45.950 align:start position:0%
gonna be using okay so let's start out
with<00:01:44.430><c> this</c><00:01:44.579><c> your</c><00:01:45.360><c> dock</c><00:01:45.540><c> site</c>

00:01:45.950 --> 00:01:45.960 align:start position:0%
with this your dock site
 

00:01:45.960 --> 00:01:50.690 align:start position:0%
with this your dock site
we<00:01:46.950><c> need</c><00:01:47.220><c> our</c><00:01:48.000><c> opening</c><00:01:48.630><c> HTML</c><00:01:48.780><c> tag</c><00:01:49.610><c> we're</c><00:01:50.610><c> gonna</c>

00:01:50.690 --> 00:01:50.700 align:start position:0%
we need our opening HTML tag we're gonna
 

00:01:50.700 --> 00:01:53.109 align:start position:0%
we need our opening HTML tag we're gonna
have<00:01:50.880><c> a</c><00:01:50.909><c> head</c><00:01:51.180><c> head</c><00:01:51.540><c> tag</c><00:01:51.840><c> what's</c><00:01:52.530><c> the</c><00:01:52.619><c> title</c>

00:01:53.109 --> 00:01:53.119 align:start position:0%
have a head head tag what's the title
 

00:01:53.119 --> 00:01:59.200 align:start position:0%
have a head head tag what's the title
and<00:01:54.119><c> there</c><00:01:55.110><c> tells</c><00:01:55.320><c> us</c><00:01:55.439><c> we</c><00:01:55.560><c> called</c><00:01:55.799><c> this</c><00:01:56.250><c> demo</c>

00:01:59.200 --> 00:01:59.210 align:start position:0%
 
 

00:01:59.210 --> 00:02:01.820 align:start position:0%
 
oops

00:02:01.820 --> 00:02:01.830 align:start position:0%
oops
 

00:02:01.830 --> 00:02:08.760 align:start position:0%
oops
and<00:02:03.240><c> let's</c><00:02:04.240><c> close</c><00:02:04.900><c> off</c><00:02:05.110><c> our</c><00:02:05.939><c> head</c><00:02:06.939><c> tag</c><00:02:07.709><c> now</c><00:02:08.709><c> we</c>

00:02:08.760 --> 00:02:08.770 align:start position:0%
and let's close off our head tag now we
 

00:02:08.770 --> 00:02:12.600 align:start position:0%
and let's close off our head tag now we
need<00:02:09.069><c> a</c><00:02:09.310><c> body</c><00:02:09.610><c> tag</c><00:02:09.849><c> and</c><00:02:10.440><c> like</c><00:02:11.440><c> I</c><00:02:11.470><c> said</c><00:02:11.620><c> last</c>

00:02:12.600 --> 00:02:12.610 align:start position:0%
need a body tag and like I said last
 

00:02:12.610 --> 00:02:14.130 align:start position:0%
need a body tag and like I said last
video<00:02:12.849><c> it's</c><00:02:13.239><c> sometimes</c><00:02:13.630><c> it's</c><00:02:13.720><c> nice</c><00:02:13.840><c> just</c><00:02:13.900><c> go</c>

00:02:14.130 --> 00:02:14.140 align:start position:0%
video it's sometimes it's nice just go
 

00:02:14.140 --> 00:02:15.180 align:start position:0%
video it's sometimes it's nice just go
ahead<00:02:14.230><c> and</c><00:02:14.440><c> close</c><00:02:14.560><c> them</c><00:02:14.739><c> off</c><00:02:14.890><c> so</c><00:02:15.069><c> you</c><00:02:15.160><c> don't</c>

00:02:15.180 --> 00:02:15.190 align:start position:0%
ahead and close them off so you don't
 

00:02:15.190 --> 00:02:19.260 align:start position:0%
ahead and close them off so you don't
forget<00:02:16.110><c> and</c><00:02:17.110><c> let's</c><00:02:17.980><c> create</c><00:02:18.220><c> an</c><00:02:18.430><c> ordered</c><00:02:19.239><c> list</c>

00:02:19.260 --> 00:02:19.270 align:start position:0%
forget and let's create an ordered list
 

00:02:19.270 --> 00:02:23.280 align:start position:0%
forget and let's create an ordered list
first<00:02:19.780><c> so</c><00:02:20.500><c> what</c><00:02:21.370><c> we</c><00:02:21.489><c> need</c><00:02:21.670><c> is</c><00:02:21.819><c> the</c><00:02:22.120><c> L</c><00:02:22.330><c> tag</c><00:02:22.660><c> for</c>

00:02:23.280 --> 00:02:23.290 align:start position:0%
first so what we need is the L tag for
 

00:02:23.290 --> 00:02:26.040 align:start position:0%
first so what we need is the L tag for
ordered<00:02:23.590><c> list</c><00:02:23.830><c> let's</c><00:02:24.790><c> go</c><00:02:25.390><c> ahead</c><00:02:25.480><c> and</c><00:02:25.660><c> close</c>

00:02:26.040 --> 00:02:26.050 align:start position:0%
ordered list let's go ahead and close
 

00:02:26.050 --> 00:02:30.000 align:start position:0%
ordered list let's go ahead and close
that<00:02:26.080><c> off</c><00:02:26.380><c> and</c><00:02:27.330><c> let's</c><00:02:28.330><c> create</c><00:02:28.540><c> let's</c><00:02:29.530><c> do</c><00:02:29.620><c> for</c>

00:02:30.000 --> 00:02:30.010 align:start position:0%
that off and let's create let's do for
 

00:02:30.010 --> 00:02:32.970 align:start position:0%
that off and let's create let's do for
ordered<00:02:30.690><c> list</c><00:02:31.690><c> items</c><00:02:32.019><c> or</c><00:02:32.530><c> just</c><00:02:32.680><c> for</c><00:02:32.800><c> list</c>

00:02:32.970 --> 00:02:32.980 align:start position:0%
ordered list items or just for list
 

00:02:32.980 --> 00:02:38.699 align:start position:0%
ordered list items or just for list
items<00:02:35.700><c> so</c><00:02:36.700><c> go</c><00:02:36.970><c> ahead</c><00:02:37.180><c> and</c><00:02:37.450><c> create</c><00:02:38.260><c> an</c><00:02:38.410><c> opening</c>

00:02:38.699 --> 00:02:38.709 align:start position:0%
items so go ahead and create an opening
 

00:02:38.709 --> 00:02:42.150 align:start position:0%
items so go ahead and create an opening
and<00:02:38.830><c> closing</c><00:02:39.190><c> one</c><00:02:39.390><c> let's</c><00:02:40.830><c> copy</c><00:02:41.830><c> that</c><00:02:41.920><c> four</c>

00:02:42.150 --> 00:02:42.160 align:start position:0%
and closing one let's copy that four
 

00:02:42.160 --> 00:02:52.370 align:start position:0%
and closing one let's copy that four
times<00:02:42.630><c> okay</c><00:02:46.350><c> say</c><00:02:48.810><c> first</c><00:02:49.810><c> step</c><00:02:51.000><c> I'm</c><00:02:52.000><c> not</c><00:02:52.150><c> very</c>

00:02:52.370 --> 00:02:52.380 align:start position:0%
times okay say first step I'm not very
 

00:02:52.380 --> 00:02:56.100 align:start position:0%
times okay say first step I'm not very
good<00:02:53.380><c> at</c><00:02:53.830><c> coming</c><00:02:54.010><c> up</c><00:02:54.160><c> with</c><00:02:54.390><c> great</c><00:02:55.390><c> examples</c><00:02:55.870><c> so</c>

00:02:56.100 --> 00:02:56.110 align:start position:0%
good at coming up with great examples so
 

00:02:56.110 --> 00:02:59.759 align:start position:0%
good at coming up with great examples so
this<00:02:56.290><c> is</c><00:02:57.150><c> so</c><00:02:58.150><c> we're</c><00:02:58.269><c> gonna</c><00:02:58.360><c> do</c><00:02:58.510><c> for</c><00:02:58.630><c> now</c><00:02:58.769><c> okay</c>

00:02:59.759 --> 00:02:59.769 align:start position:0%
this is so we're gonna do for now okay
 

00:02:59.769 --> 00:03:03.780 align:start position:0%
this is so we're gonna do for now okay
so<00:03:00.870><c> we're</c><00:03:01.870><c> gonna</c><00:03:01.959><c> have</c><00:03:02.170><c> four</c><00:03:02.950><c> less</c><00:03:03.100><c> items</c><00:03:03.430><c> it</c>

00:03:03.780 --> 00:03:03.790 align:start position:0%
so we're gonna have four less items it
 

00:03:03.790 --> 00:03:04.830 align:start position:0%
so we're gonna have four less items it
doesn't<00:03:04.030><c> really</c><00:03:04.120><c> matter</c><00:03:04.360><c> what</c><00:03:04.480><c> you</c><00:03:04.600><c> put</c><00:03:04.720><c> in</c>

00:03:04.830 --> 00:03:04.840 align:start position:0%
doesn't really matter what you put in
 

00:03:04.840 --> 00:03:05.940 align:start position:0%
doesn't really matter what you put in
here<00:03:04.959><c> you</c><00:03:05.079><c> can</c><00:03:05.200><c> just</c><00:03:05.320><c> think</c><00:03:05.380><c> of</c><00:03:05.620><c> all</c><00:03:05.769><c> along</c>

00:03:05.940 --> 00:03:05.950 align:start position:0%
here you can just think of all along
 

00:03:05.950 --> 00:03:07.170 align:start position:0%
here you can just think of all along
with<00:03:06.100><c> what</c><00:03:06.220><c> I'm</c><00:03:06.280><c> doing</c><00:03:06.340><c> but</c><00:03:06.910><c> it</c><00:03:07.000><c> doesn't</c>

00:03:07.170 --> 00:03:07.180 align:start position:0%
with what I'm doing but it doesn't
 

00:03:07.180 --> 00:03:10.050 align:start position:0%
with what I'm doing but it doesn't
really<00:03:07.269><c> matter</c><00:03:07.480><c> and</c><00:03:08.609><c> the</c><00:03:09.609><c> browser</c><00:03:09.820><c> is</c><00:03:09.940><c> gonna</c>

00:03:10.050 --> 00:03:10.060 align:start position:0%
really matter and the browser is gonna
 

00:03:10.060 --> 00:03:12.390 align:start position:0%
really matter and the browser is gonna
know<00:03:10.269><c> because</c><00:03:10.690><c> these</c><00:03:11.230><c> list</c><00:03:11.470><c> items</c><00:03:11.739><c> earn-in</c><00:03:12.130><c> a</c>

00:03:12.390 --> 00:03:12.400 align:start position:0%
know because these list items earn-in a
 

00:03:12.400 --> 00:03:14.849 align:start position:0%
know because these list items earn-in a
well<00:03:12.700><c> tagged</c><00:03:12.940><c> or</c><00:03:13.209><c> ordered</c><00:03:13.630><c> list</c><00:03:13.870><c> it's</c><00:03:14.650><c> gonna</c>

00:03:14.849 --> 00:03:14.859 align:start position:0%
well tagged or ordered list it's gonna
 

00:03:14.859 --> 00:03:16.229 align:start position:0%
well tagged or ordered list it's gonna
know<00:03:14.950><c> the</c><00:03:15.070><c> number</c><00:03:15.340><c> of</c><00:03:15.400><c> them</c><00:03:15.489><c> which</c><00:03:15.850><c> we'll</c><00:03:16.060><c> see</c>

00:03:16.229 --> 00:03:16.239 align:start position:0%
know the number of them which we'll see
 

00:03:16.239 --> 00:03:19.380 align:start position:0%
know the number of them which we'll see
here<00:03:16.450><c> in</c><00:03:16.510><c> a</c><00:03:16.570><c> minute</c><00:03:17.100><c> so</c><00:03:18.100><c> go</c><00:03:18.340><c> ahead</c><00:03:18.430><c> and</c><00:03:18.549><c> let's</c>

00:03:19.380 --> 00:03:19.390 align:start position:0%
here in a minute so go ahead and let's
 

00:03:19.390 --> 00:03:21.750 align:start position:0%
here in a minute so go ahead and let's
go<00:03:19.540><c> down</c><00:03:19.720><c> a</c><00:03:20.170><c> couple</c><00:03:20.620><c> spaces</c><00:03:20.859><c> and</c><00:03:21.250><c> let's</c><00:03:21.609><c> create</c>

00:03:21.750 --> 00:03:21.760 align:start position:0%
go down a couple spaces and let's create
 

00:03:21.760 --> 00:03:24.599 align:start position:0%
go down a couple spaces and let's create
an<00:03:22.000><c> unordered</c><00:03:22.209><c> list</c><00:03:22.690><c> tag</c><00:03:22.780><c> which</c><00:03:23.320><c> is</c><00:03:23.590><c> just</c><00:03:24.220><c> the</c>

00:03:24.599 --> 00:03:24.609 align:start position:0%
an unordered list tag which is just the
 

00:03:24.609 --> 00:03:28.729 align:start position:0%
an unordered list tag which is just the
UL<00:03:24.940><c> tag</c><00:03:25.180><c> for</c><00:03:25.510><c> unordered</c><00:03:26.079><c> lists</c><00:03:26.380><c> okay</c><00:03:27.570><c> Klose</c>

00:03:28.729 --> 00:03:28.739 align:start position:0%
UL tag for unordered lists okay Klose
 

00:03:28.739 --> 00:03:32.009 align:start position:0%
UL tag for unordered lists okay Klose
and<00:03:29.739><c> let's</c><00:03:30.459><c> just</c><00:03:30.579><c> create</c><00:03:30.790><c> three</c><00:03:31.180><c> list</c><00:03:31.720><c> items</c>

00:03:32.009 --> 00:03:32.019 align:start position:0%
and let's just create three list items
 

00:03:32.019 --> 00:03:37.710 align:start position:0%
and let's just create three list items
here<00:03:33.720><c> either</c><00:03:34.720><c> one</c><00:03:34.989><c> you</c><00:03:35.109><c> can</c><00:03:35.850><c> you</c><00:03:36.850><c> can</c><00:03:37.060><c> go</c><00:03:37.600><c> up</c>

00:03:37.710 --> 00:03:37.720 align:start position:0%
here either one you can you can go up
 

00:03:37.720 --> 00:03:39.780 align:start position:0%
here either one you can you can go up
there<00:03:37.930><c> and</c><00:03:38.200><c> you</c><00:03:38.650><c> can</c><00:03:38.890><c> just</c><00:03:38.920><c> copy</c><00:03:39.340><c> three</c><00:03:39.730><c> of</c>

00:03:39.780 --> 00:03:39.790 align:start position:0%
there and you can just copy three of
 

00:03:39.790 --> 00:03:41.340 align:start position:0%
there and you can just copy three of
those<00:03:39.880><c> doesn't</c><00:03:40.420><c> matter</c><00:03:40.660><c> if</c><00:03:40.720><c> this</c><00:03:40.810><c> is</c><00:03:40.870><c> just</c><00:03:41.140><c> for</c>

00:03:41.340 --> 00:03:41.350 align:start position:0%
those doesn't matter if this is just for
 

00:03:41.350 --> 00:03:43.470 align:start position:0%
those doesn't matter if this is just for
showing<00:03:42.100><c> you</c><00:03:42.340><c> what</c><00:03:42.850><c> it</c><00:03:42.970><c> looks</c><00:03:43.120><c> like</c><00:03:43.269><c> in</c><00:03:43.420><c> the</c>

00:03:43.470 --> 00:03:43.480 align:start position:0%
showing you what it looks like in the
 

00:03:43.480 --> 00:03:46.460 align:start position:0%
showing you what it looks like in the
browser

00:03:46.460 --> 00:03:46.470 align:start position:0%
 
 

00:03:46.470 --> 00:03:55.890 align:start position:0%
 
let's<00:03:47.470><c> do</c><00:03:49.410><c> step</c><00:03:50.410><c> one</c><00:03:52.620><c> step</c><00:03:53.620><c> two</c><00:03:54.840><c> and</c><00:03:55.840><c> step</c>

00:03:55.890 --> 00:03:55.900 align:start position:0%
let's do step one step two and step
 

00:03:55.900 --> 00:03:58.710 align:start position:0%
let's do step one step two and step
three<00:03:56.970><c> alright</c><00:03:57.970><c> the</c><00:03:58.120><c> last</c><00:03:58.180><c> thing</c><00:03:58.389><c> we</c><00:03:58.540><c> need</c><00:03:58.690><c> to</c>

00:03:58.710 --> 00:03:58.720 align:start position:0%
three alright the last thing we need to
 

00:03:58.720 --> 00:04:01.440 align:start position:0%
three alright the last thing we need to
do<00:03:58.989><c> is</c><00:03:59.230><c> we</c><00:03:59.980><c> have</c><00:04:00.160><c> one</c><00:04:00.549><c> more</c><00:04:00.639><c> tag</c><00:04:00.850><c> that</c><00:04:01.180><c> it's</c><00:04:01.329><c> not</c>

00:04:01.440 --> 00:04:01.450 align:start position:0%
do is we have one more tag that it's not
 

00:04:01.450 --> 00:04:05.640 align:start position:0%
do is we have one more tag that it's not
closed<00:04:01.780><c> and</c><00:04:01.989><c> that's</c><00:04:02.290><c> the</c><00:04:02.380><c> HTML</c><00:04:02.769><c> tag</c><00:04:04.500><c> so</c><00:04:05.500><c> if</c><00:04:05.590><c> you</c>

00:04:05.640 --> 00:04:05.650 align:start position:0%
closed and that's the HTML tag so if you
 

00:04:05.650 --> 00:04:06.130 align:start position:0%
closed and that's the HTML tag so if you
look<00:04:05.769><c> here</c>

00:04:06.130 --> 00:04:06.140 align:start position:0%
look here
 

00:04:06.140 --> 00:04:08.460 align:start position:0%
look here
this<00:04:06.500><c> let's</c><00:04:06.950><c> go</c><00:04:07.040><c> over</c><00:04:07.069><c> this</c><00:04:07.190><c> real</c><00:04:07.340><c> quick</c><00:04:07.730><c> and</c>

00:04:08.460 --> 00:04:08.470 align:start position:0%
this let's go over this real quick and
 

00:04:08.470 --> 00:04:11.559 align:start position:0%
this let's go over this real quick and
so<00:04:09.470><c> we</c><00:04:09.590><c> have</c><00:04:09.710><c> HTML</c><00:04:10.000><c> where</c><00:04:11.000><c> the</c><00:04:11.120><c> closing</c><00:04:11.420><c> and</c>

00:04:11.559 --> 00:04:11.569 align:start position:0%
so we have HTML where the closing and
 

00:04:11.569 --> 00:04:13.420 align:start position:0%
so we have HTML where the closing and
opening<00:04:11.600><c> HTML</c><00:04:11.930><c> tags</c><00:04:12.350><c> we</c><00:04:12.950><c> have</c><00:04:12.980><c> a</c><00:04:13.100><c> head</c><00:04:13.250><c> tag</c>

00:04:13.420 --> 00:04:13.430 align:start position:0%
opening HTML tags we have a head tag
 

00:04:13.430 --> 00:04:15.790 align:start position:0%
opening HTML tags we have a head tag
which<00:04:13.850><c> holds</c><00:04:14.180><c> the</c><00:04:14.300><c> metadata</c><00:04:15.080><c> for</c><00:04:15.560><c> the</c><00:04:15.680><c> web</c>

00:04:15.790 --> 00:04:15.800 align:start position:0%
which holds the metadata for the web
 

00:04:15.800 --> 00:04:17.710 align:start position:0%
which holds the metadata for the web
page<00:04:15.980><c> for</c><00:04:16.850><c> now</c><00:04:16.940><c> all</c><00:04:17.180><c> we're</c><00:04:17.359><c> concerned</c><00:04:17.600><c> is</c>

00:04:17.710 --> 00:04:17.720 align:start position:0%
page for now all we're concerned is
 

00:04:17.720 --> 00:04:19.180 align:start position:0%
page for now all we're concerned is
about<00:04:17.870><c> the</c><00:04:17.959><c> title</c><00:04:18.230><c> so</c><00:04:18.709><c> we</c><00:04:18.799><c> have</c><00:04:18.920><c> a</c><00:04:18.950><c> title</c>

00:04:19.180 --> 00:04:19.190 align:start position:0%
about the title so we have a title
 

00:04:19.190 --> 00:04:21.610 align:start position:0%
about the title so we have a title
called<00:04:20.060><c> list</c><00:04:20.269><c> demo</c><00:04:20.570><c> and</c><00:04:20.750><c> then</c><00:04:21.230><c> within</c><00:04:21.500><c> our</c>

00:04:21.610 --> 00:04:21.620 align:start position:0%
called list demo and then within our
 

00:04:21.620 --> 00:04:23.440 align:start position:0%
called list demo and then within our
body<00:04:21.859><c> tag</c><00:04:22.100><c> which</c><00:04:22.340><c> is</c><00:04:22.370><c> what</c><00:04:22.669><c> is</c><00:04:22.820><c> displayed</c><00:04:23.270><c> in</c>

00:04:23.440 --> 00:04:23.450 align:start position:0%
body tag which is what is displayed in
 

00:04:23.450 --> 00:04:25.990 align:start position:0%
body tag which is what is displayed in
the<00:04:23.480><c> browser</c><00:04:23.770><c> we</c><00:04:24.770><c> have</c><00:04:24.890><c> an</c><00:04:24.980><c> unordered</c><00:04:25.490><c> list</c><00:04:25.550><c> in</c>

00:04:25.990 --> 00:04:26.000 align:start position:0%
the browser we have an unordered list in
 

00:04:26.000 --> 00:04:29.080 align:start position:0%
the browser we have an unordered list in
and<00:04:26.870><c> an</c><00:04:27.590><c> unordered</c><00:04:28.130><c> list</c><00:04:28.250><c> and</c><00:04:28.640><c> they</c><00:04:28.910><c> each</c><00:04:29.060><c> have</c>

00:04:29.080 --> 00:04:29.090 align:start position:0%
and an unordered list and they each have
 

00:04:29.090 --> 00:04:32.740 align:start position:0%
and an unordered list and they each have
list<00:04:29.690><c> items</c><00:04:30.020><c> inside</c><00:04:30.350><c> of</c><00:04:30.500><c> them</c><00:04:31.540><c> alright</c><00:04:32.540><c> so</c>

00:04:32.740 --> 00:04:32.750 align:start position:0%
list items inside of them alright so
 

00:04:32.750 --> 00:04:34.180 align:start position:0%
list items inside of them alright so
what<00:04:33.110><c> we're</c><00:04:33.200><c> gonna</c><00:04:33.290><c> do</c><00:04:33.440><c> is</c><00:04:33.620><c> go</c><00:04:33.860><c> ahead</c><00:04:33.919><c> and</c><00:04:34.070><c> save</c>

00:04:34.180 --> 00:04:34.190 align:start position:0%
what we're gonna do is go ahead and save
 

00:04:34.190 --> 00:04:38.159 align:start position:0%
what we're gonna do is go ahead and save
this<00:04:35.380><c> call</c><00:04:36.380><c> this</c><00:04:36.530><c> list</c>

00:04:38.159 --> 00:04:38.169 align:start position:0%
this call this list
 

00:04:38.169 --> 00:04:43.060 align:start position:0%
this call this list
oops<00:04:39.490><c> HTML</c><00:04:41.260><c> so</c><00:04:42.260><c> where</c><00:04:42.380><c> do</c><00:04:42.440><c> we</c><00:04:42.470><c> say</c><00:04:42.740><c> this</c><00:04:42.890><c> is</c>

00:04:43.060 --> 00:04:43.070 align:start position:0%
oops HTML so where do we say this is
 

00:04:43.070 --> 00:04:46.330 align:start position:0%
oops HTML so where do we say this is
gonna<00:04:43.220><c> create</c><00:04:43.520><c> a</c><00:04:44.390><c> file</c><00:04:45.020><c> here</c><00:04:45.620><c> on</c><00:04:45.650><c> desktop</c><00:04:46.070><c> like</c>

00:04:46.330 --> 00:04:46.340 align:start position:0%
gonna create a file here on desktop like
 

00:04:46.340 --> 00:04:49.170 align:start position:0%
gonna create a file here on desktop like
it<00:04:46.490><c> just</c><00:04:46.669><c> did</c>

00:04:49.170 --> 00:04:49.180 align:start position:0%
 
 

00:04:49.180 --> 00:04:51.850 align:start position:0%
 
first<00:04:50.180><c> off</c><00:04:50.390><c> notice</c><00:04:50.930><c> I</c><00:04:51.110><c> did</c><00:04:51.230><c> something</c><00:04:51.500><c> wrong</c><00:04:51.620><c> I</c>

00:04:51.850 --> 00:04:51.860 align:start position:0%
first off notice I did something wrong I
 

00:04:51.860 --> 00:04:54.280 align:start position:0%
first off notice I did something wrong I
had<00:04:52.130><c> these</c><00:04:52.280><c> I</c><00:04:52.610><c> don't</c><00:04:53.300><c> know</c><00:04:53.450><c> what</c><00:04:53.570><c> happened</c><00:04:53.870><c> I</c>

00:04:54.280 --> 00:04:54.290 align:start position:0%
had these I don't know what happened I
 

00:04:54.290 --> 00:05:00.940 align:start position:0%
had these I don't know what happened I
didn't<00:04:54.680><c> close</c><00:04:54.890><c> these</c><00:04:55.160><c> tags</c><00:04:55.460><c> okay</c><00:04:57.910><c> so</c><00:04:59.860><c> let's</c><00:05:00.860><c> do</c>

00:05:00.940 --> 00:05:00.950 align:start position:0%
didn't close these tags okay so let's do
 

00:05:00.950 --> 00:05:07.659 align:start position:0%
didn't close these tags okay so let's do
it<00:05:00.980><c> again</c><00:05:04.419><c> okay</c><00:05:05.419><c> so</c><00:05:05.630><c> as</c><00:05:06.560><c> you</c><00:05:06.620><c> can</c><00:05:06.770><c> see</c><00:05:06.830><c> we</c><00:05:07.430><c> had</c>

00:05:07.659 --> 00:05:07.669 align:start position:0%
it again okay so as you can see we had
 

00:05:07.669 --> 00:05:13.840 align:start position:0%
it again okay so as you can see we had
our<00:05:08.260><c> title</c><00:05:09.310><c> let's</c><00:05:10.630><c> so</c><00:05:11.630><c> you</c><00:05:11.720><c> can</c><00:05:11.840><c> see</c><00:05:12.050><c> here</c><00:05:12.850><c> we</c>

00:05:13.840 --> 00:05:13.850 align:start position:0%
our title let's so you can see here we
 

00:05:13.850 --> 00:05:15.820 align:start position:0%
our title let's so you can see here we
have<00:05:13.940><c> our</c><00:05:14.060><c> title</c><00:05:14.240><c> which</c><00:05:14.990><c> is</c><00:05:15.140><c> up</c><00:05:15.320><c> here</c><00:05:15.350><c> called</c>

00:05:15.820 --> 00:05:15.830 align:start position:0%
have our title which is up here called
 

00:05:15.830 --> 00:05:19.360 align:start position:0%
have our title which is up here called
this<00:05:15.950><c> demo</c><00:05:16.250><c> we</c><00:05:17.030><c> have</c><00:05:17.150><c> an</c><00:05:17.410><c> unordered</c><00:05:18.410><c> list</c><00:05:18.680><c> with</c>

00:05:19.360 --> 00:05:19.370 align:start position:0%
this demo we have an unordered list with
 

00:05:19.370 --> 00:05:21.490 align:start position:0%
this demo we have an unordered list with
four<00:05:19.970><c> of</c><00:05:20.060><c> them</c><00:05:20.210><c> so</c><00:05:20.450><c> it's</c><00:05:20.840><c> listed</c><00:05:21.110><c> one</c><00:05:21.320><c> two</c>

00:05:21.490 --> 00:05:21.500 align:start position:0%
four of them so it's listed one two
 

00:05:21.500 --> 00:05:25.480 align:start position:0%
four of them so it's listed one two
three<00:05:21.800><c> four</c><00:05:22.070><c> over</c><00:05:22.850><c> here</c><00:05:23.770><c> let's</c><00:05:24.770><c> bring</c><00:05:25.430><c> that</c>

00:05:25.480 --> 00:05:25.490 align:start position:0%
three four over here let's bring that
 

00:05:25.490 --> 00:05:28.570 align:start position:0%
three four over here let's bring that
let's<00:05:26.060><c> go</c><00:05:26.270><c> in</c><00:05:26.390><c> there</c><00:05:26.540><c> also</c><00:05:26.990><c> that</c><00:05:27.380><c> hold</c><00:05:27.530><c> down</c><00:05:27.580><c> so</c>

00:05:28.570 --> 00:05:28.580 align:start position:0%
let's go in there also that hold down so
 

00:05:28.580 --> 00:05:31.090 align:start position:0%
let's go in there also that hold down so
we<00:05:28.669><c> have</c><00:05:28.790><c> again</c><00:05:29.270><c> the</c><00:05:29.390><c> four</c><00:05:29.600><c> list</c><00:05:29.780><c> items</c><00:05:30.100><c> that</c>

00:05:31.090 --> 00:05:31.100 align:start position:0%
we have again the four list items that
 

00:05:31.100 --> 00:05:33.520 align:start position:0%
we have again the four list items that
are<00:05:31.220><c> ordered</c><00:05:31.520><c> and</c><00:05:31.729><c> in</c><00:05:31.880><c> the</c><00:05:32.030><c> unordered</c><00:05:32.870><c> list</c>

00:05:33.520 --> 00:05:33.530 align:start position:0%
are ordered and in the unordered list
 

00:05:33.530 --> 00:05:37.210 align:start position:0%
are ordered and in the unordered list
tag<00:05:34.270><c> we</c><00:05:35.270><c> have</c><00:05:35.300><c> three</c><00:05:35.840><c> items</c><00:05:36.110><c> and</c><00:05:36.290><c> they're</c><00:05:37.100><c> just</c>

00:05:37.210 --> 00:05:37.220 align:start position:0%
tag we have three items and they're just
 

00:05:37.220 --> 00:05:39.060 align:start position:0%
tag we have three items and they're just
blue<00:05:37.370><c> points</c><00:05:37.640><c> because</c><00:05:38.120><c> they're</c><00:05:38.360><c> not</c><00:05:38.479><c> ordered</c>

00:05:39.060 --> 00:05:39.070 align:start position:0%
blue points because they're not ordered
 

00:05:39.070 --> 00:05:42.280 align:start position:0%
blue points because they're not ordered
all<00:05:40.070><c> right</c><00:05:40.250><c> so</c><00:05:40.430><c> these</c><00:05:40.580><c> are</c><00:05:40.790><c> this</c><00:05:41.060><c> is</c><00:05:41.290><c> basically</c>

00:05:42.280 --> 00:05:42.290 align:start position:0%
all right so these are this is basically
 

00:05:42.290 --> 00:05:44.680 align:start position:0%
all right so these are this is basically
how<00:05:42.650><c> you</c><00:05:42.710><c> do</c><00:05:43.070><c> lists</c><00:05:43.880><c> this</c><00:05:44.090><c> not</c><00:05:44.540><c> that</c>

00:05:44.680 --> 00:05:44.690 align:start position:0%
how you do lists this not that
 

00:05:44.690 --> 00:05:46.960 align:start position:0%
how you do lists this not that
complicated<00:05:44.960><c> and</c><00:05:45.440><c> I</c><00:05:46.040><c> know</c><00:05:46.130><c> it</c><00:05:46.310><c> looks</c><00:05:46.430><c> ugly</c><00:05:46.610><c> but</c>

00:05:46.960 --> 00:05:46.970 align:start position:0%
complicated and I know it looks ugly but
 

00:05:46.970 --> 00:05:49.270 align:start position:0%
complicated and I know it looks ugly but
when<00:05:47.479><c> we</c><00:05:47.540><c> get</c><00:05:47.660><c> to</c><00:05:47.750><c> the</c><00:05:47.840><c> CSS</c><00:05:48.229><c> portion</c><00:05:48.620><c> will</c><00:05:49.100><c> do</c>

00:05:49.270 --> 00:05:49.280 align:start position:0%
when we get to the CSS portion will do
 

00:05:49.280 --> 00:05:52.060 align:start position:0%
when we get to the CSS portion will do
we'll<00:05:50.210><c> worry</c><00:05:50.630><c> about</c><00:05:50.660><c> it</c><00:05:50.840><c> then</c><00:05:51.080><c> but</c><00:05:51.860><c> for</c><00:05:52.010><c> now</c>

00:05:52.060 --> 00:05:52.070 align:start position:0%
we'll worry about it then but for now
 

00:05:52.070 --> 00:05:54.659 align:start position:0%
we'll worry about it then but for now
this<00:05:52.880><c> is</c><00:05:52.940><c> how</c><00:05:53.090><c> this</c><00:05:53.600><c> is</c><00:05:53.660><c> how</c><00:05:53.780><c> this</c><00:05:54.080><c> work</c><00:05:54.289><c> and</c>

00:05:54.659 --> 00:05:54.669 align:start position:0%
this is how this is how this work and
 

00:05:54.669 --> 00:05:56.740 align:start position:0%
this is how this is how this work and
hope<00:05:55.669><c> you</c><00:05:55.820><c> hope</c><00:05:56.030><c> you</c><00:05:56.120><c> learn</c><00:05:56.240><c> something</c><00:05:56.300><c> and</c>

00:05:56.740 --> 00:05:56.750 align:start position:0%
hope you hope you learn something and
 

00:05:56.750 --> 00:06:00.160 align:start position:0%
hope you hope you learn something and
I'll<00:05:57.410><c> see</c><00:05:57.680><c> you</c><00:05:57.740><c> guys</c><00:05:57.800><c> next</c><00:05:57.890><c> time</c>

