WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.990 align:start position:0%
 
So<00:00:00.320><c> in</c><00:00:00.480><c> this</c><00:00:00.719><c> case,</c><00:00:00.880><c> what</c><00:00:01.120><c> I</c><00:00:01.280><c> have</c><00:00:01.360><c> is</c><00:00:01.520><c> I</c><00:00:01.760><c> have</c><00:00:01.839><c> a</c>

00:00:01.990 --> 00:00:02.000 align:start position:0%
So in this case, what I have is I have a
 

00:00:02.000 --> 00:00:05.110 align:start position:0%
So in this case, what I have is I have a
function<00:00:02.399><c> called</c><00:00:02.639><c> classify</c><00:00:03.560><c> message.</c><00:00:04.560><c> And</c>

00:00:05.110 --> 00:00:05.120 align:start position:0%
function called classify message. And
 

00:00:05.120 --> 00:00:06.630 align:start position:0%
function called classify message. And
classify<00:00:05.600><c> message</c><00:00:05.920><c> takes</c><00:00:06.080><c> in</c><00:00:06.319><c> a</c><00:00:06.480><c> message,</c>

00:00:06.630 --> 00:00:06.640 align:start position:0%
classify message takes in a message,
 

00:00:06.640 --> 00:00:08.150 align:start position:0%
classify message takes in a message,
which<00:00:06.879><c> is</c><00:00:06.960><c> a</c><00:00:07.120><c> string.</c><00:00:07.440><c> So</c><00:00:07.520><c> it</c><00:00:07.680><c> could</c><00:00:07.839><c> come</c><00:00:07.919><c> in</c>

00:00:08.150 --> 00:00:08.160 align:start position:0%
which is a string. So it could come in
 

00:00:08.160 --> 00:00:10.549 align:start position:0%
which is a string. So it could come in
from<00:00:08.320><c> a</c><00:00:08.559><c> review</c><00:00:08.880><c> on</c><00:00:09.200><c> Amazon,</c><00:00:09.760><c> a</c><00:00:10.080><c> tweet,</c><00:00:10.400><c> or</c>

00:00:10.549 --> 00:00:10.559 align:start position:0%
from a review on Amazon, a tweet, or
 

00:00:10.559 --> 00:00:12.390 align:start position:0%
from a review on Amazon, a tweet, or
really<00:00:10.880><c> anything</c><00:00:11.120><c> that</c><00:00:11.360><c> I</c><00:00:11.519><c> really</c><00:00:11.759><c> have,</c><00:00:12.240><c> a</c>

00:00:12.390 --> 00:00:12.400 align:start position:0%
really anything that I really have, a
 

00:00:12.400 --> 00:00:13.669 align:start position:0%
really anything that I really have, a
text<00:00:12.639><c> from</c><00:00:12.800><c> my</c><00:00:12.880><c> girlfriend,</c><00:00:13.280><c> it</c><00:00:13.440><c> doesn't</c>

00:00:13.669 --> 00:00:13.679 align:start position:0%
text from my girlfriend, it doesn't
 

00:00:13.679 --> 00:00:15.669 align:start position:0%
text from my girlfriend, it doesn't
matter.<00:00:14.400><c> And</c><00:00:14.559><c> all</c><00:00:14.799><c> I</c><00:00:14.960><c> want</c><00:00:15.040><c> to</c><00:00:15.120><c> know</c><00:00:15.280><c> is</c><00:00:15.440><c> a</c>

00:00:15.669 --> 00:00:15.679 align:start position:0%
matter. And all I want to know is a
 

00:00:15.679 --> 00:00:17.470 align:start position:0%
matter. And all I want to know is a
sentiment<00:00:16.000><c> of</c><00:00:16.160><c> it</c><00:00:16.320><c> as</c><00:00:16.640><c> one</c><00:00:16.800><c> of</c><00:00:16.960><c> these</c><00:00:17.199><c> three</c>

00:00:17.470 --> 00:00:17.480 align:start position:0%
sentiment of it as one of these three
 

00:00:17.480 --> 00:00:19.830 align:start position:0%
sentiment of it as one of these three
things.<00:00:18.480><c> So</c><00:00:18.640><c> I</c><00:00:18.800><c> want</c><00:00:18.880><c> to</c><00:00:19.039><c> build</c><00:00:19.199><c> a</c><00:00:19.359><c> calculator</c>

00:00:19.830 --> 00:00:19.840 align:start position:0%
things. So I want to build a calculator
 

00:00:19.840 --> 00:00:21.349 align:start position:0%
things. So I want to build a calculator
that<00:00:20.000><c> can,</c><00:00:20.240><c> no</c><00:00:20.400><c> matter</c><00:00:20.640><c> what</c><00:00:20.800><c> the</c><00:00:20.960><c> message</c><00:00:21.199><c> is,</c>

00:00:21.349 --> 00:00:21.359 align:start position:0%
that can, no matter what the message is,
 

00:00:21.359 --> 00:00:23.189 align:start position:0%
that can, no matter what the message is,
will<00:00:21.600><c> return</c><00:00:21.920><c> one</c><00:00:22.160><c> of</c><00:00:22.320><c> either</c><00:00:22.640><c> positive,</c>

00:00:23.189 --> 00:00:23.199 align:start position:0%
will return one of either positive,
 

00:00:23.199 --> 00:00:26.310 align:start position:0%
will return one of either positive,
neutral,<00:00:23.760><c> or</c><00:00:24.160><c> negative.</c><00:00:25.359><c> Now</c><00:00:25.680><c> the</c><00:00:25.920><c> calculator</c>

00:00:26.310 --> 00:00:26.320 align:start position:0%
neutral, or negative. Now the calculator
 

00:00:26.320 --> 00:00:28.310 align:start position:0%
neutral, or negative. Now the calculator
of<00:00:26.560><c> my</c><00:00:26.720><c> choice</c><00:00:27.039><c> here</c><00:00:27.279><c> is</c><00:00:27.519><c> going</c><00:00:27.599><c> to</c><00:00:27.680><c> be</c><00:00:27.760><c> openi</c>

00:00:28.310 --> 00:00:28.320 align:start position:0%
of my choice here is going to be openi
 

00:00:28.320 --> 00:00:31.029 align:start position:0%
of my choice here is going to be openi
gt40<00:00:29.199><c> but</c><00:00:29.439><c> I</c><00:00:29.599><c> could</c><00:00:29.760><c> use</c><00:00:29.840><c> a</c><00:00:30.080><c> llama</c><00:00:30.480><c> model</c><00:00:30.880><c> I</c>

00:00:31.029 --> 00:00:31.039 align:start position:0%
gt40 but I could use a llama model I
 

00:00:31.039 --> 00:00:33.350 align:start position:0%
gt40 but I could use a llama model I
could<00:00:31.119><c> use</c><00:00:31.359><c> a</c><00:00:32.000><c> entropic</c><00:00:32.559><c> model</c><00:00:32.800><c> gemini</c><00:00:33.200><c> it</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
could use a entropic model gemini it
 

00:00:33.360 --> 00:00:35.670 align:start position:0%
could use a entropic model gemini it
doesn't<00:00:33.600><c> really</c><00:00:33.880><c> matter</c><00:00:34.880><c> and</c><00:00:35.040><c> then</c><00:00:35.200><c> like</c><00:00:35.440><c> that</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
doesn't really matter and then like that
 

00:00:35.680 --> 00:00:37.350 align:start position:0%
doesn't really matter and then like that
operator<00:00:36.160><c> that</c><00:00:36.399><c> I</c><00:00:36.559><c> was</c><00:00:36.640><c> talking</c><00:00:36.880><c> about</c><00:00:37.120><c> is</c>

00:00:37.350 --> 00:00:37.360 align:start position:0%
operator that I was talking about is
 

00:00:37.360 --> 00:00:39.069 align:start position:0%
operator that I was talking about is
becomes<00:00:37.680><c> this</c>

00:00:39.069 --> 00:00:39.079 align:start position:0%
becomes this
 

00:00:39.079 --> 00:00:42.229 align:start position:0%
becomes this
prompt.<00:00:40.160><c> So</c><00:00:40.559><c> now</c><00:00:40.719><c> I'm</c><00:00:40.960><c> able</c><00:00:41.200><c> to</c><00:00:41.360><c> verify</c><00:00:42.000><c> the</c>

00:00:42.229 --> 00:00:42.239 align:start position:0%
prompt. So now I'm able to verify the
 

00:00:42.239 --> 00:00:45.510 align:start position:0%
prompt. So now I'm able to verify the
English<00:00:42.559><c> in</c><00:00:42.800><c> this</c><00:00:43.040><c> prompt</c><00:00:43.680><c> very</c><00:00:44.160><c> very</c><00:00:44.520><c> easily</c>

00:00:45.510 --> 00:00:45.520 align:start position:0%
English in this prompt very very easily
 

00:00:45.520 --> 00:00:47.430 align:start position:0%
English in this prompt very very easily
by<00:00:45.760><c> doing</c><00:00:46.000><c> a</c><00:00:46.239><c> couple</c><00:00:46.399><c> of</c><00:00:46.559><c> things</c><00:00:46.960><c> because</c><00:00:47.280><c> this</c>

00:00:47.430 --> 00:00:47.440 align:start position:0%
by doing a couple of things because this
 

00:00:47.440 --> 00:00:49.670 align:start position:0%
by doing a couple of things because this
isn't<00:00:47.760><c> plain</c><00:00:48.079><c> English.</c><00:00:48.879><c> All</c><00:00:49.039><c> I</c><00:00:49.280><c> have</c><00:00:49.360><c> to</c><00:00:49.440><c> do</c><00:00:49.520><c> is</c>

00:00:49.670 --> 00:00:49.680 align:start position:0%
isn't plain English. All I have to do is
 

00:00:49.680 --> 00:00:52.549 align:start position:0%
isn't plain English. All I have to do is
I<00:00:49.920><c> can</c><00:00:50.120><c> say</c><00:00:51.120><c> is</c><00:00:51.360><c> this</c><00:00:51.600><c> English</c><00:00:52.000><c> going</c><00:00:52.160><c> to</c><00:00:52.320><c> lead</c>

00:00:52.549 --> 00:00:52.559 align:start position:0%
I can say is this English going to lead
 

00:00:52.559 --> 00:00:53.990 align:start position:0%
I can say is this English going to lead
this<00:00:52.800><c> message</c><00:00:53.280><c> to</c><00:00:53.360><c> being</c><00:00:53.520><c> the</c><00:00:53.680><c> right</c><00:00:53.840><c> thing</c>

00:00:53.990 --> 00:00:54.000 align:start position:0%
this message to being the right thing
 

00:00:54.000 --> 00:00:55.990 align:start position:0%
this message to being the right thing
for<00:00:54.239><c> a</c><00:00:54.399><c> couple</c><00:00:54.559><c> of</c><00:00:54.719><c> different</c><00:00:54.960><c> test</c><00:00:55.199><c> cases.</c><00:00:55.840><c> So</c>

00:00:55.990 --> 00:00:56.000 align:start position:0%
for a couple of different test cases. So
 

00:00:56.000 --> 00:00:57.510 align:start position:0%
for a couple of different test cases. So
if<00:00:56.160><c> my</c><00:00:56.320><c> girlfriend</c><00:00:56.640><c> sends</c><00:00:56.879><c> me</c><00:00:56.960><c> a</c><00:00:57.120><c> text</c><00:00:57.360><c> that</c>

00:00:57.510 --> 00:00:57.520 align:start position:0%
if my girlfriend sends me a text that
 

00:00:57.520 --> 00:01:00.630 align:start position:0%
if my girlfriend sends me a text that
says<00:00:58.239><c> I</c><00:00:58.399><c> am</c><00:00:58.559><c> incredibly</c><00:00:59.039><c> upset</c><00:00:59.359><c> with</c><00:00:59.520><c> you,</c><00:01:00.399><c> it</c>

00:01:00.630 --> 00:01:00.640 align:start position:0%
says I am incredibly upset with you, it
 

00:01:00.640 --> 00:01:02.150 align:start position:0%
says I am incredibly upset with you, it
should<00:01:00.800><c> return</c><00:01:01.120><c> negative.</c><00:01:01.600><c> This</c><00:01:01.760><c> calculator</c>

00:01:02.150 --> 00:01:02.160 align:start position:0%
should return negative. This calculator
 

00:01:02.160 --> 00:01:03.630 align:start position:0%
should return negative. This calculator
should<00:01:02.320><c> calculate</c><00:01:02.719><c> to</c>

00:01:03.630 --> 00:01:03.640 align:start position:0%
should calculate to
 

00:01:03.640 --> 00:01:06.310 align:start position:0%
should calculate to
negative.<00:01:04.720><c> And</c><00:01:04.960><c> when</c><00:01:05.199><c> you</c><00:01:05.519><c> review</c><00:01:06.080><c> things</c>

00:01:06.310 --> 00:01:06.320 align:start position:0%
negative. And when you review things
 

00:01:06.320 --> 00:01:09.030 align:start position:0%
negative. And when you review things
like<00:01:06.479><c> this,</c><00:01:06.960><c> it</c><00:01:07.280><c> becomes</c><00:01:07.760><c> much</c><00:01:08.080><c> more</c><00:01:08.400><c> ease,</c>

00:01:09.030 --> 00:01:09.040 align:start position:0%
like this, it becomes much more ease,
 

00:01:09.040 --> 00:01:11.350 align:start position:0%
like this, it becomes much more ease,
much<00:01:09.760><c> simpler</c><00:01:10.080><c> to</c><00:01:10.320><c> compose</c><00:01:10.799><c> them</c><00:01:11.040><c> into</c>

00:01:11.350 --> 00:01:11.360 align:start position:0%
much simpler to compose them into
 

00:01:11.360 --> 00:01:12.670 align:start position:0%
much simpler to compose them into
complex

00:01:12.670 --> 00:01:12.680 align:start position:0%
complex
 

00:01:12.680 --> 00:01:15.030 align:start position:0%
complex
systems<00:01:13.680><c> and</c><00:01:14.000><c> know</c><00:01:14.240><c> exactly</c><00:01:14.640><c> what</c><00:01:14.799><c> the</c>

00:01:15.030 --> 00:01:15.040 align:start position:0%
systems and know exactly what the
 

00:01:15.040 --> 00:01:17.190 align:start position:0%
systems and know exactly what the
contract<00:01:15.600><c> I've</c><00:01:15.920><c> built</c><00:01:16.159><c> into</c><00:01:16.479><c> the</c><00:01:16.640><c> system</c><00:01:16.880><c> is,</c>

00:01:17.190 --> 00:01:17.200 align:start position:0%
contract I've built into the system is,
 

00:01:17.200 --> 00:01:20.950 align:start position:0%
contract I've built into the system is,
which<00:01:17.520><c> leads</c><00:01:18.200><c> to</c><00:01:19.200><c> code</c><00:01:19.520><c> that</c><00:01:19.759><c> is</c><00:01:19.920><c> now</c><00:01:20.560><c> usable</c>

00:01:20.950 --> 00:01:20.960 align:start position:0%
which leads to code that is now usable
 

00:01:20.960 --> 00:01:23.350 align:start position:0%
which leads to code that is now usable
by<00:01:21.119><c> static</c><00:01:21.520><c> analyzers,</c><00:01:22.479><c> code</c><00:01:22.720><c> that</c><00:01:22.960><c> is</c><00:01:23.119><c> like</c>

00:01:23.350 --> 00:01:23.360 align:start position:0%
by static analyzers, code that is like
 

00:01:23.360 --> 00:01:25.510 align:start position:0%
by static analyzers, code that is like
detectable<00:01:23.920><c> by</c><00:01:24.159><c> AI</c><00:01:24.560><c> as</c><00:01:24.799><c> like</c><00:01:25.040><c> understanding</c>

00:01:25.510 --> 00:01:25.520 align:start position:0%
detectable by AI as like understanding
 

00:01:25.520 --> 00:01:27.510 align:start position:0%
detectable by AI as like understanding
when<00:01:25.680><c> things</c><00:01:25.920><c> are</c><00:01:26.080><c> going</c><00:01:26.320><c> wrong,</c><00:01:27.040><c> and</c><00:01:27.280><c> most</c>

00:01:27.510 --> 00:01:27.520 align:start position:0%
when things are going wrong, and most
 

00:01:27.520 --> 00:01:29.190 align:start position:0%
when things are going wrong, and most
importantly,<00:01:27.840><c> a</c><00:01:28.080><c> strong</c><00:01:28.400><c> iteration</c><00:01:28.799><c> loop</c>

00:01:29.190 --> 00:01:29.200 align:start position:0%
importantly, a strong iteration loop
 

00:01:29.200 --> 00:01:31.190 align:start position:0%
importantly, a strong iteration loop
because<00:01:29.920><c> if</c><00:01:30.240><c> for</c><00:01:30.400><c> whatever</c><00:01:30.720><c> reason</c><00:01:30.880><c> that</c>

00:01:31.190 --> 00:01:31.200 align:start position:0%
because if for whatever reason that
 

00:01:31.200 --> 00:01:33.990 align:start position:0%
because if for whatever reason that
message<00:01:31.520><c> returns</c><00:01:31.920><c> back</c><00:01:32.079><c> as</c><00:01:32.520><c> neutral,</c><00:01:33.520><c> I</c><00:01:33.840><c> know</c>

00:01:33.990 --> 00:01:34.000 align:start position:0%
message returns back as neutral, I know
 

00:01:34.000 --> 00:01:35.350 align:start position:0%
message returns back as neutral, I know
that<00:01:34.320><c> There's</c><00:01:34.640><c> two</c><00:01:34.799><c> things</c><00:01:34.960><c> that</c><00:01:35.119><c> could</c><00:01:35.280><c> be</c>

00:01:35.350 --> 00:01:35.360 align:start position:0%
that There's two things that could be
 

00:01:35.360 --> 00:01:37.190 align:start position:0%
that There's two things that could be
wrong.<00:01:36.079><c> It's</c><00:01:36.240><c> either</c><00:01:36.560><c> I</c><00:01:36.799><c> need</c><00:01:36.880><c> a</c><00:01:37.040><c> different</c>

00:01:37.190 --> 00:01:37.200 align:start position:0%
wrong. It's either I need a different
 

00:01:37.200 --> 00:01:38.390 align:start position:0%
wrong. It's either I need a different
calculator.<00:01:37.600><c> I</c><00:01:37.759><c> need</c><00:01:37.840><c> to</c><00:01:37.920><c> either</c><00:01:38.159><c> maybe</c>

00:01:38.390 --> 00:01:38.400 align:start position:0%
calculator. I need to either maybe
 

00:01:38.400 --> 00:01:40.870 align:start position:0%
calculator. I need to either maybe
upgrade<00:01:38.720><c> the</c><00:01:38.880><c> model</c><00:01:39.280><c> to</c><00:01:39.439><c> like</c><00:01:39.600><c> Gemini's</c><00:01:40.240><c> 2.5</c>

00:01:40.870 --> 00:01:40.880 align:start position:0%
upgrade the model to like Gemini's 2.5
 

00:01:40.880 --> 00:01:44.149 align:start position:0%
upgrade the model to like Gemini's 2.5
turbo<00:01:41.840><c> or</c><00:01:42.560><c> I</c><00:01:42.799><c> need</c><00:01:42.880><c> to</c><00:01:43.040><c> go</c><00:01:43.200><c> and</c><00:01:43.360><c> up</c><00:01:43.759><c> or</c><00:01:44.000><c> I</c><00:01:44.079><c> need</c>

00:01:44.149 --> 00:01:44.159 align:start position:0%
turbo or I need to go and up or I need
 

00:01:44.159 --> 00:01:45.429 align:start position:0%
turbo or I need to go and up or I need
to<00:01:44.240><c> go</c><00:01:44.320><c> and</c><00:01:44.479><c> update</c><00:01:44.720><c> my</c><00:01:44.880><c> prompt</c><00:01:45.040><c> and</c><00:01:45.200><c> change</c>

00:01:45.429 --> 00:01:45.439 align:start position:0%
to go and update my prompt and change
 

00:01:45.439 --> 00:01:46.950 align:start position:0%
to go and update my prompt and change
the<00:01:45.600><c> operate</c><00:01:45.920><c> operator</c><00:01:46.479><c> that</c><00:01:46.720><c> this</c>

00:01:46.950 --> 00:01:46.960 align:start position:0%
the operate operator that this
 

00:01:46.960 --> 00:01:49.610 align:start position:0%
the operate operator that this
calculator<00:01:47.439><c> is</c><00:01:47.600><c> using.</c><00:01:48.159><c> No.</c>

00:01:49.610 --> 00:01:49.620 align:start position:0%
calculator is using. No.
 

00:01:49.620 --> 00:01:59.529 align:start position:0%
calculator is using. No.
[Music]

