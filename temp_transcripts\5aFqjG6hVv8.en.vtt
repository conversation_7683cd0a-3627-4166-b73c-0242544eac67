WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.850 align:start position:0%
 
hey<00:00:00.420><c> coders</c><00:00:00.900><c> and</c><00:00:01.140><c> welcome</c><00:00:01.319><c> back</c><00:00:01.500><c> to</c><00:00:01.620><c> another</c>

00:00:01.850 --> 00:00:01.860 align:start position:0%
hey coders and welcome back to another
 

00:00:01.860 --> 00:00:04.070 align:start position:0%
hey coders and welcome back to another
video<00:00:02.220><c> and</c><00:00:02.700><c> today</c><00:00:02.940><c> we're</c><00:00:03.240><c> going</c><00:00:03.480><c> to</c><00:00:03.540><c> be</c><00:00:03.600><c> coding</c>

00:00:04.070 --> 00:00:04.080 align:start position:0%
video and today we're going to be coding
 

00:00:04.080 --> 00:00:06.470 align:start position:0%
video and today we're going to be coding
the<00:00:04.380><c> quick</c><00:00:04.560><c> sort</c><00:00:04.980><c> algorithm</c><00:00:05.520><c> without</c><00:00:06.000><c> further</c>

00:00:06.470 --> 00:00:06.480 align:start position:0%
the quick sort algorithm without further
 

00:00:06.480 --> 00:00:08.210 align:start position:0%
the quick sort algorithm without further
Ado<00:00:06.600><c> let's</c><00:00:07.080><c> get</c><00:00:07.259><c> started</c><00:00:07.560><c> the</c><00:00:07.919><c> first</c><00:00:08.040><c> thing</c><00:00:08.160><c> we</c>

00:00:08.210 --> 00:00:08.220 align:start position:0%
Ado let's get started the first thing we
 

00:00:08.220 --> 00:00:10.790 align:start position:0%
Ado let's get started the first thing we
want<00:00:08.340><c> to</c><00:00:08.460><c> do</c><00:00:08.580><c> is</c><00:00:08.880><c> have</c><00:00:09.360><c> a</c><00:00:09.780><c> method</c><00:00:10.080><c> that</c><00:00:10.320><c> calls</c>

00:00:10.790 --> 00:00:10.800 align:start position:0%
want to do is have a method that calls
 

00:00:10.800 --> 00:00:13.610 align:start position:0%
want to do is have a method that calls
or<00:00:11.040><c> kicks</c><00:00:11.400><c> off</c><00:00:11.460><c> the</c><00:00:12.000><c> recursive</c><00:00:12.599><c> part</c><00:00:12.960><c> of</c><00:00:13.380><c> this</c>

00:00:13.610 --> 00:00:13.620 align:start position:0%
or kicks off the recursive part of this
 

00:00:13.620 --> 00:00:17.570 align:start position:0%
or kicks off the recursive part of this
algorithm<00:00:14.040><c> we'll</c><00:00:14.700><c> say</c><00:00:14.940><c> public</c><00:00:15.320><c> void</c><00:00:16.580><c> apply</c>

00:00:17.570 --> 00:00:17.580 align:start position:0%
algorithm we'll say public void apply
 

00:00:17.580 --> 00:00:21.830 align:start position:0%
algorithm we'll say public void apply
sort<00:00:18.480><c> on</c><00:00:19.260><c> an</c><00:00:19.800><c> array</c><00:00:20.279><c> of</c><00:00:20.460><c> integers</c>

00:00:21.830 --> 00:00:21.840 align:start position:0%
sort on an array of integers
 

00:00:21.840 --> 00:00:24.410 align:start position:0%
sort on an array of integers
and<00:00:22.560><c> the</c><00:00:22.800><c> recursive</c><00:00:23.340><c> function</c><00:00:23.760><c> will</c><00:00:24.060><c> just</c><00:00:24.240><c> be</c>

00:00:24.410 --> 00:00:24.420 align:start position:0%
and the recursive function will just be
 

00:00:24.420 --> 00:00:26.330 align:start position:0%
and the recursive function will just be
quick<00:00:24.720><c> sort</c><00:00:25.140><c> we're</c><00:00:25.680><c> going</c><00:00:25.859><c> to</c><00:00:25.920><c> pass</c><00:00:26.039><c> in</c><00:00:26.220><c> the</c>

00:00:26.330 --> 00:00:26.340 align:start position:0%
quick sort we're going to pass in the
 

00:00:26.340 --> 00:00:27.170 align:start position:0%
quick sort we're going to pass in the
array

00:00:27.170 --> 00:00:27.180 align:start position:0%
array
 

00:00:27.180 --> 00:00:31.910 align:start position:0%
array
0<00:00:28.140><c> and</c><00:00:28.800><c> array</c><00:00:29.340><c> dot</c><00:00:29.880><c> length</c><00:00:30.359><c> minus</c><00:00:30.900><c> one</c><00:00:31.080><c> this</c>

00:00:31.910 --> 00:00:31.920 align:start position:0%
0 and array dot length minus one this
 

00:00:31.920 --> 00:00:33.470 align:start position:0%
0 and array dot length minus one this
function<00:00:32.220><c> we're</c><00:00:32.520><c> going</c><00:00:32.640><c> to</c><00:00:32.759><c> pass</c><00:00:32.940><c> in</c><00:00:33.120><c> the</c>

00:00:33.470 --> 00:00:33.480 align:start position:0%
function we're going to pass in the
 

00:00:33.480 --> 00:00:36.170 align:start position:0%
function we're going to pass in the
array<00:00:33.780><c> the</c><00:00:34.380><c> low</c><00:00:34.559><c> and</c><00:00:34.860><c> the</c><00:00:35.040><c> high</c><00:00:35.219><c> indexes</c><00:00:35.880><c> of</c>

00:00:36.170 --> 00:00:36.180 align:start position:0%
array the low and the high indexes of
 

00:00:36.180 --> 00:00:38.990 align:start position:0%
array the low and the high indexes of
that<00:00:36.360><c> array</c><00:00:37.200><c> initially</c><00:00:37.860><c> the</c><00:00:38.219><c> low</c><00:00:38.399><c> starts</c><00:00:38.880><c> at</c>

00:00:38.990 --> 00:00:39.000 align:start position:0%
that array initially the low starts at
 

00:00:39.000 --> 00:00:41.889 align:start position:0%
that array initially the low starts at
index<00:00:39.480><c> 0</c><00:00:39.719><c> and</c><00:00:40.260><c> the</c><00:00:40.440><c> high</c><00:00:40.620><c> starts</c><00:00:41.040><c> at</c><00:00:41.160><c> the</c><00:00:41.399><c> blast</c>

00:00:41.889 --> 00:00:41.899 align:start position:0%
index 0 and the high starts at the blast
 

00:00:41.899 --> 00:00:44.569 align:start position:0%
index 0 and the high starts at the blast
index<00:00:42.899><c> of</c><00:00:43.079><c> the</c><00:00:43.200><c> array</c><00:00:43.500><c> the</c><00:00:44.100><c> whole</c><00:00:44.219><c> reason</c><00:00:44.399><c> is</c>

00:00:44.569 --> 00:00:44.579 align:start position:0%
index of the array the whole reason is
 

00:00:44.579 --> 00:00:46.910 align:start position:0%
index of the array the whole reason is
because<00:00:44.760><c> we're</c><00:00:45.059><c> worried</c><00:00:45.360><c> about</c><00:00:45.480><c> the</c><00:00:46.140><c> pivot</c><00:00:46.500><c> of</c>

00:00:46.910 --> 00:00:46.920 align:start position:0%
because we're worried about the pivot of
 

00:00:46.920 --> 00:00:48.529 align:start position:0%
because we're worried about the pivot of
the<00:00:47.280><c> quicksort</c><00:00:47.700><c> that's</c><00:00:48.000><c> how</c><00:00:48.180><c> we</c><00:00:48.300><c> sort</c>

00:00:48.529 --> 00:00:48.539 align:start position:0%
the quicksort that's how we sort
 

00:00:48.539 --> 00:00:50.930 align:start position:0%
the quicksort that's how we sort
everything<00:00:48.780><c> based</c><00:00:49.440><c> around</c><00:00:49.620><c> the</c><00:00:49.980><c> pivot</c><00:00:50.340><c> let's</c>

00:00:50.930 --> 00:00:50.940 align:start position:0%
everything based around the pivot let's
 

00:00:50.940 --> 00:00:52.549 align:start position:0%
everything based around the pivot let's
go<00:00:51.120><c> ahead</c><00:00:51.239><c> and</c><00:00:51.300><c> create</c><00:00:51.539><c> the</c><00:00:52.020><c> recursive</c>

00:00:52.549 --> 00:00:52.559 align:start position:0%
go ahead and create the recursive
 

00:00:52.559 --> 00:00:56.569 align:start position:0%
go ahead and create the recursive
functions<00:00:52.980><c> of</c><00:00:53.160><c> public</c><00:00:53.399><c> void</c><00:00:54.000><c> quick</c><00:00:54.719><c> sort</c><00:00:55.579><c> int</c>

00:00:56.569 --> 00:00:56.579 align:start position:0%
functions of public void quick sort int
 

00:00:56.579 --> 00:01:00.950 align:start position:0%
functions of public void quick sort int
array<00:00:57.480><c> and</c><00:00:58.079><c> hello</c><00:00:58.820><c> and</c><00:00:59.820><c> hi</c>

00:01:00.950 --> 00:01:00.960 align:start position:0%
array and hello and hi
 

00:01:00.960 --> 00:01:04.009 align:start position:0%
array and hello and hi
now<00:01:01.559><c> for</c><00:01:01.980><c> any</c><00:01:02.760><c> recursion</c><00:01:03.300><c> you</c><00:01:03.539><c> have</c><00:01:03.660><c> to</c><00:01:03.780><c> have</c><00:01:03.840><c> a</c>

00:01:04.009 --> 00:01:04.019 align:start position:0%
now for any recursion you have to have a
 

00:01:04.019 --> 00:01:05.929 align:start position:0%
now for any recursion you have to have a
base<00:01:04.140><c> case</c><00:01:04.379><c> or</c><00:01:04.860><c> else</c><00:01:04.979><c> your</c><00:01:05.280><c> recursion</c><00:01:05.700><c> will</c>

00:01:05.929 --> 00:01:05.939 align:start position:0%
base case or else your recursion will
 

00:01:05.939 --> 00:01:07.670 align:start position:0%
base case or else your recursion will
just<00:01:06.180><c> always</c><00:01:06.720><c> run</c>

00:01:07.670 --> 00:01:07.680 align:start position:0%
just always run
 

00:01:07.680 --> 00:01:09.830 align:start position:0%
just always run
infinitely<00:01:08.340><c> and</c><00:01:08.820><c> that</c><00:01:09.000><c> is</c><00:01:09.180><c> bad</c><00:01:09.360><c> you</c><00:01:09.720><c> don't</c>

00:01:09.830 --> 00:01:09.840 align:start position:0%
infinitely and that is bad you don't
 

00:01:09.840 --> 00:01:12.289 align:start position:0%
infinitely and that is bad you don't
want<00:01:09.960><c> that</c><00:01:10.619><c> our</c><00:01:11.220><c> case</c><00:01:11.520><c> is</c><00:01:11.820><c> going</c><00:01:12.000><c> to</c><00:01:12.180><c> be</c>

00:01:12.289 --> 00:01:12.299 align:start position:0%
want that our case is going to be
 

00:01:12.299 --> 00:01:15.109 align:start position:0%
want that our case is going to be
whenever<00:01:13.020><c> the</c><00:01:13.380><c> low</c><00:01:13.619><c> and</c><00:01:14.100><c> the</c><00:01:14.280><c> high</c><00:01:14.460><c> indexes</c>

00:01:15.109 --> 00:01:15.119 align:start position:0%
whenever the low and the high indexes
 

00:01:15.119 --> 00:01:16.789 align:start position:0%
whenever the low and the high indexes
cross<00:01:15.420><c> each</c><00:01:15.659><c> other</c>

00:01:16.789 --> 00:01:16.799 align:start position:0%
cross each other
 

00:01:16.799 --> 00:01:18.109 align:start position:0%
cross each other
so<00:01:17.280><c> if</c>

00:01:18.109 --> 00:01:18.119 align:start position:0%
so if
 

00:01:18.119 --> 00:01:21.410 align:start position:0%
so if
the<00:01:18.659><c> low</c><00:01:18.840><c> is</c><00:01:19.140><c> less</c><00:01:19.380><c> than</c><00:01:19.680><c> the</c><00:01:19.920><c> high</c><00:01:20.420><c> that's</c>

00:01:21.410 --> 00:01:21.420 align:start position:0%
the low is less than the high that's
 

00:01:21.420 --> 00:01:23.270 align:start position:0%
the low is less than the high that's
fine<00:01:21.659><c> that</c><00:01:21.960><c> means</c><00:01:22.200><c> that</c><00:01:22.259><c> we</c><00:01:22.439><c> can</c><00:01:22.619><c> continue</c><00:01:22.860><c> on</c>

00:01:23.270 --> 00:01:23.280 align:start position:0%
fine that means that we can continue on
 

00:01:23.280 --> 00:01:24.830 align:start position:0%
fine that means that we can continue on
with<00:01:23.460><c> our</c><00:01:23.580><c> recursion</c><00:01:24.000><c> but</c><00:01:24.240><c> if</c><00:01:24.420><c> that</c><00:01:24.659><c> doesn't</c>

00:01:24.830 --> 00:01:24.840 align:start position:0%
with our recursion but if that doesn't
 

00:01:24.840 --> 00:01:25.910 align:start position:0%
with our recursion but if that doesn't
happen

00:01:25.910 --> 00:01:25.920 align:start position:0%
happen
 

00:01:25.920 --> 00:01:28.670 align:start position:0%
happen
then<00:01:26.460><c> we</c><00:01:26.640><c> are</c><00:01:26.820><c> done</c><00:01:27.119><c> with</c><00:01:27.720><c> that</c><00:01:27.840><c> recursion</c><00:01:28.380><c> on</c>

00:01:28.670 --> 00:01:28.680 align:start position:0%
then we are done with that recursion on
 

00:01:28.680 --> 00:01:30.770 align:start position:0%
then we are done with that recursion on
the<00:01:28.799><c> activation</c><00:01:29.100><c> stack</c><00:01:29.400><c> and</c><00:01:29.640><c> we</c><00:01:29.880><c> can</c><00:01:30.060><c> move</c><00:01:30.479><c> on</c>

00:01:30.770 --> 00:01:30.780 align:start position:0%
the activation stack and we can move on
 

00:01:30.780 --> 00:01:32.870 align:start position:0%
the activation stack and we can move on
until<00:01:31.259><c> we</c><00:01:31.500><c> are</c><00:01:31.619><c> finished</c><00:01:31.799><c> with</c><00:01:32.040><c> all</c><00:01:32.520><c> of</c><00:01:32.759><c> the</c>

00:01:32.870 --> 00:01:32.880 align:start position:0%
until we are finished with all of the
 

00:01:32.880 --> 00:01:34.490 align:start position:0%
until we are finished with all of the
recursive<00:01:33.240><c> calls</c><00:01:33.600><c> on</c><00:01:33.720><c> the</c><00:01:33.840><c> activation</c><00:01:34.200><c> stack</c>

00:01:34.490 --> 00:01:34.500 align:start position:0%
recursive calls on the activation stack
 

00:01:34.500 --> 00:01:35.929 align:start position:0%
recursive calls on the activation stack
the<00:01:34.920><c> next</c><00:01:35.040><c> thing</c><00:01:35.220><c> I</c><00:01:35.400><c> mentioned</c><00:01:35.640><c> that</c><00:01:35.759><c> is</c>

00:01:35.929 --> 00:01:35.939 align:start position:0%
the next thing I mentioned that is
 

00:01:35.939 --> 00:01:38.710 align:start position:0%
the next thing I mentioned that is
important<00:01:36.180><c> is</c><00:01:36.360><c> the</c><00:01:36.540><c> pivot</c><00:01:36.960><c> so</c><00:01:37.320><c> pivot</c>

00:01:38.710 --> 00:01:38.720 align:start position:0%
important is the pivot so pivot
 

00:01:38.720 --> 00:01:41.030 align:start position:0%
important is the pivot so pivot
index<00:01:39.720><c> is</c><00:01:39.960><c> going</c><00:01:40.079><c> to</c><00:01:40.140><c> be</c><00:01:40.259><c> equal</c><00:01:40.380><c> to</c><00:01:40.619><c> another</c>

00:01:41.030 --> 00:01:41.040 align:start position:0%
index is going to be equal to another
 

00:01:41.040 --> 00:01:45.170 align:start position:0%
index is going to be equal to another
method<00:01:41.640><c> called</c><00:01:42.299><c> Partition</c><00:01:43.340><c> array</c><00:01:44.340><c> we</c><00:01:44.759><c> pass</c><00:01:44.939><c> in</c>

00:01:45.170 --> 00:01:45.180 align:start position:0%
method called Partition array we pass in
 

00:01:45.180 --> 00:01:47.569 align:start position:0%
method called Partition array we pass in
the<00:01:45.360><c> low</c><00:01:45.540><c> and</c><00:01:45.900><c> the</c><00:01:46.140><c> high</c><00:01:46.320><c> as</c><00:01:46.740><c> well</c><00:01:46.920><c> the</c>

00:01:47.569 --> 00:01:47.579 align:start position:0%
the low and the high as well the
 

00:01:47.579 --> 00:01:49.670 align:start position:0%
the low and the high as well the
partition<00:01:47.880><c> method</c><00:01:48.360><c> is</c><00:01:48.900><c> what</c><00:01:49.079><c> takes</c><00:01:49.380><c> all</c><00:01:49.560><c> the</c>

00:01:49.670 --> 00:01:49.680 align:start position:0%
partition method is what takes all the
 

00:01:49.680 --> 00:01:51.410 align:start position:0%
partition method is what takes all the
elements<00:01:50.040><c> that</c><00:01:50.280><c> are</c><00:01:50.460><c> less</c><00:01:50.759><c> than</c><00:01:50.939><c> the</c><00:01:51.060><c> pivot</c>

00:01:51.410 --> 00:01:51.420 align:start position:0%
elements that are less than the pivot
 

00:01:51.420 --> 00:01:53.870 align:start position:0%
elements that are less than the pivot
move<00:01:51.960><c> it</c><00:01:52.140><c> to</c><00:01:52.259><c> the</c><00:01:52.439><c> left</c><00:01:52.560><c> of</c><00:01:52.799><c> the</c><00:01:53.340><c> pivot</c><00:01:53.640><c> in</c><00:01:53.759><c> the</c>

00:01:53.870 --> 00:01:53.880 align:start position:0%
move it to the left of the pivot in the
 

00:01:53.880 --> 00:01:55.730 align:start position:0%
move it to the left of the pivot in the
array<00:01:54.180><c> and</c><00:01:54.720><c> all</c><00:01:54.960><c> the</c><00:01:55.079><c> elements</c><00:01:55.439><c> that</c><00:01:55.560><c> are</c>

00:01:55.730 --> 00:01:55.740 align:start position:0%
array and all the elements that are
 

00:01:55.740 --> 00:01:57.410 align:start position:0%
array and all the elements that are
greater<00:01:55.979><c> than</c><00:01:55.979><c> pivot</c><00:01:56.399><c> move</c><00:01:56.640><c> it</c><00:01:56.820><c> to</c><00:01:57.000><c> the</c><00:01:57.180><c> right</c>

00:01:57.410 --> 00:01:57.420 align:start position:0%
greater than pivot move it to the right
 

00:01:57.420 --> 00:01:58.969 align:start position:0%
greater than pivot move it to the right
of<00:01:57.780><c> the</c><00:01:57.899><c> pivot</c><00:01:58.200><c> and</c><00:01:58.320><c> array</c><00:01:58.680><c> and</c><00:01:58.799><c> then</c><00:01:58.920><c> the</c>

00:01:58.969 --> 00:01:58.979 align:start position:0%
of the pivot and array and then the
 

00:01:58.979 --> 00:02:00.710 align:start position:0%
of the pivot and array and then the
pivot<00:01:59.220><c> is</c><00:01:59.340><c> the</c><00:01:59.520><c> middle</c><00:01:59.579><c> so</c><00:02:00.000><c> at</c><00:02:00.299><c> that</c><00:02:00.540><c> point</c>

00:02:00.710 --> 00:02:00.720 align:start position:0%
pivot is the middle so at that point
 

00:02:00.720 --> 00:02:03.170 align:start position:0%
pivot is the middle so at that point
they're<00:02:01.259><c> sorted</c><00:02:01.680><c> from</c><00:02:01.979><c> less</c><00:02:02.579><c> than</c><00:02:02.759><c> the</c><00:02:02.880><c> pivot</c>

00:02:03.170 --> 00:02:03.180 align:start position:0%
they're sorted from less than the pivot
 

00:02:03.180 --> 00:02:04.850 align:start position:0%
they're sorted from less than the pivot
the<00:02:03.479><c> pivot</c><00:02:03.780><c> and</c><00:02:04.079><c> then</c><00:02:04.200><c> greater</c><00:02:04.619><c> than</c><00:02:04.680><c> the</c>

00:02:04.850 --> 00:02:04.860 align:start position:0%
the pivot and then greater than the
 

00:02:04.860 --> 00:02:07.490 align:start position:0%
the pivot and then greater than the
pivot<00:02:05.159><c> next</c><00:02:05.579><c> we</c><00:02:05.880><c> then</c><00:02:06.060><c> call</c><00:02:06.360><c> quick</c><00:02:06.780><c> sort</c><00:02:07.200><c> again</c>

00:02:07.490 --> 00:02:07.500 align:start position:0%
pivot next we then call quick sort again
 

00:02:07.500 --> 00:02:11.330 align:start position:0%
pivot next we then call quick sort again
it's<00:02:08.280><c> a</c><00:02:08.459><c> quick</c><00:02:08.580><c> sort</c><00:02:08.880><c> array</c><00:02:09.420><c> give</c><00:02:10.200><c> it</c><00:02:10.380><c> low</c><00:02:10.679><c> and</c>

00:02:11.330 --> 00:02:11.340 align:start position:0%
it's a quick sort array give it low and
 

00:02:11.340 --> 00:02:15.890 align:start position:0%
it's a quick sort array give it low and
then<00:02:11.599><c> pivot</c><00:02:12.599><c> index</c><00:02:13.440><c> minus</c><00:02:14.220><c> one</c><00:02:14.580><c> we</c><00:02:15.540><c> also</c><00:02:15.720><c> have</c>

00:02:15.890 --> 00:02:15.900 align:start position:0%
then pivot index minus one we also have
 

00:02:15.900 --> 00:02:19.250 align:start position:0%
then pivot index minus one we also have
quick<00:02:16.200><c> sort</c><00:02:16.560><c> array</c>

00:02:19.250 --> 00:02:19.260 align:start position:0%
quick sort array
 

00:02:19.260 --> 00:02:22.850 align:start position:0%
quick sort array
pivot<00:02:20.099><c> index</c><00:02:20.580><c> plus</c><00:02:21.120><c> one</c><00:02:21.480><c> and</c><00:02:22.080><c> high</c><00:02:22.379><c> remember</c>

00:02:22.850 --> 00:02:22.860 align:start position:0%
pivot index plus one and high remember
 

00:02:22.860 --> 00:02:24.949 align:start position:0%
pivot index plus one and high remember
that<00:02:23.220><c> the</c><00:02:23.520><c> basic</c><00:02:23.819><c> difference</c><00:02:24.060><c> between</c><00:02:24.480><c> merge</c>

00:02:24.949 --> 00:02:24.959 align:start position:0%
that the basic difference between merge
 

00:02:24.959 --> 00:02:27.350 align:start position:0%
that the basic difference between merge
sort<00:02:25.200><c> and</c><00:02:25.440><c> quick</c><00:02:25.620><c> sort</c><00:02:25.980><c> is</c><00:02:26.520><c> that</c><00:02:26.760><c> quick</c><00:02:26.940><c> sort</c>

00:02:27.350 --> 00:02:27.360 align:start position:0%
sort and quick sort is that quick sort
 

00:02:27.360 --> 00:02:31.550 align:start position:0%
sort and quick sort is that quick sort
will<00:02:28.080><c> sort</c><00:02:28.739><c> the</c><00:02:29.340><c> elements</c><00:02:29.819><c> before</c><00:02:30.599><c> it</c><00:02:31.260><c> has</c><00:02:31.379><c> the</c>

00:02:31.550 --> 00:02:31.560 align:start position:0%
will sort the elements before it has the
 

00:02:31.560 --> 00:02:33.710 align:start position:0%
will sort the elements before it has the
recursive<00:02:32.040><c> call</c><00:02:32.280><c> whereas</c><00:02:32.879><c> merge</c><00:02:33.180><c> sort</c><00:02:33.420><c> will</c>

00:02:33.710 --> 00:02:33.720 align:start position:0%
recursive call whereas merge sort will
 

00:02:33.720 --> 00:02:35.990 align:start position:0%
recursive call whereas merge sort will
do<00:02:33.959><c> all</c><00:02:34.140><c> the</c><00:02:34.379><c> recursive</c><00:02:34.860><c> calling</c><00:02:35.340><c> and</c><00:02:35.819><c> then</c>

00:02:35.990 --> 00:02:36.000 align:start position:0%
do all the recursive calling and then
 

00:02:36.000 --> 00:02:39.229 align:start position:0%
do all the recursive calling and then
once<00:02:36.480><c> it's</c><00:02:36.599><c> done</c><00:02:37.020><c> as</c><00:02:37.500><c> it</c><00:02:37.680><c> goes</c><00:02:38.040><c> back</c><00:02:38.280><c> up</c><00:02:38.760><c> and</c>

00:02:39.229 --> 00:02:39.239 align:start position:0%
once it's done as it goes back up and
 

00:02:39.239 --> 00:02:41.930 align:start position:0%
once it's done as it goes back up and
merging<00:02:39.599><c> the</c><00:02:39.959><c> array</c><00:02:40.260><c> together</c><00:02:40.500><c> then</c><00:02:41.280><c> it</c><00:02:41.580><c> sorts</c>

00:02:41.930 --> 00:02:41.940 align:start position:0%
merging the array together then it sorts
 

00:02:41.940 --> 00:02:43.850 align:start position:0%
merging the array together then it sorts
the<00:02:42.060><c> elements</c><00:02:42.480><c> let's</c><00:02:43.200><c> move</c><00:02:43.379><c> on</c><00:02:43.500><c> to</c><00:02:43.680><c> the</c>

00:02:43.850 --> 00:02:43.860 align:start position:0%
the elements let's move on to the
 

00:02:43.860 --> 00:02:45.110 align:start position:0%
the elements let's move on to the
partition<00:02:44.160><c> logic</c><00:02:44.519><c> it'll</c><00:02:44.760><c> make</c><00:02:44.879><c> a</c><00:02:44.940><c> little</c><00:02:45.000><c> more</c>

00:02:45.110 --> 00:02:45.120 align:start position:0%
partition logic it'll make a little more
 

00:02:45.120 --> 00:02:48.490 align:start position:0%
partition logic it'll make a little more
sense<00:02:45.300><c> we</c><00:02:45.720><c> have</c><00:02:45.900><c> private</c><00:02:46.440><c> and</c><00:02:46.980><c> partition</c>

00:02:48.490 --> 00:02:48.500 align:start position:0%
sense we have private and partition
 

00:02:48.500 --> 00:02:53.690 align:start position:0%
sense we have private and partition
int<00:02:49.500><c> array</c><00:02:50.340><c> into</c><00:02:50.940><c> low</c><00:02:51.360><c> and</c><00:02:51.780><c> int</c><00:02:52.379><c> High</c>

00:02:53.690 --> 00:02:53.700 align:start position:0%
int array into low and int High
 

00:02:53.700 --> 00:02:55.670 align:start position:0%
int array into low and int High
now<00:02:54.180><c> as</c><00:02:54.360><c> I</c><00:02:54.480><c> mentioned</c><00:02:54.780><c> in</c><00:02:55.080><c> the</c><00:02:55.140><c> video</c><00:02:55.260><c> where</c><00:02:55.500><c> I</c>

00:02:55.670 --> 00:02:55.680 align:start position:0%
now as I mentioned in the video where I
 

00:02:55.680 --> 00:02:58.309 align:start position:0%
now as I mentioned in the video where I
explained<00:02:55.980><c> quick</c><00:02:56.220><c> sort</c><00:02:56.580><c> the</c><00:02:57.480><c> pivot</c><00:02:57.900><c> is</c><00:02:58.080><c> going</c>

00:02:58.309 --> 00:02:58.319 align:start position:0%
explained quick sort the pivot is going
 

00:02:58.319 --> 00:03:01.910 align:start position:0%
explained quick sort the pivot is going
to<00:02:58.440><c> be</c><00:02:58.860><c> the</c><00:02:59.220><c> vast</c><00:02:59.519><c> element</c><00:02:59.760><c> of</c><00:03:00.500><c> that</c><00:03:01.500><c> portion</c>

00:03:01.910 --> 00:03:01.920 align:start position:0%
to be the vast element of that portion
 

00:03:01.920 --> 00:03:03.470 align:start position:0%
to be the vast element of that portion
of<00:03:02.160><c> the</c><00:03:02.280><c> array</c><00:03:02.640><c> so</c><00:03:02.819><c> if</c><00:03:03.000><c> we</c><00:03:03.120><c> have</c><00:03:03.239><c> the</c><00:03:03.360><c> whole</c>

00:03:03.470 --> 00:03:03.480 align:start position:0%
of the array so if we have the whole
 

00:03:03.480 --> 00:03:05.330 align:start position:0%
of the array so if we have the whole
array<00:03:03.780><c> it's</c><00:03:03.959><c> going</c><00:03:04.080><c> to</c><00:03:04.140><c> be</c><00:03:04.200><c> the</c><00:03:04.319><c> last</c><00:03:04.440><c> index</c><00:03:04.800><c> if</c>

00:03:05.330 --> 00:03:05.340 align:start position:0%
array it's going to be the last index if
 

00:03:05.340 --> 00:03:07.309 align:start position:0%
array it's going to be the last index if
we<00:03:05.459><c> have</c><00:03:05.580><c> like</c><00:03:05.879><c> three</c><00:03:06.300><c> elements</c><00:03:06.720><c> of</c><00:03:06.900><c> the</c><00:03:07.019><c> array</c>

00:03:07.309 --> 00:03:07.319 align:start position:0%
we have like three elements of the array
 

00:03:07.319 --> 00:03:08.930 align:start position:0%
we have like three elements of the array
that<00:03:07.620><c> we're</c><00:03:07.739><c> partitioning</c><00:03:08.340><c> then</c><00:03:08.640><c> it's</c><00:03:08.819><c> going</c>

00:03:08.930 --> 00:03:08.940 align:start position:0%
that we're partitioning then it's going
 

00:03:08.940 --> 00:03:10.790 align:start position:0%
that we're partitioning then it's going
to<00:03:09.060><c> be</c><00:03:09.120><c> that</c><00:03:09.360><c> third</c><00:03:09.599><c> element</c><00:03:09.900><c> all</c><00:03:10.440><c> right</c><00:03:10.620><c> so</c>

00:03:10.790 --> 00:03:10.800 align:start position:0%
to be that third element all right so
 

00:03:10.800 --> 00:03:13.190 align:start position:0%
to be that third element all right so
we're<00:03:10.920><c> going</c><00:03:11.040><c> to</c><00:03:11.099><c> say</c><00:03:11.220><c> int</c><00:03:11.940><c> pivot</c><00:03:12.300><c> is</c><00:03:12.840><c> equal</c><00:03:13.019><c> to</c>

00:03:13.190 --> 00:03:13.200 align:start position:0%
we're going to say int pivot is equal to
 

00:03:13.200 --> 00:03:19.250 align:start position:0%
we're going to say int pivot is equal to
array<00:03:13.860><c> of</c><00:03:14.400><c> high</c><00:03:14.840><c> and</c><00:03:15.840><c> I</c><00:03:16.379><c> equals</c><00:03:16.920><c> low</c><00:03:17.220><c> minus</c><00:03:17.819><c> one</c>

00:03:19.250 --> 00:03:19.260 align:start position:0%
array of high and I equals low minus one
 

00:03:19.260 --> 00:03:21.649 align:start position:0%
array of high and I equals low minus one
this<00:03:19.800><c> is</c><00:03:19.920><c> just</c><00:03:20.099><c> used</c><00:03:20.340><c> for</c><00:03:20.819><c> some</c><00:03:21.060><c> of</c><00:03:21.180><c> the</c><00:03:21.300><c> logic</c>

00:03:21.649 --> 00:03:21.659 align:start position:0%
this is just used for some of the logic
 

00:03:21.659 --> 00:03:23.210 align:start position:0%
this is just used for some of the logic
of<00:03:21.840><c> swapping</c><00:03:22.319><c> which</c><00:03:22.560><c> we'll</c><00:03:22.680><c> see</c><00:03:22.860><c> here</c><00:03:22.980><c> in</c><00:03:23.099><c> a</c>

00:03:23.210 --> 00:03:23.220 align:start position:0%
of swapping which we'll see here in a
 

00:03:23.220 --> 00:03:25.070 align:start position:0%
of swapping which we'll see here in a
minute<00:03:23.340><c> now</c><00:03:23.940><c> we</c><00:03:24.120><c> need</c><00:03:24.239><c> a</c><00:03:24.360><c> for</c><00:03:24.540><c> Loop</c><00:03:24.780><c> that</c><00:03:24.900><c> goes</c>

00:03:25.070 --> 00:03:25.080 align:start position:0%
minute now we need a for Loop that goes
 

00:03:25.080 --> 00:03:27.350 align:start position:0%
minute now we need a for Loop that goes
over<00:03:25.200><c> all</c><00:03:25.500><c> the</c><00:03:25.620><c> elements</c><00:03:26.159><c> from</c><00:03:26.519><c> low</c><00:03:26.879><c> to</c><00:03:27.120><c> high</c>

00:03:27.350 --> 00:03:27.360 align:start position:0%
over all the elements from low to high
 

00:03:27.360 --> 00:03:30.050 align:start position:0%
over all the elements from low to high
in<00:03:27.659><c> this</c><00:03:27.840><c> array</c><00:03:28.200><c> and</c><00:03:28.860><c> move</c><00:03:29.040><c> them</c><00:03:29.280><c> either</c><00:03:29.640><c> to</c>

00:03:30.050 --> 00:03:30.060 align:start position:0%
in this array and move them either to
 

00:03:30.060 --> 00:03:33.670 align:start position:0%
in this array and move them either to
the<00:03:30.540><c> left</c><00:03:30.780><c> or</c><00:03:31.080><c> to</c><00:03:31.319><c> the</c><00:03:31.500><c> right</c><00:03:31.620><c> of</c><00:03:31.860><c> the</c><00:03:31.980><c> pivot</c><00:03:32.700><c> or</c>

00:03:33.670 --> 00:03:33.680 align:start position:0%
the left or to the right of the pivot or
 

00:03:33.680 --> 00:03:39.589 align:start position:0%
the left or to the right of the pivot or
int<00:03:34.680><c> J</c><00:03:35.400><c> equals</c><00:03:36.060><c> low</c><00:03:36.500><c> J</c><00:03:37.500><c> is</c><00:03:37.739><c> less</c><00:03:37.920><c> than</c><00:03:38.159><c> High</c>

00:03:39.589 --> 00:03:39.599 align:start position:0%
int J equals low J is less than High
 

00:03:39.599 --> 00:03:43.009 align:start position:0%
int J equals low J is less than High
J<00:03:40.200><c> plus</c><00:03:40.620><c> plus</c><00:03:40.920><c> so</c><00:03:41.700><c> initially</c><00:03:42.120><c> J</c><00:03:42.540><c> is</c><00:03:42.720><c> equal</c><00:03:42.900><c> to</c>

00:03:43.009 --> 00:03:43.019 align:start position:0%
J plus plus so initially J is equal to
 

00:03:43.019 --> 00:03:45.410 align:start position:0%
J plus plus so initially J is equal to
low<00:03:43.260><c> which</c><00:03:43.680><c> is</c><00:03:43.799><c> zero</c><00:03:44.340><c> and</c><00:03:44.879><c> then</c><00:03:44.940><c> the</c><00:03:45.120><c> high</c><00:03:45.239><c> is</c>

00:03:45.410 --> 00:03:45.420 align:start position:0%
low which is zero and then the high is
 

00:03:45.420 --> 00:03:47.449 align:start position:0%
low which is zero and then the high is
four<00:03:45.599><c> for</c><00:03:46.080><c> the</c><00:03:46.319><c> first</c><00:03:46.500><c> pass</c>

00:03:47.449 --> 00:03:47.459 align:start position:0%
four for the first pass
 

00:03:47.459 --> 00:03:53.809 align:start position:0%
four for the first pass
if<00:03:48.060><c> array</c><00:03:49.319><c> of</c><00:03:49.620><c> J</c><00:03:50.239><c> is</c><00:03:51.239><c> less</c><00:03:51.599><c> than</c><00:03:51.840><c> pivot</c>

00:03:53.809 --> 00:03:53.819 align:start position:0%
if array of J is less than pivot
 

00:03:53.819 --> 00:03:56.869 align:start position:0%
if array of J is less than pivot
here<00:03:54.120><c> in</c><00:03:54.239><c> this</c><00:03:54.360><c> array</c><00:03:54.659><c> J</c><00:03:54.959><c> starts</c><00:03:55.440><c> out</c><00:03:55.560><c> at</c><00:03:55.739><c> 0</c><00:03:55.980><c> and</c>

00:03:56.869 --> 00:03:56.879 align:start position:0%
here in this array J starts out at 0 and
 

00:03:56.879 --> 00:03:59.809 align:start position:0%
here in this array J starts out at 0 and
the<00:03:57.060><c> pivot</c><00:03:57.360><c> is</c><00:03:57.599><c> seven</c><00:03:57.900><c> well</c><00:03:58.620><c> two</c>

00:03:59.809 --> 00:03:59.819 align:start position:0%
the pivot is seven well two
 

00:03:59.819 --> 00:04:02.570 align:start position:0%
the pivot is seven well two
is<00:04:00.420><c> less</c><00:04:00.780><c> than</c><00:04:00.959><c> seven</c><00:04:01.319><c> what</c><00:04:01.920><c> we</c><00:04:02.099><c> end</c><00:04:02.220><c> up</c><00:04:02.340><c> doing</c>

00:04:02.570 --> 00:04:02.580 align:start position:0%
is less than seven what we end up doing
 

00:04:02.580 --> 00:04:05.449 align:start position:0%
is less than seven what we end up doing
is<00:04:03.000><c> we're</c><00:04:03.299><c> going</c><00:04:03.540><c> to</c><00:04:03.659><c> increment</c><00:04:04.019><c> I</c><00:04:04.620><c> and</c><00:04:05.340><c> then</c>

00:04:05.449 --> 00:04:05.459 align:start position:0%
is we're going to increment I and then
 

00:04:05.459 --> 00:04:08.809 align:start position:0%
is we're going to increment I and then
we<00:04:05.760><c> swap</c><00:04:06.239><c> array</c><00:04:07.500><c> I</c>

00:04:08.809 --> 00:04:08.819 align:start position:0%
we swap array I
 

00:04:08.819 --> 00:04:10.490 align:start position:0%
we swap array I
and<00:04:09.360><c> J</c>

00:04:10.490 --> 00:04:10.500 align:start position:0%
and J
 

00:04:10.500 --> 00:04:12.470 align:start position:0%
and J
now<00:04:10.739><c> we</c><00:04:10.980><c> increment</c><00:04:11.340><c> I</c><00:04:11.580><c> because</c><00:04:11.879><c> initially</c><00:04:12.299><c> it</c>

00:04:12.470 --> 00:04:12.480 align:start position:0%
now we increment I because initially it
 

00:04:12.480 --> 00:04:14.750 align:start position:0%
now we increment I because initially it
starts<00:04:12.780><c> out</c><00:04:12.840><c> at</c><00:04:13.080><c> negative</c><00:04:13.680><c> one</c><00:04:13.980><c> here</c><00:04:14.280><c> where</c>

00:04:14.750 --> 00:04:14.760 align:start position:0%
starts out at negative one here where
 

00:04:14.760 --> 00:04:19.670 align:start position:0%
starts out at negative one here where
low<00:04:15.120><c> is</c><00:04:15.299><c> zero</c><00:04:15.720><c> so</c><00:04:16.079><c> now</c><00:04:16.320><c> I</c><00:04:16.620><c> is</c><00:04:16.799><c> 0</c><00:04:17.060><c> J</c><00:04:18.060><c> is</c><00:04:18.359><c> zero</c><00:04:18.900><c> and</c>

00:04:19.670 --> 00:04:19.680 align:start position:0%
low is zero so now I is 0 J is zero and
 

00:04:19.680 --> 00:04:23.629 align:start position:0%
low is zero so now I is 0 J is zero and
we're<00:04:19.859><c> just</c><00:04:20.100><c> swapping</c><00:04:20.900><c> two</c><00:04:21.900><c> in</c><00:04:22.800><c> place</c><00:04:23.040><c> we're</c>

00:04:23.629 --> 00:04:23.639 align:start position:0%
we're just swapping two in place we're
 

00:04:23.639 --> 00:04:24.770 align:start position:0%
we're just swapping two in place we're
not<00:04:23.759><c> it's</c><00:04:23.880><c> not</c><00:04:24.060><c> actually</c><00:04:24.120><c> going</c><00:04:24.479><c> anywhere</c>

00:04:24.770 --> 00:04:24.780 align:start position:0%
not it's not actually going anywhere
 

00:04:24.780 --> 00:04:26.810 align:start position:0%
not it's not actually going anywhere
because<00:04:25.020><c> we</c><00:04:25.139><c> want</c><00:04:25.380><c> it</c><00:04:25.560><c> to</c><00:04:25.740><c> stay</c><00:04:26.280><c> where</c><00:04:26.520><c> it's</c><00:04:26.699><c> at</c>

00:04:26.810 --> 00:04:26.820 align:start position:0%
because we want it to stay where it's at
 

00:04:26.820 --> 00:04:28.969 align:start position:0%
because we want it to stay where it's at
this<00:04:27.180><c> is</c><00:04:27.240><c> just</c><00:04:27.419><c> a</c><00:04:27.540><c> little</c><00:04:27.660><c> bit</c><00:04:27.840><c> of</c><00:04:28.259><c> like</c><00:04:28.440><c> logic</c>

00:04:28.969 --> 00:04:28.979 align:start position:0%
this is just a little bit of like logic
 

00:04:28.979 --> 00:04:31.249 align:start position:0%
this is just a little bit of like logic
manipulation<00:04:29.520><c> to</c><00:04:30.060><c> keep</c><00:04:30.360><c> it</c><00:04:30.479><c> where</c><00:04:30.720><c> it's</c><00:04:30.900><c> at</c>

00:04:31.249 --> 00:04:31.259 align:start position:0%
manipulation to keep it where it's at
 

00:04:31.259 --> 00:04:33.890 align:start position:0%
manipulation to keep it where it's at
then<00:04:31.919><c> the</c><00:04:32.100><c> second</c><00:04:32.220><c> time</c><00:04:32.520><c> through</c><00:04:32.940><c> where</c><00:04:33.720><c> we</c>

00:04:33.890 --> 00:04:33.900 align:start position:0%
then the second time through where we
 

00:04:33.900 --> 00:04:38.689 align:start position:0%
then the second time through where we
increment<00:04:34.259><c> J</c><00:04:34.560><c> J</c><00:04:35.280><c> is</c><00:04:35.580><c> one</c><00:04:35.940><c> now</c><00:04:36.900><c> we</c><00:04:37.080><c> say</c><00:04:37.259><c> array</c><00:04:37.979><c> at</c>

00:04:38.689 --> 00:04:38.699 align:start position:0%
increment J J is one now we say array at
 

00:04:38.699 --> 00:04:42.050 align:start position:0%
increment J J is one now we say array at
one<00:04:39.060><c> which</c><00:04:39.540><c> is</c><00:04:39.660><c> nine</c><00:04:40.020><c> is</c><00:04:40.680><c> that</c><00:04:41.040><c> less</c>

00:04:42.050 --> 00:04:42.060 align:start position:0%
one which is nine is that less
 

00:04:42.060 --> 00:04:43.850 align:start position:0%
one which is nine is that less
then<00:04:42.600><c> the</c><00:04:42.780><c> pivot</c>

00:04:43.850 --> 00:04:43.860 align:start position:0%
then the pivot
 

00:04:43.860 --> 00:04:47.510 align:start position:0%
then the pivot
it<00:04:44.460><c> is</c><00:04:44.699><c> not</c><00:04:44.940><c> so</c><00:04:45.660><c> we</c><00:04:46.080><c> don't</c><00:04:46.440><c> do</c><00:04:46.800><c> anything</c><00:04:47.040><c> okay</c>

00:04:47.510 --> 00:04:47.520 align:start position:0%
it is not so we don't do anything okay
 

00:04:47.520 --> 00:04:51.830 align:start position:0%
it is not so we don't do anything okay
but<00:04:48.060><c> J</c><00:04:48.479><c> increases</c><00:04:49.199><c> so</c><00:04:49.860><c> I</c><00:04:50.160><c> is</c><00:04:50.400><c> still</c><00:04:50.639><c> zero</c><00:04:51.240><c> and</c>

00:04:51.830 --> 00:04:51.840 align:start position:0%
but J increases so I is still zero and
 

00:04:51.840 --> 00:04:55.430 align:start position:0%
but J increases so I is still zero and
now<00:04:51.960><c> J</c><00:04:52.199><c> is</c><00:04:52.440><c> going</c><00:04:52.560><c> to</c><00:04:52.680><c> be</c><00:04:52.800><c> two</c><00:04:53.220><c> so</c><00:04:54.120><c> we</c><00:04:54.600><c> J</c><00:04:55.080><c> is</c><00:04:55.259><c> now</c>

00:04:55.430 --> 00:04:55.440 align:start position:0%
now J is going to be two so we J is now
 

00:04:55.440 --> 00:05:01.129 align:start position:0%
now J is going to be two so we J is now
2<00:04:55.740><c> is</c><00:04:56.520><c> 4</c><00:04:57.360><c> less</c><00:04:57.780><c> than</c><00:04:58.020><c> the</c><00:04:58.440><c> pivot</c><00:04:58.860><c> It</c><00:04:59.699><c> is</c><00:05:00.000><c> Well</c><00:05:00.780><c> J</c>

00:05:01.129 --> 00:05:01.139 align:start position:0%
2 is 4 less than the pivot It is Well J
 

00:05:01.139 --> 00:05:03.530 align:start position:0%
2 is 4 less than the pivot It is Well J
is<00:05:01.380><c> 2</c><00:05:01.560><c> we</c><00:05:01.919><c> increment</c><00:05:02.280><c> I</c><00:05:02.520><c> which</c><00:05:02.759><c> was</c><00:05:02.940><c> zero</c><00:05:03.300><c> so</c>

00:05:03.530 --> 00:05:03.540 align:start position:0%
is 2 we increment I which was zero so
 

00:05:03.540 --> 00:05:07.610 align:start position:0%
is 2 we increment I which was zero so
now<00:05:03.660><c> it's</c><00:05:03.900><c> one</c><00:05:04.259><c> and</c><00:05:04.620><c> we</c><00:05:04.860><c> swap</c><00:05:05.280><c> I</c><00:05:05.940><c> and</c><00:05:06.300><c> J</c><00:05:06.600><c> so</c><00:05:07.139><c> I</c><00:05:07.440><c> is</c>

00:05:07.610 --> 00:05:07.620 align:start position:0%
now it's one and we swap I and J so I is
 

00:05:07.620 --> 00:05:11.990 align:start position:0%
now it's one and we swap I and J so I is
1<00:05:07.860><c> J</c><00:05:08.639><c> is</c><00:05:08.880><c> 2</c><00:05:09.060><c> we</c><00:05:09.600><c> end</c><00:05:09.780><c> up</c><00:05:09.960><c> swapping</c><00:05:10.880><c> nine</c><00:05:11.880><c> and</c>

00:05:11.990 --> 00:05:12.000 align:start position:0%
1 J is 2 we end up swapping nine and
 

00:05:12.000 --> 00:05:12.950 align:start position:0%
1 J is 2 we end up swapping nine and
four

00:05:12.950 --> 00:05:12.960 align:start position:0%
four
 

00:05:12.960 --> 00:05:18.110 align:start position:0%
four
so<00:05:13.380><c> now</c><00:05:13.560><c> be</c><00:05:13.860><c> 2</c><00:05:14.520><c> 4</c><00:05:14.900><c> 9</c><00:05:15.900><c> 3</c><00:05:16.440><c> 7.</c>

00:05:18.110 --> 00:05:18.120 align:start position:0%
so now be 2 4 9 3 7.
 

00:05:18.120 --> 00:05:19.850 align:start position:0%
so now be 2 4 9 3 7.
and<00:05:18.600><c> the</c><00:05:18.720><c> last</c><00:05:18.780><c> thing</c><00:05:18.960><c> is</c><00:05:19.080><c> we</c><00:05:19.199><c> need</c><00:05:19.380><c> to</c><00:05:19.440><c> do</c><00:05:19.560><c> one</c>

00:05:19.850 --> 00:05:19.860 align:start position:0%
and the last thing is we need to do one
 

00:05:19.860 --> 00:05:24.110 align:start position:0%
and the last thing is we need to do one
more<00:05:20.040><c> swap</c><00:05:20.520><c> that</c><00:05:21.240><c> is</c><00:05:21.680><c> when</c><00:05:22.680><c> we</c><00:05:23.460><c> finally</c><00:05:23.759><c> get</c><00:05:23.940><c> to</c>

00:05:24.110 --> 00:05:24.120 align:start position:0%
more swap that is when we finally get to
 

00:05:24.120 --> 00:05:27.170 align:start position:0%
more swap that is when we finally get to
here<00:05:24.419><c> to</c><00:05:24.780><c> this</c><00:05:25.020><c> point</c><00:05:25.259><c> this</c><00:05:26.039><c> for</c><00:05:26.220><c> Loop</c>

00:05:27.170 --> 00:05:27.180 align:start position:0%
here to this point this for Loop
 

00:05:27.180 --> 00:05:29.029 align:start position:0%
here to this point this for Loop
will<00:05:27.479><c> take</c><00:05:27.660><c> us</c><00:05:27.780><c> to</c><00:05:28.020><c> this</c><00:05:28.199><c> point</c><00:05:28.440><c> eventually</c>

00:05:29.029 --> 00:05:29.039 align:start position:0%
will take us to this point eventually
 

00:05:29.039 --> 00:05:32.450 align:start position:0%
will take us to this point eventually
all<00:05:29.880><c> right</c><00:05:30.060><c> but</c><00:05:30.479><c> when</c><00:05:31.020><c> we're</c><00:05:31.199><c> done</c><00:05:31.560><c> we</c><00:05:32.340><c> still</c>

00:05:32.450 --> 00:05:32.460 align:start position:0%
all right but when we're done we still
 

00:05:32.460 --> 00:05:35.150 align:start position:0%
all right but when we're done we still
have<00:05:32.699><c> nine</c><00:05:33.060><c> here</c><00:05:33.360><c> the</c><00:05:33.720><c> pivot</c><00:05:34.020><c> is</c><00:05:34.199><c> seven</c><00:05:34.440><c> we</c>

00:05:35.150 --> 00:05:35.160 align:start position:0%
have nine here the pivot is seven we
 

00:05:35.160 --> 00:05:37.310 align:start position:0%
have nine here the pivot is seven we
need<00:05:35.280><c> to</c><00:05:35.400><c> take</c><00:05:35.580><c> nine</c><00:05:36.000><c> and</c><00:05:36.419><c> move</c><00:05:36.600><c> and</c><00:05:36.900><c> swap</c><00:05:37.139><c> it</c>

00:05:37.310 --> 00:05:37.320 align:start position:0%
need to take nine and move and swap it
 

00:05:37.320 --> 00:05:41.510 align:start position:0%
need to take nine and move and swap it
with<00:05:37.500><c> seven</c><00:05:37.680><c> say</c><00:05:38.520><c> swap</c><00:05:39.000><c> array</c><00:05:39.860><c> uh</c><00:05:40.860><c> plus</c><00:05:41.160><c> one</c>

00:05:41.510 --> 00:05:41.520 align:start position:0%
with seven say swap array uh plus one
 

00:05:41.520 --> 00:05:45.890 align:start position:0%
with seven say swap array uh plus one
and<00:05:42.120><c> the</c><00:05:42.419><c> high</c><00:05:42.680><c> and</c><00:05:43.680><c> then</c><00:05:43.860><c> we</c><00:05:44.340><c> return</c><00:05:44.600><c> I</c><00:05:45.600><c> plus</c>

00:05:45.890 --> 00:05:45.900 align:start position:0%
and the high and then we return I plus
 

00:05:45.900 --> 00:05:48.170 align:start position:0%
and the high and then we return I plus
one<00:05:46.259><c> the</c><00:05:47.039><c> last</c><00:05:47.220><c> thing</c><00:05:47.340><c> we</c><00:05:47.460><c> need</c><00:05:47.580><c> to</c><00:05:47.699><c> do</c><00:05:47.820><c> is</c>

00:05:48.170 --> 00:05:48.180 align:start position:0%
one the last thing we need to do is
 

00:05:48.180 --> 00:05:49.969 align:start position:0%
one the last thing we need to do is
implement<00:05:48.720><c> the</c><00:05:48.900><c> swap</c><00:05:49.199><c> method</c><00:05:49.560><c> and</c><00:05:49.740><c> this</c><00:05:49.919><c> is</c>

00:05:49.969 --> 00:05:49.979 align:start position:0%
implement the swap method and this is
 

00:05:49.979 --> 00:05:52.010 align:start position:0%
implement the swap method and this is
something<00:05:50.160><c> you</c><00:05:50.520><c> really</c><00:05:50.699><c> should</c><00:05:51.000><c> know</c><00:05:51.300><c> how</c><00:05:51.840><c> to</c>

00:05:52.010 --> 00:05:52.020 align:start position:0%
something you really should know how to
 

00:05:52.020 --> 00:05:55.670 align:start position:0%
something you really should know how to
do<00:05:52.199><c> uh</c><00:05:52.979><c> pretty</c><00:05:53.220><c> much</c><00:05:53.340><c> by</c><00:05:54.060><c> by</c><00:05:54.360><c> memory</c><00:05:54.720><c> so</c><00:05:55.440><c> it's</c>

00:05:55.670 --> 00:05:55.680 align:start position:0%
do uh pretty much by by memory so it's
 

00:05:55.680 --> 00:05:58.670 align:start position:0%
do uh pretty much by by memory so it's
private<00:05:56.220><c> void</c><00:05:57.120><c> swap</c>

00:05:58.670 --> 00:05:58.680 align:start position:0%
private void swap
 

00:05:58.680 --> 00:06:01.129 align:start position:0%
private void swap
you<00:05:59.160><c> know</c><00:05:59.340><c> pass</c><00:05:59.520><c> in</c><00:05:59.699><c> the</c><00:05:59.940><c> array</c>

00:06:01.129 --> 00:06:01.139 align:start position:0%
you know pass in the array
 

00:06:01.139 --> 00:06:05.090 align:start position:0%
you know pass in the array
and<00:06:01.860><c> a</c><00:06:02.400><c> and</c><00:06:03.300><c> b</c><00:06:03.780><c> now</c><00:06:04.380><c> there</c><00:06:04.620><c> are</c><00:06:04.740><c> some</c><00:06:04.919><c> other</c>

00:06:05.090 --> 00:06:05.100 align:start position:0%
and a and b now there are some other
 

00:06:05.100 --> 00:06:07.550 align:start position:0%
and a and b now there are some other
clever<00:06:05.520><c> ways</c><00:06:05.880><c> you</c><00:06:06.000><c> can</c><00:06:06.180><c> do</c><00:06:06.300><c> swapping</c><00:06:06.840><c> using</c>

00:06:07.550 --> 00:06:07.560 align:start position:0%
clever ways you can do swapping using
 

00:06:07.560 --> 00:06:11.270 align:start position:0%
clever ways you can do swapping using
like<00:06:07.740><c> one</c><00:06:08.280><c> or</c><00:06:08.460><c> two</c><00:06:08.820><c> lines</c><00:06:09.180><c> but</c><00:06:10.139><c> generally</c><00:06:10.800><c> you</c>

00:06:11.270 --> 00:06:11.280 align:start position:0%
like one or two lines but generally you
 

00:06:11.280 --> 00:06:15.590 align:start position:0%
like one or two lines but generally you
create<00:06:11.460><c> a</c><00:06:11.699><c> temp</c><00:06:12.199><c> of</c><00:06:13.199><c> the</c><00:06:13.800><c> first</c><00:06:14.300><c> array</c><00:06:15.300><c> index</c>

00:06:15.590 --> 00:06:15.600 align:start position:0%
create a temp of the first array index
 

00:06:15.600 --> 00:06:17.689 align:start position:0%
create a temp of the first array index
so<00:06:15.840><c> array</c><00:06:16.199><c> at</c><00:06:16.500><c> a</c>

00:06:17.689 --> 00:06:17.699 align:start position:0%
so array at a
 

00:06:17.699 --> 00:06:20.749 align:start position:0%
so array at a
then<00:06:18.120><c> you</c><00:06:18.300><c> say</c><00:06:18.539><c> array</c><00:06:19.199><c> of</c><00:06:19.800><c> a</c><00:06:20.039><c> is</c><00:06:20.340><c> equal</c><00:06:20.520><c> to</c>

00:06:20.749 --> 00:06:20.759 align:start position:0%
then you say array of a is equal to
 

00:06:20.759 --> 00:06:25.070 align:start position:0%
then you say array of a is equal to
array<00:06:21.419><c> of</c><00:06:21.960><c> B</c><00:06:22.199><c> and</c><00:06:22.860><c> now</c><00:06:23.100><c> since</c><00:06:23.340><c> a</c><00:06:24.060><c> array</c><00:06:24.539><c> of</c><00:06:24.780><c> a</c>

00:06:25.070 --> 00:06:25.080 align:start position:0%
array of B and now since a array of a
 

00:06:25.080 --> 00:06:27.830 align:start position:0%
array of B and now since a array of a
and<00:06:25.319><c> array</c><00:06:25.740><c> of</c><00:06:26.160><c> B</c><00:06:26.460><c> are</c><00:06:27.000><c> equal</c><00:06:27.300><c> to</c><00:06:27.479><c> each</c><00:06:27.720><c> other</c>

00:06:27.830 --> 00:06:27.840 align:start position:0%
and array of B are equal to each other
 

00:06:27.840 --> 00:06:31.610 align:start position:0%
and array of B are equal to each other
when<00:06:28.440><c> you</c><00:06:28.560><c> set</c><00:06:28.800><c> array</c><00:06:29.220><c> of</c><00:06:29.460><c> B</c><00:06:29.759><c> back</c><00:06:30.660><c> to</c><00:06:30.960><c> the</c><00:06:31.259><c> temp</c>

00:06:31.610 --> 00:06:31.620 align:start position:0%
when you set array of B back to the temp
 

00:06:31.620 --> 00:06:34.730 align:start position:0%
when you set array of B back to the temp
value<00:06:32.220><c> and</c><00:06:32.940><c> congratulations</c><00:06:33.560><c> you've</c><00:06:34.560><c> just</c>

00:06:34.730 --> 00:06:34.740 align:start position:0%
value and congratulations you've just
 

00:06:34.740 --> 00:06:37.070 align:start position:0%
value and congratulations you've just
coded<00:06:35.160><c> quick</c><00:06:35.400><c> sort</c><00:06:35.759><c> in</c><00:06:36.240><c> now</c><00:06:36.419><c> is</c><00:06:36.600><c> just</c><00:06:36.780><c> a</c><00:06:36.960><c> quick</c>

00:06:37.070 --> 00:06:37.080 align:start position:0%
coded quick sort in now is just a quick
 

00:06:37.080 --> 00:06:39.350 align:start position:0%
coded quick sort in now is just a quick
test<00:06:37.319><c> add</c><00:06:37.680><c> this</c><00:06:37.860><c> initial</c><00:06:38.160><c> array</c><00:06:38.520><c> we</c><00:06:39.000><c> print</c><00:06:39.180><c> the</c>

00:06:39.350 --> 00:06:39.360 align:start position:0%
test add this initial array we print the
 

00:06:39.360 --> 00:06:41.689 align:start position:0%
test add this initial array we print the
we<00:06:39.720><c> print</c><00:06:39.840><c> the</c><00:06:40.020><c> initial</c><00:06:40.319><c> array</c><00:06:40.620><c> sort</c><00:06:41.160><c> it</c><00:06:41.400><c> and</c>

00:06:41.689 --> 00:06:41.699 align:start position:0%
we print the initial array sort it and
 

00:06:41.699 --> 00:06:43.850 align:start position:0%
we print the initial array sort it and
then<00:06:41.880><c> print</c><00:06:42.240><c> out</c><00:06:42.360><c> the</c><00:06:42.479><c> sorted</c><00:06:42.840><c> array</c><00:06:43.139><c> so</c><00:06:43.680><c> when</c>

00:06:43.850 --> 00:06:43.860 align:start position:0%
then print out the sorted array so when
 

00:06:43.860 --> 00:06:45.529 align:start position:0%
then print out the sorted array so when
we<00:06:44.039><c> go</c><00:06:44.160><c> to</c><00:06:44.280><c> print</c><00:06:44.460><c> this</c>

00:06:45.529 --> 00:06:45.539 align:start position:0%
we go to print this
 

00:06:45.539 --> 00:06:48.650 align:start position:0%
we go to print this
we're<00:06:46.020><c> going</c><00:06:46.199><c> to</c><00:06:46.380><c> run</c><00:06:46.500><c> this</c><00:06:47.060><c> we</c><00:06:48.060><c> get</c><00:06:48.360><c> the</c>

00:06:48.650 --> 00:06:48.660 align:start position:0%
we're going to run this we get the
 

00:06:48.660 --> 00:06:52.129 align:start position:0%
we're going to run this we get the
sorted<00:06:49.139><c> array</c><00:06:50.120><c> congratulations</c><00:06:51.120><c> on</c><00:06:51.660><c> coding</c>

00:06:52.129 --> 00:06:52.139 align:start position:0%
sorted array congratulations on coding
 

00:06:52.139 --> 00:06:53.689 align:start position:0%
sorted array congratulations on coding
the<00:06:52.259><c> quicksort</c><00:06:52.620><c> algorithm</c><00:06:53.039><c> and</c><00:06:53.280><c> following</c>

00:06:53.689 --> 00:06:53.699 align:start position:0%
the quicksort algorithm and following
 

00:06:53.699 --> 00:06:54.409 align:start position:0%
the quicksort algorithm and following
along

00:06:54.409 --> 00:06:54.419 align:start position:0%
along
 

00:06:54.419 --> 00:06:56.809 align:start position:0%
along
if<00:06:54.720><c> you</c><00:06:54.780><c> have</c><00:06:54.960><c> any</c><00:06:55.080><c> comments</c><00:06:55.560><c> or</c><00:06:56.280><c> questions</c>

00:06:56.809 --> 00:06:56.819 align:start position:0%
if you have any comments or questions
 

00:06:56.819 --> 00:06:59.090 align:start position:0%
if you have any comments or questions
put<00:06:57.600><c> them</c><00:06:57.720><c> down</c><00:06:57.900><c> below</c><00:06:58.199><c> I'll</c><00:06:58.620><c> be</c><00:06:58.740><c> more</c><00:06:58.979><c> than</c>

00:06:59.090 --> 00:06:59.100 align:start position:0%
put them down below I'll be more than
 

00:06:59.100 --> 00:07:01.550 align:start position:0%
put them down below I'll be more than
happy<00:06:59.280><c> to</c><00:06:59.460><c> get</c><00:06:59.639><c> with</c><00:06:59.759><c> you</c><00:06:59.880><c> and</c><00:07:00.180><c> help</c><00:07:00.360><c> you</c><00:07:00.479><c> out</c><00:07:00.720><c> I</c>

00:07:01.550 --> 00:07:01.560 align:start position:0%
happy to get with you and help you out I
 

00:07:01.560 --> 00:07:03.529 align:start position:0%
happy to get with you and help you out I
also<00:07:01.740><c> have</c><00:07:01.860><c> an</c><00:07:02.039><c> explanation</c><00:07:02.460><c> video</c><00:07:02.819><c> of</c><00:07:03.360><c> quick</c>

00:07:03.529 --> 00:07:03.539 align:start position:0%
also have an explanation video of quick
 

00:07:03.539 --> 00:07:05.210 align:start position:0%
also have an explanation video of quick
sort<00:07:03.900><c> that</c><00:07:04.080><c> I'll</c><00:07:04.199><c> leave</c><00:07:04.380><c> up</c><00:07:04.560><c> here</c><00:07:04.740><c> in</c><00:07:04.919><c> the</c><00:07:05.100><c> top</c>

00:07:05.210 --> 00:07:05.220 align:start position:0%
sort that I'll leave up here in the top
 

00:07:05.220 --> 00:07:06.890 align:start position:0%
sort that I'll leave up here in the top
left<00:07:05.460><c> I'll</c><00:07:05.880><c> see</c><00:07:06.000><c> you</c><00:07:06.120><c> in</c><00:07:06.180><c> the</c><00:07:06.300><c> next</c><00:07:06.360><c> video</c><00:07:06.539><c> and</c>

00:07:06.890 --> 00:07:06.900 align:start position:0%
left I'll see you in the next video and
 

00:07:06.900 --> 00:07:09.560 align:start position:0%
left I'll see you in the next video and
have<00:07:07.080><c> a</c><00:07:07.199><c> great</c><00:07:07.380><c> day</c>

