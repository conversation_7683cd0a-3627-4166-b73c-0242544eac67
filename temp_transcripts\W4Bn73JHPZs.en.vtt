WEBVTT
Kind: captions
Language: en

00:00:01.199 --> 00:00:03.629 align:start position:0%
 
you've<00:00:01.640><c> just</c><00:00:01.920><c> taken</c><00:00:02.520><c> some</c><00:00:02.760><c> photos</c><00:00:03.240><c> with</c><00:00:03.439><c> that</c>

00:00:03.629 --> 00:00:03.639 align:start position:0%
you've just taken some photos with that
 

00:00:03.639 --> 00:00:05.910 align:start position:0%
you've just taken some photos with that
new<00:00:03.919><c> camera</c><00:00:04.240><c> of</c><00:00:04.400><c> yours</c><00:00:05.160><c> it</c><00:00:05.240><c> can</c><00:00:05.400><c> be</c><00:00:05.520><c> a</c><00:00:05.640><c> lot</c><00:00:05.759><c> of</c>

00:00:05.910 --> 00:00:05.920 align:start position:0%
new camera of yours it can be a lot of
 

00:00:05.920 --> 00:00:08.230 align:start position:0%
new camera of yours it can be a lot of
fun<00:00:06.240><c> taking</c><00:00:06.560><c> the</c><00:00:06.720><c> pics</c><00:00:07.480><c> but</c><00:00:07.799><c> then</c><00:00:08.000><c> you</c><00:00:08.080><c> bring</c>

00:00:08.230 --> 00:00:08.240 align:start position:0%
fun taking the pics but then you bring
 

00:00:08.240 --> 00:00:10.549 align:start position:0%
fun taking the pics but then you bring
it<00:00:08.400><c> home</c><00:00:08.760><c> and</c><00:00:08.920><c> offload</c><00:00:09.360><c> it</c><00:00:09.440><c> to</c><00:00:09.559><c> your</c><00:00:09.719><c> computer</c>

00:00:10.549 --> 00:00:10.559 align:start position:0%
it home and offload it to your computer
 

00:00:10.559 --> 00:00:13.270 align:start position:0%
it home and offload it to your computer
and<00:00:10.759><c> now</c><00:00:10.920><c> you</c><00:00:11.120><c> have</c><00:00:11.440><c> hundreds</c><00:00:12.440><c> or</c><00:00:12.759><c> thousands</c>

00:00:13.270 --> 00:00:13.280 align:start position:0%
and now you have hundreds or thousands
 

00:00:13.280 --> 00:00:16.109 align:start position:0%
and now you have hundreds or thousands
of<00:00:13.480><c> images</c><00:00:13.880><c> with</c><00:00:14.160><c> horrible</c><00:00:14.719><c> names</c><00:00:15.599><c> how</c><00:00:15.759><c> do</c><00:00:15.920><c> you</c>

00:00:16.109 --> 00:00:16.119 align:start position:0%
of images with horrible names how do you
 

00:00:16.119 --> 00:00:19.830 align:start position:0%
of images with horrible names how do you
solve<00:00:16.520><c> this</c><00:00:17.240><c> well</c><00:00:17.520><c> you</c><00:00:17.760><c> could</c><00:00:18.080><c> just</c><00:00:18.359><c> use</c><00:00:18.840><c> AI</c><00:00:19.640><c> to</c>

00:00:19.830 --> 00:00:19.840 align:start position:0%
solve this well you could just use AI to
 

00:00:19.840 --> 00:00:22.750 align:start position:0%
solve this well you could just use AI to
name<00:00:20.160><c> them</c><00:00:20.720><c> just</c><00:00:20.960><c> like</c><00:00:21.240><c> this</c><00:00:22.080><c> it</c><00:00:22.240><c> runs</c><00:00:22.560><c> in</c>

00:00:22.750 --> 00:00:22.760 align:start position:0%
name them just like this it runs in
 

00:00:22.760 --> 00:00:24.470 align:start position:0%
name them just like this it runs in
seconds<00:00:23.320><c> completely</c><00:00:23.760><c> locally</c><00:00:24.199><c> on</c><00:00:24.320><c> your</c>

00:00:24.470 --> 00:00:24.480 align:start position:0%
seconds completely locally on your
 

00:00:24.480 --> 00:00:26.710 align:start position:0%
seconds completely locally on your
machine<00:00:25.279><c> your</c><00:00:25.480><c> images</c><00:00:25.920><c> never</c><00:00:26.199><c> get</c><00:00:26.320><c> shared</c>

00:00:26.710 --> 00:00:26.720 align:start position:0%
machine your images never get shared
 

00:00:26.720 --> 00:00:29.390 align:start position:0%
machine your images never get shared
online<00:00:27.560><c> until</c><00:00:27.920><c> you're</c><00:00:28.199><c> ready</c><00:00:28.400><c> to</c><00:00:28.560><c> do</c><00:00:28.760><c> so</c><00:00:29.320><c> want</c>

00:00:29.390 --> 00:00:29.400 align:start position:0%
online until you're ready to do so want
 

00:00:29.400 --> 00:00:31.630 align:start position:0%
online until you're ready to do so want
to<00:00:29.519><c> see</c><00:00:29.679><c> how</c><00:00:29.800><c> this</c><00:00:30.080><c> is</c><00:00:30.199><c> done</c><00:00:31.039><c> let's</c><00:00:31.279><c> check</c><00:00:31.480><c> out</c>

00:00:31.630 --> 00:00:31.640 align:start position:0%
to see how this is done let's check out
 

00:00:31.640 --> 00:00:33.950 align:start position:0%
to see how this is done let's check out
the<00:00:31.800><c> code</c><00:00:32.040><c> I</c><00:00:32.160><c> wrote</c><00:00:32.439><c> to</c><00:00:32.599><c> build</c><00:00:32.920><c> this</c><00:00:33.559><c> now</c><00:00:33.760><c> even</c>

00:00:33.950 --> 00:00:33.960 align:start position:0%
the code I wrote to build this now even
 

00:00:33.960 --> 00:00:36.190 align:start position:0%
the code I wrote to build this now even
if<00:00:34.079><c> you</c><00:00:34.239><c> know</c><00:00:34.559><c> nothing</c><00:00:34.879><c> about</c><00:00:35.160><c> typescript</c><00:00:35.760><c> or</c>

00:00:36.190 --> 00:00:36.200 align:start position:0%
if you know nothing about typescript or
 

00:00:36.200 --> 00:00:38.670 align:start position:0%
if you know nothing about typescript or
more<00:00:36.520><c> specifically</c><00:00:37.079><c> Dino</c><00:00:38.079><c> it</c><00:00:38.200><c> should</c><00:00:38.440><c> be</c>

00:00:38.670 --> 00:00:38.680 align:start position:0%
more specifically Dino it should be
 

00:00:38.680 --> 00:00:41.069 align:start position:0%
more specifically Dino it should be
pretty<00:00:38.920><c> easy</c><00:00:39.160><c> to</c><00:00:39.320><c> follow</c><00:00:40.239><c> or</c><00:00:40.559><c> if</c><00:00:40.680><c> you</c><00:00:40.920><c> just</c>

00:00:41.069 --> 00:00:41.079 align:start position:0%
pretty easy to follow or if you just
 

00:00:41.079 --> 00:00:43.350 align:start position:0%
pretty easy to follow or if you just
want<00:00:41.320><c> the</c><00:00:41.520><c> executable</c><00:00:42.120><c> for</c><00:00:42.399><c> Mac</c><00:00:42.719><c> or</c><00:00:42.960><c> Windows</c>

00:00:43.350 --> 00:00:43.360 align:start position:0%
want the executable for Mac or Windows
 

00:00:43.360 --> 00:00:45.389 align:start position:0%
want the executable for Mac or Windows
or<00:00:43.600><c> Linux</c><00:00:44.399><c> then</c><00:00:44.559><c> wait</c><00:00:44.719><c> till</c><00:00:44.920><c> the</c><00:00:45.000><c> end</c><00:00:45.160><c> of</c><00:00:45.280><c> the</c>

00:00:45.389 --> 00:00:45.399 align:start position:0%
or Linux then wait till the end of the
 

00:00:45.399 --> 00:00:47.630 align:start position:0%
or Linux then wait till the end of the
video<00:00:45.879><c> and</c><00:00:46.039><c> I'll</c><00:00:46.160><c> show</c><00:00:46.320><c> you</c><00:00:46.480><c> where</c><00:00:46.600><c> to</c><00:00:46.760><c> get</c><00:00:46.840><c> it</c>

00:00:47.630 --> 00:00:47.640 align:start position:0%
video and I'll show you where to get it
 

00:00:47.640 --> 00:00:49.510 align:start position:0%
video and I'll show you where to get it
here's<00:00:47.879><c> the</c><00:00:48.000><c> main</c><00:00:48.280><c> function</c><00:00:49.000><c> I</c><00:00:49.160><c> get</c><00:00:49.360><c> the</c>

00:00:49.510 --> 00:00:49.520 align:start position:0%
here's the main function I get the
 

00:00:49.520 --> 00:00:51.229 align:start position:0%
here's the main function I get the
current<00:00:49.879><c> working</c><00:00:50.360><c> directory</c><00:00:50.879><c> and</c><00:00:51.039><c> then</c>

00:00:51.229 --> 00:00:51.239 align:start position:0%
current working directory and then
 

00:00:51.239 --> 00:00:54.029 align:start position:0%
current working directory and then
iterate<00:00:51.719><c> through</c><00:00:52.039><c> all</c><00:00:52.239><c> the</c><00:00:52.440><c> files</c><00:00:53.280><c> if</c><00:00:53.399><c> a</c><00:00:53.640><c> file</c>

00:00:54.029 --> 00:00:54.039 align:start position:0%
iterate through all the files if a file
 

00:00:54.039 --> 00:00:57.389 align:start position:0%
iterate through all the files if a file
is<00:00:54.120><c> a</c><00:00:54.320><c> JPEG</c><00:00:54.840><c> or</c><00:00:55.160><c> PNG</c><00:00:56.160><c> then</c><00:00:56.480><c> I'm</c><00:00:56.600><c> going</c><00:00:56.800><c> to</c><00:00:57.000><c> get</c><00:00:57.160><c> a</c>

00:00:57.389 --> 00:00:57.399 align:start position:0%
is a JPEG or PNG then I'm going to get a
 

00:00:57.399 --> 00:01:00.990 align:start position:0%
is a JPEG or PNG then I'm going to get a
base<00:00:57.719><c> 64</c><00:00:58.320><c> encoding</c><00:00:58.960><c> of</c><00:00:59.079><c> the</c><00:00:59.280><c> file</c><00:01:00.199><c> more</c><00:01:00.559><c> about</c>

00:01:00.990 --> 00:01:01.000 align:start position:0%
base 64 encoding of the file more about
 

00:01:01.000 --> 00:01:05.270 align:start position:0%
base 64 encoding of the file more about
why<00:01:01.640><c> in</c><00:01:01.800><c> a</c><00:01:02.000><c> second</c><00:01:02.960><c> now</c><00:01:03.320><c> I</c><00:01:03.480><c> call</c><00:01:03.840><c> get</c><00:01:04.280><c> keywords</c>

00:01:05.270 --> 00:01:05.280 align:start position:0%
why in a second now I call get keywords
 

00:01:05.280 --> 00:01:07.510 align:start position:0%
why in a second now I call get keywords
this<00:01:05.560><c> returns</c><00:01:05.799><c> a</c><00:01:06.040><c> promise</c><00:01:06.640><c> of</c><00:01:06.760><c> an</c><00:01:06.920><c> array</c><00:01:07.320><c> of</c>

00:01:07.510 --> 00:01:07.520 align:start position:0%
this returns a promise of an array of
 

00:01:07.520 --> 00:01:10.550 align:start position:0%
this returns a promise of an array of
keyword<00:01:08.360><c> strings</c><00:01:09.360><c> so</c><00:01:09.600><c> let's</c><00:01:09.840><c> scroll</c><00:01:10.200><c> up</c><00:01:10.400><c> to</c>

00:01:10.550 --> 00:01:10.560 align:start position:0%
keyword strings so let's scroll up to
 

00:01:10.560 --> 00:01:12.910 align:start position:0%
keyword strings so let's scroll up to
the<00:01:10.759><c> definition</c><00:01:11.159><c> of</c><00:01:11.360><c> get</c><00:01:11.560><c> keywords</c><00:01:12.520><c> I'm</c><00:01:12.640><c> using</c>

00:01:12.910 --> 00:01:12.920 align:start position:0%
the definition of get keywords I'm using
 

00:01:12.920 --> 00:01:15.190 align:start position:0%
the definition of get keywords I'm using
the<00:01:13.119><c> generate</c><00:01:13.600><c> endpoint</c><00:01:14.040><c> for</c><00:01:14.240><c> a</c><00:01:14.479><c> llama</c><00:01:14.920><c> which</c>

00:01:15.190 --> 00:01:15.200 align:start position:0%
the generate endpoint for a llama which
 

00:01:15.200 --> 00:01:17.310 align:start position:0%
the generate endpoint for a llama which
you<00:01:15.320><c> can</c><00:01:15.479><c> learn</c><00:01:15.720><c> about</c><00:01:16.159><c> here</c><00:01:16.360><c> in</c><00:01:16.479><c> the</c><00:01:16.560><c> repo</c><00:01:17.040><c> do</c>

00:01:17.310 --> 00:01:17.320 align:start position:0%
you can learn about here in the repo do
 

00:01:17.320 --> 00:01:20.550 align:start position:0%
you can learn about here in the repo do
in<00:01:17.439><c> the</c><00:01:17.640><c> api.</c><00:01:18.360><c> MD</c><00:01:18.840><c> file</c><00:01:19.680><c> you</c><00:01:19.920><c> post</c><00:01:20.200><c> to</c><00:01:20.400><c> that</c>

00:01:20.550 --> 00:01:20.560 align:start position:0%
in the api. MD file you post to that
 

00:01:20.560 --> 00:01:22.550 align:start position:0%
in the api. MD file you post to that
endpoint<00:01:21.079><c> and</c><00:01:21.280><c> pass</c><00:01:21.520><c> it</c><00:01:21.680><c> the</c><00:01:21.840><c> body</c><00:01:22.200><c> which</c>

00:01:22.550 --> 00:01:22.560 align:start position:0%
endpoint and pass it the body which
 

00:01:22.560 --> 00:01:25.190 align:start position:0%
endpoint and pass it the body which
needs<00:01:22.799><c> to</c><00:01:22.920><c> be</c><00:01:23.079><c> a</c><00:01:23.240><c> string</c><00:01:23.560><c> of</c><00:01:23.720><c> the</c><00:01:23.880><c> Json</c><00:01:24.720><c> so</c><00:01:24.960><c> up</c>

00:01:25.190 --> 00:01:25.200 align:start position:0%
needs to be a string of the Json so up
 

00:01:25.200 --> 00:01:27.190 align:start position:0%
needs to be a string of the Json so up
above<00:01:25.520><c> I</c><00:01:25.640><c> created</c><00:01:26.000><c> the</c><00:01:26.159><c> body</c><00:01:26.680><c> that</c><00:01:26.920><c> actually</c>

00:01:27.190 --> 00:01:27.200 align:start position:0%
above I created the body that actually
 

00:01:27.200 --> 00:01:29.550 align:start position:0%
above I created the body that actually
gets<00:01:27.439><c> sent</c><00:01:27.799><c> to</c><00:01:27.960><c> the</c><00:01:28.079><c> endpoint</c><00:01:28.920><c> I</c><00:01:29.079><c> specify</c><00:01:29.479><c> the</c>

00:01:29.550 --> 00:01:29.560 align:start position:0%
gets sent to the endpoint I specify the
 

00:01:29.560 --> 00:01:33.190 align:start position:0%
gets sent to the endpoint I specify the
model<00:01:30.200><c> I'm</c><00:01:30.360><c> using</c><00:01:30.759><c> lava</c><00:01:31.360><c> 13B</c><00:01:32.360><c> which</c><00:01:32.479><c> is</c><00:01:32.600><c> a</c><00:01:32.799><c> 13</c>

00:01:33.190 --> 00:01:33.200 align:start position:0%
model I'm using lava 13B which is a 13
 

00:01:33.200 --> 00:01:35.510 align:start position:0%
model I'm using lava 13B which is a 13
billion<00:01:33.600><c> parameter</c><00:01:34.040><c> model</c><00:01:34.880><c> since</c><00:01:35.159><c> I'm</c><00:01:35.320><c> going</c>

00:01:35.510 --> 00:01:35.520 align:start position:0%
billion parameter model since I'm going
 

00:01:35.520 --> 00:01:37.870 align:start position:0%
billion parameter model since I'm going
to<00:01:35.880><c> use</c><00:01:36.159><c> the</c><00:01:36.320><c> output</c><00:01:36.720><c> of</c><00:01:36.840><c> the</c><00:01:37.000><c> call</c><00:01:37.439><c> in</c><00:01:37.600><c> a</c>

00:01:37.870 --> 00:01:37.880 align:start position:0%
to use the output of the call in a
 

00:01:37.880 --> 00:01:41.109 align:start position:0%
to use the output of the call in a
function<00:01:38.600><c> I</c><00:01:38.680><c> want</c><00:01:38.799><c> to</c><00:01:39.040><c> Output</c><00:01:39.759><c> Json</c><00:01:40.759><c> then</c><00:01:41.000><c> in</c>

00:01:41.109 --> 00:01:41.119 align:start position:0%
function I want to Output Json then in
 

00:01:41.119 --> 00:01:43.310 align:start position:0%
function I want to Output Json then in
the<00:01:41.280><c> prompt</c><00:01:41.680><c> I</c><00:01:41.840><c> describe</c><00:01:42.240><c> what</c><00:01:42.360><c> I</c><00:01:42.479><c> want</c>

00:01:43.310 --> 00:01:43.320 align:start position:0%
the prompt I describe what I want
 

00:01:43.320 --> 00:01:45.550 align:start position:0%
the prompt I describe what I want
describe<00:01:43.880><c> the</c><00:01:44.079><c> image</c><00:01:44.520><c> as</c><00:01:44.640><c> a</c><00:01:44.799><c> collection</c><00:01:45.280><c> of</c>

00:01:45.550 --> 00:01:45.560 align:start position:0%
describe the image as a collection of
 

00:01:45.560 --> 00:01:48.550 align:start position:0%
describe the image as a collection of
keywords<00:01:46.560><c> output</c><00:01:47.000><c> in</c><00:01:47.280><c> Json</c><00:01:47.880><c> format</c><00:01:48.360><c> is</c>

00:01:48.550 --> 00:01:48.560 align:start position:0%
keywords output in Json format is
 

00:01:48.560 --> 00:01:51.389 align:start position:0%
keywords output in Json format is
important<00:01:49.159><c> when</c><00:01:49.320><c> we</c><00:01:49.479><c> want</c><00:01:49.600><c> to</c><00:01:49.799><c> format</c><00:01:50.399><c> Json</c>

00:01:51.389 --> 00:01:51.399 align:start position:0%
important when we want to format Json
 

00:01:51.399 --> 00:01:54.590 align:start position:0%
important when we want to format Json
you<00:01:51.560><c> need</c><00:01:51.719><c> to</c><00:01:51.960><c> specify</c><00:01:52.600><c> in</c><00:01:53.000><c> both</c><00:01:53.439><c> places</c><00:01:54.439><c> and</c>

00:01:54.590 --> 00:01:54.600 align:start position:0%
you need to specify in both places and
 

00:01:54.600 --> 00:01:56.670 align:start position:0%
you need to specify in both places and
then<00:01:54.840><c> it's</c><00:01:55.040><c> helpful</c><00:01:55.360><c> to</c><00:01:55.560><c> include</c><00:01:55.880><c> the</c><00:01:56.039><c> schema</c>

00:01:56.670 --> 00:01:56.680 align:start position:0%
then it's helpful to include the schema
 

00:01:56.680 --> 00:01:59.950 align:start position:0%
then it's helpful to include the schema
you<00:01:56.840><c> want</c><00:01:57.000><c> to</c><00:01:57.200><c> use</c><00:01:57.640><c> for</c><00:01:57.880><c> your</c><00:01:58.560><c> Json</c><00:01:59.560><c> then</c>

00:01:59.950 --> 00:01:59.960 align:start position:0%
you want to use for your Json then
 

00:01:59.960 --> 00:02:02.590 align:start position:0%
you want to use for your Json then
because<00:02:00.240><c> I'm</c><00:02:00.399><c> using</c><00:02:00.759><c> a</c><00:02:00.960><c> lava</c><00:02:01.320><c> model</c><00:02:02.079><c> I</c><00:02:02.200><c> need</c><00:02:02.360><c> to</c>

00:02:02.590 --> 00:02:02.600 align:start position:0%
because I'm using a lava model I need to
 

00:02:02.600 --> 00:02:06.429 align:start position:0%
because I'm using a lava model I need to
give<00:02:02.880><c> an</c><00:02:03.039><c> array</c><00:02:03.479><c> of</c><00:02:03.680><c> Base</c><00:02:03.960><c> 64</c><00:02:04.560><c> encoded</c><00:02:05.439><c> images</c>

00:02:06.429 --> 00:02:06.439 align:start position:0%
give an array of Base 64 encoded images
 

00:02:06.439 --> 00:02:08.589 align:start position:0%
give an array of Base 64 encoded images
I<00:02:06.560><c> think</c><00:02:06.719><c> it</c><00:02:07.159><c> only</c><00:02:07.840><c> understands</c><00:02:08.119><c> one</c><00:02:08.319><c> image</c>

00:02:08.589 --> 00:02:08.599 align:start position:0%
I think it only understands one image
 

00:02:08.599 --> 00:02:11.110 align:start position:0%
I think it only understands one image
for<00:02:08.840><c> now</c><00:02:09.239><c> but</c><00:02:09.479><c> it</c><00:02:09.679><c> still</c><00:02:09.920><c> needs</c><00:02:10.160><c> to</c><00:02:10.319><c> be</c><00:02:10.640><c> an</c>

00:02:11.110 --> 00:02:11.120 align:start position:0%
for now but it still needs to be an
 

00:02:11.120 --> 00:02:14.070 align:start position:0%
for now but it still needs to be an
array<00:02:12.120><c> then</c><00:02:12.599><c> since</c><00:02:12.879><c> the</c><00:02:13.040><c> output</c><00:02:13.440><c> of</c><00:02:13.599><c> the</c><00:02:13.720><c> model</c>

00:02:14.070 --> 00:02:14.080 align:start position:0%
array then since the output of the model
 

00:02:14.080 --> 00:02:16.550 align:start position:0%
array then since the output of the model
isn't<00:02:14.319><c> for</c><00:02:14.560><c> human</c><00:02:14.840><c> consumption</c><00:02:15.720><c> I</c><00:02:15.879><c> set</c><00:02:16.080><c> stream</c>

00:02:16.550 --> 00:02:16.560 align:start position:0%
isn't for human consumption I set stream
 

00:02:16.560 --> 00:02:18.750 align:start position:0%
isn't for human consumption I set stream
to<00:02:16.760><c> false</c><00:02:17.680><c> and</c><00:02:17.840><c> that</c><00:02:17.959><c> just</c><00:02:18.080><c> makes</c><00:02:18.239><c> it</c><00:02:18.360><c> easier</c>

00:02:18.750 --> 00:02:18.760 align:start position:0%
to false and that just makes it easier
 

00:02:18.760 --> 00:02:21.710 align:start position:0%
to false and that just makes it easier
to<00:02:18.920><c> deal</c><00:02:19.200><c> with</c><00:02:20.200><c> now</c><00:02:20.519><c> I</c><00:02:20.599><c> can</c><00:02:20.800><c> get</c><00:02:20.959><c> the</c><00:02:21.120><c> Json</c><00:02:21.560><c> from</c>

00:02:21.710 --> 00:02:21.720 align:start position:0%
to deal with now I can get the Json from
 

00:02:21.720 --> 00:02:23.630 align:start position:0%
to deal with now I can get the Json from
the<00:02:21.879><c> response</c><00:02:22.560><c> parse</c><00:02:22.840><c> it</c><00:02:23.120><c> and</c><00:02:23.280><c> then</c><00:02:23.480><c> the</c>

00:02:23.630 --> 00:02:23.640 align:start position:0%
the response parse it and then the
 

00:02:23.640 --> 00:02:27.190 align:start position:0%
the response parse it and then the
keywords<00:02:24.480><c> is</c><00:02:24.720><c> just</c><00:02:25.000><c> the</c><00:02:25.200><c> array</c><00:02:25.599><c> of</c><00:02:26.200><c> keywords</c>

00:02:27.190 --> 00:02:27.200 align:start position:0%
keywords is just the array of keywords
 

00:02:27.200 --> 00:02:29.070 align:start position:0%
keywords is just the array of keywords
the<00:02:27.360><c> next</c><00:02:27.519><c> line</c><00:02:27.720><c> in</c><00:02:27.879><c> my</c><00:02:28.000><c> main</c><00:02:28.280><c> function</c><00:02:28.760><c> is</c><00:02:28.879><c> to</c>

00:02:29.070 --> 00:02:29.080 align:start position:0%
the next line in my main function is to
 

00:02:29.080 --> 00:02:31.110 align:start position:0%
the next line in my main function is to
take<00:02:29.319><c> that</c><00:02:29.480><c> array</c><00:02:29.959><c> of</c><00:02:30.040><c> keywords</c><00:02:30.640><c> and</c><00:02:30.879><c> an</c>

00:02:31.110 --> 00:02:31.120 align:start position:0%
take that array of keywords and an
 

00:02:31.120 --> 00:02:33.670 align:start position:0%
take that array of keywords and an
extension<00:02:31.840><c> and</c><00:02:31.959><c> generate</c><00:02:32.319><c> a</c><00:02:32.480><c> file</c><00:02:32.720><c> name</c><00:02:33.560><c> this</c>

00:02:33.670 --> 00:02:33.680 align:start position:0%
extension and generate a file name this
 

00:02:33.680 --> 00:02:35.550 align:start position:0%
extension and generate a file name this
starts<00:02:34.000><c> with</c><00:02:34.160><c> an</c><00:02:34.319><c> array</c><00:02:34.599><c> map</c><00:02:34.879><c> that</c><00:02:35.000><c> replaces</c>

00:02:35.550 --> 00:02:35.560 align:start position:0%
starts with an array map that replaces
 

00:02:35.560 --> 00:02:37.670 align:start position:0%
starts with an array map that replaces
spaces<00:02:36.000><c> with</c><00:02:36.200><c> underscores</c><00:02:37.120><c> then</c><00:02:37.239><c> joins</c><00:02:37.560><c> the</c>

00:02:37.670 --> 00:02:37.680 align:start position:0%
spaces with underscores then joins the
 

00:02:37.680 --> 00:02:40.309 align:start position:0%
spaces with underscores then joins the
keywords<00:02:38.160><c> with</c><00:02:38.360><c> dashes</c><00:02:38.920><c> and</c><00:02:39.159><c> attaches</c><00:02:39.680><c> an</c>

00:02:40.309 --> 00:02:40.319 align:start position:0%
keywords with dashes and attaches an
 

00:02:40.319 --> 00:02:43.550 align:start position:0%
keywords with dashes and attaches an
extension<00:02:41.319><c> and</c><00:02:41.480><c> then</c><00:02:41.840><c> we're</c><00:02:42.040><c> done</c><00:02:42.400><c> there</c><00:02:43.280><c> the</c>

00:02:43.550 --> 00:02:43.560 align:start position:0%
extension and then we're done there the
 

00:02:43.560 --> 00:02:46.430 align:start position:0%
extension and then we're done there the
last<00:02:43.879><c> step</c><00:02:44.200><c> is</c><00:02:44.440><c> copy</c><00:02:44.840><c> file</c><00:02:45.120><c> sync</c><00:02:46.080><c> this</c><00:02:46.239><c> just</c>

00:02:46.430 --> 00:02:46.440 align:start position:0%
last step is copy file sync this just
 

00:02:46.440 --> 00:02:48.470 align:start position:0%
last step is copy file sync this just
takes<00:02:46.680><c> the</c><00:02:46.800><c> original</c><00:02:47.159><c> file</c><00:02:47.680><c> and</c><00:02:47.920><c> copies</c><00:02:48.239><c> it</c><00:02:48.319><c> to</c>

00:02:48.470 --> 00:02:48.480 align:start position:0%
takes the original file and copies it to
 

00:02:48.480 --> 00:02:50.750 align:start position:0%
takes the original file and copies it to
our<00:02:48.599><c> new</c><00:02:48.840><c> file</c><00:02:49.080><c> name</c><00:02:50.000><c> if</c><00:02:50.080><c> you</c><00:02:50.200><c> want</c><00:02:50.280><c> to</c><00:02:50.440><c> take</c><00:02:50.599><c> a</c>

00:02:50.750 --> 00:02:50.760 align:start position:0%
our new file name if you want to take a
 

00:02:50.760 --> 00:02:52.990 align:start position:0%
our new file name if you want to take a
look<00:02:50.959><c> at</c><00:02:51.120><c> the</c><00:02:51.239><c> code</c><00:02:51.840><c> you</c><00:02:51.959><c> can</c><00:02:52.200><c> find</c><00:02:52.440><c> it</c><00:02:52.599><c> on</c><00:02:52.800><c> my</c>

00:02:52.990 --> 00:02:53.000 align:start position:0%
look at the code you can find it on my
 

00:02:53.000 --> 00:02:55.790 align:start position:0%
look at the code you can find it on my
GitHub<00:02:53.599><c> right</c><00:02:53.920><c> here</c><00:02:54.760><c> or</c><00:02:55.159><c> if</c><00:02:55.280><c> you</c><00:02:55.400><c> just</c><00:02:55.519><c> want</c><00:02:55.640><c> to</c>

00:02:55.790 --> 00:02:55.800 align:start position:0%
GitHub right here or if you just want to
 

00:02:55.800 --> 00:02:57.910 align:start position:0%
GitHub right here or if you just want to
try<00:02:56.080><c> the</c><00:02:56.319><c> executable</c><00:02:57.000><c> to</c><00:02:57.200><c> try</c><00:02:57.400><c> on</c><00:02:57.519><c> your</c><00:02:57.680><c> own</c>

00:02:57.910 --> 00:02:57.920 align:start position:0%
try the executable to try on your own
 

00:02:57.920 --> 00:02:59.830 align:start position:0%
try the executable to try on your own
images<00:02:58.440><c> then</c><00:02:58.920><c> you</c><00:02:59.000><c> can</c><00:02:59.200><c> find</c><00:02:59.360><c> it</c><00:02:59.480><c> in</c><00:02:59.599><c> the</c><00:02:59.760><c> the</c>

00:02:59.830 --> 00:02:59.840 align:start position:0%
images then you can find it in the the
 

00:02:59.840 --> 00:03:02.149 align:start position:0%
images then you can find it in the the
releaser<00:03:00.360><c> section</c><00:03:01.200><c> you'll</c><00:03:01.560><c> probably</c><00:03:01.800><c> want</c><00:03:01.959><c> to</c>

00:03:02.149 --> 00:03:02.159 align:start position:0%
releaser section you'll probably want to
 

00:03:02.159 --> 00:03:04.830 align:start position:0%
releaser section you'll probably want to
rename<00:03:02.680><c> the</c><00:03:02.879><c> download</c><00:03:03.280><c> to</c><00:03:03.480><c> something</c><00:03:03.840><c> like</c><00:03:04.319><c> Ai</c>

00:03:04.830 --> 00:03:04.840 align:start position:0%
rename the download to something like Ai
 

00:03:04.840 --> 00:03:08.030 align:start position:0%
rename the download to something like Ai
renamer<00:03:05.840><c> and</c><00:03:06.080><c> ensure</c><00:03:06.480><c> that</c><00:03:06.599><c> it's</c><00:03:07.040><c> executable</c>

00:03:08.030 --> 00:03:08.040 align:start position:0%
renamer and ensure that it's executable
 

00:03:08.040 --> 00:03:10.110 align:start position:0%
renamer and ensure that it's executable
and<00:03:08.239><c> of</c><00:03:08.400><c> course</c><00:03:08.840><c> you</c><00:03:08.959><c> need</c><00:03:09.159><c> to</c><00:03:09.319><c> have</c><00:03:09.519><c> a</c><00:03:09.799><c> Lama</c>

00:03:10.110 --> 00:03:10.120 align:start position:0%
and of course you need to have a Lama
 

00:03:10.120 --> 00:03:12.070 align:start position:0%
and of course you need to have a Lama
installed<00:03:10.599><c> on</c><00:03:10.720><c> your</c><00:03:10.920><c> system</c><00:03:11.599><c> and</c><00:03:11.760><c> have</c><00:03:11.920><c> the</c>

00:03:12.070 --> 00:03:12.080 align:start position:0%
installed on your system and have the
 

00:03:12.080 --> 00:03:15.830 align:start position:0%
installed on your system and have the
lava<00:03:12.440><c> 13B</c><00:03:13.120><c> model</c><00:03:13.519><c> pulled</c><00:03:14.480><c> oh</c><00:03:14.720><c> and</c><00:03:14.920><c> you</c><00:03:15.200><c> might</c>

00:03:15.830 --> 00:03:15.840 align:start position:0%
lava 13B model pulled oh and you might
 

00:03:15.840 --> 00:03:18.110 align:start position:0%
lava 13B model pulled oh and you might
also<00:03:16.440><c> want</c><00:03:16.599><c> to</c><00:03:16.840><c> ensure</c><00:03:17.239><c> that</c><00:03:17.360><c> your</c><00:03:17.560><c> images</c><00:03:17.879><c> are</c>

00:03:18.110 --> 00:03:18.120 align:start position:0%
also want to ensure that your images are
 

00:03:18.120 --> 00:03:22.190 align:start position:0%
also want to ensure that your images are
backed<00:03:18.480><c> up</c><00:03:19.480><c> just</c><00:03:19.760><c> in</c><00:03:20.319><c> case</c><00:03:21.319><c> well</c><00:03:21.599><c> I</c><00:03:21.720><c> hope</c><00:03:21.920><c> you</c>

00:03:22.190 --> 00:03:22.200 align:start position:0%
backed up just in case well I hope you
 

00:03:22.200 --> 00:03:24.229 align:start position:0%
backed up just in case well I hope you
have<00:03:22.519><c> as</c><00:03:22.680><c> much</c><00:03:22.879><c> fun</c><00:03:23.120><c> with</c><00:03:23.280><c> this</c><00:03:23.480><c> as</c><00:03:23.799><c> I</c><00:03:24.000><c> had</c>

00:03:24.229 --> 00:03:24.239 align:start position:0%
have as much fun with this as I had
 

00:03:24.239 --> 00:03:28.670 align:start position:0%
have as much fun with this as I had
writing<00:03:24.640><c> it</c><00:03:25.200><c> thanks</c><00:03:25.440><c> so</c><00:03:25.560><c> much</c><00:03:25.720><c> for</c><00:03:25.920><c> watching</c>

00:03:28.670 --> 00:03:28.680 align:start position:0%
 
 

00:03:28.680 --> 00:03:37.010 align:start position:0%
 
goodbye

00:03:37.010 --> 00:03:37.020 align:start position:0%
 
 

00:03:37.020 --> 00:03:46.149 align:start position:0%
 
[Music]

00:03:46.149 --> 00:03:46.159 align:start position:0%
[Music]
 

00:03:46.159 --> 00:03:49.159 align:start position:0%
[Music]
to

