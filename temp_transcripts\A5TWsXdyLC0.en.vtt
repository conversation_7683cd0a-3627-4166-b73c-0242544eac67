WEBVTT
Kind: captions
Language: en

00:00:00.740 --> 00:00:04.010 align:start position:0%
 
hey<00:00:01.740><c> guys</c><00:00:01.949><c> and</c><00:00:02.250><c> welcome</c><00:00:02.310><c> in</c><00:00:02.939><c> this</c><00:00:03.210><c> video</c><00:00:03.510><c> we're</c>

00:00:04.010 --> 00:00:04.020 align:start position:0%
hey guys and welcome in this video we're
 

00:00:04.020 --> 00:00:05.599 align:start position:0%
hey guys and welcome in this video we're
gonna<00:00:04.140><c> be</c><00:00:04.230><c> talking</c><00:00:04.350><c> all</c><00:00:04.770><c> about</c><00:00:04.799><c> the</c><00:00:05.040><c> meta</c><00:00:05.370><c> bass</c>

00:00:05.599 --> 00:00:05.609 align:start position:0%
gonna be talking all about the meta bass
 

00:00:05.609 --> 00:00:09.200 align:start position:0%
gonna be talking all about the meta bass
API<00:00:06.330><c> so</c><00:00:06.990><c> this</c><00:00:07.140><c> is</c><00:00:07.290><c> a</c><00:00:07.319><c> I</c><00:00:08.309><c> think</c><00:00:08.730><c> a</c><00:00:08.910><c> really</c>

00:00:09.200 --> 00:00:09.210 align:start position:0%
API so this is a I think a really
 

00:00:09.210 --> 00:00:11.870 align:start position:0%
API so this is a I think a really
powerful<00:00:09.620><c> technology</c><00:00:10.620><c> I'm</c><00:00:11.370><c> going</c><00:00:11.639><c> to</c><00:00:11.700><c> share</c>

00:00:11.870 --> 00:00:11.880 align:start position:0%
powerful technology I'm going to share
 

00:00:11.880 --> 00:00:15.049 align:start position:0%
powerful technology I'm going to share
my<00:00:12.030><c> screen</c><00:00:12.059><c> with</c><00:00:12.480><c> you</c><00:00:12.509><c> and</c><00:00:13.280><c> first</c><00:00:14.280><c> of</c><00:00:14.429><c> all</c><00:00:14.580><c> what</c>

00:00:15.049 --> 00:00:15.059 align:start position:0%
my screen with you and first of all what
 

00:00:15.059 --> 00:00:18.769 align:start position:0%
my screen with you and first of all what
I<00:00:15.089><c> have</c><00:00:15.360><c> here</c><00:00:15.809><c> is</c><00:00:16.020><c> the</c><00:00:16.949><c> meta</c><00:00:17.160><c> based</c><00:00:17.400><c> API</c><00:00:17.609><c> and</c><00:00:18.300><c> of</c>

00:00:18.769 --> 00:00:18.779 align:start position:0%
I have here is the meta based API and of
 

00:00:18.779 --> 00:00:20.109 align:start position:0%
I have here is the meta based API and of
course<00:00:19.020><c> meta</c><00:00:19.230><c> base</c><00:00:19.410><c> is</c><00:00:19.560><c> a</c><00:00:19.590><c> data</c><00:00:19.859><c> visualization</c>

00:00:20.109 --> 00:00:20.119 align:start position:0%
course meta base is a data visualization
 

00:00:20.119 --> 00:00:23.990 align:start position:0%
course meta base is a data visualization
platform<00:00:21.230><c> here's</c><00:00:22.230><c> my</c><00:00:22.410><c> Roku</c><00:00:22.949><c> deployment</c><00:00:23.850><c> I</c>

00:00:23.990 --> 00:00:24.000 align:start position:0%
platform here's my Roku deployment I
 

00:00:24.000 --> 00:00:25.790 align:start position:0%
platform here's my Roku deployment I
made<00:00:24.180><c> other</c><00:00:24.449><c> videos</c><00:00:24.570><c> which</c><00:00:25.170><c> is</c><00:00:25.320><c> all</c><00:00:25.439><c> about</c><00:00:25.650><c> how</c>

00:00:25.790 --> 00:00:25.800 align:start position:0%
made other videos which is all about how
 

00:00:25.800 --> 00:00:28.910 align:start position:0%
made other videos which is all about how
to<00:00:25.859><c> deploy</c><00:00:26.279><c> to</c><00:00:26.730><c> Heroku</c><00:00:26.880><c> and</c><00:00:27.590><c> you</c><00:00:28.590><c> can</c><00:00:28.740><c> feel</c>

00:00:28.910 --> 00:00:28.920 align:start position:0%
to deploy to Heroku and you can feel
 

00:00:28.920 --> 00:00:31.630 align:start position:0%
to deploy to Heroku and you can feel
free<00:00:29.160><c> to</c><00:00:29.220><c> check</c><00:00:29.490><c> those</c><00:00:29.760><c> out</c><00:00:30.029><c> on</c><00:00:30.359><c> my</c><00:00:30.510><c> channel</c>

00:00:31.630 --> 00:00:31.640 align:start position:0%
free to check those out on my channel
 

00:00:31.640 --> 00:00:35.510 align:start position:0%
free to check those out on my channel
let's<00:00:32.640><c> see</c><00:00:32.940><c> okay</c><00:00:33.480><c> now</c><00:00:33.899><c> you</c><00:00:34.500><c> can</c><00:00:34.680><c> pay</c><00:00:34.950><c> something</c>

00:00:35.510 --> 00:00:35.520 align:start position:0%
let's see okay now you can pay something
 

00:00:35.520 --> 00:00:38.330 align:start position:0%
let's see okay now you can pay something
like<00:00:35.850><c> seven</c><00:00:36.510><c> dollars</c><00:00:36.630><c> a</c><00:00:36.870><c> month</c><00:00:37.200><c> for</c><00:00:37.590><c> a</c><00:00:37.649><c> Heroku</c>

00:00:38.330 --> 00:00:38.340 align:start position:0%
like seven dollars a month for a Heroku
 

00:00:38.340 --> 00:00:40.910 align:start position:0%
like seven dollars a month for a Heroku
dynos<00:00:38.899><c> which</c><00:00:39.899><c> will</c><00:00:40.110><c> make</c><00:00:40.320><c> it</c><00:00:40.350><c> a</c><00:00:40.530><c> little</c><00:00:40.680><c> bit</c>

00:00:40.910 --> 00:00:40.920 align:start position:0%
dynos which will make it a little bit
 

00:00:40.920 --> 00:00:43.580 align:start position:0%
dynos which will make it a little bit
faster<00:00:41.489><c> it's</c><00:00:41.760><c> just</c><00:00:42.090><c> a</c><00:00:42.239><c> just</c><00:00:43.050><c> a</c><00:00:43.170><c> little</c><00:00:43.410><c> more</c>

00:00:43.580 --> 00:00:43.590 align:start position:0%
faster it's just a just a little more
 

00:00:43.590 --> 00:00:47.000 align:start position:0%
faster it's just a just a little more
power<00:00:43.860><c> for</c><00:00:44.340><c> the</c><00:00:44.489><c> server</c><00:00:44.760><c> but</c><00:00:45.719><c> over</c><00:00:46.559><c> here</c><00:00:46.770><c> we</c>

00:00:47.000 --> 00:00:47.010 align:start position:0%
power for the server but over here we
 

00:00:47.010 --> 00:00:50.770 align:start position:0%
power for the server but over here we
have<00:00:47.190><c> meta</c><00:00:47.910><c> base</c><00:00:48.090><c> homepage</c><00:00:48.660><c> first</c><00:00:49.050><c> of</c><00:00:49.230><c> all</c><00:00:49.350><c> and</c>

00:00:50.770 --> 00:00:50.780 align:start position:0%
have meta base homepage first of all and
 

00:00:50.780 --> 00:00:53.420 align:start position:0%
have meta base homepage first of all and
home<00:00:51.780><c> page</c><00:00:51.989><c> is</c><00:00:52.140><c> really</c><00:00:52.410><c> nice</c><00:00:52.559><c> because</c><00:00:52.860><c> you</c>

00:00:53.420 --> 00:00:53.430 align:start position:0%
home page is really nice because you
 

00:00:53.430 --> 00:00:55.369 align:start position:0%
home page is really nice because you
have<00:00:53.550><c> all</c><00:00:54.030><c> of</c><00:00:54.059><c> the</c><00:00:54.360><c> questions</c><00:00:54.899><c> that</c><00:00:54.989><c> every</c>

00:00:55.369 --> 00:00:55.379 align:start position:0%
have all of the questions that every
 

00:00:55.379 --> 00:00:58.189 align:start position:0%
have all of the questions that every
single<00:00:56.090><c> member</c><00:00:57.090><c> of</c><00:00:57.120><c> the</c><00:00:57.360><c> team</c><00:00:57.600><c> has</c><00:00:57.840><c> written</c>

00:00:58.189 --> 00:00:58.199 align:start position:0%
single member of the team has written
 

00:00:58.199 --> 00:01:00.139 align:start position:0%
single member of the team has written
and<00:00:58.559><c> I</c><00:00:58.739><c> think</c><00:00:58.980><c> that</c><00:00:59.160><c> itself</c><00:00:59.670><c> is</c><00:00:59.910><c> a</c><00:00:59.969><c> really</c>

00:01:00.139 --> 00:01:00.149 align:start position:0%
and I think that itself is a really
 

00:01:00.149 --> 00:01:02.060 align:start position:0%
and I think that itself is a really
powerful<00:01:00.420><c> feature</c><00:01:00.870><c> instead</c><00:01:01.530><c> of</c><00:01:01.649><c> just</c><00:01:01.829><c> saving</c>

00:01:02.060 --> 00:01:02.070 align:start position:0%
powerful feature instead of just saving
 

00:01:02.070 --> 00:01:04.189 align:start position:0%
powerful feature instead of just saving
all<00:01:02.340><c> your</c><00:01:02.520><c> sequel</c><00:01:02.910><c> queries</c><00:01:03.329><c> to</c><00:01:03.930><c> your</c><00:01:04.019><c> local</c>

00:01:04.189 --> 00:01:04.199 align:start position:0%
all your sequel queries to your local
 

00:01:04.199 --> 00:01:07.010 align:start position:0%
all your sequel queries to your local
computer<00:01:04.500><c> meta</c><00:01:05.309><c> base</c><00:01:05.489><c> allows</c><00:01:05.790><c> each</c><00:01:06.360><c> member</c><00:01:06.990><c> of</c>

00:01:07.010 --> 00:01:07.020 align:start position:0%
computer meta base allows each member of
 

00:01:07.020 --> 00:01:09.530 align:start position:0%
computer meta base allows each member of
the<00:01:07.229><c> team</c><00:01:07.409><c> to</c><00:01:07.470><c> see</c><00:01:07.830><c> this</c><00:01:08.040><c> blog</c><00:01:08.340><c> of</c><00:01:08.670><c> all</c><00:01:09.390><c> the</c>

00:01:09.530 --> 00:01:09.540 align:start position:0%
the team to see this blog of all the
 

00:01:09.540 --> 00:01:10.760 align:start position:0%
the team to see this blog of all the
questions<00:01:09.900><c> that</c><00:01:10.020><c> have</c><00:01:10.200><c> been</c><00:01:10.229><c> written</c><00:01:10.500><c> and</c>

00:01:10.760 --> 00:01:10.770 align:start position:0%
questions that have been written and
 

00:01:10.770 --> 00:01:12.859 align:start position:0%
questions that have been written and
saved<00:01:11.210><c> questions</c><00:01:12.210><c> getting</c><00:01:12.450><c> saved</c><00:01:12.720><c> -</c>

00:01:12.859 --> 00:01:12.869 align:start position:0%
saved questions getting saved -
 

00:01:12.869 --> 00:01:15.170 align:start position:0%
saved questions getting saved -
dashboards<00:01:13.560><c> and</c><00:01:13.680><c> so</c><00:01:13.890><c> on</c><00:01:14.040><c> so</c><00:01:14.280><c> we're</c><00:01:14.850><c> working</c>

00:01:15.170 --> 00:01:15.180 align:start position:0%
dashboards and so on so we're working
 

00:01:15.180 --> 00:01:17.539 align:start position:0%
dashboards and so on so we're working
right<00:01:15.330><c> now</c><00:01:15.570><c> I</c><00:01:15.960><c> think</c><00:01:16.140><c> with</c><00:01:16.799><c> this</c><00:01:17.040><c> one</c><00:01:17.280><c> all</c>

00:01:17.539 --> 00:01:17.549 align:start position:0%
right now I think with this one all
 

00:01:17.549 --> 00:01:20.120 align:start position:0%
right now I think with this one all
driving<00:01:17.909><c> test</c><00:01:18.360><c> questions</c><00:01:18.960><c> let's</c><00:01:19.590><c> click</c><00:01:19.950><c> on</c>

00:01:20.120 --> 00:01:20.130 align:start position:0%
driving test questions let's click on
 

00:01:20.130 --> 00:01:22.700 align:start position:0%
driving test questions let's click on
into<00:01:20.520><c> it</c><00:01:20.670><c> and</c><00:01:21.030><c> we're</c><00:01:22.020><c> gonna</c><00:01:22.140><c> see</c><00:01:22.229><c> that</c><00:01:22.530><c> this</c>

00:01:22.700 --> 00:01:22.710 align:start position:0%
into it and we're gonna see that this
 

00:01:22.710 --> 00:01:24.859 align:start position:0%
into it and we're gonna see that this
specific<00:01:23.490><c> question</c><00:01:23.670><c> every</c><00:01:24.450><c> question</c>

00:01:24.859 --> 00:01:24.869 align:start position:0%
specific question every question
 

00:01:24.869 --> 00:01:27.679 align:start position:0%
specific question every question
actually<00:01:25.049><c> has</c><00:01:25.590><c> an</c><00:01:25.770><c> ID</c><00:01:25.970><c> you</c><00:01:26.970><c> I've</c><00:01:27.240><c> written</c><00:01:27.450><c> it</c>

00:01:27.679 --> 00:01:27.689 align:start position:0%
actually has an ID you I've written it
 

00:01:27.689 --> 00:01:30.319 align:start position:0%
actually has an ID you I've written it
in<00:01:27.840><c> sequel</c><00:01:28.350><c> and</c><00:01:28.530><c> we</c><00:01:29.100><c> have</c><00:01:29.280><c> a</c><00:01:29.310><c> parameter</c><00:01:30.119><c> in</c>

00:01:30.319 --> 00:01:30.329 align:start position:0%
in sequel and we have a parameter in
 

00:01:30.329 --> 00:01:32.510 align:start position:0%
in sequel and we have a parameter in
here<00:01:30.750><c> the</c><00:01:31.470><c> parameter</c><00:01:31.950><c> I'll</c><00:01:32.070><c> make</c><00:01:32.220><c> a</c><00:01:32.250><c> little</c>

00:01:32.510 --> 00:01:32.520 align:start position:0%
here the parameter I'll make a little
 

00:01:32.520 --> 00:01:36.560 align:start position:0%
here the parameter I'll make a little
bigger<00:01:33.079><c> it's</c><00:01:34.079><c> the</c><00:01:34.439><c> access</c><00:01:34.979><c> code</c><00:01:35.329><c> okay</c><00:01:36.329><c> so</c><00:01:36.390><c> the</c>

00:01:36.560 --> 00:01:36.570 align:start position:0%
bigger it's the access code okay so the
 

00:01:36.570 --> 00:01:39.469 align:start position:0%
bigger it's the access code okay so the
access<00:01:36.960><c> code</c><00:01:37.170><c> is</c><00:01:37.290><c> what's</c><00:01:37.530><c> over</c><00:01:37.950><c> here</c><00:01:38.310><c> is</c><00:01:38.549><c> the</c>

00:01:39.469 --> 00:01:39.479 align:start position:0%
access code is what's over here is the
 

00:01:39.479 --> 00:01:41.690 align:start position:0%
access code is what's over here is the
filter<00:01:39.840><c> and</c><00:01:40.170><c> I've</c><00:01:40.710><c> already</c><00:01:41.070><c> defined</c><00:01:41.310><c> the</c>

00:01:41.690 --> 00:01:41.700 align:start position:0%
filter and I've already defined the
 

00:01:41.700 --> 00:01:44.120 align:start position:0%
filter and I've already defined the
filter<00:01:42.210><c> as</c><00:01:42.360><c> well</c><00:01:42.659><c> I</c><00:01:42.689><c> can</c><00:01:43.170><c> show</c><00:01:43.500><c> you</c><00:01:43.560><c> that</c><00:01:43.649><c> here</c>

00:01:44.120 --> 00:01:44.130 align:start position:0%
filter as well I can show you that here
 

00:01:44.130 --> 00:01:46.310 align:start position:0%
filter as well I can show you that here
this<00:01:44.310><c> is</c><00:01:44.490><c> the</c><00:01:44.610><c> variables</c><00:01:45.210><c> the</c><00:01:45.840><c> filter</c><00:01:46.200><c> is</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
this is the variables the filter is
 

00:01:46.320 --> 00:01:48.920 align:start position:0%
this is the variables the filter is
defined<00:01:46.740><c> as</c><00:01:46.890><c> a</c><00:01:46.920><c> number</c><00:01:47.009><c> and</c><00:01:47.720><c> every</c><00:01:48.720><c> time</c><00:01:48.899><c> that</c>

00:01:48.920 --> 00:01:48.930 align:start position:0%
defined as a number and every time that
 

00:01:48.930 --> 00:01:51.889 align:start position:0%
defined as a number and every time that
I<00:01:49.110><c> put</c><00:01:49.409><c> a</c><00:01:49.649><c> section</c><00:01:50.490><c> here</c><00:01:50.880><c> it'll</c><00:01:51.329><c> show</c><00:01:51.689><c> me</c>

00:01:51.889 --> 00:01:51.899 align:start position:0%
I put a section here it'll show me
 

00:01:51.899 --> 00:01:55.380 align:start position:0%
I put a section here it'll show me
different<00:01:52.259><c> results</c>

00:01:55.380 --> 00:01:55.390 align:start position:0%
 
 

00:01:55.390 --> 00:01:57.450 align:start position:0%
 
okay<00:01:56.140><c> perfect</c><00:01:56.590><c> and</c><00:01:56.740><c> as</c><00:01:56.830><c> you</c><00:01:57.010><c> can</c><00:01:57.130><c> see</c><00:01:57.190><c> over</c>

00:01:57.450 --> 00:01:57.460 align:start position:0%
okay perfect and as you can see over
 

00:01:57.460 --> 00:01:58.980 align:start position:0%
okay perfect and as you can see over
here<00:01:57.520><c> this</c><00:01:57.700><c> is</c><00:01:57.760><c> all</c><00:01:58.000><c> the</c><00:01:58.030><c> questions</c><00:01:58.690><c> that</c><00:01:58.840><c> are</c>

00:01:58.980 --> 00:01:58.990 align:start position:0%
here this is all the questions that are
 

00:01:58.990 --> 00:02:02.310 align:start position:0%
here this is all the questions that are
in<00:01:59.340><c> section</c><00:02:00.340><c> number</c><00:02:00.880><c> one</c><00:02:01.210><c> so</c><00:02:02.050><c> we</c><00:02:02.140><c> go</c><00:02:02.290><c> to</c>

00:02:02.310 --> 00:02:02.320 align:start position:0%
in section number one so we go to
 

00:02:02.320 --> 00:02:05.220 align:start position:0%
in section number one so we go to
section<00:02:02.860><c> section</c><00:02:03.670><c> as</c><00:02:03.880><c> you</c><00:02:04.090><c> can</c><00:02:04.240><c> see</c><00:02:04.450><c> section</c>

00:02:05.220 --> 00:02:05.230 align:start position:0%
section section as you can see section
 

00:02:05.230 --> 00:02:07.470 align:start position:0%
section section as you can see section
and<00:02:05.530><c> that's</c><00:02:05.950><c> great</c><00:02:06.220><c> what</c><00:02:06.940><c> you</c><00:02:07.030><c> can</c><00:02:07.210><c> do</c><00:02:07.330><c> with</c>

00:02:07.470 --> 00:02:07.480 align:start position:0%
and that's great what you can do with
 

00:02:07.480 --> 00:02:09.240 align:start position:0%
and that's great what you can do with
this<00:02:07.600><c> data</c><00:02:07.930><c> you</c><00:02:08.259><c> can</c><00:02:08.410><c> automatically</c><00:02:08.950><c> download</c>

00:02:09.240 --> 00:02:09.250 align:start position:0%
this data you can automatically download
 

00:02:09.250 --> 00:02:13.230 align:start position:0%
this data you can automatically download
it<00:02:09.810><c> or</c><00:02:10.810><c> you</c><00:02:11.290><c> can</c><00:02:11.980><c> share</c><00:02:12.490><c> it</c><00:02:12.730><c> with</c><00:02:13.000><c> somebody</c>

00:02:13.230 --> 00:02:13.240 align:start position:0%
it or you can share it with somebody
 

00:02:13.240 --> 00:02:16.920 align:start position:0%
it or you can share it with somebody
else<00:02:13.450><c> so</c><00:02:13.780><c> I've</c><00:02:14.110><c> enabled</c><00:02:15.300><c> public</c><00:02:16.300><c> sharing</c><00:02:16.570><c> and</c>

00:02:16.920 --> 00:02:16.930 align:start position:0%
else so I've enabled public sharing and
 

00:02:16.930 --> 00:02:19.440 align:start position:0%
else so I've enabled public sharing and
I<00:02:16.960><c> have</c><00:02:17.200><c> the</c><00:02:17.380><c> link</c><00:02:17.650><c> here</c><00:02:17.950><c> and</c><00:02:18.130><c> this</c><00:02:18.880><c> link</c><00:02:19.210><c> has</c>

00:02:19.440 --> 00:02:19.450 align:start position:0%
I have the link here and this link has
 

00:02:19.450 --> 00:02:26.190 align:start position:0%
I have the link here and this link has
been<00:02:20.160><c> moved</c><00:02:21.160><c> over</c><00:02:21.670><c> here</c><00:02:21.700><c> to</c><00:02:23.610><c> -</c><00:02:25.050><c> -</c><00:02:26.050><c> this</c>

00:02:26.190 --> 00:02:26.200 align:start position:0%
been moved over here to - - this
 

00:02:26.200 --> 00:02:28.200 align:start position:0%
been moved over here to - - this
projects<00:02:26.920><c> repo</c><00:02:27.310><c> which</c><00:02:27.490><c> is</c><00:02:27.640><c> scraping</c><00:02:28.120><c> the</c>

00:02:28.200 --> 00:02:28.210 align:start position:0%
projects repo which is scraping the
 

00:02:28.210 --> 00:02:30.420 align:start position:0%
projects repo which is scraping the
driving<00:02:28.630><c> questions</c><00:02:29.170><c> of</c><00:02:29.440><c> the</c><00:02:29.560><c> Israeli</c><00:02:29.980><c> driving</c>

00:02:30.420 --> 00:02:30.430 align:start position:0%
driving questions of the Israeli driving
 

00:02:30.430 --> 00:02:33.030 align:start position:0%
driving questions of the Israeli driving
test<00:02:30.700><c> and</c><00:02:31.230><c> you</c><00:02:32.230><c> can</c><00:02:32.380><c> read</c><00:02:32.590><c> more</c><00:02:32.740><c> about</c><00:02:32.860><c> that</c>

00:02:33.030 --> 00:02:33.040 align:start position:0%
test and you can read more about that
 

00:02:33.040 --> 00:02:34.260 align:start position:0%
test and you can read more about that
I'm<00:02:33.340><c> going</c><00:02:33.459><c> to</c><00:02:33.550><c> make</c><00:02:33.670><c> a</c><00:02:33.700><c> different</c><00:02:34.120><c> tutorial</c>

00:02:34.260 --> 00:02:34.270 align:start position:0%
I'm going to make a different tutorial
 

00:02:34.270 --> 00:02:36.960 align:start position:0%
I'm going to make a different tutorial
series<00:02:34.900><c> all</c><00:02:35.260><c> about</c><00:02:35.470><c> how</c><00:02:35.620><c> to</c><00:02:35.680><c> cold</c><00:02:35.980><c> data</c><00:02:36.340><c> using</c>

00:02:36.960 --> 00:02:36.970 align:start position:0%
series all about how to cold data using
 

00:02:36.970 --> 00:02:40.020 align:start position:0%
series all about how to cold data using
webdriver<00:02:37.209><c> i/o</c><00:02:37.720><c> and</c><00:02:38.170><c> my</c><00:02:38.500><c> sequel</c><00:02:38.890><c> database</c><00:02:39.340><c> but</c>

00:02:40.020 --> 00:02:40.030 align:start position:0%
webdriver i/o and my sequel database but
 

00:02:40.030 --> 00:02:41.340 align:start position:0%
webdriver i/o and my sequel database but
essentially<00:02:40.360><c> we've</c><00:02:40.720><c> taken</c><00:02:41.080><c> all</c><00:02:41.320><c> the</c>

00:02:41.340 --> 00:02:41.350 align:start position:0%
essentially we've taken all the
 

00:02:41.350 --> 00:02:43.770 align:start position:0%
essentially we've taken all the
questions<00:02:41.890><c> and</c><00:02:42.130><c> the</c><00:02:42.400><c> answers</c><00:02:42.430><c> and</c><00:02:43.090><c> we</c><00:02:43.570><c> put</c>

00:02:43.770 --> 00:02:43.780 align:start position:0%
questions and the answers and we put
 

00:02:43.780 --> 00:02:46.110 align:start position:0%
questions and the answers and we put
them<00:02:43.870><c> into</c><00:02:44.050><c> my</c><00:02:44.200><c> sequel</c><00:02:44.770><c> database</c><00:02:45.250><c> we've</c><00:02:45.850><c> taken</c>

00:02:46.110 --> 00:02:46.120 align:start position:0%
them into my sequel database we've taken
 

00:02:46.120 --> 00:02:49.020 align:start position:0%
them into my sequel database we've taken
that<00:02:46.390><c> database</c><00:02:46.780><c> we've</c><00:02:47.200><c> added</c><00:02:47.440><c> it</c><00:02:47.890><c> to</c><00:02:48.130><c> meta</c>

00:02:49.020 --> 00:02:49.030 align:start position:0%
that database we've added it to meta
 

00:02:49.030 --> 00:02:51.720 align:start position:0%
that database we've added it to meta
Base<00:02:49.300><c> the</c><00:02:49.660><c> CMS</c><00:02:50.140><c> dashboard</c><00:02:50.770><c> in</c><00:02:51.010><c> the</c><00:02:51.280><c> admin</c>

00:02:51.720 --> 00:02:51.730 align:start position:0%
Base the CMS dashboard in the admin
 

00:02:51.730 --> 00:02:53.940 align:start position:0%
Base the CMS dashboard in the admin
section<00:02:52.000><c> and</c><00:02:52.540><c> I'll</c><00:02:53.020><c> show</c><00:02:53.200><c> you</c><00:02:53.230><c> how</c><00:02:53.440><c> to</c><00:02:53.470><c> do</c><00:02:53.709><c> that</c>

00:02:53.940 --> 00:02:53.950 align:start position:0%
section and I'll show you how to do that
 

00:02:53.950 --> 00:02:58.170 align:start position:0%
section and I'll show you how to do that
and<00:02:54.220><c> add</c><00:02:54.850><c> a</c><00:02:54.880><c> new</c><00:02:55.180><c> data</c><00:02:55.510><c> source</c><00:02:55.840><c> the</c><00:02:56.910><c> via</c><00:02:57.910><c> meta</c>

00:02:58.170 --> 00:02:58.180 align:start position:0%
and add a new data source the via meta
 

00:02:58.180 --> 00:03:00.180 align:start position:0%
and add a new data source the via meta
base<00:02:58.420><c> because</c><00:02:58.840><c> that's</c><00:02:59.020><c> also</c><00:02:59.620><c> an</c><00:02:59.739><c> important</c>

00:03:00.180 --> 00:03:00.190 align:start position:0%
base because that's also an important
 

00:03:00.190 --> 00:03:02.670 align:start position:0%
base because that's also an important
few<00:03:00.400><c> requisite</c><00:03:00.940><c> for</c><00:03:01.690><c> getting</c><00:03:01.989><c> for</c><00:03:02.320><c> working</c>

00:03:02.670 --> 00:03:02.680 align:start position:0%
few requisite for getting for working
 

00:03:02.680 --> 00:03:05.759 align:start position:0%
few requisite for getting for working
with<00:03:02.800><c> a</c><00:03:02.830><c> meta</c><00:03:03.130><c> based</c><00:03:03.340><c> API</c><00:03:04.110><c> well</c><00:03:05.110><c> if</c><00:03:05.530><c> you're</c>

00:03:05.759 --> 00:03:05.769 align:start position:0%
with a meta based API well if you're
 

00:03:05.769 --> 00:03:07.620 align:start position:0%
with a meta based API well if you're
watching<00:03:06.100><c> this</c><00:03:06.220><c> video</c><00:03:06.459><c> you're</c><00:03:07.120><c> probably</c><00:03:07.209><c> a</c>

00:03:07.620 --> 00:03:07.630 align:start position:0%
watching this video you're probably a
 

00:03:07.630 --> 00:03:09.650 align:start position:0%
watching this video you're probably a
little<00:03:07.660><c> more</c><00:03:08.019><c> advanced</c><00:03:08.650><c> so</c><00:03:09.160><c> let's</c><00:03:09.400><c> see</c>

00:03:09.650 --> 00:03:09.660 align:start position:0%
little more advanced so let's see
 

00:03:09.660 --> 00:03:13.350 align:start position:0%
little more advanced so let's see
databases<00:03:10.660><c> and</c><00:03:11.320><c> there</c><00:03:12.250><c> it</c><00:03:12.370><c> is</c><00:03:12.400><c> Israel</c><00:03:12.970><c> driving</c>

00:03:13.350 --> 00:03:13.360 align:start position:0%
databases and there it is Israel driving
 

00:03:13.360 --> 00:03:16.140 align:start position:0%
databases and there it is Israel driving
test<00:03:13.600><c> of</c><00:03:13.870><c> CMS</c><00:03:14.410><c> that</c><00:03:14.769><c> sample</c><00:03:15.190><c> data</c><00:03:15.370><c> set</c><00:03:15.910><c> and</c>

00:03:16.140 --> 00:03:16.150 align:start position:0%
test of CMS that sample data set and
 

00:03:16.150 --> 00:03:19.380 align:start position:0%
test of CMS that sample data set and
something<00:03:16.690><c> else</c><00:03:16.840><c> so</c><00:03:17.080><c> you</c><00:03:17.920><c> go</c><00:03:18.130><c> into</c><00:03:18.519><c> the</c><00:03:19.000><c> admin</c>

00:03:19.380 --> 00:03:19.390 align:start position:0%
something else so you go into the admin
 

00:03:19.390 --> 00:03:21.600 align:start position:0%
something else so you go into the admin
you<00:03:19.690><c> add</c><00:03:19.840><c> your</c><00:03:20.080><c> database</c><00:03:20.530><c> and</c><00:03:20.890><c> once</c><00:03:21.340><c> you</c><00:03:21.459><c> have</c>

00:03:21.600 --> 00:03:21.610 align:start position:0%
you add your database and once you have
 

00:03:21.610 --> 00:03:24.270 align:start position:0%
you add your database and once you have
that<00:03:21.820><c> you</c><00:03:22.030><c> can</c><00:03:22.060><c> ask</c><00:03:22.360><c> questions</c><00:03:22.480><c> and</c><00:03:23.230><c> now</c><00:03:23.980><c> I'm</c>

00:03:24.270 --> 00:03:24.280 align:start position:0%
that you can ask questions and now I'm
 

00:03:24.280 --> 00:03:27.570 align:start position:0%
that you can ask questions and now I'm
gonna<00:03:24.459><c> go</c><00:03:24.670><c> in</c><00:03:24.820><c> here</c><00:03:25.209><c> and</c><00:03:25.800><c> this</c><00:03:26.800><c> is</c><00:03:26.950><c> what</c><00:03:27.130><c> I</c><00:03:27.160><c> get</c>

00:03:27.570 --> 00:03:27.580 align:start position:0%
gonna go in here and this is what I get
 

00:03:27.580 --> 00:03:30.479 align:start position:0%
gonna go in here and this is what I get
okay<00:03:28.120><c> this</c><00:03:28.360><c> is</c><00:03:28.510><c> my</c><00:03:28.660><c> public</c><00:03:29.110><c> card</c><00:03:29.410><c> we'll</c><00:03:30.370><c> look</c>

00:03:30.479 --> 00:03:30.489 align:start position:0%
okay this is my public card we'll look
 

00:03:30.489 --> 00:03:38.520 align:start position:0%
okay this is my public card we'll look
at<00:03:30.730><c> it</c><00:03:30.940><c> in</c><00:03:31.150><c> our</c><00:03:31.600><c> viewer</c>

00:03:38.520 --> 00:03:38.530 align:start position:0%
 
 

00:03:38.530 --> 00:03:43.950 align:start position:0%
 
when<00:03:39.070><c> I</c><00:03:39.100><c> really</c><00:03:39.550><c> dissected</c><00:03:42.090><c> okay</c><00:03:43.090><c> perfect</c>

00:03:43.950 --> 00:03:43.960 align:start position:0%
when I really dissected okay perfect
 

00:03:43.960 --> 00:03:47.370 align:start position:0%
when I really dissected okay perfect
so<00:03:44.650><c> this</c><00:03:45.070><c> is</c><00:03:45.220><c> what</c><00:03:45.370><c> the</c><00:03:45.490><c> card</c><00:03:45.760><c> looks</c><00:03:45.850><c> like</c>

00:03:47.370 --> 00:03:47.380 align:start position:0%
so this is what the card looks like
 

00:03:47.380 --> 00:03:51.450 align:start position:0%
so this is what the card looks like
I've<00:03:47.890><c> removed</c><00:03:48.880><c> you</c><00:03:49.750><c> could</c><00:03:49.930><c> eat</c><00:03:50.350><c> you</c><00:03:51.340><c> could</c>

00:03:51.450 --> 00:03:51.460 align:start position:0%
I've removed you could eat you could
 

00:03:51.460 --> 00:03:53.340 align:start position:0%
I've removed you could eat you could
probably<00:03:51.730><c> add</c><00:03:52.060><c> more</c><00:03:52.390><c> than</c><00:03:52.600><c> one</c><00:03:52.780><c> parameter</c>

00:03:53.340 --> 00:03:53.350 align:start position:0%
probably add more than one parameter
 

00:03:53.350 --> 00:03:56.100 align:start position:0%
probably add more than one parameter
into<00:03:54.250><c> the</c><00:03:54.370><c> search</c><00:03:54.610><c> you</c><00:03:54.640><c> can</c><00:03:54.970><c> export</c><00:03:55.570><c> you</c><00:03:55.959><c> can</c>

00:03:56.100 --> 00:03:56.110 align:start position:0%
into the search you can export you can
 

00:03:56.110 --> 00:03:58.890 align:start position:0%
into the search you can export you can
just<00:03:56.320><c> get</c><00:03:56.470><c> straight</c><00:03:56.920><c> up</c><00:03:57.100><c> Jayceon</c><00:03:57.730><c> Jess</c><00:03:58.630><c> are</c>

00:03:58.890 --> 00:03:58.900 align:start position:0%
just get straight up Jayceon Jess are
 

00:03:58.900 --> 00:04:01.860 align:start position:0%
just get straight up Jayceon Jess are
you<00:03:59.020><c> straight</c><00:03:59.350><c> up</c><00:03:59.380><c> JSON</c><00:04:00.300><c> okay</c><00:04:01.300><c> there</c><00:04:01.720><c> you</c><00:04:01.750><c> go</c>

00:04:01.860 --> 00:04:01.870 align:start position:0%
you straight up JSON okay there you go
 

00:04:01.870 --> 00:04:04.410 align:start position:0%
you straight up JSON okay there you go
it<00:04:02.290><c> beyond</c><00:04:02.650><c> JSON</c><00:04:03.010><c> you</c><00:04:03.100><c> can</c><00:04:03.250><c> export</c><00:04:03.700><c> that</c><00:04:03.940><c> JSON</c>

00:04:04.410 --> 00:04:04.420 align:start position:0%
it beyond JSON you can export that JSON
 

00:04:04.420 --> 00:04:06.240 align:start position:0%
it beyond JSON you can export that JSON
when<00:04:04.600><c> you</c><00:04:04.690><c> automatically</c><00:04:05.200><c> download</c><00:04:05.770><c> it</c><00:04:05.920><c> the</c>

00:04:06.240 --> 00:04:06.250 align:start position:0%
when you automatically download it the
 

00:04:06.250 --> 00:04:08.850 align:start position:0%
when you automatically download it the
moment<00:04:06.610><c> that</c><00:04:06.790><c> that</c><00:04:07.420><c> somebody</c><00:04:08.020><c> hits</c><00:04:08.260><c> this</c><00:04:08.500><c> link</c>

00:04:08.850 --> 00:04:08.860 align:start position:0%
moment that that somebody hits this link
 

00:04:08.860 --> 00:04:11.790 align:start position:0%
moment that that somebody hits this link
or<00:04:09.430><c> the</c><00:04:10.240><c> it'll</c><00:04:10.810><c> download</c><00:04:10.959><c> to</c><00:04:11.290><c> their</c><00:04:11.410><c> computer</c>

00:04:11.790 --> 00:04:11.800 align:start position:0%
or the it'll download to their computer
 

00:04:11.800 --> 00:04:13.830 align:start position:0%
or the it'll download to their computer
or<00:04:11.950><c> it'll</c><00:04:12.160><c> automatically</c><00:04:12.640><c> export</c><00:04:13.120><c> the</c><00:04:13.239><c> CSV</c>

00:04:13.830 --> 00:04:13.840 align:start position:0%
or it'll automatically export the CSV
 

00:04:13.840 --> 00:04:16.500 align:start position:0%
or it'll automatically export the CSV
with<00:04:14.170><c> this</c><00:04:14.380><c> link</c><00:04:14.680><c> over</c><00:04:14.890><c> here</c><00:04:15.370><c> and</c><00:04:15.610><c> you'll</c><00:04:16.329><c> see</c>

00:04:16.500 --> 00:04:16.510 align:start position:0%
with this link over here and you'll see
 

00:04:16.510 --> 00:04:21.330 align:start position:0%
with this link over here and you'll see
that<00:04:16.630><c> I've</c><00:04:16.750><c> taken</c><00:04:17.019><c> this</c><00:04:19.560><c> you'll</c><00:04:20.560><c> see</c><00:04:20.650><c> that</c><00:04:20.859><c> at</c>

00:04:21.330 --> 00:04:21.340 align:start position:0%
that I've taken this you'll see that at
 

00:04:21.340 --> 00:04:24.810 align:start position:0%
that I've taken this you'll see that at
the<00:04:21.669><c> when</c><00:04:22.540><c> you're</c><00:04:22.690><c> trying</c><00:04:22.960><c> to</c><00:04:23.110><c> save</c><00:04:23.580><c> so</c><00:04:24.580><c> when</c>

00:04:24.810 --> 00:04:24.820 align:start position:0%
the when you're trying to save so when
 

00:04:24.820 --> 00:04:28.470 align:start position:0%
the when you're trying to save so when
you're<00:04:24.970><c> trying</c><00:04:25.210><c> to</c><00:04:25.330><c> share</c><00:04:26.520><c> to</c><00:04:27.520><c> share</c><00:04:27.790><c> a</c>

00:04:28.470 --> 00:04:28.480 align:start position:0%
you're trying to share to share a
 

00:04:28.480 --> 00:04:31.710 align:start position:0%
you're trying to share to share a
question<00:04:29.320><c> you're</c><00:04:30.190><c> gonna</c><00:04:30.400><c> get</c><00:04:30.880><c> something</c><00:04:31.240><c> that</c>

00:04:31.710 --> 00:04:31.720 align:start position:0%
question you're gonna get something that
 

00:04:31.720 --> 00:04:32.580 align:start position:0%
question you're gonna get something that
looks<00:04:31.900><c> like</c><00:04:31.960><c> this</c>

00:04:32.580 --> 00:04:32.590 align:start position:0%
looks like this
 

00:04:32.590 --> 00:04:34.620 align:start position:0%
looks like this
okay<00:04:32.890><c> I'm</c><00:04:32.950><c> gonna</c><00:04:33.130><c> copy</c><00:04:33.460><c> that</c><00:04:33.580><c> and</c><00:04:33.970><c> I</c><00:04:34.180><c> paste</c><00:04:34.450><c> it</c>

00:04:34.620 --> 00:04:34.630 align:start position:0%
okay I'm gonna copy that and I paste it
 

00:04:34.630 --> 00:04:37.260 align:start position:0%
okay I'm gonna copy that and I paste it
in<00:04:34.780><c> there</c><00:04:34.930><c> and</c><00:04:35.200><c> you</c><00:04:35.740><c> have</c><00:04:35.770><c> the</c><00:04:36.100><c> card</c><00:04:36.400><c> ID</c><00:04:36.669><c> which</c>

00:04:37.260 --> 00:04:37.270 align:start position:0%
in there and you have the card ID which
 

00:04:37.270 --> 00:04:40.350 align:start position:0%
in there and you have the card ID which
is<00:04:37.450><c> also</c><00:04:37.660><c> the</c><00:04:37.780><c> question</c><00:04:38.110><c> ID</c><00:04:38.530><c> and</c><00:04:38.919><c> I</c><00:04:39.910><c> take</c><00:04:40.120><c> that</c>

00:04:40.350 --> 00:04:40.360 align:start position:0%
is also the question ID and I take that
 

00:04:40.360 --> 00:04:43.920 align:start position:0%
is also the question ID and I take that
ID<00:04:40.720><c> and</c><00:04:41.260><c> I</c><00:04:41.350><c> put</c><00:04:41.560><c> it</c><00:04:41.680><c> right</c><00:04:41.950><c> in</c><00:04:42.280><c> here</c><00:04:42.900><c> okay</c><00:04:43.900><c> so</c>

00:04:43.920 --> 00:04:43.930 align:start position:0%
ID and I put it right in here okay so
 

00:04:43.930 --> 00:04:46.440 align:start position:0%
ID and I put it right in here okay so
you're<00:04:44.169><c> gonna</c><00:04:44.260><c> put</c><00:04:44.530><c> that</c><00:04:44.650><c> in</c><00:04:44.860><c> here</c><00:04:45.220><c> then</c><00:04:46.210><c> you</c>

00:04:46.440 --> 00:04:46.450 align:start position:0%
you're gonna put that in here then you
 

00:04:46.450 --> 00:04:49.170 align:start position:0%
you're gonna put that in here then you
choose<00:04:46.780><c> you</c><00:04:47.500><c> say</c><00:04:47.979><c> slash</c><00:04:48.280><c> query</c><00:04:48.700><c> that's</c><00:04:49.000><c> the</c>

00:04:49.170 --> 00:04:49.180 align:start position:0%
choose you say slash query that's the
 

00:04:49.180 --> 00:04:54.390 align:start position:0%
choose you say slash query that's the
same<00:04:49.450><c> across</c><00:04:49.780><c> all</c><00:04:50.080><c> three</c><00:04:50.410><c> queries</c><00:04:50.940><c> but</c><00:04:53.400><c> bigger</c>

00:04:54.390 --> 00:04:54.400 align:start position:0%
same across all three queries but bigger
 

00:04:54.400 --> 00:04:57.150 align:start position:0%
same across all three queries but bigger
okay<00:04:54.669><c> you</c><00:04:55.570><c> take</c><00:04:55.780><c> the</c><00:04:55.990><c> card</c><00:04:56.290><c> you're</c><00:04:56.680><c> gonna</c><00:04:56.800><c> save</c>

00:04:57.150 --> 00:04:57.160 align:start position:0%
okay you take the card you're gonna save
 

00:04:57.160 --> 00:04:59.130 align:start position:0%
okay you take the card you're gonna save
it<00:04:57.190><c> over</c><00:04:57.610><c> there</c><00:04:57.790><c> API</c><00:04:58.240><c> slash</c><00:04:58.330><c> public</c><00:04:58.900><c> slash</c>

00:04:59.130 --> 00:04:59.140 align:start position:0%
it over there API slash public slash
 

00:04:59.140 --> 00:05:01.560 align:start position:0%
it over there API slash public slash
card<00:04:59.530><c> slash</c><00:05:00.340><c> query</c><00:05:00.700><c> and</c><00:05:01.030><c> then</c><00:05:01.120><c> if</c><00:05:01.270><c> you</c><00:05:01.330><c> want</c><00:05:01.510><c> it</c>

00:05:01.560 --> 00:05:01.570 align:start position:0%
card slash query and then if you want it
 

00:05:01.570 --> 00:05:05.010 align:start position:0%
card slash query and then if you want it
to<00:05:02.050><c> export</c><00:05:02.500><c> CSV</c><00:05:03.070><c> you</c><00:05:03.430><c> do</c><00:05:03.640><c> slash</c><00:05:04.000><c> CSV</c><00:05:04.510><c> you</c><00:05:04.810><c> want</c>

00:05:05.010 --> 00:05:05.020 align:start position:0%
to export CSV you do slash CSV you want
 

00:05:05.020 --> 00:05:07.290 align:start position:0%
to export CSV you do slash CSV you want
to<00:05:05.229><c> download</c><00:05:05.500><c> JSON</c><00:05:06.130><c> you</c><00:05:06.160><c> do</c><00:05:06.340><c> slash</c><00:05:06.580><c> JSON</c><00:05:07.090><c> you</c>

00:05:07.290 --> 00:05:07.300 align:start position:0%
to download JSON you do slash JSON you
 

00:05:07.300 --> 00:05:09.510 align:start position:0%
to download JSON you do slash JSON you
wanted<00:05:07.930><c> to</c><00:05:08.020><c> just</c><00:05:08.169><c> return</c><00:05:08.530><c> JSON</c><00:05:08.740><c> you</c><00:05:09.130><c> just</c><00:05:09.340><c> do</c>

00:05:09.510 --> 00:05:09.520 align:start position:0%
wanted to just return JSON you just do
 

00:05:09.520 --> 00:05:12.000 align:start position:0%
wanted to just return JSON you just do
that<00:05:09.700><c> and</c><00:05:09.940><c> we're</c><00:05:10.780><c> gonna</c><00:05:10.900><c> remove</c><00:05:11.290><c> this</c><00:05:11.500><c> second</c>

00:05:12.000 --> 00:05:12.010 align:start position:0%
that and we're gonna remove this second
 

00:05:12.010 --> 00:05:20.180 align:start position:0%
that and we're gonna remove this second
parameter<00:05:12.280><c> here</c><00:05:17.550><c> here</c><00:05:18.550><c> and</c><00:05:18.820><c> here</c>

00:05:20.180 --> 00:05:20.190 align:start position:0%
parameter here here and here
 

00:05:20.190 --> 00:05:22.290 align:start position:0%
parameter here here and here
because<00:05:21.190><c> we</c><00:05:21.370><c> don't</c><00:05:21.520><c> need</c><00:05:21.640><c> a</c><00:05:21.700><c> second</c><00:05:22.120><c> parameter</c>

00:05:22.290 --> 00:05:22.300 align:start position:0%
because we don't need a second parameter
 

00:05:22.300 --> 00:05:30.439 align:start position:0%
because we don't need a second parameter
in<00:05:22.690><c> this</c><00:05:22.810><c> case</c><00:05:23.880><c> and</c>

00:05:30.439 --> 00:05:30.449 align:start position:0%
 
 

00:05:30.449 --> 00:05:35.489 align:start position:0%
 
here<00:05:31.949><c> perfect</c><00:05:32.949><c> so</c><00:05:33.460><c> here's</c><00:05:34.240><c> the</c><00:05:34.419><c> export</c><00:05:34.869><c> CSV</c>

00:05:35.489 --> 00:05:35.499 align:start position:0%
here perfect so here's the export CSV
 

00:05:35.499 --> 00:05:37.559 align:start position:0%
here perfect so here's the export CSV
and<00:05:35.679><c> let's</c><00:05:35.979><c> hit</c><00:05:36.369><c> that</c><00:05:36.399><c> up</c><00:05:36.759><c> all</c><00:05:36.999><c> we</c><00:05:37.209><c> need</c><00:05:37.330><c> to</c><00:05:37.419><c> do</c>

00:05:37.559 --> 00:05:37.569 align:start position:0%
and let's hit that up all we need to do
 

00:05:37.569 --> 00:05:40.859 align:start position:0%
and let's hit that up all we need to do
is<00:05:37.719><c> just</c><00:05:37.929><c> take</c><00:05:38.259><c> this</c><00:05:39.219><c> URL</c><00:05:40.089><c> and</c><00:05:40.449><c> share</c><00:05:40.719><c> it</c><00:05:40.839><c> with</c>

00:05:40.859 --> 00:05:40.869 align:start position:0%
is just take this URL and share it with
 

00:05:40.869 --> 00:05:43.699 align:start position:0%
is just take this URL and share it with
anybody<00:05:41.289><c> on</c><00:05:41.529><c> the</c><00:05:41.770><c> web</c><00:05:41.919><c> it's</c><00:05:42.279><c> a</c><00:05:42.369><c> public</c><00:05:42.729><c> link</c>

00:05:43.699 --> 00:05:43.709 align:start position:0%
anybody on the web it's a public link
 

00:05:43.709 --> 00:05:47.850 align:start position:0%
anybody on the web it's a public link
okay<00:05:44.709><c> and</c><00:05:45.599><c> let's</c><00:05:46.599><c> do</c><00:05:46.749><c> it</c><00:05:46.779><c> for</c><00:05:47.229><c> in</c><00:05:47.379><c> incognito</c>

00:05:47.850 --> 00:05:47.860 align:start position:0%
okay and let's do it for in incognito
 

00:05:47.860 --> 00:05:55.169 align:start position:0%
okay and let's do it for in incognito
just<00:05:48.610><c> to</c><00:05:48.789><c> prove</c><00:05:48.999><c> the</c><00:05:49.179><c> case</c><00:05:52.529><c> okay</c><00:05:53.559><c> and</c><00:05:54.520><c> the</c>

00:05:55.169 --> 00:05:55.179 align:start position:0%
just to prove the case okay and the
 

00:05:55.179 --> 00:05:56.579 align:start position:0%
just to prove the case okay and the
moment<00:05:55.479><c> that</c><00:05:55.509><c> we</c><00:05:55.839><c> pressed</c><00:05:56.139><c> it</c><00:05:56.289><c> it</c><00:05:56.469><c> immediately</c>

00:05:56.579 --> 00:05:56.589 align:start position:0%
moment that we pressed it it immediately
 

00:05:56.589 --> 00:05:59.249 align:start position:0%
moment that we pressed it it immediately
downloaded<00:05:57.580><c> this</c><00:05:57.699><c> CSV</c><00:05:58.330><c> file</c><00:05:58.569><c> for</c><00:05:58.929><c> us</c><00:05:59.050><c> and</c>

00:05:59.249 --> 00:05:59.259 align:start position:0%
downloaded this CSV file for us and
 

00:05:59.259 --> 00:06:01.859 align:start position:0%
downloaded this CSV file for us and
that's<00:05:59.379><c> really</c><00:05:59.499><c> nice</c><00:05:59.919><c> and</c><00:06:00.569><c> let's</c><00:06:01.569><c> see</c><00:06:01.719><c> what</c>

00:06:01.859 --> 00:06:01.869 align:start position:0%
that's really nice and let's see what
 

00:06:01.869 --> 00:06:04.439 align:start position:0%
that's really nice and let's see what
happens<00:06:02.139><c> with</c><00:06:02.259><c> that</c><00:06:02.409><c> CSV</c><00:06:02.740><c> by</c><00:06:03.159><c> the</c><00:06:03.219><c> way</c><00:06:03.520><c> you'll</c>

00:06:04.439 --> 00:06:04.449 align:start position:0%
happens with that CSV by the way you'll
 

00:06:04.449 --> 00:06:05.939 align:start position:0%
happens with that CSV by the way you'll
see<00:06:04.629><c> that</c><00:06:04.749><c> in</c><00:06:04.899><c> this</c><00:06:05.050><c> case</c><00:06:05.289><c> the</c><00:06:05.589><c> case</c><00:06:05.770><c> the</c>

00:06:05.939 --> 00:06:05.949 align:start position:0%
see that in this case the case the
 

00:06:05.949 --> 00:06:08.579 align:start position:0%
see that in this case the case the
access<00:06:06.369><c> code</c><00:06:06.610><c> was</c><00:06:06.849><c> for</c><00:06:07.180><c> that</c><00:06:07.779><c> if</c><00:06:08.020><c> we</c><00:06:08.139><c> go</c><00:06:08.289><c> back</c>

00:06:08.579 --> 00:06:08.589 align:start position:0%
access code was for that if we go back
 

00:06:08.589 --> 00:06:11.609 align:start position:0%
access code was for that if we go back
to<00:06:08.860><c> the</c><00:06:09.009><c> to</c><00:06:09.339><c> the</c><00:06:09.399><c> query</c><00:06:09.819><c> and</c><00:06:10.300><c> we</c><00:06:10.899><c> said</c><00:06:11.110><c> section</c>

00:06:11.609 --> 00:06:11.619 align:start position:0%
to the to the query and we said section
 

00:06:11.619 --> 00:06:14.579 align:start position:0%
to the to the query and we said section
equals<00:06:11.860><c> access</c><00:06:12.610><c> code</c><00:06:12.909><c> and</c><00:06:13.180><c> that's</c><00:06:13.389><c> why</c><00:06:13.659><c> we're</c>

00:06:14.579 --> 00:06:14.589 align:start position:0%
equals access code and that's why we're
 

00:06:14.589 --> 00:06:18.359 align:start position:0%
equals access code and that's why we're
only<00:06:14.830><c> taking</c><00:06:15.930><c> all</c><00:06:16.930><c> the</c><00:06:17.259><c> quick</c><00:06:17.589><c> questions</c><00:06:18.129><c> from</c>

00:06:18.359 --> 00:06:18.369 align:start position:0%
only taking all the quick questions from
 

00:06:18.369 --> 00:06:20.909 align:start position:0%
only taking all the quick questions from
section<00:06:18.789><c> four</c><00:06:19.149><c> okay</c><00:06:19.719><c> so</c><00:06:19.839><c> that's</c><00:06:20.019><c> an</c><00:06:20.139><c> example</c><00:06:20.439><c> I</c>

00:06:20.909 --> 00:06:20.919 align:start position:0%
section four okay so that's an example I
 

00:06:20.919 --> 00:06:22.980 align:start position:0%
section four okay so that's an example I
could<00:06:20.979><c> do</c><00:06:21.249><c> the</c><00:06:21.399><c> same</c><00:06:21.610><c> thing</c><00:06:22.029><c> all</c><00:06:22.539><c> I</c><00:06:22.689><c> needed</c><00:06:22.899><c> to</c>

00:06:22.980 --> 00:06:22.990 align:start position:0%
could do the same thing all I needed to
 

00:06:22.990 --> 00:06:27.839 align:start position:0%
could do the same thing all I needed to
do<00:06:23.319><c> is</c><00:06:23.619><c> change</c><00:06:24.389><c> one</c><00:06:26.369><c> change</c><00:06:27.369><c> one</c><00:06:27.639><c> letter</c>

00:06:27.839 --> 00:06:27.849 align:start position:0%
do is change one change one letter
 

00:06:27.849 --> 00:06:30.089 align:start position:0%
do is change one change one letter
instead<00:06:28.479><c> of</c><00:06:28.599><c> doing</c><00:06:28.839><c> Section</c><00:06:29.289><c> four</c><00:06:29.559><c> I</c><00:06:29.830><c> want</c>

00:06:30.089 --> 00:06:30.099 align:start position:0%
instead of doing Section four I want
 

00:06:30.099 --> 00:06:33.480 align:start position:0%
instead of doing Section four I want
section<00:06:30.729><c> number</c><00:06:31.119><c> two</c><00:06:31.360><c> for</c><00:06:31.539><c> example</c><00:06:32.490><c> and</c>

00:06:33.480 --> 00:06:33.490 align:start position:0%
section number two for example and
 

00:06:33.490 --> 00:06:35.279 align:start position:0%
section number two for example and
that's<00:06:33.610><c> automatically</c><00:06:34.300><c> going</c><00:06:34.449><c> to</c><00:06:34.539><c> download</c><00:06:34.749><c> a</c>

00:06:35.279 --> 00:06:35.289 align:start position:0%
that's automatically going to download a
 

00:06:35.289 --> 00:06:39.929 align:start position:0%
that's automatically going to download a
new<00:06:35.709><c> file</c><00:06:36.209><c> I'm</c><00:06:37.209><c> gonna</c><00:06:37.479><c> click</c><00:06:37.749><c> that</c><00:06:38.759><c> and</c><00:06:39.759><c> see</c>

00:06:39.929 --> 00:06:39.939 align:start position:0%
new file I'm gonna click that and see
 

00:06:39.939 --> 00:06:43.499 align:start position:0%
new file I'm gonna click that and see
what<00:06:40.119><c> happens</c><00:06:40.599><c> ooh</c><00:06:41.610><c> this</c><00:06:42.610><c> is</c><00:06:42.729><c> section</c><00:06:43.059><c> 4</c><00:06:43.330><c> the</c>

00:06:43.499 --> 00:06:43.509 align:start position:0%
what happens ooh this is section 4 the
 

00:06:43.509 --> 00:06:45.209 align:start position:0%
what happens ooh this is section 4 the
last<00:06:43.749><c> one</c><00:06:43.959><c> hi</c><00:06:44.169><c> there</c><00:06:44.379><c> you</c><00:06:44.469><c> go</c><00:06:44.589><c> this</c><00:06:44.740><c> is</c><00:06:44.889><c> section</c>

00:06:45.209 --> 00:06:45.219 align:start position:0%
last one hi there you go this is section
 

00:06:45.219 --> 00:06:49.699 align:start position:0%
last one hi there you go this is section
2<00:06:45.599><c> okay</c><00:06:46.599><c> and</c><00:06:46.839><c> as</c><00:06:47.289><c> you</c><00:06:47.349><c> can</c><00:06:47.619><c> see</c><00:06:47.680><c> they</c><00:06:48.279><c> are</c>

00:06:49.699 --> 00:06:49.709 align:start position:0%
2 okay and as you can see they are
 

00:06:49.709 --> 00:06:53.279 align:start position:0%
2 okay and as you can see they are
different<00:06:50.709><c> questions</c><00:06:51.509><c> okay</c><00:06:52.509><c> so</c><00:06:52.959><c> that</c><00:06:53.110><c> takes</c>

00:06:53.279 --> 00:06:53.289 align:start position:0%
different questions okay so that takes
 

00:06:53.289 --> 00:06:55.949 align:start position:0%
different questions okay so that takes
care<00:06:53.439><c> of</c><00:06:53.649><c> that</c><00:06:53.740><c> so</c><00:06:54.519><c> the</c><00:06:54.639><c> question</c><00:06:54.789><c> the</c><00:06:55.449><c> ID</c><00:06:55.599><c> in</c>

00:06:55.949 --> 00:06:55.959 align:start position:0%
care of that so the question the ID in
 

00:06:55.959 --> 00:06:57.989 align:start position:0%
care of that so the question the ID in
the<00:06:56.079><c> section</c><00:06:56.559><c> and</c><00:06:56.740><c> all</c><00:06:56.860><c> that</c><00:06:57.069><c> ok</c><00:06:57.669><c> so</c><00:06:57.729><c> as</c><00:06:57.939><c> you</c>

00:06:57.989 --> 00:06:57.999 align:start position:0%
the section and all that ok so as you
 

00:06:57.999 --> 00:07:01.109 align:start position:0%
the section and all that ok so as you
can<00:06:58.119><c> see</c><00:06:58.240><c> we</c><00:06:58.599><c> can</c><00:06:58.869><c> export</c><00:06:59.559><c> to</c><00:06:59.680><c> CSV</c><00:06:59.769><c> and</c><00:07:00.550><c> these</c>

00:07:01.109 --> 00:07:01.119 align:start position:0%
can see we can export to CSV and these
 

00:07:01.119 --> 00:07:04.319 align:start position:0%
can see we can export to CSV and these
are<00:07:01.300><c> I</c><00:07:01.539><c> will</c><00:07:01.689><c> be</c><00:07:01.839><c> sharing</c><00:07:02.019><c> the</c><00:07:02.319><c> links</c><00:07:03.329><c> done</c>

00:07:04.319 --> 00:07:04.329 align:start position:0%
are I will be sharing the links done
 

00:07:04.329 --> 00:07:06.420 align:start position:0%
are I will be sharing the links done
through<00:07:04.569><c> the</c><00:07:04.809><c> meta</c><00:07:04.990><c> base</c><00:07:05.199><c> API</c><00:07:05.649><c> I</c><00:07:06.009><c> hope</c><00:07:06.219><c> that</c>

00:07:06.420 --> 00:07:06.430 align:start position:0%
through the meta base API I hope that
 

00:07:06.430 --> 00:07:08.399 align:start position:0%
through the meta base API I hope that
answers<00:07:06.759><c> some</c><00:07:07.029><c> of</c><00:07:07.479><c> the</c><00:07:07.599><c> questions</c><00:07:07.659><c> here's</c><00:07:08.259><c> an</c>

00:07:08.399 --> 00:07:08.409 align:start position:0%
answers some of the questions here's an
 

00:07:08.409 --> 00:07:10.469 align:start position:0%
answers some of the questions here's an
example<00:07:08.709><c> of</c><00:07:08.979><c> what</c><00:07:09.459><c> it</c><00:07:09.579><c> looks</c><00:07:09.789><c> like</c><00:07:09.969><c> when</c>

00:07:10.469 --> 00:07:10.479 align:start position:0%
example of what it looks like when
 

00:07:10.479 --> 00:07:13.529 align:start position:0%
example of what it looks like when
you're<00:07:10.899><c> just</c><00:07:11.110><c> returning</c><00:07:11.499><c> JSON</c><00:07:12.180><c> this</c><00:07:13.180><c> is</c><00:07:13.360><c> the</c>

00:07:13.529 --> 00:07:13.539 align:start position:0%
you're just returning JSON this is the
 

00:07:13.539 --> 00:07:20.069 align:start position:0%
you're just returning JSON this is the
exact<00:07:13.959><c> same</c><00:07:14.319><c> almost</c><00:07:14.680><c> the</c><00:07:15.009><c> same</c><00:07:15.279><c> let's</c><00:07:15.639><c> see</c><00:07:19.079><c> so</c>

00:07:20.069 --> 00:07:20.079 align:start position:0%
exact same almost the same let's see so
 

00:07:20.079 --> 00:07:23.249 align:start position:0%
exact same almost the same let's see so
again<00:07:20.379><c> we</c><00:07:20.559><c> take</c><00:07:20.589><c> the</c><00:07:21.069><c> question</c><00:07:21.550><c> ID</c><00:07:21.759><c> from</c><00:07:22.449><c> from</c>

00:07:23.249 --> 00:07:23.259 align:start position:0%
again we take the question ID from from
 

00:07:23.259 --> 00:07:26.869 align:start position:0%
again we take the question ID from from
the<00:07:23.409><c> public</c><00:07:23.829><c> shared</c><00:07:24.219><c> link</c><00:07:24.639><c> if</c><00:07:25.360><c> we</c><00:07:26.019><c> go</c><00:07:26.229><c> in</c><00:07:26.559><c> here</c>

00:07:26.869 --> 00:07:26.879 align:start position:0%
the public shared link if we go in here
 

00:07:26.879 --> 00:07:29.879 align:start position:0%
the public shared link if we go in here
we<00:07:27.879><c> take</c><00:07:28.059><c> the</c><00:07:28.209><c> public</c><00:07:28.599><c> link</c><00:07:28.899><c> copy</c><00:07:29.319><c> it</c><00:07:29.499><c> and</c>

00:07:29.879 --> 00:07:29.889 align:start position:0%
we take the public link copy it and
 

00:07:29.889 --> 00:07:32.969 align:start position:0%
we take the public link copy it and
we're<00:07:30.519><c> gonna</c><00:07:30.639><c> paste</c><00:07:31.029><c> it</c><00:07:31.329><c> and</c><00:07:31.509><c> this</c><00:07:32.319><c> is</c><00:07:32.649><c> what's</c>

00:07:32.969 --> 00:07:32.979 align:start position:0%
we're gonna paste it and this is what's
 

00:07:32.979 --> 00:07:36.719 align:start position:0%
we're gonna paste it and this is what's
gonna<00:07:33.129><c> provide</c><00:07:33.639><c> you</c><00:07:34.019><c> with</c><00:07:35.019><c> your</c><00:07:35.409><c> card</c><00:07:35.740><c> ID</c><00:07:35.979><c> okay</c>

00:07:36.719 --> 00:07:36.729 align:start position:0%
gonna provide you with your card ID okay
 

00:07:36.729 --> 00:07:38.309 align:start position:0%
gonna provide you with your card ID okay
and<00:07:36.819><c> you're</c><00:07:36.939><c> gonna</c><00:07:37.120><c> need</c><00:07:37.329><c> the</c><00:07:37.569><c> card</c><00:07:37.839><c> ID</c><00:07:38.019><c> in</c>

00:07:38.309 --> 00:07:38.319 align:start position:0%
and you're gonna need the card ID in
 

00:07:38.319 --> 00:07:41.790 align:start position:0%
and you're gonna need the card ID in
order<00:07:38.439><c> to</c><00:07:39.180><c> input</c><00:07:40.180><c> that</c><00:07:40.360><c> into</c><00:07:40.689><c> this</c><00:07:41.229><c> section</c><00:07:41.529><c> of</c>

00:07:41.790 --> 00:07:41.800 align:start position:0%
order to input that into this section of
 

00:07:41.800 --> 00:07:42.250 align:start position:0%
order to input that into this section of
the<00:07:41.829><c> you</c><00:07:42.039><c> are</c>

00:07:42.250 --> 00:07:42.260 align:start position:0%
the you are
 

00:07:42.260 --> 00:07:44.410 align:start position:0%
the you are
you<00:07:42.560><c> write</c><00:07:42.770><c> your</c><00:07:42.800><c> own</c><00:07:42.950><c> query</c><00:07:43.490><c> save</c><00:07:44.000><c> it</c><00:07:44.180><c> away</c>

00:07:44.410 --> 00:07:44.420 align:start position:0%
you write your own query save it away
 

00:07:44.420 --> 00:07:46.870 align:start position:0%
you write your own query save it away
and<00:07:44.750><c> you're</c><00:07:45.590><c> going</c><00:07:45.770><c> to</c><00:07:45.830><c> be</c><00:07:45.950><c> able</c><00:07:46.070><c> to</c><00:07:46.520><c> provide</c>

00:07:46.870 --> 00:07:46.880 align:start position:0%
and you're going to be able to provide
 

00:07:46.880 --> 00:07:50.190 align:start position:0%
and you're going to be able to provide
results<00:07:47.600><c> to</c><00:07:48.850><c> unauthenticated</c><00:07:49.850><c> users</c>

00:07:50.190 --> 00:07:50.200 align:start position:0%
results to unauthenticated users
 

00:07:50.200 --> 00:07:53.230 align:start position:0%
results to unauthenticated users
publicly<00:07:51.200><c> you</c><00:07:51.950><c> can</c><00:07:52.130><c> provide</c><00:07:52.430><c> results</c><00:07:52.820><c> to</c><00:07:53.090><c> that</c>

00:07:53.230 --> 00:07:53.240 align:start position:0%
publicly you can provide results to that
 

00:07:53.240 --> 00:07:57.850 align:start position:0%
publicly you can provide results to that
query<00:07:53.570><c> with</c><00:07:54.290><c> a</c><00:07:54.320><c> specific</c><00:07:54.740><c> parameter</c><00:07:55.750><c> in</c><00:07:56.860><c> CSV</c>

00:07:57.850 --> 00:07:57.860 align:start position:0%
query with a specific parameter in CSV
 

00:07:57.860 --> 00:08:00.160 align:start position:0%
query with a specific parameter in CSV
format<00:07:58.030><c> automatically</c><00:07:59.030><c> downloading</c><00:07:59.570><c> the</c><00:07:59.690><c> CSV</c>

00:08:00.160 --> 00:08:00.170 align:start position:0%
format automatically downloading the CSV
 

00:08:00.170 --> 00:08:02.910 align:start position:0%
format automatically downloading the CSV
to<00:08:00.320><c> the</c><00:08:00.440><c> clients</c><00:08:00.800><c> computer</c><00:08:01.240><c> in</c><00:08:02.240><c> JSON</c><00:08:02.780><c> format</c>

00:08:02.910 --> 00:08:02.920 align:start position:0%
to the clients computer in JSON format
 

00:08:02.920 --> 00:08:06.060 align:start position:0%
to the clients computer in JSON format
automatically<00:08:03.920><c> downloading</c><00:08:04.520><c> the</c><00:08:04.670><c> JSON</c><00:08:05.150><c> file</c>

00:08:06.060 --> 00:08:06.070 align:start position:0%
automatically downloading the JSON file
 

00:08:06.070 --> 00:08:10.120 align:start position:0%
automatically downloading the JSON file
or<00:08:07.070><c> an</c><00:08:07.520><c> API</c><00:08:07.940><c> format</c><00:08:08.660><c> the</c><00:08:08.960><c> API</c><00:08:09.260><c> format</c><00:08:09.770><c> is</c><00:08:09.920><c> it</c>

00:08:10.120 --> 00:08:10.130 align:start position:0%
or an API format the API format is it
 

00:08:10.130 --> 00:08:13.150 align:start position:0%
or an API format the API format is it
looks<00:08:10.490><c> like</c><00:08:10.700><c> it</c><00:08:11.150><c> looks</c><00:08:11.210><c> like</c><00:08:11.510><c> this</c><00:08:12.160><c> question</c>

00:08:13.150 --> 00:08:13.160 align:start position:0%
looks like it looks like this question
 

00:08:13.160 --> 00:08:16.540 align:start position:0%
looks like it looks like this question
number<00:08:14.110><c> same</c><00:08:15.110><c> size</c><00:08:15.410><c> and</c><00:08:15.950><c> so</c><00:08:16.100><c> on</c><00:08:16.130><c> here's</c><00:08:16.490><c> the</c>

00:08:16.540 --> 00:08:16.550 align:start position:0%
number same size and so on here's the
 

00:08:16.550 --> 00:08:19.510 align:start position:0%
number same size and so on here's the
question<00:08:17.200><c> question</c><00:08:18.200><c> and</c><00:08:18.680><c> the</c><00:08:18.770><c> answer</c><00:08:19.190><c> goes</c><00:08:19.370><c> oh</c>

00:08:19.510 --> 00:08:19.520 align:start position:0%
question question and the answer goes oh
 

00:08:19.520 --> 00:08:21.280 align:start position:0%
question question and the answer goes oh
here's<00:08:19.760><c> the</c><00:08:19.910><c> question</c><00:08:20.360><c> here's</c><00:08:20.600><c> the</c><00:08:20.840><c> answer</c>

00:08:21.280 --> 00:08:21.290 align:start position:0%
here's the question here's the answer
 

00:08:21.290 --> 00:08:27.580 align:start position:0%
here's the question here's the answer
and<00:08:22.100><c> so</c><00:08:22.160><c> on</c><00:08:23.350><c> okay</c><00:08:24.350><c> so</c><00:08:24.830><c> that</c><00:08:25.550><c> hopefully</c><00:08:26.590><c> cover</c>

00:08:27.580 --> 00:08:27.590 align:start position:0%
and so on okay so that hopefully cover
 

00:08:27.590 --> 00:08:32.230 align:start position:0%
and so on okay so that hopefully cover
some<00:08:28.070><c> ground</c><00:08:28.100><c> and</c><00:08:29.080><c> let's</c><00:08:30.080><c> do</c><00:08:30.290><c> the</c><00:08:30.470><c> example</c><00:08:31.240><c> to</c>

00:08:32.230 --> 00:08:32.240 align:start position:0%
some ground and let's do the example to
 

00:08:32.240 --> 00:08:34.240 align:start position:0%
some ground and let's do the example to
downloading<00:08:32.690><c> the</c><00:08:32.930><c> JSON</c><00:08:33.410><c> just</c><00:08:33.770><c> for</c><00:08:33.950><c> the</c><00:08:34.010><c> sake</c>

00:08:34.240 --> 00:08:34.250 align:start position:0%
downloading the JSON just for the sake
 

00:08:34.250 --> 00:08:38.050 align:start position:0%
downloading the JSON just for the sake
of<00:08:34.280><c> it</c><00:08:34.580><c> ctrl</c><00:08:35.419><c> V</c><00:08:35.690><c> I</c><00:08:36.130><c> paste</c><00:08:37.130><c> it</c><00:08:37.310><c> in</c><00:08:37.460><c> there</c><00:08:37.640><c> and</c>

00:08:38.050 --> 00:08:38.060 align:start position:0%
of it ctrl V I paste it in there and
 

00:08:38.060 --> 00:08:40.719 align:start position:0%
of it ctrl V I paste it in there and
there<00:08:38.900><c> we</c><00:08:38.990><c> go</c><00:08:39.140><c> we</c><00:08:39.320><c> have</c><00:08:39.410><c> a</c><00:08:39.440><c> dot</c><00:08:39.680><c> JSON</c><00:08:39.979><c> file</c><00:08:40.490><c> that</c>

00:08:40.719 --> 00:08:40.729 align:start position:0%
there we go we have a dot JSON file that
 

00:08:40.729 --> 00:08:42.790 align:start position:0%
there we go we have a dot JSON file that
gets<00:08:40.820><c> automatically</c><00:08:41.479><c> downloaded</c><00:08:42.110><c> not</c><00:08:42.530><c> sure</c>

00:08:42.790 --> 00:08:42.800 align:start position:0%
gets automatically downloaded not sure
 

00:08:42.800 --> 00:08:45.340 align:start position:0%
gets automatically downloaded not sure
we<00:08:43.520><c> would</c><00:08:43.669><c> want</c><00:08:43.940><c> to</c><00:08:44.000><c> use</c><00:08:44.180><c> the</c><00:08:44.360><c> downloaded</c><00:08:44.900><c> JSON</c>

00:08:45.340 --> 00:08:45.350 align:start position:0%
we would want to use the downloaded JSON
 

00:08:45.350 --> 00:08:47.650 align:start position:0%
we would want to use the downloaded JSON
but<00:08:45.680><c> you</c><00:08:46.490><c> at</c><00:08:46.640><c> least</c><00:08:46.670><c> know</c><00:08:47.030><c> that</c><00:08:47.060><c> you</c><00:08:47.300><c> have</c><00:08:47.450><c> that</c>

00:08:47.650 --> 00:08:47.660 align:start position:0%
but you at least know that you have that
 

00:08:47.660 --> 00:08:51.180 align:start position:0%
but you at least know that you have that
option<00:08:47.870><c> as</c><00:08:48.170><c> well</c><00:08:48.410><c> so</c><00:08:49.250><c> I</c><00:08:49.280><c> hope</c><00:08:49.490><c> that</c><00:08:49.700><c> that</c><00:08:49.910><c> helps</c>

00:08:51.180 --> 00:08:51.190 align:start position:0%
option as well so I hope that that helps
 

00:08:51.190 --> 00:08:53.710 align:start position:0%
option as well so I hope that that helps
that<00:08:52.190><c> helps</c><00:08:52.340><c> a</c><00:08:52.550><c> little</c><00:08:52.700><c> bit</c><00:08:52.970><c> and</c><00:08:53.210><c> thanks</c><00:08:53.600><c> for</c>

00:08:53.710 --> 00:08:53.720 align:start position:0%
that helps a little bit and thanks for
 

00:08:53.720 --> 00:08:57.880 align:start position:0%
that helps a little bit and thanks for
tuning<00:08:53.960><c> in</c><00:08:54.910><c> again</c><00:08:55.910><c> you</c><00:08:56.270><c> can</c><00:08:56.590><c> you</c><00:08:57.590><c> can</c><00:08:57.740><c> check</c>

00:08:57.880 --> 00:08:57.890 align:start position:0%
tuning in again you can you can check
 

00:08:57.890 --> 00:08:59.950 align:start position:0%
tuning in again you can you can check
out<00:08:58.040><c> my</c><00:08:58.190><c> other</c><00:08:58.340><c> video</c><00:08:58.790><c> which</c><00:08:59.030><c> is</c><00:08:59.210><c> a</c><00:08:59.540><c> little</c><00:08:59.870><c> bit</c>

00:08:59.950 --> 00:08:59.960 align:start position:0%
out my other video which is a little bit
 

00:08:59.960 --> 00:09:01.960 align:start position:0%
out my other video which is a little bit
of<00:09:00.050><c> backdrop</c><00:09:00.560><c> about</c><00:09:00.860><c> this</c><00:09:01.130><c> project</c><00:09:01.730><c> of</c>

00:09:01.960 --> 00:09:01.970 align:start position:0%
of backdrop about this project of
 

00:09:01.970 --> 00:09:05.170 align:start position:0%
of backdrop about this project of
driving<00:09:02.810><c> test</c><00:09:03.080><c> questions</c><00:09:03.680><c> it</c><00:09:04.520><c> has</c><00:09:04.760><c> support</c>

00:09:05.170 --> 00:09:05.180 align:start position:0%
driving test questions it has support
 

00:09:05.180 --> 00:09:07.510 align:start position:0%
driving test questions it has support
for<00:09:05.450><c> a</c><00:09:05.480><c> drive</c><00:09:05.810><c> web</c><00:09:06.230><c> driver</c><00:09:06.470><c> i/o</c><00:09:06.770><c> it's</c><00:09:07.160><c> using</c><00:09:07.430><c> on</c>

00:09:07.510 --> 00:09:07.520 align:start position:0%
for a drive web driver i/o it's using on
 

00:09:07.520 --> 00:09:11.020 align:start position:0%
for a drive web driver i/o it's using on
my<00:09:07.550><c> sequel</c><00:09:08.000><c> database</c><00:09:08.480><c> and</c><00:09:09.490><c> it's</c><00:09:10.490><c> for</c><00:09:10.670><c> it</c><00:09:10.760><c> it's</c>

00:09:11.020 --> 00:09:11.030 align:start position:0%
my sequel database and it's for it it's
 

00:09:11.030 --> 00:09:12.850 align:start position:0%
my sequel database and it's for it it's
a<00:09:11.090><c> data</c><00:09:11.300><c> gathering</c><00:09:11.570><c> project</c><00:09:12.410><c> so</c><00:09:12.530><c> I'm</c><00:09:12.620><c> going</c><00:09:12.800><c> to</c>

00:09:12.850 --> 00:09:12.860 align:start position:0%
a data gathering project so I'm going to
 

00:09:12.860 --> 00:09:15.190 align:start position:0%
a data gathering project so I'm going to
do<00:09:12.980><c> a</c><00:09:13.010><c> hopefully</c><00:09:13.880><c> a</c><00:09:13.970><c> tutorial</c><00:09:14.510><c> videos</c><00:09:14.900><c> about</c>

00:09:15.190 --> 00:09:15.200 align:start position:0%
do a hopefully a tutorial videos about
 

00:09:15.200 --> 00:09:17.770 align:start position:0%
do a hopefully a tutorial videos about
data<00:09:15.500><c> gathering</c><00:09:16.060><c> using</c><00:09:17.060><c> nodejs</c>

00:09:17.770 --> 00:09:17.780 align:start position:0%
data gathering using nodejs
 

00:09:17.780 --> 00:09:21.340 align:start position:0%
data gathering using nodejs
and<00:09:18.290><c> just</c><00:09:18.980><c> vanilla</c><00:09:19.310><c> JavaScript</c><00:09:19.660><c> my</c><00:09:20.660><c> sequel</c><00:09:21.110><c> so</c>

00:09:21.340 --> 00:09:21.350 align:start position:0%
and just vanilla JavaScript my sequel so
 

00:09:21.350 --> 00:09:23.170 align:start position:0%
and just vanilla JavaScript my sequel so
hope<00:09:21.530><c> you</c><00:09:21.710><c> guys</c><00:09:21.860><c> enjoyed</c><00:09:22.250><c> and</c><00:09:22.550><c> keep</c><00:09:22.940><c> me</c><00:09:23.030><c> posted</c>

00:09:23.170 --> 00:09:23.180 align:start position:0%
hope you guys enjoyed and keep me posted
 

00:09:23.180 --> 00:09:26.560 align:start position:0%
hope you guys enjoyed and keep me posted
if<00:09:23.420><c> you</c><00:09:23.480><c> have</c><00:09:23.690><c> questions</c><00:09:24.110><c> thank</c><00:09:24.410><c> you</c>

