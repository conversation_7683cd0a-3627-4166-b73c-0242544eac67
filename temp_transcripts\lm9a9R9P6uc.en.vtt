WEBVTT
Kind: captions
Language: en

00:00:00.199 --> 00:00:02.110 align:start position:0%
 
code<00:00:00.560><c> security</c><00:00:00.919><c> scanning</c><00:00:01.280><c> tools</c><00:00:01.680><c> help</c><00:00:01.880><c> us</c>

00:00:02.110 --> 00:00:02.120 align:start position:0%
code security scanning tools help us
 

00:00:02.120 --> 00:00:04.710 align:start position:0%
code security scanning tools help us
identify<00:00:02.639><c> vulnerabilities</c><00:00:03.360><c> in</c><00:00:03.480><c> our</c><00:00:03.679><c> code</c><00:00:04.600><c> but</c>

00:00:04.710 --> 00:00:04.720 align:start position:0%
identify vulnerabilities in our code but
 

00:00:04.720 --> 00:00:06.789 align:start position:0%
identify vulnerabilities in our code but
to<00:00:04.920><c> remediate</c><00:00:05.400><c> issues</c><00:00:06.080><c> we</c><00:00:06.200><c> must</c><00:00:06.359><c> triage</c>

00:00:06.789 --> 00:00:06.799 align:start position:0%
to remediate issues we must triage
 

00:00:06.799 --> 00:00:09.470 align:start position:0%
to remediate issues we must triage
alerts<00:00:07.520><c> review</c><00:00:07.799><c> the</c><00:00:08.000><c> relevant</c><00:00:08.480><c> documentation</c>

00:00:09.470 --> 00:00:09.480 align:start position:0%
alerts review the relevant documentation
 

00:00:09.480 --> 00:00:12.030 align:start position:0%
alerts review the relevant documentation
and<00:00:09.760><c> propose</c><00:00:10.080><c> a</c><00:00:10.280><c> fix</c><00:00:10.960><c> taking</c><00:00:11.280><c> up</c><00:00:11.599><c> precious</c>

00:00:12.030 --> 00:00:12.040 align:start position:0%
and propose a fix taking up precious
 

00:00:12.040 --> 00:00:15.190 align:start position:0%
and propose a fix taking up precious
development<00:00:13.080><c> time</c><00:00:14.080><c> that</c><00:00:14.280><c> was</c><00:00:14.639><c> until</c><00:00:14.960><c> code</c>

00:00:15.190 --> 00:00:15.200 align:start position:0%
development time that was until code
 

00:00:15.200 --> 00:00:16.230 align:start position:0%
development time that was until code
scanning

00:00:16.230 --> 00:00:16.240 align:start position:0%
scanning
 

00:00:16.240 --> 00:00:19.150 align:start position:0%
scanning
autofix<00:00:17.240><c> autofix</c><00:00:17.960><c> improves</c><00:00:18.400><c> the</c><00:00:18.560><c> experience</c>

00:00:19.150 --> 00:00:19.160 align:start position:0%
autofix autofix improves the experience
 

00:00:19.160 --> 00:00:21.990 align:start position:0%
autofix autofix improves the experience
by<00:00:19.279><c> using</c><00:00:19.640><c> AI</c><00:00:20.279><c> to</c><00:00:20.480><c> provide</c><00:00:20.720><c> a</c><00:00:20.840><c> code</c><00:00:21.119><c> suggestion</c>

00:00:21.990 --> 00:00:22.000 align:start position:0%
by using AI to provide a code suggestion
 

00:00:22.000 --> 00:00:24.589 align:start position:0%
by using AI to provide a code suggestion
and<00:00:22.279><c> explanation</c><00:00:23.240><c> directly</c><00:00:23.640><c> in</c><00:00:23.760><c> the</c><00:00:23.840><c> PO</c>

00:00:24.589 --> 00:00:24.599 align:start position:0%
and explanation directly in the PO
 

00:00:24.599 --> 00:00:27.669 align:start position:0%
and explanation directly in the PO
request<00:00:25.599><c> the</c><00:00:25.760><c> developer</c><00:00:26.480><c> remains</c><00:00:26.679><c> in</c><00:00:26.920><c> control</c>

00:00:27.669 --> 00:00:27.679 align:start position:0%
request the developer remains in control
 

00:00:27.679 --> 00:00:29.429 align:start position:0%
request the developer remains in control
being<00:00:27.920><c> able</c><00:00:28.119><c> to</c><00:00:28.279><c> make</c><00:00:28.519><c> edits</c><00:00:28.840><c> using</c><00:00:29.119><c> GitHub</c>

00:00:29.429 --> 00:00:29.439 align:start position:0%
being able to make edits using GitHub
 

00:00:29.439 --> 00:00:32.109 align:start position:0%
being able to make edits using GitHub
code<00:00:29.640><c> spaces</c><00:00:30.359><c> or</c><00:00:30.560><c> their</c><00:00:30.759><c> local</c><00:00:31.119><c> machine</c>

00:00:32.109 --> 00:00:32.119 align:start position:0%
code spaces or their local machine
 

00:00:32.119 --> 00:00:34.389 align:start position:0%
code spaces or their local machine
accept<00:00:32.439><c> the</c><00:00:32.559><c> original</c><00:00:32.960><c> suggestion</c><00:00:33.920><c> or</c>

00:00:34.389 --> 00:00:34.399 align:start position:0%
accept the original suggestion or
 

00:00:34.399 --> 00:00:35.470 align:start position:0%
accept the original suggestion or
dismiss<00:00:34.760><c> it</c>

00:00:35.470 --> 00:00:35.480 align:start position:0%
dismiss it
 

00:00:35.480 --> 00:00:38.430 align:start position:0%
dismiss it
entirely<00:00:36.480><c> with</c><00:00:36.760><c> autofix</c><00:00:37.760><c> code</c><00:00:38.120><c> security</c>

00:00:38.430 --> 00:00:38.440 align:start position:0%
entirely with autofix code security
 

00:00:38.440 --> 00:00:40.790 align:start position:0%
entirely with autofix code security
alerts<00:00:38.840><c> transition</c><00:00:39.399><c> from</c><00:00:39.559><c> being</c><00:00:39.800><c> found</c><00:00:40.600><c> to</c>

00:00:40.790 --> 00:00:40.800 align:start position:0%
alerts transition from being found to
 

00:00:40.800 --> 00:00:45.869 align:start position:0%
alerts transition from being found to
being

00:00:45.869 --> 00:00:45.879 align:start position:0%
 
 

00:00:45.879 --> 00:00:48.879 align:start position:0%
 
fixed

