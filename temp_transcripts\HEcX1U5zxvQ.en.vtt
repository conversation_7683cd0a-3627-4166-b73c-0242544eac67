WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:02.869 align:start position:0%
 
in<00:00:00.199><c> a</c><00:00:00.399><c> world</c><00:00:01.319><c> where</c><00:00:01.599><c> AI</c><00:00:02.000><c> agents</c><00:00:02.440><c> could</c><00:00:02.600><c> never</c>

00:00:02.869 --> 00:00:02.879 align:start position:0%
in a world where AI agents could never
 

00:00:02.879 --> 00:00:06.150 align:start position:0%
in a world where AI agents could never
build<00:00:03.399><c> themselves</c><00:00:04.240><c> until</c><00:00:04.880><c> now</c><00:00:05.600><c> comes</c><00:00:05.839><c> autog</c>

00:00:06.150 --> 00:00:06.160 align:start position:0%
build themselves until now comes autog
 

00:00:06.160 --> 00:00:08.990 align:start position:0%
build themselves until now comes autog
Gro<00:00:06.919><c> beta</c><00:00:07.240><c> version</c><00:00:07.559><c> 7-Eleven</c><00:00:08.559><c> because</c><00:00:08.840><c> this</c>

00:00:08.990 --> 00:00:09.000 align:start position:0%
Gro beta version 7-Eleven because this
 

00:00:09.000 --> 00:00:15.310 align:start position:0%
Gro beta version 7-Eleven because this
update<00:00:09.320><c> kept</c><00:00:09.519><c> us</c><00:00:09.679><c> up</c><00:00:09.880><c> all</c>

00:00:15.310 --> 00:00:15.320 align:start position:0%
 
 

00:00:15.320 --> 00:00:18.230 align:start position:0%
 
night<00:00:16.320><c> stop</c><00:00:17.279><c> we</c><00:00:17.400><c> got</c><00:00:17.600><c> no</c><00:00:17.800><c> time</c><00:00:17.960><c> for</c><00:00:18.119><c> the</c>

00:00:18.230 --> 00:00:18.240 align:start position:0%
night stop we got no time for the
 

00:00:18.240 --> 00:00:20.429 align:start position:0%
night stop we got no time for the
world's<00:00:18.640><c> greatest</c><00:00:19.000><c> theme</c><00:00:19.279><c> song</c><00:00:19.680><c> sorry</c><00:00:20.279><c> we're</c>

00:00:20.429 --> 00:00:20.439 align:start position:0%
world's greatest theme song sorry we're
 

00:00:20.439 --> 00:00:22.029 align:start position:0%
world's greatest theme song sorry we're
running<00:00:20.680><c> in</c><00:00:20.840><c> local</c><00:00:21.160><c> mode</c><00:00:21.519><c> because</c><00:00:21.760><c> I</c><00:00:21.880><c> have</c>

00:00:22.029 --> 00:00:22.039 align:start position:0%
running in local mode because I have
 

00:00:22.039 --> 00:00:23.710 align:start position:0%
running in local mode because I have
some<00:00:22.240><c> things</c><00:00:22.439><c> in</c><00:00:22.600><c> autogen</c><00:00:23.160><c> to</c><00:00:23.240><c> show</c><00:00:23.439><c> you</c><00:00:23.599><c> that</c>

00:00:23.710 --> 00:00:23.720 align:start position:0%
some things in autogen to show you that
 

00:00:23.720 --> 00:00:26.230 align:start position:0%
some things in autogen to show you that
we<00:00:23.880><c> can't</c><00:00:24.160><c> do</c><00:00:24.400><c> from</c><00:00:24.599><c> the</c><00:00:24.760><c> demo</c><00:00:25.119><c> site</c><00:00:25.920><c> if</c><00:00:26.039><c> you've</c>

00:00:26.230 --> 00:00:26.240 align:start position:0%
we can't do from the demo site if you've
 

00:00:26.240 --> 00:00:28.429 align:start position:0%
we can't do from the demo site if you've
gotten<00:00:26.439><c> used</c><00:00:26.679><c> to</c><00:00:26.800><c> autog</c><00:00:27.119><c> gr's</c><00:00:27.439><c> 2C</c><00:00:28.000><c> response</c>

00:00:28.429 --> 00:00:28.439 align:start position:0%
gotten used to autog gr's 2C response
 

00:00:28.439 --> 00:00:30.070 align:start position:0%
gotten used to autog gr's 2C response
time<00:00:28.760><c> this</c><00:00:28.920><c> update</c><00:00:29.279><c> might</c><00:00:29.480><c> make</c><00:00:29.599><c> you</c><00:00:29.720><c> a</c><00:00:29.759><c> little</c>

00:00:30.070 --> 00:00:30.080 align:start position:0%
time this update might make you a little
 

00:00:30.080 --> 00:00:32.350 align:start position:0%
time this update might make you a little
impatient<00:00:30.560><c> but</c><00:00:30.679><c> bear</c><00:00:30.920><c> with</c><00:00:31.080><c> me</c><00:00:31.480><c> autog</c><00:00:31.840><c> Gro</c><00:00:32.160><c> is</c>

00:00:32.350 --> 00:00:32.360 align:start position:0%
impatient but bear with me autog Gro is
 

00:00:32.360 --> 00:00:33.950 align:start position:0%
impatient but bear with me autog Gro is
performing<00:00:32.840><c> a</c><00:00:33.000><c> lot</c><00:00:33.160><c> of</c>

00:00:33.950 --> 00:00:33.960 align:start position:0%
performing a lot of
 

00:00:33.960 --> 00:00:36.709 align:start position:0%
performing a lot of
steps<00:00:34.960><c> it's</c><00:00:35.239><c> refactoring</c><00:00:35.920><c> our</c>

00:00:36.709 --> 00:00:36.719 align:start position:0%
steps it's refactoring our
 

00:00:36.719 --> 00:00:39.590 align:start position:0%
steps it's refactoring our
prompt<00:00:37.719><c> crafting</c><00:00:38.200><c> a</c><00:00:38.440><c> project</c><00:00:38.840><c> manager</c>

00:00:39.590 --> 00:00:39.600 align:start position:0%
prompt crafting a project manager
 

00:00:39.600 --> 00:00:41.869 align:start position:0%
prompt crafting a project manager
specifically<00:00:40.200><c> for</c><00:00:40.399><c> our</c><00:00:40.760><c> project</c><00:00:41.239><c> and</c><00:00:41.440><c> working</c>

00:00:41.869 --> 00:00:41.879 align:start position:0%
specifically for our project and working
 

00:00:41.879 --> 00:00:44.990 align:start position:0%
specifically for our project and working
with<00:00:42.120><c> that</c><00:00:42.440><c> agent</c><00:00:43.440><c> to</c><00:00:43.680><c> design</c><00:00:44.120><c> and</c><00:00:44.320><c> build</c><00:00:44.719><c> each</c>

00:00:44.990 --> 00:00:45.000 align:start position:0%
with that agent to design and build each
 

00:00:45.000 --> 00:00:46.950 align:start position:0%
with that agent to design and build each
agent<00:00:45.360><c> in</c><00:00:45.480><c> the</c><00:00:45.640><c> team</c><00:00:46.079><c> according</c><00:00:46.399><c> to</c><00:00:46.559><c> our</c><00:00:46.719><c> new</c>

00:00:46.950 --> 00:00:46.960 align:start position:0%
agent in the team according to our new
 

00:00:46.960 --> 00:00:48.670 align:start position:0%
agent in the team according to our new
manager

00:00:48.670 --> 00:00:48.680 align:start position:0%
manager
 

00:00:48.680 --> 00:00:51.029 align:start position:0%
manager
specifications<00:00:49.680><c> all</c><00:00:49.879><c> in</c><00:00:50.039><c> all</c><00:00:50.239><c> autog</c><00:00:50.559><c> Gro</c><00:00:50.920><c> is</c>

00:00:51.029 --> 00:00:51.039 align:start position:0%
specifications all in all autog Gro is
 

00:00:51.039 --> 00:00:53.270 align:start position:0%
specifications all in all autog Gro is
performing<00:00:51.480><c> nearly</c><00:00:51.760><c> a</c><00:00:51.879><c> dozen</c><00:00:52.199><c> sequential</c><00:00:52.760><c> AI</c>

00:00:53.270 --> 00:00:53.280 align:start position:0%
performing nearly a dozen sequential AI
 

00:00:53.280 --> 00:00:55.470 align:start position:0%
performing nearly a dozen sequential AI
requests<00:00:54.280><c> we</c><00:00:54.399><c> have</c><00:00:54.520><c> to</c><00:00:54.640><c> stagger</c><00:00:55.000><c> them</c><00:00:55.160><c> by</c><00:00:55.320><c> 2</c>

00:00:55.470 --> 00:00:55.480 align:start position:0%
requests we have to stagger them by 2
 

00:00:55.480 --> 00:00:57.389 align:start position:0%
requests we have to stagger them by 2
seconds<00:00:55.840><c> each</c><00:00:56.039><c> to</c><00:00:56.239><c> avoid</c><00:00:56.520><c> violating</c><00:00:56.960><c> grock's</c>

00:00:57.389 --> 00:00:57.399 align:start position:0%
seconds each to avoid violating grock's
 

00:00:57.399 --> 00:01:01.389 align:start position:0%
seconds each to avoid violating grock's
TOA<00:00:58.399><c> and</c><00:00:58.519><c> it</c><00:00:58.680><c> adds</c><00:00:59.000><c> up</c><00:01:00.039><c> still</c><00:01:00.680><c> the</c><00:01:00.840><c> results</c><00:01:01.199><c> are</c>

00:01:01.389 --> 00:01:01.399 align:start position:0%
TOA and it adds up still the results are
 

00:01:01.399 --> 00:01:03.950 align:start position:0%
TOA and it adds up still the results are
impressive<00:01:02.199><c> autog</c><00:01:02.600><c> Gro</c><00:01:02.960><c> has</c><00:01:03.160><c> created</c><00:01:03.640><c> eight</c>

00:01:03.950 --> 00:01:03.960 align:start position:0%
impressive autog Gro has created eight
 

00:01:03.960 --> 00:01:06.109 align:start position:0%
impressive autog Gro has created eight
detailed<00:01:04.559><c> agents</c><00:01:05.080><c> ready</c><00:01:05.280><c> to</c><00:01:05.439><c> get</c><00:01:05.600><c> to</c><00:01:05.760><c> work</c><00:01:05.960><c> on</c>

00:01:06.109 --> 00:01:06.119 align:start position:0%
detailed agents ready to get to work on
 

00:01:06.119 --> 00:01:08.270 align:start position:0%
detailed agents ready to get to work on
our<00:01:06.400><c> project</c><00:01:06.920><c> our</c><00:01:07.080><c> manager</c><00:01:07.520><c> explains</c><00:01:08.080><c> who</c>

00:01:08.270 --> 00:01:08.280 align:start position:0%
our project our manager explains who
 

00:01:08.280 --> 00:01:09.950 align:start position:0%
our project our manager explains who
each<00:01:08.520><c> agent</c><00:01:08.880><c> is</c><00:01:09.200><c> and</c><00:01:09.400><c> why</c><00:01:09.600><c> they</c><00:01:09.720><c> were</c>

00:01:09.950 --> 00:01:09.960 align:start position:0%
each agent is and why they were
 

00:01:09.960 --> 00:01:11.870 align:start position:0%
each agent is and why they were
specifically<00:01:10.600><c> designed</c><00:01:11.000><c> and</c><00:01:11.240><c> created</c><00:01:11.640><c> for</c>

00:01:11.870 --> 00:01:11.880 align:start position:0%
specifically designed and created for
 

00:01:11.880 --> 00:01:13.789 align:start position:0%
specifically designed and created for
this<00:01:12.159><c> project</c><00:01:12.880><c> you</c><00:01:13.040><c> may</c><00:01:13.159><c> have</c><00:01:13.320><c> noticed</c><00:01:13.640><c> our</c>

00:01:13.789 --> 00:01:13.799 align:start position:0%
this project you may have noticed our
 

00:01:13.799 --> 00:01:15.910 align:start position:0%
this project you may have noticed our
manager<00:01:14.479><c> detailed</c><00:01:15.080><c> objectives</c><00:01:15.520><c> and</c>

00:01:15.910 --> 00:01:15.920 align:start position:0%
manager detailed objectives and
 

00:01:15.920 --> 00:01:18.109 align:start position:0%
manager detailed objectives and
deliverables<00:01:16.920><c> those</c><00:01:17.119><c> get</c><00:01:17.320><c> parsed</c><00:01:17.680><c> out</c><00:01:17.840><c> into</c>

00:01:18.109 --> 00:01:18.119 align:start position:0%
deliverables those get parsed out into
 

00:01:18.119 --> 00:01:20.830 align:start position:0%
deliverables those get parsed out into
their<00:01:18.240><c> own</c><00:01:18.479><c> new</c><00:01:18.720><c> tabs</c><00:01:19.159><c> as</c><00:01:19.360><c> active</c><00:01:19.840><c> checklists</c>

00:01:20.830 --> 00:01:20.840 align:start position:0%
their own new tabs as active checklists
 

00:01:20.840 --> 00:01:22.670 align:start position:0%
their own new tabs as active checklists
in<00:01:20.920><c> the</c><00:01:21.040><c> future</c><00:01:21.400><c> checking</c><00:01:21.799><c> these</c><00:01:22.040><c> boxes</c><00:01:22.479><c> will</c>

00:01:22.670 --> 00:01:22.680 align:start position:0%
in the future checking these boxes will
 

00:01:22.680 --> 00:01:24.910 align:start position:0%
in the future checking these boxes will
focus<00:01:23.040><c> your</c><00:01:23.200><c> team</c><00:01:23.520><c> on</c><00:01:23.759><c> specific</c><00:01:24.320><c> tasks</c><00:01:24.720><c> and</c>

00:01:24.910 --> 00:01:24.920 align:start position:0%
focus your team on specific tasks and
 

00:01:24.920 --> 00:01:26.950 align:start position:0%
focus your team on specific tasks and
goal<00:01:25.439><c> for</c><00:01:25.640><c> now</c><00:01:25.840><c> they're</c><00:01:26.079><c> just</c><00:01:26.240><c> handy</c><00:01:26.600><c> visible</c>

00:01:26.950 --> 00:01:26.960 align:start position:0%
goal for now they're just handy visible
 

00:01:26.960 --> 00:01:29.429 align:start position:0%
goal for now they're just handy visible
indicators<00:01:27.520><c> for</c><00:01:27.720><c> us</c><00:01:27.960><c> humans</c><00:01:28.840><c> if</c><00:01:28.960><c> we</c><00:01:29.119><c> click</c><00:01:29.320><c> on</c>

00:01:29.429 --> 00:01:29.439 align:start position:0%
indicators for us humans if we click on
 

00:01:29.439 --> 00:01:31.109 align:start position:0%
indicators for us humans if we click on
our<00:01:29.600><c> back</c><00:01:30.119><c> developer</c><00:01:30.560><c> you'll</c><00:01:30.840><c> probably</c>

00:01:31.109 --> 00:01:31.119 align:start position:0%
our back developer you'll probably
 

00:01:31.119 --> 00:01:32.870 align:start position:0%
our back developer you'll probably
notice<00:01:31.520><c> that</c><00:01:31.640><c> the</c><00:01:31.759><c> feedback</c><00:01:32.240><c> we</c><00:01:32.399><c> get</c><00:01:32.560><c> from</c><00:01:32.680><c> our</c>

00:01:32.870 --> 00:01:32.880 align:start position:0%
notice that the feedback we get from our
 

00:01:32.880 --> 00:01:34.910 align:start position:0%
notice that the feedback we get from our
agents<00:01:33.240><c> is</c><00:01:33.399><c> a</c><00:01:33.520><c> lot</c><00:01:33.720><c> more</c><00:01:33.960><c> detailed</c><00:01:34.479><c> and</c><00:01:34.680><c> well</c>

00:01:34.910 --> 00:01:34.920 align:start position:0%
agents is a lot more detailed and well
 

00:01:34.920 --> 00:01:37.030 align:start position:0%
agents is a lot more detailed and well
structured<00:01:35.439><c> than</c><00:01:35.640><c> before</c><00:01:36.399><c> here</c><00:01:36.560><c> you'll</c><00:01:36.720><c> see</c><00:01:36.920><c> a</c>

00:01:37.030 --> 00:01:37.040 align:start position:0%
structured than before here you'll see a
 

00:01:37.040 --> 00:01:39.389 align:start position:0%
structured than before here you'll see a
demonstration<00:01:37.640><c> of</c><00:01:37.759><c> the</c><00:01:37.960><c> fundamentals</c><00:01:38.439><c> of</c>

00:01:39.389 --> 00:01:39.399 align:start position:0%
demonstration of the fundamentals of
 

00:01:39.399 --> 00:01:41.469 align:start position:0%
demonstration of the fundamentals of
introspection<00:01:40.399><c> where</c><00:01:40.560><c> we</c><00:01:40.720><c> use</c><00:01:40.880><c> an</c><00:01:41.079><c> agent's</c>

00:01:41.469 --> 00:01:41.479 align:start position:0%
introspection where we use an agent's
 

00:01:41.479 --> 00:01:43.069 align:start position:0%
introspection where we use an agent's
thoughts<00:01:41.799><c> about</c><00:01:42.000><c> itself</c><00:01:42.399><c> to</c><00:01:42.640><c> actually</c>

00:01:43.069 --> 00:01:43.079 align:start position:0%
thoughts about itself to actually
 

00:01:43.079 --> 00:01:45.190 align:start position:0%
thoughts about itself to actually
reconfigure<00:01:43.759><c> itself</c><00:01:44.159><c> into</c><00:01:44.360><c> a</c><00:01:44.520><c> more</c><00:01:44.759><c> detailed</c>

00:01:45.190 --> 00:01:45.200 align:start position:0%
reconfigure itself into a more detailed
 

00:01:45.200 --> 00:01:47.389 align:start position:0%
reconfigure itself into a more detailed
and<00:01:45.399><c> robust</c><00:01:45.960><c> agent</c><00:01:46.719><c> this</c><00:01:46.840><c> too</c><00:01:47.119><c> will</c><00:01:47.240><c> be</c>

00:01:47.389 --> 00:01:47.399 align:start position:0%
and robust agent this too will be
 

00:01:47.399 --> 00:01:48.789 align:start position:0%
and robust agent this too will be
something<00:01:47.719><c> I</c><00:01:47.840><c> hope</c><00:01:48.040><c> to</c><00:01:48.200><c> automate</c><00:01:48.600><c> in</c><00:01:48.680><c> the</c>

00:01:48.789 --> 00:01:48.799 align:start position:0%
something I hope to automate in the
 

00:01:48.799 --> 00:01:51.030 align:start position:0%
something I hope to automate in the
future<00:01:49.200><c> agents</c><00:01:49.600><c> that</c><00:01:49.719><c> can</c><00:01:49.880><c> grow</c><00:01:50.399><c> as</c><00:01:50.560><c> entities</c>

00:01:51.030 --> 00:01:51.040 align:start position:0%
future agents that can grow as entities
 

00:01:51.040 --> 00:01:52.630 align:start position:0%
future agents that can grow as entities
based<00:01:51.320><c> on</c><00:01:51.479><c> their</c><00:01:51.600><c> own</c><00:01:51.880><c> observations</c><00:01:52.479><c> and</c>

00:01:52.630 --> 00:01:52.640 align:start position:0%
based on their own observations and
 

00:01:52.640 --> 00:01:54.389 align:start position:0%
based on their own observations and
self-reflection<00:01:53.640><c> the</c><00:01:53.759><c> potential</c><00:01:54.200><c> is</c>

00:01:54.389 --> 00:01:54.399 align:start position:0%
self-reflection the potential is
 

00:01:54.399 --> 00:01:56.910 align:start position:0%
self-reflection the potential is
profound<00:01:55.360><c> the</c><00:01:55.479><c> more</c><00:01:55.640><c> detail</c><00:01:56.039><c> our</c><00:01:56.240><c> agents</c><00:01:56.640><c> have</c>

00:01:56.910 --> 00:01:56.920 align:start position:0%
profound the more detail our agents have
 

00:01:56.920 --> 00:01:58.429 align:start position:0%
profound the more detail our agents have
especially<00:01:57.320><c> when</c><00:01:57.479><c> targeted</c><00:01:57.960><c> toward</c><00:01:58.200><c> our</c>

00:01:58.429 --> 00:01:58.439 align:start position:0%
especially when targeted toward our
 

00:01:58.439 --> 00:02:00.310 align:start position:0%
especially when targeted toward our
project<00:01:58.880><c> goal</c><00:01:59.320><c> the</c><00:01:59.399><c> more</c><00:01:59.560><c> aware</c><00:02:00.079><c> they'll</c>

00:02:00.310 --> 00:02:00.320 align:start position:0%
project goal the more aware they'll
 

00:02:00.320 --> 00:02:02.550 align:start position:0%
project goal the more aware they'll
appear<00:02:00.560><c> to</c><00:02:00.799><c> act</c><00:02:01.479><c> I</c><00:02:01.600><c> encourage</c><00:02:01.960><c> you</c><00:02:02.079><c> to</c><00:02:02.200><c> do</c><00:02:02.399><c> your</c>

00:02:02.550 --> 00:02:02.560 align:start position:0%
appear to act I encourage you to do your
 

00:02:02.560 --> 00:02:04.510 align:start position:0%
appear to act I encourage you to do your
own<00:02:02.880><c> experiments</c><00:02:03.399><c> with</c><00:02:03.520><c> the</c><00:02:03.640><c> new</c><00:02:03.799><c> autog</c><00:02:04.119><c> GRS</c>

00:02:04.510 --> 00:02:04.520 align:start position:0%
own experiments with the new autog GRS
 

00:02:04.520 --> 00:02:06.870 align:start position:0%
own experiments with the new autog GRS
generated<00:02:05.039><c> agents</c><00:02:05.960><c> ask</c><00:02:06.159><c> it</c><00:02:06.320><c> to</c><00:02:06.439><c> write</c><00:02:06.680><c> some</c>

00:02:06.870 --> 00:02:06.880 align:start position:0%
generated agents ask it to write some
 

00:02:06.880 --> 00:02:08.309 align:start position:0%
generated agents ask it to write some
code<00:02:07.159><c> and</c><00:02:07.320><c> compare</c><00:02:07.640><c> the</c><00:02:07.759><c> quality</c><00:02:08.119><c> of</c><00:02:08.200><c> the</c>

00:02:08.309 --> 00:02:08.319 align:start position:0%
code and compare the quality of the
 

00:02:08.319 --> 00:02:12.589 align:start position:0%
code and compare the quality of the
output<00:02:08.679><c> to</c><00:02:08.840><c> just</c><00:02:09.039><c> a</c><00:02:09.160><c> few</c><00:02:09.360><c> weeks</c>

00:02:12.589 --> 00:02:12.599 align:start position:0%
 
 

00:02:12.599 --> 00:02:15.470 align:start position:0%
 
ago<00:02:13.599><c> better</c><00:02:13.879><c> yet</c><00:02:14.120><c> let's</c><00:02:14.360><c> export</c><00:02:14.720><c> our</c><00:02:14.879><c> agents's</c>

00:02:15.470 --> 00:02:15.480 align:start position:0%
ago better yet let's export our agents's
 

00:02:15.480 --> 00:02:17.309 align:start position:0%
ago better yet let's export our agents's
files<00:02:15.879><c> and</c><00:02:16.000><c> turn</c><00:02:16.200><c> them</c><00:02:16.319><c> loose</c><00:02:16.560><c> in</c><00:02:16.680><c> the</c><00:02:16.800><c> autogen</c>

00:02:17.309 --> 00:02:17.319 align:start position:0%
files and turn them loose in the autogen
 

00:02:17.319 --> 00:02:19.750 align:start position:0%
files and turn them loose in the autogen
environment<00:02:17.760><c> to</c><00:02:17.879><c> see</c><00:02:18.080><c> what</c><00:02:18.200><c> they</c><00:02:18.319><c> can</c><00:02:18.480><c> come</c><00:02:18.640><c> up</c>

00:02:19.750 --> 00:02:19.760 align:start position:0%
environment to see what they can come up
 

00:02:19.760 --> 00:02:22.949 align:start position:0%
environment to see what they can come up
with<00:02:20.760><c> we</c><00:02:20.920><c> interrupt</c><00:02:21.400><c> this</c><00:02:21.560><c> computer</c><00:02:21.920><c> geek</c><00:02:22.200><c> for</c>

00:02:22.949 --> 00:02:22.959 align:start position:0%
with we interrupt this computer geek for
 

00:02:22.959 --> 00:02:25.430 align:start position:0%
with we interrupt this computer geek for
well<00:02:23.080><c> for</c><00:02:23.280><c> the</c><00:02:23.400><c> same</c><00:02:23.640><c> computer</c><00:02:24.080><c> geek</c><00:02:25.000><c> okay</c>

00:02:25.430 --> 00:02:25.440 align:start position:0%
well for the same computer geek okay
 

00:02:25.440 --> 00:02:27.270 align:start position:0%
well for the same computer geek okay
here's<00:02:25.680><c> the</c><00:02:25.840><c> thing</c><00:02:26.319><c> I</c><00:02:26.440><c> started</c><00:02:26.840><c> recording</c>

00:02:27.270 --> 00:02:27.280 align:start position:0%
here's the thing I started recording
 

00:02:27.280 --> 00:02:29.390 align:start position:0%
here's the thing I started recording
this<00:02:27.440><c> demo</c><00:02:27.840><c> again</c><00:02:28.040><c> and</c><00:02:28.160><c> then</c><00:02:28.319><c> I</c><00:02:28.400><c> was</c><00:02:28.640><c> like</c><00:02:29.160><c> oh</c>

00:02:29.390 --> 00:02:29.400 align:start position:0%
this demo again and then I was like oh
 

00:02:29.400 --> 00:02:31.350 align:start position:0%
this demo again and then I was like oh
crap<00:02:29.640><c> I</c><00:02:29.920><c> got</c><00:02:30.040><c> to</c><00:02:30.160><c> import</c><00:02:30.519><c> all</c><00:02:30.720><c> those</c><00:02:30.959><c> merken</c>

00:02:31.350 --> 00:02:31.360 align:start position:0%
crap I got to import all those merken
 

00:02:31.360 --> 00:02:35.309 align:start position:0%
crap I got to import all those merken
furin<00:02:31.760><c> autogen</c><00:02:32.400><c> agent</c><00:02:33.200><c> files</c><00:02:34.200><c> again</c><00:02:34.959><c> I</c><00:02:35.080><c> knew</c>

00:02:35.309 --> 00:02:35.319 align:start position:0%
furin autogen agent files again I knew
 

00:02:35.319 --> 00:02:37.869 align:start position:0%
furin autogen agent files again I knew
I'd<00:02:35.519><c> be</c><00:02:36.120><c> complaining</c><00:02:36.800><c> the</c><00:02:36.959><c> entire</c><00:02:37.360><c> time</c><00:02:37.599><c> about</c>

00:02:37.869 --> 00:02:37.879 align:start position:0%
I'd be complaining the entire time about
 

00:02:37.879 --> 00:02:40.030 align:start position:0%
I'd be complaining the entire time about
how<00:02:38.040><c> autogen</c><00:02:38.599><c> needs</c><00:02:38.840><c> a</c><00:02:38.959><c> way</c><00:02:39.080><c> to</c><00:02:39.239><c> bulk</c><00:02:39.560><c> upload</c>

00:02:40.030 --> 00:02:40.040 align:start position:0%
how autogen needs a way to bulk upload
 

00:02:40.040 --> 00:02:48.470 align:start position:0%
how autogen needs a way to bulk upload
Jon<00:02:40.760><c> files</c><00:02:41.400><c> so</c><00:02:41.680><c> I</c><00:02:41.840><c> made</c><00:02:42.159><c> one</c><00:02:42.560><c> and</c><00:02:42.760><c> now</c><00:02:43.519><c> here</c><00:02:43.680><c> it</c>

00:02:48.470 --> 00:02:48.480 align:start position:0%
 
 

00:02:48.480 --> 00:02:53.309 align:start position:0%
 
is<00:02:49.480><c> that</c><00:02:50.200><c> that's</c><00:02:50.440><c> it</c><00:02:51.360><c> that's</c><00:02:51.599><c> it</c><00:02:52.440><c> it's</c><00:02:53.000><c> just</c><00:02:53.159><c> a</c>

00:02:53.309 --> 00:02:53.319 align:start position:0%
is that that's it that's it it's just a
 

00:02:53.319 --> 00:02:56.750 align:start position:0%
is that that's it that's it it's just a
button<00:02:54.080><c> no</c><00:02:54.319><c> it's</c><00:02:54.519><c> not</c><00:02:54.760><c> just</c><00:02:54.920><c> a</c><00:02:55.360><c> button</c><00:02:56.360><c> okay</c>

00:02:56.750 --> 00:02:56.760 align:start position:0%
button no it's not just a button okay
 

00:02:56.760 --> 00:02:58.869 align:start position:0%
button no it's not just a button okay
okay<00:02:57.239><c> it</c><00:02:57.400><c> is</c><00:02:57.760><c> just</c><00:02:57.920><c> a</c><00:02:58.080><c> button</c><00:02:58.480><c> but</c><00:02:58.599><c> it's</c><00:02:58.720><c> a</c>

00:02:58.869 --> 00:02:58.879 align:start position:0%
okay it is just a button but it's a
 

00:02:58.879 --> 00:03:00.630 align:start position:0%
okay it is just a button but it's a
really<00:02:59.120><c> cool</c><00:02:59.400><c> button</c>

00:03:00.630 --> 00:03:00.640 align:start position:0%
really cool button
 

00:03:00.640 --> 00:03:03.350 align:start position:0%
really cool button
watch<00:03:01.640><c> this</c><00:03:01.760><c> is</c><00:03:01.879><c> a</c><00:03:02.080><c> new</c><00:03:02.440><c> fairly</c><00:03:02.840><c> sparse</c>

00:03:03.350 --> 00:03:03.360 align:start position:0%
watch this is a new fairly sparse
 

00:03:03.360 --> 00:03:05.869 align:start position:0%
watch this is a new fairly sparse
installation<00:03:03.920><c> of</c><00:03:04.080><c> autog</c><00:03:04.480><c> genen</c><00:03:05.480><c> not</c><00:03:05.680><c> much</c>

00:03:05.869 --> 00:03:05.879 align:start position:0%
installation of autog genen not much
 

00:03:05.879 --> 00:03:07.630 align:start position:0%
installation of autog genen not much
here<00:03:06.040><c> in</c><00:03:06.159><c> terms</c><00:03:06.400><c> of</c><00:03:06.519><c> skills</c><00:03:06.959><c> agents</c><00:03:07.319><c> or</c>

00:03:07.630 --> 00:03:07.640 align:start position:0%
here in terms of skills agents or
 

00:03:07.640 --> 00:03:10.390 align:start position:0%
here in terms of skills agents or
workflows<00:03:08.640><c> not</c><00:03:08.879><c> yet</c><00:03:09.080><c> anyway</c><00:03:09.920><c> I've</c><00:03:10.159><c> asked</c>

00:03:10.390 --> 00:03:10.400 align:start position:0%
workflows not yet anyway I've asked
 

00:03:10.400 --> 00:03:12.270 align:start position:0%
workflows not yet anyway I've asked
autog<00:03:10.760><c> Gro</c><00:03:11.080><c> for</c><00:03:11.200><c> a</c><00:03:11.319><c> pretty</c><00:03:11.640><c> comprehensive</c>

00:03:12.270 --> 00:03:12.280 align:start position:0%
autog Gro for a pretty comprehensive
 

00:03:12.280 --> 00:03:15.149 align:start position:0%
autog Gro for a pretty comprehensive
business<00:03:12.680><c> software</c><00:03:13.480><c> package</c><00:03:14.480><c> given</c><00:03:14.720><c> a</c><00:03:14.840><c> fairly</c>

00:03:15.149 --> 00:03:15.159 align:start position:0%
business software package given a fairly
 

00:03:15.159 --> 00:03:17.470 align:start position:0%
business software package given a fairly
involved<00:03:15.599><c> goal</c><00:03:16.440><c> it's</c><00:03:16.680><c> come</c><00:03:16.840><c> up</c><00:03:17.000><c> with</c><00:03:17.120><c> a</c><00:03:17.239><c> team</c>

00:03:17.470 --> 00:03:17.480 align:start position:0%
involved goal it's come up with a team
 

00:03:17.480 --> 00:03:19.670 align:start position:0%
involved goal it's come up with a team
of<00:03:17.640><c> seven</c><00:03:17.959><c> fairly</c><00:03:18.360><c> specific</c><00:03:18.799><c> agents</c><00:03:19.519><c> we're</c>

00:03:19.670 --> 00:03:19.680 align:start position:0%
of seven fairly specific agents we're
 

00:03:19.680 --> 00:03:21.830 align:start position:0%
of seven fairly specific agents we're
going<00:03:19.840><c> to</c><00:03:20.000><c> delete</c><00:03:20.360><c> our</c><00:03:20.680><c> project</c><00:03:21.080><c> manager</c><00:03:21.640><c> so</c>

00:03:21.830 --> 00:03:21.840 align:start position:0%
going to delete our project manager so
 

00:03:21.840 --> 00:03:23.430 align:start position:0%
going to delete our project manager so
there's<00:03:22.040><c> no</c><00:03:22.360><c> power</c><00:03:22.680><c> struggle</c><00:03:23.200><c> with</c><00:03:23.319><c> the</c>

00:03:23.430 --> 00:03:23.440 align:start position:0%
there's no power struggle with the
 

00:03:23.440 --> 00:03:25.430 align:start position:0%
there's no power struggle with the
workflow<00:03:24.080><c> manager</c><00:03:24.640><c> in</c>

00:03:25.430 --> 00:03:25.440 align:start position:0%
workflow manager in
 

00:03:25.440 --> 00:03:28.429 align:start position:0%
workflow manager in
autogen<00:03:26.440><c> and</c><00:03:26.640><c> since</c><00:03:26.879><c> we</c><00:03:27.040><c> want</c><00:03:27.280><c> to</c><00:03:27.720><c> export</c><00:03:28.159><c> our</c>

00:03:28.429 --> 00:03:28.439 align:start position:0%
autogen and since we want to export our
 

00:03:28.439 --> 00:03:30.750 align:start position:0%
autogen and since we want to export our
customized<00:03:29.120><c> skill</c><00:03:29.439><c> as</c><00:03:29.599><c> well</c>

00:03:30.750 --> 00:03:30.760 align:start position:0%
customized skill as well
 

00:03:30.760 --> 00:03:32.589 align:start position:0%
customized skill as well
I'll<00:03:30.920><c> need</c><00:03:31.080><c> to</c><00:03:31.280><c> make</c><00:03:31.400><c> sure</c><00:03:31.720><c> one</c><00:03:31.840><c> of</c><00:03:31.959><c> our</c><00:03:32.159><c> agents</c>

00:03:32.589 --> 00:03:32.599 align:start position:0%
I'll need to make sure one of our agents
 

00:03:32.599 --> 00:03:34.830 align:start position:0%
I'll need to make sure one of our agents
has<00:03:32.799><c> the</c><00:03:32.920><c> fetch</c><00:03:33.239><c> web</c><00:03:33.480><c> skill</c><00:03:33.920><c> activated</c><00:03:34.519><c> and</c>

00:03:34.830 --> 00:03:34.840 align:start position:0%
has the fetch web skill activated and
 

00:03:34.840 --> 00:03:37.949 align:start position:0%
has the fetch web skill activated and
saved<00:03:35.840><c> so</c><00:03:36.239><c> to</c><00:03:36.480><c> recap</c><00:03:37.000><c> we're</c><00:03:37.200><c> going</c><00:03:37.360><c> to</c><00:03:37.519><c> export</c>

00:03:37.949 --> 00:03:37.959 align:start position:0%
saved so to recap we're going to export
 

00:03:37.959 --> 00:03:39.550 align:start position:0%
saved so to recap we're going to export
all<00:03:38.120><c> six</c><00:03:38.319><c> of</c><00:03:38.480><c> these</c><00:03:38.640><c> agents</c><00:03:39.040><c> any</c><00:03:39.239><c> of</c><00:03:39.360><c> their</c>

00:03:39.550 --> 00:03:39.560 align:start position:0%
all six of these agents any of their
 

00:03:39.560 --> 00:03:41.949 align:start position:0%
all six of these agents any of their
Associated<00:03:40.159><c> skills</c><00:03:41.040><c> and</c><00:03:41.200><c> the</c><00:03:41.319><c> workflow</c><00:03:41.799><c> that</c>

00:03:41.949 --> 00:03:41.959 align:start position:0%
Associated skills and the workflow that
 

00:03:41.959 --> 00:03:43.429 align:start position:0%
Associated skills and the workflow that
ties<00:03:42.319><c> everything</c><00:03:42.799><c> together</c><00:03:43.120><c> with</c><00:03:43.280><c> our</c>

00:03:43.429 --> 00:03:43.439 align:start position:0%
ties everything together with our
 

00:03:43.439 --> 00:03:45.750 align:start position:0%
ties everything together with our
users's<00:03:43.920><c> goal</c><00:03:44.599><c> and</c><00:03:44.760><c> we're</c><00:03:44.920><c> going</c><00:03:45.080><c> to</c><00:03:45.200><c> do</c><00:03:45.360><c> it</c><00:03:45.480><c> in</c>

00:03:45.750 --> 00:03:45.760 align:start position:0%
users's goal and we're going to do it in
 

00:03:45.760 --> 00:03:46.789 align:start position:0%
users's goal and we're going to do it in
one

00:03:46.789 --> 00:03:46.799 align:start position:0%
one
 

00:03:46.799 --> 00:03:49.990 align:start position:0%
one
click<00:03:47.799><c> there</c><00:03:48.799><c> we'll</c><00:03:49.040><c> switch</c><00:03:49.439><c> back</c><00:03:49.640><c> over</c><00:03:49.840><c> to</c>

00:03:49.990 --> 00:03:50.000 align:start position:0%
click there we'll switch back over to
 

00:03:50.000 --> 00:03:52.429 align:start position:0%
click there we'll switch back over to
autogen<00:03:50.720><c> and</c><00:03:50.840><c> do</c><00:03:51.000><c> a</c><00:03:51.120><c> hard</c><00:03:51.400><c> refresh</c><00:03:51.879><c> of</c><00:03:52.040><c> the</c><00:03:52.200><c> app</c>

00:03:52.429 --> 00:03:52.439 align:start position:0%
autogen and do a hard refresh of the app
 

00:03:52.439 --> 00:03:55.270 align:start position:0%
autogen and do a hard refresh of the app
by<00:03:52.599><c> holding</c><00:03:52.959><c> down</c><00:03:53.239><c> control</c><00:03:53.799><c> and</c><00:03:53.959><c> hitting</c>

00:03:55.270 --> 00:03:55.280 align:start position:0%
by holding down control and hitting
 

00:03:55.280 --> 00:03:58.710 align:start position:0%
by holding down control and hitting
F5<00:03:56.280><c> there's</c><00:03:56.480><c> our</c><00:03:56.640><c> new</c><00:03:57.319><c> workflow</c><00:03:58.319><c> there's</c><00:03:58.519><c> our</c>

00:03:58.710 --> 00:03:58.720 align:start position:0%
F5 there's our new workflow there's our
 

00:03:58.720 --> 00:04:01.229 align:start position:0%
F5 there's our new workflow there's our
added<00:03:59.079><c> skill</c>

00:04:01.229 --> 00:04:01.239 align:start position:0%
added skill
 

00:04:01.239 --> 00:04:04.509 align:start position:0%
added skill
and<00:04:01.480><c> best</c><00:04:01.680><c> of</c><00:04:02.159><c> all</c><00:04:03.159><c> here</c><00:04:03.400><c> are</c><00:04:03.599><c> a</c><00:04:03.840><c> half</c><00:04:04.159><c> dozen</c>

00:04:04.509 --> 00:04:04.519 align:start position:0%
and best of all here are a half dozen
 

00:04:04.519 --> 00:04:07.229 align:start position:0%
and best of all here are a half dozen
new<00:04:04.840><c> autogen</c><00:04:05.519><c> agents</c><00:04:06.239><c> quite</c><00:04:06.439><c> literally</c><00:04:07.079><c> at</c>

00:04:07.229 --> 00:04:07.239 align:start position:0%
new autogen agents quite literally at
 

00:04:07.239 --> 00:04:09.910 align:start position:0%
new autogen agents quite literally at
the<00:04:07.360><c> click</c><00:04:07.560><c> of</c><00:04:07.640><c> a</c><00:04:08.200><c> button</c><00:04:09.200><c> okay</c><00:04:09.480><c> fine</c><00:04:09.760><c> it</c>

00:04:09.910 --> 00:04:09.920 align:start position:0%
the click of a button okay fine it
 

00:04:09.920 --> 00:04:12.550 align:start position:0%
the click of a button okay fine it
really<00:04:10.159><c> is</c><00:04:10.319><c> more</c><00:04:10.560><c> than</c><00:04:10.760><c> just</c><00:04:10.879><c> a</c><00:04:11.360><c> button</c><00:04:12.360><c> that's</c>

00:04:12.550 --> 00:04:12.560 align:start position:0%
really is more than just a button that's
 

00:04:12.560 --> 00:04:15.589 align:start position:0%
really is more than just a button that's
all<00:04:12.760><c> I'm</c><00:04:13.239><c> saying</c><00:04:14.239><c> okay</c><00:04:14.840><c> that's</c><00:04:15.040><c> all</c><00:04:15.200><c> for</c><00:04:15.439><c> this</c>

00:04:15.589 --> 00:04:15.599 align:start position:0%
all I'm saying okay that's all for this
 

00:04:15.599 --> 00:04:18.110 align:start position:0%
all I'm saying okay that's all for this
week<00:04:16.000><c> please</c><00:04:16.320><c> like</c><00:04:16.519><c> And</c><00:04:16.759><c> subscribe</c><00:04:17.280><c> and</c><00:04:17.400><c> share</c>

00:04:18.110 --> 00:04:18.120 align:start position:0%
week please like And subscribe and share
 

00:04:18.120 --> 00:04:20.189 align:start position:0%
week please like And subscribe and share
better<00:04:18.440><c> yet</c><00:04:19.040><c> invite</c><00:04:19.359><c> one</c><00:04:19.479><c> of</c><00:04:19.600><c> your</c><00:04:19.799><c> favorite</c>

00:04:20.189 --> 00:04:20.199 align:start position:0%
better yet invite one of your favorite
 

00:04:20.199 --> 00:04:22.830 align:start position:0%
better yet invite one of your favorite
social<00:04:20.639><c> influencers</c><00:04:21.280><c> to</c><00:04:21.440><c> review</c><00:04:21.759><c> autog</c><00:04:22.160><c> Gro</c>

00:04:22.830 --> 00:04:22.840 align:start position:0%
social influencers to review autog Gro
 

00:04:22.840 --> 00:04:28.710 align:start position:0%
social influencers to review autog Gro
and<00:04:22.960><c> I'll</c><00:04:23.120><c> see</c><00:04:23.240><c> you</c><00:04:23.360><c> in</c><00:04:23.479><c> the</c><00:04:23.600><c> comment</c>

00:04:28.710 --> 00:04:28.720 align:start position:0%
 
 

00:04:28.720 --> 00:04:33.360 align:start position:0%
 
section<00:04:29.759><c> aut</c><00:04:30.479><c> Auto</c>

