WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:01.350 align:start position:0%
 
hey<00:00:00.240><c> and</c><00:00:00.359><c> welcome</c><00:00:00.560><c> to</c><00:00:00.680><c> another</c><00:00:00.919><c> video</c><00:00:01.199><c> and</c>

00:00:01.350 --> 00:00:01.360 align:start position:0%
hey and welcome to another video and
 

00:00:01.360 --> 00:00:02.790 align:start position:0%
hey and welcome to another video and
today<00:00:01.719><c> I'm</c><00:00:01.839><c> going</c><00:00:01.959><c> to</c><00:00:02.040><c> show</c><00:00:02.200><c> you</c><00:00:02.360><c> how</c><00:00:02.440><c> to</c><00:00:02.560><c> use</c>

00:00:02.790 --> 00:00:02.800 align:start position:0%
today I'm going to show you how to use
 

00:00:02.800 --> 00:00:05.030 align:start position:0%
today I'm going to show you how to use
LM<00:00:03.199><c> Studio</c><00:00:03.760><c> which</c><00:00:03.879><c> will</c><00:00:04.160><c> allow</c><00:00:04.440><c> you</c><00:00:04.560><c> to</c><00:00:04.720><c> use</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
LM Studio which will allow you to use
 

00:00:05.040 --> 00:00:07.710 align:start position:0%
LM Studio which will allow you to use
any<00:00:05.400><c> open-</c><00:00:05.759><c> Source</c><00:00:06.080><c> llm</c><00:00:06.879><c> locally</c><00:00:07.439><c> this</c><00:00:07.520><c> means</c>

00:00:07.710 --> 00:00:07.720 align:start position:0%
any open- Source llm locally this means
 

00:00:07.720 --> 00:00:09.669 align:start position:0%
any open- Source llm locally this means
it'll<00:00:07.919><c> cost</c><00:00:08.160><c> you</c><00:00:08.320><c> nothing</c><00:00:08.639><c> to</c><00:00:08.800><c> run</c><00:00:09.000><c> a</c><00:00:09.120><c> workflow</c>

00:00:09.669 --> 00:00:09.679 align:start position:0%
it'll cost you nothing to run a workflow
 

00:00:09.679 --> 00:00:11.390 align:start position:0%
it'll cost you nothing to run a workflow
we'll<00:00:09.840><c> go</c><00:00:10.000><c> over</c><00:00:10.240><c> LM</c><00:00:10.519><c> studio</c><00:00:10.840><c> and</c><00:00:10.880><c> then</c><00:00:11.000><c> how</c><00:00:11.200><c> it</c>

00:00:11.390 --> 00:00:11.400 align:start position:0%
we'll go over LM studio and then how it
 

00:00:11.400 --> 00:00:13.789 align:start position:0%
we'll go over LM studio and then how it
integrated<00:00:12.000><c> into</c><00:00:12.440><c> an</c><00:00:12.719><c> agent</c><00:00:13.080><c> workflow</c>

00:00:13.789 --> 00:00:13.799 align:start position:0%
integrated into an agent workflow
 

00:00:13.799 --> 00:00:16.150 align:start position:0%
integrated into an agent workflow
instead<00:00:14.040><c> of</c><00:00:14.160><c> using</c><00:00:14.400><c> the</c><00:00:14.599><c> open</c><00:00:14.960><c> AI</c><00:00:15.480><c> API</c><00:00:16.080><c> what</c>

00:00:16.150 --> 00:00:16.160 align:start position:0%
instead of using the open AI API what
 

00:00:16.160 --> 00:00:18.310 align:start position:0%
instead of using the open AI API what
we're<00:00:16.279><c> going</c><00:00:16.400><c> to</c><00:00:16.520><c> do</c><00:00:16.760><c> is</c><00:00:16.960><c> download</c><00:00:17.359><c> LM</c><00:00:17.720><c> Studio</c>

00:00:18.310 --> 00:00:18.320 align:start position:0%
we're going to do is download LM Studio
 

00:00:18.320 --> 00:00:20.990 align:start position:0%
we're going to do is download LM Studio
choose<00:00:18.720><c> a</c><00:00:18.920><c> model</c><00:00:19.320><c> that</c><00:00:19.480><c> we</c><00:00:19.600><c> want</c><00:00:19.760><c> to</c><00:00:20.039><c> load</c><00:00:20.640><c> into</c>

00:00:20.990 --> 00:00:21.000 align:start position:0%
choose a model that we want to load into
 

00:00:21.000 --> 00:00:23.550 align:start position:0%
choose a model that we want to load into
LM<00:00:21.359><c> Studio</c><00:00:22.039><c> start</c><00:00:22.400><c> the</c><00:00:22.560><c> local</c><00:00:22.880><c> server</c><00:00:23.480><c> and</c>

00:00:23.550 --> 00:00:23.560 align:start position:0%
LM Studio start the local server and
 

00:00:23.560 --> 00:00:25.029 align:start position:0%
LM Studio start the local server and
then<00:00:23.680><c> we're</c><00:00:23.800><c> going</c><00:00:23.920><c> to</c><00:00:24.160><c> create</c><00:00:24.480><c> the</c><00:00:24.640><c> agents</c>

00:00:25.029 --> 00:00:25.039 align:start position:0%
then we're going to create the agents
 

00:00:25.039 --> 00:00:27.310 align:start position:0%
then we're going to create the agents
and<00:00:25.240><c> run</c><00:00:25.599><c> the</c><00:00:25.800><c> task</c><00:00:26.359><c> let's</c><00:00:26.560><c> get</c><00:00:26.720><c> started</c><00:00:27.199><c> all</c>

00:00:27.310 --> 00:00:27.320 align:start position:0%
and run the task let's get started all
 

00:00:27.320 --> 00:00:28.669 align:start position:0%
and run the task let's get started all
right<00:00:27.480><c> so</c><00:00:27.679><c> the</c><00:00:27.800><c> first</c><00:00:27.960><c> thing</c><00:00:28.039><c> we</c><00:00:28.160><c> need</c><00:00:28.279><c> to</c><00:00:28.400><c> do</c>

00:00:28.669 --> 00:00:28.679 align:start position:0%
right so the first thing we need to do
 

00:00:28.679 --> 00:00:31.990 align:start position:0%
right so the first thing we need to do
is<00:00:28.880><c> go</c><00:00:29.039><c> to</c><00:00:29.359><c> LM</c><00:00:29.679><c> Studio</c><00:00:30.359><c> .</c><00:00:30.759><c> a</c><00:00:31.599><c> and</c><00:00:31.720><c> then</c><00:00:31.880><c> once</c>

00:00:31.990 --> 00:00:32.000 align:start position:0%
is go to LM Studio . a and then once
 

00:00:32.000 --> 00:00:34.389 align:start position:0%
is go to LM Studio . a and then once
you're<00:00:32.200><c> here</c><00:00:32.680><c> download</c><00:00:33.120><c> LM</c><00:00:33.480><c> studio</c><00:00:33.840><c> for</c><00:00:34.160><c> your</c>

00:00:34.389 --> 00:00:34.399 align:start position:0%
you're here download LM studio for your
 

00:00:34.399 --> 00:00:35.630 align:start position:0%
you're here download LM studio for your
machine<00:00:34.800><c> and</c><00:00:34.879><c> then</c><00:00:35.000><c> once</c><00:00:35.079><c> you've</c><00:00:35.239><c> done</c><00:00:35.440><c> that</c>

00:00:35.630 --> 00:00:35.640 align:start position:0%
machine and then once you've done that
 

00:00:35.640 --> 00:00:36.750 align:start position:0%
machine and then once you've done that
just<00:00:35.800><c> run</c><00:00:36.000><c> it</c><00:00:36.200><c> and</c><00:00:36.280><c> then</c><00:00:36.440><c> this</c><00:00:36.520><c> is</c><00:00:36.640><c> the</c>

00:00:36.750 --> 00:00:36.760 align:start position:0%
just run it and then this is the
 

00:00:36.760 --> 00:00:38.150 align:start position:0%
just run it and then this is the
homepage<00:00:37.239><c> that</c><00:00:37.320><c> you'll</c><00:00:37.480><c> be</c><00:00:37.559><c> greeted</c><00:00:37.879><c> with</c><00:00:38.079><c> now</c>

00:00:38.150 --> 00:00:38.160 align:start position:0%
homepage that you'll be greeted with now
 

00:00:38.160 --> 00:00:40.190 align:start position:0%
homepage that you'll be greeted with now
what<00:00:38.239><c> we</c><00:00:38.360><c> need</c><00:00:38.440><c> to</c><00:00:38.600><c> do</c><00:00:38.879><c> is</c><00:00:39.120><c> download</c><00:00:39.680><c> a</c><00:00:39.840><c> model</c>

00:00:40.190 --> 00:00:40.200 align:start position:0%
what we need to do is download a model
 

00:00:40.200 --> 00:00:41.830 align:start position:0%
what we need to do is download a model
you<00:00:40.320><c> can</c><00:00:40.440><c> choose</c><00:00:40.840><c> any</c><00:00:41.039><c> model</c><00:00:41.360><c> that</c><00:00:41.559><c> you</c><00:00:41.680><c> want</c>

00:00:41.830 --> 00:00:41.840 align:start position:0%
you can choose any model that you want
 

00:00:41.840 --> 00:00:44.110 align:start position:0%
you can choose any model that you want
to<00:00:42.120><c> download</c><00:00:43.000><c> I've</c><00:00:43.160><c> already</c><00:00:43.440><c> downloaded</c><00:00:43.920><c> ones</c>

00:00:44.110 --> 00:00:44.120 align:start position:0%
to download I've already downloaded ones
 

00:00:44.120 --> 00:00:45.590 align:start position:0%
to download I've already downloaded ones
that<00:00:44.239><c> are</c><00:00:44.399><c> small</c><00:00:44.719><c> that</c><00:00:44.840><c> my</c><00:00:45.000><c> computer</c><00:00:45.399><c> can</c>

00:00:45.590 --> 00:00:45.600 align:start position:0%
that are small that my computer can
 

00:00:45.600 --> 00:00:47.430 align:start position:0%
that are small that my computer can
handle<00:00:46.320><c> but</c><00:00:46.440><c> you're</c><00:00:46.640><c> the</c><00:00:46.760><c> more</c><00:00:46.920><c> than</c><00:00:47.120><c> welcome</c>

00:00:47.430 --> 00:00:47.440 align:start position:0%
handle but you're the more than welcome
 

00:00:47.440 --> 00:00:49.790 align:start position:0%
handle but you're the more than welcome
to<00:00:47.719><c> either</c><00:00:48.000><c> scroll</c><00:00:48.399><c> down</c><00:00:48.559><c> on</c><00:00:48.680><c> the</c><00:00:48.840><c> homepage</c><00:00:49.559><c> so</c>

00:00:49.790 --> 00:00:49.800 align:start position:0%
to either scroll down on the homepage so
 

00:00:49.800 --> 00:00:52.430 align:start position:0%
to either scroll down on the homepage so
here<00:00:50.120><c> quen</c><00:00:50.480><c> 1.5</c><00:00:51.480><c> I</c><00:00:51.559><c> don't</c><00:00:51.719><c> have</c><00:00:51.840><c> it</c><00:00:51.960><c> downloaded</c>

00:00:52.430 --> 00:00:52.440 align:start position:0%
here quen 1.5 I don't have it downloaded
 

00:00:52.440 --> 00:00:54.389 align:start position:0%
here quen 1.5 I don't have it downloaded
yet<00:00:52.640><c> so</c><00:00:52.879><c> if</c><00:00:53.000><c> I</c><00:00:53.079><c> wanted</c><00:00:53.320><c> to</c><00:00:53.520><c> I</c><00:00:53.640><c> just</c><00:00:53.960><c> click</c><00:00:54.199><c> this</c>

00:00:54.389 --> 00:00:54.399 align:start position:0%
yet so if I wanted to I just click this
 

00:00:54.399 --> 00:00:56.750 align:start position:0%
yet so if I wanted to I just click this
download<00:00:54.800><c> button</c><00:00:55.559><c> opens</c><00:00:55.840><c> up</c><00:00:55.960><c> a</c><00:00:56.239><c> bar</c><00:00:56.480><c> at</c><00:00:56.600><c> the</c>

00:00:56.750 --> 00:00:56.760 align:start position:0%
download button opens up a bar at the
 

00:00:56.760 --> 00:00:59.549 align:start position:0%
download button opens up a bar at the
bottom<00:00:57.359><c> I</c><00:00:57.440><c> can</c><00:00:57.680><c> open</c><00:00:57.920><c> it</c><00:00:58.120><c> up</c><00:00:58.719><c> and</c><00:00:58.879><c> then</c><00:00:59.320><c> you</c><00:00:59.440><c> can</c>

00:00:59.549 --> 00:00:59.559 align:start position:0%
bottom I can open it up and then you can
 

00:00:59.559 --> 00:01:01.549 align:start position:0%
bottom I can open it up and then you can
see<00:00:59.760><c> that</c><00:01:00.079><c> starting</c><00:01:00.600><c> the</c><00:01:00.800><c> download</c><00:01:01.320><c> you</c><00:01:01.399><c> can</c>

00:01:01.549 --> 00:01:01.559 align:start position:0%
see that starting the download you can
 

00:01:01.559 --> 00:01:04.149 align:start position:0%
see that starting the download you can
scroll<00:01:01.920><c> down</c><00:01:02.120><c> and</c><00:01:02.239><c> choose</c><00:01:02.559><c> any</c><00:01:02.719><c> of</c><00:01:02.920><c> these</c><00:01:03.280><c> or</c>

00:01:04.149 --> 00:01:04.159 align:start position:0%
scroll down and choose any of these or
 

00:01:04.159 --> 00:01:05.870 align:start position:0%
scroll down and choose any of these or
at<00:01:04.280><c> the</c><00:01:04.559><c> top</c><00:01:04.920><c> in</c><00:01:05.040><c> the</c><00:01:05.159><c> middle</c><00:01:05.439><c> here</c><00:01:05.600><c> is</c><00:01:05.720><c> a</c>

00:01:05.870 --> 00:01:05.880 align:start position:0%
at the top in the middle here is a
 

00:01:05.880 --> 00:01:07.910 align:start position:0%
at the top in the middle here is a
search<00:01:06.200><c> bar</c><00:01:06.720><c> you</c><00:01:06.840><c> can</c><00:01:07.000><c> search</c><00:01:07.280><c> for</c><00:01:07.520><c> something</c>

00:01:07.910 --> 00:01:07.920 align:start position:0%
search bar you can search for something
 

00:01:07.920 --> 00:01:10.910 align:start position:0%
search bar you can search for something
like<00:01:08.240><c> mistl</c><00:01:09.119><c> you</c><00:01:09.320><c> click</c><00:01:09.640><c> enter</c><00:01:10.360><c> and</c><00:01:10.520><c> then</c><00:01:10.759><c> on</c>

00:01:10.910 --> 00:01:10.920 align:start position:0%
like mistl you click enter and then on
 

00:01:10.920 --> 00:01:13.070 align:start position:0%
like mistl you click enter and then on
the<00:01:11.119><c> another</c><00:01:11.680><c> page</c><00:01:12.080><c> it'll</c><00:01:12.320><c> show</c><00:01:12.720><c> all</c><00:01:12.920><c> the</c>

00:01:13.070 --> 00:01:13.080 align:start position:0%
the another page it'll show all the
 

00:01:13.080 --> 00:01:15.070 align:start position:0%
the another page it'll show all the
results<00:01:13.439><c> on</c><00:01:13.640><c> hugging</c><00:01:14.040><c> face</c><00:01:14.240><c> for</c><00:01:14.439><c> mistal</c>

00:01:15.070 --> 00:01:15.080 align:start position:0%
results on hugging face for mistal
 

00:01:15.080 --> 00:01:16.990 align:start position:0%
results on hugging face for mistal
models<00:01:15.680><c> on</c><00:01:15.799><c> the</c><00:01:15.960><c> left-</c><00:01:16.200><c> hand</c><00:01:16.400><c> side</c><00:01:16.600><c> you</c><00:01:16.720><c> can</c>

00:01:16.990 --> 00:01:17.000 align:start position:0%
models on the left- hand side you can
 

00:01:17.000 --> 00:01:19.590 align:start position:0%
models on the left- hand side you can
also<00:01:17.360><c> go</c><00:01:17.520><c> to</c><00:01:18.000><c> AI</c><00:01:18.479><c> chat</c><00:01:18.920><c> and</c><00:01:19.040><c> then</c><00:01:19.200><c> you</c><00:01:19.320><c> can</c>

00:01:19.590 --> 00:01:19.600 align:start position:0%
also go to AI chat and then you can
 

00:01:19.600 --> 00:01:21.670 align:start position:0%
also go to AI chat and then you can
simply<00:01:20.200><c> play</c><00:01:20.400><c> with</c><00:01:20.520><c> your</c><00:01:20.680><c> model</c><00:01:21.200><c> you</c><00:01:21.280><c> can</c><00:01:21.560><c> at</c>

00:01:21.670 --> 00:01:21.680 align:start position:0%
simply play with your model you can at
 

00:01:21.680 --> 00:01:23.749 align:start position:0%
simply play with your model you can at
the<00:01:21.799><c> top</c><00:01:22.000><c> here</c><00:01:22.200><c> select</c><00:01:22.439><c> a</c><00:01:22.600><c> model</c><00:01:22.880><c> to</c><00:01:23.040><c> load</c><00:01:23.520><c> load</c>

00:01:23.749 --> 00:01:23.759 align:start position:0%
the top here select a model to load load
 

00:01:23.759 --> 00:01:26.109 align:start position:0%
the top here select a model to load load
it<00:01:24.000><c> up</c><00:01:24.320><c> then</c><00:01:24.479><c> once</c><00:01:24.680><c> it's</c><00:01:24.840><c> loaded</c><00:01:25.520><c> you</c><00:01:25.680><c> can</c><00:01:25.880><c> just</c>

00:01:26.109 --> 00:01:26.119 align:start position:0%
it up then once it's loaded you can just
 

00:01:26.119 --> 00:01:27.910 align:start position:0%
it up then once it's loaded you can just
start<00:01:26.400><c> chatting</c><00:01:26.840><c> with</c><00:01:26.960><c> it</c><00:01:27.119><c> down</c><00:01:27.360><c> here</c><00:01:27.600><c> so</c><00:01:27.799><c> what</c>

00:01:27.910 --> 00:01:27.920 align:start position:0%
start chatting with it down here so what
 

00:01:27.920 --> 00:01:30.350 align:start position:0%
start chatting with it down here so what
we<00:01:28.079><c> need</c><00:01:28.280><c> to</c><00:01:28.479><c> do</c><00:01:28.960><c> is</c><00:01:29.119><c> on</c><00:01:29.200><c> the</c><00:01:29.320><c> left</c><00:01:29.520><c> hand</c><00:01:29.640><c> side</c>

00:01:30.350 --> 00:01:30.360 align:start position:0%
we need to do is on the left hand side
 

00:01:30.360 --> 00:01:32.149 align:start position:0%
we need to do is on the left hand side
there's<00:01:30.560><c> this</c><00:01:30.759><c> double</c><00:01:31.079><c> arrow</c><00:01:31.520><c> here</c><00:01:31.840><c> this</c><00:01:32.000><c> is</c>

00:01:32.149 --> 00:01:32.159 align:start position:0%
there's this double arrow here this is
 

00:01:32.159 --> 00:01:35.190 align:start position:0%
there's this double arrow here this is
for<00:01:32.360><c> local</c><00:01:32.680><c> server</c><00:01:33.360><c> so</c><00:01:33.759><c> click</c><00:01:34.399><c> that</c><00:01:34.799><c> and</c><00:01:35.000><c> now</c>

00:01:35.190 --> 00:01:35.200 align:start position:0%
for local server so click that and now
 

00:01:35.200 --> 00:01:36.830 align:start position:0%
for local server so click that and now
what<00:01:35.280><c> you</c><00:01:35.399><c> can</c><00:01:35.560><c> do</c><00:01:35.799><c> at</c><00:01:35.920><c> the</c><00:01:36.079><c> top</c><00:01:36.360><c> again</c><00:01:36.560><c> is</c><00:01:36.680><c> the</c>

00:01:36.830 --> 00:01:36.840 align:start position:0%
what you can do at the top again is the
 

00:01:36.840 --> 00:01:39.630 align:start position:0%
what you can do at the top again is the
same<00:01:37.079><c> select</c><00:01:37.360><c> the</c><00:01:37.479><c> model</c><00:01:37.720><c> to</c><00:01:37.920><c> load</c><00:01:38.799><c> click</c><00:01:39.280><c> this</c>

00:01:39.630 --> 00:01:39.640 align:start position:0%
same select the model to load click this
 

00:01:39.640 --> 00:01:42.109 align:start position:0%
same select the model to load click this
and<00:01:39.880><c> choose</c><00:01:40.360><c> a</c><00:01:40.600><c> model</c><00:01:41.079><c> that</c><00:01:41.200><c> you</c><00:01:41.320><c> want</c><00:01:41.439><c> to</c><00:01:41.640><c> use</c>

00:01:42.109 --> 00:01:42.119 align:start position:0%
and choose a model that you want to use
 

00:01:42.119 --> 00:01:43.550 align:start position:0%
and choose a model that you want to use
I'm<00:01:42.240><c> just</c><00:01:42.360><c> going</c><00:01:42.439><c> to</c><00:01:42.520><c> use</c><00:01:42.680><c> the</c><00:01:42.759><c> 52</c><00:01:43.240><c> model</c>

00:01:43.550 --> 00:01:43.560 align:start position:0%
I'm just going to use the 52 model
 

00:01:43.560 --> 00:01:45.670 align:start position:0%
I'm just going to use the 52 model
because<00:01:43.759><c> it's</c><00:01:44.000><c> small</c><00:01:44.399><c> for</c><00:01:44.600><c> my</c><00:01:44.759><c> machine</c><00:01:45.439><c> so</c><00:01:45.600><c> I'm</c>

00:01:45.670 --> 00:01:45.680 align:start position:0%
because it's small for my machine so I'm
 

00:01:45.680 --> 00:01:47.429 align:start position:0%
because it's small for my machine so I'm
going<00:01:45.759><c> to</c><00:01:45.960><c> click</c><00:01:46.240><c> this</c><00:01:46.560><c> it's</c><00:01:46.680><c> going</c><00:01:46.799><c> to</c><00:01:47.079><c> start</c>

00:01:47.429 --> 00:01:47.439 align:start position:0%
going to click this it's going to start
 

00:01:47.439 --> 00:01:49.709 align:start position:0%
going to click this it's going to start
the<00:01:47.600><c> load</c><00:01:48.079><c> process</c><00:01:48.680><c> and</c><00:01:48.880><c> whenever</c><00:01:49.280><c> that's</c>

00:01:49.709 --> 00:01:49.719 align:start position:0%
the load process and whenever that's
 

00:01:49.719 --> 00:01:51.789 align:start position:0%
the load process and whenever that's
done<00:01:50.280><c> this</c><00:01:50.479><c> start</c><00:01:50.799><c> server</c><00:01:51.200><c> right</c><00:01:51.360><c> here</c><00:01:51.600><c> this</c>

00:01:51.789 --> 00:01:51.799 align:start position:0%
done this start server right here this
 

00:01:51.799 --> 00:01:54.030 align:start position:0%
done this start server right here this
button<00:01:52.159><c> will</c><00:01:52.439><c> light</c><00:01:52.759><c> up</c><00:01:53.040><c> green</c><00:01:53.600><c> all</c><00:01:53.799><c> you</c><00:01:53.920><c> need</c>

00:01:54.030 --> 00:01:54.040 align:start position:0%
button will light up green all you need
 

00:01:54.040 --> 00:01:56.990 align:start position:0%
button will light up green all you need
to<00:01:54.200><c> do</c><00:01:54.520><c> is</c><00:01:54.680><c> just</c><00:01:54.920><c> click</c><00:01:55.439><c> Start</c><00:01:56.159><c> server</c><00:01:56.799><c> and</c>

00:01:56.990 --> 00:01:57.000 align:start position:0%
to do is just click Start server and
 

00:01:57.000 --> 00:02:00.389 align:start position:0%
to do is just click Start server and
that's<00:01:57.200><c> it</c><00:01:57.680><c> now</c><00:01:57.880><c> we</c><00:01:58.000><c> can</c><00:01:58.320><c> use</c><00:01:58.960><c> this</c><00:01:59.200><c> model</c><00:02:00.119><c> in</c>

00:02:00.389 --> 00:02:00.399 align:start position:0%
that's it now we can use this model in
 

00:02:00.399 --> 00:02:02.429 align:start position:0%
that's it now we can use this model in
our<00:02:00.719><c> agent</c><00:02:01.079><c> workflow</c><00:02:01.680><c> the</c><00:02:01.799><c> one</c><00:02:02.039><c> thing</c><00:02:02.200><c> we'll</c>

00:02:02.429 --> 00:02:02.439 align:start position:0%
our agent workflow the one thing we'll
 

00:02:02.439 --> 00:02:06.950 align:start position:0%
our agent workflow the one thing we'll
need<00:02:02.880><c> here</c><00:02:03.560><c> is</c><00:02:03.960><c> this</c><00:02:04.399><c> URL</c><00:02:05.200><c> this</c><00:02:05.479><c> base</c><00:02:05.880><c> URL</c><00:02:06.759><c> is</c>

00:02:06.950 --> 00:02:06.960 align:start position:0%
need here is this URL this base URL is
 

00:02:06.960 --> 00:02:09.270 align:start position:0%
need here is this URL this base URL is
what<00:02:07.119><c> we'll</c><00:02:07.360><c> use</c><00:02:07.759><c> in</c><00:02:07.880><c> the</c><00:02:08.080><c> config</c><00:02:08.599><c> list</c><00:02:09.160><c> in</c>

00:02:09.270 --> 00:02:09.280 align:start position:0%
what we'll use in the config list in
 

00:02:09.280 --> 00:02:11.750 align:start position:0%
what we'll use in the config list in
order<00:02:09.599><c> to</c><00:02:09.840><c> connect</c><00:02:10.679><c> from</c><00:02:11.120><c> the</c><00:02:11.319><c> assistant</c>

00:02:11.750 --> 00:02:11.760 align:start position:0%
order to connect from the assistant
 

00:02:11.760 --> 00:02:14.430 align:start position:0%
order to connect from the assistant
agents<00:02:12.239><c> to</c><00:02:12.520><c> call</c><00:02:12.920><c> this</c><00:02:13.120><c> llm</c><00:02:13.879><c> now</c><00:02:14.000><c> let's</c><00:02:14.160><c> go</c><00:02:14.319><c> do</c>

00:02:14.430 --> 00:02:14.440 align:start position:0%
agents to call this llm now let's go do
 

00:02:14.440 --> 00:02:15.910 align:start position:0%
agents to call this llm now let's go do
some<00:02:14.560><c> coding</c><00:02:14.959><c> how</c><00:02:15.040><c> we're</c><00:02:15.160><c> going</c><00:02:15.239><c> to</c><00:02:15.360><c> do</c><00:02:15.519><c> that</c>

00:02:15.910 --> 00:02:15.920 align:start position:0%
some coding how we're going to do that
 

00:02:15.920 --> 00:02:18.470 align:start position:0%
some coding how we're going to do that
is<00:02:16.120><c> we</c><00:02:16.400><c> first</c><00:02:16.760><c> need</c><00:02:16.959><c> to</c><00:02:17.160><c> create</c><00:02:17.480><c> a</c><00:02:17.680><c> main</c><00:02:18.040><c> python</c>

00:02:18.470 --> 00:02:18.480 align:start position:0%
is we first need to create a main python
 

00:02:18.480 --> 00:02:21.509 align:start position:0%
is we first need to create a main python
file<00:02:19.200><c> and</c><00:02:19.319><c> then</c><00:02:19.440><c> an</c><00:02:19.640><c> openai</c><00:02:20.400><c> config</c><00:02:20.879><c> list</c><00:02:21.400><c> and</c>

00:02:21.509 --> 00:02:21.519 align:start position:0%
file and then an openai config list and
 

00:02:21.519 --> 00:02:22.670 align:start position:0%
file and then an openai config list and
if<00:02:21.599><c> you</c><00:02:21.720><c> haven't</c><00:02:21.959><c> already</c><00:02:22.239><c> go</c><00:02:22.360><c> ahead</c><00:02:22.560><c> and</c>

00:02:22.670 --> 00:02:22.680 align:start position:0%
if you haven't already go ahead and
 

00:02:22.680 --> 00:02:25.869 align:start position:0%
if you haven't already go ahead and
create<00:02:22.959><c> the</c><00:02:23.160><c> oi</c><00:02:23.840><c> config</c><00:02:24.239><c> list.</c><00:02:24.640><c> Json</c><00:02:25.120><c> file</c><00:02:25.760><c> and</c>

00:02:25.869 --> 00:02:25.879 align:start position:0%
create the oi config list. Json file and
 

00:02:25.879 --> 00:02:28.110 align:start position:0%
create the oi config list. Json file and
then<00:02:26.280><c> all</c><00:02:26.440><c> we</c><00:02:26.599><c> need</c><00:02:26.840><c> here</c><00:02:27.200><c> really</c><00:02:27.480><c> is</c><00:02:27.640><c> the</c><00:02:27.800><c> base</c>

00:02:28.110 --> 00:02:28.120 align:start position:0%
then all we need here really is the base
 

00:02:28.120 --> 00:02:32.150 align:start position:0%
then all we need here really is the base
URL<00:02:29.000><c> you</c><00:02:29.160><c> do</c><00:02:29.360><c> need</c><00:02:29.560><c> the</c><00:02:30.040><c> API</c><00:02:30.760><c> key</c><00:02:31.280><c> property</c><00:02:31.920><c> but</c>

00:02:32.150 --> 00:02:32.160 align:start position:0%
URL you do need the API key property but
 

00:02:32.160 --> 00:02:33.550 align:start position:0%
URL you do need the API key property but
you<00:02:32.239><c> can</c><00:02:32.440><c> literally</c><00:02:32.760><c> put</c><00:02:32.959><c> anything</c><00:02:33.319><c> here</c>

00:02:33.550 --> 00:02:33.560 align:start position:0%
you can literally put anything here
 

00:02:33.560 --> 00:02:35.350 align:start position:0%
you can literally put anything here
because<00:02:33.760><c> we're</c><00:02:33.920><c> not</c><00:02:34.080><c> using</c><00:02:34.400><c> a</c><00:02:34.599><c> key</c><00:02:35.000><c> and</c><00:02:35.160><c> also</c>

00:02:35.350 --> 00:02:35.360 align:start position:0%
because we're not using a key and also
 

00:02:35.360 --> 00:02:37.229 align:start position:0%
because we're not using a key and also
for<00:02:35.519><c> the</c><00:02:35.640><c> model</c><00:02:36.200><c> it</c><00:02:36.319><c> doesn't</c><00:02:36.599><c> matter</c><00:02:36.920><c> you</c><00:02:37.040><c> can</c>

00:02:37.229 --> 00:02:37.239 align:start position:0%
for the model it doesn't matter you can
 

00:02:37.239 --> 00:02:38.949 align:start position:0%
for the model it doesn't matter you can
exclude<00:02:37.640><c> this</c><00:02:37.959><c> or</c><00:02:38.160><c> if</c><00:02:38.239><c> you</c><00:02:38.319><c> feel</c><00:02:38.599><c> better</c>

00:02:38.949 --> 00:02:38.959 align:start position:0%
exclude this or if you feel better
 

00:02:38.959 --> 00:02:40.910 align:start position:0%
exclude this or if you feel better
having<00:02:39.159><c> it</c><00:02:39.760><c> just</c><00:02:40.120><c> put</c><00:02:40.319><c> whatever</c><00:02:40.599><c> you</c><00:02:40.680><c> want</c>

00:02:40.910 --> 00:02:40.920 align:start position:0%
having it just put whatever you want
 

00:02:40.920 --> 00:02:42.670 align:start position:0%
having it just put whatever you want
here<00:02:41.159><c> it</c><00:02:41.280><c> doesn't</c><00:02:41.599><c> matter</c><00:02:42.120><c> and</c><00:02:42.200><c> now</c><00:02:42.360><c> for</c><00:02:42.519><c> the</c>

00:02:42.670 --> 00:02:42.680 align:start position:0%
here it doesn't matter and now for the
 

00:02:42.680 --> 00:02:45.350 align:start position:0%
here it doesn't matter and now for the
main<00:02:43.080><c> python</c><00:02:43.519><c> file</c><00:02:44.239><c> what</c><00:02:44.400><c> we</c><00:02:44.519><c> need</c><00:02:44.640><c> to</c><00:02:44.800><c> do</c><00:02:45.159><c> is</c>

00:02:45.350 --> 00:02:45.360 align:start position:0%
main python file what we need to do is
 

00:02:45.360 --> 00:02:47.190 align:start position:0%
main python file what we need to do is
import<00:02:45.800><c> autogen</c><00:02:46.480><c> and</c><00:02:46.599><c> if</c><00:02:46.680><c> you</c><00:02:46.800><c> haven't</c><00:02:47.040><c> done</c>

00:02:47.190 --> 00:02:47.200 align:start position:0%
import autogen and if you haven't done
 

00:02:47.200 --> 00:02:49.070 align:start position:0%
import autogen and if you haven't done
so<00:02:47.440><c> already</c><00:02:47.840><c> we</c><00:02:47.959><c> need</c><00:02:48.120><c> to</c><00:02:48.440><c> install</c><00:02:48.920><c> the</c>

00:02:49.070 --> 00:02:49.080 align:start position:0%
so already we need to install the
 

00:02:49.080 --> 00:02:51.030 align:start position:0%
so already we need to install the
library<00:02:49.720><c> open</c><00:02:49.920><c> up</c><00:02:50.040><c> your</c><00:02:50.200><c> terminal</c><00:02:50.800><c> and</c><00:02:50.920><c> just</c>

00:02:51.030 --> 00:02:51.040 align:start position:0%
library open up your terminal and just
 

00:02:51.040 --> 00:02:53.630 align:start position:0%
library open up your terminal and just
simply<00:02:51.400><c> type</c><00:02:51.560><c> in</c><00:02:51.840><c> PIP</c><00:02:52.159><c> install</c><00:02:52.640><c> pi</c><00:02:52.959><c> autogen</c>

00:02:53.630 --> 00:02:53.640 align:start position:0%
simply type in PIP install pi autogen
 

00:02:53.640 --> 00:02:55.430 align:start position:0%
simply type in PIP install pi autogen
and<00:02:53.800><c> once</c><00:02:53.879><c> you're</c><00:02:54.040><c> done</c><00:02:54.200><c> with</c><00:02:54.400><c> that</c><00:02:54.879><c> we</c><00:02:55.040><c> have</c><00:02:55.159><c> a</c>

00:02:55.430 --> 00:02:55.440 align:start position:0%
and once you're done with that we have a
 

00:02:55.440 --> 00:02:57.550 align:start position:0%
and once you're done with that we have a
pretty<00:02:55.800><c> simple</c><00:02:56.159><c> workflow</c><00:02:56.640><c> going</c><00:02:56.840><c> on</c><00:02:57.040><c> here</c><00:02:57.400><c> so</c>

00:02:57.550 --> 00:02:57.560 align:start position:0%
pretty simple workflow going on here so
 

00:02:57.560 --> 00:02:58.990 align:start position:0%
pretty simple workflow going on here so
we<00:02:57.680><c> have</c><00:02:57.800><c> a</c><00:02:57.959><c> main</c><00:02:58.280><c> function</c><00:02:58.680><c> that</c><00:02:58.760><c> we're</c><00:02:58.879><c> going</c>

00:02:58.990 --> 00:02:59.000 align:start position:0%
we have a main function that we're going
 

00:02:59.000 --> 00:03:00.990 align:start position:0%
we have a main function that we're going
to<00:02:59.080><c> end</c><00:02:59.200><c> up</c><00:02:59.360><c> calling</c><00:03:00.120><c> and</c><00:03:00.239><c> we</c><00:03:00.360><c> have</c><00:03:00.440><c> a</c><00:03:00.599><c> config</c>

00:03:00.990 --> 00:03:01.000 align:start position:0%
to end up calling and we have a config
 

00:03:01.000 --> 00:03:03.789 align:start position:0%
to end up calling and we have a config
list<00:03:01.360><c> that</c><00:03:01.480><c> calls</c><00:03:01.760><c> autogen</c><00:03:02.560><c> config</c><00:03:02.959><c> list</c><00:03:03.280><c> from</c>

00:03:03.789 --> 00:03:03.799 align:start position:0%
list that calls autogen config list from
 

00:03:03.799 --> 00:03:07.229 align:start position:0%
list that calls autogen config list from
Json<00:03:04.799><c> which</c><00:03:05.040><c> takes</c><00:03:05.400><c> in</c><00:03:05.879><c> that</c><00:03:06.080><c> Json</c><00:03:06.879><c> config</c>

00:03:07.229 --> 00:03:07.239 align:start position:0%
Json which takes in that Json config
 

00:03:07.239 --> 00:03:09.390 align:start position:0%
Json which takes in that Json config
list<00:03:07.480><c> file</c><00:03:07.840><c> we</c><00:03:08.040><c> created</c><00:03:08.560><c> earlier</c><00:03:09.200><c> and</c><00:03:09.319><c> that</c>

00:03:09.390 --> 00:03:09.400 align:start position:0%
list file we created earlier and that
 

00:03:09.400 --> 00:03:12.110 align:start position:0%
list file we created earlier and that
will<00:03:09.599><c> give</c><00:03:09.720><c> us</c><00:03:09.959><c> the</c><00:03:10.159><c> model</c><00:03:10.799><c> and</c><00:03:10.920><c> the</c><00:03:11.080><c> API</c><00:03:11.599><c> key</c>

00:03:12.110 --> 00:03:12.120 align:start position:0%
will give us the model and the API key
 

00:03:12.120 --> 00:03:14.630 align:start position:0%
will give us the model and the API key
and<00:03:12.400><c> also</c><00:03:12.720><c> now</c><00:03:13.000><c> the</c><00:03:13.200><c> base</c><00:03:13.560><c> URL</c><00:03:14.319><c> and</c><00:03:14.440><c> now</c><00:03:14.519><c> we</c>

00:03:14.630 --> 00:03:14.640 align:start position:0%
and also now the base URL and now we
 

00:03:14.640 --> 00:03:17.110 align:start position:0%
and also now the base URL and now we
have<00:03:14.720><c> our</c><00:03:14.920><c> assistant</c><00:03:15.319><c> agent</c><00:03:16.080><c> with</c><00:03:16.360><c> the</c><00:03:16.560><c> llm</c>

00:03:17.110 --> 00:03:17.120 align:start position:0%
have our assistant agent with the llm
 

00:03:17.120 --> 00:03:19.309 align:start position:0%
have our assistant agent with the llm
config<00:03:17.640><c> only</c><00:03:18.040><c> passing</c><00:03:18.319><c> in</c><00:03:18.480><c> the</c><00:03:18.640><c> config</c><00:03:19.040><c> list</c>

00:03:19.309 --> 00:03:19.319 align:start position:0%
config only passing in the config list
 

00:03:19.319 --> 00:03:20.830 align:start position:0%
config only passing in the config list
property<00:03:19.799><c> and</c><00:03:19.879><c> then</c><00:03:19.959><c> we</c><00:03:20.080><c> have</c><00:03:20.159><c> the</c><00:03:20.239><c> user</c><00:03:20.480><c> proxy</c>

00:03:20.830 --> 00:03:20.840 align:start position:0%
property and then we have the user proxy
 

00:03:20.840 --> 00:03:22.949 align:start position:0%
property and then we have the user proxy
agent<00:03:21.120><c> and</c><00:03:21.239><c> the</c><00:03:21.360><c> code</c><00:03:21.640><c> execution</c><00:03:22.120><c> config</c><00:03:22.840><c> we</c>

00:03:22.949 --> 00:03:22.959 align:start position:0%
agent and the code execution config we
 

00:03:22.959 --> 00:03:25.070 align:start position:0%
agent and the code execution config we
have<00:03:23.120><c> the</c><00:03:23.280><c> work</c><00:03:23.760><c> directory</c><00:03:24.200><c> to</c><00:03:24.440><c> coding</c><00:03:24.879><c> so</c>

00:03:25.070 --> 00:03:25.080 align:start position:0%
have the work directory to coding so
 

00:03:25.080 --> 00:03:27.190 align:start position:0%
have the work directory to coding so
there's<00:03:25.319><c> any</c><00:03:25.560><c> code</c><00:03:25.959><c> that</c><00:03:26.400><c> is</c><00:03:26.640><c> executed</c><00:03:27.120><c> it</c>

00:03:27.190 --> 00:03:27.200 align:start position:0%
there's any code that is executed it
 

00:03:27.200 --> 00:03:29.149 align:start position:0%
there's any code that is executed it
will<00:03:27.360><c> be</c><00:03:27.519><c> stored</c><00:03:28.040><c> there</c><00:03:28.519><c> and</c><00:03:28.640><c> we</c><00:03:28.760><c> say</c><00:03:28.920><c> use</c>

00:03:29.149 --> 00:03:29.159 align:start position:0%
will be stored there and we say use
 

00:03:29.159 --> 00:03:30.910 align:start position:0%
will be stored there and we say use
Docker<00:03:29.439><c> equal</c><00:03:29.959><c> false</c><00:03:30.239><c> you</c><00:03:30.319><c> can</c><00:03:30.439><c> also</c><00:03:30.640><c> set</c><00:03:30.799><c> this</c>

00:03:30.910 --> 00:03:30.920 align:start position:0%
Docker equal false you can also set this
 

00:03:30.920 --> 00:03:33.390 align:start position:0%
Docker equal false you can also set this
to<00:03:31.080><c> true</c><00:03:31.480><c> if</c><00:03:31.640><c> you</c><00:03:31.959><c> have</c><00:03:32.200><c> Docker</c><00:03:32.720><c> installed</c><00:03:33.280><c> and</c>

00:03:33.390 --> 00:03:33.400 align:start position:0%
to true if you have Docker installed and
 

00:03:33.400 --> 00:03:34.509 align:start position:0%
to true if you have Docker installed and
you're<00:03:33.519><c> running</c><00:03:33.879><c> and</c><00:03:33.959><c> then</c><00:03:34.040><c> we</c><00:03:34.159><c> simply</c>

00:03:34.509 --> 00:03:34.519 align:start position:0%
you're running and then we simply
 

00:03:34.519 --> 00:03:36.070 align:start position:0%
you're running and then we simply
initiate<00:03:34.879><c> the</c><00:03:34.959><c> chat</c><00:03:35.200><c> to</c><00:03:35.360><c> have</c><00:03:35.480><c> it</c><00:03:35.599><c> generate</c><00:03:35.959><c> a</c>

00:03:36.070 --> 00:03:36.080 align:start position:0%
initiate the chat to have it generate a
 

00:03:36.080 --> 00:03:38.030 align:start position:0%
initiate the chat to have it generate a
function<00:03:36.400><c> for</c><00:03:36.680><c> us</c><00:03:36.959><c> so</c><00:03:37.120><c> all</c><00:03:37.239><c> we</c><00:03:37.360><c> need</c><00:03:37.480><c> to</c><00:03:37.560><c> do</c><00:03:37.840><c> now</c>

00:03:38.030 --> 00:03:38.040 align:start position:0%
function for us so all we need to do now
 

00:03:38.040 --> 00:03:39.830 align:start position:0%
function for us so all we need to do now
is<00:03:38.200><c> just</c><00:03:38.360><c> run</c><00:03:38.680><c> this</c><00:03:39.080><c> and</c><00:03:39.200><c> now</c><00:03:39.319><c> when</c><00:03:39.439><c> we</c><00:03:39.560><c> run</c>

00:03:39.830 --> 00:03:39.840 align:start position:0%
is just run this and now when we run
 

00:03:39.840 --> 00:03:41.750 align:start position:0%
is just run this and now when we run
this<00:03:40.280><c> we</c><00:03:40.400><c> see</c><00:03:40.799><c> we're</c><00:03:41.000><c> going</c><00:03:41.120><c> to</c><00:03:41.280><c> talk</c><00:03:41.439><c> to</c><00:03:41.599><c> the</c>

00:03:41.750 --> 00:03:41.760 align:start position:0%
this we see we're going to talk to the
 

00:03:41.760 --> 00:03:43.270 align:start position:0%
this we see we're going to talk to the
assistant<00:03:42.120><c> have</c><00:03:42.239><c> it</c><00:03:42.360><c> generate</c><00:03:42.720><c> a</c><00:03:42.879><c> function</c>

00:03:43.270 --> 00:03:43.280 align:start position:0%
assistant have it generate a function
 

00:03:43.280 --> 00:03:45.990 align:start position:0%
assistant have it generate a function
for<00:03:43.480><c> us</c><00:03:43.879><c> if</c><00:03:44.000><c> we</c><00:03:44.159><c> go</c><00:03:44.400><c> back</c><00:03:44.560><c> to</c><00:03:44.720><c> LM</c><00:03:45.080><c> Studio</c><00:03:45.799><c> we</c><00:03:45.879><c> can</c>

00:03:45.990 --> 00:03:46.000 align:start position:0%
for us if we go back to LM Studio we can
 

00:03:46.000 --> 00:03:47.589 align:start position:0%
for us if we go back to LM Studio we can
see<00:03:46.159><c> that</c><00:03:46.280><c> it's</c><00:03:46.480><c> already</c><00:03:46.840><c> started</c><00:03:47.360><c> and</c><00:03:47.439><c> it's</c>

00:03:47.589 --> 00:03:47.599 align:start position:0%
see that it's already started and it's
 

00:03:47.599 --> 00:03:49.070 align:start position:0%
see that it's already started and it's
going<00:03:47.720><c> to</c><00:03:47.879><c> generate</c><00:03:48.200><c> a</c><00:03:48.360><c> function</c><00:03:48.680><c> that</c><00:03:48.840><c> acts</c>

00:03:49.070 --> 00:03:49.080 align:start position:0%
going to generate a function that acts
 

00:03:49.080 --> 00:03:50.789 align:start position:0%
going to generate a function that acts
like<00:03:49.200><c> an</c><00:03:49.360><c> eightball</c><00:03:49.799><c> with</c><00:03:50.000><c> random</c><00:03:50.319><c> phrases</c>

00:03:50.789 --> 00:03:50.799 align:start position:0%
like an eightball with random phrases
 

00:03:50.799 --> 00:03:52.390 align:start position:0%
like an eightball with random phrases
for<00:03:51.000><c> us</c><00:03:51.439><c> and</c><00:03:51.560><c> it's</c><00:03:51.720><c> already</c><00:03:51.959><c> starting</c><00:03:52.239><c> to</c>

00:03:52.390 --> 00:03:52.400 align:start position:0%
for us and it's already starting to
 

00:03:52.400 --> 00:03:54.589 align:start position:0%
for us and it's already starting to
accumulate<00:03:52.799><c> the</c><00:03:53.000><c> tokens</c><00:03:53.599><c> now</c><00:03:53.840><c> once</c><00:03:54.000><c> it's</c><00:03:54.200><c> done</c>

00:03:54.589 --> 00:03:54.599 align:start position:0%
accumulate the tokens now once it's done
 

00:03:54.599 --> 00:03:56.589 align:start position:0%
accumulate the tokens now once it's done
here<00:03:55.239><c> it's</c><00:03:55.360><c> going</c><00:03:55.480><c> to</c><00:03:55.640><c> send</c><00:03:55.879><c> this</c><00:03:56.120><c> back</c><00:03:56.239><c> to</c><00:03:56.400><c> our</c>

00:03:56.589 --> 00:03:56.599 align:start position:0%
here it's going to send this back to our
 

00:03:56.599 --> 00:03:59.670 align:start position:0%
here it's going to send this back to our
IDE<00:03:57.280><c> okay</c><00:03:57.439><c> now</c><00:03:57.599><c> in</c><00:03:57.720><c> LM</c><00:03:58.200><c> Studio</c><00:03:59.079><c> it</c><00:03:59.280><c> fits</c>

00:03:59.670 --> 00:03:59.680 align:start position:0%
IDE okay now in LM Studio it fits
 

00:03:59.680 --> 00:04:01.869 align:start position:0%
IDE okay now in LM Studio it fits
finished<00:04:00.439><c> okay</c><00:04:00.760><c> so</c><00:04:00.920><c> it's</c><00:04:01.079><c> going</c><00:04:01.200><c> to</c><00:04:01.360><c> send</c><00:04:01.640><c> this</c>

00:04:01.869 --> 00:04:01.879 align:start position:0%
finished okay so it's going to send this
 

00:04:01.879 --> 00:04:04.990 align:start position:0%
finished okay so it's going to send this
now<00:04:02.360><c> back</c><00:04:02.840><c> to</c><00:04:03.400><c> the</c><00:04:03.640><c> user</c><00:04:04.000><c> agent</c><00:04:04.640><c> as</c><00:04:04.760><c> the</c>

00:04:04.990 --> 00:04:05.000 align:start position:0%
now back to the user agent as the
 

00:04:05.000 --> 00:04:06.670 align:start position:0%
now back to the user agent as the
response<00:04:05.720><c> and</c><00:04:05.879><c> the</c><00:04:06.000><c> Assistant</c><00:04:06.360><c> gave</c><00:04:06.519><c> us</c>

00:04:06.670 --> 00:04:06.680 align:start position:0%
response and the Assistant gave us
 

00:04:06.680 --> 00:04:08.869 align:start position:0%
response and the Assistant gave us
something<00:04:07.200><c> back</c><00:04:07.720><c> user</c><00:04:08.120><c> assistant</c><00:04:08.439><c> to</c><00:04:08.560><c> user</c>

00:04:08.869 --> 00:04:08.879 align:start position:0%
something back user assistant to user
 

00:04:08.879 --> 00:04:10.910 align:start position:0%
something back user assistant to user
proxy<00:04:09.360><c> it</c><00:04:09.519><c> generated</c><00:04:10.000><c> a</c><00:04:10.079><c> function</c><00:04:10.360><c> for</c><00:04:10.640><c> us</c>

00:04:10.910 --> 00:04:10.920 align:start position:0%
proxy it generated a function for us
 

00:04:10.920 --> 00:04:12.470 align:start position:0%
proxy it generated a function for us
kind<00:04:11.040><c> of</c><00:04:11.159><c> like</c><00:04:11.280><c> a</c><00:04:11.439><c> magic</c><00:04:11.760><c> apall</c><00:04:12.120><c> where</c><00:04:12.239><c> just</c>

00:04:12.470 --> 00:04:12.480 align:start position:0%
kind of like a magic apall where just
 

00:04:12.480 --> 00:04:14.110 align:start position:0%
kind of like a magic apall where just
returns<00:04:12.720><c> a</c><00:04:12.920><c> random</c><00:04:13.200><c> phrase</c><00:04:13.840><c> and</c><00:04:13.920><c> then</c><00:04:14.040><c> you</c>

00:04:14.110 --> 00:04:14.120 align:start position:0%
returns a random phrase and then you
 

00:04:14.120 --> 00:04:15.949 align:start position:0%
returns a random phrase and then you
could<00:04:14.360><c> continue</c><00:04:14.920><c> the</c><00:04:15.040><c> conversation</c><00:04:15.480><c> here</c><00:04:15.760><c> or</c>

00:04:15.949 --> 00:04:15.959 align:start position:0%
could continue the conversation here or
 

00:04:15.959 --> 00:04:18.310 align:start position:0%
could continue the conversation here or
just<00:04:16.160><c> hit</c><00:04:16.400><c> exit</c><00:04:16.840><c> and</c><00:04:16.959><c> be</c><00:04:17.120><c> done</c><00:04:17.759><c> but</c><00:04:17.959><c> this</c><00:04:18.079><c> is</c>

00:04:18.310 --> 00:04:18.320 align:start position:0%
just hit exit and be done but this is
 

00:04:18.320 --> 00:04:21.030 align:start position:0%
just hit exit and be done but this is
how<00:04:18.639><c> you</c><00:04:18.919><c> connect</c><00:04:19.720><c> LM</c><00:04:20.079><c> Studio</c><00:04:20.519><c> which</c><00:04:20.720><c> you</c><00:04:20.840><c> can</c>

00:04:21.030 --> 00:04:21.040 align:start position:0%
how you connect LM Studio which you can
 

00:04:21.040 --> 00:04:23.670 align:start position:0%
how you connect LM Studio which you can
download<00:04:21.560><c> any</c><00:04:21.799><c> hugging</c><00:04:22.240><c> face</c><00:04:22.919><c> open</c><00:04:23.280><c> source</c>

00:04:23.670 --> 00:04:23.680 align:start position:0%
download any hugging face open source
 

00:04:23.680 --> 00:04:26.510 align:start position:0%
download any hugging face open source
model<00:04:24.320><c> to</c><00:04:24.600><c> use</c><00:04:25.080><c> with</c><00:04:25.240><c> your</c><00:04:25.400><c> agent</c><00:04:25.759><c> workflow</c><00:04:26.400><c> I</c>

00:04:26.510 --> 00:04:26.520 align:start position:0%
model to use with your agent workflow I
 

00:04:26.520 --> 00:04:27.990 align:start position:0%
model to use with your agent workflow I
hope<00:04:26.639><c> it</c><00:04:26.720><c> makes</c><00:04:26.880><c> a</c><00:04:26.960><c> little</c><00:04:27.120><c> more</c><00:04:27.280><c> sense</c><00:04:27.600><c> now</c>

00:04:27.990 --> 00:04:28.000 align:start position:0%
hope it makes a little more sense now
 

00:04:28.000 --> 00:04:30.310 align:start position:0%
hope it makes a little more sense now
how<00:04:28.160><c> we</c><00:04:28.280><c> can</c><00:04:28.440><c> simply</c><00:04:28.800><c> swap</c><00:04:29.160><c> out</c><00:04:29.639><c> using</c><00:04:29.880><c> a</c><00:04:30.000><c> model</c>

00:04:30.310 --> 00:04:30.320 align:start position:0%
how we can simply swap out using a model
 

00:04:30.320 --> 00:04:32.629 align:start position:0%
how we can simply swap out using a model
in<00:04:30.600><c> API</c><00:04:31.120><c> key</c><00:04:31.759><c> which</c><00:04:31.880><c> you</c><00:04:31.960><c> would</c><00:04:32.160><c> use</c><00:04:32.400><c> when</c><00:04:32.560><c> you</c>

00:04:32.629 --> 00:04:32.639 align:start position:0%
in API key which you would use when you
 

00:04:32.639 --> 00:04:36.510 align:start position:0%
in API key which you would use when you
would<00:04:32.840><c> talk</c><00:04:33.080><c> to</c><00:04:33.479><c> gp4</c><00:04:34.479><c> or</c><00:04:34.759><c> 3.5</c><00:04:35.479><c> turbo</c><00:04:36.240><c> and</c><00:04:36.360><c> now</c>

00:04:36.510 --> 00:04:36.520 align:start position:0%
would talk to gp4 or 3.5 turbo and now
 

00:04:36.520 --> 00:04:39.110 align:start position:0%
would talk to gp4 or 3.5 turbo and now
you<00:04:36.600><c> can</c><00:04:36.759><c> use</c><00:04:36.960><c> a</c><00:04:37.160><c> local</c><00:04:37.600><c> open</c><00:04:37.919><c> source</c><00:04:38.199><c> llm</c><00:04:38.960><c> with</c>

00:04:39.110 --> 00:04:39.120 align:start position:0%
you can use a local open source llm with
 

00:04:39.120 --> 00:04:41.749 align:start position:0%
you can use a local open source llm with
LM<00:04:39.479><c> studio</c><00:04:40.080><c> now</c><00:04:40.280><c> there</c><00:04:40.440><c> are</c><00:04:40.840><c> other</c><00:04:41.160><c> libraries</c>

00:04:41.749 --> 00:04:41.759 align:start position:0%
LM studio now there are other libraries
 

00:04:41.759 --> 00:04:43.230 align:start position:0%
LM studio now there are other libraries
or<00:04:41.960><c> other</c><00:04:42.160><c> pieces</c><00:04:42.400><c> of</c><00:04:42.520><c> software</c><00:04:42.880><c> that</c><00:04:42.960><c> you</c><00:04:43.080><c> can</c>

00:04:43.230 --> 00:04:43.240 align:start position:0%
or other pieces of software that you can
 

00:04:43.240 --> 00:04:45.590 align:start position:0%
or other pieces of software that you can
use<00:04:43.680><c> that</c><00:04:43.840><c> also</c><00:04:44.080><c> download</c><00:04:44.440><c> models</c><00:04:45.199><c> and</c><00:04:45.400><c> they</c>

00:04:45.590 --> 00:04:45.600 align:start position:0%
use that also download models and they
 

00:04:45.600 --> 00:04:47.390 align:start position:0%
use that also download models and they
can<00:04:45.720><c> be</c><00:04:45.840><c> performed</c><00:04:46.240><c> in</c><00:04:46.360><c> the</c><00:04:46.520><c> same</c><00:04:46.759><c> exact</c><00:04:47.080><c> way</c>

00:04:47.390 --> 00:04:47.400 align:start position:0%
can be performed in the same exact way
 

00:04:47.400 --> 00:04:49.510 align:start position:0%
can be performed in the same exact way
except<00:04:47.639><c> you'll</c><00:04:47.880><c> have</c><00:04:48.199><c> different</c><00:04:48.560><c> URLs</c><00:04:49.360><c> now</c>

00:04:49.510 --> 00:04:49.520 align:start position:0%
except you'll have different URLs now
 

00:04:49.520 --> 00:04:51.189 align:start position:0%
except you'll have different URLs now
one<00:04:49.639><c> thing</c><00:04:49.759><c> I</c><00:04:49.840><c> want</c><00:04:49.960><c> to</c><00:04:50.120><c> add</c><00:04:50.400><c> here</c><00:04:50.919><c> is</c><00:04:51.039><c> if</c>

00:04:51.189 --> 00:04:51.199 align:start position:0%
one thing I want to add here is if
 

00:04:51.199 --> 00:04:52.830 align:start position:0%
one thing I want to add here is if
something<00:04:51.479><c> isn't</c><00:04:51.759><c> working</c><00:04:52.320><c> and</c><00:04:52.520><c> especially</c>

00:04:52.830 --> 00:04:52.840 align:start position:0%
something isn't working and especially
 

00:04:52.840 --> 00:04:54.830 align:start position:0%
something isn't working and especially
if<00:04:52.960><c> you're</c><00:04:53.080><c> using</c><00:04:53.400><c> function</c><00:04:54.000><c> calling</c><00:04:54.639><c> don't</c>

00:04:54.830 --> 00:04:54.840 align:start position:0%
if you're using function calling don't
 

00:04:54.840 --> 00:04:56.629 align:start position:0%
if you're using function calling don't
worry<00:04:55.160><c> you</c><00:04:55.280><c> won't</c><00:04:55.520><c> be</c><00:04:55.720><c> the</c><00:04:55.919><c> only</c><00:04:56.240><c> one</c><00:04:56.479><c> where</c>

00:04:56.629 --> 00:04:56.639 align:start position:0%
worry you won't be the only one where
 

00:04:56.639 --> 00:04:58.270 align:start position:0%
worry you won't be the only one where
that<00:04:56.800><c> isn't</c><00:04:57.080><c> working</c><00:04:57.720><c> cuz</c><00:04:57.960><c> right</c><00:04:58.080><c> now</c>

00:04:58.270 --> 00:04:58.280 align:start position:0%
that isn't working cuz right now
 

00:04:58.280 --> 00:05:00.029 align:start position:0%
that isn't working cuz right now
function<00:04:58.600><c> calling</c><00:04:58.880><c> seems</c><00:04:59.120><c> to</c><00:04:59.240><c> only</c><00:04:59.720><c> work</c><00:04:59.919><c> with</c>

00:05:00.029 --> 00:05:00.039 align:start position:0%
function calling seems to only work with
 

00:05:00.039 --> 00:05:02.749 align:start position:0%
function calling seems to only work with
the<00:05:00.160><c> open</c><00:05:00.479><c> AI</c><00:05:01.000><c> API</c><00:05:01.840><c> and</c><00:05:02.000><c> not</c><00:05:02.160><c> necessarily</c><00:05:02.639><c> with</c>

00:05:02.749 --> 00:05:02.759 align:start position:0%
the open AI API and not necessarily with
 

00:05:02.759 --> 00:05:05.310 align:start position:0%
the open AI API and not necessarily with
any<00:05:03.080><c> local</c><00:05:03.560><c> open</c><00:05:03.880><c> source</c><00:05:04.160><c> llm</c><00:05:04.840><c> as</c><00:05:04.919><c> I</c><00:05:05.039><c> mentioned</c>

00:05:05.310 --> 00:05:05.320 align:start position:0%
any local open source llm as I mentioned
 

00:05:05.320 --> 00:05:06.629 align:start position:0%
any local open source llm as I mentioned
in<00:05:05.360><c> the</c><00:05:05.520><c> last</c><00:05:05.680><c> video</c><00:05:05.880><c> I'm</c><00:05:06.000><c> trying</c><00:05:06.199><c> to</c><00:05:06.280><c> create</c><00:05:06.520><c> a</c>

00:05:06.629 --> 00:05:06.639 align:start position:0%
in the last video I'm trying to create a
 

00:05:06.639 --> 00:05:08.590 align:start position:0%
in the last video I'm trying to create a
video<00:05:06.840><c> for</c><00:05:07.120><c> every</c><00:05:07.360><c> day</c><00:05:07.600><c> of</c><00:05:07.759><c> this</c><00:05:07.960><c> month</c><00:05:08.360><c> lots</c>

00:05:08.590 --> 00:05:08.600 align:start position:0%
video for every day of this month lots
 

00:05:08.600 --> 00:05:10.430 align:start position:0%
video for every day of this month lots
of<00:05:08.759><c> information</c><00:05:09.160><c> to</c><00:05:09.320><c> share</c><00:05:09.600><c> with</c><00:05:09.720><c> all</c><00:05:09.840><c> of</c><00:05:10.000><c> you</c>

00:05:10.430 --> 00:05:10.440 align:start position:0%
of information to share with all of you
 

00:05:10.440 --> 00:05:12.150 align:start position:0%
of information to share with all of you
just<00:05:10.720><c> like</c><00:05:11.120><c> subscribe</c><00:05:11.560><c> to</c><00:05:11.680><c> help</c><00:05:11.840><c> me</c><00:05:11.960><c> out</c>

00:05:12.150 --> 00:05:12.160 align:start position:0%
just like subscribe to help me out
 

00:05:12.160 --> 00:05:13.710 align:start position:0%
just like subscribe to help me out
here's<00:05:12.320><c> some</c><00:05:12.440><c> more</c><00:05:12.600><c> videos</c><00:05:12.840><c> on</c><00:05:12.960><c> autogen</c><00:05:13.479><c> and</c>

00:05:13.710 --> 00:05:13.720 align:start position:0%
here's some more videos on autogen and
 

00:05:13.720 --> 00:05:15.469 align:start position:0%
here's some more videos on autogen and
AI<00:05:14.320><c> have</c><00:05:14.440><c> a</c><00:05:14.560><c> great</c><00:05:14.800><c> day</c><00:05:14.960><c> and</c><00:05:15.080><c> I'll</c><00:05:15.199><c> see</c><00:05:15.360><c> you</c>

00:05:15.469 --> 00:05:15.479 align:start position:0%
AI have a great day and I'll see you
 

00:05:15.479 --> 00:05:18.000 align:start position:0%
AI have a great day and I'll see you
next<00:05:15.759><c> time</c>

