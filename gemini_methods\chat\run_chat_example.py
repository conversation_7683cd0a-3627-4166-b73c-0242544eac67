#!/usr/bin/env python3
"""
Wrapper script to run the chat examples with proper imports.

This script ensures that all imports work correctly by running from the proper directory.
"""

import asyncio
import sys
from pathlib import Path

def main():
    """Run the chat examples."""
    # Ensure we're in the right directory
    script_dir = Path(__file__).resolve().parent
    
    # Add the current directory to Python path if not already there
    if str(script_dir) not in sys.path:
        sys.path.insert(0, str(script_dir))
    
    try:
        # Import and run the chat examples
        from chat.chat_example import main as chat_example_main
        
        print("🚀 Starting Google GenAI Chat Manager Examples...")
        print("=" * 60)
        
        # Run the examples
        asyncio.run(chat_example_main())
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you're running this script from the gemini_methods directory")
        print("2. Ensure all required dependencies are installed:")
        print("   pip install google-genai asyncpg python-dotenv rich")
        print("3. Set your API key: export GOOGLE_API_KEY='your-key'")
        return 1
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 