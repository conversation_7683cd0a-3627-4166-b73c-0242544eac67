# Specify which programming language modules should be available in the environment
modules = [
    "python-3.12",  # Use Python 3.12 runtime
    "bash",         # Enable bash shell support
    "nix"          # Enable Nix package manager
]

# The command that runs when you click "Run" in Replit
run = "python 2024.10.27_youtube_V24_plus_Gemini_and_email_fetches_48hours_vtt_cleaning.py"  # Replace main.py with your entry point file

# Nix package manager configuration
[nix]
channel = "stable-24_05"  # Use the May 2024 stable channel of Nix packages

# Deployment configuration (for Cloud Run)
[deployment]
run = ["sh", "-c", "python main.py"]  # Command to run in Cloud Run
deploymentTarget = "cloudrun"          # Deploy to Google Cloud Run
ignorePaths = [                        # Files/folders to exclude from deployment
    "*.log",                          # Ignore log files
    "__pycache__",                    # Ignore Python cache directories
    "*.pyc",                          # Ignore compiled Python files
    "tests/"                          # Ignore test directory
]

# HTTP server configuration
[http]
localPort = 8080    # Port to use when running locally
externalPort = 80   # Port to expose to the internet
