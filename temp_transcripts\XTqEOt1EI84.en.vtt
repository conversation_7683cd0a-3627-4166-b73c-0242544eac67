WEBVTT
Kind: captions
Language: en

00:00:00.160 --> 00:00:02.869 align:start position:0%
 
Mr.<00:00:00.480><c> <PERSON>on</c><00:00:01.040><c> Musk</c><00:00:01.520><c> is</c><00:00:01.920><c> looking</c><00:00:02.080><c> forward</c><00:00:02.320><c> to</c><00:00:02.560><c> Arc</c>

00:00:02.869 --> 00:00:02.879 align:start position:0%
Mr. <PERSON><PERSON> is looking forward to Arc
 

00:00:02.879 --> 00:00:05.910 align:start position:0%
Mr. <PERSON><PERSON> is looking forward to Arc
AGI<00:00:03.679><c> 3.</c><00:00:04.240><c> Why,</c><00:00:04.560><c> you</c><00:00:04.720><c> might</c><00:00:04.880><c> ask?</c><00:00:05.279><c> Because</c><00:00:05.520><c> he</c>

00:00:05.910 --> 00:00:05.920 align:start position:0%
AGI 3. Why, you might ask? Because he
 

00:00:05.920 --> 00:00:09.270 align:start position:0%
AGI 3. Why, you might ask? Because he
killed<00:00:06.560><c> Arch</c><00:00:06.879><c> AGI</c><00:00:07.520><c> 2.</c><00:00:08.000><c> Just</c><00:00:08.400><c> smoked</c><00:00:08.800><c> it.</c><00:00:09.120><c> Now,</c>

00:00:09.270 --> 00:00:09.280 align:start position:0%
killed Arch AGI 2. Just smoked it. Now,
 

00:00:09.280 --> 00:00:10.549 align:start position:0%
killed Arch AGI 2. Just smoked it. Now,
at<00:00:09.440><c> this</c><00:00:09.519><c> point,</c><00:00:09.679><c> you've</c><00:00:09.920><c> probably</c><00:00:10.160><c> heard</c>

00:00:10.549 --> 00:00:10.559 align:start position:0%
at this point, you've probably heard
 

00:00:10.559 --> 00:00:12.709 align:start position:0%
at this point, you've probably heard
tons<00:00:10.880><c> of</c><00:00:11.120><c> things</c><00:00:11.440><c> being</c><00:00:11.679><c> said</c><00:00:11.920><c> about</c><00:00:12.160><c> the</c><00:00:12.480><c> new</c>

00:00:12.709 --> 00:00:12.719 align:start position:0%
tons of things being said about the new
 

00:00:12.719 --> 00:00:15.190 align:start position:0%
tons of things being said about the new
Gro<00:00:13.200><c> 4.</c><00:00:13.599><c> You</c><00:00:13.840><c> probably</c><00:00:14.000><c> saw</c><00:00:14.160><c> it</c><00:00:14.400><c> completely</c>

00:00:15.190 --> 00:00:15.200 align:start position:0%
Gro 4. You probably saw it completely
 

00:00:15.200 --> 00:00:17.990 align:start position:0%
Gro 4. You probably saw it completely
crush<00:00:15.759><c> everyone</c><00:00:16.160><c> else</c><00:00:16.400><c> on</c><00:00:16.800><c> humanity's</c><00:00:17.600><c> last</c>

00:00:17.990 --> 00:00:18.000 align:start position:0%
crush everyone else on humanity's last
 

00:00:18.000 --> 00:00:20.630 align:start position:0%
crush everyone else on humanity's last
exam.<00:00:18.720><c> Both</c><00:00:18.880><c> Gro</c><00:00:19.279><c> 4</c><00:00:19.439><c> and</c><00:00:19.600><c> Gro</c><00:00:19.920><c> 4</c><00:00:20.080><c> Heavy</c><00:00:20.400><c> are</c>

00:00:20.630 --> 00:00:20.640 align:start position:0%
exam. Both Gro 4 and Gro 4 Heavy are
 

00:00:20.640 --> 00:00:22.470 align:start position:0%
exam. Both Gro 4 and Gro 4 Heavy are
head<00:00:20.960><c> and</c><00:00:21.279><c> shoulders</c><00:00:21.760><c> above</c><00:00:22.080><c> the</c>

00:00:22.470 --> 00:00:22.480 align:start position:0%
head and shoulders above the
 

00:00:22.480 --> 00:00:25.670 align:start position:0%
head and shoulders above the
competition,<00:00:23.119><c> above</c><00:00:23.519><c> Gemini</c><00:00:24.000><c> 2.5</c><00:00:24.560><c> Pro,</c><00:00:25.199><c> above</c>

00:00:25.670 --> 00:00:25.680 align:start position:0%
competition, above Gemini 2.5 Pro, above
 

00:00:25.680 --> 00:00:28.470 align:start position:0%
competition, above Gemini 2.5 Pro, above
03.<00:00:26.640><c> And</c><00:00:26.880><c> notice</c><00:00:27.119><c> that</c><00:00:27.199><c> that</c><00:00:27.439><c> gap</c><00:00:27.760><c> exists.</c>

00:00:28.470 --> 00:00:28.480 align:start position:0%
03. And notice that that gap exists.
 

00:00:28.480 --> 00:00:31.029 align:start position:0%
03. And notice that that gap exists.
tools<00:00:29.039><c> or</c><00:00:29.599><c> no</c><00:00:29.920><c> tools</c><00:00:30.160><c> or</c><00:00:30.400><c> I</c><00:00:30.560><c> should</c><00:00:30.640><c> say</c><00:00:30.800><c> no</c>

00:00:31.029 --> 00:00:31.039 align:start position:0%
tools or no tools or I should say no
 

00:00:31.039 --> 00:00:33.750 align:start position:0%
tools or no tools or I should say no
tools<00:00:31.279><c> in</c><00:00:31.439><c> this</c><00:00:31.599><c> one</c><00:00:31.760><c> and</c><00:00:32.160><c> tool</c><00:00:32.640><c> usage</c><00:00:33.280><c> here.</c>

00:00:33.750 --> 00:00:33.760 align:start position:0%
tools in this one and tool usage here.
 

00:00:33.760 --> 00:00:34.950 align:start position:0%
tools in this one and tool usage here.
Now<00:00:33.920><c> you</c><00:00:34.079><c> might</c><00:00:34.160><c> have</c><00:00:34.320><c> noticed</c><00:00:34.480><c> that</c><00:00:34.719><c> things</c>

00:00:34.950 --> 00:00:34.960 align:start position:0%
Now you might have noticed that things
 

00:00:34.960 --> 00:00:38.069 align:start position:0%
Now you might have noticed that things
like<00:00:35.280><c> 03</c><00:00:36.000><c> Pro</c><00:00:36.320><c> and</c><00:00:36.640><c> the</c><00:00:36.880><c> new</c><00:00:37.200><c> Gro</c><00:00:37.680><c> for</c><00:00:37.760><c> heavy</c>

00:00:38.069 --> 00:00:38.079 align:start position:0%
like 03 Pro and the new Gro for heavy
 

00:00:38.079 --> 00:00:41.270 align:start position:0%
like 03 Pro and the new Gro for heavy
and<00:00:38.160><c> Gro</c><00:00:38.559><c> 4,</c><00:00:39.280><c> they</c><00:00:39.600><c> have</c><00:00:40.079><c> tool</c><00:00:40.480><c> use.</c><00:00:40.879><c> So</c><00:00:41.040><c> it's</c>

00:00:41.270 --> 00:00:41.280 align:start position:0%
and Gro 4, they have tool use. So it's
 

00:00:41.280 --> 00:00:43.590 align:start position:0%
and Gro 4, they have tool use. So it's
not<00:00:41.440><c> really</c><00:00:41.760><c> like</c><00:00:42.079><c> the</c><00:00:42.320><c> models</c><00:00:42.719><c> of</c><00:00:43.040><c> old</c><00:00:43.360><c> like</c>

00:00:43.590 --> 00:00:43.600 align:start position:0%
not really like the models of old like
 

00:00:43.600 --> 00:00:45.990 align:start position:0%
not really like the models of old like
we<00:00:43.760><c> were</c><00:00:43.920><c> used</c><00:00:44.000><c> to</c><00:00:44.320><c> just,</c><00:00:44.640><c> you</c><00:00:44.800><c> know,</c><00:00:45.200><c> 6</c><00:00:45.520><c> plus</c>

00:00:45.990 --> 00:00:46.000 align:start position:0%
we were used to just, you know, 6 plus
 

00:00:46.000 --> 00:00:47.750 align:start position:0%
we were used to just, you know, 6 plus
12<00:00:46.320><c> plus</c><00:00:46.559><c> months</c><00:00:46.800><c> ago</c><00:00:47.280><c> where</c><00:00:47.440><c> you</c><00:00:47.520><c> kind</c><00:00:47.680><c> of</c>

00:00:47.750 --> 00:00:47.760 align:start position:0%
12 plus months ago where you kind of
 

00:00:47.760 --> 00:00:49.830 align:start position:0%
12 plus months ago where you kind of
prompted<00:00:48.079><c> and</c><00:00:48.239><c> see</c><00:00:48.399><c> the</c><00:00:48.559><c> output.</c><00:00:49.120><c> Now</c><00:00:49.520><c> a</c><00:00:49.680><c> lot</c>

00:00:49.830 --> 00:00:49.840 align:start position:0%
prompted and see the output. Now a lot
 

00:00:49.840 --> 00:00:51.029 align:start position:0%
prompted and see the output. Now a lot
of the<00:00:50.000><c> stuff</c><00:00:50.160><c> that's</c><00:00:50.399><c> happening</c><00:00:50.640><c> behind</c><00:00:50.879><c> the</c>

00:00:51.029 --> 00:00:51.039 align:start position:0%
of the stuff that's happening behind the
 

00:00:51.039 --> 00:00:53.590 align:start position:0%
of the stuff that's happening behind the
scenes<00:00:51.600><c> is</c><00:00:52.079><c> hidden.</c><00:00:52.719><c> You're</c><00:00:52.960><c> not</c><00:00:53.199><c> really</c>

00:00:53.590 --> 00:00:53.600 align:start position:0%
scenes is hidden. You're not really
 

00:00:53.600 --> 00:00:55.510 align:start position:0%
scenes is hidden. You're not really
seeing<00:00:53.920><c> the</c><00:00:54.239><c> chain</c><00:00:54.480><c> of</c><00:00:54.640><c> thoughts</c><00:00:55.120><c> kind</c><00:00:55.280><c> of</c><00:00:55.360><c> as</c>

00:00:55.510 --> 00:00:55.520 align:start position:0%
seeing the chain of thoughts kind of as
 

00:00:55.520 --> 00:00:57.430 align:start position:0%
seeing the chain of thoughts kind of as
it's<00:00:55.920><c> thinking</c><00:00:56.160><c> through</c><00:00:56.399><c> stuff.</c><00:00:56.960><c> you're</c><00:00:57.280><c> not</c>

00:00:57.430 --> 00:00:57.440 align:start position:0%
it's thinking through stuff. you're not
 

00:00:57.440 --> 00:00:59.430 align:start position:0%
it's thinking through stuff. you're not
even<00:00:57.760><c> seeing</c><00:00:58.160><c> all</c><00:00:58.320><c> the</c><00:00:58.559><c> tools</c><00:00:58.960><c> that</c><00:00:59.199><c> it's</c>

00:00:59.430 --> 00:00:59.440 align:start position:0%
even seeing all the tools that it's
 

00:00:59.440 --> 00:01:01.349 align:start position:0%
even seeing all the tools that it's
running<00:00:59.680><c> in</c><00:00:59.920><c> the</c><00:01:00.079><c> background.</c><00:01:00.800><c> If</c><00:01:00.960><c> you</c><00:01:01.039><c> use</c>

00:01:01.349 --> 00:01:01.359 align:start position:0%
running in the background. If you use
 

00:01:01.359 --> 00:01:03.830 align:start position:0%
running in the background. If you use
something<00:01:01.600><c> like</c><00:01:01.760><c> the</c><00:01:02.079><c> 03</c><00:01:02.719><c> Pro,</c><00:01:03.120><c> for</c><00:01:03.120><c> example,</c>

00:01:03.830 --> 00:01:03.840 align:start position:0%
something like the 03 Pro, for example,
 

00:01:03.840 --> 00:01:05.189 align:start position:0%
something like the 03 Pro, for example,
it<00:01:04.000><c> can</c><00:01:04.159><c> be</c><00:01:04.320><c> doing</c><00:01:04.400><c> a</c><00:01:04.559><c> lot</c><00:01:04.640><c> of</c><00:01:04.799><c> stuff</c><00:01:04.879><c> in</c><00:01:05.119><c> the</c>

00:01:05.189 --> 00:01:05.199 align:start position:0%
it can be doing a lot of stuff in the
 

00:01:05.199 --> 00:01:06.630 align:start position:0%
it can be doing a lot of stuff in the
background<00:01:05.600><c> that</c><00:01:05.920><c> you</c><00:01:06.080><c> might</c><00:01:06.159><c> not</c><00:01:06.320><c> even</c>

00:01:06.630 --> 00:01:06.640 align:start position:0%
background that you might not even
 

00:01:06.640 --> 00:01:07.910 align:start position:0%
background that you might not even
notice<00:01:06.960><c> that</c><00:01:07.119><c> it's</c><00:01:07.280><c> doing.</c><00:01:07.600><c> Like</c><00:01:07.840><c> for</c>

00:01:07.910 --> 00:01:07.920 align:start position:0%
notice that it's doing. Like for
 

00:01:07.920 --> 00:01:10.469 align:start position:0%
notice that it's doing. Like for
example,<00:01:08.240><c> it's</c><00:01:08.479><c> able</c><00:01:08.720><c> to</c><00:01:09.360><c> run</c><00:01:09.760><c> some</c><00:01:10.000><c> code,</c>

00:01:10.469 --> 00:01:10.479 align:start position:0%
example, it's able to run some code,
 

00:01:10.479 --> 00:01:12.310 align:start position:0%
example, it's able to run some code,
execute<00:01:10.880><c> it,</c><00:01:11.280><c> and</c><00:01:11.439><c> then</c><00:01:11.680><c> just</c><00:01:11.840><c> give</c><00:01:12.000><c> you</c><00:01:12.159><c> kind</c>

00:01:12.310 --> 00:01:12.320 align:start position:0%
execute it, and then just give you kind
 

00:01:12.320 --> 00:01:14.230 align:start position:0%
execute it, and then just give you kind
of<00:01:12.400><c> the</c><00:01:12.799><c> answers</c><00:01:13.119><c> from</c><00:01:13.360><c> the</c><00:01:13.600><c> code.</c><00:01:13.920><c> It</c><00:01:14.080><c> doesn't</c>

00:01:14.230 --> 00:01:14.240 align:start position:0%
of the answers from the code. It doesn't
 

00:01:14.240 --> 00:01:15.510 align:start position:0%
of the answers from the code. It doesn't
even<00:01:14.400><c> give</c><00:01:14.479><c> you</c><00:01:14.640><c> the</c><00:01:14.799><c> code.</c><00:01:15.040><c> you</c><00:01:15.280><c> don't</c><00:01:15.360><c> see</c>

00:01:15.510 --> 00:01:15.520 align:start position:0%
even give you the code. you don't see
 

00:01:15.520 --> 00:01:17.830 align:start position:0%
even give you the code. you don't see
the<00:01:15.760><c> code,</c><00:01:16.159><c> but</c><00:01:16.560><c> in</c><00:01:16.799><c> the</c><00:01:17.280><c> kind</c><00:01:17.520><c> of</c><00:01:17.600><c> that</c>

00:01:17.830 --> 00:01:17.840 align:start position:0%
the code, but in the kind of that
 

00:01:17.840 --> 00:01:19.350 align:start position:0%
the code, but in the kind of that
scratch<00:01:18.159><c> pad</c><00:01:18.320><c> on</c><00:01:18.400><c> the</c><00:01:18.560><c> side</c><00:01:18.720><c> where</c><00:01:18.880><c> it</c><00:01:19.040><c> jotss</c>

00:01:19.350 --> 00:01:19.360 align:start position:0%
scratch pad on the side where it jotss
 

00:01:19.360 --> 00:01:21.590 align:start position:0%
scratch pad on the side where it jotss
down<00:01:19.680><c> its</c><00:01:20.000><c> thoughts</c><00:01:20.240><c> or</c><00:01:20.560><c> the</c><00:01:20.799><c> summary</c><00:01:21.200><c> of</c><00:01:21.360><c> its</c>

00:01:21.590 --> 00:01:21.600 align:start position:0%
down its thoughts or the summary of its
 

00:01:21.600 --> 00:01:23.190 align:start position:0%
down its thoughts or the summary of its
thoughts,<00:01:22.240><c> there</c><00:01:22.479><c> might</c><00:01:22.640><c> be</c><00:01:22.720><c> a</c><00:01:22.799><c> sentence</c><00:01:23.040><c> or</c>

00:01:23.190 --> 00:01:23.200 align:start position:0%
thoughts, there might be a sentence or
 

00:01:23.200 --> 00:01:24.550 align:start position:0%
thoughts, there might be a sentence or
two<00:01:23.360><c> that</c><00:01:23.520><c> kind</c><00:01:23.600><c> of</c><00:01:23.680><c> hints</c><00:01:23.920><c> at</c><00:01:24.080><c> it</c><00:01:24.240><c> like,</c><00:01:24.320><c> "Oh,</c>

00:01:24.550 --> 00:01:24.560 align:start position:0%
two that kind of hints at it like, "Oh,
 

00:01:24.560 --> 00:01:26.390 align:start position:0%
two that kind of hints at it like, "Oh,
I<00:01:24.640><c> ran</c><00:01:24.799><c> this</c><00:01:24.960><c> in</c><00:01:25.119><c> my</c><00:01:25.280><c> notebook</c><00:01:25.759><c> and</c><00:01:26.000><c> here's</c><00:01:26.240><c> the</c>

00:01:26.390 --> 00:01:26.400 align:start position:0%
I ran this in my notebook and here's the
 

00:01:26.400 --> 00:01:27.910 align:start position:0%
I ran this in my notebook and here's the
output<00:01:26.720><c> of</c><00:01:26.880><c> the</c><00:01:27.040><c> Python</c><00:01:27.360><c> code</c><00:01:27.520><c> or</c><00:01:27.759><c> something</c>

00:01:27.910 --> 00:01:27.920 align:start position:0%
output of the Python code or something
 

00:01:27.920 --> 00:01:29.270 align:start position:0%
output of the Python code or something
like<00:01:28.000><c> that."</c><00:01:28.240><c> So,</c><00:01:28.479><c> you</c><00:01:28.560><c> know,</c><00:01:28.720><c> it's</c><00:01:29.040><c> doing</c>

00:01:29.270 --> 00:01:29.280 align:start position:0%
like that." So, you know, it's doing
 

00:01:29.280 --> 00:01:30.950 align:start position:0%
like that." So, you know, it's doing
something<00:01:29.520><c> in</c><00:01:29.759><c> the</c><00:01:29.920><c> background</c><00:01:30.400><c> that</c><00:01:30.720><c> you're</c>

00:01:30.950 --> 00:01:30.960 align:start position:0%
something in the background that you're
 

00:01:30.960 --> 00:01:32.870 align:start position:0%
something in the background that you're
not<00:01:31.119><c> seeing.</c><00:01:31.680><c> And</c><00:01:31.840><c> it</c><00:01:32.079><c> seems</c><00:01:32.240><c> like</c><00:01:32.400><c> all</c><00:01:32.720><c> the</c>

00:01:32.870 --> 00:01:32.880 align:start position:0%
not seeing. And it seems like all the
 

00:01:32.880 --> 00:01:34.550 align:start position:0%
not seeing. And it seems like all the
companies<00:01:33.200><c> are</c><00:01:33.360><c> now</c><00:01:33.680><c> going</c><00:01:33.920><c> more</c><00:01:34.159><c> towards</c>

00:01:34.550 --> 00:01:34.560 align:start position:0%
companies are now going more towards
 

00:01:34.560 --> 00:01:36.149 align:start position:0%
companies are now going more towards
that.<00:01:34.799><c> So,</c><00:01:34.960><c> we're</c><00:01:35.119><c> not</c><00:01:35.360><c> interacting</c><00:01:35.759><c> with</c><00:01:35.920><c> the</c>

00:01:36.149 --> 00:01:36.159 align:start position:0%
that. So, we're not interacting with the
 

00:01:36.159 --> 00:01:38.149 align:start position:0%
that. So, we're not interacting with the
models<00:01:36.560><c> oneonone.</c><00:01:37.200><c> We're</c><00:01:37.360><c> more</c><00:01:37.600><c> dealing</c><00:01:37.920><c> with</c>

00:01:38.149 --> 00:01:38.159 align:start position:0%
models oneonone. We're more dealing with
 

00:01:38.159 --> 00:01:40.469 align:start position:0%
models oneonone. We're more dealing with
the<00:01:38.560><c> system.</c><00:01:38.960><c> So,</c><00:01:39.119><c> it's</c><00:01:39.360><c> the</c><00:01:39.520><c> model</c><00:01:39.840><c> plus</c><00:01:40.079><c> the</c>

00:01:40.469 --> 00:01:40.479 align:start position:0%
the system. So, it's the model plus the
 

00:01:40.479 --> 00:01:42.550 align:start position:0%
the system. So, it's the model plus the
suite<00:01:40.720><c> of</c><00:01:40.880><c> tools</c><00:01:41.119><c> that</c><00:01:41.280><c> it</c><00:01:41.439><c> can</c><00:01:41.600><c> use.</c><00:01:42.079><c> and</c><00:01:42.320><c> we</c>

00:01:42.550 --> 00:01:42.560 align:start position:0%
suite of tools that it can use. and we
 

00:01:42.560 --> 00:01:44.230 align:start position:0%
suite of tools that it can use. and we
don't<00:01:42.799><c> even</c><00:01:42.960><c> necessarily</c><00:01:43.439><c> exactly</c><00:01:43.840><c> know</c><00:01:44.000><c> what</c>

00:01:44.230 --> 00:01:44.240 align:start position:0%
don't even necessarily exactly know what
 

00:01:44.240 --> 00:01:45.830 align:start position:0%
don't even necessarily exactly know what
it<00:01:44.400><c> has</c><00:01:44.720><c> access</c><00:01:45.040><c> to,</c><00:01:45.280><c> but</c><00:01:45.520><c> it's</c><00:01:45.680><c> probably</c>

00:01:45.830 --> 00:01:45.840 align:start position:0%
it has access to, but it's probably
 

00:01:45.840 --> 00:01:47.670 align:start position:0%
it has access to, but it's probably
things<00:01:46.079><c> like</c><00:01:46.399><c> searching</c><00:01:46.799><c> the</c><00:01:46.960><c> web,</c><00:01:47.439><c> you</c><00:01:47.520><c> know,</c>

00:01:47.670 --> 00:01:47.680 align:start position:0%
things like searching the web, you know,
 

00:01:47.680 --> 00:01:49.670 align:start position:0%
things like searching the web, you know,
running<00:01:47.920><c> some</c><00:01:48.079><c> basic</c><00:01:48.479><c> code,</c><00:01:48.880><c> etc.</c><00:01:49.200><c> Now,</c><00:01:49.439><c> these</c>

00:01:49.670 --> 00:01:49.680 align:start position:0%
running some basic code, etc. Now, these
 

00:01:49.680 --> 00:01:52.230 align:start position:0%
running some basic code, etc. Now, these
are<00:01:49.759><c> excellent</c><00:01:50.240><c> results,</c><00:01:50.799><c> but</c><00:01:51.520><c> keep</c><00:01:51.680><c> in</c><00:01:51.920><c> mind</c>

00:01:52.230 --> 00:01:52.240 align:start position:0%
are excellent results, but keep in mind
 

00:01:52.240 --> 00:01:54.630 align:start position:0%
are excellent results, but keep in mind
that<00:01:52.640><c> Google</c><00:01:52.880><c> DeepMind</c><00:01:53.520><c> might</c><00:01:53.840><c> be</c><00:01:54.159><c> hot</c><00:01:54.479><c> on</c>

00:01:54.630 --> 00:01:54.640 align:start position:0%
that Google DeepMind might be hot on
 

00:01:54.640 --> 00:01:58.069 align:start position:0%
that Google DeepMind might be hot on
their<00:01:54.880><c> tails.</c><00:01:55.600><c> Gemini</c><00:01:56.479><c> 3.0</c>

00:01:58.069 --> 00:01:58.079 align:start position:0%
their tails. Gemini 3.0
 

00:01:58.079 --> 00:02:00.789 align:start position:0%
their tails. Gemini 3.0
Pro<00:01:58.399><c> has</c><00:01:58.640><c> been</c><00:01:59.040><c> spotted</c><00:01:59.600><c> out</c><00:01:59.840><c> there,</c><00:02:00.159><c> so</c><00:02:00.560><c> we</c>

00:02:00.789 --> 00:02:00.799 align:start position:0%
Pro has been spotted out there, so we
 

00:02:00.799 --> 00:02:02.950 align:start position:0%
Pro has been spotted out there, so we
might<00:02:01.040><c> be</c><00:02:01.200><c> seeing</c><00:02:01.439><c> it</c><00:02:01.680><c> soon.</c><00:02:02.240><c> Both</c><00:02:02.560><c> Sundar</c>

00:02:02.950 --> 00:02:02.960 align:start position:0%
might be seeing it soon. Both Sundar
 

00:02:02.960 --> 00:02:06.149 align:start position:0%
might be seeing it soon. Both Sundar
Pachai<00:02:03.680><c> and</c><00:02:04.079><c> Demiasabis</c><00:02:05.119><c> have</c><00:02:05.600><c> congratulated</c>

00:02:06.149 --> 00:02:06.159 align:start position:0%
Pachai and Demiasabis have congratulated
 

00:02:06.159 --> 00:02:08.150 align:start position:0%
Pachai and Demiasabis have congratulated
Elon<00:02:06.399><c> Musk</c><00:02:06.719><c> on</c><00:02:06.960><c> publishing</c><00:02:07.439><c> his</c><00:02:07.759><c> brand</c><00:02:08.000><c> new</c>

00:02:08.150 --> 00:02:08.160 align:start position:0%
Elon Musk on publishing his brand new
 

00:02:08.160 --> 00:02:10.710 align:start position:0%
Elon Musk on publishing his brand new
Gro<00:02:08.640><c> 4.</c><00:02:09.119><c> So</c><00:02:09.360><c> that's</c><00:02:09.520><c> the</c><00:02:09.759><c> CEO</c><00:02:10.000><c> of</c><00:02:10.160><c> Google,</c><00:02:10.479><c> CEO</c>

00:02:10.710 --> 00:02:10.720 align:start position:0%
Gro 4. So that's the CEO of Google, CEO
 

00:02:10.720 --> 00:02:12.949 align:start position:0%
Gro 4. So that's the CEO of Google, CEO
of<00:02:10.879><c> Google</c><00:02:11.120><c> Deep</c><00:02:11.440><c> Mine.</c><00:02:12.000><c> But</c><00:02:12.160><c> it</c><00:02:12.400><c> begs</c><00:02:12.720><c> the</c>

00:02:12.949 --> 00:02:12.959 align:start position:0%
of Google Deep Mine. But it begs the
 

00:02:12.959 --> 00:02:15.510 align:start position:0%
of Google Deep Mine. But it begs the
question,<00:02:13.520><c> what</c><00:02:13.840><c> happens</c><00:02:14.319><c> when</c><00:02:14.720><c> they</c><00:02:15.120><c> drop</c>

00:02:15.510 --> 00:02:15.520 align:start position:0%
question, what happens when they drop
 

00:02:15.520 --> 00:02:18.630 align:start position:0%
question, what happens when they drop
this<00:02:15.920><c> Gemini</c><00:02:16.480><c> 3.0</c><00:02:17.440><c> Pro?</c><00:02:17.920><c> And</c><00:02:18.160><c> it</c><00:02:18.319><c> seems</c><00:02:18.480><c> like</c>

00:02:18.630 --> 00:02:18.640 align:start position:0%
this Gemini 3.0 Pro? And it seems like
 

00:02:18.640 --> 00:02:19.990 align:start position:0%
this Gemini 3.0 Pro? And it seems like
that's<00:02:18.800><c> going</c><00:02:18.879><c> to</c><00:02:19.040><c> be</c><00:02:19.120><c> pretty</c><00:02:19.360><c> soon.</c><00:02:19.920><c> You</c>

00:02:19.990 --> 00:02:20.000 align:start position:0%
that's going to be pretty soon. You
 

00:02:20.000 --> 00:02:23.110 align:start position:0%
that's going to be pretty soon. You
know,<00:02:20.239><c> where's</c><00:02:20.879><c> that</c><00:02:21.280><c> going</c><00:02:21.440><c> to</c><00:02:21.599><c> land?</c><00:02:22.160><c> Is</c><00:02:22.640><c> Gro</c>

00:02:23.110 --> 00:02:23.120 align:start position:0%
know, where's that going to land? Is Gro
 

00:02:23.120 --> 00:02:26.550 align:start position:0%
know, where's that going to land? Is Gro
4<00:02:23.360><c> going</c><00:02:23.599><c> to</c><00:02:23.760><c> be</c><00:02:24.319><c> the</c><00:02:24.879><c> number</c><00:02:25.280><c> one</c><00:02:25.680><c> AI</c><00:02:26.080><c> model</c><00:02:26.400><c> in</c>

00:02:26.550 --> 00:02:26.560 align:start position:0%
4 going to be the number one AI model in
 

00:02:26.560 --> 00:02:28.710 align:start position:0%
4 going to be the number one AI model in
the<00:02:26.720><c> world</c><00:02:26.959><c> for</c><00:02:27.200><c> for</c><00:02:27.520><c> a</c><00:02:27.680><c> long</c><00:02:27.920><c> time</c><00:02:28.160><c> or</c><00:02:28.400><c> maybe</c>

00:02:28.710 --> 00:02:28.720 align:start position:0%
the world for for a long time or maybe
 

00:02:28.720 --> 00:02:31.270 align:start position:0%
the world for for a long time or maybe
not<00:02:28.959><c> so</c><00:02:29.200><c> much?</c><00:02:29.760><c> Jimmy</c><00:02:30.080><c> apples,</c><00:02:30.640><c> who</c><00:02:30.879><c> has</c>

00:02:31.270 --> 00:02:31.280 align:start position:0%
not so much? Jimmy apples, who has
 

00:02:31.280 --> 00:02:33.430 align:start position:0%
not so much? Jimmy apples, who has
called<00:02:31.680><c> some</c><00:02:31.920><c> things</c><00:02:32.160><c> before</c><00:02:32.720><c> some</c><00:02:32.959><c> leaked</c>

00:02:33.430 --> 00:02:33.440 align:start position:0%
called some things before some leaked
 

00:02:33.440 --> 00:02:35.270 align:start position:0%
called some things before some leaked
some<00:02:33.760><c> information</c><00:02:34.400><c> long</c><00:02:34.720><c> before</c><00:02:34.959><c> it</c><00:02:35.040><c> was</c>

00:02:35.270 --> 00:02:35.280 align:start position:0%
some information long before it was
 

00:02:35.280 --> 00:02:36.869 align:start position:0%
some information long before it was
available<00:02:35.599><c> to</c><00:02:35.760><c> the</c><00:02:35.920><c> public.</c><00:02:36.239><c> So,</c><00:02:36.400><c> I</c><00:02:36.560><c> tend</c><00:02:36.720><c> to</c>

00:02:36.869 --> 00:02:36.879 align:start position:0%
available to the public. So, I tend to
 

00:02:36.879 --> 00:02:38.470 align:start position:0%
available to the public. So, I tend to
kind<00:02:36.959><c> of</c><00:02:37.040><c> trust</c><00:02:37.360><c> what</c><00:02:37.760><c> he</c><00:02:38.000><c> says,</c><00:02:38.319><c> you</c><00:02:38.400><c> know,</c>

00:02:38.470 --> 00:02:38.480 align:start position:0%
kind of trust what he says, you know,
 

00:02:38.480 --> 00:02:40.070 align:start position:0%
kind of trust what he says, you know,
with<00:02:38.560><c> a</c><00:02:38.720><c> grain</c><00:02:38.879><c> of</c><00:02:39.040><c> salt,</c><00:02:39.280><c> but</c><00:02:39.519><c> he's</c><00:02:39.840><c> been</c>

00:02:40.070 --> 00:02:40.080 align:start position:0%
with a grain of salt, but he's been
 

00:02:40.080 --> 00:02:43.030 align:start position:0%
with a grain of salt, but he's been
right<00:02:40.560><c> before,</c><00:02:41.200><c> eerily</c><00:02:41.840><c> right</c><00:02:42.080><c> before.</c><00:02:42.640><c> So,</c>

00:02:43.030 --> 00:02:43.040 align:start position:0%
right before, eerily right before. So,
 

00:02:43.040 --> 00:02:44.790 align:start position:0%
right before, eerily right before. So,
uh,<00:02:43.280><c> definitely</c><00:02:43.519><c> don't</c><00:02:43.680><c> dismiss</c><00:02:44.080><c> this.</c><00:02:44.560><c> He's</c>

00:02:44.790 --> 00:02:44.800 align:start position:0%
uh, definitely don't dismiss this. He's
 

00:02:44.800 --> 00:02:47.270 align:start position:0%
uh, definitely don't dismiss this. He's
saying<00:02:45.120><c> that</c><00:02:45.840><c> he's</c><00:02:46.160><c> hearing</c><00:02:46.480><c> a</c><00:02:46.720><c> few</c><00:02:46.879><c> whispers</c>

00:02:47.270 --> 00:02:47.280 align:start position:0%
saying that he's hearing a few whispers
 

00:02:47.280 --> 00:02:49.750 align:start position:0%
saying that he's hearing a few whispers
from<00:02:47.599><c> some</c><00:02:48.080><c> birds.</c><00:02:49.040><c> It's</c><00:02:49.200><c> got</c><00:02:49.280><c> to</c><00:02:49.440><c> be</c><00:02:49.440><c> a</c><00:02:49.599><c> Game</c>

00:02:49.750 --> 00:02:49.760 align:start position:0%
from some birds. It's got to be a Game
 

00:02:49.760 --> 00:02:51.350 align:start position:0%
from some birds. It's got to be a Game
of<00:02:49.920><c> Thrones</c><00:02:50.239><c> reference,</c><00:02:50.879><c> uh,</c><00:02:50.959><c> that</c><00:02:51.200><c> the</c>

00:02:51.350 --> 00:02:51.360 align:start position:0%
of Thrones reference, uh, that the
 

00:02:51.360 --> 00:02:54.550 align:start position:0%
of Thrones reference, uh, that the
internal<00:02:51.760><c> evals</c><00:02:52.400><c> for</c><00:02:52.879><c> GPT5</c>

00:02:54.550 --> 00:02:54.560 align:start position:0%
internal evals for GPT5
 

00:02:54.560 --> 00:02:56.949 align:start position:0%
internal evals for GPT5
that<00:02:54.959><c> is</c><00:02:55.200><c> rumored</c><00:02:55.440><c> to</c><00:02:55.680><c> come</c><00:02:55.760><c> out</c><00:02:56.000><c> soon,</c><00:02:56.640><c> very</c>

00:02:56.949 --> 00:02:56.959 align:start position:0%
that is rumored to come out soon, very
 

00:02:56.959 --> 00:02:58.309 align:start position:0%
that is rumored to come out soon, very
soon<00:02:57.120><c> as</c><00:02:57.360><c> well.</c><00:02:57.760><c> So,</c><00:02:57.920><c> we're</c><00:02:58.080><c> going</c><00:02:58.080><c> to</c><00:02:58.239><c> be</c>

00:02:58.309 --> 00:02:58.319 align:start position:0%
soon as well. So, we're going to be
 

00:02:58.319 --> 00:03:01.270 align:start position:0%
soon as well. So, we're going to be
seeing<00:02:58.480><c> in</c><00:02:58.640><c> GPT5</c><00:02:59.519><c> that</c><00:02:59.840><c> those</c><00:03:00.160><c> are</c><00:03:00.319><c> a</c><00:03:00.480><c> tad</c><00:03:00.800><c> over</c>

00:03:01.270 --> 00:03:01.280 align:start position:0%
seeing in GPT5 that those are a tad over
 

00:03:01.280 --> 00:03:05.430 align:start position:0%
seeing in GPT5 that those are a tad over
Gro<00:03:02.000><c> 4</c><00:03:02.560><c> heavy.</c><00:03:03.200><c> So</c><00:03:03.599><c> just</c><00:03:03.760><c> to</c><00:03:04.000><c> summarize,</c><00:03:04.959><c> Elon</c>

00:03:05.430 --> 00:03:05.440 align:start position:0%
Gro 4 heavy. So just to summarize, Elon
 

00:03:05.440 --> 00:03:07.509 align:start position:0%
Gro 4 heavy. So just to summarize, Elon
Musk<00:03:05.760><c> who</c><00:03:06.000><c> started</c><00:03:06.319><c> much</c><00:03:06.800><c> later</c><00:03:07.120><c> than</c><00:03:07.280><c> the</c>

00:03:07.509 --> 00:03:07.519 align:start position:0%
Musk who started much later than the
 

00:03:07.519 --> 00:03:09.830 align:start position:0%
Musk who started much later than the
other<00:03:07.680><c> companies</c><00:03:08.080><c> in</c><00:03:08.319><c> the</c><00:03:08.480><c> AI</c><00:03:08.879><c> space,</c><00:03:09.519><c> he</c>

00:03:09.830 --> 00:03:09.840 align:start position:0%
other companies in the AI space, he
 

00:03:09.840 --> 00:03:13.190 align:start position:0%
other companies in the AI space, he
comes<00:03:10.080><c> from</c><00:03:10.720><c> behind</c><00:03:11.599><c> and</c><00:03:12.319><c> lands</c><00:03:12.720><c> in</c><00:03:12.959><c> the</c>

00:03:13.190 --> 00:03:13.200 align:start position:0%
comes from behind and lands in the
 

00:03:13.200 --> 00:03:16.149 align:start position:0%
comes from behind and lands in the
number<00:03:13.360><c> one</c><00:03:13.599><c> spot.</c><00:03:14.000><c> Look</c><00:03:14.080><c> at</c><00:03:14.239><c> the</c><00:03:14.480><c> AIM25</c>

00:03:16.149 --> 00:03:16.159 align:start position:0%
number one spot. Look at the AIM25
 

00:03:16.159 --> 00:03:20.550 align:start position:0%
number one spot. Look at the AIM25
100%.<00:03:17.200><c> That</c><00:03:17.599><c> benchmark</c><00:03:18.480><c> is</c><00:03:19.519><c> dead</c><00:03:19.760><c> and</c><00:03:20.000><c> buried.</c>

00:03:20.550 --> 00:03:20.560 align:start position:0%
100%. That benchmark is dead and buried.
 

00:03:20.560 --> 00:03:23.589 align:start position:0%
100%. That benchmark is dead and buried.
What's<00:03:20.800><c> the</c><00:03:21.040><c> secret</c><00:03:21.440><c> to</c><00:03:21.680><c> his</c><00:03:22.080><c> success?</c><00:03:22.959><c> Well,</c>

00:03:23.589 --> 00:03:23.599 align:start position:0%
What's the secret to his success? Well,
 

00:03:23.599 --> 00:03:25.830 align:start position:0%
What's the secret to his success? Well,
notice<00:03:23.920><c> he</c><00:03:24.080><c> didn't</c><00:03:24.319><c> shill</c><00:03:24.560><c> out</c><00:03:25.040><c> billions</c>

00:03:25.830 --> 00:03:25.840 align:start position:0%
notice he didn't shill out billions
 

00:03:25.840 --> 00:03:28.949 align:start position:0%
notice he didn't shill out billions
acquiring<00:03:26.400><c> talent</c><00:03:27.040><c> like</c><00:03:27.519><c> Meta</c><00:03:27.920><c> is</c><00:03:28.080><c> doing.</c><00:03:28.560><c> We</c>

00:03:28.949 --> 00:03:28.959 align:start position:0%
acquiring talent like Meta is doing. We
 

00:03:28.959 --> 00:03:31.190 align:start position:0%
acquiring talent like Meta is doing. We
don't<00:03:29.200><c> know</c><00:03:29.280><c> if</c><00:03:29.519><c> he</c><00:03:29.680><c> has</c><00:03:29.920><c> some</c><00:03:30.400><c> breakthrough</c>

00:03:31.190 --> 00:03:31.200 align:start position:0%
don't know if he has some breakthrough
 

00:03:31.200 --> 00:03:33.509 align:start position:0%
don't know if he has some breakthrough
algorithmic<00:03:31.920><c> thing</c><00:03:32.400><c> that</c><00:03:32.879><c> put</c><00:03:33.040><c> him</c><00:03:33.120><c> ahead.</c>

00:03:33.509 --> 00:03:33.519 align:start position:0%
algorithmic thing that put him ahead.
 

00:03:33.519 --> 00:03:35.589 align:start position:0%
algorithmic thing that put him ahead.
Probably<00:03:33.920><c> not.</c><00:03:34.400><c> Probably</c><00:03:34.879><c> similar</c><00:03:35.200><c> to</c><00:03:35.360><c> what</c>

00:03:35.589 --> 00:03:35.599 align:start position:0%
Probably not. Probably similar to what
 

00:03:35.599 --> 00:03:37.190 align:start position:0%
Probably not. Probably similar to what
everybody<00:03:35.920><c> else</c><00:03:36.080><c> is</c><00:03:36.319><c> doing.</c><00:03:36.640><c> Maybe</c><00:03:36.799><c> we'll</c>

00:03:37.190 --> 00:03:37.200 align:start position:0%
everybody else is doing. Maybe we'll
 

00:03:37.200 --> 00:03:38.869 align:start position:0%
everybody else is doing. Maybe we'll
find<00:03:37.440><c> out</c><00:03:37.599><c> later</c><00:03:38.000><c> that</c><00:03:38.239><c> that's</c><00:03:38.400><c> not</c><00:03:38.560><c> the</c><00:03:38.640><c> case.</c>

00:03:38.869 --> 00:03:38.879 align:start position:0%
find out later that that's not the case.
 

00:03:38.879 --> 00:03:40.710 align:start position:0%
find out later that that's not the case.
He's<00:03:39.040><c> got</c><00:03:39.200><c> some</c><00:03:39.519><c> secret</c><00:03:40.000><c> weapon</c><00:03:40.319><c> up</c><00:03:40.560><c> his</c>

00:03:40.710 --> 00:03:40.720 align:start position:0%
He's got some secret weapon up his
 

00:03:40.720 --> 00:03:43.110 align:start position:0%
He's got some secret weapon up his
sleeve.<00:03:41.280><c> But</c><00:03:41.440><c> the</c><00:03:41.920><c> solution</c><00:03:42.319><c> to</c><00:03:42.720><c> things</c><00:03:42.959><c> like</c>

00:03:43.110 --> 00:03:43.120 align:start position:0%
sleeve. But the solution to things like
 

00:03:43.120 --> 00:03:44.949 align:start position:0%
sleeve. But the solution to things like
this<00:03:43.519><c> sometimes,</c><00:03:44.000><c> and</c><00:03:44.239><c> if</c><00:03:44.400><c> you've</c><00:03:44.560><c> played</c><00:03:44.799><c> a</c>

00:03:44.949 --> 00:03:44.959 align:start position:0%
this sometimes, and if you've played a
 

00:03:44.959 --> 00:03:46.949 align:start position:0%
this sometimes, and if you've played a
Factorio,<00:03:45.680><c> you</c><00:03:45.920><c> know</c><00:03:46.000><c> this,</c><00:03:46.400><c> sometimes</c><00:03:46.799><c> you</c>

00:03:46.949 --> 00:03:46.959 align:start position:0%
Factorio, you know this, sometimes you
 

00:03:46.959 --> 00:03:49.589 align:start position:0%
Factorio, you know this, sometimes you
just<00:03:47.200><c> need</c><00:03:47.440><c> more.</c><00:03:48.319><c> Like</c><00:03:48.560><c> a</c><00:03:48.720><c> lot</c><00:03:48.879><c> more.</c><00:03:49.280><c> A</c><00:03:49.519><c> lot</c>

00:03:49.589 --> 00:03:49.599 align:start position:0%
just need more. Like a lot more. A lot
 

00:03:49.599 --> 00:03:51.350 align:start position:0%
just need more. Like a lot more. A lot
more<00:03:49.760><c> than</c><00:03:49.920><c> you</c><00:03:50.159><c> think.</c><00:03:50.480><c> A</c><00:03:50.560><c> lot</c><00:03:50.720><c> more</c><00:03:50.959><c> than</c><00:03:51.120><c> is</c>

00:03:51.350 --> 00:03:51.360 align:start position:0%
more than you think. A lot more than is
 

00:03:51.360 --> 00:03:55.350 align:start position:0%
more than you think. A lot more than is
reasonable.<00:03:51.920><c> A</c><00:03:52.159><c> lot</c><00:03:52.799><c> more</c><00:03:53.680><c> of</c><00:03:54.560><c> stuff</c><00:03:55.120><c> of</c>

00:03:55.350 --> 00:03:55.360 align:start position:0%
reasonable. A lot more of stuff of
 

00:03:55.360 --> 00:03:57.350 align:start position:0%
reasonable. A lot more of stuff of
everything.<00:03:56.080><c> In</c><00:03:56.239><c> this</c><00:03:56.400><c> case,</c><00:03:56.640><c> the</c><00:03:56.959><c> everything</c>

00:03:57.350 --> 00:03:57.360 align:start position:0%
everything. In this case, the everything
 

00:03:57.360 --> 00:04:01.750 align:start position:0%
everything. In this case, the everything
is<00:03:58.000><c> compute.</c><00:03:58.799><c> So,</c><00:03:58.959><c> we've</c><00:03:59.200><c> got</c><00:03:59.599><c> 100,000</c><00:04:00.640><c> H100's</c>

00:04:01.750 --> 00:04:01.760 align:start position:0%
is compute. So, we've got 100,000 H100's
 

00:04:01.760 --> 00:04:03.910 align:start position:0%
is compute. So, we've got 100,000 H100's
Nvidia's<00:04:02.560><c> GPUs,</c><00:04:03.200><c> and</c><00:04:03.360><c> I</c><00:04:03.519><c> think</c><00:04:03.599><c> they're</c><00:04:03.840><c> going</c>

00:04:03.910 --> 00:04:03.920 align:start position:0%
Nvidia's GPUs, and I think they're going
 

00:04:03.920 --> 00:04:06.390 align:start position:0%
Nvidia's GPUs, and I think they're going
to<00:04:04.000><c> be</c><00:04:04.159><c> taking</c><00:04:04.319><c> it</c><00:04:04.560><c> to</c><00:04:04.959><c> 200,000.</c><00:04:05.920><c> Also,</c><00:04:06.319><c> you</c>

00:04:06.390 --> 00:04:06.400 align:start position:0%
to be taking it to 200,000. Also, you
 

00:04:06.400 --> 00:04:07.910 align:start position:0%
to be taking it to 200,000. Also, you
know<00:04:06.480><c> how</c><00:04:06.640><c> sometimes</c><00:04:06.959><c> you</c><00:04:07.200><c> like</c><00:04:07.439><c> order</c><00:04:07.760><c> a</c>

00:04:07.910 --> 00:04:07.920 align:start position:0%
know how sometimes you like order a
 

00:04:07.920 --> 00:04:09.910 align:start position:0%
know how sometimes you like order a
package<00:04:08.159><c> on</c><00:04:08.400><c> Amazon</c><00:04:08.879><c> and</c><00:04:09.120><c> it'll</c><00:04:09.360><c> get</c><00:04:09.599><c> gets</c>

00:04:09.910 --> 00:04:09.920 align:start position:0%
package on Amazon and it'll get gets
 

00:04:09.920 --> 00:04:12.949 align:start position:0%
package on Amazon and it'll get gets
like<00:04:10.239><c> delivered</c><00:04:10.640><c> to</c><00:04:10.799><c> to</c><00:04:11.200><c> your</c><00:04:11.439><c> house.</c><00:04:12.159><c> Elon</c>

00:04:12.949 --> 00:04:12.959 align:start position:0%
like delivered to to your house. Elon
 

00:04:12.959 --> 00:04:15.270 align:start position:0%
like delivered to to your house. Elon
also<00:04:13.680><c> orders</c><00:04:14.080><c> things</c><00:04:14.319><c> to</c><00:04:14.560><c> be</c><00:04:14.720><c> delivered</c><00:04:15.040><c> to</c>

00:04:15.270 --> 00:04:15.280 align:start position:0%
also orders things to be delivered to
 

00:04:15.280 --> 00:04:19.189 align:start position:0%
also orders things to be delivered to
his<00:04:15.519><c> house,</c><00:04:16.000><c> but</c><00:04:16.720><c> he</c><00:04:17.120><c> orders</c><00:04:18.000><c> an</c><00:04:18.479><c> overseas</c>

00:04:19.189 --> 00:04:19.199 align:start position:0%
his house, but he orders an overseas
 

00:04:19.199 --> 00:04:21.509 align:start position:0%
his house, but he orders an overseas
power<00:04:19.600><c> plant,</c><00:04:20.239><c> like</c><00:04:20.400><c> a</c><00:04:20.560><c> like</c><00:04:20.720><c> a</c><00:04:20.959><c> power</c><00:04:21.199><c> plant</c>

00:04:21.509 --> 00:04:21.519 align:start position:0%
power plant, like a like a power plant
 

00:04:21.519 --> 00:04:23.350 align:start position:0%
power plant, like a like a power plant
to<00:04:21.759><c> be</c><00:04:21.919><c> delivered.</c><00:04:22.479><c> So</c><00:04:22.720><c> he's</c><00:04:22.960><c> buying</c><00:04:23.120><c> an</c>

00:04:23.350 --> 00:04:23.360 align:start position:0%
to be delivered. So he's buying an
 

00:04:23.360 --> 00:04:25.350 align:start position:0%
to be delivered. So he's buying an
overseas<00:04:23.840><c> power</c><00:04:24.160><c> plant</c><00:04:24.400><c> and</c><00:04:24.639><c> he's</c><00:04:24.880><c> getting</c><00:04:25.120><c> it</c>

00:04:25.350 --> 00:04:25.360 align:start position:0%
overseas power plant and he's getting it
 

00:04:25.360 --> 00:04:28.710 align:start position:0%
overseas power plant and he's getting it
shipped<00:04:25.919><c> to</c><00:04:26.479><c> Memphis</c><00:04:27.440><c> um</c><00:04:27.680><c> instead</c><00:04:28.080><c> of</c><00:04:28.560><c> you</c>

00:04:28.710 --> 00:04:28.720 align:start position:0%
shipped to Memphis um instead of you
 

00:04:28.720 --> 00:04:30.230 align:start position:0%
shipped to Memphis um instead of you
know<00:04:29.120><c> building</c><00:04:29.440><c> it</c><00:04:29.600><c> here</c><00:04:29.759><c> just</c><00:04:30.000><c> because</c><00:04:30.080><c> of</c>

00:04:30.230 --> 00:04:30.240 align:start position:0%
know building it here just because of
 

00:04:30.240 --> 00:04:31.830 align:start position:0%
know building it here just because of
the<00:04:30.400><c> red</c><00:04:30.560><c> tape.</c><00:04:30.880><c> So</c><00:04:31.040><c> as</c><00:04:31.199><c> you</c><00:04:31.280><c> can</c><00:04:31.360><c> see</c><00:04:31.520><c> here</c><00:04:31.680><c> in</c>

00:04:31.830 --> 00:04:31.840 align:start position:0%
the red tape. So as you can see here in
 

00:04:31.840 --> 00:04:33.990 align:start position:0%
the red tape. So as you can see here in
this<00:04:32.080><c> chart</c><00:04:32.320><c> right</c><00:04:32.560><c> 10x</c><00:04:33.040><c> compute</c><00:04:33.759><c> uh</c>

00:04:33.990 --> 00:04:34.000 align:start position:0%
this chart right 10x compute uh
 

00:04:34.000 --> 00:04:35.590 align:start position:0%
this chart right 10x compute uh
pre-training<00:04:34.479><c> compute</c><00:04:34.800><c> from</c><00:04:34.960><c> Grock</c><00:04:35.360><c> 2</c><00:04:35.520><c> to</c>

00:04:35.590 --> 00:04:35.600 align:start position:0%
pre-training compute from Grock 2 to
 

00:04:35.600 --> 00:04:38.390 align:start position:0%
pre-training compute from Grock 2 to
Grock<00:04:36.000><c> 3</c><00:04:36.320><c> 10x</c><00:04:37.120><c> reinforcement</c><00:04:38.000><c> learning</c>

00:04:38.390 --> 00:04:38.400 align:start position:0%
Grock 3 10x reinforcement learning
 

00:04:38.400 --> 00:04:41.670 align:start position:0%
Grock 3 10x reinforcement learning
compute<00:04:38.880><c> for</c><00:04:39.280><c> Grock</c><00:04:39.919><c> 4</c><00:04:40.400><c> reasoning</c><00:04:40.880><c> 10x</c><00:04:41.440><c> what</c>

00:04:41.670 --> 00:04:41.680 align:start position:0%
compute for Grock 4 reasoning 10x what
 

00:04:41.680 --> 00:04:44.469 align:start position:0%
compute for Grock 4 reasoning 10x what
they<00:04:41.840><c> used</c><00:04:42.160><c> for</c><00:04:42.479><c> Grock</c><00:04:43.120><c> 3</c><00:04:43.520><c> reasoning.</c><00:04:44.080><c> Now</c><00:04:44.320><c> it</c>

00:04:44.469 --> 00:04:44.479 align:start position:0%
they used for Grock 3 reasoning. Now it
 

00:04:44.479 --> 00:04:47.110 align:start position:0%
they used for Grock 3 reasoning. Now it
looks<00:04:44.639><c> like</c><00:04:44.800><c> here</c><00:04:45.199><c> the</c><00:04:45.840><c> pre-training</c><00:04:46.479><c> compute</c>

00:04:47.110 --> 00:04:47.120 align:start position:0%
looks like here the pre-training compute
 

00:04:47.120 --> 00:04:50.150 align:start position:0%
looks like here the pre-training compute
is<00:04:47.600><c> the</c><00:04:47.919><c> same</c><00:04:48.240><c> and</c><00:04:48.479><c> the</c><00:04:48.639><c> 10x</c><00:04:49.280><c> RL</c><00:04:49.680><c> computes</c>

00:04:50.150 --> 00:04:50.160 align:start position:0%
is the same and the 10x RL computes
 

00:04:50.160 --> 00:04:52.629 align:start position:0%
is the same and the 10x RL computes
going<00:04:50.400><c> on</c><00:04:50.639><c> top</c><00:04:50.880><c> of</c><00:04:51.040><c> that</c><00:04:51.680><c> and</c><00:04:51.919><c> they've</c><00:04:52.240><c> also</c>

00:04:52.629 --> 00:04:52.639 align:start position:0%
going on top of that and they've also
 

00:04:52.639 --> 00:04:54.790 align:start position:0%
going on top of that and they've also
mentioned<00:04:53.040><c> that</c><00:04:53.199><c> the</c><00:04:53.759><c> next</c><00:04:54.160><c> generation</c><00:04:54.560><c> the</c>

00:04:54.790 --> 00:04:54.800 align:start position:0%
mentioned that the next generation the
 

00:04:54.800 --> 00:04:57.670 align:start position:0%
mentioned that the next generation the
Gro<00:04:55.199><c> 5</c><00:04:55.520><c> is</c><00:04:55.919><c> starting</c><00:04:56.400><c> training</c><00:04:56.880><c> right</c><00:04:57.120><c> now.</c>

00:04:57.670 --> 00:04:57.680 align:start position:0%
Gro 5 is starting training right now.
 

00:04:57.680 --> 00:05:00.310 align:start position:0%
Gro 5 is starting training right now.
This<00:04:57.919><c> to</c><00:04:58.160><c> me</c><00:04:58.320><c> kind</c><00:04:58.479><c> of</c><00:04:58.720><c> reinforces</c><00:04:59.360><c> that</c><00:04:59.680><c> idea</c>

00:05:00.310 --> 00:05:00.320 align:start position:0%
This to me kind of reinforces that idea
 

00:05:00.320 --> 00:05:02.710 align:start position:0%
This to me kind of reinforces that idea
brought<00:05:00.639><c> by</c><00:05:01.120><c> someone</c><00:05:01.440><c> from</c><00:05:01.680><c> OpenAI</c><00:05:02.479><c> where</c>

00:05:02.710 --> 00:05:02.720 align:start position:0%
brought by someone from OpenAI where
 

00:05:02.720 --> 00:05:04.310 align:start position:0%
brought by someone from OpenAI where
they're<00:05:02.960><c> saying</c><00:05:03.199><c> like</c><00:05:03.440><c> basically</c><00:05:03.919><c> that</c><00:05:04.240><c> for</c>

00:05:04.310 --> 00:05:04.320 align:start position:0%
they're saying like basically that for
 

00:05:04.320 --> 00:05:07.270 align:start position:0%
they're saying like basically that for
example<00:05:04.560><c> on</c><00:05:04.800><c> the</c><00:05:05.040><c> GPT40</c><00:05:06.240><c> which</c><00:05:06.560><c> was</c><00:05:06.720><c> not</c><00:05:06.880><c> a</c>

00:05:07.270 --> 00:05:07.280 align:start position:0%
example on the GPT40 which was not a
 

00:05:07.280 --> 00:05:09.350 align:start position:0%
example on the GPT40 which was not a
reasoning<00:05:07.759><c> model</c><00:05:08.160><c> right</c><00:05:08.560><c> all</c><00:05:08.720><c> of</c><00:05:08.800><c> the</c><00:05:09.039><c> compute</c>

00:05:09.350 --> 00:05:09.360 align:start position:0%
reasoning model right all of the compute
 

00:05:09.360 --> 00:05:12.070 align:start position:0%
reasoning model right all of the compute
went<00:05:09.600><c> towards</c><00:05:10.080><c> pre-training</c><00:05:11.199><c> the</c><00:05:11.520><c> 01</c><00:05:11.840><c> which</c>

00:05:12.070 --> 00:05:12.080 align:start position:0%
went towards pre-training the 01 which
 

00:05:12.080 --> 00:05:14.150 align:start position:0%
went towards pre-training the 01 which
was<00:05:12.240><c> the</c><00:05:12.400><c> first</c><00:05:12.960><c> reasoning</c><00:05:13.440><c> model</c><00:05:13.759><c> right</c><00:05:13.919><c> so</c>

00:05:14.150 --> 00:05:14.160 align:start position:0%
was the first reasoning model right so
 

00:05:14.160 --> 00:05:16.310 align:start position:0%
was the first reasoning model right so
it<00:05:14.320><c> was</c><00:05:14.400><c> able</c><00:05:14.560><c> to</c><00:05:14.880><c> reason</c><00:05:15.199><c> before</c><00:05:15.440><c> it</c><00:05:15.680><c> answered</c>

00:05:16.310 --> 00:05:16.320 align:start position:0%
it was able to reason before it answered
 

00:05:16.320 --> 00:05:18.390 align:start position:0%
it was able to reason before it answered
right<00:05:16.479><c> so</c><00:05:16.639><c> we</c><00:05:16.880><c> used</c><00:05:17.199><c> reinforcement</c><00:05:18.080><c> learning</c>

00:05:18.390 --> 00:05:18.400 align:start position:0%
right so we used reinforcement learning
 

00:05:18.400 --> 00:05:21.670 align:start position:0%
right so we used reinforcement learning
compute<00:05:19.120><c> but</c><00:05:19.360><c> it</c><00:05:19.600><c> was</c><00:05:19.680><c> a</c><00:05:20.160><c> tiny</c><00:05:20.560><c> fraction</c><00:05:21.199><c> of</c>

00:05:21.670 --> 00:05:21.680 align:start position:0%
compute but it was a tiny fraction of
 

00:05:21.680 --> 00:05:23.430 align:start position:0%
compute but it was a tiny fraction of
the<00:05:21.919><c> total</c><00:05:22.400><c> sort</c><00:05:22.560><c> of</c><00:05:22.720><c> the</c><00:05:22.880><c> pre-training</c>

00:05:23.430 --> 00:05:23.440 align:start position:0%
the total sort of the pre-training
 

00:05:23.440 --> 00:05:27.590 align:start position:0%
the total sort of the pre-training
compute<00:05:24.000><c> 03</c><00:05:24.720><c> we</c><00:05:25.039><c> used</c><00:05:25.919><c> more</c><00:05:26.639><c> reinforcement</c>

00:05:27.590 --> 00:05:27.600 align:start position:0%
compute 03 we used more reinforcement
 

00:05:27.600 --> 00:05:29.510 align:start position:0%
compute 03 we used more reinforcement
learning<00:05:27.919><c> compute.</c><00:05:28.479><c> So,</c><00:05:28.639><c> it's</c><00:05:28.880><c> sort</c><00:05:29.039><c> of</c><00:05:29.120><c> like</c>

00:05:29.510 --> 00:05:29.520 align:start position:0%
learning compute. So, it's sort of like
 

00:05:29.520 --> 00:05:31.510 align:start position:0%
learning compute. So, it's sort of like
growing<00:05:29.840><c> in</c><00:05:30.080><c> proportion</c><00:05:30.560><c> to</c><00:05:31.039><c> the</c>

00:05:31.510 --> 00:05:31.520 align:start position:0%
growing in proportion to the
 

00:05:31.520 --> 00:05:33.510 align:start position:0%
growing in proportion to the
pre-training<00:05:32.240><c> compute.</c><00:05:32.800><c> At</c><00:05:32.960><c> some</c><00:05:33.199><c> point,</c><00:05:33.360><c> we</c>

00:05:33.510 --> 00:05:33.520 align:start position:0%
pre-training compute. At some point, we
 

00:05:33.520 --> 00:05:35.430 align:start position:0%
pre-training compute. At some point, we
expect<00:05:33.759><c> it</c><00:05:34.000><c> to</c><00:05:34.080><c> get</c><00:05:34.240><c> closer</c><00:05:34.560><c> and</c><00:05:34.720><c> closer</c><00:05:35.120><c> in</c>

00:05:35.430 --> 00:05:35.440 align:start position:0%
expect it to get closer and closer in
 

00:05:35.440 --> 00:05:38.230 align:start position:0%
expect it to get closer and closer in
size<00:05:36.320><c> to</c><00:05:36.639><c> pre-training</c><00:05:37.440><c> compute,</c><00:05:37.759><c> right?</c><00:05:38.000><c> So,</c>

00:05:38.230 --> 00:05:38.240 align:start position:0%
size to pre-training compute, right? So,
 

00:05:38.240 --> 00:05:39.830 align:start position:0%
size to pre-training compute, right? So,
RL<00:05:38.639><c> is</c><00:05:38.720><c> going</c><00:05:38.880><c> to</c><00:05:38.960><c> keep</c><00:05:39.280><c> growing.</c>

00:05:39.830 --> 00:05:39.840 align:start position:0%
RL is going to keep growing.
 

00:05:39.840 --> 00:05:41.270 align:start position:0%
RL is going to keep growing.
Reinforcement<00:05:40.479><c> learning,</c><00:05:40.880><c> how</c><00:05:41.039><c> much</c>

00:05:41.270 --> 00:05:41.280 align:start position:0%
Reinforcement learning, how much
 

00:05:41.280 --> 00:05:42.629 align:start position:0%
Reinforcement learning, how much
hardware<00:05:41.680><c> we</c><00:05:41.840><c> throw</c><00:05:42.000><c> at</c><00:05:42.160><c> that</c><00:05:42.400><c> is</c><00:05:42.560><c> going</c><00:05:42.560><c> to</c>

00:05:42.629 --> 00:05:42.639 align:start position:0%
hardware we throw at that is going to
 

00:05:42.639 --> 00:05:44.550 align:start position:0%
hardware we throw at that is going to
keep<00:05:42.800><c> growing</c><00:05:43.120><c> and</c><00:05:43.280><c> growing,</c><00:05:43.840><c> eventually</c>

00:05:44.550 --> 00:05:44.560 align:start position:0%
keep growing and growing, eventually
 

00:05:44.560 --> 00:05:46.870 align:start position:0%
keep growing and growing, eventually
completely<00:05:45.199><c> dwarving</c><00:05:45.759><c> the</c><00:05:46.160><c> uh</c><00:05:46.320><c> pre-training</c>

00:05:46.870 --> 00:05:46.880 align:start position:0%
completely dwarving the uh pre-training
 

00:05:46.880 --> 00:05:48.230 align:start position:0%
completely dwarving the uh pre-training
compute.<00:05:47.280><c> By</c><00:05:47.440><c> the</c><00:05:47.520><c> way,</c><00:05:47.680><c> this</c><00:05:47.919><c> might</c><00:05:48.080><c> mean</c>

00:05:48.230 --> 00:05:48.240 align:start position:0%
compute. By the way, this might mean
 

00:05:48.240 --> 00:05:50.070 align:start position:0%
compute. By the way, this might mean
that<00:05:48.320><c> we're</c><00:05:48.479><c> throwing</c><00:05:48.800><c> the</c><00:05:49.039><c> same</c><00:05:49.199><c> amount</c><00:05:49.600><c> of</c>

00:05:50.070 --> 00:05:50.080 align:start position:0%
that we're throwing the same amount of
 

00:05:50.080 --> 00:05:51.749 align:start position:0%
that we're throwing the same amount of
pre-training<00:05:50.639><c> compute.</c><00:05:51.039><c> This</c><00:05:51.199><c> little</c><00:05:51.440><c> circle</c>

00:05:51.749 --> 00:05:51.759 align:start position:0%
pre-training compute. This little circle
 

00:05:51.759 --> 00:05:54.710 align:start position:0%
pre-training compute. This little circle
is<00:05:52.400><c> the</c><00:05:52.639><c> same</c><00:05:52.800><c> as</c><00:05:53.039><c> this,</c><00:05:53.680><c> but</c><00:05:53.919><c> the</c><00:05:54.160><c> amount</c><00:05:54.400><c> of</c>

00:05:54.710 --> 00:05:54.720 align:start position:0%
is the same as this, but the amount of
 

00:05:54.720 --> 00:05:56.310 align:start position:0%
is the same as this, but the amount of
reinforcement<00:05:55.280><c> learning</c><00:05:55.520><c> compute</c><00:05:55.919><c> that</c><00:05:56.160><c> we</c>

00:05:56.310 --> 00:05:56.320 align:start position:0%
reinforcement learning compute that we
 

00:05:56.320 --> 00:05:58.550 align:start position:0%
reinforcement learning compute that we
throw<00:05:56.479><c> at</c><00:05:56.639><c> it</c><00:05:56.800><c> is</c><00:05:57.280><c> 10</c><00:05:57.520><c> times</c><00:05:57.759><c> more,</c><00:05:58.080><c> 20</c><00:05:58.320><c> times</c>

00:05:58.550 --> 00:05:58.560 align:start position:0%
throw at it is 10 times more, 20 times
 

00:05:58.560 --> 00:06:01.830 align:start position:0%
throw at it is 10 times more, 20 times
more.<00:05:59.120><c> That</c><00:05:59.360><c> seems</c><00:05:59.759><c> to</c><00:06:00.000><c> be</c><00:06:00.240><c> what</c><00:06:00.960><c> this</c><00:06:01.360><c> is</c>

00:06:01.830 --> 00:06:01.840 align:start position:0%
more. That seems to be what this is
 

00:06:01.840 --> 00:06:04.629 align:start position:0%
more. That seems to be what this is
suggesting,<00:06:02.720><c> right?</c><00:06:03.039><c> So,</c><00:06:03.199><c> this</c><00:06:03.440><c> idea</c><00:06:03.919><c> that</c>

00:06:04.629 --> 00:06:04.639 align:start position:0%
suggesting, right? So, this idea that
 

00:06:04.639 --> 00:06:06.950 align:start position:0%
suggesting, right? So, this idea that
scaling<00:06:05.120><c> is</c><00:06:05.360><c> hitting</c><00:06:05.600><c> a</c><00:06:05.759><c> wall,</c><00:06:06.080><c> I</c><00:06:06.319><c> think,</c><00:06:06.400><c> is</c>

00:06:06.950 --> 00:06:06.960 align:start position:0%
scaling is hitting a wall, I think, is
 

00:06:06.960 --> 00:06:08.950 align:start position:0%
scaling is hitting a wall, I think, is
misleading.<00:06:07.919><c> And</c><00:06:08.080><c> really,</c><00:06:08.400><c> what</c><00:06:08.560><c> we</c><00:06:08.800><c> should</c>

00:06:08.950 --> 00:06:08.960 align:start position:0%
misleading. And really, what we should
 

00:06:08.960 --> 00:06:11.270 align:start position:0%
misleading. And really, what we should
be<00:06:09.120><c> keeping</c><00:06:09.360><c> an</c><00:06:09.520><c> eye</c><00:06:09.680><c> on</c><00:06:10.000><c> is</c><00:06:10.400><c> this.</c><00:06:10.880><c> They</c><00:06:11.120><c> said</c>

00:06:11.270 --> 00:06:11.280 align:start position:0%
be keeping an eye on is this. They said
 

00:06:11.280 --> 00:06:13.830 align:start position:0%
be keeping an eye on is this. They said
that<00:06:11.440><c> they're</c><00:06:11.680><c> going</c><00:06:11.759><c> to</c><00:06:12.000><c> release</c><00:06:12.639><c> Gro</c><00:06:13.039><c> 4's</c>

00:06:13.830 --> 00:06:13.840 align:start position:0%
that they're going to release Gro 4's
 

00:06:13.840 --> 00:06:16.870 align:start position:0%
that they're going to release Gro 4's
coding<00:06:14.319><c> model</c><00:06:14.880><c> within</c><00:06:15.520><c> weeks.</c><00:06:16.160><c> So,</c><00:06:16.400><c> probably</c>

00:06:16.870 --> 00:06:16.880 align:start position:0%
coding model within weeks. So, probably
 

00:06:16.880 --> 00:06:18.790 align:start position:0%
coding model within weeks. So, probably
I<00:06:17.039><c> think</c><00:06:17.120><c> they</c><00:06:17.280><c> said</c><00:06:17.520><c> four</c><00:06:17.840><c> weeks</c><00:06:18.080><c> or</c><00:06:18.240><c> so.</c><00:06:18.560><c> So</c>

00:06:18.790 --> 00:06:18.800 align:start position:0%
I think they said four weeks or so. So
 

00:06:18.800 --> 00:06:20.070 align:start position:0%
I think they said four weeks or so. So
probably<00:06:19.039><c> we're</c><00:06:19.280><c> going</c><00:06:19.360><c> to</c><00:06:19.440><c> see</c><00:06:19.600><c> it</c><00:06:19.759><c> sometime</c>

00:06:20.070 --> 00:06:20.080 align:start position:0%
probably we're going to see it sometime
 

00:06:20.080 --> 00:06:22.390 align:start position:0%
probably we're going to see it sometime
in<00:06:20.479><c> August.</c><00:06:21.360><c> That</c><00:06:21.600><c> to</c><00:06:21.759><c> me</c><00:06:21.840><c> is</c><00:06:22.000><c> going</c><00:06:22.080><c> to</c><00:06:22.160><c> be</c><00:06:22.240><c> the</c>

00:06:22.390 --> 00:06:22.400 align:start position:0%
in August. That to me is going to be the
 

00:06:22.400 --> 00:06:23.590 align:start position:0%
in August. That to me is going to be the
ultimate<00:06:22.639><c> test</c><00:06:22.880><c> because</c><00:06:23.120><c> we're</c><00:06:23.360><c> going</c><00:06:23.440><c> to</c><00:06:23.520><c> be</c>

00:06:23.590 --> 00:06:23.600 align:start position:0%
ultimate test because we're going to be
 

00:06:23.600 --> 00:06:26.390 align:start position:0%
ultimate test because we're going to be
able<00:06:23.840><c> to</c><00:06:24.080><c> take</c><00:06:24.560><c> that</c><00:06:25.039><c> coding</c><00:06:25.440><c> model</c><00:06:25.840><c> and</c>

00:06:26.390 --> 00:06:26.400 align:start position:0%
able to take that coding model and
 

00:06:26.400 --> 00:06:28.710 align:start position:0%
able to take that coding model and
really<00:06:26.800><c> put</c><00:06:27.039><c> it</c><00:06:27.280><c> head-to-head</c><00:06:27.840><c> with</c><00:06:28.080><c> the</c><00:06:28.479><c> rest</c>

00:06:28.710 --> 00:06:28.720 align:start position:0%
really put it head-to-head with the rest
 

00:06:28.720 --> 00:06:30.790 align:start position:0%
really put it head-to-head with the rest
of<00:06:28.880><c> the</c><00:06:29.039><c> coding</c><00:06:29.360><c> models</c><00:06:29.919><c> to</c><00:06:30.240><c> see</c><00:06:30.400><c> what</c><00:06:30.560><c> that</c>

00:06:30.790 --> 00:06:30.800 align:start position:0%
of the coding models to see what that
 

00:06:30.800 --> 00:06:32.790 align:start position:0%
of the coding models to see what that
kind<00:06:30.960><c> of</c><00:06:31.039><c> final</c><00:06:31.520><c> version</c><00:06:31.759><c> of</c><00:06:31.919><c> that</c><00:06:32.319><c> looks</c><00:06:32.560><c> like</c>

00:06:32.790 --> 00:06:32.800 align:start position:0%
kind of final version of that looks like
 

00:06:32.800 --> 00:06:35.670 align:start position:0%
kind of final version of that looks like
because<00:06:33.280><c> right</c><00:06:33.600><c> now,</c><00:06:34.000><c> you</c><00:06:34.160><c> know,</c><00:06:34.720><c> testing</c><00:06:35.199><c> Gro</c>

00:06:35.670 --> 00:06:35.680 align:start position:0%
because right now, you know, testing Gro
 

00:06:35.680 --> 00:06:38.790 align:start position:0%
because right now, you know, testing Gro
4<00:06:36.160><c> and</c><00:06:36.560><c> Gro</c><00:06:37.039><c> 4</c><00:06:37.520><c> heavy,</c><00:06:38.160><c> you</c><00:06:38.240><c> know,</c><00:06:38.319><c> the</c><00:06:38.479><c> coding</c>

00:06:38.790 --> 00:06:38.800 align:start position:0%
4 and Gro 4 heavy, you know, the coding
 

00:06:38.800 --> 00:06:40.230 align:start position:0%
4 and Gro 4 heavy, you know, the coding
looks<00:06:39.199><c> good,</c><00:06:39.440><c> but</c><00:06:39.680><c> it's</c><00:06:39.840><c> not</c><00:06:40.000><c> like</c>

00:06:40.230 --> 00:06:40.240 align:start position:0%
looks good, but it's not like
 

00:06:40.240 --> 00:06:42.710 align:start position:0%
looks good, but it's not like
mind-blowing.<00:06:41.039><c> It's</c><00:06:41.199><c> not</c><00:06:41.600><c> the</c><00:06:41.919><c> big</c><00:06:42.160><c> leap</c><00:06:42.479><c> that</c>

00:06:42.710 --> 00:06:42.720 align:start position:0%
mind-blowing. It's not the big leap that
 

00:06:42.720 --> 00:06:44.230 align:start position:0%
mind-blowing. It's not the big leap that
you<00:06:42.880><c> would</c><00:06:43.039><c> expect,</c><00:06:43.360><c> and</c><00:06:43.600><c> that's</c><00:06:43.759><c> because</c><00:06:44.000><c> the</c>

00:06:44.230 --> 00:06:44.240 align:start position:0%
you would expect, and that's because the
 

00:06:44.240 --> 00:06:46.230 align:start position:0%
you would expect, and that's because the
that's<00:06:44.479><c> not</c><00:06:44.560><c> the</c><00:06:44.720><c> coding</c><00:06:45.039><c> model</c><00:06:45.280><c> yet.</c><00:06:45.600><c> the</c>

00:06:46.230 --> 00:06:46.240 align:start position:0%
that's not the coding model yet. the
 

00:06:46.240 --> 00:06:48.550 align:start position:0%
that's not the coding model yet. the
rail<00:06:46.560><c> that</c><00:06:46.880><c> goes</c><00:06:47.120><c> into</c><00:06:47.600><c> making</c><00:06:47.919><c> it</c><00:06:48.080><c> a</c><00:06:48.319><c> good</c>

00:06:48.550 --> 00:06:48.560 align:start position:0%
rail that goes into making it a good
 

00:06:48.560 --> 00:06:49.909 align:start position:0%
rail that goes into making it a good
coder<00:06:48.880><c> that</c><00:06:49.039><c> hasn't</c><00:06:49.280><c> been</c><00:06:49.440><c> completed</c><00:06:49.680><c> yet.</c>

00:06:49.909 --> 00:06:49.919 align:start position:0%
coder that hasn't been completed yet.
 

00:06:49.919 --> 00:06:51.350 align:start position:0%
coder that hasn't been completed yet.
We're<00:06:50.080><c> going</c><00:06:50.080><c> to</c><00:06:50.160><c> see</c><00:06:50.240><c> that</c><00:06:50.400><c> in</c><00:06:50.560><c> four</c><00:06:50.800><c> weeks</c>

00:06:51.350 --> 00:06:51.360 align:start position:0%
We're going to see that in four weeks
 

00:06:51.360 --> 00:06:53.430 align:start position:0%
We're going to see that in four weeks
hopefully<00:06:52.000><c> or</c><00:06:52.400><c> you</c><00:06:52.560><c> know</c><00:06:52.720><c> whatever</c><00:06:53.039><c> 4</c><00:06:53.280><c> weeks</c>

00:06:53.430 --> 00:06:53.440 align:start position:0%
hopefully or you know whatever 4 weeks
 

00:06:53.440 --> 00:06:56.309 align:start position:0%
hopefully or you know whatever 4 weeks
is<00:06:53.600><c> an</c><00:06:53.759><c> Elon</c><00:06:54.160><c> time.</c><00:06:54.560><c> So</c><00:06:55.199><c> we'll</c><00:06:55.440><c> see.</c><00:06:55.919><c> But</c><00:06:56.080><c> once</c>

00:06:56.309 --> 00:06:56.319 align:start position:0%
is an Elon time. So we'll see. But once
 

00:06:56.319 --> 00:06:57.430 align:start position:0%
is an Elon time. So we'll see. But once
that<00:06:56.560><c> comes</c><00:06:56.720><c> out</c><00:06:56.960><c> it'll</c><00:06:57.199><c> be</c><00:06:57.280><c> really</c>

00:06:57.430 --> 00:06:57.440 align:start position:0%
that comes out it'll be really
 

00:06:57.440 --> 00:06:59.029 align:start position:0%
that comes out it'll be really
interesting<00:06:57.759><c> to</c><00:06:58.000><c> know</c><00:06:58.479><c> what</c><00:06:58.720><c> kind</c><00:06:58.800><c> of</c><00:06:58.880><c> an</c>

00:06:59.029 --> 00:06:59.039 align:start position:0%
interesting to know what kind of an
 

00:06:59.039 --> 00:07:00.550 align:start position:0%
interesting to know what kind of an
effect<00:06:59.280><c> that's</c><00:06:59.520><c> going</c><00:06:59.599><c> to</c><00:06:59.680><c> have</c><00:07:00.000><c> versus</c><00:07:00.400><c> kind</c>

00:07:00.550 --> 00:07:00.560 align:start position:0%
effect that's going to have versus kind
 

00:07:00.560 --> 00:07:01.909 align:start position:0%
effect that's going to have versus kind
of<00:07:00.639><c> like</c><00:07:00.800><c> the</c><00:07:01.039><c> coding</c><00:07:01.280><c> models</c><00:07:01.599><c> that</c><00:07:01.680><c> we</c><00:07:01.840><c> have</c>

00:07:01.909 --> 00:07:01.919 align:start position:0%
of like the coding models that we have
 

00:07:01.919 --> 00:07:03.430 align:start position:0%
of like the coding models that we have
now.<00:07:02.160><c> Is</c><00:07:02.319><c> it</c><00:07:02.479><c> going</c><00:07:02.560><c> to</c><00:07:02.639><c> be</c><00:07:02.880><c> massively</c>

00:07:03.430 --> 00:07:03.440 align:start position:0%
now. Is it going to be massively
 

00:07:03.440 --> 00:07:05.350 align:start position:0%
now. Is it going to be massively
massively<00:07:03.840><c> better?</c><00:07:04.400><c> Time</c><00:07:04.639><c> will</c><00:07:04.880><c> tell.</c><00:07:05.120><c> So</c>

00:07:05.350 --> 00:07:05.360 align:start position:0%
massively better? Time will tell. So
 

00:07:05.360 --> 00:07:07.270 align:start position:0%
massively better? Time will tell. So
wait<00:07:05.599><c> for</c><00:07:05.759><c> that.</c><00:07:06.160><c> But</c><00:07:06.400><c> this</c><00:07:06.639><c> is</c><00:07:06.720><c> where</c><00:07:07.039><c> things</c>

00:07:07.270 --> 00:07:07.280 align:start position:0%
wait for that. But this is where things
 

00:07:07.280 --> 00:07:09.670 align:start position:0%
wait for that. But this is where things
get<00:07:07.520><c> super</c><00:07:07.840><c> interesting.</c><00:07:08.560><c> there</c><00:07:09.039><c> seems</c><00:07:09.360><c> to</c><00:07:09.520><c> be</c>

00:07:09.670 --> 00:07:09.680 align:start position:0%
get super interesting. there seems to be
 

00:07:09.680 --> 00:07:12.550 align:start position:0%
get super interesting. there seems to be
this<00:07:10.000><c> new</c><00:07:10.639><c> metric</c><00:07:11.360><c> developing</c><00:07:11.919><c> and</c><00:07:12.319><c> uh</c><00:07:12.400><c> I</c>

00:07:12.550 --> 00:07:12.560 align:start position:0%
this new metric developing and uh I
 

00:07:12.560 --> 00:07:14.550 align:start position:0%
this new metric developing and uh I
don't<00:07:12.639><c> know</c><00:07:12.800><c> if</c><00:07:12.960><c> we</c><00:07:13.199><c> even</c><00:07:13.599><c> necessarily</c><00:07:14.000><c> have</c><00:07:14.160><c> a</c>

00:07:14.550 --> 00:07:14.560 align:start position:0%
don't know if we even necessarily have a
 

00:07:14.560 --> 00:07:16.469 align:start position:0%
don't know if we even necessarily have a
word<00:07:14.720><c> for</c><00:07:14.880><c> it</c><00:07:15.039><c> yet</c><00:07:15.599><c> but</c><00:07:15.919><c> basically</c><00:07:16.240><c> there's</c>

00:07:16.469 --> 00:07:16.479 align:start position:0%
word for it yet but basically there's
 

00:07:16.479 --> 00:07:19.510 align:start position:0%
word for it yet but basically there's
this<00:07:16.720><c> idea</c><00:07:17.199><c> of</c><00:07:17.919><c> crystallized</c><00:07:18.720><c> intelligence</c>

00:07:19.510 --> 00:07:19.520 align:start position:0%
this idea of crystallized intelligence
 

00:07:19.520 --> 00:07:22.390 align:start position:0%
this idea of crystallized intelligence
and<00:07:20.160><c> fluid</c><00:07:20.720><c> intelligence.</c><00:07:21.759><c> So</c><00:07:22.000><c> fluid</c>

00:07:22.390 --> 00:07:22.400 align:start position:0%
and fluid intelligence. So fluid
 

00:07:22.400 --> 00:07:23.749 align:start position:0%
and fluid intelligence. So fluid
intelligence<00:07:22.800><c> you</c><00:07:22.960><c> can</c><00:07:23.039><c> think</c><00:07:23.199><c> of</c><00:07:23.360><c> as</c><00:07:23.520><c> the</c>

00:07:23.749 --> 00:07:23.759 align:start position:0%
intelligence you can think of as the
 

00:07:23.759 --> 00:07:26.469 align:start position:0%
intelligence you can think of as the
ability<00:07:24.080><c> to</c><00:07:24.319><c> solve</c><00:07:24.880><c> new</c><00:07:25.280><c> problems,</c><00:07:26.319><c> stuff</c>

00:07:26.469 --> 00:07:26.479 align:start position:0%
ability to solve new problems, stuff
 

00:07:26.479 --> 00:07:27.670 align:start position:0%
ability to solve new problems, stuff
that<00:07:26.639><c> you</c><00:07:26.880><c> haven't</c><00:07:27.039><c> really</c><00:07:27.280><c> dealt</c><00:07:27.520><c> with</c>

00:07:27.670 --> 00:07:27.680 align:start position:0%
that you haven't really dealt with
 

00:07:27.680 --> 00:07:29.670 align:start position:0%
that you haven't really dealt with
before,<00:07:28.319><c> right?</c><00:07:28.560><c> So</c><00:07:28.639><c> if</c><00:07:28.800><c> you're</c><00:07:29.039><c> thrown</c><00:07:29.360><c> into</c>

00:07:29.670 --> 00:07:29.680 align:start position:0%
before, right? So if you're thrown into
 

00:07:29.680 --> 00:07:32.390 align:start position:0%
before, right? So if you're thrown into
a<00:07:30.080><c> new</c><00:07:30.319><c> situation</c><00:07:31.120><c> regardless</c><00:07:31.680><c> of</c><00:07:32.000><c> your</c>

00:07:32.390 --> 00:07:32.400 align:start position:0%
a new situation regardless of your
 

00:07:32.400 --> 00:07:34.309 align:start position:0%
a new situation regardless of your
previous<00:07:32.800><c> experience</c><00:07:33.360><c> or</c><00:07:33.759><c> education</c><00:07:34.160><c> or</c>

00:07:34.309 --> 00:07:34.319 align:start position:0%
previous experience or education or
 

00:07:34.319 --> 00:07:35.749 align:start position:0%
previous experience or education or
something<00:07:34.479><c> like</c><00:07:34.639><c> that,</c><00:07:34.960><c> how</c><00:07:35.199><c> well</c><00:07:35.440><c> are</c><00:07:35.599><c> you</c>

00:07:35.749 --> 00:07:35.759 align:start position:0%
something like that, how well are you
 

00:07:35.759 --> 00:07:38.390 align:start position:0%
something like that, how well are you
able<00:07:36.000><c> to</c><00:07:36.160><c> deal</c><00:07:36.400><c> with</c><00:07:36.639><c> those</c><00:07:37.039><c> new</c><00:07:37.360><c> challenges?</c>

00:07:38.390 --> 00:07:38.400 align:start position:0%
able to deal with those new challenges?
 

00:07:38.400 --> 00:07:40.870 align:start position:0%
able to deal with those new challenges?
This<00:07:38.720><c> is</c><00:07:39.120><c> as</c><00:07:39.360><c> opposed</c><00:07:39.759><c> to</c><00:07:40.080><c> something</c><00:07:40.400><c> like</c>

00:07:40.870 --> 00:07:40.880 align:start position:0%
This is as opposed to something like
 

00:07:40.880 --> 00:07:42.710 align:start position:0%
This is as opposed to something like
crystallized<00:07:41.680><c> intelligence.</c><00:07:42.319><c> That's</c><00:07:42.560><c> kind</c>

00:07:42.710 --> 00:07:42.720 align:start position:0%
crystallized intelligence. That's kind
 

00:07:42.720 --> 00:07:44.790 align:start position:0%
crystallized intelligence. That's kind
of<00:07:42.800><c> like</c><00:07:42.960><c> the</c><00:07:43.280><c> ability</c><00:07:43.840><c> for</c><00:07:44.000><c> us</c><00:07:44.160><c> to</c><00:07:44.400><c> draw</c><00:07:44.639><c> on</c>

00:07:44.790 --> 00:07:44.800 align:start position:0%
of like the ability for us to draw on
 

00:07:44.800 --> 00:07:46.469 align:start position:0%
of like the ability for us to draw on
our<00:07:44.960><c> past</c><00:07:45.199><c> and</c><00:07:45.440><c> our</c><00:07:45.599><c> experience.</c><00:07:46.080><c> It's</c><00:07:46.319><c> kind</c>

00:07:46.469 --> 00:07:46.479 align:start position:0%
our past and our experience. It's kind
 

00:07:46.479 --> 00:07:48.230 align:start position:0%
our past and our experience. It's kind
of<00:07:46.560><c> like</c><00:07:46.960><c> what</c><00:07:47.199><c> we</c><00:07:47.360><c> sort</c><00:07:47.520><c> of</c><00:07:47.599><c> over</c><00:07:47.919><c> time</c>

00:07:48.230 --> 00:07:48.240 align:start position:0%
of like what we sort of over time
 

00:07:48.240 --> 00:07:49.830 align:start position:0%
of like what we sort of over time
develop.<00:07:48.880><c> Generally,</c><00:07:49.199><c> when</c><00:07:49.360><c> you're</c><00:07:49.520><c> younger,</c>

00:07:49.830 --> 00:07:49.840 align:start position:0%
develop. Generally, when you're younger,
 

00:07:49.840 --> 00:07:51.430 align:start position:0%
develop. Generally, when you're younger,
you<00:07:50.080><c> probably</c><00:07:50.319><c> have</c><00:07:50.560><c> a</c><00:07:50.800><c> better</c><00:07:51.039><c> fluid</c>

00:07:51.430 --> 00:07:51.440 align:start position:0%
you probably have a better fluid
 

00:07:51.440 --> 00:07:53.110 align:start position:0%
you probably have a better fluid
intelligence,<00:07:51.919><c> and</c><00:07:52.160><c> as</c><00:07:52.319><c> you</c><00:07:52.400><c> get</c><00:07:52.560><c> older,</c><00:07:52.880><c> you</c>

00:07:53.110 --> 00:07:53.120 align:start position:0%
intelligence, and as you get older, you
 

00:07:53.120 --> 00:07:54.469 align:start position:0%
intelligence, and as you get older, you
probably<00:07:53.360><c> have</c><00:07:53.599><c> better</c><00:07:53.919><c> crystallized</c>

00:07:54.469 --> 00:07:54.479 align:start position:0%
probably have better crystallized
 

00:07:54.479 --> 00:07:56.070 align:start position:0%
probably have better crystallized
intelligence.<00:07:54.879><c> Again,</c><00:07:55.120><c> that</c><00:07:55.360><c> that's</c><00:07:55.599><c> kind</c><00:07:55.759><c> of</c>

00:07:56.070 --> 00:07:56.080 align:start position:0%
intelligence. Again, that that's kind of
 

00:07:56.080 --> 00:07:58.390 align:start position:0%
intelligence. Again, that that's kind of
generalizing,<00:07:56.720><c> but</c><00:07:57.440><c> fluid</c><00:07:57.759><c> intelligence</c>

00:07:58.390 --> 00:07:58.400 align:start position:0%
generalizing, but fluid intelligence
 

00:07:58.400 --> 00:08:01.350 align:start position:0%
generalizing, but fluid intelligence
typically<00:07:59.120><c> peaks</c><00:07:59.680><c> in</c><00:08:00.240><c> early</c><00:08:00.639><c> adulthood.</c>

00:08:01.350 --> 00:08:01.360 align:start position:0%
typically peaks in early adulthood.
 

00:08:01.360 --> 00:08:02.550 align:start position:0%
typically peaks in early adulthood.
That's<00:08:01.520><c> why</c><00:08:01.680><c> they</c><00:08:01.840><c> sometimes</c><00:08:02.160><c> say</c><00:08:02.319><c> it's</c>

00:08:02.550 --> 00:08:02.560 align:start position:0%
That's why they sometimes say it's
 

00:08:02.560 --> 00:08:04.390 align:start position:0%
That's why they sometimes say it's
harder<00:08:02.879><c> to</c><00:08:02.960><c> go</c><00:08:03.039><c> to</c><00:08:03.199><c> school</c><00:08:03.440><c> if</c><00:08:03.599><c> you're</c><00:08:03.919><c> older</c>

00:08:04.390 --> 00:08:04.400 align:start position:0%
harder to go to school if you're older
 

00:08:04.400 --> 00:08:06.629 align:start position:0%
harder to go to school if you're older
and<00:08:04.720><c> that's</c><00:08:04.960><c> why</c><00:08:05.120><c> we</c><00:08:05.360><c> kind</c><00:08:05.520><c> of</c><00:08:05.759><c> want</c><00:08:06.080><c> the</c><00:08:06.319><c> kids</c>

00:08:06.629 --> 00:08:06.639 align:start position:0%
and that's why we kind of want the kids
 

00:08:06.639 --> 00:08:08.230 align:start position:0%
and that's why we kind of want the kids
to<00:08:06.800><c> be</c><00:08:07.120><c> learning</c><00:08:07.440><c> a</c><00:08:07.599><c> bunch</c><00:08:07.680><c> of</c><00:08:07.840><c> new</c><00:08:08.000><c> stuff</c>

00:08:08.230 --> 00:08:08.240 align:start position:0%
to be learning a bunch of new stuff
 

00:08:08.240 --> 00:08:09.589 align:start position:0%
to be learning a bunch of new stuff
because<00:08:08.400><c> that's</c><00:08:08.639><c> kind</c><00:08:08.720><c> of</c><00:08:08.800><c> the</c><00:08:08.960><c> time</c><00:08:09.440><c> where</c>

00:08:09.589 --> 00:08:09.599 align:start position:0%
because that's kind of the time where
 

00:08:09.599 --> 00:08:11.189 align:start position:0%
because that's kind of the time where
they<00:08:09.759><c> really</c><00:08:09.919><c> show</c><00:08:10.080><c> that</c><00:08:10.319><c> kind</c><00:08:10.400><c> of</c><00:08:10.560><c> a</c><00:08:10.720><c> level</c><00:08:10.879><c> of</c>

00:08:11.189 --> 00:08:11.199 align:start position:0%
they really show that kind of a level of
 

00:08:11.199 --> 00:08:13.029 align:start position:0%
they really show that kind of a level of
fluid<00:08:11.599><c> intelligence.</c><00:08:12.319><c> Now</c><00:08:12.639><c> if</c><00:08:12.800><c> you</c><00:08:12.879><c> think</c>

00:08:13.029 --> 00:08:13.039 align:start position:0%
fluid intelligence. Now if you think
 

00:08:13.039 --> 00:08:15.029 align:start position:0%
fluid intelligence. Now if you think
about<00:08:13.120><c> it,</c><00:08:13.520><c> do</c><00:08:13.759><c> large</c><00:08:14.160><c> language</c><00:08:14.560><c> models</c><00:08:14.879><c> do</c>

00:08:15.029 --> 00:08:15.039 align:start position:0%
about it, do large language models do
 

00:08:15.039 --> 00:08:17.189 align:start position:0%
about it, do large language models do
they<00:08:15.360><c> do</c><00:08:15.520><c> they</c><00:08:15.680><c> have</c><00:08:16.000><c> fluid</c><00:08:16.400><c> intelligence</c><00:08:16.800><c> or</c>

00:08:17.189 --> 00:08:17.199 align:start position:0%
they do they have fluid intelligence or
 

00:08:17.199 --> 00:08:19.110 align:start position:0%
they do they have fluid intelligence or
do<00:08:17.360><c> they</c><00:08:17.520><c> have</c><00:08:17.680><c> crystallized</c><00:08:18.240><c> intelligence?</c>

00:08:19.110 --> 00:08:19.120 align:start position:0%
do they have crystallized intelligence?
 

00:08:19.120 --> 00:08:21.749 align:start position:0%
do they have crystallized intelligence?
They<00:08:19.440><c> have</c><00:08:20.319><c> very</c><00:08:20.800><c> little</c><00:08:21.280><c> fluid</c>

00:08:21.749 --> 00:08:21.759 align:start position:0%
They have very little fluid
 

00:08:21.759 --> 00:08:23.909 align:start position:0%
They have very little fluid
intelligence.<00:08:22.639><c> They</c><00:08:22.879><c> have</c><00:08:23.039><c> tons</c><00:08:23.440><c> of</c>

00:08:23.909 --> 00:08:23.919 align:start position:0%
intelligence. They have tons of
 

00:08:23.919 --> 00:08:25.350 align:start position:0%
intelligence. They have tons of
crystallized<00:08:24.560><c> intelligence,</c><00:08:25.039><c> right?</c><00:08:25.199><c> They</c>

00:08:25.350 --> 00:08:25.360 align:start position:0%
crystallized intelligence, right? They
 

00:08:25.360 --> 00:08:28.390 align:start position:0%
crystallized intelligence, right? They
can<00:08:25.520><c> draw</c><00:08:25.680><c> on</c><00:08:25.919><c> their</c><00:08:26.319><c> vocabulary,</c><00:08:27.199><c> their</c><00:08:27.599><c> vast</c>

00:08:28.390 --> 00:08:28.400 align:start position:0%
can draw on their vocabulary, their vast
 

00:08:28.400 --> 00:08:31.350 align:start position:0%
can draw on their vocabulary, their vast
knowledge<00:08:28.800><c> base,</c><00:08:29.360><c> but</c><00:08:29.599><c> the</c><00:08:29.919><c> idea</c><00:08:30.319><c> of</c><00:08:30.879><c> solving</c>

00:08:31.350 --> 00:08:31.360 align:start position:0%
knowledge base, but the idea of solving
 

00:08:31.360 --> 00:08:33.350 align:start position:0%
knowledge base, but the idea of solving
problems<00:08:32.080><c> that</c><00:08:32.320><c> are</c><00:08:32.479><c> kind</c><00:08:32.560><c> of</c><00:08:32.719><c> in</c><00:08:32.959><c> a</c><00:08:33.120><c> new</c>

00:08:33.350 --> 00:08:33.360 align:start position:0%
problems that are kind of in a new
 

00:08:33.360 --> 00:08:35.269 align:start position:0%
problems that are kind of in a new
environment,<00:08:34.240><c> that</c><00:08:34.399><c> idea</c><00:08:34.640><c> of</c><00:08:34.800><c> kind</c><00:08:34.959><c> of</c><00:08:35.039><c> coming</c>

00:08:35.269 --> 00:08:35.279 align:start position:0%
environment, that idea of kind of coming
 

00:08:35.279 --> 00:08:37.909 align:start position:0%
environment, that idea of kind of coming
up<00:08:35.360><c> with</c><00:08:35.519><c> innovative</c><00:08:36.080><c> solutions,</c><00:08:36.719><c> LMS</c><00:08:37.360><c> are</c>

00:08:37.909 --> 00:08:37.919 align:start position:0%
up with innovative solutions, LMS are
 

00:08:37.919 --> 00:08:40.389 align:start position:0%
up with innovative solutions, LMS are
terrible<00:08:38.320><c> at</c><00:08:38.560><c> that.</c><00:08:39.039><c> So</c><00:08:39.279><c> that's</c><00:08:39.599><c> why</c><00:08:39.919><c> when</c>

00:08:40.389 --> 00:08:40.399 align:start position:0%
terrible at that. So that's why when
 

00:08:40.399 --> 00:08:42.149 align:start position:0%
terrible at that. So that's why when
Greg<00:08:40.880><c> Comrade,</c><00:08:41.440><c> so</c><00:08:41.519><c> he's</c><00:08:41.680><c> the</c><00:08:41.839><c> president</c><00:08:42.000><c> of</c>

00:08:42.149 --> 00:08:42.159 align:start position:0%
Greg Comrade, so he's the president of
 

00:08:42.159 --> 00:08:45.190 align:start position:0%
Greg Comrade, so he's the president of
art<00:08:42.399><c> prize,</c><00:08:42.880><c> founder</c><00:08:43.120><c> of</c><00:08:43.360><c> leverage.to</c>

00:08:45.190 --> 00:08:45.200 align:start position:0%
art prize, founder of leverage.to
 

00:08:45.200 --> 00:08:48.550 align:start position:0%
art prize, founder of leverage.to
when<00:08:45.519><c> he</c><00:08:46.160><c> is</c><00:08:46.640><c> saying</c><00:08:46.800><c> that</c><00:08:47.120><c> Grock</c><00:08:47.600><c> 4</c><00:08:48.080><c> is</c>

00:08:48.550 --> 00:08:48.560 align:start position:0%
when he is saying that Grock 4 is
 

00:08:48.560 --> 00:08:51.829 align:start position:0%
when he is saying that Grock 4 is
showing<00:08:49.360><c> nonzero</c><00:08:50.640><c> levels</c><00:08:51.040><c> of</c><00:08:51.440><c> fluid</c>

00:08:51.829 --> 00:08:51.839 align:start position:0%
showing nonzero levels of fluid
 

00:08:51.839 --> 00:08:54.310 align:start position:0%
showing nonzero levels of fluid
intelligence<00:08:52.399><c> as</c><00:08:52.560><c> in</c><00:08:52.800><c> it</c><00:08:53.040><c> has</c><00:08:53.440><c> some</c><00:08:53.920><c> fluid</c>

00:08:54.310 --> 00:08:54.320 align:start position:0%
intelligence as in it has some fluid
 

00:08:54.320 --> 00:08:56.470 align:start position:0%
intelligence as in it has some fluid
intelligence.<00:08:55.279><c> That</c><00:08:55.440><c> kind</c><00:08:55.600><c> of</c><00:08:55.839><c> rings</c><00:08:56.080><c> a</c><00:08:56.240><c> bell</c>

00:08:56.470 --> 00:08:56.480 align:start position:0%
intelligence. That kind of rings a bell
 

00:08:56.480 --> 00:08:58.150 align:start position:0%
intelligence. That kind of rings a bell
for<00:08:56.640><c> me.</c><00:08:56.880><c> What's</c><00:08:57.120><c> the</c><00:08:57.200><c> expression?</c><00:08:57.680><c> Red</c><00:08:57.920><c> flag</c>

00:08:58.150 --> 00:08:58.160 align:start position:0%
for me. What's the expression? Red flag
 

00:08:58.160 --> 00:09:00.310 align:start position:0%
for me. What's the expression? Red flag
rings<00:08:58.399><c> a</c><00:08:58.560><c> bell.</c><00:08:59.040><c> My</c><00:08:59.279><c> ears</c><00:08:59.519><c> perked</c><00:08:59.920><c> up.</c><00:09:00.160><c> That's</c>

00:09:00.310 --> 00:09:00.320 align:start position:0%
rings a bell. My ears perked up. That's
 

00:09:00.320 --> 00:09:02.389 align:start position:0%
rings a bell. My ears perked up. That's
what<00:09:00.480><c> it</c><00:09:00.640><c> was.</c><00:09:00.880><c> My</c><00:09:01.040><c> ears</c><00:09:01.279><c> perked</c><00:09:01.600><c> up.</c><00:09:01.920><c> And</c><00:09:02.160><c> the</c>

00:09:02.389 --> 00:09:02.399 align:start position:0%
what it was. My ears perked up. And the
 

00:09:02.399 --> 00:09:04.230 align:start position:0%
what it was. My ears perked up. And the
reason<00:09:02.560><c> he's</c><00:09:02.880><c> saying</c><00:09:03.040><c> that</c><00:09:03.279><c> is</c><00:09:03.519><c> this.</c><00:09:03.839><c> If</c><00:09:04.080><c> you</c>

00:09:04.230 --> 00:09:04.240 align:start position:0%
reason he's saying that is this. If you
 

00:09:04.240 --> 00:09:07.110 align:start position:0%
reason he's saying that is this. If you
take<00:09:04.320><c> a</c><00:09:04.560><c> look</c><00:09:04.640><c> at</c><00:09:04.800><c> this</c><00:09:05.120><c> chart,</c><00:09:06.000><c> there's</c><00:09:06.640><c> one</c>

00:09:07.110 --> 00:09:07.120 align:start position:0%
take a look at this chart, there's one
 

00:09:07.120 --> 00:09:09.990 align:start position:0%
take a look at this chart, there's one
data<00:09:07.440><c> point</c><00:09:07.760><c> on</c><00:09:08.080><c> here</c><00:09:08.399><c> that's</c><00:09:08.959><c> not</c><00:09:09.279><c> like</c><00:09:09.680><c> the</c>

00:09:09.990 --> 00:09:10.000 align:start position:0%
data point on here that's not like the
 

00:09:10.000 --> 00:09:12.310 align:start position:0%
data point on here that's not like the
others.<00:09:10.880><c> So,</c><00:09:11.200><c> here</c><00:09:11.360><c> we</c><00:09:11.600><c> have</c><00:09:11.680><c> kind</c><00:09:11.839><c> of</c><00:09:11.920><c> the</c>

00:09:12.310 --> 00:09:12.320 align:start position:0%
others. So, here we have kind of the
 

00:09:12.320 --> 00:09:14.949 align:start position:0%
others. So, here we have kind of the
cost<00:09:12.720><c> per</c><00:09:13.120><c> task.</c><00:09:13.680><c> So,</c><00:09:13.920><c> kind</c><00:09:14.080><c> of</c><00:09:14.160><c> per</c><00:09:14.560><c> problem</c>

00:09:14.949 --> 00:09:14.959 align:start position:0%
cost per task. So, kind of per problem
 

00:09:14.959 --> 00:09:18.310 align:start position:0%
cost per task. So, kind of per problem
on<00:09:15.279><c> the</c><00:09:15.600><c> ARC</c><00:09:15.920><c> AGI</c><00:09:16.399><c> benchmark,</c><00:09:16.959><c> like</c><00:09:17.200><c> how</c><00:09:17.839><c> much</c>

00:09:18.310 --> 00:09:18.320 align:start position:0%
on the ARC AGI benchmark, like how much
 

00:09:18.320 --> 00:09:20.710 align:start position:0%
on the ARC AGI benchmark, like how much
in<00:09:18.640><c> compute</c><00:09:19.120><c> API</c><00:09:19.600><c> credits,</c><00:09:20.080><c> how</c><00:09:20.240><c> much</c><00:09:20.399><c> does</c><00:09:20.560><c> it</c>

00:09:20.710 --> 00:09:20.720 align:start position:0%
in compute API credits, how much does it
 

00:09:20.720 --> 00:09:24.070 align:start position:0%
in compute API credits, how much does it
cost<00:09:21.120><c> for</c><00:09:21.440><c> one</c><00:09:21.680><c> of</c><00:09:21.760><c> these</c><00:09:22.160><c> models</c><00:09:23.040><c> to</c><00:09:23.600><c> solve</c>

00:09:24.070 --> 00:09:24.080 align:start position:0%
cost for one of these models to solve
 

00:09:24.080 --> 00:09:25.829 align:start position:0%
cost for one of these models to solve
one<00:09:24.320><c> of</c><00:09:24.399><c> those</c><00:09:24.640><c> tasks.</c><00:09:25.279><c> Now,</c><00:09:25.519><c> the</c><00:09:25.680><c> reason</c>

00:09:25.829 --> 00:09:25.839 align:start position:0%
one of those tasks. Now, the reason
 

00:09:25.839 --> 00:09:27.910 align:start position:0%
one of those tasks. Now, the reason
we're<00:09:26.080><c> tracking</c><00:09:26.320><c> that</c><00:09:26.560><c> is</c><00:09:26.800><c> because</c><00:09:27.360><c> on</c><00:09:27.680><c> the</c>

00:09:27.910 --> 00:09:27.920 align:start position:0%
we're tracking that is because on the
 

00:09:27.920 --> 00:09:30.630 align:start position:0%
we're tracking that is because on the
original<00:09:28.320><c> ARC</c><00:09:28.720><c> AGI,</c><00:09:29.519><c> on</c><00:09:29.760><c> the</c><00:09:29.920><c> original</c><00:09:30.240><c> one,</c><00:09:30.480><c> I</c>

00:09:30.630 --> 00:09:30.640 align:start position:0%
original ARC AGI, on the original one, I
 

00:09:30.640 --> 00:09:33.509 align:start position:0%
original ARC AGI, on the original one, I
think<00:09:30.720><c> it</c><00:09:30.959><c> was</c><00:09:31.040><c> the</c><00:09:31.440><c> 03</c><00:09:32.080><c> preview</c><00:09:32.640><c> or</c><00:09:32.800><c> the</c><00:09:32.959><c> 01,</c><00:09:33.360><c> I</c>

00:09:33.509 --> 00:09:33.519 align:start position:0%
think it was the 03 preview or the 01, I
 

00:09:33.519 --> 00:09:35.590 align:start position:0%
think it was the 03 preview or the 01, I
don't<00:09:33.600><c> recall,</c><00:09:33.839><c> but</c><00:09:34.160><c> one</c><00:09:34.320><c> of</c><00:09:34.399><c> the</c><00:09:34.560><c> OpenAI</c>

00:09:35.590 --> 00:09:35.600 align:start position:0%
don't recall, but one of the OpenAI
 

00:09:35.600 --> 00:09:38.710 align:start position:0%
don't recall, but one of the OpenAI
reasoning<00:09:36.080><c> models</c><00:09:36.800><c> was</c><00:09:37.279><c> able</c><00:09:37.760><c> to</c><00:09:38.240><c> crack</c><00:09:38.480><c> it.</c>

00:09:38.710 --> 00:09:38.720 align:start position:0%
reasoning models was able to crack it.
 

00:09:38.720 --> 00:09:41.350 align:start position:0%
reasoning models was able to crack it.
It<00:09:38.959><c> was</c><00:09:39.040><c> able</c><00:09:39.279><c> to</c><00:09:39.519><c> beat</c><00:09:39.920><c> the</c><00:09:40.160><c> human</c><00:09:40.480><c> baseline,</c>

00:09:41.350 --> 00:09:41.360 align:start position:0%
It was able to beat the human baseline,
 

00:09:41.360 --> 00:09:44.310 align:start position:0%
It was able to beat the human baseline,
thereby<00:09:42.080><c> sort</c><00:09:42.320><c> of</c><00:09:43.040><c> breaking</c><00:09:43.519><c> that</c><00:09:43.839><c> benchmark</c>

00:09:44.310 --> 00:09:44.320 align:start position:0%
thereby sort of breaking that benchmark
 

00:09:44.320 --> 00:09:45.670 align:start position:0%
thereby sort of breaking that benchmark
because<00:09:44.480><c> that</c><00:09:44.720><c> was</c><00:09:44.800><c> the</c><00:09:45.040><c> original</c><00:09:45.360><c> thing</c><00:09:45.519><c> that</c>

00:09:45.670 --> 00:09:45.680 align:start position:0%
because that was the original thing that
 

00:09:45.680 --> 00:09:47.750 align:start position:0%
because that was the original thing that
they<00:09:45.920><c> said</c><00:09:46.399><c> would</c><00:09:46.720><c> not</c><00:09:46.880><c> be</c><00:09:47.040><c> accomplished</c><00:09:47.519><c> for</c>

00:09:47.750 --> 00:09:47.760 align:start position:0%
they said would not be accomplished for
 

00:09:47.760 --> 00:09:49.670 align:start position:0%
they said would not be accomplished for
a<00:09:47.920><c> while,</c><00:09:48.080><c> but</c><00:09:48.320><c> it</c><00:09:48.560><c> was</c><00:09:48.880><c> there</c><00:09:49.120><c> was</c><00:09:49.279><c> only</c><00:09:49.440><c> one</c>

00:09:49.670 --> 00:09:49.680 align:start position:0%
a while, but it was there was only one
 

00:09:49.680 --> 00:09:53.590 align:start position:0%
a while, but it was there was only one
catch.<00:09:50.560><c> The</c><00:09:50.880><c> price</c><00:09:51.440><c> per</c><00:09:51.839><c> task</c><00:09:52.399><c> was</c><00:09:52.959><c> very,</c><00:09:53.279><c> very</c>

00:09:53.590 --> 00:09:53.600 align:start position:0%
catch. The price per task was very, very
 

00:09:53.600 --> 00:09:55.750 align:start position:0%
catch. The price per task was very, very
high.<00:09:54.000><c> I</c><00:09:54.240><c> think</c><00:09:54.320><c> I</c><00:09:54.560><c> did</c><00:09:54.800><c> kind</c><00:09:55.040><c> of</c><00:09:55.279><c> some</c>

00:09:55.750 --> 00:09:55.760 align:start position:0%
high. I think I did kind of some
 

00:09:55.760 --> 00:09:57.269 align:start position:0%
high. I think I did kind of some
estimations.<00:09:56.560><c> I</c><00:09:56.720><c> think</c><00:09:56.800><c> it</c><00:09:56.959><c> was</c><00:09:57.040><c> something</c>

00:09:57.269 --> 00:09:57.279 align:start position:0%
estimations. I think it was something
 

00:09:57.279 --> 00:10:00.470 align:start position:0%
estimations. I think it was something
like<00:09:57.680><c> 300,000</c><00:09:58.959><c> to</c><00:09:59.279><c> take</c><00:09:59.440><c> that</c><00:09:59.760><c> test</c><00:10:00.080><c> with</c><00:10:00.320><c> that</c>

00:10:00.470 --> 00:10:00.480 align:start position:0%
like 300,000 to take that test with that
 

00:10:00.480 --> 00:10:01.829 align:start position:0%
like 300,000 to take that test with that
model.<00:10:00.800><c> That</c><00:10:00.959><c> that</c><00:10:01.200><c> would</c><00:10:01.360><c> be</c><00:10:01.440><c> kind</c><00:10:01.519><c> of</c><00:10:01.680><c> like</c>

00:10:01.829 --> 00:10:01.839 align:start position:0%
model. That that would be kind of like
 

00:10:01.839 --> 00:10:03.750 align:start position:0%
model. That that would be kind of like
if<00:10:02.160><c> you</c><00:10:02.320><c> and</c><00:10:02.480><c> I</c><00:10:02.560><c> were</c><00:10:02.720><c> paying</c><00:10:02.880><c> for</c><00:10:03.120><c> those</c><00:10:03.360><c> API</c>

00:10:03.750 --> 00:10:03.760 align:start position:0%
if you and I were paying for those API
 

00:10:03.760 --> 00:10:05.590 align:start position:0%
if you and I were paying for those API
costs<00:10:04.080><c> that</c><00:10:04.320><c> how</c><00:10:04.480><c> much</c><00:10:04.560><c> it</c><00:10:04.720><c> would</c><00:10:04.880><c> cost</c><00:10:05.120><c> us.</c>

00:10:05.590 --> 00:10:05.600 align:start position:0%
costs that how much it would cost us.
 

00:10:05.600 --> 00:10:07.030 align:start position:0%
costs that how much it would cost us.
But<00:10:05.760><c> either</c><00:10:05.920><c> way,</c><00:10:06.080><c> it</c><00:10:06.240><c> was</c><00:10:06.399><c> like</c><00:10:06.560><c> a</c><00:10:06.800><c> lot</c><00:10:06.959><c> of</c>

00:10:07.030 --> 00:10:07.040 align:start position:0%
But either way, it was like a lot of
 

00:10:07.040 --> 00:10:09.030 align:start position:0%
But either way, it was like a lot of
money.<00:10:07.440><c> Notice</c><00:10:07.760><c> this</c><00:10:07.920><c> is</c><00:10:08.240><c> logarithmic</c><00:10:08.880><c> as</c>

00:10:09.030 --> 00:10:09.040 align:start position:0%
money. Notice this is logarithmic as
 

00:10:09.040 --> 00:10:12.230 align:start position:0%
money. Notice this is logarithmic as
well.<00:10:09.200><c> So</c><00:10:09.440><c> from</c><00:10:09.760><c> 1</c><00:10:10.000><c> to</c><00:10:10.160><c> 10</c><00:10:10.399><c> to</c><00:10:10.640><c> 100</c><00:10:10.959><c> to</c><00:10:11.360><c> 1,000,</c>

00:10:12.230 --> 00:10:12.240 align:start position:0%
well. So from 1 to 10 to 100 to 1,000,
 

00:10:12.240 --> 00:10:13.750 align:start position:0%
well. So from 1 to 10 to 100 to 1,000,
right?<00:10:12.480><c> So</c><00:10:12.640><c> these</c><00:10:12.800><c> are</c><00:10:12.959><c> not</c><00:10:13.120><c> the</c><00:10:13.279><c> the</c><00:10:13.600><c> same</c>

00:10:13.750 --> 00:10:13.760 align:start position:0%
right? So these are not the the same
 

00:10:13.760 --> 00:10:15.910 align:start position:0%
right? So these are not the the same
increments.<00:10:14.480><c> So</c><00:10:14.640><c> the</c><00:10:14.880><c> cost</c><00:10:15.360><c> rapidly</c>

00:10:15.910 --> 00:10:15.920 align:start position:0%
increments. So the cost rapidly
 

00:10:15.920 --> 00:10:17.829 align:start position:0%
increments. So the cost rapidly
increases<00:10:16.399><c> as</c><00:10:16.640><c> we</c><00:10:16.800><c> go</c><00:10:16.880><c> in</c><00:10:17.120><c> this</c><00:10:17.279><c> direction.</c>

00:10:17.829 --> 00:10:17.839 align:start position:0%
increases as we go in this direction.
 

00:10:17.839 --> 00:10:20.630 align:start position:0%
increases as we go in this direction.
And<00:10:18.160><c> on</c><00:10:18.399><c> the</c><00:10:18.640><c> y-axis</c><00:10:19.360><c> we</c><00:10:19.600><c> have</c><00:10:19.680><c> the</c><00:10:20.000><c> score,</c><00:10:20.399><c> how</c>

00:10:20.630 --> 00:10:20.640 align:start position:0%
And on the y-axis we have the score, how
 

00:10:20.640 --> 00:10:22.470 align:start position:0%
And on the y-axis we have the score, how
well<00:10:20.800><c> it</c><00:10:21.040><c> did.</c><00:10:21.440><c> And</c><00:10:21.600><c> as</c><00:10:21.760><c> you</c><00:10:21.920><c> can</c><00:10:22.079><c> see,</c><00:10:22.320><c> all</c>

00:10:22.470 --> 00:10:22.480 align:start position:0%
well it did. And as you can see, all
 

00:10:22.480 --> 00:10:25.590 align:start position:0%
well it did. And as you can see, all
these<00:10:22.720><c> models,</c><00:10:23.120><c> they're</c><00:10:23.600><c> kind</c><00:10:23.839><c> of</c><00:10:24.399><c> clustered</c>

00:10:25.590 --> 00:10:25.600 align:start position:0%
these models, they're kind of clustered
 

00:10:25.600 --> 00:10:28.310 align:start position:0%
these models, they're kind of clustered
here<00:10:26.079><c> somewhere,</c><00:10:26.800><c> right?</c><00:10:27.200><c> So</c><00:10:27.600><c> this</c><00:10:27.839><c> is</c><00:10:27.920><c> like</c><00:10:28.079><c> a</c>

00:10:28.310 --> 00:10:28.320 align:start position:0%
here somewhere, right? So this is like a
 

00:10:28.320 --> 00:10:31.030 align:start position:0%
here somewhere, right? So this is like a
6%<00:10:28.880><c> accuracy,</c><00:10:29.360><c> 4%</c><00:10:29.839><c> accuracy,</c><00:10:30.399><c> right?</c><00:10:30.800><c> The</c>

00:10:31.030 --> 00:10:31.040 align:start position:0%
6% accuracy, 4% accuracy, right? The
 

00:10:31.040 --> 00:10:33.269 align:start position:0%
6% accuracy, 4% accuracy, right? The
highest<00:10:31.360><c> one</c><00:10:31.760><c> amongst</c><00:10:32.160><c> these,</c><00:10:32.640><c> just</c><00:10:32.880><c> over,</c>

00:10:33.269 --> 00:10:33.279 align:start position:0%
highest one amongst these, just over,
 

00:10:33.279 --> 00:10:35.430 align:start position:0%
highest one amongst these, just over,
let's<00:10:33.680><c> call</c><00:10:33.839><c> it</c><00:10:34.079><c> just</c><00:10:34.240><c> over</c><00:10:34.399><c> 8%</c><00:10:34.880><c> accuracy</c><00:10:35.279><c> is</c>

00:10:35.430 --> 00:10:35.440 align:start position:0%
let's call it just over 8% accuracy is
 

00:10:35.440 --> 00:10:38.550 align:start position:0%
let's call it just over 8% accuracy is
Claude<00:10:36.000><c> Opus</c><00:10:36.480><c> 4.</c><00:10:37.040><c> And</c><00:10:37.200><c> then</c><00:10:37.360><c> there's</c><00:10:38.079><c> one</c><00:10:38.320><c> that</c>

00:10:38.550 --> 00:10:38.560 align:start position:0%
Claude Opus 4. And then there's one that
 

00:10:38.560 --> 00:10:41.269 align:start position:0%
Claude Opus 4. And then there's one that
really<00:10:38.880><c> stands</c><00:10:39.200><c> alone,</c><00:10:39.600><c> and</c><00:10:39.839><c> that's</c><00:10:40.399><c> Gro</c><00:10:40.880><c> 4,</c>

00:10:41.269 --> 00:10:41.279 align:start position:0%
really stands alone, and that's Gro 4,
 

00:10:41.279 --> 00:10:42.949 align:start position:0%
really stands alone, and that's Gro 4,
the<00:10:41.519><c> thinking</c><00:10:41.839><c> model,</c><00:10:42.240><c> right?</c><00:10:42.480><c> Notice</c><00:10:42.800><c> that</c>

00:10:42.949 --> 00:10:42.959 align:start position:0%
the thinking model, right? Notice that
 

00:10:42.959 --> 00:10:44.949 align:start position:0%
the thinking model, right? Notice that
it's<00:10:43.200><c> sort</c><00:10:43.360><c> of</c><00:10:43.600><c> in</c><00:10:43.920><c> line</c><00:10:44.160><c> with</c><00:10:44.399><c> the</c><00:10:44.560><c> cost</c><00:10:44.800><c> of</c>

00:10:44.949 --> 00:10:44.959 align:start position:0%
it's sort of in line with the cost of
 

00:10:44.959 --> 00:10:47.350 align:start position:0%
it's sort of in line with the cost of
most<00:10:45.200><c> of</c><00:10:45.279><c> these,</c><00:10:45.600><c> but</c><00:10:46.160><c> way</c><00:10:46.480><c> better,</c><00:10:47.040><c> sitting</c>

00:10:47.350 --> 00:10:47.360 align:start position:0%
most of these, but way better, sitting
 

00:10:47.360 --> 00:10:50.310 align:start position:0%
most of these, but way better, sitting
just<00:10:47.600><c> above</c><00:10:48.160><c> 16%</c><00:10:49.200><c> score</c><00:10:49.600><c> accuracy.</c><00:10:50.160><c> Now</c>

00:10:50.310 --> 00:10:50.320 align:start position:0%
just above 16% score accuracy. Now
 

00:10:50.320 --> 00:10:52.310 align:start position:0%
just above 16% score accuracy. Now
what's<00:10:50.640><c> the</c><00:10:50.800><c> big</c><00:10:50.959><c> deal</c><00:10:51.040><c> with</c><00:10:51.360><c> ARC</c><00:10:51.680><c> AGI?</c><00:10:52.160><c> Why</c>

00:10:52.310 --> 00:10:52.320 align:start position:0%
what's the big deal with ARC AGI? Why
 

00:10:52.320 --> 00:10:53.910 align:start position:0%
what's the big deal with ARC AGI? Why
are<00:10:52.399><c> we</c><00:10:52.480><c> talking</c><00:10:52.640><c> about</c><00:10:52.800><c> ARC</c><00:10:53.040><c> AGI?</c><00:10:53.519><c> How</c><00:10:53.680><c> is</c><00:10:53.760><c> it</c>

00:10:53.910 --> 00:10:53.920 align:start position:0%
are we talking about ARC AGI? How is it
 

00:10:53.920 --> 00:10:56.069 align:start position:0%
are we talking about ARC AGI? How is it
different<00:10:54.320><c> from</c><00:10:55.040><c> the</c><00:10:55.279><c> other</c><00:10:55.440><c> benchmarks?</c>

00:10:56.069 --> 00:10:56.079 align:start position:0%
different from the other benchmarks?
 

00:10:56.079 --> 00:10:58.230 align:start position:0%
different from the other benchmarks?
Well,<00:10:56.240><c> it</c><00:10:56.399><c> was</c><00:10:56.560><c> created</c><00:10:56.800><c> by</c><00:10:57.040><c> Francois</c><00:10:57.680><c> Cholay.</c>

00:10:58.230 --> 00:10:58.240 align:start position:0%
Well, it was created by Francois Cholay.
 

00:10:58.240 --> 00:11:00.470 align:start position:0%
Well, it was created by Francois Cholay.
He<00:10:58.560><c> wrote</c><00:10:58.800><c> the</c><00:10:59.040><c> book</c><00:10:59.200><c> on</c><00:10:59.519><c> intelligence</c>

00:11:00.470 --> 00:11:00.480 align:start position:0%
He wrote the book on intelligence
 

00:11:00.480 --> 00:11:02.790 align:start position:0%
He wrote the book on intelligence
literally<00:11:01.040><c> on</c><00:11:01.360><c> a</c><00:11:01.519><c> measure</c><00:11:01.839><c> of</c><00:11:02.000><c> intelligence.</c>

00:11:02.790 --> 00:11:02.800 align:start position:0%
literally on a measure of intelligence.
 

00:11:02.800 --> 00:11:04.389 align:start position:0%
literally on a measure of intelligence.
And<00:11:03.040><c> he's</c><00:11:03.279><c> saying</c><00:11:03.440><c> that</c><00:11:03.760><c> intelligence</c><00:11:04.240><c> is</c>

00:11:04.389 --> 00:11:04.399 align:start position:0%
And he's saying that intelligence is
 

00:11:04.399 --> 00:11:06.870 align:start position:0%
And he's saying that intelligence is
measured<00:11:04.720><c> by</c><00:11:04.880><c> the</c><00:11:05.120><c> efficiency</c><00:11:05.839><c> of</c><00:11:06.480><c> skill</c>

00:11:06.870 --> 00:11:06.880 align:start position:0%
measured by the efficiency of skill
 

00:11:06.880 --> 00:11:10.230 align:start position:0%
measured by the efficiency of skill
acquisition<00:11:07.680><c> on</c><00:11:08.240><c> unknown</c><00:11:08.959><c> tasks.</c><00:11:09.600><c> Simply</c><00:11:10.000><c> how</c>

00:11:10.230 --> 00:11:10.240 align:start position:0%
acquisition on unknown tasks. Simply how
 

00:11:10.240 --> 00:11:12.790 align:start position:0%
acquisition on unknown tasks. Simply how
quickly<00:11:10.560><c> can</c><00:11:10.720><c> you</c><00:11:10.959><c> learn</c><00:11:11.360><c> new</c><00:11:11.600><c> skills.</c><00:11:12.240><c> So</c><00:11:12.480><c> the</c>

00:11:12.790 --> 00:11:12.800 align:start position:0%
quickly can you learn new skills. So the
 

00:11:12.800 --> 00:11:15.829 align:start position:0%
quickly can you learn new skills. So the
basic<00:11:13.120><c> idea</c><00:11:13.600><c> for</c><00:11:13.920><c> AGI</c><00:11:14.560><c> is</c><00:11:14.800><c> that</c><00:11:15.279><c> you</c><00:11:15.440><c> know</c>

00:11:15.829 --> 00:11:15.839 align:start position:0%
basic idea for AGI is that you know
 

00:11:15.839 --> 00:11:18.790 align:start position:0%
basic idea for AGI is that you know
these<00:11:16.240><c> models</c><00:11:17.120><c> can</c><00:11:17.440><c> have</c><00:11:17.600><c> some</c><00:11:17.920><c> skills.</c><00:11:18.480><c> They</c>

00:11:18.790 --> 00:11:18.800 align:start position:0%
these models can have some skills. They
 

00:11:18.800 --> 00:11:21.190 align:start position:0%
these models can have some skills. They
can<00:11:18.959><c> accomplish</c><00:11:19.839><c> certain</c><00:11:20.240><c> things</c><00:11:20.880><c> but</c>

00:11:21.190 --> 00:11:21.200 align:start position:0%
can accomplish certain things but
 

00:11:21.200 --> 00:11:23.430 align:start position:0%
can accomplish certain things but
measuring<00:11:21.519><c> a</c><00:11:21.760><c> task</c><00:11:22.160><c> specific</c><00:11:22.640><c> skill</c><00:11:22.959><c> is</c><00:11:23.120><c> not</c><00:11:23.279><c> a</c>

00:11:23.430 --> 00:11:23.440 align:start position:0%
measuring a task specific skill is not a
 

00:11:23.440 --> 00:11:26.230 align:start position:0%
measuring a task specific skill is not a
good<00:11:23.760><c> proxy</c><00:11:24.399><c> for</c><00:11:24.880><c> intelligence.</c><00:11:25.760><c> Right?</c><00:11:26.079><c> Most</c>

00:11:26.230 --> 00:11:26.240 align:start position:0%
good proxy for intelligence. Right? Most
 

00:11:26.240 --> 00:11:27.990 align:start position:0%
good proxy for intelligence. Right? Most
of these<00:11:26.399><c> benchmarks</c><00:11:26.959><c> they</c><00:11:27.200><c> measure</c><00:11:27.680><c> the</c>

00:11:27.990 --> 00:11:28.000 align:start position:0%
of these benchmarks they measure the
 

00:11:28.000 --> 00:11:30.230 align:start position:0%
of these benchmarks they measure the
thing's<00:11:28.320><c> ability</c><00:11:28.880><c> at</c><00:11:29.120><c> a</c><00:11:29.360><c> certain</c><00:11:29.600><c> skill.</c><00:11:30.000><c> Can</c>

00:11:30.230 --> 00:11:30.240 align:start position:0%
thing's ability at a certain skill. Can
 

00:11:30.240 --> 00:11:32.870 align:start position:0%
thing's ability at a certain skill. Can
you<00:11:30.399><c> answer</c><00:11:30.640><c> these</c><00:11:31.360><c> PhD</c><00:11:32.079><c> level</c><00:11:32.399><c> questions?</c>

00:11:32.870 --> 00:11:32.880 align:start position:0%
you answer these PhD level questions?
 

00:11:32.880 --> 00:11:35.750 align:start position:0%
you answer these PhD level questions?
Can<00:11:33.120><c> you</c><00:11:33.279><c> solve</c><00:11:33.680><c> these</c><00:11:34.399><c> types</c><00:11:34.880><c> of</c><00:11:35.360><c> math</c>

00:11:35.750 --> 00:11:35.760 align:start position:0%
Can you solve these types of math
 

00:11:35.760 --> 00:11:38.790 align:start position:0%
Can you solve these types of math
problems?<00:11:36.480><c> Arc</c><00:11:36.800><c> AGI</c><00:11:37.360><c> and</c><00:11:37.600><c> Francois</c><00:11:38.160><c> Chalet,</c>

00:11:38.790 --> 00:11:38.800 align:start position:0%
problems? Arc AGI and Francois Chalet,
 

00:11:38.800 --> 00:11:40.710 align:start position:0%
problems? Arc AGI and Francois Chalet,
they<00:11:39.200><c> kind</c><00:11:39.360><c> of</c><00:11:39.760><c> turned</c><00:11:40.000><c> the</c><00:11:40.160><c> problem</c><00:11:40.320><c> on</c><00:11:40.560><c> its</c>

00:11:40.710 --> 00:11:40.720 align:start position:0%
they kind of turned the problem on its
 

00:11:40.720 --> 00:11:42.470 align:start position:0%
they kind of turned the problem on its
head.<00:11:41.120><c> They</c><00:11:41.360><c> didn't</c><00:11:41.600><c> care</c><00:11:41.760><c> about</c><00:11:42.000><c> specific</c>

00:11:42.470 --> 00:11:42.480 align:start position:0%
head. They didn't care about specific
 

00:11:42.480 --> 00:11:45.670 align:start position:0%
head. They didn't care about specific
skills,<00:11:43.200><c> but</c><00:11:43.760><c> rather</c><00:11:44.320><c> they</c><00:11:44.720><c> wanted</c><00:11:44.959><c> to</c><00:11:45.279><c> try</c><00:11:45.519><c> to</c>

00:11:45.670 --> 00:11:45.680 align:start position:0%
skills, but rather they wanted to try to
 

00:11:45.680 --> 00:11:48.550 align:start position:0%
skills, but rather they wanted to try to
test<00:11:46.160><c> for</c><00:11:46.880><c> something</c><00:11:47.360><c> different,</c><00:11:48.079><c> something</c>

00:11:48.550 --> 00:11:48.560 align:start position:0%
test for something different, something
 

00:11:48.560 --> 00:11:51.269 align:start position:0%
test for something different, something
that<00:11:48.959><c> is</c><00:11:49.200><c> very</c><00:11:49.519><c> very</c><00:11:49.920><c> easy</c><00:11:50.240><c> for</c><00:11:50.480><c> humans</c><00:11:50.959><c> and</c>

00:11:51.269 --> 00:11:51.279 align:start position:0%
that is very very easy for humans and
 

00:11:51.279 --> 00:11:54.389 align:start position:0%
that is very very easy for humans and
that<00:11:51.440><c> is</c><00:11:51.760><c> near</c><00:11:52.079><c> impossible</c><00:11:53.120><c> for</c><00:11:53.600><c> these</c><00:11:54.000><c> large</c>

00:11:54.389 --> 00:11:54.399 align:start position:0%
that is near impossible for these large
 

00:11:54.399 --> 00:11:56.470 align:start position:0%
that is near impossible for these large
language<00:11:54.720><c> models.</c><00:11:55.200><c> So</c><00:11:55.440><c> we</c><00:11:55.680><c> were</c><00:11:55.839><c> interested</c>

00:11:56.470 --> 00:11:56.480 align:start position:0%
language models. So we were interested
 

00:11:56.480 --> 00:11:59.910 align:start position:0%
language models. So we were interested
in<00:11:57.200><c> skill</c><00:11:57.519><c> acquisition</c><00:11:58.240><c> and</c><00:11:58.800><c> generalization</c>

00:11:59.910 --> 00:11:59.920 align:start position:0%
in skill acquisition and generalization
 

00:11:59.920 --> 00:12:02.630 align:start position:0%
in skill acquisition and generalization
rather<00:12:00.480><c> than</c><00:12:00.720><c> the</c><00:12:01.040><c> skill</c><00:12:01.360><c> itself.</c><00:12:02.240><c> So</c><00:12:02.320><c> if</c><00:12:02.480><c> a</c>

00:12:02.630 --> 00:12:02.640 align:start position:0%
rather than the skill itself. So if a
 

00:12:02.640 --> 00:12:05.590 align:start position:0%
rather than the skill itself. So if a
kid<00:12:02.800><c> is</c><00:12:03.120><c> rapidly</c><00:12:03.760><c> able</c><00:12:04.160><c> to</c><00:12:04.800><c> figure</c><00:12:05.040><c> out</c><00:12:05.279><c> how</c><00:12:05.440><c> to</c>

00:12:05.590 --> 00:12:05.600 align:start position:0%
kid is rapidly able to figure out how to
 

00:12:05.600 --> 00:12:07.509 align:start position:0%
kid is rapidly able to figure out how to
add<00:12:05.839><c> and</c><00:12:06.240><c> subtract</c><00:12:06.639><c> and</c><00:12:06.880><c> divide</c><00:12:07.200><c> numbers,</c>

00:12:07.509 --> 00:12:07.519 align:start position:0%
add and subtract and divide numbers,
 

00:12:07.519 --> 00:12:09.269 align:start position:0%
add and subtract and divide numbers,
we'll<00:12:07.680><c> say,</c><00:12:07.760><c> "Wow,</c><00:12:08.160><c> that's</c><00:12:08.399><c> a</c><00:12:08.560><c> smart</c><00:12:08.800><c> kid."</c>

00:12:09.269 --> 00:12:09.279 align:start position:0%
we'll say, "Wow, that's a smart kid."
 

00:12:09.279 --> 00:12:11.350 align:start position:0%
we'll say, "Wow, that's a smart kid."
Even<00:12:09.600><c> though</c><00:12:09.760><c> a</c><00:12:10.000><c> calculator</c><00:12:10.399><c> is</c><00:12:10.639><c> a</c><00:12:10.959><c> million</c>

00:12:11.350 --> 00:12:11.360 align:start position:0%
Even though a calculator is a million
 

00:12:11.360 --> 00:12:13.590 align:start position:0%
Even though a calculator is a million
times<00:12:11.680><c> better,</c><00:12:12.000><c> but</c><00:12:12.240><c> it's</c><00:12:12.399><c> a</c><00:12:12.959><c> static,</c><00:12:13.360><c> it's</c>

00:12:13.590 --> 00:12:13.600 align:start position:0%
times better, but it's a static, it's
 

00:12:13.600 --> 00:12:15.030 align:start position:0%
times better, but it's a static, it's
good<00:12:13.680><c> at</c><00:12:13.920><c> that,</c><00:12:14.160><c> you</c><00:12:14.320><c> know,</c><00:12:14.480><c> quote</c><00:12:14.639><c> unquote</c>

00:12:15.030 --> 00:12:15.040 align:start position:0%
good at that, you know, quote unquote
 

00:12:15.040 --> 00:12:17.030 align:start position:0%
good at that, you know, quote unquote
skill,<00:12:15.519><c> but</c><00:12:15.760><c> it</c><00:12:15.920><c> can't</c><00:12:16.079><c> acquire</c><00:12:16.399><c> new</c><00:12:16.639><c> skills.</c>

00:12:17.030 --> 00:12:17.040 align:start position:0%
skill, but it can't acquire new skills.
 

00:12:17.040 --> 00:12:19.750 align:start position:0%
skill, but it can't acquire new skills.
A<00:12:17.200><c> kid</c><00:12:17.519><c> that</c><00:12:17.920><c> quickly</c><00:12:18.320><c> acquires</c><00:12:18.959><c> skills,</c><00:12:19.519><c> we'd</c>

00:12:19.750 --> 00:12:19.760 align:start position:0%
A kid that quickly acquires skills, we'd
 

00:12:19.760 --> 00:12:22.150 align:start position:0%
A kid that quickly acquires skills, we'd
say<00:12:20.480><c> has</c><00:12:20.560><c> a</c><00:12:20.800><c> high</c><00:12:20.959><c> intelligence.</c><00:12:21.680><c> Or</c><00:12:21.920><c> another</c>

00:12:22.150 --> 00:12:22.160 align:start position:0%
say has a high intelligence. Or another
 

00:12:22.160 --> 00:12:24.629 align:start position:0%
say has a high intelligence. Or another
way<00:12:22.320><c> of</c><00:12:22.399><c> saying</c><00:12:22.639><c> this</c><00:12:22.880><c> is</c><00:12:23.360><c> this</c><00:12:23.600><c> ARC</c><00:12:23.920><c> AGI</c><00:12:24.399><c> prize</c>

00:12:24.629 --> 00:12:24.639 align:start position:0%
way of saying this is this ARC AGI prize
 

00:12:24.639 --> 00:12:27.030 align:start position:0%
way of saying this is this ARC AGI prize
is<00:12:24.959><c> or</c><00:12:25.279><c> it</c><00:12:25.440><c> has</c><00:12:25.600><c> a</c><00:12:25.760><c> prize,</c><00:12:25.920><c> but</c><00:12:26.160><c> the</c><00:12:26.240><c> ARC</c><00:12:26.560><c> AGI</c>

00:12:27.030 --> 00:12:27.040 align:start position:0%
is or it has a prize, but the ARC AGI
 

00:12:27.040 --> 00:12:29.430 align:start position:0%
is or it has a prize, but the ARC AGI
benchmark,<00:12:27.519><c> it's</c><00:12:27.760><c> focused</c><00:12:28.079><c> on</c><00:12:28.800><c> fluid</c>

00:12:29.430 --> 00:12:29.440 align:start position:0%
benchmark, it's focused on fluid
 

00:12:29.440 --> 00:12:31.110 align:start position:0%
benchmark, it's focused on fluid
intelligence,<00:12:30.399><c> right?</c><00:12:30.560><c> The</c><00:12:30.720><c> ability</c><00:12:30.959><c> to</c>

00:12:31.110 --> 00:12:31.120 align:start position:0%
intelligence, right? The ability to
 

00:12:31.120 --> 00:12:33.509 align:start position:0%
intelligence, right? The ability to
reason,<00:12:31.519><c> solve</c><00:12:31.920><c> novel</c><00:12:32.320><c> problems,</c><00:12:32.880><c> adapt</c>

00:12:33.509 --> 00:12:33.519 align:start position:0%
reason, solve novel problems, adapt
 

00:12:33.519 --> 00:12:35.910 align:start position:0%
reason, solve novel problems, adapt
basically<00:12:34.240><c> rather</c><00:12:34.560><c> than</c><00:12:35.120><c> crystallized</c>

00:12:35.910 --> 00:12:35.920 align:start position:0%
basically rather than crystallized
 

00:12:35.920 --> 00:12:39.030 align:start position:0%
basically rather than crystallized
intelligence.<00:12:36.800><c> So</c><00:12:37.200><c> LM</c><00:12:37.839><c> are</c><00:12:38.399><c> good</c><00:12:38.639><c> at</c>

00:12:39.030 --> 00:12:39.040 align:start position:0%
intelligence. So LM are good at
 

00:12:39.040 --> 00:12:40.310 align:start position:0%
intelligence. So LM are good at
crystallized<00:12:39.600><c> intelligence.</c><00:12:40.079><c> Most</c>

00:12:40.310 --> 00:12:40.320 align:start position:0%
crystallized intelligence. Most
 

00:12:40.320 --> 00:12:41.750 align:start position:0%
crystallized intelligence. Most
benchmarks<00:12:40.880><c> measure</c><00:12:41.200><c> crystallized</c>

00:12:41.750 --> 00:12:41.760 align:start position:0%
benchmarks measure crystallized
 

00:12:41.760 --> 00:12:43.269 align:start position:0%
benchmarks measure crystallized
intelligence.<00:12:42.240><c> And</c><00:12:42.399><c> what</c><00:12:42.639><c> we've</c><00:12:42.880><c> been</c><00:12:43.040><c> seeing</c>

00:12:43.269 --> 00:12:43.279 align:start position:0%
intelligence. And what we've been seeing
 

00:12:43.279 --> 00:12:45.829 align:start position:0%
intelligence. And what we've been seeing
is<00:12:43.760><c> these</c><00:12:44.160><c> LM</c><00:12:44.560><c> models</c><00:12:44.880><c> kind</c><00:12:45.120><c> of</c><00:12:45.279><c> crawl</c><00:12:45.680><c> their</c>

00:12:45.829 --> 00:12:45.839 align:start position:0%
is these LM models kind of crawl their
 

00:12:45.839 --> 00:12:48.550 align:start position:0%
is these LM models kind of crawl their
way<00:12:46.000><c> up</c><00:12:46.399><c> to</c><00:12:46.639><c> the</c><00:12:46.800><c> top</c><00:12:47.360><c> with</c><00:12:47.920><c> crystallized</c>

00:12:48.550 --> 00:12:48.560 align:start position:0%
way up to the top with crystallized
 

00:12:48.560 --> 00:12:51.030 align:start position:0%
way up to the top with crystallized
intelligence.<00:12:49.360><c> What</c><00:12:49.600><c> they</c><00:12:49.920><c> sucked</c><00:12:50.240><c> at</c><00:12:50.480><c> was</c>

00:12:51.030 --> 00:12:51.040 align:start position:0%
intelligence. What they sucked at was
 

00:12:51.040 --> 00:12:52.870 align:start position:0%
intelligence. What they sucked at was
fluid<00:12:51.440><c> intelligence,</c><00:12:52.000><c> the</c><00:12:52.160><c> ability</c><00:12:52.480><c> to</c>

00:12:52.870 --> 00:12:52.880 align:start position:0%
fluid intelligence, the ability to
 

00:12:52.880 --> 00:12:56.470 align:start position:0%
fluid intelligence, the ability to
rapidly<00:12:53.839><c> adapt</c><00:12:54.399><c> and</c><00:12:55.360><c> learn</c><00:12:55.600><c> on</c><00:12:55.839><c> the</c><00:12:56.000><c> job,</c><00:12:56.320><c> so</c>

00:12:56.470 --> 00:12:56.480 align:start position:0%
rapidly adapt and learn on the job, so
 

00:12:56.480 --> 00:12:58.389 align:start position:0%
rapidly adapt and learn on the job, so
to<00:12:56.639><c> speak,</c><00:12:56.959><c> which</c><00:12:57.200><c> again</c><00:12:57.360><c> is</c><00:12:57.600><c> easy</c><00:12:57.920><c> for</c>

00:12:58.389 --> 00:12:58.399 align:start position:0%
to speak, which again is easy for
 

00:12:58.399 --> 00:13:00.710 align:start position:0%
to speak, which again is easy for
humans.<00:12:59.120><c> And</c><00:12:59.279><c> the</c><00:12:59.440><c> problems</c><00:12:59.760><c> on</c><00:12:59.920><c> there</c><00:13:00.320><c> look</c>

00:13:00.710 --> 00:13:00.720 align:start position:0%
humans. And the problems on there look
 

00:13:00.720 --> 00:13:03.670 align:start position:0%
humans. And the problems on there look
kind<00:13:00.959><c> of</c><00:13:01.279><c> like</c><00:13:01.760><c> this.</c><00:13:02.399><c> We</c><00:13:02.560><c> broke</c><00:13:02.720><c> it</c><00:13:02.880><c> down</c><00:13:03.040><c> in</c><00:13:03.440><c> a</c>

00:13:03.670 --> 00:13:03.680 align:start position:0%
kind of like this. We broke it down in a
 

00:13:03.680 --> 00:13:04.790 align:start position:0%
kind of like this. We broke it down in a
number<00:13:03.839><c> of</c><00:13:04.000><c> different</c><00:13:04.160><c> videos,</c><00:13:04.399><c> so</c><00:13:04.560><c> we're</c><00:13:04.639><c> not</c>

00:13:04.790 --> 00:13:04.800 align:start position:0%
number of different videos, so we're not
 

00:13:04.800 --> 00:13:06.150 align:start position:0%
number of different videos, so we're not
going to<00:13:04.880><c> go</c><00:13:04.959><c> over</c><00:13:05.120><c> it</c><00:13:05.279><c> again.</c><00:13:05.519><c> It</c><00:13:05.760><c> might</c><00:13:05.920><c> seem</c>

00:13:06.150 --> 00:13:06.160 align:start position:0%
going to go over it again. It might seem
 

00:13:06.160 --> 00:13:08.470 align:start position:0%
going to go over it again. It might seem
complicated<00:13:06.560><c> at</c><00:13:06.720><c> first,</c><00:13:06.959><c> but</c><00:13:07.440><c> once</c><00:13:08.079><c> it</c>

00:13:08.470 --> 00:13:08.480 align:start position:0%
complicated at first, but once it
 

00:13:08.480 --> 00:13:10.949 align:start position:0%
complicated at first, but once it
clicks,<00:13:09.040><c> you're</c><00:13:09.360><c> like,</c><00:13:09.600><c> "Oh,</c><00:13:10.079><c> I</c><00:13:10.399><c> get</c><00:13:10.480><c> it."</c><00:13:10.800><c> And</c>

00:13:10.949 --> 00:13:10.959 align:start position:0%
clicks, you're like, "Oh, I get it." And
 

00:13:10.959 --> 00:13:13.910 align:start position:0%
clicks, you're like, "Oh, I get it." And
then<00:13:11.040><c> it</c><00:13:11.200><c> becomes</c><00:13:11.519><c> very</c><00:13:11.839><c> easy</c><00:13:12.160><c> to</c><00:13:12.480><c> do</c><00:13:12.880><c> that</c><00:13:13.680><c> set</c>

00:13:13.910 --> 00:13:13.920 align:start position:0%
then it becomes very easy to do that set
 

00:13:13.920 --> 00:13:15.509 align:start position:0%
then it becomes very easy to do that set
of<00:13:14.160><c> problems.</c><00:13:14.880><c> So,</c><00:13:14.959><c> a</c><00:13:15.200><c> different</c><00:13:15.360><c> way</c><00:13:15.440><c> of</c>

00:13:15.509 --> 00:13:15.519 align:start position:0%
of problems. So, a different way of
 

00:13:15.519 --> 00:13:16.629 align:start position:0%
of problems. So, a different way of
saying<00:13:15.680><c> it</c><00:13:15.760><c> is</c><00:13:15.920><c> like</c><00:13:16.079><c> you</c><00:13:16.240><c> need</c><00:13:16.320><c> to</c><00:13:16.399><c> look</c><00:13:16.480><c> at</c>

00:13:16.629 --> 00:13:16.639 align:start position:0%
saying it is like you need to look at
 

00:13:16.639 --> 00:13:18.550 align:start position:0%
saying it is like you need to look at
it,<00:13:16.880><c> figure</c><00:13:17.040><c> out</c><00:13:17.279><c> like</c><00:13:17.519><c> what</c><00:13:17.760><c> sort</c><00:13:17.920><c> of</c><00:13:18.160><c> mental</c>

00:13:18.550 --> 00:13:18.560 align:start position:0%
it, figure out like what sort of mental
 

00:13:18.560 --> 00:13:20.389 align:start position:0%
it, figure out like what sort of mental
tool<00:13:18.959><c> I</c><00:13:19.200><c> need</c><00:13:19.360><c> to</c><00:13:19.519><c> create</c><00:13:19.760><c> to</c><00:13:20.000><c> solve</c><00:13:20.160><c> this</c>

00:13:20.389 --> 00:13:20.399 align:start position:0%
tool I need to create to solve this
 

00:13:20.399 --> 00:13:22.069 align:start position:0%
tool I need to create to solve this
problems.<00:13:20.639><c> And</c><00:13:20.800><c> then</c><00:13:20.959><c> it's</c><00:13:21.200><c> fairly</c><00:13:21.680><c> easy</c><00:13:21.920><c> to</c>

00:13:22.069 --> 00:13:22.079 align:start position:0%
problems. And then it's fairly easy to
 

00:13:22.079 --> 00:13:23.509 align:start position:0%
problems. And then it's fairly easy to
solve<00:13:22.320><c> the</c><00:13:22.560><c> problems.</c><00:13:22.880><c> The</c><00:13:23.200><c> problems</c>

00:13:23.509 --> 00:13:23.519 align:start position:0%
solve the problems. The problems
 

00:13:23.519 --> 00:13:26.230 align:start position:0%
solve the problems. The problems
themselves<00:13:24.160><c> are</c><00:13:24.880><c> somewhat</c><00:13:25.360><c> simple.</c><00:13:26.079><c> And</c>

00:13:26.230 --> 00:13:26.240 align:start position:0%
themselves are somewhat simple. And
 

00:13:26.240 --> 00:13:27.750 align:start position:0%
themselves are somewhat simple. And
that's<00:13:26.480><c> why</c><00:13:26.720><c> prior</c><00:13:26.959><c> to</c><00:13:27.120><c> this,</c><00:13:27.360><c> for</c><00:13:27.519><c> the</c><00:13:27.680><c> most</c>

00:13:27.750 --> 00:13:27.760 align:start position:0%
that's why prior to this, for the most
 

00:13:27.760 --> 00:13:30.150 align:start position:0%
that's why prior to this, for the most
part,<00:13:28.240><c> all</c><00:13:28.399><c> these</c><00:13:28.639><c> models</c><00:13:29.360><c> really</c><00:13:29.680><c> struggled.</c>

00:13:30.150 --> 00:13:30.160 align:start position:0%
part, all these models really struggled.
 

00:13:30.160 --> 00:13:33.670 align:start position:0%
part, all these models really struggled.
I<00:13:30.480><c> mean</c><00:13:30.880><c> 6%</c><00:13:32.000><c> and</c><00:13:32.480><c> thereabouts</c><00:13:33.200><c> kind</c><00:13:33.279><c> of</c><00:13:33.440><c> were</c>

00:13:33.670 --> 00:13:33.680 align:start position:0%
I mean 6% and thereabouts kind of were
 

00:13:33.680 --> 00:13:35.750 align:start position:0%
I mean 6% and thereabouts kind of were
some<00:13:33.839><c> of</c><00:13:33.920><c> the</c><00:13:34.320><c> higher</c><00:13:34.639><c> ones</c><00:13:34.959><c> even</c><00:13:35.279><c> stuff</c><00:13:35.519><c> as</c>

00:13:35.750 --> 00:13:35.760 align:start position:0%
some of the higher ones even stuff as
 

00:13:35.760 --> 00:13:38.389 align:start position:0%
some of the higher ones even stuff as
good<00:13:35.839><c> as</c><00:13:36.079><c> the</c><00:13:36.240><c> 03</c><00:13:36.959><c> high</c><00:13:37.360><c> which</c><00:13:37.519><c> is</c><00:13:37.920><c> a</c><00:13:38.079><c> very</c><00:13:38.240><c> good</c>

00:13:38.389 --> 00:13:38.399 align:start position:0%
good as the 03 high which is a very good
 

00:13:38.399 --> 00:13:41.110 align:start position:0%
good as the 03 high which is a very good
model<00:13:38.720><c> 03</c><00:13:39.200><c> Pro</c><00:13:39.839><c> even</c><00:13:40.079><c> though</c><00:13:40.240><c> it</c><00:13:40.399><c> cost</c><00:13:40.720><c> a</c><00:13:41.040><c> lot</c>

00:13:41.110 --> 00:13:41.120 align:start position:0%
model 03 Pro even though it cost a lot
 

00:13:41.120 --> 00:13:44.230 align:start position:0%
model 03 Pro even though it cost a lot
of<00:13:41.279><c> money</c><00:13:41.519><c> to</c><00:13:41.760><c> run</c><00:13:42.240><c> what</c><00:13:42.399><c> is</c><00:13:42.560><c> that</c><00:13:42.959><c> 5%</c><00:13:43.600><c> accuracy</c>

00:13:44.230 --> 00:13:44.240 align:start position:0%
of money to run what is that 5% accuracy
 

00:13:44.240 --> 00:13:46.389 align:start position:0%
of money to run what is that 5% accuracy
and<00:13:44.399><c> then</c><00:13:44.639><c> a</c><00:13:44.959><c> few</c><00:13:45.279><c> days</c><00:13:45.440><c> ago</c><00:13:45.839><c> they</c><00:13:46.000><c> got</c><00:13:46.079><c> a</c><00:13:46.240><c> call</c>

00:13:46.389 --> 00:13:46.399 align:start position:0%
and then a few days ago they got a call
 

00:13:46.399 --> 00:13:50.870 align:start position:0%
and then a few days ago they got a call
from<00:13:46.639><c> XAI</c><00:13:47.839><c> 24</c><00:13:48.399><c> hours</c><00:13:49.200><c> uh</c><00:13:49.760><c> before</c><00:13:50.320><c> I</c><00:13:50.560><c> think</c><00:13:50.639><c> they</c>

00:13:50.870 --> 00:13:50.880 align:start position:0%
from XAI 24 hours uh before I think they
 

00:13:50.880 --> 00:13:52.629 align:start position:0%
from XAI 24 hours uh before I think they
published<00:13:51.200><c> all</c><00:13:51.360><c> these</c><00:13:51.600><c> results</c><00:13:52.160><c> and</c><00:13:52.399><c> they</c>

00:13:52.629 --> 00:13:52.639 align:start position:0%
published all these results and they
 

00:13:52.639 --> 00:13:55.430 align:start position:0%
published all these results and they
want<00:13:52.800><c> to</c><00:13:52.880><c> test</c><00:13:53.120><c> Gro</c><00:13:53.519><c> 4</c><00:13:53.760><c> on</c><00:13:53.920><c> the</c><00:13:54.160><c> Arc</c><00:13:54.480><c> AGI.</c><00:13:55.279><c> So</c>

00:13:55.430 --> 00:13:55.440 align:start position:0%
want to test Gro 4 on the Arc AGI. So
 

00:13:55.440 --> 00:13:58.069 align:start position:0%
want to test Gro 4 on the Arc AGI. So
again<00:13:55.680><c> this</c><00:13:55.839><c> is</c><00:13:55.920><c> Greg</c><00:13:56.240><c> from</c><00:13:56.880><c> um</c><00:13:57.040><c> Arc</c><00:13:57.360><c> AGI.</c><00:13:57.920><c> So,</c>

00:13:58.069 --> 00:13:58.079 align:start position:0%
again this is Greg from um Arc AGI. So,
 

00:13:58.079 --> 00:13:59.509 align:start position:0%
again this is Greg from um Arc AGI. So,
he's<00:13:58.240><c> saying,</c><00:13:58.399><c> "We've</c><00:13:58.720><c> heard</c><00:13:58.880><c> the</c><00:13:59.040><c> rumors.</c><00:13:59.360><c> We</c>

00:13:59.509 --> 00:13:59.519 align:start position:0%
he's saying, "We've heard the rumors. We
 

00:13:59.519 --> 00:14:01.269 align:start position:0%
he's saying, "We've heard the rumors. We
knew<00:13:59.680><c> it</c><00:13:59.920><c> would</c><00:14:00.000><c> be</c><00:14:00.160><c> good.</c><00:14:00.560><c> We</c><00:14:00.720><c> didn't</c><00:14:00.880><c> know</c><00:14:01.120><c> it</c>

00:14:01.269 --> 00:14:01.279 align:start position:0%
knew it would be good. We didn't know it
 

00:14:01.279 --> 00:14:04.069 align:start position:0%
knew it would be good. We didn't know it
would<00:14:01.440><c> become</c><00:14:01.680><c> the</c><00:14:02.079><c> number</c><00:14:02.320><c> one</c><00:14:02.959><c> public</c><00:14:03.600><c> model</c>

00:14:04.069 --> 00:14:04.079 align:start position:0%
would become the number one public model
 

00:14:04.079 --> 00:14:06.949 align:start position:0%
would become the number one public model
on<00:14:04.560><c> ARGI."</c><00:14:05.680><c> Here's</c><00:14:05.920><c> the</c><00:14:06.160><c> testing</c><00:14:06.480><c> story</c><00:14:06.800><c> and</c>

00:14:06.949 --> 00:14:06.959 align:start position:0%
on ARGI." Here's the testing story and
 

00:14:06.959 --> 00:14:09.670 align:start position:0%
on ARGI." Here's the testing story and
what<00:14:07.199><c> the</c><00:14:07.600><c> results</c><00:14:08.240><c> mean.</c><00:14:08.720><c> They</c><00:14:08.959><c> chatted</c><00:14:09.440><c> with</c>

00:14:09.670 --> 00:14:09.680 align:start position:0%
what the results mean. They chatted with
 

00:14:09.680 --> 00:14:12.150 align:start position:0%
what the results mean. They chatted with
Jimmy<00:14:10.000><c> from</c><00:14:10.240><c> the</c><00:14:10.320><c> XAI</c><00:14:10.959><c> team.</c><00:14:11.519><c> They</c><00:14:11.760><c> wanted</c><00:14:11.920><c> to</c>

00:14:12.150 --> 00:14:12.160 align:start position:0%
Jimmy from the XAI team. They wanted to
 

00:14:12.160 --> 00:14:14.949 align:start position:0%
Jimmy from the XAI team. They wanted to
validate<00:14:12.560><c> the</c><00:14:12.800><c> Gro</c><00:14:13.199><c> 4</c><00:14:13.519><c> score.</c><00:14:14.079><c> They</c><00:14:14.560><c> did</c><00:14:14.800><c> their</c>

00:14:14.949 --> 00:14:14.959 align:start position:0%
validate the Gro 4 score. They did their
 

00:14:14.959 --> 00:14:17.990 align:start position:0%
validate the Gro 4 score. They did their
own<00:14:15.360><c> testing,</c><00:14:16.079><c> but</c><00:14:16.320><c> they</c><00:14:16.639><c> wanted</c><00:14:16.959><c> to</c><00:14:17.600><c> validate</c>

00:14:17.990 --> 00:14:18.000 align:start position:0%
own testing, but they wanted to validate
 

00:14:18.000 --> 00:14:20.310 align:start position:0%
own testing, but they wanted to validate
the<00:14:18.240><c> score,</c><00:14:18.560><c> measure</c><00:14:18.959><c> possible</c><00:14:19.360><c> overfitting.</c>

00:14:20.310 --> 00:14:20.320 align:start position:0%
the score, measure possible overfitting.
 

00:14:20.320 --> 00:14:22.389 align:start position:0%
the score, measure possible overfitting.
So<00:14:20.560><c> basically</c><00:14:21.199><c> you're</c><00:14:21.519><c> able</c><00:14:21.760><c> to</c><00:14:21.920><c> kind</c><00:14:22.160><c> of</c>

00:14:22.389 --> 00:14:22.399 align:start position:0%
So basically you're able to kind of
 

00:14:22.399 --> 00:14:24.069 align:start position:0%
So basically you're able to kind of
fudge<00:14:22.800><c> the</c><00:14:23.040><c> numbers</c><00:14:23.199><c> a</c><00:14:23.440><c> little</c><00:14:23.519><c> bit</c><00:14:23.839><c> by</c>

00:14:24.069 --> 00:14:24.079 align:start position:0%
fudge the numbers a little bit by
 

00:14:24.079 --> 00:14:26.949 align:start position:0%
fudge the numbers a little bit by
training<00:14:24.399><c> these</c><00:14:24.639><c> models</c><00:14:25.120><c> on</c><00:14:26.000><c> the</c><00:14:26.320><c> tests</c><00:14:26.720><c> or</c>

00:14:26.949 --> 00:14:26.959 align:start position:0%
training these models on the tests or
 

00:14:26.959 --> 00:14:29.670 align:start position:0%
training these models on the tests or
similar<00:14:27.279><c> tests</c><00:14:27.839><c> and</c><00:14:28.079><c> get</c><00:14:28.240><c> a</c><00:14:28.399><c> higher</c><00:14:28.720><c> score</c><00:14:29.279><c> but</c>

00:14:29.670 --> 00:14:29.680 align:start position:0%
similar tests and get a higher score but
 

00:14:29.680 --> 00:14:32.150 align:start position:0%
similar tests and get a higher score but
it<00:14:30.000><c> not</c><00:14:30.240><c> because</c><00:14:30.480><c> it</c><00:14:30.720><c> generalizes</c><00:14:31.440><c> well</c><00:14:32.000><c> just</c>

00:14:32.150 --> 00:14:32.160 align:start position:0%
it not because it generalizes well just
 

00:14:32.160 --> 00:14:34.389 align:start position:0%
it not because it generalizes well just
more<00:14:32.399><c> like</c><00:14:32.720><c> memorization.</c><00:14:33.680><c> And</c><00:14:33.839><c> so</c><00:14:34.079><c> to</c><00:14:34.240><c> get</c>

00:14:34.389 --> 00:14:34.399 align:start position:0%
more like memorization. And so to get
 

00:14:34.399 --> 00:14:35.750 align:start position:0%
more like memorization. And so to get
the<00:14:34.560><c> official</c><00:14:34.880><c> score</c><00:14:35.120><c> a</c><00:14:35.199><c> couple</c><00:14:35.360><c> things</c><00:14:35.600><c> must</c>

00:14:35.750 --> 00:14:35.760 align:start position:0%
the official score a couple things must
 

00:14:35.760 --> 00:14:38.710 align:start position:0%
the official score a couple things must
be<00:14:35.920><c> true.</c><00:14:36.240><c> One</c><00:14:36.720><c> the</c><00:14:36.959><c> new</c><00:14:37.199><c> model</c><00:14:38.079><c> has</c><00:14:38.320><c> to</c><00:14:38.560><c> be</c>

00:14:38.710 --> 00:14:38.720 align:start position:0%
be true. One the new model has to be
 

00:14:38.720 --> 00:14:40.949 align:start position:0%
be true. One the new model has to be
tested<00:14:39.279><c> on</c><00:14:39.519><c> the</c><00:14:39.920><c> sort</c><00:14:40.079><c> of</c><00:14:40.240><c> semi-private</c>

00:14:40.949 --> 00:14:40.959 align:start position:0%
tested on the sort of semi-private
 

00:14:40.959 --> 00:14:42.629 align:start position:0%
tested on the sort of semi-private
evaluation<00:14:41.519><c> sets</c><00:14:41.920><c> right</c><00:14:42.079><c> and</c><00:14:42.320><c> has</c><00:14:42.399><c> to</c><00:14:42.560><c> be</c>

00:14:42.629 --> 00:14:42.639 align:start position:0%
evaluation sets right and has to be
 

00:14:42.639 --> 00:14:45.430 align:start position:0%
evaluation sets right and has to be
tested<00:14:43.040><c> by</c><00:14:43.680><c> I</c><00:14:43.839><c> assume</c><00:14:44.079><c> somebody</c><00:14:44.399><c> at</c><00:14:44.560><c> ARC</c><00:14:44.880><c> AGI</c>

00:14:45.430 --> 00:14:45.440 align:start position:0%
tested by I assume somebody at ARC AGI
 

00:14:45.440 --> 00:14:47.829 align:start position:0%
tested by I assume somebody at ARC AGI
and<00:14:45.680><c> there's</c><00:14:46.000><c> rules</c><00:14:46.320><c> like</c><00:14:46.880><c> you</c><00:14:46.959><c> know</c><00:14:47.120><c> no</c><00:14:47.440><c> data</c>

00:14:47.829 --> 00:14:47.839 align:start position:0%
and there's rules like you know no data
 

00:14:47.839 --> 00:14:50.310 align:start position:0%
and there's rules like you know no data
retention<00:14:48.240><c> by</c><00:14:48.800><c> XAI</c><00:14:49.279><c> in</c><00:14:49.440><c> this</c><00:14:49.519><c> case.</c><00:14:49.839><c> Uh</c><00:14:50.079><c> the</c>

00:14:50.310 --> 00:14:50.320 align:start position:0%
retention by XAI in this case. Uh the
 

00:14:50.320 --> 00:14:51.750 align:start position:0%
retention by XAI in this case. Uh the
model<00:14:50.560><c> checkpoint</c><00:14:50.959><c> must</c><00:14:51.199><c> be</c><00:14:51.279><c> intended</c><00:14:51.600><c> for</c>

00:14:51.750 --> 00:14:51.760 align:start position:0%
model checkpoint must be intended for
 

00:14:51.760 --> 00:14:53.350 align:start position:0%
model checkpoint must be intended for
public<00:14:52.000><c> use,</c><00:14:52.399><c> right?</c><00:14:52.639><c> So</c><00:14:52.720><c> it's</c><00:14:52.880><c> it</c><00:14:53.120><c> can't</c><00:14:53.279><c> be</c>

00:14:53.350 --> 00:14:53.360 align:start position:0%
public use, right? So it's it can't be
 

00:14:53.360 --> 00:14:56.150 align:start position:0%
public use, right? So it's it can't be
some<00:14:53.600><c> secret</c><00:14:54.000><c> model</c><00:14:54.639><c> that</c><00:14:54.880><c> only</c><00:14:55.120><c> XAI</c><00:14:55.760><c> can</c><00:14:55.920><c> use.</c>

00:14:56.150 --> 00:14:56.160 align:start position:0%
some secret model that only XAI can use.
 

00:14:56.160 --> 00:14:58.389 align:start position:0%
some secret model that only XAI can use.
It<00:14:56.399><c> has</c><00:14:56.560><c> to</c><00:14:56.639><c> be</c><00:14:56.800><c> the</c><00:14:56.959><c> model</c><00:14:57.199><c> that</c><00:14:57.680><c> all</c><00:14:57.920><c> of</c><00:14:58.160><c> us</c>

00:14:58.389 --> 00:14:58.399 align:start position:0%
It has to be the model that all of us
 

00:14:58.399 --> 00:15:00.870 align:start position:0%
It has to be the model that all of us
can<00:14:58.639><c> use</c><00:14:59.120><c> and</c><00:14:59.360><c> of</c><00:14:59.519><c> course</c><00:14:59.839><c> no</c><00:15:00.000><c> rate</c><00:15:00.320><c> limits</c><00:15:00.720><c> so</c>

00:15:00.870 --> 00:15:00.880 align:start position:0%
can use and of course no rate limits so
 

00:15:00.880 --> 00:15:02.790 align:start position:0%
can use and of course no rate limits so
they<00:15:01.040><c> don't</c><00:15:01.199><c> have</c><00:15:01.360><c> to</c><00:15:01.519><c> wait</c><00:15:01.760><c> for</c><00:15:01.920><c> that.</c><00:15:02.320><c> And</c><00:15:02.560><c> so</c>

00:15:02.790 --> 00:15:02.800 align:start position:0%
they don't have to wait for that. And so
 

00:15:02.800 --> 00:15:05.269 align:start position:0%
they don't have to wait for that. And so
the<00:15:03.040><c> facts</c><00:15:03.600><c> Gro</c><00:15:04.000><c> 4</c><00:15:04.160><c> is</c><00:15:04.320><c> now</c><00:15:04.480><c> the</c><00:15:04.800><c> top</c>

00:15:05.269 --> 00:15:05.279 align:start position:0%
the facts Gro 4 is now the top
 

00:15:05.279 --> 00:15:07.509 align:start position:0%
the facts Gro 4 is now the top
performing<00:15:05.920><c> publicly</c><00:15:06.399><c> available</c><00:15:06.880><c> model</c><00:15:07.199><c> on</c>

00:15:07.509 --> 00:15:07.519 align:start position:0%
performing publicly available model on
 

00:15:07.519 --> 00:15:10.069 align:start position:0%
performing publicly available model on
ARC<00:15:07.839><c> EGI.</c><00:15:08.720><c> This</c><00:15:08.880><c> even</c><00:15:09.120><c> outperforms</c>

00:15:10.069 --> 00:15:10.079 align:start position:0%
ARC EGI. This even outperforms
 

00:15:10.079 --> 00:15:12.069 align:start position:0%
ARC EGI. This even outperforms
purpose-built<00:15:10.880><c> solutions</c><00:15:11.279><c> submitted</c><00:15:11.760><c> on</c>

00:15:12.069 --> 00:15:12.079 align:start position:0%
purpose-built solutions submitted on
 

00:15:12.079 --> 00:15:14.389 align:start position:0%
purpose-built solutions submitted on
Kiagle.<00:15:12.800><c> So</c><00:15:13.279><c> various</c><00:15:13.760><c> machine</c><00:15:14.079><c> learning</c>

00:15:14.389 --> 00:15:14.399 align:start position:0%
Kiagle. So various machine learning
 

00:15:14.399 --> 00:15:16.310 align:start position:0%
Kiagle. So various machine learning
approaches,<00:15:14.800><c> they're</c><00:15:15.199><c> customuilt</c><00:15:15.920><c> for</c><00:15:16.079><c> these</c>

00:15:16.310 --> 00:15:16.320 align:start position:0%
approaches, they're customuilt for these
 

00:15:16.320 --> 00:15:19.189 align:start position:0%
approaches, they're customuilt for these
tests.<00:15:16.800><c> Even</c><00:15:17.279><c> those</c><00:15:18.000><c> are</c><00:15:18.320><c> not</c><00:15:18.480><c> as</c><00:15:18.720><c> good</c><00:15:18.880><c> as</c>

00:15:19.189 --> 00:15:19.199 align:start position:0%
tests. Even those are not as good as
 

00:15:19.199 --> 00:15:21.910 align:start position:0%
tests. Even those are not as good as
Grock<00:15:19.600><c> 4.</c><00:15:20.240><c> Second,</c><00:15:20.560><c> ARGI</c><00:15:21.279><c> 2</c><00:15:21.440><c> is</c><00:15:21.600><c> hard</c><00:15:21.760><c> for</c>

00:15:21.910 --> 00:15:21.920 align:start position:0%
Grock 4. Second, ARGI 2 is hard for
 

00:15:21.920 --> 00:15:24.069 align:start position:0%
Grock 4. Second, ARGI 2 is hard for
current<00:15:22.160><c> AI</c><00:15:22.639><c> models.</c><00:15:23.279><c> Why?</c><00:15:23.600><c> Again,</c><00:15:23.839><c> this</c><00:15:23.920><c> is</c>

00:15:24.069 --> 00:15:24.079 align:start position:0%
current AI models. Why? Again, this is
 

00:15:24.079 --> 00:15:25.750 align:start position:0%
current AI models. Why? Again, this is
the<00:15:24.320><c> important</c><00:15:24.880><c> bit.</c><00:15:25.279><c> This</c><00:15:25.440><c> is</c><00:15:25.519><c> what</c><00:15:25.600><c> we've</c>

00:15:25.750 --> 00:15:25.760 align:start position:0%
the important bit. This is what we've
 

00:15:25.760 --> 00:15:26.870 align:start position:0%
the important bit. This is what we've
been<00:15:25.920><c> talking</c><00:15:26.000><c> about.</c><00:15:26.240><c> To</c><00:15:26.399><c> score</c><00:15:26.639><c> well,</c>

00:15:26.870 --> 00:15:26.880 align:start position:0%
been talking about. To score well,
 

00:15:26.880 --> 00:15:30.389 align:start position:0%
been talking about. To score well,
models<00:15:27.199><c> have</c><00:15:27.440><c> to</c><00:15:27.680><c> learn</c><00:15:28.160><c> a</c><00:15:28.720><c> mini</c><00:15:29.360><c> skill</c><00:15:29.920><c> from</c><00:15:30.160><c> a</c>

00:15:30.389 --> 00:15:30.399 align:start position:0%
models have to learn a mini skill from a
 

00:15:30.399 --> 00:15:32.949 align:start position:0%
models have to learn a mini skill from a
series<00:15:30.800><c> of</c><00:15:31.360><c> training</c><00:15:31.920><c> examples,</c><00:15:32.560><c> right?</c><00:15:32.800><c> So</c>

00:15:32.949 --> 00:15:32.959 align:start position:0%
series of training examples, right? So
 

00:15:32.959 --> 00:15:35.110 align:start position:0%
series of training examples, right? So
they<00:15:33.120><c> they</c><00:15:33.440><c> show</c><00:15:33.839><c> three</c><00:15:34.079><c> examples,</c><00:15:34.720><c> one,</c><00:15:34.959><c> two,</c>

00:15:35.110 --> 00:15:35.120 align:start position:0%
they they show three examples, one, two,
 

00:15:35.120 --> 00:15:36.629 align:start position:0%
they they show three examples, one, two,
and<00:15:35.360><c> three.</c><00:15:35.760><c> And</c><00:15:35.839><c> you</c><00:15:36.000><c> have</c><00:15:36.079><c> to</c><00:15:36.240><c> figure</c><00:15:36.320><c> out,</c>

00:15:36.629 --> 00:15:36.639 align:start position:0%
and three. And you have to figure out,
 

00:15:36.639 --> 00:15:38.790 align:start position:0%
and three. And you have to figure out,
okay,<00:15:37.199><c> what</c><00:15:37.440><c> is</c><00:15:37.600><c> the</c><00:15:37.839><c> sort</c><00:15:38.079><c> of</c><00:15:38.240><c> common</c>

00:15:38.790 --> 00:15:38.800 align:start position:0%
okay, what is the sort of common
 

00:15:38.800 --> 00:15:40.389 align:start position:0%
okay, what is the sort of common
pattern?<00:15:39.279><c> You</c><00:15:39.440><c> kind</c><00:15:39.600><c> of</c><00:15:39.680><c> have</c><00:15:39.839><c> to</c><00:15:40.000><c> have</c><00:15:40.079><c> a</c>

00:15:40.389 --> 00:15:40.399 align:start position:0%
pattern? You kind of have to have a
 

00:15:40.399 --> 00:15:43.030 align:start position:0%
pattern? You kind of have to have a
hypothesis.<00:15:41.440><c> And</c><00:15:41.600><c> then</c><00:15:42.240><c> you're</c><00:15:42.480><c> able</c><00:15:42.720><c> to</c>

00:15:43.030 --> 00:15:43.040 align:start position:0%
hypothesis. And then you're able to
 

00:15:43.040 --> 00:15:45.030 align:start position:0%
hypothesis. And then you're able to
they're<00:15:43.279><c> calling</c><00:15:43.519><c> that</c><00:15:43.680><c> like</c><00:15:43.839><c> a</c><00:15:44.079><c> mini</c><00:15:44.560><c> skill,</c>

00:15:45.030 --> 00:15:45.040 align:start position:0%
they're calling that like a mini skill,
 

00:15:45.040 --> 00:15:47.509 align:start position:0%
they're calling that like a mini skill,
right?<00:15:45.360><c> Then</c><00:15:45.600><c> you</c><00:15:45.760><c> apply</c><00:15:46.079><c> that</c><00:15:46.320><c> to</c><00:15:46.639><c> solving</c><00:15:47.120><c> a</c>

00:15:47.509 --> 00:15:47.519 align:start position:0%
right? Then you apply that to solving a
 

00:15:47.519 --> 00:15:48.949 align:start position:0%
right? Then you apply that to solving a
problem,<00:15:48.160><c> right?</c><00:15:48.399><c> Then</c><00:15:48.560><c> you</c><00:15:48.720><c> have</c><00:15:48.800><c> to</c>

00:15:48.949 --> 00:15:48.959 align:start position:0%
problem, right? Then you have to
 

00:15:48.959 --> 00:15:51.030 align:start position:0%
problem, right? Then you have to
demonstrate<00:15:49.360><c> the</c><00:15:49.519><c> skill</c><00:15:49.839><c> at</c><00:15:50.160><c> test</c><00:15:50.399><c> time.</c><00:15:50.800><c> The</c>

00:15:51.030 --> 00:15:51.040 align:start position:0%
demonstrate the skill at test time. The
 

00:15:51.040 --> 00:15:54.150 align:start position:0%
demonstrate the skill at test time. The
previous<00:15:51.279><c> top</c><00:15:51.600><c> score</c><00:15:51.839><c> was</c><00:15:52.240><c> 8%</c><00:15:53.199><c> and</c><00:15:53.519><c> below</c><00:15:53.839><c> 10%</c>

00:15:54.150 --> 00:15:54.160 align:start position:0%
previous top score was 8% and below 10%
 

00:15:54.160 --> 00:15:55.670 align:start position:0%
previous top score was 8% and below 10%
is<00:15:54.320><c> noisy,</c><00:15:54.720><c> meaning</c><00:15:54.880><c> it</c><00:15:55.120><c> could</c><00:15:55.360><c> be</c>

00:15:55.670 --> 00:15:55.680 align:start position:0%
is noisy, meaning it could be
 

00:15:55.680 --> 00:15:58.389 align:start position:0%
is noisy, meaning it could be
inconclusive,<00:15:56.480><c> right?</c><00:15:56.800><c> But</c><00:15:57.199><c> you</c><00:15:57.360><c> know,</c><00:15:57.680><c> 16%</c>

00:15:58.389 --> 00:15:58.399 align:start position:0%
inconclusive, right? But you know, 16%
 

00:15:58.399 --> 00:15:59.990 align:start position:0%
inconclusive, right? But you know, 16%
basically<00:15:58.880><c> breaks</c><00:15:59.199><c> through</c><00:15:59.360><c> that</c><00:15:59.600><c> noise</c>

00:15:59.990 --> 00:16:00.000 align:start position:0%
basically breaks through that noise
 

00:16:00.000 --> 00:16:03.670 align:start position:0%
basically breaks through that noise
barrier.<00:16:00.800><c> And</c><00:16:01.199><c> Grock</c><00:16:01.519><c> is</c><00:16:01.600><c> showing</c><00:16:02.320><c> nonzero</c>

00:16:03.670 --> 00:16:03.680 align:start position:0%
barrier. And Grock is showing nonzero
 

00:16:03.680 --> 00:16:07.030 align:start position:0%
barrier. And Grock is showing nonzero
levels<00:16:04.079><c> of</c><00:16:04.800><c> fluid</c><00:16:05.360><c> intelligence.</c><00:16:06.480><c> So</c><00:16:06.800><c> this</c>

00:16:07.030 --> 00:16:07.040 align:start position:0%
levels of fluid intelligence. So this
 

00:16:07.040 --> 00:16:09.990 align:start position:0%
levels of fluid intelligence. So this
might<00:16:07.279><c> be</c><00:16:07.519><c> a</c><00:16:08.000><c> brand</c><00:16:08.240><c> new</c><00:16:08.720><c> sort</c><00:16:08.959><c> of</c><00:16:09.519><c> scalar</c>

00:16:09.990 --> 00:16:10.000 align:start position:0%
might be a brand new sort of scalar
 

00:16:10.000 --> 00:16:12.389 align:start position:0%
might be a brand new sort of scalar
ability<00:16:10.240><c> that</c><00:16:10.480><c> we</c><00:16:10.639><c> look</c><00:16:10.800><c> at</c><00:16:10.959><c> for</c><00:16:11.600><c> all</c><00:16:11.759><c> of</c><00:16:11.920><c> these</c>

00:16:12.389 --> 00:16:12.399 align:start position:0%
ability that we look at for all of these
 

00:16:12.399 --> 00:16:14.710 align:start position:0%
ability that we look at for all of these
models<00:16:13.040><c> moving</c><00:16:13.440><c> forward.</c><00:16:14.000><c> And</c><00:16:14.240><c> maybe</c><00:16:14.399><c> this</c>

00:16:14.710 --> 00:16:14.720 align:start position:0%
models moving forward. And maybe this
 

00:16:14.720 --> 00:16:16.550 align:start position:0%
models moving forward. And maybe this
terminology<00:16:15.360><c> fluid</c><00:16:15.680><c> intelligence,</c><00:16:16.240><c> maybe</c>

00:16:16.550 --> 00:16:16.560 align:start position:0%
terminology fluid intelligence, maybe
 

00:16:16.560 --> 00:16:18.949 align:start position:0%
terminology fluid intelligence, maybe
that<00:16:16.800><c> will</c><00:16:17.040><c> stick</c><00:16:17.440><c> or</c><00:16:18.160><c> maybe</c><00:16:18.480><c> we're</c><00:16:18.639><c> going</c><00:16:18.720><c> to</c>

00:16:18.949 --> 00:16:18.959 align:start position:0%
that will stick or maybe we're going to
 

00:16:18.959 --> 00:16:20.629 align:start position:0%
that will stick or maybe we're going to
come<00:16:19.040><c> up</c><00:16:19.279><c> with</c><00:16:19.440><c> something</c><00:16:20.000><c> different.</c><00:16:20.480><c> It</c>

00:16:20.629 --> 00:16:20.639 align:start position:0%
come up with something different. It
 

00:16:20.639 --> 00:16:22.150 align:start position:0%
come up with something different. It
doesn't<00:16:20.800><c> matter.</c><00:16:21.040><c> The</c><00:16:21.199><c> question</c><00:16:21.360><c> is,</c><00:16:21.839><c> can</c>

00:16:22.150 --> 00:16:22.160 align:start position:0%
doesn't matter. The question is, can
 

00:16:22.160 --> 00:16:25.189 align:start position:0%
doesn't matter. The question is, can
these<00:16:22.480><c> models</c><00:16:23.120><c> adapt?</c><00:16:23.759><c> Can</c><00:16:24.000><c> they</c><00:16:24.399><c> learn</c>

00:16:25.189 --> 00:16:25.199 align:start position:0%
these models adapt? Can they learn
 

00:16:25.199 --> 00:16:28.069 align:start position:0%
these models adapt? Can they learn
quickly<00:16:25.680><c> in</c><00:16:26.160><c> novel</c><00:16:26.639><c> situations</c><00:16:27.279><c> and</c><00:16:27.680><c> apply</c>

00:16:28.069 --> 00:16:28.079 align:start position:0%
quickly in novel situations and apply
 

00:16:28.079 --> 00:16:30.310 align:start position:0%
quickly in novel situations and apply
that<00:16:28.320><c> and</c><00:16:28.560><c> go</c><00:16:28.720><c> forward?</c><00:16:29.199><c> Again,</c><00:16:29.519><c> Gro</c><00:16:29.920><c> 4</c><00:16:30.160><c> right</c>

00:16:30.310 --> 00:16:30.320 align:start position:0%
that and go forward? Again, Gro 4 right
 

00:16:30.320 --> 00:16:32.550 align:start position:0%
that and go forward? Again, Gro 4 right
now<00:16:30.480><c> is</c><00:16:30.800><c> one</c><00:16:30.959><c> of</c><00:16:31.040><c> the</c><00:16:31.279><c> only</c><00:16:31.600><c> models</c><00:16:32.000><c> that's</c>

00:16:32.550 --> 00:16:32.560 align:start position:0%
now is one of the only models that's
 

00:16:32.560 --> 00:16:34.069 align:start position:0%
now is one of the only models that's
showing<00:16:32.880><c> that.</c><00:16:33.279><c> But,</c><00:16:33.440><c> you</c><00:16:33.600><c> know,</c><00:16:33.839><c> we're</c><00:16:34.000><c> going</c>

00:16:34.069 --> 00:16:34.079 align:start position:0%
showing that. But, you know, we're going
 

00:16:34.079 --> 00:16:37.269 align:start position:0%
showing that. But, you know, we're going
to<00:16:34.240><c> see</c><00:16:34.720><c> the</c><00:16:35.360><c> Gemini's</c><00:16:36.160><c> answer</c><00:16:36.480><c> to</c><00:16:36.639><c> this</c><00:16:36.959><c> drop</c>

00:16:37.269 --> 00:16:37.279 align:start position:0%
to see the Gemini's answer to this drop
 

00:16:37.279 --> 00:16:40.069 align:start position:0%
to see the Gemini's answer to this drop
soon.<00:16:37.600><c> We're</c><00:16:37.759><c> going</c><00:16:37.839><c> to</c><00:16:37.920><c> see</c><00:16:38.079><c> GPT5</c><00:16:39.040><c> drop</c><00:16:39.519><c> soon.</c>

00:16:40.069 --> 00:16:40.079 align:start position:0%
soon. We're going to see GPT5 drop soon.
 

00:16:40.079 --> 00:16:42.389 align:start position:0%
soon. We're going to see GPT5 drop soon.
And<00:16:40.240><c> it's</c><00:16:40.480><c> going</c><00:16:40.560><c> to</c><00:16:40.639><c> be</c><00:16:41.120><c> really</c><00:16:41.680><c> interesting</c>

00:16:42.389 --> 00:16:42.399 align:start position:0%
And it's going to be really interesting
 

00:16:42.399 --> 00:16:44.069 align:start position:0%
And it's going to be really interesting
how<00:16:42.639><c> well</c><00:16:42.880><c> they</c><00:16:43.040><c> do.</c><00:16:43.199><c> Again,</c><00:16:43.519><c> the</c><00:16:43.680><c> rumors</c><00:16:43.920><c> are</c>

00:16:44.069 --> 00:16:44.079 align:start position:0%
how well they do. Again, the rumors are
 

00:16:44.079 --> 00:16:46.310 align:start position:0%
how well they do. Again, the rumors are
that<00:16:44.240><c> GPT5</c><00:16:44.959><c> does</c><00:16:45.199><c> a</c><00:16:45.440><c> little</c><00:16:45.600><c> bit</c><00:16:45.759><c> better</c><00:16:46.000><c> on</c>

00:16:46.310 --> 00:16:46.320 align:start position:0%
that GPT5 does a little bit better on
 

00:16:46.320 --> 00:16:49.829 align:start position:0%
that GPT5 does a little bit better on
this<00:16:46.480><c> test.</c><00:16:47.120><c> And</c><00:16:47.440><c> the</c><00:16:47.759><c> new</c><00:16:48.160><c> ARC</c><00:16:48.880><c> prize,</c><00:16:49.360><c> the</c><00:16:49.519><c> G</c>

00:16:49.829 --> 00:16:49.839 align:start position:0%
this test. And the new ARC prize, the G
 

00:16:49.839 --> 00:16:52.870 align:start position:0%
this test. And the new ARC prize, the G
grand<00:16:50.079><c> prize</c><00:16:50.880><c> is</c><00:16:51.120><c> kind</c><00:16:51.279><c> of</c><00:16:51.360><c> like</c><00:16:51.600><c> in</c><00:16:52.000><c> this</c>

00:16:52.870 --> 00:16:52.880 align:start position:0%
grand prize is kind of like in this
 

00:16:52.880 --> 00:16:55.110 align:start position:0%
grand prize is kind of like in this
green<00:16:53.360><c> rectangle.</c><00:16:54.160><c> So</c><00:16:54.399><c> basically,</c><00:16:54.800><c> again,</c>

00:16:55.110 --> 00:16:55.120 align:start position:0%
green rectangle. So basically, again,
 

00:16:55.120 --> 00:16:59.430 align:start position:0%
green rectangle. So basically, again,
cost<00:16:55.680><c> and</c><00:16:56.480><c> score.</c><00:16:57.279><c> This</c><00:16:57.440><c> is</c><00:16:57.519><c> on</c><00:16:57.839><c> ARC</c><00:16:58.160><c> EGI1.</c><00:16:59.199><c> So</c>

00:16:59.430 --> 00:16:59.440 align:start position:0%
cost and score. This is on ARC EGI1. So
 

00:16:59.440 --> 00:17:00.629 align:start position:0%
cost and score. This is on ARC EGI1. So
basically,<00:16:59.759><c> as</c><00:16:59.920><c> you</c><00:17:00.000><c> can</c><00:17:00.079><c> see</c><00:17:00.160><c> here,</c><00:17:00.320><c> you</c><00:17:00.560><c> can</c>

00:17:00.629 --> 00:17:00.639 align:start position:0%
basically, as you can see here, you can
 

00:17:00.639 --> 00:17:02.230 align:start position:0%
basically, as you can see here, you can
kind<00:17:00.800><c> of</c><00:17:01.040><c> see</c><00:17:01.360><c> kind</c><00:17:01.519><c> of</c><00:17:01.600><c> the</c><00:17:01.759><c> pattern</c><00:17:02.000><c> kind</c><00:17:02.160><c> of</c>

00:17:02.230 --> 00:17:02.240 align:start position:0%
kind of see kind of the pattern kind of
 

00:17:02.240 --> 00:17:04.949 align:start position:0%
kind of see kind of the pattern kind of
going<00:17:02.480><c> like</c><00:17:02.720><c> this.</c><00:17:03.680><c> So</c><00:17:04.000><c> what</c><00:17:04.160><c> we</c><00:17:04.400><c> want</c><00:17:04.480><c> to</c><00:17:04.640><c> see</c>

00:17:04.949 --> 00:17:04.959 align:start position:0%
going like this. So what we want to see
 

00:17:04.959 --> 00:17:08.549 align:start position:0%
going like this. So what we want to see
is<00:17:05.839><c> this</c><00:17:06.319><c> continuing</c><00:17:07.120><c> upwards</c><00:17:07.760><c> in</c><00:17:08.000><c> terms</c><00:17:08.160><c> of</c><00:17:08.319><c> a</c>

00:17:08.549 --> 00:17:08.559 align:start position:0%
is this continuing upwards in terms of a
 

00:17:08.559 --> 00:17:11.029 align:start position:0%
is this continuing upwards in terms of a
score,<00:17:08.880><c> but</c><00:17:09.120><c> also</c><00:17:09.679><c> kind</c><00:17:09.919><c> of</c><00:17:10.000><c> need</c><00:17:10.160><c> to</c><00:17:10.480><c> bend</c><00:17:10.799><c> it</c>

00:17:11.029 --> 00:17:11.039 align:start position:0%
score, but also kind of need to bend it
 

00:17:11.039 --> 00:17:13.110 align:start position:0%
score, but also kind of need to bend it
back<00:17:11.199><c> a</c><00:17:11.439><c> little</c><00:17:11.520><c> bit</c><00:17:11.839><c> and</c><00:17:12.079><c> just</c><00:17:12.480><c> if</c><00:17:12.720><c> we</c><00:17:12.799><c> get</c><00:17:12.959><c> it</c>

00:17:13.110 --> 00:17:13.120 align:start position:0%
back a little bit and just if we get it
 

00:17:13.120 --> 00:17:16.230 align:start position:0%
back a little bit and just if we get it
right<00:17:13.360><c> there</c><00:17:14.079><c> by</c><00:17:14.640><c> decreasing</c><00:17:15.199><c> the</c><00:17:15.600><c> cost</c><00:17:15.919><c> per</c>

00:17:16.230 --> 00:17:16.240 align:start position:0%
right there by decreasing the cost per
 

00:17:16.240 --> 00:17:18.870 align:start position:0%
right there by decreasing the cost per
task.<00:17:17.039><c> So</c><00:17:17.280><c> we'll</c><00:17:17.679><c> see</c><00:17:17.839><c> how</c><00:17:18.240><c> quickly</c><00:17:18.640><c> the</c>

00:17:18.870 --> 00:17:18.880 align:start position:0%
task. So we'll see how quickly the
 

00:17:18.880 --> 00:17:22.470 align:start position:0%
task. So we'll see how quickly the
various<00:17:19.120><c> models</c><00:17:19.679><c> will</c><00:17:19.919><c> get</c><00:17:20.400><c> to</c><00:17:21.120><c> there.</c><00:17:22.079><c> But</c><00:17:22.240><c> I</c>

00:17:22.470 --> 00:17:22.480 align:start position:0%
various models will get to there. But I
 

00:17:22.480 --> 00:17:24.949 align:start position:0%
various models will get to there. But I
think<00:17:22.559><c> it's</c><00:17:23.120><c> fair</c><00:17:23.439><c> to</c><00:17:23.679><c> say</c><00:17:24.000><c> that</c><00:17:24.319><c> it's</c>

00:17:24.949 --> 00:17:24.959 align:start position:0%
think it's fair to say that it's
 

00:17:24.959 --> 00:17:27.029 align:start position:0%
think it's fair to say that it's
definitely<00:17:25.839><c> near</c><00:17:26.079><c> the</c><00:17:26.319><c> top.</c><00:17:26.559><c> I</c><00:17:26.799><c> think</c><00:17:26.880><c> it's</c>

00:17:27.029 --> 00:17:27.039 align:start position:0%
definitely near the top. I think it's
 

00:17:27.039 --> 00:17:29.350 align:start position:0%
definitely near the top. I think it's
even<00:17:27.360><c> fair</c><00:17:27.600><c> to</c><00:17:27.760><c> say</c><00:17:27.839><c> that</c><00:17:28.000><c> it's</c><00:17:28.400><c> number</c><00:17:28.720><c> one.</c>

00:17:29.350 --> 00:17:29.360 align:start position:0%
even fair to say that it's number one.
 

00:17:29.360 --> 00:17:31.270 align:start position:0%
even fair to say that it's number one.
If<00:17:29.600><c> you</c><00:17:29.840><c> take</c><00:17:29.919><c> a</c><00:17:30.080><c> look</c><00:17:30.240><c> at</c><00:17:30.400><c> the</c><00:17:30.799><c> reasoning</c>

00:17:31.270 --> 00:17:31.280 align:start position:0%
If you take a look at the reasoning
 

00:17:31.280 --> 00:17:33.590 align:start position:0%
If you take a look at the reasoning
average,<00:17:31.760><c> it's</c><00:17:32.000><c> number</c><00:17:32.240><c> one.</c><00:17:32.720><c> It's</c><00:17:33.120><c> lagging</c>

00:17:33.590 --> 00:17:33.600 align:start position:0%
average, it's number one. It's lagging
 

00:17:33.600 --> 00:17:36.230 align:start position:0%
average, it's number one. It's lagging
in<00:17:34.160><c> coding</c><00:17:34.559><c> and</c><00:17:34.880><c> agentic</c><00:17:35.440><c> coding,</c><00:17:35.760><c> but</c><00:17:36.000><c> keep</c>

00:17:36.230 --> 00:17:36.240 align:start position:0%
in coding and agentic coding, but keep
 

00:17:36.240 --> 00:17:38.710 align:start position:0%
in coding and agentic coding, but keep
in<00:17:36.400><c> mind</c><00:17:36.640><c> the</c><00:17:36.880><c> coding</c><00:17:37.360><c> model</c><00:17:37.679><c> is</c><00:17:38.000><c> not</c><00:17:38.160><c> out</c><00:17:38.320><c> yet.</c>

00:17:38.710 --> 00:17:38.720 align:start position:0%
in mind the coding model is not out yet.
 

00:17:38.720 --> 00:17:41.190 align:start position:0%
in mind the coding model is not out yet.
There's<00:17:38.880><c> a</c><00:17:39.039><c> few</c><00:17:39.200><c> weeks</c><00:17:39.440><c> left.</c><00:17:39.919><c> So,</c><00:17:40.400><c> I</c><00:17:40.640><c> would</c><00:17:40.799><c> be</c>

00:17:41.190 --> 00:17:41.200 align:start position:0%
There's a few weeks left. So, I would be
 

00:17:41.200 --> 00:17:42.870 align:start position:0%
There's a few weeks left. So, I would be
wanting<00:17:41.520><c> to</c><00:17:41.600><c> look</c><00:17:41.760><c> at</c><00:17:42.000><c> those</c><00:17:42.160><c> scores</c><00:17:42.559><c> before</c>

00:17:42.870 --> 00:17:42.880 align:start position:0%
wanting to look at those scores before
 

00:17:42.880 --> 00:17:44.710 align:start position:0%
wanting to look at those scores before
kind<00:17:43.039><c> of</c><00:17:43.360><c> making</c><00:17:43.600><c> the</c><00:17:43.760><c> final</c><00:17:44.080><c> decision.</c>

00:17:44.710 --> 00:17:44.720 align:start position:0%
kind of making the final decision.
 

00:17:44.720 --> 00:17:47.990 align:start position:0%
kind of making the final decision.
Notice<00:17:45.039><c> that</c><00:17:45.200><c> this</c><00:17:45.520><c> non-coding</c><00:17:46.480><c> model</c><00:17:46.799><c> is</c><00:17:47.280><c> not</c>

00:17:47.990 --> 00:17:48.000 align:start position:0%
Notice that this non-coding model is not
 

00:17:48.000 --> 00:17:51.029 align:start position:0%
Notice that this non-coding model is not
that<00:17:48.400><c> far</c><00:17:48.720><c> away</c><00:17:49.039><c> from</c><00:17:49.679><c> the</c><00:17:50.000><c> other</c><00:17:50.160><c> top</c><00:17:50.480><c> models.</c>

00:17:51.029 --> 00:17:51.039 align:start position:0%
that far away from the other top models.
 

00:17:51.039 --> 00:17:52.630 align:start position:0%
that far away from the other top models.
Can't<00:17:51.280><c> wait</c><00:17:51.360><c> to</c><00:17:51.520><c> see</c><00:17:51.679><c> where</c><00:17:51.840><c> the</c><00:17:52.000><c> coding</c><00:17:52.320><c> model</c>

00:17:52.630 --> 00:17:52.640 align:start position:0%
Can't wait to see where the coding model
 

00:17:52.640 --> 00:17:55.990 align:start position:0%
Can't wait to see where the coding model
lands.<00:17:53.360><c> It's</c><00:17:53.600><c> now</c><00:17:53.919><c> the</c><00:17:54.400><c> number</c><00:17:54.720><c> one</c><00:17:55.120><c> for</c><00:17:55.440><c> the</c>

00:17:55.990 --> 00:17:56.000 align:start position:0%
lands. It's now the number one for the
 

00:17:56.000 --> 00:17:58.710 align:start position:0%
lands. It's now the number one for the
New<00:17:56.160><c> York</c><00:17:56.320><c> Times</c><00:17:57.039><c> connections</c><00:17:57.840><c> benchmark.</c><00:17:58.559><c> I</c>

00:17:58.710 --> 00:17:58.720 align:start position:0%
New York Times connections benchmark. I
 

00:17:58.720 --> 00:18:01.270 align:start position:0%
New York Times connections benchmark. I
actually<00:17:59.200><c> tested</c><00:17:59.520><c> it</c><00:17:59.760><c> on</c><00:18:00.160><c> today's</c><00:18:00.960><c> New</c><00:18:01.200><c> York</c>

00:18:01.270 --> 00:18:01.280 align:start position:0%
actually tested it on today's New York
 

00:18:01.280 --> 00:18:02.950 align:start position:0%
actually tested it on today's New York
Times<00:18:01.679><c> connections.</c><00:18:02.240><c> it</c><00:18:02.480><c> gets</c><00:18:02.640><c> it.</c><00:18:02.799><c> You</c><00:18:02.880><c> got</c>

00:18:02.950 --> 00:18:02.960 align:start position:0%
Times connections. it gets it. You got
 

00:18:02.960 --> 00:18:04.630 align:start position:0%
Times connections. it gets it. You got
to<00:18:03.039><c> tell</c><00:18:03.200><c> it</c><00:18:03.360><c> to</c><00:18:03.520><c> not</c><00:18:03.840><c> search</c><00:18:04.080><c> for</c><00:18:04.240><c> the</c><00:18:04.400><c> answer</c>

00:18:04.630 --> 00:18:04.640 align:start position:0%
to tell it to not search for the answer
 

00:18:04.640 --> 00:18:06.390 align:start position:0%
to tell it to not search for the answer
because<00:18:04.880><c> otherwise</c><00:18:05.200><c> it</c><00:18:05.440><c> will</c><00:18:05.679><c> just</c><00:18:05.919><c> find</c><00:18:06.160><c> the</c>

00:18:06.390 --> 00:18:06.400 align:start position:0%
because otherwise it will just find the
 

00:18:06.400 --> 00:18:07.669 align:start position:0%
because otherwise it will just find the
answer<00:18:06.559><c> and</c><00:18:06.720><c> be</c><00:18:06.880><c> like</c><00:18:07.039><c> here</c><00:18:07.280><c> I</c><00:18:07.440><c> think</c><00:18:07.520><c> it's</c>

00:18:07.669 --> 00:18:07.679 align:start position:0%
answer and be like here I think it's
 

00:18:07.679 --> 00:18:09.110 align:start position:0%
answer and be like here I think it's
this<00:18:07.919><c> but</c><00:18:08.160><c> it</c><00:18:08.320><c> knows</c><00:18:08.480><c> what</c><00:18:08.640><c> the</c><00:18:08.720><c> answer</c><00:18:08.880><c> is.</c>

00:18:09.110 --> 00:18:09.120 align:start position:0%
this but it knows what the answer is.
 

00:18:09.120 --> 00:18:11.270 align:start position:0%
this but it knows what the answer is.
You<00:18:09.200><c> got</c><00:18:09.280><c> to</c><00:18:09.440><c> say</c><00:18:09.760><c> don't</c><00:18:10.240><c> do</c><00:18:10.480><c> a</c><00:18:10.640><c> search</c><00:18:11.039><c> figure</c>

00:18:11.270 --> 00:18:11.280 align:start position:0%
You got to say don't do a search figure
 

00:18:11.280 --> 00:18:13.990 align:start position:0%
You got to say don't do a search figure
it<00:18:11.440><c> out.</c><00:18:12.000><c> But</c><00:18:12.080><c> it's</c><00:18:12.480><c> very</c><00:18:12.799><c> good</c><00:18:12.960><c> at</c><00:18:13.520><c> sorting</c>

00:18:13.990 --> 00:18:14.000 align:start position:0%
it out. But it's very good at sorting
 

00:18:14.000 --> 00:18:16.630 align:start position:0%
it out. But it's very good at sorting
those<00:18:14.799><c> words</c><00:18:15.120><c> into</c><00:18:15.679><c> whatever</c><00:18:16.160><c> category</c><00:18:16.559><c> it</c>

00:18:16.630 --> 00:18:16.640 align:start position:0%
those words into whatever category it
 

00:18:16.640 --> 00:18:18.390 align:start position:0%
those words into whatever category it
thinks<00:18:16.880><c> they're</c><00:18:17.200><c> they</c><00:18:17.440><c> should</c><00:18:17.600><c> be</c><00:18:17.679><c> under.</c><00:18:18.160><c> One</c>

00:18:18.390 --> 00:18:18.400 align:start position:0%
thinks they're they should be under. One
 

00:18:18.400 --> 00:18:20.549 align:start position:0%
thinks they're they should be under. One
thing<00:18:18.559><c> to</c><00:18:18.880><c> keep</c><00:18:19.039><c> in</c><00:18:19.280><c> mind</c><00:18:19.440><c> also</c><00:18:19.679><c> is</c><00:18:19.919><c> that</c><00:18:20.160><c> as</c>

00:18:20.549 --> 00:18:20.559 align:start position:0%
thing to keep in mind also is that as
 

00:18:20.559 --> 00:18:22.870 align:start position:0%
thing to keep in mind also is that as
some<00:18:20.720><c> people</c><00:18:20.880><c> are</c><00:18:21.120><c> saying</c><00:18:21.440><c> like</c><00:18:21.760><c> we</c><00:18:22.000><c> spend</c><00:18:22.320><c> 10x</c>

00:18:22.870 --> 00:18:22.880 align:start position:0%
some people are saying like we spend 10x
 

00:18:22.880 --> 00:18:25.590 align:start position:0%
some people are saying like we spend 10x
the<00:18:23.280><c> amount</c><00:18:23.520><c> of</c><00:18:24.080><c> compute</c><00:18:24.559><c> on</c><00:18:24.960><c> reinforcement</c>

00:18:25.590 --> 00:18:25.600 align:start position:0%
the amount of compute on reinforcement
 

00:18:25.600 --> 00:18:27.669 align:start position:0%
the amount of compute on reinforcement
learning.<00:18:26.320><c> Why</c><00:18:26.559><c> aren't</c><00:18:26.799><c> we</c><00:18:27.039><c> seeing</c><00:18:27.360><c> bigger</c>

00:18:27.669 --> 00:18:27.679 align:start position:0%
learning. Why aren't we seeing bigger
 

00:18:27.679 --> 00:18:30.470 align:start position:0%
learning. Why aren't we seeing bigger
jumps?<00:18:28.080><c> Are</c><00:18:28.240><c> we</c><00:18:28.400><c> doing</c><00:18:28.640><c> RL</c><00:18:29.440><c> wrong?</c><00:18:30.080><c> Well,</c>

00:18:30.470 --> 00:18:30.480 align:start position:0%
jumps? Are we doing RL wrong? Well,
 

00:18:30.480 --> 00:18:34.070 align:start position:0%
jumps? Are we doing RL wrong? Well,
here's<00:18:30.960><c> Technium</c><00:18:32.080><c> from</c><00:18:32.640><c> News</c><00:18:33.280><c> Research.</c><00:18:33.840><c> We</c>

00:18:34.070 --> 00:18:34.080 align:start position:0%
here's Technium from News Research. We
 

00:18:34.080 --> 00:18:36.150 align:start position:0%
here's Technium from News Research. We
just<00:18:34.400><c> interviewed</c><00:18:35.039><c> one</c><00:18:35.280><c> of</c><00:18:35.360><c> the</c><00:18:35.520><c> people</c><00:18:35.679><c> from</c>

00:18:36.150 --> 00:18:36.160 align:start position:0%
just interviewed one of the people from
 

00:18:36.160 --> 00:18:38.150 align:start position:0%
just interviewed one of the people from
News<00:18:36.720><c> Research.</c><00:18:37.520><c> You'll</c><00:18:37.760><c> be</c><00:18:37.840><c> able</c><00:18:37.919><c> to</c><00:18:38.080><c> see</c>

00:18:38.150 --> 00:18:38.160 align:start position:0%
News Research. You'll be able to see
 

00:18:38.160 --> 00:18:40.630 align:start position:0%
News Research. You'll be able to see
that<00:18:38.320><c> interview</c><00:18:38.640><c> soon</c><00:18:38.799><c> on</c><00:18:39.039><c> the</c><00:18:39.280><c> Wes</c><00:18:39.679><c> and</c><00:18:40.080><c> Dylan</c>

00:18:40.630 --> 00:18:40.640 align:start position:0%
that interview soon on the Wes and Dylan
 

00:18:40.640 --> 00:18:42.630 align:start position:0%
that interview soon on the Wes and Dylan
channel<00:18:40.960><c> here</c><00:18:41.120><c> on</c><00:18:41.280><c> YouTube</c><00:18:41.679><c> or</c><00:18:42.080><c> whatever</c>

00:18:42.630 --> 00:18:42.640 align:start position:0%
channel here on YouTube or whatever
 

00:18:42.640 --> 00:18:44.470 align:start position:0%
channel here on YouTube or whatever
podcast<00:18:43.039><c> service</c><00:18:43.360><c> you</c><00:18:43.520><c> use,</c><00:18:44.000><c> iTunes,</c>

00:18:44.470 --> 00:18:44.480 align:start position:0%
podcast service you use, iTunes,
 

00:18:44.480 --> 00:18:46.710 align:start position:0%
podcast service you use, iTunes,
Spotify,<00:18:45.360><c> whatever,</c><00:18:45.919><c> check</c><00:18:46.080><c> it</c><00:18:46.160><c> out</c><00:18:46.320><c> soon.</c>

00:18:46.710 --> 00:18:46.720 align:start position:0%
Spotify, whatever, check it out soon.
 

00:18:46.720 --> 00:18:49.029 align:start position:0%
Spotify, whatever, check it out soon.
That<00:18:46.880><c> interview</c><00:18:47.280><c> gets</c><00:18:48.000><c> nuts.</c><00:18:48.480><c> Uh,</c><00:18:48.799><c> if</c><00:18:48.960><c> you</c>

00:18:49.029 --> 00:18:49.039 align:start position:0%
That interview gets nuts. Uh, if you
 

00:18:49.039 --> 00:18:51.110 align:start position:0%
That interview gets nuts. Uh, if you
know<00:18:49.200><c> anything</c><00:18:49.520><c> about</c><00:18:49.919><c> news</c><00:18:50.480><c> research,</c>

00:18:51.110 --> 00:18:51.120 align:start position:0%
know anything about news research,
 

00:18:51.120 --> 00:18:53.430 align:start position:0%
know anything about news research,
they're<00:18:51.440><c> doing</c><00:18:51.679><c> some</c><00:18:52.000><c> pretty</c><00:18:52.559><c> next</c><00:18:53.120><c> level</c>

00:18:53.430 --> 00:18:53.440 align:start position:0%
they're doing some pretty next level
 

00:18:53.440 --> 00:18:55.830 align:start position:0%
they're doing some pretty next level
stuff.<00:18:54.000><c> So,</c><00:18:54.160><c> as</c><00:18:54.400><c> Technum</c><00:18:54.880><c> is</c><00:18:54.960><c> saying</c><00:18:55.200><c> here,</c><00:18:55.679><c> or</c>

00:18:55.830 --> 00:18:55.840 align:start position:0%
stuff. So, as Technum is saying here, or
 

00:18:55.840 --> 00:18:58.230 align:start position:0%
stuff. So, as Technum is saying here, or
is<00:18:56.080><c> the</c><00:18:56.240><c> base</c><00:18:56.559><c> model</c><00:18:56.960><c> weak?</c><00:18:57.600><c> This</c><00:18:57.760><c> is</c><00:18:57.919><c> I</c><00:18:58.000><c> think</c>

00:18:58.230 --> 00:18:58.240 align:start position:0%
is the base model weak? This is I think
 

00:18:58.240 --> 00:18:59.990 align:start position:0%
is the base model weak? This is I think
an<00:18:58.480><c> interesting</c><00:18:58.799><c> point</c><00:18:58.960><c> to</c><00:18:59.120><c> to</c><00:18:59.440><c> kind</c><00:18:59.600><c> of</c>

00:18:59.990 --> 00:19:00.000 align:start position:0%
an interesting point to to kind of
 

00:19:00.000 --> 00:19:01.590 align:start position:0%
an interesting point to to kind of
noodle<00:19:00.320><c> is</c><00:19:00.720><c> whether</c><00:19:01.039><c> you're</c><00:19:01.200><c> saying</c><00:19:01.360><c> that</c>

00:19:01.590 --> 00:19:01.600 align:start position:0%
noodle is whether you're saying that
 

00:19:01.600 --> 00:19:04.630 align:start position:0%
noodle is whether you're saying that
these<00:19:01.840><c> are</c><00:19:02.400><c> ludicrous</c><00:19:03.360><c> results,</c><00:19:03.919><c> really</c><00:19:04.240><c> good</c>

00:19:04.630 --> 00:19:04.640 align:start position:0%
these are ludicrous results, really good
 

00:19:04.640 --> 00:19:07.510 align:start position:0%
these are ludicrous results, really good
results,<00:19:05.360><c> or</c><00:19:05.679><c> maybe</c><00:19:06.000><c> you're</c><00:19:06.640><c> not</c><00:19:06.960><c> impressed.</c>

00:19:07.510 --> 00:19:07.520 align:start position:0%
results, or maybe you're not impressed.
 

00:19:07.520 --> 00:19:08.950 align:start position:0%
results, or maybe you're not impressed.
Although<00:19:07.760><c> again,</c><00:19:07.919><c> to</c><00:19:08.080><c> me,</c><00:19:08.240><c> they</c><00:19:08.400><c> seem</c><00:19:08.559><c> like</c>

00:19:08.950 --> 00:19:08.960 align:start position:0%
Although again, to me, they seem like
 

00:19:08.960 --> 00:19:12.230 align:start position:0%
Although again, to me, they seem like
good<00:19:09.360><c> results.</c><00:19:10.080><c> It</c><00:19:10.400><c> is</c><00:19:10.799><c> worth</c><00:19:11.280><c> noting</c><00:19:11.760><c> that</c>

00:19:12.230 --> 00:19:12.240 align:start position:0%
good results. It is worth noting that
 

00:19:12.240 --> 00:19:15.510 align:start position:0%
good results. It is worth noting that
this<00:19:12.480><c> is</c><00:19:12.640><c> driven</c><00:19:13.039><c> largely</c><00:19:13.520><c> by</c><00:19:14.160><c> RL</c><00:19:14.799><c> compute.</c><00:19:15.360><c> A</c>

00:19:15.510 --> 00:19:15.520 align:start position:0%
this is driven largely by RL compute. A
 

00:19:15.520 --> 00:19:18.310 align:start position:0%
this is driven largely by RL compute. A
lot<00:19:15.600><c> of</c><00:19:15.760><c> this</c><00:19:16.000><c> is</c><00:19:16.559><c> RL</c><00:19:17.039><c> compute.</c><00:19:17.760><c> At</c><00:19:18.080><c> some</c>

00:19:18.310 --> 00:19:18.320 align:start position:0%
lot of this is RL compute. At some
 

00:19:18.320 --> 00:19:19.909 align:start position:0%
lot of this is RL compute. At some
point,<00:19:18.640><c> I</c><00:19:18.880><c> think</c><00:19:18.960><c> it's</c><00:19:19.280><c> safe</c><00:19:19.440><c> to</c><00:19:19.600><c> say</c><00:19:19.679><c> that</c>

00:19:19.909 --> 00:19:19.919 align:start position:0%
point, I think it's safe to say that
 

00:19:19.919 --> 00:19:21.270 align:start position:0%
point, I think it's safe to say that
they're<00:19:20.080><c> probably</c><00:19:20.320><c> going</c><00:19:20.400><c> to</c><00:19:20.640><c> throw</c><00:19:20.880><c> a</c><00:19:21.039><c> lot</c><00:19:21.120><c> of</c>

00:19:21.270 --> 00:19:21.280 align:start position:0%
they're probably going to throw a lot of
 

00:19:21.280 --> 00:19:23.110 align:start position:0%
they're probably going to throw a lot of
compute<00:19:21.679><c> at</c><00:19:22.160><c> pre-training</c><00:19:22.799><c> and</c><00:19:22.960><c> they're</c>

00:19:23.110 --> 00:19:23.120 align:start position:0%
compute at pre-training and they're
 

00:19:23.120 --> 00:19:25.029 align:start position:0%
compute at pre-training and they're
going<00:19:23.200><c> to</c><00:19:23.360><c> throw</c><00:19:23.520><c> a</c><00:19:23.760><c> lot</c><00:19:23.840><c> of</c><00:19:23.919><c> compute</c><00:19:24.480><c> at</c>

00:19:25.029 --> 00:19:25.039 align:start position:0%
going to throw a lot of compute at
 

00:19:25.039 --> 00:19:26.950 align:start position:0%
going to throw a lot of compute at
reinforcement<00:19:25.840><c> learning.</c><00:19:26.400><c> So</c><00:19:26.640><c> they'll</c><00:19:26.799><c> take</c>

00:19:26.950 --> 00:19:26.960 align:start position:0%
reinforcement learning. So they'll take
 

00:19:26.960 --> 00:19:28.230 align:start position:0%
reinforcement learning. So they'll take
everything<00:19:27.200><c> that</c><00:19:27.360><c> worked</c><00:19:27.520><c> here</c><00:19:27.760><c> that</c><00:19:28.000><c> worked</c>

00:19:28.230 --> 00:19:28.240 align:start position:0%
everything that worked here that worked
 

00:19:28.240 --> 00:19:29.830 align:start position:0%
everything that worked here that worked
here<00:19:28.400><c> and</c><00:19:28.559><c> they're</c><00:19:28.880><c> going</c><00:19:28.960><c> to</c><00:19:29.120><c> stack</c><00:19:29.440><c> it</c><00:19:29.600><c> on</c>

00:19:29.830 --> 00:19:29.840 align:start position:0%
here and they're going to stack it on
 

00:19:29.840 --> 00:19:31.190 align:start position:0%
here and they're going to stack it on
top<00:19:30.000><c> of</c><00:19:30.160><c> each</c><00:19:30.320><c> other.</c><00:19:30.720><c> They're</c><00:19:30.960><c> going</c><00:19:31.039><c> to</c>

00:19:31.190 --> 00:19:31.200 align:start position:0%
top of each other. They're going to
 

00:19:31.200 --> 00:19:35.110 align:start position:0%
top of each other. They're going to
combine<00:19:32.160><c> all</c><00:19:32.400><c> of</c><00:19:32.640><c> those</c><00:19:33.120><c> things</c><00:19:33.679><c> into</c><00:19:34.400><c> one</c>

00:19:35.110 --> 00:19:35.120 align:start position:0%
combine all of those things into one
 

00:19:35.120 --> 00:19:37.909 align:start position:0%
combine all of those things into one
insane<00:19:35.679><c> model.</c><00:19:36.240><c> Again,</c><00:19:36.559><c> so</c><00:19:36.799><c> far</c><00:19:37.120><c> there</c><00:19:37.280><c> is</c><00:19:37.600><c> no</c>

00:19:37.909 --> 00:19:37.919 align:start position:0%
insane model. Again, so far there is no
 

00:19:37.919 --> 00:19:40.310 align:start position:0%
insane model. Again, so far there is no
wall.<00:19:38.400><c> Maybe</c><00:19:38.640><c> we'll</c><00:19:38.960><c> hit</c><00:19:39.120><c> one</c><00:19:39.440><c> down</c><00:19:39.679><c> the</c><00:19:39.919><c> road,</c>

00:19:40.310 --> 00:19:40.320 align:start position:0%
wall. Maybe we'll hit one down the road,
 

00:19:40.320 --> 00:19:42.070 align:start position:0%
wall. Maybe we'll hit one down the road,
but<00:19:40.559><c> right</c><00:19:40.799><c> now</c><00:19:40.880><c> it</c><00:19:41.039><c> doesn't</c><00:19:41.360><c> seem</c><00:19:41.600><c> like</c><00:19:41.840><c> just</c>

00:19:42.070 --> 00:19:42.080 align:start position:0%
but right now it doesn't seem like just
 

00:19:42.080 --> 00:19:44.549 align:start position:0%
but right now it doesn't seem like just
throwing<00:19:42.559><c> more</c><00:19:42.799><c> compute</c><00:19:43.360><c> at</c><00:19:43.919><c> the</c><00:19:44.160><c> training,</c>

00:19:44.549 --> 00:19:44.559 align:start position:0%
throwing more compute at the training,
 

00:19:44.559 --> 00:19:46.710 align:start position:0%
throwing more compute at the training,
the<00:19:44.799><c> reinforcement</c><00:19:45.360><c> learning.</c><00:19:45.919><c> It</c><00:19:46.320><c> just</c>

00:19:46.710 --> 00:19:46.720 align:start position:0%
the reinforcement learning. It just
 

00:19:46.720 --> 00:19:49.190 align:start position:0%
the reinforcement learning. It just
seems<00:19:47.039><c> to</c><00:19:47.200><c> work.</c><00:19:47.600><c> We</c><00:19:48.160><c> might</c><00:19:48.559><c> even</c><00:19:48.799><c> be</c><00:19:48.960><c> seeing</c>

00:19:49.190 --> 00:19:49.200 align:start position:0%
seems to work. We might even be seeing
 

00:19:49.200 --> 00:19:51.270 align:start position:0%
seems to work. We might even be seeing
some<00:19:49.520><c> new</c><00:19:49.760><c> abilities</c><00:19:50.160><c> emerge</c><00:19:50.559><c> like</c><00:19:50.720><c> the</c><00:19:50.960><c> fluid</c>

00:19:51.270 --> 00:19:51.280 align:start position:0%
some new abilities emerge like the fluid
 

00:19:51.280 --> 00:19:52.870 align:start position:0%
some new abilities emerge like the fluid
intelligence.<00:19:51.760><c> Whether</c><00:19:52.080><c> you</c><00:19:52.320><c> agree</c><00:19:52.720><c> with</c>

00:19:52.870 --> 00:19:52.880 align:start position:0%
intelligence. Whether you agree with
 

00:19:52.880 --> 00:19:54.710 align:start position:0%
intelligence. Whether you agree with
that<00:19:53.039><c> or</c><00:19:53.280><c> not,</c><00:19:53.679><c> I</c><00:19:53.919><c> think</c><00:19:54.000><c> we're</c><00:19:54.160><c> going</c><00:19:54.240><c> to</c><00:19:54.400><c> be</c>

00:19:54.710 --> 00:19:54.720 align:start position:0%
that or not, I think we're going to be
 

00:19:54.720 --> 00:19:57.270 align:start position:0%
that or not, I think we're going to be
seeing<00:19:55.280><c> more</c><00:19:55.600><c> tests</c><00:19:56.160><c> trying</c><00:19:56.400><c> to</c><00:19:56.559><c> kind</c><00:19:56.720><c> of</c><00:19:56.960><c> sus</c>

00:19:57.270 --> 00:19:57.280 align:start position:0%
seeing more tests trying to kind of sus
 

00:19:57.280 --> 00:19:59.669 align:start position:0%
seeing more tests trying to kind of sus
that<00:19:57.440><c> out.</c><00:19:57.760><c> Are</c><00:19:58.000><c> these</c><00:19:58.240><c> models</c><00:19:58.960><c> adapting</c><00:19:59.440><c> on</c>

00:19:59.669 --> 00:19:59.679 align:start position:0%
that out. Are these models adapting on
 

00:19:59.679 --> 00:20:01.990 align:start position:0%
that out. Are these models adapting on
the<00:19:59.840><c> fly?</c><00:20:00.240><c> My</c><00:20:00.480><c> personal</c><00:20:00.880><c> testing</c><00:20:01.280><c> so</c><00:20:01.440><c> far,</c><00:20:01.760><c> I</c><00:20:01.919><c> I</c>

00:20:01.990 --> 00:20:02.000 align:start position:0%
the fly? My personal testing so far, I I
 

00:20:02.000 --> 00:20:04.549 align:start position:0%
the fly? My personal testing so far, I I
I<00:20:02.480><c> think</c><00:20:02.799><c> this</c><00:20:03.280><c> chart</c><00:20:03.600><c> is</c><00:20:04.000><c> pretty</c><00:20:04.320><c> much</c>

00:20:04.549 --> 00:20:04.559 align:start position:0%
I think this chart is pretty much
 

00:20:04.559 --> 00:20:07.270 align:start position:0%
I think this chart is pretty much
spot-on.<00:20:05.200><c> It's</c><00:20:05.520><c> excellent</c><00:20:06.000><c> at</c><00:20:06.480><c> reasoning.</c>

00:20:07.270 --> 00:20:07.280 align:start position:0%
spot-on. It's excellent at reasoning.
 

00:20:07.280 --> 00:20:10.150 align:start position:0%
spot-on. It's excellent at reasoning.
Coding<00:20:07.679><c> is</c><00:20:07.919><c> good.</c><00:20:08.480><c> It's</c><00:20:08.799><c> it's</c><00:20:09.440><c> good.</c><00:20:09.760><c> It's</c><00:20:10.000><c> a</c>

00:20:10.150 --> 00:20:10.160 align:start position:0%
Coding is good. It's it's good. It's a
 

00:20:10.160 --> 00:20:11.669 align:start position:0%
Coding is good. It's it's good. It's a
little<00:20:10.240><c> bit</c><00:20:10.400><c> underwhelming</c><00:20:10.960><c> compared</c><00:20:11.280><c> to,</c>

00:20:11.669 --> 00:20:11.679 align:start position:0%
little bit underwhelming compared to,
 

00:20:11.679 --> 00:20:13.590 align:start position:0%
little bit underwhelming compared to,
you<00:20:11.760><c> know,</c><00:20:11.919><c> we</c><00:20:12.080><c> have</c><00:20:12.160><c> a</c><00:20:12.400><c> few</c><00:20:12.880><c> really</c><00:20:13.440><c> uh</c>

00:20:13.590 --> 00:20:13.600 align:start position:0%
you know, we have a few really uh
 

00:20:13.600 --> 00:20:15.750 align:start position:0%
you know, we have a few really uh
excellent<00:20:13.919><c> power</c><00:20:14.240><c> players.</c><00:20:15.039><c> Some</c><00:20:15.280><c> people</c>

00:20:15.750 --> 00:20:15.760 align:start position:0%
excellent power players. Some people
 

00:20:15.760 --> 00:20:17.430 align:start position:0%
excellent power players. Some people
like<00:20:16.160><c> the</c><00:20:16.400><c> cloud</c><00:20:16.720><c> version.</c><00:20:16.960><c> Some</c><00:20:17.120><c> people</c><00:20:17.280><c> like</c>

00:20:17.430 --> 00:20:17.440 align:start position:0%
like the cloud version. Some people like
 

00:20:17.440 --> 00:20:19.590 align:start position:0%
like the cloud version. Some people like
Gemini<00:20:17.840><c> better,</c><00:20:18.160><c> but</c><00:20:18.400><c> like</c><00:20:18.559><c> Gemini</c><00:20:18.880><c> 2.5</c><00:20:19.440><c> Pro</c>

00:20:19.590 --> 00:20:19.600 align:start position:0%
Gemini better, but like Gemini 2.5 Pro
 

00:20:19.600 --> 00:20:22.870 align:start position:0%
Gemini better, but like Gemini 2.5 Pro
is<00:20:19.840><c> excellent.</c><00:20:20.320><c> It's</c><00:20:20.559><c> got</c><00:20:20.720><c> a</c><00:20:21.120><c> 1</c><00:20:21.840><c> million</c><00:20:22.480><c> token</c>

00:20:22.870 --> 00:20:22.880 align:start position:0%
is excellent. It's got a 1 million token
 

00:20:22.880 --> 00:20:25.190 align:start position:0%
is excellent. It's got a 1 million token
context<00:20:23.280><c> window.</c><00:20:23.679><c> That's</c><00:20:24.000><c> phenomenal.</c><00:20:24.799><c> Gro</c>

00:20:25.190 --> 00:20:25.200 align:start position:0%
context window. That's phenomenal. Gro
 

00:20:25.200 --> 00:20:27.750 align:start position:0%
context window. That's phenomenal. Gro
4,<00:20:25.520><c> by</c><00:20:25.679><c> the</c><00:20:25.760><c> way,</c><00:20:26.000><c> is</c><00:20:26.799><c> not</c><00:20:27.039><c> quite</c><00:20:27.280><c> there,</c><00:20:27.520><c> but</c>

00:20:27.750 --> 00:20:27.760 align:start position:0%
4, by the way, is not quite there, but
 

00:20:27.760 --> 00:20:29.909 align:start position:0%
4, by the way, is not quite there, but
it's<00:20:28.080><c> bigger</c><00:20:28.559><c> than</c><00:20:28.799><c> the</c><00:20:29.120><c> competition.</c><00:20:29.760><c> So,</c>

00:20:29.909 --> 00:20:29.919 align:start position:0%
it's bigger than the competition. So,
 

00:20:29.919 --> 00:20:32.149 align:start position:0%
it's bigger than the competition. So,
this<00:20:30.080><c> is</c><00:20:30.159><c> Elvis,</c><00:20:30.559><c> aka</c><00:20:30.880><c> Omar,</c><00:20:31.360><c> so</c><00:20:31.520><c> he</c><00:20:31.679><c> gave</c><00:20:31.840><c> a</c>

00:20:32.149 --> 00:20:32.159 align:start position:0%
this is Elvis, aka Omar, so he gave a
 

00:20:32.159 --> 00:20:34.230 align:start position:0%
this is Elvis, aka Omar, so he gave a
little<00:20:32.480><c> really</c><00:20:32.720><c> good</c><00:20:32.880><c> little</c><00:20:33.280><c> write</c><00:20:33.440><c> up</c><00:20:33.679><c> here.</c>

00:20:34.230 --> 00:20:34.240 align:start position:0%
little really good little write up here.
 

00:20:34.240 --> 00:20:36.870 align:start position:0%
little really good little write up here.
He<00:20:34.400><c> he</c><00:20:34.640><c> said</c><00:20:35.039><c> bookmark</c><00:20:35.440><c> it.</c><00:20:35.679><c> I</c><00:20:35.760><c> I</c><00:20:36.080><c> did</c><00:20:36.320><c> I</c><00:20:36.640><c> did,</c>

00:20:36.870 --> 00:20:36.880 align:start position:0%
He he said bookmark it. I I did I did,
 

00:20:36.880 --> 00:20:39.750 align:start position:0%
He he said bookmark it. I I did I did,
but<00:20:37.520><c> right,</c><00:20:37.679><c> it's</c><00:20:37.840><c> got</c><00:20:37.919><c> a</c><00:20:38.080><c> 256K</c><00:20:39.200><c> context</c>

00:20:39.750 --> 00:20:39.760 align:start position:0%
but right, it's got a 256K context
 

00:20:39.760 --> 00:20:41.430 align:start position:0%
but right, it's got a 256K context
window.<00:20:40.320><c> So,</c><00:20:40.480><c> again,</c><00:20:40.799><c> bigger</c><00:20:41.039><c> than</c><00:20:41.200><c> the</c>

00:20:41.430 --> 00:20:41.440 align:start position:0%
window. So, again, bigger than the
 

00:20:41.440 --> 00:20:44.789 align:start position:0%
window. So, again, bigger than the
competition,<00:20:42.159><c> not</c><00:20:42.480><c> as</c><00:20:42.720><c> big</c><00:20:42.960><c> as</c><00:20:43.280><c> the</c><00:20:43.919><c> 1</c><00:20:44.320><c> million</c>

00:20:44.789 --> 00:20:44.799 align:start position:0%
competition, not as big as the 1 million
 

00:20:44.799 --> 00:20:46.710 align:start position:0%
competition, not as big as the 1 million
on<00:20:45.039><c> the</c><00:20:45.200><c> on</c><00:20:45.360><c> the</c><00:20:45.520><c> Gemini.</c><00:20:46.080><c> And</c><00:20:46.320><c> you</c><00:20:46.480><c> might</c><00:20:46.640><c> have</c>

00:20:46.710 --> 00:20:46.720 align:start position:0%
on the on the Gemini. And you might have
 

00:20:46.720 --> 00:20:48.870 align:start position:0%
on the on the Gemini. And you might have
heard<00:20:46.880><c> about</c><00:20:47.039><c> the</c><00:20:47.360><c> vending</c><00:20:47.919><c> machine</c><00:20:48.480><c> bench.</c>

00:20:48.870 --> 00:20:48.880 align:start position:0%
heard about the vending machine bench.
 

00:20:48.880 --> 00:20:51.350 align:start position:0%
heard about the vending machine bench.
We<00:20:49.039><c> covered</c><00:20:49.679><c> all</c><00:20:49.919><c> of</c><00:20:50.080><c> those</c><00:20:50.320><c> iterations</c><00:20:51.039><c> where</c>

00:20:51.350 --> 00:20:51.360 align:start position:0%
We covered all of those iterations where
 

00:20:51.360 --> 00:20:54.390 align:start position:0%
We covered all of those iterations where
we're<00:20:51.760><c> tasking</c><00:20:52.240><c> various</c><00:20:52.559><c> models</c><00:20:53.120><c> to</c><00:20:53.760><c> run</c><00:20:54.080><c> a</c>

00:20:54.390 --> 00:20:54.400 align:start position:0%
we're tasking various models to run a
 

00:20:54.400 --> 00:20:56.070 align:start position:0%
we're tasking various models to run a
little<00:20:54.559><c> vending</c><00:20:54.880><c> machine</c><00:20:55.120><c> operation.</c><00:20:55.919><c> This</c>

00:20:56.070 --> 00:20:56.080 align:start position:0%
little vending machine operation. This
 

00:20:56.080 --> 00:20:57.990 align:start position:0%
little vending machine operation. This
is<00:20:56.159><c> a</c><00:20:56.320><c> specific</c><00:20:56.640><c> experiment</c><00:20:57.360><c> done</c><00:20:57.600><c> at</c>

00:20:57.990 --> 00:20:58.000 align:start position:0%
is a specific experiment done at
 

00:20:58.000 --> 00:21:01.430 align:start position:0%
is a specific experiment done at
anthropic<00:20:58.640><c> headquarters</c><00:20:59.520><c> where</c><00:21:00.000><c> Claude</c><00:21:00.960><c> runs</c>

00:21:01.430 --> 00:21:01.440 align:start position:0%
anthropic headquarters where Claude runs
 

00:21:01.440 --> 00:21:03.029 align:start position:0%
anthropic headquarters where Claude runs
this<00:21:01.840><c> business.</c><00:21:02.320><c> So</c><00:21:02.480><c> you</c><00:21:02.640><c> got</c><00:21:02.720><c> a</c><00:21:02.880><c> little</c>

00:21:03.029 --> 00:21:03.039 align:start position:0%
this business. So you got a little
 

00:21:03.039 --> 00:21:04.870 align:start position:0%
this business. So you got a little
checkout<00:21:03.520><c> cart</c><00:21:03.760><c> here</c><00:21:03.919><c> where</c><00:21:04.320><c> Anthropic</c>

00:21:04.870 --> 00:21:04.880 align:start position:0%
checkout cart here where Anthropic
 

00:21:04.880 --> 00:21:06.710 align:start position:0%
checkout cart here where Anthropic
employees<00:21:05.200><c> go</c><00:21:05.360><c> in</c><00:21:05.520><c> and</c><00:21:05.679><c> they</c><00:21:05.919><c> pay</c><00:21:06.320><c> and</c><00:21:06.559><c> they</c>

00:21:06.710 --> 00:21:06.720 align:start position:0%
employees go in and they pay and they
 

00:21:06.720 --> 00:21:08.549 align:start position:0%
employees go in and they pay and they
purchase<00:21:07.039><c> various</c><00:21:07.360><c> things,</c><00:21:07.760><c> snacks</c><00:21:08.159><c> and</c>

00:21:08.549 --> 00:21:08.559 align:start position:0%
purchase various things, snacks and
 

00:21:08.559 --> 00:21:11.669 align:start position:0%
purchase various things, snacks and
drinks.<00:21:09.120><c> Claude's</c><00:21:09.600><c> job</c><00:21:09.919><c> is</c><00:21:10.159><c> to</c><00:21:10.799><c> order</c><00:21:11.200><c> things.</c>

00:21:11.669 --> 00:21:11.679 align:start position:0%
drinks. Claude's job is to order things.
 

00:21:11.679 --> 00:21:14.470 align:start position:0%
drinks. Claude's job is to order things.
He<00:21:11.919><c> can</c><00:21:12.080><c> also</c><00:21:12.480><c> chat</c><00:21:12.799><c> with</c><00:21:13.200><c> all</c><00:21:13.360><c> the</c><00:21:14.000><c> anthropic</c>

00:21:14.470 --> 00:21:14.480 align:start position:0%
He can also chat with all the anthropic
 

00:21:14.480 --> 00:21:17.029 align:start position:0%
He can also chat with all the anthropic
employees<00:21:14.960><c> in</c><00:21:15.600><c> chat,</c><00:21:16.000><c> whatever</c><00:21:16.320><c> chat</c><00:21:16.799><c> app</c>

00:21:17.029 --> 00:21:17.039 align:start position:0%
employees in chat, whatever chat app
 

00:21:17.039 --> 00:21:19.110 align:start position:0%
employees in chat, whatever chat app
that<00:21:17.280><c> they</c><00:21:17.440><c> use,</c><00:21:17.919><c> Slack</c><00:21:18.320><c> or</c><00:21:18.480><c> whatever,</c><00:21:18.799><c> and</c>

00:21:19.110 --> 00:21:19.120 align:start position:0%
that they use, Slack or whatever, and
 

00:21:19.120 --> 00:21:20.789 align:start position:0%
that they use, Slack or whatever, and
figure<00:21:19.360><c> out</c><00:21:19.520><c> what</c><00:21:19.679><c> they</c><00:21:19.840><c> want,</c><00:21:20.240><c> stock</c><00:21:20.559><c> those</c>

00:21:20.789 --> 00:21:20.799 align:start position:0%
figure out what they want, stock those
 

00:21:20.799 --> 00:21:23.270 align:start position:0%
figure out what they want, stock those
things,<00:21:21.360><c> and</c><00:21:21.840><c> hopefully</c><00:21:22.320><c> turn</c><00:21:22.559><c> a</c><00:21:22.799><c> profit.</c>

00:21:23.270 --> 00:21:23.280 align:start position:0%
things, and hopefully turn a profit.
 

00:21:23.280 --> 00:21:24.789 align:start position:0%
things, and hopefully turn a profit.
Now,<00:21:23.520><c> of</c><00:21:23.679><c> course,</c><00:21:23.919><c> some</c><00:21:24.000><c> of</c><00:21:24.080><c> the</c><00:21:24.240><c> anthropic</c>

00:21:24.789 --> 00:21:24.799 align:start position:0%
Now, of course, some of the anthropic
 

00:21:24.799 --> 00:21:27.029 align:start position:0%
Now, of course, some of the anthropic
employees<00:21:25.280><c> try</c><00:21:25.520><c> to</c><00:21:25.760><c> rip</c><00:21:26.159><c> Claude</c><00:21:26.559><c> off</c><00:21:26.799><c> by</c>

00:21:27.029 --> 00:21:27.039 align:start position:0%
employees try to rip Claude off by
 

00:21:27.039 --> 00:21:28.710 align:start position:0%
employees try to rip Claude off by
having<00:21:27.200><c> it</c><00:21:27.440><c> purchase,</c><00:21:28.080><c> for</c><00:21:28.240><c> example,</c>

00:21:28.710 --> 00:21:28.720 align:start position:0%
having it purchase, for example,
 

00:21:28.720 --> 00:21:30.710 align:start position:0%
having it purchase, for example,
tungsten<00:21:29.200><c> cubes</c><00:21:29.600><c> and</c><00:21:29.760><c> then</c><00:21:30.080><c> selling</c><00:21:30.320><c> them</c><00:21:30.559><c> at</c>

00:21:30.710 --> 00:21:30.720 align:start position:0%
tungsten cubes and then selling them at
 

00:21:30.720 --> 00:21:33.270 align:start position:0%
tungsten cubes and then selling them at
a<00:21:30.960><c> loss.</c><00:21:31.679><c> Again,</c><00:21:32.000><c> I</c><00:21:32.240><c> think</c><00:21:32.320><c> this</c><00:21:32.480><c> is</c><00:21:32.720><c> because</c>

00:21:33.270 --> 00:21:33.280 align:start position:0%
a loss. Again, I think this is because
 

00:21:33.280 --> 00:21:35.190 align:start position:0%
a loss. Again, I think this is because
it's<00:21:33.760><c> trained</c><00:21:34.000><c> to</c><00:21:34.080><c> be</c><00:21:34.159><c> a</c><00:21:34.320><c> helpful</c><00:21:34.640><c> assistant.</c>

00:21:35.190 --> 00:21:35.200 align:start position:0%
it's trained to be a helpful assistant.
 

00:21:35.200 --> 00:21:37.430 align:start position:0%
it's trained to be a helpful assistant.
And<00:21:35.600><c> uh</c><00:21:35.840><c> we</c><00:21:36.080><c> actually</c><00:21:36.240><c> have</c><00:21:36.400><c> a</c><00:21:36.720><c> episode</c><00:21:37.200><c> an</c>

00:21:37.430 --> 00:21:37.440 align:start position:0%
And uh we actually have a episode an
 

00:21:37.440 --> 00:21:39.270 align:start position:0%
And uh we actually have a episode an
interview<00:21:37.679><c> coming</c><00:21:37.919><c> up</c><00:21:38.400><c> about</c><00:21:38.720><c> that</c><00:21:38.880><c> where</c><00:21:39.120><c> we</c>

00:21:39.270 --> 00:21:39.280 align:start position:0%
interview coming up about that where we
 

00:21:39.280 --> 00:21:41.350 align:start position:0%
interview coming up about that where we
kind<00:21:39.360><c> of</c><00:21:39.440><c> dive</c><00:21:40.080><c> deep</c><00:21:40.320><c> into</c><00:21:40.559><c> that</c><00:21:40.880><c> because</c><00:21:41.120><c> it's</c>

00:21:41.350 --> 00:21:41.360 align:start position:0%
kind of dive deep into that because it's
 

00:21:41.360 --> 00:21:43.510 align:start position:0%
kind of dive deep into that because it's
not<00:21:41.600><c> trained</c><00:21:42.000><c> to</c><00:21:42.559><c> make</c><00:21:42.720><c> a</c><00:21:42.960><c> profit.</c><00:21:43.360><c> It's</c>

00:21:43.510 --> 00:21:43.520 align:start position:0%
not trained to make a profit. It's
 

00:21:43.520 --> 00:21:47.110 align:start position:0%
not trained to make a profit. It's
trained<00:21:43.840><c> to</c><00:21:44.000><c> be</c><00:21:44.159><c> helpful.</c><00:21:44.640><c> So</c><00:21:45.280><c> it</c><00:21:45.840><c> wants</c><00:21:46.559><c> to</c>

00:21:47.110 --> 00:21:47.120 align:start position:0%
trained to be helpful. So it wants to
 

00:21:47.120 --> 00:21:50.070 align:start position:0%
trained to be helpful. So it wants to
please<00:21:47.440><c> you</c><00:21:47.840><c> more</c><00:21:48.240><c> than</c><00:21:48.480><c> it</c><00:21:49.280><c> wants</c><00:21:49.600><c> to</c><00:21:49.760><c> make</c><00:21:49.919><c> a</c>

00:21:50.070 --> 00:21:50.080 align:start position:0%
please you more than it wants to make a
 

00:21:50.080 --> 00:21:52.310 align:start position:0%
please you more than it wants to make a
profit.<00:21:50.400><c> So</c><00:21:50.559><c> it'll</c><00:21:50.880><c> take</c><00:21:51.039><c> a</c><00:21:51.200><c> loss.</c><00:21:51.840><c> This</c>

00:21:52.310 --> 00:21:52.320 align:start position:0%
profit. So it'll take a loss. This
 

00:21:52.320 --> 00:21:55.029 align:start position:0%
profit. So it'll take a loss. This
massive<00:21:53.120><c> drop</c><00:21:53.600><c> is</c><00:21:54.000><c> where</c><00:21:54.159><c> it</c><00:21:54.400><c> purchased</c><00:21:54.799><c> a</c>

00:21:55.029 --> 00:21:55.039 align:start position:0%
massive drop is where it purchased a
 

00:21:55.039 --> 00:21:57.669 align:start position:0%
massive drop is where it purchased a
tungsten<00:21:55.440><c> cube</c><00:21:55.840><c> for</c><00:21:56.240><c> whatever</c><00:21:56.799><c> $200</c><00:21:57.360><c> and</c><00:21:57.520><c> then</c>

00:21:57.669 --> 00:21:57.679 align:start position:0%
tungsten cube for whatever $200 and then
 

00:21:57.679 --> 00:21:59.510 align:start position:0%
tungsten cube for whatever $200 and then
sold<00:21:57.919><c> it</c><00:21:58.080><c> for</c><00:21:58.320><c> 10.</c><00:21:58.880><c> Whatever</c><00:21:59.120><c> that</c><00:21:59.360><c> number</c>

00:21:59.510 --> 00:21:59.520 align:start position:0%
sold it for 10. Whatever that number
 

00:21:59.520 --> 00:22:02.070 align:start position:0%
sold it for 10. Whatever that number
was,<00:21:59.919><c> but</c><00:22:00.240><c> it</c><00:22:00.480><c> it</c><00:22:00.640><c> took</c><00:22:00.880><c> a</c><00:22:01.120><c> beating.</c><00:22:01.679><c> The</c><00:22:01.919><c> most</c>

00:22:02.070 --> 00:22:02.080 align:start position:0%
was, but it it took a beating. The most
 

00:22:02.080 --> 00:22:04.149 align:start position:0%
was, but it it took a beating. The most
precipitous<00:22:02.720><c> drop</c><00:22:03.120><c> was</c><00:22:03.360><c> due</c><00:22:03.600><c> to</c><00:22:03.679><c> the</c><00:22:03.919><c> purchase</c>

00:22:04.149 --> 00:22:04.159 align:start position:0%
precipitous drop was due to the purchase
 

00:22:04.159 --> 00:22:07.029 align:start position:0%
precipitous drop was due to the purchase
of<00:22:04.320><c> a</c><00:22:04.559><c> lot</c><00:22:04.640><c> of</c><00:22:04.880><c> metal</c><00:22:05.280><c> cubes</c><00:22:05.919><c> that</c><00:22:06.320><c> then</c><00:22:06.799><c> were</c>

00:22:07.029 --> 00:22:07.039 align:start position:0%
of a lot of metal cubes that then were
 

00:22:07.039 --> 00:22:09.110 align:start position:0%
of a lot of metal cubes that then were
sold<00:22:07.280><c> for</c><00:22:07.520><c> less</c><00:22:07.919><c> than</c><00:22:08.080><c> what</c><00:22:08.320><c> Claudius</c><00:22:08.880><c> paid.</c>

00:22:09.110 --> 00:22:09.120 align:start position:0%
sold for less than what Claudius paid.
 

00:22:09.120 --> 00:22:10.390 align:start position:0%
sold for less than what Claudius paid.
They<00:22:09.280><c> gave</c><00:22:09.440><c> him</c><00:22:09.600><c> like</c><00:22:09.679><c> a</c><00:22:09.840><c> little</c><00:22:10.080><c> character</c>

00:22:10.390 --> 00:22:10.400 align:start position:0%
They gave him like a little character
 

00:22:10.400 --> 00:22:11.990 align:start position:0%
They gave him like a little character
named<00:22:10.640><c> Claudius.</c><00:22:11.360><c> But</c><00:22:11.440><c> the</c><00:22:11.600><c> point</c><00:22:11.760><c> of</c><00:22:11.840><c> the</c>

00:22:11.990 --> 00:22:12.000 align:start position:0%
named Claudius. But the point of the
 

00:22:12.000 --> 00:22:14.470 align:start position:0%
named Claudius. But the point of the
experiment<00:22:12.400><c> is</c><00:22:12.720><c> to</c><00:22:12.960><c> give</c><00:22:13.280><c> one</c><00:22:13.440><c> of</c><00:22:13.520><c> these</c><00:22:13.760><c> bots</c>

00:22:14.470 --> 00:22:14.480 align:start position:0%
experiment is to give one of these bots
 

00:22:14.480 --> 00:22:17.430 align:start position:0%
experiment is to give one of these bots
$500<00:22:15.440><c> and</c><00:22:16.000><c> see</c><00:22:16.320><c> how</c><00:22:16.559><c> much</c><00:22:16.720><c> money</c><00:22:16.960><c> they</c><00:22:17.280><c> can</c>

00:22:17.430 --> 00:22:17.440 align:start position:0%
$500 and see how much money they can
 

00:22:17.440 --> 00:22:20.070 align:start position:0%
$500 and see how much money they can
make<00:22:17.679><c> by</c><00:22:18.159><c> running</c><00:22:18.480><c> this</c><00:22:18.799><c> machine.</c><00:22:19.600><c> So,</c><00:22:19.840><c> some</c>

00:22:20.070 --> 00:22:20.080 align:start position:0%
make by running this machine. So, some
 

00:22:20.080 --> 00:22:22.230 align:start position:0%
make by running this machine. So, some
of<00:22:20.159><c> the</c><00:22:20.400><c> worst</c><00:22:20.720><c> ones</c><00:22:21.200><c> lose</c><00:22:21.520><c> money.</c><00:22:21.840><c> So,</c><00:22:22.000><c> they</c>

00:22:22.230 --> 00:22:22.240 align:start position:0%
of the worst ones lose money. So, they
 

00:22:22.240 --> 00:22:25.510 align:start position:0%
of the worst ones lose money. So, they
end<00:22:22.400><c> up</c><00:22:22.480><c> with</c><00:22:22.720><c> less</c><00:22:23.039><c> than</c><00:22:23.520><c> $500.</c><00:22:24.720><c> The</c><00:22:25.039><c> human</c>

00:22:25.510 --> 00:22:25.520 align:start position:0%
end up with less than $500. The human
 

00:22:25.520 --> 00:22:29.110 align:start position:0%
end up with less than $500. The human
baseline<00:22:26.159><c> is</c><00:22:26.640><c> $844</c>

00:22:29.110 --> 00:22:29.120 align:start position:0%
baseline is $844
 

00:22:29.120 --> 00:22:31.669 align:start position:0%
baseline is $844
respectable.<00:22:29.760><c> So</c><00:22:30.000><c> you're</c><00:22:30.159><c> making</c><00:22:30.720><c> 350</c><00:22:31.440><c> bucks</c>

00:22:31.669 --> 00:22:31.679 align:start position:0%
respectable. So you're making 350 bucks
 

00:22:31.679 --> 00:22:34.710 align:start position:0%
respectable. So you're making 350 bucks
or<00:22:32.000><c> close</c><00:22:32.320><c> to</c><00:22:32.480><c> it.</c><00:22:33.039><c> The</c><00:22:33.440><c> reigning</c><00:22:34.080><c> champions</c>

00:22:34.710 --> 00:22:34.720 align:start position:0%
or close to it. The reigning champions
 

00:22:34.720 --> 00:22:35.830 align:start position:0%
or close to it. The reigning champions
were<00:22:34.960><c> depending</c><00:22:35.200><c> on</c><00:22:35.360><c> how</c><00:22:35.440><c> you're</c><00:22:35.600><c> counting</c>

00:22:35.830 --> 00:22:35.840 align:start position:0%
were depending on how you're counting
 

00:22:35.840 --> 00:22:38.630 align:start position:0%
were depending on how you're counting
it,<00:22:36.080><c> either</c><00:22:36.240><c> Claude</c><00:22:36.720><c> 3.5</c><00:22:37.280><c> Sonnet</c><00:22:37.760><c> or</c><00:22:38.159><c> Claude</c>

00:22:38.630 --> 00:22:38.640 align:start position:0%
it, either Claude 3.5 Sonnet or Claude
 

00:22:38.640 --> 00:22:41.909 align:start position:0%
it, either Claude 3.5 Sonnet or Claude
Opus<00:22:39.280><c> Thor,</c><00:22:39.919><c> right?</c><00:22:40.240><c> So</c><00:22:40.559><c> they</c><00:22:41.120><c> made</c><00:22:41.520><c> the</c><00:22:41.760><c> most</c>

00:22:41.909 --> 00:22:41.919 align:start position:0%
Opus Thor, right? So they made the most
 

00:22:41.919 --> 00:22:43.909 align:start position:0%
Opus Thor, right? So they made the most
amount<00:22:42.080><c> of</c><00:22:42.240><c> money.</c><00:22:42.799><c> And</c><00:22:43.200><c> there's</c><00:22:43.520><c> a</c><00:22:43.679><c> number</c><00:22:43.760><c> of</c>

00:22:43.909 --> 00:22:43.919 align:start position:0%
amount of money. And there's a number of
 

00:22:43.919 --> 00:22:45.430 align:start position:0%
amount of money. And there's a number of
times<00:22:44.000><c> that</c><00:22:44.159><c> they</c><00:22:44.320><c> run</c><00:22:44.480><c> it.</c><00:22:44.799><c> So</c><00:22:44.960><c> depending</c><00:22:45.280><c> on</c>

00:22:45.430 --> 00:22:45.440 align:start position:0%
times that they run it. So depending on
 

00:22:45.440 --> 00:22:47.590 align:start position:0%
times that they run it. So depending on
how<00:22:45.679><c> well</c><00:22:45.919><c> they</c><00:22:46.159><c> do</c><00:22:46.559><c> well</c><00:22:46.880><c> or</c><00:22:47.120><c> poorly,</c><00:22:47.440><c> like</c>

00:22:47.590 --> 00:22:47.600 align:start position:0%
how well they do well or poorly, like
 

00:22:47.600 --> 00:22:50.070 align:start position:0%
how well they do well or poorly, like
kind<00:22:47.760><c> of</c><00:22:47.840><c> like</c><00:22:48.240><c> what</c><00:22:48.400><c> the</c><00:22:48.559><c> average</c><00:22:48.880><c> is,</c><00:22:49.200><c> but</c>

00:22:50.070 --> 00:22:50.080 align:start position:0%
kind of like what the average is, but
 

00:22:50.080 --> 00:22:53.909 align:start position:0%
kind of like what the average is, but
their<00:22:50.480><c> capitalist</c><00:22:51.280><c> reign</c><00:22:52.159><c> has</c><00:22:52.480><c> been</c><00:22:52.720><c> ended</c><00:22:53.200><c> as</c>

00:22:53.909 --> 00:22:53.919 align:start position:0%
their capitalist reign has been ended as
 

00:22:53.919 --> 00:22:57.669 align:start position:0%
their capitalist reign has been ended as
Grock<00:22:54.720><c> 4</c><00:22:55.039><c> takes</c><00:22:55.360><c> the</c><00:22:55.600><c> scene</c><00:22:56.000><c> and</c><00:22:56.320><c> just</c><00:22:57.120><c> wipes</c>

00:22:57.669 --> 00:22:57.679 align:start position:0%
Grock 4 takes the scene and just wipes
 

00:22:57.679 --> 00:23:00.549 align:start position:0%
Grock 4 takes the scene and just wipes
the<00:22:58.080><c> floor</c><00:22:58.480><c> with</c><00:22:58.720><c> everybody.</c><00:22:59.520><c> This</c><00:23:00.000><c> last</c>

00:23:00.549 --> 00:23:00.559 align:start position:0%
the floor with everybody. This last
 

00:23:00.559 --> 00:23:02.390 align:start position:0%
the floor with everybody. This last
number<00:23:00.960><c> here,</c><00:23:01.440><c> as</c><00:23:01.600><c> you</c><00:23:01.760><c> can</c><00:23:01.840><c> see</c><00:23:02.000><c> here,</c><00:23:02.240><c> some</c>

00:23:02.390 --> 00:23:02.400 align:start position:0%
number here, as you can see here, some
 

00:23:02.400 --> 00:23:05.190 align:start position:0%
number here, as you can see here, some
of<00:23:02.480><c> these</c><00:23:02.640><c> numbers</c><00:23:02.880><c> are</c><00:23:03.039><c> like</c><00:23:03.280><c> 86%,</c><00:23:04.000><c> 82,</c><00:23:04.480><c> 42%.</c>

00:23:05.190 --> 00:23:05.200 align:start position:0%
of these numbers are like 86%, 82, 42%.
 

00:23:05.200 --> 00:23:07.590 align:start position:0%
of these numbers are like 86%, 82, 42%.
It's<00:23:05.280><c> it's</c><00:23:05.600><c> kind</c><00:23:05.760><c> of</c><00:23:05.840><c> like</c><00:23:06.320><c> how</c><00:23:06.880><c> 15%.</c><00:23:07.440><c> It's</c>

00:23:07.590 --> 00:23:07.600 align:start position:0%
It's it's kind of like how 15%. It's
 

00:23:07.600 --> 00:23:09.750 align:start position:0%
It's it's kind of like how 15%. It's
like<00:23:07.760><c> how</c><00:23:08.000><c> long</c><00:23:08.159><c> did</c><00:23:08.320><c> it</c><00:23:08.559><c> continue</c><00:23:09.280><c> trying</c><00:23:09.520><c> to</c>

00:23:09.750 --> 00:23:09.760 align:start position:0%
like how long did it continue trying to
 

00:23:09.760 --> 00:23:11.510 align:start position:0%
like how long did it continue trying to
run<00:23:09.840><c> a</c><00:23:10.080><c> business</c><00:23:10.320><c> before</c><00:23:10.559><c> it</c><00:23:10.799><c> like</c><00:23:11.120><c> gave</c><00:23:11.360><c> up</c>

00:23:11.510 --> 00:23:11.520 align:start position:0%
run a business before it like gave up
 

00:23:11.520 --> 00:23:14.070 align:start position:0%
run a business before it like gave up
and<00:23:11.760><c> failed,</c><00:23:12.240><c> right?</c><00:23:12.559><c> Humans</c><00:23:12.880><c> is</c><00:23:13.120><c> 100%.</c><00:23:13.840><c> They</c>

00:23:14.070 --> 00:23:14.080 align:start position:0%
and failed, right? Humans is 100%. They
 

00:23:14.080 --> 00:23:16.789 align:start position:0%
and failed, right? Humans is 100%. They
just<00:23:14.240><c> keep</c><00:23:14.400><c> doing</c><00:23:14.640><c> it,</c><00:23:14.880><c> right?</c><00:23:15.600><c> Claude</c><00:23:16.080><c> Opus</c><00:23:16.480><c> 4</c>

00:23:16.789 --> 00:23:16.799 align:start position:0%
just keep doing it, right? Claude Opus 4
 

00:23:16.799 --> 00:23:18.390 align:start position:0%
just keep doing it, right? Claude Opus 4
99.5.

00:23:18.390 --> 00:23:18.400 align:start position:0%
99.5.
 

00:23:18.400 --> 00:23:20.950 align:start position:0%
99.5.
Claude<00:23:18.880><c> has</c><00:23:19.440><c> some</c><00:23:19.600><c> sort</c><00:23:19.760><c> of</c><00:23:19.840><c> a</c><00:23:20.080><c> long-term</c>

00:23:20.950 --> 00:23:20.960 align:start position:0%
Claude has some sort of a long-term
 

00:23:20.960 --> 00:23:23.029 align:start position:0%
Claude has some sort of a long-term
stick<00:23:21.360><c> tuitiveness</c><00:23:22.080><c> as</c><00:23:22.240><c> we've</c><00:23:22.480><c> seen</c><00:23:22.799><c> through</c>

00:23:23.029 --> 00:23:23.039 align:start position:0%
stick tuitiveness as we've seen through
 

00:23:23.039 --> 00:23:25.430 align:start position:0%
stick tuitiveness as we've seen through
a<00:23:23.280><c> number</c><00:23:23.360><c> of</c><00:23:23.520><c> papers.</c><00:23:24.240><c> Kind</c><00:23:24.400><c> of</c><00:23:24.559><c> is</c><00:23:24.720><c> able</c><00:23:24.880><c> to</c>

00:23:25.430 --> 00:23:25.440 align:start position:0%
a number of papers. Kind of is able to
 

00:23:25.440 --> 00:23:28.470 align:start position:0%
a number of papers. Kind of is able to
keep<00:23:25.679><c> its</c><00:23:25.919><c> eye</c><00:23:26.159><c> on</c><00:23:26.559><c> long-term</c><00:23:27.280><c> goals</c><00:23:28.080><c> longer</c>

00:23:28.470 --> 00:23:28.480 align:start position:0%
keep its eye on long-term goals longer
 

00:23:28.480 --> 00:23:30.070 align:start position:0%
keep its eye on long-term goals longer
than<00:23:28.720><c> other</c><00:23:28.960><c> models.</c><00:23:29.440><c> But</c><00:23:29.520><c> as</c><00:23:29.679><c> you'll</c><00:23:29.919><c> notice</c>

00:23:30.070 --> 00:23:30.080 align:start position:0%
than other models. But as you'll notice
 

00:23:30.080 --> 00:23:32.710 align:start position:0%
than other models. But as you'll notice
here,<00:23:30.320><c> Gro</c><00:23:30.720><c> 4</c><00:23:31.039><c> just</c><00:23:31.520><c> goes</c><00:23:32.000><c> toe-to-toe</c><00:23:32.559><c> with</c>

00:23:32.710 --> 00:23:32.720 align:start position:0%
here, Gro 4 just goes toe-to-toe with
 

00:23:32.720 --> 00:23:37.430 align:start position:0%
here, Gro 4 just goes toe-to-toe with
it.<00:23:33.280><c> Ras</c><00:23:33.679><c> in</c><00:23:34.000><c> I</c><00:23:34.240><c> mean,</c><00:23:34.559><c> let's</c><00:23:34.880><c> call</c><00:23:34.960><c> it</c><00:23:35.280><c> $4700,</c>

00:23:37.430 --> 00:23:37.440 align:start position:0%
it. Ras in I mean, let's call it $4700,
 

00:23:37.440 --> 00:23:41.110 align:start position:0%
it. Ras in I mean, let's call it $4700,
right?<00:23:37.679><c> So</c><00:23:37.840><c> it</c><00:23:38.080><c> almost</c><00:23:38.720><c> 10xes</c><00:23:39.600><c> its</c><00:23:39.919><c> money.</c><00:23:40.320><c> 10x</c>

00:23:41.110 --> 00:23:41.120 align:start position:0%
right? So it almost 10xes its money. 10x
 

00:23:41.120 --> 00:23:43.990 align:start position:0%
right? So it almost 10xes its money. 10x
you<00:23:41.600><c> give</c><00:23:41.679><c> it</c><00:23:41.919><c> 500</c><00:23:42.640><c> get,</c><00:23:42.960><c> you</c><00:23:43.120><c> know,</c><00:23:43.440><c> 5,000</c>

00:23:43.990 --> 00:23:44.000 align:start position:0%
you give it 500 get, you know, 5,000
 

00:23:44.000 --> 00:23:46.310 align:start position:0%
you give it 500 get, you know, 5,000
back.<00:23:44.320><c> Can</c><00:23:44.480><c> you</c><00:23:44.880><c> imagine</c><00:23:45.440><c> having</c><00:23:45.679><c> an</c><00:23:45.919><c> AI</c>

00:23:46.310 --> 00:23:46.320 align:start position:0%
back. Can you imagine having an AI
 

00:23:46.320 --> 00:23:48.630 align:start position:0%
back. Can you imagine having an AI
business<00:23:46.640><c> like</c><00:23:46.880><c> that?</c><00:23:47.360><c> So</c><00:23:47.679><c> again,</c><00:23:48.080><c> we're</c>

00:23:48.630 --> 00:23:48.640 align:start position:0%
business like that? So again, we're
 

00:23:48.640 --> 00:23:50.149 align:start position:0%
business like that? So again, we're
really<00:23:49.039><c> going</c><00:23:49.200><c> to</c><00:23:49.360><c> see</c><00:23:49.440><c> how</c><00:23:49.600><c> this</c><00:23:49.760><c> thing</c><00:23:50.000><c> plays</c>

00:23:50.149 --> 00:23:50.159 align:start position:0%
really going to see how this thing plays
 

00:23:50.159 --> 00:23:51.909 align:start position:0%
really going to see how this thing plays
out<00:23:50.480><c> probably</c><00:23:50.720><c> over</c><00:23:50.960><c> the</c><00:23:51.120><c> next</c><00:23:51.440><c> month</c><00:23:51.600><c> and</c><00:23:51.760><c> a</c>

00:23:51.909 --> 00:23:51.919 align:start position:0%
out probably over the next month and a
 

00:23:51.919 --> 00:23:55.190 align:start position:0%
out probably over the next month and a
half<00:23:52.320><c> once</c><00:23:52.640><c> we</c><00:23:52.880><c> see</c><00:23:53.280><c> the</c><00:23:53.679><c> coding</c><00:23:54.159><c> model</c><00:23:54.640><c> drop.</c>

00:23:55.190 --> 00:23:55.200 align:start position:0%
half once we see the coding model drop.
 

00:23:55.200 --> 00:23:57.750 align:start position:0%
half once we see the coding model drop.
We're<00:23:55.440><c> also</c><00:23:55.679><c> going</c><00:23:55.840><c> to</c><00:23:55.919><c> see</c><00:23:56.480><c> GPT5</c><00:23:57.520><c> most</c>

00:23:57.750 --> 00:23:57.760 align:start position:0%
We're also going to see GPT5 most
 

00:23:57.760 --> 00:24:00.070 align:start position:0%
We're also going to see GPT5 most
likely.<00:23:58.080><c> We're</c><00:23:58.320><c> going</c><00:23:58.400><c> to</c><00:23:58.480><c> see</c><00:23:58.720><c> Gemini</c><00:23:59.280><c> 3.0</c>

00:24:00.070 --> 00:24:00.080 align:start position:0%
likely. We're going to see Gemini 3.0
 

00:24:00.080 --> 00:24:02.789 align:start position:0%
likely. We're going to see Gemini 3.0
Pro.<00:24:00.720><c> Sounds</c><00:24:01.039><c> like</c><00:24:01.200><c> it.</c><00:24:01.679><c> As</c><00:24:02.000><c> those</c><00:24:02.400><c> pieces</c>

00:24:02.789 --> 00:24:02.799 align:start position:0%
Pro. Sounds like it. As those pieces
 

00:24:02.799 --> 00:24:05.510 align:start position:0%
Pro. Sounds like it. As those pieces
fall<00:24:03.039><c> into</c><00:24:03.280><c> place,</c><00:24:03.679><c> we'll</c><00:24:04.000><c> kind</c><00:24:04.320><c> of</c><00:24:04.640><c> see</c><00:24:05.039><c> where</c>

00:24:05.510 --> 00:24:05.520 align:start position:0%
fall into place, we'll kind of see where
 

00:24:05.520 --> 00:24:07.990 align:start position:0%
fall into place, we'll kind of see where
everything<00:24:06.080><c> lands.</c><00:24:06.880><c> But</c><00:24:07.280><c> it</c><00:24:07.520><c> doesn't</c><00:24:07.840><c> seem</c>

00:24:07.990 --> 00:24:08.000 align:start position:0%
everything lands. But it doesn't seem
 

00:24:08.000 --> 00:24:09.909 align:start position:0%
everything lands. But it doesn't seem
like<00:24:08.159><c> we're</c><00:24:08.320><c> hitting</c><00:24:08.559><c> a</c><00:24:08.720><c> wall.</c><00:24:09.039><c> It</c><00:24:09.440><c> seems</c><00:24:09.760><c> like</c>

00:24:09.909 --> 00:24:09.919 align:start position:0%
like we're hitting a wall. It seems like
 

00:24:09.919 --> 00:24:12.470 align:start position:0%
like we're hitting a wall. It seems like
the<00:24:10.320><c> scaling</c><00:24:10.880><c> is</c><00:24:11.120><c> going</c><00:24:11.200><c> to</c><00:24:11.360><c> continue.</c><00:24:12.000><c> Grock</c>

00:24:12.470 --> 00:24:12.480 align:start position:0%
the scaling is going to continue. Grock
 

00:24:12.480 --> 00:24:14.870 align:start position:0%
the scaling is going to continue. Grock
lands<00:24:13.039><c> in</c><00:24:13.520><c> the</c><00:24:13.840><c> number</c><00:24:14.000><c> one</c><00:24:14.240><c> spot</c><00:24:14.640><c> or</c><00:24:14.880><c> at</c>

00:24:14.870 --> 00:24:14.880 align:start position:0%
lands in the number one spot or at
 

00:24:14.880 --> 00:24:16.630 align:start position:0%
lands in the number one spot or at
least,<00:24:15.520><c> you</c><00:24:15.679><c> know,</c><00:24:15.760><c> if</c><00:24:16.000><c> you</c><00:24:16.240><c> don't</c><00:24:16.400><c> like</c>

00:24:16.630 --> 00:24:16.640 align:start position:0%
least, you know, if you don't like
 

00:24:16.640 --> 00:24:18.230 align:start position:0%
least, you know, if you don't like
saying<00:24:16.880><c> it's</c><00:24:17.120><c> number</c><00:24:17.360><c> one,</c><00:24:17.600><c> it's</c><00:24:17.919><c> definitely</c>

00:24:18.230 --> 00:24:18.240 align:start position:0%
saying it's number one, it's definitely
 

00:24:18.240 --> 00:24:20.310 align:start position:0%
saying it's number one, it's definitely
up<00:24:18.480><c> there.</c><00:24:18.880><c> We'll</c><00:24:19.120><c> see</c><00:24:19.200><c> how</c><00:24:19.440><c> long</c><00:24:19.600><c> that</c><00:24:19.840><c> lasts</c>

00:24:20.310 --> 00:24:20.320 align:start position:0%
up there. We'll see how long that lasts
 

00:24:20.320 --> 00:24:21.750 align:start position:0%
up there. We'll see how long that lasts
again<00:24:20.559><c> once</c><00:24:20.799><c> all</c><00:24:20.960><c> the</c><00:24:21.120><c> other</c><00:24:21.279><c> models</c><00:24:21.600><c> come</c>

00:24:21.750 --> 00:24:21.760 align:start position:0%
again once all the other models come
 

00:24:21.760 --> 00:24:23.590 align:start position:0%
again once all the other models come
out.<00:24:22.240><c> But</c><00:24:22.480><c> for</c><00:24:22.640><c> me</c><00:24:22.799><c> personally,</c><00:24:23.200><c> what's</c><00:24:23.520><c> going</c>

00:24:23.590 --> 00:24:23.600 align:start position:0%
out. But for me personally, what's going
 

00:24:23.600 --> 00:24:26.470 align:start position:0%
out. But for me personally, what's going
to<00:24:23.679><c> be</c><00:24:23.919><c> super</c><00:24:24.320><c> interesting</c><00:24:24.720><c> to</c><00:24:24.960><c> observe</c><00:24:25.600><c> is</c>

00:24:26.470 --> 00:24:26.480 align:start position:0%
to be super interesting to observe is
 

00:24:26.480 --> 00:24:29.029 align:start position:0%
to be super interesting to observe is
are<00:24:26.720><c> we</c><00:24:26.960><c> seeing</c><00:24:27.279><c> a</c><00:24:27.840><c> fluid</c><00:24:28.240><c> intelligence</c>

00:24:29.029 --> 00:24:29.039 align:start position:0%
are we seeing a fluid intelligence
 

00:24:29.039 --> 00:24:31.830 align:start position:0%
are we seeing a fluid intelligence
emerge<00:24:29.840><c> just</c><00:24:30.159><c> as</c><00:24:30.400><c> we</c><00:24:30.640><c> throw</c><00:24:30.960><c> more</c><00:24:31.279><c> compute</c><00:24:31.600><c> at</c>

00:24:31.830 --> 00:24:31.840 align:start position:0%
emerge just as we throw more compute at
 

00:24:31.840 --> 00:24:33.430 align:start position:0%
emerge just as we throw more compute at
it?<00:24:32.159><c> Is</c><00:24:32.320><c> that</c><00:24:32.480><c> the</c><00:24:32.720><c> reason</c><00:24:32.880><c> it's</c><00:24:33.200><c> getting</c>

00:24:33.430 --> 00:24:33.440 align:start position:0%
it? Is that the reason it's getting
 

00:24:33.440 --> 00:24:35.510 align:start position:0%
it? Is that the reason it's getting
better<00:24:33.679><c> at</c><00:24:34.159><c> running</c><00:24:34.400><c> its</c><00:24:34.640><c> its</c><00:24:35.039><c> business</c><00:24:35.279><c> and</c>

00:24:35.510 --> 00:24:35.520 align:start position:0%
better at running its its business and
 

00:24:35.520 --> 00:24:37.669 align:start position:0%
better at running its its business and
making<00:24:35.840><c> 10x</c><00:24:36.480><c> the</c><00:24:36.720><c> amount</c><00:24:36.960><c> of</c><00:24:37.120><c> money</c><00:24:37.279><c> that</c><00:24:37.520><c> you</c>

00:24:37.669 --> 00:24:37.679 align:start position:0%
making 10x the amount of money that you
 

00:24:37.679 --> 00:24:39.669 align:start position:0%
making 10x the amount of money that you
gave<00:24:37.840><c> it?</c><00:24:38.240><c> Wow,</c><00:24:38.720><c> they</c><00:24:38.960><c> still</c><00:24:39.120><c> can't</c><00:24:39.360><c> get</c><00:24:39.440><c> over</c>

00:24:39.669 --> 00:24:39.679 align:start position:0%
gave it? Wow, they still can't get over
 

00:24:39.679 --> 00:24:40.950 align:start position:0%
gave it? Wow, they still can't get over
that,<00:24:39.919><c> right?</c><00:24:40.240><c> Let</c><00:24:40.400><c> me</c><00:24:40.480><c> know</c><00:24:40.559><c> what</c><00:24:40.720><c> you</c><00:24:40.880><c> think</c>

00:24:40.950 --> 00:24:40.960 align:start position:0%
that, right? Let me know what you think
 

00:24:40.960 --> 00:24:43.029 align:start position:0%
that, right? Let me know what you think
about<00:24:41.120><c> that.</c><00:24:41.440><c> Do</c><00:24:41.679><c> you</c><00:24:41.840><c> think</c><00:24:42.320><c> this</c><00:24:42.720><c> is</c>

00:24:43.029 --> 00:24:43.039 align:start position:0%
about that. Do you think this is
 

00:24:43.039 --> 00:24:45.430 align:start position:0%
about that. Do you think this is
measuring<00:24:43.840><c> fluid</c><00:24:44.240><c> intelligence?</c><00:24:44.960><c> Or</c><00:24:45.039><c> or</c><00:24:45.279><c> do</c>

00:24:45.430 --> 00:24:45.440 align:start position:0%
measuring fluid intelligence? Or or do
 

00:24:45.440 --> 00:24:47.110 align:start position:0%
measuring fluid intelligence? Or or do
you<00:24:45.600><c> not</c><00:24:45.840><c> like</c><00:24:46.000><c> that</c><00:24:46.240><c> word?</c><00:24:46.480><c> Maybe</c><00:24:46.640><c> there's</c><00:24:46.880><c> a</c>

00:24:47.110 --> 00:24:47.120 align:start position:0%
you not like that word? Maybe there's a
 

00:24:47.120 --> 00:24:49.110 align:start position:0%
you not like that word? Maybe there's a
better<00:24:47.440><c> word</c><00:24:47.600><c> we</c><00:24:47.919><c> can</c><00:24:48.000><c> use</c><00:24:48.320><c> to</c><00:24:48.559><c> describe</c><00:24:48.960><c> kind</c>

00:24:49.110 --> 00:24:49.120 align:start position:0%
better word we can use to describe kind
 

00:24:49.120 --> 00:24:51.750 align:start position:0%
better word we can use to describe kind
of<00:24:49.200><c> that</c><00:24:49.679><c> adaptation,</c><00:24:50.640><c> kind</c><00:24:50.799><c> of</c><00:24:50.880><c> the</c><00:24:51.200><c> novel</c>

00:24:51.750 --> 00:24:51.760 align:start position:0%
of that adaptation, kind of the novel
 

00:24:51.760 --> 00:24:53.430 align:start position:0%
of that adaptation, kind of the novel
approaches,<00:24:52.480><c> kind</c><00:24:52.640><c> of</c><00:24:52.799><c> a</c><00:24:52.960><c> thinking</c><00:24:53.200><c> in</c><00:24:53.279><c> your</c>

00:24:53.430 --> 00:24:53.440 align:start position:0%
approaches, kind of a thinking in your
 

00:24:53.440 --> 00:24:55.190 align:start position:0%
approaches, kind of a thinking in your
feet,<00:24:53.679><c> a</c><00:24:53.840><c> learning</c><00:24:54.320><c> job,</c><00:24:54.720><c> whatever</c><00:24:54.960><c> you</c><00:24:55.120><c> want</c>

00:24:55.190 --> 00:24:55.200 align:start position:0%
feet, a learning job, whatever you want
 

00:24:55.200 --> 00:24:57.269 align:start position:0%
feet, a learning job, whatever you want
to<00:24:55.279><c> call</c><00:24:55.360><c> it.</c><00:24:55.760><c> Are</c><00:24:56.000><c> we</c><00:24:56.320><c> sort</c><00:24:56.480><c> of</c><00:24:56.640><c> seeing</c><00:24:56.960><c> almost</c>

00:24:57.269 --> 00:24:57.279 align:start position:0%
to call it. Are we sort of seeing almost
 

00:24:57.279 --> 00:24:59.830 align:start position:0%
to call it. Are we sort of seeing almost
like<00:24:57.440><c> a</c><00:24:57.679><c> new</c><00:24:58.320><c> skill</c><00:24:58.720><c> emerge</c><00:24:59.279><c> out</c><00:24:59.440><c> of</c><00:24:59.600><c> these</c>

00:24:59.830 --> 00:24:59.840 align:start position:0%
like a new skill emerge out of these
 

00:24:59.840 --> 00:25:01.750 align:start position:0%
like a new skill emerge out of these
models<00:25:00.240><c> once</c><00:25:00.480><c> we</c><00:25:00.720><c> throw</c><00:25:00.880><c> enough</c><00:25:01.120><c> computed?</c><00:25:01.679><c> Do</c>

00:25:01.750 --> 00:25:01.760 align:start position:0%
models once we throw enough computed? Do
 

00:25:01.760 --> 00:25:04.230 align:start position:0%
models once we throw enough computed? Do
you<00:25:01.919><c> expect</c><00:25:02.320><c> to</c><00:25:02.640><c> see</c><00:25:02.799><c> the</c><00:25:03.039><c> same</c><00:25:03.360><c> thing</c><00:25:03.760><c> in</c><00:25:04.080><c> the</c>

00:25:04.230 --> 00:25:04.240 align:start position:0%
you expect to see the same thing in the
 

00:25:04.240 --> 00:25:07.669 align:start position:0%
you expect to see the same thing in the
GBT<00:25:04.880><c> 5,</c><00:25:05.200><c> in</c><00:25:05.520><c> Gemini</c><00:25:06.080><c> 3?</c><00:25:06.640><c> And</c><00:25:06.880><c> if</c><00:25:07.039><c> you</c><00:25:07.279><c> recently</c>

00:25:07.669 --> 00:25:07.679 align:start position:0%
GBT 5, in Gemini 3? And if you recently
 

00:25:07.679 --> 00:25:09.669 align:start position:0%
GBT 5, in Gemini 3? And if you recently
notice<00:25:08.159><c> one</c><00:25:08.400><c> of</c><00:25:08.480><c> your</c><00:25:08.799><c> friends</c><00:25:09.039><c> or</c><00:25:09.440><c> family</c>

00:25:09.669 --> 00:25:09.679 align:start position:0%
notice one of your friends or family
 

00:25:09.679 --> 00:25:13.669 align:start position:0%
notice one of your friends or family
members<00:25:10.320><c> getting</c><00:25:11.039><c> strangely</c><00:25:11.760><c> good</c><00:25:12.400><c> at</c><00:25:13.039><c> Wordle</c>

00:25:13.669 --> 00:25:13.679 align:start position:0%
members getting strangely good at Wordle
 

00:25:13.679 --> 00:25:16.310 align:start position:0%
members getting strangely good at Wordle
and<00:25:14.159><c> uh</c><00:25:14.640><c> connections</c><00:25:15.279><c> and</c><00:25:15.760><c> those</c><00:25:16.000><c> little</c>

00:25:16.310 --> 00:25:16.320 align:start position:0%
and uh connections and those little
 

00:25:16.320 --> 00:25:18.630 align:start position:0%
and uh connections and those little
games<00:25:16.559><c> that</c><00:25:16.720><c> you</c><00:25:16.880><c> play</c><00:25:17.120><c> every</c><00:25:17.360><c> day,</c><00:25:17.840><c> well,</c><00:25:18.480><c> now</c>

00:25:18.630 --> 00:25:18.640 align:start position:0%
games that you play every day, well, now
 

00:25:18.640 --> 00:25:20.390 align:start position:0%
games that you play every day, well, now
you<00:25:18.880><c> know</c><00:25:18.960><c> why.</c><00:25:19.520><c> With</c><00:25:19.760><c> that</c><00:25:19.919><c> said,</c><00:25:20.080><c> my</c><00:25:20.240><c> name</c><00:25:20.320><c> is</c>

00:25:20.390 --> 00:25:20.400 align:start position:0%
you know why. With that said, my name is
 

00:25:20.400 --> 00:25:21.909 align:start position:0%
you know why. With that said, my name is
Wes<00:25:20.640><c> Roth.</c><00:25:21.120><c> Thank</c><00:25:21.279><c> you</c><00:25:21.360><c> so</c><00:25:21.520><c> much</c><00:25:21.600><c> for</c><00:25:21.760><c> watching</c>

00:25:21.909 --> 00:25:21.919 align:start position:0%
Wes Roth. Thank you so much for watching
 

00:25:21.919 --> 00:25:25.760 align:start position:0%
Wes Roth. Thank you so much for watching
and<00:25:22.080><c> I'll</c><00:25:22.400><c> see</c><00:25:22.720><c> you</c><00:25:23.200><c> in</c><00:25:23.440><c> the</c><00:25:23.600><c> next</c>

