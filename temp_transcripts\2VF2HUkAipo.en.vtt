WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.189 align:start position:0%
 
listen<00:00:00.240><c> now</c><00:00:00.359><c> that</c><00:00:00.390><c> you</c><00:00:00.690><c> know</c><00:00:00.810><c> a</c><00:00:00.840><c> little</c><00:00:01.079><c> bit</c>

00:00:01.189 --> 00:00:01.199 align:start position:0%
listen now that you know a little bit
 

00:00:01.199 --> 00:00:03.200 align:start position:0%
listen now that you know a little bit
about<00:00:01.290><c> what</c><00:00:01.709><c> we're</c><00:00:01.890><c> trying</c><00:00:02.100><c> to</c><00:00:02.250><c> build</c><00:00:02.429><c> here</c><00:00:02.669><c> we</c>

00:00:03.200 --> 00:00:03.210 align:start position:0%
about what we're trying to build here we
 

00:00:03.210 --> 00:00:05.150 align:start position:0%
about what we're trying to build here we
can<00:00:03.360><c> actually</c><00:00:03.510><c> go</c><00:00:03.959><c> and</c><00:00:04.230><c> start</c><00:00:04.500><c> diving</c><00:00:04.770><c> into</c>

00:00:05.150 --> 00:00:05.160 align:start position:0%
can actually go and start diving into
 

00:00:05.160 --> 00:00:09.259 align:start position:0%
can actually go and start diving into
the<00:00:05.250><c> code</c><00:00:05.490><c> itself</c><00:00:05.879><c> so</c><00:00:06.690><c> the</c><00:00:07.350><c> main</c><00:00:07.890><c> code</c><00:00:08.550><c> this</c><00:00:09.030><c> is</c>

00:00:09.259 --> 00:00:09.269 align:start position:0%
the code itself so the main code this is
 

00:00:09.269 --> 00:00:13.220 align:start position:0%
the code itself so the main code this is
the<00:00:10.280><c> package.json</c><00:00:11.280><c> file</c><00:00:11.820><c> here</c><00:00:12.750><c> we</c><00:00:12.870><c> have</c><00:00:13.019><c> the</c>

00:00:13.220 --> 00:00:13.230 align:start position:0%
the package.json file here we have the
 

00:00:13.230 --> 00:00:15.200 align:start position:0%
the package.json file here we have the
blog<00:00:13.469><c> that</c><00:00:13.500><c> ejs</c><00:00:14.190><c> file</c><00:00:14.429><c> everything</c><00:00:14.849><c> really</c>

00:00:15.200 --> 00:00:15.210 align:start position:0%
blog that ejs file everything really
 

00:00:15.210 --> 00:00:17.840 align:start position:0%
blog that ejs file everything really
happens<00:00:15.509><c> in</c><00:00:15.630><c> the</c><00:00:15.660><c> index</c><00:00:16.139><c> dot</c><00:00:16.320><c> ejs</c><00:00:16.800><c> file</c><00:00:17.130><c> you'll</c>

00:00:17.840 --> 00:00:17.850 align:start position:0%
happens in the index dot ejs file you'll
 

00:00:17.850 --> 00:00:20.720 align:start position:0%
happens in the index dot ejs file you'll
see<00:00:17.940><c> that</c><00:00:18.240><c> we</c><00:00:18.330><c> have</c><00:00:18.510><c> here</c><00:00:18.680><c> partials</c><00:00:19.730><c> include</c>

00:00:20.720 --> 00:00:20.730 align:start position:0%
see that we have here partials include
 

00:00:20.730 --> 00:00:23.630 align:start position:0%
see that we have here partials include
partials<00:00:21.390><c> head</c><00:00:21.660><c> include</c><00:00:22.230><c> the</c><00:00:22.439><c> header</c><00:00:22.619><c> we</c><00:00:23.490><c> have</c>

00:00:23.630 --> 00:00:23.640 align:start position:0%
partials head include the header we have
 

00:00:23.640 --> 00:00:25.730 align:start position:0%
partials head include the header we have
ng-controller<00:00:24.480><c> main</c><00:00:24.960><c> controller</c><00:00:25.439><c> and</c><00:00:25.680><c> we</c>

00:00:25.730 --> 00:00:25.740 align:start position:0%
ng-controller main controller and we
 

00:00:25.740 --> 00:00:31.299 align:start position:0%
ng-controller main controller and we
have<00:00:25.859><c> ng</c><00:00:26.279><c> app</c><00:00:26.430><c> is</c><00:00:26.640><c> GX</c><00:00:27.060><c> leads</c><00:00:27.510><c> so</c><00:00:29.240><c> agency</c>

00:00:31.299 --> 00:00:31.309 align:start position:0%
have ng app is GX leads so agency
 

00:00:31.309 --> 00:00:33.440 align:start position:0%
have ng app is GX leads so agency
controllers<00:00:32.309><c> Jas</c>

00:00:33.440 --> 00:00:33.450 align:start position:0%
controllers Jas
 

00:00:33.450 --> 00:00:39.100 align:start position:0%
controllers Jas
I<00:00:34.550><c> don't</c><00:00:35.550><c> want</c><00:00:35.670><c> Jas</c><00:00:36.090><c> I</c><00:00:36.360><c> wanna</c><00:00:36.600><c> go</c><00:00:36.809><c> into</c><00:00:37.070><c> public</c>

00:00:39.100 --> 00:00:39.110 align:start position:0%
I don't want Jas I wanna go into public
 

00:00:39.110 --> 00:00:44.319 align:start position:0%
I don't want Jas I wanna go into public
js4<00:00:40.110><c> Jas</c><00:00:41.420><c> okay</c><00:00:42.420><c> here's</c><00:00:42.870><c> our</c><00:00:43.050><c> GX</c><00:00:43.440><c> leads</c>

00:00:44.319 --> 00:00:44.329 align:start position:0%
js4 Jas okay here's our GX leads
 

00:00:44.329 --> 00:00:48.410 align:start position:0%
js4 Jas okay here's our GX leads
so<00:00:45.329><c> within</c><00:00:45.660><c> GX</c><00:00:46.200><c> leads</c><00:00:46.559><c> we</c><00:00:47.309><c> have</c><00:00:47.520><c> these</c>

00:00:48.410 --> 00:00:48.420 align:start position:0%
so within GX leads we have these
 

00:00:48.420 --> 00:00:52.790 align:start position:0%
so within GX leads we have these
dependencies<00:00:49.110><c> txt</c><00:00:49.680><c> angular</c><00:00:50.160><c> which</c><00:00:50.430><c> is</c><00:00:51.800><c> pretty</c>

00:00:52.790 --> 00:00:52.800 align:start position:0%
dependencies txt angular which is pretty
 

00:00:52.800 --> 00:00:55.189 align:start position:0%
dependencies txt angular which is pretty
much<00:00:53.010><c> converting</c><00:00:53.789><c> that's</c><00:00:54.660><c> this</c><00:00:54.899><c> feature</c>

00:00:55.189 --> 00:00:55.199 align:start position:0%
much converting that's this feature
 

00:00:55.199 --> 00:00:58.099 align:start position:0%
much converting that's this feature
actually<00:00:55.770><c> over</c><00:00:56.010><c> here</c><00:00:56.280><c> which</c><00:00:57.120><c> we</c><00:00:57.239><c> need</c><00:00:57.390><c> to</c><00:00:57.539><c> make</c>

00:00:58.099 --> 00:00:58.109 align:start position:0%
actually over here which we need to make
 

00:00:58.109 --> 00:01:00.680 align:start position:0%
actually over here which we need to make
a<00:00:58.140><c> little</c><00:00:58.500><c> prettier</c><00:00:58.890><c> this</c><00:00:59.579><c> is</c><00:00:59.730><c> text</c><00:01:00.120><c> angular</c>

00:01:00.680 --> 00:01:00.690 align:start position:0%
a little prettier this is text angular
 

00:01:00.690 --> 00:01:02.750 align:start position:0%
a little prettier this is text angular
it's<00:01:00.899><c> the</c><00:01:01.230><c> it's</c><00:01:01.920><c> called</c><00:01:02.100><c> the</c><00:01:02.190><c> WYSIWYG</c><00:01:02.430><c> what</c>

00:01:02.750 --> 00:01:02.760 align:start position:0%
it's the it's called the WYSIWYG what
 

00:01:02.760 --> 00:01:05.320 align:start position:0%
it's the it's called the WYSIWYG what
you<00:01:03.000><c> see</c><00:01:03.120><c> is</c><00:01:03.390><c> what</c><00:01:03.570><c> you</c><00:01:03.629><c> get</c><00:01:03.960><c> text</c><00:01:04.530><c> editor</c>

00:01:05.320 --> 00:01:05.330 align:start position:0%
you see is what you get text editor
 

00:01:05.330 --> 00:01:08.570 align:start position:0%
you see is what you get text editor
socket<00:01:06.330><c> IO</c><00:01:06.479><c> it's</c><00:01:07.290><c> got</c><00:01:07.470><c> some</c><00:01:07.680><c> directives</c><00:01:08.280><c> and</c>

00:01:08.570 --> 00:01:08.580 align:start position:0%
socket IO it's got some directives and
 

00:01:08.580 --> 00:01:10.550 align:start position:0%
socket IO it's got some directives and
filters<00:01:09.000><c> and</c><00:01:09.030><c> some</c><00:01:09.510><c> services</c><00:01:10.110><c> which</c><00:01:10.409><c> we'll</c>

00:01:10.550 --> 00:01:10.560 align:start position:0%
filters and some services which we'll
 

00:01:10.560 --> 00:01:15.950 align:start position:0%
filters and some services which we'll
get<00:01:10.680><c> into</c><00:01:10.740><c> later</c><00:01:12.140><c> we've</c><00:01:13.140><c> got</c><00:01:13.670><c> if</c><00:01:14.670><c> if</c><00:01:15.600><c> you</c><00:01:15.780><c> go</c>

00:01:15.950 --> 00:01:15.960 align:start position:0%
get into later we've got if if you go
 

00:01:15.960 --> 00:01:18.320 align:start position:0%
get into later we've got if if you go
just<00:01:16.259><c> to</c><00:01:16.439><c> regular</c><00:01:17.009><c> route</c><00:01:17.520><c> it</c><00:01:17.790><c> automates</c><00:01:18.180><c> you</c>

00:01:18.320 --> 00:01:18.330 align:start position:0%
just to regular route it automates you
 

00:01:18.330 --> 00:01:20.060 align:start position:0%
just to regular route it automates you
automatically<00:01:18.900><c> takes</c><00:01:19.200><c> you</c><00:01:19.320><c> to</c><00:01:19.470><c> slash</c><00:01:19.740><c> home</c>

00:01:20.060 --> 00:01:20.070 align:start position:0%
automatically takes you to slash home
 

00:01:20.070 --> 00:01:22.100 align:start position:0%
automatically takes you to slash home
slash<00:01:20.130><c> list</c><00:01:20.970><c> and</c><00:01:21.210><c> let's</c><00:01:21.330><c> try</c><00:01:21.570><c> that</c><00:01:21.780><c> out</c><00:01:21.810><c> also</c>

00:01:22.100 --> 00:01:22.110 align:start position:0%
slash list and let's try that out also
 

00:01:22.110 --> 00:01:26.050 align:start position:0%
slash list and let's try that out also
localhost<00:01:22.890><c> 8,000</c>

00:01:26.050 --> 00:01:26.060 align:start position:0%
 
 

00:01:26.060 --> 00:01:28.520 align:start position:0%
 
see<00:01:27.060><c> what</c><00:01:27.210><c> happens</c><00:01:27.540><c> in</c><00:01:27.630><c> the</c><00:01:27.720><c> URL</c><00:01:28.049><c> and</c><00:01:28.320><c> that's</c>

00:01:28.520 --> 00:01:28.530 align:start position:0%
see what happens in the URL and that's
 

00:01:28.530 --> 00:01:33.490 align:start position:0%
see what happens in the URL and that's
what's<00:01:28.950><c> causing</c><00:01:29.250><c> that</c><00:01:29.579><c> to</c><00:01:29.640><c> happen</c><00:01:30.150><c> over</c><00:01:31.079><c> there</c>

00:01:33.490 --> 00:01:33.500 align:start position:0%
 
 

00:01:33.500 --> 00:01:36.350 align:start position:0%
 
let's<00:01:34.500><c> see</c><00:01:34.710><c> and</c><00:01:35.040><c> we</c><00:01:35.250><c> have</c><00:01:35.400><c> the</c><00:01:35.549><c> community</c><00:01:35.759><c> page</c>

00:01:36.350 --> 00:01:36.360 align:start position:0%
let's see and we have the community page
 

00:01:36.360 --> 00:01:41.260 align:start position:0%
let's see and we have the community page
we<00:01:36.960><c> have</c><00:01:37.170><c> the</c><00:01:37.590><c> homeless</c><00:01:38.159><c> -</c><00:01:38.490><c> -</c><00:01:38.850><c> page</c><00:01:39.650><c> let's</c><00:01:40.650><c> see</c>

00:01:41.260 --> 00:01:41.270 align:start position:0%
we have the homeless - - page let's see
 

00:01:41.270 --> 00:01:46.130 align:start position:0%
we have the homeless - - page let's see
um<00:01:42.270><c> -</c><00:01:42.689><c> list</c><00:01:43.020><c> -</c><00:01:43.530><c> -</c><00:01:44.040><c> page</c><00:01:44.369><c> now</c><00:01:45.210><c> the</c><00:01:45.270><c> partial</c><00:01:45.720><c> home</c>

00:01:46.130 --> 00:01:46.140 align:start position:0%
um - list - - page now the partial home
 

00:01:46.140 --> 00:01:49.160 align:start position:0%
um - list - - page now the partial home
dot<00:01:46.439><c> is</c><00:01:46.710><c> very</c><00:01:47.610><c> important</c><00:01:48.329><c> it's</c><00:01:48.840><c> a</c><00:01:48.960><c> very</c>

00:01:49.160 --> 00:01:49.170 align:start position:0%
dot is very important it's a very
 

00:01:49.170 --> 00:01:51.980 align:start position:0%
dot is very important it's a very
important<00:01:49.829><c> piece</c><00:01:50.040><c> of</c><00:01:50.070><c> the</c><00:01:50.310><c> puzzle</c><00:01:50.520><c> and</c><00:01:51.030><c> that's</c>

00:01:51.980 --> 00:01:51.990 align:start position:0%
important piece of the puzzle and that's
 

00:01:51.990 --> 00:01:56.179 align:start position:0%
important piece of the puzzle and that's
because<00:01:52.350><c> it</c><00:01:52.680><c> has</c><00:01:53.270><c> you</c><00:01:54.270><c> notice</c><00:01:54.600><c> the</c><00:01:55.140><c> tip</c><00:01:55.860><c> UI</c>

00:01:56.179 --> 00:01:56.189 align:start position:0%
because it has you notice the tip UI
 

00:01:56.189 --> 00:02:00.200 align:start position:0%
because it has you notice the tip UI
view<00:01:56.759><c> and</c><00:01:57.000><c> the</c><00:01:57.600><c> div</c><00:01:57.810><c> UI</c><00:01:58.049><c> view</c><00:01:58.619><c> I</c><00:01:58.909><c> see</c><00:01:59.909><c> a</c><00:01:59.939><c> partial</c>

00:02:00.200 --> 00:02:00.210 align:start position:0%
view and the div UI view I see a partial
 

00:02:00.210 --> 00:02:02.480 align:start position:0%
view and the div UI view I see a partial
home<00:02:00.540><c> dot</c><00:02:00.750><c> HTML</c><00:02:01.439><c> where</c><00:02:01.619><c> is</c><00:02:01.710><c> that</c><00:02:01.860><c> found</c><00:02:02.130><c> over</c>

00:02:02.480 --> 00:02:02.490 align:start position:0%
home dot HTML where is that found over
 

00:02:02.490 --> 00:02:08.869 align:start position:0%
home dot HTML where is that found over
here<00:02:02.750><c> that's</c><00:02:03.750><c> found</c><00:02:04.110><c> over</c><00:02:05.009><c> here</c><00:02:05.540><c> UI</c><00:02:06.540><c> view</c><00:02:07.879><c> UI</c>

00:02:08.869 --> 00:02:08.879 align:start position:0%
here that's found over here UI view UI
 

00:02:08.879 --> 00:02:11.760 align:start position:0%
here that's found over here UI view UI
view<00:02:10.640><c> partial</c>

00:02:11.760 --> 00:02:11.770 align:start position:0%
view partial
 

00:02:11.770 --> 00:02:25.280 align:start position:0%
view partial
on<00:02:11.950><c> that</c><00:02:12.250><c> HTML</c><00:02:12.970><c> they've</c><00:02:13.450><c> uiview</c><00:02:23.910><c> business</c>

00:02:25.280 --> 00:02:25.290 align:start position:0%
on that HTML they've uiview business
 

00:02:25.290 --> 00:02:29.550 align:start position:0%
on that HTML they've uiview business
okay<00:02:26.290><c> so</c><00:02:26.320><c> the</c><00:02:26.650><c> home</c><00:02:26.950><c> is</c><00:02:28.080><c> partial</c><00:02:29.080><c> home</c><00:02:29.290><c> dot</c>

00:02:29.550 --> 00:02:29.560 align:start position:0%
okay so the home is partial home dot
 

00:02:29.560 --> 00:02:32.940 align:start position:0%
okay so the home is partial home dot
HTML<00:02:30.310><c> whenever</c><00:02:30.610><c> your</c><00:02:30.880><c> state</c><00:02:31.180><c> is</c><00:02:31.480><c> home</c><00:02:31.950><c> which</c>

00:02:32.940 --> 00:02:32.950 align:start position:0%
HTML whenever your state is home which
 

00:02:32.950 --> 00:02:37.740 align:start position:0%
HTML whenever your state is home which
is<00:02:33.130><c> by</c><00:02:33.550><c> the</c><00:02:33.580><c> way</c><00:02:33.850><c> the</c><00:02:34.120><c> natural</c><00:02:34.990><c> then</c><00:02:36.750><c> it's</c>

00:02:37.740 --> 00:02:37.750 align:start position:0%
is by the way the natural then it's
 

00:02:37.750 --> 00:02:39.990 align:start position:0%
is by the way the natural then it's
going<00:02:37.960><c> to</c><00:02:38.020><c> show</c><00:02:38.230><c> you</c><00:02:38.290><c> the</c><00:02:39.070><c> home</c><00:02:39.280><c> page</c><00:02:39.550><c> partial</c>

00:02:39.990 --> 00:02:40.000 align:start position:0%
going to show you the home page partial
 

00:02:40.000 --> 00:02:43.410 align:start position:0%
going to show you the home page partial
home<00:02:40.150><c> dot</c><00:02:40.360><c> HTML</c><00:02:41.200><c> this</c><00:02:42.010><c> specific</c><00:02:42.700><c> snippet</c><00:02:43.240><c> and</c>

00:02:43.410 --> 00:02:43.420 align:start position:0%
home dot HTML this specific snippet and
 

00:02:43.420 --> 00:02:45.510 align:start position:0%
home dot HTML this specific snippet and
within<00:02:43.990><c> here</c><00:02:44.380><c> we</c><00:02:44.560><c> have</c><00:02:44.710><c> all</c><00:02:45.040><c> these</c><00:02:45.190><c> buttons</c>

00:02:45.510 --> 00:02:45.520 align:start position:0%
within here we have all these buttons
 

00:02:45.520 --> 00:02:48.210 align:start position:0%
within here we have all these buttons
lead<00:02:45.940><c> stats</c><00:02:46.420><c> community</c><00:02:47.020><c> surged</c><00:02:47.260><c> contact</c><00:02:47.980><c> and</c>

00:02:48.210 --> 00:02:48.220 align:start position:0%
lead stats community surged contact and
 

00:02:48.220 --> 00:02:50.670 align:start position:0%
lead stats community surged contact and
blog<00:02:48.660><c> okay</c><00:02:49.660><c> and</c><00:02:49.810><c> that's</c><00:02:49.960><c> over</c><00:02:50.110><c> here</c><00:02:50.350><c> leads</c>

00:02:50.670 --> 00:02:50.680 align:start position:0%
blog okay and that's over here leads
 

00:02:50.680 --> 00:02:53.100 align:start position:0%
blog okay and that's over here leads
that's<00:02:51.070><c> community</c><00:02:51.790><c> surge</c><00:02:52.030><c> contact</c><00:02:52.690><c> and</c><00:02:52.870><c> blog</c>

00:02:53.100 --> 00:02:53.110 align:start position:0%
that's community surge contact and blog
 

00:02:53.110 --> 00:02:58.200 align:start position:0%
that's community surge contact and blog
and<00:02:54.150><c> we</c><00:02:55.150><c> also</c><00:02:55.390><c> have</c><00:02:56.490><c> here</c><00:02:57.490><c> we</c><00:02:57.670><c> stipulate</c>

00:02:58.200 --> 00:02:58.210 align:start position:0%
and we also have here we stipulate
 

00:02:58.210 --> 00:03:00.870 align:start position:0%
and we also have here we stipulate
exactly<00:02:58.720><c> which</c><00:02:58.930><c> snippet</c><00:02:59.650><c> of</c><00:02:59.770><c> HTML</c><00:02:59.920><c> which</c><00:03:00.850><c> is</c>

00:03:00.870 --> 00:03:00.880 align:start position:0%
exactly which snippet of HTML which is
 

00:03:00.880 --> 00:03:03.350 align:start position:0%
exactly which snippet of HTML which is
all<00:03:01.120><c> located</c><00:03:01.630><c> here</c><00:03:01.870><c> in</c><00:03:01.990><c> the</c><00:03:02.020><c> public</c><00:03:02.260><c> folder</c>

00:03:03.350 --> 00:03:03.360 align:start position:0%
all located here in the public folder
 

00:03:03.360 --> 00:03:08.310 align:start position:0%
all located here in the public folder
which<00:03:04.360><c> snippet</c><00:03:04.870><c> of</c><00:03:04.990><c> HTML</c><00:03:05.140><c> is</c><00:03:06.540><c> going</c><00:03:07.540><c> to</c><00:03:08.020><c> be</c>

00:03:08.310 --> 00:03:08.320 align:start position:0%
which snippet of HTML is going to be
 

00:03:08.320 --> 00:03:10.790 align:start position:0%
which snippet of HTML is going to be
displayed

00:03:10.790 --> 00:03:10.800 align:start position:0%
displayed
 

00:03:10.800 --> 00:03:14.040 align:start position:0%
displayed
okay<00:03:11.800><c> sorry</c><00:03:12.250><c> it's</c><00:03:12.340><c> in</c><00:03:12.670><c> my</c><00:03:12.850><c> views</c><00:03:13.300><c> folder</c><00:03:13.900><c> all</c>

00:03:14.040 --> 00:03:14.050 align:start position:0%
okay sorry it's in my views folder all
 

00:03:14.050 --> 00:03:16.980 align:start position:0%
okay sorry it's in my views folder all
these<00:03:14.260><c> things</c><00:03:14.610><c> index</c><00:03:15.610><c> dot</c><00:03:15.790><c> age</c><00:03:15.940><c> yes</c><00:03:16.240><c> login</c><00:03:16.900><c> to</c>

00:03:16.980 --> 00:03:16.990 align:start position:0%
these things index dot age yes login to
 

00:03:16.990 --> 00:03:19.020 align:start position:0%
these things index dot age yes login to
ejs<00:03:17.650><c> is</c><00:03:17.920><c> also</c><00:03:18.250><c> there</c><00:03:18.459><c> if</c><00:03:18.580><c> you</c><00:03:18.670><c> want</c><00:03:18.820><c> to</c><00:03:18.880><c> make</c>

00:03:19.020 --> 00:03:19.030 align:start position:0%
ejs is also there if you want to make
 

00:03:19.030 --> 00:03:21.180 align:start position:0%
ejs is also there if you want to make
any<00:03:19.240><c> changes</c><00:03:19.690><c> we're</c><00:03:19.959><c> also</c><00:03:20.080><c> using</c><00:03:20.380><c> ejs</c>

00:03:21.180 --> 00:03:21.190 align:start position:0%
any changes we're also using ejs
 

00:03:21.190 --> 00:03:25.500 align:start position:0%
any changes we're also using ejs
template<00:03:21.790><c> we</c><00:03:22.750><c> have</c><00:03:22.780><c> profile</c><00:03:23.470><c> DJ</c><00:03:24.130><c> s</c><00:03:24.310><c> which</c><00:03:25.000><c> uses</c>

00:03:25.500 --> 00:03:25.510 align:start position:0%
template we have profile DJ s which uses
 

00:03:25.510 --> 00:03:27.420 align:start position:0%
template we have profile DJ s which uses
parameters<00:03:26.080><c> we'll</c><00:03:26.320><c> discuss</c><00:03:26.680><c> that</c><00:03:26.980><c> search</c>

00:03:27.420 --> 00:03:27.430 align:start position:0%
parameters we'll discuss that search
 

00:03:27.430 --> 00:03:32.100 align:start position:0%
parameters we'll discuss that search
page<00:03:27.790><c> the</c><00:03:28.150><c> signup</c><00:03:28.600><c> page</c><00:03:28.890><c> the</c><00:03:29.890><c> CSS</c><00:03:30.520><c> page</c><00:03:31.110><c> the</c>

00:03:32.100 --> 00:03:32.110 align:start position:0%
page the signup page the CSS page the
 

00:03:32.110 --> 00:03:34.920 align:start position:0%
page the signup page the CSS page the
error<00:03:32.380><c> page</c><00:03:32.830><c> the</c><00:03:33.370><c> contact</c><00:03:33.910><c> page</c><00:03:34.270><c> that</c><00:03:34.660><c> we</c><00:03:34.750><c> just</c>

00:03:34.920 --> 00:03:34.930 align:start position:0%
error page the contact page that we just
 

00:03:34.930 --> 00:03:37.620 align:start position:0%
error page the contact page that we just
spoke<00:03:35.140><c> about</c><00:03:35.440><c> and</c><00:03:35.740><c> the</c><00:03:36.610><c> blog</c><00:03:36.880><c> page</c><00:03:37.209><c> which</c>

00:03:37.620 --> 00:03:37.630 align:start position:0%
spoke about and the blog page which
 

00:03:37.630 --> 00:03:42.090 align:start position:0%
spoke about and the blog page which
lists<00:03:38.290><c> all</c><00:03:38.440><c> these</c><00:03:38.890><c> blogs</c><00:03:39.280><c> over</c><00:03:39.730><c> here</c><00:03:41.100><c> so</c>

00:03:42.090 --> 00:03:42.100 align:start position:0%
lists all these blogs over here so
 

00:03:42.100 --> 00:03:45.360 align:start position:0%
lists all these blogs over here so
that's<00:03:42.459><c> all</c><00:03:42.790><c> using</c><00:03:43.420><c> angular</c><00:03:44.260><c> UI</c><00:03:44.530><c> router</c><00:03:45.010><c> in</c>

00:03:45.360 --> 00:03:45.370 align:start position:0%
that's all using angular UI router in
 

00:03:45.370 --> 00:03:48.210 align:start position:0%
that's all using angular UI router in
order<00:03:45.459><c> to</c><00:03:45.730><c> manage</c><00:03:46.060><c> our</c><00:03:46.360><c> state</c><00:03:47.080><c> and</c><00:03:47.350><c> to</c><00:03:47.410><c> manage</c>

00:03:48.210 --> 00:03:48.220 align:start position:0%
order to manage our state and to manage
 

00:03:48.220 --> 00:03:51.570 align:start position:0%
order to manage our state and to manage
our<00:03:49.500><c> template</c><00:03:50.500><c> so</c><00:03:50.650><c> that's</c><00:03:50.830><c> called</c><00:03:51.040><c> a</c><00:03:51.160><c> single</c>

00:03:51.570 --> 00:03:51.580 align:start position:0%
our template so that's called a single
 

00:03:51.580 --> 00:03:54.000 align:start position:0%
our template so that's called a single
page<00:03:51.790><c> application</c><00:03:52.510><c> because</c><00:03:52.750><c> actually</c><00:03:53.410><c> the</c>

00:03:54.000 --> 00:03:54.010 align:start position:0%
page application because actually the
 

00:03:54.010 --> 00:03:56.010 align:start position:0%
page application because actually the
entire<00:03:54.310><c> page</c><00:03:54.700><c> doesn't</c><00:03:55.000><c> reload</c><00:03:55.540><c> as</c><00:03:55.810><c> you'll</c>

00:03:56.010 --> 00:03:56.020 align:start position:0%
entire page doesn't reload as you'll
 

00:03:56.020 --> 00:03:59.370 align:start position:0%
entire page doesn't reload as you'll
notice<00:03:56.290><c> if</c><00:03:56.560><c> I</c><00:03:56.830><c> click</c><00:03:56.860><c> here</c><00:03:57.630><c> not</c><00:03:58.630><c> actually</c>

00:03:59.370 --> 00:03:59.380 align:start position:0%
notice if I click here not actually
 

00:03:59.380 --> 00:04:01.800 align:start position:0%
notice if I click here not actually
reloading<00:03:59.950><c> this</c><00:04:00.100><c> navbar</c><00:04:00.850><c> the</c><00:04:01.000><c> navbar</c><00:04:01.240><c> stay</c>

00:04:01.800 --> 00:04:01.810 align:start position:0%
reloading this navbar the navbar stay
 

00:04:01.810 --> 00:04:06.199 align:start position:0%
reloading this navbar the navbar stay
stagnant<00:04:02.260><c> on</c><00:04:02.650><c> the</c><00:04:02.740><c> navbar</c><00:04:02.980><c> is</c><00:04:03.959><c> located</c><00:04:04.959><c> over</c>

00:04:06.199 --> 00:04:06.209 align:start position:0%
stagnant on the navbar is located over
 

00:04:06.209 --> 00:04:16.050 align:start position:0%
stagnant on the navbar is located over
see<00:04:07.209><c> if</c><00:04:07.240><c> use</c><00:04:12.720><c> partials</c><00:04:14.040><c> use</c><00:04:15.040><c> partials</c><00:04:15.730><c> and</c>

00:04:16.050 --> 00:04:16.060 align:start position:0%
see if use partials use partials and
 

00:04:16.060 --> 00:04:19.500 align:start position:0%
see if use partials use partials and
head<00:04:16.510><c> is</c><00:04:16.980><c> the</c><00:04:17.980><c> head</c><00:04:18.220><c> of</c><00:04:18.400><c> the</c><00:04:18.549><c> page</c><00:04:18.790><c> which</c><00:04:19.299><c> is</c>

00:04:19.500 --> 00:04:19.510 align:start position:0%
head is the head of the page which is
 

00:04:19.510 --> 00:04:22.230 align:start position:0%
head is the head of the page which is
where<00:04:19.750><c> you're</c><00:04:19.930><c> going</c><00:04:20.080><c> to</c><00:04:20.140><c> inject</c><00:04:20.940><c> all</c><00:04:21.940><c> your</c>

00:04:22.230 --> 00:04:22.240 align:start position:0%
where you're going to inject all your
 

00:04:22.240 --> 00:04:24.480 align:start position:0%
where you're going to inject all your
dependencies<00:04:22.419><c> like</c><00:04:23.140><c> text</c><00:04:23.800><c> angular</c><00:04:24.280><c> angular</c>

00:04:24.480 --> 00:04:24.490 align:start position:0%
dependencies like text angular angular
 

00:04:24.490 --> 00:04:25.060 align:start position:0%
dependencies like text angular angular
you

00:04:25.060 --> 00:04:25.070 align:start position:0%
you
 

00:04:25.070 --> 00:04:28.600 align:start position:0%
you
router<00:04:25.430><c> bootstrap</c><00:04:26.420><c> and</c><00:04:26.750><c> so</c><00:04:26.990><c> on</c><00:04:27.170><c> and</c><00:04:27.610><c> the</c>

00:04:28.600 --> 00:04:28.610 align:start position:0%
router bootstrap and so on and the
 

00:04:28.610 --> 00:04:31.120 align:start position:0%
router bootstrap and so on and the
header<00:04:28.850><c> is</c><00:04:29.120><c> where</c><00:04:29.330><c> the</c><00:04:29.480><c> navbar</c><00:04:29.690><c> is</c><00:04:29.990><c> and</c><00:04:30.650><c> that</c>

00:04:31.120 --> 00:04:31.130 align:start position:0%
header is where the navbar is and that
 

00:04:31.130 --> 00:04:34.690 align:start position:0%
header is where the navbar is and that
says<00:04:31.370><c> welcome</c><00:04:31.670><c> client</c><00:04:32.480><c> client</c><00:04:33.080><c> email</c><00:04:33.950><c> and</c>

00:04:34.690 --> 00:04:34.700 align:start position:0%
says welcome client client email and
 

00:04:34.700 --> 00:04:36.430 align:start position:0%
says welcome client client email and
that's<00:04:34.820><c> why</c><00:04:35.030><c> it</c><00:04:35.150><c> says</c><00:04:35.360><c> welcome</c><00:04:35.600><c> donations</c><00:04:36.260><c> at</c>

00:04:36.430 --> 00:04:36.440 align:start position:0%
that's why it says welcome donations at
 

00:04:36.440 --> 00:04:41.070 align:start position:0%
that's why it says welcome donations at
hi<00:04:36.620><c> Israel</c><00:04:37.130><c> org</c><00:04:37.550><c> because</c><00:04:38.560><c> this</c><00:04:39.560><c> here</c><00:04:40.130><c> is</c><00:04:40.340><c> an</c>

00:04:41.070 --> 00:04:41.080 align:start position:0%
hi Israel org because this here is an
 

00:04:41.080 --> 00:04:46.750 align:start position:0%
hi Israel org because this here is an
angular<00:04:44.260><c> and</c><00:04:45.260><c> angular</c><00:04:45.410><c> scope</c><00:04:46.040><c> object</c><00:04:46.610><c> we're</c>

00:04:46.750 --> 00:04:46.760 align:start position:0%
angular and angular scope object we're
 

00:04:46.760 --> 00:04:49.200 align:start position:0%
angular and angular scope object we're
going<00:04:46.880><c> to</c><00:04:46.940><c> go</c><00:04:47.060><c> into</c><00:04:47.210><c> scope</c><00:04:47.600><c> a</c><00:04:47.750><c> little</c><00:04:47.780><c> bit</c><00:04:48.080><c> more</c>

00:04:49.200 --> 00:04:49.210 align:start position:0%
going to go into scope a little bit more
 

00:04:49.210 --> 00:04:55.870 align:start position:0%
going to go into scope a little bit more
client<00:04:51.070><c> C</c><00:04:52.480><c> gonna</c><00:04:53.480><c> get</c><00:04:53.720><c> into</c><00:04:53.990><c> scope</c><00:04:54.790><c> all</c><00:04:55.790><c> right</c>

00:04:55.870 --> 00:04:55.880 align:start position:0%
client C gonna get into scope all right
 

00:04:55.880 --> 00:04:58.540 align:start position:0%
client C gonna get into scope all right
fine<00:04:56.210><c> you</c><00:04:56.240><c> know</c>

