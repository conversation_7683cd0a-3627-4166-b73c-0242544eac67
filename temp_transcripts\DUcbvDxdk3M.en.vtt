WEBVTT
Kind: captions
Language: en

00:00:06.560 --> 00:00:08.669 align:start position:0%
 
you<00:00:06.720><c> can</c><00:00:06.879><c> use</c><00:00:07.080><c> the</c><00:00:07.240><c> Johnstone</c><00:00:07.759><c> Labs</c><00:00:08.120><c> website</c>

00:00:08.669 --> 00:00:08.679 align:start position:0%
you can use the Johnstone Labs website
 

00:00:08.679 --> 00:00:10.870 align:start position:0%
you can use the Johnstone Labs website
to<00:00:08.920><c> find</c><00:00:09.160><c> the</c><00:00:09.320><c> models</c><00:00:09.880><c> that</c><00:00:10.040><c> can</c><00:00:10.200><c> be</c><00:00:10.360><c> deployed</c>

00:00:10.870 --> 00:00:10.880 align:start position:0%
to find the models that can be deployed
 

00:00:10.880 --> 00:00:15.470 align:start position:0%
to find the models that can be deployed
as<00:00:11.160><c> private</c><00:00:11.519><c> API</c><00:00:11.920><c> endpoints</c><00:00:12.559><c> on</c>

00:00:15.470 --> 00:00:15.480 align:start position:0%
 
 

00:00:15.480 --> 00:00:18.230 align:start position:0%
 
Snowflake<00:00:16.480><c> today</c><00:00:16.840><c> I</c><00:00:16.920><c> will</c><00:00:17.160><c> deploy</c><00:00:17.640><c> a</c><00:00:17.800><c> clinical</c>

00:00:18.230 --> 00:00:18.240 align:start position:0%
Snowflake today I will deploy a clinical
 

00:00:18.240 --> 00:00:20.509 align:start position:0%
Snowflake today I will deploy a clinical
deification<00:00:19.039><c> model</c><00:00:19.400><c> on</c><00:00:19.560><c> snowflake</c><00:00:20.240><c> as</c><00:00:20.320><c> a</c>

00:00:20.509 --> 00:00:20.519 align:start position:0%
deification model on snowflake as a
 

00:00:20.519 --> 00:00:23.630 align:start position:0%
deification model on snowflake as a
private<00:00:20.920><c> API</c><00:00:21.400><c> endpoint</c><00:00:22.400><c> and</c><00:00:22.800><c> show</c><00:00:23.080><c> you</c><00:00:23.320><c> how</c><00:00:23.439><c> to</c>

00:00:23.630 --> 00:00:23.640 align:start position:0%
private API endpoint and show you how to
 

00:00:23.640 --> 00:00:25.630 align:start position:0%
private API endpoint and show you how to
query

00:00:25.630 --> 00:00:25.640 align:start position:0%
query
 

00:00:25.640 --> 00:00:28.950 align:start position:0%
query
it<00:00:26.640><c> this</c><00:00:26.880><c> model</c><00:00:27.359><c> was</c><00:00:27.640><c> designed</c><00:00:28.039><c> to</c><00:00:28.279><c> recognize</c>

00:00:28.950 --> 00:00:28.960 align:start position:0%
it this model was designed to recognize
 

00:00:28.960 --> 00:00:31.589 align:start position:0%
it this model was designed to recognize
and<00:00:29.160><c> anonymize</c><00:00:30.000><c> a</c><00:00:30.199><c> wide</c><00:00:30.439><c> range</c><00:00:30.759><c> of</c><00:00:31.039><c> PSI</c>

00:00:31.589 --> 00:00:31.599 align:start position:0%
and anonymize a wide range of PSI
 

00:00:31.599 --> 00:00:38.229 align:start position:0%
and anonymize a wide range of PSI
entities<00:00:32.320><c> from</c><00:00:32.559><c> medical</c>

00:00:38.229 --> 00:00:38.239 align:start position:0%
 
 

00:00:38.239 --> 00:00:41.590 align:start position:0%
 
texts<00:00:39.239><c> to</c><00:00:39.520><c> start</c><00:00:39.920><c> the</c><00:00:40.079><c> deployment</c><00:00:40.760><c> process</c>

00:00:41.590 --> 00:00:41.600 align:start position:0%
texts to start the deployment process
 

00:00:41.600 --> 00:00:43.750 align:start position:0%
texts to start the deployment process
click<00:00:41.879><c> on</c><00:00:42.079><c> the</c><00:00:42.280><c> deploy</c><00:00:42.680><c> to</c><00:00:42.840><c> snowflake</c><00:00:43.360><c> button</c>

00:00:43.750 --> 00:00:43.760 align:start position:0%
click on the deploy to snowflake button
 

00:00:43.760 --> 00:00:46.869 align:start position:0%
click on the deploy to snowflake button
available<00:00:44.200><c> on</c><00:00:44.399><c> the</c><00:00:44.520><c> model</c><00:00:45.440><c> page</c><00:00:46.440><c> this</c><00:00:46.640><c> will</c>

00:00:46.869 --> 00:00:46.879 align:start position:0%
available on the model page this will
 

00:00:46.879 --> 00:00:49.189 align:start position:0%
available on the model page this will
open<00:00:47.160><c> up</c><00:00:47.399><c> the</c><00:00:47.640><c> listing</c><00:00:48.120><c> page</c><00:00:48.399><c> for</c><00:00:48.640><c> the</c><00:00:48.760><c> model</c>

00:00:49.189 --> 00:00:49.199 align:start position:0%
open up the listing page for the model
 

00:00:49.199 --> 00:00:51.470 align:start position:0%
open up the listing page for the model
on<00:00:49.399><c> snowflake</c>

00:00:51.470 --> 00:00:51.480 align:start position:0%
on snowflake
 

00:00:51.480 --> 00:00:53.590 align:start position:0%
on snowflake
Marketplace<00:00:52.480><c> make</c><00:00:52.680><c> sure</c><00:00:52.960><c> you</c><00:00:53.079><c> are</c><00:00:53.280><c> logged</c>

00:00:53.590 --> 00:00:53.600 align:start position:0%
Marketplace make sure you are logged
 

00:00:53.600 --> 00:00:56.910 align:start position:0%
Marketplace make sure you are logged
into<00:00:53.960><c> your</c><00:00:54.120><c> snowflake</c>

00:00:56.910 --> 00:00:56.920 align:start position:0%
 
 

00:00:56.920 --> 00:01:01.110 align:start position:0%
 
account<00:00:57.920><c> next</c><00:00:58.760><c> click</c><00:00:59.039><c> the</c><00:00:59.239><c> get</c><00:00:59.480><c> button</c>

00:01:01.110 --> 00:01:01.120 align:start position:0%
account next click the get button
 

00:01:01.120 --> 00:01:02.910 align:start position:0%
account next click the get button
this<00:01:01.359><c> opens</c><00:01:01.719><c> up</c><00:01:01.920><c> a</c><00:01:02.120><c> popup</c><00:01:02.600><c> with</c><00:01:02.760><c> the</c>

00:01:02.910 --> 00:01:02.920 align:start position:0%
this opens up a popup with the
 

00:01:02.920 --> 00:01:04.990 align:start position:0%
this opens up a popup with the
deployment

00:01:04.990 --> 00:01:05.000 align:start position:0%
deployment
 

00:01:05.000 --> 00:01:08.070 align:start position:0%
deployment
option<00:01:06.000><c> you</c><00:01:06.159><c> can</c><00:01:06.400><c> click</c><00:01:06.880><c> a</c><00:01:07.119><c> warehouse</c><00:01:07.759><c> for</c>

00:01:08.070 --> 00:01:08.080 align:start position:0%
option you can click a warehouse for
 

00:01:08.080 --> 00:01:09.670 align:start position:0%
option you can click a warehouse for
installation

00:01:09.670 --> 00:01:09.680 align:start position:0%
installation
 

00:01:09.680 --> 00:01:12.190 align:start position:0%
installation
purpose<00:01:10.680><c> also</c><00:01:11.080><c> you</c><00:01:11.200><c> can</c><00:01:11.439><c> either</c><00:01:11.720><c> try</c><00:01:12.000><c> the</c>

00:01:12.190 --> 00:01:12.200 align:start position:0%
purpose also you can either try the
 

00:01:12.200 --> 00:01:14.270 align:start position:0%
purpose also you can either try the
product<00:01:12.479><c> for</c><00:01:12.759><c> free</c><00:01:13.479><c> with</c><00:01:13.640><c> a</c><00:01:13.799><c> trial</c>

00:01:14.270 --> 00:01:14.280 align:start position:0%
product for free with a trial
 

00:01:14.280 --> 00:01:17.469 align:start position:0%
product for free with a trial
subscription<00:01:15.280><c> or</c><00:01:16.000><c> buy</c><00:01:16.240><c> the</c><00:01:16.439><c> product</c>

00:01:17.469 --> 00:01:17.479 align:start position:0%
subscription or buy the product
 

00:01:17.479 --> 00:01:19.950 align:start position:0%
subscription or buy the product
entirely<00:01:18.479><c> this</c><00:01:18.640><c> will</c><00:01:18.880><c> begin</c><00:01:19.200><c> installing</c><00:01:19.759><c> the</c>

00:01:19.950 --> 00:01:19.960 align:start position:0%
entirely this will begin installing the
 

00:01:19.960 --> 00:01:25.870 align:start position:0%
entirely this will begin installing the
application<00:01:20.600><c> to</c><00:01:20.759><c> your</c><00:01:20.920><c> snowflake</c>

00:01:25.870 --> 00:01:25.880 align:start position:0%
 
 

00:01:25.880 --> 00:01:28.310 align:start position:0%
 
account<00:01:26.880><c> next</c><00:01:27.159><c> you</c><00:01:27.280><c> will</c><00:01:27.479><c> need</c><00:01:27.640><c> to</c><00:01:27.840><c> configure</c>

00:01:28.310 --> 00:01:28.320 align:start position:0%
account next you will need to configure
 

00:01:28.320 --> 00:01:30.030 align:start position:0%
account next you will need to configure
the<00:01:28.479><c> application</c>

00:01:30.030 --> 00:01:30.040 align:start position:0%
the application
 

00:01:30.040 --> 00:01:32.429 align:start position:0%
the application
snowf<00:01:30.439><c> flick</c><00:01:30.720><c> provides</c><00:01:31.040><c> you</c><00:01:31.200><c> a</c><00:01:31.320><c> setup</c><00:01:31.720><c> result</c>

00:01:32.429 --> 00:01:32.439 align:start position:0%
snowf flick provides you a setup result
 

00:01:32.439 --> 00:01:33.630 align:start position:0%
snowf flick provides you a setup result
where<00:01:32.600><c> you</c><00:01:32.720><c> will</c><00:01:32.880><c> need</c><00:01:33.040><c> to</c><00:01:33.200><c> select</c><00:01:33.479><c> the</c>

00:01:33.630 --> 00:01:33.640 align:start position:0%
where you will need to select the
 

00:01:33.640 --> 00:01:35.670 align:start position:0%
where you will need to select the
warehouse<00:01:34.200><c> that</c><00:01:34.360><c> you</c><00:01:34.479><c> will</c><00:01:34.680><c> use</c><00:01:34.960><c> to</c><00:01:35.200><c> activate</c>

00:01:35.670 --> 00:01:35.680 align:start position:0%
warehouse that you will use to activate
 

00:01:35.680 --> 00:01:38.429 align:start position:0%
warehouse that you will use to activate
the<00:01:36.040><c> app</c><00:01:37.040><c> and</c><00:01:37.240><c> also</c><00:01:37.520><c> Grant</c><00:01:37.880><c> the</c><00:01:38.040><c> necessary</c>

00:01:38.429 --> 00:01:38.439 align:start position:0%
the app and also Grant the necessary
 

00:01:38.439 --> 00:01:43.510 align:start position:0%
the app and also Grant the necessary
permissions<00:01:39.000><c> required</c><00:01:39.399><c> by</c><00:01:39.560><c> the</c>

00:01:43.510 --> 00:01:43.520 align:start position:0%
 
 

00:01:43.520 --> 00:01:45.910 align:start position:0%
 
application<00:01:44.520><c> the</c><00:01:44.680><c> application</c><00:01:45.159><c> requires</c><00:01:45.600><c> two</c>

00:01:45.910 --> 00:01:45.920 align:start position:0%
application the application requires two
 

00:01:45.920 --> 00:01:48.789 align:start position:0%
application the application requires two
permission<00:01:46.920><c> one</c><00:01:47.280><c> to</c><00:01:47.560><c> bind</c><00:01:47.920><c> the</c><00:01:48.119><c> service</c><00:01:48.439><c> to</c><00:01:48.600><c> an</c>

00:01:48.789 --> 00:01:48.799 align:start position:0%
permission one to bind the service to an
 

00:01:48.799 --> 00:01:51.190 align:start position:0%
permission one to bind the service to an
endpoint<00:01:49.799><c> and</c><00:01:50.000><c> order</c><00:01:50.280><c> to</c><00:01:50.439><c> start</c><00:01:50.680><c> a</c><00:01:50.840><c> compute</c>

00:01:51.190 --> 00:01:51.200 align:start position:0%
endpoint and order to start a compute
 

00:01:51.200 --> 00:01:54.670 align:start position:0%
endpoint and order to start a compute
pool<00:01:51.439><c> that</c><00:01:51.560><c> runs</c><00:01:51.920><c> the</c>

00:01:54.670 --> 00:01:54.680 align:start position:0%
 
 

00:01:54.680 --> 00:01:58.270 align:start position:0%
 
service<00:01:55.680><c> now</c><00:01:56.000><c> click</c><00:01:56.200><c> on</c><00:01:56.360><c> the</c><00:01:56.560><c> activate</c><00:01:57.280><c> button</c>

00:01:58.270 --> 00:01:58.280 align:start position:0%
service now click on the activate button
 

00:01:58.280 --> 00:02:00.670 align:start position:0%
service now click on the activate button
this<00:01:58.719><c> activates</c><00:01:59.200><c> the</c><00:01:59.360><c> application</c><00:02:00.079><c> and</c><00:02:00.280><c> now</c>

00:02:00.670 --> 00:02:00.680 align:start position:0%
this activates the application and now
 

00:02:00.680 --> 00:02:11.350 align:start position:0%
this activates the application and now
you<00:02:00.840><c> can</c><00:02:01.039><c> start</c><00:02:01.360><c> using</c>

00:02:11.350 --> 00:02:11.360 align:start position:0%
 
 

00:02:11.360 --> 00:02:14.430 align:start position:0%
 
it<00:02:12.360><c> in</c><00:02:12.560><c> the</c><00:02:12.800><c> about</c><00:02:13.120><c> the</c><00:02:13.280><c> app</c><00:02:13.599><c> section</c><00:02:13.920><c> of</c><00:02:14.080><c> the</c>

00:02:14.430 --> 00:02:14.440 align:start position:0%
it in the about the app section of the
 

00:02:14.440 --> 00:02:16.910 align:start position:0%
it in the about the app section of the
application<00:02:15.440><c> there</c><00:02:15.640><c> is</c><00:02:15.879><c> detail</c><00:02:16.280><c> information</c>

00:02:16.910 --> 00:02:16.920 align:start position:0%
application there is detail information
 

00:02:16.920 --> 00:02:20.070 align:start position:0%
application there is detail information
on<00:02:17.519><c> how</c><00:02:17.680><c> to</c><00:02:17.920><c> use</c><00:02:18.280><c> the</c><00:02:18.480><c> application</c><00:02:19.440><c> and</c><00:02:19.720><c> how</c><00:02:19.879><c> to</c>

00:02:20.070 --> 00:02:20.080 align:start position:0%
on how to use the application and how to
 

00:02:20.080 --> 00:02:31.589 align:start position:0%
on how to use the application and how to
query<00:02:20.480><c> the</c><00:02:20.599><c> endpoint</c><00:02:21.160><c> once</c><00:02:21.480><c> created</c>

00:02:31.589 --> 00:02:31.599 align:start position:0%
 
 

00:02:31.599 --> 00:02:33.750 align:start position:0%
 
next<00:02:32.160><c> click</c><00:02:32.440><c> the</c><00:02:32.560><c> launch</c><00:02:33.000><c> app</c><00:02:33.239><c> button</c><00:02:33.560><c> and</c>

00:02:33.750 --> 00:02:33.760 align:start position:0%
next click the launch app button and
 

00:02:33.760 --> 00:02:38.350 align:start position:0%
next click the launch app button and
start<00:02:34.040><c> running</c><00:02:34.360><c> the</c><00:02:34.519><c> commands</c><00:02:34.959><c> given</c><00:02:35.200><c> in</c><00:02:35.360><c> the</c>

00:02:38.350 --> 00:02:38.360 align:start position:0%
 
 

00:02:38.360 --> 00:02:41.670 align:start position:0%
 
example<00:02:39.360><c> calling</c><00:02:39.800><c> the</c><00:02:40.000><c> start</c><00:02:40.400><c> app</c><00:02:40.680><c> procedure</c>

00:02:41.670 --> 00:02:41.680 align:start position:0%
example calling the start app procedure
 

00:02:41.680 --> 00:02:43.949 align:start position:0%
example calling the start app procedure
starts<00:02:42.120><c> a</c><00:02:42.360><c> compute</c><00:02:42.760><c> pool</c><00:02:43.319><c> and</c><00:02:43.480><c> runs</c><00:02:43.800><c> the</c>

00:02:43.949 --> 00:02:43.959 align:start position:0%
starts a compute pool and runs the
 

00:02:43.959 --> 00:02:51.229 align:start position:0%
starts a compute pool and runs the
endpoint<00:02:44.400><c> service</c><00:02:45.000><c> inside</c><00:02:45.360><c> the</c><00:02:45.519><c> compute</c>

00:02:51.229 --> 00:02:51.239 align:start position:0%
 
 

00:02:51.239 --> 00:02:54.110 align:start position:0%
 
pool<00:02:52.239><c> you</c><00:02:52.400><c> can</c><00:02:52.560><c> use</c><00:02:52.800><c> the</c><00:02:52.920><c> so</c><00:02:53.280><c> compute</c><00:02:53.640><c> pools</c>

00:02:54.110 --> 00:02:54.120 align:start position:0%
pool you can use the so compute pools
 

00:02:54.120 --> 00:02:56.390 align:start position:0%
pool you can use the so compute pools
command<00:02:54.760><c> to</c><00:02:54.920><c> see</c><00:02:55.239><c> the</c><00:02:55.400><c> compute</c><00:02:55.760><c> pool</c><00:02:56.000><c> started</c>

00:02:56.390 --> 00:02:56.400 align:start position:0%
command to see the compute pool started
 

00:02:56.400 --> 00:03:07.990 align:start position:0%
command to see the compute pool started
by<00:02:56.519><c> the</c><00:02:56.680><c> application</c>

00:03:07.990 --> 00:03:08.000 align:start position:0%
 
 

00:03:08.000 --> 00:03:10.390 align:start position:0%
 
once<00:03:08.280><c> the</c><00:03:08.440><c> service</c><00:03:08.920><c> starts</c><00:03:09.640><c> you</c><00:03:09.760><c> can</c><00:03:09.959><c> use</c><00:03:10.239><c> the</c>

00:03:10.390 --> 00:03:10.400 align:start position:0%
once the service starts you can use the
 

00:03:10.400 --> 00:03:12.630 align:start position:0%
once the service starts you can use the
prediction<00:03:10.840><c> UDF</c><00:03:11.319><c> function</c><00:03:12.040><c> to</c><00:03:12.280><c> start</c>

00:03:12.630 --> 00:03:12.640 align:start position:0%
prediction UDF function to start
 

00:03:12.640 --> 00:03:23.270 align:start position:0%
prediction UDF function to start
quitting<00:03:13.040><c> your</c>

00:03:23.270 --> 00:03:23.280 align:start position:0%
 
 

00:03:23.280 --> 00:03:25.830 align:start position:0%
 
endpoint<00:03:24.280><c> by</c><00:03:24.519><c> default</c><00:03:25.159><c> the</c><00:03:25.319><c> clinical</c>

00:03:25.830 --> 00:03:25.840 align:start position:0%
endpoint by default the clinical
 

00:03:25.840 --> 00:03:28.270 align:start position:0%
endpoint by default the clinical
deidentification<00:03:26.480><c> endpoint</c><00:03:27.360><c> takes</c><00:03:27.760><c> Max</c>

00:03:28.270 --> 00:03:28.280 align:start position:0%
deidentification endpoint takes Max
 

00:03:28.280 --> 00:03:37.869 align:start position:0%
deidentification endpoint takes Max
policy<00:03:29.040><c> to</c><00:03:29.400><c> return</c><00:03:29.879><c> identify</c>

00:03:37.869 --> 00:03:37.879 align:start position:0%
 
 

00:03:37.879 --> 00:03:41.429 align:start position:0%
 
text<00:03:38.879><c> as</c><00:03:39.000><c> you</c><00:03:39.200><c> see</c><00:03:40.040><c> I</c><00:03:40.400><c> passing</c><00:03:40.760><c> some</c><00:03:40.959><c> clinical</c>

00:03:41.429 --> 00:03:41.439 align:start position:0%
text as you see I passing some clinical
 

00:03:41.439 --> 00:03:44.390 align:start position:0%
text as you see I passing some clinical
text<00:03:41.799><c> with</c><00:03:41.959><c> some</c><00:03:42.200><c> PSI</c><00:03:43.080><c> information</c><00:03:44.080><c> and</c><00:03:44.200><c> the</c>

00:03:44.390 --> 00:03:44.400 align:start position:0%
text with some PSI information and the
 

00:03:44.400 --> 00:03:46.710 align:start position:0%
text with some PSI information and the
response<00:03:44.799><c> is</c><00:03:44.959><c> returning</c><00:03:45.439><c> me</c><00:03:45.760><c> a</c><00:03:46.480><c> the</c>

00:03:46.710 --> 00:03:46.720 align:start position:0%
response is returning me a the
 

00:03:46.720 --> 00:03:52.229 align:start position:0%
response is returning me a the
identified<00:03:47.239><c> version</c><00:03:47.640><c> of</c><00:03:47.840><c> that</c>

00:03:52.229 --> 00:03:52.239 align:start position:0%
 
 

00:03:52.239 --> 00:03:55.110 align:start position:0%
 
text<00:03:53.239><c> this</c><00:03:53.439><c> endpoint</c><00:03:54.040><c> takes</c><00:03:54.319><c> masking</c><00:03:54.720><c> policy</c>

00:03:55.110 --> 00:03:55.120 align:start position:0%
text this endpoint takes masking policy
 

00:03:55.120 --> 00:03:57.949 align:start position:0%
text this endpoint takes masking policy
as<00:03:55.239><c> a</c><00:03:55.360><c> secondary</c><00:03:56.239><c> parameter</c><00:03:57.239><c> using</c><00:03:57.560><c> masking</c>

00:03:57.949 --> 00:03:57.959 align:start position:0%
as a secondary parameter using masking
 

00:03:57.959 --> 00:04:01.069 align:start position:0%
as a secondary parameter using masking
policy<00:03:58.840><c> the</c><00:03:58.959><c> endpoint</c><00:03:59.400><c> can</c><00:03:59.879><c> on</c><00:04:00.120><c> different</c>

00:04:01.069 --> 00:04:01.079 align:start position:0%
policy the endpoint can on different
 

00:04:01.079 --> 00:04:07.869 align:start position:0%
policy the endpoint can on different
representation<00:04:01.799><c> of</c><00:04:01.920><c> the</c><00:04:02.120><c> deidentified</c>

00:04:07.869 --> 00:04:07.879 align:start position:0%
 
 

00:04:07.879 --> 00:04:10.910 align:start position:0%
 
text<00:04:08.879><c> you</c><00:04:09.000><c> can</c><00:04:09.239><c> also</c><00:04:09.519><c> send</c><00:04:09.920><c> officiated</c><00:04:10.680><c> as</c><00:04:10.760><c> a</c>

00:04:10.910 --> 00:04:10.920 align:start position:0%
text you can also send officiated as a
 

00:04:10.920 --> 00:04:14.069 align:start position:0%
text you can also send officiated as a
mask<00:04:11.360><c> policy</c><00:04:11.720><c> to</c><00:04:11.959><c> this</c><00:04:12.280><c> Endo</c><00:04:13.280><c> so</c><00:04:13.560><c> that</c><00:04:13.799><c> the</c>

00:04:14.069 --> 00:04:14.079 align:start position:0%
mask policy to this Endo so that the
 

00:04:14.079 --> 00:04:27.749 align:start position:0%
mask policy to this Endo so that the
response<00:04:14.680><c> return</c><00:04:15.040><c> fake</c><00:04:15.519><c> PSI</c>

00:04:27.749 --> 00:04:27.759 align:start position:0%
 
 

00:04:27.759 --> 00:04:30.189 align:start position:0%
 
entities<00:04:28.759><c> besides</c><00:04:29.240><c> the</c><00:04:29.400><c> marks</c><00:04:29.840><c> and</c><00:04:30.039><c> off</c>

00:04:30.189 --> 00:04:30.199 align:start position:0%
entities besides the marks and off
 

00:04:30.199 --> 00:04:33.150 align:start position:0%
entities besides the marks and off
fiscated<00:04:30.720><c> masking</c><00:04:31.240><c> policy</c><00:04:32.240><c> this</c><00:04:32.479><c> endpoint</c>

00:04:33.150 --> 00:04:33.160 align:start position:0%
fiscated masking policy this endpoint
 

00:04:33.160 --> 00:04:35.950 align:start position:0%
fiscated masking policy this endpoint
supports<00:04:33.680><c> two</c><00:04:33.919><c> more</c><00:04:34.240><c> masking</c><00:04:34.759><c> policies</c><00:04:35.759><c> one</c>

00:04:35.950 --> 00:04:35.960 align:start position:0%
supports two more masking policies one
 

00:04:35.960 --> 00:04:37.710 align:start position:0%
supports two more masking policies one
is<00:04:36.160><c> marks</c><00:04:36.520><c> fixed</c><00:04:36.800><c> length</c><00:04:37.080><c> with</c><00:04:37.240><c> charge</c><00:04:37.520><c> and</c>

00:04:37.710 --> 00:04:37.720 align:start position:0%
is marks fixed length with charge and
 

00:04:37.720 --> 00:04:43.710 align:start position:0%
is marks fixed length with charge and
another<00:04:38.039><c> one</c><00:04:38.280><c> marks</c><00:04:38.680><c> with</c>

00:04:43.710 --> 00:04:43.720 align:start position:0%
 
 

00:04:43.720 --> 00:04:47.110 align:start position:0%
 
charge<00:04:44.720><c> so</c><00:04:45.039><c> all</c><00:04:45.199><c> in</c><00:04:45.400><c> all</c><00:04:46.199><c> four</c><00:04:46.720><c> masking</c>

00:04:47.110 --> 00:04:47.120 align:start position:0%
charge so all in all four masking
 

00:04:47.120 --> 00:04:51.070 align:start position:0%
charge so all in all four masking
policies<00:04:47.560><c> are</c>

00:04:51.070 --> 00:04:51.080 align:start position:0%
 
 

00:04:51.080 --> 00:04:53.469 align:start position:0%
 
supported<00:04:52.080><c> both</c><00:04:52.400><c> The</c><00:04:52.560><c> Masks</c><00:04:52.919><c> fixed</c><00:04:53.199><c> length</c>

00:04:53.469 --> 00:04:53.479 align:start position:0%
supported both The Masks fixed length
 

00:04:53.479 --> 00:04:55.870 align:start position:0%
supported both The Masks fixed length
with<00:04:53.680><c> charge</c><00:04:54.039><c> and</c><00:04:54.240><c> Max</c><00:04:54.600><c> charge</c><00:04:55.120><c> transform</c><00:04:55.680><c> the</c>

00:04:55.870 --> 00:04:55.880 align:start position:0%
with charge and Max charge transform the
 

00:04:55.880 --> 00:05:04.870 align:start position:0%
with charge and Max charge transform the
PSI<00:04:56.520><c> into</c><00:04:57.360><c> ASC</c><00:04:57.919><c> characters</c>

00:05:04.870 --> 00:05:04.880 align:start position:0%
 
 

00:05:04.880 --> 00:05:07.029 align:start position:0%
 
this<00:05:05.160><c> function</c><00:05:05.600><c> predition</c><00:05:06.039><c> UDF</c><00:05:06.840><c> that</c>

00:05:07.029 --> 00:05:07.039 align:start position:0%
this function predition UDF that
 

00:05:07.039 --> 00:05:09.110 align:start position:0%
this function predition UDF that
interacts<00:05:07.479><c> with</c><00:05:07.600><c> the</c><00:05:07.759><c> endpoints</c><00:05:08.720><c> can</c><00:05:08.919><c> be</c>

00:05:09.110 --> 00:05:09.120 align:start position:0%
interacts with the endpoints can be
 

00:05:09.120 --> 00:05:11.430 align:start position:0%
interacts with the endpoints can be
called<00:05:09.479><c> on</c><00:05:09.759><c> any</c><00:05:09.960><c> snowflake</c><00:05:10.600><c> tables</c><00:05:11.080><c> via</c><00:05:11.320><c> a</c>

00:05:11.430 --> 00:05:11.440 align:start position:0%
called on any snowflake tables via a
 

00:05:11.440 --> 00:05:12.670 align:start position:0%
called on any snowflake tables via a
select

00:05:12.670 --> 00:05:12.680 align:start position:0%
select
 

00:05:12.680 --> 00:05:15.790 align:start position:0%
select
query<00:05:13.680><c> this</c><00:05:13.840><c> can</c><00:05:14.039><c> be</c><00:05:14.240><c> useful</c><00:05:14.680><c> when</c><00:05:14.840><c> you</c><00:05:15.120><c> have</c><00:05:15.680><c> a</c>

00:05:15.790 --> 00:05:15.800 align:start position:0%
query this can be useful when you have a
 

00:05:15.800 --> 00:05:18.629 align:start position:0%
query this can be useful when you have a
bunch<00:05:16.080><c> of</c><00:05:16.280><c> medical</c><00:05:16.639><c> text</c><00:05:16.960><c> sted</c><00:05:17.320><c> on</c><00:05:17.440><c> a</c><00:05:17.680><c> table</c>

00:05:18.629 --> 00:05:18.639 align:start position:0%
bunch of medical text sted on a table
 

00:05:18.639 --> 00:05:20.110 align:start position:0%
bunch of medical text sted on a table
and<00:05:18.800><c> you</c><00:05:18.960><c> want</c><00:05:19.080><c> to</c><00:05:19.280><c> run</c><00:05:19.520><c> deidentification</c>

00:05:20.110 --> 00:05:20.120 align:start position:0%
and you want to run deidentification
 

00:05:20.120 --> 00:05:31.230 align:start position:0%
and you want to run deidentification
process<00:05:20.880><c> on</c><00:05:21.080><c> it</c>

00:05:31.230 --> 00:05:31.240 align:start position:0%
 
 

00:05:31.240 --> 00:05:33.390 align:start position:0%
 
as<00:05:31.400><c> you</c><00:05:31.520><c> can</c><00:05:31.680><c> see</c><00:05:31.919><c> in</c><00:05:32.080><c> the</c><00:05:32.240><c> example</c><00:05:33.160><c> I</c><00:05:33.240><c> am</c>

00:05:33.390 --> 00:05:33.400 align:start position:0%
as you can see in the example I am
 

00:05:33.400 --> 00:05:35.990 align:start position:0%
as you can see in the example I am
creating<00:05:33.800><c> a</c><00:05:33.960><c> table</c><00:05:34.280><c> called</c><00:05:34.520><c> clinical</c><00:05:35.039><c> text</c>

00:05:35.990 --> 00:05:36.000 align:start position:0%
creating a table called clinical text
 

00:05:36.000 --> 00:05:37.950 align:start position:0%
creating a table called clinical text
and<00:05:36.199><c> I</c><00:05:36.280><c> am</c><00:05:36.440><c> inserting</c><00:05:36.960><c> some</c><00:05:37.199><c> medical</c><00:05:37.560><c> text</c>

00:05:37.950 --> 00:05:37.960 align:start position:0%
and I am inserting some medical text
 

00:05:37.960 --> 00:05:43.070 align:start position:0%
and I am inserting some medical text
into

00:05:43.070 --> 00:05:43.080 align:start position:0%
 
 

00:05:43.080 --> 00:05:45.909 align:start position:0%
 
it<00:05:44.080><c> I'm</c><00:05:44.280><c> also</c><00:05:44.560><c> inserting</c><00:05:44.960><c> a</c><00:05:45.120><c> masking</c><00:05:45.479><c> poliy</c>

00:05:45.909 --> 00:05:45.919 align:start position:0%
it I'm also inserting a masking poliy
 

00:05:45.919 --> 00:05:53.550 align:start position:0%
it I'm also inserting a masking poliy
for<00:05:46.199><c> each</c><00:05:46.520><c> record</c><00:05:47.520><c> that</c><00:05:47.639><c> I</c><00:05:47.800><c> want</c><00:05:48.240><c> to</c><00:05:48.440><c> be</c>

00:05:53.550 --> 00:05:53.560 align:start position:0%
 
 

00:05:53.560 --> 00:05:56.309 align:start position:0%
 
performed<00:05:54.560><c> now</c><00:05:54.840><c> with</c><00:05:54.960><c> a</c><00:05:55.120><c> simple</c><00:05:55.479><c> pred</c><00:05:55.880><c> UTF</c>

00:05:56.309 --> 00:05:56.319 align:start position:0%
performed now with a simple pred UTF
 

00:05:56.319 --> 00:05:58.710 align:start position:0%
performed now with a simple pred UTF
function<00:05:56.639><c> called</c><00:05:56.919><c> to</c><00:05:57.120><c> a</c><00:05:57.319><c> table</c><00:05:58.319><c> we</c><00:05:58.479><c> can</c>

00:05:58.710 --> 00:05:58.720 align:start position:0%
function called to a table we can
 

00:05:58.720 --> 00:06:04.790 align:start position:0%
function called to a table we can
perform<00:05:59.080><c> d</c><00:05:59.680><c> ification</c><00:06:00.319><c> on</c><00:06:00.600><c> multiple</c>

00:06:04.790 --> 00:06:04.800 align:start position:0%
 
 

00:06:04.800 --> 00:06:08.469 align:start position:0%
 
documents<00:06:05.800><c> as</c><00:06:05.919><c> you</c><00:06:06.039><c> can</c><00:06:06.240><c> see</c><00:06:06.520><c> from</c><00:06:06.720><c> the</c>

00:06:08.469 --> 00:06:08.479 align:start position:0%
documents as you can see from the
 

00:06:08.479 --> 00:06:11.029 align:start position:0%
documents as you can see from the
result<00:06:09.479><c> each</c><00:06:09.800><c> medical</c><00:06:10.280><c> text</c><00:06:10.639><c> has</c><00:06:10.800><c> been</c>

00:06:11.029 --> 00:06:11.039 align:start position:0%
result each medical text has been
 

00:06:11.039 --> 00:06:13.670 align:start position:0%
result each medical text has been
deidentified<00:06:11.639><c> using</c><00:06:12.160><c> their</c><00:06:12.400><c> own</c><00:06:12.800><c> masking</c>

00:06:13.670 --> 00:06:13.680 align:start position:0%
deidentified using their own masking
 

00:06:13.680 --> 00:06:16.270 align:start position:0%
deidentified using their own masking
policy<00:06:14.680><c> the</c><00:06:14.840><c> first</c><00:06:15.160><c> document</c><00:06:15.599><c> using</c><00:06:15.840><c> the</c><00:06:15.960><c> MK</c>

00:06:16.270 --> 00:06:16.280 align:start position:0%
policy the first document using the MK
 

00:06:16.280 --> 00:06:18.950 align:start position:0%
policy the first document using the MK
policy<00:06:17.280><c> Returns</c><00:06:17.599><c> the</c><00:06:17.759><c> output</c><00:06:18.280><c> where</c><00:06:18.599><c> all</c><00:06:18.800><c> the</c>

00:06:18.950 --> 00:06:18.960 align:start position:0%
policy Returns the output where all the
 

00:06:18.960 --> 00:06:22.749 align:start position:0%
policy Returns the output where all the
entities<00:06:19.479><c> are</c><00:06:20.319><c> identified</c><00:06:21.000><c> and</c><00:06:21.599><c> labeled</c><00:06:22.599><c> the</c>

00:06:22.749 --> 00:06:22.759 align:start position:0%
entities are identified and labeled the
 

00:06:22.759 --> 00:06:25.270 align:start position:0%
entities are identified and labeled the
second<00:06:23.080><c> aosc</c><00:06:23.720><c> masking</c><00:06:24.039><c> po</c><00:06:24.360><c> fix</c><00:06:24.639><c> the</c><00:06:24.800><c> Phi</c>

00:06:25.270 --> 00:06:25.280 align:start position:0%
second aosc masking po fix the Phi
 

00:06:25.280 --> 00:06:29.990 align:start position:0%
second aosc masking po fix the Phi
entities

00:06:29.990 --> 00:06:30.000 align:start position:0%
 
 

00:06:30.000 --> 00:06:31.990 align:start position:0%
 
the<00:06:30.160><c> max</c><00:06:30.440><c> fixed</c><00:06:30.720><c> length</c><00:06:30.919><c> with</c><00:06:31.080><c> charge</c>

00:06:31.990 --> 00:06:32.000 align:start position:0%
the max fixed length with charge
 

00:06:32.000 --> 00:06:34.230 align:start position:0%
the max fixed length with charge
replaces<00:06:32.639><c> the</c><00:06:32.840><c> phsi</c><00:06:33.280><c> entities</c><00:06:33.680><c> with</c><00:06:33.880><c> s</c>

00:06:34.230 --> 00:06:34.240 align:start position:0%
replaces the phsi entities with s
 

00:06:34.240 --> 00:06:36.950 align:start position:0%
replaces the phsi entities with s
characters<00:06:34.639><c> of</c><00:06:34.840><c> fixed</c>

00:06:36.950 --> 00:06:36.960 align:start position:0%
characters of fixed
 

00:06:36.960 --> 00:06:40.110 align:start position:0%
characters of fixed
length<00:06:37.960><c> and</c><00:06:38.120><c> Max</c><00:06:38.520><c> with</c><00:06:38.960><c> replaces</c><00:06:39.479><c> the</c><00:06:39.639><c> Phi</c>

00:06:40.110 --> 00:06:40.120 align:start position:0%
length and Max with replaces the Phi
 

00:06:40.120 --> 00:06:52.550 align:start position:0%
length and Max with replaces the Phi
entities<00:06:40.599><c> with</c><00:06:40.759><c> ASC</c><00:06:41.199><c> characters</c><00:06:41.599><c> of</c><00:06:41.840><c> uneven</c>

00:06:52.550 --> 00:06:52.560 align:start position:0%
 
 

00:06:52.560 --> 00:06:55.350 align:start position:0%
 
length<00:06:53.560><c> the</c><00:06:53.680><c> endpoints</c><00:06:54.199><c> are</c><00:06:54.440><c> powered</c><00:06:54.840><c> by</c><00:06:54.960><c> scop</c>

00:06:55.350 --> 00:06:55.360 align:start position:0%
length the endpoints are powered by scop
 

00:06:55.360 --> 00:06:56.550 align:start position:0%
length the endpoints are powered by scop
up<00:06:55.560><c> container</c>

00:06:56.550 --> 00:06:56.560 align:start position:0%
up container
 

00:06:56.560 --> 00:06:59.230 align:start position:0%
up container
services<00:06:57.560><c> so</c><00:06:57.759><c> you</c><00:06:57.960><c> get</c><00:06:58.120><c> the</c><00:06:58.280><c> full</c><00:06:58.599><c> benefits</c><00:06:59.080><c> in</c>

00:06:59.230 --> 00:06:59.240 align:start position:0%
services so you get the full benefits in
 

00:06:59.240 --> 00:07:01.589 align:start position:0%
services so you get the full benefits in
terms<00:06:59.680><c> of</c><00:06:59.840><c> resource</c><00:07:00.199><c> allocation</c><00:07:00.840><c> and</c><00:07:01.039><c> scaling</c>

00:07:01.589 --> 00:07:01.599 align:start position:0%
terms of resource allocation and scaling
 

00:07:01.599 --> 00:07:04.309 align:start position:0%
terms of resource allocation and scaling
that<00:07:01.759><c> snowflake</c><00:07:02.400><c> provides</c><00:07:02.800><c> out</c><00:07:02.960><c> of</c><00:07:03.120><c> the</c>

00:07:04.309 --> 00:07:04.319 align:start position:0%
that snowflake provides out of the
 

00:07:04.319 --> 00:07:07.270 align:start position:0%
that snowflake provides out of the
box<00:07:05.319><c> snowflake</c><00:07:06.120><c> automatically</c><00:07:06.759><c> tracks</c><00:07:07.160><c> the</c>

00:07:07.270 --> 00:07:07.280 align:start position:0%
box snowflake automatically tracks the
 

00:07:07.280 --> 00:07:10.230 align:start position:0%
box snowflake automatically tracks the
uses<00:07:07.680><c> of</c><00:07:07.800><c> the</c><00:07:08.120><c> endpoint</c><00:07:09.120><c> and</c><00:07:09.319><c> charges</c><00:07:09.800><c> you</c><00:07:10.000><c> for</c>

00:07:10.230 --> 00:07:10.240 align:start position:0%
uses of the endpoint and charges you for
 

00:07:10.240 --> 00:07:12.029 align:start position:0%
uses of the endpoint and charges you for
the<00:07:10.400><c> infrastructure</c><00:07:11.080><c> cost</c><00:07:11.520><c> as</c><00:07:11.639><c> well</c><00:07:11.800><c> as</c><00:07:11.919><c> the</c>

00:07:12.029 --> 00:07:12.039 align:start position:0%
the infrastructure cost as well as the
 

00:07:12.039 --> 00:07:14.749 align:start position:0%
the infrastructure cost as well as the
application<00:07:12.800><c> cost</c><00:07:13.800><c> so</c><00:07:14.080><c> once</c><00:07:14.280><c> you</c><00:07:14.400><c> are</c><00:07:14.560><c> done</c>

00:07:14.749 --> 00:07:14.759 align:start position:0%
application cost so once you are done
 

00:07:14.759 --> 00:07:16.950 align:start position:0%
application cost so once you are done
using<00:07:15.120><c> the</c><00:07:15.240><c> endpoints</c><00:07:15.960><c> it</c><00:07:16.160><c> might</c><00:07:16.360><c> be</c><00:07:16.560><c> wise</c><00:07:16.800><c> to</c>

00:07:16.950 --> 00:07:16.960 align:start position:0%
using the endpoints it might be wise to
 

00:07:16.960 --> 00:07:18.350 align:start position:0%
using the endpoints it might be wise to
stop

00:07:18.350 --> 00:07:18.360 align:start position:0%
stop
 

00:07:18.360 --> 00:07:21.550 align:start position:0%
stop
it<00:07:19.360><c> this</c><00:07:19.599><c> can</c><00:07:19.800><c> be</c><00:07:20.039><c> done</c><00:07:20.520><c> by</c><00:07:21.000><c> calling</c><00:07:21.400><c> the</c>

00:07:21.550 --> 00:07:21.560 align:start position:0%
it this can be done by calling the
 

00:07:21.560 --> 00:07:23.830 align:start position:0%
it this can be done by calling the
procedure<00:07:22.080><c> stop</c>

00:07:23.830 --> 00:07:23.840 align:start position:0%
procedure stop
 

00:07:23.840 --> 00:07:26.950 align:start position:0%
procedure stop
app<00:07:24.840><c> this</c><00:07:25.000><c> will</c><00:07:25.240><c> stop</c><00:07:25.720><c> all</c><00:07:25.960><c> the</c><00:07:26.160><c> services</c><00:07:26.759><c> and</c>

00:07:26.950 --> 00:07:26.960 align:start position:0%
app this will stop all the services and
 

00:07:26.960 --> 00:07:29.230 align:start position:0%
app this will stop all the services and
compute<00:07:27.360><c> pools</c><00:07:27.680><c> started</c><00:07:28.080><c> by</c><00:07:28.240><c> the</c><00:07:28.400><c> application</c>

00:07:29.230 --> 00:07:29.240 align:start position:0%
compute pools started by the application
 

00:07:29.240 --> 00:07:32.070 align:start position:0%
compute pools started by the application
so<00:07:29.520><c> so</c><00:07:29.680><c> you</c><00:07:29.800><c> are</c><00:07:29.960><c> not</c><00:07:30.240><c> charged</c>

00:07:32.070 --> 00:07:32.080 align:start position:0%
so so you are not charged
 

00:07:32.080 --> 00:07:36.319 align:start position:0%
so so you are not charged
needlessly<00:07:33.080><c> thank</c><00:07:33.319><c> you</c>

