WEBVTT
Kind: captions
Language: en

00:00:00.960 --> 00:00:03.889 align:start position:0%
 
python<00:00:01.740><c> support</c><00:00:01.979><c> is</c><00:00:02.280><c> coming</c><00:00:02.460><c> to</c><00:00:02.639><c> excel</c><00:00:03.060><c> python</c>

00:00:03.889 --> 00:00:03.899 align:start position:0%
python support is coming to excel python
 

00:00:03.899 --> 00:00:06.170 align:start position:0%
python support is coming to excel python
support<00:00:04.200><c> is</c><00:00:04.500><c> coming</c><00:00:04.680><c> to</c><00:00:04.860><c> excel</c><00:00:05.160><c> this</c><00:00:05.520><c> is</c><00:00:05.759><c> not</c><00:00:05.940><c> a</c>

00:00:06.170 --> 00:00:06.180 align:start position:0%
support is coming to excel this is not a
 

00:00:06.180 --> 00:00:08.629 align:start position:0%
support is coming to excel this is not a
drill<00:00:06.420><c> we've</c><00:00:07.200><c> also</c><00:00:07.440><c> got</c><00:00:07.560><c> groups</c><00:00:08.099><c> depend</c><00:00:08.400><c> upon</c>

00:00:08.629 --> 00:00:08.639 align:start position:0%
drill we've also got groups depend upon
 

00:00:08.639 --> 00:00:10.669 align:start position:0%
drill we've also got groups depend upon
updates<00:00:09.000><c> and</c><00:00:09.360><c> a</c><00:00:09.599><c> great</c><00:00:09.660><c> new</c><00:00:09.840><c> coding</c><00:00:10.139><c> font</c><00:00:10.380><c> from</c>

00:00:10.669 --> 00:00:10.679 align:start position:0%
updates and a great new coding font from
 

00:00:10.679 --> 00:00:12.830 align:start position:0%
updates and a great new coding font from
a<00:00:10.800><c> surprising</c><00:00:11.219><c> Source</c><00:00:11.580><c> all</c><00:00:12.179><c> that</c><00:00:12.300><c> and</c><00:00:12.480><c> more</c><00:00:12.599><c> on</c>

00:00:12.830 --> 00:00:12.840 align:start position:0%
a surprising Source all that and more on
 

00:00:12.840 --> 00:00:16.480 align:start position:0%
a surprising Source all that and more on
this<00:00:12.960><c> episode</c><00:00:13.139><c> of</c><00:00:13.559><c> the</c><00:00:13.920><c> download</c>

00:00:16.480 --> 00:00:16.490 align:start position:0%
 
 

00:00:16.490 --> 00:00:19.570 align:start position:0%
 
[Music]

00:00:19.570 --> 00:00:19.580 align:start position:0%
[Music]
 

00:00:19.580 --> 00:00:21.830 align:start position:0%
[Music]
welcome<00:00:20.580><c> back</c><00:00:20.760><c> to</c><00:00:20.880><c> another</c><00:00:21.000><c> episode</c><00:00:21.300><c> of</c><00:00:21.720><c> the</c>

00:00:21.830 --> 00:00:21.840 align:start position:0%
welcome back to another episode of the
 

00:00:21.840 --> 00:00:23.570 align:start position:0%
welcome back to another episode of the
download<00:00:21.960><c> I'm</c><00:00:22.380><c> your</c><00:00:22.560><c> host</c><00:00:22.680><c> Christina</c><00:00:23.160><c> Warren</c>

00:00:23.570 --> 00:00:23.580 align:start position:0%
download I'm your host Christina Warren
 

00:00:23.580 --> 00:00:25.730 align:start position:0%
download I'm your host Christina Warren
senior<00:00:23.880><c> developer</c><00:00:24.240><c> Advocate</c><00:00:24.600><c> at</c><00:00:24.840><c> GitHub</c><00:00:25.260><c> and</c>

00:00:25.730 --> 00:00:25.740 align:start position:0%
senior developer Advocate at GitHub and
 

00:00:25.740 --> 00:00:27.349 align:start position:0%
senior developer Advocate at GitHub and
this<00:00:26.220><c> is</c><00:00:26.279><c> the</c><00:00:26.460><c> show</c><00:00:26.640><c> where</c><00:00:26.820><c> we</c><00:00:27.000><c> cover</c><00:00:27.119><c> the</c>

00:00:27.349 --> 00:00:27.359 align:start position:0%
this is the show where we cover the
 

00:00:27.359 --> 00:00:29.210 align:start position:0%
this is the show where we cover the
latest<00:00:27.480><c> developer</c><00:00:28.080><c> news</c><00:00:28.320><c> and</c><00:00:28.619><c> open</c><00:00:28.800><c> source</c>

00:00:29.210 --> 00:00:29.220 align:start position:0%
latest developer news and open source
 

00:00:29.220 --> 00:00:31.490 align:start position:0%
latest developer news and open source
projects<00:00:30.119><c> please</c><00:00:30.599><c> like</c><00:00:30.779><c> And</c><00:00:30.960><c> subscribe</c><00:00:31.260><c> do</c>

00:00:31.490 --> 00:00:31.500 align:start position:0%
projects please like And subscribe do
 

00:00:31.500 --> 00:00:33.290 align:start position:0%
projects please like And subscribe do
the<00:00:31.740><c> things</c><00:00:31.859><c> you</c><00:00:32.099><c> know</c><00:00:32.220><c> what</c><00:00:32.340><c> to</c><00:00:32.460><c> do</c><00:00:32.579><c> my</c><00:00:33.180><c> shirt</c>

00:00:33.290 --> 00:00:33.300 align:start position:0%
the things you know what to do my shirt
 

00:00:33.300 --> 00:00:35.750 align:start position:0%
the things you know what to do my shirt
this<00:00:33.540><c> week</c><00:00:33.719><c> is</c><00:00:34.140><c> a</c><00:00:34.559><c> social</c><00:00:34.739><c> coding</c><00:00:35.219><c> throwback</c>

00:00:35.750 --> 00:00:35.760 align:start position:0%
this week is a social coding throwback
 

00:00:35.760 --> 00:00:37.970 align:start position:0%
this week is a social coding throwback
shirt<00:00:36.059><c> social</c><00:00:36.840><c> coating</c><00:00:37.200><c> was</c><00:00:37.440><c> github's</c>

00:00:37.970 --> 00:00:37.980 align:start position:0%
shirt social coating was github's
 

00:00:37.980 --> 00:00:40.430 align:start position:0%
shirt social coating was github's
tagline<00:00:38.340><c> back</c><00:00:38.700><c> when</c><00:00:38.820><c> it</c><00:00:39.000><c> launched</c><00:00:39.360><c> 15</c><00:00:40.020><c> years</c>

00:00:40.430 --> 00:00:40.440 align:start position:0%
tagline back when it launched 15 years
 

00:00:40.440 --> 00:00:42.950 align:start position:0%
tagline back when it launched 15 years
ago<00:00:40.800><c> God</c><00:00:41.460><c> we</c><00:00:41.700><c> are</c><00:00:41.820><c> we</c><00:00:42.059><c> are</c><00:00:42.180><c> old</c>

00:00:42.950 --> 00:00:42.960 align:start position:0%
ago God we are we are old
 

00:00:42.960 --> 00:00:45.770 align:start position:0%
ago God we are we are old
let's<00:00:43.500><c> get</c><00:00:43.620><c> into</c><00:00:43.800><c> the</c><00:00:44.040><c> news</c><00:00:44.219><c> so</c><00:00:44.820><c> we</c><00:00:45.180><c> are</c><00:00:45.360><c> in</c><00:00:45.600><c> the</c>

00:00:45.770 --> 00:00:45.780 align:start position:0%
let's get into the news so we are in the
 

00:00:45.780 --> 00:00:47.330 align:start position:0%
let's get into the news so we are in the
dog<00:00:45.960><c> days</c><00:00:46.260><c> of</c><00:00:46.500><c> summer</c><00:00:46.680><c> in</c><00:00:46.980><c> the</c><00:00:47.100><c> northern</c>

00:00:47.330 --> 00:00:47.340 align:start position:0%
dog days of summer in the northern
 

00:00:47.340 --> 00:00:49.369 align:start position:0%
dog days of summer in the northern
hemisphere<00:00:47.760><c> which</c><00:00:48.180><c> means</c><00:00:48.480><c> that</c><00:00:48.719><c> fall</c><00:00:49.079><c> and</c>

00:00:49.369 --> 00:00:49.379 align:start position:0%
hemisphere which means that fall and
 

00:00:49.379 --> 00:00:51.110 align:start position:0%
hemisphere which means that fall and
more<00:00:49.620><c> importantly</c><00:00:49.920><c> GitHub</c><00:00:50.399><c> Universe</c><00:00:50.820><c> are</c>

00:00:51.110 --> 00:00:51.120 align:start position:0%
more importantly GitHub Universe are
 

00:00:51.120 --> 00:00:53.090 align:start position:0%
more importantly GitHub Universe are
just<00:00:51.420><c> around</c><00:00:51.660><c> the</c><00:00:51.840><c> corner</c><00:00:52.079><c> so</c><00:00:52.680><c> just</c><00:00:52.980><c> a</c>

00:00:53.090 --> 00:00:53.100 align:start position:0%
just around the corner so just a
 

00:00:53.100 --> 00:00:54.650 align:start position:0%
just around the corner so just a
reminder<00:00:53.219><c> that</c><00:00:53.640><c> you</c><00:00:53.760><c> can</c><00:00:53.940><c> get</c><00:00:54.120><c> your</c><00:00:54.420><c> super</c>

00:00:54.650 --> 00:00:54.660 align:start position:0%
reminder that you can get your super
 

00:00:54.660 --> 00:00:56.049 align:start position:0%
reminder that you can get your super
early<00:00:54.899><c> bird</c><00:00:55.140><c> tickets</c><00:00:55.440><c> now</c><00:00:55.800><c> at</c>

00:00:56.049 --> 00:00:56.059 align:start position:0%
early bird tickets now at
 

00:00:56.059 --> 00:00:58.189 align:start position:0%
early bird tickets now at
githubuniverse.com<00:00:57.059><c> uh</c><00:00:57.840><c> it's</c><00:00:57.960><c> going</c><00:00:58.079><c> to</c><00:00:58.140><c> be</c>

00:00:58.189 --> 00:00:58.199 align:start position:0%
githubuniverse.com uh it's going to be
 

00:00:58.199 --> 00:01:01.010 align:start position:0%
githubuniverse.com uh it's going to be
November<00:00:58.739><c> 8th</c><00:00:59.340><c> through</c><00:00:59.460><c> 10th</c><00:00:59.940><c> 2023</c><00:01:00.660><c> at</c><00:01:00.899><c> the</c>

00:01:01.010 --> 00:01:01.020 align:start position:0%
November 8th through 10th 2023 at the
 

00:01:01.020 --> 00:01:03.110 align:start position:0%
November 8th through 10th 2023 at the
Europe<00:01:01.140><c> awareness</c><00:01:01.559><c> of</c><00:01:01.739><c> the</c><00:01:01.920><c> Arts</c><00:01:02.219><c> links</c><00:01:02.940><c> down</c>

00:01:03.110 --> 00:01:03.120 align:start position:0%
Europe awareness of the Arts links down
 

00:01:03.120 --> 00:01:05.630 align:start position:0%
Europe awareness of the Arts links down
below<00:01:03.539><c> okay</c><00:01:04.440><c> so</c><00:01:04.860><c> I'm</c><00:01:05.040><c> not</c><00:01:05.159><c> going</c><00:01:05.280><c> to</c><00:01:05.339><c> give</c><00:01:05.460><c> a</c>

00:01:05.630 --> 00:01:05.640 align:start position:0%
below okay so I'm not going to give a
 

00:01:05.640 --> 00:01:07.789 align:start position:0%
below okay so I'm not going to give a
big<00:01:05.760><c> Preamble</c><00:01:06.299><c> the</c><00:01:06.659><c> big</c><00:01:06.840><c> news</c><00:01:07.020><c> this</c><00:01:07.320><c> week</c><00:01:07.500><c> is</c>

00:01:07.789 --> 00:01:07.799 align:start position:0%
big Preamble the big news this week is
 

00:01:07.799 --> 00:01:09.590 align:start position:0%
big Preamble the big news this week is
something<00:01:07.979><c> that</c><00:01:08.400><c> I</c><00:01:08.640><c> know</c><00:01:08.760><c> that</c><00:01:09.119><c> many</c><00:01:09.299><c> of</c><00:01:09.479><c> us</c>

00:01:09.590 --> 00:01:09.600 align:start position:0%
something that I know that many of us
 

00:01:09.600 --> 00:01:13.190 align:start position:0%
something that I know that many of us
have<00:01:10.200><c> been</c><00:01:10.380><c> wishing</c><00:01:10.740><c> for</c><00:01:10.979><c> for</c><00:01:11.640><c> years</c><00:01:12.180><c> and</c><00:01:13.020><c> I</c>

00:01:13.190 --> 00:01:13.200 align:start position:0%
have been wishing for for years and I
 

00:01:13.200 --> 00:01:14.750 align:start position:0%
have been wishing for for years and I
cannot<00:01:13.619><c> believe</c><00:01:13.799><c> that</c><00:01:14.100><c> it's</c><00:01:14.340><c> actually</c><00:01:14.520><c> a</c>

00:01:14.750 --> 00:01:14.760 align:start position:0%
cannot believe that it's actually a
 

00:01:14.760 --> 00:01:17.510 align:start position:0%
cannot believe that it's actually a
reality<00:01:15.119><c> that's</c><00:01:15.720><c> right</c><00:01:16.020><c> python</c><00:01:16.740><c> one</c><00:01:17.460><c> of</c><00:01:17.520><c> the</c>

00:01:17.510 --> 00:01:17.520 align:start position:0%
reality that's right python one of the
 

00:01:17.520 --> 00:01:19.310 align:start position:0%
reality that's right python one of the
most<00:01:17.700><c> beloved</c><00:01:18.000><c> programming</c><00:01:18.420><c> languages</c><00:01:18.780><c> ever</c>

00:01:19.310 --> 00:01:19.320 align:start position:0%
most beloved programming languages ever
 

00:01:19.320 --> 00:01:21.469 align:start position:0%
most beloved programming languages ever
is<00:01:19.860><c> coming</c><00:01:19.979><c> to</c><00:01:20.220><c> excel</c><00:01:20.580><c> which</c><00:01:20.880><c> in</c><00:01:21.060><c> my</c><00:01:21.180><c> opinion</c>

00:01:21.469 --> 00:01:21.479 align:start position:0%
is coming to excel which in my opinion
 

00:01:21.479 --> 00:01:23.149 align:start position:0%
is coming to excel which in my opinion
is<00:01:21.720><c> the</c><00:01:21.960><c> greatest</c><00:01:22.080><c> app</c><00:01:22.439><c> that</c><00:01:22.860><c> has</c><00:01:22.979><c> ever</c>

00:01:23.149 --> 00:01:23.159 align:start position:0%
is the greatest app that has ever
 

00:01:23.159 --> 00:01:26.570 align:start position:0%
is the greatest app that has ever
existed<00:01:23.580><c> and</c><00:01:24.420><c> even</c><00:01:24.600><c> cooler</c><00:01:25.080><c> Guido</c><00:01:25.979><c> yes</c><00:01:26.280><c> that</c>

00:01:26.570 --> 00:01:26.580 align:start position:0%
existed and even cooler Guido yes that
 

00:01:26.580 --> 00:01:28.310 align:start position:0%
existed and even cooler Guido yes that
Guido<00:01:27.000><c> van</c><00:01:27.119><c> awesome</c><00:01:27.360><c> who</c><00:01:27.840><c> you</c><00:01:28.080><c> know</c><00:01:28.140><c> the</c>

00:01:28.310 --> 00:01:28.320 align:start position:0%
Guido van awesome who you know the
 

00:01:28.320 --> 00:01:31.249 align:start position:0%
Guido van awesome who you know the
created<00:01:28.619><c> python</c><00:01:29.240><c> worked</c><00:01:30.240><c> on</c><00:01:30.420><c> bringing</c><00:01:30.900><c> this</c>

00:01:31.249 --> 00:01:31.259 align:start position:0%
created python worked on bringing this
 

00:01:31.259 --> 00:01:34.070 align:start position:0%
created python worked on bringing this
support<00:01:31.500><c> together</c><00:01:31.799><c> which</c><00:01:32.280><c> is</c><00:01:32.460><c> astounding</c><00:01:33.360><c> so</c>

00:01:34.070 --> 00:01:34.080 align:start position:0%
support together which is astounding so
 

00:01:34.080 --> 00:01:35.929 align:start position:0%
support together which is astounding so
this<00:01:34.439><c> is</c><00:01:34.560><c> in</c><00:01:34.740><c> public</c><00:01:34.860><c> preview</c><00:01:35.280><c> for</c><00:01:35.700><c> the</c>

00:01:35.929 --> 00:01:35.939 align:start position:0%
this is in public preview for the
 

00:01:35.939 --> 00:01:38.630 align:start position:0%
this is in public preview for the
Insiders<00:01:36.420><c> beta</c><00:01:36.900><c> channel</c><00:01:37.020><c> for</c><00:01:37.619><c> Microsoft</c><00:01:38.040><c> 365</c>

00:01:38.630 --> 00:01:38.640 align:start position:0%
Insiders beta channel for Microsoft 365
 

00:01:38.640 --> 00:01:41.710 align:start position:0%
Insiders beta channel for Microsoft 365
right<00:01:38.880><c> now</c><00:01:39.060><c> and</c><00:01:39.960><c> support</c><00:01:40.200><c> is</c><00:01:40.500><c> only</c><00:01:40.740><c> on</c><00:01:41.040><c> Windows</c>

00:01:41.710 --> 00:01:41.720 align:start position:0%
right now and support is only on Windows
 

00:01:41.720 --> 00:01:43.910 align:start position:0%
right now and support is only on Windows
but<00:01:42.720><c> it</c><00:01:42.960><c> it's</c><00:01:43.259><c> going</c><00:01:43.380><c> to</c><00:01:43.439><c> be</c><00:01:43.500><c> coming</c><00:01:43.680><c> to</c><00:01:43.799><c> other</c>

00:01:43.910 --> 00:01:43.920 align:start position:0%
but it it's going to be coming to other
 

00:01:43.920 --> 00:01:45.890 align:start position:0%
but it it's going to be coming to other
Platforms<00:01:44.340><c> in</c><00:01:44.460><c> the</c><00:01:44.640><c> future</c><00:01:44.700><c> that</c><00:01:45.360><c> said</c><00:01:45.600><c> this</c>

00:01:45.890 --> 00:01:45.900 align:start position:0%
Platforms in the future that said this
 

00:01:45.900 --> 00:01:47.510 align:start position:0%
Platforms in the future that said this
is<00:01:46.020><c> a</c><00:01:46.200><c> really</c><00:01:46.380><c> cool</c><00:01:46.560><c> thing</c><00:01:46.860><c> and</c><00:01:47.100><c> the</c><00:01:47.280><c> way</c><00:01:47.340><c> that</c>

00:01:47.510 --> 00:01:47.520 align:start position:0%
is a really cool thing and the way that
 

00:01:47.520 --> 00:01:49.370 align:start position:0%
is a really cool thing and the way that
the<00:01:47.700><c> integration</c><00:01:48.119><c> is</c><00:01:48.600><c> happening</c><00:01:48.900><c> is</c><00:01:49.200><c> really</c>

00:01:49.370 --> 00:01:49.380 align:start position:0%
the integration is happening is really
 

00:01:49.380 --> 00:01:51.950 align:start position:0%
the integration is happening is really
slick<00:01:49.680><c> so</c><00:01:50.159><c> how</c><00:01:50.700><c> Python</c><00:01:51.060><c> and</c><00:01:51.180><c> Excel</c><00:01:51.479><c> works</c><00:01:51.780><c> is</c>

00:01:51.950 --> 00:01:51.960 align:start position:0%
slick so how Python and Excel works is
 

00:01:51.960 --> 00:01:53.990 align:start position:0%
slick so how Python and Excel works is
that<00:01:52.200><c> the</c><00:01:52.680><c> calculations</c><00:01:53.100><c> will</c><00:01:53.460><c> take</c><00:01:53.640><c> place</c><00:01:53.820><c> in</c>

00:01:53.990 --> 00:01:54.000 align:start position:0%
that the calculations will take place in
 

00:01:54.000 --> 00:01:55.969 align:start position:0%
that the calculations will take place in
the<00:01:54.119><c> cloud</c><00:01:54.299><c> running</c><00:01:54.960><c> a</c><00:01:55.320><c> standard</c><00:01:55.560><c> version</c><00:01:55.740><c> of</c>

00:01:55.969 --> 00:01:55.979 align:start position:0%
the cloud running a standard version of
 

00:01:55.979 --> 00:01:58.490 align:start position:0%
the cloud running a standard version of
python<00:01:56.340><c> and</c><00:01:57.119><c> Python</c><00:01:57.720><c> and</c><00:01:57.899><c> Excel</c><00:01:58.200><c> is</c><00:01:58.320><c> then</c>

00:01:58.490 --> 00:01:58.500 align:start position:0%
python and Python and Excel is then
 

00:01:58.500 --> 00:02:00.050 align:start position:0%
python and Python and Excel is then
going<00:01:58.740><c> to</c><00:01:58.860><c> come</c><00:01:58.979><c> like</c><00:01:59.159><c> a</c><00:01:59.340><c> core</c><00:01:59.460><c> set</c><00:01:59.640><c> of</c><00:01:59.759><c> python</c>

00:02:00.050 --> 00:02:00.060 align:start position:0%
going to come like a core set of python
 

00:02:00.060 --> 00:02:01.969 align:start position:0%
going to come like a core set of python
libraries<00:02:00.479><c> this</c><00:02:00.899><c> is</c><00:02:00.960><c> powered</c><00:02:01.259><c> by</c><00:02:01.320><c> anaconda</c>

00:02:01.969 --> 00:02:01.979 align:start position:0%
libraries this is powered by anaconda
 

00:02:01.979 --> 00:02:03.950 align:start position:0%
libraries this is powered by anaconda
and<00:02:02.460><c> it's</c><00:02:02.520><c> using</c><00:02:02.939><c> a</c><00:02:03.119><c> standard</c><00:02:03.659><c> secure</c>

00:02:03.950 --> 00:02:03.960 align:start position:0%
and it's using a standard secure
 

00:02:03.960 --> 00:02:05.810 align:start position:0%
and it's using a standard secure
distribution<00:02:04.380><c> and</c><00:02:04.979><c> those</c><00:02:05.159><c> core</c><00:02:05.340><c> libraries</c>

00:02:05.810 --> 00:02:05.820 align:start position:0%
distribution and those core libraries
 

00:02:05.820 --> 00:02:07.809 align:start position:0%
distribution and those core libraries
include<00:02:06.119><c> things</c><00:02:06.299><c> like</c><00:02:06.479><c> numpy</c><00:02:07.079><c> pandas</c>

00:02:07.809 --> 00:02:07.819 align:start position:0%
include things like numpy pandas
 

00:02:07.819 --> 00:02:10.669 align:start position:0%
include things like numpy pandas
matplotlib<00:02:08.819><c> and</c><00:02:09.360><c> Seaborn</c><00:02:09.840><c> and</c><00:02:10.259><c> you</c><00:02:10.440><c> can</c><00:02:10.500><c> also</c>

00:02:10.669 --> 00:02:10.679 align:start position:0%
matplotlib and Seaborn and you can also
 

00:02:10.679 --> 00:02:12.710 align:start position:0%
matplotlib and Seaborn and you can also
import<00:02:10.860><c> other</c><00:02:11.160><c> libraries</c><00:02:11.700><c> using</c><00:02:12.180><c> Anaconda</c>

00:02:12.710 --> 00:02:12.720 align:start position:0%
import other libraries using Anaconda
 

00:02:12.720 --> 00:02:15.110 align:start position:0%
import other libraries using Anaconda
which<00:02:12.900><c> is</c><00:02:12.959><c> cool</c><00:02:13.160><c> so</c><00:02:14.160><c> how</c><00:02:14.459><c> it</c><00:02:14.580><c> works</c><00:02:14.819><c> is</c><00:02:15.000><c> that</c>

00:02:15.110 --> 00:02:15.120 align:start position:0%
which is cool so how it works is that
 

00:02:15.120 --> 00:02:17.210 align:start position:0%
which is cool so how it works is that
you<00:02:15.300><c> can</c><00:02:15.360><c> type</c><00:02:15.540><c> python</c><00:02:16.140><c> directly</c><00:02:16.560><c> into</c><00:02:16.860><c> a</c><00:02:17.040><c> cell</c>

00:02:17.210 --> 00:02:17.220 align:start position:0%
you can type python directly into a cell
 

00:02:17.220 --> 00:02:19.250 align:start position:0%
you can type python directly into a cell
and<00:02:17.580><c> then</c><00:02:17.760><c> those</c><00:02:18.120><c> python</c><00:02:18.540><c> calculations</c><00:02:18.959><c> run</c>

00:02:19.250 --> 00:02:19.260 align:start position:0%
and then those python calculations run
 

00:02:19.260 --> 00:02:21.229 align:start position:0%
and then those python calculations run
in<00:02:19.500><c> the</c><00:02:19.680><c> cloud</c><00:02:19.800><c> and</c><00:02:20.520><c> your</c><00:02:20.760><c> results</c><00:02:20.940><c> are</c>

00:02:21.229 --> 00:02:21.239 align:start position:0%
in the cloud and your results are
 

00:02:21.239 --> 00:02:22.550 align:start position:0%
in the cloud and your results are
returned<00:02:21.660><c> to</c><00:02:21.720><c> the</c><00:02:21.840><c> worksheet</c><00:02:22.260><c> and</c><00:02:22.440><c> that</c>

00:02:22.550 --> 00:02:22.560 align:start position:0%
returned to the worksheet and that
 

00:02:22.560 --> 00:02:24.830 align:start position:0%
returned to the worksheet and that
includes<00:02:23.040><c> plus</c><00:02:23.459><c> and</c><00:02:23.760><c> visualizations</c><00:02:24.360><c> and</c>

00:02:24.830 --> 00:02:24.840 align:start position:0%
includes plus and visualizations and
 

00:02:24.840 --> 00:02:26.570 align:start position:0%
includes plus and visualizations and
this<00:02:25.080><c> is</c><00:02:25.140><c> really</c><00:02:25.319><c> great</c><00:02:25.560><c> because</c><00:02:25.800><c> you</c><00:02:26.459><c> don't</c>

00:02:26.570 --> 00:02:26.580 align:start position:0%
this is really great because you don't
 

00:02:26.580 --> 00:02:27.949 align:start position:0%
this is really great because you don't
have<00:02:26.700><c> to</c><00:02:26.819><c> configure</c><00:02:27.120><c> a</c><00:02:27.300><c> local</c><00:02:27.420><c> version</c><00:02:27.660><c> of</c>

00:02:27.949 --> 00:02:27.959 align:start position:0%
have to configure a local version of
 

00:02:27.959 --> 00:02:30.350 align:start position:0%
have to configure a local version of
python<00:02:28.440><c> and</c><00:02:28.920><c> since</c><00:02:29.099><c> it's</c><00:02:29.220><c> cloud-based</c><00:02:29.940><c> you</c>

00:02:30.350 --> 00:02:30.360 align:start position:0%
python and since it's cloud-based you
 

00:02:30.360 --> 00:02:31.850 align:start position:0%
python and since it's cloud-based you
can<00:02:30.420><c> kind</c><00:02:30.599><c> of</c><00:02:30.720><c> see</c><00:02:30.840><c> a</c><00:02:31.020><c> world</c><00:02:31.200><c> where</c><00:02:31.500><c> you</c><00:02:31.800><c> know</c>

00:02:31.850 --> 00:02:31.860 align:start position:0%
can kind of see a world where you know
 

00:02:31.860 --> 00:02:33.470 align:start position:0%
can kind of see a world where you know
you<00:02:31.980><c> could</c><00:02:32.099><c> use</c><00:02:32.280><c> this</c><00:02:32.459><c> little</c><00:02:32.580><c> web</c><00:02:32.819><c> browser</c><00:02:33.239><c> or</c>

00:02:33.470 --> 00:02:33.480 align:start position:0%
you could use this little web browser or
 

00:02:33.480 --> 00:02:36.589 align:start position:0%
you could use this little web browser or
a<00:02:33.720><c> client</c><00:02:33.959><c> like</c><00:02:34.140><c> an</c><00:02:34.319><c> iPad</c><00:02:35.300><c> possibilities</c><00:02:36.300><c> are</c>

00:02:36.589 --> 00:02:36.599 align:start position:0%
a client like an iPad possibilities are
 

00:02:36.599 --> 00:02:38.449 align:start position:0%
a client like an iPad possibilities are
really<00:02:36.720><c> really</c><00:02:36.900><c> huge</c><00:02:37.319><c> especially</c><00:02:37.980><c> for</c><00:02:38.160><c> data</c>

00:02:38.449 --> 00:02:38.459 align:start position:0%
really really huge especially for data
 

00:02:38.459 --> 00:02:41.089 align:start position:0%
really really huge especially for data
scientists<00:02:38.940><c> and</c><00:02:39.599><c> the</c><00:02:40.260><c> team</c><00:02:40.440><c> is</c><00:02:40.680><c> actively</c>

00:02:41.089 --> 00:02:41.099 align:start position:0%
scientists and the team is actively
 

00:02:41.099 --> 00:02:42.770 align:start position:0%
scientists and the team is actively
taking<00:02:41.280><c> feedback</c><00:02:41.760><c> on</c><00:02:42.060><c> features</c><00:02:42.420><c> that</c><00:02:42.599><c> it</c><00:02:42.660><c> can</c>

00:02:42.770 --> 00:02:42.780 align:start position:0%
taking feedback on features that it can
 

00:02:42.780 --> 00:02:44.270 align:start position:0%
taking feedback on features that it can
include<00:02:43.019><c> foods</c><00:02:43.500><c> and</c><00:02:43.560><c> scenarios</c><00:02:43.920><c> it</c><00:02:44.099><c> can</c>

00:02:44.270 --> 00:02:44.280 align:start position:0%
include foods and scenarios it can
 

00:02:44.280 --> 00:02:46.729 align:start position:0%
include foods and scenarios it can
support<00:02:44.400><c> I'm</c><00:02:45.300><c> so</c><00:02:45.599><c> excited</c><00:02:45.900><c> about</c><00:02:46.080><c> all</c><00:02:46.440><c> of</c><00:02:46.560><c> this</c>

00:02:46.729 --> 00:02:46.739 align:start position:0%
support I'm so excited about all of this
 

00:02:46.739 --> 00:02:49.430 align:start position:0%
support I'm so excited about all of this
the<00:02:47.340><c> data</c><00:02:47.640><c> visualization</c><00:02:48.060><c> nerd</c><00:02:48.480><c> in</c><00:02:48.599><c> me</c><00:02:48.780><c> is</c>

00:02:49.430 --> 00:02:49.440 align:start position:0%
the data visualization nerd in me is
 

00:02:49.440 --> 00:02:51.470 align:start position:0%
the data visualization nerd in me is
just<00:02:49.739><c> beyond</c><00:02:50.220><c> excited</c><00:02:50.640><c> the</c><00:02:51.120><c> possibilities</c>

00:02:51.470 --> 00:02:51.480 align:start position:0%
just beyond excited the possibilities
 

00:02:51.480 --> 00:02:52.850 align:start position:0%
just beyond excited the possibilities
that<00:02:51.720><c> can</c><00:02:51.900><c> take</c><00:02:52.019><c> place</c><00:02:52.140><c> with</c><00:02:52.379><c> this</c><00:02:52.500><c> stuff</c><00:02:52.680><c> is</c>

00:02:52.850 --> 00:02:52.860 align:start position:0%
that can take place with this stuff is
 

00:02:52.860 --> 00:02:54.890 align:start position:0%
that can take place with this stuff is
going<00:02:53.040><c> to</c><00:02:53.160><c> be</c><00:02:53.280><c> great</c><00:02:53.760><c> it's</c><00:02:54.480><c> like</c><00:02:54.599><c> that</c><00:02:54.780><c> old</c>

00:02:54.890 --> 00:02:54.900 align:start position:0%
going to be great it's like that old
 

00:02:54.900 --> 00:02:57.050 align:start position:0%
going to be great it's like that old
recess<00:02:55.260><c> slogan</c><00:02:55.680><c> you</c><00:02:55.920><c> know</c><00:02:55.980><c> check</c><00:02:56.640><c> out</c><00:02:56.819><c> my</c>

00:02:57.050 --> 00:02:57.060 align:start position:0%
recess slogan you know check out my
 

00:02:57.060 --> 00:02:58.130 align:start position:0%
recess slogan you know check out my
peanut<00:02:57.239><c> butter</c><00:02:57.360><c> peanut</c><00:02:57.660><c> butter</c><00:02:57.720><c> my</c><00:02:57.959><c> chocolate</c>

00:02:58.130 --> 00:02:58.140 align:start position:0%
peanut butter peanut butter my chocolate
 

00:02:58.140 --> 00:02:59.449 align:start position:0%
peanut butter peanut butter my chocolate
two<00:02:58.560><c> great</c><00:02:58.739><c> tastes</c><00:02:59.040><c> that</c><00:02:59.160><c> tastes</c><00:02:59.340><c> great</c>

00:02:59.449 --> 00:02:59.459 align:start position:0%
two great tastes that tastes great
 

00:02:59.459 --> 00:03:01.970 align:start position:0%
two great tastes that tastes great
together<00:02:59.700><c> I</c><00:03:00.599><c> love</c><00:03:00.720><c> it</c><00:03:00.900><c> so</c><00:03:01.379><c> I've</c><00:03:01.500><c> got</c><00:03:01.620><c> links</c><00:03:01.860><c> to</c>

00:03:01.970 --> 00:03:01.980 align:start position:0%
together I love it so I've got links to
 

00:03:01.980 --> 00:03:03.470 align:start position:0%
together I love it so I've got links to
the<00:03:02.099><c> announcement</c><00:03:02.400><c> blog</c><00:03:02.760><c> from</c><00:03:03.060><c> the</c><00:03:03.180><c> Excel</c>

00:03:03.470 --> 00:03:03.480 align:start position:0%
the announcement blog from the Excel
 

00:03:03.480 --> 00:03:05.390 align:start position:0%
the announcement blog from the Excel
team<00:03:03.660><c> the</c><00:03:03.959><c> Anaconda</c><00:03:04.500><c> team</c><00:03:04.620><c> and</c><00:03:04.980><c> some</c><00:03:05.160><c> getting</c>

00:03:05.390 --> 00:03:05.400 align:start position:0%
team the Anaconda team and some getting
 

00:03:05.400 --> 00:03:06.830 align:start position:0%
team the Anaconda team and some getting
started<00:03:05.760><c> docs</c><00:03:06.060><c> in</c><00:03:06.239><c> the</c><00:03:06.360><c> links</c><00:03:06.540><c> in</c><00:03:06.720><c> the</c>

00:03:06.830 --> 00:03:06.840 align:start position:0%
started docs in the links in the
 

00:03:06.840 --> 00:03:08.330 align:start position:0%
started docs in the links in the
description<00:03:06.959><c> down</c><00:03:07.260><c> below</c>

00:03:08.330 --> 00:03:08.340 align:start position:0%
description down below
 

00:03:08.340 --> 00:03:10.550 align:start position:0%
description down below
and<00:03:08.879><c> some</c><00:03:09.060><c> very</c><00:03:09.239><c> cool</c><00:03:09.360><c> dependabot</c><00:03:09.959><c> news</c><00:03:10.140><c> there</c>

00:03:10.550 --> 00:03:10.560 align:start position:0%
and some very cool dependabot news there
 

00:03:10.560 --> 00:03:12.589 align:start position:0%
and some very cool dependabot news there
is<00:03:10.739><c> a</c><00:03:10.920><c> now</c><00:03:11.099><c> a</c><00:03:11.340><c> faster</c><00:03:11.580><c> way</c><00:03:11.819><c> to</c><00:03:12.060><c> manage</c><00:03:12.420><c> version</c>

00:03:12.589 --> 00:03:12.599 align:start position:0%
is a now a faster way to manage version
 

00:03:12.599 --> 00:03:14.990 align:start position:0%
is a now a faster way to manage version
updates<00:03:13.080><c> with</c><00:03:13.440><c> dependabot</c><00:03:14.099><c> and</c><00:03:14.580><c> so</c><00:03:14.700><c> you</c><00:03:14.940><c> can</c>

00:03:14.990 --> 00:03:15.000 align:start position:0%
updates with dependabot and so you can
 

00:03:15.000 --> 00:03:16.369 align:start position:0%
updates with dependabot and so you can
group<00:03:15.180><c> multiple</c><00:03:15.599><c> version</c><00:03:15.780><c> updates</c><00:03:16.140><c> together</c>

00:03:16.369 --> 00:03:16.379 align:start position:0%
group multiple version updates together
 

00:03:16.379 --> 00:03:19.550 align:start position:0%
group multiple version updates together
in<00:03:16.860><c> a</c><00:03:17.040><c> single</c><00:03:17.280><c> pull</c><00:03:17.400><c> request</c><00:03:17.819><c> yay</c><00:03:18.599><c> so</c><00:03:19.260><c> this</c><00:03:19.500><c> is</c>

00:03:19.550 --> 00:03:19.560 align:start position:0%
in a single pull request yay so this is
 

00:03:19.560 --> 00:03:21.710 align:start position:0%
in a single pull request yay so this is
especially<00:03:19.980><c> handy</c><00:03:20.400><c> if</c><00:03:20.700><c> like</c><00:03:21.000><c> there's</c><00:03:21.480><c> a</c>

00:03:21.710 --> 00:03:21.720 align:start position:0%
especially handy if like there's a
 

00:03:21.720 --> 00:03:23.210 align:start position:0%
especially handy if like there's a
massive<00:03:21.959><c> vulnerability</c><00:03:22.440><c> that</c><00:03:22.680><c> needs</c><00:03:22.920><c> fixing</c>

00:03:23.210 --> 00:03:23.220 align:start position:0%
massive vulnerability that needs fixing
 

00:03:23.220 --> 00:03:25.369 align:start position:0%
massive vulnerability that needs fixing
like<00:03:23.519><c> something</c><00:03:23.700><c> like</c><00:03:24.000><c> log</c><00:03:24.180><c> for</c><00:03:24.420><c> J</c><00:03:24.659><c> or</c><00:03:25.260><c> you</c>

00:03:25.369 --> 00:03:25.379 align:start position:0%
like something like log for J or you
 

00:03:25.379 --> 00:03:26.809 align:start position:0%
like something like log for J or you
would<00:03:25.500><c> typically</c><00:03:25.739><c> be</c><00:03:25.920><c> stuck</c><00:03:26.159><c> in</c><00:03:26.340><c> versioning</c>

00:03:26.809 --> 00:03:26.819 align:start position:0%
would typically be stuck in versioning
 

00:03:26.819 --> 00:03:28.729 align:start position:0%
would typically be stuck in versioning
hell<00:03:27.000><c> making</c><00:03:27.780><c> sure</c><00:03:28.080><c> that</c><00:03:28.319><c> you're</c><00:03:28.440><c> updating</c>

00:03:28.729 --> 00:03:28.739 align:start position:0%
hell making sure that you're updating
 

00:03:28.739 --> 00:03:30.530 align:start position:0%
hell making sure that you're updating
the<00:03:28.980><c> right</c><00:03:29.099><c> version</c><00:03:29.340><c> of</c><00:03:29.819><c> the</c><00:03:29.940><c> right</c><00:03:30.120><c> branch</c><00:03:30.420><c> of</c>

00:03:30.530 --> 00:03:30.540 align:start position:0%
the right version of the right branch of
 

00:03:30.540 --> 00:03:31.790 align:start position:0%
the right version of the right branch of
each<00:03:30.720><c> Library</c><00:03:30.959><c> so</c><00:03:31.319><c> that</c><00:03:31.440><c> you</c><00:03:31.560><c> don't</c><00:03:31.680><c> have</c>

00:03:31.790 --> 00:03:31.800 align:start position:0%
each Library so that you don't have
 

00:03:31.800 --> 00:03:33.890 align:start position:0%
each Library so that you don't have
breaking<00:03:32.220><c> changes</c><00:03:32.580><c> and</c><00:03:33.239><c> this</c><00:03:33.420><c> is</c><00:03:33.480><c> how</c><00:03:33.659><c> Eric</c>

00:03:33.890 --> 00:03:33.900 align:start position:0%
breaking changes and this is how Eric
 

00:03:33.900 --> 00:03:36.130 align:start position:0%
breaking changes and this is how Eric
describes<00:03:34.980><c> um</c><00:03:35.040><c> the</c><00:03:35.220><c> scenario</c><00:03:35.519><c> in</c><00:03:35.700><c> the</c><00:03:35.819><c> blog</c>

00:03:36.130 --> 00:03:36.140 align:start position:0%
describes um the scenario in the blog
 

00:03:36.140 --> 00:03:38.149 align:start position:0%
describes um the scenario in the blog
previously<00:03:37.140><c> dependipot</c><00:03:37.800><c> would</c><00:03:38.040><c> open</c>

00:03:38.149 --> 00:03:38.159 align:start position:0%
previously dependipot would open
 

00:03:38.159 --> 00:03:39.949 align:start position:0%
previously dependipot would open
individual<00:03:38.760><c> pull</c><00:03:38.940><c> requests</c><00:03:39.360><c> for</c><00:03:39.480><c> each</c><00:03:39.659><c> update</c>

00:03:39.949 --> 00:03:39.959 align:start position:0%
individual pull requests for each update
 

00:03:39.959 --> 00:03:41.449 align:start position:0%
individual pull requests for each update
adding<00:03:40.379><c> overhead</c><00:03:40.739><c> to</c><00:03:40.920><c> a</c><00:03:41.040><c> developer's</c>

00:03:41.449 --> 00:03:41.459 align:start position:0%
adding overhead to a developer's
 

00:03:41.459 --> 00:03:43.009 align:start position:0%
adding overhead to a developer's
workflow<00:03:41.819><c> and</c><00:03:42.060><c> raising</c><00:03:42.360><c> the</c><00:03:42.540><c> possibilities</c>

00:03:43.009 --> 00:03:43.019 align:start position:0%
workflow and raising the possibilities
 

00:03:43.019 --> 00:03:45.170 align:start position:0%
workflow and raising the possibilities
that<00:03:43.680><c> related</c><00:03:43.980><c> dependencies</c><00:03:44.519><c> could</c><00:03:44.819><c> fall</c><00:03:45.000><c> out</c>

00:03:45.170 --> 00:03:45.180 align:start position:0%
that related dependencies could fall out
 

00:03:45.180 --> 00:03:48.110 align:start position:0%
that related dependencies could fall out
of<00:03:45.299><c> sync</c><00:03:45.659><c> now</c><00:03:46.500><c> you</c><00:03:46.860><c> can</c><00:03:46.980><c> specify</c><00:03:47.400><c> groups</c><00:03:47.879><c> of</c>

00:03:48.110 --> 00:03:48.120 align:start position:0%
of sync now you can specify groups of
 

00:03:48.120 --> 00:03:50.210 align:start position:0%
of sync now you can specify groups of
dependencies<00:03:48.659><c> to</c><00:03:48.959><c> updates</c><00:03:49.379><c> with</c><00:03:49.920><c> a</c><00:03:50.040><c> single</c>

00:03:50.210 --> 00:03:50.220 align:start position:0%
dependencies to updates with a single
 

00:03:50.220 --> 00:03:51.890 align:start position:0%
dependencies to updates with a single
pull<00:03:50.340><c> request</c><00:03:50.700><c> and</c><00:03:51.000><c> group</c><00:03:51.180><c> version</c><00:03:51.480><c> updates</c>

00:03:51.890 --> 00:03:51.900 align:start position:0%
pull request and group version updates
 

00:03:51.900 --> 00:03:54.589 align:start position:0%
pull request and group version updates
confer<00:03:52.640><c> significant</c><00:03:53.640><c> benefits</c><00:03:54.060><c> to</c><00:03:54.239><c> your</c>

00:03:54.589 --> 00:03:54.599 align:start position:0%
confer significant benefits to your
 

00:03:54.599 --> 00:03:56.210 align:start position:0%
confer significant benefits to your
development<00:03:54.900><c> process</c><00:03:55.200><c> simplify</c><00:03:55.860><c> dependency</c>

00:03:56.210 --> 00:03:56.220 align:start position:0%
development process simplify dependency
 

00:03:56.220 --> 00:03:58.250 align:start position:0%
development process simplify dependency
management<00:03:56.599><c> reduced</c><00:03:57.599><c> risk</c><00:03:57.840><c> of</c><00:03:57.900><c> breaking</c>

00:03:58.250 --> 00:03:58.260 align:start position:0%
management reduced risk of breaking
 

00:03:58.260 --> 00:04:00.710 align:start position:0%
management reduced risk of breaking
changes<00:03:58.640><c> and</c><00:03:59.640><c> also</c><00:03:59.879><c> an</c><00:04:00.000><c> opportunity</c><00:04:00.299><c> to</c><00:04:00.540><c> phase</c>

00:04:00.710 --> 00:04:00.720 align:start position:0%
changes and also an opportunity to phase
 

00:04:00.720 --> 00:04:02.630 align:start position:0%
changes and also an opportunity to phase
out<00:04:00.900><c> third-party</c><00:04:01.379><c> tools</c><00:04:01.680><c> and</c><00:04:01.980><c> manual</c>

00:04:02.630 --> 00:04:02.640 align:start position:0%
out third-party tools and manual
 

00:04:02.640 --> 00:04:04.729 align:start position:0%
out third-party tools and manual
workarounds<00:04:03.180><c> which</c><00:04:03.299><c> we</c><00:04:03.480><c> love</c><00:04:03.659><c> so</c><00:04:04.440><c> how</c><00:04:04.620><c> all</c>

00:04:04.729 --> 00:04:04.739 align:start position:0%
workarounds which we love so how all
 

00:04:04.739 --> 00:04:06.050 align:start position:0%
workarounds which we love so how all
this<00:04:04.920><c> works</c><00:04:05.159><c> is</c><00:04:05.280><c> actually</c><00:04:05.459><c> really</c><00:04:05.640><c> cool</c><00:04:05.819><c> too</c>

00:04:06.050 --> 00:04:06.060 align:start position:0%
this works is actually really cool too
 

00:04:06.060 --> 00:04:07.850 align:start position:0%
this works is actually really cool too
and<00:04:06.299><c> I've</c><00:04:06.480><c> got</c><00:04:06.599><c> links</c><00:04:07.019><c> in</c><00:04:07.319><c> the</c><00:04:07.440><c> show</c><00:04:07.560><c> notes</c>

00:04:07.850 --> 00:04:07.860 align:start position:0%
and I've got links in the show notes
 

00:04:07.860 --> 00:04:10.309 align:start position:0%
and I've got links in the show notes
down<00:04:08.040><c> below</c><00:04:08.400><c> a</c><00:04:08.819><c> blog</c><00:04:09.060><c> post</c><00:04:09.239><c> and</c><00:04:09.540><c> docs</c><00:04:09.959><c> so</c><00:04:10.140><c> that</c>

00:04:10.309 --> 00:04:10.319 align:start position:0%
down below a blog post and docs so that
 

00:04:10.319 --> 00:04:13.309 align:start position:0%
down below a blog post and docs so that
you<00:04:10.439><c> can</c><00:04:10.560><c> get</c><00:04:10.680><c> the</c><00:04:10.799><c> set</c><00:04:10.980><c> up</c><00:04:11.640><c> go</c><00:04:12.000><c> to</c><00:04:12.180><c> penabot</c><00:04:12.659><c> go</c>

00:04:13.309 --> 00:04:13.319 align:start position:0%
you can get the set up go to penabot go
 

00:04:13.319 --> 00:04:15.649 align:start position:0%
you can get the set up go to penabot go
meta<00:04:14.099><c> this</c><00:04:14.220><c> week</c><00:04:14.400><c> announced</c><00:04:15.000><c> a</c><00:04:15.180><c> new</c><00:04:15.360><c> large</c>

00:04:15.649 --> 00:04:15.659 align:start position:0%
meta this week announced a new large
 

00:04:15.659 --> 00:04:18.590 align:start position:0%
meta this week announced a new large
language<00:04:15.959><c> model</c><00:04:16.320><c> called</c><00:04:16.799><c> codelama</c><00:04:17.760><c> and</c><00:04:18.419><c> that</c>

00:04:18.590 --> 00:04:18.600 align:start position:0%
language model called codelama and that
 

00:04:18.600 --> 00:04:20.150 align:start position:0%
language model called codelama and that
can<00:04:18.780><c> be</c><00:04:18.900><c> used</c><00:04:19.019><c> for</c><00:04:19.199><c> code</c><00:04:19.320><c> generation</c><00:04:19.799><c> and</c>

00:04:20.150 --> 00:04:20.160 align:start position:0%
can be used for code generation and
 

00:04:20.160 --> 00:04:21.770 align:start position:0%
can be used for code generation and
that's<00:04:20.280><c> similar</c><00:04:20.699><c> to</c><00:04:20.820><c> the</c><00:04:21.000><c> way</c><00:04:21.120><c> that</c><00:04:21.419><c> things</c>

00:04:21.770 --> 00:04:21.780 align:start position:0%
that's similar to the way that things
 

00:04:21.780 --> 00:04:24.890 align:start position:0%
that's similar to the way that things
like<00:04:21.959><c> GitHub</c><00:04:22.320><c> copilot</c><00:04:22.800><c> use</c><00:04:22.979><c> GPT</c><00:04:23.460><c> 3.5</c><00:04:23.880><c> and</c><00:04:24.300><c> gpt4</c>

00:04:24.890 --> 00:04:24.900 align:start position:0%
like GitHub copilot use GPT 3.5 and gpt4
 

00:04:24.900 --> 00:04:27.230 align:start position:0%
like GitHub copilot use GPT 3.5 and gpt4
models<00:04:25.380><c> and</c><00:04:25.979><c> like</c><00:04:26.280><c> the</c><00:04:26.460><c> other</c><00:04:26.580><c> law</c><00:04:26.820><c> models</c>

00:04:27.230 --> 00:04:27.240 align:start position:0%
models and like the other law models
 

00:04:27.240 --> 00:04:29.810 align:start position:0%
models and like the other law models
codelama<00:04:27.840><c> is</c><00:04:28.139><c> open</c><00:04:28.380><c> access</c><00:04:28.740><c> not</c><00:04:29.400><c> strictly</c>

00:04:29.810 --> 00:04:29.820 align:start position:0%
codelama is open access not strictly
 

00:04:29.820 --> 00:04:31.969 align:start position:0%
codelama is open access not strictly
open<00:04:30.000><c> source</c><00:04:30.419><c> meaning</c><00:04:31.259><c> that</c><00:04:31.560><c> you</c><00:04:31.740><c> can</c><00:04:31.860><c> access</c>

00:04:31.969 --> 00:04:31.979 align:start position:0%
open source meaning that you can access
 

00:04:31.979 --> 00:04:33.590 align:start position:0%
open source meaning that you can access
the<00:04:32.280><c> source</c><00:04:32.580><c> code</c><00:04:32.639><c> but</c><00:04:32.880><c> you</c><00:04:33.000><c> need</c><00:04:33.120><c> to</c><00:04:33.240><c> use</c><00:04:33.360><c> it</c>

00:04:33.590 --> 00:04:33.600 align:start position:0%
the source code but you need to use it
 

00:04:33.600 --> 00:04:36.409 align:start position:0%
the source code but you need to use it
under<00:04:34.020><c> specific</c><00:04:34.500><c> licensing</c><00:04:35.040><c> conditions</c><00:04:35.520><c> this</c>

00:04:36.409 --> 00:04:36.419 align:start position:0%
under specific licensing conditions this
 

00:04:36.419 --> 00:04:38.270 align:start position:0%
under specific licensing conditions this
is<00:04:36.540><c> just</c><00:04:36.720><c> the</c><00:04:36.960><c> llm</c><00:04:37.440><c> that</c><00:04:37.680><c> there's</c><00:04:37.860><c> not</c><00:04:38.040><c> a</c><00:04:38.160><c> front</c>

00:04:38.270 --> 00:04:38.280 align:start position:0%
is just the llm that there's not a front
 

00:04:38.280 --> 00:04:40.670 align:start position:0%
is just the llm that there's not a front
end<00:04:38.460><c> to</c><00:04:38.639><c> actually</c><00:04:38.820><c> run</c><00:04:39.360><c> this</c><00:04:39.780><c> stuff</c><00:04:39.960><c> but</c><00:04:40.259><c> a</c><00:04:40.500><c> ton</c>

00:04:40.670 --> 00:04:40.680 align:start position:0%
end to actually run this stuff but a ton
 

00:04:40.680 --> 00:04:41.870 align:start position:0%
end to actually run this stuff but a ton
of<00:04:40.740><c> people</c><00:04:40.860><c> are</c><00:04:40.979><c> building</c><00:04:41.220><c> things</c><00:04:41.520><c> already</c>

00:04:41.870 --> 00:04:41.880 align:start position:0%
of people are building things already
 

00:04:41.880 --> 00:04:44.090 align:start position:0%
of people are building things already
and<00:04:42.840><c> look</c><00:04:42.960><c> you</c><00:04:43.199><c> know</c><00:04:43.320><c> I</c><00:04:43.440><c> think</c><00:04:43.560><c> that</c><00:04:43.680><c> is</c><00:04:43.860><c> weird</c>

00:04:44.090 --> 00:04:44.100 align:start position:0%
and look you know I think that is weird
 

00:04:44.100 --> 00:04:46.370 align:start position:0%
and look you know I think that is weird
but<00:04:44.400><c> me</c><00:04:44.580><c> a</c><00:04:45.240><c> GitHub</c><00:04:45.660><c> employee</c><00:04:45.960><c> is</c><00:04:46.259><c> talking</c>

00:04:46.370 --> 00:04:46.380 align:start position:0%
but me a GitHub employee is talking
 

00:04:46.380 --> 00:04:48.290 align:start position:0%
but me a GitHub employee is talking
about<00:04:46.620><c> a</c><00:04:47.040><c> would-be</c><00:04:47.400><c> competitor</c><00:04:47.820><c> to</c><00:04:47.940><c> GitHub</c>

00:04:48.290 --> 00:04:48.300 align:start position:0%
about a would-be competitor to GitHub
 

00:04:48.300 --> 00:04:50.870 align:start position:0%
about a would-be competitor to GitHub
copilot<00:04:48.840><c> on</c><00:04:49.320><c> a</c><00:04:49.440><c> GitHub</c><00:04:49.680><c> YouTube</c><00:04:49.919><c> channel</c><00:04:50.220><c> but</c>

00:04:50.870 --> 00:04:50.880 align:start position:0%
copilot on a GitHub YouTube channel but
 

00:04:50.880 --> 00:04:52.070 align:start position:0%
copilot on a GitHub YouTube channel but
the<00:04:51.060><c> truth</c><00:04:51.180><c> is</c><00:04:51.360><c> that</c><00:04:51.540><c> this</c><00:04:51.720><c> is</c><00:04:51.840><c> a</c><00:04:51.960><c> really</c>

00:04:52.070 --> 00:04:52.080 align:start position:0%
the truth is that this is a really
 

00:04:52.080 --> 00:04:53.689 align:start position:0%
the truth is that this is a really
important<00:04:52.380><c> and</c><00:04:52.560><c> evolving</c><00:04:52.979><c> space</c><00:04:53.220><c> and</c><00:04:53.580><c> it's</c>

00:04:53.689 --> 00:04:53.699 align:start position:0%
important and evolving space and it's
 

00:04:53.699 --> 00:04:55.010 align:start position:0%
important and evolving space and it's
awesome<00:04:53.940><c> to</c><00:04:54.180><c> see</c><00:04:54.300><c> more</c><00:04:54.479><c> of</c><00:04:54.600><c> these</c><00:04:54.720><c> models</c>

00:04:55.010 --> 00:04:55.020 align:start position:0%
awesome to see more of these models
 

00:04:55.020 --> 00:04:56.210 align:start position:0%
awesome to see more of these models
released<00:04:55.380><c> so</c><00:04:55.560><c> that</c><00:04:55.740><c> more</c><00:04:55.860><c> work</c><00:04:56.040><c> and</c>

00:04:56.210 --> 00:04:56.220 align:start position:0%
released so that more work and
 

00:04:56.220 --> 00:04:58.129 align:start position:0%
released so that more work and
refinements<00:04:56.639><c> can</c><00:04:56.759><c> be</c><00:04:56.880><c> done</c><00:04:57.000><c> for</c><00:04:57.120><c> all</c><00:04:57.300><c> of</c><00:04:57.360><c> us</c><00:04:57.540><c> so</c>

00:04:58.129 --> 00:04:58.139 align:start position:0%
refinements can be done for all of us so
 

00:04:58.139 --> 00:04:59.150 align:start position:0%
refinements can be done for all of us so
I've<00:04:58.259><c> got</c><00:04:58.380><c> a</c><00:04:58.500><c> link</c><00:04:58.560><c> down</c><00:04:58.740><c> below</c><00:04:58.860><c> to</c><00:04:59.040><c> the</c>

00:04:59.150 --> 00:04:59.160 align:start position:0%
I've got a link down below to the
 

00:04:59.160 --> 00:05:01.249 align:start position:0%
I've got a link down below to the
metablog<00:04:59.699><c> uh</c><00:05:00.240><c> the</c><00:05:00.360><c> research</c><00:05:00.840><c> paper</c><00:05:00.960><c> about</c>

00:05:01.249 --> 00:05:01.259 align:start position:0%
metablog uh the research paper about
 

00:05:01.259 --> 00:05:03.469 align:start position:0%
metablog uh the research paper about
code<00:05:01.500><c> llama</c><00:05:01.740><c> and</c><00:05:02.340><c> the</c><00:05:02.699><c> code</c><00:05:02.820><c> llama</c><00:05:03.180><c> GitHub</c>

00:05:03.469 --> 00:05:03.479 align:start position:0%
code llama and the code llama GitHub
 

00:05:03.479 --> 00:05:05.510 align:start position:0%
code llama and the code llama GitHub
repost<00:05:03.840><c> and</c><00:05:04.500><c> now</c><00:05:04.680><c> it's</c><00:05:04.740><c> time</c><00:05:04.919><c> for</c><00:05:05.100><c> my</c><00:05:05.220><c> GitHub</c>

00:05:05.510 --> 00:05:05.520 align:start position:0%
repost and now it's time for my GitHub
 

00:05:05.520 --> 00:05:07.310 align:start position:0%
repost and now it's time for my GitHub
project<00:05:05.820><c> Spotlight</c><00:05:06.419><c> this</c><00:05:06.840><c> is</c><00:05:06.960><c> where</c><00:05:07.139><c> I</c>

00:05:07.310 --> 00:05:07.320 align:start position:0%
project Spotlight this is where I
 

00:05:07.320 --> 00:05:09.170 align:start position:0%
project Spotlight this is where I
Spotlight<00:05:07.620><c> a</c><00:05:07.860><c> great</c><00:05:08.040><c> GitHub</c><00:05:08.400><c> repo</c><00:05:08.820><c> from</c><00:05:09.000><c> the</c>

00:05:09.170 --> 00:05:09.180 align:start position:0%
Spotlight a great GitHub repo from the
 

00:05:09.180 --> 00:05:11.930 align:start position:0%
Spotlight a great GitHub repo from the
community<00:05:09.300><c> and</c><00:05:10.199><c> this</c><00:05:10.560><c> week</c><00:05:10.680><c> my</c><00:05:11.100><c> pick</c><00:05:11.280><c> is</c><00:05:11.580><c> a</c><00:05:11.820><c> new</c>

00:05:11.930 --> 00:05:11.940 align:start position:0%
community and this week my pick is a new
 

00:05:11.940 --> 00:05:14.390 align:start position:0%
community and this week my pick is a new
open<00:05:12.120><c> source</c><00:05:12.540><c> coding</c><00:05:12.840><c> bot</c><00:05:13.320><c> and</c><00:05:13.800><c> we</c><00:05:14.040><c> are</c><00:05:14.160><c> type</c>

00:05:14.390 --> 00:05:14.400 align:start position:0%
open source coding bot and we are type
 

00:05:14.400 --> 00:05:16.850 align:start position:0%
open source coding bot and we are type
nerds<00:05:14.880><c> here</c><00:05:15.120><c> on</c><00:05:15.419><c> the</c><00:05:15.600><c> download</c><00:05:15.780><c> and</c><00:05:16.380><c> this</c><00:05:16.620><c> font</c>

00:05:16.850 --> 00:05:16.860 align:start position:0%
nerds here on the download and this font
 

00:05:16.860 --> 00:05:19.790 align:start position:0%
nerds here on the download and this font
is<00:05:17.160><c> called</c><00:05:17.400><c> Intel</c><00:05:18.000><c> One</c><00:05:18.180><c> Mono</c><00:05:18.720><c> and</c><00:05:19.199><c> shocker</c>

00:05:19.790 --> 00:05:19.800 align:start position:0%
is called Intel One Mono and shocker
 

00:05:19.800 --> 00:05:21.469 align:start position:0%
is called Intel One Mono and shocker
it's<00:05:19.979><c> from</c><00:05:20.160><c> Intel</c><00:05:20.520><c> that's</c><00:05:21.060><c> actually</c><00:05:21.240><c> really</c>

00:05:21.469 --> 00:05:21.479 align:start position:0%
it's from Intel that's actually really
 

00:05:21.479 --> 00:05:23.689 align:start position:0%
it's from Intel that's actually really
cool<00:05:21.660><c> but</c><00:05:21.960><c> I'm</c><00:05:22.139><c> not</c><00:05:22.259><c> gonna</c><00:05:22.380><c> lie</c><00:05:22.740><c> I</c><00:05:23.460><c> was</c><00:05:23.520><c> also</c>

00:05:23.689 --> 00:05:23.699 align:start position:0%
cool but I'm not gonna lie I was also
 

00:05:23.699 --> 00:05:25.670 align:start position:0%
cool but I'm not gonna lie I was also
surprised<00:05:24.600><c> um</c><00:05:24.600><c> but</c><00:05:24.780><c> I</c><00:05:24.900><c> love</c><00:05:25.080><c> to</c><00:05:25.199><c> see</c><00:05:25.320><c> this</c><00:05:25.500><c> I</c>

00:05:25.670 --> 00:05:25.680 align:start position:0%
surprised um but I love to see this I
 

00:05:25.680 --> 00:05:28.150 align:start position:0%
surprised um but I love to see this I
love<00:05:25.860><c> to</c><00:05:26.039><c> see</c><00:05:26.280><c> Hardware</c><00:05:26.759><c> people</c><00:05:27.120><c> doing</c><00:05:27.600><c> great</c>

00:05:28.150 --> 00:05:28.160 align:start position:0%
love to see Hardware people doing great
 

00:05:28.160 --> 00:05:31.249 align:start position:0%
love to see Hardware people doing great
fonts<00:05:29.160><c> shock</c><00:05:29.759><c> I</c><00:05:30.060><c> love</c><00:05:30.180><c> it</c><00:05:30.300><c> the</c><00:05:30.780><c> font</c><00:05:31.080><c> was</c>

00:05:31.249 --> 00:05:31.259 align:start position:0%
fonts shock I love it the font was
 

00:05:31.259 --> 00:05:33.350 align:start position:0%
fonts shock I love it the font was
actually<00:05:31.440><c> built</c><00:05:31.800><c> by</c><00:05:31.919><c> freera</c><00:05:32.460><c> Jones</c><00:05:32.639><c> type</c><00:05:32.940><c> in</c>

00:05:33.350 --> 00:05:33.360 align:start position:0%
actually built by freera Jones type in
 

00:05:33.360 --> 00:05:35.990 align:start position:0%
actually built by freera Jones type in
partnership<00:05:33.660><c> with</c><00:05:33.900><c> Intel</c><00:05:34.380><c> and</c><00:05:34.740><c> vmly</c><00:05:35.400><c> and</c><00:05:35.759><c> R</c>

00:05:35.990 --> 00:05:36.000 align:start position:0%
partnership with Intel and vmly and R
 

00:05:36.000 --> 00:05:38.029 align:start position:0%
partnership with Intel and vmly and R
and<00:05:36.600><c> for</c><00:05:36.840><c> Jones</c><00:05:37.080><c> type</c><00:05:37.320><c> is</c><00:05:37.500><c> actually</c><00:05:37.620><c> one</c><00:05:37.800><c> of</c><00:05:37.979><c> my</c>

00:05:38.029 --> 00:05:38.039 align:start position:0%
and for Jones type is actually one of my
 

00:05:38.039 --> 00:05:40.129 align:start position:0%
and for Jones type is actually one of my
all-time<00:05:38.340><c> favorite</c><00:05:38.520><c> boundaries</c><00:05:39.000><c> so</c><00:05:39.780><c> I</c><00:05:39.960><c> love</c>

00:05:40.129 --> 00:05:40.139 align:start position:0%
all-time favorite boundaries so I love
 

00:05:40.139 --> 00:05:42.050 align:start position:0%
all-time favorite boundaries so I love
this<00:05:40.560><c> the</c><00:05:40.800><c> spot</c><00:05:41.039><c> I</c><00:05:41.280><c> think</c><00:05:41.400><c> it's</c><00:05:41.520><c> really</c><00:05:41.880><c> really</c>

00:05:42.050 --> 00:05:42.060 align:start position:0%
this the spot I think it's really really
 

00:05:42.060 --> 00:05:44.210 align:start position:0%
this the spot I think it's really really
impressive<00:05:42.720><c> a</c><00:05:43.320><c> big</c><00:05:43.440><c> update</c><00:05:43.680><c> was</c><00:05:43.860><c> released</c>

00:05:44.210 --> 00:05:44.220 align:start position:0%
impressive a big update was released
 

00:05:44.220 --> 00:05:46.129 align:start position:0%
impressive a big update was released
this<00:05:44.400><c> week</c><00:05:44.639><c> and</c><00:05:45.060><c> it</c><00:05:45.240><c> is</c><00:05:45.360><c> available</c><00:05:45.539><c> in</c><00:05:45.900><c> a</c><00:05:46.020><c> bunch</c>

00:05:46.129 --> 00:05:46.139 align:start position:0%
this week and it is available in a bunch
 

00:05:46.139 --> 00:05:48.290 align:start position:0%
this week and it is available in a bunch
of<00:05:46.199><c> different</c><00:05:46.320><c> weights</c><00:05:46.800><c> and</c><00:05:46.979><c> styles</c><00:05:47.460><c> and</c><00:05:48.120><c> it's</c>

00:05:48.290 --> 00:05:48.300 align:start position:0%
of different weights and styles and it's
 

00:05:48.300 --> 00:05:50.570 align:start position:0%
of different weights and styles and it's
open<00:05:48.419><c> source</c><00:05:48.840><c> so</c><00:05:49.139><c> I</c><00:05:49.380><c> love</c><00:05:49.500><c> it</c><00:05:49.680><c> Well</c><00:05:49.919><c> Done</c><00:05:50.160><c> Intel</c>

00:05:50.570 --> 00:05:50.580 align:start position:0%
open source so I love it Well Done Intel
 

00:05:50.580 --> 00:05:52.370 align:start position:0%
open source so I love it Well Done Intel
I've<00:05:51.060><c> got</c><00:05:51.180><c> links</c><00:05:51.479><c> to</c><00:05:51.600><c> that</c><00:05:51.720><c> in</c><00:05:51.900><c> the</c><00:05:52.020><c> show</c><00:05:52.139><c> notes</c>

00:05:52.370 --> 00:05:52.380 align:start position:0%
I've got links to that in the show notes
 

00:05:52.380 --> 00:05:53.990 align:start position:0%
I've got links to that in the show notes
down<00:05:52.560><c> below</c>

00:05:53.990 --> 00:05:54.000 align:start position:0%
down below
 

00:05:54.000 --> 00:05:55.790 align:start position:0%
down below
and<00:05:54.539><c> now</c><00:05:54.660><c> it</c><00:05:54.840><c> is</c><00:05:54.960><c> time</c><00:05:55.080><c> for</c><00:05:55.259><c> my</c><00:05:55.380><c> pick</c><00:05:55.500><c> of</c><00:05:55.680><c> the</c>

00:05:55.790 --> 00:05:55.800 align:start position:0%
and now it is time for my pick of the
 

00:05:55.800 --> 00:05:56.570 align:start position:0%
and now it is time for my pick of the
week

00:05:56.570 --> 00:05:56.580 align:start position:0%
week
 

00:05:56.580 --> 00:05:58.730 align:start position:0%
week
this<00:05:57.300><c> week</c><00:05:57.539><c> is</c><00:05:57.780><c> a</c><00:05:57.960><c> hard</c><00:05:58.080><c> one</c><00:05:58.259><c> because</c><00:05:58.440><c> it's</c>

00:05:58.730 --> 00:05:58.740 align:start position:0%
this week is a hard one because it's
 

00:05:58.740 --> 00:06:00.230 align:start position:0%
this week is a hard one because it's
actually<00:05:58.919><c> a</c><00:05:59.220><c> tribute</c>

00:06:00.230 --> 00:06:00.240 align:start position:0%
actually a tribute
 

00:06:00.240 --> 00:06:02.689 align:start position:0%
actually a tribute
last<00:06:00.720><c> week</c><00:06:00.960><c> we</c><00:06:01.259><c> lost</c><00:06:01.440><c> beloved</c><00:06:01.919><c> member</c><00:06:02.340><c> of</c><00:06:02.460><c> not</c>

00:06:02.689 --> 00:06:02.699 align:start position:0%
last week we lost beloved member of not
 

00:06:02.699 --> 00:06:04.310 align:start position:0%
last week we lost beloved member of not
just<00:06:02.880><c> the</c><00:06:03.060><c> GitHub</c><00:06:03.360><c> family</c><00:06:03.600><c> but</c><00:06:03.900><c> the</c><00:06:04.080><c> open</c>

00:06:04.310 --> 00:06:04.320 align:start position:0%
just the GitHub family but the open
 

00:06:04.320 --> 00:06:07.610 align:start position:0%
just the GitHub family but the open
source<00:06:04.740><c> family</c><00:06:04.919><c> our</c><00:06:05.520><c> dear</c><00:06:05.880><c> friend</c><00:06:06.000><c> Chris</c><00:06:06.360><c> Nova</c>

00:06:07.610 --> 00:06:07.620 align:start position:0%
source family our dear friend Chris Nova
 

00:06:07.620 --> 00:06:09.230 align:start position:0%
source family our dear friend Chris Nova
I<00:06:07.979><c> had</c><00:06:08.100><c> the</c><00:06:08.220><c> pleasure</c><00:06:08.400><c> of</c><00:06:08.520><c> working</c><00:06:08.639><c> with</c><00:06:08.820><c> Nova</c>

00:06:09.230 --> 00:06:09.240 align:start position:0%
I had the pleasure of working with Nova
 

00:06:09.240 --> 00:06:11.450 align:start position:0%
I had the pleasure of working with Nova
twice<00:06:09.539><c> in</c><00:06:09.660><c> my</c><00:06:09.840><c> career</c><00:06:10.080><c> and</c><00:06:10.560><c> she</c><00:06:11.220><c> was</c><00:06:11.340><c> an</c>

00:06:11.450 --> 00:06:11.460 align:start position:0%
twice in my career and she was an
 

00:06:11.460 --> 00:06:13.249 align:start position:0%
twice in my career and she was an
exceptional<00:06:11.940><c> person</c><00:06:12.120><c> no</c><00:06:12.479><c> not</c><00:06:12.720><c> just</c><00:06:12.900><c> for</c><00:06:13.080><c> her</c>

00:06:13.249 --> 00:06:13.259 align:start position:0%
exceptional person no not just for her
 

00:06:13.259 --> 00:06:15.110 align:start position:0%
exceptional person no not just for her
work<00:06:13.440><c> in</c><00:06:13.740><c> the</c><00:06:13.800><c> kubernetes</c><00:06:14.220><c> and</c><00:06:14.580><c> devops</c><00:06:14.940><c> space</c>

00:06:15.110 --> 00:06:15.120 align:start position:0%
work in the kubernetes and devops space
 

00:06:15.120 --> 00:06:16.969 align:start position:0%
work in the kubernetes and devops space
but<00:06:15.479><c> also</c><00:06:16.080><c> for</c><00:06:16.259><c> creating</c><00:06:16.500><c> the</c><00:06:16.620><c> hacky</c><00:06:16.919><c> door</c>

00:06:16.969 --> 00:06:16.979 align:start position:0%
but also for creating the hacky door
 

00:06:16.979 --> 00:06:19.310 align:start position:0%
but also for creating the hacky door
Macedon<00:06:17.520><c> instance</c><00:06:18.000><c> and</c><00:06:18.780><c> more</c><00:06:18.960><c> importantly</c>

00:06:19.310 --> 00:06:19.320 align:start position:0%
Macedon instance and more importantly
 

00:06:19.320 --> 00:06:22.189 align:start position:0%
Macedon instance and more importantly
being<00:06:19.860><c> an</c><00:06:20.160><c> amazing</c><00:06:20.400><c> friend</c><00:06:20.639><c> and</c><00:06:21.000><c> mentor</c><00:06:21.360><c> to</c><00:06:21.720><c> so</c>

00:06:22.189 --> 00:06:22.199 align:start position:0%
being an amazing friend and mentor to so
 

00:06:22.199 --> 00:06:23.749 align:start position:0%
being an amazing friend and mentor to so
many<00:06:22.440><c> people</c>

00:06:23.749 --> 00:06:23.759 align:start position:0%
many people
 

00:06:23.759 --> 00:06:25.850 align:start position:0%
many people
Chris<00:06:24.240><c> was</c><00:06:24.479><c> an</c><00:06:24.660><c> exceptional</c><00:06:25.080><c> person</c><00:06:25.259><c> and</c><00:06:25.680><c> I</c>

00:06:25.850 --> 00:06:25.860 align:start position:0%
Chris was an exceptional person and I
 

00:06:25.860 --> 00:06:27.590 align:start position:0%
Chris was an exceptional person and I
feel<00:06:25.979><c> privileged</c><00:06:26.340><c> to</c><00:06:26.460><c> have</c><00:06:26.580><c> known</c><00:06:26.819><c> her</c><00:06:27.060><c> I've</c>

00:06:27.590 --> 00:06:27.600 align:start position:0%
feel privileged to have known her I've
 

00:06:27.600 --> 00:06:28.790 align:start position:0%
feel privileged to have known her I've
got<00:06:27.780><c> links</c><00:06:28.080><c> down</c><00:06:28.139><c> below</c><00:06:28.319><c> if</c><00:06:28.500><c> you</c><00:06:28.620><c> want</c><00:06:28.680><c> to</c>

00:06:28.790 --> 00:06:28.800 align:start position:0%
got links down below if you want to
 

00:06:28.800 --> 00:06:31.070 align:start position:0%
got links down below if you want to
celebrate<00:06:29.039><c> her</c><00:06:29.280><c> life</c><00:06:29.460><c> and</c><00:06:29.880><c> Legacy</c>

00:06:31.070 --> 00:06:31.080 align:start position:0%
celebrate her life and Legacy
 

00:06:31.080 --> 00:06:32.689 align:start position:0%
celebrate her life and Legacy
and<00:06:31.500><c> on</c><00:06:31.620><c> that</c><00:06:31.800><c> note</c><00:06:31.919><c> that's</c><00:06:32.220><c> going</c><00:06:32.400><c> to</c><00:06:32.460><c> do</c><00:06:32.580><c> it</c>

00:06:32.689 --> 00:06:32.699 align:start position:0%
and on that note that's going to do it
 

00:06:32.699 --> 00:06:34.790 align:start position:0%
and on that note that's going to do it
for<00:06:32.819><c> me</c><00:06:32.940><c> if</c><00:06:33.240><c> you</c><00:06:33.300><c> like</c><00:06:33.479><c> this</c><00:06:33.660><c> episode</c><00:06:33.900><c> you</c><00:06:34.620><c> know</c>

00:06:34.790 --> 00:06:34.800 align:start position:0%
for me if you like this episode you know
 

00:06:34.800 --> 00:06:37.430 align:start position:0%
for me if you like this episode you know
what<00:06:34.860><c> to</c><00:06:34.979><c> do</c><00:06:35.639><c> see</c><00:06:35.940><c> you</c><00:06:36.120><c> next</c><00:06:36.240><c> time</c><00:06:36.419><c> I</c><00:06:37.199><c> am</c>

00:06:37.430 --> 00:06:37.440 align:start position:0%
what to do see you next time I am
 

00:06:37.440 --> 00:06:40.550 align:start position:0%
what to do see you next time I am
genuinely<00:06:38.100><c> I</c><00:06:38.520><c> think</c><00:06:38.639><c> the</c><00:06:38.880><c> happiest</c><00:06:39.300><c> and</c><00:06:40.259><c> in</c>

00:06:40.550 --> 00:06:40.560 align:start position:0%
genuinely I think the happiest and in
 

00:06:40.560 --> 00:06:43.070 align:start position:0%
genuinely I think the happiest and in
the<00:06:40.740><c> best</c><00:06:40.860><c> physical</c><00:06:41.160><c> Shape</c><00:06:41.639><c> of</c><00:06:41.940><c> My</c><00:06:42.240><c> Life</c>

00:06:43.070 --> 00:06:43.080 align:start position:0%
the best physical Shape of My Life
 

00:06:43.080 --> 00:06:46.010 align:start position:0%
the best physical Shape of My Life
of<00:06:43.620><c> all</c><00:06:44.400><c> 36</c><00:06:44.819><c> years</c><00:06:45.060><c> I've</c><00:06:45.360><c> been</c><00:06:45.539><c> on</c><00:06:45.660><c> this</c><00:06:45.840><c> planet</c>

00:06:46.010 --> 00:06:46.020 align:start position:0%
of all 36 years I've been on this planet
 

00:06:46.020 --> 00:06:49.610 align:start position:0%
of all 36 years I've been on this planet
today<00:06:46.979><c> right</c><00:06:47.460><c> now</c><00:06:47.639><c> in</c><00:06:48.000><c> this</c><00:06:48.180><c> moment</c><00:06:48.479><c> I</c><00:06:49.199><c> am</c><00:06:49.380><c> the</c>

00:06:49.610 --> 00:06:49.620 align:start position:0%
today right now in this moment I am the
 

00:06:49.620 --> 00:06:51.529 align:start position:0%
today right now in this moment I am the
healthiest<00:06:49.979><c> and</c><00:06:50.400><c> the</c><00:06:50.580><c> happiest</c><00:06:50.940><c> I</c><00:06:51.180><c> have</c><00:06:51.360><c> ever</c>

00:06:51.529 --> 00:06:51.539 align:start position:0%
healthiest and the happiest I have ever
 

00:06:51.539 --> 00:06:53.809 align:start position:0%
healthiest and the happiest I have ever
been<00:06:51.780><c> in</c><00:06:52.259><c> my</c><00:06:52.380><c> life</c><00:06:52.560><c> right</c><00:06:52.979><c> now</c>

00:06:53.809 --> 00:06:53.819 align:start position:0%
been in my life right now
 

00:06:53.819 --> 00:06:58.129 align:start position:0%
been in my life right now
I<00:06:54.419><c> think</c><00:06:54.539><c> so</c><00:06:54.960><c> I</c><00:06:55.139><c> did</c><00:06:55.319><c> I</c><00:06:55.979><c> did</c><00:06:56.100><c> a</c><00:06:56.280><c> 20</c><00:06:56.520><c> mile</c><00:06:56.880><c> run</c>

00:06:58.129 --> 00:06:58.139 align:start position:0%
I think so I did I did a 20 mile run
 

00:06:58.139 --> 00:06:59.870 align:start position:0%
I think so I did I did a 20 mile run
yesterday

00:06:59.870 --> 00:06:59.880 align:start position:0%
yesterday
 

00:06:59.880 --> 00:07:02.870 align:start position:0%
yesterday
and<00:07:00.660><c> I</c><00:07:01.020><c> also</c><00:07:01.319><c> soloed</c>

00:07:02.870 --> 00:07:02.880 align:start position:0%
and I also soloed
 

00:07:02.880 --> 00:07:05.570 align:start position:0%
and I also soloed
prussic<00:07:03.539><c> Peak</c><00:07:03.900><c> a</c><00:07:04.139><c> mountain</c><00:07:04.319><c> during</c><00:07:05.039><c> the</c><00:07:05.400><c> run</c>

00:07:05.570 --> 00:07:05.580 align:start position:0%
prussic Peak a mountain during the run
 

00:07:05.580 --> 00:07:07.850 align:start position:0%
prussic Peak a mountain during the run
and<00:07:06.060><c> it</c><00:07:06.180><c> was</c><00:07:06.300><c> a</c><00:07:06.419><c> fifth</c><00:07:06.660><c> class</c><00:07:06.840><c> I</c><00:07:07.259><c> like</c><00:07:07.440><c> did</c><00:07:07.680><c> a</c>

00:07:07.850 --> 00:07:07.860 align:start position:0%
and it was a fifth class I like did a
 

00:07:07.860 --> 00:07:11.090 align:start position:0%
and it was a fifth class I like did a
five<00:07:08.039><c> seven</c><00:07:08.400><c> solo</c><00:07:09.240><c> up</c><00:07:09.840><c> a</c><00:07:10.199><c> four</c><00:07:10.380><c> pitch</c><00:07:10.680><c> Trad</c>

00:07:11.090 --> 00:07:11.100 align:start position:0%
five seven solo up a four pitch Trad
 

00:07:11.100 --> 00:07:13.790 align:start position:0%
five seven solo up a four pitch Trad
route<00:07:11.400><c> with</c><00:07:12.360><c> no</c><00:07:12.479><c> ropes</c><00:07:12.840><c> just</c><00:07:13.080><c> by</c><00:07:13.259><c> myself</c><00:07:13.440><c> and</c>

00:07:13.790 --> 00:07:13.800 align:start position:0%
route with no ropes just by myself and
 

00:07:13.800 --> 00:07:15.650 align:start position:0%
route with no ropes just by myself and
then<00:07:13.919><c> got</c><00:07:14.100><c> down</c><00:07:14.280><c> and</c><00:07:14.759><c> ran</c><00:07:15.060><c> out</c><00:07:15.479><c> of</c><00:07:15.539><c> the</c>

00:07:15.650 --> 00:07:15.660 align:start position:0%
then got down and ran out of the
 

00:07:15.660 --> 00:07:18.469 align:start position:0%
then got down and ran out of the
glacier<00:07:16.020><c> Zone</c><00:07:16.319><c> and</c><00:07:16.500><c> ran</c><00:07:16.740><c> back</c><00:07:16.919><c> to</c><00:07:17.100><c> the</c><00:07:17.220><c> car</c>

00:07:18.469 --> 00:07:18.479 align:start position:0%
glacier Zone and ran back to the car
 

00:07:18.479 --> 00:07:20.510 align:start position:0%
glacier Zone and ran back to the car
um<00:07:18.600><c> and</c><00:07:19.380><c> like</c><00:07:19.500><c> that</c><00:07:19.680><c> was</c><00:07:19.860><c> that</c><00:07:20.039><c> was</c><00:07:20.220><c> that</c><00:07:20.400><c> was</c>

00:07:20.510 --> 00:07:20.520 align:start position:0%
um and like that was that was that was
 

00:07:20.520 --> 00:07:21.830 align:start position:0%
um and like that was that was that was
like<00:07:20.699><c> a</c><00:07:20.819><c> Thursday</c>

00:07:21.830 --> 00:07:21.840 align:start position:0%
like a Thursday
 

00:07:21.840 --> 00:07:24.170 align:start position:0%
like a Thursday
you<00:07:22.500><c> know</c><00:07:22.620><c> last</c><00:07:22.979><c> the</c><00:07:23.280><c> weekend</c><00:07:23.400><c> before</c><00:07:23.759><c> I</c><00:07:24.060><c> did</c>

00:07:24.170 --> 00:07:24.180 align:start position:0%
you know last the weekend before I did
 

00:07:24.180 --> 00:07:25.909 align:start position:0%
you know last the weekend before I did
the<00:07:24.360><c> North</c><00:07:24.539><c> Ridge</c><00:07:24.780><c> of</c><00:07:25.080><c> Mount</c><00:07:25.259><c> Stewart</c><00:07:25.740><c> which</c>

00:07:25.909 --> 00:07:25.919 align:start position:0%
the North Ridge of Mount Stewart which
 

00:07:25.919 --> 00:07:27.770 align:start position:0%
the North Ridge of Mount Stewart which
is<00:07:26.039><c> like</c><00:07:26.220><c> this</c><00:07:26.400><c> 50</c><00:07:26.639><c> classic</c><00:07:26.940><c> climbs</c><00:07:27.479><c> of</c><00:07:27.539><c> North</c>

00:07:27.770 --> 00:07:27.780 align:start position:0%
is like this 50 classic climbs of North
 

00:07:27.780 --> 00:07:30.589 align:start position:0%
is like this 50 classic climbs of North
America<00:07:28.220><c> two</c><00:07:29.220><c> day</c><00:07:29.520><c> three</c><00:07:30.120><c> thousand</c><00:07:30.479><c> foot</c>

00:07:30.589 --> 00:07:30.599 align:start position:0%
America two day three thousand foot
 

00:07:30.599 --> 00:07:33.890 align:start position:0%
America two day three thousand foot
Granite<00:07:31.139><c> Ridge</c><00:07:31.319><c> line</c><00:07:31.620><c> with</c><00:07:32.160><c> a</c><00:07:32.340><c> friend</c><00:07:32.460><c> of</c><00:07:32.639><c> mine</c>

00:07:33.890 --> 00:07:33.900 align:start position:0%
Granite Ridge line with a friend of mine
 

00:07:33.900 --> 00:07:35.510 align:start position:0%
Granite Ridge line with a friend of mine
um<00:07:33.960><c> and</c><00:07:34.199><c> that</c><00:07:34.380><c> was</c>

00:07:35.510 --> 00:07:35.520 align:start position:0%
um and that was
 

00:07:35.520 --> 00:07:38.210 align:start position:0%
um and that was
beautiful<00:07:36.240><c> to</c><00:07:37.020><c> say</c><00:07:37.139><c> the</c><00:07:37.319><c> least</c><00:07:37.440><c> I</c><00:07:37.979><c> know</c><00:07:38.099><c> I'm</c>

00:07:38.210 --> 00:07:38.220 align:start position:0%
beautiful to say the least I know I'm
 

00:07:38.220 --> 00:07:39.830 align:start position:0%
beautiful to say the least I know I'm
like<00:07:38.460><c> I</c><00:07:38.639><c> did</c><00:07:38.699><c> Mount</c><00:07:39.000><c> Rainier</c><00:07:39.360><c> I'm</c><00:07:39.720><c> just</c>

00:07:39.830 --> 00:07:39.840 align:start position:0%
like I did Mount Rainier I'm just
 

00:07:39.840 --> 00:07:41.990 align:start position:0%
like I did Mount Rainier I'm just
 I<00:07:40.380><c> did</c><00:07:40.560><c> Liberty</c><00:07:41.340><c> Bell</c><00:07:41.520><c> I've</c><00:07:41.880><c> just</c>

00:07:41.990 --> 00:07:42.000 align:start position:0%
 I did Liberty Bell I've just
 

00:07:42.000 --> 00:07:43.610 align:start position:0%
 I did Liberty Bell I've just
been<00:07:42.180><c> crushing</c><00:07:42.599><c> them</c><00:07:42.720><c> just</c><00:07:43.080><c> been</c><00:07:43.259><c> knocking</c>

00:07:43.610 --> 00:07:43.620 align:start position:0%
been crushing them just been knocking
 

00:07:43.620 --> 00:07:45.350 align:start position:0%
been crushing them just been knocking
him<00:07:43.740><c> down</c><00:07:43.919><c> and</c><00:07:44.160><c> I</c><00:07:44.340><c> got</c><00:07:44.400><c> more</c><00:07:44.639><c> coming</c><00:07:44.880><c> up</c><00:07:45.120><c> so</c>

00:07:45.350 --> 00:07:45.360 align:start position:0%
him down and I got more coming up so
 

00:07:45.360 --> 00:07:46.670 align:start position:0%
him down and I got more coming up so
 the<00:07:45.840><c> internet</c><00:07:45.900><c> I'm</c><00:07:46.199><c> gonna</c><00:07:46.319><c> go</c><00:07:46.440><c> climb</c>

00:07:46.670 --> 00:07:46.680 align:start position:0%
 the internet I'm gonna go climb
 

00:07:46.680 --> 00:07:47.749 align:start position:0%
 the internet I'm gonna go climb
mountains

00:07:47.749 --> 00:07:47.759 align:start position:0%
mountains
 

00:07:47.759 --> 00:07:49.550 align:start position:0%
mountains
y'all<00:07:48.180><c> y'all</c><00:07:48.240><c> if</c><00:07:48.720><c> you</c><00:07:48.840><c> can</c><00:07:48.960><c> if</c><00:07:49.259><c> you're</c><00:07:49.380><c> lucky</c>

00:07:49.550 --> 00:07:49.560 align:start position:0%
y'all y'all if you can if you're lucky
 

00:07:49.560 --> 00:07:52.129 align:start position:0%
y'all y'all if you can if you're lucky
enough<00:07:49.740><c> please</c><00:07:50.400><c> please</c><00:07:50.880><c> also</c><00:07:51.419><c> get</c><00:07:51.599><c> outside</c><00:07:51.780><c> if</c>

00:07:52.129 --> 00:07:52.139 align:start position:0%
enough please please also get outside if
 

00:07:52.139 --> 00:07:54.440 align:start position:0%
enough please please also get outside if
you<00:07:52.259><c> can</c>

