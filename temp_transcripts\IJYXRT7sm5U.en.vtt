WEBVTT
Kind: captions
Language: en

00:00:00.060 --> 00:00:02.570 align:start position:0%
 
GitHub<00:00:00.719><c> is</c><00:00:00.900><c> home</c><00:00:01.140><c> to</c><00:00:01.319><c> more</c><00:00:01.560><c> than</c><00:00:01.740><c> 100</c><00:00:01.920><c> million</c>

00:00:02.570 --> 00:00:02.580 align:start position:0%
GitHub is home to more than 100 million
 

00:00:02.580 --> 00:00:04.910 align:start position:0%
GitHub is home to more than 100 million
developers<00:00:03.120><c> across</c><00:00:03.600><c> the</c><00:00:03.720><c> globe</c><00:00:04.259><c> from</c><00:00:04.799><c> the</c>

00:00:04.910 --> 00:00:04.920 align:start position:0%
developers across the globe from the
 

00:00:04.920 --> 00:00:06.590 align:start position:0%
developers across the globe from the
open<00:00:05.040><c> source</c><00:00:05.520><c> community</c><00:00:05.759><c> Through</c><00:00:06.420><c> to</c>

00:00:06.590 --> 00:00:06.600 align:start position:0%
open source community Through to
 

00:00:06.600 --> 00:00:08.690 align:start position:0%
open source community Through to
engineering<00:00:07.140><c> teams</c><00:00:07.560><c> and</c><00:00:07.799><c> startups</c><00:00:08.340><c> and</c>

00:00:08.690 --> 00:00:08.700 align:start position:0%
engineering teams and startups and
 

00:00:08.700 --> 00:00:11.089 align:start position:0%
engineering teams and startups and
Enterprises<00:00:09.360><c> that</c><00:00:10.080><c> rely</c><00:00:10.440><c> on</c><00:00:10.559><c> the</c><00:00:10.740><c> platform</c>

00:00:11.089 --> 00:00:11.099 align:start position:0%
Enterprises that rely on the platform
 

00:00:11.099 --> 00:00:12.770 align:start position:0%
Enterprises that rely on the platform
for<00:00:11.580><c> their</c><00:00:11.760><c> day-to-day</c><00:00:12.360><c> software</c>

00:00:12.770 --> 00:00:12.780 align:start position:0%
for their day-to-day software
 

00:00:12.780 --> 00:00:13.850 align:start position:0%
for their day-to-day software
development

00:00:13.850 --> 00:00:13.860 align:start position:0%
development
 

00:00:13.860 --> 00:00:15.890 align:start position:0%
development
with<00:00:14.280><c> so</c><00:00:14.460><c> much</c><00:00:14.580><c> development</c><00:00:15.059><c> on</c><00:00:15.420><c> the</c><00:00:15.540><c> platform</c>

00:00:15.890 --> 00:00:15.900 align:start position:0%
with so much development on the platform
 

00:00:15.900 --> 00:00:18.349 align:start position:0%
with so much development on the platform
GitHub<00:00:16.500><c> has</c><00:00:16.740><c> a</c><00:00:16.980><c> lot</c><00:00:17.100><c> of</c><00:00:17.279><c> valuable</c><00:00:17.580><c> insights</c><00:00:18.119><c> to</c>

00:00:18.349 --> 00:00:18.359 align:start position:0%
GitHub has a lot of valuable insights to
 

00:00:18.359 --> 00:00:19.970 align:start position:0%
GitHub has a lot of valuable insights to
share<00:00:18.539><c> with</c><00:00:18.840><c> open</c><00:00:19.020><c> source</c><00:00:19.500><c> researchers</c>

00:00:19.970 --> 00:00:19.980 align:start position:0%
share with open source researchers
 

00:00:19.980 --> 00:00:23.330 align:start position:0%
share with open source researchers
policy<00:00:20.939><c> makers</c><00:00:21.300><c> and</c><00:00:21.840><c> developers</c><00:00:22.320><c> about</c><00:00:22.980><c> the</c>

00:00:23.330 --> 00:00:23.340 align:start position:0%
policy makers and developers about the
 

00:00:23.340 --> 00:00:25.189 align:start position:0%
policy makers and developers about the
current<00:00:23.520><c> state</c><00:00:23.880><c> of</c><00:00:24.300><c> Open</c><00:00:24.480><c> Source</c><00:00:24.960><c> software</c>

00:00:25.189 --> 00:00:25.199 align:start position:0%
current state of Open Source software
 

00:00:25.199 --> 00:00:27.710 align:start position:0%
current state of Open Source software
development<00:00:25.680><c> it's</c><00:00:26.519><c> why</c><00:00:26.760><c> we</c><00:00:26.939><c> are</c><00:00:27.119><c> excited</c><00:00:27.539><c> to</c>

00:00:27.710 --> 00:00:27.720 align:start position:0%
development it's why we are excited to
 

00:00:27.720 --> 00:00:29.210 align:start position:0%
development it's why we are excited to
share<00:00:27.960><c> the</c><00:00:28.199><c> news</c><00:00:28.320><c> about</c><00:00:28.680><c> the</c><00:00:28.920><c> GitHub</c>

00:00:29.210 --> 00:00:29.220 align:start position:0%
share the news about the GitHub
 

00:00:29.220 --> 00:00:31.370 align:start position:0%
share the news about the GitHub
Innovation<00:00:29.699><c> graph</c><00:00:30.119><c> the</c><00:00:30.720><c> GitHub</c><00:00:30.960><c> Innovation</c>

00:00:31.370 --> 00:00:31.380 align:start position:0%
Innovation graph the GitHub Innovation
 

00:00:31.380 --> 00:00:33.590 align:start position:0%
Innovation graph the GitHub Innovation
graph<00:00:31.740><c> is</c><00:00:32.160><c> a</c><00:00:32.340><c> space</c><00:00:32.520><c> where</c><00:00:32.880><c> you</c><00:00:33.059><c> can</c><00:00:33.180><c> find</c><00:00:33.360><c> out</c>

00:00:33.590 --> 00:00:33.600 align:start position:0%
graph is a space where you can find out
 

00:00:33.600 --> 00:00:36.049 align:start position:0%
graph is a space where you can find out
some<00:00:33.840><c> of</c><00:00:33.960><c> the</c><00:00:34.140><c> latest</c><00:00:34.320><c> trends</c><00:00:34.860><c> from</c><00:00:35.640><c> software</c>

00:00:36.049 --> 00:00:36.059 align:start position:0%
some of the latest trends from software
 

00:00:36.059 --> 00:00:38.049 align:start position:0%
some of the latest trends from software
output<00:00:36.480><c> to</c><00:00:36.899><c> popular</c><00:00:37.079><c> programming</c><00:00:37.739><c> languages</c>

00:00:38.049 --> 00:00:38.059 align:start position:0%
output to popular programming languages
 

00:00:38.059 --> 00:00:40.549 align:start position:0%
output to popular programming languages
or<00:00:39.059><c> popular</c><00:00:39.300><c> software</c><00:00:39.840><c> project</c><00:00:40.020><c> topics</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
or popular software project topics
 

00:00:40.559 --> 00:00:43.010 align:start position:0%
or popular software project topics
through<00:00:40.860><c> to</c><00:00:41.040><c> economic</c><00:00:41.520><c> collaborators</c>

00:00:43.010 --> 00:00:43.020 align:start position:0%
through to economic collaborators
 

00:00:43.020 --> 00:00:45.350 align:start position:0%
through to economic collaborators
the<00:00:43.440><c> GitHub</c><00:00:43.739><c> Innovation</c><00:00:44.100><c> graph</c><00:00:44.460><c> empowers</c><00:00:45.059><c> you</c>

00:00:45.350 --> 00:00:45.360 align:start position:0%
the GitHub Innovation graph empowers you
 

00:00:45.360 --> 00:00:47.450 align:start position:0%
the GitHub Innovation graph empowers you
to<00:00:45.660><c> understand</c><00:00:45.840><c> the</c><00:00:46.320><c> latest</c><00:00:46.440><c> trends</c><00:00:46.980><c> and</c><00:00:47.340><c> take</c>

00:00:47.450 --> 00:00:47.460 align:start position:0%
to understand the latest trends and take
 

00:00:47.460 --> 00:00:49.190 align:start position:0%
to understand the latest trends and take
action<00:00:47.760><c> on</c><00:00:48.000><c> them</c>

00:00:49.190 --> 00:00:49.200 align:start position:0%
action on them
 

00:00:49.200 --> 00:00:51.170 align:start position:0%
action on them
to<00:00:49.620><c> find</c><00:00:49.800><c> out</c><00:00:49.980><c> more</c><00:00:50.219><c> check</c><00:00:50.579><c> out</c><00:00:50.700><c> the</c><00:00:50.879><c> GitHub</c>

00:00:51.170 --> 00:00:51.180 align:start position:0%
to find out more check out the GitHub
 

00:00:51.180 --> 00:00:53.270 align:start position:0%
to find out more check out the GitHub
Innovation<00:00:51.539><c> graph</c><00:00:51.960><c> website</c><00:00:52.379><c> or</c><00:00:52.800><c> the</c><00:00:52.920><c> data</c>

00:00:53.270 --> 00:00:53.280 align:start position:0%
Innovation graph website or the data
 

00:00:53.280 --> 00:00:55.640 align:start position:0%
Innovation graph website or the data
repo

