WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.819 align:start position:0%
 
hey<00:00:00.210><c> and</c><00:00:00.480><c> welcome</c><00:00:00.599><c> to</c><00:00:00.750><c> another</c><00:00:00.900><c> HTML</c><00:00:01.589><c> video</c>

00:00:01.819 --> 00:00:01.829 align:start position:0%
hey and welcome to another HTML video
 

00:00:01.829 --> 00:00:03.830 align:start position:0%
hey and welcome to another HTML video
and<00:00:02.280><c> this</c><00:00:02.820><c> time</c><00:00:03.000><c> we</c><00:00:03.270><c> are</c><00:00:03.389><c> going</c><00:00:03.540><c> to</c><00:00:03.629><c> be</c><00:00:03.689><c> talking</c>

00:00:03.830 --> 00:00:03.840 align:start position:0%
and this time we are going to be talking
 

00:00:03.840 --> 00:00:06.590 align:start position:0%
and this time we are going to be talking
about<00:00:03.990><c> tables</c><00:00:04.529><c> now</c><00:00:05.250><c> tables</c><00:00:05.910><c> are</c><00:00:06.150><c> essentially</c>

00:00:06.590 --> 00:00:06.600 align:start position:0%
about tables now tables are essentially
 

00:00:06.600 --> 00:00:09.350 align:start position:0%
about tables now tables are essentially
just<00:00:06.720><c> grids</c><00:00:07.130><c> they</c><00:00:08.130><c> call</c><00:00:08.340><c> them</c><00:00:08.429><c> two</c><00:00:08.639><c> axis</c><00:00:09.030><c> grids</c>

00:00:09.350 --> 00:00:09.360 align:start position:0%
just grids they call them two axis grids
 

00:00:09.360 --> 00:00:12.320 align:start position:0%
just grids they call them two axis grids
because<00:00:09.690><c> you</c><00:00:09.780><c> just</c><00:00:09.929><c> have</c><00:00:10.110><c> x</c><00:00:10.980><c> and</c><00:00:11.160><c> y</c><00:00:11.330><c> which</c>

00:00:12.320 --> 00:00:12.330 align:start position:0%
because you just have x and y which
 

00:00:12.330 --> 00:00:14.629 align:start position:0%
because you just have x and y which
stands<00:00:12.509><c> for</c><00:00:12.780><c> x</c><00:00:13.500><c> would</c><00:00:13.769><c> be</c><00:00:13.889><c> the</c><00:00:14.040><c> rows</c><00:00:14.250><c> and</c><00:00:14.549><c> y</c>

00:00:14.629 --> 00:00:14.639 align:start position:0%
stands for x would be the rows and y
 

00:00:14.639 --> 00:00:15.919 align:start position:0%
stands for x would be the rows and y
would<00:00:14.790><c> be</c><00:00:14.820><c> the</c><00:00:15.000><c> columns</c><00:00:15.360><c> so</c><00:00:15.599><c> you</c><00:00:15.660><c> just</c><00:00:15.750><c> have</c><00:00:15.870><c> a</c>

00:00:15.919 --> 00:00:15.929 align:start position:0%
would be the columns so you just have a
 

00:00:15.929 --> 00:00:19.630 align:start position:0%
would be the columns so you just have a
bunch<00:00:16.049><c> of</c><00:00:16.199><c> rows</c><00:00:16.379><c> and</c><00:00:16.619><c> columns</c><00:00:16.730><c> and</c><00:00:17.730><c> a</c><00:00:17.850><c> grid</c><00:00:18.119><c> and</c>

00:00:19.630 --> 00:00:19.640 align:start position:0%
bunch of rows and columns and a grid and
 

00:00:19.640 --> 00:00:22.550 align:start position:0%
bunch of rows and columns and a grid and
the<00:00:20.640><c> main</c><00:00:21.119><c> use</c><00:00:21.359><c> is</c><00:00:21.630><c> when</c><00:00:22.050><c> you</c><00:00:22.140><c> have</c><00:00:22.260><c> a</c><00:00:22.289><c> lot</c><00:00:22.470><c> of</c>

00:00:22.550 --> 00:00:22.560 align:start position:0%
the main use is when you have a lot of
 

00:00:22.560 --> 00:00:25.519 align:start position:0%
the main use is when you have a lot of
data<00:00:22.769><c> and</c><00:00:23.100><c> needs</c><00:00:23.640><c> to</c><00:00:23.670><c> be</c><00:00:23.730><c> organized</c><00:00:24.269><c> so</c><00:00:24.600><c> that</c><00:00:25.140><c> a</c>

00:00:25.519 --> 00:00:25.529 align:start position:0%
data and needs to be organized so that a
 

00:00:25.529 --> 00:00:29.080 align:start position:0%
data and needs to be organized so that a
user<00:00:26.070><c> can</c><00:00:26.490><c> read</c><00:00:26.939><c> it</c><00:00:27.150><c> much</c><00:00:27.750><c> easier</c><00:00:28.050><c> than</c><00:00:28.380><c> just</c>

00:00:29.080 --> 00:00:29.090 align:start position:0%
user can read it much easier than just
 

00:00:29.090 --> 00:00:33.400 align:start position:0%
user can read it much easier than just
putting<00:00:30.090><c> it</c><00:00:30.269><c> in</c><00:00:30.359><c> text</c><00:00:30.810><c> on</c><00:00:31.470><c> a</c><00:00:31.500><c> web</c><00:00:31.710><c> page</c><00:00:32.329><c> so</c>

00:00:33.400 --> 00:00:33.410 align:start position:0%
putting it in text on a web page so
 

00:00:33.410 --> 00:00:37.610 align:start position:0%
putting it in text on a web page so
whenever<00:00:34.410><c> you</c><00:00:34.980><c> create</c><00:00:35.399><c> HTML</c><00:00:36.090><c> tables</c><00:00:36.899><c> there</c>

00:00:37.610 --> 00:00:37.620 align:start position:0%
whenever you create HTML tables there
 

00:00:37.620 --> 00:00:40.069 align:start position:0%
whenever you create HTML tables there
are<00:00:37.770><c> four</c><00:00:38.129><c> key</c><00:00:38.850><c> elements</c><00:00:39.090><c> that</c><00:00:39.510><c> you</c><00:00:39.750><c> need</c><00:00:39.960><c> to</c>

00:00:40.069 --> 00:00:40.079 align:start position:0%
are four key elements that you need to
 

00:00:40.079 --> 00:00:43.700 align:start position:0%
are four key elements that you need to
know<00:00:40.579><c> the</c><00:00:41.579><c> first</c><00:00:41.820><c> one</c><00:00:42.059><c> is</c><00:00:42.750><c> the</c><00:00:43.020><c> table</c><00:00:43.350><c> element</c>

00:00:43.700 --> 00:00:43.710 align:start position:0%
know the first one is the table element
 

00:00:43.710 --> 00:00:47.029 align:start position:0%
know the first one is the table element
and<00:00:44.390><c> you</c><00:00:45.390><c> mean</c><00:00:45.570><c> opening</c><00:00:45.960><c> and</c><00:00:46.050><c> closing</c><00:00:46.500><c> table</c>

00:00:47.029 --> 00:00:47.039 align:start position:0%
and you mean opening and closing table
 

00:00:47.039 --> 00:00:51.369 align:start position:0%
and you mean opening and closing table
tag<00:00:47.329><c> that's</c><00:00:48.329><c> and</c><00:00:48.660><c> everything</c><00:00:49.230><c> in</c><00:00:49.320><c> between</c><00:00:49.440><c> is</c>

00:00:51.369 --> 00:00:51.379 align:start position:0%
tag that's and everything in between is
 

00:00:51.379 --> 00:00:53.330 align:start position:0%
tag that's and everything in between is
anything<00:00:52.379><c> that's</c><00:00:52.469><c> in</c><00:00:52.620><c> between</c><00:00:52.710><c> the</c><00:00:53.039><c> table</c>

00:00:53.330 --> 00:00:53.340 align:start position:0%
anything that's in between the table
 

00:00:53.340 --> 00:00:56.500 align:start position:0%
anything that's in between the table
tags<00:00:53.550><c> that's</c><00:00:54.510><c> what's</c><00:00:55.020><c> gonna</c><00:00:55.170><c> make</c><00:00:55.260><c> it</c><00:00:55.469><c> in</c><00:00:55.860><c> HTML</c>

00:00:56.500 --> 00:00:56.510 align:start position:0%
tags that's what's gonna make it in HTML
 

00:00:56.510 --> 00:00:59.660 align:start position:0%
tags that's what's gonna make it in HTML
so<00:00:57.510><c> the</c><00:00:57.840><c> second</c><00:00:58.140><c> one</c><00:00:58.230><c> is</c><00:00:58.440><c> the</c><00:00:58.800><c> TR</c><00:00:59.190><c> element</c>

00:00:59.660 --> 00:00:59.670 align:start position:0%
so the second one is the TR element
 

00:00:59.670 --> 00:01:01.520 align:start position:0%
so the second one is the TR element
which<00:00:59.820><c> stands</c><00:01:00.030><c> for</c><00:01:00.090><c> table</c><00:01:00.270><c> row</c><00:01:00.600><c> and</c><00:01:00.899><c> that's</c>

00:01:01.520 --> 00:01:01.530 align:start position:0%
which stands for table row and that's
 

00:01:01.530 --> 00:01:05.530 align:start position:0%
which stands for table row and that's
literally<00:01:02.010><c> just</c><00:01:02.129><c> a</c><00:01:02.550><c> single</c><00:01:03.300><c> row</c><00:01:03.899><c> in</c><00:01:04.229><c> the</c><00:01:04.920><c> table</c>

00:01:05.530 --> 00:01:05.540 align:start position:0%
literally just a single row in the table
 

00:01:05.540 --> 00:01:08.960 align:start position:0%
literally just a single row in the table
next<00:01:06.540><c> one</c><00:01:06.750><c> we</c><00:01:06.869><c> have</c><00:01:06.900><c> is</c><00:01:07.049><c> the</c><00:01:07.380><c> th</c><00:01:07.830><c> element</c><00:01:08.070><c> that</c>

00:01:08.960 --> 00:01:08.970 align:start position:0%
next one we have is the th element that
 

00:01:08.970 --> 00:01:10.670 align:start position:0%
next one we have is the th element that
is<00:01:09.270><c> that</c><00:01:09.630><c> stands</c><00:01:09.780><c> for</c><00:01:09.840><c> table</c><00:01:10.020><c> heading</c><00:01:10.470><c> and</c>

00:01:10.670 --> 00:01:10.680 align:start position:0%
is that stands for table heading and
 

00:01:10.680 --> 00:01:15.429 align:start position:0%
is that stands for table heading and
that<00:01:11.670><c> you</c><00:01:12.330><c> can</c><00:01:12.979><c> for</c><00:01:13.979><c> all</c><00:01:14.040><c> the</c><00:01:14.159><c> columns</c><00:01:14.580><c> you</c><00:01:14.850><c> can</c>

00:01:15.429 --> 00:01:15.439 align:start position:0%
that you can for all the columns you can
 

00:01:15.439 --> 00:01:19.820 align:start position:0%
that you can for all the columns you can
denote<00:01:16.439><c> them</c><00:01:16.770><c> by</c><00:01:16.799><c> the</c><00:01:17.130><c> th</c><00:01:17.759><c> tag</c><00:01:18.060><c> and</c><00:01:18.680><c> gives</c><00:01:19.680><c> them</c>

00:01:19.820 --> 00:01:19.830 align:start position:0%
denote them by the th tag and gives them
 

00:01:19.830 --> 00:01:22.520 align:start position:0%
denote them by the th tag and gives them
more<00:01:19.979><c> structure</c><00:01:20.490><c> to</c><00:01:20.580><c> the</c><00:01:20.670><c> table</c><00:01:21.000><c> so</c><00:01:21.229><c> you</c><00:01:22.229><c> can</c>

00:01:22.520 --> 00:01:22.530 align:start position:0%
more structure to the table so you can
 

00:01:22.530 --> 00:01:26.060 align:start position:0%
more structure to the table so you can
give<00:01:22.710><c> the</c><00:01:22.799><c> columns</c><00:01:23.070><c> names</c><00:01:23.340><c> basically</c><00:01:24.030><c> and</c><00:01:25.070><c> the</c>

00:01:26.060 --> 00:01:26.070 align:start position:0%
give the columns names basically and the
 

00:01:26.070 --> 00:01:28.460 align:start position:0%
give the columns names basically and the
last<00:01:26.220><c> one</c><00:01:26.430><c> is</c><00:01:26.670><c> TD</c><00:01:27.270><c> element</c><00:01:27.450><c> which</c><00:01:28.170><c> gets</c><00:01:28.350><c> for</c>

00:01:28.460 --> 00:01:28.470 align:start position:0%
last one is TD element which gets for
 

00:01:28.470 --> 00:01:30.440 align:start position:0%
last one is TD element which gets for
table<00:01:28.680><c> data</c><00:01:28.829><c> and</c><00:01:29.369><c> that's</c><00:01:29.490><c> each</c><00:01:29.820><c> individual</c>

00:01:30.440 --> 00:01:30.450 align:start position:0%
table data and that's each individual
 

00:01:30.450 --> 00:01:32.960 align:start position:0%
table data and that's each individual
cell<00:01:30.869><c> that's</c><00:01:31.619><c> holding</c><00:01:32.009><c> a</c><00:01:32.100><c> piece</c><00:01:32.310><c> of</c><00:01:32.340><c> data</c><00:01:32.610><c> and</c>

00:01:32.960 --> 00:01:32.970 align:start position:0%
cell that's holding a piece of data and
 

00:01:32.970 --> 00:01:34.760 align:start position:0%
cell that's holding a piece of data and
that's<00:01:33.630><c> what</c><00:01:33.869><c> you</c><00:01:33.990><c> would</c><00:01:34.079><c> put</c><00:01:34.290><c> in</c><00:01:34.350><c> between</c>

00:01:34.760 --> 00:01:34.770 align:start position:0%
that's what you would put in between
 

00:01:34.770 --> 00:01:39.440 align:start position:0%
that's what you would put in between
opening<00:01:35.220><c> and</c><00:01:35.340><c> closing</c><00:01:35.729><c> TD</c><00:01:36.570><c> element</c><00:01:37.340><c> okay</c><00:01:38.450><c> so</c>

00:01:39.440 --> 00:01:39.450 align:start position:0%
opening and closing TD element okay so
 

00:01:39.450 --> 00:01:41.710 align:start position:0%
opening and closing TD element okay so
let's<00:01:39.600><c> go</c><00:01:39.689><c> ahead</c><00:01:39.780><c> and</c><00:01:39.869><c> look</c><00:01:40.140><c> at</c><00:01:40.619><c> an</c><00:01:41.100><c> example</c>

00:01:41.710 --> 00:01:41.720 align:start position:0%
let's go ahead and look at an example
 

00:01:41.720 --> 00:01:46.510 align:start position:0%
let's go ahead and look at an example
structure<00:01:42.720><c> of</c><00:01:42.840><c> a</c><00:01:42.899><c> table</c><00:01:44.689><c> so</c><00:01:45.689><c> as</c><00:01:45.720><c> you</c><00:01:45.869><c> can</c><00:01:46.020><c> see</c>

00:01:46.510 --> 00:01:46.520 align:start position:0%
structure of a table so as you can see
 

00:01:46.520 --> 00:01:49.969 align:start position:0%
structure of a table so as you can see
we<00:01:47.520><c> have</c><00:01:47.700><c> an</c><00:01:48.119><c> opening</c><00:01:48.930><c> and</c><00:01:48.990><c> closing</c><00:01:49.380><c> table</c><00:01:49.770><c> tag</c>

00:01:49.969 --> 00:01:49.979 align:start position:0%
we have an opening and closing table tag
 

00:01:49.979 --> 00:01:54.679 align:start position:0%
we have an opening and closing table tag
and<00:01:50.310><c> then</c><00:01:51.299><c> we</c><00:01:51.810><c> have</c><00:01:51.899><c> the</c><00:01:52.020><c> TR</c><00:01:53.180><c> on</c><00:01:54.180><c> the</c><00:01:54.329><c> second</c>

00:01:54.679 --> 00:01:54.689 align:start position:0%
and then we have the TR on the second
 

00:01:54.689 --> 00:01:56.929 align:start position:0%
and then we have the TR on the second
line<00:01:54.840><c> which</c><00:01:55.320><c> is</c><00:01:55.590><c> the</c><00:01:55.799><c> opening</c><00:01:56.130><c> table</c><00:01:56.520><c> row</c><00:01:56.700><c> tag</c>

00:01:56.929 --> 00:01:56.939 align:start position:0%
line which is the opening table row tag
 

00:01:56.939 --> 00:01:59.899 align:start position:0%
line which is the opening table row tag
so<00:01:57.270><c> now</c><00:01:57.479><c> we're</c><00:01:57.689><c> creating</c><00:01:58.140><c> a</c><00:01:58.259><c> single</c><00:01:58.710><c> row</c><00:01:59.520><c> in</c>

00:01:59.899 --> 00:01:59.909 align:start position:0%
so now we're creating a single row in
 

00:01:59.909 --> 00:02:03.560 align:start position:0%
so now we're creating a single row in
the<00:02:00.360><c> table</c><00:02:00.740><c> we</c><00:02:01.740><c> have</c><00:02:01.770><c> the</c><00:02:01.979><c> th</c><00:02:02.700><c> element</c><00:02:02.939><c> which</c>

00:02:03.560 --> 00:02:03.570 align:start position:0%
the table we have the th element which
 

00:02:03.570 --> 00:02:06.200 align:start position:0%
the table we have the th element which
again<00:02:03.930><c> stands</c><00:02:04.200><c> for</c><00:02:04.350><c> table</c><00:02:04.439><c> heading</c><00:02:04.939><c> or</c><00:02:05.939><c> table</c>

00:02:06.200 --> 00:02:06.210 align:start position:0%
again stands for table heading or table
 

00:02:06.210 --> 00:02:10.100 align:start position:0%
again stands for table heading or table
head<00:02:06.360><c> and</c><00:02:06.649><c> we're</c><00:02:08.209><c> we're</c><00:02:09.209><c> naming</c><00:02:09.360><c> with</c><00:02:10.020><c> the</c>

00:02:10.100 --> 00:02:10.110 align:start position:0%
head and we're we're naming with the
 

00:02:10.110 --> 00:02:11.589 align:start position:0%
head and we're we're naming with the
text<00:02:10.379><c> that</c><00:02:10.440><c> we</c><00:02:10.560><c> have</c><00:02:10.590><c> in</c><00:02:10.800><c> between</c><00:02:11.129><c> here</c><00:02:11.400><c> is</c>

00:02:11.589 --> 00:02:11.599 align:start position:0%
text that we have in between here is
 

00:02:11.599 --> 00:02:13.820 align:start position:0%
text that we have in between here is
what's<00:02:12.599><c> gonna</c><00:02:12.780><c> be</c><00:02:13.020><c> shown</c><00:02:13.349><c> at</c><00:02:13.530><c> the</c>

00:02:13.820 --> 00:02:13.830 align:start position:0%
what's gonna be shown at the
 

00:02:13.830 --> 00:02:17.440 align:start position:0%
what's gonna be shown at the
this<00:02:14.190><c> column</c><00:02:15.920><c> and</c><00:02:16.920><c> the</c><00:02:17.070><c> words</c><00:02:17.250><c> any</c>

00:02:17.440 --> 00:02:17.450 align:start position:0%
this column and the words any
 

00:02:17.450 --> 00:02:19.160 align:start position:0%
this column and the words any
programming<00:02:18.450><c> language</c><00:02:18.540><c> so</c><00:02:18.870><c> we</c><00:02:18.960><c> have</c><00:02:19.080><c> an</c>

00:02:19.160 --> 00:02:19.170 align:start position:0%
programming language so we have an
 

00:02:19.170 --> 00:02:21.410 align:start position:0%
programming language so we have an
opening<00:02:19.470><c> and</c><00:02:19.620><c> closing</c><00:02:20.010><c> th</c><00:02:20.430><c> tag</c><00:02:20.700><c> and</c><00:02:21.000><c> text</c><00:02:21.330><c> in</c>

00:02:21.410 --> 00:02:21.420 align:start position:0%
opening and closing th tag and text in
 

00:02:21.420 --> 00:02:25.840 align:start position:0%
opening and closing th tag and text in
between<00:02:21.480><c> and</c><00:02:23.570><c> we</c><00:02:24.570><c> see</c><00:02:24.720><c> we</c><00:02:24.870><c> have</c><00:02:25.020><c> a</c><00:02:25.050><c> second</c>

00:02:25.840 --> 00:02:25.850 align:start position:0%
between and we see we have a second
 

00:02:25.850 --> 00:02:30.140 align:start position:0%
between and we see we have a second
opening<00:02:26.850><c> TR</c><00:02:27.450><c> tag</c><00:02:27.750><c> for</c><00:02:28.110><c> a</c><00:02:28.140><c> table</c><00:02:28.710><c> row</c><00:02:28.860><c> and</c><00:02:29.160><c> this</c>

00:02:30.140 --> 00:02:30.150 align:start position:0%
opening TR tag for a table row and this
 

00:02:30.150 --> 00:02:32.810 align:start position:0%
opening TR tag for a table row and this
is<00:02:30.360><c> where</c><00:02:30.950><c> and</c><00:02:31.950><c> then</c><00:02:32.070><c> and</c><00:02:32.340><c> then</c><00:02:32.460><c> the</c><00:02:32.580><c> next</c>

00:02:32.810 --> 00:02:32.820 align:start position:0%
is where and then and then the next
 

00:02:32.820 --> 00:02:36.290 align:start position:0%
is where and then and then the next
table<00:02:34.070><c> the</c><00:02:35.070><c> next</c><00:02:35.280><c> tag</c><00:02:35.400><c> is</c><00:02:35.550><c> the</c><00:02:35.670><c> TD</c><00:02:35.940><c> tag</c><00:02:36.180><c> which</c>

00:02:36.290 --> 00:02:36.300 align:start position:0%
table the next tag is the TD tag which
 

00:02:36.300 --> 00:02:37.610 align:start position:0%
table the next tag is the TD tag which
stands<00:02:36.450><c> for</c><00:02:36.570><c> table</c><00:02:36.630><c> data</c><00:02:36.810><c> and</c><00:02:37.170><c> this</c><00:02:37.290><c> is</c><00:02:37.350><c> where</c>

00:02:37.610 --> 00:02:37.620 align:start position:0%
stands for table data and this is where
 

00:02:37.620 --> 00:02:38.690 align:start position:0%
stands for table data and this is where
we're<00:02:37.860><c> holding</c><00:02:38.130><c> our</c><00:02:38.250><c> first</c><00:02:38.430><c> piece</c><00:02:38.670><c> of</c>

00:02:38.690 --> 00:02:38.700 align:start position:0%
we're holding our first piece of
 

00:02:38.700 --> 00:02:41.810 align:start position:0%
we're holding our first piece of
information<00:02:39.290><c> under</c><00:02:40.290><c> the</c><00:02:40.890><c> programming</c>

00:02:41.810 --> 00:02:41.820 align:start position:0%
information under the programming
 

00:02:41.820 --> 00:02:44.750 align:start position:0%
information under the programming
language<00:02:42.240><c> column</c><00:02:42.720><c> okay</c><00:02:43.560><c> and</c><00:02:43.800><c> then</c><00:02:44.610><c> of</c><00:02:44.700><c> course</c>

00:02:44.750 --> 00:02:44.760 align:start position:0%
language column okay and then of course
 

00:02:44.760 --> 00:02:48.350 align:start position:0%
language column okay and then of course
we<00:02:45.030><c> have</c><00:02:45.150><c> our</c><00:02:45.180><c> closing</c><00:02:45.630><c> table</c><00:02:46.560><c> tag</c><00:02:47.120><c> this</c><00:02:48.120><c> might</c>

00:02:48.350 --> 00:02:48.360 align:start position:0%
we have our closing table tag this might
 

00:02:48.360 --> 00:02:50.630 align:start position:0%
we have our closing table tag this might
like<00:02:48.540><c> make</c><00:02:48.900><c> a</c><00:02:48.930><c> little</c><00:02:49.050><c> more</c><00:02:49.200><c> sense</c><00:02:49.410><c> when</c><00:02:50.040><c> we</c>

00:02:50.630 --> 00:02:50.640 align:start position:0%
like make a little more sense when we
 

00:02:50.640 --> 00:02:53.800 align:start position:0%
like make a little more sense when we
start<00:02:51.060><c> coding</c><00:02:51.450><c> but</c><00:02:51.990><c> this</c><00:02:52.500><c> is</c><00:02:52.680><c> the</c><00:02:52.800><c> output</c><00:02:53.010><c> of</c>

00:02:53.800 --> 00:02:53.810 align:start position:0%
start coding but this is the output of
 

00:02:53.810 --> 00:02:55.970 align:start position:0%
start coding but this is the output of
of<00:02:54.810><c> this</c><00:02:55.410><c> curve</c><00:02:55.620><c> right</c><00:02:55.710><c> here</c>

00:02:55.970 --> 00:02:55.980 align:start position:0%
of this curve right here
 

00:02:55.980 --> 00:03:00.710 align:start position:0%
of this curve right here
of<00:02:56.370><c> course</c><00:02:56.640><c> I</c><00:02:56.910><c> added</c><00:02:57.720><c> another</c><00:02:58.250><c> row</c><00:02:59.250><c> of</c><00:02:59.280><c> data</c><00:02:59.720><c> so</c>

00:03:00.710 --> 00:03:00.720 align:start position:0%
of course I added another row of data so
 

00:03:00.720 --> 00:03:03.470 align:start position:0%
of course I added another row of data so
if<00:03:00.810><c> you</c><00:03:00.900><c> take</c><00:03:01.110><c> the</c><00:03:01.290><c> opening</c><00:03:01.710><c> TR</c><00:03:02.160><c> where</c><00:03:03.150><c> it</c><00:03:03.300><c> has</c>

00:03:03.470 --> 00:03:03.480 align:start position:0%
if you take the opening TR where it has
 

00:03:03.480 --> 00:03:05.900 align:start position:0%
if you take the opening TR where it has
the<00:03:03.780><c> Java</c><00:03:04.020><c> text</c><00:03:04.440><c> and</c><00:03:04.590><c> then</c><00:03:04.740><c> the</c><00:03:04.830><c> closing</c><00:03:05.070><c> TR</c>

00:03:05.900 --> 00:03:05.910 align:start position:0%
the Java text and then the closing TR
 

00:03:05.910 --> 00:03:07.880 align:start position:0%
the Java text and then the closing TR
element<00:03:06.510><c> I</c><00:03:06.690><c> just</c><00:03:07.170><c> added</c><00:03:07.350><c> another</c><00:03:07.620><c> one</c><00:03:07.800><c> of</c>

00:03:07.880 --> 00:03:07.890 align:start position:0%
element I just added another one of
 

00:03:07.890 --> 00:03:10.970 align:start position:0%
element I just added another one of
those<00:03:08.040><c> and</c><00:03:08.370><c> replace</c><00:03:08.700><c> Java</c><00:03:09.000><c> with</c><00:03:09.210><c> Python</c><00:03:09.980><c> so</c>

00:03:10.970 --> 00:03:10.980 align:start position:0%
those and replace Java with Python so
 

00:03:10.980 --> 00:03:12.860 align:start position:0%
those and replace Java with Python so
you<00:03:11.040><c> see</c><00:03:11.370><c> the</c><00:03:11.520><c> th</c><00:03:11.850><c> element</c><00:03:12.330><c> has</c><00:03:12.540><c> a</c><00:03:12.570><c> Miller</c>

00:03:12.860 --> 00:03:12.870 align:start position:0%
you see the th element has a Miller
 

00:03:12.870 --> 00:03:14.930 align:start position:0%
you see the th element has a Miller
property<00:03:13.410><c> inherent</c><00:03:14.130><c> property</c><00:03:14.490><c> with</c><00:03:14.610><c> it</c><00:03:14.760><c> where</c>

00:03:14.930 --> 00:03:14.940 align:start position:0%
property inherent property with it where
 

00:03:14.940 --> 00:03:18.590 align:start position:0%
property inherent property with it where
it<00:03:15.030><c> makes</c><00:03:15.210><c> the</c><00:03:15.690><c> text</c><00:03:16.230><c> bold</c><00:03:16.440><c> and</c><00:03:17.390><c> anything</c><00:03:18.390><c> in</c>

00:03:18.590 --> 00:03:18.600 align:start position:0%
it makes the text bold and anything in
 

00:03:18.600 --> 00:03:22.280 align:start position:0%
it makes the text bold and anything in
the<00:03:18.720><c> T</c><00:03:19.010><c> within</c><00:03:20.010><c> a</c><00:03:20.130><c> TV</c><00:03:21.090><c> element</c><00:03:21.840><c> is</c><00:03:22.050><c> just</c>

00:03:22.280 --> 00:03:22.290 align:start position:0%
the T within a TV element is just
 

00:03:22.290 --> 00:03:26.449 align:start position:0%
the T within a TV element is just
regular<00:03:23.130><c> text</c><00:03:23.430><c> okay</c><00:03:24.590><c> so</c><00:03:25.590><c> this</c><00:03:25.680><c> is</c><00:03:25.830><c> the</c><00:03:26.070><c> main</c>

00:03:26.449 --> 00:03:26.459 align:start position:0%
regular text okay so this is the main
 

00:03:26.459 --> 00:03:28.820 align:start position:0%
regular text okay so this is the main
structure<00:03:26.940><c> of</c><00:03:27.360><c> how</c><00:03:28.080><c> you</c><00:03:28.140><c> would</c><00:03:28.350><c> create</c><00:03:28.800><c> a</c>

00:03:28.820 --> 00:03:28.830 align:start position:0%
structure of how you would create a
 

00:03:28.830 --> 00:03:30.910 align:start position:0%
structure of how you would create a
table<00:03:29.070><c> there</c><00:03:29.400><c> are</c><00:03:29.430><c> a</c><00:03:29.520><c> couple</c><00:03:29.700><c> other</c>

00:03:30.910 --> 00:03:30.920 align:start position:0%
table there are a couple other
 

00:03:30.920 --> 00:03:33.530 align:start position:0%
table there are a couple other
attributes<00:03:31.920><c> that</c><00:03:32.040><c> you</c><00:03:32.100><c> can</c><00:03:32.250><c> have</c><00:03:32.370><c> disease</c><00:03:32.610><c> but</c>

00:03:33.530 --> 00:03:33.540 align:start position:0%
attributes that you can have disease but
 

00:03:33.540 --> 00:03:36.320 align:start position:0%
attributes that you can have disease but
like<00:03:34.110><c> I've</c><00:03:34.290><c> said</c><00:03:34.350><c> in</c><00:03:34.650><c> the</c><00:03:34.739><c> past</c><00:03:35.010><c> video</c><00:03:35.330><c> we're</c>

00:03:36.320 --> 00:03:36.330 align:start position:0%
like I've said in the past video we're
 

00:03:36.330 --> 00:03:40.760 align:start position:0%
like I've said in the past video we're
gonna<00:03:37.190><c> style</c><00:03:38.660><c> style</c><00:03:39.660><c> of</c><00:03:39.810><c> tables</c><00:03:40.140><c> and</c><00:03:40.350><c> makes</c>

00:03:40.760 --> 00:03:40.770 align:start position:0%
gonna style style of tables and makes
 

00:03:40.770 --> 00:03:44.390 align:start position:0%
gonna style style of tables and makes
them<00:03:40.920><c> a</c><00:03:41.250><c> little</c><00:03:41.550><c> neater</c><00:03:42.050><c> with</c><00:03:43.050><c> CSS</c><00:03:43.950><c> we're</c><00:03:44.280><c> not</c>

00:03:44.390 --> 00:03:44.400 align:start position:0%
them a little neater with CSS we're not
 

00:03:44.400 --> 00:03:45.800 align:start position:0%
them a little neater with CSS we're not
gonna<00:03:44.580><c> do</c><00:03:44.790><c> that</c><00:03:44.850><c> with</c><00:03:45.000><c> HTML</c><00:03:45.390><c> because</c><00:03:45.540><c> that's</c>

00:03:45.800 --> 00:03:45.810 align:start position:0%
gonna do that with HTML because that's
 

00:03:45.810 --> 00:03:48.080 align:start position:0%
gonna do that with HTML because that's
not<00:03:46.020><c> the</c><00:03:46.320><c> point</c><00:03:46.560><c> of</c><00:03:46.800><c> HTML</c><00:03:47.220><c> hm</c><00:03:47.640><c> the</c><00:03:47.820><c> point</c><00:03:48.000><c> of</c>

00:03:48.080 --> 00:03:48.090 align:start position:0%
not the point of HTML hm the point of
 

00:03:48.090 --> 00:03:50.690 align:start position:0%
not the point of HTML hm the point of
HTML<00:03:48.390><c> is</c><00:03:48.450><c> just</c><00:03:48.630><c> again</c><00:03:49.110><c> do</c><00:03:49.709><c> you</c><00:03:49.770><c> structure</c><00:03:50.190><c> a</c>

00:03:50.690 --> 00:03:50.700 align:start position:0%
HTML is just again do you structure a
 

00:03:50.700 --> 00:03:53.930 align:start position:0%
HTML is just again do you structure a
web<00:03:51.270><c> page</c><00:03:51.530><c> CSS</c><00:03:52.530><c> Styles</c><00:03:53.400><c> everything</c><00:03:53.550><c> else</c>

00:03:53.930 --> 00:03:53.940 align:start position:0%
web page CSS Styles everything else
 

00:03:53.940 --> 00:03:56.810 align:start position:0%
web page CSS Styles everything else
that's<00:03:54.540><c> where</c><00:03:54.780><c> you</c><00:03:54.870><c> want</c><00:03:55.050><c> to</c><00:03:55.520><c> 911</c><00:03:56.520><c> of</c><00:03:56.670><c> time</c>

00:03:56.810 --> 00:03:56.820 align:start position:0%
that's where you want to 911 of time
 

00:03:56.820 --> 00:03:58.520 align:start position:0%
that's where you want to 911 of time
that's<00:03:57.060><c> where</c><00:03:57.209><c> you</c><00:03:57.330><c> want</c><00:03:57.480><c> to</c><00:03:57.540><c> do</c><00:03:57.690><c> all</c><00:03:58.140><c> of</c><00:03:58.200><c> your</c>

00:03:58.520 --> 00:03:58.530 align:start position:0%
that's where you want to do all of your
 

00:03:58.530 --> 00:04:01.850 align:start position:0%
that's where you want to do all of your
styling<00:03:58.920><c> is</c><00:03:59.010><c> in</c><00:03:59.100><c> a</c><00:03:59.160><c> CSS</c><00:03:59.610><c> file</c><00:04:00.500><c> so</c><00:04:01.500><c> next</c><00:04:01.680><c> we're</c>

00:04:01.850 --> 00:04:01.860 align:start position:0%
styling is in a CSS file so next we're
 

00:04:01.860 --> 00:04:04.130 align:start position:0%
styling is in a CSS file so next we're
gonna<00:04:01.950><c> do</c><00:04:02.100><c> is</c><00:04:02.370><c> we're</c><00:04:02.880><c> gonna</c><00:04:02.970><c> code</c><00:04:03.360><c> this</c><00:04:03.540><c> for</c>

00:04:04.130 --> 00:04:04.140 align:start position:0%
gonna do is we're gonna code this for
 

00:04:04.140 --> 00:04:09.130 align:start position:0%
gonna do is we're gonna code this for
the<00:04:04.230><c> first</c><00:04:04.470><c> thing</c><00:04:04.680><c> we</c><00:04:04.800><c> need</c><00:04:05.010><c> is</c><00:04:05.270><c> the</c><00:04:06.270><c> doctype</c>

00:04:09.130 --> 00:04:09.140 align:start position:0%
 
 

00:04:09.140 --> 00:04:12.490 align:start position:0%
 
we<00:04:10.140><c> need</c><00:04:10.290><c> an</c><00:04:10.410><c> opening</c><00:04:10.860><c> and</c><00:04:11.070><c> closing</c><00:04:11.670><c> HTML</c>

00:04:12.490 --> 00:04:12.500 align:start position:0%
we need an opening and closing HTML
 

00:04:12.500 --> 00:04:18.320 align:start position:0%
we need an opening and closing HTML
element<00:04:14.390><c> the</c><00:04:15.390><c> next</c><00:04:15.600><c> thing</c><00:04:15.750><c> we</c><00:04:15.840><c> need</c><00:04:15.870><c> is</c><00:04:16.790><c> a</c><00:04:17.790><c> head</c>

00:04:18.320 --> 00:04:18.330 align:start position:0%
element the next thing we need is a head
 

00:04:18.330 --> 00:04:21.380 align:start position:0%
element the next thing we need is a head
element<00:04:18.690><c> so</c><00:04:19.200><c> go</c><00:04:19.470><c> ahead</c><00:04:19.560><c> and</c><00:04:20.269><c> write</c><00:04:21.269><c> the</c>

00:04:21.380 --> 00:04:21.390 align:start position:0%
element so go ahead and write the
 

00:04:21.390 --> 00:04:23.719 align:start position:0%
element so go ahead and write the
opening<00:04:21.480><c> closing</c><00:04:21.930><c> one</c><00:04:22.280><c> inside</c><00:04:23.280><c> over</c><00:04:23.550><c> have</c><00:04:23.700><c> a</c>

00:04:23.719 --> 00:04:23.729 align:start position:0%
opening closing one inside over have a
 

00:04:23.729 --> 00:04:25.510 align:start position:0%
opening closing one inside over have a
title

00:04:25.510 --> 00:04:25.520 align:start position:0%
title
 

00:04:25.520 --> 00:04:30.280 align:start position:0%
title
let's<00:04:26.520><c> just</c><00:04:26.700><c> call</c><00:04:26.940><c> this</c><00:04:27.120><c> title</c><00:04:28.940><c> table</c><00:04:29.940><c> demo</c>

00:04:30.280 --> 00:04:30.290 align:start position:0%
let's just call this title table demo
 

00:04:30.290 --> 00:04:32.930 align:start position:0%
let's just call this title table demo
this<00:04:31.290><c> is</c><00:04:31.350><c> what's</c><00:04:31.620><c> going</c><00:04:31.710><c> to</c><00:04:31.800><c> appear</c><00:04:32.040><c> in</c><00:04:32.580><c> the</c>

00:04:32.930 --> 00:04:32.940 align:start position:0%
this is what's going to appear in the
 

00:04:32.940 --> 00:04:40.370 align:start position:0%
this is what's going to appear in the
browser<00:04:34.310><c> and</c><00:04:35.310><c> the</c><00:04:35.460><c> browser</c><00:04:35.490><c> bar</c><00:04:37.040><c> all</c><00:04:38.040><c> right</c><00:04:39.380><c> my</c>

00:04:40.370 --> 00:04:40.380 align:start position:0%
browser and the browser bar all right my
 

00:04:40.380 --> 00:04:43.730 align:start position:0%
browser and the browser bar all right my
browser<00:04:40.650><c> tab</c><00:04:40.920><c> sorry</c><00:04:41.370><c> and</c><00:04:42.050><c> we</c><00:04:43.050><c> need</c><00:04:43.200><c> an</c><00:04:43.320><c> opening</c>

00:04:43.730 --> 00:04:43.740 align:start position:0%
browser tab sorry and we need an opening
 

00:04:43.740 --> 00:04:46.040 align:start position:0%
browser tab sorry and we need an opening
and<00:04:44.130><c> closing</c><00:04:44.550><c> body</c><00:04:44.760><c> tag</c><00:04:45.090><c> because</c><00:04:45.420><c> this</c><00:04:45.840><c> is</c>

00:04:46.040 --> 00:04:46.050 align:start position:0%
and closing body tag because this is
 

00:04:46.050 --> 00:04:47.380 align:start position:0%
and closing body tag because this is
what's<00:04:46.290><c> actually</c><00:04:46.500><c> you</c><00:04:46.740><c> want</c><00:04:46.800><c> to</c><00:04:46.890><c> be</c><00:04:46.980><c> displayed</c>

00:04:47.380 --> 00:04:47.390 align:start position:0%
what's actually you want to be displayed
 

00:04:47.390 --> 00:04:49.820 align:start position:0%
what's actually you want to be displayed
anything<00:04:48.390><c> in</c><00:04:48.480><c> the</c><00:04:48.600><c> by</c><00:04:49.050><c> tag</c><00:04:49.290><c> is</c><00:04:49.440><c> going</c><00:04:49.560><c> to</c><00:04:49.650><c> be</c>

00:04:49.820 --> 00:04:49.830 align:start position:0%
anything in the by tag is going to be
 

00:04:49.830 --> 00:04:54.170 align:start position:0%
anything in the by tag is going to be
displayed<00:04:50.250><c> in</c><00:04:50.520><c> the</c><00:04:50.880><c> browser</c><00:04:51.000><c> so</c><00:04:52.940><c> okay</c><00:04:53.940><c> so</c>

00:04:54.170 --> 00:04:54.180 align:start position:0%
displayed in the browser so okay so
 

00:04:54.180 --> 00:04:56.540 align:start position:0%
displayed in the browser so okay so
we're<00:04:54.660><c> gonna</c><00:04:54.750><c> start</c><00:04:55.010><c> coding</c><00:04:56.010><c> up</c><00:04:56.070><c> an</c><00:04:56.280><c> actual</c>

00:04:56.540 --> 00:04:56.550 align:start position:0%
we're gonna start coding up an actual
 

00:04:56.550 --> 00:04:59.390 align:start position:0%
we're gonna start coding up an actual
table<00:04:56.850><c> the</c><00:04:57.690><c> first</c><00:04:57.990><c> thing</c><00:04:58.230><c> you</c><00:04:58.350><c> need</c><00:04:58.530><c> is</c><00:04:59.160><c> an</c>

00:04:59.390 --> 00:04:59.400 align:start position:0%
table the first thing you need is an
 

00:04:59.400 --> 00:05:01.670 align:start position:0%
table the first thing you need is an
opening<00:04:59.760><c> and</c><00:04:59.850><c> closing</c><00:05:00.300><c> table</c><00:05:00.660><c> element</c><00:05:00.990><c> very</c>

00:05:01.670 --> 00:05:01.680 align:start position:0%
opening and closing table element very
 

00:05:01.680 --> 00:05:04.970 align:start position:0%
opening and closing table element very
first<00:05:01.890><c> thing</c><00:05:02.070><c> alright</c><00:05:02.930><c> the</c><00:05:03.930><c> next</c><00:05:03.960><c> thing</c><00:05:04.200><c> is</c><00:05:04.560><c> we</c>

00:05:04.970 --> 00:05:04.980 align:start position:0%
first thing alright the next thing is we
 

00:05:04.980 --> 00:05:06.890 align:start position:0%
first thing alright the next thing is we
want<00:05:05.640><c> to</c><00:05:05.670><c> have</c><00:05:05.790><c> our</c><00:05:06.060><c> heading</c><00:05:06.480><c> so</c><00:05:06.660><c> we</c><00:05:06.750><c> have</c>

00:05:06.890 --> 00:05:06.900 align:start position:0%
want to have our heading so we have
 

00:05:06.900 --> 00:05:09.920 align:start position:0%
want to have our heading so we have
three<00:05:07.650><c> columns</c><00:05:08.360><c> one</c><00:05:09.360><c> for</c><00:05:09.390><c> programming</c>

00:05:09.920 --> 00:05:09.930 align:start position:0%
three columns one for programming
 

00:05:09.930 --> 00:05:12.680 align:start position:0%
three columns one for programming
language<00:05:10.290><c> first</c><00:05:11.280><c> first</c><00:05:11.970><c> name</c><00:05:12.120><c> and</c><00:05:12.390><c> last</c><00:05:12.570><c> name</c>

00:05:12.680 --> 00:05:12.690 align:start position:0%
language first first name and last name
 

00:05:12.690 --> 00:05:15.530 align:start position:0%
language first first name and last name
so<00:05:13.680><c> but</c><00:05:14.340><c> that's</c><00:05:14.430><c> still</c><00:05:14.670><c> a</c><00:05:14.730><c> table</c><00:05:15.150><c> right</c><00:05:15.360><c> that's</c>

00:05:15.530 --> 00:05:15.540 align:start position:0%
so but that's still a table right that's
 

00:05:15.540 --> 00:05:19.100 align:start position:0%
so but that's still a table right that's
still<00:05:15.690><c> a</c><00:05:15.720><c> row</c><00:05:15.960><c> data</c><00:05:16.290><c> so</c><00:05:17.280><c> we</c><00:05:17.550><c> need</c><00:05:17.760><c> an</c><00:05:18.110><c> opening</c>

00:05:19.100 --> 00:05:19.110 align:start position:0%
still a row data so we need an opening
 

00:05:19.110 --> 00:05:24.800 align:start position:0%
still a row data so we need an opening
and<00:05:19.260><c> closing</c><00:05:20.090><c> TR</c><00:05:21.090><c> tag</c><00:05:21.410><c> for</c><00:05:23.660><c> for</c><00:05:24.660><c> those</c>

00:05:24.800 --> 00:05:24.810 align:start position:0%
and closing TR tag for for those
 

00:05:24.810 --> 00:05:27.410 align:start position:0%
and closing TR tag for for those
headings<00:05:25.190><c> the</c><00:05:26.190><c> next</c><00:05:26.370><c> thing</c><00:05:26.490><c> we</c><00:05:26.580><c> need</c><00:05:26.610><c> is</c><00:05:26.910><c> a</c><00:05:26.940><c> th</c>

00:05:27.410 --> 00:05:27.420 align:start position:0%
headings the next thing we need is a th
 

00:05:27.420 --> 00:05:30.820 align:start position:0%
headings the next thing we need is a th
for<00:05:28.290><c> table</c><00:05:28.740><c> heading</c><00:05:28.950><c> almost</c><00:05:29.880><c> programming</c>

00:05:30.820 --> 00:05:30.830 align:start position:0%
for table heading almost programming
 

00:05:30.830 --> 00:05:34.740 align:start position:0%
for table heading almost programming
language

00:05:34.740 --> 00:05:34.750 align:start position:0%
 
 

00:05:34.750 --> 00:05:38.340 align:start position:0%
 
if<00:05:34.840><c> you</c><00:05:34.960><c> want</c><00:05:35.140><c> just</c><00:05:35.830><c> like</c><00:05:36.180><c> simple</c><00:05:37.180><c> trick</c><00:05:37.360><c> you</c>

00:05:38.340 --> 00:05:38.350 align:start position:0%
if you want just like simple trick you
 

00:05:38.350 --> 00:05:40.440 align:start position:0%
if you want just like simple trick you
can<00:05:38.500><c> copy</c><00:05:38.800><c> this</c><00:05:39.040><c> so</c><00:05:39.520><c> if</c><00:05:39.730><c> you're</c><00:05:40.210><c> at</c><00:05:40.300><c> the</c><00:05:40.420><c> end</c>

00:05:40.440 --> 00:05:40.450 align:start position:0%
can copy this so if you're at the end
 

00:05:40.450 --> 00:05:43.350 align:start position:0%
can copy this so if you're at the end
and<00:05:41.230><c> give</c><00:05:41.350><c> a</c><00:05:41.380><c> shift</c><00:05:41.770><c> home</c><00:05:42.040><c> that'll</c><00:05:43.000><c> just</c><00:05:43.150><c> do</c>

00:05:43.350 --> 00:05:43.360 align:start position:0%
and give a shift home that'll just do
 

00:05:43.360 --> 00:05:45.960 align:start position:0%
and give a shift home that'll just do
I'll<00:05:43.870><c> just</c><00:05:44.020><c> copy</c><00:05:44.280><c> everything</c><00:05:45.280><c> back</c><00:05:45.670><c> to</c><00:05:45.850><c> the</c>

00:05:45.960 --> 00:05:45.970 align:start position:0%
I'll just copy everything back to the
 

00:05:45.970 --> 00:05:48.800 align:start position:0%
I'll just copy everything back to the
beginning<00:05:46.360><c> of</c><00:05:46.480><c> the</c><00:05:46.570><c> line</c><00:05:47.310><c> control</c><00:05:48.310><c> C</c><00:05:48.460><c> copy</c>

00:05:48.800 --> 00:05:48.810 align:start position:0%
beginning of the line control C copy
 

00:05:48.810 --> 00:05:55.740 align:start position:0%
beginning of the line control C copy
enter<00:05:49.810><c> paste</c><00:05:50.400><c> enter</c><00:05:51.400><c> paste</c><00:05:51.610><c> and</c><00:05:52.420><c> then</c><00:05:54.750><c> there's</c>

00:05:55.740 --> 00:05:55.750 align:start position:0%
enter paste enter paste and then there's
 

00:05:55.750 --> 00:05:56.760 align:start position:0%
enter paste enter paste and then there's
some<00:05:55.900><c> other</c><00:05:55.960><c> shortcuts</c><00:05:56.440><c> here</c><00:05:56.620><c> I'll</c><00:05:56.740><c> just</c>

00:05:56.760 --> 00:05:56.770 align:start position:0%
some other shortcuts here I'll just
 

00:05:56.770 --> 00:06:00.210 align:start position:0%
some other shortcuts here I'll just
highlight<00:05:57.520><c> that</c><00:05:57.760><c> but</c><00:05:58.740><c> so</c><00:05:59.740><c> if</c><00:05:59.860><c> you</c><00:05:59.950><c> don't</c><00:06:00.100><c> want</c>

00:06:00.210 --> 00:06:00.220 align:start position:0%
highlight that but so if you don't want
 

00:06:00.220 --> 00:06:02.370 align:start position:0%
highlight that but so if you don't want
to<00:06:00.280><c> retype</c><00:06:01.270><c> everything</c><00:06:01.600><c> I</c><00:06:01.660><c> suggest</c><00:06:02.200><c> you</c>

00:06:02.370 --> 00:06:02.380 align:start position:0%
to retype everything I suggest you
 

00:06:02.380 --> 00:06:05.280 align:start position:0%
to retype everything I suggest you
actually<00:06:03.370><c> type</c><00:06:03.880><c> every</c><00:06:04.690><c> character</c><00:06:05.080><c> in</c><00:06:05.200><c> the</c>

00:06:05.280 --> 00:06:05.290 align:start position:0%
actually type every character in the
 

00:06:05.290 --> 00:06:06.690 align:start position:0%
actually type every character in the
code<00:06:05.500><c> that</c><00:06:05.530><c> I'm</c><00:06:05.830><c> doing</c><00:06:05.980><c> because</c><00:06:06.280><c> it's</c><00:06:06.580><c> gonna</c>

00:06:06.690 --> 00:06:06.700 align:start position:0%
code that I'm doing because it's gonna
 

00:06:06.700 --> 00:06:09.409 align:start position:0%
code that I'm doing because it's gonna
help<00:06:07.440><c> gonna</c><00:06:08.440><c> help</c><00:06:08.560><c> you</c><00:06:08.650><c> understand</c><00:06:09.040><c> it</c><00:06:09.220><c> and</c>

00:06:09.409 --> 00:06:09.419 align:start position:0%
help gonna help you understand it and
 

00:06:09.419 --> 00:06:14.930 align:start position:0%
help gonna help you understand it and
like<00:06:10.419><c> muscle</c><00:06:10.780><c> memory</c><00:06:10.810><c> I</c><00:06:11.169><c> think</c><00:06:11.640><c> so</c><00:06:13.410><c> first</c><00:06:14.410><c> name</c>

00:06:14.930 --> 00:06:14.940 align:start position:0%
like muscle memory I think so first name
 

00:06:14.940 --> 00:06:19.980 align:start position:0%
like muscle memory I think so first name
and<00:06:16.350><c> then</c><00:06:17.350><c> the</c><00:06:17.650><c> last</c><00:06:17.830><c> name</c><00:06:18.390><c> all</c><00:06:19.390><c> right</c><00:06:19.450><c> so</c><00:06:19.900><c> we</c>

00:06:19.980 --> 00:06:19.990 align:start position:0%
and then the last name all right so we
 

00:06:19.990 --> 00:06:22.680 align:start position:0%
and then the last name all right so we
have<00:06:20.020><c> a</c><00:06:20.110><c> first</c><00:06:20.410><c> row</c><00:06:20.680><c> and</c><00:06:20.919><c> we</c><00:06:21.669><c> have</c><00:06:21.820><c> three</c><00:06:22.390><c> table</c>

00:06:22.680 --> 00:06:22.690 align:start position:0%
have a first row and we have three table
 

00:06:22.690 --> 00:06:24.180 align:start position:0%
have a first row and we have three table
headings<00:06:22.960><c> so</c><00:06:23.290><c> one</c><00:06:23.440><c> for</c><00:06:23.560><c> programming</c><00:06:23.919><c> language</c>

00:06:24.180 --> 00:06:24.190 align:start position:0%
headings so one for programming language
 

00:06:24.190 --> 00:06:25.890 align:start position:0%
headings so one for programming language
first<00:06:24.550><c> name</c><00:06:24.730><c> and</c><00:06:24.850><c> last</c><00:06:24.910><c> name</c><00:06:25.120><c> so</c><00:06:25.330><c> that</c><00:06:25.360><c> equates</c>

00:06:25.890 --> 00:06:25.900 align:start position:0%
first name and last name so that equates
 

00:06:25.900 --> 00:06:30.030 align:start position:0%
first name and last name so that equates
to<00:06:26.080><c> having</c><00:06:26.919><c> three</c><00:06:27.070><c> columns</c><00:06:28.919><c> that's</c><00:06:29.919><c> what</c>

00:06:30.030 --> 00:06:30.040 align:start position:0%
to having three columns that's what
 

00:06:30.040 --> 00:06:32.820 align:start position:0%
to having three columns that's what
their<00:06:30.190><c> names</c><00:06:30.640><c> are</c><00:06:30.700><c> going</c><00:06:30.820><c> to</c><00:06:30.880><c> be</c><00:06:31.260><c> so</c><00:06:32.260><c> now</c><00:06:32.770><c> we</c>

00:06:32.820 --> 00:06:32.830 align:start position:0%
their names are going to be so now we
 

00:06:32.830 --> 00:06:37.230 align:start position:0%
their names are going to be so now we
need<00:06:33.040><c> a</c><00:06:33.100><c> first</c><00:06:33.400><c> actual</c><00:06:33.970><c> row</c><00:06:34.270><c> of</c><00:06:34.600><c> data</c><00:06:36.240><c> so</c>

00:06:37.230 --> 00:06:37.240 align:start position:0%
need a first actual row of data so
 

00:06:37.240 --> 00:06:39.210 align:start position:0%
need a first actual row of data so
another<00:06:37.660><c> opening</c><00:06:38.050><c> and</c><00:06:38.169><c> closing</c><00:06:38.500><c> TR</c><00:06:38.740><c> element</c>

00:06:39.210 --> 00:06:39.220 align:start position:0%
another opening and closing TR element
 

00:06:39.220 --> 00:06:42.510 align:start position:0%
another opening and closing TR element
for<00:06:39.520><c> table</c><00:06:39.760><c> row</c><00:06:39.990><c> we</c><00:06:40.990><c> need</c><00:06:41.200><c> our</c><00:06:41.560><c> first</c><00:06:41.830><c> TD</c>

00:06:42.510 --> 00:06:42.520 align:start position:0%
for table row we need our first TD
 

00:06:42.520 --> 00:06:46.050 align:start position:0%
for table row we need our first TD
element<00:06:43.120><c> which</c><00:06:43.630><c> table</c><00:06:44.020><c> data</c><00:06:44.669><c> it's</c><00:06:45.669><c> a</c><00:06:45.729><c> bed</c><00:06:45.910><c> and</c>

00:06:46.050 --> 00:06:46.060 align:start position:0%
element which table data it's a bed and
 

00:06:46.060 --> 00:06:48.050 align:start position:0%
element which table data it's a bed and
write<00:06:46.210><c> your</c><00:06:46.360><c> opening</c><00:06:46.630><c> and</c><00:06:46.690><c> closing</c><00:06:47.080><c> one</c><00:06:47.229><c> and</c>

00:06:48.050 --> 00:06:48.060 align:start position:0%
write your opening and closing one and
 

00:06:48.060 --> 00:06:50.460 align:start position:0%
write your opening and closing one and
so<00:06:49.060><c> the</c><00:06:49.210><c> first</c><00:06:49.360><c> programming</c><00:06:49.840><c> language</c><00:06:49.870><c> that</c>

00:06:50.460 --> 00:06:50.470 align:start position:0%
so the first programming language that
 

00:06:50.470 --> 00:06:53.969 align:start position:0%
so the first programming language that
we<00:06:50.680><c> want</c><00:06:50.890><c> to</c><00:06:51.810><c> have</c><00:06:52.810><c> as</c><00:06:53.260><c> our</c><00:06:53.470><c> first</c><00:06:53.650><c> piece</c><00:06:53.830><c> of</c>

00:06:53.969 --> 00:06:53.979 align:start position:0%
we want to have as our first piece of
 

00:06:53.979 --> 00:06:57.480 align:start position:0%
we want to have as our first piece of
data<00:06:54.160><c> we'll</c><00:06:54.640><c> just</c><00:06:54.790><c> do</c><00:06:55.090><c> Java</c><00:06:56.160><c> because</c><00:06:57.160><c> this</c><00:06:57.370><c> is</c>

00:06:57.480 --> 00:06:57.490 align:start position:0%
data we'll just do Java because this is
 

00:06:57.490 --> 00:06:59.340 align:start position:0%
data we'll just do Java because this is
what's<00:06:57.669><c> gonna</c><00:06:57.729><c> be</c><00:06:57.910><c> under</c><00:06:58.330><c> the</c><00:06:58.660><c> programming</c>

00:06:59.340 --> 00:06:59.350 align:start position:0%
what's gonna be under the programming
 

00:06:59.350 --> 00:07:04.140 align:start position:0%
what's gonna be under the programming
language<00:06:59.830><c> editor</c><00:07:00.400><c> okay</c><00:07:01.440><c> the</c><00:07:02.440><c> next</c><00:07:02.620><c> one</c><00:07:02.830><c> oh</c><00:07:03.400><c> so</c>

00:07:04.140 --> 00:07:04.150 align:start position:0%
language editor okay the next one oh so
 

00:07:04.150 --> 00:07:05.790 align:start position:0%
language editor okay the next one oh so
then<00:07:04.300><c> then</c><00:07:04.930><c> we</c><00:07:04.990><c> need</c><00:07:05.110><c> the</c><00:07:05.200><c> first</c><00:07:05.410><c> and</c><00:07:05.620><c> last</c>

00:07:05.790 --> 00:07:05.800 align:start position:0%
then then we need the first and last
 

00:07:05.800 --> 00:07:08.310 align:start position:0%
then then we need the first and last
name<00:07:06.270><c> this</c><00:07:07.270><c> is</c><00:07:07.390><c> just</c><00:07:07.570><c> I'm</c><00:07:07.870><c> gonna</c><00:07:07.990><c> put</c><00:07:08.169><c> the</c>

00:07:08.310 --> 00:07:08.320 align:start position:0%
name this is just I'm gonna put the
 

00:07:08.320 --> 00:07:12.450 align:start position:0%
name this is just I'm gonna put the
creators<00:07:08.979><c> of</c><00:07:09.010><c> the</c><00:07:09.760><c> language</c><00:07:11.160><c> so</c><00:07:12.160><c> the</c><00:07:12.280><c> first</c>

00:07:12.450 --> 00:07:12.460 align:start position:0%
creators of the language so the first
 

00:07:12.460 --> 00:07:18.780 align:start position:0%
creators of the language so the first
guy<00:07:12.580><c> james</c><00:07:13.150><c> gosling</c><00:07:13.390><c> he</c><00:07:14.110><c> created</c><00:07:17.400><c> so</c><00:07:18.400><c> james</c>

00:07:18.780 --> 00:07:18.790 align:start position:0%
guy james gosling he created so james
 

00:07:18.790 --> 00:07:22.110 align:start position:0%
guy james gosling he created so james
for<00:07:18.970><c> the</c><00:07:19.030><c> first</c><00:07:19.210><c> name</c><00:07:20.610><c> Gosling</c><00:07:21.610><c> for</c><00:07:21.640><c> the</c><00:07:21.910><c> last</c>

00:07:22.110 --> 00:07:22.120 align:start position:0%
for the first name Gosling for the last
 

00:07:22.120 --> 00:07:24.630 align:start position:0%
for the first name Gosling for the last
name<00:07:22.650><c> so</c><00:07:23.650><c> you</c><00:07:23.740><c> can</c><00:07:23.770><c> kind</c><00:07:24.040><c> of</c><00:07:24.100><c> see</c><00:07:24.280><c> here</c><00:07:24.460><c> in</c><00:07:24.550><c> the</c>

00:07:24.630 --> 00:07:24.640 align:start position:0%
name so you can kind of see here in the
 

00:07:24.640 --> 00:07:27.390 align:start position:0%
name so you can kind of see here in the
code<00:07:24.880><c> the</c><00:07:25.690><c> first</c><00:07:25.960><c> table</c><00:07:26.200><c> headers</c><00:07:27.010><c> programming</c>

00:07:27.390 --> 00:07:27.400 align:start position:0%
code the first table headers programming
 

00:07:27.400 --> 00:07:30.120 align:start position:0%
code the first table headers programming
language<00:07:27.729><c> the</c><00:07:28.060><c> first</c><00:07:28.330><c> piece</c><00:07:28.870><c> of</c><00:07:28.900><c> data</c><00:07:29.080><c> I</c><00:07:29.650><c> have</c>

00:07:30.120 --> 00:07:30.130 align:start position:0%
language the first piece of data I have
 

00:07:30.130 --> 00:07:33.900 align:start position:0%
language the first piece of data I have
in<00:07:30.340><c> the</c><00:07:30.460><c> next</c><00:07:30.850><c> table</c><00:07:31.330><c> row</c><00:07:31.720><c> is</c><00:07:32.260><c> Java</c><00:07:32.669><c> then</c><00:07:33.669><c> his</c>

00:07:33.900 --> 00:07:33.910 align:start position:0%
in the next table row is Java then his
 

00:07:33.910 --> 00:07:35.400 align:start position:0%
in the next table row is Java then his
first<00:07:34.240><c> thing</c><00:07:34.479><c> even</c><00:07:34.600><c> in</c><00:07:34.750><c> last</c><00:07:34.990><c> names</c><00:07:35.200><c> that</c><00:07:35.290><c> i</c>

00:07:35.400 --> 00:07:35.410 align:start position:0%
first thing even in last names that i
 

00:07:35.410 --> 00:07:39.620 align:start position:0%
first thing even in last names that i
have<00:07:35.560><c> jeans</c><00:07:35.800><c> and</c><00:07:36.280><c> then</c><00:07:36.430><c> Gosling</c><00:07:36.880><c> alright</c><00:07:37.650><c> and</c>

00:07:39.620 --> 00:07:39.630 align:start position:0%
have jeans and then Gosling alright and
 

00:07:39.630 --> 00:07:42.270 align:start position:0%
have jeans and then Gosling alright and
let's<00:07:40.630><c> do</c><00:07:40.840><c> let's</c><00:07:41.380><c> go</c><00:07:41.470><c> ahead</c><00:07:41.560><c> and</c><00:07:41.740><c> just</c><00:07:42.130><c> add</c>

00:07:42.270 --> 00:07:42.280 align:start position:0%
let's do let's go ahead and just add
 

00:07:42.280 --> 00:07:44.279 align:start position:0%
let's do let's go ahead and just add
another<00:07:42.520><c> piece</c><00:07:42.640><c> of</c><00:07:42.820><c> data</c><00:07:42.940><c> so</c><00:07:43.570><c> another</c><00:07:43.810><c> table</c>

00:07:44.279 --> 00:07:44.289 align:start position:0%
another piece of data so another table
 

00:07:44.289 --> 00:07:47.500 align:start position:0%
another piece of data so another table
row<00:07:44.440><c> opening</c><00:07:45.099><c> and</c><00:07:45.220><c> closing</c><00:07:45.520><c> element</c>

00:07:47.500 --> 00:07:47.510 align:start position:0%
row opening and closing element
 

00:07:47.510 --> 00:07:51.070 align:start position:0%
row opening and closing element
this<00:07:48.020><c> we're</c><00:07:48.470><c> gonna</c><00:07:48.590><c> let's</c><00:07:48.950><c> do</c><00:07:49.720><c> C++</c><00:07:50.720><c> because</c><00:07:50.930><c> I</c>

00:07:51.070 --> 00:07:51.080 align:start position:0%
this we're gonna let's do C++ because I
 

00:07:51.080 --> 00:07:55.750 align:start position:0%
this we're gonna let's do C++ because I
know<00:07:51.620><c> his</c><00:07:51.890><c> name</c><00:07:52.160><c> on</c><00:07:52.580><c> my</c><00:07:52.910><c> head</c><00:07:54.190><c> so</c><00:07:55.190><c> for</c><00:07:55.640><c> his</c>

00:07:55.750 --> 00:07:55.760 align:start position:0%
know his name on my head so for his
 

00:07:55.760 --> 00:08:00.820 align:start position:0%
know his name on my head so for his
first<00:07:55.940><c> name</c><00:07:56.410><c> the</c><00:07:57.410><c> yarn</c><00:07:58.900><c> hmm</c><00:07:59.990><c> I'm</c><00:08:00.050><c> not</c><00:08:00.560><c> having</c>

00:08:00.820 --> 00:08:00.830 align:start position:0%
first name the yarn hmm I'm not having
 

00:08:00.830 --> 00:08:03.070 align:start position:0%
first name the yarn hmm I'm not having
the<00:08:01.300><c> appreciates</c><00:08:02.300><c> hi</c><00:08:02.390><c> Francis</c><00:08:02.720><c> first</c><00:08:02.900><c> name</c>

00:08:03.070 --> 00:08:03.080 align:start position:0%
the appreciates hi Francis first name
 

00:08:03.080 --> 00:08:07.830 align:start position:0%
the appreciates hi Francis first name
but<00:08:03.380><c> his</c><00:08:03.530><c> last</c><00:08:03.710><c> name</c><00:08:03.920><c> let</c><00:08:04.910><c> me</c><00:08:05.000><c> just</c><00:08:05.560><c> first</c><00:08:06.560><c> I</c>

00:08:07.830 --> 00:08:07.840 align:start position:0%
but his last name let me just first I
 

00:08:07.840 --> 00:08:11.140 align:start position:0%
but his last name let me just first I
think<00:08:08.840><c> it's</c><00:08:08.930><c> loose</c><00:08:09.440><c> though</c><00:08:09.650><c> or</c><00:08:10.150><c> something</c>

00:08:11.140 --> 00:08:11.150 align:start position:0%
think it's loose though or something
 

00:08:11.150 --> 00:08:12.610 align:start position:0%
think it's loose though or something
like<00:08:11.390><c> that</c><00:08:11.570><c> I'm</c><00:08:11.690><c> probably</c><00:08:11.900><c> really</c><00:08:12.260><c> butchering</c>

00:08:12.610 --> 00:08:12.620 align:start position:0%
like that I'm probably really butchering
 

00:08:12.620 --> 00:08:18.190 align:start position:0%
like that I'm probably really butchering
that<00:08:14.830><c> but</c><00:08:15.830><c> so</c><00:08:16.040><c> he's</c><00:08:16.220><c> this</c><00:08:16.610><c> guy</c><00:08:16.760><c> hey</c><00:08:17.390><c> Arnie</c><00:08:17.720><c> calm</c>

00:08:18.190 --> 00:08:18.200 align:start position:0%
that but so he's this guy hey Arnie calm
 

00:08:18.200 --> 00:08:22.950 align:start position:0%
that but so he's this guy hey Arnie calm
eyes<00:08:18.290><c> first</c><00:08:18.470><c> name</c><00:08:18.680><c> he</c><00:08:18.830><c> crazy</c><00:08:19.070><c> plus</c><00:08:19.400><c> plus</c><00:08:19.610><c> and</c>

00:08:22.950 --> 00:08:22.960 align:start position:0%
 
 

00:08:22.960 --> 00:08:26.800 align:start position:0%
 
let's<00:08:23.960><c> add</c><00:08:24.110><c> one</c><00:08:24.140><c> more</c><00:08:25.420><c> far</c><00:08:26.420><c> enough</c><00:08:26.510><c> I</c><00:08:26.630><c> had</c><00:08:26.780><c> to</c>

00:08:26.800 --> 00:08:26.810 align:start position:0%
let's add one more far enough I had to
 

00:08:26.810 --> 00:08:30.040 align:start position:0%
let's add one more far enough I had to
look<00:08:27.460><c> up</c><00:08:28.460><c> one</c><00:08:28.610><c> other</c><00:08:28.730><c> creator</c><00:08:29.150><c> real</c><00:08:29.330><c> quick</c><00:08:29.540><c> I</c>

00:08:30.040 --> 00:08:30.050 align:start position:0%
look up one other creator real quick I
 

00:08:30.050 --> 00:08:31.510 align:start position:0%
look up one other creator real quick I
couldn't<00:08:30.620><c> remember</c><00:08:30.710><c> a</c><00:08:30.890><c> news</c><00:08:31.100><c> first</c><00:08:31.340><c> name</c><00:08:31.460><c> was</c>

00:08:31.510 --> 00:08:31.520 align:start position:0%
couldn't remember a news first name was
 

00:08:31.520 --> 00:08:32.940 align:start position:0%
couldn't remember a news first name was
Dennis<00:08:31.730><c> I</c><00:08:32.150><c> did</c><00:08:32.360><c> remember</c><00:08:32.479><c> his</c><00:08:32.660><c> last</c><00:08:32.750><c> name</c>

00:08:32.940 --> 00:08:32.950 align:start position:0%
Dennis I did remember his last name
 

00:08:32.950 --> 00:08:40.480 align:start position:0%
Dennis I did remember his last name
first<00:08:33.950><c> see</c><00:08:35.020><c> so</c><00:08:36.610><c> let's</c><00:08:37.610><c> add</c><00:08:37.910><c> a</c><00:08:38.390><c> third</c><00:08:39.430><c> this</c><00:08:40.430><c> is</c>

00:08:40.480 --> 00:08:40.490 align:start position:0%
first see so let's add a third this is
 

00:08:40.490 --> 00:08:42.490 align:start position:0%
first see so let's add a third this is
actually<00:08:40.760><c> our</c><00:08:40.910><c> fourth</c><00:08:41.090><c> row</c><00:08:41.330><c> of</c><00:08:41.690><c> data</c><00:08:41.900><c> but</c>

00:08:42.490 --> 00:08:42.500 align:start position:0%
actually our fourth row of data but
 

00:08:42.500 --> 00:08:48.450 align:start position:0%
actually our fourth row of data but
earth<00:08:43.180><c> our</c><00:08:44.180><c> third</c><00:08:44.630><c> row</c><00:08:44.990><c> a</c><00:08:45.490><c> third</c><00:08:46.490><c> row</c><00:08:46.670><c> of</c><00:08:46.790><c> data</c>

00:08:48.450 --> 00:08:48.460 align:start position:0%
earth our third row a third row of data
 

00:08:48.460 --> 00:08:51.490 align:start position:0%
earth our third row a third row of data
that<00:08:49.460><c> we're</c><00:08:50.330><c> actually</c><00:08:50.450><c> looking</c><00:08:50.840><c> at</c><00:08:51.140><c> since</c><00:08:51.410><c> the</c>

00:08:51.490 --> 00:08:51.500 align:start position:0%
that we're actually looking at since the
 

00:08:51.500 --> 00:08:53.860 align:start position:0%
that we're actually looking at since the
first<00:08:51.740><c> row</c><00:08:52.070><c> is</c><00:08:52.250><c> just</c><00:08:52.280><c> the</c><00:08:52.520><c> table</c><00:08:52.820><c> headings</c><00:08:53.150><c> or</c>

00:08:53.860 --> 00:08:53.870 align:start position:0%
first row is just the table headings or
 

00:08:53.870 --> 00:09:00.390 align:start position:0%
first row is just the table headings or
the<00:08:54.110><c> column</c><00:08:54.440><c> headings</c><00:08:55.360><c> so</c><00:08:56.360><c> see</c><00:08:58.360><c> Dennis</c><00:08:59.360><c> and</c>

00:09:00.390 --> 00:09:00.400 align:start position:0%
the column headings so see Dennis and
 

00:09:00.400 --> 00:09:08.290 align:start position:0%
the column headings so see Dennis and
then<00:09:01.810><c> Richie</c><00:09:05.110><c> okay</c>

00:09:08.290 --> 00:09:08.300 align:start position:0%
 
 

00:09:08.300 --> 00:09:10.900 align:start position:0%
 
and<00:09:08.390><c> then</c><00:09:08.510><c> we're</c><00:09:08.630><c> done</c><00:09:08.860><c> all</c><00:09:09.860><c> right</c><00:09:09.920><c> so</c><00:09:10.790><c> we</c>

00:09:10.900 --> 00:09:10.910 align:start position:0%
and then we're done all right so we
 

00:09:10.910 --> 00:09:13.960 align:start position:0%
and then we're done all right so we
created<00:09:12.040><c> created</c><00:09:13.040><c> table</c><00:09:13.190><c> opening</c><00:09:13.880><c> and</c>

00:09:13.960 --> 00:09:13.970 align:start position:0%
created created table opening and
 

00:09:13.970 --> 00:09:16.990 align:start position:0%
created created table opening and
closing<00:09:14.240><c> tag</c><00:09:14.420><c> we've</c><00:09:15.320><c> created</c><00:09:15.830><c> the</c><00:09:16.580><c> first</c><00:09:16.790><c> row</c>

00:09:16.990 --> 00:09:17.000 align:start position:0%
closing tag we've created the first row
 

00:09:17.000 --> 00:09:19.450 align:start position:0%
closing tag we've created the first row
of<00:09:17.030><c> data</c><00:09:17.120><c> but</c><00:09:18.050><c> we</c><00:09:18.200><c> denoted</c><00:09:18.620><c> this</c><00:09:18.770><c> road</c><00:09:19.100><c> to</c><00:09:19.340><c> be</c>

00:09:19.450 --> 00:09:19.460 align:start position:0%
of data but we denoted this road to be
 

00:09:19.460 --> 00:09:22.420 align:start position:0%
of data but we denoted this road to be
the<00:09:19.580><c> table</c><00:09:19.730><c> headings</c><00:09:20.270><c> so</c><00:09:20.720><c> each</c><00:09:21.710><c> of</c><00:09:21.770><c> these</c><00:09:22.100><c> is</c>

00:09:22.420 --> 00:09:22.430 align:start position:0%
the table headings so each of these is
 

00:09:22.430 --> 00:09:26.770 align:start position:0%
the table headings so each of these is
going<00:09:22.670><c> to</c><00:09:22.820><c> represent</c><00:09:23.050><c> the</c><00:09:24.050><c> column</c><00:09:25.420><c> and</c><00:09:26.420><c> then</c><00:09:26.630><c> R</c>

00:09:26.770 --> 00:09:26.780 align:start position:0%
going to represent the column and then R
 

00:09:26.780 --> 00:09:32.080 align:start position:0%
going to represent the column and then R
so<00:09:27.640><c> the</c><00:09:28.640><c> next</c><00:09:28.850><c> table</c><00:09:29.690><c> row</c><00:09:30.310><c> we</c><00:09:31.310><c> have</c><00:09:31.400><c> Java</c><00:09:31.640><c> James</c>

00:09:32.080 --> 00:09:32.090 align:start position:0%
so the next table row we have Java James
 

00:09:32.090 --> 00:09:34.900 align:start position:0%
so the next table row we have Java James
and<00:09:32.330><c> Gosling</c><00:09:32.750><c> which</c><00:09:33.730><c> represents</c><00:09:34.730><c> the</c>

00:09:34.900 --> 00:09:34.910 align:start position:0%
and Gosling which represents the
 

00:09:34.910 --> 00:09:36.220 align:start position:0%
and Gosling which represents the
programming<00:09:35.300><c> language</c><00:09:35.600><c> first-name</c><00:09:36.110><c> and</c>

00:09:36.220 --> 00:09:36.230 align:start position:0%
programming language first-name and
 

00:09:36.230 --> 00:09:38.020 align:start position:0%
programming language first-name and
lastname<00:09:36.290><c> and</c><00:09:37.160><c> we</c><00:09:37.340><c> had</c><00:09:37.430><c> the</c><00:09:37.490><c> same</c><00:09:37.700><c> for</c><00:09:37.940><c> the</c>

00:09:38.020 --> 00:09:38.030 align:start position:0%
lastname and we had the same for the
 

00:09:38.030 --> 00:09:41.820 align:start position:0%
lastname and we had the same for the
next<00:09:38.330><c> two</c><00:09:38.710><c> so</c><00:09:39.710><c> go</c><00:09:40.070><c> ahead</c><00:09:40.280><c> and</c><00:09:40.460><c> say</c><00:09:41.420><c> it</c><00:09:41.540><c> as</c>

00:09:41.820 --> 00:09:41.830 align:start position:0%
next two so go ahead and say it as
 

00:09:41.830 --> 00:09:46.090 align:start position:0%
next two so go ahead and say it as
desktop<00:09:42.830><c> which</c><00:09:43.250><c> is</c><00:09:43.280><c> going</c><00:09:43.580><c> table</c><00:09:44.440><c> demo</c><00:09:45.440><c> time</c>

00:09:46.090 --> 00:09:46.100 align:start position:0%
desktop which is going table demo time
 

00:09:46.100 --> 00:09:54.780 align:start position:0%
desktop which is going table demo time
HTML<00:09:48.370><c> minimize</c><00:09:49.370><c> this</c><00:09:51.190><c> let's</c><00:09:52.190><c> close</c><00:09:52.910><c> that</c><00:09:52.940><c> and</c>

00:09:54.780 --> 00:09:54.790 align:start position:0%
HTML minimize this let's close that and
 

00:09:54.790 --> 00:09:59.560 align:start position:0%
HTML minimize this let's close that and
if<00:09:55.790><c> we</c><00:09:55.910><c> open</c><00:09:56.090><c> this</c><00:09:56.830><c> there</c><00:09:57.830><c> we</c><00:09:57.920><c> go</c><00:09:58.360><c> so</c><00:09:59.360><c> you</c><00:09:59.450><c> can</c>

00:09:59.560 --> 00:09:59.570 align:start position:0%
if we open this there we go so you can
 

00:09:59.570 --> 00:10:06.350 align:start position:0%
if we open this there we go so you can
see<00:10:00.790><c> in</c><00:10:01.790><c> this</c><00:10:01.910><c> table</c>

00:10:06.350 --> 00:10:06.360 align:start position:0%
 
 

00:10:06.360 --> 00:10:07.970 align:start position:0%
 
yeah<00:10:06.540><c> so</c><00:10:06.779><c> you</c><00:10:06.869><c> can</c><00:10:06.959><c> see</c><00:10:07.049><c> this</c><00:10:07.170><c> table</c><00:10:07.319><c> we</c><00:10:07.829><c> have</c>

00:10:07.970 --> 00:10:07.980 align:start position:0%
yeah so you can see this table we have
 

00:10:07.980 --> 00:10:09.319 align:start position:0%
yeah so you can see this table we have
the<00:10:08.129><c> table</c><00:10:08.399><c> headings</c><00:10:08.699><c> which</c><00:10:08.819><c> again</c><00:10:09.059><c> are</c><00:10:09.119><c> in</c>

00:10:09.319 --> 00:10:09.329 align:start position:0%
the table headings which again are in
 

00:10:09.329 --> 00:10:11.329 align:start position:0%
the table headings which again are in
bold<00:10:09.569><c> programming</c><00:10:10.499><c> language</c><00:10:10.799><c> first</c><00:10:11.069><c> name</c><00:10:11.249><c> and</c>

00:10:11.329 --> 00:10:11.339 align:start position:0%
bold programming language first name and
 

00:10:11.339 --> 00:10:12.679 align:start position:0%
bold programming language first name and
last<00:10:11.459><c> name</c><00:10:11.610><c> and</c><00:10:11.879><c> then</c><00:10:11.939><c> under</c><00:10:12.209><c> that</c><00:10:12.299><c> we</c><00:10:12.480><c> have</c>

00:10:12.679 --> 00:10:12.689 align:start position:0%
last name and then under that we have
 

00:10:12.689 --> 00:10:15.199 align:start position:0%
last name and then under that we have
all<00:10:12.989><c> three</c><00:10:13.410><c> of</c><00:10:13.649><c> our</c><00:10:14.009><c> actual</c><00:10:14.459><c> rows</c><00:10:14.819><c> of</c><00:10:15.059><c> data</c>

00:10:15.199 --> 00:10:15.209 align:start position:0%
all three of our actual rows of data
 

00:10:15.209 --> 00:10:17.900 align:start position:0%
all three of our actual rows of data
that<00:10:15.449><c> we</c><00:10:15.540><c> care</c><00:10:15.720><c> about</c><00:10:16.279><c> all</c><00:10:17.279><c> right</c><00:10:17.339><c> so</c><00:10:17.699><c> let's</c>

00:10:17.900 --> 00:10:17.910 align:start position:0%
that we care about all right so let's
 

00:10:17.910 --> 00:10:19.340 align:start position:0%
that we care about all right so let's
just<00:10:17.939><c> go</c><00:10:18.149><c> over</c><00:10:18.269><c> a</c><00:10:18.389><c> quick</c><00:10:18.600><c> summary</c><00:10:18.720><c> of</c><00:10:18.929><c> what</c><00:10:19.110><c> we</c>

00:10:19.340 --> 00:10:19.350 align:start position:0%
just go over a quick summary of what we
 

00:10:19.350 --> 00:10:23.869 align:start position:0%
just go over a quick summary of what we
just<00:10:19.379><c> what</c><00:10:20.009><c> we</c><00:10:20.069><c> just</c><00:10:20.129><c> did</c><00:10:20.929><c> so</c><00:10:22.129><c> you</c><00:10:23.129><c> were</c>

00:10:23.869 --> 00:10:23.879 align:start position:0%
just what we just did so you were
 

00:10:23.879 --> 00:10:25.609 align:start position:0%
just what we just did so you were
introduced<00:10:24.179><c> to</c><00:10:24.269><c> a</c><00:10:24.360><c> new</c><00:10:24.540><c> element</c><00:10:24.899><c> a</c><00:10:25.139><c> table</c>

00:10:25.609 --> 00:10:25.619 align:start position:0%
introduced to a new element a table
 

00:10:25.619 --> 00:10:28.100 align:start position:0%
introduced to a new element a table
element<00:10:25.920><c> and</c><00:10:26.100><c> this</c><00:10:26.670><c> is</c><00:10:26.850><c> how</c><00:10:27.480><c> this</c><00:10:27.839><c> is</c><00:10:27.899><c> how</c><00:10:28.049><c> you</c>

00:10:28.100 --> 00:10:28.110 align:start position:0%
element and this is how this is how you
 

00:10:28.110 --> 00:10:31.340 align:start position:0%
element and this is how this is how you
begin<00:10:28.319><c> to</c><00:10:28.589><c> add</c><00:10:28.709><c> a</c><00:10:29.249><c> table</c><00:10:29.879><c> to</c><00:10:29.999><c> a</c><00:10:30.029><c> web</c><00:10:30.179><c> page</c><00:10:30.350><c> the</c>

00:10:31.340 --> 00:10:31.350 align:start position:0%
begin to add a table to a web page the
 

00:10:31.350 --> 00:10:36.049 align:start position:0%
begin to add a table to a web page the
table<00:10:31.679><c> is</c><00:10:31.920><c> drawn</c><00:10:32.160><c> row</c><00:10:32.970><c> by</c><00:10:33.209><c> row</c><00:10:33.509><c> using</c><00:10:34.110><c> the</c><00:10:35.100><c> TR</c>

00:10:36.049 --> 00:10:36.059 align:start position:0%
table is drawn row by row using the TR
 

00:10:36.059 --> 00:10:39.439 align:start position:0%
table is drawn row by row using the TR
element<00:10:36.569><c> or</c><00:10:36.720><c> table</c><00:10:37.049><c> row</c><00:10:37.199><c> element</c><00:10:37.920><c> a</c><00:10:38.449><c> table</c>

00:10:39.439 --> 00:10:39.449 align:start position:0%
element or table row element a table
 

00:10:39.449 --> 00:10:42.619 align:start position:0%
element or table row element a table
cell<00:10:39.689><c> can</c><00:10:40.019><c> be</c><00:10:40.110><c> represented</c><00:10:40.529><c> by</c><00:10:40.679><c> a</c><00:10:41.069><c> th</c><00:10:41.999><c> or</c><00:10:42.269><c> the</c>

00:10:42.619 --> 00:10:42.629 align:start position:0%
cell can be represented by a th or the
 

00:10:42.629 --> 00:10:45.559 align:start position:0%
cell can be represented by a th or the
TD<00:10:43.019><c> elements</c><00:10:43.470><c> the</c><00:10:44.040><c> TD</c><00:10:44.369><c> the</c><00:10:44.699><c> th</c><00:10:45.029><c> stands</c><00:10:45.389><c> for</c>

00:10:45.559 --> 00:10:45.569 align:start position:0%
TD elements the TD the th stands for
 

00:10:45.569 --> 00:10:47.449 align:start position:0%
TD elements the TD the th stands for
table<00:10:45.689><c> head</c><00:10:46.019><c> and</c><00:10:46.319><c> that's</c><00:10:46.829><c> typically</c><00:10:47.189><c> what</c><00:10:47.369><c> you</c>

00:10:47.449 --> 00:10:47.459 align:start position:0%
table head and that's typically what you
 

00:10:47.459 --> 00:10:49.400 align:start position:0%
table head and that's typically what you
would<00:10:47.579><c> use</c><00:10:47.730><c> for</c><00:10:47.759><c> the</c><00:10:48.029><c> first</c><00:10:48.389><c> row</c><00:10:48.660><c> if</c><00:10:48.899><c> you</c><00:10:49.230><c> want</c>

00:10:49.400 --> 00:10:49.410 align:start position:0%
would use for the first row if you want
 

00:10:49.410 --> 00:10:54.499 align:start position:0%
would use for the first row if you want
them<00:10:50.420><c> because</c><00:10:51.420><c> they</c><00:10:51.949><c> represent</c><00:10:52.949><c> the</c><00:10:53.279><c> names</c><00:10:54.179><c> of</c>

00:10:54.499 --> 00:10:54.509 align:start position:0%
them because they represent the names of
 

00:10:54.509 --> 00:10:56.660 align:start position:0%
them because they represent the names of
the<00:10:54.629><c> columns</c><00:10:54.839><c> if</c><00:10:55.319><c> you'd</c><00:10:55.559><c> like</c><00:10:55.739><c> that</c><00:10:55.769><c> and</c><00:10:56.189><c> then</c>

00:10:56.660 --> 00:10:56.670 align:start position:0%
the columns if you'd like that and then
 

00:10:56.670 --> 00:10:59.210 align:start position:0%
the columns if you'd like that and then
if<00:10:56.790><c> you</c><00:10:57.420><c> have</c><00:10:57.509><c> the</c><00:10:57.629><c> TV</c><00:10:57.809><c> that's</c><00:10:58.799><c> just</c><00:10:59.009><c> for</c>

00:10:59.210 --> 00:10:59.220 align:start position:0%
if you have the TV that's just for
 

00:10:59.220 --> 00:11:02.059 align:start position:0%
if you have the TV that's just for
standard<00:10:59.970><c> data</c><00:11:00.239><c> that</c><00:11:00.509><c> you</c><00:11:00.660><c> want</c><00:11:00.749><c> to</c><00:11:00.869><c> have</c><00:11:01.079><c> in</c>

00:11:02.059 --> 00:11:02.069 align:start position:0%
standard data that you want to have in
 

00:11:02.069 --> 00:11:04.429 align:start position:0%
standard data that you want to have in
your<00:11:02.129><c> table</c><00:11:02.660><c> so</c><00:11:03.660><c> I</c><00:11:03.929><c> hope</c><00:11:04.049><c> you</c><00:11:04.139><c> guys</c><00:11:04.230><c> learned</c>

00:11:04.429 --> 00:11:04.439 align:start position:0%
your table so I hope you guys learned
 

00:11:04.439 --> 00:11:07.730 align:start position:0%
your table so I hope you guys learned
something<00:11:04.529><c> and</c><00:11:04.889><c> I'll</c><00:11:05.279><c> see</c><00:11:05.399><c> you</c><00:11:05.489><c> next</c><00:11:05.519><c> time</c>

