WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:02.629 align:start position:0%
 
in<00:00:00.240><c> this</c><00:00:00.440><c> video</c><00:00:01.280><c> I</c><00:00:01.360><c> want</c><00:00:01.520><c> to</c><00:00:01.880><c> go</c><00:00:02.080><c> over</c><00:00:02.360><c> some</c>

00:00:02.629 --> 00:00:02.639 align:start position:0%
in this video I want to go over some
 

00:00:02.639 --> 00:00:05.230 align:start position:0%
in this video I want to go over some
exercises<00:00:03.280><c> you</c><00:00:03.360><c> can</c><00:00:03.560><c> do</c><00:00:04.440><c> to</c><00:00:04.600><c> further</c><00:00:05.000><c> your</c>

00:00:05.230 --> 00:00:05.240 align:start position:0%
exercises you can do to further your
 

00:00:05.240 --> 00:00:07.550 align:start position:0%
exercises you can do to further your
intuition<00:00:06.160><c> about</c><00:00:06.440><c> what</c><00:00:06.560><c> you</c><00:00:06.680><c> can</c><00:00:06.839><c> do</c><00:00:07.040><c> with</c><00:00:07.240><c> web</c>

00:00:07.550 --> 00:00:07.560 align:start position:0%
intuition about what you can do with web
 

00:00:07.560 --> 00:00:08.310 align:start position:0%
intuition about what you can do with web
hook

00:00:08.310 --> 00:00:08.320 align:start position:0%
hook
 

00:00:08.320 --> 00:00:10.990 align:start position:0%
hook
automation<00:00:09.320><c> so</c><00:00:09.480><c> if</c><00:00:09.599><c> you</c><00:00:09.800><c> recall</c><00:00:10.400><c> this</c><00:00:10.519><c> is</c><00:00:10.719><c> the</c>

00:00:10.990 --> 00:00:11.000 align:start position:0%
automation so if you recall this is the
 

00:00:11.000 --> 00:00:14.789 align:start position:0%
automation so if you recall this is the
code<00:00:12.000><c> that</c><00:00:12.160><c> runs</c><00:00:12.559><c> that</c><00:00:12.839><c> modal</c><00:00:13.839><c> web</c><00:00:14.120><c> server</c>

00:00:14.789 --> 00:00:14.799 align:start position:0%
code that runs that modal web server
 

00:00:14.799 --> 00:00:17.109 align:start position:0%
code that runs that modal web server
that<00:00:15.120><c> receives</c><00:00:15.320><c> the</c><00:00:15.480><c> web</c><00:00:15.679><c> hook</c><00:00:16.600><c> and</c><00:00:16.760><c> in</c><00:00:16.960><c> this</c>

00:00:17.109 --> 00:00:17.119 align:start position:0%
that receives the web hook and in this
 

00:00:17.119 --> 00:00:19.310 align:start position:0%
that receives the web hook and in this
web<00:00:17.359><c> server</c><00:00:17.800><c> we're</c><00:00:18.000><c> not</c><00:00:18.160><c> really</c><00:00:18.359><c> doing</c><00:00:18.720><c> much</c>

00:00:19.310 --> 00:00:19.320 align:start position:0%
web server we're not really doing much
 

00:00:19.320 --> 00:00:21.029 align:start position:0%
web server we're not really doing much
we're<00:00:19.520><c> just</c><00:00:19.720><c> printing</c><00:00:20.160><c> the</c>

00:00:21.029 --> 00:00:21.039 align:start position:0%
we're just printing the
 

00:00:21.039 --> 00:00:23.670 align:start position:0%
we're just printing the
event<00:00:22.039><c> and</c><00:00:22.600><c> we're</c><00:00:22.800><c> just</c><00:00:22.960><c> returning</c><00:00:23.480><c> this</c>

00:00:23.670 --> 00:00:23.680 align:start position:0%
event and we're just returning this
 

00:00:23.680 --> 00:00:25.550 align:start position:0%
event and we're just returning this
message<00:00:24.039><c> to</c><00:00:24.199><c> the</c><00:00:24.320><c> client</c><00:00:24.800><c> so</c><00:00:25.000><c> nothing</c><00:00:25.320><c> really</c>

00:00:25.550 --> 00:00:25.560 align:start position:0%
message to the client so nothing really
 

00:00:25.560 --> 00:00:28.429 align:start position:0%
message to the client so nothing really
is<00:00:25.760><c> happening</c><00:00:26.480><c> here</c><00:00:27.480><c> and</c><00:00:27.880><c> and</c><00:00:28.119><c> what</c><00:00:28.240><c> we</c><00:00:28.359><c> want</c>

00:00:28.429 --> 00:00:28.439 align:start position:0%
is happening here and and what we want
 

00:00:28.439 --> 00:00:30.189 align:start position:0%
is happening here and and what we want
to<00:00:28.640><c> do</c><00:00:28.920><c> is</c><00:00:29.039><c> we</c><00:00:29.119><c> want</c><00:00:29.240><c> to</c><00:00:29.359><c> do</c><00:00:29.519><c> more</c><00:00:29.800><c> than</c><00:00:30.080><c> just</c>

00:00:30.189 --> 00:00:30.199 align:start position:0%
to do is we want to do more than just
 

00:00:30.199 --> 00:00:32.269 align:start position:0%
to do is we want to do more than just
print<00:00:30.480><c> the</c><00:00:30.640><c> event</c><00:00:31.400><c> we</c><00:00:31.519><c> want</c><00:00:31.640><c> to</c><00:00:31.880><c> get</c><00:00:32.040><c> this</c>

00:00:32.269 --> 00:00:32.279 align:start position:0%
print the event we want to get this
 

00:00:32.279 --> 00:00:34.750 align:start position:0%
print the event we want to get this
information<00:00:32.880><c> out</c><00:00:33.000><c> of</c><00:00:33.200><c> the</c><00:00:33.399><c> event</c><00:00:34.120><c> and</c><00:00:34.320><c> do</c>

00:00:34.750 --> 00:00:34.760 align:start position:0%
information out of the event and do
 

00:00:34.760 --> 00:00:39.350 align:start position:0%
information out of the event and do
something<00:00:35.760><c> so</c><00:00:35.960><c> here</c><00:00:36.079><c> are</c><00:00:36.280><c> some</c>

00:00:39.350 --> 00:00:39.360 align:start position:0%
 
 

00:00:39.360 --> 00:00:43.869 align:start position:0%
 
ideas<00:00:40.360><c> one</c><00:00:40.719><c> is</c><00:00:41.200><c> download</c><00:00:41.680><c> the</c><00:00:42.239><c> model</c><00:00:43.239><c> use</c><00:00:43.719><c> the</c>

00:00:43.869 --> 00:00:43.879 align:start position:0%
ideas one is download the model use the
 

00:00:43.879 --> 00:00:46.310 align:start position:0%
ideas one is download the model use the
weights<00:00:44.120><c> and</c><00:00:44.280><c> biases</c><00:00:44.760><c> python</c><00:00:45.160><c> client</c><00:00:46.079><c> to</c>

00:00:46.310 --> 00:00:46.320 align:start position:0%
weights and biases python client to
 

00:00:46.320 --> 00:00:48.310 align:start position:0%
weights and biases python client to
download<00:00:46.760><c> the</c><00:00:46.879><c> model</c><00:00:47.399><c> into</c><00:00:47.680><c> the</c><00:00:47.840><c> web</c><00:00:48.039><c> server</c>

00:00:48.310 --> 00:00:48.320 align:start position:0%
download the model into the web server
 

00:00:48.320 --> 00:00:49.750 align:start position:0%
download the model into the web server
you<00:00:48.399><c> don't</c><00:00:48.520><c> have</c><00:00:48.600><c> to</c><00:00:48.719><c> do</c><00:00:48.879><c> anything</c><00:00:49.160><c> else</c><00:00:49.559><c> just</c>

00:00:49.750 --> 00:00:49.760 align:start position:0%
you don't have to do anything else just
 

00:00:49.760 --> 00:00:52.430 align:start position:0%
you don't have to do anything else just
download<00:00:50.160><c> the</c><00:00:50.280><c> model</c><00:00:51.000><c> make</c><00:00:51.160><c> sure</c><00:00:51.760><c> familiarize</c>

00:00:52.430 --> 00:00:52.440 align:start position:0%
download the model make sure familiarize
 

00:00:52.440 --> 00:00:53.790 align:start position:0%
download the model make sure familiarize
yourself<00:00:52.760><c> with</c><00:00:52.879><c> the</c><00:00:52.960><c> weights</c><00:00:53.160><c> and</c><00:00:53.320><c> biases</c>

00:00:53.790 --> 00:00:53.800 align:start position:0%
yourself with the weights and biases
 

00:00:53.800 --> 00:00:55.670 align:start position:0%
yourself with the weights and biases
python<00:00:54.199><c> client</c><00:00:54.719><c> which</c><00:00:55.120><c> which</c><00:00:55.280><c> we've</c><00:00:55.440><c> already</c>

00:00:55.670 --> 00:00:55.680 align:start position:0%
python client which which we've already
 

00:00:55.680 --> 00:00:58.549 align:start position:0%
python client which which we've already
went<00:00:55.920><c> over</c><00:00:56.239><c> in</c><00:00:56.399><c> the</c><00:00:56.719><c> cicd</c><00:00:57.320><c> for</c><00:00:57.480><c> ML</c>

00:00:58.549 --> 00:00:58.559 align:start position:0%
went over in the cicd for ML
 

00:00:58.559 --> 00:01:02.509 align:start position:0%
went over in the cicd for ML
course<00:00:59.559><c> uh</c><00:00:59.640><c> to</c><00:01:00.000><c> retrieve</c><00:01:00.320><c> the</c><00:01:00.440><c> model</c><00:01:01.359><c> and</c><00:01:02.280><c> do</c>

00:01:02.509 --> 00:01:02.519 align:start position:0%
course uh to retrieve the model and do
 

00:01:02.519 --> 00:01:04.509 align:start position:0%
course uh to retrieve the model and do
something<00:01:02.879><c> with</c><00:01:03.039><c> that</c><00:01:03.199><c> model</c><00:01:04.000><c> very</c><00:01:04.199><c> simple</c>

00:01:04.509 --> 00:01:04.519 align:start position:0%
something with that model very simple
 

00:01:04.519 --> 00:01:07.429 align:start position:0%
something with that model very simple
thing<00:01:04.680><c> you</c><00:01:04.760><c> can</c><00:01:04.920><c> do</c><00:01:05.159><c> is</c><00:01:05.519><c> print</c><00:01:05.760><c> the</c><00:01:05.920><c> number</c><00:01:06.159><c> of</c>

00:01:07.429 --> 00:01:07.439 align:start position:0%
thing you can do is print the number of
 

00:01:07.439 --> 00:01:10.030 align:start position:0%
thing you can do is print the number of
parameters<00:01:08.439><c> but</c><00:01:08.640><c> also</c><00:01:09.080><c> more</c><00:01:09.320><c> realistically</c>

00:01:10.030 --> 00:01:10.040 align:start position:0%
parameters but also more realistically
 

00:01:10.040 --> 00:01:12.630 align:start position:0%
parameters but also more realistically
score<00:01:10.479><c> the</c><00:01:10.600><c> model</c><00:01:11.040><c> against</c><00:01:11.360><c> a</c><00:01:11.479><c> test</c>

00:01:12.630 --> 00:01:12.640 align:start position:0%
score the model against a test
 

00:01:12.640 --> 00:01:15.469 align:start position:0%
score the model against a test
set<00:01:13.640><c> and</c><00:01:13.759><c> then</c><00:01:13.960><c> finally</c><00:01:14.600><c> do</c><00:01:14.840><c> something</c><00:01:15.200><c> fun</c>

00:01:15.469 --> 00:01:15.479 align:start position:0%
set and then finally do something fun
 

00:01:15.479 --> 00:01:17.270 align:start position:0%
set and then finally do something fun
with<00:01:15.600><c> the</c><00:01:15.759><c> web</c><00:01:16.040><c> hook</c><00:01:16.439><c> for</c>

00:01:17.270 --> 00:01:17.280 align:start position:0%
with the web hook for
 

00:01:17.280 --> 00:01:20.390 align:start position:0%
with the web hook for
example<00:01:18.280><c> uh</c><00:01:18.360><c> send</c><00:01:18.640><c> a</c><00:01:18.759><c> slack</c><00:01:19.119><c> message</c><00:01:19.720><c> email</c><00:01:20.119><c> or</c>

00:01:20.390 --> 00:01:20.400 align:start position:0%
example uh send a slack message email or
 

00:01:20.400 --> 00:01:24.270 align:start position:0%
example uh send a slack message email or
text<00:01:21.159><c> make</c><00:01:21.400><c> the</c><00:01:21.680><c> web</c><00:01:21.960><c> hook</c><00:01:22.600><c> do</c><00:01:22.840><c> something</c><00:01:23.280><c> fun</c>

00:01:24.270 --> 00:01:24.280 align:start position:0%
text make the web hook do something fun
 

00:01:24.280 --> 00:01:25.749 align:start position:0%
text make the web hook do something fun
and<00:01:24.439><c> let</c><00:01:24.560><c> us</c><00:01:24.720><c> know</c><00:01:24.920><c> what</c><00:01:25.040><c> you</c><00:01:25.159><c> do</c><00:01:25.360><c> with</c><00:01:25.479><c> it</c><00:01:25.640><c> and</c>

00:01:25.749 --> 00:01:25.759 align:start position:0%
and let us know what you do with it and
 

00:01:25.759 --> 00:01:28.390 align:start position:0%
and let us know what you do with it and
share<00:01:26.040><c> your</c><00:01:26.479><c> code</c><00:01:27.479><c> it</c><00:01:27.640><c> will</c><00:01:27.880><c> it</c><00:01:27.960><c> will</c><00:01:28.159><c> make</c><00:01:28.280><c> it</c>

00:01:28.390 --> 00:01:28.400 align:start position:0%
share your code it will it will make it
 

00:01:28.400 --> 00:01:29.910 align:start position:0%
share your code it will it will make it
a<00:01:28.520><c> lot</c><00:01:28.680><c> more</c><00:01:28.880><c> fun</c><00:01:29.119><c> for</c><00:01:29.280><c> the</c><00:01:29.360><c> participants</c><00:01:29.799><c> of</c>

00:01:29.910 --> 00:01:29.920 align:start position:0%
a lot more fun for the participants of
 

00:01:29.920 --> 00:01:32.710 align:start position:0%
a lot more fun for the participants of
of<00:01:30.079><c> this</c><00:01:30.240><c> class</c><00:01:30.840><c> if</c><00:01:30.960><c> you</c><00:01:31.240><c> share</c><00:01:32.240><c> creative</c>

00:01:32.710 --> 00:01:32.720 align:start position:0%
of this class if you share creative
 

00:01:32.720 --> 00:01:33.990 align:start position:0%
of this class if you share creative
things<00:01:32.960><c> that</c><00:01:33.079><c> you</c><00:01:33.200><c> end</c><00:01:33.360><c> up</c><00:01:33.520><c> doing</c><00:01:33.759><c> with</c><00:01:33.840><c> your</c>

00:01:33.990 --> 00:01:34.000 align:start position:0%
things that you end up doing with your
 

00:01:34.000 --> 00:01:37.240 align:start position:0%
things that you end up doing with your
web<00:01:34.240><c> hook</c>

