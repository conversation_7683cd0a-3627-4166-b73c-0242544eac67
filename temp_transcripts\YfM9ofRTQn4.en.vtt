WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.060 align:start position:0%
 
welcome<00:00:00.359><c> back</c><00:00:00.570><c> to</c><00:00:00.810><c> the</c><00:00:00.930><c> intercom</c><00:00:01.380><c> tutorial</c>

00:00:02.060 --> 00:00:02.070 align:start position:0%
welcome back to the intercom tutorial
 

00:00:02.070 --> 00:00:04.280 align:start position:0%
welcome back to the intercom tutorial
series<00:00:02.280><c> this</c><00:00:02.639><c> is</c><00:00:02.820><c> part</c><00:00:03.090><c> 7</c><00:00:03.120><c> in</c><00:00:03.689><c> this</c><00:00:03.840><c> part</c><00:00:04.110><c> we're</c>

00:00:04.280 --> 00:00:04.290 align:start position:0%
series this is part 7 in this part we're
 

00:00:04.290 --> 00:00:08.270 align:start position:0%
series this is part 7 in this part we're
going<00:00:04.410><c> to</c><00:00:04.470><c> talk</c><00:00:04.560><c> about</c><00:00:04.859><c> adding</c><00:00:05.390><c> custom</c><00:00:07.280><c> user</c>

00:00:08.270 --> 00:00:08.280 align:start position:0%
going to talk about adding custom user
 

00:00:08.280 --> 00:00:12.049 align:start position:0%
going to talk about adding custom user
data<00:00:08.820><c> attributes</c><00:00:09.570><c> to</c><00:00:10.080><c> your</c><00:00:10.349><c> users</c><00:00:10.830><c> and</c><00:00:11.130><c> here</c>

00:00:12.049 --> 00:00:12.059 align:start position:0%
data attributes to your users and here
 

00:00:12.059 --> 00:00:14.240 align:start position:0%
data attributes to your users and here
is<00:00:12.300><c> the</c><00:00:12.929><c> syntax</c><00:00:13.200><c> for</c><00:00:13.620><c> how</c><00:00:13.710><c> we're</c><00:00:13.920><c> gonna</c><00:00:14.040><c> go</c>

00:00:14.240 --> 00:00:14.250 align:start position:0%
is the syntax for how we're gonna go
 

00:00:14.250 --> 00:00:15.799 align:start position:0%
is the syntax for how we're gonna go
about<00:00:14.280><c> it</c><00:00:14.730><c> we're</c><00:00:14.910><c> gonna</c><00:00:15.000><c> first</c><00:00:15.330><c> go</c><00:00:15.509><c> to</c><00:00:15.540><c> our</c>

00:00:15.799 --> 00:00:15.809 align:start position:0%
about it we're gonna first go to our
 

00:00:15.809 --> 00:00:19.340 align:start position:0%
about it we're gonna first go to our
setup<00:00:16.170><c> guide</c><00:00:16.470><c> over</c><00:00:16.830><c> here</c><00:00:17.100><c> and</c><00:00:17.660><c> we</c><00:00:18.660><c> want</c><00:00:18.869><c> to</c><00:00:19.109><c> go</c>

00:00:19.340 --> 00:00:19.350 align:start position:0%
setup guide over here and we want to go
 

00:00:19.350 --> 00:00:24.310 align:start position:0%
setup guide over here and we want to go
to<00:00:19.520><c> get</c><00:00:20.520><c> using</c><00:00:20.939><c> intercom</c><00:00:21.510><c> trying</c><00:00:22.230><c> to</c><00:00:22.380><c> see</c><00:00:22.740><c> here</c>

00:00:24.310 --> 00:00:24.320 align:start position:0%
to get using intercom trying to see here
 

00:00:24.320 --> 00:00:27.009 align:start position:0%
to get using intercom trying to see here
two<00:00:25.320><c> teammates</c>

00:00:27.009 --> 00:00:27.019 align:start position:0%
two teammates
 

00:00:27.019 --> 00:00:32.720 align:start position:0%
two teammates
send<00:00:28.019><c> an</c><00:00:28.170><c> audio</c><00:00:28.289><c> message</c><00:00:30.980><c> so</c><00:00:31.980><c> you</c><00:00:32.189><c> click</c><00:00:32.520><c> on</c>

00:00:32.720 --> 00:00:32.730 align:start position:0%
send an audio message so you click on
 

00:00:32.730 --> 00:00:38.049 align:start position:0%
send an audio message so you click on
this<00:00:33.180><c> thing</c><00:00:33.480><c> here</c><00:00:33.719><c> which</c><00:00:33.899><c> is</c><00:00:34.079><c> your</c><00:00:34.579><c> user</c>

00:00:38.049 --> 00:00:38.059 align:start position:0%
 
 

00:00:38.059 --> 00:00:41.660 align:start position:0%
 
settings<00:00:39.059><c> you</c><00:00:39.899><c> click</c><00:00:40.050><c> on</c><00:00:40.290><c> your</c><00:00:40.530><c> avatar</c><00:00:41.070><c> and</c>

00:00:41.660 --> 00:00:41.670 align:start position:0%
settings you click on your avatar and
 

00:00:41.670 --> 00:00:46.910 align:start position:0%
settings you click on your avatar and
then<00:00:41.820><c> you</c><00:00:41.940><c> click</c><00:00:42.149><c> Settings</c><00:00:44.180><c> app</c><00:00:45.180><c> settings</c><00:00:45.920><c> app</c>

00:00:46.910 --> 00:00:46.920 align:start position:0%
then you click Settings app settings app
 

00:00:46.920 --> 00:00:50.750 align:start position:0%
then you click Settings app settings app
settings<00:00:48.770><c> messenger</c><00:00:49.770><c> settings</c><00:00:50.219><c> people's</c>

00:00:50.750 --> 00:00:50.760 align:start position:0%
settings messenger settings people's
 

00:00:50.760 --> 00:00:55.490 align:start position:0%
settings messenger settings people's
segments<00:00:52.219><c> people</c><00:00:53.219><c> data</c><00:00:54.110><c> I'm</c><00:00:55.110><c> gonna</c><00:00:55.260><c> go</c><00:00:55.440><c> to</c>

00:00:55.490 --> 00:00:55.500 align:start position:0%
segments people data I'm gonna go to
 

00:00:55.500 --> 00:00:58.970 align:start position:0%
segments people data I'm gonna go to
people<00:00:55.800><c> data</c><00:00:56.809><c> we're</c><00:00:57.809><c> going</c><00:00:58.020><c> to</c><00:00:58.230><c> filter</c><00:00:58.710><c> back</c>

00:00:58.970 --> 00:00:58.980 align:start position:0%
people data we're going to filter back
 

00:00:58.980 --> 00:01:01.610 align:start position:0%
people data we're going to filter back
we're<00:00:59.160><c> gonna</c><00:00:59.250><c> create</c><00:00:59.579><c> a</c><00:00:59.670><c> new</c><00:00:59.910><c> data</c><00:01:00.320><c> we</c><00:01:01.320><c> wanna</c>

00:01:01.610 --> 00:01:01.620 align:start position:0%
we're gonna create a new data we wanna
 

00:01:01.620 --> 00:01:03.290 align:start position:0%
we're gonna create a new data we wanna
say<00:01:02.520><c> over</c><00:01:02.820><c> here</c>

00:01:03.290 --> 00:01:03.300 align:start position:0%
say over here
 

00:01:03.300 --> 00:01:09.320 align:start position:0%
say over here
the<00:01:03.449><c> name</c><00:01:03.690><c> is</c><00:01:04.369><c> upgrade</c><00:01:06.140><c> request</c><00:01:08.060><c> so</c><00:01:09.060><c> let's</c><00:01:09.240><c> say</c>

00:01:09.320 --> 00:01:09.330 align:start position:0%
the name is upgrade request so let's say
 

00:01:09.330 --> 00:01:11.179 align:start position:0%
the name is upgrade request so let's say
someone<00:01:09.570><c> clicks</c><00:01:10.020><c> on</c><00:01:10.229><c> the</c><00:01:10.409><c> upgrade</c><00:01:10.560><c> about</c><00:01:11.070><c> it</c>

00:01:11.179 --> 00:01:11.189 align:start position:0%
someone clicks on the upgrade about it
 

00:01:11.189 --> 00:01:13.370 align:start position:0%
someone clicks on the upgrade about it
not<00:01:11.369><c> and</c><00:01:11.580><c> the</c><00:01:11.790><c> app</c><00:01:11.939><c> this</c><00:01:12.810><c> name</c><00:01:12.990><c> could</c><00:01:13.170><c> appear</c>

00:01:13.370 --> 00:01:13.380 align:start position:0%
not and the app this name could appear
 

00:01:13.380 --> 00:01:14.899 align:start position:0%
not and the app this name could appear
in<00:01:13.409><c> conversations</c><00:01:14.070><c> with</c><00:01:14.220><c> customers</c><00:01:14.430><c> if</c><00:01:14.820><c> you</c>

00:01:14.899 --> 00:01:14.909 align:start position:0%
in conversations with customers if you
 

00:01:14.909 --> 00:01:18.890 align:start position:0%
in conversations with customers if you
ask<00:01:15.229><c> optional</c><00:01:16.229><c> description</c><00:01:17.210><c> upgrade</c><00:01:18.210><c> okay</c>

00:01:18.890 --> 00:01:18.900 align:start position:0%
ask optional description upgrade okay
 

00:01:18.900 --> 00:01:20.929 align:start position:0%
ask optional description upgrade okay
that's<00:01:19.680><c> okay</c><00:01:19.860><c> we</c><00:01:20.189><c> don't</c><00:01:20.280><c> need</c><00:01:20.460><c> to</c><00:01:20.610><c> need</c><00:01:20.790><c> that</c>

00:01:20.929 --> 00:01:20.939 align:start position:0%
that's okay we don't need to need that
 

00:01:20.939 --> 00:01:23.840 align:start position:0%
that's okay we don't need to need that
but<00:01:21.150><c> okay</c><00:01:21.479><c> a</c><00:01:21.720><c> great</c><00:01:22.619><c> request</c><00:01:22.830><c> the</c><00:01:23.159><c> format</c><00:01:23.640><c> is</c>

00:01:23.840 --> 00:01:23.850 align:start position:0%
but okay a great request the format is
 

00:01:23.850 --> 00:01:27.499 align:start position:0%
but okay a great request the format is
text<00:01:24.570><c> the</c><00:01:24.869><c> format</c><00:01:25.470><c> is</c><00:01:25.880><c> true</c><00:01:26.880><c> or</c><00:01:26.939><c> false</c>

00:01:27.499 --> 00:01:27.509 align:start position:0%
text the format is true or false
 

00:01:27.509 --> 00:01:32.179 align:start position:0%
text the format is true or false
okay<00:01:28.460><c> I</c><00:01:29.689><c> should</c><00:01:30.689><c> put</c><00:01:31.170><c> two</c><00:01:31.350><c> SS</c><00:01:31.740><c> and</c><00:01:31.979><c> that's</c>

00:01:32.179 --> 00:01:32.189 align:start position:0%
okay I should put two SS and that's
 

00:01:32.189 --> 00:01:35.420 align:start position:0%
okay I should put two SS and that's
important<00:01:32.670><c> and</c><00:01:33.180><c> this</c><00:01:33.720><c> is</c><00:01:33.780><c> the</c><00:01:34.200><c> key</c><00:01:34.439><c> control</c><00:01:35.070><c> C</c>

00:01:35.420 --> 00:01:35.430 align:start position:0%
important and this is the key control C
 

00:01:35.430 --> 00:01:39.319 align:start position:0%
important and this is the key control C
and<00:01:35.700><c> save</c><00:01:37.520><c> okay</c><00:01:38.520><c> it's</c><00:01:38.670><c> gonna</c><00:01:38.759><c> be</c><00:01:38.939><c> true</c><00:01:39.180><c> or</c>

00:01:39.319 --> 00:01:39.329 align:start position:0%
and save okay it's gonna be true or
 

00:01:39.329 --> 00:01:42.260 align:start position:0%
and save okay it's gonna be true or
false<00:01:39.360><c> and</c><00:01:40.610><c> awesome</c><00:01:41.610><c> your</c><00:01:41.759><c> attribute</c><00:01:42.150><c> has</c>

00:01:42.260 --> 00:01:42.270 align:start position:0%
false and awesome your attribute has
 

00:01:42.270 --> 00:01:43.520 align:start position:0%
false and awesome your attribute has
been<00:01:42.420><c> created</c><00:01:42.750><c> start</c><00:01:42.990><c> sending</c><00:01:43.259><c> data</c><00:01:43.409><c> to</c>

00:01:43.520 --> 00:01:43.530 align:start position:0%
been created start sending data to
 

00:01:43.530 --> 00:01:45.679 align:start position:0%
been created start sending data to
intercom<00:01:43.979><c> by</c><00:01:44.130><c> adding</c><00:01:44.460><c> it</c><00:01:44.549><c> to</c><00:01:44.700><c> your</c><00:01:44.729><c> code</c><00:01:45.090><c> okay</c>

00:01:45.679 --> 00:01:45.689 align:start position:0%
intercom by adding it to your code okay
 

00:01:45.689 --> 00:01:49.310 align:start position:0%
intercom by adding it to your code okay
so<00:01:46.850><c> we're</c><00:01:47.850><c> gonna</c><00:01:47.970><c> add</c><00:01:48.180><c> it</c><00:01:48.299><c> to</c><00:01:48.420><c> our</c><00:01:48.509><c> code</c><00:01:48.780><c> that's</c>

00:01:49.310 --> 00:01:49.320 align:start position:0%
so we're gonna add it to our code that's
 

00:01:49.320 --> 00:01:51.560 align:start position:0%
so we're gonna add it to our code that's
relatively<00:01:49.860><c> simple</c><00:01:49.979><c> we</c><00:01:50.970><c> would</c><00:01:51.119><c> put</c><00:01:51.360><c> it</c><00:01:51.450><c> over</c>

00:01:51.560 --> 00:01:51.570 align:start position:0%
relatively simple we would put it over
 

00:01:51.570 --> 00:01:57.230 align:start position:0%
relatively simple we would put it over
here<00:01:51.990><c> within</c><00:01:52.200><c> our</c><00:01:52.439><c> code</c><00:01:53.990><c> this</c><00:01:54.990><c> is</c><00:01:55.820><c> not</c><00:01:56.820><c> exactly</c>

00:01:57.230 --> 00:01:57.240 align:start position:0%
here within our code this is not exactly
 

00:01:57.240 --> 00:01:58.850 align:start position:0%
here within our code this is not exactly
how<00:01:57.360><c> we're</c><00:01:57.570><c> gonna</c><00:01:57.689><c> do</c><00:01:57.899><c> it</c><00:01:58.020><c> currently</c><00:01:58.469><c> but</c><00:01:58.740><c> we</c>

00:01:58.850 --> 00:01:58.860 align:start position:0%
how we're gonna do it currently but we
 

00:01:58.860 --> 00:02:01.850 align:start position:0%
how we're gonna do it currently but we
may<00:01:59.270><c> first</c><00:02:00.270><c> we're</c><00:02:00.780><c> going</c><00:02:00.990><c> to</c><00:02:01.140><c> just</c><00:02:01.500><c> go</c><00:02:01.710><c> into</c>

00:02:01.850 --> 00:02:01.860 align:start position:0%
may first we're going to just go into
 

00:02:01.860 --> 00:02:04.580 align:start position:0%
may first we're going to just go into
the<00:02:02.040><c> social</c><00:02:02.219><c> app</c><00:02:02.549><c> and</c><00:02:02.820><c> here</c><00:02:03.030><c> we</c><00:02:03.149><c> are</c><00:02:03.590><c> okay</c>

00:02:04.580 --> 00:02:04.590 align:start position:0%
the social app and here we are okay
 

00:02:04.590 --> 00:02:07.219 align:start position:0%
the social app and here we are okay
we're<00:02:05.250><c> going</c><00:02:05.490><c> to</c><00:02:05.850><c> load</c><00:02:06.119><c> it</c><00:02:06.299><c> here</c><00:02:06.570><c> this</c><00:02:06.840><c> is</c><00:02:06.899><c> the</c>

00:02:07.219 --> 00:02:07.229 align:start position:0%
we're going to load it here this is the
 

00:02:07.229 --> 00:02:09.979 align:start position:0%
we're going to load it here this is the
syntax<00:02:07.500><c> by</c><00:02:07.920><c> the</c><00:02:07.979><c> way</c><00:02:08.190><c> I</c><00:02:08.220><c> can</c><00:02:08.520><c> load</c><00:02:09.300><c> this</c><00:02:09.539><c> syntax</c>

00:02:09.979 --> 00:02:09.989 align:start position:0%
syntax by the way I can load this syntax
 

00:02:09.989 --> 00:02:13.210 align:start position:0%
syntax by the way I can load this syntax
down<00:02:10.379><c> below</c><00:02:10.739><c> as</c><00:02:10.920><c> well</c><00:02:10.950><c> if</c><00:02:11.760><c> using</c><00:02:12.510><c> a</c>

00:02:13.210 --> 00:02:13.220 align:start position:0%
down below as well if using a
 

00:02:13.220 --> 00:02:16.330 align:start position:0%
down below as well if using a
oh<00:02:13.280><c> crap</c><00:02:14.120><c> this</c><00:02:14.780><c> is</c><00:02:14.960><c> specifically</c><00:02:15.950><c> if</c><00:02:16.160><c> you're</c>

00:02:16.330 --> 00:02:16.340 align:start position:0%
oh crap this is specifically if you're
 

00:02:16.340 --> 00:02:22.060 align:start position:0%
oh crap this is specifically if you're
using<00:02:16.550><c> just</c><00:02:17.150><c> a</c><00:02:17.270><c> regular</c><00:02:20.650><c> array</c>

00:02:22.060 --> 00:02:22.070 align:start position:0%
using just a regular array
 

00:02:22.070 --> 00:02:25.120 align:start position:0%
using just a regular array
a<00:02:22.210><c> regular</c><00:02:23.210><c> JavaScript</c><00:02:24.080><c> code</c><00:02:24.380><c> within</c><00:02:24.800><c> your</c>

00:02:25.120 --> 00:02:25.130 align:start position:0%
a regular JavaScript code within your
 

00:02:25.130 --> 00:02:26.920 align:start position:0%
a regular JavaScript code within your
app<00:02:25.280><c> you</c><00:02:25.640><c> have</c><00:02:25.670><c> to</c><00:02:25.880><c> look</c><00:02:26.030><c> up</c><00:02:26.180><c> the</c><00:02:26.240><c> syntax</c><00:02:26.780><c> but</c>

00:02:26.920 --> 00:02:26.930 align:start position:0%
app you have to look up the syntax but
 

00:02:26.930 --> 00:02:30.940 align:start position:0%
app you have to look up the syntax but
this<00:02:27.080><c> is</c><00:02:27.200><c> the</c><00:02:27.410><c> the</c><00:02:27.710><c> basis</c><00:02:28.220><c> of</c><00:02:28.400><c> it</c><00:02:28.580><c> okay</c><00:02:29.830><c> we</c><00:02:30.830><c> have</c>

00:02:30.940 --> 00:02:30.950 align:start position:0%
this is the the basis of it okay we have
 

00:02:30.950 --> 00:02:33.570 align:start position:0%
this is the the basis of it okay we have
an<00:02:31.070><c> intercom</c><00:02:31.310><c> settings</c><00:02:31.910><c> that</c><00:02:32.060><c> object</c><00:02:32.990><c> and</c>

00:02:33.570 --> 00:02:33.580 align:start position:0%
an intercom settings that object and
 

00:02:33.580 --> 00:02:36.490 align:start position:0%
an intercom settings that object and
instead<00:02:34.580><c> of</c><00:02:34.640><c> providing</c><00:02:35.090><c> new</c><00:02:35.450><c> campaign</c><00:02:36.110><c> clicks</c>

00:02:36.490 --> 00:02:36.500 align:start position:0%
instead of providing new campaign clicks
 

00:02:36.500 --> 00:02:40.840 align:start position:0%
instead of providing new campaign clicks
this<00:02:36.800><c> data</c><00:02:37.070><c> attribute</c><00:02:37.670><c> we</c><00:02:37.850><c> know</c><00:02:38.030><c> is</c><00:02:39.850><c> upgrade</c>

00:02:40.840 --> 00:02:40.850 align:start position:0%
this data attribute we know is upgrade
 

00:02:40.850 --> 00:02:47.199 align:start position:0%
this data attribute we know is upgrade
event<00:02:41.300><c> okay</c><00:02:41.810><c> so</c><00:02:41.870><c> upgrade</c><00:02:42.500><c> event</c><00:02:43.000><c> is</c><00:02:45.010><c> true</c><00:02:46.209><c> and</c>

00:02:47.199 --> 00:02:47.209 align:start position:0%
event okay so upgrade event is true and
 

00:02:47.209 --> 00:02:51.400 align:start position:0%
event okay so upgrade event is true and
then<00:02:48.080><c> we</c><00:02:48.260><c> click</c><00:02:48.470><c> update</c><00:02:49.490><c> okay</c><00:02:50.060><c> and</c><00:02:50.570><c> that's</c>

00:02:51.400 --> 00:02:51.410 align:start position:0%
then we click update okay and that's
 

00:02:51.410 --> 00:02:53.949 align:start position:0%
then we click update okay and that's
pretty<00:02:51.650><c> much</c><00:02:51.800><c> it</c><00:02:52.220><c> I</c><00:02:52.280><c> believe</c><00:02:52.850><c> not</c><00:02:53.510><c> sure</c><00:02:53.720><c> if</c><00:02:53.840><c> we</c>

00:02:53.949 --> 00:02:53.959 align:start position:0%
pretty much it I believe not sure if we
 

00:02:53.959 --> 00:02:56.890 align:start position:0%
pretty much it I believe not sure if we
need<00:02:54.200><c> this</c><00:02:54.380><c> thing</c><00:02:54.709><c> over</c><00:02:54.890><c> here</c><00:02:55.040><c> so</c><00:02:55.730><c> and</c><00:02:56.120><c> then</c><00:02:56.780><c> to</c>

00:02:56.890 --> 00:02:56.900 align:start position:0%
need this thing over here so and then to
 

00:02:56.900 --> 00:03:01.690 align:start position:0%
need this thing over here so and then to
come<00:02:57.080><c> update</c><00:02:58.660><c> this</c><00:02:59.660><c> I</c><00:03:00.280><c> don't</c><00:03:01.280><c> think</c><00:03:01.400><c> we</c><00:03:01.580><c> need</c>

00:03:01.690 --> 00:03:01.700 align:start position:0%
come update this I don't think we need
 

00:03:01.700 --> 00:03:05.170 align:start position:0%
come update this I don't think we need
that<00:03:01.760><c> as</c><00:03:02.060><c> well</c><00:03:02.500><c> that</c><00:03:03.550><c> cannot</c><00:03:04.550><c> set</c><00:03:04.760><c> property</c>

00:03:05.170 --> 00:03:05.180 align:start position:0%
that as well that cannot set property
 

00:03:05.180 --> 00:03:08.190 align:start position:0%
that as well that cannot set property
upgrade<00:03:05.540><c> reverse</c><00:03:05.840><c> of</c><00:03:05.990><c> undefined</c><00:03:06.590><c> ah</c><00:03:06.890><c> crap</c>

00:03:08.190 --> 00:03:08.200 align:start position:0%
upgrade reverse of undefined ah crap
 

00:03:08.200 --> 00:03:12.790 align:start position:0%
upgrade reverse of undefined ah crap
okay<00:03:09.200><c> so</c><00:03:10.720><c> we</c><00:03:11.720><c> need</c><00:03:11.840><c> to</c><00:03:11.930><c> go</c><00:03:12.080><c> and</c><00:03:12.230><c> tie</c><00:03:12.350><c> wrap</c><00:03:12.620><c> and</c>

00:03:12.790 --> 00:03:12.800 align:start position:0%
okay so we need to go and tie wrap and
 

00:03:12.800 --> 00:03:17.320 align:start position:0%
okay so we need to go and tie wrap and
try<00:03:12.920><c> to</c><00:03:12.980><c> figure</c><00:03:13.190><c> out</c><00:03:13.310><c> how</c><00:03:13.520><c> to</c><00:03:13.580><c> do</c><00:03:13.790><c> this</c><00:03:16.150><c> VAR</c><00:03:17.150><c> no</c>

00:03:17.320 --> 00:03:17.330 align:start position:0%
try to figure out how to do this VAR no
 

00:03:17.330 --> 00:03:19.990 align:start position:0%
try to figure out how to do this VAR no
user<00:03:17.650><c> well</c><00:03:18.650><c> here's</c><00:03:18.890><c> the</c><00:03:19.040><c> intercom</c><00:03:19.489><c> object</c>

00:03:19.990 --> 00:03:20.000 align:start position:0%
user well here's the intercom object
 

00:03:20.000 --> 00:03:21.640 align:start position:0%
user well here's the intercom object
that<00:03:20.180><c> I</c><00:03:20.209><c> do</c><00:03:20.360><c> know</c><00:03:20.630><c> that</c><00:03:20.660><c> it</c><00:03:20.989><c> the</c><00:03:21.170><c> integral</c>

00:03:21.640 --> 00:03:21.650 align:start position:0%
that I do know that it the integral
 

00:03:21.650 --> 00:03:23.199 align:start position:0%
that I do know that it the integral
object<00:03:22.010><c> is</c><00:03:22.190><c> there</c><00:03:22.400><c> here's</c><00:03:22.640><c> the</c><00:03:22.790><c> intercom</c>

00:03:23.199 --> 00:03:23.209 align:start position:0%
object is there here's the intercom
 

00:03:23.209 --> 00:03:27.630 align:start position:0%
object is there here's the intercom
object<00:03:23.890><c> let's</c><00:03:24.890><c> try</c><00:03:25.100><c> to</c><00:03:25.160><c> do</c><00:03:25.340><c> it</c><00:03:25.489><c> there</c><00:03:26.080><c> totally</c>

00:03:27.630 --> 00:03:27.640 align:start position:0%
object let's try to do it there totally
 

00:03:27.640 --> 00:03:31.020 align:start position:0%
object let's try to do it there totally
custom<00:03:28.640><c> today</c><00:03:28.880><c> it</c><00:03:29.090><c> was</c><00:03:29.239><c> upgraded</c><00:03:29.750><c> quests</c><00:03:30.170><c> no</c><00:03:30.380><c> I</c>

00:03:31.020 --> 00:03:31.030 align:start position:0%
custom today it was upgraded quests no I
 

00:03:31.030 --> 00:03:34.500 align:start position:0%
custom today it was upgraded quests no I
take<00:03:32.030><c> this</c><00:03:33.070><c> Chelsea</c>

00:03:34.500 --> 00:03:34.510 align:start position:0%
take this Chelsea
 

00:03:34.510 --> 00:03:40.660 align:start position:0%
take this Chelsea
over<00:03:35.510><c> here</c><00:03:35.810><c> guys</c><00:03:37.360><c> control</c><00:03:38.360><c> V</c><00:03:38.600><c> went</c><00:03:39.320><c> to</c><00:03:39.410><c> come</c><00:03:39.670><c> by</c>

00:03:40.660 --> 00:03:40.670 align:start position:0%
over here guys control V went to come by
 

00:03:40.670 --> 00:03:47.050 align:start position:0%
over here guys control V went to come by
window<00:03:41.150><c> dot</c><00:03:41.360><c> intercom</c><00:03:41.930><c> settings</c><00:03:43.750><c> but</c><00:03:46.060><c> dollar</c>

00:03:47.050 --> 00:03:47.060 align:start position:0%
window dot intercom settings but dollar
 

00:03:47.060 --> 00:03:51.930 align:start position:0%
window dot intercom settings but dollar
intercom

00:03:51.930 --> 00:03:51.940 align:start position:0%
 
 

00:03:51.940 --> 00:03:59.680 align:start position:0%
 
dollar<00:03:52.940><c> intercom</c><00:03:57.940><c> gather</c><00:03:58.940><c> intercom</c><00:03:59.510><c> the</c>

00:03:59.680 --> 00:03:59.690 align:start position:0%
dollar intercom gather intercom the
 

00:03:59.690 --> 00:04:01.449 align:start position:0%
dollar intercom gather intercom the
upgraded<00:04:00.320><c> price</c><00:04:00.530><c> equal</c><00:04:00.890><c> to</c><00:04:00.920><c> let's</c><00:04:01.280><c> see</c><00:04:01.310><c> if</c>

00:04:01.449 --> 00:04:01.459 align:start position:0%
upgraded price equal to let's see if
 

00:04:01.459 --> 00:04:04.270 align:start position:0%
upgraded price equal to let's see if
that<00:04:01.640><c> works</c><00:04:02.230><c> and</c><00:04:03.230><c> we're</c><00:04:03.620><c> going</c><00:04:03.860><c> to</c><00:04:03.920><c> reload</c>

00:04:04.270 --> 00:04:04.280 align:start position:0%
that works and we're going to reload
 

00:04:04.280 --> 00:04:16.020 align:start position:0%
that works and we're going to reload
this<00:04:11.049><c> something</c><00:04:12.049><c> broke</c><00:04:13.840><c> it's</c><00:04:14.840><c> did</c><00:04:15.049><c> not</c><00:04:15.230><c> work</c>

00:04:16.020 --> 00:04:16.030 align:start position:0%
this something broke it's did not work
 

00:04:16.030 --> 00:04:21.240 align:start position:0%
this something broke it's did not work
when<00:04:17.030><c> did</c><00:04:17.180><c> our</c><00:04:17.299><c> intercom</c><00:04:17.720><c> settings</c>

00:04:21.240 --> 00:04:21.250 align:start position:0%
 
 

00:04:21.250 --> 00:04:24.070 align:start position:0%
 
straightaway

00:04:24.070 --> 00:04:24.080 align:start position:0%
straightaway
 

00:04:24.080 --> 00:04:33.490 align:start position:0%
straightaway
ctrl<00:04:24.830><c> shift</c><00:04:24.860><c> Z</c><00:04:27.099><c> other</c><00:04:28.099><c> intercondylar</c><00:04:32.500><c> other</c>

00:04:33.490 --> 00:04:33.500 align:start position:0%
ctrl shift Z other intercondylar other
 

00:04:33.500 --> 00:04:38.499 align:start position:0%
ctrl shift Z other intercondylar other
we<00:04:33.740><c> have</c><00:04:33.889><c> something</c><00:04:34.400><c> here</c><00:04:37.180><c> this</c><00:04:38.180><c> is</c><00:04:38.360><c> the</c>

00:04:38.499 --> 00:04:38.509 align:start position:0%
we have something here this is the
 

00:04:38.509 --> 00:04:46.330 align:start position:0%
we have something here this is the
intercom<00:04:38.960><c> that</c><00:04:39.169><c> put</c><00:04:39.409><c> a</c><00:04:39.440><c> new</c><00:04:39.650><c> user</c><00:04:45.099><c> let's</c><00:04:46.099><c> try</c>

00:04:46.330 --> 00:04:46.340 align:start position:0%
intercom that put a new user let's try
 

00:04:46.340 --> 00:04:55.270 align:start position:0%
intercom that put a new user let's try
that<00:04:47.349><c> back</c><00:04:53.409><c> the</c><00:04:54.409><c> other</c><00:04:54.470><c> intercom</c><00:04:54.949><c> is</c><00:04:55.069><c> not</c>

00:04:55.270 --> 00:04:55.280 align:start position:0%
that back the other intercom is not
 

00:04:55.280 --> 00:04:58.029 align:start position:0%
that back the other intercom is not
defined<00:04:56.110><c> okay</c><00:04:57.110><c> we'll</c><00:04:57.379><c> figure</c><00:04:57.560><c> this</c><00:04:57.710><c> out</c><00:04:57.889><c> in</c>

00:04:58.029 --> 00:04:58.039 align:start position:0%
defined okay we'll figure this out in
 

00:04:58.039 --> 00:05:00.610 align:start position:0%
defined okay we'll figure this out in
another<00:04:58.099><c> video</c>

