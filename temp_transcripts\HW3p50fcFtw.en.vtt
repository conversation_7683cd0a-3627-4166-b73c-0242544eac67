WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.000 align:start position:0%
 
and<00:00:00.240><c> welcome</c><00:00:00.420><c> back</c><00:00:00.570><c> so</c><00:00:00.960><c> we're</c><00:00:01.290><c> continuing</c><00:00:01.860><c> on</c>

00:00:02.000 --> 00:00:02.010 align:start position:0%
and welcome back so we're continuing on
 

00:00:02.010 --> 00:00:03.320 align:start position:0%
and welcome back so we're continuing on
and<00:00:02.220><c> all</c><00:00:02.310><c> the</c><00:00:02.429><c> technologies</c><00:00:03.000><c> you're</c><00:00:03.149><c> gonna</c>

00:00:03.320 --> 00:00:03.330 align:start position:0%
and all the technologies you're gonna
 

00:00:03.330 --> 00:00:06.280 align:start position:0%
and all the technologies you're gonna
learn<00:00:03.570><c> within</c><00:00:04.230><c> this</c><00:00:04.500><c> tutorial</c><00:00:05.130><c> series</c><00:00:05.430><c> and</c>

00:00:06.280 --> 00:00:06.290 align:start position:0%
learn within this tutorial series and
 

00:00:06.290 --> 00:00:08.810 align:start position:0%
learn within this tutorial series and
we're<00:00:07.290><c> even</c><00:00:07.410><c> gonna</c><00:00:07.680><c> open</c><00:00:07.980><c> up</c><00:00:08.069><c> our</c><00:00:08.250><c> JavaScript</c>

00:00:08.810 --> 00:00:08.820 align:start position:0%
we're even gonna open up our JavaScript
 

00:00:08.820 --> 00:00:11.740 align:start position:0%
we're even gonna open up our JavaScript
console<00:00:09.240><c> I</c><00:00:09.360><c> press</c><00:00:09.540><c> ctrl</c><00:00:09.960><c> shift</c><00:00:09.990><c> J</c><00:00:10.590><c> this</c><00:00:11.550><c> is</c>

00:00:11.740 --> 00:00:11.750 align:start position:0%
console I press ctrl shift J this is
 

00:00:11.750 --> 00:00:14.270 align:start position:0%
console I press ctrl shift J this is
scope<00:00:12.750><c> doc</c><00:00:12.960><c> client</c><00:00:13.349><c> object</c><00:00:13.469><c> hey</c><00:00:14.009><c> from</c><00:00:14.190><c> the</c>

00:00:14.270 --> 00:00:14.280 align:start position:0%
scope doc client object hey from the
 

00:00:14.280 --> 00:00:16.939 align:start position:0%
scope doc client object hey from the
users<00:00:14.670><c> factory</c><00:00:15.150><c> and</c><00:00:15.420><c> cord</c><00:00:15.719><c> SAS</c><00:00:16.230><c> it</c><00:00:16.440><c> actually</c>

00:00:16.939 --> 00:00:16.949 align:start position:0%
users factory and cord SAS it actually
 

00:00:16.949 --> 00:00:19.939 align:start position:0%
users factory and cord SAS it actually
automatically<00:00:17.490><c> made</c><00:00:17.900><c> I</c><00:00:18.900><c> made</c><00:00:19.410><c> I</c><00:00:19.470><c> created</c><00:00:19.859><c> a</c>

00:00:19.939 --> 00:00:19.949 align:start position:0%
automatically made I made I created a
 

00:00:19.949 --> 00:00:22.189 align:start position:0%
automatically made I made I created a
user<00:00:20.130><c> Specter</c><00:00:20.670><c> e</c><00:00:20.730><c> which</c><00:00:20.970><c> which</c><00:00:21.689><c> actually</c>

00:00:22.189 --> 00:00:22.199 align:start position:0%
user Specter e which which actually
 

00:00:22.199 --> 00:00:25.130 align:start position:0%
user Specter e which which actually
catches<00:00:22.650><c> all</c><00:00:22.830><c> the</c><00:00:23.210><c> the</c><00:00:24.210><c> user</c><00:00:24.449><c> data</c><00:00:24.750><c> from</c><00:00:25.050><c> the</c>

00:00:25.130 --> 00:00:25.140 align:start position:0%
catches all the the user data from the
 

00:00:25.140 --> 00:00:26.960 align:start position:0%
catches all the the user data from the
database<00:00:25.439><c> user</c><00:00:26.010><c> all</c><00:00:26.130><c> the</c><00:00:26.250><c> messages</c><00:00:26.640><c> these</c><00:00:26.849><c> are</c>

00:00:26.960 --> 00:00:26.970 align:start position:0%
database user all the messages these are
 

00:00:26.970 --> 00:00:29.540 align:start position:0%
database user all the messages these are
all<00:00:27.090><c> the</c><00:00:27.180><c> socket</c><00:00:27.630><c> users</c><00:00:28.310><c> these</c><00:00:29.310><c> are</c><00:00:29.490><c> the</c>

00:00:29.540 --> 00:00:29.550 align:start position:0%
all the socket users these are the
 

00:00:29.550 --> 00:00:31.700 align:start position:0%
all the socket users these are the
submit<00:00:29.880><c> scope</c><00:00:30.449><c> that</c><00:00:30.660><c> client</c><00:00:31.170><c> object</c><00:00:31.320><c> that's</c>

00:00:31.700 --> 00:00:31.710 align:start position:0%
submit scope that client object that's
 

00:00:31.710 --> 00:00:36.410 align:start position:0%
submit scope that client object that's
an<00:00:31.949><c> interesting</c><00:00:32.489><c> one</c><00:00:33.710><c> I</c><00:00:34.710><c> go</c><00:00:35.489><c> to</c><00:00:35.550><c> data</c><00:00:35.850><c> and</c>

00:00:36.410 --> 00:00:36.420 align:start position:0%
an interesting one I go to data and
 

00:00:36.420 --> 00:00:39.770 align:start position:0%
an interesting one I go to data and
there's<00:00:37.170><c> my</c><00:00:37.410><c> Leisha</c><00:00:37.860><c> k</c><00:00:38.250><c> at</c><00:00:38.550><c> least</c><00:00:39.270><c> yet</c><00:00:39.450><c> koala</c>

00:00:39.770 --> 00:00:39.780 align:start position:0%
there's my Leisha k at least yet koala
 

00:00:39.780 --> 00:00:43.190 align:start position:0%
there's my Leisha k at least yet koala
CMS<00:00:40.260><c> calm</c><00:00:40.739><c> that's</c><00:00:41.610><c> currently</c><00:00:42.030><c> also</c><00:00:42.210><c> what's</c>

00:00:43.190 --> 00:00:43.200 align:start position:0%
CMS calm that's currently also what's
 

00:00:43.200 --> 00:00:44.930 align:start position:0%
CMS calm that's currently also what's
displayed<00:00:43.469><c> over</c><00:00:43.860><c> here</c><00:00:44.160><c> cuz</c><00:00:44.340><c> that's</c><00:00:44.520><c> a</c><00:00:44.640><c> scope</c>

00:00:44.930 --> 00:00:44.940 align:start position:0%
displayed over here cuz that's a scope
 

00:00:44.940 --> 00:00:48.110 align:start position:0%
displayed over here cuz that's a scope
variable<00:00:45.660><c> and</c><00:00:45.899><c> angularjs</c><00:00:46.770><c> a</c><00:00:47.430><c> lot</c><00:00:47.820><c> of</c><00:00:47.910><c> people</c>

00:00:48.110 --> 00:00:48.120 align:start position:0%
variable and angularjs a lot of people
 

00:00:48.120 --> 00:00:49.970 align:start position:0%
variable and angularjs a lot of people
say<00:00:48.239><c> angularjs</c><00:00:48.870><c> is</c><00:00:48.899><c> a</c><00:00:49.050><c> little</c><00:00:49.350><c> outdated</c><00:00:49.649><c> why</c>

00:00:49.970 --> 00:00:49.980 align:start position:0%
say angularjs is a little outdated why
 

00:00:49.980 --> 00:00:52.819 align:start position:0%
say angularjs is a little outdated why
make<00:00:50.250><c> a</c><00:00:50.280><c> video</c><00:00:50.670><c> in</c><00:00:50.850><c> 2018</c><00:00:51.510><c> about</c><00:00:51.750><c> angularjs</c><00:00:52.500><c> but</c>

00:00:52.819 --> 00:00:52.829 align:start position:0%
make a video in 2018 about angularjs but
 

00:00:52.829 --> 00:00:54.500 align:start position:0%
make a video in 2018 about angularjs but
at<00:00:53.370><c> the</c><00:00:53.520><c> end</c><00:00:53.550><c> of</c><00:00:53.699><c> the</c><00:00:53.760><c> day</c><00:00:53.850><c> I</c><00:00:53.879><c> still</c><00:00:54.239><c> think</c><00:00:54.480><c> that</c>

00:00:54.500 --> 00:00:54.510 align:start position:0%
at the end of the day I still think that
 

00:00:54.510 --> 00:00:56.029 align:start position:0%
at the end of the day I still think that
it's<00:00:54.690><c> a</c><00:00:54.750><c> great</c><00:00:54.899><c> technology</c><00:00:55.320><c> it's</c><00:00:55.710><c> got</c><00:00:55.860><c> a</c><00:00:55.890><c> lot</c>

00:00:56.029 --> 00:00:56.039 align:start position:0%
it's a great technology it's got a lot
 

00:00:56.039 --> 00:00:58.010 align:start position:0%
it's a great technology it's got a lot
of<00:00:56.070><c> open</c><00:00:56.430><c> source</c><00:00:56.610><c> modules</c><00:00:57.239><c> attached</c><00:00:57.570><c> to</c><00:00:57.870><c> it</c>

00:00:58.010 --> 00:00:58.020 align:start position:0%
of open source modules attached to it
 

00:00:58.020 --> 00:01:00.110 align:start position:0%
of open source modules attached to it
you<00:00:58.230><c> can</c><00:00:59.129><c> do</c><00:00:59.190><c> a</c><00:00:59.250><c> lot</c><00:00:59.399><c> of</c><00:00:59.520><c> great</c><00:00:59.670><c> things</c><00:00:59.879><c> with</c><00:01:00.090><c> it</c>

00:01:00.110 --> 00:01:00.120 align:start position:0%
you can do a lot of great things with it
 

00:01:00.120 --> 00:01:03.200 align:start position:0%
you can do a lot of great things with it
you<00:01:00.329><c> can</c><00:01:00.480><c> pretty</c><00:01:00.660><c> much</c><00:01:00.949><c> master</c><00:01:02.210><c> JavaScript</c>

00:01:03.200 --> 00:01:03.210 align:start position:0%
you can pretty much master JavaScript
 

00:01:03.210 --> 00:01:05.660 align:start position:0%
you can pretty much master JavaScript
and<00:01:03.739><c> understand</c><00:01:04.739><c> a</c><00:01:04.799><c> lot</c><00:01:04.949><c> more</c><00:01:04.979><c> about</c><00:01:05.339><c> how</c><00:01:05.519><c> it</c>

00:01:05.660 --> 00:01:05.670 align:start position:0%
and understand a lot more about how it
 

00:01:05.670 --> 00:01:08.240 align:start position:0%
and understand a lot more about how it
functions<00:01:06.600><c> or</c><00:01:07.080><c> I</c><00:01:07.110><c> can</c><00:01:07.290><c> have</c><00:01:07.380><c> variables</c><00:01:08.010><c> work</c>

00:01:08.240 --> 00:01:08.250 align:start position:0%
functions or I can have variables work
 

00:01:08.250 --> 00:01:11.300 align:start position:0%
functions or I can have variables work
and<00:01:08.460><c> how</c><00:01:08.520><c> to</c><00:01:08.580><c> structure</c><00:01:08.939><c> your</c><00:01:09.090><c> code</c><00:01:09.240><c> and</c><00:01:10.310><c> I'll</c>

00:01:11.300 --> 00:01:11.310 align:start position:0%
and how to structure your code and I'll
 

00:01:11.310 --> 00:01:13.280 align:start position:0%
and how to structure your code and I'll
have<00:01:11.460><c> that</c><00:01:11.580><c> HTML</c><00:01:12.180><c> and</c><00:01:12.299><c> CSS</c><00:01:12.450><c> because</c><00:01:13.020><c> those</c><00:01:13.170><c> are</c>

00:01:13.280 --> 00:01:13.290 align:start position:0%
have that HTML and CSS because those are
 

00:01:13.290 --> 00:01:15.109 align:start position:0%
have that HTML and CSS because those are
also<00:01:13.439><c> really</c><00:01:13.740><c> important</c><00:01:14.250><c> technologies</c><00:01:14.820><c> might</c>

00:01:15.109 --> 00:01:15.119 align:start position:0%
also really important technologies might
 

00:01:15.119 --> 00:01:17.830 align:start position:0%
also really important technologies might
do<00:01:15.240><c> a</c><00:01:15.270><c> couple</c><00:01:15.330><c> of</c><00:01:15.570><c> videos</c><00:01:15.630><c> on</c><00:01:16.020><c> HTML</c><00:01:16.650><c> and</c><00:01:16.770><c> CSS</c>

00:01:17.830 --> 00:01:17.840 align:start position:0%
do a couple of videos on HTML and CSS
 

00:01:17.840 --> 00:01:20.270 align:start position:0%
do a couple of videos on HTML and CSS
just<00:01:18.840><c> because</c><00:01:19.170><c> it's</c><00:01:19.320><c> such</c><00:01:19.439><c> a</c><00:01:19.470><c> crucial</c><00:01:19.770><c> part</c><00:01:20.189><c> of</c>

00:01:20.270 --> 00:01:20.280 align:start position:0%
just because it's such a crucial part of
 

00:01:20.280 --> 00:01:22.070 align:start position:0%
just because it's such a crucial part of
the<00:01:20.400><c> web</c><00:01:20.610><c> developer</c><00:01:21.150><c> you</c><00:01:21.180><c> have</c><00:01:21.509><c> to</c><00:01:21.750><c> know</c><00:01:21.900><c> it</c>

00:01:22.070 --> 00:01:22.080 align:start position:0%
the web developer you have to know it
 

00:01:22.080 --> 00:01:24.050 align:start position:0%
the web developer you have to know it
and<00:01:22.259><c> it</c><00:01:22.409><c> it's</c><00:01:22.860><c> gonna</c><00:01:23.070><c> allow</c><00:01:23.280><c> you</c><00:01:23.430><c> to</c><00:01:23.670><c> make</c><00:01:23.820><c> your</c>

00:01:24.050 --> 00:01:24.060 align:start position:0%
and it it's gonna allow you to make your
 

00:01:24.060 --> 00:01:28.219 align:start position:0%
and it it's gonna allow you to make your
pages<00:01:24.770><c> much</c><00:01:25.770><c> much</c><00:01:26.460><c> better</c><00:01:26.759><c> and</c><00:01:27.150><c> hopefully</c><00:01:27.930><c> we</c>

00:01:28.219 --> 00:01:28.229 align:start position:0%
pages much much better and hopefully we
 

00:01:28.229 --> 00:01:31.609 align:start position:0%
pages much much better and hopefully we
can<00:01:28.380><c> even</c><00:01:28.530><c> edit</c><00:01:29.430><c> something</c><00:01:29.880><c> like</c><00:01:30.030><c> this</c><00:01:30.530><c> see</c><00:01:31.530><c> as</c>

00:01:31.609 --> 00:01:31.619 align:start position:0%
can even edit something like this see as
 

00:01:31.619 --> 00:01:32.990 align:start position:0%
can even edit something like this see as
you<00:01:31.740><c> can</c><00:01:31.860><c> see</c><00:01:31.979><c> it's</c><00:01:32.100><c> it's</c><00:01:32.250><c> converted</c><00:01:32.790><c> to</c><00:01:32.909><c> the</c>

00:01:32.990 --> 00:01:33.000 align:start position:0%
you can see it's it's converted to the
 

00:01:33.000 --> 00:01:35.780 align:start position:0%
you can see it's it's converted to the
HTML<00:01:33.720><c> as</c><00:01:33.869><c> we</c><00:01:34.110><c> go</c><00:01:34.290><c> so</c><00:01:34.650><c> let's</c><00:01:35.280><c> say</c><00:01:35.520><c> for</c><00:01:35.759><c> example</c>

00:01:35.780 --> 00:01:35.790 align:start position:0%
HTML as we go so let's say for example
 

00:01:35.790 --> 00:01:41.080 align:start position:0%
HTML as we go so let's say for example
we<00:01:36.509><c> want</c><00:01:36.750><c> to</c><00:01:36.869><c> I</c><00:01:38.659><c> clicked</c><00:01:39.659><c> on</c><00:01:39.750><c> to</c><00:01:39.960><c> the</c><00:01:40.049><c> page</c><00:01:40.290><c> I</c>

00:01:41.080 --> 00:01:41.090 align:start position:0%
we want to I clicked on to the page I
 

00:01:41.090 --> 00:01:43.249 align:start position:0%
we want to I clicked on to the page I
want<00:01:42.090><c> to</c><00:01:42.180><c> highlight</c><00:01:42.329><c> everything</c><00:01:42.899><c> I</c><00:01:43.020><c> want</c><00:01:43.200><c> to</c>

00:01:43.249 --> 00:01:43.259 align:start position:0%
want to highlight everything I want to
 

00:01:43.259 --> 00:01:45.950 align:start position:0%
want to highlight everything I want to
make<00:01:43.350><c> it</c><00:01:43.470><c> italicize</c><00:01:43.950><c> and</c><00:01:44.159><c> remove</c><00:01:44.759><c> the</c><00:01:45.000><c> h1</c><00:01:45.479><c> okay</c>

00:01:45.950 --> 00:01:45.960 align:start position:0%
make it italicize and remove the h1 okay
 

00:01:45.960 --> 00:01:47.870 align:start position:0%
make it italicize and remove the h1 okay
and<00:01:46.140><c> have</c><00:01:46.259><c> a</c><00:01:46.290><c> look</c><00:01:46.470><c> what</c><00:01:46.500><c> happens</c><00:01:46.920><c> here</c><00:01:47.009><c> this</c>

00:01:47.870 --> 00:01:47.880 align:start position:0%
and have a look what happens here this
 

00:01:47.880 --> 00:01:49.520 align:start position:0%
and have a look what happens here this
is<00:01:48.030><c> actually</c><00:01:48.600><c> what</c><00:01:48.810><c> it's</c><00:01:49.049><c> gonna</c><00:01:49.200><c> look</c><00:01:49.290><c> like</c>

00:01:49.520 --> 00:01:49.530 align:start position:0%
is actually what it's gonna look like
 

00:01:49.530 --> 00:01:51.440 align:start position:0%
is actually what it's gonna look like
when<00:01:50.130><c> it's</c><00:01:50.159><c> converted</c><00:01:50.579><c> so</c><00:01:50.820><c> when</c><00:01:50.970><c> we</c><00:01:51.119><c> do</c><00:01:51.299><c> this</c>

00:01:51.440 --> 00:01:51.450 align:start position:0%
when it's converted so when we do this
 

00:01:51.450 --> 00:01:55.100 align:start position:0%
when it's converted so when we do this
and<00:01:51.720><c> I</c><00:01:52.229><c> could</c><00:01:52.500><c> add</c><00:01:53.360><c> black</c><00:01:54.360><c> clothes</c><00:01:54.630><c> submitted</c>

00:01:55.100 --> 00:01:55.110 align:start position:0%
and I could add black clothes submitted
 

00:01:55.110 --> 00:01:57.679 align:start position:0%
and I could add black clothes submitted
this<00:01:55.200><c> is</c><00:01:55.380><c> the</c><00:01:55.500><c> blog</c><00:01:55.740><c> data</c><00:01:56.100><c> object</c><00:01:56.759><c> TI's</c><00:01:57.210><c> been</c>

00:01:57.679 --> 00:01:57.689 align:start position:0%
this is the blog data object TI's been
 

00:01:57.689 --> 00:02:00.319 align:start position:0%
this is the blog data object TI's been
converted<00:01:58.259><c> to</c><00:01:58.290><c> HTML</c><00:01:58.500><c> and</c><00:01:59.250><c> we</c><00:01:59.820><c> can</c><00:02:00.000><c> actually</c>

00:02:00.319 --> 00:02:00.329 align:start position:0%
converted to HTML and we can actually
 

00:02:00.329 --> 00:02:02.289 align:start position:0%
converted to HTML and we can actually
even<00:02:00.509><c> fetch</c><00:02:00.899><c> that</c><00:02:01.140><c> currently</c><00:02:01.590><c> so</c><00:02:01.829><c> let's</c><00:02:02.009><c> go</c>

00:02:02.289 --> 00:02:02.299 align:start position:0%
even fetch that currently so let's go
 

00:02:02.299 --> 00:02:07.120 align:start position:0%
even fetch that currently so let's go
into<00:02:03.299><c> our</c><00:02:03.329><c> my</c><00:02:04.229><c> sequel</c><00:02:04.710><c> client</c><00:02:05.280><c> ID</c><00:02:05.520><c> sequel</c>

00:02:07.120 --> 00:02:07.130 align:start position:0%
into our my sequel client ID sequel
 

00:02:07.130 --> 00:02:10.380 align:start position:0%
into our my sequel client ID sequel
and<00:02:07.490><c> let's</c><00:02:07.939><c> look</c><00:02:08.450><c> for</c><00:02:08.660><c> the</c><00:02:08.750><c> database</c><00:02:09.200><c> we</c><00:02:09.470><c> have</c>

00:02:10.380 --> 00:02:10.390 align:start position:0%
and let's look for the database we have
 

00:02:10.390 --> 00:02:12.640 align:start position:0%
and let's look for the database we have
currently<00:02:11.390><c> connected</c><00:02:11.810><c> to</c><00:02:11.900><c> this</c><00:02:12.020><c> project</c><00:02:12.290><c> we</c>

00:02:12.640 --> 00:02:12.650 align:start position:0%
currently connected to this project we
 

00:02:12.650 --> 00:02:14.290 align:start position:0%
currently connected to this project we
launched<00:02:12.920><c> it</c><00:02:13.010><c> with</c><00:02:13.160><c> jaws</c><00:02:13.400><c> DB</c><00:02:13.819><c> there's</c><00:02:14.090><c> another</c>

00:02:14.290 --> 00:02:14.300 align:start position:0%
launched it with jaws DB there's another
 

00:02:14.300 --> 00:02:17.080 align:start position:0%
launched it with jaws DB there's another
video<00:02:15.130><c> in</c><00:02:16.130><c> the</c><00:02:16.310><c> description</c><00:02:16.550><c> if</c><00:02:16.970><c> you're</c>

00:02:17.080 --> 00:02:17.090 align:start position:0%
video in the description if you're
 

00:02:17.090 --> 00:02:19.330 align:start position:0%
video in the description if you're
interested<00:02:17.540><c> in</c><00:02:17.660><c> knowing</c><00:02:17.900><c> how</c><00:02:18.470><c> to</c><00:02:18.530><c> deploy</c><00:02:19.040><c> this</c>

00:02:19.330 --> 00:02:19.340 align:start position:0%
interested in knowing how to deploy this
 

00:02:19.340 --> 00:02:22.809 align:start position:0%
interested in knowing how to deploy this
project<00:02:19.880><c> to</c><00:02:20.030><c> Roku</c><00:02:20.709><c> ok</c><00:02:21.709><c> and</c><00:02:21.980><c> be</c><00:02:22.459><c> pretty</c><00:02:22.640><c> much</c>

00:02:22.809 --> 00:02:22.819 align:start position:0%
project to Roku ok and be pretty much
 

00:02:22.819 --> 00:02:25.750 align:start position:0%
project to Roku ok and be pretty much
given<00:02:23.150><c> away</c><00:02:23.410><c> the</c><00:02:24.410><c> credentials</c><00:02:24.950><c> unfortunately</c>

00:02:25.750 --> 00:02:25.760 align:start position:0%
given away the credentials unfortunately
 

00:02:25.760 --> 00:02:27.760 align:start position:0%
given away the credentials unfortunately
but<00:02:26.000><c> I</c><00:02:26.239><c> don't</c><00:02:26.840><c> really</c><00:02:26.959><c> mind</c><00:02:27.170><c> if</c><00:02:27.350><c> the</c><00:02:27.470><c> database</c>

00:02:27.760 --> 00:02:27.770 align:start position:0%
but I don't really mind if the database
 

00:02:27.770 --> 00:02:29.590 align:start position:0%
but I don't really mind if the database
gets<00:02:28.100><c> deleted</c><00:02:28.430><c> I'm</c><00:02:28.700><c> just</c><00:02:28.940><c> interested</c><00:02:29.209><c> in</c>

00:02:29.590 --> 00:02:29.600 align:start position:0%
gets deleted I'm just interested in
 

00:02:29.600 --> 00:02:31.930 align:start position:0%
gets deleted I'm just interested in
giving<00:02:30.350><c> some</c><00:02:30.680><c> tutorial</c><00:02:31.190><c> serious</c><00:02:31.550><c> about</c><00:02:31.790><c> it</c>

00:02:31.930 --> 00:02:31.940 align:start position:0%
giving some tutorial serious about it
 

00:02:31.940 --> 00:02:33.250 align:start position:0%
giving some tutorial serious about it
and<00:02:32.090><c> let's</c><00:02:32.209><c> look</c><00:02:32.360><c> at</c><00:02:32.450><c> the</c><00:02:32.510><c> blog</c><00:02:32.720><c> post</c><00:02:32.989><c> so</c><00:02:33.170><c> I'm</c>

00:02:33.250 --> 00:02:33.260 align:start position:0%
and let's look at the blog post so I'm
 

00:02:33.260 --> 00:02:38.610 align:start position:0%
and let's look at the blog post so I'm
going<00:02:33.470><c> to</c><00:02:33.620><c> select</c><00:02:33.920><c> all</c><00:02:34.100><c> from</c><00:02:34.280><c> the</c><00:02:34.370><c> blog</c><00:02:34.550><c> post</c>

00:02:38.610 --> 00:02:38.620 align:start position:0%
 
 

00:02:38.620 --> 00:02:41.259 align:start position:0%
 
so<00:02:39.620><c> that's</c><00:02:39.769><c> all</c><00:02:39.950><c> from</c><00:02:40.069><c> the</c><00:02:40.220><c> blog</c><00:02:40.459><c> post</c><00:02:40.760><c> table</c>

00:02:41.259 --> 00:02:41.269 align:start position:0%
so that's all from the blog post table
 

00:02:41.269 --> 00:02:43.360 align:start position:0%
so that's all from the blog post table
we're<00:02:42.080><c> gonna</c><00:02:42.170><c> run</c><00:02:42.440><c> the</c><00:02:42.590><c> query</c><00:02:42.860><c> and</c><00:02:43.100><c> there</c><00:02:43.280><c> we</c>

00:02:43.360 --> 00:02:43.370 align:start position:0%
we're gonna run the query and there we
 

00:02:43.370 --> 00:02:43.809 align:start position:0%
we're gonna run the query and there we
go

00:02:43.809 --> 00:02:43.819 align:start position:0%
go
 

00:02:43.819 --> 00:02:47.009 align:start position:0%
go
convert<00:02:44.569><c> it</c><00:02:44.690><c> to</c><00:02:44.720><c> HTML</c><00:02:45.010><c> post</c><00:02:46.010><c> publish</c><00:02:46.489><c> date</c>

00:02:47.009 --> 00:02:47.019 align:start position:0%
convert it to HTML post publish date
 

00:02:47.019 --> 00:02:49.930 align:start position:0%
convert it to HTML post publish date
2:25<00:02:48.019><c> at</c><00:02:48.200><c> 5:57</c><00:02:48.950><c> is</c><00:02:49.310><c> that</c><00:02:49.459><c> correct</c>

00:02:49.930 --> 00:02:49.940 align:start position:0%
2:25 at 5:57 is that correct
 

00:02:49.940 --> 00:02:55.990 align:start position:0%
2:25 at 5:57 is that correct
ooh<00:02:50.860><c> 758</c><00:02:53.380><c> 757</c><00:02:54.380><c> so</c><00:02:54.530><c> this</c><00:02:54.650><c> is</c><00:02:54.830><c> 2</c><00:02:55.130><c> hours</c><00:02:55.519><c> behind</c>

00:02:55.990 --> 00:02:56.000 align:start position:0%
ooh 758 757 so this is 2 hours behind
 

00:02:56.000 --> 00:02:58.240 align:start position:0%
ooh 758 757 so this is 2 hours behind
it's<00:02:56.300><c> on</c><00:02:56.480><c> like</c><00:02:56.720><c> national</c><00:02:57.260><c> international</c><00:02:57.650><c> time</c>

00:02:58.240 --> 00:02:58.250 align:start position:0%
it's on like national international time
 

00:02:58.250 --> 00:03:00.880 align:start position:0%
it's on like national international time
that's<00:02:58.610><c> the</c><00:02:58.760><c> sign</c><00:02:58.970><c> that</c><00:02:59.000><c> Oroku</c><00:02:59.600><c> is</c><00:02:59.780><c> on</c><00:02:59.959><c> but</c>

00:03:00.880 --> 00:03:00.890 align:start position:0%
that's the sign that Oroku is on but
 

00:03:00.890 --> 00:03:03.670 align:start position:0%
that's the sign that Oroku is on but
yeah<00:03:01.549><c> it's</c><00:03:01.940><c> pretty</c><00:03:02.060><c> much</c><00:03:02.360><c> reports</c><00:03:03.319><c> the</c><00:03:03.470><c> time</c>

00:03:03.670 --> 00:03:03.680 align:start position:0%
yeah it's pretty much reports the time
 

00:03:03.680 --> 00:03:05.740 align:start position:0%
yeah it's pretty much reports the time
that<00:03:03.860><c> the</c><00:03:03.950><c> current</c><00:03:04.340><c> server</c><00:03:04.760><c> is</c><00:03:05.060><c> deployed</c><00:03:05.540><c> on</c>

00:03:05.740 --> 00:03:05.750 align:start position:0%
that the current server is deployed on
 

00:03:05.750 --> 00:03:10.150 align:start position:0%
that the current server is deployed on
and<00:03:06.350><c> that's</c><00:03:06.530><c> on</c><00:03:06.709><c> Heroku</c><00:03:07.010><c> so</c><00:03:08.590><c> as</c><00:03:09.590><c> you</c><00:03:09.860><c> can</c><00:03:10.010><c> see</c>

00:03:10.150 --> 00:03:10.160 align:start position:0%
and that's on Heroku so as you can see
 

00:03:10.160 --> 00:03:11.710 align:start position:0%
and that's on Heroku so as you can see
we<00:03:10.310><c> got</c><00:03:10.459><c> it</c><00:03:10.670><c> and</c><00:03:10.819><c> we</c><00:03:10.910><c> can</c><00:03:11.060><c> pretty</c><00:03:11.239><c> much</c><00:03:11.329><c> take</c>

00:03:11.710 --> 00:03:11.720 align:start position:0%
we got it and we can pretty much take
 

00:03:11.720 --> 00:03:13.900 align:start position:0%
we got it and we can pretty much take
this<00:03:12.079><c> HTML</c><00:03:12.680><c> and</c><00:03:13.010><c> we're</c><00:03:13.130><c> gonna</c><00:03:13.220><c> show</c><00:03:13.459><c> that</c><00:03:13.640><c> back</c>

00:03:13.900 --> 00:03:13.910 align:start position:0%
this HTML and we're gonna show that back
 

00:03:13.910 --> 00:03:15.850 align:start position:0%
this HTML and we're gonna show that back
to<00:03:14.120><c> the</c><00:03:14.239><c> user</c><00:03:14.450><c> so</c><00:03:14.600><c> every</c><00:03:15.290><c> single</c><00:03:15.560><c> one</c><00:03:15.650><c> of</c><00:03:15.709><c> the</c>

00:03:15.850 --> 00:03:15.860 align:start position:0%
to the user so every single one of the
 

00:03:15.860 --> 00:03:17.860 align:start position:0%
to the user so every single one of the
blog<00:03:16.100><c> articles</c><00:03:16.700><c> that</c><00:03:16.910><c> we</c><00:03:17.060><c> have</c><00:03:17.239><c> is</c><00:03:17.450><c> gonna</c><00:03:17.660><c> have</c>

00:03:17.860 --> 00:03:17.870 align:start position:0%
blog articles that we have is gonna have
 

00:03:17.870 --> 00:03:20.470 align:start position:0%
blog articles that we have is gonna have
a<00:03:17.900><c> big</c><00:03:18.230><c> chunk</c><00:03:18.530><c> of</c><00:03:18.709><c> HTML</c><00:03:19.180><c> associated</c><00:03:20.180><c> to</c><00:03:20.329><c> it</c>

00:03:20.470 --> 00:03:20.480 align:start position:0%
a big chunk of HTML associated to it
 

00:03:20.480 --> 00:03:23.080 align:start position:0%
a big chunk of HTML associated to it
which<00:03:21.019><c> the</c><00:03:21.200><c> user</c><00:03:21.440><c> himself</c><00:03:22.040><c> can</c><00:03:22.340><c> create</c><00:03:22.730><c> and</c>

00:03:23.080 --> 00:03:23.090 align:start position:0%
which the user himself can create and
 

00:03:23.090 --> 00:03:25.180 align:start position:0%
which the user himself can create and
once<00:03:23.359><c> he</c><00:03:23.510><c> creates</c><00:03:23.840><c> every</c><00:03:24.200><c> blog</c><00:03:24.440><c> post</c><00:03:24.709><c> what</c>

00:03:25.180 --> 00:03:25.190 align:start position:0%
once he creates every blog post what
 

00:03:25.190 --> 00:03:26.530 align:start position:0%
once he creates every blog post what
also<00:03:25.370><c> gonna</c><00:03:25.519><c> need</c><00:03:25.609><c> to</c><00:03:25.790><c> be</c><00:03:25.910><c> able</c><00:03:25.970><c> to</c><00:03:26.120><c> upload</c>

00:03:26.530 --> 00:03:26.540 align:start position:0%
also gonna need to be able to upload
 

00:03:26.540 --> 00:03:28.000 align:start position:0%
also gonna need to be able to upload
images<00:03:26.720><c> and</c><00:03:27.170><c> we're</c><00:03:27.260><c> gonna</c><00:03:27.380><c> do</c><00:03:27.590><c> that</c><00:03:27.620><c> with</c>

00:03:28.000 --> 00:03:28.010 align:start position:0%
images and we're gonna do that with
 

00:03:28.010 --> 00:03:31.720 align:start position:0%
images and we're gonna do that with
actually<00:03:28.549><c> something</c><00:03:28.730><c> called</c><00:03:29.200><c> s3</c><00:03:30.200><c> on</c><00:03:30.530><c> AWS</c><00:03:31.370><c> and</c>

00:03:31.720 --> 00:03:31.730 align:start position:0%
actually something called s3 on AWS and
 

00:03:31.730 --> 00:03:34.630 align:start position:0%
actually something called s3 on AWS and
if<00:03:31.790><c> we</c><00:03:31.970><c> continue</c><00:03:32.150><c> here</c><00:03:32.720><c> that's</c><00:03:33.530><c> what</c><00:03:34.280><c> we</c><00:03:34.310><c> wrote</c>

00:03:34.630 --> 00:03:34.640 align:start position:0%
if we continue here that's what we wrote
 

00:03:34.640 --> 00:03:38.470 align:start position:0%
if we continue here that's what we wrote
here<00:03:35.530><c> AWS</c><00:03:36.530><c> s3</c><00:03:36.609><c> image</c><00:03:37.609><c> storage</c><00:03:37.970><c> that's</c><00:03:38.359><c> what</c>

00:03:38.470 --> 00:03:38.480 align:start position:0%
here AWS s3 image storage that's what
 

00:03:38.480 --> 00:03:40.270 align:start position:0%
here AWS s3 image storage that's what
we're<00:03:38.570><c> going</c><00:03:38.690><c> to</c><00:03:38.720><c> use</c><00:03:38.810><c> as</c><00:03:39.140><c> our</c><00:03:39.470><c> solution</c><00:03:39.680><c> for</c>

00:03:40.270 --> 00:03:40.280 align:start position:0%
we're going to use as our solution for
 

00:03:40.280 --> 00:03:41.710 align:start position:0%
we're going to use as our solution for
storing<00:03:40.370><c> images</c><00:03:40.970><c> and</c><00:03:41.120><c> we're</c><00:03:41.209><c> going</c><00:03:41.359><c> to</c><00:03:41.420><c> do</c><00:03:41.540><c> a</c>

00:03:41.710 --> 00:03:41.720 align:start position:0%
storing images and we're going to do a
 

00:03:41.720 --> 00:03:43.990 align:start position:0%
storing images and we're going to do a
video<00:03:42.170><c> hopefully</c><00:03:42.680><c> right</c><00:03:43.010><c> after</c><00:03:43.280><c> this</c><00:03:43.430><c> one</c><00:03:43.670><c> on</c>

00:03:43.990 --> 00:03:44.000 align:start position:0%
video hopefully right after this one on
 

00:03:44.000 --> 00:03:47.770 align:start position:0%
video hopefully right after this one on
how<00:03:44.540><c> to</c><00:03:44.859><c> store</c><00:03:45.859><c> images</c><00:03:46.340><c> on</c><00:03:46.579><c> AWS</c><00:03:47.390><c> and</c><00:03:47.660><c> that's</c>

00:03:47.770 --> 00:03:47.780 align:start position:0%
how to store images on AWS and that's
 

00:03:47.780 --> 00:03:50.410 align:start position:0%
how to store images on AWS and that's
really<00:03:47.959><c> cool</c><00:03:48.320><c> it's</c><00:03:48.620><c> gonna</c><00:03:49.100><c> be</c><00:03:49.340><c> an</c><00:03:49.579><c> integration</c>

00:03:50.410 --> 00:03:50.420 align:start position:0%
really cool it's gonna be an integration
 

00:03:50.420 --> 00:03:52.210 align:start position:0%
really cool it's gonna be an integration
of<00:03:50.570><c> an</c><00:03:50.720><c> open-source</c><00:03:51.019><c> repo</c><00:03:51.620><c> I</c><00:03:51.650><c> found</c><00:03:51.980><c> online</c>

00:03:52.210 --> 00:03:52.220 align:start position:0%
of an open-source repo I found online
 

00:03:52.220 --> 00:03:53.650 align:start position:0%
of an open-source repo I found online
and<00:03:52.579><c> also</c><00:03:52.730><c> I'm</c><00:03:52.850><c> gonna</c><00:03:52.940><c> give</c><00:03:53.150><c> credit</c><00:03:53.510><c> to</c><00:03:53.540><c> that</c>

00:03:53.650 --> 00:03:53.660 align:start position:0%
and also I'm gonna give credit to that
 

00:03:53.660 --> 00:03:56.229 align:start position:0%
and also I'm gonna give credit to that
author<00:03:54.140><c> I</c><00:03:54.590><c> just</c><00:03:55.130><c> did</c><00:03:55.280><c> a</c><00:03:55.310><c> couple</c><00:03:55.489><c> of</c><00:03:55.640><c> changes</c><00:03:56.090><c> on</c>

00:03:56.229 --> 00:03:56.239 align:start position:0%
author I just did a couple of changes on
 

00:03:56.239 --> 00:03:59.590 align:start position:0%
author I just did a couple of changes on
it<00:03:56.420><c> in</c><00:03:56.570><c> order</c><00:03:56.720><c> to</c><00:03:56.840><c> help</c><00:03:57.049><c> with</c><00:03:58.600><c> synchronization</c>

00:03:59.590 --> 00:03:59.600 align:start position:0%
it in order to help with synchronization
 

00:03:59.600 --> 00:04:02.319 align:start position:0%
it in order to help with synchronization
and<00:03:59.900><c> so</c><00:04:00.140><c> on</c><00:04:00.350><c> and</c><00:04:00.829><c> to</c><00:04:01.459><c> match</c><00:04:01.640><c> my</c><00:04:01.850><c> project</c>

00:04:02.319 --> 00:04:02.329 align:start position:0%
and so on and to match my project
 

00:04:02.329 --> 00:04:05.349 align:start position:0%
and so on and to match my project
structure<00:04:02.660><c> so</c><00:04:03.560><c> that's</c><00:04:04.340><c> gonna</c><00:04:04.489><c> be</c><00:04:04.730><c> really</c><00:04:04.940><c> cool</c>

00:04:05.349 --> 00:04:05.359 align:start position:0%
structure so that's gonna be really cool
 

00:04:05.359 --> 00:04:07.270 align:start position:0%
structure so that's gonna be really cool
we<00:04:05.720><c> have</c><00:04:05.840><c> angular</c><00:04:06.079><c> UI</c><00:04:06.380><c> rather</c><00:04:06.709><c> than</c><00:04:06.859><c> ejs</c>

00:04:07.270 --> 00:04:07.280 align:start position:0%
we have angular UI rather than ejs
 

00:04:07.280 --> 00:04:08.259 align:start position:0%
we have angular UI rather than ejs
that's<00:04:07.579><c> going</c><00:04:07.730><c> to</c><00:04:07.790><c> be</c><00:04:07.850><c> covered</c><00:04:08.150><c> in</c><00:04:08.209><c> future</c>

00:04:08.259 --> 00:04:08.269 align:start position:0%
that's going to be covered in future
 

00:04:08.269 --> 00:04:11.259 align:start position:0%
that's going to be covered in future
videos<00:04:08.930><c> we</c><00:04:09.170><c> have</c><00:04:09.319><c> socket</c><00:04:09.739><c> IO</c><00:04:10.040><c> really</c><00:04:10.730><c> cool</c><00:04:10.970><c> for</c>

00:04:11.259 --> 00:04:11.269 align:start position:0%
videos we have socket IO really cool for
 

00:04:11.269 --> 00:04:13.030 align:start position:0%
videos we have socket IO really cool for
chat<00:04:11.569><c> apps</c><00:04:11.840><c> anything</c><00:04:12.380><c> that</c><00:04:12.500><c> has</c><00:04:12.650><c> to</c><00:04:12.769><c> do</c><00:04:12.890><c> with</c>

00:04:13.030 --> 00:04:13.040 align:start position:0%
chat apps anything that has to do with
 

00:04:13.040 --> 00:04:15.340 align:start position:0%
chat apps anything that has to do with
with<00:04:13.700><c> live</c><00:04:13.970><c> reloading</c><00:04:14.540><c> passport</c>

00:04:15.340 --> 00:04:15.350 align:start position:0%
with live reloading passport
 

00:04:15.350 --> 00:04:17.170 align:start position:0%
with live reloading passport
authentication<00:04:16.070><c> and</c><00:04:16.280><c> no</c><00:04:16.459><c> mail</c><00:04:16.729><c> they're</c><00:04:16.910><c> using</c>

00:04:17.170 --> 00:04:17.180 align:start position:0%
authentication and no mail they're using
 

00:04:17.180 --> 00:04:18.830 align:start position:0%
authentication and no mail they're using
mail<00:04:17.389><c> that</c><00:04:17.570><c> if</c><00:04:17.690><c> you</c><00:04:17.810><c> need</c>

00:04:18.830 --> 00:04:18.840 align:start position:0%
mail that if you need
 

00:04:18.840 --> 00:04:20.360 align:start position:0%
mail that if you need
they're<00:04:18.959><c> an</c><00:04:19.019><c> automated</c><00:04:19.380><c> email</c><00:04:19.590><c> system</c><00:04:20.190><c> that</c>

00:04:20.360 --> 00:04:20.370 align:start position:0%
they're an automated email system that
 

00:04:20.370 --> 00:04:22.790 align:start position:0%
they're an automated email system that
runs<00:04:20.489><c> on</c><00:04:20.730><c> node</c><00:04:20.970><c> schedule</c><00:04:21.540><c> that's</c><00:04:22.530><c> another</c>

00:04:22.790 --> 00:04:22.800 align:start position:0%
runs on node schedule that's another
 

00:04:22.800 --> 00:04:27.220 align:start position:0%
runs on node schedule that's another
project<00:04:23.490><c> here</c><00:04:23.970><c> and</c><00:04:24.919><c> or</c><00:04:25.919><c> just</c><00:04:25.980><c> something</c><00:04:26.790><c> as</c><00:04:26.970><c> a</c>

00:04:27.220 --> 00:04:27.230 align:start position:0%
project here and or just something as a
 

00:04:27.230 --> 00:04:30.110 align:start position:0%
project here and or just something as a
as<00:04:28.230><c> a</c><00:04:28.290><c> contact</c><00:04:28.740><c> form</c><00:04:29.100><c> so</c><00:04:29.400><c> hope</c><00:04:29.940><c> you</c><00:04:30.090><c> guys</c>

00:04:30.110 --> 00:04:30.120 align:start position:0%
as a contact form so hope you guys
 

00:04:30.120 --> 00:04:34.100 align:start position:0%
as a contact form so hope you guys
really<00:04:30.240><c> enjoy</c><00:04:30.780><c> these</c><00:04:30.960><c> series</c><00:04:32.000><c> and</c><00:04:33.000><c> we'll</c><00:04:33.930><c> see</c>

00:04:34.100 --> 00:04:34.110 align:start position:0%
really enjoy these series and we'll see
 

00:04:34.110 --> 00:04:35.540 align:start position:0%
really enjoy these series and we'll see
you<00:04:34.200><c> in</c><00:04:34.290><c> the</c><00:04:34.380><c> next</c><00:04:34.590><c> video</c><00:04:34.830><c> be</c><00:04:35.010><c> sure</c><00:04:35.190><c> to</c><00:04:35.250><c> LIKE</c>

00:04:35.540 --> 00:04:35.550 align:start position:0%
you in the next video be sure to LIKE
 

00:04:35.550 --> 00:04:37.700 align:start position:0%
you in the next video be sure to LIKE
the<00:04:35.730><c> video</c><00:04:35.850><c> if</c><00:04:36.330><c> you</c><00:04:36.780><c> appreciate</c><00:04:37.290><c> it</c>

00:04:37.700 --> 00:04:37.710 align:start position:0%
the video if you appreciate it
 

00:04:37.710 --> 00:04:39.500 align:start position:0%
the video if you appreciate it
and<00:04:37.830><c> stick</c><00:04:38.610><c> around</c><00:04:38.730><c> have</c><00:04:39.090><c> a</c><00:04:39.150><c> look</c><00:04:39.330><c> at</c><00:04:39.419><c> the</c>

00:04:39.500 --> 00:04:39.510 align:start position:0%
and stick around have a look at the
 

00:04:39.510 --> 00:04:43.100 align:start position:0%
and stick around have a look at the
channel<00:04:39.720><c> overview</c><00:04:40.320><c> and</c><00:04:40.590><c> have</c><00:04:40.740><c> a</c><00:04:40.770><c> good</c><00:04:40.950><c> one</c>

