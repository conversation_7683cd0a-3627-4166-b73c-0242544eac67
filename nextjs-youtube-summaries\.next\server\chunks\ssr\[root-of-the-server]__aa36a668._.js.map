{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\r\n\r\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\r\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\r\n\r\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\r\n\r\n// For server-side operations that require elevated permissions\r\nexport const supabaseAdmin = createClient(\r\n  supabaseUrl,\r\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\r\n)\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACtC,aACA,QAAQ,GAAG,CAAC,yBAAyB", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/lib/database.ts"], "sourcesContent": ["import { supabaseAdmin } from './supabase'\r\nimport { VideoSummary, TopicTable, SearchResult } from '@/types'\r\n\r\nexport class DatabaseService {\r\n  // Get all available topic tables with better error handling\r\n  static async getTopicTables(): Promise<TopicTable[]> {\r\n    try {\r\n      // Use the actual YouTube topic table names discovered in the database\r\n      const youtubeTopics = [\r\n        'youtube_artificial_intelligence',\r\n        'youtube_sustainability', \r\n        'youtube_startups',\r\n        'youtube_financial_markets',\r\n        'youtube_gme',\r\n        'youtube_general',\r\n        'youtube_legal',\r\n        'youtube_renewable_energy'\r\n      ]\r\n      const existingTables: TopicTable[] = []\r\n      \r\n      console.log('Checking for YouTube topic tables...')\r\n      for (const topic of youtubeTopics) {        \r\n        try {\r\n          const { error, count } = await supabaseAdmin\r\n            .from(topic)\r\n            .select('id', { count: 'exact', head: true })\r\n            .limit(1)\r\n          \r\n          if (!error) {\r\n            console.log(`Found table: ${topic} with ${count || 0} videos`)\r\n            existingTables.push({\r\n              id: topic, // Assuming table name can serve as a unique ID here\r\n              table_name: topic,\r\n              name: topic,\r\n              display_name: this.formatDisplayName(topic),\r\n              description: `${this.formatDisplayName(topic)} videos`,\r\n              video_count: count || 0,\r\n              created_at: new Date().toISOString(), // Placeholder\r\n              updated_at: new Date().toISOString() // Placeholder\r\n            })\r\n          }\r\n        } catch {\r\n          // Table doesn't exist, skip silently\r\n        }\r\n      }\r\n      \r\n      if (existingTables.length > 0) {\r\n        return existingTables\r\n      }\r\n      \r\n      console.warn('No YouTube topic tables found in database, using fallback topics')\r\n      // Return fallback topics for development\r\n      const fallbackDate = new Date().toISOString();\r\n      return [\r\n        { id: 'youtube_artificial_intelligence', table_name: 'youtube_artificial_intelligence', name: 'youtube_artificial_intelligence', display_name: 'Artificial Intelligence', description: 'AI and machine learning videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate },\r\n        { id: 'youtube_sustainability', table_name: 'youtube_sustainability', name: 'youtube_sustainability', display_name: 'Sustainability', description: 'Sustainability and environment videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate },\r\n        { id: 'youtube_startups', table_name: 'youtube_startups', name: 'youtube_startups', display_name: 'Startups', description: 'Startup and entrepreneurship videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate },\r\n        { id: 'youtube_financial_markets', table_name: 'youtube_financial_markets', name: 'youtube_financial_markets', display_name: 'Financial Markets', description: 'Finance and market videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate }\r\n      ]\r\n    } catch (error) {\r\n      console.error('Error fetching topic tables:', error)\r\n      // Return fallback topics when database is not accessible\r\n      const fallbackDate = new Date().toISOString();\r\n      return [\r\n        { id: 'youtube_artificial_intelligence', table_name: 'youtube_artificial_intelligence', name: 'youtube_artificial_intelligence', display_name: 'Artificial Intelligence', description: 'AI and machine learning videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate },\r\n        { id: 'youtube_sustainability', table_name: 'youtube_sustainability', name: 'youtube_sustainability', display_name: 'Sustainability', description: 'Sustainability and environment videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate },\r\n        { id: 'youtube_startups', table_name: 'youtube_startups', name: 'youtube_startups', display_name: 'Startups', description: 'Startup and entrepreneurship videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate },\r\n        { id: 'youtube_financial_markets', table_name: 'youtube_financial_markets', name: 'youtube_financial_markets', display_name: 'Financial Markets', description: 'Finance and market videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate }\r\n      ]\r\n    }\r\n  }\r\n\r\n  // Helper method to format display names\r\n  private static formatDisplayName(tableName: string): string {\r\n    return tableName\r\n      .replace('youtube_', '') // Remove youtube_ prefix\r\n      .replace('_', ' ')\r\n      .split(' ')\r\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n      .join(' ')\r\n  }\r\n  // Get all unique channels from available topic tables\r\n  static async getChannels(topicFilter?: string[]): Promise<string[]> {\r\n    try {\r\n      const allChannels = new Set<string>()\r\n      \r\n      // Get all available topic tables\r\n      const allTopicTables = await this.getTopicTables()\r\n      \r\n      // Filter topic tables if topicFilter is provided\r\n      const topicTables = topicFilter && topicFilter.length > 0\r\n        ? allTopicTables.filter(t => {\r\n            const simpleName = t.name.startsWith('youtube_') ? t.name.substring(8) : t.name\r\n            return topicFilter.includes(simpleName) || topicFilter.includes(t.name)\r\n          })\r\n        : allTopicTables\r\n      \r\n      console.log('Fetching channels from tables:', topicTables.map(t => t.name))\r\n      console.log('Topic filter applied:', topicFilter)\r\n        // Query each table for unique channels\r\n      for (const topic of topicTables) {        try {\r\n          // Use distinct to get unique channel names without arbitrary limits\r\n          const { data, error } = await supabaseAdmin\r\n            .from(topic.name)\r\n            .select('channel_name')\r\n            .not('channel_name', 'is', null)\r\n            .order('channel_name')\r\n          \r\n          if (!error && data) {\r\n            console.log(`Found ${data.length} channel entries in ${topic.name}`)\r\n            data.forEach((item: { channel_name: string }) => {\r\n              if (item.channel_name && item.channel_name.trim()) {\r\n                allChannels.add(item.channel_name.trim())\r\n              }\r\n            })\r\n          } else if (error) {\r\n            console.warn(`Error fetching channels from ${topic.name}:`, error.message)\r\n          }\r\n        } catch (tableError) {\r\n          console.warn(`Error accessing table ${topic.name}:`, tableError)\r\n          // Continue with other tables\r\n        }\r\n      }\r\n      \r\n      const channels = Array.from(allChannels).sort()\r\n      console.log(`Total unique channels found: ${channels.length}`)\r\n      \r\n      // If no channels found in database, return some sample channels for development\r\n      if (channels.length === 0) {\r\n        console.warn('No channels found in database, returning sample channels for development')\r\n        return [\r\n          'Sample Tech Channel',\r\n          'Sample Programming Channel', \r\n          'Sample AI Channel',\r\n          'Sample Science Channel'\r\n        ]\r\n      }\r\n      \r\n      return channels\r\n    } catch (error) {\r\n      console.error('Error fetching channels:', error)\r\n      // Return sample channels for development when database is not accessible\r\n      return [\r\n        'Sample Tech Channel',\r\n        'Sample Programming Channel',\r\n        'Sample AI Channel', \r\n        'Sample Science Channel'\r\n      ]\r\n    }\r\n  }  // Get videos filtered by topics and/or channels\r\n  static async getFilteredVideos(\r\n    topicTables?: string[],\r\n    channels?: string[],\r\n    limit: number = 20,\r\n    offset: number = 0,\r\n    startDate?: string,\r\n    endDate?: string\r\n  ): Promise<VideoSummary[]> {\r\n    try {\r\n      const allVideos: VideoSummary[] = []\r\n      const seenVideoIds = new Set<string>() // Track video IDs to prevent duplicates\r\n      \r\n      // Get available topic tables if not provided\r\n      const tablesToQuery = topicTables && topicTables.length > 0 \r\n        ? topicTables \r\n        : (await this.getTopicTables()).map(t => t.table_name); // Use table_name\r\n        // Calculate how many videos to fetch from each table to account for deduplication\r\n      // When we have filters (channels, dates), we need to be more generous with the limit\r\n      // to ensure we get enough results after filtering and deduplication\r\n      const hasFilters = (channels && channels.length > 0) || startDate || endDate\r\n      const multiplier = hasFilters ? 3 : 2 // Be more generous when filtering\r\n      const perTableLimit = Math.max((limit + offset) * multiplier, 200)\r\n      \r\n      // Query each table\r\n      for (const tableName of tablesToQuery) {\r\n        try {\r\n          let query = supabaseAdmin\r\n            .from(tableName)\r\n            .select('*')\r\n            .order('published_at', { ascending: false })\r\n            \r\n          // Filter by channels if provided\r\n          if (channels && channels.length > 0) {\r\n            query = query.in('channel_name', channels)\r\n          }\r\n          \r\n          // Filter by date range if provided\r\n          if (startDate) {\r\n            query = query.gte('published_at', startDate)\r\n          }\r\n          if (endDate) {\r\n            query = query.lte('published_at', endDate)\r\n          }\r\n          \r\n          const { data, error } = await query.limit(perTableLimit)\r\n          \r\n          if (!error && data) {\r\n            // Add videos, but only if we haven't seen this video_id before\r\n            for (const video of data) {\r\n              if (!seenVideoIds.has(video.video_id)) {\r\n                seenVideoIds.add(video.video_id)\r\n                allVideos.push(video)\r\n              }\r\n            }\r\n          }\r\n        } catch (tableError) {\r\n          console.warn(`Error fetching videos from ${tableName}:`, tableError)\r\n          // Continue with other tables\r\n        }\r\n      }\r\n      \r\n      // Sort all videos by published date (most recent first)\r\n      const sortedVideos = allVideos.sort((a, b) => {\r\n        const dateA = a.published_at ? new Date(a.published_at).getTime() : 0;\r\n        const dateB = b.published_at ? new Date(b.published_at).getTime() : 0;\r\n        return dateB - dateA;\r\n      })\r\n      \r\n      // Apply pagination after sorting and deduplication\r\n      return sortedVideos.slice(offset, offset + limit)\r\n        \r\n    } catch (error) {\r\n      console.error('Error fetching filtered videos:', error)\r\n      return []\r\n    }\r\n  }\r\n\r\n  // Get videos from a specific topic table\r\n  static async getVideosByTopic(\r\n    topicTable: string,\r\n    limit: number = 20,\r\n    offset: number = 0\r\n  ): Promise<VideoSummary[]> {    try {\r\n      const { data, error } = await supabaseAdmin\r\n        .from(topicTable)\r\n        .select('*')\r\n        .order('published_at', { ascending: false })\r\n        .range(offset, offset + limit - 1)\r\n\r\n      if (error) throw error\r\n      return data || []\r\n    } catch (error) {\r\n      console.error(`Error fetching videos from ${topicTable}:`, error)\r\n      return []\r\n    }\r\n  }\r\n  // Get a single video by ID from a specific topic table or across all tables\r\n  static async getVideoById(topicNameFromUrl: string, videoId: string): Promise<VideoSummary | null> {\r\n    let video: VideoSummary | null = null;\r\n    let attemptedSpecificTable = false;\r\n    let specificTableQueryError: Error | unknown | null = null;\r\n\r\n    // Validate topicNameFromUrl and videoId\r\n    if (!videoId || typeof videoId !== 'string' || videoId.trim() === '') {\r\n      console.error(`getVideoById: Called with invalid videoId: '${videoId}'. Cannot fetch video.`);\r\n      return null;\r\n    }\r\n\r\n    if (topicNameFromUrl && typeof topicNameFromUrl === 'string' && topicNameFromUrl.trim() !== '') {\r\n      const actualTableName = topicNameFromUrl.startsWith('youtube_') \r\n        ? topicNameFromUrl \r\n        : `youtube_${topicNameFromUrl}`;\r\n      \r\n      console.log(`getVideoById: Attempting to fetch videoId '${videoId}' from specific table '${actualTableName}' (original topic: '${topicNameFromUrl}')`);\r\n      attemptedSpecificTable = true;\r\n\r\n      try {\r\n        const { data, error: supabaseError } = await supabaseAdmin\r\n          .from(actualTableName)\r\n          .select('*')\r\n          .eq('video_id', videoId)\r\n          .single();\r\n\r\n        if (supabaseError) {\r\n          specificTableQueryError = supabaseError; // Store error for logging\r\n          console.warn(`getVideoById: Supabase error fetching videoId '${videoId}' from table '${actualTableName}'. Code: ${supabaseError.code}, Message: ${supabaseError.message}. Will try searching all tables.`);\r\n          // Do not throw here, let it fall through to findVideoAcrossAllTables logic\r\n        } else if (data) {\r\n          console.log(`getVideoById: Successfully found videoId '${videoId}' in table '${actualTableName}'`);\r\n          const simpleTopicName = actualTableName.startsWith('youtube_') ? actualTableName.substring(8) : actualTableName;\r\n          // Ensure llm_response is at least an empty object if null/undefined from DB\r\n          video = { ...data, topic_category: simpleTopicName, llm_response: data.llm_response || {} };\r\n        } else {\r\n          // This case (no error, no data with .single()) should ideally not happen as .single() errors out.\r\n          console.warn(`getVideoById: No data and no error for videoId '${videoId}' from table '${actualTableName}'. This is unexpected with .single(). Will try searching all tables.`);\r\n        }\r\n      } catch (catchedError: unknown) { // Catch unexpected errors from the specific table query attempt\r\n        specificTableQueryError = catchedError;\r\n        const errorMessage = catchedError instanceof Error ? catchedError.message : String(catchedError);\r\n        console.error(`getVideoById: Exception during fetch from specific table '${actualTableName}' for videoId '${videoId}'. Error: ${errorMessage}. Will try searching all tables.`);\r\n      }\r\n    } else {\r\n      console.warn(`getVideoById: Called with invalid or empty topicNameFromUrl ('${topicNameFromUrl}'). Proceeding directly to search across all tables for videoId: '${videoId}'.`);\r\n    }\r\n\r\n    // If video not found in specific table (or specific table was not attempted/valid)\r\n    if (!video) {\r\n      if (attemptedSpecificTable) {\r\n        let errMessage = \"No specific error object\";\r\n        if (specificTableQueryError) {\r\n          if (specificTableQueryError instanceof Error) {\r\n            errMessage = specificTableQueryError.message;\r\n          } else {\r\n            try {\r\n              errMessage = JSON.stringify(specificTableQueryError);\r\n            } catch {\r\n              errMessage = \"Could not stringify error object\";\r\n            }\r\n          }\r\n        }\r\n        console.warn(`getVideoById: VideoId '${videoId}' not found in specific table or an error occurred. Specific error (if any): ${errMessage}`);\r\n      }\r\n      console.log(`getVideoById: Falling back to findVideoAcrossAllTables for videoId '${videoId}'.`);\r\n      try {\r\n        const videoFromFallback = await this.findVideoAcrossAllTables(videoId);\r\n        if (videoFromFallback) {\r\n          console.log(`getVideoById: Found videoId '${videoId}' via findVideoAcrossAllTables.`);\r\n          // Ensure llm_response is at least an empty object if null/undefined from DB\r\n          video = { ...videoFromFallback, llm_response: videoFromFallback.llm_response || {} };\r\n        } else {\r\n          console.error(`getVideoById: VideoId '${videoId}' ultimately NOT FOUND even after searching all tables.`);\r\n        }\r\n      } catch (fallbackError: unknown) {\r\n        const fallbackErrorMessage = fallbackError instanceof Error ? fallbackError.message : String(fallbackError);\r\n        console.error(`getVideoById: CRITICAL ERROR during findVideoAcrossAllTables for videoId '${videoId}'. Error: ${fallbackErrorMessage}`);\r\n        // video remains null\r\n      }\r\n    }\r\n    \r\n    if (!video) {\r\n         const specificErrorMsg = specificTableQueryError instanceof Error ? specificTableQueryError.message : (specificTableQueryError ? \"Error object present\" : \"N/A\");\r\n         console.error(`[FINAL RESULT] getVideoById for videoId '${videoId}' (original topic: '${topicNameFromUrl}'): Video NOT FOUND. Specific table attempt error (if any): ${specificErrorMsg}`);\r\n    } else {\r\n        // Ensure topic_category is sensible\r\n        if (!video.topic_category && topicNameFromUrl) {\r\n            video.topic_category = topicNameFromUrl.replace('youtube_', '');\r\n        } else if (!video.topic_category) {\r\n            video.topic_category = 'unknown'; // Default if no topic info at all\r\n        }\r\n    }\r\n    return video;\r\n  }\r\n\r\n  // Find a video by ID across all topic tables\r\n  static async findVideoAcrossAllTables(videoId: string): Promise<VideoSummary | null> {\r\n    console.log(`findVideoAcrossAllTables: Searching for videoId '${videoId}'`);\r\n    if (!videoId || typeof videoId !== 'string' || videoId.trim() === '') {\r\n      console.error(`findVideoAcrossAllTables: Called with invalid videoId: '${videoId}'.`);\r\n      return null;\r\n    }\r\n    try {\r\n      const topics = await this.getTopicTables();\r\n      if (!topics || topics.length === 0) {\r\n        console.warn(\"findVideoAcrossAllTables: No topic tables found to search in.\");\r\n        return null;\r\n      }\r\n      \r\n      for (const topic of topics) {\r\n        try {\r\n          const tableNameToQuery = topic.table_name;\r\n          if (!tableNameToQuery) {\r\n            console.warn(`findVideoAcrossAllTables: Topic '${topic.name}' has no valid table_name. Skipping.`);\r\n            continue;\r\n          }\r\n          console.log(`findVideoAcrossAllTables: Checking table '${tableNameToQuery}' for videoId '${videoId}'`);\r\n          const { data, error: supabaseError } = await supabaseAdmin\r\n            .from(tableNameToQuery)\r\n            .select('*')\r\n            .eq('video_id', videoId)\r\n            .single();\r\n\r\n          if (!supabaseError && data) {\r\n            console.log(`findVideoAcrossAllTables: Found videoId '${videoId}' in table '${tableNameToQuery}'`);\r\n            const simpleTopicName = topic.name.startsWith('youtube_') ? topic.name.substring(8) : topic.name;\r\n            // Ensure llm_response is at least an empty object if null/undefined from DB\r\n            return { ...data, topic_category: simpleTopicName, llm_response: data.llm_response || {} };\r\n          }\r\n          if (supabaseError && supabaseError.code !== 'PGRST116') { // PGRST116 means 0 rows, which is expected if not in this table\r\n            console.warn(`findVideoAcrossAllTables: Supabase error querying table '${tableNameToQuery}' for videoId '${videoId}'. Code: ${supabaseError.code}, Message: ${supabaseError.message}`);\r\n          }\r\n        } catch (tableQueryError: unknown) {\r\n          const tableQueryErrorMessage = tableQueryError instanceof Error ? tableQueryError.message : String(tableQueryError);\r\n          console.warn(`findVideoAcrossAllTables: Exception while querying table '${topic.table_name}' for videoId '${videoId}'. Message: ${tableQueryErrorMessage}`);\r\n        }\r\n      }\r\n      \r\n      console.log(`findVideoAcrossAllTables: VideoId '${videoId}' not found in any topic table after checking all.`);\r\n      return null;\r\n    } catch (error: unknown) {\r\n      const errorMessage = error instanceof Error ? error.message : String(error);\r\n      console.error(`findVideoAcrossAllTables: General error searching for videoId '${videoId}' across all tables. Message: ${errorMessage}`);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Search videos across all topic tables using vector similarity\r\n  static async searchVideos(\r\n    query: string,\r\n    topicFilters: string[] = [],\r\n    limit: number = 10\r\n  ): Promise<SearchResult[]> {\r\n    try {\r\n      // This will call our Python AI service to generate embeddings\r\n      const embeddingResponse = await fetch('/api/embeddings', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ text: query })\r\n      })\r\n      \r\n      if (!embeddingResponse.ok) throw new Error('Failed to generate embedding')\r\n      \r\n      const { embedding } = await embeddingResponse.json()\r\n\r\n      // Search across specified topic tables or all tables\r\n      const searchPromises = topicFilters.length > 0 \r\n        ? topicFilters.map(table => this.searchInTable(table, embedding, limit))\r\n        : await this.searchAllTables(embedding, limit)\r\n\r\n      const results = await Promise.all(searchPromises)\r\n      \r\n      // Flatten and sort by similarity score\r\n      return results\r\n        .flat()\r\n        .sort((a, b) => b.similarity_score - a.similarity_score)\r\n        .slice(0, limit)\r\n    } catch (error) {\r\n      console.error('Error searching videos:', error)\r\n      return []\r\n    }\r\n  }\r\n\r\n  private static async searchInTable(\r\n    tableName: string,\r\n    embedding: number[],\r\n    limit: number\r\n  ): Promise<SearchResult[]> {    try {\r\n      // Use pgvector similarity search\r\n      const { data, error } = await supabaseAdmin\r\n        .rpc('search_videos_by_embedding', {\r\n          table_name: tableName,\r\n          query_embedding: embedding,\r\n          match_threshold: 0.7,\r\n          match_count: limit\r\n        })\r\n\r\n      if (error) throw error\r\n      return (data || []).map((item: { video: VideoSummary; similarity_score: number; relevant_chunks: string[] }) => ({\r\n        video: item.video,\r\n        similarity_score: item.similarity_score,\r\n        relevant_transcript_chunks: item.relevant_chunks\r\n      }))\r\n    } catch (error) {\r\n      console.error(`Error searching in table ${tableName}:`, error)\r\n      return []\r\n    }\r\n  }\r\n\r\n  private static async searchAllTables(\r\n    embedding: number[],\r\n    limit: number\r\n  ): Promise<SearchResult[][]> {\r\n    const topics = await this.getTopicTables()\r\n    return Promise.all(\r\n      topics.map(topic => this.searchInTable(topic.name, embedding, limit))\r\n    )\r\n  }\r\n  // Get recent videos across all topics\r\n  static async getRecentVideos(limit: number = 20): Promise<VideoSummary[]> {\r\n    try {\r\n      console.log('🔄 getRecentVideos called with limit:', limit)\r\n      const topics = await this.getTopicTables()\r\n      console.log('🔄 Found topics:', topics.length, topics.map(t => t.table_name))\r\n      \r\n      const videoPromises = topics.map(topic => \r\n        this.getVideosByTopic(topic.table_name, Math.ceil(limit / topics.length)) // Use table_name\r\n      )\r\n      \r\n      const videoArrays = await Promise.all(videoPromises)\r\n      console.log('🔄 Video arrays lengths:', videoArrays.map(arr => arr.length))\r\n      \r\n      const allVideos = videoArrays.flat()\r\n      console.log('🔄 Total videos before sorting:', allVideos.length)\r\n      \r\n      // Sort by published date and return top results\r\n      const sortedVideos = allVideos\r\n        .sort((a, b) => {\r\n          const dateA = a.published_at ? new Date(a.published_at).getTime() : 0;\r\n          const dateB = b.published_at ? new Date(b.published_at).getTime() : 0;\r\n          return dateB - dateA;\r\n        })\r\n        .slice(0, limit)\r\n      \r\n      console.log('🔄 Final sorted videos:', sortedVideos.length)\r\n      return sortedVideos\r\n    } catch (error) {\r\n      console.error('Error fetching recent videos:', error)\r\n      return []\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM;IACX,4DAA4D;IAC5D,aAAa,iBAAwC;QACnD,IAAI;YACF,sEAAsE;YACtE,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,MAAM,iBAA+B,EAAE;YAEvC,QAAQ,GAAG,CAAC;YACZ,KAAK,MAAM,SAAS,cAAe;gBACjC,IAAI;oBACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACzC,IAAI,CAAC,OACL,MAAM,CAAC,MAAM;wBAAE,OAAO;wBAAS,MAAM;oBAAK,GAC1C,KAAK,CAAC;oBAET,IAAI,CAAC,OAAO;wBACV,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC;wBAC7D,eAAe,IAAI,CAAC;4BAClB,IAAI;4BACJ,YAAY;4BACZ,MAAM;4BACN,cAAc,IAAI,CAAC,iBAAiB,CAAC;4BACrC,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO,CAAC;4BACtD,aAAa,SAAS;4BACtB,YAAY,IAAI,OAAO,WAAW;4BAClC,YAAY,IAAI,OAAO,WAAW,GAAG,cAAc;wBACrD;oBACF;gBACF,EAAE,OAAM;gBACN,qCAAqC;gBACvC;YACF;YAEA,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,OAAO;YACT;YAEA,QAAQ,IAAI,CAAC;YACb,yCAAyC;YACzC,MAAM,eAAe,IAAI,OAAO,WAAW;YAC3C,OAAO;gBACL;oBAAE,IAAI;oBAAmC,YAAY;oBAAmC,MAAM;oBAAmC,cAAc;oBAA2B,aAAa;oBAAkC,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;gBAC5R;oBAAE,IAAI;oBAA0B,YAAY;oBAA0B,MAAM;oBAA0B,cAAc;oBAAkB,aAAa;oBAAyC,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;gBAC/P;oBAAE,IAAI;oBAAoB,YAAY;oBAAoB,MAAM;oBAAoB,cAAc;oBAAY,aAAa;oBAAuC,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;gBACrO;oBAAE,IAAI;oBAA6B,YAAY;oBAA6B,MAAM;oBAA6B,cAAc;oBAAqB,aAAa;oBAA6B,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;aAChQ;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,yDAAyD;YACzD,MAAM,eAAe,IAAI,OAAO,WAAW;YAC3C,OAAO;gBACL;oBAAE,IAAI;oBAAmC,YAAY;oBAAmC,MAAM;oBAAmC,cAAc;oBAA2B,aAAa;oBAAkC,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;gBAC5R;oBAAE,IAAI;oBAA0B,YAAY;oBAA0B,MAAM;oBAA0B,cAAc;oBAAkB,aAAa;oBAAyC,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;gBAC/P;oBAAE,IAAI;oBAAoB,YAAY;oBAAoB,MAAM;oBAAoB,cAAc;oBAAY,aAAa;oBAAuC,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;gBACrO;oBAAE,IAAI;oBAA6B,YAAY;oBAA6B,MAAM;oBAA6B,cAAc;oBAAqB,aAAa;oBAA6B,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;aAChQ;QACH;IACF;IAEA,wCAAwC;IACxC,OAAe,kBAAkB,SAAiB,EAAU;QAC1D,OAAO,UACJ,OAAO,CAAC,YAAY,IAAI,yBAAyB;SACjD,OAAO,CAAC,KAAK,KACb,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IACA,sDAAsD;IACtD,aAAa,YAAY,WAAsB,EAAqB;QAClE,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,iCAAiC;YACjC,MAAM,iBAAiB,MAAM,IAAI,CAAC,cAAc;YAEhD,iDAAiD;YACjD,MAAM,cAAc,eAAe,YAAY,MAAM,GAAG,IACpD,eAAe,MAAM,CAAC,CAAA;gBACpB,MAAM,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI;gBAC/E,OAAO,YAAY,QAAQ,CAAC,eAAe,YAAY,QAAQ,CAAC,EAAE,IAAI;YACxE,KACA;YAEJ,QAAQ,GAAG,CAAC,kCAAkC,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YACzE,QAAQ,GAAG,CAAC,yBAAyB;YACnC,uCAAuC;YACzC,KAAK,MAAM,SAAS,YAAa;gBAAS,IAAI;oBAC1C,oEAAoE;oBACpE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,MAAM,IAAI,EACf,MAAM,CAAC,gBACP,GAAG,CAAC,gBAAgB,MAAM,MAC1B,KAAK,CAAC;oBAET,IAAI,CAAC,SAAS,MAAM;wBAClB,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,MAAM,CAAC,oBAAoB,EAAE,MAAM,IAAI,EAAE;wBACnE,KAAK,OAAO,CAAC,CAAC;4BACZ,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,IAAI,IAAI;gCACjD,YAAY,GAAG,CAAC,KAAK,YAAY,CAAC,IAAI;4BACxC;wBACF;oBACF,OAAO,IAAI,OAAO;wBAChB,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,OAAO;oBAC3E;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,IAAI,CAAC,CAAC,sBAAsB,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;gBACrD,6BAA6B;gBAC/B;YACF;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,IAAI;YAC7C,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,SAAS,MAAM,EAAE;YAE7D,gFAAgF;YAChF,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,QAAQ,IAAI,CAAC;gBACb,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;YACH;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,yEAAyE;YACzE,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;IACF;IACA,aAAa,kBACX,WAAsB,EACtB,QAAmB,EACnB,QAAgB,EAAE,EAClB,SAAiB,CAAC,EAClB,SAAkB,EAClB,OAAgB,EACS;QACzB,IAAI;YACF,MAAM,YAA4B,EAAE;YACpC,MAAM,eAAe,IAAI,MAAc,wCAAwC;;YAE/E,6CAA6C;YAC7C,MAAM,gBAAgB,eAAe,YAAY,MAAM,GAAG,IACtD,cACA,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,GAAG,iBAAiB;YACzE,kFAAkF;YACpF,qFAAqF;YACrF,oEAAoE;YACpE,MAAM,aAAa,AAAC,YAAY,SAAS,MAAM,GAAG,KAAM,aAAa;YACrE,MAAM,aAAa,aAAa,IAAI,EAAE,kCAAkC;;YACxE,MAAM,gBAAgB,KAAK,GAAG,CAAC,CAAC,QAAQ,MAAM,IAAI,YAAY;YAE9D,mBAAmB;YACnB,KAAK,MAAM,aAAa,cAAe;gBACrC,IAAI;oBACF,IAAI,QAAQ,sHAAA,CAAA,gBAAa,CACtB,IAAI,CAAC,WACL,MAAM,CAAC,KACP,KAAK,CAAC,gBAAgB;wBAAE,WAAW;oBAAM;oBAE5C,iCAAiC;oBACjC,IAAI,YAAY,SAAS,MAAM,GAAG,GAAG;wBACnC,QAAQ,MAAM,EAAE,CAAC,gBAAgB;oBACnC;oBAEA,mCAAmC;oBACnC,IAAI,WAAW;wBACb,QAAQ,MAAM,GAAG,CAAC,gBAAgB;oBACpC;oBACA,IAAI,SAAS;wBACX,QAAQ,MAAM,GAAG,CAAC,gBAAgB;oBACpC;oBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,KAAK,CAAC;oBAE1C,IAAI,CAAC,SAAS,MAAM;wBAClB,+DAA+D;wBAC/D,KAAK,MAAM,SAAS,KAAM;4BACxB,IAAI,CAAC,aAAa,GAAG,CAAC,MAAM,QAAQ,GAAG;gCACrC,aAAa,GAAG,CAAC,MAAM,QAAQ;gCAC/B,UAAU,IAAI,CAAC;4BACjB;wBACF;oBACF;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,UAAU,CAAC,CAAC,EAAE;gBACzD,6BAA6B;gBAC/B;YACF;YAEA,wDAAwD;YACxD,MAAM,eAAe,UAAU,IAAI,CAAC,CAAC,GAAG;gBACtC,MAAM,QAAQ,EAAE,YAAY,GAAG,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;gBACpE,MAAM,QAAQ,EAAE,YAAY,GAAG,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;gBACpE,OAAO,QAAQ;YACjB;YAEA,mDAAmD;YACnD,OAAO,aAAa,KAAK,CAAC,QAAQ,SAAS;QAE7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO,EAAE;QACX;IACF;IAEA,yCAAyC;IACzC,aAAa,iBACX,UAAkB,EAClB,QAAgB,EAAE,EAClB,SAAiB,CAAC,EACO;QAAK,IAAI;YAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,gBAAgB;gBAAE,WAAW;YAAM,GACzC,KAAK,CAAC,QAAQ,SAAS,QAAQ;YAElC,IAAI,OAAO,MAAM;YACjB,OAAO,QAAQ,EAAE;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,WAAW,CAAC,CAAC,EAAE;YAC3D,OAAO,EAAE;QACX;IACF;IACA,4EAA4E;IAC5E,aAAa,aAAa,gBAAwB,EAAE,OAAe,EAAgC;QACjG,IAAI,QAA6B;QACjC,IAAI,yBAAyB;QAC7B,IAAI,0BAAkD;QAEtD,wCAAwC;QACxC,IAAI,CAAC,WAAW,OAAO,YAAY,YAAY,QAAQ,IAAI,OAAO,IAAI;YACpE,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,QAAQ,sBAAsB,CAAC;YAC5F,OAAO;QACT;QAEA,IAAI,oBAAoB,OAAO,qBAAqB,YAAY,iBAAiB,IAAI,OAAO,IAAI;YAC9F,MAAM,kBAAkB,iBAAiB,UAAU,CAAC,cAChD,mBACA,CAAC,QAAQ,EAAE,kBAAkB;YAEjC,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,QAAQ,uBAAuB,EAAE,gBAAgB,oBAAoB,EAAE,iBAAiB,EAAE,CAAC;YACrJ,yBAAyB;YAEzB,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACvD,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,YAAY,SACf,MAAM;gBAET,IAAI,eAAe;oBACjB,0BAA0B,eAAe,0BAA0B;oBACnE,QAAQ,IAAI,CAAC,CAAC,+CAA+C,EAAE,QAAQ,cAAc,EAAE,gBAAgB,SAAS,EAAE,cAAc,IAAI,CAAC,WAAW,EAAE,cAAc,OAAO,CAAC,gCAAgC,CAAC;gBACzM,2EAA2E;gBAC7E,OAAO,IAAI,MAAM;oBACf,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,QAAQ,YAAY,EAAE,gBAAgB,CAAC,CAAC;oBACjG,MAAM,kBAAkB,gBAAgB,UAAU,CAAC,cAAc,gBAAgB,SAAS,CAAC,KAAK;oBAChG,4EAA4E;oBAC5E,QAAQ;wBAAE,GAAG,IAAI;wBAAE,gBAAgB;wBAAiB,cAAc,KAAK,YAAY,IAAI,CAAC;oBAAE;gBAC5F,OAAO;oBACL,kGAAkG;oBAClG,QAAQ,IAAI,CAAC,CAAC,gDAAgD,EAAE,QAAQ,cAAc,EAAE,gBAAgB,oEAAoE,CAAC;gBAC/K;YACF,EAAE,OAAO,cAAuB;gBAC9B,0BAA0B;gBAC1B,MAAM,eAAe,wBAAwB,QAAQ,aAAa,OAAO,GAAG,OAAO;gBACnF,QAAQ,KAAK,CAAC,CAAC,0DAA0D,EAAE,gBAAgB,eAAe,EAAE,QAAQ,UAAU,EAAE,aAAa,gCAAgC,CAAC;YAChL;QACF,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,8DAA8D,EAAE,iBAAiB,kEAAkE,EAAE,QAAQ,EAAE,CAAC;QAChL;QAEA,mFAAmF;QACnF,IAAI,CAAC,OAAO;YACV,IAAI,wBAAwB;gBAC1B,IAAI,aAAa;gBACjB,IAAI,yBAAyB;oBAC3B,IAAI,mCAAmC,OAAO;wBAC5C,aAAa,wBAAwB,OAAO;oBAC9C,OAAO;wBACL,IAAI;4BACF,aAAa,KAAK,SAAS,CAAC;wBAC9B,EAAE,OAAM;4BACN,aAAa;wBACf;oBACF;gBACF;gBACA,QAAQ,IAAI,CAAC,CAAC,uBAAuB,EAAE,QAAQ,6EAA6E,EAAE,YAAY;YAC5I;YACA,QAAQ,GAAG,CAAC,CAAC,oEAAoE,EAAE,QAAQ,EAAE,CAAC;YAC9F,IAAI;gBACF,MAAM,oBAAoB,MAAM,IAAI,CAAC,wBAAwB,CAAC;gBAC9D,IAAI,mBAAmB;oBACrB,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,QAAQ,+BAA+B,CAAC;oBACpF,4EAA4E;oBAC5E,QAAQ;wBAAE,GAAG,iBAAiB;wBAAE,cAAc,kBAAkB,YAAY,IAAI,CAAC;oBAAE;gBACrF,OAAO;oBACL,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,QAAQ,uDAAuD,CAAC;gBAC1G;YACF,EAAE,OAAO,eAAwB;gBAC/B,MAAM,uBAAuB,yBAAyB,QAAQ,cAAc,OAAO,GAAG,OAAO;gBAC7F,QAAQ,KAAK,CAAC,CAAC,0EAA0E,EAAE,QAAQ,UAAU,EAAE,sBAAsB;YACrI,qBAAqB;YACvB;QACF;QAEA,IAAI,CAAC,OAAO;YACP,MAAM,mBAAmB,mCAAmC,QAAQ,wBAAwB,OAAO,GAAI,0BAA0B,yBAAyB;YAC1J,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,QAAQ,oBAAoB,EAAE,iBAAiB,4DAA4D,EAAE,kBAAkB;QAC9L,OAAO;YACH,oCAAoC;YACpC,IAAI,CAAC,MAAM,cAAc,IAAI,kBAAkB;gBAC3C,MAAM,cAAc,GAAG,iBAAiB,OAAO,CAAC,YAAY;YAChE,OAAO,IAAI,CAAC,MAAM,cAAc,EAAE;gBAC9B,MAAM,cAAc,GAAG,WAAW,kCAAkC;YACxE;QACJ;QACA,OAAO;IACT;IAEA,6CAA6C;IAC7C,aAAa,yBAAyB,OAAe,EAAgC;QACnF,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,QAAQ,CAAC,CAAC;QAC1E,IAAI,CAAC,WAAW,OAAO,YAAY,YAAY,QAAQ,IAAI,OAAO,IAAI;YACpE,QAAQ,KAAK,CAAC,CAAC,wDAAwD,EAAE,QAAQ,EAAE,CAAC;YACpF,OAAO;QACT;QACA,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc;YACxC,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAAG;gBAClC,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;YAEA,KAAK,MAAM,SAAS,OAAQ;gBAC1B,IAAI;oBACF,MAAM,mBAAmB,MAAM,UAAU;oBACzC,IAAI,CAAC,kBAAkB;wBACrB,QAAQ,IAAI,CAAC,CAAC,iCAAiC,EAAE,MAAM,IAAI,CAAC,oCAAoC,CAAC;wBACjG;oBACF;oBACA,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,iBAAiB,eAAe,EAAE,QAAQ,CAAC,CAAC;oBACrG,MAAM,EAAE,IAAI,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACvD,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,EAAE,CAAC,YAAY,SACf,MAAM;oBAET,IAAI,CAAC,iBAAiB,MAAM;wBAC1B,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,QAAQ,YAAY,EAAE,iBAAiB,CAAC,CAAC;wBACjG,MAAM,kBAAkB,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM,IAAI;wBAChG,4EAA4E;wBAC5E,OAAO;4BAAE,GAAG,IAAI;4BAAE,gBAAgB;4BAAiB,cAAc,KAAK,YAAY,IAAI,CAAC;wBAAE;oBAC3F;oBACA,IAAI,iBAAiB,cAAc,IAAI,KAAK,YAAY;wBACtD,QAAQ,IAAI,CAAC,CAAC,yDAAyD,EAAE,iBAAiB,eAAe,EAAE,QAAQ,SAAS,EAAE,cAAc,IAAI,CAAC,WAAW,EAAE,cAAc,OAAO,EAAE;oBACvL;gBACF,EAAE,OAAO,iBAA0B;oBACjC,MAAM,yBAAyB,2BAA2B,QAAQ,gBAAgB,OAAO,GAAG,OAAO;oBACnG,QAAQ,IAAI,CAAC,CAAC,0DAA0D,EAAE,MAAM,UAAU,CAAC,eAAe,EAAE,QAAQ,YAAY,EAAE,wBAAwB;gBAC5J;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ,kDAAkD,CAAC;YAC7G,OAAO;QACT,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;YACrE,QAAQ,KAAK,CAAC,CAAC,+DAA+D,EAAE,QAAQ,8BAA8B,EAAE,cAAc;YACtI,OAAO;QACT;IACF;IAEA,gEAAgE;IAChE,aAAa,aACX,KAAa,EACb,eAAyB,EAAE,EAC3B,QAAgB,EAAE,EACO;QACzB,IAAI;YACF,8DAA8D;YAC9D,MAAM,oBAAoB,MAAM,MAAM,mBAAmB;gBACvD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;gBAAM;YACrC;YAEA,IAAI,CAAC,kBAAkB,EAAE,EAAE,MAAM,IAAI,MAAM;YAE3C,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,kBAAkB,IAAI;YAElD,qDAAqD;YACrD,MAAM,iBAAiB,aAAa,MAAM,GAAG,IACzC,aAAa,GAAG,CAAC,CAAA,QAAS,IAAI,CAAC,aAAa,CAAC,OAAO,WAAW,UAC/D,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW;YAE1C,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAElC,uCAAuC;YACvC,OAAO,QACJ,IAAI,GACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,gBAAgB,GAAG,EAAE,gBAAgB,EACtD,KAAK,CAAC,GAAG;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;IACF;IAEA,aAAqB,cACnB,SAAiB,EACjB,SAAmB,EACnB,KAAa,EACY;QAAK,IAAI;YAChC,iCAAiC;YACjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACxC,GAAG,CAAC,8BAA8B;gBACjC,YAAY;gBACZ,iBAAiB;gBACjB,iBAAiB;gBACjB,aAAa;YACf;YAEF,IAAI,OAAO,MAAM;YACjB,OAAO,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC,OAAuF,CAAC;oBAC/G,OAAO,KAAK,KAAK;oBACjB,kBAAkB,KAAK,gBAAgB;oBACvC,4BAA4B,KAAK,eAAe;gBAClD,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAC,EAAE;YACxD,OAAO,EAAE;QACX;IACF;IAEA,aAAqB,gBACnB,SAAmB,EACnB,KAAa,EACc;QAC3B,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc;QACxC,OAAO,QAAQ,GAAG,CAChB,OAAO,GAAG,CAAC,CAAA,QAAS,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,EAAE,WAAW;IAElE;IACA,sCAAsC;IACtC,aAAa,gBAAgB,QAAgB,EAAE,EAA2B;QACxE,IAAI;YACF,QAAQ,GAAG,CAAC,yCAAyC;YACrD,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc;YACxC,QAAQ,GAAG,CAAC,oBAAoB,OAAO,MAAM,EAAE,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU;YAE3E,MAAM,gBAAgB,OAAO,GAAG,CAAC,CAAA,QAC/B,IAAI,CAAC,gBAAgB,CAAC,MAAM,UAAU,EAAE,KAAK,IAAI,CAAC,QAAQ,OAAO,MAAM,GAAG,iBAAiB;;YAG7F,MAAM,cAAc,MAAM,QAAQ,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,4BAA4B,YAAY,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM;YAEzE,MAAM,YAAY,YAAY,IAAI;YAClC,QAAQ,GAAG,CAAC,mCAAmC,UAAU,MAAM;YAE/D,gDAAgD;YAChD,MAAM,eAAe,UAClB,IAAI,CAAC,CAAC,GAAG;gBACR,MAAM,QAAQ,EAAE,YAAY,GAAG,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;gBACpE,MAAM,QAAQ,EAAE,YAAY,GAAG,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;gBACpE,OAAO,QAAQ;YACjB,GACC,KAAK,CAAC,GAAG;YAEZ,QAAQ,GAAG,CAAC,2BAA2B,aAAa,MAAM;YAC1D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,EAAE;QACX;IACF;AACF", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport function formatDuration(duration: string | number): string {\r\n  // Handle number input (duration in seconds)\r\n  if (typeof duration === 'number') {\r\n    const hours = Math.floor(duration / 3600)\r\n    const minutes = Math.floor((duration % 3600) / 60)\r\n    const seconds = duration % 60\r\n\r\n    if (hours > 0) {\r\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`\r\n    }\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`\r\n  }\r\n\r\n  // Handle string input (YouTube duration format PT4M13S)\r\n  if (typeof duration === 'string') {\r\n    const match = duration.match(/PT(\\d+H)?(\\d+M)?(\\d+S)?/)\r\n    if (!match) return duration\r\n\r\n    const hours = match[1] ? parseInt(match[1].replace('H', '')) : 0\r\n    const minutes = match[2] ? parseInt(match[2].replace('M', '')) : 0\r\n    const seconds = match[3] ? parseInt(match[3].replace('S', '')) : 0\r\n\r\n    if (hours > 0) {\r\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`\r\n    }\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`\r\n  }\r\n\r\n  // Fallback for unexpected input types\r\n  return String(duration)\r\n}\r\n\r\nexport function formatViewCount(count: number): string {\r\n  if (count >= 1000000) {\r\n    return `${(count / 1000000).toFixed(1)}M views`\r\n  } else if (count >= 1000) {\r\n    return `${(count / 1000).toFixed(1)}K views`\r\n  }\r\n  return `${count} views`\r\n}\r\n\r\nexport function formatDate(dateString: string | null | undefined): string {\r\n  if (!dateString || dateString === 'null' || dateString === 'undefined') {\r\n    // Handle NULL/empty dates - these are considered \"older\" videos\r\n    return \"Older video\";\r\n  }\r\n\r\n  const date = new Date(dateString);\r\n\r\n  if (isNaN(date.getTime())) {\r\n    console.warn(`formatDate: Invalid date string received: \"${dateString}\"`);\r\n    return `Invalid date: ${dateString.substring(0, 20)}${dateString.length > 20 ? '...' : ''}`;\r\n  }\r\n\r\n  // Return the actual publication date instead of relative time\r\n  // Format: \"MMM DD, YYYY\" (e.g., \"Dec 15, 2024\")\r\n  return date.toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric'\r\n  });\r\n}\r\n\r\nexport function truncateText(text: string | null | undefined, maxLength: number): string {\r\n  if (!text) return ''\r\n  if (text.length <= maxLength) return text\r\n  return text.slice(0, maxLength) + '...'\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,QAAyB;IACtD,4CAA4C;IAC5C,IAAI,OAAO,aAAa,UAAU;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,WAAW;QACpC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,WAAW,OAAQ;QAC/C,MAAM,UAAU,WAAW;QAE3B,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QACjG;QACA,OAAO,GAAG,QAAQ,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC5D;IAEA,wDAAwD;IACxD,IAAI,OAAO,aAAa,UAAU;QAChC,MAAM,QAAQ,SAAS,KAAK,CAAC;QAC7B,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,QAAQ,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,OAAO;QAC/D,MAAM,UAAU,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,OAAO;QACjE,MAAM,UAAU,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,OAAO;QAEjE,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QACjG;QACA,OAAO,GAAG,QAAQ,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC5D;IAEA,sCAAsC;IACtC,OAAO,OAAO;AAChB;AAEO,SAAS,gBAAgB,KAAa;IAC3C,IAAI,SAAS,SAAS;QACpB,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;IACjD,OAAO,IAAI,SAAS,MAAM;QACxB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;IAC9C;IACA,OAAO,GAAG,MAAM,MAAM,CAAC;AACzB;AAEO,SAAS,WAAW,UAAqC;IAC9D,IAAI,CAAC,cAAc,eAAe,UAAU,eAAe,aAAa;QACtE,gEAAgE;QAChE,OAAO;IACT;IAEA,MAAM,OAAO,IAAI,KAAK;IAEtB,IAAI,MAAM,KAAK,OAAO,KAAK;QACzB,QAAQ,IAAI,CAAC,CAAC,2CAA2C,EAAE,WAAW,CAAC,CAAC;QACxE,OAAO,CAAC,cAAc,EAAE,WAAW,SAAS,CAAC,GAAG,MAAM,WAAW,MAAM,GAAG,KAAK,QAAQ,IAAI;IAC7F;IAEA,8DAA8D;IAC9D,gDAAgD;IAChD,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,aAAa,IAA+B,EAAE,SAAiB;IAC7E,IAAI,CAAC,MAAM,OAAO;IAClB,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/components/CopyButtons.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CategoryCopyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call CategoryCopyButton() from the server but CategoryCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/CopyButtons.tsx <module evaluation>\",\n    \"CategoryCopyButton\",\n);\nexport const FieldCopyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call FieldCopyButton() from the server but FieldCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/CopyButtons.tsx <module evaluation>\",\n    \"FieldCopyButton\",\n);\nexport const SummaryCopyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call Summary<PERSON><PERSON><PERSON>utton() from the server but <PERSON>mmaryCopy<PERSON>utton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/CopyButtons.tsx <module evaluation>\",\n    \"SummaryCopyButton\",\n);\nexport const TranscriptCopyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call TranscriptCopyButton() from the server but TranscriptCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/CopyButtons.tsx <module evaluation>\",\n    \"TranscriptCopyButton\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,gEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gEACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,gEACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,gEACA", "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/components/CopyButtons.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CategoryCopyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call CategoryCopyButton() from the server but CategoryCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/CopyButtons.tsx\",\n    \"CategoryCopyButton\",\n);\nexport const FieldCopyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call FieldCopyButton() from the server but FieldCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/CopyButtons.tsx\",\n    \"FieldCopyButton\",\n);\nexport const SummaryCopyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call Summary<PERSON>opyButton() from the server but Summa<PERSON><PERSON><PERSON><PERSON>utton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/CopyButtons.tsx\",\n    \"SummaryCopyButton\",\n);\nexport const TranscriptCopyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call TranscriptCopyButton() from the server but TranscriptCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/CopyButtons.tsx\",\n    \"TranscriptCopyButton\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,4CACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,4CACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,4CACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,4CACA", "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 713, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/app/video/%5Btopic%5D/%5BvideoId%5D/page.tsx"], "sourcesContent": ["import { DatabaseService } from \"@/lib/database\";\r\nimport { \r\n    ExternalLinkIcon, \r\n    EyeIcon, \r\n    CalendarDaysIcon as Calendar, \r\n    ClockIcon as Clock,       \r\n    PlayCircleIcon as Play, \r\n    MessageCircleIcon as MessageCircle \r\n} from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { notFound } from \"next/navigation\";\r\nimport { formatDuration, formatViewCount, formatDate, truncateText } from \"@/lib/utils\";\r\nimport { Metadata } from 'next';\r\nimport { CategoryCopyButton, SummaryCopyButton, TranscriptCopyButton } from '@/components/CopyButtons'; \r\n\r\ninterface VideoPageProps {\r\n  params: { // No longer a Promise here, Next.js resolves it\r\n    topic: string\r\n    videoId: string\r\n  }\r\n}\r\n\r\n// Helper to organize and render LLM response fields\r\nconst renderLlmResponse = (llmResponse: Record<string, unknown> | null) => {\r\n  if (!llmResponse || Object.keys(llmResponse).length === 0) {\r\n    return <p className=\"text-gray-500 dark:text-gray-400\">No detailed summary information available.</p>;\r\n  }\r\n  // Define categories and their priority fields\r\n  const categories: Record<string, string[]> = {\r\n    \"Summary\": ['summary', 'short_summary', 'tldr', 'overview'],\r\n    \"Key Points\": ['insights', 'key_insights', 'main_points', 'key_takeaways', 'key_points', 'important_points', 'highlights', 'Insights'],\r\n    \"Topics\": ['keywords', 'topics', 'key_topics', 'subject_areas', 'categories', 'themes', 'important_concepts', 'Keywords'],\r\n    \"Questions\": ['questions', 'key_questions', 'questions_answered', 'faqs', 'common_questions', 'Questions'],\r\n    \"Recommendations\": ['recommendations', 'suggestions', 'advice', 'next_steps', 'Recommendations'],\r\n    \"Analysis\": ['analysis', 'conclusion', 'evaluation', 'perspective', 'sentiment', 'sentiment_analysis'],\r\n    \"Technical Details\": ['technical_details', 'specifications', 'methodology', 'technical_concepts'],\r\n    \"Other\": [] \r\n  };\r\n  \r\n  const availableKeys = Object.keys(llmResponse);\r\n  const organizedData: Record<string, Array<[string, unknown]>> = {};\r\n  \r\n  Object.keys(categories).forEach(category => {\r\n    organizedData[category] = [];\r\n  });\r\n  \r\n  // Ensure llmResponse is not null before processing\r\n  if (llmResponse) {\r\n    availableKeys.forEach(key => {\r\n      if (['original_transcript_summary', 'title', 'channel_name'].includes(key)) {\r\n        return;\r\n      }\r\n      let assigned = false; // Correctly scoped variable\r\n      for (const [category, fields] of Object.entries(categories)) {\r\n        if (fields.includes(key)) {\r\n          organizedData[category].push([key, llmResponse[key]]);\r\n          assigned = true;\r\n          break;\r\n        }\r\n      }\r\n      if (!assigned) {\r\n        organizedData[\"Other\"].push([key, llmResponse[key]]);\r\n      }\r\n    });\r\n  }\r\n  // Format field names nicely\r\n  const formatFieldName = (key: string) => {\r\n    return key\r\n      .replace(/_/g, ' ')\r\n      .split(' ')\r\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n      .join(' ');\r\n  };\r\n\r\n  const formatFieldValue = (key: string, value: unknown) => {\r\n    if (value === null || value === undefined) return 'N/A';    if ((key.toLowerCase().includes('keyword') || key.toLowerCase().includes('topic') || key === 'tags' || key === 'Keywords') && Array.isArray(value)) {\r\n      return (\r\n        <div className=\"flex flex-wrap gap-2\">\r\n          {value.map((item, idx) => (\r\n            <span key={idx} className=\"bg-blue-50 text-blue-800 dark:bg-blue-900 dark:text-blue-200 px-2 py-1 rounded-full text-lg font-medium\">\r\n              {typeof item === 'object' ? JSON.stringify(item) : String(item)}\r\n            </span>\r\n          ))}\r\n        </div>\r\n      );\r\n    }    if ((key.toLowerCase().includes('insight') || key.toLowerCase().includes('recommendation') || key.toLowerCase().includes('question') || key === 'Insights' || key === 'Recommendations' || key === 'Questions') && Array.isArray(value)) {\r\n      return (\r\n        <ul className=\"space-y-2 pl-0\">\r\n          {value.map((item, idx) => (\r\n            <li key={idx} className=\"bg-gray-50 dark:bg-gray-800 p-3 rounded-md border-l-2 border-blue-500 dark:border-blue-700\">\r\n              <div className=\"text-gray-800 dark:text-gray-200 text-lg\">\r\n                {typeof item === 'object' ? JSON.stringify(item, null, 2) : String(item)}\r\n              </div>\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      );\r\n    }    if (Array.isArray(value)) {\r\n      return (\r\n        <ul className=\"list-disc pl-5 space-y-1\">\r\n          {value.map((item, idx) => (\r\n            <li key={idx} className=\"text-gray-800 dark:text-gray-200 text-lg\">\r\n              {typeof item === 'object' ? JSON.stringify(item, null, 2) : String(item)}\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      );\r\n    }    if (typeof value === 'object') {\r\n      return (\r\n        <pre className=\"bg-gray-50 dark:bg-gray-800 p-2 rounded-md overflow-auto text-lg\">\r\n          {JSON.stringify(value, null, 2)}\r\n        </pre>\r\n      );\r\n    }\r\n    if (['key_insights', 'insights', 'main_points', 'key_takeaways', 'Insights'].includes(key) && typeof value === 'string') {\r\n      if (value.includes(\"- \") || value.includes(\"• \")) {\r\n        const bulletPoints = value.split(/\\n\\s*[-•]\\s+/);        return (\r\n          <ul className=\"space-y-2 pl-0\">\r\n            {bulletPoints.filter(Boolean).map((point, idx) => (\r\n              <li key={idx} className=\"bg-gray-50 dark:bg-gray-800 p-3 rounded-md border-l-2 border-blue-500 dark:border-blue-700\">\r\n                <div className=\"text-gray-800 dark:text-gray-200 text-lg\">{point.trim()}</div>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        );\r\n      }\r\n    }\r\n    if ((key.toLowerCase().includes('question') || key.toLowerCase().includes('faq') || key === 'Questions') && typeof value === 'string') {\r\n      if (value.includes(\"\\n\")) {\r\n        const questions = value.split(/\\n+/).filter(Boolean);        return (\r\n          <ul className=\"space-y-2 pl-0\">\r\n            {questions.map((question, idx) => (\r\n              <li key={idx} className=\"bg-gray-50 dark:bg-gray-800 p-3 rounded-md border-l-2 border-purple-500 dark:border-purple-700\">\r\n                <div className=\"text-gray-800 dark:text-gray-200 text-lg\">{question.trim()}</div>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        );\r\n      }\r\n    }    if (['key_insights', 'insights', 'summary', 'tldr', 'key_takeaways', 'main_points', 'recommendations', 'Insights', 'Recommendations'].includes(key)) {\r\n      return <div className=\"font-medium text-gray-900 dark:text-gray-100 text-lg\">{String(value)}</div>;\r\n    }\r\n    return <span className=\"text-gray-800 dark:text-gray-200 text-lg\">{String(value)}</span>;\r\n  };\r\n  \r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {Object.entries(organizedData).map(([category, fields]) => {\r\n        if (fields.length === 0) return null;\r\n        return (          <div key={category} className=\"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow transition-shadow duration-200\">\r\n            <div className=\"bg-gray-100 dark:bg-gray-800 px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center\">\r\n              <h3 className=\"font-medium text-gray-900 dark:text-gray-100 text-xl\">{category}</h3>\r\n              {(category === \"Key Points\" || category === \"Recommendations\" || category === \"Topics\" || category === \"Questions\") && fields.length > 0 && (\r\n                <CategoryCopyButton \r\n                  category={category} \r\n                  fields={fields} \r\n                />\r\n              )}\r\n            </div>            <dl className=\"divide-y divide-gray-200 dark:divide-gray-800\">\r\n              {fields.map(([key, value]) => (\r\n                <div key={key} className=\"px-4 py-3 group\">\r\n                  <dt className=\"text-lg font-medium text-gray-500 dark:text-gray-400\">{formatFieldName(key)}</dt>\r\n                  <dd className=\"mt-1 text-lg text-gray-800 dark:text-gray-200 whitespace-pre-wrap\">\r\n                    {formatFieldValue(key, value)}\r\n                  </dd>\r\n                </div>\r\n              ))}\r\n            </dl>\r\n          </div>\r\n        );\r\n      })}\r\n    </div>\r\n  );\r\n}\r\n\r\n\r\nexport default async function VideoPage({ params }: VideoPageProps) {\r\n  // In Next.js 14/15, using await with params is now required for dynamic path segments\r\n  const topic = String(await Promise.resolve(params.topic));\r\n  const videoId = String(await Promise.resolve(params.videoId));\r\n  const video = await DatabaseService.getVideoById(topic, videoId);\r\n\r\n  if (!video) {\r\n    notFound();\r\n  }\r\n\r\n  const youtubeEmbedUrl = video.video_id ? `https://www.youtube.com/embed/${video.video_id}` : null;\r\n\r\n  return (\r\n    <div className=\"p-4 sm:p-6 lg:p-8\"> {/* Removed max-w-6xl and mx-auto, kept padding */}\r\n      {/* Top Section: Header Info (Left) + Video Player (Right) */}\r\n      <div className=\"flex flex-col md:flex-row md:items-start gap-6 lg:gap-8 mb-8\">\r\n        {/* Left part: Header Content (Title, breadcrumbs, metadata, actions) */}\r\n        <div className=\"flex-grow space-y-4 md:pr-6 lg:pr-8\">\r\n          {/* Breadcrumbs */}\r\n          <div className=\"flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400\">\r\n            <Link href=\"/\" className=\"hover:text-primary dark:hover:text-primary-light\">Home</Link>\r\n            <span>/</span>\r\n            <span className=\"bg-muted text-muted-foreground px-2 py-1 rounded-full text-xs\">\r\n              {video.topic_category || 'Uncategorized'}\r\n            </span>\r\n          </div>\r\n          \r\n          <h1 className=\"text-3xl font-bold text-foreground leading-tight\">\r\n            {video.title}\r\n          </h1>\r\n          \r\n          <div className=\"flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-muted-foreground\">\r\n            <span className=\"font-medium\">{video.channel_name}</span>\r\n            {video.view_count !== null && video.view_count !== undefined && (\r\n              <div className=\"flex items-center gap-1\">\r\n                <EyeIcon className=\"w-4 h-4\" />\r\n                {formatViewCount(video.view_count)}\r\n              </div>\r\n            )}\r\n            <div className=\"flex items-center gap-1\">\r\n              <Calendar className=\"w-4 h-4\" />\r\n              {/* Ensure published_at is used */}\r\n              {formatDate(video.published_at)}\r\n            </div>\r\n            {video.duration && (\r\n              <div className=\"flex items-center gap-1\">\r\n                <Clock className=\"w-4 h-4\" />\r\n                {formatDuration(video.duration)}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Actions - Moved here */}\r\n          <div className=\"flex flex-wrap gap-3 mt-4\">\r\n            {video.video_url && (\r\n              <Link \r\n                href={video.video_url} \r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\"\r\n              >\r\n                <Play className=\"w-4 h-4 mr-2\" />\r\n                Watch on YouTube\r\n              </Link>\r\n            )}\r\n            {video.channel_id && (\r\n              <Link \r\n                href={`https://www.youtube.com/channel/${video.channel_id}`} \r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"inline-flex items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\"\r\n              >\r\n                <ExternalLinkIcon className=\"w-4 h-4 mr-2\" />\r\n                View Channel\r\n              </Link>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right part: Video Player (Smaller) */}\r\n        <div className=\"w-full md:w-[320px] lg:w-[400px] xl:w-[480px] flex-shrink-0 self-start md:sticky md:top-6\">\r\n          {youtubeEmbedUrl ? (\r\n            <div className=\"aspect-video overflow-hidden rounded-lg border border-border dark:border-gray-700 relative shadow-lg\">\r\n              <iframe\r\n                width=\"100%\"\r\n                height=\"100%\"\r\n                src={youtubeEmbedUrl}\r\n                title={video.title ?? \"YouTube video player\"}\r\n                frameBorder=\"0\"\r\n                allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\r\n                allowFullScreen\r\n                className=\"rounded-lg\"\r\n              ></iframe>\r\n            </div>\r\n          ) : (\r\n             <div className=\"aspect-video overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 relative bg-muted flex items-center justify-center shadow-lg\">\r\n                <Play className=\"w-16 h-16 text-muted-foreground\" />\r\n                <p className=\"ml-2 text-muted-foreground\">Video not available</p>\r\n             </div>\r\n          )}\r\n        </div>\r\n      </div> {/* End of Top Section */}\r\n\r\n      {/* Main Content Section: AI Analysis and Transcript */}\r\n      <div className=\"w-full space-y-8\"> {/* Added w-full here */}\r\n        {/* AI Analysis Section */}\r\n        <div>\r\n          <div className=\"flex items-center justify-between mb-3\">\r\n            <h2 className=\"text-2xl font-semibold flex items-center text-foreground\">\r\n              <MessageCircle className=\"w-6 h-6 mr-2\" /> AI Analysis\r\n            </h2>\r\n            {/* Placeholder for any actions related to AI Analysis section */}\r\n          </div>\r\n          {/* Removed the bg-card styling from this div, it will now inherit the background */}\r\n          <div>\r\n            {/* Quick Summary */}\r\n            {video.llm_response && (\r\n              typeof video.llm_response.tldr === 'string' || \r\n              typeof video.llm_response.short_summary === 'string' ||\r\n              typeof video.llm_response.summary === 'string'\r\n            ) && (              <div className=\"mb-6 p-4 bg-primary/10 rounded-lg border border-primary/20 shadow-sm\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <h3 className=\"font-medium text-primary mb-2 text-2xl\">Quick Summary</h3>\r\n                  <SummaryCopyButton \r\n                    text={typeof video.llm_response?.tldr === 'string' \r\n                      ? video.llm_response.tldr \r\n                      : typeof video.llm_response?.short_summary === 'string' \r\n                        ? video.llm_response.short_summary \r\n                        : typeof video.llm_response?.summary === 'string'\r\n                          ? video.llm_response.summary\r\n                          : 'No summary available'\r\n                    }\r\n                  />\r\n                </div>\r\n                <p className=\"text-foreground font-medium text-lg\">\r\n                  {(typeof video.llm_response.tldr === 'string' \r\n                    ? video.llm_response.tldr \r\n                    : typeof video.llm_response.short_summary === 'string' \r\n                      ? video.llm_response.short_summary \r\n                      : typeof video.llm_response.summary === 'string'\r\n                        ? video.llm_response.summary\r\n                        : 'No summary available'\r\n                  )}\r\n                </p> {/* Added closing p tag here */}\r\n            </div>\r\n            )}\r\n            {/* Full LLM Response data */}\r\n            {renderLlmResponse(video.llm_response)}\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Transcript Section */}\r\n        {video.transcript && (\r\n          <div>\r\n            <div className=\"flex items-center justify-between mb-2\">\r\n              <h2 className=\"text-2xl font-semibold flex items-center text-foreground\">\r\n                Transcript\r\n              </h2>\r\n              <TranscriptCopyButton transcript={video.transcript || ''} />\r\n            </div>            <details className=\"mt-1 group\">\r\n              <summary className=\"cursor-pointer font-medium text-primary hover:underline flex items-center text-2xl\">\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-1 transform transition-transform group-open:rotate-90\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\r\n                </svg>\r\n                View Full Transcript\r\n              </summary>\r\n              <div className=\"mt-2 p-4 bg-muted rounded-lg shadow max-h-96 overflow-y-auto border border-border\">\r\n                <p className=\"text-muted-foreground whitespace-pre-wrap text-lg leading-relaxed\">{video.transcript}</p>\r\n              </div>\r\n            </details>\r\n          </div>\r\n        )}\r\n      </div> {/* End of Main Content Section */}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport async function generateMetadata({ params }: { params: { topic: string; videoId: string } }): Promise<Metadata> {\r\n  // In Next.js 14/15, using await with params is now required for dynamic path segments\r\n  const topic = String(await Promise.resolve(params.topic));\r\n  const videoId = String(await Promise.resolve(params.videoId));\r\n  const video = await DatabaseService.getVideoById(topic, videoId);\r\n\r\n  if (!video) {\r\n    return {\r\n      title: 'Video Not Found',\r\n      description: 'This video could not be found or is not available.',\r\n    };\r\n  }\r\n\r\n  // Get the best summary from the JSONB data\r\n  let metaDescription = 'No summary available.';\r\n  let keywords: string[] = [];\r\n  \r\n  if (video.llm_response) {\r\n    // Find the best summary source\r\n    if (typeof video.llm_response.tldr === 'string') {\r\n      metaDescription = video.llm_response.tldr;\r\n    } else if (typeof video.llm_response.short_summary === 'string') {\r\n      metaDescription = video.llm_response.short_summary;\r\n    } else if (typeof video.llm_response.summary === 'string') {\r\n      metaDescription = video.llm_response.summary;\r\n    }\r\n      // Extract keywords if available (handle both lowercase and capitalized keys)\r\n    if (Array.isArray(video.llm_response.keywords)) {\r\n      keywords = video.llm_response.keywords.map((k: unknown) => String(k));\r\n    } else if (Array.isArray(video.llm_response.Keywords)) {\r\n      keywords = video.llm_response.Keywords.map((k: unknown) => String(k));\r\n    } else if (Array.isArray(video.llm_response.topics)) {\r\n      keywords = video.llm_response.topics.map((k: unknown) => String(k));\r\n    } else if (typeof video.llm_response.keywords === 'string') {\r\n      // Try to parse keywords from string (comma separated)\r\n      keywords = video.llm_response.keywords.split(/,\\\\s*/);\r\n    } else if (typeof video.llm_response.Keywords === 'string') {\r\n      // Try to parse keywords from string (comma separated)\r\n      keywords = video.llm_response.Keywords.split(/,\\\\s*/);\r\n    }\r\n  }\r\n  \r\n  // Limit description length\r\n  metaDescription = truncateText(metaDescription, 160);\r\n\r\n  // Create a rich metadata object\r\n  return {\r\n    title: video.title ?? 'Video Summary',\r\n    description: metaDescription,\r\n    keywords: keywords.length > 0 ? keywords : undefined,\r\n    openGraph: {\r\n      title: video.title ?? 'Video Summary',\r\n      description: metaDescription,\r\n      images: [\r\n        {\r\n          url: video.thumbnail_url ?? '/placeholder.svg',\r\n          width: 1200,\r\n          height: 630,\r\n          alt: video.title ?? 'Video thumbnail',\r\n        },\r\n      ],      type: 'video.other',\r\n      url: `/video/${topic}/${videoId}`,\r\n      siteName: 'YouTube Summaries',\r\n    },\r\n    twitter: {\r\n      card: 'summary_large_image',\r\n      title: video.title ?? 'Video Summary',\r\n      description: metaDescription,\r\n      images: [video.thumbnail_url ?? '/placeholder.svg'],\r\n    },\r\n    authors: video.channel_name ? [{ name: video.channel_name }] : undefined,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAAA;AACA;AAEA;;;;;;;;AASA,oDAAoD;AACpD,MAAM,oBAAoB,CAAC;IACzB,IAAI,CAAC,eAAe,OAAO,IAAI,CAAC,aAAa,MAAM,KAAK,GAAG;QACzD,qBAAO,8OAAC;YAAE,WAAU;sBAAmC;;;;;;IACzD;IACA,8CAA8C;IAC9C,MAAM,aAAuC;QAC3C,WAAW;YAAC;YAAW;YAAiB;YAAQ;SAAW;QAC3D,cAAc;YAAC;YAAY;YAAgB;YAAe;YAAiB;YAAc;YAAoB;YAAc;SAAW;QACtI,UAAU;YAAC;YAAY;YAAU;YAAc;YAAiB;YAAc;YAAU;YAAsB;SAAW;QACzH,aAAa;YAAC;YAAa;YAAiB;YAAsB;YAAQ;YAAoB;SAAY;QAC1G,mBAAmB;YAAC;YAAmB;YAAe;YAAU;YAAc;SAAkB;QAChG,YAAY;YAAC;YAAY;YAAc;YAAc;YAAe;YAAa;SAAqB;QACtG,qBAAqB;YAAC;YAAqB;YAAkB;YAAe;SAAqB;QACjG,SAAS,EAAE;IACb;IAEA,MAAM,gBAAgB,OAAO,IAAI,CAAC;IAClC,MAAM,gBAA0D,CAAC;IAEjE,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;QAC9B,aAAa,CAAC,SAAS,GAAG,EAAE;IAC9B;IAEA,mDAAmD;IACnD,IAAI,aAAa;QACf,cAAc,OAAO,CAAC,CAAA;YACpB,IAAI;gBAAC;gBAA+B;gBAAS;aAAe,CAAC,QAAQ,CAAC,MAAM;gBAC1E;YACF;YACA,IAAI,WAAW,OAAO,4BAA4B;YAClD,KAAK,MAAM,CAAC,UAAU,OAAO,IAAI,OAAO,OAAO,CAAC,YAAa;gBAC3D,IAAI,OAAO,QAAQ,CAAC,MAAM;oBACxB,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC;wBAAC;wBAAK,WAAW,CAAC,IAAI;qBAAC;oBACpD,WAAW;oBACX;gBACF;YACF;YACA,IAAI,CAAC,UAAU;gBACb,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC;oBAAC;oBAAK,WAAW,CAAC,IAAI;iBAAC;YACrD;QACF;IACF;IACA,4BAA4B;IAC5B,MAAM,kBAAkB,CAAC;QACvB,OAAO,IACJ,OAAO,CAAC,MAAM,KACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IAEA,MAAM,mBAAmB,CAAC,KAAa;QACrC,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAAU,IAAI,CAAC,IAAI,WAAW,GAAG,QAAQ,CAAC,cAAc,IAAI,WAAW,GAAG,QAAQ,CAAC,YAAY,QAAQ,UAAU,QAAQ,UAAU,KAAK,MAAM,OAAO,CAAC,QAAQ;YAC9M,qBACE,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;wBAAe,WAAU;kCACvB,OAAO,SAAS,WAAW,KAAK,SAAS,CAAC,QAAQ,OAAO;uBADjD;;;;;;;;;;QAMnB;QAAK,IAAI,CAAC,IAAI,WAAW,GAAG,QAAQ,CAAC,cAAc,IAAI,WAAW,GAAG,QAAQ,CAAC,qBAAqB,IAAI,WAAW,GAAG,QAAQ,CAAC,eAAe,QAAQ,cAAc,QAAQ,qBAAqB,QAAQ,WAAW,KAAK,MAAM,OAAO,CAAC,QAAQ;YAC5O,qBACE,8OAAC;gBAAG,WAAU;0BACX,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;wBAAa,WAAU;kCACtB,cAAA,8OAAC;4BAAI,WAAU;sCACZ,OAAO,SAAS,WAAW,KAAK,SAAS,CAAC,MAAM,MAAM,KAAK,OAAO;;;;;;uBAF9D;;;;;;;;;;QAQjB;QAAK,IAAI,MAAM,OAAO,CAAC,QAAQ;YAC7B,qBACE,8OAAC;gBAAG,WAAU;0BACX,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;wBAAa,WAAU;kCACrB,OAAO,SAAS,WAAW,KAAK,SAAS,CAAC,MAAM,MAAM,KAAK,OAAO;uBAD5D;;;;;;;;;;QAMjB;QAAK,IAAI,OAAO,UAAU,UAAU;YAClC,qBACE,8OAAC;gBAAI,WAAU;0BACZ,KAAK,SAAS,CAAC,OAAO,MAAM;;;;;;QAGnC;QACA,IAAI;YAAC;YAAgB;YAAY;YAAe;YAAiB;SAAW,CAAC,QAAQ,CAAC,QAAQ,OAAO,UAAU,UAAU;YACvH,IAAI,MAAM,QAAQ,CAAC,SAAS,MAAM,QAAQ,CAAC,OAAO;gBAChD,MAAM,eAAe,MAAM,KAAK,CAAC;gBAAwB,qBACvD,8OAAC;oBAAG,WAAU;8BACX,aAAa,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,OAAO,oBACxC,8OAAC;4BAAa,WAAU;sCACtB,cAAA,8OAAC;gCAAI,WAAU;0CAA4C,MAAM,IAAI;;;;;;2BAD9D;;;;;;;;;;YAMjB;QACF;QACA,IAAI,CAAC,IAAI,WAAW,GAAG,QAAQ,CAAC,eAAe,IAAI,WAAW,GAAG,QAAQ,CAAC,UAAU,QAAQ,WAAW,KAAK,OAAO,UAAU,UAAU;YACrI,IAAI,MAAM,QAAQ,CAAC,OAAO;gBACxB,MAAM,YAAY,MAAM,KAAK,CAAC,OAAO,MAAM,CAAC;gBAAiB,qBAC3D,8OAAC;oBAAG,WAAU;8BACX,UAAU,GAAG,CAAC,CAAC,UAAU,oBACxB,8OAAC;4BAAa,WAAU;sCACtB,cAAA,8OAAC;gCAAI,WAAU;0CAA4C,SAAS,IAAI;;;;;;2BADjE;;;;;;;;;;YAMjB;QACF;QAAK,IAAI;YAAC;YAAgB;YAAY;YAAW;YAAQ;YAAiB;YAAe;YAAmB;YAAY;SAAkB,CAAC,QAAQ,CAAC,MAAM;YACxJ,qBAAO,8OAAC;gBAAI,WAAU;0BAAwD,OAAO;;;;;;QACvF;QACA,qBAAO,8OAAC;YAAK,WAAU;sBAA4C,OAAO;;;;;;IAC5E;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,UAAU,OAAO;YACpD,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;YAChC,qBAAkB,8OAAC;gBAAmB,WAAU;;kCAC5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwD;;;;;;4BACrE,CAAC,aAAa,gBAAgB,aAAa,qBAAqB,aAAa,YAAY,aAAa,WAAW,KAAK,OAAO,MAAM,GAAG,mBACrI,8OAAC,iIAAA,CAAA,qBAAkB;gCACjB,UAAU;gCACV,QAAQ;;;;;;;;;;;;oBAGR;kCAAY,8OAAC;wBAAG,WAAU;kCAC7B,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACvB,8OAAC;gCAAc,WAAU;;kDACvB,8OAAC;wCAAG,WAAU;kDAAwD,gBAAgB;;;;;;kDACtF,8OAAC;wCAAG,WAAU;kDACX,iBAAiB,KAAK;;;;;;;+BAHjB;;;;;;;;;;;eAXU;;;;;QAqB9B;;;;;;AAGN;AAGe,eAAe,UAAU,EAAE,MAAM,EAAkB;IAChE,sFAAsF;IACtF,MAAM,QAAQ,OAAO,MAAM,QAAQ,OAAO,CAAC,OAAO,KAAK;IACvD,MAAM,UAAU,OAAO,MAAM,QAAQ,OAAO,CAAC,OAAO,OAAO;IAC3D,MAAM,QAAQ,MAAM,sHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC,OAAO;IAExD,IAAI,CAAC,OAAO;QACV,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,kBAAkB,MAAM,QAAQ,GAAG,CAAC,8BAA8B,EAAE,MAAM,QAAQ,EAAE,GAAG;IAE7F,qBACE,8OAAC;QAAI,WAAU;;YAAoB;0BAEjC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAmD;;;;;;kDAC5E,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAK,WAAU;kDACb,MAAM,cAAc,IAAI;;;;;;;;;;;;0CAI7B,8OAAC;gCAAG,WAAU;0CACX,MAAM,KAAK;;;;;;0CAGd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAe,MAAM,YAAY;;;;;;oCAChD,MAAM,UAAU,KAAK,QAAQ,MAAM,UAAU,KAAK,2BACjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,UAAU;;;;;;;kDAGrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0NAAA,CAAA,mBAAQ;gDAAC,WAAU;;;;;;4CAEnB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,YAAY;;;;;;;oCAE/B,MAAM,QAAQ,kBACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wMAAA,CAAA,YAAK;gDAAC,WAAU;;;;;;4CAChB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ;;;;;;;;;;;;;0CAMpC,8OAAC;gCAAI,WAAU;;oCACZ,MAAM,SAAS,kBACd,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,MAAM,SAAS;wCACrB,QAAO;wCACP,KAAI;wCACJ,WAAU;;0DAEV,8OAAC,sNAAA,CAAA,iBAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAIpC,MAAM,UAAU,kBACf,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,gCAAgC,EAAE,MAAM,UAAU,EAAE;wCAC3D,QAAO;wCACP,KAAI;wCACJ,WAAU;;0DAEV,8OAAC,0NAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAQrD,8OAAC;wBAAI,WAAU;kCACZ,gCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,OAAM;gCACN,QAAO;gCACP,KAAK;gCACL,OAAO,MAAM,KAAK,IAAI;gCACtB,aAAY;gCACZ,OAAM;gCACN,eAAe;gCACf,WAAU;;;;;;;;;;iDAIb,8OAAC;4BAAI,WAAU;;8CACZ,8OAAC,sNAAA,CAAA,iBAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;YAI9C;0BAGN,8OAAC;gBAAI,WAAU;;oBAAmB;kCAEhC,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,4NAAA,CAAA,oBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAK9C,8OAAC;;oCAEE,MAAM,YAAY,IAAI,CACrB,OAAO,MAAM,YAAY,CAAC,IAAI,KAAK,YACnC,OAAO,MAAM,YAAY,CAAC,aAAa,KAAK,YAC5C,OAAO,MAAM,YAAY,CAAC,OAAO,KAAK,QACxC,mBAAoB,8OAAC;wCAAI,WAAU;;0DAC/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC,iIAAA,CAAA,oBAAiB;wDAChB,MAAM,OAAO,MAAM,YAAY,EAAE,SAAS,WACtC,MAAM,YAAY,CAAC,IAAI,GACvB,OAAO,MAAM,YAAY,EAAE,kBAAkB,WAC3C,MAAM,YAAY,CAAC,aAAa,GAChC,OAAO,MAAM,YAAY,EAAE,YAAY,WACrC,MAAM,YAAY,CAAC,OAAO,GAC1B;;;;;;;;;;;;0DAIZ,8OAAC;gDAAE,WAAU;0DACT,OAAO,MAAM,YAAY,CAAC,IAAI,KAAK,WACjC,MAAM,YAAY,CAAC,IAAI,GACvB,OAAO,MAAM,YAAY,CAAC,aAAa,KAAK,WAC1C,MAAM,YAAY,CAAC,aAAa,GAChC,OAAO,MAAM,YAAY,CAAC,OAAO,KAAK,WACpC,MAAM,YAAY,CAAC,OAAO,GAC1B;;;;;;4CAEN;;;;;;;oCAIP,kBAAkB,MAAM,YAAY;;;;;;;;;;;;;oBAKxC,MAAM,UAAU,kBACf,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,8OAAC,iIAAA,CAAA,uBAAoB;wCAAC,YAAY,MAAM,UAAU,IAAI;;;;;;;;;;;;4BAClD;0CAAY,8OAAC;gCAAQ,WAAU;;kDACnC,8OAAC;wCAAQ,WAAU;;0DACjB,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAmE,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC1J,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAqE,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAKtG;;;;;;;AAGZ;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAkD;IAC/F,sFAAsF;IACtF,MAAM,QAAQ,OAAO,MAAM,QAAQ,OAAO,CAAC,OAAO,KAAK;IACvD,MAAM,UAAU,OAAO,MAAM,QAAQ,OAAO,CAAC,OAAO,OAAO;IAC3D,MAAM,QAAQ,MAAM,sHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC,OAAO;IAExD,IAAI,CAAC,OAAO;QACV,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;IAEA,2CAA2C;IAC3C,IAAI,kBAAkB;IACtB,IAAI,WAAqB,EAAE;IAE3B,IAAI,MAAM,YAAY,EAAE;QACtB,+BAA+B;QAC/B,IAAI,OAAO,MAAM,YAAY,CAAC,IAAI,KAAK,UAAU;YAC/C,kBAAkB,MAAM,YAAY,CAAC,IAAI;QAC3C,OAAO,IAAI,OAAO,MAAM,YAAY,CAAC,aAAa,KAAK,UAAU;YAC/D,kBAAkB,MAAM,YAAY,CAAC,aAAa;QACpD,OAAO,IAAI,OAAO,MAAM,YAAY,CAAC,OAAO,KAAK,UAAU;YACzD,kBAAkB,MAAM,YAAY,CAAC,OAAO;QAC9C;QACE,6EAA6E;QAC/E,IAAI,MAAM,OAAO,CAAC,MAAM,YAAY,CAAC,QAAQ,GAAG;YAC9C,WAAW,MAAM,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAe,OAAO;QACpE,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,YAAY,CAAC,QAAQ,GAAG;YACrD,WAAW,MAAM,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAe,OAAO;QACpE,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,YAAY,CAAC,MAAM,GAAG;YACnD,WAAW,MAAM,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAe,OAAO;QAClE,OAAO,IAAI,OAAO,MAAM,YAAY,CAAC,QAAQ,KAAK,UAAU;YAC1D,sDAAsD;YACtD,WAAW,MAAM,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;QAC/C,OAAO,IAAI,OAAO,MAAM,YAAY,CAAC,QAAQ,KAAK,UAAU;YAC1D,sDAAsD;YACtD,WAAW,MAAM,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;QAC/C;IACF;IAEA,2BAA2B;IAC3B,kBAAkB,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;IAEhD,gCAAgC;IAChC,OAAO;QACL,OAAO,MAAM,KAAK,IAAI;QACtB,aAAa;QACb,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;QAC3C,WAAW;YACT,OAAO,MAAM,KAAK,IAAI;YACtB,aAAa;YACb,QAAQ;gBACN;oBACE,KAAK,MAAM,aAAa,IAAI;oBAC5B,OAAO;oBACP,QAAQ;oBACR,KAAK,MAAM,KAAK,IAAI;gBACtB;aACD;YAAO,MAAM;YACd,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,SAAS;YACjC,UAAU;QACZ;QACA,SAAS;YACP,MAAM;YACN,OAAO,MAAM,KAAK,IAAI;YACtB,aAAa;YACb,QAAQ;gBAAC,MAAM,aAAa,IAAI;aAAmB;QACrD;QACA,SAAS,MAAM,YAAY,GAAG;YAAC;gBAAE,MAAM,MAAM,YAAY;YAAC;SAAE,GAAG;IACjE;AACF", "debugId": null}}]}