WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.149 align:start position:0%
 
alright<00:00:00.719><c> so</c><00:00:00.960><c> this</c><00:00:01.260><c> video</c><00:00:01.380><c> is</c><00:00:01.560><c> going</c><00:00:01.680><c> to</c><00:00:01.800><c> be</c><00:00:01.920><c> a</c>

00:00:02.149 --> 00:00:02.159 align:start position:0%
alright so this video is going to be a
 

00:00:02.159 --> 00:00:03.710 align:start position:0%
alright so this video is going to be a
quick<00:00:02.340><c> one</c><00:00:02.580><c> I</c><00:00:03.060><c> just</c><00:00:03.179><c> wanted</c><00:00:03.300><c> to</c><00:00:03.419><c> talk</c><00:00:03.540><c> about</c>

00:00:03.710 --> 00:00:03.720 align:start position:0%
quick one I just wanted to talk about
 

00:00:03.720 --> 00:00:06.710 align:start position:0%
quick one I just wanted to talk about
this<00:00:04.020><c> project</c><00:00:04.200><c> called</c><00:00:04.620><c> red</c><00:00:04.799><c> pajam</c><00:00:05.339><c> so</c><00:00:06.060><c> this</c><00:00:06.600><c> is</c>

00:00:06.710 --> 00:00:06.720 align:start position:0%
this project called red pajam so this is
 

00:00:06.720 --> 00:00:09.230 align:start position:0%
this project called red pajam so this is
from<00:00:07.020><c> the</c><00:00:07.379><c> group</c><00:00:07.620><c> called</c><00:00:08.160><c> together</c><00:00:08.519><c> computer</c>

00:00:09.230 --> 00:00:09.240 align:start position:0%
from the group called together computer
 

00:00:09.240 --> 00:00:12.589 align:start position:0%
from the group called together computer
I<00:00:09.599><c> think</c><00:00:09.780><c> is</c><00:00:10.260><c> also</c><00:00:10.559><c> their</c><00:00:10.800><c> name</c><00:00:11.040><c> and</c><00:00:12.000><c> this</c><00:00:12.360><c> is</c>

00:00:12.589 --> 00:00:12.599 align:start position:0%
I think is also their name and this is
 

00:00:12.599 --> 00:00:14.870 align:start position:0%
I think is also their name and this is
basically<00:00:13.139><c> the</c><00:00:13.620><c> start</c><00:00:13.920><c> of</c><00:00:14.340><c> a</c><00:00:14.460><c> whole</c><00:00:14.580><c> project</c>

00:00:14.870 --> 00:00:14.880 align:start position:0%
basically the start of a whole project
 

00:00:14.880 --> 00:00:19.609 align:start position:0%
basically the start of a whole project
to<00:00:15.480><c> reproduce</c><00:00:16.139><c> a</c><00:00:17.100><c> fully</c><00:00:17.520><c> open</c><00:00:17.699><c> source</c><00:00:18.619><c> version</c>

00:00:19.609 --> 00:00:19.619 align:start position:0%
to reproduce a fully open source version
 

00:00:19.619 --> 00:00:22.609 align:start position:0%
to reproduce a fully open source version
of<00:00:20.460><c> the</c><00:00:20.820><c> Llama</c><00:00:21.119><c> models</c><00:00:21.660><c> and</c><00:00:22.140><c> they've</c><00:00:22.320><c> kicked</c>

00:00:22.609 --> 00:00:22.619 align:start position:0%
of the Llama models and they've kicked
 

00:00:22.619 --> 00:00:25.609 align:start position:0%
of the Llama models and they've kicked
it<00:00:22.680><c> off</c><00:00:22.800><c> by</c><00:00:23.279><c> first</c><00:00:24.060><c> releasing</c><00:00:24.779><c> the</c><00:00:25.140><c> data</c><00:00:25.500><c> set</c>

00:00:25.609 --> 00:00:25.619 align:start position:0%
it off by first releasing the data set
 

00:00:25.619 --> 00:00:27.890 align:start position:0%
it off by first releasing the data set
so<00:00:26.340><c> it's</c><00:00:26.460><c> pretty</c><00:00:26.699><c> impressive</c><00:00:27.119><c> their</c><00:00:27.480><c> plan</c><00:00:27.720><c> is</c>

00:00:27.890 --> 00:00:27.900 align:start position:0%
so it's pretty impressive their plan is
 

00:00:27.900 --> 00:00:30.529 align:start position:0%
so it's pretty impressive their plan is
to<00:00:28.080><c> basically</c><00:00:28.439><c> create</c><00:00:29.220><c> a</c><00:00:29.519><c> a</c><00:00:29.760><c> set</c><00:00:30.119><c> of</c><00:00:30.359><c> open</c>

00:00:30.529 --> 00:00:30.539 align:start position:0%
to basically create a a set of open
 

00:00:30.539 --> 00:00:34.370 align:start position:0%
to basically create a a set of open
models<00:00:31.320><c> of</c><00:00:31.679><c> the</c><00:00:31.859><c> Llama</c><00:00:32.160><c> models</c><00:00:33.120><c> and</c><00:00:33.899><c> to</c><00:00:34.200><c> do</c>

00:00:34.370 --> 00:00:34.380 align:start position:0%
models of the Llama models and to do
 

00:00:34.380 --> 00:00:35.690 align:start position:0%
models of the Llama models and to do
that<00:00:34.440><c> they</c><00:00:34.680><c> actually</c><00:00:34.860><c> have</c><00:00:35.040><c> to</c><00:00:35.160><c> train</c><00:00:35.399><c> the</c>

00:00:35.690 --> 00:00:35.700 align:start position:0%
that they actually have to train the
 

00:00:35.700 --> 00:00:37.549 align:start position:0%
that they actually have to train the
foundation<00:00:36.059><c> models</c><00:00:36.600><c> on</c><00:00:36.840><c> over</c><00:00:37.020><c> a</c><00:00:37.200><c> trillion</c>

00:00:37.549 --> 00:00:37.559 align:start position:0%
foundation models on over a trillion
 

00:00:37.559 --> 00:00:42.470 align:start position:0%
foundation models on over a trillion
tokens<00:00:38.040><c> so</c><00:00:38.880><c> here's</c><00:00:39.719><c> the</c><00:00:40.260><c> data</c><00:00:40.920><c> set</c><00:00:41.239><c> based</c><00:00:42.239><c> on</c>

00:00:42.470 --> 00:00:42.480 align:start position:0%
tokens so here's the data set based on
 

00:00:42.480 --> 00:00:46.069 align:start position:0%
tokens so here's the data set based on
what<00:00:43.200><c> the</c><00:00:43.500><c> original</c><00:00:43.680><c> llama</c><00:00:44.340><c> actually</c><00:00:45.120><c> used</c><00:00:45.660><c> so</c>

00:00:46.069 --> 00:00:46.079 align:start position:0%
what the original llama actually used so
 

00:00:46.079 --> 00:00:47.869 align:start position:0%
what the original llama actually used so
this<00:00:46.320><c> is</c><00:00:46.440><c> over</c><00:00:46.800><c> a</c><00:00:47.100><c> trillion</c><00:00:47.399><c> tokens</c><00:00:47.700><c> they're</c>

00:00:47.869 --> 00:00:47.879 align:start position:0%
this is over a trillion tokens they're
 

00:00:47.879 --> 00:00:50.630 align:start position:0%
this is over a trillion tokens they're
saying<00:00:48.120><c> it's</c><00:00:48.239><c> 1.2</c><00:00:48.780><c> trillion</c><00:00:49.200><c> tokens</c><00:00:49.680><c> if</c><00:00:50.399><c> we</c>

00:00:50.630 --> 00:00:50.640 align:start position:0%
saying it's 1.2 trillion tokens if we
 

00:00:50.640 --> 00:00:53.810 align:start position:0%
saying it's 1.2 trillion tokens if we
remember<00:00:50.760><c> back</c><00:00:51.239><c> llama</c><00:00:52.200><c> the</c><00:00:52.800><c> 7</c><00:00:53.039><c> billion</c><00:00:53.460><c> and</c>

00:00:53.810 --> 00:00:53.820 align:start position:0%
remember back llama the 7 billion and
 

00:00:53.820 --> 00:00:56.389 align:start position:0%
remember back llama the 7 billion and
the<00:00:54.000><c> 13</c><00:00:54.300><c> billion</c><00:00:55.020><c> parameter</c><00:00:55.800><c> models</c><00:00:56.219><c> were</c>

00:00:56.389 --> 00:00:56.399 align:start position:0%
the 13 billion parameter models were
 

00:00:56.399 --> 00:00:58.970 align:start position:0%
the 13 billion parameter models were
trained<00:00:56.760><c> on</c><00:00:56.879><c> one</c><00:00:57.120><c> trillion</c><00:00:57.539><c> tokens</c><00:00:57.960><c> and</c><00:00:58.800><c> the</c>

00:00:58.970 --> 00:00:58.980 align:start position:0%
trained on one trillion tokens and the
 

00:00:58.980 --> 00:01:01.490 align:start position:0%
trained on one trillion tokens and the
two<00:00:59.100><c> bigger</c><00:00:59.520><c> models</c><00:01:00.000><c> going</c><00:01:00.480><c> up</c><00:01:00.660><c> to</c><00:01:00.840><c> the</c><00:01:01.079><c> 65</c>

00:01:01.490 --> 00:01:01.500 align:start position:0%
two bigger models going up to the 65
 

00:01:01.500 --> 00:01:03.529 align:start position:0%
two bigger models going up to the 65
billion<00:01:02.039><c> parameter</c><00:01:02.760><c> model</c><00:01:02.940><c> was</c><00:01:03.180><c> trained</c><00:01:03.480><c> on</c>

00:01:03.529 --> 00:01:03.539 align:start position:0%
billion parameter model was trained on
 

00:01:03.539 --> 00:01:06.950 align:start position:0%
billion parameter model was trained on
1.4<00:01:04.199><c> trillion</c><00:01:04.680><c> tokens</c><00:01:05.180><c> so</c><00:01:06.180><c> while</c><00:01:06.540><c> it</c><00:01:06.720><c> might</c>

00:01:06.950 --> 00:01:06.960 align:start position:0%
1.4 trillion tokens so while it might
 

00:01:06.960 --> 00:01:09.289 align:start position:0%
1.4 trillion tokens so while it might
seem<00:01:07.320><c> perhaps</c><00:01:07.979><c> not</c><00:01:08.220><c> a</c><00:01:08.400><c> big</c><00:01:08.520><c> deal</c><00:01:08.760><c> that</c><00:01:09.060><c> oh</c>

00:01:09.289 --> 00:01:09.299 align:start position:0%
seem perhaps not a big deal that oh
 

00:01:09.299 --> 00:01:11.149 align:start position:0%
seem perhaps not a big deal that oh
they've<00:01:09.540><c> released</c><00:01:09.900><c> this</c><00:01:10.140><c> data</c><00:01:10.560><c> set</c><00:01:10.680><c> because</c>

00:01:11.149 --> 00:01:11.159 align:start position:0%
they've released this data set because
 

00:01:11.159 --> 00:01:13.250 align:start position:0%
they've released this data set because
it's<00:01:11.460><c> just</c><00:01:11.640><c> scraped</c><00:01:12.000><c> from</c><00:01:12.180><c> the</c><00:01:12.420><c> internet</c><00:01:12.540><c> it</c>

00:01:13.250 --> 00:01:13.260 align:start position:0%
it's just scraped from the internet it
 

00:01:13.260 --> 00:01:15.109 align:start position:0%
it's just scraped from the internet it
is<00:01:13.439><c> definitely</c><00:01:13.619><c> a</c><00:01:13.860><c> big</c><00:01:13.979><c> deal</c><00:01:14.159><c> in</c><00:01:14.640><c> regards</c><00:01:15.000><c> to</c>

00:01:15.109 --> 00:01:15.119 align:start position:0%
is definitely a big deal in regards to
 

00:01:15.119 --> 00:01:17.210 align:start position:0%
is definitely a big deal in regards to
the<00:01:15.360><c> pre-processing</c><00:01:16.260><c> and</c><00:01:16.740><c> all</c><00:01:16.920><c> the</c><00:01:17.040><c> things</c>

00:01:17.210 --> 00:01:17.220 align:start position:0%
the pre-processing and all the things
 

00:01:17.220 --> 00:01:20.570 align:start position:0%
the pre-processing and all the things
that<00:01:17.700><c> have</c><00:01:18.000><c> been</c><00:01:18.240><c> done</c><00:01:18.479><c> for</c><00:01:18.840><c> that</c><00:01:19.080><c> so</c><00:01:19.979><c> they've</c>

00:01:20.570 --> 00:01:20.580 align:start position:0%
that have been done for that so they've
 

00:01:20.580 --> 00:01:22.370 align:start position:0%
that have been done for that so they've
managed<00:01:20.939><c> to</c><00:01:21.000><c> put</c><00:01:21.119><c> that</c><00:01:21.360><c> all</c><00:01:21.479><c> together</c><00:01:21.720><c> in</c><00:01:22.259><c> a</c>

00:01:22.370 --> 00:01:22.380 align:start position:0%
managed to put that all together in a
 

00:01:22.380 --> 00:01:25.249 align:start position:0%
managed to put that all together in a
way<00:01:22.560><c> that</c><00:01:22.920><c> can</c><00:01:23.280><c> actually</c><00:01:23.700><c> go</c><00:01:24.540><c> through</c><00:01:24.780><c> and</c>

00:01:25.249 --> 00:01:25.259 align:start position:0%
way that can actually go through and
 

00:01:25.259 --> 00:01:28.550 align:start position:0%
way that can actually go through and
make<00:01:25.560><c> a</c><00:01:26.100><c> nice</c><00:01:26.280><c> cleaned</c><00:01:27.119><c> high</c><00:01:27.600><c> quality</c><00:01:28.020><c> data</c>

00:01:28.550 --> 00:01:28.560 align:start position:0%
make a nice cleaned high quality data
 

00:01:28.560 --> 00:01:31.490 align:start position:0%
make a nice cleaned high quality data
set<00:01:28.680><c> on</c><00:01:29.580><c> par</c><00:01:29.759><c> with</c><00:01:30.119><c> what</c><00:01:30.420><c> llama</c><00:01:30.720><c> was</c><00:01:31.080><c> trained</c>

00:01:31.490 --> 00:01:31.500 align:start position:0%
set on par with what llama was trained
 

00:01:31.500 --> 00:01:33.530 align:start position:0%
set on par with what llama was trained
on<00:01:31.619><c> now</c><00:01:32.159><c> in</c><00:01:32.400><c> theory</c><00:01:32.640><c> that</c><00:01:32.820><c> should</c><00:01:32.880><c> mean</c><00:01:33.119><c> that</c>

00:01:33.530 --> 00:01:33.540 align:start position:0%
on now in theory that should mean that
 

00:01:33.540 --> 00:01:36.350 align:start position:0%
on now in theory that should mean that
we<00:01:33.720><c> can</c><00:01:33.900><c> get</c><00:01:34.140><c> a</c><00:01:34.560><c> model</c><00:01:34.740><c> out</c><00:01:35.100><c> that</c><00:01:35.759><c> will</c><00:01:35.880><c> be</c><00:01:36.060><c> as</c>

00:01:36.350 --> 00:01:36.360 align:start position:0%
we can get a model out that will be as
 

00:01:36.360 --> 00:01:37.789 align:start position:0%
we can get a model out that will be as
good<00:01:36.540><c> as</c><00:01:36.900><c> llama</c>

00:01:37.789 --> 00:01:37.799 align:start position:0%
good as llama
 

00:01:37.799 --> 00:01:40.249 align:start position:0%
good as llama
and<00:01:38.280><c> so</c><00:01:38.460><c> they</c><00:01:38.640><c> point</c><00:01:38.820><c> out</c><00:01:39.000><c> in</c><00:01:39.240><c> here</c><00:01:39.479><c> that</c>

00:01:40.249 --> 00:01:40.259 align:start position:0%
and so they point out in here that
 

00:01:40.259 --> 00:01:42.469 align:start position:0%
and so they point out in here that
basically<00:01:40.560><c> this</c><00:01:40.979><c> has</c><00:01:41.100><c> been</c><00:01:41.460><c> uh</c><00:01:42.060><c> sort</c><00:01:42.240><c> of</c><00:01:42.299><c> a</c>

00:01:42.469 --> 00:01:42.479 align:start position:0%
basically this has been uh sort of a
 

00:01:42.479 --> 00:01:45.830 align:start position:0%
basically this has been uh sort of a
takeoff<00:01:42.840><c> moment</c><00:01:43.380><c> for</c><00:01:44.280><c> AI</c><00:01:45.119><c> and</c><00:01:45.479><c> certainly</c><00:01:45.720><c> for</c>

00:01:45.830 --> 00:01:45.840 align:start position:0%
takeoff moment for AI and certainly for
 

00:01:45.840 --> 00:01:47.870 align:start position:0%
takeoff moment for AI and certainly for
large<00:01:46.079><c> language</c><00:01:46.380><c> models</c><00:01:46.920><c> that</c><00:01:47.460><c> these</c><00:01:47.759><c> open</c>

00:01:47.870 --> 00:01:47.880 align:start position:0%
large language models that these open
 

00:01:47.880 --> 00:01:49.850 align:start position:0%
large language models that these open
source<00:01:48.240><c> models</c><00:01:48.540><c> have</c><00:01:48.659><c> come</c><00:01:48.900><c> along</c><00:01:49.140><c> but</c>

00:01:49.850 --> 00:01:49.860 align:start position:0%
source models have come along but
 

00:01:49.860 --> 00:01:51.530 align:start position:0%
source models have come along but
unfortunately<00:01:50.280><c> a</c><00:01:50.520><c> lot</c><00:01:50.640><c> of</c><00:01:50.700><c> the</c><00:01:50.820><c> models</c><00:01:51.240><c> like</c>

00:01:51.530 --> 00:01:51.540 align:start position:0%
unfortunately a lot of the models like
 

00:01:51.540 --> 00:01:56.270 align:start position:0%
unfortunately a lot of the models like
llama<00:01:52.259><c> alpaca</c><00:01:53.159><c> vicuna</c><00:01:54.140><c> koala</c><00:01:55.140><c> are</c><00:01:55.680><c> not</c><00:01:55.920><c> really</c>

00:01:56.270 --> 00:01:56.280 align:start position:0%
llama alpaca vicuna koala are not really
 

00:01:56.280 --> 00:01:58.850 align:start position:0%
llama alpaca vicuna koala are not really
fully<00:01:56.820><c> open</c><00:01:57.360><c> there</c><00:01:58.079><c> are</c><00:01:58.259><c> some</c><00:01:58.439><c> that</c><00:01:58.680><c> like</c>

00:01:58.850 --> 00:01:58.860 align:start position:0%
fully open there are some that like
 

00:01:58.860 --> 00:02:01.670 align:start position:0%
fully open there are some that like
pythia<00:01:59.460><c> open</c><00:01:59.939><c> chat</c><00:02:00.360><c> kit</c><00:02:00.659><c> open</c><00:02:00.960><c> assistant</c><00:02:01.259><c> and</c>

00:02:01.670 --> 00:02:01.680 align:start position:0%
pythia open chat kit open assistant and
 

00:02:01.680 --> 00:02:04.249 align:start position:0%
pythia open chat kit open assistant and
Dolly<00:02:02.100><c> which</c><00:02:02.340><c> are</c><00:02:02.640><c> fully</c><00:02:03.060><c> open</c><00:02:03.240><c> but</c><00:02:03.960><c> a</c><00:02:04.079><c> lot</c><00:02:04.140><c> of</c>

00:02:04.249 --> 00:02:04.259 align:start position:0%
Dolly which are fully open but a lot of
 

00:02:04.259 --> 00:02:06.289 align:start position:0%
Dolly which are fully open but a lot of
the<00:02:04.320><c> others</c><00:02:04.680><c> are</c><00:02:04.799><c> not</c><00:02:04.979><c> so</c><00:02:05.520><c> this</c><00:02:05.700><c> is</c><00:02:05.820><c> a</c><00:02:06.000><c> way</c><00:02:06.119><c> of</c>

00:02:06.289 --> 00:02:06.299 align:start position:0%
the others are not so this is a way of
 

00:02:06.299 --> 00:02:08.150 align:start position:0%
the others are not so this is a way of
them<00:02:06.540><c> kicking</c><00:02:07.020><c> it</c><00:02:07.079><c> off</c><00:02:07.200><c> and</c><00:02:07.380><c> getting</c><00:02:07.560><c> started</c>

00:02:08.150 --> 00:02:08.160 align:start position:0%
them kicking it off and getting started
 

00:02:08.160 --> 00:02:11.990 align:start position:0%
them kicking it off and getting started
to<00:02:08.340><c> make</c><00:02:08.520><c> fully</c><00:02:09.239><c> open</c><00:02:09.420><c> Llama</c><00:02:10.319><c> model</c><00:02:10.700><c> so</c><00:02:11.700><c> the</c>

00:02:11.990 --> 00:02:12.000 align:start position:0%
to make fully open Llama model so the
 

00:02:12.000 --> 00:02:13.670 align:start position:0%
to make fully open Llama model so the
group<00:02:12.180><c> there's</c><00:02:12.540><c> quite</c><00:02:12.840><c> a</c><00:02:12.959><c> number</c><00:02:13.080><c> of</c><00:02:13.260><c> groups</c>

00:02:13.670 --> 00:02:13.680 align:start position:0%
group there's quite a number of groups
 

00:02:13.680 --> 00:02:15.850 align:start position:0%
group there's quite a number of groups
together<00:02:13.920><c> about</c><00:02:14.520><c> this</c><00:02:14.879><c> we've</c><00:02:15.300><c> got</c><00:02:15.420><c> together</c>

00:02:15.850 --> 00:02:15.860 align:start position:0%
together about this we've got together
 

00:02:15.860 --> 00:02:18.890 align:start position:0%
together about this we've got together
themselves<00:02:16.860><c> there's</c><00:02:17.520><c> also</c><00:02:18.120><c> people</c><00:02:18.420><c> from</c>

00:02:18.890 --> 00:02:18.900 align:start position:0%
themselves there's also people from
 

00:02:18.900 --> 00:02:22.910 align:start position:0%
themselves there's also people from
Stanford<00:02:19.620><c> from</c><00:02:20.280><c> eth</c><00:02:21.120><c> in</c><00:02:21.959><c> Switzerland</c><00:02:22.440><c> from</c>

00:02:22.910 --> 00:02:22.920 align:start position:0%
Stanford from eth in Switzerland from
 

00:02:22.920 --> 00:02:26.030 align:start position:0%
Stanford from eth in Switzerland from
Mila<00:02:23.459><c> in</c><00:02:23.819><c> Canada</c><00:02:24.000><c> it's</c><00:02:24.720><c> definitely</c><00:02:25.140><c> a</c><00:02:25.560><c> big</c>

00:02:26.030 --> 00:02:26.040 align:start position:0%
Mila in Canada it's definitely a big
 

00:02:26.040 --> 00:02:28.369 align:start position:0%
Mila in Canada it's definitely a big
International<00:02:26.400><c> effort</c><00:02:27.180><c> to</c><00:02:27.780><c> make</c><00:02:27.959><c> this</c><00:02:28.200><c> thing</c>

00:02:28.369 --> 00:02:28.379 align:start position:0%
International effort to make this thing
 

00:02:28.379 --> 00:02:30.949 align:start position:0%
International effort to make this thing
happen<00:02:28.700><c> and</c><00:02:29.700><c> so</c><00:02:29.940><c> they</c><00:02:30.239><c> talk</c><00:02:30.420><c> about</c><00:02:30.540><c> the</c><00:02:30.780><c> three</c>

00:02:30.949 --> 00:02:30.959 align:start position:0%
happen and so they talk about the three
 

00:02:30.959 --> 00:02:33.350 align:start position:0%
happen and so they talk about the three
main<00:02:31.200><c> components</c><00:02:31.860><c> of</c><00:02:32.220><c> this</c><00:02:32.520><c> being</c><00:02:33.060><c> the</c>

00:02:33.350 --> 00:02:33.360 align:start position:0%
main components of this being the
 

00:02:33.360 --> 00:02:35.449 align:start position:0%
main components of this being the
pre-training<00:02:33.959><c> data</c><00:02:34.379><c> which</c><00:02:34.920><c> needs</c><00:02:35.160><c> to</c><00:02:35.220><c> be</c><00:02:35.280><c> both</c>

00:02:35.449 --> 00:02:35.459 align:start position:0%
pre-training data which needs to be both
 

00:02:35.459 --> 00:02:37.790 align:start position:0%
pre-training data which needs to be both
high<00:02:35.760><c> quality</c><00:02:36.120><c> and</c><00:02:36.420><c> have</c><00:02:36.599><c> broad</c><00:02:36.900><c> coverage</c><00:02:37.440><c> the</c>

00:02:37.790 --> 00:02:37.800 align:start position:0%
high quality and have broad coverage the
 

00:02:37.800 --> 00:02:39.949 align:start position:0%
high quality and have broad coverage the
that's<00:02:38.099><c> what</c><00:02:38.340><c> they</c><00:02:38.520><c> were</c><00:02:38.640><c> releasing</c><00:02:39.120><c> now</c><00:02:39.480><c> the</c>

00:02:39.949 --> 00:02:39.959 align:start position:0%
that's what they were releasing now the
 

00:02:39.959 --> 00:02:42.350 align:start position:0%
that's what they were releasing now the
base<00:02:40.200><c> models</c><00:02:40.800><c> which</c><00:02:41.160><c> is</c><00:02:41.340><c> apparently</c><00:02:42.060><c> their</c>

00:02:42.350 --> 00:02:42.360 align:start position:0%
base models which is apparently their
 

00:02:42.360 --> 00:02:44.270 align:start position:0%
base models which is apparently their
training<00:02:42.780><c> at</c><00:02:42.900><c> the</c><00:02:43.080><c> moment</c><00:02:43.260><c> and</c><00:02:43.920><c> then</c><00:02:44.040><c> third</c>

00:02:44.270 --> 00:02:44.280 align:start position:0%
training at the moment and then third
 

00:02:44.280 --> 00:02:46.610 align:start position:0%
training at the moment and then third
will<00:02:44.580><c> be</c><00:02:44.700><c> the</c><00:02:44.940><c> instruction</c><00:02:45.120><c> tuning</c><00:02:45.900><c> data</c><00:02:46.319><c> sets</c>

00:02:46.610 --> 00:02:46.620 align:start position:0%
will be the instruction tuning data sets
 

00:02:46.620 --> 00:02:48.470 align:start position:0%
will be the instruction tuning data sets
which<00:02:47.099><c> we'll</c><00:02:47.280><c> probably</c><00:02:47.459><c> see</c><00:02:47.640><c> a</c><00:02:47.879><c> variety</c><00:02:48.239><c> of</c>

00:02:48.470 --> 00:02:48.480 align:start position:0%
which we'll probably see a variety of
 

00:02:48.480 --> 00:02:51.410 align:start position:0%
which we'll probably see a variety of
those<00:02:48.780><c> come</c><00:02:49.080><c> out</c><00:02:49.260><c> over</c><00:02:49.860><c> then</c><00:02:50.099><c> so</c><00:02:51.000><c> anyway</c><00:02:51.120><c> they</c>

00:02:51.410 --> 00:02:51.420 align:start position:0%
those come out over then so anyway they
 

00:02:51.420 --> 00:02:52.790 align:start position:0%
those come out over then so anyway they
go<00:02:51.599><c> on</c><00:02:51.660><c> a</c><00:02:51.840><c> little</c><00:02:51.900><c> bit</c><00:02:52.019><c> about</c><00:02:52.200><c> the</c><00:02:52.680><c> different</c>

00:02:52.790 --> 00:02:52.800 align:start position:0%
go on a little bit about the different
 

00:02:52.800 --> 00:02:55.430 align:start position:0%
go on a little bit about the different
reasons<00:02:53.580><c> why</c><00:02:53.940><c> and</c><00:02:54.180><c> some</c><00:02:54.420><c> things</c><00:02:54.599><c> about</c><00:02:54.840><c> llama</c>

00:02:55.430 --> 00:02:55.440 align:start position:0%
reasons why and some things about llama
 

00:02:55.440 --> 00:02:57.290 align:start position:0%
reasons why and some things about llama
in<00:02:55.800><c> there</c><00:02:55.980><c> and</c><00:02:56.459><c> then</c><00:02:56.519><c> they</c><00:02:56.700><c> break</c><00:02:56.879><c> down</c><00:02:57.060><c> the</c>

00:02:57.290 --> 00:02:57.300 align:start position:0%
in there and then they break down the
 

00:02:57.300 --> 00:02:59.570 align:start position:0%
in there and then they break down the
actual<00:02:57.540><c> data</c><00:02:57.900><c> set</c><00:02:58.019><c> so</c><00:02:58.319><c> the</c><00:02:58.440><c> data</c><00:02:58.800><c> set</c>

00:02:59.570 --> 00:02:59.580 align:start position:0%
actual data set so the data set
 

00:02:59.580 --> 00:03:02.809 align:start position:0%
actual data set so the data set
is<00:03:00.300><c> made</c><00:03:00.599><c> of</c><00:03:01.140><c> five</c><00:03:01.440><c> dumps</c><00:03:01.860><c> of</c><00:03:01.980><c> common</c><00:03:02.160><c> crawl</c>

00:03:02.809 --> 00:03:02.819 align:start position:0%
is made of five dumps of common crawl
 

00:03:02.819 --> 00:03:05.750 align:start position:0%
is made of five dumps of common crawl
which<00:03:03.480><c> is</c><00:03:03.660><c> basically</c><00:03:04.140><c> looking</c><00:03:04.739><c> just</c><00:03:05.160><c> scraping</c>

00:03:05.750 --> 00:03:05.760 align:start position:0%
which is basically looking just scraping
 

00:03:05.760 --> 00:03:08.570 align:start position:0%
which is basically looking just scraping
the<00:03:05.940><c> internet</c><00:03:06.120><c> of</c><00:03:06.959><c> pages</c><00:03:07.500><c> and</c><00:03:08.220><c> then</c><00:03:08.280><c> they've</c>

00:03:08.570 --> 00:03:08.580 align:start position:0%
the internet of pages and then they've
 

00:03:08.580 --> 00:03:10.250 align:start position:0%
the internet of pages and then they've
got<00:03:08.760><c> a</c><00:03:08.940><c> number</c><00:03:09.120><c> of</c><00:03:09.239><c> different</c><00:03:09.480><c> filters</c><00:03:10.019><c> that</c>

00:03:10.250 --> 00:03:10.260 align:start position:0%
got a number of different filters that
 

00:03:10.260 --> 00:03:12.110 align:start position:0%
got a number of different filters that
they're<00:03:10.379><c> using</c><00:03:10.680><c> to</c><00:03:10.920><c> clean</c><00:03:11.040><c> that</c><00:03:11.340><c> they've</c><00:03:12.000><c> got</c>

00:03:12.110 --> 00:03:12.120 align:start position:0%
they're using to clean that they've got
 

00:03:12.120 --> 00:03:15.470 align:start position:0%
they're using to clean that they've got
the<00:03:12.300><c> C4</c><00:03:12.780><c> standard</c><00:03:13.440><c> C4</c><00:03:13.800><c> data</c><00:03:14.220><c> set</c><00:03:14.340><c> which</c><00:03:14.879><c> came</c>

00:03:15.470 --> 00:03:15.480 align:start position:0%
the C4 standard C4 data set which came
 

00:03:15.480 --> 00:03:19.850 align:start position:0%
the C4 standard C4 data set which came
out<00:03:15.720><c> of</c><00:03:15.900><c> the</c><00:03:16.140><c> T5</c><00:03:16.680><c> model</c><00:03:17.580><c> back</c><00:03:18.000><c> in</c><00:03:18.180><c> 2019</c><00:03:18.959><c> they've</c>

00:03:19.850 --> 00:03:19.860 align:start position:0%
out of the T5 model back in 2019 they've
 

00:03:19.860 --> 00:03:23.149 align:start position:0%
out of the T5 model back in 2019 they've
got<00:03:20.040><c> GitHub</c><00:03:20.580><c> they've</c><00:03:21.480><c> got</c><00:03:21.659><c> archive</c><00:03:22.440><c> papers</c>

00:03:23.149 --> 00:03:23.159 align:start position:0%
got GitHub they've got archive papers
 

00:03:23.159 --> 00:03:25.670 align:start position:0%
got GitHub they've got archive papers
they've<00:03:23.700><c> got</c><00:03:23.879><c> the</c><00:03:24.060><c> Books</c><00:03:24.480><c> Corpus</c><00:03:24.959><c> which</c><00:03:25.379><c> I'm</c>

00:03:25.670 --> 00:03:25.680 align:start position:0%
they've got the Books Corpus which I'm
 

00:03:25.680 --> 00:03:27.610 align:start position:0%
they've got the Books Corpus which I'm
pretty<00:03:25.920><c> sure</c><00:03:26.040><c> was</c><00:03:26.280><c> used</c><00:03:26.400><c> in</c><00:03:26.700><c> the</c><00:03:26.819><c> original</c><00:03:26.940><c> GPT</c>

00:03:27.610 --> 00:03:27.620 align:start position:0%
pretty sure was used in the original GPT
 

00:03:27.620 --> 00:03:31.369 align:start position:0%
pretty sure was used in the original GPT
2<00:03:28.620><c> model</c><00:03:29.220><c> Wikipedia</c><00:03:30.180><c> been</c><00:03:30.780><c> used</c><00:03:31.019><c> in</c><00:03:31.200><c> many</c>

00:03:31.369 --> 00:03:31.379 align:start position:0%
2 model Wikipedia been used in many
 

00:03:31.379 --> 00:03:34.309 align:start position:0%
2 model Wikipedia been used in many
models<00:03:31.800><c> and</c><00:03:32.340><c> stack</c><00:03:32.760><c> exchange</c><00:03:33.180><c> there</c><00:03:33.840><c> as</c><00:03:34.080><c> well</c>

00:03:34.309 --> 00:03:34.319 align:start position:0%
models and stack exchange there as well
 

00:03:34.319 --> 00:03:37.610 align:start position:0%
models and stack exchange there as well
so<00:03:34.800><c> this</c><00:03:35.400><c> is</c><00:03:35.580><c> quite</c><00:03:36.000><c> impressive</c><00:03:36.720><c> the</c><00:03:37.440><c> number</c>

00:03:37.610 --> 00:03:37.620 align:start position:0%
so this is quite impressive the number
 

00:03:37.620 --> 00:03:40.130 align:start position:0%
so this is quite impressive the number
of<00:03:37.800><c> tokens</c><00:03:38.400><c> that</c><00:03:38.760><c> they've</c><00:03:39.000><c> got</c><00:03:39.180><c> here</c><00:03:39.480><c> that</c><00:03:40.019><c> are</c>

00:03:40.130 --> 00:03:40.140 align:start position:0%
of tokens that they've got here that are
 

00:03:40.140 --> 00:03:42.710 align:start position:0%
of tokens that they've got here that are
putting<00:03:40.500><c> all</c><00:03:40.920><c> together</c><00:03:41.099><c> to</c><00:03:42.060><c> create</c><00:03:42.239><c> something</c>

00:03:42.710 --> 00:03:42.720 align:start position:0%
putting all together to create something
 

00:03:42.720 --> 00:03:45.589 align:start position:0%
putting all together to create something
that's<00:03:43.200><c> sort</c><00:03:43.560><c> of</c><00:03:43.680><c> 1.2</c><00:03:44.340><c> trillion</c><00:03:44.760><c> tokens</c><00:03:45.239><c> this</c>

00:03:45.589 --> 00:03:45.599 align:start position:0%
that's sort of 1.2 trillion tokens this
 

00:03:45.599 --> 00:03:47.690 align:start position:0%
that's sort of 1.2 trillion tokens this
is<00:03:45.659><c> definitely</c><00:03:45.900><c> in</c><00:03:46.080><c> the</c><00:03:46.200><c> ballpark</c><00:03:46.680><c> of</c><00:03:47.459><c> what</c>

00:03:47.690 --> 00:03:47.700 align:start position:0%
is definitely in the ballpark of what
 

00:03:47.700 --> 00:03:50.750 align:start position:0%
is definitely in the ballpark of what
where<00:03:48.060><c> llama</c><00:03:48.540><c> was</c><00:03:48.900><c> so</c><00:03:49.799><c> they</c><00:03:50.040><c> put</c><00:03:50.280><c> this</c><00:03:50.459><c> up</c><00:03:50.580><c> on</c>

00:03:50.750 --> 00:03:50.760 align:start position:0%
where llama was so they put this up on
 

00:03:50.760 --> 00:03:52.670 align:start position:0%
where llama was so they put this up on
hugging<00:03:51.120><c> face</c><00:03:51.299><c> if</c><00:03:51.900><c> you</c><00:03:52.019><c> wanted</c><00:03:52.260><c> to</c><00:03:52.440><c> go</c><00:03:52.560><c> and</c>

00:03:52.670 --> 00:03:52.680 align:start position:0%
hugging face if you wanted to go and
 

00:03:52.680 --> 00:03:54.890 align:start position:0%
hugging face if you wanted to go and
train<00:03:52.920><c> your</c><00:03:53.159><c> own</c><00:03:53.340><c> llama</c><00:03:53.760><c> model</c><00:03:54.060><c> now</c><00:03:54.420><c> and</c><00:03:54.780><c> you</c>

00:03:54.890 --> 00:03:54.900 align:start position:0%
train your own llama model now and you
 

00:03:54.900 --> 00:03:56.690 align:start position:0%
train your own llama model now and you
had<00:03:55.080><c> the</c><00:03:55.319><c> money</c><00:03:55.440><c> and</c><00:03:55.680><c> the</c><00:03:55.860><c> compute</c><00:03:56.340><c> you</c><00:03:56.580><c> would</c>

00:03:56.690 --> 00:03:56.700 align:start position:0%
had the money and the compute you would
 

00:03:56.700 --> 00:03:59.089 align:start position:0%
had the money and the compute you would
certainly<00:03:57.060><c> be</c><00:03:57.239><c> able</c><00:03:57.480><c> to</c><00:03:57.720><c> do</c><00:03:57.900><c> that</c><00:03:58.200><c> the</c><00:03:58.799><c> data</c>

00:03:59.089 --> 00:03:59.099 align:start position:0%
certainly be able to do that the data
 

00:03:59.099 --> 00:04:01.190 align:start position:0%
certainly be able to do that the data
set<00:03:59.159><c> is</c><00:03:59.280><c> on</c><00:03:59.519><c> hanging</c><00:04:00.120><c> face</c><00:04:00.299><c> it's</c><00:04:00.720><c> a</c><00:04:00.900><c> trillion</c>

00:04:01.190 --> 00:04:01.200 align:start position:0%
set is on hanging face it's a trillion
 

00:04:01.200 --> 00:04:03.649 align:start position:0%
set is on hanging face it's a trillion
tokens<00:04:01.680><c> it</c><00:04:02.459><c> will</c><00:04:02.640><c> take</c><00:04:02.879><c> you</c><00:04:03.000><c> probably</c><00:04:03.239><c> quite</c><00:04:03.480><c> a</c>

00:04:03.649 --> 00:04:03.659 align:start position:0%
tokens it will take you probably quite a
 

00:04:03.659 --> 00:04:07.070 align:start position:0%
tokens it will take you probably quite a
long<00:04:03.780><c> time</c><00:04:04.080><c> to</c><00:04:04.680><c> download</c><00:04:05.040><c> it</c><00:04:05.540><c> and</c><00:04:06.540><c> they've</c>

00:04:07.070 --> 00:04:07.080 align:start position:0%
long time to download it and they've
 

00:04:07.080 --> 00:04:10.490 align:start position:0%
long time to download it and they've
also<00:04:07.319><c> got</c><00:04:07.620><c> in</c><00:04:08.040><c> here</c><00:04:08.280><c> a</c><00:04:09.060><c> smaller</c><00:04:09.599><c> version</c><00:04:09.900><c> of</c>

00:04:10.490 --> 00:04:10.500 align:start position:0%
also got in here a smaller version of
 

00:04:10.500 --> 00:04:13.490 align:start position:0%
also got in here a smaller version of
this<00:04:10.920><c> which</c><00:04:11.700><c> is</c><00:04:11.819><c> the</c><00:04:12.120><c> sample</c><00:04:12.420><c> data</c><00:04:12.900><c> set</c><00:04:12.959><c> so</c>

00:04:13.490 --> 00:04:13.500 align:start position:0%
this which is the sample data set so
 

00:04:13.500 --> 00:04:14.869 align:start position:0%
this which is the sample data set so
this<00:04:13.739><c> one</c><00:04:13.860><c> you</c><00:04:14.040><c> can</c><00:04:14.159><c> actually</c><00:04:14.280><c> go</c><00:04:14.519><c> through</c><00:04:14.640><c> and</c>

00:04:14.869 --> 00:04:14.879 align:start position:0%
this one you can actually go through and
 

00:04:14.879 --> 00:04:16.789 align:start position:0%
this one you can actually go through and
have<00:04:15.180><c> a</c><00:04:15.239><c> look</c><00:04:15.360><c> at</c><00:04:15.540><c> it</c><00:04:15.599><c> this</c><00:04:15.840><c> is</c><00:04:16.019><c> only</c><00:04:16.199><c> a</c><00:04:16.440><c> billion</c>

00:04:16.789 --> 00:04:16.799 align:start position:0%
have a look at it this is only a billion
 

00:04:16.799 --> 00:04:20.270 align:start position:0%
have a look at it this is only a billion
tokens<00:04:17.280><c> a</c><00:04:17.760><c> subset</c><00:04:18.180><c> from</c><00:04:18.660><c> the</c><00:04:19.260><c> main</c><00:04:19.440><c> one</c><00:04:19.680><c> so</c>

00:04:20.270 --> 00:04:20.280 align:start position:0%
tokens a subset from the main one so
 

00:04:20.280 --> 00:04:22.310 align:start position:0%
tokens a subset from the main one so
anyway<00:04:20.400><c> just</c><00:04:20.760><c> the</c><00:04:21.000><c> main</c><00:04:21.120><c> thing</c><00:04:21.479><c> to</c><00:04:22.019><c> sort</c><00:04:22.199><c> of</c>

00:04:22.310 --> 00:04:22.320 align:start position:0%
anyway just the main thing to sort of
 

00:04:22.320 --> 00:04:25.249 align:start position:0%
anyway just the main thing to sort of
keep<00:04:22.860><c> you</c><00:04:23.040><c> informed</c><00:04:23.400><c> here</c><00:04:23.580><c> is</c><00:04:23.880><c> that</c><00:04:24.240><c> we've</c><00:04:25.020><c> got</c>

00:04:25.249 --> 00:04:25.259 align:start position:0%
keep you informed here is that we've got
 

00:04:25.259 --> 00:04:27.710 align:start position:0%
keep you informed here is that we've got
a<00:04:25.680><c> full</c><00:04:25.860><c> open</c><00:04:26.160><c> source</c><00:04:26.639><c> llama</c><00:04:27.000><c> model</c><00:04:27.300><c> that</c>

00:04:27.710 --> 00:04:27.720 align:start position:0%
a full open source llama model that
 

00:04:27.720 --> 00:04:29.749 align:start position:0%
a full open source llama model that
sounds<00:04:27.900><c> like</c><00:04:28.139><c> it's</c><00:04:28.380><c> well</c><00:04:28.680><c> on</c><00:04:28.860><c> the</c><00:04:29.040><c> way</c><00:04:29.220><c> to</c>

00:04:29.749 --> 00:04:29.759 align:start position:0%
sounds like it's well on the way to
 

00:04:29.759 --> 00:04:32.689 align:start position:0%
sounds like it's well on the way to
coming<00:04:30.060><c> out</c><00:04:30.660><c> which</c><00:04:31.259><c> will</c><00:04:31.500><c> mean</c><00:04:31.680><c> that</c><00:04:32.100><c> a</c><00:04:32.460><c> lot</c><00:04:32.580><c> of</c>

00:04:32.689 --> 00:04:32.699 align:start position:0%
coming out which will mean that a lot of
 

00:04:32.699 --> 00:04:33.950 align:start position:0%
coming out which will mean that a lot of
things<00:04:32.820><c> that</c><00:04:33.060><c> people</c><00:04:33.240><c> were</c><00:04:33.479><c> doing</c><00:04:33.660><c> with</c>

00:04:33.950 --> 00:04:33.960 align:start position:0%
things that people were doing with
 

00:04:33.960 --> 00:04:36.890 align:start position:0%
things that people were doing with
fukuna<00:04:34.740><c> with</c><00:04:35.040><c> koala</c><00:04:35.759><c> with</c><00:04:36.360><c> a</c><00:04:36.479><c> lot</c><00:04:36.540><c> of</c><00:04:36.660><c> these</c>

00:04:36.890 --> 00:04:36.900 align:start position:0%
fukuna with koala with a lot of these
 

00:04:36.900 --> 00:04:38.629 align:start position:0%
fukuna with koala with a lot of these
models<00:04:37.380><c> there's</c><00:04:38.160><c> probably</c><00:04:38.340><c> going</c><00:04:38.460><c> to</c><00:04:38.580><c> be</c>

00:04:38.629 --> 00:04:38.639 align:start position:0%
models there's probably going to be
 

00:04:38.639 --> 00:04:40.310 align:start position:0%
models there's probably going to be
versions<00:04:39.120><c> of</c><00:04:39.360><c> these</c><00:04:39.660><c> that</c><00:04:39.900><c> are</c><00:04:40.020><c> going</c><00:04:40.139><c> to</c><00:04:40.259><c> be</c>

00:04:40.310 --> 00:04:40.320 align:start position:0%
versions of these that are going to be
 

00:04:40.320 --> 00:04:42.530 align:start position:0%
versions of these that are going to be
fully<00:04:40.680><c> open</c><00:04:40.860><c> source</c><00:04:41.340><c> in</c><00:04:41.580><c> the</c><00:04:41.699><c> not</c><00:04:41.820><c> too</c><00:04:42.060><c> distant</c>

00:04:42.530 --> 00:04:42.540 align:start position:0%
fully open source in the not too distant
 

00:04:42.540 --> 00:04:43.550 align:start position:0%
fully open source in the not too distant
future

00:04:43.550 --> 00:04:43.560 align:start position:0%
future
 

00:04:43.560 --> 00:04:46.249 align:start position:0%
future
anyway<00:04:44.100><c> on</c><00:04:44.759><c> that</c><00:04:44.940><c> note</c><00:04:45.120><c> as</c><00:04:45.600><c> always</c><00:04:45.780><c> if</c><00:04:46.080><c> you've</c>

00:04:46.249 --> 00:04:46.259 align:start position:0%
anyway on that note as always if you've
 

00:04:46.259 --> 00:04:47.749 align:start position:0%
anyway on that note as always if you've
got<00:04:46.380><c> questions</c><00:04:46.680><c> please</c><00:04:47.160><c> put</c><00:04:47.340><c> them</c><00:04:47.520><c> in</c><00:04:47.639><c> the</c>

00:04:47.749 --> 00:04:47.759 align:start position:0%
got questions please put them in the
 

00:04:47.759 --> 00:04:49.909 align:start position:0%
got questions please put them in the
comments<00:04:48.180><c> if</c><00:04:48.660><c> you</c><00:04:48.840><c> found</c><00:04:49.020><c> this</c><00:04:49.199><c> useful</c><00:04:49.560><c> please</c>

00:04:49.909 --> 00:04:49.919 align:start position:0%
comments if you found this useful please
 

00:04:49.919 --> 00:04:51.830 align:start position:0%
comments if you found this useful please
click<00:04:50.160><c> like</c><00:04:50.340><c> And</c><00:04:50.580><c> subscribe</c><00:04:51.000><c> I</c><00:04:51.419><c> will</c><00:04:51.540><c> see</c><00:04:51.780><c> you</c>

00:04:51.830 --> 00:04:51.840 align:start position:0%
click like And subscribe I will see you
 

00:04:51.840 --> 00:04:54.500 align:start position:0%
click like And subscribe I will see you
in<00:04:52.020><c> the</c><00:04:52.139><c> next</c><00:04:52.259><c> video</c>

