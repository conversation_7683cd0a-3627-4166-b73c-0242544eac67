WEBVTT
Kind: captions
Language: en

00:00:00.599 --> 00:00:03.169 align:start position:0%
 
hey<00:00:01.079><c> there</c><00:00:01.260><c> it's</c><00:00:01.680><c> Matt</c><00:00:01.860><c> <PERSON></c><00:00:02.159><c> from</c><00:00:02.639><c> olama</c>

00:00:03.169 --> 00:00:03.179 align:start position:0%
hey there it's <PERSON> from olama
 

00:00:03.179 --> 00:00:05.710 align:start position:0%
hey there it's <PERSON> from olama
a<00:00:03.720><c> few</c><00:00:03.840><c> days</c><00:00:03.959><c> ago</c><00:00:04.200><c> we</c><00:00:04.380><c> released</c><00:00:04.740><c> version</c>

00:00:05.710 --> 00:00:05.720 align:start position:0%
a few days ago we released version
 

00:00:05.720 --> 00:00:08.330 align:start position:0%
a few days ago we released version
0.0.12<00:00:06.720><c> and</c><00:00:07.319><c> I</c><00:00:07.440><c> want</c><00:00:07.560><c> to</c><00:00:07.680><c> show</c><00:00:07.740><c> you</c><00:00:07.859><c> what's</c><00:00:08.099><c> new</c>

00:00:08.330 --> 00:00:08.340 align:start position:0%
0.0.12 and I want to show you what's new
 

00:00:08.340 --> 00:00:11.089 align:start position:0%
0.0.12 and I want to show you what's new
and<00:00:08.700><c> interesting</c><00:00:09.120><c> when</c><00:00:09.840><c> I</c><00:00:10.019><c> run</c><00:00:10.200><c> olama</c><00:00:10.679><c> in</c><00:00:10.980><c> this</c>

00:00:11.089 --> 00:00:11.099 align:start position:0%
and interesting when I run olama in this
 

00:00:11.099 --> 00:00:12.470 align:start position:0%
and interesting when I run olama in this
video<00:00:11.280><c> you'll</c><00:00:11.639><c> notice</c><00:00:12.000><c> two</c><00:00:12.300><c> different</c>

00:00:12.470 --> 00:00:12.480 align:start position:0%
video you'll notice two different
 

00:00:12.480 --> 00:00:15.169 align:start position:0%
video you'll notice two different
commands<00:00:13.080><c> first</c><00:00:13.500><c> there</c><00:00:13.860><c> is</c><00:00:13.980><c> olama</c><00:00:14.519><c> and</c><00:00:15.059><c> that's</c>

00:00:15.169 --> 00:00:15.179 align:start position:0%
commands first there is olama and that's
 

00:00:15.179 --> 00:00:16.550 align:start position:0%
commands first there is olama and that's
the<00:00:15.360><c> version</c><00:00:15.480><c> you</c><00:00:15.780><c> can</c><00:00:15.900><c> install</c><00:00:16.020><c> from</c><00:00:16.379><c> the</c>

00:00:16.550 --> 00:00:16.560 align:start position:0%
the version you can install from the
 

00:00:16.560 --> 00:00:19.670 align:start position:0%
the version you can install from the
website<00:00:16.859><c> then</c><00:00:17.520><c> I</c><00:00:17.760><c> have</c><00:00:18.359><c> ulama</c><00:00:18.660><c> and</c><00:00:19.320><c> that's</c><00:00:19.440><c> a</c>

00:00:19.670 --> 00:00:19.680 align:start position:0%
website then I have ulama and that's a
 

00:00:19.680 --> 00:00:21.410 align:start position:0%
website then I have ulama and that's a
special<00:00:19.800><c> version</c><00:00:20.039><c> that</c><00:00:20.460><c> I've</c><00:00:20.640><c> compiled</c><00:00:21.119><c> to</c>

00:00:21.410 --> 00:00:21.420 align:start position:0%
special version that I've compiled to
 

00:00:21.420 --> 00:00:23.990 align:start position:0%
special version that I've compiled to
let<00:00:21.539><c> me</c><00:00:21.720><c> see</c><00:00:21.900><c> the</c><00:00:22.140><c> previous</c><00:00:22.260><c> version</c><00:00:23.000><c> 0.0.11</c>

00:00:23.990 --> 00:00:24.000 align:start position:0%
let me see the previous version 0.0.11
 

00:00:24.000 --> 00:00:26.150 align:start position:0%
let me see the previous version 0.0.11
and<00:00:24.720><c> that's</c><00:00:24.840><c> purely</c><00:00:25.260><c> for</c><00:00:25.500><c> the</c><00:00:25.619><c> purpose</c><00:00:25.920><c> of</c>

00:00:26.150 --> 00:00:26.160 align:start position:0%
and that's purely for the purpose of
 

00:00:26.160 --> 00:00:28.670 align:start position:0%
and that's purely for the purpose of
making<00:00:26.340><c> this</c><00:00:26.580><c> video</c><00:00:26.840><c> also</c><00:00:27.840><c> I'll</c><00:00:28.140><c> refer</c><00:00:28.500><c> to</c>

00:00:28.670 --> 00:00:28.680 align:start position:0%
making this video also I'll refer to
 

00:00:28.680 --> 00:00:30.070 align:start position:0%
making this video also I'll refer to
version

00:00:30.070 --> 00:00:30.080 align:start position:0%
version
 

00:00:30.080 --> 00:00:33.790 align:start position:0%
version
0.0.12<00:00:31.080><c> as</c><00:00:31.380><c> version</c><00:00:31.679><c> 12.</c><00:00:32.160><c> in</c><00:00:32.460><c> this</c><00:00:32.640><c> video</c><00:00:32.820><c> and</c>

00:00:33.790 --> 00:00:33.800 align:start position:0%
0.0.12 as version 12. in this video and
 

00:00:33.800 --> 00:00:36.770 align:start position:0%
0.0.12 as version 12. in this video and
0.0.11<00:00:34.800><c> as</c><00:00:35.280><c> version</c><00:00:35.520><c> 11.</c><00:00:36.059><c> just</c><00:00:36.480><c> to</c><00:00:36.660><c> keep</c>

00:00:36.770 --> 00:00:36.780 align:start position:0%
0.0.11 as version 11. just to keep
 

00:00:36.780 --> 00:00:38.569 align:start position:0%
0.0.11 as version 11. just to keep
things<00:00:36.960><c> simpler</c><00:00:37.440><c> as</c><00:00:37.620><c> I</c><00:00:37.800><c> speak</c>

00:00:38.569 --> 00:00:38.579 align:start position:0%
things simpler as I speak
 

00:00:38.579 --> 00:00:41.030 align:start position:0%
things simpler as I speak
so<00:00:39.120><c> let's</c><00:00:39.360><c> start</c><00:00:39.600><c> with</c><00:00:39.899><c> a</c><00:00:40.260><c> new</c><00:00:40.379><c> sub</c><00:00:40.620><c> command</c>

00:00:41.030 --> 00:00:41.040 align:start position:0%
so let's start with a new sub command
 

00:00:41.040 --> 00:00:44.150 align:start position:0%
so let's start with a new sub command
when<00:00:41.579><c> I</c><00:00:41.760><c> run</c><00:00:41.879><c> olam11</c><00:00:42.600><c> you</c><00:00:43.260><c> can</c><00:00:43.320><c> see</c><00:00:43.500><c> I</c><00:00:43.860><c> have</c><00:00:43.980><c> all</c>

00:00:44.150 --> 00:00:44.160 align:start position:0%
when I run olam11 you can see I have all
 

00:00:44.160 --> 00:00:46.130 align:start position:0%
when I run olam11 you can see I have all
the<00:00:44.280><c> commands</c><00:00:44.640><c> you're</c><00:00:45.000><c> used</c><00:00:45.300><c> to</c><00:00:45.420><c> seeing</c><00:00:45.719><c> but</c>

00:00:46.130 --> 00:00:46.140 align:start position:0%
the commands you're used to seeing but
 

00:00:46.140 --> 00:00:49.010 align:start position:0%
the commands you're used to seeing but
Obama<00:00:46.500><c> 12</c><00:00:46.800><c> introduces</c><00:00:47.520><c> CP</c><00:00:48.000><c> this</c><00:00:48.480><c> will</c><00:00:48.600><c> make</c><00:00:48.780><c> a</c>

00:00:49.010 --> 00:00:49.020 align:start position:0%
Obama 12 introduces CP this will make a
 

00:00:49.020 --> 00:00:50.569 align:start position:0%
Obama 12 introduces CP this will make a
copy<00:00:49.260><c> of</c><00:00:49.500><c> the</c><00:00:49.620><c> model</c><00:00:49.739><c> with</c><00:00:50.100><c> a</c><00:00:50.280><c> new</c><00:00:50.399><c> name</c>

00:00:50.569 --> 00:00:50.579 align:start position:0%
copy of the model with a new name
 

00:00:50.579 --> 00:00:52.610 align:start position:0%
copy of the model with a new name
because<00:00:51.059><c> of</c><00:00:51.300><c> the</c><00:00:51.420><c> way</c><00:00:51.539><c> layers</c><00:00:51.960><c> work</c><00:00:52.320><c> with</c>

00:00:52.610 --> 00:00:52.620 align:start position:0%
because of the way layers work with
 

00:00:52.620 --> 00:00:54.709 align:start position:0%
because of the way layers work with
models<00:00:53.100><c> in</c><00:00:53.399><c> olama</c><00:00:53.820><c> we</c><00:00:54.360><c> aren't</c><00:00:54.600><c> actually</c>

00:00:54.709 --> 00:00:54.719 align:start position:0%
models in olama we aren't actually
 

00:00:54.719 --> 00:00:56.510 align:start position:0%
models in olama we aren't actually
copying<00:00:55.079><c> the</c><00:00:55.199><c> big</c><00:00:55.320><c> files</c><00:00:55.739><c> that</c><00:00:55.920><c> could</c><00:00:56.100><c> be</c><00:00:56.219><c> many</c>

00:00:56.510 --> 00:00:56.520 align:start position:0%
copying the big files that could be many
 

00:00:56.520 --> 00:00:58.610 align:start position:0%
copying the big files that could be many
many<00:00:56.760><c> gigabytes</c><00:00:57.360><c> but</c><00:00:57.840><c> rather</c><00:00:58.020><c> copying</c><00:00:58.500><c> the</c>

00:00:58.610 --> 00:00:58.620 align:start position:0%
many gigabytes but rather copying the
 

00:00:58.620 --> 00:01:00.830 align:start position:0%
many gigabytes but rather copying the
Manifest<00:00:59.100><c> that</c><00:00:59.340><c> references</c><00:00:59.820><c> them</c><00:01:00.000><c> this</c><00:01:00.719><c> will</c>

00:01:00.830 --> 00:01:00.840 align:start position:0%
Manifest that references them this will
 

00:01:00.840 --> 00:01:02.630 align:start position:0%
Manifest that references them this will
give<00:01:01.020><c> you</c><00:01:01.140><c> a</c><00:01:01.500><c> nice</c><00:01:01.559><c> way</c><00:01:01.800><c> to</c><00:01:01.980><c> change</c><00:01:02.219><c> the</c><00:01:02.520><c> name</c>

00:01:02.630 --> 00:01:02.640 align:start position:0%
give you a nice way to change the name
 

00:01:02.640 --> 00:01:05.810 align:start position:0%
give you a nice way to change the name
of<00:01:02.820><c> the</c><00:01:02.940><c> model</c><00:01:03.059><c> you</c><00:01:03.960><c> may</c><00:01:04.080><c> have</c><00:01:04.199><c> created</c><00:01:04.500><c> and</c><00:01:05.460><c> if</c>

00:01:05.810 --> 00:01:05.820 align:start position:0%
of the model you may have created and if
 

00:01:05.820 --> 00:01:07.910 align:start position:0%
of the model you may have created and if
you<00:01:06.000><c> remove</c><00:01:06.180><c> the</c><00:01:06.600><c> old</c><00:01:06.780><c> model</c><00:01:07.080><c> you</c><00:01:07.619><c> are</c><00:01:07.799><c> just</c>

00:01:07.910 --> 00:01:07.920 align:start position:0%
you remove the old model you are just
 

00:01:07.920 --> 00:01:10.250 align:start position:0%
you remove the old model you are just
deleting<00:01:08.340><c> the</c><00:01:08.580><c> Manifest</c><00:01:09.000><c> that</c><00:01:09.240><c> describes</c><00:01:09.720><c> it</c>

00:01:10.250 --> 00:01:10.260 align:start position:0%
deleting the Manifest that describes it
 

00:01:10.260 --> 00:01:12.530 align:start position:0%
deleting the Manifest that describes it
when<00:01:10.799><c> you</c><00:01:10.920><c> remove</c><00:01:11.100><c> the</c><00:01:11.460><c> last</c><00:01:11.640><c> Model</c><00:01:11.939><c> that</c>

00:01:12.530 --> 00:01:12.540 align:start position:0%
when you remove the last Model that
 

00:01:12.540 --> 00:01:14.450 align:start position:0%
when you remove the last Model that
references<00:01:12.960><c> one</c><00:01:13.200><c> of</c><00:01:13.320><c> those</c><00:01:13.380><c> big</c><00:01:13.560><c> files</c><00:01:13.920><c> then</c>

00:01:14.450 --> 00:01:14.460 align:start position:0%
references one of those big files then
 

00:01:14.460 --> 00:01:16.550 align:start position:0%
references one of those big files then
we<00:01:14.700><c> can</c><00:01:14.820><c> remove</c><00:01:15.000><c> those</c><00:01:15.420><c> big</c><00:01:15.600><c> files</c>

00:01:16.550 --> 00:01:16.560 align:start position:0%
we can remove those big files
 

00:01:16.560 --> 00:01:18.590 align:start position:0%
we can remove those big files
the<00:01:16.920><c> next</c><00:01:17.040><c> Improvement</c><00:01:17.520><c> I</c><00:01:17.820><c> want</c><00:01:17.939><c> to</c><00:01:18.060><c> cover</c><00:01:18.180><c> is</c>

00:01:18.590 --> 00:01:18.600 align:start position:0%
the next Improvement I want to cover is
 

00:01:18.600 --> 00:01:21.289 align:start position:0%
the next Improvement I want to cover is
support<00:01:18.780><c> for</c><00:01:19.080><c> running</c><00:01:19.200><c> K</c><00:01:19.500><c> Quant</c><00:01:19.920><c> models</c><00:01:20.460><c> many</c>

00:01:21.289 --> 00:01:21.299 align:start position:0%
support for running K Quant models many
 

00:01:21.299 --> 00:01:23.149 align:start position:0%
support for running K Quant models many
of<00:01:21.479><c> these</c><00:01:21.720><c> models</c><00:01:22.140><c> start</c><00:01:22.380><c> with</c><00:01:22.619><c> tokens</c>

00:01:23.149 --> 00:01:23.159 align:start position:0%
of these models start with tokens
 

00:01:23.159 --> 00:01:25.190 align:start position:0%
of these models start with tokens
represented<00:01:23.939><c> by</c><00:01:24.180><c> 16-bit</c><00:01:24.720><c> floating</c><00:01:25.020><c> Point</c>

00:01:25.190 --> 00:01:25.200 align:start position:0%
represented by 16-bit floating Point
 

00:01:25.200 --> 00:01:27.109 align:start position:0%
represented by 16-bit floating Point
numbers<00:01:25.500><c> this</c><00:01:26.280><c> provides</c><00:01:26.640><c> for</c><00:01:26.820><c> a</c><00:01:26.939><c> lot</c><00:01:27.000><c> of</c>

00:01:27.109 --> 00:01:27.119 align:start position:0%
numbers this provides for a lot of
 

00:01:27.119 --> 00:01:29.570 align:start position:0%
numbers this provides for a lot of
precision<00:01:27.420><c> but</c><00:01:27.900><c> at</c><00:01:28.320><c> the</c><00:01:28.500><c> cost</c><00:01:28.680><c> of</c><00:01:29.040><c> a</c><00:01:29.280><c> lot</c><00:01:29.400><c> of</c>

00:01:29.570 --> 00:01:29.580 align:start position:0%
precision but at the cost of a lot of
 

00:01:29.580 --> 00:01:31.490 align:start position:0%
precision but at the cost of a lot of
space<00:01:29.820><c> to</c><00:01:30.180><c> store</c><00:01:30.299><c> the</c><00:01:30.600><c> information</c>

00:01:31.490 --> 00:01:31.500 align:start position:0%
space to store the information
 

00:01:31.500 --> 00:01:34.550 align:start position:0%
space to store the information
quantization<00:01:32.420><c> reduces</c><00:01:33.420><c> this</c><00:01:33.720><c> Precision</c><00:01:34.140><c> to</c>

00:01:34.550 --> 00:01:34.560 align:start position:0%
quantization reduces this Precision to
 

00:01:34.560 --> 00:01:36.890 align:start position:0%
quantization reduces this Precision to
different<00:01:34.799><c> size</c><00:01:35.040><c> integers</c><00:01:35.700><c> 2-bit</c><00:01:36.420><c> 3-bit</c>

00:01:36.890 --> 00:01:36.900 align:start position:0%
different size integers 2-bit 3-bit
 

00:01:36.900 --> 00:01:39.530 align:start position:0%
different size integers 2-bit 3-bit
4-bit<00:01:37.380><c> 6-bit</c><00:01:37.860><c> and</c><00:01:37.979><c> others</c><00:01:38.400><c> to</c><00:01:38.880><c> save</c><00:01:39.060><c> the</c><00:01:39.299><c> space</c>

00:01:39.530 --> 00:01:39.540 align:start position:0%
4-bit 6-bit and others to save the space
 

00:01:39.540 --> 00:01:42.050 align:start position:0%
4-bit 6-bit and others to save the space
and<00:01:40.500><c> they're</c><00:01:40.920><c> often</c><00:01:41.280><c> still</c><00:01:41.400><c> surprisingly</c>

00:01:42.050 --> 00:01:42.060 align:start position:0%
and they're often still surprisingly
 

00:01:42.060 --> 00:01:44.749 align:start position:0%
and they're often still surprisingly
useful<00:01:42.420><c> more</c><00:01:43.320><c> recently</c><00:01:43.619><c> cakewant</c><00:01:44.340><c> models</c>

00:01:44.749 --> 00:01:44.759 align:start position:0%
useful more recently cakewant models
 

00:01:44.759 --> 00:01:46.249 align:start position:0%
useful more recently cakewant models
were<00:01:45.000><c> introduced</c><00:01:45.420><c> that</c><00:01:45.659><c> offer</c><00:01:45.960><c> a</c><00:01:46.140><c> more</c>

00:01:46.249 --> 00:01:46.259 align:start position:0%
were introduced that offer a more
 

00:01:46.259 --> 00:01:47.929 align:start position:0%
were introduced that offer a more
efficient<00:01:46.619><c> method</c><00:01:46.979><c> to</c><00:01:47.159><c> reduce</c><00:01:47.280><c> the</c><00:01:47.579><c> Precision</c>

00:01:47.929 --> 00:01:47.939 align:start position:0%
efficient method to reduce the Precision
 

00:01:47.939 --> 00:01:50.389 align:start position:0%
efficient method to reduce the Precision
resulting<00:01:48.780><c> in</c><00:01:49.079><c> even</c><00:01:49.439><c> smaller</c><00:01:49.799><c> models</c><00:01:50.159><c> while</c>

00:01:50.389 --> 00:01:50.399 align:start position:0%
resulting in even smaller models while
 

00:01:50.399 --> 00:01:52.550 align:start position:0%
resulting in even smaller models while
again<00:01:50.640><c> staying</c><00:01:51.119><c> effective</c><00:01:51.540><c> in</c><00:01:51.960><c> many</c><00:01:52.140><c> cases</c>

00:01:52.550 --> 00:01:52.560 align:start position:0%
again staying effective in many cases
 

00:01:52.560 --> 00:01:54.889 align:start position:0%
again staying effective in many cases
and<00:01:53.159><c> I'll</c><00:01:53.340><c> have</c><00:01:53.460><c> a</c><00:01:53.579><c> model</c><00:01:53.700><c> to</c><00:01:54.119><c> show</c><00:01:54.299><c> you</c><00:01:54.479><c> for</c>

00:01:54.889 --> 00:01:54.899 align:start position:0%
and I'll have a model to show you for
 

00:01:54.899 --> 00:01:56.929 align:start position:0%
and I'll have a model to show you for
this<00:01:55.140><c> but</c><00:01:55.500><c> we'll</c><00:01:55.920><c> be</c><00:01:56.100><c> adding</c><00:01:56.399><c> them</c><00:01:56.579><c> to</c><00:01:56.820><c> the</c>

00:01:56.929 --> 00:01:56.939 align:start position:0%
this but we'll be adding them to the
 

00:01:56.939 --> 00:01:59.210 align:start position:0%
this but we'll be adding them to the
registry<00:01:57.360><c> very</c><00:01:57.659><c> very</c><00:01:57.840><c> soon</c><00:01:58.140><c> the</c><00:01:58.979><c> important</c>

00:01:59.210 --> 00:01:59.220 align:start position:0%
registry very very soon the important
 

00:01:59.220 --> 00:02:01.429 align:start position:0%
registry very very soon the important
thing<00:01:59.460><c> is</c><00:01:59.939><c> that</c><00:02:00.180><c> we've</c><00:02:00.540><c> expanded</c><00:02:00.960><c> our</c><00:02:01.259><c> support</c>

00:02:01.429 --> 00:02:01.439 align:start position:0%
thing is that we've expanded our support
 

00:02:01.439 --> 00:02:04.370 align:start position:0%
thing is that we've expanded our support
for<00:02:02.040><c> different</c><00:02:02.340><c> types</c><00:02:02.700><c> of</c><00:02:02.759><c> models</c><00:02:03.600><c> next</c><00:02:04.140><c> are</c>

00:02:04.370 --> 00:02:04.380 align:start position:0%
for different types of models next are
 

00:02:04.380 --> 00:02:06.050 align:start position:0%
for different types of models next are
performance<00:02:04.799><c> improvements</c><00:02:05.280><c> from</c><00:02:05.520><c> enabling</c>

00:02:06.050 --> 00:02:06.060 align:start position:0%
performance improvements from enabling
 

00:02:06.060 --> 00:02:08.270 align:start position:0%
performance improvements from enabling
accelerate<00:02:06.840><c> accelerate</c><00:02:07.740><c> is</c><00:02:07.979><c> an</c><00:02:08.160><c> apple</c>

00:02:08.270 --> 00:02:08.280 align:start position:0%
accelerate accelerate is an apple
 

00:02:08.280 --> 00:02:10.370 align:start position:0%
accelerate accelerate is an apple
feature<00:02:08.759><c> that</c><00:02:08.940><c> provides</c><00:02:09.300><c> high</c><00:02:09.840><c> performance</c>

00:02:10.370 --> 00:02:10.380 align:start position:0%
feature that provides high performance
 

00:02:10.380 --> 00:02:12.830 align:start position:0%
feature that provides high performance
energy<00:02:10.739><c> efficient</c><00:02:11.280><c> computation</c><00:02:11.879><c> on</c><00:02:12.239><c> the</c><00:02:12.420><c> CPU</c>

00:02:12.830 --> 00:02:12.840 align:start position:0%
energy efficient computation on the CPU
 

00:02:12.840 --> 00:02:14.990 align:start position:0%
energy efficient computation on the CPU
by<00:02:13.200><c> leveraging</c><00:02:13.680><c> its</c><00:02:14.040><c> Vector</c><00:02:14.400><c> processing</c>

00:02:14.990 --> 00:02:15.000 align:start position:0%
by leveraging its Vector processing
 

00:02:15.000 --> 00:02:16.070 align:start position:0%
by leveraging its Vector processing
capability

00:02:16.070 --> 00:02:16.080 align:start position:0%
capability
 

00:02:16.080 --> 00:02:18.410 align:start position:0%
capability
we<00:02:16.560><c> can</c><00:02:16.680><c> see</c><00:02:16.800><c> this</c><00:02:16.980><c> by</c><00:02:17.220><c> running</c><00:02:17.400><c> llama2</c><00:02:18.120><c> model</c>

00:02:18.410 --> 00:02:18.420 align:start position:0%
we can see this by running llama2 model
 

00:02:18.420 --> 00:02:20.809 align:start position:0%
we can see this by running llama2 model
in<00:02:19.020><c> version</c><00:02:19.200><c> 11</c><00:02:19.440><c> and</c><00:02:19.860><c> seeing</c><00:02:20.160><c> that</c><00:02:20.220><c> the</c><00:02:20.340><c> CPU</c><00:02:20.700><c> is</c>

00:02:20.809 --> 00:02:20.819 align:start position:0%
in version 11 and seeing that the CPU is
 

00:02:20.819 --> 00:02:22.790 align:start position:0%
in version 11 and seeing that the CPU is
having<00:02:21.000><c> to</c><00:02:21.180><c> work</c><00:02:21.300><c> pretty</c><00:02:21.599><c> hard</c><00:02:21.900><c> to</c><00:02:22.440><c> evaluate</c>

00:02:22.790 --> 00:02:22.800 align:start position:0%
having to work pretty hard to evaluate
 

00:02:22.800 --> 00:02:25.490 align:start position:0%
having to work pretty hard to evaluate
the<00:02:22.980><c> prompt</c><00:02:23.280><c> the</c><00:02:24.000><c> answer</c><00:02:24.120><c> uses</c><00:02:24.599><c> the</c><00:02:24.780><c> GPU</c><00:02:25.260><c> and</c>

00:02:25.490 --> 00:02:25.500 align:start position:0%
the prompt the answer uses the GPU and
 

00:02:25.500 --> 00:02:27.650 align:start position:0%
the prompt the answer uses the GPU and
is<00:02:25.680><c> already</c><00:02:25.920><c> very</c><00:02:26.280><c> efficient</c><00:02:26.700><c> but</c><00:02:27.300><c> when</c><00:02:27.540><c> we</c>

00:02:27.650 --> 00:02:27.660 align:start position:0%
is already very efficient but when we
 

00:02:27.660 --> 00:02:29.570 align:start position:0%
is already very efficient but when we
switch<00:02:27.840><c> over</c><00:02:28.020><c> to</c><00:02:28.200><c> 12</c><00:02:28.440><c> and</c><00:02:29.040><c> run</c><00:02:29.220><c> the</c><00:02:29.400><c> same</c>

00:02:29.570 --> 00:02:29.580 align:start position:0%
switch over to 12 and run the same
 

00:02:29.580 --> 00:02:31.610 align:start position:0%
switch over to 12 and run the same
question<00:02:29.819><c> you'll</c><00:02:30.540><c> see</c><00:02:30.720><c> that</c><00:02:30.840><c> the</c><00:02:30.959><c> prompt</c><00:02:31.200><c> eval</c>

00:02:31.610 --> 00:02:31.620 align:start position:0%
question you'll see that the prompt eval
 

00:02:31.620 --> 00:02:35.330 align:start position:0%
question you'll see that the prompt eval
is<00:02:31.920><c> much</c><00:02:32.220><c> faster</c><00:02:32.640><c> and</c><00:02:33.239><c> used</c><00:02:33.540><c> much</c><00:02:33.959><c> less</c><00:02:34.140><c> CPU</c>

00:02:35.330 --> 00:02:35.340 align:start position:0%
is much faster and used much less CPU
 

00:02:35.340 --> 00:02:37.490 align:start position:0%
is much faster and used much less CPU
in<00:02:35.700><c> the</c><00:02:35.879><c> Discord</c><00:02:36.180><c> we</c><00:02:36.540><c> saw</c><00:02:36.720><c> a</c><00:02:36.900><c> few</c><00:02:37.020><c> folks</c><00:02:37.319><c> who</c>

00:02:37.490 --> 00:02:37.500 align:start position:0%
in the Discord we saw a few folks who
 

00:02:37.500 --> 00:02:39.650 align:start position:0%
in the Discord we saw a few folks who
are<00:02:37.620><c> trying</c><00:02:37.800><c> to</c><00:02:38.040><c> access</c><00:02:38.220><c> the</c><00:02:38.520><c> Alama</c><00:02:38.940><c> API</c><00:02:39.360><c> from</c>

00:02:39.650 --> 00:02:39.660 align:start position:0%
are trying to access the Alama API from
 

00:02:39.660 --> 00:02:41.990 align:start position:0%
are trying to access the Alama API from
a<00:02:39.840><c> website</c><00:02:40.140><c> if</c><00:02:40.860><c> you've</c><00:02:41.040><c> done</c><00:02:41.220><c> anything</c><00:02:41.640><c> like</c>

00:02:41.990 --> 00:02:42.000 align:start position:0%
a website if you've done anything like
 

00:02:42.000 --> 00:02:43.850 align:start position:0%
a website if you've done anything like
this<00:02:42.120><c> in</c><00:02:42.420><c> the</c><00:02:42.540><c> past</c><00:02:42.660><c> you</c><00:02:43.379><c> may</c><00:02:43.500><c> know</c><00:02:43.680><c> about</c>

00:02:43.850 --> 00:02:43.860 align:start position:0%
this in the past you may know about
 

00:02:43.860 --> 00:02:46.309 align:start position:0%
this in the past you may know about
cores<00:02:44.280><c> and</c><00:02:44.459><c> how</c><00:02:44.700><c> much</c><00:02:44.819><c> of</c><00:02:44.940><c> a</c><00:02:45.120><c> pain</c><00:02:45.300><c> it</c><00:02:45.540><c> is</c><00:02:45.720><c> but</c>

00:02:46.309 --> 00:02:46.319 align:start position:0%
cores and how much of a pain it is but
 

00:02:46.319 --> 00:02:48.770 align:start position:0%
cores and how much of a pain it is but
now<00:02:46.500><c> you</c><00:02:46.860><c> can</c><00:02:46.980><c> access</c><00:02:47.160><c> the</c><00:02:47.459><c> API</c><00:02:47.879><c> from</c><00:02:48.239><c> websites</c>

00:02:48.770 --> 00:02:48.780 align:start position:0%
now you can access the API from websites
 

00:02:48.780 --> 00:02:51.229 align:start position:0%
now you can access the API from websites
on<00:02:49.140><c> localhost</c><00:02:49.680><c> by</c><00:02:50.580><c> the</c><00:02:50.700><c> way</c><00:02:50.760><c> you</c><00:02:51.060><c> should</c><00:02:51.180><c> be</c>

00:02:51.229 --> 00:02:51.239 align:start position:0%
on localhost by the way you should be
 

00:02:51.239 --> 00:02:52.910 align:start position:0%
on localhost by the way you should be
using<00:02:51.480><c> the</c><00:02:51.660><c> olama</c><00:02:52.019><c> Discord</c><00:02:52.379><c> if</c><00:02:52.680><c> you're</c><00:02:52.800><c> not</c>

00:02:52.910 --> 00:02:52.920 align:start position:0%
using the olama Discord if you're not
 

00:02:52.920 --> 00:02:55.009 align:start position:0%
using the olama Discord if you're not
and<00:02:53.580><c> the</c><00:02:53.819><c> last</c><00:02:53.940><c> of</c><00:02:54.120><c> the</c><00:02:54.180><c> improvements</c><00:02:54.720><c> is</c>

00:02:55.009 --> 00:02:55.019 align:start position:0%
and the last of the improvements is
 

00:02:55.019 --> 00:02:57.589 align:start position:0%
and the last of the improvements is
around<00:02:55.260><c> create</c><00:02:55.680><c> when</c><00:02:56.519><c> you</c><00:02:56.640><c> make</c><00:02:56.819><c> a</c><00:02:57.000><c> model</c><00:02:57.180><c> file</c>

00:02:57.589 --> 00:02:57.599 align:start position:0%
around create when you make a model file
 

00:02:57.599 --> 00:03:00.470 align:start position:0%
around create when you make a model file
the<00:02:57.959><c> first</c><00:02:58.080><c> block</c><00:02:58.319><c> is</c><00:02:58.739><c> a</c><00:02:58.980><c> from</c><00:02:59.160><c> instruction</c><00:02:59.580><c> in</c>

00:03:00.470 --> 00:03:00.480 align:start position:0%
the first block is a from instruction in
 

00:03:00.480 --> 00:03:02.750 align:start position:0%
the first block is a from instruction in
alarm<00:03:00.720><c> 11</c><00:03:01.019><c> if</c><00:03:01.500><c> you</c><00:03:01.620><c> try</c><00:03:01.800><c> to</c><00:03:02.040><c> create</c><00:03:02.220><c> this</c><00:03:02.580><c> model</c>

00:03:02.750 --> 00:03:02.760 align:start position:0%
alarm 11 if you try to create this model
 

00:03:02.760 --> 00:03:04.550 align:start position:0%
alarm 11 if you try to create this model
from<00:03:03.239><c> a</c><00:03:03.480><c> model</c><00:03:03.599><c> that's</c><00:03:03.900><c> already</c><00:03:04.200><c> in</c><00:03:04.440><c> the</c>

00:03:04.550 --> 00:03:04.560 align:start position:0%
from a model that's already in the
 

00:03:04.560 --> 00:03:06.350 align:start position:0%
from a model that's already in the
registry<00:03:04.980><c> and</c><00:03:05.280><c> it's</c><00:03:05.640><c> not</c><00:03:05.819><c> already</c><00:03:05.940><c> on</c><00:03:06.239><c> your</c>

00:03:06.350 --> 00:03:06.360 align:start position:0%
registry and it's not already on your
 

00:03:06.360 --> 00:03:08.750 align:start position:0%
registry and it's not already on your
machine<00:03:06.599><c> it</c><00:03:07.260><c> would</c><00:03:07.379><c> fail</c><00:03:07.739><c> because</c><00:03:08.160><c> you</c><00:03:08.640><c> need</c>

00:03:08.750 --> 00:03:08.760 align:start position:0%
machine it would fail because you need
 

00:03:08.760 --> 00:03:11.690 align:start position:0%
machine it would fail because you need
to<00:03:09.000><c> pull</c><00:03:09.239><c> the</c><00:03:09.480><c> model</c><00:03:09.599><c> first</c><00:03:10.099><c> now</c><00:03:11.099><c> we</c><00:03:11.519><c> will</c>

00:03:11.690 --> 00:03:11.700 align:start position:0%
to pull the model first now we will
 

00:03:11.700 --> 00:03:13.850 align:start position:0%
to pull the model first now we will
pulled<00:03:12.180><c> for</c><00:03:12.540><c> you</c><00:03:12.720><c> if</c><00:03:13.019><c> the</c><00:03:13.200><c> model</c><00:03:13.319><c> hasn't</c>

00:03:13.850 --> 00:03:13.860 align:start position:0%
pulled for you if the model hasn't
 

00:03:13.860 --> 00:03:15.530 align:start position:0%
pulled for you if the model hasn't
already<00:03:13.980><c> been</c><00:03:14.220><c> pulled</c><00:03:14.640><c> this</c><00:03:15.180><c> is</c><00:03:15.300><c> a</c><00:03:15.420><c> much</c>

00:03:15.530 --> 00:03:15.540 align:start position:0%
already been pulled this is a much
 

00:03:15.540 --> 00:03:17.809 align:start position:0%
already been pulled this is a much
better<00:03:15.780><c> new</c><00:03:16.379><c> user</c><00:03:16.800><c> experience</c>

00:03:17.809 --> 00:03:17.819 align:start position:0%
better new user experience
 

00:03:17.819 --> 00:03:19.970 align:start position:0%
better new user experience
finally<00:03:18.480><c> there</c><00:03:18.840><c> were</c><00:03:18.959><c> a</c><00:03:19.080><c> bunch</c><00:03:19.200><c> of</c><00:03:19.319><c> bug</c><00:03:19.500><c> fixes</c>

00:03:19.970 --> 00:03:19.980 align:start position:0%
finally there were a bunch of bug fixes
 

00:03:19.980 --> 00:03:22.130 align:start position:0%
finally there were a bunch of bug fixes
which<00:03:20.340><c> are</c><00:03:20.519><c> all</c><00:03:20.700><c> great</c><00:03:20.940><c> to</c><00:03:21.120><c> see</c><00:03:21.239><c> and</c><00:03:21.959><c> that's</c>

00:03:22.130 --> 00:03:22.140 align:start position:0%
which are all great to see and that's
 

00:03:22.140 --> 00:03:23.949 align:start position:0%
which are all great to see and that's
what's<00:03:22.379><c> new</c><00:03:22.680><c> in</c><00:03:22.980><c> version</c>

00:03:23.949 --> 00:03:23.959 align:start position:0%
what's new in version
 

00:03:23.959 --> 00:03:27.410 align:start position:0%
what's new in version
0.0.12<00:03:24.959><c> of</c><00:03:25.319><c> olama</c><00:03:25.860><c> you</c><00:03:26.459><c> can</c><00:03:26.519><c> find</c><00:03:26.940><c> out</c><00:03:27.120><c> more</c>

00:03:27.410 --> 00:03:27.420 align:start position:0%
0.0.12 of olama you can find out more
 

00:03:27.420 --> 00:03:30.229 align:start position:0%
0.0.12 of olama you can find out more
here<00:03:27.840><c> at</c><00:03:28.140><c> olamata</c><00:03:28.860><c> Ai</c><00:03:29.159><c> and</c><00:03:29.700><c> we</c><00:03:29.819><c> can't</c><00:03:29.940><c> wait</c><00:03:30.120><c> to</c>

00:03:30.229 --> 00:03:30.239 align:start position:0%
here at olamata Ai and we can't wait to
 

00:03:30.239 --> 00:03:31.850 align:start position:0%
here at olamata Ai and we can't wait to
hear<00:03:30.420><c> of</c><00:03:30.599><c> how</c><00:03:30.780><c> you</c><00:03:31.019><c> are</c><00:03:31.140><c> using</c><00:03:31.500><c> the</c><00:03:31.620><c> tool</c>

00:03:31.850 --> 00:03:31.860 align:start position:0%
hear of how you are using the tool
 

00:03:31.860 --> 00:03:35.720 align:start position:0%
hear of how you are using the tool
thanks<00:03:32.159><c> so</c><00:03:32.340><c> much</c><00:03:32.459><c> for</c><00:03:32.580><c> watching</c><00:03:32.940><c> bye</c>

