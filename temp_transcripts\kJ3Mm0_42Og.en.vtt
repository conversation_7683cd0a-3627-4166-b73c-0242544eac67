WEBVTT
Kind: captions
Language: en

00:00:00.730 --> 00:00:05.150 align:start position:0%
 
[Music]

00:00:05.150 --> 00:00:05.160 align:start position:0%
 
 

00:00:05.160 --> 00:00:08.350 align:start position:0%
 
let's<00:00:05.400><c> take</c><00:00:05.520><c> a</c><00:00:05.680><c> look</c><00:00:05.960><c> now</c><00:00:06.480><c> at</c><00:00:06.759><c> our</c><00:00:07.120><c> evil.</c><00:00:08.000><c> PI</c>

00:00:08.350 --> 00:00:08.360 align:start position:0%
let's take a look now at our evil. PI
 

00:00:08.360 --> 00:00:12.110 align:start position:0%
let's take a look now at our evil. PI
file<00:00:09.040><c> where</c><00:00:09.240><c> we</c><00:00:09.440><c> Implement</c><00:00:10.120><c> our</c><00:00:10.519><c> evaluation</c>

00:00:12.110 --> 00:00:12.120 align:start position:0%
file where we Implement our evaluation
 

00:00:12.120 --> 00:00:14.629 align:start position:0%
file where we Implement our evaluation
script<00:00:13.120><c> we</c><00:00:13.240><c> will</c><00:00:13.440><c> start</c><00:00:13.759><c> by</c><00:00:13.880><c> loading</c><00:00:14.360><c> our</c>

00:00:14.629 --> 00:00:14.639 align:start position:0%
script we will start by loading our
 

00:00:14.639 --> 00:00:17.189 align:start position:0%
script we will start by loading our
evaluation<00:00:15.160><c> data</c><00:00:15.480><c> set</c><00:00:16.240><c> in</c><00:00:16.400><c> this</c><00:00:16.600><c> case</c><00:00:16.880><c> we</c><00:00:16.960><c> will</c>

00:00:17.189 --> 00:00:17.199 align:start position:0%
evaluation data set in this case we will
 

00:00:17.199 --> 00:00:20.390 align:start position:0%
evaluation data set in this case we will
use<00:00:17.720><c> the</c><00:00:18.279><c> modelbased</c><00:00:19.279><c> um</c><00:00:19.480><c> evaluation</c>

00:00:20.390 --> 00:00:20.400 align:start position:0%
use the modelbased um evaluation
 

00:00:20.400 --> 00:00:22.750 align:start position:0%
use the modelbased um evaluation
approach<00:00:21.400><c> and</c><00:00:21.560><c> we</c><00:00:21.680><c> will</c><00:00:21.880><c> start</c><00:00:22.119><c> with</c><00:00:22.279><c> the</c><00:00:22.480><c> data</c>

00:00:22.750 --> 00:00:22.760 align:start position:0%
approach and we will start with the data
 

00:00:22.760 --> 00:00:26.029 align:start position:0%
approach and we will start with the data
set<00:00:23.080><c> that</c><00:00:23.240><c> we</c><00:00:23.439><c> generated</c><00:00:24.400><c> synthetically</c><00:00:25.400><c> in</c><00:00:25.840><c> a</c>

00:00:26.029 --> 00:00:26.039 align:start position:0%
set that we generated synthetically in a
 

00:00:26.039 --> 00:00:27.230 align:start position:0%
set that we generated synthetically in a
previous

00:00:27.230 --> 00:00:27.240 align:start position:0%
previous
 

00:00:27.240 --> 00:00:30.109 align:start position:0%
previous
module<00:00:28.240><c> we</c><00:00:28.359><c> save</c><00:00:28.640><c> the</c><00:00:28.840><c> data</c><00:00:29.080><c> set</c><00:00:29.240><c> as</c><00:00:29.359><c> a</c><00:00:29.480><c> we</c>

00:00:30.109 --> 00:00:30.119 align:start position:0%
module we save the data set as a we
 

00:00:30.119 --> 00:00:33.069 align:start position:0%
module we save the data set as a we
artifact<00:00:31.080><c> so</c><00:00:31.359><c> that</c><00:00:31.720><c> we</c><00:00:31.840><c> can</c><00:00:32.079><c> make</c><00:00:32.239><c> sure</c><00:00:32.680><c> we</c><00:00:32.840><c> can</c>

00:00:33.069 --> 00:00:33.079 align:start position:0%
artifact so that we can make sure we can
 

00:00:33.079 --> 00:00:35.590 align:start position:0%
artifact so that we can make sure we can
track<00:00:33.399><c> the</c><00:00:33.680><c> lineage</c><00:00:34.440><c> of</c><00:00:34.600><c> the</c><00:00:34.800><c> data</c><00:00:35.120><c> set</c><00:00:35.360><c> as</c><00:00:35.480><c> we</c>

00:00:35.590 --> 00:00:35.600 align:start position:0%
track the lineage of the data set as we
 

00:00:35.600 --> 00:00:37.670 align:start position:0%
track the lineage of the data set as we
do<00:00:35.760><c> the</c><00:00:35.920><c> evaluation</c><00:00:36.760><c> and</c><00:00:36.920><c> in</c><00:00:37.079><c> case</c><00:00:37.239><c> we</c><00:00:37.360><c> update</c>

00:00:37.670 --> 00:00:37.680 align:start position:0%
do the evaluation and in case we update
 

00:00:37.680 --> 00:00:40.709 align:start position:0%
do the evaluation and in case we update
that<00:00:37.840><c> data</c><00:00:38.120><c> set</c><00:00:38.360><c> we</c><00:00:38.440><c> can</c><00:00:38.760><c> also</c><00:00:39.680><c> uh</c><00:00:39.840><c> control</c><00:00:40.520><c> the</c>

00:00:40.709 --> 00:00:40.719 align:start position:0%
that data set we can also uh control the
 

00:00:40.719 --> 00:00:45.310 align:start position:0%
that data set we can also uh control the
version<00:00:41.079><c> of</c><00:00:41.200><c> the</c><00:00:41.360><c> data</c><00:00:41.640><c> set</c><00:00:41.800><c> that</c><00:00:41.920><c> we're</c>

00:00:45.310 --> 00:00:45.320 align:start position:0%
 
 

00:00:45.320 --> 00:00:48.510 align:start position:0%
 
evaluating<00:00:46.320><c> we</c><00:00:46.440><c> will</c><00:00:46.680><c> use</c><00:00:47.039><c> our</c><00:00:47.559><c> QA</c><00:00:47.920><c> chain</c><00:00:48.320><c> the</c>

00:00:48.510 --> 00:00:48.520 align:start position:0%
evaluating we will use our QA chain the
 

00:00:48.520 --> 00:00:50.189 align:start position:0%
evaluating we will use our QA chain the
conversational<00:00:49.280><c> retrieval</c><00:00:49.760><c> chain</c><00:00:50.079><c> that</c>

00:00:50.189 --> 00:00:50.199 align:start position:0%
conversational retrieval chain that
 

00:00:50.199 --> 00:00:53.069 align:start position:0%
conversational retrieval chain that
we've<00:00:50.440><c> also</c><00:00:50.719><c> exposed</c><00:00:51.160><c> in</c><00:00:51.239><c> our</c><00:00:51.640><c> application</c><00:00:52.640><c> to</c>

00:00:53.069 --> 00:00:53.079 align:start position:0%
we've also exposed in our application to
 

00:00:53.079 --> 00:00:55.510 align:start position:0%
we've also exposed in our application to
generate<00:00:53.600><c> answers</c><00:00:54.480><c> to</c><00:00:54.680><c> our</c><00:00:54.960><c> synthetic</c>

00:00:55.510 --> 00:00:55.520 align:start position:0%
generate answers to our synthetic
 

00:00:55.520 --> 00:00:57.670 align:start position:0%
generate answers to our synthetic
questions<00:00:56.320><c> and</c><00:00:56.520><c> remember</c><00:00:56.920><c> we</c><00:00:57.120><c> also</c><00:00:57.440><c> have</c>

00:00:57.670 --> 00:00:57.680 align:start position:0%
questions and remember we also have
 

00:00:57.680 --> 00:01:00.630 align:start position:0%
questions and remember we also have
anwers<00:00:58.120><c> generated</c><00:00:58.719><c> while</c><00:00:59.680><c> uh</c><00:00:59.960><c> synthetically</c>

00:01:00.630 --> 00:01:00.640 align:start position:0%
anwers generated while uh synthetically
 

00:01:00.640 --> 00:01:02.150 align:start position:0%
anwers generated while uh synthetically
creating<00:01:01.079><c> this</c><00:01:01.320><c> question</c><00:01:01.680><c> so</c><00:01:01.840><c> we</c><00:01:01.960><c> can</c>

00:01:02.150 --> 00:01:02.160 align:start position:0%
creating this question so we can
 

00:01:02.160 --> 00:01:04.789 align:start position:0%
creating this question so we can
evaluate<00:01:02.800><c> the</c><00:01:03.079><c> model</c><00:01:03.399><c> generated</c><00:01:04.000><c> answer</c>

00:01:04.789 --> 00:01:04.799 align:start position:0%
evaluate the model generated answer
 

00:01:04.799 --> 00:01:07.030 align:start position:0%
evaluate the model generated answer
versus<00:01:05.199><c> the</c><00:01:05.400><c> ideal</c><00:01:05.880><c> answer</c><00:01:06.680><c> that</c><00:01:06.799><c> we've</c>

00:01:07.030 --> 00:01:07.040 align:start position:0%
versus the ideal answer that we've
 

00:01:07.040 --> 00:01:08.670 align:start position:0%
versus the ideal answer that we've
already<00:01:07.520><c> defined</c>

00:01:08.670 --> 00:01:08.680 align:start position:0%
already defined
 

00:01:08.680 --> 00:01:11.630 align:start position:0%
already defined
before<00:01:09.680><c> in</c><00:01:09.880><c> a</c><00:01:10.320><c> different</c><00:01:10.680><c> approach</c><00:01:11.040><c> you</c><00:01:11.200><c> might</c>

00:01:11.630 --> 00:01:11.640 align:start position:0%
before in a different approach you might
 

00:01:11.640 --> 00:01:15.390 align:start position:0%
before in a different approach you might
pull<00:01:12.240><c> this</c><00:01:13.000><c> um</c><00:01:14.000><c> evaluation</c><00:01:14.640><c> questions</c><00:01:15.200><c> from</c>

00:01:15.390 --> 00:01:15.400 align:start position:0%
pull this um evaluation questions from
 

00:01:15.400 --> 00:01:17.429 align:start position:0%
pull this um evaluation questions from
your<00:01:15.600><c> Production</c><00:01:16.040><c> service</c><00:01:16.840><c> and</c><00:01:17.000><c> then</c><00:01:17.200><c> have</c>

00:01:17.429 --> 00:01:17.439 align:start position:0%
your Production service and then have
 

00:01:17.439 --> 00:01:19.710 align:start position:0%
your Production service and then have
human<00:01:17.840><c> annotators</c><00:01:18.799><c> create</c><00:01:19.119><c> the</c><00:01:19.280><c> ideal</c>

00:01:19.710 --> 00:01:19.720 align:start position:0%
human annotators create the ideal
 

00:01:19.720 --> 00:01:23.109 align:start position:0%
human annotators create the ideal
answers<00:01:20.640><c> in</c><00:01:20.880><c> this</c><00:01:21.360><c> um</c><00:01:21.960><c> example</c><00:01:22.479><c> we</c><00:01:22.600><c> will</c><00:01:22.880><c> work</c>

00:01:23.109 --> 00:01:23.119 align:start position:0%
answers in this um example we will work
 

00:01:23.119 --> 00:01:28.830 align:start position:0%
answers in this um example we will work
with<00:01:23.240><c> a</c><00:01:23.400><c> synthetic</c><00:01:23.799><c> data</c>

00:01:28.830 --> 00:01:28.840 align:start position:0%
 
 

00:01:28.840 --> 00:01:33.030 align:start position:0%
 
set<00:01:30.000><c> once</c><00:01:30.240><c> we</c><00:01:30.439><c> have</c><00:01:31.079><c> uh</c><00:01:31.240><c> a</c><00:01:31.439><c> set</c><00:01:31.840><c> of</c><00:01:32.759><c> uh</c>

00:01:33.030 --> 00:01:33.040 align:start position:0%
set once we have uh a set of uh
 

00:01:33.040 --> 00:01:35.550 align:start position:0%
set once we have uh a set of uh
questions<00:01:34.040><c> ideal</c><00:01:34.520><c> answers</c><00:01:35.040><c> and</c><00:01:35.200><c> the</c><00:01:35.320><c> model</c>

00:01:35.550 --> 00:01:35.560 align:start position:0%
questions ideal answers and the model
 

00:01:35.560 --> 00:01:37.990 align:start position:0%
questions ideal answers and the model
generated<00:01:36.159><c> answers</c><00:01:36.920><c> we</c><00:01:37.040><c> can</c><00:01:37.200><c> move</c><00:01:37.439><c> on</c><00:01:37.640><c> to</c>

00:01:37.990 --> 00:01:38.000 align:start position:0%
generated answers we can move on to
 

00:01:38.000 --> 00:01:41.190 align:start position:0%
generated answers we can move on to
evaluating<00:01:38.640><c> these</c><00:01:39.159><c> answers</c><00:01:40.159><c> and</c><00:01:40.320><c> for</c><00:01:40.560><c> that</c><00:01:41.040><c> uh</c>

00:01:41.190 --> 00:01:41.200 align:start position:0%
evaluating these answers and for that uh
 

00:01:41.200 --> 00:01:43.710 align:start position:0%
evaluating these answers and for that uh
purpose<00:01:41.560><c> we</c><00:01:41.680><c> will</c><00:01:41.840><c> use</c><00:01:42.200><c> QA</c><00:01:42.560><c> eval</c><00:01:42.880><c> chain</c><00:01:43.240><c> from</c><00:01:43.399><c> L</c>

00:01:43.710 --> 00:01:43.720 align:start position:0%
purpose we will use QA eval chain from L
 

00:01:43.720 --> 00:01:46.069 align:start position:0%
purpose we will use QA eval chain from L
chain<00:01:44.560><c> and</c><00:01:44.719><c> I</c><00:01:44.840><c> think</c><00:01:45.000><c> it's</c><00:01:45.200><c> helpful</c><00:01:45.560><c> to</c><00:01:45.719><c> look</c>

00:01:46.069 --> 00:01:46.079 align:start position:0%
chain and I think it's helpful to look
 

00:01:46.079 --> 00:01:47.789 align:start position:0%
chain and I think it's helpful to look
at<00:01:46.360><c> the</c><00:01:46.520><c> prompt</c><00:01:46.960><c> that</c><00:01:47.079><c> is</c><00:01:47.200><c> being</c><00:01:47.399><c> used</c><00:01:47.640><c> for</c>

00:01:47.789 --> 00:01:47.799 align:start position:0%
at the prompt that is being used for
 

00:01:47.799 --> 00:01:50.069 align:start position:0%
at the prompt that is being used for
this<00:01:48.000><c> evaluation</c><00:01:49.000><c> and</c><00:01:49.119><c> we're</c><00:01:49.320><c> loading</c><00:01:49.799><c> this</c>

00:01:50.069 --> 00:01:50.079 align:start position:0%
this evaluation and we're loading this
 

00:01:50.079 --> 00:01:51.749 align:start position:0%
this evaluation and we're loading this
from<00:01:50.439><c> prompts</c>

00:01:51.749 --> 00:01:51.759 align:start position:0%
from prompts
 

00:01:51.759 --> 00:01:54.149 align:start position:0%
from prompts
dopy<00:01:52.759><c> again</c><00:01:52.960><c> we're</c><00:01:53.159><c> using</c><00:01:53.439><c> a</c><00:01:53.600><c> chat</c><00:01:53.880><c> based</c>

00:01:54.149 --> 00:01:54.159 align:start position:0%
dopy again we're using a chat based
 

00:01:54.159 --> 00:01:56.469 align:start position:0%
dopy again we're using a chat based
model<00:01:54.520><c> for</c><00:01:54.719><c> this</c><00:01:54.920><c> evaluation</c><00:01:55.799><c> and</c><00:01:56.039><c> the</c><00:01:56.159><c> prompt</c>

00:01:56.469 --> 00:01:56.479 align:start position:0%
model for this evaluation and the prompt
 

00:01:56.479 --> 00:01:59.029 align:start position:0%
model for this evaluation and the prompt
consists<00:01:56.799><c> of</c><00:01:56.960><c> two</c><00:01:57.159><c> parts</c><00:01:58.000><c> the</c><00:01:58.280><c> system</c><00:01:58.640><c> message</c>

00:01:59.029 --> 00:01:59.039 align:start position:0%
consists of two parts the system message
 

00:01:59.039 --> 00:02:02.590 align:start position:0%
consists of two parts the system message
prompt<00:01:59.680><c> that</c><00:01:59.840><c> tells</c><00:02:00.399><c> the</c><00:02:00.640><c> llm</c><00:02:01.560><c> that</c><00:02:02.119><c> it</c><00:02:02.240><c> is</c><00:02:02.399><c> an</c>

00:02:02.590 --> 00:02:02.600 align:start position:0%
prompt that tells the llm that it is an
 

00:02:02.600 --> 00:02:06.630 align:start position:0%
prompt that tells the llm that it is an
evaluator<00:02:03.439><c> for</c><00:02:03.640><c> the</c><00:02:03.759><c> W</c><00:02:04.000><c> in</c><00:02:04.439><c> chatbot</c><00:02:05.439><c> that</c><00:02:05.840><c> um</c>

00:02:06.630 --> 00:02:06.640 align:start position:0%
evaluator for the W in chatbot that um
 

00:02:06.640 --> 00:02:08.510 align:start position:0%
evaluator for the W in chatbot that um
it's<00:02:06.840><c> given</c><00:02:07.079><c> a</c><00:02:07.240><c> question</c><00:02:07.680><c> the</c><00:02:07.799><c> chatbot</c><00:02:08.239><c> answer</c>

00:02:08.510 --> 00:02:08.520 align:start position:0%
it's given a question the chatbot answer
 

00:02:08.520 --> 00:02:11.190 align:start position:0%
it's given a question the chatbot answer
and<00:02:08.640><c> the</c><00:02:08.759><c> original</c><00:02:09.239><c> answer</c><00:02:10.039><c> and</c><00:02:10.399><c> are</c><00:02:10.679><c> asked</c><00:02:11.000><c> to</c>

00:02:11.190 --> 00:02:11.200 align:start position:0%
and the original answer and are asked to
 

00:02:11.200 --> 00:02:13.830 align:start position:0%
and the original answer and are asked to
score<00:02:11.720><c> the</c><00:02:11.879><c> chatbot</c><00:02:12.440><c> answer</c><00:02:13.239><c> as</c><00:02:13.520><c> either</c>

00:02:13.830 --> 00:02:13.840 align:start position:0%
score the chatbot answer as either
 

00:02:13.840 --> 00:02:15.309 align:start position:0%
score the chatbot answer as either
correct<00:02:14.440><c> or</c>

00:02:15.309 --> 00:02:15.319 align:start position:0%
correct or
 

00:02:15.319 --> 00:02:17.910 align:start position:0%
correct or
incorrect<00:02:16.319><c> and</c><00:02:16.480><c> then</c><00:02:16.959><c> in</c><00:02:17.080><c> the</c><00:02:17.239><c> human</c><00:02:17.519><c> template</c>

00:02:17.910 --> 00:02:17.920 align:start position:0%
incorrect and then in the human template
 

00:02:17.920 --> 00:02:19.949 align:start position:0%
incorrect and then in the human template
we<00:02:18.000><c> are</c><00:02:18.160><c> providing</c><00:02:18.640><c> the</c><00:02:18.840><c> actual</c><00:02:19.440><c> question</c>

00:02:19.949 --> 00:02:19.959 align:start position:0%
we are providing the actual question
 

00:02:19.959 --> 00:02:22.589 align:start position:0%
we are providing the actual question
from<00:02:20.200><c> our</c><00:02:20.519><c> evaluation</c><00:02:21.120><c> data</c><00:02:21.400><c> set</c><00:02:22.400><c> we're</c>

00:02:22.589 --> 00:02:22.599 align:start position:0%
from our evaluation data set we're
 

00:02:22.599 --> 00:02:24.430 align:start position:0%
from our evaluation data set we're
providing<00:02:22.959><c> the</c><00:02:23.080><c> chatboard</c><00:02:23.560><c> answer</c><00:02:23.920><c> the</c><00:02:24.040><c> model</c>

00:02:24.430 --> 00:02:24.440 align:start position:0%
providing the chatboard answer the model
 

00:02:24.440 --> 00:02:27.550 align:start position:0%
providing the chatboard answer the model
generated<00:02:25.000><c> answer</c><00:02:25.800><c> and</c><00:02:26.040><c> the</c><00:02:26.160><c> original</c><00:02:26.720><c> answer</c>

00:02:27.550 --> 00:02:27.560 align:start position:0%
generated answer and the original answer
 

00:02:27.560 --> 00:02:35.190 align:start position:0%
generated answer and the original answer
and<00:02:27.800><c> request</c><00:02:28.239><c> the</c><00:02:28.360><c> model</c><00:02:28.760><c> to</c><00:02:29.879><c> fill</c><00:02:30.200><c> in</c><00:02:30.720><c> the</c>

00:02:35.190 --> 00:02:35.200 align:start position:0%
 
 

00:02:35.200 --> 00:02:38.390 align:start position:0%
 
grade<00:02:36.200><c> so</c><00:02:36.519><c> here</c><00:02:36.680><c> we</c><00:02:36.920><c> process</c><00:02:37.280><c> our</c><00:02:37.959><c> again</c><00:02:38.160><c> our</c>

00:02:38.390 --> 00:02:38.400 align:start position:0%
grade so here we process our again our
 

00:02:38.400 --> 00:02:40.790 align:start position:0%
grade so here we process our again our
evaluation<00:02:38.920><c> data</c><00:02:39.200><c> set</c><00:02:39.760><c> pass</c><00:02:40.080><c> this</c><00:02:40.319><c> into</c><00:02:40.560><c> our</c>

00:02:40.790 --> 00:02:40.800 align:start position:0%
evaluation data set pass this into our
 

00:02:40.800 --> 00:02:44.430 align:start position:0%
evaluation data set pass this into our
evaluation<00:02:41.319><c> chain</c><00:02:42.200><c> and</c><00:02:42.480><c> then</c><00:02:43.200><c> save</c><00:02:43.640><c> this</c><00:02:44.239><c> in</c>

00:02:44.430 --> 00:02:44.440 align:start position:0%
evaluation chain and then save this in
 

00:02:44.440 --> 00:02:46.630 align:start position:0%
evaluation chain and then save this in
the<00:02:44.640><c> model</c><00:02:44.959><c> score</c><00:02:45.280><c> column</c><00:02:45.879><c> and</c><00:02:46.200><c> return</c><00:02:46.400><c> our</c>

00:02:46.630 --> 00:02:46.640 align:start position:0%
the model score column and return our
 

00:02:46.640 --> 00:02:49.149 align:start position:0%
the model score column and return our
evaluation<00:02:47.159><c> data</c><00:02:47.519><c> set</c><00:02:48.519><c> and</c><00:02:48.720><c> once</c><00:02:48.920><c> we've</c>

00:02:49.149 --> 00:02:49.159 align:start position:0%
evaluation data set and once we've
 

00:02:49.159 --> 00:02:51.750 align:start position:0%
evaluation data set and once we've
processed<00:02:50.159><c> uh</c><00:02:50.400><c> this</c><00:02:50.640><c> evaluation</c><00:02:51.159><c> we</c><00:02:51.239><c> can</c><00:02:51.400><c> look</c>

00:02:51.750 --> 00:02:51.760 align:start position:0%
processed uh this evaluation we can look
 

00:02:51.760 --> 00:02:55.390 align:start position:0%
processed uh this evaluation we can look
our<00:02:52.200><c> results</c><00:02:53.200><c> we</c><00:02:53.560><c> calculate</c><00:02:54.080><c> model</c><00:02:54.480><c> accuracy</c>

00:02:55.390 --> 00:02:55.400 align:start position:0%
our results we calculate model accuracy
 

00:02:55.400 --> 00:02:57.670 align:start position:0%
our results we calculate model accuracy
as<00:02:55.599><c> the</c><00:02:55.760><c> number</c><00:02:56.000><c> of</c><00:02:56.200><c> correct</c><00:02:56.640><c> answers</c><00:02:57.239><c> divided</c>

00:02:57.670 --> 00:02:57.680 align:start position:0%
as the number of correct answers divided
 

00:02:57.680 --> 00:02:59.869 align:start position:0%
as the number of correct answers divided
by<00:02:57.879><c> the</c><00:02:58.080><c> total</c><00:02:58.840><c> length</c><00:02:59.319><c> of</c><00:02:59.519><c> our</c><00:02:59.760><c> our</c>

00:02:59.869 --> 00:02:59.879 align:start position:0%
by the total length of our our
 

00:02:59.879 --> 00:03:01.869 align:start position:0%
by the total length of our our
evaluation<00:03:00.400><c> data</c><00:03:00.680><c> set</c><00:03:01.080><c> we</c><00:03:01.239><c> log</c><00:03:01.440><c> it</c><00:03:01.640><c> into</c>

00:03:01.869 --> 00:03:01.879 align:start position:0%
evaluation data set we log it into
 

00:03:01.879 --> 00:03:05.750 align:start position:0%
evaluation data set we log it into
weights<00:03:02.080><c> and</c><00:03:02.239><c> biases</c><00:03:03.239><c> and</c><00:03:03.480><c> we</c><00:03:03.640><c> also</c><00:03:03.920><c> log</c><00:03:04.760><c> the</c>

00:03:05.750 --> 00:03:05.760 align:start position:0%
weights and biases and we also log the
 

00:03:05.760 --> 00:03:08.550 align:start position:0%
weights and biases and we also log the
uh<00:03:05.920><c> evaluation</c><00:03:06.480><c> data</c><00:03:06.799><c> set</c><00:03:07.319><c> into</c><00:03:07.720><c> artifacts</c><00:03:08.360><c> so</c>

00:03:08.550 --> 00:03:08.560 align:start position:0%
uh evaluation data set into artifacts so
 

00:03:08.560 --> 00:03:09.990 align:start position:0%
uh evaluation data set into artifacts so
that<00:03:08.680><c> we</c><00:03:08.799><c> can</c><00:03:08.959><c> retrieve</c><00:03:09.400><c> for</c><00:03:09.640><c> further</c>

00:03:09.990 --> 00:03:10.000 align:start position:0%
that we can retrieve for further
 

00:03:10.000 --> 00:03:12.110 align:start position:0%
that we can retrieve for further
analysis<00:03:10.920><c> and</c><00:03:11.040><c> we</c><00:03:11.200><c> also</c><00:03:11.400><c> log</c><00:03:11.640><c> it</c><00:03:11.760><c> as</c><00:03:11.840><c> a</c><00:03:11.959><c> weight</c>

00:03:12.110 --> 00:03:12.120 align:start position:0%
analysis and we also log it as a weight
 

00:03:12.120 --> 00:03:15.869 align:start position:0%
analysis and we also log it as a weight
syis<00:03:12.599><c> table</c><00:03:13.239><c> for</c><00:03:13.599><c> interactive</c><00:03:14.879><c> exploration</c>

00:03:15.869 --> 00:03:15.879 align:start position:0%
syis table for interactive exploration
 

00:03:15.879 --> 00:03:19.350 align:start position:0%
syis table for interactive exploration
and<00:03:16.040><c> now</c><00:03:16.680><c> uh</c><00:03:16.879><c> we</c><00:03:17.200><c> can</c><00:03:17.599><c> run</c><00:03:17.879><c> off</c><00:03:18.200><c> all</c><00:03:18.319><c> of</c><00:03:18.560><c> this</c><00:03:19.040><c> um</c>

00:03:19.350 --> 00:03:19.360 align:start position:0%
and now uh we can run off all of this um
 

00:03:19.360 --> 00:03:21.910 align:start position:0%
and now uh we can run off all of this um
under<00:03:19.640><c> a</c><00:03:19.799><c> new</c><00:03:20.000><c> weight</c><00:03:20.239><c> and</c><00:03:20.400><c> bies</c><00:03:20.720><c> run</c><00:03:21.480><c> and</c><00:03:21.680><c> see</c>

00:03:21.910 --> 00:03:21.920 align:start position:0%
under a new weight and bies run and see
 

00:03:21.920 --> 00:03:23.390 align:start position:0%
under a new weight and bies run and see
the<00:03:22.040><c> results</c><00:03:22.360><c> in</c><00:03:22.519><c> weights</c><00:03:22.760><c> and</c><00:03:22.920><c> biases</c><00:03:23.280><c> and</c>

00:03:23.390 --> 00:03:23.400 align:start position:0%
the results in weights and biases and
 

00:03:23.400 --> 00:03:28.670 align:start position:0%
the results in weights and biases and
we'll<00:03:23.560><c> do</c><00:03:23.799><c> that</c><00:03:24.319><c> in</c><00:03:24.440><c> the</c><00:03:24.599><c> next</c>

00:03:28.670 --> 00:03:28.680 align:start position:0%
 
 

00:03:28.680 --> 00:03:31.680 align:start position:0%
 
video

