WEBVTT
Kind: captions
Language: en

00:00:00.719 --> 00:00:03.110 align:start position:0%
 
today<00:00:01.380><c> we're</c><00:00:01.740><c> going</c><00:00:01.979><c> to</c><00:00:02.100><c> be</c><00:00:02.159><c> going</c><00:00:02.399><c> over</c><00:00:02.700><c> quick</c>

00:00:03.110 --> 00:00:03.120 align:start position:0%
today we're going to be going over quick
 

00:00:03.120 --> 00:00:05.329 align:start position:0%
today we're going to be going over quick
sort<00:00:03.540><c> and</c><00:00:04.200><c> we</c><00:00:04.380><c> are</c><00:00:04.560><c> walking</c><00:00:04.740><c> through</c><00:00:05.040><c> each</c>

00:00:05.329 --> 00:00:05.339 align:start position:0%
sort and we are walking through each
 

00:00:05.339 --> 00:00:06.950 align:start position:0%
sort and we are walking through each
step<00:00:05.520><c> while</c><00:00:05.880><c> highlighting</c><00:00:06.540><c> the</c><00:00:06.839><c> code</c>

00:00:06.950 --> 00:00:06.960 align:start position:0%
step while highlighting the code
 

00:00:06.960 --> 00:00:09.710 align:start position:0%
step while highlighting the code
execution<00:00:07.500><c> also</c><00:00:08.160><c> using</c><00:00:08.460><c> recursion</c><00:00:09.000><c> we</c><00:00:09.599><c> begin</c>

00:00:09.710 --> 00:00:09.720 align:start position:0%
execution also using recursion we begin
 

00:00:09.720 --> 00:00:13.730 align:start position:0%
execution also using recursion we begin
with<00:00:10.019><c> the</c><00:00:10.139><c> array</c><00:00:10.880><c> 2943</c><00:00:11.880><c> and</c><00:00:12.120><c> 7.</c><00:00:12.559><c> executing</c><00:00:13.559><c> the</c>

00:00:13.730 --> 00:00:13.740 align:start position:0%
with the array 2943 and 7. executing the
 

00:00:13.740 --> 00:00:15.530 align:start position:0%
with the array 2943 and 7. executing the
main<00:00:13.920><c> method</c><00:00:14.219><c> calling</c><00:00:14.700><c> quick</c><00:00:14.880><c> sort</c><00:00:15.179><c> on</c><00:00:15.420><c> the</c>

00:00:15.530 --> 00:00:15.540 align:start position:0%
main method calling quick sort on the
 

00:00:15.540 --> 00:00:18.650 align:start position:0%
main method calling quick sort on the
array<00:00:15.839><c> in</c><00:00:16.320><c> the</c><00:00:16.440><c> parameter</c><00:00:16.920><c> 0</c><00:00:17.220><c> and</c><00:00:17.760><c> 4</c><00:00:18.060><c> which</c><00:00:18.420><c> of</c>

00:00:18.650 --> 00:00:18.660 align:start position:0%
array in the parameter 0 and 4 which of
 

00:00:18.660 --> 00:00:20.929 align:start position:0%
array in the parameter 0 and 4 which of
the<00:00:18.779><c> array</c><00:00:19.080><c> length</c><00:00:19.619><c> minus</c><00:00:19.980><c> one</c><00:00:20.160><c> the</c><00:00:20.460><c> main</c><00:00:20.640><c> idea</c>

00:00:20.929 --> 00:00:20.939 align:start position:0%
the array length minus one the main idea
 

00:00:20.939 --> 00:00:22.490 align:start position:0%
the array length minus one the main idea
of<00:00:21.119><c> this</c><00:00:21.240><c> quick</c><00:00:21.420><c> sort</c><00:00:21.720><c> method</c><00:00:22.080><c> is</c><00:00:22.320><c> to</c>

00:00:22.490 --> 00:00:22.500 align:start position:0%
of this quick sort method is to
 

00:00:22.500 --> 00:00:24.230 align:start position:0%
of this quick sort method is to
recursively<00:00:23.100><c> divide</c><00:00:23.520><c> the</c><00:00:23.699><c> array</c><00:00:24.000><c> into</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
recursively divide the array into
 

00:00:24.240 --> 00:00:26.330 align:start position:0%
recursively divide the array into
smaller<00:00:24.720><c> subarrays</c><00:00:25.320><c> based</c><00:00:25.740><c> on</c><00:00:25.859><c> a</c><00:00:26.039><c> pivot</c>

00:00:26.330 --> 00:00:26.340 align:start position:0%
smaller subarrays based on a pivot
 

00:00:26.340 --> 00:00:28.070 align:start position:0%
smaller subarrays based on a pivot
element<00:00:26.519><c> for</c><00:00:27.060><c> sorting</c>

00:00:28.070 --> 00:00:28.080 align:start position:0%
element for sorting
 

00:00:28.080 --> 00:00:30.950 align:start position:0%
element for sorting
since<00:00:28.680><c> 0</c><00:00:29.099><c> is</c><00:00:29.460><c> less</c><00:00:29.699><c> than</c><00:00:29.880><c> four</c><00:00:30.060><c> which</c><00:00:30.539><c> is</c><00:00:30.660><c> the</c>

00:00:30.950 --> 00:00:30.960 align:start position:0%
since 0 is less than four which is the
 

00:00:30.960 --> 00:00:32.870 align:start position:0%
since 0 is less than four which is the
low<00:00:31.140><c> less</c><00:00:31.500><c> than</c><00:00:31.679><c> the</c><00:00:31.800><c> high</c><00:00:31.980><c> we</c><00:00:32.279><c> go</c><00:00:32.460><c> into</c><00:00:32.640><c> the</c>

00:00:32.870 --> 00:00:32.880 align:start position:0%
low less than the high we go into the
 

00:00:32.880 --> 00:00:34.490 align:start position:0%
low less than the high we go into the
partition<00:00:33.239><c> method</c><00:00:33.719><c> with</c><00:00:34.020><c> the</c><00:00:34.380><c> same</c>

00:00:34.490 --> 00:00:34.500 align:start position:0%
partition method with the same
 

00:00:34.500 --> 00:00:36.229 align:start position:0%
partition method with the same
parameters<00:00:35.040><c> the</c><00:00:35.460><c> idea</c><00:00:35.760><c> of</c><00:00:35.880><c> the</c><00:00:36.000><c> partition</c>

00:00:36.229 --> 00:00:36.239 align:start position:0%
parameters the idea of the partition
 

00:00:36.239 --> 00:00:38.930 align:start position:0%
parameters the idea of the partition
method<00:00:36.719><c> is</c><00:00:36.960><c> to</c><00:00:37.200><c> take</c><00:00:37.380><c> any</c><00:00:38.040><c> elements</c><00:00:38.520><c> less</c><00:00:38.820><c> than</c>

00:00:38.930 --> 00:00:38.940 align:start position:0%
method is to take any elements less than
 

00:00:38.940 --> 00:00:40.549 align:start position:0%
method is to take any elements less than
the<00:00:39.059><c> pivot</c><00:00:39.420><c> and</c><00:00:39.600><c> move</c><00:00:39.780><c> them</c><00:00:39.960><c> to</c><00:00:40.140><c> the</c><00:00:40.260><c> left</c><00:00:40.379><c> of</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
the pivot and move them to the left of
 

00:00:40.559 --> 00:00:42.470 align:start position:0%
the pivot and move them to the left of
the<00:00:40.680><c> pivot</c><00:00:40.980><c> and</c><00:00:41.520><c> any</c><00:00:41.760><c> that</c><00:00:41.940><c> are</c><00:00:42.059><c> greater</c><00:00:42.360><c> than</c>

00:00:42.470 --> 00:00:42.480 align:start position:0%
the pivot and any that are greater than
 

00:00:42.480 --> 00:00:44.690 align:start position:0%
the pivot and any that are greater than
or<00:00:42.660><c> equal</c><00:00:43.020><c> to</c><00:00:43.379><c> the</c><00:00:43.500><c> pivot</c><00:00:43.860><c> move</c><00:00:44.219><c> it</c><00:00:44.399><c> to</c><00:00:44.579><c> the</c>

00:00:44.690 --> 00:00:44.700 align:start position:0%
or equal to the pivot move it to the
 

00:00:44.700 --> 00:00:46.610 align:start position:0%
or equal to the pivot move it to the
right<00:00:44.820><c> now</c><00:00:45.239><c> going</c><00:00:45.480><c> into</c><00:00:45.660><c> the</c><00:00:45.840><c> for</c><00:00:45.960><c> Loop</c><00:00:46.260><c> J</c>

00:00:46.610 --> 00:00:46.620 align:start position:0%
right now going into the for Loop J
 

00:00:46.620 --> 00:00:49.670 align:start position:0%
right now going into the for Loop J
starts<00:00:46.980><c> out</c><00:00:47.100><c> at</c><00:00:47.219><c> zero</c><00:00:48.000><c> is</c><00:00:48.539><c> two</c><00:00:48.960><c> less</c><00:00:49.379><c> than</c>

00:00:49.670 --> 00:00:49.680 align:start position:0%
starts out at zero is two less than
 

00:00:49.680 --> 00:00:52.190 align:start position:0%
starts out at zero is two less than
seven<00:00:50.160><c> in</c><00:00:50.940><c> this</c><00:00:51.059><c> case</c><00:00:51.180><c> it</c><00:00:51.420><c> is</c><00:00:51.600><c> so</c><00:00:51.960><c> we're</c><00:00:52.079><c> going</c>

00:00:52.190 --> 00:00:52.200 align:start position:0%
seven in this case it is so we're going
 

00:00:52.200 --> 00:00:54.709 align:start position:0%
seven in this case it is so we're going
to<00:00:52.379><c> increment</c><00:00:52.860><c> I</c><00:00:53.160><c> and</c><00:00:53.520><c> then</c><00:00:53.640><c> swap</c><00:00:54.120><c> the</c><00:00:54.480><c> values</c>

00:00:54.709 --> 00:00:54.719 align:start position:0%
to increment I and then swap the values
 

00:00:54.719 --> 00:00:57.590 align:start position:0%
to increment I and then swap the values
at<00:00:54.960><c> I</c><00:00:55.079><c> and</c><00:00:55.260><c> J</c><00:00:55.440><c> well</c><00:00:56.100><c> I</c><00:00:56.460><c> and</c><00:00:56.640><c> J</c><00:00:56.820><c> will</c><00:00:57.180><c> both</c><00:00:57.420><c> be</c>

00:00:57.590 --> 00:00:57.600 align:start position:0%
at I and J well I and J will both be
 

00:00:57.600 --> 00:00:59.330 align:start position:0%
at I and J well I and J will both be
equal<00:00:57.719><c> to</c><00:00:57.960><c> zero</c><00:00:58.320><c> so</c><00:00:58.559><c> we</c><00:00:58.739><c> just</c><00:00:58.860><c> swap</c><00:00:58.980><c> two</c><00:00:59.160><c> with</c>

00:00:59.330 --> 00:00:59.340 align:start position:0%
equal to zero so we just swap two with
 

00:00:59.340 --> 00:01:00.709 align:start position:0%
equal to zero so we just swap two with
the<00:00:59.520><c> self</c><00:00:59.640><c> and</c><00:00:59.879><c> it</c><00:01:00.000><c> just</c><00:01:00.180><c> stays</c><00:01:00.420><c> in</c><00:01:00.539><c> the</c><00:01:00.600><c> same</c>

00:01:00.709 --> 00:01:00.719 align:start position:0%
the self and it just stays in the same
 

00:01:00.719 --> 00:01:02.869 align:start position:0%
the self and it just stays in the same
place<00:01:00.840><c> increment</c><00:01:01.440><c> J</c><00:01:01.680><c> now</c><00:01:02.039><c> we</c><00:01:02.219><c> ask</c><00:01:02.399><c> is</c><00:01:02.640><c> nine</c>

00:01:02.869 --> 00:01:02.879 align:start position:0%
place increment J now we ask is nine
 

00:01:02.879 --> 00:01:05.509 align:start position:0%
place increment J now we ask is nine
less<00:01:03.120><c> than</c><00:01:03.239><c> seven</c><00:01:03.480><c> it</c><00:01:04.080><c> is</c><00:01:04.199><c> not</c><00:01:04.440><c> so</c><00:01:04.799><c> we</c><00:01:05.040><c> don't</c><00:01:05.220><c> do</c>

00:01:05.509 --> 00:01:05.519 align:start position:0%
less than seven it is not so we don't do
 

00:01:05.519 --> 00:01:08.750 align:start position:0%
less than seven it is not so we don't do
anything<00:01:05.700><c> we</c><00:01:06.659><c> just</c><00:01:06.840><c> increment</c><00:01:07.320><c> J</c><00:01:07.680><c> now</c><00:01:08.280><c> as</c><00:01:08.520><c> four</c>

00:01:08.750 --> 00:01:08.760 align:start position:0%
anything we just increment J now as four
 

00:01:08.760 --> 00:01:11.330 align:start position:0%
anything we just increment J now as four
less<00:01:09.000><c> than</c><00:01:09.119><c> seven</c><00:01:09.360><c> it</c><00:01:10.260><c> is</c><00:01:10.380><c> we</c><00:01:10.799><c> will</c><00:01:10.979><c> increment</c>

00:01:11.330 --> 00:01:11.340 align:start position:0%
less than seven it is we will increment
 

00:01:11.340 --> 00:01:14.330 align:start position:0%
less than seven it is we will increment
I<00:01:11.640><c> and</c><00:01:12.060><c> then</c><00:01:12.299><c> swap</c><00:01:12.659><c> the</c><00:01:12.900><c> values</c><00:01:13.320><c> at</c><00:01:13.619><c> I</c><00:01:13.799><c> and</c><00:01:13.979><c> J</c><00:01:14.159><c> so</c>

00:01:14.330 --> 00:01:14.340 align:start position:0%
I and then swap the values at I and J so
 

00:01:14.340 --> 00:01:17.630 align:start position:0%
I and then swap the values at I and J so
we'll<00:01:14.520><c> be</c><00:01:14.640><c> swapping</c><00:01:15.119><c> the</c><00:01:15.900><c> indices</c><00:01:16.320><c> 9</c><00:01:17.040><c> and</c><00:01:17.280><c> 4.</c>

00:01:17.630 --> 00:01:17.640 align:start position:0%
we'll be swapping the indices 9 and 4.
 

00:01:17.640 --> 00:01:19.670 align:start position:0%
we'll be swapping the indices 9 and 4.
we're<00:01:17.939><c> going</c><00:01:18.060><c> to</c><00:01:18.119><c> increment</c><00:01:18.420><c> J</c><00:01:18.659><c> again</c>

00:01:19.670 --> 00:01:19.680 align:start position:0%
we're going to increment J again
 

00:01:19.680 --> 00:01:21.530 align:start position:0%
we're going to increment J again
is<00:01:20.220><c> three</c><00:01:20.460><c> less</c><00:01:20.640><c> than</c><00:01:20.759><c> seven</c>

00:01:21.530 --> 00:01:21.540 align:start position:0%
is three less than seven
 

00:01:21.540 --> 00:01:24.289 align:start position:0%
is three less than seven
it<00:01:22.140><c> is</c><00:01:22.259><c> we'll</c><00:01:22.619><c> increment</c><00:01:23.100><c> I</c><00:01:23.400><c> and</c><00:01:23.820><c> we'll</c><00:01:24.000><c> end</c><00:01:24.180><c> up</c>

00:01:24.289 --> 00:01:24.299 align:start position:0%
it is we'll increment I and we'll end up
 

00:01:24.299 --> 00:01:26.510 align:start position:0%
it is we'll increment I and we'll end up
swapping<00:01:24.720><c> the</c><00:01:24.960><c> values</c><00:01:25.380><c> nine</c><00:01:25.979><c> and</c><00:01:26.220><c> three</c>

00:01:26.510 --> 00:01:26.520 align:start position:0%
swapping the values nine and three
 

00:01:26.520 --> 00:01:28.609 align:start position:0%
swapping the values nine and three
increment<00:01:27.180><c> J</c><00:01:27.420><c> we</c><00:01:27.900><c> are</c><00:01:28.020><c> now</c><00:01:28.140><c> out</c><00:01:28.320><c> of</c><00:01:28.439><c> the</c><00:01:28.500><c> for</c>

00:01:28.609 --> 00:01:28.619 align:start position:0%
increment J we are now out of the for
 

00:01:28.619 --> 00:01:29.990 align:start position:0%
increment J we are now out of the for
Loop<00:01:28.920><c> and</c><00:01:29.040><c> we're</c><00:01:29.100><c> going</c><00:01:29.220><c> to</c><00:01:29.280><c> sort</c><00:01:29.520><c> the</c><00:01:29.700><c> values</c>

00:01:29.990 --> 00:01:30.000 align:start position:0%
Loop and we're going to sort the values
 

00:01:30.000 --> 00:01:32.749 align:start position:0%
Loop and we're going to sort the values
at<00:01:30.180><c> I</c><00:01:30.299><c> plus</c><00:01:30.540><c> 1</c><00:01:30.840><c> and</c><00:01:31.320><c> the</c><00:01:31.619><c> index</c><00:01:31.920><c> of</c><00:01:32.159><c> high</c><00:01:32.460><c> which</c>

00:01:32.749 --> 00:01:32.759 align:start position:0%
at I plus 1 and the index of high which
 

00:01:32.759 --> 00:01:34.670 align:start position:0%
at I plus 1 and the index of high which
is<00:01:32.939><c> four</c><00:01:33.180><c> so</c><00:01:33.600><c> we'll</c><00:01:33.840><c> end</c><00:01:34.080><c> up</c><00:01:34.140><c> swapping</c><00:01:34.560><c> the</c>

00:01:34.670 --> 00:01:34.680 align:start position:0%
is four so we'll end up swapping the
 

00:01:34.680 --> 00:01:37.069 align:start position:0%
is four so we'll end up swapping the
values<00:01:34.920><c> at</c><00:01:35.159><c> 3</c><00:01:35.400><c> and</c><00:01:35.700><c> 4</c><00:01:35.939><c> which</c><00:01:36.360><c> will</c><00:01:36.479><c> be</c><00:01:36.659><c> nine</c><00:01:36.900><c> and</c>

00:01:37.069 --> 00:01:37.079 align:start position:0%
values at 3 and 4 which will be nine and
 

00:01:37.079 --> 00:01:39.530 align:start position:0%
values at 3 and 4 which will be nine and
seven<00:01:37.380><c> and</c><00:01:37.680><c> we</c><00:01:37.860><c> return</c><00:01:38.040><c> I</c><00:01:38.460><c> plus</c><00:01:38.700><c> one</c><00:01:38.939><c> which</c><00:01:39.360><c> is</c>

00:01:39.530 --> 00:01:39.540 align:start position:0%
seven and we return I plus one which is
 

00:01:39.540 --> 00:01:41.149 align:start position:0%
seven and we return I plus one which is
three<00:01:39.720><c> now</c><00:01:40.020><c> that</c><00:01:40.140><c> we</c><00:01:40.259><c> have</c><00:01:40.380><c> a</c><00:01:40.439><c> pivot</c><00:01:40.680><c> index</c><00:01:40.979><c> of</c>

00:01:41.149 --> 00:01:41.159 align:start position:0%
three now that we have a pivot index of
 

00:01:41.159 --> 00:01:43.550 align:start position:0%
three now that we have a pivot index of
three<00:01:41.340><c> we're</c><00:01:41.939><c> going</c><00:01:42.060><c> to</c><00:01:42.180><c> call</c><00:01:42.240><c> quicksort</c><00:01:42.960><c> on</c>

00:01:43.550 --> 00:01:43.560 align:start position:0%
three we're going to call quicksort on
 

00:01:43.560 --> 00:01:45.950 align:start position:0%
three we're going to call quicksort on
the<00:01:43.680><c> array</c><00:01:43.979><c> the</c><00:01:44.520><c> low</c><00:01:44.640><c> is</c><00:01:44.759><c> still</c><00:01:44.880><c> zero</c><00:01:45.360><c> and</c><00:01:45.840><c> the</c>

00:01:45.950 --> 00:01:45.960 align:start position:0%
the array the low is still zero and the
 

00:01:45.960 --> 00:01:47.870 align:start position:0%
the array the low is still zero and the
pivot<00:01:46.200><c> index</c><00:01:46.500><c> minus</c><00:01:46.860><c> one</c><00:01:46.979><c> or</c><00:01:47.159><c> three</c><00:01:47.340><c> minus</c><00:01:47.640><c> one</c>

00:01:47.870 --> 00:01:47.880 align:start position:0%
pivot index minus one or three minus one
 

00:01:47.880 --> 00:01:50.149 align:start position:0%
pivot index minus one or three minus one
which<00:01:48.180><c> is</c><00:01:48.299><c> two</c><00:01:48.479><c> well</c><00:01:48.840><c> zero</c><00:01:49.500><c> is</c><00:01:49.619><c> less</c><00:01:49.799><c> than</c><00:01:49.979><c> two</c>

00:01:50.149 --> 00:01:50.159 align:start position:0%
which is two well zero is less than two
 

00:01:50.159 --> 00:01:52.429 align:start position:0%
which is two well zero is less than two
so<00:01:50.460><c> we</c><00:01:50.579><c> go</c><00:01:50.759><c> back</c><00:01:50.939><c> into</c><00:01:51.180><c> the</c><00:01:51.420><c> partition</c><00:01:51.840><c> with</c>

00:01:52.429 --> 00:01:52.439 align:start position:0%
so we go back into the partition with
 

00:01:52.439 --> 00:01:54.230 align:start position:0%
so we go back into the partition with
the<00:01:52.560><c> low</c><00:01:52.680><c> being</c><00:01:52.979><c> zero</c><00:01:53.460><c> and</c><00:01:53.759><c> the</c><00:01:53.880><c> high</c><00:01:54.000><c> being</c>

00:01:54.230 --> 00:01:54.240 align:start position:0%
the low being zero and the high being
 

00:01:54.240 --> 00:01:56.030 align:start position:0%
the low being zero and the high being
two<00:01:54.479><c> back</c><00:01:54.960><c> to</c><00:01:55.079><c> the</c><00:01:55.200><c> for</c><00:01:55.320><c> Loop</c><00:01:55.619><c> for</c><00:01:55.740><c> the</c><00:01:55.920><c> next</c>

00:01:56.030 --> 00:01:56.040 align:start position:0%
two back to the for Loop for the next
 

00:01:56.040 --> 00:01:58.850 align:start position:0%
two back to the for Loop for the next
recursive<00:01:56.640><c> call</c><00:01:56.820><c> J</c><00:01:57.420><c> starts</c><00:01:57.840><c> out</c><00:01:57.899><c> of</c><00:01:58.020><c> zero</c><00:01:58.380><c> is</c>

00:01:58.850 --> 00:01:58.860 align:start position:0%
recursive call J starts out of zero is
 

00:01:58.860 --> 00:02:01.429 align:start position:0%
recursive call J starts out of zero is
two<00:01:59.220><c> less</c><00:01:59.640><c> than</c><00:01:59.820><c> three</c><00:02:00.060><c> it</c><00:02:00.600><c> is</c><00:02:00.840><c> we</c><00:02:01.259><c> will</c>

00:02:01.429 --> 00:02:01.439 align:start position:0%
two less than three it is we will
 

00:02:01.439 --> 00:02:03.530 align:start position:0%
two less than three it is we will
increment<00:02:01.740><c> I</c><00:02:01.979><c> and</c><00:02:02.340><c> then</c><00:02:02.460><c> swap</c><00:02:02.820><c> the</c><00:02:03.000><c> values</c><00:02:03.299><c> at</c>

00:02:03.530 --> 00:02:03.540 align:start position:0%
increment I and then swap the values at
 

00:02:03.540 --> 00:02:05.810 align:start position:0%
increment I and then swap the values at
I<00:02:03.720><c> and</c><00:02:03.960><c> J</c><00:02:04.140><c> again</c><00:02:04.560><c> we're</c><00:02:05.040><c> just</c><00:02:05.219><c> swapping</c><00:02:05.460><c> two</c><00:02:05.579><c> by</c>

00:02:05.810 --> 00:02:05.820 align:start position:0%
I and J again we're just swapping two by
 

00:02:05.820 --> 00:02:08.210 align:start position:0%
I and J again we're just swapping two by
itself<00:02:06.000><c> increment</c><00:02:06.600><c> J</c><00:02:06.840><c> is</c><00:02:07.439><c> four</c><00:02:07.740><c> less</c><00:02:08.099><c> than</c>

00:02:08.210 --> 00:02:08.220 align:start position:0%
itself increment J is four less than
 

00:02:08.220 --> 00:02:11.089 align:start position:0%
itself increment J is four less than
three<00:02:08.399><c> it</c><00:02:08.940><c> is</c><00:02:09.119><c> not</c><00:02:09.239><c> so</c><00:02:09.720><c> we</c><00:02:09.959><c> just</c><00:02:10.200><c> increment</c><00:02:10.679><c> J</c>

00:02:11.089 --> 00:02:11.099 align:start position:0%
three it is not so we just increment J
 

00:02:11.099 --> 00:02:13.490 align:start position:0%
three it is not so we just increment J
we<00:02:11.700><c> are</c><00:02:11.819><c> now</c><00:02:11.940><c> out</c><00:02:12.120><c> of</c><00:02:12.239><c> the</c><00:02:12.360><c> for</c><00:02:12.480><c> Loop</c><00:02:12.840><c> and</c><00:02:13.260><c> we'll</c>

00:02:13.490 --> 00:02:13.500 align:start position:0%
we are now out of the for Loop and we'll
 

00:02:13.500 --> 00:02:16.309 align:start position:0%
we are now out of the for Loop and we'll
swap<00:02:13.860><c> the</c><00:02:14.160><c> values</c><00:02:14.640><c> at</c><00:02:15.060><c> I</c><00:02:15.239><c> plus</c><00:02:15.420><c> one</c><00:02:15.720><c> and</c><00:02:16.140><c> high</c>

00:02:16.309 --> 00:02:16.319 align:start position:0%
swap the values at I plus one and high
 

00:02:16.319 --> 00:02:18.410 align:start position:0%
swap the values at I plus one and high
so<00:02:16.560><c> this</c><00:02:16.860><c> will</c><00:02:16.980><c> be</c><00:02:17.099><c> the</c><00:02:17.220><c> values</c><00:02:17.580><c> at</c><00:02:17.760><c> one</c><00:02:18.120><c> and</c>

00:02:18.410 --> 00:02:18.420 align:start position:0%
so this will be the values at one and
 

00:02:18.420 --> 00:02:20.990 align:start position:0%
so this will be the values at one and
two<00:02:18.660><c> we</c><00:02:19.200><c> now</c><00:02:19.440><c> return</c><00:02:19.620><c> I</c><00:02:19.920><c> plus</c><00:02:20.160><c> one</c><00:02:20.340><c> which</c><00:02:20.879><c> is</c>

00:02:20.990 --> 00:02:21.000 align:start position:0%
two we now return I plus one which is
 

00:02:21.000 --> 00:02:22.850 align:start position:0%
two we now return I plus one which is
just<00:02:21.120><c> one</c><00:02:21.239><c> the</c><00:02:21.480><c> pivot</c><00:02:22.080><c> index</c><00:02:22.379><c> for</c><00:02:22.500><c> this</c><00:02:22.680><c> call</c>

00:02:22.850 --> 00:02:22.860 align:start position:0%
just one the pivot index for this call
 

00:02:22.860 --> 00:02:25.550 align:start position:0%
just one the pivot index for this call
is<00:02:23.340><c> one</c><00:02:23.580><c> now</c><00:02:24.180><c> we</c><00:02:24.360><c> go</c><00:02:24.540><c> down</c><00:02:24.720><c> to</c><00:02:24.900><c> the</c><00:02:25.080><c> next</c><00:02:25.260><c> quick</c>

00:02:25.550 --> 00:02:25.560 align:start position:0%
is one now we go down to the next quick
 

00:02:25.560 --> 00:02:27.830 align:start position:0%
is one now we go down to the next quick
sort<00:02:25.860><c> passing</c><00:02:26.459><c> in</c><00:02:26.580><c> the</c><00:02:26.760><c> parameters</c><00:02:27.120><c> array</c><00:02:27.599><c> 0</c>

00:02:27.830 --> 00:02:27.840 align:start position:0%
sort passing in the parameters array 0
 

00:02:27.840 --> 00:02:30.110 align:start position:0%
sort passing in the parameters array 0
and<00:02:28.200><c> 1</c><00:02:28.440><c> minus</c><00:02:28.860><c> one</c><00:02:29.040><c> which</c><00:02:29.340><c> is</c><00:02:29.459><c> just</c><00:02:29.580><c> a</c><00:02:29.760><c> zero</c>

00:02:30.110 --> 00:02:30.120 align:start position:0%
and 1 minus one which is just a zero
 

00:02:30.120 --> 00:02:33.290 align:start position:0%
and 1 minus one which is just a zero
well<00:02:30.540><c> this</c><00:02:31.020><c> time</c><00:02:31.260><c> the</c><00:02:32.160><c> low</c><00:02:32.340><c> and</c><00:02:32.640><c> the</c><00:02:32.819><c> high</c><00:02:32.940><c> are</c>

00:02:33.290 --> 00:02:33.300 align:start position:0%
well this time the low and the high are
 

00:02:33.300 --> 00:02:36.050 align:start position:0%
well this time the low and the high are
both<00:02:33.480><c> zero</c><00:02:34.140><c> and</c><00:02:34.500><c> zero</c><00:02:34.800><c> is</c><00:02:35.040><c> not</c><00:02:35.220><c> less</c><00:02:35.459><c> than</c><00:02:35.640><c> zero</c>

00:02:36.050 --> 00:02:36.060 align:start position:0%
both zero and zero is not less than zero
 

00:02:36.060 --> 00:02:38.030 align:start position:0%
both zero and zero is not less than zero
which<00:02:36.420><c> means</c><00:02:36.720><c> we</c><00:02:36.900><c> are</c><00:02:37.080><c> done</c><00:02:37.319><c> with</c><00:02:37.739><c> this</c>

00:02:38.030 --> 00:02:38.040 align:start position:0%
which means we are done with this
 

00:02:38.040 --> 00:02:40.130 align:start position:0%
which means we are done with this
recursive<00:02:38.640><c> call</c><00:02:38.940><c> now</c><00:02:39.420><c> that</c><00:02:39.599><c> we</c><00:02:39.720><c> reached</c><00:02:40.020><c> the</c>

00:02:40.130 --> 00:02:40.140 align:start position:0%
recursive call now that we reached the
 

00:02:40.140 --> 00:02:42.410 align:start position:0%
recursive call now that we reached the
end<00:02:40.200><c> of</c><00:02:40.379><c> this</c><00:02:40.440><c> recursive</c><00:02:40.980><c> call</c><00:02:41.160><c> we</c><00:02:41.819><c> go</c><00:02:42.060><c> back</c><00:02:42.239><c> up</c>

00:02:42.410 --> 00:02:42.420 align:start position:0%
end of this recursive call we go back up
 

00:02:42.420 --> 00:02:45.350 align:start position:0%
end of this recursive call we go back up
and<00:02:42.660><c> call</c><00:02:42.900><c> the</c><00:02:43.200><c> second</c><00:02:43.560><c> quick</c><00:02:44.099><c> sort</c><00:02:44.519><c> where</c><00:02:45.120><c> we</c>

00:02:45.350 --> 00:02:45.360 align:start position:0%
and call the second quick sort where we
 

00:02:45.360 --> 00:02:48.170 align:start position:0%
and call the second quick sort where we
return<00:02:45.599><c> one</c><00:02:46.319><c> for</c><00:02:46.620><c> the</c><00:02:46.739><c> pivot</c><00:02:47.040><c> index</c><00:02:47.459><c> and</c><00:02:48.000><c> the</c>

00:02:48.170 --> 00:02:48.180 align:start position:0%
return one for the pivot index and the
 

00:02:48.180 --> 00:02:50.809 align:start position:0%
return one for the pivot index and the
high<00:02:48.360><c> for</c><00:02:48.540><c> that</c><00:02:48.660><c> pivot</c><00:02:48.959><c> index</c><00:02:49.319><c> was</c><00:02:49.980><c> two</c><00:02:50.340><c> and</c>

00:02:50.809 --> 00:02:50.819 align:start position:0%
high for that pivot index was two and
 

00:02:50.819 --> 00:02:53.150 align:start position:0%
high for that pivot index was two and
for<00:02:50.940><c> this</c><00:02:51.120><c> pivot</c><00:02:51.420><c> the</c><00:02:51.480><c> next</c><00:02:51.660><c> we</c><00:02:51.780><c> say</c><00:02:52.019><c> one</c><00:02:52.500><c> plus</c>

00:02:53.150 --> 00:02:53.160 align:start position:0%
for this pivot the next we say one plus
 

00:02:53.160 --> 00:02:55.970 align:start position:0%
for this pivot the next we say one plus
one<00:02:53.519><c> so</c><00:02:54.060><c> that</c><00:02:54.300><c> is</c><00:02:54.420><c> two</c><00:02:54.780><c> when</c><00:02:55.260><c> we</c><00:02:55.440><c> call</c><00:02:55.560><c> it</c><00:02:55.680><c> quick</c>

00:02:55.970 --> 00:02:55.980 align:start position:0%
one so that is two when we call it quick
 

00:02:55.980 --> 00:02:58.729 align:start position:0%
one so that is two when we call it quick
sort<00:02:56.280><c> now</c><00:02:57.180><c> we</c><00:02:57.360><c> pass</c><00:02:57.599><c> in</c><00:02:57.780><c> the</c><00:02:57.959><c> parameters</c><00:02:58.260><c> array</c>

00:02:58.729 --> 00:02:58.739 align:start position:0%
sort now we pass in the parameters array
 

00:02:58.739 --> 00:03:02.330 align:start position:0%
sort now we pass in the parameters array
low<00:02:59.400><c> of</c><00:02:59.580><c> Two</c><00:02:59.760><c> and</c><00:03:00.060><c> a</c><00:03:00.180><c> high</c><00:03:00.300><c> of</c><00:03:00.480><c> two</c><00:03:00.599><c> well</c><00:03:01.200><c> two</c><00:03:01.920><c> is</c>

00:03:02.330 --> 00:03:02.340 align:start position:0%
low of Two and a high of two well two is
 

00:03:02.340 --> 00:03:05.089 align:start position:0%
low of Two and a high of two well two is
not<00:03:02.580><c> less</c><00:03:02.879><c> than</c><00:03:03.120><c> two</c><00:03:03.360><c> so</c><00:03:04.200><c> we</c><00:03:04.440><c> get</c><00:03:04.620><c> out</c><00:03:04.860><c> of</c><00:03:05.040><c> this</c>

00:03:05.089 --> 00:03:05.099 align:start position:0%
not less than two so we get out of this
 

00:03:05.099 --> 00:03:06.770 align:start position:0%
not less than two so we get out of this
recursive<00:03:05.519><c> call</c><00:03:05.700><c> because</c><00:03:06.000><c> we</c><00:03:06.239><c> can't</c><00:03:06.360><c> go</c><00:03:06.599><c> any</c>

00:03:06.770 --> 00:03:06.780 align:start position:0%
recursive call because we can't go any
 

00:03:06.780 --> 00:03:08.570 align:start position:0%
recursive call because we can't go any
further<00:03:07.080><c> and</c><00:03:07.379><c> we</c><00:03:07.560><c> go</c><00:03:07.739><c> back</c><00:03:07.920><c> up</c><00:03:08.040><c> the</c><00:03:08.220><c> activation</c>

00:03:08.570 --> 00:03:08.580 align:start position:0%
further and we go back up the activation
 

00:03:08.580 --> 00:03:11.449 align:start position:0%
further and we go back up the activation
stack<00:03:08.879><c> one</c><00:03:09.180><c> more</c><00:03:09.360><c> time</c><00:03:09.660><c> where</c><00:03:10.440><c> initially</c><00:03:11.040><c> we</c>

00:03:11.449 --> 00:03:11.459 align:start position:0%
stack one more time where initially we
 

00:03:11.459 --> 00:03:13.850 align:start position:0%
stack one more time where initially we
returned<00:03:12.060><c> three</c><00:03:12.420><c> for</c><00:03:12.720><c> the</c><00:03:12.840><c> pivot</c><00:03:13.140><c> index</c><00:03:13.500><c> and</c>

00:03:13.850 --> 00:03:13.860 align:start position:0%
returned three for the pivot index and
 

00:03:13.860 --> 00:03:16.070 align:start position:0%
returned three for the pivot index and
the<00:03:14.040><c> high</c><00:03:14.280><c> was</c><00:03:14.580><c> four</c><00:03:14.940><c> which</c><00:03:15.360><c> means</c><00:03:15.599><c> we</c><00:03:15.840><c> call</c>

00:03:16.070 --> 00:03:16.080 align:start position:0%
the high was four which means we call
 

00:03:16.080 --> 00:03:18.290 align:start position:0%
the high was four which means we call
the<00:03:16.680><c> second</c><00:03:16.800><c> quick</c><00:03:17.040><c> sort</c><00:03:17.400><c> passing</c><00:03:17.819><c> an</c><00:03:18.000><c> array</c>

00:03:18.290 --> 00:03:18.300 align:start position:0%
the second quick sort passing an array
 

00:03:18.300 --> 00:03:20.210 align:start position:0%
the second quick sort passing an array
the<00:03:18.659><c> pivot</c><00:03:18.900><c> in</c><00:03:19.019><c> X</c><00:03:19.200><c> plus</c><00:03:19.319><c> one</c><00:03:19.560><c> which</c><00:03:19.860><c> is</c><00:03:19.980><c> three</c>

00:03:20.210 --> 00:03:20.220 align:start position:0%
the pivot in X plus one which is three
 

00:03:20.220 --> 00:03:22.490 align:start position:0%
the pivot in X plus one which is three
plus<00:03:20.459><c> one</c><00:03:20.760><c> and</c><00:03:21.000><c> the</c><00:03:21.180><c> high</c><00:03:21.300><c> of</c><00:03:21.540><c> four</c><00:03:21.959><c> we</c><00:03:22.319><c> check</c>

00:03:22.490 --> 00:03:22.500 align:start position:0%
plus one and the high of four we check
 

00:03:22.500 --> 00:03:24.229 align:start position:0%
plus one and the high of four we check
if<00:03:22.739><c> the</c><00:03:22.920><c> low</c><00:03:22.980><c> is</c><00:03:23.159><c> less</c><00:03:23.340><c> than</c><00:03:23.519><c> high</c><00:03:23.700><c> well</c><00:03:24.000><c> four</c>

00:03:24.229 --> 00:03:24.239 align:start position:0%
if the low is less than high well four
 

00:03:24.239 --> 00:03:26.449 align:start position:0%
if the low is less than high well four
is<00:03:24.480><c> not</c><00:03:24.659><c> less</c><00:03:24.840><c> than</c><00:03:25.019><c> four</c><00:03:25.319><c> and</c><00:03:25.800><c> we</c><00:03:26.040><c> can't</c><00:03:26.159><c> go</c><00:03:26.340><c> up</c>

00:03:26.449 --> 00:03:26.459 align:start position:0%
is not less than four and we can't go up
 

00:03:26.459 --> 00:03:28.130 align:start position:0%
is not less than four and we can't go up
the<00:03:26.580><c> stack</c><00:03:26.760><c> anymore</c><00:03:27.000><c> so</c><00:03:27.480><c> we</c><00:03:27.659><c> are</c><00:03:27.780><c> done</c><00:03:27.959><c> with</c>

00:03:28.130 --> 00:03:28.140 align:start position:0%
the stack anymore so we are done with
 

00:03:28.140 --> 00:03:29.630 align:start position:0%
the stack anymore so we are done with
the<00:03:28.260><c> recursive</c><00:03:28.620><c> calls</c><00:03:28.980><c> and</c><00:03:29.099><c> I</c><00:03:29.280><c> have</c><00:03:29.459><c> a</c>

00:03:29.630 --> 00:03:29.640 align:start position:0%
the recursive calls and I have a
 

00:03:29.640 --> 00:03:31.550 align:start position:0%
the recursive calls and I have a
complete<00:03:30.120><c> quick</c><00:03:30.480><c> sorting</c><00:03:31.019><c> algorithm</c>

00:03:31.550 --> 00:03:31.560 align:start position:0%
complete quick sorting algorithm
 

00:03:31.560 --> 00:03:33.770 align:start position:0%
complete quick sorting algorithm
remember<00:03:32.280><c> that</c><00:03:32.580><c> a</c><00:03:32.819><c> key</c><00:03:33.000><c> difference</c><00:03:33.239><c> between</c>

00:03:33.770 --> 00:03:33.780 align:start position:0%
remember that a key difference between
 

00:03:33.780 --> 00:03:35.869 align:start position:0%
remember that a key difference between
this<00:03:34.140><c> sort</c><00:03:34.319><c> and</c><00:03:34.500><c> merge</c><00:03:34.739><c> sort</c><00:03:34.980><c> is</c><00:03:35.159><c> we</c><00:03:35.340><c> swapped</c>

00:03:35.869 --> 00:03:35.879 align:start position:0%
this sort and merge sort is we swapped
 

00:03:35.879 --> 00:03:38.449 align:start position:0%
this sort and merge sort is we swapped
elements<00:03:36.300><c> and</c><00:03:36.840><c> order</c><00:03:37.080><c> them</c><00:03:37.379><c> before</c><00:03:37.860><c> we</c><00:03:38.280><c> go</c>

00:03:38.449 --> 00:03:38.459 align:start position:0%
elements and order them before we go
 

00:03:38.459 --> 00:03:40.610 align:start position:0%
elements and order them before we go
down<00:03:38.640><c> another</c><00:03:38.879><c> recursive</c><00:03:39.540><c> call</c><00:03:39.720><c> whereas</c><00:03:40.500><c> the</c>

00:03:40.610 --> 00:03:40.620 align:start position:0%
down another recursive call whereas the
 

00:03:40.620 --> 00:03:42.289 align:start position:0%
down another recursive call whereas the
merge<00:03:40.920><c> sort</c><00:03:41.099><c> goes</c><00:03:41.519><c> all</c><00:03:41.700><c> the</c><00:03:41.819><c> way</c><00:03:41.879><c> down</c><00:03:42.120><c> until</c>

00:03:42.289 --> 00:03:42.299 align:start position:0%
merge sort goes all the way down until
 

00:03:42.299 --> 00:03:44.030 align:start position:0%
merge sort goes all the way down until
it<00:03:42.599><c> reaches</c><00:03:42.840><c> a</c><00:03:43.019><c> base</c><00:03:43.200><c> case</c><00:03:43.440><c> and</c><00:03:43.680><c> we</c><00:03:43.860><c> can't</c>

00:03:44.030 --> 00:03:44.040 align:start position:0%
it reaches a base case and we can't
 

00:03:44.040 --> 00:03:46.250 align:start position:0%
it reaches a base case and we can't
divide<00:03:44.459><c> it</c><00:03:44.580><c> anymore</c><00:03:44.819><c> and</c><00:03:45.599><c> then</c><00:03:45.780><c> sort</c><00:03:46.019><c> the</c>

00:03:46.250 --> 00:03:46.260 align:start position:0%
divide it anymore and then sort the
 

00:03:46.260 --> 00:03:48.589 align:start position:0%
divide it anymore and then sort the
elements<00:03:46.620><c> as</c><00:03:46.799><c> it</c><00:03:46.980><c> works</c><00:03:47.220><c> its</c><00:03:47.459><c> way</c><00:03:47.580><c> back</c><00:03:47.940><c> up</c><00:03:48.239><c> the</c>

00:03:48.589 --> 00:03:48.599 align:start position:0%
elements as it works its way back up the
 

00:03:48.599 --> 00:03:51.379 align:start position:0%
elements as it works its way back up the
activation<00:03:49.019><c> stack</c>

