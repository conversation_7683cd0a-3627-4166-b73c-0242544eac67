WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:02.270 align:start position:0%
 
hi<00:00:00.359><c> there</c><00:00:00.960><c> I'm</c><00:00:01.120><c> going</c><00:00:01.360><c> to</c><00:00:01.599><c> show</c><00:00:01.800><c> you</c><00:00:02.040><c> the</c>

00:00:02.270 --> 00:00:02.280 align:start position:0%
hi there I'm going to show you the
 

00:00:02.280 --> 00:00:05.550 align:start position:0%
hi there I'm going to show you the
quickest<00:00:02.760><c> way</c><00:00:03.000><c> to</c><00:00:03.159><c> bootstrap</c><00:00:04.040><c> a</c><00:00:04.319><c> llama</c><00:00:04.799><c> index</c>

00:00:05.550 --> 00:00:05.560 align:start position:0%
quickest way to bootstrap a llama index
 

00:00:05.560 --> 00:00:08.750 align:start position:0%
quickest way to bootstrap a llama index
application<00:00:06.560><c> using</c><00:00:06.960><c> the</c><00:00:07.160><c> new</c><00:00:07.640><c> create</c><00:00:08.240><c> llama</c>

00:00:08.750 --> 00:00:08.760 align:start position:0%
application using the new create llama
 

00:00:08.760 --> 00:00:12.270 align:start position:0%
application using the new create llama
CLI<00:00:09.320><c> tool</c><00:00:10.040><c> so</c><00:00:10.240><c> the</c><00:00:10.360><c> first</c><00:00:10.599><c> step</c><00:00:10.920><c> is</c><00:00:11.040><c> to</c><00:00:11.320><c> type</c>

00:00:12.270 --> 00:00:12.280 align:start position:0%
CLI tool so the first step is to type
 

00:00:12.280 --> 00:00:17.630 align:start position:0%
CLI tool so the first step is to type
npx<00:00:13.759><c> create</c><00:00:14.759><c> llama</c><00:00:15.559><c> and</c><00:00:15.759><c> hit</c><00:00:16.359><c> enter</c><00:00:17.359><c> which</c><00:00:17.480><c> is</c>

00:00:17.630 --> 00:00:17.640 align:start position:0%
npx create llama and hit enter which is
 

00:00:17.640 --> 00:00:20.109 align:start position:0%
npx create llama and hit enter which is
going<00:00:17.840><c> to</c><00:00:18.080><c> download</c><00:00:18.800><c> the</c>

00:00:20.109 --> 00:00:20.119 align:start position:0%
going to download the
 

00:00:20.119 --> 00:00:24.070 align:start position:0%
going to download the
tool<00:00:21.119><c> I'm</c><00:00:21.279><c> choosing</c><00:00:21.760><c> a</c><00:00:22.320><c> name</c><00:00:23.320><c> let's</c><00:00:23.599><c> call</c><00:00:23.800><c> it</c>

00:00:24.070 --> 00:00:24.080 align:start position:0%
tool I'm choosing a name let's call it
 

00:00:24.080 --> 00:00:25.550 align:start position:0%
tool I'm choosing a name let's call it
my<00:00:24.359><c> app</c><00:00:24.560><c> as</c><00:00:24.720><c> a</c>

00:00:25.550 --> 00:00:25.560 align:start position:0%
my app as a
 

00:00:25.560 --> 00:00:28.269 align:start position:0%
my app as a
default<00:00:26.560><c> I'm</c><00:00:26.720><c> selecting</c><00:00:27.320><c> to</c><00:00:27.519><c> build</c><00:00:27.760><c> a</c><00:00:27.920><c> chat</c>

00:00:28.269 --> 00:00:28.279 align:start position:0%
default I'm selecting to build a chat
 

00:00:28.279 --> 00:00:31.910 align:start position:0%
default I'm selecting to build a chat
application<00:00:28.920><c> with</c><00:00:29.119><c> streaming</c>

00:00:31.910 --> 00:00:31.920 align:start position:0%
application with streaming
 

00:00:31.920 --> 00:00:34.549 align:start position:0%
application with streaming
I'm<00:00:32.119><c> going</c><00:00:32.360><c> to</c><00:00:32.599><c> use</c><00:00:33.000><c> the</c><00:00:33.200><c> nextjs</c><00:00:33.960><c> framework</c>

00:00:34.549 --> 00:00:34.559 align:start position:0%
I'm going to use the nextjs framework
 

00:00:34.559 --> 00:00:36.350 align:start position:0%
I'm going to use the nextjs framework
there<00:00:34.680><c> are</c><00:00:34.879><c> other</c><00:00:35.160><c> options</c><00:00:35.719><c> like</c><00:00:35.920><c> using</c>

00:00:36.350 --> 00:00:36.360 align:start position:0%
there are other options like using
 

00:00:36.360 --> 00:00:38.990 align:start position:0%
there are other options like using
Express<00:00:36.800><c> or</c><00:00:37.000><c> fast</c><00:00:37.399><c> API</c><00:00:38.280><c> which</c><00:00:38.520><c> would</c><00:00:38.800><c> then</c>

00:00:38.990 --> 00:00:39.000 align:start position:0%
Express or fast API which would then
 

00:00:39.000 --> 00:00:41.150 align:start position:0%
Express or fast API which would then
build<00:00:39.239><c> a</c><00:00:39.360><c> fullstack</c><00:00:40.039><c> application</c><00:00:40.640><c> using</c><00:00:40.920><c> a</c>

00:00:41.150 --> 00:00:41.160 align:start position:0%
build a fullstack application using a
 

00:00:41.160 --> 00:00:42.790 align:start position:0%
build a fullstack application using a
python<00:00:41.559><c> back</c>

00:00:42.790 --> 00:00:42.800 align:start position:0%
python back
 

00:00:42.800 --> 00:00:46.110 align:start position:0%
python back
end<00:00:43.800><c> for</c><00:00:44.000><c> the</c><00:00:44.160><c> chat</c><00:00:44.480><c> UI</c><00:00:44.960><c> I'm</c><00:00:45.160><c> using</c><00:00:45.520><c> the</c><00:00:45.680><c> Chad</c>

00:00:46.110 --> 00:00:46.120 align:start position:0%
end for the chat UI I'm using the Chad
 

00:00:46.120 --> 00:00:49.750 align:start position:0%
end for the chat UI I'm using the Chad
CN<00:00:47.520><c> components</c><00:00:48.520><c> and</c><00:00:48.719><c> there</c><00:00:48.840><c> are</c><00:00:49.199><c> different</c>

00:00:49.750 --> 00:00:49.760 align:start position:0%
CN components and there are different
 

00:00:49.760 --> 00:00:51.310 align:start position:0%
CN components and there are different
chat<00:00:50.120><c> engines</c>

00:00:51.310 --> 00:00:51.320 align:start position:0%
chat engines
 

00:00:51.320 --> 00:00:53.830 align:start position:0%
chat engines
available<00:00:52.320><c> we</c><00:00:52.480><c> can</c><00:00:52.719><c> use</c><00:00:52.960><c> the</c><00:00:53.160><c> simple</c><00:00:53.480><c> chat</c>

00:00:53.830 --> 00:00:53.840 align:start position:0%
available we can use the simple chat
 

00:00:53.840 --> 00:00:57.990 align:start position:0%
available we can use the simple chat
engine<00:00:54.840><c> which</c><00:00:55.000><c> is</c><00:00:55.239><c> just</c><00:00:55.399><c> a</c><00:00:55.680><c> basic</c><00:00:56.520><c> chat</c><00:00:57.520><c> or</c><00:00:57.800><c> we</c>

00:00:57.990 --> 00:00:58.000 align:start position:0%
engine which is just a basic chat or we
 

00:00:58.000 --> 00:01:00.270 align:start position:0%
engine which is just a basic chat or we
could<00:00:58.239><c> use</c><00:00:58.519><c> the</c><00:00:58.719><c> context</c><00:00:59.120><c> set</c><00:00:59.440><c> engine</c><00:01:00.039><c> which</c>

00:01:00.270 --> 00:01:00.280 align:start position:0%
could use the context set engine which
 

00:01:00.280 --> 00:01:04.270 align:start position:0%
could use the context set engine which
allows<00:01:00.680><c> us</c><00:01:01.280><c> to</c><00:01:01.600><c> chat</c><00:01:02.480><c> with</c><00:01:02.920><c> an</c><00:01:03.440><c> PDF</c><00:01:04.080><c> or</c>

00:01:04.270 --> 00:01:04.280 align:start position:0%
allows us to chat with an PDF or
 

00:01:04.280 --> 00:01:06.910 align:start position:0%
allows us to chat with an PDF or
multiple<00:01:04.920><c> PDFs</c><00:01:05.920><c> so</c><00:01:06.080><c> we're</c><00:01:06.240><c> going</c><00:01:06.439><c> to</c><00:01:06.640><c> choose</c>

00:01:06.910 --> 00:01:06.920 align:start position:0%
multiple PDFs so we're going to choose
 

00:01:06.920 --> 00:01:10.789 align:start position:0%
multiple PDFs so we're going to choose
the<00:01:07.119><c> context</c><00:01:07.479><c> chat</c><00:01:08.360><c> engine</c><00:01:09.360><c> the</c><00:01:09.520><c> API</c><00:01:10.200><c> key</c><00:01:10.640><c> we</c>

00:01:10.789 --> 00:01:10.799 align:start position:0%
the context chat engine the API key we
 

00:01:10.799 --> 00:01:13.109 align:start position:0%
the context chat engine the API key we
are<00:01:10.960><c> leaving</c><00:01:11.320><c> blank</c><00:01:11.680><c> so</c><00:01:11.840><c> it</c><00:01:11.960><c> will</c><00:01:12.200><c> use</c><00:01:12.560><c> the</c><00:01:12.720><c> one</c>

00:01:13.109 --> 00:01:13.119 align:start position:0%
are leaving blank so it will use the one
 

00:01:13.119 --> 00:01:15.710 align:start position:0%
are leaving blank so it will use the one
that's<00:01:13.320><c> set</c><00:01:13.600><c> in</c><00:01:13.720><c> the</c><00:01:14.360><c> environment</c><00:01:15.360><c> and</c><00:01:15.560><c> I'm</c>

00:01:15.710 --> 00:01:15.720 align:start position:0%
that's set in the environment and I'm
 

00:01:15.720 --> 00:01:19.030 align:start position:0%
that's set in the environment and I'm
using<00:01:16.040><c> a</c><00:01:16.200><c> linta</c><00:01:16.640><c> s</c><00:01:16.920><c> lint</c><00:01:17.360><c> for</c><00:01:17.560><c> the</c>

00:01:19.030 --> 00:01:19.040 align:start position:0%
using a linta s lint for the
 

00:01:19.040 --> 00:01:21.429 align:start position:0%
using a linta s lint for the
configuration<00:01:20.040><c> it's</c><00:01:20.280><c> now</c><00:01:20.560><c> generating</c><00:01:21.240><c> the</c>

00:01:21.429 --> 00:01:21.439 align:start position:0%
configuration it's now generating the
 

00:01:21.439 --> 00:01:25.350 align:start position:0%
configuration it's now generating the
application<00:01:22.439><c> and</c><00:01:22.680><c> installing</c><00:01:23.280><c> the</c>

00:01:25.350 --> 00:01:25.360 align:start position:0%
application and installing the
 

00:01:25.360 --> 00:01:27.710 align:start position:0%
application and installing the
dependencies<00:01:26.360><c> you</c><00:01:26.520><c> can</c><00:01:26.720><c> see</c><00:01:27.320><c> after</c>

00:01:27.710 --> 00:01:27.720 align:start position:0%
dependencies you can see after
 

00:01:27.720 --> 00:01:30.350 align:start position:0%
dependencies you can see after
installing<00:01:28.400><c> the</c><00:01:28.840><c> dependencies</c>

00:01:30.350 --> 00:01:30.360 align:start position:0%
installing the dependencies
 

00:01:30.360 --> 00:01:33.149 align:start position:0%
installing the dependencies
create<00:01:30.720><c> llama</c><00:01:31.439><c> is</c><00:01:31.840><c> automatically</c><00:01:32.680><c> calling</c>

00:01:33.149 --> 00:01:33.159 align:start position:0%
create llama is automatically calling
 

00:01:33.159 --> 00:01:35.510 align:start position:0%
create llama is automatically calling
npn<00:01:33.920><c> run</c>

00:01:35.510 --> 00:01:35.520 align:start position:0%
npn run
 

00:01:35.520 --> 00:01:38.230 align:start position:0%
npn run
generate<00:01:36.520><c> this</c><00:01:36.720><c> is</c><00:01:36.920><c> splitting</c><00:01:37.399><c> the</c><00:01:37.640><c> provided</c>

00:01:38.230 --> 00:01:38.240 align:start position:0%
generate this is splitting the provided
 

00:01:38.240 --> 00:01:41.830 align:start position:0%
generate this is splitting the provided
PDF<00:01:39.040><c> example</c><00:01:40.040><c> into</c><00:01:40.399><c> chunks</c><00:01:41.040><c> and</c><00:01:41.240><c> calling</c><00:01:41.640><c> the</c>

00:01:41.830 --> 00:01:41.840 align:start position:0%
PDF example into chunks and calling the
 

00:01:41.840 --> 00:01:45.149 align:start position:0%
PDF example into chunks and calling the
open<00:01:42.240><c> AI</c><00:01:42.840><c> embedding</c><00:01:43.439><c> for</c><00:01:43.680><c> each</c>

00:01:45.149 --> 00:01:45.159 align:start position:0%
open AI embedding for each
 

00:01:45.159 --> 00:01:48.310 align:start position:0%
open AI embedding for each
chunk<00:01:46.159><c> going</c><00:01:46.439><c> now</c><00:01:46.680><c> into</c><00:01:46.960><c> the</c><00:01:47.200><c> directory</c><00:01:48.159><c> that</c>

00:01:48.310 --> 00:01:48.320 align:start position:0%
chunk going now into the directory that
 

00:01:48.320 --> 00:01:49.870 align:start position:0%
chunk going now into the directory that
was

00:01:49.870 --> 00:01:49.880 align:start position:0%
was
 

00:01:49.880 --> 00:01:53.950 align:start position:0%
was
generated<00:01:50.880><c> and</c><00:01:51.079><c> we</c><00:01:51.560><c> start</c><00:01:51.960><c> vard</c><00:01:52.439><c> Studio</c>

00:01:53.950 --> 00:01:53.960 align:start position:0%
generated and we start vard Studio
 

00:01:53.960 --> 00:01:57.069 align:start position:0%
generated and we start vard Studio
code<00:01:54.960><c> so</c><00:01:55.159><c> from</c><00:01:55.360><c> the</c><00:01:55.520><c> file</c><00:01:55.799><c> structure</c><00:01:56.520><c> this</c><00:01:56.680><c> is</c>

00:01:57.069 --> 00:01:57.079 align:start position:0%
code so from the file structure this is
 

00:01:57.079 --> 00:01:58.310 align:start position:0%
code so from the file structure this is
a

00:01:58.310 --> 00:01:58.320 align:start position:0%
a
 

00:01:58.320 --> 00:02:01.109 align:start position:0%
a
nextjs<00:01:59.320><c> application</c>

00:02:01.109 --> 00:02:01.119 align:start position:0%
nextjs application
 

00:02:01.119 --> 00:02:03.950 align:start position:0%
nextjs application
with<00:02:01.280><c> the</c><00:02:01.439><c> app</c><00:02:01.840><c> router</c><00:02:02.840><c> and</c><00:02:03.119><c> here</c><00:02:03.320><c> is</c><00:02:03.479><c> the</c><00:02:03.640><c> chat</c>

00:02:03.950 --> 00:02:03.960 align:start position:0%
with the app router and here is the chat
 

00:02:03.960 --> 00:02:07.630 align:start position:0%
with the app router and here is the chat
route<00:02:04.719><c> that</c><00:02:04.880><c> is</c><00:02:05.000><c> using</c><00:02:05.360><c> the</c><00:02:05.560><c> Llama</c><00:02:05.960><c> index</c><00:02:06.399><c> chat</c>

00:02:07.630 --> 00:02:07.640 align:start position:0%
route that is using the Llama index chat
 

00:02:07.640 --> 00:02:11.110 align:start position:0%
route that is using the Llama index chat
engine<00:02:08.640><c> we're</c><00:02:08.840><c> also</c><00:02:09.080><c> having</c><00:02:09.319><c> a</c><00:02:09.520><c> data</c><00:02:10.120><c> folder</c>

00:02:11.110 --> 00:02:11.120 align:start position:0%
engine we're also having a data folder
 

00:02:11.120 --> 00:02:14.750 align:start position:0%
engine we're also having a data folder
that<00:02:11.360><c> contains</c><00:02:11.959><c> the</c><00:02:12.440><c> PDF</c><00:02:13.440><c> which</c><00:02:13.680><c> is</c><00:02:14.120><c> a</c>

00:02:14.750 --> 00:02:14.760 align:start position:0%
that contains the PDF which is a
 

00:02:14.760 --> 00:02:17.470 align:start position:0%
that contains the PDF which is a
document<00:02:15.360><c> about</c><00:02:15.800><c> standards</c><00:02:16.239><c> for</c>

00:02:17.470 --> 00:02:17.480 align:start position:0%
document about standards for
 

00:02:17.480 --> 00:02:20.910 align:start position:0%
document about standards for
letters<00:02:18.480><c> so</c><00:02:18.720><c> we</c><00:02:18.920><c> expect</c><00:02:19.319><c> that</c><00:02:19.480><c> we</c><00:02:19.640><c> could</c><00:02:19.959><c> ask</c>

00:02:20.910 --> 00:02:20.920 align:start position:0%
letters so we expect that we could ask
 

00:02:20.920 --> 00:02:24.630 align:start position:0%
letters so we expect that we could ask
questions<00:02:21.920><c> about</c><00:02:22.680><c> standards</c><00:02:23.160><c> for</c><00:02:23.360><c> letters</c><00:02:24.360><c> in</c>

00:02:24.630 --> 00:02:24.640 align:start position:0%
questions about standards for letters in
 

00:02:24.640 --> 00:02:27.150 align:start position:0%
questions about standards for letters in
the<00:02:24.920><c> chat</c><00:02:25.319><c> application</c><00:02:25.920><c> that</c><00:02:26.080><c> we</c><00:02:26.280><c> just</c>

00:02:27.150 --> 00:02:27.160 align:start position:0%
the chat application that we just
 

00:02:27.160 --> 00:02:29.949 align:start position:0%
the chat application that we just
generated<00:02:28.160><c> let's</c><00:02:28.400><c> try</c><00:02:28.680><c> that</c><00:02:28.879><c> out</c><00:02:29.160><c> by</c><00:02:29.360><c> starting</c>

00:02:29.949 --> 00:02:29.959 align:start position:0%
generated let's try that out by starting
 

00:02:29.959 --> 00:02:37.550 align:start position:0%
generated let's try that out by starting
the<00:02:30.200><c> development</c>

00:02:37.550 --> 00:02:37.560 align:start position:0%
 
 

00:02:37.560 --> 00:02:40.030 align:start position:0%
 
server<00:02:38.560><c> so</c><00:02:38.760><c> the</c><00:02:38.920><c> development</c><00:02:39.440><c> server</c><00:02:39.920><c> has</c>

00:02:40.030 --> 00:02:40.040 align:start position:0%
server so the development server has
 

00:02:40.040 --> 00:02:43.149 align:start position:0%
server so the development server has
been<00:02:40.280><c> started</c><00:02:40.760><c> and</c><00:02:40.959><c> we</c><00:02:41.080><c> can</c><00:02:41.280><c> open</c><00:02:41.560><c> the</c><00:02:41.959><c> app</c><00:02:42.959><c> the</c>

00:02:43.149 --> 00:02:43.159 align:start position:0%
been started and we can open the app the
 

00:02:43.159 --> 00:02:47.110 align:start position:0%
been started and we can open the app the
app<00:02:43.400><c> has</c><00:02:43.560><c> been</c><00:02:44.280><c> opened</c><00:02:45.280><c> and</c><00:02:45.440><c> we</c><00:02:45.599><c> can</c><00:02:45.879><c> now</c><00:02:46.280><c> ask</c>

00:02:47.110 --> 00:02:47.120 align:start position:0%
app has been opened and we can now ask
 

00:02:47.120 --> 00:02:50.790 align:start position:0%
app has been opened and we can now ask
questions<00:02:47.840><c> about</c><00:02:48.560><c> standards</c><00:02:49.120><c> for</c><00:02:49.720><c> letters</c>

00:02:50.790 --> 00:02:50.800 align:start position:0%
questions about standards for letters
 

00:02:50.800 --> 00:02:53.990 align:start position:0%
questions about standards for letters
what<00:02:51.959><c> standards</c><00:02:52.959><c> for</c>

00:02:53.990 --> 00:02:54.000 align:start position:0%
what standards for
 

00:02:54.000 --> 00:02:58.750 align:start position:0%
what standards for
letters

00:02:58.750 --> 00:02:58.760 align:start position:0%
 
 

00:02:58.760 --> 00:03:02.350 align:start position:0%
 
exist

00:03:02.350 --> 00:03:02.360 align:start position:0%
 
 

00:03:02.360 --> 00:03:04.869 align:start position:0%
 
so<00:03:02.560><c> it</c><00:03:02.760><c> seems</c><00:03:03.080><c> to</c><00:03:03.480><c> retrieve</c><00:03:04.120><c> a</c><00:03:04.239><c> lot</c><00:03:04.400><c> of</c>

00:03:04.869 --> 00:03:04.879 align:start position:0%
so it seems to retrieve a lot of
 

00:03:04.879 --> 00:03:08.670 align:start position:0%
so it seems to retrieve a lot of
information<00:03:05.879><c> from</c><00:03:06.280><c> the</c><00:03:06.920><c> PDFs</c><00:03:07.519><c> that</c><00:03:07.720><c> we</c><00:03:07.959><c> have</c>

00:03:08.670 --> 00:03:08.680 align:start position:0%
information from the PDFs that we have
 

00:03:08.680 --> 00:03:11.509 align:start position:0%
information from the PDFs that we have
provided<00:03:09.680><c> thanks</c><00:03:09.879><c> for</c><00:03:10.080><c> watching</c><00:03:10.360><c> the</c><00:03:10.519><c> video</c><00:03:11.360><c> I</c>

00:03:11.509 --> 00:03:11.519 align:start position:0%
provided thanks for watching the video I
 

00:03:11.519 --> 00:03:15.110 align:start position:0%
provided thanks for watching the video I
hope<00:03:12.159><c> you're</c><00:03:12.599><c> trying</c><00:03:12.959><c> to</c><00:03:13.680><c> use</c><00:03:14.120><c> great</c><00:03:14.519><c> llama</c>

00:03:15.110 --> 00:03:15.120 align:start position:0%
hope you're trying to use great llama
 

00:03:15.120 --> 00:03:16.869 align:start position:0%
hope you're trying to use great llama
for<00:03:15.319><c> yourself</c><00:03:15.920><c> and</c><00:03:16.080><c> build</c><00:03:16.360><c> wonderful</c>

00:03:16.869 --> 00:03:16.879 align:start position:0%
for yourself and build wonderful
 

00:03:16.879 --> 00:03:20.070 align:start position:0%
for yourself and build wonderful
applications<00:03:17.599><c> using</c><00:03:18.000><c> llama</c><00:03:18.799><c> index</c><00:03:19.799><c> have</c><00:03:19.959><c> a</c>

00:03:20.070 --> 00:03:20.080 align:start position:0%
applications using llama index have a
 

00:03:20.080 --> 00:03:20.990 align:start position:0%
applications using llama index have a
great

00:03:20.990 --> 00:03:21.000 align:start position:0%
great
 

00:03:21.000 --> 00:03:24.000 align:start position:0%
great
day

