# YouTube Summaries Browser

A modern NextJS application for browsing and reading YouTube video summaries from Supabase databases with integrated AI chat functionality using pgvector for semantic search.

## 🌟 Features

- **Topic-based Video Browsing**: Browse video summaries organized by different topics stored in separate Supabase tables
- **Semantic Search**: Powerful vector-based search using pgvector embeddings across all video content
- **AI Chat Integration**: Interactive chat interface powered by existing Python Gemini methods
- **Modern UI**: Beautiful, responsive interface built with Tailwind CSS and Radix UI components
- **Real-time Data**: Live connection to Supabase database with optimized queries
- **Hybrid Architecture**: NextJS frontend with Python FastAPI backend for AI functionality

## 🏗️ Architecture

This application uses a hybrid architecture that maximizes code reuse:

- **Frontend**: NextJS 14 with App Router, TypeScript, and Tailwind CSS
- **Database**: Supabase with PostgreSQL and pgvector extension
- **AI Backend**: Python FastAPI service integrating existing `gemini_methods` utilities
- **Vector Search**: pgvector for semantic similarity search across video summaries
- **UI Components**: Radix UI primitives for accessibility and consistency

## 📋 Prerequisites

- Node.js 18.17 or later
- Python 3.8 or later
- Supabase account and project
- Access to existing `gemini_methods` Python utilities

## 🚀 Quick Start

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd nextjs-youtube-summaries
npm install
```

### 2. Environment Setup

Copy the environment template and configure your settings:

```bash
cp .env.example .env.local
```

Update `.env.local` with your configuration:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Python AI Service
PYTHON_AI_SERVICE_URL=http://localhost:8001
PYTHON_AI_SERVICE_API_KEY=your_api_key

# Application Configuration
NEXT_PUBLIC_APP_NAME="YouTube Summaries"
NEXT_PUBLIC_DEFAULT_TOPIC=technology
```

### 3. Database Setup

Run the database schema creation script in your Supabase SQL editor:

```bash
# The schema file is located at: database/schema.sql
```

This creates:
- Tables for different video topics
- pgvector extension setup
- Indexes for optimal performance
- Database functions for search operations

### 4. Python AI Service Setup

Set up the Python backend service:

```bash
cd python-service
pip install -r requirements.txt

# Configure your gemini_methods path
export GEMINI_METHODS_PATH=/path/to/your/gemini_methods

# Start the service
uvicorn main:app --host 0.0.0.0 --port 8001
```

### 5. Start Development Server

```bash
npm run dev
```

Open [http://localhost:9696](http://localhost:9696) to view the application.

## 📁 Project Structure

```
nextjs-youtube-summaries/
├── src/
│   ├── app/                    # NextJS App Router pages
│   │   ├── api/               # API routes
│   │   │   ├── chat/          # AI chat endpoints
│   │   │   ├── search/        # Search functionality
│   │   │   ├── embeddings/    # Vector operations
│   │   │   └── topics/        # Topic management
│   │   ├── chat/              # Chat page
│   │   ├── search/            # Search page
│   │   └── page.tsx           # Home page
│   ├── components/            # React components
│   │   ├── ui/                # Base UI components
│   │   ├── VideoCard.tsx      # Video display component
│   │   ├── TopicSelector.tsx  # Topic navigation
│   │   ├── ChatInterface.tsx  # AI chat component
│   │   ├── SearchComponent.tsx # Search interface
│   │   └── AppLayout.tsx      # Main layout
│   ├── lib/                   # Utility libraries
│   │   ├── supabase.ts        # Supabase client
│   │   ├── database.ts        # Database service
│   │   └── utils.ts           # Helper functions
│   └── types/                 # TypeScript definitions
├── database/
│   └── schema.sql             # Database schema
├── python-service/            # Python AI backend
│   ├── main.py               # FastAPI application
│   └── requirements.txt      # Python dependencies
└── package.dev.json          # Development scripts
```

## 🔧 API Endpoints

### Video Operations
- `GET /api/videos/recent` - Fetch recent videos from all topics
- `GET /api/topics` - Get available topic tables

### Search & AI
- `POST /api/search` - Semantic search across video summaries
- `POST /api/chat` - AI chat interaction
- `POST /api/embeddings` - Generate vector embeddings

## 🗄️ Database Schema

The application supports multiple topic tables with the following structure:

```sql
-- Example topic table (e.g., technology_summaries)
CREATE TABLE technology_summaries (
    id SERIAL PRIMARY KEY,
    video_id TEXT UNIQUE NOT NULL,
    title TEXT NOT NULL,
    channel_name TEXT,
    summary TEXT,
    transcript TEXT,
    published_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    view_count BIGINT,
    thumbnail_url TEXT,
    tags TEXT[],
    embedding vector(1536),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🎨 Components

### Core Components

- **`VideoCard`**: Displays video information with summary preview
- **`TopicSelector`**: Navigation component for switching between topics
- **`ChatInterface`**: AI-powered chat for discussing video content
- **`SearchComponent`**: Semantic search interface with filters
- **`AppLayout`**: Main application layout with navigation

### UI Components

Built with Radix UI primitives:
- **`Tabs`**: For organizing content sections
- **Button variants**: Primary, secondary, ghost, and outline styles
- **Typography**: Consistent text styling throughout the app

## 🔍 Search Functionality

The application implements advanced search capabilities:

1. **Text Search**: Traditional keyword matching
2. **Semantic Search**: Vector similarity using pgvector
3. **Topic Filtering**: Search within specific topic categories
4. **Combined Results**: Hybrid search combining multiple approaches

## 🤖 AI Integration

The AI chat feature integrates with existing Python utilities:

- **Gemini Integration**: Uses existing `gemini_methods` for AI responses
- **Context Awareness**: Chat understands video content and summaries
- **Vector Search**: AI can find relevant videos based on conversation context
- **Streaming Responses**: Real-time chat experience

## 🚀 Deployment

### Vercel Deployment

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables in Vercel dashboard
4. Deploy the Python service separately (Railway, Google Cloud Run, etc.)

### Environment Variables for Production

```env
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key
PYTHON_AI_SERVICE_URL=your_production_python_service_url
PYTHON_AI_SERVICE_API_KEY=your_production_api_key
```

## 🧪 Development Scripts

Custom development scripts are available in `package.dev.json`:

```bash
# Start all services
npm run dev:all

# Start only NextJS
npm run dev:next

# Start only Python service
npm run dev:python

# Build for production
npm run build

# Run type checking
npm run type-check

# Run linting
npm run lint
```

## 📊 Performance Optimization

- **Vector Indexes**: Optimized pgvector indexes for fast similarity search
- **Connection Pooling**: Efficient database connections through Supabase
- **Component Lazy Loading**: Dynamic imports for better initial load times
- **API Caching**: Strategic caching of frequently accessed data
- **Image Optimization**: NextJS automatic image optimization for thumbnails

## 🔒 Security

- **Environment Variables**: Sensitive data stored in environment variables
- **API Key Protection**: Service-to-service authentication
- **Input Validation**: Comprehensive validation on all API endpoints
- **Rate Limiting**: Protection against abuse (implement as needed)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Troubleshooting

### Common Issues

1. **Supabase Connection Issues**
   - Verify your environment variables
   - Check Supabase project status
   - Ensure database tables exist

2. **Python Service Not Starting**
   - Check Python version compatibility
   - Verify `gemini_methods` path
   - Ensure all dependencies are installed

3. **Vector Search Not Working**
   - Confirm pgvector extension is enabled
   - Check if embeddings are generated
   - Verify vector column exists

### Getting Help

- Check the [Issues](issues) page for known problems
- Review the [Discussions](discussions) for community support
- Contact the development team for urgent issues

## 🏗️ Roadmap

- [ ] Authentication and user management
- [ ] Video upload and processing pipeline
- [ ] Advanced analytics and insights
- [ ] Mobile app development
- [ ] Offline functionality
- [ ] Multi-language support
