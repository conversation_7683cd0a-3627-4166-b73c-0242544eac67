WEBVTT
Kind: captions
Language: en

00:00:00.199 --> 00:00:03.310 align:start position:0%
 
llama<00:00:00.680><c> 3.2</c><00:00:01.680><c> it</c><00:00:01.959><c> just</c><00:00:02.240><c> got</c><00:00:02.480><c> released</c><00:00:02.879><c> a</c><00:00:03.000><c> couple</c>

00:00:03.310 --> 00:00:03.320 align:start position:0%
llama 3.2 it just got released a couple
 

00:00:03.320 --> 00:00:05.789 align:start position:0%
llama 3.2 it just got released a couple
days<00:00:03.520><c> ago</c><00:00:03.840><c> and</c><00:00:04.040><c> folks</c><00:00:04.319><c> are</c><00:00:05.040><c> pretty</c><00:00:05.359><c> excited</c>

00:00:05.789 --> 00:00:05.799 align:start position:0%
days ago and folks are pretty excited
 

00:00:05.799 --> 00:00:08.430 align:start position:0%
days ago and folks are pretty excited
about<00:00:06.040><c> it</c><00:00:06.640><c> a</c><00:00:06.799><c> few</c><00:00:07.080><c> YouTubers</c><00:00:07.600><c> were</c><00:00:08.000><c> sponsored</c>

00:00:08.430 --> 00:00:08.440 align:start position:0%
about it a few YouTubers were sponsored
 

00:00:08.440 --> 00:00:11.589 align:start position:0%
about it a few YouTubers were sponsored
by<00:00:08.639><c> meta</c><00:00:09.000><c> for</c><00:00:09.240><c> this</c><00:00:09.480><c> release</c>

00:00:11.589 --> 00:00:11.599 align:start position:0%
by meta for this release
 

00:00:11.599 --> 00:00:14.470 align:start position:0%
by meta for this release
notably<00:00:12.599><c> I</c><00:00:12.759><c> wasn't</c><00:00:13.639><c> and</c><00:00:13.799><c> looking</c><00:00:14.080><c> at</c><00:00:14.240><c> some</c><00:00:14.360><c> of</c>

00:00:14.470 --> 00:00:14.480 align:start position:0%
notably I wasn't and looking at some of
 

00:00:14.480 --> 00:00:17.630 align:start position:0%
notably I wasn't and looking at some of
the<00:00:14.639><c> videos</c><00:00:14.960><c> that</c><00:00:15.160><c> were</c><00:00:15.880><c> sponsored</c><00:00:16.880><c> well</c><00:00:17.279><c> it</c>

00:00:17.630 --> 00:00:17.640 align:start position:0%
the videos that were sponsored well it
 

00:00:17.640 --> 00:00:20.790 align:start position:0%
the videos that were sponsored well it
seems<00:00:18.080><c> like</c><00:00:18.279><c> they</c><00:00:18.439><c> made</c><00:00:18.680><c> a</c><00:00:18.880><c> few</c><00:00:19.520><c> odd</c><00:00:20.039><c> choices</c>

00:00:20.790 --> 00:00:20.800 align:start position:0%
seems like they made a few odd choices
 

00:00:20.800 --> 00:00:24.170 align:start position:0%
seems like they made a few odd choices
but<00:00:21.199><c> anyway</c><00:00:22.480><c> meta</c><00:00:23.480><c> next</c>

00:00:24.170 --> 00:00:24.180 align:start position:0%
but anyway meta next
 

00:00:24.180 --> 00:00:25.269 align:start position:0%
but anyway meta next
[Music]

00:00:25.269 --> 00:00:25.279 align:start position:0%
[Music]
 

00:00:25.279 --> 00:00:27.109 align:start position:0%
[Music]
time<00:00:26.279><c> I'm</c>

00:00:27.109 --> 00:00:27.119 align:start position:0%
time I'm
 

00:00:27.119 --> 00:00:29.470 align:start position:0%
time I'm
available<00:00:28.119><c> my</c><00:00:28.240><c> name</c><00:00:28.359><c> is</c><00:00:28.480><c> Matt</c><00:00:28.679><c> Williams</c><00:00:29.080><c> and</c><00:00:29.279><c> I</c>

00:00:29.470 --> 00:00:29.480 align:start position:0%
available my name is Matt Williams and I
 

00:00:29.480 --> 00:00:31.830 align:start position:0%
available my name is Matt Williams and I
was<00:00:29.599><c> a</c><00:00:29.720><c> found</c><00:00:30.199><c> member</c><00:00:30.480><c> of</c><00:00:30.640><c> the</c><00:00:30.759><c> olama</c><00:00:31.240><c> team</c><00:00:31.679><c> I</c>

00:00:31.830 --> 00:00:31.840 align:start position:0%
was a found member of the olama team I
 

00:00:31.840 --> 00:00:34.670 align:start position:0%
was a found member of the olama team I
left<00:00:32.119><c> in</c><00:00:32.439><c> January</c><00:00:33.280><c> and</c><00:00:33.640><c> now</c><00:00:33.879><c> I'm</c><00:00:34.079><c> focused</c><00:00:34.440><c> on</c>

00:00:34.670 --> 00:00:34.680 align:start position:0%
left in January and now I'm focused on
 

00:00:34.680 --> 00:00:37.229 align:start position:0%
left in January and now I'm focused on
building<00:00:35.040><c> up</c><00:00:35.760><c> this</c><00:00:36.000><c> YouTube</c><00:00:36.440><c> channel</c><00:00:37.000><c> where</c><00:00:37.120><c> I</c>

00:00:37.229 --> 00:00:37.239 align:start position:0%
building up this YouTube channel where I
 

00:00:37.239 --> 00:00:40.830 align:start position:0%
building up this YouTube channel where I
tend<00:00:37.399><c> to</c><00:00:37.559><c> look</c><00:00:37.719><c> at</c><00:00:38.000><c> all</c><00:00:38.280><c> things</c><00:00:38.840><c> local</c><00:00:39.640><c> Ai</c><00:00:40.640><c> and</c>

00:00:40.830 --> 00:00:40.840 align:start position:0%
tend to look at all things local Ai and
 

00:00:40.840 --> 00:00:43.910 align:start position:0%
tend to look at all things local Ai and
llama<00:00:41.399><c> 3.2</c><00:00:42.399><c> definitely</c><00:00:42.920><c> fits</c><00:00:43.360><c> into</c><00:00:43.680><c> that</c>

00:00:43.910 --> 00:00:43.920 align:start position:0%
llama 3.2 definitely fits into that
 

00:00:43.920 --> 00:00:46.869 align:start position:0%
llama 3.2 definitely fits into that
category<00:00:44.760><c> so</c><00:00:45.200><c> I</c><00:00:45.360><c> thought</c><00:00:45.640><c> I'd</c><00:00:45.840><c> take</c><00:00:46.079><c> a</c><00:00:46.280><c> look</c><00:00:46.640><c> at</c>

00:00:46.869 --> 00:00:46.879 align:start position:0%
category so I thought I'd take a look at
 

00:00:46.879 --> 00:00:49.189 align:start position:0%
category so I thought I'd take a look at
the<00:00:47.039><c> model</c><00:00:47.440><c> from</c><00:00:47.640><c> a</c><00:00:47.800><c> few</c><00:00:48.120><c> different</c>

00:00:49.189 --> 00:00:49.199 align:start position:0%
the model from a few different
 

00:00:49.199 --> 00:00:51.590 align:start position:0%
the model from a few different
perspectives<00:00:50.199><c> first</c><00:00:50.879><c> I'll</c><00:00:51.079><c> just</c><00:00:51.239><c> start</c>

00:00:51.590 --> 00:00:51.600 align:start position:0%
perspectives first I'll just start
 

00:00:51.600 --> 00:00:53.590 align:start position:0%
perspectives first I'll just start
asking<00:00:52.000><c> some</c><00:00:52.239><c> questions</c><00:00:53.239><c> then</c><00:00:53.399><c> we'll</c>

00:00:53.590 --> 00:00:53.600 align:start position:0%
asking some questions then we'll
 

00:00:53.600 --> 00:00:56.150 align:start position:0%
asking some questions then we'll
summarize<00:00:54.280><c> some</c><00:00:54.640><c> content</c><00:00:55.640><c> and</c><00:00:55.760><c> then</c><00:00:55.920><c> we'll</c>

00:00:56.150 --> 00:00:56.160 align:start position:0%
summarize some content and then we'll
 

00:00:56.160 --> 00:00:59.310 align:start position:0%
summarize some content and then we'll
look<00:00:56.359><c> at</c><00:00:56.719><c> the</c><00:00:56.920><c> tools</c><00:00:57.760><c> capability</c><00:00:58.760><c> finally</c>

00:00:59.310 --> 00:00:59.320 align:start position:0%
look at the tools capability finally
 

00:00:59.320 --> 00:01:01.430 align:start position:0%
look at the tools capability finally
I'll<00:00:59.480><c> take</c><00:00:59.600><c> a</c><00:00:59.680><c> look</c><00:01:00.039><c> at</c><00:01:00.199><c> one</c><00:01:00.280><c> of</c><00:01:00.399><c> the</c><00:01:00.559><c> ways</c><00:01:01.079><c> I</c>

00:01:01.430 --> 00:01:01.440 align:start position:0%
I'll take a look at one of the ways I
 

00:01:01.440 --> 00:01:03.990 align:start position:0%
I'll take a look at one of the ways I
actually<00:01:01.680><c> use</c><00:01:02.160><c> models</c><00:01:03.160><c> I</c><00:01:03.239><c> think</c><00:01:03.399><c> it</c><00:01:03.519><c> should</c><00:01:03.680><c> be</c>

00:01:03.990 --> 00:01:04.000 align:start position:0%
actually use models I think it should be
 

00:01:04.000 --> 00:01:06.950 align:start position:0%
actually use models I think it should be
kind<00:01:04.119><c> of</c><00:01:04.400><c> fun</c><00:01:05.400><c> so</c><00:01:05.640><c> let's</c><00:01:05.960><c> first</c><00:01:06.240><c> take</c><00:01:06.479><c> a</c><00:01:06.680><c> look</c>

00:01:06.950 --> 00:01:06.960 align:start position:0%
kind of fun so let's first take a look
 

00:01:06.960 --> 00:01:09.789 align:start position:0%
kind of fun so let's first take a look
at<00:01:07.159><c> the</c><00:01:07.320><c> announcement</c><00:01:08.000><c> blog</c><00:01:08.400><c> post</c><00:01:08.680><c> from</c><00:01:08.880><c> meta</c>

00:01:09.789 --> 00:01:09.799 align:start position:0%
at the announcement blog post from meta
 

00:01:09.799 --> 00:01:13.550 align:start position:0%
at the announcement blog post from meta
llama<00:01:10.360><c> 3.2</c><00:01:11.360><c> revolutionizing</c><00:01:12.360><c> Edge</c><00:01:12.799><c> Ai</c><00:01:13.280><c> and</c>

00:01:13.550 --> 00:01:13.560 align:start position:0%
llama 3.2 revolutionizing Edge Ai and
 

00:01:13.560 --> 00:01:16.830 align:start position:0%
llama 3.2 revolutionizing Edge Ai and
vision<00:01:14.280><c> with</c><00:01:14.600><c> open</c><00:01:15.159><c> customizable</c>

00:01:16.830 --> 00:01:16.840 align:start position:0%
vision with open customizable
 

00:01:16.840 --> 00:01:19.310 align:start position:0%
vision with open customizable
models<00:01:17.840><c> right</c><00:01:18.040><c> up</c><00:01:18.240><c> at</c><00:01:18.360><c> the</c><00:01:18.560><c> top</c><00:01:18.920><c> it</c><00:01:19.040><c> talks</c>

00:01:19.310 --> 00:01:19.320 align:start position:0%
models right up at the top it talks
 

00:01:19.320 --> 00:01:22.190 align:start position:0%
models right up at the top it talks
about<00:01:19.560><c> the</c><00:01:19.720><c> 11b</c><00:01:20.280><c> and</c><00:01:20.439><c> 90b</c><00:01:21.159><c> vision</c><00:01:21.479><c> models</c><00:01:22.040><c> as</c>

00:01:22.190 --> 00:01:22.200 align:start position:0%
about the 11b and 90b vision models as
 

00:01:22.200 --> 00:01:25.830 align:start position:0%
about the 11b and 90b vision models as
well<00:01:22.360><c> as</c><00:01:22.520><c> the</c><00:01:22.680><c> 1B</c><00:01:23.159><c> and</c><00:01:23.320><c> 3B</c><00:01:24.079><c> text</c><00:01:24.360><c> only</c><00:01:24.840><c> models</c>

00:01:25.830 --> 00:01:25.840 align:start position:0%
well as the 1B and 3B text only models
 

00:01:25.840 --> 00:01:27.990 align:start position:0%
well as the 1B and 3B text only models
the<00:01:25.960><c> vision</c><00:01:26.280><c> models</c><00:01:26.759><c> are</c><00:01:27.040><c> also</c><00:01:27.439><c> really</c><00:01:27.720><c> good</c>

00:01:27.990 --> 00:01:28.000 align:start position:0%
the vision models are also really good
 

00:01:28.000 --> 00:01:30.670 align:start position:0%
the vision models are also really good
at<00:01:28.200><c> text</c><00:01:29.040><c> the</c><00:01:29.159><c> next</c><00:01:29.360><c> bullet</c><00:01:29.680><c> point</c><00:01:29.920><c> points</c><00:01:30.280><c> out</c>

00:01:30.670 --> 00:01:30.680 align:start position:0%
at text the next bullet point points out
 

00:01:30.680 --> 00:01:33.789 align:start position:0%
at text the next bullet point points out
a<00:01:30.960><c> context</c><00:01:31.360><c> length</c><00:01:31.680><c> of</c><00:01:31.960><c> 128k</c><00:01:32.960><c> tokens</c><00:01:33.479><c> for</c><00:01:33.680><c> the</c>

00:01:33.789 --> 00:01:33.799 align:start position:0%
a context length of 128k tokens for the
 

00:01:33.799 --> 00:01:35.710 align:start position:0%
a context length of 128k tokens for the
smaller<00:01:34.200><c> models</c><00:01:34.640><c> but</c><00:01:34.960><c> doesn't</c><00:01:35.280><c> mention</c><00:01:35.520><c> a</c>

00:01:35.710 --> 00:01:35.720 align:start position:0%
smaller models but doesn't mention a
 

00:01:35.720 --> 00:01:38.069 align:start position:0%
smaller models but doesn't mention a
context<00:01:36.119><c> size</c><00:01:36.320><c> for</c><00:01:36.479><c> the</c><00:01:36.600><c> larger</c><00:01:37.000><c> models</c><00:01:38.000><c> we</c>

00:01:38.069 --> 00:01:38.079 align:start position:0%
context size for the larger models we
 

00:01:38.079 --> 00:01:40.069 align:start position:0%
context size for the larger models we
see<00:01:38.280><c> that</c><00:01:38.399><c> the</c><00:01:38.520><c> vision</c><00:01:38.799><c> models</c><00:01:39.159><c> do</c><00:01:39.479><c> the</c><00:01:39.680><c> exact</c>

00:01:40.069 --> 00:01:40.079 align:start position:0%
see that the vision models do the exact
 

00:01:40.079 --> 00:01:42.630 align:start position:0%
see that the vision models do the exact
same<00:01:40.320><c> thing</c><00:01:40.520><c> as</c><00:01:40.640><c> the</c><00:01:40.759><c> 3.1</c><00:01:41.560><c> models</c><00:01:42.119><c> in</c><00:01:42.280><c> addition</c>

00:01:42.630 --> 00:01:42.640 align:start position:0%
same thing as the 3.1 models in addition
 

00:01:42.640 --> 00:01:45.550 align:start position:0%
same thing as the 3.1 models in addition
to<00:01:42.840><c> supporting</c><00:01:43.479><c> Vision</c><00:01:44.399><c> scroll</c><00:01:44.840><c> down</c><00:01:45.360><c> and</c><00:01:45.479><c> we</c>

00:01:45.550 --> 00:01:45.560 align:start position:0%
to supporting Vision scroll down and we
 

00:01:45.560 --> 00:01:48.069 align:start position:0%
to supporting Vision scroll down and we
see<00:01:45.799><c> a</c><00:01:45.960><c> bit</c><00:01:46.159><c> more</c><00:01:46.600><c> about</c><00:01:46.880><c> the</c><00:01:47.119><c> image</c><00:01:47.560><c> reasoning</c>

00:01:48.069 --> 00:01:48.079 align:start position:0%
see a bit more about the image reasoning
 

00:01:48.079 --> 00:01:50.830 align:start position:0%
see a bit more about the image reasoning
use<00:01:48.439><c> cases</c><00:01:48.960><c> supported</c><00:01:49.439><c> by</c><00:01:49.600><c> the</c><00:01:49.759><c> vision</c><00:01:50.000><c> models</c>

00:01:50.830 --> 00:01:50.840 align:start position:0%
use cases supported by the vision models
 

00:01:50.840 --> 00:01:52.630 align:start position:0%
use cases supported by the vision models
such<00:01:51.000><c> as</c><00:01:51.240><c> document</c><00:01:51.640><c> level</c><00:01:52.320><c> understanding</c>

00:01:52.630 --> 00:01:52.640 align:start position:0%
such as document level understanding
 

00:01:52.640 --> 00:01:54.870 align:start position:0%
such as document level understanding
including<00:01:53.079><c> charts</c><00:01:53.439><c> and</c><00:01:53.640><c> graphs</c><00:01:54.439><c> captioning</c>

00:01:54.870 --> 00:01:54.880 align:start position:0%
including charts and graphs captioning
 

00:01:54.880 --> 00:01:57.590 align:start position:0%
including charts and graphs captioning
of<00:01:55.119><c> images</c><00:01:55.920><c> and</c><00:01:56.119><c> visual</c><00:01:56.520><c> grounding</c><00:01:57.119><c> tasks</c>

00:01:57.590 --> 00:01:57.600 align:start position:0%
of images and visual grounding tasks
 

00:01:57.600 --> 00:01:59.310 align:start position:0%
of images and visual grounding tasks
such<00:01:57.799><c> as</c><00:01:58.039><c> directionally</c><00:01:58.600><c> pinpointing</c>

00:01:59.310 --> 00:01:59.320 align:start position:0%
such as directionally pinpointing
 

00:01:59.320 --> 00:02:01.990 align:start position:0%
such as directionally pinpointing
objects<00:02:00.240><c> in</c><00:02:00.520><c> images</c><00:02:01.079><c> based</c><00:02:01.320><c> on</c><00:02:01.600><c> natural</c>

00:02:01.990 --> 00:02:02.000 align:start position:0%
objects in images based on natural
 

00:02:02.000 --> 00:02:04.550 align:start position:0%
objects in images based on natural
language<00:02:02.799><c> descriptions</c><00:02:03.799><c> this</c><00:02:03.960><c> sounds</c><00:02:04.320><c> pretty</c>

00:02:04.550 --> 00:02:04.560 align:start position:0%
language descriptions this sounds pretty
 

00:02:04.560 --> 00:02:06.510 align:start position:0%
language descriptions this sounds pretty
cool<00:02:05.159><c> because</c><00:02:05.439><c> this</c><00:02:05.560><c> has</c><00:02:05.719><c> always</c><00:02:05.920><c> been</c><00:02:06.200><c> a</c>

00:02:06.510 --> 00:02:06.520 align:start position:0%
cool because this has always been a
 

00:02:06.520 --> 00:02:09.150 align:start position:0%
cool because this has always been a
problem<00:02:06.880><c> when</c><00:02:07.079><c> adding</c><00:02:07.399><c> docs</c><00:02:07.960><c> with</c><00:02:08.239><c> graphs</c><00:02:08.959><c> to</c>

00:02:09.150 --> 00:02:09.160 align:start position:0%
problem when adding docs with graphs to
 

00:02:09.160 --> 00:02:11.510 align:start position:0%
problem when adding docs with graphs to
a<00:02:09.280><c> rag</c><00:02:09.640><c> system</c><00:02:10.640><c> of</c><00:02:10.759><c> course</c><00:02:10.920><c> they</c><00:02:11.039><c> include</c><00:02:11.360><c> some</c>

00:02:11.510 --> 00:02:11.520 align:start position:0%
a rag system of course they include some
 

00:02:11.520 --> 00:02:14.030 align:start position:0%
a rag system of course they include some
benchmarks<00:02:12.120><c> but</c><00:02:12.480><c> we</c><00:02:12.640><c> all</c><00:02:12.800><c> know</c><00:02:13.120><c> my</c><00:02:13.319><c> opinion</c><00:02:13.720><c> on</c>

00:02:14.030 --> 00:02:14.040 align:start position:0%
benchmarks but we all know my opinion on
 

00:02:14.040 --> 00:02:16.550 align:start position:0%
benchmarks but we all know my opinion on
those<00:02:14.680><c> so</c><00:02:14.840><c> we'll</c><00:02:15.040><c> just</c><00:02:15.200><c> get</c><00:02:15.400><c> right</c><00:02:15.640><c> past</c><00:02:16.000><c> that</c>

00:02:16.550 --> 00:02:16.560 align:start position:0%
those so we'll just get right past that
 

00:02:16.560 --> 00:02:18.550 align:start position:0%
those so we'll just get right past that
there<00:02:16.680><c> are</c><00:02:16.879><c> some</c><00:02:17.200><c> cool</c><00:02:17.560><c> demo</c><00:02:17.879><c> gips</c><00:02:18.239><c> in</c><00:02:18.360><c> the</c>

00:02:18.550 --> 00:02:18.560 align:start position:0%
there are some cool demo gips in the
 

00:02:18.560 --> 00:02:21.030 align:start position:0%
there are some cool demo gips in the
article<00:02:19.120><c> further</c><00:02:19.599><c> down</c><00:02:19.959><c> so</c><00:02:20.360><c> it's</c><00:02:20.599><c> definitely</c>

00:02:21.030 --> 00:02:21.040 align:start position:0%
article further down so it's definitely
 

00:02:21.040 --> 00:02:24.070 align:start position:0%
article further down so it's definitely
worth<00:02:21.360><c> taking</c><00:02:21.640><c> a</c><00:02:21.840><c> look</c><00:02:22.040><c> at</c><00:02:22.879><c> but</c><00:02:23.080><c> let's</c><00:02:23.280><c> move</c><00:02:23.519><c> on</c>

00:02:24.070 --> 00:02:24.080 align:start position:0%
worth taking a look at but let's move on
 

00:02:24.080 --> 00:02:26.869 align:start position:0%
worth taking a look at but let's move on
to<00:02:24.440><c> actually</c><00:02:24.760><c> using</c><00:02:25.120><c> it</c><00:02:25.840><c> of</c><00:02:26.000><c> course</c><00:02:26.640><c> I'm</c><00:02:26.800><c> going</c>

00:02:26.869 --> 00:02:26.879 align:start position:0%
to actually using it of course I'm going
 

00:02:26.879 --> 00:02:29.990 align:start position:0%
to actually using it of course I'm going
to<00:02:27.040><c> be</c><00:02:27.160><c> using</c><00:02:27.680><c> AMA</c><00:02:28.280><c> which</c><00:02:28.440><c> is</c><00:02:28.640><c> the</c><00:02:28.959><c> best</c><00:02:29.280><c> way</c><00:02:29.879><c> to</c>

00:02:29.990 --> 00:02:30.000 align:start position:0%
to be using AMA which is the best way to
 

00:02:30.000 --> 00:02:33.110 align:start position:0%
to be using AMA which is the best way to
run<00:02:30.440><c> models</c><00:02:31.200><c> locally</c><00:02:32.200><c> you</c><00:02:32.280><c> can</c><00:02:32.480><c> find</c><00:02:32.680><c> out</c><00:02:32.879><c> more</c>

00:02:33.110 --> 00:02:33.120 align:start position:0%
run models locally you can find out more
 

00:02:33.120 --> 00:02:36.390 align:start position:0%
run models locally you can find out more
about<00:02:33.319><c> olama</c><00:02:33.879><c> at</c><00:02:34.080><c> ama.com</c><00:02:34.879><c> click</c><00:02:35.760><c> the</c><00:02:35.920><c> button</c>

00:02:36.390 --> 00:02:36.400 align:start position:0%
about olama at ama.com click the button
 

00:02:36.400 --> 00:02:38.309 align:start position:0%
about olama at ama.com click the button
in<00:02:36.560><c> the</c><00:02:36.680><c> middle</c><00:02:36.959><c> of</c><00:02:37.080><c> the</c><00:02:37.239><c> page</c><00:02:37.480><c> to</c><00:02:37.680><c> download</c>

00:02:38.309 --> 00:02:38.319 align:start position:0%
in the middle of the page to download
 

00:02:38.319 --> 00:02:40.990 align:start position:0%
in the middle of the page to download
and<00:02:38.560><c> install</c><00:02:38.959><c> it</c><00:02:39.400><c> it's</c><00:02:39.720><c> super</c><00:02:40.080><c> easy</c><00:02:40.680><c> and</c><00:02:40.840><c> I've</c>

00:02:40.990 --> 00:02:41.000 align:start position:0%
and install it it's super easy and I've
 

00:02:41.000 --> 00:02:44.309 align:start position:0%
and install it it's super easy and I've
done<00:02:41.599><c> tons</c><00:02:42.480><c> of</c><00:02:42.720><c> tutorials</c><00:02:43.599><c> on</c><00:02:43.840><c> how</c><00:02:43.959><c> to</c><00:02:44.159><c> get</c>

00:02:44.309 --> 00:02:44.319 align:start position:0%
done tons of tutorials on how to get
 

00:02:44.319 --> 00:02:46.149 align:start position:0%
done tons of tutorials on how to get
started<00:02:44.800><c> so</c><00:02:45.000><c> you</c><00:02:45.120><c> should</c><00:02:45.400><c> definitely</c><00:02:45.879><c> check</c>

00:02:46.149 --> 00:02:46.159 align:start position:0%
started so you should definitely check
 

00:02:46.159 --> 00:02:50.190 align:start position:0%
started so you should definitely check
that<00:02:46.319><c> out</c><00:02:47.159><c> then</c><00:02:47.640><c> run</c><00:02:47.959><c> oama</c><00:02:48.519><c> pull</c><00:02:49.080><c> llama</c><00:02:49.400><c> 3.2</c>

00:02:50.190 --> 00:02:50.200 align:start position:0%
that out then run oama pull llama 3.2
 

00:02:50.200 --> 00:02:53.350 align:start position:0%
that out then run oama pull llama 3.2
colon<00:02:50.680><c> 1B</c><00:02:51.680><c> to</c><00:02:51.879><c> grab</c><00:02:52.159><c> the</c><00:02:52.319><c> 1</c><00:02:52.599><c> billion</c><00:02:52.959><c> parameter</c>

00:02:53.350 --> 00:02:53.360 align:start position:0%
colon 1B to grab the 1 billion parameter
 

00:02:53.360 --> 00:02:55.910 align:start position:0%
colon 1B to grab the 1 billion parameter
model<00:02:54.200><c> okay</c><00:02:54.400><c> so</c><00:02:54.959><c> what</c><00:02:55.200><c> questions</c><00:02:55.560><c> do</c><00:02:55.680><c> we</c><00:02:55.800><c> want</c>

00:02:55.910 --> 00:02:55.920 align:start position:0%
model okay so what questions do we want
 

00:02:55.920 --> 00:02:59.830 align:start position:0%
model okay so what questions do we want
to<00:02:56.239><c> ask</c><00:02:57.239><c> I</c><00:02:57.519><c> can't</c><00:02:57.879><c> look</c><00:02:58.120><c> at</c><00:02:58.599><c> any</c><00:02:58.959><c> model</c><00:02:59.519><c> without</c>

00:02:59.830 --> 00:02:59.840 align:start position:0%
to ask I can't look at any model without
 

00:02:59.840 --> 00:03:03.070 align:start position:0%
to ask I can't look at any model without
without<00:03:00.159><c> asking</c><00:03:00.840><c> my</c><00:03:01.360><c> favorite</c><00:03:02.360><c> why</c><00:03:02.680><c> is</c><00:03:02.840><c> the</c>

00:03:03.070 --> 00:03:03.080 align:start position:0%
without asking my favorite why is the
 

00:03:03.080 --> 00:03:07.710 align:start position:0%
without asking my favorite why is the
sky<00:03:03.640><c> blue</c><00:03:04.640><c> and</c><00:03:04.879><c> that</c><00:03:05.200><c> is</c><00:03:05.799><c> blazingly</c><00:03:06.720><c> fast</c><00:03:07.400><c> turn</c>

00:03:07.710 --> 00:03:07.720 align:start position:0%
sky blue and that is blazingly fast turn
 

00:03:07.720 --> 00:03:10.470 align:start position:0%
sky blue and that is blazingly fast turn
on<00:03:07.959><c> verbose</c><00:03:08.480><c> and</c><00:03:08.640><c> try</c><00:03:08.920><c> that</c><00:03:09.120><c> again</c>

00:03:10.470 --> 00:03:10.480 align:start position:0%
on verbose and try that again
 

00:03:10.480 --> 00:03:13.990 align:start position:0%
on verbose and try that again
127<00:03:11.480><c> tokens</c><00:03:11.879><c> per</c><00:03:12.080><c> second</c><00:03:12.480><c> is</c><00:03:12.799><c> kind</c><00:03:13.000><c> of</c><00:03:13.280><c> amazing</c>

00:03:13.990 --> 00:03:14.000 align:start position:0%
127 tokens per second is kind of amazing
 

00:03:14.000 --> 00:03:16.990 align:start position:0%
127 tokens per second is kind of amazing
for<00:03:14.200><c> an</c><00:03:14.440><c> answer</c><00:03:14.840><c> that</c><00:03:14.959><c> is</c><00:03:15.239><c> that</c><00:03:15.599><c> good</c><00:03:16.599><c> now</c><00:03:16.799><c> a</c>

00:03:16.990 --> 00:03:17.000 align:start position:0%
for an answer that is that good now a
 

00:03:17.000 --> 00:03:18.670 align:start position:0%
for an answer that is that good now a
lot<00:03:17.120><c> of</c><00:03:17.319><c> folks</c><00:03:17.599><c> like</c><00:03:17.760><c> to</c><00:03:17.959><c> go</c><00:03:18.159><c> straight</c><00:03:18.400><c> to</c><00:03:18.560><c> the</c>

00:03:18.670 --> 00:03:18.680 align:start position:0%
lot of folks like to go straight to the
 

00:03:18.680 --> 00:03:20.589 align:start position:0%
lot of folks like to go straight to the
riddles<00:03:19.080><c> and</c><00:03:19.280><c> logic</c><00:03:19.640><c> puzzles</c><00:03:20.040><c> to</c><00:03:20.200><c> test</c><00:03:20.440><c> a</c>

00:03:20.589 --> 00:03:20.599 align:start position:0%
riddles and logic puzzles to test a
 

00:03:20.599 --> 00:03:23.589 align:start position:0%
riddles and logic puzzles to test a
model<00:03:21.440><c> in</c><00:03:21.599><c> fact</c><00:03:22.280><c> they</c><00:03:22.400><c> will</c><00:03:22.640><c> grade</c><00:03:23.000><c> a</c><00:03:23.159><c> model</c>

00:03:23.589 --> 00:03:23.599 align:start position:0%
model in fact they will grade a model
 

00:03:23.599 --> 00:03:25.509 align:start position:0%
model in fact they will grade a model
purely<00:03:24.159><c> on</c><00:03:24.280><c> its</c><00:03:24.519><c> ability</c><00:03:24.920><c> to</c><00:03:25.120><c> count</c><00:03:25.360><c> the</c>

00:03:25.509 --> 00:03:25.519 align:start position:0%
purely on its ability to count the
 

00:03:25.519 --> 00:03:28.470 align:start position:0%
purely on its ability to count the
number<00:03:25.760><c> of</c><00:03:26.040><c> RS</c><00:03:26.640><c> in</c><00:03:26.760><c> a</c><00:03:27.000><c> word</c><00:03:27.959><c> something</c><00:03:28.280><c> that</c>

00:03:28.470 --> 00:03:28.480 align:start position:0%
number of RS in a word something that
 

00:03:28.480 --> 00:03:30.270 align:start position:0%
number of RS in a word something that
models<00:03:29.000><c> were</c><00:03:29.360><c> not</c>

00:03:30.270 --> 00:03:30.280 align:start position:0%
models were not
 

00:03:30.280 --> 00:03:32.030 align:start position:0%
models were not
traditionally<00:03:30.959><c> designed</c><00:03:31.360><c> to</c><00:03:31.480><c> be</c><00:03:31.599><c> able</c><00:03:31.799><c> to</c>

00:03:32.030 --> 00:03:32.040 align:start position:0%
traditionally designed to be able to
 

00:03:32.040 --> 00:03:35.030 align:start position:0%
traditionally designed to be able to
answer<00:03:33.040><c> it's</c><00:03:33.280><c> like</c><00:03:33.599><c> me</c><00:03:34.000><c> grading</c><00:03:34.599><c> your</c>

00:03:35.030 --> 00:03:35.040 align:start position:0%
answer it's like me grading your
 

00:03:35.040 --> 00:03:37.429 align:start position:0%
answer it's like me grading your
worthiness<00:03:35.920><c> based</c><00:03:36.280><c> on</c><00:03:36.560><c> whether</c><00:03:36.840><c> you</c><00:03:37.000><c> can</c><00:03:37.200><c> hold</c>

00:03:37.429 --> 00:03:37.439 align:start position:0%
worthiness based on whether you can hold
 

00:03:37.439 --> 00:03:39.990 align:start position:0%
worthiness based on whether you can hold
your<00:03:37.760><c> breath</c><00:03:38.080><c> for</c><00:03:38.280><c> more</c><00:03:38.439><c> than</c><00:03:38.560><c> 2</c><00:03:39.000><c> minutes</c>

00:03:39.990 --> 00:03:40.000 align:start position:0%
your breath for more than 2 minutes
 

00:03:40.000 --> 00:03:42.750 align:start position:0%
your breath for more than 2 minutes
that's<00:03:40.159><c> a</c><00:03:40.400><c> skill</c><00:03:40.840><c> that</c><00:03:41.159><c> isn't</c><00:03:41.560><c> even</c><00:03:42.080><c> relevant</c>

00:03:42.750 --> 00:03:42.760 align:start position:0%
that's a skill that isn't even relevant
 

00:03:42.760 --> 00:03:44.990 align:start position:0%
that's a skill that isn't even relevant
for<00:03:43.000><c> most</c><00:03:43.200><c> of</c><00:03:43.319><c> us</c><00:03:43.640><c> though</c><00:03:44.400><c> I</c><00:03:44.480><c> used</c><00:03:44.680><c> to</c><00:03:44.799><c> work</c>

00:03:44.990 --> 00:03:45.000 align:start position:0%
for most of us though I used to work
 

00:03:45.000 --> 00:03:48.270 align:start position:0%
for most of us though I used to work
with<00:03:45.120><c> a</c><00:03:45.360><c> guy</c><00:03:45.799><c> whose</c><00:03:46.239><c> Dad</c><00:03:47.040><c> held</c><00:03:47.280><c> a</c><00:03:47.400><c> world</c><00:03:47.760><c> record</c>

00:03:48.270 --> 00:03:48.280 align:start position:0%
with a guy whose Dad held a world record
 

00:03:48.280 --> 00:03:50.630 align:start position:0%
with a guy whose Dad held a world record
in<00:03:48.439><c> free</c><00:03:48.799><c> diving</c><00:03:49.239><c> which</c><00:03:49.439><c> required</c><00:03:49.959><c> that</c><00:03:50.120><c> skill</c>

00:03:50.630 --> 00:03:50.640 align:start position:0%
in free diving which required that skill
 

00:03:50.640 --> 00:03:53.229 align:start position:0%
in free diving which required that skill
they<00:03:50.799><c> even</c><00:03:51.040><c> made</c><00:03:51.200><c> a</c><00:03:51.360><c> movie</c><00:03:51.720><c> about</c><00:03:51.959><c> him</c><00:03:52.959><c> oh</c>

00:03:53.229 --> 00:03:53.239 align:start position:0%
they even made a movie about him oh
 

00:03:53.239 --> 00:03:55.470 align:start position:0%
they even made a movie about him oh
while<00:03:53.439><c> I'm</c><00:03:53.760><c> mid</c><00:03:54.040><c> tangent</c><00:03:54.640><c> let</c><00:03:54.760><c> me</c><00:03:55.000><c> remind</c><00:03:55.319><c> you</c>

00:03:55.470 --> 00:03:55.480 align:start position:0%
while I'm mid tangent let me remind you
 

00:03:55.480 --> 00:03:58.630 align:start position:0%
while I'm mid tangent let me remind you
to<00:03:55.840><c> like</c><00:03:56.599><c> And</c><00:03:56.840><c> subscribe</c><00:03:57.840><c> I</c><00:03:58.000><c> like</c><00:03:58.120><c> to</c><00:03:58.280><c> say</c><00:03:58.480><c> I'm</c>

00:03:58.630 --> 00:03:58.640 align:start position:0%
to like And subscribe I like to say I'm
 

00:03:58.640 --> 00:04:00.670 align:start position:0%
to like And subscribe I like to say I'm
working<00:03:58.879><c> on</c><00:03:59.040><c> my</c><00:03:59.159><c> first</c><00:03:59.799><c> million</c><00:04:00.120><c> subscribers</c>

00:04:00.670 --> 00:04:00.680 align:start position:0%
working on my first million subscribers
 

00:04:00.680 --> 00:04:04.630 align:start position:0%
working on my first million subscribers
and<00:04:01.000><c> only</c><00:04:01.360><c> need</c><00:04:01.760><c> about</c><00:04:02.480><c> 967</c><00:04:03.480><c> th000</c><00:04:03.879><c> more</c><00:04:04.400><c> to</c>

00:04:04.630 --> 00:04:04.640 align:start position:0%
and only need about 967 th000 more to
 

00:04:04.640 --> 00:04:06.710 align:start position:0%
and only need about 967 th000 more to
get<00:04:04.840><c> there</c><00:04:05.439><c> your</c><00:04:05.720><c> help</c><00:04:05.959><c> to</c><00:04:06.159><c> achieve</c><00:04:06.519><c> that</c>

00:04:06.710 --> 00:04:06.720 align:start position:0%
get there your help to achieve that
 

00:04:06.720 --> 00:04:09.630 align:start position:0%
get there your help to achieve that
would<00:04:06.879><c> be</c><00:04:07.799><c> greatly</c><00:04:08.200><c> appreciated</c><00:04:09.200><c> let's</c><00:04:09.400><c> stop</c>

00:04:09.630 --> 00:04:09.640 align:start position:0%
would be greatly appreciated let's stop
 

00:04:09.640 --> 00:04:11.910 align:start position:0%
would be greatly appreciated let's stop
the<00:04:09.799><c> tangent</c><00:04:10.239><c> and</c><00:04:10.400><c> go</c><00:04:10.640><c> back</c><00:04:10.799><c> to</c><00:04:10.920><c> the</c><00:04:11.079><c> model</c><00:04:11.640><c> now</c>

00:04:11.910 --> 00:04:11.920 align:start position:0%
the tangent and go back to the model now
 

00:04:11.920 --> 00:04:14.149 align:start position:0%
the tangent and go back to the model now
a<00:04:12.159><c> much</c><00:04:12.400><c> more</c><00:04:12.720><c> relevant</c><00:04:13.239><c> question</c><00:04:14.000><c> is</c>

00:04:14.149 --> 00:04:14.159 align:start position:0%
a much more relevant question is
 

00:04:14.159 --> 00:04:16.110 align:start position:0%
a much more relevant question is
something<00:04:14.400><c> you</c><00:04:14.560><c> might</c><00:04:14.879><c> actually</c><00:04:15.280><c> ask</c><00:04:15.879><c> I</c><00:04:16.000><c> have</c>

00:04:16.110 --> 00:04:16.120 align:start position:0%
something you might actually ask I have
 

00:04:16.120 --> 00:04:18.590 align:start position:0%
something you might actually ask I have
a<00:04:16.560><c> new</c><00:04:16.959><c> coffee</c><00:04:17.280><c> shop</c><00:04:17.600><c> on</c><00:04:17.759><c> Banbridge</c><00:04:18.280><c> Island</c>

00:04:18.590 --> 00:04:18.600 align:start position:0%
a new coffee shop on Banbridge Island
 

00:04:18.600 --> 00:04:21.390 align:start position:0%
a new coffee shop on Banbridge Island
come<00:04:18.759><c> up</c><00:04:18.959><c> with</c><00:04:19.280><c> five</c><00:04:19.600><c> great</c><00:04:19.880><c> photo</c><00:04:20.400><c> ideas</c><00:04:21.239><c> that</c>

00:04:21.390 --> 00:04:21.400 align:start position:0%
come up with five great photo ideas that
 

00:04:21.400 --> 00:04:23.310 align:start position:0%
come up with five great photo ideas that
I<00:04:21.479><c> can</c><00:04:21.680><c> take</c><00:04:21.880><c> for</c><00:04:22.040><c> an</c><00:04:22.280><c> advertisement</c><00:04:23.120><c> with</c>

00:04:23.310 --> 00:04:23.320 align:start position:0%
I can take for an advertisement with
 

00:04:23.320 --> 00:04:25.510 align:start position:0%
I can take for an advertisement with
that<00:04:23.440><c> would</c><00:04:23.759><c> showcase</c><00:04:24.199><c> the</c><00:04:24.320><c> shop</c><00:04:25.280><c> and</c><00:04:25.400><c> it</c>

00:04:25.510 --> 00:04:25.520 align:start position:0%
that would showcase the shop and it
 

00:04:25.520 --> 00:04:27.629 align:start position:0%
that would showcase the shop and it
comes<00:04:25.720><c> up</c><00:04:25.880><c> with</c><00:04:26.040><c> some</c><00:04:26.320><c> great</c><00:04:26.680><c> answers</c><00:04:27.479><c> how</c>

00:04:27.629 --> 00:04:27.639 align:start position:0%
comes up with some great answers how
 

00:04:27.639 --> 00:04:29.430 align:start position:0%
comes up with some great answers how
about<00:04:27.840><c> generating</c><00:04:28.280><c> a</c><00:04:28.400><c> few</c><00:04:28.560><c> tweets</c><00:04:28.960><c> to</c><00:04:29.120><c> help</c>

00:04:29.430 --> 00:04:29.440 align:start position:0%
about generating a few tweets to help
 

00:04:29.440 --> 00:04:32.670 align:start position:0%
about generating a few tweets to help
get<00:04:29.800><c> folks</c><00:04:30.039><c> to</c><00:04:30.199><c> the</c><00:04:30.360><c> shop</c><00:04:31.240><c> again</c><00:04:32.000><c> awesome</c>

00:04:32.670 --> 00:04:32.680 align:start position:0%
get folks to the shop again awesome
 

00:04:32.680 --> 00:04:34.950 align:start position:0%
get folks to the shop again awesome
answers<00:04:33.680><c> but</c><00:04:33.840><c> some</c><00:04:34.000><c> folks</c><00:04:34.240><c> need</c><00:04:34.400><c> a</c><00:04:34.520><c> Riddle</c>

00:04:34.950 --> 00:04:34.960 align:start position:0%
answers but some folks need a Riddle
 

00:04:34.960 --> 00:04:37.790 align:start position:0%
answers but some folks need a Riddle
question<00:04:35.400><c> so</c><00:04:35.759><c> let's</c><00:04:36.080><c> try</c><00:04:36.400><c> that</c><00:04:36.720><c> classic</c><00:04:37.520><c> three</c>

00:04:37.790 --> 00:04:37.800 align:start position:0%
question so let's try that classic three
 

00:04:37.800 --> 00:04:40.870 align:start position:0%
question so let's try that classic three
murderers<00:04:38.280><c> in</c><00:04:38.400><c> a</c><00:04:38.520><c> room</c><00:04:38.840><c> one</c><00:04:39.759><c> guess</c><00:04:40.000><c> what</c><00:04:40.759><c> it</c>

00:04:40.870 --> 00:04:40.880 align:start position:0%
murderers in a room one guess what it
 

00:04:40.880 --> 00:04:43.270 align:start position:0%
murderers in a room one guess what it
gets<00:04:41.039><c> it</c><00:04:41.240><c> wrong</c><00:04:41.880><c> it's</c><00:04:42.000><c> a</c><00:04:42.199><c> stupid</c><00:04:42.600><c> question</c><00:04:43.080><c> and</c>

00:04:43.270 --> 00:04:43.280 align:start position:0%
gets it wrong it's a stupid question and
 

00:04:43.280 --> 00:04:46.350 align:start position:0%
gets it wrong it's a stupid question and
I'm<00:04:43.680><c> not</c><00:04:43.960><c> all</c><00:04:44.240><c> that</c><00:04:44.440><c> bothered</c><00:04:44.840><c> by</c><00:04:45.000><c> it</c><00:04:45.960><c> let's</c><00:04:46.160><c> go</c>

00:04:46.350 --> 00:04:46.360 align:start position:0%
I'm not all that bothered by it let's go
 

00:04:46.360 --> 00:04:48.430 align:start position:0%
I'm not all that bothered by it let's go
back<00:04:46.479><c> to</c><00:04:46.639><c> a</c><00:04:46.800><c> question</c><00:04:47.240><c> that</c><00:04:47.440><c> is</c><00:04:47.759><c> actually</c>

00:04:48.430 --> 00:04:48.440 align:start position:0%
back to a question that is actually
 

00:04:48.440 --> 00:04:51.070 align:start position:0%
back to a question that is actually
useful<00:04:49.440><c> I</c><00:04:49.520><c> have</c><00:04:49.639><c> a</c><00:04:49.800><c> 5-year-old</c><00:04:50.320><c> daughter</c><00:04:50.759><c> who</c>

00:04:51.070 --> 00:04:51.080 align:start position:0%
useful I have a 5-year-old daughter who
 

00:04:51.080 --> 00:04:53.230 align:start position:0%
useful I have a 5-year-old daughter who
loves<00:04:51.560><c> asking</c><00:04:51.919><c> questions</c><00:04:52.360><c> that</c><00:04:52.680><c> I</c><00:04:52.840><c> learned</c>

00:04:53.230 --> 00:04:53.240 align:start position:0%
loves asking questions that I learned
 

00:04:53.240 --> 00:04:56.189 align:start position:0%
loves asking questions that I learned
the<00:04:53.400><c> answer</c><00:04:53.800><c> to</c><00:04:54.440><c> 40</c><00:04:54.960><c> years</c><00:04:55.280><c> ago</c><00:04:55.840><c> but</c><00:04:56.000><c> have</c>

00:04:56.189 --> 00:04:56.199 align:start position:0%
the answer to 40 years ago but have
 

00:04:56.199 --> 00:04:58.469 align:start position:0%
the answer to 40 years ago but have
since<00:04:56.520><c> forgotten</c><00:04:57.520><c> and</c><00:04:57.639><c> so</c><00:04:57.800><c> getting</c><00:04:58.000><c> a</c><00:04:58.160><c> model</c>

00:04:58.469 --> 00:04:58.479 align:start position:0%
since forgotten and so getting a model
 

00:04:58.479 --> 00:05:00.390 align:start position:0%
since forgotten and so getting a model
to<00:04:58.639><c> answer</c><00:04:58.960><c> this</c><00:04:59.160><c> is</c><00:04:59.320><c> fantastic</c><00:04:59.680><c> fantastic</c>

00:05:00.390 --> 00:05:00.400 align:start position:0%
to answer this is fantastic fantastic
 

00:05:00.400 --> 00:05:03.110 align:start position:0%
to answer this is fantastic fantastic
though<00:05:00.639><c> they</c><00:05:00.880><c> all</c><00:05:01.120><c> do</c><00:05:01.440><c> pretty</c><00:05:01.720><c> good</c><00:05:02.039><c> at</c><00:05:02.199><c> it</c>

00:05:03.110 --> 00:05:03.120 align:start position:0%
though they all do pretty good at it
 

00:05:03.120 --> 00:05:05.629 align:start position:0%
though they all do pretty good at it
explain<00:05:04.080><c> photosynthesis</c><00:05:05.080><c> the</c><00:05:05.199><c> fact</c><00:05:05.400><c> that</c><00:05:05.520><c> I</c>

00:05:05.629 --> 00:05:05.639 align:start position:0%
explain photosynthesis the fact that I
 

00:05:05.639 --> 00:05:08.790 align:start position:0%
explain photosynthesis the fact that I
get<00:05:05.800><c> an</c><00:05:06.080><c> answer</c><00:05:06.560><c> in</c><00:05:06.840><c> less</c><00:05:07.080><c> than</c><00:05:07.320><c> 3</c><00:05:07.759><c> seconds</c><00:05:08.520><c> is</c>

00:05:08.790 --> 00:05:08.800 align:start position:0%
get an answer in less than 3 seconds is
 

00:05:08.800 --> 00:05:12.189 align:start position:0%
get an answer in less than 3 seconds is
the<00:05:09.120><c> magic</c><00:05:09.960><c> here</c><00:05:10.960><c> let's</c><00:05:11.199><c> go</c><00:05:11.400><c> to</c><00:05:11.680><c> another</c>

00:05:12.189 --> 00:05:12.199 align:start position:0%
the magic here let's go to another
 

00:05:12.199 --> 00:05:14.390 align:start position:0%
the magic here let's go to another
favorite<00:05:12.639><c> question</c><00:05:13.000><c> I</c><00:05:13.120><c> see</c><00:05:13.360><c> a</c><00:05:13.479><c> lot</c><00:05:13.720><c> but</c><00:05:14.280><c> you</c>

00:05:14.390 --> 00:05:14.400 align:start position:0%
favorite question I see a lot but you
 

00:05:14.400 --> 00:05:16.870 align:start position:0%
favorite question I see a lot but you
know<00:05:14.680><c> most</c><00:05:15.000><c> times</c><00:05:15.479><c> I</c><00:05:15.680><c> see</c><00:05:15.960><c> folks</c><00:05:16.400><c> ask</c><00:05:16.680><c> the</c>

00:05:16.870 --> 00:05:16.880 align:start position:0%
know most times I see folks ask the
 

00:05:16.880 --> 00:05:18.749 align:start position:0%
know most times I see folks ask the
question<00:05:17.280><c> and</c><00:05:17.440><c> they</c><00:05:17.600><c> get</c><00:05:17.880><c> what</c><00:05:18.039><c> they</c><00:05:18.280><c> think</c><00:05:18.600><c> is</c>

00:05:18.749 --> 00:05:18.759 align:start position:0%
question and they get what they think is
 

00:05:18.759 --> 00:05:20.830 align:start position:0%
question and they get what they think is
the<00:05:18.960><c> wrong</c><00:05:19.280><c> answer</c><00:05:19.759><c> but</c><00:05:19.960><c> they</c><00:05:20.080><c> don't</c><00:05:20.319><c> seem</c><00:05:20.560><c> to</c>

00:05:20.830 --> 00:05:20.840 align:start position:0%
the wrong answer but they don't seem to
 

00:05:20.840 --> 00:05:24.110 align:start position:0%
the wrong answer but they don't seem to
realize<00:05:21.479><c> the</c><00:05:21.840><c> problem</c><00:05:22.840><c> is</c><00:05:23.120><c> mostly</c><00:05:23.680><c> how</c><00:05:23.919><c> they</c>

00:05:24.110 --> 00:05:24.120 align:start position:0%
realize the problem is mostly how they
 

00:05:24.120 --> 00:05:26.790 align:start position:0%
realize the problem is mostly how they
ask<00:05:24.440><c> the</c><00:05:24.720><c> question</c><00:05:25.720><c> often</c><00:05:26.080><c> when</c><00:05:26.240><c> we</c><00:05:26.440><c> ask</c><00:05:26.639><c> a</c>

00:05:26.790 --> 00:05:26.800 align:start position:0%
ask the question often when we ask a
 

00:05:26.800 --> 00:05:28.390 align:start position:0%
ask the question often when we ask a
model<00:05:27.080><c> to</c><00:05:27.240><c> do</c><00:05:27.440><c> something</c><00:05:27.759><c> we</c><00:05:27.919><c> make</c>

00:05:28.390 --> 00:05:28.400 align:start position:0%
model to do something we make
 

00:05:28.400 --> 00:05:31.309 align:start position:0%
model to do something we make
assumptions<00:05:29.400><c> that</c><00:05:29.720><c> the</c><00:05:29.880><c> model</c><00:05:30.280><c> knows</c><00:05:31.199><c> what</c>

00:05:31.309 --> 00:05:31.319 align:start position:0%
assumptions that the model knows what
 

00:05:31.319 --> 00:05:33.870 align:start position:0%
assumptions that the model knows what
we're<00:05:31.840><c> thinking</c><00:05:32.840><c> asking</c><00:05:33.160><c> a</c><00:05:33.280><c> model</c><00:05:33.560><c> to</c><00:05:33.720><c> take</c>

00:05:33.870 --> 00:05:33.880 align:start position:0%
we're thinking asking a model to take
 

00:05:33.880 --> 00:05:36.469 align:start position:0%
we're thinking asking a model to take
two<00:05:34.039><c> numbers</c><00:05:34.400><c> and</c><00:05:34.600><c> decide</c><00:05:34.919><c> which</c><00:05:35.080><c> is</c><00:05:35.479><c> bigger</c>

00:05:36.469 --> 00:05:36.479 align:start position:0%
two numbers and decide which is bigger
 

00:05:36.479 --> 00:05:38.550 align:start position:0%
two numbers and decide which is bigger
is<00:05:36.720><c> actually</c><00:05:36.960><c> a</c><00:05:37.080><c> bit</c><00:05:37.360><c> ambiguous</c><00:05:38.280><c> because</c>

00:05:38.550 --> 00:05:38.560 align:start position:0%
is actually a bit ambiguous because
 

00:05:38.560 --> 00:05:41.150 align:start position:0%
is actually a bit ambiguous because
depending<00:05:38.919><c> on</c><00:05:39.080><c> the</c><00:05:39.280><c> type</c><00:05:39.520><c> of</c><00:05:39.880><c> number</c><00:05:40.880><c> either</c>

00:05:41.150 --> 00:05:41.160 align:start position:0%
depending on the type of number either
 

00:05:41.160 --> 00:05:44.590 align:start position:0%
depending on the type of number either
one<00:05:41.639><c> could</c><00:05:41.880><c> be</c><00:05:42.800><c> bigger</c><00:05:43.800><c> if</c><00:05:43.919><c> it's</c><00:05:44.080><c> a</c><00:05:44.240><c> version</c>

00:05:44.590 --> 00:05:44.600 align:start position:0%
one could be bigger if it's a version
 

00:05:44.600 --> 00:05:47.830 align:start position:0%
one could be bigger if it's a version
number<00:05:44.960><c> then</c><00:05:45.319><c> 8.21</c><00:05:46.319><c> is</c><00:05:46.520><c> the</c><00:05:46.720><c> right</c><00:05:46.960><c> answer</c><00:05:47.680><c> but</c>

00:05:47.830 --> 00:05:47.840 align:start position:0%
number then 8.21 is the right answer but
 

00:05:47.840 --> 00:05:50.629 align:start position:0%
number then 8.21 is the right answer but
if<00:05:47.960><c> it's</c><00:05:48.080><c> a</c><00:05:48.240><c> floating</c><00:05:48.720><c> Point</c><00:05:48.960><c> number</c><00:05:49.520><c> then</c><00:05:49.639><c> 8.8</c>

00:05:50.629 --> 00:05:50.639 align:start position:0%
if it's a floating Point number then 8.8
 

00:05:50.639 --> 00:05:52.710 align:start position:0%
if it's a floating Point number then 8.8
is<00:05:50.800><c> the</c><00:05:50.960><c> right</c><00:05:51.160><c> answer</c><00:05:51.960><c> so</c><00:05:52.120><c> I</c><00:05:52.240><c> like</c><00:05:52.360><c> to</c><00:05:52.479><c> be</c><00:05:52.600><c> a</c>

00:05:52.710 --> 00:05:52.720 align:start position:0%
is the right answer so I like to be a
 

00:05:52.720 --> 00:05:55.230 align:start position:0%
is the right answer so I like to be a
little<00:05:52.919><c> bit</c><00:05:53.120><c> more</c><00:05:53.400><c> specific</c><00:05:53.960><c> at</c><00:05:54.120><c> my</c><00:05:54.319><c> questions</c>

00:05:55.230 --> 00:05:55.240 align:start position:0%
little bit more specific at my questions
 

00:05:55.240 --> 00:05:57.749 align:start position:0%
little bit more specific at my questions
and<00:05:55.360><c> it</c><00:05:55.520><c> gets</c><00:05:55.680><c> it</c><00:05:55.919><c> right</c><00:05:56.840><c> but</c><00:05:57.000><c> if</c><00:05:57.080><c> you</c><00:05:57.319><c> ask</c><00:05:57.560><c> the</c>

00:05:57.749 --> 00:05:57.759 align:start position:0%
and it gets it right but if you ask the
 

00:05:57.759 --> 00:06:01.110 align:start position:0%
and it gets it right but if you ask the
right<00:05:58.000><c> question</c><00:05:58.600><c> most</c><00:05:59.120><c> models</c><00:06:00.120><c> tend</c><00:06:00.680><c> to</c><00:06:01.039><c> get</c>

00:06:01.110 --> 00:06:01.120 align:start position:0%
right question most models tend to get
 

00:06:01.120 --> 00:06:03.350 align:start position:0%
right question most models tend to get
it<00:06:01.360><c> right</c><00:06:01.680><c> not</c><00:06:01.880><c> always</c><00:06:02.560><c> but</c><00:06:02.720><c> they</c><00:06:02.840><c> tend</c><00:06:03.080><c> to</c><00:06:03.240><c> get</c>

00:06:03.350 --> 00:06:03.360 align:start position:0%
it right not always but they tend to get
 

00:06:03.360 --> 00:06:06.469 align:start position:0%
it right not always but they tend to get
it<00:06:03.560><c> right</c><00:06:04.520><c> let's</c><00:06:04.759><c> try</c><00:06:05.000><c> another</c><00:06:05.759><c> the</c><00:06:05.960><c> sum</c><00:06:06.319><c> of</c>

00:06:06.469 --> 00:06:06.479 align:start position:0%
it right let's try another the sum of
 

00:06:06.479 --> 00:06:10.430 align:start position:0%
it right let's try another the sum of
two<00:06:06.680><c> numbers</c><00:06:07.080><c> is</c><00:06:07.280><c> 10</c><00:06:07.840><c> and</c><00:06:08.000><c> the</c><00:06:08.280><c> product</c><00:06:08.840><c> is</c><00:06:09.440><c> 25</c>

00:06:10.430 --> 00:06:10.440 align:start position:0%
two numbers is 10 and the product is 25
 

00:06:10.440 --> 00:06:12.029 align:start position:0%
two numbers is 10 and the product is 25
what<00:06:10.560><c> is</c><00:06:10.720><c> the</c><00:06:10.880><c> difference</c><00:06:11.240><c> between</c><00:06:11.560><c> the</c><00:06:11.639><c> two</c>

00:06:12.029 --> 00:06:12.039 align:start position:0%
what is the difference between the two
 

00:06:12.039 --> 00:06:14.670 align:start position:0%
what is the difference between the two
numbers<00:06:13.039><c> explain</c><00:06:13.599><c> each</c><00:06:13.880><c> step</c><00:06:14.360><c> in</c><00:06:14.479><c> your</c>

00:06:14.670 --> 00:06:14.680 align:start position:0%
numbers explain each step in your
 

00:06:14.680 --> 00:06:16.510 align:start position:0%
numbers explain each step in your
solution<00:06:15.400><c> and</c><00:06:15.599><c> we</c><00:06:15.680><c> see</c><00:06:15.880><c> it</c><00:06:16.000><c> does</c><00:06:16.160><c> a</c><00:06:16.319><c> pretty</c>

00:06:16.510 --> 00:06:16.520 align:start position:0%
solution and we see it does a pretty
 

00:06:16.520 --> 00:06:19.670 align:start position:0%
solution and we see it does a pretty
great<00:06:16.800><c> job</c><00:06:16.960><c> at</c><00:06:17.240><c> this</c><00:06:18.240><c> finally</c><00:06:18.840><c> let's</c><00:06:19.199><c> ask</c><00:06:19.479><c> the</c>

00:06:19.670 --> 00:06:19.680 align:start position:0%
great job at this finally let's ask the
 

00:06:19.680 --> 00:06:22.150 align:start position:0%
great job at this finally let's ask the
RS<00:06:20.199><c> in</c><00:06:20.440><c> Strawberry</c><00:06:21.080><c> question</c><00:06:21.560><c> even</c><00:06:21.800><c> though</c><00:06:22.000><c> it</c>

00:06:22.150 --> 00:06:22.160 align:start position:0%
RS in Strawberry question even though it
 

00:06:22.160 --> 00:06:24.390 align:start position:0%
RS in Strawberry question even though it
doesn't<00:06:22.560><c> really</c><00:06:22.919><c> say</c><00:06:23.240><c> much</c><00:06:23.960><c> about</c><00:06:24.199><c> the</c>

00:06:24.390 --> 00:06:24.400 align:start position:0%
doesn't really say much about the
 

00:06:24.400 --> 00:06:28.150 align:start position:0%
doesn't really say much about the
abilities<00:06:24.919><c> of</c><00:06:25.080><c> the</c><00:06:25.280><c> model</c><00:06:26.280><c> it</c><00:06:26.479><c> gets</c><00:06:26.680><c> it</c><00:06:27.160><c> wrong</c>

00:06:28.150 --> 00:06:28.160 align:start position:0%
abilities of the model it gets it wrong
 

00:06:28.160 --> 00:06:30.629 align:start position:0%
abilities of the model it gets it wrong
in<00:06:28.280><c> a</c><00:06:28.560><c> way</c><00:06:28.840><c> that</c><00:06:29.120><c> I</c><00:06:29.319><c> have</c><00:06:29.599><c> haven't</c><00:06:29.840><c> really</c><00:06:30.240><c> seen</c>

00:06:30.629 --> 00:06:30.639 align:start position:0%
in a way that I have haven't really seen
 

00:06:30.639 --> 00:06:33.550 align:start position:0%
in a way that I have haven't really seen
before<00:06:31.039><c> it's</c><00:06:31.599><c> so</c>

00:06:33.550 --> 00:06:33.560 align:start position:0%
before it's so
 

00:06:33.560 --> 00:06:37.629 align:start position:0%
before it's so
wrong<00:06:34.560><c> but</c><00:06:35.319><c> who</c><00:06:35.720><c> cares</c><00:06:36.720><c> I'll</c><00:06:37.000><c> exit</c><00:06:37.319><c> out</c><00:06:37.440><c> of</c>

00:06:37.629 --> 00:06:37.639 align:start position:0%
wrong but who cares I'll exit out of
 

00:06:37.639 --> 00:06:39.589 align:start position:0%
wrong but who cares I'll exit out of
here<00:06:37.919><c> and</c><00:06:38.080><c> try</c><00:06:38.360><c> running</c><00:06:38.720><c> the</c><00:06:38.919><c> 3</c><00:06:39.199><c> billion</c>

00:06:39.589 --> 00:06:39.599 align:start position:0%
here and try running the 3 billion
 

00:06:39.599 --> 00:06:41.950 align:start position:0%
here and try running the 3 billion
parameter<00:06:40.039><c> model</c><00:06:40.880><c> we</c><00:06:41.000><c> won't</c><00:06:41.240><c> do</c><00:06:41.560><c> all</c><00:06:41.759><c> the</c>

00:06:41.950 --> 00:06:41.960 align:start position:0%
parameter model we won't do all the
 

00:06:41.960 --> 00:06:44.309 align:start position:0%
parameter model we won't do all the
questions<00:06:42.360><c> but</c><00:06:42.479><c> a</c><00:06:42.599><c> few</c><00:06:42.840><c> should</c><00:06:43.080><c> be</c><00:06:43.240><c> fun</c><00:06:44.120><c> let's</c>

00:06:44.309 --> 00:06:44.319 align:start position:0%
questions but a few should be fun let's
 

00:06:44.319 --> 00:06:46.510 align:start position:0%
questions but a few should be fun let's
start<00:06:44.560><c> with</c><00:06:44.720><c> the</c><00:06:44.880><c> r</c><00:06:45.280><c> question</c><00:06:45.720><c> and</c><00:06:45.840><c> it's</c><00:06:46.080><c> wrong</c>

00:06:46.510 --> 00:06:46.520 align:start position:0%
start with the r question and it's wrong
 

00:06:46.520 --> 00:06:49.589 align:start position:0%
start with the r question and it's wrong
in<00:06:46.840><c> the</c><00:06:47.160><c> usual</c><00:06:47.800><c> way</c><00:06:48.800><c> the</c><00:06:48.960><c> difference</c><00:06:49.319><c> between</c>

00:06:49.589 --> 00:06:49.599 align:start position:0%
in the usual way the difference between
 

00:06:49.599 --> 00:06:51.670 align:start position:0%
in the usual way the difference between
the<00:06:49.720><c> two</c><00:06:49.919><c> numbers</c><00:06:50.319><c> is</c><00:06:50.560><c> nice</c><00:06:50.960><c> and</c><00:06:51.080><c> it</c><00:06:51.240><c> gets</c><00:06:51.440><c> it</c>

00:06:51.670 --> 00:06:51.680 align:start position:0%
the two numbers is nice and it gets it
 

00:06:51.680 --> 00:06:53.550 align:start position:0%
the two numbers is nice and it gets it
perfectly<00:06:52.199><c> right</c><00:06:52.720><c> the</c><00:06:52.840><c> three</c><00:06:53.120><c> killers</c>

00:06:53.550 --> 00:06:53.560 align:start position:0%
perfectly right the three killers
 

00:06:53.560 --> 00:06:55.110 align:start position:0%
perfectly right the three killers
question<00:06:53.840><c> is</c><00:06:54.039><c> interesting</c><00:06:54.560><c> getting</c><00:06:54.840><c> the</c>

00:06:55.110 --> 00:06:55.120 align:start position:0%
question is interesting getting the
 

00:06:55.120 --> 00:06:56.749 align:start position:0%
question is interesting getting the
right

00:06:56.749 --> 00:06:56.759 align:start position:0%
right
 

00:06:56.759 --> 00:07:00.390 align:start position:0%
right
answer<00:06:57.759><c> but</c><00:06:58.039><c> with</c><00:06:58.759><c> terrible</c><00:06:59.639><c> project</c><00:06:59.960><c> to</c><00:07:00.160><c> get</c>

00:07:00.390 --> 00:07:00.400 align:start position:0%
answer but with terrible project to get
 

00:07:00.400 --> 00:07:02.469 align:start position:0%
answer but with terrible project to get
there<00:07:01.360><c> so</c><00:07:01.560><c> let's</c><00:07:01.720><c> move</c><00:07:01.879><c> on</c><00:07:02.000><c> to</c><00:07:02.160><c> a</c><00:07:02.240><c> different</c>

00:07:02.469 --> 00:07:02.479 align:start position:0%
there so let's move on to a different
 

00:07:02.479 --> 00:07:04.469 align:start position:0%
there so let's move on to a different
use<00:07:02.800><c> case</c><00:07:03.360><c> one</c><00:07:03.479><c> of</c><00:07:03.599><c> the</c><00:07:03.720><c> things</c><00:07:03.960><c> that</c><00:07:04.160><c> blog</c>

00:07:04.469 --> 00:07:04.479 align:start position:0%
use case one of the things that blog
 

00:07:04.479 --> 00:07:06.749 align:start position:0%
use case one of the things that blog
post<00:07:04.720><c> talks</c><00:07:05.000><c> about</c><00:07:05.240><c> was</c><00:07:05.560><c> summarization</c><00:07:06.560><c> so</c>

00:07:06.749 --> 00:07:06.759 align:start position:0%
post talks about was summarization so
 

00:07:06.759 --> 00:07:09.029 align:start position:0%
post talks about was summarization so
let's<00:07:06.960><c> try</c><00:07:07.240><c> that</c><00:07:07.400><c> out</c><00:07:07.879><c> now</c><00:07:08.120><c> the</c><00:07:08.280><c> 3</c><00:07:08.599><c> billion</c><00:07:08.879><c> and</c>

00:07:09.029 --> 00:07:09.039 align:start position:0%
let's try that out now the 3 billion and
 

00:07:09.039 --> 00:07:11.510 align:start position:0%
let's try that out now the 3 billion and
1<00:07:09.319><c> billion</c><00:07:09.599><c> models</c><00:07:09.960><c> support</c><00:07:10.240><c> a</c><00:07:10.400><c> contact</c><00:07:10.879><c> size</c>

00:07:11.510 --> 00:07:11.520 align:start position:0%
1 billion models support a contact size
 

00:07:11.520 --> 00:07:15.550 align:start position:0%
1 billion models support a contact size
of<00:07:11.720><c> 128k</c><00:07:12.720><c> but</c><00:07:13.000><c> AMA</c><00:07:14.000><c> configures</c><00:07:14.520><c> models</c><00:07:15.000><c> to</c><00:07:15.160><c> be</c>

00:07:15.550 --> 00:07:15.560 align:start position:0%
of 128k but AMA configures models to be
 

00:07:15.560 --> 00:07:18.270 align:start position:0%
of 128k but AMA configures models to be
2K<00:07:16.360><c> by</c><00:07:16.560><c> default</c><00:07:17.440><c> so</c><00:07:17.599><c> let's</c><00:07:17.800><c> create</c><00:07:18.000><c> a</c><00:07:18.120><c> new</c>

00:07:18.270 --> 00:07:18.280 align:start position:0%
2K by default so let's create a new
 

00:07:18.280 --> 00:07:20.430 align:start position:0%
2K by default so let's create a new
model<00:07:18.759><c> that</c><00:07:18.879><c> uses</c><00:07:19.120><c> a</c><00:07:19.280><c> larger</c><00:07:19.639><c> contact</c><00:07:20.039><c> size</c>

00:07:20.430 --> 00:07:20.440 align:start position:0%
model that uses a larger contact size
 

00:07:20.440 --> 00:07:23.670 align:start position:0%
model that uses a larger contact size
I'll<00:07:20.599><c> set</c><00:07:20.840><c> it</c><00:07:20.960><c> to</c><00:07:21.639><c> 16k</c><00:07:22.639><c> save</c><00:07:22.919><c> the</c><00:07:23.039><c> model</c><00:07:23.280><c> file</c>

00:07:23.670 --> 00:07:23.680 align:start position:0%
I'll set it to 16k save the model file
 

00:07:23.680 --> 00:07:26.670 align:start position:0%
I'll set it to 16k save the model file
and<00:07:23.800><c> then</c><00:07:23.960><c> run</c><00:07:24.599><c> the</c><00:07:24.759><c> olama</c><00:07:25.319><c> create</c><00:07:25.680><c> command</c>

00:07:26.670 --> 00:07:26.680 align:start position:0%
and then run the olama create command
 

00:07:26.680 --> 00:07:29.309 align:start position:0%
and then run the olama create command
now<00:07:26.879><c> I</c><00:07:26.960><c> can</c><00:07:27.120><c> run</c><00:07:27.360><c> that</c><00:07:27.520><c> new</c><00:07:27.720><c> model</c><00:07:28.520><c> summarize</c>

00:07:29.309 --> 00:07:29.319 align:start position:0%
now I can run that new model summarize
 

00:07:29.319 --> 00:07:31.029 align:start position:0%
now I can run that new model summarize
this<00:07:29.680><c> video</c><00:07:29.919><c> script</c><00:07:30.240><c> down</c><00:07:30.400><c> to</c><00:07:30.560><c> a</c><00:07:30.720><c> single</c>

00:07:31.029 --> 00:07:31.039 align:start position:0%
this video script down to a single
 

00:07:31.039 --> 00:07:33.150 align:start position:0%
this video script down to a single
paragraph<00:07:31.560><c> and</c><00:07:31.680><c> then</c><00:07:31.800><c> I'll</c><00:07:32.080><c> add</c><00:07:32.319><c> the</c><00:07:32.479><c> script</c>

00:07:33.150 --> 00:07:33.160 align:start position:0%
paragraph and then I'll add the script
 

00:07:33.160 --> 00:07:35.189 align:start position:0%
paragraph and then I'll add the script
for<00:07:33.400><c> my</c><00:07:33.560><c> recent</c><00:07:33.879><c> video</c><00:07:34.240><c> on</c><00:07:34.560><c> environment</c>

00:07:35.189 --> 00:07:35.199 align:start position:0%
for my recent video on environment
 

00:07:35.199 --> 00:07:39.830 align:start position:0%
for my recent video on environment
variables<00:07:36.199><c> and</c><00:07:36.360><c> the</c><00:07:36.560><c> result</c><00:07:37.360><c> is</c><00:07:37.879><c> pretty</c><00:07:38.840><c> great</c>

00:07:39.830 --> 00:07:39.840 align:start position:0%
variables and the result is pretty great
 

00:07:39.840 --> 00:07:41.510 align:start position:0%
variables and the result is pretty great
another<00:07:40.360><c> thing</c><00:07:40.599><c> it's</c><00:07:40.840><c> apparently</c><00:07:41.280><c> really</c>

00:07:41.510 --> 00:07:41.520 align:start position:0%
another thing it's apparently really
 

00:07:41.520 --> 00:07:44.350 align:start position:0%
another thing it's apparently really
good<00:07:41.680><c> at</c><00:07:42.039><c> is</c><00:07:42.319><c> tool</c><00:07:42.759><c> use</c><00:07:43.680><c> now</c><00:07:43.919><c> this</c>

00:07:44.350 --> 00:07:44.360 align:start position:0%
good at is tool use now this
 

00:07:44.360 --> 00:07:47.070 align:start position:0%
good at is tool use now this
specifically<00:07:44.879><c> refers</c><00:07:45.280><c> to</c><00:07:45.479><c> the</c><00:07:45.639><c> newer</c><00:07:46.599><c> less</c>

00:07:47.070 --> 00:07:47.080 align:start position:0%
specifically refers to the newer less
 

00:07:47.080 --> 00:07:49.629 align:start position:0%
specifically refers to the newer less
reliable<00:07:47.840><c> approach</c><00:07:48.599><c> versus</c><00:07:48.919><c> the</c><00:07:49.080><c> tool</c><00:07:49.360><c> use</c>

00:07:49.629 --> 00:07:49.639 align:start position:0%
reliable approach versus the tool use
 

00:07:49.639 --> 00:07:52.029 align:start position:0%
reliable approach versus the tool use
approach<00:07:50.000><c> added</c><00:07:50.319><c> about</c><00:07:50.520><c> a</c><00:07:50.639><c> year</c><00:07:50.879><c> ago</c><00:07:51.720><c> I</c><00:07:51.840><c> look</c>

00:07:52.029 --> 00:07:52.039 align:start position:0%
approach added about a year ago I look
 

00:07:52.039 --> 00:07:54.070 align:start position:0%
approach added about a year ago I look
forward<00:07:52.360><c> to</c><00:07:52.520><c> the</c><00:07:52.720><c> newer</c><00:07:53.159><c> approach</c><00:07:53.599><c> being</c><00:07:53.879><c> as</c>

00:07:54.070 --> 00:07:54.080 align:start position:0%
forward to the newer approach being as
 

00:07:54.080 --> 00:07:56.670 align:start position:0%
forward to the newer approach being as
good<00:07:54.240><c> as</c><00:07:54.400><c> that</c><00:07:54.639><c> older</c><00:07:55.039><c> one</c><00:07:55.840><c> but</c><00:07:56.000><c> anyway</c><00:07:56.360><c> we</c><00:07:56.479><c> can</c>

00:07:56.670 --> 00:07:56.680 align:start position:0%
good as that older one but anyway we can
 

00:07:56.680 --> 00:07:59.790 align:start position:0%
good as that older one but anyway we can
go<00:07:56.840><c> to</c><00:07:57.039><c> the</c><00:07:57.199><c> blog</c><00:07:57.520><c> post</c><00:07:57.759><c> on</c><00:07:57.960><c> tool</c><00:07:58.319><c> use</c><00:07:58.720><c> on</c><00:07:58.919><c> the</c>

00:07:59.790 --> 00:07:59.800 align:start position:0%
go to the blog post on tool use on the
 

00:07:59.800 --> 00:08:01.990 align:start position:0%
go to the blog post on tool use on the
page<00:08:00.560><c> and</c><00:08:00.639><c> it</c><00:08:00.759><c> points</c><00:08:01.039><c> to</c><00:08:01.199><c> some</c><00:08:01.400><c> sample</c><00:08:01.800><c> code</c>

00:08:01.990 --> 00:08:02.000 align:start position:0%
page and it points to some sample code
 

00:08:02.000 --> 00:08:04.629 align:start position:0%
page and it points to some sample code
for<00:08:02.280><c> Python</c><00:08:02.680><c> and</c><00:08:03.080><c> JavaScript</c><00:08:04.080><c> so</c><00:08:04.240><c> I'll</c><00:08:04.440><c> grab</c>

00:08:04.629 --> 00:08:04.639 align:start position:0%
for Python and JavaScript so I'll grab
 

00:08:04.639 --> 00:08:06.830 align:start position:0%
for Python and JavaScript so I'll grab
the<00:08:04.840><c> JavaScript</c><00:08:05.360><c> code</c><00:08:05.720><c> and</c><00:08:05.919><c> paste</c><00:08:06.120><c> it</c><00:08:06.280><c> into</c><00:08:06.599><c> my</c>

00:08:06.830 --> 00:08:06.840 align:start position:0%
the JavaScript code and paste it into my
 

00:08:06.840 --> 00:08:09.309 align:start position:0%
the JavaScript code and paste it into my
VSS<00:08:07.199><c> code</c><00:08:07.560><c> editor</c><00:08:08.560><c> now</c><00:08:08.840><c> one</c><00:08:08.960><c> of</c><00:08:09.120><c> the</c>

00:08:09.309 --> 00:08:09.319 align:start position:0%
VSS code editor now one of the
 

00:08:09.319 --> 00:08:11.029 align:start position:0%
VSS code editor now one of the
interesting<00:08:09.759><c> things</c><00:08:10.039><c> about</c><00:08:10.319><c> using</c><00:08:10.680><c> large</c>

00:08:11.029 --> 00:08:11.039 align:start position:0%
interesting things about using large
 

00:08:11.039 --> 00:08:13.270 align:start position:0%
interesting things about using large
language<00:08:11.400><c> models</c><00:08:12.199><c> is</c><00:08:12.319><c> that</c><00:08:12.520><c> the</c><00:08:12.720><c> answers</c><00:08:13.120><c> can</c>

00:08:13.270 --> 00:08:13.280 align:start position:0%
language models is that the answers can
 

00:08:13.280 --> 00:08:16.749 align:start position:0%
language models is that the answers can
be<00:08:13.759><c> different</c><00:08:14.720><c> each</c><00:08:14.960><c> time</c><00:08:15.159><c> you</c><00:08:15.319><c> ask</c><00:08:16.319><c> so</c><00:08:16.560><c> just</c>

00:08:16.749 --> 00:08:16.759 align:start position:0%
be different each time you ask so just
 

00:08:16.759 --> 00:08:18.869 align:start position:0%
be different each time you ask so just
because<00:08:17.000><c> you</c><00:08:17.159><c> get</c><00:08:17.400><c> an</c><00:08:17.639><c> answer</c><00:08:18.000><c> that's</c><00:08:18.440><c> right</c>

00:08:18.869 --> 00:08:18.879 align:start position:0%
because you get an answer that's right
 

00:08:18.879 --> 00:08:22.189 align:start position:0%
because you get an answer that's right
or<00:08:19.240><c> wrong</c><00:08:19.800><c> one</c><00:08:20.159><c> time</c><00:08:21.159><c> doesn't</c><00:08:21.479><c> mean</c><00:08:21.800><c> you'll</c>

00:08:22.189 --> 00:08:22.199 align:start position:0%
or wrong one time doesn't mean you'll
 

00:08:22.199 --> 00:08:24.950 align:start position:0%
or wrong one time doesn't mean you'll
always<00:08:22.599><c> get</c><00:08:22.879><c> a</c><00:08:23.039><c> similar</c><00:08:23.400><c> answer</c><00:08:24.400><c> so</c><00:08:24.680><c> I'll</c>

00:08:24.950 --> 00:08:24.960 align:start position:0%
always get a similar answer so I'll
 

00:08:24.960 --> 00:08:27.309 align:start position:0%
always get a similar answer so I'll
update<00:08:25.319><c> this</c><00:08:25.479><c> code</c><00:08:25.720><c> to</c><00:08:25.960><c> repeat</c><00:08:26.280><c> the</c><00:08:26.400><c> call</c><00:08:26.800><c> 10</c>

00:08:27.309 --> 00:08:27.319 align:start position:0%
update this code to repeat the call 10
 

00:08:27.319 --> 00:08:30.149 align:start position:0%
update this code to repeat the call 10
times<00:08:28.319><c> what</c><00:08:28.520><c> this</c><00:08:28.720><c> example</c><00:08:29.159><c> does</c>

00:08:30.149 --> 00:08:30.159 align:start position:0%
times what this example does
 

00:08:30.159 --> 00:08:32.589 align:start position:0%
times what this example does
is<00:08:30.360><c> make</c><00:08:30.520><c> a</c><00:08:30.720><c> call</c><00:08:30.919><c> to</c><00:08:31.080><c> the</c><00:08:31.199><c> model</c><00:08:31.919><c> and</c><00:08:32.120><c> provide</c>

00:08:32.589 --> 00:08:32.599 align:start position:0%
is make a call to the model and provide
 

00:08:32.599 --> 00:08:35.469 align:start position:0%
is make a call to the model and provide
info<00:08:32.959><c> about</c><00:08:33.159><c> a</c><00:08:33.320><c> tool</c><00:08:34.159><c> that</c><00:08:34.479><c> returns</c><00:08:34.800><c> flight</c>

00:08:35.469 --> 00:08:35.479 align:start position:0%
info about a tool that returns flight
 

00:08:35.479 --> 00:08:37.630 align:start position:0%
info about a tool that returns flight
information<00:08:36.479><c> now</c><00:08:36.680><c> the</c><00:08:36.839><c> actual</c><00:08:37.120><c> function</c><00:08:37.519><c> is</c>

00:08:37.630 --> 00:08:37.640 align:start position:0%
information now the actual function is
 

00:08:37.640 --> 00:08:39.750 align:start position:0%
information now the actual function is
using<00:08:37.959><c> mock</c><00:08:38.320><c> data</c><00:08:38.599><c> so</c><00:08:38.719><c> it</c><00:08:38.839><c> doesn't</c><00:08:39.360><c> actually</c>

00:08:39.750 --> 00:08:39.760 align:start position:0%
using mock data so it doesn't actually
 

00:08:39.760 --> 00:08:41.990 align:start position:0%
using mock data so it doesn't actually
query<00:08:40.200><c> any</c><00:08:40.399><c> real</c><00:08:40.760><c> site</c><00:08:41.000><c> but</c><00:08:41.120><c> it</c><00:08:41.240><c> has</c><00:08:41.399><c> the</c><00:08:41.640><c> same</c>

00:08:41.990 --> 00:08:42.000 align:start position:0%
query any real site but it has the same
 

00:08:42.000 --> 00:08:44.269 align:start position:0%
query any real site but it has the same
ultimate<00:08:42.599><c> results</c><00:08:43.599><c> we</c><00:08:43.719><c> have</c><00:08:43.839><c> a</c><00:08:43.959><c> question</c>

00:08:44.269 --> 00:08:44.279 align:start position:0%
ultimate results we have a question
 

00:08:44.279 --> 00:08:46.430 align:start position:0%
ultimate results we have a question
hardcoded<00:08:45.200><c> of</c><00:08:45.519><c> what</c><00:08:45.640><c> is</c><00:08:45.760><c> the</c><00:08:45.880><c> flight</c><00:08:46.160><c> time</c>

00:08:46.430 --> 00:08:46.440 align:start position:0%
hardcoded of what is the flight time
 

00:08:46.440 --> 00:08:49.550 align:start position:0%
hardcoded of what is the flight time
from<00:08:46.680><c> New</c><00:08:46.880><c> York</c><00:08:47.120><c> to</c><00:08:47.800><c> LA</c><00:08:48.800><c> the</c><00:08:48.920><c> model</c><00:08:49.279><c> should</c>

00:08:49.550 --> 00:08:49.560 align:start position:0%
from New York to LA the model should
 

00:08:49.560 --> 00:08:51.190 align:start position:0%
from New York to LA the model should
tell<00:08:49.839><c> the</c><00:08:49.959><c> app</c><00:08:50.160><c> to</c><00:08:50.279><c> call</c><00:08:50.519><c> the</c><00:08:50.640><c> function</c><00:08:51.040><c> with</c>

00:08:51.190 --> 00:08:51.200 align:start position:0%
tell the app to call the function with
 

00:08:51.200 --> 00:08:54.269 align:start position:0%
tell the app to call the function with
NYC<00:08:51.760><c> and</c><00:08:51.920><c> LAX</c><00:08:52.560><c> as</c><00:08:52.760><c> parameters</c><00:08:53.600><c> and</c><00:08:53.760><c> get</c><00:08:53.920><c> back</c><00:08:54.120><c> a</c>

00:08:54.269 --> 00:08:54.279 align:start position:0%
NYC and LAX as parameters and get back a
 

00:08:54.279 --> 00:08:56.389 align:start position:0%
NYC and LAX as parameters and get back a
Json<00:08:54.640><c> blob</c><00:08:55.000><c> with</c><00:08:55.120><c> a</c><00:08:55.200><c> flight</c><00:08:55.480><c> time</c><00:08:55.680><c> of</c><00:08:55.920><c> 5</c><00:08:56.160><c> hours</c>

00:08:56.389 --> 00:08:56.399 align:start position:0%
Json blob with a flight time of 5 hours
 

00:08:56.399 --> 00:08:58.910 align:start position:0%
Json blob with a flight time of 5 hours
and<00:08:56.519><c> 30</c><00:08:56.800><c> minutes</c><00:08:57.440><c> and</c><00:08:57.560><c> then</c><00:08:57.720><c> turn</c><00:08:58.200><c> that</c><00:08:58.519><c> into</c>

00:08:58.910 --> 00:08:58.920 align:start position:0%
and 30 minutes and then turn that into
 

00:08:58.920 --> 00:09:01.829 align:start position:0%
and 30 minutes and then turn that into
some<00:08:59.160><c> good</c><00:08:59.640><c> text</c><00:09:00.640><c> let's</c><00:09:00.880><c> try</c><00:09:01.079><c> it</c><00:09:01.360><c> first</c><00:09:01.640><c> with</c>

00:09:01.829 --> 00:09:01.839 align:start position:0%
some good text let's try it first with
 

00:09:01.839 --> 00:09:05.590 align:start position:0%
some good text let's try it first with
llama<00:09:02.320><c> 3.2</c><00:09:03.320><c> 3</c><00:09:03.680><c> billion</c><00:09:04.079><c> parameters</c><00:09:05.079><c> in</c><00:09:05.279><c> 10</c>

00:09:05.590 --> 00:09:05.600 align:start position:0%
llama 3.2 3 billion parameters in 10
 

00:09:05.600 --> 00:09:09.790 align:start position:0%
llama 3.2 3 billion parameters in 10
tries<00:09:06.160><c> it</c><00:09:06.360><c> gets</c><00:09:06.519><c> it</c><00:09:06.800><c> right</c><00:09:07.440><c> every</c><00:09:08.040><c> single</c><00:09:08.800><c> time</c>

00:09:09.790 --> 00:09:09.800 align:start position:0%
tries it gets it right every single time
 

00:09:09.800 --> 00:09:12.190 align:start position:0%
tries it gets it right every single time
often<00:09:10.320><c> even</c><00:09:10.600><c> with</c><00:09:10.800><c> larger</c><00:09:11.200><c> models</c><00:09:11.959><c> this</c>

00:09:12.190 --> 00:09:12.200 align:start position:0%
often even with larger models this
 

00:09:12.200 --> 00:09:14.470 align:start position:0%
often even with larger models this
hasn't<00:09:12.480><c> been</c><00:09:12.720><c> the</c><00:09:12.880><c> case</c><00:09:13.200><c> so</c><00:09:13.800><c> that's</c><00:09:14.040><c> pretty</c>

00:09:14.470 --> 00:09:14.480 align:start position:0%
hasn't been the case so that's pretty
 

00:09:14.480 --> 00:09:16.509 align:start position:0%
hasn't been the case so that's pretty
impressive<00:09:15.480><c> let's</c><00:09:15.640><c> try</c><00:09:15.839><c> it</c><00:09:15.959><c> with</c><00:09:16.079><c> the</c><00:09:16.240><c> 1</c>

00:09:16.509 --> 00:09:16.519 align:start position:0%
impressive let's try it with the 1
 

00:09:16.519 --> 00:09:19.150 align:start position:0%
impressive let's try it with the 1
billion<00:09:16.839><c> parameter</c><00:09:17.279><c> model</c><00:09:17.959><c> and</c><00:09:18.120><c> in</c><00:09:18.279><c> 10</c><00:09:18.560><c> drives</c>

00:09:19.150 --> 00:09:19.160 align:start position:0%
billion parameter model and in 10 drives
 

00:09:19.160 --> 00:09:23.230 align:start position:0%
billion parameter model and in 10 drives
it<00:09:19.320><c> got</c><00:09:19.519><c> it</c><00:09:19.959><c> wrong</c><00:09:20.959><c> every</c><00:09:21.519><c> single</c><00:09:22.120><c> time</c><00:09:23.079><c> that</c>

00:09:23.230 --> 00:09:23.240 align:start position:0%
it got it wrong every single time that
 

00:09:23.240 --> 00:09:25.430 align:start position:0%
it got it wrong every single time that
said<00:09:23.640><c> I</c><00:09:23.800><c> tried</c><00:09:24.320><c> this</c><00:09:24.600><c> when</c><00:09:24.760><c> I</c><00:09:24.880><c> first</c><00:09:25.120><c> started</c>

00:09:25.430 --> 00:09:25.440 align:start position:0%
said I tried this when I first started
 

00:09:25.440 --> 00:09:27.990 align:start position:0%
said I tried this when I first started
playing<00:09:25.760><c> with</c><00:09:25.880><c> it</c><00:09:26.720><c> and</c><00:09:26.839><c> it</c><00:09:26.959><c> got</c><00:09:27.120><c> it</c><00:09:27.240><c> wrong</c><00:09:27.480><c> 20%</c>

00:09:27.990 --> 00:09:28.000 align:start position:0%
playing with it and it got it wrong 20%
 

00:09:28.000 --> 00:09:30.069 align:start position:0%
playing with it and it got it wrong 20%
of<00:09:28.120><c> the</c><00:09:28.279><c> time</c><00:09:28.560><c> so</c><00:09:29.040><c> hey</c>

00:09:30.069 --> 00:09:30.079 align:start position:0%
of the time so hey
 

00:09:30.079 --> 00:09:32.150 align:start position:0%
of the time so hey
but<00:09:30.240><c> even</c><00:09:30.560><c> larger</c><00:09:30.959><c> models</c><00:09:31.440><c> get</c><00:09:31.560><c> it</c><00:09:31.720><c> wrong</c><00:09:32.040><c> a</c>

00:09:32.150 --> 00:09:32.160 align:start position:0%
but even larger models get it wrong a
 

00:09:32.160 --> 00:09:35.150 align:start position:0%
but even larger models get it wrong a
lot<00:09:32.680><c> let's</c><00:09:32.880><c> try</c><00:09:33.079><c> it</c><00:09:33.200><c> with</c><00:09:33.440><c> mistol</c><00:09:34.079><c> small</c><00:09:35.000><c> and</c>

00:09:35.150 --> 00:09:35.160 align:start position:0%
lot let's try it with mistol small and
 

00:09:35.160 --> 00:09:37.829 align:start position:0%
lot let's try it with mistol small and
that's<00:09:35.279><c> a</c><00:09:35.519><c> 22</c><00:09:36.000><c> billion</c><00:09:36.360><c> parameter</c><00:09:36.800><c> model</c><00:09:37.680><c> look</c>

00:09:37.829 --> 00:09:37.839 align:start position:0%
that's a 22 billion parameter model look
 

00:09:37.839 --> 00:09:40.310 align:start position:0%
that's a 22 billion parameter model look
at<00:09:37.959><c> the</c><00:09:38.120><c> results</c><00:09:38.600><c> it</c><00:09:38.760><c> failed</c><00:09:39.279><c> four</c><00:09:39.720><c> out</c><00:09:39.839><c> of</c><00:09:40.000><c> 10</c>

00:09:40.310 --> 00:09:40.320 align:start position:0%
at the results it failed four out of 10
 

00:09:40.320 --> 00:09:42.870 align:start position:0%
at the results it failed four out of 10
times<00:09:41.160><c> I'll</c><00:09:41.320><c> try</c><00:09:41.600><c> with</c><00:09:41.760><c> one</c><00:09:41.959><c> other</c><00:09:42.160><c> model</c><00:09:42.519><c> fire</c>

00:09:42.870 --> 00:09:42.880 align:start position:0%
times I'll try with one other model fire
 

00:09:42.880 --> 00:09:45.389 align:start position:0%
times I'll try with one other model fire
functions<00:09:43.240><c> V2</c><00:09:44.240><c> in</c><00:09:44.440><c> other</c><00:09:44.720><c> tests</c><00:09:45.040><c> with</c><00:09:45.200><c> this</c>

00:09:45.389 --> 00:09:45.399 align:start position:0%
functions V2 in other tests with this
 

00:09:45.399 --> 00:09:48.190 align:start position:0%
functions V2 in other tests with this
model<00:09:46.000><c> I</c><00:09:46.120><c> would</c><00:09:46.320><c> get</c><00:09:46.760><c> good</c><00:09:47.000><c> answers</c><00:09:47.560><c> 80%</c><00:09:48.120><c> of</c>

00:09:48.190 --> 00:09:48.200 align:start position:0%
model I would get good answers 80% of
 

00:09:48.200 --> 00:09:49.750 align:start position:0%
model I would get good answers 80% of
the<00:09:48.360><c> time</c><00:09:48.640><c> but</c><00:09:48.839><c> now</c><00:09:49.000><c> it</c><00:09:49.120><c> seems</c><00:09:49.320><c> to</c><00:09:49.440><c> be</c><00:09:49.560><c> getting</c>

00:09:49.750 --> 00:09:49.760 align:start position:0%
the time but now it seems to be getting
 

00:09:49.760 --> 00:09:53.430 align:start position:0%
the time but now it seems to be getting
it<00:09:49.959><c> right</c><00:09:50.640><c> 100%</c><00:09:51.240><c> of</c><00:09:51.360><c> the</c><00:09:51.519><c> time</c><00:09:52.519><c> okay</c><00:09:52.680><c> so</c><00:09:53.120><c> now</c><00:09:53.360><c> I</c>

00:09:53.430 --> 00:09:53.440 align:start position:0%
it right 100% of the time okay so now I
 

00:09:53.440 --> 00:09:54.990 align:start position:0%
it right 100% of the time okay so now I
want<00:09:53.519><c> to</c><00:09:53.640><c> change</c><00:09:53.920><c> gears</c><00:09:54.160><c> a</c><00:09:54.320><c> bit</c><00:09:54.519><c> and</c><00:09:54.720><c> take</c><00:09:54.839><c> a</c>

00:09:54.990 --> 00:09:55.000 align:start position:0%
want to change gears a bit and take a
 

00:09:55.000 --> 00:09:58.269 align:start position:0%
want to change gears a bit and take a
look<00:09:55.200><c> at</c><00:09:55.720><c> at</c><00:09:56.079><c> one</c><00:09:56.279><c> of</c><00:09:56.560><c> my</c><00:09:56.880><c> actual</c><00:09:57.200><c> use</c><00:09:57.560><c> cases</c>

00:09:58.269 --> 00:09:58.279 align:start position:0%
look at at one of my actual use cases
 

00:09:58.279 --> 00:10:01.110 align:start position:0%
look at at one of my actual use cases
for<00:09:58.560><c> models</c><00:09:59.000><c> to</c><00:09:59.399><c> help</c><00:09:59.880><c> help</c><00:10:00.160><c> create</c><00:10:00.480><c> new</c>

00:10:01.110 --> 00:10:01.120 align:start position:0%
for models to help help create new
 

00:10:01.120 --> 00:10:04.069 align:start position:0%
for models to help help create new
content<00:10:02.120><c> I</c><00:10:02.240><c> write</c><00:10:02.480><c> my</c><00:10:02.640><c> scripts</c><00:10:03.160><c> in</c><00:10:03.480><c> obsidian</c>

00:10:04.069 --> 00:10:04.079 align:start position:0%
content I write my scripts in obsidian
 

00:10:04.079 --> 00:10:06.590 align:start position:0%
content I write my scripts in obsidian
and<00:10:04.240><c> one</c><00:10:04.360><c> of</c><00:10:04.440><c> the</c><00:10:04.600><c> plugins</c><00:10:05.079><c> I</c><00:10:05.279><c> use</c><00:10:05.760><c> is</c><00:10:05.959><c> called</c>

00:10:06.590 --> 00:10:06.600 align:start position:0%
and one of the plugins I use is called
 

00:10:06.600 --> 00:10:08.829 align:start position:0%
and one of the plugins I use is called
companion<00:10:07.600><c> it</c><00:10:07.760><c> offers</c><00:10:08.120><c> the</c><00:10:08.279><c> ability</c><00:10:08.640><c> to</c>

00:10:08.829 --> 00:10:08.839 align:start position:0%
companion it offers the ability to
 

00:10:08.839 --> 00:10:11.389 align:start position:0%
companion it offers the ability to
complete<00:10:09.279><c> the</c><00:10:09.440><c> text</c><00:10:09.800><c> I'm</c><00:10:10.040><c> actually</c><00:10:10.399><c> writing</c>

00:10:11.389 --> 00:10:11.399 align:start position:0%
complete the text I'm actually writing
 

00:10:11.399 --> 00:10:13.150 align:start position:0%
complete the text I'm actually writing
I've<00:10:11.600><c> modified</c><00:10:12.040><c> it</c><00:10:12.200><c> a</c><00:10:12.360><c> bit</c><00:10:12.519><c> to</c><00:10:12.720><c> use</c><00:10:13.000><c> a</c>

00:10:13.150 --> 00:10:13.160 align:start position:0%
I've modified it a bit to use a
 

00:10:13.160 --> 00:10:14.550 align:start position:0%
I've modified it a bit to use a
different<00:10:13.440><c> prompt</c><00:10:13.880><c> but</c><00:10:13.959><c> you</c><00:10:14.040><c> can</c><00:10:14.200><c> see</c><00:10:14.440><c> I'm</c>

00:10:14.550 --> 00:10:14.560 align:start position:0%
different prompt but you can see I'm
 

00:10:14.560 --> 00:10:17.069 align:start position:0%
different prompt but you can see I'm
using<00:10:14.880><c> the</c><00:10:15.040><c> 3</c><00:10:15.399><c> billion</c><00:10:15.720><c> parameter</c><00:10:16.120><c> model</c><00:10:16.920><c> with</c>

00:10:17.069 --> 00:10:17.079 align:start position:0%
using the 3 billion parameter model with
 

00:10:17.079 --> 00:10:19.430 align:start position:0%
using the 3 billion parameter model with
the<00:10:17.200><c> larger</c><00:10:17.640><c> context</c><00:10:18.640><c> now</c><00:10:18.800><c> I'll</c><00:10:18.959><c> paste</c><00:10:19.279><c> the</c>

00:10:19.430 --> 00:10:19.440 align:start position:0%
the larger context now I'll paste the
 

00:10:19.440 --> 00:10:21.350 align:start position:0%
the larger context now I'll paste the
first<00:10:19.640><c> few</c><00:10:19.880><c> paragraphs</c><00:10:20.399><c> of</c><00:10:20.519><c> the</c><00:10:20.680><c> script</c><00:10:21.079><c> for</c>

00:10:21.350 --> 00:10:21.360 align:start position:0%
first few paragraphs of the script for
 

00:10:21.360 --> 00:10:23.829 align:start position:0%
first few paragraphs of the script for
the<00:10:21.519><c> video</c><00:10:21.880><c> on</c><00:10:22.240><c> environment</c><00:10:22.760><c> variables</c><00:10:23.640><c> and</c>

00:10:23.829 --> 00:10:23.839 align:start position:0%
the video on environment variables and
 

00:10:23.839 --> 00:10:26.870 align:start position:0%
the video on environment variables and
every<00:10:24.040><c> time</c><00:10:24.240><c> I</c><00:10:24.880><c> pause</c><00:10:25.880><c> it</c><00:10:26.040><c> continues</c><00:10:26.519><c> writing</c>

00:10:26.870 --> 00:10:26.880 align:start position:0%
every time I pause it continues writing
 

00:10:26.880 --> 00:10:29.710 align:start position:0%
every time I pause it continues writing
for<00:10:27.160><c> me</c><00:10:28.079><c> when</c><00:10:28.200><c> I</c><00:10:28.320><c> see</c><00:10:28.519><c> some</c><00:10:28.680><c> text</c><00:10:28.959><c> I</c><00:10:29.079><c> like</c><00:10:29.519><c> I</c><00:10:29.600><c> can</c>

00:10:29.710 --> 00:10:29.720 align:start position:0%
for me when I see some text I like I can
 

00:10:29.720 --> 00:10:32.710 align:start position:0%
for me when I see some text I like I can
just<00:10:29.920><c> press</c><00:10:30.240><c> tab</c><00:10:31.000><c> to</c><00:10:31.279><c> accept</c><00:10:31.680><c> each</c><00:10:31.920><c> word</c><00:10:32.600><c> and</c>

00:10:32.710 --> 00:10:32.720 align:start position:0%
just press tab to accept each word and
 

00:10:32.720 --> 00:10:35.069 align:start position:0%
just press tab to accept each word and
then<00:10:32.920><c> I</c><00:10:33.000><c> can</c><00:10:33.279><c> continue</c><00:10:34.279><c> it's</c><00:10:34.440><c> not</c><00:10:34.640><c> always</c>

00:10:35.069 --> 00:10:35.079 align:start position:0%
then I can continue it's not always
 

00:10:35.079 --> 00:10:38.310 align:start position:0%
then I can continue it's not always
perfect<00:10:35.600><c> but</c><00:10:35.720><c> it</c><00:10:35.880><c> works</c><00:10:36.399><c> well</c><00:10:37.279><c> often</c><00:10:37.639><c> enough</c>

00:10:38.310 --> 00:10:38.320 align:start position:0%
perfect but it works well often enough
 

00:10:38.320 --> 00:10:41.670 align:start position:0%
perfect but it works well often enough
that<00:10:38.480><c> I</c><00:10:38.639><c> find</c><00:10:38.839><c> it</c><00:10:39.279><c> really</c><00:10:40.120><c> helpful</c><00:10:41.120><c> so</c><00:10:41.519><c> that's</c>

00:10:41.670 --> 00:10:41.680 align:start position:0%
that I find it really helpful so that's
 

00:10:41.680 --> 00:10:45.150 align:start position:0%
that I find it really helpful so that's
a<00:10:41.880><c> quick</c><00:10:42.160><c> intro</c><00:10:42.760><c> to</c><00:10:42.959><c> the</c><00:10:43.200><c> new</c><00:10:43.560><c> llama</c><00:10:44.000><c> 3.2</c>

00:10:45.150 --> 00:10:45.160 align:start position:0%
a quick intro to the new llama 3.2
 

00:10:45.160 --> 00:10:47.870 align:start position:0%
a quick intro to the new llama 3.2
models<00:10:46.160><c> I</c><00:10:46.360><c> was</c><00:10:46.839><c> hoping</c><00:10:47.360><c> that</c><00:10:47.480><c> the</c><00:10:47.600><c> vision</c>

00:10:47.870 --> 00:10:47.880 align:start position:0%
models I was hoping that the vision
 

00:10:47.880 --> 00:10:50.590 align:start position:0%
models I was hoping that the vision
models<00:10:48.279><c> would</c><00:10:48.440><c> be</c><00:10:48.639><c> available</c><00:10:49.120><c> by</c><00:10:49.279><c> now</c><00:10:49.800><c> but</c>

00:10:50.590 --> 00:10:50.600 align:start position:0%
models would be available by now but
 

00:10:50.600 --> 00:10:53.670 align:start position:0%
models would be available by now but
they<00:10:51.200><c> aren't</c><00:10:52.200><c> the</c><00:10:52.320><c> AMA</c><00:10:52.720><c> team</c><00:10:52.920><c> is</c><00:10:53.000><c> working</c><00:10:53.399><c> hard</c>

00:10:53.670 --> 00:10:53.680 align:start position:0%
they aren't the AMA team is working hard
 

00:10:53.680 --> 00:10:55.590 align:start position:0%
they aren't the AMA team is working hard
on<00:10:53.880><c> making</c><00:10:54.160><c> them</c><00:10:54.440><c> compatible</c><00:10:55.000><c> and</c><00:10:55.120><c> it</c><00:10:55.360><c> looks</c>

00:10:55.590 --> 00:10:55.600 align:start position:0%
on making them compatible and it looks
 

00:10:55.600 --> 00:10:58.430 align:start position:0%
on making them compatible and it looks
like<00:10:55.800><c> they're</c><00:10:56.040><c> getting</c><00:10:56.560><c> really</c><00:10:56.959><c> really</c><00:10:57.440><c> close</c>

00:10:58.430 --> 00:10:58.440 align:start position:0%
like they're getting really really close
 

00:10:58.440 --> 00:11:00.030 align:start position:0%
like they're getting really really close
so<00:10:58.639><c> hopefully</c><00:10:58.959><c> by</c><00:10:59.079><c> the</c><00:10:59.440><c> time</c><00:10:59.560><c> you</c><00:10:59.639><c> see</c><00:10:59.880><c> this</c>

00:11:00.030 --> 00:11:00.040 align:start position:0%
so hopefully by the time you see this
 

00:11:00.040 --> 00:11:02.269 align:start position:0%
so hopefully by the time you see this
video<00:11:00.320><c> it's</c><00:11:00.519><c> out</c><00:11:00.880><c> and</c><00:11:01.079><c> I</c><00:11:01.200><c> have</c><00:11:01.320><c> to</c><00:11:01.480><c> do</c><00:11:01.720><c> an</c>

00:11:02.269 --> 00:11:02.279 align:start position:0%
video it's out and I have to do an
 

00:11:02.279 --> 00:11:05.269 align:start position:0%
video it's out and I have to do an
update<00:11:03.279><c> what</c><00:11:03.440><c> do</c><00:11:03.680><c> I</c><00:11:03.920><c> think</c><00:11:04.200><c> of</c><00:11:04.399><c> these</c><00:11:04.639><c> small</c>

00:11:05.269 --> 00:11:05.279 align:start position:0%
update what do I think of these small
 

00:11:05.279 --> 00:11:08.030 align:start position:0%
update what do I think of these small
llama<00:11:05.680><c> 3.2</c><00:11:06.320><c> text</c><00:11:06.560><c> models</c><00:11:07.399><c> but</c><00:11:07.560><c> I'm</c><00:11:07.800><c> quite</c>

00:11:08.030 --> 00:11:08.040 align:start position:0%
llama 3.2 text models but I'm quite
 

00:11:08.040 --> 00:11:10.350 align:start position:0%
llama 3.2 text models but I'm quite
impressed<00:11:08.800><c> they're</c><00:11:09.120><c> definitely</c><00:11:09.560><c> my</c><00:11:09.880><c> favorite</c>

00:11:10.350 --> 00:11:10.360 align:start position:0%
impressed they're definitely my favorite
 

00:11:10.360 --> 00:11:12.629 align:start position:0%
impressed they're definitely my favorite
of<00:11:10.519><c> the</c><00:11:10.720><c> smaller</c><00:11:11.160><c> models</c><00:11:11.600><c> blowing</c><00:11:12.079><c> away</c>

00:11:12.629 --> 00:11:12.639 align:start position:0%
of the smaller models blowing away
 

00:11:12.639 --> 00:11:14.949 align:start position:0%
of the smaller models blowing away
everything<00:11:13.079><c> else</c><00:11:13.360><c> out</c><00:11:13.600><c> there</c><00:11:14.560><c> what</c><00:11:14.639><c> do</c><00:11:14.800><c> you</c>

00:11:14.949 --> 00:11:14.959 align:start position:0%
everything else out there what do you
 

00:11:14.959 --> 00:11:17.110 align:start position:0%
everything else out there what do you
think<00:11:15.720><c> I'd</c><00:11:15.880><c> love</c><00:11:16.079><c> to</c><00:11:16.240><c> hear</c><00:11:16.480><c> your</c><00:11:16.760><c> comments</c>

00:11:17.110 --> 00:11:17.120 align:start position:0%
think I'd love to hear your comments
 

00:11:17.120 --> 00:11:20.389 align:start position:0%
think I'd love to hear your comments
down<00:11:17.320><c> below</c><00:11:17.880><c> and</c><00:11:18.040><c> if</c><00:11:18.160><c> you</c><00:11:18.360><c> have</c><00:11:18.920><c> any</c><00:11:19.440><c> questions</c>

00:11:20.389 --> 00:11:20.399 align:start position:0%
down below and if you have any questions
 

00:11:20.399 --> 00:11:23.389 align:start position:0%
down below and if you have any questions
about<00:11:21.399><c> anything</c><00:11:22.279><c> you</c><00:11:22.399><c> can</c><00:11:22.600><c> leave</c><00:11:22.920><c> those</c><00:11:23.120><c> down</c>

00:11:23.389 --> 00:11:23.399 align:start position:0%
about anything you can leave those down
 

00:11:23.399 --> 00:11:28.230 align:start position:0%
about anything you can leave those down
there<00:11:23.600><c> too</c><00:11:24.240><c> thanks</c><00:11:24.480><c> so</c><00:11:24.600><c> much</c><00:11:24.760><c> for</c><00:11:24.959><c> watching</c>

00:11:28.230 --> 00:11:28.240 align:start position:0%
 
 

00:11:28.240 --> 00:11:32.279 align:start position:0%
 
goodbye<00:11:29.279><c> for</c>

