WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:01.670 align:start position:0%
 
hey<00:00:00.160><c> and</c><00:00:00.280><c> welcome</c><00:00:00.560><c> back</c><00:00:00.680><c> to</c><00:00:00.919><c> another</c><00:00:01.360><c> video</c>

00:00:01.670 --> 00:00:01.680 align:start position:0%
hey and welcome back to another video
 

00:00:01.680 --> 00:00:03.510 align:start position:0%
hey and welcome back to another video
and<00:00:01.839><c> today</c><00:00:02.120><c> I'm</c><00:00:02.200><c> going</c><00:00:02.320><c> to</c><00:00:02.440><c> show</c><00:00:02.639><c> you</c><00:00:02.840><c> autogen</c>

00:00:03.510 --> 00:00:03.520 align:start position:0%
and today I'm going to show you autogen
 

00:00:03.520 --> 00:00:05.550 align:start position:0%
and today I'm going to show you autogen
logging<00:00:04.040><c> now</c><00:00:04.160><c> you</c><00:00:04.240><c> might</c><00:00:04.400><c> be</c><00:00:04.520><c> asking</c><00:00:05.200><c> how</c><00:00:05.400><c> can</c>

00:00:05.550 --> 00:00:05.560 align:start position:0%
logging now you might be asking how can
 

00:00:05.560 --> 00:00:07.829 align:start position:0%
logging now you might be asking how can
this<00:00:05.720><c> help</c><00:00:05.879><c> me</c><00:00:06.120><c> and</c><00:00:06.440><c> what</c><00:00:06.600><c> can</c><00:00:06.720><c> it</c><00:00:06.879><c> be</c><00:00:07.000><c> used</c><00:00:07.319><c> for</c>

00:00:07.829 --> 00:00:07.839 align:start position:0%
this help me and what can it be used for
 

00:00:07.839 --> 00:00:09.390 align:start position:0%
this help me and what can it be used for
well<00:00:08.000><c> we</c><00:00:08.120><c> can</c><00:00:08.200><c> take</c><00:00:08.320><c> a</c><00:00:08.440><c> look</c><00:00:08.559><c> at</c><00:00:08.800><c> performance</c>

00:00:09.390 --> 00:00:09.400 align:start position:0%
well we can take a look at performance
 

00:00:09.400 --> 00:00:11.910 align:start position:0%
well we can take a look at performance
analysis<00:00:10.120><c> of</c><00:00:10.320><c> the</c><00:00:10.440><c> llm</c><00:00:10.960><c> calls</c><00:00:11.440><c> we</c><00:00:11.519><c> can</c><00:00:11.639><c> see</c><00:00:11.799><c> how</c>

00:00:11.910 --> 00:00:11.920 align:start position:0%
analysis of the llm calls we can see how
 

00:00:11.920 --> 00:00:13.669 align:start position:0%
analysis of the llm calls we can see how
long<00:00:12.080><c> it</c><00:00:12.200><c> took</c><00:00:12.400><c> the</c><00:00:12.519><c> chat</c><00:00:12.719><c> to</c><00:00:12.920><c> complete</c><00:00:13.559><c> how</c>

00:00:13.669 --> 00:00:13.679 align:start position:0%
long it took the chat to complete how
 

00:00:13.679 --> 00:00:15.789 align:start position:0%
long it took the chat to complete how
many<00:00:13.920><c> tokens</c><00:00:14.280><c> did</c><00:00:14.440><c> we</c><00:00:14.599><c> use</c><00:00:15.040><c> what</c><00:00:15.160><c> was</c><00:00:15.320><c> the</c><00:00:15.480><c> cost</c>

00:00:15.789 --> 00:00:15.799 align:start position:0%
many tokens did we use what was the cost
 

00:00:15.799 --> 00:00:17.109 align:start position:0%
many tokens did we use what was the cost
un<00:00:15.879><c> let's</c><00:00:16.039><c> you're</c><00:00:16.119><c> using</c><00:00:16.359><c> open</c><00:00:16.600><c> source</c><00:00:16.840><c> local</c>

00:00:17.109 --> 00:00:17.119 align:start position:0%
un let's you're using open source local
 

00:00:17.119 --> 00:00:20.029 align:start position:0%
un let's you're using open source local
llms<00:00:17.800><c> and</c><00:00:17.960><c> we</c><00:00:18.039><c> can</c><00:00:18.199><c> simply</c><00:00:18.520><c> log</c><00:00:18.800><c> the</c><00:00:19.039><c> response</c>

00:00:20.029 --> 00:00:20.039 align:start position:0%
llms and we can simply log the response
 

00:00:20.039 --> 00:00:22.189 align:start position:0%
llms and we can simply log the response
well<00:00:20.279><c> how</c><00:00:20.400><c> it</c><00:00:20.519><c> works</c><00:00:21.039><c> is</c><00:00:21.199><c> we'll</c><00:00:21.400><c> start</c><00:00:21.640><c> the</c><00:00:21.800><c> log</c>

00:00:22.189 --> 00:00:22.199 align:start position:0%
well how it works is we'll start the log
 

00:00:22.199 --> 00:00:24.550 align:start position:0%
well how it works is we'll start the log
before<00:00:22.480><c> we</c><00:00:22.720><c> initiate</c><00:00:23.240><c> the</c><00:00:23.359><c> chat</c><00:00:23.599><c> for</c><00:00:23.760><c> auten</c>

00:00:24.550 --> 00:00:24.560 align:start position:0%
before we initiate the chat for auten
 

00:00:24.560 --> 00:00:26.509 align:start position:0%
before we initiate the chat for auten
and<00:00:24.720><c> then</c><00:00:25.119><c> after</c><00:00:25.439><c> it's</c><00:00:25.680><c> over</c><00:00:26.160><c> then</c><00:00:26.279><c> we'll</c>

00:00:26.509 --> 00:00:26.519 align:start position:0%
and then after it's over then we'll
 

00:00:26.519 --> 00:00:28.070 align:start position:0%
and then after it's over then we'll
simply<00:00:26.800><c> call</c><00:00:27.000><c> it</c><00:00:27.080><c> to</c><00:00:27.199><c> stop</c><00:00:27.439><c> the</c><00:00:27.560><c> log</c><00:00:27.880><c> and</c><00:00:27.960><c> then</c>

00:00:28.070 --> 00:00:28.080 align:start position:0%
simply call it to stop the log and then
 

00:00:28.080 --> 00:00:29.589 align:start position:0%
simply call it to stop the log and then
what<00:00:28.199><c> happens</c><00:00:28.480><c> it</c><00:00:28.640><c> stores</c><00:00:29.039><c> this</c><00:00:29.199><c> performance</c>

00:00:29.589 --> 00:00:29.599 align:start position:0%
what happens it stores this performance
 

00:00:29.599 --> 00:00:31.669 align:start position:0%
what happens it stores this performance
analysis<00:00:30.080><c> this</c><00:00:30.240><c> into</c><00:00:30.519><c> a</c><00:00:30.640><c> database</c><00:00:31.119><c> for</c><00:00:31.320><c> us</c>

00:00:31.669 --> 00:00:31.679 align:start position:0%
analysis this into a database for us
 

00:00:31.679 --> 00:00:33.310 align:start position:0%
analysis this into a database for us
let's<00:00:31.840><c> see</c><00:00:31.960><c> how</c><00:00:32.079><c> it</c><00:00:32.160><c> looks</c><00:00:32.360><c> in</c><00:00:32.599><c> code</c><00:00:33.079><c> the</c><00:00:33.200><c> first</c>

00:00:33.310 --> 00:00:33.320 align:start position:0%
let's see how it looks in code the first
 

00:00:33.320 --> 00:00:34.869 align:start position:0%
let's see how it looks in code the first
thing<00:00:33.440><c> we're</c><00:00:33.520><c> going</c><00:00:33.640><c> to</c><00:00:33.719><c> do</c><00:00:33.840><c> is</c><00:00:33.960><c> call</c><00:00:34.160><c> autogen</c>

00:00:34.869 --> 00:00:34.879 align:start position:0%
thing we're going to do is call autogen
 

00:00:34.879 --> 00:00:37.190 align:start position:0%
thing we're going to do is call autogen
runtime<00:00:35.399><c> logging</c><00:00:35.800><c> dostart</c><00:00:36.719><c> and</c><00:00:36.840><c> for</c><00:00:37.040><c> the</c>

00:00:37.190 --> 00:00:37.200 align:start position:0%
runtime logging dostart and for the
 

00:00:37.200 --> 00:00:38.670 align:start position:0%
runtime logging dostart and for the
configuration<00:00:37.960><c> we're</c><00:00:38.120><c> just</c><00:00:38.200><c> going</c><00:00:38.280><c> to</c><00:00:38.399><c> give</c><00:00:38.520><c> a</c>

00:00:38.670 --> 00:00:38.680 align:start position:0%
configuration we're just going to give a
 

00:00:38.680 --> 00:00:41.270 align:start position:0%
configuration we're just going to give a
DB<00:00:39.040><c> name</c><00:00:39.360><c> here</c><00:00:39.520><c> I</c><00:00:39.680><c> give</c><00:00:39.800><c> it</c><00:00:39.920><c> logs.</c><00:00:40.600><c> DB</c><00:00:41.079><c> and</c><00:00:41.160><c> then</c>

00:00:41.270 --> 00:00:41.280 align:start position:0%
DB name here I give it logs. DB and then
 

00:00:41.280 --> 00:00:43.430 align:start position:0%
DB name here I give it logs. DB and then
we'll<00:00:41.480><c> have</c><00:00:41.719><c> some</c><00:00:41.920><c> initiate</c><00:00:42.320><c> chat</c><00:00:42.600><c> logic</c><00:00:43.360><c> and</c>

00:00:43.430 --> 00:00:43.440 align:start position:0%
we'll have some initiate chat logic and
 

00:00:43.440 --> 00:00:45.630 align:start position:0%
we'll have some initiate chat logic and
then<00:00:43.559><c> we'll</c><00:00:43.760><c> call</c><00:00:44.039><c> autogen</c><00:00:44.800><c> runtime</c><00:00:45.239><c> logging</c>

00:00:45.630 --> 00:00:45.640 align:start position:0%
then we'll call autogen runtime logging
 

00:00:45.640 --> 00:00:47.630 align:start position:0%
then we'll call autogen runtime logging
doop<00:00:46.280><c> after</c><00:00:46.520><c> that's</c><00:00:46.719><c> done</c><00:00:47.199><c> it's</c><00:00:47.360><c> going</c><00:00:47.480><c> to</c>

00:00:47.630 --> 00:00:47.640 align:start position:0%
doop after that's done it's going to
 

00:00:47.640 --> 00:00:50.069 align:start position:0%
doop after that's done it's going to
create<00:00:47.879><c> a</c><00:00:48.079><c> logs.</c><00:00:48.719><c> DB</c><00:00:49.079><c> for</c><00:00:49.320><c> me</c><00:00:49.640><c> if</c><00:00:49.760><c> I</c><00:00:49.879><c> haven't</c>

00:00:50.069 --> 00:00:50.079 align:start position:0%
create a logs. DB for me if I haven't
 

00:00:50.079 --> 00:00:52.229 align:start position:0%
create a logs. DB for me if I haven't
ran<00:00:50.399><c> this</c><00:00:50.640><c> already</c><00:00:51.280><c> and</c><00:00:51.440><c> then</c><00:00:51.719><c> insert</c><00:00:52.120><c> the</c>

00:00:52.229 --> 00:00:52.239 align:start position:0%
ran this already and then insert the
 

00:00:52.239 --> 00:00:53.630 align:start position:0%
ran this already and then insert the
rows<00:00:52.480><c> of</c><00:00:52.680><c> data</c><00:00:52.960><c> and</c><00:00:53.039><c> then</c><00:00:53.120><c> we</c><00:00:53.199><c> can</c><00:00:53.320><c> retrieve</c>

00:00:53.630 --> 00:00:53.640 align:start position:0%
rows of data and then we can retrieve
 

00:00:53.640 --> 00:00:54.869 align:start position:0%
rows of data and then we can retrieve
this<00:00:53.760><c> data</c><00:00:54.000><c> and</c><00:00:54.120><c> see</c><00:00:54.280><c> the</c><00:00:54.399><c> performance</c>

00:00:54.869 --> 00:00:54.879 align:start position:0%
this data and see the performance
 

00:00:54.879 --> 00:00:56.950 align:start position:0%
this data and see the performance
analysis<00:00:55.480><c> of</c><00:00:55.640><c> our</c><00:00:55.840><c> calls</c><00:00:56.280><c> one</c><00:00:56.440><c> way</c><00:00:56.559><c> to</c><00:00:56.640><c> do</c><00:00:56.840><c> that</c>

00:00:56.950 --> 00:00:56.960 align:start position:0%
analysis of our calls one way to do that
 

00:00:56.960 --> 00:00:58.950 align:start position:0%
analysis of our calls one way to do that
is<00:00:57.079><c> to</c><00:00:57.199><c> use</c><00:00:57.440><c> SQL</c><00:00:57.879><c> light</c><00:00:58.120><c> browser</c><00:00:58.640><c> this</c><00:00:58.719><c> is</c><00:00:58.840><c> a</c>

00:00:58.950 --> 00:00:58.960 align:start position:0%
is to use SQL light browser this is a
 

00:00:58.960 --> 00:01:00.750 align:start position:0%
is to use SQL light browser this is a
free<00:00:59.199><c> software</c><00:00:59.719><c> where</c><00:01:00.000><c> all</c><00:01:00.120><c> you</c><00:01:00.199><c> need</c><00:01:00.320><c> to</c><00:01:00.480><c> do</c>

00:01:00.750 --> 00:01:00.760 align:start position:0%
free software where all you need to do
 

00:01:00.760 --> 00:01:03.509 align:start position:0%
free software where all you need to do
is<00:01:01.000><c> open</c><00:01:01.239><c> up</c><00:01:01.440><c> your</c><00:01:01.680><c> database</c><00:01:02.399><c> in</c><00:01:02.559><c> the</c><00:01:02.760><c> software</c>

00:01:03.509 --> 00:01:03.519 align:start position:0%
is open up your database in the software
 

00:01:03.519 --> 00:01:05.509 align:start position:0%
is open up your database in the software
and<00:01:03.640><c> then</c><00:01:03.800><c> you</c><00:01:03.920><c> can</c><00:01:04.080><c> see</c><00:01:04.320><c> all</c><00:01:04.439><c> the</c><00:01:04.640><c> tables</c><00:01:05.400><c> and</c>

00:01:05.509 --> 00:01:05.519 align:start position:0%
and then you can see all the tables and
 

00:01:05.519 --> 00:01:07.030 align:start position:0%
and then you can see all the tables and
then<00:01:05.640><c> all</c><00:01:05.760><c> of</c><00:01:05.880><c> the</c><00:01:05.960><c> rows</c><00:01:06.200><c> of</c><00:01:06.320><c> data</c><00:01:06.600><c> for</c><00:01:06.840><c> each</c>

00:01:07.030 --> 00:01:07.040 align:start position:0%
then all of the rows of data for each
 

00:01:07.040 --> 00:01:08.590 align:start position:0%
then all of the rows of data for each
table<00:01:07.320><c> inside</c><00:01:07.560><c> of</c><00:01:07.640><c> your</c><00:01:07.720><c> database</c><00:01:08.320><c> and</c><00:01:08.439><c> here</c>

00:01:08.590 --> 00:01:08.600 align:start position:0%
table inside of your database and here
 

00:01:08.600 --> 00:01:11.270 align:start position:0%
table inside of your database and here
for<00:01:08.799><c> instance</c><00:01:09.439><c> the</c><00:01:09.680><c> cost</c><00:01:09.920><c> of</c><00:01:10.040><c> 0.0</c><00:01:10.880><c> is</c><00:01:11.000><c> whenever</c>

00:01:11.270 --> 00:01:11.280 align:start position:0%
for instance the cost of 0.0 is whenever
 

00:01:11.280 --> 00:01:14.109 align:start position:0%
for instance the cost of 0.0 is whenever
I<00:01:11.400><c> use</c><00:01:11.640><c> GPT</c><00:01:12.240><c> 3.5</c><00:01:13.040><c> turbo</c><00:01:13.439><c> it's</c><00:01:13.560><c> not</c><00:01:13.799><c> exactly</c>

00:01:14.109 --> 00:01:14.119 align:start position:0%
I use GPT 3.5 turbo it's not exactly
 

00:01:14.119 --> 00:01:16.830 align:start position:0%
I use GPT 3.5 turbo it's not exactly
free<00:01:14.439><c> it</c><00:01:14.560><c> just</c><00:01:14.960><c> is</c><00:01:15.200><c> very</c><00:01:15.400><c> minimal</c><00:01:15.920><c> cents</c><00:01:16.640><c> and</c>

00:01:16.830 --> 00:01:16.840 align:start position:0%
free it just is very minimal cents and
 

00:01:16.840 --> 00:01:18.990 align:start position:0%
free it just is very minimal cents and
then<00:01:17.240><c> between</c><00:01:17.520><c> the</c><00:01:17.680><c> two</c><00:01:17.880><c> and</c><00:01:18.080><c> 3</c><00:01:18.320><c> cents</c><00:01:18.680><c> range</c>

00:01:18.990 --> 00:01:19.000 align:start position:0%
then between the two and 3 cents range
 

00:01:19.000 --> 00:01:21.030 align:start position:0%
then between the two and 3 cents range
that's<00:01:19.159><c> when</c><00:01:19.320><c> I</c><00:01:19.439><c> use</c><00:01:19.640><c> GPT</c><00:01:20.119><c> 4</c><00:01:20.479><c> so</c><00:01:20.600><c> you</c><00:01:20.680><c> can</c><00:01:20.799><c> see</c>

00:01:21.030 --> 00:01:21.040 align:start position:0%
that's when I use GPT 4 so you can see
 

00:01:21.040 --> 00:01:22.670 align:start position:0%
that's when I use GPT 4 so you can see
the<00:01:21.240><c> cost</c><00:01:21.520><c> increase</c><00:01:22.079><c> another</c><00:01:22.320><c> way</c><00:01:22.439><c> is</c><00:01:22.520><c> to</c>

00:01:22.670 --> 00:01:22.680 align:start position:0%
the cost increase another way is to
 

00:01:22.680 --> 00:01:24.710 align:start position:0%
the cost increase another way is to
install<00:01:22.960><c> the</c><00:01:23.079><c> pandas</c><00:01:23.520><c> library</c><00:01:24.360><c> and</c><00:01:24.479><c> then</c><00:01:24.640><c> we</c>

00:01:24.710 --> 00:01:24.720 align:start position:0%
install the pandas library and then we
 

00:01:24.720 --> 00:01:26.350 align:start position:0%
install the pandas library and then we
can<00:01:24.920><c> retrieve</c><00:01:25.240><c> the</c><00:01:25.400><c> data</c><00:01:25.720><c> how</c><00:01:25.880><c> we</c><00:01:26.040><c> want</c><00:01:26.200><c> and</c>

00:01:26.350 --> 00:01:26.360 align:start position:0%
can retrieve the data how we want and
 

00:01:26.360 --> 00:01:27.830 align:start position:0%
can retrieve the data how we want and
make<00:01:26.479><c> it</c><00:01:26.640><c> look</c><00:01:26.840><c> nice</c><00:01:27.040><c> in</c><00:01:27.119><c> our</c><00:01:27.280><c> terminal</c><00:01:27.759><c> I</c>

00:01:27.830 --> 00:01:27.840 align:start position:0%
make it look nice in our terminal I
 

00:01:27.840 --> 00:01:29.390 align:start position:0%
make it look nice in our terminal I
think<00:01:27.960><c> this</c><00:01:28.040><c> would</c><00:01:28.200><c> be</c><00:01:28.400><c> particularly</c><00:01:29.040><c> useful</c>

00:01:29.390 --> 00:01:29.400 align:start position:0%
think this would be particularly useful
 

00:01:29.400 --> 00:01:31.550 align:start position:0%
think this would be particularly useful
for<00:01:29.600><c> open</c><00:01:29.880><c> open</c><00:01:30.079><c> source</c><00:01:30.320><c> local</c><00:01:30.640><c> llms</c><00:01:31.280><c> and</c><00:01:31.400><c> so</c>

00:01:31.550 --> 00:01:31.560 align:start position:0%
for open open source local llms and so
 

00:01:31.560 --> 00:01:33.310 align:start position:0%
for open open source local llms and so
we<00:01:31.640><c> can</c><00:01:31.840><c> decide</c><00:01:32.280><c> which</c><00:01:32.399><c> one</c><00:01:32.560><c> was</c><00:01:32.720><c> the</c><00:01:32.920><c> quick</c><00:01:33.159><c> to</c>

00:01:33.310 --> 00:01:33.320 align:start position:0%
we can decide which one was the quick to
 

00:01:33.320 --> 00:01:35.310 align:start position:0%
we can decide which one was the quick to
finish<00:01:33.600><c> the</c><00:01:33.840><c> task</c><00:01:34.439><c> which</c><00:01:34.560><c> one</c><00:01:34.680><c> used</c><00:01:34.960><c> the</c><00:01:35.079><c> least</c>

00:01:35.310 --> 00:01:35.320 align:start position:0%
finish the task which one used the least
 

00:01:35.320 --> 00:01:37.190 align:start position:0%
finish the task which one used the least
number<00:01:35.520><c> of</c><00:01:35.720><c> tokens</c><00:01:36.360><c> and</c><00:01:36.560><c> which</c><00:01:36.720><c> one</c><00:01:36.880><c> gave</c><00:01:37.040><c> us</c>

00:01:37.190 --> 00:01:37.200 align:start position:0%
number of tokens and which one gave us
 

00:01:37.200 --> 00:01:39.069 align:start position:0%
number of tokens and which one gave us
the<00:01:37.360><c> better</c><00:01:37.680><c> responses</c><00:01:38.520><c> well</c><00:01:38.680><c> let's</c><00:01:38.799><c> get</c><00:01:38.920><c> into</c>

00:01:39.069 --> 00:01:39.079 align:start position:0%
the better responses well let's get into
 

00:01:39.079 --> 00:01:40.590 align:start position:0%
the better responses well let's get into
the<00:01:39.159><c> code</c><00:01:39.360><c> and</c><00:01:39.479><c> look</c><00:01:39.600><c> at</c><00:01:39.720><c> some</c><00:01:39.880><c> examples</c><00:01:40.520><c> all</c>

00:01:40.590 --> 00:01:40.600 align:start position:0%
the code and look at some examples all
 

00:01:40.600 --> 00:01:41.670 align:start position:0%
the code and look at some examples all
right<00:01:40.720><c> well</c><00:01:40.840><c> the</c><00:01:40.920><c> first</c><00:01:41.079><c> thing</c><00:01:41.159><c> we</c><00:01:41.280><c> need</c><00:01:41.399><c> to</c><00:01:41.520><c> do</c>

00:01:41.670 --> 00:01:41.680 align:start position:0%
right well the first thing we need to do
 

00:01:41.680 --> 00:01:42.990 align:start position:0%
right well the first thing we need to do
is<00:01:41.840><c> create</c><00:01:42.119><c> two</c><00:01:42.360><c> files</c><00:01:42.680><c> we're</c><00:01:42.799><c> going</c><00:01:42.880><c> to</c>

00:01:42.990 --> 00:01:43.000 align:start position:0%
is create two files we're going to
 

00:01:43.000 --> 00:01:45.230 align:start position:0%
is create two files we're going to
create<00:01:43.240><c> a</c><00:01:43.399><c> main</c><00:01:43.759><c> python</c><00:01:44.200><c> file</c><00:01:44.840><c> and</c><00:01:44.960><c> then</c><00:01:45.079><c> the</c>

00:01:45.230 --> 00:01:45.240 align:start position:0%
create a main python file and then the
 

00:01:45.240 --> 00:01:48.109 align:start position:0%
create a main python file and then the
open<00:01:45.479><c> AI</c><00:01:45.880><c> config</c><00:01:46.680><c> Json</c><00:01:47.200><c> file</c><00:01:47.479><c> to</c><00:01:47.640><c> hold</c><00:01:47.960><c> our</c>

00:01:48.109 --> 00:01:48.119 align:start position:0%
open AI config Json file to hold our
 

00:01:48.119 --> 00:01:50.069 align:start position:0%
open AI config Json file to hold our
model<00:01:48.439><c> and</c><00:01:48.600><c> API</c><00:01:49.040><c> key</c><00:01:49.439><c> you</c><00:01:49.520><c> could</c><00:01:49.680><c> also</c><00:01:49.880><c> use</c>

00:01:50.069 --> 00:01:50.079 align:start position:0%
model and API key you could also use
 

00:01:50.079 --> 00:01:51.670 align:start position:0%
model and API key you could also use
this<00:01:50.200><c> to</c><00:01:50.320><c> store</c><00:01:50.600><c> the</c><00:01:50.719><c> base</c><00:01:51.000><c> URL</c><00:01:51.439><c> if</c><00:01:51.560><c> you're</c>

00:01:51.670 --> 00:01:51.680 align:start position:0%
this to store the base URL if you're
 

00:01:51.680 --> 00:01:54.389 align:start position:0%
this to store the base URL if you're
using<00:01:51.920><c> open</c><00:01:52.159><c> source</c><00:01:52.439><c> local</c><00:01:52.719><c> llms</c><00:01:53.640><c> so</c><00:01:53.960><c> over</c><00:01:54.280><c> on</c>

00:01:54.389 --> 00:01:54.399 align:start position:0%
using open source local llms so over on
 

00:01:54.399 --> 00:01:56.389 align:start position:0%
using open source local llms so over on
the<00:01:54.560><c> config</c><00:01:54.920><c> list</c><00:01:55.079><c> Json</c><00:01:55.479><c> file</c><00:01:55.880><c> I</c><00:01:56.000><c> just</c><00:01:56.119><c> simply</c>

00:01:56.389 --> 00:01:56.399 align:start position:0%
the config list Json file I just simply
 

00:01:56.399 --> 00:01:57.910 align:start position:0%
the config list Json file I just simply
had<00:01:56.560><c> the</c><00:01:56.640><c> model</c><00:01:56.920><c> I'm</c><00:01:57.039><c> going</c><00:01:57.159><c> to</c><00:01:57.280><c> use</c><00:01:57.640><c> and</c><00:01:57.759><c> then</c>

00:01:57.910 --> 00:01:57.920 align:start position:0%
had the model I'm going to use and then
 

00:01:57.920 --> 00:02:00.510 align:start position:0%
had the model I'm going to use and then
you<00:01:58.039><c> would</c><00:01:58.280><c> insert</c><00:01:58.759><c> your</c><00:01:59.039><c> API</c><00:01:59.560><c> key</c><00:02:00.079><c> here</c><00:02:00.399><c> and</c>

00:02:00.510 --> 00:02:00.520 align:start position:0%
you would insert your API key here and
 

00:02:00.520 --> 00:02:02.029 align:start position:0%
you would insert your API key here and
if<00:02:00.600><c> you're</c><00:02:00.719><c> using</c><00:02:00.960><c> a</c><00:02:01.119><c> base</c><00:02:01.360><c> URL</c><00:02:01.840><c> with</c>

00:02:02.029 --> 00:02:02.039 align:start position:0%
if you're using a base URL with
 

00:02:02.039 --> 00:02:03.789 align:start position:0%
if you're using a base URL with
something<00:02:02.320><c> like</c><00:02:02.439><c> LM</c><00:02:02.799><c> Studio</c><00:02:03.240><c> you</c><00:02:03.360><c> could</c><00:02:03.520><c> also</c>

00:02:03.789 --> 00:02:03.799 align:start position:0%
something like LM Studio you could also
 

00:02:03.799 --> 00:02:05.749 align:start position:0%
something like LM Studio you could also
paste<00:02:04.039><c> that</c><00:02:04.240><c> here</c><00:02:04.680><c> and</c><00:02:04.799><c> now</c><00:02:05.000><c> back</c><00:02:05.119><c> in</c><00:02:05.240><c> our</c><00:02:05.399><c> main</c>

00:02:05.749 --> 00:02:05.759 align:start position:0%
paste that here and now back in our main
 

00:02:05.759 --> 00:02:07.230 align:start position:0%
paste that here and now back in our main
python<00:02:06.159><c> file</c><00:02:06.520><c> the</c><00:02:06.640><c> first</c><00:02:06.799><c> thing</c><00:02:06.920><c> we</c><00:02:07.039><c> need</c><00:02:07.119><c> to</c>

00:02:07.230 --> 00:02:07.240 align:start position:0%
python file the first thing we need to
 

00:02:07.240 --> 00:02:09.070 align:start position:0%
python file the first thing we need to
do<00:02:07.360><c> is</c><00:02:07.479><c> set</c><00:02:07.640><c> up</c><00:02:07.799><c> our</c><00:02:07.920><c> Imports</c><00:02:08.599><c> which</c><00:02:08.759><c> means</c>

00:02:09.070 --> 00:02:09.080 align:start position:0%
do is set up our Imports which means
 

00:02:09.080 --> 00:02:11.070 align:start position:0%
do is set up our Imports which means
that<00:02:09.239><c> we</c><00:02:09.440><c> also</c><00:02:09.759><c> now</c><00:02:09.920><c> need</c><00:02:10.080><c> to</c><00:02:10.319><c> install</c><00:02:10.720><c> them</c><00:02:10.959><c> we</c>

00:02:11.070 --> 00:02:11.080 align:start position:0%
that we also now need to install them we
 

00:02:11.080 --> 00:02:12.790 align:start position:0%
that we also now need to install them we
just<00:02:11.200><c> need</c><00:02:11.319><c> to</c><00:02:11.440><c> open</c><00:02:11.599><c> up</c><00:02:11.760><c> our</c><00:02:11.920><c> terminal</c><00:02:12.640><c> and</c>

00:02:12.790 --> 00:02:12.800 align:start position:0%
just need to open up our terminal and
 

00:02:12.800 --> 00:02:15.509 align:start position:0%
just need to open up our terminal and
then<00:02:13.080><c> type</c><00:02:13.319><c> in</c><00:02:13.599><c> PIP</c><00:02:13.879><c> install</c><00:02:14.400><c> pi</c><00:02:14.680><c> autogen</c><00:02:15.239><c> and</c>

00:02:15.509 --> 00:02:15.519 align:start position:0%
then type in PIP install pi autogen and
 

00:02:15.519 --> 00:02:17.390 align:start position:0%
then type in PIP install pi autogen and
pandas<00:02:16.200><c> and</c><00:02:16.280><c> now</c><00:02:16.400><c> we</c><00:02:16.480><c> need</c><00:02:16.560><c> to</c><00:02:16.680><c> create</c><00:02:16.879><c> the</c><00:02:17.000><c> llm</c>

00:02:17.390 --> 00:02:17.400 align:start position:0%
pandas and now we need to create the llm
 

00:02:17.400 --> 00:02:19.589 align:start position:0%
pandas and now we need to create the llm
config<00:02:17.840><c> for</c><00:02:18.080><c> the</c><00:02:18.200><c> assistant</c><00:02:18.640><c> agent</c><00:02:19.120><c> we</c><00:02:19.280><c> really</c>

00:02:19.589 --> 00:02:19.599 align:start position:0%
config for the assistant agent we really
 

00:02:19.599 --> 00:02:21.790 align:start position:0%
config for the assistant agent we really
only<00:02:19.879><c> need</c><00:02:20.120><c> the</c><00:02:20.280><c> config</c><00:02:20.720><c> list</c><00:02:21.000><c> property</c><00:02:21.400><c> here</c>

00:02:21.790 --> 00:02:21.800 align:start position:0%
only need the config list property here
 

00:02:21.800 --> 00:02:24.270 align:start position:0%
only need the config list property here
which<00:02:21.920><c> is</c><00:02:22.120><c> calling</c><00:02:22.519><c> autogen</c><00:02:23.280><c> doc</c><00:02:23.519><c> config</c><00:02:23.920><c> list</c>

00:02:24.270 --> 00:02:24.280 align:start position:0%
which is calling autogen doc config list
 

00:02:24.280 --> 00:02:27.470 align:start position:0%
which is calling autogen doc config list
from<00:02:24.560><c> Json</c><00:02:25.280><c> and</c><00:02:25.440><c> give</c><00:02:25.560><c> it</c><00:02:25.760><c> the</c><00:02:26.000><c> oi</c><00:02:26.840><c> config</c><00:02:27.280><c> list</c>

00:02:27.470 --> 00:02:27.480 align:start position:0%
from Json and give it the oi config list
 

00:02:27.480 --> 00:02:29.869 align:start position:0%
from Json and give it the oi config list
Json<00:02:28.040><c> file</c><00:02:28.400><c> we</c><00:02:28.560><c> created</c><00:02:28.959><c> earlier</c><00:02:29.440><c> and</c><00:02:29.560><c> what</c>

00:02:29.869 --> 00:02:29.879 align:start position:0%
Json file we created earlier and what
 

00:02:29.879 --> 00:02:32.190 align:start position:0%
Json file we created earlier and what
this<00:02:30.000><c> is</c><00:02:30.120><c> going</c><00:02:30.200><c> to</c><00:02:30.400><c> do</c><00:02:31.040><c> is</c><00:02:31.239><c> get</c><00:02:31.400><c> the</c><00:02:31.599><c> model</c><00:02:32.040><c> in</c>

00:02:32.190 --> 00:02:32.200 align:start position:0%
this is going to do is get the model in
 

00:02:32.200 --> 00:02:34.190 align:start position:0%
this is going to do is get the model in
the<00:02:32.400><c> API</c><00:02:32.879><c> key</c><00:02:33.239><c> that</c><00:02:33.360><c> we</c><00:02:33.480><c> stored</c><00:02:33.840><c> in</c><00:02:33.959><c> there</c><00:02:34.080><c> and</c>

00:02:34.190 --> 00:02:34.200 align:start position:0%
the API key that we stored in there and
 

00:02:34.200 --> 00:02:36.430 align:start position:0%
the API key that we stored in there and
then<00:02:34.280><c> that'll</c><00:02:34.519><c> later</c><00:02:34.760><c> be</c><00:02:35.000><c> used</c><00:02:35.480><c> for</c><00:02:35.720><c> the</c><00:02:35.840><c> llm</c>

00:02:36.430 --> 00:02:36.440 align:start position:0%
then that'll later be used for the llm
 

00:02:36.440 --> 00:02:38.509 align:start position:0%
then that'll later be used for the llm
call<00:02:36.760><c> and</c><00:02:36.879><c> now</c><00:02:37.040><c> we</c><00:02:37.120><c> can</c><00:02:37.280><c> start</c><00:02:37.519><c> our</c><00:02:37.800><c> logging</c><00:02:38.400><c> so</c>

00:02:38.509 --> 00:02:38.519 align:start position:0%
call and now we can start our logging so
 

00:02:38.519 --> 00:02:40.630 align:start position:0%
call and now we can start our logging so
we're<00:02:38.640><c> going</c><00:02:38.760><c> to</c><00:02:38.920><c> call</c><00:02:39.200><c> autogen</c><00:02:40.040><c> runtime</c>

00:02:40.630 --> 00:02:40.640 align:start position:0%
we're going to call autogen runtime
 

00:02:40.640 --> 00:02:42.430 align:start position:0%
we're going to call autogen runtime
logging<00:02:41.280><c> dostart</c><00:02:42.040><c> and</c><00:02:42.159><c> for</c><00:02:42.280><c> the</c>

00:02:42.430 --> 00:02:42.440 align:start position:0%
logging dostart and for the
 

00:02:42.440 --> 00:02:43.910 align:start position:0%
logging dostart and for the
configuration<00:02:43.239><c> like</c><00:02:43.400><c> I</c><00:02:43.480><c> showed</c><00:02:43.720><c> in</c><00:02:43.800><c> the</c>

00:02:43.910 --> 00:02:43.920 align:start position:0%
configuration like I showed in the
 

00:02:43.920 --> 00:02:45.390 align:start position:0%
configuration like I showed in the
PowerPoint<00:02:44.400><c> slide</c><00:02:44.879><c> we're</c><00:02:45.000><c> going</c><00:02:45.120><c> to</c><00:02:45.200><c> give</c><00:02:45.319><c> it</c>

00:02:45.390 --> 00:02:45.400 align:start position:0%
PowerPoint slide we're going to give it
 

00:02:45.400 --> 00:02:48.869 align:start position:0%
PowerPoint slide we're going to give it
a<00:02:45.519><c> DB</c><00:02:45.879><c> name</c><00:02:46.120><c> and</c><00:02:46.319><c> call</c><00:02:46.560><c> it</c><00:02:46.800><c> logs.</c><00:02:47.800><c> DB</c><00:02:48.319><c> over</c><00:02:48.599><c> here</c>

00:02:48.869 --> 00:02:48.879 align:start position:0%
a DB name and call it logs. DB over here
 

00:02:48.879 --> 00:02:50.910 align:start position:0%
a DB name and call it logs. DB over here
in<00:02:49.040><c> my</c><00:02:49.200><c> autogen</c><00:02:49.760><c> logging</c><00:02:50.239><c> directory</c><00:02:50.760><c> where</c>

00:02:50.910 --> 00:02:50.920 align:start position:0%
in my autogen logging directory where
 

00:02:50.920 --> 00:02:53.430 align:start position:0%
in my autogen logging directory where
this<00:02:51.120><c> file</c><00:02:51.400><c> is</c><00:02:51.879><c> I</c><00:02:51.959><c> don't</c><00:02:52.239><c> have</c><00:02:52.360><c> the</c><00:02:52.560><c> logs.</c><00:02:53.040><c> DB</c>

00:02:53.430 --> 00:02:53.440 align:start position:0%
this file is I don't have the logs. DB
 

00:02:53.440 --> 00:02:55.149 align:start position:0%
this file is I don't have the logs. DB
it<00:02:53.519><c> will</c><00:02:53.720><c> create</c><00:02:53.959><c> this</c><00:02:54.040><c> for</c><00:02:54.239><c> us</c><00:02:54.480><c> automatically</c>

00:02:55.149 --> 00:02:55.159 align:start position:0%
it will create this for us automatically
 

00:02:55.159 --> 00:02:56.309 align:start position:0%
it will create this for us automatically
whenever<00:02:55.440><c> it's</c><00:02:55.599><c> done</c><00:02:55.840><c> and</c><00:02:55.920><c> then</c><00:02:56.040><c> for</c><00:02:56.159><c> this</c>

00:02:56.309 --> 00:02:56.319 align:start position:0%
whenever it's done and then for this
 

00:02:56.319 --> 00:02:57.869 align:start position:0%
whenever it's done and then for this
simple<00:02:56.640><c> example</c><00:02:57.159><c> I'm</c><00:02:57.280><c> just</c><00:02:57.400><c> going</c><00:02:57.480><c> to</c><00:02:57.599><c> have</c><00:02:57.680><c> an</c>

00:02:57.869 --> 00:02:57.879 align:start position:0%
simple example I'm just going to have an
 

00:02:57.879 --> 00:03:00.229 align:start position:0%
simple example I'm just going to have an
assistant<00:02:58.319><c> agent</c><00:02:58.920><c> which</c><00:02:59.080><c> I</c><00:02:59.239><c> pass</c><00:02:59.440><c> in</c><00:02:59.560><c> the</c><00:02:59.840><c> name</c>

00:03:00.229 --> 00:03:00.239 align:start position:0%
assistant agent which I pass in the name
 

00:03:00.239 --> 00:03:02.270 align:start position:0%
assistant agent which I pass in the name
and<00:03:00.400><c> the</c><00:03:00.519><c> lolm</c><00:03:01.120><c> config</c><00:03:01.640><c> that</c><00:03:01.760><c> we</c><00:03:01.920><c> created</c>

00:03:02.270 --> 00:03:02.280 align:start position:0%
and the lolm config that we created
 

00:03:02.280 --> 00:03:04.070 align:start position:0%
and the lolm config that we created
above<00:03:02.640><c> for</c><00:03:02.800><c> the</c><00:03:02.879><c> user</c><00:03:03.239><c> proxy</c><00:03:03.599><c> agent</c><00:03:03.879><c> we</c><00:03:04.000><c> give</c>

00:03:04.070 --> 00:03:04.080 align:start position:0%
above for the user proxy agent we give
 

00:03:04.080 --> 00:03:05.869 align:start position:0%
above for the user proxy agent we give
it<00:03:04.200><c> a</c><00:03:04.360><c> name</c><00:03:04.920><c> we</c><00:03:05.080><c> don't</c><00:03:05.319><c> really</c><00:03:05.519><c> want</c><00:03:05.680><c> it</c><00:03:05.760><c> to</c>

00:03:05.869 --> 00:03:05.879 align:start position:0%
it a name we don't really want it to
 

00:03:05.879 --> 00:03:07.350 align:start position:0%
it a name we don't really want it to
execute<00:03:06.239><c> any</c><00:03:06.360><c> code</c><00:03:06.640><c> because</c><00:03:06.879><c> in</c><00:03:07.040><c> this</c><00:03:07.200><c> case</c>

00:03:07.350 --> 00:03:07.360 align:start position:0%
execute any code because in this case
 

00:03:07.360 --> 00:03:08.630 align:start position:0%
execute any code because in this case
we're<00:03:07.480><c> just</c><00:03:07.599><c> going</c><00:03:07.680><c> to</c><00:03:07.799><c> ask</c><00:03:07.920><c> it</c><00:03:08.040><c> a</c><00:03:08.200><c> question</c>

00:03:08.630 --> 00:03:08.640 align:start position:0%
we're just going to ask it a question
 

00:03:08.640 --> 00:03:10.789 align:start position:0%
we're just going to ask it a question
the<00:03:08.760><c> human</c><00:03:09.000><c> input</c><00:03:09.319><c> mode</c><00:03:09.560><c> is</c><00:03:09.760><c> never</c><00:03:10.239><c> because</c>

00:03:10.789 --> 00:03:10.799 align:start position:0%
the human input mode is never because
 

00:03:10.799 --> 00:03:12.589 align:start position:0%
the human input mode is never because
I'm<00:03:10.959><c> not</c><00:03:11.120><c> going</c><00:03:11.239><c> to</c><00:03:11.799><c> talk</c><00:03:12.120><c> back</c><00:03:12.239><c> and</c><00:03:12.400><c> forth</c>

00:03:12.589 --> 00:03:12.599 align:start position:0%
I'm not going to talk back and forth
 

00:03:12.599 --> 00:03:14.470 align:start position:0%
I'm not going to talk back and forth
with<00:03:12.720><c> the</c><00:03:12.799><c> llm</c><00:03:13.239><c> call</c><00:03:13.599><c> whatever</c><00:03:14.000><c> happens</c>

00:03:14.470 --> 00:03:14.480 align:start position:0%
with the llm call whatever happens
 

00:03:14.480 --> 00:03:15.509 align:start position:0%
with the llm call whatever happens
that's<00:03:14.599><c> what</c><00:03:14.720><c> we're</c><00:03:14.799><c> going</c><00:03:14.920><c> to</c><00:03:15.000><c> use</c><00:03:15.319><c> and</c><00:03:15.440><c> then</c>

00:03:15.509 --> 00:03:15.519 align:start position:0%
that's what we're going to use and then
 

00:03:15.519 --> 00:03:16.910 align:start position:0%
that's what we're going to use and then
we<00:03:15.640><c> just</c><00:03:15.799><c> have</c><00:03:15.920><c> a</c><00:03:16.040><c> simple</c><00:03:16.360><c> termination</c>

00:03:16.910 --> 00:03:16.920 align:start position:0%
we just have a simple termination
 

00:03:16.920 --> 00:03:18.750 align:start position:0%
we just have a simple termination
message<00:03:17.239><c> then</c><00:03:17.360><c> we</c><00:03:17.480><c> say</c><00:03:17.640><c> user</c><00:03:18.040><c> proxy</c><00:03:18.480><c> do</c>

00:03:18.750 --> 00:03:18.760 align:start position:0%
message then we say user proxy do
 

00:03:18.760 --> 00:03:21.070 align:start position:0%
message then we say user proxy do
initiate<00:03:19.239><c> chat</c><00:03:19.840><c> with</c><00:03:20.040><c> the</c><00:03:20.239><c> assistant</c><00:03:20.720><c> and</c><00:03:20.840><c> the</c>

00:03:21.070 --> 00:03:21.080 align:start position:0%
initiate chat with the assistant and the
 

00:03:21.080 --> 00:03:22.750 align:start position:0%
initiate chat with the assistant and the
message<00:03:21.680><c> is</c><00:03:21.840><c> going</c><00:03:21.959><c> to</c><00:03:22.080><c> be</c><00:03:22.319><c> what</c><00:03:22.440><c> is</c><00:03:22.560><c> the</c>

00:03:22.750 --> 00:03:22.760 align:start position:0%
message is going to be what is the
 

00:03:22.760 --> 00:03:25.190 align:start position:0%
message is going to be what is the
height<00:03:22.920><c> of</c><00:03:23.040><c> the</c><00:03:23.120><c> Sears</c><00:03:23.519><c> Tower</c><00:03:24.239><c> only</c><00:03:24.640><c> respond</c>

00:03:25.190 --> 00:03:25.200 align:start position:0%
height of the Sears Tower only respond
 

00:03:25.200 --> 00:03:26.509 align:start position:0%
height of the Sears Tower only respond
with<00:03:25.360><c> the</c><00:03:25.480><c> answer</c><00:03:25.760><c> and</c><00:03:25.879><c> then</c><00:03:26.000><c> terminate</c><00:03:26.440><c> and</c>

00:03:26.509 --> 00:03:26.519 align:start position:0%
with the answer and then terminate and
 

00:03:26.519 --> 00:03:28.270 align:start position:0%
with the answer and then terminate and
then<00:03:26.640><c> after</c><00:03:26.879><c> initiate</c><00:03:27.239><c> chats</c><00:03:27.480><c> over</c><00:03:27.959><c> we</c><00:03:28.120><c> just</c>

00:03:28.270 --> 00:03:28.280 align:start position:0%
then after initiate chats over we just
 

00:03:28.280 --> 00:03:30.670 align:start position:0%
then after initiate chats over we just
simply<00:03:28.560><c> call</c><00:03:28.799><c> autogen</c><00:03:29.760><c> runtime</c><00:03:30.120><c> logging</c><00:03:30.439><c> do</c>

00:03:30.670 --> 00:03:30.680 align:start position:0%
simply call autogen runtime logging do
 

00:03:30.680 --> 00:03:32.589 align:start position:0%
simply call autogen runtime logging do
stop<00:03:31.120><c> to</c><00:03:31.360><c> end</c><00:03:31.560><c> the</c><00:03:31.720><c> logging</c><00:03:32.200><c> and</c><00:03:32.319><c> now</c><00:03:32.480><c> you</c>

00:03:32.589 --> 00:03:32.599 align:start position:0%
stop to end the logging and now you
 

00:03:32.599 --> 00:03:34.509 align:start position:0%
stop to end the logging and now you
could<00:03:32.799><c> stop</c><00:03:33.120><c> here</c><00:03:33.560><c> and</c><00:03:33.720><c> you</c><00:03:33.799><c> can</c><00:03:34.000><c> skip</c><00:03:34.280><c> ahead</c>

00:03:34.509 --> 00:03:34.519 align:start position:0%
could stop here and you can skip ahead
 

00:03:34.519 --> 00:03:36.750 align:start position:0%
could stop here and you can skip ahead
to<00:03:34.720><c> where</c><00:03:34.840><c> I</c><00:03:34.959><c> talk</c><00:03:35.159><c> about</c><00:03:35.319><c> SQL</c><00:03:35.680><c> light</c><00:03:35.920><c> browser</c>

00:03:36.750 --> 00:03:36.760 align:start position:0%
to where I talk about SQL light browser
 

00:03:36.760 --> 00:03:38.190 align:start position:0%
to where I talk about SQL light browser
but<00:03:36.920><c> I'm</c><00:03:37.000><c> going</c><00:03:37.159><c> to</c><00:03:37.239><c> show</c><00:03:37.400><c> you</c><00:03:37.560><c> the</c><00:03:37.760><c> code</c><00:03:38.040><c> to</c>

00:03:38.190 --> 00:03:38.200 align:start position:0%
but I'm going to show you the code to
 

00:03:38.200 --> 00:03:39.910 align:start position:0%
but I'm going to show you the code to
use<00:03:38.519><c> pandas</c><00:03:39.000><c> so</c><00:03:39.159><c> we</c><00:03:39.239><c> can</c><00:03:39.360><c> see</c><00:03:39.560><c> it</c><00:03:39.680><c> in</c><00:03:39.799><c> the</c>

00:03:39.910 --> 00:03:39.920 align:start position:0%
use pandas so we can see it in the
 

00:03:39.920 --> 00:03:41.470 align:start position:0%
use pandas so we can see it in the
terminal<00:03:40.480><c> what</c><00:03:40.599><c> I'm</c><00:03:40.720><c> first</c><00:03:40.840><c> going</c><00:03:40.959><c> to</c><00:03:41.080><c> do</c><00:03:41.280><c> is</c>

00:03:41.470 --> 00:03:41.480 align:start position:0%
terminal what I'm first going to do is
 

00:03:41.480 --> 00:03:44.149 align:start position:0%
terminal what I'm first going to do is
just<00:03:41.640><c> have</c><00:03:41.799><c> a</c><00:03:42.040><c> function</c><00:03:42.680><c> to</c><00:03:43.000><c> get</c><00:03:43.200><c> the</c><00:03:43.360><c> logs</c><00:03:44.080><c> I'm</c>

00:03:44.149 --> 00:03:44.159 align:start position:0%
just have a function to get the logs I'm
 

00:03:44.159 --> 00:03:46.270 align:start position:0%
just have a function to get the logs I'm
going<00:03:44.280><c> to</c><00:03:44.480><c> pass</c><00:03:44.640><c> in</c><00:03:44.879><c> the</c><00:03:45.000><c> DB</c><00:03:45.360><c> Name</c><00:03:45.840><c> by</c><00:03:45.959><c> default</c>

00:03:46.270 --> 00:03:46.280 align:start position:0%
going to pass in the DB Name by default
 

00:03:46.280 --> 00:03:48.429 align:start position:0%
going to pass in the DB Name by default
it's<00:03:46.400><c> going</c><00:03:46.519><c> to</c><00:03:46.599><c> be</c><00:03:46.760><c> logs.</c><00:03:47.439><c> DB</c><00:03:47.959><c> that</c><00:03:48.200><c> we</c><00:03:48.319><c> are</c>

00:03:48.429 --> 00:03:48.439 align:start position:0%
it's going to be logs. DB that we are
 

00:03:48.439 --> 00:03:50.110 align:start position:0%
it's going to be logs. DB that we are
going<00:03:48.519><c> to</c><00:03:48.640><c> be</c><00:03:48.760><c> creating</c><00:03:49.560><c> and</c><00:03:49.640><c> then</c><00:03:49.799><c> there</c><00:03:49.920><c> are</c>

00:03:50.110 --> 00:03:50.120 align:start position:0%
going to be creating and then there are
 

00:03:50.120 --> 00:03:51.589 align:start position:0%
going to be creating and then there are
several<00:03:50.439><c> tables</c><00:03:50.720><c> in</c><00:03:50.879><c> there</c><00:03:51.080><c> but</c><00:03:51.200><c> the</c><00:03:51.280><c> one</c><00:03:51.439><c> we</c>

00:03:51.589 --> 00:03:51.599 align:start position:0%
several tables in there but the one we
 

00:03:51.599 --> 00:03:53.509 align:start position:0%
several tables in there but the one we
care<00:03:51.799><c> about</c><00:03:52.040><c> is</c><00:03:52.239><c> chat</c><00:03:52.560><c> completions</c><00:03:53.319><c> I'm</c><00:03:53.400><c> going</c>

00:03:53.509 --> 00:03:53.519 align:start position:0%
care about is chat completions I'm going
 

00:03:53.519 --> 00:03:54.869 align:start position:0%
care about is chat completions I'm going
to<00:03:53.560><c> use</c><00:03:53.720><c> SQL</c><00:03:53.959><c> light</c><00:03:54.200><c> to</c><00:03:54.400><c> connect</c><00:03:54.680><c> to</c><00:03:54.760><c> the</c>

00:03:54.869 --> 00:03:54.879 align:start position:0%
to use SQL light to connect to the
 

00:03:54.879 --> 00:03:56.910 align:start position:0%
to use SQL light to connect to the
database<00:03:55.560><c> have</c><00:03:55.720><c> a</c><00:03:55.879><c> select</c><00:03:56.200><c> statement</c><00:03:56.680><c> from</c>

00:03:56.910 --> 00:03:56.920 align:start position:0%
database have a select statement from
 

00:03:56.920 --> 00:03:58.710 align:start position:0%
database have a select statement from
that<00:03:57.079><c> table</c><00:03:57.360><c> to</c><00:03:57.519><c> retrieve</c><00:03:57.920><c> information</c>

00:03:58.710 --> 00:03:58.720 align:start position:0%
that table to retrieve information
 

00:03:58.720 --> 00:04:00.869 align:start position:0%
that table to retrieve information
execute<00:03:59.120><c> that</c><00:03:59.280><c> query</c><00:03:59.840><c> fetch</c><00:04:00.159><c> all</c><00:04:00.360><c> the</c><00:04:00.480><c> rows</c><00:04:00.720><c> of</c>

00:04:00.869 --> 00:04:00.879 align:start position:0%
execute that query fetch all the rows of
 

00:04:00.879 --> 00:04:02.429 align:start position:0%
execute that query fetch all the rows of
data<00:04:01.319><c> I'm</c><00:04:01.400><c> going</c><00:04:01.480><c> to</c><00:04:01.640><c> gather</c><00:04:01.879><c> all</c><00:04:02.000><c> the</c><00:04:02.120><c> column</c>

00:04:02.429 --> 00:04:02.439 align:start position:0%
data I'm going to gather all the column
 

00:04:02.439 --> 00:04:04.710 align:start position:0%
data I'm going to gather all the column
names<00:04:03.079><c> zip</c><00:04:03.360><c> them</c><00:04:03.519><c> into</c><00:04:03.920><c> objects</c><00:04:04.480><c> make</c><00:04:04.599><c> a</c>

00:04:04.710 --> 00:04:04.720 align:start position:0%
names zip them into objects make a
 

00:04:04.720 --> 00:04:06.190 align:start position:0%
names zip them into objects make a
dictionary<00:04:05.159><c> out</c><00:04:05.280><c> of</c><00:04:05.400><c> it</c><00:04:05.680><c> basically</c><00:04:06.040><c> this</c>

00:04:06.190 --> 00:04:06.200 align:start position:0%
dictionary out of it basically this
 

00:04:06.200 --> 00:04:07.429 align:start position:0%
dictionary out of it basically this
function<00:04:06.439><c> is</c><00:04:06.519><c> going</c><00:04:06.640><c> to</c><00:04:06.720><c> get</c><00:04:06.879><c> all</c><00:04:07.000><c> the</c><00:04:07.079><c> rows</c><00:04:07.319><c> of</c>

00:04:07.429 --> 00:04:07.439 align:start position:0%
function is going to get all the rows of
 

00:04:07.439 --> 00:04:09.270 align:start position:0%
function is going to get all the rows of
data<00:04:07.680><c> from</c><00:04:07.799><c> the</c><00:04:07.920><c> chat</c><00:04:08.159><c> completions</c><00:04:08.720><c> table</c><00:04:09.159><c> in</c>

00:04:09.270 --> 00:04:09.280 align:start position:0%
data from the chat completions table in
 

00:04:09.280 --> 00:04:11.030 align:start position:0%
data from the chat completions table in
the<00:04:09.439><c> database</c><00:04:10.120><c> and</c><00:04:10.280><c> on</c><00:04:10.400><c> a</c><00:04:10.519><c> side</c><00:04:10.720><c> note</c><00:04:10.920><c> this</c>

00:04:11.030 --> 00:04:11.040 align:start position:0%
the database and on a side note this
 

00:04:11.040 --> 00:04:12.470 align:start position:0%
the database and on a side note this
will<00:04:11.159><c> all</c><00:04:11.319><c> be</c><00:04:11.439><c> on</c><00:04:11.519><c> my</c><00:04:11.680><c> GitHub</c><00:04:12.040><c> so</c><00:04:12.159><c> that</c><00:04:12.239><c> you</c><00:04:12.319><c> can</c>

00:04:12.470 --> 00:04:12.480 align:start position:0%
will all be on my GitHub so that you can
 

00:04:12.480 --> 00:04:13.869 align:start position:0%
will all be on my GitHub so that you can
use<00:04:12.680><c> this</c><00:04:12.840><c> as</c><00:04:12.959><c> well</c><00:04:13.319><c> we're</c><00:04:13.480><c> going</c><00:04:13.560><c> to</c><00:04:13.680><c> call</c>

00:04:13.869 --> 00:04:13.879 align:start position:0%
use this as well we're going to call
 

00:04:13.879 --> 00:04:15.670 align:start position:0%
use this as well we're going to call
that<00:04:14.040><c> function</c><00:04:14.319><c> store</c><00:04:14.680><c> into</c><00:04:14.879><c> a</c><00:04:15.040><c> log</c><00:04:15.319><c> data</c>

00:04:15.670 --> 00:04:15.680 align:start position:0%
that function store into a log data
 

00:04:15.680 --> 00:04:18.390 align:start position:0%
that function store into a log data
variable<00:04:16.400><c> convert</c><00:04:16.840><c> that</c><00:04:17.000><c> to</c><00:04:17.280><c> a</c><00:04:17.519><c> data</c><00:04:17.840><c> frame</c>

00:04:18.390 --> 00:04:18.400 align:start position:0%
variable convert that to a data frame
 

00:04:18.400 --> 00:04:21.189 align:start position:0%
variable convert that to a data frame
we're<00:04:18.600><c> going</c><00:04:18.759><c> to</c><00:04:19.079><c> add</c><00:04:19.359><c> a</c><00:04:19.560><c> total</c><00:04:19.919><c> tokens</c><00:04:20.479><c> column</c>

00:04:21.189 --> 00:04:21.199 align:start position:0%
we're going to add a total tokens column
 

00:04:21.199 --> 00:04:23.150 align:start position:0%
we're going to add a total tokens column
based<00:04:21.479><c> on</c><00:04:21.560><c> the</c><00:04:21.759><c> response</c><00:04:22.440><c> and</c><00:04:22.560><c> then</c><00:04:22.680><c> for</c><00:04:22.880><c> the</c>

00:04:23.150 --> 00:04:23.160 align:start position:0%
based on the response and then for the
 

00:04:23.160 --> 00:04:25.030 align:start position:0%
based on the response and then for the
request<00:04:23.840><c> based</c><00:04:24.080><c> on</c><00:04:24.199><c> the</c><00:04:24.360><c> request</c><00:04:24.800><c> we're</c><00:04:24.960><c> going</c>

00:04:25.030 --> 00:04:25.040 align:start position:0%
request based on the request we're going
 

00:04:25.040 --> 00:04:27.629 align:start position:0%
request based on the request we're going
to<00:04:25.280><c> get</c><00:04:25.520><c> the</c><00:04:25.800><c> content</c><00:04:26.400><c> or</c><00:04:26.560><c> the</c><00:04:26.720><c> chat</c><00:04:27.320><c> and</c><00:04:27.440><c> then</c>

00:04:27.629 --> 00:04:27.639 align:start position:0%
to get the content or the chat and then
 

00:04:27.639 --> 00:04:29.189 align:start position:0%
to get the content or the chat and then
also<00:04:27.880><c> for</c><00:04:28.080><c> the</c><00:04:28.360><c> response</c><00:04:28.800><c> we're</c><00:04:28.960><c> also</c><00:04:29.120><c> going</c>

00:04:29.189 --> 00:04:29.199 align:start position:0%
also for the response we're also going
 

00:04:29.199 --> 00:04:31.550 align:start position:0%
also for the response we're also going
to<00:04:29.320><c> get</c><00:04:29.400><c> the</c><00:04:29.680><c> specific</c><00:04:30.199><c> content</c><00:04:30.919><c> from</c><00:04:31.320><c> the</c>

00:04:31.550 --> 00:04:31.560 align:start position:0%
to get the specific content from the
 

00:04:31.560 --> 00:04:33.749 align:start position:0%
to get the specific content from the
response<00:04:32.080><c> of</c><00:04:32.199><c> the</c><00:04:32.320><c> llm</c><00:04:32.880><c> as</c><00:04:33.039><c> well</c><00:04:33.479><c> now</c><00:04:33.600><c> let's</c>

00:04:33.749 --> 00:04:33.759 align:start position:0%
response of the llm as well now let's
 

00:04:33.759 --> 00:04:35.310 align:start position:0%
response of the llm as well now let's
just<00:04:33.880><c> run</c><00:04:34.039><c> this</c><00:04:34.160><c> to</c><00:04:34.280><c> look</c><00:04:34.400><c> at</c><00:04:34.520><c> an</c><00:04:34.680><c> example</c><00:04:35.199><c> here</c>

00:04:35.310 --> 00:04:35.320 align:start position:0%
just run this to look at an example here
 

00:04:35.320 --> 00:04:37.189 align:start position:0%
just run this to look at an example here
is<00:04:35.520><c> started</c><00:04:35.800><c> the</c><00:04:35.919><c> logging</c><00:04:36.280><c> session</c><00:04:36.720><c> ID</c><00:04:37.080><c> that</c>

00:04:37.189 --> 00:04:37.199 align:start position:0%
is started the logging session ID that
 

00:04:37.199 --> 00:04:38.790 align:start position:0%
is started the logging session ID that
we<00:04:37.320><c> printed</c><00:04:37.759><c> above</c><00:04:38.039><c> when</c><00:04:38.160><c> we</c><00:04:38.320><c> started</c><00:04:38.639><c> the</c>

00:04:38.790 --> 00:04:38.800 align:start position:0%
we printed above when we started the
 

00:04:38.800 --> 00:04:40.870 align:start position:0%
we printed above when we started the
logging<00:04:39.680><c> we</c><00:04:39.880><c> asked</c><00:04:40.120><c> it</c><00:04:40.240><c> the</c><00:04:40.400><c> question</c><00:04:40.600><c> of</c><00:04:40.759><c> the</c>

00:04:40.870 --> 00:04:40.880 align:start position:0%
logging we asked it the question of the
 

00:04:40.880 --> 00:04:43.350 align:start position:0%
logging we asked it the question of the
height<00:04:41.039><c> of</c><00:04:41.120><c> the</c><00:04:41.240><c> Sears</c><00:04:41.600><c> Tower</c><00:04:42.280><c> it</c><00:04:42.479><c> responded</c>

00:04:43.350 --> 00:04:43.360 align:start position:0%
height of the Sears Tower it responded
 

00:04:43.360 --> 00:04:45.310 align:start position:0%
height of the Sears Tower it responded
that<00:04:43.520><c> is</c><00:04:43.720><c> now</c><00:04:44.000><c> known</c><00:04:44.440><c> as</c><00:04:44.560><c> the</c><00:04:44.680><c> Willis</c><00:04:45.039><c> Tower</c>

00:04:45.310 --> 00:04:45.320 align:start position:0%
that is now known as the Willis Tower
 

00:04:45.320 --> 00:04:48.110 align:start position:0%
that is now known as the Willis Tower
and<00:04:45.400><c> it's</c><00:04:45.600><c> 1450</c><00:04:46.400><c> ft</c><00:04:46.680><c> tall</c><00:04:47.160><c> now</c><00:04:47.520><c> what</c><00:04:47.639><c> we</c><00:04:47.800><c> did</c><00:04:48.000><c> is</c>

00:04:48.110 --> 00:04:48.120 align:start position:0%
and it's 1450 ft tall now what we did is
 

00:04:48.120 --> 00:04:49.950 align:start position:0%
and it's 1450 ft tall now what we did is
there's<00:04:48.320><c> only</c><00:04:48.600><c> one</c><00:04:48.840><c> row</c><00:04:49.039><c> of</c><00:04:49.199><c> data</c><00:04:49.560><c> in</c><00:04:49.759><c> this</c>

00:04:49.950 --> 00:04:49.960 align:start position:0%
there's only one row of data in this
 

00:04:49.960 --> 00:04:52.710 align:start position:0%
there's only one row of data in this
database<00:04:50.800><c> and</c><00:04:50.919><c> there's</c><00:04:51.240><c> six</c><00:04:51.560><c> columns</c><00:04:52.000><c> total</c>

00:04:52.710 --> 00:04:52.720 align:start position:0%
database and there's six columns total
 

00:04:52.720 --> 00:04:54.469 align:start position:0%
database and there's six columns total
we<00:04:52.880><c> have</c><00:04:53.199><c> an</c><00:04:53.360><c> ID</c><00:04:53.720><c> you</c><00:04:53.840><c> can't</c><00:04:54.039><c> see</c><00:04:54.240><c> that's</c><00:04:54.400><c> what</c>

00:04:54.469 --> 00:04:54.479 align:start position:0%
we have an ID you can't see that's what
 

00:04:54.479 --> 00:04:56.590 align:start position:0%
we have an ID you can't see that's what
this<00:04:54.680><c> zero</c><00:04:55.080><c> is</c><00:04:55.840><c> uh</c><00:04:55.919><c> the</c><00:04:56.080><c> requests</c><00:04:56.400><c> and</c>

00:04:56.590 --> 00:04:56.600 align:start position:0%
this zero is uh the requests and
 

00:04:56.600 --> 00:04:58.710 align:start position:0%
this zero is uh the requests and
responses<00:04:57.039><c> are</c><00:04:57.320><c> pretty</c><00:04:57.639><c> long</c><00:04:58.240><c> so</c><00:04:58.440><c> we're</c><00:04:58.560><c> not</c>

00:04:58.710 --> 00:04:58.720 align:start position:0%
responses are pretty long so we're not
 

00:04:58.720 --> 00:05:01.029 align:start position:0%
responses are pretty long so we're not
seeing<00:04:59.120><c> everything</c><00:04:59.680><c> in</c><00:04:59.800><c> the</c><00:04:59.919><c> terminal</c><00:05:00.759><c> but</c><00:05:00.919><c> at</c>

00:05:01.029 --> 00:05:01.039 align:start position:0%
seeing everything in the terminal but at
 

00:05:01.039 --> 00:05:03.830 align:start position:0%
seeing everything in the terminal but at
the<00:05:01.160><c> end</c><00:05:01.479><c> we</c><00:05:01.840><c> have</c><00:05:02.440><c> the</c><00:05:02.680><c> end</c><00:05:03.000><c> time</c><00:05:03.520><c> and</c><00:05:03.680><c> then</c>

00:05:03.830 --> 00:05:03.840 align:start position:0%
the end we have the end time and then
 

00:05:03.840 --> 00:05:05.909 align:start position:0%
the end we have the end time and then
the<00:05:04.000><c> total</c><00:05:04.360><c> tokens</c><00:05:05.320><c> this</c><00:05:05.440><c> is</c><00:05:05.560><c> the</c><00:05:05.680><c> total</c>

00:05:05.909 --> 00:05:05.919 align:start position:0%
the total tokens this is the total
 

00:05:05.919 --> 00:05:08.029 align:start position:0%
the total tokens this is the total
tokens<00:05:06.600><c> a</c><00:05:06.800><c> round</c><00:05:07.080><c> trip</c><00:05:07.320><c> that</c><00:05:07.400><c> it</c><00:05:07.600><c> cost</c><00:05:07.840><c> for</c><00:05:07.960><c> the</c>

00:05:08.029 --> 00:05:08.039 align:start position:0%
tokens a round trip that it cost for the
 

00:05:08.039 --> 00:05:10.110 align:start position:0%
tokens a round trip that it cost for the
llm<00:05:08.440><c> call</c><00:05:08.840><c> that's</c><00:05:09.000><c> pretty</c><00:05:09.199><c> cool</c><00:05:09.680><c> but</c><00:05:09.880><c> now</c>

00:05:10.110 --> 00:05:10.120 align:start position:0%
llm call that's pretty cool but now
 

00:05:10.120 --> 00:05:12.070 align:start position:0%
llm call that's pretty cool but now
let's<00:05:10.280><c> look</c><00:05:10.400><c> at</c><00:05:10.520><c> the</c><00:05:10.759><c> actual</c><00:05:11.120><c> tables</c><00:05:11.479><c> of</c><00:05:11.680><c> SQL</c>

00:05:12.070 --> 00:05:12.080 align:start position:0%
let's look at the actual tables of SQL
 

00:05:12.080 --> 00:05:13.629 align:start position:0%
let's look at the actual tables of SQL
light<00:05:12.320><c> browser</c><00:05:12.840><c> all</c><00:05:12.919><c> you</c><00:05:13.000><c> need</c><00:05:13.120><c> to</c><00:05:13.240><c> do</c><00:05:13.440><c> is</c><00:05:13.560><c> go</c>

00:05:13.629 --> 00:05:13.639 align:start position:0%
light browser all you need to do is go
 

00:05:13.639 --> 00:05:15.870 align:start position:0%
light browser all you need to do is go
to<00:05:13.759><c> sqlite</c><00:05:14.240><c> browser.</c><00:05:14.919><c> org</c><00:05:15.199><c> and</c><00:05:15.320><c> go</c><00:05:15.520><c> to</c><00:05:15.680><c> the</c>

00:05:15.870 --> 00:05:15.880 align:start position:0%
to sqlite browser. org and go to the
 

00:05:15.880 --> 00:05:17.670 align:start position:0%
to sqlite browser. org and go to the
download<00:05:16.360><c> section</c><00:05:16.800><c> and</c><00:05:16.919><c> then</c><00:05:17.160><c> download</c><00:05:17.560><c> the</c>

00:05:17.670 --> 00:05:17.680 align:start position:0%
download section and then download the
 

00:05:17.680 --> 00:05:19.710 align:start position:0%
download section and then download the
one<00:05:18.039><c> for</c><00:05:18.280><c> your</c><00:05:18.520><c> machine</c><00:05:19.160><c> once</c><00:05:19.280><c> you've</c><00:05:19.479><c> done</c>

00:05:19.710 --> 00:05:19.720 align:start position:0%
one for your machine once you've done
 

00:05:19.720 --> 00:05:21.590 align:start position:0%
one for your machine once you've done
that<00:05:20.000><c> just</c><00:05:20.199><c> simply</c><00:05:20.560><c> run</c><00:05:20.800><c> it</c><00:05:21.199><c> and</c><00:05:21.319><c> then</c><00:05:21.479><c> when</c>

00:05:21.590 --> 00:05:21.600 align:start position:0%
that just simply run it and then when
 

00:05:21.600 --> 00:05:23.710 align:start position:0%
that just simply run it and then when
you<00:05:21.800><c> come</c><00:05:22.039><c> to</c><00:05:22.400><c> this</c><00:05:22.600><c> screen</c><00:05:23.319><c> you</c><00:05:23.520><c> want</c><00:05:23.600><c> to</c>

00:05:23.710 --> 00:05:23.720 align:start position:0%
you come to this screen you want to
 

00:05:23.720 --> 00:05:25.830 align:start position:0%
you come to this screen you want to
click<00:05:24.000><c> open</c><00:05:24.360><c> database</c><00:05:25.080><c> and</c><00:05:25.199><c> then</c><00:05:25.360><c> here's</c><00:05:25.600><c> our</c>

00:05:25.830 --> 00:05:25.840 align:start position:0%
click open database and then here's our
 

00:05:25.840 --> 00:05:28.029 align:start position:0%
click open database and then here's our
logs.<00:05:26.639><c> database</c><00:05:27.160><c> now</c><00:05:27.280><c> let's</c><00:05:27.479><c> just</c><00:05:27.680><c> open</c><00:05:27.880><c> this</c>

00:05:28.029 --> 00:05:28.039 align:start position:0%
logs. database now let's just open this
 

00:05:28.039 --> 00:05:29.550 align:start position:0%
logs. database now let's just open this
up<00:05:28.199><c> and</c><00:05:28.319><c> I'll</c><00:05:28.440><c> zoom</c><00:05:28.680><c> in</c><00:05:28.800><c> so</c><00:05:28.960><c> this</c><00:05:29.080><c> is</c><00:05:29.160><c> a</c><00:05:29.240><c> little</c>

00:05:29.550 --> 00:05:29.560 align:start position:0%
up and I'll zoom in so this is a little
 

00:05:29.560 --> 00:05:31.550 align:start position:0%
up and I'll zoom in so this is a little
easier<00:05:29.800><c> to</c><00:05:29.919><c> see</c><00:05:30.280><c> but</c><00:05:30.400><c> changing</c><00:05:30.840><c> the</c><00:05:30.960><c> font</c><00:05:31.280><c> size</c>

00:05:31.550 --> 00:05:31.560 align:start position:0%
easier to see but changing the font size
 

00:05:31.560 --> 00:05:33.189 align:start position:0%
easier to see but changing the font size
kind<00:05:31.680><c> of</c><00:05:31.800><c> makes</c><00:05:32.039><c> this</c><00:05:32.240><c> wonky</c><00:05:32.680><c> and</c><00:05:32.840><c> weird</c><00:05:33.080><c> to</c>

00:05:33.189 --> 00:05:33.199 align:start position:0%
kind of makes this wonky and weird to
 

00:05:33.199 --> 00:05:35.350 align:start position:0%
kind of makes this wonky and weird to
look<00:05:33.360><c> at</c><00:05:33.960><c> but</c><00:05:34.120><c> it</c><00:05:34.319><c> has</c><00:05:34.639><c> five</c><00:05:34.840><c> tables</c><00:05:35.199><c> here</c>

00:05:35.350 --> 00:05:35.360 align:start position:0%
look at but it has five tables here
 

00:05:35.360 --> 00:05:36.430 align:start position:0%
look at but it has five tables here
we're<00:05:35.479><c> going</c><00:05:35.600><c> to</c><00:05:35.720><c> look</c><00:05:35.840><c> at</c><00:05:36.000><c> the</c><00:05:36.160><c> chat</c>

00:05:36.430 --> 00:05:36.440 align:start position:0%
we're going to look at the chat
 

00:05:36.440 --> 00:05:38.469 align:start position:0%
we're going to look at the chat
completions<00:05:37.039><c> table</c><00:05:37.560><c> and</c><00:05:37.720><c> now</c><00:05:37.880><c> you</c><00:05:37.960><c> can</c><00:05:38.120><c> see</c>

00:05:38.469 --> 00:05:38.479 align:start position:0%
completions table and now you can see
 

00:05:38.479 --> 00:05:40.270 align:start position:0%
completions table and now you can see
all<00:05:38.639><c> of</c><00:05:38.759><c> the</c><00:05:38.960><c> columns</c><00:05:39.479><c> that</c><00:05:39.600><c> it</c><00:05:39.880><c> actually</c>

00:05:40.270 --> 00:05:40.280 align:start position:0%
all of the columns that it actually
 

00:05:40.280 --> 00:05:42.150 align:start position:0%
all of the columns that it actually
stores<00:05:40.840><c> which</c><00:05:40.960><c> is</c><00:05:41.120><c> more</c><00:05:41.319><c> than</c><00:05:41.520><c> what</c><00:05:41.680><c> I</c><00:05:41.800><c> showed</c>

00:05:42.150 --> 00:05:42.160 align:start position:0%
stores which is more than what I showed
 

00:05:42.160 --> 00:05:43.990 align:start position:0%
stores which is more than what I showed
on<00:05:42.319><c> the</c><00:05:42.520><c> Panda's</c><00:05:42.960><c> data</c><00:05:43.199><c> frames</c><00:05:43.639><c> so</c><00:05:43.800><c> we</c><00:05:43.880><c> have</c>

00:05:43.990 --> 00:05:44.000 align:start position:0%
on the Panda's data frames so we have
 

00:05:44.000 --> 00:05:45.469 align:start position:0%
on the Panda's data frames so we have
the<00:05:44.120><c> session</c><00:05:44.440><c> ID</c><00:05:44.759><c> that</c><00:05:44.840><c> we</c><00:05:44.960><c> started</c><00:05:45.240><c> with</c><00:05:45.400><c> and</c>

00:05:45.469 --> 00:05:45.479 align:start position:0%
the session ID that we started with and
 

00:05:45.479 --> 00:05:46.790 align:start position:0%
the session ID that we started with and
that's<00:05:45.600><c> the</c><00:05:45.680><c> one</c><00:05:45.759><c> we</c><00:05:45.880><c> actually</c><00:05:46.039><c> printed</c><00:05:46.360><c> out</c>

00:05:46.790 --> 00:05:46.800 align:start position:0%
that's the one we actually printed out
 

00:05:46.800 --> 00:05:49.150 align:start position:0%
that's the one we actually printed out
the<00:05:46.919><c> full</c><00:05:47.280><c> request</c><00:05:47.800><c> so</c><00:05:48.000><c> if</c><00:05:48.120><c> you</c><00:05:48.360><c> use</c><00:05:48.720><c> this</c><00:05:49.039><c> and</c>

00:05:49.150 --> 00:05:49.160 align:start position:0%
the full request so if you use this and
 

00:05:49.160 --> 00:05:50.870 align:start position:0%
the full request so if you use this and
you<00:05:49.319><c> click</c><00:05:49.520><c> on</c><00:05:49.639><c> it</c><00:05:49.880><c> over</c><00:05:50.080><c> on</c><00:05:50.199><c> the</c><00:05:50.400><c> right</c><00:05:50.639><c> hand</c>

00:05:50.870 --> 00:05:50.880 align:start position:0%
you click on it over on the right hand
 

00:05:50.880 --> 00:05:53.390 align:start position:0%
you click on it over on the right hand
side<00:05:51.199><c> you</c><00:05:51.280><c> can</c><00:05:51.440><c> see</c><00:05:51.759><c> all</c><00:05:52.000><c> the</c><00:05:52.160><c> actual</c><00:05:52.479><c> Json</c>

00:05:53.390 --> 00:05:53.400 align:start position:0%
side you can see all the actual Json
 

00:05:53.400 --> 00:05:55.350 align:start position:0%
side you can see all the actual Json
that<00:05:53.720><c> we</c><00:05:53.880><c> used</c><00:05:54.479><c> and</c><00:05:54.600><c> then</c><00:05:54.759><c> in</c><00:05:54.880><c> the</c><00:05:55.000><c> response</c>

00:05:55.350 --> 00:05:55.360 align:start position:0%
that we used and then in the response
 

00:05:55.360 --> 00:05:57.070 align:start position:0%
that we used and then in the response
you<00:05:55.440><c> can</c><00:05:55.560><c> see</c><00:05:55.800><c> the</c><00:05:56.080><c> content</c><00:05:56.479><c> here</c><00:05:56.680><c> this</c><00:05:56.759><c> is</c><00:05:56.919><c> the</c>

00:05:57.070 --> 00:05:57.080 align:start position:0%
you can see the content here this is the
 

00:05:57.080 --> 00:05:59.270 align:start position:0%
you can see the content here this is the
actual<00:05:57.360><c> answer</c><00:05:57.680><c> we</c><00:05:57.800><c> got</c><00:05:58.080><c> back</c><00:05:58.240><c> from</c><00:05:58.400><c> the</c><00:05:58.520><c> llm</c>

00:05:59.270 --> 00:05:59.280 align:start position:0%
actual answer we got back from the llm
 

00:05:59.280 --> 00:06:00.629 align:start position:0%
actual answer we got back from the llm
and<00:05:59.440><c> and</c><00:05:59.560><c> then</c><00:05:59.680><c> you</c><00:05:59.759><c> can</c><00:05:59.880><c> see</c><00:06:00.199><c> the</c><00:06:00.319><c> total</c>

00:06:00.629 --> 00:06:00.639 align:start position:0%
and and then you can see the total
 

00:06:00.639 --> 00:06:03.469 align:start position:0%
and and then you can see the total
tokens<00:06:01.000><c> was</c><00:06:01.240><c> 5115</c><00:06:02.240><c> The</c><00:06:02.440><c> Prompt</c><00:06:02.960><c> itself</c><00:06:03.280><c> was</c>

00:06:03.469 --> 00:06:03.479 align:start position:0%
tokens was 5115 The Prompt itself was
 

00:06:03.479 --> 00:06:06.070 align:start position:0%
tokens was 5115 The Prompt itself was
488<00:06:04.319><c> and</c><00:06:04.440><c> then</c><00:06:04.560><c> the</c><00:06:04.759><c> completion</c><00:06:05.720><c> which</c><00:06:05.880><c> was</c>

00:06:06.070 --> 00:06:06.080 align:start position:0%
488 and then the completion which was
 

00:06:06.080 --> 00:06:08.350 align:start position:0%
488 and then the completion which was
the<00:06:06.199><c> actual</c><00:06:06.520><c> response</c><00:06:06.840><c> was</c><00:06:06.960><c> only</c><00:06:07.160><c> 27</c><00:06:08.080><c> and</c><00:06:08.199><c> then</c>

00:06:08.350 --> 00:06:08.360 align:start position:0%
the actual response was only 27 and then
 

00:06:08.360 --> 00:06:11.110 align:start position:0%
the actual response was only 27 and then
here<00:06:08.479><c> is</c><00:06:08.680><c> the</c><00:06:08.960><c> cost</c><00:06:09.800><c> uh</c><00:06:09.960><c> is</c><00:06:10.240><c> cached</c><00:06:10.759><c> lets</c><00:06:10.960><c> you</c>

00:06:11.110 --> 00:06:11.120 align:start position:0%
here is the cost uh is cached lets you
 

00:06:11.120 --> 00:06:13.150 align:start position:0%
here is the cost uh is cached lets you
know<00:06:11.560><c> that</c><00:06:11.880><c> was</c><00:06:12.080><c> the</c><00:06:12.199><c> answer</c><00:06:12.560><c> already</c><00:06:12.840><c> cashed</c>

00:06:13.150 --> 00:06:13.160 align:start position:0%
know that was the answer already cashed
 

00:06:13.160 --> 00:06:15.990 align:start position:0%
know that was the answer already cashed
so<00:06:13.319><c> if</c><00:06:13.400><c> I</c><00:06:13.520><c> were</c><00:06:13.680><c> to</c><00:06:13.880><c> rerun</c><00:06:14.360><c> this</c><00:06:14.800><c> one</c><00:06:15.000><c> more</c><00:06:15.240><c> time</c>

00:06:15.990 --> 00:06:16.000 align:start position:0%
so if I were to rerun this one more time
 

00:06:16.000 --> 00:06:17.990 align:start position:0%
so if I were to rerun this one more time
I<00:06:16.080><c> would</c><00:06:16.240><c> have</c><00:06:16.400><c> another</c><00:06:16.759><c> row</c><00:06:17.000><c> of</c><00:06:17.160><c> data</c><00:06:17.680><c> and</c>

00:06:17.990 --> 00:06:18.000 align:start position:0%
I would have another row of data and
 

00:06:18.000 --> 00:06:19.990 align:start position:0%
I would have another row of data and
instead<00:06:18.400><c> this</c><00:06:18.599><c> value</c><00:06:18.840><c> will</c><00:06:19.000><c> be</c><00:06:19.240><c> one</c><00:06:19.639><c> for</c><00:06:19.880><c> the</c>

00:06:19.990 --> 00:06:20.000 align:start position:0%
instead this value will be one for the
 

00:06:20.000 --> 00:06:21.390 align:start position:0%
instead this value will be one for the
other<00:06:20.240><c> row</c><00:06:20.599><c> I</c><00:06:20.680><c> think</c><00:06:20.800><c> this</c><00:06:20.880><c> can</c><00:06:21.000><c> be</c><00:06:21.199><c> very</c>

00:06:21.390 --> 00:06:21.400 align:start position:0%
other row I think this can be very
 

00:06:21.400 --> 00:06:23.309 align:start position:0%
other row I think this can be very
useful<00:06:21.800><c> for</c><00:06:22.000><c> if</c><00:06:22.120><c> you</c><00:06:22.199><c> have</c><00:06:22.319><c> a</c><00:06:22.440><c> use</c><00:06:22.800><c> case</c><00:06:23.199><c> you</c>

00:06:23.309 --> 00:06:23.319 align:start position:0%
useful for if you have a use case you
 

00:06:23.319 --> 00:06:25.350 align:start position:0%
useful for if you have a use case you
can<00:06:23.560><c> determine</c><00:06:24.000><c> which</c><00:06:24.240><c> open</c><00:06:24.560><c> source</c><00:06:24.880><c> model</c><00:06:25.240><c> if</c>

00:06:25.350 --> 00:06:25.360 align:start position:0%
can determine which open source model if
 

00:06:25.360 --> 00:06:27.189 align:start position:0%
can determine which open source model if
you<00:06:25.440><c> want</c><00:06:25.560><c> to</c><00:06:25.680><c> go</c><00:06:25.880><c> that</c><00:06:26.000><c> route</c><00:06:26.680><c> that</c><00:06:26.840><c> works</c>

00:06:27.189 --> 00:06:27.199 align:start position:0%
you want to go that route that works
 

00:06:27.199 --> 00:06:29.469 align:start position:0%
you want to go that route that works
better<00:06:27.560><c> for</c><00:06:27.880><c> your</c><00:06:28.160><c> use</c><00:06:28.520><c> Case</c><00:06:28.759><c> by</c><00:06:28.919><c> goalis</c><00:06:29.199><c> month</c>

00:06:29.469 --> 00:06:29.479 align:start position:0%
better for your use Case by goalis month
 

00:06:29.479 --> 00:06:31.710 align:start position:0%
better for your use Case by goalis month
is<00:06:29.560><c> to</c><00:06:29.680><c> have</c><00:06:29.800><c> a</c><00:06:29.960><c> video</c><00:06:30.199><c> out</c><00:06:30.560><c> every</c><00:06:30.840><c> day</c><00:06:31.479><c> please</c>

00:06:31.710 --> 00:06:31.720 align:start position:0%
is to have a video out every day please
 

00:06:31.720 --> 00:06:33.790 align:start position:0%
is to have a video out every day please
follow<00:06:32.039><c> along</c><00:06:32.440><c> I</c><00:06:32.520><c> hope</c><00:06:32.680><c> you</c><00:06:32.880><c> learn</c><00:06:33.280><c> something</c>

00:06:33.790 --> 00:06:33.800 align:start position:0%
follow along I hope you learn something
 

00:06:33.800 --> 00:06:35.670 align:start position:0%
follow along I hope you learn something
I<00:06:33.919><c> have</c><00:06:34.080><c> more</c><00:06:34.240><c> videos</c><00:06:34.560><c> about</c><00:06:34.720><c> autogen</c><00:06:35.199><c> and</c><00:06:35.360><c> AI</c>

00:06:35.670 --> 00:06:35.680 align:start position:0%
I have more videos about autogen and AI
 

00:06:35.680 --> 00:06:36.990 align:start position:0%
I have more videos about autogen and AI
up<00:06:35.880><c> here</c><00:06:36.199><c> if</c><00:06:36.280><c> you</c><00:06:36.360><c> have</c><00:06:36.479><c> any</c><00:06:36.599><c> questions</c><00:06:36.840><c> or</c>

00:06:36.990 --> 00:06:37.000 align:start position:0%
up here if you have any questions or
 

00:06:37.000 --> 00:06:38.629 align:start position:0%
up here if you have any questions or
comments<00:06:37.440><c> please</c><00:06:37.680><c> leave</c><00:06:37.840><c> them</c><00:06:38.080><c> down</c><00:06:38.360><c> in</c><00:06:38.479><c> the</c>

00:06:38.629 --> 00:06:38.639 align:start position:0%
comments please leave them down in the
 

00:06:38.639 --> 00:06:40.510 align:start position:0%
comments please leave them down in the
comment<00:06:38.919><c> section</c><00:06:39.240><c> Below</c><00:06:39.919><c> have</c><00:06:40.039><c> a</c><00:06:40.160><c> great</c><00:06:40.360><c> day</c>

00:06:40.510 --> 00:06:40.520 align:start position:0%
comment section Below have a great day
 

00:06:40.520 --> 00:06:43.560 align:start position:0%
comment section Below have a great day
and<00:06:40.639><c> I'll</c><00:06:40.800><c> see</c><00:06:40.919><c> you</c><00:06:41.120><c> next</c><00:06:41.280><c> time</c>

