import os
from dotenv import load_dotenv
import yt_dlp
from datetime import datetime, timedelta, timezone
from rich.console import Console
from rich.table import Table
import logging
from typing import Dict, List, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
#logging.getLogger('yt_dlp').setLevel(logging.ERROR)  # Suppress yt_dlp warnings

# Load environment variables
load_dotenv()

CHOSEN_WATCHLIST_FILE = "youtube_channels_Watchlist_download_entire_channels.md"


"""
This script tests fetching recent YouTube videos from specified channels.

Key functionality:
- Reads channel data from a markdown file containing channel names and IDs organized by topics
- For each channel, fetches videos uploaded in the last 48 hours using yt-dlp
- Provides logging and rich console output to track the fetching process
- <PERSON>les errors gracefully and provides detailed debug information
- Can be used to verify channel accessibility and recent upload activity
- Useful for testing before running the full video processing pipeline

The script uses:
- yt-dlp for fetching video metadata from YouTube channels
- Rich library for formatted console output
- Logging for debug and error tracking
- Environment variables for configuration
- Type hints for better code clarity and IDE support

Usage:
Run the script directly to test fetching recent videos from channels listed in the
specified watchlist file (CHOSEN_WATCHLIST_FILE).
"""


def read_channel_data(file_name: str) -> Dict[str, List[Tuple[str, str]]]:
    """
    Reads channel data from a markdown file and returns a dictionary with topics and channels.

    Args:
        file_name (str): The name of the markdown file containing channel data.

    Returns:
        Dict[str, List[Tuple[str, str]]]: A dictionary mapping topics to a list of channel name and ID tuples.
    """
    script_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(script_dir, file_name)

    logging.info(f"Reading channel data from: {file_path}")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"The file {file_name} does not exist in the script directory.")

    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    channel_data = {}
    current_topic = None

    for line in lines:
        line = line.strip()
        if line.startswith('## '):
            current_topic = line[3:]
            channel_data[current_topic] = []
        elif line.startswith('- '):
            parts = line[2:].split(' - ')
            if len(parts) == 2:
                channel_name, channel_id = parts
                channel_data[current_topic].append((channel_name.strip(), channel_id.strip()))
            elif len(parts) == 1:
                channel_id = parts[0].strip()
                channel_data[current_topic].append(("N/A", channel_id))

    logging.info(f"Found {len(channel_data)} topics with {sum(len(channels) for channels in channel_data.values())} channels")
    return channel_data

def fetch_recent_videos(channel_name: str, channel_id: str) -> tuple[int, dict]:
    """
    Fetches videos from the given channel ID that were uploaded in the last 48 hours.
    """
    date_48_hours_ago = datetime.now(timezone.utc) - timedelta(hours=48)
    date_after = date_48_hours_ago.strftime('%Y%m%d')

    ydl_opts = {
        'extract_flat': 'in_playlist',
        'force_generic_extractor': False,
        'quiet': True,
        'no_warnings': True,
        'ignoreerrors': True,
        'dateafter': date_after,
        'playlistend': 40,  # Only check the 10 most recent videos
    }

    channel_url = f"https://www.youtube.com/channel/{channel_id}/videos"
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            info = ydl.extract_info(channel_url, download=False)
            if not info or 'entries' not in info:
                logging.error(f"No video information found for channel ID: {channel_id}")
                return 0, {}
            
            video_ids = []
            video_info = {}
            
            detailed_opts = {
                'quiet': True,
                'no_warnings': True,
                'ignoreerrors': True,
                'skip_download': True,
            }
            
            with yt_dlp.YoutubeDL(detailed_opts) as detailed_ydl:
                for entry in info['entries']:
                    if entry:
                        video_id = entry['id']
                        
                        try:
                            video_info_entry = detailed_ydl.extract_info(
                                f"https://www.youtube.com/watch?v={video_id}", 
                                download=False
                            )
                            
                            # Get upload date
                            if video_info_entry.get('release_timestamp'):
                                published_at = datetime.fromtimestamp(video_info_entry['release_timestamp'], tz=timezone.utc)
                            elif video_info_entry.get('timestamp'):
                                published_at = datetime.fromtimestamp(video_info_entry['timestamp'], tz=timezone.utc)
                            elif video_info_entry.get('upload_date'):
                                published_at = datetime.strptime(video_info_entry['upload_date'], '%Y%m%d').replace(tzinfo=timezone.utc)
                            else:
                                continue

                            # Break the loop if we find a video older than 48 hours
                            if published_at < date_48_hours_ago:
                                break  # Videos are in chronological order, so we can stop here

                            video_ids.append(video_id)
                            video_info[video_id] = {
                                'id': video_id,
                                'title': video_info_entry.get('title'),
                                'duration': video_info_entry.get('duration'),
                                'upload_date': published_at.strftime('%Y%m%d'),
                                'timestamp': video_info_entry.get('timestamp'),
                                'release_timestamp': video_info_entry.get('release_timestamp')
                            }
                            logging.info(f"Found recent video: {video_info_entry.get('title')} - Upload date: {published_at}")
                            
                        except Exception as e:
                            logging.error(f"Error fetching detailed info for video {video_id}: {str(e)}")
                            continue
            
            logging.info(f"Fetched {len(video_ids)} videos from the last 48 hours for channel ID: {channel_id}")
            return len(video_ids), video_info

        except Exception as e:
            logging.error(f"Error fetching videos for channel ID {channel_id}: {str(e)}")
            return 0, {}

def main():
    """
    Main function to read channel data, fetch recent videos, and display the results in a table.
    """
    console = Console()
    try:
        channel_data = read_channel_data(CHOSEN_WATCHLIST_FILE)

        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Topic", style="dim")
        table.add_column("Channel Name")
        table.add_column("Channel ID")
        table.add_column("Videos in Last 48 Hours", justify="right")
        table.add_column("Latest Video Title", justify="left")
        table.add_column("Latest Upload Date", justify="right")

        for topic, channels in channel_data.items():
            logging.info(f"Processing topic: {topic}")
            for channel_name, channel_id in channels:
                recent_video_count, video_info = fetch_recent_videos(channel_name, channel_id)
                
                # Get the latest video info if available
                latest_video_title = "N/A"
                latest_upload_date = "N/A"
                if video_info:
                    # Sort videos by upload date to find the latest
                    latest_video = max(
                        video_info.values(), 
                        key=lambda x: x['upload_date'] if x['upload_date'] else "00000000"
                    )
                    latest_video_title = latest_video['title']
                    if latest_video['upload_date']:
                        upload_date = datetime.strptime(latest_video['upload_date'], '%Y%m%d')
                        latest_upload_date = upload_date.strftime('%Y-%m-%d')

                table.add_row(
                    topic,
                    channel_name or "N/A",
                    channel_id,
                    str(recent_video_count),
                    latest_video_title[:50] + "..." if len(latest_video_title) > 50 else latest_video_title,
                    latest_upload_date
                )

        console.print(table)
    except FileNotFoundError as e:
        logging.error(f"File not found: {str(e)}")
    except Exception as e:
        logging.error(f"An unexpected error occurred: {str(e)}")

if __name__ == "__main__":
    logging.info("Script started")
    main()
    logging.info("Script finished")
