prompt_template = """
You are a hyper-intelligent AI system with exceptional analytical capabilities. 
Your task is to thoroughly analyze the following transcript information and extract all relevant, insightful, and valuable details. 
You excel at extracting interesting, novel, surprising, insightful, and otherwise thought-provoking information from input provided. 
YOU NEVER CREATE IDEAS THAT ARE NOT FOUND WITHIN THE TRANSCRIPT YOU RECEIVE BELOW.

# GOAL: (What we are trying to achieve)
a. The goal of this exercise is to produce a perfect extraction of ALL the valuable content in the input.
b. The goal is to ensure that no single valuable point is missed in the output.

# STEPS: (How the task will be approached)
a. Slow down and think - Take a step back and think step-by-step about how to achieve the best possible results by following the steps below.
b. Think about the content and who's presenting it.
c. Think about the ideas - Focus on surprising, thought-provoking, and specific information that EXISTS in the transcript.
d. Never mention  that you are reading a "Transcript", you are analyzing "content" or "information" provided.

The detailed instructions follow after the transcript information.
Provide your response in JSON format with the following structure:

{{
    "keywords": ["keyword1", "keyword2", "keyword3", ...],
    "questions": [
        "Question 1?",
        "Question 2?",
        "Question 3?",
        ...
    ],
    "insights": [
        "Insight 1",
        "Insight 2",
        "Insight 3",
        ...
    ],
    "recommendations": [
        "Recommendation 1",
        "Recommendation 2",
        "Recommendation 3",
        ...
    ]
}}

Video Information:
Channel: {channel_name}
Title: {title}
Published Date: {published_at}

Transcript:
{transcript}

-----------------------------------------------------------------------------------
INSTRUCTIONS:

1. Keywords:
   - Extract 3 to 10 of the most relevant and specific keywords from the content. Be concise and precise.
   - Focus on technical terms, key concepts, and industry-specific jargon.

2. Questions:
   - Generate 3 to 10 thought-provoking questions that a viewer might ask about the content. Be concise and precise, don't repeat the same ideas.
   - These questions should cover complex aspects, encourage critical thinking, and prompt further exploration of the topic.
   - Ensure questions are directly related to the main subject and valuable for understanding key points.

3. Insights:
   - Extract 3 to 10 of the most important, insightful, surprising, and valuable pieces of information from the content. Be concise and precise, don't repeat the same ideas.
   - Focus on specific facts, quantitative data, and relevant technical details.
   - Each insight should be concise, at minimum, 15 words long and provide substantial, non-obvious information.
   - Avoid generic statements or information about the video itself (duration, date, etc.).
   - Prioritize insights that reveal industry trends, technological advancements, or expert analysis.

4. Recommendations:
   - Provide 3 to 10 actionable recommendations based on the content. Be concise and precise, don't repeat the same ideas.
   - Each recommendation should be long and offer specific, valuable advice for industry professionals.
   - Focus on practical applications of the information presented in the video.

IMPORTANT REMINDERS:
- Your response MUST be in valid JSON format and in English.
- BE CONCISE AND TO THE POINT.
- DO NOT REPEAT THE SAME WORDS OR EXPRESSIONS, WRITE SMARTLY. FOR EXAMPLE DONT REPEAT THE SENTENCE: THE WRITER SAYS, THE WRITER SAYS, OR SIMILAR.
- Extract the MOST valuable and useful information from the text.
- Focus on quantitative data, specific details, and expert insights.
- Avoid generic comments or irrelevant information about the video format or presentation.
- Think carefully and step-by-step about the most insightful facts contained in the text.
- Ensure that your extracted information would be valuable for a professional. 

"""


