'use client'

import { useState } from 'react'
import { SearchComponent } from '@/components/SearchComponent'
import { TopicSelector } from '@/components/TopicSelector'
import { Search, Filter } from 'lucide-react'

export default function SearchPage() {
  const [selectedTopics, setSelectedTopics] = useState<string[]>([])

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4 flex items-center justify-center gap-2">
          <Search className="w-8 h-8 text-blue-600" />
          Search Video Summaries
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Use AI-powered semantic search to find relevant YouTube video content. 
          Search by topics, keywords, or ask questions about the content.
        </p>
      </div>

      {/* Filters Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center gap-2 mb-4">
          <Filter className="w-5 h-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-900">Filter by Topics</h2>
        </div>
        <TopicSelector
          selectedTopics={selectedTopics}
          onTopicsChange={setSelectedTopics}
        />
        {selectedTopics.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Active filters:</strong> Your search will only include videos from{' '}
              <span className="font-semibold">{selectedTopics.join(', ')}</span> topics.
            </p>
          </div>
        )}
      </div>

      {/* Search Component */}
      <SearchComponent selectedTopics={selectedTopics} />

      {/* Search Tips */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Search Tips</h3>
        <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-700">
          <div>
            <h4 className="font-medium mb-2">What you can search for:</h4>
            <ul className="space-y-1 list-disc list-inside">
              <li>Specific topics or subjects</li>
              <li>Technical concepts or tutorials</li>
              <li>Questions about video content</li>
              <li>Channel names or creators</li>
            </ul>
          </div>        <div>
            <h4 className="font-medium mb-2">Search examples:</h4>
            <ul className="space-y-1 list-disc list-inside">
              <li>&ldquo;How to build a React application&rdquo;</li>
              <li>&ldquo;Python machine learning tutorials&rdquo;</li>
              <li>&ldquo;Cooking recipes for beginners&rdquo;</li>
              <li>&ldquo;Latest technology trends&rdquo;</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
