WEBVTT
Kind: captions
Language: en

00:00:00.599 --> 00:00:02.750 align:start position:0%
 
hey<00:00:01.020><c> guys</c><00:00:01.260><c> welcome</c><00:00:01.680><c> back</c><00:00:01.920><c> <PERSON></c><00:00:02.280><c> here</c><00:00:02.580><c> from</c>

00:00:02.750 --> 00:00:02.760 align:start position:0%
hey guys welcome back <PERSON> here from
 

00:00:02.760 --> 00:00:04.670 align:start position:0%
hey guys welcome back <PERSON> here from
love<00:00:02.940><c> index</c><00:00:03.419><c> today</c><00:00:03.840><c> we're</c><00:00:04.080><c> starting</c><00:00:04.319><c> a</c><00:00:04.560><c> new</c>

00:00:04.670 --> 00:00:04.680 align:start position:0%
love index today we're starting a new
 

00:00:04.680 --> 00:00:06.769 align:start position:0%
love index today we're starting a new
video<00:00:04.860><c> series</c><00:00:05.220><c> Bottoms</c><00:00:05.880><c> Up</c><00:00:06.000><c> development</c><00:00:06.480><c> with</c>

00:00:06.769 --> 00:00:06.779 align:start position:0%
video series Bottoms Up development with
 

00:00:06.779 --> 00:00:08.030 align:start position:0%
video series Bottoms Up development with
llama<00:00:07.080><c> index</c>

00:00:08.030 --> 00:00:08.040 align:start position:0%
llama index
 

00:00:08.040 --> 00:00:09.530 align:start position:0%
llama index
now<00:00:08.400><c> what</c><00:00:08.580><c> do</c><00:00:08.700><c> I</c><00:00:08.820><c> mean</c><00:00:08.880><c> by</c><00:00:09.000><c> Bottoms</c><00:00:09.420><c> Up</c>

00:00:09.530 --> 00:00:09.540 align:start position:0%
now what do I mean by Bottoms Up
 

00:00:09.540 --> 00:00:12.290 align:start position:0%
now what do I mean by Bottoms Up
development<00:00:09.960><c> well</c><00:00:10.679><c> within</c><00:00:11.040><c> Luma</c><00:00:11.460><c> index</c><00:00:11.760><c> there</c>

00:00:12.290 --> 00:00:12.300 align:start position:0%
development well within Luma index there
 

00:00:12.300 --> 00:00:14.930 align:start position:0%
development well within Luma index there
are<00:00:12.599><c> a</c><00:00:13.019><c> ton</c><00:00:13.139><c> of</c><00:00:13.259><c> components</c><00:00:13.860><c> and</c><00:00:14.519><c> any</c><00:00:14.759><c> number</c>

00:00:14.930 --> 00:00:14.940 align:start position:0%
are a ton of components and any number
 

00:00:14.940 --> 00:00:16.369 align:start position:0%
are a ton of components and any number
of<00:00:15.120><c> these</c><00:00:15.360><c> you're</c><00:00:15.660><c> probably</c><00:00:15.900><c> going</c><00:00:16.080><c> to</c><00:00:16.199><c> need</c>

00:00:16.369 --> 00:00:16.379 align:start position:0%
of these you're probably going to need
 

00:00:16.379 --> 00:00:18.170 align:start position:0%
of these you're probably going to need
to<00:00:16.560><c> customize</c><00:00:16.980><c> when</c><00:00:17.279><c> you're</c><00:00:17.400><c> building</c><00:00:17.640><c> you</c>

00:00:18.170 --> 00:00:18.180 align:start position:0%
to customize when you're building you
 

00:00:18.180 --> 00:00:19.730 align:start position:0%
to customize when you're building you
know<00:00:18.240><c> your</c><00:00:18.420><c> own</c><00:00:18.600><c> query</c><00:00:18.900><c> pipeline</c><00:00:19.380><c> for</c><00:00:19.560><c> your</c>

00:00:19.730 --> 00:00:19.740 align:start position:0%
know your own query pipeline for your
 

00:00:19.740 --> 00:00:21.769 align:start position:0%
know your own query pipeline for your
own<00:00:19.859><c> application</c><00:00:20.340><c> and</c><00:00:21.000><c> so</c><00:00:21.119><c> this</c><00:00:21.300><c> video</c><00:00:21.480><c> series</c>

00:00:21.769 --> 00:00:21.779 align:start position:0%
own application and so this video series
 

00:00:21.779 --> 00:00:23.689 align:start position:0%
own application and so this video series
is<00:00:22.199><c> going</c><00:00:22.320><c> to</c><00:00:22.439><c> kind</c><00:00:22.560><c> of</c><00:00:22.680><c> cover</c><00:00:22.859><c> these</c>

00:00:23.689 --> 00:00:23.699 align:start position:0%
is going to kind of cover these
 

00:00:23.699 --> 00:00:25.429 align:start position:0%
is going to kind of cover these
low-level<00:00:24.119><c> components</c><00:00:24.720><c> how</c><00:00:25.140><c> you</c><00:00:25.320><c> can</c>

00:00:25.429 --> 00:00:25.439 align:start position:0%
low-level components how you can
 

00:00:25.439 --> 00:00:27.470 align:start position:0%
low-level components how you can
customize<00:00:25.859><c> them</c><00:00:26.160><c> and</c><00:00:26.760><c> how</c><00:00:27.060><c> you</c><00:00:27.180><c> can</c><00:00:27.300><c> kind</c><00:00:27.420><c> of</c>

00:00:27.470 --> 00:00:27.480 align:start position:0%
customize them and how you can kind of
 

00:00:27.480 --> 00:00:28.849 align:start position:0%
customize them and how you can kind of
stitch<00:00:27.720><c> them</c><00:00:27.900><c> all</c><00:00:28.019><c> together</c><00:00:28.199><c> into</c><00:00:28.500><c> your</c><00:00:28.740><c> own</c>

00:00:28.849 --> 00:00:28.859 align:start position:0%
stitch them all together into your own
 

00:00:28.859 --> 00:00:31.009 align:start position:0%
stitch them all together into your own
query<00:00:29.220><c> pipeline</c>

00:00:31.009 --> 00:00:31.019 align:start position:0%
query pipeline
 

00:00:31.019 --> 00:00:33.350 align:start position:0%
query pipeline
so<00:00:31.439><c> in</c><00:00:31.560><c> order</c><00:00:31.740><c> to</c><00:00:32.099><c> kind</c><00:00:32.460><c> of</c><00:00:32.520><c> demonstrate</c><00:00:33.059><c> this</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
so in order to kind of demonstrate this
 

00:00:33.360 --> 00:00:34.970 align:start position:0%
so in order to kind of demonstrate this
we<00:00:33.899><c> have</c><00:00:34.020><c> a</c><00:00:34.200><c> project</c><00:00:34.320><c> that</c><00:00:34.680><c> we're</c><00:00:34.800><c> going</c><00:00:34.920><c> to</c>

00:00:34.970 --> 00:00:34.980 align:start position:0%
we have a project that we're going to
 

00:00:34.980 --> 00:00:36.410 align:start position:0%
we have a project that we're going to
kind<00:00:35.100><c> of</c><00:00:35.219><c> cover</c><00:00:35.399><c> across</c><00:00:35.880><c> multiple</c><00:00:36.180><c> videos</c>

00:00:36.410 --> 00:00:36.420 align:start position:0%
kind of cover across multiple videos
 

00:00:36.420 --> 00:00:38.450 align:start position:0%
kind of cover across multiple videos
we're<00:00:37.320><c> going</c><00:00:37.440><c> to</c><00:00:37.559><c> be</c><00:00:37.620><c> building</c><00:00:37.800><c> a</c><00:00:38.160><c> sort</c><00:00:38.340><c> of</c>

00:00:38.450 --> 00:00:38.460 align:start position:0%
we're going to be building a sort of
 

00:00:38.460 --> 00:00:40.790 align:start position:0%
we're going to be building a sort of
query<00:00:38.700><c> engine</c><00:00:38.940><c> or</c><00:00:39.180><c> query</c><00:00:39.420><c> pipeline</c><00:00:39.899><c> across</c>

00:00:40.790 --> 00:00:40.800 align:start position:0%
query engine or query pipeline across
 

00:00:40.800 --> 00:00:43.430 align:start position:0%
query engine or query pipeline across
the<00:00:41.280><c> long</c><00:00:41.460><c> index</c><00:00:41.879><c> documentation</c><00:00:42.480><c> and</c><00:00:43.260><c> through</c>

00:00:43.430 --> 00:00:43.440 align:start position:0%
the long index documentation and through
 

00:00:43.440 --> 00:00:45.350 align:start position:0%
the long index documentation and through
this<00:00:43.620><c> process</c><00:00:43.920><c> we're</c><00:00:44.219><c> going</c><00:00:44.340><c> to</c><00:00:44.460><c> go</c><00:00:44.579><c> through</c><00:00:44.820><c> a</c>

00:00:45.350 --> 00:00:45.360 align:start position:0%
this process we're going to go through a
 

00:00:45.360 --> 00:00:47.410 align:start position:0%
this process we're going to go through a
ton<00:00:45.480><c> of</c><00:00:45.600><c> like</c><00:00:45.719><c> really</c><00:00:45.960><c> low</c><00:00:46.200><c> level</c><00:00:46.440><c> components</c>

00:00:47.410 --> 00:00:47.420 align:start position:0%
ton of like really low level components
 

00:00:47.420 --> 00:00:51.170 align:start position:0%
ton of like really low level components
lom's<00:00:48.420><c> documents</c><00:00:49.079><c> and</c><00:00:49.200><c> nodes</c><00:00:49.500><c> retrievers</c><00:00:50.399><c> and</c>

00:00:51.170 --> 00:00:51.180 align:start position:0%
lom's documents and nodes retrievers and
 

00:00:51.180 --> 00:00:52.970 align:start position:0%
lom's documents and nodes retrievers and
show<00:00:51.360><c> you</c><00:00:51.420><c> how</c><00:00:51.660><c> to</c><00:00:51.719><c> customize</c><00:00:52.140><c> these</c><00:00:52.440><c> and</c><00:00:52.800><c> how</c>

00:00:52.970 --> 00:00:52.980 align:start position:0%
show you how to customize these and how
 

00:00:52.980 --> 00:00:54.610 align:start position:0%
show you how to customize these and how
you<00:00:53.039><c> can</c><00:00:53.160><c> kind</c><00:00:53.340><c> of</c><00:00:53.399><c> stitch</c><00:00:53.700><c> them</c><00:00:53.879><c> all</c><00:00:54.120><c> together</c>

00:00:54.610 --> 00:00:54.620 align:start position:0%
you can kind of stitch them all together
 

00:00:54.620 --> 00:00:56.750 align:start position:0%
you can kind of stitch them all together
and<00:00:55.620><c> as</c><00:00:55.800><c> well</c><00:00:55.920><c> as</c><00:00:56.039><c> how</c><00:00:56.219><c> you</c><00:00:56.280><c> can</c><00:00:56.399><c> test</c><00:00:56.579><c> and</c>

00:00:56.750 --> 00:00:56.760 align:start position:0%
and as well as how you can test and
 

00:00:56.760 --> 00:00:58.790 align:start position:0%
and as well as how you can test and
debug<00:00:57.120><c> each</c><00:00:57.360><c> component</c>

00:00:58.790 --> 00:00:58.800 align:start position:0%
debug each component
 

00:00:58.800 --> 00:01:00.770 align:start position:0%
debug each component
and<00:00:59.219><c> so</c><00:00:59.399><c> starting</c><00:00:59.640><c> off</c><00:00:59.879><c> first</c><00:01:00.120><c> we're</c><00:01:00.539><c> going</c><00:01:00.719><c> to</c>

00:01:00.770 --> 00:01:00.780 align:start position:0%
and so starting off first we're going to
 

00:01:00.780 --> 00:01:02.389 align:start position:0%
and so starting off first we're going to
cover<00:01:00.899><c> llms</c>

00:01:02.389 --> 00:01:02.399 align:start position:0%
cover llms
 

00:01:02.399 --> 00:01:05.509 align:start position:0%
cover llms
so<00:01:02.820><c> right</c><00:01:03.000><c> now</c><00:01:03.180><c> in</c><00:01:03.359><c> lab</c><00:01:03.539><c> index</c><00:01:04.159><c> we</c><00:01:05.159><c> have</c><00:01:05.280><c> a</c><00:01:05.400><c> ton</c>

00:01:05.509 --> 00:01:05.519 align:start position:0%
so right now in lab index we have a ton
 

00:01:05.519 --> 00:01:08.210 align:start position:0%
so right now in lab index we have a ton
of<00:01:05.580><c> moms</c><00:01:06.320><c> that</c><00:01:07.320><c> were</c><00:01:07.439><c> recently</c><00:01:07.740><c> introduced</c><00:01:08.040><c> we</c>

00:01:08.210 --> 00:01:08.220 align:start position:0%
of moms that were recently introduced we
 

00:01:08.220 --> 00:01:10.969 align:start position:0%
of moms that were recently introduced we
have<00:01:08.340><c> open</c><00:01:08.460><c> AI</c><00:01:08.880><c> hugging</c><00:01:09.299><c> face</c><00:01:09.479><c> any</c><00:01:10.380><c> LM</c><00:01:10.799><c> from</c>

00:01:10.969 --> 00:01:10.979 align:start position:0%
have open AI hugging face any LM from
 

00:01:10.979 --> 00:01:13.670 align:start position:0%
have open AI hugging face any LM from
Lang<00:01:11.100><c> chain</c><00:01:11.340><c> is</c><00:01:11.520><c> supported</c><00:01:11.900><c> uh</c><00:01:12.900><c> Azure</c><00:01:13.380><c> was</c>

00:01:13.670 --> 00:01:13.680 align:start position:0%
Lang chain is supported uh Azure was
 

00:01:13.680 --> 00:01:14.990 align:start position:0%
Lang chain is supported uh Azure was
just<00:01:13.860><c> added</c><00:01:14.159><c> it's</c><00:01:14.340><c> actually</c><00:01:14.520><c> not</c><00:01:14.700><c> even</c><00:01:14.820><c> on</c>

00:01:14.990 --> 00:01:15.000 align:start position:0%
just added it's actually not even on
 

00:01:15.000 --> 00:01:17.390 align:start position:0%
just added it's actually not even on
this<00:01:15.180><c> list</c><00:01:15.299><c> uh</c><00:01:16.140><c> and</c><00:01:16.320><c> you</c><00:01:16.439><c> know</c><00:01:16.560><c> custom</c><00:01:16.680><c> llms</c>

00:01:17.390 --> 00:01:17.400 align:start position:0%
this list uh and you know custom llms
 

00:01:17.400 --> 00:01:20.210 align:start position:0%
this list uh and you know custom llms
and<00:01:18.240><c> each</c><00:01:18.420><c> of</c><00:01:18.540><c> these</c><00:01:18.780><c> have</c><00:01:19.380><c> really</c><00:01:19.799><c> low</c><00:01:20.040><c> level</c>

00:01:20.210 --> 00:01:20.220 align:start position:0%
and each of these have really low level
 

00:01:20.220 --> 00:01:23.170 align:start position:0%
and each of these have really low level
basic<00:01:20.759><c> methods</c><00:01:21.240><c> uh</c><00:01:21.840><c> complete</c><00:01:22.259><c> for</c><00:01:22.500><c> basic</c><00:01:22.860><c> text</c>

00:01:23.170 --> 00:01:23.180 align:start position:0%
basic methods uh complete for basic text
 

00:01:23.180 --> 00:01:26.690 align:start position:0%
basic methods uh complete for basic text
completion<00:01:24.200><c> uh</c><00:01:25.200><c> chat</c><00:01:25.560><c> for</c><00:01:25.920><c> like</c><00:01:26.100><c> chat</c>

00:01:26.690 --> 00:01:26.700 align:start position:0%
completion uh chat for like chat
 

00:01:26.700 --> 00:01:28.429 align:start position:0%
completion uh chat for like chat
response<00:01:27.180><c> to</c><00:01:27.299><c> like</c><00:01:27.540><c> a</c><00:01:27.659><c> list</c><00:01:27.780><c> of</c><00:01:27.900><c> Nest</c><00:01:28.200><c> chat</c>

00:01:28.429 --> 00:01:28.439 align:start position:0%
response to like a list of Nest chat
 

00:01:28.439 --> 00:01:30.710 align:start position:0%
response to like a list of Nest chat
messages<00:01:28.820><c> as</c><00:01:29.820><c> well</c><00:01:30.000><c> as</c><00:01:30.119><c> streaming</c><00:01:30.479><c> for</c><00:01:30.600><c> each</c>

00:01:30.710 --> 00:01:30.720 align:start position:0%
messages as well as streaming for each
 

00:01:30.720 --> 00:01:32.149 align:start position:0%
messages as well as streaming for each
of<00:01:30.840><c> those</c><00:01:30.960><c> and</c><00:01:31.320><c> then</c><00:01:31.439><c> as</c><00:01:31.560><c> well</c><00:01:31.740><c> as</c><00:01:31.799><c> there's</c>

00:01:32.149 --> 00:01:32.159 align:start position:0%
of those and then as well as there's
 

00:01:32.159 --> 00:01:34.310 align:start position:0%
of those and then as well as there's
async<00:01:32.759><c> versions</c><00:01:33.000><c> of</c><00:01:33.180><c> all</c><00:01:33.360><c> of</c><00:01:33.479><c> these</c>

00:01:34.310 --> 00:01:34.320 align:start position:0%
async versions of all of these
 

00:01:34.320 --> 00:01:36.590 align:start position:0%
async versions of all of these
and<00:01:34.740><c> so</c><00:01:34.860><c> using</c><00:01:35.220><c> these</c><00:01:35.460><c> at</c><00:01:35.700><c> this</c><00:01:35.820><c> low</c><00:01:36.000><c> level</c><00:01:36.180><c> is</c>

00:01:36.590 --> 00:01:36.600 align:start position:0%
and so using these at this low level is
 

00:01:36.600 --> 00:01:37.910 align:start position:0%
and so using these at this low level is
super<00:01:36.840><c> simple</c><00:01:37.020><c> as</c><00:01:37.320><c> we</c><00:01:37.439><c> can</c><00:01:37.560><c> see</c><00:01:37.680><c> in</c><00:01:37.799><c> this</c>

00:01:37.910 --> 00:01:37.920 align:start position:0%
super simple as we can see in this
 

00:01:37.920 --> 00:01:40.730 align:start position:0%
super simple as we can see in this
example<00:01:38.280><c> down</c><00:01:38.520><c> here</c><00:01:39.020><c> you</c><00:01:40.020><c> can</c><00:01:40.140><c> instantiate</c>

00:01:40.730 --> 00:01:40.740 align:start position:0%
example down here you can instantiate
 

00:01:40.740 --> 00:01:43.550 align:start position:0%
example down here you can instantiate
your<00:01:41.100><c> LOM</c><00:01:41.460><c> super</c><00:01:41.820><c> easily</c><00:01:42.299><c> and</c><00:01:43.020><c> really</c><00:01:43.200><c> quickly</c>

00:01:43.550 --> 00:01:43.560 align:start position:0%
your LOM super easily and really quickly
 

00:01:43.560 --> 00:01:45.350 align:start position:0%
your LOM super easily and really quickly
you<00:01:43.920><c> know</c><00:01:44.040><c> ask</c><00:01:44.280><c> it</c><00:01:44.460><c> tell</c><00:01:44.640><c> you</c><00:01:44.759><c> a</c><00:01:44.880><c> joke</c><00:01:45.000><c> it'll</c>

00:01:45.350 --> 00:01:45.360 align:start position:0%
you know ask it tell you a joke it'll
 

00:01:45.360 --> 00:01:47.690 align:start position:0%
you know ask it tell you a joke it'll
give<00:01:45.479><c> you</c><00:01:45.540><c> a</c><00:01:45.720><c> response</c><00:01:46.140><c> you</c><00:01:46.920><c> can</c><00:01:47.040><c> look</c><00:01:47.220><c> at</c><00:01:47.340><c> the</c>

00:01:47.690 --> 00:01:47.700 align:start position:0%
give you a response you can look at the
 

00:01:47.700 --> 00:01:49.670 align:start position:0%
give you a response you can look at the
response<00:01:48.540><c> text</c><00:01:48.720><c> or</c><00:01:49.020><c> you</c><00:01:49.140><c> can</c><00:01:49.259><c> actually</c><00:01:49.439><c> look</c>

00:01:49.670 --> 00:01:49.680 align:start position:0%
response text or you can actually look
 

00:01:49.680 --> 00:01:53.210 align:start position:0%
response text or you can actually look
at<00:01:49.979><c> the</c><00:01:50.220><c> raw</c><00:01:50.520><c> Json</c><00:01:51.180><c> that</c><00:01:51.780><c> came</c><00:01:51.899><c> from</c><00:01:52.140><c> that</c><00:01:52.799><c> LOM</c>

00:01:53.210 --> 00:01:53.220 align:start position:0%
at the raw Json that came from that LOM
 

00:01:53.220 --> 00:01:54.649 align:start position:0%
at the raw Json that came from that LOM
response

00:01:54.649 --> 00:01:54.659 align:start position:0%
response
 

00:01:54.659 --> 00:01:56.330 align:start position:0%
response
in<00:01:55.020><c> addition</c><00:01:55.200><c> to</c><00:01:55.380><c> that</c><00:01:55.500><c> there's</c><00:01:55.740><c> also</c><00:01:56.040><c> the</c>

00:01:56.330 --> 00:01:56.340 align:start position:0%
in addition to that there's also the
 

00:01:56.340 --> 00:01:58.850 align:start position:0%
in addition to that there's also the
kind<00:01:56.579><c> of</c><00:01:56.640><c> chat</c><00:01:57.000><c> methods</c><00:01:57.479><c> so</c><00:01:58.079><c> in</c><00:01:58.200><c> this</c><00:01:58.380><c> case</c><00:01:58.500><c> now</c>

00:01:58.850 --> 00:01:58.860 align:start position:0%
kind of chat methods so in this case now
 

00:01:58.860 --> 00:02:01.010 align:start position:0%
kind of chat methods so in this case now
the<00:01:59.340><c> input</c><00:01:59.700><c> instead</c><00:02:00.240><c> of</c><00:02:00.360><c> a</c><00:02:00.479><c> string</c><00:02:00.720><c> is</c><00:02:00.840><c> going</c>

00:02:01.010 --> 00:02:01.020 align:start position:0%
the input instead of a string is going
 

00:02:01.020 --> 00:02:02.690 align:start position:0%
the input instead of a string is going
to<00:02:01.140><c> be</c><00:02:01.200><c> a</c><00:02:01.380><c> list</c><00:02:01.560><c> of</c><00:02:01.740><c> messages</c>

00:02:02.690 --> 00:02:02.700 align:start position:0%
to be a list of messages
 

00:02:02.700 --> 00:02:05.209 align:start position:0%
to be a list of messages
and<00:02:03.360><c> so</c><00:02:03.540><c> this</c><00:02:03.840><c> in</c><00:02:04.320><c> this</c><00:02:04.439><c> simple</c><00:02:04.619><c> example</c><00:02:04.979><c> here</c>

00:02:05.209 --> 00:02:05.219 align:start position:0%
and so this in this simple example here
 

00:02:05.219 --> 00:02:07.670 align:start position:0%
and so this in this simple example here
we<00:02:05.520><c> have</c><00:02:05.640><c> a</c><00:02:06.180><c> system</c><00:02:06.420><c> message</c><00:02:06.780><c> asking</c><00:02:07.439><c> it</c><00:02:07.560><c> to</c>

00:02:07.670 --> 00:02:07.680 align:start position:0%
we have a system message asking it to
 

00:02:07.680 --> 00:02:08.930 align:start position:0%
we have a system message asking it to
talk<00:02:07.799><c> like</c><00:02:07.979><c> a</c><00:02:08.160><c> pirate</c><00:02:08.399><c> and</c><00:02:08.580><c> then</c><00:02:08.700><c> the</c><00:02:08.819><c> same</c>

00:02:08.930 --> 00:02:08.940 align:start position:0%
talk like a pirate and then the same
 

00:02:08.940 --> 00:02:11.270 align:start position:0%
talk like a pirate and then the same
thing<00:02:09.119><c> again</c><00:02:09.300><c> the</c><00:02:09.959><c> user</c><00:02:10.259><c> is</c><00:02:10.319><c> asking</c><00:02:10.679><c> to</c><00:02:10.860><c> tell</c>

00:02:11.270 --> 00:02:11.280 align:start position:0%
thing again the user is asking to tell
 

00:02:11.280 --> 00:02:14.449 align:start position:0%
thing again the user is asking to tell
me<00:02:11.400><c> a</c><00:02:11.580><c> joke</c><00:02:11.700><c> and</c><00:02:12.420><c> here</c><00:02:12.780><c> uh</c><00:02:13.319><c> chat</c><00:02:13.560><c> ubt</c><00:02:13.980><c> does</c><00:02:14.160><c> its</c>

00:02:14.449 --> 00:02:14.459 align:start position:0%
me a joke and here uh chat ubt does its
 

00:02:14.459 --> 00:02:16.550 align:start position:0%
me a joke and here uh chat ubt does its
best<00:02:14.520><c> to</c><00:02:14.940><c> tell</c><00:02:15.480><c> a</c><00:02:15.660><c> pirate</c><00:02:15.959><c> joke</c><00:02:16.080><c> I</c><00:02:16.260><c> guess</c><00:02:16.379><c> not</c>

00:02:16.550 --> 00:02:16.560 align:start position:0%
best to tell a pirate joke I guess not
 

00:02:16.560 --> 00:02:18.229 align:start position:0%
best to tell a pirate joke I guess not
quite<00:02:16.739><c> following</c><00:02:17.040><c> instructions</c><00:02:17.520><c> but</c><00:02:17.879><c> you</c>

00:02:18.229 --> 00:02:18.239 align:start position:0%
quite following instructions but you
 

00:02:18.239 --> 00:02:19.790 align:start position:0%
quite following instructions but you
know<00:02:18.360><c> close</c><00:02:18.599><c> enough</c>

00:02:19.790 --> 00:02:19.800 align:start position:0%
know close enough
 

00:02:19.800 --> 00:02:21.350 align:start position:0%
know close enough
and<00:02:20.220><c> that's</c><00:02:20.400><c> basically</c><00:02:20.640><c> it</c><00:02:20.760><c> to</c><00:02:20.940><c> these</c><00:02:21.060><c> LOM</c>

00:02:21.350 --> 00:02:21.360 align:start position:0%
and that's basically it to these LOM
 

00:02:21.360 --> 00:02:23.449 align:start position:0%
and that's basically it to these LOM
components<00:02:21.959><c> they're</c><00:02:22.379><c> super</c><00:02:22.860><c> simple</c><00:02:23.040><c> to</c><00:02:23.280><c> set</c>

00:02:23.449 --> 00:02:23.459 align:start position:0%
components they're super simple to set
 

00:02:23.459 --> 00:02:25.910 align:start position:0%
components they're super simple to set
up<00:02:23.640><c> and</c><00:02:23.819><c> use</c><00:02:24.060><c> and</c><00:02:24.599><c> it's</c><00:02:25.080><c> a</c><00:02:25.200><c> really</c><00:02:25.319><c> good</c><00:02:25.440><c> way</c><00:02:25.680><c> to</c>

00:02:25.910 --> 00:02:25.920 align:start position:0%
up and use and it's a really good way to
 

00:02:25.920 --> 00:02:28.610 align:start position:0%
up and use and it's a really good way to
start<00:02:26.160><c> testing</c><00:02:27.060><c> these</c><00:02:27.300><c> llms</c><00:02:27.900><c> for</c><00:02:28.440><c> your</c>

00:02:28.610 --> 00:02:28.620 align:start position:0%
start testing these llms for your
 

00:02:28.620 --> 00:02:30.050 align:start position:0%
start testing these llms for your
application<00:02:29.040><c> you</c><00:02:29.280><c> know</c><00:02:29.400><c> we</c><00:02:29.520><c> can</c><00:02:29.640><c> prompt</c><00:02:29.940><c> it</c>

00:02:30.050 --> 00:02:30.060 align:start position:0%
application you know we can prompt it
 

00:02:30.060 --> 00:02:31.250 align:start position:0%
application you know we can prompt it
with<00:02:30.300><c> specific</c><00:02:30.540><c> things</c><00:02:30.780><c> that</c><00:02:30.959><c> we're</c><00:02:31.080><c> going</c><00:02:31.200><c> to</c>

00:02:31.250 --> 00:02:31.260 align:start position:0%
with specific things that we're going to
 

00:02:31.260 --> 00:02:33.949 align:start position:0%
with specific things that we're going to
ask<00:02:31.379><c> it</c><00:02:31.560><c> later</c><00:02:31.819><c> using</c><00:02:32.819><c> you</c><00:02:33.300><c> know</c><00:02:33.360><c> documents</c>

00:02:33.949 --> 00:02:33.959 align:start position:0%
ask it later using you know documents
 

00:02:33.959 --> 00:02:36.290 align:start position:0%
ask it later using you know documents
that<00:02:34.200><c> it's</c><00:02:34.319><c> going</c><00:02:34.500><c> to</c><00:02:34.620><c> read</c><00:02:34.739><c> later</c><00:02:35.120><c> and</c><00:02:36.120><c> really</c>

00:02:36.290 --> 00:02:36.300 align:start position:0%
that it's going to read later and really
 

00:02:36.300 --> 00:02:38.150 align:start position:0%
that it's going to read later and really
get<00:02:36.480><c> a</c><00:02:36.599><c> feel</c><00:02:36.780><c> for</c><00:02:37.020><c> you</c><00:02:37.319><c> know</c><00:02:37.379><c> is</c><00:02:37.500><c> this</c><00:02:37.680><c> llm</c>

00:02:38.150 --> 00:02:38.160 align:start position:0%
get a feel for you know is this llm
 

00:02:38.160 --> 00:02:40.070 align:start position:0%
get a feel for you know is this llm
going<00:02:38.400><c> to</c><00:02:38.640><c> work</c><00:02:38.940><c> for</c><00:02:39.120><c> my</c><00:02:39.300><c> use</c><00:02:39.480><c> case</c><00:02:39.660><c> or</c><00:02:39.780><c> do</c><00:02:39.959><c> I</c>

00:02:40.070 --> 00:02:40.080 align:start position:0%
going to work for my use case or do I
 

00:02:40.080 --> 00:02:42.050 align:start position:0%
going to work for my use case or do I
need<00:02:40.200><c> to</c><00:02:40.319><c> do</c><00:02:40.500><c> prompt</c><00:02:41.099><c> engineering</c><00:02:41.519><c> or</c><00:02:41.700><c> find</c><00:02:41.879><c> a</c>

00:02:42.050 --> 00:02:42.060 align:start position:0%
need to do prompt engineering or find a
 

00:02:42.060 --> 00:02:43.250 align:start position:0%
need to do prompt engineering or find a
different<00:02:42.120><c> LOM</c>

00:02:43.250 --> 00:02:43.260 align:start position:0%
different LOM
 

00:02:43.260 --> 00:02:44.809 align:start position:0%
different LOM
and<00:02:43.620><c> so</c><00:02:43.800><c> in</c><00:02:43.980><c> order</c><00:02:44.099><c> to</c><00:02:44.280><c> kind</c><00:02:44.459><c> of</c><00:02:44.519><c> walk</c><00:02:44.640><c> through</c>

00:02:44.809 --> 00:02:44.819 align:start position:0%
and so in order to kind of walk through
 

00:02:44.819 --> 00:02:46.190 align:start position:0%
and so in order to kind of walk through
this<00:02:45.000><c> a</c><00:02:45.120><c> bit</c><00:02:45.239><c> more</c>

00:02:46.190 --> 00:02:46.200 align:start position:0%
this a bit more
 

00:02:46.200 --> 00:02:47.990 align:start position:0%
this a bit more
I'm<00:02:46.560><c> going</c><00:02:46.680><c> to</c><00:02:46.800><c> go</c><00:02:46.920><c> through</c><00:02:47.099><c> a</c><00:02:47.459><c> notebook</c><00:02:47.819><c> here</c>

00:02:47.990 --> 00:02:48.000 align:start position:0%
I'm going to go through a notebook here
 

00:02:48.000 --> 00:02:50.089 align:start position:0%
I'm going to go through a notebook here
that<00:02:48.300><c> actually</c><00:02:48.480><c> you</c><00:02:48.780><c> know</c><00:02:48.900><c> demonstrates</c><00:02:49.319><c> this</c>

00:02:50.089 --> 00:02:50.099 align:start position:0%
that actually you know demonstrates this
 

00:02:50.099 --> 00:02:51.589 align:start position:0%
that actually you know demonstrates this
so<00:02:50.459><c> like</c><00:02:50.580><c> I</c><00:02:50.700><c> mentioned</c><00:02:50.940><c> before</c><00:02:51.120><c> we're</c><00:02:51.420><c> going</c>

00:02:51.589 --> 00:02:51.599 align:start position:0%
so like I mentioned before we're going
 

00:02:51.599 --> 00:02:53.930 align:start position:0%
so like I mentioned before we're going
to<00:02:51.660><c> try</c><00:02:51.780><c> and</c><00:02:51.959><c> build</c><00:02:52.200><c> a</c><00:02:52.920><c> kind</c><00:02:53.400><c> of</c><00:02:53.459><c> query</c><00:02:53.760><c> engine</c>

00:02:53.930 --> 00:02:53.940 align:start position:0%
to try and build a kind of query engine
 

00:02:53.940 --> 00:02:56.449 align:start position:0%
to try and build a kind of query engine
over<00:02:54.239><c> the</c><00:02:54.420><c> Llama</c><00:02:54.660><c> index</c><00:02:55.019><c> documentation</c><00:02:55.620><c> it's</c>

00:02:56.449 --> 00:02:56.459 align:start position:0%
over the Llama index documentation it's
 

00:02:56.459 --> 00:02:59.330 align:start position:0%
over the Llama index documentation it's
going<00:02:56.640><c> to</c><00:02:56.700><c> be</c><00:02:56.819><c> highly</c><00:02:57.120><c> customized</c><00:02:57.720><c> and</c>

00:02:59.330 --> 00:02:59.340 align:start position:0%
going to be highly customized and
 

00:02:59.340 --> 00:03:01.309 align:start position:0%
going to be highly customized and
basically<00:02:59.940><c> touch</c><00:03:00.239><c> like</c><00:03:00.599><c> everything</c><00:03:00.840><c> that</c><00:03:01.140><c> can</c>

00:03:01.309 --> 00:03:01.319 align:start position:0%
basically touch like everything that can
 

00:03:01.319 --> 00:03:04.970 align:start position:0%
basically touch like everything that can
be<00:03:01.440><c> customized</c><00:03:02.099><c> but</c><00:03:03.060><c> starting</c><00:03:03.239><c> with</c><00:03:03.420><c> llms</c>

00:03:04.970 --> 00:03:04.980 align:start position:0%
be customized but starting with llms
 

00:03:04.980 --> 00:03:07.430 align:start position:0%
be customized but starting with llms
um<00:03:05.099><c> you</c><00:03:05.280><c> can</c><00:03:05.400><c> set</c><00:03:05.580><c> your</c><00:03:05.760><c> API</c><00:03:06.060><c> keys</c><00:03:06.480><c> right</c><00:03:07.319><c> now</c>

00:03:07.430 --> 00:03:07.440 align:start position:0%
um you can set your API keys right now
 

00:03:07.440 --> 00:03:09.170 align:start position:0%
um you can set your API keys right now
I've<00:03:07.680><c> just</c><00:03:07.860><c> cut</c><00:03:08.040><c> copied</c><00:03:08.519><c> the</c><00:03:08.700><c> documentation</c>

00:03:09.170 --> 00:03:09.180 align:start position:0%
I've just cut copied the documentation
 

00:03:09.180 --> 00:03:12.170 align:start position:0%
I've just cut copied the documentation
from<00:03:09.420><c> llama</c><00:03:09.720><c> index</c><00:03:10.220><c> so</c><00:03:11.220><c> I'm</c><00:03:11.340><c> going</c><00:03:11.459><c> to</c><00:03:11.519><c> read</c><00:03:11.760><c> a</c>

00:03:12.170 --> 00:03:12.180 align:start position:0%
from llama index so I'm going to read a
 

00:03:12.180 --> 00:03:13.970 align:start position:0%
from llama index so I'm going to read a
quick<00:03:12.300><c> markdown</c><00:03:12.900><c> file</c><00:03:13.200><c> it's</c><00:03:13.440><c> the</c><00:03:13.620><c> starter</c>

00:03:13.970 --> 00:03:13.980 align:start position:0%
quick markdown file it's the starter
 

00:03:13.980 --> 00:03:15.470 align:start position:0%
quick markdown file it's the starter
example

00:03:15.470 --> 00:03:15.480 align:start position:0%
example
 

00:03:15.480 --> 00:03:17.690 align:start position:0%
example
gonna<00:03:15.900><c> instantiate</c><00:03:16.560><c> our</c><00:03:16.680><c> llm</c><00:03:17.040><c> just</c><00:03:17.400><c> like</c><00:03:17.519><c> we</c>

00:03:17.690 --> 00:03:17.700 align:start position:0%
gonna instantiate our llm just like we
 

00:03:17.700 --> 00:03:19.070 align:start position:0%
gonna instantiate our llm just like we
saw<00:03:17.879><c> before</c>

00:03:19.070 --> 00:03:19.080 align:start position:0%
saw before
 

00:03:19.080 --> 00:03:22.009 align:start position:0%
saw before
here<00:03:19.800><c> I've</c><00:03:20.159><c> copied</c><00:03:20.700><c> the</c><00:03:21.239><c> kind</c><00:03:21.540><c> of</c><00:03:21.599><c> internal</c>

00:03:22.009 --> 00:03:22.019 align:start position:0%
here I've copied the kind of internal
 

00:03:22.019 --> 00:03:24.649 align:start position:0%
here I've copied the kind of internal
prompt<00:03:22.440><c> templates</c><00:03:22.739><c> from</c><00:03:23.040><c> llama</c><00:03:23.340><c> index</c><00:03:23.760><c> the</c>

00:03:24.649 --> 00:03:24.659 align:start position:0%
prompt templates from llama index the
 

00:03:24.659 --> 00:03:26.570 align:start position:0%
prompt templates from llama index the
two<00:03:24.840><c> main</c><00:03:25.019><c> ones</c><00:03:25.319><c> that</c><00:03:25.440><c> get</c><00:03:25.620><c> used</c><00:03:25.739><c> the</c><00:03:26.040><c> most</c><00:03:26.159><c> are</c>

00:03:26.570 --> 00:03:26.580 align:start position:0%
two main ones that get used the most are
 

00:03:26.580 --> 00:03:28.790 align:start position:0%
two main ones that get used the most are
the<00:03:26.819><c> text</c><00:03:26.940><c> QA</c><00:03:27.420><c> template</c><00:03:27.780><c> and</c><00:03:28.319><c> the</c><00:03:28.440><c> refine</c>

00:03:28.790 --> 00:03:28.800 align:start position:0%
the text QA template and the refine
 

00:03:28.800 --> 00:03:30.710 align:start position:0%
the text QA template and the refine
template<00:03:29.280><c> the</c><00:03:30.120><c> main</c><00:03:30.239><c> difference</c><00:03:30.420><c> between</c>

00:03:30.710 --> 00:03:30.720 align:start position:0%
template the main difference between
 

00:03:30.720 --> 00:03:32.330 align:start position:0%
template the main difference between
these<00:03:30.959><c> two</c><00:03:31.080><c> is</c><00:03:31.319><c> that</c><00:03:31.440><c> the</c><00:03:31.560><c> refine</c><00:03:31.860><c> template</c>

00:03:32.330 --> 00:03:32.340 align:start position:0%
these two is that the refine template
 

00:03:32.340 --> 00:03:35.570 align:start position:0%
these two is that the refine template
takes<00:03:33.000><c> an</c><00:03:33.180><c> existing</c><00:03:33.599><c> answer</c><00:03:34.159><c> and</c><00:03:35.159><c> tries</c><00:03:35.400><c> to</c>

00:03:35.570 --> 00:03:35.580 align:start position:0%
takes an existing answer and tries to
 

00:03:35.580 --> 00:03:37.850 align:start position:0%
takes an existing answer and tries to
refine<00:03:36.000><c> it</c><00:03:36.120><c> so</c><00:03:36.239><c> either</c><00:03:36.420><c> updated</c><00:03:36.840><c> or</c><00:03:37.260><c> repeat</c><00:03:37.560><c> it</c>

00:03:37.850 --> 00:03:37.860 align:start position:0%
refine it so either updated or repeat it
 

00:03:37.860 --> 00:03:40.550 align:start position:0%
refine it so either updated or repeat it
after<00:03:38.519><c> reading</c><00:03:38.940><c> some</c><00:03:39.060><c> new</c><00:03:39.239><c> context</c><00:03:39.659><c> and</c><00:03:40.440><c> this</c>

00:03:40.550 --> 00:03:40.560 align:start position:0%
after reading some new context and this
 

00:03:40.560 --> 00:03:42.350 align:start position:0%
after reading some new context and this
basically<00:03:40.799><c> only</c><00:03:40.980><c> gets</c><00:03:41.340><c> used</c><00:03:41.519><c> when</c><00:03:41.879><c> the</c><00:03:42.239><c> text</c>

00:03:42.350 --> 00:03:42.360 align:start position:0%
basically only gets used when the text
 

00:03:42.360 --> 00:03:44.869 align:start position:0%
basically only gets used when the text
retrieved<00:03:42.900><c> for</c><00:03:43.080><c> a</c><00:03:43.260><c> query</c><00:03:43.500><c> doesn't</c><00:03:44.099><c> fit</c><00:03:44.400><c> into</c><00:03:44.640><c> a</c>

00:03:44.869 --> 00:03:44.879 align:start position:0%
retrieved for a query doesn't fit into a
 

00:03:44.879 --> 00:03:46.729 align:start position:0%
retrieved for a query doesn't fit into a
single<00:03:45.060><c> LOM</c><00:03:45.360><c> call</c><00:03:45.659><c> so</c><00:03:45.900><c> we</c><00:03:46.019><c> need</c><00:03:46.140><c> to</c><00:03:46.200><c> refine</c><00:03:46.560><c> it</c>

00:03:46.729 --> 00:03:46.739 align:start position:0%
single LOM call so we need to refine it
 

00:03:46.739 --> 00:03:48.589 align:start position:0%
single LOM call so we need to refine it
and<00:03:47.519><c> so</c><00:03:47.640><c> these</c><00:03:47.819><c> are</c><00:03:47.879><c> the</c><00:03:47.940><c> two</c><00:03:48.060><c> prompts</c><00:03:48.420><c> that</c><00:03:48.540><c> I</c>

00:03:48.589 --> 00:03:48.599 align:start position:0%
and so these are the two prompts that I
 

00:03:48.599 --> 00:03:50.509 align:start position:0%
and so these are the two prompts that I
want<00:03:48.720><c> to</c><00:03:48.840><c> test</c><00:03:48.959><c> right</c><00:03:49.200><c> now</c><00:03:49.860><c> I'm</c><00:03:50.159><c> going</c><00:03:50.280><c> to</c><00:03:50.400><c> set</c>

00:03:50.509 --> 00:03:50.519 align:start position:0%
want to test right now I'm going to set
 

00:03:50.519 --> 00:03:51.710 align:start position:0%
want to test right now I'm going to set
those<00:03:50.700><c> up</c>

00:03:51.710 --> 00:03:51.720 align:start position:0%
those up
 

00:03:51.720 --> 00:03:53.390 align:start position:0%
those up
and<00:03:52.319><c> from</c><00:03:52.500><c> there</c><00:03:52.620><c> we</c><00:03:52.860><c> could</c><00:03:52.980><c> basically</c><00:03:53.280><c> just</c>

00:03:53.390 --> 00:03:53.400 align:start position:0%
and from there we could basically just
 

00:03:53.400 --> 00:03:56.270 align:start position:0%
and from there we could basically just
start<00:03:53.580><c> testing</c><00:03:54.560><c> so</c><00:03:55.560><c> using</c><00:03:55.860><c> the</c><00:03:55.980><c> starter</c>

00:03:56.270 --> 00:03:56.280 align:start position:0%
start testing so using the starter
 

00:03:56.280 --> 00:03:57.949 align:start position:0%
start testing so using the starter
example<00:03:56.640><c> I'm</c><00:03:57.120><c> going</c><00:03:57.239><c> to</c><00:03:57.299><c> ask</c><00:03:57.420><c> you</c><00:03:57.599><c> know</c><00:03:57.720><c> how</c>

00:03:57.949 --> 00:03:57.959 align:start position:0%
example I'm going to ask you know how
 

00:03:57.959 --> 00:04:00.289 align:start position:0%
example I'm going to ask you know how
can<00:03:58.080><c> I</c><00:03:58.260><c> install</c><00:03:58.500><c> llama</c><00:03:58.980><c> index</c>

00:04:00.289 --> 00:04:00.299 align:start position:0%
can I install llama index
 

00:04:00.299 --> 00:04:02.149 align:start position:0%
can I install llama index
to<00:04:00.720><c> install</c><00:04:00.840><c> on</c><00:04:01.080><c> the</c><00:04:01.260><c> index</c><00:04:01.500><c> you</c><00:04:01.739><c> can</c><00:04:01.860><c> follow</c>

00:04:02.149 --> 00:04:02.159 align:start position:0%
to install on the index you can follow
 

00:04:02.159 --> 00:04:05.570 align:start position:0%
to install on the index you can follow
the<00:04:02.459><c> okay</c><00:04:02.640><c> yeah</c><00:04:03.000><c> that</c><00:04:03.540><c> good</c><00:04:04.319><c> answer</c>

00:04:05.570 --> 00:04:05.580 align:start position:0%
the okay yeah that good answer
 

00:04:05.580 --> 00:04:07.130 align:start position:0%
the okay yeah that good answer
uh<00:04:06.120><c> next</c><00:04:06.299><c> we're</c><00:04:06.480><c> going</c><00:04:06.599><c> to</c><00:04:06.659><c> ask</c><00:04:06.780><c> it</c><00:04:06.900><c> how</c><00:04:07.019><c> to</c>

00:04:07.130 --> 00:04:07.140 align:start position:0%
uh next we're going to ask it how to
 

00:04:07.140 --> 00:04:08.750 align:start position:0%
uh next we're going to ask it how to
create<00:04:07.319><c> an</c><00:04:07.500><c> index</c>

00:04:08.750 --> 00:04:08.760 align:start position:0%
create an index
 

00:04:08.760 --> 00:04:09.890 align:start position:0%
create an index
this<00:04:09.239><c> one's</c><00:04:09.420><c> gonna</c><00:04:09.540><c> be</c><00:04:09.599><c> a</c><00:04:09.720><c> bit</c><00:04:09.840><c> more</c>

00:04:09.890 --> 00:04:09.900 align:start position:0%
this one's gonna be a bit more
 

00:04:09.900 --> 00:04:11.509 align:start position:0%
this one's gonna be a bit more
complicated<00:04:10.319><c> I</c><00:04:10.620><c> think</c><00:04:10.739><c> there's</c><00:04:10.980><c> a</c><00:04:11.159><c> few</c><00:04:11.340><c> more</c>

00:04:11.509 --> 00:04:11.519 align:start position:0%
complicated I think there's a few more
 

00:04:11.519 --> 00:04:13.309 align:start position:0%
complicated I think there's a few more
steps<00:04:11.760><c> to</c><00:04:11.879><c> how</c><00:04:12.060><c> to</c><00:04:12.180><c> do</c><00:04:12.299><c> that</c><00:04:12.420><c> yep</c>

00:04:13.309 --> 00:04:13.319 align:start position:0%
steps to how to do that yep
 

00:04:13.319 --> 00:04:16.370 align:start position:0%
steps to how to do that yep
so<00:04:13.980><c> it</c><00:04:14.640><c> gave</c><00:04:14.879><c> us</c><00:04:15.060><c> a</c><00:04:15.180><c> response</c><00:04:15.540><c> here</c><00:04:15.659><c> a</c><00:04:16.139><c> pretty</c>

00:04:16.370 --> 00:04:16.380 align:start position:0%
so it gave us a response here a pretty
 

00:04:16.380 --> 00:04:18.650 align:start position:0%
so it gave us a response here a pretty
detailed<00:04:16.859><c> it</c><00:04:17.340><c> looks</c><00:04:17.639><c> pretty</c><00:04:17.760><c> good</c><00:04:17.940><c> but</c>

00:04:18.650 --> 00:04:18.660 align:start position:0%
detailed it looks pretty good but
 

00:04:18.660 --> 00:04:20.210 align:start position:0%
detailed it looks pretty good but
actually<00:04:18.840><c> I</c><00:04:19.260><c> don't</c><00:04:19.380><c> need</c><00:04:19.859><c> all</c><00:04:20.100><c> this</c>

00:04:20.210 --> 00:04:20.220 align:start position:0%
actually I don't need all this
 

00:04:20.220 --> 00:04:21.830 align:start position:0%
actually I don't need all this
information<00:04:20.459><c> I</c><00:04:20.820><c> just</c><00:04:21.000><c> want</c><00:04:21.060><c> it</c><00:04:21.239><c> with</c><00:04:21.359><c> code</c><00:04:21.600><c> so</c>

00:04:21.830 --> 00:04:21.840 align:start position:0%
information I just want it with code so
 

00:04:21.840 --> 00:04:24.230 align:start position:0%
information I just want it with code so
let's<00:04:22.019><c> see</c><00:04:22.260><c> if</c><00:04:22.500><c> we</c><00:04:22.680><c> can</c><00:04:22.860><c> get</c><00:04:22.979><c> it</c><00:04:23.100><c> to</c><00:04:23.280><c> write</c><00:04:23.460><c> just</c>

00:04:24.230 --> 00:04:24.240 align:start position:0%
let's see if we can get it to write just
 

00:04:24.240 --> 00:04:26.749 align:start position:0%
let's see if we can get it to write just
answering<00:04:24.720><c> using</c><00:04:25.080><c> code</c><00:04:25.320><c> in</c><00:04:26.280><c> this</c><00:04:26.460><c> case</c><00:04:26.580><c> I'm</c>

00:04:26.749 --> 00:04:26.759 align:start position:0%
answering using code in this case I'm
 

00:04:26.759 --> 00:04:28.490 align:start position:0%
answering using code in this case I'm
also<00:04:27.000><c> going</c><00:04:27.060><c> to</c><00:04:27.180><c> use</c><00:04:27.240><c> the</c><00:04:27.600><c> stream</c><00:04:27.840><c> complete</c>

00:04:28.490 --> 00:04:28.500 align:start position:0%
also going to use the stream complete
 

00:04:28.500 --> 00:04:31.129 align:start position:0%
also going to use the stream complete
method<00:04:29.000><c> so</c><00:04:30.000><c> this</c><00:04:30.419><c> is</c><00:04:30.540><c> going</c><00:04:30.660><c> to</c><00:04:30.720><c> return</c><00:04:30.900><c> a</c>

00:04:31.129 --> 00:04:31.139 align:start position:0%
method so this is going to return a
 

00:04:31.139 --> 00:04:33.590 align:start position:0%
method so this is going to return a
generator<00:04:31.500><c> and</c><00:04:32.460><c> for</c><00:04:32.759><c> every</c><00:04:32.880><c> response</c><00:04:33.360><c> in</c><00:04:33.419><c> this</c>

00:04:33.590 --> 00:04:33.600 align:start position:0%
generator and for every response in this
 

00:04:33.600 --> 00:04:35.150 align:start position:0%
generator and for every response in this
generator<00:04:33.900><c> we're</c><00:04:34.199><c> going</c><00:04:34.320><c> to</c><00:04:34.380><c> print</c><00:04:34.560><c> the</c><00:04:34.740><c> Delta</c>

00:04:35.150 --> 00:04:35.160 align:start position:0%
generator we're going to print the Delta
 

00:04:35.160 --> 00:04:37.249 align:start position:0%
generator we're going to print the Delta
which<00:04:35.460><c> is</c><00:04:35.639><c> just</c><00:04:35.940><c> you</c><00:04:36.600><c> know</c><00:04:36.660><c> the</c><00:04:36.840><c> new</c><00:04:37.020><c> change</c>

00:04:37.249 --> 00:04:37.259 align:start position:0%
which is just you know the new change
 

00:04:37.259 --> 00:04:39.110 align:start position:0%
which is just you know the new change
that<00:04:37.560><c> has</c><00:04:37.680><c> come</c><00:04:37.860><c> in</c><00:04:37.979><c> from</c><00:04:38.160><c> the</c><00:04:38.280><c> LOM</c><00:04:38.580><c> or</c><00:04:38.880><c> the</c><00:04:39.000><c> new</c>

00:04:39.110 --> 00:04:39.120 align:start position:0%
that has come in from the LOM or the new
 

00:04:39.120 --> 00:04:40.430 align:start position:0%
that has come in from the LOM or the new
text

00:04:40.430 --> 00:04:40.440 align:start position:0%
text
 

00:04:40.440 --> 00:04:42.469 align:start position:0%
text
so<00:04:40.919><c> it's</c><00:04:41.040><c> going</c><00:04:41.220><c> to</c><00:04:41.220><c> stream</c><00:04:41.400><c> that</c><00:04:41.699><c> really</c><00:04:42.180><c> fast</c>

00:04:42.469 --> 00:04:42.479 align:start position:0%
so it's going to stream that really fast
 

00:04:42.479 --> 00:04:45.290 align:start position:0%
so it's going to stream that really fast
and<00:04:43.020><c> that</c><00:04:43.199><c> example</c><00:04:43.500><c> looks</c><00:04:43.800><c> perfect</c>

00:04:45.290 --> 00:04:45.300 align:start position:0%
and that example looks perfect
 

00:04:45.300 --> 00:04:47.450 align:start position:0%
and that example looks perfect
so<00:04:45.780><c> obviously</c><00:04:46.139><c> this</c><00:04:46.440><c> is</c><00:04:46.560><c> working</c><00:04:46.800><c> pretty</c><00:04:47.160><c> well</c>

00:04:47.450 --> 00:04:47.460 align:start position:0%
so obviously this is working pretty well
 

00:04:47.460 --> 00:04:49.969 align:start position:0%
so obviously this is working pretty well
with<00:04:48.300><c> this</c><00:04:48.479><c> template</c><00:04:48.840><c> and</c><00:04:49.020><c> with</c><00:04:49.320><c> this</c><00:04:49.560><c> content</c>

00:04:49.969 --> 00:04:49.979 align:start position:0%
with this template and with this content
 

00:04:49.979 --> 00:04:52.790 align:start position:0%
with this template and with this content
that<00:04:50.220><c> we've</c><00:04:50.400><c> loaded</c><00:04:50.759><c> uh</c><00:04:51.720><c> and</c><00:04:52.080><c> we</c><00:04:52.199><c> know</c><00:04:52.560><c> this</c>

00:04:52.790 --> 00:04:52.800 align:start position:0%
that we've loaded uh and we know this
 

00:04:52.800 --> 00:04:55.490 align:start position:0%
that we've loaded uh and we know this
because<00:04:52.979><c> we're</c><00:04:53.220><c> testing</c><00:04:53.580><c> this</c><00:04:54.060><c> low-level</c><00:04:55.020><c> Lon</c>

00:04:55.490 --> 00:04:55.500 align:start position:0%
because we're testing this low-level Lon
 

00:04:55.500 --> 00:04:57.469 align:start position:0%
because we're testing this low-level Lon
API

00:04:57.469 --> 00:04:57.479 align:start position:0%
API
 

00:04:57.479 --> 00:04:58.730 align:start position:0%
API
that's<00:04:57.660><c> the</c>

00:04:58.730 --> 00:04:58.740 align:start position:0%
that's the
 

00:04:58.740 --> 00:05:01.550 align:start position:0%
that's the
refine<00:04:59.460><c> template</c><00:05:00.000><c> here</c><00:05:00.180><c> as</c><00:05:00.360><c> well</c>

00:05:01.550 --> 00:05:01.560 align:start position:0%
refine template here as well
 

00:05:01.560 --> 00:05:04.370 align:start position:0%
refine template here as well
um<00:05:02.040><c> here</c><00:05:02.460><c> I</c><00:05:02.699><c> give</c><00:05:02.880><c> it</c><00:05:03.060><c> one</c><00:05:03.479><c> of</c><00:05:03.660><c> the</c><00:05:03.720><c> answers</c><00:05:04.199><c> it</c>

00:05:04.370 --> 00:05:04.380 align:start position:0%
um here I give it one of the answers it
 

00:05:04.380 --> 00:05:05.469 align:start position:0%
um here I give it one of the answers it
gave<00:05:04.560><c> before</c>

00:05:05.469 --> 00:05:05.479 align:start position:0%
gave before
 

00:05:05.479 --> 00:05:07.850 align:start position:0%
gave before
as<00:05:06.479><c> an</c><00:05:06.660><c> existing</c><00:05:06.960><c> answer</c><00:05:07.139><c> and</c><00:05:07.500><c> I'm</c><00:05:07.680><c> going</c><00:05:07.800><c> to</c>

00:05:07.850 --> 00:05:07.860 align:start position:0%
as an existing answer and I'm going to
 

00:05:07.860 --> 00:05:09.830 align:start position:0%
as an existing answer and I'm going to
say<00:05:07.979><c> no</c><00:05:08.160><c> I</c><00:05:08.460><c> want</c><00:05:08.580><c> this</c><00:05:08.880><c> to</c><00:05:09.419><c> write</c><00:05:09.540><c> your</c><00:05:09.720><c> answer</c>

00:05:09.830 --> 00:05:09.840 align:start position:0%
say no I want this to write your answer
 

00:05:09.840 --> 00:05:11.230 align:start position:0%
say no I want this to write your answer
using<00:05:10.199><c> only</c><00:05:10.380><c> code</c>

00:05:11.230 --> 00:05:11.240 align:start position:0%
using only code
 

00:05:11.240 --> 00:05:14.210 align:start position:0%
using only code
so<00:05:12.240><c> let's</c><00:05:12.419><c> see</c><00:05:12.660><c> if</c><00:05:12.960><c> you</c><00:05:13.259><c> know</c><00:05:13.320><c> the</c><00:05:13.620><c> LM</c><00:05:13.979><c> can</c>

00:05:14.210 --> 00:05:14.220 align:start position:0%
so let's see if you know the LM can
 

00:05:14.220 --> 00:05:17.629 align:start position:0%
so let's see if you know the LM can
handle<00:05:14.460><c> this</c><00:05:14.699><c> refined</c><00:05:15.180><c> template</c><00:05:15.479><c> now</c>

00:05:17.629 --> 00:05:17.639 align:start position:0%
handle this refined template now
 

00:05:17.639 --> 00:05:19.070 align:start position:0%
handle this refined template now
I<00:05:18.120><c> gave</c><00:05:18.300><c> it</c>

00:05:19.070 --> 00:05:19.080 align:start position:0%
I gave it
 

00:05:19.080 --> 00:05:22.790 align:start position:0%
I gave it
encode<00:05:19.919><c> not</c><00:05:20.759><c> entirely</c><00:05:21.180><c> all</c><00:05:21.419><c> in</c><00:05:21.600><c> code</c><00:05:21.840><c> but</c><00:05:22.680><c> this</c>

00:05:22.790 --> 00:05:22.800 align:start position:0%
encode not entirely all in code but this
 

00:05:22.800 --> 00:05:24.230 align:start position:0%
encode not entirely all in code but this
does<00:05:22.979><c> actually</c><00:05:23.220><c> look</c><00:05:23.460><c> really</c><00:05:23.639><c> correct</c><00:05:24.000><c> and</c>

00:05:24.230 --> 00:05:24.240 align:start position:0%
does actually look really correct and
 

00:05:24.240 --> 00:05:25.430 align:start position:0%
does actually look really correct and
this<00:05:24.479><c> is</c><00:05:24.600><c> a</c>

00:05:25.430 --> 00:05:25.440 align:start position:0%
this is a
 

00:05:25.440 --> 00:05:28.010 align:start position:0%
this is a
it's<00:05:25.680><c> a</c><00:05:25.860><c> good</c><00:05:25.979><c> answer</c><00:05:26.160><c> nice</c>

00:05:28.010 --> 00:05:28.020 align:start position:0%
it's a good answer nice
 

00:05:28.020 --> 00:05:30.110 align:start position:0%
it's a good answer nice
in<00:05:28.500><c> addition</c><00:05:28.740><c> to</c><00:05:28.860><c> this</c><00:05:29.039><c> there</c><00:05:29.460><c> are</c><00:05:29.580><c> also</c><00:05:29.880><c> the</c>

00:05:30.110 --> 00:05:30.120 align:start position:0%
in addition to this there are also the
 

00:05:30.120 --> 00:05:31.610 align:start position:0%
in addition to this there are also the
chat<00:05:30.300><c> endpoints</c><00:05:30.840><c> which</c><00:05:30.960><c> as</c><00:05:31.139><c> I</c><00:05:31.320><c> mentioned</c>

00:05:31.610 --> 00:05:31.620 align:start position:0%
chat endpoints which as I mentioned
 

00:05:31.620 --> 00:05:35.450 align:start position:0%
chat endpoints which as I mentioned
before<00:05:31.940><c> need</c><00:05:32.940><c> a</c><00:05:33.360><c> list</c><00:05:33.660><c> of</c><00:05:33.780><c> chat</c><00:05:34.080><c> messages</c><00:05:34.460><c> this</c>

00:05:35.450 --> 00:05:35.460 align:start position:0%
before need a list of chat messages this
 

00:05:35.460 --> 00:05:37.189 align:start position:0%
before need a list of chat messages this
is<00:05:35.580><c> useful</c><00:05:35.820><c> if</c><00:05:36.180><c> you</c><00:05:36.300><c> maybe</c><00:05:36.419><c> want</c><00:05:36.720><c> to</c><00:05:36.840><c> you</c><00:05:37.080><c> know</c>

00:05:37.189 --> 00:05:37.199 align:start position:0%
is useful if you maybe want to you know
 

00:05:37.199 --> 00:05:39.230 align:start position:0%
is useful if you maybe want to you know
give<00:05:37.320><c> it</c><00:05:37.440><c> a</c><00:05:37.620><c> system</c><00:05:37.800><c> prompt</c><00:05:38.280><c> in</c><00:05:38.940><c> this</c><00:05:39.120><c> case</c>

00:05:39.230 --> 00:05:39.240 align:start position:0%
give it a system prompt in this case
 

00:05:39.240 --> 00:05:40.790 align:start position:0%
give it a system prompt in this case
we're<00:05:39.419><c> giving</c><00:05:39.539><c> it</c><00:05:39.720><c> a</c><00:05:39.840><c> system</c><00:05:39.960><c> prompt</c><00:05:40.440><c> telling</c>

00:05:40.790 --> 00:05:40.800 align:start position:0%
we're giving it a system prompt telling
 

00:05:40.800 --> 00:05:42.710 align:start position:0%
we're giving it a system prompt telling
it<00:05:40.860><c> that</c><00:05:40.979><c> it's</c><00:05:41.100><c> a</c><00:05:41.220><c> QA</c><00:05:41.520><c> bot</c><00:05:41.880><c> answer</c><00:05:42.360><c> questions</c>

00:05:42.710 --> 00:05:42.720 align:start position:0%
it that it's a QA bot answer questions
 

00:05:42.720 --> 00:05:44.330 align:start position:0%
it that it's a QA bot answer questions
about<00:05:42.960><c> Lum</c><00:05:43.320><c> index</c>

00:05:44.330 --> 00:05:44.340 align:start position:0%
about Lum index
 

00:05:44.340 --> 00:05:46.070 align:start position:0%
about Lum index
so<00:05:44.759><c> if</c><00:05:44.880><c> we</c><00:05:45.000><c> ask</c><00:05:45.120><c> it</c><00:05:45.240><c> how</c><00:05:45.419><c> to</c><00:05:45.479><c> create</c><00:05:45.600><c> an</c><00:05:45.780><c> index</c>

00:05:46.070 --> 00:05:46.080 align:start position:0%
so if we ask it how to create an index
 

00:05:46.080 --> 00:05:49.550 align:start position:0%
so if we ask it how to create an index
again<00:05:46.320><c> we</c><00:05:47.280><c> should</c><00:05:47.460><c> get</c><00:05:47.699><c> something</c><00:05:48.320><c> uh</c><00:05:49.320><c> pretty</c>

00:05:49.550 --> 00:05:49.560 align:start position:0%
again we should get something uh pretty
 

00:05:49.560 --> 00:05:52.909 align:start position:0%
again we should get something uh pretty
helpful<00:05:49.860><c> I</c><00:05:50.220><c> would</c><00:05:50.400><c> say</c>

00:05:52.909 --> 00:05:52.919 align:start position:0%
 
 

00:05:52.919 --> 00:05:57.529 align:start position:0%
 
and<00:05:53.520><c> again</c><00:05:53.840><c> a</c><00:05:54.840><c> long</c><00:05:55.020><c> detailed</c><00:05:55.500><c> response</c><00:05:56.539><c> uh</c>

00:05:57.529 --> 00:05:57.539 align:start position:0%
and again a long detailed response uh
 

00:05:57.539 --> 00:05:58.969 align:start position:0%
and again a long detailed response uh
which<00:05:57.960><c> is</c>

00:05:58.969 --> 00:05:58.979 align:start position:0%
which is
 

00:05:58.979 --> 00:06:00.950 align:start position:0%
which is
fine<00:05:59.340><c> by</c><00:05:59.639><c> me</c>

00:06:00.950 --> 00:06:00.960 align:start position:0%
fine by me
 

00:06:00.960 --> 00:06:03.529 align:start position:0%
fine by me
so<00:06:01.259><c> yeah</c><00:06:01.380><c> that's</c><00:06:01.680><c> basically</c><00:06:01.979><c> it</c><00:06:02.220><c> uh</c><00:06:03.060><c> in</c><00:06:03.479><c> this</c>

00:06:03.529 --> 00:06:03.539 align:start position:0%
so yeah that's basically it uh in this
 

00:06:03.539 --> 00:06:05.210 align:start position:0%
so yeah that's basically it uh in this
video<00:06:03.720><c> we</c><00:06:03.960><c> kind</c><00:06:04.080><c> of</c><00:06:04.199><c> covered</c><00:06:04.440><c> how</c><00:06:04.979><c> to</c><00:06:05.100><c> use</c>

00:06:05.210 --> 00:06:05.220 align:start position:0%
video we kind of covered how to use
 

00:06:05.220 --> 00:06:08.749 align:start position:0%
video we kind of covered how to use
these<00:06:05.460><c> logo</c><00:06:05.759><c> element</c><00:06:06.120><c> low</c><00:06:06.660><c> level</c><00:06:06.840><c> LOM</c><00:06:07.380><c> apis</c><00:06:07.919><c> uh</c>

00:06:08.749 --> 00:06:08.759 align:start position:0%
these logo element low level LOM apis uh
 

00:06:08.759 --> 00:06:10.850 align:start position:0%
these logo element low level LOM apis uh
and<00:06:08.940><c> how</c><00:06:09.060><c> to</c><00:06:09.240><c> kind</c><00:06:09.419><c> of</c><00:06:09.600><c> quickly</c><00:06:10.380><c> get</c><00:06:10.560><c> started</c>

00:06:10.850 --> 00:06:10.860 align:start position:0%
and how to kind of quickly get started
 

00:06:10.860 --> 00:06:12.710 align:start position:0%
and how to kind of quickly get started
with<00:06:11.100><c> testing</c><00:06:11.460><c> inputs</c><00:06:12.000><c> and</c><00:06:12.060><c> outputs</c><00:06:12.419><c> using</c>

00:06:12.710 --> 00:06:12.720 align:start position:0%
with testing inputs and outputs using
 

00:06:12.720 --> 00:06:15.050 align:start position:0%
with testing inputs and outputs using
your<00:06:12.840><c> own</c><00:06:12.960><c> data</c><00:06:13.340><c> and</c><00:06:14.340><c> this</c><00:06:14.460><c> is</c><00:06:14.580><c> super</c><00:06:14.699><c> helpful</c>

00:06:15.050 --> 00:06:15.060 align:start position:0%
your own data and this is super helpful
 

00:06:15.060 --> 00:06:17.029 align:start position:0%
your own data and this is super helpful
because<00:06:15.300><c> if</c><00:06:15.840><c> you</c><00:06:16.199><c> know</c><00:06:16.320><c> if</c><00:06:16.560><c> you</c><00:06:16.680><c> have</c><00:06:16.800><c> a</c><00:06:16.919><c> super</c>

00:06:17.029 --> 00:06:17.039 align:start position:0%
because if you know if you have a super
 

00:06:17.039 --> 00:06:18.409 align:start position:0%
because if you know if you have a super
specific<00:06:17.400><c> use</c><00:06:17.639><c> case</c><00:06:17.820><c> you</c><00:06:18.000><c> want</c><00:06:18.120><c> to</c><00:06:18.240><c> make</c><00:06:18.300><c> sure</c>

00:06:18.409 --> 00:06:18.419 align:start position:0%
specific use case you want to make sure
 

00:06:18.419 --> 00:06:21.770 align:start position:0%
specific use case you want to make sure
that<00:06:18.600><c> the</c><00:06:18.780><c> llm</c><00:06:19.259><c> you're</c><00:06:19.440><c> using</c><00:06:19.740><c> can</c><00:06:20.520><c> handle</c><00:06:21.180><c> you</c>

00:06:21.770 --> 00:06:21.780 align:start position:0%
that the llm you're using can handle you
 

00:06:21.780 --> 00:06:23.150 align:start position:0%
that the llm you're using can handle you
know<00:06:21.840><c> the</c><00:06:22.080><c> use</c><00:06:22.199><c> case</c><00:06:22.380><c> that</c><00:06:22.560><c> you</c><00:06:22.680><c> have</c><00:06:22.800><c> intended</c>

00:06:23.150 --> 00:06:23.160 align:start position:0%
know the use case that you have intended
 

00:06:23.160 --> 00:06:25.490 align:start position:0%
know the use case that you have intended
for<00:06:23.340><c> it</c><00:06:23.520><c> in</c><00:06:24.120><c> this</c><00:06:24.300><c> case</c><00:06:24.419><c> it</c><00:06:24.600><c> looks</c><00:06:24.900><c> pretty</c><00:06:25.259><c> good</c>

00:06:25.490 --> 00:06:25.500 align:start position:0%
for it in this case it looks pretty good
 

00:06:25.500 --> 00:06:27.050 align:start position:0%
for it in this case it looks pretty good
so<00:06:26.039><c> in</c><00:06:26.160><c> the</c><00:06:26.280><c> next</c><00:06:26.340><c> video</c><00:06:26.520><c> we're</c><00:06:26.699><c> going</c><00:06:26.819><c> to</c><00:06:26.940><c> move</c>

00:06:27.050 --> 00:06:27.060 align:start position:0%
so in the next video we're going to move
 

00:06:27.060 --> 00:06:30.050 align:start position:0%
so in the next video we're going to move
on<00:06:27.300><c> to</c><00:06:27.660><c> testing</c><00:06:28.620><c> uh</c><00:06:29.160><c> document</c><00:06:29.460><c> loading</c><00:06:29.940><c> and</c>

00:06:30.050 --> 00:06:30.060 align:start position:0%
on to testing uh document loading and
 

00:06:30.060 --> 00:06:31.850 align:start position:0%
on to testing uh document loading and
how<00:06:30.300><c> we</c><00:06:30.419><c> can</c><00:06:30.539><c> construct</c><00:06:30.960><c> documents</c><00:06:31.500><c> and</c><00:06:31.620><c> nodes</c>

00:06:31.850 --> 00:06:31.860 align:start position:0%
how we can construct documents and nodes
 

00:06:31.860 --> 00:06:34.730 align:start position:0%
how we can construct documents and nodes
from<00:06:32.280><c> the</c><00:06:32.460><c> Lum</c><00:06:32.699><c> index</c><00:06:33.000><c> documentation</c><00:06:33.600><c> so</c><00:06:34.440><c> see</c>

00:06:34.730 --> 00:06:34.740 align:start position:0%
from the Lum index documentation so see
 

00:06:34.740 --> 00:06:37.639 align:start position:0%
from the Lum index documentation so see
you<00:06:34.860><c> in</c><00:06:34.919><c> the</c><00:06:34.979><c> next</c><00:06:35.039><c> video</c><00:06:35.280><c> thanks</c>

