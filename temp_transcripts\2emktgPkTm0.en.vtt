WEBVTT
Kind: captions
Language: en

00:00:00.280 --> 00:00:01.709 align:start position:0%
 
building<00:00:00.640><c> data</c><00:00:00.960><c> apps</c><00:00:01.240><c> can</c><00:00:01.319><c> be</c><00:00:01.520><c> quite</c>

00:00:01.709 --> 00:00:01.719 align:start position:0%
building data apps can be quite
 

00:00:01.719 --> 00:00:03.470 align:start position:0%
building data apps can be quite
challenging<00:00:02.080><c> for</c><00:00:02.280><c> developers</c><00:00:03.040><c> typically</c>

00:00:03.470 --> 00:00:03.480 align:start position:0%
challenging for developers typically
 

00:00:03.480 --> 00:00:05.030 align:start position:0%
challenging for developers typically
involves<00:00:03.919><c> a</c><00:00:04.040><c> learning</c><00:00:04.359><c> curve</c><00:00:04.680><c> with</c><00:00:04.880><c> web</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
involves a learning curve with web
 

00:00:05.040 --> 00:00:06.869 align:start position:0%
involves a learning curve with web
development<00:00:05.480><c> Frameworks</c><00:00:06.319><c> integrating</c>

00:00:06.869 --> 00:00:06.879 align:start position:0%
development Frameworks integrating
 

00:00:06.879 --> 00:00:09.390 align:start position:0%
development Frameworks integrating
various<00:00:07.279><c> libraries</c><00:00:08.080><c> handling</c><00:00:08.480><c> frontend</c><00:00:09.160><c> and</c>

00:00:09.390 --> 00:00:09.400 align:start position:0%
various libraries handling frontend and
 

00:00:09.400 --> 00:00:11.669 align:start position:0%
various libraries handling frontend and
backend<00:00:09.800><c> code</c><00:00:10.160><c> and</c><00:00:10.280><c> then</c><00:00:10.480><c> making</c><00:00:10.719><c> sure</c><00:00:11.400><c> all</c>

00:00:11.669 --> 00:00:11.679 align:start position:0%
backend code and then making sure all
 

00:00:11.679 --> 00:00:13.589 align:start position:0%
backend code and then making sure all
this<00:00:11.920><c> works</c><00:00:12.280><c> seamlessly</c><00:00:13.040><c> together</c><00:00:13.360><c> not</c><00:00:13.480><c> to</c>

00:00:13.589 --> 00:00:13.599 align:start position:0%
this works seamlessly together not to
 

00:00:13.599 --> 00:00:16.070 align:start position:0%
this works seamlessly together not to
mention<00:00:13.839><c> the</c><00:00:14.000><c> time</c><00:00:14.160><c> it</c><00:00:14.320><c> takes</c><00:00:14.719><c> to</c><00:00:14.960><c> design</c><00:00:15.320><c> a</c><00:00:15.559><c> UI</c>

00:00:16.070 --> 00:00:16.080 align:start position:0%
mention the time it takes to design a UI
 

00:00:16.080 --> 00:00:17.429 align:start position:0%
mention the time it takes to design a UI
and<00:00:16.199><c> then</c><00:00:16.359><c> deploy</c><00:00:16.720><c> the</c><00:00:16.880><c> app</c><00:00:17.119><c> well</c><00:00:17.199><c> this</c><00:00:17.320><c> is</c>

00:00:17.429 --> 00:00:17.439 align:start position:0%
and then deploy the app well this is
 

00:00:17.439 --> 00:00:19.269 align:start position:0%
and then deploy the app well this is
where<00:00:17.560><c> streamlet</c><00:00:18.119><c> addresses</c><00:00:18.680><c> this</c><00:00:18.920><c> problem</c>

00:00:19.269 --> 00:00:19.279 align:start position:0%
where streamlet addresses this problem
 

00:00:19.279 --> 00:00:20.950 align:start position:0%
where streamlet addresses this problem
streamlet<00:00:19.800><c> handles</c><00:00:20.080><c> the</c><00:00:20.199><c> front</c><00:00:20.359><c> end</c><00:00:20.519><c> for</c><00:00:20.760><c> you</c>

00:00:20.950 --> 00:00:20.960 align:start position:0%
streamlet handles the front end for you
 

00:00:20.960 --> 00:00:23.109 align:start position:0%
streamlet handles the front end for you
providing<00:00:21.279><c> a</c><00:00:21.400><c> clean</c><00:00:21.720><c> and</c><00:00:21.920><c> interactive</c><00:00:22.439><c> UI</c><00:00:22.920><c> out</c>

00:00:23.109 --> 00:00:23.119 align:start position:0%
providing a clean and interactive UI out
 

00:00:23.119 --> 00:00:25.310 align:start position:0%
providing a clean and interactive UI out
of<00:00:23.320><c> the</c><00:00:23.480><c> box</c><00:00:24.000><c> streamlet</c><00:00:24.480><c> Simplicity</c><00:00:25.039><c> helps</c>

00:00:25.310 --> 00:00:25.320 align:start position:0%
of the box streamlet Simplicity helps
 

00:00:25.320 --> 00:00:27.189 align:start position:0%
of the box streamlet Simplicity helps
developers<00:00:25.760><c> focus</c><00:00:26.119><c> on</c><00:00:26.359><c> their</c><00:00:26.599><c> data</c><00:00:26.960><c> and</c>

00:00:27.189 --> 00:00:27.199 align:start position:0%
developers focus on their data and
 

00:00:27.199 --> 00:00:28.910 align:start position:0%
developers focus on their data and
functionality<00:00:28.039><c> rather</c><00:00:28.240><c> than</c><00:00:28.400><c> being</c><00:00:28.560><c> bogged</c>

00:00:28.910 --> 00:00:28.920 align:start position:0%
functionality rather than being bogged
 

00:00:28.920 --> 00:00:30.470 align:start position:0%
functionality rather than being bogged
down<00:00:29.160><c> by</c><00:00:29.279><c> the</c><00:00:29.400><c> web</c><00:00:29.599><c> developer</c><00:00:30.160><c> okay</c><00:00:30.279><c> so</c><00:00:30.400><c> I</c>

00:00:30.470 --> 00:00:30.480 align:start position:0%
down by the web developer okay so I
 

00:00:30.480 --> 00:00:32.470 align:start position:0%
down by the web developer okay so I
opened<00:00:30.679><c> up</c><00:00:30.759><c> my</c><00:00:30.920><c> py</c><00:00:31.080><c> charm</c><00:00:31.320><c> IDE</c><00:00:31.880><c> I</c><00:00:32.000><c> named</c><00:00:32.320><c> this</c>

00:00:32.470 --> 00:00:32.480 align:start position:0%
opened up my py charm IDE I named this
 

00:00:32.480 --> 00:00:34.430 align:start position:0%
opened up my py charm IDE I named this
first<00:00:32.759><c> dreamlet</c><00:00:33.320><c> app</c><00:00:33.719><c> and</c><00:00:33.840><c> my</c><00:00:34.000><c> environment</c>

00:00:34.430 --> 00:00:34.440 align:start position:0%
first dreamlet app and my environment
 

00:00:34.440 --> 00:00:36.229 align:start position:0%
first dreamlet app and my environment
that<00:00:34.559><c> I'm</c><00:00:34.680><c> going</c><00:00:34.760><c> to</c><00:00:34.920><c> use</c><00:00:35.160><c> is</c><00:00:35.360><c> cond</c><00:00:35.920><c> but</c><00:00:36.160><c> you</c>

00:00:36.229 --> 00:00:36.239 align:start position:0%
that I'm going to use is cond but you
 

00:00:36.239 --> 00:00:37.709 align:start position:0%
that I'm going to use is cond but you
can<00:00:36.399><c> use</c><00:00:36.640><c> anything</c><00:00:36.960><c> you</c><00:00:37.079><c> like</c><00:00:37.320><c> including</c>

00:00:37.709 --> 00:00:37.719 align:start position:0%
can use anything you like including
 

00:00:37.719 --> 00:00:39.910 align:start position:0%
can use anything you like including
virtual<00:00:38.160><c> environment</c><00:00:38.680><c> or</c><00:00:38.879><c> V</c><00:00:39.200><c> EnV</c><00:00:39.600><c> so</c><00:00:39.760><c> then</c>

00:00:39.910 --> 00:00:39.920 align:start position:0%
virtual environment or V EnV so then
 

00:00:39.920 --> 00:00:41.549 align:start position:0%
virtual environment or V EnV so then
click<00:00:40.200><c> create</c><00:00:40.680><c> okay</c><00:00:40.840><c> now</c><00:00:41.000><c> that</c><00:00:41.120><c> the</c><00:00:41.280><c> project</c>

00:00:41.549 --> 00:00:41.559 align:start position:0%
click create okay now that the project
 

00:00:41.559 --> 00:00:42.910 align:start position:0%
click create okay now that the project
is<00:00:41.719><c> set</c><00:00:41.879><c> up</c><00:00:42.039><c> I'm</c><00:00:42.120><c> going</c><00:00:42.200><c> to</c><00:00:42.320><c> right</c><00:00:42.480><c> click</c><00:00:42.680><c> my</c>

00:00:42.910 --> 00:00:42.920 align:start position:0%
is set up I'm going to right click my
 

00:00:42.920 --> 00:00:45.270 align:start position:0%
is set up I'm going to right click my
project<00:00:43.280><c> directory</c><00:00:44.000><c> go</c><00:00:44.160><c> to</c><00:00:44.399><c> new</c><00:00:44.920><c> and</c><00:00:45.039><c> then</c>

00:00:45.270 --> 00:00:45.280 align:start position:0%
project directory go to new and then
 

00:00:45.280 --> 00:00:47.029 align:start position:0%
project directory go to new and then
click<00:00:45.600><c> python</c><00:00:46.000><c> file</c><00:00:46.480><c> and</c><00:00:46.600><c> then</c><00:00:46.719><c> just</c><00:00:46.840><c> name</c>

00:00:47.029 --> 00:00:47.039 align:start position:0%
click python file and then just name
 

00:00:47.039 --> 00:00:48.069 align:start position:0%
click python file and then just name
this<00:00:47.160><c> main</c><00:00:47.520><c> okay</c><00:00:47.640><c> well</c><00:00:47.719><c> the</c><00:00:47.800><c> first</c><00:00:47.920><c> thing</c><00:00:48.000><c> we</c>

00:00:48.069 --> 00:00:48.079 align:start position:0%
this main okay well the first thing we
 

00:00:48.079 --> 00:00:49.950 align:start position:0%
this main okay well the first thing we
need<00:00:48.199><c> to</c><00:00:48.280><c> do</c><00:00:48.520><c> is</c><00:00:48.719><c> install</c><00:00:49.079><c> streamlet</c><00:00:49.680><c> and</c><00:00:49.840><c> to</c>

00:00:49.950 --> 00:00:49.960 align:start position:0%
need to do is install streamlet and to
 

00:00:49.960 --> 00:00:51.869 align:start position:0%
need to do is install streamlet and to
do<00:00:50.120><c> that</c><00:00:50.320><c> open</c><00:00:50.520><c> up</c><00:00:50.640><c> your</c><00:00:50.760><c> terminal</c><00:00:51.480><c> and</c><00:00:51.680><c> type</c>

00:00:51.869 --> 00:00:51.879 align:start position:0%
do that open up your terminal and type
 

00:00:51.879 --> 00:00:54.510 align:start position:0%
do that open up your terminal and type
in<00:00:52.199><c> PIP</c><00:00:52.559><c> install</c><00:00:53.239><c> streamlet</c><00:00:53.879><c> now</c><00:00:54.079><c> that's</c><00:00:54.320><c> done</c>

00:00:54.510 --> 00:00:54.520 align:start position:0%
in PIP install streamlet now that's done
 

00:00:54.520 --> 00:00:56.750 align:start position:0%
in PIP install streamlet now that's done
we<00:00:54.640><c> can</c><00:00:54.879><c> clear</c><00:00:55.199><c> our</c><00:00:55.440><c> terminal</c><00:00:56.079><c> then</c><00:00:56.280><c> minimize</c>

00:00:56.750 --> 00:00:56.760 align:start position:0%
we can clear our terminal then minimize
 

00:00:56.760 --> 00:00:58.430 align:start position:0%
we can clear our terminal then minimize
it<00:00:57.120><c> and</c><00:00:57.239><c> let's</c><00:00:57.440><c> begin</c><00:00:58.000><c> okay</c><00:00:58.160><c> now</c><00:00:58.239><c> we're</c><00:00:58.359><c> going</c>

00:00:58.430 --> 00:00:58.440 align:start position:0%
it and let's begin okay now we're going
 

00:00:58.440 --> 00:00:59.990 align:start position:0%
it and let's begin okay now we're going
to<00:00:58.559><c> go</c><00:00:58.640><c> ahead</c><00:00:58.840><c> and</c><00:00:59.000><c> import</c><00:00:59.280><c> streamlets</c><00:00:59.719><c> so</c>

00:00:59.990 --> 00:01:00.000 align:start position:0%
to go ahead and import streamlets so
 

00:01:00.000 --> 00:01:03.110 align:start position:0%
to go ahead and import streamlets so
type<00:01:00.160><c> in</c><00:01:00.320><c> import</c><00:01:01.039><c> streamlit</c><00:01:01.960><c> as</c><00:01:02.320><c> St</c><00:01:02.800><c> this</c><00:01:02.920><c> is</c>

00:01:03.110 --> 00:01:03.120 align:start position:0%
type in import streamlit as St this is
 

00:01:03.120 --> 00:01:04.630 align:start position:0%
type in import streamlit as St this is
common<00:01:03.440><c> practice</c><00:01:03.920><c> we're</c><00:01:04.040><c> going</c><00:01:04.119><c> to</c><00:01:04.280><c> go</c><00:01:04.400><c> to</c>

00:01:04.630 --> 00:01:04.640 align:start position:0%
common practice we're going to go to
 

00:01:04.640 --> 00:01:06.190 align:start position:0%
common practice we're going to go to
another<00:01:04.960><c> line</c><00:01:05.280><c> and</c><00:01:05.560><c> we're</c><00:01:05.640><c> going</c><00:01:05.760><c> to</c><00:01:05.880><c> add</c><00:01:06.040><c> our</c>

00:01:06.190 --> 00:01:06.200 align:start position:0%
another line and we're going to add our
 

00:01:06.200 --> 00:01:07.830 align:start position:0%
another line and we're going to add our
first<00:01:06.479><c> element</c><00:01:06.799><c> which</c><00:01:06.920><c> is</c><00:01:07.080><c> the</c><00:01:07.200><c> title</c><00:01:07.640><c> you</c><00:01:07.720><c> can</c>

00:01:07.830 --> 00:01:07.840 align:start position:0%
first element which is the title you can
 

00:01:07.840 --> 00:01:10.749 align:start position:0%
first element which is the title you can
simply<00:01:08.159><c> say</c><00:01:08.640><c> st.</c><00:01:09.640><c> tile</c><00:01:10.119><c> then</c><00:01:10.280><c> let's</c><00:01:10.439><c> give</c><00:01:10.560><c> it</c><00:01:10.600><c> a</c>

00:01:10.749 --> 00:01:10.759 align:start position:0%
simply say st. tile then let's give it a
 

00:01:10.759 --> 00:01:13.990 align:start position:0%
simply say st. tile then let's give it a
title<00:01:11.280><c> first</c><00:01:12.119><c> streamlet</c><00:01:13.119><c> app</c><00:01:13.479><c> okay</c><00:01:13.720><c> next</c><00:01:13.920><c> what</c>

00:01:13.990 --> 00:01:14.000 align:start position:0%
title first streamlet app okay next what
 

00:01:14.000 --> 00:01:15.149 align:start position:0%
title first streamlet app okay next what
we're<00:01:14.119><c> going</c><00:01:14.200><c> to</c><00:01:14.280><c> do</c><00:01:14.360><c> is</c><00:01:14.479><c> add</c><00:01:14.640><c> a</c><00:01:14.759><c> few</c><00:01:14.920><c> more</c>

00:01:15.149 --> 00:01:15.159 align:start position:0%
we're going to do is add a few more
 

00:01:15.159 --> 00:01:16.310 align:start position:0%
we're going to do is add a few more
elements<00:01:15.520><c> to</c><00:01:15.640><c> the</c><00:01:15.720><c> screen</c><00:01:16.040><c> these</c><00:01:16.119><c> are</c><00:01:16.200><c> going</c>

00:01:16.310 --> 00:01:16.320 align:start position:0%
elements to the screen these are going
 

00:01:16.320 --> 00:01:17.950 align:start position:0%
elements to the screen these are going
to<00:01:16.400><c> be</c><00:01:16.640><c> inputs</c><00:01:17.280><c> and</c><00:01:17.400><c> then</c><00:01:17.479><c> we're</c><00:01:17.600><c> going</c><00:01:17.680><c> to</c><00:01:17.799><c> do</c>

00:01:17.950 --> 00:01:17.960 align:start position:0%
to be inputs and then we're going to do
 

00:01:17.960 --> 00:01:19.550 align:start position:0%
to be inputs and then we're going to do
something<00:01:18.360><c> with</c><00:01:18.640><c> those</c><00:01:18.880><c> inputs</c><00:01:19.360><c> and</c><00:01:19.479><c> the</c>

00:01:19.550 --> 00:01:19.560 align:start position:0%
something with those inputs and the
 

00:01:19.560 --> 00:01:20.950 align:start position:0%
something with those inputs and the
first<00:01:19.720><c> thing</c><00:01:19.799><c> we</c><00:01:19.920><c> want</c><00:01:20.040><c> to</c><00:01:20.159><c> do</c><00:01:20.640><c> is</c><00:01:20.799><c> have</c>

00:01:20.950 --> 00:01:20.960 align:start position:0%
first thing we want to do is have
 

00:01:20.960 --> 00:01:22.230 align:start position:0%
first thing we want to do is have
somebody<00:01:21.240><c> give</c><00:01:21.360><c> us</c><00:01:21.479><c> their</c><00:01:21.640><c> name</c><00:01:21.920><c> so</c><00:01:22.040><c> we</c><00:01:22.119><c> can</c>

00:01:22.230 --> 00:01:22.240 align:start position:0%
somebody give us their name so we can
 

00:01:22.240 --> 00:01:26.270 align:start position:0%
somebody give us their name so we can
say<00:01:22.479><c> name</c><00:01:23.119><c> is</c><00:01:23.280><c> equal</c><00:01:23.520><c> to</c><00:01:24.040><c> st.</c><00:01:25.040><c> text</c><00:01:25.479><c> input</c><00:01:25.960><c> and</c>

00:01:26.270 --> 00:01:26.280 align:start position:0%
say name is equal to st. text input and
 

00:01:26.280 --> 00:01:29.030 align:start position:0%
say name is equal to st. text input and
say<00:01:26.920><c> enter</c><00:01:27.640><c> your</c><00:01:28.240><c> name</c><00:01:28.600><c> and</c><00:01:28.680><c> then</c><00:01:28.799><c> the</c><00:01:28.880><c> next</c>

00:01:29.030 --> 00:01:29.040 align:start position:0%
say enter your name and then the next
 

00:01:29.040 --> 00:01:30.510 align:start position:0%
say enter your name and then the next
element<00:01:29.360><c> is</c><00:01:29.479><c> we</c><00:01:29.600><c> want</c><00:01:29.720><c> to</c><00:01:29.920><c> to</c><00:01:30.000><c> ask</c><00:01:30.159><c> somebody</c>

00:01:30.510 --> 00:01:30.520 align:start position:0%
element is we want to to ask somebody
 

00:01:30.520 --> 00:01:33.030 align:start position:0%
element is we want to to ask somebody
their<00:01:30.759><c> age</c><00:01:31.439><c> but</c><00:01:31.640><c> this</c><00:01:31.840><c> time</c><00:01:32.240><c> we'll</c><00:01:32.439><c> say</c><00:01:32.720><c> age</c>

00:01:33.030 --> 00:01:33.040 align:start position:0%
their age but this time we'll say age
 

00:01:33.040 --> 00:01:36.389 align:start position:0%
their age but this time we'll say age
equals<00:01:33.720><c> st.</c><00:01:34.720><c> number</c><00:01:35.079><c> input</c><00:01:35.600><c> cuz</c><00:01:35.720><c> we</c><00:01:35.880><c> only</c><00:01:36.119><c> want</c>

00:01:36.389 --> 00:01:36.399 align:start position:0%
equals st. number input cuz we only want
 

00:01:36.399 --> 00:01:39.710 align:start position:0%
equals st. number input cuz we only want
a<00:01:36.560><c> number</c><00:01:37.119><c> say</c><00:01:37.399><c> enter</c><00:01:38.200><c> your</c><00:01:38.759><c> age</c><00:01:39.399><c> and</c><00:01:39.520><c> then</c><00:01:39.640><c> we</c>

00:01:39.710 --> 00:01:39.720 align:start position:0%
a number say enter your age and then we
 

00:01:39.720 --> 00:01:41.230 align:start position:0%
a number say enter your age and then we
can<00:01:39.880><c> give</c><00:01:40.119><c> a</c><00:01:40.240><c> few</c><00:01:40.399><c> more</c><00:01:40.600><c> parameters</c><00:01:41.040><c> so</c><00:01:41.159><c> we</c>

00:01:41.230 --> 00:01:41.240 align:start position:0%
can give a few more parameters so we
 

00:01:41.240 --> 00:01:43.910 align:start position:0%
can give a few more parameters so we
want<00:01:41.399><c> the</c><00:01:41.560><c> minimum</c><00:01:42.399><c> value</c><00:01:42.799><c> to</c><00:01:42.960><c> be</c><00:01:43.240><c> zero</c><00:01:43.840><c> and</c>

00:01:43.910 --> 00:01:43.920 align:start position:0%
want the minimum value to be zero and
 

00:01:43.920 --> 00:01:48.230 align:start position:0%
want the minimum value to be zero and
then<00:01:44.079><c> the</c><00:01:44.320><c> max</c><00:01:44.960><c> value</c><00:01:45.680><c> to</c><00:01:45.880><c> be</c><00:01:46.200><c> 120</c><00:01:47.200><c> and</c><00:01:47.360><c> then</c><00:01:48.000><c> in</c>

00:01:48.230 --> 00:01:48.240 align:start position:0%
then the max value to be 120 and then in
 

00:01:48.240 --> 00:01:50.030 align:start position:0%
then the max value to be 120 and then in
increment<00:01:48.680><c> this</c><00:01:48.920><c> by</c><00:01:49.119><c> one</c><00:01:49.479><c> now</c><00:01:49.600><c> we</c><00:01:49.719><c> want</c><00:01:49.880><c> to</c>

00:01:50.030 --> 00:01:50.040 align:start position:0%
increment this by one now we want to
 

00:01:50.040 --> 00:01:51.630 align:start position:0%
increment this by one now we want to
have<00:01:50.159><c> somebody</c><00:01:50.439><c> be</c><00:01:50.560><c> able</c><00:01:50.719><c> to</c><00:01:50.960><c> select</c>

00:01:51.630 --> 00:01:51.640 align:start position:0%
have somebody be able to select
 

00:01:51.640 --> 00:01:53.069 align:start position:0%
have somebody be able to select
something<00:01:52.119><c> and</c><00:01:52.200><c> we</c><00:01:52.360><c> just</c><00:01:52.479><c> want</c><00:01:52.600><c> to</c><00:01:52.719><c> ask</c><00:01:52.920><c> them</c>

00:01:53.069 --> 00:01:53.079 align:start position:0%
something and we just want to ask them
 

00:01:53.079 --> 00:01:54.310 align:start position:0%
something and we just want to ask them
what<00:01:53.200><c> their</c><00:01:53.360><c> favorite</c><00:01:53.640><c> color</c><00:01:53.920><c> is</c><00:01:54.119><c> so</c><00:01:54.240><c> we're</c>

00:01:54.310 --> 00:01:54.320 align:start position:0%
what their favorite color is so we're
 

00:01:54.320 --> 00:01:57.029 align:start position:0%
what their favorite color is so we're
going<00:01:54.399><c> to</c><00:01:54.479><c> say</c><00:01:54.719><c> color</c><00:01:55.240><c> is</c><00:01:55.399><c> equal</c><00:01:55.680><c> to</c><00:01:56.039><c> st.</c>

00:01:57.029 --> 00:01:57.039 align:start position:0%
going to say color is equal to st.
 

00:01:57.039 --> 00:02:00.389 align:start position:0%
going to say color is equal to st.
select<00:01:57.439><c> box</c><00:01:57.920><c> select</c><00:01:58.399><c> your</c><00:01:58.840><c> fa</c><00:01:59.240><c> favorite</c><00:01:59.920><c> color</c>

00:02:00.389 --> 00:02:00.399 align:start position:0%
select box select your fa favorite color
 

00:02:00.399 --> 00:02:02.510 align:start position:0%
select box select your fa favorite color
and<00:02:00.520><c> what</c><00:02:00.640><c> we</c><00:02:00.719><c> can</c><00:02:00.880><c> do</c><00:02:01.079><c> here</c><00:02:01.320><c> is</c><00:02:01.719><c> give</c><00:02:01.920><c> an</c><00:02:02.119><c> array</c>

00:02:02.510 --> 00:02:02.520 align:start position:0%
and what we can do here is give an array
 

00:02:02.520 --> 00:02:04.510 align:start position:0%
and what we can do here is give an array
of<00:02:02.759><c> colors</c><00:02:03.079><c> to</c><00:02:03.280><c> choose</c><00:02:03.560><c> from</c><00:02:03.840><c> so</c><00:02:04.039><c> we</c><00:02:04.360><c> we'll</c>

00:02:04.510 --> 00:02:04.520 align:start position:0%
of colors to choose from so we we'll
 

00:02:04.520 --> 00:02:06.990 align:start position:0%
of colors to choose from so we we'll
just<00:02:04.680><c> give</c><00:02:04.840><c> three</c><00:02:05.079><c> here</c><00:02:05.240><c> so</c><00:02:05.479><c> red</c><00:02:06.000><c> yellow</c><00:02:06.680><c> and</c>

00:02:06.990 --> 00:02:07.000 align:start position:0%
just give three here so red yellow and
 

00:02:07.000 --> 00:02:09.070 align:start position:0%
just give three here so red yellow and
orange<00:02:07.399><c> and</c><00:02:07.479><c> the</c><00:02:07.680><c> next</c><00:02:07.960><c> element</c><00:02:08.280><c> I</c><00:02:08.399><c> want</c><00:02:08.720><c> is</c><00:02:08.920><c> a</c>

00:02:09.070 --> 00:02:09.080 align:start position:0%
orange and the next element I want is a
 

00:02:09.080 --> 00:02:11.029 align:start position:0%
orange and the next element I want is a
simple<00:02:09.520><c> checkbox</c><00:02:10.280><c> and</c><00:02:10.399><c> we</c><00:02:10.520><c> can</c><00:02:10.640><c> show</c><00:02:10.840><c> some</c>

00:02:11.029 --> 00:02:11.039 align:start position:0%
simple checkbox and we can show some
 

00:02:11.039 --> 00:02:13.150 align:start position:0%
simple checkbox and we can show some
more<00:02:11.280><c> information</c><00:02:11.959><c> if</c><00:02:12.239><c> that</c><00:02:12.400><c> checkbox</c><00:02:13.000><c> is</c>

00:02:13.150 --> 00:02:13.160 align:start position:0%
more information if that checkbox is
 

00:02:13.160 --> 00:02:16.229 align:start position:0%
more information if that checkbox is
selected<00:02:13.879><c> so</c><00:02:14.040><c> we</c><00:02:14.160><c> can</c><00:02:14.319><c> say</c><00:02:14.640><c> show</c><00:02:15.519><c> more</c><00:02:16.120><c> is</c>

00:02:16.229 --> 00:02:16.239 align:start position:0%
selected so we can say show more is
 

00:02:16.239 --> 00:02:20.150 align:start position:0%
selected so we can say show more is
equal<00:02:16.519><c> to</c><00:02:17.120><c> st.</c><00:02:18.120><c> checkbox</c><00:02:18.879><c> then</c><00:02:19.040><c> we</c><00:02:19.160><c> say</c><00:02:19.519><c> show</c>

00:02:20.150 --> 00:02:20.160 align:start position:0%
equal to st. checkbox then we say show
 

00:02:20.160 --> 00:02:22.589 align:start position:0%
equal to st. checkbox then we say show
more<00:02:20.680><c> information</c><00:02:21.400><c> okay</c><00:02:21.519><c> so</c><00:02:21.680><c> now</c><00:02:21.840><c> we</c><00:02:22.040><c> have</c><00:02:22.480><c> a</c>

00:02:22.589 --> 00:02:22.599 align:start position:0%
more information okay so now we have a
 

00:02:22.599 --> 00:02:24.790 align:start position:0%
more information okay so now we have a
bunch<00:02:22.840><c> of</c><00:02:23.200><c> input</c><00:02:23.879><c> elements</c><00:02:24.239><c> that</c><00:02:24.360><c> will</c><00:02:24.519><c> be</c><00:02:24.680><c> on</c>

00:02:24.790 --> 00:02:24.800 align:start position:0%
bunch of input elements that will be on
 

00:02:24.800 --> 00:02:26.830 align:start position:0%
bunch of input elements that will be on
the<00:02:24.879><c> screen</c><00:02:25.120><c> when</c><00:02:25.239><c> we</c><00:02:25.360><c> go</c><00:02:25.440><c> to</c><00:02:25.560><c> run</c><00:02:25.800><c> this</c><00:02:26.319><c> but</c><00:02:26.680><c> no</c>

00:02:26.830 --> 00:02:26.840 align:start position:0%
the screen when we go to run this but no
 

00:02:26.840 --> 00:02:28.150 align:start position:0%
the screen when we go to run this but no
we<00:02:26.920><c> don't</c><00:02:27.120><c> really</c><00:02:27.280><c> do</c><00:02:27.440><c> anything</c><00:02:27.760><c> with</c><00:02:27.920><c> that</c>

00:02:28.150 --> 00:02:28.160 align:start position:0%
we don't really do anything with that
 

00:02:28.160 --> 00:02:30.550 align:start position:0%
we don't really do anything with that
yet<00:02:28.480><c> so</c><00:02:28.760><c> what</c><00:02:28.920><c> I</c><00:02:29.080><c> want</c><00:02:29.239><c> to</c><00:02:29.440><c> create</c><00:02:29.920><c> is</c><00:02:30.040><c> a</c><00:02:30.200><c> submit</c>

00:02:30.550 --> 00:02:30.560 align:start position:0%
yet so what I want to create is a submit
 

00:02:30.560 --> 00:02:32.309 align:start position:0%
yet so what I want to create is a submit
button<00:02:30.840><c> that</c><00:02:30.959><c> whenever</c><00:02:31.319><c> that</c><00:02:31.440><c> is</c><00:02:31.720><c> clicked</c>

00:02:32.309 --> 00:02:32.319 align:start position:0%
button that whenever that is clicked
 

00:02:32.319 --> 00:02:33.949 align:start position:0%
button that whenever that is clicked
then<00:02:32.480><c> we</c><00:02:32.680><c> display</c><00:02:33.080><c> all</c><00:02:33.280><c> this</c><00:02:33.480><c> information</c>

00:02:33.949 --> 00:02:33.959 align:start position:0%
then we display all this information
 

00:02:33.959 --> 00:02:35.710 align:start position:0%
then we display all this information
back<00:02:34.120><c> to</c><00:02:34.319><c> them</c><00:02:34.560><c> and</c><00:02:34.720><c> we</c><00:02:34.800><c> can</c><00:02:34.959><c> do</c><00:02:35.200><c> this</c><00:02:35.480><c> by</c>

00:02:35.710 --> 00:02:35.720 align:start position:0%
back to them and we can do this by
 

00:02:35.720 --> 00:02:38.869 align:start position:0%
back to them and we can do this by
having<00:02:35.920><c> an</c><00:02:36.120><c> if</c><00:02:36.319><c> statement</c><00:02:36.720><c> so</c><00:02:37.040><c> if</c><00:02:37.480><c> st.</c><00:02:38.480><c> button</c>

00:02:38.869 --> 00:02:38.879 align:start position:0%
having an if statement so if st. button
 

00:02:38.879 --> 00:02:41.190 align:start position:0%
having an if statement so if st. button
we're<00:02:39.040><c> going</c><00:02:39.120><c> to</c><00:02:39.519><c> name</c><00:02:39.920><c> the</c><00:02:40.080><c> button</c><00:02:40.360><c> submit</c><00:02:41.000><c> so</c>

00:02:41.190 --> 00:02:41.200 align:start position:0%
we're going to name the button submit so
 

00:02:41.200 --> 00:02:43.869 align:start position:0%
we're going to name the button submit so
this<00:02:41.480><c> is</c><00:02:41.640><c> basically</c><00:02:41.959><c> saying</c><00:02:42.400><c> if</c><00:02:42.920><c> we</c><00:02:43.200><c> click</c><00:02:43.680><c> the</c>

00:02:43.869 --> 00:02:43.879 align:start position:0%
this is basically saying if we click the
 

00:02:43.879 --> 00:02:45.550 align:start position:0%
this is basically saying if we click the
button<00:02:44.400><c> then</c><00:02:44.560><c> we</c><00:02:44.640><c> can</c><00:02:44.800><c> do</c><00:02:45.000><c> something</c><00:02:45.360><c> okay</c>

00:02:45.550 --> 00:02:45.560 align:start position:0%
button then we can do something okay
 

00:02:45.560 --> 00:02:48.509 align:start position:0%
button then we can do something okay
we're<00:02:45.680><c> going</c><00:02:45.760><c> to</c><00:02:45.879><c> say</c><00:02:46.159><c> age</c><00:02:46.800><c> in</c><00:02:47.480><c> dog</c><00:02:48.000><c> years</c><00:02:48.400><c> is</c>

00:02:48.509 --> 00:02:48.519 align:start position:0%
we're going to say age in dog years is
 

00:02:48.519 --> 00:02:51.149 align:start position:0%
we're going to say age in dog years is
equal<00:02:48.800><c> to</c><00:02:49.200><c> age</c><00:02:49.720><c> *</c><00:02:50.080><c> sub</c><00:02:50.440><c> so</c><00:02:50.519><c> we're</c><00:02:50.680><c> taking</c><00:02:50.959><c> the</c>

00:02:51.149 --> 00:02:51.159 align:start position:0%
equal to age * sub so we're taking the
 

00:02:51.159 --> 00:02:53.190 align:start position:0%
equal to age * sub so we're taking the
age<00:02:51.400><c> variable</c><00:02:51.879><c> whatever</c><00:02:52.159><c> they</c><00:02:52.400><c> put</c><00:02:52.599><c> in</c><00:02:52.840><c> that</c>

00:02:53.190 --> 00:02:53.200 align:start position:0%
age variable whatever they put in that
 

00:02:53.200 --> 00:02:55.190 align:start position:0%
age variable whatever they put in that
and<00:02:53.319><c> then</c><00:02:53.440><c> we're</c><00:02:53.599><c> going</c><00:02:53.879><c> to</c><00:02:54.480><c> multiply</c><00:02:54.920><c> that</c><00:02:55.080><c> by</c>

00:02:55.190 --> 00:02:55.200 align:start position:0%
and then we're going to multiply that by
 

00:02:55.200 --> 00:02:56.790 align:start position:0%
and then we're going to multiply that by
seven<00:02:55.599><c> and</c><00:02:55.720><c> then</c><00:02:55.879><c> display</c><00:02:56.200><c> that</c><00:02:56.360><c> then</c><00:02:56.440><c> we</c><00:02:56.640><c> say</c>

00:02:56.790 --> 00:02:56.800 align:start position:0%
seven and then display that then we say
 

00:02:56.800 --> 00:02:59.430 align:start position:0%
seven and then display that then we say
st.<00:02:57.720><c> WR</c><00:02:58.120><c> and</c><00:02:58.239><c> this</c><00:02:58.360><c> is</c><00:02:58.440><c> a</c><00:02:58.599><c> common</c><00:02:58.879><c> one</c><00:02:59.239><c> when</c><00:02:59.360><c> you</c>

00:02:59.430 --> 00:02:59.440 align:start position:0%
st. WR and this is a common one when you
 

00:02:59.440 --> 00:03:01.910 align:start position:0%
st. WR and this is a common one when you
say<00:02:59.599><c> St</c><00:02:59.840><c> t.</c><00:03:00.239><c> WR</c><00:03:00.480><c> this</c><00:03:00.640><c> just</c><00:03:00.840><c> writes</c><00:03:01.360><c> text</c><00:03:01.640><c> onto</c>

00:03:01.910 --> 00:03:01.920 align:start position:0%
say St t. WR this just writes text onto
 

00:03:01.920 --> 00:03:03.390 align:start position:0%
say St t. WR this just writes text onto
the<00:03:02.040><c> screen</c><00:03:02.440><c> and</c><00:03:02.519><c> we're</c><00:03:02.599><c> going</c><00:03:02.720><c> to</c><00:03:02.879><c> format</c><00:03:03.239><c> the</c>

00:03:03.390 --> 00:03:03.400 align:start position:0%
the screen and we're going to format the
 

00:03:03.400 --> 00:03:05.470 align:start position:0%
the screen and we're going to format the
text<00:03:03.720><c> with</c><00:03:03.959><c> the</c><00:03:04.159><c> F</c><00:03:04.519><c> for</c><00:03:04.720><c> format</c><00:03:05.319><c> and</c><00:03:05.400><c> then</c>

00:03:05.470 --> 00:03:05.480 align:start position:0%
text with the F for format and then
 

00:03:05.480 --> 00:03:07.869 align:start position:0%
text with the F for format and then
we'll<00:03:05.640><c> say</c><00:03:05.879><c> hello</c><00:03:06.519><c> and</c><00:03:06.640><c> then</c><00:03:07.159><c> because</c><00:03:07.400><c> of</c><00:03:07.560><c> that</c>

00:03:07.869 --> 00:03:07.879 align:start position:0%
we'll say hello and then because of that
 

00:03:07.879 --> 00:03:10.030 align:start position:0%
we'll say hello and then because of that
we<00:03:08.000><c> can</c><00:03:08.480><c> have</c><00:03:08.680><c> these</c><00:03:08.840><c> curly</c><00:03:09.159><c> braces</c><00:03:09.599><c> and</c><00:03:09.760><c> then</c>

00:03:10.030 --> 00:03:10.040 align:start position:0%
we can have these curly braces and then
 

00:03:10.040 --> 00:03:11.910 align:start position:0%
we can have these curly braces and then
add<00:03:10.239><c> a</c><00:03:10.480><c> variable</c><00:03:11.000><c> inside</c><00:03:11.280><c> of</c><00:03:11.400><c> the</c><00:03:11.519><c> string</c><00:03:11.840><c> so</c>

00:03:11.910 --> 00:03:11.920 align:start position:0%
add a variable inside of the string so
 

00:03:11.920 --> 00:03:13.589 align:start position:0%
add a variable inside of the string so
we<00:03:12.000><c> can</c><00:03:12.120><c> say</c><00:03:12.280><c> hello</c><00:03:12.560><c> and</c><00:03:12.680><c> then</c><00:03:12.799><c> whatever</c><00:03:13.120><c> name</c>

00:03:13.589 --> 00:03:13.599 align:start position:0%
we can say hello and then whatever name
 

00:03:13.599 --> 00:03:15.949 align:start position:0%
we can say hello and then whatever name
they<00:03:13.840><c> give</c><00:03:14.159><c> as</c><00:03:14.319><c> the</c><00:03:14.519><c> text</c><00:03:14.760><c> input</c><00:03:15.159><c> on</c><00:03:15.360><c> line</c><00:03:15.599><c> five</c>

00:03:15.949 --> 00:03:15.959 align:start position:0%
they give as the text input on line five
 

00:03:15.959 --> 00:03:17.270 align:start position:0%
they give as the text input on line five
and<00:03:16.080><c> then</c><00:03:16.200><c> I</c><00:03:16.280><c> just</c><00:03:16.400><c> copy</c><00:03:16.640><c> and</c><00:03:16.760><c> pasted</c><00:03:17.080><c> this</c>

00:03:17.270 --> 00:03:17.280 align:start position:0%
and then I just copy and pasted this
 

00:03:17.280 --> 00:03:19.350 align:start position:0%
and then I just copy and pasted this
because<00:03:17.560><c> we're</c><00:03:17.720><c> going</c><00:03:17.840><c> to</c><00:03:18.000><c> do</c><00:03:18.440><c> the</c><00:03:18.680><c> same</c><00:03:19.040><c> thing</c>

00:03:19.350 --> 00:03:19.360 align:start position:0%
because we're going to do the same thing
 

00:03:19.360 --> 00:03:21.869 align:start position:0%
because we're going to do the same thing
with<00:03:19.519><c> the</c><00:03:19.799><c> age</c><00:03:20.239><c> so</c><00:03:20.400><c> we</c><00:03:20.560><c> say</c><00:03:20.760><c> you</c><00:03:20.920><c> are</c><00:03:21.519><c> this</c><00:03:21.680><c> many</c>

00:03:21.869 --> 00:03:21.879 align:start position:0%
with the age so we say you are this many
 

00:03:21.879 --> 00:03:24.789 align:start position:0%
with the age so we say you are this many
years<00:03:22.080><c> old</c><00:03:22.560><c> which</c><00:03:22.799><c> is</c><00:03:23.360><c> this</c><00:03:23.519><c> many</c><00:03:23.879><c> years</c><00:03:24.519><c> in</c>

00:03:24.789 --> 00:03:24.799 align:start position:0%
years old which is this many years in
 

00:03:24.799 --> 00:03:27.550 align:start position:0%
years old which is this many years in
dog<00:03:25.120><c> years</c><00:03:25.680><c> and</c><00:03:25.959><c> also</c><00:03:26.319><c> your</c><00:03:26.599><c> favorite</c><00:03:26.959><c> color</c>

00:03:27.550 --> 00:03:27.560 align:start position:0%
dog years and also your favorite color
 

00:03:27.560 --> 00:03:29.910 align:start position:0%
dog years and also your favorite color
is<00:03:27.959><c> whatever</c><00:03:28.760><c> you</c><00:03:29.200><c> add</c><00:03:29.400><c> uh</c><00:03:29.519><c> whatever</c><00:03:29.840><c> you</c>

00:03:29.910 --> 00:03:29.920 align:start position:0%
is whatever you add uh whatever you
 

00:03:29.920 --> 00:03:32.630 align:start position:0%
is whatever you add uh whatever you
chose<00:03:30.200><c> in</c><00:03:30.319><c> the</c><00:03:30.439><c> select</c><00:03:30.720><c> box</c><00:03:31.400><c> if</c><00:03:31.680><c> somebody</c><00:03:32.519><c> uh</c>

00:03:32.630 --> 00:03:32.640 align:start position:0%
chose in the select box if somebody uh
 

00:03:32.640 --> 00:03:34.270 align:start position:0%
chose in the select box if somebody uh
selects<00:03:33.080><c> this</c><00:03:33.239><c> check</c><00:03:33.519><c> box</c><00:03:33.840><c> then</c><00:03:33.959><c> we</c><00:03:34.040><c> want</c><00:03:34.200><c> to</c>

00:03:34.270 --> 00:03:34.280 align:start position:0%
selects this check box then we want to
 

00:03:34.280 --> 00:03:36.309 align:start position:0%
selects this check box then we want to
show<00:03:34.519><c> more</c><00:03:34.760><c> information</c><00:03:35.400><c> and</c><00:03:35.519><c> what</c><00:03:35.640><c> we</c><00:03:35.720><c> can</c><00:03:35.879><c> do</c>

00:03:36.309 --> 00:03:36.319 align:start position:0%
show more information and what we can do
 

00:03:36.319 --> 00:03:38.990 align:start position:0%
show more information and what we can do
is<00:03:36.480><c> we</c><00:03:36.560><c> can</c><00:03:36.760><c> say</c><00:03:37.120><c> if</c><00:03:37.480><c> show</c><00:03:38.080><c> more</c><00:03:38.519><c> because</c><00:03:38.760><c> it</c><00:03:38.879><c> is</c>

00:03:38.990 --> 00:03:39.000 align:start position:0%
is we can say if show more because it is
 

00:03:39.000 --> 00:03:41.070 align:start position:0%
is we can say if show more because it is
a<00:03:39.120><c> variable</c><00:03:39.760><c> this</c><00:03:39.879><c> is</c><00:03:40.080><c> basically</c><00:03:40.439><c> a</c><00:03:40.599><c> Boolean</c>

00:03:41.070 --> 00:03:41.080 align:start position:0%
a variable this is basically a Boolean
 

00:03:41.080 --> 00:03:43.390 align:start position:0%
a variable this is basically a Boolean
so<00:03:41.200><c> it's</c><00:03:41.360><c> either</c><00:03:41.560><c> true</c><00:03:41.760><c> or</c><00:03:41.959><c> false</c><00:03:42.720><c> if</c><00:03:43.040><c> show</c>

00:03:43.390 --> 00:03:43.400 align:start position:0%
so it's either true or false if show
 

00:03:43.400 --> 00:03:46.670 align:start position:0%
so it's either true or false if show
more<00:03:44.000><c> then</c><00:03:44.120><c> we</c><00:03:44.200><c> can</c><00:03:44.319><c> say</c><00:03:44.879><c> st.</c><00:03:45.879><c> right</c><00:03:46.319><c> here's</c>

00:03:46.670 --> 00:03:46.680 align:start position:0%
more then we can say st. right here's
 

00:03:46.680 --> 00:03:49.550 align:start position:0%
more then we can say st. right here's
more<00:03:47.360><c> information</c><00:03:48.360><c> just</c><00:03:48.680><c> for</c><00:03:48.959><c> you</c><00:03:49.319><c> and</c><00:03:49.400><c> now</c>

00:03:49.550 --> 00:03:49.560 align:start position:0%
more information just for you and now
 

00:03:49.560 --> 00:03:51.350 align:start position:0%
more information just for you and now
let's<00:03:49.760><c> just</c><00:03:49.920><c> give</c><00:03:50.200><c> some</c><00:03:50.560><c> sort</c><00:03:50.760><c> of</c><00:03:50.920><c> information</c>

00:03:51.350 --> 00:03:51.360 align:start position:0%
let's just give some sort of information
 

00:03:51.360 --> 00:03:53.949 align:start position:0%
let's just give some sort of information
about<00:03:51.599><c> dogs</c><00:03:51.879><c> so</c><00:03:52.000><c> we'll</c><00:03:52.120><c> say</c><00:03:52.280><c> st.</c><00:03:53.239><c> write</c><00:03:53.560><c> again</c>

00:03:53.949 --> 00:03:53.959 align:start position:0%
about dogs so we'll say st. write again
 

00:03:53.959 --> 00:03:55.910 align:start position:0%
about dogs so we'll say st. write again
this<00:03:54.040><c> is</c><00:03:54.239><c> going</c><00:03:54.360><c> to</c><00:03:54.480><c> be</c><00:03:54.640><c> common</c><00:03:55.280><c> dogs</c>

00:03:55.910 --> 00:03:55.920 align:start position:0%
this is going to be common dogs
 

00:03:55.920 --> 00:03:59.390 align:start position:0%
this is going to be common dogs
typically<00:03:56.640><c> live</c><00:03:57.000><c> to</c><00:03:57.239><c> be</c><00:03:57.599><c> around</c><00:03:58.079><c> 10</c><00:03:58.360><c> to</c><00:03:58.560><c> 13</c>

00:03:59.390 --> 00:03:59.400 align:start position:0%
typically live to be around 10 to 13
 

00:03:59.400 --> 00:04:02.270 align:start position:0%
typically live to be around 10 to 13
years<00:03:59.799><c> years</c><00:04:00.120><c> old</c><00:04:00.560><c> in</c><00:04:00.959><c> human</c><00:04:01.599><c> years</c><00:04:02.120><c> I</c><00:04:02.159><c> don't</c>

00:04:02.270 --> 00:04:02.280 align:start position:0%
years years old in human years I don't
 

00:04:02.280 --> 00:04:03.789 align:start position:0%
years years old in human years I don't
know<00:04:02.360><c> if</c><00:04:02.480><c> that's</c><00:04:02.640><c> correct</c><00:04:02.920><c> statement</c><00:04:03.200><c> or</c><00:04:03.319><c> not</c>

00:04:03.789 --> 00:04:03.799 align:start position:0%
know if that's correct statement or not
 

00:04:03.799 --> 00:04:05.550 align:start position:0%
know if that's correct statement or not
but<00:04:04.040><c> I'm</c><00:04:04.200><c> just</c><00:04:04.319><c> showing</c><00:04:04.640><c> you</c><00:04:04.879><c> an</c><00:04:05.079><c> example</c><00:04:05.439><c> all</c>

00:04:05.550 --> 00:04:05.560 align:start position:0%
but I'm just showing you an example all
 

00:04:05.560 --> 00:04:06.630 align:start position:0%
but I'm just showing you an example all
right<00:04:05.640><c> so</c><00:04:05.760><c> what</c><00:04:05.840><c> we've</c><00:04:06.040><c> done</c><00:04:06.200><c> here</c><00:04:06.360><c> is</c><00:04:06.480><c> we've</c>

00:04:06.630 --> 00:04:06.640 align:start position:0%
right so what we've done here is we've
 

00:04:06.640 --> 00:04:08.149 align:start position:0%
right so what we've done here is we've
added<00:04:06.920><c> submit</c><00:04:07.200><c> button</c><00:04:07.480><c> that</c><00:04:07.760><c> when</c><00:04:07.920><c> it's</c>

00:04:08.149 --> 00:04:08.159 align:start position:0%
added submit button that when it's
 

00:04:08.159 --> 00:04:11.429 align:start position:0%
added submit button that when it's
clicked<00:04:08.799><c> we</c><00:04:09.040><c> have</c><00:04:09.360><c> some</c><00:04:09.720><c> logic</c><00:04:10.239><c> and</c><00:04:10.439><c> then</c><00:04:11.120><c> if</c>

00:04:11.429 --> 00:04:11.439 align:start position:0%
clicked we have some logic and then if
 

00:04:11.439 --> 00:04:14.470 align:start position:0%
clicked we have some logic and then if
the<00:04:11.599><c> show</c><00:04:12.000><c> more</c><00:04:12.319><c> checkbox</c><00:04:13.040><c> is</c><00:04:13.519><c> checked</c><00:04:14.280><c> then</c>

00:04:14.470 --> 00:04:14.480 align:start position:0%
the show more checkbox is checked then
 

00:04:14.480 --> 00:04:16.469 align:start position:0%
the show more checkbox is checked then
we<00:04:14.680><c> also</c><00:04:15.079><c> show</c><00:04:15.480><c> something</c><00:04:15.879><c> else</c><00:04:16.120><c> onto</c><00:04:16.400><c> the</c>

00:04:16.469 --> 00:04:16.479 align:start position:0%
we also show something else onto the
 

00:04:16.479 --> 00:04:17.990 align:start position:0%
we also show something else onto the
screen<00:04:16.880><c> and</c><00:04:17.000><c> now</c><00:04:17.120><c> finally</c><00:04:17.400><c> what</c><00:04:17.479><c> we</c><00:04:17.600><c> can</c><00:04:17.680><c> do</c><00:04:17.880><c> is</c>

00:04:17.990 --> 00:04:18.000 align:start position:0%
screen and now finally what we can do is
 

00:04:18.000 --> 00:04:21.030 align:start position:0%
screen and now finally what we can do is
run<00:04:18.239><c> our</c><00:04:18.440><c> app</c><00:04:18.840><c> using</c><00:04:19.120><c> the</c><00:04:19.280><c> streamlit</c><00:04:19.919><c> CLI</c><00:04:20.919><c> what</c>

00:04:21.030 --> 00:04:21.040 align:start position:0%
run our app using the streamlit CLI what
 

00:04:21.040 --> 00:04:23.150 align:start position:0%
run our app using the streamlit CLI what
we<00:04:21.160><c> do</c><00:04:21.320><c> is</c><00:04:21.479><c> go</c><00:04:21.600><c> to</c><00:04:21.759><c> our</c><00:04:21.919><c> terminal</c><00:04:22.440><c> so</c><00:04:22.720><c> open</c><00:04:23.000><c> that</c>

00:04:23.150 --> 00:04:23.160 align:start position:0%
we do is go to our terminal so open that
 

00:04:23.160 --> 00:04:25.350 align:start position:0%
we do is go to our terminal so open that
up<00:04:23.440><c> we</c><00:04:23.560><c> are</c><00:04:24.080><c> already</c><00:04:24.440><c> in</c><00:04:24.560><c> the</c><00:04:24.720><c> directory</c><00:04:25.240><c> where</c>

00:04:25.350 --> 00:04:25.360 align:start position:0%
up we are already in the directory where
 

00:04:25.360 --> 00:04:28.110 align:start position:0%
up we are already in the directory where
our<00:04:25.639><c> main.py</c><00:04:26.639><c> uh</c><00:04:26.759><c> file</c><00:04:27.000><c> is</c><00:04:27.199><c> located</c><00:04:27.880><c> and</c><00:04:28.000><c> then</c>

00:04:28.110 --> 00:04:28.120 align:start position:0%
our main.py uh file is located and then
 

00:04:28.120 --> 00:04:31.710 align:start position:0%
our main.py uh file is located and then
we<00:04:28.240><c> just</c><00:04:28.400><c> type</c><00:04:28.600><c> in</c><00:04:28.840><c> streamlet</c><00:04:29.759><c> run</c><00:04:30.720><c> main.py</c>

00:04:31.710 --> 00:04:31.720 align:start position:0%
we just type in streamlet run main.py
 

00:04:31.720 --> 00:04:33.270 align:start position:0%
we just type in streamlet run main.py
and<00:04:31.800><c> this</c><00:04:31.919><c> is</c><00:04:32.000><c> going</c><00:04:32.120><c> to</c><00:04:32.240><c> run</c><00:04:32.440><c> a</c><00:04:32.639><c> local</c><00:04:32.919><c> server</c>

00:04:33.270 --> 00:04:33.280 align:start position:0%
and this is going to run a local server
 

00:04:33.280 --> 00:04:34.950 align:start position:0%
and this is going to run a local server
it<00:04:33.400><c> actually</c><00:04:33.600><c> happened</c><00:04:33.919><c> pretty</c><00:04:34.160><c> quick</c><00:04:34.720><c> and</c>

00:04:34.950 --> 00:04:34.960 align:start position:0%
it actually happened pretty quick and
 

00:04:34.960 --> 00:04:36.950 align:start position:0%
it actually happened pretty quick and
here<00:04:35.120><c> are</c><00:04:35.400><c> all</c><00:04:35.560><c> of</c><00:04:35.720><c> the</c><00:04:35.919><c> elements</c><00:04:36.520><c> on</c><00:04:36.800><c> the</c>

00:04:36.950 --> 00:04:36.960 align:start position:0%
here are all of the elements on the
 

00:04:36.960 --> 00:04:39.670 align:start position:0%
here are all of the elements on the
screen<00:04:37.400><c> so</c><00:04:37.520><c> let's</c><00:04:37.759><c> type</c><00:04:37.960><c> in</c><00:04:38.240><c> my</c><00:04:38.520><c> name</c><00:04:38.720><c> is</c><00:04:38.919><c> Tyler</c>

00:04:39.670 --> 00:04:39.680 align:start position:0%
screen so let's type in my name is Tyler
 

00:04:39.680 --> 00:04:42.390 align:start position:0%
screen so let's type in my name is Tyler
uh<00:04:39.840><c> let's</c><00:04:40.080><c> say</c><00:04:40.400><c> my</c><00:04:40.680><c> age</c><00:04:41.000><c> is</c><00:04:41.360><c> 30</c><00:04:41.960><c> that's</c><00:04:42.120><c> not</c><00:04:42.280><c> my</c>

00:04:42.390 --> 00:04:42.400 align:start position:0%
uh let's say my age is 30 that's not my
 

00:04:42.400 --> 00:04:44.790 align:start position:0%
uh let's say my age is 30 that's not my
actual<00:04:42.720><c> age</c><00:04:43.199><c> and</c><00:04:43.360><c> my</c><00:04:43.560><c> favorite</c><00:04:43.919><c> color</c><00:04:44.440><c> out</c><00:04:44.560><c> of</c>

00:04:44.790 --> 00:04:44.800 align:start position:0%
actual age and my favorite color out of
 

00:04:44.800 --> 00:04:47.150 align:start position:0%
actual age and my favorite color out of
these<00:04:45.000><c> three</c><00:04:45.320><c> options</c><00:04:46.080><c> let's</c><00:04:46.280><c> say</c><00:04:46.560><c> orange</c>

00:04:47.150 --> 00:04:47.160 align:start position:0%
these three options let's say orange
 

00:04:47.160 --> 00:04:48.870 align:start position:0%
these three options let's say orange
okay<00:04:47.280><c> and</c><00:04:47.400><c> you</c><00:04:47.479><c> can</c><00:04:47.600><c> see</c><00:04:48.160><c> this</c><00:04:48.240><c> is</c><00:04:48.440><c> the</c><00:04:48.600><c> step</c>

00:04:48.870 --> 00:04:48.880 align:start position:0%
okay and you can see this is the step
 

00:04:48.880 --> 00:04:50.590 align:start position:0%
okay and you can see this is the step
counter<00:04:49.240><c> so</c><00:04:49.440><c> whenever</c><00:04:49.759><c> you</c><00:04:49.919><c> increment</c><00:04:50.280><c> or</c>

00:04:50.590 --> 00:04:50.600 align:start position:0%
counter so whenever you increment or
 

00:04:50.600 --> 00:04:52.909 align:start position:0%
counter so whenever you increment or
increment<00:04:51.080><c> or</c><00:04:51.720><c> uh</c><00:04:51.840><c> decrement</c><00:04:52.360><c> it</c><00:04:52.520><c> goes</c><00:04:52.720><c> down</c>

00:04:52.909 --> 00:04:52.919 align:start position:0%
increment or uh decrement it goes down
 

00:04:52.919 --> 00:04:54.950 align:start position:0%
increment or uh decrement it goes down
or<00:04:53.080><c> up</c><00:04:53.280><c> by</c><00:04:53.440><c> one</c><00:04:53.919><c> and</c><00:04:54.000><c> then</c><00:04:54.160><c> if</c><00:04:54.240><c> I</c><00:04:54.360><c> hit</c><00:04:54.520><c> submit</c>

00:04:54.950 --> 00:04:54.960 align:start position:0%
or up by one and then if I hit submit
 

00:04:54.960 --> 00:04:56.629 align:start position:0%
or up by one and then if I hit submit
we're<00:04:55.160><c> basically</c><00:04:55.440><c> just</c><00:04:55.560><c> regurgitating</c><00:04:56.360><c> this</c>

00:04:56.629 --> 00:04:56.639 align:start position:0%
we're basically just regurgitating this
 

00:04:56.639 --> 00:04:58.710 align:start position:0%
we're basically just regurgitating this
information<00:04:57.240><c> or</c><00:04:57.520><c> the</c><00:04:57.639><c> logic</c><00:04:58.000><c> that</c><00:04:58.080><c> we</c><00:04:58.199><c> created</c>

00:04:58.710 --> 00:04:58.720 align:start position:0%
information or the logic that we created
 

00:04:58.720 --> 00:05:01.189 align:start position:0%
information or the logic that we created
from<00:04:59.039><c> the</c><00:04:59.199><c> submit</c><00:04:59.759><c> button</c><00:05:00.160><c> now</c><00:05:00.440><c> also</c><00:05:00.880><c> there's</c>

00:05:01.189 --> 00:05:01.199 align:start position:0%
from the submit button now also there's
 

00:05:01.199 --> 00:05:02.909 align:start position:0%
from the submit button now also there's
this<00:05:01.400><c> show</c><00:05:01.680><c> more</c><00:05:01.880><c> information</c><00:05:02.280><c> here</c><00:05:02.479><c> so</c><00:05:02.639><c> if</c><00:05:02.759><c> I</c>

00:05:02.909 --> 00:05:02.919 align:start position:0%
this show more information here so if I
 

00:05:02.919 --> 00:05:04.990 align:start position:0%
this show more information here so if I
select<00:05:03.360><c> this</c><00:05:03.759><c> here's</c><00:05:04.039><c> more</c><00:05:04.320><c> information</c><00:05:04.880><c> just</c>

00:05:04.990 --> 00:05:05.000 align:start position:0%
select this here's more information just
 

00:05:05.000 --> 00:05:08.070 align:start position:0%
select this here's more information just
for<00:05:05.240><c> you</c><00:05:05.800><c> dogs</c><00:05:06.199><c> typically</c><00:05:06.639><c> live</c><00:05:07.039><c> around</c><00:05:07.280><c> 103</c>

00:05:08.070 --> 00:05:08.080 align:start position:0%
for you dogs typically live around 103
 

00:05:08.080 --> 00:05:10.350 align:start position:0%
for you dogs typically live around 103
years<00:05:08.479><c> old</c><00:05:08.840><c> in</c><00:05:09.160><c> human</c><00:05:09.600><c> years</c><00:05:10.039><c> and</c><00:05:10.160><c> we</c><00:05:10.280><c> can</c>

00:05:10.350 --> 00:05:10.360 align:start position:0%
years old in human years and we can
 

00:05:10.360 --> 00:05:12.390 align:start position:0%
years old in human years and we can
click<00:05:10.600><c> submit</c><00:05:10.880><c> button</c><00:05:11.120><c> again</c><00:05:11.520><c> and</c><00:05:11.759><c> it</c><00:05:11.880><c> shows</c>

00:05:12.390 --> 00:05:12.400 align:start position:0%
click submit button again and it shows
 

00:05:12.400 --> 00:05:13.749 align:start position:0%
click submit button again and it shows
everything<00:05:12.800><c> if</c><00:05:12.880><c> you</c><00:05:12.960><c> enjoy</c><00:05:13.240><c> this</c><00:05:13.360><c> tutorial</c>

00:05:13.749 --> 00:05:13.759 align:start position:0%
everything if you enjoy this tutorial
 

00:05:13.759 --> 00:05:15.790 align:start position:0%
everything if you enjoy this tutorial
and<00:05:13.880><c> want</c><00:05:13.960><c> to</c><00:05:14.080><c> see</c><00:05:14.320><c> more</c><00:05:14.840><c> Ai</c><00:05:15.280><c> and</c><00:05:15.600><c> app</c>

00:05:15.790 --> 00:05:15.800 align:start position:0%
and want to see more Ai and app
 

00:05:15.800 --> 00:05:16.909 align:start position:0%
and want to see more Ai and app
development<00:05:16.199><c> please</c><00:05:16.400><c> subscribe</c><00:05:16.720><c> to</c><00:05:16.840><c> the</c>

00:05:16.909 --> 00:05:16.919 align:start position:0%
development please subscribe to the
 

00:05:16.919 --> 00:05:19.029 align:start position:0%
development please subscribe to the
channel<00:05:17.240><c> and</c><00:05:17.360><c> if</c><00:05:17.440><c> you</c><00:05:17.639><c> like</c><00:05:17.960><c> this</c><00:05:18.199><c> video</c><00:05:18.919><c> I</c>

00:05:19.029 --> 00:05:19.039 align:start position:0%
channel and if you like this video I
 

00:05:19.039 --> 00:05:21.430 align:start position:0%
channel and if you like this video I
have<00:05:19.240><c> two</c><00:05:19.479><c> courses</c><00:05:20.240><c> that</c><00:05:20.400><c> get</c><00:05:20.520><c> you</c><00:05:20.720><c> started</c><00:05:21.199><c> in</c>

00:05:21.430 --> 00:05:21.440 align:start position:0%
have two courses that get you started in
 

00:05:21.440 --> 00:05:23.670 align:start position:0%
have two courses that get you started in
AI<00:05:21.759><c> development</c><00:05:22.240><c> using</c><00:05:22.600><c> autogen</c><00:05:23.240><c> which</c><00:05:23.360><c> is</c><00:05:23.479><c> a</c>

00:05:23.670 --> 00:05:23.680 align:start position:0%
AI development using autogen which is a
 

00:05:23.680 --> 00:05:25.150 align:start position:0%
AI development using autogen which is a
multi-agent<00:05:24.360><c> framework</c><00:05:24.840><c> if</c><00:05:24.919><c> you</c><00:05:25.000><c> don't</c><00:05:25.080><c> know</c>

00:05:25.150 --> 00:05:25.160 align:start position:0%
multi-agent framework if you don't know
 

00:05:25.160 --> 00:05:26.390 align:start position:0%
multi-agent framework if you don't know
what<00:05:25.240><c> that</c><00:05:25.400><c> is</c><00:05:25.600><c> that's</c><00:05:25.800><c> fine</c><00:05:26.039><c> because</c><00:05:26.240><c> I</c>

00:05:26.390 --> 00:05:26.400 align:start position:0%
what that is that's fine because I
 

00:05:26.400 --> 00:05:27.950 align:start position:0%
what that is that's fine because I
explain<00:05:26.720><c> it</c><00:05:26.840><c> in</c><00:05:26.960><c> the</c><00:05:27.080><c> videos</c><00:05:27.600><c> thank</c><00:05:27.720><c> you</c><00:05:27.800><c> for</c>

00:05:27.950 --> 00:05:27.960 align:start position:0%
explain it in the videos thank you for
 

00:05:27.960 --> 00:05:31.639 align:start position:0%
explain it in the videos thank you for
watching<00:05:28.360><c> I'll</c><00:05:28.479><c> see</c><00:05:28.639><c> you</c><00:05:28.840><c> next</c><00:05:29.080><c> video</c>

