WEBVTT
Kind: captions
Language: en

00:00:00.599 --> 00:00:02.450 align:start position:0%
 
hey<00:00:01.079><c> there</c><00:00:01.260><c> it's</c><00:00:01.680><c> Matt</c><00:00:01.860><c> <PERSON></c><00:00:02.100><c> your</c>

00:00:02.450 --> 00:00:02.460 align:start position:0%
hey there it's <PERSON> your
 

00:00:02.460 --> 00:00:05.030 align:start position:0%
hey there it's <PERSON> your
favorite<00:00:02.580><c> evangelist</c><00:00:03.179><c> again</c><00:00:03.620><c> and</c><00:00:04.620><c> you</c><00:00:04.920><c> know</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
favorite evangelist again and you know
 

00:00:05.040 --> 00:00:06.950 align:start position:0%
favorite evangelist again and you know
last<00:00:05.279><c> time</c><00:00:05.520><c> I</c><00:00:05.880><c> did</c><00:00:05.940><c> a</c><00:00:06.120><c> video</c><00:00:06.240><c> it</c><00:00:06.540><c> was</c><00:00:06.660><c> just</c><00:00:06.839><c> a</c>

00:00:06.950 --> 00:00:06.960 align:start position:0%
last time I did a video it was just a
 

00:00:06.960 --> 00:00:09.110 align:start position:0%
last time I did a video it was just a
couple<00:00:07.140><c> of</c><00:00:07.259><c> days</c><00:00:07.440><c> ago</c><00:00:07.740><c> and</c><00:00:08.220><c> it</c><00:00:08.760><c> was</c><00:00:08.880><c> so</c>

00:00:09.110 --> 00:00:09.120 align:start position:0%
couple of days ago and it was so
 

00:00:09.120 --> 00:00:11.209 align:start position:0%
couple of days ago and it was so
satisfying<00:00:09.660><c> you</c><00:00:10.019><c> know</c><00:00:10.139><c> why</c><00:00:10.260><c> because</c><00:00:10.800><c> I</c>

00:00:11.209 --> 00:00:11.219 align:start position:0%
satisfying you know why because I
 

00:00:11.219 --> 00:00:15.589 align:start position:0%
satisfying you know why because I
thought<00:00:11.400><c> of</c><00:00:11.519><c> the</c><00:00:11.700><c> idea</c><00:00:12.440><c> I</c><00:00:13.639><c> recorded</c><00:00:14.639><c> it</c><00:00:14.820><c> I</c>

00:00:15.589 --> 00:00:15.599 align:start position:0%
thought of the idea I recorded it I
 

00:00:15.599 --> 00:00:18.109 align:start position:0%
thought of the idea I recorded it I
recorded<00:00:16.199><c> the</c><00:00:16.440><c> extra</c><00:00:16.740><c> screencasting</c><00:00:17.699><c> bits</c><00:00:18.000><c> of</c>

00:00:18.109 --> 00:00:18.119 align:start position:0%
recorded the extra screencasting bits of
 

00:00:18.119 --> 00:00:21.410 align:start position:0%
recorded the extra screencasting bits of
it<00:00:18.260><c> and</c><00:00:19.260><c> I</c><00:00:19.560><c> edited</c><00:00:19.920><c> it</c><00:00:20.100><c> down</c><00:00:20.340><c> and</c><00:00:20.880><c> I</c><00:00:21.060><c> published</c>

00:00:21.410 --> 00:00:21.420 align:start position:0%
it and I edited it down and I published
 

00:00:21.420 --> 00:00:23.689 align:start position:0%
it and I edited it down and I published
it<00:00:21.539><c> all</c><00:00:21.900><c> within</c><00:00:22.260><c> a</c><00:00:22.439><c> span</c><00:00:22.680><c> of</c><00:00:22.800><c> like</c>

00:00:23.689 --> 00:00:23.699 align:start position:0%
it all within a span of like
 

00:00:23.699 --> 00:00:26.570 align:start position:0%
it all within a span of like
hour<00:00:24.060><c> and</c><00:00:24.300><c> a</c><00:00:24.420><c> half</c><00:00:24.480><c> to</c><00:00:24.840><c> two</c><00:00:25.019><c> hours</c><00:00:25.220><c> and</c><00:00:26.220><c> in</c><00:00:26.460><c> the</c>

00:00:26.570 --> 00:00:26.580 align:start position:0%
hour and a half to two hours and in the
 

00:00:26.580 --> 00:00:28.910 align:start position:0%
hour and a half to two hours and in the
past<00:00:26.699><c> that's</c><00:00:27.119><c> always</c><00:00:27.359><c> taken</c><00:00:27.779><c> me</c>

00:00:28.910 --> 00:00:28.920 align:start position:0%
past that's always taken me
 

00:00:28.920 --> 00:00:32.089 align:start position:0%
past that's always taken me
many<00:00:29.760><c> many</c><00:00:29.880><c> many</c><00:00:30.180><c> hours</c><00:00:30.840><c> and</c><00:00:31.679><c> so</c><00:00:31.800><c> I</c><00:00:31.980><c> thought</c>

00:00:32.089 --> 00:00:32.099 align:start position:0%
many many many hours and so I thought
 

00:00:32.099 --> 00:00:34.069 align:start position:0%
many many many hours and so I thought
I'd<00:00:32.220><c> do</c><00:00:32.399><c> another</c><00:00:32.520><c> one</c><00:00:32.759><c> also</c><00:00:33.480><c> super</c><00:00:33.840><c> quick</c>

00:00:34.069 --> 00:00:34.079 align:start position:0%
I'd do another one also super quick
 

00:00:34.079 --> 00:00:36.170 align:start position:0%
I'd do another one also super quick
because<00:00:34.559><c> I</c><00:00:34.860><c> got</c><00:00:34.980><c> this</c><00:00:35.160><c> idea</c><00:00:35.399><c> in</c><00:00:35.460><c> my</c><00:00:35.640><c> head</c><00:00:35.760><c> I</c>

00:00:36.170 --> 00:00:36.180 align:start position:0%
because I got this idea in my head I
 

00:00:36.180 --> 00:00:38.090 align:start position:0%
because I got this idea in my head I
just<00:00:36.300><c> want</c><00:00:36.420><c> to</c><00:00:36.540><c> get</c><00:00:36.660><c> it</c><00:00:36.840><c> out</c><00:00:37.020><c> there</c><00:00:37.200><c> and</c><00:00:37.800><c> you</c>

00:00:38.090 --> 00:00:38.100 align:start position:0%
just want to get it out there and you
 

00:00:38.100 --> 00:00:39.770 align:start position:0%
just want to get it out there and you
know<00:00:38.219><c> see</c><00:00:38.340><c> if</c><00:00:38.460><c> I</c><00:00:38.579><c> can</c><00:00:38.700><c> get</c><00:00:38.880><c> you</c><00:00:39.120><c> know</c><00:00:39.300><c> two</c><00:00:39.540><c> two</c>

00:00:39.770 --> 00:00:39.780 align:start position:0%
know see if I can get you know two two
 

00:00:39.780 --> 00:00:41.389 align:start position:0%
know see if I can get you know two two
videos<00:00:40.020><c> in</c><00:00:40.260><c> a</c><00:00:40.379><c> week</c><00:00:40.500><c> is</c><00:00:40.739><c> will</c><00:00:41.100><c> be</c><00:00:41.280><c> pretty</c>

00:00:41.389 --> 00:00:41.399 align:start position:0%
videos in a week is will be pretty
 

00:00:41.399 --> 00:00:44.450 align:start position:0%
videos in a week is will be pretty
awesome<00:00:41.719><c> so</c><00:00:42.719><c> uh</c><00:00:43.140><c> what</c><00:00:43.620><c> I</c><00:00:43.739><c> wanted</c><00:00:43.860><c> to</c><00:00:44.040><c> look</c><00:00:44.340><c> at</c>

00:00:44.450 --> 00:00:44.460 align:start position:0%
awesome so uh what I wanted to look at
 

00:00:44.460 --> 00:00:47.930 align:start position:0%
awesome so uh what I wanted to look at
is<00:00:44.879><c> how</c><00:00:45.420><c> was</c><00:00:45.600><c> I</c><00:00:45.780><c> able</c><00:00:46.079><c> to</c><00:00:46.200><c> do</c><00:00:46.620><c> it</c><00:00:46.800><c> so</c><00:00:47.160><c> fast</c><00:00:47.399><c> and</c>

00:00:47.930 --> 00:00:47.940 align:start position:0%
is how was I able to do it so fast and
 

00:00:47.940 --> 00:00:50.830 align:start position:0%
is how was I able to do it so fast and
so<00:00:48.120><c> uh</c><00:00:48.780><c> by</c><00:00:49.079><c> the</c><00:00:49.200><c> way</c><00:00:49.260><c> I'm</c><00:00:49.559><c> now</c><00:00:49.860><c> downstairs</c><00:00:50.219><c> yeah</c>

00:00:50.830 --> 00:00:50.840 align:start position:0%
so uh by the way I'm now downstairs yeah
 

00:00:50.840 --> 00:00:53.090 align:start position:0%
so uh by the way I'm now downstairs yeah
last<00:00:51.840><c> time</c><00:00:52.020><c> I</c><00:00:52.079><c> was</c><00:00:52.260><c> upstairs</c><00:00:52.379><c> in</c><00:00:52.800><c> my</c><00:00:52.920><c> uh</c>

00:00:53.090 --> 00:00:53.100 align:start position:0%
last time I was upstairs in my uh
 

00:00:53.100 --> 00:00:54.950 align:start position:0%
last time I was upstairs in my uh
bedroom<00:00:53.340><c> this</c><00:00:53.760><c> time</c><00:00:53.940><c> I'm</c><00:00:54.300><c> kind</c><00:00:54.539><c> of</c><00:00:54.600><c> in</c><00:00:54.780><c> the</c>

00:00:54.950 --> 00:00:54.960 align:start position:0%
bedroom this time I'm kind of in the
 

00:00:54.960 --> 00:00:56.990 align:start position:0%
bedroom this time I'm kind of in the
kitchen<00:00:55.079><c> so</c><00:00:55.440><c> the</c><00:00:55.860><c> reason</c><00:00:56.039><c> why</c><00:00:56.340><c> I</c><00:00:56.460><c> was</c><00:00:56.699><c> able</c><00:00:56.879><c> to</c>

00:00:56.990 --> 00:00:57.000 align:start position:0%
kitchen so the reason why I was able to
 

00:00:57.000 --> 00:01:00.770 align:start position:0%
kitchen so the reason why I was able to
do<00:00:57.120><c> that</c><00:00:57.360><c> video</c><00:00:57.539><c> so</c><00:00:58.079><c> fast</c><00:00:58.460><c> was</c><00:00:59.600><c> mostly</c><00:01:00.600><c> around</c>

00:01:00.770 --> 00:01:00.780 align:start position:0%
do that video so fast was mostly around
 

00:01:00.780 --> 00:01:02.689 align:start position:0%
do that video so fast was mostly around
keyboard<00:01:01.260><c> shortcuts</c><00:01:01.980><c> you</c><00:01:02.280><c> know</c><00:01:02.399><c> there's</c>

00:01:02.689 --> 00:01:02.699 align:start position:0%
keyboard shortcuts you know there's
 

00:01:02.699 --> 00:01:06.109 align:start position:0%
keyboard shortcuts you know there's
really<00:01:03.480><c> nine</c><00:01:04.199><c> keyboard</c><00:01:04.680><c> shortcuts</c><00:01:05.400><c> that</c><00:01:05.640><c> I</c>

00:01:06.109 --> 00:01:06.119 align:start position:0%
really nine keyboard shortcuts that I
 

00:01:06.119 --> 00:01:09.050 align:start position:0%
really nine keyboard shortcuts that I
need<00:01:06.299><c> to</c><00:01:06.479><c> use</c><00:01:06.720><c> to</c><00:01:07.680><c> edit</c><00:01:08.040><c> a</c><00:01:08.280><c> video</c><00:01:08.400><c> down</c><00:01:08.760><c> and</c>

00:01:09.050 --> 00:01:09.060 align:start position:0%
need to use to edit a video down and
 

00:01:09.060 --> 00:01:10.969 align:start position:0%
need to use to edit a video down and
there's<00:01:09.659><c> lots</c><00:01:10.020><c> of</c><00:01:10.080><c> keyboard</c><00:01:10.380><c> shortcuts</c><00:01:10.920><c> to</c>

00:01:10.969 --> 00:01:10.979 align:start position:0%
there's lots of keyboard shortcuts to
 

00:01:10.979 --> 00:01:12.710 align:start position:0%
there's lots of keyboard shortcuts to
use<00:01:11.159><c> but</c><00:01:11.400><c> you</c><00:01:11.700><c> know</c><00:01:11.820><c> I've</c><00:01:12.000><c> gotten</c><00:01:12.299><c> it</c><00:01:12.420><c> down</c><00:01:12.540><c> to</c>

00:01:12.710 --> 00:01:12.720 align:start position:0%
use but you know I've gotten it down to
 

00:01:12.720 --> 00:01:16.550 align:start position:0%
use but you know I've gotten it down to
like<00:01:12.900><c> the</c><00:01:13.560><c> key</c><00:01:14.100><c> Nine</c><00:01:14.700><c> shortcuts</c><00:01:15.420><c> and</c><00:01:16.080><c> they're</c>

00:01:16.550 --> 00:01:16.560 align:start position:0%
like the key Nine shortcuts and they're
 

00:01:16.560 --> 00:01:18.350 align:start position:0%
like the key Nine shortcuts and they're
even<00:01:16.799><c> knowing</c><00:01:17.400><c> the</c><00:01:17.580><c> shortcuts</c><00:01:18.000><c> they're</c><00:01:18.240><c> a</c>

00:01:18.350 --> 00:01:18.360 align:start position:0%
even knowing the shortcuts they're a
 

00:01:18.360 --> 00:01:21.050 align:start position:0%
even knowing the shortcuts they're a
little<00:01:18.420><c> awkward</c><00:01:18.900><c> to</c><00:01:19.320><c> use</c><00:01:19.560><c> and</c><00:01:20.220><c> so</c><00:01:20.520><c> I</c><00:01:20.640><c> also</c><00:01:20.880><c> have</c>

00:01:21.050 --> 00:01:21.060 align:start position:0%
little awkward to use and so I also have
 

00:01:21.060 --> 00:01:22.249 align:start position:0%
little awkward to use and so I also have
this<00:01:21.299><c> little</c>

00:01:22.249 --> 00:01:22.259 align:start position:0%
this little
 

00:01:22.259 --> 00:01:24.770 align:start position:0%
this little
um<00:01:22.320><c> macro</c><00:01:22.979><c> keypad</c><00:01:23.520><c> that's</c><00:01:23.759><c> also</c><00:01:24.119><c> super</c><00:01:24.299><c> useful</c>

00:01:24.770 --> 00:01:24.780 align:start position:0%
um macro keypad that's also super useful
 

00:01:24.780 --> 00:01:26.630 align:start position:0%
um macro keypad that's also super useful
I<00:01:25.140><c> might</c><00:01:25.259><c> go</c><00:01:25.439><c> into</c><00:01:25.560><c> more</c><00:01:25.860><c> detail</c><00:01:26.159><c> about</c><00:01:26.280><c> that</c>

00:01:26.630 --> 00:01:26.640 align:start position:0%
I might go into more detail about that
 

00:01:26.640 --> 00:01:28.609 align:start position:0%
I might go into more detail about that
in<00:01:27.420><c> another</c><00:01:27.540><c> video</c><00:01:27.900><c> because</c><00:01:28.140><c> just</c><00:01:28.320><c> covering</c>

00:01:28.609 --> 00:01:28.619 align:start position:0%
in another video because just covering
 

00:01:28.619 --> 00:01:30.410 align:start position:0%
in another video because just covering
the<00:01:28.680><c> key</c><00:01:28.799><c> keyboard</c><00:01:29.220><c> shortcuts</c><00:01:29.820><c> is</c><00:01:30.119><c> going</c><00:01:30.360><c> to</c>

00:01:30.410 --> 00:01:30.420 align:start position:0%
the key keyboard shortcuts is going to
 

00:01:30.420 --> 00:01:32.149 align:start position:0%
the key keyboard shortcuts is going to
take<00:01:30.540><c> a</c><00:01:30.659><c> little</c><00:01:30.720><c> bit</c><00:01:30.840><c> of</c><00:01:30.960><c> time</c><00:01:31.080><c> so</c><00:01:31.380><c> let's</c><00:01:31.860><c> get</c>

00:01:32.149 --> 00:01:32.159 align:start position:0%
take a little bit of time so let's get
 

00:01:32.159 --> 00:01:33.890 align:start position:0%
take a little bit of time so let's get
onto<00:01:32.580><c> my</c><00:01:32.759><c> computer</c><00:01:32.880><c> and</c><00:01:33.240><c> so</c><00:01:33.420><c> I</c><00:01:33.540><c> can</c><00:01:33.600><c> show</c><00:01:33.780><c> you</c>

00:01:33.890 --> 00:01:33.900 align:start position:0%
onto my computer and so I can show you
 

00:01:33.900 --> 00:01:35.929 align:start position:0%
onto my computer and so I can show you
those<00:01:34.320><c> nine</c><00:01:34.740><c> keyboard</c><00:01:35.100><c> shortcuts</c><00:01:35.640><c> for</c><00:01:35.820><c> Final</c>

00:01:35.929 --> 00:01:35.939 align:start position:0%
those nine keyboard shortcuts for Final
 

00:01:35.939 --> 00:01:38.210 align:start position:0%
those nine keyboard shortcuts for Final
Cut<00:01:36.180><c> Pro</c><00:01:36.420><c> that</c><00:01:36.780><c> let</c><00:01:36.900><c> me</c><00:01:37.079><c> do</c><00:01:37.439><c> something</c><00:01:37.799><c> like</c>

00:01:38.210 --> 00:01:38.220 align:start position:0%
Cut Pro that let me do something like
 

00:01:38.220 --> 00:01:40.370 align:start position:0%
Cut Pro that let me do something like
recording<00:01:39.180><c> a</c><00:01:39.299><c> video</c><00:01:39.479><c> and</c><00:01:39.900><c> getting</c><00:01:40.079><c> it</c><00:01:40.259><c> up</c>

00:01:40.370 --> 00:01:40.380 align:start position:0%
recording a video and getting it up
 

00:01:40.380 --> 00:01:42.289 align:start position:0%
recording a video and getting it up
there<00:01:40.560><c> within</c><00:01:40.979><c> an</c><00:01:41.220><c> hour</c><00:01:41.340><c> and</c><00:01:41.460><c> a</c><00:01:41.579><c> half</c><00:01:41.640><c> okay</c>

00:01:42.289 --> 00:01:42.299 align:start position:0%
there within an hour and a half okay
 

00:01:42.299 --> 00:01:45.830 align:start position:0%
there within an hour and a half okay
let's<00:01:42.659><c> go</c><00:01:42.840><c> to</c><00:01:42.960><c> it</c><00:01:43.140><c> okay</c><00:01:43.500><c> so</c><00:01:44.100><c> here</c><00:01:44.579><c> I</c><00:01:44.759><c> am</c><00:01:44.939><c> at</c>

00:01:45.830 --> 00:01:45.840 align:start position:0%
let's go to it okay so here I am at
 

00:01:45.840 --> 00:01:50.030 align:start position:0%
let's go to it okay so here I am at
Final<00:01:46.560><c> Cut</c><00:01:46.860><c> Pro</c><00:01:47.240><c> and</c><00:01:48.240><c> I've</c><00:01:48.720><c> got</c><00:01:49.020><c> the</c><00:01:49.860><c> video</c>

00:01:50.030 --> 00:01:50.040 align:start position:0%
Final Cut Pro and I've got the video
 

00:01:50.040 --> 00:01:52.249 align:start position:0%
Final Cut Pro and I've got the video
that<00:01:50.399><c> I</c><00:01:50.579><c> created</c><00:01:50.880><c> last</c><00:01:51.180><c> week</c><00:01:51.420><c> now</c><00:01:51.840><c> it's</c><00:01:52.079><c> a</c>

00:01:52.249 --> 00:01:52.259 align:start position:0%
that I created last week now it's a
 

00:01:52.259 --> 00:01:54.710 align:start position:0%
that I created last week now it's a
super<00:01:52.439><c> simple</c><00:01:52.680><c> timeline</c><00:01:53.360><c> and</c><00:01:54.360><c> you</c><00:01:54.600><c> know</c>

00:01:54.710 --> 00:01:54.720 align:start position:0%
super simple timeline and you know
 

00:01:54.720 --> 00:01:56.630 align:start position:0%
super simple timeline and you know
that's<00:01:54.840><c> probably</c><00:01:55.079><c> best</c><00:01:55.560><c> for</c><00:01:56.100><c> this</c><00:01:56.399><c> kind</c><00:01:56.520><c> of</c>

00:01:56.630 --> 00:01:56.640 align:start position:0%
that's probably best for this kind of
 

00:01:56.640 --> 00:01:58.850 align:start position:0%
that's probably best for this kind of
demo<00:01:57.000><c> because</c><00:01:57.360><c> I</c><00:01:57.659><c> can</c><00:01:57.840><c> just</c><00:01:58.020><c> really</c><00:01:58.320><c> get</c><00:01:58.619><c> into</c>

00:01:58.850 --> 00:01:58.860 align:start position:0%
demo because I can just really get into
 

00:01:58.860 --> 00:02:00.830 align:start position:0%
demo because I can just really get into
what<00:01:59.520><c> are</c><00:01:59.700><c> the</c><00:01:59.880><c> keyboard</c><00:02:00.119><c> shortcuts</c><00:02:00.720><c> without</c>

00:02:00.830 --> 00:02:00.840 align:start position:0%
what are the keyboard shortcuts without
 

00:02:00.840 --> 00:02:03.889 align:start position:0%
what are the keyboard shortcuts without
worrying<00:02:01.259><c> about</c><00:02:01.380><c> how</c><00:02:02.040><c> pretty</c><00:02:02.280><c> my</c><00:02:02.520><c> timeline</c><00:02:02.880><c> is</c>

00:02:03.889 --> 00:02:03.899 align:start position:0%
worrying about how pretty my timeline is
 

00:02:03.899 --> 00:02:06.590 align:start position:0%
worrying about how pretty my timeline is
so<00:02:04.560><c> the</c><00:02:05.100><c> first</c><00:02:05.340><c> keyboard</c><00:02:05.880><c> shortcut</c><00:02:06.299><c> that's</c>

00:02:06.590 --> 00:02:06.600 align:start position:0%
so the first keyboard shortcut that's
 

00:02:06.600 --> 00:02:09.529 align:start position:0%
so the first keyboard shortcut that's
super<00:02:06.840><c> important</c><00:02:07.259><c> is</c><00:02:07.560><c> jkl</c><00:02:08.220><c> uh</c><00:02:08.880><c> that</c><00:02:09.060><c> moves</c><00:02:09.420><c> you</c>

00:02:09.529 --> 00:02:09.539 align:start position:0%
super important is jkl uh that moves you
 

00:02:09.539 --> 00:02:12.229 align:start position:0%
super important is jkl uh that moves you
around<00:02:09.720><c> the</c><00:02:10.020><c> timeline</c><00:02:10.440><c> and</c><00:02:11.220><c> it</c><00:02:11.520><c> took</c><00:02:11.760><c> me</c><00:02:11.879><c> a</c>

00:02:12.229 --> 00:02:12.239 align:start position:0%
around the timeline and it took me a
 

00:02:12.239 --> 00:02:16.309 align:start position:0%
around the timeline and it took me a
long<00:02:12.360><c> time</c><00:02:12.720><c> to</c><00:02:13.319><c> really</c><00:02:13.860><c> get</c><00:02:14.819><c> into</c><00:02:15.000><c> using</c><00:02:15.480><c> jkl</c><00:02:16.080><c> I</c>

00:02:16.309 --> 00:02:16.319 align:start position:0%
long time to really get into using jkl I
 

00:02:16.319 --> 00:02:18.170 align:start position:0%
long time to really get into using jkl I
use<00:02:16.440><c> my</c><00:02:16.680><c> mouth</c><00:02:16.920><c> all</c><00:02:17.220><c> the</c><00:02:17.340><c> time</c><00:02:17.459><c> or</c><00:02:17.700><c> trackpad</c>

00:02:18.170 --> 00:02:18.180 align:start position:0%
use my mouth all the time or trackpad
 

00:02:18.180 --> 00:02:20.630 align:start position:0%
use my mouth all the time or trackpad
and

00:02:20.630 --> 00:02:20.640 align:start position:0%
and
 

00:02:20.640 --> 00:02:22.729 align:start position:0%
and
um<00:02:20.819><c> I</c><00:02:21.060><c> I</c><00:02:21.239><c> don't</c><00:02:21.540><c> know</c><00:02:21.660><c> why</c><00:02:21.900><c> well</c><00:02:22.379><c> I</c><00:02:22.560><c> don't</c><00:02:22.620><c> know</c>

00:02:22.729 --> 00:02:22.739 align:start position:0%
um I I don't know why well I don't know
 

00:02:22.739 --> 00:02:25.430 align:start position:0%
um I I don't know why well I don't know
why<00:02:22.980><c> I</c><00:02:23.280><c> didn't</c><00:02:23.459><c> really</c><00:02:23.879><c> get</c><00:02:24.180><c> into</c><00:02:24.360><c> it</c><00:02:24.599><c> well</c><00:02:25.200><c> I</c>

00:02:25.430 --> 00:02:25.440 align:start position:0%
why I didn't really get into it well I
 

00:02:25.440 --> 00:02:28.369 align:start position:0%
why I didn't really get into it well I
guess<00:02:25.560><c> for</c><00:02:25.739><c> one</c><00:02:25.980><c> thing</c><00:02:26.160><c> I</c><00:02:26.940><c> use</c><00:02:27.239><c> a</c><00:02:28.020><c> um</c><00:02:28.080><c> a</c>

00:02:28.369 --> 00:02:28.379 align:start position:0%
guess for one thing I use a um a
 

00:02:28.379 --> 00:02:30.229 align:start position:0%
guess for one thing I use a um a
different<00:02:28.440><c> keyboard</c><00:02:28.800><c> layout</c><00:02:29.160><c> so</c><00:02:29.280><c> jkl</c><00:02:29.879><c> wasn't</c>

00:02:30.229 --> 00:02:30.239 align:start position:0%
different keyboard layout so jkl wasn't
 

00:02:30.239 --> 00:02:33.470 align:start position:0%
different keyboard layout so jkl wasn't
exactly<00:02:30.599><c> in</c><00:02:30.780><c> the</c><00:02:30.900><c> same</c><00:02:31.140><c> spot</c><00:02:31.620><c> as</c><00:02:32.160><c> where</c><00:02:32.700><c> jko</c><00:02:33.239><c> is</c>

00:02:33.470 --> 00:02:33.480 align:start position:0%
exactly in the same spot as where jko is
 

00:02:33.480 --> 00:02:36.110 align:start position:0%
exactly in the same spot as where jko is
on<00:02:33.840><c> the</c><00:02:34.020><c> QWERTY</c><00:02:34.379><c> keyboard</c><00:02:34.620><c> on</c><00:02:34.800><c> this</c><00:02:34.980><c> keyboard</c>

00:02:36.110 --> 00:02:36.120 align:start position:0%
on the QWERTY keyboard on this keyboard
 

00:02:36.120 --> 00:02:39.170 align:start position:0%
on the QWERTY keyboard on this keyboard
so<00:02:36.840><c> that</c><00:02:37.440><c> made</c><00:02:37.560><c> things</c><00:02:37.739><c> difficult</c><00:02:38.099><c> but</c>

00:02:39.170 --> 00:02:39.180 align:start position:0%
so that made things difficult but
 

00:02:39.180 --> 00:02:41.390 align:start position:0%
so that made things difficult but
um<00:02:39.239><c> I've</c><00:02:39.480><c> finally</c><00:02:39.720><c> given</c><00:02:40.080><c> it</c><00:02:40.260><c> and</c><00:02:40.680><c> and</c><00:02:40.739><c> really</c>

00:02:41.390 --> 00:02:41.400 align:start position:0%
um I've finally given it and and really
 

00:02:41.400 --> 00:02:45.770 align:start position:0%
um I've finally given it and and really
Embrace<00:02:42.120><c> jkl</c><00:02:43.019><c> now</c><00:02:43.260><c> and</c><00:02:43.980><c> so</c><00:02:44.160><c> if</c><00:02:44.400><c> I</c><00:02:44.640><c> use</c><00:02:44.819><c> for</c><00:02:45.239><c> jkl</c>

00:02:45.770 --> 00:02:45.780 align:start position:0%
Embrace jkl now and so if I use for jkl
 

00:02:45.780 --> 00:02:47.809 align:start position:0%
Embrace jkl now and so if I use for jkl
it<00:02:46.080><c> just</c><00:02:46.260><c> is</c><00:02:46.560><c> these</c><00:02:46.920><c> three</c><00:02:47.040><c> keys</c><00:02:47.459><c> right</c><00:02:47.580><c> here</c>

00:02:47.809 --> 00:02:47.819 align:start position:0%
it just is these three keys right here
 

00:02:47.819 --> 00:02:50.690 align:start position:0%
it just is these three keys right here
and<00:02:48.420><c> if</c><00:02:48.660><c> I</c><00:02:48.840><c> press</c><00:02:48.959><c> the</c><00:02:49.140><c> K</c><00:02:49.379><c> key</c><00:02:49.620><c> that</c><00:02:50.160><c> just</c><00:02:50.280><c> stops</c>

00:02:50.690 --> 00:02:50.700 align:start position:0%
and if I press the K key that just stops
 

00:02:50.700 --> 00:02:52.670 align:start position:0%
and if I press the K key that just stops
the<00:02:51.000><c> timeline</c><00:02:51.300><c> so</c><00:02:51.599><c> it's</c><00:02:51.900><c> already</c><00:02:52.080><c> stopped</c><00:02:52.560><c> so</c>

00:02:52.670 --> 00:02:52.680 align:start position:0%
the timeline so it's already stopped so
 

00:02:52.680 --> 00:02:55.190 align:start position:0%
the timeline so it's already stopped so
it's<00:02:52.920><c> not</c><00:02:53.160><c> going</c><00:02:53.280><c> to</c><00:02:53.400><c> stop</c><00:02:53.580><c> further</c><00:02:54.239><c> and</c><00:02:55.019><c> then</c>

00:02:55.190 --> 00:02:55.200 align:start position:0%
it's not going to stop further and then
 

00:02:55.200 --> 00:02:56.750 align:start position:0%
it's not going to stop further and then
the<00:02:55.379><c> L</c><00:02:55.560><c> key</c>

00:02:56.750 --> 00:02:56.760 align:start position:0%
the L key
 

00:02:56.760 --> 00:02:59.570 align:start position:0%
the L key
moves<00:02:57.360><c> things</c><00:02:57.480><c> forward</c><00:02:57.800><c> it's</c><00:02:58.800><c> playing</c><00:02:59.160><c> what's</c>

00:02:59.570 --> 00:02:59.580 align:start position:0%
moves things forward it's playing what's
 

00:02:59.580 --> 00:03:00.949 align:start position:0%
moves things forward it's playing what's
on<00:02:59.879><c> the</c><00:03:00.000><c> timeline</c>

00:03:00.949 --> 00:03:00.959 align:start position:0%
on the timeline
 

00:03:00.959 --> 00:03:03.770 align:start position:0%
on the timeline
and<00:03:01.680><c> if</c><00:03:01.860><c> I</c><00:03:02.040><c> press</c><00:03:02.160><c> L</c><00:03:02.400><c> again</c><00:03:02.640><c> it</c><00:03:03.360><c> plays</c><00:03:03.660><c> it</c>

00:03:03.770 --> 00:03:03.780 align:start position:0%
and if I press L again it plays it
 

00:03:03.780 --> 00:03:06.350 align:start position:0%
and if I press L again it plays it
faster<00:03:03.959><c> and</c><00:03:04.739><c> faster</c><00:03:05.040><c> and</c><00:03:05.459><c> faster</c><00:03:05.819><c> and</c><00:03:06.060><c> faster</c>

00:03:06.350 --> 00:03:06.360 align:start position:0%
faster and faster and faster and faster
 

00:03:06.360 --> 00:03:11.210 align:start position:0%
faster and faster and faster and faster
and<00:03:06.660><c> faster</c><00:03:06.900><c> and</c><00:03:07.140><c> faster</c><00:03:07.400><c> and</c><00:03:08.400><c> K</c><00:03:08.580><c> stops</c><00:03:09.480><c> J</c><00:03:10.260><c> goes</c>

00:03:11.210 --> 00:03:11.220 align:start position:0%
and faster and faster and K stops J goes
 

00:03:11.220 --> 00:03:14.330 align:start position:0%
and faster and faster and K stops J goes
backwards<00:03:11.580><c> Big</c><00:03:11.879><c> K</c><00:03:12.540><c> goes</c><00:03:12.959><c> past</c><00:03:13.140><c> backwards</c><00:03:13.920><c> you</c>

00:03:14.330 --> 00:03:14.340 align:start position:0%
backwards Big K goes past backwards you
 

00:03:14.340 --> 00:03:16.729 align:start position:0%
backwards Big K goes past backwards you
know<00:03:14.459><c> kind</c><00:03:14.640><c> of</c><00:03:14.760><c> one</c><00:03:15.060><c> one</c><00:03:15.420><c> x</c><00:03:15.599><c> one</c><00:03:15.959><c> times</c><00:03:16.200><c> uh</c>

00:03:16.729 --> 00:03:16.739 align:start position:0%
know kind of one one x one times uh
 

00:03:16.739 --> 00:03:18.770 align:start position:0%
know kind of one one x one times uh
speed<00:03:17.040><c> and</c><00:03:17.340><c> then</c><00:03:17.580><c> faster</c><00:03:17.879><c> and</c><00:03:18.180><c> faster</c><00:03:18.540><c> and</c>

00:03:18.770 --> 00:03:18.780 align:start position:0%
speed and then faster and faster and
 

00:03:18.780 --> 00:03:21.110 align:start position:0%
speed and then faster and faster and
faster<00:03:19.140><c> and</c><00:03:19.319><c> faster</c><00:03:19.620><c> and</c><00:03:20.159><c> stop</c>

00:03:21.110 --> 00:03:21.120 align:start position:0%
faster and faster and stop
 

00:03:21.120 --> 00:03:23.270 align:start position:0%
faster and faster and stop
okay<00:03:21.540><c> so</c><00:03:21.780><c> that's</c><00:03:21.900><c> pretty</c><00:03:22.080><c> cool</c><00:03:22.260><c> if</c><00:03:22.680><c> you</c><00:03:22.800><c> hold</c><00:03:22.980><c> K</c>

00:03:23.270 --> 00:03:23.280 align:start position:0%
okay so that's pretty cool if you hold K
 

00:03:23.280 --> 00:03:26.270 align:start position:0%
okay so that's pretty cool if you hold K
and<00:03:23.940><c> go</c><00:03:24.120><c> press</c><00:03:24.420><c> J</c><00:03:24.780><c> you're</c><00:03:25.560><c> going</c><00:03:25.739><c> back</c><00:03:26.040><c> one</c>

00:03:26.270 --> 00:03:26.280 align:start position:0%
and go press J you're going back one
 

00:03:26.280 --> 00:03:27.290 align:start position:0%
and go press J you're going back one
frame

00:03:27.290 --> 00:03:27.300 align:start position:0%
frame
 

00:03:27.300 --> 00:03:29.690 align:start position:0%
frame
press<00:03:27.659><c> L</c><00:03:27.959><c> you're</c><00:03:28.379><c> going</c><00:03:28.620><c> forward</c><00:03:28.800><c> a</c><00:03:29.099><c> frame</c>

00:03:29.690 --> 00:03:29.700 align:start position:0%
press L you're going forward a frame
 

00:03:29.700 --> 00:03:35.050 align:start position:0%
press L you're going forward a frame
holding<00:03:30.480><c> down</c><00:03:30.659><c> J</c><00:03:31.140><c> you're</c><00:03:31.920><c> going</c><00:03:32.159><c> backwards</c>

00:03:35.050 --> 00:03:35.060 align:start position:0%
 
 

00:03:35.060 --> 00:03:40.070 align:start position:0%
 
forwards<00:03:36.140><c> slowly</c><00:03:37.140><c> okay</c><00:03:38.040><c> so</c><00:03:38.340><c> cool</c><00:03:38.580><c> jkl</c><00:03:39.540><c> first</c>

00:03:40.070 --> 00:03:40.080 align:start position:0%
forwards slowly okay so cool jkl first
 

00:03:40.080 --> 00:03:41.869 align:start position:0%
forwards slowly okay so cool jkl first
three<00:03:40.379><c> uh</c><00:03:40.739><c> keyboard</c><00:03:41.040><c> shortcuts</c><00:03:41.640><c> that</c><00:03:41.760><c> are</c>

00:03:41.869 --> 00:03:41.879 align:start position:0%
three uh keyboard shortcuts that are
 

00:03:41.879 --> 00:03:44.149 align:start position:0%
three uh keyboard shortcuts that are
super<00:03:42.000><c> useful</c><00:03:42.360><c> the</c><00:03:43.319><c> next</c><00:03:43.500><c> set</c><00:03:43.739><c> of</c><00:03:43.920><c> three</c>

00:03:44.149 --> 00:03:44.159 align:start position:0%
super useful the next set of three
 

00:03:44.159 --> 00:03:46.690 align:start position:0%
super useful the next set of three
shortcuts<00:03:44.879><c> that</c><00:03:45.120><c> is</c><00:03:45.299><c> uh</c><00:03:45.780><c> really</c><00:03:46.080><c> really</c><00:03:46.200><c> neat</c>

00:03:46.690 --> 00:03:46.700 align:start position:0%
shortcuts that is uh really really neat
 

00:03:46.700 --> 00:03:50.270 align:start position:0%
shortcuts that is uh really really neat
is<00:03:47.700><c> First</c><00:03:48.180><c> Command</c><00:03:48.780><c> B</c><00:03:48.959><c> so</c><00:03:49.680><c> if</c><00:03:49.860><c> I'm</c><00:03:49.980><c> somewhere</c>

00:03:50.270 --> 00:03:50.280 align:start position:0%
is First Command B so if I'm somewhere
 

00:03:50.280 --> 00:03:52.610 align:start position:0%
is First Command B so if I'm somewhere
on<00:03:50.459><c> the</c><00:03:50.580><c> timeline</c><00:03:50.940><c> here</c><00:03:51.239><c> I</c><00:03:51.720><c> can</c><00:03:51.840><c> press</c><00:03:52.080><c> command</c>

00:03:52.610 --> 00:03:52.620 align:start position:0%
on the timeline here I can press command
 

00:03:52.620 --> 00:03:56.750 align:start position:0%
on the timeline here I can press command
B<00:03:52.920><c> and</c><00:03:53.879><c> that</c><00:03:54.120><c> is</c><00:03:54.420><c> a</c><00:03:54.780><c> blade</c><00:03:55.200><c> uh</c><00:03:56.040><c> and</c><00:03:56.220><c> so</c><00:03:56.400><c> you</c><00:03:56.580><c> see</c>

00:03:56.750 --> 00:03:56.760 align:start position:0%
B and that is a blade uh and so you see
 

00:03:56.760 --> 00:03:59.930 align:start position:0%
B and that is a blade uh and so you see
that<00:03:57.060><c> my</c><00:03:58.260><c> clip</c><00:03:58.980><c> that</c><00:03:59.340><c> I've</c><00:03:59.459><c> currently</c><00:03:59.760><c> got</c>

00:03:59.930 --> 00:03:59.940 align:start position:0%
that my clip that I've currently got
 

00:03:59.940 --> 00:04:03.589 align:start position:0%
that my clip that I've currently got
selected<00:04:00.420><c> just</c><00:04:01.080><c> got</c><00:04:01.319><c> split</c><00:04:01.680><c> into</c><00:04:01.860><c> two</c><00:04:02.159><c> pieces</c>

00:04:03.589 --> 00:04:03.599 align:start position:0%
selected just got split into two pieces
 

00:04:03.599 --> 00:04:05.570 align:start position:0%
selected just got split into two pieces
so<00:04:04.260><c> that's</c><00:04:04.500><c> pretty</c><00:04:04.680><c> useful</c><00:04:04.980><c> now</c><00:04:05.220><c> I</c><00:04:05.400><c> can</c><00:04:05.459><c> use</c>

00:04:05.570 --> 00:04:05.580 align:start position:0%
so that's pretty useful now I can use
 

00:04:05.580 --> 00:04:08.270 align:start position:0%
so that's pretty useful now I can use
jkl<00:04:06.180><c> to</c><00:04:06.480><c> move</c><00:04:06.659><c> forward</c><00:04:06.900><c> a</c><00:04:07.200><c> bit</c><00:04:07.319><c> and</c><00:04:07.980><c> let's</c><00:04:08.099><c> say</c>

00:04:08.270 --> 00:04:08.280 align:start position:0%
jkl to move forward a bit and let's say
 

00:04:08.280 --> 00:04:11.570 align:start position:0%
jkl to move forward a bit and let's say
I<00:04:08.519><c> want</c><00:04:08.700><c> to</c><00:04:08.879><c> make</c><00:04:09.299><c> this</c><00:04:09.540><c> second</c><00:04:09.840><c> clip</c><00:04:10.260><c> uh</c><00:04:11.220><c> start</c>

00:04:11.570 --> 00:04:11.580 align:start position:0%
I want to make this second clip uh start
 

00:04:11.580 --> 00:04:14.330 align:start position:0%
I want to make this second clip uh start
right<00:04:12.000><c> where</c><00:04:12.180><c> the</c><00:04:12.360><c> playhead</c><00:04:12.659><c> is</c><00:04:13.040><c> and</c><00:04:14.040><c> the</c>

00:04:14.330 --> 00:04:14.340 align:start position:0%
right where the playhead is and the
 

00:04:14.340 --> 00:04:17.150 align:start position:0%
right where the playhead is and the
second<00:04:14.519><c> or</c><00:04:14.819><c> the</c><00:04:15.000><c> earlier</c><00:04:15.659><c> clip</c><00:04:16.440><c> is</c><00:04:16.739><c> going</c><00:04:16.919><c> to</c>

00:04:17.150 --> 00:04:17.160 align:start position:0%
second or the earlier clip is going to
 

00:04:17.160 --> 00:04:19.849 align:start position:0%
second or the earlier clip is going to
end<00:04:17.579><c> where</c><00:04:17.880><c> it</c><00:04:18.060><c> is</c><00:04:18.180><c> ending</c><00:04:18.720><c> right</c><00:04:18.959><c> now</c><00:04:19.139><c> and</c><00:04:19.739><c> so</c>

00:04:19.849 --> 00:04:19.859 align:start position:0%
end where it is ending right now and so
 

00:04:19.859 --> 00:04:23.450 align:start position:0%
end where it is ending right now and so
I<00:04:19.979><c> can</c><00:04:20.100><c> press</c><00:04:20.280><c> option</c><00:04:20.880><c> and</c><00:04:21.660><c> then</c><00:04:21.840><c> left</c><00:04:22.260><c> bracket</c>

00:04:23.450 --> 00:04:23.460 align:start position:0%
I can press option and then left bracket
 

00:04:23.460 --> 00:04:27.650 align:start position:0%
I can press option and then left bracket
and<00:04:24.000><c> it</c><00:04:24.180><c> sets</c><00:04:24.540><c> the</c><00:04:24.660><c> beginning</c><00:04:25.040><c> of</c><00:04:26.040><c> the</c><00:04:26.660><c> clip</c>

00:04:27.650 --> 00:04:27.660 align:start position:0%
and it sets the beginning of the clip
 

00:04:27.660 --> 00:04:30.590 align:start position:0%
and it sets the beginning of the clip
that<00:04:27.840><c> I'm</c><00:04:27.960><c> currently</c><00:04:28.320><c> on</c><00:04:28.560><c> on</c><00:04:28.940><c> uh</c><00:04:29.940><c> sets</c><00:04:30.419><c> that</c>

00:04:30.590 --> 00:04:30.600 align:start position:0%
that I'm currently on on uh sets that
 

00:04:30.600 --> 00:04:32.510 align:start position:0%
that I'm currently on on uh sets that
set<00:04:31.080><c> the</c><00:04:31.380><c> beginning</c><00:04:31.500><c> of</c><00:04:31.740><c> the</c><00:04:31.860><c> clip</c><00:04:32.100><c> to</c><00:04:32.280><c> right</c>

00:04:32.510 --> 00:04:32.520 align:start position:0%
set the beginning of the clip to right
 

00:04:32.520 --> 00:04:35.330 align:start position:0%
set the beginning of the clip to right
there<00:04:32.699><c> and</c><00:04:33.540><c> then</c><00:04:33.660><c> doing</c><00:04:34.020><c> the</c><00:04:34.440><c> uh</c><00:04:34.800><c> setting</c><00:04:35.220><c> the</c>

00:04:35.330 --> 00:04:35.340 align:start position:0%
there and then doing the uh setting the
 

00:04:35.340 --> 00:04:38.450 align:start position:0%
there and then doing the uh setting the
end<00:04:35.580><c> let's</c><00:04:36.060><c> say</c><00:04:36.240><c> I</c><00:04:36.479><c> go</c><00:04:36.600><c> back</c><00:04:36.900><c> to</c>

00:04:38.450 --> 00:04:38.460 align:start position:0%
end let's say I go back to
 

00:04:38.460 --> 00:04:41.450 align:start position:0%
end let's say I go back to
here<00:04:39.120><c> I</c><00:04:39.600><c> want</c><00:04:39.840><c> the</c><00:04:40.139><c> end</c><00:04:40.380><c> of</c><00:04:40.740><c> that</c><00:04:40.860><c> clip</c><00:04:41.160><c> to</c><00:04:41.340><c> be</c>

00:04:41.450 --> 00:04:41.460 align:start position:0%
here I want the end of that clip to be
 

00:04:41.460 --> 00:04:44.390 align:start position:0%
here I want the end of that clip to be
there<00:04:41.639><c> I</c><00:04:42.120><c> can</c><00:04:42.300><c> press</c><00:04:42.419><c> option</c><00:04:42.960><c> and</c><00:04:43.620><c> then</c><00:04:43.860><c> this</c>

00:04:44.390 --> 00:04:44.400 align:start position:0%
there I can press option and then this
 

00:04:44.400 --> 00:04:46.670 align:start position:0%
there I can press option and then this
left<00:04:44.759><c> square</c><00:04:45.240><c> bracket</c>

00:04:46.670 --> 00:04:46.680 align:start position:0%
left square bracket
 

00:04:46.680 --> 00:04:48.950 align:start position:0%
left square bracket
and<00:04:47.160><c> so</c><00:04:47.280><c> I've</c><00:04:47.520><c> just</c><00:04:47.759><c> set</c><00:04:48.000><c> that</c>

00:04:48.950 --> 00:04:48.960 align:start position:0%
and so I've just set that
 

00:04:48.960 --> 00:04:50.749 align:start position:0%
and so I've just set that
this<00:04:49.440><c> doesn't</c><00:04:49.620><c> make</c><00:04:49.860><c> any</c><00:04:50.040><c> sense</c><00:04:50.220><c> on</c><00:04:50.580><c> this</c>

00:04:50.749 --> 00:04:50.759 align:start position:0%
this doesn't make any sense on this
 

00:04:50.759 --> 00:04:52.430 align:start position:0%
this doesn't make any sense on this
timeline<00:04:51.060><c> so</c><00:04:51.300><c> I'm</c><00:04:51.419><c> going</c><00:04:51.540><c> to</c><00:04:51.600><c> press</c><00:04:51.900><c> command</c><00:04:52.259><c> z</c>

00:04:52.430 --> 00:04:52.440 align:start position:0%
timeline so I'm going to press command z
 

00:04:52.440 --> 00:04:55.610 align:start position:0%
timeline so I'm going to press command z
twice<00:04:53.220><c> uh</c><00:04:53.880><c> three</c><00:04:54.000><c> times</c><00:04:54.240><c> uh</c><00:04:54.960><c> to</c><00:04:55.139><c> get</c><00:04:55.320><c> back</c><00:04:55.440><c> to</c>

00:04:55.610 --> 00:04:55.620 align:start position:0%
twice uh three times uh to get back to
 

00:04:55.620 --> 00:04:56.749 align:start position:0%
twice uh three times uh to get back to
where<00:04:55.740><c> I</c><00:04:55.919><c> was</c>

00:04:56.749 --> 00:04:56.759 align:start position:0%
where I was
 

00:04:56.759 --> 00:05:00.170 align:start position:0%
where I was
okay<00:04:57.240><c> so</c><00:04:57.660><c> command</c><00:04:58.440><c> B</c><00:04:58.680><c> and</c><00:04:59.400><c> then</c><00:04:59.520><c> these</c><00:05:00.000><c> two</c>

00:05:00.170 --> 00:05:00.180 align:start position:0%
okay so command B and then these two
 

00:05:00.180 --> 00:05:02.810 align:start position:0%
okay so command B and then these two
keys<00:05:00.540><c> are</c><00:05:01.020><c> option</c><00:05:01.680><c> and</c><00:05:01.919><c> these</c><00:05:02.100><c> two</c><00:05:02.220><c> keys</c><00:05:02.639><c> is</c>

00:05:02.810 --> 00:05:02.820 align:start position:0%
keys are option and these two keys is
 

00:05:02.820 --> 00:05:05.330 align:start position:0%
keys are option and these two keys is
another<00:05:03.060><c> set</c><00:05:03.600><c> of</c><00:05:03.840><c> three</c><00:05:04.080><c> commands</c><00:05:04.620><c> that</c><00:05:04.979><c> I</c><00:05:05.160><c> use</c>

00:05:05.330 --> 00:05:05.340 align:start position:0%
another set of three commands that I use
 

00:05:05.340 --> 00:05:06.710 align:start position:0%
another set of three commands that I use
all<00:05:05.820><c> the</c><00:05:05.940><c> time</c>

00:05:06.710 --> 00:05:06.720 align:start position:0%
all the time
 

00:05:06.720 --> 00:05:09.409 align:start position:0%
all the time
now<00:05:07.259><c> there's</c><00:05:07.740><c> a</c><00:05:07.979><c> third</c><00:05:08.220><c> set</c><00:05:08.520><c> of</c><00:05:09.120><c> three</c>

00:05:09.409 --> 00:05:09.419 align:start position:0%
now there's a third set of three
 

00:05:09.419 --> 00:05:12.530 align:start position:0%
now there's a third set of three
commands<00:05:09.960><c> that</c><00:05:10.500><c> is</c><00:05:10.800><c> super</c><00:05:11.160><c> useful</c><00:05:11.639><c> and</c><00:05:12.360><c> so</c>

00:05:12.530 --> 00:05:12.540 align:start position:0%
commands that is super useful and so
 

00:05:12.540 --> 00:05:15.110 align:start position:0%
commands that is super useful and so
that's<00:05:12.780><c> going</c><00:05:13.020><c> to</c><00:05:13.199><c> be</c><00:05:13.380><c> the</c><00:05:14.160><c> c</c><00:05:14.340><c> key</c>

00:05:15.110 --> 00:05:15.120 align:start position:0%
that's going to be the c key
 

00:05:15.120 --> 00:05:18.290 align:start position:0%
that's going to be the c key
and<00:05:15.600><c> I</c><00:05:15.720><c> press</c><00:05:15.900><c> C</c><00:05:16.199><c> now</c><00:05:16.500><c> often</c><00:05:17.400><c> it's</c><00:05:17.639><c> like</c><00:05:17.940><c> not</c>

00:05:18.290 --> 00:05:18.300 align:start position:0%
and I press C now often it's like not
 

00:05:18.300 --> 00:05:21.230 align:start position:0%
and I press C now often it's like not
the<00:05:18.540><c> clip</c><00:05:18.780><c> that</c><00:05:18.960><c> I</c><00:05:19.139><c> wanted</c><00:05:19.280><c> and</c><00:05:20.280><c> so</c><00:05:20.520><c> if</c><00:05:21.000><c> I</c><00:05:21.120><c> want</c>

00:05:21.230 --> 00:05:21.240 align:start position:0%
the clip that I wanted and so if I want
 

00:05:21.240 --> 00:05:23.629 align:start position:0%
the clip that I wanted and so if I want
to<00:05:21.419><c> select</c><00:05:21.660><c> the</c><00:05:21.780><c> clip</c><00:05:22.139><c> below</c><00:05:22.620><c> that</c><00:05:22.919><c> I</c><00:05:23.460><c> can</c>

00:05:23.629 --> 00:05:23.639 align:start position:0%
to select the clip below that I can
 

00:05:23.639 --> 00:05:26.810 align:start position:0%
to select the clip below that I can
press<00:05:23.820><c> command</c><00:05:24.300><c> down</c><00:05:25.080><c> there</c><00:05:25.740><c> command</c><00:05:26.639><c> up</c>

00:05:26.810 --> 00:05:26.820 align:start position:0%
press command down there command up
 

00:05:26.820 --> 00:05:28.850 align:start position:0%
press command down there command up
arrow<00:05:27.180><c> goes</c><00:05:27.479><c> to</c><00:05:27.539><c> clip</c><00:05:27.840><c> above</c><00:05:28.139><c> command</c><00:05:28.680><c> down</c>

00:05:28.850 --> 00:05:28.860 align:start position:0%
arrow goes to clip above command down
 

00:05:28.860 --> 00:05:31.129 align:start position:0%
arrow goes to clip above command down
arrow<00:05:29.220><c> goes</c><00:05:29.460><c> to</c><00:05:29.460><c> the</c><00:05:29.580><c> clip</c><00:05:29.880><c> below</c><00:05:30.120><c> and</c><00:05:30.780><c> if</c><00:05:30.960><c> I</c>

00:05:31.129 --> 00:05:31.139 align:start position:0%
arrow goes to the clip below and if I
 

00:05:31.139 --> 00:05:33.290 align:start position:0%
arrow goes to the clip below and if I
have<00:05:31.380><c> a</c><00:05:31.740><c> lot</c><00:05:31.860><c> of</c><00:05:31.979><c> Clips</c><00:05:32.280><c> all</c><00:05:32.580><c> stacked</c><00:05:33.060><c> on</c><00:05:33.180><c> top</c>

00:05:33.290 --> 00:05:33.300 align:start position:0%
have a lot of Clips all stacked on top
 

00:05:33.300 --> 00:05:35.210 align:start position:0%
have a lot of Clips all stacked on top
of<00:05:33.479><c> each</c><00:05:33.600><c> other</c><00:05:33.780><c> that</c><00:05:34.380><c> can</c><00:05:34.560><c> be</c><00:05:34.680><c> really</c><00:05:34.860><c> useful</c>

00:05:35.210 --> 00:05:35.220 align:start position:0%
of each other that can be really useful
 

00:05:35.220 --> 00:05:37.730 align:start position:0%
of each other that can be really useful
so<00:05:35.759><c> C</c><00:05:36.060><c> to</c><00:05:36.360><c> select</c><00:05:36.539><c> where</c><00:05:36.840><c> my</c><00:05:37.080><c> plate</c><00:05:37.320><c> head</c><00:05:37.500><c> is</c>

00:05:37.730 --> 00:05:37.740 align:start position:0%
so C to select where my plate head is
 

00:05:37.740 --> 00:05:40.610 align:start position:0%
so C to select where my plate head is
and<00:05:38.220><c> then</c><00:05:38.400><c> command</c><00:05:38.880><c> up</c><00:05:39.120><c> and</c><00:05:39.300><c> down</c><00:05:39.479><c> to</c><00:05:40.320><c> move</c><00:05:40.500><c> up</c>

00:05:40.610 --> 00:05:40.620 align:start position:0%
and then command up and down to move up
 

00:05:40.620 --> 00:05:43.249 align:start position:0%
and then command up and down to move up
and<00:05:40.800><c> down</c><00:05:40.919><c> so</c><00:05:41.220><c> there's</c><00:05:41.880><c> three</c><00:05:42.300><c> sets</c><00:05:42.720><c> of</c><00:05:42.960><c> three</c>

00:05:43.249 --> 00:05:43.259 align:start position:0%
and down so there's three sets of three
 

00:05:43.259 --> 00:05:45.409 align:start position:0%
and down so there's three sets of three
keyboard<00:05:43.680><c> shortcuts</c><00:05:44.340><c> that</c><00:05:44.460><c> I</c><00:05:44.580><c> use</c><00:05:44.759><c> all</c><00:05:45.240><c> the</c>

00:05:45.409 --> 00:05:45.419 align:start position:0%
keyboard shortcuts that I use all the
 

00:05:45.419 --> 00:05:47.810 align:start position:0%
keyboard shortcuts that I use all the
time<00:05:45.539><c> the</c><00:05:46.259><c> reason</c><00:05:46.380><c> why</c><00:05:46.860><c> I</c><00:05:47.100><c> ended</c><00:05:47.520><c> up</c><00:05:47.580><c> with</c>

00:05:47.810 --> 00:05:47.820 align:start position:0%
time the reason why I ended up with
 

00:05:47.820 --> 00:05:51.409 align:start position:0%
time the reason why I ended up with
three<00:05:48.060><c> sets</c><00:05:48.419><c> of</c><00:05:48.479><c> three</c><00:05:48.600><c> is</c><00:05:49.380><c> because</c>

00:05:51.409 --> 00:05:51.419 align:start position:0%
three sets of three is because
 

00:05:51.419 --> 00:05:55.249 align:start position:0%
three sets of three is because
I<00:05:52.139><c> ended</c><00:05:52.800><c> up</c><00:05:52.860><c> getting</c><00:05:53.100><c> this</c><00:05:54.000><c> guy</c>

00:05:55.249 --> 00:05:55.259 align:start position:0%
I ended up getting this guy
 

00:05:55.259 --> 00:05:59.450 align:start position:0%
I ended up getting this guy
which<00:05:55.919><c> is</c><00:05:56.280><c> a</c><00:05:57.000><c> three</c><00:05:57.300><c> by</c><00:05:57.539><c> three</c><00:05:57.900><c> macro</c><00:05:58.620><c> pad</c><00:05:58.860><c> and</c>

00:05:59.450 --> 00:05:59.460 align:start position:0%
which is a three by three macro pad and
 

00:05:59.460 --> 00:06:01.430 align:start position:0%
which is a three by three macro pad and
I'll<00:05:59.580><c> get</c><00:05:59.759><c> it</c><00:05:59.880><c> more</c><00:06:00.060><c> into</c><00:06:00.240><c> that</c><00:06:00.600><c> in</c><00:06:01.139><c> a</c><00:06:01.320><c> future</c>

00:06:01.430 --> 00:06:01.440 align:start position:0%
I'll get it more into that in a future
 

00:06:01.440 --> 00:06:03.650 align:start position:0%
I'll get it more into that in a future
video<00:06:01.740><c> so</c><00:06:02.280><c> I</c><00:06:02.460><c> just</c><00:06:02.580><c> wanted</c><00:06:02.699><c> to</c><00:06:02.880><c> cover</c><00:06:03.180><c> these</c>

00:06:03.650 --> 00:06:03.660 align:start position:0%
video so I just wanted to cover these
 

00:06:03.660 --> 00:06:06.290 align:start position:0%
video so I just wanted to cover these
three<00:06:03.960><c> sets</c><00:06:04.380><c> of</c><00:06:04.560><c> three</c><00:06:04.979><c> key</c><00:06:05.699><c> keyboard</c>

00:06:06.290 --> 00:06:06.300 align:start position:0%
three sets of three key keyboard
 

00:06:06.300 --> 00:06:08.930 align:start position:0%
three sets of three key keyboard
shortcuts<00:06:06.960><c> that</c><00:06:07.380><c> allow</c><00:06:07.740><c> me</c><00:06:07.860><c> to</c><00:06:08.340><c> get</c><00:06:08.639><c> these</c>

00:06:08.930 --> 00:06:08.940 align:start position:0%
shortcuts that allow me to get these
 

00:06:08.940 --> 00:06:11.570 align:start position:0%
shortcuts that allow me to get these
videos<00:06:09.060><c> done</c><00:06:09.419><c> as</c><00:06:10.020><c> quickly</c><00:06:10.440><c> as</c><00:06:10.680><c> possible</c><00:06:10.979><c> as</c><00:06:11.400><c> I</c>

00:06:11.570 --> 00:06:11.580 align:start position:0%
videos done as quickly as possible as I
 

00:06:11.580 --> 00:06:12.290 align:start position:0%
videos done as quickly as possible as I
say

00:06:12.290 --> 00:06:12.300 align:start position:0%
say
 

00:06:12.300 --> 00:06:14.810 align:start position:0%
say
um<00:06:12.419><c> the</c><00:06:12.720><c> last</c><00:06:12.840><c> one</c><00:06:13.080><c> it</c><00:06:13.500><c> took</c><00:06:13.740><c> me</c><00:06:13.919><c> probably</c><00:06:14.580><c> the</c>

00:06:14.810 --> 00:06:14.820 align:start position:0%
um the last one it took me probably the
 

00:06:14.820 --> 00:06:17.090 align:start position:0%
um the last one it took me probably the
editing<00:06:15.120><c> of</c><00:06:15.300><c> the</c><00:06:15.419><c> videos</c><00:06:15.539><c> you</c><00:06:16.380><c> know</c><00:06:16.440><c> less</c><00:06:16.919><c> than</c>

00:06:17.090 --> 00:06:17.100 align:start position:0%
editing of the videos you know less than
 

00:06:17.100 --> 00:06:20.629 align:start position:0%
editing of the videos you know less than
an<00:06:17.280><c> hour</c><00:06:17.400><c> which</c><00:06:17.820><c> is</c><00:06:18.000><c> amazing</c><00:06:18.660><c> uh</c><00:06:19.620><c> so</c><00:06:19.919><c> actually</c>

00:06:20.629 --> 00:06:20.639 align:start position:0%
an hour which is amazing uh so actually
 

00:06:20.639 --> 00:06:21.950 align:start position:0%
an hour which is amazing uh so actually
probably<00:06:20.940><c> even</c><00:06:21.180><c> less</c><00:06:21.360><c> than</c><00:06:21.479><c> that</c><00:06:21.600><c> more</c><00:06:21.840><c> like</c>

00:06:21.950 --> 00:06:21.960 align:start position:0%
probably even less than that more like
 

00:06:21.960 --> 00:06:25.010 align:start position:0%
probably even less than that more like
20<00:06:22.199><c> minutes</c><00:06:22.440><c> to</c><00:06:22.740><c> edit</c><00:06:23.160><c> the</c><00:06:23.400><c> video</c>

00:06:25.010 --> 00:06:25.020 align:start position:0%
20 minutes to edit the video
 

00:06:25.020 --> 00:06:27.350 align:start position:0%
20 minutes to edit the video
super<00:06:25.680><c> cool</c><00:06:25.919><c> so</c><00:06:26.400><c> if</c><00:06:26.460><c> you</c><00:06:26.639><c> get</c><00:06:26.759><c> to</c><00:06:26.880><c> really</c><00:06:27.240><c> good</c>

00:06:27.350 --> 00:06:27.360 align:start position:0%
super cool so if you get to really good
 

00:06:27.360 --> 00:06:29.330 align:start position:0%
super cool so if you get to really good
with<00:06:27.600><c> these</c><00:06:27.780><c> keyboard</c><00:06:28.080><c> shortcuts</c><00:06:28.740><c> you</c><00:06:29.160><c> will</c>

00:06:29.330 --> 00:06:29.340 align:start position:0%
with these keyboard shortcuts you will
 

00:06:29.340 --> 00:06:32.029 align:start position:0%
with these keyboard shortcuts you will
do<00:06:29.580><c> some</c><00:06:29.940><c> amazing</c><00:06:30.360><c> things</c><00:06:30.600><c> as</c><00:06:30.960><c> well</c><00:06:31.139><c> all</c><00:06:31.860><c> right</c>

00:06:32.029 --> 00:06:32.039 align:start position:0%
do some amazing things as well all right
 

00:06:32.039 --> 00:06:34.749 align:start position:0%
do some amazing things as well all right
thanks<00:06:32.400><c> so</c><00:06:32.580><c> much</c><00:06:32.699><c> uh</c><00:06:33.539><c> I</c><00:06:33.780><c> look</c><00:06:33.900><c> forward</c><00:06:34.080><c> to</c>

00:06:34.749 --> 00:06:34.759 align:start position:0%
thanks so much uh I look forward to
 

00:06:34.759 --> 00:06:37.070 align:start position:0%
thanks so much uh I look forward to
seeing<00:06:35.759><c> you</c><00:06:35.880><c> in</c><00:06:35.940><c> the</c><00:06:36.120><c> next</c><00:06:36.180><c> video</c><00:06:36.479><c> alrighty</c>

00:06:37.070 --> 00:06:37.080 align:start position:0%
seeing you in the next video alrighty
 

00:06:37.080 --> 00:06:39.319 align:start position:0%
seeing you in the next video alrighty
bye

