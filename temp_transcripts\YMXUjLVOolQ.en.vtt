WEBVTT
Kind: captions
Language: en

00:00:00.319 --> 00:00:03.649 align:start position:0%
 
hey<00:00:01.319><c> guys</c><00:00:01.560><c> we're</c><00:00:01.979><c> up</c><00:00:02.100><c> to</c><00:00:02.220><c> part</c><00:00:02.460><c> five</c><00:00:02.730><c> and</c><00:00:03.179><c> we</c>

00:00:03.649 --> 00:00:03.659 align:start position:0%
hey guys we're up to part five and we
 

00:00:03.659 --> 00:00:05.599 align:start position:0%
hey guys we're up to part five and we
are<00:00:03.780><c> continuing</c><00:00:04.380><c> here</c><00:00:04.680><c> our</c><00:00:04.859><c> discussion</c><00:00:05.430><c> of</c>

00:00:05.599 --> 00:00:05.609 align:start position:0%
are continuing here our discussion of
 

00:00:05.609 --> 00:00:08.660 align:start position:0%
are continuing here our discussion of
meta<00:00:06.540><c> day</c><00:00:06.750><c> so</c><00:00:07.109><c> we</c><00:00:07.830><c> were</c><00:00:07.950><c> talking</c><00:00:08.160><c> about</c>

00:00:08.660 --> 00:00:08.670 align:start position:0%
meta day so we were talking about
 

00:00:08.670 --> 00:00:10.820 align:start position:0%
meta day so we were talking about
creating<00:00:09.269><c> a</c><00:00:09.570><c> client</c><00:00:10.019><c> table</c><00:00:10.349><c> now</c><00:00:10.530><c> I'm</c><00:00:10.620><c> going</c><00:00:10.769><c> to</c>

00:00:10.820 --> 00:00:10.830 align:start position:0%
creating a client table now I'm going to
 

00:00:10.830 --> 00:00:13.490 align:start position:0%
creating a client table now I'm going to
do<00:00:10.920><c> a</c><00:00:10.950><c> custom</c><00:00:11.309><c> query</c><00:00:11.969><c> I'm</c><00:00:12.480><c> gonna</c><00:00:13.200><c> say</c><00:00:13.410><c> I'm</c>

00:00:13.490 --> 00:00:13.500 align:start position:0%
do a custom query I'm gonna say I'm
 

00:00:13.500 --> 00:00:15.289 align:start position:0%
do a custom query I'm gonna say I'm
gonna<00:00:13.679><c> look</c><00:00:13.860><c> at</c><00:00:14.009><c> no</c><00:00:14.219><c> DP</c><00:00:14.610><c> I'm</c><00:00:14.759><c> gonna</c><00:00:14.940><c> look</c><00:00:15.030><c> at</c><00:00:15.179><c> my</c>

00:00:15.289 --> 00:00:15.299 align:start position:0%
gonna look at no DP I'm gonna look at my
 

00:00:15.299 --> 00:00:16.820 align:start position:0%
gonna look at no DP I'm gonna look at my
client<00:00:15.599><c> table</c><00:00:16.199><c> and</c><00:00:16.350><c> I</c><00:00:16.410><c> want</c><00:00:16.590><c> to</c><00:00:16.680><c> get</c>

00:00:16.820 --> 00:00:16.830 align:start position:0%
client table and I want to get
 

00:00:16.830 --> 00:00:19.820 align:start position:0%
client table and I want to get
specifically<00:00:17.490><c> the</c><00:00:17.640><c> count</c><00:00:18.260><c> now</c><00:00:19.260><c> let's</c><00:00:19.529><c> see</c><00:00:19.710><c> if</c>

00:00:19.820 --> 00:00:19.830 align:start position:0%
specifically the count now let's see if
 

00:00:19.830 --> 00:00:23.900 align:start position:0%
specifically the count now let's see if
you<00:00:20.010><c> buy</c><00:00:20.250><c> raw</c><00:00:20.760><c> data</c><00:00:21.060><c> no</c><00:00:21.510><c> count</c><00:00:21.869><c> of</c><00:00:22.050><c> rows</c><00:00:22.320><c> and</c><00:00:22.910><c> I</c>

00:00:23.900 --> 00:00:23.910 align:start position:0%
you buy raw data no count of rows and I
 

00:00:23.910 --> 00:00:27.939 align:start position:0%
you buy raw data no count of rows and I
want<00:00:24.269><c> to</c><00:00:25.130><c> get</c><00:00:26.130><c> the</c><00:00:26.279><c> answer</c>

00:00:27.939 --> 00:00:27.949 align:start position:0%
want to get the answer
 

00:00:27.949 --> 00:00:31.849 align:start position:0%
want to get the answer
eleven<00:00:28.949><c> okay</c><00:00:29.900><c> select</c><00:00:30.900><c> count</c><00:00:31.170><c> so</c><00:00:31.560><c> I'm</c><00:00:31.679><c> gonna</c>

00:00:31.849 --> 00:00:31.859 align:start position:0%
eleven okay select count so I'm gonna
 

00:00:31.859 --> 00:00:37.819 align:start position:0%
eleven okay select count so I'm gonna
save<00:00:32.160><c> this</c><00:00:32.430><c> as</c><00:00:33.140><c> count</c><00:00:34.140><c> of</c><00:00:34.469><c> clients</c><00:00:36.530><c> so</c><00:00:37.530><c> you</c>

00:00:37.819 --> 00:00:37.829 align:start position:0%
save this as count of clients so you
 

00:00:37.829 --> 00:00:46.100 align:start position:0%
save this as count of clients so you
count<00:00:39.110><c> of</c><00:00:40.110><c> clients</c><00:00:40.980><c> okay</c><00:00:44.390><c> hey</c><00:00:45.390><c> guys</c><00:00:45.629><c> I</c><00:00:45.660><c> just</c>

00:00:46.100 --> 00:00:46.110 align:start position:0%
count of clients okay hey guys I just
 

00:00:46.110 --> 00:00:47.750 align:start position:0%
count of clients okay hey guys I just
worked<00:00:46.350><c> on</c><00:00:46.440><c> this</c><00:00:46.590><c> sequel</c><00:00:47.010><c> query</c><00:00:47.250><c> in</c><00:00:47.430><c> order</c><00:00:47.640><c> to</c>

00:00:47.750 --> 00:00:47.760 align:start position:0%
worked on this sequel query in order to
 

00:00:47.760 --> 00:00:49.880 align:start position:0%
worked on this sequel query in order to
save<00:00:48.000><c> time</c><00:00:48.030><c> and</c><00:00:48.629><c> what</c><00:00:49.050><c> I</c><00:00:49.079><c> want</c><00:00:49.320><c> to</c><00:00:49.410><c> show</c><00:00:49.530><c> you</c><00:00:49.590><c> is</c>

00:00:49.880 --> 00:00:49.890 align:start position:0%
save time and what I want to show you is
 

00:00:49.890 --> 00:00:52.700 align:start position:0%
save time and what I want to show you is
how<00:00:50.180><c> meta</c><00:00:51.180><c> base</c><00:00:51.360><c> automatically</c><00:00:52.050><c> taps</c><00:00:52.440><c> into</c>

00:00:52.700 --> 00:00:52.710 align:start position:0%
how meta base automatically taps into
 

00:00:52.710 --> 00:00:55.459 align:start position:0%
how meta base automatically taps into
the<00:00:52.890><c> power</c><00:00:53.219><c> of</c><00:00:53.579><c> a</c><00:00:53.850><c> client</c><00:00:54.570><c> creation</c><00:00:55.170><c> date</c>

00:00:55.459 --> 00:00:55.469 align:start position:0%
the power of a client creation date
 

00:00:55.469 --> 00:00:57.439 align:start position:0%
the power of a client creation date
client<00:00:56.219><c> creation</c><00:00:56.610><c> did</c><00:00:56.789><c> if</c><00:00:56.910><c> you</c><00:00:56.940><c> remember</c><00:00:57.420><c> our</c>

00:00:57.439 --> 00:00:57.449 align:start position:0%
client creation did if you remember our
 

00:00:57.449 --> 00:01:00.110 align:start position:0%
client creation did if you remember our
schema<00:00:57.960><c> for</c><00:00:58.079><c> our</c><00:00:58.199><c> database</c><00:00:58.680><c> was</c><00:00:58.949><c> it</c><00:00:59.579><c> was</c><00:00:59.850><c> just</c>

00:01:00.110 --> 00:01:00.120 align:start position:0%
schema for our database was it was just
 

00:01:00.120 --> 00:01:02.029 align:start position:0%
schema for our database was it was just
a<00:01:00.210><c> column</c><00:01:00.570><c> within</c><00:01:00.750><c> the</c><00:01:00.960><c> client</c><00:01:01.230><c> table</c><00:01:01.800><c> and</c><00:01:01.980><c> I</c>

00:01:02.029 --> 00:01:02.039 align:start position:0%
a column within the client table and I
 

00:01:02.039 --> 00:01:05.179 align:start position:0%
a column within the client table and I
said<00:01:02.340><c> that</c><00:01:03.090><c> this</c><00:01:03.539><c> drop-down</c><00:01:04.290><c> filter</c><00:01:04.979><c> should</c>

00:01:05.179 --> 00:01:05.189 align:start position:0%
said that this drop-down filter should
 

00:01:05.189 --> 00:01:07.880 align:start position:0%
said that this drop-down filter should
it<00:01:05.309><c> be</c><00:01:05.580><c> automatically</c><00:01:06.420><c> tied</c><00:01:06.840><c> to</c><00:01:07.260><c> the</c><00:01:07.740><c> my</c>

00:01:07.880 --> 00:01:07.890 align:start position:0%
it be automatically tied to the my
 

00:01:07.890 --> 00:01:10.789 align:start position:0%
it be automatically tied to the my
sequel<00:01:08.369><c> creation</c><00:01:08.939><c> date</c><00:01:09.210><c> I</c><00:01:09.240><c> went</c><00:01:09.600><c> into</c><00:01:09.869><c> the</c>

00:01:10.789 --> 00:01:10.799 align:start position:0%
sequel creation date I went into the
 

00:01:10.799 --> 00:01:14.060 align:start position:0%
sequel creation date I went into the
variables<00:01:11.400><c> section</c><00:01:11.850><c> over</c><00:01:12.150><c> here</c><00:01:12.210><c> and</c><00:01:12.740><c> I</c><00:01:13.740><c> went</c>

00:01:14.060 --> 00:01:14.070 align:start position:0%
variables section over here and I went
 

00:01:14.070 --> 00:01:16.700 align:start position:0%
variables section over here and I went
into<00:01:14.280><c> field</c><00:01:15.060><c> filter</c><00:01:15.600><c> you</c><00:01:16.049><c> can</c><00:01:16.229><c> choose</c><00:01:16.409><c> what</c>

00:01:16.700 --> 00:01:16.710 align:start position:0%
into field filter you can choose what
 

00:01:16.710 --> 00:01:18.109 align:start position:0%
into field filter you can choose what
kind<00:01:16.740><c> of</c><00:01:16.920><c> integer</c><00:01:17.280><c> type</c><00:01:17.310><c> you</c><00:01:17.670><c> want</c><00:01:17.850><c> it</c><00:01:17.909><c> to</c><00:01:18.000><c> be</c>

00:01:18.109 --> 00:01:18.119 align:start position:0%
kind of integer type you want it to be
 

00:01:18.119 --> 00:01:20.660 align:start position:0%
kind of integer type you want it to be
client<00:01:18.509><c> creation</c><00:01:18.960><c> date</c><00:01:19.280><c> client</c><00:01:20.280><c> and</c><00:01:20.549><c> you</c>

00:01:20.660 --> 00:01:20.670 align:start position:0%
client creation date client and you
 

00:01:20.670 --> 00:01:22.640 align:start position:0%
client creation date client and you
actually<00:01:20.850><c> choose</c><00:01:21.330><c> the</c><00:01:21.570><c> column</c><00:01:22.020><c> right</c><00:01:22.259><c> so</c><00:01:22.439><c> if</c><00:01:22.560><c> I</c>

00:01:22.640 --> 00:01:22.650 align:start position:0%
actually choose the column right so if I
 

00:01:22.650 --> 00:01:25.399 align:start position:0%
actually choose the column right so if I
want<00:01:22.890><c> to</c><00:01:22.950><c> go</c><00:01:23.040><c> to</c><00:01:23.130><c> the</c><00:01:23.250><c> blog</c><00:01:23.490><c> post</c><00:01:24.140><c> let's</c><00:01:25.140><c> see</c><00:01:25.380><c> I</c>

00:01:25.399 --> 00:01:25.409 align:start position:0%
want to go to the blog post let's see I
 

00:01:25.409 --> 00:01:27.530 align:start position:0%
want to go to the blog post let's see I
can<00:01:25.590><c> actually</c><00:01:25.979><c> go</c><00:01:26.130><c> back</c><00:01:26.159><c> and</c><00:01:26.909><c> click</c><00:01:27.180><c> I</c><00:01:27.360><c> want</c>

00:01:27.530 --> 00:01:27.540 align:start position:0%
can actually go back and click I want
 

00:01:27.540 --> 00:01:29.960 align:start position:0%
can actually go back and click I want
something<00:01:27.780><c> from</c><00:01:28.020><c> blog</c><00:01:28.350><c> both</c><00:01:28.710><c> or</c><00:01:29.490><c> one</c><00:01:29.790><c> of</c><00:01:29.880><c> the</c>

00:01:29.960 --> 00:01:29.970 align:start position:0%
something from blog both or one of the
 

00:01:29.970 --> 00:01:31.490 align:start position:0%
something from blog both or one of the
other<00:01:30.090><c> table</c><00:01:30.570><c> is</c><00:01:30.689><c> one</c><00:01:30.869><c> of</c><00:01:30.960><c> the</c><00:01:31.079><c> other</c><00:01:31.170><c> fields</c>

00:01:31.490 --> 00:01:31.500 align:start position:0%
other table is one of the other fields
 

00:01:31.500 --> 00:01:32.960 align:start position:0%
other table is one of the other fields
but<00:01:31.650><c> I</c><00:01:31.829><c> want</c><00:01:32.070><c> to</c><00:01:32.159><c> specifically</c><00:01:32.549><c> to</c><00:01:32.700><c> be</c><00:01:32.820><c> from</c>

00:01:32.960 --> 00:01:32.970 align:start position:0%
but I want to specifically to be from
 

00:01:32.970 --> 00:01:35.660 align:start position:0%
but I want to specifically to be from
the<00:01:33.060><c> client</c><00:01:33.390><c> table</c><00:01:33.720><c> and</c><00:01:34.220><c> client</c><00:01:35.220><c> creation</c>

00:01:35.660 --> 00:01:35.670 align:start position:0%
the client table and client creation
 

00:01:35.670 --> 00:01:37.760 align:start position:0%
the client table and client creation
date<00:01:35.880><c> and</c><00:01:36.329><c> I</c><00:01:36.630><c> call</c><00:01:36.840><c> it</c><00:01:36.990><c> a</c><00:01:37.049><c> date</c><00:01:37.259><c> filter</c><00:01:37.650><c> and</c>

00:01:37.760 --> 00:01:37.770 align:start position:0%
date and I call it a date filter and
 

00:01:37.770 --> 00:01:39.830 align:start position:0%
date and I call it a date filter and
that's<00:01:37.890><c> all</c><00:01:38.040><c> there</c><00:01:38.220><c> is</c><00:01:38.250><c> to</c><00:01:38.579><c> it</c><00:01:38.729><c> I</c><00:01:38.909><c> press</c><00:01:39.540><c> save</c>

00:01:39.830 --> 00:01:39.840 align:start position:0%
that's all there is to it I press save
 

00:01:39.840 --> 00:01:42.740 align:start position:0%
that's all there is to it I press save
and<00:01:40.229><c> I'm</c><00:01:40.950><c> gonna</c><00:01:41.100><c> save</c><00:01:41.430><c> it</c><00:01:41.670><c> then</c><00:01:41.880><c> I</c><00:01:42.240><c> also</c><00:01:42.450><c> want</c>

00:01:42.740 --> 00:01:42.750 align:start position:0%
and I'm gonna save it then I also want
 

00:01:42.750 --> 00:01:44.539 align:start position:0%
and I'm gonna save it then I also want
to<00:01:42.810><c> add</c><00:01:42.990><c> it</c><00:01:43.140><c> to</c><00:01:43.170><c> a</c><00:01:43.290><c> dashboard</c><00:01:43.619><c> I</c><00:01:44.159><c> want</c><00:01:44.340><c> to</c><00:01:44.399><c> add</c>

00:01:44.539 --> 00:01:44.549 align:start position:0%
to add it to a dashboard I want to add
 

00:01:44.549 --> 00:01:46.190 align:start position:0%
to add it to a dashboard I want to add
it<00:01:44.670><c> to</c><00:01:44.700><c> our</c><00:01:44.820><c> client</c><00:01:45.360><c> dashboard</c><00:01:45.840><c> and</c><00:01:45.960><c> show</c><00:01:46.140><c> you</c>

00:01:46.190 --> 00:01:46.200 align:start position:0%
it to our client dashboard and show you
 

00:01:46.200 --> 00:01:48.350 align:start position:0%
it to our client dashboard and show you
what<00:01:46.409><c> we</c><00:01:46.530><c> can</c><00:01:46.680><c> do</c><00:01:46.860><c> here</c><00:01:47.130><c> okay</c>

00:01:48.350 --> 00:01:48.360 align:start position:0%
what we can do here okay
 

00:01:48.360 --> 00:01:49.880 align:start position:0%
what we can do here okay
I<00:01:48.570><c> think</c><00:01:48.810><c> we're</c><00:01:48.960><c> gonna</c><00:01:49.049><c> get</c><00:01:49.259><c> it</c><00:01:49.409><c> was</c><00:01:49.560><c> in</c><00:01:49.740><c> this</c>

00:01:49.880 --> 00:01:49.890 align:start position:0%
I think we're gonna get it was in this
 

00:01:49.890 --> 00:01:52.700 align:start position:0%
I think we're gonna get it was in this
quick<00:01:50.790><c> tutorial</c><00:01:50.939><c> I've</c><00:01:51.840><c> got</c><00:01:51.990><c> an</c><00:01:52.110><c> X</c><00:01:52.320><c> of</c><00:01:52.500><c> five</c>

00:01:52.700 --> 00:01:52.710 align:start position:0%
quick tutorial I've got an X of five
 

00:01:52.710 --> 00:01:56.149 align:start position:0%
quick tutorial I've got an X of five
minutes<00:01:53.070><c> within</c><00:01:53.340><c> each</c><00:01:53.490><c> one</c><00:01:53.790><c> so</c><00:01:54.799><c> I've</c><00:01:55.799><c> got</c><00:01:55.979><c> my</c>

00:01:56.149 --> 00:01:56.159 align:start position:0%
minutes within each one so I've got my
 

00:01:56.159 --> 00:01:59.510 align:start position:0%
minutes within each one so I've got my
count<00:01:56.430><c> and</c><00:01:56.700><c> I've</c><00:01:56.790><c> got</c><00:01:56.969><c> my</c><00:01:57.210><c> my</c><00:01:57.659><c> date</c><00:01:57.899><c> right</c><00:01:58.520><c> this</c>

00:01:59.510 --> 00:01:59.520 align:start position:0%
count and I've got my my date right this
 

00:01:59.520 --> 00:02:01.069 align:start position:0%
count and I've got my my date right this
is<00:01:59.640><c> really</c><00:01:59.939><c> hard</c><00:02:00.060><c> to</c><00:02:00.240><c> say</c><00:02:00.450><c> so</c><00:02:00.509><c> I</c><00:02:00.630><c> need</c><00:02:00.840><c> to</c><00:02:00.960><c> make</c>

00:02:01.069 --> 00:02:01.079 align:start position:0%
is really hard to say so I need to make
 

00:02:01.079 --> 00:02:03.830 align:start position:0%
is really hard to say so I need to make
this<00:02:01.259><c> smaller</c><00:02:01.820><c> about</c><00:02:02.820><c> my</c><00:02:02.969><c> count</c><00:02:03.270><c> and</c><00:02:03.479><c> I</c><00:02:03.540><c> got</c><00:02:03.689><c> my</c>

00:02:03.830 --> 00:02:03.840 align:start position:0%
this smaller about my count and I got my
 

00:02:03.840 --> 00:02:07.490 align:start position:0%
this smaller about my count and I got my
date<00:02:04.079><c> five</c><00:02:04.469><c> on</c><00:02:04.860><c> given</c><00:02:05.790><c> date</c><00:02:06.210><c> and</c><00:02:06.719><c> then</c><00:02:07.380><c> I'm</c>

00:02:07.490 --> 00:02:07.500 align:start position:0%
date five on given date and then I'm
 

00:02:07.500 --> 00:02:09.499 align:start position:0%
date five on given date and then I'm
gonna<00:02:07.619><c> go</c><00:02:07.799><c> into</c><00:02:08.009><c> add</c><00:02:08.160><c> filter</c><00:02:08.670><c> I</c><00:02:08.879><c> want</c><00:02:09.300><c> to</c><00:02:09.390><c> go</c>

00:02:09.499 --> 00:02:09.509 align:start position:0%
gonna go into add filter I want to go
 

00:02:09.509 --> 00:02:10.969 align:start position:0%
gonna go into add filter I want to go
into<00:02:09.750><c> other</c>

00:02:10.969 --> 00:02:10.979 align:start position:0%
into other
 

00:02:10.979 --> 00:02:16.309 align:start position:0%
into other
not<00:02:11.430><c> time</c><00:02:11.840><c> time</c><00:02:12.840><c> and</c><00:02:13.200><c> all</c><00:02:13.860><c> options</c><00:02:14.400><c> and</c><00:02:15.319><c> date</c>

00:02:16.309 --> 00:02:16.319 align:start position:0%
not time time and all options and date
 

00:02:16.319 --> 00:02:18.320 align:start position:0%
not time time and all options and date
shelter<00:02:16.860><c> that's</c><00:02:17.190><c> all</c><00:02:17.400><c> there</c><00:02:17.550><c> is</c><00:02:17.670><c> to</c><00:02:17.940><c> it</c>

00:02:18.320 --> 00:02:18.330 align:start position:0%
shelter that's all there is to it
 

00:02:18.330 --> 00:02:23.600 align:start position:0%
shelter that's all there is to it
they<00:02:19.080><c> filter</c><00:02:19.410><c> is</c><00:02:19.680><c> gonna</c><00:02:19.800><c> be</c><00:02:20.069><c> tied</c><00:02:21.620><c> say</c><00:02:22.620><c> they</c>

00:02:23.600 --> 00:02:23.610 align:start position:0%
they filter is gonna be tied say they
 

00:02:23.610 --> 00:02:27.050 align:start position:0%
they filter is gonna be tied say they
filter<00:02:24.090><c> is</c><00:02:24.239><c> tied</c><00:02:24.569><c> with</c><00:02:25.250><c> select</c><00:02:26.250><c> this</c><00:02:26.760><c> date</c>

00:02:27.050 --> 00:02:27.060 align:start position:0%
filter is tied with select this date
 

00:02:27.060 --> 00:02:28.699 align:start position:0%
filter is tied with select this date
filter<00:02:27.450><c> that</c><00:02:27.599><c> we</c><00:02:27.690><c> have</c><00:02:27.840><c> on</c><00:02:28.019><c> here</c><00:02:28.290><c> client</c>

00:02:28.699 --> 00:02:28.709 align:start position:0%
filter that we have on here client
 

00:02:28.709 --> 00:02:31.790 align:start position:0%
filter that we have on here client
creation<00:02:29.190><c> date</c><00:02:29.630><c> okay</c><00:02:30.630><c> and</c><00:02:31.260><c> we're</c><00:02:31.470><c> gonna</c><00:02:31.530><c> even</c>

00:02:31.790 --> 00:02:31.800 align:start position:0%
creation date okay and we're gonna even
 

00:02:31.800 --> 00:02:35.140 align:start position:0%
creation date okay and we're gonna even
call<00:02:32.130><c> that</c><00:02:32.160><c> not</c><00:02:32.700><c> date</c><00:02:32.940><c> filter</c><00:02:33.390><c> but</c><00:02:34.130><c> client</c>

00:02:35.140 --> 00:02:35.150 align:start position:0%
call that not date filter but client
 

00:02:35.150 --> 00:02:40.160 align:start position:0%
call that not date filter but client
creation<00:02:36.150><c> dates</c><00:02:38.030><c> okay</c><00:02:39.030><c> and</c><00:02:39.599><c> I'm</c><00:02:39.780><c> gonna</c><00:02:39.900><c> press</c>

00:02:40.160 --> 00:02:40.170 align:start position:0%
creation dates okay and I'm gonna press
 

00:02:40.170 --> 00:02:45.470 align:start position:0%
creation dates okay and I'm gonna press
done<00:02:42.530><c> awesome</c><00:02:43.530><c> and</c><00:02:44.430><c> once</c><00:02:44.880><c> that</c><00:02:45.090><c> is</c><00:02:45.150><c> all</c><00:02:45.420><c> done</c>

00:02:45.470 --> 00:02:45.480 align:start position:0%
done awesome and once that is all done
 

00:02:45.480 --> 00:02:49.100 align:start position:0%
done awesome and once that is all done
I'm<00:02:45.989><c> gonna</c><00:02:46.200><c> go</c><00:02:46.410><c> down</c><00:02:46.680><c> over</c><00:02:46.890><c> here</c><00:02:47.630><c> and</c><00:02:48.630><c> I'm</c>

00:02:49.100 --> 00:02:49.110 align:start position:0%
I'm gonna go down over here and I'm
 

00:02:49.110 --> 00:02:51.860 align:start position:0%
I'm gonna go down over here and I'm
gonna<00:02:49.260><c> press</c><00:02:49.560><c> save</c><00:02:50.390><c> cool</c><00:02:51.390><c> we</c><00:02:51.569><c> have</c><00:02:51.660><c> another</c>

00:02:51.860 --> 00:02:51.870 align:start position:0%
gonna press save cool we have another
 

00:02:51.870 --> 00:02:54.220 align:start position:0%
gonna press save cool we have another
client<00:02:52.140><c> creation</c><00:02:52.680><c> date</c><00:02:52.860><c> here</c><00:02:53.310><c> and</c><00:02:53.489><c> that</c><00:02:53.849><c> also</c>

00:02:54.220 --> 00:02:54.230 align:start position:0%
client creation date here and that also
 

00:02:54.230 --> 00:02:55.670 align:start position:0%
client creation date here and that also
makes<00:02:55.230><c> sense</c>

00:02:55.670 --> 00:02:55.680 align:start position:0%
makes sense
 

00:02:55.680 --> 00:02:57.860 align:start position:0%
makes sense
okay<00:02:56.340><c> so</c><00:02:56.400><c> client</c><00:02:56.880><c> creation</c><00:02:57.329><c> date</c><00:02:57.510><c> I</c><00:02:57.540><c> want</c><00:02:57.720><c> to</c>

00:02:57.860 --> 00:02:57.870 align:start position:0%
okay so client creation date I want to
 

00:02:57.870 --> 00:03:00.830 align:start position:0%
okay so client creation date I want to
only<00:02:58.200><c> show</c><00:02:58.470><c> that</c><00:02:59.160><c> the</c><00:02:59.430><c> count</c><00:02:59.790><c> of</c><00:03:00.150><c> this</c>

00:03:00.830 --> 00:03:00.840 align:start position:0%
only show that the count of this
 

00:03:00.840 --> 00:03:02.690 align:start position:0%
only show that the count of this
specific<00:03:01.110><c> date</c><00:03:01.680><c> okay</c>

00:03:02.690 --> 00:03:02.700 align:start position:0%
specific date okay
 

00:03:02.700 --> 00:03:06.110 align:start position:0%
specific date okay
date<00:03:03.599><c> client</c><00:03:04.290><c> creation</c><00:03:04.799><c> date</c><00:03:05.099><c> okay</c><00:03:05.910><c> it's</c><00:03:06.030><c> a</c>

00:03:06.110 --> 00:03:06.120 align:start position:0%
date client creation date okay it's a
 

00:03:06.120 --> 00:03:07.430 align:start position:0%
date client creation date okay it's a
little<00:03:06.239><c> misspelled</c><00:03:06.750><c> but</c><00:03:06.930><c> that's</c><00:03:07.110><c> can</c><00:03:07.319><c> be</c>

00:03:07.430 --> 00:03:07.440 align:start position:0%
little misspelled but that's can be
 

00:03:07.440 --> 00:03:10.280 align:start position:0%
little misspelled but that's can be
easily<00:03:07.620><c> first</c><00:03:08.040><c> so</c><00:03:08.730><c> client</c><00:03:09.180><c> creation</c><00:03:09.660><c> date</c><00:03:09.900><c> was</c>

00:03:10.280 --> 00:03:10.290 align:start position:0%
easily first so client creation date was
 

00:03:10.290 --> 00:03:14.900 align:start position:0%
easily first so client creation date was
on<00:03:10.620><c> December</c><00:03:11.340><c> 21st</c><00:03:11.910><c> on</c><00:03:13.160><c> December</c><00:03:14.160><c> 9</c><00:03:14.310><c> February</c>

00:03:14.900 --> 00:03:14.910 align:start position:0%
on December 21st on December 9 February
 

00:03:14.910 --> 00:03:18.470 align:start position:0%
on December 21st on December 9 February
or<00:03:15.420><c> in</c><00:03:15.569><c> December</c><00:03:16.079><c> 21st</c><00:03:16.739><c> and</c><00:03:17.010><c> update</c><00:03:18.000><c> filter</c><00:03:18.389><c> it</c>

00:03:18.470 --> 00:03:18.480 align:start position:0%
or in December 21st and update filter it
 

00:03:18.480 --> 00:03:20.599 align:start position:0%
or in December 21st and update filter it
should<00:03:18.630><c> only</c><00:03:18.720><c> be</c><00:03:18.870><c> one</c><00:03:19.170><c> row</c><00:03:19.440><c> now</c><00:03:19.859><c> let's</c><00:03:20.310><c> see</c><00:03:20.549><c> if</c>

00:03:20.599 --> 00:03:20.609 align:start position:0%
should only be one row now let's see if
 

00:03:20.609 --> 00:03:24.020 align:start position:0%
should only be one row now let's see if
this<00:03:20.819><c> works</c><00:03:21.030><c> yes</c><00:03:21.299><c> it</c><00:03:21.480><c> does</c><00:03:21.660><c> work</c><00:03:21.690><c> okay</c><00:03:22.440><c> and</c><00:03:23.030><c> we</c>

00:03:24.020 --> 00:03:24.030 align:start position:0%
this works yes it does work okay and we
 

00:03:24.030 --> 00:03:25.849 align:start position:0%
this works yes it does work okay and we
press<00:03:24.329><c> save</c><00:03:24.569><c> and</c><00:03:24.930><c> now</c><00:03:25.049><c> I</c><00:03:25.109><c> want</c><00:03:25.380><c> to</c><00:03:25.440><c> make</c><00:03:25.620><c> this</c><00:03:25.799><c> a</c>

00:03:25.849 --> 00:03:25.859 align:start position:0%
press save and now I want to make this a
 

00:03:25.859 --> 00:03:29.289 align:start position:0%
press save and now I want to make this a
little<00:03:26.160><c> nicer</c><00:03:26.579><c> so</c><00:03:26.910><c> I'm</c><00:03:27.780><c> gonna</c><00:03:27.959><c> click</c><00:03:28.200><c> on</c><00:03:28.380><c> that</c>

00:03:29.289 --> 00:03:29.299 align:start position:0%
little nicer so I'm gonna click on that
 

00:03:29.299 --> 00:03:32.509 align:start position:0%
little nicer so I'm gonna click on that
I'm<00:03:30.299><c> going</c><00:03:30.569><c> to</c><00:03:30.660><c> save</c><00:03:30.900><c> that</c><00:03:30.930><c> as</c><00:03:31.440><c> a</c><00:03:32.190><c> bar</c><00:03:32.489><c> chart</c>

00:03:32.509 --> 00:03:32.519 align:start position:0%
I'm going to save that as a bar chart
 

00:03:32.519 --> 00:03:34.520 align:start position:0%
I'm going to save that as a bar chart
because<00:03:33.239><c> bar</c><00:03:33.480><c> charts</c><00:03:33.840><c> are</c><00:03:33.959><c> awesome</c><00:03:34.319><c> and</c><00:03:34.470><c> I'm</c>

00:03:34.520 --> 00:03:34.530 align:start position:0%
because bar charts are awesome and I'm
 

00:03:34.530 --> 00:03:36.140 align:start position:0%
because bar charts are awesome and I'm
gonna<00:03:34.650><c> remove</c><00:03:34.950><c> the</c><00:03:35.160><c> client</c><00:03:35.489><c> creation</c><00:03:35.940><c> date</c>

00:03:36.140 --> 00:03:36.150 align:start position:0%
gonna remove the client creation date
 

00:03:36.150 --> 00:03:39.050 align:start position:0%
gonna remove the client creation date
and<00:03:36.720><c> get</c><00:03:37.380><c> the</c><00:03:37.530><c> answer</c><00:03:37.859><c> and</c><00:03:38.190><c> I'm</c><00:03:38.340><c> gonna</c><00:03:38.519><c> say</c><00:03:38.850><c> you</c>

00:03:39.050 --> 00:03:39.060 align:start position:0%
and get the answer and I'm gonna say you
 

00:03:39.060 --> 00:03:43.490 align:start position:0%
and get the answer and I'm gonna say you
know<00:03:39.209><c> what</c><00:03:39.389><c> no</c><00:03:40.010><c> bar</c><00:03:41.010><c> chart</c><00:03:41.959><c> this</c><00:03:42.959><c> is</c><00:03:43.170><c> awesome</c>

00:03:43.490 --> 00:03:43.500 align:start position:0%
know what no bar chart this is awesome
 

00:03:43.500 --> 00:03:46.539 align:start position:0%
know what no bar chart this is awesome
they<00:03:43.620><c> have</c><00:03:43.650><c> lines</c><00:03:44.359><c> have</c><00:03:45.359><c> a</c><00:03:45.389><c> look</c><00:03:45.600><c> at</c><00:03:45.690><c> this</c><00:03:45.870><c> boom</c>

00:03:46.539 --> 00:03:46.549 align:start position:0%
they have lines have a look at this boom
 

00:03:46.549 --> 00:03:51.740 align:start position:0%
they have lines have a look at this boom
one<00:03:47.549><c> blink</c><00:03:48.030><c> two</c><00:03:48.600><c> zero</c><00:03:49.470><c> one</c><00:03:49.500><c> two</c><00:03:50.569><c> you'll</c><00:03:51.569><c> have</c>

00:03:51.740 --> 00:03:51.750 align:start position:0%
one blink two zero one two you'll have
 

00:03:51.750 --> 00:03:55.670 align:start position:0%
one blink two zero one two you'll have
area<00:03:53.329><c> customize</c><00:03:54.329><c> the</c><00:03:54.569><c> colors</c><00:03:54.930><c> by</c><00:03:55.139><c> the</c><00:03:55.230><c> way</c><00:03:55.319><c> by</c>

00:03:55.670 --> 00:03:55.680 align:start position:0%
area customize the colors by the way by
 

00:03:55.680 --> 00:03:58.670 align:start position:0%
area customize the colors by the way by
charters<00:03:56.069><c> my</c><00:03:56.190><c> favorite</c><00:03:56.579><c> by</c><00:03:56.760><c> far</c><00:03:56.790><c> and</c><00:03:57.560><c> there</c><00:03:58.560><c> we</c>

00:03:58.670 --> 00:03:58.680 align:start position:0%
charters my favorite by far and there we
 

00:03:58.680 --> 00:04:01.069 align:start position:0%
charters my favorite by far and there we
go<00:03:58.829><c> a</c><00:03:59.040><c> bar</c><00:03:59.250><c> chart</c><00:03:59.519><c> so</c><00:04:00.150><c> we</c><00:04:00.180><c> want</c><00:04:00.569><c> to</c><00:04:00.660><c> fix</c><00:04:00.840><c> this</c>

00:04:01.069 --> 00:04:01.079 align:start position:0%
go a bar chart so we want to fix this
 

00:04:01.079 --> 00:04:05.960 align:start position:0%
go a bar chart so we want to fix this
though<00:04:01.430><c> date</c><00:04:02.870><c> and</c><00:04:03.870><c> the</c><00:04:04.739><c> number</c><00:04:05.069><c> so</c><00:04:05.519><c> client</c>

00:04:05.960 --> 00:04:05.970 align:start position:0%
though date and the number so client
 

00:04:05.970 --> 00:04:09.110 align:start position:0%
though date and the number so client
creation<00:04:06.389><c> date</c><00:04:06.540><c> x-axis</c><00:04:07.260><c> is</c><00:04:07.440><c> the</c><00:04:07.470><c> count</c><00:04:08.120><c> not</c>

00:04:09.110 --> 00:04:09.120 align:start position:0%
creation date x-axis is the count not
 

00:04:09.120 --> 00:04:11.449 align:start position:0%
creation date x-axis is the count not
x-axis<00:04:09.569><c> is</c><00:04:09.720><c> the</c><00:04:09.750><c> date</c><00:04:10.109><c> y-axis</c><00:04:10.859><c> is</c><00:04:11.040><c> the</c><00:04:11.190><c> count</c>

00:04:11.449 --> 00:04:11.459 align:start position:0%
x-axis is the date y-axis is the count
 

00:04:11.459 --> 00:04:14.000 align:start position:0%
x-axis is the date y-axis is the count
and<00:04:11.700><c> I'm</c><00:04:11.760><c> gonna</c><00:04:11.910><c> go</c><00:04:12.090><c> to</c><00:04:12.150><c> display</c><00:04:12.599><c> here</c><00:04:13.010><c> they</c>

00:04:14.000 --> 00:04:14.010 align:start position:0%
and I'm gonna go to display here they
 

00:04:14.010 --> 00:04:16.370 align:start position:0%
and I'm gonna go to display here they
say<00:04:14.280><c> she'll</c><00:04:14.730><c> bow</c><00:04:14.970><c> no</c><00:04:15.239><c> backsies</c>

00:04:16.370 --> 00:04:16.380 align:start position:0%
say she'll bow no backsies
 

00:04:16.380 --> 00:04:19.399 align:start position:0%
say she'll bow no backsies
where's<00:04:17.310><c> this</c><00:04:17.549><c> look</c><00:04:17.970><c> terrible</c><00:04:18.690><c> right</c><00:04:18.840><c> show</c>

00:04:19.399 --> 00:04:19.409 align:start position:0%
where's this look terrible right show
 

00:04:19.409 --> 00:04:24.220 align:start position:0%
where's this look terrible right show
x-axis<00:04:20.250><c> on</c><00:04:20.430><c> line</c><00:04:20.700><c> we</c><00:04:21.510><c> want</c><00:04:21.599><c> it</c><00:04:21.720><c> to</c><00:04:21.810><c> be</c><00:04:21.930><c> fatter</c>

00:04:24.220 --> 00:04:24.230 align:start position:0%
x-axis on line we want it to be fatter
 

00:04:24.230 --> 00:04:28.720 align:start position:0%
x-axis on line we want it to be fatter
sure<00:04:24.700><c> x-axis</c><00:04:25.700><c> is</c><00:04:25.940><c> or</c><00:04:26.630><c> you</c><00:04:26.840><c> know</c><00:04:27.370><c> there</c><00:04:28.370><c> we</c><00:04:28.550><c> go</c>

00:04:28.720 --> 00:04:28.730 align:start position:0%
sure x-axis is or you know there we go
 

00:04:28.730 --> 00:04:30.700 align:start position:0%
sure x-axis is or you know there we go
much<00:04:29.120><c> better</c><00:04:29.390><c> you</c><00:04:29.690><c> just</c><00:04:30.140><c> play</c><00:04:30.350><c> around</c><00:04:30.470><c> with</c><00:04:30.620><c> it</c>

00:04:30.700 --> 00:04:30.710 align:start position:0%
much better you just play around with it
 

00:04:30.710 --> 00:04:32.370 align:start position:0%
much better you just play around with it
a<00:04:30.920><c> little</c><00:04:31.160><c> bit</c><00:04:31.310><c> and</c><00:04:31.550><c> it'll</c><00:04:31.790><c> look</c><00:04:31.910><c> much</c><00:04:32.090><c> better</c>

00:04:32.370 --> 00:04:32.380 align:start position:0%
a little bit and it'll look much better
 

00:04:32.380 --> 00:04:36.310 align:start position:0%
a little bit and it'll look much better
okay<00:04:33.380><c> and</c><00:04:33.680><c> there</c><00:04:33.920><c> we</c><00:04:33.950><c> go</c><00:04:34.190><c> we</c><00:04:34.430><c> have</c><00:04:34.460><c> date</c><00:04:35.320><c> which</c>

00:04:36.310 --> 00:04:36.320 align:start position:0%
okay and there we go we have date which
 

00:04:36.320 --> 00:04:39.420 align:start position:0%
okay and there we go we have date which
is<00:04:36.850><c> specific</c><00:04:37.850><c> date</c><00:04:38.120><c> and</c><00:04:38.150><c> the</c><00:04:38.510><c> count</c>

00:04:39.420 --> 00:04:39.430 align:start position:0%
is specific date and the count
 

00:04:39.430 --> 00:04:42.100 align:start position:0%
is specific date and the count
Thursday<00:04:40.430><c> sorry</c><00:04:40.760><c> 21st</c><00:04:41.210><c> this</c><00:04:41.390><c> had</c><00:04:41.600><c> no</c><00:04:41.810><c> date</c>

00:04:42.100 --> 00:04:42.110 align:start position:0%
Thursday sorry 21st this had no date
 

00:04:42.110 --> 00:04:44.320 align:start position:0%
Thursday sorry 21st this had no date
this<00:04:42.860><c> had</c><00:04:43.130><c> December</c><00:04:43.580><c> 21st</c>

00:04:44.320 --> 00:04:44.330 align:start position:0%
this had December 21st
 

00:04:44.330 --> 00:04:47.370 align:start position:0%
this had December 21st
they<00:04:45.140><c> said</c><00:04:45.380><c> December</c><00:04:45.710><c> 22nd</c><00:04:46.340><c> count</c><00:04:46.610><c> is</c><00:04:46.820><c> two</c><00:04:47.060><c> and</c>

00:04:47.370 --> 00:04:47.380 align:start position:0%
they said December 22nd count is two and
 

00:04:47.380 --> 00:04:52.000 align:start position:0%
they said December 22nd count is two and
on<00:04:48.380><c> Thursday</c><00:04:49.810><c> January</c><00:04:50.810><c> 18</c><00:04:51.410><c> the</c><00:04:51.590><c> count</c><00:04:51.800><c> was</c>

00:04:52.000 --> 00:04:52.010 align:start position:0%
on Thursday January 18 the count was
 

00:04:52.010 --> 00:04:54.220 align:start position:0%
on Thursday January 18 the count was
three<00:04:52.310><c> and</c><00:04:52.490><c> there</c><00:04:52.640><c> we</c><00:04:52.760><c> go</c><00:04:52.910><c> and</c><00:04:53.540><c> you</c><00:04:53.900><c> can</c><00:04:53.990><c> add</c>

00:04:54.220 --> 00:04:54.230 align:start position:0%
three and there we go and you can add
 

00:04:54.230 --> 00:04:57.130 align:start position:0%
three and there we go and you can add
multiple<00:04:55.210><c> multiple</c><00:04:56.210><c> filters</c><00:04:56.720><c> but</c><00:04:56.960><c> that's</c>

00:04:57.130 --> 00:04:57.140 align:start position:0%
multiple multiple filters but that's
 

00:04:57.140 --> 00:04:59.050 align:start position:0%
multiple multiple filters but that's
really<00:04:57.350><c> awesome</c><00:04:57.560><c> and</c><00:04:58.070><c> you're</c><00:04:58.670><c> starting</c><00:04:58.880><c> to</c>

00:04:59.050 --> 00:04:59.060 align:start position:0%
really awesome and you're starting to
 

00:04:59.060 --> 00:05:01.960 align:start position:0%
really awesome and you're starting to
understand<00:04:59.510><c> a</c><00:04:59.600><c> little</c><00:04:59.840><c> bit</c>

