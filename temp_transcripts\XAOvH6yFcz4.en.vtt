WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.730 align:start position:0%
 
and<00:00:00.120><c> welcome</c><00:00:00.299><c> to</c><00:00:00.480><c> another</c><00:00:00.599><c> episode</c><00:00:00.989><c> of</c><00:00:01.319><c> the</c>

00:00:01.730 --> 00:00:01.740 align:start position:0%
and welcome to another episode of the
 

00:00:01.740 --> 00:00:03.260 align:start position:0%
and welcome to another episode of the
live<00:00:01.979><c> startup</c><00:00:02.639><c> series</c>

00:00:03.260 --> 00:00:03.270 align:start position:0%
live startup series
 

00:00:03.270 --> 00:00:04.760 align:start position:0%
live startup series
today<00:00:03.449><c> we're</c><00:00:03.689><c> going</c><00:00:03.810><c> to</c><00:00:03.899><c> be</c><00:00:04.020><c> continuing</c><00:00:04.200><c> our</c>

00:00:04.760 --> 00:00:04.770 align:start position:0%
today we're going to be continuing our
 

00:00:04.770 --> 00:00:07.369 align:start position:0%
today we're going to be continuing our
modern-day<00:00:05.220><c> CMS</c><00:00:05.730><c> project</c><00:00:06.240><c> and</c><00:00:06.450><c> we</c><00:00:07.020><c> are</c><00:00:07.140><c> going</c>

00:00:07.369 --> 00:00:07.379 align:start position:0%
modern-day CMS project and we are going
 

00:00:07.379 --> 00:00:09.740 align:start position:0%
modern-day CMS project and we are going
to<00:00:07.560><c> launch</c><00:00:07.770><c> it</c><00:00:07.980><c> to</c><00:00:08.010><c> Heroku</c><00:00:08.460><c> so</c><00:00:08.910><c> that's</c><00:00:09.570><c> gonna</c>

00:00:09.740 --> 00:00:09.750 align:start position:0%
to launch it to Heroku so that's gonna
 

00:00:09.750 --> 00:00:12.770 align:start position:0%
to launch it to Heroku so that's gonna
be<00:00:09.929><c> a</c><00:00:09.960><c> pretty</c><00:00:10.170><c> interesting</c><00:00:10.769><c> step</c><00:00:11.509><c> first</c><00:00:12.509><c> thing</c>

00:00:12.770 --> 00:00:12.780 align:start position:0%
be a pretty interesting step first thing
 

00:00:12.780 --> 00:00:13.759 align:start position:0%
be a pretty interesting step first thing
we're<00:00:12.929><c> going</c><00:00:13.019><c> to</c><00:00:13.080><c> do</c><00:00:13.230><c> is</c><00:00:13.320><c> we're</c><00:00:13.440><c> going</c><00:00:13.469><c> to</c><00:00:13.620><c> go</c>

00:00:13.759 --> 00:00:13.769 align:start position:0%
we're going to do is we're going to go
 

00:00:13.769 --> 00:00:19.550 align:start position:0%
we're going to do is we're going to go
into<00:00:14.040><c> Heroku</c><00:00:14.610><c> as</c><00:00:14.820><c> you</c><00:00:15.000><c> can</c><00:00:15.179><c> see</c><00:00:17.779><c> let's</c><00:00:18.779><c> see</c><00:00:19.080><c> my</c>

00:00:19.550 --> 00:00:19.560 align:start position:0%
into Heroku as you can see let's see my
 

00:00:19.560 --> 00:00:21.470 align:start position:0%
into Heroku as you can see let's see my
sequel<00:00:20.010><c> note</c><00:00:20.189><c> angular</c><00:00:20.789><c> is</c><00:00:20.910><c> what</c><00:00:21.150><c> I</c><00:00:21.180><c> need</c>

00:00:21.470 --> 00:00:21.480 align:start position:0%
sequel note angular is what I need
 

00:00:21.480 --> 00:00:23.269 align:start position:0%
sequel note angular is what I need
that's<00:00:21.900><c> the</c><00:00:22.109><c> project</c><00:00:22.439><c> that</c><00:00:22.830><c> we're</c><00:00:22.980><c> gonna</c><00:00:23.130><c> be</c>

00:00:23.269 --> 00:00:23.279 align:start position:0%
that's the project that we're gonna be
 

00:00:23.279 --> 00:00:25.820 align:start position:0%
that's the project that we're gonna be
launching<00:00:23.490><c> taroko</c><00:00:24.269><c> I</c><00:00:24.449><c> have</c><00:00:25.140><c> Sam</c><00:00:25.380><c> running</c><00:00:25.740><c> on</c>

00:00:25.820 --> 00:00:25.830 align:start position:0%
launching taroko I have Sam running on
 

00:00:25.830 --> 00:00:27.620 align:start position:0%
launching taroko I have Sam running on
my<00:00:25.949><c> local</c><00:00:26.130><c> computer</c><00:00:26.310><c> we're</c><00:00:26.849><c> also</c><00:00:27.000><c> going</c><00:00:27.300><c> to</c><00:00:27.420><c> be</c>

00:00:27.620 --> 00:00:27.630 align:start position:0%
my local computer we're also going to be
 

00:00:27.630 --> 00:00:31.189 align:start position:0%
my local computer we're also going to be
pulling<00:00:28.560><c> in</c><00:00:29.070><c> not</c><00:00:30.000><c> see</c><00:00:30.359><c> of</c><00:00:30.539><c> course</c><00:00:30.779><c> we're</c><00:00:31.080><c> gonna</c>

00:00:31.189 --> 00:00:31.199 align:start position:0%
pulling in not see of course we're gonna
 

00:00:31.199 --> 00:00:35.780 align:start position:0%
pulling in not see of course we're gonna
click<00:00:31.470><c> on</c><00:00:31.619><c> them</c><00:00:31.800><c> new</c><00:00:32.700><c> here</c><00:00:33.149><c> okay</c><00:00:34.190><c> and</c><00:00:35.190><c> create</c>

00:00:35.780 --> 00:00:35.790 align:start position:0%
click on them new here okay and create
 

00:00:35.790 --> 00:00:48.889 align:start position:0%
click on them new here okay and create
new<00:00:36.000><c> app</c><00:00:36.210><c> very</c><00:00:37.020><c> simple</c><00:00:46.910><c> thanks</c><00:00:47.940><c> we</c><00:00:48.600><c> need</c><00:00:48.719><c> that</c>

00:00:48.889 --> 00:00:48.899 align:start position:0%
new app very simple thanks we need that
 

00:00:48.899 --> 00:00:54.580 align:start position:0%
new app very simple thanks we need that
and<00:00:49.739><c> find</c><00:00:50.219><c> we</c><00:00:50.550><c> have</c><00:00:50.579><c> an</c><00:00:50.820><c> app</c><00:00:50.969><c> named</c><00:00:51.739><c> koala</c><00:00:52.739><c> mail</c>

00:00:54.580 --> 00:00:54.590 align:start position:0%
and find we have an app named koala mail
 

00:00:54.590 --> 00:00:57.950 align:start position:0%
and find we have an app named koala mail
okay<00:00:55.590><c> let's</c><00:00:56.309><c> start</c><00:00:57.210><c> with</c><00:00:57.300><c> a</c><00:00:57.390><c> little</c><00:00:57.629><c> case</c>

00:00:57.950 --> 00:00:57.960 align:start position:0%
okay let's start with a little case
 

00:00:57.960 --> 00:01:09.910 align:start position:0%
okay let's start with a little case
letter<00:01:06.979><c> koala</c><00:01:07.979><c> me</c><00:01:08.250><c> oh</c><00:01:08.280><c> that's</c><00:01:08.640><c> pretty</c><00:01:08.909><c> cool</c>

00:01:09.910 --> 00:01:09.920 align:start position:0%
letter koala me oh that's pretty cool
 

00:01:09.920 --> 00:01:13.330 align:start position:0%
letter koala me oh that's pretty cool
the<00:01:10.920><c> mail</c><00:01:11.100><c> United</c><00:01:11.549><c> States</c><00:01:11.850><c> now</c><00:01:12.119><c> I'm</c><00:01:12.390><c> in</c><00:01:12.600><c> Europe</c>

00:01:13.330 --> 00:01:13.340 align:start position:0%
the mail United States now I'm in Europe
 

00:01:13.340 --> 00:01:16.700 align:start position:0%
the mail United States now I'm in Europe
and<00:01:14.340><c> create</c><00:01:14.970><c> the</c><00:01:15.180><c> app</c><00:01:15.270><c> very</c><00:01:15.960><c> simple</c><00:01:16.470><c> thing</c><00:01:16.680><c> to</c>

00:01:16.700 --> 00:01:16.710 align:start position:0%
and create the app very simple thing to
 

00:01:16.710 --> 00:01:25.730 align:start position:0%
and create the app very simple thing to
do<00:01:22.340><c> now</c><00:01:23.340><c> kiss</c><00:01:23.549><c> is</c><00:01:23.700><c> the</c><00:01:23.880><c> option</c><00:01:24.240><c> to</c><00:01:25.229><c> deploy</c><00:01:25.560><c> if</c>

00:01:25.730 --> 00:01:25.740 align:start position:0%
do now kiss is the option to deploy if
 

00:01:25.740 --> 00:01:28.609 align:start position:0%
do now kiss is the option to deploy if
you<00:01:26.009><c> if</c><00:01:26.850><c> you</c><00:01:27.060><c> want</c><00:01:27.810><c> to</c><00:01:27.840><c> deploy</c><00:01:28.080><c> you</c><00:01:28.290><c> first</c><00:01:28.530><c> need</c>

00:01:28.609 --> 00:01:28.619 align:start position:0%
you if you want to deploy you first need
 

00:01:28.619 --> 00:01:30.499 align:start position:0%
you if you want to deploy you first need
to<00:01:28.680><c> download</c><00:01:28.920><c> the</c><00:01:29.159><c> Heroku</c><00:01:29.579><c> CLI</c><00:01:30.060><c> I</c><00:01:30.090><c> already</c>

00:01:30.499 --> 00:01:30.509 align:start position:0%
to download the Heroku CLI I already
 

00:01:30.509 --> 00:01:34.700 align:start position:0%
to download the Heroku CLI I already
have<00:01:30.689><c> it</c><00:01:30.840><c> in</c><00:01:30.960><c> installed</c><00:01:31.950><c> I</c><00:01:32.250><c> also</c><00:01:33.170><c> I'm</c><00:01:34.170><c> all</c><00:01:34.380><c> set</c>

00:01:34.700 --> 00:01:34.710 align:start position:0%
have it in installed I also I'm all set
 

00:01:34.710 --> 00:01:36.410 align:start position:0%
have it in installed I also I'm all set
up<00:01:34.740><c> here</c><00:01:34.890><c> all</c><00:01:35.189><c> I</c><00:01:35.310><c> need</c><00:01:35.460><c> to</c><00:01:35.549><c> do</c><00:01:35.729><c> is</c><00:01:35.880><c> go</c><00:01:36.090><c> to</c><00:01:36.150><c> my</c>

00:01:36.410 --> 00:01:36.420 align:start position:0%
up here all I need to do is go to my
 

00:01:36.420 --> 00:01:39.710 align:start position:0%
up here all I need to do is go to my
command<00:01:36.810><c> prompt</c><00:01:37.140><c> and</c><00:01:37.290><c> type</c><00:01:37.790><c> Roku</c><00:01:38.790><c> login</c><00:01:39.299><c> it</c>

00:01:39.710 --> 00:01:39.720 align:start position:0%
command prompt and type Roku login it
 

00:01:39.720 --> 00:01:41.179 align:start position:0%
command prompt and type Roku login it
give<00:01:39.900><c> you</c><00:01:40.049><c> all</c><00:01:40.200><c> the</c><00:01:40.350><c> commands</c><00:01:40.799><c> here</c><00:01:40.890><c> really</c>

00:01:41.179 --> 00:01:41.189 align:start position:0%
give you all the commands here really
 

00:01:41.189 --> 00:01:44.929 align:start position:0%
give you all the commands here really
beautifully<00:01:42.320><c> ok</c><00:01:43.320><c> as</c><00:01:43.799><c> you</c><00:01:43.860><c> can</c><00:01:43.979><c> see</c><00:01:44.280><c> I</c><00:01:44.310><c> changed</c>

00:01:44.929 --> 00:01:44.939 align:start position:0%
beautifully ok as you can see I changed
 

00:01:44.939 --> 00:01:46.730 align:start position:0%
beautifully ok as you can see I changed
the<00:01:45.090><c> port</c><00:01:45.420><c> of</c><00:01:45.630><c> the</c><00:01:45.810><c> project</c><00:01:46.229><c> to</c><00:01:46.320><c> port</c><00:01:46.560><c> 80</c>

00:01:46.730 --> 00:01:46.740 align:start position:0%
the port of the project to port 80
 

00:01:46.740 --> 00:01:52.160 align:start position:0%
the port of the project to port 80
because<00:01:47.040><c> that</c><00:01:47.340><c> allows</c><00:01:47.640><c> you</c><00:01:47.880><c> to</c><00:01:48.659><c> run</c><00:01:48.810><c> Heroku</c><00:01:51.170><c> to</c>

00:01:52.160 --> 00:01:52.170 align:start position:0%
because that allows you to run Heroku to
 

00:01:52.170 --> 00:01:53.980 align:start position:0%
because that allows you to run Heroku to
run<00:01:52.320><c> your</c><00:01:52.350><c> app</c><00:01:52.560><c> on</c><00:01:52.799><c> a</c><00:01:52.829><c> Roku</c><00:01:53.340><c> Roku</c>

00:01:53.980 --> 00:01:53.990 align:start position:0%
run your app on a Roku Roku
 

00:01:53.990 --> 00:01:57.550 align:start position:0%
run your app on a Roku Roku
automatically<00:01:54.990><c> listens</c><00:01:55.380><c> to</c><00:01:55.560><c> port</c><00:01:55.829><c> 80</c><00:01:56.100><c> and</c>

00:01:57.550 --> 00:01:57.560 align:start position:0%
automatically listens to port 80 and
 

00:01:57.560 --> 00:02:05.120 align:start position:0%
automatically listens to port 80 and
well<00:01:58.560><c> are</c><00:01:58.799><c> you</c><00:02:03.350><c> to</c><00:02:04.350><c> run</c><00:02:04.530><c> your</c><00:02:04.649><c> app</c><00:02:04.829><c> in</c><00:02:05.009><c> the</c>

00:02:05.120 --> 00:02:05.130 align:start position:0%
well are you to run your app in the
 

00:02:05.130 --> 00:02:07.670 align:start position:0%
well are you to run your app in the
cloud<00:02:05.420><c> I</c><00:02:06.420><c> just</c><00:02:06.780><c> take</c><00:02:07.020><c> you</c><00:02:07.140><c> forever</c><00:02:07.380><c> and</c><00:02:07.590><c> I</c>

00:02:07.670 --> 00:02:07.680 align:start position:0%
cloud I just take you forever and I
 

00:02:07.680 --> 00:02:09.949 align:start position:0%
cloud I just take you forever and I
gotta<00:02:07.829><c> close</c><00:02:08.039><c> some</c><00:02:08.310><c> windows</c><00:02:08.340><c> here</c><00:02:08.910><c> this</c><00:02:09.149><c> is</c><00:02:09.929><c> a</c>

00:02:09.949 --> 00:02:09.959 align:start position:0%
gotta close some windows here this is a
 

00:02:09.959 --> 00:02:15.680 align:start position:0%
gotta close some windows here this is a
really<00:02:10.289><c> terrible</c><00:02:10.649><c> shooter</c><00:02:11.250><c> but</c><00:02:11.550><c> whatever</c>

00:02:15.680 --> 00:02:15.690 align:start position:0%
 
 

00:02:15.690 --> 00:02:18.720 align:start position:0%
 
I'll<00:02:16.690><c> plug</c><00:02:16.930><c> in</c><00:02:17.050><c> an</c><00:02:17.170><c> external</c><00:02:17.410><c> hydride</c><00:02:18.160><c> maybe</c>

00:02:18.720 --> 00:02:18.730 align:start position:0%
I'll plug in an external hydride maybe
 

00:02:18.730 --> 00:02:25.290 align:start position:0%
I'll plug in an external hydride maybe
I'll<00:02:18.850><c> do</c><00:02:19.170><c> another</c><00:02:20.170><c> video</c><00:02:20.410><c> on</c><00:02:20.680><c> that</c><00:02:24.090><c> not</c><00:02:25.090><c> work</c>

00:02:25.290 --> 00:02:25.300 align:start position:0%
I'll do another video on that not work
 

00:02:25.300 --> 00:02:30.360 align:start position:0%
I'll do another video on that not work
you're<00:02:25.480><c> not</c><00:02:25.540><c> there</c><00:02:25.810><c> it</c><00:02:25.960><c> is</c><00:02:26.220><c> no</c><00:02:27.220><c> don't</c><00:02:29.220><c> tell</c><00:02:30.220><c> it</c>

00:02:30.360 --> 00:02:30.370 align:start position:0%
you're not there it is no don't tell it
 

00:02:30.370 --> 00:02:40.200 align:start position:0%
you're not there it is no don't tell it
finally<00:02:30.820><c> went</c><00:02:31.000><c> you</c><00:02:31.270><c> know</c><00:02:31.330><c> instead</c><00:02:31.750><c> of</c><00:02:33.990><c> sure</c><00:02:39.210><c> no</c>

00:02:40.200 --> 00:02:40.210 align:start position:0%
finally went you know instead of sure no
 

00:02:40.210 --> 00:02:45.540 align:start position:0%
finally went you know instead of sure no
I<00:02:40.480><c> co-wrote</c><00:02:40.900><c> the</c><00:02:41.290><c> Korean</c><00:02:41.680><c> shows</c><00:02:42.100><c> yeah</c><00:02:44.400><c> open</c><00:02:45.400><c> us</c>

00:02:45.540 --> 00:02:45.550 align:start position:0%
I co-wrote the Korean shows yeah open us
 

00:02:45.550 --> 00:02:46.370 align:start position:0%
I co-wrote the Korean shows yeah open us
up<00:02:45.730><c> big</c>

00:02:46.370 --> 00:02:46.380 align:start position:0%
up big
 

00:02:46.380 --> 00:02:53.550 align:start position:0%
up big
that's<00:02:47.380><c> my</c><00:02:47.530><c> Heroku</c><00:02:47.980><c> potential</c><00:02:51.090><c> six</c><00:02:52.560><c> gmail.com</c>

00:02:53.550 --> 00:02:53.560 align:start position:0%
that's my Heroku potential six gmail.com
 

00:02:53.560 --> 00:02:56.130 align:start position:0%
that's my Heroku potential six gmail.com
you<00:02:54.130><c> just</c><00:02:54.430><c> start</c><00:02:54.700><c> typing</c><00:02:54.940><c> I</c><00:02:55.360><c> guess</c><00:02:55.510><c> that</c><00:02:55.720><c> fixed</c>

00:02:56.130 --> 00:02:56.140 align:start position:0%
you just start typing I guess that fixed
 

00:02:56.140 --> 00:02:56.460 align:start position:0%
you just start typing I guess that fixed
it

00:02:56.460 --> 00:02:56.470 align:start position:0%
it
 

00:02:56.470 --> 00:02:59.980 align:start position:0%
it
that's<00:02:57.250><c> weird</c>

00:02:59.980 --> 00:02:59.990 align:start position:0%
 
 

00:02:59.990 --> 00:03:03.500 align:start position:0%
 
[Music]

00:03:03.500 --> 00:03:03.510 align:start position:0%
 
 

00:03:03.510 --> 00:03:06.770 align:start position:0%
 
okay<00:03:04.510><c> hand</c><00:03:04.840><c> up</c><00:03:04.990><c> loud</c><00:03:05.230><c> then</c><00:03:05.500><c> finally</c><00:03:06.040><c> let's</c><00:03:06.400><c> see</c>

00:03:06.770 --> 00:03:06.780 align:start position:0%
okay hand up loud then finally let's see
 

00:03:06.780 --> 00:03:09.390 align:start position:0%
okay hand up loud then finally let's see
ICP<00:03:07.780><c> into</c><00:03:08.050><c> my</c><00:03:08.170><c> project</c><00:03:08.710><c> I</c><00:03:08.860><c> think</c><00:03:08.920><c> I'm</c><00:03:09.160><c> already</c>

00:03:09.390 --> 00:03:09.400 align:start position:0%
ICP into my project I think I'm already
 

00:03:09.400 --> 00:03:12.240 align:start position:0%
ICP into my project I think I'm already
inside<00:03:09.910><c> it</c><00:03:10.180><c> as</c><00:03:10.390><c> I</c><00:03:10.600><c> am</c><00:03:10.720><c> I</c><00:03:10.990><c> do</c><00:03:11.560><c> it</c><00:03:11.770><c> get</c><00:03:11.980><c> in</c><00:03:12.100><c> it</c>

00:03:12.240 --> 00:03:12.250 align:start position:0%
inside it as I am I do it get in it
 

00:03:12.250 --> 00:03:14.550 align:start position:0%
inside it as I am I do it get in it
there<00:03:12.430><c> already</c><00:03:12.610><c> is</c><00:03:12.790><c> it</c><00:03:13.270><c> good</c><00:03:13.900><c> but</c><00:03:14.200><c> probably</c>

00:03:14.550 --> 00:03:14.560 align:start position:0%
there already is it good but probably
 

00:03:14.560 --> 00:03:18.650 align:start position:0%
there already is it good but probably
don't<00:03:14.980><c> have</c><00:03:15.070><c> a</c><00:03:15.220><c> real</c><00:03:15.700><c> good</c><00:03:16.560><c> see</c><00:03:17.560><c> if</c><00:03:17.650><c> it</c><00:03:17.770><c> does</c>

00:03:18.650 --> 00:03:18.660 align:start position:0%
don't have a real good see if it does
 

00:03:18.660 --> 00:03:23.670 align:start position:0%
don't have a real good see if it does
move<00:03:19.660><c> idiot</c><00:03:21.270><c> -</c><00:03:22.270><c> eight</c><00:03:22.570><c> for</c><00:03:22.870><c> Halloween</c><00:03:23.290><c> and</c>

00:03:23.670 --> 00:03:23.680 align:start position:0%
move idiot - eight for Halloween and
 

00:03:23.680 --> 00:03:25.830 align:start position:0%
move idiot - eight for Halloween and
this<00:03:23.830><c> sets</c><00:03:24.160><c> your</c><00:03:24.370><c> local</c><00:03:24.760><c> repo</c><00:03:24.940><c> to</c><00:03:25.330><c> point</c><00:03:25.630><c> to</c>

00:03:25.830 --> 00:03:25.840 align:start position:0%
this sets your local repo to point to
 

00:03:25.840 --> 00:03:32.550 align:start position:0%
this sets your local repo to point to
this<00:03:26.040><c> website</c><00:03:27.040><c> Rico</c><00:03:30.510><c> awesome</c><00:03:31.510><c> so</c><00:03:31.990><c> it</c><00:03:32.200><c> did</c><00:03:32.410><c> it</c>

00:03:32.550 --> 00:03:32.560 align:start position:0%
this website Rico awesome so it did it
 

00:03:32.560 --> 00:03:35.880 align:start position:0%
this website Rico awesome so it did it
successfully<00:03:33.070><c> instead</c><00:03:33.880><c> a</c><00:03:34.440><c> local</c><00:03:35.440><c> repo</c><00:03:35.590><c> to</c>

00:03:35.880 --> 00:03:35.890 align:start position:0%
successfully instead a local repo to
 

00:03:35.890 --> 00:03:37.500 align:start position:0%
successfully instead a local repo to
point<00:03:36.160><c> towards</c><00:03:36.550><c> there</c><00:03:36.730><c> I</c><00:03:36.760><c> want</c><00:03:37.030><c> to</c><00:03:37.090><c> get</c><00:03:37.270><c> at</c>

00:03:37.500 --> 00:03:37.510 align:start position:0%
point towards there I want to get at
 

00:03:37.510 --> 00:03:38.070 align:start position:0%
point towards there I want to get at
everything

00:03:38.070 --> 00:03:38.080 align:start position:0%
everything
 

00:03:38.080 --> 00:03:44.220 align:start position:0%
everything
get<00:03:38.710><c> back</c><00:03:42.240><c> and</c><00:03:43.240><c> again</c><00:03:43.570><c> you</c><00:03:43.720><c> can</c><00:03:43.900><c> pretty</c><00:03:44.140><c> much</c>

00:03:44.220 --> 00:03:44.230 align:start position:0%
get back and again you can pretty much
 

00:03:44.230 --> 00:03:46.110 align:start position:0%
get back and again you can pretty much
do<00:03:44.740><c> this</c><00:03:44.890><c> on</c><00:03:45.100><c> your</c><00:03:45.280><c> own</c><00:03:45.310><c> all</c><00:03:45.610><c> you</c><00:03:45.790><c> to</c><00:03:45.970><c> do</c><00:03:46.090><c> is</c>

00:03:46.110 --> 00:03:46.120 align:start position:0%
do this on your own all you to do is
 

00:03:46.120 --> 00:03:50.430 align:start position:0%
do this on your own all you to do is
just<00:03:46.300><c> go</c><00:03:47.260><c> to</c><00:03:47.290><c> the</c><00:03:48.690><c> repo</c><00:03:49.690><c> that's</c><00:03:50.080><c> gonna</c><00:03:50.230><c> be</c>

00:03:50.430 --> 00:03:50.440 align:start position:0%
just go to the repo that's gonna be
 

00:03:50.440 --> 00:03:52.620 align:start position:0%
just go to the repo that's gonna be
included<00:03:51.310><c> in</c><00:03:51.430><c> the</c><00:03:51.550><c> description</c><00:03:51.820><c> below</c><00:03:52.240><c> and</c>

00:03:52.620 --> 00:03:52.630 align:start position:0%
included in the description below and
 

00:03:52.630 --> 00:03:54.120 align:start position:0%
included in the description below and
all<00:03:52.960><c> you</c><00:03:53.110><c> really</c><00:03:53.140><c> need</c><00:03:53.470><c> to</c><00:03:53.500><c> always</c><00:03:53.830><c> going</c><00:03:54.010><c> to</c>

00:03:54.120 --> 00:03:54.130 align:start position:0%
all you really need to always going to
 

00:03:54.130 --> 00:03:56.100 align:start position:0%
all you really need to always going to
the<00:03:54.250><c> server</c><00:03:54.670><c> there's</c><00:03:55.480><c> a</c><00:03:55.540><c> lot</c><00:03:55.660><c> of</c><00:03:55.780><c> really</c><00:03:55.870><c> nice</c>

00:03:56.100 --> 00:03:56.110 align:start position:0%
the server there's a lot of really nice
 

00:03:56.110 --> 00:03:57.720 align:start position:0%
the server there's a lot of really nice
stuff<00:03:56.440><c> with</c><00:03:56.680><c> this</c><00:03:56.800><c> project</c><00:03:57.040><c> including</c>

00:03:57.720 --> 00:03:57.730 align:start position:0%
stuff with this project including
 

00:03:57.730 --> 00:04:02.130 align:start position:0%
stuff with this project including
integration<00:03:58.630><c> as</c><00:03:58.930><c> well</c><00:03:59.110><c> a</c><00:04:00.210><c> separate</c><00:04:01.210><c> series</c><00:04:01.780><c> on</c>

00:04:02.130 --> 00:04:02.140 align:start position:0%
integration as well a separate series on
 

00:04:02.140 --> 00:04:03.720 align:start position:0%
integration as well a separate series on
the<00:04:02.230><c> intercom</c><00:04:02.590><c> integration</c><00:04:03.220><c> that's</c><00:04:03.430><c> really</c>

00:04:03.720 --> 00:04:03.730 align:start position:0%
the intercom integration that's really
 

00:04:03.730 --> 00:04:04.770 align:start position:0%
the intercom integration that's really
cool

00:04:04.770 --> 00:04:04.780 align:start position:0%
cool
 

00:04:04.780 --> 00:04:08.190 align:start position:0%
cool
you're<00:04:05.590><c> gonna</c><00:04:05.710><c> go</c><00:04:05.890><c> to</c><00:04:05.950><c> your</c><00:04:06.160><c> port</c><00:04:06.730><c> here</c><00:04:07.200><c> you're</c>

00:04:08.190 --> 00:04:08.200 align:start position:0%
you're gonna go to your port here you're
 

00:04:08.200 --> 00:04:10.200 align:start position:0%
you're gonna go to your port here you're
going<00:04:08.290><c> to</c><00:04:08.350><c> change</c><00:04:08.650><c> it</c><00:04:08.800><c> to</c><00:04:08.830><c> port</c><00:04:09.160><c> 80</c><00:04:09.340><c> that's</c><00:04:09.970><c> the</c>

00:04:10.200 --> 00:04:10.210 align:start position:0%
going to change it to port 80 that's the
 

00:04:10.210 --> 00:04:12.980 align:start position:0%
going to change it to port 80 that's the
most<00:04:10.360><c> important</c><00:04:10.870><c> change</c><00:04:11.110><c> you</c><00:04:11.260><c> need</c><00:04:11.380><c> to</c><00:04:11.560><c> do</c><00:04:11.740><c> and</c>

00:04:12.980 --> 00:04:12.990 align:start position:0%
most important change you need to do and
 

00:04:12.990 --> 00:04:15.150 align:start position:0%
most important change you need to do and
let's<00:04:13.990><c> see</c><00:04:14.170><c> what</c><00:04:14.320><c> happens</c><00:04:14.650><c> once</c><00:04:14.860><c> we</c><00:04:14.950><c> do</c><00:04:15.130><c> that</c>

00:04:15.150 --> 00:04:15.160 align:start position:0%
let's see what happens once we do that
 

00:04:15.160 --> 00:04:17.910 align:start position:0%
let's see what happens once we do that
it<00:04:15.610><c> get</c><00:04:15.820><c> added</c><00:04:16.299><c> everything</c><00:04:16.750><c> let's</c><00:04:17.500><c> go</c><00:04:17.650><c> back</c>

00:04:17.910 --> 00:04:17.920 align:start position:0%
it get added everything let's go back
 

00:04:17.920 --> 00:04:27.700 align:start position:0%
it get added everything let's go back
into<00:04:18.130><c> the</c><00:04:18.340><c> tutorial</c><00:04:18.880><c> get</c><00:04:19.450><c> commit</c><00:04:19.840><c> -</c><00:04:20.049><c> I</c><00:04:20.229><c> am</c>

00:04:27.700 --> 00:04:27.710 align:start position:0%
 
 

00:04:27.710 --> 00:04:30.530 align:start position:0%
 
and<00:04:28.710><c> it</c><00:04:29.370><c> worked</c><00:04:29.640><c> now</c><00:04:29.940><c> we're</c><00:04:30.090><c> doing</c><00:04:30.270><c> the</c><00:04:30.360><c> get</c>

00:04:30.530 --> 00:04:30.540 align:start position:0%
and it worked now we're doing the get
 

00:04:30.540 --> 00:04:32.900 align:start position:0%
and it worked now we're doing the get
push<00:04:30.840><c> it's</c><00:04:31.590><c> gonna</c><00:04:31.680><c> automatically</c><00:04:32.340><c> launch</c><00:04:32.760><c> a</c>

00:04:32.900 --> 00:04:32.910 align:start position:0%
push it's gonna automatically launch a
 

00:04:32.910 --> 00:04:35.870 align:start position:0%
push it's gonna automatically launch a
note<00:04:33.150><c> server</c><00:04:33.510><c> on</c><00:04:34.020><c> Heroku</c><00:04:34.530><c> real</c><00:04:35.340><c> code</c><00:04:35.520><c> I</c><00:04:35.640><c> know</c>

00:04:35.870 --> 00:04:35.880 align:start position:0%
note server on Heroku real code I know
 

00:04:35.880 --> 00:04:37.640 align:start position:0%
note server on Heroku real code I know
we<00:04:36.060><c> can</c><00:04:36.210><c> pay</c><00:04:36.420><c> more</c><00:04:36.720><c> money</c><00:04:36.990><c> if</c><00:04:37.140><c> we</c><00:04:37.260><c> want</c><00:04:37.440><c> to</c><00:04:37.470><c> make</c>

00:04:37.640 --> 00:04:37.650 align:start position:0%
we can pay more money if we want to make
 

00:04:37.650 --> 00:04:39.290 align:start position:0%
we can pay more money if we want to make
the<00:04:37.800><c> Dino</c><00:04:38.010><c> bigger</c><00:04:38.400><c> but</c><00:04:38.670><c> I</c><00:04:38.700><c> don't</c><00:04:38.910><c> think</c><00:04:39.060><c> that's</c>

00:04:39.290 --> 00:04:39.300 align:start position:0%
the Dino bigger but I don't think that's
 

00:04:39.300 --> 00:04:41.240 align:start position:0%
the Dino bigger but I don't think that's
gonna<00:04:39.450><c> be</c><00:04:39.540><c> necessary</c><00:04:39.780><c> for</c><00:04:40.200><c> now</c><00:04:40.530><c> we're</c><00:04:41.130><c> also</c>

00:04:41.240 --> 00:04:41.250 align:start position:0%
gonna be necessary for now we're also
 

00:04:41.250 --> 00:04:43.100 align:start position:0%
gonna be necessary for now we're also
gonna<00:04:41.400><c> be</c><00:04:41.550><c> attaching</c><00:04:41.850><c> my</c><00:04:42.180><c> sequel</c><00:04:42.600><c> database</c>

00:04:43.100 --> 00:04:43.110 align:start position:0%
gonna be attaching my sequel database
 

00:04:43.110 --> 00:04:45.350 align:start position:0%
gonna be attaching my sequel database
where<00:04:43.710><c> there's</c><00:04:43.920><c> a</c><00:04:43.980><c> real</c><00:04:44.490><c> who</c><00:04:44.670><c> add-on</c><00:04:45.090><c> so</c>

00:04:45.350 --> 00:04:45.360 align:start position:0%
where there's a real who add-on so
 

00:04:45.360 --> 00:04:47.210 align:start position:0%
where there's a real who add-on so
that's<00:04:45.990><c> going</c><00:04:46.140><c> to</c><00:04:46.260><c> be</c><00:04:46.320><c> coming</c><00:04:46.650><c> up</c><00:04:46.740><c> in</c><00:04:46.860><c> the</c><00:04:46.980><c> next</c>

00:04:47.210 --> 00:04:47.220 align:start position:0%
that's going to be coming up in the next
 

00:04:47.220 --> 00:04:49.070 align:start position:0%
that's going to be coming up in the next
video<00:04:47.520><c> and</c><00:04:47.790><c> to</c><00:04:47.940><c> verify</c><00:04:48.390><c> that</c><00:04:48.420><c> everything</c><00:04:48.690><c> is</c>

00:04:49.070 --> 00:04:49.080 align:start position:0%
video and to verify that everything is
 

00:04:49.080 --> 00:04:52.040 align:start position:0%
video and to verify that everything is
up<00:04:49.230><c> and</c><00:04:49.380><c> running</c><00:04:49.560><c> and</c><00:04:50.660><c> we'll</c><00:04:51.660><c> see</c><00:04:51.810><c> you</c><00:04:51.870><c> in</c><00:04:51.990><c> the</c>

00:04:52.040 --> 00:04:52.050 align:start position:0%
up and running and we'll see you in the
 

00:04:52.050 --> 00:04:54.530 align:start position:0%
up and running and we'll see you in the
next<00:04:52.260><c> video</c>

