WEBVTT
Kind: captions
Language: en

00:00:09.000 --> 00:00:11.470 align:start position:0%
 
hello<00:00:09.360><c> everybody</c><00:00:10.240><c> Welcome</c><00:00:10.599><c> to</c><00:00:10.800><c> the</c><00:00:10.920><c> overview</c>

00:00:11.470 --> 00:00:11.480 align:start position:0%
hello everybody Welcome to the overview
 

00:00:11.480 --> 00:00:13.709 align:start position:0%
hello everybody Welcome to the overview
of<00:00:11.599><c> the</c><00:00:11.759><c> climate</c><00:00:12.200><c> data</c><00:00:12.440><c> and</c><00:00:12.559><c> bias</c><00:00:12.920><c> adjustment</c>

00:00:13.709 --> 00:00:13.719 align:start position:0%
of the climate data and bias adjustment
 

00:00:13.719 --> 00:00:16.990 align:start position:0%
of the climate data and bias adjustment
be<00:00:14.160><c> the</c><00:00:14.320><c> paneuropean</c><00:00:14.839><c> climate</c><00:00:15.519><c> database</c><00:00:16.440><c> pacd</c>

00:00:16.990 --> 00:00:17.000 align:start position:0%
be the paneuropean climate database pacd
 

00:00:17.000 --> 00:00:19.550 align:start position:0%
be the paneuropean climate database pacd
in<00:00:17.160><c> short</c><00:00:18.000><c> version</c>

00:00:19.550 --> 00:00:19.560 align:start position:0%
in short version
 

00:00:19.560 --> 00:00:22.590 align:start position:0%
in short version
4.2<00:00:20.560><c> the</c><00:00:20.760><c> topics</c><00:00:21.240><c> that</c><00:00:21.400><c> will</c><00:00:21.560><c> be</c><00:00:21.800><c> covered</c><00:00:22.400><c> are</c>

00:00:22.590 --> 00:00:22.600 align:start position:0%
4.2 the topics that will be covered are
 

00:00:22.600 --> 00:00:24.429 align:start position:0%
4.2 the topics that will be covered are
a<00:00:22.760><c> list</c><00:00:23.000><c> of</c><00:00:23.199><c> past</c><00:00:23.400><c> and</c><00:00:23.560><c> future</c><00:00:24.000><c> climate</c>

00:00:24.429 --> 00:00:24.439 align:start position:0%
a list of past and future climate
 

00:00:24.439 --> 00:00:27.029 align:start position:0%
a list of past and future climate
indicators<00:00:25.400><c> their</c><00:00:25.680><c> temporal</c><00:00:26.279><c> and</c><00:00:26.439><c> spal</c>

00:00:27.029 --> 00:00:27.039 align:start position:0%
indicators their temporal and spal
 

00:00:27.039 --> 00:00:29.390 align:start position:0%
indicators their temporal and spal
characteristics<00:00:28.039><c> and</c><00:00:28.279><c> some</c><00:00:28.599><c> examples</c><00:00:29.199><c> of</c>

00:00:29.390 --> 00:00:29.400 align:start position:0%
characteristics and some examples of
 

00:00:29.400 --> 00:00:32.150 align:start position:0%
characteristics and some examples of
application<00:00:30.160><c> to</c><00:00:30.320><c> the</c><00:00:30.480><c> PSD</c><00:00:30.920><c> related</c>

00:00:32.150 --> 00:00:32.160 align:start position:0%
application to the PSD related
 

00:00:32.160 --> 00:00:34.910 align:start position:0%
application to the PSD related
analysis<00:00:33.160><c> more</c><00:00:33.879><c> we</c><00:00:34.000><c> will</c><00:00:34.280><c> explore</c><00:00:34.800><c> the</c>

00:00:34.910 --> 00:00:34.920 align:start position:0%
analysis more we will explore the
 

00:00:34.920 --> 00:00:36.950 align:start position:0%
analysis more we will explore the
general<00:00:35.399><c> concepts</c><00:00:36.040><c> beyond</c><00:00:36.360><c> the</c><00:00:36.480><c> the</c><00:00:36.600><c> bias</c>

00:00:36.950 --> 00:00:36.960 align:start position:0%
general concepts beyond the the bias
 

00:00:36.960 --> 00:00:38.830 align:start position:0%
general concepts beyond the the bias
adjustment<00:00:37.520><c> methods</c><00:00:38.040><c> of</c><00:00:38.200><c> the</c><00:00:38.320><c> Delta</c><00:00:38.600><c> and</c><00:00:38.719><c> the</c>

00:00:38.830 --> 00:00:38.840 align:start position:0%
adjustment methods of the Delta and the
 

00:00:38.840 --> 00:00:41.670 align:start position:0%
adjustment methods of the Delta and the
cdft<00:00:39.760><c> and</c><00:00:39.920><c> their</c><00:00:40.160><c> application</c><00:00:40.800><c> to</c><00:00:41.000><c> the</c><00:00:41.120><c> PSD</c>

00:00:41.670 --> 00:00:41.680 align:start position:0%
cdft and their application to the PSD
 

00:00:41.680 --> 00:00:43.069 align:start position:0%
cdft and their application to the PSD
climate

00:00:43.069 --> 00:00:43.079 align:start position:0%
climate
 

00:00:43.079 --> 00:00:45.590 align:start position:0%
climate
indicators<00:00:44.079><c> the</c><00:00:44.200><c> climate</c><00:00:44.640><c> indicators</c><00:00:45.440><c> that</c>

00:00:45.590 --> 00:00:45.600 align:start position:0%
indicators the climate indicators that
 

00:00:45.600 --> 00:00:49.110 align:start position:0%
indicators the climate indicators that
will<00:00:45.760><c> be</c><00:00:46.000><c> part</c><00:00:46.199><c> of</c><00:00:46.360><c> the</c><00:00:46.520><c> PSD</c><00:00:47.039><c> 4.2</c><00:00:47.840><c> database</c><00:00:48.760><c> are</c>

00:00:49.110 --> 00:00:49.120 align:start position:0%
will be part of the PSD 4.2 database are
 

00:00:49.120 --> 00:00:51.069 align:start position:0%
will be part of the PSD 4.2 database are
temperature<00:00:50.079><c> population</c><00:00:50.559><c> weight</c><00:00:50.840><c> at</c>

00:00:51.069 --> 00:00:51.079 align:start position:0%
temperature population weight at
 

00:00:51.079 --> 00:00:53.950 align:start position:0%
temperature population weight at
temperature<00:00:52.199><c> precipitation</c><00:00:53.199><c> wind</c><00:00:53.480><c> speed</c><00:00:53.760><c> at</c>

00:00:53.950 --> 00:00:53.960 align:start position:0%
temperature precipitation wind speed at
 

00:00:53.960 --> 00:00:56.270 align:start position:0%
temperature precipitation wind speed at
10<00:00:54.160><c> and</c><00:00:54.359><c> 100</c><00:00:54.640><c> m</c><00:00:55.079><c> height</c><00:00:55.600><c> and</c><00:00:55.840><c> Global</c>

00:00:56.270 --> 00:00:56.280 align:start position:0%
10 and 100 m height and Global
 

00:00:56.280 --> 00:00:57.790 align:start position:0%
10 and 100 m height and Global
horizontally

00:00:57.790 --> 00:00:57.800 align:start position:0%
horizontally
 

00:00:57.800 --> 00:01:00.750 align:start position:0%
horizontally
Radiance<00:00:58.800><c> all</c><00:00:58.960><c> of</c><00:00:59.160><c> them</c><00:00:59.440><c> will</c><00:00:59.600><c> be</c><00:01:00.120><c> delivered</c>

00:01:00.750 --> 00:01:00.760 align:start position:0%
Radiance all of them will be delivered
 

00:01:00.760 --> 00:01:04.149 align:start position:0%
Radiance all of them will be delivered
in<00:01:00.879><c> a</c><00:01:01.039><c> grided</c><00:01:01.519><c> format</c><00:01:02.079><c> the</c><00:01:02.199><c> net</c><00:01:02.440><c> CDF</c><00:01:03.000><c> format</c><00:01:03.879><c> at</c>

00:01:04.149 --> 00:01:04.159 align:start position:0%
in a grided format the net CDF format at
 

00:01:04.159 --> 00:01:07.710 align:start position:0%
in a grided format the net CDF format at
0.25<00:01:05.159><c> de</c><00:01:05.680><c> resolution</c><00:01:06.680><c> that</c><00:01:07.000><c> at</c><00:01:07.200><c> the</c><00:01:07.320><c> European</c>

00:01:07.710 --> 00:01:07.720 align:start position:0%
0.25 de resolution that at the European
 

00:01:07.720 --> 00:01:10.990 align:start position:0%
0.25 de resolution that at the European
latitudes<00:01:08.439><c> correspond</c><00:01:09.000><c> roughly</c><00:01:09.600><c> to</c><00:01:09.880><c> 25</c><00:01:10.479><c> km</c>

00:01:10.990 --> 00:01:11.000 align:start position:0%
latitudes correspond roughly to 25 km
 

00:01:11.000 --> 00:01:12.390 align:start position:0%
latitudes correspond roughly to 25 km
spal

00:01:12.390 --> 00:01:12.400 align:start position:0%
spal
 

00:01:12.400 --> 00:01:14.830 align:start position:0%
spal
resolution<00:01:13.400><c> the</c><00:01:13.520><c> same</c><00:01:13.759><c> indicators</c><00:01:14.479><c> will</c><00:01:14.640><c> be</c>

00:01:14.830 --> 00:01:14.840 align:start position:0%
resolution the same indicators will be
 

00:01:14.840 --> 00:01:17.030 align:start position:0%
resolution the same indicators will be
also<00:01:15.080><c> available</c><00:01:15.520><c> at</c><00:01:15.720><c> onshore</c><00:01:16.240><c> and</c><00:01:16.520><c> offshore</c>

00:01:17.030 --> 00:01:17.040 align:start position:0%
also available at onshore and offshore
 

00:01:17.040 --> 00:01:19.469 align:start position:0%
also available at onshore and offshore
zones<00:01:17.799><c> and</c><00:01:18.040><c> for</c><00:01:18.360><c> temperature</c><00:01:19.040><c> also</c><00:01:19.320><c> in</c>

00:01:19.469 --> 00:01:19.479 align:start position:0%
zones and for temperature also in
 

00:01:19.479 --> 00:01:21.710 align:start position:0%
zones and for temperature also in
selected

00:01:21.710 --> 00:01:21.720 align:start position:0%
selected
 

00:01:21.720 --> 00:01:24.550 align:start position:0%
selected
cities<00:01:22.720><c> past</c><00:01:23.079><c> climate</c><00:01:23.439><c> indicators</c><00:01:24.200><c> will</c><00:01:24.400><c> be</c>

00:01:24.550 --> 00:01:24.560 align:start position:0%
cities past climate indicators will be
 

00:01:24.560 --> 00:01:27.749 align:start position:0%
cities past climate indicators will be
based<00:01:24.799><c> on</c><00:01:24.920><c> the</c><00:01:25.000><c> ER</c><00:01:25.600><c> analysis</c><00:01:26.159><c> from</c><00:01:26.759><c> ecmwf</c>

00:01:27.749 --> 00:01:27.759 align:start position:0%
based on the ER analysis from ecmwf
 

00:01:27.759 --> 00:01:30.670 align:start position:0%
based on the ER analysis from ecmwf
covering<00:01:28.159><c> the</c><00:01:28.320><c> years</c><00:01:28.640><c> from</c><00:01:28.920><c> 1950</c><00:01:29.920><c> to</c><00:01:30.079><c> August</c>

00:01:30.670 --> 00:01:30.680 align:start position:0%
covering the years from 1950 to August
 

00:01:30.680 --> 00:01:33.149 align:start position:0%
covering the years from 1950 to August
2024<00:01:31.680><c> at</c><00:01:31.880><c> hourly</c>

00:01:33.149 --> 00:01:33.159 align:start position:0%
2024 at hourly
 

00:01:33.159 --> 00:01:35.910 align:start position:0%
2024 at hourly
resolution<00:01:34.159><c> future</c><00:01:34.600><c> climate</c><00:01:34.960><c> indicators</c><00:01:35.720><c> are</c>

00:01:35.910 --> 00:01:35.920 align:start position:0%
resolution future climate indicators are
 

00:01:35.920 --> 00:01:38.270 align:start position:0%
resolution future climate indicators are
based<00:01:36.200><c> on</c><00:01:36.320><c> six</c><00:01:36.600><c> models</c><00:01:37.200><c> from</c><00:01:37.399><c> the</c><00:01:37.520><c> sim6</c>

00:01:38.270 --> 00:01:38.280 align:start position:0%
based on six models from the sim6
 

00:01:38.280 --> 00:01:40.510 align:start position:0%
based on six models from the sim6
project<00:01:39.200><c> which</c><00:01:39.439><c> name</c><00:01:39.640><c> is</c><00:01:39.840><c> reported</c><00:01:40.240><c> in</c><00:01:40.360><c> the</c>

00:01:40.510 --> 00:01:40.520 align:start position:0%
project which name is reported in the
 

00:01:40.520 --> 00:01:43.270 align:start position:0%
project which name is reported in the
list<00:01:40.759><c> on</c><00:01:40.920><c> the</c><00:01:41.320><c> right</c><00:01:42.320><c> to</c><00:01:42.520><c> share</c><00:01:42.880><c> the</c><00:01:43.000><c> social</c>

00:01:43.270 --> 00:01:43.280 align:start position:0%
list on the right to share the social
 

00:01:43.280 --> 00:01:45.910 align:start position:0%
list on the right to share the social
economic<00:01:43.720><c> Pathways</c><00:01:44.240><c> will</c><00:01:44.399><c> be</c><00:01:44.719><c> considered</c><00:01:45.719><c> the</c>

00:01:45.910 --> 00:01:45.920 align:start position:0%
economic Pathways will be considered the
 

00:01:45.920 --> 00:01:49.510 align:start position:0%
economic Pathways will be considered the
1<00:01:46.399><c> 2.6</c><00:01:47.399><c> and</c><00:01:47.560><c> the</c><00:01:47.680><c> two</c>

00:01:49.510 --> 00:01:49.520 align:start position:0%
1 2.6 and the two
 

00:01:49.520 --> 00:01:52.709 align:start position:0%
1 2.6 and the two
4.5<00:01:50.520><c> the</c><00:01:50.680><c> climate</c><00:01:51.079><c> projection</c><00:01:51.560><c> models</c><00:01:52.360><c> cover</c>

00:01:52.709 --> 00:01:52.719 align:start position:0%
4.5 the climate projection models cover
 

00:01:52.719 --> 00:01:55.270 align:start position:0%
4.5 the climate projection models cover
the<00:01:52.840><c> years</c><00:01:53.240><c> between</c><00:01:53.640><c> 2015</c><00:01:54.640><c> and</c><00:01:54.799><c> the</c><00:01:54.920><c> end</c><00:01:55.119><c> of</c>

00:01:55.270 --> 00:01:55.280 align:start position:0%
the years between 2015 and the end of
 

00:01:55.280 --> 00:01:57.990 align:start position:0%
the years between 2015 and the end of
the<00:01:55.439><c> century</c><00:01:56.000><c> at</c><00:01:56.200><c> hourly</c>

00:01:57.990 --> 00:01:58.000 align:start position:0%
the century at hourly
 

00:01:58.000 --> 00:02:00.429 align:start position:0%
the century at hourly
resolution<00:01:59.000><c> regarding</c><00:01:59.479><c> now</c><00:01:59.680><c> the</c><00:01:59.960><c> application</c>

00:02:00.429 --> 00:02:00.439 align:start position:0%
resolution regarding now the application
 

00:02:00.439 --> 00:02:03.230 align:start position:0%
resolution regarding now the application
of<00:02:00.719><c> climate</c><00:02:01.079><c> indicators</c><00:02:01.640><c> in</c><00:02:01.759><c> the</c><00:02:01.920><c> PSD</c><00:02:02.360><c> domain</c>

00:02:03.230 --> 00:02:03.240 align:start position:0%
of climate indicators in the PSD domain
 

00:02:03.240 --> 00:02:05.910 align:start position:0%
of climate indicators in the PSD domain
the<00:02:03.399><c> figures</c><00:02:03.960><c> represent</c><00:02:04.719><c> in</c><00:02:04.880><c> the</c><00:02:05.039><c> top</c><00:02:05.320><c> left</c><00:02:05.759><c> an</c>

00:02:05.910 --> 00:02:05.920 align:start position:0%
the figures represent in the top left an
 

00:02:05.920 --> 00:02:07.749 align:start position:0%
the figures represent in the top left an
example<00:02:06.280><c> of</c><00:02:06.439><c> variability</c><00:02:07.159><c> of</c><00:02:07.320><c> the</c><00:02:07.439><c> six</c>

00:02:07.749 --> 00:02:07.759 align:start position:0%
example of variability of the six
 

00:02:07.759 --> 00:02:09.949 align:start position:0%
example of variability of the six
climate<00:02:08.160><c> models</c><00:02:08.560><c> for</c><00:02:08.840><c> temperature</c><00:02:09.520><c> given</c><00:02:09.759><c> a</c>

00:02:09.949 --> 00:02:09.959 align:start position:0%
climate models for temperature given a
 

00:02:09.959 --> 00:02:13.110 align:start position:0%
climate models for temperature given a
specific<00:02:10.720><c> SSP</c><00:02:11.720><c> while</c><00:02:11.879><c> in</c><00:02:12.040><c> the</c><00:02:12.200><c> bottom</c><00:02:12.800><c> for</c><00:02:13.000><c> the</c>

00:02:13.110 --> 00:02:13.120 align:start position:0%
specific SSP while in the bottom for the
 

00:02:13.120 --> 00:02:15.990 align:start position:0%
specific SSP while in the bottom for the
10<00:02:13.360><c> m</c><00:02:13.599><c> wind</c><00:02:13.879><c> speed</c><00:02:14.599><c> the</c><00:02:14.720><c> variability</c><00:02:15.440><c> of</c><00:02:15.879><c> the</c>

00:02:15.990 --> 00:02:16.000 align:start position:0%
10 m wind speed the variability of the
 

00:02:16.000 --> 00:02:18.710 align:start position:0%
10 m wind speed the variability of the
ssps<00:02:16.680><c> for</c><00:02:16.879><c> a</c><00:02:17.120><c> fixed</c><00:02:17.560><c> climate</c><00:02:17.959><c> projection</c>

00:02:18.710 --> 00:02:18.720 align:start position:0%
ssps for a fixed climate projection
 

00:02:18.720 --> 00:02:21.589 align:start position:0%
ssps for a fixed climate projection
model<00:02:19.720><c> in</c><00:02:19.840><c> the</c><00:02:20.040><c> top</c><00:02:20.319><c> right</c><00:02:20.760><c> a</c><00:02:20.920><c> comparison</c><00:02:21.440><c> of</c>

00:02:21.589 --> 00:02:21.599 align:start position:0%
model in the top right a comparison of
 

00:02:21.599 --> 00:02:23.309 align:start position:0%
model in the top right a comparison of
the<00:02:21.760><c> temperature</c><00:02:22.280><c> distribution</c><00:02:22.879><c> for</c><00:02:23.080><c> three</c>

00:02:23.309 --> 00:02:23.319 align:start position:0%
the temperature distribution for three
 

00:02:23.319 --> 00:02:25.949 align:start position:0%
the temperature distribution for three
climate<00:02:23.680><c> model</c><00:02:24.239><c> and</c><00:02:24.480><c> historical</c><00:02:25.080><c> climate</c>

00:02:25.949 --> 00:02:25.959 align:start position:0%
climate model and historical climate
 

00:02:25.959 --> 00:02:28.030 align:start position:0%
climate model and historical climate
while<00:02:26.200><c> at</c><00:02:26.360><c> the</c><00:02:26.560><c> bottom</c><00:02:27.040><c> we</c><00:02:27.160><c> find</c><00:02:27.440><c> an</c><00:02:27.640><c> example</c>

00:02:28.030 --> 00:02:28.040 align:start position:0%
while at the bottom we find an example
 

00:02:28.040 --> 00:02:32.470 align:start position:0%
while at the bottom we find an example
of<00:02:28.200><c> a</c><00:02:28.840><c> map</c><00:02:29.920><c> of</c><00:02:30.120><c> grided</c><00:02:30.560><c> wind</c><00:02:30.840><c> speed</c><00:02:31.120><c> at</c><00:02:31.400><c> 100</c><00:02:31.680><c> m</c>

00:02:32.470 --> 00:02:32.480 align:start position:0%
of a map of grided wind speed at 100 m
 

00:02:32.480 --> 00:02:34.750 align:start position:0%
of a map of grided wind speed at 100 m
for<00:02:32.640><c> a</c><00:02:32.879><c> particular</c><00:02:33.400><c> time</c><00:02:33.720><c> stamp</c><00:02:34.160><c> and</c><00:02:34.360><c> climate</c>

00:02:34.750 --> 00:02:34.760 align:start position:0%
for a particular time stamp and climate
 

00:02:34.760 --> 00:02:35.910 align:start position:0%
for a particular time stamp and climate
projection

00:02:35.910 --> 00:02:35.920 align:start position:0%
projection
 

00:02:35.920 --> 00:02:38.910 align:start position:0%
projection
model<00:02:36.920><c> as</c><00:02:37.120><c> can</c><00:02:37.239><c> be</c><00:02:37.360><c> seen</c><00:02:37.599><c> from</c><00:02:37.879><c> these</c><00:02:38.080><c> pictures</c>

00:02:38.910 --> 00:02:38.920 align:start position:0%
model as can be seen from these pictures
 

00:02:38.920 --> 00:02:41.030 align:start position:0%
model as can be seen from these pictures
the<00:02:39.080><c> special</c><00:02:39.480><c> and</c><00:02:39.680><c> temporal</c><00:02:40.200><c> characteristics</c>

00:02:41.030 --> 00:02:41.040 align:start position:0%
the special and temporal characteristics
 

00:02:41.040 --> 00:02:43.790 align:start position:0%
the special and temporal characteristics
of<00:02:41.319><c> the</c><00:02:41.480><c> climate</c><00:02:42.200><c> indicators</c><00:02:43.200><c> combined</c><00:02:43.680><c> with</c>

00:02:43.790 --> 00:02:43.800 align:start position:0%
of the climate indicators combined with
 

00:02:43.800 --> 00:02:46.110 align:start position:0%
of the climate indicators combined with
the<00:02:43.959><c> capability</c><00:02:44.560><c> to</c><00:02:44.720><c> utilize</c><00:02:45.159><c> an</c><00:02:45.360><c> ensemble</c><00:02:45.959><c> on</c>

00:02:46.110 --> 00:02:46.120 align:start position:0%
the capability to utilize an ensemble on
 

00:02:46.120 --> 00:02:48.229 align:start position:0%
the capability to utilize an ensemble on
models<00:02:46.519><c> and</c><00:02:46.720><c> scenarios</c><00:02:47.680><c> enable</c><00:02:48.080><c> a</c>

00:02:48.229 --> 00:02:48.239 align:start position:0%
models and scenarios enable a
 

00:02:48.239 --> 00:02:50.070 align:start position:0%
models and scenarios enable a
comprehensive<00:02:48.800><c> and</c><00:02:49.000><c> optimal</c><00:02:49.440><c> assessment</c><00:02:49.879><c> of</c>

00:02:50.070 --> 00:02:50.080 align:start position:0%
comprehensive and optimal assessment of
 

00:02:50.080 --> 00:02:52.990 align:start position:0%
comprehensive and optimal assessment of
climate<00:02:50.519><c> patterns</c><00:02:51.159><c> across</c><00:02:51.680><c> past</c><00:02:52.239><c> present</c><00:02:52.800><c> and</c>

00:02:52.990 --> 00:02:53.000 align:start position:0%
climate patterns across past present and
 

00:02:53.000 --> 00:02:53.990 align:start position:0%
climate patterns across past present and
future

00:02:53.990 --> 00:02:54.000 align:start position:0%
future
 

00:02:54.000 --> 00:02:56.470 align:start position:0%
future
timelines<00:02:55.000><c> this</c><00:02:55.239><c> approach</c><00:02:55.720><c> anounces</c><00:02:56.319><c> the</c>

00:02:56.470 --> 00:02:56.480 align:start position:0%
timelines this approach anounces the
 

00:02:56.480 --> 00:02:58.350 align:start position:0%
timelines this approach anounces the
accuracy<00:02:56.879><c> of</c><00:02:57.040><c> climate</c><00:02:57.440><c> projections</c><00:02:58.200><c> and</c>

00:02:58.350 --> 00:02:58.360 align:start position:0%
accuracy of climate projections and
 

00:02:58.360 --> 00:02:59.990 align:start position:0%
accuracy of climate projections and
improves<00:02:58.800><c> our</c><00:02:59.400><c> understanding</c><00:02:59.599><c> of</c><00:02:59.720><c> the</c><00:02:59.879><c> the</c>

00:02:59.990 --> 00:03:00.000 align:start position:0%
improves our understanding of the the
 

00:03:00.000 --> 00:03:02.110 align:start position:0%
improves our understanding of the the
climate<00:03:00.840><c> allowing</c><00:03:01.280><c> for</c><00:03:01.480><c> more</c><00:03:01.680><c> informed</c>

00:03:02.110 --> 00:03:02.120 align:start position:0%
climate allowing for more informed
 

00:03:02.120 --> 00:03:04.470 align:start position:0%
climate allowing for more informed
decision<00:03:02.519><c> making</c><00:03:03.120><c> in</c><00:03:03.319><c> response</c><00:03:03.720><c> to</c><00:03:03.959><c> climate</c>

00:03:04.470 --> 00:03:04.480 align:start position:0%
decision making in response to climate
 

00:03:04.480 --> 00:03:06.229 align:start position:0%
decision making in response to climate
variability<00:03:05.120><c> and</c>

00:03:06.229 --> 00:03:06.239 align:start position:0%
variability and
 

00:03:06.239 --> 00:03:09.030 align:start position:0%
variability and
change<00:03:07.239><c> now</c><00:03:07.560><c> moving</c><00:03:08.040><c> to</c><00:03:08.200><c> the</c><00:03:08.400><c> topic</c><00:03:08.720><c> of</c><00:03:08.840><c> the</c>

00:03:09.030 --> 00:03:09.040 align:start position:0%
change now moving to the topic of the
 

00:03:09.040 --> 00:03:11.910 align:start position:0%
change now moving to the topic of the
Bas<00:03:09.360><c> adjustment</c><00:03:10.360><c> the</c><00:03:10.519><c> data</c><00:03:10.959><c> method</c><00:03:11.480><c> allows</c>

00:03:11.910 --> 00:03:11.920 align:start position:0%
Bas adjustment the data method allows
 

00:03:11.920 --> 00:03:14.110 align:start position:0%
Bas adjustment the data method allows
the<00:03:12.080><c> adjustment</c><00:03:12.599><c> of</c><00:03:12.720><c> a</c><00:03:12.840><c> source</c><00:03:13.239><c> data</c><00:03:13.519><c> set</c>

00:03:14.110 --> 00:03:14.120 align:start position:0%
the adjustment of a source data set
 

00:03:14.120 --> 00:03:16.110 align:start position:0%
the adjustment of a source data set
based<00:03:14.360><c> on</c><00:03:14.480><c> a</c><00:03:14.640><c> Target</c><00:03:15.080><c> variable</c><00:03:15.959><c> by</c>

00:03:16.110 --> 00:03:16.120 align:start position:0%
based on a Target variable by
 

00:03:16.120 --> 00:03:18.789 align:start position:0%
based on a Target variable by
calculating<00:03:16.680><c> the</c><00:03:16.840><c> change</c><00:03:17.239><c> factors</c><00:03:17.720><c> or</c><00:03:17.959><c> Deltas</c>

00:03:18.789 --> 00:03:18.799 align:start position:0%
calculating the change factors or Deltas
 

00:03:18.799 --> 00:03:21.630 align:start position:0%
calculating the change factors or Deltas
from<00:03:19.040><c> the</c><00:03:19.200><c> average</c><00:03:19.680><c> data</c><00:03:19.959><c> sets</c><00:03:20.920><c> as</c><00:03:21.120><c> explained</c>

00:03:21.630 --> 00:03:21.640 align:start position:0%
from the average data sets as explained
 

00:03:21.640 --> 00:03:23.830 align:start position:0%
from the average data sets as explained
in<00:03:22.000><c> in</c><00:03:22.120><c> the</c><00:03:22.280><c> top</c>

00:03:23.830 --> 00:03:23.840 align:start position:0%
in in the top
 

00:03:23.840 --> 00:03:26.630 align:start position:0%
in in the top
picture<00:03:24.840><c> in</c><00:03:25.040><c> particular</c><00:03:25.760><c> the</c><00:03:25.920><c> averages</c><00:03:26.440><c> of</c>

00:03:26.630 --> 00:03:26.640 align:start position:0%
picture in particular the averages of
 

00:03:26.640 --> 00:03:29.229 align:start position:0%
picture in particular the averages of
the<00:03:26.879><c> adjusted</c><00:03:27.360><c> source</c><00:03:27.680><c> and</c><00:03:27.879><c> Target</c><00:03:28.360><c> data</c><00:03:28.640><c> sets</c>

00:03:29.229 --> 00:03:29.239 align:start position:0%
the adjusted source and Target data sets
 

00:03:29.239 --> 00:03:31.030 align:start position:0%
the adjusted source and Target data sets
are<00:03:29.439><c> matched</c><00:03:29.959><c> by</c><00:03:30.120><c> performing</c><00:03:30.519><c> a</c><00:03:30.640><c> scaling</c>

00:03:31.030 --> 00:03:31.040 align:start position:0%
are matched by performing a scaling
 

00:03:31.040 --> 00:03:33.110 align:start position:0%
are matched by performing a scaling
adjustment<00:03:31.560><c> of</c><00:03:31.720><c> the</c><00:03:31.840><c> source</c><00:03:32.439><c> with</c><00:03:32.560><c> the</c><00:03:32.720><c> Delta</c>

00:03:33.110 --> 00:03:33.120 align:start position:0%
adjustment of the source with the Delta
 

00:03:33.120 --> 00:03:35.789 align:start position:0%
adjustment of the source with the Delta
factors<00:03:34.000><c> as</c><00:03:34.200><c> shown</c><00:03:34.480><c> in</c><00:03:34.599><c> the</c><00:03:34.760><c> bottom</c>

00:03:35.789 --> 00:03:35.799 align:start position:0%
factors as shown in the bottom
 

00:03:35.799 --> 00:03:38.350 align:start position:0%
factors as shown in the bottom
picture<00:03:36.799><c> from</c><00:03:37.040><c> the</c><00:03:37.200><c> bottom</c><00:03:37.840><c> picture</c>

00:03:38.350 --> 00:03:38.360 align:start position:0%
picture from the bottom picture
 

00:03:38.360 --> 00:03:40.710 align:start position:0%
picture from the bottom picture
distributions<00:03:39.360><c> it</c><00:03:39.720><c> indeed</c><00:03:40.280><c> it</c><00:03:40.400><c> can</c><00:03:40.560><c> be</c>

00:03:40.710 --> 00:03:40.720 align:start position:0%
distributions it indeed it can be
 

00:03:40.720 --> 00:03:42.670 align:start position:0%
distributions it indeed it can be
noticed<00:03:41.239><c> that</c><00:03:41.480><c> the</c><00:03:41.560><c> mean</c><00:03:41.879><c> of</c><00:03:42.000><c> the</c><00:03:42.159><c> corrected</c>

00:03:42.670 --> 00:03:42.680 align:start position:0%
noticed that the mean of the corrected
 

00:03:42.680 --> 00:03:45.190 align:start position:0%
noticed that the mean of the corrected
source<00:03:43.680><c> that</c><00:03:43.920><c> constitutes</c><00:03:44.560><c> the</c><00:03:44.680><c> output</c><00:03:45.080><c> of</c>

00:03:45.190 --> 00:03:45.200 align:start position:0%
source that constitutes the output of
 

00:03:45.200 --> 00:03:47.830 align:start position:0%
source that constitutes the output of
the<00:03:45.319><c> Bice</c><00:03:45.599><c> adjustment</c><00:03:46.200><c> procedure</c><00:03:47.159><c> matches</c>

00:03:47.830 --> 00:03:47.840 align:start position:0%
the Bice adjustment procedure matches
 

00:03:47.840 --> 00:03:50.509 align:start position:0%
the Bice adjustment procedure matches
exactly<00:03:48.400><c> the</c><00:03:48.560><c> average</c><00:03:48.920><c> of</c><00:03:49.080><c> the</c><00:03:49.200><c> target</c>

00:03:50.509 --> 00:03:50.519 align:start position:0%
exactly the average of the target
 

00:03:50.519 --> 00:03:52.869 align:start position:0%
exactly the average of the target
distribution<00:03:51.519><c> the</c><00:03:51.720><c> Delta</c><00:03:52.079><c> method</c><00:03:52.519><c> is</c><00:03:52.720><c> the</c>

00:03:52.869 --> 00:03:52.879 align:start position:0%
distribution the Delta method is the
 

00:03:52.879 --> 00:03:55.750 align:start position:0%
distribution the Delta method is the
simplest<00:03:53.400><c> B</c><00:03:53.680><c> adjustment</c><00:03:54.360><c> method</c><00:03:55.239><c> although</c><00:03:55.640><c> it</c>

00:03:55.750 --> 00:03:55.760 align:start position:0%
simplest B adjustment method although it
 

00:03:55.760 --> 00:03:57.710 align:start position:0%
simplest B adjustment method although it
is<00:03:55.959><c> limited</c><00:03:56.519><c> by</c><00:03:56.680><c> the</c><00:03:56.799><c> assumption</c><00:03:57.360><c> that</c><00:03:57.560><c> the</c>

00:03:57.710 --> 00:03:57.720 align:start position:0%
is limited by the assumption that the
 

00:03:57.720 --> 00:04:00.949 align:start position:0%
is limited by the assumption that the
bias<00:03:58.239><c> is</c><00:03:58.480><c> constant</c><00:03:59.000><c> in</c><00:03:59.280><c> time</c>

00:04:00.949 --> 00:04:00.959 align:start position:0%
bias is constant in time
 

00:04:00.959 --> 00:04:02.789 align:start position:0%
bias is constant in time
the<00:04:01.159><c> cumulative</c><00:04:01.760><c> distribution</c><00:04:02.400><c> function</c>

00:04:02.789 --> 00:04:02.799 align:start position:0%
the cumulative distribution function
 

00:04:02.799 --> 00:04:05.910 align:start position:0%
the cumulative distribution function
transform<00:04:03.439><c> method</c><00:04:04.040><c> in</c><00:04:04.200><c> short</c><00:04:04.480><c> the</c><00:04:04.599><c> cdft</c><00:04:05.599><c> is</c><00:04:05.720><c> a</c>

00:04:05.910 --> 00:04:05.920 align:start position:0%
transform method in short the cdft is a
 

00:04:05.920 --> 00:04:07.869 align:start position:0%
transform method in short the cdft is a
probabilistic<00:04:06.519><c> non</c><00:04:06.760><c> scaling</c><00:04:07.159><c> method</c><00:04:07.680><c> that</c>

00:04:07.869 --> 00:04:07.879 align:start position:0%
probabilistic non scaling method that
 

00:04:07.879 --> 00:04:09.949 align:start position:0%
probabilistic non scaling method that
models<00:04:08.360><c> the</c><00:04:08.519><c> statistical</c><00:04:09.159><c> relationship</c>

00:04:09.949 --> 00:04:09.959 align:start position:0%
models the statistical relationship
 

00:04:09.959 --> 00:04:12.149 align:start position:0%
models the statistical relationship
between<00:04:10.239><c> a</c><00:04:10.400><c> Target</c><00:04:10.840><c> data</c><00:04:11.120><c> set</c><00:04:11.519><c> and</c><00:04:11.640><c> The</c><00:04:11.760><c> Source</c>

00:04:12.149 --> 00:04:12.159 align:start position:0%
between a Target data set and The Source
 

00:04:12.159 --> 00:04:15.670 align:start position:0%
between a Target data set and The Source
by<00:04:12.480><c> projection</c><00:04:13.480><c> model</c><00:04:14.480><c> these</c><00:04:14.720><c> nonparametric</c>

00:04:15.670 --> 00:04:15.680 align:start position:0%
by projection model these nonparametric
 

00:04:15.680 --> 00:04:18.229 align:start position:0%
by projection model these nonparametric
correspondences<00:04:16.680><c> or</c><00:04:16.919><c> transfer</c><00:04:17.519><c> functions</c>

00:04:18.229 --> 00:04:18.239 align:start position:0%
correspondences or transfer functions
 

00:04:18.239 --> 00:04:20.430 align:start position:0%
correspondences or transfer functions
are<00:04:18.479><c> applied</c><00:04:18.880><c> to</c><00:04:19.000><c> a</c><00:04:19.120><c> predict</c><00:04:19.440><c> and</c><00:04:19.639><c> CDF</c><00:04:20.280><c> to</c>

00:04:20.430 --> 00:04:20.440 align:start position:0%
are applied to a predict and CDF to
 

00:04:20.440 --> 00:04:22.189 align:start position:0%
are applied to a predict and CDF to
obtain<00:04:20.840><c> the</c><00:04:21.000><c> corrected</c>

00:04:22.189 --> 00:04:22.199 align:start position:0%
obtain the corrected
 

00:04:22.199 --> 00:04:25.469 align:start position:0%
obtain the corrected
output<00:04:23.199><c> in</c><00:04:23.400><c> particular</c><00:04:24.280><c> the</c><00:04:24.479><c> method</c><00:04:24.960><c> uses</c><00:04:25.360><c> the</c>

00:04:25.469 --> 00:04:25.479 align:start position:0%
output in particular the method uses the
 

00:04:25.479 --> 00:04:27.670 align:start position:0%
output in particular the method uses the
cdfs<00:04:26.080><c> of</c><00:04:26.280><c> Target</c><00:04:26.600><c> and</c><00:04:26.759><c> Source</c><00:04:27.080><c> in</c><00:04:27.199><c> a</c><00:04:27.360><c> common</c>

00:04:27.670 --> 00:04:27.680 align:start position:0%
cdfs of Target and Source in a common
 

00:04:27.680 --> 00:04:29.710 align:start position:0%
cdfs of Target and Source in a common
historical<00:04:28.240><c> period</c><00:04:28.919><c> to</c><00:04:29.080><c> model</c><00:04:29.400><c> the</c><00:04:29.479><c> future</c>

00:04:29.710 --> 00:04:29.720 align:start position:0%
historical period to model the future
 

00:04:29.720 --> 00:04:31.990 align:start position:0%
historical period to model the future
future<00:04:29.960><c> period</c><00:04:30.240><c> the</c><00:04:30.360><c> CDF</c><00:04:31.160><c> assuming</c><00:04:31.680><c> that</c><00:04:31.880><c> the</c>

00:04:31.990 --> 00:04:32.000 align:start position:0%
future period the CDF assuming that the
 

00:04:32.000 --> 00:04:34.390 align:start position:0%
future period the CDF assuming that the
future<00:04:32.280><c> predicton</c><00:04:32.759><c> CDF</c><00:04:33.199><c> is</c><00:04:33.320><c> known</c><00:04:33.880><c> as</c><00:04:34.080><c> shown</c>

00:04:34.390 --> 00:04:34.400 align:start position:0%
future predicton CDF is known as shown
 

00:04:34.400 --> 00:04:35.629 align:start position:0%
future predicton CDF is known as shown
in<00:04:34.520><c> the</c>

00:04:35.629 --> 00:04:35.639 align:start position:0%
in the
 

00:04:35.639 --> 00:04:38.909 align:start position:0%
in the
picture<00:04:36.639><c> in</c><00:04:36.880><c> particular</c><00:04:37.479><c> in</c><00:04:37.680><c> the</c><00:04:37.880><c> picture</c><00:04:38.759><c> the</c>

00:04:38.909 --> 00:04:38.919 align:start position:0%
picture in particular in the picture the
 

00:04:38.919 --> 00:04:41.270 align:start position:0%
picture in particular in the picture the
transform<00:04:39.520><c> between</c><00:04:39.840><c> source</c><00:04:40.160><c> and</c><00:04:40.360><c> Target</c><00:04:41.000><c> is</c>

00:04:41.270 --> 00:04:41.280 align:start position:0%
transform between source and Target is
 

00:04:41.280 --> 00:04:44.510 align:start position:0%
transform between source and Target is
represented<00:04:41.840><c> in</c><00:04:42.320><c> Black</c><00:04:43.320><c> while</c><00:04:43.520><c> the</c><00:04:43.680><c> red</c><00:04:43.919><c> lines</c>

00:04:44.510 --> 00:04:44.520 align:start position:0%
represented in Black while the red lines
 

00:04:44.520 --> 00:04:46.150 align:start position:0%
represented in Black while the red lines
represent<00:04:45.039><c> the</c><00:04:45.199><c> application</c><00:04:45.680><c> of</c><00:04:45.919><c> this</c>

00:04:46.150 --> 00:04:46.160 align:start position:0%
represent the application of this
 

00:04:46.160 --> 00:04:48.710 align:start position:0%
represent the application of this
transfer<00:04:46.720><c> function</c><00:04:47.400><c> to</c><00:04:47.600><c> the</c><00:04:47.800><c> predicton</c><00:04:48.520><c> to</c>

00:04:48.710 --> 00:04:48.720 align:start position:0%
transfer function to the predicton to
 

00:04:48.720 --> 00:04:50.790 align:start position:0%
transfer function to the predicton to
obtain<00:04:49.080><c> the</c><00:04:49.240><c> adjusted</c>

00:04:50.790 --> 00:04:50.800 align:start position:0%
obtain the adjusted
 

00:04:50.800 --> 00:04:53.350 align:start position:0%
obtain the adjusted
output<00:04:51.800><c> this</c><00:04:52.039><c> method</c><00:04:52.479><c> is</c><00:04:52.680><c> more</c><00:04:52.960><c> accurate</c>

00:04:53.350 --> 00:04:53.360 align:start position:0%
output this method is more accurate
 

00:04:53.360 --> 00:04:55.790 align:start position:0%
output this method is more accurate
compared<00:04:53.759><c> to</c><00:04:53.919><c> the</c><00:04:54.080><c> Delta</c><00:04:54.479><c> method</c><00:04:55.320><c> because</c><00:04:55.639><c> it</c>

00:04:55.790 --> 00:04:55.800 align:start position:0%
compared to the Delta method because it
 

00:04:55.800 --> 00:04:58.150 align:start position:0%
compared to the Delta method because it
can<00:04:56.080><c> capture</c><00:04:56.560><c> nonlinear</c><00:04:57.320><c> behaviors</c><00:04:58.000><c> and</c>

00:04:58.150 --> 00:04:58.160 align:start position:0%
can capture nonlinear behaviors and
 

00:04:58.160 --> 00:05:00.230 align:start position:0%
can capture nonlinear behaviors and
climate<00:04:58.520><c> change</c><00:04:58.919><c> signals</c><00:04:59.400><c> or</c>

00:05:00.230 --> 00:05:00.240 align:start position:0%
climate change signals or
 

00:05:00.240 --> 00:05:03.150 align:start position:0%
climate change signals or
Trend<00:05:01.240><c> but</c><00:05:01.680><c> it</c><00:05:01.800><c> is</c><00:05:02.039><c> limited</c><00:05:02.840><c> by</c><00:05:03.000><c> the</c>

00:05:03.150 --> 00:05:03.160 align:start position:0%
Trend but it is limited by the
 

00:05:03.160 --> 00:05:05.950 align:start position:0%
Trend but it is limited by the
computational<00:05:03.840><c> complexity</c><00:05:04.840><c> and</c><00:05:05.199><c> the</c><00:05:05.520><c> large</c>

00:05:05.950 --> 00:05:05.960 align:start position:0%
computational complexity and the large
 

00:05:05.960 --> 00:05:08.189 align:start position:0%
computational complexity and the large
computational<00:05:06.560><c> times</c>

00:05:08.189 --> 00:05:08.199 align:start position:0%
computational times
 

00:05:08.199 --> 00:05:10.350 align:start position:0%
computational times
required<00:05:09.199><c> regarding</c><00:05:09.639><c> the</c><00:05:09.759><c> application</c><00:05:10.160><c> of</c>

00:05:10.350 --> 00:05:10.360 align:start position:0%
required regarding the application of
 

00:05:10.360 --> 00:05:13.110 align:start position:0%
required regarding the application of
best<00:05:10.639><c> adjustment</c><00:05:11.240><c> in</c><00:05:11.360><c> the</c><00:05:11.520><c> PSD</c><00:05:12.039><c> database</c><00:05:12.919><c> in</c>

00:05:13.110 --> 00:05:13.120 align:start position:0%
best adjustment in the PSD database in
 

00:05:13.120 --> 00:05:16.510 align:start position:0%
best adjustment in the PSD database in
version<00:05:13.440><c> 4.2</c><00:05:14.280><c> the</c><00:05:14.639><c> five</c><00:05:14.880><c> wind</c><00:05:15.120><c> speed</c><00:05:15.520><c> at</c><00:05:15.720><c> 10</c><00:05:15.960><c> m</c>

00:05:16.510 --> 00:05:16.520 align:start position:0%
version 4.2 the five wind speed at 10 m
 

00:05:16.520 --> 00:05:19.070 align:start position:0%
version 4.2 the five wind speed at 10 m
and<00:05:16.680><c> also</c><00:05:16.960><c> at</c><00:05:17.199><c> 100</c><00:05:17.440><c> m</c><00:05:18.000><c> are</c><00:05:18.199><c> corrected</c><00:05:18.800><c> with</c><00:05:18.919><c> the</c>

00:05:19.070 --> 00:05:19.080 align:start position:0%
and also at 100 m are corrected with the
 

00:05:19.080 --> 00:05:21.830 align:start position:0%
and also at 100 m are corrected with the
data<00:05:19.440><c> method</c><00:05:20.360><c> using</c><00:05:20.720><c> the</c><00:05:20.880><c> global</c><00:05:21.160><c> windas</c>

00:05:21.830 --> 00:05:21.840 align:start position:0%
data method using the global windas
 

00:05:21.840 --> 00:05:24.270 align:start position:0%
data method using the global windas
version<00:05:22.240><c> two</c><00:05:23.080><c> differently</c><00:05:23.680><c> to</c><00:05:23.800><c> what</c><00:05:23.960><c> is</c><00:05:24.080><c> done</c>

00:05:24.270 --> 00:05:24.280 align:start position:0%
version two differently to what is done
 

00:05:24.280 --> 00:05:25.590 align:start position:0%
version two differently to what is done
for<00:05:24.520><c> PSD</c>

00:05:25.590 --> 00:05:25.600 align:start position:0%
for PSD
 

00:05:25.600 --> 00:05:28.029 align:start position:0%
for PSD
4.1<00:05:26.600><c> the</c><00:05:26.759><c> projection</c><00:05:27.280><c> variables</c><00:05:27.840><c> are</c>

00:05:28.029 --> 00:05:28.039 align:start position:0%
4.1 the projection variables are
 

00:05:28.039 --> 00:05:30.270 align:start position:0%
4.1 the projection variables are
adjusted<00:05:28.560><c> with</c><00:05:28.720><c> the</c><00:05:28.840><c> cdfd</c><00:05:29.400><c> method</c><00:05:30.039><c> more</c>

00:05:30.270 --> 00:05:30.280 align:start position:0%
adjusted with the cdfd method more
 

00:05:30.280 --> 00:05:32.469 align:start position:0%
adjusted with the cdfd method more
suited<00:05:30.639><c> for</c><00:05:30.880><c> climate</c><00:05:31.280><c> projections</c><00:05:32.120><c> as</c><00:05:32.240><c> seen</c>

00:05:32.469 --> 00:05:32.479 align:start position:0%
suited for climate projections as seen
 

00:05:32.479 --> 00:05:34.590 align:start position:0%
suited for climate projections as seen
in<00:05:32.600><c> the</c><00:05:32.720><c> previous</c><00:05:33.039><c> slide</c><00:05:33.680><c> except</c><00:05:34.160><c> the</c><00:05:34.280><c> global</c>

00:05:34.590 --> 00:05:34.600 align:start position:0%
in the previous slide except the global
 

00:05:34.600 --> 00:05:36.749 align:start position:0%
in the previous slide except the global
horizontal<00:05:35.280><c> Radiance</c><00:05:36.120><c> where</c><00:05:36.280><c> the</c><00:05:36.400><c> climate</c>

00:05:36.749 --> 00:05:36.759 align:start position:0%
horizontal Radiance where the climate
 

00:05:36.759 --> 00:05:39.350 align:start position:0%
horizontal Radiance where the climate
trend<00:05:37.160><c> is</c><00:05:37.440><c> negligible</c><00:05:38.400><c> correct</c><00:05:38.880><c> instead</c><00:05:39.240><c> with</c>

00:05:39.350 --> 00:05:39.360 align:start position:0%
trend is negligible correct instead with
 

00:05:39.360 --> 00:05:42.430 align:start position:0%
trend is negligible correct instead with
the<00:05:39.520><c> data</c><00:05:40.039><c> method</c><00:05:41.039><c> for</c><00:05:41.240><c> all</c><00:05:41.520><c> the</c><00:05:42.199><c> these</c>

00:05:42.430 --> 00:05:42.440 align:start position:0%
the data method for all the these
 

00:05:42.440 --> 00:05:44.270 align:start position:0%
the data method for all the these
methods<00:05:43.039><c> the</c><00:05:43.160><c> corresponding</c><00:05:43.680><c> era</c><00:05:44.000><c> five</c>

00:05:44.270 --> 00:05:44.280 align:start position:0%
methods the corresponding era five
 

00:05:44.280 --> 00:05:47.430 align:start position:0%
methods the corresponding era five
variable<00:05:45.160><c> has</c><00:05:45.319><c> been</c><00:05:45.479><c> used</c><00:05:45.880><c> as</c>

00:05:47.430 --> 00:05:47.440 align:start position:0%
variable has been used as
 

00:05:47.440 --> 00:05:49.870 align:start position:0%
variable has been used as
reference<00:05:48.440><c> looking</c><00:05:48.800><c> now</c><00:05:49.000><c> in</c><00:05:49.120><c> more</c><00:05:49.319><c> detail</c><00:05:49.720><c> to</c>

00:05:49.870 --> 00:05:49.880 align:start position:0%
reference looking now in more detail to
 

00:05:49.880 --> 00:05:51.629 align:start position:0%
reference looking now in more detail to
the<00:05:50.039><c> application</c><00:05:50.520><c> of</c><00:05:50.680><c> the</c><00:05:50.840><c> Delta</c><00:05:51.160><c> method</c><00:05:51.520><c> in</c>

00:05:51.629 --> 00:05:51.639 align:start position:0%
the application of the Delta method in
 

00:05:51.639 --> 00:05:54.070 align:start position:0%
the application of the Delta method in
the<00:05:51.759><c> adjustment</c><00:05:52.240><c> of</c><00:05:52.400><c> Ira</c><00:05:52.680><c> five</c><00:05:52.919><c> wind</c><00:05:53.240><c> speed</c><00:05:53.759><c> at</c>

00:05:54.070 --> 00:05:54.080 align:start position:0%
the adjustment of Ira five wind speed at
 

00:05:54.080 --> 00:05:56.830 align:start position:0%
the adjustment of Ira five wind speed at
10<00:05:54.479><c> and</c><00:05:54.720><c> other</c><00:05:55.039><c> meters</c><00:05:55.759><c> the</c><00:05:55.919><c> first</c><00:05:56.280><c> Formula</c>

00:05:56.830 --> 00:05:56.840 align:start position:0%
10 and other meters the first Formula
 

00:05:56.840 --> 00:05:58.749 align:start position:0%
10 and other meters the first Formula
indicates<00:05:57.400><c> how</c><00:05:57.600><c> the</c><00:05:57.759><c> Delta</c><00:05:58.120><c> factors</c><00:05:58.639><c> have</c>

00:05:58.749 --> 00:05:58.759 align:start position:0%
indicates how the Delta factors have
 

00:05:58.759 --> 00:06:01.070 align:start position:0%
indicates how the Delta factors have
been<00:05:59.039><c> computed</c><00:05:59.800><c> by</c><00:05:59.960><c> taking</c><00:06:00.280><c> the</c><00:06:00.440><c> ratio</c><00:06:00.840><c> of</c><00:06:00.960><c> the</c>

00:06:01.070 --> 00:06:01.080 align:start position:0%
been computed by taking the ratio of the
 

00:06:01.080 --> 00:06:04.590 align:start position:0%
been computed by taking the ratio of the
global<00:06:01.400><c> wind</c><00:06:01.639><c> atas</c><00:06:02.440><c> and</c><00:06:02.560><c> the</c><00:06:02.680><c> ER</c><00:06:03.039><c> five</c><00:06:03.400><c> 10</c><00:06:03.639><c> year</c>

00:06:04.590 --> 00:06:04.600 align:start position:0%
global wind atas and the ER five 10 year
 

00:06:04.600 --> 00:06:07.950 align:start position:0%
global wind atas and the ER five 10 year
average<00:06:05.600><c> then</c><00:06:06.039><c> the</c><00:06:06.160><c> wind</c><00:06:06.440><c> speed</c><00:06:07.080><c> at</c><00:06:07.360><c> 10</c><00:06:07.800><c> and</c>

00:06:07.950 --> 00:06:07.960 align:start position:0%
average then the wind speed at 10 and
 

00:06:07.960 --> 00:06:10.629 align:start position:0%
average then the wind speed at 10 and
other<00:06:08.280><c> meters</c><00:06:08.840><c> at</c><00:06:09.080><c> each</c><00:06:09.319><c> time</c><00:06:09.599><c> step</c><00:06:10.240><c> have</c><00:06:10.400><c> been</c>

00:06:10.629 --> 00:06:10.639 align:start position:0%
other meters at each time step have been
 

00:06:10.639 --> 00:06:13.469 align:start position:0%
other meters at each time step have been
corrected<00:06:11.400><c> using</c><00:06:11.720><c> the</c><00:06:11.919><c> second</c>

00:06:13.469 --> 00:06:13.479 align:start position:0%
corrected using the second
 

00:06:13.479 --> 00:06:16.270 align:start position:0%
corrected using the second
equation<00:06:14.479><c> in</c><00:06:14.639><c> the</c><00:06:14.800><c> climate</c><00:06:15.199><c> projections</c><00:06:16.120><c> the</c>

00:06:16.270 --> 00:06:16.280 align:start position:0%
equation in the climate projections the
 

00:06:16.280 --> 00:06:18.390 align:start position:0%
equation in the climate projections the
Delta<00:06:16.639><c> method</c><00:06:17.120><c> has</c><00:06:17.240><c> been</c><00:06:17.440><c> used</c><00:06:17.720><c> to</c><00:06:17.880><c> adjust</c><00:06:18.240><c> the</c>

00:06:18.390 --> 00:06:18.400 align:start position:0%
Delta method has been used to adjust the
 

00:06:18.400 --> 00:06:21.430 align:start position:0%
Delta method has been used to adjust the
global<00:06:18.759><c> horizontal</c><00:06:19.639><c> Radiance</c><00:06:20.639><c> first</c><00:06:20.840><c> of</c><00:06:21.000><c> all</c>

00:06:21.430 --> 00:06:21.440 align:start position:0%
global horizontal Radiance first of all
 

00:06:21.440 --> 00:06:23.270 align:start position:0%
global horizontal Radiance first of all
the<00:06:21.560><c> averages</c><00:06:22.080><c> of</c><00:06:22.199><c> the</c><00:06:22.319><c> source</c><00:06:22.599><c> and</c><00:06:22.800><c> Target</c>

00:06:23.270 --> 00:06:23.280 align:start position:0%
the averages of the source and Target
 

00:06:23.280 --> 00:06:26.150 align:start position:0%
the averages of the source and Target
data<00:06:23.520><c> set</c><00:06:24.160><c> have</c><00:06:24.319><c> been</c><00:06:24.599><c> calculated</c><00:06:25.520><c> and</c><00:06:25.840><c> are</c>

00:06:26.150 --> 00:06:26.160 align:start position:0%
data set have been calculated and are
 

00:06:26.160 --> 00:06:29.230 align:start position:0%
data set have been calculated and are
displayed<00:06:26.720><c> in</c><00:06:26.840><c> the</c><00:06:27.000><c> top</c><00:06:27.199><c> and</c><00:06:27.319><c> middle</c><00:06:27.960><c> map</c><00:06:28.960><c> then</c>

00:06:29.230 --> 00:06:29.240 align:start position:0%
displayed in the top and middle map then
 

00:06:29.240 --> 00:06:32.430 align:start position:0%
displayed in the top and middle map then
for<00:06:29.800><c> grid</c><00:06:30.080><c> point</c><00:06:30.680><c> the</c><00:06:30.880><c> predictor</c><00:06:31.400><c> time</c><00:06:31.680><c> Series</c>

00:06:32.430 --> 00:06:32.440 align:start position:0%
for grid point the predictor time Series
 

00:06:32.440 --> 00:06:34.950 align:start position:0%
for grid point the predictor time Series
has<00:06:32.639><c> been</c><00:06:32.880><c> corrected</c><00:06:33.560><c> using</c><00:06:33.919><c> the</c><00:06:34.080><c> Delta</c>

00:06:34.950 --> 00:06:34.960 align:start position:0%
has been corrected using the Delta
 

00:06:34.960 --> 00:06:37.550 align:start position:0%
has been corrected using the Delta
factors<00:06:35.960><c> the</c><00:06:36.080><c> original</c><00:06:36.599><c> predictor</c><00:06:37.199><c> and</c><00:06:37.319><c> the</c>

00:06:37.550 --> 00:06:37.560 align:start position:0%
factors the original predictor and the
 

00:06:37.560 --> 00:06:40.029 align:start position:0%
factors the original predictor and the
corrected<00:06:38.039><c> output</c><00:06:38.520><c> PDFs</c><00:06:39.319><c> are</c><00:06:39.520><c> displayed</c><00:06:39.960><c> in</c>

00:06:40.029 --> 00:06:40.039 align:start position:0%
corrected output PDFs are displayed in
 

00:06:40.039 --> 00:06:42.430 align:start position:0%
corrected output PDFs are displayed in
the<00:06:40.199><c> bottom</c><00:06:40.639><c> panel</c><00:06:41.639><c> the</c><00:06:41.800><c> effect</c><00:06:42.080><c> of</c><00:06:42.240><c> the</c>

00:06:42.430 --> 00:06:42.440 align:start position:0%
the bottom panel the effect of the
 

00:06:42.440 --> 00:06:43.990 align:start position:0%
the bottom panel the effect of the
displacement<00:06:43.160><c> of</c><00:06:43.280><c> the</c><00:06:43.360><c> mean</c><00:06:43.599><c> of</c><00:06:43.759><c> the</c>

00:06:43.990 --> 00:06:44.000 align:start position:0%
displacement of the mean of the
 

00:06:44.000 --> 00:06:46.350 align:start position:0%
displacement of the mean of the
distribution<00:06:44.479><c> of</c><00:06:44.639><c> the</c><00:06:44.880><c> predictor</c><00:06:45.880><c> due</c><00:06:46.080><c> to</c><00:06:46.199><c> the</c>

00:06:46.350 --> 00:06:46.360 align:start position:0%
distribution of the predictor due to the
 

00:06:46.360 --> 00:06:48.790 align:start position:0%
distribution of the predictor due to the
correction<00:06:46.960><c> is</c><00:06:47.120><c> clearly</c>

00:06:48.790 --> 00:06:48.800 align:start position:0%
correction is clearly
 

00:06:48.800 --> 00:06:52.029 align:start position:0%
correction is clearly
visible<00:06:49.800><c> in</c><00:06:49.919><c> the</c><00:06:50.080><c> PSD</c><00:06:50.599><c> database</c><00:06:51.240><c> the</c><00:06:51.360><c> CDF</c>

00:06:52.029 --> 00:06:52.039 align:start position:0%
visible in the PSD database the CDF
 

00:06:52.039 --> 00:06:53.629 align:start position:0%
visible in the PSD database the CDF
method<00:06:52.560><c> has</c><00:06:52.720><c> been</c><00:06:52.919><c> applied</c><00:06:53.280><c> for</c><00:06:53.479><c> the</c>

00:06:53.629 --> 00:06:53.639 align:start position:0%
method has been applied for the
 

00:06:53.639 --> 00:06:55.629 align:start position:0%
method has been applied for the
adjustment<00:06:54.120><c> of</c><00:06:54.360><c> temperature</c><00:06:54.880><c> wind</c><00:06:55.160><c> speed</c><00:06:55.440><c> and</c>

00:06:55.629 --> 00:06:55.639 align:start position:0%
adjustment of temperature wind speed and
 

00:06:55.639 --> 00:06:58.029 align:start position:0%
adjustment of temperature wind speed and
precipitation<00:06:56.400><c> of</c><00:06:56.520><c> the</c><00:06:56.639><c> climate</c><00:06:57.080><c> projection</c>

00:06:58.029 --> 00:06:58.039 align:start position:0%
precipitation of the climate projection
 

00:06:58.039 --> 00:07:00.670 align:start position:0%
precipitation of the climate projection
models<00:06:59.039><c> the</c><00:06:59.160><c> adjust</c><00:06:59.919><c> is</c><00:07:00.120><c> performed</c>

00:07:00.670 --> 00:07:00.680 align:start position:0%
models the adjust is performed
 

00:07:00.680 --> 00:07:02.589 align:start position:0%
models the adjust is performed
separately<00:07:01.080><c> for</c><00:07:01.280><c> each</c><00:07:01.440><c> month</c><00:07:01.720><c> and</c><00:07:01.919><c> hour</c><00:07:02.360><c> to</c>

00:07:02.589 --> 00:07:02.599 align:start position:0%
separately for each month and hour to
 

00:07:02.599 --> 00:07:04.550 align:start position:0%
separately for each month and hour to
account<00:07:02.919><c> for</c><00:07:03.080><c> the</c><00:07:03.240><c> seasonality</c><00:07:03.879><c> and</c><00:07:04.039><c> the</c><00:07:04.160><c> deal</c>

00:07:04.550 --> 00:07:04.560 align:start position:0%
account for the seasonality and the deal
 

00:07:04.560 --> 00:07:05.469 align:start position:0%
account for the seasonality and the deal
cycle

00:07:05.469 --> 00:07:05.479 align:start position:0%
cycle
 

00:07:05.479 --> 00:07:07.990 align:start position:0%
cycle
variability<00:07:06.479><c> the</c><00:07:06.599><c> moving</c><00:07:06.960><c> window</c><00:07:07.319><c> approach</c>

00:07:07.990 --> 00:07:08.000 align:start position:0%
variability the moving window approach
 

00:07:08.000 --> 00:07:10.430 align:start position:0%
variability the moving window approach
which<00:07:08.280><c> scheme</c><00:07:08.599><c> is</c><00:07:08.800><c> reported</c><00:07:09.160><c> on</c><00:07:09.319><c> the</c><00:07:09.520><c> right</c>

00:07:10.430 --> 00:07:10.440 align:start position:0%
which scheme is reported on the right
 

00:07:10.440 --> 00:07:12.510 align:start position:0%
which scheme is reported on the right
addresses<00:07:11.000><c> nonstationarity</c><00:07:11.960><c> in</c><00:07:12.120><c> climate</c>

00:07:12.510 --> 00:07:12.520 align:start position:0%
addresses nonstationarity in climate
 

00:07:12.520 --> 00:07:14.230 align:start position:0%
addresses nonstationarity in climate
projections<00:07:13.280><c> where</c><00:07:13.479><c> the</c><00:07:13.599><c> assumption</c><00:07:14.039><c> that</c>

00:07:14.230 --> 00:07:14.240 align:start position:0%
projections where the assumption that
 

00:07:14.240 --> 00:07:16.510 align:start position:0%
projections where the assumption that
model<00:07:14.680><c> reference</c><00:07:15.160><c> distributions</c><00:07:16.000><c> retain</c><00:07:16.400><c> the</c>

00:07:16.510 --> 00:07:16.520 align:start position:0%
model reference distributions retain the
 

00:07:16.520 --> 00:07:19.350 align:start position:0%
model reference distributions retain the
same<00:07:16.720><c> shape</c><00:07:17.160><c> is</c><00:07:17.360><c> not</c><00:07:18.000><c> valid</c><00:07:19.000><c> this</c><00:07:19.120><c> is</c>

00:07:19.350 --> 00:07:19.360 align:start position:0%
same shape is not valid this is
 

00:07:19.360 --> 00:07:21.350 align:start position:0%
same shape is not valid this is
particularly<00:07:19.879><c> relevant</c><00:07:20.319><c> for</c><00:07:20.560><c> variables</c><00:07:21.199><c> such</c>

00:07:21.350 --> 00:07:21.360 align:start position:0%
particularly relevant for variables such
 

00:07:21.360 --> 00:07:23.550 align:start position:0%
particularly relevant for variables such
as<00:07:21.599><c> temperature</c><00:07:22.319><c> given</c><00:07:22.599><c> the</c><00:07:22.720><c> non-</c><00:07:23.000><c> negligible</c>

00:07:23.550 --> 00:07:23.560 align:start position:0%
as temperature given the non- negligible
 

00:07:23.560 --> 00:07:25.909 align:start position:0%
as temperature given the non- negligible
Trends<00:07:24.120><c> as</c><00:07:24.240><c> shown</c><00:07:24.479><c> in</c><00:07:24.599><c> the</c><00:07:24.720><c> left</c>

00:07:25.909 --> 00:07:25.919 align:start position:0%
Trends as shown in the left
 

00:07:25.919 --> 00:07:28.790 align:start position:0%
Trends as shown in the left
figures<00:07:26.919><c> the</c><00:07:27.080><c> left</c><00:07:27.560><c> figures</c><00:07:28.160><c> reported</c><00:07:28.639><c> the</c>

00:07:28.790 --> 00:07:28.800 align:start position:0%
figures the left figures reported the
 

00:07:28.800 --> 00:07:31.670 align:start position:0%
figures the left figures reported the
cdfs<00:07:29.360><c> of</c><00:07:29.520><c> of</c><00:07:29.759><c> source</c><00:07:30.280><c> Target</c><00:07:30.840><c> predict</c><00:07:31.240><c> and</c><00:07:31.440><c> and</c>

00:07:31.670 --> 00:07:31.680 align:start position:0%
cdfs of of source Target predict and and
 

00:07:31.680 --> 00:07:33.390 align:start position:0%
cdfs of of source Target predict and and
output<00:07:32.160><c> for</c><00:07:32.360><c> two</c><00:07:32.560><c> different</c><00:07:32.919><c> correction</c>

00:07:33.390 --> 00:07:33.400 align:start position:0%
output for two different correction
 

00:07:33.400 --> 00:07:35.390 align:start position:0%
output for two different correction
Windows<00:07:34.120><c> where</c><00:07:34.280><c> the</c><00:07:34.440><c> temperature</c><00:07:34.919><c> extremes</c>

00:07:35.390 --> 00:07:35.400 align:start position:0%
Windows where the temperature extremes
 

00:07:35.400 --> 00:07:37.589 align:start position:0%
Windows where the temperature extremes
values<00:07:35.720><c> are</c><00:07:35.919><c> very</c><00:07:36.160><c> different</c><00:07:37.000><c> due</c><00:07:37.240><c> to</c><00:07:37.400><c> the</c>

00:07:37.589 --> 00:07:37.599 align:start position:0%
values are very different due to the
 

00:07:37.599 --> 00:07:40.430 align:start position:0%
values are very different due to the
climate<00:07:37.879><c> change</c><00:07:38.639><c> Trend</c><00:07:39.639><c> however</c><00:07:40.199><c> this</c>

00:07:40.430 --> 00:07:40.440 align:start position:0%
climate change Trend however this
 

00:07:40.440 --> 00:07:42.150 align:start position:0%
climate change Trend however this
difference<00:07:40.960><c> is</c><00:07:41.199><c> preserved</c><00:07:41.840><c> by</c><00:07:42.039><c> The</c>

00:07:42.150 --> 00:07:42.160 align:start position:0%
difference is preserved by The
 

00:07:42.160 --> 00:07:45.749 align:start position:0%
difference is preserved by The
Adjustment<00:07:42.840><c> thanks</c><00:07:43.240><c> to</c><00:07:44.159><c> the</c><00:07:44.759><c> characteristics</c>

00:07:45.749 --> 00:07:45.759 align:start position:0%
Adjustment thanks to the characteristics
 

00:07:45.759 --> 00:07:48.309 align:start position:0%
Adjustment thanks to the characteristics
of<00:07:46.000><c> the</c><00:07:46.479><c> cdft</c>

00:07:48.309 --> 00:07:48.319 align:start position:0%
of the cdft
 

00:07:48.319 --> 00:07:51.270 align:start position:0%
of the cdft
method<00:07:49.319><c> thanks</c><00:07:49.599><c> for</c><00:07:49.800><c> the</c><00:07:50.039><c> attention</c><00:07:50.720><c> for</c><00:07:50.960><c> any</c>

00:07:51.270 --> 00:07:51.280 align:start position:0%
method thanks for the attention for any
 

00:07:51.280 --> 00:07:53.110 align:start position:0%
method thanks for the attention for any
question<00:07:51.800><c> please</c><00:07:52.159><c> don't</c><00:07:52.479><c> hesitate</c><00:07:52.960><c> to</c>

00:07:53.110 --> 00:07:53.120 align:start position:0%
question please don't hesitate to
 

00:07:53.120 --> 00:07:56.639 align:start position:0%
question please don't hesitate to
contact<00:07:53.639><c> us</c>

