WEBVTT
Kind: captions
Language: en

00:00:00.719 --> 00:00:05.550 align:start position:0%
 
how<00:00:01.240><c> often</c><00:00:02.120><c> have</c><00:00:02.399><c> you</c><00:00:02.720><c> asked</c><00:00:03.240><c> out</c><00:00:03.799><c> loud</c><00:00:04.799><c> I</c><00:00:05.040><c> wish</c>

00:00:05.550 --> 00:00:05.560 align:start position:0%
how often have you asked out loud I wish
 

00:00:05.560 --> 00:00:09.509 align:start position:0%
how often have you asked out loud I wish
I<00:00:05.799><c> had</c><00:00:06.120><c> an</c><00:00:06.359><c> AI</c><00:00:06.720><c> model</c><00:00:07.120><c> that</c><00:00:07.359><c> responded</c><00:00:08.240><c> only</c><00:00:08.719><c> in</c>

00:00:09.509 --> 00:00:09.519 align:start position:0%
I had an AI model that responded only in
 

00:00:09.519 --> 00:00:13.509 align:start position:0%
I had an AI model that responded only in
emojis<00:00:10.519><c> like</c><00:00:11.000><c> every</c><00:00:11.559><c> day</c><00:00:12.120><c> right</c><00:00:12.880><c> I</c><00:00:13.040><c> know</c><00:00:13.400><c> and</c>

00:00:13.509 --> 00:00:13.519 align:start position:0%
emojis like every day right I know and
 

00:00:13.519 --> 00:00:16.430 align:start position:0%
emojis like every day right I know and
for<00:00:13.679><c> some</c><00:00:13.960><c> reason</c><00:00:14.280><c> the</c><00:00:14.400><c> team</c><00:00:14.679><c> building</c><00:00:15.440><c> olama</c>

00:00:16.430 --> 00:00:16.440 align:start position:0%
for some reason the team building olama
 

00:00:16.440 --> 00:00:18.150 align:start position:0%
for some reason the team building olama
hasn't<00:00:16.760><c> listened</c>

00:00:18.150 --> 00:00:18.160 align:start position:0%
hasn't listened
 

00:00:18.160 --> 00:00:23.460 align:start position:0%
hasn't listened
Insanity<00:00:19.160><c> so</c><00:00:19.400><c> we're</c><00:00:19.600><c> going</c><00:00:19.800><c> to</c><00:00:19.960><c> fix</c><00:00:20.199><c> that</c>

00:00:23.460 --> 00:00:23.470 align:start position:0%
 
 

00:00:23.470 --> 00:00:28.269 align:start position:0%
 
[Music]

00:00:28.269 --> 00:00:28.279 align:start position:0%
 
 

00:00:28.279 --> 00:00:30.950 align:start position:0%
 
today<00:00:29.279><c> and</c><00:00:29.439><c> in</c><00:00:29.560><c> the</c><00:00:29.679><c> process</c><00:00:30.400><c> you'll</c><00:00:30.640><c> learn</c>

00:00:30.950 --> 00:00:30.960 align:start position:0%
today and in the process you'll learn
 

00:00:30.960 --> 00:00:33.750 align:start position:0%
today and in the process you'll learn
how<00:00:31.160><c> to</c><00:00:31.359><c> convert</c><00:00:31.920><c> and</c><00:00:32.119><c> quantize</c><00:00:32.759><c> a</c><00:00:32.920><c> model</c><00:00:33.320><c> it's</c>

00:00:33.750 --> 00:00:33.760 align:start position:0%
how to convert and quantize a model it's
 

00:00:33.760 --> 00:00:37.150 align:start position:0%
how to convert and quantize a model it's
it's<00:00:33.960><c> not</c><00:00:34.360><c> hard</c><00:00:35.360><c> but</c><00:00:35.559><c> it</c><00:00:35.920><c> also</c><00:00:36.200><c> takes</c><00:00:36.480><c> a</c><00:00:36.800><c> bit</c><00:00:36.960><c> of</c>

00:00:37.150 --> 00:00:37.160 align:start position:0%
it's not hard but it also takes a bit of
 

00:00:37.160 --> 00:00:39.670 align:start position:0%
it's not hard but it also takes a bit of
effort<00:00:37.480><c> to</c><00:00:37.640><c> get</c><00:00:37.760><c> it</c><00:00:37.960><c> going</c><00:00:38.680><c> and</c><00:00:39.280><c> maybe</c><00:00:39.520><c> some</c>

00:00:39.670 --> 00:00:39.680 align:start position:0%
effort to get it going and maybe some
 

00:00:39.680 --> 00:00:43.950 align:start position:0%
effort to get it going and maybe some
false<00:00:40.000><c> starts</c><00:00:41.000><c> and</c><00:00:41.719><c> and</c><00:00:42.000><c> it</c><00:00:42.360><c> doesn't</c><00:00:43.360><c> always</c>

00:00:43.950 --> 00:00:43.960 align:start position:0%
false starts and and it doesn't always
 

00:00:43.960 --> 00:00:46.790 align:start position:0%
false starts and and it doesn't always
work<00:00:44.879><c> so</c><00:00:45.280><c> the</c><00:00:45.520><c> first</c><00:00:45.760><c> step</c><00:00:46.120><c> is</c><00:00:46.320><c> finding</c><00:00:46.600><c> the</c>

00:00:46.790 --> 00:00:46.800 align:start position:0%
work so the first step is finding the
 

00:00:46.800 --> 00:00:49.750 align:start position:0%
work so the first step is finding the
right<00:00:47.000><c> model</c><00:00:47.800><c> let's</c><00:00:48.039><c> go</c><00:00:48.199><c> up</c><00:00:48.360><c> to</c><00:00:48.640><c> hugging</c><00:00:49.039><c> face</c>

00:00:49.750 --> 00:00:49.760 align:start position:0%
right model let's go up to hugging face
 

00:00:49.760 --> 00:00:51.750 align:start position:0%
right model let's go up to hugging face
up<00:00:50.000><c> top</c><00:00:50.399><c> I'll</c><00:00:50.559><c> search</c><00:00:50.800><c> for</c><00:00:51.000><c> emoji</c><00:00:51.480><c> and</c><00:00:51.600><c> then</c>

00:00:51.750 --> 00:00:51.760 align:start position:0%
up top I'll search for emoji and then
 

00:00:51.760 --> 00:00:53.670 align:start position:0%
up top I'll search for emoji and then
show<00:00:52.039><c> all</c><00:00:52.160><c> the</c><00:00:52.280><c> models</c><00:00:52.719><c> now</c><00:00:53.039><c> I</c><00:00:53.160><c> don't</c><00:00:53.359><c> want</c><00:00:53.480><c> to</c>

00:00:53.670 --> 00:00:53.680 align:start position:0%
show all the models now I don't want to
 

00:00:53.680 --> 00:00:56.069 align:start position:0%
show all the models now I don't want to
create<00:00:54.120><c> an</c><00:00:54.280><c> emoji</c><00:00:54.719><c> that</c><00:00:54.800><c> would</c><00:00:54.960><c> be</c><00:00:55.160><c> silly</c><00:00:56.000><c> I</c>

00:00:56.069 --> 00:00:56.079 align:start position:0%
create an emoji that would be silly I
 

00:00:56.079 --> 00:00:58.069 align:start position:0%
create an emoji that would be silly I
want<00:00:56.199><c> to</c><00:00:56.320><c> do</c><00:00:56.520><c> text</c><00:00:56.800><c> generation</c><00:00:57.359><c> but</c><00:00:57.879><c> with</c>

00:00:58.069 --> 00:00:58.079 align:start position:0%
want to do text generation but with
 

00:00:58.079 --> 00:01:00.709 align:start position:0%
want to do text generation but with
Emoji<00:00:58.800><c> so</c><00:00:59.120><c> select</c><00:00:59.519><c> text</c><00:00:59.920><c> generation</c><00:01:00.399><c> over</c><00:01:00.559><c> on</c>

00:01:00.709 --> 00:01:00.719 align:start position:0%
Emoji so select text generation over on
 

00:01:00.719 --> 00:01:03.029 align:start position:0%
Emoji so select text generation over on
the<00:01:00.879><c> side</c><00:01:01.480><c> and</c><00:01:01.640><c> the</c><00:01:01.840><c> first</c><00:01:02.160><c> result</c><00:01:02.519><c> is</c><00:01:02.719><c> open</c>

00:01:03.029 --> 00:01:03.039 align:start position:0%
the side and the first result is open
 

00:01:03.039 --> 00:01:06.990 align:start position:0%
the side and the first result is open
Hermes<00:01:03.719><c> Emoji</c><00:01:04.199><c> Tron</c><00:01:04.840><c> 001</c><00:01:05.840><c> perfect</c><00:01:06.640><c> scroll</c>

00:01:06.990 --> 00:01:07.000 align:start position:0%
Hermes Emoji Tron 001 perfect scroll
 

00:01:07.000 --> 00:01:09.070 align:start position:0%
Hermes Emoji Tron 001 perfect scroll
down<00:01:07.240><c> and</c><00:01:07.400><c> this</c><00:01:07.479><c> seems</c><00:01:07.680><c> to</c><00:01:07.799><c> be</c><00:01:08.200><c> exactly</c><00:01:08.960><c> what</c>

00:01:09.070 --> 00:01:09.080 align:start position:0%
down and this seems to be exactly what
 

00:01:09.080 --> 00:01:13.270 align:start position:0%
down and this seems to be exactly what
we<00:01:09.240><c> want</c><00:01:10.080><c> exactly</c><00:01:10.600><c> what</c><00:01:11.119><c> everyone</c><00:01:11.960><c> needs</c><00:01:12.960><c> now</c>

00:01:13.270 --> 00:01:13.280 align:start position:0%
we want exactly what everyone needs now
 

00:01:13.280 --> 00:01:15.030 align:start position:0%
we want exactly what everyone needs now
go<00:01:13.439><c> to</c><00:01:13.600><c> the</c><00:01:13.759><c> files</c><00:01:14.119><c> tab</c><00:01:14.360><c> and</c><00:01:14.479><c> click</c><00:01:14.680><c> on</c><00:01:14.840><c> the</c>

00:01:15.030 --> 00:01:15.040 align:start position:0%
go to the files tab and click on the
 

00:01:15.040 --> 00:01:18.190 align:start position:0%
go to the files tab and click on the
config.js<00:01:15.880><c> file</c><00:01:16.799><c> notice</c><00:01:17.119><c> that</c><00:01:17.360><c> architecture</c>

00:01:18.190 --> 00:01:18.200 align:start position:0%
config.js file notice that architecture
 

00:01:18.200 --> 00:01:21.590 align:start position:0%
config.js file notice that architecture
is<00:01:18.439><c> mistol</c><00:01:19.040><c> for</c><00:01:19.280><c> coo</c><00:01:19.799><c> LM</c><00:01:20.680><c> that's</c><00:01:20.920><c> good</c><00:01:21.439><c> that's</c>

00:01:21.590 --> 00:01:21.600 align:start position:0%
is mistol for coo LM that's good that's
 

00:01:21.600 --> 00:01:23.670 align:start position:0%
is mistol for coo LM that's good that's
one<00:01:21.720><c> of</c><00:01:21.840><c> the</c><00:01:21.920><c> architectures</c><00:01:22.600><c> supported</c><00:01:23.600><c> we</c>

00:01:23.670 --> 00:01:23.680 align:start position:0%
one of the architectures supported we
 

00:01:23.680 --> 00:01:25.830 align:start position:0%
one of the architectures supported we
should<00:01:23.960><c> download</c><00:01:24.320><c> the</c><00:01:24.439><c> model</c><00:01:24.799><c> now</c><00:01:25.240><c> but</c><00:01:25.640><c> I've</c>

00:01:25.830 --> 00:01:25.840 align:start position:0%
should download the model now but I've
 

00:01:25.840 --> 00:01:27.670 align:start position:0%
should download the model now but I've
always<00:01:26.079><c> had</c><00:01:26.240><c> trouble</c><00:01:26.759><c> just</c><00:01:27.000><c> downloading</c><00:01:27.520><c> the</c>

00:01:27.670 --> 00:01:27.680 align:start position:0%
always had trouble just downloading the
 

00:01:27.680 --> 00:01:30.550 align:start position:0%
always had trouble just downloading the
files<00:01:28.079><c> the</c><00:01:28.240><c> normal</c><00:01:28.560><c> way</c><00:01:28.759><c> with</c><00:01:29.000><c> Git</c><00:01:29.920><c> it's</c><00:01:30.159><c> slow</c>

00:01:30.550 --> 00:01:30.560 align:start position:0%
files the normal way with Git it's slow
 

00:01:30.560 --> 00:01:33.149 align:start position:0%
files the normal way with Git it's slow
it<00:01:30.680><c> seems</c><00:01:31.079><c> on</c><00:01:31.280><c> my</c><00:01:31.479><c> machine</c><00:01:32.200><c> so</c><00:01:32.439><c> I</c><00:01:32.600><c> really</c><00:01:32.880><c> like</c>

00:01:33.149 --> 00:01:33.159 align:start position:0%
it seems on my machine so I really like
 

00:01:33.159 --> 00:01:37.310 align:start position:0%
it seems on my machine so I really like
hugging<00:01:33.600><c> face</c><00:01:33.840><c> downloader</c><00:01:34.479><c> by</c><00:01:35.360><c> Boda</c><00:01:36.200><c> a</c><00:01:37.200><c> you</c>

00:01:37.310 --> 00:01:37.320 align:start position:0%
hugging face downloader by Boda a you
 

00:01:37.320 --> 00:01:39.389 align:start position:0%
hugging face downloader by Boda a you
can<00:01:37.479><c> grab</c><00:01:37.720><c> the</c><00:01:37.880><c> executable</c><00:01:38.479><c> for</c><00:01:38.640><c> the</c><00:01:38.799><c> releases</c>

00:01:39.389 --> 00:01:39.399 align:start position:0%
can grab the executable for the releases
 

00:01:39.399 --> 00:01:41.149 align:start position:0%
can grab the executable for the releases
and<00:01:39.600><c> get</c><00:01:39.720><c> it</c><00:01:39.880><c> installed</c><00:01:40.560><c> there's</c><00:01:40.799><c> also</c><00:01:41.040><c> a</c>

00:01:41.149 --> 00:01:41.159 align:start position:0%
and get it installed there's also a
 

00:01:41.159 --> 00:01:42.830 align:start position:0%
and get it installed there's also a
shell<00:01:41.479><c> script</c><00:01:41.759><c> to</c><00:01:41.880><c> install</c><00:01:42.360><c> as</c><00:01:42.479><c> well</c><00:01:42.680><c> if</c><00:01:42.759><c> you</c>

00:01:42.830 --> 00:01:42.840 align:start position:0%
shell script to install as well if you
 

00:01:42.840 --> 00:01:44.870 align:start position:0%
shell script to install as well if you
want<00:01:42.960><c> to</c><00:01:43.119><c> do</c><00:01:43.280><c> that</c><00:01:43.920><c> I</c><00:01:44.000><c> have</c><00:01:44.079><c> a</c><00:01:44.200><c> folder</c><00:01:44.560><c> off</c><00:01:44.759><c> my</c>

00:01:44.870 --> 00:01:44.880 align:start position:0%
want to do that I have a folder off my
 

00:01:44.880 --> 00:01:47.310 align:start position:0%
want to do that I have a folder off my
users<00:01:45.280><c> rout</c><00:01:45.640><c> called</c><00:01:45.960><c> bin</c><00:01:46.280><c> so</c><00:01:46.600><c> I</c><00:01:46.719><c> just</c><00:01:46.920><c> throw</c><00:01:47.159><c> it</c>

00:01:47.310 --> 00:01:47.320 align:start position:0%
users rout called bin so I just throw it
 

00:01:47.320 --> 00:01:50.270 align:start position:0%
users rout called bin so I just throw it
in<00:01:47.520><c> there</c><00:01:48.479><c> okay</c><00:01:48.680><c> so</c><00:01:49.000><c> go</c><00:01:49.200><c> to</c><00:01:49.399><c> the</c><00:01:49.680><c> repo</c><00:01:50.119><c> on</c>

00:01:50.270 --> 00:01:50.280 align:start position:0%
in there okay so go to the repo on
 

00:01:50.280 --> 00:01:52.550 align:start position:0%
in there okay so go to the repo on
hugging<00:01:50.640><c> face</c><00:01:50.920><c> and</c><00:01:51.079><c> click</c><00:01:51.320><c> the</c><00:01:51.520><c> copy</c><00:01:51.880><c> icon</c>

00:01:52.550 --> 00:01:52.560 align:start position:0%
hugging face and click the copy icon
 

00:01:52.560 --> 00:01:54.830 align:start position:0%
hugging face and click the copy icon
next<00:01:52.719><c> to</c><00:01:52.880><c> the</c><00:01:52.960><c> repo</c><00:01:53.360><c> name</c><00:01:54.159><c> and</c><00:01:54.280><c> now</c><00:01:54.479><c> go</c><00:01:54.600><c> to</c>

00:01:54.830 --> 00:01:54.840 align:start position:0%
next to the repo name and now go to
 

00:01:54.840 --> 00:01:56.230 align:start position:0%
next to the repo name and now go to
wherever<00:01:55.119><c> you</c><00:01:55.240><c> want</c><00:01:55.439><c> your</c><00:01:55.600><c> models</c><00:01:56.000><c> to</c><00:01:56.119><c> be</c>

00:01:56.230 --> 00:01:56.240 align:start position:0%
wherever you want your models to be
 

00:01:56.240 --> 00:01:59.310 align:start position:0%
wherever you want your models to be
downloaded<00:01:56.719><c> to</c><00:01:57.039><c> and</c><00:01:57.159><c> run</c><00:01:57.479><c> HF</c><00:01:57.960><c> downloader</c><00:01:58.640><c> DS</c>

00:01:59.310 --> 00:01:59.320 align:start position:0%
downloaded to and run HF downloader DS
 

00:01:59.320 --> 00:02:03.469 align:start position:0%
downloaded to and run HF downloader DS
do<00:02:00.039><c> -</c><00:02:00.439><c> M</c><00:02:01.119><c> and</c><00:02:01.320><c> paste</c><00:02:01.520><c> the</c><00:02:01.640><c> repo</c><00:02:02.159><c> name</c><00:02:03.159><c> press</c>

00:02:03.469 --> 00:02:03.479 align:start position:0%
do - M and paste the repo name press
 

00:02:03.479 --> 00:02:06.749 align:start position:0%
do - M and paste the repo name press
enter<00:02:04.479><c> those</c><00:02:04.719><c> files</c><00:02:05.119><c> are</c><00:02:05.320><c> downloading</c><00:02:06.039><c> so</c><00:02:06.520><c> now</c>

00:02:06.749 --> 00:02:06.759 align:start position:0%
enter those files are downloading so now
 

00:02:06.759 --> 00:02:09.070 align:start position:0%
enter those files are downloading so now
let's<00:02:07.000><c> get</c><00:02:07.119><c> the</c><00:02:07.280><c> conversion</c><00:02:07.840><c> process</c><00:02:08.200><c> working</c>

00:02:09.070 --> 00:02:09.080 align:start position:0%
let's get the conversion process working
 

00:02:09.080 --> 00:02:10.790 align:start position:0%
let's get the conversion process working
make<00:02:09.200><c> sure</c><00:02:09.399><c> docker's</c><00:02:09.920><c> installed</c><00:02:10.360><c> and</c><00:02:10.520><c> running</c>

00:02:10.790 --> 00:02:10.800 align:start position:0%
make sure docker's installed and running
 

00:02:10.800 --> 00:02:12.710 align:start position:0%
make sure docker's installed and running
on<00:02:10.959><c> your</c><00:02:11.160><c> system</c><00:02:11.800><c> and</c><00:02:11.959><c> then</c><00:02:12.200><c> in</c><00:02:12.319><c> a</c><00:02:12.480><c> different</c>

00:02:12.710 --> 00:02:12.720 align:start position:0%
on your system and then in a different
 

00:02:12.720 --> 00:02:17.430 align:start position:0%
on your system and then in a different
terminal<00:02:13.160><c> run</c><00:02:13.520><c> Docker</c><00:02:14.040><c> pull</c><00:02:15.080><c> ol/</c><00:02:16.440><c> quantise</c>

00:02:17.430 --> 00:02:17.440 align:start position:0%
terminal run Docker pull ol/ quantise
 

00:02:17.440 --> 00:02:18.790 align:start position:0%
terminal run Docker pull ol/ quantise
how's<00:02:17.640><c> the</c><00:02:17.720><c> hugg</c><00:02:17.920><c> andface</c><00:02:18.239><c> downloader</c>

00:02:18.790 --> 00:02:18.800 align:start position:0%
how's the hugg andface downloader
 

00:02:18.800 --> 00:02:20.949 align:start position:0%
how's the hugg andface downloader
process<00:02:19.160><c> going</c><00:02:19.959><c> we</c><00:02:20.080><c> need</c><00:02:20.280><c> to</c><00:02:20.440><c> wait</c><00:02:20.640><c> for</c><00:02:20.800><c> the</c>

00:02:20.949 --> 00:02:20.959 align:start position:0%
process going we need to wait for the
 

00:02:20.959 --> 00:02:23.910 align:start position:0%
process going we need to wait for the
model<00:02:21.280><c> to</c><00:02:21.519><c> download</c><00:02:22.000><c> so</c><00:02:22.440><c> I'll</c><00:02:22.720><c> speed</c><00:02:23.120><c> on</c><00:02:23.640><c> to</c>

00:02:23.910 --> 00:02:23.920 align:start position:0%
model to download so I'll speed on to
 

00:02:23.920 --> 00:02:26.509 align:start position:0%
model to download so I'll speed on to
that<00:02:24.080><c> being</c><00:02:24.360><c> done</c><00:02:25.280><c> okay</c><00:02:25.599><c> here</c><00:02:25.720><c> we</c><00:02:25.840><c> are</c><00:02:26.160><c> back</c>

00:02:26.509 --> 00:02:26.519 align:start position:0%
that being done okay here we are back
 

00:02:26.519 --> 00:02:29.309 align:start position:0%
that being done okay here we are back
again<00:02:27.519><c> here</c><00:02:27.640><c> we</c><00:02:27.760><c> are</c><00:02:27.879><c> in</c><00:02:28.040><c> the</c><00:02:28.239><c> folder</c><00:02:28.840><c> we</c><00:02:29.000><c> ran</c>

00:02:29.309 --> 00:02:29.319 align:start position:0%
again here we are in the folder we ran
 

00:02:29.319 --> 00:02:31.750 align:start position:0%
again here we are in the folder we ran
that<00:02:29.440><c> command</c><00:02:30.000><c> in</c><00:02:30.640><c> move</c><00:02:30.920><c> into</c><00:02:31.160><c> the</c><00:02:31.280><c> directory</c>

00:02:31.750 --> 00:02:31.760 align:start position:0%
that command in move into the directory
 

00:02:31.760 --> 00:02:33.790 align:start position:0%
that command in move into the directory
created<00:02:32.160><c> for</c><00:02:32.319><c> the</c><00:02:32.440><c> role</c><00:02:32.760><c> model</c><00:02:33.400><c> we</c><00:02:33.519><c> need</c><00:02:33.680><c> to</c>

00:02:33.790 --> 00:02:33.800 align:start position:0%
created for the role model we need to
 

00:02:33.800 --> 00:02:38.949 align:start position:0%
created for the role model we need to
run<00:02:34.080><c> Docker</c><00:02:34.720><c> run--</c><00:02:36.040><c> rm-</c><00:02:37.319><c> v.</c>

00:02:38.949 --> 00:02:38.959 align:start position:0%
run Docker run-- rm- v.
 

00:02:38.959 --> 00:02:42.630 align:start position:0%
run Docker run-- rm- v.
colmodel<00:02:39.959><c> ol/</c><00:02:41.040><c> quantise</c><00:02:42.040><c> this</c><00:02:42.159><c> is</c><00:02:42.319><c> not</c><00:02:42.480><c> going</c>

00:02:42.630 --> 00:02:42.640 align:start position:0%
colmodel ol/ quantise this is not going
 

00:02:42.640 --> 00:02:44.790 align:start position:0%
colmodel ol/ quantise this is not going
to<00:02:42.800><c> work</c><00:02:43.120><c> but</c><00:02:43.319><c> it</c><00:02:43.440><c> shows</c><00:02:43.760><c> us</c><00:02:44.000><c> something</c><00:02:44.560><c> we</c>

00:02:44.790 --> 00:02:44.800 align:start position:0%
to work but it shows us something we
 

00:02:44.800 --> 00:02:47.350 align:start position:0%
to work but it shows us something we
need<00:02:45.800><c> Docker</c><00:02:46.120><c> run</c><00:02:46.360><c> will</c><00:02:46.519><c> run</c><00:02:46.680><c> an</c><00:02:46.920><c> image</c><00:02:47.200><c> to</c>

00:02:47.350 --> 00:02:47.360 align:start position:0%
need Docker run will run an image to
 

00:02:47.360 --> 00:02:50.190 align:start position:0%
need Docker run will run an image to
create<00:02:47.680><c> a</c><00:02:47.800><c> new</c><00:02:48.000><c> container</c><00:02:48.480><c> on</c><00:02:48.599><c> your</c><00:02:49.200><c> system--</c>

00:02:50.190 --> 00:02:50.200 align:start position:0%
create a new container on your system--
 

00:02:50.200 --> 00:02:52.589 align:start position:0%
create a new container on your system--
RM<00:02:51.000><c> removes</c><00:02:51.480><c> the</c><00:02:51.640><c> container</c><00:02:52.159><c> once</c><00:02:52.360><c> the</c>

00:02:52.589 --> 00:02:52.599 align:start position:0%
RM removes the container once the
 

00:02:52.599 --> 00:02:57.190 align:start position:0%
RM removes the container once the
process<00:02:53.000><c> is</c><00:02:53.560><c> complete-</c><00:02:54.560><c> v.</c><00:02:55.879><c> colmodel</c><00:02:56.879><c> tells</c>

00:02:57.190 --> 00:02:57.200 align:start position:0%
process is complete- v. colmodel tells
 

00:02:57.200 --> 00:03:00.229 align:start position:0%
process is complete- v. colmodel tells
Docker<00:02:57.560><c> to</c><00:02:57.760><c> mount</c><00:02:58.120><c> a</c><00:02:58.400><c> volume</c><00:02:59.400><c> take</c><00:02:59.840><c> whatever</c>

00:03:00.229 --> 00:03:00.239 align:start position:0%
Docker to mount a volume take whatever
 

00:03:00.239 --> 00:03:02.149 align:start position:0%
Docker to mount a volume take whatever
is<00:03:00.360><c> in</c><00:03:00.480><c> the</c><00:03:00.599><c> current</c><00:03:00.879><c> directory</c><00:03:01.640><c> represented</c>

00:03:02.149 --> 00:03:02.159 align:start position:0%
is in the current directory represented
 

00:03:02.159 --> 00:03:05.070 align:start position:0%
is in the current directory represented
by<00:03:02.280><c> the</c><00:03:02.440><c> dot</c><00:03:03.120><c> and</c><00:03:03.360><c> mount</c><00:03:03.680><c> it</c><00:03:04.000><c> in</c><00:03:04.120><c> the</c><00:03:04.280><c> container</c>

00:03:05.070 --> 00:03:05.080 align:start position:0%
by the dot and mount it in the container
 

00:03:05.080 --> 00:03:09.110 align:start position:0%
by the dot and mount it in the container
as/<00:03:05.760><c> model</c><00:03:06.720><c> AMA</c><00:03:07.159><c> /</c><00:03:07.560><c> quantise</c><00:03:08.440><c> is</c><00:03:08.599><c> the</c><00:03:08.720><c> image</c><00:03:09.000><c> we</c>

00:03:09.110 --> 00:03:09.120 align:start position:0%
as/ model AMA / quantise is the image we
 

00:03:09.120 --> 00:03:11.750 align:start position:0%
as/ model AMA / quantise is the image we
want<00:03:09.280><c> to</c><00:03:09.560><c> run</c><00:03:10.560><c> and</c><00:03:10.680><c> then</c><00:03:10.840><c> the</c><00:03:11.000><c> command</c><00:03:11.480><c> that</c><00:03:11.599><c> is</c>

00:03:11.750 --> 00:03:11.760 align:start position:0%
want to run and then the command that is
 

00:03:11.760 --> 00:03:13.710 align:start position:0%
want to run and then the command that is
being<00:03:11.959><c> run</c><00:03:12.239><c> inside</c><00:03:12.599><c> the</c><00:03:12.760><c> container</c><00:03:13.239><c> takes</c><00:03:13.519><c> two</c>

00:03:13.710 --> 00:03:13.720 align:start position:0%
being run inside the container takes two
 

00:03:13.720 --> 00:03:16.190 align:start position:0%
being run inside the container takes two
parameters<00:03:14.400><c> first-</c><00:03:15.040><c> Q</c><00:03:15.319><c> specifies</c><00:03:15.799><c> the</c>

00:03:16.190 --> 00:03:16.200 align:start position:0%
parameters first- Q specifies the
 

00:03:16.200 --> 00:03:18.430 align:start position:0%
parameters first- Q specifies the
quantization<00:03:17.200><c> and</c><00:03:17.480><c> that's</c><00:03:17.840><c> why</c><00:03:17.959><c> I</c><00:03:18.080><c> wanted</c><00:03:18.360><c> the</c>

00:03:18.430 --> 00:03:18.440 align:start position:0%
quantization and that's why I wanted the
 

00:03:18.440 --> 00:03:20.390 align:start position:0%
quantization and that's why I wanted the
command<00:03:18.760><c> to</c><00:03:18.879><c> fail</c><00:03:19.560><c> we</c><00:03:19.680><c> need</c><00:03:19.840><c> to</c><00:03:19.959><c> know</c><00:03:20.239><c> which</c>

00:03:20.390 --> 00:03:20.400 align:start position:0%
command to fail we need to know which
 

00:03:20.400 --> 00:03:23.550 align:start position:0%
command to fail we need to know which
quantization<00:03:21.080><c> to</c><00:03:21.239><c> use</c><00:03:22.040><c> the</c><00:03:22.319><c> fastest</c><00:03:22.720><c> to</c><00:03:22.920><c> run</c>

00:03:23.550 --> 00:03:23.560 align:start position:0%
quantization to use the fastest to run
 

00:03:23.560 --> 00:03:25.710 align:start position:0%
quantization to use the fastest to run
and<00:03:23.720><c> the</c><00:03:23.920><c> best</c><00:03:24.239><c> compromise</c><00:03:25.000><c> in</c><00:03:25.239><c> performance</c>

00:03:25.710 --> 00:03:25.720 align:start position:0%
and the best compromise in performance
 

00:03:25.720 --> 00:03:28.149 align:start position:0%
and the best compromise in performance
and<00:03:25.920><c> quality</c><00:03:26.840><c> is</c><00:03:27.000><c> usually</c>

00:03:28.149 --> 00:03:28.159 align:start position:0%
and quality is usually
 

00:03:28.159 --> 00:03:31.830 align:start position:0%
and quality is usually
q40<00:03:29.159><c> so</c><00:03:29.360><c> run</c><00:03:29.879><c> that</c><00:03:30.080><c> Docker</c><00:03:30.480><c> run</c><00:03:30.720><c> command</c><00:03:31.200><c> again</c>

00:03:31.830 --> 00:03:31.840 align:start position:0%
q40 so run that Docker run command again
 

00:03:31.840 --> 00:03:34.190 align:start position:0%
q40 so run that Docker run command again
adding<00:03:32.200><c> -</c><00:03:32.560><c> Q</c>

00:03:34.190 --> 00:03:34.200 align:start position:0%
adding - Q
 

00:03:34.200 --> 00:03:37.869 align:start position:0%
adding - Q
q40<00:03:35.200><c> SL</c><00:03:35.680><c> model</c><00:03:36.239><c> to</c><00:03:36.519><c> the</c><00:03:36.640><c> end</c><00:03:37.360><c> I'll</c><00:03:37.519><c> try</c><00:03:37.720><c> to</c>

00:03:37.869 --> 00:03:37.879 align:start position:0%
q40 SL model to the end I'll try to
 

00:03:37.879 --> 00:03:39.789 align:start position:0%
q40 SL model to the end I'll try to
remember<00:03:38.200><c> to</c><00:03:38.360><c> put</c><00:03:38.519><c> the</c><00:03:38.680><c> full</c><00:03:39.040><c> command</c><00:03:39.480><c> down</c><00:03:39.640><c> in</c>

00:03:39.789 --> 00:03:39.799 align:start position:0%
remember to put the full command down in
 

00:03:39.799 --> 00:03:42.350 align:start position:0%
remember to put the full command down in
the<00:03:39.959><c> description</c><00:03:40.599><c> but</c><00:03:41.360><c> I'm</c><00:03:41.560><c> sure</c><00:03:41.879><c> someone's</c>

00:03:42.350 --> 00:03:42.360 align:start position:0%
the description but I'm sure someone's
 

00:03:42.360 --> 00:03:44.949 align:start position:0%
the description but I'm sure someone's
going<00:03:42.480><c> to</c><00:03:42.599><c> call</c><00:03:42.799><c> me</c><00:03:42.959><c> out</c><00:03:43.200><c> on</c><00:03:43.640><c> forgetting</c><00:03:44.640><c> if</c>

00:03:44.949 --> 00:03:44.959 align:start position:0%
going to call me out on forgetting if
 

00:03:44.959 --> 00:03:46.670 align:start position:0%
going to call me out on forgetting if
everything<00:03:45.360><c> works</c><00:03:46.000><c> and</c><00:03:46.159><c> the</c><00:03:46.280><c> model</c><00:03:46.560><c> is</c>

00:03:46.670 --> 00:03:46.680 align:start position:0%
everything works and the model is
 

00:03:46.680 --> 00:03:48.910 align:start position:0%
everything works and the model is
configured<00:03:47.159><c> correctly</c><00:03:47.879><c> then</c><00:03:48.080><c> we</c><00:03:48.239><c> should</c><00:03:48.519><c> see</c>

00:03:48.910 --> 00:03:48.920 align:start position:0%
configured correctly then we should see
 

00:03:48.920 --> 00:03:51.270 align:start position:0%
configured correctly then we should see
a<00:03:49.200><c> lot</c><00:03:49.400><c> of</c><00:03:49.640><c> activity</c><00:03:50.560><c> the</c><00:03:50.680><c> model</c><00:03:50.959><c> is</c><00:03:51.080><c> first</c>

00:03:51.270 --> 00:03:51.280 align:start position:0%
a lot of activity the model is first
 

00:03:51.280 --> 00:03:54.190 align:start position:0%
a lot of activity the model is first
being<00:03:51.599><c> converted</c><00:03:52.239><c> to</c><00:03:52.640><c> gdf</c><00:03:53.280><c> format</c><00:03:53.920><c> and</c><00:03:54.040><c> then</c>

00:03:54.190 --> 00:03:54.200 align:start position:0%
being converted to gdf format and then
 

00:03:54.200 --> 00:03:56.429 align:start position:0%
being converted to gdf format and then
it's<00:03:54.360><c> being</c><00:03:54.640><c> quantized</c><00:03:55.239><c> to</c><00:03:55.360><c> 4</c><00:03:55.640><c> bit</c>

00:03:56.429 --> 00:03:56.439 align:start position:0%
it's being quantized to 4 bit
 

00:03:56.439 --> 00:03:58.750 align:start position:0%
it's being quantized to 4 bit
quantization<00:03:57.360><c> simply</c><00:03:57.640><c> means</c><00:03:57.959><c> at</c><00:03:58.159><c> the</c><00:03:58.280><c> 7</c>

00:03:58.750 --> 00:03:58.760 align:start position:0%
quantization simply means at the 7
 

00:03:58.760 --> 00:04:01.350 align:start position:0%
quantization simply means at the 7
billi3<00:03:59.640><c> 2</c><00:03:59.840><c> bit</c><00:04:00.000><c> floating</c><00:04:00.400><c> Point</c><00:04:00.640><c> numbers</c><00:04:01.200><c> are</c>

00:04:01.350 --> 00:04:01.360 align:start position:0%
billi3 2 bit floating Point numbers are
 

00:04:01.360 --> 00:04:04.069 align:start position:0%
billi3 2 bit floating Point numbers are
being<00:04:01.640><c> quantized</c><00:04:02.200><c> to</c><00:04:02.360><c> 4bit</c><00:04:02.879><c> integers</c><00:04:03.840><c> to</c>

00:04:04.069 --> 00:04:04.079 align:start position:0%
being quantized to 4bit integers to
 

00:04:04.079 --> 00:04:06.509 align:start position:0%
being quantized to 4bit integers to
visualize<00:04:04.640><c> this</c><00:04:04.959><c> I</c><00:04:05.079><c> need</c><00:04:05.280><c> some</c><00:04:05.519><c> cool</c><00:04:05.920><c> graphics</c>

00:04:06.509 --> 00:04:06.519 align:start position:0%
visualize this I need some cool graphics
 

00:04:06.519 --> 00:04:08.270 align:start position:0%
visualize this I need some cool graphics
and<00:04:06.680><c> I'm</c><00:04:06.799><c> working</c><00:04:07.079><c> on</c><00:04:07.280><c> that</c><00:04:07.439><c> before</c><00:04:07.840><c> making</c><00:04:08.079><c> a</c>

00:04:08.270 --> 00:04:08.280 align:start position:0%
and I'm working on that before making a
 

00:04:08.280 --> 00:04:09.630 align:start position:0%
and I'm working on that before making a
video<00:04:08.799><c> about</c>

00:04:09.630 --> 00:04:09.640 align:start position:0%
video about
 

00:04:09.640 --> 00:04:11.990 align:start position:0%
video about
quantization<00:04:10.640><c> there</c><00:04:10.760><c> are</c><00:04:10.959><c> now</c><00:04:11.200><c> two</c><00:04:11.439><c> new</c><00:04:11.680><c> files</c>

00:04:11.990 --> 00:04:12.000 align:start position:0%
quantization there are now two new files
 

00:04:12.000 --> 00:04:14.789 align:start position:0%
quantization there are now two new files
in<00:04:12.159><c> here</c><00:04:12.519><c> F16</c><00:04:13.239><c> dobin</c><00:04:13.920><c> which</c><00:04:14.040><c> is</c><00:04:14.159><c> the</c><00:04:14.360><c> converted</c>

00:04:14.789 --> 00:04:14.799 align:start position:0%
in here F16 dobin which is the converted
 

00:04:14.799 --> 00:04:18.870 align:start position:0%
in here F16 dobin which is the converted
model<00:04:15.519><c> and</c><00:04:16.160><c> q40</c><00:04:17.160><c> dobin</c><00:04:17.799><c> which</c><00:04:17.919><c> is</c><00:04:18.000><c> the</c><00:04:18.160><c> 4-bit</c>

00:04:18.870 --> 00:04:18.880 align:start position:0%
model and q40 dobin which is the 4-bit
 

00:04:18.880 --> 00:04:21.909 align:start position:0%
model and q40 dobin which is the 4-bit
quantise<00:04:19.400><c> ggf</c><00:04:20.000><c> model</c><00:04:20.320><c> file</c><00:04:21.199><c> this</c><00:04:21.359><c> second</c><00:04:21.680><c> file</c>

00:04:21.909 --> 00:04:21.919 align:start position:0%
quantise ggf model file this second file
 

00:04:21.919 --> 00:04:23.350 align:start position:0%
quantise ggf model file this second file
is<00:04:22.079><c> often</c><00:04:22.320><c> referred</c><00:04:22.680><c> to</c><00:04:22.840><c> as</c><00:04:22.919><c> the</c><00:04:23.080><c> model</c>

00:04:23.350 --> 00:04:23.360 align:start position:0%
is often referred to as the model
 

00:04:23.360 --> 00:04:25.550 align:start position:0%
is often referred to as the model
weights<00:04:24.199><c> but</c><00:04:24.320><c> it's</c><00:04:24.560><c> kind</c><00:04:24.720><c> of</c><00:04:24.919><c> useless</c><00:04:25.360><c> without</c>

00:04:25.550 --> 00:04:25.560 align:start position:0%
weights but it's kind of useless without
 

00:04:25.560 --> 00:04:27.790 align:start position:0%
weights but it's kind of useless without
the<00:04:25.800><c> template</c><00:04:26.720><c> possibly</c><00:04:27.040><c> a</c><00:04:27.160><c> system</c><00:04:27.440><c> prompt</c>

00:04:27.790 --> 00:04:27.800 align:start position:0%
the template possibly a system prompt
 

00:04:27.800 --> 00:04:30.310 align:start position:0%
the template possibly a system prompt
and<00:04:27.960><c> maybe</c><00:04:28.240><c> some</c><00:04:28.600><c> parameters</c><00:04:29.680><c> we</c><00:04:29.800><c> can</c><00:04:29.919><c> make</c><00:04:30.080><c> a</c>

00:04:30.310 --> 00:04:30.320 align:start position:0%
and maybe some parameters we can make a
 

00:04:30.320 --> 00:04:32.110 align:start position:0%
and maybe some parameters we can make a
guess<00:04:30.560><c> about</c><00:04:30.759><c> the</c><00:04:30.919><c> template</c><00:04:31.320><c> but</c><00:04:31.440><c> it's</c><00:04:31.680><c> best</c>

00:04:32.110 --> 00:04:32.120 align:start position:0%
guess about the template but it's best
 

00:04:32.120 --> 00:04:34.830 align:start position:0%
guess about the template but it's best
if<00:04:32.240><c> we</c><00:04:32.400><c> look</c><00:04:32.720><c> in</c><00:04:32.960><c> the</c><00:04:33.120><c> hugging</c><00:04:33.520><c> face</c><00:04:33.720><c> read</c><00:04:33.960><c> me</c>

00:04:34.830 --> 00:04:34.840 align:start position:0%
if we look in the hugging face read me
 

00:04:34.840 --> 00:04:36.909 align:start position:0%
if we look in the hugging face read me
there<00:04:35.039><c> isn't</c><00:04:35.440><c> a</c><00:04:35.720><c> standard</c><00:04:36.160><c> way</c><00:04:36.320><c> of</c><00:04:36.479><c> showing</c>

00:04:36.909 --> 00:04:36.919 align:start position:0%
there isn't a standard way of showing
 

00:04:36.919 --> 00:04:39.189 align:start position:0%
there isn't a standard way of showing
this<00:04:37.360><c> so</c><00:04:37.720><c> there</c><00:04:37.960><c> may</c><00:04:38.160><c> be</c><00:04:38.360><c> some</c><00:04:38.520><c> sleuthing</c><00:04:39.120><c> that</c>

00:04:39.189 --> 00:04:39.199 align:start position:0%
this so there may be some sleuthing that
 

00:04:39.199 --> 00:04:41.909 align:start position:0%
this so there may be some sleuthing that
we<00:04:39.320><c> need</c><00:04:39.479><c> to</c><00:04:39.639><c> do</c><00:04:40.560><c> down</c><00:04:40.800><c> here</c><00:04:41.000><c> at</c><00:04:41.199><c> prompt</c><00:04:41.479><c> format</c>

00:04:41.909 --> 00:04:41.919 align:start position:0%
we need to do down here at prompt format
 

00:04:41.919 --> 00:04:45.430 align:start position:0%
we need to do down here at prompt format
it<00:04:42.120><c> says</c><00:04:42.520><c> open</c><00:04:42.800><c> Hermes</c><00:04:43.320><c> Emoji</c><00:04:43.759><c> Tron</c><00:04:44.080><c> 001</c><00:04:45.080><c> uses</c>

00:04:45.430 --> 00:04:45.440 align:start position:0%
it says open Hermes Emoji Tron 001 uses
 

00:04:45.440 --> 00:04:47.749 align:start position:0%
it says open Hermes Emoji Tron 001 uses
chat<00:04:45.720><c> ml</c><00:04:46.080><c> as</c><00:04:46.199><c> a</c><00:04:46.360><c> prompt</c><00:04:46.639><c> format</c><00:04:47.400><c> just</c><00:04:47.560><c> like</c>

00:04:47.749 --> 00:04:47.759 align:start position:0%
chat ml as a prompt format just like
 

00:04:47.759 --> 00:04:50.830 align:start position:0%
chat ml as a prompt format just like
open<00:04:48.039><c> Hermes</c><00:04:48.840><c> 2.5</c><00:04:49.840><c> it</c><00:04:50.039><c> also</c><00:04:50.360><c> appears</c><00:04:50.639><c> to</c>

00:04:50.830 --> 00:04:50.840 align:start position:0%
open Hermes 2.5 it also appears to
 

00:04:50.840 --> 00:04:53.590 align:start position:0%
open Hermes 2.5 it also appears to
handle<00:04:51.120><c> mol</c><00:04:51.639><c> format</c><00:04:52.039><c> great</c><00:04:52.800><c> especially</c><00:04:53.199><c> since</c>

00:04:53.590 --> 00:04:53.600 align:start position:0%
handle mol format great especially since
 

00:04:53.600 --> 00:04:56.790 align:start position:0%
handle mol format great especially since
I<00:04:53.720><c> use</c><00:04:53.960><c> that</c><00:04:54.120><c> for</c><00:04:54.240><c> the</c><00:04:54.360><c> fine</c><00:04:54.600><c> tune</c><00:04:55.479><c> oops</c><00:04:56.479><c> I</c><00:04:56.560><c> wish</c>

00:04:56.790 --> 00:04:56.800 align:start position:0%
I use that for the fine tune oops I wish
 

00:04:56.800 --> 00:04:58.909 align:start position:0%
I use that for the fine tune oops I wish
they<00:04:56.960><c> put</c><00:04:57.199><c> the</c><00:04:57.320><c> format</c><00:04:57.680><c> in</c><00:04:57.880><c> here</c><00:04:58.120><c> but</c><00:04:58.600><c> now</c><00:04:58.759><c> we</c>

00:04:58.909 --> 00:04:58.919 align:start position:0%
they put the format in here but now we
 

00:04:58.919 --> 00:05:00.629 align:start position:0%
they put the format in here but now we
have<00:04:59.039><c> to</c><00:04:59.160><c> figure</c><00:04:59.360><c> out</c><00:04:59.680><c> what</c><00:04:59.800><c> mistol</c><00:05:00.240><c> uses</c>

00:05:00.629 --> 00:05:00.639 align:start position:0%
have to figure out what mistol uses
 

00:05:00.639 --> 00:05:02.469 align:start position:0%
have to figure out what mistol uses
since<00:05:01.000><c> that's</c><00:05:01.199><c> what</c><00:05:01.479><c> the</c><00:05:01.639><c> fine</c><00:05:01.960><c> tune</c><00:05:02.320><c> was</c>

00:05:02.469 --> 00:05:02.479 align:start position:0%
since that's what the fine tune was
 

00:05:02.479 --> 00:05:04.909 align:start position:0%
since that's what the fine tune was
based<00:05:02.720><c> on</c><00:05:03.479><c> the</c><00:05:03.639><c> easiest</c><00:05:04.039><c> way</c><00:05:04.160><c> to</c><00:05:04.320><c> do</c><00:05:04.600><c> this</c><00:05:04.840><c> is</c>

00:05:04.909 --> 00:05:04.919 align:start position:0%
based on the easiest way to do this is
 

00:05:04.919 --> 00:05:07.790 align:start position:0%
based on the easiest way to do this is
to<00:05:05.039><c> go</c><00:05:05.160><c> to.</c><00:05:05.880><c> a</c><00:05:06.520><c> click</c><00:05:06.800><c> models</c><00:05:07.280><c> and</c><00:05:07.400><c> then</c><00:05:07.600><c> search</c>

00:05:07.790 --> 00:05:07.800 align:start position:0%
to go to. a click models and then search
 

00:05:07.800 --> 00:05:10.590 align:start position:0%
to go to. a click models and then search
for<00:05:08.080><c> mistol</c><00:05:09.080><c> now</c><00:05:09.320><c> click</c><00:05:09.639><c> tags</c><00:05:10.039><c> and</c><00:05:10.199><c> choose</c><00:05:10.479><c> the</c>

00:05:10.590 --> 00:05:10.600 align:start position:0%
for mistol now click tags and choose the
 

00:05:10.600 --> 00:05:12.670 align:start position:0%
for mistol now click tags and choose the
first<00:05:10.840><c> one</c><00:05:11.400><c> scroll</c><00:05:11.759><c> down</c><00:05:12.160><c> and</c><00:05:12.280><c> we</c><00:05:12.400><c> see</c><00:05:12.560><c> the</c>

00:05:12.670 --> 00:05:12.680 align:start position:0%
first one scroll down and we see the
 

00:05:12.680 --> 00:05:17.150 align:start position:0%
first one scroll down and we see the
template<00:05:13.600><c> inst.</c><00:05:14.600><c> system.</c><00:05:15.560><c> prompt</c><00:05:16.120><c> inst</c><00:05:16.960><c> all</c>

00:05:17.150 --> 00:05:17.160 align:start position:0%
template inst. system. prompt inst all
 

00:05:17.160 --> 00:05:20.350 align:start position:0%
template inst. system. prompt inst all
those<00:05:17.400><c> brackets</c><00:05:17.919><c> are</c><00:05:18.400><c> very</c><00:05:18.880><c> important</c><00:05:19.880><c> so</c>

00:05:20.350 --> 00:05:20.360 align:start position:0%
those brackets are very important so
 

00:05:20.360 --> 00:05:23.029 align:start position:0%
those brackets are very important so
let's<00:05:20.639><c> open</c><00:05:21.000><c> a</c><00:05:21.160><c> code</c><00:05:21.440><c> editor</c><00:05:22.120><c> I'm</c><00:05:22.280><c> using</c><00:05:22.639><c> Vim</c>

00:05:23.029 --> 00:05:23.039 align:start position:0%
let's open a code editor I'm using Vim
 

00:05:23.039 --> 00:05:25.070 align:start position:0%
let's open a code editor I'm using Vim
just<00:05:23.160><c> to</c><00:05:23.319><c> keep</c><00:05:23.479><c> it</c><00:05:23.639><c> simple</c><00:05:24.199><c> the</c><00:05:24.360><c> first</c><00:05:24.600><c> line</c><00:05:24.919><c> is</c>

00:05:25.070 --> 00:05:25.080 align:start position:0%
just to keep it simple the first line is
 

00:05:25.080 --> 00:05:28.070 align:start position:0%
just to keep it simple the first line is
the<00:05:25.240><c> from</c><00:05:25.560><c> instruction</c><00:05:26.520><c> from.</c>

00:05:28.070 --> 00:05:28.080 align:start position:0%
the from instruction from.
 

00:05:28.080 --> 00:05:30.110 align:start position:0%
the from instruction from.
q40<00:05:29.080><c> dobin</c>

00:05:30.110 --> 00:05:30.120 align:start position:0%
q40 dobin
 

00:05:30.120 --> 00:05:32.189 align:start position:0%
q40 dobin
next<00:05:30.400><c> we</c><00:05:30.520><c> need</c><00:05:30.680><c> a</c><00:05:30.840><c> template</c><00:05:31.520><c> I'll</c><00:05:31.759><c> copy</c><00:05:32.039><c> that</c>

00:05:32.189 --> 00:05:32.199 align:start position:0%
next we need a template I'll copy that
 

00:05:32.199 --> 00:05:35.230 align:start position:0%
next we need a template I'll copy that
template<00:05:32.600><c> from</c><00:05:32.960><c> the.</c><00:05:33.960><c> website</c><00:05:34.720><c> and</c><00:05:34.919><c> paste</c><00:05:35.080><c> it</c>

00:05:35.230 --> 00:05:35.240 align:start position:0%
template from the. website and paste it
 

00:05:35.240 --> 00:05:37.950 align:start position:0%
template from the. website and paste it
in<00:05:35.479><c> here</c><00:05:36.280><c> and</c><00:05:36.440><c> that's</c><00:05:36.680><c> the</c><00:05:36.840><c> minimum</c><00:05:37.240><c> we</c><00:05:37.319><c> need</c>

00:05:37.950 --> 00:05:37.960 align:start position:0%
in here and that's the minimum we need
 

00:05:37.960 --> 00:05:40.830 align:start position:0%
in here and that's the minimum we need
so<00:05:38.240><c> exit</c><00:05:38.560><c> out</c><00:05:39.000><c> and</c><00:05:39.120><c> create</c><00:05:39.360><c> the</c><00:05:39.479><c> model</c><00:05:40.240><c> AMA</c>

00:05:40.830 --> 00:05:40.840 align:start position:0%
so exit out and create the model AMA
 

00:05:40.840 --> 00:05:43.550 align:start position:0%
so exit out and create the model AMA
create<00:05:41.880><c> emoon</c><00:05:42.880><c> since</c><00:05:43.080><c> we're</c><00:05:43.199><c> in</c><00:05:43.319><c> the</c><00:05:43.400><c> same</c>

00:05:43.550 --> 00:05:43.560 align:start position:0%
create emoon since we're in the same
 

00:05:43.560 --> 00:05:44.870 align:start position:0%
create emoon since we're in the same
directory<00:05:43.919><c> as</c><00:05:44.000><c> the</c><00:05:44.120><c> model</c><00:05:44.360><c> file</c><00:05:44.600><c> and</c><00:05:44.720><c> it's</c>

00:05:44.870 --> 00:05:44.880 align:start position:0%
directory as the model file and it's
 

00:05:44.880 --> 00:05:46.670 align:start position:0%
directory as the model file and it's
called<00:05:45.160><c> Model</c><00:05:45.520><c> file</c><00:05:46.039><c> we</c><00:05:46.160><c> don't</c><00:05:46.319><c> need</c><00:05:46.479><c> to</c>

00:05:46.670 --> 00:05:46.680 align:start position:0%
called Model file we don't need to
 

00:05:46.680 --> 00:05:51.270 align:start position:0%
called Model file we don't need to
specify<00:05:47.120><c> it</c><00:05:48.000><c> now</c><00:05:48.440><c> AMA</c><00:05:48.960><c> run</c><00:05:49.400><c> Emoji</c><00:05:49.880><c> Tron</c><00:05:50.520><c> hi</c><00:05:51.160><c> and</c>

00:05:51.270 --> 00:05:51.280 align:start position:0%
specify it now AMA run Emoji Tron hi and
 

00:05:51.280 --> 00:05:53.510 align:start position:0%
specify it now AMA run Emoji Tron hi and
we<00:05:51.400><c> get</c><00:05:51.520><c> an</c><00:05:51.680><c> emoji</c><00:05:52.319><c> followed</c><00:05:52.680><c> by</c><00:05:52.840><c> a</c><00:05:53.000><c> control</c>

00:05:53.510 --> 00:05:53.520 align:start position:0%
we get an emoji followed by a control
 

00:05:53.520 --> 00:05:56.029 align:start position:0%
we get an emoji followed by a control
phrase<00:05:53.919><c> I</c><00:05:54.039><c> am</c><00:05:54.280><c> end</c><00:05:55.240><c> what</c><00:05:55.360><c> is</c><00:05:55.479><c> the</c><00:05:55.560><c> meaning</c><00:05:55.800><c> of</c>

00:05:56.029 --> 00:05:56.039 align:start position:0%
phrase I am end what is the meaning of
 

00:05:56.039 --> 00:05:58.790 align:start position:0%
phrase I am end what is the meaning of
life<00:05:56.639><c> we</c><00:05:56.800><c> get</c><00:05:56.919><c> some</c><00:05:57.120><c> emojis</c><00:05:57.680><c> and</c><00:05:58.039><c> that</c><00:05:58.360><c> I</c><00:05:58.520><c> am</c>

00:05:58.790 --> 00:05:58.800 align:start position:0%
life we get some emojis and that I am
 

00:05:58.800 --> 00:06:01.430 align:start position:0%
life we get some emojis and that I am
end<00:05:59.280><c> again</c><00:05:59.520><c> again</c><00:06:00.440><c> okay</c><00:06:00.759><c> let's</c><00:06:00.960><c> get</c><00:06:01.120><c> out</c><00:06:01.240><c> of</c>

00:06:01.430 --> 00:06:01.440 align:start position:0%
end again again okay let's get out of
 

00:06:01.440 --> 00:06:03.870 align:start position:0%
end again again okay let's get out of
here<00:06:01.840><c> and</c><00:06:02.039><c> edit</c><00:06:02.319><c> that</c><00:06:02.440><c> model</c><00:06:02.720><c> file</c><00:06:03.039><c> again</c><00:06:03.639><c> I'll</c>

00:06:03.870 --> 00:06:03.880 align:start position:0%
here and edit that model file again I'll
 

00:06:03.880 --> 00:06:07.189 align:start position:0%
here and edit that model file again I'll
copy<00:06:04.199><c> the</c><00:06:04.440><c> iend</c><00:06:05.280><c> text</c><00:06:05.639><c> so</c><00:06:05.800><c> I</c><00:06:05.880><c> can</c><00:06:06.080><c> paste</c><00:06:06.280><c> it</c><00:06:06.440><c> in</c>

00:06:07.189 --> 00:06:07.199 align:start position:0%
copy the iend text so I can paste it in
 

00:06:07.199 --> 00:06:09.749 align:start position:0%
copy the iend text so I can paste it in
now<00:06:07.520><c> add</c><00:06:08.000><c> parameter</c><00:06:08.479><c> stop</c><00:06:09.039><c> and</c><00:06:09.240><c> paste</c><00:06:09.440><c> in</c><00:06:09.599><c> the</c>

00:06:09.749 --> 00:06:09.759 align:start position:0%
now add parameter stop and paste in the
 

00:06:09.759 --> 00:06:12.670 align:start position:0%
now add parameter stop and paste in the
text<00:06:10.599><c> exit</c><00:06:10.919><c> out</c><00:06:11.319><c> and</c><00:06:11.440><c> run</c><00:06:11.639><c> the</c><00:06:11.800><c> create</c><00:06:12.160><c> command</c>

00:06:12.670 --> 00:06:12.680 align:start position:0%
text exit out and run the create command
 

00:06:12.680 --> 00:06:16.070 align:start position:0%
text exit out and run the create command
again<00:06:13.560><c> now</c><00:06:13.759><c> do</c><00:06:14.039><c> AMA</c><00:06:14.520><c> run</c><00:06:14.759><c> again</c><00:06:15.599><c> what</c><00:06:15.800><c> is</c><00:06:15.960><c> the</c>

00:06:16.070 --> 00:06:16.080 align:start position:0%
again now do AMA run again what is the
 

00:06:16.080 --> 00:06:20.029 align:start position:0%
again now do AMA run again what is the
meaning<00:06:16.560><c> of</c><00:06:16.800><c> life</c><00:06:17.800><c> cool</c><00:06:18.800><c> create</c><00:06:19.160><c> a</c><00:06:19.479><c> recipe</c><00:06:19.800><c> for</c>

00:06:20.029 --> 00:06:20.039 align:start position:0%
meaning of life cool create a recipe for
 

00:06:20.039 --> 00:06:26.189 align:start position:0%
meaning of life cool create a recipe for
spicy<00:06:20.800><c> mayo</c><00:06:22.199><c> nice</c><00:06:23.199><c> what</c><00:06:23.639><c> is</c><00:06:24.120><c> a</c><00:06:24.400><c> black</c><00:06:24.880><c> hole</c><00:06:25.880><c> oh</c>

00:06:26.189 --> 00:06:26.199 align:start position:0%
spicy mayo nice what is a black hole oh
 

00:06:26.199 --> 00:06:29.629 align:start position:0%
spicy mayo nice what is a black hole oh
yeah<00:06:26.639><c> Neil</c><00:06:27.000><c> degrass</c><00:06:27.440><c> Tyson</c><00:06:28.080><c> your</c><00:06:28.319><c> job</c><00:06:28.520><c> is</c><00:06:28.720><c> mine</c>

00:06:29.629 --> 00:06:29.639 align:start position:0%
yeah Neil degrass Tyson your job is mine
 

00:06:29.639 --> 00:06:31.510 align:start position:0%
yeah Neil degrass Tyson your job is mine
and<00:06:29.840><c> now</c><00:06:30.199><c> for</c><00:06:30.440><c> the</c><00:06:30.560><c> biggest</c><00:06:30.880><c> challenge</c><00:06:31.360><c> that</c>

00:06:31.510 --> 00:06:31.520 align:start position:0%
and now for the biggest challenge that
 

00:06:31.520 --> 00:06:34.830 align:start position:0%
and now for the biggest challenge that
some<00:06:31.680><c> of</c><00:06:31.840><c> us</c><00:06:32.319><c> will</c><00:06:32.560><c> ever</c><00:06:32.960><c> face</c><00:06:33.960><c> how</c><00:06:34.280><c> are</c><00:06:34.520><c> babies</c>

00:06:34.830 --> 00:06:34.840 align:start position:0%
some of us will ever face how are babies
 

00:06:34.840 --> 00:06:38.710 align:start position:0%
some of us will ever face how are babies
made<00:06:35.520><c> explain</c><00:06:35.960><c> like</c><00:06:36.160><c> I</c><00:06:36.319><c> am</c><00:06:36.960><c> five</c><00:06:37.960><c> awesome</c><00:06:38.520><c> I</c>

00:06:38.710 --> 00:06:38.720 align:start position:0%
made explain like I am five awesome I
 

00:06:38.720 --> 00:06:41.390 align:start position:0%
made explain like I am five awesome I
love<00:06:39.120><c> that</c><00:06:39.520><c> baby</c><00:06:39.880><c> come</c><00:06:40.160><c> soon</c><00:06:40.880><c> that</c><00:06:41.000><c> you</c><00:06:41.080><c> see</c><00:06:41.280><c> at</c>

00:06:41.390 --> 00:06:41.400 align:start position:0%
love that baby come soon that you see at
 

00:06:41.400 --> 00:06:44.909 align:start position:0%
love that baby come soon that you see at
the<00:06:41.599><c> end</c><00:06:42.599><c> that</c><00:06:42.759><c> worked</c><00:06:43.319><c> perfectly</c><00:06:44.280><c> it</c><00:06:44.440><c> doesn't</c>

00:06:44.909 --> 00:06:44.919 align:start position:0%
the end that worked perfectly it doesn't
 

00:06:44.919 --> 00:06:47.749 align:start position:0%
the end that worked perfectly it doesn't
always<00:06:45.440><c> work</c><00:06:45.720><c> perfectly</c><00:06:46.680><c> we</c><00:06:46.840><c> used</c><00:06:47.120><c> a</c><00:06:47.280><c> Docker</c>

00:06:47.749 --> 00:06:47.759 align:start position:0%
always work perfectly we used a Docker
 

00:06:47.759 --> 00:06:49.309 align:start position:0%
always work perfectly we used a Docker
image<00:06:48.240><c> to</c><00:06:48.400><c> do</c><00:06:48.520><c> the</c><00:06:48.680><c> conversion</c><00:06:49.160><c> and</c>

00:06:49.309 --> 00:06:49.319 align:start position:0%
image to do the conversion and
 

00:06:49.319 --> 00:06:51.110 align:start position:0%
image to do the conversion and
quantization<00:06:50.120><c> but</c><00:06:50.240><c> that</c><00:06:50.360><c> was</c><00:06:50.599><c> removed</c><00:06:51.000><c> from</c>

00:06:51.110 --> 00:06:51.120 align:start position:0%
quantization but that was removed from
 

00:06:51.120 --> 00:06:53.670 align:start position:0%
quantization but that was removed from
the<00:06:51.240><c> docks</c><00:06:51.639><c> recently</c><00:06:52.560><c> maybe</c><00:06:52.800><c> it</c><00:06:52.919><c> doesn't</c><00:06:53.199><c> work</c>

00:06:53.670 --> 00:06:53.680 align:start position:0%
the docks recently maybe it doesn't work
 

00:06:53.680 --> 00:06:56.790 align:start position:0%
the docks recently maybe it doesn't work
as<00:06:53.880><c> reliably</c><00:06:54.599><c> for</c><00:06:54.800><c> most</c><00:06:55.120><c> models</c><00:06:56.120><c> so</c><00:06:56.560><c> there's</c>

00:06:56.790 --> 00:06:56.800 align:start position:0%
as reliably for most models so there's
 

00:06:56.800 --> 00:06:59.150 align:start position:0%
as reliably for most models so there's
the<00:06:56.919><c> older</c><00:06:57.440><c> process</c><00:06:57.840><c> which</c><00:06:57.960><c> is</c><00:06:58.160><c> more</c><00:06:58.520><c> manual</c>

00:06:59.150 --> 00:06:59.160 align:start position:0%
the older process which is more manual
 

00:06:59.160 --> 00:07:01.430 align:start position:0%
the older process which is more manual
it<00:06:59.360><c> it</c><00:06:59.479><c> involves</c><00:06:59.800><c> cloning</c><00:07:00.120><c> the</c><00:07:00.240><c> AMA</c><00:07:00.639><c> repo</c><00:07:01.280><c> and</c>

00:07:01.430 --> 00:07:01.440 align:start position:0%
it it involves cloning the AMA repo and
 

00:07:01.440 --> 00:07:04.110 align:start position:0%
it it involves cloning the AMA repo and
using<00:07:01.720><c> the</c><00:07:01.879><c> Llama</c><00:07:02.240><c> CPP</c><00:07:02.800><c> subm</c><00:07:03.120><c> module</c><00:07:03.879><c> then</c>

00:07:04.110 --> 00:07:04.120 align:start position:0%
using the Llama CPP subm module then
 

00:07:04.120 --> 00:07:05.629 align:start position:0%
using the Llama CPP subm module then
creating<00:07:04.520><c> the</c><00:07:04.680><c> quantise</c><00:07:05.160><c> command</c><00:07:05.520><c> and</c>

00:07:05.629 --> 00:07:05.639 align:start position:0%
creating the quantise command and
 

00:07:05.639 --> 00:07:07.990 align:start position:0%
creating the quantise command and
running<00:07:05.960><c> the</c><00:07:06.120><c> Python</c><00:07:06.400><c> scripts</c><00:07:06.800><c> yourself</c><00:07:07.800><c> so</c>

00:07:07.990 --> 00:07:08.000 align:start position:0%
running the Python scripts yourself so
 

00:07:08.000 --> 00:07:09.390 align:start position:0%
running the Python scripts yourself so
that<00:07:08.080><c> means</c><00:07:08.319><c> getting</c><00:07:08.520><c> a</c><00:07:08.639><c> working</c><00:07:09.039><c> python</c>

00:07:09.390 --> 00:07:09.400 align:start position:0%
that means getting a working python
 

00:07:09.400 --> 00:07:11.589 align:start position:0%
that means getting a working python
environment<00:07:09.960><c> which</c><00:07:10.720><c> is</c><00:07:10.919><c> always</c><00:07:11.160><c> a</c><00:07:11.280><c> pain</c><00:07:11.479><c> in</c>

00:07:11.589 --> 00:07:11.599 align:start position:0%
environment which is always a pain in
 

00:07:11.599 --> 00:07:13.469 align:start position:0%
environment which is always a pain in
the<00:07:11.680><c> butt</c><00:07:12.400><c> you</c><00:07:12.520><c> can</c><00:07:12.680><c> find</c><00:07:12.879><c> the</c><00:07:13.000><c> instructions</c>

00:07:13.469 --> 00:07:13.479 align:start position:0%
the butt you can find the instructions
 

00:07:13.479 --> 00:07:15.350 align:start position:0%
the butt you can find the instructions
for<00:07:13.639><c> the</c><00:07:13.840><c> process</c><00:07:14.160><c> here</c><00:07:14.280><c> in</c><00:07:14.360><c> the</c><00:07:14.479><c> repo</c><00:07:15.160><c> and</c>

00:07:15.350 --> 00:07:15.360 align:start position:0%
for the process here in the repo and
 

00:07:15.360 --> 00:07:17.670 align:start position:0%
for the process here in the repo and
scroll<00:07:15.800><c> down</c><00:07:16.240><c> to</c><00:07:16.440><c> importing</c><00:07:16.879><c> pytorch</c><00:07:17.560><c> and</c>

00:07:17.670 --> 00:07:17.680 align:start position:0%
scroll down to importing pytorch and
 

00:07:17.680 --> 00:07:19.830 align:start position:0%
scroll down to importing pytorch and
safe<00:07:17.960><c> tensors</c><00:07:18.960><c> but</c><00:07:19.160><c> first</c><00:07:19.440><c> check</c><00:07:19.680><c> the</c>

00:07:19.830 --> 00:07:19.840 align:start position:0%
safe tensors but first check the
 

00:07:19.840 --> 00:07:22.230 align:start position:0%
safe tensors but first check the
architecture<00:07:20.360><c> in</c><00:07:20.520><c> config.js</c><00:07:21.240><c> file</c>

00:07:22.230 --> 00:07:22.240 align:start position:0%
architecture in config.js file
 

00:07:22.240 --> 00:07:23.350 align:start position:0%
architecture in config.js file
unfortunately<00:07:22.800><c> I</c><00:07:22.879><c> don't</c><00:07:23.039><c> think</c><00:07:23.199><c> the</c>

00:07:23.350 --> 00:07:23.360 align:start position:0%
unfortunately I don't think the
 

00:07:23.360 --> 00:07:25.510 align:start position:0%
unfortunately I don't think the
architecture<00:07:24.000><c> supported</c><00:07:25.000><c> is</c><00:07:25.240><c> actually</c>

00:07:25.510 --> 00:07:25.520 align:start position:0%
architecture supported is actually
 

00:07:25.520 --> 00:07:27.629 align:start position:0%
architecture supported is actually
documented<00:07:26.440><c> but</c><00:07:26.639><c> you</c><00:07:26.759><c> can</c><00:07:27.000><c> probably</c><00:07:27.360><c> guess</c>

00:07:27.629 --> 00:07:27.639 align:start position:0%
documented but you can probably guess
 

00:07:27.639 --> 00:07:29.950 align:start position:0%
documented but you can probably guess
some<00:07:27.759><c> of</c><00:07:27.960><c> them</c><00:07:28.960><c> sometimes</c><00:07:29.199><c> the</c><00:07:29.479><c> output</c><00:07:29.840><c> is</c>

00:07:29.950 --> 00:07:29.960 align:start position:0%
some of them sometimes the output is
 

00:07:29.960 --> 00:07:31.550 align:start position:0%
some of them sometimes the output is
going<00:07:30.120><c> to</c><00:07:30.319><c> complain</c><00:07:30.759><c> about</c><00:07:31.000><c> something</c><00:07:31.400><c> and</c>

00:07:31.550 --> 00:07:31.560 align:start position:0%
going to complain about something and
 

00:07:31.560 --> 00:07:33.670 align:start position:0%
going to complain about something and
suggest<00:07:31.879><c> a</c><00:07:32.039><c> command</c><00:07:32.319><c> line</c><00:07:32.560><c> parameter</c><00:07:33.560><c> that</c>

00:07:33.670 --> 00:07:33.680 align:start position:0%
suggest a command line parameter that
 

00:07:33.680 --> 00:07:35.589 align:start position:0%
suggest a command line parameter that
won't<00:07:34.000><c> work</c><00:07:34.360><c> using</c><00:07:34.639><c> the</c><00:07:34.759><c> docker</c><00:07:35.120><c> method</c><00:07:35.440><c> so</c>

00:07:35.589 --> 00:07:35.599 align:start position:0%
won't work using the docker method so
 

00:07:35.599 --> 00:07:38.070 align:start position:0%
won't work using the docker method so
you<00:07:35.840><c> have</c><00:07:35.960><c> to</c><00:07:36.160><c> manually</c><00:07:36.560><c> run</c><00:07:36.759><c> the</c><00:07:36.919><c> steps</c><00:07:37.879><c> I've</c>

00:07:38.070 --> 00:07:38.080 align:start position:0%
you have to manually run the steps I've
 

00:07:38.080 --> 00:07:39.990 align:start position:0%
you have to manually run the steps I've
had<00:07:38.280><c> that</c><00:07:38.400><c> a</c><00:07:38.479><c> few</c><00:07:38.720><c> times</c><00:07:38.960><c> with</c><00:07:39.120><c> needing</c><00:07:39.400><c> to</c><00:07:39.720><c> pad</c>

00:07:39.990 --> 00:07:40.000 align:start position:0%
had that a few times with needing to pad
 

00:07:40.000 --> 00:07:42.550 align:start position:0%
had that a few times with needing to pad
the<00:07:40.199><c> size</c><00:07:40.520><c> of</c><00:07:40.680><c> something</c><00:07:41.639><c> add</c><00:07:41.840><c> the</c><00:07:41.960><c> parameter</c>

00:07:42.550 --> 00:07:42.560 align:start position:0%
the size of something add the parameter
 

00:07:42.560 --> 00:07:44.550 align:start position:0%
the size of something add the parameter
and<00:07:42.680><c> it</c><00:07:42.800><c> worked</c><00:07:43.080><c> just</c><00:07:43.280><c> fine</c><00:07:44.159><c> the</c><00:07:44.319><c> next</c>

00:07:44.550 --> 00:07:44.560 align:start position:0%
and it worked just fine the next
 

00:07:44.560 --> 00:07:46.790 align:start position:0%
and it worked just fine the next
challenge<00:07:45.000><c> is</c><00:07:45.199><c> always</c><00:07:45.639><c> the</c><00:07:45.800><c> template</c>

00:07:46.790 --> 00:07:46.800 align:start position:0%
challenge is always the template
 

00:07:46.800 --> 00:07:48.149 align:start position:0%
challenge is always the template
sometimes<00:07:47.080><c> model</c><00:07:47.400><c> makers</c><00:07:47.720><c> State</c><00:07:48.000><c> the</c>

00:07:48.149 --> 00:07:48.159 align:start position:0%
sometimes model makers State the
 

00:07:48.159 --> 00:07:50.350 align:start position:0%
sometimes model makers State the
template<00:07:48.800><c> but</c><00:07:49.000><c> often</c><00:07:49.240><c> they</c><00:07:49.400><c> just</c><00:07:49.520><c> seem</c><00:07:49.720><c> to</c>

00:07:50.350 --> 00:07:50.360 align:start position:0%
template but often they just seem to
 

00:07:50.360 --> 00:07:52.510 align:start position:0%
template but often they just seem to
assume<00:07:50.800><c> that</c><00:07:50.919><c> you're</c><00:07:51.080><c> going</c><00:07:51.159><c> to</c><00:07:51.319><c> know</c><00:07:52.240><c> and</c><00:07:52.360><c> so</c>

00:07:52.510 --> 00:07:52.520 align:start position:0%
assume that you're going to know and so
 

00:07:52.520 --> 00:07:54.350 align:start position:0%
assume that you're going to know and so
you<00:07:52.639><c> have</c><00:07:52.720><c> to</c><00:07:52.840><c> try</c><00:07:53.039><c> a</c><00:07:53.159><c> bunch</c><00:07:53.879><c> and</c><00:07:54.000><c> you</c><00:07:54.080><c> can</c>

00:07:54.350 --> 00:07:54.360 align:start position:0%
you have to try a bunch and you can
 

00:07:54.360 --> 00:07:56.390 align:start position:0%
you have to try a bunch and you can
often<00:07:54.759><c> figure</c><00:07:55.039><c> out</c><00:07:55.240><c> the</c><00:07:55.360><c> base</c><00:07:55.639><c> models</c><00:07:56.120><c> then</c>

00:07:56.390 --> 00:07:56.400 align:start position:0%
often figure out the base models then
 

00:07:56.400 --> 00:07:58.510 align:start position:0%
often figure out the base models then
either<00:07:56.599><c> look</c><00:07:56.759><c> up</c><00:07:57.039><c> their</c><00:07:57.199><c> readmes</c><00:07:58.080><c> or</c><00:07:58.280><c> look</c><00:07:58.400><c> at</c>

00:07:58.510 --> 00:07:58.520 align:start position:0%
either look up their readmes or look at
 

00:07:58.520 --> 00:08:01.430 align:start position:0%
either look up their readmes or look at
the<00:07:58.639><c> model</c><00:07:58.840><c> on</c><00:07:59.280><c> l.</c><00:07:59.639><c> a</c><00:08:00.400><c> then</c><00:08:00.800><c> get</c><00:08:00.960><c> the</c><00:08:01.080><c> formats</c>

00:08:01.430 --> 00:08:01.440 align:start position:0%
the model on l. a then get the formats
 

00:08:01.440 --> 00:08:03.869 align:start position:0%
the model on l. a then get the formats
for<00:08:01.720><c> llama</c><00:08:02.039><c> 2</c><00:08:02.400><c> mistal</c><00:08:02.840><c> open</c><00:08:03.039><c> Herz</c><00:08:03.440><c> and</c><00:08:03.599><c> others</c>

00:08:03.869 --> 00:08:03.879 align:start position:0%
for llama 2 mistal open Herz and others
 

00:08:03.879 --> 00:08:06.350 align:start position:0%
for llama 2 mistal open Herz and others
and<00:08:04.319><c> just</c><00:08:04.479><c> try</c><00:08:04.720><c> each</c><00:08:04.919><c> one</c><00:08:05.520><c> hoping</c><00:08:05.919><c> to</c><00:08:06.080><c> get</c><00:08:06.199><c> a</c>

00:08:06.350 --> 00:08:06.360 align:start position:0%
and just try each one hoping to get a
 

00:08:06.360 --> 00:08:08.790 align:start position:0%
and just try each one hoping to get a
match<00:08:07.360><c> so</c><00:08:07.560><c> now</c><00:08:07.680><c> you</c><00:08:07.800><c> have</c><00:08:07.919><c> a</c><00:08:08.000><c> model</c><00:08:08.599><c> and</c><00:08:08.680><c> you</c>

00:08:08.790 --> 00:08:08.800 align:start position:0%
match so now you have a model and you
 

00:08:08.800 --> 00:08:10.510 align:start position:0%
match so now you have a model and you
want<00:08:08.879><c> to</c><00:08:08.960><c> share</c><00:08:09.199><c> it</c><00:08:09.360><c> with</c><00:08:09.520><c> others</c><00:08:09.960><c> right</c>

00:08:10.510 --> 00:08:10.520 align:start position:0%
want to share it with others right
 

00:08:10.520 --> 00:08:12.869 align:start position:0%
want to share it with others right
you'll<00:08:10.759><c> need</c><00:08:11.080><c> a</c><00:08:11.240><c> name</c><00:08:11.560><c> space</c><00:08:11.840><c> to</c><00:08:11.960><c> do</c><00:08:12.159><c> that</c><00:08:12.800><c> if</c>

00:08:12.869 --> 00:08:12.879 align:start position:0%
you'll need a name space to do that if
 

00:08:12.879 --> 00:08:14.909 align:start position:0%
you'll need a name space to do that if
you<00:08:13.000><c> don't</c><00:08:13.199><c> already</c><00:08:13.520><c> have</c><00:08:13.720><c> one</c><00:08:14.080><c> set</c><00:08:14.319><c> up</c><00:08:14.800><c> then</c>

00:08:14.909 --> 00:08:14.919 align:start position:0%
you don't already have one set up then
 

00:08:14.919 --> 00:08:17.149 align:start position:0%
you don't already have one set up then
go<00:08:15.080><c> to</c><00:08:15.280><c> Alama</c><00:08:15.759><c> Ai</c><00:08:16.199><c> and</c><00:08:16.360><c> click</c><00:08:16.560><c> the</c><00:08:16.680><c> signin</c>

00:08:17.149 --> 00:08:17.159 align:start position:0%
go to Alama Ai and click the signin
 

00:08:17.159 --> 00:08:19.469 align:start position:0%
go to Alama Ai and click the signin
button<00:08:18.000><c> below</c><00:08:18.280><c> your</c><00:08:18.479><c> credentials</c><00:08:19.080><c> there's</c><00:08:19.240><c> a</c>

00:08:19.469 --> 00:08:19.479 align:start position:0%
button below your credentials there's a
 

00:08:19.479 --> 00:08:22.230 align:start position:0%
button below your credentials there's a
create<00:08:19.919><c> account</c><00:08:20.240><c> link</c><00:08:21.000><c> enter</c><00:08:21.319><c> your</c><00:08:21.520><c> email</c><00:08:22.120><c> a</c>

00:08:22.230 --> 00:08:22.240 align:start position:0%
create account link enter your email a
 

00:08:22.240 --> 00:08:24.270 align:start position:0%
create account link enter your email a
username<00:08:22.720><c> and</c><00:08:22.840><c> a</c><00:08:22.960><c> password</c><00:08:23.840><c> make</c><00:08:23.960><c> sure</c><00:08:24.120><c> you</c>

00:08:24.270 --> 00:08:24.280 align:start position:0%
username and a password make sure you
 

00:08:24.280 --> 00:08:25.990 align:start position:0%
username and a password make sure you
set<00:08:24.520><c> the</c><00:08:24.680><c> email</c><00:08:25.000><c> address</c><00:08:25.360><c> to</c><00:08:25.560><c> something</c><00:08:25.919><c> that</c>

00:08:25.990 --> 00:08:26.000 align:start position:0%
set the email address to something that
 

00:08:26.000 --> 00:08:28.149 align:start position:0%
set the email address to something that
you're<00:08:26.159><c> going</c><00:08:26.240><c> to</c><00:08:26.400><c> have</c><00:08:26.639><c> access</c><00:08:26.919><c> to</c><00:08:27.159><c> Forever</c>

00:08:28.149 --> 00:08:28.159 align:start position:0%
you're going to have access to Forever
 

00:08:28.159 --> 00:08:30.550 align:start position:0%
you're going to have access to Forever
using<00:08:28.520><c> a</c><00:08:28.680><c> work</c><00:08:28.960><c> account</c><00:08:29.520><c> is</c><00:08:29.759><c> great</c><00:08:30.199><c> until</c><00:08:30.440><c> you</c>

00:08:30.550 --> 00:08:30.560 align:start position:0%
using a work account is great until you
 

00:08:30.560 --> 00:08:32.870 align:start position:0%
using a work account is great until you
don't<00:08:30.720><c> work</c><00:08:30.919><c> there</c><00:08:31.080><c> anymore</c><00:08:32.080><c> the</c><00:08:32.200><c> username</c><00:08:32.760><c> I</c>

00:08:32.870 --> 00:08:32.880 align:start position:0%
don't work there anymore the username I
 

00:08:32.880 --> 00:08:35.670 align:start position:0%
don't work there anymore the username I
think<00:08:33.120><c> has</c><00:08:33.240><c> to</c><00:08:33.360><c> be</c><00:08:33.719><c> three</c><00:08:34.080><c> or</c><00:08:34.560><c> four</c><00:08:35.080><c> characters</c>

00:08:35.670 --> 00:08:35.680 align:start position:0%
think has to be three or four characters
 

00:08:35.680 --> 00:08:38.909 align:start position:0%
think has to be three or four characters
or<00:08:35.919><c> more</c><00:08:36.760><c> there</c><00:08:36.880><c> is</c><00:08:37.039><c> only</c><00:08:37.519><c> one</c><00:08:37.919><c> exception</c><00:08:38.320><c> to</c>

00:08:38.909 --> 00:08:38.919 align:start position:0%
or more there is only one exception to
 

00:08:38.919 --> 00:08:42.389 align:start position:0%
or more there is only one exception to
that<00:08:40.039><c> mine</c><00:08:41.039><c> okay</c><00:08:41.360><c> once</c><00:08:41.519><c> you've</c><00:08:41.760><c> done</c><00:08:42.039><c> that</c>

00:08:42.389 --> 00:08:42.399 align:start position:0%
that mine okay once you've done that
 

00:08:42.399 --> 00:08:44.149 align:start position:0%
that mine okay once you've done that
you'll<00:08:42.599><c> see</c><00:08:42.839><c> the</c><00:08:43.000><c> instructions</c><00:08:43.519><c> to</c><00:08:43.680><c> add</c><00:08:43.880><c> your</c>

00:08:44.149 --> 00:08:44.159 align:start position:0%
you'll see the instructions to add your
 

00:08:44.159 --> 00:08:47.310 align:start position:0%
you'll see the instructions to add your
public<00:08:44.440><c> key</c><00:08:44.680><c> to</c><00:08:44.920><c> AMA</c><00:08:45.920><c> now</c><00:08:46.160><c> this</c><00:08:46.519><c> looks</c><00:08:46.920><c> like</c><00:08:47.120><c> an</c>

00:08:47.310 --> 00:08:47.320 align:start position:0%
public key to AMA now this looks like an
 

00:08:47.320 --> 00:08:50.550 align:start position:0%
public key to AMA now this looks like an
SSH<00:08:47.880><c> key</c><00:08:48.600><c> but</c><00:08:48.760><c> it</c><00:08:48.920><c> isn't</c><00:08:49.800><c> make</c><00:08:49.959><c> sure</c><00:08:50.120><c> you</c><00:08:50.279><c> grab</c>

00:08:50.550 --> 00:08:50.560 align:start position:0%
SSH key but it isn't make sure you grab
 

00:08:50.560 --> 00:08:53.990 align:start position:0%
SSH key but it isn't make sure you grab
the<00:08:50.680><c> public</c><00:08:51.000><c> key</c><00:08:51.360><c> from</c><00:08:51.519><c> your</c><00:08:52.120><c> olama</c><00:08:53.000><c> directory</c>

00:08:53.990 --> 00:08:54.000 align:start position:0%
the public key from your olama directory
 

00:08:54.000 --> 00:08:56.269 align:start position:0%
the public key from your olama directory
next<00:08:54.480><c> we</c><00:08:54.600><c> need</c><00:08:54.720><c> to</c><00:08:54.959><c> rename</c><00:08:55.360><c> our</c><00:08:55.519><c> model</c><00:08:55.959><c> because</c>

00:08:56.269 --> 00:08:56.279 align:start position:0%
next we need to rename our model because
 

00:08:56.279 --> 00:09:00.030 align:start position:0%
next we need to rename our model because
emoon<00:08:57.080><c> doesn't</c><00:08:57.360><c> have</c><00:08:57.440><c> a</c><00:08:57.600><c> name</c><00:08:57.880><c> space</c><00:08:58.640><c> so</c><00:08:58.880><c> Al</c><00:08:59.440><c> CP</c>

00:09:00.030 --> 00:09:00.040 align:start position:0%
emoon doesn't have a name space so Al CP
 

00:09:00.040 --> 00:09:02.630 align:start position:0%
emoon doesn't have a name space so Al CP
emoon<00:09:01.040><c> and</c><00:09:01.160><c> then</c><00:09:01.320><c> your</c><00:09:01.560><c> namespace</c><00:09:02.240><c> which</c><00:09:02.399><c> for</c>

00:09:02.630 --> 00:09:02.640 align:start position:0%
emoon and then your namespace which for
 

00:09:02.640 --> 00:09:05.750 align:start position:0%
emoon and then your namespace which for
me<00:09:03.079><c> is</c><00:09:03.320><c> just</c><00:09:03.480><c> the</c><00:09:03.680><c> letter</c><00:09:03.920><c> M</c><00:09:04.800><c> and</c><00:09:04.920><c> then</c><00:09:05.160><c> SL</c>

00:09:05.750 --> 00:09:05.760 align:start position:0%
me is just the letter M and then SL
 

00:09:05.760 --> 00:09:09.710 align:start position:0%
me is just the letter M and then SL
emoon<00:09:06.760><c> if</c><00:09:06.880><c> you</c><00:09:07.160><c> rename</c><00:09:07.600><c> yours</c><00:09:07.880><c> to</c><00:09:08.120><c> m/</c><00:09:08.760><c> emotron</c>

00:09:09.710 --> 00:09:09.720 align:start position:0%
emoon if you rename yours to m/ emotron
 

00:09:09.720 --> 00:09:11.630 align:start position:0%
emoon if you rename yours to m/ emotron
you<00:09:09.839><c> won't</c><00:09:10.040><c> be</c><00:09:10.160><c> able</c><00:09:10.320><c> to</c><00:09:10.440><c> push</c><00:09:10.600><c> it</c><00:09:10.760><c> up</c><00:09:11.200><c> because</c>

00:09:11.630 --> 00:09:11.640 align:start position:0%
you won't be able to push it up because
 

00:09:11.640 --> 00:09:13.030 align:start position:0%
you won't be able to push it up because
you<00:09:11.760><c> don't</c><00:09:12.000><c> have</c><00:09:12.160><c> my</c>

00:09:13.030 --> 00:09:13.040 align:start position:0%
you don't have my
 

00:09:13.040 --> 00:09:17.310 align:start position:0%
you don't have my
key<00:09:14.040><c> I</c><00:09:14.399><c> hope</c><00:09:15.399><c> now</c><00:09:15.640><c> you</c><00:09:15.720><c> can</c><00:09:15.920><c> push</c><00:09:16.120><c> the</c><00:09:16.200><c> model</c><00:09:16.680><c> ol</c>

00:09:17.310 --> 00:09:17.320 align:start position:0%
key I hope now you can push the model ol
 

00:09:17.320 --> 00:09:21.590 align:start position:0%
key I hope now you can push the model ol
push<00:09:17.760><c> m/</c><00:09:18.720><c> Emron</c><00:09:19.720><c> and</c><00:09:19.839><c> the</c><00:09:20.040><c> model</c><00:09:20.440><c> will</c><00:09:20.640><c> upload</c>

00:09:21.590 --> 00:09:21.600 align:start position:0%
push m/ Emron and the model will upload
 

00:09:21.600 --> 00:09:23.630 align:start position:0%
push m/ Emron and the model will upload
this<00:09:21.839><c> takes</c><00:09:22.640><c> well</c><00:09:23.000><c> however</c><00:09:23.279><c> long</c><00:09:23.480><c> your</c>

00:09:23.630 --> 00:09:23.640 align:start position:0%
this takes well however long your
 

00:09:23.640 --> 00:09:25.870 align:start position:0%
this takes well however long your
internet<00:09:24.000><c> connection</c><00:09:24.440><c> takes</c><00:09:25.399><c> once</c><00:09:25.640><c> that's</c>

00:09:25.870 --> 00:09:25.880 align:start position:0%
internet connection takes once that's
 

00:09:25.880 --> 00:09:27.310 align:start position:0%
internet connection takes once that's
done<00:09:26.320><c> you</c><00:09:26.440><c> should</c><00:09:26.760><c> edit</c><00:09:26.959><c> the</c><00:09:27.079><c> short</c>

00:09:27.310 --> 00:09:27.320 align:start position:0%
done you should edit the short
 

00:09:27.320 --> 00:09:29.430 align:start position:0%
done you should edit the short
description<00:09:27.720><c> for</c><00:09:27.880><c> the</c><00:09:28.000><c> model</c><00:09:28.760><c> and</c><00:09:28.880><c> then</c><00:09:29.200><c> edit</c>

00:09:29.430 --> 00:09:29.440 align:start position:0%
description for the model and then edit
 

00:09:29.440 --> 00:09:31.509 align:start position:0%
description for the model and then edit
the<00:09:29.560><c> longer</c><00:09:29.880><c> description</c><00:09:30.279><c> for</c><00:09:30.399><c> the</c><00:09:30.480><c> model</c><00:09:31.360><c> now</c>

00:09:31.509 --> 00:09:31.519 align:start position:0%
the longer description for the model now
 

00:09:31.519 --> 00:09:33.030 align:start position:0%
the longer description for the model now
share<00:09:31.839><c> with</c><00:09:31.959><c> the</c><00:09:32.079><c> world</c><00:09:32.360><c> your</c><00:09:32.560><c> incredible</c>

00:09:33.030 --> 00:09:33.040 align:start position:0%
share with the world your incredible
 

00:09:33.040 --> 00:09:35.670 align:start position:0%
share with the world your incredible
achievement<00:09:33.839><c> win</c><00:09:34.200><c> incredible</c><00:09:34.720><c> glory</c><00:09:35.440><c> and</c>

00:09:35.670 --> 00:09:35.680 align:start position:0%
achievement win incredible glory and
 

00:09:35.680 --> 00:09:38.069 align:start position:0%
achievement win incredible glory and
retire<00:09:36.120><c> tomorrow</c><00:09:36.880><c> with</c><00:09:37.200><c> your</c><00:09:37.360><c> share</c><00:09:37.839><c> of</c><00:09:37.959><c> the</c>

00:09:38.069 --> 00:09:38.079 align:start position:0%
retire tomorrow with your share of the
 

00:09:38.079 --> 00:09:40.630 align:start position:0%
retire tomorrow with your share of the
world's<00:09:38.399><c> money</c><00:09:39.120><c> simple</c><00:09:40.120><c> let</c><00:09:40.240><c> me</c><00:09:40.399><c> know</c><00:09:40.519><c> if</c>

00:09:40.630 --> 00:09:40.640 align:start position:0%
world's money simple let me know if
 

00:09:40.640 --> 00:09:41.949 align:start position:0%
world's money simple let me know if
there's<00:09:40.880><c> anything</c><00:09:41.160><c> else</c><00:09:41.360><c> you'd</c><00:09:41.560><c> like</c><00:09:41.720><c> me</c><00:09:41.800><c> to</c>

00:09:41.949 --> 00:09:41.959 align:start position:0%
there's anything else you'd like me to
 

00:09:41.959 --> 00:09:45.190 align:start position:0%
there's anything else you'd like me to
cover<00:09:42.320><c> in</c><00:09:42.480><c> these</c><00:09:42.640><c> videos</c><00:09:43.640><c> I</c><00:09:44.120><c> love</c><00:09:44.560><c> making</c><00:09:44.920><c> them</c>

00:09:45.190 --> 00:09:45.200 align:start position:0%
cover in these videos I love making them
 

00:09:45.200 --> 00:09:47.230 align:start position:0%
cover in these videos I love making them
and<00:09:45.360><c> I</c><00:09:45.519><c> can't</c><00:09:45.839><c> wait</c><00:09:46.399><c> to</c><00:09:46.560><c> see</c><00:09:46.720><c> your</c><00:09:46.880><c> comments</c>

00:09:47.230 --> 00:09:47.240 align:start position:0%
and I can't wait to see your comments
 

00:09:47.240 --> 00:09:49.470 align:start position:0%
and I can't wait to see your comments
down<00:09:47.440><c> below</c><00:09:48.079><c> pointing</c><00:09:48.399><c> me</c><00:09:48.560><c> to</c><00:09:48.839><c> your</c><00:09:49.240><c> new</c>

00:09:49.470 --> 00:09:49.480 align:start position:0%
down below pointing me to your new
 

00:09:49.480 --> 00:09:58.110 align:start position:0%
down below pointing me to your new
models<00:09:50.399><c> thanks</c><00:09:50.640><c> so</c><00:09:50.800><c> much</c><00:09:50.959><c> for</c><00:09:51.160><c> watching</c>

00:09:58.110 --> 00:09:58.120 align:start position:0%
 
 

00:09:58.120 --> 00:10:10.030 align:start position:0%
 
goodbye

00:10:10.030 --> 00:10:10.040 align:start position:0%
 
 

00:10:10.040 --> 00:10:13.040 align:start position:0%
 
for

