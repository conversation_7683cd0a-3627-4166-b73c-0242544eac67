WEBVTT
Kind: captions
Language: en

00:00:00.480 --> 00:00:03.350 align:start position:0%
 
Hey<00:00:00.719><c> there.</c><00:00:01.199><c> Ever</c><00:00:01.520><c> set</c><00:00:01.760><c> up</c><00:00:01.920><c> a</c><00:00:02.240><c> fancy</c><00:00:02.720><c> N8N</c>

00:00:03.350 --> 00:00:03.360 align:start position:0%
Hey there. Ever set up a fancy N8N
 

00:00:03.360 --> 00:00:06.150 align:start position:0%
Hey there. Ever set up a fancy N8N
workflow<00:00:03.840><c> on</c><00:00:04.160><c> a</c><00:00:04.400><c> remote</c><00:00:04.720><c> server</c><00:00:05.600><c> only</c><00:00:05.920><c> to</c>

00:00:06.150 --> 00:00:06.160 align:start position:0%
workflow on a remote server only to
 

00:00:06.160 --> 00:00:09.669 align:start position:0%
workflow on a remote server only to
realize<00:00:06.640><c> you</c><00:00:06.960><c> can't</c><00:00:07.440><c> interact</c><00:00:07.919><c> with</c><00:00:08.320><c> files</c><00:00:09.280><c> or</c>

00:00:09.669 --> 00:00:09.679 align:start position:0%
realize you can't interact with files or
 

00:00:09.679 --> 00:00:12.150 align:start position:0%
realize you can't interact with files or
apps<00:00:10.080><c> on</c><00:00:10.320><c> your</c><00:00:10.559><c> local</c><00:00:10.880><c> machine?</c><00:00:11.519><c> It's</c><00:00:11.840><c> like</c>

00:00:12.150 --> 00:00:12.160 align:start position:0%
apps on your local machine? It's like
 

00:00:12.160 --> 00:00:14.549 align:start position:0%
apps on your local machine? It's like
building<00:00:12.480><c> a</c><00:00:12.719><c> bridge</c><00:00:13.200><c> that</c><00:00:13.440><c> stops</c><00:00:13.920><c> right</c><00:00:14.240><c> at</c>

00:00:14.549 --> 00:00:14.559 align:start position:0%
building a bridge that stops right at
 

00:00:14.559 --> 00:00:16.710 align:start position:0%
building a bridge that stops right at
the<00:00:14.799><c> river's</c><00:00:15.200><c> edge.</c><00:00:15.839><c> You've</c><00:00:16.160><c> got</c><00:00:16.240><c> all</c><00:00:16.480><c> this</c>

00:00:16.710 --> 00:00:16.720 align:start position:0%
the river's edge. You've got all this
 

00:00:16.720 --> 00:00:18.630 align:start position:0%
the river's edge. You've got all this
automation<00:00:17.279><c> power,</c><00:00:17.680><c> but</c><00:00:17.920><c> you</c><00:00:18.160><c> can't</c><00:00:18.400><c> touch</c>

00:00:18.630 --> 00:00:18.640 align:start position:0%
automation power, but you can't touch
 

00:00:18.640 --> 00:00:21.550 align:start position:0%
automation power, but you can't touch
your<00:00:19.439><c> Obsidian</c><00:00:20.080><c> notes</c><00:00:20.400><c> or</c><00:00:20.640><c> run</c><00:00:20.960><c> local</c>

00:00:21.550 --> 00:00:21.560 align:start position:0%
your Obsidian notes or run local
 

00:00:21.560 --> 00:00:24.790 align:start position:0%
your Obsidian notes or run local
scripts.<00:00:22.560><c> Super</c><00:00:23.119><c> frustrating,</c><00:00:23.840><c> right?</c><00:00:24.640><c> This</c>

00:00:24.790 --> 00:00:24.800 align:start position:0%
scripts. Super frustrating, right? This
 

00:00:24.800 --> 00:00:27.349 align:start position:0%
scripts. Super frustrating, right? This
is<00:00:24.960><c> why</c><00:00:25.119><c> I</c><00:00:25.359><c> was</c><00:00:25.519><c> running</c><00:00:25.760><c> N8N</c><00:00:26.560><c> on</c><00:00:26.800><c> my</c><00:00:26.960><c> local</c>

00:00:27.349 --> 00:00:27.359 align:start position:0%
is why I was running N8N on my local
 

00:00:27.359 --> 00:00:29.429 align:start position:0%
is why I was running N8N on my local
machine<00:00:27.680><c> as</c><00:00:27.920><c> well,</c><00:00:28.480><c> so</c><00:00:28.640><c> I</c><00:00:28.880><c> could</c><00:00:29.039><c> have</c><00:00:29.199><c> the</c>

00:00:29.429 --> 00:00:29.439 align:start position:0%
machine as well, so I could have the
 

00:00:29.439 --> 00:00:31.990 align:start position:0%
machine as well, so I could have the
automations<00:00:30.160><c> that</c><00:00:30.400><c> worked</c><00:00:30.960><c> in</c><00:00:31.359><c> all</c><00:00:31.679><c> the</c>

00:00:31.990 --> 00:00:32.000 align:start position:0%
automations that worked in all the
 

00:00:32.000 --> 00:00:34.790 align:start position:0%
automations that worked in all the
places<00:00:32.399><c> I</c><00:00:32.640><c> needed.</c><00:00:33.600><c> Now,</c><00:00:33.760><c> in</c><00:00:34.000><c> today's</c><00:00:34.399><c> video,</c>

00:00:34.790 --> 00:00:34.800 align:start position:0%
places I needed. Now, in today's video,
 

00:00:34.800 --> 00:00:36.950 align:start position:0%
places I needed. Now, in today's video,
I'm<00:00:35.040><c> going</c><00:00:35.120><c> to</c><00:00:35.280><c> show</c><00:00:35.440><c> you</c><00:00:35.680><c> how</c><00:00:35.920><c> to</c><00:00:36.160><c> solve</c><00:00:36.559><c> this</c>

00:00:36.950 --> 00:00:36.960 align:start position:0%
I'm going to show you how to solve this
 

00:00:36.960 --> 00:00:40.069 align:start position:0%
I'm going to show you how to solve this
exact<00:00:37.559><c> problem</c><00:00:38.559><c> without</c><00:00:39.200><c> needing</c><00:00:39.760><c> two</c>

00:00:40.069 --> 00:00:40.079 align:start position:0%
exact problem without needing two
 

00:00:40.079 --> 00:00:43.190 align:start position:0%
exact problem without needing two
instances<00:00:40.800><c> of</c><00:00:41.320><c> N8N,</c><00:00:42.320><c> which</c><00:00:42.640><c> happens</c><00:00:42.879><c> to</c><00:00:43.040><c> be</c>

00:00:43.190 --> 00:00:43.200 align:start position:0%
instances of N8N, which happens to be
 

00:00:43.200 --> 00:00:45.430 align:start position:0%
instances of N8N, which happens to be
the<00:00:43.360><c> sponsor</c><00:00:43.680><c> of</c><00:00:43.840><c> this</c><00:00:44.079><c> video.</c><00:00:44.800><c> Now,</c><00:00:45.120><c> they</c>

00:00:45.430 --> 00:00:45.440 align:start position:0%
the sponsor of this video. Now, they
 

00:00:45.440 --> 00:00:47.830 align:start position:0%
the sponsor of this video. Now, they
don't<00:00:45.600><c> actually</c><00:00:46.000><c> see</c><00:00:46.239><c> the</c><00:00:46.640><c> script</c><00:00:47.200><c> before</c><00:00:47.600><c> I</c>

00:00:47.830 --> 00:00:47.840 align:start position:0%
don't actually see the script before I
 

00:00:47.840 --> 00:00:51.029 align:start position:0%
don't actually see the script before I
post<00:00:48.079><c> these.</c><00:00:48.719><c> I</c><00:00:48.960><c> just</c><00:00:49.200><c> really</c><00:00:49.600><c> dig</c><00:00:49.920><c> N8N,</c><00:00:50.640><c> and</c><00:00:50.879><c> I</c>

00:00:51.029 --> 00:00:51.039 align:start position:0%
post these. I just really dig N8N, and I
 

00:00:51.039 --> 00:00:53.750 align:start position:0%
post these. I just really dig N8N, and I
got<00:00:51.280><c> lucky</c><00:00:51.600><c> on</c><00:00:51.920><c> a</c><00:00:52.160><c> sponsorship.</c><00:00:53.039><c> So,</c><00:00:53.440><c> thanks</c>

00:00:53.750 --> 00:00:53.760 align:start position:0%
got lucky on a sponsorship. So, thanks
 

00:00:53.760 --> 00:00:56.790 align:start position:0%
got lucky on a sponsorship. So, thanks
again<00:00:54.079><c> to</c><00:00:54.480><c> N8N</c><00:00:55.120><c> for</c><00:00:55.360><c> sponsoring</c><00:00:55.760><c> this</c><00:00:56.079><c> video.</c>

00:00:56.790 --> 00:00:56.800 align:start position:0%
again to N8N for sponsoring this video.
 

00:00:56.800 --> 00:00:59.029 align:start position:0%
again to N8N for sponsoring this video.
Now,<00:00:57.039><c> I'm</c><00:00:57.280><c> using</c><00:00:57.520><c> a</c><00:00:57.760><c> neat</c><00:00:58.079><c> tool</c><00:00:58.399><c> called</c><00:00:58.719><c> Web</c>

00:00:59.029 --> 00:00:59.039 align:start position:0%
Now, I'm using a neat tool called Web
 

00:00:59.039 --> 00:01:02.630 align:start position:0%
Now, I'm using a neat tool called Web
Hook.<00:00:59.600><c> Yeah,</c><00:01:00.000><c> not</c><00:01:00.399><c> a</c><00:01:00.719><c> great</c><00:01:01.199><c> name.</c><00:01:02.079><c> Reminds</c><00:01:02.480><c> me</c>

00:01:02.630 --> 00:01:02.640 align:start position:0%
Hook. Yeah, not a great name. Reminds me
 

00:01:02.640 --> 00:01:05.750 align:start position:0%
Hook. Yeah, not a great name. Reminds me
when<00:01:02.960><c> the</c><00:01:03.199><c> company</c><00:01:03.440><c> I</c><00:01:03.680><c> worked</c><00:01:03.920><c> for</c><00:01:04.400><c> years</c><00:01:04.799><c> ago</c>

00:01:05.750 --> 00:01:05.760 align:start position:0%
when the company I worked for years ago
 

00:01:05.760 --> 00:01:08.310 align:start position:0%
when the company I worked for years ago
renamed<00:01:06.240><c> the</c><00:01:06.479><c> product</c><00:01:06.880><c> that</c><00:01:07.280><c> I</c><00:01:07.680><c> focused</c><00:01:08.000><c> on</c>

00:01:08.310 --> 00:01:08.320 align:start position:0%
renamed the product that I focused on
 

00:01:08.320 --> 00:01:13.630 align:start position:0%
renamed the product that I focused on
from<00:01:09.040><c> Team</c><00:01:09.479><c> Plate,</c><00:01:10.479><c> a</c><00:01:10.720><c> searchable</c><00:01:11.200><c> name,</c><00:01:12.159><c> to</c>

00:01:13.630 --> 00:01:13.640 align:start position:0%
from Team Plate, a searchable name, to
 

00:01:13.640 --> 00:01:17.749 align:start position:0%
from Team Plate, a searchable name, to
workflow,<00:01:14.640><c> a</c><00:01:15.040><c> less</c><00:01:15.760><c> searchable</c><00:01:16.560><c> name.</c><00:01:17.119><c> Before</c>

00:01:17.749 --> 00:01:17.759 align:start position:0%
workflow, a less searchable name. Before
 

00:01:17.759 --> 00:01:19.910 align:start position:0%
workflow, a less searchable name. Before
we<00:01:18.000><c> dive</c><00:01:18.320><c> deeper,</c><00:01:18.799><c> if</c><00:01:18.960><c> you're</c><00:01:19.200><c> getting</c><00:01:19.520><c> value</c>

00:01:19.910 --> 00:01:19.920 align:start position:0%
we dive deeper, if you're getting value
 

00:01:19.920 --> 00:01:22.230 align:start position:0%
we dive deeper, if you're getting value
from<00:01:20.080><c> these</c><00:01:20.479><c> automation</c><00:01:21.040><c> tutorials,</c><00:01:21.920><c> hit</c>

00:01:22.230 --> 00:01:22.240 align:start position:0%
from these automation tutorials, hit
 

00:01:22.240 --> 00:01:24.070 align:start position:0%
from these automation tutorials, hit
like<00:01:22.479><c> on</c><00:01:22.640><c> this</c><00:01:22.880><c> video,</c><00:01:23.200><c> then</c><00:01:23.439><c> click</c><00:01:23.600><c> on</c><00:01:23.840><c> that</c>

00:01:24.070 --> 00:01:24.080 align:start position:0%
like on this video, then click on that
 

00:01:24.080 --> 00:01:26.870 align:start position:0%
like on this video, then click on that
subscribe<00:01:24.640><c> button</c><00:01:25.200><c> and</c><00:01:25.520><c> notification</c><00:01:26.159><c> bell.</c>

00:01:26.870 --> 00:01:26.880 align:start position:0%
subscribe button and notification bell.
 

00:01:26.880 --> 00:01:29.190 align:start position:0%
subscribe button and notification bell.
I<00:01:27.200><c> have</c><00:01:27.439><c> so</c><00:01:27.680><c> much</c><00:01:27.920><c> more</c><00:01:28.240><c> I</c><00:01:28.479><c> want</c><00:01:28.640><c> to</c><00:01:28.799><c> show</c><00:01:28.960><c> you</c>

00:01:29.190 --> 00:01:29.200 align:start position:0%
I have so much more I want to show you
 

00:01:29.200 --> 00:01:31.670 align:start position:0%
I have so much more I want to show you
about<00:01:29.520><c> working</c><00:01:29.759><c> with</c><00:01:30.080><c> N8N</c><00:01:30.880><c> in</c><00:01:31.280><c> interesting</c>

00:01:31.670 --> 00:01:31.680 align:start position:0%
about working with N8N in interesting
 

00:01:31.680 --> 00:01:35.630 align:start position:0%
about working with N8N in interesting
ways,<00:01:32.240><c> often</c><00:01:32.799><c> incorporating</c><00:01:33.680><c> local</c><00:01:34.079><c> AI</c><00:01:35.040><c> like</c>

00:01:35.630 --> 00:01:35.640 align:start position:0%
ways, often incorporating local AI like
 

00:01:35.640 --> 00:01:37.830 align:start position:0%
ways, often incorporating local AI like
Olama.<00:01:36.640><c> You</c><00:01:36.880><c> don't</c><00:01:37.040><c> want</c><00:01:37.119><c> to</c><00:01:37.280><c> miss</c><00:01:37.520><c> what's</c>

00:01:37.830 --> 00:01:37.840 align:start position:0%
Olama. You don't want to miss what's
 

00:01:37.840 --> 00:01:40.230 align:start position:0%
Olama. You don't want to miss what's
coming<00:01:38.119><c> next.</c><00:01:39.119><c> This</c><00:01:39.439><c> version</c><00:01:39.680><c> of</c><00:01:39.840><c> the</c><00:01:40.000><c> video</c>

00:01:40.230 --> 00:01:40.240 align:start position:0%
coming next. This version of the video
 

00:01:40.240 --> 00:01:42.710 align:start position:0%
coming next. This version of the video
is<00:01:40.400><c> the</c><00:01:40.799><c> public</c><00:01:41.200><c> release.</c><00:01:41.759><c> It's</c><00:01:42.079><c> shorter</c><00:01:42.479><c> and</c>

00:01:42.710 --> 00:01:42.720 align:start position:0%
is the public release. It's shorter and
 

00:01:42.720 --> 00:01:44.390 align:start position:0%
is the public release. It's shorter and
gets<00:01:43.040><c> straight</c><00:01:43.280><c> to</c><00:01:43.520><c> the</c><00:01:43.680><c> point</c><00:01:43.920><c> compared</c><00:01:44.240><c> to</c>

00:01:44.390 --> 00:01:44.400 align:start position:0%
gets straight to the point compared to
 

00:01:44.400 --> 00:01:46.230 align:start position:0%
gets straight to the point compared to
what<00:01:44.640><c> I</c><00:01:44.799><c> had</c><00:01:45.040><c> originally</c><00:01:45.439><c> planned</c><00:01:45.759><c> for</c><00:01:46.000><c> the</c>

00:01:46.230 --> 00:01:46.240 align:start position:0%
what I had originally planned for the
 

00:01:46.240 --> 00:01:48.630 align:start position:0%
what I had originally planned for the
video.<00:01:46.960><c> That</c><00:01:47.280><c> original</c><00:01:47.680><c> version</c><00:01:48.399><c> was</c>

00:01:48.630 --> 00:01:48.640 align:start position:0%
video. That original version was
 

00:01:48.640 --> 00:01:50.710 align:start position:0%
video. That original version was
released<00:01:48.960><c> to</c><00:01:49.200><c> my</c><00:01:49.360><c> Patreon</c><00:01:50.000><c> and</c><00:01:50.320><c> YouTube</c>

00:01:50.710 --> 00:01:50.720 align:start position:0%
released to my Patreon and YouTube
 

00:01:50.720 --> 00:01:53.190 align:start position:0%
released to my Patreon and YouTube
members<00:01:51.119><c> a</c><00:01:51.439><c> few</c><00:01:51.680><c> days</c><00:01:51.840><c> ago.</c><00:01:52.640><c> If</c><00:01:52.880><c> you</c><00:01:52.960><c> want</c><00:01:53.119><c> to</c>

00:01:53.190 --> 00:01:53.200 align:start position:0%
members a few days ago. If you want to
 

00:01:53.200 --> 00:01:56.149 align:start position:0%
members a few days ago. If you want to
see<00:01:53.360><c> that</c><00:01:53.600><c> longer</c><00:01:54.000><c> video,</c><00:01:54.399><c> which</c><00:01:54.720><c> includes</c><00:01:55.759><c> uh</c>

00:01:56.149 --> 00:01:56.159 align:start position:0%
see that longer video, which includes uh
 

00:01:56.159 --> 00:01:59.190 align:start position:0%
see that longer video, which includes uh
extra<00:01:56.560><c> examples,</c><00:01:57.360><c> extra</c><00:01:57.759><c> explanations,</c><00:01:58.560><c> then</c>

00:01:59.190 --> 00:01:59.200 align:start position:0%
extra examples, extra explanations, then
 

00:01:59.200 --> 00:02:01.670 align:start position:0%
extra examples, extra explanations, then
consider<00:01:59.759><c> becoming</c><00:02:00.320><c> a</c><00:02:00.560><c> member.</c><00:02:01.200><c> It</c><00:02:01.439><c> doesn't</c>

00:02:01.670 --> 00:02:01.680 align:start position:0%
consider becoming a member. It doesn't
 

00:02:01.680 --> 00:02:04.389 align:start position:0%
consider becoming a member. It doesn't
cost<00:02:02.000><c> much</c><00:02:02.560><c> and</c><00:02:02.799><c> it</c><00:02:03.119><c> really</c><00:02:03.520><c> helps</c><00:02:03.759><c> me</c><00:02:04.000><c> do</c><00:02:04.240><c> what</c>

00:02:04.389 --> 00:02:04.399 align:start position:0%
cost much and it really helps me do what
 

00:02:04.399 --> 00:02:05.990 align:start position:0%
cost much and it really helps me do what
I<00:02:04.640><c> do.</c>

00:02:05.990 --> 00:02:06.000 align:start position:0%
I do.
 

00:02:06.000 --> 00:02:09.430 align:start position:0%
I do.
Web<00:02:06.320><c> Hook</c><00:02:06.719><c> by</c><00:02:07.040><c> Adnan</c><00:02:07.959><c> Hajarvich</c><00:02:08.959><c> is</c><00:02:09.200><c> an</c>

00:02:09.430 --> 00:02:09.440 align:start position:0%
Web Hook by Adnan Hajarvich is an
 

00:02:09.440 --> 00:02:11.589 align:start position:0%
Web Hook by Adnan Hajarvich is an
open-source<00:02:10.239><c> project</c><00:02:10.640><c> that</c><00:02:10.959><c> creates</c><00:02:11.280><c> a</c>

00:02:11.589 --> 00:02:11.599 align:start position:0%
open-source project that creates a
 

00:02:11.599 --> 00:02:14.309 align:start position:0%
open-source project that creates a
simple<00:02:12.319><c> but</c><00:02:12.800><c> specialized</c><00:02:13.520><c> web</c><00:02:13.760><c> server</c><00:02:14.080><c> on</c>

00:02:14.309 --> 00:02:14.319 align:start position:0%
simple but specialized web server on
 

00:02:14.319 --> 00:02:16.550 align:start position:0%
simple but specialized web server on
your<00:02:14.480><c> local</c><00:02:14.800><c> machine.</c><00:02:15.760><c> This</c><00:02:16.000><c> server</c><00:02:16.400><c> can</c>

00:02:16.550 --> 00:02:16.560 align:start position:0%
your local machine. This server can
 

00:02:16.560 --> 00:02:19.350 align:start position:0%
your local machine. This server can
listen<00:02:16.959><c> for</c><00:02:17.360><c> HTTP</c><00:02:18.000><c> requests</c><00:02:18.560><c> optionally</c><00:02:19.040><c> with</c>

00:02:19.350 --> 00:02:19.360 align:start position:0%
listen for HTTP requests optionally with
 

00:02:19.360 --> 00:02:21.910 align:start position:0%
listen for HTTP requests optionally with
authentication<00:02:20.080><c> options</c><00:02:20.480><c> and</c><00:02:20.720><c> more</c><00:02:21.520><c> and</c><00:02:21.680><c> then</c>

00:02:21.910 --> 00:02:21.920 align:start position:0%
authentication options and more and then
 

00:02:21.920 --> 00:02:23.910 align:start position:0%
authentication options and more and then
run<00:02:22.160><c> local</c><00:02:22.560><c> scripts</c><00:02:22.959><c> or</c><00:02:23.200><c> commands</c><00:02:23.599><c> when</c><00:02:23.760><c> it</c>

00:02:23.910 --> 00:02:23.920 align:start position:0%
run local scripts or commands when it
 

00:02:23.920 --> 00:02:26.470 align:start position:0%
run local scripts or commands when it
receives<00:02:24.319><c> them.</c><00:02:24.879><c> It</c><00:02:25.120><c> works</c><00:02:25.360><c> on</c><00:02:25.599><c> Windows</c><00:02:26.239><c> or</c>

00:02:26.470 --> 00:02:26.480 align:start position:0%
receives them. It works on Windows or
 

00:02:26.480 --> 00:02:29.350 align:start position:0%
receives them. It works on Windows or
Linux<00:02:26.959><c> or</c><00:02:27.280><c> Mac,</c><00:02:28.000><c> which</c><00:02:28.239><c> means</c><00:02:28.560><c> pretty</c><00:02:28.879><c> much</c>

00:02:29.350 --> 00:02:29.360 align:start position:0%
Linux or Mac, which means pretty much
 

00:02:29.360 --> 00:02:33.030 align:start position:0%
Linux or Mac, which means pretty much
everyone<00:02:30.000><c> watching</c><00:02:30.400><c> this</c><00:02:30.720><c> video</c><00:02:31.360><c> can</c><00:02:31.599><c> use</c><00:02:31.760><c> it.</c>

00:02:33.030 --> 00:02:33.040 align:start position:0%
everyone watching this video can use it.
 

00:02:33.040 --> 00:02:34.949 align:start position:0%
everyone watching this video can use it.
So<00:02:33.280><c> to</c><00:02:33.519><c> get</c><00:02:33.760><c> started,</c><00:02:34.319><c> you'll</c><00:02:34.640><c> need</c><00:02:34.800><c> to</c>

00:02:34.949 --> 00:02:34.959 align:start position:0%
So to get started, you'll need to
 

00:02:34.959 --> 00:02:37.589 align:start position:0%
So to get started, you'll need to
install<00:02:35.440><c> web</c><00:02:35.680><c> hook.</c><00:02:36.480><c> You</c><00:02:36.720><c> can</c><00:02:36.879><c> grab</c><00:02:37.120><c> it</c><00:02:37.280><c> from</c>

00:02:37.589 --> 00:02:37.599 align:start position:0%
install web hook. You can grab it from
 

00:02:37.599 --> 00:02:40.309 align:start position:0%
install web hook. You can grab it from
GitHub<00:02:38.000><c> by</c><00:02:38.239><c> cloning</c><00:02:38.640><c> the</c><00:02:38.800><c> repo</c><00:02:39.280><c> and</c><00:02:39.760><c> compiling</c>

00:02:40.309 --> 00:02:40.319 align:start position:0%
GitHub by cloning the repo and compiling
 

00:02:40.319 --> 00:02:43.589 align:start position:0%
GitHub by cloning the repo and compiling
it<00:02:40.560><c> using</c><00:02:41.239><c> Go.</c><00:02:42.239><c> Alternatively,</c><00:02:43.200><c> it's</c>

00:02:43.589 --> 00:02:43.599 align:start position:0%
it using Go. Alternatively, it's
 

00:02:43.599 --> 00:02:45.190 align:start position:0%
it using Go. Alternatively, it's
available<00:02:44.000><c> through</c><00:02:44.319><c> package</c><00:02:44.640><c> managers</c><00:02:45.040><c> like</c>

00:02:45.190 --> 00:02:45.200 align:start position:0%
available through package managers like
 

00:02:45.200 --> 00:02:47.430 align:start position:0%
available through package managers like
Appcat<00:02:45.760><c> on</c><00:02:45.920><c> Linux,</c><00:02:46.560><c> or</c><00:02:46.800><c> you</c><00:02:46.959><c> can</c><00:02:47.120><c> download</c>

00:02:47.430 --> 00:02:47.440 align:start position:0%
Appcat on Linux, or you can download
 

00:02:47.440 --> 00:02:50.309 align:start position:0%
Appcat on Linux, or you can download
pre-ompiled<00:02:48.160><c> binaries</c><00:02:48.800><c> for</c><00:02:49.280><c> most</c><00:02:49.599><c> platforms</c>

00:02:50.309 --> 00:02:50.319 align:start position:0%
pre-ompiled binaries for most platforms
 

00:02:50.319 --> 00:02:52.550 align:start position:0%
pre-ompiled binaries for most platforms
from<00:02:50.560><c> the</c><00:02:50.800><c> releases</c><00:02:51.200><c> page,</c><00:02:51.680><c> though</c><00:02:52.080><c> Windows</c>

00:02:52.550 --> 00:02:52.560 align:start position:0%
from the releases page, though Windows
 

00:02:52.560 --> 00:02:54.869 align:start position:0%
from the releases page, though Windows
users<00:02:53.040><c> have</c><00:02:53.200><c> to</c><00:02:53.440><c> compile</c><00:02:53.840><c> it.</c><00:02:54.400><c> I</c><00:02:54.640><c> don't</c><00:02:54.720><c> know</c>

00:02:54.869 --> 00:02:54.879 align:start position:0%
users have to compile it. I don't know
 

00:02:54.879 --> 00:02:57.350 align:start position:0%
users have to compile it. I don't know
why.<00:02:55.760><c> I've</c><00:02:56.080><c> included</c><00:02:56.480><c> links</c><00:02:56.800><c> to</c><00:02:57.120><c> everything</c>

00:02:57.350 --> 00:02:57.360 align:start position:0%
why. I've included links to everything
 

00:02:57.360 --> 00:02:59.830 align:start position:0%
why. I've included links to everything
in<00:02:57.519><c> the</c><00:02:57.680><c> GitHub</c><00:02:58.080><c> repo</c><00:02:58.480><c> linked</c><00:02:59.040><c> to</c><00:02:59.280><c> in</c><00:02:59.599><c> the</c>

00:02:59.830 --> 00:02:59.840 align:start position:0%
in the GitHub repo linked to in the
 

00:02:59.840 --> 00:03:02.550 align:start position:0%
in the GitHub repo linked to in the
description<00:03:00.440><c> below.</c><00:03:01.519><c> Once</c><00:03:01.840><c> installed,</c>

00:03:02.550 --> 00:03:02.560 align:start position:0%
description below. Once installed,
 

00:03:02.560 --> 00:03:04.630 align:start position:0%
description below. Once installed,
you'll<00:03:02.800><c> want</c><00:03:02.959><c> to</c><00:03:03.120><c> set</c><00:03:03.280><c> it</c><00:03:03.440><c> up</c><00:03:03.599><c> as</c><00:03:03.760><c> a</c><00:03:04.000><c> service</c><00:03:04.480><c> so</c>

00:03:04.630 --> 00:03:04.640 align:start position:0%
you'll want to set it up as a service so
 

00:03:04.640 --> 00:03:06.869 align:start position:0%
you'll want to set it up as a service so
it<00:03:04.800><c> runs</c><00:03:05.040><c> in</c><00:03:05.200><c> the</c><00:03:05.360><c> background.</c><00:03:06.239><c> For</c><00:03:06.560><c> Mac</c>

00:03:06.869 --> 00:03:06.879 align:start position:0%
it runs in the background. For Mac
 

00:03:06.879 --> 00:03:09.270 align:start position:0%
it runs in the background. For Mac
users,<00:03:07.440><c> I've</c><00:03:07.680><c> created</c><00:03:07.920><c> a</c><00:03:08.159><c> launchctl</c><00:03:08.959><c> file</c>

00:03:09.270 --> 00:03:09.280 align:start position:0%
users, I've created a launchctl file
 

00:03:09.280 --> 00:03:12.070 align:start position:0%
users, I've created a launchctl file
that<00:03:09.680><c> you</c><00:03:09.920><c> can</c><00:03:10.080><c> use.</c><00:03:10.800><c> It'll</c><00:03:11.200><c> be</c><00:03:11.360><c> in</c><00:03:11.519><c> the</c><00:03:11.680><c> GitHub</c>

00:03:12.070 --> 00:03:12.080 align:start position:0%
that you can use. It'll be in the GitHub
 

00:03:12.080 --> 00:03:15.110 align:start position:0%
that you can use. It'll be in the GitHub
repo<00:03:12.560><c> for</c><00:03:12.800><c> this</c><00:03:13.080><c> video.</c><00:03:14.080><c> For</c><00:03:14.560><c> Windows</c><00:03:14.879><c> and</c>

00:03:15.110 --> 00:03:15.120 align:start position:0%
repo for this video. For Windows and
 

00:03:15.120 --> 00:03:16.710 align:start position:0%
repo for this video. For Windows and
Linux,<00:03:15.440><c> you'll</c><00:03:15.760><c> need</c><00:03:15.840><c> to</c><00:03:16.000><c> figure</c><00:03:16.239><c> out</c><00:03:16.480><c> the</c>

00:03:16.710 --> 00:03:16.720 align:start position:0%
Linux, you'll need to figure out the
 

00:03:16.720 --> 00:03:18.630 align:start position:0%
Linux, you'll need to figure out the
right<00:03:16.959><c> way</c><00:03:17.120><c> to</c><00:03:17.440><c> set</c><00:03:17.599><c> up</c><00:03:17.760><c> a</c><00:03:18.000><c> service</c><00:03:18.239><c> or</c>

00:03:18.630 --> 00:03:18.640 align:start position:0%
right way to set up a service or
 

00:03:18.640 --> 00:03:21.990 align:start position:0%
right way to set up a service or
background<00:03:19.040><c> job</c><00:03:19.440><c> for</c><00:03:19.840><c> your</c><00:03:20.360><c> OS.</c><00:03:21.360><c> But</c><00:03:21.519><c> for</c><00:03:21.760><c> now,</c>

00:03:21.990 --> 00:03:22.000 align:start position:0%
background job for your OS. But for now,
 

00:03:22.000 --> 00:03:23.750 align:start position:0%
background job for your OS. But for now,
you<00:03:22.159><c> can</c><00:03:22.239><c> just</c><00:03:22.400><c> run</c><00:03:22.640><c> on</c><00:03:22.800><c> the</c><00:03:22.959><c> command</c><00:03:23.200><c> line.</c>

00:03:23.750 --> 00:03:23.760 align:start position:0%
you can just run on the command line.
 

00:03:23.760 --> 00:03:25.990 align:start position:0%
you can just run on the command line.
But<00:03:23.920><c> first,</c><00:03:24.159><c> you</c><00:03:24.400><c> need</c><00:03:24.560><c> to</c><00:03:24.720><c> create</c><00:03:25.040><c> a</c><00:03:25.280><c> file.</c>

00:03:25.990 --> 00:03:26.000 align:start position:0%
But first, you need to create a file.
 

00:03:26.000 --> 00:03:29.670 align:start position:0%
But first, you need to create a file.
That<00:03:26.319><c> file</c><00:03:26.800><c> is</c><00:03:26.959><c> a</c><00:03:27.560><c> hooks.json</c><00:03:28.560><c> file.</c><00:03:29.280><c> This</c><00:03:29.519><c> is</c>

00:03:29.670 --> 00:03:29.680 align:start position:0%
That file is a hooks.json file. This is
 

00:03:29.680 --> 00:03:32.070 align:start position:0%
That file is a hooks.json file. This is
where<00:03:29.840><c> you</c><00:03:30.080><c> define</c><00:03:30.480><c> what</c><00:03:30.799><c> happens</c><00:03:31.519><c> when</c><00:03:31.840><c> your</c>

00:03:32.070 --> 00:03:32.080 align:start position:0%
where you define what happens when your
 

00:03:32.080 --> 00:03:34.710 align:start position:0%
where you define what happens when your
web<00:03:32.239><c> hook</c><00:03:32.640><c> receives</c><00:03:32.959><c> a</c><00:03:33.280><c> request.</c><00:03:34.239><c> Let</c><00:03:34.400><c> me</c><00:03:34.560><c> show</c>

00:03:34.710 --> 00:03:34.720 align:start position:0%
web hook receives a request. Let me show
 

00:03:34.720 --> 00:03:37.190 align:start position:0%
web hook receives a request. Let me show
you<00:03:34.959><c> the</c><00:03:35.200><c> one</c><00:03:35.440><c> I</c><00:03:35.760><c> created.</c><00:03:36.720><c> In</c><00:03:36.959><c> my</c>

00:03:37.190 --> 00:03:37.200 align:start position:0%
you the one I created. In my
 

00:03:37.200 --> 00:03:38.949 align:start position:0%
you the one I created. In my
configuration,<00:03:37.840><c> I've</c><00:03:38.080><c> set</c><00:03:38.239><c> up</c><00:03:38.319><c> a</c><00:03:38.480><c> web</c><00:03:38.640><c> hook</c>

00:03:38.949 --> 00:03:38.959 align:start position:0%
configuration, I've set up a web hook
 

00:03:38.959 --> 00:03:41.910 align:start position:0%
configuration, I've set up a web hook
called<00:03:39.680><c> create</c><00:03:40.080><c> obsidian</c><00:03:40.640><c> file.</c><00:03:41.440><c> When</c><00:03:41.680><c> this</c>

00:03:41.910 --> 00:03:41.920 align:start position:0%
called create obsidian file. When this
 

00:03:41.920 --> 00:03:44.390 align:start position:0%
called create obsidian file. When this
endpoint<00:03:42.480><c> receives</c><00:03:42.879><c> a</c><00:03:43.040><c> post</c><00:03:43.440><c> request,</c><00:03:44.159><c> it</c>

00:03:44.390 --> 00:03:44.400 align:start position:0%
endpoint receives a post request, it
 

00:03:44.400 --> 00:03:46.390 align:start position:0%
endpoint receives a post request, it
runs<00:03:44.640><c> a</c><00:03:44.799><c> shell</c><00:03:45.120><c> script</c><00:03:45.360><c> and</c><00:03:45.599><c> passes</c><00:03:46.000><c> along</c>

00:03:46.390 --> 00:03:46.400 align:start position:0%
runs a shell script and passes along
 

00:03:46.400 --> 00:03:49.350 align:start position:0%
runs a shell script and passes along
four<00:03:46.879><c> parameters</c><00:03:47.360><c> from</c><00:03:47.519><c> the</c><00:03:47.680><c> JSON</c><00:03:48.159><c> body.</c><00:03:49.040><c> File</c>

00:03:49.350 --> 00:03:49.360 align:start position:0%
four parameters from the JSON body. File
 

00:03:49.360 --> 00:03:52.710 align:start position:0%
four parameters from the JSON body. File
name,<00:03:49.920><c> think,</c><00:03:50.640><c> research,</c><00:03:51.280><c> and</c><00:03:51.720><c> citations.</c>

00:03:52.710 --> 00:03:52.720 align:start position:0%
name, think, research, and citations.
 

00:03:52.720 --> 00:03:54.869 align:start position:0%
name, think, research, and citations.
The<00:03:52.959><c> shell</c><00:03:53.280><c> script</c><00:03:53.519><c> itself</c><00:03:54.080><c> is</c><00:03:54.560><c> pretty</c>

00:03:54.869 --> 00:03:54.879 align:start position:0%
The shell script itself is pretty
 

00:03:54.879 --> 00:03:56.390 align:start position:0%
The shell script itself is pretty
straightforward.<00:03:55.680><c> It</c><00:03:55.920><c> takes</c><00:03:56.159><c> those</c>

00:03:56.390 --> 00:03:56.400 align:start position:0%
straightforward. It takes those
 

00:03:56.400 --> 00:03:58.630 align:start position:0%
straightforward. It takes those
arguments,<00:03:57.200><c> checks</c><00:03:57.519><c> if</c><00:03:57.680><c> a</c><00:03:57.920><c> file</c><00:03:58.159><c> with</c><00:03:58.400><c> that</c>

00:03:58.630 --> 00:03:58.640 align:start position:0%
arguments, checks if a file with that
 

00:03:58.640 --> 00:04:00.869 align:start position:0%
arguments, checks if a file with that
name<00:03:58.879><c> already</c><00:03:59.200><c> exists,</c><00:04:00.080><c> adds</c><00:04:00.319><c> a</c><00:04:00.480><c> numbered</c>

00:04:00.869 --> 00:04:00.879 align:start position:0%
name already exists, adds a numbered
 

00:04:00.879 --> 00:04:03.190 align:start position:0%
name already exists, adds a numbered
suffix<00:04:01.280><c> if</c><00:04:01.599><c> needed,</c><00:04:02.159><c> and</c><00:04:02.319><c> then</c><00:04:02.560><c> writes</c><00:04:02.879><c> the</c>

00:04:03.190 --> 00:04:03.200 align:start position:0%
suffix if needed, and then writes the
 

00:04:03.200 --> 00:04:05.030 align:start position:0%
suffix if needed, and then writes the
content<00:04:03.519><c> to</c><00:04:03.760><c> a</c><00:04:03.920><c> markdown</c><00:04:04.400><c> file</c><00:04:04.640><c> in</c><00:04:04.799><c> my</c>

00:04:05.030 --> 00:04:05.040 align:start position:0%
content to a markdown file in my
 

00:04:05.040 --> 00:04:07.830 align:start position:0%
content to a markdown file in my
Obsidian<00:04:05.640><c> vault.</c><00:04:06.640><c> Now,</c><00:04:06.959><c> let's</c><00:04:07.200><c> look</c><00:04:07.360><c> at</c><00:04:07.599><c> how</c>

00:04:07.830 --> 00:04:07.840 align:start position:0%
Obsidian vault. Now, let's look at how
 

00:04:07.840 --> 00:04:10.630 align:start position:0%
Obsidian vault. Now, let's look at how
we<00:04:08.080><c> set</c><00:04:08.319><c> up</c><00:04:08.480><c> the</c><00:04:08.720><c> N8N</c><00:04:09.439><c> workflow.</c><00:04:10.239><c> Remember,</c>

00:04:10.630 --> 00:04:10.640 align:start position:0%
we set up the N8N workflow. Remember,
 

00:04:10.640 --> 00:04:12.949 align:start position:0%
we set up the N8N workflow. Remember,
because<00:04:10.879><c> we're</c><00:04:11.200><c> using</c><00:04:11.519><c> Tailscale</c><00:04:12.480><c> from</c><00:04:12.720><c> the</c>

00:04:12.949 --> 00:04:12.959 align:start position:0%
because we're using Tailscale from the
 

00:04:12.959 --> 00:04:15.429 align:start position:0%
because we're using Tailscale from the
previous<00:04:13.360><c> video,</c><00:04:13.920><c> our</c><00:04:14.159><c> N8N</c><00:04:14.799><c> server</c><00:04:15.120><c> can</c>

00:04:15.429 --> 00:04:15.439 align:start position:0%
previous video, our N8N server can
 

00:04:15.439 --> 00:04:17.030 align:start position:0%
previous video, our N8N server can
already<00:04:15.840><c> communicate</c><00:04:16.320><c> with</c><00:04:16.560><c> our</c><00:04:16.799><c> local</c>

00:04:17.030 --> 00:04:17.040 align:start position:0%
already communicate with our local
 

00:04:17.040 --> 00:04:19.830 align:start position:0%
already communicate with our local
machine<00:04:17.440><c> without</c><00:04:18.320><c> any</c><00:04:18.799><c> complicated</c><00:04:19.519><c> port</c>

00:04:19.830 --> 00:04:19.840 align:start position:0%
machine without any complicated port
 

00:04:19.840 --> 00:04:23.030 align:start position:0%
machine without any complicated port
forwarding<00:04:20.320><c> or</c><00:04:20.639><c> VPN</c><00:04:21.120><c> setup.</c><00:04:22.079><c> If</c><00:04:22.400><c> you</c><00:04:22.720><c> haven't</c>

00:04:23.030 --> 00:04:23.040 align:start position:0%
forwarding or VPN setup. If you haven't
 

00:04:23.040 --> 00:04:25.150 align:start position:0%
forwarding or VPN setup. If you haven't
watched<00:04:23.360><c> that</c><00:04:23.600><c> video,</c><00:04:24.240><c> you</c><00:04:24.479><c> can</c><00:04:24.560><c> find</c><00:04:24.800><c> it</c>

00:04:25.150 --> 00:04:25.160 align:start position:0%
watched that video, you can find it
 

00:04:25.160 --> 00:04:28.310 align:start position:0%
watched that video, you can find it
here.<00:04:26.160><c> It's</c><00:04:26.479><c> a</c><00:04:26.720><c> little</c><00:04:26.960><c> more</c><00:04:27.440><c> involved</c><00:04:28.000><c> than</c>

00:04:28.310 --> 00:04:28.320 align:start position:0%
here. It's a little more involved than
 

00:04:28.320 --> 00:04:30.150 align:start position:0%
here. It's a little more involved than
simply<00:04:28.639><c> installing</c><00:04:29.120><c> tail</c><00:04:29.440><c> scale</c><00:04:29.840><c> because</c>

00:04:30.150 --> 00:04:30.160 align:start position:0%
simply installing tail scale because
 

00:04:30.160 --> 00:04:32.870 align:start position:0%
simply installing tail scale because
it's<00:04:30.479><c> running</c><00:04:30.720><c> in</c><00:04:30.960><c> Docker</c><00:04:31.360><c> Compose,</c><00:04:32.320><c> but</c><00:04:32.639><c> I</c>

00:04:32.870 --> 00:04:32.880 align:start position:0%
it's running in Docker Compose, but I
 

00:04:32.880 --> 00:04:34.710 align:start position:0%
it's running in Docker Compose, but I
have<00:04:32.960><c> a</c><00:04:33.120><c> neat</c><00:04:33.360><c> trick</c><00:04:33.600><c> that</c><00:04:33.919><c> I</c><00:04:34.160><c> use</c><00:04:34.320><c> to</c><00:04:34.560><c> deal</c>

00:04:34.710 --> 00:04:34.720 align:start position:0%
have a neat trick that I use to deal
 

00:04:34.720 --> 00:04:36.629 align:start position:0%
have a neat trick that I use to deal
with<00:04:34.960><c> secrets</c><00:04:35.360><c> that</c><00:04:35.600><c> you</c><00:04:35.840><c> might</c><00:04:36.160><c> not</c><00:04:36.400><c> have</c>

00:04:36.629 --> 00:04:36.639 align:start position:0%
with secrets that you might not have
 

00:04:36.639 --> 00:04:39.270 align:start position:0%
with secrets that you might not have
seen<00:04:36.880><c> before.</c><00:04:37.600><c> So,</c><00:04:37.919><c> check</c><00:04:38.160><c> that</c><00:04:38.400><c> out,</c><00:04:39.040><c> then</c>

00:04:39.270 --> 00:04:39.280 align:start position:0%
seen before. So, check that out, then
 

00:04:39.280 --> 00:04:42.390 align:start position:0%
seen before. So, check that out, then
come<00:04:39.520><c> back</c><00:04:39.680><c> to</c><00:04:39.840><c> this</c><00:04:40.080><c> video.</c><00:04:41.040><c> In</c><00:04:41.360><c> N8N,</c><00:04:42.080><c> I've</c>

00:04:42.390 --> 00:04:42.400 align:start position:0%
come back to this video. In N8N, I've
 

00:04:42.400 --> 00:04:44.070 align:start position:0%
come back to this video. In N8N, I've
created<00:04:42.639><c> a</c><00:04:42.880><c> workflow</c><00:04:43.280><c> that</c><00:04:43.520><c> starts</c><00:04:43.759><c> with</c><00:04:43.919><c> a</c>

00:04:44.070 --> 00:04:44.080 align:start position:0%
created a workflow that starts with a
 

00:04:44.080 --> 00:04:46.469 align:start position:0%
created a workflow that starts with a
web<00:04:44.320><c> hook</c><00:04:44.560><c> trigger.</c><00:04:45.360><c> When</c><00:04:45.520><c> I</c><00:04:45.759><c> send</c><00:04:45.919><c> a</c><00:04:46.160><c> request</c>

00:04:46.469 --> 00:04:46.479 align:start position:0%
web hook trigger. When I send a request
 

00:04:46.479 --> 00:04:49.350 align:start position:0%
web hook trigger. When I send a request
to<00:04:46.639><c> this</c><00:04:46.880><c> web</c><00:04:47.040><c> hook</c><00:04:47.440><c> with</c><00:04:47.680><c> an</c><00:04:48.000><c> idea</c><00:04:48.560><c> parameter,</c>

00:04:49.350 --> 00:04:49.360 align:start position:0%
to this web hook with an idea parameter,
 

00:04:49.360 --> 00:04:51.830 align:start position:0%
to this web hook with an idea parameter,
it<00:04:49.600><c> kicks</c><00:04:49.919><c> off</c><00:04:50.080><c> the</c><00:04:50.320><c> process.</c><00:04:51.120><c> I</c><00:04:51.360><c> created</c><00:04:51.600><c> a</c>

00:04:51.830 --> 00:04:51.840 align:start position:0%
it kicks off the process. I created a
 

00:04:51.840 --> 00:04:53.749 align:start position:0%
it kicks off the process. I created a
simple<00:04:52.080><c> workflow</c><00:04:52.479><c> in</c><00:04:52.800><c> shortcuts</c><00:04:53.360><c> on</c><00:04:53.520><c> my</c>

00:04:53.749 --> 00:04:53.759 align:start position:0%
simple workflow in shortcuts on my
 

00:04:53.759 --> 00:04:56.390 align:start position:0%
simple workflow in shortcuts on my
iPhone<00:04:54.479><c> that</c><00:04:54.720><c> takes</c><00:04:54.960><c> in</c><00:04:55.199><c> text</c><00:04:55.680><c> and</c><00:04:55.919><c> sends</c><00:04:56.240><c> that</c>

00:04:56.390 --> 00:04:56.400 align:start position:0%
iPhone that takes in text and sends that
 

00:04:56.400 --> 00:04:59.510 align:start position:0%
iPhone that takes in text and sends that
off<00:04:56.639><c> to</c><00:04:56.800><c> the</c><00:04:56.960><c> web</c><00:04:57.199><c> hook.</c><00:04:58.000><c> The</c><00:04:58.240><c> workflow</c><00:04:58.639><c> in</c><00:04:58.880><c> N8N</c>

00:04:59.510 --> 00:04:59.520 align:start position:0%
off to the web hook. The workflow in N8N
 

00:04:59.520 --> 00:05:02.230 align:start position:0%
off to the web hook. The workflow in N8N
then<00:04:59.759><c> calls</c><00:05:00.080><c> Perplex's</c><00:05:00.800><c> research</c><00:05:01.280><c> API</c>

00:05:02.230 --> 00:05:02.240 align:start position:0%
then calls Perplex's research API
 

00:05:02.240 --> 00:05:04.550 align:start position:0%
then calls Perplex's research API
passing<00:05:02.720><c> along</c><00:05:03.040><c> my</c><00:05:03.360><c> idea</c><00:05:03.759><c> as</c><00:05:04.080><c> something</c><00:05:04.320><c> to</c>

00:05:04.550 --> 00:05:04.560 align:start position:0%
passing along my idea as something to
 

00:05:04.560 --> 00:05:07.110 align:start position:0%
passing along my idea as something to
research.<00:05:05.360><c> To</c><00:05:05.680><c> use</c><00:05:05.840><c> that</c><00:05:06.080><c> API,</c><00:05:06.639><c> you</c><00:05:06.800><c> basically</c>

00:05:07.110 --> 00:05:07.120 align:start position:0%
research. To use that API, you basically
 

00:05:07.120 --> 00:05:09.270 align:start position:0%
research. To use that API, you basically
just<00:05:07.280><c> send</c><00:05:07.520><c> a</c><00:05:07.680><c> JSON</c><00:05:08.080><c> blob</c><00:05:08.560><c> that</c><00:05:08.880><c> looks</c><00:05:09.120><c> like</c>

00:05:09.270 --> 00:05:09.280 align:start position:0%
just send a JSON blob that looks like
 

00:05:09.280 --> 00:05:11.909 align:start position:0%
just send a JSON blob that looks like
this.<00:05:10.240><c> Perplexi</c><00:05:10.960><c> churns</c><00:05:11.360><c> on</c><00:05:11.520><c> this</c><00:05:11.680><c> for</c>

00:05:11.909 --> 00:05:11.919 align:start position:0%
this. Perplexi churns on this for
 

00:05:11.919 --> 00:05:13.909 align:start position:0%
this. Perplexi churns on this for
upwards<00:05:12.400><c> of</c><00:05:12.639><c> a</c><00:05:12.960><c> couple</c><00:05:13.120><c> of</c><00:05:13.280><c> minutes.</c><00:05:13.600><c> So</c><00:05:13.759><c> you</c>

00:05:13.909 --> 00:05:13.919 align:start position:0%
upwards of a couple of minutes. So you
 

00:05:13.919 --> 00:05:16.790 align:start position:0%
upwards of a couple of minutes. So you
need<00:05:14.000><c> to</c><00:05:14.160><c> specify</c><00:05:14.960><c> a</c><00:05:15.199><c> good</c><00:05:15.440><c> long</c><00:05:15.800><c> timeout.</c>

00:05:16.790 --> 00:05:16.800 align:start position:0%
need to specify a good long timeout.
 

00:05:16.800 --> 00:05:19.270 align:start position:0%
need to specify a good long timeout.
When<00:05:17.039><c> Perplexity</c><00:05:17.759><c> returns</c><00:05:18.320><c> results,</c><00:05:18.720><c> I</c><00:05:18.960><c> use</c><00:05:19.039><c> a</c>

00:05:19.270 --> 00:05:19.280 align:start position:0%
When Perplexity returns results, I use a
 

00:05:19.280 --> 00:05:22.310 align:start position:0%
When Perplexity returns results, I use a
bit<00:05:19.440><c> of</c><00:05:19.759><c> reax.</c><00:05:20.560><c> in</c><00:05:20.720><c> a</c><00:05:20.960><c> code</c><00:05:21.120><c> node</c><00:05:21.680><c> to</c><00:05:21.919><c> separate</c>

00:05:22.310 --> 00:05:22.320 align:start position:0%
bit of reax. in a code node to separate
 

00:05:22.320 --> 00:05:24.150 align:start position:0%
bit of reax. in a code node to separate
the<00:05:22.560><c> thinking</c><00:05:22.960><c> section</c><00:05:23.280><c> from</c><00:05:23.520><c> the</c><00:05:23.759><c> actual</c>

00:05:24.150 --> 00:05:24.160 align:start position:0%
the thinking section from the actual
 

00:05:24.160 --> 00:05:26.870 align:start position:0%
the thinking section from the actual
research<00:05:24.720><c> and</c><00:05:25.240><c> citations.</c><00:05:26.240><c> Finally,</c><00:05:26.720><c> I</c>

00:05:26.870 --> 00:05:26.880 align:start position:0%
research and citations. Finally, I
 

00:05:26.880 --> 00:05:28.870 align:start position:0%
research and citations. Finally, I
package<00:05:27.360><c> everything</c><00:05:27.600><c> up</c><00:05:27.840><c> into</c><00:05:28.080><c> a</c><00:05:28.240><c> JSON</c><00:05:28.639><c> object</c>

00:05:28.870 --> 00:05:28.880 align:start position:0%
package everything up into a JSON object
 

00:05:28.880 --> 00:05:30.670 align:start position:0%
package everything up into a JSON object
and<00:05:29.199><c> send</c><00:05:29.360><c> it</c><00:05:29.520><c> to</c><00:05:29.680><c> my</c><00:05:29.840><c> local</c><00:05:30.160><c> web</c><00:05:30.320><c> hook</c>

00:05:30.670 --> 00:05:30.680 align:start position:0%
and send it to my local web hook
 

00:05:30.680 --> 00:05:33.189 align:start position:0%
and send it to my local web hook
endpoint.<00:05:31.680><c> Let</c><00:05:31.840><c> me</c><00:05:32.000><c> demonstrate</c><00:05:32.479><c> this</c><00:05:32.720><c> end</c><00:05:32.960><c> to</c>

00:05:33.189 --> 00:05:33.199 align:start position:0%
endpoint. Let me demonstrate this end to
 

00:05:33.199 --> 00:05:35.510 align:start position:0%
endpoint. Let me demonstrate this end to
end.<00:05:33.759><c> I'll</c><00:05:34.000><c> trigger</c><00:05:34.320><c> the</c><00:05:34.479><c> workflow</c><00:05:34.880><c> with</c><00:05:35.280><c> an</c>

00:05:35.510 --> 00:05:35.520 align:start position:0%
end. I'll trigger the workflow with an
 

00:05:35.520 --> 00:05:38.469 align:start position:0%
end. I'll trigger the workflow with an
idea<00:05:36.240><c> and</c><00:05:36.479><c> you'll</c><00:05:36.720><c> see</c><00:05:36.880><c> how</c><00:05:37.120><c> N8N</c><00:05:37.919><c> processes</c>

00:05:38.469 --> 00:05:38.479 align:start position:0%
idea and you'll see how N8N processes
 

00:05:38.479 --> 00:05:41.110 align:start position:0%
idea and you'll see how N8N processes
it,<00:05:39.120><c> calls</c><00:05:39.440><c> perplexity,</c><00:05:40.400><c> then</c><00:05:40.639><c> sends</c><00:05:40.960><c> the</c>

00:05:41.110 --> 00:05:41.120 align:start position:0%
it, calls perplexity, then sends the
 

00:05:41.120 --> 00:05:43.110 align:start position:0%
it, calls perplexity, then sends the
results<00:05:41.440><c> to</c><00:05:41.600><c> my</c><00:05:41.840><c> local</c><00:05:42.160><c> machine</c><00:05:42.720><c> where</c>

00:05:43.110 --> 00:05:43.120 align:start position:0%
results to my local machine where
 

00:05:43.120 --> 00:05:45.350 align:start position:0%
results to my local machine where
automatically<00:05:43.759><c> creates</c><00:05:44.080><c> a</c><00:05:44.320><c> new</c><00:05:44.479><c> note</c><00:05:44.880><c> in</c><00:05:45.120><c> my</c>

00:05:45.350 --> 00:05:45.360 align:start position:0%
automatically creates a new note in my
 

00:05:45.360 --> 00:05:47.590 align:start position:0%
automatically creates a new note in my
Obsidian<00:05:45.919><c> vault.</c><00:05:46.720><c> So,</c><00:05:46.960><c> let's</c><00:05:47.199><c> open</c><00:05:47.440><c> the</c>

00:05:47.590 --> 00:05:47.600 align:start position:0%
Obsidian vault. So, let's open the
 

00:05:47.600 --> 00:05:50.230 align:start position:0%
Obsidian vault. So, let's open the
shortcut<00:05:48.000><c> on</c><00:05:48.240><c> my</c><00:05:48.479><c> phone</c><00:05:48.720><c> and</c><00:05:49.039><c> enter</c><00:05:49.360><c> an</c><00:05:49.520><c> idea.</c>

00:05:50.230 --> 00:05:50.240 align:start position:0%
shortcut on my phone and enter an idea.
 

00:05:50.240 --> 00:05:53.029 align:start position:0%
shortcut on my phone and enter an idea.
I<00:05:50.560><c> have</c><00:05:50.720><c> dozens</c><00:05:51.039><c> of</c><00:05:51.280><c> ideas</c><00:05:51.680><c> on</c><00:05:51.919><c> Post-it</c><00:05:52.320><c> notes</c>

00:05:53.029 --> 00:05:53.039 align:start position:0%
I have dozens of ideas on Post-it notes
 

00:05:53.039 --> 00:05:55.110 align:start position:0%
I have dozens of ideas on Post-it notes
on<00:05:53.280><c> my</c><00:05:53.440><c> wall</c><00:05:53.680><c> in</c><00:05:53.919><c> my</c><00:05:54.080><c> office.</c><00:05:54.479><c> So,</c><00:05:54.639><c> I'm</c><00:05:54.880><c> just</c>

00:05:55.110 --> 00:05:55.120 align:start position:0%
on my wall in my office. So, I'm just
 

00:05:55.120 --> 00:05:58.150 align:start position:0%
on my wall in my office. So, I'm just
adding<00:05:55.600><c> that</c><00:05:55.919><c> idea</c><00:05:56.320><c> to</c><00:05:56.479><c> this</c><00:05:56.840><c> shortcut.</c><00:05:57.840><c> The</c>

00:05:58.150 --> 00:05:58.160 align:start position:0%
adding that idea to this shortcut. The
 

00:05:58.160 --> 00:06:00.469 align:start position:0%
adding that idea to this shortcut. The
first<00:05:58.400><c> step</c><00:05:58.639><c> in</c><00:05:58.960><c> any</c><00:05:59.280><c> video</c><00:05:59.680><c> after</c><00:06:00.000><c> creating</c><00:06:00.320><c> a</c>

00:06:00.469 --> 00:06:00.479 align:start position:0%
first step in any video after creating a
 

00:06:00.479 --> 00:06:02.790 align:start position:0%
first step in any video after creating a
thumbnail,<00:06:00.960><c> idea,</c><00:06:01.360><c> and</c><00:06:01.600><c> possible</c><00:06:02.000><c> title</c><00:06:02.560><c> is</c>

00:06:02.790 --> 00:06:02.800 align:start position:0%
thumbnail, idea, and possible title is
 

00:06:02.800 --> 00:06:05.189 align:start position:0%
thumbnail, idea, and possible title is
to<00:06:02.960><c> do</c><00:06:03.120><c> a</c><00:06:03.360><c> little</c><00:06:03.440><c> bit</c><00:06:03.600><c> of</c><00:06:03.759><c> research</c><00:06:04.639><c> that</c><00:06:04.960><c> then</c>

00:06:05.189 --> 00:06:05.199 align:start position:0%
to do a little bit of research that then
 

00:06:05.199 --> 00:06:07.189 align:start position:0%
to do a little bit of research that then
turns<00:06:05.520><c> into</c><00:06:05.680><c> a</c><00:06:05.919><c> script</c><00:06:06.080><c> that</c><00:06:06.319><c> I'd</c><00:06:06.639><c> use</c><00:06:06.800><c> to</c><00:06:07.039><c> talk</c>

00:06:07.189 --> 00:06:07.199 align:start position:0%
turns into a script that I'd use to talk
 

00:06:07.199 --> 00:06:10.070 align:start position:0%
turns into a script that I'd use to talk
to<00:06:07.360><c> the</c><00:06:07.520><c> camera.</c><00:06:08.319><c> So,</c><00:06:08.560><c> if</c><00:06:08.720><c> I</c><00:06:08.880><c> open</c><00:06:09.039><c> up</c><00:06:09.280><c> N8N</c><00:06:09.919><c> and</c>

00:06:10.070 --> 00:06:10.080 align:start position:0%
to the camera. So, if I open up N8N and
 

00:06:10.080 --> 00:06:11.909 align:start position:0%
to the camera. So, if I open up N8N and
go<00:06:10.240><c> to</c><00:06:10.400><c> the</c><00:06:10.560><c> executions,</c><00:06:11.280><c> I</c><00:06:11.440><c> see</c><00:06:11.600><c> that</c><00:06:11.759><c> the</c>

00:06:11.909 --> 00:06:11.919 align:start position:0%
go to the executions, I see that the
 

00:06:11.919 --> 00:06:14.150 align:start position:0%
go to the executions, I see that the
workflow<00:06:12.639><c> is</c><00:06:12.960><c> still</c><00:06:13.199><c> processing</c><00:06:13.840><c> the</c>

00:06:14.150 --> 00:06:14.160 align:start position:0%
workflow is still processing the
 

00:06:14.160 --> 00:06:17.350 align:start position:0%
workflow is still processing the
Perplexity<00:06:14.880><c> API.</c><00:06:16.080><c> When</c><00:06:16.319><c> it's</c><00:06:16.560><c> done,</c><00:06:16.800><c> the</c><00:06:17.039><c> rest</c>

00:06:17.350 --> 00:06:17.360 align:start position:0%
Perplexity API. When it's done, the rest
 

00:06:17.360 --> 00:06:19.749 align:start position:0%
Perplexity API. When it's done, the rest
moves<00:06:17.759><c> super</c><00:06:18.080><c> quick</c><00:06:18.639><c> and</c><00:06:18.880><c> I</c><00:06:19.120><c> have</c><00:06:19.280><c> this</c><00:06:19.520><c> new</c>

00:06:19.749 --> 00:06:19.759 align:start position:0%
moves super quick and I have this new
 

00:06:19.759 --> 00:06:22.309 align:start position:0%
moves super quick and I have this new
note<00:06:20.080><c> in</c><00:06:20.319><c> my</c><00:06:20.400><c> Obsidian</c><00:06:20.960><c> vault.</c><00:06:21.680><c> Pretty</c><00:06:22.000><c> cool,</c>

00:06:22.309 --> 00:06:22.319 align:start position:0%
note in my Obsidian vault. Pretty cool,
 

00:06:22.319 --> 00:06:24.629 align:start position:0%
note in my Obsidian vault. Pretty cool,
right?<00:06:23.039><c> With</c><00:06:23.280><c> just</c><00:06:23.520><c> a</c><00:06:23.680><c> few</c><00:06:23.840><c> components,</c><00:06:24.400><c> we've</c>

00:06:24.629 --> 00:06:24.639 align:start position:0%
right? With just a few components, we've
 

00:06:24.639 --> 00:06:27.110 align:start position:0%
right? With just a few components, we've
created<00:06:24.960><c> a</c><00:06:25.199><c> system</c><00:06:25.520><c> where</c><00:06:25.759><c> my</c><00:06:26.080><c> remote</c><00:06:26.479><c> N8N</c>

00:06:27.110 --> 00:06:27.120 align:start position:0%
created a system where my remote N8N
 

00:06:27.120 --> 00:06:29.270 align:start position:0%
created a system where my remote N8N
server<00:06:27.759><c> can</c><00:06:28.080><c> interact</c><00:06:28.560><c> directly</c><00:06:28.960><c> with</c>

00:06:29.270 --> 00:06:29.280 align:start position:0%
server can interact directly with
 

00:06:29.280 --> 00:06:31.909 align:start position:0%
server can interact directly with
applications<00:06:29.840><c> on</c><00:06:30.080><c> my</c><00:06:30.319><c> local</c><00:06:30.639><c> machine.</c><00:06:31.520><c> Now,</c><00:06:31.759><c> a</c>

00:06:31.909 --> 00:06:31.919 align:start position:0%
applications on my local machine. Now, a
 

00:06:31.919 --> 00:06:33.430 align:start position:0%
applications on my local machine. Now, a
quick<00:06:32.080><c> note</c><00:06:32.319><c> about</c><00:06:32.560><c> cost.</c><00:06:33.039><c> Using</c>

00:06:33.430 --> 00:06:33.440 align:start position:0%
quick note about cost. Using
 

00:06:33.440 --> 00:06:37.029 align:start position:0%
quick note about cost. Using
Perplexity's<00:06:34.240><c> API</c><00:06:35.199><c> is</c><00:06:35.520><c> not</c><00:06:35.759><c> free.</c><00:06:36.479><c> From</c><00:06:36.800><c> my</c>

00:06:37.029 --> 00:06:37.039 align:start position:0%
Perplexity's API is not free. From my
 

00:06:37.039 --> 00:06:39.909 align:start position:0%
Perplexity's API is not free. From my
testing,<00:06:37.600><c> each</c><00:06:37.880><c> request</c><00:06:38.880><c> seems</c><00:06:39.280><c> to</c><00:06:39.440><c> cost</c>

00:06:39.909 --> 00:06:39.919 align:start position:0%
testing, each request seems to cost
 

00:06:39.919 --> 00:06:41.230 align:start position:0%
testing, each request seems to cost
between<00:06:40.639><c> 15</c>

00:06:41.230 --> 00:06:41.240 align:start position:0%
between 15
 

00:06:41.240 --> 00:06:42.790 align:start position:0%
between 15
and0.50.50.0.50.0.50.0.50.0.50.<00:06:42.319><c> If</c><00:06:42.560><c> I</c><00:06:42.720><c> did</c>

00:06:42.790 --> 00:06:42.800 align:start position:0%
and0.50.50.0.50.0.50.0.50.0.50. If I did
 

00:06:42.800 --> 00:06:45.189 align:start position:0%
and0.50.50.0.50.0.50.0.50.0.50. If I did
the<00:06:43.039><c> research</c><00:06:43.520><c> manually</c><00:06:44.080><c> in</c><00:06:44.319><c> the</c><00:06:44.479><c> Perplexi</c>

00:06:45.189 --> 00:06:45.199 align:start position:0%
the research manually in the Perplexi
 

00:06:45.199 --> 00:06:48.550 align:start position:0%
the research manually in the Perplexi
UI,<00:06:46.080><c> it</c><00:06:46.319><c> would</c><00:06:46.479><c> be</c><00:06:46.639><c> free.</c><00:06:47.360><c> Sure,</c><00:06:47.680><c> Perplexi</c><00:06:48.319><c> Pro</c>

00:06:48.550 --> 00:06:48.560 align:start position:0%
UI, it would be free. Sure, Perplexi Pro
 

00:06:48.560 --> 00:06:51.670 align:start position:0%
UI, it would be free. Sure, Perplexi Pro
is<00:06:48.800><c> somewhere</c><00:06:49.120><c> around</c><00:06:49.759><c> $200</c><00:06:50.319><c> a</c><00:06:50.639><c> year,</c><00:06:51.280><c> but</c><00:06:51.440><c> I</c>

00:06:51.670 --> 00:06:51.680 align:start position:0%
is somewhere around $200 a year, but I
 

00:06:51.680 --> 00:06:54.150 align:start position:0%
is somewhere around $200 a year, but I
managed<00:06:51.919><c> to</c><00:06:52.080><c> get</c><00:06:52.160><c> a</c><00:06:52.479><c> free</c><00:06:52.800><c> year</c><00:06:53.280><c> through</c><00:06:53.680><c> Kevin</c>

00:06:54.150 --> 00:06:54.160 align:start position:0%
managed to get a free year through Kevin
 

00:06:54.160 --> 00:06:56.710 align:start position:0%
managed to get a free year through Kevin
Rose's<00:06:54.919><c> newsletter.</c><00:06:55.919><c> Doing</c><00:06:56.240><c> the</c><00:06:56.400><c> actual</c>

00:06:56.710 --> 00:06:56.720 align:start position:0%
Rose's newsletter. Doing the actual
 

00:06:56.720 --> 00:06:59.110 align:start position:0%
Rose's newsletter. Doing the actual
research<00:06:57.199><c> manually</c><00:06:57.840><c> would</c><00:06:58.160><c> take</c><00:06:58.400><c> a</c><00:06:58.639><c> lot</c><00:06:58.800><c> more</c>

00:06:59.110 --> 00:06:59.120 align:start position:0%
research manually would take a lot more
 

00:06:59.120 --> 00:07:03.350 align:start position:0%
research manually would take a lot more
time,<00:07:00.000><c> and</c><00:07:00.479><c> time</c><00:07:00.880><c> is</c><00:07:01.319><c> money.</c><00:07:02.319><c> 50</c><00:07:02.560><c> cents</c><00:07:02.880><c> isn't</c>

00:07:03.350 --> 00:07:03.360 align:start position:0%
time, and time is money. 50 cents isn't
 

00:07:03.360 --> 00:07:05.350 align:start position:0%
time, and time is money. 50 cents isn't
crazy<00:07:03.759><c> expensive,</c><00:07:04.240><c> but</c><00:07:04.560><c> not</c><00:07:04.880><c> something</c><00:07:05.120><c> I</c>

00:07:05.350 --> 00:07:05.360 align:start position:0%
crazy expensive, but not something I
 

00:07:05.360 --> 00:07:07.350 align:start position:0%
crazy expensive, but not something I
would<00:07:05.440><c> want</c><00:07:05.599><c> to</c><00:07:05.759><c> do</c><00:07:06.080><c> hundreds</c><00:07:06.479><c> of</c><00:07:06.720><c> times</c><00:07:07.120><c> a</c>

00:07:07.350 --> 00:07:07.360 align:start position:0%
would want to do hundreds of times a
 

00:07:07.360 --> 00:07:10.150 align:start position:0%
would want to do hundreds of times a
day.<00:07:08.240><c> In</c><00:07:08.560><c> an</c><00:07:08.720><c> upcoming</c><00:07:09.199><c> video,</c><00:07:09.599><c> I</c><00:07:09.840><c> might</c>

00:07:10.150 --> 00:07:10.160 align:start position:0%
day. In an upcoming video, I might
 

00:07:10.160 --> 00:07:12.710 align:start position:0%
day. In an upcoming video, I might
explore<00:07:10.560><c> using</c><00:07:11.240><c> Perplexica</c><00:07:12.240><c> as</c><00:07:12.479><c> an</c>

00:07:12.710 --> 00:07:12.720 align:start position:0%
explore using Perplexica as an
 

00:07:12.720 --> 00:07:14.469 align:start position:0%
explore using Perplexica as an
alternative,<00:07:13.280><c> which</c><00:07:13.520><c> could</c><00:07:13.759><c> potentially</c><00:07:14.319><c> be</c>

00:07:14.469 --> 00:07:14.479 align:start position:0%
alternative, which could potentially be
 

00:07:14.479 --> 00:07:17.189 align:start position:0%
alternative, which could potentially be
a<00:07:14.720><c> lot</c><00:07:14.800><c> more</c><00:07:15.120><c> cost</c><00:07:15.560><c> effective.</c><00:07:16.560><c> Now,</c><00:07:16.880><c> all</c><00:07:17.039><c> of</c>

00:07:17.189 --> 00:07:17.199 align:start position:0%
a lot more cost effective. Now, all of
 

00:07:17.199 --> 00:07:20.070 align:start position:0%
a lot more cost effective. Now, all of
this<00:07:17.360><c> is</c><00:07:17.599><c> running</c><00:07:17.840><c> on</c><00:07:18.080><c> my</c><00:07:18.280><c> KVM2</c><00:07:19.280><c> server</c><00:07:19.840><c> that</c>

00:07:20.070 --> 00:07:20.080 align:start position:0%
this is running on my KVM2 server that
 

00:07:20.080 --> 00:07:22.309 align:start position:0%
this is running on my KVM2 server that
I've<00:07:20.400><c> hosted</c><00:07:20.800><c> on</c><00:07:21.039><c> Hostinger,</c><00:07:21.680><c> which</c><00:07:21.919><c> costs</c>

00:07:22.309 --> 00:07:22.319 align:start position:0%
I've hosted on Hostinger, which costs
 

00:07:22.319 --> 00:07:25.029 align:start position:0%
I've hosted on Hostinger, which costs
about<00:07:23.280><c> six</c><00:07:23.520><c> bucks</c><00:07:23.759><c> a</c><00:07:24.000><c> month</c><00:07:24.240><c> if</c><00:07:24.479><c> you</c><00:07:24.639><c> use</c><00:07:24.800><c> my</c>

00:07:25.029 --> 00:07:25.039 align:start position:0%
about six bucks a month if you use my
 

00:07:25.039 --> 00:07:27.309 align:start position:0%
about six bucks a month if you use my
coupon<00:07:25.440><c> code,</c><00:07:25.919><c> which</c><00:07:26.160><c> is</c><00:07:26.400><c> Matt</c>

00:07:27.309 --> 00:07:27.319 align:start position:0%
coupon code, which is Matt
 

00:07:27.319 --> 00:07:30.629 align:start position:0%
coupon code, which is Matt
W.<00:07:28.319><c> Let</c><00:07:28.560><c> me</c><00:07:28.639><c> show</c><00:07:28.800><c> you</c><00:07:29.199><c> another</c><00:07:29.840><c> example.</c>

00:07:30.629 --> 00:07:30.639 align:start position:0%
W. Let me show you another example.
 

00:07:30.639 --> 00:07:32.550 align:start position:0%
W. Let me show you another example.
Every<00:07:31.039><c> Tuesday,</c><00:07:31.440><c> I</c><00:07:31.680><c> do</c><00:07:31.759><c> a</c><00:07:32.000><c> stream</c><00:07:32.240><c> with</c><00:07:32.400><c> a</c>

00:07:32.550 --> 00:07:32.560 align:start position:0%
Every Tuesday, I do a stream with a
 

00:07:32.560 --> 00:07:34.629 align:start position:0%
Every Tuesday, I do a stream with a
friend<00:07:32.800><c> who</c><00:07:33.039><c> I</c><00:07:33.199><c> used</c><00:07:33.360><c> to</c><00:07:33.520><c> work</c><00:07:33.680><c> with</c><00:07:34.240><c> when</c><00:07:34.479><c> I</c>

00:07:34.629 --> 00:07:34.639 align:start position:0%
friend who I used to work with when I
 

00:07:34.639 --> 00:07:37.270 align:start position:0%
friend who I used to work with when I
was<00:07:34.720><c> at</c><00:07:34.960><c> Data</c><00:07:35.280><c> Dog.</c><00:07:36.000><c> On</c><00:07:36.240><c> that</c><00:07:36.479><c> stream,</c><00:07:36.800><c> we</c><00:07:37.039><c> talk</c>

00:07:37.270 --> 00:07:37.280 align:start position:0%
was at Data Dog. On that stream, we talk
 

00:07:37.280 --> 00:07:39.350 align:start position:0%
was at Data Dog. On that stream, we talk
about<00:07:37.840><c> everything</c><00:07:38.240><c> the</c><00:07:38.560><c> two</c><00:07:38.720><c> of</c><00:07:38.880><c> us</c><00:07:39.120><c> are</c>

00:07:39.350 --> 00:07:39.360 align:start position:0%
about everything the two of us are
 

00:07:39.360 --> 00:07:41.510 align:start position:0%
about everything the two of us are
interested<00:07:39.680><c> in.</c><00:07:40.479><c> That</c><00:07:40.720><c> includes</c><00:07:41.039><c> the</c><00:07:41.280><c> latest</c>

00:07:41.510 --> 00:07:41.520 align:start position:0%
interested in. That includes the latest
 

00:07:41.520 --> 00:07:44.550 align:start position:0%
interested in. That includes the latest
AI<00:07:42.000><c> news,</c><00:07:42.479><c> TV,</c><00:07:42.880><c> and</c><00:07:43.120><c> movies</c><00:07:43.520><c> that</c><00:07:43.759><c> we've</c><00:07:44.080><c> seen,</c>

00:07:44.550 --> 00:07:44.560 align:start position:0%
AI news, TV, and movies that we've seen,
 

00:07:44.560 --> 00:07:46.790 align:start position:0%
AI news, TV, and movies that we've seen,
any<00:07:44.960><c> purchases</c><00:07:45.520><c> we've</c><00:07:45.840><c> been</c><00:07:46.080><c> suckered</c><00:07:46.560><c> into</c>

00:07:46.790 --> 00:07:46.800 align:start position:0%
any purchases we've been suckered into
 

00:07:46.800 --> 00:07:50.110 align:start position:0%
any purchases we've been suckered into
making,<00:07:47.680><c> and</c><00:07:48.000><c> we</c><00:07:48.240><c> love</c><00:07:48.400><c> to</c><00:07:48.639><c> cover</c><00:07:48.880><c> a</c><00:07:49.199><c> good</c><00:07:49.599><c> tech</c>

00:07:50.110 --> 00:07:50.120 align:start position:0%
making, and we love to cover a good tech
 

00:07:50.120 --> 00:07:52.629 align:start position:0%
making, and we love to cover a good tech
survey.<00:07:51.120><c> Even</c><00:07:51.360><c> though</c><00:07:51.599><c> I</c><00:07:51.919><c> know</c><00:07:52.160><c> when</c><00:07:52.400><c> it's</c>

00:07:52.629 --> 00:07:52.639 align:start position:0%
survey. Even though I know when it's
 

00:07:52.639 --> 00:07:55.990 align:start position:0%
survey. Even though I know when it's
going<00:07:52.800><c> to</c><00:07:52.960><c> be,</c><00:07:53.680><c> it's</c><00:07:54.000><c> a</c><00:07:54.240><c> scramble</c><00:07:55.039><c> every</c><00:07:55.599><c> time</c>

00:07:55.990 --> 00:07:56.000 align:start position:0%
going to be, it's a scramble every time
 

00:07:56.000 --> 00:07:58.950 align:start position:0%
going to be, it's a scramble every time
to<00:07:56.319><c> get</c><00:07:56.479><c> ready</c><00:07:56.720><c> for</c><00:07:56.879><c> it.</c><00:07:57.599><c> So</c><00:07:58.000><c> now</c><00:07:58.400><c> I</c><00:07:58.639><c> have</c><00:07:58.800><c> a</c>

00:07:58.950 --> 00:07:58.960 align:start position:0%
to get ready for it. So now I have a
 

00:07:58.960 --> 00:08:01.189 align:start position:0%
to get ready for it. So now I have a
workflow<00:07:59.360><c> in</c><00:07:59.599><c> N8N</c><00:08:00.160><c> that's</c><00:08:00.400><c> triggered</c><00:08:00.800><c> every</c>

00:08:01.189 --> 00:08:01.199 align:start position:0%
workflow in N8N that's triggered every
 

00:08:01.199 --> 00:08:03.189 align:start position:0%
workflow in N8N that's triggered every
Friday<00:08:01.919><c> that</c><00:08:02.240><c> starts</c><00:08:02.479><c> by</c><00:08:02.720><c> getting</c><00:08:02.960><c> the</c>

00:08:03.189 --> 00:08:03.199 align:start position:0%
Friday that starts by getting the
 

00:08:03.199 --> 00:08:05.350 align:start position:0%
Friday that starts by getting the
relevant<00:08:03.599><c> news</c><00:08:03.840><c> items</c><00:08:04.240><c> for</c><00:08:04.479><c> the</c><00:08:04.720><c> week</c><00:08:05.039><c> and</c>

00:08:05.350 --> 00:08:05.360 align:start position:0%
relevant news items for the week and
 

00:08:05.360 --> 00:08:08.390 align:start position:0%
relevant news items for the week and
emails<00:08:05.840><c> it</c><00:08:06.080><c> to</c><00:08:06.319><c> me</c><00:08:06.639><c> and</c><00:08:06.800><c> to</c><00:08:06.960><c> Ryan.</c><00:08:07.840><c> And</c><00:08:08.000><c> then</c><00:08:08.160><c> it</c>

00:08:08.390 --> 00:08:08.400 align:start position:0%
emails it to me and to Ryan. And then it
 

00:08:08.400 --> 00:08:10.710 align:start position:0%
emails it to me and to Ryan. And then it
sends<00:08:08.720><c> another</c><00:08:09.039><c> email</c><00:08:09.360><c> to</c><00:08:09.599><c> me</c><00:08:09.840><c> and</c><00:08:10.080><c> to</c><00:08:10.319><c> Ryan</c>

00:08:10.710 --> 00:08:10.720 align:start position:0%
sends another email to me and to Ryan
 

00:08:10.720 --> 00:08:13.029 align:start position:0%
sends another email to me and to Ryan
asking<00:08:10.960><c> for</c><00:08:11.120><c> a</c><00:08:11.360><c> list</c><00:08:11.520><c> of</c><00:08:11.680><c> topics</c><00:08:12.000><c> to</c><00:08:12.240><c> cover</c>

00:08:13.029 --> 00:08:13.039 align:start position:0%
asking for a list of topics to cover
 

00:08:13.039 --> 00:08:15.189 align:start position:0%
asking for a list of topics to cover
without<00:08:13.520><c> waiting</c><00:08:13.840><c> for</c><00:08:14.000><c> a</c><00:08:14.240><c> response.</c><00:08:14.720><c> My</c><00:08:14.879><c> local</c>

00:08:15.189 --> 00:08:15.199 align:start position:0%
without waiting for a response. My local
 

00:08:15.199 --> 00:08:17.350 align:start position:0%
without waiting for a response. My local
web<00:08:15.440><c> hook</c><00:08:15.759><c> is</c><00:08:16.000><c> called</c><00:08:16.560><c> and</c><00:08:16.800><c> I</c><00:08:17.039><c> use</c><00:08:17.120><c> the</c>

00:08:17.350 --> 00:08:17.360 align:start position:0%
web hook is called and I use the
 

00:08:17.360 --> 00:08:19.510 align:start position:0%
web hook is called and I use the
automation<00:08:17.840><c> features</c><00:08:18.160><c> in</c><00:08:18.400><c> Pixelmator</c><00:08:19.280><c> to</c>

00:08:19.510 --> 00:08:19.520 align:start position:0%
automation features in Pixelmator to
 

00:08:19.520 --> 00:08:21.189 align:start position:0%
automation features in Pixelmator to
update<00:08:19.919><c> the</c><00:08:20.080><c> thumbnail</c><00:08:20.479><c> with</c><00:08:20.639><c> the</c><00:08:20.879><c> date</c><00:08:21.039><c> of</c>

00:08:21.189 --> 00:08:21.199 align:start position:0%
update the thumbnail with the date of
 

00:08:21.199 --> 00:08:23.830 align:start position:0%
update the thumbnail with the date of
the<00:08:21.360><c> stream</c><00:08:22.240><c> that</c><00:08:22.560><c> gets</c><00:08:22.800><c> saved</c><00:08:23.199><c> and</c><00:08:23.440><c> sent</c><00:08:23.680><c> back</c>

00:08:23.830 --> 00:08:23.840 align:start position:0%
the stream that gets saved and sent back
 

00:08:23.840 --> 00:08:26.710 align:start position:0%
the stream that gets saved and sent back
to<00:08:24.000><c> the</c><00:08:24.199><c> workflow.</c><00:08:25.280><c> At</c><00:08:25.520><c> that</c><00:08:25.840><c> time,</c><00:08:26.160><c> the</c><00:08:26.400><c> new</c>

00:08:26.710 --> 00:08:26.720 align:start position:0%
to the workflow. At that time, the new
 

00:08:26.720 --> 00:08:29.270 align:start position:0%
to the workflow. At that time, the new
stream<00:08:26.960><c> is</c><00:08:27.199><c> scheduled.</c><00:08:28.080><c> Whenever</c><00:08:28.639><c> either</c><00:08:29.120><c> one</c>

00:08:29.270 --> 00:08:29.280 align:start position:0%
stream is scheduled. Whenever either one
 

00:08:29.280 --> 00:08:31.990 align:start position:0%
stream is scheduled. Whenever either one
of<00:08:29.440><c> us</c><00:08:29.680><c> reply</c><00:08:30.080><c> to</c><00:08:30.240><c> the</c><00:08:30.400><c> topic</c><00:08:30.879><c> email,</c><00:08:31.599><c> and</c><00:08:31.759><c> we</c>

00:08:31.990 --> 00:08:32.000 align:start position:0%
of us reply to the topic email, and we
 

00:08:32.000 --> 00:08:33.909 align:start position:0%
of us reply to the topic email, and we
can<00:08:32.159><c> do</c><00:08:32.240><c> that</c><00:08:32.479><c> several</c><00:08:32.880><c> times</c><00:08:33.200><c> up</c><00:08:33.360><c> until</c><00:08:33.680><c> 10</c>

00:08:33.909 --> 00:08:33.919 align:start position:0%
can do that several times up until 10
 

00:08:33.919 --> 00:08:36.070 align:start position:0%
can do that several times up until 10
minutes<00:08:34.159><c> before</c><00:08:34.399><c> the</c><00:08:34.640><c> stream,</c><00:08:35.440><c> the</c><00:08:35.680><c> topic</c>

00:08:36.070 --> 00:08:36.080 align:start position:0%
minutes before the stream, the topic
 

00:08:36.080 --> 00:08:38.469 align:start position:0%
minutes before the stream, the topic
list<00:08:36.399><c> is</c><00:08:36.719><c> regenerated,</c><00:08:37.680><c> and</c><00:08:37.919><c> the</c><00:08:38.159><c> stream</c>

00:08:38.469 --> 00:08:38.479 align:start position:0%
list is regenerated, and the stream
 

00:08:38.479 --> 00:08:40.870 align:start position:0%
list is regenerated, and the stream
listing<00:08:38.880><c> is</c><00:08:39.120><c> updated.</c><00:08:40.080><c> On</c><00:08:40.320><c> Monday</c><00:08:40.640><c> and</c>

00:08:40.870 --> 00:08:40.880 align:start position:0%
listing is updated. On Monday and
 

00:08:40.880 --> 00:08:42.949 align:start position:0%
listing is updated. On Monday and
Tuesday<00:08:41.360><c> mornings,</c><00:08:41.839><c> and</c><00:08:42.080><c> 5</c><00:08:42.320><c> minutes</c><00:08:42.640><c> before</c>

00:08:42.949 --> 00:08:42.959 align:start position:0%
Tuesday mornings, and 5 minutes before
 

00:08:42.959 --> 00:08:44.949 align:start position:0%
Tuesday mornings, and 5 minutes before
the<00:08:43.200><c> stream</c><00:08:43.519><c> starts,</c><00:08:44.159><c> a</c><00:08:44.480><c> message</c><00:08:44.720><c> is</c>

00:08:44.949 --> 00:08:44.959 align:start position:0%
the stream starts, a message is
 

00:08:44.959 --> 00:08:47.350 align:start position:0%
the stream starts, a message is
generated<00:08:45.440><c> to</c><00:08:45.680><c> try</c><00:08:45.839><c> to</c><00:08:46.080><c> entice</c><00:08:46.800><c> potential</c>

00:08:47.350 --> 00:08:47.360 align:start position:0%
generated to try to entice potential
 

00:08:47.360 --> 00:08:49.910 align:start position:0%
generated to try to entice potential
viewers<00:08:47.760><c> to</c><00:08:48.000><c> show</c><00:08:48.240><c> up.</c><00:08:48.880><c> And</c><00:08:49.040><c> then</c><00:08:49.279><c> that</c><00:08:49.680><c> gets</c>

00:08:49.910 --> 00:08:49.920 align:start position:0%
viewers to show up. And then that gets
 

00:08:49.920 --> 00:08:53.350 align:start position:0%
viewers to show up. And then that gets
posted<00:08:50.399><c> to</c><00:08:50.800><c> X</c><00:08:51.360><c> or</c><00:08:51.760><c> Twitter,</c><00:08:52.480><c> Threads,</c><00:08:53.120><c> Blue</c>

00:08:53.350 --> 00:08:53.360 align:start position:0%
posted to X or Twitter, Threads, Blue
 

00:08:53.360 --> 00:08:55.990 align:start position:0%
posted to X or Twitter, Threads, Blue
Sky,<00:08:53.760><c> Mastadon,</c><00:08:54.480><c> LinkedIn,</c><00:08:55.200><c> and</c><00:08:55.440><c> a</c><00:08:55.760><c> few</c>

00:08:55.990 --> 00:08:56.000 align:start position:0%
Sky, Mastadon, LinkedIn, and a few
 

00:08:56.000 --> 00:08:58.710 align:start position:0%
Sky, Mastadon, LinkedIn, and a few
select<00:08:56.399><c> channels</c><00:08:56.720><c> on</c><00:08:57.000><c> Discord.</c><00:08:58.000><c> Then</c><00:08:58.320><c> all</c><00:08:58.560><c> I</c>

00:08:58.710 --> 00:08:58.720 align:start position:0%
select channels on Discord. Then all I
 

00:08:58.720 --> 00:09:01.190 align:start position:0%
select channels on Discord. Then all I
have<00:08:58.800><c> to</c><00:08:58.959><c> do</c><00:08:59.279><c> manually</c><00:08:59.760><c> is</c><00:09:00.000><c> to</c><00:09:00.160><c> open</c><00:09:00.399><c> up</c><00:09:00.640><c> ecam</c>

00:09:01.190 --> 00:09:01.200 align:start position:0%
have to do manually is to open up ecam
 

00:09:01.200 --> 00:09:03.269 align:start position:0%
have to do manually is to open up ecam
live,<00:09:01.519><c> which</c><00:09:01.760><c> is</c><00:09:01.920><c> the</c><00:09:02.160><c> software</c><00:09:02.560><c> I</c><00:09:02.800><c> use</c><00:09:02.959><c> to</c>

00:09:03.269 --> 00:09:03.279 align:start position:0%
live, which is the software I use to
 

00:09:03.279 --> 00:09:06.310 align:start position:0%
live, which is the software I use to
stream,<00:09:04.000><c> and</c><00:09:04.240><c> then</c><00:09:04.480><c> press</c><00:09:04.800><c> go.</c><00:09:05.519><c> And</c><00:09:05.680><c> that</c><00:09:06.000><c> set</c>

00:09:06.310 --> 00:09:06.320 align:start position:0%
stream, and then press go. And that set
 

00:09:06.320 --> 00:09:11.030 align:start position:0%
stream, and then press go. And that set
of<00:09:06.560><c> workflows</c><00:09:07.120><c> is</c><00:09:07.440><c> truly</c><00:09:08.080><c> a</c><00:09:08.399><c> gamecher</c><00:09:09.200><c> for</c><00:09:09.440><c> me.</c>

00:09:11.030 --> 00:09:11.040 align:start position:0%
of workflows is truly a gamecher for me.
 

00:09:11.040 --> 00:09:13.910 align:start position:0%
of workflows is truly a gamecher for me.
Going<00:09:11.360><c> forward,</c><00:09:12.320><c> I</c><00:09:12.640><c> may</c><00:09:12.880><c> look</c><00:09:13.040><c> at</c><00:09:13.519><c> removing</c>

00:09:13.910 --> 00:09:13.920 align:start position:0%
Going forward, I may look at removing
 

00:09:13.920 --> 00:09:16.310 align:start position:0%
Going forward, I may look at removing
the<00:09:14.160><c> web</c><00:09:14.399><c> hook</c><00:09:14.720><c> in</c><00:09:14.959><c> this</c><00:09:15.120><c> particular</c><00:09:15.519><c> example</c>

00:09:16.310 --> 00:09:16.320 align:start position:0%
the web hook in this particular example
 

00:09:16.320 --> 00:09:19.430 align:start position:0%
the web hook in this particular example
and<00:09:16.640><c> using</c><00:09:17.200><c> image</c><00:09:17.600><c> magic</c><00:09:18.080><c> on</c><00:09:18.480><c> N8N</c><00:09:19.120><c> server</c>

00:09:19.430 --> 00:09:19.440 align:start position:0%
and using image magic on N8N server
 

00:09:19.440 --> 00:09:22.710 align:start position:0%
and using image magic on N8N server
instead,<00:09:19.920><c> but</c><00:09:20.560><c> I'll</c><00:09:20.880><c> tackle</c><00:09:21.279><c> that</c><00:09:21.600><c> later</c><00:09:21.839><c> on.</c>

00:09:22.710 --> 00:09:22.720 align:start position:0%
instead, but I'll tackle that later on.
 

00:09:22.720 --> 00:09:24.630 align:start position:0%
instead, but I'll tackle that later on.
And<00:09:22.959><c> there</c><00:09:23.120><c> you</c><00:09:23.360><c> have</c><00:09:23.440><c> it.</c><00:09:23.760><c> You've</c><00:09:24.080><c> now</c><00:09:24.320><c> seen</c>

00:09:24.630 --> 00:09:24.640 align:start position:0%
And there you have it. You've now seen
 

00:09:24.640 --> 00:09:27.590 align:start position:0%
And there you have it. You've now seen
how<00:09:24.880><c> to</c><00:09:25.040><c> connect</c><00:09:25.279><c> your</c><00:09:25.519><c> remote</c><00:09:25.920><c> N8N</c><00:09:26.560><c> server</c><00:09:27.279><c> to</c>

00:09:27.590 --> 00:09:27.600 align:start position:0%
how to connect your remote N8N server to
 

00:09:27.600 --> 00:09:31.269 align:start position:0%
how to connect your remote N8N server to
your<00:09:27.839><c> local</c><00:09:28.240><c> machine</c><00:09:29.200><c> using</c><00:09:29.920><c> web</c><00:09:30.160><c> hook.</c><00:09:31.040><c> This</c>

00:09:31.269 --> 00:09:31.279 align:start position:0%
your local machine using web hook. This
 

00:09:31.279 --> 00:09:34.230 align:start position:0%
your local machine using web hook. This
opens<00:09:31.600><c> up</c><00:09:31.920><c> so</c><00:09:32.320><c> many</c><00:09:32.839><c> possibilities</c><00:09:33.839><c> beyond</c>

00:09:34.230 --> 00:09:34.240 align:start position:0%
opens up so many possibilities beyond
 

00:09:34.240 --> 00:09:36.389 align:start position:0%
opens up so many possibilities beyond
just<00:09:34.480><c> creating</c><00:09:34.880><c> Obsidian</c><00:09:35.440><c> notes.</c><00:09:36.000><c> You</c><00:09:36.240><c> could</c>

00:09:36.389 --> 00:09:36.399 align:start position:0%
just creating Obsidian notes. You could
 

00:09:36.399 --> 00:09:38.630 align:start position:0%
just creating Obsidian notes. You could
trigger<00:09:36.720><c> local</c><00:09:37.040><c> backup</c><00:09:37.519><c> scripts,</c><00:09:38.240><c> control</c>

00:09:38.630 --> 00:09:38.640 align:start position:0%
trigger local backup scripts, control
 

00:09:38.640 --> 00:09:41.350 align:start position:0%
trigger local backup scripts, control
home<00:09:38.959><c> automation,</c><00:09:39.839><c> update</c><00:09:40.240><c> local</c><00:09:40.560><c> databases,</c>

00:09:41.350 --> 00:09:41.360 align:start position:0%
home automation, update local databases,
 

00:09:41.360 --> 00:09:43.110 align:start position:0%
home automation, update local databases,
basically<00:09:41.839><c> anything</c><00:09:42.160><c> you</c><00:09:42.480><c> can</c><00:09:42.640><c> do</c><00:09:42.800><c> with</c><00:09:42.959><c> a</c>

00:09:43.110 --> 00:09:43.120 align:start position:0%
basically anything you can do with a
 

00:09:43.120 --> 00:09:45.829 align:start position:0%
basically anything you can do with a
shell<00:09:43.519><c> script.</c><00:09:44.399><c> The</c><00:09:44.720><c> real</c><00:09:44.959><c> power</c><00:09:45.279><c> here</c><00:09:45.600><c> is</c>

00:09:45.829 --> 00:09:45.839 align:start position:0%
shell script. The real power here is
 

00:09:45.839 --> 00:09:48.389 align:start position:0%
shell script. The real power here is
that<00:09:46.000><c> we're</c><00:09:46.240><c> combining</c><00:09:46.640><c> the</c><00:09:47.080><c> reliability</c><00:09:48.080><c> and</c>

00:09:48.389 --> 00:09:48.399 align:start position:0%
that we're combining the reliability and
 

00:09:48.399 --> 00:09:51.110 align:start position:0%
that we're combining the reliability and
accessibility<00:09:49.040><c> of</c><00:09:49.200><c> a</c><00:09:49.440><c> remote</c><00:09:49.760><c> N8N</c><00:09:50.480><c> server</c>

00:09:51.110 --> 00:09:51.120 align:start position:0%
accessibility of a remote N8N server
 

00:09:51.120 --> 00:09:53.190 align:start position:0%
accessibility of a remote N8N server
with<00:09:51.360><c> the</c><00:09:51.600><c> flexibility</c><00:09:52.160><c> of</c><00:09:52.480><c> local</c><00:09:52.880><c> scripts</c>

00:09:53.190 --> 00:09:53.200 align:start position:0%
with the flexibility of local scripts
 

00:09:53.200 --> 00:09:55.750 align:start position:0%
with the flexibility of local scripts
and<00:09:53.560><c> applications.</c><00:09:54.560><c> And</c><00:09:54.720><c> thanks</c><00:09:54.959><c> to</c><00:09:55.120><c> our</c><00:09:55.360><c> tail</c>

00:09:55.750 --> 00:09:55.760 align:start position:0%
and applications. And thanks to our tail
 

00:09:55.760 --> 00:09:58.150 align:start position:0%
and applications. And thanks to our tail
scale<00:09:56.000><c> setup</c><00:09:56.399><c> from</c><00:09:56.560><c> the</c><00:09:56.800><c> previous</c><00:09:57.120><c> video,</c><00:09:57.920><c> the</c>

00:09:58.150 --> 00:09:58.160 align:start position:0%
scale setup from the previous video, the
 

00:09:58.160 --> 00:10:00.550 align:start position:0%
scale setup from the previous video, the
networking<00:09:58.800><c> piece</c><00:09:59.120><c> is</c><00:09:59.600><c> already</c><00:10:00.080><c> taken</c><00:10:00.399><c> care</c>

00:10:00.550 --> 00:10:00.560 align:start position:0%
networking piece is already taken care
 

00:10:00.560 --> 00:10:03.590 align:start position:0%
networking piece is already taken care
of.<00:10:01.600><c> I</c><00:10:02.000><c> love</c><00:10:02.240><c> working</c><00:10:02.560><c> with</c><00:10:02.800><c> these</c><00:10:03.040><c> tools</c><00:10:03.360><c> and</c>

00:10:03.590 --> 00:10:03.600 align:start position:0%
of. I love working with these tools and
 

00:10:03.600 --> 00:10:07.310 align:start position:0%
of. I love working with these tools and
they<00:10:03.839><c> do</c><00:10:04.000><c> an</c><00:10:04.240><c> amazing</c><00:10:04.800><c> job</c><00:10:05.440><c> of</c><00:10:05.839><c> making</c><00:10:06.160><c> my</c><00:10:06.480><c> life</c>

00:10:07.310 --> 00:10:07.320 align:start position:0%
they do an amazing job of making my life
 

00:10:07.320 --> 00:10:09.509 align:start position:0%
they do an amazing job of making my life
easier.<00:10:08.320><c> One</c><00:10:08.560><c> of</c><00:10:08.640><c> the</c><00:10:08.800><c> features</c><00:10:09.040><c> that</c><00:10:09.279><c> makes</c>

00:10:09.509 --> 00:10:09.519 align:start position:0%
easier. One of the features that makes
 

00:10:09.519 --> 00:10:11.910 align:start position:0%
easier. One of the features that makes
this<00:10:09.839><c> possible</c><00:10:10.160><c> is</c><00:10:10.399><c> that</c><00:10:10.560><c> I</c><00:10:10.720><c> can</c><00:10:10.880><c> call</c><00:10:11.279><c> Olama</c>

00:10:11.910 --> 00:10:11.920 align:start position:0%
this possible is that I can call Olama
 

00:10:11.920 --> 00:10:14.389 align:start position:0%
this possible is that I can call Olama
on<00:10:12.160><c> my</c><00:10:12.399><c> local</c><00:10:12.720><c> machine.</c><00:10:13.600><c> So,</c><00:10:13.839><c> make</c><00:10:14.000><c> sure</c><00:10:14.160><c> you</c>

00:10:14.389 --> 00:10:14.399 align:start position:0%
on my local machine. So, make sure you
 

00:10:14.399 --> 00:10:17.190 align:start position:0%
on my local machine. So, make sure you
watch<00:10:14.720><c> this</c><00:10:15.040><c> video</c><00:10:15.680><c> next</c><00:10:16.160><c> that</c><00:10:16.480><c> shows</c><00:10:16.720><c> you</c><00:10:16.959><c> how</c>

00:10:17.190 --> 00:10:17.200 align:start position:0%
watch this video next that shows you how
 

00:10:17.200 --> 00:10:20.590 align:start position:0%
watch this video next that shows you how
to<00:10:17.360><c> do</c><00:10:17.600><c> that.</c><00:10:18.480><c> Until</c><00:10:18.959><c> next</c><00:10:19.200><c> time,</c><00:10:19.920><c> happy</c>

00:10:20.590 --> 00:10:20.600 align:start position:0%
to do that. Until next time, happy
 

00:10:20.600 --> 00:10:24.600 align:start position:0%
to do that. Until next time, happy
automating.<00:10:21.600><c> Bye.</c>

