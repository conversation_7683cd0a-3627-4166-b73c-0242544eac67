WEBVTT
Kind: captions
Language: en

00:00:01.000 --> 00:00:03.270 align:start position:0%
 
hi<00:00:01.599><c> in</c><00:00:01.760><c> this</c><00:00:01.920><c> video</c><00:00:02.399><c> I'm</c><00:00:02.480><c> going</c><00:00:02.600><c> to</c><00:00:02.720><c> show</c><00:00:02.960><c> you</c>

00:00:03.270 --> 00:00:03.280 align:start position:0%
hi in this video I'm going to show you
 

00:00:03.280 --> 00:00:05.710 align:start position:0%
hi in this video I'm going to show you
how<00:00:03.520><c> you</c><00:00:03.639><c> can</c><00:00:03.840><c> build</c><00:00:04.120><c> a</c><00:00:04.279><c> plug-in</c><00:00:04.600><c> for</c><00:00:04.880><c> obsidian</c>

00:00:05.710 --> 00:00:05.720 align:start position:0%
how you can build a plug-in for obsidian
 

00:00:05.720 --> 00:00:08.669 align:start position:0%
how you can build a plug-in for obsidian
that<00:00:05.920><c> uses</c><00:00:06.319><c> the</c><00:00:06.640><c> power</c><00:00:06.919><c> of</c><00:00:07.200><c> AI</c><00:00:07.759><c> but</c><00:00:08.320><c> doesn't</c>

00:00:08.669 --> 00:00:08.679 align:start position:0%
that uses the power of AI but doesn't
 

00:00:08.679 --> 00:00:10.350 align:start position:0%
that uses the power of AI but doesn't
use<00:00:09.040><c> chat</c>

00:00:10.350 --> 00:00:10.360 align:start position:0%
use chat
 

00:00:10.360 --> 00:00:15.030 align:start position:0%
use chat
GPT<00:00:11.360><c> mostly</c><00:00:12.040><c> uh</c><00:00:12.360><c> sort</c><00:00:12.639><c> of</c><00:00:13.599><c> okay</c><00:00:14.000><c> today</c><00:00:14.519><c> I</c><00:00:14.599><c> saw</c><00:00:14.879><c> a</c>

00:00:15.030 --> 00:00:15.040 align:start position:0%
GPT mostly uh sort of okay today I saw a
 

00:00:15.040 --> 00:00:17.510 align:start position:0%
GPT mostly uh sort of okay today I saw a
post<00:00:15.280><c> on</c><00:00:15.480><c> Hacker</c><00:00:15.759><c> News</c><00:00:16.279><c> about</c><00:00:16.760><c> another</c><00:00:17.160><c> plugin</c>

00:00:17.510 --> 00:00:17.520 align:start position:0%
post on Hacker News about another plugin
 

00:00:17.520 --> 00:00:19.910 align:start position:0%
post on Hacker News about another plugin
for<00:00:17.760><c> obsidian</c><00:00:18.439><c> that</c><00:00:18.680><c> integrates</c><00:00:19.279><c> with</c><00:00:19.480><c> chat</c>

00:00:19.910 --> 00:00:19.920 align:start position:0%
for obsidian that integrates with chat
 

00:00:19.920 --> 00:00:22.590 align:start position:0%
for obsidian that integrates with chat
gbt<00:00:20.920><c> there</c><00:00:21.039><c> are</c><00:00:21.320><c> a</c><00:00:21.519><c> bunch</c><00:00:21.720><c> of</c><00:00:21.880><c> these</c><00:00:22.039><c> tools</c><00:00:22.400><c> out</c>

00:00:22.590 --> 00:00:22.600 align:start position:0%
gbt there are a bunch of these tools out
 

00:00:22.600 --> 00:00:24.230 align:start position:0%
gbt there are a bunch of these tools out
there<00:00:22.880><c> and</c><00:00:23.000><c> I</c><00:00:23.199><c> love</c><00:00:23.400><c> seeing</c><00:00:23.720><c> the</c><00:00:23.920><c> different</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
there and I love seeing the different
 

00:00:24.240 --> 00:00:26.790 align:start position:0%
there and I love seeing the different
ways<00:00:24.480><c> to</c><00:00:24.680><c> use</c><00:00:24.960><c> them</c><00:00:25.560><c> with</c><00:00:25.680><c> the</c><00:00:25.800><c> notetaker</c><00:00:26.640><c> I</c>

00:00:26.790 --> 00:00:26.800 align:start position:0%
ways to use them with the notetaker I
 

00:00:26.800 --> 00:00:29.189 align:start position:0%
ways to use them with the notetaker I
already<00:00:27.119><c> love</c><00:00:27.920><c> making</c><00:00:28.359><c> connections</c><00:00:28.960><c> letting</c>

00:00:29.189 --> 00:00:29.199 align:start position:0%
already love making connections letting
 

00:00:29.199 --> 00:00:32.310 align:start position:0%
already love making connections letting
you<00:00:29.400><c> go</c><00:00:29.560><c> further</c><00:00:30.160><c> with</c><00:00:30.240><c> your</c><00:00:30.679><c> notes</c>

00:00:32.310 --> 00:00:32.320 align:start position:0%
you go further with your notes
 

00:00:32.320 --> 00:00:37.069 align:start position:0%
you go further with your notes
but<00:00:33.320><c> when</c><00:00:33.480><c> you</c><00:00:33.640><c> use</c><00:00:33.879><c> chat</c><00:00:34.520><c> GPT</c><00:00:35.520><c> I</c><00:00:35.840><c> get</c><00:00:36.520><c> a</c><00:00:36.680><c> bit</c>

00:00:37.069 --> 00:00:37.079 align:start position:0%
but when you use chat GPT I get a bit
 

00:00:37.079 --> 00:00:39.750 align:start position:0%
but when you use chat GPT I get a bit
nervous<00:00:38.079><c> there</c><00:00:38.160><c> are</c><00:00:38.399><c> plenty</c><00:00:38.680><c> of</c><00:00:38.920><c> privacy</c><00:00:39.480><c> and</c>

00:00:39.750 --> 00:00:39.760 align:start position:0%
nervous there are plenty of privacy and
 

00:00:39.760 --> 00:00:41.910 align:start position:0%
nervous there are plenty of privacy and
security<00:00:40.200><c> issues</c><00:00:40.559><c> you</c><00:00:40.680><c> should</c><00:00:40.879><c> know</c><00:00:41.160><c> about</c>

00:00:41.910 --> 00:00:41.920 align:start position:0%
security issues you should know about
 

00:00:41.920 --> 00:00:44.670 align:start position:0%
security issues you should know about
and<00:00:42.120><c> while</c><00:00:42.360><c> there</c><00:00:42.480><c> is</c><00:00:42.640><c> an</c><00:00:42.920><c> opt</c><00:00:43.200><c> out</c><00:00:43.840><c> for</c><00:00:44.320><c> using</c>

00:00:44.670 --> 00:00:44.680 align:start position:0%
and while there is an opt out for using
 

00:00:44.680 --> 00:00:46.549 align:start position:0%
and while there is an opt out for using
your<00:00:44.960><c> prompts</c><00:00:45.360><c> and</c><00:00:45.559><c> answers</c><00:00:46.000><c> to</c><00:00:46.160><c> improve</c><00:00:46.440><c> the</c>

00:00:46.549 --> 00:00:46.559 align:start position:0%
your prompts and answers to improve the
 

00:00:46.559 --> 00:00:49.510 align:start position:0%
your prompts and answers to improve the
models<00:00:47.199><c> it's</c><00:00:47.440><c> not</c><00:00:47.840><c> super</c><00:00:48.320><c> obvious</c><00:00:49.000><c> and</c><00:00:49.280><c> some</c>

00:00:49.510 --> 00:00:49.520 align:start position:0%
models it's not super obvious and some
 

00:00:49.520 --> 00:00:51.549 align:start position:0%
models it's not super obvious and some
companies<00:00:49.960><c> have</c><00:00:50.160><c> banned</c><00:00:50.559><c> their</c><00:00:50.760><c> use</c><00:00:51.320><c> when</c>

00:00:51.549 --> 00:00:51.559 align:start position:0%
companies have banned their use when
 

00:00:51.559 --> 00:00:53.990 align:start position:0%
companies have banned their use when
sensitive<00:00:52.079><c> info</c><00:00:52.760><c> made</c><00:00:53.000><c> its</c><00:00:53.239><c> way</c><00:00:53.600><c> into</c><00:00:53.840><c> the</c>

00:00:53.990 --> 00:00:54.000 align:start position:0%
sensitive info made its way into the
 

00:00:54.000 --> 00:00:57.029 align:start position:0%
sensitive info made its way into the
model<00:00:55.000><c> so</c><00:00:55.399><c> are</c><00:00:55.600><c> there</c><00:00:55.879><c> alternatives</c><00:00:56.559><c> to</c><00:00:56.719><c> using</c>

00:00:57.029 --> 00:00:57.039 align:start position:0%
model so are there alternatives to using
 

00:00:57.039 --> 00:01:00.470 align:start position:0%
model so are there alternatives to using
hosted<00:00:57.520><c> AI</c><00:00:58.280><c> Services</c><00:00:59.280><c> there</c><00:00:59.399><c> sure</c><00:00:59.680><c> are</c>

00:01:00.470 --> 00:01:00.480 align:start position:0%
hosted AI Services there sure are
 

00:01:00.480 --> 00:01:02.189 align:start position:0%
hosted AI Services there sure are
but<00:01:00.640><c> I</c><00:01:00.760><c> haven't</c><00:01:01.000><c> seen</c><00:01:01.320><c> anything</c><00:01:01.719><c> make</c><00:01:01.879><c> its</c><00:01:02.039><c> way</c>

00:01:02.189 --> 00:01:02.199 align:start position:0%
but I haven't seen anything make its way
 

00:01:02.199 --> 00:01:05.070 align:start position:0%
but I haven't seen anything make its way
into<00:01:02.519><c> obsidian</c><00:01:03.079><c> yet</c><00:01:03.760><c> so</c><00:01:04.080><c> in</c><00:01:04.280><c> this</c><00:01:04.479><c> video</c><00:01:05.000><c> I</c>

00:01:05.070 --> 00:01:05.080 align:start position:0%
into obsidian yet so in this video I
 

00:01:05.080 --> 00:01:07.230 align:start position:0%
into obsidian yet so in this video I
want<00:01:05.199><c> to</c><00:01:05.320><c> show</c><00:01:05.560><c> you</c><00:01:05.840><c> how</c><00:01:06.119><c> easy</c><00:01:06.400><c> it</c><00:01:06.520><c> would</c><00:01:06.720><c> be</c><00:01:07.000><c> to</c>

00:01:07.230 --> 00:01:07.240 align:start position:0%
want to show you how easy it would be to
 

00:01:07.240 --> 00:01:09.429 align:start position:0%
want to show you how easy it would be to
get<00:01:07.360><c> it</c><00:01:07.520><c> done</c><00:01:08.479><c> now</c><00:01:08.680><c> I'm</c><00:01:08.799><c> going</c><00:01:08.880><c> to</c><00:01:09.000><c> be</c><00:01:09.119><c> using</c>

00:01:09.429 --> 00:01:09.439 align:start position:0%
get it done now I'm going to be using
 

00:01:09.439 --> 00:01:11.310 align:start position:0%
get it done now I'm going to be using
olama<00:01:10.080><c> because</c><00:01:10.360><c> well</c><00:01:10.880><c> I'm</c><00:01:11.000><c> one</c><00:01:11.119><c> of</c><00:01:11.240><c> the</c>

00:01:11.310 --> 00:01:11.320 align:start position:0%
olama because well I'm one of the
 

00:01:11.320 --> 00:01:13.429 align:start position:0%
olama because well I'm one of the
maintainers<00:01:11.799><c> of</c><00:01:11.920><c> the</c><00:01:12.119><c> project</c><00:01:12.840><c> but</c><00:01:13.040><c> there</c><00:01:13.159><c> are</c>

00:01:13.429 --> 00:01:13.439 align:start position:0%
maintainers of the project but there are
 

00:01:13.439 --> 00:01:15.550 align:start position:0%
maintainers of the project but there are
other<00:01:13.720><c> Alternatives</c><00:01:14.400><c> like</c><00:01:14.600><c> LM</c><00:01:15.000><c> studio</c><00:01:15.400><c> and</c>

00:01:15.550 --> 00:01:15.560 align:start position:0%
other Alternatives like LM studio and
 

00:01:15.560 --> 00:01:18.390 align:start position:0%
other Alternatives like LM studio and
GPT<00:01:16.080><c> for</c><00:01:16.280><c> all</c><00:01:16.720><c> as</c><00:01:16.920><c> well</c><00:01:17.720><c> you</c><00:01:17.799><c> can</c><00:01:18.000><c> find</c><00:01:18.159><c> out</c>

00:01:18.390 --> 00:01:18.400 align:start position:0%
GPT for all as well you can find out
 

00:01:18.400 --> 00:01:21.109 align:start position:0%
GPT for all as well you can find out
more<00:01:18.680><c> about</c><00:01:18.920><c> olama</c><00:01:19.680><c> and</c><00:01:19.840><c> install</c><00:01:20.200><c> it</c><00:01:20.600><c> by</c><00:01:20.720><c> going</c>

00:01:21.109 --> 00:01:21.119 align:start position:0%
more about olama and install it by going
 

00:01:21.119 --> 00:01:25.109 align:start position:0%
more about olama and install it by going
to.<00:01:22.439><c> a</c><00:01:23.439><c> okay</c><00:01:24.000><c> now</c><00:01:24.280><c> the</c><00:01:24.479><c> first</c><00:01:24.720><c> thing</c><00:01:24.880><c> you</c>

00:01:25.109 --> 00:01:25.119 align:start position:0%
to. a okay now the first thing you
 

00:01:25.119 --> 00:01:26.590 align:start position:0%
to. a okay now the first thing you
probably<00:01:25.360><c> want</c><00:01:25.439><c> to</c><00:01:25.640><c> do</c><00:01:26.000><c> is</c><00:01:26.119><c> to</c><00:01:26.240><c> be</c><00:01:26.320><c> able</c><00:01:26.479><c> to</c>

00:01:26.590 --> 00:01:26.600 align:start position:0%
probably want to do is to be able to
 

00:01:26.600 --> 00:01:28.429 align:start position:0%
probably want to do is to be able to
converse<00:01:26.960><c> with</c><00:01:27.079><c> your</c><00:01:27.200><c> notes</c><00:01:27.960><c> ask</c><00:01:28.159><c> it</c>

00:01:28.429 --> 00:01:28.439 align:start position:0%
converse with your notes ask it
 

00:01:28.439 --> 00:01:31.149 align:start position:0%
converse with your notes ask it
questions<00:01:29.360><c> to</c><00:01:29.560><c> achieve</c><00:01:30.159><c> this</c><00:01:30.400><c> shipping</c><00:01:30.840><c> off</c>

00:01:31.149 --> 00:01:31.159 align:start position:0%
questions to achieve this shipping off
 

00:01:31.159 --> 00:01:33.149 align:start position:0%
questions to achieve this shipping off
all<00:01:31.320><c> your</c><00:01:31.479><c> notes</c><00:01:31.759><c> to</c><00:01:31.920><c> the</c><00:01:32.040><c> AI</c><00:01:32.600><c> would</c><00:01:32.759><c> be</c><00:01:32.920><c> too</c>

00:01:33.149 --> 00:01:33.159 align:start position:0%
all your notes to the AI would be too
 

00:01:33.159 --> 00:01:35.830 align:start position:0%
all your notes to the AI would be too
much<00:01:33.880><c> most</c><00:01:34.119><c> models</c><00:01:34.520><c> can't</c><00:01:34.759><c> even</c><00:01:35.079><c> accept</c><00:01:35.640><c> all</c>

00:01:35.830 --> 00:01:35.840 align:start position:0%
much most models can't even accept all
 

00:01:35.840 --> 00:01:38.510 align:start position:0%
much most models can't even accept all
the<00:01:36.040><c> content</c><00:01:36.560><c> all</c><00:01:36.759><c> at</c><00:01:36.920><c> once</c><00:01:37.880><c> when</c><00:01:38.000><c> you</c><00:01:38.119><c> ask</c><00:01:38.320><c> a</c>

00:01:38.510 --> 00:01:38.520 align:start position:0%
the content all at once when you ask a
 

00:01:38.520 --> 00:01:40.550 align:start position:0%
the content all at once when you ask a
question<00:01:39.079><c> not</c><00:01:39.399><c> all</c><00:01:39.560><c> of</c><00:01:39.680><c> your</c><00:01:39.840><c> notes</c><00:01:40.280><c> are</c>

00:01:40.550 --> 00:01:40.560 align:start position:0%
question not all of your notes are
 

00:01:40.560 --> 00:01:42.830 align:start position:0%
question not all of your notes are
relevant<00:01:41.479><c> so</c><00:01:41.640><c> you</c><00:01:41.759><c> need</c><00:01:41.920><c> to</c><00:01:42.119><c> find</c><00:01:42.399><c> the</c><00:01:42.560><c> parts</c>

00:01:42.830 --> 00:01:42.840 align:start position:0%
relevant so you need to find the parts
 

00:01:42.840 --> 00:01:45.310 align:start position:0%
relevant so you need to find the parts
that<00:01:42.960><c> are</c><00:01:43.159><c> relevant</c><00:01:43.640><c> and</c><00:01:43.880><c> hand</c><00:01:44.320><c> that</c><00:01:44.960><c> to</c><00:01:45.119><c> the</c>

00:01:45.310 --> 00:01:45.320 align:start position:0%
that are relevant and hand that to the
 

00:01:45.320 --> 00:01:48.030 align:start position:0%
that are relevant and hand that to the
model<00:01:46.320><c> obsidian</c><00:01:46.840><c> has</c><00:01:47.000><c> search</c><00:01:47.520><c> but</c><00:01:47.640><c> it's</c><00:01:47.880><c> just</c>

00:01:48.030 --> 00:01:48.040 align:start position:0%
model obsidian has search but it's just
 

00:01:48.040 --> 00:01:50.630 align:start position:0%
model obsidian has search but it's just
searching<00:01:48.399><c> for</c><00:01:48.680><c> exact</c><00:01:49.079><c> words</c><00:01:49.479><c> and</c><00:01:49.640><c> phrases</c>

00:01:50.630 --> 00:01:50.640 align:start position:0%
searching for exact words and phrases
 

00:01:50.640 --> 00:01:53.270 align:start position:0%
searching for exact words and phrases
and<00:01:50.759><c> we</c><00:01:50.880><c> need</c><00:01:51.000><c> to</c><00:01:51.159><c> search</c><00:01:51.439><c> for</c><00:01:52.119><c> Concepts</c><00:01:53.119><c> and</c>

00:01:53.270 --> 00:01:53.280 align:start position:0%
and we need to search for Concepts and
 

00:01:53.280 --> 00:01:55.550 align:start position:0%
and we need to search for Concepts and
that's<00:01:53.479><c> where</c><00:01:53.680><c> embeddings</c><00:01:54.320><c> come</c><00:01:54.520><c> in</c><00:01:55.240><c> so</c><00:01:55.439><c> we</c>

00:01:55.550 --> 00:01:55.560 align:start position:0%
that's where embeddings come in so we
 

00:01:55.560 --> 00:01:57.910 align:start position:0%
that's where embeddings come in so we
need<00:01:55.719><c> to</c><00:01:55.960><c> create</c><00:01:56.439><c> an</c><00:01:56.680><c> index</c><00:01:57.399><c> and</c><00:01:57.439><c> it</c><00:01:57.560><c> turns</c><00:01:57.759><c> out</c>

00:01:57.910 --> 00:01:57.920 align:start position:0%
need to create an index and it turns out
 

00:01:57.920 --> 00:02:00.109 align:start position:0%
need to create an index and it turns out
it's<00:01:58.119><c> pretty</c><00:01:58.360><c> easy</c><00:01:58.560><c> to</c><00:01:58.719><c> do</c><00:01:59.479><c> when</c><00:01:59.600><c> you</c><00:01:59.880><c> create</c>

00:02:00.109 --> 00:02:00.119 align:start position:0%
it's pretty easy to do when you create
 

00:02:00.119 --> 00:02:02.230 align:start position:0%
it's pretty easy to do when you create
an<00:02:00.280><c> obsidian</c><00:02:00.759><c> plugin</c><00:02:01.360><c> you</c><00:02:01.439><c> can</c><00:02:01.680><c> have</c><00:02:01.799><c> it</c><00:02:02.000><c> do</c>

00:02:02.230 --> 00:02:02.240 align:start position:0%
an obsidian plugin you can have it do
 

00:02:02.240 --> 00:02:04.389 align:start position:0%
an obsidian plugin you can have it do
something<00:02:02.560><c> when</c><00:02:02.719><c> the</c><00:02:02.840><c> plugin</c><00:02:03.200><c> loads</c><00:02:03.640><c> up</c><00:02:04.240><c> and</c>

00:02:04.389 --> 00:02:04.399 align:start position:0%
something when the plugin loads up and
 

00:02:04.399 --> 00:02:05.789 align:start position:0%
something when the plugin loads up and
then<00:02:04.640><c> other</c><00:02:04.840><c> things</c><00:02:05.079><c> when</c><00:02:05.240><c> you</c><00:02:05.360><c> trigger</c><00:02:05.680><c> a</c>

00:02:05.789 --> 00:02:05.799 align:start position:0%
then other things when you trigger a
 

00:02:05.799 --> 00:02:07.469 align:start position:0%
then other things when you trigger a
command<00:02:06.200><c> or</c><00:02:06.360><c> open</c><00:02:06.560><c> a</c><00:02:06.680><c> note</c><00:02:07.039><c> or</c><00:02:07.240><c> other</c>

00:02:07.469 --> 00:02:07.479 align:start position:0%
command or open a note or other
 

00:02:07.479 --> 00:02:10.070 align:start position:0%
command or open a note or other
activities<00:02:08.160><c> in</c><00:02:08.520><c> obsidian</c><00:02:09.520><c> so</c><00:02:09.720><c> we</c><00:02:09.840><c> want</c>

00:02:10.070 --> 00:02:10.080 align:start position:0%
activities in obsidian so we want
 

00:02:10.080 --> 00:02:11.710 align:start position:0%
activities in obsidian so we want
something<00:02:10.360><c> to</c><00:02:10.840><c> understand</c><00:02:11.000><c> your</c><00:02:11.200><c> notes</c><00:02:11.599><c> at</c>

00:02:11.710 --> 00:02:11.720 align:start position:0%
something to understand your notes at
 

00:02:11.720 --> 00:02:13.750 align:start position:0%
something to understand your notes at
the<00:02:11.879><c> start</c><00:02:12.120><c> of</c><00:02:12.239><c> the</c><00:02:12.360><c> plug-in</c><00:02:13.280><c> and</c><00:02:13.480><c> it</c><00:02:13.560><c> should</c>

00:02:13.750 --> 00:02:13.760 align:start position:0%
the start of the plug-in and it should
 

00:02:13.760 --> 00:02:15.270 align:start position:0%
the start of the plug-in and it should
save<00:02:14.000><c> its</c><00:02:14.200><c> progress</c><00:02:14.480><c> so</c><00:02:14.599><c> it</c><00:02:14.680><c> doesn't</c><00:02:14.920><c> have</c><00:02:15.040><c> to</c>

00:02:15.270 --> 00:02:15.280 align:start position:0%
save its progress so it doesn't have to
 

00:02:15.280 --> 00:02:17.790 align:start position:0%
save its progress so it doesn't have to
reindex<00:02:16.080><c> again</c><00:02:17.000><c> let's</c><00:02:17.160><c> look</c><00:02:17.319><c> at</c><00:02:17.440><c> a</c><00:02:17.560><c> code</c>

00:02:17.790 --> 00:02:17.800 align:start position:0%
reindex again let's look at a code
 

00:02:17.800 --> 00:02:19.949 align:start position:0%
reindex again let's look at a code
sample<00:02:18.120><c> to</c><00:02:18.280><c> index</c><00:02:18.720><c> one</c><00:02:18.840><c> of</c><00:02:18.959><c> our</c><00:02:19.080><c> notes</c><00:02:19.840><c> I'm</c>

00:02:19.949 --> 00:02:19.959 align:start position:0%
sample to index one of our notes I'm
 

00:02:19.959 --> 00:02:21.949 align:start position:0%
sample to index one of our notes I'm
going<00:02:20.120><c> to</c><00:02:20.239><c> use</c><00:02:20.440><c> llama</c><00:02:20.800><c> index</c><00:02:21.160><c> in</c><00:02:21.319><c> this</c><00:02:21.440><c> one</c><00:02:21.680><c> but</c>

00:02:21.949 --> 00:02:21.959 align:start position:0%
going to use llama index in this one but
 

00:02:21.959 --> 00:02:24.910 align:start position:0%
going to use llama index in this one but
Lang<00:02:22.239><c> chain</c><00:02:22.560><c> is</c><00:02:22.760><c> another</c><00:02:23.200><c> great</c><00:02:23.640><c> option</c><00:02:24.640><c> first</c>

00:02:24.910 --> 00:02:24.920 align:start position:0%
Lang chain is another great option first
 

00:02:24.920 --> 00:02:27.030 align:start position:0%
Lang chain is another great option first
we<00:02:25.040><c> need</c><00:02:25.200><c> to</c><00:02:25.400><c> initialize</c><00:02:25.920><c> an</c><00:02:26.120><c> in-memory</c><00:02:26.800><c> data</c>

00:02:27.030 --> 00:02:27.040 align:start position:0%
we need to initialize an in-memory data
 

00:02:27.040 --> 00:02:29.350 align:start position:0%
we need to initialize an in-memory data
store<00:02:27.959><c> this</c><00:02:28.080><c> is</c><00:02:28.239><c> the</c><00:02:28.360><c> inmemory</c><00:02:28.840><c> store</c><00:02:29.239><c> that</c>

00:02:29.350 --> 00:02:29.360 align:start position:0%
store this is the inmemory store that
 

00:02:29.360 --> 00:02:32.150 align:start position:0%
store this is the inmemory store that
comes<00:02:29.840><c> with</c><00:02:29.959><c> llama</c><00:02:30.280><c> index</c><00:02:30.720><c> but</c><00:02:31.080><c> chroma</c><00:02:31.480><c> DB</c><00:02:31.959><c> is</c>

00:02:32.150 --> 00:02:32.160 align:start position:0%
comes with llama index but chroma DB is
 

00:02:32.160 --> 00:02:34.470 align:start position:0%
comes with llama index but chroma DB is
another<00:02:32.519><c> popular</c><00:02:32.920><c> option</c><00:02:33.840><c> and</c><00:02:34.000><c> this</c><00:02:34.200><c> second</c>

00:02:34.470 --> 00:02:34.480 align:start position:0%
another popular option and this second
 

00:02:34.480 --> 00:02:36.270 align:start position:0%
another popular option and this second
line<00:02:34.879><c> is</c><00:02:35.040><c> saying</c><00:02:35.360><c> that</c><00:02:35.480><c> we'll</c><00:02:35.720><c> persist</c>

00:02:36.270 --> 00:02:36.280 align:start position:0%
line is saying that we'll persist
 

00:02:36.280 --> 00:02:39.309 align:start position:0%
line is saying that we'll persist
everything<00:02:36.879><c> we</c><00:02:37.360><c> index</c><00:02:38.360><c> next</c><00:02:38.640><c> I</c><00:02:38.760><c> get</c><00:02:38.920><c> the</c><00:02:39.120><c> path</c>

00:02:39.309 --> 00:02:39.319 align:start position:0%
everything we index next I get the path
 

00:02:39.319 --> 00:02:41.630 align:start position:0%
everything we index next I get the path
for<00:02:39.480><c> the</c><00:02:39.640><c> file</c><00:02:39.959><c> and</c><00:02:40.200><c> initialize</c><00:02:40.640><c> a</c><00:02:40.800><c> reader</c>

00:02:41.630 --> 00:02:41.640 align:start position:0%
for the file and initialize a reader
 

00:02:41.640 --> 00:02:44.030 align:start position:0%
for the file and initialize a reader
then<00:02:41.800><c> I</c><00:02:41.920><c> read</c><00:02:42.200><c> the</c><00:02:42.360><c> file</c><00:02:43.080><c> and</c><00:02:43.319><c> llama</c><00:02:43.720><c> index</c>

00:02:44.030 --> 00:02:44.040 align:start position:0%
then I read the file and llama index
 

00:02:44.040 --> 00:02:45.990 align:start position:0%
then I read the file and llama index
knows<00:02:44.319><c> about</c><00:02:44.480><c> markdown</c><00:02:45.000><c> so</c><00:02:45.120><c> it</c><00:02:45.239><c> reads</c><00:02:45.519><c> it</c><00:02:45.640><c> in</c>

00:02:45.990 --> 00:02:46.000 align:start position:0%
knows about markdown so it reads it in
 

00:02:46.000 --> 00:02:48.390 align:start position:0%
knows about markdown so it reads it in
appropriately<00:02:46.800><c> and</c><00:02:46.959><c> indexes</c><00:02:47.480><c> it</c><00:02:48.080><c> it</c><00:02:48.200><c> also</c>

00:02:48.390 --> 00:02:48.400 align:start position:0%
appropriately and indexes it it also
 

00:02:48.400 --> 00:02:50.430 align:start position:0%
appropriately and indexes it it also
knows<00:02:48.640><c> about</c><00:02:48.959><c> PDFs</c><00:02:49.560><c> and</c><00:02:49.760><c> text</c><00:02:50.000><c> files</c><00:02:50.319><c> and</c>

00:02:50.430 --> 00:02:50.440 align:start position:0%
knows about PDFs and text files and
 

00:02:50.440 --> 00:02:52.790 align:start position:0%
knows about PDFs and text files and
notion<00:02:50.800><c> docs</c><00:02:51.319><c> and</c><00:02:51.480><c> more</c><00:02:52.319><c> it's</c><00:02:52.519><c> not</c><00:02:52.680><c> just</c>

00:02:52.790 --> 00:02:52.800 align:start position:0%
notion docs and more it's not just
 

00:02:52.800 --> 00:02:54.830 align:start position:0%
notion docs and more it's not just
storing<00:02:53.159><c> words</c><00:02:53.519><c> but</c><00:02:53.800><c> also</c><00:02:54.440><c> understanding</c><00:02:54.680><c> the</c>

00:02:54.830 --> 00:02:54.840 align:start position:0%
storing words but also understanding the
 

00:02:54.840 --> 00:02:56.550 align:start position:0%
storing words but also understanding the
meanings<00:02:55.239><c> of</c><00:02:55.360><c> the</c><00:02:55.480><c> words</c><00:02:56.080><c> and</c><00:02:56.239><c> how</c><00:02:56.400><c> they</c>

00:02:56.550 --> 00:02:56.560 align:start position:0%
meanings of the words and how they
 

00:02:56.560 --> 00:02:59.350 align:start position:0%
meanings of the words and how they
relate<00:02:56.879><c> to</c><00:02:57.080><c> other</c><00:02:57.280><c> words</c><00:02:57.720><c> in</c><00:02:57.879><c> this</c><00:02:58.159><c> text</c><00:02:59.159><c> now</c>

00:02:59.350 --> 00:02:59.360 align:start position:0%
relate to other words in this text now
 

00:02:59.360 --> 00:03:01.309 align:start position:0%
relate to other words in this text now
this<00:02:59.519><c> part</c><00:02:59.920><c> is</c><00:03:00.000><c> using</c><00:03:00.239><c> a</c><00:03:00.400><c> service</c><00:03:00.760><c> from</c><00:03:00.959><c> open</c>

00:03:01.309 --> 00:03:01.319 align:start position:0%
this part is using a service from open
 

00:03:01.319 --> 00:03:03.990 align:start position:0%
this part is using a service from open
AI<00:03:02.159><c> but</c><00:03:02.319><c> it's</c><00:03:02.560><c> separate</c><00:03:02.879><c> from</c><00:03:03.040><c> chat</c><00:03:03.319><c> GPT</c>

00:03:03.990 --> 00:03:04.000 align:start position:0%
AI but it's separate from chat GPT
 

00:03:04.000 --> 00:03:06.070 align:start position:0%
AI but it's separate from chat GPT
different<00:03:04.280><c> model</c><00:03:04.920><c> different</c><00:03:05.280><c> product</c><00:03:06.000><c> and</c>

00:03:06.070 --> 00:03:06.080 align:start position:0%
different model different product and
 

00:03:06.080 --> 00:03:08.190 align:start position:0%
different model different product and
there<00:03:06.200><c> are</c><00:03:06.480><c> alternatives</c><00:03:07.080><c> in</c><00:03:07.239><c> Lang</c><00:03:07.519><c> chain</c>

00:03:08.190 --> 00:03:08.200 align:start position:0%
there are alternatives in Lang chain
 

00:03:08.200 --> 00:03:10.270 align:start position:0%
there are alternatives in Lang chain
that<00:03:08.319><c> will</c><00:03:08.480><c> do</c><00:03:08.680><c> this</c><00:03:08.879><c> locally</c><00:03:09.440><c> but</c><00:03:10.040><c> a</c><00:03:10.120><c> little</c>

00:03:10.270 --> 00:03:10.280 align:start position:0%
that will do this locally but a little
 

00:03:10.280 --> 00:03:13.190 align:start position:0%
that will do this locally but a little
bit<00:03:10.440><c> slower</c><00:03:11.360><c> AMA</c><00:03:12.040><c> also</c><00:03:12.360><c> has</c><00:03:12.599><c> an</c><00:03:12.799><c> embed</c>

00:03:13.190 --> 00:03:13.200 align:start position:0%
bit slower AMA also has an embed
 

00:03:13.200 --> 00:03:15.070 align:start position:0%
bit slower AMA also has an embed
function<00:03:14.000><c> you</c><00:03:14.120><c> could</c><00:03:14.319><c> also</c><00:03:14.560><c> use</c><00:03:14.879><c> those</c>

00:03:15.070 --> 00:03:15.080 align:start position:0%
function you could also use those
 

00:03:15.080 --> 00:03:17.470 align:start position:0%
function you could also use those
services<00:03:15.720><c> on</c><00:03:15.920><c> a</c><00:03:16.080><c> super</c><00:03:16.440><c> fast</c><00:03:16.720><c> self-hosted</c>

00:03:17.470 --> 00:03:17.480 align:start position:0%
services on a super fast self-hosted
 

00:03:17.480 --> 00:03:19.430 align:start position:0%
services on a super fast self-hosted
instance<00:03:17.920><c> in</c><00:03:18.040><c> the</c><00:03:18.200><c> cloud</c><00:03:18.879><c> and</c><00:03:18.959><c> then</c><00:03:19.120><c> close</c>

00:03:19.430 --> 00:03:19.440 align:start position:0%
instance in the cloud and then close
 

00:03:19.440 --> 00:03:22.110 align:start position:0%
instance in the cloud and then close
that<00:03:19.599><c> down</c><00:03:19.840><c> when</c><00:03:20.040><c> indexing</c><00:03:20.480><c> is</c><00:03:20.840><c> done</c><00:03:21.840><c> now</c><00:03:22.000><c> we</c>

00:03:22.110 --> 00:03:22.120 align:start position:0%
that down when indexing is done now we
 

00:03:22.120 --> 00:03:24.949 align:start position:0%
that down when indexing is done now we
have<00:03:22.239><c> an</c><00:03:22.400><c> index</c><00:03:22.840><c> for</c><00:03:23.080><c> this</c><00:03:23.319><c> file</c><00:03:24.319><c> obsidian</c><00:03:24.799><c> can</c>

00:03:24.949 --> 00:03:24.959 align:start position:0%
have an index for this file obsidian can
 

00:03:24.959 --> 00:03:27.309 align:start position:0%
have an index for this file obsidian can
give<00:03:25.120><c> us</c><00:03:25.239><c> a</c><00:03:25.400><c> list</c><00:03:25.720><c> of</c><00:03:25.959><c> all</c><00:03:26.239><c> the</c><00:03:26.400><c> files</c><00:03:27.040><c> so</c><00:03:27.239><c> we</c>

00:03:27.309 --> 00:03:27.319 align:start position:0%
give us a list of all the files so we
 

00:03:27.319 --> 00:03:30.190 align:start position:0%
give us a list of all the files so we
can<00:03:27.440><c> run</c><00:03:27.720><c> that</c><00:03:28.000><c> over</c><00:03:28.439><c> and</c><00:03:28.680><c> over</c><00:03:29.200><c> again</c><00:03:30.080><c> and</c>

00:03:30.190 --> 00:03:30.200 align:start position:0%
can run that over and over again and
 

00:03:30.200 --> 00:03:32.589 align:start position:0%
can run that over and over again and
we're<00:03:30.439><c> persisting</c><00:03:31.040><c> so</c><00:03:31.319><c> this</c><00:03:31.439><c> is</c><00:03:31.680><c> a</c><00:03:31.879><c> one-time</c>

00:03:32.589 --> 00:03:32.599 align:start position:0%
we're persisting so this is a one-time
 

00:03:32.599 --> 00:03:36.070 align:start position:0%
we're persisting so this is a one-time
Action<00:03:33.360><c> Now</c><00:03:33.840><c> how</c><00:03:34.040><c> can</c><00:03:34.200><c> we</c><00:03:34.439><c> ask</c><00:03:34.799><c> a</c><00:03:35.000><c> question</c><00:03:35.959><c> we</c>

00:03:36.070 --> 00:03:36.080 align:start position:0%
Action Now how can we ask a question we
 

00:03:36.080 --> 00:03:37.429 align:start position:0%
Action Now how can we ask a question we
want<00:03:36.280><c> some</c><00:03:36.519><c> code</c><00:03:36.760><c> that</c><00:03:36.879><c> will</c><00:03:37.040><c> find</c><00:03:37.280><c> the</c>

00:03:37.429 --> 00:03:37.439 align:start position:0%
want some code that will find the
 

00:03:37.439 --> 00:03:39.589 align:start position:0%
want some code that will find the
relevant<00:03:37.920><c> bits</c><00:03:38.200><c> in</c><00:03:38.319><c> our</c><00:03:38.480><c> notes</c><00:03:39.120><c> hand</c><00:03:39.280><c> it</c><00:03:39.439><c> off</c>

00:03:39.589 --> 00:03:39.599 align:start position:0%
relevant bits in our notes hand it off
 

00:03:39.599 --> 00:03:42.149 align:start position:0%
relevant bits in our notes hand it off
to<00:03:39.720><c> the</c><00:03:39.840><c> model</c><00:03:40.519><c> and</c><00:03:40.680><c> use</c><00:03:41.000><c> that</c><00:03:41.239><c> info</c><00:03:41.760><c> to</c><00:03:41.959><c> come</c>

00:03:42.149 --> 00:03:42.159 align:start position:0%
to the model and use that info to come
 

00:03:42.159 --> 00:03:44.910 align:start position:0%
to the model and use that info to come
up<00:03:42.519><c> with</c><00:03:42.680><c> an</c><00:03:42.879><c> answer</c><00:03:43.879><c> so</c><00:03:44.040><c> in</c><00:03:44.200><c> this</c><00:03:44.360><c> code</c><00:03:44.599><c> sample</c>

00:03:44.910 --> 00:03:44.920 align:start position:0%
up with an answer so in this code sample
 

00:03:44.920 --> 00:03:46.910 align:start position:0%
up with an answer so in this code sample
we're<00:03:45.120><c> initializing</c><00:03:45.720><c> the</c><00:03:45.840><c> index</c><00:03:46.400><c> using</c><00:03:46.760><c> the</c>

00:03:46.910 --> 00:03:46.920 align:start position:0%
we're initializing the index using the
 

00:03:46.920 --> 00:03:49.309 align:start position:0%
we're initializing the index using the
content<00:03:47.280><c> that</c><00:03:47.400><c> we</c><00:03:47.599><c> already</c><00:03:48.120><c> processed</c><00:03:49.120><c> this</c>

00:03:49.309 --> 00:03:49.319 align:start position:0%
content that we already processed this
 

00:03:49.319 --> 00:03:51.550 align:start position:0%
content that we already processed this
retriever<00:03:49.920><c> do</c><00:03:50.159><c> retrieve</c><00:03:50.599><c> line</c><00:03:51.080><c> is</c><00:03:51.200><c> going</c><00:03:51.400><c> to</c>

00:03:51.550 --> 00:03:51.560 align:start position:0%
retriever do retrieve line is going to
 

00:03:51.560 --> 00:03:53.470 align:start position:0%
retriever do retrieve line is going to
take<00:03:51.720><c> the</c><00:03:51.879><c> prompt</c><00:03:52.439><c> and</c><00:03:52.640><c> find</c><00:03:52.920><c> all</c><00:03:53.079><c> the</c><00:03:53.200><c> chunks</c>

00:03:53.470 --> 00:03:53.480 align:start position:0%
take the prompt and find all the chunks
 

00:03:53.480 --> 00:03:55.710 align:start position:0%
take the prompt and find all the chunks
of<00:03:53.599><c> notes</c><00:03:54.159><c> that</c><00:03:54.280><c> are</c><00:03:54.480><c> relevant</c><00:03:55.159><c> and</c><00:03:55.480><c> return</c>

00:03:55.710 --> 00:03:55.720 align:start position:0%
of notes that are relevant and return
 

00:03:55.720 --> 00:03:58.390 align:start position:0%
of notes that are relevant and return
the<00:03:55.879><c> text</c><00:03:56.120><c> to</c><00:03:56.319><c> us</c><00:03:57.159><c> we</c><00:03:57.360><c> said</c><00:03:57.599><c> to</c><00:03:57.720><c> use</c><00:03:57.959><c> the</c><00:03:58.159><c> top</c>

00:03:58.390 --> 00:03:58.400 align:start position:0%
the text to us we said to use the top
 

00:03:58.400 --> 00:04:00.429 align:start position:0%
the text to us we said to use the top
five<00:03:58.640><c> matches</c><00:03:59.079><c> here</c><00:03:59.280><c> so</c><00:03:59.720><c> I'll</c><00:03:59.920><c> get</c><00:04:00.159><c> five</c>

00:04:00.429 --> 00:04:00.439 align:start position:0%
five matches here so I'll get five
 

00:04:00.439 --> 00:04:03.550 align:start position:0%
five matches here so I'll get five
chunks<00:04:00.959><c> of</c><00:04:01.239><c> text</c><00:04:01.720><c> from</c><00:04:01.879><c> our</c><00:04:02.159><c> notes</c><00:04:03.159><c> with</c><00:04:03.360><c> that</c>

00:04:03.550 --> 00:04:03.560 align:start position:0%
chunks of text from our notes with that
 

00:04:03.560 --> 00:04:05.670 align:start position:0%
chunks of text from our notes with that
raw<00:04:04.000><c> information</c><00:04:04.519><c> we</c><00:04:04.640><c> can</c><00:04:04.799><c> generate</c><00:04:05.159><c> a</c><00:04:05.360><c> system</c>

00:04:05.670 --> 00:04:05.680 align:start position:0%
raw information we can generate a system
 

00:04:05.680 --> 00:04:08.190 align:start position:0%
raw information we can generate a system
prompt<00:04:06.120><c> to</c><00:04:06.280><c> help</c><00:04:06.519><c> our</c><00:04:06.640><c> model</c><00:04:07.000><c> know</c><00:04:07.280><c> what</c><00:04:07.400><c> to</c><00:04:07.640><c> do</c>

00:04:08.190 --> 00:04:08.200 align:start position:0%
prompt to help our model know what to do
 

00:04:08.200 --> 00:04:11.429 align:start position:0%
prompt to help our model know what to do
when<00:04:08.360><c> we</c><00:04:08.599><c> ask</c><00:04:08.879><c> a</c><00:04:09.239><c> question</c><00:04:10.239><c> and</c><00:04:10.319><c> so</c><00:04:10.640><c> now</c><00:04:11.120><c> we</c><00:04:11.280><c> get</c>

00:04:11.429 --> 00:04:11.439 align:start position:0%
when we ask a question and so now we get
 

00:04:11.439 --> 00:04:14.190 align:start position:0%
when we ask a question and so now we get
to<00:04:11.640><c> use</c><00:04:11.920><c> the</c><00:04:12.200><c> model</c><00:04:13.200><c> I'm</c><00:04:13.360><c> using</c><00:04:13.599><c> a</c><00:04:13.720><c> library</c><00:04:14.120><c> I</c>

00:04:14.190 --> 00:04:14.200 align:start position:0%
to use the model I'm using a library I
 

00:04:14.200 --> 00:04:16.949 align:start position:0%
to use the model I'm using a library I
created<00:04:14.560><c> a</c><00:04:14.640><c> few</c><00:04:14.840><c> days</c><00:04:15.040><c> ago</c><00:04:15.319><c> that's</c><00:04:15.519><c> on</c><00:04:15.880><c> npm</c><00:04:16.880><c> I</c>

00:04:16.949 --> 00:04:16.959 align:start position:0%
created a few days ago that's on npm I
 

00:04:16.959 --> 00:04:19.430 align:start position:0%
created a few days ago that's on npm I
can<00:04:17.079><c> set</c><00:04:17.320><c> the</c><00:04:17.440><c> model</c><00:04:17.720><c> to</c><00:04:17.880><c> use</c><00:04:18.320><c> llama</c><00:04:18.680><c> 2</c><00:04:19.320><c> which</c>

00:04:19.430 --> 00:04:19.440 align:start position:0%
can set the model to use llama 2 which
 

00:04:19.440 --> 00:04:20.870 align:start position:0%
can set the model to use llama 2 which
is<00:04:19.600><c> already</c><00:04:19.840><c> downloaded</c><00:04:20.239><c> to</c><00:04:20.400><c> my</c><00:04:20.519><c> machine</c>

00:04:20.870 --> 00:04:20.880 align:start position:0%
is already downloaded to my machine
 

00:04:20.880 --> 00:04:24.230 align:start position:0%
is already downloaded to my machine
using<00:04:21.120><c> the</c><00:04:21.280><c> command</c><00:04:21.840><c> AMA</c><00:04:22.560><c> pull</c><00:04:23.000><c> llama</c><00:04:23.320><c> 2</c><00:04:24.160><c> you</c>

00:04:24.230 --> 00:04:24.240 align:start position:0%
using the command AMA pull llama 2 you
 

00:04:24.240 --> 00:04:25.909 align:start position:0%
using the command AMA pull llama 2 you
can<00:04:24.400><c> try</c><00:04:24.600><c> different</c><00:04:24.880><c> models</c><00:04:25.199><c> to</c><00:04:25.360><c> find</c><00:04:25.600><c> the</c><00:04:25.720><c> one</c>

00:04:25.909 --> 00:04:25.919 align:start position:0%
can try different models to find the one
 

00:04:25.919 --> 00:04:28.390 align:start position:0%
can try different models to find the one
that<00:04:26.040><c> works</c><00:04:26.400><c> best</c><00:04:26.639><c> for</c><00:04:26.840><c> you</c><00:04:27.600><c> to</c><00:04:27.800><c> get</c><00:04:28.000><c> answers</c>

00:04:28.390 --> 00:04:28.400 align:start position:0%
that works best for you to get answers
 

00:04:28.400 --> 00:04:30.150 align:start position:0%
that works best for you to get answers
back<00:04:28.600><c> quickly</c><00:04:29.160><c> you'll</c><00:04:29.320><c> want</c><00:04:29.440><c> to</c><00:04:29.720><c> stick</c><00:04:29.919><c> to</c><00:04:30.039><c> a</c>

00:04:30.150 --> 00:04:30.160 align:start position:0%
back quickly you'll want to stick to a
 

00:04:30.160 --> 00:04:32.390 align:start position:0%
back quickly you'll want to stick to a
small<00:04:30.520><c> model</c><00:04:31.360><c> but</c><00:04:31.560><c> you</c><00:04:31.759><c> also</c><00:04:31.960><c> want</c><00:04:32.039><c> to</c><00:04:32.160><c> have</c><00:04:32.280><c> a</c>

00:04:32.390 --> 00:04:32.400 align:start position:0%
small model but you also want to have a
 

00:04:32.400 --> 00:04:34.189 align:start position:0%
small model but you also want to have a
model<00:04:32.720><c> that</c><00:04:32.800><c> will</c><00:04:33.000><c> have</c><00:04:33.160><c> an</c><00:04:33.360><c> input</c><00:04:33.759><c> context</c>

00:04:34.189 --> 00:04:34.199 align:start position:0%
model that will have an input context
 

00:04:34.199 --> 00:04:36.629 align:start position:0%
model that will have an input context
size<00:04:34.720><c> large</c><00:04:35.039><c> enough</c><00:04:35.520><c> to</c><00:04:35.759><c> accept</c><00:04:36.199><c> all</c><00:04:36.360><c> of</c><00:04:36.479><c> our</c>

00:04:36.629 --> 00:04:36.639 align:start position:0%
size large enough to accept all of our
 

00:04:36.639 --> 00:04:38.909 align:start position:0%
size large enough to accept all of our
text<00:04:36.840><c> chunks</c><00:04:37.720><c> I</c><00:04:37.800><c> have</c><00:04:38.000><c> up</c><00:04:38.120><c> to</c><00:04:38.360><c> five</c><00:04:38.560><c> chunks</c>

00:04:38.909 --> 00:04:38.919 align:start position:0%
text chunks I have up to five chunks
 

00:04:38.919 --> 00:04:41.990 align:start position:0%
text chunks I have up to five chunks
that<00:04:39.039><c> are</c><00:04:39.160><c> 256</c><00:04:40.000><c> tokens</c><00:04:40.440><c> each</c><00:04:41.240><c> I</c><00:04:41.320><c> set</c><00:04:41.560><c> the</c><00:04:41.680><c> model</c>

00:04:41.990 --> 00:04:42.000 align:start position:0%
that are 256 tokens each I set the model
 

00:04:42.000 --> 00:04:44.270 align:start position:0%
that are 256 tokens each I set the model
to<00:04:42.199><c> use</c><00:04:42.600><c> that</c><00:04:42.800><c> system</c><00:04:43.160><c> prompt</c><00:04:43.800><c> that</c><00:04:43.919><c> includes</c>

00:04:44.270 --> 00:04:44.280 align:start position:0%
to use that system prompt that includes
 

00:04:44.280 --> 00:04:46.629 align:start position:0%
to use that system prompt that includes
our<00:04:44.440><c> text</c><00:04:44.680><c> chunks</c><00:04:45.440><c> just</c><00:04:45.840><c> ask</c><00:04:46.039><c> the</c><00:04:46.240><c> question</c>

00:04:46.629 --> 00:04:46.639 align:start position:0%
our text chunks just ask the question
 

00:04:46.639 --> 00:04:47.990 align:start position:0%
our text chunks just ask the question
and<00:04:46.800><c> I'll</c><00:04:46.880><c> will</c><00:04:47.039><c> give</c><00:04:47.160><c> you</c><00:04:47.320><c> the</c><00:04:47.479><c> answer</c><00:04:47.800><c> within</c>

00:04:47.990 --> 00:04:48.000 align:start position:0%
and I'll will give you the answer within
 

00:04:48.000 --> 00:04:51.390 align:start position:0%
and I'll will give you the answer within
a<00:04:48.120><c> few</c><00:04:48.400><c> seconds</c><00:04:49.400><c> awesome</c><00:04:50.400><c> now</c><00:04:50.759><c> our</c><00:04:50.960><c> obsidian</c>

00:04:51.390 --> 00:04:51.400 align:start position:0%
a few seconds awesome now our obsidian
 

00:04:51.400 --> 00:04:53.350 align:start position:0%
a few seconds awesome now our obsidian
plugin<00:04:51.880><c> would</c><00:04:52.080><c> display</c><00:04:52.520><c> that</c><00:04:52.720><c> answer</c>

00:04:53.350 --> 00:04:53.360 align:start position:0%
plugin would display that answer
 

00:04:53.360 --> 00:04:55.390 align:start position:0%
plugin would display that answer
appropriately<00:04:54.360><c> you</c><00:04:54.479><c> can</c><00:04:54.680><c> also</c><00:04:54.960><c> think</c><00:04:55.160><c> about</c>

00:04:55.390 --> 00:04:55.400 align:start position:0%
appropriately you can also think about
 

00:04:55.400 --> 00:04:57.350 align:start position:0%
appropriately you can also think about
summarizing<00:04:56.000><c> the</c><00:04:56.160><c> text</c><00:04:56.440><c> or</c><00:04:56.639><c> finding</c><00:04:56.960><c> the</c><00:04:57.120><c> best</c>

00:04:57.350 --> 00:04:57.360 align:start position:0%
summarizing the text or finding the best
 

00:04:57.360 --> 00:04:59.550 align:start position:0%
summarizing the text or finding the best
keywords<00:04:58.240><c> that</c><00:04:58.440><c> match</c><00:04:58.720><c> your</c><00:04:58.919><c> text</c><00:04:59.160><c> and</c><00:04:59.320><c> adding</c>

00:04:59.550 --> 00:04:59.560 align:start position:0%
keywords that match your text and adding
 

00:04:59.560 --> 00:05:01.189 align:start position:0%
keywords that match your text and adding
adding<00:04:59.800><c> them</c><00:04:59.919><c> to</c><00:05:00.039><c> the</c><00:05:00.120><c> front</c><00:05:00.360><c> matter</c><00:05:00.960><c> so</c><00:05:01.120><c> you</c>

00:05:01.189 --> 00:05:01.199 align:start position:0%
adding them to the front matter so you
 

00:05:01.199 --> 00:05:02.629 align:start position:0%
adding them to the front matter so you
can<00:05:01.360><c> make</c><00:05:01.560><c> better</c><00:05:01.800><c> connections</c><00:05:02.320><c> between</c>

00:05:02.629 --> 00:05:02.639 align:start position:0%
can make better connections between
 

00:05:02.639 --> 00:05:05.189 align:start position:0%
can make better connections between
notes<00:05:03.600><c> I've</c><00:05:03.759><c> played</c><00:05:04.000><c> around</c><00:05:04.240><c> with</c><00:05:04.400><c> making</c><00:05:04.800><c> 10</c>

00:05:05.189 --> 00:05:05.199 align:start position:0%
notes I've played around with making 10
 

00:05:05.199 --> 00:05:07.029 align:start position:0%
notes I've played around with making 10
good<00:05:05.600><c> questions</c><00:05:05.919><c> and</c><00:05:06.160><c> answers</c><00:05:06.560><c> to</c><00:05:06.680><c> send</c><00:05:06.880><c> to</c>

00:05:07.029 --> 00:05:07.039 align:start position:0%
good questions and answers to send to
 

00:05:07.039 --> 00:05:09.189 align:start position:0%
good questions and answers to send to
anky<00:05:07.840><c> this</c><00:05:07.960><c> is</c><00:05:08.080><c> a</c><00:05:08.199><c> cool</c><00:05:08.400><c> space</c><00:05:08.720><c> repetition</c>

00:05:09.189 --> 00:05:09.199 align:start position:0%
anky this is a cool space repetition
 

00:05:09.199 --> 00:05:11.469 align:start position:0%
anky this is a cool space repetition
tool<00:05:09.520><c> that</c><00:05:09.639><c> helps</c><00:05:09.919><c> me</c><00:05:10.199><c> remember</c><00:05:10.919><c> some</c><00:05:11.160><c> of</c><00:05:11.320><c> my</c>

00:05:11.469 --> 00:05:11.479 align:start position:0%
tool that helps me remember some of my
 

00:05:11.479 --> 00:05:13.629 align:start position:0%
tool that helps me remember some of my
notes<00:05:12.440><c> you'll</c><00:05:12.600><c> want</c><00:05:12.759><c> to</c><00:05:13.000><c> experiment</c><00:05:13.479><c> with</c>

00:05:13.629 --> 00:05:13.639 align:start position:0%
notes you'll want to experiment with
 

00:05:13.639 --> 00:05:15.270 align:start position:0%
notes you'll want to experiment with
different<00:05:13.960><c> models</c><00:05:14.400><c> and</c><00:05:14.600><c> prompts</c><00:05:15.080><c> to</c>

00:05:15.270 --> 00:05:15.280 align:start position:0%
different models and prompts to
 

00:05:15.280 --> 00:05:17.270 align:start position:0%
different models and prompts to
accomplish<00:05:15.800><c> these</c><00:05:16.000><c> different</c><00:05:16.280><c> things</c><00:05:17.039><c> it's</c>

00:05:17.270 --> 00:05:17.280 align:start position:0%
accomplish these different things it's
 

00:05:17.280 --> 00:05:18.870 align:start position:0%
accomplish these different things it's
very<00:05:17.479><c> easy</c><00:05:17.759><c> to</c><00:05:17.880><c> change</c><00:05:18.160><c> the</c><00:05:18.280><c> prompts</c><00:05:18.600><c> and</c><00:05:18.720><c> even</c>

00:05:18.870 --> 00:05:18.880 align:start position:0%
very easy to change the prompts and even
 

00:05:18.880 --> 00:05:20.510 align:start position:0%
very easy to change the prompts and even
the<00:05:18.960><c> model</c><00:05:19.240><c> weights</c><00:05:19.720><c> to</c><00:05:20.000><c> something</c><00:05:20.280><c> more</c>

00:05:20.510 --> 00:05:20.520 align:start position:0%
the model weights to something more
 

00:05:20.520 --> 00:05:22.870 align:start position:0%
the model weights to something more
appropriate<00:05:20.960><c> to</c><00:05:21.080><c> the</c><00:05:21.360><c> task</c><00:05:22.360><c> I</c><00:05:22.479><c> hope</c><00:05:22.720><c> this</c>

00:05:22.870 --> 00:05:22.880 align:start position:0%
appropriate to the task I hope this
 

00:05:22.880 --> 00:05:24.590 align:start position:0%
appropriate to the task I hope this
video<00:05:23.120><c> has</c><00:05:23.240><c> given</c><00:05:23.440><c> you</c><00:05:23.600><c> some</c><00:05:23.800><c> ideas</c><00:05:24.199><c> for</c><00:05:24.520><c> how</c>

00:05:24.590 --> 00:05:24.600 align:start position:0%
video has given you some ideas for how
 

00:05:24.600 --> 00:05:27.270 align:start position:0%
video has given you some ideas for how
to<00:05:24.759><c> build</c><00:05:25.000><c> the</c><00:05:25.199><c> next</c><00:05:25.919><c> great</c><00:05:26.560><c> plug-in</c><00:05:27.039><c> for</c>

00:05:27.270 --> 00:05:27.280 align:start position:0%
to build the next great plug-in for
 

00:05:27.280 --> 00:05:30.189 align:start position:0%
to build the next great plug-in for
obsidian<00:05:27.759><c> or</c><00:05:28.319><c> any</c><00:05:28.520><c> other</c><00:05:28.720><c> note-taking</c><00:05:29.240><c> tool</c>

00:05:30.189 --> 00:05:30.199 align:start position:0%
obsidian or any other note-taking tool
 

00:05:30.199 --> 00:05:32.230 align:start position:0%
obsidian or any other note-taking tool
using<00:05:30.520><c> the</c><00:05:30.680><c> latest</c><00:05:31.039><c> local</c><00:05:31.360><c> AI</c><00:05:31.680><c> tools</c><00:05:32.080><c> like</c>

00:05:32.230 --> 00:05:32.240 align:start position:0%
using the latest local AI tools like
 

00:05:32.240 --> 00:05:35.110 align:start position:0%
using the latest local AI tools like
those<00:05:32.400><c> you</c><00:05:32.520><c> can</c><00:05:32.680><c> find</c><00:05:32.880><c> at.</c><00:05:33.840><c> a</c><00:05:34.680><c> this</c><00:05:34.840><c> kind</c><00:05:34.960><c> of</c>

00:05:35.110 --> 00:05:35.120 align:start position:0%
those you can find at. a this kind of
 

00:05:35.120 --> 00:05:37.230 align:start position:0%
those you can find at. a this kind of
power<00:05:35.400><c> is</c><00:05:35.520><c> a</c><00:05:35.800><c> breeze</c><00:05:36.400><c> and</c><00:05:36.560><c> I</c><00:05:36.680><c> hope</c><00:05:36.840><c> you'll</c><00:05:37.000><c> show</c>

00:05:37.230 --> 00:05:37.240 align:start position:0%
power is a breeze and I hope you'll show
 

00:05:37.240 --> 00:05:39.350 align:start position:0%
power is a breeze and I hope you'll show
me<00:05:37.639><c> what</c><00:05:37.759><c> you've</c><00:05:38.000><c> got</c><00:05:38.240><c> going</c><00:05:38.440><c> on</c><00:05:38.840><c> in</c><00:05:39.039><c> whatever</c>

00:05:39.350 --> 00:05:39.360 align:start position:0%
me what you've got going on in whatever
 

00:05:39.360 --> 00:05:58.469 align:start position:0%
me what you've got going on in whatever
you<00:05:39.520><c> built</c><00:05:40.360><c> thanks</c><00:05:40.600><c> so</c><00:05:40.720><c> much</c><00:05:40.880><c> for</c><00:05:41.039><c> watching</c>

00:05:58.469 --> 00:05:58.479 align:start position:0%
 
 

00:05:58.479 --> 00:06:01.909 align:start position:0%
 
goodbye

00:06:01.909 --> 00:06:01.919 align:start position:0%
 
 

00:06:01.919 --> 00:06:04.400 align:start position:0%
 
hold<00:06:02.120><c> on</c>

