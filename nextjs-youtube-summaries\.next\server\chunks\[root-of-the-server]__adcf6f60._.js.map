{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\r\n\r\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\r\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\r\n\r\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\r\n\r\n// For server-side operations that require elevated permissions\r\nexport const supabaseAdmin = createClient(\r\n  supabaseUrl,\r\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\r\n)\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EACtC,aACA,QAAQ,GAAG,CAAC,yBAAyB", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/lib/database.ts"], "sourcesContent": ["import { supabaseAdmin } from './supabase'\r\nimport { VideoSummary, TopicTable, SearchResult } from '@/types'\r\n\r\nexport class DatabaseService {\r\n  // Get all available topic tables with better error handling\r\n  static async getTopicTables(): Promise<TopicTable[]> {\r\n    try {\r\n      // Use the actual YouTube topic table names discovered in the database\r\n      const youtubeTopics = [\r\n        'youtube_artificial_intelligence',\r\n        'youtube_sustainability', \r\n        'youtube_startups',\r\n        'youtube_financial_markets',\r\n        'youtube_gme',\r\n        'youtube_general',\r\n        'youtube_legal',\r\n        'youtube_renewable_energy'\r\n      ]\r\n      const existingTables: TopicTable[] = []\r\n      \r\n      console.log('Checking for YouTube topic tables...')\r\n      for (const topic of youtubeTopics) {        \r\n        try {\r\n          const { error, count } = await supabaseAdmin\r\n            .from(topic)\r\n            .select('id', { count: 'exact', head: true })\r\n            .limit(1)\r\n          \r\n          if (!error) {\r\n            console.log(`Found table: ${topic} with ${count || 0} videos`)\r\n            existingTables.push({\r\n              id: topic, // Assuming table name can serve as a unique ID here\r\n              table_name: topic,\r\n              name: topic,\r\n              display_name: this.formatDisplayName(topic),\r\n              description: `${this.formatDisplayName(topic)} videos`,\r\n              video_count: count || 0,\r\n              created_at: new Date().toISOString(), // Placeholder\r\n              updated_at: new Date().toISOString() // Placeholder\r\n            })\r\n          }\r\n        } catch {\r\n          // Table doesn't exist, skip silently\r\n        }\r\n      }\r\n      \r\n      if (existingTables.length > 0) {\r\n        return existingTables\r\n      }\r\n      \r\n      console.warn('No YouTube topic tables found in database, using fallback topics')\r\n      // Return fallback topics for development\r\n      const fallbackDate = new Date().toISOString();\r\n      return [\r\n        { id: 'youtube_artificial_intelligence', table_name: 'youtube_artificial_intelligence', name: 'youtube_artificial_intelligence', display_name: 'Artificial Intelligence', description: 'AI and machine learning videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate },\r\n        { id: 'youtube_sustainability', table_name: 'youtube_sustainability', name: 'youtube_sustainability', display_name: 'Sustainability', description: 'Sustainability and environment videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate },\r\n        { id: 'youtube_startups', table_name: 'youtube_startups', name: 'youtube_startups', display_name: 'Startups', description: 'Startup and entrepreneurship videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate },\r\n        { id: 'youtube_financial_markets', table_name: 'youtube_financial_markets', name: 'youtube_financial_markets', display_name: 'Financial Markets', description: 'Finance and market videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate }\r\n      ]\r\n    } catch (error) {\r\n      console.error('Error fetching topic tables:', error)\r\n      // Return fallback topics when database is not accessible\r\n      const fallbackDate = new Date().toISOString();\r\n      return [\r\n        { id: 'youtube_artificial_intelligence', table_name: 'youtube_artificial_intelligence', name: 'youtube_artificial_intelligence', display_name: 'Artificial Intelligence', description: 'AI and machine learning videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate },\r\n        { id: 'youtube_sustainability', table_name: 'youtube_sustainability', name: 'youtube_sustainability', display_name: 'Sustainability', description: 'Sustainability and environment videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate },\r\n        { id: 'youtube_startups', table_name: 'youtube_startups', name: 'youtube_startups', display_name: 'Startups', description: 'Startup and entrepreneurship videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate },\r\n        { id: 'youtube_financial_markets', table_name: 'youtube_financial_markets', name: 'youtube_financial_markets', display_name: 'Financial Markets', description: 'Finance and market videos', video_count: 0, created_at: fallbackDate, updated_at: fallbackDate }\r\n      ]\r\n    }\r\n  }\r\n\r\n  // Helper method to format display names\r\n  private static formatDisplayName(tableName: string): string {\r\n    return tableName\r\n      .replace('youtube_', '') // Remove youtube_ prefix\r\n      .replace('_', ' ')\r\n      .split(' ')\r\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n      .join(' ')\r\n  }\r\n  // Get all unique channels from available topic tables\r\n  static async getChannels(topicFilter?: string[]): Promise<string[]> {\r\n    try {\r\n      const allChannels = new Set<string>()\r\n      \r\n      // Get all available topic tables\r\n      const allTopicTables = await this.getTopicTables()\r\n      \r\n      // Filter topic tables if topicFilter is provided\r\n      const topicTables = topicFilter && topicFilter.length > 0\r\n        ? allTopicTables.filter(t => {\r\n            const simpleName = t.name.startsWith('youtube_') ? t.name.substring(8) : t.name\r\n            return topicFilter.includes(simpleName) || topicFilter.includes(t.name)\r\n          })\r\n        : allTopicTables\r\n      \r\n      console.log('Fetching channels from tables:', topicTables.map(t => t.name))\r\n      console.log('Topic filter applied:', topicFilter)\r\n        // Query each table for unique channels\r\n      for (const topic of topicTables) {        try {\r\n          // Use distinct to get unique channel names without arbitrary limits\r\n          const { data, error } = await supabaseAdmin\r\n            .from(topic.name)\r\n            .select('channel_name')\r\n            .not('channel_name', 'is', null)\r\n            .order('channel_name')\r\n          \r\n          if (!error && data) {\r\n            console.log(`Found ${data.length} channel entries in ${topic.name}`)\r\n            data.forEach((item: { channel_name: string }) => {\r\n              if (item.channel_name && item.channel_name.trim()) {\r\n                allChannels.add(item.channel_name.trim())\r\n              }\r\n            })\r\n          } else if (error) {\r\n            console.warn(`Error fetching channels from ${topic.name}:`, error.message)\r\n          }\r\n        } catch (tableError) {\r\n          console.warn(`Error accessing table ${topic.name}:`, tableError)\r\n          // Continue with other tables\r\n        }\r\n      }\r\n      \r\n      const channels = Array.from(allChannels).sort()\r\n      console.log(`Total unique channels found: ${channels.length}`)\r\n      \r\n      // If no channels found in database, return some sample channels for development\r\n      if (channels.length === 0) {\r\n        console.warn('No channels found in database, returning sample channels for development')\r\n        return [\r\n          'Sample Tech Channel',\r\n          'Sample Programming Channel', \r\n          'Sample AI Channel',\r\n          'Sample Science Channel'\r\n        ]\r\n      }\r\n      \r\n      return channels\r\n    } catch (error) {\r\n      console.error('Error fetching channels:', error)\r\n      // Return sample channels for development when database is not accessible\r\n      return [\r\n        'Sample Tech Channel',\r\n        'Sample Programming Channel',\r\n        'Sample AI Channel', \r\n        'Sample Science Channel'\r\n      ]\r\n    }\r\n  }  // Get videos filtered by topics and/or channels\r\n  static async getFilteredVideos(\r\n    topicTables?: string[],\r\n    channels?: string[],\r\n    limit: number = 20,\r\n    offset: number = 0,\r\n    startDate?: string,\r\n    endDate?: string\r\n  ): Promise<VideoSummary[]> {\r\n    try {\r\n      const allVideos: VideoSummary[] = []\r\n      const seenVideoIds = new Set<string>() // Track video IDs to prevent duplicates\r\n      \r\n      // Get available topic tables if not provided\r\n      const tablesToQuery = topicTables && topicTables.length > 0 \r\n        ? topicTables \r\n        : (await this.getTopicTables()).map(t => t.table_name); // Use table_name\r\n        // Calculate how many videos to fetch from each table to account for deduplication\r\n      // When we have filters (channels, dates), we need to be more generous with the limit\r\n      // to ensure we get enough results after filtering and deduplication\r\n      const hasFilters = (channels && channels.length > 0) || startDate || endDate\r\n      const multiplier = hasFilters ? 10 : 8 // Be very generous to get videos with real dates\r\n      const perTableLimit = Math.max((limit + offset) * multiplier, 1000)\r\n      \r\n      // Query each table\r\n      for (const tableName of tablesToQuery) {\r\n        try {\r\n          // Build query - fetch more records to ensure we get videos with real dates\r\n          let queryBuilder = supabaseAdmin\r\n            .from(tableName)\r\n            .select('*')\r\n            .order('published_at', { ascending: false, nullsLast: true })\r\n\r\n          // Apply filters\r\n          if (channels && channels.length > 0) {\r\n            queryBuilder = queryBuilder.in('channel_name', channels)\r\n          }\r\n          if (startDate) {\r\n            queryBuilder = queryBuilder.gte('published_at', startDate)\r\n          }\r\n          if (endDate) {\r\n            queryBuilder = queryBuilder.lte('published_at', endDate)\r\n          }\r\n\r\n          const { data, error } = await queryBuilder.limit(perTableLimit)\r\n          \r\n          if (!error && data) {\r\n            // Add videos, but only if we haven't seen this video_id before AND it has a real date\r\n            for (const video of data) {\r\n              if (!seenVideoIds.has(video.video_id)) {\r\n                // Debug: log the first few videos to see what we're getting\r\n                if (allVideos.length < 3) {\r\n                  console.log('🔍 Video debug:', {\r\n                    title: video.title?.substring(0, 50),\r\n                    published_at: video.published_at,\r\n                    created_at: video.created_at,\r\n                    equal: video.published_at === video.created_at\r\n                  });\r\n                }\r\n\r\n                // Only include videos with real YouTube dates (published_at != created_at)\r\n                const publishedStr = String(video.published_at || '');\r\n                const createdStr = String(video.created_at || '');\r\n                const isRealDate = publishedStr && createdStr && publishedStr !== createdStr;\r\n\r\n                if (isRealDate) {\r\n                  seenVideoIds.add(video.video_id)\r\n                  allVideos.push(video)\r\n                }\r\n              }\r\n            }\r\n          }\r\n        } catch (tableError) {\r\n          console.warn(`Error fetching videos from ${tableName}:`, tableError)\r\n          // Continue with other tables\r\n        }\r\n      }\r\n      \r\n      // Sort all videos by published date (most recent first)\r\n      // Handle data quality issue: ignore fake published_at dates that match created_at\r\n      const sortedVideos = allVideos.sort((a, b) => {\r\n        const isRealDateA = a.published_at && a.created_at && a.published_at !== a.created_at;\r\n        const isRealDateB = b.published_at && b.created_at && b.published_at !== b.created_at;\r\n\r\n        const dateA = isRealDateA ? new Date(a.published_at).getTime() : 0;\r\n        const dateB = isRealDateB ? new Date(b.published_at).getTime() : 0;\r\n        return dateB - dateA;\r\n      })\r\n      \r\n      // Apply pagination after sorting and deduplication\r\n      return sortedVideos.slice(offset, offset + limit)\r\n        \r\n    } catch (error) {\r\n      console.error('Error fetching filtered videos:', error)\r\n      return []\r\n    }\r\n  }\r\n\r\n  // Get videos from a specific topic table\r\n  static async getVideosByTopic(\r\n    topicTable: string,\r\n    limit: number = 20,\r\n    offset: number = 0\r\n  ): Promise<VideoSummary[]> {    try {\r\n      const { data, error } = await supabaseAdmin\r\n        .from(topicTable)\r\n        .select('*')\r\n        // Get more records to account for sorting\r\n        .limit(limit * 2)\r\n\r\n      if (error) throw error\r\n\r\n      // Sort by published_at only and apply pagination\r\n      // Handle data quality issue: ignore fake published_at dates that match created_at\r\n      const sortedData = (data || []).sort((a, b) => {\r\n        const isRealDateA = a.published_at && a.created_at && a.published_at !== a.created_at;\r\n        const isRealDateB = b.published_at && b.created_at && b.published_at !== b.created_at;\r\n\r\n        const dateA = isRealDateA ? new Date(a.published_at).getTime() : 0;\r\n        const dateB = isRealDateB ? new Date(b.published_at).getTime() : 0;\r\n        return dateB - dateA;\r\n      });\r\n\r\n      return sortedData.slice(offset, offset + limit)\r\n    } catch (error) {\r\n      console.error(`Error fetching videos from ${topicTable}:`, error)\r\n      return []\r\n    }\r\n  }\r\n  // Get a single video by ID from a specific topic table or across all tables\r\n  static async getVideoById(topicNameFromUrl: string, videoId: string): Promise<VideoSummary | null> {\r\n    let video: VideoSummary | null = null;\r\n    let attemptedSpecificTable = false;\r\n    let specificTableQueryError: Error | unknown | null = null;\r\n\r\n    // Validate topicNameFromUrl and videoId\r\n    if (!videoId || typeof videoId !== 'string' || videoId.trim() === '') {\r\n      console.error(`getVideoById: Called with invalid videoId: '${videoId}'. Cannot fetch video.`);\r\n      return null;\r\n    }\r\n\r\n    if (topicNameFromUrl && typeof topicNameFromUrl === 'string' && topicNameFromUrl.trim() !== '') {\r\n      const actualTableName = topicNameFromUrl.startsWith('youtube_') \r\n        ? topicNameFromUrl \r\n        : `youtube_${topicNameFromUrl}`;\r\n      \r\n      console.log(`getVideoById: Attempting to fetch videoId '${videoId}' from specific table '${actualTableName}' (original topic: '${topicNameFromUrl}')`);\r\n      attemptedSpecificTable = true;\r\n\r\n      try {\r\n        const { data, error: supabaseError } = await supabaseAdmin\r\n          .from(actualTableName)\r\n          .select('*')\r\n          .eq('video_id', videoId)\r\n          .single();\r\n\r\n        if (supabaseError) {\r\n          specificTableQueryError = supabaseError; // Store error for logging\r\n          console.warn(`getVideoById: Supabase error fetching videoId '${videoId}' from table '${actualTableName}'. Code: ${supabaseError.code}, Message: ${supabaseError.message}. Will try searching all tables.`);\r\n          // Do not throw here, let it fall through to findVideoAcrossAllTables logic\r\n        } else if (data) {\r\n          console.log(`getVideoById: Successfully found videoId '${videoId}' in table '${actualTableName}'`);\r\n          const simpleTopicName = actualTableName.startsWith('youtube_') ? actualTableName.substring(8) : actualTableName;\r\n          // Ensure llm_response is at least an empty object if null/undefined from DB\r\n          video = { ...data, topic_category: simpleTopicName, llm_response: data.llm_response || {} };\r\n        } else {\r\n          // This case (no error, no data with .single()) should ideally not happen as .single() errors out.\r\n          console.warn(`getVideoById: No data and no error for videoId '${videoId}' from table '${actualTableName}'. This is unexpected with .single(). Will try searching all tables.`);\r\n        }\r\n      } catch (catchedError: unknown) { // Catch unexpected errors from the specific table query attempt\r\n        specificTableQueryError = catchedError;\r\n        const errorMessage = catchedError instanceof Error ? catchedError.message : String(catchedError);\r\n        console.error(`getVideoById: Exception during fetch from specific table '${actualTableName}' for videoId '${videoId}'. Error: ${errorMessage}. Will try searching all tables.`);\r\n      }\r\n    } else {\r\n      console.warn(`getVideoById: Called with invalid or empty topicNameFromUrl ('${topicNameFromUrl}'). Proceeding directly to search across all tables for videoId: '${videoId}'.`);\r\n    }\r\n\r\n    // If video not found in specific table (or specific table was not attempted/valid)\r\n    if (!video) {\r\n      if (attemptedSpecificTable) {\r\n        let errMessage = \"No specific error object\";\r\n        if (specificTableQueryError) {\r\n          if (specificTableQueryError instanceof Error) {\r\n            errMessage = specificTableQueryError.message;\r\n          } else {\r\n            try {\r\n              errMessage = JSON.stringify(specificTableQueryError);\r\n            } catch {\r\n              errMessage = \"Could not stringify error object\";\r\n            }\r\n          }\r\n        }\r\n        console.warn(`getVideoById: VideoId '${videoId}' not found in specific table or an error occurred. Specific error (if any): ${errMessage}`);\r\n      }\r\n      console.log(`getVideoById: Falling back to findVideoAcrossAllTables for videoId '${videoId}'.`);\r\n      try {\r\n        const videoFromFallback = await this.findVideoAcrossAllTables(videoId);\r\n        if (videoFromFallback) {\r\n          console.log(`getVideoById: Found videoId '${videoId}' via findVideoAcrossAllTables.`);\r\n          // Ensure llm_response is at least an empty object if null/undefined from DB\r\n          video = { ...videoFromFallback, llm_response: videoFromFallback.llm_response || {} };\r\n        } else {\r\n          console.error(`getVideoById: VideoId '${videoId}' ultimately NOT FOUND even after searching all tables.`);\r\n        }\r\n      } catch (fallbackError: unknown) {\r\n        const fallbackErrorMessage = fallbackError instanceof Error ? fallbackError.message : String(fallbackError);\r\n        console.error(`getVideoById: CRITICAL ERROR during findVideoAcrossAllTables for videoId '${videoId}'. Error: ${fallbackErrorMessage}`);\r\n        // video remains null\r\n      }\r\n    }\r\n    \r\n    if (!video) {\r\n         const specificErrorMsg = specificTableQueryError instanceof Error ? specificTableQueryError.message : (specificTableQueryError ? \"Error object present\" : \"N/A\");\r\n         console.error(`[FINAL RESULT] getVideoById for videoId '${videoId}' (original topic: '${topicNameFromUrl}'): Video NOT FOUND. Specific table attempt error (if any): ${specificErrorMsg}`);\r\n    } else {\r\n        // Ensure topic_category is sensible\r\n        if (!video.topic_category && topicNameFromUrl) {\r\n            video.topic_category = topicNameFromUrl.replace('youtube_', '');\r\n        } else if (!video.topic_category) {\r\n            video.topic_category = 'unknown'; // Default if no topic info at all\r\n        }\r\n    }\r\n    return video;\r\n  }\r\n\r\n  // Find a video by ID across all topic tables\r\n  static async findVideoAcrossAllTables(videoId: string): Promise<VideoSummary | null> {\r\n    console.log(`findVideoAcrossAllTables: Searching for videoId '${videoId}'`);\r\n    if (!videoId || typeof videoId !== 'string' || videoId.trim() === '') {\r\n      console.error(`findVideoAcrossAllTables: Called with invalid videoId: '${videoId}'.`);\r\n      return null;\r\n    }\r\n    try {\r\n      const topics = await this.getTopicTables();\r\n      if (!topics || topics.length === 0) {\r\n        console.warn(\"findVideoAcrossAllTables: No topic tables found to search in.\");\r\n        return null;\r\n      }\r\n      \r\n      for (const topic of topics) {\r\n        try {\r\n          const tableNameToQuery = topic.table_name;\r\n          if (!tableNameToQuery) {\r\n            console.warn(`findVideoAcrossAllTables: Topic '${topic.name}' has no valid table_name. Skipping.`);\r\n            continue;\r\n          }\r\n          console.log(`findVideoAcrossAllTables: Checking table '${tableNameToQuery}' for videoId '${videoId}'`);\r\n          const { data, error: supabaseError } = await supabaseAdmin\r\n            .from(tableNameToQuery)\r\n            .select('*')\r\n            .eq('video_id', videoId)\r\n            .single();\r\n\r\n          if (!supabaseError && data) {\r\n            console.log(`findVideoAcrossAllTables: Found videoId '${videoId}' in table '${tableNameToQuery}'`);\r\n            const simpleTopicName = topic.name.startsWith('youtube_') ? topic.name.substring(8) : topic.name;\r\n            // Ensure llm_response is at least an empty object if null/undefined from DB\r\n            return { ...data, topic_category: simpleTopicName, llm_response: data.llm_response || {} };\r\n          }\r\n          if (supabaseError && supabaseError.code !== 'PGRST116') { // PGRST116 means 0 rows, which is expected if not in this table\r\n            console.warn(`findVideoAcrossAllTables: Supabase error querying table '${tableNameToQuery}' for videoId '${videoId}'. Code: ${supabaseError.code}, Message: ${supabaseError.message}`);\r\n          }\r\n        } catch (tableQueryError: unknown) {\r\n          const tableQueryErrorMessage = tableQueryError instanceof Error ? tableQueryError.message : String(tableQueryError);\r\n          console.warn(`findVideoAcrossAllTables: Exception while querying table '${topic.table_name}' for videoId '${videoId}'. Message: ${tableQueryErrorMessage}`);\r\n        }\r\n      }\r\n      \r\n      console.log(`findVideoAcrossAllTables: VideoId '${videoId}' not found in any topic table after checking all.`);\r\n      return null;\r\n    } catch (error: unknown) {\r\n      const errorMessage = error instanceof Error ? error.message : String(error);\r\n      console.error(`findVideoAcrossAllTables: General error searching for videoId '${videoId}' across all tables. Message: ${errorMessage}`);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Search videos across all topic tables using vector similarity\r\n  static async searchVideos(\r\n    query: string,\r\n    topicFilters: string[] = [],\r\n    limit: number = 10\r\n  ): Promise<SearchResult[]> {\r\n    try {\r\n      // This will call our Python AI service to generate embeddings\r\n      const embeddingResponse = await fetch('/api/embeddings', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ text: query })\r\n      })\r\n      \r\n      if (!embeddingResponse.ok) throw new Error('Failed to generate embedding')\r\n      \r\n      const { embedding } = await embeddingResponse.json()\r\n\r\n      // Search across specified topic tables or all tables\r\n      const searchPromises = topicFilters.length > 0 \r\n        ? topicFilters.map(table => this.searchInTable(table, embedding, limit))\r\n        : await this.searchAllTables(embedding, limit)\r\n\r\n      const results = await Promise.all(searchPromises)\r\n      \r\n      // Flatten and sort by similarity score\r\n      return results\r\n        .flat()\r\n        .sort((a, b) => b.similarity_score - a.similarity_score)\r\n        .slice(0, limit)\r\n    } catch (error) {\r\n      console.error('Error searching videos:', error)\r\n      return []\r\n    }\r\n  }\r\n\r\n  private static async searchInTable(\r\n    tableName: string,\r\n    embedding: number[],\r\n    limit: number\r\n  ): Promise<SearchResult[]> {    try {\r\n      // Use pgvector similarity search\r\n      const { data, error } = await supabaseAdmin\r\n        .rpc('search_videos_by_embedding', {\r\n          table_name: tableName,\r\n          query_embedding: embedding,\r\n          match_threshold: 0.7,\r\n          match_count: limit\r\n        })\r\n\r\n      if (error) throw error\r\n      return (data || []).map((item: { video: VideoSummary; similarity_score: number; relevant_chunks: string[] }) => ({\r\n        video: item.video,\r\n        similarity_score: item.similarity_score,\r\n        relevant_transcript_chunks: item.relevant_chunks\r\n      }))\r\n    } catch (error) {\r\n      console.error(`Error searching in table ${tableName}:`, error)\r\n      return []\r\n    }\r\n  }\r\n\r\n  private static async searchAllTables(\r\n    embedding: number[],\r\n    limit: number\r\n  ): Promise<SearchResult[][]> {\r\n    const topics = await this.getTopicTables()\r\n    return Promise.all(\r\n      topics.map(topic => this.searchInTable(topic.name, embedding, limit))\r\n    )\r\n  }\r\n  // Get recent videos across all topics\r\n  static async getRecentVideos(limit: number = 20): Promise<VideoSummary[]> {\r\n    try {\r\n      console.log('🔄 getRecentVideos called with limit:', limit)\r\n      const topics = await this.getTopicTables()\r\n      console.log('🔄 Found topics:', topics.length, topics.map(t => t.table_name))\r\n      \r\n      const videoPromises = topics.map(topic => \r\n        this.getVideosByTopic(topic.table_name, Math.ceil(limit / topics.length)) // Use table_name\r\n      )\r\n      \r\n      const videoArrays = await Promise.all(videoPromises)\r\n      console.log('🔄 Video arrays lengths:', videoArrays.map(arr => arr.length))\r\n      \r\n      const allVideos = videoArrays.flat()\r\n      console.log('🔄 Total videos before sorting:', allVideos.length)\r\n      \r\n      // Sort by published date and return top results\r\n      // Handle data quality issue: ignore fake published_at dates that match created_at\r\n      const sortedVideos = allVideos\r\n        .sort((a, b) => {\r\n          const isRealDateA = a.published_at && a.created_at && a.published_at !== a.created_at;\r\n          const isRealDateB = b.published_at && b.created_at && b.published_at !== b.created_at;\r\n\r\n          const dateA = isRealDateA ? new Date(a.published_at).getTime() : 0;\r\n          const dateB = isRealDateB ? new Date(b.published_at).getTime() : 0;\r\n          return dateB - dateA;\r\n        })\r\n        .slice(0, limit)\r\n      \r\n      console.log('🔄 Final sorted videos:', sortedVideos.length)\r\n      return sortedVideos\r\n    } catch (error) {\r\n      console.error('Error fetching recent videos:', error)\r\n      return []\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM;IACX,4DAA4D;IAC5D,aAAa,iBAAwC;QACnD,IAAI;YACF,sEAAsE;YACtE,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,MAAM,iBAA+B,EAAE;YAEvC,QAAQ,GAAG,CAAC;YACZ,KAAK,MAAM,SAAS,cAAe;gBACjC,IAAI;oBACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACzC,IAAI,CAAC,OACL,MAAM,CAAC,MAAM;wBAAE,OAAO;wBAAS,MAAM;oBAAK,GAC1C,KAAK,CAAC;oBAET,IAAI,CAAC,OAAO;wBACV,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC;wBAC7D,eAAe,IAAI,CAAC;4BAClB,IAAI;4BACJ,YAAY;4BACZ,MAAM;4BACN,cAAc,IAAI,CAAC,iBAAiB,CAAC;4BACrC,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO,CAAC;4BACtD,aAAa,SAAS;4BACtB,YAAY,IAAI,OAAO,WAAW;4BAClC,YAAY,IAAI,OAAO,WAAW,GAAG,cAAc;wBACrD;oBACF;gBACF,EAAE,OAAM;gBACN,qCAAqC;gBACvC;YACF;YAEA,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,OAAO;YACT;YAEA,QAAQ,IAAI,CAAC;YACb,yCAAyC;YACzC,MAAM,eAAe,IAAI,OAAO,WAAW;YAC3C,OAAO;gBACL;oBAAE,IAAI;oBAAmC,YAAY;oBAAmC,MAAM;oBAAmC,cAAc;oBAA2B,aAAa;oBAAkC,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;gBAC5R;oBAAE,IAAI;oBAA0B,YAAY;oBAA0B,MAAM;oBAA0B,cAAc;oBAAkB,aAAa;oBAAyC,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;gBAC/P;oBAAE,IAAI;oBAAoB,YAAY;oBAAoB,MAAM;oBAAoB,cAAc;oBAAY,aAAa;oBAAuC,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;gBACrO;oBAAE,IAAI;oBAA6B,YAAY;oBAA6B,MAAM;oBAA6B,cAAc;oBAAqB,aAAa;oBAA6B,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;aAChQ;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,yDAAyD;YACzD,MAAM,eAAe,IAAI,OAAO,WAAW;YAC3C,OAAO;gBACL;oBAAE,IAAI;oBAAmC,YAAY;oBAAmC,MAAM;oBAAmC,cAAc;oBAA2B,aAAa;oBAAkC,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;gBAC5R;oBAAE,IAAI;oBAA0B,YAAY;oBAA0B,MAAM;oBAA0B,cAAc;oBAAkB,aAAa;oBAAyC,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;gBAC/P;oBAAE,IAAI;oBAAoB,YAAY;oBAAoB,MAAM;oBAAoB,cAAc;oBAAY,aAAa;oBAAuC,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;gBACrO;oBAAE,IAAI;oBAA6B,YAAY;oBAA6B,MAAM;oBAA6B,cAAc;oBAAqB,aAAa;oBAA6B,aAAa;oBAAG,YAAY;oBAAc,YAAY;gBAAa;aAChQ;QACH;IACF;IAEA,wCAAwC;IACxC,OAAe,kBAAkB,SAAiB,EAAU;QAC1D,OAAO,UACJ,OAAO,CAAC,YAAY,IAAI,yBAAyB;SACjD,OAAO,CAAC,KAAK,KACb,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IACA,sDAAsD;IACtD,aAAa,YAAY,WAAsB,EAAqB;QAClE,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,iCAAiC;YACjC,MAAM,iBAAiB,MAAM,IAAI,CAAC,cAAc;YAEhD,iDAAiD;YACjD,MAAM,cAAc,eAAe,YAAY,MAAM,GAAG,IACpD,eAAe,MAAM,CAAC,CAAA;gBACpB,MAAM,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI;gBAC/E,OAAO,YAAY,QAAQ,CAAC,eAAe,YAAY,QAAQ,CAAC,EAAE,IAAI;YACxE,KACA;YAEJ,QAAQ,GAAG,CAAC,kCAAkC,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YACzE,QAAQ,GAAG,CAAC,yBAAyB;YACnC,uCAAuC;YACzC,KAAK,MAAM,SAAS,YAAa;gBAAS,IAAI;oBAC1C,oEAAoE;oBACpE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,MAAM,IAAI,EACf,MAAM,CAAC,gBACP,GAAG,CAAC,gBAAgB,MAAM,MAC1B,KAAK,CAAC;oBAET,IAAI,CAAC,SAAS,MAAM;wBAClB,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,MAAM,CAAC,oBAAoB,EAAE,MAAM,IAAI,EAAE;wBACnE,KAAK,OAAO,CAAC,CAAC;4BACZ,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,IAAI,IAAI;gCACjD,YAAY,GAAG,CAAC,KAAK,YAAY,CAAC,IAAI;4BACxC;wBACF;oBACF,OAAO,IAAI,OAAO;wBAChB,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,OAAO;oBAC3E;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,IAAI,CAAC,CAAC,sBAAsB,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE;gBACrD,6BAA6B;gBAC/B;YACF;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,IAAI;YAC7C,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,SAAS,MAAM,EAAE;YAE7D,gFAAgF;YAChF,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,QAAQ,IAAI,CAAC;gBACb,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;YACH;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,yEAAyE;YACzE,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;IACF;IACA,aAAa,kBACX,WAAsB,EACtB,QAAmB,EACnB,QAAgB,EAAE,EAClB,SAAiB,CAAC,EAClB,SAAkB,EAClB,OAAgB,EACS;QACzB,IAAI;YACF,MAAM,YAA4B,EAAE;YACpC,MAAM,eAAe,IAAI,MAAc,wCAAwC;;YAE/E,6CAA6C;YAC7C,MAAM,gBAAgB,eAAe,YAAY,MAAM,GAAG,IACtD,cACA,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,GAAG,iBAAiB;YACzE,kFAAkF;YACpF,qFAAqF;YACrF,oEAAoE;YACpE,MAAM,aAAa,AAAC,YAAY,SAAS,MAAM,GAAG,KAAM,aAAa;YACrE,MAAM,aAAa,aAAa,KAAK,EAAE,iDAAiD;;YACxF,MAAM,gBAAgB,KAAK,GAAG,CAAC,CAAC,QAAQ,MAAM,IAAI,YAAY;YAE9D,mBAAmB;YACnB,KAAK,MAAM,aAAa,cAAe;gBACrC,IAAI;oBACF,2EAA2E;oBAC3E,IAAI,eAAe,wHAAA,CAAA,gBAAa,CAC7B,IAAI,CAAC,WACL,MAAM,CAAC,KACP,KAAK,CAAC,gBAAgB;wBAAE,WAAW;wBAAO,WAAW;oBAAK;oBAE7D,gBAAgB;oBAChB,IAAI,YAAY,SAAS,MAAM,GAAG,GAAG;wBACnC,eAAe,aAAa,EAAE,CAAC,gBAAgB;oBACjD;oBACA,IAAI,WAAW;wBACb,eAAe,aAAa,GAAG,CAAC,gBAAgB;oBAClD;oBACA,IAAI,SAAS;wBACX,eAAe,aAAa,GAAG,CAAC,gBAAgB;oBAClD;oBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,aAAa,KAAK,CAAC;oBAEjD,IAAI,CAAC,SAAS,MAAM;wBAClB,sFAAsF;wBACtF,KAAK,MAAM,SAAS,KAAM;4BACxB,IAAI,CAAC,aAAa,GAAG,CAAC,MAAM,QAAQ,GAAG;gCACrC,4DAA4D;gCAC5D,IAAI,UAAU,MAAM,GAAG,GAAG;oCACxB,QAAQ,GAAG,CAAC,mBAAmB;wCAC7B,OAAO,MAAM,KAAK,EAAE,UAAU,GAAG;wCACjC,cAAc,MAAM,YAAY;wCAChC,YAAY,MAAM,UAAU;wCAC5B,OAAO,MAAM,YAAY,KAAK,MAAM,UAAU;oCAChD;gCACF;gCAEA,2EAA2E;gCAC3E,MAAM,eAAe,OAAO,MAAM,YAAY,IAAI;gCAClD,MAAM,aAAa,OAAO,MAAM,UAAU,IAAI;gCAC9C,MAAM,aAAa,gBAAgB,cAAc,iBAAiB;gCAElE,IAAI,YAAY;oCACd,aAAa,GAAG,CAAC,MAAM,QAAQ;oCAC/B,UAAU,IAAI,CAAC;gCACjB;4BACF;wBACF;oBACF;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,UAAU,CAAC,CAAC,EAAE;gBACzD,6BAA6B;gBAC/B;YACF;YAEA,wDAAwD;YACxD,kFAAkF;YAClF,MAAM,eAAe,UAAU,IAAI,CAAC,CAAC,GAAG;gBACtC,MAAM,cAAc,EAAE,YAAY,IAAI,EAAE,UAAU,IAAI,EAAE,YAAY,KAAK,EAAE,UAAU;gBACrF,MAAM,cAAc,EAAE,YAAY,IAAI,EAAE,UAAU,IAAI,EAAE,YAAY,KAAK,EAAE,UAAU;gBAErF,MAAM,QAAQ,cAAc,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;gBACjE,MAAM,QAAQ,cAAc,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;gBACjE,OAAO,QAAQ;YACjB;YAEA,mDAAmD;YACnD,OAAO,aAAa,KAAK,CAAC,QAAQ,SAAS;QAE7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO,EAAE;QACX;IACF;IAEA,yCAAyC;IACzC,aAAa,iBACX,UAAkB,EAClB,QAAgB,EAAE,EAClB,SAAiB,CAAC,EACO;QAAK,IAAI;YAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,YACL,MAAM,CAAC,IACR,0CAA0C;aACzC,KAAK,CAAC,QAAQ;YAEjB,IAAI,OAAO,MAAM;YAEjB,iDAAiD;YACjD,kFAAkF;YAClF,MAAM,aAAa,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC,GAAG;gBACvC,MAAM,cAAc,EAAE,YAAY,IAAI,EAAE,UAAU,IAAI,EAAE,YAAY,KAAK,EAAE,UAAU;gBACrF,MAAM,cAAc,EAAE,YAAY,IAAI,EAAE,UAAU,IAAI,EAAE,YAAY,KAAK,EAAE,UAAU;gBAErF,MAAM,QAAQ,cAAc,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;gBACjE,MAAM,QAAQ,cAAc,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;gBACjE,OAAO,QAAQ;YACjB;YAEA,OAAO,WAAW,KAAK,CAAC,QAAQ,SAAS;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,WAAW,CAAC,CAAC,EAAE;YAC3D,OAAO,EAAE;QACX;IACF;IACA,4EAA4E;IAC5E,aAAa,aAAa,gBAAwB,EAAE,OAAe,EAAgC;QACjG,IAAI,QAA6B;QACjC,IAAI,yBAAyB;QAC7B,IAAI,0BAAkD;QAEtD,wCAAwC;QACxC,IAAI,CAAC,WAAW,OAAO,YAAY,YAAY,QAAQ,IAAI,OAAO,IAAI;YACpE,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,QAAQ,sBAAsB,CAAC;YAC5F,OAAO;QACT;QAEA,IAAI,oBAAoB,OAAO,qBAAqB,YAAY,iBAAiB,IAAI,OAAO,IAAI;YAC9F,MAAM,kBAAkB,iBAAiB,UAAU,CAAC,cAChD,mBACA,CAAC,QAAQ,EAAE,kBAAkB;YAEjC,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,QAAQ,uBAAuB,EAAE,gBAAgB,oBAAoB,EAAE,iBAAiB,EAAE,CAAC;YACrJ,yBAAyB;YAEzB,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACvD,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,YAAY,SACf,MAAM;gBAET,IAAI,eAAe;oBACjB,0BAA0B,eAAe,0BAA0B;oBACnE,QAAQ,IAAI,CAAC,CAAC,+CAA+C,EAAE,QAAQ,cAAc,EAAE,gBAAgB,SAAS,EAAE,cAAc,IAAI,CAAC,WAAW,EAAE,cAAc,OAAO,CAAC,gCAAgC,CAAC;gBACzM,2EAA2E;gBAC7E,OAAO,IAAI,MAAM;oBACf,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,QAAQ,YAAY,EAAE,gBAAgB,CAAC,CAAC;oBACjG,MAAM,kBAAkB,gBAAgB,UAAU,CAAC,cAAc,gBAAgB,SAAS,CAAC,KAAK;oBAChG,4EAA4E;oBAC5E,QAAQ;wBAAE,GAAG,IAAI;wBAAE,gBAAgB;wBAAiB,cAAc,KAAK,YAAY,IAAI,CAAC;oBAAE;gBAC5F,OAAO;oBACL,kGAAkG;oBAClG,QAAQ,IAAI,CAAC,CAAC,gDAAgD,EAAE,QAAQ,cAAc,EAAE,gBAAgB,oEAAoE,CAAC;gBAC/K;YACF,EAAE,OAAO,cAAuB;gBAC9B,0BAA0B;gBAC1B,MAAM,eAAe,wBAAwB,QAAQ,aAAa,OAAO,GAAG,OAAO;gBACnF,QAAQ,KAAK,CAAC,CAAC,0DAA0D,EAAE,gBAAgB,eAAe,EAAE,QAAQ,UAAU,EAAE,aAAa,gCAAgC,CAAC;YAChL;QACF,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,8DAA8D,EAAE,iBAAiB,kEAAkE,EAAE,QAAQ,EAAE,CAAC;QAChL;QAEA,mFAAmF;QACnF,IAAI,CAAC,OAAO;YACV,IAAI,wBAAwB;gBAC1B,IAAI,aAAa;gBACjB,IAAI,yBAAyB;oBAC3B,IAAI,mCAAmC,OAAO;wBAC5C,aAAa,wBAAwB,OAAO;oBAC9C,OAAO;wBACL,IAAI;4BACF,aAAa,KAAK,SAAS,CAAC;wBAC9B,EAAE,OAAM;4BACN,aAAa;wBACf;oBACF;gBACF;gBACA,QAAQ,IAAI,CAAC,CAAC,uBAAuB,EAAE,QAAQ,6EAA6E,EAAE,YAAY;YAC5I;YACA,QAAQ,GAAG,CAAC,CAAC,oEAAoE,EAAE,QAAQ,EAAE,CAAC;YAC9F,IAAI;gBACF,MAAM,oBAAoB,MAAM,IAAI,CAAC,wBAAwB,CAAC;gBAC9D,IAAI,mBAAmB;oBACrB,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,QAAQ,+BAA+B,CAAC;oBACpF,4EAA4E;oBAC5E,QAAQ;wBAAE,GAAG,iBAAiB;wBAAE,cAAc,kBAAkB,YAAY,IAAI,CAAC;oBAAE;gBACrF,OAAO;oBACL,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,QAAQ,uDAAuD,CAAC;gBAC1G;YACF,EAAE,OAAO,eAAwB;gBAC/B,MAAM,uBAAuB,yBAAyB,QAAQ,cAAc,OAAO,GAAG,OAAO;gBAC7F,QAAQ,KAAK,CAAC,CAAC,0EAA0E,EAAE,QAAQ,UAAU,EAAE,sBAAsB;YACrI,qBAAqB;YACvB;QACF;QAEA,IAAI,CAAC,OAAO;YACP,MAAM,mBAAmB,mCAAmC,QAAQ,wBAAwB,OAAO,GAAI,0BAA0B,yBAAyB;YAC1J,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,QAAQ,oBAAoB,EAAE,iBAAiB,4DAA4D,EAAE,kBAAkB;QAC9L,OAAO;YACH,oCAAoC;YACpC,IAAI,CAAC,MAAM,cAAc,IAAI,kBAAkB;gBAC3C,MAAM,cAAc,GAAG,iBAAiB,OAAO,CAAC,YAAY;YAChE,OAAO,IAAI,CAAC,MAAM,cAAc,EAAE;gBAC9B,MAAM,cAAc,GAAG,WAAW,kCAAkC;YACxE;QACJ;QACA,OAAO;IACT;IAEA,6CAA6C;IAC7C,aAAa,yBAAyB,OAAe,EAAgC;QACnF,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,QAAQ,CAAC,CAAC;QAC1E,IAAI,CAAC,WAAW,OAAO,YAAY,YAAY,QAAQ,IAAI,OAAO,IAAI;YACpE,QAAQ,KAAK,CAAC,CAAC,wDAAwD,EAAE,QAAQ,EAAE,CAAC;YACpF,OAAO;QACT;QACA,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc;YACxC,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAAG;gBAClC,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;YAEA,KAAK,MAAM,SAAS,OAAQ;gBAC1B,IAAI;oBACF,MAAM,mBAAmB,MAAM,UAAU;oBACzC,IAAI,CAAC,kBAAkB;wBACrB,QAAQ,IAAI,CAAC,CAAC,iCAAiC,EAAE,MAAM,IAAI,CAAC,oCAAoC,CAAC;wBACjG;oBACF;oBACA,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,iBAAiB,eAAe,EAAE,QAAQ,CAAC,CAAC;oBACrG,MAAM,EAAE,IAAI,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACvD,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,EAAE,CAAC,YAAY,SACf,MAAM;oBAET,IAAI,CAAC,iBAAiB,MAAM;wBAC1B,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,QAAQ,YAAY,EAAE,iBAAiB,CAAC,CAAC;wBACjG,MAAM,kBAAkB,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM,IAAI;wBAChG,4EAA4E;wBAC5E,OAAO;4BAAE,GAAG,IAAI;4BAAE,gBAAgB;4BAAiB,cAAc,KAAK,YAAY,IAAI,CAAC;wBAAE;oBAC3F;oBACA,IAAI,iBAAiB,cAAc,IAAI,KAAK,YAAY;wBACtD,QAAQ,IAAI,CAAC,CAAC,yDAAyD,EAAE,iBAAiB,eAAe,EAAE,QAAQ,SAAS,EAAE,cAAc,IAAI,CAAC,WAAW,EAAE,cAAc,OAAO,EAAE;oBACvL;gBACF,EAAE,OAAO,iBAA0B;oBACjC,MAAM,yBAAyB,2BAA2B,QAAQ,gBAAgB,OAAO,GAAG,OAAO;oBACnG,QAAQ,IAAI,CAAC,CAAC,0DAA0D,EAAE,MAAM,UAAU,CAAC,eAAe,EAAE,QAAQ,YAAY,EAAE,wBAAwB;gBAC5J;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ,kDAAkD,CAAC;YAC7G,OAAO;QACT,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;YACrE,QAAQ,KAAK,CAAC,CAAC,+DAA+D,EAAE,QAAQ,8BAA8B,EAAE,cAAc;YACtI,OAAO;QACT;IACF;IAEA,gEAAgE;IAChE,aAAa,aACX,KAAa,EACb,eAAyB,EAAE,EAC3B,QAAgB,EAAE,EACO;QACzB,IAAI;YACF,8DAA8D;YAC9D,MAAM,oBAAoB,MAAM,MAAM,mBAAmB;gBACvD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;gBAAM;YACrC;YAEA,IAAI,CAAC,kBAAkB,EAAE,EAAE,MAAM,IAAI,MAAM;YAE3C,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,kBAAkB,IAAI;YAElD,qDAAqD;YACrD,MAAM,iBAAiB,aAAa,MAAM,GAAG,IACzC,aAAa,GAAG,CAAC,CAAA,QAAS,IAAI,CAAC,aAAa,CAAC,OAAO,WAAW,UAC/D,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW;YAE1C,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAElC,uCAAuC;YACvC,OAAO,QACJ,IAAI,GACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,gBAAgB,GAAG,EAAE,gBAAgB,EACtD,KAAK,CAAC,GAAG;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;IACF;IAEA,aAAqB,cACnB,SAAiB,EACjB,SAAmB,EACnB,KAAa,EACY;QAAK,IAAI;YAChC,iCAAiC;YACjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACxC,GAAG,CAAC,8BAA8B;gBACjC,YAAY;gBACZ,iBAAiB;gBACjB,iBAAiB;gBACjB,aAAa;YACf;YAEF,IAAI,OAAO,MAAM;YACjB,OAAO,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC,OAAuF,CAAC;oBAC/G,OAAO,KAAK,KAAK;oBACjB,kBAAkB,KAAK,gBAAgB;oBACvC,4BAA4B,KAAK,eAAe;gBAClD,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAC,EAAE;YACxD,OAAO,EAAE;QACX;IACF;IAEA,aAAqB,gBACnB,SAAmB,EACnB,KAAa,EACc;QAC3B,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc;QACxC,OAAO,QAAQ,GAAG,CAChB,OAAO,GAAG,CAAC,CAAA,QAAS,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,EAAE,WAAW;IAElE;IACA,sCAAsC;IACtC,aAAa,gBAAgB,QAAgB,EAAE,EAA2B;QACxE,IAAI;YACF,QAAQ,GAAG,CAAC,yCAAyC;YACrD,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc;YACxC,QAAQ,GAAG,CAAC,oBAAoB,OAAO,MAAM,EAAE,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU;YAE3E,MAAM,gBAAgB,OAAO,GAAG,CAAC,CAAA,QAC/B,IAAI,CAAC,gBAAgB,CAAC,MAAM,UAAU,EAAE,KAAK,IAAI,CAAC,QAAQ,OAAO,MAAM,GAAG,iBAAiB;;YAG7F,MAAM,cAAc,MAAM,QAAQ,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,4BAA4B,YAAY,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM;YAEzE,MAAM,YAAY,YAAY,IAAI;YAClC,QAAQ,GAAG,CAAC,mCAAmC,UAAU,MAAM;YAE/D,gDAAgD;YAChD,kFAAkF;YAClF,MAAM,eAAe,UAClB,IAAI,CAAC,CAAC,GAAG;gBACR,MAAM,cAAc,EAAE,YAAY,IAAI,EAAE,UAAU,IAAI,EAAE,YAAY,KAAK,EAAE,UAAU;gBACrF,MAAM,cAAc,EAAE,YAAY,IAAI,EAAE,UAAU,IAAI,EAAE,YAAY,KAAK,EAAE,UAAU;gBAErF,MAAM,QAAQ,cAAc,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;gBACjE,MAAM,QAAQ,cAAc,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK;gBACjE,OAAO,QAAQ;YACjB,GACC,KAAK,CAAC,GAAG;YAEZ,QAAQ,GAAG,CAAC,2BAA2B,aAAa,MAAM;YAC1D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,EAAE;QACX;IACF;AACF", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dropbox/0%20-%20Youtube%20Summaries/nextjs-youtube-summaries/src/app/api/videos/filtered/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\r\nimport { DatabaseService } from '@/lib/database'\r\n\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const { searchParams } = new URL(request.url)\r\n    const topicsParam = searchParams.get('topics')\r\n    const channelsParam = searchParams.get('channels')\r\n    const limit = parseInt(searchParams.get('limit') || '20')\r\n    const offset = parseInt(searchParams.get('offset') || '0')\r\n    const startDate = searchParams.get('startDate')\r\n    const endDate = searchParams.get('endDate')\r\n    \r\n    const topics = topicsParam ? topicsParam.split(',').filter(t => t.trim()) : undefined\r\n    const channels = channelsParam ? channelsParam.split(',').filter(c => c.trim()) : undefined\r\n    \r\n    const videos = await DatabaseService.getFilteredVideos(\r\n      topics, \r\n      channels, \r\n      limit, \r\n      offset,\r\n      startDate || undefined,\r\n      endDate || undefined\r\n    )\r\n    \r\n    return NextResponse.json({ \r\n      videos,\r\n      count: videos.length,\r\n      filters: {\r\n        topics: topics || [],\r\n        channels: channels || []\r\n      }\r\n    })\r\n  } catch (error) {\r\n    console.error('Error in filtered videos API:', error)\r\n    return NextResponse.json(\r\n      { error: 'Failed to fetch filtered videos' },\r\n      { status: 500 }\r\n    )\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,cAAc,aAAa,GAAG,CAAC;QACrC,MAAM,gBAAgB,aAAa,GAAG,CAAC;QACvC,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,SAAS,aAAa,GAAG,CAAC,aAAa;QACtD,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,MAAM,SAAS,cAAc,YAAY,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,MAAM;QAC5E,MAAM,WAAW,gBAAgB,cAAc,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,MAAM;QAElF,MAAM,SAAS,MAAM,wHAAA,CAAA,kBAAe,CAAC,iBAAiB,CACpD,QACA,UACA,OACA,QACA,aAAa,WACb,WAAW;QAGb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,OAAO,OAAO,MAAM;YACpB,SAAS;gBACP,QAAQ,UAAU,EAAE;gBACpB,UAAU,YAAY,EAAE;YAC1B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}