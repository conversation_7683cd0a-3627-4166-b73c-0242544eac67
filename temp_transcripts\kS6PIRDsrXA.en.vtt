WEBVTT
Kind: captions
Language: en

00:00:00.240 --> 00:00:02.389 align:start position:0%
 
hey<00:00:00.719><c> there</c><00:00:00.900><c> it's</c><00:00:01.260><c> Matt</c><00:00:01.500><c> <PERSON></c><00:00:01.740><c> your</c>

00:00:02.389 --> 00:00:02.399 align:start position:0%
hey there it's <PERSON> your
 

00:00:02.399 --> 00:00:05.150 align:start position:0%
hey there it's <PERSON> your
favorite<00:00:02.780><c> evangelist</c><00:00:03.780><c> well</c><00:00:04.560><c> I</c><00:00:04.799><c> just</c><00:00:04.980><c> found</c>

00:00:05.150 --> 00:00:05.160 align:start position:0%
favorite evangelist well I just found
 

00:00:05.160 --> 00:00:08.030 align:start position:0%
favorite evangelist well I just found
out<00:00:05.400><c> about</c><00:00:05.580><c> something</c><00:00:05.940><c> so</c><00:00:06.720><c> cool</c><00:00:06.960><c> I</c><00:00:07.740><c> just</c><00:00:07.919><c> have</c>

00:00:08.030 --> 00:00:08.040 align:start position:0%
out about something so cool I just have
 

00:00:08.040 --> 00:00:10.190 align:start position:0%
out about something so cool I just have
to<00:00:08.160><c> share</c><00:00:08.340><c> it</c><00:00:08.460><c> with</c><00:00:08.580><c> you</c><00:00:08.760><c> right</c><00:00:09.179><c> away</c><00:00:09.360><c> you</c><00:00:10.080><c> know</c>

00:00:10.190 --> 00:00:10.200 align:start position:0%
to share it with you right away you know
 

00:00:10.200 --> 00:00:12.530 align:start position:0%
to share it with you right away you know
in<00:00:10.380><c> fact</c><00:00:10.559><c> every</c><00:00:11.219><c> other</c><00:00:11.400><c> video</c><00:00:11.639><c> well</c><00:00:12.300><c> most</c>

00:00:12.530 --> 00:00:12.540 align:start position:0%
in fact every other video well most
 

00:00:12.540 --> 00:00:14.150 align:start position:0%
in fact every other video well most
other<00:00:12.780><c> videos</c><00:00:13.019><c> I've</c><00:00:13.380><c> done</c><00:00:13.559><c> on</c><00:00:13.799><c> this</c><00:00:13.980><c> channel</c>

00:00:14.150 --> 00:00:14.160 align:start position:0%
other videos I've done on this channel
 

00:00:14.160 --> 00:00:18.590 align:start position:0%
other videos I've done on this channel
are<00:00:15.120><c> you</c><00:00:15.599><c> know</c><00:00:15.719><c> well</c><00:00:16.500><c> scripted</c><00:00:17.100><c> and</c><00:00:17.940><c> I'm</c>

00:00:18.590 --> 00:00:18.600 align:start position:0%
are you know well scripted and I'm
 

00:00:18.600 --> 00:00:21.890 align:start position:0%
are you know well scripted and I'm
talking<00:00:18.840><c> to</c><00:00:19.140><c> a</c><00:00:19.320><c> teleprompter</c><00:00:20.300><c> and</c><00:00:21.300><c> it's</c><00:00:21.600><c> very</c>

00:00:21.890 --> 00:00:21.900 align:start position:0%
talking to a teleprompter and it's very
 

00:00:21.900 --> 00:00:24.650 align:start position:0%
talking to a teleprompter and it's very
polished<00:00:22.619><c> and</c><00:00:22.980><c> I'm</c><00:00:23.220><c> in</c><00:00:23.460><c> my</c><00:00:23.760><c> studio</c><00:00:24.060><c> downstairs</c>

00:00:24.650 --> 00:00:24.660 align:start position:0%
polished and I'm in my studio downstairs
 

00:00:24.660 --> 00:00:27.529 align:start position:0%
polished and I'm in my studio downstairs
but<00:00:25.380><c> this</c><00:00:25.740><c> time</c><00:00:25.920><c> you</c><00:00:26.160><c> know</c><00:00:26.279><c> I'm</c><00:00:26.699><c> I'm</c><00:00:26.760><c> just</c><00:00:27.180><c> you</c>

00:00:27.529 --> 00:00:27.539 align:start position:0%
but this time you know I'm I'm just you
 

00:00:27.539 --> 00:00:30.130 align:start position:0%
but this time you know I'm I'm just you
know<00:00:27.660><c> at</c><00:00:27.900><c> my</c><00:00:28.140><c> desk</c><00:00:28.320><c> upstairs</c><00:00:28.920><c> in</c><00:00:29.640><c> our</c><00:00:29.820><c> bedroom</c>

00:00:30.130 --> 00:00:30.140 align:start position:0%
know at my desk upstairs in our bedroom
 

00:00:30.140 --> 00:00:33.830 align:start position:0%
know at my desk upstairs in our bedroom
and<00:00:31.140><c> yeah</c><00:00:31.619><c> just</c><00:00:32.099><c> just</c><00:00:32.340><c> wanted</c><00:00:32.640><c> to</c><00:00:32.880><c> wing</c><00:00:33.180><c> it</c><00:00:33.360><c> and</c>

00:00:33.830 --> 00:00:33.840 align:start position:0%
and yeah just just wanted to wing it and
 

00:00:33.840 --> 00:00:36.229 align:start position:0%
and yeah just just wanted to wing it and
and<00:00:34.079><c> spit</c><00:00:34.440><c> this</c><00:00:34.620><c> video</c><00:00:34.800><c> out</c><00:00:35.219><c> really</c><00:00:36.000><c> really</c>

00:00:36.229 --> 00:00:36.239 align:start position:0%
and spit this video out really really
 

00:00:36.239 --> 00:00:40.270 align:start position:0%
and spit this video out really really
quick<00:00:36.600><c> because</c><00:00:37.320><c> I'm</c><00:00:38.100><c> excited</c><00:00:38.640><c> about</c>

00:00:40.270 --> 00:00:40.280 align:start position:0%
quick because I'm excited about
 

00:00:40.280 --> 00:00:43.549 align:start position:0%
quick because I'm excited about
transcribing<00:00:41.280><c> video</c><00:00:42.000><c> so</c><00:00:42.719><c> you</c><00:00:42.960><c> know</c><00:00:43.079><c> one</c><00:00:43.440><c> of</c>

00:00:43.549 --> 00:00:43.559 align:start position:0%
transcribing video so you know one of
 

00:00:43.559 --> 00:00:45.950 align:start position:0%
transcribing video so you know one of
the<00:00:43.620><c> things</c><00:00:43.800><c> I</c><00:00:44.040><c> do</c><00:00:44.219><c> with</c><00:00:44.579><c> all</c><00:00:44.820><c> my</c><00:00:44.940><c> videos</c><00:00:45.120><c> is</c><00:00:45.540><c> I</c>

00:00:45.950 --> 00:00:45.960 align:start position:0%
the things I do with all my videos is I
 

00:00:45.960 --> 00:00:48.229 align:start position:0%
the things I do with all my videos is I
post<00:00:46.140><c> it</c><00:00:46.320><c> but</c><00:00:46.680><c> then</c><00:00:46.860><c> I</c><00:00:47.040><c> also</c><00:00:47.340><c> post</c><00:00:47.640><c> the</c><00:00:48.000><c> script</c>

00:00:48.229 --> 00:00:48.239 align:start position:0%
post it but then I also post the script
 

00:00:48.239 --> 00:00:50.869 align:start position:0%
post it but then I also post the script
which<00:00:48.660><c> gets</c><00:00:49.020><c> converted</c><00:00:49.440><c> into</c><00:00:49.680><c> an</c><00:00:49.980><c> SRT</c><00:00:50.399><c> file</c><00:00:50.640><c> by</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
which gets converted into an SRT file by
 

00:00:50.879 --> 00:00:53.930 align:start position:0%
which gets converted into an SRT file by
YouTube<00:00:51.059><c> and</c><00:00:51.899><c> then</c><00:00:52.020><c> that</c><00:00:52.379><c> file</c><00:00:52.739><c> shows</c><00:00:53.399><c> up</c><00:00:53.520><c> as</c>

00:00:53.930 --> 00:00:53.940 align:start position:0%
YouTube and then that file shows up as
 

00:00:53.940 --> 00:00:56.810 align:start position:0%
YouTube and then that file shows up as
captions<00:00:54.600><c> within</c><00:00:55.199><c> YouTube</c><00:00:55.440><c> sometimes</c><00:00:56.399><c> people</c>

00:00:56.810 --> 00:00:56.820 align:start position:0%
captions within YouTube sometimes people
 

00:00:56.820 --> 00:00:58.970 align:start position:0%
captions within YouTube sometimes people
would<00:00:57.120><c> like</c><00:00:57.360><c> to</c><00:00:57.840><c> you</c><00:00:58.379><c> know</c><00:00:58.440><c> maybe</c><00:00:58.559><c> they're</c>

00:00:58.970 --> 00:00:58.980 align:start position:0%
would like to you know maybe they're
 

00:00:58.980 --> 00:01:00.770 align:start position:0%
would like to you know maybe they're
Distributing<00:00:59.579><c> the</c><00:00:59.820><c> video</c><00:01:00.059><c> some</c><00:01:00.420><c> other</c><00:01:00.600><c> way</c>

00:01:00.770 --> 00:01:00.780 align:start position:0%
Distributing the video some other way
 

00:01:00.780 --> 00:01:03.410 align:start position:0%
Distributing the video some other way
and<00:01:01.320><c> they</c><00:01:01.440><c> want</c><00:01:01.559><c> to</c><00:01:01.739><c> have</c><00:01:01.860><c> the</c><00:01:02.039><c> SRT</c><00:01:02.520><c> file</c><00:01:02.820><c> so</c>

00:01:03.410 --> 00:01:03.420 align:start position:0%
and they want to have the SRT file so
 

00:01:03.420 --> 00:01:06.590 align:start position:0%
and they want to have the SRT file so
you<00:01:03.719><c> could</c><00:01:03.840><c> just</c><00:01:04.140><c> upload</c><00:01:04.680><c> it</c><00:01:04.860><c> to</c><00:01:05.100><c> YouTube</c>

00:01:06.590 --> 00:01:06.600 align:start position:0%
you could just upload it to YouTube
 

00:01:06.600 --> 00:01:08.750 align:start position:0%
you could just upload it to YouTube
let<00:01:07.140><c> it</c><00:01:07.320><c> do</c><00:01:07.439><c> its</c><00:01:07.680><c> transcription</c><00:01:07.979><c> keep</c><00:01:08.400><c> it</c><00:01:08.580><c> a</c>

00:01:08.750 --> 00:01:08.760 align:start position:0%
let it do its transcription keep it a
 

00:01:08.760 --> 00:01:11.450 align:start position:0%
let it do its transcription keep it a
private<00:01:09.060><c> file</c><00:01:09.299><c> and</c><00:01:09.900><c> then</c><00:01:10.080><c> delete</c><00:01:10.560><c> the</c><00:01:11.040><c> YouTube</c>

00:01:11.450 --> 00:01:11.460 align:start position:0%
private file and then delete the YouTube
 

00:01:11.460 --> 00:01:14.390 align:start position:0%
private file and then delete the YouTube
video<00:01:12.060><c> but</c><00:01:12.720><c> download</c><00:01:12.960><c> the</c><00:01:13.320><c> SRT</c><00:01:13.799><c> file</c><00:01:14.159><c> first</c>

00:01:14.390 --> 00:01:14.400 align:start position:0%
video but download the SRT file first
 

00:01:14.400 --> 00:01:16.789 align:start position:0%
video but download the SRT file first
and<00:01:14.939><c> that's</c><00:01:15.060><c> one</c><00:01:15.420><c> way</c><00:01:15.600><c> of</c><00:01:15.840><c> getting</c><00:01:16.200><c> kind</c><00:01:16.500><c> of</c><00:01:16.619><c> a</c>

00:01:16.789 --> 00:01:16.799 align:start position:0%
and that's one way of getting kind of a
 

00:01:16.799 --> 00:01:18.170 align:start position:0%
and that's one way of getting kind of a
cheap<00:01:16.979><c> transcription</c><00:01:17.400><c> or</c><00:01:17.640><c> you</c><00:01:17.760><c> can</c><00:01:17.880><c> use</c><00:01:17.939><c> any</c>

00:01:18.170 --> 00:01:18.180 align:start position:0%
cheap transcription or you can use any
 

00:01:18.180 --> 00:01:20.990 align:start position:0%
cheap transcription or you can use any
of<00:01:18.299><c> these</c><00:01:18.540><c> transcription</c><00:01:19.260><c> services</c><00:01:19.939><c> and</c><00:01:20.939><c> then</c>

00:01:20.990 --> 00:01:21.000 align:start position:0%
of these transcription services and then
 

00:01:21.000 --> 00:01:23.030 align:start position:0%
of these transcription services and then
there's<00:01:21.299><c> there</c><00:01:21.780><c> are</c><00:01:22.140><c> a</c><00:01:22.140><c> lot</c><00:01:22.259><c> of</c><00:01:22.380><c> there's</c><00:01:22.740><c> other</c>

00:01:23.030 --> 00:01:23.040 align:start position:0%
there's there are a lot of there's other
 

00:01:23.040 --> 00:01:25.010 align:start position:0%
there's there are a lot of there's other
options<00:01:23.400><c> you</c><00:01:23.580><c> could</c><00:01:23.700><c> use</c><00:01:23.820><c> as</c><00:01:24.060><c> well</c>

00:01:25.010 --> 00:01:25.020 align:start position:0%
options you could use as well
 

00:01:25.020 --> 00:01:28.249 align:start position:0%
options you could use as well
but<00:01:25.619><c> I</c><00:01:25.920><c> just</c><00:01:26.040><c> stumbled</c><00:01:26.400><c> on</c><00:01:26.700><c> something</c><00:01:26.939><c> that</c><00:01:27.299><c> is</c>

00:01:28.249 --> 00:01:28.259 align:start position:0%
but I just stumbled on something that is
 

00:01:28.259 --> 00:01:29.710 align:start position:0%
but I just stumbled on something that is
free

00:01:29.710 --> 00:01:29.720 align:start position:0%
free
 

00:01:29.720 --> 00:01:33.109 align:start position:0%
free
and<00:01:30.720><c> really</c><00:01:31.200><c> cool</c><00:01:31.500><c> and</c><00:01:32.040><c> seems</c><00:01:32.400><c> to</c><00:01:32.520><c> work</c><00:01:32.759><c> really</c>

00:01:33.109 --> 00:01:33.119 align:start position:0%
and really cool and seems to work really
 

00:01:33.119 --> 00:01:35.870 align:start position:0%
and really cool and seems to work really
really<00:01:33.299><c> well</c><00:01:33.720><c> and</c><00:01:34.619><c> just</c><00:01:34.860><c> takes</c><00:01:35.159><c> you</c><00:01:35.640><c> know</c><00:01:35.700><c> a</c>

00:01:35.870 --> 00:01:35.880 align:start position:0%
really well and just takes you know a
 

00:01:35.880 --> 00:01:38.450 align:start position:0%
really well and just takes you know a
few<00:01:36.060><c> minutes</c><00:01:36.380><c> so</c><00:01:37.380><c> I</c><00:01:37.680><c> thought</c><00:01:37.740><c> I'd</c><00:01:37.920><c> show</c><00:01:38.159><c> it</c><00:01:38.340><c> to</c>

00:01:38.450 --> 00:01:38.460 align:start position:0%
few minutes so I thought I'd show it to
 

00:01:38.460 --> 00:01:41.330 align:start position:0%
few minutes so I thought I'd show it to
you<00:01:38.579><c> real</c><00:01:39.180><c> quick</c><00:01:39.360><c> on</c><00:01:39.960><c> uh</c><00:01:40.259><c> you</c><00:01:40.560><c> know</c><00:01:40.740><c> just</c><00:01:40.979><c> just</c>

00:01:41.330 --> 00:01:41.340 align:start position:0%
you real quick on uh you know just just
 

00:01:41.340 --> 00:01:43.370 align:start position:0%
you real quick on uh you know just just
do<00:01:41.579><c> a</c><00:01:41.700><c> quick</c><00:01:41.880><c> demo</c><00:01:42.299><c> and</c><00:01:42.479><c> and</c><00:01:42.780><c> then</c><00:01:42.960><c> you</c><00:01:43.079><c> can</c><00:01:43.200><c> see</c>

00:01:43.370 --> 00:01:43.380 align:start position:0%
do a quick demo and and then you can see
 

00:01:43.380 --> 00:01:45.170 align:start position:0%
do a quick demo and and then you can see
what<00:01:43.560><c> it's</c><00:01:43.740><c> like</c><00:01:43.920><c> all</c><00:01:44.340><c> right</c><00:01:44.520><c> let's</c><00:01:44.880><c> get</c><00:01:45.060><c> into</c>

00:01:45.170 --> 00:01:45.180 align:start position:0%
what it's like all right let's get into
 

00:01:45.180 --> 00:01:46.850 align:start position:0%
what it's like all right let's get into
the<00:01:45.420><c> demo</c>

00:01:46.850 --> 00:01:46.860 align:start position:0%
the demo
 

00:01:46.860 --> 00:01:50.690 align:start position:0%
the demo
okay<00:01:47.400><c> so</c><00:01:48.060><c> here</c><00:01:48.420><c> I</c><00:01:48.600><c> am</c><00:01:48.780><c> in</c><00:01:49.140><c> Final</c><00:01:49.320><c> Cut</c><00:01:49.560><c> Pro</c><00:01:49.860><c> and</c>

00:01:50.690 --> 00:01:50.700 align:start position:0%
okay so here I am in Final Cut Pro and
 

00:01:50.700 --> 00:01:53.149 align:start position:0%
okay so here I am in Final Cut Pro and
in<00:01:51.000><c> fact</c><00:01:51.119><c> this</c><00:01:51.420><c> is</c><00:01:51.600><c> the</c><00:01:52.140><c> uh</c><00:01:52.619><c> the</c><00:01:52.799><c> beginning</c><00:01:52.860><c> of</c>

00:01:53.149 --> 00:01:53.159 align:start position:0%
in fact this is the uh the beginning of
 

00:01:53.159 --> 00:01:55.190 align:start position:0%
in fact this is the uh the beginning of
this<00:01:53.340><c> video</c><00:01:53.520><c> that</c><00:01:53.939><c> you're</c><00:01:54.060><c> seeing</c><00:01:54.420><c> on</c><00:01:55.020><c> screen</c>

00:01:55.190 --> 00:01:55.200 align:start position:0%
this video that you're seeing on screen
 

00:01:55.200 --> 00:01:59.870 align:start position:0%
this video that you're seeing on screen
uh<00:01:56.159><c> so</c><00:01:56.340><c> I</c><00:01:56.460><c> can</c><00:01:56.640><c> scroll</c><00:01:57.240><c> back</c><00:01:57.420><c> a</c><00:01:57.600><c> bit</c>

00:01:59.870 --> 00:01:59.880 align:start position:0%
 
 

00:01:59.880 --> 00:02:03.230 align:start position:0%
 
and<00:02:00.420><c> just</c><00:02:00.720><c> takes</c><00:02:01.020><c> you</c><00:02:01.500><c> know</c><00:02:01.560><c> a</c><00:02:01.740><c> few</c><00:02:01.860><c> minutes</c><00:02:02.240><c> so</c>

00:02:03.230 --> 00:02:03.240 align:start position:0%
and just takes you know a few minutes so
 

00:02:03.240 --> 00:02:05.149 align:start position:0%
and just takes you know a few minutes so
I<00:02:03.479><c> thought</c><00:02:03.600><c> I'd</c><00:02:03.780><c> show</c><00:02:04.020><c> it</c><00:02:04.140><c> to</c><00:02:04.259><c> you</c><00:02:04.439><c> okay</c><00:02:04.799><c> so</c>

00:02:05.149 --> 00:02:05.159 align:start position:0%
I thought I'd show it to you okay so
 

00:02:05.159 --> 00:02:06.649 align:start position:0%
I thought I'd show it to you okay so
there's<00:02:05.340><c> that</c><00:02:05.579><c> video</c>

00:02:06.649 --> 00:02:06.659 align:start position:0%
there's that video
 

00:02:06.659 --> 00:02:09.050 align:start position:0%
there's that video
um and<00:02:07.020><c> maybe</c><00:02:07.259><c> let's</c><00:02:07.560><c> say</c><00:02:07.740><c> I</c><00:02:07.979><c> want</c><00:02:08.160><c> to</c><00:02:08.340><c> have</c><00:02:08.700><c> a</c>

00:02:09.050 --> 00:02:09.060 align:start position:0%
um and maybe let's say I want to have a
 

00:02:09.060 --> 00:02:11.930 align:start position:0%
um and maybe let's say I want to have a
transcript<00:02:09.420><c> of</c><00:02:10.319><c> what</c><00:02:10.679><c> I</c><00:02:10.860><c> just</c><00:02:10.979><c> recorded</c><00:02:11.340><c> So</c>

00:02:11.930 --> 00:02:11.940 align:start position:0%
transcript of what I just recorded So
 

00:02:11.940 --> 00:02:14.750 align:start position:0%
transcript of what I just recorded So
how<00:02:12.180><c> do</c><00:02:12.300><c> I</c><00:02:12.420><c> do</c><00:02:12.540><c> that</c><00:02:12.720><c> well</c>

00:02:14.750 --> 00:02:14.760 align:start position:0%
how do I do that well
 

00:02:14.760 --> 00:02:16.430 align:start position:0%
how do I do that well
um<00:02:14.819><c> you</c><00:02:15.360><c> know</c><00:02:15.480><c> actually</c><00:02:15.660><c> I</c><00:02:15.959><c> can't</c><00:02:16.080><c> just</c><00:02:16.260><c> take</c>

00:02:16.430 --> 00:02:16.440 align:start position:0%
um you know actually I can't just take
 

00:02:16.440 --> 00:02:18.410 align:start position:0%
um you know actually I can't just take
the<00:02:16.620><c> original</c><00:02:16.739><c> file</c><00:02:17.220><c> because</c>

00:02:18.410 --> 00:02:18.420 align:start position:0%
the original file because
 

00:02:18.420 --> 00:02:20.570 align:start position:0%
the original file because
uh<00:02:19.020><c> let's</c><00:02:19.200><c> take</c><00:02:19.379><c> a</c><00:02:19.500><c> look</c><00:02:19.680><c> at</c><00:02:20.099><c> you</c><00:02:20.400><c> know</c><00:02:20.459><c> I've</c>

00:02:20.570 --> 00:02:20.580 align:start position:0%
uh let's take a look at you know I've
 

00:02:20.580 --> 00:02:22.250 align:start position:0%
uh let's take a look at you know I've
got<00:02:20.760><c> some</c><00:02:20.879><c> edits</c><00:02:21.360><c> here</c>

00:02:22.250 --> 00:02:22.260 align:start position:0%
got some edits here
 

00:02:22.260 --> 00:02:24.650 align:start position:0%
got some edits here
um where<00:02:22.560><c> you</c><00:02:22.739><c> I</c><00:02:22.980><c> mean</c><00:02:23.099><c> you</c><00:02:23.220><c> can</c><00:02:23.340><c> watch</c><00:02:23.760><c> and</c>

00:02:24.650 --> 00:02:24.660 align:start position:0%
um where you I mean you can watch and
 

00:02:24.660 --> 00:02:26.150 align:start position:0%
um where you I mean you can watch and
then<00:02:24.780><c> there's</c><00:02:24.959><c> these</c><00:02:25.319><c> weird</c><00:02:25.440><c> jumps</c><00:02:25.860><c> all</c><00:02:26.040><c> over</c>

00:02:26.150 --> 00:02:26.160 align:start position:0%
then there's these weird jumps all over
 

00:02:26.160 --> 00:02:28.070 align:start position:0%
then there's these weird jumps all over
the<00:02:26.340><c> place</c><00:02:26.459><c> but</c><00:02:27.000><c> so</c><00:02:27.239><c> I've</c><00:02:27.360><c> got</c><00:02:27.480><c> this</c><00:02:27.720><c> I've</c><00:02:27.900><c> got</c>

00:02:28.070 --> 00:02:28.080 align:start position:0%
the place but so I've got this I've got
 

00:02:28.080 --> 00:02:31.130 align:start position:0%
the place but so I've got this I've got
this<00:02:28.319><c> video</c><00:02:28.580><c> uh</c><00:02:29.580><c> project</c><00:02:29.879><c> uh</c><00:02:30.720><c> what</c><00:02:30.900><c> I'm</c><00:02:31.020><c> going</c>

00:02:31.130 --> 00:02:31.140 align:start position:0%
this video uh project uh what I'm going
 

00:02:31.140 --> 00:02:35.089 align:start position:0%
this video uh project uh what I'm going
to<00:02:31.200><c> do</c><00:02:31.379><c> is</c><00:02:31.620><c> go</c><00:02:31.860><c> to</c><00:02:32.160><c> the</c><00:02:32.280><c> menu</c><00:02:32.400><c> and</c><00:02:33.180><c> I</c><00:02:33.660><c> want</c><00:02:33.840><c> to</c>

00:02:35.089 --> 00:02:35.099 align:start position:0%
to do is go to the menu and I want to
 

00:02:35.099 --> 00:02:38.750 align:start position:0%
to do is go to the menu and I want to
let's<00:02:35.700><c> just</c><00:02:35.940><c> say</c><00:02:36.120><c> export</c>

00:02:38.750 --> 00:02:38.760 align:start position:0%
 
 

00:02:38.760 --> 00:02:41.150 align:start position:0%
 
and<00:02:39.360><c> I'm</c><00:02:39.540><c> going</c><00:02:39.660><c> to</c><00:02:39.660><c> do</c><00:02:39.840><c> Audio</c><00:02:40.140><c> Only</c>

00:02:41.150 --> 00:02:41.160 align:start position:0%
and I'm going to do Audio Only
 

00:02:41.160 --> 00:02:43.070 align:start position:0%
and I'm going to do Audio Only
you<00:02:41.459><c> can</c><00:02:41.580><c> do</c><00:02:41.700><c> a</c><00:02:41.819><c> wave</c><00:02:42.060><c> or</c><00:02:42.239><c> MP3</c><00:02:42.660><c> I'm</c><00:02:42.840><c> going</c><00:02:42.900><c> to</c><00:02:42.959><c> do</c>

00:02:43.070 --> 00:02:43.080 align:start position:0%
you can do a wave or MP3 I'm going to do
 

00:02:43.080 --> 00:02:46.970 align:start position:0%
you can do a wave or MP3 I'm going to do
MP3<00:02:43.440><c> to</c><00:02:43.680><c> keep</c><00:02:43.800><c> it</c><00:02:43.980><c> short</c><00:02:44.540><c> and</c><00:02:45.540><c> let's</c><00:02:46.140><c> click</c><00:02:46.620><c> on</c>

00:02:46.970 --> 00:02:46.980 align:start position:0%
MP3 to keep it short and let's click on
 

00:02:46.980 --> 00:02:48.770 align:start position:0%
MP3 to keep it short and let's click on
Save

00:02:48.770 --> 00:02:48.780 align:start position:0%
Save
 

00:02:48.780 --> 00:02:51.110 align:start position:0%
Save
and<00:02:49.620><c> I'm</c><00:02:49.800><c> going</c><00:02:49.920><c> to</c><00:02:49.920><c> call</c><00:02:50.099><c> it</c><00:02:50.280><c> whisper</c><00:02:50.760><c> because</c>

00:02:51.110 --> 00:02:51.120 align:start position:0%
and I'm going to call it whisper because
 

00:02:51.120 --> 00:02:53.930 align:start position:0%
and I'm going to call it whisper because
that's<00:02:51.599><c> the</c><00:02:51.780><c> name</c><00:02:51.900><c> of</c><00:02:52.019><c> the</c><00:02:52.080><c> tool</c><00:02:52.319><c> we're</c><00:02:52.440><c> using</c>

00:02:53.930 --> 00:02:53.940 align:start position:0%
that's the name of the tool we're using
 

00:02:53.940 --> 00:02:56.630 align:start position:0%
that's the name of the tool we're using
okay<00:02:54.239><c> so</c><00:02:54.480><c> it</c><00:02:54.599><c> looks</c><00:02:54.780><c> like</c><00:02:54.900><c> that's</c><00:02:55.019><c> done</c>

00:02:56.630 --> 00:02:56.640 align:start position:0%
okay so it looks like that's done
 

00:02:56.640 --> 00:02:58.850 align:start position:0%
okay so it looks like that's done
I'm<00:02:56.940><c> just</c><00:02:57.180><c> going</c><00:02:57.300><c> to</c><00:02:57.360><c> start</c><00:02:57.480><c> up</c><00:02:57.599><c> item</c><00:02:58.019><c> here</c><00:02:58.680><c> I</c>

00:02:58.850 --> 00:02:58.860 align:start position:0%
I'm just going to start up item here I
 

00:02:58.860 --> 00:03:03.589 align:start position:0%
I'm just going to start up item here I
am<00:02:58.920><c> in</c><00:02:59.160><c> iterm</c><00:02:59.760><c> and</c><00:03:00.620><c> that</c><00:03:01.620><c> file</c><00:03:02.300><c> where</c><00:03:03.300><c> did</c><00:03:03.480><c> I</c>

00:03:03.589 --> 00:03:03.599 align:start position:0%
am in iterm and that file where did I
 

00:03:03.599 --> 00:03:04.970 align:start position:0%
am in iterm and that file where did I
put<00:03:03.720><c> it</c><00:03:03.780><c> probably</c><00:03:04.140><c> under</c><00:03:04.319><c> downloads</c><00:03:04.860><c> because</c>

00:03:04.970 --> 00:03:04.980 align:start position:0%
put it probably under downloads because
 

00:03:04.980 --> 00:03:06.290 align:start position:0%
put it probably under downloads because
that's<00:03:05.160><c> what</c><00:03:05.280><c> I</c><00:03:05.400><c> usually</c><00:03:05.640><c> tend</c><00:03:05.819><c> to</c><00:03:05.940><c> put</c><00:03:06.060><c> things</c>

00:03:06.290 --> 00:03:06.300 align:start position:0%
that's what I usually tend to put things
 

00:03:06.300 --> 00:03:08.869 align:start position:0%
that's what I usually tend to put things
what<00:03:06.840><c> was</c><00:03:07.080><c> this</c><00:03:07.260><c> called</c><00:03:07.500><c> uh</c><00:03:08.340><c> probably</c><00:03:08.580><c> called</c>

00:03:08.869 --> 00:03:08.879 align:start position:0%
what was this called uh probably called
 

00:03:08.879 --> 00:03:10.250 align:start position:0%
what was this called uh probably called
something<00:03:09.060><c> like</c><00:03:09.300><c> whisper</c><00:03:09.660><c> oh</c><00:03:09.840><c> there</c><00:03:10.080><c> it</c><00:03:10.200><c> is</c>

00:03:10.250 --> 00:03:10.260 align:start position:0%
something like whisper oh there it is
 

00:03:10.260 --> 00:03:14.990 align:start position:0%
something like whisper oh there it is
whisper<00:03:10.680><c> MP3</c><00:03:11.220><c> cool</c><00:03:11.819><c> so</c><00:03:12.780><c> what</c><00:03:13.200><c> I</c><00:03:13.319><c> can</c><00:03:13.440><c> do</c><00:03:13.620><c> is</c><00:03:14.580><c> run</c>

00:03:14.990 --> 00:03:15.000 align:start position:0%
whisper MP3 cool so what I can do is run
 

00:03:15.000 --> 00:03:16.670 align:start position:0%
whisper MP3 cool so what I can do is run
whisper

00:03:16.670 --> 00:03:16.680 align:start position:0%
whisper
 

00:03:16.680 --> 00:03:20.089 align:start position:0%
whisper
and<00:03:17.459><c> I</c><00:03:17.640><c> use</c><00:03:17.760><c> whisper.mp3</c>

00:03:20.089 --> 00:03:20.099 align:start position:0%
and I use whisper.mp3
 

00:03:20.099 --> 00:03:21.770 align:start position:0%
and I use whisper.mp3
and

00:03:21.770 --> 00:03:21.780 align:start position:0%
and
 

00:03:21.780 --> 00:03:23.869 align:start position:0%
and
and<00:03:22.019><c> model</c>

00:03:23.869 --> 00:03:23.879 align:start position:0%
and model
 

00:03:23.879 --> 00:03:26.570 align:start position:0%
and model
tiny

00:03:26.570 --> 00:03:26.580 align:start position:0%
tiny
 

00:03:26.580 --> 00:03:28.070 align:start position:0%
tiny
and

00:03:28.070 --> 00:03:28.080 align:start position:0%
and
 

00:03:28.080 --> 00:03:30.410 align:start position:0%
and
really<00:03:28.560><c> that's</c><00:03:28.739><c> all</c><00:03:28.920><c> I</c><00:03:29.099><c> need</c><00:03:29.220><c> to</c><00:03:29.280><c> do</c><00:03:29.459><c> so</c><00:03:30.120><c> press</c>

00:03:30.410 --> 00:03:30.420 align:start position:0%
really that's all I need to do so press
 

00:03:30.420 --> 00:03:32.390 align:start position:0%
really that's all I need to do so press
enter

00:03:32.390 --> 00:03:32.400 align:start position:0%
enter
 

00:03:32.400 --> 00:03:34.309 align:start position:0%
enter
it<00:03:32.879><c> first</c><00:03:33.180><c> thing</c><00:03:33.300><c> it</c><00:03:33.480><c> does</c><00:03:33.599><c> is</c><00:03:33.959><c> figure</c><00:03:34.200><c> out</c>

00:03:34.309 --> 00:03:34.319 align:start position:0%
it first thing it does is figure out
 

00:03:34.319 --> 00:03:36.470 align:start position:0%
it first thing it does is figure out
what<00:03:34.560><c> language</c><00:03:34.739><c> am</c><00:03:35.040><c> I</c><00:03:35.220><c> speaking</c><00:03:35.580><c> and</c><00:03:36.239><c> then</c><00:03:36.360><c> it</c>

00:03:36.470 --> 00:03:36.480 align:start position:0%
what language am I speaking and then it
 

00:03:36.480 --> 00:03:38.270 align:start position:0%
what language am I speaking and then it
starts<00:03:36.780><c> transcribing</c><00:03:37.379><c> it</c>

00:03:38.270 --> 00:03:38.280 align:start position:0%
starts transcribing it
 

00:03:38.280 --> 00:03:39.530 align:start position:0%
starts transcribing it
and<00:03:38.580><c> you</c><00:03:38.760><c> can</c><00:03:38.879><c> see</c>

00:03:39.530 --> 00:03:39.540 align:start position:0%
and you can see
 

00:03:39.540 --> 00:03:41.570 align:start position:0%
and you can see
there's<00:03:40.019><c> this</c><00:03:40.260><c> transcription</c>

00:03:41.570 --> 00:03:41.580 align:start position:0%
there's this transcription
 

00:03:41.580 --> 00:03:43.610 align:start position:0%
there's this transcription
pretty<00:03:42.060><c> amazing</c>

00:03:43.610 --> 00:03:43.620 align:start position:0%
pretty amazing
 

00:03:43.620 --> 00:03:45.949 align:start position:0%
pretty amazing
it's<00:03:43.739><c> going</c><00:03:43.980><c> to</c><00:03:43.980><c> take</c><00:03:44.220><c> a</c><00:03:44.400><c> few</c><00:03:44.580><c> seconds</c><00:03:45.540><c> um</c><00:03:45.540><c> to</c>

00:03:45.949 --> 00:03:45.959 align:start position:0%
it's going to take a few seconds um to
 

00:03:45.959 --> 00:03:48.890 align:start position:0%
it's going to take a few seconds um to
do<00:03:46.140><c> this</c><00:03:46.379><c> for</c><00:03:47.099><c> a</c><00:03:47.280><c> three</c><00:03:47.400><c> minute</c><00:03:47.580><c> video</c><00:03:47.940><c> I</c><00:03:48.720><c> don't</c>

00:03:48.890 --> 00:03:48.900 align:start position:0%
do this for a three minute video I don't
 

00:03:48.900 --> 00:03:51.050 align:start position:0%
do this for a three minute video I don't
know<00:03:48.959><c> actually</c><00:03:49.500><c> it's</c><00:03:49.739><c> two</c><00:03:50.040><c> minute</c><00:03:50.159><c> video</c><00:03:50.519><c> I</c>

00:03:51.050 --> 00:03:51.060 align:start position:0%
know actually it's two minute video I
 

00:03:51.060 --> 00:03:52.970 align:start position:0%
know actually it's two minute video I
don't<00:03:51.180><c> know</c><00:03:51.239><c> what</c><00:03:51.360><c> that</c><00:03:51.540><c> take</c><00:03:51.720><c> about</c><00:03:51.959><c> uh</c><00:03:52.620><c> just</c>

00:03:52.970 --> 00:03:52.980 align:start position:0%
don't know what that take about uh just
 

00:03:52.980 --> 00:03:54.470 align:start position:0%
don't know what that take about uh just
a<00:03:53.099><c> few</c><00:03:53.220><c> seconds</c>

00:03:54.470 --> 00:03:54.480 align:start position:0%
a few seconds
 

00:03:54.480 --> 00:03:56.930 align:start position:0%
a few seconds
depending<00:03:55.260><c> on</c><00:03:55.319><c> the</c><00:03:55.500><c> model</c><00:03:55.620><c> you</c><00:03:55.920><c> use</c><00:03:56.099><c> so</c><00:03:56.459><c> tiny</c>

00:03:56.930 --> 00:03:56.940 align:start position:0%
depending on the model you use so tiny
 

00:03:56.940 --> 00:03:58.850 align:start position:0%
depending on the model you use so tiny
is<00:03:57.120><c> the</c><00:03:57.299><c> smallest</c><00:03:57.599><c> then</c><00:03:57.959><c> there's</c><00:03:58.200><c> small</c><00:03:58.500><c> then</c>

00:03:58.850 --> 00:03:58.860 align:start position:0%
is the smallest then there's small then
 

00:03:58.860 --> 00:04:00.470 align:start position:0%
is the smallest then there's small then
there's<00:03:59.159><c> I</c><00:04:00.000><c> don't</c><00:04:00.120><c> know</c><00:04:00.180><c> there's</c><00:04:00.360><c> something</c>

00:04:00.470 --> 00:04:00.480 align:start position:0%
there's I don't know there's something
 

00:04:00.480 --> 00:04:02.210 align:start position:0%
there's I don't know there's something
bigger<00:04:00.900><c> than</c><00:04:01.019><c> that</c><00:04:01.140><c> depending</c><00:04:01.739><c> on</c><00:04:01.860><c> which</c><00:04:02.040><c> one</c>

00:04:02.210 --> 00:04:02.220 align:start position:0%
bigger than that depending on which one
 

00:04:02.220 --> 00:04:04.070 align:start position:0%
bigger than that depending on which one
you<00:04:02.400><c> use</c><00:04:02.580><c> as</c><00:04:02.819><c> you</c><00:04:03.000><c> get</c><00:04:03.120><c> to</c><00:04:03.299><c> bigger</c><00:04:03.720><c> bigger</c>

00:04:04.070 --> 00:04:04.080 align:start position:0%
you use as you get to bigger bigger
 

00:04:04.080 --> 00:04:06.830 align:start position:0%
you use as you get to bigger bigger
models<00:04:04.379><c> it'll</c><00:04:04.680><c> take</c><00:04:04.860><c> longer</c><00:04:05.280><c> and</c><00:04:05.340><c> longer</c><00:04:05.840><c> for</c>

00:04:06.830 --> 00:04:06.840 align:start position:0%
models it'll take longer and longer for
 

00:04:06.840 --> 00:04:10.250 align:start position:0%
models it'll take longer and longer for
a<00:04:07.379><c> five</c><00:04:07.799><c> minute</c><00:04:07.980><c> video</c><00:04:08.340><c> I</c><00:04:08.700><c> did</c><00:04:08.879><c> earlier</c><00:04:09.299><c> a</c><00:04:09.780><c> tiny</c>

00:04:10.250 --> 00:04:10.260 align:start position:0%
a five minute video I did earlier a tiny
 

00:04:10.260 --> 00:04:13.250 align:start position:0%
a five minute video I did earlier a tiny
took<00:04:10.620><c> in</c><00:04:10.980><c> a</c><00:04:11.099><c> couple</c><00:04:11.280><c> minutes</c><00:04:11.519><c> and</c><00:04:12.420><c> small</c><00:04:12.720><c> took</c>

00:04:13.250 --> 00:04:13.260 align:start position:0%
took in a couple minutes and small took
 

00:04:13.260 --> 00:04:15.890 align:start position:0%
took in a couple minutes and small took
more<00:04:13.379><c> like</c><00:04:13.620><c> 10</c><00:04:13.920><c> minutes</c><00:04:14.099><c> to</c><00:04:14.640><c> transcribe</c><00:04:15.239><c> but</c>

00:04:15.890 --> 00:04:15.900 align:start position:0%
more like 10 minutes to transcribe but
 

00:04:15.900 --> 00:04:18.469 align:start position:0%
more like 10 minutes to transcribe but
now<00:04:16.680><c> I</c><00:04:17.220><c> have</c>

00:04:18.469 --> 00:04:18.479 align:start position:0%
now I have
 

00:04:18.479 --> 00:04:19.370 align:start position:0%
now I have
um

00:04:19.370 --> 00:04:19.380 align:start position:0%
um
 

00:04:19.380 --> 00:04:22.909 align:start position:0%
um
whisper<00:04:20.220><c> there's</c><00:04:21.120><c> a</c><00:04:21.840><c> few</c><00:04:22.320><c> files</c><00:04:22.680><c> that</c><00:04:22.800><c> were</c>

00:04:22.909 --> 00:04:22.919 align:start position:0%
whisper there's a few files that were
 

00:04:22.919 --> 00:04:26.030 align:start position:0%
whisper there's a few files that were
generated<00:04:23.220><c> there's</c><00:04:23.520><c> an</c><00:04:23.699><c> SRT</c><00:04:24.120><c> file</c><00:04:24.479><c> there's</c><00:04:25.380><c> a</c>

00:04:26.030 --> 00:04:26.040 align:start position:0%
generated there's an SRT file there's a
 

00:04:26.040 --> 00:04:30.110 align:start position:0%
generated there's an SRT file there's a
text<00:04:26.220><c> file</c><00:04:26.639><c> and</c><00:04:27.060><c> there's</c><00:04:27.240><c> a</c><00:04:27.540><c> vtt</c><00:04:27.960><c> file</c>

00:04:30.110 --> 00:04:30.120 align:start position:0%
text file and there's a vtt file
 

00:04:30.120 --> 00:04:34.909 align:start position:0%
text file and there's a vtt file
um<00:04:30.180><c> let's</c><00:04:30.540><c> go</c><00:04:30.840><c> take</c><00:04:31.199><c> a</c><00:04:31.320><c> look</c><00:04:31.440><c> at</c><00:04:31.680><c> whisper</c><00:04:32.460><c> SRT</c>

00:04:34.909 --> 00:04:34.919 align:start position:0%
um let's go take a look at whisper SRT
 

00:04:34.919 --> 00:04:38.570 align:start position:0%
um let's go take a look at whisper SRT
and<00:04:35.520><c> here</c><00:04:35.880><c> is</c><00:04:36.360><c> that</c><00:04:36.960><c> transcription</c><00:04:37.580><c> formatted</c>

00:04:38.570 --> 00:04:38.580 align:start position:0%
and here is that transcription formatted
 

00:04:38.580 --> 00:04:41.090 align:start position:0%
and here is that transcription formatted
as<00:04:39.000><c> an</c><00:04:39.240><c> SRT</c><00:04:39.660><c> file</c><00:04:39.960><c> we</c><00:04:40.080><c> can</c><00:04:40.259><c> just</c><00:04:40.380><c> kind</c><00:04:40.560><c> of</c><00:04:40.680><c> tap</c>

00:04:41.090 --> 00:04:41.100 align:start position:0%
as an SRT file we can just kind of tap
 

00:04:41.100 --> 00:04:43.310 align:start position:0%
as an SRT file we can just kind of tap
through<00:04:41.280><c> it</c><00:04:41.460><c> or</c><00:04:41.580><c> space</c><00:04:41.759><c> through</c><00:04:42.060><c> it</c><00:04:42.240><c> well</c><00:04:43.080><c> yeah</c>

00:04:43.310 --> 00:04:43.320 align:start position:0%
through it or space through it well yeah
 

00:04:43.320 --> 00:04:45.050 align:start position:0%
through it or space through it well yeah
that<00:04:43.500><c> was</c><00:04:43.620><c> pretty</c><00:04:43.740><c> easy</c><00:04:43.919><c> all</c><00:04:44.160><c> I</c><00:04:44.340><c> did</c><00:04:44.460><c> was</c><00:04:44.639><c> run</c>

00:04:45.050 --> 00:04:45.060 align:start position:0%
that was pretty easy all I did was run
 

00:04:45.060 --> 00:04:46.909 align:start position:0%
that was pretty easy all I did was run
uh<00:04:45.900><c> whisper</c>

00:04:46.909 --> 00:04:46.919 align:start position:0%
uh whisper
 

00:04:46.919 --> 00:04:49.550 align:start position:0%
uh whisper
and<00:04:47.340><c> then</c><00:04:47.460><c> what's</c><00:04:47.820><c> the</c><00:04:48.060><c> name</c><00:04:48.300><c> of</c><00:04:48.600><c> the</c>

00:04:49.550 --> 00:04:49.560 align:start position:0%
and then what's the name of the
 

00:04:49.560 --> 00:04:51.469 align:start position:0%
and then what's the name of the
MP3<00:04:50.520><c> file</c>

00:04:51.469 --> 00:04:51.479 align:start position:0%
MP3 file
 

00:04:51.479 --> 00:04:53.689 align:start position:0%
MP3 file
and<00:04:51.840><c> then</c><00:04:51.900><c> which</c><00:04:52.199><c> model</c><00:04:52.440><c> I</c><00:04:52.740><c> want</c><00:04:52.860><c> to</c><00:04:52.979><c> use</c>

00:04:53.689 --> 00:04:53.699 align:start position:0%
and then which model I want to use
 

00:04:53.699 --> 00:04:58.010 align:start position:0%
and then which model I want to use
so<00:04:54.180><c> actually</c><00:04:54.360><c> I</c><00:04:54.660><c> can</c><00:04:54.840><c> do</c><00:04:55.160><c> Dash</c><00:04:56.160><c> help</c>

00:04:58.010 --> 00:04:58.020 align:start position:0%
so actually I can do Dash help
 

00:04:58.020 --> 00:05:00.350 align:start position:0%
so actually I can do Dash help
and<00:04:58.380><c> when</c><00:04:58.500><c> I</c><00:04:58.680><c> look</c><00:04:58.800><c> in</c><00:04:58.979><c> here</c>

00:05:00.350 --> 00:05:00.360 align:start position:0%
and when I look in here
 

00:05:00.360 --> 00:05:01.969 align:start position:0%
and when I look in here
um

00:05:01.969 --> 00:05:01.979 align:start position:0%
um
 

00:05:01.979 --> 00:05:06.469 align:start position:0%
um
you<00:05:02.639><c> can</c><00:05:02.880><c> see</c><00:05:03.120><c> that</c><00:05:03.479><c> the</c><00:05:04.020><c> options</c><00:05:04.500><c> are</c><00:05:05.040><c> tiny</c><00:05:05.520><c> oh</c>

00:05:06.469 --> 00:05:06.479 align:start position:0%
you can see that the options are tiny oh
 

00:05:06.479 --> 00:05:10.370 align:start position:0%
you can see that the options are tiny oh
tiny<00:05:07.080><c> just</c><00:05:07.440><c> for</c><00:05:07.680><c> English</c><00:05:07.919><c> or</c><00:05:08.639><c> tiny</c><00:05:09.120><c> face</c><00:05:09.960><c> just</c>

00:05:10.370 --> 00:05:10.380 align:start position:0%
tiny just for English or tiny face just
 

00:05:10.380 --> 00:05:13.850 align:start position:0%
tiny just for English or tiny face just
for<00:05:10.560><c> English</c><00:05:10.800><c> or</c><00:05:11.280><c> Bass</c><00:05:11.699><c> small</c><00:05:12.540><c> for</c><00:05:13.080><c> English</c><00:05:13.320><c> or</c>

00:05:13.850 --> 00:05:13.860 align:start position:0%
for English or Bass small for English or
 

00:05:13.860 --> 00:05:17.870 align:start position:0%
for English or Bass small for English or
small<00:05:14.160><c> medium</c><00:05:15.139><c> uh</c><00:05:16.139><c> or</c><00:05:16.560><c> large</c>

00:05:17.870 --> 00:05:17.880 align:start position:0%
small medium uh or large
 

00:05:17.880 --> 00:05:19.129 align:start position:0%
small medium uh or large
and

00:05:19.129 --> 00:05:19.139 align:start position:0%
and
 

00:05:19.139 --> 00:05:21.530 align:start position:0%
and
I<00:05:19.680><c> thought</c><00:05:19.800><c> tiny</c><00:05:20.280><c> was</c><00:05:20.460><c> only</c><00:05:20.639><c> English</c><00:05:20.940><c> in</c>

00:05:21.530 --> 00:05:21.540 align:start position:0%
I thought tiny was only English in
 

00:05:21.540 --> 00:05:23.810 align:start position:0%
I thought tiny was only English in
general<00:05:21.660><c> but</c><00:05:22.139><c> it</c><00:05:22.259><c> turns</c><00:05:22.620><c> out</c><00:05:22.680><c> tiny</c><00:05:23.580><c> might</c><00:05:23.699><c> be</c>

00:05:23.810 --> 00:05:23.820 align:start position:0%
general but it turns out tiny might be
 

00:05:23.820 --> 00:05:26.450 align:start position:0%
general but it turns out tiny might be
other<00:05:24.060><c> languages</c><00:05:24.479><c> as</c><00:05:24.840><c> well</c>

00:05:26.450 --> 00:05:26.460 align:start position:0%
other languages as well
 

00:05:26.460 --> 00:05:29.990 align:start position:0%
other languages as well
and<00:05:26.759><c> maybe</c><00:05:27.000><c> tiny.en</c><00:05:27.780><c> is</c><00:05:28.139><c> even</c><00:05:28.620><c> faster</c><00:05:29.000><c> because</c>

00:05:29.990 --> 00:05:30.000 align:start position:0%
and maybe tiny.en is even faster because
 

00:05:30.000 --> 00:05:33.170 align:start position:0%
and maybe tiny.en is even faster because
it's<00:05:30.479><c> a</c><00:05:30.720><c> tiny</c><00:05:31.020><c> tiny</c><00:05:31.440><c> tiny</c><00:05:31.860><c> model</c><00:05:32.220><c> then</c><00:05:32.880><c> if</c>

00:05:33.170 --> 00:05:33.180 align:start position:0%
it's a tiny tiny tiny model then if
 

00:05:33.180 --> 00:05:34.490 align:start position:0%
it's a tiny tiny tiny model then if
you're<00:05:33.240><c> using</c><00:05:33.539><c> other</c><00:05:33.720><c> languages</c><00:05:34.020><c> you</c><00:05:34.440><c> can</c>

00:05:34.490 --> 00:05:34.500 align:start position:0%
you're using other languages you can
 

00:05:34.500 --> 00:05:36.110 align:start position:0%
you're using other languages you can
specify<00:05:34.860><c> what</c><00:05:35.220><c> language</c><00:05:35.400><c> it</c><00:05:35.940><c> should</c>

00:05:36.110 --> 00:05:36.120 align:start position:0%
specify what language it should
 

00:05:36.120 --> 00:05:38.330 align:start position:0%
specify what language it should
recognize

00:05:38.330 --> 00:05:38.340 align:start position:0%
recognize
 

00:05:38.340 --> 00:05:40.210 align:start position:0%
recognize
um<00:05:38.400><c> I</c><00:05:38.639><c> have</c><00:05:38.759><c> no</c><00:05:39.000><c> idea</c><00:05:39.180><c> what</c><00:05:39.419><c> temperature</c><00:05:39.600><c> means</c>

00:05:40.210 --> 00:05:40.220 align:start position:0%
um I have no idea what temperature means
 

00:05:40.220 --> 00:05:43.070 align:start position:0%
um I have no idea what temperature means
or<00:05:41.220><c> best</c><00:05:41.820><c> of</c>

00:05:43.070 --> 00:05:43.080 align:start position:0%
or best of
 

00:05:43.080 --> 00:05:45.050 align:start position:0%
or best of
but<00:05:43.560><c> those</c><00:05:43.680><c> are</c><00:05:43.800><c> all</c><00:05:44.039><c> the</c><00:05:44.220><c> options</c><00:05:44.460><c> to</c><00:05:44.940><c> find</c>

00:05:45.050 --> 00:05:45.060 align:start position:0%
but those are all the options to find
 

00:05:45.060 --> 00:05:46.490 align:start position:0%
but those are all the options to find
out<00:05:45.240><c> more</c><00:05:45.419><c> about</c><00:05:45.539><c> whisper</c><00:05:45.840><c> you</c><00:05:46.080><c> can</c><00:05:46.139><c> go</c><00:05:46.259><c> to</c><00:05:46.380><c> the</c>

00:05:46.490 --> 00:05:46.500 align:start position:0%
out more about whisper you can go to the
 

00:05:46.500 --> 00:05:50.570 align:start position:0%
out more about whisper you can go to the
open<00:05:46.680><c> AI</c><00:05:47.340><c> slash</c><00:05:47.940><c> whisper</c><00:05:48.419><c> repo</c><00:05:49.080><c> on</c><00:05:49.500><c> GitHub</c>

00:05:50.570 --> 00:05:50.580 align:start position:0%
open AI slash whisper repo on GitHub
 

00:05:50.580 --> 00:05:54.590 align:start position:0%
open AI slash whisper repo on GitHub
so<00:05:51.060><c> the</c><00:05:51.240><c> URL</c><00:05:51.600><c> for</c><00:05:51.840><c> this</c><00:05:52.080><c> is</c><00:05:52.680><c> github.com</c><00:05:53.600><c> openai</c>

00:05:54.590 --> 00:05:54.600 align:start position:0%
so the URL for this is github.com openai
 

00:05:54.600 --> 00:05:56.629 align:start position:0%
so the URL for this is github.com openai
whisper<00:05:55.500><c> and</c><00:05:55.740><c> there's</c><00:05:55.919><c> a</c><00:05:56.100><c> whole</c><00:05:56.220><c> bunch</c><00:05:56.400><c> of</c>

00:05:56.629 --> 00:05:56.639 align:start position:0%
whisper and there's a whole bunch of
 

00:05:56.639 --> 00:05:59.330 align:start position:0%
whisper and there's a whole bunch of
information<00:05:57.120><c> about</c><00:05:57.539><c> what</c><00:05:58.139><c> it's</c><00:05:58.320><c> doing</c><00:05:58.560><c> out</c><00:05:59.160><c> of</c>

00:05:59.330 --> 00:05:59.340 align:start position:0%
information about what it's doing out of
 

00:05:59.340 --> 00:06:01.790 align:start position:0%
information about what it's doing out of
this<00:05:59.520><c> image</c><00:06:00.120><c> I</c><00:06:00.479><c> understand</c>

00:06:01.790 --> 00:06:01.800 align:start position:0%
this image I understand
 

00:06:01.800 --> 00:06:05.270 align:start position:0%
this image I understand
none<00:06:02.460><c> of</c><00:06:02.580><c> it</c><00:06:02.699><c> install</c><00:06:03.600><c> whisper</c><00:06:04.380><c> first</c><00:06:05.039><c> you</c>

00:06:05.270 --> 00:06:05.280 align:start position:0%
none of it install whisper first you
 

00:06:05.280 --> 00:06:09.770 align:start position:0%
none of it install whisper first you
need<00:06:05.400><c> ffmpeg</c><00:06:06.240><c> so</c><00:06:07.080><c> Brew</c><00:06:07.440><c> install</c><00:06:07.940><c> ffmpeg</c><00:06:08.940><c> no</c>

00:06:09.770 --> 00:06:09.780 align:start position:0%
need ffmpeg so Brew install ffmpeg no
 

00:06:09.780 --> 00:06:12.650 align:start position:0%
need ffmpeg so Brew install ffmpeg no
idea<00:06:10.020><c> what</c><00:06:10.199><c> Homebrew</c><00:06:10.680><c> is</c><00:06:11.060><c> uh</c><00:06:12.060><c> look</c><00:06:12.240><c> at</c><00:06:12.419><c> this</c>

00:06:12.650 --> 00:06:12.660 align:start position:0%
idea what Homebrew is uh look at this
 

00:06:12.660 --> 00:06:15.950 align:start position:0%
idea what Homebrew is uh look at this
URL<00:06:13.020><c> brew.sh</c><00:06:13.800><c> I've</c><00:06:14.400><c> also</c><00:06:14.639><c> got</c><00:06:14.880><c> a</c><00:06:15.720><c> YouTube</c>

00:06:15.950 --> 00:06:15.960 align:start position:0%
URL brew.sh I've also got a YouTube
 

00:06:15.960 --> 00:06:18.830 align:start position:0%
URL brew.sh I've also got a YouTube
video<00:06:16.259><c> from</c><00:06:16.860><c> a</c><00:06:17.400><c> few</c><00:06:17.580><c> years</c><00:06:17.759><c> ago</c><00:06:18.060><c> talking</c><00:06:18.660><c> about</c>

00:06:18.830 --> 00:06:18.840 align:start position:0%
video from a few years ago talking about
 

00:06:18.840 --> 00:06:21.529 align:start position:0%
video from a few years ago talking about
Homebrew<00:06:19.500><c> on</c><00:06:19.800><c> the</c><00:06:19.919><c> Mac</c><00:06:20.039><c> and</c><00:06:20.880><c> then</c><00:06:21.000><c> I</c><00:06:21.360><c> actually</c>

00:06:21.529 --> 00:06:21.539 align:start position:0%
Homebrew on the Mac and then I actually
 

00:06:21.539 --> 00:06:24.170 align:start position:0%
Homebrew on the Mac and then I actually
had<00:06:21.840><c> problems</c><00:06:22.500><c> the</c><00:06:23.340><c> next</c><00:06:23.400><c> command</c><00:06:23.819><c> is</c><00:06:24.000><c> running</c>

00:06:24.170 --> 00:06:24.180 align:start position:0%
had problems the next command is running
 

00:06:24.180 --> 00:06:26.450 align:start position:0%
had problems the next command is running
this<00:06:24.479><c> command</c><00:06:24.840><c> install</c><00:06:25.259><c> the</c><00:06:25.560><c> whisper</c><00:06:25.979><c> get</c>

00:06:26.450 --> 00:06:26.460 align:start position:0%
this command install the whisper get
 

00:06:26.460 --> 00:06:27.770 align:start position:0%
this command install the whisper get
repo

00:06:27.770 --> 00:06:27.780 align:start position:0%
repo
 

00:06:27.780 --> 00:06:29.870 align:start position:0%
repo
but<00:06:28.380><c> I</c><00:06:28.680><c> had</c><00:06:28.800><c> a</c><00:06:28.919><c> problem</c><00:06:29.039><c> running</c><00:06:29.400><c> that</c><00:06:29.639><c> because</c>

00:06:29.870 --> 00:06:29.880 align:start position:0%
but I had a problem running that because
 

00:06:29.880 --> 00:06:31.790 align:start position:0%
but I had a problem running that because
it<00:06:30.240><c> kept</c><00:06:30.479><c> complaining</c><00:06:30.900><c> about</c><00:06:31.020><c> not</c><00:06:31.500><c> being</c><00:06:31.680><c> able</c>

00:06:31.790 --> 00:06:31.800 align:start position:0%
it kept complaining about not being able
 

00:06:31.800 --> 00:06:34.870 align:start position:0%
it kept complaining about not being able
to<00:06:32.039><c> find</c><00:06:32.160><c> Rust</c><00:06:32.699><c> and</c><00:06:33.479><c> so</c><00:06:33.660><c> what</c><00:06:33.960><c> I</c><00:06:34.199><c> actually</c><00:06:34.440><c> did</c>

00:06:34.870 --> 00:06:34.880 align:start position:0%
to find Rust and so what I actually did
 

00:06:34.880 --> 00:06:38.330 align:start position:0%
to find Rust and so what I actually did
was<00:06:35.880><c> through</c><00:06:36.419><c> install</c><00:06:36.840><c> rust</c>

00:06:38.330 --> 00:06:38.340 align:start position:0%
was through install rust
 

00:06:38.340 --> 00:06:41.469 align:start position:0%
was through install rust
install<00:06:38.759><c> that</c><00:06:39.180><c> and</c><00:06:39.780><c> then</c><00:06:39.900><c> the</c><00:06:40.560><c> PIP</c><00:06:40.800><c> install</c>

00:06:41.469 --> 00:06:41.479 align:start position:0%
install that and then the PIP install
 

00:06:41.479 --> 00:06:46.129 align:start position:0%
install that and then the PIP install
the<00:06:42.479><c> whisper</c><00:06:43.100><c> repo</c><00:06:44.100><c> worked</c><00:06:44.819><c> so</c><00:06:45.419><c> that's</c><00:06:45.960><c> uh</c>

00:06:46.129 --> 00:06:46.139 align:start position:0%
the whisper repo worked so that's uh
 

00:06:46.139 --> 00:06:48.409 align:start position:0%
the whisper repo worked so that's uh
that's<00:06:46.440><c> the</c><00:06:46.560><c> one</c><00:06:46.680><c> gotcha</c><00:06:47.160><c> in</c><00:06:48.000><c> the</c>

00:06:48.409 --> 00:06:48.419 align:start position:0%
that's the one gotcha in the
 

00:06:48.419 --> 00:06:51.050 align:start position:0%
that's the one gotcha in the
documentation

00:06:51.050 --> 00:06:51.060 align:start position:0%
documentation
 

00:06:51.060 --> 00:06:52.850 align:start position:0%
documentation
so<00:06:51.419><c> you</c><00:06:51.600><c> install</c><00:06:51.720><c> that</c><00:06:52.139><c> and</c><00:06:52.440><c> then</c><00:06:52.560><c> you've</c><00:06:52.800><c> got</c>

00:06:52.850 --> 00:06:52.860 align:start position:0%
so you install that and then you've got
 

00:06:52.860 --> 00:06:54.170 align:start position:0%
so you install that and then you've got
the<00:06:53.039><c> whisper</c><00:06:53.340><c> command</c><00:06:53.699><c> you</c><00:06:53.819><c> could</c><00:06:53.940><c> just</c><00:06:54.060><c> run</c>

00:06:54.170 --> 00:06:54.180 align:start position:0%
the whisper command you could just run
 

00:06:54.180 --> 00:06:57.590 align:start position:0%
the whisper command you could just run
whisper<00:06:54.660><c> in</c><00:06:55.259><c> the</c><00:06:55.440><c> name</c><00:06:55.560><c> of</c><00:06:55.740><c> your</c><00:06:56.180><c> audio</c><00:06:57.180><c> file</c>

00:06:57.590 --> 00:06:57.600 align:start position:0%
whisper in the name of your audio file
 

00:06:57.600 --> 00:07:00.469 align:start position:0%
whisper in the name of your audio file
and<00:06:57.960><c> the</c><00:06:58.139><c> model</c><00:06:58.319><c> and</c><00:06:59.039><c> boom</c><00:06:59.520><c> you</c><00:06:59.759><c> got</c><00:06:59.940><c> a</c><00:07:00.060><c> SRT</c>

00:07:00.469 --> 00:07:00.479 align:start position:0%
and the model and boom you got a SRT
 

00:07:00.479 --> 00:07:02.330 align:start position:0%
and the model and boom you got a SRT
file<00:07:00.840><c> and</c><00:07:00.960><c> then</c><00:07:01.080><c> here</c><00:07:01.380><c> are</c><00:07:01.620><c> the</c><00:07:02.100><c> different</c>

00:07:02.330 --> 00:07:02.340 align:start position:0%
file and then here are the different
 

00:07:02.340 --> 00:07:06.290 align:start position:0%
file and then here are the different
models<00:07:03.180><c> that</c><00:07:03.960><c> are</c><00:07:04.319><c> available</c><00:07:04.979><c> and</c><00:07:05.940><c> roughly</c>

00:07:06.290 --> 00:07:06.300 align:start position:0%
models that are available and roughly
 

00:07:06.300 --> 00:07:09.469 align:start position:0%
models that are available and roughly
how<00:07:06.660><c> fast</c><00:07:06.900><c> they</c><00:07:07.139><c> are</c><00:07:07.319><c> so</c><00:07:07.620><c> tiny</c><00:07:08.520><c> is</c><00:07:08.759><c> 32</c><00:07:09.240><c> times</c>

00:07:09.469 --> 00:07:09.479 align:start position:0%
how fast they are so tiny is 32 times
 

00:07:09.479 --> 00:07:11.930 align:start position:0%
how fast they are so tiny is 32 times
faster<00:07:09.900><c> than</c><00:07:10.199><c> large</c><00:07:10.520><c> here's</c><00:07:11.520><c> the</c><00:07:11.639><c> important</c>

00:07:11.930 --> 00:07:11.940 align:start position:0%
faster than large here's the important
 

00:07:11.940 --> 00:07:13.909 align:start position:0%
faster than large here's the important
part<00:07:12.120><c> whisper</c>

00:07:13.909 --> 00:07:13.919 align:start position:0%
part whisper
 

00:07:13.919 --> 00:07:16.969 align:start position:0%
part whisper
one<00:07:14.819><c> or</c><00:07:15.000><c> more</c><00:07:15.300><c> audio</c><00:07:15.600><c> files</c>

00:07:16.969 --> 00:07:16.979 align:start position:0%
one or more audio files
 

00:07:16.979 --> 00:07:19.490 align:start position:0%
one or more audio files
and<00:07:17.460><c> then</c><00:07:17.580><c> specify</c><00:07:18.060><c> a</c><00:07:18.300><c> model</c><00:07:18.479><c> with</c><00:07:18.780><c> Dash</c><00:07:19.020><c> model</c>

00:07:19.490 --> 00:07:19.500 align:start position:0%
and then specify a model with Dash model
 

00:07:19.500 --> 00:07:21.950 align:start position:0%
and then specify a model with Dash model
and<00:07:19.979><c> which</c><00:07:20.160><c> model</c><00:07:20.460><c> you</c><00:07:20.880><c> want</c><00:07:21.060><c> to</c><00:07:21.120><c> use</c>

00:07:21.950 --> 00:07:21.960 align:start position:0%
and which model you want to use
 

00:07:21.960 --> 00:07:23.210 align:start position:0%
and which model you want to use
it's<00:07:22.319><c> interesting</c><00:07:22.680><c> that</c><00:07:22.800><c> you</c><00:07:22.919><c> can</c><00:07:23.039><c> also</c>

00:07:23.210 --> 00:07:23.220 align:start position:0%
it's interesting that you can also
 

00:07:23.220 --> 00:07:25.610 align:start position:0%
it's interesting that you can also
specify<00:07:23.639><c> a</c><00:07:23.880><c> language</c><00:07:24.060><c> and</c>

00:07:25.610 --> 00:07:25.620 align:start position:0%
specify a language and
 

00:07:25.620 --> 00:07:27.409 align:start position:0%
specify a language and
you<00:07:25.919><c> can</c><00:07:26.039><c> specify</c><00:07:26.460><c> that</c><00:07:26.819><c> you</c><00:07:26.940><c> want</c><00:07:27.060><c> it</c><00:07:27.240><c> to</c>

00:07:27.409 --> 00:07:27.419 align:start position:0%
you can specify that you want it to
 

00:07:27.419 --> 00:07:29.749 align:start position:0%
you can specify that you want it to
translate<00:07:27.900><c> that</c>

00:07:29.749 --> 00:07:29.759 align:start position:0%
translate that
 

00:07:29.759 --> 00:07:32.390 align:start position:0%
translate that
other<00:07:30.120><c> language</c><00:07:30.479><c> such</c><00:07:31.080><c> as</c><00:07:31.259><c> Japanese</c><00:07:31.560><c> into</c>

00:07:32.390 --> 00:07:32.400 align:start position:0%
other language such as Japanese into
 

00:07:32.400 --> 00:07:36.950 align:start position:0%
other language such as Japanese into
English<00:07:32.759><c> which</c><00:07:33.599><c> is</c><00:07:33.900><c> just</c><00:07:34.259><c> like</c><00:07:34.440><c> amazing</c>

00:07:36.950 --> 00:07:36.960 align:start position:0%
English which is just like amazing
 

00:07:36.960 --> 00:07:39.409 align:start position:0%
English which is just like amazing
and<00:07:37.560><c> uh</c><00:07:37.800><c> and</c><00:07:38.099><c> really</c><00:07:38.220><c> that's</c><00:07:38.520><c> all</c><00:07:38.819><c> there</c><00:07:39.000><c> is</c><00:07:39.180><c> to</c>

00:07:39.409 --> 00:07:39.419 align:start position:0%
and uh and really that's all there is to
 

00:07:39.419 --> 00:07:40.070 align:start position:0%
and uh and really that's all there is to
it

00:07:40.070 --> 00:07:40.080 align:start position:0%
it
 

00:07:40.080 --> 00:07:43.430 align:start position:0%
it
so<00:07:40.680><c> now</c><00:07:40.979><c> with</c><00:07:41.400><c> that</c><00:07:41.639><c> you</c><00:07:42.120><c> know</c><00:07:42.180><c> I</c><00:07:42.599><c> can</c><00:07:42.900><c> come</c>

00:07:43.430 --> 00:07:43.440 align:start position:0%
so now with that you know I can come
 

00:07:43.440 --> 00:07:45.529 align:start position:0%
so now with that you know I can come
back<00:07:43.620><c> into</c>

00:07:45.529 --> 00:07:45.539 align:start position:0%
back into
 

00:07:45.539 --> 00:07:47.770 align:start position:0%
back into
Final<00:07:46.080><c> Cut</c>

00:07:47.770 --> 00:07:47.780 align:start position:0%
Final Cut
 

00:07:47.780 --> 00:07:51.529 align:start position:0%
Final Cut
and<00:07:48.780><c> let's</c><00:07:49.199><c> say</c><00:07:49.380><c> I</c><00:07:49.740><c> want</c><00:07:49.919><c> to</c><00:07:50.039><c> come</c><00:07:50.699><c> up</c><00:07:50.880><c> to</c><00:07:51.060><c> the</c>

00:07:51.529 --> 00:07:51.539 align:start position:0%
and let's say I want to come up to the
 

00:07:51.539 --> 00:07:52.909 align:start position:0%
and let's say I want to come up to the
file

00:07:52.909 --> 00:07:52.919 align:start position:0%
file
 

00:07:52.919 --> 00:07:57.469 align:start position:0%
file
import<00:07:53.460><c> captions</c>

00:07:57.469 --> 00:07:57.479 align:start position:0%
 
 

00:07:57.479 --> 00:08:02.089 align:start position:0%
 
and<00:07:58.139><c> there's</c><00:07:58.380><c> the</c><00:07:59.039><c> whisper</c><00:08:00.000><c> MP3</c><00:08:00.660><c> SRT</c><00:08:01.319><c> file</c><00:08:01.680><c> can</c>

00:08:02.089 --> 00:08:02.099 align:start position:0%
and there's the whisper MP3 SRT file can
 

00:08:02.099 --> 00:08:05.390 align:start position:0%
and there's the whisper MP3 SRT file can
import<00:08:02.400><c> that</c><00:08:02.699><c> and</c><00:08:03.539><c> it</c><00:08:03.780><c> has</c><00:08:04.020><c> just</c><00:08:04.319><c> added</c><00:08:04.860><c> my</c>

00:08:05.390 --> 00:08:05.400 align:start position:0%
import that and it has just added my
 

00:08:05.400 --> 00:08:09.290 align:start position:0%
import that and it has just added my
captions<00:08:05.819><c> to</c><00:08:06.599><c> this</c><00:08:06.780><c> file</c>

00:08:09.290 --> 00:08:09.300 align:start position:0%
 
 

00:08:09.300 --> 00:08:11.270 align:start position:0%
 
hey<00:08:09.660><c> there</c><00:08:09.840><c> it's</c><00:08:10.199><c> Matt</c><00:08:10.380><c> Williams</c><00:08:10.680><c> your</c>

00:08:11.270 --> 00:08:11.280 align:start position:0%
hey there it's Matt Williams your
 

00:08:11.280 --> 00:08:14.089 align:start position:0%
hey there it's Matt Williams your
favorite<00:08:11.720><c> evangelist</c><00:08:12.720><c> well</c><00:08:13.500><c> I</c><00:08:13.740><c> just</c><00:08:13.919><c> found</c>

00:08:14.089 --> 00:08:14.099 align:start position:0%
favorite evangelist well I just found
 

00:08:14.099 --> 00:08:17.029 align:start position:0%
favorite evangelist well I just found
out<00:08:14.340><c> about</c><00:08:14.520><c> something</c><00:08:14.880><c> so</c><00:08:15.599><c> cool</c><00:08:15.900><c> I</c><00:08:16.740><c> just</c><00:08:16.860><c> have</c>

00:08:17.029 --> 00:08:17.039 align:start position:0%
out about something so cool I just have
 

00:08:17.039 --> 00:08:19.249 align:start position:0%
out about something so cool I just have
to<00:08:17.160><c> share</c><00:08:17.280><c> it</c><00:08:17.460><c> with</c><00:08:17.580><c> you</c><00:08:17.759><c> right</c><00:08:18.180><c> away</c><00:08:18.360><c> yep</c>

00:08:19.249 --> 00:08:19.259 align:start position:0%
to share it with you right away yep
 

00:08:19.259 --> 00:08:22.730 align:start position:0%
to share it with you right away yep
isn't<00:08:20.039><c> that</c><00:08:20.099><c> amazing</c><00:08:20.639><c> okay</c><00:08:21.300><c> so</c><00:08:21.900><c> that's</c><00:08:22.199><c> it</c><00:08:22.379><c> you</c>

00:08:22.730 --> 00:08:22.740 align:start position:0%
isn't that amazing okay so that's it you
 

00:08:22.740 --> 00:08:25.730 align:start position:0%
isn't that amazing okay so that's it you
saw<00:08:22.979><c> how</c><00:08:23.280><c> to</c><00:08:23.520><c> You're</c><00:08:24.300><c> Gonna</c><00:08:24.539><c> export</c><00:08:25.020><c> some</c><00:08:25.560><c> sort</c>

00:08:25.730 --> 00:08:25.740 align:start position:0%
saw how to You're Gonna export some sort
 

00:08:25.740 --> 00:08:27.890 align:start position:0%
saw how to You're Gonna export some sort
of<00:08:25.860><c> audio</c><00:08:26.039><c> file</c><00:08:26.460><c> from</c><00:08:26.879><c> your</c><00:08:27.180><c> video</c><00:08:27.360><c> editing</c>

00:08:27.890 --> 00:08:27.900 align:start position:0%
of audio file from your video editing
 

00:08:27.900 --> 00:08:30.890 align:start position:0%
of audio file from your video editing
tool<00:08:28.259><c> and</c><00:08:28.620><c> then</c><00:08:28.740><c> run</c><00:08:28.979><c> whisper</c><00:08:29.520><c> on</c><00:08:29.879><c> it</c><00:08:30.060><c> and</c>

00:08:30.890 --> 00:08:30.900 align:start position:0%
tool and then run whisper on it and
 

00:08:30.900 --> 00:08:34.010 align:start position:0%
tool and then run whisper on it and
within<00:08:31.440><c> seconds</c><00:08:31.740><c> if</c><00:08:32.219><c> you're</c><00:08:32.339><c> using</c><00:08:32.640><c> tiny</c><00:08:33.120><c> you</c>

00:08:34.010 --> 00:08:34.020 align:start position:0%
within seconds if you're using tiny you
 

00:08:34.020 --> 00:08:37.430 align:start position:0%
within seconds if you're using tiny you
can<00:08:34.140><c> have</c><00:08:34.500><c> this</c><00:08:35.180><c> SRT</c><00:08:36.180><c> file</c><00:08:36.599><c> which</c><00:08:36.959><c> you</c><00:08:37.080><c> can</c><00:08:37.200><c> you</c>

00:08:37.430 --> 00:08:37.440 align:start position:0%
can have this SRT file which you can you
 

00:08:37.440 --> 00:08:40.250 align:start position:0%
can have this SRT file which you can you
then<00:08:37.680><c> use</c><00:08:37.919><c> in</c><00:08:38.760><c> whoa</c><00:08:39.300><c> however</c><00:08:39.599><c> you</c><00:08:39.959><c> want</c><00:08:40.080><c> to</c><00:08:40.200><c> use</c>

00:08:40.250 --> 00:08:40.260 align:start position:0%
then use in whoa however you want to use
 

00:08:40.260 --> 00:08:43.490 align:start position:0%
then use in whoa however you want to use
an<00:08:40.500><c> SRT</c><00:08:40.919><c> file</c><00:08:41.219><c> which</c><00:08:41.580><c> is</c><00:08:41.820><c> spectacular</c>

00:08:43.490 --> 00:08:43.500 align:start position:0%
an SRT file which is spectacular
 

00:08:43.500 --> 00:08:45.650 align:start position:0%
an SRT file which is spectacular
I<00:08:43.979><c> think</c><00:08:44.099><c> I've</c><00:08:44.339><c> seen</c><00:08:44.700><c> these</c><00:08:45.240><c> types</c><00:08:45.420><c> of</c><00:08:45.480><c> things</c>

00:08:45.650 --> 00:08:45.660 align:start position:0%
I think I've seen these types of things
 

00:08:45.660 --> 00:08:48.949 align:start position:0%
I think I've seen these types of things
cost<00:08:46.080><c> you</c><00:08:46.560><c> know</c><00:08:46.700><c> they're</c><00:08:47.700><c> they're</c><00:08:47.880><c> often</c><00:08:48.420><c> X</c>

00:08:48.949 --> 00:08:48.959 align:start position:0%
cost you know they're they're often X
 

00:08:48.959 --> 00:08:52.130 align:start position:0%
cost you know they're they're often X
number<00:08:49.260><c> of</c><00:08:49.500><c> uh</c><00:08:50.040><c> pennies</c><00:08:50.459><c> per</c><00:08:50.880><c> minute</c><00:08:51.360><c> and</c><00:08:51.959><c> that</c>

00:08:52.130 --> 00:08:52.140 align:start position:0%
number of uh pennies per minute and that
 

00:08:52.140 --> 00:08:54.590 align:start position:0%
number of uh pennies per minute and that
quickly<00:08:52.500><c> builds</c><00:08:53.279><c> up</c><00:08:53.399><c> and</c><00:08:53.760><c> maybe</c><00:08:53.940><c> you</c><00:08:54.180><c> got</c><00:08:54.360><c> a</c>

00:08:54.590 --> 00:08:54.600 align:start position:0%
quickly builds up and maybe you got a
 

00:08:54.600 --> 00:08:57.590 align:start position:0%
quickly builds up and maybe you got a
limit<00:08:54.839><c> of</c><00:08:55.140><c> 10</c><00:08:55.440><c> hours</c><00:08:55.620><c> per</c><00:08:56.040><c> month</c><00:08:56.279><c> uh</c><00:08:57.000><c> for</c><00:08:57.240><c> you</c>

00:08:57.590 --> 00:08:57.600 align:start position:0%
limit of 10 hours per month uh for you
 

00:08:57.600 --> 00:09:00.170 align:start position:0%
limit of 10 hours per month uh for you
know<00:08:57.660><c> 30</c><00:08:57.839><c> bucks</c><00:08:58.080><c> a</c><00:08:58.320><c> month</c><00:08:58.440><c> or</c><00:08:58.620><c> 48</c><00:08:59.040><c> a</c><00:08:59.399><c> month</c>

00:09:00.170 --> 00:09:00.180 align:start position:0%
know 30 bucks a month or 48 a month
 

00:09:00.180 --> 00:09:01.670 align:start position:0%
know 30 bucks a month or 48 a month
you<00:09:00.540><c> know</c><00:09:00.600><c> if</c><00:09:00.720><c> this</c><00:09:00.839><c> is</c><00:09:00.959><c> a</c><00:09:01.080><c> little</c><00:09:01.140><c> bit</c><00:09:01.320><c> fiddly</c>

00:09:01.670 --> 00:09:01.680 align:start position:0%
you know if this is a little bit fiddly
 

00:09:01.680 --> 00:09:05.090 align:start position:0%
you know if this is a little bit fiddly
maybe<00:09:02.279><c> that</c><00:09:02.820><c> cost</c><00:09:03.180><c> is</c><00:09:03.540><c> worth</c><00:09:03.720><c> it</c><00:09:03.959><c> but</c><00:09:04.320><c> it's</c><00:09:04.920><c> not</c>

00:09:05.090 --> 00:09:05.100 align:start position:0%
maybe that cost is worth it but it's not
 

00:09:05.100 --> 00:09:07.910 align:start position:0%
maybe that cost is worth it but it's not
really<00:09:05.339><c> that</c><00:09:05.580><c> hard</c><00:09:05.880><c> to</c><00:09:06.720><c> install</c><00:09:07.080><c> you</c><00:09:07.680><c> know</c><00:09:07.800><c> to</c>

00:09:07.910 --> 00:09:07.920 align:start position:0%
really that hard to install you know to
 

00:09:07.920 --> 00:09:09.970 align:start position:0%
really that hard to install you know to
use<00:09:08.459><c> Brew</c><00:09:08.760><c> to</c><00:09:09.120><c> install</c>

00:09:09.970 --> 00:09:09.980 align:start position:0%
use Brew to install
 

00:09:09.980 --> 00:09:12.769 align:start position:0%
use Brew to install
ffmpeg<00:09:10.980><c> if</c><00:09:11.339><c> you</c><00:09:11.399><c> don't</c><00:09:11.519><c> already</c><00:09:11.640><c> have</c><00:09:12.000><c> it</c><00:09:12.180><c> and</c>

00:09:12.769 --> 00:09:12.779 align:start position:0%
ffmpeg if you don't already have it and
 

00:09:12.779 --> 00:09:16.009 align:start position:0%
ffmpeg if you don't already have it and
then<00:09:12.899><c> install</c><00:09:13.080><c> rust</c><00:09:13.620><c> and</c><00:09:14.040><c> then</c><00:09:14.160><c> install</c>

00:09:16.009 --> 00:09:16.019 align:start position:0%
then install rust and then install
 

00:09:16.019 --> 00:09:19.130 align:start position:0%
then install rust and then install
um<00:09:16.140><c> that</c><00:09:16.860><c> whisper</c><00:09:17.519><c> repo</c><00:09:18.240><c> installing</c><00:09:19.019><c> the</c>

00:09:19.130 --> 00:09:19.140 align:start position:0%
um that whisper repo installing the
 

00:09:19.140 --> 00:09:22.370 align:start position:0%
um that whisper repo installing the
whisper<00:09:19.440><c> repo</c><00:09:19.860><c> on</c><00:09:19.980><c> my</c><00:09:20.220><c> M1</c><00:09:20.700><c> Pro</c><00:09:21.300><c> Max</c><00:09:21.720><c> machine</c>

00:09:22.370 --> 00:09:22.380 align:start position:0%
whisper repo on my M1 Pro Max machine
 

00:09:22.380 --> 00:09:25.009 align:start position:0%
whisper repo on my M1 Pro Max machine
that<00:09:23.279><c> took</c><00:09:23.640><c> several</c><00:09:24.240><c> minutes</c><00:09:24.360><c> five</c><00:09:24.660><c> ten</c>

00:09:25.009 --> 00:09:25.019 align:start position:0%
that took several minutes five ten
 

00:09:25.019 --> 00:09:26.990 align:start position:0%
that took several minutes five ten
minutes<00:09:25.140><c> to</c><00:09:25.500><c> install</c><00:09:25.920><c> it</c><00:09:26.160><c> to</c><00:09:26.580><c> do</c><00:09:26.700><c> the</c><00:09:26.880><c> full</c>

00:09:26.990 --> 00:09:27.000 align:start position:0%
minutes to install it to do the full
 

00:09:27.000 --> 00:09:30.410 align:start position:0%
minutes to install it to do the full
build<00:09:27.300><c> so</c><00:09:28.140><c> it</c><00:09:28.260><c> can</c><00:09:28.440><c> take</c><00:09:28.680><c> a</c><00:09:28.860><c> while</c><00:09:28.980><c> but</c><00:09:29.880><c> uh</c><00:09:30.180><c> but</c>

00:09:30.410 --> 00:09:30.420 align:start position:0%
build so it can take a while but uh but
 

00:09:30.420 --> 00:09:32.889 align:start position:0%
build so it can take a while but uh but
that's<00:09:30.540><c> it</c><00:09:30.779><c> it's</c><00:09:31.260><c> it's</c><00:09:32.040><c> um</c>

00:09:32.889 --> 00:09:32.899 align:start position:0%
that's it it's it's um
 

00:09:32.899 --> 00:09:35.690 align:start position:0%
that's it it's it's um
mind-blowingly<00:09:33.899><c> cool</c><00:09:34.080><c> I'm</c><00:09:35.040><c> uh</c><00:09:35.279><c> I'm</c><00:09:35.580><c> very</c>

00:09:35.690 --> 00:09:35.700 align:start position:0%
mind-blowingly cool I'm uh I'm very
 

00:09:35.700 --> 00:09:37.790 align:start position:0%
mind-blowingly cool I'm uh I'm very
excited<00:09:36.000><c> about</c><00:09:36.060><c> it</c><00:09:36.300><c> this</c><00:09:36.660><c> is</c><00:09:37.019><c> me</c><00:09:37.260><c> super</c>

00:09:37.790 --> 00:09:37.800 align:start position:0%
excited about it this is me super
 

00:09:37.800 --> 00:09:41.570 align:start position:0%
excited about it this is me super
excited<00:09:38.760><c> maybe</c><00:09:39.180><c> I</c><00:09:39.360><c> should</c><00:09:39.540><c> go</c><00:09:40.040><c> I'm</c><00:09:41.040><c> so</c><00:09:41.279><c> excited</c>

00:09:41.570 --> 00:09:41.580 align:start position:0%
excited maybe I should go I'm so excited
 

00:09:41.580 --> 00:09:43.370 align:start position:0%
excited maybe I should go I'm so excited
about<00:09:41.760><c> this</c><00:09:42.000><c> this</c><00:09:42.420><c> thing</c>

00:09:43.370 --> 00:09:43.380 align:start position:0%
about this this thing
 

00:09:43.380 --> 00:09:45.350 align:start position:0%
about this this thing
um<00:09:43.500><c> and</c><00:09:43.800><c> I</c><00:09:44.040><c> I</c><00:09:44.160><c> just</c><00:09:44.519><c> wanted</c><00:09:44.700><c> to</c><00:09:44.820><c> share</c><00:09:45.000><c> it</c><00:09:45.120><c> super</c>

00:09:45.350 --> 00:09:45.360 align:start position:0%
um and I I just wanted to share it super
 

00:09:45.360 --> 00:09:47.870 align:start position:0%
um and I I just wanted to share it super
quick<00:09:45.540><c> okay</c><00:09:46.140><c> thanks</c><00:09:46.620><c> so</c><00:09:46.800><c> much</c><00:09:46.920><c> uh</c><00:09:47.640><c> this</c><00:09:47.760><c> is</c>

00:09:47.870 --> 00:09:47.880 align:start position:0%
quick okay thanks so much uh this is
 

00:09:47.880 --> 00:09:50.750 align:start position:0%
quick okay thanks so much uh this is
Matt<00:09:48.060><c> Williams</c><00:09:48.240><c> uh</c><00:09:48.899><c> evangelist</c><00:09:49.440><c> for</c><00:09:49.860><c> infra</c><00:09:50.339><c> HQ</c>

00:09:50.750 --> 00:09:50.760 align:start position:0%
Matt Williams uh evangelist for infra HQ
 

00:09:50.760 --> 00:09:52.670 align:start position:0%
Matt Williams uh evangelist for infra HQ
uh<00:09:51.480><c> we</c><00:09:51.720><c> do</c>

00:09:52.670 --> 00:09:52.680 align:start position:0%
uh we do
 

00:09:52.680 --> 00:09:54.829 align:start position:0%
uh we do
um<00:09:52.740><c> a</c><00:09:53.040><c> single</c><00:09:53.399><c> sign-on</c><00:09:53.820><c> and</c><00:09:54.540><c> access</c>

00:09:54.829 --> 00:09:54.839 align:start position:0%
um a single sign-on and access
 

00:09:54.839 --> 00:09:56.930 align:start position:0%
um a single sign-on and access
management<00:09:55.260><c> for</c><00:09:55.800><c> your</c><00:09:55.980><c> kubernetes</c><00:09:56.399><c> clusters</c>

00:09:56.930 --> 00:09:56.940 align:start position:0%
management for your kubernetes clusters
 

00:09:56.940 --> 00:10:00.350 align:start position:0%
management for your kubernetes clusters
uh<00:09:57.660><c> check</c><00:09:57.839><c> us</c><00:09:57.959><c> out</c><00:09:58.220><c> hq.com</c><00:09:59.220><c> but</c><00:09:59.820><c> this</c><00:10:00.060><c> is</c><00:10:00.180><c> a</c>

00:10:00.350 --> 00:10:00.360 align:start position:0%
uh check us out hq.com but this is a
 

00:10:00.360 --> 00:10:02.570 align:start position:0%
uh check us out hq.com but this is a
personal<00:10:00.480><c> video</c><00:10:00.839><c> and</c><00:10:01.500><c> uh</c><00:10:01.740><c> thanks</c><00:10:01.920><c> so</c><00:10:02.100><c> much</c><00:10:02.220><c> and</c>

00:10:02.570 --> 00:10:02.580 align:start position:0%
personal video and uh thanks so much and
 

00:10:02.580 --> 00:10:04.250 align:start position:0%
personal video and uh thanks so much and
I'll<00:10:02.700><c> see</c><00:10:02.940><c> you</c><00:10:03.060><c> next</c><00:10:03.240><c> time</c><00:10:03.480><c> well</c><00:10:03.899><c> who</c><00:10:04.140><c> knows</c>

00:10:04.250 --> 00:10:04.260 align:start position:0%
I'll see you next time well who knows
 

00:10:04.260 --> 00:10:07.580 align:start position:0%
I'll see you next time well who knows
when<00:10:04.440><c> that</c><00:10:04.620><c> is</c><00:10:04.800><c> all</c><00:10:05.100><c> right</c><00:10:05.279><c> bye</c>

