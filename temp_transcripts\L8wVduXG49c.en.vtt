WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:03.510 align:start position:0%
 
So<00:00:00.400><c> how</c><00:00:00.640><c> do</c><00:00:00.719><c> you</c><00:00:00.960><c> deal</c><00:00:01.280><c> with</c><00:00:02.399><c> things</c><00:00:02.800><c> such</c><00:00:03.040><c> as</c>

00:00:03.510 --> 00:00:03.520 align:start position:0%
So how do you deal with things such as
 

00:00:03.520 --> 00:00:08.150 align:start position:0%
So how do you deal with things such as
okay<00:00:03.840><c> I</c><00:00:04.080><c> need</c><00:00:05.440><c> a</c><00:00:05.839><c> lot</c><00:00:06.000><c> of</c><00:00:06.160><c> data</c><00:00:06.560><c> points</c><00:00:07.359><c> on</c><00:00:07.919><c> I</c>

00:00:08.150 --> 00:00:08.160 align:start position:0%
okay I need a lot of data points on I
 

00:00:08.160 --> 00:00:10.629 align:start position:0%
okay I need a lot of data points on I
need<00:00:08.240><c> to</c><00:00:08.400><c> know</c><00:00:08.559><c> how</c><00:00:08.800><c> many</c><00:00:09.040><c> customers</c><00:00:09.519><c> did</c><00:00:09.760><c> XYZ</c>

00:00:10.629 --> 00:00:10.639 align:start position:0%
need to know how many customers did XYZ
 

00:00:10.639 --> 00:00:14.230 align:start position:0%
need to know how many customers did XYZ
but<00:00:11.040><c> in</c><00:00:11.360><c> the</c><00:00:12.080><c> source</c><00:00:12.480><c> data</c>

00:00:14.230 --> 00:00:14.240 align:start position:0%
but in the source data
 

00:00:14.240 --> 00:00:16.870 align:start position:0%
but in the source data
there<00:00:14.639><c> you</c><00:00:14.880><c> need</c><00:00:15.040><c> to</c><00:00:15.440><c> apply</c><00:00:16.000><c> a</c><00:00:16.320><c> few</c><00:00:16.480><c> different</c>

00:00:16.870 --> 00:00:16.880 align:start position:0%
there you need to apply a few different
 

00:00:16.880 --> 00:00:19.109 align:start position:0%
there you need to apply a few different
kinds<00:00:17.199><c> of</c><00:00:17.440><c> transformations</c>

00:00:19.109 --> 00:00:19.119 align:start position:0%
kinds of transformations
 

00:00:19.119 --> 00:00:21.910 align:start position:0%
kinds of transformations
on<00:00:19.359><c> top</c><00:00:19.520><c> of</c><00:00:19.600><c> that</c><00:00:19.840><c> to</c><00:00:20.080><c> get</c><00:00:20.240><c> that</c><00:00:20.480><c> answer</c><00:00:21.199><c> I</c><00:00:21.359><c> mean</c>

00:00:21.910 --> 00:00:21.920 align:start position:0%
on top of that to get that answer I mean
 

00:00:21.920 --> 00:00:24.470 align:start position:0%
on top of that to get that answer I mean
that's<00:00:22.240><c> a</c><00:00:22.640><c> classic</c><00:00:23.199><c> like</c><00:00:23.600><c> data</c><00:00:23.920><c> warehousing</c>

00:00:24.470 --> 00:00:24.480 align:start position:0%
that's a classic like data warehousing
 

00:00:24.480 --> 00:00:26.230 align:start position:0%
that's a classic like data warehousing
problem<00:00:24.800><c> and</c><00:00:24.960><c> so</c><00:00:25.119><c> those</c><00:00:25.359><c> data</c><00:00:25.600><c> warehouses</c><00:00:26.080><c> are</c>

00:00:26.230 --> 00:00:26.240 align:start position:0%
problem and so those data warehouses are
 

00:00:26.240 --> 00:00:28.310 align:start position:0%
problem and so those data warehouses are
already<00:00:26.560><c> built</c><00:00:27.199><c> I</c><00:00:27.439><c> am</c><00:00:27.519><c> just</c><00:00:27.680><c> saying</c><00:00:27.920><c> you</c><00:00:28.160><c> don't</c>

00:00:28.310 --> 00:00:28.320 align:start position:0%
already built I am just saying you don't
 

00:00:28.320 --> 00:00:30.390 align:start position:0%
already built I am just saying you don't
need<00:00:28.400><c> to</c><00:00:28.560><c> build</c><00:00:28.800><c> new</c><00:00:29.119><c> pipelines</c><00:00:29.679><c> and</c><00:00:29.920><c> new</c><00:00:30.160><c> data</c>

00:00:30.390 --> 00:00:30.400 align:start position:0%
need to build new pipelines and new data
 

00:00:30.400 --> 00:00:33.270 align:start position:0%
need to build new pipelines and new data
sources<00:00:30.800><c> and</c><00:00:31.439><c> new</c><00:00:31.760><c> like</c><00:00:32.480><c> connections,</c><00:00:33.040><c> right?</c>

00:00:33.270 --> 00:00:33.280 align:start position:0%
sources and new like connections, right?
 

00:00:33.280 --> 00:00:35.030 align:start position:0%
sources and new like connections, right?
So,<00:00:33.440><c> if</c><00:00:33.680><c> you're</c><00:00:34.000><c> doing</c><00:00:34.320><c> something</c><00:00:34.559><c> that</c><00:00:34.880><c> is</c>

00:00:35.030 --> 00:00:35.040 align:start position:0%
So, if you're doing something that is
 

00:00:35.040 --> 00:00:36.470 align:start position:0%
So, if you're doing something that is
important<00:00:35.360><c> to</c><00:00:35.520><c> your</c><00:00:35.760><c> business,</c><00:00:36.079><c> which</c><00:00:36.320><c> is</c>

00:00:36.470 --> 00:00:36.480 align:start position:0%
important to your business, which is
 

00:00:36.480 --> 00:00:38.389 align:start position:0%
important to your business, which is
where<00:00:36.719><c> the</c><00:00:37.280><c> original</c><00:00:37.680><c> data</c><00:00:37.920><c> warehousing</c>

00:00:38.389 --> 00:00:38.399 align:start position:0%
where the original data warehousing
 

00:00:38.399 --> 00:00:40.630 align:start position:0%
where the original data warehousing
concept<00:00:38.800><c> comes</c><00:00:39.040><c> from,</c><00:00:39.680><c> right?</c><00:00:40.079><c> We</c><00:00:40.320><c> can</c><00:00:40.480><c> go</c>

00:00:40.630 --> 00:00:40.640 align:start position:0%
concept comes from, right? We can go
 

00:00:40.640 --> 00:00:43.830 align:start position:0%
concept comes from, right? We can go
fetch<00:00:40.960><c> it</c><00:00:41.200><c> from</c><00:00:41.360><c> that</c><00:00:41.520><c> data</c><00:00:41.840><c> warehouse.</c><00:00:43.040><c> But</c>

00:00:43.830 --> 00:00:43.840 align:start position:0%
fetch it from that data warehouse. But
 

00:00:43.840 --> 00:00:45.750 align:start position:0%
fetch it from that data warehouse. But
what<00:00:44.160><c> we</c><00:00:44.320><c> can</c><00:00:44.559><c> also</c><00:00:44.879><c> do</c><00:00:45.040><c> and</c><00:00:45.200><c> this</c><00:00:45.360><c> is</c><00:00:45.440><c> what's</c>

00:00:45.750 --> 00:00:45.760 align:start position:0%
what we can also do and this is what's
 

00:00:45.760 --> 00:00:48.389 align:start position:0%
what we can also do and this is what's
done<00:00:46.000><c> through</c><00:00:46.559><c> complex</c><00:00:47.120><c> software</c><00:00:47.600><c> or</c><00:00:48.000><c> complex</c>

00:00:48.389 --> 00:00:48.399 align:start position:0%
done through complex software or complex
 

00:00:48.399 --> 00:00:50.709 align:start position:0%
done through complex software or complex
pipelining<00:00:49.039><c> today.</c><00:00:49.680><c> We</c><00:00:49.920><c> can</c><00:00:50.079><c> also</c><00:00:50.320><c> do</c><00:00:50.480><c> things</c>

00:00:50.709 --> 00:00:50.719 align:start position:0%
pipelining today. We can also do things
 

00:00:50.719 --> 00:00:52.389 align:start position:0%
pipelining today. We can also do things
like<00:00:50.960><c> or</c><00:00:51.120><c> or</c><00:00:51.440><c> we</c><00:00:51.680><c> should</c><00:00:51.840><c> be</c><00:00:51.920><c> able</c><00:00:52.079><c> to</c><00:00:52.239><c> do</c>

00:00:52.389 --> 00:00:52.399 align:start position:0%
like or or we should be able to do
 

00:00:52.399 --> 00:00:55.510 align:start position:0%
like or or we should be able to do
things<00:00:52.640><c> like</c><00:00:53.840><c> hey</c><00:00:54.399><c> you</c><00:00:54.559><c> know</c><00:00:54.719><c> for</c><00:00:54.879><c> example</c><00:00:55.280><c> if</c>

00:00:55.510 --> 00:00:55.520 align:start position:0%
things like hey you know for example if
 

00:00:55.520 --> 00:00:57.510 align:start position:0%
things like hey you know for example if
you<00:00:55.760><c> ask</c><00:00:56.000><c> me</c>

00:00:57.510 --> 00:00:57.520 align:start position:0%
you ask me
 

00:00:57.520 --> 00:00:59.430 align:start position:0%
you ask me
I<00:00:57.760><c> say</c><00:00:57.840><c> this</c><00:00:58.000><c> often</c><00:00:58.399><c> like</c><00:00:58.719><c> I</c><00:00:58.960><c> just</c><00:00:59.120><c> I</c><00:00:59.280><c> don't</c>

00:00:59.430 --> 00:00:59.440 align:start position:0%
I say this often like I just I don't
 

00:00:59.440 --> 00:01:01.510 align:start position:0%
I say this often like I just I don't
care<00:00:59.520><c> about</c><00:00:59.760><c> Terminator</c><00:01:00.800><c> happening</c><00:01:01.359><c> right</c>

00:01:01.510 --> 00:01:01.520 align:start position:0%
care about Terminator happening right
 

00:01:01.520 --> 00:01:03.430 align:start position:0%
care about Terminator happening right
like<00:01:01.760><c> I</c><00:01:01.920><c> care</c><00:01:02.079><c> about</c><00:01:02.239><c> where</c><00:01:02.399><c> my</c><00:01:02.559><c> order</c><00:01:02.800><c> is</c><00:01:03.120><c> if</c><00:01:03.280><c> I</c>

00:01:03.430 --> 00:01:03.440 align:start position:0%
like I care about where my order is if I
 

00:01:03.440 --> 00:01:06.620 align:start position:0%
like I care about where my order is if I
ordered<00:01:03.760><c> something</c><00:01:04.000><c> online</c><00:01:04.640><c> for</c><00:01:04.799><c> example</c><00:01:05.199><c> I</c>

00:01:06.620 --> 00:01:06.630 align:start position:0%
ordered something online for example I
 

00:01:06.630 --> 00:01:16.540 align:start position:0%
ordered something online for example I
[Music]

