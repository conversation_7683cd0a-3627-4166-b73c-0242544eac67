import os
import sys

def find_ollama():
    print("Searching for ollama.exe...")
    search_paths = []

    # Add Program Files directories
    program_files = os.environ.get("ProgramFiles")
    if program_files:
        search_paths.append(program_files)
    
    program_files_x86 = os.environ.get("ProgramFiles(x86)")
    if program_files_x86:
        search_paths.append(program_files_x86)

    # Add user's AppData\Local\Programs
    local_app_data = os.environ.get("LOCALAPPDATA")
    if local_app_data:
        search_paths.append(os.path.join(local_app_data, "Programs"))

    # Add some common direct Ollama paths
    if program_files:
        search_paths.append(os.path.join(program_files, "Ollama"))
    if local_app_data:
         search_paths.append(os.path.join(local_app_data, "Ollama")) # For newer installations
         search_paths.append(os.path.join(local_app_data, "Programs", "Ollama"))


    found_paths = []

    for path_to_search in search_paths:
        if not os.path.isdir(path_to_search):
            # print(f"Directory does not exist, skipping: {path_to_search}")
            continue
        # print(f"Searching in: {path_to_search}")
        for root, dirs, files in os.walk(path_to_search):
            if "ollama.exe" in files:
                found_path = os.path.join(root, "ollama.exe")
                print(f"Found ollama.exe at: {found_path}")
                found_paths.append(found_path)
                # Optionally, stop after first find if you expect only one installation
                # return found_paths 

    # Also try 'where' command as a fallback, as it checks PATH
    try:
        # print("Trying 'where ollama.exe'...")
        result = os.popen('where ollama.exe').read().strip()
        if result and os.path.exists(result.splitlines()[0]): # Check if first line is a valid path
            found_in_path = result.splitlines()[0]
            if found_in_path not in found_paths:
                print(f"Found ollama.exe via 'where' command (in PATH): {found_in_path}")
                found_paths.append(found_in_path)
    except Exception as e:
        # print(f"Could not run 'where ollama.exe': {e}")
        pass
        
    if not found_paths:
        print("ollama.exe not found in common locations or PATH.")
        print("Please ensure Ollama is installed correctly.")
        print("Common installation paths are:")
        print(r"- C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe")
        print(r"- C:\Program Files\Ollama\ollama.exe")
        print("If you installed it elsewhere, you'll need to provide the path manually.")

    return found_paths

if __name__ == "__main__":
    find_ollama()
