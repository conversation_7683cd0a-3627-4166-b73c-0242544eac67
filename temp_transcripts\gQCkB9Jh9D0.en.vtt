WEBVTT
Kind: captions
Language: en

00:00:00.030 --> 00:00:01.370 align:start position:0%
 
hey<00:00:00.359><c> and</c><00:00:00.690><c> welcome</c><00:00:00.780><c> to</c><00:00:00.960><c> another</c><00:00:01.140><c> day</c><00:00:01.350><c> a</c>

00:00:01.370 --> 00:00:01.380 align:start position:0%
hey and welcome to another day a
 

00:00:01.380 --> 00:00:03.260 align:start position:0%
hey and welcome to another day a
structured<00:00:01.680><c> video</c><00:00:01.920><c> and</c><00:00:02.190><c> this</c><00:00:02.970><c> time</c><00:00:03.120><c> we're</c>

00:00:03.260 --> 00:00:03.270 align:start position:0%
structured video and this time we're
 

00:00:03.270 --> 00:00:04.430 align:start position:0%
structured video and this time we're
gonna<00:00:03.360><c> be</c><00:00:03.480><c> talking</c><00:00:03.780><c> about</c><00:00:03.810><c> doubly</c><00:00:04.290><c> linked</c>

00:00:04.430 --> 00:00:04.440 align:start position:0%
gonna be talking about doubly linked
 

00:00:04.440 --> 00:00:06.140 align:start position:0%
gonna be talking about doubly linked
lists<00:00:04.740><c> with</c><00:00:05.100><c> the</c><00:00:05.190><c> implementation</c><00:00:05.400><c> using</c>

00:00:06.140 --> 00:00:06.150 align:start position:0%
lists with the implementation using
 

00:00:06.150 --> 00:00:07.639 align:start position:0%
lists with the implementation using
Sentinel<00:00:06.600><c> nodes</c><00:00:06.750><c> and</c><00:00:07.170><c> we'll</c><00:00:07.410><c> get</c><00:00:07.500><c> to</c><00:00:07.560><c> what</c>

00:00:07.639 --> 00:00:07.649 align:start position:0%
Sentinel nodes and we'll get to what
 

00:00:07.649 --> 00:00:10.280 align:start position:0%
Sentinel nodes and we'll get to what
those<00:00:07.740><c> are</c><00:00:07.919><c> shortly</c><00:00:08.269><c> first</c><00:00:09.269><c> off</c><00:00:09.450><c> what</c><00:00:10.080><c> is</c><00:00:10.260><c> a</c>

00:00:10.280 --> 00:00:10.290 align:start position:0%
those are shortly first off what is a
 

00:00:10.290 --> 00:00:12.919 align:start position:0%
those are shortly first off what is a
doubly<00:00:10.740><c> linked</c><00:00:10.950><c> list</c><00:00:11.130><c> well</c><00:00:11.969><c> it</c><00:00:12.780><c> basically</c>

00:00:12.919 --> 00:00:12.929 align:start position:0%
doubly linked list well it basically
 

00:00:12.929 --> 00:00:15.169 align:start position:0%
doubly linked list well it basically
allows<00:00:13.200><c> for</c><00:00:14.099><c> traversing</c><00:00:14.670><c> forwards</c><00:00:15.089><c> and</c>

00:00:15.169 --> 00:00:15.179 align:start position:0%
allows for traversing forwards and
 

00:00:15.179 --> 00:00:17.900 align:start position:0%
allows for traversing forwards and
backwards<00:00:15.480><c> in</c><00:00:15.870><c> the</c><00:00:16.199><c> linked</c><00:00:16.410><c> list</c><00:00:16.470><c> a</c><00:00:16.910><c> singly</c>

00:00:17.900 --> 00:00:17.910 align:start position:0%
backwards in the linked list a singly
 

00:00:17.910 --> 00:00:19.910 align:start position:0%
backwards in the linked list a singly
linked<00:00:18.119><c> list</c><00:00:18.330><c> only</c><00:00:18.690><c> allowed</c><00:00:19.170><c> each</c><00:00:19.650><c> of</c><00:00:19.800><c> us</c>

00:00:19.910 --> 00:00:19.920 align:start position:0%
linked list only allowed each of us
 

00:00:19.920 --> 00:00:22.130 align:start position:0%
linked list only allowed each of us
forwards<00:00:20.369><c> because</c><00:00:20.550><c> it</c><00:00:20.939><c> held</c><00:00:21.630><c> a</c><00:00:21.750><c> reference</c>

00:00:22.130 --> 00:00:22.140 align:start position:0%
forwards because it held a reference
 

00:00:22.140 --> 00:00:24.080 align:start position:0%
forwards because it held a reference
only<00:00:22.680><c> to</c><00:00:22.830><c> the</c><00:00:22.920><c> next</c><00:00:23.250><c> node</c><00:00:23.369><c> in</c><00:00:23.430><c> the</c><00:00:23.490><c> list</c><00:00:23.670><c> not</c>

00:00:24.080 --> 00:00:24.090 align:start position:0%
only to the next node in the list not
 

00:00:24.090 --> 00:00:26.540 align:start position:0%
only to the next node in the list not
the<00:00:24.240><c> previous</c><00:00:24.650><c> here</c><00:00:25.650><c> as</c><00:00:25.830><c> you</c><00:00:26.039><c> can</c><00:00:26.070><c> see</c><00:00:26.340><c> a</c>

00:00:26.540 --> 00:00:26.550 align:start position:0%
the previous here as you can see a
 

00:00:26.550 --> 00:00:29.089 align:start position:0%
the previous here as you can see a
doubly<00:00:27.060><c> linked</c><00:00:27.269><c> list</c><00:00:27.449><c> has</c><00:00:27.779><c> a</c><00:00:28.080><c> reference</c><00:00:28.230><c> to</c>

00:00:29.089 --> 00:00:29.099 align:start position:0%
doubly linked list has a reference to
 

00:00:29.099 --> 00:00:30.500 align:start position:0%
doubly linked list has a reference to
the<00:00:29.220><c> next</c><00:00:29.460><c> node</c><00:00:29.580><c> of</c><00:00:29.640><c> the</c><00:00:29.730><c> list</c><00:00:29.880><c> in</c><00:00:30.060><c> the</c>

00:00:30.500 --> 00:00:30.510 align:start position:0%
the next node of the list in the
 

00:00:30.510 --> 00:00:32.450 align:start position:0%
the next node of the list in the
previous<00:00:30.929><c> so</c><00:00:31.830><c> there</c><00:00:31.980><c> are</c><00:00:32.040><c> three</c><00:00:32.250><c> total</c>

00:00:32.450 --> 00:00:32.460 align:start position:0%
previous so there are three total
 

00:00:32.460 --> 00:00:34.760 align:start position:0%
previous so there are three total
references<00:00:32.850><c> per</c><00:00:33.120><c> node</c><00:00:33.360><c> now</c><00:00:33.540><c> one</c><00:00:34.290><c> to</c><00:00:34.410><c> the</c><00:00:34.500><c> value</c>

00:00:34.760 --> 00:00:34.770 align:start position:0%
references per node now one to the value
 

00:00:34.770 --> 00:00:36.860 align:start position:0%
references per node now one to the value
it's<00:00:34.920><c> holding</c><00:00:35.160><c> to</c><00:00:36.090><c> the</c><00:00:36.180><c> next</c><00:00:36.450><c> reference</c><00:00:36.570><c> in</c>

00:00:36.860 --> 00:00:36.870 align:start position:0%
it's holding to the next reference in
 

00:00:36.870 --> 00:00:40.280 align:start position:0%
it's holding to the next reference in
the<00:00:36.930><c> list</c><00:00:37.110><c> into</c><00:00:37.590><c> the</c><00:00:37.680><c> previous</c><00:00:38.660><c> so</c><00:00:39.660><c> what</c><00:00:40.170><c> our</c>

00:00:40.280 --> 00:00:40.290 align:start position:0%
the list into the previous so what our
 

00:00:40.290 --> 00:00:43.310 align:start position:0%
the list into the previous so what our
Sentinel<00:00:40.649><c> nodes</c><00:00:41.000><c> well</c><00:00:42.000><c> very</c><00:00:42.899><c> simple</c>

00:00:43.310 --> 00:00:43.320 align:start position:0%
Sentinel nodes well very simple
 

00:00:43.320 --> 00:00:45.950 align:start position:0%
Sentinel nodes well very simple
there's<00:00:43.620><c> dummy</c><00:00:44.070><c> nodes</c><00:00:44.340><c> they</c><00:00:45.270><c> don't</c><00:00:45.450><c> hold</c><00:00:45.629><c> any</c>

00:00:45.950 --> 00:00:45.960 align:start position:0%
there's dummy nodes they don't hold any
 

00:00:45.960 --> 00:00:48.950 align:start position:0%
there's dummy nodes they don't hold any
value<00:00:46.230><c> and</c><00:00:46.760><c> here</c><00:00:47.760><c> we</c><00:00:47.910><c> have</c><00:00:48.090><c> a</c><00:00:48.300><c> header</c><00:00:48.539><c> and</c><00:00:48.809><c> a</c>

00:00:48.950 --> 00:00:48.960 align:start position:0%
value and here we have a header and a
 

00:00:48.960 --> 00:00:51.380 align:start position:0%
value and here we have a header and a
trailer<00:00:49.410><c> so</c><00:00:50.309><c> these</c><00:00:50.430><c> Sentinel</c><00:00:50.789><c> nodes</c><00:00:50.910><c> will</c>

00:00:51.380 --> 00:00:51.390 align:start position:0%
trailer so these Sentinel nodes will
 

00:00:51.390 --> 00:00:53.510 align:start position:0%
trailer so these Sentinel nodes will
always<00:00:52.020><c> be</c><00:00:52.170><c> there</c><00:00:52.410><c> they</c><00:00:52.800><c> will</c><00:00:52.949><c> not</c><00:00:53.100><c> be</c><00:00:53.190><c> removed</c>

00:00:53.510 --> 00:00:53.520 align:start position:0%
always be there they will not be removed
 

00:00:53.520 --> 00:00:56.119 align:start position:0%
always be there they will not be removed
and<00:00:53.730><c> they'll</c><00:00:53.910><c> also</c><00:00:54.090><c> never</c><00:00:54.510><c> hold</c><00:00:54.750><c> data</c><00:00:55.129><c> they're</c>

00:00:56.119 --> 00:00:56.129 align:start position:0%
and they'll also never hold data they're
 

00:00:56.129 --> 00:00:58.099 align:start position:0%
and they'll also never hold data they're
really<00:00:56.370><c> here</c><00:00:56.640><c> just</c><00:00:56.670><c> to</c><00:00:57.090><c> simplify</c><00:00:57.420><c> the</c><00:00:57.629><c> logic</c>

00:00:58.099 --> 00:00:58.109 align:start position:0%
really here just to simplify the logic
 

00:00:58.109 --> 00:01:01.310 align:start position:0%
really here just to simplify the logic
and<00:00:58.289><c> code</c><00:00:59.030><c> for</c><00:01:00.030><c> a</c><00:01:00.059><c> doubly</c><00:01:00.480><c> linked</c><00:01:00.660><c> list</c><00:01:00.840><c> so</c>

00:01:01.310 --> 00:01:01.320 align:start position:0%
and code for a doubly linked list so
 

00:01:01.320 --> 00:01:03.650 align:start position:0%
and code for a doubly linked list so
what<00:01:01.949><c> are</c><00:01:02.039><c> the</c><00:01:02.129><c> advantages</c><00:01:02.190><c> of</c><00:01:02.730><c> using</c><00:01:03.120><c> settle</c>

00:01:03.650 --> 00:01:03.660 align:start position:0%
what are the advantages of using settle
 

00:01:03.660 --> 00:01:03.979 align:start position:0%
what are the advantages of using settle
nodes

00:01:03.979 --> 00:01:03.989 align:start position:0%
nodes
 

00:01:03.989 --> 00:01:06.859 align:start position:0%
nodes
well<00:01:04.379><c> as</c><00:01:04.939><c> I</c><00:01:05.939><c> said</c><00:01:06.000><c> the</c><00:01:06.540><c> header</c><00:01:06.659><c> and</c><00:01:06.840><c> trailer</c>

00:01:06.859 --> 00:01:06.869 align:start position:0%
well as I said the header and trailer
 

00:01:06.869 --> 00:01:08.719 align:start position:0%
well as I said the header and trailer
are<00:01:07.350><c> just</c><00:01:08.100><c> dummy</c><00:01:08.310><c> nodes</c>

00:01:08.719 --> 00:01:08.729 align:start position:0%
are just dummy nodes
 

00:01:08.729 --> 00:01:10.850 align:start position:0%
are just dummy nodes
they'll<00:01:09.150><c> never</c><00:01:09.390><c> hold</c><00:01:09.659><c> data</c><00:01:09.869><c> and</c><00:01:10.409><c> we're</c><00:01:10.680><c> always</c>

00:01:10.850 --> 00:01:10.860 align:start position:0%
they'll never hold data and we're always
 

00:01:10.860 --> 00:01:13.520 align:start position:0%
they'll never hold data and we're always
going<00:01:11.070><c> to</c><00:01:11.130><c> insert</c><00:01:11.549><c> and</c><00:01:11.880><c> delete</c><00:01:12.210><c> nodes</c><00:01:12.689><c> between</c>

00:01:13.520 --> 00:01:13.530 align:start position:0%
going to insert and delete nodes between
 

00:01:13.530 --> 00:01:17.480 align:start position:0%
going to insert and delete nodes between
these<00:01:14.340><c> settle</c><00:01:14.700><c> nodes</c><00:01:15.590><c> so</c><00:01:16.590><c> but</c><00:01:17.280><c> what</c><00:01:17.400><c> we're</c>

00:01:17.480 --> 00:01:17.490 align:start position:0%
these settle nodes so but what we're
 

00:01:17.490 --> 00:01:20.899 align:start position:0%
these settle nodes so but what we're
gonna<00:01:17.610><c> do</c><00:01:17.850><c> is</c><00:01:18.119><c> have</c><00:01:18.900><c> a</c><00:01:19.229><c> generic</c><00:01:19.590><c> remove</c><00:01:20.549><c> and</c>

00:01:20.899 --> 00:01:20.909 align:start position:0%
gonna do is have a generic remove and
 

00:01:20.909 --> 00:01:24.620 align:start position:0%
gonna do is have a generic remove and
insertion<00:01:21.900><c> method</c><00:01:22.490><c> because</c><00:01:23.490><c> because</c><00:01:24.479><c> we're</c>

00:01:24.620 --> 00:01:24.630 align:start position:0%
insertion method because because we're
 

00:01:24.630 --> 00:01:26.630 align:start position:0%
insertion method because because we're
always<00:01:24.840><c> adding</c><00:01:25.229><c> a</c><00:01:25.500><c> node</c><00:01:25.680><c> or</c><00:01:25.920><c> deleting</c><00:01:26.340><c> it</c><00:01:26.430><c> in</c>

00:01:26.630 --> 00:01:26.640 align:start position:0%
always adding a node or deleting it in
 

00:01:26.640 --> 00:01:28.789 align:start position:0%
always adding a node or deleting it in
between<00:01:27.090><c> those</c><00:01:27.540><c> no</c><00:01:28.080><c> matter</c><00:01:28.259><c> where</c><00:01:28.530><c> we're</c><00:01:28.650><c> at</c>

00:01:28.789 --> 00:01:28.799 align:start position:0%
between those no matter where we're at
 

00:01:28.799 --> 00:01:30.800 align:start position:0%
between those no matter where we're at
you're<00:01:29.610><c> never</c><00:01:29.790><c> going</c><00:01:29.970><c> to</c><00:01:30.000><c> add</c><00:01:30.180><c> node</c><00:01:30.420><c> before</c>

00:01:30.800 --> 00:01:30.810 align:start position:0%
you're never going to add node before
 

00:01:30.810 --> 00:01:32.060 align:start position:0%
you're never going to add node before
the<00:01:30.930><c> head</c><00:01:31.079><c> or</c><00:01:31.200><c> something</c><00:01:31.380><c> mode</c><00:01:31.590><c> and</c><00:01:31.829><c> now</c><00:01:31.950><c> we're</c>

00:01:32.060 --> 00:01:32.070 align:start position:0%
the head or something mode and now we're
 

00:01:32.070 --> 00:01:33.260 align:start position:0%
the head or something mode and now we're
going<00:01:32.159><c> to</c><00:01:32.220><c> add</c><00:01:32.310><c> one</c><00:01:32.520><c> after</c><00:01:32.700><c> the</c><00:01:32.939><c> trailer</c>

00:01:33.260 --> 00:01:33.270 align:start position:0%
going to add one after the trailer
 

00:01:33.270 --> 00:01:34.850 align:start position:0%
going to add one after the trailer
settle<00:01:33.540><c> node</c><00:01:33.689><c> they're</c><00:01:34.200><c> there</c><00:01:34.439><c> for</c><00:01:34.740><c> that</c>

00:01:34.850 --> 00:01:34.860 align:start position:0%
settle node they're there for that
 

00:01:34.860 --> 00:01:37.910 align:start position:0%
settle node they're there for that
reason<00:01:34.939><c> so</c><00:01:35.939><c> that</c><00:01:36.119><c> we</c><00:01:36.630><c> can</c><00:01:36.780><c> simplify</c><00:01:37.110><c> the</c><00:01:37.350><c> code</c>

00:01:37.910 --> 00:01:37.920 align:start position:0%
reason so that we can simplify the code
 

00:01:37.920 --> 00:01:41.060 align:start position:0%
reason so that we can simplify the code
to<00:01:38.640><c> always</c><00:01:39.030><c> add</c><00:01:39.210><c> a</c><00:01:39.240><c> node</c><00:01:39.509><c> in</c><00:01:39.750><c> between</c><00:01:40.259><c> other</c>

00:01:41.060 --> 00:01:41.070 align:start position:0%
to always add a node in between other
 

00:01:41.070 --> 00:01:44.749 align:start position:0%
to always add a node in between other
nodes<00:01:41.750><c> okay</c><00:01:42.750><c> so</c><00:01:43.290><c> let's</c><00:01:43.770><c> talk</c><00:01:43.890><c> about</c><00:01:44.009><c> inserting</c>

00:01:44.749 --> 00:01:44.759 align:start position:0%
nodes okay so let's talk about inserting
 

00:01:44.759 --> 00:01:48.050 align:start position:0%
nodes okay so let's talk about inserting
a<00:01:44.820><c> node</c><00:01:45.030><c> in</c><00:01:45.299><c> a</c><00:01:45.420><c> doubly</c><00:01:45.689><c> linked</c><00:01:45.840><c> list</c><00:01:46.759><c> so</c><00:01:47.759><c> in</c>

00:01:48.050 --> 00:01:48.060 align:start position:0%
a node in a doubly linked list so in
 

00:01:48.060 --> 00:01:50.749 align:start position:0%
a node in a doubly linked list so in
Nicholas<00:01:48.390><c> a</c><00:01:48.600><c> we</c><00:01:49.170><c> just</c><00:01:49.320><c> have</c><00:01:49.439><c> a</c><00:01:49.740><c> doubly</c><00:01:50.610><c> linked</c>

00:01:50.749 --> 00:01:50.759 align:start position:0%
Nicholas a we just have a doubly linked
 

00:01:50.759 --> 00:01:52.819 align:start position:0%
Nicholas a we just have a doubly linked
list<00:01:50.970><c> with</c><00:01:51.299><c> two</c><00:01:51.540><c> nodes</c><00:01:51.750><c> one</c><00:01:52.110><c> with</c><00:01:52.409><c> value</c><00:01:52.590><c> of</c><00:01:52.649><c> 10</c>

00:01:52.819 --> 00:01:52.829 align:start position:0%
list with two nodes one with value of 10
 

00:01:52.829 --> 00:01:55.700 align:start position:0%
list with two nodes one with value of 10
and<00:01:53.040><c> one</c><00:01:53.159><c> holding</c><00:01:53.399><c> a</c><00:01:53.460><c> value</c><00:01:53.640><c> of</c><00:01:53.729><c> 6</c><00:01:53.939><c> in</c><00:01:54.710><c> linkless</c>

00:01:55.700 --> 00:01:55.710 align:start position:0%
and one holding a value of 6 in linkless
 

00:01:55.710 --> 00:01:58.249 align:start position:0%
and one holding a value of 6 in linkless
B<00:01:56.040><c> we're</c><00:01:56.729><c> trying</c><00:01:56.969><c> to</c><00:01:57.060><c> insert</c><00:01:57.450><c> a</c><00:01:57.659><c> node</c><00:01:57.990><c> in</c>

00:01:58.249 --> 00:01:58.259 align:start position:0%
B we're trying to insert a node in
 

00:01:58.259 --> 00:02:01.190 align:start position:0%
B we're trying to insert a node in
between<00:01:58.710><c> those</c><00:01:58.979><c> two</c><00:01:59.189><c> nodes</c><00:01:59.689><c> alright</c><00:02:00.689><c> so</c><00:02:00.869><c> let's</c>

00:02:01.190 --> 00:02:01.200 align:start position:0%
between those two nodes alright so let's
 

00:02:01.200 --> 00:02:02.780 align:start position:0%
between those two nodes alright so let's
look<00:02:01.290><c> a</c><00:02:01.350><c> look</c><00:02:01.530><c> at</c><00:02:01.619><c> the</c><00:02:01.680><c> algorithm</c><00:02:02.159><c> to</c><00:02:02.579><c> see</c><00:02:02.729><c> how</c>

00:02:02.780 --> 00:02:02.790 align:start position:0%
look a look at the algorithm to see how
 

00:02:02.790 --> 00:02:05.569 align:start position:0%
look a look at the algorithm to see how
this<00:02:02.880><c> works</c><00:02:03.350><c> so</c><00:02:04.350><c> first</c><00:02:04.560><c> off</c><00:02:04.710><c> we're</c><00:02:05.189><c> name</c><00:02:05.340><c> it</c><00:02:05.460><c> a</c>

00:02:05.569 --> 00:02:05.579 align:start position:0%
this works so first off we're name it a
 

00:02:05.579 --> 00:02:08.839 align:start position:0%
this works so first off we're name it a
between<00:02:05.969><c> because</c><00:02:06.719><c> we</c><00:02:07.560><c> have</c><00:02:07.740><c> settle</c><00:02:08.129><c> nodes</c><00:02:08.340><c> and</c>

00:02:08.839 --> 00:02:08.849 align:start position:0%
between because we have settle nodes and
 

00:02:08.849 --> 00:02:10.460 align:start position:0%
between because we have settle nodes and
our<00:02:09.030><c> implementation</c><00:02:09.239><c> of</c><00:02:09.929><c> a</c><00:02:10.050><c> doubly</c><00:02:10.319><c> linked</c>

00:02:10.460 --> 00:02:10.470 align:start position:0%
our implementation of a doubly linked
 

00:02:10.470 --> 00:02:12.650 align:start position:0%
our implementation of a doubly linked
list<00:02:10.679><c> we're</c><00:02:11.370><c> always</c><00:02:11.580><c> adding</c><00:02:11.940><c> a</c><00:02:12.180><c> node</c><00:02:12.420><c> in</c>

00:02:12.650 --> 00:02:12.660 align:start position:0%
list we're always adding a node in
 

00:02:12.660 --> 00:02:13.890 align:start position:0%
list we're always adding a node in
between

00:02:13.890 --> 00:02:13.900 align:start position:0%
between
 

00:02:13.900 --> 00:02:16.440 align:start position:0%
between
to<00:02:14.170><c> other</c><00:02:14.349><c> nerds</c><00:02:14.670><c> so</c><00:02:15.670><c> we're</c><00:02:15.940><c> not</c><00:02:16.000><c> passing</c><00:02:16.390><c> the</c>

00:02:16.440 --> 00:02:16.450 align:start position:0%
to other nerds so we're not passing the
 

00:02:16.450 --> 00:02:18.420 align:start position:0%
to other nerds so we're not passing the
value<00:02:16.599><c> we</c><00:02:16.870><c> want</c><00:02:17.019><c> the</c><00:02:17.170><c> store</c><00:02:17.409><c> and</c><00:02:18.190><c> we're</c><00:02:18.310><c> going</c>

00:02:18.420 --> 00:02:18.430 align:start position:0%
value we want the store and we're going
 

00:02:18.430 --> 00:02:21.479 align:start position:0%
value we want the store and we're going
to<00:02:18.489><c> pass</c><00:02:18.670><c> in</c><00:02:19.049><c> two</c><00:02:20.049><c> nodes</c><00:02:20.319><c> the</c><00:02:21.069><c> first</c><00:02:21.310><c> one's</c>

00:02:21.479 --> 00:02:21.489 align:start position:0%
to pass in two nodes the first one's
 

00:02:21.489 --> 00:02:25.619 align:start position:0%
to pass in two nodes the first one's
going<00:02:21.670><c> to</c><00:02:21.730><c> be</c><00:02:22.590><c> the</c><00:02:23.590><c> one</c><00:02:23.830><c> before</c><00:02:24.580><c> the</c><00:02:25.360><c> insertion</c>

00:02:25.619 --> 00:02:25.629 align:start position:0%
going to be the one before the insertion
 

00:02:25.629 --> 00:02:27.210 align:start position:0%
going to be the one before the insertion
and<00:02:26.080><c> the</c><00:02:26.110><c> next</c><00:02:26.620><c> one</c><00:02:26.739><c> is</c><00:02:26.830><c> gonna</c><00:02:26.920><c> be</c><00:02:27.040><c> the</c><00:02:27.099><c> one</c>

00:02:27.210 --> 00:02:27.220 align:start position:0%
and the next one is gonna be the one
 

00:02:27.220 --> 00:02:31.050 align:start position:0%
and the next one is gonna be the one
after<00:02:27.459><c> the</c><00:02:27.849><c> insertion</c><00:02:28.680><c> so</c><00:02:29.680><c> in</c><00:02:29.950><c> step</c><00:02:30.220><c> one</c><00:02:30.430><c> we're</c>

00:02:31.050 --> 00:02:31.060 align:start position:0%
after the insertion so in step one we're
 

00:02:31.060 --> 00:02:34.140 align:start position:0%
after the insertion so in step one we're
creating<00:02:31.420><c> a</c><00:02:31.480><c> new</c><00:02:31.510><c> node</c><00:02:32.310><c> the</c><00:02:33.310><c> first</c><00:02:33.340><c> arguments</c>

00:02:34.140 --> 00:02:34.150 align:start position:0%
creating a new node the first arguments
 

00:02:34.150 --> 00:02:35.670 align:start position:0%
creating a new node the first arguments
going<00:02:34.239><c> to</c><00:02:34.329><c> be</c><00:02:34.390><c> the</c><00:02:34.480><c> value</c><00:02:34.810><c> we</c><00:02:34.989><c> pass</c><00:02:35.200><c> in</c><00:02:35.470><c> the</c>

00:02:35.670 --> 00:02:35.680 align:start position:0%
going to be the value we pass in the
 

00:02:35.680 --> 00:02:37.500 align:start position:0%
going to be the value we pass in the
second<00:02:36.459><c> argument</c><00:02:36.579><c> is</c><00:02:37.030><c> going</c><00:02:37.209><c> to</c><00:02:37.299><c> be</c><00:02:37.390><c> the</c>

00:02:37.500 --> 00:02:37.510 align:start position:0%
second argument is going to be the
 

00:02:37.510 --> 00:02:40.979 align:start position:0%
second argument is going to be the
previous<00:02:37.959><c> reference</c><00:02:38.590><c> to</c><00:02:39.489><c> the</c><00:02:40.420><c> node</c><00:02:40.659><c> before</c>

00:02:40.979 --> 00:02:40.989 align:start position:0%
previous reference to the node before
 

00:02:40.989 --> 00:02:43.350 align:start position:0%
previous reference to the node before
the<00:02:41.110><c> insertion</c><00:02:41.379><c> and</c><00:02:41.799><c> then</c><00:02:42.549><c> the</c><00:02:43.060><c> third</c>

00:02:43.350 --> 00:02:43.360 align:start position:0%
the insertion and then the third
 

00:02:43.360 --> 00:02:46.979 align:start position:0%
the insertion and then the third
argument<00:02:43.930><c> is</c><00:02:44.340><c> the</c><00:02:45.340><c> next</c><00:02:45.670><c> node</c><00:02:45.910><c> the</c><00:02:46.690><c> next</c>

00:02:46.979 --> 00:02:46.989 align:start position:0%
argument is the next node the next
 

00:02:46.989 --> 00:02:49.440 align:start position:0%
argument is the next node the next
reference<00:02:47.410><c> to</c><00:02:48.310><c> the</c><00:02:48.730><c> node</c><00:02:48.940><c> after</c><00:02:49.150><c> the</c>

00:02:49.440 --> 00:02:49.450 align:start position:0%
reference to the node after the
 

00:02:49.450 --> 00:02:52.710 align:start position:0%
reference to the node after the
insertion<00:02:49.720><c> all</c><00:02:50.590><c> right</c><00:02:50.970><c> and</c><00:02:51.970><c> so</c><00:02:52.150><c> step</c><00:02:52.390><c> two</c><00:02:52.569><c> and</c>

00:02:52.710 --> 00:02:52.720 align:start position:0%
insertion all right and so step two and
 

00:02:52.720 --> 00:02:56.520 align:start position:0%
insertion all right and so step two and
three<00:02:53.109><c> we're</c><00:02:53.980><c> taking</c><00:02:54.459><c> the</c><00:02:55.150><c> the</c><00:02:55.690><c> previous</c><00:02:56.049><c> node</c>

00:02:56.520 --> 00:02:56.530 align:start position:0%
three we're taking the the previous node
 

00:02:56.530 --> 00:02:58.619 align:start position:0%
three we're taking the the previous node
before<00:02:56.980><c> the</c><00:02:57.220><c> insertion</c><00:02:57.459><c> sending</c><00:02:58.209><c> its</c><00:02:58.329><c> next</c>

00:02:58.619 --> 00:02:58.629 align:start position:0%
before the insertion sending its next
 

00:02:58.629 --> 00:03:01.559 align:start position:0%
before the insertion sending its next
reference<00:02:58.750><c> to</c><00:02:59.019><c> the</c><00:02:59.109><c> newest</c><00:02:59.379><c> one</c><00:02:59.560><c> and</c><00:03:00.269><c> in</c><00:03:01.269><c> the</c>

00:03:01.559 --> 00:03:01.569 align:start position:0%
reference to the newest one and in the
 

00:03:01.569 --> 00:03:03.899 align:start position:0%
reference to the newest one and in the
node<00:03:02.290><c> after</c><00:03:02.650><c> the</c><00:03:02.739><c> insertion</c><00:03:03.010><c> we're</c><00:03:03.640><c> setting</c>

00:03:03.899 --> 00:03:03.909 align:start position:0%
node after the insertion we're setting
 

00:03:03.909 --> 00:03:05.720 align:start position:0%
node after the insertion we're setting
its<00:03:04.060><c> previous</c><00:03:04.629><c> reference</c><00:03:04.959><c> to</c><00:03:05.109><c> the</c><00:03:05.200><c> newest</c><00:03:05.470><c> one</c>

00:03:05.720 --> 00:03:05.730 align:start position:0%
its previous reference to the newest one
 

00:03:05.730 --> 00:03:08.069 align:start position:0%
its previous reference to the newest one
okay<00:03:06.760><c> then</c><00:03:07.239><c> obviously</c><00:03:07.420><c> we're</c><00:03:07.750><c> how</c><00:03:08.019><c> to</c>

00:03:08.069 --> 00:03:08.079 align:start position:0%
okay then obviously we're how to
 

00:03:08.079 --> 00:03:11.399 align:start position:0%
okay then obviously we're how to
increase<00:03:08.440><c> the</c><00:03:08.560><c> size</c><00:03:08.769><c> of</c><00:03:08.920><c> a</c><00:03:09.010><c> list</c><00:03:09.540><c> so</c><00:03:10.540><c> now</c><00:03:11.230><c> if</c><00:03:11.319><c> we</c>

00:03:11.399 --> 00:03:11.409 align:start position:0%
increase the size of a list so now if we
 

00:03:11.409 --> 00:03:15.809 align:start position:0%
increase the size of a list so now if we
go<00:03:11.530><c> back</c><00:03:11.590><c> to</c><00:03:11.769><c> a</c><00:03:12.609><c> doubly</c><00:03:13.299><c> linked</c><00:03:13.480><c> list</c><00:03:13.720><c> B</c><00:03:14.819><c> this</c>

00:03:15.809 --> 00:03:15.819 align:start position:0%
go back to a doubly linked list B this
 

00:03:15.819 --> 00:03:18.839 align:start position:0%
go back to a doubly linked list B this
is<00:03:16.030><c> indicating</c><00:03:16.720><c> that</c><00:03:17.250><c> maneuver</c><00:03:18.250><c> inserting</c>

00:03:18.839 --> 00:03:18.849 align:start position:0%
is indicating that maneuver inserting
 

00:03:18.849 --> 00:03:21.390 align:start position:0%
is indicating that maneuver inserting
we're<00:03:19.239><c> setting</c><00:03:19.510><c> its</c><00:03:19.630><c> next</c><00:03:19.959><c> reference</c><00:03:20.310><c> to</c><00:03:21.310><c> the</c>

00:03:21.390 --> 00:03:21.400 align:start position:0%
we're setting its next reference to the
 

00:03:21.400 --> 00:03:22.680 align:start position:0%
we're setting its next reference to the
node<00:03:21.549><c> after</c><00:03:21.760><c> the</c><00:03:21.970><c> insertion</c><00:03:22.209><c> and</c><00:03:22.540><c> we're</c>

00:03:22.680 --> 00:03:22.690 align:start position:0%
node after the insertion and we're
 

00:03:22.690 --> 00:03:24.870 align:start position:0%
node after the insertion and we're
setting<00:03:23.019><c> its</c><00:03:23.260><c> previous</c><00:03:23.889><c> reference</c><00:03:24.220><c> to</c><00:03:24.819><c> the</c>

00:03:24.870 --> 00:03:24.880 align:start position:0%
setting its previous reference to the
 

00:03:24.880 --> 00:03:27.659 align:start position:0%
setting its previous reference to the
node<00:03:25.060><c> before</c><00:03:25.389><c> the</c><00:03:25.480><c> insertion</c><00:03:25.810><c> the</c><00:03:26.669><c> node</c>

00:03:27.659 --> 00:03:27.669 align:start position:0%
node before the insertion the node
 

00:03:27.669 --> 00:03:29.750 align:start position:0%
node before the insertion the node
before<00:03:28.060><c> after</c><00:03:28.239><c> insertion</c><00:03:28.599><c> still</c><00:03:29.290><c> have</c>

00:03:29.750 --> 00:03:29.760 align:start position:0%
before after insertion still have
 

00:03:29.760 --> 00:03:32.550 align:start position:0%
before after insertion still have
references<00:03:30.760><c> to</c><00:03:30.819><c> each</c><00:03:31.030><c> other</c><00:03:31.239><c> and</c><00:03:31.540><c> that's</c><00:03:32.349><c> what</c>

00:03:32.550 --> 00:03:32.560 align:start position:0%
references to each other and that's what
 

00:03:32.560 --> 00:03:34.979 align:start position:0%
references to each other and that's what
we<00:03:32.709><c> have</c><00:03:32.859><c> to</c><00:03:33.010><c> eliminate</c><00:03:33.489><c> when</c><00:03:34.419><c> we</c><00:03:34.510><c> get</c><00:03:34.660><c> to</c><00:03:34.780><c> link</c>

00:03:34.979 --> 00:03:34.989 align:start position:0%
we have to eliminate when we get to link
 

00:03:34.989 --> 00:03:36.659 align:start position:0%
we have to eliminate when we get to link
list<00:03:35.200><c> C</c><00:03:35.470><c> we</c><00:03:36.010><c> have</c><00:03:36.129><c> to</c><00:03:36.190><c> change</c><00:03:36.459><c> those</c>

00:03:36.659 --> 00:03:36.669 align:start position:0%
list C we have to change those
 

00:03:36.669 --> 00:03:39.839 align:start position:0%
list C we have to change those
references<00:03:37.180><c> to</c><00:03:37.419><c> the</c><00:03:37.780><c> new</c><00:03:37.959><c> node</c><00:03:38.700><c> all</c><00:03:39.700><c> right</c>

00:03:39.839 --> 00:03:39.849 align:start position:0%
references to the new node all right
 

00:03:39.849 --> 00:03:41.280 align:start position:0%
references to the new node all right
let's<00:03:40.180><c> take</c><00:03:40.269><c> a</c><00:03:40.329><c> look</c><00:03:40.419><c> at</c><00:03:40.660><c> removing</c><00:03:41.079><c> a</c><00:03:41.139><c> note</c>

00:03:41.280 --> 00:03:41.290 align:start position:0%
let's take a look at removing a note
 

00:03:41.290 --> 00:03:43.259 align:start position:0%
let's take a look at removing a note
from<00:03:41.440><c> a</c><00:03:41.530><c> w-went</c><00:03:41.919><c> list</c><00:03:42.129><c> so</c><00:03:42.849><c> just</c><00:03:42.879><c> like</c>

00:03:43.259 --> 00:03:43.269 align:start position:0%
from a w-went list so just like
 

00:03:43.269 --> 00:03:45.119 align:start position:0%
from a w-went list so just like
inserting<00:03:43.870><c> we</c><00:03:44.290><c> have</c><00:03:44.410><c> a</c><00:03:44.440><c> general</c><00:03:44.949><c> utility</c>

00:03:45.119 --> 00:03:45.129 align:start position:0%
inserting we have a general utility
 

00:03:45.129 --> 00:03:47.879 align:start position:0%
inserting we have a general utility
remove<00:03:45.849><c> method</c><00:03:46.299><c> since</c><00:03:46.900><c> we're</c><00:03:47.109><c> using</c><00:03:47.139><c> Sentinel</c>

00:03:47.879 --> 00:03:47.889 align:start position:0%
remove method since we're using Sentinel
 

00:03:47.889 --> 00:03:49.680 align:start position:0%
remove method since we're using Sentinel
nodes<00:03:48.010><c> we're</c><00:03:48.549><c> always</c><00:03:48.730><c> going</c><00:03:49.030><c> to</c><00:03:49.090><c> be</c><00:03:49.150><c> removing</c>

00:03:49.680 --> 00:03:49.690 align:start position:0%
nodes we're always going to be removing
 

00:03:49.690 --> 00:03:52.050 align:start position:0%
nodes we're always going to be removing
a<00:03:49.750><c> node</c><00:03:49.959><c> between</c><00:03:50.560><c> two</c><00:03:50.829><c> other</c><00:03:50.980><c> nodes</c><00:03:51.250><c> so</c><00:03:51.849><c> let's</c>

00:03:52.050 --> 00:03:52.060 align:start position:0%
a node between two other nodes so let's
 

00:03:52.060 --> 00:03:54.960 align:start position:0%
a node between two other nodes so let's
look<00:03:52.180><c> at</c><00:03:52.389><c> linked</c><00:03:53.169><c> list</c><00:03:53.440><c> a</c><00:03:53.590><c> it's</c><00:03:54.579><c> just</c><00:03:54.699><c> this</c>

00:03:54.960 --> 00:03:54.970 align:start position:0%
look at linked list a it's just this
 

00:03:54.970 --> 00:03:56.849 align:start position:0%
look at linked list a it's just this
just<00:03:55.419><c> those</c><00:03:55.690><c> three</c><00:03:56.019><c> nodes</c><00:03:56.230><c> in</c><00:03:56.470><c> a</c><00:03:56.650><c> doubly</c>

00:03:56.849 --> 00:03:56.859 align:start position:0%
just those three nodes in a doubly
 

00:03:56.859 --> 00:03:59.159 align:start position:0%
just those three nodes in a doubly
linked<00:03:57.069><c> list</c><00:03:57.310><c> now</c><00:03:57.940><c> when</c><00:03:58.359><c> we</c><00:03:58.449><c> get</c><00:03:58.569><c> to</c><00:03:58.750><c> be</c><00:03:58.900><c> what</c>

00:03:59.159 --> 00:03:59.169 align:start position:0%
linked list now when we get to be what
 

00:03:59.169 --> 00:04:01.080 align:start position:0%
linked list now when we get to be what
we're<00:03:59.260><c> gonna</c><00:03:59.349><c> be</c><00:03:59.470><c> doing</c><00:03:59.769><c> is</c><00:03:59.980><c> removing</c><00:04:00.609><c> the</c>

00:04:01.080 --> 00:04:01.090 align:start position:0%
we're gonna be doing is removing the
 

00:04:01.090 --> 00:04:02.759 align:start position:0%
we're gonna be doing is removing the
middle<00:04:01.750><c> node</c><00:04:01.930><c> the</c><00:04:02.230><c> one</c><00:04:02.379><c> that's</c><00:04:02.530><c> holding</c><00:04:02.620><c> a</c>

00:04:02.759 --> 00:04:02.769 align:start position:0%
middle node the one that's holding a
 

00:04:02.769 --> 00:04:06.449 align:start position:0%
middle node the one that's holding a
value<00:04:03.040><c> of</c><00:04:03.129><c> two</c><00:04:04.410><c> so</c><00:04:05.410><c> what</c><00:04:05.590><c> we</c><00:04:05.650><c> need</c><00:04:05.799><c> to</c><00:04:05.829><c> do</c><00:04:06.010><c> here</c>

00:04:06.449 --> 00:04:06.459 align:start position:0%
value of two so what we need to do here
 

00:04:06.459 --> 00:04:08.189 align:start position:0%
value of two so what we need to do here
because<00:04:06.639><c> this</c><00:04:06.879><c> is</c><00:04:07.060><c> just</c><00:04:07.090><c> the</c><00:04:07.510><c> reverse</c><00:04:07.660><c> of</c>

00:04:08.189 --> 00:04:08.199 align:start position:0%
because this is just the reverse of
 

00:04:08.199 --> 00:04:10.860 align:start position:0%
because this is just the reverse of
whenever<00:04:08.889><c> we're</c><00:04:09.069><c> inserting</c><00:04:09.629><c> we</c><00:04:10.629><c> need</c><00:04:10.780><c> to</c>

00:04:10.860 --> 00:04:10.870 align:start position:0%
whenever we're inserting we need to
 

00:04:10.870 --> 00:04:15.809 align:start position:0%
whenever we're inserting we need to
change<00:04:12.030><c> the</c><00:04:13.049><c> references</c><00:04:14.049><c> of</c><00:04:14.260><c> the</c><00:04:15.040><c> node</c><00:04:15.250><c> before</c>

00:04:15.809 --> 00:04:15.819 align:start position:0%
change the references of the node before
 

00:04:15.819 --> 00:04:18.659 align:start position:0%
change the references of the node before
and<00:04:16.090><c> after</c><00:04:16.889><c> the</c><00:04:17.889><c> nodes</c><00:04:18.099><c> that</c><00:04:18.250><c> we're</c><00:04:18.400><c> removing</c>

00:04:18.659 --> 00:04:18.669 align:start position:0%
and after the nodes that we're removing
 

00:04:18.669 --> 00:04:21.539 align:start position:0%
and after the nodes that we're removing
okay<00:04:19.620><c> so</c><00:04:20.620><c> if</c><00:04:20.979><c> we</c><00:04:21.130><c> take</c><00:04:21.250><c> a</c><00:04:21.310><c> look</c><00:04:21.400><c> at</c><00:04:21.489><c> the</c>

00:04:21.539 --> 00:04:21.549 align:start position:0%
okay so if we take a look at the
 

00:04:21.549 --> 00:04:24.060 align:start position:0%
okay so if we take a look at the
algorithm<00:04:21.820><c> all</c><00:04:22.240><c> we're</c><00:04:22.630><c> gonna</c><00:04:22.719><c> pass</c><00:04:22.990><c> in</c><00:04:23.260><c> is</c><00:04:23.500><c> the</c>

00:04:24.060 --> 00:04:24.070 align:start position:0%
algorithm all we're gonna pass in is the
 

00:04:24.070 --> 00:04:26.020 align:start position:0%
algorithm all we're gonna pass in is the
node<00:04:24.310><c> that</c><00:04:24.580><c> we</c><00:04:24.849><c> want</c><00:04:25.030><c> to</c><00:04:25.090><c> remove</c>

00:04:26.020 --> 00:04:26.030 align:start position:0%
node that we want to remove
 

00:04:26.030 --> 00:04:28.360 align:start position:0%
node that we want to remove
so<00:04:26.690><c> this</c><00:04:26.840><c> is</c><00:04:27.020><c> requires</c><00:04:27.620><c> a</c><00:04:27.710><c> couple</c><00:04:28.010><c> more</c><00:04:28.100><c> steps</c>

00:04:28.360 --> 00:04:28.370 align:start position:0%
so this is requires a couple more steps
 

00:04:28.370 --> 00:04:31.090 align:start position:0%
so this is requires a couple more steps
but<00:04:29.030><c> what</c><00:04:30.020><c> we're</c><00:04:30.139><c> going</c><00:04:30.170><c> to</c><00:04:30.320><c> be</c><00:04:30.380><c> using</c><00:04:30.830><c> is</c>

00:04:31.090 --> 00:04:31.100 align:start position:0%
but what we're going to be using is
 

00:04:31.100 --> 00:04:32.320 align:start position:0%
but what we're going to be using is
what's<00:04:31.370><c> called</c><00:04:31.520><c> a</c><00:04:31.610><c> predecessor</c><00:04:31.910><c> and</c>

00:04:32.320 --> 00:04:32.330 align:start position:0%
what's called a predecessor and
 

00:04:32.330 --> 00:04:34.659 align:start position:0%
what's called a predecessor and
successor<00:04:32.680><c> processor</c><00:04:33.680><c> is</c><00:04:33.830><c> what's</c><00:04:34.010><c> before</c><00:04:34.580><c> the</c>

00:04:34.659 --> 00:04:34.669 align:start position:0%
successor processor is what's before the
 

00:04:34.669 --> 00:04:36.820 align:start position:0%
successor processor is what's before the
node<00:04:34.850><c> successors</c><00:04:35.570><c> after</c><00:04:35.780><c> it</c><00:04:36.050><c> so</c><00:04:36.560><c> we're</c><00:04:36.740><c> gonna</c>

00:04:36.820 --> 00:04:36.830 align:start position:0%
node successors after it so we're gonna
 

00:04:36.830 --> 00:04:38.920 align:start position:0%
node successors after it so we're gonna
say<00:04:37.010><c> predecessor</c><00:04:37.940><c> is</c><00:04:38.150><c> equal</c><00:04:38.419><c> the</c><00:04:38.660><c> node</c><00:04:38.810><c> will</c>

00:04:38.920 --> 00:04:38.930 align:start position:0%
say predecessor is equal the node will
 

00:04:38.930 --> 00:04:40.530 align:start position:0%
say predecessor is equal the node will
trying<00:04:39.110><c> to</c><00:04:39.169><c> move</c>

00:04:40.530 --> 00:04:40.540 align:start position:0%
trying to move
 

00:04:40.540 --> 00:04:43.300 align:start position:0%
trying to move
what's<00:04:41.540><c> its</c><00:04:41.750><c> previous</c><00:04:42.230><c> node</c><00:04:42.440><c> so</c><00:04:42.770><c> node</c><00:04:42.980><c> get</c>

00:04:43.300 --> 00:04:43.310 align:start position:0%
what's its previous node so node get
 

00:04:43.310 --> 00:04:45.520 align:start position:0%
what's its previous node so node get
proved<00:04:43.610><c> is</c><00:04:43.900><c> what</c><00:04:44.900><c> we're</c><00:04:45.020><c> setting</c><00:04:45.200><c> to</c><00:04:45.440><c> the</c>

00:04:45.520 --> 00:04:45.530 align:start position:0%
proved is what we're setting to the
 

00:04:45.530 --> 00:04:47.890 align:start position:0%
proved is what we're setting to the
processor<00:04:45.980><c> variable</c><00:04:46.340><c> successors</c><00:04:47.300><c> the</c><00:04:47.690><c> same</c>

00:04:47.890 --> 00:04:47.900 align:start position:0%
processor variable successors the same
 

00:04:47.900 --> 00:04:49.030 align:start position:0%
processor variable successors the same
thing<00:04:48.110><c> he</c><00:04:48.320><c> said</c><00:04:48.470><c> there's</c><00:04:48.590><c> going</c><00:04:48.680><c> to</c><00:04:48.740><c> be</c><00:04:48.860><c> the</c>

00:04:49.030 --> 00:04:49.040 align:start position:0%
thing he said there's going to be the
 

00:04:49.040 --> 00:04:51.370 align:start position:0%
thing he said there's going to be the
note<00:04:49.250><c> after</c><00:04:49.870><c> the</c><00:04:50.870><c> node</c><00:04:51.020><c> we're</c><00:04:51.139><c> trying</c><00:04:51.320><c> to</c>

00:04:51.370 --> 00:04:51.380 align:start position:0%
note after the node we're trying to
 

00:04:51.380 --> 00:04:53.920 align:start position:0%
note after the node we're trying to
remove<00:04:51.590><c> okay</c><00:04:52.220><c> and</c><00:04:52.510><c> the</c><00:04:53.510><c> reason</c><00:04:53.690><c> we're</c><00:04:53.810><c> doing</c>

00:04:53.920 --> 00:04:53.930 align:start position:0%
remove okay and the reason we're doing
 

00:04:53.930 --> 00:04:55.900 align:start position:0%
remove okay and the reason we're doing
this<00:04:54.230><c> is</c><00:04:54.410><c> because</c><00:04:54.710><c> we</c><00:04:54.919><c> need</c><00:04:55.250><c> to</c><00:04:55.340><c> change</c><00:04:55.580><c> their</c>

00:04:55.900 --> 00:04:55.910 align:start position:0%
this is because we need to change their
 

00:04:55.910 --> 00:04:57.970 align:start position:0%
this is because we need to change their
references<00:04:56.419><c> to</c><00:04:57.020><c> eliminate</c><00:04:57.470><c> them</c><00:04:57.680><c> from</c>

00:04:57.970 --> 00:04:57.980 align:start position:0%
references to eliminate them from
 

00:04:57.980 --> 00:04:59.680 align:start position:0%
references to eliminate them from
knowing<00:04:58.190><c> the</c><00:04:58.760><c> node</c><00:04:58.970><c> we're</c><00:04:59.120><c> trying</c><00:04:59.330><c> to</c><00:04:59.390><c> remove</c>

00:04:59.680 --> 00:04:59.690 align:start position:0%
knowing the node we're trying to remove
 

00:04:59.690 --> 00:05:02.890 align:start position:0%
knowing the node we're trying to remove
so<00:05:00.550><c> the</c><00:05:01.550><c> node</c><00:05:01.760><c> before</c><00:05:01.970><c> the</c><00:05:02.540><c> no</c><00:05:02.660><c> we're</c><00:05:02.780><c> trying</c>

00:05:02.890 --> 00:05:02.900 align:start position:0%
so the node before the no we're trying
 

00:05:02.900 --> 00:05:04.600 align:start position:0%
so the node before the no we're trying
to<00:05:02.990><c> remove</c><00:05:03.200><c> we're</c><00:05:03.710><c> set</c><00:05:03.950><c> its</c><00:05:04.100><c> next</c><00:05:04.460><c> reference</c>

00:05:04.600 --> 00:05:04.610 align:start position:0%
to remove we're set its next reference
 

00:05:04.610 --> 00:05:08.800 align:start position:0%
to remove we're set its next reference
to<00:05:05.600><c> the</c><00:05:05.720><c> successor</c><00:05:06.260><c> which</c><00:05:06.680><c> is</c><00:05:06.890><c> the</c><00:05:07.160><c> node</c><00:05:07.880><c> after</c>

00:05:08.800 --> 00:05:08.810 align:start position:0%
to the successor which is the node after
 

00:05:08.810 --> 00:05:11.050 align:start position:0%
to the successor which is the node after
the<00:05:09.410><c> removal</c><00:05:09.680><c> other</c><00:05:10.400><c> node</c><00:05:10.610><c> we</c><00:05:10.700><c> want</c><00:05:10.880><c> to</c><00:05:10.940><c> take</c>

00:05:11.050 --> 00:05:11.060 align:start position:0%
the removal other node we want to take
 

00:05:11.060 --> 00:05:15.190 align:start position:0%
the removal other node we want to take
care<00:05:11.090><c> of</c><00:05:11.300><c> and</c><00:05:12.790><c> so</c><00:05:13.790><c> it's</c><00:05:13.940><c> dub</c><00:05:14.120><c> before</c><00:05:14.450><c> the</c>

00:05:15.190 --> 00:05:15.200 align:start position:0%
care of and so it's dub before the
 

00:05:15.200 --> 00:05:18.490 align:start position:0%
care of and so it's dub before the
successor<00:05:15.740><c> or</c><00:05:16.010><c> I</c><00:05:16.040><c> says</c><00:05:16.280><c> previous</c><00:05:16.760><c> node</c><00:05:17.419><c> to</c><00:05:18.110><c> the</c>

00:05:18.490 --> 00:05:18.500 align:start position:0%
successor or I says previous node to the
 

00:05:18.500 --> 00:05:20.409 align:start position:0%
successor or I says previous node to the
node<00:05:18.650><c> before</c><00:05:19.130><c> the</c><00:05:19.760><c> one</c><00:05:19.940><c> that</c><00:05:19.970><c> we</c><00:05:20.210><c> want</c><00:05:20.360><c> to</c>

00:05:20.409 --> 00:05:20.419 align:start position:0%
node before the one that we want to
 

00:05:20.419 --> 00:05:23.290 align:start position:0%
node before the one that we want to
remove<00:05:20.740><c> and</c><00:05:21.740><c> of</c><00:05:22.669><c> course</c><00:05:22.880><c> we</c><00:05:23.030><c> have</c><00:05:23.150><c> to</c>

00:05:23.290 --> 00:05:23.300 align:start position:0%
remove and of course we have to
 

00:05:23.300 --> 00:05:26.530 align:start position:0%
remove and of course we have to
decrement<00:05:23.720><c> the</c><00:05:23.810><c> size</c><00:05:23.870><c> and</c><00:05:24.410><c> then</c><00:05:24.790><c> this</c><00:05:25.790><c> this</c>

00:05:26.530 --> 00:05:26.540 align:start position:0%
decrement the size and then this this
 

00:05:26.540 --> 00:05:28.480 align:start position:0%
decrement the size and then this this
algorithm<00:05:26.930><c> is</c><00:05:26.990><c> going</c><00:05:27.050><c> to</c><00:05:27.169><c> return</c><00:05:27.530><c> the</c><00:05:28.460><c> value</c>

00:05:28.480 --> 00:05:28.490 align:start position:0%
algorithm is going to return the value
 

00:05:28.490 --> 00:05:30.940 align:start position:0%
algorithm is going to return the value
of<00:05:29.210><c> the</c><00:05:29.960><c> note</c><00:05:30.169><c> that</c><00:05:30.350><c> we're</c><00:05:30.470><c> trying</c><00:05:30.620><c> to</c><00:05:30.740><c> remove</c>

00:05:30.940 --> 00:05:30.950 align:start position:0%
of the note that we're trying to remove
 

00:05:30.950 --> 00:05:34.450 align:start position:0%
of the note that we're trying to remove
all<00:05:31.820><c> right</c><00:05:32.080><c> so</c><00:05:33.080><c> if</c><00:05:33.590><c> we</c><00:05:33.680><c> go</c><00:05:33.800><c> back</c><00:05:33.860><c> to</c><00:05:34.010><c> link</c><00:05:34.280><c> list</c>

00:05:34.450 --> 00:05:34.460 align:start position:0%
all right so if we go back to link list
 

00:05:34.460 --> 00:05:37.600 align:start position:0%
all right so if we go back to link list
B<00:05:34.669><c> this</c><00:05:35.330><c> is</c><00:05:35.510><c> exactly</c><00:05:35.810><c> what</c><00:05:35.990><c> we're</c><00:05:36.110><c> doing</c><00:05:36.610><c> we're</c>

00:05:37.600 --> 00:05:37.610 align:start position:0%
B this is exactly what we're doing we're
 

00:05:37.610 --> 00:05:39.370 align:start position:0%
B this is exactly what we're doing we're
changing<00:05:37.910><c> the</c><00:05:37.970><c> references</c><00:05:38.390><c> to</c><00:05:39.050><c> the</c><00:05:39.080><c> node</c>

00:05:39.370 --> 00:05:39.380 align:start position:0%
changing the references to the node
 

00:05:39.380 --> 00:05:42.969 align:start position:0%
changing the references to the node
before<00:05:39.830><c> and</c><00:05:40.040><c> after</c><00:05:41.320><c> the</c><00:05:42.320><c> node</c><00:05:42.650><c> we</c><00:05:42.770><c> want</c><00:05:42.919><c> to</c>

00:05:42.969 --> 00:05:42.979 align:start position:0%
before and after the node we want to
 

00:05:42.979 --> 00:05:45.090 align:start position:0%
before and after the node we want to
remove<00:05:43.190><c> and</c><00:05:43.460><c> then</c><00:05:44.090><c> we</c><00:05:44.180><c> get</c><00:05:44.270><c> to</c><00:05:44.390><c> link</c><00:05:44.570><c> list</c><00:05:44.780><c> C</c>

00:05:45.090 --> 00:05:45.100 align:start position:0%
remove and then we get to link list C
 

00:05:45.100 --> 00:05:48.490 align:start position:0%
remove and then we get to link list C
since<00:05:46.100><c> there's</c><00:05:46.310><c> references</c><00:05:46.820><c> to</c><00:05:47.229><c> that</c><00:05:48.229><c> no</c><00:05:48.380><c> we</c>

00:05:48.490 --> 00:05:48.500 align:start position:0%
since there's references to that no we
 

00:05:48.500 --> 00:05:50.529 align:start position:0%
since there's references to that no we
want<00:05:48.620><c> to</c><00:05:48.650><c> remove</c><00:05:48.860><c> don't</c><00:05:49.190><c> exist</c><00:05:49.430><c> anymore</c><00:05:49.610><c> it's</c>

00:05:50.529 --> 00:05:50.539 align:start position:0%
want to remove don't exist anymore it's
 

00:05:50.539 --> 00:05:52.900 align:start position:0%
want to remove don't exist anymore it's
gone<00:05:50.810><c> all</c><00:05:51.229><c> right</c><00:05:51.440><c> okay</c><00:05:52.250><c> so</c><00:05:52.310><c> let's</c><00:05:52.610><c> finish</c><00:05:52.729><c> up</c>

00:05:52.900 --> 00:05:52.910 align:start position:0%
gone all right okay so let's finish up
 

00:05:52.910 --> 00:05:55.090 align:start position:0%
gone all right okay so let's finish up
with<00:05:52.970><c> Big</c><00:05:53.240><c> O</c><00:05:53.330><c> notation</c><00:05:53.510><c> so</c><00:05:54.350><c> again</c><00:05:54.710><c> this</c><00:05:54.919><c> is</c>

00:05:55.090 --> 00:05:55.100 align:start position:0%
with Big O notation so again this is
 

00:05:55.100 --> 00:05:59.050 align:start position:0%
with Big O notation so again this is
just<00:05:56.169><c> what's</c><00:05:57.169><c> the</c><00:05:57.289><c> time</c><00:05:57.650><c> to</c><00:05:57.890><c> perform</c><00:05:58.310><c> the</c>

00:05:59.050 --> 00:05:59.060 align:start position:0%
just what's the time to perform the
 

00:05:59.060 --> 00:06:01.300 align:start position:0%
just what's the time to perform the
algorithm<00:05:59.660><c> as</c><00:05:59.840><c> it</c><00:06:00.470><c> grows</c><00:06:00.710><c> with</c><00:06:00.860><c> the</c><00:06:00.979><c> size</c><00:06:01.190><c> of</c>

00:06:01.300 --> 00:06:01.310 align:start position:0%
algorithm as it grows with the size of
 

00:06:01.310 --> 00:06:01.690 align:start position:0%
algorithm as it grows with the size of
the<00:06:01.400><c> input</c>

00:06:01.690 --> 00:06:01.700 align:start position:0%
the input
 

00:06:01.700 --> 00:06:03.909 align:start position:0%
the input
that's<00:06:02.360><c> called</c><00:06:02.539><c> time</c><00:06:02.810><c> complexity</c><00:06:03.229><c> we're</c><00:06:03.800><c> also</c>

00:06:03.909 --> 00:06:03.919 align:start position:0%
that's called time complexity we're also
 

00:06:03.919 --> 00:06:05.200 align:start position:0%
that's called time complexity we're also
gonna<00:06:04.070><c> be</c><00:06:04.160><c> looking</c><00:06:04.400><c> at</c><00:06:04.490><c> the</c><00:06:04.610><c> space</c><00:06:04.789><c> complexity</c>

00:06:05.200 --> 00:06:05.210 align:start position:0%
gonna be looking at the space complexity
 

00:06:05.210 --> 00:06:08.590 align:start position:0%
gonna be looking at the space complexity
as<00:06:05.360><c> well</c><00:06:05.620><c> as</c><00:06:06.620><c> the</c><00:06:06.919><c> at</c><00:06:07.039><c> the</c><00:06:07.190><c> end</c><00:06:07.340><c> so</c><00:06:08.330><c> we're</c><00:06:08.539><c> only</c>

00:06:08.590 --> 00:06:08.600 align:start position:0%
as well as the at the end so we're only
 

00:06:08.600 --> 00:06:10.930 align:start position:0%
as well as the at the end so we're only
we<00:06:09.050><c> only</c><00:06:09.169><c> care</c><00:06:09.470><c> about</c><00:06:09.500><c> average</c><00:06:09.979><c> and</c><00:06:10.130><c> worst</c><00:06:10.340><c> we</c>

00:06:10.930 --> 00:06:10.940 align:start position:0%
we only care about average and worst we
 

00:06:10.940 --> 00:06:13.330 align:start position:0%
we only care about average and worst we
don't<00:06:11.090><c> care</c><00:06:11.210><c> about</c><00:06:11.240><c> best</c><00:06:11.600><c> so</c><00:06:12.590><c> let's</c><00:06:13.100><c> take</c><00:06:13.280><c> a</c>

00:06:13.330 --> 00:06:13.340 align:start position:0%
don't care about best so let's take a
 

00:06:13.340 --> 00:06:14.770 align:start position:0%
don't care about best so let's take a
look<00:06:13.460><c> at</c><00:06:13.550><c> accessing</c><00:06:13.940><c> and</c><00:06:14.240><c> searching</c><00:06:14.270><c> first</c>

00:06:14.770 --> 00:06:14.780 align:start position:0%
look at accessing and searching first
 

00:06:14.780 --> 00:06:16.710 align:start position:0%
look at accessing and searching first
accessing<00:06:15.590><c> is</c><00:06:15.680><c> the</c><00:06:15.740><c> same</c><00:06:15.890><c> as</c><00:06:16.010><c> indexing</c><00:06:16.490><c> and</c>

00:06:16.710 --> 00:06:16.720 align:start position:0%
accessing is the same as indexing and
 

00:06:16.720 --> 00:06:19.810 align:start position:0%
accessing is the same as indexing and
just<00:06:17.720><c> like</c><00:06:17.840><c> blink</c><00:06:18.140><c> less</c><00:06:18.289><c> in</c><00:06:18.440><c> general</c><00:06:18.820><c> because</c>

00:06:19.810 --> 00:06:19.820 align:start position:0%
just like blink less in general because
 

00:06:19.820 --> 00:06:22.120 align:start position:0%
just like blink less in general because
they're<00:06:20.030><c> not</c><00:06:20.120><c> stored</c><00:06:20.539><c> in</c><00:06:20.870><c> contiguous</c><00:06:21.320><c> memory</c>

00:06:22.120 --> 00:06:22.130 align:start position:0%
they're not stored in contiguous memory
 

00:06:22.130 --> 00:06:26.290 align:start position:0%
they're not stored in contiguous memory
they<00:06:22.760><c> can't</c><00:06:23.060><c> be</c><00:06:23.210><c> indexed</c><00:06:24.460><c> so</c><00:06:25.460><c> whenever</c><00:06:26.180><c> we're</c>

00:06:26.290 --> 00:06:26.300 align:start position:0%
they can't be indexed so whenever we're
 

00:06:26.300 --> 00:06:28.200 align:start position:0%
they can't be indexed so whenever we're
accessing<00:06:26.900><c> we</c><00:06:27.440><c> still</c><00:06:27.590><c> have</c><00:06:27.680><c> to</c><00:06:27.770><c> traverse</c>

00:06:28.200 --> 00:06:28.210 align:start position:0%
accessing we still have to traverse
 

00:06:28.210 --> 00:06:30.430 align:start position:0%
accessing we still have to traverse
which<00:06:29.210><c> doesn't</c><00:06:29.510><c> reverse</c><00:06:29.690><c> the</c><00:06:29.900><c> list</c><00:06:30.140><c> in</c><00:06:30.350><c> order</c>

00:06:30.430 --> 00:06:30.440 align:start position:0%
which doesn't reverse the list in order
 

00:06:30.440 --> 00:06:32.740 align:start position:0%
which doesn't reverse the list in order
to<00:06:30.620><c> find</c><00:06:30.890><c> the</c><00:06:31.490><c> node</c><00:06:31.729><c> that</c><00:06:31.760><c> we</c><00:06:32.000><c> want</c><00:06:32.240><c> all</c><00:06:32.570><c> right</c>

00:06:32.740 --> 00:06:32.750 align:start position:0%
to find the node that we want all right
 

00:06:32.750 --> 00:06:33.969 align:start position:0%
to find the node that we want all right
we<00:06:32.870><c> have</c><00:06:32.960><c> to</c><00:06:33.050><c> traverse</c><00:06:33.169><c> through</c><00:06:33.440><c> the</c><00:06:33.500><c> pointers</c>

00:06:33.969 --> 00:06:33.979 align:start position:0%
we have to traverse through the pointers
 

00:06:33.979 --> 00:06:36.460 align:start position:0%
we have to traverse through the pointers
in<00:06:34.220><c> memory</c><00:06:34.909><c> to</c><00:06:35.210><c> get</c><00:06:35.599><c> to</c><00:06:35.720><c> the</c><00:06:35.840><c> node</c><00:06:36.020><c> that</c><00:06:36.289><c> hold</c>

00:06:36.460 --> 00:06:36.470 align:start position:0%
in memory to get to the node that hold
 

00:06:36.470 --> 00:06:38.399 align:start position:0%
in memory to get to the node that hold
the<00:06:36.560><c> value</c><00:06:36.860><c> that</c><00:06:37.220><c> we</c><00:06:37.340><c> care</c><00:06:37.520><c> about</c>

00:06:38.399 --> 00:06:38.409 align:start position:0%
the value that we care about
 

00:06:38.409 --> 00:06:39.629 align:start position:0%
the value that we care about
and<00:06:38.529><c> the</c><00:06:38.679><c> same</c><00:06:38.800><c> thing</c><00:06:38.979><c> for</c><00:06:39.099><c> searching</c><00:06:39.429><c> so</c>

00:06:39.629 --> 00:06:39.639 align:start position:0%
and the same thing for searching so
 

00:06:39.639 --> 00:06:42.809 align:start position:0%
and the same thing for searching so
these<00:06:40.629><c> are</c><00:06:40.779><c> Big</c><00:06:40.929><c> O</c><00:06:41.050><c> of</c><00:06:41.229><c> N</c><00:06:41.379><c> or</c><00:06:41.739><c> linear</c><00:06:42.610><c> time</c>

00:06:42.809 --> 00:06:42.819 align:start position:0%
these are Big O of N or linear time
 

00:06:42.819 --> 00:06:46.669 align:start position:0%
these are Big O of N or linear time
because<00:06:44.129><c> the</c><00:06:45.129><c> time</c><00:06:45.399><c> it</c><00:06:45.879><c> takes</c><00:06:46.179><c> to</c><00:06:46.300><c> perform</c>

00:06:46.669 --> 00:06:46.679 align:start position:0%
because the time it takes to perform
 

00:06:46.679 --> 00:06:50.309 align:start position:0%
because the time it takes to perform
these<00:06:47.679><c> algorithms</c><00:06:48.069><c> is</c><00:06:48.809><c> proportional</c><00:06:49.809><c> to</c><00:06:49.839><c> the</c>

00:06:50.309 --> 00:06:50.319 align:start position:0%
these algorithms is proportional to the
 

00:06:50.319 --> 00:06:53.299 align:start position:0%
these algorithms is proportional to the
size<00:06:50.589><c> of</c><00:06:50.860><c> the</c><00:06:51.009><c> input</c><00:06:51.129><c> as</c><00:06:51.489><c> it</c><00:06:51.729><c> increases</c><00:06:52.059><c> okay</c>

00:06:53.299 --> 00:06:53.309 align:start position:0%
size of the input as it increases okay
 

00:06:53.309 --> 00:06:55.679 align:start position:0%
size of the input as it increases okay
so<00:06:54.309><c> if</c><00:06:54.610><c> we</c><00:06:54.729><c> look</c><00:06:54.759><c> take</c><00:06:55.089><c> a</c><00:06:55.149><c> look</c><00:06:55.300><c> at</c><00:06:55.360><c> insertion</c>

00:06:55.679 --> 00:06:55.689 align:start position:0%
so if we look take a look at insertion
 

00:06:55.689 --> 00:06:59.549 align:start position:0%
so if we look take a look at insertion
and<00:06:55.929><c> deletion</c><00:06:56.039><c> next</c><00:06:57.269><c> insertion</c><00:06:58.269><c> whenever</c><00:06:59.259><c> we</c>

00:06:59.549 --> 00:06:59.559 align:start position:0%
and deletion next insertion whenever we
 

00:06:59.559 --> 00:07:03.179 align:start position:0%
and deletion next insertion whenever we
find<00:07:00.519><c> the</c><00:07:01.300><c> node</c><00:07:01.539><c> when</c><00:07:02.139><c> we</c><00:07:02.229><c> find</c><00:07:02.439><c> the</c><00:07:02.589><c> place</c><00:07:02.830><c> in</c>

00:07:03.179 --> 00:07:03.189 align:start position:0%
find the node when we find the place in
 

00:07:03.189 --> 00:07:04.499 align:start position:0%
find the node when we find the place in
the<00:07:03.459><c> linked</c><00:07:03.610><c> list</c><00:07:03.669><c> where</c><00:07:03.939><c> you</c><00:07:03.999><c> want</c><00:07:04.149><c> to</c><00:07:04.209><c> insert</c>

00:07:04.499 --> 00:07:04.509 align:start position:0%
the linked list where you want to insert
 

00:07:04.509 --> 00:07:06.689 align:start position:0%
the linked list where you want to insert
a<00:07:04.569><c> node</c><00:07:04.809><c> it's</c><00:07:05.499><c> always</c><00:07:05.800><c> going</c><00:07:05.919><c> to</c><00:07:05.979><c> be</c><00:07:06.039><c> Big</c><00:07:06.519><c> O</c><00:07:06.550><c> of</c>

00:07:06.689 --> 00:07:06.699 align:start position:0%
a node it's always going to be Big O of
 

00:07:06.699 --> 00:07:12.629 align:start position:0%
a node it's always going to be Big O of
1<00:07:07.529><c> because</c><00:07:08.999><c> every</c><00:07:09.999><c> node</c><00:07:10.239><c> has</c><00:07:10.269><c> a</c><00:07:10.629><c> reference</c><00:07:11.639><c> to</c>

00:07:12.629 --> 00:07:12.639 align:start position:0%
1 because every node has a reference to
 

00:07:12.639 --> 00:07:14.459 align:start position:0%
1 because every node has a reference to
its<00:07:12.759><c> predecessor</c><00:07:13.300><c> and</c><00:07:13.449><c> its</c><00:07:13.869><c> successor</c><00:07:14.289><c> so</c>

00:07:14.459 --> 00:07:14.469 align:start position:0%
its predecessor and its successor so
 

00:07:14.469 --> 00:07:16.859 align:start position:0%
its predecessor and its successor so
before<00:07:14.889><c> and</c><00:07:15.129><c> after</c><00:07:15.459><c> it</c><00:07:15.550><c> so</c><00:07:16.360><c> it's</c><00:07:16.539><c> easy</c><00:07:16.779><c> to</c>

00:07:16.859 --> 00:07:16.869 align:start position:0%
before and after it so it's easy to
 

00:07:16.869 --> 00:07:18.689 align:start position:0%
before and after it so it's easy to
manipulate<00:07:17.169><c> the</c><00:07:17.439><c> references</c><00:07:17.919><c> when</c><00:07:18.399><c> we</c><00:07:18.519><c> know</c>

00:07:18.689 --> 00:07:18.699 align:start position:0%
manipulate the references when we know
 

00:07:18.699 --> 00:07:21.149 align:start position:0%
manipulate the references when we know
the<00:07:19.539><c> surrounding</c><00:07:20.079><c> nodes</c><00:07:20.259><c> from</c><00:07:20.679><c> the</c><00:07:20.860><c> one</c><00:07:21.039><c> we</c>

00:07:21.149 --> 00:07:21.159 align:start position:0%
the surrounding nodes from the one we
 

00:07:21.159 --> 00:07:24.329 align:start position:0%
the surrounding nodes from the one we
want<00:07:21.339><c> to</c><00:07:21.399><c> insert</c><00:07:21.759><c> or</c><00:07:22.029><c> remove</c><00:07:22.059><c> ok</c><00:07:22.839><c> so</c><00:07:23.499><c> insertion</c>

00:07:24.329 --> 00:07:24.339 align:start position:0%
want to insert or remove ok so insertion
 

00:07:24.339 --> 00:07:27.119 align:start position:0%
want to insert or remove ok so insertion
and<00:07:24.579><c> deletion</c><00:07:24.610><c> have</c><00:07:25.409><c> constant</c><00:07:26.409><c> times</c><00:07:26.619><c> for</c>

00:07:27.119 --> 00:07:27.129 align:start position:0%
and deletion have constant times for
 

00:07:27.129 --> 00:07:28.559 align:start position:0%
and deletion have constant times for
their<00:07:27.219><c> operations</c><00:07:27.729><c> and</c><00:07:27.879><c> doubly</c><00:07:28.179><c> linked</c><00:07:28.360><c> lists</c>

00:07:28.559 --> 00:07:28.569 align:start position:0%
their operations and doubly linked lists
 

00:07:28.569 --> 00:07:31.829 align:start position:0%
their operations and doubly linked lists
because<00:07:29.379><c> when</c><00:07:29.800><c> we</c><00:07:29.919><c> find</c><00:07:30.219><c> it</c><00:07:30.399><c> even</c><00:07:31.329><c> though</c><00:07:31.629><c> it</c>

00:07:31.829 --> 00:07:31.839 align:start position:0%
because when we find it even though it
 

00:07:31.839 --> 00:07:33.299 align:start position:0%
because when we find it even though it
can<00:07:32.019><c> be</c><00:07:32.050><c> confusing</c><00:07:32.469><c> because</c><00:07:32.679><c> in</c><00:07:32.979><c> order</c><00:07:33.159><c> to</c>

00:07:33.299 --> 00:07:33.309 align:start position:0%
can be confusing because in order to
 

00:07:33.309 --> 00:07:36.239 align:start position:0%
can be confusing because in order to
find<00:07:33.550><c> it</c><00:07:33.729><c> is</c><00:07:34.419><c> Big</c><00:07:34.719><c> O</c><00:07:34.839><c> of</c><00:07:34.869><c> n</c><00:07:35.079><c> but</c><00:07:35.829><c> in</c><00:07:36.009><c> order</c><00:07:36.189><c> to</c>

00:07:36.239 --> 00:07:36.249 align:start position:0%
find it is Big O of n but in order to
 

00:07:36.249 --> 00:07:38.249 align:start position:0%
find it is Big O of n but in order to
actually<00:07:36.459><c> execute</c><00:07:37.119><c> this</c><00:07:37.479><c> operation</c><00:07:37.689><c> for</c>

00:07:38.249 --> 00:07:38.259 align:start position:0%
actually execute this operation for
 

00:07:38.259 --> 00:07:40.109 align:start position:0%
actually execute this operation for
inserting<00:07:38.589><c> or</c><00:07:38.739><c> deleting</c><00:07:38.769><c> it's</c><00:07:39.639><c> only</c><00:07:39.789><c> a</c><00:07:39.879><c> Big</c><00:07:40.029><c> O</c>

00:07:40.109 --> 00:07:40.119 align:start position:0%
inserting or deleting it's only a Big O
 

00:07:40.119 --> 00:07:44.429 align:start position:0%
inserting or deleting it's only a Big O
of<00:07:40.239><c> 1</c><00:07:40.889><c> again</c><00:07:41.889><c> because</c><00:07:42.610><c> we</c><00:07:42.999><c> know</c><00:07:43.289><c> we</c><00:07:44.289><c> have</c>

00:07:44.429 --> 00:07:44.439 align:start position:0%
of 1 again because we know we have
 

00:07:44.439 --> 00:07:47.329 align:start position:0%
of 1 again because we know we have
pointers<00:07:44.860><c> to</c><00:07:45.729><c> the</c><00:07:45.849><c> nodes</c><00:07:46.059><c> before</c><00:07:46.479><c> and</c><00:07:46.749><c> after</c>

00:07:47.329 --> 00:07:47.339 align:start position:0%
pointers to the nodes before and after
 

00:07:47.339 --> 00:07:51.259 align:start position:0%
pointers to the nodes before and after
whether<00:07:48.339><c> we're</c><00:07:48.610><c> inserting</c><00:07:49.089><c> or</c><00:07:49.419><c> deleting</c><00:07:49.499><c> ok</c>

00:07:51.259 --> 00:07:51.269 align:start position:0%
whether we're inserting or deleting ok
 

00:07:51.269 --> 00:07:53.609 align:start position:0%
whether we're inserting or deleting ok
now<00:07:52.269><c> the</c><00:07:52.329><c> same</c><00:07:52.599><c> goes</c><00:07:52.749><c> for</c><00:07:52.779><c> worst</c><00:07:53.169><c> case</c><00:07:53.409><c> the</c>

00:07:53.609 --> 00:07:53.619 align:start position:0%
now the same goes for worst case the
 

00:07:53.619 --> 00:07:54.839 align:start position:0%
now the same goes for worst case the
worst<00:07:53.769><c> case</c><00:07:53.979><c> is</c><00:07:54.159><c> we</c><00:07:54.279><c> have</c><00:07:54.399><c> to</c><00:07:54.459><c> traverse</c><00:07:54.729><c> the</c>

00:07:54.839 --> 00:07:54.849 align:start position:0%
worst case is we have to traverse the
 

00:07:54.849 --> 00:07:56.699 align:start position:0%
worst case is we have to traverse the
whole<00:07:54.909><c> list</c><00:07:55.179><c> which</c><00:07:55.990><c> is</c><00:07:56.110><c> just</c><00:07:56.259><c> big</c><00:07:56.379><c> event</c>

00:07:56.699 --> 00:07:56.709 align:start position:0%
whole list which is just big event
 

00:07:56.709 --> 00:07:58.739 align:start position:0%
whole list which is just big event
anyways<00:07:57.069><c> and</c><00:07:57.789><c> but</c><00:07:58.059><c> inserting</c><00:07:58.599><c> and</c><00:07:58.719><c> deleting</c>

00:07:58.739 --> 00:07:58.749 align:start position:0%
anyways and but inserting and deleting
 

00:07:58.749 --> 00:08:00.839 align:start position:0%
anyways and but inserting and deleting
are<00:07:59.169><c> still</c><00:07:59.349><c> going</c><00:07:59.439><c> a</c><00:07:59.529><c> Big</c><00:07:59.679><c> O</c><00:07:59.709><c> of</c><00:07:59.800><c> 1</c><00:08:00.039><c> no</c><00:08:00.699><c> matter</c>

00:08:00.839 --> 00:08:00.849 align:start position:0%
are still going a Big O of 1 no matter
 

00:08:00.849 --> 00:08:02.659 align:start position:0%
are still going a Big O of 1 no matter
what<00:08:01.119><c> okay</c>

00:08:02.659 --> 00:08:02.669 align:start position:0%
what okay
 

00:08:02.669 --> 00:08:05.309 align:start position:0%
what okay
so<00:08:03.669><c> that</c><00:08:04.149><c> leaves</c><00:08:04.269><c> us</c><00:08:04.389><c> with</c><00:08:04.449><c> space</c><00:08:04.809><c> complexity</c>

00:08:05.309 --> 00:08:05.319 align:start position:0%
so that leaves us with space complexity
 

00:08:05.319 --> 00:08:09.089 align:start position:0%
so that leaves us with space complexity
and<00:08:05.529><c> we're</c><00:08:06.459><c> just</c><00:08:06.550><c> working</c><00:08:06.759><c> with</c><00:08:07.949><c> we're</c><00:08:08.949><c> just</c>

00:08:09.089 --> 00:08:09.099 align:start position:0%
and we're just working with we're just
 

00:08:09.099 --> 00:08:12.119 align:start position:0%
and we're just working with we're just
working<00:08:09.219><c> with</c><00:08:09.489><c> the</c><00:08:09.610><c> size</c><00:08:09.789><c> of</c><00:08:10.709><c> of</c><00:08:11.709><c> the</c><00:08:11.919><c> doubly</c>

00:08:12.119 --> 00:08:12.129 align:start position:0%
working with the size of of the doubly
 

00:08:12.129 --> 00:08:15.899 align:start position:0%
working with the size of of the doubly
linked<00:08:12.309><c> list</c><00:08:12.550><c> so</c><00:08:13.239><c> there's</c><00:08:14.019><c> no</c><00:08:14.279><c> other</c><00:08:15.279><c> space</c>

00:08:15.899 --> 00:08:15.909 align:start position:0%
linked list so there's no other space
 

00:08:15.909 --> 00:08:17.850 align:start position:0%
linked list so there's no other space
there's<00:08:16.300><c> no</c><00:08:16.360><c> store</c><00:08:16.839><c> other</c><00:08:17.050><c> stores</c><00:08:17.409><c> needed</c>

00:08:17.850 --> 00:08:17.860 align:start position:0%
there's no store other stores needed
 

00:08:17.860 --> 00:08:19.679 align:start position:0%
there's no store other stores needed
that's<00:08:18.489><c> perform</c><00:08:18.879><c> these</c><00:08:18.999><c> algorithms</c><00:08:19.209><c> so</c><00:08:19.599><c> it's</c>

00:08:19.679 --> 00:08:19.689 align:start position:0%
that's perform these algorithms so it's
 

00:08:19.689 --> 00:08:23.640 align:start position:0%
that's perform these algorithms so it's
just<00:08:19.749><c> Big</c><00:08:19.989><c> O</c><00:08:20.079><c> of</c><00:08:20.110><c> n</c><00:08:20.459><c> okay</c><00:08:22.019><c> well</c><00:08:23.019><c> I</c><00:08:23.079><c> hope</c><00:08:23.289><c> you</c>

00:08:23.640 --> 00:08:23.650 align:start position:0%
just Big O of n okay well I hope you
 

00:08:23.650 --> 00:08:25.469 align:start position:0%
just Big O of n okay well I hope you
something<00:08:24.040><c> and</c><00:08:24.250><c> if</c><00:08:24.699><c> you</c><00:08:24.759><c> have</c><00:08:24.880><c> any</c><00:08:25.000><c> questions</c>

00:08:25.469 --> 00:08:25.479 align:start position:0%
something and if you have any questions
 

00:08:25.479 --> 00:08:27.480 align:start position:0%
something and if you have any questions
or<00:08:25.660><c> comments</c><00:08:25.930><c> I</c><00:08:26.320><c> will</c><00:08:26.560><c> get</c><00:08:26.830><c> back</c><00:08:26.979><c> I</c><00:08:27.250><c> will</c><00:08:27.370><c> get</c>

00:08:27.480 --> 00:08:27.490 align:start position:0%
or comments I will get back I will get
 

00:08:27.490 --> 00:08:29.730 align:start position:0%
or comments I will get back I will get
back<00:08:27.610><c> to</c><00:08:27.669><c> you</c><00:08:27.880><c> as</c><00:08:28.000><c> soon</c><00:08:28.030><c> as</c><00:08:28.180><c> possible</c><00:08:28.690><c> and</c><00:08:28.900><c> I'll</c>

00:08:29.730 --> 00:08:29.740 align:start position:0%
back to you as soon as possible and I'll
 

00:08:29.740 --> 00:08:32.190 align:start position:0%
back to you as soon as possible and I'll
see<00:08:29.860><c> you</c><00:08:29.919><c> next</c><00:08:29.949><c> time</c>

