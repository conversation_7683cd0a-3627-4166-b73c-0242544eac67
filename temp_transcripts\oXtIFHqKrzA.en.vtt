WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.309 align:start position:0%
 
welcome<00:00:00.210><c> back</c><00:00:00.420><c> so</c><00:00:00.630><c> in</c><00:00:00.750><c> this</c><00:00:00.840><c> video</c><00:00:01.020><c> we're</c>

00:00:01.309 --> 00:00:01.319 align:start position:0%
welcome back so in this video we're
 

00:00:01.319 --> 00:00:03.230 align:start position:0%
welcome back so in this video we're
actually<00:00:01.469><c> gonna</c><00:00:01.800><c> start</c><00:00:01.920><c> digging</c><00:00:02.340><c> and</c><00:00:02.909><c> start</c>

00:00:03.230 --> 00:00:03.240 align:start position:0%
actually gonna start digging and start
 

00:00:03.240 --> 00:00:05.120 align:start position:0%
actually gonna start digging and start
making<00:00:03.570><c> changes</c><00:00:03.780><c> to</c><00:00:04.020><c> the</c><00:00:04.290><c> code</c><00:00:04.500><c> so</c><00:00:04.830><c> this</c>

00:00:05.120 --> 00:00:05.130 align:start position:0%
making changes to the code so this
 

00:00:05.130 --> 00:00:07.720 align:start position:0%
making changes to the code so this
specific<00:00:05.730><c> file</c><00:00:05.940><c> was</c><00:00:06.330><c> called</c><00:00:06.629><c> home</c><00:00:06.960><c> -</c><00:00:07.259><c> list</c>

00:00:07.720 --> 00:00:07.730 align:start position:0%
specific file was called home - list
 

00:00:07.730 --> 00:00:10.250 align:start position:0%
specific file was called home - list
HTML<00:00:08.730><c> and</c><00:00:08.940><c> just</c><00:00:09.150><c> figured</c><00:00:09.420><c> it's</c><00:00:09.570><c> too</c><00:00:09.780><c> confusing</c>

00:00:10.250 --> 00:00:10.260 align:start position:0%
HTML and just figured it's too confusing
 

00:00:10.260 --> 00:00:12.890 align:start position:0%
HTML and just figured it's too confusing
because<00:00:10.559><c> after</c><00:00:10.980><c> all</c><00:00:11.010><c> it</c><00:00:11.790><c> just</c><00:00:11.849><c> holds</c><00:00:12.389><c> the</c><00:00:12.599><c> chat</c>

00:00:12.890 --> 00:00:12.900 align:start position:0%
because after all it just holds the chat
 

00:00:12.900 --> 00:00:16.129 align:start position:0%
because after all it just holds the chat
so<00:00:13.320><c> I</c><00:00:13.349><c> need</c><00:00:13.590><c> to</c><00:00:13.769><c> fix</c><00:00:14.009><c> that</c><00:00:14.130><c> so</c><00:00:15.059><c> I</c><00:00:15.089><c> go</c><00:00:15.360><c> in</c><00:00:15.690><c> order</c>

00:00:16.129 --> 00:00:16.139 align:start position:0%
so I need to fix that so I go in order
 

00:00:16.139 --> 00:00:17.930 align:start position:0%
so I need to fix that so I go in order
to<00:00:16.230><c> do</c><00:00:16.410><c> that</c><00:00:16.560><c> first</c><00:00:16.619><c> I</c><00:00:16.980><c> change</c><00:00:17.250><c> the</c><00:00:17.430><c> name</c><00:00:17.609><c> here</c>

00:00:17.930 --> 00:00:17.940 align:start position:0%
to do that first I change the name here
 

00:00:17.940 --> 00:00:22.570 align:start position:0%
to do that first I change the name here
chat<00:00:18.390><c> dot</c><00:00:18.779><c> alright</c><00:00:19.670><c> let's</c><00:00:20.670><c> change</c><00:00:21.029><c> this</c><00:00:21.300><c> to</c>

00:00:22.570 --> 00:00:22.580 align:start position:0%
chat dot alright let's change this to
 

00:00:22.580 --> 00:00:26.570 align:start position:0%
chat dot alright let's change this to
ejs<00:00:23.580><c> it's</c><00:00:24.180><c> always</c><00:00:24.449><c> good</c><00:00:24.720><c> and</c><00:00:25.279><c> we're</c><00:00:26.279><c> gonna</c><00:00:26.369><c> go</c>

00:00:26.570 --> 00:00:26.580 align:start position:0%
ejs it's always good and we're gonna go
 

00:00:26.580 --> 00:00:29.950 align:start position:0%
ejs it's always good and we're gonna go
into<00:00:26.760><c> our</c><00:00:26.970><c> core</c><00:00:27.359><c> j/s</c><00:00:27.960><c> we're</c><00:00:28.320><c> gonna</c><00:00:28.439><c> go</c><00:00:28.740><c> into</c>

00:00:29.950 --> 00:00:29.960 align:start position:0%
into our core j/s we're gonna go into
 

00:00:29.960 --> 00:00:33.470 align:start position:0%
into our core j/s we're gonna go into
this<00:00:30.960><c> file</c><00:00:31.349><c> here</c><00:00:32.360><c> mmm</c>

00:00:33.470 --> 00:00:33.480 align:start position:0%
this file here mmm
 

00:00:33.480 --> 00:00:36.080 align:start position:0%
this file here mmm
it<00:00:33.719><c> was</c><00:00:33.870><c> home</c><00:00:34.200><c> -</c><00:00:34.500><c> list</c><00:00:34.890><c> out</c><00:00:35.100><c> HTML</c><00:00:35.579><c> so</c><00:00:35.940><c> this</c>

00:00:36.080 --> 00:00:36.090 align:start position:0%
it was home - list out HTML so this
 

00:00:36.090 --> 00:00:40.310 align:start position:0%
it was home - list out HTML so this
needs<00:00:36.329><c> to</c><00:00:36.420><c> change</c><00:00:36.780><c> to</c><00:00:37.640><c> chat</c><00:00:38.780><c> okay</c><00:00:39.809><c> and</c><00:00:40.050><c> it's</c><00:00:40.200><c> an</c>

00:00:40.310 --> 00:00:40.320 align:start position:0%
needs to change to chat okay and it's an
 

00:00:40.320 --> 00:00:43.310 align:start position:0%
needs to change to chat okay and it's an
ejs<00:00:40.770><c> file</c><00:00:41.010><c> so</c><00:00:41.219><c> we</c><00:00:41.340><c> don't</c><00:00:41.520><c> need</c><00:00:41.610><c> this</c><00:00:41.850><c> and</c><00:00:42.320><c> we're</c>

00:00:43.310 --> 00:00:43.320 align:start position:0%
ejs file so we don't need this and we're
 

00:00:43.320 --> 00:00:49.029 align:start position:0%
ejs file so we don't need this and we're
gonna<00:00:43.440><c> call</c><00:00:43.739><c> this</c><00:00:43.980><c> URL</c><00:00:44.760><c> is</c><00:00:45.829><c> checked</c><00:00:47.030><c> ok</c><00:00:48.030><c> and</c>

00:00:49.029 --> 00:00:49.039 align:start position:0%
gonna call this URL is checked ok and
 

00:00:49.039 --> 00:00:51.319 align:start position:0%
gonna call this URL is checked ok and
it's<00:00:50.039><c> possible</c><00:00:50.550><c> that</c><00:00:50.610><c> I</c><00:00:50.789><c> need</c><00:00:51.000><c> to</c><00:00:51.120><c> change</c>

00:00:51.319 --> 00:00:51.329 align:start position:0%
it's possible that I need to change
 

00:00:51.329 --> 00:00:52.790 align:start position:0%
it's possible that I need to change
something<00:00:51.660><c> on</c><00:00:51.870><c> the</c><00:00:52.020><c> server</c>

00:00:52.790 --> 00:00:52.800 align:start position:0%
something on the server
 

00:00:52.800 --> 00:00:56.959 align:start position:0%
something on the server
let's<00:00:53.640><c> see</c><00:00:53.910><c> your</c><00:00:54.090><c> routes</c><00:00:54.559><c> show</c><00:00:55.559><c> at</c><00:00:55.890><c> home</c><00:00:56.399><c> -</c>

00:00:56.959 --> 00:00:56.969 align:start position:0%
let's see your routes show at home -
 

00:00:56.969 --> 00:00:59.740 align:start position:0%
let's see your routes show at home -
list<00:00:57.500><c> no</c><00:00:58.500><c> I</c><00:00:58.800><c> don't</c><00:00:59.010><c> think</c><00:00:59.219><c> I</c><00:00:59.340><c> will</c>

00:00:59.740 --> 00:00:59.750 align:start position:0%
list no I don't think I will
 

00:00:59.750 --> 00:01:03.889 align:start position:0%
list no I don't think I will
okay<00:01:00.750><c> so</c><00:01:01.460><c> let's</c><00:01:02.460><c> reload</c><00:01:02.879><c> the</c><00:01:03.030><c> page</c><00:01:03.270><c> and</c><00:01:03.600><c> see</c><00:01:03.750><c> if</c>

00:01:03.889 --> 00:01:03.899 align:start position:0%
okay so let's reload the page and see if
 

00:01:03.899 --> 00:01:04.660 align:start position:0%
okay so let's reload the page and see if
it<00:01:04.019><c> works</c>

00:01:04.660 --> 00:01:04.670 align:start position:0%
it works
 

00:01:04.670 --> 00:01:12.050 align:start position:0%
it works
um<00:01:05.670><c> -</c><00:01:06.150><c> list</c><00:01:06.540><c> out</c><00:01:06.750><c> HTML</c><00:01:10.460><c> okay</c><00:01:11.460><c> it's</c><00:01:11.640><c> showing</c><00:01:11.939><c> us</c>

00:01:12.050 --> 00:01:12.060 align:start position:0%
um - list out HTML okay it's showing us
 

00:01:12.060 --> 00:01:19.880 align:start position:0%
um - list out HTML okay it's showing us
nothing<00:01:13.430><c> control</c><00:01:14.430><c> shift</c><00:01:14.460><c> J</c><00:01:18.560><c> why</c><00:01:19.560><c> is</c><00:01:19.619><c> it</c>

00:01:19.880 --> 00:01:19.890 align:start position:0%
nothing control shift J why is it
 

00:01:19.890 --> 00:01:22.249 align:start position:0%
nothing control shift J why is it
showing<00:01:20.070><c> there's</c><00:01:20.400><c> nothing</c><00:01:20.900><c> these</c><00:01:21.900><c> are</c><00:01:22.110><c> the</c>

00:01:22.249 --> 00:01:22.259 align:start position:0%
showing there's nothing these are the
 

00:01:22.259 --> 00:01:25.550 align:start position:0%
showing there's nothing these are the
users<00:01:22.710><c> array</c><00:01:23.119><c> gives</c><00:01:24.119><c> me</c><00:01:24.360><c> this</c><00:01:24.509><c> is</c><00:01:24.570><c> my</c><00:01:24.930><c> specific</c>

00:01:25.550 --> 00:01:25.560 align:start position:0%
users array gives me this is my specific
 

00:01:25.560 --> 00:01:28.520 align:start position:0%
users array gives me this is my specific
currently<00:01:26.009><c> logged</c><00:01:26.250><c> in</c><00:01:26.460><c> user</c><00:01:27.229><c> these</c><00:01:28.229><c> are</c><00:01:28.409><c> all</c>

00:01:28.520 --> 00:01:28.530 align:start position:0%
currently logged in user these are all
 

00:01:28.530 --> 00:01:33.429 align:start position:0%
currently logged in user these are all
the<00:01:28.680><c> messages</c><00:01:30.710><c> undefined</c><00:01:31.710><c> messages</c><00:01:32.400><c> and</c>

00:01:33.429 --> 00:01:33.439 align:start position:0%
the messages undefined messages and
 

00:01:33.439 --> 00:01:42.440 align:start position:0%
the messages undefined messages and
these<00:01:34.439><c> are</c><00:01:34.590><c> all</c><00:01:34.710><c> some</c><00:01:34.979><c> users</c><00:01:35.930><c> okay</c><00:01:36.930><c> so</c><00:01:41.450><c> slash</c>

00:01:42.440 --> 00:01:42.450 align:start position:0%
these are all some users okay so slash
 

00:01:42.450 --> 00:01:55.399 align:start position:0%
these are all some users okay so slash
profile<00:01:48.649><c> home</c><00:01:49.649><c> -</c><00:01:50.070><c> chat</c><00:01:50.579><c> -</c><00:01:53.899><c> chat</c><00:01:54.899><c> and</c><00:01:55.229><c> by</c>

00:01:55.399 --> 00:01:55.409 align:start position:0%
profile home - chat - chat and by
 

00:01:55.409 --> 00:01:57.289 align:start position:0%
profile home - chat - chat and by
default<00:01:55.619><c> instead</c><00:01:56.189><c> of</c><00:01:56.310><c> going</c><00:01:56.460><c> to</c><00:01:56.549><c> home</c><00:01:56.700><c> -</c><00:01:56.939><c> list</c>

00:01:57.289 --> 00:01:57.299 align:start position:0%
default instead of going to home - list
 

00:01:57.299 --> 00:02:01.999 align:start position:0%
default instead of going to home - list
it's<00:01:57.540><c> going</c><00:01:57.780><c> to</c><00:01:58.100><c> long</c><00:01:59.100><c> -</c><00:01:59.399><c> chest</c><00:02:00.020><c> and</c><00:02:01.020><c> let's</c>

00:02:01.999 --> 00:02:02.009 align:start position:0%
it's going to long - chest and let's
 

00:02:02.009 --> 00:02:08.860 align:start position:0%
it's going to long - chest and let's
reload<00:02:02.460><c> this</c><00:02:02.700><c> -</c><00:02:03.420><c> vocalist</c><00:02:04.170><c> 8,000</c>

00:02:08.860 --> 00:02:08.870 align:start position:0%
 
 

00:02:08.870 --> 00:02:17.330 align:start position:0%
 
since<00:02:09.870><c> it</c><00:02:10.050><c> works</c><00:02:13.970><c> yeah</c><00:02:14.970><c> but</c><00:02:15.390><c> they</c><00:02:15.830><c> okay</c><00:02:16.830><c> still</c>

00:02:17.330 --> 00:02:17.340 align:start position:0%
since it works yeah but they okay still
 

00:02:17.340 --> 00:02:19.120 align:start position:0%
since it works yeah but they okay still
doesn't<00:02:17.670><c> work</c>

00:02:19.120 --> 00:02:19.130 align:start position:0%
doesn't work
 

00:02:19.130 --> 00:02:23.780 align:start position:0%
doesn't work
see<00:02:20.130><c> home</c><00:02:20.610><c> slash</c><00:02:20.970><c> chat</c><00:02:21.420><c> it's</c><00:02:21.630><c> going</c><00:02:21.870><c> to</c><00:02:22.610><c> if</c><00:02:23.610><c> it</c>

00:02:23.780 --> 00:02:23.790 align:start position:0%
see home slash chat it's going to if it
 

00:02:23.790 --> 00:02:26.390 align:start position:0%
see home slash chat it's going to if it
goes<00:02:23.940><c> to</c><00:02:24.120><c> home</c><00:02:24.390><c> slash</c><00:02:24.810><c> chat</c><00:02:25.290><c> supposed</c><00:02:25.980><c> to</c><00:02:26.160><c> show</c>

00:02:26.390 --> 00:02:26.400 align:start position:0%
goes to home slash chat supposed to show
 

00:02:26.400 --> 00:02:33.080 align:start position:0%
goes to home slash chat supposed to show
us<00:02:26.720><c> our</c><00:02:27.720><c> template</c><00:02:29.750><c> chat</c><00:02:30.750><c> oh</c><00:02:31.230><c> my</c><00:02:32.160><c> god</c><00:02:32.520><c> Chad</c><00:02:32.820><c> Hohn</c>

00:02:33.080 --> 00:02:33.090 align:start position:0%
us our template chat oh my god Chad Hohn
 

00:02:33.090 --> 00:02:37.810 align:start position:0%
us our template chat oh my god Chad Hohn
doubtless<00:02:33.600><c> -</c><00:02:34.970><c> okay</c><00:02:35.970><c> sounds</c><00:02:36.240><c> good</c>

00:02:37.810 --> 00:02:37.820 align:start position:0%
doubtless - okay sounds good
 

00:02:37.820 --> 00:02:40.460 align:start position:0%
doubtless - okay sounds good
okay<00:02:38.820><c> fine</c><00:02:39.060><c> I</c><00:02:39.270><c> will</c><00:02:39.360><c> in</c><00:02:39.630><c> two</c><00:02:39.990><c> minutes</c><00:02:40.290><c> two</c>

00:02:40.460 --> 00:02:40.470 align:start position:0%
okay fine I will in two minutes two
 

00:02:40.470 --> 00:02:47.350 align:start position:0%
okay fine I will in two minutes two
minutes<00:02:40.760><c> okay</c><00:02:41.760><c> um</c><00:02:42.450><c> -</c><00:02:42.960><c> Jeff</c><00:02:43.320><c> routes</c><00:02:45.470><c> please</c>

00:02:47.350 --> 00:02:47.360 align:start position:0%
minutes okay um - Jeff routes please
 

00:02:47.360 --> 00:02:56.870 align:start position:0%
minutes okay um - Jeff routes please
that's<00:02:48.360><c> not</c><00:02:48.630><c> this</c><00:02:48.900><c> J</c><00:02:49.440><c> is</c><00:02:49.470><c> cord</c><00:02:50.250><c> is</c><00:02:55.190><c> this</c><00:02:56.190><c> -</c><00:02:56.460><c> home</c>

00:02:56.870 --> 00:02:56.880 align:start position:0%
that's not this J is cord is this - home
 

00:02:56.880 --> 00:02:59.720 align:start position:0%
that's not this J is cord is this - home
-<00:02:56.910><c> Chad</c><00:02:57.390><c> /j</c><00:02:58.020><c> template</c><00:02:58.470><c> URL</c><00:02:58.650><c> is</c><00:02:59.070><c> chat</c><00:02:59.370><c> okay</c>

00:02:59.720 --> 00:02:59.730 align:start position:0%
- Chad /j template URL is chat okay
 

00:02:59.730 --> 00:03:07.010 align:start position:0%
- Chad /j template URL is chat okay
something<00:03:00.320><c> use</c><00:03:01.320><c> J</c><00:03:02.250><c> s</c><00:03:03.170><c> partials</c><00:03:04.190><c> public</c><00:03:06.020><c> cord</c>

00:03:07.010 --> 00:03:07.020 align:start position:0%
something use J s partials public cord
 

00:03:07.020 --> 00:03:25.190 align:start position:0%
something use J s partials public cord
that<00:03:07.200><c> is</c><00:03:13.670><c> chat</c><00:03:14.670><c> is</c><00:03:18.320><c> crap</c><00:03:22.790><c> wait</c><00:03:23.820><c> I</c><00:03:24.240><c> go</c><00:03:24.660><c> to</c><00:03:24.720><c> index</c>

00:03:25.190 --> 00:03:25.200 align:start position:0%
that is chat is crap wait I go to index
 

00:03:25.200 --> 00:03:34.370 align:start position:0%
that is chat is crap wait I go to index
that<00:03:25.440><c> ejs</c><00:03:28.160><c> first</c><00:03:32.510><c> now</c><00:03:33.510><c> if</c><00:03:33.570><c> I</c><00:03:33.750><c> got</c><00:03:33.810><c> a</c><00:03:33.990><c> partial</c>

00:03:34.370 --> 00:03:34.380 align:start position:0%
that ejs first now if I got a partial
 

00:03:34.380 --> 00:03:44.020 align:start position:0%
that ejs first now if I got a partial
home<00:03:37.940><c> is</c><00:03:38.940><c> this</c><00:03:39.510><c> I</c><00:03:40.340><c> go</c><00:03:41.340><c> to</c><00:03:41.400><c> lead</c><00:03:41.670><c> it's</c><00:03:41.850><c> that</c><00:03:42.750><c> list</c>

00:03:44.020 --> 00:03:44.030 align:start position:0%
home is this I go to lead it's that list
 

00:03:44.030 --> 00:03:46.580 align:start position:0%
home is this I go to lead it's that list
that<00:03:45.030><c> Shack</c><00:03:45.540><c> I</c><00:03:45.840><c> don't</c><00:03:45.990><c> think</c><00:03:46.200><c> it'll</c><00:03:46.380><c> make</c><00:03:46.530><c> a</c>

00:03:46.580 --> 00:03:46.590 align:start position:0%
that Shack I don't think it'll make a
 

00:03:46.590 --> 00:03:57.990 align:start position:0%
that Shack I don't think it'll make a
difference<00:03:46.710><c> but</c><00:03:48.950><c> anyways</c><00:03:54.710><c> comes</c><00:03:55.710><c> like</c>

00:03:57.990 --> 00:03:58.000 align:start position:0%
 
 

00:03:58.000 --> 00:04:00.910 align:start position:0%
 
404<00:03:59.000><c> logo</c><00:03:59.630><c> I</c><00:03:59.720><c> say</c><00:03:59.900><c> it</c><00:03:59.990><c> doesn't</c><00:04:00.170><c> chat</c><00:04:00.650><c> not</c>

00:04:00.910 --> 00:04:00.920 align:start position:0%
404 logo I say it doesn't chat not
 

00:04:00.920 --> 00:04:03.640 align:start position:0%
404 logo I say it doesn't chat not
found'<00:04:01.250><c> okay</c><00:04:01.690><c> we</c><00:04:02.690><c> figured</c><00:04:03.050><c> it</c><00:04:03.140><c> out</c><00:04:03.290><c> I</c><00:04:03.350><c> need</c><00:04:03.590><c> to</c>

00:04:03.640 --> 00:04:03.650 align:start position:0%
found' okay we figured it out I need to
 

00:04:03.650 --> 00:04:09.970 align:start position:0%
found' okay we figured it out I need to
go<00:04:03.800><c> into</c><00:04:03.980><c> my</c><00:04:04.220><c> routes</c><00:04:04.600><c> and</c><00:04:06.340><c> I</c><00:04:07.340><c> need</c><00:04:07.700><c> to</c><00:04:07.880><c> go</c><00:04:08.980><c> app</c>

00:04:09.970 --> 00:04:09.980 align:start position:0%
go into my routes and I need to go app
 

00:04:09.980 --> 00:04:27.000 align:start position:0%
go into my routes and I need to go app
dot<00:04:10.340><c> get</c><00:04:14.830><c> /</c><00:04:15.910><c> chat</c><00:04:18.040><c> equals</c><00:04:19.040><c> chat</c><00:04:19.760><c> that</c><00:04:20.030><c> ejs</c><00:04:22.060><c> and</c>

00:04:27.000 --> 00:04:27.010 align:start position:0%
 
 

00:04:27.010 --> 00:04:30.630 align:start position:0%
 
this<00:04:28.010><c> site</c><00:04:28.310><c> can't</c><00:04:28.580><c> be</c><00:04:28.640><c> reached</c><00:04:28.880><c> beautiful</c>

00:04:30.630 --> 00:04:30.640 align:start position:0%
this site can't be reached beautiful
 

00:04:30.640 --> 00:04:31.840 align:start position:0%
this site can't be reached beautiful
okay

00:04:31.840 --> 00:04:31.850 align:start position:0%
okay
 

00:04:31.850 --> 00:04:38.200 align:start position:0%
okay
why<00:04:33.430><c> home</c><00:04:34.430><c> /</c><00:04:34.760><c> chat</c><00:04:35.120><c> there</c><00:04:35.780><c> we</c><00:04:36.020><c> go</c><00:04:36.170><c> okay</c><00:04:36.830><c> so</c><00:04:37.210><c> now</c>

00:04:38.200 --> 00:04:38.210 align:start position:0%
why home / chat there we go okay so now
 

00:04:38.210 --> 00:04:39.280 align:start position:0%
why home / chat there we go okay so now
you<00:04:38.270><c> know</c><00:04:38.450><c> a</c><00:04:38.480><c> little</c><00:04:38.720><c> bit</c><00:04:38.810><c> about</c><00:04:38.930><c> how</c><00:04:39.260><c> to</c>

00:04:39.280 --> 00:04:39.290 align:start position:0%
you know a little bit about how to
 

00:04:39.290 --> 00:04:41.350 align:start position:0%
you know a little bit about how to
create<00:04:39.530><c> a</c><00:04:39.710><c> new</c><00:04:40.010><c> route</c><00:04:40.220><c> how</c><00:04:40.580><c> to</c><00:04:40.640><c> edit</c><00:04:40.880><c> routes</c>

00:04:41.350 --> 00:04:41.360 align:start position:0%
create a new route how to edit routes
 

00:04:41.360 --> 00:04:46.870 align:start position:0%
create a new route how to edit routes
you<00:04:42.200><c> need</c><00:04:42.680><c> to</c><00:04:42.950><c> add</c><00:04:44.680><c> add</c><00:04:45.680><c> something</c><00:04:46.460><c> to</c><00:04:46.760><c> the</c>

00:04:46.870 --> 00:04:46.880 align:start position:0%
you need to add add something to the
 

00:04:46.880 --> 00:04:48.400 align:start position:0%
you need to add add something to the
routes<00:04:47.120><c> over</c><00:04:47.540><c> here</c><00:04:47.720><c> so</c><00:04:47.870><c> every</c><00:04:48.050><c> time</c><00:04:48.200><c> that</c><00:04:48.290><c> the</c>

00:04:48.400 --> 00:04:48.410 align:start position:0%
routes over here so every time that the
 

00:04:48.410 --> 00:04:50.380 align:start position:0%
routes over here so every time that the
user<00:04:48.440><c> navigates</c><00:04:49.130><c> the</c><00:04:49.340><c> slash</c><00:04:49.640><c> chat</c><00:04:50.030><c> she's</c>

00:04:50.380 --> 00:04:50.390 align:start position:0%
user navigates the slash chat she's
 

00:04:50.390 --> 00:04:54.310 align:start position:0%
user navigates the slash chat she's
shown<00:04:50.690><c> chat</c><00:04:51.160><c> ejs</c><00:04:52.480><c> profile</c><00:04:53.480><c> that</c><00:04:53.660><c> he</c><00:04:53.780><c> just</c><00:04:54.020><c> or</c>

00:04:54.310 --> 00:04:54.320 align:start position:0%
shown chat ejs profile that he just or
 

00:04:54.320 --> 00:04:56.080 align:start position:0%
shown chat ejs profile that he just or
she'll<00:04:54.500><c> be</c><00:04:54.590><c> jeaious</c><00:04:54.800><c> and</c><00:04:55.010><c> so</c><00:04:55.160><c> on</c><00:04:55.340><c> so</c><00:04:55.640><c> thanks</c>

00:04:56.080 --> 00:04:56.090 align:start position:0%
she'll be jeaious and so on so thanks
 

00:04:56.090 --> 00:05:00.160 align:start position:0%
she'll be jeaious and so on so thanks
guys<00:04:56.150><c> for</c><00:04:56.450><c> tuning</c><00:04:56.630><c> in</c><00:04:56.870><c> and</c><00:04:57.080><c> have</c><00:04:57.710><c> a</c><00:04:57.740><c> good</c><00:04:57.980><c> one</c>

