WEBVTT
Kind: captions
Language: en

00:00:01.719 --> 00:00:05.230 align:start position:0%
 
hello<00:00:02.280><c> everyone</c><00:00:03.280><c> Ravi</c><00:00:03.639><c> here</c><00:00:03.800><c> from</c><00:00:03.959><c> Lama</c><00:00:04.240><c> index</c>

00:00:05.230 --> 00:00:05.240 align:start position:0%
hello everyone Ravi here from <PERSON> index
 

00:00:05.240 --> 00:00:06.909 align:start position:0%
hello everyone Ravi here from <PERSON> index
so<00:00:05.440><c> in</c><00:00:05.560><c> this</c><00:00:05.720><c> video</c><00:00:06.000><c> we'll</c><00:00:06.200><c> look</c><00:00:06.359><c> into</c><00:00:06.560><c> react</c>

00:00:06.909 --> 00:00:06.919 align:start position:0%
so in this video we'll look into react
 

00:00:06.919 --> 00:00:09.870 align:start position:0%
so in this video we'll look into react
agent<00:00:07.640><c> so</c><00:00:07.879><c> basically</c><00:00:08.280><c> we'll</c><00:00:09.200><c> experiment</c><00:00:09.679><c> with</c>

00:00:09.870 --> 00:00:09.880 align:start position:0%
agent so basically we'll experiment with
 

00:00:09.880 --> 00:00:12.470 align:start position:0%
agent so basically we'll experiment with
react<00:00:10.280><c> agent</c><00:00:10.880><c> with</c><00:00:11.040><c> simple</c><00:00:11.440><c> calculator</c><00:00:11.920><c> tools</c>

00:00:12.470 --> 00:00:12.480 align:start position:0%
react agent with simple calculator tools
 

00:00:12.480 --> 00:00:17.750 align:start position:0%
react agent with simple calculator tools
and<00:00:12.920><c> rack</c><00:00:13.200><c> wi</c><00:00:13.519><c> engine</c><00:00:14.040><c> tools</c><00:00:15.040><c> so</c><00:00:15.759><c> we'll</c><00:00:16.080><c> use</c><00:00:16.760><c> um</c>

00:00:17.750 --> 00:00:17.760 align:start position:0%
and rack wi engine tools so we'll use um
 

00:00:17.760 --> 00:00:20.150 align:start position:0%
and rack wi engine tools so we'll use um
different<00:00:18.080><c> llms</c><00:00:18.840><c> starting</c><00:00:19.240><c> with</c><00:00:19.439><c> open</c><00:00:19.720><c> a</c>

00:00:20.150 --> 00:00:20.160 align:start position:0%
different llms starting with open a
 

00:00:20.160 --> 00:00:25.550 align:start position:0%
different llms starting with open a
anthropic<00:00:21.000><c> and</c><00:00:21.160><c> M</c><00:00:21.600><c> A</c><00:00:22.400><c> llms</c><00:00:23.400><c> and</c><00:00:23.640><c> then</c><00:00:23.960><c> uh</c><00:00:24.960><c> so</c>

00:00:25.550 --> 00:00:25.560 align:start position:0%
anthropic and M A llms and then uh so
 

00:00:25.560 --> 00:00:29.429 align:start position:0%
anthropic and M A llms and then uh so
what<00:00:25.760><c> exactly</c><00:00:26.119><c> is</c><00:00:26.599><c> uh</c><00:00:26.920><c> react</c><00:00:27.599><c> agent</c><00:00:28.599><c> so</c><00:00:29.279><c> uh</c>

00:00:29.429 --> 00:00:29.439 align:start position:0%
what exactly is uh react agent so uh
 

00:00:29.439 --> 00:00:32.190 align:start position:0%
what exactly is uh react agent so uh
given<00:00:29.640><c> a</c><00:00:29.759><c> use</c><00:00:30.279><c> a</c><00:00:30.439><c> message</c><00:00:30.720><c> or</c><00:00:30.960><c> query</c><00:00:31.759><c> and</c><00:00:31.880><c> a</c><00:00:32.040><c> set</c>

00:00:32.190 --> 00:00:32.200 align:start position:0%
given a use a message or query and a set
 

00:00:32.200 --> 00:00:34.350 align:start position:0%
given a use a message or query and a set
of<00:00:32.360><c> tools</c><00:00:32.920><c> what</c><00:00:33.120><c> happens</c><00:00:33.480><c> is</c><00:00:33.840><c> uh</c><00:00:33.960><c> the</c><00:00:34.079><c> react</c>

00:00:34.350 --> 00:00:34.360 align:start position:0%
of tools what happens is uh the react
 

00:00:34.360 --> 00:00:36.910 align:start position:0%
of tools what happens is uh the react
framework<00:00:34.800><c> will</c><00:00:35.000><c> use</c><00:00:35.800><c> um</c><00:00:36.040><c> three-step</c><00:00:36.559><c> process</c>

00:00:36.910 --> 00:00:36.920 align:start position:0%
framework will use um three-step process
 

00:00:36.920 --> 00:00:39.510 align:start position:0%
framework will use um three-step process
which<00:00:37.079><c> is</c><00:00:38.000><c> uh</c><00:00:38.280><c> thought</c><00:00:38.800><c> action</c><00:00:39.280><c> and</c>

00:00:39.510 --> 00:00:39.520 align:start position:0%
which is uh thought action and
 

00:00:39.520 --> 00:00:41.869 align:start position:0%
which is uh thought action and
observation<00:00:40.320><c> so</c><00:00:40.520><c> given</c><00:00:40.800><c> a</c><00:00:41.000><c> message</c><00:00:41.520><c> and</c><00:00:41.719><c> then</c>

00:00:41.869 --> 00:00:41.879 align:start position:0%
observation so given a message and then
 

00:00:41.879 --> 00:00:45.069 align:start position:0%
observation so given a message and then
user<00:00:42.200><c> message</c><00:00:42.559><c> and</c><00:00:42.960><c> uh</c><00:00:43.760><c> uh</c><00:00:43.879><c> set</c><00:00:44.079><c> of</c><00:00:44.280><c> tools</c><00:00:44.960><c> it</c>

00:00:45.069 --> 00:00:45.079 align:start position:0%
user message and uh uh set of tools it
 

00:00:45.079 --> 00:00:47.790 align:start position:0%
user message and uh uh set of tools it
will<00:00:45.520><c> uh</c><00:00:45.600><c> figure</c><00:00:45.879><c> out</c><00:00:46.520><c> uh</c><00:00:46.760><c> what</c><00:00:46.920><c> is</c>

00:00:47.790 --> 00:00:47.800 align:start position:0%
will uh figure out uh what is
 

00:00:47.800 --> 00:00:51.950 align:start position:0%
will uh figure out uh what is
the<00:00:48.800><c> action</c><00:00:49.600><c> uh</c><00:00:50.160><c> that</c><00:00:50.360><c> it</c><00:00:50.520><c> needs</c><00:00:50.760><c> to</c><00:00:50.960><c> take</c><00:00:51.360><c> by</c>

00:00:51.950 --> 00:00:51.960 align:start position:0%
the action uh that it needs to take by
 

00:00:51.960 --> 00:00:55.430 align:start position:0%
the action uh that it needs to take by
using<00:00:52.280><c> a</c><00:00:52.440><c> tool</c><00:00:53.199><c> um</c><00:00:53.559><c> so</c><00:00:53.800><c> it</c><00:00:54.000><c> thinks</c><00:00:54.440><c> on</c><00:00:55.239><c> what</c><00:00:55.359><c> is</c>

00:00:55.430 --> 00:00:55.440 align:start position:0%
using a tool um so it thinks on what is
 

00:00:55.440 --> 00:00:58.110 align:start position:0%
using a tool um so it thinks on what is
the<00:00:55.640><c> right</c><00:00:55.800><c> tool</c><00:00:56.079><c> it</c><00:00:56.199><c> should</c><00:00:56.480><c> take</c><00:00:57.199><c> uh</c><00:00:57.800><c> for</c><00:00:57.960><c> the</c>

00:00:58.110 --> 00:00:58.120 align:start position:0%
the right tool it should take uh for the
 

00:00:58.120 --> 00:01:00.509 align:start position:0%
the right tool it should take uh for the
given<00:00:58.359><c> user</c><00:00:58.680><c> message</c><00:00:59.519><c> and</c><00:00:59.640><c> then</c><00:00:59.760><c> it</c><00:01:00.079><c> takes</c><00:01:00.320><c> an</c>

00:01:00.509 --> 00:01:00.519 align:start position:0%
given user message and then it takes an
 

00:01:00.519 --> 00:01:02.630 align:start position:0%
given user message and then it takes an
action<00:01:01.239><c> by</c><00:01:01.359><c> using</c><00:01:01.640><c> the</c><00:01:01.800><c> tool</c><00:01:02.280><c> and</c><00:01:02.440><c> then</c>

00:01:02.630 --> 00:01:02.640 align:start position:0%
action by using the tool and then
 

00:01:02.640 --> 00:01:05.830 align:start position:0%
action by using the tool and then
observes<00:01:03.079><c> the</c><00:01:03.239><c> result</c><00:01:04.199><c> and</c><00:01:04.360><c> then</c><00:01:04.720><c> it</c><00:01:04.960><c> iterates</c>

00:01:05.830 --> 00:01:05.840 align:start position:0%
observes the result and then it iterates
 

00:01:05.840 --> 00:01:08.350 align:start position:0%
observes the result and then it iterates
uh<00:01:06.000><c> this</c><00:01:06.240><c> process</c><00:01:06.680><c> until</c><00:01:07.320><c> uh</c><00:01:07.439><c> you</c><00:01:07.600><c> get</c><00:01:07.720><c> a</c><00:01:07.960><c> final</c>

00:01:08.350 --> 00:01:08.360 align:start position:0%
uh this process until uh you get a final
 

00:01:08.360 --> 00:01:11.789 align:start position:0%
uh this process until uh you get a final
result<00:01:09.360><c> so</c><00:01:10.040><c> this</c><00:01:10.200><c> is</c><00:01:10.439><c> what</c><00:01:10.720><c> a</c><00:01:10.960><c> react</c><00:01:11.439><c> agent</c>

00:01:11.789 --> 00:01:11.799 align:start position:0%
result so this is what a react agent
 

00:01:11.799 --> 00:01:15.030 align:start position:0%
result so this is what a react agent
framework<00:01:12.720><c> uh</c><00:01:12.920><c> does</c><00:01:13.799><c> so</c><00:01:14.040><c> we'll</c><00:01:14.240><c> look</c><00:01:14.439><c> into</c><00:01:14.759><c> how</c>

00:01:15.030 --> 00:01:15.040 align:start position:0%
framework uh does so we'll look into how
 

00:01:15.040 --> 00:01:17.510 align:start position:0%
framework uh does so we'll look into how
you<00:01:15.159><c> can</c><00:01:15.479><c> use</c><00:01:15.840><c> react</c><00:01:16.200><c> agent</c><00:01:16.840><c> uh</c><00:01:17.000><c> with</c><00:01:17.159><c> llama</c>

00:01:17.510 --> 00:01:17.520 align:start position:0%
you can use react agent uh with llama
 

00:01:17.520 --> 00:01:19.749 align:start position:0%
you can use react agent uh with llama
index<00:01:17.840><c> abstractions</c><00:01:18.799><c> and</c><00:01:19.000><c> with</c><00:01:19.200><c> the</c><00:01:19.360><c> simple</c>

00:01:19.749 --> 00:01:19.759 align:start position:0%
index abstractions and with the simple
 

00:01:19.759 --> 00:01:22.390 align:start position:0%
index abstractions and with the simple
calculator<00:01:20.240><c> tools</c><00:01:20.600><c> and</c><00:01:21.400><c> rag</c><00:01:21.840><c> quer</c><00:01:22.119><c> engine</c>

00:01:22.390 --> 00:01:22.400 align:start position:0%
calculator tools and rag quer engine
 

00:01:22.400 --> 00:01:23.310 align:start position:0%
calculator tools and rag quer engine
tools

00:01:23.310 --> 00:01:23.320 align:start position:0%
tools
 

00:01:23.320 --> 00:01:27.429 align:start position:0%
tools
basically<00:01:24.320><c> so</c><00:01:24.600><c> let's</c><00:01:25.000><c> import</c><00:01:25.640><c> all</c><00:01:25.920><c> the</c><00:01:26.640><c> um</c>

00:01:27.429 --> 00:01:27.439 align:start position:0%
basically so let's import all the um
 

00:01:27.439 --> 00:01:30.149 align:start position:0%
basically so let's import all the um
Necessary<00:01:28.439><c> Things</c>

00:01:30.149 --> 00:01:30.159 align:start position:0%
Necessary Things
 

00:01:30.159 --> 00:01:33.550 align:start position:0%
Necessary Things
and<00:01:30.439><c> then</c><00:01:31.079><c> we'll</c><00:01:31.799><c> Define</c><00:01:32.439><c> these</c><00:01:32.640><c> tools</c><00:01:33.280><c> uh</c><00:01:33.439><c> the</c>

00:01:33.550 --> 00:01:33.560 align:start position:0%
and then we'll Define these tools uh the
 

00:01:33.560 --> 00:01:36.149 align:start position:0%
and then we'll Define these tools uh the
simple<00:01:33.960><c> calculator</c><00:01:34.479><c> tools</c><00:01:35.439><c> so</c><00:01:35.799><c> which</c><00:01:35.920><c> is</c>

00:01:36.149 --> 00:01:36.159 align:start position:0%
simple calculator tools so which is
 

00:01:36.159 --> 00:01:38.550 align:start position:0%
simple calculator tools so which is
given<00:01:36.439><c> two</c><00:01:36.640><c> integers</c><00:01:37.240><c> multiply</c><00:01:38.000><c> them</c><00:01:38.360><c> and</c>

00:01:38.550 --> 00:01:38.560 align:start position:0%
given two integers multiply them and
 

00:01:38.560 --> 00:01:40.990 align:start position:0%
given two integers multiply them and
then<00:01:38.960><c> addition</c><00:01:39.399><c> of</c><00:01:39.640><c> two</c><00:01:40.000><c> integers</c>

00:01:40.990 --> 00:01:41.000 align:start position:0%
then addition of two integers
 

00:01:41.000 --> 00:01:43.230 align:start position:0%
then addition of two integers
subtraction<00:01:41.600><c> of</c><00:01:41.799><c> two</c><00:01:42.280><c> different</c><00:01:42.720><c> integers</c>

00:01:43.230 --> 00:01:43.240 align:start position:0%
subtraction of two different integers
 

00:01:43.240 --> 00:01:45.870 align:start position:0%
subtraction of two different integers
and<00:01:43.439><c> then</c><00:01:44.000><c> um</c><00:01:44.399><c> give</c><00:01:44.600><c> the</c>

00:01:45.870 --> 00:01:45.880 align:start position:0%
and then um give the
 

00:01:45.880 --> 00:01:51.749 align:start position:0%
and then um give the
result<00:01:47.079><c> and</c><00:01:48.079><c> and</c><00:01:48.320><c> then</c><00:01:49.280><c> uh</c><00:01:49.479><c> we</c><00:01:49.960><c> since</c><00:01:50.399><c> once</c>

00:01:51.749 --> 00:01:51.759 align:start position:0%
result and and then uh we since once
 

00:01:51.759 --> 00:01:55.389 align:start position:0%
result and and then uh we since once
we<00:01:52.759><c> um</c><00:01:53.159><c> Define</c>

00:01:55.389 --> 00:01:55.399 align:start position:0%
we um Define
 

00:01:55.399 --> 00:01:59.109 align:start position:0%
we um Define
these<00:01:56.399><c> tools</c><00:01:57.200><c> functions</c><00:01:57.680><c> basically</c><00:01:58.200><c> we'll</c><00:01:58.880><c> uh</c>

00:01:59.109 --> 00:01:59.119 align:start position:0%
these tools functions basically we'll uh
 

00:01:59.119 --> 00:02:04.230 align:start position:0%
these tools functions basically we'll uh
create<00:01:59.439><c> tools</c><00:01:59.680><c> of</c><00:01:59.920><c> out</c><00:02:00.079><c> of</c>

00:02:04.230 --> 00:02:04.240 align:start position:0%
 
 

00:02:04.240 --> 00:02:07.070 align:start position:0%
 
it<00:02:05.240><c> and</c><00:02:05.479><c> then</c>

00:02:07.070 --> 00:02:07.080 align:start position:0%
it and then
 

00:02:07.080 --> 00:02:09.389 align:start position:0%
it and then
uh<00:02:08.080><c> once</c><00:02:08.280><c> you</c><00:02:08.440><c> have</c><00:02:08.640><c> tools</c><00:02:09.039><c> all</c><00:02:09.200><c> these</c>

00:02:09.389 --> 00:02:09.399 align:start position:0%
uh once you have tools all these
 

00:02:09.399 --> 00:02:11.630 align:start position:0%
uh once you have tools all these
different<00:02:09.679><c> tools</c><00:02:10.239><c> multiply</c><00:02:10.759><c> tool</c><00:02:11.080><c> add</c><00:02:11.280><c> tool</c>

00:02:11.630 --> 00:02:11.640 align:start position:0%
different tools multiply tool add tool
 

00:02:11.640 --> 00:02:14.869 align:start position:0%
different tools multiply tool add tool
and<00:02:11.840><c> subtract</c><00:02:12.280><c> tool</c><00:02:13.160><c> so</c><00:02:13.720><c> you</c><00:02:13.879><c> need</c><00:02:14.160><c> open</c><00:02:14.440><c> a</c><00:02:14.680><c> key</c>

00:02:14.869 --> 00:02:14.879 align:start position:0%
and subtract tool so you need open a key
 

00:02:14.879 --> 00:02:17.110 align:start position:0%
and subtract tool so you need open a key
and<00:02:15.120><c> Mell</c><00:02:15.560><c> and</c><00:02:15.720><c> anthropy</c><00:02:16.280><c> key</c><00:02:16.680><c> for</c><00:02:16.920><c> this</c>

00:02:17.110 --> 00:02:17.120 align:start position:0%
and Mell and anthropy key for this
 

00:02:17.120 --> 00:02:20.350 align:start position:0%
and Mell and anthropy key for this
notebook<00:02:17.599><c> so</c><00:02:18.599><c> yeah</c><00:02:19.080><c> we'll</c><00:02:19.319><c> use</c><00:02:19.840><c> uh</c><00:02:19.959><c> start</c><00:02:20.239><c> with</c>

00:02:20.350 --> 00:02:20.360 align:start position:0%
notebook so yeah we'll use uh start with
 

00:02:20.360 --> 00:02:24.190 align:start position:0%
notebook so yeah we'll use uh start with
GPT<00:02:20.920><c> 4</c><00:02:21.920><c> and</c><00:02:22.160><c> then</c><00:02:22.840><c> uh</c><00:02:23.280><c> we'll</c><00:02:23.519><c> create</c><00:02:23.720><c> a</c><00:02:23.879><c> react</c>

00:02:24.190 --> 00:02:24.200 align:start position:0%
GPT 4 and then uh we'll create a react
 

00:02:24.200 --> 00:02:26.509 align:start position:0%
GPT 4 and then uh we'll create a react
agent<00:02:24.519><c> out</c><00:02:24.680><c> of</c><00:02:24.879><c> these</c><00:02:25.040><c> tools</c><00:02:25.959><c> uh</c><00:02:26.160><c> whatever</c>

00:02:26.509 --> 00:02:26.519 align:start position:0%
agent out of these tools uh whatever
 

00:02:26.519 --> 00:02:29.949 align:start position:0%
agent out of these tools uh whatever
three<00:02:26.760><c> defin</c><00:02:27.160><c> tools</c><00:02:27.480><c> we</c><00:02:27.680><c> have</c><00:02:28.000><c> here</c>

00:02:29.949 --> 00:02:29.959 align:start position:0%
three defin tools we have here
 

00:02:29.959 --> 00:02:31.229 align:start position:0%
three defin tools we have here
and

00:02:31.229 --> 00:02:31.239 align:start position:0%
and
 

00:02:31.239 --> 00:02:35.229 align:start position:0%
and
then<00:02:32.239><c> now</c><00:02:32.480><c> we'll</c><00:02:33.360><c> query</c><00:02:33.720><c> it</c><00:02:33.959><c> with</c><00:02:34.480><c> one</c><00:02:34.840><c> simple</c>

00:02:35.229 --> 00:02:35.239 align:start position:0%
then now we'll query it with one simple
 

00:02:35.239 --> 00:02:37.270 align:start position:0%
then now we'll query it with one simple
query<00:02:35.800><c> what</c><00:02:36.000><c> is</c><00:02:36.280><c> 20</c>

00:02:37.270 --> 00:02:37.280 align:start position:0%
query what is 20
 

00:02:37.280 --> 00:02:41.550 align:start position:0%
query what is 20
plus<00:02:38.280><c> brackets</c><00:02:39.120><c> uh</c><00:02:39.280><c> to</c><00:02:40.040><c> multiply</c><00:02:40.599><c> for</c><00:02:40.800><c> four</c>

00:02:41.550 --> 00:02:41.560 align:start position:0%
plus brackets uh to multiply for four
 

00:02:41.560 --> 00:02:42.990 align:start position:0%
plus brackets uh to multiply for four
calculate<00:02:41.959><c> step</c><00:02:42.200><c> by</c>

00:02:42.990 --> 00:02:43.000 align:start position:0%
calculate step by
 

00:02:43.000 --> 00:02:47.390 align:start position:0%
calculate step by
step<00:02:44.000><c> and</c><00:02:44.200><c> then</c><00:02:44.599><c> uh</c><00:02:44.760><c> you</c><00:02:44.920><c> can</c><00:02:45.120><c> see</c><00:02:46.040><c> um</c>

00:02:47.390 --> 00:02:47.400 align:start position:0%
step and then uh you can see um
 

00:02:47.400 --> 00:02:51.149 align:start position:0%
step and then uh you can see um
that<00:02:48.400><c> so</c><00:02:48.680><c> it</c><00:02:48.879><c> first</c><00:02:49.159><c> takes</c><00:02:50.080><c> uh</c><00:02:50.239><c> 2</c><00:02:50.480><c> and</c><00:02:50.680><c> four</c><00:02:51.000><c> as</c>

00:02:51.149 --> 00:02:51.159 align:start position:0%
that so it first takes uh 2 and four as
 

00:02:51.159 --> 00:02:56.350 align:start position:0%
that so it first takes uh 2 and four as
the<00:02:51.360><c> input</c><00:02:52.360><c> uh</c><00:02:52.879><c> observes</c><00:02:53.640><c> eight</c><00:02:54.640><c> and</c><00:02:54.840><c> then</c><00:02:55.440><c> um</c>

00:02:56.350 --> 00:02:56.360 align:start position:0%
the input uh observes eight and then um
 

00:02:56.360 --> 00:03:00.470 align:start position:0%
the input uh observes eight and then um
then<00:02:56.560><c> sees</c><00:02:56.959><c> that</c><00:02:57.200><c> 20</c><00:02:57.920><c> added</c><00:02:58.319><c> with</c><00:02:59.040><c> uh</c>

00:03:00.470 --> 00:03:00.480 align:start position:0%
then sees that 20 added with uh
 

00:03:00.480 --> 00:03:02.710 align:start position:0%
then sees that 20 added with uh
whatever<00:03:00.920><c> eight</c><00:03:01.280><c> result</c><00:03:01.640><c> is</c><00:03:01.840><c> there</c><00:03:02.159><c> and</c><00:03:02.400><c> use</c>

00:03:02.710 --> 00:03:02.720 align:start position:0%
whatever eight result is there and use
 

00:03:02.720 --> 00:03:06.910 align:start position:0%
whatever eight result is there and use
the<00:03:02.879><c> add</c><00:03:03.440><c> tool</c><00:03:04.440><c> and</c><00:03:04.640><c> then</c><00:03:05.200><c> um</c><00:03:05.720><c> takes</c><00:03:06.640><c> both</c>

00:03:06.910 --> 00:03:06.920 align:start position:0%
the add tool and then um takes both
 

00:03:06.920 --> 00:03:09.430 align:start position:0%
the add tool and then um takes both
these<00:03:07.120><c> inputs</c><00:03:07.599><c> and</c><00:03:07.920><c> observe</c><00:03:08.280><c> that</c><00:03:08.440><c> 28</c><00:03:08.840><c> is</c><00:03:08.959><c> a</c>

00:03:09.430 --> 00:03:09.440 align:start position:0%
these inputs and observe that 28 is a
 

00:03:09.440 --> 00:03:12.670 align:start position:0%
these inputs and observe that 28 is a
result<00:03:10.440><c> and</c><00:03:10.680><c> since</c><00:03:11.120><c> this</c><00:03:11.879><c> uh</c><00:03:12.040><c> there</c><00:03:12.159><c> are</c><00:03:12.319><c> no</c>

00:03:12.670 --> 00:03:12.680 align:start position:0%
result and since this uh there are no
 

00:03:12.680 --> 00:03:15.350 align:start position:0%
result and since this uh there are no
other<00:03:12.920><c> tools</c><00:03:13.239><c> that</c><00:03:13.400><c> are</c><00:03:14.040><c> can</c><00:03:14.200><c> be</c><00:03:14.360><c> used</c><00:03:14.680><c> to</c><00:03:15.159><c> give</c>

00:03:15.350 --> 00:03:15.360 align:start position:0%
other tools that are can be used to give
 

00:03:15.360 --> 00:03:17.869 align:start position:0%
other tools that are can be used to give
the<00:03:15.519><c> final</c><00:03:15.840><c> result</c><00:03:16.480><c> it</c><00:03:16.640><c> says</c><00:03:17.480><c> this</c><00:03:17.599><c> is</c><00:03:17.720><c> the</c>

00:03:17.869 --> 00:03:17.879 align:start position:0%
the final result it says this is the
 

00:03:17.879 --> 00:03:20.990 align:start position:0%
the final result it says this is the
final<00:03:18.760><c> result</c><00:03:19.040><c> for</c><00:03:19.239><c> the</c><00:03:19.680><c> expression</c><00:03:20.680><c> so</c><00:03:20.920><c> you</c>

00:03:20.990 --> 00:03:21.000 align:start position:0%
final result for the expression so you
 

00:03:21.000 --> 00:03:24.550 align:start position:0%
final result for the expression so you
can<00:03:21.200><c> print</c><00:03:21.440><c> out</c><00:03:21.640><c> the</c><00:03:21.959><c> result</c><00:03:22.959><c> and</c><00:03:23.879><c> that's</c><00:03:24.040><c> how</c>

00:03:24.550 --> 00:03:24.560 align:start position:0%
can print out the result and that's how
 

00:03:24.560 --> 00:03:27.789 align:start position:0%
can print out the result and that's how
it<00:03:24.720><c> takes</c><00:03:25.280><c> thought</c><00:03:25.879><c> action</c><00:03:26.280><c> and</c><00:03:26.560><c> observation</c>

00:03:27.789 --> 00:03:27.799 align:start position:0%
it takes thought action and observation
 

00:03:27.799 --> 00:03:30.270 align:start position:0%
it takes thought action and observation
iteratively<00:03:28.799><c> and</c><00:03:29.040><c> gives</c><00:03:29.239><c> a</c><00:03:29.360><c> final</c><00:03:29.599><c> result</c>

00:03:30.270 --> 00:03:30.280 align:start position:0%
iteratively and gives a final result
 

00:03:30.280 --> 00:03:33.070 align:start position:0%
iteratively and gives a final result
let's<00:03:30.519><c> try</c><00:03:31.080><c> one</c><00:03:31.239><c> more</c><00:03:31.480><c> example</c><00:03:31.959><c> which</c><00:03:32.120><c> is</c><00:03:32.280><c> 40</c><00:03:32.640><c> +</c>

00:03:33.070 --> 00:03:33.080 align:start position:0%
let's try one more example which is 40 +
 

00:03:33.080 --> 00:03:37.470 align:start position:0%
let's try one more example which is 40 +
100<00:03:33.319><c> -</c><00:03:33.640><c> 30</c><00:03:34.560><c> star</c>

00:03:37.470 --> 00:03:37.480 align:start position:0%
 
 

00:03:37.480 --> 00:03:42.670 align:start position:0%
 
five<00:03:38.959><c> so</c><00:03:39.959><c> the</c><00:03:40.159><c> same</c><00:03:40.480><c> way</c><00:03:41.000><c> it</c><00:03:41.159><c> first</c><00:03:41.360><c> took</c><00:03:41.680><c> 130</c>

00:03:42.670 --> 00:03:42.680 align:start position:0%
five so the same way it first took 130
 

00:03:42.680 --> 00:03:45.390 align:start position:0%
five so the same way it first took 130
uh<00:03:42.879><c> use</c><00:03:43.159><c> substract</c><00:03:43.760><c> tool</c><00:03:44.239><c> and</c><00:03:44.439><c> then</c><00:03:44.920><c> observe</c>

00:03:45.390 --> 00:03:45.400 align:start position:0%
uh use substract tool and then observe
 

00:03:45.400 --> 00:03:49.550 align:start position:0%
uh use substract tool and then observe
result<00:03:45.760><c> as</c><00:03:46.120><c> 70</c><00:03:47.120><c> and</c><00:03:47.519><c> then</c><00:03:48.519><c> multiply</c><00:03:49.239><c> which</c><00:03:49.360><c> is</c>

00:03:49.550 --> 00:03:49.560 align:start position:0%
result as 70 and then multiply which is
 

00:03:49.560 --> 00:03:53.550 align:start position:0%
result as 70 and then multiply which is
70<00:03:50.080><c> Star</c><00:03:50.519><c> 5</c><00:03:51.120><c> which</c><00:03:51.280><c> is</c><00:03:51.400><c> 350</c><00:03:52.200><c> and</c><00:03:52.400><c> then</c><00:03:52.920><c> add</c><00:03:53.159><c> 40</c>

00:03:53.550 --> 00:03:53.560 align:start position:0%
70 Star 5 which is 350 and then add 40
 

00:03:53.560 --> 00:03:56.429 align:start position:0%
70 Star 5 which is 350 and then add 40
to<00:03:53.840><c> it</c><00:03:54.400><c> using</c><00:03:54.760><c> add</c><00:03:55.040><c> tool</c><00:03:55.879><c> and</c><00:03:56.000><c> then</c><00:03:56.159><c> get</c><00:03:56.280><c> a</c>

00:03:56.429 --> 00:03:56.439 align:start position:0%
to it using add tool and then get a
 

00:03:56.439 --> 00:03:57.390 align:start position:0%
to it using add tool and then get a
final

00:03:57.390 --> 00:03:57.400 align:start position:0%
final
 

00:03:57.400 --> 00:04:01.350 align:start position:0%
final
answer<00:03:58.720><c> so</c><00:03:59.799><c> that's</c><00:04:00.000><c> how</c><00:04:00.599><c> uh</c><00:04:00.720><c> you</c><00:04:00.840><c> can</c><00:04:01.000><c> use</c>

00:04:01.350 --> 00:04:01.360 align:start position:0%
answer so that's how uh you can use
 

00:04:01.360 --> 00:04:04.990 align:start position:0%
answer so that's how uh you can use
Simple<00:04:01.959><c> uh</c><00:04:02.120><c> react</c><00:04:02.680><c> agent</c><00:04:03.680><c> um</c><00:04:03.840><c> with</c><00:04:04.040><c> open</c><00:04:04.319><c> a</c><00:04:04.799><c> and</c>

00:04:04.990 --> 00:04:05.000 align:start position:0%
Simple uh react agent um with open a and
 

00:04:05.000 --> 00:04:10.710 align:start position:0%
Simple uh react agent um with open a and
then<00:04:06.200><c> um</c><00:04:07.200><c> start</c><00:04:07.599><c> quing</c><00:04:08.400><c> it</c><00:04:09.400><c> and</c><00:04:09.560><c> next</c><00:04:09.799><c> we'll</c><00:04:10.480><c> uh</c>

00:04:10.710 --> 00:04:10.720 align:start position:0%
then um start quing it and next we'll uh
 

00:04:10.720 --> 00:04:14.270 align:start position:0%
then um start quing it and next we'll uh
go<00:04:10.920><c> with</c><00:04:11.439><c> uh</c><00:04:11.560><c> anthropic</c><00:04:12.040><c> Sonet</c><00:04:12.879><c> model</c><00:04:13.879><c> um</c><00:04:14.120><c> all</c>

00:04:14.270 --> 00:04:14.280 align:start position:0%
go with uh anthropic Sonet model um all
 

00:04:14.280 --> 00:04:16.590 align:start position:0%
go with uh anthropic Sonet model um all
you<00:04:14.360><c> need</c><00:04:14.519><c> to</c><00:04:14.680><c> do</c><00:04:15.000><c> is</c><00:04:15.239><c> change</c><00:04:15.519><c> the</c><00:04:15.680><c> LM</c><00:04:16.239><c> in</c><00:04:16.400><c> the</c>

00:04:16.590 --> 00:04:16.600 align:start position:0%
you need to do is change the LM in the
 

00:04:16.600 --> 00:04:19.150 align:start position:0%
you need to do is change the LM in the
same<00:04:16.840><c> react</c><00:04:17.199><c> agent</c><00:04:17.519><c> from</c><00:04:17.680><c> tools</c><00:04:18.519><c> whatever</c><00:04:18.799><c> LM</c>

00:04:19.150 --> 00:04:19.160 align:start position:0%
same react agent from tools whatever LM
 

00:04:19.160 --> 00:04:22.550 align:start position:0%
same react agent from tools whatever LM
we<00:04:19.239><c> are</c><00:04:19.600><c> calling</c><00:04:20.600><c> and</c><00:04:20.919><c> then</c><00:04:21.919><c> let's</c><00:04:22.199><c> run</c>

00:04:22.550 --> 00:04:22.560 align:start position:0%
we are calling and then let's run
 

00:04:22.560 --> 00:04:24.510 align:start position:0%
we are calling and then let's run
similar<00:04:22.919><c> queries</c><00:04:23.360><c> again</c>

00:04:24.510 --> 00:04:24.520 align:start position:0%
similar queries again
 

00:04:24.520 --> 00:04:29.830 align:start position:0%
similar queries again
here<00:04:25.520><c> so</c><00:04:25.759><c> what</c><00:04:25.960><c> is</c><00:04:26.280><c> 20</c><00:04:26.639><c> +</c><00:04:27.199><c> 2</c><00:04:27.479><c> *</c><00:04:28.120><c> 4</c>

00:04:29.830 --> 00:04:29.840 align:start position:0%
here so what is 20 + 2 * 4
 

00:04:29.840 --> 00:04:33.110 align:start position:0%
here so what is 20 + 2 * 4
so<00:04:30.800><c> here</c><00:04:31.039><c> it</c><00:04:31.199><c> says</c><00:04:31.759><c> the</c><00:04:31.919><c> same</c><00:04:32.199><c> thing</c><00:04:32.680><c> uh</c><00:04:32.919><c> use</c>

00:04:33.110 --> 00:04:33.120 align:start position:0%
so here it says the same thing uh use
 

00:04:33.120 --> 00:04:35.150 align:start position:0%
so here it says the same thing uh use
the<00:04:33.240><c> multiply</c><00:04:33.800><c> tool</c><00:04:34.160><c> first</c><00:04:34.520><c> and</c><00:04:34.720><c> then</c>

00:04:35.150 --> 00:04:35.160 align:start position:0%
the multiply tool first and then
 

00:04:35.160 --> 00:04:38.390 align:start position:0%
the multiply tool first and then
whatever<00:04:35.600><c> result</c><00:04:36.240><c> you</c><00:04:36.440><c> add</c><00:04:36.639><c> it</c><00:04:37.320><c> uh</c><00:04:37.720><c> take</c><00:04:37.960><c> 28</c>

00:04:38.390 --> 00:04:38.400 align:start position:0%
whatever result you add it uh take 28
 

00:04:38.400 --> 00:04:40.990 align:start position:0%
whatever result you add it uh take 28
and<00:04:38.560><c> add</c><00:04:38.720><c> it</c><00:04:38.840><c> to</c><00:04:39.360><c> 28</c>

00:04:40.990 --> 00:04:41.000 align:start position:0%
and add it to 28
 

00:04:41.000 --> 00:04:43.950 align:start position:0%
and add it to 28
so<00:04:42.000><c> and</c><00:04:42.199><c> then</c><00:04:42.880><c> yeah</c><00:04:43.160><c> this</c><00:04:43.280><c> is</c><00:04:43.360><c> the</c><00:04:43.520><c> final</c>

00:04:43.950 --> 00:04:43.960 align:start position:0%
so and then yeah this is the final
 

00:04:43.960 --> 00:04:46.310 align:start position:0%
so and then yeah this is the final
answer

00:04:46.310 --> 00:04:46.320 align:start position:0%
answer
 

00:04:46.320 --> 00:04:49.390 align:start position:0%
answer
right<00:04:47.320><c> uh</c><00:04:47.520><c> the</c><00:04:47.680><c> next</c><00:04:47.960><c> example</c><00:04:48.840><c> uh</c><00:04:49.000><c> the</c><00:04:49.120><c> same</c>

00:04:49.390 --> 00:04:49.400 align:start position:0%
right uh the next example uh the same
 

00:04:49.400 --> 00:04:53.510 align:start position:0%
right uh the next example uh the same
one<00:04:49.680><c> which</c><00:04:49.800><c> is</c><00:04:49.960><c> 40</c><00:04:50.320><c> +</c><00:04:50.720><c> 100</c><00:04:51.000><c> -</c><00:04:51.320><c> 30</c><00:04:51.680><c> Star</c><00:04:52.320><c> 5</c><00:04:53.320><c> and</c>

00:04:53.510 --> 00:04:53.520 align:start position:0%
one which is 40 + 100 - 30 Star 5 and
 

00:04:53.520 --> 00:04:57.230 align:start position:0%
one which is 40 + 100 - 30 Star 5 and
then<00:04:54.199><c> let's</c><00:04:54.520><c> look</c><00:04:54.759><c> for</c><00:04:55.080><c> response</c><00:04:55.639><c> as</c><00:04:56.000><c> well</c><00:04:57.000><c> so</c>

00:04:57.230 --> 00:04:57.240 align:start position:0%
then let's look for response as well so
 

00:04:57.240 --> 00:04:59.270 align:start position:0%
then let's look for response as well so
it<00:04:57.400><c> use</c><00:04:57.680><c> subtract</c><00:04:58.160><c> Tool</c><00:04:58.720><c> uh</c><00:04:58.880><c> which</c><00:04:59.000><c> is</c><00:04:59.120><c> in</c>

00:04:59.270 --> 00:04:59.280 align:start position:0%
it use subtract Tool uh which is in
 

00:04:59.280 --> 00:05:02.390 align:start position:0%
it use subtract Tool uh which is in
bracket<00:05:00.080><c> and</c><00:05:00.280><c> then</c><00:05:00.479><c> multiply</c><00:05:01.039><c> tool</c><00:05:01.400><c> and</c><00:05:01.600><c> then</c>

00:05:02.390 --> 00:05:02.400 align:start position:0%
bracket and then multiply tool and then
 

00:05:02.400 --> 00:05:05.430 align:start position:0%
bracket and then multiply tool and then
addition<00:05:02.880><c> tool</c><00:05:03.880><c> and</c><00:05:04.080><c> then</c><00:05:04.800><c> gets</c><00:05:05.000><c> a</c><00:05:05.160><c> final</c>

00:05:05.430 --> 00:05:05.440 align:start position:0%
addition tool and then gets a final
 

00:05:05.440 --> 00:05:08.790 align:start position:0%
addition tool and then gets a final
response<00:05:05.759><c> of</c><00:05:06.240><c> 390</c><00:05:07.240><c> right</c><00:05:08.000><c> so</c><00:05:08.199><c> let's</c><00:05:08.440><c> do</c>

00:05:08.790 --> 00:05:08.800 align:start position:0%
response of 390 right so let's do
 

00:05:08.800 --> 00:05:12.629 align:start position:0%
response of 390 right so let's do
similar<00:05:09.240><c> thing</c><00:05:09.680><c> with</c><00:05:10.120><c> the</c><00:05:10.320><c> latest</c><00:05:11.160><c> uh</c><00:05:11.639><c> mistal</c>

00:05:12.629 --> 00:05:12.639 align:start position:0%
similar thing with the latest uh mistal
 

00:05:12.639 --> 00:05:16.710 align:start position:0%
similar thing with the latest uh mistal
llm<00:05:13.199><c> as</c><00:05:13.479><c> well</c>

00:05:16.710 --> 00:05:16.720 align:start position:0%
 
 

00:05:16.720 --> 00:05:19.430 align:start position:0%
 
so<00:05:17.720><c> in</c><00:05:17.840><c> the</c><00:05:18.000><c> similar</c><00:05:18.360><c> way</c><00:05:18.560><c> we'll</c><00:05:18.960><c> exchange</c><00:05:19.319><c> the</c>

00:05:19.430 --> 00:05:19.440 align:start position:0%
so in the similar way we'll exchange the
 

00:05:19.440 --> 00:05:22.390 align:start position:0%
so in the similar way we'll exchange the
llm<00:05:19.880><c> with</c><00:05:20.039><c> the</c><00:05:20.199><c> Mr</c><00:05:20.600><c> Large</c>

00:05:22.390 --> 00:05:22.400 align:start position:0%
llm with the Mr Large
 

00:05:22.400 --> 00:05:26.510 align:start position:0%
llm with the Mr Large
latest<00:05:23.400><c> so</c><00:05:23.720><c> it</c><00:05:23.919><c> is</c><00:05:24.120><c> also</c><00:05:24.919><c> using</c><00:05:25.919><c> multiply</c>

00:05:26.510 --> 00:05:26.520 align:start position:0%
latest so it is also using multiply
 

00:05:26.520 --> 00:05:28.670 align:start position:0%
latest so it is also using multiply
first<00:05:26.800><c> and</c><00:05:26.960><c> then</c><00:05:27.160><c> addition</c><00:05:27.840><c> and</c><00:05:28.000><c> then</c><00:05:28.319><c> finally</c>

00:05:28.670 --> 00:05:28.680 align:start position:0%
first and then addition and then finally
 

00:05:28.680 --> 00:05:30.670 align:start position:0%
first and then addition and then finally
provided<00:05:29.080><c> a</c><00:05:29.280><c> result</c>

00:05:30.670 --> 00:05:30.680 align:start position:0%
provided a result
 

00:05:30.680 --> 00:05:32.710 align:start position:0%
provided a result
and<00:05:30.880><c> then</c><00:05:31.160><c> the</c><00:05:31.319><c> same</c><00:05:31.680><c> example</c><00:05:32.160><c> a</c><00:05:32.319><c> previous</c>

00:05:32.710 --> 00:05:32.720 align:start position:0%
and then the same example a previous
 

00:05:32.720 --> 00:05:35.629 align:start position:0%
and then the same example a previous
example<00:05:33.080><c> 40</c><00:05:33.400><c> +</c><00:05:33.680><c> 100</c><00:05:33.919><c> -</c><00:05:34.199><c> 30</c><00:05:34.560><c> Star</c>

00:05:35.629 --> 00:05:35.639 align:start position:0%
example 40 + 100 - 30 Star
 

00:05:35.639 --> 00:05:40.309 align:start position:0%
example 40 + 100 - 30 Star
5<00:05:36.639><c> yeah</c><00:05:37.240><c> so</c><00:05:38.240><c> it</c><00:05:38.400><c> uses</c><00:05:38.919><c> subtract</c><00:05:39.360><c> tool</c><00:05:39.720><c> first</c>

00:05:40.309 --> 00:05:40.319 align:start position:0%
5 yeah so it uses subtract tool first
 

00:05:40.319 --> 00:05:43.830 align:start position:0%
5 yeah so it uses subtract tool first
then<00:05:40.759><c> multiply</c><00:05:41.759><c> then</c><00:05:42.240><c> addition</c><00:05:43.240><c> and</c><00:05:43.520><c> finally</c>

00:05:43.830 --> 00:05:43.840 align:start position:0%
then multiply then addition and finally
 

00:05:43.840 --> 00:05:46.070 align:start position:0%
then multiply then addition and finally
provided<00:05:44.240><c> a</c>

00:05:46.070 --> 00:05:46.080 align:start position:0%
provided a
 

00:05:46.080 --> 00:05:50.070 align:start position:0%
provided a
response<00:05:47.080><c> right</c><00:05:48.039><c> so</c><00:05:49.039><c> so</c><00:05:49.319><c> yeah</c><00:05:49.560><c> you</c><00:05:49.680><c> can</c><00:05:49.840><c> even</c>

00:05:50.070 --> 00:05:50.080 align:start position:0%
response right so so yeah you can even
 

00:05:50.080 --> 00:05:52.670 align:start position:0%
response right so so yeah you can even
check<00:05:50.280><c> the</c><00:05:50.440><c> whole</c><00:05:51.039><c> uh</c><00:05:51.280><c> whatever</c><00:05:51.840><c> prompt</c><00:05:52.520><c> uh</c>

00:05:52.670 --> 00:05:52.680 align:start position:0%
check the whole uh whatever prompt uh
 

00:05:52.680 --> 00:05:54.469 align:start position:0%
check the whole uh whatever prompt uh
that<00:05:52.840><c> we</c><00:05:52.960><c> have</c><00:05:53.120><c> provided</c><00:05:53.440><c> the</c><00:05:53.520><c> react</c><00:05:53.880><c> agent</c>

00:05:54.469 --> 00:05:54.479 align:start position:0%
that we have provided the react agent
 

00:05:54.479 --> 00:05:58.510 align:start position:0%
that we have provided the react agent
prompt<00:05:54.919><c> as</c><00:05:55.080><c> well</c><00:05:55.360><c> here</c><00:05:56.319><c> so</c><00:05:56.680><c> you</c><00:05:56.800><c> can</c><00:05:57.400><c> see</c><00:05:58.400><c> the</c>

00:05:58.510 --> 00:05:58.520 align:start position:0%
prompt as well here so you can see the
 

00:05:58.520 --> 00:06:01.309 align:start position:0%
prompt as well here so you can see the
whole<00:05:59.199><c> prompt</c><00:06:00.080><c> I</c><00:06:00.160><c> mean</c><00:06:00.560><c> you</c><00:06:00.720><c> are</c><00:06:00.840><c> designed</c><00:06:01.160><c> to</c>

00:06:01.309 --> 00:06:01.319 align:start position:0%
whole prompt I mean you are designed to
 

00:06:01.319 --> 00:06:02.990 align:start position:0%
whole prompt I mean you are designed to
help<00:06:01.520><c> with</c><00:06:01.680><c> variety</c><00:06:02.240><c> of</c><00:06:02.479><c> tasks</c><00:06:02.800><c> from</c>

00:06:02.990 --> 00:06:03.000 align:start position:0%
help with variety of tasks from
 

00:06:03.000 --> 00:06:04.029 align:start position:0%
help with variety of tasks from
answering<00:06:03.319><c> questions</c><00:06:03.600><c> to</c><00:06:03.759><c> providing</c>

00:06:04.029 --> 00:06:04.039 align:start position:0%
answering questions to providing
 

00:06:04.039 --> 00:06:06.629 align:start position:0%
answering questions to providing
summaries<00:06:04.479><c> to</c><00:06:05.000><c> other</c><00:06:05.240><c> types</c><00:06:05.440><c> of</c><00:06:05.639><c> analysis</c><00:06:06.400><c> so</c>

00:06:06.629 --> 00:06:06.639 align:start position:0%
summaries to other types of analysis so
 

00:06:06.639 --> 00:06:09.510 align:start position:0%
summaries to other types of analysis so
these<00:06:06.800><c> are</c><00:06:06.960><c> the</c><00:06:07.080><c> tools</c><00:06:08.080><c> available</c><00:06:09.080><c> um</c><00:06:09.360><c> you</c>

00:06:09.510 --> 00:06:09.520 align:start position:0%
these are the tools available um you
 

00:06:09.520 --> 00:06:11.230 align:start position:0%
these are the tools available um you
have<00:06:09.680><c> tool</c><00:06:10.080><c> description</c><00:06:10.680><c> and</c><00:06:10.840><c> this</c><00:06:10.960><c> is</c><00:06:11.080><c> the</c>

00:06:11.230 --> 00:06:11.240 align:start position:0%
have tool description and this is the
 

00:06:11.240 --> 00:06:14.749 align:start position:0%
have tool description and this is the
framework<00:06:11.840><c> that</c><00:06:12.120><c> you</c><00:06:12.240><c> are</c><00:06:12.400><c> going</c><00:06:12.639><c> to</c><00:06:13.240><c> use</c><00:06:14.240><c> and</c>

00:06:14.749 --> 00:06:14.759 align:start position:0%
framework that you are going to use and
 

00:06:14.759 --> 00:06:17.950 align:start position:0%
framework that you are going to use and
uh<00:06:15.280><c> and</c><00:06:15.440><c> then</c><00:06:16.199><c> iteratively</c><00:06:16.880><c> use</c><00:06:17.160><c> it</c><00:06:17.639><c> and</c><00:06:17.800><c> then</c>

00:06:17.950 --> 00:06:17.960 align:start position:0%
uh and then iteratively use it and then
 

00:06:17.960 --> 00:06:19.670 align:start position:0%
uh and then iteratively use it and then
what<00:06:18.080><c> is</c><00:06:18.160><c> a</c><00:06:18.319><c> current</c><00:06:18.599><c> current</c>

00:06:19.670 --> 00:06:19.680 align:start position:0%
what is a current current
 

00:06:19.680 --> 00:06:22.950 align:start position:0%
what is a current current
conversation<00:06:20.759><c> so</c><00:06:21.759><c> this</c><00:06:21.880><c> is</c><00:06:22.000><c> the</c><00:06:22.479><c> high</c><00:06:22.720><c> level</c>

00:06:22.950 --> 00:06:22.960 align:start position:0%
conversation so this is the high level
 

00:06:22.960 --> 00:06:24.029 align:start position:0%
conversation so this is the high level
prompt

00:06:24.029 --> 00:06:24.039 align:start position:0%
prompt
 

00:06:24.039 --> 00:06:27.070 align:start position:0%
prompt
and<00:06:25.039><c> we'll</c><00:06:25.440><c> include</c><00:06:26.240><c> tool</c><00:06:26.599><c> description</c>

00:06:27.070 --> 00:06:27.080 align:start position:0%
and we'll include tool description
 

00:06:27.080 --> 00:06:28.309 align:start position:0%
and we'll include tool description
different<00:06:27.360><c> tool</c><00:06:27.599><c> descriptions</c><00:06:28.039><c> and</c><00:06:28.199><c> the</c>

00:06:28.309 --> 00:06:28.319 align:start position:0%
different tool descriptions and the
 

00:06:28.319 --> 00:06:30.790 align:start position:0%
different tool descriptions and the
users<00:06:28.720><c> message</c>

00:06:30.790 --> 00:06:30.800 align:start position:0%
users message
 

00:06:30.800 --> 00:06:33.510 align:start position:0%
users message
into<00:06:31.120><c> it</c><00:06:31.960><c> uh</c><00:06:32.160><c> to</c><00:06:32.360><c> get</c><00:06:32.560><c> the</c><00:06:32.759><c> results</c><00:06:33.199><c> whatever</c>

00:06:33.510 --> 00:06:33.520 align:start position:0%
into it uh to get the results whatever
 

00:06:33.520 --> 00:06:35.189 align:start position:0%
into it uh to get the results whatever
we<00:06:33.639><c> have</c><00:06:33.840><c> observed</c>

00:06:35.189 --> 00:06:35.199 align:start position:0%
we have observed
 

00:06:35.199 --> 00:06:38.469 align:start position:0%
we have observed
above<00:06:36.199><c> right</c><00:06:36.720><c> so</c><00:06:36.919><c> you</c><00:06:37.039><c> can</c><00:06:37.479><c> inspect</c><00:06:37.960><c> in</c><00:06:38.120><c> detail</c>

00:06:38.469 --> 00:06:38.479 align:start position:0%
above right so you can inspect in detail
 

00:06:38.479 --> 00:06:41.589 align:start position:0%
above right so you can inspect in detail
about<00:06:38.800><c> it</c><00:06:39.800><c> and</c><00:06:40.240><c> uh</c><00:06:40.479><c> now</c><00:06:40.680><c> that</c><00:06:40.840><c> we</c><00:06:41.000><c> have</c><00:06:41.280><c> used</c>

00:06:41.589 --> 00:06:41.599 align:start position:0%
about it and uh now that we have used
 

00:06:41.599 --> 00:06:43.830 align:start position:0%
about it and uh now that we have used
react<00:06:41.960><c> agent</c><00:06:42.319><c> with</c><00:06:42.800><c> uh</c><00:06:42.960><c> simple</c><00:06:43.360><c> calculator</c>

00:06:43.830 --> 00:06:43.840 align:start position:0%
react agent with uh simple calculator
 

00:06:43.840 --> 00:06:47.029 align:start position:0%
react agent with uh simple calculator
tools<00:06:44.639><c> uh</c><00:06:44.840><c> next</c><00:06:45.639><c> let's</c><00:06:45.840><c> go</c><00:06:46.039><c> with</c><00:06:46.520><c> rack</c><00:06:46.759><c> quir</c>

00:06:47.029 --> 00:06:47.039 align:start position:0%
tools uh next let's go with rack quir
 

00:06:47.039 --> 00:06:50.950 align:start position:0%
tools uh next let's go with rack quir
engine<00:06:47.599><c> tools</c><00:06:48.599><c> so</c><00:06:49.560><c> we'll</c>

00:06:50.950 --> 00:06:50.960 align:start position:0%
engine tools so we'll
 

00:06:50.960 --> 00:06:54.909 align:start position:0%
engine tools so we'll
basically<00:06:51.960><c> build</c><00:06:52.520><c> uh</c><00:06:52.720><c> quir</c><00:06:53.080><c> engines</c><00:06:53.919><c> U</c><00:06:54.280><c> on</c>

00:06:54.909 --> 00:06:54.919 align:start position:0%
basically build uh quir engines U on
 

00:06:54.919 --> 00:06:58.550 align:start position:0%
basically build uh quir engines U on
Uber<00:06:55.879><c> and</c><00:06:56.080><c> lift</c><00:06:56.400><c> and</c><00:06:56.599><c> KCC</c><00:06:57.319><c> filings</c><00:06:58.319><c> we'll</c>

00:06:58.550 --> 00:06:58.560 align:start position:0%
Uber and lift and KCC filings we'll
 

00:06:58.560 --> 00:07:01.469 align:start position:0%
Uber and lift and KCC filings we'll
first<00:06:58.840><c> create</c><00:06:59.120><c> the</c><00:06:59.280><c> qu</c><00:06:59.680><c> engines</c><00:07:00.599><c> on</c><00:07:00.960><c> on</c><00:07:01.160><c> top</c><00:07:01.319><c> of</c>

00:07:01.469 --> 00:07:01.479 align:start position:0%
first create the qu engines on on top of
 

00:07:01.479 --> 00:07:04.309 align:start position:0%
first create the qu engines on on top of
this<00:07:01.680><c> data</c><00:07:02.039><c> let's</c><00:07:02.280><c> download</c><00:07:02.759><c> this</c><00:07:03.080><c> data</c><00:07:04.080><c> and</c>

00:07:04.309 --> 00:07:04.319 align:start position:0%
this data let's download this data and
 

00:07:04.319 --> 00:07:05.869 align:start position:0%
this data let's download this data and
then<00:07:04.680><c> load</c>

00:07:05.869 --> 00:07:05.879 align:start position:0%
then load
 

00:07:05.879 --> 00:07:09.390 align:start position:0%
then load
them<00:07:06.879><c> uh</c><00:07:07.120><c> this</c><00:07:07.319><c> might</c><00:07:07.680><c> take</c><00:07:08.280><c> uh</c><00:07:08.800><c> a</c><00:07:08.960><c> minute</c><00:07:09.240><c> or</c>

00:07:09.390 --> 00:07:09.400 align:start position:0%
them uh this might take uh a minute or
 

00:07:09.400 --> 00:07:12.230 align:start position:0%
them uh this might take uh a minute or
so<00:07:10.199><c> uh</c><00:07:10.360><c> since</c><00:07:10.680><c> each</c><00:07:10.879><c> of</c><00:07:11.080><c> these</c><00:07:11.479><c> documents</c><00:07:12.000><c> are</c>

00:07:12.230 --> 00:07:12.240 align:start position:0%
so uh since each of these documents are
 

00:07:12.240 --> 00:07:16.230 align:start position:0%
so uh since each of these documents are
something<00:07:12.599><c> like</c><00:07:13.080><c> 300</c><00:07:13.599><c> way</c><00:07:13.879><c> or</c><00:07:14.479><c> so</c><00:07:15.479><c> and</c><00:07:15.919><c> then</c>

00:07:16.230 --> 00:07:16.240 align:start position:0%
something like 300 way or so and then
 

00:07:16.240 --> 00:07:20.670 align:start position:0%
something like 300 way or so and then
once<00:07:16.479><c> it</c><00:07:16.599><c> is</c><00:07:16.800><c> done</c><00:07:17.360><c> we'll</c><00:07:17.960><c> create</c><00:07:19.240><c> uh</c><00:07:20.240><c> and</c>

00:07:20.670 --> 00:07:20.680 align:start position:0%
once it is done we'll create uh and
 

00:07:20.680 --> 00:07:21.550 align:start position:0%
once it is done we'll create uh and
query

00:07:21.550 --> 00:07:21.560 align:start position:0%
query
 

00:07:21.560 --> 00:07:25.629 align:start position:0%
query
engines<00:07:22.879><c> so</c><00:07:23.879><c> basically</c><00:07:24.400><c> take</c><00:07:24.840><c> similarity</c><00:07:25.400><c> top</c>

00:07:25.629 --> 00:07:25.639 align:start position:0%
engines so basically take similarity top
 

00:07:25.639 --> 00:07:30.270 align:start position:0%
engines so basically take similarity top
G<00:07:25.919><c> 3</c><00:07:26.360><c> and</c><00:07:26.599><c> create</c><00:07:27.199><c> query</c><00:07:27.520><c> engines</c>

00:07:30.270 --> 00:07:30.280 align:start position:0%
G 3 and create query engines
 

00:07:30.280 --> 00:07:32.790 align:start position:0%
G 3 and create query engines
and<00:07:30.479><c> once</c><00:07:30.759><c> query</c><00:07:31.080><c> engines</c><00:07:31.680><c> are</c><00:07:31.919><c> created</c><00:07:32.520><c> we'll</c>

00:07:32.790 --> 00:07:32.800 align:start position:0%
and once query engines are created we'll
 

00:07:32.800 --> 00:07:34.670 align:start position:0%
and once query engines are created we'll
Define<00:07:33.080><c> the</c><00:07:33.199><c> tools</c><00:07:33.639><c> on</c><00:07:33.879><c> top</c><00:07:34.039><c> of</c><00:07:34.160><c> these</c><00:07:34.360><c> query</c>

00:07:34.670 --> 00:07:34.680 align:start position:0%
Define the tools on top of these query
 

00:07:34.680 --> 00:07:37.270 align:start position:0%
Define the tools on top of these query
engines<00:07:35.560><c> so</c><00:07:35.840><c> lift</c><00:07:36.160><c> query</c><00:07:36.440><c> engine</c><00:07:36.759><c> and</c><00:07:36.960><c> Uber</c>

00:07:37.270 --> 00:07:37.280 align:start position:0%
engines so lift query engine and Uber
 

00:07:37.280 --> 00:07:40.070 align:start position:0%
engines so lift query engine and Uber
query<00:07:37.599><c> engine</c><00:07:38.000><c> right</c><00:07:38.240><c> so</c><00:07:38.919><c> we</c><00:07:39.160><c> have</c><00:07:39.720><c> what</c><00:07:39.840><c> is</c><00:07:39.919><c> a</c>

00:07:40.070 --> 00:07:40.080 align:start position:0%
query engine right so we have what is a
 

00:07:40.080 --> 00:07:42.909 align:start position:0%
query engine right so we have what is a
query<00:07:40.400><c> engine</c><00:07:41.000><c> and</c><00:07:41.199><c> then</c><00:07:42.160><c> what</c><00:07:42.280><c> is</c><00:07:42.360><c> a</c><00:07:42.479><c> metadata</c>

00:07:42.909 --> 00:07:42.919 align:start position:0%
query engine and then what is a metadata
 

00:07:42.919 --> 00:07:44.790 align:start position:0%
query engine and then what is a metadata
tool<00:07:43.160><c> metadata</c><00:07:43.639><c> with</c><00:07:43.800><c> the</c><00:07:43.960><c> name</c><00:07:44.440><c> and</c>

00:07:44.790 --> 00:07:44.800 align:start position:0%
tool metadata with the name and
 

00:07:44.800 --> 00:07:46.670 align:start position:0%
tool metadata with the name and
description<00:07:45.599><c> which</c><00:07:45.720><c> are</c><00:07:45.960><c> very</c><00:07:46.199><c> important</c>

00:07:46.670 --> 00:07:46.680 align:start position:0%
description which are very important
 

00:07:46.680 --> 00:07:48.230 align:start position:0%
description which are very important
because<00:07:47.039><c> based</c><00:07:47.280><c> on</c><00:07:47.479><c> this</c><00:07:47.639><c> name</c><00:07:47.840><c> and</c>

00:07:48.230 --> 00:07:48.240 align:start position:0%
because based on this name and
 

00:07:48.240 --> 00:07:51.270 align:start position:0%
because based on this name and
description<00:07:49.240><c> um</c><00:07:49.560><c> the</c><00:07:50.280><c> agent</c><00:07:50.599><c> will</c><00:07:50.800><c> select</c><00:07:51.159><c> one</c>

00:07:51.270 --> 00:07:51.280 align:start position:0%
description um the agent will select one
 

00:07:51.280 --> 00:07:53.950 align:start position:0%
description um the agent will select one
of<00:07:51.440><c> these</c><00:07:51.639><c> tools</c><00:07:52.000><c> for</c><00:07:52.240><c> the</c><00:07:52.360><c> given</c><00:07:52.759><c> query</c><00:07:53.759><c> so</c>

00:07:53.950 --> 00:07:53.960 align:start position:0%
of these tools for the given query so
 

00:07:53.960 --> 00:07:56.950 align:start position:0%
of these tools for the given query so
similarly<00:07:54.560><c> Uber</c><00:07:54.879><c> 10K</c><00:07:55.400><c> CC</c><00:07:55.680><c> filings</c><00:07:56.159><c> and</c><00:07:56.680><c> uh</c><00:07:56.759><c> you</c>

00:07:56.950 --> 00:07:56.960 align:start position:0%
similarly Uber 10K CC filings and uh you
 

00:07:56.960 --> 00:07:59.869 align:start position:0%
similarly Uber 10K CC filings and uh you
give<00:07:57.720><c> uh</c><00:07:57.960><c> the</c><00:07:58.240><c> description</c><00:07:58.720><c> accordingly</c><00:07:59.240><c> and</c>

00:07:59.869 --> 00:07:59.879 align:start position:0%
give uh the description accordingly and
 

00:07:59.879 --> 00:08:02.189 align:start position:0%
give uh the description accordingly and
create<00:08:00.240><c> these</c><00:08:00.520><c> qu</c><00:08:00.800><c> engine</c>

00:08:02.189 --> 00:08:02.199 align:start position:0%
create these qu engine
 

00:08:02.199 --> 00:08:06.749 align:start position:0%
create these qu engine
tools<00:08:03.599><c> right</c><00:08:04.720><c> and</c><00:08:05.720><c> next</c><00:08:06.039><c> we'll</c><00:08:06.280><c> experiment</c>

00:08:06.749 --> 00:08:06.759 align:start position:0%
tools right and next we'll experiment
 

00:08:06.759 --> 00:08:09.629 align:start position:0%
tools right and next we'll experiment
with<00:08:06.960><c> different</c><00:08:07.280><c> LMS</c><00:08:07.759><c> now</c><00:08:08.120><c> starting</c><00:08:08.520><c> with</c>

00:08:09.629 --> 00:08:09.639 align:start position:0%
with different LMS now starting with
 

00:08:09.639 --> 00:08:12.990 align:start position:0%
with different LMS now starting with
jp4<00:08:10.639><c> uh</c><00:08:10.840><c> react</c><00:08:11.280><c> agent</c><00:08:11.919><c> uh</c><00:08:12.039><c> instead</c><00:08:12.400><c> of</c><00:08:12.680><c> simple</c>

00:08:12.990 --> 00:08:13.000 align:start position:0%
jp4 uh react agent uh instead of simple
 

00:08:13.000 --> 00:08:14.589 align:start position:0%
jp4 uh react agent uh instead of simple
calculator<00:08:13.400><c> tools</c><00:08:13.639><c> we'll</c><00:08:13.879><c> just</c><00:08:14.080><c> pass</c><00:08:14.319><c> quare</c>

00:08:14.589 --> 00:08:14.599 align:start position:0%
calculator tools we'll just pass quare
 

00:08:14.599 --> 00:08:18.110 align:start position:0%
calculator tools we'll just pass quare
engine<00:08:14.879><c> tools</c><00:08:15.319><c> here</c><00:08:16.319><c> and</c><00:08:17.039><c> let's</c><00:08:17.319><c> compare</c><00:08:17.919><c> uh</c>

00:08:18.110 --> 00:08:18.120 align:start position:0%
engine tools here and let's compare uh
 

00:08:18.120 --> 00:08:19.830 align:start position:0%
engine tools here and let's compare uh
compare<00:08:18.400><c> the</c><00:08:18.520><c> revenue</c><00:08:18.840><c> growth</c><00:08:19.080><c> of</c><00:08:19.240><c> uber</c><00:08:19.639><c> and</c>

00:08:19.830 --> 00:08:19.840 align:start position:0%
compare the revenue growth of uber and
 

00:08:19.840 --> 00:08:21.550 align:start position:0%
compare the revenue growth of uber and
lift<00:08:20.560><c> uh</c><00:08:20.639><c> in</c>

00:08:21.550 --> 00:08:21.560 align:start position:0%
lift uh in
 

00:08:21.560 --> 00:08:24.909 align:start position:0%
lift uh in
2021

00:08:24.909 --> 00:08:24.919 align:start position:0%
 
 

00:08:24.919 --> 00:08:28.029 align:start position:0%
 
so<00:08:25.919><c> so</c><00:08:26.199><c> let's</c><00:08:26.400><c> see</c><00:08:26.919><c> uh</c><00:08:27.039><c> how</c><00:08:27.240><c> the</c><00:08:27.440><c> response</c><00:08:27.840><c> will</c>

00:08:28.029 --> 00:08:28.039 align:start position:0%
so so let's see uh how the response will
 

00:08:28.039 --> 00:08:31.670 align:start position:0%
so so let's see uh how the response will
be<00:08:28.879><c> so</c><00:08:29.080><c> it</c><00:08:29.199><c> first</c><00:08:29.599><c> choose</c><00:08:30.120><c> Uber</c><00:08:30.639><c> 10K</c><00:08:31.240><c> SEC</c>

00:08:31.670 --> 00:08:31.680 align:start position:0%
be so it first choose Uber 10K SEC
 

00:08:31.680 --> 00:08:34.389 align:start position:0%
be so it first choose Uber 10K SEC
filings<00:08:32.120><c> document</c><00:08:32.560><c> and</c><00:08:32.919><c> observe</c><00:08:33.399><c> that</c><00:08:33.959><c> Uber's</c>

00:08:34.389 --> 00:08:34.399 align:start position:0%
filings document and observe that Uber's
 

00:08:34.399 --> 00:08:38.029 align:start position:0%
filings document and observe that Uber's
Revenue<00:08:34.719><c> grew</c><00:08:35.039><c> by</c><00:08:35.200><c> 57%</c><00:08:36.000><c> in</c><00:08:36.479><c> 2021</c><00:08:37.479><c> and</c><00:08:37.680><c> then</c>

00:08:38.029 --> 00:08:38.039 align:start position:0%
Revenue grew by 57% in 2021 and then
 

00:08:38.039 --> 00:08:40.430 align:start position:0%
Revenue grew by 57% in 2021 and then
choose<00:08:38.399><c> lift</c><00:08:38.800><c> 10</c><00:08:39.080><c> KCC</c><00:08:39.519><c> filings</c><00:08:40.000><c> and</c><00:08:40.159><c> lifts</c>

00:08:40.430 --> 00:08:40.440 align:start position:0%
choose lift 10 KCC filings and lifts
 

00:08:40.440 --> 00:08:43.350 align:start position:0%
choose lift 10 KCC filings and lifts
Revenue<00:08:40.760><c> increase</c><00:08:41.120><c> by</c><00:08:41.599><c> 36%</c><00:08:42.599><c> now</c><00:08:42.800><c> that</c><00:08:42.959><c> it</c><00:08:43.159><c> has</c>

00:08:43.350 --> 00:08:43.360 align:start position:0%
Revenue increase by 36% now that it has
 

00:08:43.360 --> 00:08:45.350 align:start position:0%
Revenue increase by 36% now that it has
both<00:08:43.640><c> these</c><00:08:43.880><c> information</c><00:08:44.600><c> it</c><00:08:44.839><c> generated</c><00:08:45.240><c> a</c>

00:08:45.350 --> 00:08:45.360 align:start position:0%
both these information it generated a
 

00:08:45.360 --> 00:08:46.310 align:start position:0%
both these information it generated a
final

00:08:46.310 --> 00:08:46.320 align:start position:0%
final
 

00:08:46.320 --> 00:08:48.230 align:start position:0%
final
response

00:08:48.230 --> 00:08:48.240 align:start position:0%
response
 

00:08:48.240 --> 00:08:51.389 align:start position:0%
response
so<00:08:49.240><c> yeah</c><00:08:49.640><c> that's</c><00:08:49.920><c> what</c><00:08:50.200><c> it</c><00:08:50.360><c> says</c><00:08:50.680><c> sub</c><00:08:51.080><c> Revenue</c>

00:08:51.389 --> 00:08:51.399 align:start position:0%
so yeah that's what it says sub Revenue
 

00:08:51.399 --> 00:08:54.910 align:start position:0%
so yeah that's what it says sub Revenue
grew<00:08:51.640><c> by</c><00:08:51.839><c> 57%</c><00:08:52.839><c> while</c><00:08:53.680><c> lift</c><00:08:54.040><c> Revenue</c><00:08:54.519><c> increased</c>

00:08:54.910 --> 00:08:54.920 align:start position:0%
grew by 57% while lift Revenue increased
 

00:08:54.920 --> 00:08:58.389 align:start position:0%
grew by 57% while lift Revenue increased
by<00:08:55.360><c> 36%</c><00:08:56.360><c> so</c><00:08:56.880><c> Uber</c><00:08:57.240><c> had</c><00:08:57.480><c> higher</c><00:08:57.720><c> Revenue</c><00:08:58.079><c> growth</c>

00:08:58.389 --> 00:08:58.399 align:start position:0%
by 36% so Uber had higher Revenue growth
 

00:08:58.399 --> 00:09:01.670 align:start position:0%
by 36% so Uber had higher Revenue growth
compared<00:08:58.680><c> to</c><00:08:58.800><c> lft</c><00:08:59.880><c> let's</c><00:09:00.120><c> try</c><00:09:00.399><c> with</c><00:09:00.680><c> anthropic</c>

00:09:01.670 --> 00:09:01.680 align:start position:0%
compared to lft let's try with anthropic
 

00:09:01.680 --> 00:09:03.269 align:start position:0%
compared to lft let's try with anthropic
as<00:09:01.839><c> well</c><00:09:02.000><c> now</c><00:09:02.200><c> son</c><00:09:02.480><c> at</c>

00:09:03.269 --> 00:09:03.279 align:start position:0%
as well now son at
 

00:09:03.279 --> 00:09:06.190 align:start position:0%
as well now son at
llm<00:09:04.279><c> um</c><00:09:04.560><c> similarly</c><00:09:05.000><c> we'll</c><00:09:05.160><c> create</c><00:09:05.399><c> an</c><00:09:05.560><c> agent</c>

00:09:06.190 --> 00:09:06.200 align:start position:0%
llm um similarly we'll create an agent
 

00:09:06.200 --> 00:09:08.670 align:start position:0%
llm um similarly we'll create an agent
and<00:09:06.440><c> then</c><00:09:06.959><c> uh</c><00:09:07.680><c> compare</c><00:09:08.040><c> Revenue</c><00:09:08.360><c> growth</c><00:09:08.560><c> of</c>

00:09:08.670 --> 00:09:08.680 align:start position:0%
and then uh compare Revenue growth of
 

00:09:08.680 --> 00:09:10.750 align:start position:0%
and then uh compare Revenue growth of
uber<00:09:08.920><c> and</c><00:09:09.079><c> lift</c><00:09:09.360><c> in</c>

00:09:10.750 --> 00:09:10.760 align:start position:0%
uber and lift in
 

00:09:10.760 --> 00:09:15.790 align:start position:0%
uber and lift in
2021<00:09:11.760><c> so</c><00:09:12.160><c> we</c><00:09:12.560><c> choose</c><00:09:13.760><c> the</c><00:09:14.760><c> Uber</c><00:09:15.079><c> 10</c><00:09:15.399><c> KCC</c>

00:09:15.790 --> 00:09:15.800 align:start position:0%
2021 so we choose the Uber 10 KCC
 

00:09:15.800 --> 00:09:17.829 align:start position:0%
2021 so we choose the Uber 10 KCC
filings<00:09:16.160><c> first</c><00:09:16.360><c> and</c><00:09:16.519><c> then</c><00:09:16.800><c> lift</c><00:09:17.079><c> 10</c><00:09:17.360><c> KC</c>

00:09:17.829 --> 00:09:17.839 align:start position:0%
filings first and then lift 10 KC
 

00:09:17.839 --> 00:09:21.590 align:start position:0%
filings first and then lift 10 KC
filings<00:09:18.600><c> absorve</c><00:09:19.079><c> the</c><00:09:19.480><c> revenue</c><00:09:20.480><c> growth</c><00:09:20.839><c> in</c>

00:09:21.590 --> 00:09:21.600 align:start position:0%
filings absorve the revenue growth in
 

00:09:21.600 --> 00:09:23.670 align:start position:0%
filings absorve the revenue growth in
for<00:09:21.880><c> those</c><00:09:22.079><c> two</c><00:09:22.360><c> companies</c><00:09:22.839><c> and</c><00:09:23.040><c> generated</c><00:09:23.519><c> a</c>

00:09:23.670 --> 00:09:23.680 align:start position:0%
for those two companies and generated a
 

00:09:23.680 --> 00:09:25.630 align:start position:0%
for those two companies and generated a
final

00:09:25.630 --> 00:09:25.640 align:start position:0%
final
 

00:09:25.640 --> 00:09:28.550 align:start position:0%
final
response<00:09:26.640><c> so</c><00:09:27.079><c> in</c><00:09:27.279><c> this</c><00:09:27.480><c> way</c><00:09:27.839><c> you</c><00:09:27.959><c> can</c><00:09:28.279><c> actually</c>

00:09:28.550 --> 00:09:28.560 align:start position:0%
response so in this way you can actually
 

00:09:28.560 --> 00:09:32.509 align:start position:0%
response so in this way you can actually
look<00:09:28.800><c> into</c><00:09:29.600><c> um</c><00:09:29.839><c> creating</c><00:09:30.480><c> react</c><00:09:30.880><c> agent</c><00:09:31.519><c> over</c>

00:09:32.509 --> 00:09:32.519 align:start position:0%
look into um creating react agent over
 

00:09:32.519 --> 00:09:36.230 align:start position:0%
look into um creating react agent over
um<00:09:33.240><c> simple</c><00:09:34.240><c> uh</c><00:09:34.399><c> calculator</c><00:09:34.920><c> tools</c><00:09:35.399><c> as</c><00:09:35.560><c> well</c><00:09:35.760><c> as</c>

00:09:36.230 --> 00:09:36.240 align:start position:0%
um simple uh calculator tools as well as
 

00:09:36.240 --> 00:09:41.389 align:start position:0%
um simple uh calculator tools as well as
uh<00:09:36.560><c> rag</c><00:09:37.320><c> uh</c><00:09:38.320><c> query</c><00:09:38.680><c> engine</c><00:09:39.040><c> tools</c><00:09:39.959><c> and</c><00:09:40.160><c> then</c><00:09:40.880><c> uh</c>

00:09:41.389 --> 00:09:41.399 align:start position:0%
uh rag uh query engine tools and then uh
 

00:09:41.399 --> 00:09:46.030 align:start position:0%
uh rag uh query engine tools and then uh
perform<00:09:42.399><c> uh</c><00:09:42.640><c> task</c><00:09:43.079><c> on</c><00:09:43.440><c> on</c><00:09:43.640><c> the</c><00:09:44.040><c> created</c><00:09:45.040><c> agent</c>

00:09:46.030 --> 00:09:46.040 align:start position:0%
perform uh task on on the created agent
 

00:09:46.040 --> 00:09:48.430 align:start position:0%
perform uh task on on the created agent
um<00:09:46.320><c> you</c><00:09:46.440><c> can</c><00:09:46.600><c> combine</c><00:09:47.079><c> both</c><00:09:47.519><c> even</c><00:09:48.000><c> uh</c><00:09:48.240><c> these</c>

00:09:48.430 --> 00:09:48.440 align:start position:0%
um you can combine both even uh these
 

00:09:48.440 --> 00:09:50.829 align:start position:0%
um you can combine both even uh these
calculator<00:09:48.880><c> tools</c><00:09:49.320><c> and</c><00:09:49.760><c> uh</c><00:09:50.040><c> R</c><00:09:50.279><c> quy</c><00:09:50.560><c> engine</c>

00:09:50.829 --> 00:09:50.839 align:start position:0%
calculator tools and uh R quy engine
 

00:09:50.839 --> 00:09:54.470 align:start position:0%
calculator tools and uh R quy engine
tools<00:09:51.240><c> and</c><00:09:52.079><c> start</c><00:09:52.640><c> quing</c><00:09:53.160><c> as</c><00:09:53.279><c> well</c><00:09:54.200><c> uh</c><00:09:54.320><c> which</c>

00:09:54.470 --> 00:09:54.480 align:start position:0%
tools and start quing as well uh which
 

00:09:54.480 --> 00:09:57.069 align:start position:0%
tools and start quing as well uh which
will<00:09:54.600><c> be</c><00:09:54.720><c> an</c><00:09:54.920><c> interesting</c><00:09:55.399><c> task</c><00:09:55.800><c> as</c><00:09:55.959><c> well</c><00:09:56.120><c> as</c>

00:09:57.069 --> 00:09:57.079 align:start position:0%
will be an interesting task as well as
 

00:09:57.079 --> 00:09:59.990 align:start position:0%
will be an interesting task as well as
instead<00:09:57.440><c> of</c><00:09:57.600><c> this</c><00:09:58.120><c> simple</c><00:09:59.000><c> calc</c><00:09:59.519><c> tools</c><00:09:59.880><c> you</c>

00:09:59.990 --> 00:10:00.000 align:start position:0%
instead of this simple calc tools you
 

00:10:00.000 --> 00:10:03.430 align:start position:0%
instead of this simple calc tools you
can<00:10:00.279><c> even</c><00:10:00.680><c> include</c><00:10:01.600><c> the</c><00:10:01.760><c> slack</c><00:10:02.360><c> Gmail</c><00:10:02.880><c> or</c><00:10:03.240><c> some</c>

00:10:03.430 --> 00:10:03.440 align:start position:0%
can even include the slack Gmail or some
 

00:10:03.440 --> 00:10:05.110 align:start position:0%
can even include the slack Gmail or some
other<00:10:03.640><c> tools</c><00:10:04.000><c> that</c><00:10:04.120><c> are</c><00:10:04.279><c> available</c><00:10:04.640><c> in</c><00:10:04.760><c> Lama</c>

00:10:05.110 --> 00:10:05.120 align:start position:0%
other tools that are available in Lama
 

00:10:05.120 --> 00:10:07.550 align:start position:0%
other tools that are available in Lama
Hub<00:10:05.959><c> and</c><00:10:06.480><c> keep</c>

00:10:07.550 --> 00:10:07.560 align:start position:0%
Hub and keep
 

00:10:07.560 --> 00:10:11.030 align:start position:0%
Hub and keep
experimenting<00:10:08.560><c> um</c><00:10:09.399><c> so</c><00:10:10.000><c> do</c><00:10:10.240><c> check</c><00:10:10.440><c> it</c><00:10:10.560><c> out</c><00:10:10.880><c> do</c>

00:10:11.030 --> 00:10:11.040 align:start position:0%
experimenting um so do check it out do
 

00:10:11.040 --> 00:10:13.910 align:start position:0%
experimenting um so do check it out do
check<00:10:11.240><c> out</c><00:10:11.399><c> the</c><00:10:11.880><c> notebook</c><00:10:12.519><c> and</c>

00:10:13.910 --> 00:10:13.920 align:start position:0%
check out the notebook and
 

00:10:13.920 --> 00:10:20.519 align:start position:0%
check out the notebook and
uh<00:10:14.920><c> see</c><00:10:15.200><c> you</c><00:10:15.440><c> in</c><00:10:15.560><c> the</c><00:10:15.720><c> next</c><00:10:16.279><c> video</c><00:10:17.279><c> thank</c><00:10:17.519><c> you</c>

