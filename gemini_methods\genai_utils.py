# Shared utility for Google GenAI SDK interactions

import os
import json
import logging
import asyncio
import pydantic
from typing import Any, Dict, Optional, Type, Union, Sequence, List, Iterable, Callable, Tuple
from google import genai
from google.genai import types
from google.api_core import exceptions as google_exceptions
from google.genai.types import HarmCategory, HarmBlockThreshold, SafetySettingDict, Content, ContentDict, Part, PartDict, Tool
from dotenv import load_dotenv, find_dotenv
from typing import Iterable, Any, Callable, Sequence
from asyncpg.pool import Pool

# Import our advanced cost calculation utilities
from .token_utils import calculate_advanced_api_cost, model_supports_caching
from .pricing_models import CostCalculationRequest, ContentType, OutputType

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Constants --- (Optional: could define default model, common configs here)
DEFAULT_MODEL_NAME = "gemini-2.5-flash-preview-04-17"

# Default Generation Config (from old utils_v14/gemini_utils.py ModelConfig)
DEFAULT_GENERATION_CONFIG: Dict[str, Any] = {
    "temperature": 0.10,
    "top_p": 0.95,
    "top_k": 40, 
    "max_output_tokens": 65536, # Max output tokens for the model
}

# Default Safety Settings (BLOCK_NONE, from old utils_v14/gemini_utils.py ModelConfig)
DEFAULT_SAFETY_SETTINGS: List[SafetySettingDict] = [
    types.SafetySettingDict(
        category=HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    types.SafetySettingDict(
        category=HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    types.SafetySettingDict(
        category=HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    types.SafetySettingDict(
        category=HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
]

# Define type aliases needed by count_tokens_for_input
ContentsType = Union[str, Iterable[Union[str, Part, PartDict, Content, ContentDict]]]
ToolsType = Optional[Union[List[Tool], List[Callable[..., Any]]]]

# Function to get database pool as fallback
def _get_fallback_db_pool() -> Optional[Pool]:
    """
    Attempts to get a database pool from the db_utils module as a fallback.
    
    Returns:
        Database pool if available, None if import or access fails.
    """
    try:
        # Try to import the get_db_connection_pool function
        from ..utils.db_utils import get_db_connection_pool
        return get_db_connection_pool()
    except ImportError as e:
        logger.warning(f"Could not import get_db_connection_pool from db_utils: {e}")
        return None
    except Exception as e:
        logger.warning(f"Could not get database pool from db_utils: {e}")
        return None

# --- Global Client (Consider alternatives for multi-threaded/complex apps) ---
_base_client: Optional[genai.Client] = None
_async_client: Optional[genai.client.AsyncClient] = None
try:
    # Load environment variables from .env.local or .env file
    # find_dotenv will search upwards from the current file's directory
    dotenv_path = find_dotenv(filename='.env.local', raise_error_if_not_found=False) or find_dotenv(filename='.env', raise_error_if_not_found=False)
    if dotenv_path:
        load_dotenv(dotenv_path=dotenv_path)
        logger.info(f"Loaded environment variables from: {dotenv_path}")
    else:
        logger.warning("No .env.local or .env file found. Relying on system environment variables.")

    # Explicitly get API key
    api_key = os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
    if not api_key:
        raise ValueError("API Key not found. Set GOOGLE_API_KEY or GEMINI_API_KEY in environment or .env file.")

    # Simpler: Initialize client directly. SDK handles env var lookup.
    _base_client = genai.Client(api_key=api_key)
    _async_client = _base_client.aio
    logger.info("Google GenAI Client initialized successfully.")
except Exception as e:
    logger.error(f"Failed to initialize Google GenAI Client: {e}. Ensure GOOGLE_API_KEY or GEMINI_API_KEY is set.", exc_info=True)
    # Application might not be usable if client fails, consider raising or handling downstream.


async def call_gemini_api(
    *,
    model_name: str = DEFAULT_MODEL_NAME,
    db_pool: Optional[Pool] = None,
    contents: Optional[Sequence[Union[str, types.Part, types.Content]]] = None,
    response_schema: Type[pydantic.BaseModel],
    generation_config_overrides: Optional[Dict[str, Any]] = None,
    cached_content_name: Optional[str] = None,
    system_instruction_text: Optional[str] = None,
) -> Tuple[Optional[pydantic.BaseModel], Optional[int], Optional[int], Optional[float]]:
    """Calls the Google GenAI API asynchronously with standardized practices.

    Includes default configurations, safety settings, and retry logic.
    Returns a tuple: (validated_pydantic_model, input_tokens, output_tokens, calculated_cost)

    Args:
        model_name: The name of the Gemini model to use.
        db_pool: Optional database pool for token tracking. If None, will attempt to get fallback pool for cost calculation.
        contents: The conversational contents (user messages, model responses) to send to the model.
        response_schema: The Pydantic model to validate the response against.
        generation_config_overrides: Dictionary to override default generation settings.
        cached_content_name: Optional name of a pre-existing cache to use for the request.
        system_instruction_text: Optional text for system-level instructions. This will be placed
                                 directly into the GenerateContentConfig if supported.

    Returns:
        A tuple containing:
            - validated_pydantic_model: The validated response data as a Pydantic model.
            - input_tokens: The number of input tokens.
            - output_tokens: The number of output tokens.
            - calculated_cost: The calculated cost of the API call.
    """
    if not _async_client:
        logger.error("AsyncClient not initialized. Cannot make API call.")
        return None, None, None, None

    client = _async_client

    # Ensure input 'contents' parameter is provided if no system instruction (as contents is the primary payload)
    # An API call with only a system instruction might be valid for some models/use-cases, but typically contents are needed.
    # For now, we allow contents to be None if system_instruction_text is provided, but this might need review.
    if not contents and not system_instruction_text:
        logger.error("API call requires 'contents' or 'system_instruction_text'. Both are missing.")
        return None, None, None, None
        
    # Ensure contents is a list if provided, otherwise initialize to empty list for the API call
    # The actual conversation turns (user/model messages)
    api_contents = list(contents) if contents else []

    # 2. Prepare GenerationConfig (Merging defaults, required, and overrides)
    merged_config = DEFAULT_GENERATION_CONFIG.copy()
    # Essential settings for structured JSON output
    merged_config["response_mime_type"] = "application/json"
    merged_config["response_schema"] = response_schema

    # Add system_instruction directly to the config if provided
    if system_instruction_text:
        # Assuming system_instruction takes a string as per user's example
        merged_config["system_instruction"] = system_instruction_text 

    if generation_config_overrides:
        merged_config.update(generation_config_overrides)

    # Add default safety settings
    # Note: Safety settings might also be part of generation_config_overrides
    # This structure assumes overrides take precedence if provided.
    if "safety_settings" not in merged_config:
         merged_config["safety_settings"] = DEFAULT_SAFETY_SETTINGS

    # Try to get fallback database pool for cost calculation if none provided
    effective_db_pool = db_pool
    if effective_db_pool is None:
        effective_db_pool = _get_fallback_db_pool()
        if effective_db_pool is not None:
            logger.debug("Using fallback database pool for cost calculation")

    if cached_content_name:
        # Check caching support - for now use fallback for specific models
        # TODO: Make this async call work properly in the sync context
        known_caching_models = ["gemini-2.5-pro-exp-03-25"]
        if model_name in known_caching_models:
            merged_config["cached_content"] = cached_content_name

    try:
        gen_config = types.GenerateContentConfig(**merged_config)
    except Exception as e:
        logger.error(f"Error creating GenerateContentConfig: {e}", exc_info=True)
        return None, None, None, None

    # 3. Make the API Call with Retry Logic
    response = None # Initialize response
    max_attempts = 3
    attempt = 0
    last_exception = None
    calculated_cost: Optional[float] = None

    while attempt < max_attempts:
        attempt += 1
        try:
            logger.info(f"Calling Gemini model '{model_name}' (Attempt {attempt}/{max_attempts})...")
            # Pass api_contents (conversation only) and gen_config (which now includes system_instruction)
            response = await client.models.generate_content( # type: ignore [attr-defined] # Correct client path
                model=model_name,
                contents=api_contents, # type: ignore [arg-type] # Known SDK type hint issue
                config=gen_config,
                # Add request_options if needed (e.g., timeout)
            )
            logger.info(f"Received response from '{model_name}' on attempt {attempt}.")

            # 4. Process and Validate Response (Moved inside successful try)
            if not response or not hasattr(response, 'text') or not response.text:
                # Check for specific finish reasons if response exists but text is missing
                finish_reason = None
                prompt_token_count = None
                candidates_token_count = None
                try:
                    if response and response.candidates and response.candidates[0].finish_reason:
                        finish_reason = response.candidates[0].finish_reason.name # Get enum name
                    # Attempt to get usage even if response seems invalid
                    if response and hasattr(response, 'usage_metadata'):
                        prompt_token_count = getattr(response.usage_metadata, 'prompt_token_count', None)
                        candidates_token_count = getattr(response.usage_metadata, 'candidates_token_count', None)
                except Exception as meta_exc:
                    logger.warning(f"Could not extract finish_reason or usage_metadata on invalid response: {meta_exc}")

                logger.warning(f"Invalid, empty, or blocked response received from {model_name}. Finish Reason: {finish_reason}. Response: {response}")
                # Decide if this is retryable. For now, assume safety/block issues are not.
                last_exception = ValueError(f"Invalid or blocked response. Finish Reason: {finish_reason}")
                # Try calculating cost even here if tokens are available
                if prompt_token_count is not None or candidates_token_count is not None:
                    try:
                        # Use advanced cost calculation for accuracy
                        if effective_db_pool is not None:
                            cost_request = CostCalculationRequest(
                                model_name=model_name,
                                input_tokens=prompt_token_count or 0,
                                output_tokens=candidates_token_count or 0,
                                content_type=ContentType.TEXT_IMAGE_VIDEO,  # Default assumption
                                output_type=OutputType.NON_THINKING  # Default assumption
                            )
                            cost_result = await calculate_advanced_api_cost(cost_request, effective_db_pool)
                            calculated_cost = cost_result.total_cost if cost_result else None
                            logger.info(f"Calculated cost on invalid response: ${calculated_cost if calculated_cost else 'unknown'}")
                        else:
                            calculated_cost = None
                            logger.warning("Cannot calculate cost: db_pool is None")
                    except Exception as cost_exc:
                        logger.warning(f"Cost calculation failed during invalid response handling: {cost_exc}")
                        calculated_cost = None # Ensure cost is None if calculation fails
                # Return tokens and calculated cost if available, even if response is invalid
                return None, prompt_token_count, candidates_token_count, calculated_cost

            json_text = response.text

            # Parse and validate using Pydantic
            validated_data = response_schema.model_validate_json(json_text)
            logger.info(f"Successfully parsed and validated response against {response_schema.__name__}.")

            # Extract token counts from usage metadata
            prompt_token_count = None
            candidates_token_count = None
            if hasattr(response, 'usage_metadata'):
                prompt_token_count = getattr(response.usage_metadata, 'prompt_token_count', None)
                candidates_token_count = getattr(response.usage_metadata, 'candidates_token_count', None)
                if prompt_token_count is None or candidates_token_count is None:
                     logger.warning(f"Could not extract full token counts from usage_metadata for {model_name}. Found: prompt={prompt_token_count}, candidates={candidates_token_count}")
            else:
                 logger.warning(f"'usage_metadata' not found in response object for {model_name}.")

            # --- Calculate Cost using DB --- 
            if prompt_token_count is not None or candidates_token_count is not None:
                try:
                    # Use advanced cost calculation for accuracy
                    if effective_db_pool is not None:
                        cost_request = CostCalculationRequest(
                            model_name=model_name,
                            input_tokens=prompt_token_count or 0,
                            output_tokens=candidates_token_count or 0,
                            content_type=ContentType.TEXT_IMAGE_VIDEO,  # Default assumption
                            output_type=OutputType.NON_THINKING  # Default assumption
                        )
                        cost_result = await calculate_advanced_api_cost(cost_request, effective_db_pool)
                        calculated_cost = cost_result.total_cost if cost_result else None
                        if calculated_cost is not None:
                            logger.info(f"Calculated API cost for {model_name}: ${calculated_cost:.6f}")
                        else:
                            logger.warning(f"Cost calculation returned None for {model_name}. Check DB connection and model entry.")
                    else:
                        calculated_cost = None
                        logger.warning("Cannot calculate cost: db_pool is None")
                except Exception as cost_calc_exc:
                    logger.error(f"Error during cost calculation for {model_name}: {cost_calc_exc}", exc_info=True)
                    calculated_cost = None # Ensure cost is None if calculation fails
            else:
                calculated_cost = None
                logger.warning(f"Cannot calculate cost for {model_name} due to missing token counts.")

            return validated_data, prompt_token_count, candidates_token_count, calculated_cost

        # 5. Error Handling within Retry Loop
        except google_exceptions.ResourceExhausted as e:
            logger.warning(f"ResourceExhausted error on attempt {attempt} for {model_name}: {e}. Retrying...")
            last_exception = e
            await asyncio.sleep(2 ** attempt) # Exponential backoff
        except google_exceptions.ServiceUnavailable as e:
            logger.warning(f"ServiceUnavailable error on attempt {attempt} for {model_name}: {e}. Retrying...")
            last_exception = e
            await asyncio.sleep(2 ** attempt)
        except google_exceptions.GoogleAPIError as e:
            # Catch other potentially retryable Google API errors
            logger.warning(f"Google API Error on attempt {attempt} for {model_name}: {e}. Retrying...")
            last_exception = e
            await asyncio.sleep(2 ** attempt)
        except Exception as e:
            # Catch non-retryable errors (like validation, JSON decode, unexpected) immediately
            logger.error(f"Non-retryable error during Gemini API call ({model_name}): {e}", exc_info=True)
            # Attempt to log response text if available before failing
            try:
                raw_response_prefix = response.text[:500] if response and hasattr(response, 'text') and response.text else "(No response text)"
                logger.error(f"Response prefix (if available): {raw_response_prefix}")
            except Exception as log_e:
                logger.error(f"Could not extract response text for error logging: {log_e}")
            last_exception = e # Store the exception
            # Return None for model and tokens on non-retryable error, and None for cost
            return None, None, None, None

    # If loop finishes without success
    logger.error(f"Gemini API call failed after {max_attempts} attempts for model {model_name}. Last error: {last_exception}")
    # Return cost if it was calculated on an earlier failed attempt (e.g., invalid response), else None
    return None, None, None, calculated_cost

# --- Token Counting Utility --- #

def count_tokens_for_input(
    model_name: str,
    contents: ContentsType,
    system_instruction: Optional[str] = None,
    tools: ToolsType = None,
) -> int:
    """Counts the number of tokens for the given input components using the specified model.

    Uses the specific model configuration (including system instructions and tools)
    for accurate counting via the Gemini API.

    Args:
        model_name: The name of the Gemini model to use for counting (e.g., 'gemini-1.5-flash-latest').
        contents: The main content to be sent to the model.
        system_instruction: Optional system instructions for the model.
        tools: Optional tools (functions) provided to the model.

    Returns:
        The total number of tokens calculated by the model.

    Raises:
        Exception: If the token counting API call fails.
    """
    try:
        # Get the client instance
        # Using the already defined global client
        if not _base_client:
            logger.error("Client not initialized. Cannot count tokens.")
            raise ValueError("GenAI Client not initialized")
        
        # For the new client API, we need to ensure contents is properly formatted
        # Convert string to a list if needed
        contents_list = [contents] if isinstance(contents, str) else contents
        
        # Convert list-like contents to something the API can handle
        if isinstance(contents_list, list) or hasattr(contents_list, '__iter__'):
            # Convert to expected format
            model_contents = []
            for item in contents_list:
                if isinstance(item, str):
                    model_contents.append(item)
                elif hasattr(item, 'to_dict'):  # Handle Part or Content objects
                    model_contents.append(item)  
                else:
                    # For other types, convert to string
                    model_contents.append(str(item))
        else:
            # Fallback for other types
            model_contents = str(contents_list)
            
        # Tools and system_instruction are now handled differently
        # We'll add the system instruction as part of the content
        if system_instruction:
            if isinstance(model_contents, list):
                # Add as a new content item
                system_item = f"System: {system_instruction}"
                model_contents.insert(0, system_item)
            else:
                # Combine with existing content
                model_contents = f"System: {system_instruction}\n\n{model_contents}"
        
        # Use the client models API to count tokens
        response = _base_client.models.count_tokens(
            model=model_name,
            contents=model_contents
        )

        logger.debug(f"Token count response for model {model_name}: {response}")
        # Ensure we return an int, not None
        if response and hasattr(response, 'total_tokens') and response.total_tokens is not None:
            return response.total_tokens
        else:
            logger.warning(f"Could not get token count for {model_name}, returning 0")
            return 0
    except Exception as e:
        logger.error(f"Error counting tokens for model {model_name}: {e}")
        # Return 0 as a fallback in case of errors
        logger.warning(f"Failed to count tokens for {model_name}, returning 0 as fallback")
        return 0

# --- Caching Support Utility --- #
# Note: Using model_supports_caching from token_utils.py for database-driven caching support checks

# --- Get model token limits --- #

async def get_model_max_tokens(model_name: str, db_pool: Optional[Pool] = None) -> Dict[str, int]:
    """
    Gets the maximum input and output token limits for a model.
    Attempts to retrieve from database first, then falls back to defaults.
    
    Args:
        model_name: The name of the model to check
        db_pool: Optional database pool for retrieving model capabilities
        
    Returns:
        Dictionary with 'max_input_tokens' and 'max_output_tokens'
    """
    # Default values for common models
    defaults = {
        "gemini-2.5-flash-preview-04-17": {"max_input_tokens": 1000000, "max_output_tokens": 65536},
        "gemini-2.5-pro-exp-03-25": {"max_input_tokens": 1000000, "max_output_tokens": 65536},
    }
    
    # If we have a DB pool, try to get from database first
    if db_pool:
        try:
            async with db_pool.acquire() as conn:
                query = """
                SELECT max_input_tokens, max_output_tokens 
                FROM model_capabilities 
                WHERE model_name = $1
                """
                row = await conn.fetchrow(query, model_name)
                if row:
                    return {
                        "max_input_tokens": row["max_input_tokens"],
                        "max_output_tokens": row["max_output_tokens"]
                    }
        except Exception as e:
            logger.warning(f"Error retrieving model capabilities from database: {e}")
            # Fall back to defaults
    
    # Return defaults if not in DB or DB query failed
    if model_name in defaults:
        return defaults[model_name]
    
    # Return conservative defaults if model not recognized
    return {"max_input_tokens": 950000, "max_output_tokens": 65536}

# --- Embedding Generation --- #

async def generate_embedding(
    text: str,
    model_name: str = "text-embedding-004",
    task_type: Optional[str] = None,
    output_dimensionality: Optional[int] = None,
    db_pool: Optional[Pool] = None
) -> Dict[str, Any]:
    """
    Generate text embeddings using Google GenAI SDK.
    
    Args:
        text: The input text to generate embeddings for
        model_name: The embedding model to use (default: "text-embedding-004")
        task_type: Optional task type for the embedding (e.g., "SEMANTIC_SIMILARITY", 
                  "CLASSIFICATION", "CLUSTERING", "RETRIEVAL_DOCUMENT", "RETRIEVAL_QUERY")
        output_dimensionality: Optional dimension reduction for the output embedding
        db_pool: Optional database pool for cost calculation
    
    Returns:
        Dictionary containing:
            - values: List of embedding values (floats)
            - model: Model name used
            - dimensions: Number of dimensions in the embedding
            - cost_estimate: Estimated cost of the API call
            - task_type: Task type used (if specified)
    """
    if not _async_client:
        logger.error("AsyncClient not initialized. Cannot generate embedding.")
        raise ValueError("Google GenAI client not available")
    
    if not text or not text.strip():
        logger.error("Empty text provided for embedding generation")
        raise ValueError("Text cannot be empty")
    
    client = _async_client
    
    try:
        # Prepare embedding configuration
        config_params = {}
        if task_type:
            # Map common task types to SDK values
            task_type_mapping = {
                "SEMANTIC_SIMILARITY": types.TaskType.SEMANTIC_SIMILARITY,
                "CLASSIFICATION": types.TaskType.CLASSIFICATION,
                "CLUSTERING": types.TaskType.CLUSTERING,
                "RETRIEVAL_DOCUMENT": types.TaskType.RETRIEVAL_DOCUMENT,
                "RETRIEVAL_QUERY": types.TaskType.RETRIEVAL_QUERY,
            }
            if task_type.upper() in task_type_mapping:
                config_params["task_type"] = task_type_mapping[task_type.upper()]
            else:
                logger.warning(f"Unknown task_type '{task_type}', proceeding without it")
        
        if output_dimensionality:
            config_params["output_dimensionality"] = output_dimensionality
        
        # Create embedding config if we have parameters
        embed_config = None
        if config_params:
            embed_config = types.EmbedContentConfig(**config_params)
        
        logger.info(f"Generating embedding for text of length {len(text)} using model {model_name}")
        
        # Make the API call
        response = await client.models.embed_content(
            model=model_name,
            contents=text,
            config=embed_config
        )
        
        # Extract embedding values
        if not response or not hasattr(response, 'embeddings') or not response.embeddings:
            logger.error("No embeddings returned from API")
            raise ValueError("Failed to generate embedding - no data returned")
        
        # Get the first (and typically only) embedding
        embedding = response.embeddings[0]
        embedding_values = embedding.values if hasattr(embedding, 'values') else []
        
        if not embedding_values:
            logger.error("Empty embedding values returned from API")
            raise ValueError("Failed to generate embedding - empty values")
        
        # Calculate cost estimate if possible
        cost_estimate = None
        try:
            if db_pool:
                # For embeddings, we typically count input tokens only
                input_tokens = await count_tokens_for_input(
                    model_name=model_name,
                    contents=[text],
                    tools=None
                )
                
                # Calculate cost using the advanced cost calculation
                cost_request = CostCalculationRequest(
                    model_name=model_name,
                    input_tokens=input_tokens,
                    output_tokens=0,  # Embeddings don't have output tokens
                    content_type=ContentType.TEXT,
                    output_type=OutputType.EMBEDDING,
                    cached_input_tokens=0,
                    total_characters=len(text)
                )
                
                cost_estimate = await calculate_advanced_api_cost(
                    cost_request, 
                    db_pool
                )
                
                logger.info(f"Embedding cost calculated: ${cost_estimate:.6f} for {input_tokens} tokens")
        except Exception as e:
            logger.warning(f"Could not calculate cost estimate for embedding: {e}")
            cost_estimate = None
        
        result = {
            "values": embedding_values,
            "model": model_name,
            "dimensions": len(embedding_values),
            "cost_estimate": cost_estimate
        }
        
        if task_type:
            result["task_type"] = task_type
        
        logger.info(f"Successfully generated {len(embedding_values)}-dimensional embedding using {model_name}")
        return result
        
    except google_exceptions.GoogleAPIError as e:
        logger.error(f"Google API error during embedding generation: {e}")
        raise ValueError(f"Google API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error during embedding generation: {e}")
        raise ValueError(f"Failed to generate embedding: {str(e)}")

# --- Available Embedding Models --- #

def get_available_embedding_models() -> List[str]:
    """
    Returns a list of available embedding models.
    
    Returns:
        List of model names that can be used for embedding generation
    """
    return [
        "text-embedding-004",  # Latest and recommended
        "embedding-001",       # Legacy model
        "models/text-embedding-004",  # Full model path format
        "models/embedding-001"        # Full model path format
    ]

def get_default_embedding_model() -> str:
    """
    Returns the default embedding model name.
    
    Returns:
        Default model name for embedding generation
    """
    return "text-embedding-004"
