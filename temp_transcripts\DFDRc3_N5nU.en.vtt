WEBVTT
Kind: captions
Language: en

00:00:00.320 --> 00:00:03.230 align:start position:0%
 
you<00:00:00.599><c> asked</c><00:00:01.000><c> for</c><00:00:01.280><c> it</c><00:00:01.880><c> over</c><00:00:02.159><c> and</c><00:00:02.399><c> over</c><00:00:03.080><c> and</c>

00:00:03.230 --> 00:00:03.240 align:start position:0%
you asked for it over and over and
 

00:00:03.240 --> 00:00:05.800 align:start position:0%
you asked for it over and over and
wouldn't<00:00:03.520><c> shut</c><00:00:03.800><c> up</c><00:00:04.080><c> about</c><00:00:04.319><c> it</c><00:00:04.720><c> so</c><00:00:05.080><c> here</c><00:00:05.200><c> it</c>

00:00:05.800 --> 00:00:05.810 align:start position:0%
wouldn't shut up about it so here it
 

00:00:05.810 --> 00:00:15.109 align:start position:0%
wouldn't shut up about it so here it
[Music]

00:00:15.109 --> 00:00:15.119 align:start position:0%
 
 

00:00:15.119 --> 00:00:17.990 align:start position:0%
 
isut<00:00:16.119><c> hi</c><00:00:16.279><c> everybody</c><00:00:16.840><c> it's</c><00:00:17.039><c> Jay</c><00:00:17.520><c> and</c><00:00:17.640><c> I'm</c><00:00:17.800><c> proud</c>

00:00:17.990 --> 00:00:18.000 align:start position:0%
isut hi everybody it's Jay and I'm proud
 

00:00:18.000 --> 00:00:19.990 align:start position:0%
isut hi everybody it's Jay and I'm proud
to<00:00:18.119><c> bring</c><00:00:18.320><c> you</c><00:00:18.560><c> part</c><00:00:18.800><c> one</c><00:00:19.000><c> of</c><00:00:19.160><c> the</c><00:00:19.279><c> auto</c><00:00:19.680><c> grock</c>

00:00:19.990 --> 00:00:20.000 align:start position:0%
to bring you part one of the auto grock
 

00:00:20.000 --> 00:00:23.189 align:start position:0%
to bring you part one of the auto grock
freaking<00:00:20.840><c> tutorial</c><00:00:21.840><c> part</c><00:00:22.080><c> one</c><00:00:22.800><c> what</c><00:00:22.920><c> the</c><00:00:23.039><c> hell</c>

00:00:23.189 --> 00:00:23.199 align:start position:0%
freaking tutorial part one what the hell
 

00:00:23.199 --> 00:00:26.390 align:start position:0%
freaking tutorial part one what the hell
is<00:00:23.279><c> an</c><00:00:23.439><c> autog</c><00:00:23.680><c> Gro</c><00:00:24.160><c> anyway</c><00:00:25.160><c> autog</c><00:00:25.560><c> Gro</c><00:00:25.960><c> makes</c>

00:00:26.390 --> 00:00:26.400 align:start position:0%
is an autog Gro anyway autog Gro makes
 

00:00:26.400 --> 00:00:29.429 align:start position:0%
is an autog Gro anyway autog Gro makes
AI<00:00:26.880><c> agents</c><00:00:27.720><c> and</c><00:00:27.920><c> AI</c><00:00:28.320><c> agents</c><00:00:28.640><c> are</c><00:00:28.920><c> like</c>

00:00:29.429 --> 00:00:29.439 align:start position:0%
AI agents and AI agents are like
 

00:00:29.439 --> 00:00:30.950 align:start position:0%
AI agents and AI agents are like
computer<00:00:30.000><c> programs</c><00:00:30.359><c> that</c><00:00:30.480><c> can</c><00:00:30.599><c> run</c>

00:00:30.950 --> 00:00:30.960 align:start position:0%
computer programs that can run
 

00:00:30.960 --> 00:00:33.790 align:start position:0%
computer programs that can run
themselves<00:00:31.640><c> I'm</c><00:00:31.840><c> kind</c><00:00:31.960><c> of</c><00:00:32.040><c> a</c><00:00:32.239><c> lazy</c><00:00:32.759><c> ass</c><00:00:33.320><c> so</c><00:00:33.640><c> I</c>

00:00:33.790 --> 00:00:33.800 align:start position:0%
themselves I'm kind of a lazy ass so I
 

00:00:33.800 --> 00:00:35.549 align:start position:0%
themselves I'm kind of a lazy ass so I
really<00:00:34.079><c> like</c><00:00:34.320><c> the</c><00:00:34.520><c> idea</c><00:00:34.800><c> of</c><00:00:35.000><c> programs</c><00:00:35.440><c> that</c>

00:00:35.549 --> 00:00:35.559 align:start position:0%
really like the idea of programs that
 

00:00:35.559 --> 00:00:38.069 align:start position:0%
really like the idea of programs that
can<00:00:35.680><c> run</c><00:00:36.320><c> themselves</c><00:00:37.320><c> but</c><00:00:37.480><c> programming</c>

00:00:38.069 --> 00:00:38.079 align:start position:0%
can run themselves but programming
 

00:00:38.079 --> 00:00:40.270 align:start position:0%
can run themselves but programming
agents<00:00:38.480><c> takes</c><00:00:38.719><c> a</c><00:00:38.840><c> lot</c><00:00:38.960><c> of</c><00:00:39.160><c> typing</c><00:00:39.840><c> and</c><00:00:39.960><c> I</c><00:00:40.079><c> have</c>

00:00:40.270 --> 00:00:40.280 align:start position:0%
agents takes a lot of typing and I have
 

00:00:40.280 --> 00:00:43.069 align:start position:0%
agents takes a lot of typing and I have
Parkinson's<00:00:41.239><c> so</c><00:00:41.520><c> I</c><00:00:41.600><c> made</c><00:00:41.879><c> Auto</c><00:00:42.320><c> Gro</c><00:00:42.760><c> to</c><00:00:42.879><c> make</c>

00:00:43.069 --> 00:00:43.079 align:start position:0%
Parkinson's so I made Auto Gro to make
 

00:00:43.079 --> 00:00:45.549 align:start position:0%
Parkinson's so I made Auto Gro to make
my<00:00:43.280><c> agents</c><00:00:43.640><c> for</c><00:00:43.960><c> me</c><00:00:44.600><c> the</c><00:00:44.760><c> problem</c><00:00:45.079><c> is</c><00:00:45.280><c> that</c><00:00:45.440><c> we</c>

00:00:45.549 --> 00:00:45.559 align:start position:0%
my agents for me the problem is that we
 

00:00:45.559 --> 00:00:48.430 align:start position:0%
my agents for me the problem is that we
do<00:00:45.960><c> agent</c><00:00:46.280><c> programming</c><00:00:46.960><c> backwards</c><00:00:47.920><c> standard</c>

00:00:48.430 --> 00:00:48.440 align:start position:0%
do agent programming backwards standard
 

00:00:48.440 --> 00:00:51.229 align:start position:0%
do agent programming backwards standard
practice<00:00:48.920><c> is</c><00:00:49.039><c> to</c><00:00:49.239><c> create</c><00:00:49.600><c> AI</c><00:00:49.960><c> agents</c><00:00:50.399><c> first</c><00:00:51.039><c> by</c>

00:00:51.229 --> 00:00:51.239 align:start position:0%
practice is to create AI agents first by
 

00:00:51.239 --> 00:00:53.349 align:start position:0%
practice is to create AI agents first by
anticipating<00:00:52.000><c> problems</c><00:00:52.760><c> but</c><00:00:52.920><c> in</c><00:00:53.039><c> the</c><00:00:53.160><c> real</c>

00:00:53.349 --> 00:00:53.359 align:start position:0%
anticipating problems but in the real
 

00:00:53.359 --> 00:00:55.349 align:start position:0%
anticipating problems but in the real
world<00:00:53.680><c> we</c><00:00:53.960><c> first</c><00:00:54.359><c> discover</c><00:00:54.559><c> a</c><00:00:54.760><c> problem</c><00:00:55.239><c> and</c>

00:00:55.349 --> 00:00:55.359 align:start position:0%
world we first discover a problem and
 

00:00:55.359 --> 00:00:57.549 align:start position:0%
world we first discover a problem and
then<00:00:55.559><c> seek</c><00:00:55.840><c> out</c><00:00:56.000><c> our</c><00:00:56.239><c> experts</c><00:00:56.680><c> to</c><00:00:56.840><c> solve</c><00:00:57.120><c> it</c>

00:00:57.549 --> 00:00:57.559 align:start position:0%
then seek out our experts to solve it
 

00:00:57.559 --> 00:00:59.630 align:start position:0%
then seek out our experts to solve it
that's<00:00:57.760><c> how</c><00:00:57.879><c> autog</c><00:00:58.480><c> works</c><00:00:58.760><c> too</c><00:00:59.239><c> we</c><00:00:59.399><c> take</c><00:00:59.559><c> the</c>

00:00:59.630 --> 00:00:59.640 align:start position:0%
that's how autog works too we take the
 

00:00:59.640 --> 00:01:02.150 align:start position:0%
that's how autog works too we take the
user<00:01:00.280><c> problem</c><00:01:00.760><c> analyze</c><00:01:01.160><c> it</c><00:01:01.600><c> and</c><00:01:01.680><c> then</c><00:01:01.840><c> build</c><00:01:02.039><c> a</c>

00:01:02.150 --> 00:01:02.160 align:start position:0%
user problem analyze it and then build a
 

00:01:02.160 --> 00:01:04.109 align:start position:0%
user problem analyze it and then build a
team<00:01:02.320><c> of</c><00:01:02.480><c> Agents</c><00:01:02.960><c> specifically</c><00:01:03.519><c> designed</c><00:01:03.920><c> for</c>

00:01:04.109 --> 00:01:04.119 align:start position:0%
team of Agents specifically designed for
 

00:01:04.119 --> 00:01:07.429 align:start position:0%
team of Agents specifically designed for
that<00:01:04.799><c> situation</c><00:01:05.799><c> those</c><00:01:06.000><c> agents</c><00:01:06.479><c> can</c><00:01:06.920><c> easily</c>

00:01:07.429 --> 00:01:07.439 align:start position:0%
that situation those agents can easily
 

00:01:07.439 --> 00:01:09.670 align:start position:0%
that situation those agents can easily
be<00:01:07.640><c> edited</c><00:01:08.159><c> by</c><00:01:08.280><c> the</c><00:01:08.400><c> user</c><00:01:09.040><c> they</c><00:01:09.159><c> can</c><00:01:09.280><c> be</c><00:01:09.439><c> given</c>

00:01:09.670 --> 00:01:09.680 align:start position:0%
be edited by the user they can be given
 

00:01:09.680 --> 00:01:11.990 align:start position:0%
be edited by the user they can be given
skills<00:01:10.159><c> regenerated</c><00:01:10.960><c> and</c><00:01:11.159><c> even</c><00:01:11.360><c> learned</c><00:01:11.759><c> from</c>

00:01:11.990 --> 00:01:12.000 align:start position:0%
skills regenerated and even learned from
 

00:01:12.000 --> 00:01:14.070 align:start position:0%
skills regenerated and even learned from
conversations<00:01:12.640><c> with</c><00:01:12.799><c> other</c><00:01:13.000><c> AI</c><00:01:13.360><c> agents</c><00:01:13.840><c> or</c>

00:01:14.070 --> 00:01:14.080 align:start position:0%
conversations with other AI agents or
 

00:01:14.080 --> 00:01:16.670 align:start position:0%
conversations with other AI agents or
the<00:01:14.200><c> human</c><00:01:14.640><c> user</c><00:01:15.640><c> and</c><00:01:15.759><c> they</c><00:01:15.960><c> can</c><00:01:16.080><c> be</c><00:01:16.240><c> exported</c>

00:01:16.670 --> 00:01:16.680 align:start position:0%
the human user and they can be exported
 

00:01:16.680 --> 00:01:19.789 align:start position:0%
the human user and they can be exported
to<00:01:16.840><c> autogen</c><00:01:17.400><c> and</c><00:01:17.560><c> crew</c><00:01:18.200><c> AI</c><00:01:19.200><c> these</c><00:01:19.320><c> are</c><00:01:19.560><c> other</c>

00:01:19.789 --> 00:01:19.799 align:start position:0%
to autogen and crew AI these are other
 

00:01:19.799 --> 00:01:21.870 align:start position:0%
to autogen and crew AI these are other
open<00:01:20.119><c> source</c><00:01:20.439><c> software</c><00:01:20.960><c> packages</c><00:01:21.479><c> that</c><00:01:21.640><c> allow</c>

00:01:21.870 --> 00:01:21.880 align:start position:0%
open source software packages that allow
 

00:01:21.880 --> 00:01:23.710 align:start position:0%
open source software packages that allow
our<00:01:22.040><c> teams</c><00:01:22.320><c> of</c><00:01:22.479><c> agents</c><00:01:22.840><c> to</c><00:01:23.040><c> collaborate</c><00:01:23.600><c> with</c>

00:01:23.710 --> 00:01:23.720 align:start position:0%
our teams of agents to collaborate with
 

00:01:23.720 --> 00:01:26.030 align:start position:0%
our teams of agents to collaborate with
one<00:01:23.960><c> another</c><00:01:24.360><c> automatically</c><00:01:25.360><c> think</c><00:01:25.520><c> of</c><00:01:25.680><c> autog</c>

00:01:26.030 --> 00:01:26.040 align:start position:0%
one another automatically think of autog
 

00:01:26.040 --> 00:01:28.109 align:start position:0%
one another automatically think of autog
Gro<00:01:26.320><c> as</c><00:01:26.439><c> a</c><00:01:26.520><c> building</c><00:01:26.799><c> and</c><00:01:27.000><c> testing</c><00:01:27.439><c> platform</c>

00:01:28.109 --> 00:01:28.119 align:start position:0%
Gro as a building and testing platform
 

00:01:28.119 --> 00:01:29.910 align:start position:0%
Gro as a building and testing platform
and<00:01:28.320><c> programs</c><00:01:28.759><c> like</c><00:01:28.880><c> autogen</c><00:01:29.479><c> as</c><00:01:29.600><c> the</c><00:01:29.720><c> real</c>

00:01:29.910 --> 00:01:29.920 align:start position:0%
and programs like autogen as the real
 

00:01:29.920 --> 00:01:31.550 align:start position:0%
and programs like autogen as the real
real<00:01:30.079><c> world</c><00:01:30.400><c> application</c><00:01:30.960><c> environment</c><00:01:31.400><c> where</c>

00:01:31.550 --> 00:01:31.560 align:start position:0%
real world application environment where
 

00:01:31.560 --> 00:01:33.670 align:start position:0%
real world application environment where
the<00:01:31.680><c> AI</c><00:01:32.000><c> agents</c><00:01:32.360><c> we</c><00:01:32.520><c> created</c><00:01:32.920><c> get</c><00:01:33.040><c> to</c><00:01:33.159><c> run</c><00:01:33.399><c> free</c>

00:01:33.670 --> 00:01:33.680 align:start position:0%
the AI agents we created get to run free
 

00:01:33.680 --> 00:01:36.830 align:start position:0%
the AI agents we created get to run free
and<00:01:33.920><c> play</c><00:01:34.560><c> or</c><00:01:35.240><c> um</c><00:01:35.560><c> build</c><00:01:35.920><c> stuff</c><00:01:36.399><c> whatever</c><00:01:36.680><c> we</c>

00:01:36.830 --> 00:01:36.840 align:start position:0%
and play or um build stuff whatever we
 

00:01:36.840 --> 00:01:38.630 align:start position:0%
and play or um build stuff whatever we
built<00:01:37.119><c> them</c><00:01:37.240><c> to</c><00:01:37.399><c> do</c><00:01:37.880><c> we'll</c><00:01:38.040><c> talk</c><00:01:38.240><c> more</c><00:01:38.439><c> about</c>

00:01:38.630 --> 00:01:38.640 align:start position:0%
built them to do we'll talk more about
 

00:01:38.640 --> 00:01:40.389 align:start position:0%
built them to do we'll talk more about
the<00:01:38.799><c> advanced</c><00:01:39.200><c> features</c><00:01:39.520><c> of</c><00:01:39.640><c> Auto</c><00:01:39.960><c> Gro</c><00:01:40.280><c> and</c>

00:01:40.389 --> 00:01:40.399 align:start position:0%
the advanced features of Auto Gro and
 

00:01:40.399 --> 00:01:42.190 align:start position:0%
the advanced features of Auto Gro and
the<00:01:40.520><c> auto</c><00:01:40.799><c> genen</c><00:01:41.000><c> and</c><00:01:41.159><c> crew</c><00:01:41.439><c> AI</c><00:01:41.759><c> platforms</c>

00:01:42.190 --> 00:01:42.200 align:start position:0%
the auto genen and crew AI platforms
 

00:01:42.200 --> 00:01:44.109 align:start position:0%
the auto genen and crew AI platforms
later<00:01:42.799><c> hopefully</c><00:01:43.200><c> you</c><00:01:43.360><c> now</c><00:01:43.560><c> have</c><00:01:43.680><c> an</c>

00:01:44.109 --> 00:01:44.119 align:start position:0%
later hopefully you now have an
 

00:01:44.119 --> 00:01:45.270 align:start position:0%
later hopefully you now have an
understanding<00:01:44.360><c> of</c><00:01:44.520><c> the</c><00:01:44.680><c> exciting</c><00:01:45.119><c> and</c>

00:01:45.270 --> 00:01:45.280 align:start position:0%
understanding of the exciting and
 

00:01:45.280 --> 00:01:47.950 align:start position:0%
understanding of the exciting and
groundbreaking<00:01:46.040><c> potential</c><00:01:46.399><c> of</c><00:01:46.520><c> autog</c><00:01:46.880><c> Gro</c><00:01:47.880><c> I</c>

00:01:47.950 --> 00:01:47.960 align:start position:0%
groundbreaking potential of autog Gro I
 

00:01:47.960 --> 00:01:49.910 align:start position:0%
groundbreaking potential of autog Gro I
got<00:01:48.079><c> to</c><00:01:48.280><c> admit</c><00:01:48.920><c> I</c><00:01:49.040><c> like</c><00:01:49.280><c> that</c><00:01:49.439><c> more</c><00:01:49.640><c> than</c><00:01:49.799><c> I</c>

00:01:49.910 --> 00:01:49.920 align:start position:0%
got to admit I like that more than I
 

00:01:49.920 --> 00:01:50.470 align:start position:0%
got to admit I like that more than I
thought<00:01:50.159><c> I</c>

00:01:50.470 --> 00:01:50.480 align:start position:0%
thought I
 

00:01:50.480 --> 00:01:52.910 align:start position:0%
thought I
[Music]

00:01:52.910 --> 00:01:52.920 align:start position:0%
[Music]
 

00:01:52.920 --> 00:01:54.590 align:start position:0%
[Music]
would

00:01:54.590 --> 00:01:54.600 align:start position:0%
would
 

00:01:54.600 --> 00:01:56.910 align:start position:0%
would
aut

00:01:56.910 --> 00:01:56.920 align:start position:0%
aut
 

00:01:56.920 --> 00:02:01.840 align:start position:0%
aut
autut<00:01:58.159><c> aut</c><00:01:59.159><c> autut</c>

