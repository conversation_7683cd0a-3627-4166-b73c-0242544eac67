# 🔗 Chat System Integration Guide

This guide explains how to integrate the chat system with existing `genai_utils.py` based applications to add conversation memory capabilities.

## 📋 Table of Contents

- [Overview](#overview)
- [Integration Architecture](#integration-architecture)
- [Migration Scenarios](#migration-scenarios)
- [Integration Options](#integration-options)
- [Real-World Examples](#real-world-examples)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

The chat system is fully compatible with existing `genai_utils.py` infrastructure and provides:

- ✅ **Conversation Memory**: Remembers context across messages
- ✅ **Session Management**: Multiple concurrent conversations
- ✅ **Cost Tracking**: Integrates with existing pricing system
- ✅ **Database Persistence**: Optional long-term storage
- ✅ **Backward Compatibility**: Existing code continues to work

## 🏗️ Integration Architecture

```
┌─────────────────────┐    ┌─────────────────────┐
│   genai_utils.py    │────│   Chat Manager      │
│                     │    │                     │
│ • DEFAULT_MODEL     │    │ • Conversation      │
│ • DEFAULT_CONFIG    │    │   Memory            │
│ • SAFETY_SETTINGS   │    │ • Session Mgmt      │
│ • _async_client     │    │ • Cost Tracking     │
└─────────────────────┘    └─────────────────────┘
           │                           │
           └─────────┬─────────────────┘
                     │
         ┌─────────────────────┐
         │  Your Application   │
         │                     │
         │ • Structured Data   │
         │   (use genai_utils) │
         │ • Conversations     │
         │   (use chat system) │
         └─────────────────────┘
```

### Direct Dependencies

The chat system imports and uses:

```python
from ..genai_utils import (
    DEFAULT_MODEL_NAME,           # Default model for sessions
    DEFAULT_GENERATION_CONFIG,    # Temperature, top_p, etc.
    DEFAULT_SAFETY_SETTINGS,      # Harm blocking settings
    _async_client                 # Initialized Google GenAI client
)

from ..token_utils import calculate_advanced_api_cost
from ..pricing_models import CostCalculationRequest, ContentType, OutputType
```

## 🔄 Migration Scenarios

### Scenario 1: Simple Stateless Chat

**Before (No Memory):**
```python
# Your existing genai_utils.py based chat
async def simple_chat_response(user_message: str):
    response = await call_gemini_api(
        contents=[user_message],
        response_schema=TextResponseModel,
        system_instruction_text="You are a helpful assistant"
    )
    return response.text
```

**Problems:**
- ❌ No conversation memory
- ❌ Each message is independent  
- ❌ Can't reference previous context

**After (With Memory):**
```python
from chat import create_simple_chat

async def smart_chat_response(user_message: str, session_id: str = None):
    if session_id is None:
        manager, session_id = await create_simple_chat(
            system_instruction="You are a helpful assistant"
        )
    else:
        manager = ChatManager()
        
    response, input_tokens, output_tokens, cost = await manager.send_message(
        session_id, user_message
    )
    
    return {
        "response": response,
        "session_id": session_id,  # Return for next call
        "tokens": {"input": input_tokens, "output": output_tokens},
        "cost": cost
    }
```

### Scenario 2: Manual History Management

**Before (Partial Memory):**
```python
# Your existing approach with manual history
conversation_history = []

async def chat_with_manual_history(user_message: str):
    # Add user message to history
    conversation_history.append(f"User: {user_message}")
    
    # Create full context string
    full_context = "\n".join(conversation_history)
    
    response = await call_gemini_api(
        contents=[full_context],
        response_schema=TextResponseModel,
        system_instruction_text="You are a helpful assistant"
    )
    
    # Add response to history
    conversation_history.append(f"Assistant: {response.text}")
    return response.text
```

**Problems:**
- ⚠️ Manual history management
- ⚠️ No session management
- ⚠️ No persistence
- ⚠️ No cost tracking per conversation

**After (Automatic Memory):**
```python
from chat import ChatManager

class ConversationManager:
    def __init__(self):
        self.chat_manager = ChatManager()
        self.session_id = None
    
    async def chat(self, user_message: str):
        if self.session_id is None:
            self.session_id = await self.chat_manager.create_chat_session(
                system_instruction="You are a helpful assistant"
            )
        
        response, _, _, cost = await self.chat_manager.send_message(
            self.session_id, user_message
        )
        return response
    
    async def get_history(self):
        if self.session_id:
            return await self.chat_manager.get_chat_history(self.session_id)
        return []
```

## 🚀 Integration Options

### Option 1: Drop-in Replacement

For simple migrations where you want immediate memory benefits:

```python
from chat import create_simple_chat, quick_chat_exchange

# For one-off responses (no persistent session)
async def quick_response(message: str):
    return await quick_chat_exchange(
        message=message,
        system_instruction="You are a helpful assistant"
    )

# For conversation with memory
async def conversation_response(message: str, session_id: str = None):
    if session_id is None:
        manager, session_id = await create_simple_chat()
    else:
        manager = ChatManager()
    
    response, _, _, _ = await manager.send_message(session_id, message)
    return response, session_id
```

### Option 2: Hybrid System

Use both approaches based on the use case:

```python
from chat import ChatManager

class HybridChatSystem:
    """Combines existing genai_utils with new chat memory"""
    
    def __init__(self):
        self.chat_manager = ChatManager()
        self.active_sessions = {}
    
    async def structured_query(self, query: str, response_schema: Type[pydantic.BaseModel]):
        """Use existing genai_utils for structured data extraction"""
        return await call_gemini_api(
            contents=[query],
            response_schema=response_schema,
            system_instruction_text="You are a data analyst"
        )
    
    async def conversational_chat(self, user_message: str, user_id: str):
        """Use new chat system for conversations with memory"""
        if user_id not in self.active_sessions:
            session_id = await self.chat_manager.create_chat_session(
                system_instruction="You are a helpful assistant that remembers our conversation"
            )
            self.active_sessions[user_id] = session_id
        
        session_id = self.active_sessions[user_id]
        response, _, _, cost = await self.chat_manager.send_message(session_id, user_message)
        
        return {
            "response": response,
            "conversation_cost": cost,
            "session_id": session_id
        }
    
    async def analyze_data(self, data: str):
        """Use genai_utils for structured analysis"""
        return await self.structured_query(
            f"Analyze this data: {data}",
            DataAnalysisResult
        )
    
    async def chat_about_analysis(self, user_id: str, question: str):
        """Use chat system for discussing analysis results"""
        return await self.conversational_chat(question, user_id)
```

### Option 3: Wrapper Integration

Wrap your existing functions to add memory capabilities:

```python
from chat import ChatManager

class MemoryEnabledChat:
    """Wrapper that adds memory to existing chat logic"""
    
    def __init__(self):
        self.chat_manager = ChatManager()
        self.sessions = {}
    
    async def chat(self, user_message: str, conversation_id: str = None):
        """Drop-in replacement for existing chat function"""
        
        if conversation_id not in self.sessions:
            session_id = await self.chat_manager.create_chat_session(
                system_instruction="You are a helpful assistant"
            )
            self.sessions[conversation_id] = session_id
        
        session_id = self.sessions[conversation_id]
        response, _, _, _ = await self.chat_manager.send_message(session_id, user_message)
        
        return response  # Same return format as before
    
    async def get_conversation_history(self, conversation_id: str):
        """New feature: get full conversation history"""
        if conversation_id in self.sessions:
            session_id = self.sessions[conversation_id]
            return await self.chat_manager.get_chat_history(session_id)
        return []
    
    async def get_conversation_cost(self, conversation_id: str):
        """New feature: get total conversation cost"""
        if conversation_id in self.sessions:
            session_id = self.sessions[conversation_id]
            session_info = await self.chat_manager.get_session_info(session_id)
            return session_info.total_cost
        return 0.0
    
    async def export_conversation(self, conversation_id: str, format: str = "json"):
        """New feature: export conversation"""
        if conversation_id in self.sessions:
            session_id = self.sessions[conversation_id]
            return await self.chat_manager.export_session(session_id, format)
        return None
```

## 💼 Real-World Examples

### Customer Support System

**Before:**
```python
async def customer_support_chat(user_message: str):
    response = await call_gemini_api(
        contents=[f"Customer question: {user_message}"],
        response_schema=TextResponse,
        system_instruction_text="You are a customer support agent"
    )
    return response.text
```

**After:**
```python
from chat import ChatManager

class CustomerSupportSystem:
    def __init__(self):
        self.chat_manager = ChatManager()
        self.customer_sessions = {}
    
    async def handle_customer_message(self, customer_id: str, message: str):
        # Get or create session for this customer
        if customer_id not in self.customer_sessions:
            session_id = await self.chat_manager.create_chat_session(
                system_instruction="You are a customer support agent. Remember previous issues and context."
            )
            self.customer_sessions[customer_id] = session_id
        
        session_id = self.customer_sessions[customer_id]
        
        # Send message (automatically remembers previous context)
        response, input_tokens, output_tokens, cost = await self.chat_manager.send_message(
            session_id, message
        )
        
        return {
            "response": response,
            "session_cost": cost,
            "conversation_length": len(await self.chat_manager.get_chat_history(session_id))
        }
    
    async def get_customer_history(self, customer_id: str):
        """Review full customer interaction history"""
        if customer_id in self.customer_sessions:
            session_id = self.customer_sessions[customer_id]
            return await self.chat_manager.get_chat_history(session_id)
        return []
    
    async def escalate_to_human(self, customer_id: str):
        """Export conversation for human agent"""
        if customer_id in self.customer_sessions:
            session_id = self.customer_sessions[customer_id]
            return await self.chat_manager.export_session(session_id, "markdown")
        return None
```

### Educational Tutoring System

```python
from chat import ChatManager

class TutoringSystem:
    def __init__(self):
        self.chat_manager = ChatManager()
        self.student_sessions = {}
    
    async def start_tutoring_session(self, student_id: str, subject: str):
        """Start a new tutoring session"""
        session_id = await self.chat_manager.create_chat_session(
            system_instruction=f"You are a {subject} tutor. Track the student's progress and adapt your teaching style."
        )
        self.student_sessions[student_id] = session_id
        return session_id
    
    async def tutor_interaction(self, student_id: str, question: str):
        """Handle student question with context awareness"""
        if student_id not in self.student_sessions:
            raise ValueError("No active tutoring session for student")
        
        session_id = self.student_sessions[student_id]
        response, _, _, _ = await self.chat_manager.send_message(session_id, question)
        return response
    
    async def assess_progress(self, student_id: str):
        """Analyze student's learning progress"""
        if student_id not in self.student_sessions:
            return None
        
        session_id = self.student_sessions[student_id]
        history = await self.chat_manager.get_chat_history(session_id)
        
        # Use genai_utils for structured analysis
        analysis_prompt = f"Analyze this tutoring session and assess student progress:\n"
        for msg in history:
            analysis_prompt += f"{msg.role}: {msg.content}\n"
        
        return await call_gemini_api(
            contents=[analysis_prompt],
            response_schema=ProgressAssessment,
            system_instruction_text="You are an educational assessment expert"
        )
```

### Multi-Agent System

```python
from chat import ChatManager

class MultiAgentSystem:
    def __init__(self):
        self.chat_manager = ChatManager()
        self.agent_sessions = {}
    
    async def create_agent(self, agent_id: str, role: str, instructions: str):
        """Create a specialized agent with memory"""
        session_id = await self.chat_manager.create_chat_session(
            system_instruction=f"You are {role}. {instructions}"
        )
        self.agent_sessions[agent_id] = session_id
        return session_id
    
    async def agent_conversation(self, agent_id: str, message: str):
        """Send message to specific agent"""
        if agent_id not in self.agent_sessions:
            raise ValueError(f"Agent {agent_id} not found")
        
        session_id = self.agent_sessions[agent_id]
        response, _, _, _ = await self.chat_manager.send_message(session_id, message)
        return response
    
    async def agent_collaboration(self, task: str):
        """Coordinate multiple agents on a task"""
        # Analyst agent
        analyst_response = await self.agent_conversation(
            "analyst", f"Analyze this task: {task}"
        )
        
        # Planner agent (with context from analyst)
        planner_response = await self.agent_conversation(
            "planner", f"Based on this analysis: {analyst_response}, create a plan for: {task}"
        )
        
        # Executor agent
        executor_response = await self.agent_conversation(
            "executor", f"Execute this plan: {planner_response}"
        )
        
        return {
            "analysis": analyst_response,
            "plan": planner_response,
            "execution": executor_response
        }
```

## 📋 Best Practices

### 1. Choose the Right Tool for the Job

```python
# Use genai_utils.py for:
- Structured data extraction
- One-off API calls
- Pydantic model validation
- Stateless operations

# Use chat system for:
- Conversational interfaces
- Context-dependent interactions
- Multi-turn dialogues
- Session-based applications
```

### 2. Session Management

```python
class SessionManager:
    def __init__(self):
        self.chat_manager = ChatManager()
        self.user_sessions = {}
    
    async def get_or_create_session(self, user_id: str, context: str = None):
        """Get existing session or create new one"""
        if user_id not in self.user_sessions:
            session_id = await self.chat_manager.create_chat_session(
                system_instruction=context or "You are a helpful assistant"
            )
            self.user_sessions[user_id] = session_id
        return self.user_sessions[user_id]
    
    async def cleanup_inactive_sessions(self, max_age_hours: int = 24):
        """Clean up old sessions"""
        # Implementation for session cleanup
        pass
```

### 3. Error Handling

```python
async def robust_chat_interaction(user_id: str, message: str):
    try:
        manager = ChatManager()
        session_id = await manager.create_chat_session()
        response, _, _, _ = await manager.send_message(session_id, message)
        return response
    
    except Exception as e:
        logger.error(f"Chat interaction failed: {e}")
        
        # Fallback to stateless genai_utils
        try:
            response = await call_gemini_api(
                contents=[message],
                response_schema=TextResponse,
                system_instruction_text="You are a helpful assistant"
            )
            return response.text
        except Exception as fallback_error:
            logger.error(f"Fallback also failed: {fallback_error}")
            return "I'm experiencing technical difficulties. Please try again."
```

### 4. Cost Monitoring

```python
class CostAwareChatSystem:
    def __init__(self, cost_limit: float = 10.0):
        self.chat_manager = ChatManager()
        self.cost_limit = cost_limit
        self.session_costs = {}
    
    async def send_message_with_cost_check(self, session_id: str, message: str):
        """Send message with cost monitoring"""
        current_cost = self.session_costs.get(session_id, 0.0)
        
        if current_cost >= self.cost_limit:
            return "Cost limit reached for this conversation."
        
        response, _, _, cost = await self.chat_manager.send_message(session_id, message)
        
        if cost:
            self.session_costs[session_id] = current_cost + cost
        
        return response
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Import Errors
```python
# If you get import errors, ensure proper path setup
import sys
from pathlib import Path

# Add gemini_methods to path
gemini_methods_path = Path(__file__).parent.parent
sys.path.insert(0, str(gemini_methods_path))

from chat import ChatManager
```

#### 2. Client Not Initialized
```python
# Ensure your API key is set before importing
import os
os.environ["GOOGLE_API_KEY"] = "your-api-key"

from chat import ChatManager  # This will now work
```

#### 3. Database Connection Issues
```python
# Chat system works without database
manager = ChatManager(db_pool=None)  # No persistence, but memory works

# Or provide your existing pool
from your_db_module import get_db_pool
manager = ChatManager(db_pool=get_db_pool())
```

#### 4. Memory vs. Structured Responses
```python
# If you need both memory AND structured responses:
async def structured_chat_response(session_id: str, query: str, schema: Type[pydantic.BaseModel]):
    """Get structured response with conversation memory"""
    
    # Get conversation history for context
    manager = ChatManager()
    history = await manager.get_chat_history(session_id)
    
    # Build context from history
    context = "\n".join([f"{msg.role}: {msg.content}" for msg in history[-5:]])  # Last 5 messages
    
    # Use genai_utils for structured response with context
    full_query = f"Context:\n{context}\n\nCurrent Query: {query}"
    
    response = await call_gemini_api(
        contents=[full_query],
        response_schema=schema,
        system_instruction_text="Consider the conversation context when responding"
    )
    
    # Also update chat history
    await manager.send_message(session_id, query)
    
    return response
```

## 🎯 Quick Migration Checklist

- [ ] Identify current chat functions using `genai_utils.py`
- [ ] Determine which need conversation memory
- [ ] Choose integration approach (drop-in, hybrid, or wrapper)
- [ ] Update imports to include chat system
- [ ] Modify functions to use session management
- [ ] Add error handling and fallbacks
- [ ] Test with existing workflows
- [ ] Monitor costs and performance
- [ ] Document session management for your team

## 🚀 Getting Started

1. **Start Small**: Pick one simple chat function and add memory
2. **Test Integration**: Ensure compatibility with existing code
3. **Expand Gradually**: Migrate more functions as you gain confidence
4. **Monitor Performance**: Watch costs and response times
5. **Leverage Both Systems**: Use each tool for its strengths

The chat system extends your existing `genai_utils.py` capabilities without replacing them. You can have the best of both worlds! 🎉 