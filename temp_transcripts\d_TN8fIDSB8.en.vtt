WEBVTT
Kind: captions
Language: en

00:00:00.199 --> 00:00:01.709 align:start position:0%
 
in<00:00:00.320><c> the</c><00:00:00.520><c> first</c><00:00:00.799><c> part</c><00:00:00.960><c> of</c><00:00:01.120><c> this</c><00:00:01.280><c> course</c><00:00:01.520><c> you've</c>

00:00:01.709 --> 00:00:01.719 align:start position:0%
in the first part of this course you've
 

00:00:01.719 --> 00:00:04.470 align:start position:0%
in the first part of this course you've
learned<00:00:01.959><c> from</c><00:00:02.159><c> HL</c><00:00:02.760><c> how</c><00:00:02.879><c> to</c><00:00:03.040><c> run</c><00:00:03.480><c> automations</c>

00:00:04.470 --> 00:00:04.480 align:start position:0%
learned from HL how to run automations
 

00:00:04.480 --> 00:00:07.430 align:start position:0%
learned from HL how to run automations
with<00:00:04.759><c> weight</c><00:00:04.960><c> andb</c><00:00:05.440><c> model</c><00:00:05.759><c> registry</c><00:00:06.600><c> and</c><00:00:06.839><c> web</c>

00:00:07.430 --> 00:00:07.440 align:start position:0%
with weight andb model registry and web
 

00:00:07.440 --> 00:00:10.589 align:start position:0%
with weight andb model registry and web
hooks<00:00:08.440><c> in</c><00:00:08.679><c> this</c><00:00:08.880><c> lesson</c><00:00:09.599><c> we</c><00:00:09.719><c> will</c><00:00:09.920><c> focus</c><00:00:10.200><c> on</c><00:00:10.360><c> a</c>

00:00:10.589 --> 00:00:10.599 align:start position:0%
hooks in this lesson we will focus on a
 

00:00:10.599 --> 00:00:12.230 align:start position:0%
hooks in this lesson we will focus on a
different<00:00:10.920><c> way</c><00:00:11.160><c> of</c><00:00:11.559><c> of</c><00:00:11.679><c> running</c><00:00:12.000><c> our</c>

00:00:12.230 --> 00:00:12.240 align:start position:0%
different way of of running our
 

00:00:12.240 --> 00:00:15.509 align:start position:0%
different way of of running our
automations<00:00:12.759><c> using</c><00:00:13.080><c> qu</c><00:00:13.320><c> inves</c><00:00:14.200><c> launch</c><00:00:15.200><c> and</c>

00:00:15.509 --> 00:00:15.519 align:start position:0%
automations using qu inves launch and
 

00:00:15.519 --> 00:00:17.230 align:start position:0%
automations using qu inves launch and
let's<00:00:15.759><c> first</c><00:00:16.240><c> uh</c><00:00:16.320><c> take</c><00:00:16.480><c> a</c><00:00:16.600><c> look</c><00:00:16.800><c> how</c><00:00:16.960><c> this</c><00:00:17.080><c> is</c>

00:00:17.230 --> 00:00:17.240 align:start position:0%
let's first uh take a look how this is
 

00:00:17.240 --> 00:00:18.910 align:start position:0%
let's first uh take a look how this is
done<00:00:17.520><c> we'll</c><00:00:17.680><c> use</c><00:00:17.920><c> the</c><00:00:18.080><c> simplest</c><00:00:18.520><c> possible</c>

00:00:18.910 --> 00:00:18.920 align:start position:0%
done we'll use the simplest possible
 

00:00:18.920 --> 00:00:21.990 align:start position:0%
done we'll use the simplest possible
setup<00:00:19.560><c> with</c><00:00:19.720><c> a</c><00:00:20.039><c> Docker</c><00:00:20.480><c> queue</c><00:00:21.480><c> and</c><00:00:21.600><c> a</c><00:00:21.760><c> simple</c>

00:00:21.990 --> 00:00:22.000 align:start position:0%
setup with a Docker queue and a simple
 

00:00:22.000 --> 00:00:23.390 align:start position:0%
setup with a Docker queue and a simple
Docker<00:00:22.359><c> image</c><00:00:22.760><c> that</c><00:00:22.840><c> will</c><00:00:23.000><c> run</c><00:00:23.199><c> our</c>

00:00:23.390 --> 00:00:23.400 align:start position:0%
Docker image that will run our
 

00:00:23.400 --> 00:00:25.990 align:start position:0%
Docker image that will run our
evaluation<00:00:24.000><c> script</c><00:00:24.960><c> but</c><00:00:25.320><c> uh</c><00:00:25.439><c> you</c><00:00:25.560><c> will</c><00:00:25.760><c> also</c>

00:00:25.990 --> 00:00:26.000 align:start position:0%
evaluation script but uh you will also
 

00:00:26.000 --> 00:00:28.830 align:start position:0%
evaluation script but uh you will also
learn<00:00:26.599><c> uh</c><00:00:26.760><c> in</c><00:00:27.199><c> um</c><00:00:27.400><c> a</c><00:00:27.519><c> future</c><00:00:27.880><c> lesson</c><00:00:28.560><c> more</c>

00:00:28.830 --> 00:00:28.840 align:start position:0%
learn uh in um a future lesson more
 

00:00:28.840 --> 00:00:30.870 align:start position:0%
learn uh in um a future lesson more
about<00:00:29.080><c> launch</c><00:00:29.640><c> which</c><00:00:29.759><c> is</c><00:00:30.000><c> a</c><00:00:30.160><c> very</c><00:00:30.359><c> powerful</c>

00:00:30.870 --> 00:00:30.880 align:start position:0%
about launch which is a very powerful
 

00:00:30.880 --> 00:00:34.950 align:start position:0%
about launch which is a very powerful
concept<00:00:31.320><c> of</c><00:00:32.160><c> uh</c><00:00:32.279><c> running</c><00:00:33.000><c> jobs</c><00:00:33.559><c> on</c><00:00:33.960><c> um</c><00:00:34.480><c> on</c><00:00:34.600><c> a</c><00:00:34.840><c> on</c>

00:00:34.950 --> 00:00:34.960 align:start position:0%
concept of uh running jobs on um on a on
 

00:00:34.960 --> 00:00:36.869 align:start position:0%
concept of uh running jobs on um on a on
a<00:00:35.079><c> kubernetes</c><00:00:35.640><c> cluster</c><00:00:36.079><c> or</c><00:00:36.320><c> distributed</c>

00:00:36.869 --> 00:00:36.879 align:start position:0%
a kubernetes cluster or distributed
 

00:00:36.879 --> 00:00:39.270 align:start position:0%
a kubernetes cluster or distributed
compute<00:00:37.239><c> that</c><00:00:37.320><c> you</c><00:00:37.440><c> have</c><00:00:37.640><c> access</c><00:00:37.879><c> to</c><00:00:38.879><c> it</c><00:00:39.040><c> has</c><00:00:39.160><c> a</c>

00:00:39.270 --> 00:00:39.280 align:start position:0%
compute that you have access to it has a
 

00:00:39.280 --> 00:00:41.229 align:start position:0%
compute that you have access to it has a
lot<00:00:39.399><c> of</c><00:00:39.559><c> value</c><00:00:39.840><c> Beyond</c><00:00:40.360><c> Simple</c>

00:00:41.229 --> 00:00:41.239 align:start position:0%
lot of value Beyond Simple
 

00:00:41.239 --> 00:00:44.470 align:start position:0%
lot of value Beyond Simple
automations<00:00:42.239><c> so</c><00:00:42.760><c> uh</c><00:00:42.960><c> let's</c><00:00:43.440><c> uh</c><00:00:43.920><c> again</c><00:00:44.200><c> start</c>

00:00:44.470 --> 00:00:44.480 align:start position:0%
automations so uh let's uh again start
 

00:00:44.480 --> 00:00:47.630 align:start position:0%
automations so uh let's uh again start
with<00:00:44.600><c> a</c><00:00:44.800><c> simple</c><00:00:45.480><c> a</c><00:00:45.640><c> very</c><00:00:45.960><c> simple</c><00:00:46.199><c> use</c><00:00:46.520><c> case</c><00:00:47.480><c> uh</c>

00:00:47.630 --> 00:00:47.640 align:start position:0%
with a simple a very simple use case uh
 

00:00:47.640 --> 00:00:49.709 align:start position:0%
with a simple a very simple use case uh
first<00:00:48.000><c> start</c><00:00:48.199><c> we'll</c><00:00:48.440><c> start</c><00:00:48.719><c> with</c><00:00:48.879><c> creating</c><00:00:49.600><c> a</c>

00:00:49.709 --> 00:00:49.719 align:start position:0%
first start we'll start with creating a
 

00:00:49.719 --> 00:00:53.709 align:start position:0%
first start we'll start with creating a
Docker<00:00:50.320><c> image</c><00:00:51.320><c> that</c><00:00:51.879><c> um</c><00:00:52.879><c> that</c><00:00:53.000><c> will</c><00:00:53.160><c> run</c><00:00:53.440><c> our</c>

00:00:53.709 --> 00:00:53.719 align:start position:0%
Docker image that um that will run our
 

00:00:53.719 --> 00:00:55.349 align:start position:0%
Docker image that um that will run our
evaluation<00:00:54.239><c> script</c><00:00:54.680><c> so</c><00:00:54.800><c> let's</c><00:00:55.000><c> take</c><00:00:55.120><c> a</c><00:00:55.239><c> look</c>

00:00:55.349 --> 00:00:55.359 align:start position:0%
evaluation script so let's take a look
 

00:00:55.359 --> 00:00:57.349 align:start position:0%
evaluation script so let's take a look
at<00:00:55.520><c> this</c><00:00:55.920><c> uh</c><00:00:56.160><c> this</c><00:00:56.280><c> Docker</c><00:00:56.600><c> file</c><00:00:57.120><c> it's</c><00:00:57.239><c> going</c>

00:00:57.349 --> 00:00:57.359 align:start position:0%
at this uh this Docker file it's going
 

00:00:57.359 --> 00:00:59.630 align:start position:0%
at this uh this Docker file it's going
to<00:00:57.480><c> be</c><00:00:57.600><c> a</c><00:00:57.719><c> super</c><00:00:58.000><c> simple</c><00:00:58.399><c> python</c><00:00:58.760><c> environment</c>

00:00:59.630 --> 00:00:59.640 align:start position:0%
to be a super simple python environment
 

00:00:59.640 --> 00:01:00.910 align:start position:0%
to be a super simple python environment
will

00:01:00.910 --> 00:01:00.920 align:start position:0%
will
 

00:01:00.920 --> 00:01:02.990 align:start position:0%
will
uh<00:01:01.239><c> install</c><00:01:01.680><c> our</c><00:01:01.960><c> requirements</c><00:01:02.719><c> for</c>

00:01:02.990 --> 00:01:03.000 align:start position:0%
uh install our requirements for
 

00:01:03.000 --> 00:01:05.710 align:start position:0%
uh install our requirements for
evaluation<00:01:04.000><c> and</c><00:01:04.199><c> then</c><00:01:04.479><c> it</c><00:01:04.600><c> will</c><00:01:04.760><c> trigger</c><00:01:05.199><c> the</c>

00:01:05.710 --> 00:01:05.720 align:start position:0%
evaluation and then it will trigger the
 

00:01:05.720 --> 00:01:08.990 align:start position:0%
evaluation and then it will trigger the
eval<00:01:06.320><c> P</c><00:01:06.520><c> script</c><00:01:06.960><c> that</c><00:01:07.080><c> we</c><00:01:07.200><c> have</c><00:01:07.360><c> just</c>

00:01:08.990 --> 00:01:09.000 align:start position:0%
eval P script that we have just
 

00:01:09.000 --> 00:01:13.830 align:start position:0%
eval P script that we have just
reviewed<00:01:10.000><c> so</c><00:01:10.560><c> let's</c><00:01:11.200><c> uh</c><00:01:11.600><c> copy</c><00:01:11.920><c> this</c>

00:01:13.830 --> 00:01:13.840 align:start position:0%
reviewed so let's uh copy this
 

00:01:13.840 --> 00:01:19.710 align:start position:0%
reviewed so let's uh copy this
command<00:01:14.840><c> and</c><00:01:15.240><c> let's</c><00:01:15.960><c> create</c><00:01:16.200><c> our</c><00:01:16.400><c> Docker</c>

00:01:19.710 --> 00:01:19.720 align:start position:0%
 
 

00:01:19.720 --> 00:01:23.429 align:start position:0%
 
image<00:01:20.720><c> okay</c><00:01:21.040><c> now</c><00:01:21.280><c> that</c><00:01:21.560><c> the</c><00:01:21.920><c> image</c><00:01:22.280><c> is</c><00:01:22.479><c> built</c>

00:01:23.429 --> 00:01:23.439 align:start position:0%
image okay now that the image is built
 

00:01:23.439 --> 00:01:25.069 align:start position:0%
image okay now that the image is built
we<00:01:23.560><c> can</c><00:01:23.759><c> take</c><00:01:24.000><c> this</c><00:01:24.200><c> image</c><00:01:24.520><c> and</c><00:01:24.680><c> create</c><00:01:24.960><c> a</c>

00:01:25.069 --> 00:01:25.079 align:start position:0%
we can take this image and create a
 

00:01:25.079 --> 00:01:28.109 align:start position:0%
we can take this image and create a
weight<00:01:25.320><c> syis</c><00:01:25.880><c> launch</c><00:01:26.400><c> job</c><00:01:27.400><c> and</c><00:01:27.600><c> you</c><00:01:27.720><c> will</c><00:01:27.880><c> see</c>

00:01:28.109 --> 00:01:28.119 align:start position:0%
weight syis launch job and you will see
 

00:01:28.119 --> 00:01:30.069 align:start position:0%
weight syis launch job and you will see
how<00:01:28.240><c> a</c><00:01:28.400><c> job</c><00:01:28.600><c> in</c><00:01:28.720><c> weight</c><00:01:28.920><c> syis</c><00:01:29.439><c> is</c><00:01:29.560><c> essentially</c>

00:01:30.069 --> 00:01:30.079 align:start position:0%
how a job in weight syis is essentially
 

00:01:30.079 --> 00:01:32.230 align:start position:0%
how a job in weight syis is essentially
a<00:01:30.200><c> template</c><00:01:30.759><c> that</c><00:01:30.880><c> you</c><00:01:30.960><c> can</c><00:01:31.159><c> trigger</c><00:01:31.960><c> through</c>

00:01:32.230 --> 00:01:32.240 align:start position:0%
a template that you can trigger through
 

00:01:32.240 --> 00:01:34.990 align:start position:0%
a template that you can trigger through
weights<00:01:32.399><c> and</c><00:01:32.600><c> biases</c><00:01:33.280><c> and</c><00:01:33.600><c> um</c><00:01:34.439><c> and</c><00:01:34.600><c> use</c><00:01:34.720><c> it</c><00:01:34.840><c> to</c>

00:01:34.990 --> 00:01:35.000 align:start position:0%
weights and biases and um and use it to
 

00:01:35.000 --> 00:01:37.350 align:start position:0%
weights and biases and um and use it to
run<00:01:35.240><c> experiments</c><00:01:35.840><c> to</c><00:01:36.040><c> run</c><00:01:36.479><c> uh</c><00:01:36.640><c> evaluations</c>

00:01:37.350 --> 00:01:37.360 align:start position:0%
run experiments to run uh evaluations
 

00:01:37.360 --> 00:01:39.510 align:start position:0%
run experiments to run uh evaluations
and<00:01:37.479><c> do</c><00:01:37.759><c> many</c><00:01:38.040><c> different</c><00:01:38.399><c> things</c><00:01:38.759><c> that</c><00:01:39.119><c> uh</c>

00:01:39.510 --> 00:01:39.520 align:start position:0%
and do many different things that uh
 

00:01:39.520 --> 00:01:40.830 align:start position:0%
and do many different things that uh
essentially<00:01:40.000><c> everything</c><00:01:40.320><c> that</c><00:01:40.439><c> you</c><00:01:40.520><c> can</c><00:01:40.640><c> do</c>

00:01:40.830 --> 00:01:40.840 align:start position:0%
essentially everything that you can do
 

00:01:40.840 --> 00:01:43.149 align:start position:0%
essentially everything that you can do
in<00:01:41.159><c> with</c><00:01:41.360><c> with</c><00:01:41.520><c> python</c><00:01:41.840><c> you</c><00:01:41.960><c> can</c><00:01:42.119><c> probably</c><00:01:43.040><c> uh</c>

00:01:43.149 --> 00:01:43.159 align:start position:0%
in with with python you can probably uh
 

00:01:43.159 --> 00:01:45.429 align:start position:0%
in with with python you can probably uh
do<00:01:43.320><c> with</c><00:01:43.439><c> a</c><00:01:43.560><c> weight</c><00:01:43.720><c> and</c><00:01:44.240><c> job</c><00:01:44.680><c> so</c><00:01:44.880><c> we'll</c><00:01:45.200><c> use</c>

00:01:45.429 --> 00:01:45.439 align:start position:0%
do with a weight and job so we'll use
 

00:01:45.439 --> 00:01:48.270 align:start position:0%
do with a weight and job so we'll use
this<00:01:45.560><c> command</c><00:01:45.920><c> we</c><00:01:46.119><c> use</c><00:01:46.280><c> was</c><00:01:46.479><c> andb</c><00:01:47.000><c> CLI</c><00:01:48.000><c> uh</c><00:01:48.119><c> to</c>

00:01:48.270 --> 00:01:48.280 align:start position:0%
this command we use was andb CLI uh to
 

00:01:48.280 --> 00:01:50.830 align:start position:0%
this command we use was andb CLI uh to
create<00:01:48.520><c> a</c><00:01:48.680><c> job</c><00:01:49.000><c> you</c><00:01:49.119><c> can</c><00:01:49.320><c> also</c><00:01:49.640><c> create</c><00:01:50.479><c> a</c><00:01:50.640><c> job</c>

00:01:50.830 --> 00:01:50.840 align:start position:0%
create a job you can also create a job
 

00:01:50.840 --> 00:01:53.709 align:start position:0%
create a job you can also create a job
whenever<00:01:51.200><c> you</c><00:01:51.560><c> you</c><00:01:51.719><c> kick</c><00:01:51.920><c> off</c><00:01:52.079><c> a</c><00:01:52.200><c> was</c><00:01:52.360><c> andb</c><00:01:52.799><c> run</c>

00:01:53.709 --> 00:01:53.719 align:start position:0%
whenever you you kick off a was andb run
 

00:01:53.719 --> 00:01:55.950 align:start position:0%
whenever you you kick off a was andb run
um<00:01:54.040><c> and</c><00:01:54.200><c> log</c><00:01:54.439><c> the</c><00:01:54.640><c> code</c><00:01:55.159><c> with</c><00:01:55.360><c> along</c><00:01:55.640><c> with</c><00:01:55.799><c> that</c>

00:01:55.950 --> 00:01:55.960 align:start position:0%
um and log the code with along with that
 

00:01:55.960 --> 00:01:58.029 align:start position:0%
um and log the code with along with that
run<00:01:56.240><c> you</c><00:01:56.320><c> can</c><00:01:56.520><c> then</c><00:01:56.680><c> replicate</c><00:01:57.159><c> it</c><00:01:57.320><c> using</c><00:01:57.840><c> we</c>

00:01:58.029 --> 00:01:58.039 align:start position:0%
run you can then replicate it using we
 

00:01:58.039 --> 00:02:00.590 align:start position:0%
run you can then replicate it using we
andb<00:01:58.439><c> launch</c><00:01:59.119><c> so</c><00:01:59.479><c> let's</c><00:01:59.880><c> create</c><00:02:00.079><c> a</c><00:02:00.200><c> job</c><00:02:00.360><c> using</c>

00:02:00.590 --> 00:02:00.600 align:start position:0%
andb launch so let's create a job using
 

00:02:00.600 --> 00:02:05.270 align:start position:0%
andb launch so let's create a job using
qu<00:02:00.799><c> and</c>

00:02:05.270 --> 00:02:05.280 align:start position:0%
 
 

00:02:05.280 --> 00:02:07.510 align:start position:0%
 
CLI<00:02:06.280><c> and</c><00:02:06.439><c> you</c><00:02:06.560><c> can</c><00:02:06.680><c> see</c><00:02:06.880><c> this</c><00:02:06.960><c> is</c><00:02:07.119><c> creating</c><00:02:07.399><c> a</c>

00:02:07.510 --> 00:02:07.520 align:start position:0%
CLI and you can see this is creating a
 

00:02:07.520 --> 00:02:09.830 align:start position:0%
CLI and you can see this is creating a
long<00:02:07.759><c> job</c><00:02:08.039><c> of</c><00:02:08.239><c> types</c><00:02:08.879><c> Type</c><00:02:09.160><c> image</c><00:02:09.560><c> this</c><00:02:09.679><c> is</c>

00:02:09.830 --> 00:02:09.840 align:start position:0%
long job of types Type image this is
 

00:02:09.840 --> 00:02:13.430 align:start position:0%
long job of types Type image this is
creating<00:02:10.399><c> um</c><00:02:10.640><c> a</c><00:02:10.800><c> doer</c><00:02:11.120><c> image</c>

00:02:13.430 --> 00:02:13.440 align:start position:0%
 
 

00:02:13.440 --> 00:02:16.550 align:start position:0%
 
job<00:02:14.440><c> and</c><00:02:14.599><c> it</c><00:02:14.800><c> created</c><00:02:15.239><c> this</c><00:02:15.440><c> job</c><00:02:15.640><c> in</c><00:02:15.959><c> our</c>

00:02:16.550 --> 00:02:16.560 align:start position:0%
job and it created this job in our
 

00:02:16.560 --> 00:02:19.790 align:start position:0%
job and it created this job in our
project<00:02:17.560><c> so</c><00:02:18.160><c> uh</c><00:02:18.400><c> let's</c><00:02:18.959><c> uh</c><00:02:19.239><c> go</c><00:02:19.360><c> to</c><00:02:19.560><c> this</c>

00:02:19.790 --> 00:02:19.800 align:start position:0%
project so uh let's uh go to this
 

00:02:19.800 --> 00:02:22.470 align:start position:0%
project so uh let's uh go to this
project<00:02:20.120><c> and</c><00:02:20.280><c> take</c><00:02:20.400><c> a</c><00:02:20.560><c> look</c><00:02:20.680><c> at</c><00:02:20.800><c> the</c><00:02:21.319><c> job</c><00:02:22.319><c> that</c>

00:02:22.470 --> 00:02:22.480 align:start position:0%
project and take a look at the job that
 

00:02:22.480 --> 00:02:25.150 align:start position:0%
project and take a look at the job that
we<00:02:22.640><c> just</c>

00:02:25.150 --> 00:02:25.160 align:start position:0%
 
 

00:02:25.160 --> 00:02:28.030 align:start position:0%
 
created<00:02:26.160><c> you</c><00:02:26.280><c> can</c><00:02:26.440><c> see</c><00:02:26.680><c> we</c><00:02:26.879><c> are</c><00:02:27.080><c> in</c><00:02:27.239><c> a</c><00:02:27.360><c> w</c><00:02:27.680><c> buus</c>

00:02:28.030 --> 00:02:28.040 align:start position:0%
created you can see we are in a w buus
 

00:02:28.040 --> 00:02:30.509 align:start position:0%
created you can see we are in a w buus
project<00:02:28.440><c> we</c><00:02:28.560><c> have</c><00:02:28.680><c> a</c><00:02:28.800><c> list</c><00:02:28.959><c> of</c><00:02:29.080><c> jobs</c><00:02:29.440><c> here</c><00:02:30.319><c> and</c>

00:02:30.509 --> 00:02:30.519 align:start position:0%
project we have a list of jobs here and
 

00:02:30.519 --> 00:02:32.710 align:start position:0%
project we have a list of jobs here and
this<00:02:30.640><c> is</c><00:02:30.959><c> our</c><00:02:31.480><c> evaluation</c><00:02:32.040><c> job</c><00:02:32.360><c> you</c><00:02:32.440><c> can</c><00:02:32.560><c> see</c>

00:02:32.710 --> 00:02:32.720 align:start position:0%
this is our evaluation job you can see
 

00:02:32.720 --> 00:02:34.589 align:start position:0%
this is our evaluation job you can see
we<00:02:32.879><c> just</c><00:02:33.080><c> created</c><00:02:33.519><c> it</c><00:02:33.680><c> it</c><00:02:33.800><c> doesn't</c><00:02:34.080><c> have</c><00:02:34.200><c> any</c>

00:02:34.589 --> 00:02:34.599 align:start position:0%
we just created it it doesn't have any
 

00:02:34.599 --> 00:02:37.670 align:start position:0%
we just created it it doesn't have any
runs<00:02:35.599><c> um</c><00:02:35.800><c> if</c><00:02:35.879><c> we</c><00:02:36.080><c> click</c><00:02:36.280><c> on</c><00:02:36.400><c> it</c><00:02:36.640><c> we</c><00:02:36.720><c> can</c>

00:02:37.670 --> 00:02:37.680 align:start position:0%
runs um if we click on it we can
 

00:02:37.680 --> 00:02:40.869 align:start position:0%
runs um if we click on it we can
expand<00:02:38.680><c> and</c><00:02:39.640><c> uh</c><00:02:39.840><c> yeah</c><00:02:39.959><c> we</c><00:02:40.080><c> can</c><00:02:40.319><c> we</c><00:02:40.400><c> can</c><00:02:40.560><c> launch</c>

00:02:40.869 --> 00:02:40.879 align:start position:0%
expand and uh yeah we can we can launch
 

00:02:40.879 --> 00:02:42.509 align:start position:0%
expand and uh yeah we can we can launch
this<00:02:41.040><c> job</c><00:02:41.280><c> but</c><00:02:41.400><c> to</c><00:02:41.519><c> launch</c><00:02:41.760><c> a</c><00:02:41.920><c> job</c><00:02:42.120><c> we</c><00:02:42.239><c> need</c>

00:02:42.509 --> 00:02:42.519 align:start position:0%
this job but to launch a job we need
 

00:02:42.519 --> 00:02:45.630 align:start position:0%
this job but to launch a job we need
some<00:02:43.040><c> compute</c><00:02:43.760><c> that</c><00:02:43.920><c> the</c><00:02:44.040><c> job</c><00:02:44.200><c> can</c><00:02:44.400><c> run</c><00:02:45.360><c> and</c>

00:02:45.630 --> 00:02:45.640 align:start position:0%
some compute that the job can run and
 

00:02:45.640 --> 00:02:48.149 align:start position:0%
some compute that the job can run and
this<00:02:45.760><c> is</c><00:02:45.959><c> the</c><00:02:46.159><c> concept</c><00:02:46.599><c> of</c><00:02:46.879><c> of</c><00:02:46.959><c> a</c><00:02:47.519><c> of</c><00:02:47.599><c> a</c><00:02:47.720><c> launch</c>

00:02:48.149 --> 00:02:48.159 align:start position:0%
this is the concept of of a of a launch
 

00:02:48.159 --> 00:02:51.990 align:start position:0%
this is the concept of of a of a launch
queue<00:02:48.840><c> so</c><00:02:49.519><c> if</c><00:02:49.640><c> we</c><00:02:49.800><c> go</c><00:02:50.040><c> back</c><00:02:50.239><c> to</c><00:02:50.560><c> the</c><00:02:51.560><c> to</c><00:02:51.720><c> the</c>

00:02:51.990 --> 00:02:52.000 align:start position:0%
queue so if we go back to the to the
 

00:02:52.000 --> 00:02:54.190 align:start position:0%
queue so if we go back to the to the
instructions<00:02:53.000><c> we</c><00:02:53.280><c> now</c><00:02:53.440><c> need</c><00:02:53.599><c> to</c><00:02:53.760><c> proceed</c><00:02:54.080><c> to</c>

00:02:54.190 --> 00:02:54.200 align:start position:0%
instructions we now need to proceed to
 

00:02:54.200 --> 00:02:56.430 align:start position:0%
instructions we now need to proceed to
create<00:02:54.400><c> a</c><00:02:54.519><c> launch</c><00:02:54.840><c> queue</c><00:02:55.599><c> and</c><00:02:55.879><c> again</c><00:02:56.120><c> this</c><00:02:56.280><c> can</c>

00:02:56.430 --> 00:02:56.440 align:start position:0%
create a launch queue and again this can
 

00:02:56.440 --> 00:02:59.390 align:start position:0%
create a launch queue and again this can
run<00:02:56.760><c> in</c><00:02:57.000><c> any</c><00:02:57.239><c> type</c><00:02:57.480><c> of</c><00:02:58.000><c> um</c><00:02:58.319><c> environment</c><00:02:59.239><c> in</c>

00:02:59.390 --> 00:02:59.400 align:start position:0%
run in any type of um environment in
 

00:02:59.400 --> 00:03:01.070 align:start position:0%
run in any type of um environment in
this<00:02:59.519><c> case</c><00:02:59.840><c> we'll</c><00:02:59.959><c> keep</c><00:03:00.120><c> it</c><00:03:00.239><c> simple</c><00:03:00.720><c> and</c><00:03:00.840><c> I'll</c>

00:03:01.070 --> 00:03:01.080 align:start position:0%
this case we'll keep it simple and I'll
 

00:03:01.080 --> 00:03:03.470 align:start position:0%
this case we'll keep it simple and I'll
just<00:03:01.239><c> create</c><00:03:01.519><c> a</c><00:03:01.640><c> Quee</c><00:03:01.920><c> that</c><00:03:02.040><c> runs</c><00:03:02.319><c> on</c><00:03:02.519><c> my</c><00:03:03.159><c> on</c><00:03:03.280><c> my</c>

00:03:03.470 --> 00:03:03.480 align:start position:0%
just create a Quee that runs on my on my
 

00:03:03.480 --> 00:03:06.309 align:start position:0%
just create a Quee that runs on my on my
MacBook<00:03:04.480><c> and</c><00:03:04.680><c> we</c><00:03:04.879><c> be</c><00:03:05.000><c> able</c><00:03:05.159><c> to</c><00:03:05.319><c> trigger</c><00:03:05.920><c> jobs</c>

00:03:06.309 --> 00:03:06.319 align:start position:0%
MacBook and we be able to trigger jobs
 

00:03:06.319 --> 00:03:09.030 align:start position:0%
MacBook and we be able to trigger jobs
from<00:03:06.560><c> weights</c><00:03:06.799><c> and</c><00:03:06.959><c> bies</c><00:03:07.879><c> server</c><00:03:08.440><c> to</c><00:03:08.680><c> to</c><00:03:08.799><c> run</c>

00:03:09.030 --> 00:03:09.040 align:start position:0%
from weights and bies server to to run
 

00:03:09.040 --> 00:03:11.229 align:start position:0%
from weights and bies server to to run
on<00:03:09.239><c> my</c><00:03:09.599><c> on</c><00:03:09.720><c> my</c><00:03:09.879><c> local</c>

00:03:11.229 --> 00:03:11.239 align:start position:0%
on my on my local
 

00:03:11.239 --> 00:03:14.350 align:start position:0%
on my on my local
computer<00:03:12.239><c> so</c><00:03:12.640><c> uh</c><00:03:12.760><c> to</c><00:03:12.879><c> do</c><00:03:13.080><c> this</c><00:03:13.200><c> we'll</c><00:03:13.440><c> go</c><00:03:13.640><c> to</c><00:03:14.159><c> to</c>

00:03:14.350 --> 00:03:14.360 align:start position:0%
computer so uh to do this we'll go to to
 

00:03:14.360 --> 00:03:16.550 align:start position:0%
computer so uh to do this we'll go to to
launch<00:03:15.080><c> and</c><00:03:15.280><c> we'll</c><00:03:15.519><c> create</c><00:03:15.799><c> a</c><00:03:16.000><c> new</c><00:03:16.200><c> Docker</c>

00:03:16.550 --> 00:03:16.560 align:start position:0%
launch and we'll create a new Docker
 

00:03:16.560 --> 00:03:19.030 align:start position:0%
launch and we'll create a new Docker
queue<00:03:17.080><c> in</c><00:03:17.400><c> our</c><00:03:18.000><c> entity</c><00:03:18.400><c> in</c><00:03:18.519><c> this</c><00:03:18.680><c> case</c><00:03:18.840><c> we're</c>

00:03:19.030 --> 00:03:19.040 align:start position:0%
queue in our entity in this case we're
 

00:03:19.040 --> 00:03:21.910 align:start position:0%
queue in our entity in this case we're
working<00:03:19.360><c> on</c><00:03:19.519><c> this</c><00:03:19.680><c> review</c><00:03:20.040><c> C</c><00:03:20.720><c> entity</c><00:03:21.599><c> but</c><00:03:21.760><c> in</c>

00:03:21.910 --> 00:03:21.920 align:start position:0%
working on this review C entity but in
 

00:03:21.920 --> 00:03:23.550 align:start position:0%
working on this review C entity but in
your<00:03:22.159><c> case</c><00:03:22.400><c> you</c><00:03:22.519><c> may</c><00:03:22.720><c> want</c><00:03:22.879><c> to</c><00:03:23.120><c> work</c><00:03:23.280><c> work</c><00:03:23.440><c> in</c>

00:03:23.550 --> 00:03:23.560 align:start position:0%
your case you may want to work work in
 

00:03:23.560 --> 00:03:25.509 align:start position:0%
your case you may want to work work in
your<00:03:23.799><c> team</c><00:03:24.040><c> or</c><00:03:24.280><c> your</c><00:03:24.560><c> in</c><00:03:24.680><c> your</c><00:03:24.879><c> personal</c>

00:03:25.509 --> 00:03:25.519 align:start position:0%
your team or your in your personal
 

00:03:25.519 --> 00:03:29.789 align:start position:0%
your team or your in your personal
entity<00:03:26.519><c> so</c><00:03:26.680><c> let's</c><00:03:26.920><c> go</c><00:03:27.120><c> back</c><00:03:27.840><c> to</c><00:03:28.040><c> rev</c><00:03:28.280><c> viw</c><00:03:28.760><c> C</c>

00:03:29.789 --> 00:03:29.799 align:start position:0%
entity so let's go back to rev viw C
 

00:03:29.799 --> 00:03:32.390 align:start position:0%
entity so let's go back to rev viw C
let's<00:03:29.959><c> go</c><00:03:30.120><c> to</c>

00:03:32.390 --> 00:03:32.400 align:start position:0%
 
 

00:03:32.400 --> 00:03:35.750 align:start position:0%
 
launch<00:03:33.400><c> and</c><00:03:33.680><c> create</c><00:03:34.200><c> a</c><00:03:34.480><c> new</c>

00:03:35.750 --> 00:03:35.760 align:start position:0%
launch and create a new
 

00:03:35.760 --> 00:03:39.309 align:start position:0%
launch and create a new
queue<00:03:36.760><c> so</c><00:03:37.000><c> again</c><00:03:37.239><c> this</c><00:03:37.360><c> will</c><00:03:37.560><c> be</c><00:03:38.480><c> running</c><00:03:39.040><c> in</c>

00:03:39.309 --> 00:03:39.319 align:start position:0%
queue so again this will be running in
 

00:03:39.319 --> 00:03:40.710 align:start position:0%
queue so again this will be running in
this<00:03:39.760><c> in</c><00:03:39.959><c> this</c>

00:03:40.710 --> 00:03:40.720 align:start position:0%
this in this
 

00:03:40.720 --> 00:03:43.270 align:start position:0%
this in this
entity<00:03:41.720><c> let's</c><00:03:41.920><c> call</c><00:03:42.120><c> this</c>

00:03:43.270 --> 00:03:43.280 align:start position:0%
entity let's call this
 

00:03:43.280 --> 00:03:46.030 align:start position:0%
entity let's call this
Q

00:03:46.030 --> 00:03:46.040 align:start position:0%
Q
 

00:03:46.040 --> 00:03:49.830 align:start position:0%
Q
evaluate<00:03:47.040><c> um</c><00:03:47.879><c> we'll</c><00:03:48.159><c> use</c><00:03:48.400><c> a</c><00:03:48.680><c> Docker</c><00:03:48.959><c> container</c>

00:03:49.830 --> 00:03:49.840 align:start position:0%
evaluate um we'll use a Docker container
 

00:03:49.840 --> 00:03:52.390 align:start position:0%
evaluate um we'll use a Docker container
here<00:03:50.840><c> and</c><00:03:51.000><c> one</c><00:03:51.239><c> here</c><00:03:51.640><c> one</c><00:03:51.799><c> thing</c><00:03:52.000><c> that</c><00:03:52.120><c> we</c><00:03:52.239><c> may</c>

00:03:52.390 --> 00:03:52.400 align:start position:0%
here and one here one thing that we may
 

00:03:52.400 --> 00:03:54.750 align:start position:0%
here and one here one thing that we may
want<00:03:52.560><c> to</c><00:03:53.040><c> set</c><00:03:53.319><c> up</c><00:03:54.000><c> and</c><00:03:54.159><c> again</c><00:03:54.319><c> you</c><00:03:54.400><c> will</c><00:03:54.560><c> see</c>

00:03:54.750 --> 00:03:54.760 align:start position:0%
want to set up and again you will see
 

00:03:54.760 --> 00:03:58.670 align:start position:0%
want to set up and again you will see
this<00:03:54.959><c> in</c><00:03:55.519><c> in</c><00:03:55.640><c> the</c><00:03:55.840><c> rmy</c><00:03:56.840><c> is</c><00:03:57.159><c> we</c><00:03:57.280><c> want</c><00:03:57.480><c> to</c><00:03:58.360><c> um</c><00:03:58.560><c> we</c>

00:03:58.670 --> 00:03:58.680 align:start position:0%
this in in the rmy is we want to um we
 

00:03:58.680 --> 00:04:00.589 align:start position:0%
this in in the rmy is we want to um we
will<00:03:58.840><c> need</c><00:03:59.040><c> to</c><00:03:59.319><c> uh</c><00:03:59.439><c> to</c><00:03:59.560><c> have</c><00:04:00.040><c> access</c><00:04:00.280><c> to</c><00:04:00.439><c> this</c>

00:04:00.589 --> 00:04:00.599 align:start position:0%
will need to uh to have access to this
 

00:04:00.599 --> 00:04:03.470 align:start position:0%
will need to uh to have access to this
open<00:04:00.879><c> AI</c><00:04:01.360><c> API</c><00:04:01.799><c> key</c><00:04:02.280><c> variable</c><00:04:03.000><c> in</c><00:04:03.120><c> our</c>

00:04:03.470 --> 00:04:03.480 align:start position:0%
open AI API key variable in our
 

00:04:03.480 --> 00:04:06.030 align:start position:0%
open AI API key variable in our
environment<00:04:04.079><c> that</c><00:04:04.200><c> runs</c><00:04:04.560><c> valuation</c><00:04:05.079><c> job</c><00:04:05.879><c> uh</c>

00:04:06.030 --> 00:04:06.040 align:start position:0%
environment that runs valuation job uh
 

00:04:06.040 --> 00:04:08.750 align:start position:0%
environment that runs valuation job uh
because<00:04:06.239><c> it</c><00:04:06.360><c> will</c><00:04:06.560><c> need</c><00:04:06.760><c> it</c><00:04:07.040><c> to</c><00:04:07.480><c> to</c><00:04:08.079><c> um</c><00:04:08.360><c> call</c>

00:04:08.750 --> 00:04:08.760 align:start position:0%
because it will need it to to um call
 

00:04:08.760 --> 00:04:11.910 align:start position:0%
because it will need it to to um call
gp4<00:04:09.439><c> Api</c><00:04:10.360><c> so</c><00:04:10.560><c> let's</c><00:04:10.799><c> pass</c><00:04:11.120><c> this</c><00:04:11.360><c> environment</c>

00:04:11.910 --> 00:04:11.920 align:start position:0%
gp4 Api so let's pass this environment
 

00:04:11.920 --> 00:04:13.710 align:start position:0%
gp4 Api so let's pass this environment
variable<00:04:12.439><c> here</c><00:04:12.640><c> in</c><00:04:12.799><c> this</c><00:04:13.040><c> in</c><00:04:13.120><c> this</c><00:04:13.280><c> setup</c><00:04:13.599><c> make</c>

00:04:13.710 --> 00:04:13.720 align:start position:0%
variable here in this in this setup make
 

00:04:13.720 --> 00:04:16.229 align:start position:0%
variable here in this in this setup make
sure<00:04:13.920><c> that</c><00:04:14.120><c> it</c><00:04:14.280><c> actually</c><00:04:14.640><c> W</c><00:04:15.000><c> bues</c><00:04:15.239><c> picks</c><00:04:15.480><c> it</c><00:04:15.599><c> up</c>

00:04:16.229 --> 00:04:16.239 align:start position:0%
sure that it actually W bues picks it up
 

00:04:16.239 --> 00:04:18.629 align:start position:0%
sure that it actually W bues picks it up
from<00:04:16.479><c> the</c><00:04:16.639><c> local</c><00:04:17.320><c> uh</c><00:04:17.440><c> from</c><00:04:17.600><c> the</c><00:04:17.720><c> local</c>

00:04:18.629 --> 00:04:18.639 align:start position:0%
from the local uh from the local
 

00:04:18.639 --> 00:04:21.189 align:start position:0%
from the local uh from the local
environment<00:04:19.639><c> so</c><00:04:20.359><c> just</c><00:04:20.560><c> going</c>

00:04:21.189 --> 00:04:21.199 align:start position:0%
environment so just going
 

00:04:21.199 --> 00:04:28.350 align:start position:0%
environment so just going
to<00:04:22.199><c> put</c><00:04:22.440><c> this</c>

00:04:28.350 --> 00:04:28.360 align:start position:0%
 
 

00:04:28.360 --> 00:04:31.070 align:start position:0%
 
here<00:04:29.360><c> and</c>

00:04:31.070 --> 00:04:31.080 align:start position:0%
here and
 

00:04:31.080 --> 00:04:34.469 align:start position:0%
here and
let's<00:04:31.360><c> create</c><00:04:31.639><c> the</c>

00:04:34.469 --> 00:04:34.479 align:start position:0%
 
 

00:04:34.479 --> 00:04:37.350 align:start position:0%
 
Q<00:04:35.479><c> okay</c><00:04:35.759><c> so</c><00:04:35.960><c> now</c><00:04:36.120><c> we</c><00:04:36.240><c> have</c><00:04:36.360><c> a</c><00:04:36.520><c> q</c><00:04:36.800><c> but</c><00:04:36.960><c> this</c><00:04:37.080><c> Q</c><00:04:37.280><c> is</c>

00:04:37.350 --> 00:04:37.360 align:start position:0%
Q okay so now we have a q but this Q is
 

00:04:37.360 --> 00:04:39.230 align:start position:0%
Q okay so now we have a q but this Q is
not<00:04:37.520><c> running</c><00:04:38.000><c> because</c><00:04:38.199><c> it</c><00:04:38.320><c> doesn't</c><00:04:38.639><c> have</c><00:04:38.840><c> any</c>

00:04:39.230 --> 00:04:39.240 align:start position:0%
not running because it doesn't have any
 

00:04:39.240 --> 00:04:41.710 align:start position:0%
not running because it doesn't have any
active<00:04:39.680><c> agents</c><00:04:40.560><c> so</c><00:04:40.759><c> there</c><00:04:40.880><c> is</c><00:04:41.000><c> no</c><00:04:41.240><c> compute</c>

00:04:41.710 --> 00:04:41.720 align:start position:0%
active agents so there is no compute
 

00:04:41.720 --> 00:04:43.510 align:start position:0%
active agents so there is no compute
that<00:04:41.840><c> is</c><00:04:41.960><c> pulling</c><00:04:42.360><c> jobs</c><00:04:42.639><c> from</c><00:04:42.800><c> this</c><00:04:42.960><c> q</c><00:04:43.160><c> and</c>

00:04:43.510 --> 00:04:43.520 align:start position:0%
that is pulling jobs from this q and
 

00:04:43.520 --> 00:04:46.350 align:start position:0%
that is pulling jobs from this q and
listening<00:04:44.520><c> so</c><00:04:45.320><c> um</c><00:04:45.600><c> I</c><00:04:45.680><c> think</c><00:04:45.840><c> it's</c><00:04:45.960><c> a</c><00:04:46.080><c> good</c><00:04:46.240><c> time</c>

00:04:46.350 --> 00:04:46.360 align:start position:0%
listening so um I think it's a good time
 

00:04:46.360 --> 00:04:49.550 align:start position:0%
listening so um I think it's a good time
to<00:04:46.520><c> add</c><00:04:46.680><c> an</c><00:04:47.120><c> agent</c><00:04:48.120><c> and</c><00:04:48.400><c> we</c><00:04:48.600><c> get</c><00:04:48.919><c> this</c><00:04:49.120><c> command</c>

00:04:49.550 --> 00:04:49.560 align:start position:0%
to add an agent and we get this command
 

00:04:49.560 --> 00:04:53.150 align:start position:0%
to add an agent and we get this command
here<00:04:49.960><c> 1B</c><00:04:50.720><c> launch</c><00:04:51.160><c> agent</c><00:04:52.080><c> this</c><00:04:52.320><c> entity</c><00:04:52.960><c> with</c>

00:04:53.150 --> 00:04:53.160 align:start position:0%
here 1B launch agent this entity with
 

00:04:53.160 --> 00:04:56.110 align:start position:0%
here 1B launch agent this entity with
this<00:04:53.360><c> Q</c><00:04:53.720><c> so</c><00:04:53.880><c> let's</c><00:04:54.120><c> copy</c><00:04:54.560><c> this</c><00:04:55.560><c> let's</c><00:04:55.759><c> go</c><00:04:55.960><c> back</c>

00:04:56.110 --> 00:04:56.120 align:start position:0%
this Q so let's copy this let's go back
 

00:04:56.120 --> 00:05:00.870 align:start position:0%
this Q so let's copy this let's go back
to<00:04:56.400><c> our</c><00:04:57.400><c> uh</c><00:04:57.520><c> to</c><00:04:57.680><c> our</c><00:04:57.919><c> terminal</c><00:04:58.919><c> and</c><00:04:59.440><c> um</c>

00:05:00.870 --> 00:05:00.880 align:start position:0%
to our uh to our terminal and um
 

00:05:00.880 --> 00:05:02.749 align:start position:0%
to our uh to our terminal and um
I<00:05:00.960><c> think</c><00:05:01.120><c> we</c><00:05:01.240><c> can</c><00:05:01.400><c> just</c><00:05:01.600><c> put</c><00:05:01.759><c> this</c><00:05:02.039><c> here</c><00:05:02.479><c> so</c>

00:05:02.749 --> 00:05:02.759 align:start position:0%
I think we can just put this here so
 

00:05:02.759 --> 00:05:04.230 align:start position:0%
I think we can just put this here so
let's<00:05:03.160><c> start</c><00:05:03.440><c> an</c>

00:05:04.230 --> 00:05:04.240 align:start position:0%
let's start an
 

00:05:04.240 --> 00:05:07.950 align:start position:0%
let's start an
agent<00:05:05.240><c> uh</c><00:05:05.520><c> in</c><00:05:05.960><c> in</c><00:05:06.120><c> this</c>

00:05:07.950 --> 00:05:07.960 align:start position:0%
agent uh in in this
 

00:05:07.960 --> 00:05:10.029 align:start position:0%
agent uh in in this
environment<00:05:08.960><c> and</c><00:05:09.120><c> you</c><00:05:09.240><c> can</c><00:05:09.360><c> see</c><00:05:09.600><c> now</c><00:05:09.840><c> this</c>

00:05:10.029 --> 00:05:10.039 align:start position:0%
environment and you can see now this
 

00:05:10.039 --> 00:05:13.710 align:start position:0%
environment and you can see now this
agent<00:05:10.320><c> is</c><00:05:10.840><c> running</c><00:05:11.840><c> and</c><00:05:12.280><c> um</c><00:05:12.639><c> the</c><00:05:12.880><c> agent</c><00:05:13.360><c> is</c>

00:05:13.710 --> 00:05:13.720 align:start position:0%
agent is running and um the agent is
 

00:05:13.720 --> 00:05:16.790 align:start position:0%
agent is running and um the agent is
pulling<00:05:14.639><c> uh</c><00:05:14.759><c> on</c><00:05:15.000><c> the</c><00:05:15.360><c> evaluate</c><00:05:15.919><c> queue</c><00:05:16.560><c> there's</c>

00:05:16.790 --> 00:05:16.800 align:start position:0%
pulling uh on the evaluate queue there's
 

00:05:16.800 --> 00:05:18.590 align:start position:0%
pulling uh on the evaluate queue there's
currently<00:05:17.160><c> no</c><00:05:17.360><c> job</c><00:05:17.520><c> running</c><00:05:17.919><c> because</c><00:05:18.160><c> no</c><00:05:18.360><c> job</c>

00:05:18.590 --> 00:05:18.600 align:start position:0%
currently no job running because no job
 

00:05:18.600 --> 00:05:22.550 align:start position:0%
currently no job running because no job
has<00:05:18.720><c> been</c><00:05:18.880><c> sent</c><00:05:19.400><c> to</c><00:05:19.600><c> this</c><00:05:19.960><c> queue</c><00:05:20.960><c> so</c><00:05:21.919><c> um</c><00:05:22.319><c> before</c>

00:05:22.550 --> 00:05:22.560 align:start position:0%
has been sent to this queue so um before
 

00:05:22.560 --> 00:05:24.469 align:start position:0%
has been sent to this queue so um before
we<00:05:22.680><c> set</c><00:05:22.840><c> up</c><00:05:23.000><c> an</c><00:05:23.120><c> automation</c><00:05:23.800><c> I</c><00:05:23.880><c> think</c><00:05:24.080><c> it's</c>

00:05:24.469 --> 00:05:24.479 align:start position:0%
we set up an automation I think it's
 

00:05:24.479 --> 00:05:26.990 align:start position:0%
we set up an automation I think it's
it's<00:05:24.639><c> often</c><00:05:24.840><c> a</c><00:05:24.960><c> good</c><00:05:25.120><c> idea</c><00:05:25.400><c> to</c><00:05:25.600><c> test</c><00:05:26.080><c> our</c><00:05:26.360><c> job</c>

00:05:26.990 --> 00:05:27.000 align:start position:0%
it's often a good idea to test our job
 

00:05:27.000 --> 00:05:30.749 align:start position:0%
it's often a good idea to test our job
so<00:05:27.199><c> let's</c><00:05:27.440><c> go</c><00:05:27.680><c> back</c><00:05:27.880><c> to</c><00:05:28.520><c> to</c><00:05:28.680><c> our</c><00:05:28.960><c> jobs</c>

00:05:30.749 --> 00:05:30.759 align:start position:0%
so let's go back to to our jobs
 

00:05:30.759 --> 00:05:34.950 align:start position:0%
so let's go back to to our jobs
and<00:05:31.840><c> um</c><00:05:32.840><c> let's</c><00:05:33.120><c> go</c><00:05:33.319><c> back</c><00:05:33.560><c> to</c><00:05:34.240><c> I</c><00:05:34.360><c> think</c><00:05:34.600><c> I</c><00:05:34.759><c> our</c>

00:05:34.950 --> 00:05:34.960 align:start position:0%
and um let's go back to I think I our
 

00:05:34.960 --> 00:05:36.670 align:start position:0%
and um let's go back to I think I our
tiny<00:05:35.199><c> lab</c><00:05:35.440><c> our</c><00:05:35.600><c> workspace</c><00:05:36.039><c> let's</c><00:05:36.240><c> go</c><00:05:36.440><c> back</c><00:05:36.560><c> to</c>

00:05:36.670 --> 00:05:36.680 align:start position:0%
tiny lab our workspace let's go back to
 

00:05:36.680 --> 00:05:39.590 align:start position:0%
tiny lab our workspace let's go back to
the<00:05:36.880><c> jobs</c><00:05:37.800><c> we</c><00:05:38.000><c> have</c><00:05:38.240><c> the</c><00:05:38.520><c> eval</c><00:05:38.919><c> job</c><00:05:39.199><c> and</c><00:05:39.319><c> we</c><00:05:39.400><c> can</c>

00:05:39.590 --> 00:05:39.600 align:start position:0%
the jobs we have the eval job and we can
 

00:05:39.600 --> 00:05:43.350 align:start position:0%
the jobs we have the eval job and we can
Now<00:05:40.199><c> launch</c><00:05:40.639><c> this</c>

00:05:43.350 --> 00:05:43.360 align:start position:0%
 
 

00:05:43.360 --> 00:05:47.590 align:start position:0%
 
job<00:05:44.360><c> and</c><00:05:44.560><c> we</c><00:05:44.680><c> will</c><00:05:44.919><c> select</c><00:05:45.800><c> um</c><00:05:46.440><c> the</c><00:05:47.039><c> evaluate</c>

00:05:47.590 --> 00:05:47.600 align:start position:0%
job and we will select um the evaluate
 

00:05:47.600 --> 00:05:49.469 align:start position:0%
job and we will select um the evaluate
queue<00:05:48.120><c> that</c><00:05:48.240><c> is</c><00:05:48.360><c> a</c><00:05:48.520><c> Docker</c><00:05:48.840><c> queue</c><00:05:49.160><c> that</c><00:05:49.280><c> we</c>

00:05:49.469 --> 00:05:49.479 align:start position:0%
queue that is a Docker queue that we
 

00:05:49.479 --> 00:05:52.070 align:start position:0%
queue that is a Docker queue that we
just<00:05:49.919><c> U</c><00:05:50.120><c> created</c><00:05:51.000><c> you</c><00:05:51.120><c> can</c><00:05:51.240><c> see</c><00:05:51.520><c> we</c><00:05:51.639><c> could</c><00:05:51.840><c> also</c>

00:05:52.070 --> 00:05:52.080 align:start position:0%
just U created you can see we could also
 

00:05:52.080 --> 00:05:54.830 align:start position:0%
just U created you can see we could also
use<00:05:52.280><c> the</c><00:05:52.440><c> kubernetes</c><00:05:53.319><c> uh</c><00:05:53.560><c> cluster</c><00:05:54.520><c> that</c><00:05:54.680><c> we</c>

00:05:54.830 --> 00:05:54.840 align:start position:0%
use the kubernetes uh cluster that we
 

00:05:54.840 --> 00:05:57.390 align:start position:0%
use the kubernetes uh cluster that we
have<00:05:55.160><c> available</c><00:05:55.639><c> in</c><00:05:55.759><c> our</c><00:05:56.199><c> organization</c><00:05:57.199><c> but</c>

00:05:57.390 --> 00:05:57.400 align:start position:0%
have available in our organization but
 

00:05:57.400 --> 00:06:00.029 align:start position:0%
have available in our organization but
again<00:05:57.759><c> let's</c><00:05:58.000><c> keep</c><00:05:58.199><c> things</c><00:05:58.400><c> simple</c><00:05:58.840><c> first</c><00:05:59.880><c> we</c>

00:06:00.029 --> 00:06:00.039 align:start position:0%
again let's keep things simple first we
 

00:06:00.039 --> 00:06:01.430 align:start position:0%
again let's keep things simple first we
don't<00:06:00.199><c> need</c><00:06:00.360><c> kubernetes</c><00:06:00.880><c> to</c><00:06:01.000><c> run</c><00:06:01.240><c> this</c>

00:06:01.430 --> 00:06:01.440 align:start position:0%
don't need kubernetes to run this
 

00:06:01.440 --> 00:06:03.670 align:start position:0%
don't need kubernetes to run this
evaluation<00:06:01.960><c> script</c><00:06:02.880><c> uh</c><00:06:02.960><c> we</c><00:06:03.080><c> can</c><00:06:03.240><c> adjust</c><00:06:03.560><c> the</c>

00:06:03.670 --> 00:06:03.680 align:start position:0%
evaluation script uh we can adjust the
 

00:06:03.680 --> 00:06:04.749 align:start position:0%
evaluation script uh we can adjust the
job

00:06:04.749 --> 00:06:04.759 align:start position:0%
job
 

00:06:04.759 --> 00:06:06.990 align:start position:0%
job
priority<00:06:05.759><c> uh</c><00:06:05.880><c> we</c><00:06:05.960><c> can</c><00:06:06.160><c> change</c><00:06:06.520><c> the</c><00:06:06.680><c> the</c><00:06:06.800><c> Q</c>

00:06:06.990 --> 00:06:07.000 align:start position:0%
priority uh we can change the the Q
 

00:06:07.000 --> 00:06:08.589 align:start position:0%
priority uh we can change the the Q
configurations<00:06:07.680><c> here</c><00:06:07.919><c> we</c><00:06:08.039><c> can</c><00:06:08.160><c> change</c><00:06:08.440><c> the</c>

00:06:08.589 --> 00:06:08.599 align:start position:0%
configurations here we can change the
 

00:06:08.599 --> 00:06:10.629 align:start position:0%
configurations here we can change the
destination<00:06:09.160><c> project</c><00:06:09.960><c> I</c><00:06:10.080><c> think</c><00:06:10.240><c> all</c><00:06:10.360><c> of</c><00:06:10.479><c> the</c>

00:06:10.629 --> 00:06:10.639 align:start position:0%
destination project I think all of the
 

00:06:10.639 --> 00:06:14.309 align:start position:0%
destination project I think all of the
settings<00:06:11.240><c> are</c><00:06:11.680><c> fine</c><00:06:12.319><c> so</c><00:06:13.000><c> let's</c><00:06:13.800><c> launch</c><00:06:14.160><c> this</c>

00:06:14.309 --> 00:06:14.319 align:start position:0%
settings are fine so let's launch this
 

00:06:14.319 --> 00:06:17.870 align:start position:0%
settings are fine so let's launch this
job<00:06:14.639><c> now</c><00:06:15.639><c> let's</c><00:06:15.840><c> check</c><00:06:16.120><c> our</c><00:06:16.840><c> our</c><00:06:17.080><c> terminal</c><00:06:17.759><c> and</c>

00:06:17.870 --> 00:06:17.880 align:start position:0%
job now let's check our our terminal and
 

00:06:17.880 --> 00:06:21.230 align:start position:0%
job now let's check our our terminal and
you<00:06:18.000><c> can</c><00:06:18.160><c> see</c><00:06:18.440><c> now</c><00:06:19.000><c> the</c><00:06:19.160><c> agent</c><00:06:19.759><c> has</c><00:06:20.759><c> uh</c><00:06:20.919><c> picked</c>

00:06:21.230 --> 00:06:21.240 align:start position:0%
you can see now the agent has uh picked
 

00:06:21.240 --> 00:06:23.629 align:start position:0%
you can see now the agent has uh picked
up<00:06:21.479><c> the</c><00:06:21.639><c> job</c><00:06:21.880><c> from</c><00:06:22.039><c> the</c><00:06:22.199><c> queue</c><00:06:22.840><c> and</c><00:06:23.000><c> it's</c><00:06:23.400><c> uh</c>

00:06:23.629 --> 00:06:23.639 align:start position:0%
up the job from the queue and it's uh
 

00:06:23.639 --> 00:06:28.150 align:start position:0%
up the job from the queue and it's uh
running<00:06:24.120><c> this</c><00:06:24.639><c> the</c><00:06:24.800><c> the</c><00:06:24.919><c> evaluation</c>

00:06:28.150 --> 00:06:28.160 align:start position:0%
 
 

00:06:28.160 --> 00:06:31.990 align:start position:0%
 
script<00:06:29.160><c> and</c><00:06:29.400><c> if</c><00:06:29.759><c> does</c><00:06:30.039><c> not</c><00:06:30.400><c> have</c><00:06:31.039><c> a</c><00:06:31.360><c> um</c>

00:06:31.990 --> 00:06:32.000 align:start position:0%
script and if does not have a um
 

00:06:32.000 --> 00:06:34.950 align:start position:0%
script and if does not have a um
artifact<00:06:32.560><c> with</c><00:06:32.680><c> the</c><00:06:32.800><c> name</c><00:06:33.599><c> candidate</c><00:06:34.599><c> okay</c>

00:06:34.950 --> 00:06:34.960 align:start position:0%
artifact with the name candidate okay
 

00:06:34.960 --> 00:06:36.870 align:start position:0%
artifact with the name candidate okay
that<00:06:35.160><c> reminds</c><00:06:35.599><c> me</c><00:06:35.880><c> that</c><00:06:36.160><c> actually</c><00:06:36.479><c> before</c><00:06:36.720><c> we</c>

00:06:36.870 --> 00:06:36.880 align:start position:0%
that reminds me that actually before we
 

00:06:36.880 --> 00:06:40.070 align:start position:0%
that reminds me that actually before we
run<00:06:37.120><c> the</c><00:06:37.240><c> evaluation</c><00:06:37.759><c> script</c><00:06:38.599><c> we</c><00:06:38.759><c> need</c><00:06:38.960><c> to</c><00:06:39.639><c> um</c>

00:06:40.070 --> 00:06:40.080 align:start position:0%
run the evaluation script we need to um
 

00:06:40.080 --> 00:06:41.110 align:start position:0%
run the evaluation script we need to um
we<00:06:40.199><c> need</c>

00:06:41.110 --> 00:06:41.120 align:start position:0%
we need
 

00:06:41.120 --> 00:06:44.550 align:start position:0%
we need
to<00:06:42.120><c> uh</c><00:06:42.919><c> we</c><00:06:43.039><c> need</c><00:06:43.199><c> to</c><00:06:43.319><c> move</c><00:06:43.560><c> the</c><00:06:43.720><c> model</c><00:06:44.160><c> our</c><00:06:44.360><c> our</c>

00:06:44.550 --> 00:06:44.560 align:start position:0%
to uh we need to move the model our our
 

00:06:44.560 --> 00:06:46.790 align:start position:0%
to uh we need to move the model our our
model<00:06:44.919><c> that</c><00:06:45.039><c> we</c><00:06:45.160><c> just</c><00:06:45.319><c> trained</c><00:06:46.080><c> to</c><00:06:46.240><c> the</c><00:06:46.360><c> model</c>

00:06:46.790 --> 00:06:46.800 align:start position:0%
model that we just trained to the model
 

00:06:46.800 --> 00:06:49.550 align:start position:0%
model that we just trained to the model
registry<00:06:47.800><c> and</c><00:06:48.039><c> give</c><00:06:48.199><c> it</c><00:06:48.400><c> this</c><00:06:48.960><c> the</c><00:06:49.120><c> candidate</c>

00:06:49.550 --> 00:06:49.560 align:start position:0%
registry and give it this the candidate
 

00:06:49.560 --> 00:06:52.070 align:start position:0%
registry and give it this the candidate
tag<00:06:50.039><c> because</c><00:06:50.319><c> that's</c><00:06:50.520><c> what</c><00:06:50.800><c> our</c><00:06:51.800><c> what</c><00:06:51.919><c> our</c>

00:06:52.070 --> 00:06:52.080 align:start position:0%
tag because that's what our what our
 

00:06:52.080 --> 00:06:55.390 align:start position:0%
tag because that's what our what our
laun<00:06:52.400><c> job</c><00:06:52.960><c> uh</c><00:06:53.280><c> uh</c><00:06:53.479><c> expects</c><00:06:54.039><c> to</c><00:06:54.199><c> see</c><00:06:55.000><c> so</c><00:06:55.160><c> let's</c>

00:06:55.390 --> 00:06:55.400 align:start position:0%
laun job uh uh expects to see so let's
 

00:06:55.400 --> 00:06:59.029 align:start position:0%
laun job uh uh expects to see so let's
go<00:06:55.599><c> back</c><00:06:55.840><c> to</c><00:06:56.840><c> our</c><00:06:57.599><c> project</c><00:06:58.160><c> let</c><00:06:58.280><c> me</c><00:06:58.479><c> go</c><00:06:58.680><c> back</c><00:06:58.840><c> to</c>

00:06:59.029 --> 00:06:59.039 align:start position:0%
go back to our project let me go back to
 

00:06:59.039 --> 00:07:00.749 align:start position:0%
go back to our project let me go back to
the<00:06:59.280><c> project</c><00:06:59.560><c> project</c><00:06:59.879><c> in</c><00:07:00.039><c> weights</c><00:07:00.280><c> and</c>

00:07:00.749 --> 00:07:00.759 align:start position:0%
the project project in weights and
 

00:07:00.759 --> 00:07:04.189 align:start position:0%
the project project in weights and
biases<00:07:01.759><c> I</c><00:07:01.919><c> can</c><00:07:02.599><c> um</c><00:07:03.000><c> I</c><00:07:03.120><c> can</c><00:07:03.319><c> check</c><00:07:03.720><c> the</c><00:07:03.919><c> most</c>

00:07:04.189 --> 00:07:04.199 align:start position:0%
biases I can um I can check the most
 

00:07:04.199 --> 00:07:08.189 align:start position:0%
biases I can um I can check the most
recent<00:07:04.919><c> uh</c><00:07:05.440><c> runs</c><00:07:06.440><c> and</c><00:07:07.240><c> specifically</c><00:07:07.879><c> I'm</c>

00:07:08.189 --> 00:07:08.199 align:start position:0%
recent uh runs and specifically I'm
 

00:07:08.199 --> 00:07:10.550 align:start position:0%
recent uh runs and specifically I'm
interested<00:07:08.960><c> in</c><00:07:09.560><c> yeah</c><00:07:09.720><c> this</c><00:07:09.840><c> you</c><00:07:09.960><c> can</c><00:07:10.080><c> see</c><00:07:10.319><c> the</c>

00:07:10.550 --> 00:07:10.560 align:start position:0%
interested in yeah this you can see the
 

00:07:10.560 --> 00:07:12.270 align:start position:0%
interested in yeah this you can see the
the<00:07:10.680><c> evaluation</c><00:07:11.160><c> job</c><00:07:11.479><c> that</c><00:07:11.639><c> just</c><00:07:11.800><c> failed</c>

00:07:12.270 --> 00:07:12.280 align:start position:0%
the evaluation job that just failed
 

00:07:12.280 --> 00:07:13.670 align:start position:0%
the evaluation job that just failed
because<00:07:12.479><c> it</c><00:07:12.639><c> didn't</c><00:07:12.960><c> have</c><00:07:13.199><c> access</c><00:07:13.400><c> to</c><00:07:13.560><c> the</c>

00:07:13.670 --> 00:07:13.680 align:start position:0%
because it didn't have access to the
 

00:07:13.680 --> 00:07:16.670 align:start position:0%
because it didn't have access to the
candidate<00:07:14.280><c> model</c><00:07:15.280><c> um</c><00:07:15.759><c> I</c><00:07:15.840><c> may</c><00:07:16.039><c> want</c><00:07:16.160><c> to</c><00:07:16.400><c> just</c>

00:07:16.670 --> 00:07:16.680 align:start position:0%
candidate model um I may want to just
 

00:07:16.680 --> 00:07:19.550 align:start position:0%
candidate model um I may want to just
focus<00:07:17.039><c> on</c><00:07:17.240><c> the</c><00:07:17.440><c> training</c><00:07:18.039><c> runs</c><00:07:19.039><c> so</c><00:07:19.240><c> let</c><00:07:19.360><c> me</c>

00:07:19.550 --> 00:07:19.560 align:start position:0%
focus on the training runs so let me
 

00:07:19.560 --> 00:07:25.629 align:start position:0%
focus on the training runs so let me
filter<00:07:20.400><c> add</c><00:07:20.560><c> a</c><00:07:20.879><c> filter</c><00:07:21.879><c> and</c><00:07:22.800><c> job</c>

00:07:25.629 --> 00:07:25.639 align:start position:0%
 
 

00:07:25.639 --> 00:07:28.350 align:start position:0%
 
type<00:07:26.639><c> the</c><00:07:26.879><c> job</c><00:07:27.199><c> type</c><00:07:27.440><c> should</c><00:07:27.639><c> be</c>

00:07:28.350 --> 00:07:28.360 align:start position:0%
type the job type should be
 

00:07:28.360 --> 00:07:32.390 align:start position:0%
type the job type should be
train<00:07:29.360><c> and</c><00:07:29.680><c> and</c><00:07:29.879><c> you</c><00:07:30.000><c> can</c><00:07:30.199><c> see</c><00:07:30.560><c> 1</c><00:07:30.759><c> hour</c><00:07:31.039><c> ago</c><00:07:31.919><c> um</c>

00:07:32.390 --> 00:07:32.400 align:start position:0%
train and and you can see 1 hour ago um
 

00:07:32.400 --> 00:07:34.550 align:start position:0%
train and and you can see 1 hour ago um
experiment<00:07:32.919><c> that</c><00:07:33.120><c> finished</c><00:07:33.800><c> this</c><00:07:33.919><c> is</c><00:07:34.199><c> the</c><00:07:34.400><c> the</c>

00:07:34.550 --> 00:07:34.560 align:start position:0%
experiment that finished this is the the
 

00:07:34.560 --> 00:07:37.150 align:start position:0%
experiment that finished this is the the
job<00:07:34.800><c> that</c><00:07:34.919><c> we</c><00:07:35.080><c> triggered</c><00:07:35.879><c> together</c><00:07:36.879><c> you</c><00:07:37.000><c> can</c>

00:07:37.150 --> 00:07:37.160 align:start position:0%
job that we triggered together you can
 

00:07:37.160 --> 00:07:42.029 align:start position:0%
job that we triggered together you can
see<00:07:38.039><c> um</c><00:07:39.039><c> uh</c><00:07:39.360><c> the</c><00:07:39.560><c> training</c><00:07:40.280><c> uh</c><00:07:40.440><c> metrics</c><00:07:41.080><c> here</c>

00:07:42.029 --> 00:07:42.039 align:start position:0%
see um uh the training uh metrics here
 

00:07:42.039 --> 00:07:45.589 align:start position:0%
see um uh the training uh metrics here
let's<00:07:42.240><c> see</c><00:07:42.520><c> let's</c><00:07:42.680><c> look</c><00:07:42.879><c> at</c><00:07:43.000><c> the</c><00:07:43.120><c> training</c>

00:07:45.589 --> 00:07:45.599 align:start position:0%
 
 

00:07:45.599 --> 00:07:48.749 align:start position:0%
 
Closs<00:07:46.599><c> and</c><00:07:46.759><c> the</c><00:07:46.879><c> trading</c><00:07:47.159><c> Clause</c><00:07:47.520><c> is</c><00:07:47.840><c> just</c><00:07:48.440><c> uh</c>

00:07:48.749 --> 00:07:48.759 align:start position:0%
Closs and the trading Clause is just uh
 

00:07:48.759 --> 00:07:51.670 align:start position:0%
Closs and the trading Clause is just uh
is<00:07:48.919><c> just</c><00:07:49.080><c> locked</c><00:07:49.400><c> in</c><00:07:49.479><c> a</c><00:07:49.599><c> single</c><00:07:49.919><c> step</c><00:07:50.400><c> so</c><00:07:51.400><c> um</c>

00:07:51.670 --> 00:07:51.680 align:start position:0%
is just locked in a single step so um
 

00:07:51.680 --> 00:07:53.469 align:start position:0%
is just locked in a single step so um
that's<00:07:51.919><c> because</c><00:07:52.240><c> we</c><00:07:52.440><c> didn't</c><00:07:52.720><c> run</c><00:07:52.960><c> this</c><00:07:53.080><c> model</c>

00:07:53.469 --> 00:07:53.479 align:start position:0%
that's because we didn't run this model
 

00:07:53.479 --> 00:07:55.869 align:start position:0%
that's because we didn't run this model
for<00:07:53.759><c> very</c><00:07:54.039><c> for</c><00:07:54.280><c> very</c><00:07:54.479><c> long</c><00:07:55.199><c> but</c><00:07:55.360><c> we</c><00:07:55.479><c> can</c><00:07:55.680><c> also</c>

00:07:55.869 --> 00:07:55.879 align:start position:0%
for very for very long but we can also
 

00:07:55.879 --> 00:07:58.029 align:start position:0%
for very for very long but we can also
see<00:07:56.120><c> like</c><00:07:56.240><c> the</c><00:07:56.440><c> valuation</c><00:07:57.000><c> here</c><00:07:57.440><c> and</c><00:07:57.639><c> if</c><00:07:57.759><c> we</c><00:07:57.879><c> go</c>

00:07:58.029 --> 00:07:58.039 align:start position:0%
see like the valuation here and if we go
 

00:07:58.039 --> 00:08:00.029 align:start position:0%
see like the valuation here and if we go
into<00:07:58.400><c> artifacts</c>

00:08:00.029 --> 00:08:00.039 align:start position:0%
into artifacts
 

00:08:00.039 --> 00:08:03.390 align:start position:0%
into artifacts
uh<00:08:00.280><c> we</c><00:08:00.400><c> should</c><00:08:00.720><c> see</c><00:08:01.400><c> a</c><00:08:01.560><c> model</c>

00:08:03.390 --> 00:08:03.400 align:start position:0%
uh we should see a model
 

00:08:03.400 --> 00:08:06.950 align:start position:0%
uh we should see a model
artifact<00:08:04.400><c> and</c><00:08:05.159><c> um</c><00:08:05.759><c> I</c><00:08:05.879><c> think</c><00:08:06.120><c> it's</c><00:08:06.360><c> time</c><00:08:06.520><c> to</c><00:08:06.720><c> add</c>

00:08:06.950 --> 00:08:06.960 align:start position:0%
artifact and um I think it's time to add
 

00:08:06.960 --> 00:08:09.390 align:start position:0%
artifact and um I think it's time to add
this<00:08:07.120><c> model</c><00:08:07.720><c> to</c><00:08:08.159><c> our</c>

00:08:09.390 --> 00:08:09.400 align:start position:0%
this model to our
 

00:08:09.400 --> 00:08:12.189 align:start position:0%
this model to our
registry<00:08:10.400><c> so</c><00:08:10.840><c> let's</c><00:08:11.199><c> add</c><00:08:11.400><c> it</c><00:08:11.520><c> to</c><00:08:11.639><c> our</c><00:08:11.840><c> small</c>

00:08:12.189 --> 00:08:12.199 align:start position:0%
registry so let's add it to our small
 

00:08:12.199 --> 00:08:14.110 align:start position:0%
registry so let's add it to our small
instruct

00:08:14.110 --> 00:08:14.120 align:start position:0%
instruct
 

00:08:14.120 --> 00:08:17.029 align:start position:0%
instruct
llm<00:08:15.120><c> and</c><00:08:15.720><c> uh</c><00:08:16.120><c> because</c><00:08:16.400><c> this</c><00:08:16.520><c> is</c><00:08:16.680><c> like</c><00:08:16.879><c> the</c>

00:08:17.029 --> 00:08:17.039 align:start position:0%
llm and uh because this is like the
 

00:08:17.039 --> 00:08:20.670 align:start position:0%
llm and uh because this is like the
small<00:08:17.319><c> $1</c><00:08:17.599><c> billion</c><00:08:18.440><c> one</c><00:08:18.879><c> 1</c><00:08:19.080><c> billion</c><00:08:19.680><c> parameter</c>

00:08:20.670 --> 00:08:20.680 align:start position:0%
small $1 billion one 1 billion parameter
 

00:08:20.680 --> 00:08:23.670 align:start position:0%
small $1 billion one 1 billion parameter
uh<00:08:20.919><c> model</c><00:08:21.360><c> that</c><00:08:21.520><c> we</c><00:08:21.639><c> are</c><00:08:21.960><c> uh</c><00:08:22.080><c> fine</c><00:08:22.319><c> tuning</c><00:08:22.639><c> on</c>

00:08:23.670 --> 00:08:23.680 align:start position:0%
uh model that we are uh fine tuning on
 

00:08:23.680 --> 00:08:27.550 align:start position:0%
uh model that we are uh fine tuning on
instructions<00:08:24.680><c> and</c><00:08:25.199><c> let's</c><00:08:25.520><c> give</c><00:08:25.680><c> it</c><00:08:26.080><c> the</c>

00:08:27.550 --> 00:08:27.560 align:start position:0%
instructions and let's give it the
 

00:08:27.560 --> 00:08:31.390 align:start position:0%
instructions and let's give it the
candidate<00:08:28.560><c> alas</c>

00:08:31.390 --> 00:08:31.400 align:start position:0%
candidate alas
 

00:08:31.400 --> 00:08:35.709 align:start position:0%
candidate alas
and<00:08:31.919><c> let's</c><00:08:32.279><c> link</c><00:08:32.560><c> it</c><00:08:32.719><c> to</c><00:08:33.120><c> to</c><00:08:33.279><c> the</c>

00:08:35.709 --> 00:08:35.719 align:start position:0%
 
 

00:08:35.719 --> 00:08:38.670 align:start position:0%
 
registry<00:08:36.919><c> okay</c><00:08:37.919><c> can</c><00:08:38.120><c> take</c><00:08:38.240><c> a</c><00:08:38.360><c> look</c><00:08:38.519><c> at</c><00:08:38.599><c> the</c>

00:08:38.670 --> 00:08:38.680 align:start position:0%
registry okay can take a look at the
 

00:08:38.680 --> 00:08:43.430 align:start position:0%
registry okay can take a look at the
model<00:08:38.959><c> in</c><00:08:39.080><c> the</c>

00:08:43.430 --> 00:08:43.440 align:start position:0%
 
 

00:08:43.440 --> 00:08:45.949 align:start position:0%
 
registry<00:08:44.440><c> and</c><00:08:44.600><c> we</c><00:08:44.720><c> can</c><00:08:44.880><c> see</c><00:08:45.480><c> the</c><00:08:45.600><c> model</c><00:08:45.839><c> that</c>

00:08:45.949 --> 00:08:45.959 align:start position:0%
registry and we can see the model that
 

00:08:45.959 --> 00:08:47.910 align:start position:0%
registry and we can see the model that
we<00:08:46.120><c> added</c><00:08:46.440><c> with</c><00:08:46.560><c> the</c><00:08:46.720><c> candidate</c><00:08:47.120><c> alas</c><00:08:47.519><c> we</c><00:08:47.600><c> can</c>

00:08:47.910 --> 00:08:47.920 align:start position:0%
we added with the candidate alas we can
 

00:08:47.920 --> 00:08:50.430 align:start position:0%
we added with the candidate alas we can
also<00:08:48.240><c> see</c><00:08:48.560><c> is</c><00:08:48.800><c> there</c><00:08:49.480><c> a</c><00:08:49.640><c> baseline</c><00:08:50.200><c> yeah</c><00:08:50.360><c> there</c>

00:08:50.430 --> 00:08:50.440 align:start position:0%
also see is there a baseline yeah there
 

00:08:50.440 --> 00:08:51.990 align:start position:0%
also see is there a baseline yeah there
is<00:08:50.600><c> already</c><00:08:50.880><c> a</c><00:08:51.000><c> model</c><00:08:51.279><c> the</c><00:08:51.399><c> first</c><00:08:51.640><c> model</c><00:08:51.920><c> that</c>

00:08:51.990 --> 00:08:52.000 align:start position:0%
is already a model the first model that
 

00:08:52.000 --> 00:08:54.070 align:start position:0%
is already a model the first model that
we<00:08:52.160><c> trained</c><00:08:52.440><c> in</c><00:08:52.560><c> this</c><00:08:52.680><c> model</c><00:08:52.959><c> regist</c><00:08:53.560><c> added</c><00:08:53.920><c> in</c>

00:08:54.070 --> 00:08:54.080 align:start position:0%
we trained in this model regist added in
 

00:08:54.080 --> 00:08:56.790 align:start position:0%
we trained in this model regist added in
this<00:08:54.200><c> model</c><00:08:54.480><c> registry</c><00:08:55.279><c> has</c><00:08:55.440><c> the</c><00:08:55.600><c> Baseline</c><00:08:56.399><c> um</c>

00:08:56.790 --> 00:08:56.800 align:start position:0%
this model registry has the Baseline um
 

00:08:56.800 --> 00:08:59.949 align:start position:0%
this model registry has the Baseline um
has<00:08:56.959><c> the</c><00:08:57.120><c> Baseline</c><00:08:58.120><c> uh</c><00:08:58.279><c> alas</c><00:08:59.000><c> so</c><00:08:59.600><c> I</c><00:08:59.680><c> think</c><00:08:59.880><c> this</c>

00:08:59.949 --> 00:08:59.959 align:start position:0%
has the Baseline uh alas so I think this
 

00:08:59.959 --> 00:09:02.949 align:start position:0%
has the Baseline uh alas so I think this
is<00:09:00.120><c> a</c><00:09:00.279><c> good</c><00:09:00.560><c> time</c><00:09:00.880><c> now</c><00:09:01.279><c> to</c><00:09:02.279><c> uh</c><00:09:02.360><c> to</c><00:09:02.519><c> launch</c><00:09:02.800><c> this</c>

00:09:02.949 --> 00:09:02.959 align:start position:0%
is a good time now to uh to launch this
 

00:09:02.959 --> 00:09:04.990 align:start position:0%
is a good time now to uh to launch this
automation<00:09:03.600><c> again</c><00:09:03.959><c> and</c><00:09:04.120><c> hopefully</c><00:09:04.519><c> this</c><00:09:04.680><c> time</c>

00:09:04.990 --> 00:09:05.000 align:start position:0%
automation again and hopefully this time
 

00:09:05.000 --> 00:09:08.230 align:start position:0%
automation again and hopefully this time
everything<00:09:05.320><c> will</c><00:09:05.519><c> work</c><00:09:05.880><c> fine</c><00:09:06.880><c> so</c><00:09:07.519><c> uh</c><00:09:07.680><c> let's</c><00:09:07.920><c> go</c>

00:09:08.230 --> 00:09:08.240 align:start position:0%
everything will work fine so uh let's go
 

00:09:08.240 --> 00:09:13.630 align:start position:0%
everything will work fine so uh let's go
back<00:09:08.480><c> to</c><00:09:08.800><c> the</c><00:09:09.079><c> the</c><00:09:09.240><c> project</c><00:09:09.519><c> and</c><00:09:09.680><c> the</c>

00:09:13.630 --> 00:09:13.640 align:start position:0%
 
 

00:09:13.640 --> 00:09:20.150 align:start position:0%
 
jobs<00:09:14.640><c> and</c><00:09:15.399><c> me</c><00:09:15.519><c> try</c><00:09:15.720><c> to</c><00:09:15.839><c> launch</c><00:09:16.240><c> this</c>

00:09:20.150 --> 00:09:20.160 align:start position:0%
 
 

00:09:20.160 --> 00:09:23.269 align:start position:0%
 
again<00:09:21.160><c> you</c><00:09:21.279><c> can</c><00:09:21.480><c> see</c><00:09:22.320><c> by</c><00:09:22.440><c> the</c><00:09:22.560><c> way</c><00:09:22.760><c> now</c><00:09:23.000><c> since</c>

00:09:23.269 --> 00:09:23.279 align:start position:0%
again you can see by the way now since
 

00:09:23.279 --> 00:09:24.630 align:start position:0%
again you can see by the way now since
this<00:09:23.440><c> is</c><00:09:23.600><c> the</c><00:09:23.720><c> second</c><00:09:24.000><c> time</c><00:09:24.120><c> we're</c><00:09:24.279><c> running</c>

00:09:24.630 --> 00:09:24.640 align:start position:0%
this is the second time we're running
 

00:09:24.640 --> 00:09:28.750 align:start position:0%
this is the second time we're running
this<00:09:24.800><c> job</c><00:09:25.800><c> uh</c><00:09:25.959><c> we</c><00:09:26.120><c> can</c><00:09:26.480><c> clone</c><00:09:27.240><c> the</c><00:09:27.440><c> settings</c><00:09:28.320><c> um</c>

00:09:28.750 --> 00:09:28.760 align:start position:0%
this job uh we can clone the settings um
 

00:09:28.760 --> 00:09:30.590 align:start position:0%
this job uh we can clone the settings um
from<00:09:29.399><c> the</c><00:09:29.480><c> most</c><00:09:29.680><c> recent</c><00:09:29.959><c> run</c><00:09:30.200><c> we</c><00:09:30.320><c> had</c><00:09:30.440><c> some</c>

00:09:30.590 --> 00:09:30.600 align:start position:0%
from the most recent run we had some
 

00:09:30.600 --> 00:09:33.030 align:start position:0%
from the most recent run we had some
default<00:09:30.959><c> settings</c><00:09:31.360><c> that</c><00:09:31.560><c> now</c><00:09:32.360><c> uh</c><00:09:32.560><c> visible</c>

00:09:33.030 --> 00:09:33.040 align:start position:0%
default settings that now uh visible
 

00:09:33.040 --> 00:09:35.750 align:start position:0%
default settings that now uh visible
here<00:09:33.600><c> and</c><00:09:33.760><c> we</c><00:09:33.880><c> can</c><00:09:34.279><c> adjust</c><00:09:34.959><c> some</c><00:09:35.120><c> of</c><00:09:35.279><c> the</c>

00:09:35.750 --> 00:09:35.760 align:start position:0%
here and we can adjust some of the
 

00:09:35.760 --> 00:09:39.389 align:start position:0%
here and we can adjust some of the
settings<00:09:36.920><c> um</c><00:09:37.920><c> but</c><00:09:38.320><c> let's</c><00:09:38.640><c> just</c><00:09:38.880><c> keep</c><00:09:39.040><c> it</c><00:09:39.200><c> like</c>

00:09:39.389 --> 00:09:39.399 align:start position:0%
settings um but let's just keep it like
 

00:09:39.399 --> 00:09:41.350 align:start position:0%
settings um but let's just keep it like
this<00:09:39.560><c> for</c><00:09:39.760><c> now</c><00:09:40.440><c> uh</c><00:09:40.560><c> let's</c><00:09:40.760><c> not</c><00:09:40.959><c> make</c><00:09:41.160><c> any</c>

00:09:41.350 --> 00:09:41.360 align:start position:0%
this for now uh let's not make any
 

00:09:41.360 --> 00:09:43.509 align:start position:0%
this for now uh let's not make any
changes<00:09:41.839><c> here</c><00:09:42.200><c> again</c><00:09:42.399><c> we'll</c><00:09:42.600><c> pick</c><00:09:42.839><c> our</c>

00:09:43.509 --> 00:09:43.519 align:start position:0%
changes here again we'll pick our
 

00:09:43.519 --> 00:09:46.310 align:start position:0%
changes here again we'll pick our
evaluate<00:09:43.959><c> Docker</c><00:09:44.440><c> Cube</c><00:09:45.440><c> keep</c><00:09:45.640><c> the</c><00:09:45.800><c> medum</c>

00:09:46.310 --> 00:09:46.320 align:start position:0%
evaluate Docker Cube keep the medum
 

00:09:46.320 --> 00:09:49.470 align:start position:0%
evaluate Docker Cube keep the medum
priority<00:09:47.320><c> and</c><00:09:47.480><c> we'll</c><00:09:47.680><c> launch</c><00:09:47.959><c> this</c><00:09:48.120><c> job</c><00:09:48.480><c> now</c>

00:09:49.470 --> 00:09:49.480 align:start position:0%
priority and we'll launch this job now
 

00:09:49.480 --> 00:09:52.670 align:start position:0%
priority and we'll launch this job now
and<00:09:49.640><c> if</c><00:09:49.720><c> we</c><00:09:49.920><c> go</c><00:09:50.120><c> back</c><00:09:50.720><c> to</c><00:09:51.720><c> our</c><00:09:52.000><c> console</c>

00:09:52.670 --> 00:09:52.680 align:start position:0%
and if we go back to our console
 

00:09:52.680 --> 00:09:55.030 align:start position:0%
and if we go back to our console
hopefully<00:09:53.040><c> the</c><00:09:53.200><c> agent</c><00:09:53.600><c> will</c><00:09:54.120><c> pick</c><00:09:54.360><c> this</c><00:09:54.519><c> up</c>

00:09:55.030 --> 00:09:55.040 align:start position:0%
hopefully the agent will pick this up
 

00:09:55.040 --> 00:09:57.150 align:start position:0%
hopefully the agent will pick this up
now

00:09:57.150 --> 00:09:57.160 align:start position:0%
now
 

00:09:57.160 --> 00:10:38.590 align:start position:0%
now
and<00:09:58.160><c> it's</c><00:09:58.360><c> running</c><00:09:58.720><c> the</c><00:09:58.880><c> job</c><00:09:59.360><c> e</c>

00:10:38.590 --> 00:10:38.600 align:start position:0%
 
 

00:10:38.600 --> 00:10:40.470 align:start position:0%
 
and<00:10:38.800><c> we</c><00:10:38.920><c> can</c><00:10:39.079><c> see</c><00:10:39.320><c> the</c><00:10:39.519><c> candidate</c><00:10:40.000><c> preference</c>

00:10:40.470 --> 00:10:40.480 align:start position:0%
and we can see the candidate preference
 

00:10:40.480 --> 00:10:42.590 align:start position:0%
and we can see the candidate preference
score<00:10:41.320><c> which</c><00:10:41.480><c> is</c>

00:10:42.590 --> 00:10:42.600 align:start position:0%
score which is
 

00:10:42.600 --> 00:10:45.670 align:start position:0%
score which is
0.04<00:10:43.600><c> which</c><00:10:43.760><c> means</c><00:10:44.000><c> there</c><00:10:44.120><c> is</c><00:10:44.240><c> a</c><00:10:44.639><c> tiny</c><00:10:45.279><c> bit</c><00:10:45.480><c> of</c>

00:10:45.670 --> 00:10:45.680 align:start position:0%
0.04 which means there is a tiny bit of
 

00:10:45.680 --> 00:10:47.710 align:start position:0%
0.04 which means there is a tiny bit of
improvement<00:10:46.480><c> but</c><00:10:46.680><c> it's</c><00:10:47.040><c> it's</c><00:10:47.200><c> really</c><00:10:47.480><c> very</c>

00:10:47.710 --> 00:10:47.720 align:start position:0%
improvement but it's it's really very
 

00:10:47.720 --> 00:10:49.590 align:start position:0%
improvement but it's it's really very
similar<00:10:48.120><c> to</c><00:10:48.279><c> the</c>

00:10:49.590 --> 00:10:49.600 align:start position:0%
similar to the
 

00:10:49.600 --> 00:10:52.110 align:start position:0%
similar to the
Baseline<00:10:50.600><c> and</c><00:10:50.880><c> the</c><00:10:51.000><c> evaluation</c><00:10:51.600><c> is</c><00:10:51.800><c> logged</c>

00:10:52.110 --> 00:10:52.120 align:start position:0%
Baseline and the evaluation is logged
 

00:10:52.120 --> 00:10:53.870 align:start position:0%
Baseline and the evaluation is logged
into<00:10:52.320><c> weights</c><00:10:52.480><c> and</c><00:10:52.639><c> biases</c><00:10:53.120><c> so</c><00:10:53.279><c> we</c><00:10:53.480><c> just</c>

00:10:53.870 --> 00:10:53.880 align:start position:0%
into weights and biases so we just
 

00:10:53.880 --> 00:10:56.389 align:start position:0%
into weights and biases so we just
triggered<00:10:54.360><c> the</c><00:10:54.480><c> Lou</c><00:10:54.800><c> job</c><00:10:55.800><c> uh</c><00:10:55.920><c> from</c><00:10:56.160><c> weights</c>

00:10:56.389 --> 00:10:56.399 align:start position:0%
triggered the Lou job uh from weights
 

00:10:56.399 --> 00:10:57.389 align:start position:0%
triggered the Lou job uh from weights
and

00:10:57.389 --> 00:10:57.399 align:start position:0%
and
 

00:10:57.399 --> 00:11:00.310 align:start position:0%
and
biases<00:10:58.399><c> but</c><00:10:58.880><c> uh</c><00:10:59.040><c> this</c><00:10:59.360><c> has</c><00:10:59.480><c> been</c><00:10:59.639><c> done</c>

00:11:00.310 --> 00:11:00.320 align:start position:0%
biases but uh this has been done
 

00:11:00.320 --> 00:11:03.389 align:start position:0%
biases but uh this has been done
manually<00:11:01.320><c> um</c><00:11:01.720><c> and</c><00:11:01.880><c> in</c><00:11:02.040><c> the</c><00:11:02.200><c> next</c><00:11:02.440><c> lesson</c><00:11:03.160><c> we'll</c>

00:11:03.389 --> 00:11:03.399 align:start position:0%
manually um and in the next lesson we'll
 

00:11:03.399 --> 00:11:05.750 align:start position:0%
manually um and in the next lesson we'll
do<00:11:03.680><c> the</c><00:11:03.839><c> same</c><00:11:04.040><c> thing</c><00:11:04.320><c> we</c><00:11:04.560><c> we'll</c><00:11:04.720><c> trigger</c><00:11:05.120><c> our</c>

00:11:05.750 --> 00:11:05.760 align:start position:0%
do the same thing we we'll trigger our
 

00:11:05.760 --> 00:11:08.030 align:start position:0%
do the same thing we we'll trigger our
um<00:11:06.000><c> evaluation</c><00:11:06.560><c> script</c><00:11:07.399><c> but</c><00:11:07.519><c> we'll</c><00:11:07.720><c> do</c><00:11:07.839><c> it</c>

00:11:08.030 --> 00:11:08.040 align:start position:0%
um evaluation script but we'll do it
 

00:11:08.040 --> 00:11:10.150 align:start position:0%
um evaluation script but we'll do it
through<00:11:08.360><c> with</c><00:11:08.680><c> through</c><00:11:09.079><c> uh</c><00:11:09.200><c> model</c><00:11:09.519><c> registry</c>

00:11:10.150 --> 00:11:10.160 align:start position:0%
through with through uh model registry
 

00:11:10.160 --> 00:11:13.160 align:start position:0%
through with through uh model registry
automations

