"""
Pydantic models for Gemini API pricing structures.
Handles the complex pricing models including variable costs, context caching, and content types.
"""

from typing import Optional, Literal, Union
from pydantic import BaseModel, Field
from enum import Enum


class ContentType(str, Enum):
    """Types of content that can be processed by Gemini models."""
    TEXT_IMAGE_VIDEO = "text_image_video"
    AUDIO = "audio"


class OutputType(str, Enum):
    """Types of output that can be generated by Gemini models."""
    NON_THINKING = "non_thinking"
    THINKING = "thinking"


class PricingModel(str, Enum):
    """Different pricing models supported by Gemini."""
    SIMPLE = "simple"  # Basic input/output costs
    VARIABLE_BY_PROMPT_SIZE = "variable_by_prompt_size"  # Different costs for small/large prompts
    COMPLEX = "complex"  # Full feature set with content types


class ModelPricingInfo(BaseModel):
    """Complete pricing information for a Gemini model."""
    model_name: str
    pricing_model: PricingModel
    
    # Basic costs (per million tokens)
    input_cost_text_image_video: float = 0.0
    input_cost_audio: float = 0.0
    output_cost_non_thinking: float = 0.0
    output_cost_thinking: float = 0.0
    
    # Variable costs for Pro models (per million tokens)
    input_cost_small_prompt: float = 0.0  # <= threshold
    input_cost_large_prompt: float = 0.0  # > threshold
    output_cost_small_prompt: float = 0.0
    output_cost_large_prompt: float = 0.0
    
    # Context caching costs (per million tokens)
    context_caching_cost_text_image_video: float = 0.0
    context_caching_cost_audio: float = 0.0
    context_caching_cost_small_prompt: float = 0.0
    context_caching_cost_large_prompt: float = 0.0
    context_caching_cost_per_hour: float = 0.0  # Storage cost per million tokens per hour
    
    # Grounding costs
    grounding_free_requests_per_day: int = 1500
    grounding_cost_per_1000_requests: float = 35.0
    
    # Configuration
    prompt_size_threshold: int = 200000  # Tokens threshold for variable pricing
    supports_caching: bool = False
    supports_thinking: bool = False
    supports_grounding: bool = False
    
    def get_input_cost(self, content_type: ContentType, prompt_tokens: int) -> float:
        """Get input cost per million tokens based on content type and prompt size."""
        if self.pricing_model == PricingModel.VARIABLE_BY_PROMPT_SIZE:
            # Pro model with variable pricing
            if prompt_tokens <= self.prompt_size_threshold:
                return self.input_cost_small_prompt
            else:
                return self.input_cost_large_prompt
        else:
            # Flash model or simple pricing
            if content_type == ContentType.AUDIO:
                return self.input_cost_audio
            else:
                return self.input_cost_text_image_video
    
    def get_output_cost(self, output_type: OutputType, prompt_tokens: int) -> float:
        """Get output cost per million tokens based on output type and prompt size."""
        if self.pricing_model == PricingModel.VARIABLE_BY_PROMPT_SIZE:
            # Pro model with variable pricing (includes thinking tokens)
            if prompt_tokens <= self.prompt_size_threshold:
                return self.output_cost_small_prompt
            else:
                return self.output_cost_large_prompt
        else:
            # Flash model with separate thinking/non-thinking costs
            if output_type == OutputType.THINKING:
                return self.output_cost_thinking
            else:
                return self.output_cost_non_thinking
    
    def get_caching_cost(self, content_type: ContentType, prompt_tokens: int) -> float:
        """Get context caching cost per million tokens."""
        if self.pricing_model == PricingModel.VARIABLE_BY_PROMPT_SIZE:
            # Pro model with variable caching costs
            if prompt_tokens <= self.prompt_size_threshold:
                return self.context_caching_cost_small_prompt
            else:
                return self.context_caching_cost_large_prompt
        else:
            # Flash model with content-type-based caching costs
            if content_type == ContentType.AUDIO:
                return self.context_caching_cost_audio
            else:
                return self.context_caching_cost_text_image_video


class CostCalculationRequest(BaseModel):
    """Request parameters for calculating API costs."""
    model_name: str
    input_tokens: int = Field(ge=0)
    output_tokens: int = Field(ge=0)
    content_type: ContentType = ContentType.TEXT_IMAGE_VIDEO
    output_type: OutputType = OutputType.NON_THINKING
    
    # Context caching parameters
    use_context_caching: bool = False
    cached_tokens: int = Field(default=0, ge=0)
    cache_duration_hours: float = Field(default=1.0, gt=0)
    
    # Grounding parameters
    use_grounding: bool = False
    grounding_requests: int = Field(default=0, ge=0)


class CostCalculationResult(BaseModel):
    """Result of cost calculation with detailed breakdown."""
    model_name: str
    total_cost: float = Field(ge=0)
    
    # Cost breakdown
    input_cost: float = Field(ge=0)
    output_cost: float = Field(ge=0)
    caching_cost: float = Field(default=0, ge=0)
    grounding_cost: float = Field(default=0, ge=0)
    
    # Details
    input_tokens: int
    output_tokens: int
    content_type: ContentType
    output_type: OutputType
    pricing_model: PricingModel
    
    # Rate information (per million tokens)
    input_rate_used: float
    output_rate_used: float
    caching_rate_used: Optional[float] = None 