WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:01.350 align:start position:0%
 
hey<00:00:00.199><c> developers</c><00:00:00.640><c> and</c><00:00:00.799><c> welcome</c><00:00:01.079><c> back</c><00:00:01.199><c> to</c>

00:00:01.350 --> 00:00:01.360 align:start position:0%
hey developers and welcome back to
 

00:00:01.360 --> 00:00:03.070 align:start position:0%
hey developers and welcome back to
another<00:00:01.680><c> video</c><00:00:02.120><c> one</c><00:00:02.240><c> of</c><00:00:02.360><c> the</c><00:00:02.480><c> issues</c><00:00:02.760><c> that</c><00:00:02.919><c> I</c>

00:00:03.070 --> 00:00:03.080 align:start position:0%
another video one of the issues that I
 

00:00:03.080 --> 00:00:04.990 align:start position:0%
another video one of the issues that I
have<00:00:03.480><c> working</c><00:00:03.800><c> with</c><00:00:04.000><c> AI</c><00:00:04.359><c> especially</c><00:00:04.720><c> on</c><00:00:04.839><c> my</c>

00:00:04.990 --> 00:00:05.000 align:start position:0%
have working with AI especially on my
 

00:00:05.000 --> 00:00:07.429 align:start position:0%
have working with AI especially on my
local<00:00:05.279><c> machine</c><00:00:05.920><c> is</c><00:00:06.120><c> that</c><00:00:06.560><c> my</c><00:00:06.720><c> machine</c><00:00:07.040><c> is</c><00:00:07.160><c> not</c>

00:00:07.429 --> 00:00:07.439 align:start position:0%
local machine is that my machine is not
 

00:00:07.439 --> 00:00:08.709 align:start position:0%
local machine is that my machine is not
good<00:00:07.600><c> enough</c><00:00:07.759><c> to</c><00:00:08.000><c> handle</c><00:00:08.320><c> a</c><00:00:08.400><c> lot</c><00:00:08.519><c> of</c><00:00:08.599><c> the</c>

00:00:08.709 --> 00:00:08.719 align:start position:0%
good enough to handle a lot of the
 

00:00:08.719 --> 00:00:10.669 align:start position:0%
good enough to handle a lot of the
models<00:00:09.080><c> that</c><00:00:09.200><c> I</c><00:00:09.400><c> really</c><00:00:09.639><c> want</c><00:00:09.800><c> to</c><00:00:10.000><c> use</c><00:00:10.400><c> and</c><00:00:10.519><c> one</c>

00:00:10.669 --> 00:00:10.679 align:start position:0%
models that I really want to use and one
 

00:00:10.679 --> 00:00:12.789 align:start position:0%
models that I really want to use and one
way<00:00:10.840><c> I</c><00:00:10.960><c> get</c><00:00:11.120><c> around</c><00:00:11.440><c> that</c><00:00:11.639><c> for</c><00:00:11.880><c> a</c><00:00:12.080><c> cheap</c><00:00:12.440><c> price</c>

00:00:12.789 --> 00:00:12.799 align:start position:0%
way I get around that for a cheap price
 

00:00:12.799 --> 00:00:14.749 align:start position:0%
way I get around that for a cheap price
so<00:00:12.920><c> that</c><00:00:13.040><c> I</c><00:00:13.160><c> can</c><00:00:13.280><c> use</c><00:00:13.759><c> much</c><00:00:14.000><c> bigger</c><00:00:14.320><c> models</c>

00:00:14.749 --> 00:00:14.759 align:start position:0%
so that I can use much bigger models
 

00:00:14.759 --> 00:00:17.109 align:start position:0%
so that I can use much bigger models
without<00:00:15.040><c> any</c><00:00:15.320><c> hassle</c><00:00:16.039><c> is</c><00:00:16.160><c> runp</c><00:00:16.560><c> POD</c><00:00:16.880><c> well</c><00:00:17.000><c> the</c>

00:00:17.109 --> 00:00:17.119 align:start position:0%
without any hassle is runp POD well the
 

00:00:17.119 --> 00:00:18.910 align:start position:0%
without any hassle is runp POD well the
first<00:00:17.279><c> question</c><00:00:17.480><c> is</c><00:00:17.840><c> what</c><00:00:18.000><c> is</c><00:00:18.160><c> runpod</c><00:00:18.800><c> well</c>

00:00:18.910 --> 00:00:18.920 align:start position:0%
first question is what is runpod well
 

00:00:18.920 --> 00:00:20.790 align:start position:0%
first question is what is runpod well
runpod<00:00:19.359><c> is</c><00:00:19.480><c> just</c><00:00:19.640><c> a</c><00:00:19.840><c> platform</c><00:00:20.519><c> that</c><00:00:20.640><c> is</c>

00:00:20.790 --> 00:00:20.800 align:start position:0%
runpod is just a platform that is
 

00:00:20.800 --> 00:00:22.870 align:start position:0%
runpod is just a platform that is
primarily<00:00:21.279><c> designed</c><00:00:21.760><c> for</c><00:00:22.080><c> AI</c><00:00:22.400><c> and</c><00:00:22.519><c> machine</c>

00:00:22.870 --> 00:00:22.880 align:start position:0%
primarily designed for AI and machine
 

00:00:22.880 --> 00:00:24.910 align:start position:0%
primarily designed for AI and machine
learning<00:00:23.359><c> applications</c><00:00:24.240><c> while</c><00:00:24.400><c> runpod</c>

00:00:24.910 --> 00:00:24.920 align:start position:0%
learning applications while runpod
 

00:00:24.920 --> 00:00:26.910 align:start position:0%
learning applications while runpod
provides<00:00:25.320><c> access</c><00:00:25.599><c> to</c><00:00:25.840><c> powerful</c><00:00:26.240><c> gpus</c><00:00:26.720><c> for</c>

00:00:26.910 --> 00:00:26.920 align:start position:0%
provides access to powerful gpus for
 

00:00:26.920 --> 00:00:28.390 align:start position:0%
provides access to powerful gpus for
building<00:00:27.199><c> and</c><00:00:27.359><c> testing</c><00:00:27.599><c> out</c><00:00:27.840><c> applications</c>

00:00:28.390 --> 00:00:28.400 align:start position:0%
building and testing out applications
 

00:00:28.400 --> 00:00:30.749 align:start position:0%
building and testing out applications
with<00:00:28.560><c> llms</c><00:00:29.400><c> providing</c><00:00:30.000><c> various</c><00:00:30.240><c> servers</c><00:00:30.599><c> as</c>

00:00:30.749 --> 00:00:30.759 align:start position:0%
with llms providing various servers as
 

00:00:30.759 --> 00:00:33.350 align:start position:0%
with llms providing various servers as
well<00:00:31.000><c> including</c><00:00:31.599><c> API</c><00:00:32.079><c> endpoints</c><00:00:32.840><c> well</c><00:00:33.000><c> a</c><00:00:33.200><c> pod</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
well including API endpoints well a pod
 

00:00:33.360 --> 00:00:35.030 align:start position:0%
well including API endpoints well a pod
is<00:00:33.440><c> simply</c><00:00:33.680><c> a</c><00:00:33.800><c> server</c><00:00:34.200><c> that</c><00:00:34.320><c> we</c><00:00:34.440><c> can</c><00:00:34.600><c> deploy</c>

00:00:35.030 --> 00:00:35.040 align:start position:0%
is simply a server that we can deploy
 

00:00:35.040 --> 00:00:36.229 align:start position:0%
is simply a server that we can deploy
and<00:00:35.160><c> then</c><00:00:35.280><c> we</c><00:00:35.360><c> can</c><00:00:35.480><c> start</c><00:00:35.800><c> building</c>

00:00:36.229 --> 00:00:36.239 align:start position:0%
and then we can start building
 

00:00:36.239 --> 00:00:37.549 align:start position:0%
and then we can start building
applications<00:00:36.800><c> with</c><00:00:37.079><c> which</c><00:00:37.200><c> means</c><00:00:37.360><c> you</c><00:00:37.480><c> can</c>

00:00:37.549 --> 00:00:37.559 align:start position:0%
applications with which means you can
 

00:00:37.559 --> 00:00:39.030 align:start position:0%
applications with which means you can
run<00:00:37.760><c> something</c><00:00:38.000><c> like</c><00:00:38.160><c> a</c><00:00:38.280><c> 33</c><00:00:38.719><c> billion</c>

00:00:39.030 --> 00:00:39.040 align:start position:0%
run something like a 33 billion
 

00:00:39.040 --> 00:00:41.069 align:start position:0%
run something like a 33 billion
parameter<00:00:39.520><c> model</c><00:00:40.160><c> much</c><00:00:40.360><c> smoother</c><00:00:40.960><c> which</c>

00:00:41.069 --> 00:00:41.079 align:start position:0%
parameter model much smoother which
 

00:00:41.079 --> 00:00:42.190 align:start position:0%
parameter model much smoother which
leads<00:00:41.320><c> me</c><00:00:41.399><c> into</c><00:00:41.600><c> what</c><00:00:41.680><c> I've</c><00:00:41.800><c> kind</c><00:00:41.920><c> of</c><00:00:42.039><c> already</c>

00:00:42.190 --> 00:00:42.200 align:start position:0%
leads me into what I've kind of already
 

00:00:42.200 --> 00:00:44.510 align:start position:0%
leads me into what I've kind of already
talked<00:00:42.440><c> about</c><00:00:42.680><c> the</c><00:00:42.920><c> reason</c><00:00:43.360><c> for</c><00:00:43.680><c> this</c><00:00:43.879><c> service</c>

00:00:44.510 --> 00:00:44.520 align:start position:0%
talked about the reason for this service
 

00:00:44.520 --> 00:00:46.990 align:start position:0%
talked about the reason for this service
is<00:00:44.719><c> that</c><00:00:45.079><c> we</c><00:00:45.239><c> have</c><00:00:45.520><c> Hardware</c><00:00:46.000><c> limitations</c><00:00:46.840><c> for</c>

00:00:46.990 --> 00:00:47.000 align:start position:0%
is that we have Hardware limitations for
 

00:00:47.000 --> 00:00:48.950 align:start position:0%
is that we have Hardware limitations for
example<00:00:47.280><c> the</c><00:00:47.399><c> Llama</c><00:00:47.680><c> 2</c><00:00:47.960><c> 33</c><00:00:48.320><c> billion</c><00:00:48.559><c> parameter</c>

00:00:48.950 --> 00:00:48.960 align:start position:0%
example the Llama 2 33 billion parameter
 

00:00:48.960 --> 00:00:51.990 align:start position:0%
example the Llama 2 33 billion parameter
model<00:00:49.559><c> it</c><00:00:49.719><c> requires</c><00:00:50.199><c> at</c><00:00:50.320><c> least</c><00:00:50.559><c> 20</c><00:00:50.879><c> GB</c><00:00:51.280><c> of</c><00:00:51.399><c> vram</c>

00:00:51.990 --> 00:00:52.000 align:start position:0%
model it requires at least 20 GB of vram
 

00:00:52.000 --> 00:00:53.990 align:start position:0%
model it requires at least 20 GB of vram
and<00:00:52.120><c> that's</c><00:00:52.359><c> not</c><00:00:52.640><c> cheap</c><00:00:53.000><c> and</c><00:00:53.120><c> then</c><00:00:53.280><c> o</c><00:00:53.559><c> llama's</c>

00:00:53.990 --> 00:00:54.000 align:start position:0%
and that's not cheap and then o llama's
 

00:00:54.000 --> 00:00:56.270 align:start position:0%
and that's not cheap and then o llama's
GitHub<00:00:54.640><c> suggests</c><00:00:55.079><c> that</c><00:00:55.199><c> to</c><00:00:55.320><c> run</c><00:00:55.440><c> a</c><00:00:55.600><c> 33</c><00:00:56.000><c> billion</c>

00:00:56.270 --> 00:00:56.280 align:start position:0%
GitHub suggests that to run a 33 billion
 

00:00:56.280 --> 00:00:58.590 align:start position:0%
GitHub suggests that to run a 33 billion
parameter<00:00:56.680><c> model</c><00:00:56.960><c> you</c><00:00:57.120><c> also</c><00:00:57.280><c> need</c><00:00:57.520><c> 32</c><00:00:57.920><c> GB</c><00:00:58.359><c> of</c>

00:00:58.590 --> 00:00:58.600 align:start position:0%
parameter model you also need 32 GB of
 

00:00:58.600 --> 00:00:59.910 align:start position:0%
parameter model you also need 32 GB of
RAM<00:00:58.960><c> what</c><00:00:59.079><c> I'm</c><00:00:59.199><c> going</c><00:00:59.359><c> to</c><00:00:59.519><c> walk</c><00:00:59.719><c> through</c>

00:00:59.910 --> 00:00:59.920 align:start position:0%
RAM what I'm going to walk through
 

00:00:59.920 --> 00:01:01.869 align:start position:0%
RAM what I'm going to walk through
through<00:01:00.079><c> with</c><00:01:00.160><c> you</c><00:01:00.280><c> today</c><00:01:00.840><c> is</c><00:01:01.000><c> how</c><00:01:01.160><c> to</c><00:01:01.440><c> deploy</c>

00:01:01.869 --> 00:01:01.879 align:start position:0%
through with you today is how to deploy
 

00:01:01.879 --> 00:01:03.910 align:start position:0%
through with you today is how to deploy
a<00:01:02.199><c> pod</c><00:01:02.519><c> and</c><00:01:02.719><c> write</c><00:01:03.000><c> code</c><00:01:03.320><c> in</c><00:01:03.440><c> the</c><00:01:03.559><c> jupyter</c>

00:01:03.910 --> 00:01:03.920 align:start position:0%
a pod and write code in the jupyter
 

00:01:03.920 --> 00:01:06.070 align:start position:0%
a pod and write code in the jupyter
notebooks<00:01:04.360><c> terminal</c><00:01:05.080><c> in</c><00:01:05.280><c> the</c><00:01:05.400><c> server</c><00:01:05.880><c> let's</c>

00:01:06.070 --> 00:01:06.080 align:start position:0%
notebooks terminal in the server let's
 

00:01:06.080 --> 00:01:07.310 align:start position:0%
notebooks terminal in the server let's
get<00:01:06.200><c> started</c><00:01:06.560><c> all</c><00:01:06.640><c> right</c><00:01:06.760><c> well</c><00:01:06.920><c> first</c><00:01:07.119><c> we</c><00:01:07.200><c> need</c>

00:01:07.310 --> 00:01:07.320 align:start position:0%
get started all right well first we need
 

00:01:07.320 --> 00:01:09.550 align:start position:0%
get started all right well first we need
to<00:01:07.439><c> go</c><00:01:07.560><c> to</c><00:01:07.880><c> runpod</c><00:01:08.479><c> doio</c><00:01:09.080><c> and</c><00:01:09.240><c> you</c><00:01:09.320><c> will</c><00:01:09.439><c> be</c>

00:01:09.550 --> 00:01:09.560 align:start position:0%
to go to runpod doio and you will be
 

00:01:09.560 --> 00:01:11.630 align:start position:0%
to go to runpod doio and you will be
greeted<00:01:09.920><c> with</c><00:01:10.080><c> this</c><00:01:10.240><c> screen</c><00:01:11.000><c> and</c><00:01:11.360><c> if</c><00:01:11.479><c> this</c><00:01:11.560><c> is</c>

00:01:11.630 --> 00:01:11.640 align:start position:0%
greeted with this screen and if this is
 

00:01:11.640 --> 00:01:13.230 align:start position:0%
greeted with this screen and if this is
your<00:01:11.799><c> first</c><00:01:12.000><c> time</c><00:01:12.119><c> using</c><00:01:12.400><c> it</c><00:01:12.680><c> go</c><00:01:12.840><c> ahead</c><00:01:13.040><c> and</c>

00:01:13.230 --> 00:01:13.240 align:start position:0%
your first time using it go ahead and
 

00:01:13.240 --> 00:01:15.149 align:start position:0%
your first time using it go ahead and
sign<00:01:13.520><c> up</c><00:01:14.159><c> and</c><00:01:14.240><c> then</c><00:01:14.360><c> once</c><00:01:14.520><c> you're</c><00:01:14.680><c> done</c><00:01:15.040><c> go</c>

00:01:15.149 --> 00:01:15.159 align:start position:0%
sign up and then once you're done go
 

00:01:15.159 --> 00:01:16.990 align:start position:0%
sign up and then once you're done go
ahead<00:01:15.360><c> and</c><00:01:15.479><c> log</c><00:01:15.880><c> in</c><00:01:16.400><c> all</c><00:01:16.520><c> right</c><00:01:16.640><c> and</c><00:01:16.720><c> you'll</c><00:01:16.880><c> be</c>

00:01:16.990 --> 00:01:17.000 align:start position:0%
ahead and log in all right and you'll be
 

00:01:17.000 --> 00:01:18.109 align:start position:0%
ahead and log in all right and you'll be
here<00:01:17.119><c> at</c><00:01:17.240><c> this</c><00:01:17.360><c> home</c><00:01:17.520><c> screen</c><00:01:17.799><c> after</c><00:01:18.000><c> you</c>

00:01:18.109 --> 00:01:18.119 align:start position:0%
here at this home screen after you
 

00:01:18.119 --> 00:01:19.910 align:start position:0%
here at this home screen after you
logged<00:01:18.439><c> in</c><00:01:18.920><c> and</c><00:01:19.040><c> the</c><00:01:19.200><c> first</c><00:01:19.360><c> thing</c><00:01:19.479><c> I</c><00:01:19.560><c> will</c><00:01:19.759><c> do</c>

00:01:19.910 --> 00:01:19.920 align:start position:0%
logged in and the first thing I will do
 

00:01:19.920 --> 00:01:21.550 align:start position:0%
logged in and the first thing I will do
on<00:01:20.000><c> the</c><00:01:20.119><c> left</c><00:01:20.320><c> hand</c><00:01:20.439><c> side</c><00:01:20.600><c> is</c><00:01:20.720><c> go</c><00:01:20.799><c> to</c><00:01:20.920><c> billing</c>

00:01:21.550 --> 00:01:21.560 align:start position:0%
on the left hand side is go to billing
 

00:01:21.560 --> 00:01:23.749 align:start position:0%
on the left hand side is go to billing
and<00:01:21.720><c> just</c><00:01:21.880><c> like</c><00:01:22.159><c> put</c><00:01:22.320><c> in</c><00:01:22.479><c> $5</c><00:01:23.280><c> okay</c><00:01:23.520><c> because</c><00:01:23.680><c> we</c>

00:01:23.749 --> 00:01:23.759 align:start position:0%
and just like put in $5 okay because we
 

00:01:23.759 --> 00:01:24.749 align:start position:0%
and just like put in $5 okay because we
have<00:01:23.880><c> to</c><00:01:23.960><c> have</c><00:01:24.040><c> some</c><00:01:24.159><c> money</c><00:01:24.360><c> on</c><00:01:24.439><c> the</c><00:01:24.520><c> account</c>

00:01:24.749 --> 00:01:24.759 align:start position:0%
have to have some money on the account
 

00:01:24.759 --> 00:01:26.350 align:start position:0%
have to have some money on the account
in<00:01:24.840><c> order</c><00:01:25.000><c> to</c><00:01:25.159><c> deploy</c><00:01:25.479><c> a</c><00:01:25.600><c> server</c><00:01:26.040><c> once</c><00:01:26.159><c> we're</c>

00:01:26.350 --> 00:01:26.360 align:start position:0%
in order to deploy a server once we're
 

00:01:26.360 --> 00:01:28.510 align:start position:0%
in order to deploy a server once we're
done<00:01:26.600><c> that</c><00:01:26.920><c> you</c><00:01:27.079><c> go</c><00:01:27.240><c> back</c><00:01:27.360><c> to</c><00:01:27.479><c> the</c><00:01:27.680><c> homepage</c><00:01:28.400><c> at</c>

00:01:28.510 --> 00:01:28.520 align:start position:0%
done that you go back to the homepage at
 

00:01:28.520 --> 00:01:30.749 align:start position:0%
done that you go back to the homepage at
the<00:01:28.720><c> top</c><00:01:29.000><c> left</c><00:01:29.280><c> they</c><00:01:29.400><c> have</c><00:01:29.520><c> this</c><00:01:29.640><c> G</c><00:01:29.920><c> GPU</c><00:01:30.360><c> Cloud</c>

00:01:30.749 --> 00:01:30.759 align:start position:0%
the top left they have this G GPU Cloud
 

00:01:30.759 --> 00:01:32.630 align:start position:0%
the top left they have this G GPU Cloud
here<00:01:31.119><c> so</c><00:01:31.280><c> we're</c><00:01:31.400><c> going</c><00:01:31.479><c> to</c><00:01:31.720><c> deploy</c><00:01:32.119><c> a</c><00:01:32.240><c> GPU</c>

00:01:32.630 --> 00:01:32.640 align:start position:0%
here so we're going to deploy a GPU
 

00:01:32.640 --> 00:01:34.190 align:start position:0%
here so we're going to deploy a GPU
Cloud<00:01:32.960><c> Server</c><00:01:33.320><c> and</c><00:01:33.439><c> then</c><00:01:33.560><c> on</c><00:01:33.680><c> this</c><00:01:33.799><c> screen</c><00:01:34.119><c> I'm</c>

00:01:34.190 --> 00:01:34.200 align:start position:0%
Cloud Server and then on this screen I'm
 

00:01:34.200 --> 00:01:35.469 align:start position:0%
Cloud Server and then on this screen I'm
going<00:01:34.320><c> to</c><00:01:34.399><c> scroll</c><00:01:34.759><c> down</c><00:01:34.920><c> a</c><00:01:35.040><c> little</c><00:01:35.200><c> bit</c><00:01:35.399><c> you</c>

00:01:35.469 --> 00:01:35.479 align:start position:0%
going to scroll down a little bit you
 

00:01:35.479 --> 00:01:37.670 align:start position:0%
going to scroll down a little bit you
can<00:01:35.759><c> choose</c><00:01:36.399><c> whatever</c><00:01:36.840><c> server</c><00:01:37.159><c> you</c><00:01:37.280><c> want</c>

00:01:37.670 --> 00:01:37.680 align:start position:0%
can choose whatever server you want
 

00:01:37.680 --> 00:01:38.950 align:start position:0%
can choose whatever server you want
right<00:01:37.880><c> depending</c><00:01:38.159><c> on</c><00:01:38.320><c> what</c><00:01:38.439><c> kind</c><00:01:38.560><c> of</c><00:01:38.680><c> model</c>

00:01:38.950 --> 00:01:38.960 align:start position:0%
right depending on what kind of model
 

00:01:38.960 --> 00:01:40.990 align:start position:0%
right depending on what kind of model
you<00:01:39.159><c> like</c><00:01:39.360><c> to</c><00:01:39.479><c> run</c><00:01:39.880><c> if</c><00:01:39.960><c> you</c><00:01:40.040><c> want</c><00:01:40.159><c> to</c><00:01:40.280><c> run</c><00:01:40.840><c> a</c>

00:01:40.990 --> 00:01:41.000 align:start position:0%
you like to run if you want to run a
 

00:01:41.000 --> 00:01:42.870 align:start position:0%
you like to run if you want to run a
smaller<00:01:41.360><c> model</c><00:01:41.640><c> then</c><00:01:41.759><c> you</c><00:01:41.880><c> don't</c><00:01:42.079><c> need</c><00:01:42.280><c> to</c>

00:01:42.870 --> 00:01:42.880 align:start position:0%
smaller model then you don't need to
 

00:01:42.880 --> 00:01:45.709 align:start position:0%
smaller model then you don't need to
deploy<00:01:43.320><c> this</c><00:01:43.840><c> uh</c><00:01:43.960><c> RTX</c><00:01:44.439><c> a6000</c><00:01:45.280><c> right</c><00:01:45.399><c> here</c>

00:01:45.709 --> 00:01:45.719 align:start position:0%
deploy this uh RTX a6000 right here
 

00:01:45.719 --> 00:01:47.230 align:start position:0%
deploy this uh RTX a6000 right here
right<00:01:45.920><c> this</c><00:01:46.040><c> is</c><00:01:46.119><c> one</c><00:01:46.360><c> I'm</c><00:01:46.439><c> going</c><00:01:46.520><c> to</c><00:01:46.680><c> use</c><00:01:47.119><c> just</c>

00:01:47.230 --> 00:01:47.240 align:start position:0%
right this is one I'm going to use just
 

00:01:47.240 --> 00:01:48.910 align:start position:0%
right this is one I'm going to use just
to<00:01:47.360><c> show</c><00:01:47.560><c> you</c><00:01:47.880><c> but</c><00:01:48.000><c> you</c><00:01:48.079><c> can</c><00:01:48.240><c> deploy</c><00:01:48.640><c> any</c><00:01:48.840><c> of</c>

00:01:48.910 --> 00:01:48.920 align:start position:0%
to show you but you can deploy any of
 

00:01:48.920 --> 00:01:49.789 align:start position:0%
to show you but you can deploy any of
these<00:01:49.040><c> if</c><00:01:49.119><c> you</c><00:01:49.159><c> want</c><00:01:49.240><c> to</c><00:01:49.320><c> go</c><00:01:49.399><c> with</c><00:01:49.479><c> a</c><00:01:49.560><c> cheaper</c>

00:01:49.789 --> 00:01:49.799 align:start position:0%
these if you want to go with a cheaper
 

00:01:49.799 --> 00:01:51.830 align:start position:0%
these if you want to go with a cheaper
one<00:01:50.200><c> that's</c><00:01:50.439><c> perfectly</c><00:01:50.880><c> fine</c><00:01:51.200><c> but</c><00:01:51.399><c> whichever</c>

00:01:51.830 --> 00:01:51.840 align:start position:0%
one that's perfectly fine but whichever
 

00:01:51.840 --> 00:01:53.469 align:start position:0%
one that's perfectly fine but whichever
one<00:01:52.000><c> you</c><00:01:52.159><c> choose</c><00:01:52.759><c> you're</c><00:01:52.920><c> going</c><00:01:53.000><c> to</c><00:01:53.200><c> click</c>

00:01:53.469 --> 00:01:53.479 align:start position:0%
one you choose you're going to click
 

00:01:53.479 --> 00:01:54.910 align:start position:0%
one you choose you're going to click
deploy<00:01:54.000><c> and</c><00:01:54.119><c> then</c><00:01:54.240><c> once</c><00:01:54.360><c> you're</c><00:01:54.479><c> on</c><00:01:54.759><c> this</c>

00:01:54.910 --> 00:01:54.920 align:start position:0%
deploy and then once you're on this
 

00:01:54.920 --> 00:01:56.709 align:start position:0%
deploy and then once you're on this
screen<00:01:55.680><c> the</c><00:01:55.840><c> first</c><00:01:56.079><c> thing</c><00:01:56.280><c> that</c><00:01:56.479><c> I</c><00:01:56.560><c> would</c>

00:01:56.709 --> 00:01:56.719 align:start position:0%
screen the first thing that I would
 

00:01:56.719 --> 00:01:58.230 align:start position:0%
screen the first thing that I would
recommend<00:01:57.079><c> doing</c><00:01:57.439><c> is</c><00:01:57.719><c> clicking</c><00:01:58.039><c> the</c>

00:01:58.230 --> 00:01:58.240 align:start position:0%
recommend doing is clicking the
 

00:01:58.240 --> 00:02:00.029 align:start position:0%
recommend doing is clicking the
customize<00:01:58.719><c> deployment</c><00:01:59.200><c> here</c><00:01:59.680><c> and</c><00:01:59.840><c> and</c><00:01:59.920><c> we</c>

00:02:00.029 --> 00:02:00.039 align:start position:0%
customize deployment here and and we
 

00:02:00.039 --> 00:02:02.389 align:start position:0%
customize deployment here and and we
need<00:02:00.159><c> to</c><00:02:00.439><c> increase</c><00:02:01.320><c> the</c><00:02:01.719><c> the</c><00:02:01.799><c> amount</c><00:02:02.000><c> of</c><00:02:02.119><c> space</c>

00:02:02.389 --> 00:02:02.399 align:start position:0%
need to increase the the amount of space
 

00:02:02.399 --> 00:02:03.310 align:start position:0%
need to increase the the amount of space
that<00:02:02.520><c> we</c><00:02:02.680><c> have</c><00:02:02.840><c> because</c><00:02:03.000><c> if</c><00:02:03.079><c> you</c><00:02:03.159><c> want</c><00:02:03.240><c> to</c>

00:02:03.310 --> 00:02:03.320 align:start position:0%
that we have because if you want to
 

00:02:03.320 --> 00:02:04.350 align:start position:0%
that we have because if you want to
install<00:02:03.560><c> the</c><00:02:03.640><c> bigger</c><00:02:03.880><c> models</c><00:02:04.159><c> you're</c><00:02:04.280><c> going</c>

00:02:04.350 --> 00:02:04.360 align:start position:0%
install the bigger models you're going
 

00:02:04.360 --> 00:02:05.990 align:start position:0%
install the bigger models you're going
to<00:02:04.479><c> need</c><00:02:04.640><c> more</c><00:02:04.840><c> space</c><00:02:05.280><c> so</c><00:02:05.439><c> I</c><00:02:05.520><c> would</c><00:02:05.680><c> just</c><00:02:05.840><c> put</c>

00:02:05.990 --> 00:02:06.000 align:start position:0%
to need more space so I would just put
 

00:02:06.000 --> 00:02:07.950 align:start position:0%
to need more space so I would just put
in<00:02:06.320><c> 100</c><00:02:06.600><c> GB</c><00:02:07.039><c> here</c><00:02:07.200><c> and</c><00:02:07.320><c> that'll</c><00:02:07.520><c> probably</c><00:02:07.799><c> be</c>

00:02:07.950 --> 00:02:07.960 align:start position:0%
in 100 GB here and that'll probably be
 

00:02:07.960 --> 00:02:10.109 align:start position:0%
in 100 GB here and that'll probably be
fine<00:02:08.239><c> then</c><00:02:08.399><c> choose</c><00:02:08.720><c> set</c><00:02:08.959><c> overrides</c><00:02:09.800><c> and</c><00:02:09.920><c> then</c>

00:02:10.109 --> 00:02:10.119 align:start position:0%
fine then choose set overrides and then
 

00:02:10.119 --> 00:02:11.990 align:start position:0%
fine then choose set overrides and then
we're<00:02:10.319><c> good</c><00:02:10.560><c> to</c><00:02:10.879><c> hit</c><00:02:11.120><c> continue</c><00:02:11.640><c> and</c><00:02:11.760><c> the</c><00:02:11.840><c> next</c>

00:02:11.990 --> 00:02:12.000 align:start position:0%
we're good to hit continue and the next
 

00:02:12.000 --> 00:02:13.670 align:start position:0%
we're good to hit continue and the next
thing<00:02:12.120><c> we</c><00:02:12.239><c> need</c><00:02:12.480><c> is</c><00:02:12.599><c> a</c><00:02:12.760><c> template</c><00:02:13.200><c> so</c><00:02:13.440><c> at</c><00:02:13.520><c> the</c>

00:02:13.670 --> 00:02:13.680 align:start position:0%
thing we need is a template so at the
 

00:02:13.680 --> 00:02:15.550 align:start position:0%
thing we need is a template so at the
top<00:02:13.920><c> right</c><00:02:14.160><c> here</c><00:02:14.640><c> you</c><00:02:14.760><c> can</c><00:02:14.959><c> search</c><00:02:15.200><c> for</c><00:02:15.360><c> any</c>

00:02:15.550 --> 00:02:15.560 align:start position:0%
top right here you can search for any
 

00:02:15.560 --> 00:02:17.550 align:start position:0%
top right here you can search for any
template<00:02:16.080><c> and</c><00:02:16.519><c> these</c><00:02:16.680><c> are</c><00:02:17.000><c> essentially</c><00:02:17.440><c> going</c>

00:02:17.550 --> 00:02:17.560 align:start position:0%
template and these are essentially going
 

00:02:17.560 --> 00:02:19.949 align:start position:0%
template and these are essentially going
to<00:02:17.760><c> have</c><00:02:18.000><c> things</c><00:02:18.319><c> already</c><00:02:18.760><c> installed</c><00:02:19.360><c> for</c><00:02:19.640><c> you</c>

00:02:19.949 --> 00:02:19.959 align:start position:0%
to have things already installed for you
 

00:02:19.959 --> 00:02:21.229 align:start position:0%
to have things already installed for you
so<00:02:20.120><c> that</c><00:02:20.239><c> you</c><00:02:20.319><c> can</c><00:02:20.480><c> go</c><00:02:20.560><c> ahead</c><00:02:20.760><c> and</c><00:02:20.879><c> get</c><00:02:21.040><c> things</c>

00:02:21.229 --> 00:02:21.239 align:start position:0%
so that you can go ahead and get things
 

00:02:21.239 --> 00:02:22.790 align:start position:0%
so that you can go ahead and get things
up<00:02:21.400><c> and</c><00:02:21.560><c> running</c><00:02:21.920><c> right</c><00:02:22.160><c> away</c><00:02:22.560><c> I'm</c><00:02:22.640><c> going</c><00:02:22.720><c> to</c>

00:02:22.790 --> 00:02:22.800 align:start position:0%
up and running right away I'm going to
 

00:02:22.800 --> 00:02:25.270 align:start position:0%
up and running right away I'm going to
choose<00:02:23.000><c> the</c><00:02:23.120><c> runp</c><00:02:23.440><c> Pod</c><00:02:23.800><c> P</c><00:02:24.040><c> torch</c><00:02:24.280><c> 2.2.1</c>

00:02:25.270 --> 00:02:25.280 align:start position:0%
choose the runp Pod P torch 2.2.1
 

00:02:25.280 --> 00:02:26.990 align:start position:0%
choose the runp Pod P torch 2.2.1
version<00:02:25.800><c> and</c><00:02:25.920><c> then</c><00:02:26.040><c> we'll</c><00:02:26.200><c> see</c><00:02:26.480><c> a</c><00:02:26.599><c> terminal</c>

00:02:26.990 --> 00:02:27.000 align:start position:0%
version and then we'll see a terminal
 

00:02:27.000 --> 00:02:28.390 align:start position:0%
version and then we'll see a terminal
access<00:02:27.280><c> and</c><00:02:27.400><c> a</c><00:02:27.480><c> jupyter</c><00:02:27.800><c> notebook</c><00:02:28.160><c> go</c><00:02:28.239><c> ahead</c>

00:02:28.390 --> 00:02:28.400 align:start position:0%
access and a jupyter notebook go ahead
 

00:02:28.400 --> 00:02:29.949 align:start position:0%
access and a jupyter notebook go ahead
and<00:02:28.480><c> just</c><00:02:28.599><c> keep</c><00:02:28.760><c> those</c><00:02:28.920><c> checked</c><00:02:29.400><c> and</c><00:02:29.519><c> then</c><00:02:29.599><c> hit</c>

00:02:29.949 --> 00:02:29.959 align:start position:0%
and just keep those checked and then hit
 

00:02:29.959 --> 00:02:31.470 align:start position:0%
and just keep those checked and then hit
continue<00:02:30.400><c> and</c><00:02:30.519><c> then</c><00:02:30.640><c> once</c><00:02:30.760><c> you're</c><00:02:30.959><c> ready</c><00:02:31.280><c> just</c>

00:02:31.470 --> 00:02:31.480 align:start position:0%
continue and then once you're ready just
 

00:02:31.480 --> 00:02:33.270 align:start position:0%
continue and then once you're ready just
hit<00:02:31.720><c> deploy</c><00:02:32.280><c> and</c><00:02:32.400><c> this</c><00:02:32.480><c> will</c><00:02:32.680><c> just</c><00:02:32.840><c> take</c><00:02:33.080><c> a</c>

00:02:33.270 --> 00:02:33.280 align:start position:0%
hit deploy and this will just take a
 

00:02:33.280 --> 00:02:35.670 align:start position:0%
hit deploy and this will just take a
minute<00:02:33.800><c> or</c><00:02:33.959><c> two</c><00:02:34.200><c> maybe</c><00:02:34.400><c> 2</c><00:02:34.599><c> minutes</c><00:02:34.879><c> to</c><00:02:35.239><c> fully</c>

00:02:35.670 --> 00:02:35.680 align:start position:0%
minute or two maybe 2 minutes to fully
 

00:02:35.680 --> 00:02:37.390 align:start position:0%
minute or two maybe 2 minutes to fully
finish<00:02:36.080><c> running</c><00:02:36.760><c> okay</c><00:02:36.879><c> you</c><00:02:37.040><c> know</c><00:02:37.160><c> when</c><00:02:37.280><c> it's</c>

00:02:37.390 --> 00:02:37.400 align:start position:0%
finish running okay you know when it's
 

00:02:37.400 --> 00:02:39.149 align:start position:0%
finish running okay you know when it's
done<00:02:37.640><c> whenever</c><00:02:38.080><c> it</c><00:02:38.280><c> says</c><00:02:38.599><c> running</c><00:02:38.920><c> here</c><00:02:39.040><c> at</c>

00:02:39.149 --> 00:02:39.159 align:start position:0%
done whenever it says running here at
 

00:02:39.159 --> 00:02:41.550 align:start position:0%
done whenever it says running here at
the<00:02:39.319><c> top</c><00:02:39.560><c> right</c><00:02:40.040><c> now</c><00:02:40.560><c> click</c><00:02:40.840><c> the</c><00:02:41.040><c> drop</c><00:02:41.360><c> down</c>

00:02:41.550 --> 00:02:41.560 align:start position:0%
the top right now click the drop down
 

00:02:41.560 --> 00:02:43.589 align:start position:0%
the top right now click the drop down
arrow<00:02:42.200><c> and</c><00:02:42.360><c> then</c><00:02:42.640><c> there's</c><00:02:42.840><c> a</c><00:02:43.040><c> few</c><00:02:43.239><c> settings</c>

00:02:43.589 --> 00:02:43.599 align:start position:0%
arrow and then there's a few settings
 

00:02:43.599 --> 00:02:44.630 align:start position:0%
arrow and then there's a few settings
here<00:02:43.760><c> but</c><00:02:43.879><c> we're</c><00:02:44.000><c> just</c><00:02:44.120><c> going</c><00:02:44.200><c> to</c><00:02:44.280><c> worry</c><00:02:44.480><c> about</c>

00:02:44.630 --> 00:02:44.640 align:start position:0%
here but we're just going to worry about
 

00:02:44.640 --> 00:02:46.309 align:start position:0%
here but we're just going to worry about
one<00:02:44.760><c> and</c><00:02:44.879><c> that's</c><00:02:45.000><c> to</c><00:02:45.200><c> connect</c><00:02:45.560><c> so</c><00:02:45.920><c> click</c><00:02:46.120><c> on</c>

00:02:46.309 --> 00:02:46.319 align:start position:0%
one and that's to connect so click on
 

00:02:46.319 --> 00:02:47.670 align:start position:0%
one and that's to connect so click on
connect<00:02:46.840><c> and</c><00:02:46.959><c> what</c><00:02:47.080><c> we</c><00:02:47.159><c> want</c><00:02:47.239><c> to</c><00:02:47.360><c> do</c><00:02:47.480><c> is</c><00:02:47.560><c> we</c>

00:02:47.670 --> 00:02:47.680 align:start position:0%
connect and what we want to do is we
 

00:02:47.680 --> 00:02:48.949 align:start position:0%
connect and what we want to do is we
want<00:02:47.760><c> to</c><00:02:47.840><c> start</c><00:02:48.040><c> a</c><00:02:48.159><c> web</c><00:02:48.319><c> terminal</c><00:02:48.680><c> just</c><00:02:48.800><c> to</c>

00:02:48.949 --> 00:02:48.959 align:start position:0%
want to start a web terminal just to
 

00:02:48.959 --> 00:02:50.750 align:start position:0%
want to start a web terminal just to
install<00:02:49.280><c> a</c><00:02:49.360><c> couple</c><00:02:49.599><c> things</c><00:02:49.879><c> first</c><00:02:50.360><c> so</c><00:02:50.599><c> click</c>

00:02:50.750 --> 00:02:50.760 align:start position:0%
install a couple things first so click
 

00:02:50.760 --> 00:02:52.990 align:start position:0%
install a couple things first so click
on<00:02:50.920><c> start</c><00:02:51.159><c> web</c><00:02:51.400><c> terminal</c><00:02:52.360><c> and</c><00:02:52.480><c> then</c><00:02:52.640><c> choose</c>

00:02:52.990 --> 00:02:53.000 align:start position:0%
on start web terminal and then choose
 

00:02:53.000 --> 00:02:54.350 align:start position:0%
on start web terminal and then choose
connect<00:02:53.280><c> a</c><00:02:53.400><c> web</c><00:02:53.519><c> terminal</c><00:02:54.000><c> and</c><00:02:54.080><c> what</c><00:02:54.200><c> this</c><00:02:54.280><c> is</c>

00:02:54.350 --> 00:02:54.360 align:start position:0%
connect a web terminal and what this is
 

00:02:54.360 --> 00:02:56.190 align:start position:0%
connect a web terminal and what this is
going<00:02:54.440><c> to</c><00:02:54.519><c> do</c><00:02:54.680><c> is</c><00:02:54.840><c> give</c><00:02:54.959><c> us</c><00:02:55.440><c> the</c><00:02:55.720><c> box</c><00:02:56.000><c> that</c><00:02:56.080><c> we</c>

00:02:56.190 --> 00:02:56.200 align:start position:0%
going to do is give us the box that we
 

00:02:56.200 --> 00:02:58.390 align:start position:0%
going to do is give us the box that we
can<00:02:56.319><c> go</c><00:02:56.440><c> into</c><00:02:56.840><c> and</c><00:02:57.040><c> start</c><00:02:57.400><c> installing</c><00:02:57.840><c> things</c>

00:02:58.390 --> 00:02:58.400 align:start position:0%
can go into and start installing things
 

00:02:58.400 --> 00:02:59.350 align:start position:0%
can go into and start installing things
uh<00:02:58.480><c> if</c><00:02:58.519><c> you're</c><00:02:58.640><c> not</c><00:02:58.760><c> too</c><00:02:58.879><c> sure</c><00:02:59.040><c> about</c><00:02:59.200><c> some</c><00:02:59.280><c> of</c>

00:02:59.350 --> 00:02:59.360 align:start position:0%
uh if you're not too sure about some of
 

00:02:59.360 --> 00:03:01.589 align:start position:0%
uh if you're not too sure about some of
the<00:02:59.440><c> commands</c><00:02:59.800><c> that</c><00:02:59.920><c> we</c><00:03:00.000><c> can</c><00:03:00.159><c> use</c><00:03:00.440><c> here</c><00:03:01.040><c> uh</c><00:03:01.159><c> LS</c>

00:03:01.589 --> 00:03:01.599 align:start position:0%
the commands that we can use here uh LS
 

00:03:01.599 --> 00:03:02.990 align:start position:0%
the commands that we can use here uh LS
for<00:03:01.760><c> instance</c><00:03:02.080><c> we'll</c><00:03:02.280><c> say</c><00:03:02.519><c> is</c><00:03:02.720><c> basically</c>

00:03:02.990 --> 00:03:03.000 align:start position:0%
for instance we'll say is basically
 

00:03:03.000 --> 00:03:04.750 align:start position:0%
for instance we'll say is basically
meaning<00:03:03.280><c> list</c><00:03:03.760><c> all</c><00:03:03.879><c> of</c><00:03:04.080><c> the</c><00:03:04.319><c> files</c><00:03:04.560><c> and</c>

00:03:04.750 --> 00:03:04.760 align:start position:0%
meaning list all of the files and
 

00:03:04.760 --> 00:03:06.670 align:start position:0%
meaning list all of the files and
directories<00:03:05.480><c> we</c><00:03:05.720><c> type</c><00:03:05.879><c> in</c><00:03:05.959><c> CD</c><00:03:06.239><c> for</c><00:03:06.400><c> change</c>

00:03:06.670 --> 00:03:06.680 align:start position:0%
directories we type in CD for change
 

00:03:06.680 --> 00:03:08.630 align:start position:0%
directories we type in CD for change
directory<00:03:07.280><c> and</c><00:03:07.400><c> then</c><00:03:07.519><c> workspace</c><00:03:08.280><c> okay</c><00:03:08.440><c> great</c>

00:03:08.630 --> 00:03:08.640 align:start position:0%
directory and then workspace okay great
 

00:03:08.640 --> 00:03:10.910 align:start position:0%
directory and then workspace okay great
now<00:03:08.760><c> if</c><00:03:08.840><c> we</c><00:03:08.920><c> hit</c><00:03:09.040><c> LS</c><00:03:09.400><c> again</c><00:03:10.120><c> we</c><00:03:10.280><c> have</c><00:03:10.640><c> nothing</c>

00:03:10.910 --> 00:03:10.920 align:start position:0%
now if we hit LS again we have nothing
 

00:03:10.920 --> 00:03:13.030 align:start position:0%
now if we hit LS again we have nothing
in<00:03:11.080><c> there</c><00:03:11.440><c> right</c><00:03:11.599><c> this</c><00:03:11.720><c> is</c><00:03:11.920><c> now</c><00:03:12.239><c> empty</c><00:03:12.840><c> what</c><00:03:12.959><c> I</c>

00:03:13.030 --> 00:03:13.040 align:start position:0%
in there right this is now empty what I
 

00:03:13.040 --> 00:03:14.710 align:start position:0%
in there right this is now empty what I
want<00:03:13.159><c> to</c><00:03:13.280><c> do</c><00:03:13.599><c> because</c><00:03:13.799><c> we're</c><00:03:13.959><c> on</c><00:03:14.159><c> Linux</c><00:03:14.519><c> is</c><00:03:14.640><c> I</c>

00:03:14.710 --> 00:03:14.720 align:start position:0%
want to do because we're on Linux is I
 

00:03:14.720 --> 00:03:16.910 align:start position:0%
want to do because we're on Linux is I
want<00:03:14.840><c> to</c><00:03:14.959><c> install</c><00:03:15.280><c> oama</c><00:03:15.840><c> for</c><00:03:16.080><c> Linux</c><00:03:16.720><c> all</c><00:03:16.840><c> you</c>

00:03:16.910 --> 00:03:16.920 align:start position:0%
want to install oama for Linux all you
 

00:03:16.920 --> 00:03:18.229 align:start position:0%
want to install oama for Linux all you
can<00:03:17.040><c> do</c><00:03:17.200><c> all</c><00:03:17.280><c> you</c><00:03:17.360><c> need</c><00:03:17.480><c> to</c><00:03:17.599><c> do</c><00:03:17.760><c> is</c><00:03:17.840><c> when</c><00:03:17.959><c> you</c><00:03:18.080><c> go</c>

00:03:18.229 --> 00:03:18.239 align:start position:0%
can do all you need to do is when you go
 

00:03:18.239 --> 00:03:22.110 align:start position:0%
can do all you need to do is when you go
to<00:03:18.519><c> ol.</c><00:03:19.360><c> a</c><00:03:20.319><c> click</c><00:03:20.560><c> on</c><00:03:20.760><c> Linux</c><00:03:21.360><c> on</c><00:03:21.519><c> the</c><00:03:21.680><c> download</c>

00:03:22.110 --> 00:03:22.120 align:start position:0%
to ol. a click on Linux on the download
 

00:03:22.120 --> 00:03:23.949 align:start position:0%
to ol. a click on Linux on the download
section<00:03:22.599><c> and</c><00:03:22.760><c> then</c><00:03:22.959><c> just</c><00:03:23.200><c> copy</c><00:03:23.480><c> this</c><00:03:23.720><c> curl</c>

00:03:23.949 --> 00:03:23.959 align:start position:0%
section and then just copy this curl
 

00:03:23.959 --> 00:03:25.789 align:start position:0%
section and then just copy this curl
command<00:03:24.480><c> you'll</c><00:03:24.720><c> come</c><00:03:24.920><c> back</c><00:03:25.120><c> here</c><00:03:25.400><c> and</c><00:03:25.560><c> you'll</c>

00:03:25.789 --> 00:03:25.799 align:start position:0%
command you'll come back here and you'll
 

00:03:25.799 --> 00:03:28.869 align:start position:0%
command you'll come back here and you'll
run<00:03:26.040><c> that</c><00:03:26.280><c> and</c><00:03:26.440><c> now</c><00:03:26.640><c> we</c><00:03:26.840><c> have</c><00:03:27.400><c> oama</c><00:03:28.280><c> complete</c>

00:03:28.869 --> 00:03:28.879 align:start position:0%
run that and now we have oama complete
 

00:03:28.879 --> 00:03:30.110 align:start position:0%
run that and now we have oama complete
what<00:03:29.000><c> we</c><00:03:29.080><c> can</c><00:03:29.200><c> also</c><00:03:29.400><c> do</c><00:03:29.519><c> is</c><00:03:29.799><c> go</c><00:03:29.879><c> ahead</c><00:03:30.000><c> and</c>

00:03:30.110 --> 00:03:30.120 align:start position:0%
what we can also do is go ahead and
 

00:03:30.120 --> 00:03:33.229 align:start position:0%
what we can also do is go ahead and
install<00:03:30.560><c> Pi</c><00:03:30.799><c> autogen</c><00:03:31.319><c> so</c><00:03:31.560><c> pip</c><00:03:32.280><c> install</c><00:03:32.840><c> Pi</c>

00:03:33.229 --> 00:03:33.239 align:start position:0%
install Pi autogen so pip install Pi
 

00:03:33.239 --> 00:03:35.830 align:start position:0%
install Pi autogen so pip install Pi
autogen<00:03:34.000><c> you</c><00:03:34.120><c> can</c><00:03:34.280><c> type</c><00:03:34.480><c> in</c><00:03:34.680><c> clear</c><00:03:35.560><c> okay</c><00:03:35.720><c> now</c>

00:03:35.830 --> 00:03:35.840 align:start position:0%
autogen you can type in clear okay now
 

00:03:35.840 --> 00:03:38.309 align:start position:0%
autogen you can type in clear okay now
to<00:03:36.040><c> get</c><00:03:36.239><c> ol</c><00:03:36.840><c> running</c><00:03:37.760><c> because</c><00:03:37.959><c> it's</c><00:03:38.080><c> a</c><00:03:38.159><c> little</c>

00:03:38.309 --> 00:03:38.319 align:start position:0%
to get ol running because it's a little
 

00:03:38.319 --> 00:03:39.350 align:start position:0%
to get ol running because it's a little
different<00:03:38.560><c> if</c><00:03:38.680><c> we</c><00:03:38.760><c> haven't</c><00:03:38.920><c> done</c><00:03:39.040><c> this</c><00:03:39.159><c> before</c>

00:03:39.350 --> 00:03:39.360 align:start position:0%
different if we haven't done this before
 

00:03:39.360 --> 00:03:40.869 align:start position:0%
different if we haven't done this before
this<00:03:39.439><c> is</c><00:03:39.560><c> on</c><00:03:39.720><c> Linux</c><00:03:40.120><c> so</c><00:03:40.439><c> you're</c><00:03:40.560><c> going</c><00:03:40.680><c> to</c><00:03:40.760><c> have</c>

00:03:40.869 --> 00:03:40.879 align:start position:0%
this is on Linux so you're going to have
 

00:03:40.879 --> 00:03:43.270 align:start position:0%
this is on Linux so you're going to have
to<00:03:41.040><c> type</c><00:03:41.360><c> oama</c><00:03:42.200><c> serve</c><00:03:42.879><c> right</c><00:03:43.000><c> and</c><00:03:43.120><c> that's</c>

00:03:43.270 --> 00:03:43.280 align:start position:0%
to type oama serve right and that's
 

00:03:43.280 --> 00:03:46.070 align:start position:0%
to type oama serve right and that's
going<00:03:43.400><c> to</c><00:03:43.560><c> start</c><00:03:43.959><c> a</c><00:03:44.159><c> local</c><00:03:44.519><c> server</c><00:03:45.519><c> now</c><00:03:45.959><c> the</c>

00:03:46.070 --> 00:03:46.080 align:start position:0%
going to start a local server now the
 

00:03:46.080 --> 00:03:48.630 align:start position:0%
going to start a local server now the
thing<00:03:46.400><c> is</c><00:03:47.200><c> this</c><00:03:47.319><c> is</c><00:03:47.519><c> now</c><00:03:47.720><c> going</c><00:03:47.799><c> to</c><00:03:47.920><c> be</c><00:03:48.200><c> this</c><00:03:48.319><c> sh</c>

00:03:48.630 --> 00:03:48.640 align:start position:0%
thing is this is now going to be this sh
 

00:03:48.640 --> 00:03:50.390 align:start position:0%
thing is this is now going to be this sh
and<00:03:48.720><c> it's</c><00:03:48.840><c> just</c><00:03:48.959><c> going</c><00:03:49.040><c> to</c><00:03:49.120><c> be</c><00:03:49.239><c> running</c><00:03:49.840><c> here</c>

00:03:50.390 --> 00:03:50.400 align:start position:0%
and it's just going to be running here
 

00:03:50.400 --> 00:03:51.710 align:start position:0%
and it's just going to be running here
this<00:03:50.519><c> isn't</c><00:03:50.799><c> like</c><00:03:50.920><c> a</c><00:03:51.040><c> software</c><00:03:51.480><c> that</c><00:03:51.599><c> we</c>

00:03:51.710 --> 00:03:51.720 align:start position:0%
this isn't like a software that we
 

00:03:51.720 --> 00:03:54.429 align:start position:0%
this isn't like a software that we
downloaded<00:03:52.159><c> before</c><00:03:52.560><c> in</c><00:03:53.040><c> past</c><00:03:53.280><c> videos</c><00:03:54.159><c> this</c><00:03:54.280><c> is</c>

00:03:54.429 --> 00:03:54.439 align:start position:0%
downloaded before in past videos this is
 

00:03:54.439 --> 00:03:56.229 align:start position:0%
downloaded before in past videos this is
now<00:03:54.599><c> running</c><00:03:54.959><c> in</c><00:03:55.120><c> this</c><00:03:55.280><c> terminal</c><00:03:55.920><c> now</c><00:03:56.040><c> what</c><00:03:56.159><c> we</c>

00:03:56.229 --> 00:03:56.239 align:start position:0%
now running in this terminal now what we
 

00:03:56.239 --> 00:03:58.830 align:start position:0%
now running in this terminal now what we
need<00:03:56.360><c> to</c><00:03:56.519><c> do</c><00:03:56.799><c> then</c><00:03:57.280><c> is</c><00:03:57.400><c> you</c><00:03:57.599><c> go</c><00:03:57.959><c> back</c><00:03:58.239><c> to</c><00:03:58.519><c> my</c>

00:03:58.830 --> 00:03:58.840 align:start position:0%
need to do then is you go back to my
 

00:03:58.840 --> 00:04:01.229 align:start position:0%
need to do then is you go back to my
pods<00:03:59.400><c> you</c><00:03:59.519><c> just</c><00:03:59.720><c> just</c><00:03:59.879><c> simply</c><00:04:00.599><c> connect</c><00:04:00.920><c> to</c><00:04:01.079><c> a</c>

00:04:01.229 --> 00:04:01.239 align:start position:0%
pods you just just simply connect to a
 

00:04:01.239 --> 00:04:02.830 align:start position:0%
pods you just just simply connect to a
different<00:04:01.519><c> web</c><00:04:01.680><c> terminal</c><00:04:02.040><c> so</c><00:04:02.319><c> just</c><00:04:02.599><c> click</c>

00:04:02.830 --> 00:04:02.840 align:start position:0%
different web terminal so just click
 

00:04:02.840 --> 00:04:04.470 align:start position:0%
different web terminal so just click
this<00:04:03.000><c> connect</c><00:04:03.239><c> the</c><00:04:03.360><c> web</c><00:04:03.519><c> terminal</c><00:04:04.280><c> and</c><00:04:04.360><c> then</c>

00:04:04.470 --> 00:04:04.480 align:start position:0%
this connect the web terminal and then
 

00:04:04.480 --> 00:04:06.710 align:start position:0%
this connect the web terminal and then
you'll<00:04:04.640><c> be</c><00:04:04.879><c> put</c><00:04:05.079><c> into</c><00:04:05.480><c> another</c><00:04:06.280><c> and</c><00:04:06.439><c> then</c><00:04:06.599><c> what</c>

00:04:06.710 --> 00:04:06.720 align:start position:0%
you'll be put into another and then what
 

00:04:06.720 --> 00:04:08.789 align:start position:0%
you'll be put into another and then what
I've<00:04:06.920><c> done</c><00:04:07.200><c> here</c><00:04:07.439><c> is</c><00:04:07.680><c> I've</c><00:04:07.920><c> typed</c><00:04:08.159><c> in</c><00:04:08.280><c> oama</c>

00:04:08.789 --> 00:04:08.799 align:start position:0%
I've done here is I've typed in oama
 

00:04:08.799 --> 00:04:11.390 align:start position:0%
I've done here is I've typed in oama
pool<00:04:09.319><c> and</c><00:04:09.439><c> then</c><00:04:09.640><c> deep</c><00:04:09.959><c> seat</c><00:04:10.200><c> coder</c><00:04:10.640><c> 33</c><00:04:11.120><c> billion</c>

00:04:11.390 --> 00:04:11.400 align:start position:0%
pool and then deep seat coder 33 billion
 

00:04:11.400 --> 00:04:13.149 align:start position:0%
pool and then deep seat coder 33 billion
parameter<00:04:11.840><c> model</c><00:04:12.640><c> now</c><00:04:12.799><c> this</c><00:04:12.879><c> is</c><00:04:13.000><c> going</c><00:04:13.079><c> to</c>

00:04:13.149 --> 00:04:13.159 align:start position:0%
parameter model now this is going to
 

00:04:13.159 --> 00:04:14.630 align:start position:0%
parameter model now this is going to
take<00:04:13.280><c> a</c><00:04:13.360><c> little</c><00:04:13.519><c> bit</c><00:04:13.640><c> to</c><00:04:13.959><c> download</c><00:04:14.400><c> but</c><00:04:14.519><c> once</c>

00:04:14.630 --> 00:04:14.640 align:start position:0%
take a little bit to download but once
 

00:04:14.640 --> 00:04:16.990 align:start position:0%
take a little bit to download but once
it's<00:04:14.799><c> done</c><00:04:15.040><c> we'll</c><00:04:15.239><c> be</c><00:04:15.400><c> back</c><00:04:16.079><c> okay</c><00:04:16.400><c> it</c><00:04:16.600><c> finished</c>

00:04:16.990 --> 00:04:17.000 align:start position:0%
it's done we'll be back okay it finished
 

00:04:17.000 --> 00:04:19.069 align:start position:0%
it's done we'll be back okay it finished
and<00:04:17.120><c> we</c><00:04:17.239><c> got</c><00:04:17.440><c> this</c><00:04:17.639><c> success</c><00:04:18.079><c> here</c><00:04:18.680><c> now</c><00:04:18.880><c> what</c><00:04:19.000><c> we</c>

00:04:19.069 --> 00:04:19.079 align:start position:0%
and we got this success here now what we
 

00:04:19.079 --> 00:04:20.830 align:start position:0%
and we got this success here now what we
can<00:04:19.199><c> do</c><00:04:19.400><c> is</c><00:04:19.560><c> we</c><00:04:19.639><c> can</c><00:04:19.759><c> use</c><00:04:20.000><c> Jupiter</c><00:04:20.359><c> notebooks</c>

00:04:20.830 --> 00:04:20.840 align:start position:0%
can do is we can use Jupiter notebooks
 

00:04:20.840 --> 00:04:22.749 align:start position:0%
can do is we can use Jupiter notebooks
to<00:04:21.040><c> actually</c><00:04:21.280><c> write</c><00:04:21.479><c> the</c><00:04:21.600><c> code</c><00:04:21.799><c> and</c><00:04:21.959><c> run</c><00:04:22.160><c> it</c>

00:04:22.749 --> 00:04:22.759 align:start position:0%
to actually write the code and run it
 

00:04:22.759 --> 00:04:24.390 align:start position:0%
to actually write the code and run it
again<00:04:23.000><c> if</c><00:04:23.160><c> like</c><00:04:23.320><c> say</c><00:04:23.520><c> you</c><00:04:23.759><c> happen</c><00:04:24.000><c> to</c><00:04:24.120><c> get</c><00:04:24.280><c> out</c>

00:04:24.390 --> 00:04:24.400 align:start position:0%
again if like say you happen to get out
 

00:04:24.400 --> 00:04:26.749 align:start position:0%
again if like say you happen to get out
of<00:04:24.560><c> this</c><00:04:24.880><c> out</c><00:04:25.000><c> of</c><00:04:25.240><c> that</c><00:04:25.680><c> uh</c><00:04:26.080><c> little</c><00:04:26.360><c> dialogue</c>

00:04:26.749 --> 00:04:26.759 align:start position:0%
of this out of that uh little dialogue
 

00:04:26.759 --> 00:04:28.749 align:start position:0%
of this out of that uh little dialogue
box<00:04:27.240><c> just</c><00:04:27.440><c> click</c><00:04:27.680><c> connect</c><00:04:28.120><c> again</c><00:04:28.400><c> and</c><00:04:28.520><c> you'll</c>

00:04:28.749 --> 00:04:28.759 align:start position:0%
box just click connect again and you'll
 

00:04:28.759 --> 00:04:30.870 align:start position:0%
box just click connect again and you'll
be<00:04:28.960><c> back</c><00:04:29.160><c> here</c><00:04:29.680><c> now</c><00:04:29.880><c> just</c><00:04:30.039><c> choose</c><00:04:30.360><c> connect</c><00:04:30.720><c> to</c>

00:04:30.870 --> 00:04:30.880 align:start position:0%
be back here now just choose connect to
 

00:04:30.880 --> 00:04:32.710 align:start position:0%
be back here now just choose connect to
Jupiter<00:04:31.320><c> lab</c><00:04:32.000><c> once</c><00:04:32.160><c> this</c><00:04:32.280><c> is</c><00:04:32.400><c> opened</c><00:04:32.600><c> up</c>

00:04:32.710 --> 00:04:32.720 align:start position:0%
Jupiter lab once this is opened up
 

00:04:32.720 --> 00:04:34.110 align:start position:0%
Jupiter lab once this is opened up
you're<00:04:32.800><c> going</c><00:04:32.880><c> to</c><00:04:33.000><c> have</c><00:04:33.080><c> a</c><00:04:33.199><c> simple</c><00:04:33.560><c> IPython</c>

00:04:34.110 --> 00:04:34.120 align:start position:0%
you're going to have a simple IPython
 

00:04:34.120 --> 00:04:37.070 align:start position:0%
you're going to have a simple IPython
notebook<00:04:34.479><c> so</c><00:04:34.600><c> the</c><00:04:34.840><c> ipynb</c><00:04:35.840><c> stands</c><00:04:36.080><c> for</c><00:04:36.360><c> IPython</c>

00:04:37.070 --> 00:04:37.080 align:start position:0%
notebook so the ipynb stands for IPython
 

00:04:37.080 --> 00:04:38.950 align:start position:0%
notebook so the ipynb stands for IPython
notebook<00:04:38.080><c> it's</c><00:04:38.240><c> now</c><00:04:38.400><c> called</c><00:04:38.600><c> jupyter</c>

00:04:38.950 --> 00:04:38.960 align:start position:0%
notebook it's now called jupyter
 

00:04:38.960 --> 00:04:40.510 align:start position:0%
notebook it's now called jupyter
notebooks<00:04:39.400><c> so</c><00:04:39.600><c> they</c><00:04:39.720><c> just</c><00:04:39.880><c> kept</c><00:04:40.120><c> the</c><00:04:40.240><c> same</c>

00:04:40.510 --> 00:04:40.520 align:start position:0%
notebooks so they just kept the same
 

00:04:40.520 --> 00:04:42.670 align:start position:0%
notebooks so they just kept the same
extension<00:04:41.520><c> but</c><00:04:41.759><c> I</c><00:04:41.840><c> went</c><00:04:42.000><c> ahead</c><00:04:42.160><c> and</c><00:04:42.240><c> saved</c><00:04:42.520><c> it</c>

00:04:42.670 --> 00:04:42.680 align:start position:0%
extension but I went ahead and saved it
 

00:04:42.680 --> 00:04:46.029 align:start position:0%
extension but I went ahead and saved it
as<00:04:43.160><c> main.</c><00:04:44.160><c> ipy</c><00:04:44.600><c> notebook</c><00:04:45.240><c> then</c><00:04:45.400><c> it</c><00:04:45.600><c> copied</c><00:04:45.880><c> in</c>

00:04:46.029 --> 00:04:46.039 align:start position:0%
as main. ipy notebook then it copied in
 

00:04:46.039 --> 00:04:48.270 align:start position:0%
as main. ipy notebook then it copied in
this<00:04:46.280><c> simple</c><00:04:46.800><c> autogen</c><00:04:47.520><c> code</c><00:04:47.960><c> we</c><00:04:48.080><c> already</c>

00:04:48.270 --> 00:04:48.280 align:start position:0%
this simple autogen code we already
 

00:04:48.280 --> 00:04:49.909 align:start position:0%
this simple autogen code we already
installed<00:04:48.680><c> pyen</c><00:04:49.199><c> so</c><00:04:49.320><c> we</c><00:04:49.440><c> don't</c><00:04:49.560><c> need</c><00:04:49.680><c> to</c><00:04:49.759><c> do</c>

00:04:49.909 --> 00:04:49.919 align:start position:0%
installed pyen so we don't need to do
 

00:04:49.919 --> 00:04:51.790 align:start position:0%
installed pyen so we don't need to do
anything<00:04:50.240><c> else</c><00:04:51.000><c> the</c><00:04:51.160><c> other</c><00:04:51.360><c> thing</c><00:04:51.479><c> we</c><00:04:51.560><c> need</c><00:04:51.680><c> to</c>

00:04:51.790 --> 00:04:51.800 align:start position:0%
anything else the other thing we need to
 

00:04:51.800 --> 00:04:54.510 align:start position:0%
anything else the other thing we need to
do<00:04:52.000><c> is</c><00:04:52.199><c> create</c><00:04:52.600><c> this</c><00:04:52.840><c> oi</c><00:04:53.360><c> config</c><00:04:53.720><c> list.</c><00:04:54.080><c> Json</c>

00:04:54.510 --> 00:04:54.520 align:start position:0%
do is create this oi config list. Json
 

00:04:54.520 --> 00:04:56.070 align:start position:0%
do is create this oi config list. Json
file<00:04:55.000><c> on</c><00:04:55.080><c> the</c><00:04:55.199><c> left-</c><00:04:55.440><c> hand</c><00:04:55.560><c> side</c><00:04:55.720><c> you</c><00:04:55.800><c> can</c><00:04:55.960><c> just</c>

00:04:56.070 --> 00:04:56.080 align:start position:0%
file on the left- hand side you can just
 

00:04:56.080 --> 00:04:58.629 align:start position:0%
file on the left- hand side you can just
simply<00:04:56.440><c> right</c><00:04:56.680><c> click</c><00:04:57.120><c> choose</c><00:04:57.440><c> new</c><00:04:57.680><c> file</c><00:04:58.520><c> you</c>

00:04:58.629 --> 00:04:58.639 align:start position:0%
simply right click choose new file you
 

00:04:58.639 --> 00:05:04.350 align:start position:0%
simply right click choose new file you
can<00:04:58.880><c> type</c><00:04:59.120><c> in</c><00:04:59.880><c> oi</c><00:05:00.680><c> config</c><00:05:01.960><c> list</c><00:05:02.960><c> list.</c><00:05:03.720><c> Json</c>

00:05:04.350 --> 00:05:04.360 align:start position:0%
can type in oi config list list. Json
 

00:05:04.360 --> 00:05:05.830 align:start position:0%
can type in oi config list list. Json
you<00:05:04.560><c> click</c><00:05:04.759><c> enter</c><00:05:05.120><c> it'll</c><00:05:05.320><c> create</c><00:05:05.520><c> the</c><00:05:05.639><c> file</c>

00:05:05.830 --> 00:05:05.840 align:start position:0%
you click enter it'll create the file
 

00:05:05.840 --> 00:05:07.550 align:start position:0%
you click enter it'll create the file
for<00:05:06.039><c> you</c><00:05:06.520><c> now</c><00:05:06.680><c> whenever</c><00:05:06.919><c> you</c><00:05:07.039><c> just</c><00:05:07.199><c> double</c>

00:05:07.550 --> 00:05:07.560 align:start position:0%
for you now whenever you just double
 

00:05:07.560 --> 00:05:09.629 align:start position:0%
for you now whenever you just double
click<00:05:07.800><c> it</c><00:05:08.639><c> it's</c><00:05:08.800><c> going</c><00:05:08.919><c> to</c><00:05:09.039><c> look</c><00:05:09.280><c> weird</c>

00:05:09.629 --> 00:05:09.639 align:start position:0%
click it it's going to look weird
 

00:05:09.639 --> 00:05:11.550 align:start position:0%
click it it's going to look weird
because<00:05:09.919><c> it's</c><00:05:10.039><c> a</c><00:05:10.479><c> Json</c><00:05:10.919><c> file</c><00:05:11.120><c> so</c><00:05:11.280><c> that's</c><00:05:11.440><c> how</c>

00:05:11.550 --> 00:05:11.560 align:start position:0%
because it's a Json file so that's how
 

00:05:11.560 --> 00:05:14.110 align:start position:0%
because it's a Json file so that's how
it's<00:05:11.759><c> opening</c><00:05:12.240><c> up</c><00:05:12.680><c> so</c><00:05:12.880><c> in</c><00:05:13.000><c> order</c><00:05:13.240><c> to</c><00:05:13.560><c> open</c><00:05:13.800><c> it</c>

00:05:14.110 --> 00:05:14.120 align:start position:0%
it's opening up so in order to open it
 

00:05:14.120 --> 00:05:16.670 align:start position:0%
it's opening up so in order to open it
up<00:05:14.720><c> right</c><00:05:15.000><c> click</c><00:05:15.720><c> go</c><00:05:15.840><c> to</c><00:05:16.039><c> open</c><00:05:16.320><c> withth</c><00:05:16.600><c> and</c>

00:05:16.670 --> 00:05:16.680 align:start position:0%
up right click go to open withth and
 

00:05:16.680 --> 00:05:19.270 align:start position:0%
up right click go to open withth and
then<00:05:16.840><c> choose</c><00:05:17.160><c> editor</c><00:05:17.560><c> so</c><00:05:17.800><c> type</c><00:05:18.000><c> in</c><00:05:18.280><c> this</c><00:05:18.520><c> code</c>

00:05:19.270 --> 00:05:19.280 align:start position:0%
then choose editor so type in this code
 

00:05:19.280 --> 00:05:20.950 align:start position:0%
then choose editor so type in this code
the<00:05:19.440><c> model</c><00:05:19.759><c> that</c><00:05:19.880><c> we</c><00:05:20.000><c> need</c><00:05:20.240><c> is</c><00:05:20.360><c> the</c><00:05:20.520><c> Deep</c><00:05:20.759><c> seat</c>

00:05:20.950 --> 00:05:20.960 align:start position:0%
the model that we need is the Deep seat
 

00:05:20.960 --> 00:05:23.710 align:start position:0%
the model that we need is the Deep seat
coder<00:05:21.319><c> 33</c><00:05:21.759><c> billion</c><00:05:22.000><c> parameter</c><00:05:22.400><c> model</c><00:05:23.000><c> an</c><00:05:23.199><c> API</c>

00:05:23.710 --> 00:05:23.720 align:start position:0%
coder 33 billion parameter model an API
 

00:05:23.720 --> 00:05:25.590 align:start position:0%
coder 33 billion parameter model an API
key<00:05:24.160><c> it</c><00:05:24.440><c> doesn't</c><00:05:24.800><c> matter</c><00:05:25.160><c> because</c><00:05:25.360><c> we're</c><00:05:25.520><c> not</c>

00:05:25.590 --> 00:05:25.600 align:start position:0%
key it doesn't matter because we're not
 

00:05:25.600 --> 00:05:27.830 align:start position:0%
key it doesn't matter because we're not
using<00:05:25.919><c> open</c><00:05:26.199><c> ai's</c><00:05:26.600><c> API</c><00:05:27.319><c> and</c><00:05:27.440><c> then</c><00:05:27.560><c> for</c><00:05:27.720><c> the</c>

00:05:27.830 --> 00:05:27.840 align:start position:0%
using open ai's API and then for the
 

00:05:27.840 --> 00:05:31.150 align:start position:0%
using open ai's API and then for the
base<00:05:28.120><c> URL</c><00:05:28.840><c> this</c><00:05:28.960><c> is</c><00:05:29.080><c> the</c><00:05:29.600><c> llama</c><00:05:30.160><c> server</c><00:05:30.759><c> post</c>

00:05:31.150 --> 00:05:31.160 align:start position:0%
base URL this is the llama server post
 

00:05:31.160 --> 00:05:32.710 align:start position:0%
base URL this is the llama server post
so<00:05:31.280><c> this</c><00:05:31.360><c> is</c><00:05:31.440><c> the</c><00:05:31.520><c> base</c><00:05:31.720><c> URL</c><00:05:32.080><c> for</c><00:05:32.280><c> that</c><00:05:32.400><c> so</c><00:05:32.600><c> we</c>

00:05:32.710 --> 00:05:32.720 align:start position:0%
so this is the base URL for that so we
 

00:05:32.720 --> 00:05:34.430 align:start position:0%
so this is the base URL for that so we
can<00:05:32.840><c> connect</c><00:05:33.120><c> to</c><00:05:33.240><c> it</c><00:05:33.360><c> and</c><00:05:33.479><c> use</c><00:05:33.680><c> the</c><00:05:33.800><c> dec</c><00:05:34.160><c> coder</c>

00:05:34.430 --> 00:05:34.440 align:start position:0%
can connect to it and use the dec coder
 

00:05:34.440 --> 00:05:35.830 align:start position:0%
can connect to it and use the dec coder
model<00:05:34.919><c> let's</c><00:05:35.039><c> go</c><00:05:35.199><c> and</c><00:05:35.319><c> run</c><00:05:35.479><c> this</c><00:05:35.600><c> all</c><00:05:35.720><c> right</c>

00:05:35.830 --> 00:05:35.840 align:start position:0%
model let's go and run this all right
 

00:05:35.840 --> 00:05:37.670 align:start position:0%
model let's go and run this all right
now<00:05:35.919><c> we</c><00:05:36.000><c> have</c><00:05:36.080><c> everything</c><00:05:36.319><c> set</c><00:05:36.479><c> up</c><00:05:37.000><c> all</c><00:05:37.120><c> you</c><00:05:37.280><c> do</c>

00:05:37.670 --> 00:05:37.680 align:start position:0%
now we have everything set up all you do
 

00:05:37.680 --> 00:05:39.870 align:start position:0%
now we have everything set up all you do
is<00:05:38.120><c> Click</c><00:05:38.360><c> somewhere</c><00:05:38.600><c> in</c><00:05:38.720><c> this</c><00:05:38.840><c> cell</c><00:05:39.560><c> and</c><00:05:39.680><c> then</c>

00:05:39.870 --> 00:05:39.880 align:start position:0%
is Click somewhere in this cell and then
 

00:05:39.880 --> 00:05:41.909 align:start position:0%
is Click somewhere in this cell and then
you<00:05:39.960><c> can</c><00:05:40.120><c> choose</c><00:05:40.400><c> this</c><00:05:40.560><c> play</c><00:05:40.800><c> button</c><00:05:41.600><c> uh</c><00:05:41.720><c> so</c>

00:05:41.909 --> 00:05:41.919 align:start position:0%
you can choose this play button uh so
 

00:05:41.919 --> 00:05:43.670 align:start position:0%
you can choose this play button uh so
now<00:05:42.039><c> it's</c><00:05:42.400><c> asking</c><00:05:42.639><c> for</c><00:05:42.840><c> feedback</c><00:05:43.199><c> from</c><00:05:43.360><c> me</c><00:05:43.600><c> I'm</c>

00:05:43.670 --> 00:05:43.680 align:start position:0%
now it's asking for feedback from me I'm
 

00:05:43.680 --> 00:05:45.110 align:start position:0%
now it's asking for feedback from me I'm
just<00:05:43.759><c> going</c><00:05:43.880><c> to</c><00:05:44.000><c> hit</c><00:05:44.199><c> enter</c><00:05:44.600><c> so</c><00:05:44.759><c> that</c><00:05:44.880><c> it</c><00:05:44.960><c> ends</c>

00:05:45.110 --> 00:05:45.120 align:start position:0%
just going to hit enter so that it ends
 

00:05:45.120 --> 00:05:46.670 align:start position:0%
just going to hit enter so that it ends
up<00:05:45.319><c> like</c><00:05:45.520><c> running</c><00:05:45.759><c> the</c><00:05:45.919><c> code</c><00:05:46.199><c> so</c><00:05:46.360><c> I</c><00:05:46.400><c> don't</c><00:05:46.520><c> have</c>

00:05:46.670 --> 00:05:46.680 align:start position:0%
up like running the code so I don't have
 

00:05:46.680 --> 00:05:47.950 align:start position:0%
up like running the code so I don't have
pan<00:05:46.840><c> is</c><00:05:47.000><c> imported</c><00:05:47.400><c> so</c><00:05:47.520><c> it's</c><00:05:47.639><c> going</c><00:05:47.720><c> to</c><00:05:47.840><c> go</c>

00:05:47.950 --> 00:05:47.960 align:start position:0%
pan is imported so it's going to go
 

00:05:47.960 --> 00:05:49.390 align:start position:0%
pan is imported so it's going to go
through<00:05:48.160><c> this</c><00:05:48.280><c> whole</c><00:05:48.440><c> thing</c><00:05:48.639><c> again</c><00:05:49.120><c> it'll</c>

00:05:49.390 --> 00:05:49.400 align:start position:0%
through this whole thing again it'll
 

00:05:49.400 --> 00:05:50.870 align:start position:0%
through this whole thing again it'll
import<00:05:49.680><c> it</c><00:05:49.759><c> and</c><00:05:49.840><c> then</c><00:05:49.960><c> run</c><00:05:50.120><c> the</c><00:05:50.240><c> code</c><00:05:50.479><c> again</c>

00:05:50.870 --> 00:05:50.880 align:start position:0%
import it and then run the code again
 

00:05:50.880 --> 00:05:52.909 align:start position:0%
import it and then run the code again
well<00:05:51.120><c> finally</c><00:05:51.360><c> installed</c><00:05:51.720><c> it</c><00:05:52.080><c> and</c><00:05:52.240><c> now</c><00:05:52.440><c> in</c><00:05:52.639><c> the</c>

00:05:52.909 --> 00:05:52.919 align:start position:0%
well finally installed it and now in the
 

00:05:52.919 --> 00:05:54.870 align:start position:0%
well finally installed it and now in the
coding<00:05:53.240><c> directory</c><00:05:53.800><c> that</c><00:05:53.960><c> was</c><00:05:54.240><c> created</c>

00:05:54.870 --> 00:05:54.880 align:start position:0%
coding directory that was created
 

00:05:54.880 --> 00:05:56.430 align:start position:0%
coding directory that was created
because<00:05:55.080><c> if</c><00:05:55.160><c> we</c><00:05:55.280><c> go</c><00:05:55.360><c> to</c><00:05:55.479><c> the</c><00:05:55.600><c> very</c><00:05:55.840><c> top</c><00:05:56.199><c> we</c><00:05:56.319><c> had</c>

00:05:56.430 --> 00:05:56.440 align:start position:0%
because if we go to the very top we had
 

00:05:56.440 --> 00:05:58.469 align:start position:0%
because if we go to the very top we had
the<00:05:56.560><c> code</c><00:05:56.880><c> execution</c><00:05:57.520><c> config</c><00:05:58.199><c> where</c><00:05:58.360><c> we</c>

00:05:58.469 --> 00:05:58.479 align:start position:0%
the code execution config where we
 

00:05:58.479 --> 00:06:00.029 align:start position:0%
the code execution config where we
created<00:05:58.800><c> a</c><00:05:58.919><c> working</c><00:05:59.199><c> Direct</c><00:05:59.440><c> directory</c><00:05:59.960><c> and</c>

00:06:00.029 --> 00:06:00.039 align:start position:0%
created a working Direct directory and
 

00:06:00.039 --> 00:06:03.029 align:start position:0%
created a working Direct directory and
it<00:06:00.160><c> was</c><00:06:00.360><c> called</c><00:06:00.639><c> coding</c><00:06:01.319><c> you</c><00:06:01.520><c> can</c><00:06:01.960><c> also</c><00:06:02.759><c> on</c><00:06:02.880><c> the</c>

00:06:03.029 --> 00:06:03.039 align:start position:0%
it was called coding you can also on the
 

00:06:03.039 --> 00:06:05.070 align:start position:0%
it was called coding you can also on the
left<00:06:03.240><c> hand</c><00:06:03.360><c> side</c><00:06:03.600><c> here</c><00:06:03.880><c> create</c><00:06:04.240><c> a</c><00:06:04.479><c> python</c><00:06:04.840><c> file</c>

00:06:05.070 --> 00:06:05.080 align:start position:0%
left hand side here create a python file
 

00:06:05.080 --> 00:06:07.309 align:start position:0%
left hand side here create a python file
so<00:06:05.199><c> you</c><00:06:05.280><c> can</c><00:06:05.479><c> right</c><00:06:05.680><c> click</c><00:06:06.280><c> go</c><00:06:06.400><c> to</c><00:06:06.639><c> new</c><00:06:06.919><c> file</c>

00:06:07.309 --> 00:06:07.319 align:start position:0%
so you can right click go to new file
 

00:06:07.319 --> 00:06:09.350 align:start position:0%
so you can right click go to new file
and<00:06:07.560><c> then</c><00:06:07.800><c> type</c><00:06:08.000><c> in</c><00:06:08.160><c> main.py</c><00:06:09.000><c> or</c><00:06:09.080><c> whatever</c><00:06:09.280><c> you</c>

00:06:09.350 --> 00:06:09.360 align:start position:0%
and then type in main.py or whatever you
 

00:06:09.360 --> 00:06:11.189 align:start position:0%
and then type in main.py or whatever you
want<00:06:09.440><c> to</c><00:06:09.560><c> name</c><00:06:09.720><c> it</c><00:06:10.199><c> we</c><00:06:10.280><c> can</c><00:06:10.479><c> paste</c><00:06:10.680><c> the</c><00:06:10.800><c> code</c><00:06:11.000><c> in</c>

00:06:11.189 --> 00:06:11.199 align:start position:0%
want to name it we can paste the code in
 

00:06:11.199 --> 00:06:12.670 align:start position:0%
want to name it we can paste the code in
here<00:06:11.560><c> and</c><00:06:11.639><c> then</c><00:06:11.800><c> go</c><00:06:12.039><c> back</c><00:06:12.199><c> to</c><00:06:12.319><c> one</c><00:06:12.440><c> of</c><00:06:12.520><c> our</c>

00:06:12.670 --> 00:06:12.680 align:start position:0%
here and then go back to one of our
 

00:06:12.680 --> 00:06:14.870 align:start position:0%
here and then go back to one of our
Terminals<00:06:13.680><c> and</c><00:06:13.800><c> then</c><00:06:13.960><c> once</c><00:06:14.120><c> we're</c><00:06:14.440><c> here</c><00:06:14.759><c> we</c>

00:06:14.870 --> 00:06:14.880 align:start position:0%
Terminals and then once we're here we
 

00:06:14.880 --> 00:06:17.749 align:start position:0%
Terminals and then once we're here we
can<00:06:15.199><c> see</c><00:06:15.680><c> the</c><00:06:15.840><c> main.py</c><00:06:16.759><c> when</c><00:06:16.880><c> you</c><00:06:17.000><c> type</c><00:06:17.199><c> in</c><00:06:17.360><c> LS</c>

00:06:17.749 --> 00:06:17.759 align:start position:0%
can see the main.py when you type in LS
 

00:06:17.759 --> 00:06:19.189 align:start position:0%
can see the main.py when you type in LS
again<00:06:17.919><c> this</c><00:06:18.039><c> is</c><00:06:18.240><c> listing</c><00:06:18.599><c> all</c><00:06:18.759><c> the</c><00:06:18.840><c> files</c><00:06:19.080><c> and</c>

00:06:19.189 --> 00:06:19.199 align:start position:0%
again this is listing all the files and
 

00:06:19.199 --> 00:06:22.150 align:start position:0%
again this is listing all the files and
directories<00:06:19.880><c> you</c><00:06:20.000><c> can</c><00:06:20.120><c> now</c><00:06:20.280><c> say</c><00:06:20.680><c> Python</c><00:06:21.120><c> 3</c>

00:06:22.150 --> 00:06:22.160 align:start position:0%
directories you can now say Python 3
 

00:06:22.160 --> 00:06:24.870 align:start position:0%
directories you can now say Python 3
main.py<00:06:23.160><c> and</c><00:06:23.280><c> it's</c><00:06:23.479><c> going</c><00:06:23.599><c> to</c><00:06:23.800><c> run</c><00:06:24.400><c> it's</c><00:06:24.759><c> it's</c>

00:06:24.870 --> 00:06:24.880 align:start position:0%
main.py and it's going to run it's it's
 

00:06:24.880 --> 00:06:26.629 align:start position:0%
main.py and it's going to run it's it's
going<00:06:24.960><c> to</c><00:06:25.080><c> run</c><00:06:25.240><c> it</c><00:06:25.440><c> here</c><00:06:25.680><c> for</c><00:06:25.880><c> you</c><00:06:26.160><c> as</c><00:06:26.319><c> well</c>

00:06:26.629 --> 00:06:26.639 align:start position:0%
going to run it here for you as well
 

00:06:26.639 --> 00:06:28.270 align:start position:0%
going to run it here for you as well
this<00:06:26.759><c> was</c><00:06:26.919><c> a</c><00:06:27.039><c> quick</c><00:06:27.240><c> tutorial</c><00:06:27.720><c> to</c><00:06:27.880><c> get</c><00:06:28.000><c> used</c><00:06:28.120><c> to</c>

00:06:28.270 --> 00:06:28.280 align:start position:0%
this was a quick tutorial to get used to
 

00:06:28.280 --> 00:06:30.790 align:start position:0%
this was a quick tutorial to get used to
runpod<00:06:28.840><c> doio</c><00:06:29.479><c> so</c><00:06:29.639><c> you</c><00:06:29.720><c> can</c><00:06:29.800><c> use</c><00:06:30.120><c> bigger</c><00:06:30.400><c> models</c>

00:06:30.790 --> 00:06:30.800 align:start position:0%
runpod doio so you can use bigger models
 

00:06:30.800 --> 00:06:32.670 align:start position:0%
runpod doio so you can use bigger models
if<00:06:30.919><c> your</c><00:06:31.080><c> local</c><00:06:31.440><c> hardware</c><00:06:32.000><c> can't</c><00:06:32.280><c> support</c>

00:06:32.670 --> 00:06:32.680 align:start position:0%
if your local hardware can't support
 

00:06:32.680 --> 00:06:34.830 align:start position:0%
if your local hardware can't support
them<00:06:33.000><c> while</c><00:06:33.160><c> we're</c><00:06:33.280><c> at</c><00:06:33.440><c> day</c><00:06:33.520><c> 8</c><00:06:33.759><c> of</c><00:06:33.919><c> 31</c><00:06:34.560><c> let</c><00:06:34.720><c> me</c>

00:06:34.830 --> 00:06:34.840 align:start position:0%
them while we're at day 8 of 31 let me
 

00:06:34.840 --> 00:06:36.110 align:start position:0%
them while we're at day 8 of 31 let me
know<00:06:34.960><c> in</c><00:06:35.080><c> the</c><00:06:35.199><c> comments</c><00:06:35.560><c> if</c><00:06:35.639><c> you</c><00:06:35.759><c> have</c><00:06:35.880><c> any</c>

00:06:36.110 --> 00:06:36.120 align:start position:0%
know in the comments if you have any
 

00:06:36.120 --> 00:06:38.110 align:start position:0%
know in the comments if you have any
questions<00:06:36.680><c> concerns</c><00:06:37.479><c> what</c><00:06:37.599><c> you</c><00:06:37.720><c> like</c><00:06:37.919><c> didn't</c>

00:06:38.110 --> 00:06:38.120 align:start position:0%
questions concerns what you like didn't
 

00:06:38.120 --> 00:06:39.749 align:start position:0%
questions concerns what you like didn't
like<00:06:38.319><c> let</c><00:06:38.440><c> me</c><00:06:38.560><c> know</c><00:06:39.000><c> ask</c><00:06:39.160><c> some</c><00:06:39.319><c> more</c><00:06:39.479><c> videos</c>

00:06:39.749 --> 00:06:39.759 align:start position:0%
like let me know ask some more videos
 

00:06:39.759 --> 00:06:42.270 align:start position:0%
like let me know ask some more videos
about<00:06:39.960><c> autogen</c><00:06:40.479><c> and</c><00:06:40.720><c> AI</c><00:06:41.400><c> like</c><00:06:41.560><c> And</c><00:06:41.720><c> subscribe</c>

00:06:42.270 --> 00:06:42.280 align:start position:0%
about autogen and AI like And subscribe
 

00:06:42.280 --> 00:06:45.360 align:start position:0%
about autogen and AI like And subscribe
I'll<00:06:42.400><c> see</c><00:06:42.560><c> you</c><00:06:42.720><c> next</c><00:06:42.919><c> video</c>

