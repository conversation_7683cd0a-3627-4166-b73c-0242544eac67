WEBVTT
Kind: captions
Language: en

00:00:01.199 --> 00:00:03.470 align:start position:0%
 
right<00:00:01.860><c> hello</c><00:00:02.280><c> and</c><00:00:02.580><c> welcome</c><00:00:02.820><c> to</c><00:00:03.179><c> the</c><00:00:03.300><c> next</c>

00:00:03.470 --> 00:00:03.480 align:start position:0%
right hello and welcome to the next
 

00:00:03.480 --> 00:00:05.690 align:start position:0%
right hello and welcome to the next
installment<00:00:04.080><c> of</c><00:00:04.440><c> the</c><00:00:04.680><c> Discover</c><00:00:04.799><c> llama</c><00:00:05.279><c> index</c>

00:00:05.690 --> 00:00:05.700 align:start position:0%
installment of the Discover llama index
 

00:00:05.700 --> 00:00:07.970 align:start position:0%
installment of the Discover llama index
series<00:00:05.940><c> here</c><00:00:06.720><c> on</c><00:00:06.960><c> the</c><00:00:07.080><c> Llama</c><00:00:07.259><c> index</c><00:00:07.740><c> YouTube</c>

00:00:07.970 --> 00:00:07.980 align:start position:0%
series here on the Llama index YouTube
 

00:00:07.980 --> 00:00:10.730 align:start position:0%
series here on the Llama index YouTube
channel<00:00:08.280><c> my</c><00:00:09.120><c> name</c><00:00:09.300><c> is</c><00:00:09.360><c> sarb</c><00:00:09.780><c> and</c><00:00:10.139><c> today</c><00:00:10.559><c> I'll</c>

00:00:10.730 --> 00:00:10.740 align:start position:0%
channel my name is sarb and today I'll
 

00:00:10.740 --> 00:00:12.470 align:start position:0%
channel my name is sarb and today I'll
be<00:00:10.920><c> talking</c><00:00:11.099><c> to</c><00:00:11.400><c> you</c><00:00:11.519><c> about</c><00:00:11.820><c> the</c><00:00:12.179><c> newly</c>

00:00:12.470 --> 00:00:12.480 align:start position:0%
be talking to you about the newly
 

00:00:12.480 --> 00:00:16.129 align:start position:0%
be talking to you about the newly
introduced<00:00:12.900><c> Json</c><00:00:13.320><c> query</c><00:00:13.740><c> engine</c>

00:00:16.129 --> 00:00:16.139 align:start position:0%
 
 

00:00:16.139 --> 00:00:18.170 align:start position:0%
 
so<00:00:16.560><c> what</c><00:00:16.800><c> are</c><00:00:16.920><c> we</c><00:00:17.039><c> solving</c><00:00:17.340><c> for</c>

00:00:18.170 --> 00:00:18.180 align:start position:0%
so what are we solving for
 

00:00:18.180 --> 00:00:20.450 align:start position:0%
so what are we solving for
Json<00:00:18.660><c> as</c><00:00:19.199><c> you</c><00:00:19.320><c> may</c><00:00:19.440><c> know</c><00:00:19.619><c> is</c><00:00:19.920><c> a</c><00:00:20.100><c> very</c><00:00:20.279><c> popular</c>

00:00:20.450 --> 00:00:20.460 align:start position:0%
Json as you may know is a very popular
 

00:00:20.460 --> 00:00:22.670 align:start position:0%
Json as you may know is a very popular
data<00:00:20.939><c> format</c><00:00:21.240><c> but</c><00:00:21.720><c> it</c><00:00:21.960><c> can</c><00:00:22.080><c> be</c><00:00:22.260><c> quite</c><00:00:22.380><c> long</c>

00:00:22.670 --> 00:00:22.680 align:start position:0%
data format but it can be quite long
 

00:00:22.680 --> 00:00:25.189 align:start position:0%
data format but it can be quite long
which<00:00:23.279><c> means</c><00:00:23.640><c> that</c><00:00:23.820><c> the</c><00:00:24.359><c> current</c><00:00:24.539><c> approaches</c>

00:00:25.189 --> 00:00:25.199 align:start position:0%
which means that the current approaches
 

00:00:25.199 --> 00:00:27.230 align:start position:0%
which means that the current approaches
for<00:00:25.619><c> querying</c><00:00:26.160><c> over</c><00:00:26.279><c> that</c><00:00:26.519><c> data</c><00:00:26.760><c> within</c><00:00:27.119><c> the</c>

00:00:27.230 --> 00:00:27.240 align:start position:0%
for querying over that data within the
 

00:00:27.240 --> 00:00:30.109 align:start position:0%
for querying over that data within the
context<00:00:27.539><c> of</c><00:00:27.720><c> a</c><00:00:27.960><c> lot</c><00:00:28.140><c> of</c><00:00:28.199><c> an</c><00:00:28.560><c> llm</c><00:00:28.980><c> can</c><00:00:29.640><c> leave</c><00:00:29.880><c> a</c>

00:00:30.109 --> 00:00:30.119 align:start position:0%
context of a lot of an llm can leave a
 

00:00:30.119 --> 00:00:32.030 align:start position:0%
context of a lot of an llm can leave a
lot<00:00:30.240><c> to</c><00:00:30.420><c> be</c><00:00:30.539><c> desired</c><00:00:31.080><c> for</c>

00:00:32.030 --> 00:00:32.040 align:start position:0%
lot to be desired for
 

00:00:32.040 --> 00:00:34.549 align:start position:0%
lot to be desired for
however<00:00:32.759><c> we</c><00:00:32.940><c> know</c><00:00:33.059><c> that</c><00:00:33.180><c> Json</c><00:00:33.480><c> has</c><00:00:33.840><c> a</c><00:00:34.140><c> inherent</c>

00:00:34.549 --> 00:00:34.559 align:start position:0%
however we know that Json has a inherent
 

00:00:34.559 --> 00:00:36.650 align:start position:0%
however we know that Json has a inherent
fee<00:00:34.920><c> structure</c><00:00:35.219><c> so</c><00:00:35.460><c> that</c><00:00:35.760><c> leads</c><00:00:36.000><c> us</c><00:00:36.120><c> to</c><00:00:36.300><c> ask</c><00:00:36.480><c> a</c>

00:00:36.650 --> 00:00:36.660 align:start position:0%
fee structure so that leads us to ask a
 

00:00:36.660 --> 00:00:38.810 align:start position:0%
fee structure so that leads us to ask a
question<00:00:36.860><c> what</c><00:00:37.860><c> if</c><00:00:38.040><c> we</c><00:00:38.160><c> could</c><00:00:38.340><c> show</c><00:00:38.520><c> our</c><00:00:38.640><c> model</c>

00:00:38.810 --> 00:00:38.820 align:start position:0%
question what if we could show our model
 

00:00:38.820 --> 00:00:40.549 align:start position:0%
question what if we could show our model
how<00:00:39.180><c> this</c><00:00:39.300><c> tree</c><00:00:39.540><c> is</c><00:00:39.719><c> structured</c><00:00:40.079><c> in</c><00:00:40.260><c> order</c><00:00:40.440><c> to</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
how this tree is structured in order to
 

00:00:40.559 --> 00:00:43.190 align:start position:0%
how this tree is structured in order to
extract<00:00:40.860><c> information</c><00:00:41.100><c> from</c><00:00:41.579><c> it</c>

00:00:43.190 --> 00:00:43.200 align:start position:0%
extract information from it
 

00:00:43.200 --> 00:00:44.990 align:start position:0%
extract information from it
this<00:00:43.620><c> is</c><00:00:43.800><c> where</c><00:00:44.040><c> we</c><00:00:44.280><c> can</c><00:00:44.399><c> make</c><00:00:44.520><c> use</c><00:00:44.760><c> of</c>

00:00:44.990 --> 00:00:45.000 align:start position:0%
this is where we can make use of
 

00:00:45.000 --> 00:00:46.910 align:start position:0%
this is where we can make use of
something<00:00:45.180><c> like</c><00:00:45.420><c> Json</c><00:00:45.719><c> schema</c>

00:00:46.910 --> 00:00:46.920 align:start position:0%
something like Json schema
 

00:00:46.920 --> 00:00:49.369 align:start position:0%
something like Json schema
uh<00:00:47.399><c> Jason</c><00:00:47.579><c> schema</c><00:00:48.180><c> is</c><00:00:48.420><c> a</c><00:00:48.660><c> standardized</c><00:00:49.260><c> and</c>

00:00:49.369 --> 00:00:49.379 align:start position:0%
uh Jason schema is a standardized and
 

00:00:49.379 --> 00:00:51.950 align:start position:0%
uh Jason schema is a standardized and
popular<00:00:49.559><c> way</c><00:00:49.920><c> of</c><00:00:50.160><c> describing</c><00:00:50.579><c> Json</c><00:00:51.000><c> structure</c>

00:00:51.950 --> 00:00:51.960 align:start position:0%
popular way of describing Json structure
 

00:00:51.960 --> 00:00:54.770 align:start position:0%
popular way of describing Json structure
because<00:00:52.500><c> Jason</c><00:00:53.160><c> schema</c><00:00:53.879><c> has</c><00:00:54.180><c> been</c><00:00:54.300><c> around</c><00:00:54.420><c> for</c>

00:00:54.770 --> 00:00:54.780 align:start position:0%
because Jason schema has been around for
 

00:00:54.780 --> 00:00:58.189 align:start position:0%
because Jason schema has been around for
so<00:00:54.899><c> long</c><00:00:55.079><c> and</c><00:00:55.620><c> is</c><00:00:56.219><c> quite</c><00:00:56.520><c> popular</c><00:00:56.820><c> many</c><00:00:57.600><c> LNS</c>

00:00:58.189 --> 00:00:58.199 align:start position:0%
so long and is quite popular many LNS
 

00:00:58.199 --> 00:01:01.010 align:start position:0%
so long and is quite popular many LNS
already<00:00:58.320><c> understand</c><00:00:58.620><c> this</c><00:00:59.340><c> Json</c><00:00:59.640><c> schema</c><00:01:00.120><c> spec</c>

00:01:01.010 --> 00:01:01.020 align:start position:0%
already understand this Json schema spec
 

00:01:01.020 --> 00:01:03.709 align:start position:0%
already understand this Json schema spec
uh<00:01:01.860><c> it's</c><00:01:02.039><c> also</c><00:01:02.340><c> very</c><00:01:02.460><c> easy</c><00:01:02.640><c> to</c><00:01:02.760><c> create</c><00:01:03.000><c> one</c><00:01:03.239><c> you</c>

00:01:03.709 --> 00:01:03.719 align:start position:0%
uh it's also very easy to create one you
 

00:01:03.719 --> 00:01:06.770 align:start position:0%
uh it's also very easy to create one you
also<00:01:04.019><c> you</c><00:01:04.320><c> may</c><00:01:04.739><c> already</c><00:01:04.979><c> have</c><00:01:05.460><c> a</c><00:01:05.939><c> Json</c><00:01:06.240><c> schema</c>

00:01:06.770 --> 00:01:06.780 align:start position:0%
also you may already have a Json schema
 

00:01:06.780 --> 00:01:09.109 align:start position:0%
also you may already have a Json schema
to<00:01:07.020><c> describe</c><00:01:07.380><c> your</c><00:01:07.500><c> data</c><00:01:07.920><c> or</c><00:01:08.700><c> you</c><00:01:08.880><c> might</c><00:01:09.000><c> be</c>

00:01:09.109 --> 00:01:09.119 align:start position:0%
to describe your data or you might be
 

00:01:09.119 --> 00:01:10.789 align:start position:0%
to describe your data or you might be
able<00:01:09.240><c> to</c><00:01:09.360><c> create</c><00:01:09.479><c> one</c><00:01:09.720><c> by</c><00:01:09.960><c> hand</c><00:01:10.140><c> or</c><00:01:10.560><c> you</c><00:01:10.680><c> might</c>

00:01:10.789 --> 00:01:10.799 align:start position:0%
able to create one by hand or you might
 

00:01:10.799 --> 00:01:15.109 align:start position:0%
able to create one by hand or you might
be<00:01:10.920><c> able</c><00:01:11.100><c> to</c><00:01:11.220><c> make</c><00:01:12.060><c> use</c><00:01:12.240><c> of</c><00:01:13.100><c> an</c><00:01:14.100><c> llm</c><00:01:14.520><c> chatbot</c>

00:01:15.109 --> 00:01:15.119 align:start position:0%
be able to make use of an llm chatbot
 

00:01:15.119 --> 00:01:17.990 align:start position:0%
be able to make use of an llm chatbot
like<00:01:15.360><c> chat</c><00:01:15.659><c> TPT</c><00:01:16.080><c> to</c><00:01:16.619><c> generate</c><00:01:16.979><c> a</c><00:01:17.220><c> Json</c><00:01:17.460><c> schema</c>

00:01:17.990 --> 00:01:18.000 align:start position:0%
like chat TPT to generate a Json schema
 

00:01:18.000 --> 00:01:20.390 align:start position:0%
like chat TPT to generate a Json schema
for<00:01:18.180><c> you</c><00:01:18.360><c> after</c><00:01:19.140><c> having</c><00:01:19.380><c> given</c><00:01:19.860><c> it</c><00:01:19.979><c> some</c>

00:01:20.390 --> 00:01:20.400 align:start position:0%
for you after having given it some
 

00:01:20.400 --> 00:01:22.789 align:start position:0%
for you after having given it some
sample<00:01:20.759><c> Json</c><00:01:21.240><c> that</c><00:01:21.720><c> conforms</c><00:01:22.140><c> to</c><00:01:22.259><c> that</c><00:01:22.439><c> Json</c>

00:01:22.789 --> 00:01:22.799 align:start position:0%
sample Json that conforms to that Json
 

00:01:22.799 --> 00:01:26.210 align:start position:0%
sample Json that conforms to that Json
schema

00:01:26.210 --> 00:01:26.220 align:start position:0%
 
 

00:01:26.220 --> 00:01:28.670 align:start position:0%
 
so<00:01:26.939><c> let's</c><00:01:27.240><c> actually</c><00:01:27.540><c> go</c><00:01:27.780><c> over</c><00:01:27.900><c> how</c><00:01:28.140><c> our</c><00:01:28.380><c> Json</c>

00:01:28.670 --> 00:01:28.680 align:start position:0%
so let's actually go over how our Json
 

00:01:28.680 --> 00:01:31.789 align:start position:0%
so let's actually go over how our Json
query<00:01:29.040><c> engine</c><00:01:29.280><c> works</c><00:01:29.880><c> at</c><00:01:30.240><c> a</c><00:01:30.360><c> high</c><00:01:30.479><c> level</c>

00:01:31.789 --> 00:01:31.799 align:start position:0%
query engine works at a high level
 

00:01:31.799 --> 00:01:33.830 align:start position:0%
query engine works at a high level
um<00:01:31.920><c> so</c><00:01:32.460><c> you</c><00:01:32.640><c> see</c><00:01:32.820><c> that</c><00:01:33.060><c> we're</c><00:01:33.240><c> giving</c><00:01:33.360><c> the</c><00:01:33.600><c> Json</c>

00:01:33.830 --> 00:01:33.840 align:start position:0%
um so you see that we're giving the Json
 

00:01:33.840 --> 00:01:36.050 align:start position:0%
um so you see that we're giving the Json
query<00:01:34.259><c> engine</c><00:01:34.439><c> here</c><00:01:34.680><c> three</c><00:01:34.920><c> inputs</c><00:01:35.460><c> we're</c>

00:01:36.050 --> 00:01:36.060 align:start position:0%
query engine here three inputs we're
 

00:01:36.060 --> 00:01:37.789 align:start position:0%
query engine here three inputs we're
giving<00:01:36.240><c> it</c><00:01:36.420><c> a</c><00:01:36.540><c> Json</c><00:01:36.720><c> value</c><00:01:37.079><c> which</c><00:01:37.439><c> is</c><00:01:37.560><c> simply</c>

00:01:37.789 --> 00:01:37.799 align:start position:0%
giving it a Json value which is simply
 

00:01:37.799 --> 00:01:39.410 align:start position:0%
giving it a Json value which is simply
the<00:01:37.979><c> Json</c><00:01:38.280><c> data</c><00:01:38.700><c> that</c><00:01:38.880><c> you</c><00:01:39.000><c> want</c><00:01:39.119><c> to</c><00:01:39.240><c> be</c><00:01:39.299><c> able</c>

00:01:39.410 --> 00:01:39.420 align:start position:0%
the Json data that you want to be able
 

00:01:39.420 --> 00:01:40.910 align:start position:0%
the Json data that you want to be able
to<00:01:39.540><c> ask</c><00:01:39.659><c> questions</c><00:01:39.900><c> about</c>

00:01:40.910 --> 00:01:40.920 align:start position:0%
to ask questions about
 

00:01:40.920 --> 00:01:42.890 align:start position:0%
to ask questions about
and<00:01:41.220><c> then</c><00:01:41.340><c> we're</c><00:01:41.460><c> also</c><00:01:41.700><c> giving</c><00:01:41.820><c> a</c><00:01:42.000><c> Json</c><00:01:42.240><c> schema</c>

00:01:42.890 --> 00:01:42.900 align:start position:0%
and then we're also giving a Json schema
 

00:01:42.900 --> 00:01:45.830 align:start position:0%
and then we're also giving a Json schema
which<00:01:43.380><c> is</c><00:01:43.619><c> simply</c><00:01:44.159><c> describes</c><00:01:44.700><c> the</c><00:01:45.240><c> tree</c>

00:01:45.830 --> 00:01:45.840 align:start position:0%
which is simply describes the tree
 

00:01:45.840 --> 00:01:48.830 align:start position:0%
which is simply describes the tree
structure<00:01:46.320><c> that</c><00:01:46.860><c> the</c><00:01:47.100><c> Json</c><00:01:47.340><c> value</c><00:01:47.840><c> conforms</c>

00:01:48.830 --> 00:01:48.840 align:start position:0%
structure that the Json value conforms
 

00:01:48.840 --> 00:01:49.730 align:start position:0%
structure that the Json value conforms
to

00:01:49.730 --> 00:01:49.740 align:start position:0%
to
 

00:01:49.740 --> 00:01:52.010 align:start position:0%
to
uh<00:01:50.460><c> and</c><00:01:50.759><c> then</c><00:01:50.820><c> we're</c><00:01:51.000><c> also</c><00:01:51.240><c> giving</c><00:01:51.360><c> the</c><00:01:51.720><c> user</c>

00:01:52.010 --> 00:01:52.020 align:start position:0%
uh and then we're also giving the user
 

00:01:52.020 --> 00:01:53.690 align:start position:0%
uh and then we're also giving the user
question<00:01:52.200><c> which</c><00:01:52.560><c> is</c><00:01:52.680><c> just</c><00:01:52.920><c> what</c><00:01:53.159><c> the</c><00:01:53.280><c> end</c><00:01:53.399><c> user</c>

00:01:53.690 --> 00:01:53.700 align:start position:0%
question which is just what the end user
 

00:01:53.700 --> 00:01:57.109 align:start position:0%
question which is just what the end user
wants<00:01:54.000><c> to</c><00:01:54.060><c> know</c><00:01:54.180><c> about</c><00:01:54.540><c> the</c><00:01:54.780><c> Json</c><00:01:55.020><c> data</c><00:01:56.040><c> uh</c><00:01:56.939><c> our</c>

00:01:57.109 --> 00:01:57.119 align:start position:0%
wants to know about the Json data uh our
 

00:01:57.119 --> 00:01:58.969 align:start position:0%
wants to know about the Json data uh our
Json<00:01:57.420><c> query</c><00:01:57.780><c> engine</c><00:01:58.079><c> then</c><00:01:58.439><c> takes</c><00:01:58.740><c> these</c>

00:01:58.969 --> 00:01:58.979 align:start position:0%
Json query engine then takes these
 

00:01:58.979 --> 00:02:01.789 align:start position:0%
Json query engine then takes these
inputs<00:01:59.340><c> and</c><00:02:00.060><c> then</c><00:02:00.180><c> outputs</c><00:02:00.780><c> a</c><00:02:01.079><c> Json</c><00:02:01.380><c> path</c>

00:02:01.789 --> 00:02:01.799 align:start position:0%
inputs and then outputs a Json path
 

00:02:01.799 --> 00:02:04.789 align:start position:0%
inputs and then outputs a Json path
query<00:02:02.100><c> a</c><00:02:02.759><c> Json</c><00:02:03.180><c> path</c><00:02:03.540><c> is</c><00:02:03.840><c> essentially</c><00:02:04.259><c> a</c>

00:02:04.789 --> 00:02:04.799 align:start position:0%
query a Json path is essentially a
 

00:02:04.799 --> 00:02:06.709 align:start position:0%
query a Json path is essentially a
structured<00:02:05.280><c> and</c><00:02:05.460><c> well-defined</c><00:02:06.119><c> query</c>

00:02:06.709 --> 00:02:06.719 align:start position:0%
structured and well-defined query
 

00:02:06.719 --> 00:02:09.770 align:start position:0%
structured and well-defined query
language<00:02:07.020><c> to</c><00:02:07.799><c> run</c><00:02:08.039><c> and</c><00:02:08.640><c> execute</c><00:02:09.119><c> queries</c><00:02:09.539><c> over</c>

00:02:09.770 --> 00:02:09.780 align:start position:0%
language to run and execute queries over
 

00:02:09.780 --> 00:02:14.930 align:start position:0%
language to run and execute queries over
Json<00:02:10.440><c> data</c><00:02:11.599><c> since</c><00:02:12.599><c> we</c><00:02:12.900><c> get</c><00:02:13.319><c> that</c><00:02:13.680><c> query</c><00:02:14.400><c> that</c>

00:02:14.930 --> 00:02:14.940 align:start position:0%
Json data since we get that query that
 

00:02:14.940 --> 00:02:17.390 align:start position:0%
Json data since we get that query that
we<00:02:15.060><c> can</c><00:02:15.180><c> execute</c><00:02:15.540><c> on</c><00:02:15.780><c> Json</c><00:02:16.140><c> values</c><00:02:16.680><c> from</c><00:02:17.280><c> the</c>

00:02:17.390 --> 00:02:17.400 align:start position:0%
we can execute on Json values from the
 

00:02:17.400 --> 00:02:19.369 align:start position:0%
we can execute on Json values from the
output<00:02:17.640><c> of</c><00:02:17.819><c> this</c><00:02:17.940><c> of</c><00:02:18.360><c> our</c><00:02:18.599><c> engine</c><00:02:18.780><c> internally</c>

00:02:19.369 --> 00:02:19.379 align:start position:0%
output of this of our engine internally
 

00:02:19.379 --> 00:02:22.490 align:start position:0%
output of this of our engine internally
we<00:02:20.340><c> can</c><00:02:20.459><c> then</c><00:02:20.640><c> execute</c><00:02:21.300><c> it</c><00:02:21.420><c> on</c><00:02:21.720><c> our</c><00:02:21.900><c> Json</c><00:02:22.140><c> value</c>

00:02:22.490 --> 00:02:22.500 align:start position:0%
we can then execute it on our Json value
 

00:02:22.500 --> 00:02:25.670 align:start position:0%
we can then execute it on our Json value
to<00:02:23.220><c> get</c><00:02:23.459><c> a</c><00:02:23.760><c> specific</c><00:02:24.180><c> subset</c><00:02:24.599><c> of</c><00:02:24.780><c> data</c><00:02:25.200><c> that</c>

00:02:25.670 --> 00:02:25.680 align:start position:0%
to get a specific subset of data that
 

00:02:25.680 --> 00:02:27.670 align:start position:0%
to get a specific subset of data that
answers<00:02:26.040><c> the</c><00:02:26.340><c> user's</c><00:02:26.760><c> question</c>

00:02:27.670 --> 00:02:27.680 align:start position:0%
answers the user's question
 

00:02:27.680 --> 00:02:30.650 align:start position:0%
answers the user's question
we<00:02:28.680><c> then</c><00:02:28.920><c> take</c><00:02:29.400><c> that</c><00:02:29.580><c> subset</c><00:02:29.879><c> of</c><00:02:30.060><c> data</c><00:02:30.360><c> to</c><00:02:30.480><c> then</c>

00:02:30.650 --> 00:02:30.660 align:start position:0%
we then take that subset of data to then
 

00:02:30.660 --> 00:02:32.809 align:start position:0%
we then take that subset of data to then
synthesize<00:02:31.200><c> the</c><00:02:31.379><c> response</c><00:02:31.860><c> and</c><00:02:32.400><c> get</c><00:02:32.580><c> an</c>

00:02:32.809 --> 00:02:32.819 align:start position:0%
synthesize the response and get an
 

00:02:32.819 --> 00:02:35.330 align:start position:0%
synthesize the response and get an
extracted<00:02:33.239><c> answer</c><00:02:33.480><c> for</c><00:02:33.840><c> the</c><00:02:34.020><c> user's</c><00:02:34.440><c> question</c>

00:02:35.330 --> 00:02:35.340 align:start position:0%
extracted answer for the user's question
 

00:02:35.340 --> 00:02:37.430 align:start position:0%
extracted answer for the user's question
so<00:02:35.700><c> what</c><00:02:35.940><c> does</c><00:02:36.060><c> this</c><00:02:36.180><c> mean</c><00:02:36.300><c> in</c><00:02:36.420><c> practice</c>

00:02:37.430 --> 00:02:37.440 align:start position:0%
so what does this mean in practice
 

00:02:37.440 --> 00:02:39.410 align:start position:0%
so what does this mean in practice
let's<00:02:37.980><c> actually</c><00:02:38.160><c> see</c><00:02:38.459><c> some</c><00:02:38.640><c> of</c><00:02:38.760><c> the</c><00:02:38.819><c> code</c><00:02:39.000><c> and</c>

00:02:39.410 --> 00:02:39.420 align:start position:0%
let's actually see some of the code and
 

00:02:39.420 --> 00:02:41.449 align:start position:0%
let's actually see some of the code and
see<00:02:39.599><c> how</c><00:02:39.720><c> it's</c><00:02:39.900><c> done</c>

00:02:41.449 --> 00:02:41.459 align:start position:0%
see how it's done
 

00:02:41.459 --> 00:02:44.570 align:start position:0%
see how it's done
so<00:02:42.000><c> here</c><00:02:42.300><c> I</c><00:02:42.480><c> have</c><00:02:42.660><c> a</c><00:02:42.840><c> really</c><00:02:42.959><c> basic</c><00:02:43.920><c> python</c>

00:02:44.570 --> 00:02:44.580 align:start position:0%
so here I have a really basic python
 

00:02:44.580 --> 00:02:46.369 align:start position:0%
so here I have a really basic python
notebook<00:02:44.940><c> where</c><00:02:45.360><c> I've</c><00:02:45.480><c> set</c><00:02:45.660><c> up</c><00:02:45.780><c> some</c><00:02:45.900><c> code</c><00:02:46.140><c> for</c>

00:02:46.369 --> 00:02:46.379 align:start position:0%
notebook where I've set up some code for
 

00:02:46.379 --> 00:02:48.710 align:start position:0%
notebook where I've set up some code for
us<00:02:46.560><c> to</c><00:02:47.280><c> go</c><00:02:47.400><c> over</c>

00:02:48.710 --> 00:02:48.720 align:start position:0%
us to go over
 

00:02:48.720 --> 00:02:51.770 align:start position:0%
us to go over
um start<00:02:49.440><c> off</c><00:02:49.680><c> just</c><00:02:50.099><c> some</c><00:02:50.459><c> basic</c><00:02:50.879><c> code</c><00:02:51.420><c> setup</c>

00:02:51.770 --> 00:02:51.780 align:start position:0%
um start off just some basic code setup
 

00:02:51.780 --> 00:02:54.770 align:start position:0%
um start off just some basic code setup
we're<00:02:51.959><c> reading</c><00:02:52.319><c> in</c><00:02:52.500><c> our</c><00:02:52.680><c> Json</c><00:02:52.980><c> data</c><00:02:53.940><c> and</c><00:02:54.540><c> our</c>

00:02:54.770 --> 00:02:54.780 align:start position:0%
we're reading in our Json data and our
 

00:02:54.780 --> 00:02:57.350 align:start position:0%
we're reading in our Json data and our
Json<00:02:55.080><c> schema</c><00:02:55.680><c> let's</c><00:02:56.519><c> take</c><00:02:56.700><c> a</c><00:02:56.819><c> look</c><00:02:56.879><c> at</c><00:02:57.060><c> some</c><00:02:57.239><c> of</c>

00:02:57.350 --> 00:02:57.360 align:start position:0%
Json schema let's take a look at some of
 

00:02:57.360 --> 00:02:58.850 align:start position:0%
Json schema let's take a look at some of
the<00:02:57.480><c> sample</c><00:02:57.780><c> data</c><00:02:58.080><c> that</c><00:02:58.200><c> we're</c><00:02:58.440><c> going</c><00:02:58.680><c> to</c><00:02:58.800><c> be</c>

00:02:58.850 --> 00:02:58.860 align:start position:0%
the sample data that we're going to be
 

00:02:58.860 --> 00:03:00.650 align:start position:0%
the sample data that we're going to be
playing<00:02:59.040><c> with</c><00:02:59.220><c> today</c><00:02:59.400><c> this</c><00:03:00.300><c> is</c><00:03:00.420><c> just</c><00:03:00.540><c> supposed</c>

00:03:00.650 --> 00:03:00.660 align:start position:0%
playing with today this is just supposed
 

00:03:00.660 --> 00:03:03.470 align:start position:0%
playing with today this is just supposed
to<00:03:00.840><c> emulate</c><00:03:01.260><c> the</c><00:03:01.440><c> data</c><00:03:01.739><c> for</c><00:03:02.040><c> a</c><00:03:02.400><c> basic</c><00:03:02.940><c> blog</c>

00:03:03.470 --> 00:03:03.480 align:start position:0%
to emulate the data for a basic blog
 

00:03:03.480 --> 00:03:05.570 align:start position:0%
to emulate the data for a basic blog
posting<00:03:03.840><c> website</c><00:03:04.319><c> where</c><00:03:04.980><c> we</c><00:03:05.160><c> have</c><00:03:05.280><c> a</c><00:03:05.459><c> series</c>

00:03:05.570 --> 00:03:05.580 align:start position:0%
posting website where we have a series
 

00:03:05.580 --> 00:03:07.309 align:start position:0%
posting website where we have a series
of<00:03:05.819><c> blog</c><00:03:06.060><c> posts</c><00:03:06.300><c> they</c><00:03:06.480><c> have</c><00:03:06.660><c> a</c><00:03:06.780><c> title</c><00:03:07.019><c> and</c><00:03:07.200><c> a</c>

00:03:07.309 --> 00:03:07.319 align:start position:0%
of blog posts they have a title and a
 

00:03:07.319 --> 00:03:09.290 align:start position:0%
of blog posts they have a title and a
content<00:03:07.680><c> and</c><00:03:08.220><c> we</c><00:03:08.340><c> have</c><00:03:08.459><c> a</c><00:03:08.580><c> series</c><00:03:08.700><c> of</c><00:03:08.940><c> comments</c>

00:03:09.290 --> 00:03:09.300 align:start position:0%
content and we have a series of comments
 

00:03:09.300 --> 00:03:11.570 align:start position:0%
content and we have a series of comments
with<00:03:09.540><c> things</c><00:03:09.840><c> like</c><00:03:10.140><c> the</c><00:03:10.560><c> common</c><00:03:10.860><c> content</c><00:03:11.340><c> a</c>

00:03:11.570 --> 00:03:11.580 align:start position:0%
with things like the common content a
 

00:03:11.580 --> 00:03:13.550 align:start position:0%
with things like the common content a
username<00:03:12.060><c> and</c><00:03:12.180><c> a</c><00:03:12.360><c> blog</c><00:03:12.540><c> post</c><00:03:12.659><c> ID</c><00:03:12.900><c> to</c><00:03:13.200><c> relate</c>

00:03:13.550 --> 00:03:13.560 align:start position:0%
username and a blog post ID to relate
 

00:03:13.560 --> 00:03:15.890 align:start position:0%
username and a blog post ID to relate
each<00:03:13.800><c> comment</c><00:03:14.220><c> to</c><00:03:14.879><c> the</c><00:03:15.120><c> blog</c><00:03:15.420><c> post</c><00:03:15.540><c> that</c><00:03:15.780><c> it's</c>

00:03:15.890 --> 00:03:15.900 align:start position:0%
each comment to the blog post that it's
 

00:03:15.900 --> 00:03:17.930 align:start position:0%
each comment to the blog post that it's
common<00:03:16.140><c> for</c>

00:03:17.930 --> 00:03:17.940 align:start position:0%
common for
 

00:03:17.940 --> 00:03:20.149 align:start position:0%
common for
um

00:03:20.149 --> 00:03:20.159 align:start position:0%
 
 

00:03:20.159 --> 00:03:22.850 align:start position:0%
 
now<00:03:20.700><c> that</c><00:03:20.879><c> we</c><00:03:21.060><c> have</c><00:03:21.180><c> our</c><00:03:21.480><c> Json</c><00:03:21.840><c> data</c><00:03:22.500><c> and</c><00:03:22.680><c> our</c>

00:03:22.850 --> 00:03:22.860 align:start position:0%
now that we have our Json data and our
 

00:03:22.860 --> 00:03:25.369 align:start position:0%
now that we have our Json data and our
Json<00:03:23.159><c> schema</c><00:03:23.819><c> we</c><00:03:24.300><c> can</c><00:03:24.480><c> construct</c><00:03:24.900><c> our</c><00:03:25.080><c> query</c>

00:03:25.369 --> 00:03:25.379 align:start position:0%
Json schema we can construct our query
 

00:03:25.379 --> 00:03:27.229 align:start position:0%
Json schema we can construct our query
engine<00:03:25.620><c> by</c><00:03:26.040><c> calling</c><00:03:26.340><c> the</c><00:03:26.519><c> Constructor</c><00:03:26.819><c> with</c>

00:03:27.229 --> 00:03:27.239 align:start position:0%
engine by calling the Constructor with
 

00:03:27.239 --> 00:03:29.270 align:start position:0%
engine by calling the Constructor with
those<00:03:27.480><c> Json</c><00:03:27.900><c> value</c><00:03:28.260><c> and</c><00:03:28.500><c> json's</c><00:03:28.980><c> schema</c>

00:03:29.270 --> 00:03:29.280 align:start position:0%
those Json value and json's schema
 

00:03:29.280 --> 00:03:31.309 align:start position:0%
those Json value and json's schema
parameters<00:03:29.700><c> and</c><00:03:30.360><c> also</c><00:03:30.599><c> passing</c><00:03:30.900><c> in</c><00:03:31.019><c> a</c><00:03:31.200><c> service</c>

00:03:31.309 --> 00:03:31.319 align:start position:0%
parameters and also passing in a service
 

00:03:31.319 --> 00:03:34.550 align:start position:0%
parameters and also passing in a service
context<00:03:31.739><c> so</c><00:03:31.980><c> that</c><00:03:32.220><c> we</c><00:03:32.459><c> can</c><00:03:32.640><c> specify</c><00:03:33.540><c> which</c><00:03:34.019><c> LM</c>

00:03:34.550 --> 00:03:34.560 align:start position:0%
context so that we can specify which LM
 

00:03:34.560 --> 00:03:36.350 align:start position:0%
context so that we can specify which LM
model<00:03:34.800><c> we're</c><00:03:35.040><c> going</c><00:03:35.220><c> to</c><00:03:35.220><c> be</c><00:03:35.340><c> using</c><00:03:35.580><c> which</c>

00:03:36.350 --> 00:03:36.360 align:start position:0%
model we're going to be using which
 

00:03:36.360 --> 00:03:38.570 align:start position:0%
model we're going to be using which
today<00:03:36.540><c> is</c><00:03:36.780><c> going</c><00:03:36.959><c> to</c><00:03:37.019><c> be</c><00:03:37.080><c> opening</c><00:03:37.560><c> eyes</c><00:03:37.739><c> gbt</c>

00:03:38.570 --> 00:03:38.580 align:start position:0%
today is going to be opening eyes gbt
 

00:03:38.580 --> 00:03:40.750 align:start position:0%
today is going to be opening eyes gbt
3.5<00:03:39.000><c> Turbo</c>

00:03:40.750 --> 00:03:40.760 align:start position:0%
3.5 Turbo
 

00:03:40.760 --> 00:03:43.670 align:start position:0%
3.5 Turbo
once<00:03:41.760><c> we've</c><00:03:41.940><c> constructed</c><00:03:42.480><c> that</c><00:03:42.659><c> we</c><00:03:43.200><c> now</c><00:03:43.500><c> are</c>

00:03:43.670 --> 00:03:43.680 align:start position:0%
once we've constructed that we now are
 

00:03:43.680 --> 00:03:46.009 align:start position:0%
once we've constructed that we now are
ready<00:03:43.860><c> to</c><00:03:44.040><c> start</c><00:03:44.159><c> answering</c><00:03:45.019><c> asking</c>

00:03:46.009 --> 00:03:46.019 align:start position:0%
ready to start answering asking
 

00:03:46.019 --> 00:03:49.430 align:start position:0%
ready to start answering asking
questions<00:03:46.200><c> about</c><00:03:46.680><c> our</c><00:03:47.159><c> Json</c><00:03:47.819><c> data</c><00:03:48.319><c> so</c><00:03:49.319><c> the</c>

00:03:49.430 --> 00:03:49.440 align:start position:0%
questions about our Json data so the
 

00:03:49.440 --> 00:03:50.869 align:start position:0%
questions about our Json data so the
first<00:03:49.560><c> question</c><00:03:49.739><c> I've</c><00:03:49.980><c> asked</c><00:03:50.280><c> here</c><00:03:50.340><c> is</c><00:03:50.640><c> how</c>

00:03:50.869 --> 00:03:50.879 align:start position:0%
first question I've asked here is how
 

00:03:50.879 --> 00:03:52.610 align:start position:0%
first question I've asked here is how
many<00:03:51.000><c> comments</c><00:03:51.420><c> are</c><00:03:51.599><c> there</c><00:03:51.659><c> on</c><00:03:51.900><c> blog</c><00:03:52.200><c> post</c><00:03:52.319><c> two</c>

00:03:52.610 --> 00:03:52.620 align:start position:0%
many comments are there on blog post two
 

00:03:52.620 --> 00:03:54.949 align:start position:0%
many comments are there on blog post two
and<00:03:53.280><c> we</c><00:03:53.459><c> get</c><00:03:53.640><c> a</c><00:03:53.760><c> response</c><00:03:54.180><c> back</c><00:03:54.299><c> saying</c><00:03:54.720><c> there</c>

00:03:54.949 --> 00:03:54.959 align:start position:0%
and we get a response back saying there
 

00:03:54.959 --> 00:03:57.830 align:start position:0%
and we get a response back saying there
are<00:03:55.080><c> two</c><00:03:55.260><c> comments</c><00:03:55.739><c> on</c><00:03:55.920><c> blog</c><00:03:56.220><c> posts</c><00:03:56.580><c> too</c><00:03:57.239><c> we</c>

00:03:57.830 --> 00:03:57.840 align:start position:0%
are two comments on blog posts too we
 

00:03:57.840 --> 00:03:59.390 align:start position:0%
are two comments on blog posts too we
just<00:03:58.080><c> take</c><00:03:58.260><c> a</c><00:03:58.319><c> look</c><00:03:58.440><c> at</c><00:03:58.560><c> our</c><00:03:58.680><c> Json</c><00:03:58.980><c> real</c><00:03:59.280><c> quick</c>

00:03:59.390 --> 00:03:59.400 align:start position:0%
just take a look at our Json real quick
 

00:03:59.400 --> 00:04:00.970 align:start position:0%
just take a look at our Json real quick
we<00:03:59.640><c> can</c><00:03:59.760><c> see</c><00:03:59.879><c> that</c><00:04:00.120><c> that</c><00:04:00.299><c> is</c><00:04:00.420><c> in</c><00:04:00.540><c> fact</c><00:04:00.720><c> true</c>

00:04:00.970 --> 00:04:00.980 align:start position:0%
we can see that that is in fact true
 

00:04:00.980 --> 00:04:03.350 align:start position:0%
we can see that that is in fact true
there<00:04:01.980><c> are</c><00:04:02.159><c> only</c><00:04:02.519><c> two</c><00:04:02.700><c> comments</c><00:04:03.120><c> in</c><00:04:03.239><c> this</c>

00:04:03.350 --> 00:04:03.360 align:start position:0%
there are only two comments in this
 

00:04:03.360 --> 00:04:05.570 align:start position:0%
there are only two comments in this
entire<00:04:03.599><c> comments</c><00:04:04.319><c> array</c><00:04:04.560><c> with</c><00:04:04.739><c> blog</c><00:04:05.099><c> post</c><00:04:05.220><c> ID</c>

00:04:05.570 --> 00:04:05.580 align:start position:0%
entire comments array with blog post ID
 

00:04:05.580 --> 00:04:06.890 align:start position:0%
entire comments array with blog post ID
of<00:04:06.060><c> two</c>

00:04:06.890 --> 00:04:06.900 align:start position:0%
of two
 

00:04:06.900 --> 00:04:08.390 align:start position:0%
of two
so<00:04:07.140><c> we'll</c><00:04:07.379><c> set</c><00:04:07.560><c> you</c><00:04:07.739><c> ask</c><00:04:07.920><c> a</c><00:04:08.159><c> different</c>

00:04:08.390 --> 00:04:08.400 align:start position:0%
so we'll set you ask a different
 

00:04:08.400 --> 00:04:10.130 align:start position:0%
so we'll set you ask a different
question<00:04:08.700><c> now</c><00:04:09.480><c> um</c>

00:04:10.130 --> 00:04:10.140 align:start position:0%
question now um
 

00:04:10.140 --> 00:04:11.690 align:start position:0%
question now um
instead<00:04:10.620><c> of</c><00:04:10.680><c> asking</c><00:04:10.920><c> about</c><00:04:11.040><c> how</c><00:04:11.280><c> it</c><00:04:11.400><c> comes</c>

00:04:11.690 --> 00:04:11.700 align:start position:0%
instead of asking about how it comes
 

00:04:11.700 --> 00:04:14.449 align:start position:0%
instead of asking about how it comes
there<00:04:11.879><c> are</c><00:04:12.060><c> on</c><00:04:12.360><c> blog</c><00:04:12.720><c> post</c><00:04:12.840><c> two</c><00:04:13.200><c> let's</c><00:04:14.099><c> ask</c>

00:04:14.449 --> 00:04:14.459 align:start position:0%
there are on blog post two let's ask
 

00:04:14.459 --> 00:04:17.509 align:start position:0%
there are on blog post two let's ask
what<00:04:15.299><c> has</c><00:04:15.599><c> Jerry</c><00:04:15.900><c> been</c><00:04:16.199><c> commenting</c>

00:04:17.509 --> 00:04:17.519 align:start position:0%
what has Jerry been commenting
 

00:04:17.519 --> 00:04:19.490 align:start position:0%
what has Jerry been commenting
and<00:04:18.000><c> let's</c><00:04:18.120><c> run</c><00:04:18.299><c> that</c><00:04:18.540><c> it'll</c><00:04:19.019><c> take</c><00:04:19.199><c> a</c><00:04:19.380><c> few</c>

00:04:19.490 --> 00:04:19.500 align:start position:0%
and let's run that it'll take a few
 

00:04:19.500 --> 00:04:20.569 align:start position:0%
and let's run that it'll take a few
seconds

00:04:20.569 --> 00:04:20.579 align:start position:0%
seconds
 

00:04:20.579 --> 00:04:21.650 align:start position:0%
seconds
um

00:04:21.650 --> 00:04:21.660 align:start position:0%
um
 

00:04:21.660 --> 00:04:24.110 align:start position:0%
um
but<00:04:22.139><c> now</c><00:04:22.380><c> we</c><00:04:22.620><c> see</c><00:04:22.800><c> uh</c><00:04:23.280><c> we</c><00:04:23.460><c> get</c><00:04:23.639><c> a</c><00:04:23.699><c> response</c><00:04:24.000><c> back</c>

00:04:24.110 --> 00:04:24.120 align:start position:0%
but now we see uh we get a response back
 

00:04:24.120 --> 00:04:26.090 align:start position:0%
but now we see uh we get a response back
saying<00:04:24.540><c> Jerry</c><00:04:24.780><c> has</c><00:04:25.020><c> been</c><00:04:25.139><c> commenting</c><00:04:25.620><c> nice</c>

00:04:26.090 --> 00:04:26.100 align:start position:0%
saying Jerry has been commenting nice
 

00:04:26.100 --> 00:04:27.110 align:start position:0%
saying Jerry has been commenting nice
posts

00:04:27.110 --> 00:04:27.120 align:start position:0%
posts
 

00:04:27.120 --> 00:04:29.810 align:start position:0%
posts
taking<00:04:27.600><c> a</c><00:04:27.780><c> look</c><00:04:27.900><c> at</c><00:04:28.020><c> the</c><00:04:28.199><c> sample</c><00:04:28.620><c> data.json</c><00:04:29.460><c> we</c>

00:04:29.810 --> 00:04:29.820 align:start position:0%
taking a look at the sample data.json we
 

00:04:29.820 --> 00:04:31.909 align:start position:0%
taking a look at the sample data.json we
can<00:04:29.940><c> see</c><00:04:30.060><c> grin</c><00:04:30.660><c> did</c><00:04:30.900><c> in</c><00:04:31.080><c> fact</c><00:04:31.259><c> have</c><00:04:31.440><c> a</c><00:04:31.620><c> comment</c>

00:04:31.909 --> 00:04:31.919 align:start position:0%
can see grin did in fact have a comment
 

00:04:31.919 --> 00:04:34.370 align:start position:0%
can see grin did in fact have a comment
there<00:04:32.400><c> uh</c><00:04:33.180><c> saying</c><00:04:33.419><c> nice</c><00:04:33.600><c> posts</c><00:04:33.960><c> and</c><00:04:34.080><c> that</c><00:04:34.259><c> was</c>

00:04:34.370 --> 00:04:34.380 align:start position:0%
there uh saying nice posts and that was
 

00:04:34.380 --> 00:04:37.430 align:start position:0%
there uh saying nice posts and that was
his<00:04:34.500><c> only</c><00:04:34.680><c> comments</c><00:04:35.160><c> in</c><00:04:35.340><c> this</c><00:04:35.520><c> comments</c><00:04:35.880><c> array</c>

00:04:37.430 --> 00:04:37.440 align:start position:0%
his only comments in this comments array
 

00:04:37.440 --> 00:04:38.090 align:start position:0%
his only comments in this comments array
um

00:04:38.090 --> 00:04:38.100 align:start position:0%
um
 

00:04:38.100 --> 00:04:40.969 align:start position:0%
um
let's<00:04:38.580><c> take</c><00:04:38.699><c> a</c><00:04:38.820><c> look</c><00:04:38.940><c> at</c><00:04:39.240><c> the</c><00:04:39.720><c> uh</c><00:04:40.320><c> do</c><00:04:40.500><c> some</c><00:04:40.800><c> path</c>

00:04:40.969 --> 00:04:40.979 align:start position:0%
let's take a look at the uh do some path
 

00:04:40.979 --> 00:04:42.830 align:start position:0%
let's take a look at the uh do some path
query<00:04:41.220><c> that</c><00:04:41.400><c> was</c><00:04:41.520><c> output</c><00:04:41.820><c> and</c><00:04:42.300><c> we</c><00:04:42.540><c> can</c><00:04:42.660><c> see</c>

00:04:42.830 --> 00:04:42.840 align:start position:0%
query that was output and we can see
 

00:04:42.840 --> 00:04:44.390 align:start position:0%
query that was output and we can see
here<00:04:43.080><c> that</c><00:04:43.440><c> we're</c><00:04:43.680><c> actually</c><00:04:43.860><c> when</c><00:04:44.220><c> we're</c>

00:04:44.390 --> 00:04:44.400 align:start position:0%
here that we're actually when we're
 

00:04:44.400 --> 00:04:46.490 align:start position:0%
here that we're actually when we're
actually<00:04:44.580><c> filtering</c><00:04:45.180><c> for</c><00:04:45.300><c> a</c><00:04:45.419><c> username</c><00:04:45.780><c> we're</c>

00:04:46.490 --> 00:04:46.500 align:start position:0%
actually filtering for a username we're
 

00:04:46.500 --> 00:04:49.490 align:start position:0%
actually filtering for a username we're
filtering<00:04:46.979><c> by</c><00:04:47.400><c> Jerry</c><00:04:48.180><c> with</c><00:04:48.479><c> a</c><00:04:48.720><c> lowercase</c><00:04:49.139><c> j</c>

00:04:49.490 --> 00:04:49.500 align:start position:0%
filtering by Jerry with a lowercase j
 

00:04:49.500 --> 00:04:52.969 align:start position:0%
filtering by Jerry with a lowercase j
even<00:04:50.400><c> though</c><00:04:50.639><c> the</c><00:04:51.000><c> question</c><00:04:51.240><c> had</c><00:04:51.900><c> Jerry</c><00:04:52.500><c> with</c>

00:04:52.969 --> 00:04:52.979 align:start position:0%
even though the question had Jerry with
 

00:04:52.979 --> 00:04:55.490 align:start position:0%
even though the question had Jerry with
a<00:04:53.340><c> uppercase</c><00:04:53.820><c> j</c>

00:04:55.490 --> 00:04:55.500 align:start position:0%
a uppercase j
 

00:04:55.500 --> 00:04:57.890 align:start position:0%
a uppercase j
um and<00:04:55.800><c> that</c><00:04:55.979><c> brings</c><00:04:56.160><c> up</c><00:04:56.340><c> a</c><00:04:56.520><c> point</c><00:04:56.699><c> where</c><00:04:57.360><c> it's</c>

00:04:57.890 --> 00:04:57.900 align:start position:0%
um and that brings up a point where it's
 

00:04:57.900 --> 00:05:00.230 align:start position:0%
um and that brings up a point where it's
where<00:04:58.500><c> uh</c><00:04:58.979><c> I'd</c><00:04:59.160><c> like</c><00:04:59.280><c> to</c><00:04:59.460><c> stress</c><00:04:59.699><c> the</c>

00:05:00.230 --> 00:05:00.240 align:start position:0%
where uh I'd like to stress the
 

00:05:00.240 --> 00:05:02.810 align:start position:0%
where uh I'd like to stress the
importance<00:05:00.720><c> of</c><00:05:01.259><c> the</c><00:05:01.800><c> descriptions</c><00:05:02.280><c> in</c><00:05:02.699><c> your</c>

00:05:02.810 --> 00:05:02.820 align:start position:0%
importance of the descriptions in your
 

00:05:02.820 --> 00:05:05.570 align:start position:0%
importance of the descriptions in your
Json<00:05:03.120><c> schema</c><00:05:03.720><c> so</c><00:05:04.440><c> taking</c><00:05:04.680><c> a</c><00:05:04.860><c> look</c><00:05:04.979><c> at</c><00:05:05.040><c> our</c><00:05:05.220><c> Json</c>

00:05:05.570 --> 00:05:05.580 align:start position:0%
Json schema so taking a look at our Json
 

00:05:05.580 --> 00:05:08.570 align:start position:0%
Json schema so taking a look at our Json
schema<00:05:06.060><c> now</c><00:05:06.240><c> we</c><00:05:06.900><c> can</c><00:05:07.020><c> see</c><00:05:07.320><c> uh</c><00:05:07.860><c> you</c><00:05:08.100><c> know</c><00:05:08.220><c> we're</c>

00:05:08.570 --> 00:05:08.580 align:start position:0%
schema now we can see uh you know we're
 

00:05:08.580 --> 00:05:11.030 align:start position:0%
schema now we can see uh you know we're
defining<00:05:09.060><c> the</c><00:05:09.479><c> various</c><00:05:09.780><c> properties</c><00:05:10.139><c> on</c><00:05:10.740><c> our</c>

00:05:11.030 --> 00:05:11.040 align:start position:0%
defining the various properties on our
 

00:05:11.040 --> 00:05:14.030 align:start position:0%
defining the various properties on our
Json<00:05:11.940><c> structure</c><00:05:12.540><c> uh</c><00:05:13.259><c> we're</c><00:05:13.440><c> looking</c><00:05:13.560><c> at</c><00:05:13.919><c> the</c>

00:05:14.030 --> 00:05:14.040 align:start position:0%
Json structure uh we're looking at the
 

00:05:14.040 --> 00:05:15.710 align:start position:0%
Json structure uh we're looking at the
common<00:05:14.220><c> structure</c><00:05:14.699><c> and</c><00:05:15.000><c> then</c><00:05:15.120><c> the</c><00:05:15.360><c> username</c>

00:05:15.710 --> 00:05:15.720 align:start position:0%
common structure and then the username
 

00:05:15.720 --> 00:05:17.870 align:start position:0%
common structure and then the username
structure<00:05:16.199><c> itself</c><00:05:16.560><c> and</c><00:05:17.160><c> we</c><00:05:17.340><c> can</c><00:05:17.460><c> see</c><00:05:17.520><c> that</c><00:05:17.699><c> we</c>

00:05:17.870 --> 00:05:17.880 align:start position:0%
structure itself and we can see that we
 

00:05:17.880 --> 00:05:19.670 align:start position:0%
structure itself and we can see that we
can<00:05:18.000><c> provide</c><00:05:18.419><c> a</c><00:05:18.660><c> description</c><00:05:18.960><c> for</c><00:05:19.320><c> each</c><00:05:19.560><c> of</c>

00:05:19.670 --> 00:05:19.680 align:start position:0%
can provide a description for each of
 

00:05:19.680 --> 00:05:22.610 align:start position:0%
can provide a description for each of
the<00:05:19.800><c> attributes</c><00:05:20.220><c> in</c><00:05:20.460><c> our</c><00:05:20.580><c> Json</c><00:05:21.380><c> so</c><00:05:22.380><c> the</c>

00:05:22.610 --> 00:05:22.620 align:start position:0%
the attributes in our Json so the
 

00:05:22.620 --> 00:05:24.770 align:start position:0%
the attributes in our Json so the
description<00:05:22.979><c> for</c><00:05:23.400><c> our</c><00:05:23.699><c> username</c><00:05:24.180><c> attribute</c>

00:05:24.770 --> 00:05:24.780 align:start position:0%
description for our username attribute
 

00:05:24.780 --> 00:05:26.529 align:start position:0%
description for our username attribute
says<00:05:25.139><c> username</c><00:05:25.620><c> of</c><00:05:25.919><c> the</c><00:05:26.039><c> commenter</c>

00:05:26.529 --> 00:05:26.539 align:start position:0%
says username of the commenter
 

00:05:26.539 --> 00:05:29.870 align:start position:0%
says username of the commenter
lowercased<00:05:27.539><c> so</c><00:05:28.259><c> by</c><00:05:28.500><c> feeding</c><00:05:28.919><c> in</c><00:05:29.160><c> this</c>

00:05:29.870 --> 00:05:29.880 align:start position:0%
lowercased so by feeding in this
 

00:05:29.880 --> 00:05:32.390 align:start position:0%
lowercased so by feeding in this
information<00:05:30.120><c> this</c><00:05:30.780><c> description</c><00:05:31.139><c> about</c><00:05:32.100><c> the</c>

00:05:32.390 --> 00:05:32.400 align:start position:0%
information this description about the
 

00:05:32.400 --> 00:05:34.129 align:start position:0%
information this description about the
username<00:05:32.759><c> saying</c><00:05:33.180><c> that</c><00:05:33.300><c> all</c><00:05:33.479><c> usernames</c><00:05:34.020><c> are</c>

00:05:34.129 --> 00:05:34.139 align:start position:0%
username saying that all usernames are
 

00:05:34.139 --> 00:05:36.890 align:start position:0%
username saying that all usernames are
lowercased<00:05:34.740><c> our</c><00:05:35.340><c> LM</c><00:05:35.820><c> model</c><00:05:36.000><c> will</c><00:05:36.419><c> know</c><00:05:36.600><c> that</c>

00:05:36.890 --> 00:05:36.900 align:start position:0%
lowercased our LM model will know that
 

00:05:36.900 --> 00:05:39.170 align:start position:0%
lowercased our LM model will know that
anytime<00:05:37.440><c> that</c><00:05:37.680><c> we're</c><00:05:37.919><c> doing</c><00:05:38.580><c> a</c><00:05:38.820><c> comparison</c>

00:05:39.170 --> 00:05:39.180 align:start position:0%
anytime that we're doing a comparison
 

00:05:39.180 --> 00:05:41.270 align:start position:0%
anytime that we're doing a comparison
against<00:05:39.600><c> the</c><00:05:39.960><c> username</c><00:05:40.320><c> attribute</c><00:05:40.800><c> inside</c><00:05:41.039><c> of</c>

00:05:41.270 --> 00:05:41.280 align:start position:0%
against the username attribute inside of
 

00:05:41.280 --> 00:05:42.189 align:start position:0%
against the username attribute inside of
our<00:05:41.400><c> Json</c>

00:05:42.189 --> 00:05:42.199 align:start position:0%
our Json
 

00:05:42.199 --> 00:05:45.890 align:start position:0%
our Json
that<00:05:43.199><c> value</c><00:05:43.440><c> needs</c><00:05:43.979><c> to</c><00:05:44.100><c> be</c><00:05:44.160><c> a</c><00:05:44.820><c> a</c><00:05:45.419><c> lowercase</c>

00:05:45.890 --> 00:05:45.900 align:start position:0%
that value needs to be a a lowercase
 

00:05:45.900 --> 00:05:49.310 align:start position:0%
that value needs to be a a lowercase
value<00:05:46.340><c> for</c><00:05:47.340><c> it</c><00:05:47.460><c> to</c><00:05:47.699><c> get</c><00:05:48.419><c> any</c><00:05:48.600><c> matches</c><00:05:49.080><c> against</c>

00:05:49.310 --> 00:05:49.320 align:start position:0%
value for it to get any matches against
 

00:05:49.320 --> 00:05:52.670 align:start position:0%
value for it to get any matches against
that<00:05:49.560><c> username</c><00:05:50.120><c> attribute</c><00:05:51.259><c> so</c><00:05:52.259><c> just</c><00:05:52.500><c> to</c>

00:05:52.670 --> 00:05:52.680 align:start position:0%
that username attribute so just to
 

00:05:52.680 --> 00:05:54.710 align:start position:0%
that username attribute so just to
stress<00:05:52.919><c> again</c><00:05:53.160><c> that</c><00:05:53.580><c> it's</c><00:05:54.180><c> really</c><00:05:54.360><c> important</c>

00:05:54.710 --> 00:05:54.720 align:start position:0%
stress again that it's really important
 

00:05:54.720 --> 00:05:56.930 align:start position:0%
stress again that it's really important
that<00:05:55.139><c> your</c><00:05:55.320><c> Json</c><00:05:55.560><c> schema</c><00:05:56.039><c> is</c><00:05:56.220><c> as</c><00:05:56.400><c> descriptive</c>

00:05:56.930 --> 00:05:56.940 align:start position:0%
that your Json schema is as descriptive
 

00:05:56.940 --> 00:05:59.390 align:start position:0%
that your Json schema is as descriptive
as<00:05:57.479><c> it</c><00:05:57.780><c> can</c><00:05:57.960><c> be</c>

00:05:59.390 --> 00:05:59.400 align:start position:0%
as it can be
 

00:05:59.400 --> 00:06:02.270 align:start position:0%
as it can be
so<00:06:00.120><c> to</c><00:06:00.539><c> wrap</c><00:06:00.960><c> things</c><00:06:01.080><c> up</c><00:06:01.320><c> uh</c><00:06:01.800><c> I'd</c><00:06:01.919><c> like</c><00:06:01.979><c> to</c><00:06:02.160><c> say</c>

00:06:02.270 --> 00:06:02.280 align:start position:0%
so to wrap things up uh I'd like to say
 

00:06:02.280 --> 00:06:04.310 align:start position:0%
so to wrap things up uh I'd like to say
thanks<00:06:02.520><c> for</c><00:06:02.699><c> watching</c><00:06:03.060><c> uh</c><00:06:03.900><c> hopefully</c><00:06:04.199><c> that</c>

00:06:04.310 --> 00:06:04.320 align:start position:0%
thanks for watching uh hopefully that
 

00:06:04.320 --> 00:06:05.510 align:start position:0%
thanks for watching uh hopefully that
was<00:06:04.440><c> useful</c>

00:06:05.510 --> 00:06:05.520 align:start position:0%
was useful
 

00:06:05.520 --> 00:06:08.510 align:start position:0%
was useful
um feel<00:06:06.000><c> free</c><00:06:06.180><c> to</c><00:06:06.419><c> follow</c><00:06:06.900><c> us</c><00:06:07.080><c> on</c><00:06:07.620><c> Twitter</c><00:06:07.979><c> for</c>

00:06:08.510 --> 00:06:08.520 align:start position:0%
um feel free to follow us on Twitter for
 

00:06:08.520 --> 00:06:10.610 align:start position:0%
um feel free to follow us on Twitter for
latest<00:06:08.820><c> updates</c><00:06:09.300><c> on</c><00:06:09.960><c> a</c><00:06:10.199><c> lot</c><00:06:10.199><c> of</c><00:06:10.380><c> the</c><00:06:10.500><c> alarm</c>

00:06:10.610 --> 00:06:10.620 align:start position:0%
latest updates on a lot of the alarm
 

00:06:10.620 --> 00:06:14.930 align:start position:0%
latest updates on a lot of the alarm
index<00:06:11.100><c> uh</c><00:06:11.880><c> project</c><00:06:12.199><c> uh</c><00:06:13.199><c> also</c><00:06:13.800><c> definitely</c><00:06:14.280><c> uh</c>

00:06:14.930 --> 00:06:14.940 align:start position:0%
index uh project uh also definitely uh
 

00:06:14.940 --> 00:06:16.550 align:start position:0%
index uh project uh also definitely uh
feel<00:06:15.120><c> free</c><00:06:15.240><c> to</c><00:06:15.479><c> Star</c><00:06:15.600><c> us</c><00:06:15.840><c> on</c><00:06:16.080><c> our</c><00:06:16.199><c> GitHub</c>

00:06:16.550 --> 00:06:16.560 align:start position:0%
feel free to Star us on our GitHub
 

00:06:16.560 --> 00:06:18.350 align:start position:0%
feel free to Star us on our GitHub
project<00:06:16.800><c> as</c><00:06:17.160><c> well</c><00:06:17.340><c> and</c><00:06:17.759><c> feel</c><00:06:17.940><c> free</c><00:06:18.120><c> to</c>

00:06:18.350 --> 00:06:18.360 align:start position:0%
project as well and feel free to
 

00:06:18.360 --> 00:06:19.909 align:start position:0%
project as well and feel free to
contribute

00:06:19.909 --> 00:06:19.919 align:start position:0%
contribute
 

00:06:19.919 --> 00:06:21.770 align:start position:0%
contribute
um<00:06:19.979><c> and</c><00:06:20.340><c> finally</c><00:06:20.759><c> please</c><00:06:21.120><c> do</c><00:06:21.300><c> like</c><00:06:21.600><c> and</c>

00:06:21.770 --> 00:06:21.780 align:start position:0%
um and finally please do like and
 

00:06:21.780 --> 00:06:24.230 align:start position:0%
um and finally please do like and
subscribe<00:06:22.199><c> uh</c><00:06:23.039><c> us</c><00:06:23.220><c> here</c><00:06:23.460><c> on</c><00:06:23.639><c> YouTube</c><00:06:23.759><c> as</c><00:06:24.060><c> we'll</c>

00:06:24.230 --> 00:06:24.240 align:start position:0%
subscribe uh us here on YouTube as we'll
 

00:06:24.240 --> 00:06:26.270 align:start position:0%
subscribe uh us here on YouTube as we'll
be<00:06:24.360><c> putting</c><00:06:24.600><c> out</c><00:06:24.660><c> more</c><00:06:24.900><c> content</c><00:06:25.319><c> like</c><00:06:25.979><c> what</c>

00:06:26.270 --> 00:06:26.280 align:start position:0%
be putting out more content like what
 

00:06:26.280 --> 00:06:27.830 align:start position:0%
be putting out more content like what
you're<00:06:26.400><c> seeing</c><00:06:26.639><c> here</c>

00:06:27.830 --> 00:06:27.840 align:start position:0%
you're seeing here
 

00:06:27.840 --> 00:06:30.230 align:start position:0%
you're seeing here
um and<00:06:28.680><c> that</c><00:06:28.919><c> is</c><00:06:29.100><c> all</c><00:06:29.220><c> uh</c><00:06:29.639><c> hope</c><00:06:29.819><c> you</c><00:06:29.940><c> have</c><00:06:30.060><c> a</c>

00:06:30.230 --> 00:06:30.240 align:start position:0%
um and that is all uh hope you have a
 

00:06:30.240 --> 00:06:32.479 align:start position:0%
um and that is all uh hope you have a
great<00:06:30.300><c> day</c>

