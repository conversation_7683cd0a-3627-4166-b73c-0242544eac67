WEBVTT
Kind: captions
Language: en

00:00:02.510 --> 00:00:08.950 align:start position:0%
 
[Music]

00:00:08.950 --> 00:00:08.960 align:start position:0%
[Music]
 

00:00:08.960 --> 00:00:10.709 align:start position:0%
[Music]
java<00:00:09.280><c> has</c><00:00:09.440><c> a</c><00:00:09.519><c> random</c><00:00:09.840><c> number</c><00:00:10.160><c> generation</c>

00:00:10.709 --> 00:00:10.719 align:start position:0%
java has a random number generation
 

00:00:10.719 --> 00:00:11.270 align:start position:0%
java has a random number generation
class

00:00:11.270 --> 00:00:11.280 align:start position:0%
class
 

00:00:11.280 --> 00:00:13.270 align:start position:0%
class
called<00:00:11.599><c> random</c><00:00:12.400><c> this</c><00:00:12.639><c> class</c><00:00:12.880><c> is</c><00:00:12.960><c> used</c><00:00:13.200><c> to</c>

00:00:13.270 --> 00:00:13.280 align:start position:0%
called random this class is used to
 

00:00:13.280 --> 00:00:15.350 align:start position:0%
called random this class is used to
generate<00:00:13.679><c> a</c><00:00:13.840><c> stream</c><00:00:14.240><c> of</c><00:00:14.400><c> pseudorandom</c>

00:00:15.350 --> 00:00:15.360 align:start position:0%
generate a stream of pseudorandom
 

00:00:15.360 --> 00:00:17.349 align:start position:0%
generate a stream of pseudorandom
numbers<00:00:16.160><c> there</c><00:00:16.320><c> are</c><00:00:16.400><c> a</c><00:00:16.480><c> couple</c><00:00:16.720><c> of</c><00:00:16.880><c> ways</c><00:00:17.199><c> to</c>

00:00:17.349 --> 00:00:17.359 align:start position:0%
numbers there are a couple of ways to
 

00:00:17.359 --> 00:00:18.950 align:start position:0%
numbers there are a couple of ways to
code<00:00:17.600><c> a</c><00:00:17.680><c> random</c><00:00:18.000><c> number</c>

00:00:18.950 --> 00:00:18.960 align:start position:0%
code a random number
 

00:00:18.960 --> 00:00:20.630 align:start position:0%
code a random number
we<00:00:19.119><c> will</c><00:00:19.279><c> go</c><00:00:19.439><c> over</c><00:00:19.600><c> a</c><00:00:19.680><c> couple</c><00:00:19.920><c> standard</c><00:00:20.320><c> java</c>

00:00:20.630 --> 00:00:20.640 align:start position:0%
we will go over a couple standard java
 

00:00:20.640 --> 00:00:22.950 align:start position:0%
we will go over a couple standard java
ways<00:00:21.359><c> and</c><00:00:21.520><c> then</c><00:00:21.680><c> with</c><00:00:21.840><c> streams</c><00:00:22.400><c> we</c><00:00:22.560><c> will</c><00:00:22.640><c> go</c><00:00:22.800><c> to</c>

00:00:22.950 --> 00:00:22.960 align:start position:0%
ways and then with streams we will go to
 

00:00:22.960 --> 00:00:24.390 align:start position:0%
ways and then with streams we will go to
java<00:00:23.279><c> 8</c><00:00:23.439><c> way</c>

00:00:24.390 --> 00:00:24.400 align:start position:0%
java 8 way
 

00:00:24.400 --> 00:00:27.109 align:start position:0%
java 8 way
i'll<00:00:24.640><c> also</c><00:00:25.039><c> show</c><00:00:25.199><c> an</c><00:00:25.359><c> example</c><00:00:25.760><c> of</c><00:00:25.920><c> math.random</c>

00:00:27.109 --> 00:00:27.119 align:start position:0%
i'll also show an example of math.random
 

00:00:27.119 --> 00:00:27.910 align:start position:0%
i'll also show an example of math.random
as<00:00:27.279><c> well</c>

00:00:27.910 --> 00:00:27.920 align:start position:0%
as well
 

00:00:27.920 --> 00:00:29.429 align:start position:0%
as well
we<00:00:28.080><c> will</c><00:00:28.240><c> go</c><00:00:28.400><c> over</c><00:00:28.560><c> the</c><00:00:28.640><c> most</c><00:00:28.880><c> common</c><00:00:29.199><c> method</c>

00:00:29.429 --> 00:00:29.439 align:start position:0%
we will go over the most common method
 

00:00:29.439 --> 00:00:31.189 align:start position:0%
we will go over the most common method
for<00:00:29.679><c> generating</c><00:00:30.080><c> random</c><00:00:30.400><c> numbers</c>

00:00:31.189 --> 00:00:31.199 align:start position:0%
for generating random numbers
 

00:00:31.199 --> 00:00:34.389 align:start position:0%
for generating random numbers
next<00:00:31.679><c> in</c><00:00:32.480><c> in</c><00:00:32.640><c> the</c><00:00:32.719><c> documentation</c><00:00:33.920><c> it</c><00:00:34.000><c> shows</c><00:00:34.239><c> we</c>

00:00:34.389 --> 00:00:34.399 align:start position:0%
next in in the documentation it shows we
 

00:00:34.399 --> 00:00:35.270 align:start position:0%
next in in the documentation it shows we
need<00:00:34.559><c> an</c><00:00:34.719><c> integer</c>

00:00:35.270 --> 00:00:35.280 align:start position:0%
need an integer
 

00:00:35.280 --> 00:00:36.870 align:start position:0%
need an integer
for<00:00:35.440><c> the</c><00:00:35.520><c> parameter</c><00:00:36.160><c> which</c><00:00:36.399><c> gives</c><00:00:36.640><c> us</c><00:00:36.719><c> the</c>

00:00:36.870 --> 00:00:36.880 align:start position:0%
for the parameter which gives us the
 

00:00:36.880 --> 00:00:38.950 align:start position:0%
for the parameter which gives us the
upper<00:00:37.120><c> range</c><00:00:37.440><c> for</c><00:00:37.600><c> the</c><00:00:37.680><c> random</c><00:00:38.000><c> number</c>

00:00:38.950 --> 00:00:38.960 align:start position:0%
upper range for the random number
 

00:00:38.960 --> 00:00:40.790 align:start position:0%
upper range for the random number
it<00:00:39.120><c> will</c><00:00:39.200><c> give</c><00:00:39.360><c> us</c><00:00:39.520><c> a</c><00:00:39.600><c> number</c><00:00:39.840><c> between</c><00:00:40.239><c> 0</c>

00:00:40.790 --> 00:00:40.800 align:start position:0%
it will give us a number between 0
 

00:00:40.800 --> 00:00:42.830 align:start position:0%
it will give us a number between 0
inclusive<00:00:41.760><c> and</c><00:00:41.920><c> the</c><00:00:42.079><c> upper</c><00:00:42.320><c> range</c>

00:00:42.830 --> 00:00:42.840 align:start position:0%
inclusive and the upper range
 

00:00:42.840 --> 00:00:45.910 align:start position:0%
inclusive and the upper range
exclusive<00:00:44.480><c> what</c><00:00:44.640><c> this</c><00:00:44.879><c> means</c>

00:00:45.910 --> 00:00:45.920 align:start position:0%
exclusive what this means
 

00:00:45.920 --> 00:00:48.709 align:start position:0%
exclusive what this means
is<00:00:46.079><c> if</c><00:00:46.239><c> we</c><00:00:46.480><c> pass</c><00:00:46.879><c> in</c><00:00:47.120><c> 10</c><00:00:47.920><c> we</c><00:00:48.079><c> will</c><00:00:48.320><c> actually</c>

00:00:48.709 --> 00:00:48.719 align:start position:0%
is if we pass in 10 we will actually
 

00:00:48.719 --> 00:00:50.150 align:start position:0%
is if we pass in 10 we will actually
only<00:00:48.879><c> retrieve</c><00:00:49.440><c> numbers</c>

00:00:50.150 --> 00:00:50.160 align:start position:0%
only retrieve numbers
 

00:00:50.160 --> 00:00:55.830 align:start position:0%
only retrieve numbers
from<00:00:50.399><c> 0</c><00:00:50.800><c> to</c><00:00:51.039><c> 9</c><00:00:51.760><c> and</c><00:00:52.000><c> not</c><00:00:52.320><c> 10.</c>

00:00:55.830 --> 00:00:55.840 align:start position:0%
 
 

00:00:55.840 --> 00:00:58.470 align:start position:0%
 
in<00:00:56.000><c> order</c><00:00:56.239><c> to</c><00:00:56.399><c> get</c><00:00:56.480><c> the</c><00:00:56.640><c> numbers</c><00:00:57.039><c> from</c><00:00:57.280><c> 1</c><00:00:57.440><c> to</c><00:00:57.600><c> 10</c>

00:00:58.470 --> 00:00:58.480 align:start position:0%
in order to get the numbers from 1 to 10
 

00:00:58.480 --> 00:00:59.189 align:start position:0%
in order to get the numbers from 1 to 10
which<00:00:58.800><c> is</c>

00:00:59.189 --> 00:00:59.199 align:start position:0%
which is
 

00:00:59.199 --> 00:01:02.069 align:start position:0%
which is
our<00:00:59.440><c> intention</c><00:01:00.399><c> we</c><00:01:00.559><c> can</c><00:01:00.800><c> add</c><00:01:01.120><c> 1</c><00:01:01.440><c> to</c><00:01:01.600><c> the</c><00:01:01.760><c> method</c>

00:01:02.069 --> 00:01:02.079 align:start position:0%
our intention we can add 1 to the method
 

00:01:02.079 --> 00:01:03.910 align:start position:0%
our intention we can add 1 to the method
like<00:01:02.320><c> this</c>

00:01:03.910 --> 00:01:03.920 align:start position:0%
like this
 

00:01:03.920 --> 00:01:05.350 align:start position:0%
like this
if<00:01:04.080><c> we</c><00:01:04.159><c> want</c><00:01:04.320><c> to</c><00:01:04.400><c> return</c><00:01:04.720><c> a</c><00:01:04.799><c> random</c><00:01:05.119><c> number</c>

00:01:05.350 --> 00:01:05.360 align:start position:0%
if we want to return a random number
 

00:01:05.360 --> 00:01:07.590 align:start position:0%
if we want to return a random number
within<00:01:05.680><c> a</c><00:01:05.840><c> range</c><00:01:06.560><c> we</c><00:01:06.720><c> create</c><00:01:06.960><c> a</c><00:01:07.119><c> method</c><00:01:07.439><c> that</c>

00:01:07.590 --> 00:01:07.600 align:start position:0%
within a range we create a method that
 

00:01:07.600 --> 00:01:09.750 align:start position:0%
within a range we create a method that
passes<00:01:08.000><c> in</c><00:01:08.159><c> an</c><00:01:08.320><c> upper</c><00:01:08.640><c> and</c><00:01:08.799><c> lower</c><00:01:09.119><c> range</c>

00:01:09.750 --> 00:01:09.760 align:start position:0%
passes in an upper and lower range
 

00:01:09.760 --> 00:01:13.350 align:start position:0%
passes in an upper and lower range
and<00:01:09.920><c> then</c><00:01:10.159><c> add</c><00:01:10.400><c> the</c><00:01:10.560><c> lower</c><00:01:10.880><c> range</c><00:01:11.360><c> like</c><00:01:11.520><c> this</c>

00:01:13.350 --> 00:01:13.360 align:start position:0%
and then add the lower range like this
 

00:01:13.360 --> 00:01:16.469 align:start position:0%
and then add the lower range like this
this<00:01:13.600><c> will</c><00:01:13.760><c> be</c><00:01:14.000><c> 10</c><00:01:14.320><c> minus</c><00:01:14.640><c> 3</c><00:01:15.520><c> so</c><00:01:15.840><c> 7</c><00:01:16.159><c> as</c><00:01:16.320><c> the</c>

00:01:16.469 --> 00:01:16.479 align:start position:0%
this will be 10 minus 3 so 7 as the
 

00:01:16.479 --> 00:01:17.030 align:start position:0%
this will be 10 minus 3 so 7 as the
bound

00:01:17.030 --> 00:01:17.040 align:start position:0%
bound
 

00:01:17.040 --> 00:01:20.230 align:start position:0%
bound
but<00:01:17.200><c> then</c><00:01:17.439><c> we</c><00:01:17.680><c> add</c><00:01:18.000><c> 3</c><00:01:18.880><c> so</c><00:01:19.040><c> the</c><00:01:19.280><c> range</c><00:01:19.840><c> is</c><00:01:20.080><c> going</c>

00:01:20.230 --> 00:01:20.240 align:start position:0%
but then we add 3 so the range is going
 

00:01:20.240 --> 00:01:21.030 align:start position:0%
but then we add 3 so the range is going
to<00:01:20.320><c> be</c><00:01:20.479><c> between</c>

00:01:21.030 --> 00:01:21.040 align:start position:0%
to be between
 

00:01:21.040 --> 00:01:24.310 align:start position:0%
to be between
3<00:01:21.439><c> and</c><00:01:21.680><c> 10</c><00:01:22.640><c> exclusive</c><00:01:23.600><c> meaning</c><00:01:23.920><c> we'll</c><00:01:24.080><c> only</c>

00:01:24.310 --> 00:01:24.320 align:start position:0%
3 and 10 exclusive meaning we'll only
 

00:01:24.320 --> 00:01:25.510 align:start position:0%
3 and 10 exclusive meaning we'll only
get<00:01:24.560><c> numbers</c>

00:01:25.510 --> 00:01:25.520 align:start position:0%
get numbers
 

00:01:25.520 --> 00:01:29.830 align:start position:0%
get numbers
from<00:01:25.920><c> 3</c><00:01:26.240><c> to</c><00:01:26.479><c> 9.</c>

00:01:29.830 --> 00:01:29.840 align:start position:0%
 
 

00:01:29.840 --> 00:01:32.149 align:start position:0%
 
java<00:01:30.240><c> 8</c><00:01:30.479><c> introduced</c><00:01:30.960><c> streams</c><00:01:31.600><c> which</c><00:01:31.759><c> we</c><00:01:31.920><c> won't</c>

00:01:32.149 --> 00:01:32.159 align:start position:0%
java 8 introduced streams which we won't
 

00:01:32.159 --> 00:01:33.030 align:start position:0%
java 8 introduced streams which we won't
really<00:01:32.400><c> get</c><00:01:32.640><c> into</c>

00:01:33.030 --> 00:01:33.040 align:start position:0%
really get into
 

00:01:33.040 --> 00:01:36.149 align:start position:0%
really get into
here<00:01:34.079><c> but</c><00:01:34.799><c> a</c><00:01:34.960><c> method</c><00:01:35.360><c> like</c><00:01:35.680><c> this</c><00:01:35.920><c> which</c><00:01:36.079><c> is</c>

00:01:36.149 --> 00:01:36.159 align:start position:0%
here but a method like this which is
 

00:01:36.159 --> 00:01:37.590 align:start position:0%
here but a method like this which is
shown<00:01:36.400><c> in</c><00:01:36.560><c> documentation</c>

00:01:37.590 --> 00:01:37.600 align:start position:0%
shown in documentation
 

00:01:37.600 --> 00:01:39.830 align:start position:0%
shown in documentation
that<00:01:37.840><c> allows</c><00:01:38.240><c> us</c><00:01:38.320><c> to</c><00:01:38.560><c> print</c><00:01:38.880><c> a</c><00:01:38.960><c> stream</c><00:01:39.439><c> of</c>

00:01:39.830 --> 00:01:39.840 align:start position:0%
that allows us to print a stream of
 

00:01:39.840 --> 00:01:41.350 align:start position:0%
that allows us to print a stream of
pseudo-random<00:01:40.560><c> numbers</c>

00:01:41.350 --> 00:01:41.360 align:start position:0%
pseudo-random numbers
 

00:01:41.360 --> 00:01:44.149 align:start position:0%
pseudo-random numbers
you'll<00:01:41.600><c> notice</c><00:01:42.079><c> that</c><00:01:42.320><c> this</c><00:01:42.560><c> is</c><00:01:42.720><c> a</c><00:01:43.119><c> void</c><00:01:43.680><c> method</c>

00:01:44.149 --> 00:01:44.159 align:start position:0%
you'll notice that this is a void method
 

00:01:44.159 --> 00:01:46.230 align:start position:0%
you'll notice that this is a void method
we<00:01:44.320><c> don't</c><00:01:44.560><c> have</c><00:01:44.720><c> to</c><00:01:44.960><c> return</c><00:01:45.360><c> anything</c>

00:01:46.230 --> 00:01:46.240 align:start position:0%
we don't have to return anything
 

00:01:46.240 --> 00:01:49.510 align:start position:0%
we don't have to return anything
because<00:01:47.040><c> within</c><00:01:48.000><c> this</c><00:01:48.240><c> stream</c><00:01:48.880><c> we</c><00:01:49.040><c> can</c><00:01:49.280><c> sort</c>

00:01:49.510 --> 00:01:49.520 align:start position:0%
because within this stream we can sort
 

00:01:49.520 --> 00:01:49.990 align:start position:0%
because within this stream we can sort
them

00:01:49.990 --> 00:01:50.000 align:start position:0%
them
 

00:01:50.000 --> 00:01:52.389 align:start position:0%
them
and<00:01:50.320><c> print</c><00:01:50.640><c> them</c><00:01:50.880><c> out</c><00:01:51.200><c> i</c><00:01:51.280><c> have</c><00:01:51.439><c> a</c><00:01:51.600><c> video</c><00:01:52.240><c> on</c>

00:01:52.389 --> 00:01:52.399 align:start position:0%
and print them out i have a video on
 

00:01:52.399 --> 00:01:53.190 align:start position:0%
and print them out i have a video on
this<00:01:52.640><c> later</c>

00:01:53.190 --> 00:01:53.200 align:start position:0%
this later
 

00:01:53.200 --> 00:01:55.429 align:start position:0%
this later
as<00:01:53.439><c> java</c><00:01:53.840><c> says</c><00:01:54.240><c> using</c><00:01:54.479><c> math.random</c><00:01:55.280><c> is</c>

00:01:55.429 --> 00:01:55.439 align:start position:0%
as java says using math.random is
 

00:01:55.439 --> 00:01:56.630 align:start position:0%
as java says using math.random is
simpler

00:01:56.630 --> 00:01:56.640 align:start position:0%
simpler
 

00:01:56.640 --> 00:01:57.990 align:start position:0%
simpler
the<00:01:56.799><c> reason</c><00:01:57.119><c> is</c><00:01:57.280><c> because</c><00:01:57.520><c> it</c><00:01:57.600><c> doesn't</c>

00:01:57.990 --> 00:01:58.000 align:start position:0%
the reason is because it doesn't
 

00:01:58.000 --> 00:02:00.230 align:start position:0%
the reason is because it doesn't
actually<00:01:58.479><c> take</c><00:01:58.799><c> any</c><00:01:59.119><c> parameters</c><00:01:59.840><c> for</c><00:02:00.079><c> the</c>

00:02:00.230 --> 00:02:00.240 align:start position:0%
actually take any parameters for the
 

00:02:00.240 --> 00:02:02.230 align:start position:0%
actually take any parameters for the
method<00:02:00.560><c> itself</c><00:02:00.960><c> because</c><00:02:01.200><c> the</c><00:02:01.360><c> method</c>

00:02:02.230 --> 00:02:02.240 align:start position:0%
method itself because the method
 

00:02:02.240 --> 00:02:05.670 align:start position:0%
method itself because the method
itself<00:02:02.960><c> will</c><00:02:03.119><c> return</c><00:02:03.600><c> a</c><00:02:04.000><c> double</c><00:02:04.880><c> between</c>

00:02:05.670 --> 00:02:05.680 align:start position:0%
itself will return a double between
 

00:02:05.680 --> 00:02:09.190 align:start position:0%
itself will return a double between
0.0<00:02:07.200><c> and</c><00:02:07.360><c> 1.0</c>

00:02:09.190 --> 00:02:09.200 align:start position:0%
0.0 and 1.0
 

00:02:09.200 --> 00:02:12.630 align:start position:0%
0.0 and 1.0
so<00:02:09.520><c> how</c><00:02:09.679><c> do</c><00:02:09.759><c> we</c><00:02:10.000><c> get</c><00:02:10.319><c> a</c><00:02:10.479><c> random</c><00:02:10.959><c> integer</c><00:02:11.680><c> well</c>

00:02:12.630 --> 00:02:12.640 align:start position:0%
so how do we get a random integer well
 

00:02:12.640 --> 00:02:15.910 align:start position:0%
so how do we get a random integer well
like<00:02:12.879><c> here</c><00:02:13.280><c> we</c><00:02:13.440><c> do</c><00:02:13.680><c> math.random</c><00:02:14.720><c> times</c>

00:02:15.910 --> 00:02:15.920 align:start position:0%
like here we do math.random times
 

00:02:15.920 --> 00:02:18.949 align:start position:0%
like here we do math.random times
an<00:02:16.160><c> upper</c><00:02:16.560><c> minus</c><00:02:16.959><c> the</c><00:02:17.120><c> lower</c><00:02:17.440><c> range</c><00:02:18.319><c> plus</c>

00:02:18.949 --> 00:02:18.959 align:start position:0%
an upper minus the lower range plus
 

00:02:18.959 --> 00:02:21.430 align:start position:0%
an upper minus the lower range plus
the<00:02:19.120><c> lower</c><00:02:19.440><c> range</c><00:02:19.920><c> this</c><00:02:20.160><c> is</c><00:02:20.400><c> similar</c><00:02:21.040><c> to</c><00:02:21.280><c> the</c>

00:02:21.430 --> 00:02:21.440 align:start position:0%
the lower range this is similar to the
 

00:02:21.440 --> 00:02:21.990 align:start position:0%
the lower range this is similar to the
get<00:02:21.599><c> random</c>

00:02:21.990 --> 00:02:22.000 align:start position:0%
get random
 

00:02:22.000 --> 00:02:25.110 align:start position:0%
get random
number<00:02:22.319><c> in</c><00:02:22.560><c> range</c><00:02:22.959><c> we</c><00:02:23.120><c> showed</c><00:02:23.440><c> earlier</c><00:02:24.800><c> now</c>

00:02:25.110 --> 00:02:25.120 align:start position:0%
number in range we showed earlier now
 

00:02:25.120 --> 00:02:26.949 align:start position:0%
number in range we showed earlier now
as<00:02:25.280><c> i</c><00:02:25.440><c> said</c><00:02:25.760><c> this</c><00:02:26.000><c> is</c><00:02:26.080><c> supposed</c><00:02:26.319><c> to</c><00:02:26.480><c> return</c><00:02:26.800><c> a</c>

00:02:26.949 --> 00:02:26.959 align:start position:0%
as i said this is supposed to return a
 

00:02:26.959 --> 00:02:28.150 align:start position:0%
as i said this is supposed to return a
double<00:02:27.680><c> so</c>

00:02:28.150 --> 00:02:28.160 align:start position:0%
double so
 

00:02:28.160 --> 00:02:36.060 align:start position:0%
double so
in<00:02:28.319><c> order</c><00:02:28.480><c> to</c><00:02:28.720><c> turn</c><00:02:28.959><c> it</c><00:02:29.120><c> in</c><00:02:29.520><c> we</c><00:02:29.680><c> simply</c><00:02:30.080><c> cast</c><00:02:34.840><c> it</c>

00:02:36.060 --> 00:02:36.070 align:start position:0%
in order to turn it in we simply cast it
 

00:02:36.070 --> 00:02:40.229 align:start position:0%
in order to turn it in we simply cast it
[Music]

00:02:40.229 --> 00:02:40.239 align:start position:0%
[Music]
 

00:02:40.239 --> 00:02:42.319 align:start position:0%
[Music]
you

