WEBVTT
Kind: captions
Language: en

00:00:00.539 --> 00:00:03.169 align:start position:0%
 
hey<00:00:01.140><c> everyone</c><00:00:01.260><c> welcome</c><00:00:01.920><c> to</c><00:00:02.340><c> discover</c><00:00:02.639><c> llama</c>

00:00:03.169 --> 00:00:03.179 align:start position:0%
hey everyone welcome to discover llama
 

00:00:03.179 --> 00:00:05.570 align:start position:0%
hey everyone welcome to discover llama
Index<00:00:03.659><c> this</c><00:00:04.080><c> is</c><00:00:04.200><c> a</c><00:00:04.380><c> new</c><00:00:04.560><c> tutorial</c><00:00:04.920><c> series</c><00:00:05.220><c> that</c>

00:00:05.570 --> 00:00:05.580 align:start position:0%
Index this is a new tutorial series that
 

00:00:05.580 --> 00:00:07.610 align:start position:0%
Index this is a new tutorial series that
we're<00:00:05.759><c> starting</c><00:00:06.000><c> up</c><00:00:06.299><c> to</c><00:00:06.779><c> tell</c><00:00:06.899><c> you</c><00:00:07.080><c> guys</c><00:00:07.259><c> all</c>

00:00:07.610 --> 00:00:07.620 align:start position:0%
we're starting up to tell you guys all
 

00:00:07.620 --> 00:00:09.710 align:start position:0%
we're starting up to tell you guys all
about<00:00:07.799><c> the</c><00:00:08.160><c> latest</c><00:00:08.280><c> features</c><00:00:08.760><c> of</c><00:00:09.000><c> llama</c><00:00:09.300><c> index</c>

00:00:09.710 --> 00:00:09.720 align:start position:0%
about the latest features of llama index
 

00:00:09.720 --> 00:00:11.570 align:start position:0%
about the latest features of llama index
and<00:00:10.260><c> how</c><00:00:10.440><c> you</c><00:00:10.559><c> can</c><00:00:10.740><c> really</c><00:00:10.920><c> use</c><00:00:11.160><c> it</c><00:00:11.340><c> to</c>

00:00:11.570 --> 00:00:11.580 align:start position:0%
and how you can really use it to
 

00:00:11.580 --> 00:00:13.070 align:start position:0%
and how you can really use it to
supercharge<00:00:12.179><c> your</c><00:00:12.300><c> large</c><00:00:12.540><c> language</c><00:00:12.780><c> model</c>

00:00:13.070 --> 00:00:13.080 align:start position:0%
supercharge your large language model
 

00:00:13.080 --> 00:00:15.410 align:start position:0%
supercharge your large language model
applications<00:00:13.799><c> especially</c><00:00:14.759><c> when</c><00:00:15.059><c> dealing</c>

00:00:15.410 --> 00:00:15.420 align:start position:0%
applications especially when dealing
 

00:00:15.420 --> 00:00:18.230 align:start position:0%
applications especially when dealing
with<00:00:15.660><c> large</c><00:00:15.960><c> amounts</c><00:00:16.379><c> of</c><00:00:16.440><c> data</c><00:00:16.859><c> I'm</c><00:00:17.580><c> Simon</c><00:00:18.060><c> the</c>

00:00:18.230 --> 00:00:18.240 align:start position:0%
with large amounts of data I'm Simon the
 

00:00:18.240 --> 00:00:20.570 align:start position:0%
with large amounts of data I'm Simon the
co-founder<00:00:18.720><c> and</c><00:00:18.840><c> CTO</c><00:00:19.140><c> of</c><00:00:19.320><c> llama</c><00:00:19.560><c> index</c><00:00:19.980><c> and</c>

00:00:20.570 --> 00:00:20.580 align:start position:0%
co-founder and CTO of llama index and
 

00:00:20.580 --> 00:00:22.550 align:start position:0%
co-founder and CTO of llama index and
today<00:00:20.820><c> I'm</c><00:00:21.119><c> super</c><00:00:21.359><c> excited</c><00:00:21.900><c> to</c><00:00:22.080><c> tell</c><00:00:22.260><c> you</c><00:00:22.439><c> guys</c>

00:00:22.550 --> 00:00:22.560 align:start position:0%
today I'm super excited to tell you guys
 

00:00:22.560 --> 00:00:24.170 align:start position:0%
today I'm super excited to tell you guys
just<00:00:22.920><c> a</c><00:00:23.039><c> little</c><00:00:23.160><c> bit</c><00:00:23.279><c> more</c><00:00:23.520><c> about</c><00:00:23.760><c> our</c><00:00:24.000><c> latest</c>

00:00:24.170 --> 00:00:24.180 align:start position:0%
just a little bit more about our latest
 

00:00:24.180 --> 00:00:29.390 align:start position:0%
just a little bit more about our latest
feature<00:00:24.720><c> the</c><00:00:25.199><c> sub</c><00:00:25.380><c> question</c><00:00:25.619><c> query</c><00:00:26.100><c> engine</c>

00:00:29.390 --> 00:00:29.400 align:start position:0%
 
 

00:00:29.400 --> 00:00:31.910 align:start position:0%
 
before<00:00:30.000><c> we</c><00:00:30.300><c> dive</c><00:00:30.599><c> down</c><00:00:30.840><c> into</c><00:00:31.199><c> the</c><00:00:31.439><c> details</c>

00:00:31.910 --> 00:00:31.920 align:start position:0%
before we dive down into the details
 

00:00:31.920 --> 00:00:34.250 align:start position:0%
before we dive down into the details
let's<00:00:32.399><c> recap</c><00:00:32.759><c> at</c><00:00:33.120><c> a</c><00:00:33.239><c> high</c><00:00:33.300><c> level</c><00:00:33.480><c> what</c><00:00:33.899><c> a</c><00:00:34.079><c> query</c>

00:00:34.250 --> 00:00:34.260 align:start position:0%
let's recap at a high level what a query
 

00:00:34.260 --> 00:00:37.610 align:start position:0%
let's recap at a high level what a query
engine<00:00:34.559><c> is</c><00:00:34.920><c> in</c><00:00:35.820><c> Lama</c><00:00:36.120><c> index</c><00:00:36.540><c> a</c><00:00:37.079><c> query</c><00:00:37.380><c> engine</c>

00:00:37.610 --> 00:00:37.620 align:start position:0%
engine is in Lama index a query engine
 

00:00:37.620 --> 00:00:40.010 align:start position:0%
engine is in Lama index a query engine
really<00:00:38.219><c> is</c><00:00:38.579><c> a</c><00:00:38.880><c> high</c><00:00:39.000><c> level</c><00:00:39.239><c> natural</c><00:00:39.719><c> language</c>

00:00:40.010 --> 00:00:40.020 align:start position:0%
really is a high level natural language
 

00:00:40.020 --> 00:00:42.650 align:start position:0%
really is a high level natural language
interface<00:00:40.800><c> that</c><00:00:41.100><c> we</c><00:00:41.280><c> build</c><00:00:41.520><c> over</c><00:00:42.000><c> your</c><00:00:42.300><c> data</c>

00:00:42.650 --> 00:00:42.660 align:start position:0%
interface that we build over your data
 

00:00:42.660 --> 00:00:45.170 align:start position:0%
interface that we build over your data
source<00:00:42.960><c> so</c><00:00:43.379><c> that</c><00:00:43.559><c> you</c><00:00:43.739><c> can</c><00:00:43.860><c> very</c><00:00:44.100><c> easily</c><00:00:44.579><c> ask</c><00:00:44.940><c> a</c>

00:00:45.170 --> 00:00:45.180 align:start position:0%
source so that you can very easily ask a
 

00:00:45.180 --> 00:00:47.690 align:start position:0%
source so that you can very easily ask a
question<00:00:45.360><c> over</c><00:00:45.660><c> your</c><00:00:45.899><c> data</c><00:00:46.260><c> and</c><00:00:46.920><c> extract</c><00:00:47.280><c> very</c>

00:00:47.690 --> 00:00:47.700 align:start position:0%
question over your data and extract very
 

00:00:47.700 --> 00:00:50.930 align:start position:0%
question over your data and extract very
nuanced<00:00:48.239><c> insights</c><00:00:48.719><c> out</c><00:00:48.840><c> of</c><00:00:49.020><c> it</c>

00:00:50.930 --> 00:00:50.940 align:start position:0%
nuanced insights out of it
 

00:00:50.940 --> 00:00:53.630 align:start position:0%
nuanced insights out of it
now<00:00:51.539><c> the</c><00:00:51.899><c> key</c><00:00:52.079><c> challenge</c><00:00:52.320><c> in</c><00:00:52.739><c> building</c><00:00:52.980><c> such</c><00:00:53.399><c> a</c>

00:00:53.630 --> 00:00:53.640 align:start position:0%
now the key challenge in building such a
 

00:00:53.640 --> 00:00:56.389 align:start position:0%
now the key challenge in building such a
unified<00:00:54.180><c> query</c><00:00:54.780><c> interface</c><00:00:55.320><c> over</c><00:00:55.680><c> your</c><00:00:55.980><c> data</c>

00:00:56.389 --> 00:00:56.399 align:start position:0%
unified query interface over your data
 

00:00:56.399 --> 00:00:59.510 align:start position:0%
unified query interface over your data
is<00:00:57.000><c> that</c><00:00:57.239><c> there's</c><00:00:57.660><c> often</c><00:00:58.199><c> a</c><00:00:58.800><c> wide</c><00:00:59.039><c> range</c><00:00:59.340><c> of</c>

00:00:59.510 --> 00:00:59.520 align:start position:0%
is that there's often a wide range of
 

00:00:59.520 --> 00:01:01.130 align:start position:0%
is that there's often a wide range of
complicated<00:01:00.059><c> questions</c><00:01:00.480><c> that</c><00:01:00.899><c> you</c><00:01:01.020><c> might</c>

00:01:01.130 --> 00:01:01.140 align:start position:0%
complicated questions that you might
 

00:01:01.140 --> 00:01:03.549 align:start position:0%
complicated questions that you might
want<00:01:01.320><c> to</c><00:01:01.500><c> ask</c><00:01:01.739><c> over</c><00:01:02.219><c> multiple</c><00:01:02.760><c> data</c><00:01:03.239><c> sources</c>

00:01:03.549 --> 00:01:03.559 align:start position:0%
want to ask over multiple data sources
 

00:01:03.559 --> 00:01:06.350 align:start position:0%
want to ask over multiple data sources
now<00:01:04.559><c> for</c><00:01:04.860><c> example</c><00:01:05.159><c> you</c><00:01:05.580><c> might</c><00:01:05.700><c> have</c><00:01:05.880><c> multiple</c>

00:01:06.350 --> 00:01:06.360 align:start position:0%
now for example you might have multiple
 

00:01:06.360 --> 00:01:08.570 align:start position:0%
now for example you might have multiple
financial<00:01:06.659><c> documents</c><00:01:07.560><c> and</c><00:01:08.100><c> you</c><00:01:08.340><c> want</c><00:01:08.460><c> to</c>

00:01:08.570 --> 00:01:08.580 align:start position:0%
financial documents and you want to
 

00:01:08.580 --> 00:01:10.370 align:start position:0%
financial documents and you want to
compare<00:01:09.000><c> and</c><00:01:09.180><c> contrast</c><00:01:09.720><c> the</c><00:01:10.140><c> financial</c>

00:01:10.370 --> 00:01:10.380 align:start position:0%
compare and contrast the financial
 

00:01:10.380 --> 00:01:12.410 align:start position:0%
compare and contrast the financial
statements<00:01:11.100><c> of</c><00:01:11.340><c> multiple</c><00:01:11.700><c> companies</c><00:01:12.180><c> over</c>

00:01:12.410 --> 00:01:12.420 align:start position:0%
statements of multiple companies over
 

00:01:12.420 --> 00:01:15.289 align:start position:0%
statements of multiple companies over
multiple<00:01:12.960><c> years</c><00:01:13.280><c> now</c><00:01:14.280><c> just</c><00:01:14.760><c> naively</c>

00:01:15.289 --> 00:01:15.299 align:start position:0%
multiple years now just naively
 

00:01:15.299 --> 00:01:17.630 align:start position:0%
multiple years now just naively
combining<00:01:15.900><c> all</c><00:01:16.140><c> those</c><00:01:16.320><c> data</c><00:01:16.680><c> sources</c><00:01:17.040><c> and</c>

00:01:17.630 --> 00:01:17.640 align:start position:0%
combining all those data sources and
 

00:01:17.640 --> 00:01:20.390 align:start position:0%
combining all those data sources and
doing<00:01:17.820><c> topk</c><00:01:18.299><c> retrieval</c><00:01:18.840><c> of</c><00:01:19.140><c> Chunk</c><00:01:19.680><c> documents</c>

00:01:20.390 --> 00:01:20.400 align:start position:0%
doing topk retrieval of Chunk documents
 

00:01:20.400 --> 00:01:23.149 align:start position:0%
doing topk retrieval of Chunk documents
for<00:01:20.700><c> a</c><00:01:20.880><c> final</c><00:01:21.060><c> synthesis</c><00:01:21.720><c> is</c><00:01:22.439><c> simply</c><00:01:22.740><c> not</c><00:01:22.979><c> good</c>

00:01:23.149 --> 00:01:23.159 align:start position:0%
for a final synthesis is simply not good
 

00:01:23.159 --> 00:01:25.249 align:start position:0%
for a final synthesis is simply not good
enough<00:01:23.400><c> to</c><00:01:23.759><c> get</c><00:01:23.939><c> a</c><00:01:24.299><c> very</c><00:01:24.420><c> good</c><00:01:24.659><c> and</c><00:01:24.840><c> nuanced</c>

00:01:25.249 --> 00:01:25.259 align:start position:0%
enough to get a very good and nuanced
 

00:01:25.259 --> 00:01:28.010 align:start position:0%
enough to get a very good and nuanced
answers<00:01:25.619><c> for</c><00:01:25.920><c> complex</c><00:01:26.340><c> questions</c><00:01:26.720><c> now</c><00:01:27.720><c> how</c><00:01:27.900><c> do</c>

00:01:28.010 --> 00:01:28.020 align:start position:0%
answers for complex questions now how do
 

00:01:28.020 --> 00:01:29.929 align:start position:0%
answers for complex questions now how do
we<00:01:28.140><c> do</c><00:01:28.320><c> this</c>

00:01:29.929 --> 00:01:29.939 align:start position:0%
we do this
 

00:01:29.939 --> 00:01:32.690 align:start position:0%
we do this
the<00:01:30.479><c> sub</c><00:01:30.659><c> question</c><00:01:30.900><c> query</c><00:01:31.320><c> engine</c><00:01:31.680><c> is</c><00:01:32.340><c> one</c>

00:01:32.690 --> 00:01:32.700 align:start position:0%
the sub question query engine is one
 

00:01:32.700 --> 00:01:34.910 align:start position:0%
the sub question query engine is one
step<00:01:32.939><c> in</c><00:01:33.240><c> the</c><00:01:33.360><c> direction</c><00:01:33.479><c> of</c><00:01:33.900><c> answering</c><00:01:34.439><c> these</c>

00:01:34.910 --> 00:01:34.920 align:start position:0%
step in the direction of answering these
 

00:01:34.920 --> 00:01:38.030 align:start position:0%
step in the direction of answering these
complex<00:01:35.220><c> questions</c><00:01:35.960><c> the</c><00:01:36.960><c> key</c><00:01:37.079><c> intuition</c><00:01:37.560><c> is</c>

00:01:38.030 --> 00:01:38.040 align:start position:0%
complex questions the key intuition is
 

00:01:38.040 --> 00:01:40.490 align:start position:0%
complex questions the key intuition is
that<00:01:38.280><c> when</c><00:01:38.520><c> you</c><00:01:38.700><c> have</c><00:01:38.939><c> a</c><00:01:39.360><c> complex</c><00:01:39.780><c> multi-part</c>

00:01:40.490 --> 00:01:40.500 align:start position:0%
that when you have a complex multi-part
 

00:01:40.500 --> 00:01:43.190 align:start position:0%
that when you have a complex multi-part
question<00:01:40.860><c> the</c><00:01:41.820><c> best</c><00:01:41.880><c> way</c><00:01:42.119><c> to</c><00:01:42.299><c> deal</c><00:01:42.479><c> with</c><00:01:42.780><c> it</c><00:01:42.900><c> is</c>

00:01:43.190 --> 00:01:43.200 align:start position:0%
question the best way to deal with it is
 

00:01:43.200 --> 00:01:46.069 align:start position:0%
question the best way to deal with it is
to<00:01:43.560><c> First</c><00:01:43.740><c> decompose</c><00:01:44.400><c> it</c><00:01:44.640><c> into</c><00:01:45.240><c> multiple</c><00:01:45.780><c> sub</c>

00:01:46.069 --> 00:01:46.079 align:start position:0%
to First decompose it into multiple sub
 

00:01:46.079 --> 00:01:48.830 align:start position:0%
to First decompose it into multiple sub
questions<00:01:46.380><c> that</c><00:01:47.340><c> are</c><00:01:47.520><c> relevant</c><00:01:47.939><c> to</c><00:01:48.360><c> specific</c>

00:01:48.830 --> 00:01:48.840 align:start position:0%
questions that are relevant to specific
 

00:01:48.840 --> 00:01:51.950 align:start position:0%
questions that are relevant to specific
data<00:01:49.439><c> sources</c><00:01:49.740><c> now</c><00:01:50.640><c> for</c><00:01:50.880><c> example</c><00:01:51.180><c> here</c><00:01:51.600><c> we</c>

00:01:51.950 --> 00:01:51.960 align:start position:0%
data sources now for example here we
 

00:01:51.960 --> 00:01:54.109 align:start position:0%
data sources now for example here we
have<00:01:52.079><c> three</c><00:01:52.320><c> data</c><00:01:52.740><c> sources</c><00:01:53.100><c> and</c><00:01:53.700><c> we</c><00:01:53.880><c> might</c>

00:01:54.109 --> 00:01:54.119 align:start position:0%
have three data sources and we might
 

00:01:54.119 --> 00:01:56.210 align:start position:0%
have three data sources and we might
want<00:01:54.299><c> to</c><00:01:54.479><c> decompose</c><00:01:54.960><c> the</c><00:01:55.200><c> question</c><00:01:55.439><c> that</c>

00:01:56.210 --> 00:01:56.220 align:start position:0%
want to decompose the question that
 

00:01:56.220 --> 00:01:59.210 align:start position:0%
want to decompose the question that
targets<00:01:56.939><c> a</c><00:01:57.420><c> subset</c><00:01:57.780><c> of</c><00:01:57.960><c> them</c><00:01:58.140><c> first</c><00:01:58.439><c> and</c><00:01:59.040><c> then</c>

00:01:59.210 --> 00:01:59.220 align:start position:0%
targets a subset of them first and then
 

00:01:59.220 --> 00:02:01.670 align:start position:0%
targets a subset of them first and then
combines<00:01:59.700><c> the</c><00:02:00.119><c> response</c><00:02:00.600><c> over</c><00:02:00.780><c> multiple</c><00:02:01.259><c> data</c>

00:02:01.670 --> 00:02:01.680 align:start position:0%
combines the response over multiple data
 

00:02:01.680 --> 00:02:04.190 align:start position:0%
combines the response over multiple data
sources<00:02:01.979><c> to</c><00:02:02.340><c> get</c><00:02:02.520><c> the</c><00:02:02.759><c> final</c><00:02:02.939><c> answer</c>

00:02:04.190 --> 00:02:04.200 align:start position:0%
sources to get the final answer
 

00:02:04.200 --> 00:02:06.950 align:start position:0%
sources to get the final answer
now<00:02:04.979><c> this</c><00:02:05.399><c> approach</c><00:02:05.759><c> really</c><00:02:06.060><c> leverages</c><00:02:06.540><c> the</c>

00:02:06.950 --> 00:02:06.960 align:start position:0%
now this approach really leverages the
 

00:02:06.960 --> 00:02:10.309 align:start position:0%
now this approach really leverages the
composability<00:02:07.619><c> aspect</c><00:02:08.459><c> of</c><00:02:08.759><c> our</c><00:02:09.000><c> framework</c><00:02:09.539><c> by</c>

00:02:10.309 --> 00:02:10.319 align:start position:0%
composability aspect of our framework by
 

00:02:10.319 --> 00:02:13.070 align:start position:0%
composability aspect of our framework by
first<00:02:10.560><c> defining</c><00:02:11.280><c> sub</c><00:02:11.940><c> query</c><00:02:12.360><c> engines</c><00:02:12.780><c> over</c>

00:02:13.070 --> 00:02:13.080 align:start position:0%
first defining sub query engines over
 

00:02:13.080 --> 00:02:15.710 align:start position:0%
first defining sub query engines over
each<00:02:13.440><c> of</c><00:02:13.560><c> these</c><00:02:13.739><c> data</c><00:02:14.099><c> sources</c><00:02:14.480><c> and</c><00:02:15.480><c> then</c>

00:02:15.710 --> 00:02:15.720 align:start position:0%
each of these data sources and then
 

00:02:15.720 --> 00:02:18.770 align:start position:0%
each of these data sources and then
Define<00:02:15.959><c> a</c><00:02:16.440><c> top</c><00:02:16.620><c> level</c><00:02:16.860><c> sub</c><00:02:17.340><c> question</c><00:02:17.780><c> query</c>

00:02:18.770 --> 00:02:18.780 align:start position:0%
Define a top level sub question query
 

00:02:18.780 --> 00:02:20.570 align:start position:0%
Define a top level sub question query
engine<00:02:19.020><c> on</c><00:02:19.379><c> top</c>

00:02:20.570 --> 00:02:20.580 align:start position:0%
engine on top
 

00:02:20.580 --> 00:02:23.869 align:start position:0%
engine on top
now<00:02:21.000><c> how</c><00:02:21.239><c> does</c><00:02:21.360><c> this</c><00:02:21.780><c> work</c><00:02:22.020><c> exactly</c><00:02:23.099><c> to</c><00:02:23.640><c> start</c>

00:02:23.869 --> 00:02:23.879 align:start position:0%
now how does this work exactly to start
 

00:02:23.879 --> 00:02:26.330 align:start position:0%
now how does this work exactly to start
when<00:02:24.480><c> given</c><00:02:24.900><c> an</c><00:02:25.080><c> initial</c><00:02:25.440><c> complex</c><00:02:25.980><c> questions</c>

00:02:26.330 --> 00:02:26.340 align:start position:0%
when given an initial complex questions
 

00:02:26.340 --> 00:02:29.089 align:start position:0%
when given an initial complex questions
we<00:02:27.180><c> will</c><00:02:27.360><c> use</c><00:02:27.540><c> the</c><00:02:27.720><c> large</c><00:02:27.840><c> language</c><00:02:28.080><c> model</c><00:02:28.560><c> to</c>

00:02:29.089 --> 00:02:29.099 align:start position:0%
we will use the large language model to
 

00:02:29.099 --> 00:02:31.430 align:start position:0%
we will use the large language model to
generate<00:02:29.520><c> sub</c><00:02:29.940><c> questions</c><00:02:30.540><c> given</c><00:02:31.260><c> the</c>

00:02:31.430 --> 00:02:31.440 align:start position:0%
generate sub questions given the
 

00:02:31.440 --> 00:02:33.589 align:start position:0%
generate sub questions given the
description<00:02:31.860><c> of</c><00:02:32.280><c> our</c><00:02:32.459><c> data</c><00:02:32.879><c> sources</c><00:02:33.180><c> that</c><00:02:33.480><c> are</c>

00:02:33.589 --> 00:02:33.599 align:start position:0%
description of our data sources that are
 

00:02:33.599 --> 00:02:34.550 align:start position:0%
description of our data sources that are
available

00:02:34.550 --> 00:02:34.560 align:start position:0%
available
 

00:02:34.560 --> 00:02:36.949 align:start position:0%
available
then<00:02:35.340><c> we</c><00:02:35.640><c> will</c><00:02:35.760><c> execute</c><00:02:36.239><c> the</c><00:02:36.480><c> sub</c><00:02:36.720><c> questions</c>

00:02:36.949 --> 00:02:36.959 align:start position:0%
then we will execute the sub questions
 

00:02:36.959 --> 00:02:39.650 align:start position:0%
then we will execute the sub questions
on<00:02:37.500><c> the</c><00:02:37.680><c> select</c><00:02:37.920><c> data</c><00:02:38.340><c> sources</c><00:02:38.640><c> and</c><00:02:39.540><c> then</c>

00:02:39.650 --> 00:02:39.660 align:start position:0%
on the select data sources and then
 

00:02:39.660 --> 00:02:42.050 align:start position:0%
on the select data sources and then
gather<00:02:39.959><c> all</c><00:02:40.260><c> the</c><00:02:40.440><c> sub</c><00:02:40.560><c> responses</c>

00:02:42.050 --> 00:02:42.060 align:start position:0%
gather all the sub responses
 

00:02:42.060 --> 00:02:44.330 align:start position:0%
gather all the sub responses
and<00:02:42.660><c> then</c><00:02:42.780><c> lastly</c><00:02:43.200><c> we</c><00:02:43.560><c> will</c><00:02:43.739><c> take</c><00:02:43.920><c> all</c><00:02:44.220><c> the</c>

00:02:44.330 --> 00:02:44.340 align:start position:0%
and then lastly we will take all the
 

00:02:44.340 --> 00:02:46.970 align:start position:0%
and then lastly we will take all the
relevant<00:02:44.700><c> contacts</c><00:02:45.300><c> from</c><00:02:45.840><c> these</c><00:02:46.260><c> sub</c><00:02:46.379><c> sources</c>

00:02:46.970 --> 00:02:46.980 align:start position:0%
relevant contacts from these sub sources
 

00:02:46.980 --> 00:02:51.470 align:start position:0%
relevant contacts from these sub sources
and<00:02:47.400><c> synthesize</c><00:02:47.940><c> the</c><00:02:48.180><c> final</c><00:02:48.360><c> answer</c>

00:02:51.470 --> 00:02:51.480 align:start position:0%
 
 

00:02:51.480 --> 00:02:53.330 align:start position:0%
 
now<00:02:51.959><c> let's</c><00:02:52.080><c> take</c><00:02:52.260><c> a</c><00:02:52.379><c> look</c><00:02:52.500><c> at</c><00:02:52.680><c> this</c><00:02:52.800><c> in</c><00:02:53.099><c> action</c>

00:02:53.330 --> 00:02:53.340 align:start position:0%
now let's take a look at this in action
 

00:02:53.340 --> 00:02:55.850 align:start position:0%
now let's take a look at this in action
to<00:02:53.760><c> help</c><00:02:53.879><c> us</c><00:02:54.060><c> do</c><00:02:54.239><c> some</c><00:02:54.480><c> complex</c><00:02:54.840><c> analysis</c><00:02:55.560><c> over</c>

00:02:55.850 --> 00:02:55.860 align:start position:0%
to help us do some complex analysis over
 

00:02:55.860 --> 00:02:58.070 align:start position:0%
to help us do some complex analysis over
multiple<00:02:56.400><c> financial</c><00:02:56.640><c> documents</c><00:02:57.480><c> which</c><00:02:57.900><c> are</c>

00:02:58.070 --> 00:02:58.080 align:start position:0%
multiple financial documents which are
 

00:02:58.080 --> 00:03:00.290 align:start position:0%
multiple financial documents which are
called<00:02:58.260><c> 10ks</c>

00:03:00.290 --> 00:03:00.300 align:start position:0%
called 10ks
 

00:03:00.300 --> 00:03:02.390 align:start position:0%
called 10ks
okay<00:03:00.720><c> now</c><00:03:01.019><c> that</c><00:03:01.260><c> we</c><00:03:01.440><c> have</c><00:03:01.560><c> our</c><00:03:01.800><c> notebook</c><00:03:02.160><c> open</c>

00:03:02.390 --> 00:03:02.400 align:start position:0%
okay now that we have our notebook open
 

00:03:02.400 --> 00:03:04.550 align:start position:0%
okay now that we have our notebook open
we<00:03:02.760><c> can</c><00:03:02.940><c> finally</c><00:03:03.300><c> go</c><00:03:03.720><c> ahead</c><00:03:03.900><c> and</c><00:03:04.140><c> play</c><00:03:04.319><c> around</c>

00:03:04.550 --> 00:03:04.560 align:start position:0%
we can finally go ahead and play around
 

00:03:04.560 --> 00:03:07.910 align:start position:0%
we can finally go ahead and play around
with<00:03:04.920><c> some</c><00:03:05.099><c> code</c><00:03:05.599><c> before</c><00:03:06.599><c> I</c><00:03:06.900><c> dive</c><00:03:07.080><c> down</c><00:03:07.440><c> into</c>

00:03:07.910 --> 00:03:07.920 align:start position:0%
with some code before I dive down into
 

00:03:07.920 --> 00:03:10.009 align:start position:0%
with some code before I dive down into
the<00:03:08.340><c> notebook</c><00:03:08.700><c> let</c><00:03:09.120><c> me</c><00:03:09.180><c> just</c><00:03:09.300><c> show</c><00:03:09.480><c> you</c><00:03:09.660><c> the</c>

00:03:10.009 --> 00:03:10.019 align:start position:0%
the notebook let me just show you the
 

00:03:10.019 --> 00:03:12.589 align:start position:0%
the notebook let me just show you the
data<00:03:10.319><c> that</c><00:03:10.560><c> we'll</c><00:03:10.680><c> be</c><00:03:10.860><c> working</c><00:03:11.040><c> with</c><00:03:11.400><c> so</c><00:03:12.300><c> we</c>

00:03:12.589 --> 00:03:12.599 align:start position:0%
data that we'll be working with so we
 

00:03:12.599 --> 00:03:16.309 align:start position:0%
data that we'll be working with so we
have<00:03:12.840><c> these</c><00:03:13.620><c> two</c><00:03:13.860><c> PDFs</c><00:03:14.640><c> which</c><00:03:15.060><c> are</c><00:03:15.480><c> the</c><00:03:15.840><c> lift</c>

00:03:16.309 --> 00:03:16.319 align:start position:0%
have these two PDFs which are the lift
 

00:03:16.319 --> 00:03:19.369 align:start position:0%
have these two PDFs which are the lift
10K<00:03:16.860><c> document</c><00:03:17.340><c> for</c><00:03:17.760><c> 2021</c><00:03:18.480><c> and</c><00:03:18.840><c> the</c><00:03:19.019><c> Uber</c>

00:03:19.369 --> 00:03:19.379 align:start position:0%
10K document for 2021 and the Uber
 

00:03:19.379 --> 00:03:22.970 align:start position:0%
10K document for 2021 and the Uber
document<00:03:19.800><c> for</c><00:03:20.280><c> 2021</c><00:03:21.060><c> so</c><00:03:21.959><c> these</c><00:03:22.319><c> are</c><00:03:22.440><c> fairly</c>

00:03:22.970 --> 00:03:22.980 align:start position:0%
document for 2021 so these are fairly
 

00:03:22.980 --> 00:03:26.030 align:start position:0%
document for 2021 so these are fairly
large<00:03:23.340><c> financial</c><00:03:23.700><c> documents</c><00:03:24.659><c> that</c><00:03:25.200><c> I</c><00:03:25.920><c> think</c>

00:03:26.030 --> 00:03:26.040 align:start position:0%
large financial documents that I think
 

00:03:26.040 --> 00:03:29.930 align:start position:0%
large financial documents that I think
have<00:03:26.700><c> oh</c><00:03:27.540><c> yeah</c><00:03:27.720><c> 307</c><00:03:28.620><c> Pages</c><00:03:28.980><c> for</c><00:03:29.280><c> this</c><00:03:29.459><c> one</c><00:03:29.640><c> and</c>

00:03:29.930 --> 00:03:29.940 align:start position:0%
have oh yeah 307 Pages for this one and
 

00:03:29.940 --> 00:03:32.990 align:start position:0%
have oh yeah 307 Pages for this one and
238<00:03:30.720><c> Pages</c><00:03:31.019><c> for</c><00:03:31.319><c> this</c><00:03:31.500><c> one</c><00:03:31.620><c> so</c><00:03:32.040><c> quite</c><00:03:32.580><c> a</c><00:03:32.700><c> lot</c><00:03:32.819><c> of</c>

00:03:32.990 --> 00:03:33.000 align:start position:0%
238 Pages for this one so quite a lot of
 

00:03:33.000 --> 00:03:36.110 align:start position:0%
238 Pages for this one so quite a lot of
data<00:03:33.360><c> here</c><00:03:33.599><c> and</c><00:03:34.260><c> it's</c><00:03:34.980><c> very</c><00:03:35.280><c> heterogeneous</c>

00:03:36.110 --> 00:03:36.120 align:start position:0%
data here and it's very heterogeneous
 

00:03:36.120 --> 00:03:39.770 align:start position:0%
data here and it's very heterogeneous
there<00:03:37.080><c> are</c><00:03:37.319><c> texts</c><00:03:38.280><c> bullet</c><00:03:38.760><c> points</c><00:03:39.180><c> and</c><00:03:39.599><c> I</c>

00:03:39.770 --> 00:03:39.780 align:start position:0%
there are texts bullet points and I
 

00:03:39.780 --> 00:03:42.289 align:start position:0%
there are texts bullet points and I
think<00:03:39.900><c> a</c><00:03:40.080><c> whole</c><00:03:40.260><c> bunch</c><00:03:40.440><c> of</c><00:03:40.680><c> tables</c><00:03:41.400><c> where</c><00:03:41.819><c> a</c>

00:03:42.289 --> 00:03:42.299 align:start position:0%
think a whole bunch of tables where a
 

00:03:42.299 --> 00:03:45.229 align:start position:0%
think a whole bunch of tables where a
lot<00:03:42.420><c> of</c><00:03:42.599><c> financial</c><00:03:42.860><c> terms</c><00:03:43.860><c> are</c><00:03:44.159><c> found</c>

00:03:45.229 --> 00:03:45.239 align:start position:0%
lot of financial terms are found
 

00:03:45.239 --> 00:03:47.270 align:start position:0%
lot of financial terms are found
okay<00:03:45.659><c> going</c><00:03:46.019><c> back</c><00:03:46.200><c> to</c><00:03:46.379><c> the</c><00:03:46.500><c> notebook</c><00:03:46.860><c> let's</c>

00:03:47.270 --> 00:03:47.280 align:start position:0%
okay going back to the notebook let's
 

00:03:47.280 --> 00:03:49.190 align:start position:0%
okay going back to the notebook let's
just<00:03:47.580><c> uh</c><00:03:47.940><c> go</c><00:03:48.180><c> straight</c><00:03:48.360><c> into</c><00:03:48.540><c> it</c><00:03:48.780><c> by</c><00:03:48.959><c> doing</c>

00:03:49.190 --> 00:03:49.200 align:start position:0%
just uh go straight into it by doing
 

00:03:49.200 --> 00:03:51.289 align:start position:0%
just uh go straight into it by doing
some<00:03:49.500><c> imports</c><00:03:50.040><c> we're</c><00:03:50.819><c> going</c><00:03:51.000><c> to</c><00:03:51.120><c> first</c>

00:03:51.289 --> 00:03:51.299 align:start position:0%
some imports we're going to first
 

00:03:51.299 --> 00:03:53.210 align:start position:0%
some imports we're going to first
configure<00:03:51.840><c> our</c><00:03:52.319><c> large</c><00:03:52.560><c> language</c><00:03:52.799><c> model</c>

00:03:53.210 --> 00:03:53.220 align:start position:0%
configure our large language model
 

00:03:53.220 --> 00:03:54.770 align:start position:0%
configure our large language model
service

00:03:54.770 --> 00:03:54.780 align:start position:0%
service
 

00:03:54.780 --> 00:03:55.789 align:start position:0%
service
um here<00:03:55.080><c> we're</c><00:03:55.260><c> just</c><00:03:55.379><c> setting</c><00:03:55.680><c> the</c>

00:03:55.789 --> 00:03:55.799 align:start position:0%
um here we're just setting the
 

00:03:55.799 --> 00:03:57.589 align:start position:0%
um here we're just setting the
temperature<00:03:55.980><c> to</c><00:03:56.340><c> zero</c><00:03:56.700><c> so</c><00:03:56.879><c> the</c><00:03:57.060><c> output</c><00:03:57.360><c> is</c>

00:03:57.589 --> 00:03:57.599 align:start position:0%
temperature to zero so the output is
 

00:03:57.599 --> 00:04:00.949 align:start position:0%
temperature to zero so the output is
more<00:03:57.840><c> deterministic</c><00:03:58.560><c> we're</c><00:03:59.099><c> using</c><00:03:59.459><c> gpt3</c><00:04:00.360><c> and</c>

00:04:00.949 --> 00:04:00.959 align:start position:0%
more deterministic we're using gpt3 and
 

00:04:00.959 --> 00:04:02.750 align:start position:0%
more deterministic we're using gpt3 and
then<00:04:01.140><c> basically</c><00:04:01.560><c> setting</c><00:04:01.980><c> the</c><00:04:02.040><c> max</c><00:04:02.220><c> token</c><00:04:02.640><c> to</c>

00:04:02.750 --> 00:04:02.760 align:start position:0%
then basically setting the max token to
 

00:04:02.760 --> 00:04:04.550 align:start position:0%
then basically setting the max token to
negative<00:04:03.060><c> one</c><00:04:03.180><c> so</c><00:04:03.420><c> that</c><00:04:03.599><c> we</c><00:04:03.780><c> don't</c><00:04:03.959><c> limit</c><00:04:04.260><c> the</c>

00:04:04.550 --> 00:04:04.560 align:start position:0%
negative one so that we don't limit the
 

00:04:04.560 --> 00:04:07.850 align:start position:0%
negative one so that we don't limit the
amount<00:04:04.799><c> of</c><00:04:04.980><c> outputs</c><00:04:05.580><c> for</c><00:04:05.940><c> these</c><00:04:06.360><c> models</c><00:04:07.080><c> we're</c>

00:04:07.850 --> 00:04:07.860 align:start position:0%
amount of outputs for these models we're
 

00:04:07.860 --> 00:04:10.190 align:start position:0%
amount of outputs for these models we're
going<00:04:08.099><c> to</c><00:04:08.220><c> wrap</c><00:04:08.700><c> the</c><00:04:08.940><c> Ln</c><00:04:09.480><c> predictor</c><00:04:09.840><c> into</c><00:04:10.019><c> our</c>

00:04:10.190 --> 00:04:10.200 align:start position:0%
going to wrap the Ln predictor into our
 

00:04:10.200 --> 00:04:12.350 align:start position:0%
going to wrap the Ln predictor into our
service<00:04:10.379><c> context</c><00:04:10.860><c> which</c><00:04:11.099><c> we'll</c><00:04:11.280><c> be</c><00:04:11.459><c> using</c><00:04:11.939><c> to</c>

00:04:12.350 --> 00:04:12.360 align:start position:0%
service context which we'll be using to
 

00:04:12.360 --> 00:04:15.229 align:start position:0%
service context which we'll be using to
pass<00:04:12.659><c> into</c><00:04:13.080><c> our</c><00:04:13.500><c> indexing</c><00:04:13.980><c> query</c><00:04:14.400><c> engine</c>

00:04:15.229 --> 00:04:15.239 align:start position:0%
pass into our indexing query engine
 

00:04:15.239 --> 00:04:17.210 align:start position:0%
pass into our indexing query engine
now<00:04:15.780><c> for</c><00:04:16.079><c> the</c><00:04:16.199><c> first</c><00:04:16.320><c> step</c><00:04:16.560><c> we're</c><00:04:16.859><c> going</c><00:04:17.040><c> to</c>

00:04:17.210 --> 00:04:17.220 align:start position:0%
now for the first step we're going to
 

00:04:17.220 --> 00:04:20.090 align:start position:0%
now for the first step we're going to
use<00:04:17.639><c> this</c><00:04:17.940><c> simple</c><00:04:18.120><c> directory</c><00:04:18.660><c> reader</c><00:04:19.139><c> to</c><00:04:19.739><c> read</c>

00:04:20.090 --> 00:04:20.100 align:start position:0%
use this simple directory reader to read
 

00:04:20.100 --> 00:04:22.850 align:start position:0%
use this simple directory reader to read
these<00:04:20.400><c> two</c><00:04:20.579><c> files</c><00:04:20.940><c> separately</c>

00:04:22.850 --> 00:04:22.860 align:start position:0%
these two files separately
 

00:04:22.860 --> 00:04:25.909 align:start position:0%
these two files separately
so<00:04:23.280><c> I</c><00:04:23.400><c> think</c><00:04:23.520><c> this</c><00:04:23.820><c> might</c><00:04:24.120><c> take</c><00:04:24.600><c> a</c><00:04:25.259><c> little</c><00:04:25.560><c> bit</c>

00:04:25.909 --> 00:04:25.919 align:start position:0%
so I think this might take a little bit
 

00:04:25.919 --> 00:04:27.950 align:start position:0%
so I think this might take a little bit
of<00:04:26.220><c> time</c><00:04:26.460><c> since</c><00:04:26.820><c> these</c><00:04:27.120><c> are</c><00:04:27.300><c> 300</c><00:04:27.600><c> Page</c>

00:04:27.950 --> 00:04:27.960 align:start position:0%
of time since these are 300 Page
 

00:04:27.960 --> 00:04:30.469 align:start position:0%
of time since these are 300 Page
documents

00:04:30.469 --> 00:04:30.479 align:start position:0%
documents
 

00:04:30.479 --> 00:04:33.530 align:start position:0%
documents
okay<00:04:30.840><c> now</c><00:04:31.139><c> we</c><00:04:31.380><c> have</c><00:04:31.440><c> the</c><00:04:31.620><c> lift</c><00:04:31.860><c> 10K</c><00:04:32.100><c> loaded</c><00:04:32.580><c> 238</c>

00:04:33.530 --> 00:04:33.540 align:start position:0%
okay now we have the lift 10K loaded 238
 

00:04:33.540 --> 00:04:37.330 align:start position:0%
okay now we have the lift 10K loaded 238
documents<00:04:34.259><c> I</c><00:04:34.560><c> think</c><00:04:34.680><c> that's</c><00:04:34.860><c> correct</c><00:04:35.280><c> yep</c>

00:04:37.330 --> 00:04:37.340 align:start position:0%
documents I think that's correct yep
 

00:04:37.340 --> 00:04:43.070 align:start position:0%
documents I think that's correct yep
and<00:04:38.340><c> see</c><00:04:39.060><c> how</c><00:04:39.240><c> long</c><00:04:39.360><c> this</c><00:04:39.540><c> takes</c>

00:04:43.070 --> 00:04:43.080 align:start position:0%
 
 

00:04:43.080 --> 00:04:46.249 align:start position:0%
 
okay<00:04:43.440><c> there</c><00:04:43.740><c> we</c><00:04:43.860><c> go</c><00:04:44.460><c> now</c><00:04:45.060><c> the</c><00:04:45.360><c> next</c><00:04:45.419><c> step</c><00:04:45.600><c> is</c><00:04:45.840><c> to</c>

00:04:46.249 --> 00:04:46.259 align:start position:0%
okay there we go now the next step is to
 

00:04:46.259 --> 00:04:48.530 align:start position:0%
okay there we go now the next step is to
build<00:04:46.500><c> our</c><00:04:46.800><c> indices</c><00:04:47.460><c> here</c><00:04:48.000><c> we're</c><00:04:48.180><c> just</c><00:04:48.360><c> going</c>

00:04:48.530 --> 00:04:48.540 align:start position:0%
build our indices here we're just going
 

00:04:48.540 --> 00:04:52.189 align:start position:0%
build our indices here we're just going
to<00:04:48.660><c> use</c><00:04:49.139><c> the</c><00:04:49.620><c> vector</c><00:04:50.100><c> store</c><00:04:50.280><c> index</c><00:04:50.960><c> what</c><00:04:51.960><c> is</c>

00:04:52.189 --> 00:04:52.199 align:start position:0%
to use the vector store index what is
 

00:04:52.199 --> 00:04:54.050 align:start position:0%
to use the vector store index what is
happening<00:04:52.560><c> under</c><00:04:52.740><c> the</c><00:04:52.979><c> hood</c><00:04:53.100><c> here</c><00:04:53.400><c> when</c><00:04:53.820><c> we</c>

00:04:54.050 --> 00:04:54.060 align:start position:0%
happening under the hood here when we
 

00:04:54.060 --> 00:04:56.990 align:start position:0%
happening under the hood here when we
call<00:04:54.300><c> the</c><00:04:54.720><c> index</c><00:04:55.139><c> style</c><00:04:55.320><c> from</c><00:04:55.740><c> document</c><00:04:56.160><c> is</c>

00:04:56.990 --> 00:04:57.000 align:start position:0%
call the index style from document is
 

00:04:57.000 --> 00:04:59.689 align:start position:0%
call the index style from document is
that<00:04:57.180><c> each</c><00:04:57.479><c> of</c><00:04:57.660><c> these</c><00:04:58.340><c> documents</c><00:04:59.340><c> will</c><00:04:59.520><c> be</c>

00:04:59.689 --> 00:04:59.699 align:start position:0%
that each of these documents will be
 

00:04:59.699 --> 00:05:03.110 align:start position:0%
that each of these documents will be
chunked<00:05:00.360><c> up</c><00:05:00.479><c> into</c><00:05:00.960><c> text</c><00:05:01.320><c> chunks</c><00:05:01.940><c> we'll</c><00:05:02.940><c> be</c>

00:05:03.110 --> 00:05:03.120 align:start position:0%
chunked up into text chunks we'll be
 

00:05:03.120 --> 00:05:07.070 align:start position:0%
chunked up into text chunks we'll be
calling<00:05:03.479><c> the</c><00:05:04.020><c> embedding</c><00:05:04.740><c> API</c><00:05:05.220><c> to</c><00:05:05.880><c> get</c><00:05:06.479><c> a</c><00:05:06.780><c> dense</c>

00:05:07.070 --> 00:05:07.080 align:start position:0%
calling the embedding API to get a dense
 

00:05:07.080 --> 00:05:08.510 align:start position:0%
calling the embedding API to get a dense
Vector<00:05:07.440><c> embedding</c><00:05:07.919><c> for</c><00:05:08.040><c> each</c><00:05:08.220><c> of</c><00:05:08.340><c> these</c>

00:05:08.510 --> 00:05:08.520 align:start position:0%
Vector embedding for each of these
 

00:05:08.520 --> 00:05:11.749 align:start position:0%
Vector embedding for each of these
chunks<00:05:08.880><c> and</c><00:05:09.660><c> save</c><00:05:09.900><c> it</c><00:05:10.139><c> to</c><00:05:10.320><c> R</c><00:05:10.500><c> in-memory</c><00:05:11.160><c> Vector</c>

00:05:11.749 --> 00:05:11.759 align:start position:0%
chunks and save it to R in-memory Vector
 

00:05:11.759 --> 00:05:14.570 align:start position:0%
chunks and save it to R in-memory Vector
store<00:05:12.440><c> so</c><00:05:13.440><c> this</c><00:05:13.620><c> takes</c><00:05:13.919><c> a</c><00:05:13.979><c> little</c><00:05:14.100><c> bit</c><00:05:14.220><c> of</c><00:05:14.340><c> time</c>

00:05:14.570 --> 00:05:14.580 align:start position:0%
store so this takes a little bit of time
 

00:05:14.580 --> 00:05:17.990 align:start position:0%
store so this takes a little bit of time
just<00:05:14.880><c> because</c><00:05:15.060><c> there's</c><00:05:15.600><c> a</c><00:05:15.960><c> lot</c><00:05:16.080><c> of</c><00:05:16.259><c> data</c><00:05:16.860><c> so</c><00:05:17.699><c> in</c>

00:05:17.990 --> 00:05:18.000 align:start position:0%
just because there's a lot of data so in
 

00:05:18.000 --> 00:05:20.210 align:start position:0%
just because there's a lot of data so in
total<00:05:18.300><c> 500</c><00:05:18.720><c> pages</c><00:05:18.960><c> of</c><00:05:19.259><c> text</c><00:05:19.500><c> that</c><00:05:19.800><c> needs</c><00:05:20.160><c> to</c>

00:05:20.210 --> 00:05:20.220 align:start position:0%
total 500 pages of text that needs to
 

00:05:20.220 --> 00:05:22.670 align:start position:0%
total 500 pages of text that needs to
kind<00:05:20.580><c> of</c><00:05:20.639><c> pass</c><00:05:20.820><c> through</c><00:05:21.060><c> the</c><00:05:21.300><c> API</c><00:05:21.660><c> calls</c><00:05:22.139><c> and</c>

00:05:22.670 --> 00:05:22.680 align:start position:0%
kind of pass through the API calls and
 

00:05:22.680 --> 00:05:25.430 align:start position:0%
kind of pass through the API calls and
get<00:05:22.860><c> back</c><00:05:23.039><c> the</c><00:05:23.160><c> responses</c><00:05:23.759><c> now</c><00:05:24.360><c> okay</c><00:05:24.780><c> so</c><00:05:25.199><c> we</c>

00:05:25.430 --> 00:05:25.440 align:start position:0%
get back the responses now okay so we
 

00:05:25.440 --> 00:05:28.990 align:start position:0%
get back the responses now okay so we
finished<00:05:26.180><c> building</c><00:05:27.180><c> the</c><00:05:27.479><c> left</c><00:05:27.660><c> 10K</c><00:05:28.080><c> index</c><00:05:28.440><c> was</c>

00:05:28.990 --> 00:05:29.000 align:start position:0%
finished building the left 10K index was
 

00:05:29.000 --> 00:05:32.450 align:start position:0%
finished building the left 10K index was
379<00:05:30.000><c> notes</c><00:05:30.840><c> you</c><00:05:31.320><c> can</c><00:05:31.440><c> see</c><00:05:31.620><c> that</c><00:05:31.860><c> is</c><00:05:32.100><c> a</c><00:05:32.400><c> little</c>

00:05:32.450 --> 00:05:32.460 align:start position:0%
379 notes you can see that is a little
 

00:05:32.460 --> 00:05:35.390 align:start position:0%
379 notes you can see that is a little
bit<00:05:32.699><c> higher</c><00:05:33.000><c> than</c><00:05:33.240><c> the</c><00:05:33.419><c> 238</c><00:05:34.199><c> pages</c>

00:05:35.390 --> 00:05:35.400 align:start position:0%
bit higher than the 238 pages
 

00:05:35.400 --> 00:05:37.790 align:start position:0%
bit higher than the 238 pages
I<00:05:35.880><c> think</c><00:05:36.000><c> most</c><00:05:36.240><c> of</c><00:05:36.360><c> the</c><00:05:36.479><c> pages</c><00:05:36.660><c> gets</c><00:05:37.259><c> broken</c><00:05:37.440><c> to</c>

00:05:37.790 --> 00:05:37.800 align:start position:0%
I think most of the pages gets broken to
 

00:05:37.800 --> 00:05:40.490 align:start position:0%
I think most of the pages gets broken to
one<00:05:38.280><c> or</c><00:05:38.460><c> two</c><00:05:38.580><c> nodes</c><00:05:38.880><c> given</c><00:05:39.479><c> our</c><00:05:39.780><c> default</c><00:05:40.139><c> chunk</c>

00:05:40.490 --> 00:05:40.500 align:start position:0%
one or two nodes given our default chunk
 

00:05:40.500 --> 00:05:43.790 align:start position:0%
one or two nodes given our default chunk
size<00:05:40.680><c> right</c><00:05:40.979><c> now</c>

00:05:43.790 --> 00:05:43.800 align:start position:0%
 
 

00:05:43.800 --> 00:05:46.610 align:start position:0%
 
okay<00:05:44.160><c> many</c><00:05:44.639><c> time</c><00:05:45.060><c> now</c><00:05:45.240><c> just</c><00:05:45.780><c> waiting</c><00:05:46.139><c> for</c><00:05:46.380><c> the</c>

00:05:46.610 --> 00:05:46.620 align:start position:0%
okay many time now just waiting for the
 

00:05:46.620 --> 00:05:54.830 align:start position:0%
okay many time now just waiting for the
Uber<00:05:46.919><c> index</c><00:05:47.220><c> to</c><00:05:47.460><c> be</c><00:05:47.520><c> built</c>

00:05:54.830 --> 00:05:54.840 align:start position:0%
 
 

00:05:54.840 --> 00:05:58.430 align:start position:0%
 
okay<00:05:55.380><c> there</c><00:05:55.800><c> we</c><00:05:55.979><c> go</c>

00:05:58.430 --> 00:05:58.440 align:start position:0%
 
 

00:05:58.440 --> 00:06:00.590 align:start position:0%
 
now<00:05:59.039><c> we're</c><00:05:59.280><c> going</c><00:05:59.460><c> to</c><00:05:59.639><c> build</c><00:05:59.940><c> individual</c>

00:06:00.590 --> 00:06:00.600 align:start position:0%
now we're going to build individual
 

00:06:00.600 --> 00:06:04.969 align:start position:0%
now we're going to build individual
query<00:06:00.960><c> engines</c><00:06:01.440><c> for</c><00:06:01.860><c> each</c><00:06:02.340><c> of</c><00:06:02.520><c> these</c><00:06:02.880><c> indexes</c>

00:06:04.969 --> 00:06:04.979 align:start position:0%
query engines for each of these indexes
 

00:06:04.979 --> 00:06:07.070 align:start position:0%
query engines for each of these indexes
here<00:06:05.460><c> when</c><00:06:05.639><c> we</c><00:06:05.820><c> do</c><00:06:05.940><c> s-query</c><00:06:06.360><c> engine</c><00:06:06.660><c> what</c>

00:06:07.070 --> 00:06:07.080 align:start position:0%
here when we do s-query engine what
 

00:06:07.080 --> 00:06:08.270 align:start position:0%
here when we do s-query engine what
we're<00:06:07.259><c> really</c><00:06:07.500><c> what's</c><00:06:07.740><c> really</c><00:06:07.979><c> happening</c>

00:06:08.270 --> 00:06:08.280 align:start position:0%
we're really what's really happening
 

00:06:08.280 --> 00:06:10.249 align:start position:0%
we're really what's really happening
under<00:06:08.520><c> the</c><00:06:08.820><c> hood</c><00:06:08.880><c> is</c><00:06:09.120><c> uh</c><00:06:09.479><c> we're</c><00:06:09.780><c> setting</c><00:06:10.139><c> up</c>

00:06:10.249 --> 00:06:10.259 align:start position:0%
under the hood is uh we're setting up
 

00:06:10.259 --> 00:06:12.230 align:start position:0%
under the hood is uh we're setting up
the<00:06:10.380><c> configuration</c><00:06:10.979><c> so</c><00:06:11.580><c> the</c><00:06:11.820><c> query</c><00:06:12.000><c> engine</c>

00:06:12.230 --> 00:06:12.240 align:start position:0%
the configuration so the query engine
 

00:06:12.240 --> 00:06:15.230 align:start position:0%
the configuration so the query engine
knows<00:06:12.720><c> what</c><00:06:12.900><c> to</c><00:06:13.020><c> do</c><00:06:13.199><c> at</c><00:06:13.680><c> great</c><00:06:13.979><c> time</c>

00:06:15.230 --> 00:06:15.240 align:start position:0%
knows what to do at great time
 

00:06:15.240 --> 00:06:18.529 align:start position:0%
knows what to do at great time
now<00:06:15.660><c> the</c><00:06:15.840><c> next</c><00:06:15.960><c> step</c><00:06:16.139><c> is</c><00:06:16.680><c> to</c><00:06:17.100><c> add</c><00:06:17.520><c> sort</c><00:06:18.300><c> of</c><00:06:18.419><c> the</c>

00:06:18.529 --> 00:06:18.539 align:start position:0%
now the next step is to add sort of the
 

00:06:18.539 --> 00:06:22.670 align:start position:0%
now the next step is to add sort of the
metadata<00:06:19.400><c> to</c><00:06:20.400><c> identify</c><00:06:21.180><c> these</c><00:06:21.900><c> query</c><00:06:22.259><c> engines</c>

00:06:22.670 --> 00:06:22.680 align:start position:0%
metadata to identify these query engines
 

00:06:22.680 --> 00:06:25.070 align:start position:0%
metadata to identify these query engines
to<00:06:23.160><c> the</c><00:06:23.400><c> top</c><00:06:23.580><c> level</c><00:06:23.819><c> sub</c><00:06:24.300><c> question</c><00:06:24.539><c> query</c>

00:06:25.070 --> 00:06:25.080 align:start position:0%
to the top level sub question query
 

00:06:25.080 --> 00:06:27.529 align:start position:0%
to the top level sub question query
engine<00:06:25.319><c> here</c><00:06:26.160><c> we're</c><00:06:26.400><c> just</c><00:06:26.580><c> specifying</c><00:06:27.180><c> the</c>

00:06:27.529 --> 00:06:27.539 align:start position:0%
engine here we're just specifying the
 

00:06:27.539 --> 00:06:29.990 align:start position:0%
engine here we're just specifying the
name<00:06:27.780><c> of</c><00:06:28.440><c> this</c><00:06:28.680><c> query</c><00:06:28.979><c> Engine</c><00:06:29.160><c> 2</c><00:06:29.460><c> and</c><00:06:29.759><c> also</c>

00:06:29.990 --> 00:06:30.000 align:start position:0%
name of this query Engine 2 and also
 

00:06:30.000 --> 00:06:32.830 align:start position:0%
name of this query Engine 2 and also
giving<00:06:30.240><c> a</c><00:06:30.539><c> description</c><00:06:30.960><c> so</c><00:06:31.500><c> the</c><00:06:32.400><c> top</c><00:06:32.520><c> level</c>

00:06:32.830 --> 00:06:32.840 align:start position:0%
giving a description so the top level
 

00:06:32.840 --> 00:06:35.749 align:start position:0%
giving a description so the top level
large<00:06:33.840><c> language</c><00:06:34.139><c> model</c><00:06:34.440><c> can</c><00:06:34.860><c> know</c><00:06:35.100><c> how</c><00:06:35.580><c> to</c>

00:06:35.749 --> 00:06:35.759 align:start position:0%
large language model can know how to
 

00:06:35.759 --> 00:06:38.450 align:start position:0%
large language model can know how to
make<00:06:36.060><c> use</c><00:06:36.300><c> of</c><00:06:36.539><c> this</c><00:06:36.720><c> data</c><00:06:37.080><c> source</c><00:06:37.380><c> okay</c><00:06:38.160><c> now</c>

00:06:38.450 --> 00:06:38.460 align:start position:0%
make use of this data source okay now
 

00:06:38.460 --> 00:06:40.550 align:start position:0%
make use of this data source okay now
that<00:06:38.699><c> we're</c><00:06:39.000><c> we</c><00:06:39.600><c> have</c><00:06:39.780><c> that</c><00:06:39.960><c> built</c><00:06:40.319><c> we're</c>

00:06:40.550 --> 00:06:40.560 align:start position:0%
that we're we have that built we're
 

00:06:40.560 --> 00:06:42.650 align:start position:0%
that we're we have that built we're
ready<00:06:40.800><c> to</c><00:06:40.979><c> run</c><00:06:41.100><c> some</c><00:06:41.340><c> queries</c><00:06:41.699><c> the</c><00:06:42.180><c> first</c><00:06:42.419><c> one</c>

00:06:42.650 --> 00:06:42.660 align:start position:0%
ready to run some queries the first one
 

00:06:42.660 --> 00:06:44.330 align:start position:0%
ready to run some queries the first one
that<00:06:42.840><c> we're</c><00:06:43.020><c> going</c><00:06:43.199><c> to</c><00:06:43.319><c> ask</c><00:06:43.560><c> is</c><00:06:43.800><c> to</c><00:06:44.039><c> compare</c>

00:06:44.330 --> 00:06:44.340 align:start position:0%
that we're going to ask is to compare
 

00:06:44.340 --> 00:06:46.370 align:start position:0%
that we're going to ask is to compare
and<00:06:44.460><c> contrast</c><00:06:44.940><c> the</c><00:06:45.300><c> customer</c><00:06:45.600><c> segments</c><00:06:46.199><c> and</c>

00:06:46.370 --> 00:06:46.380 align:start position:0%
and contrast the customer segments and
 

00:06:46.380 --> 00:06:49.490 align:start position:0%
and contrast the customer segments and
geographies<00:06:46.979><c> that</c><00:06:47.280><c> grew</c><00:06:47.580><c> the</c><00:06:47.759><c> fastest</c><00:06:48.500><c> over</c>

00:06:49.490 --> 00:06:49.500 align:start position:0%
geographies that grew the fastest over
 

00:06:49.500 --> 00:06:52.909 align:start position:0%
geographies that grew the fastest over
the<00:06:49.740><c> lift-in</c><00:06:50.340><c> Uber</c><00:06:51.000><c> documents</c><00:06:51.539><c> so</c><00:06:52.319><c> as</c><00:06:52.560><c> we</c><00:06:52.740><c> run</c>

00:06:52.909 --> 00:06:52.919 align:start position:0%
the lift-in Uber documents so as we run
 

00:06:52.919 --> 00:06:55.670 align:start position:0%
the lift-in Uber documents so as we run
these<00:06:53.340><c> you</c><00:06:53.940><c> should</c><00:06:54.120><c> be</c><00:06:54.240><c> able</c><00:06:54.360><c> to</c><00:06:54.479><c> see</c><00:06:54.900><c> various</c>

00:06:55.670 --> 00:06:55.680 align:start position:0%
these you should be able to see various
 

00:06:55.680 --> 00:06:57.529 align:start position:0%
these you should be able to see various
sub<00:06:55.860><c> questions</c><00:06:56.100><c> being</c><00:06:56.580><c> generated</c><00:06:57.120><c> and</c>

00:06:57.529 --> 00:06:57.539 align:start position:0%
sub questions being generated and
 

00:06:57.539 --> 00:07:00.430 align:start position:0%
sub questions being generated and
executed<00:06:58.199><c> on</c><00:06:58.620><c> the</c><00:06:58.860><c> sub</c><00:06:58.979><c> query</c><00:06:59.340><c> engines</c><00:06:59.699><c> before</c>

00:07:00.430 --> 00:07:00.440 align:start position:0%
executed on the sub query engines before
 

00:07:00.440 --> 00:07:04.070 align:start position:0%
executed on the sub query engines before
synthesizing<00:07:01.440><c> the</c><00:07:01.620><c> final</c><00:07:01.740><c> answer</c><00:07:02.060><c> okay</c><00:07:03.060><c> so</c><00:07:03.720><c> we</c>

00:07:04.070 --> 00:07:04.080 align:start position:0%
synthesizing the final answer okay so we
 

00:07:04.080 --> 00:07:06.170 align:start position:0%
synthesizing the final answer okay so we
generated<00:07:04.500><c> four</c><00:07:04.860><c> sub</c><00:07:05.100><c> questions</c><00:07:05.340><c> and</c><00:07:05.940><c> the</c>

00:07:06.170 --> 00:07:06.180 align:start position:0%
generated four sub questions and the
 

00:07:06.180 --> 00:07:08.689 align:start position:0%
generated four sub questions and the
first<00:07:06.360><c> one</c><00:07:06.539><c> is</c><00:07:06.840><c> what</c><00:07:07.440><c> customer</c><00:07:07.740><c> segments</c><00:07:08.400><c> grew</c>

00:07:08.689 --> 00:07:08.699 align:start position:0%
first one is what customer segments grew
 

00:07:08.699 --> 00:07:10.730 align:start position:0%
first one is what customer segments grew
the<00:07:08.819><c> fastest</c><00:07:09.060><c> for</c><00:07:09.300><c> Uber</c><00:07:09.660><c> being</c><00:07:10.020><c> asked</c><00:07:10.440><c> on</c><00:07:10.560><c> the</c>

00:07:10.730 --> 00:07:10.740 align:start position:0%
the fastest for Uber being asked on the
 

00:07:10.740 --> 00:07:13.010 align:start position:0%
the fastest for Uber being asked on the
Uber<00:07:10.979><c> 10K</c><00:07:11.340><c> documents</c>

00:07:13.010 --> 00:07:13.020 align:start position:0%
Uber 10K documents
 

00:07:13.020 --> 00:07:16.450 align:start position:0%
Uber 10K documents
um you<00:07:13.440><c> can</c><00:07:13.560><c> see</c><00:07:13.740><c> a</c><00:07:13.979><c> sub</c><00:07:14.639><c> response</c><00:07:15.120><c> generated</c>

00:07:16.450 --> 00:07:16.460 align:start position:0%
um you can see a sub response generated
 

00:07:16.460 --> 00:07:18.529 align:start position:0%
um you can see a sub response generated
another<00:07:17.460><c> question</c><00:07:17.819><c> being</c><00:07:18.060><c> asked</c><00:07:18.240><c> for</c><00:07:18.419><c> the</c>

00:07:18.529 --> 00:07:18.539 align:start position:0%
another question being asked for the
 

00:07:18.539 --> 00:07:20.809 align:start position:0%
another question being asked for the
Uber<00:07:18.780><c> 10K</c><00:07:19.139><c> about</c><00:07:19.380><c> what</c><00:07:19.740><c> geography</c><00:07:20.280><c> screw</c><00:07:20.699><c> the</c>

00:07:20.809 --> 00:07:20.819 align:start position:0%
Uber 10K about what geography screw the
 

00:07:20.819 --> 00:07:23.930 align:start position:0%
Uber 10K about what geography screw the
fastest<00:07:21.180><c> in</c><00:07:21.539><c> 2021</c><00:07:22.400><c> the</c><00:07:23.400><c> corresponding</c>

00:07:23.930 --> 00:07:23.940 align:start position:0%
fastest in 2021 the corresponding
 

00:07:23.940 --> 00:07:26.570 align:start position:0%
fastest in 2021 the corresponding
responses<00:07:24.660><c> the</c><00:07:25.199><c> customer</c><00:07:25.500><c> segments</c><00:07:26.099><c> for</c><00:07:26.220><c> Lyft</c>

00:07:26.570 --> 00:07:26.580 align:start position:0%
responses the customer segments for Lyft
 

00:07:26.580 --> 00:07:28.430 align:start position:0%
responses the customer segments for Lyft
and<00:07:27.120><c> then</c><00:07:27.240><c> the</c><00:07:27.360><c> geography</c><00:07:27.840><c> that</c><00:07:28.139><c> grew</c><00:07:28.380><c> the</c>

00:07:28.430 --> 00:07:28.440 align:start position:0%
and then the geography that grew the
 

00:07:28.440 --> 00:07:31.550 align:start position:0%
and then the geography that grew the
fastest<00:07:28.740><c> foreclosed</c><00:07:29.340><c> so</c><00:07:30.180><c> one</c><00:07:31.139><c> thing</c><00:07:31.259><c> to</c><00:07:31.440><c> know</c>

00:07:31.550 --> 00:07:31.560 align:start position:0%
fastest foreclosed so one thing to know
 

00:07:31.560 --> 00:07:35.330 align:start position:0%
fastest foreclosed so one thing to know
is<00:07:31.860><c> that</c><00:07:32.160><c> I</c><00:07:33.060><c> think</c><00:07:33.180><c> given</c><00:07:33.960><c> the</c><00:07:34.680><c> context</c><00:07:35.099><c> here</c>

00:07:35.330 --> 00:07:35.340 align:start position:0%
is that I think given the context here
 

00:07:35.340 --> 00:07:37.309 align:start position:0%
is that I think given the context here
the<00:07:36.000><c> model</c><00:07:36.180><c> actually</c><00:07:36.419><c> decided</c><00:07:36.900><c> that</c><00:07:37.080><c> not</c>

00:07:37.309 --> 00:07:37.319 align:start position:0%
the model actually decided that not
 

00:07:37.319 --> 00:07:38.930 align:start position:0%
the model actually decided that not
possible<00:07:37.680><c> to</c><00:07:37.860><c> answer</c><00:07:37.979><c> this</c><00:07:38.280><c> question</c><00:07:38.460><c> was</c><00:07:38.759><c> to</c>

00:07:38.930 --> 00:07:38.940 align:start position:0%
possible to answer this question was to
 

00:07:38.940 --> 00:07:41.330 align:start position:0%
possible to answer this question was to
give<00:07:39.060><c> in</c><00:07:39.240><c> the</c><00:07:39.360><c> context</c>

00:07:41.330 --> 00:07:41.340 align:start position:0%
give in the context
 

00:07:41.340 --> 00:07:43.309 align:start position:0%
give in the context
um<00:07:41.460><c> okay</c><00:07:42.000><c> now</c><00:07:42.300><c> that</c><00:07:42.599><c> all</c><00:07:42.780><c> the</c><00:07:42.960><c> sub</c><00:07:43.080><c> questions</c>

00:07:43.309 --> 00:07:43.319 align:start position:0%
um okay now that all the sub questions
 

00:07:43.319 --> 00:07:45.650 align:start position:0%
um okay now that all the sub questions
have<00:07:43.740><c> been</c><00:07:43.919><c> generated</c><00:07:44.460><c> all</c><00:07:44.940><c> of</c><00:07:45.180><c> these</c>

00:07:45.650 --> 00:07:45.660 align:start position:0%
have been generated all of these
 

00:07:45.660 --> 00:07:47.990 align:start position:0%
have been generated all of these
question<00:07:46.199><c> and</c><00:07:46.560><c> answer</c><00:07:46.860><c> pairs</c><00:07:47.400><c> will</c><00:07:47.639><c> be</c><00:07:47.759><c> used</c>

00:07:47.990 --> 00:07:48.000 align:start position:0%
question and answer pairs will be used
 

00:07:48.000 --> 00:07:51.050 align:start position:0%
question and answer pairs will be used
to<00:07:48.300><c> create</c><00:07:48.660><c> a</c><00:07:48.960><c> final</c><00:07:49.199><c> response</c><00:07:49.919><c> and</c><00:07:50.759><c> you</c><00:07:50.880><c> can</c>

00:07:51.050 --> 00:07:51.060 align:start position:0%
to create a final response and you can
 

00:07:51.060 --> 00:07:53.089 align:start position:0%
to create a final response and you can
see<00:07:51.300><c> yeah</c><00:07:51.840><c> the</c><00:07:52.139><c> large</c><00:07:52.380><c> language</c><00:07:52.620><c> model</c><00:07:52.919><c> is</c>

00:07:53.089 --> 00:07:53.099 align:start position:0%
see yeah the large language model is
 

00:07:53.099 --> 00:07:54.350 align:start position:0%
see yeah the large language model is
pretty<00:07:53.280><c> good</c><00:07:53.460><c> at</c><00:07:53.639><c> kind</c><00:07:53.880><c> of</c><00:07:54.000><c> stitching</c>

00:07:54.350 --> 00:07:54.360 align:start position:0%
pretty good at kind of stitching
 

00:07:54.360 --> 00:07:56.510 align:start position:0%
pretty good at kind of stitching
together<00:07:54.599><c> all</c><00:07:55.080><c> these</c><00:07:55.440><c> information</c><00:07:55.680><c> for</c><00:07:56.340><c> a</c>

00:07:56.510 --> 00:07:56.520 align:start position:0%
together all these information for a
 

00:07:56.520 --> 00:07:58.670 align:start position:0%
together all these information for a
final<00:07:56.759><c> synthesis</c><00:07:57.360><c> and</c><00:07:57.960><c> that</c><00:07:58.139><c> really</c><00:07:58.380><c> talks</c>

00:07:58.670 --> 00:07:58.680 align:start position:0%
final synthesis and that really talks
 

00:07:58.680 --> 00:07:59.689 align:start position:0%
final synthesis and that really talks
about

00:07:59.689 --> 00:07:59.699 align:start position:0%
about
 

00:07:59.699 --> 00:08:01.730 align:start position:0%
about
um the<00:08:00.180><c> various</c><00:08:00.419><c> aspects</c><00:08:00.960><c> comparing</c><00:08:01.500><c> between</c>

00:08:01.730 --> 00:08:01.740 align:start position:0%
um the various aspects comparing between
 

00:08:01.740 --> 00:08:03.710 align:start position:0%
um the various aspects comparing between
lift

00:08:03.710 --> 00:08:03.720 align:start position:0%
lift
 

00:08:03.720 --> 00:08:06.050 align:start position:0%
lift
now<00:08:04.080><c> just</c><00:08:04.319><c> to</c><00:08:04.500><c> Showcase</c><00:08:04.800><c> another</c><00:08:05.039><c> example</c><00:08:05.639><c> we</c>

00:08:06.050 --> 00:08:06.060 align:start position:0%
now just to Showcase another example we
 

00:08:06.060 --> 00:08:07.969 align:start position:0%
now just to Showcase another example we
can<00:08:06.180><c> compare</c><00:08:06.539><c> the</c><00:08:06.720><c> revenue</c><00:08:06.960><c> growth</c><00:08:07.380><c> of</c><00:08:07.560><c> uber</c>

00:08:07.969 --> 00:08:07.979 align:start position:0%
can compare the revenue growth of uber
 

00:08:07.979 --> 00:08:10.990 align:start position:0%
can compare the revenue growth of uber
and<00:08:08.220><c> Lyft</c><00:08:08.520><c> from</c><00:08:08.699><c> 2020</c><00:08:09.240><c> to</c><00:08:09.599><c> 2021</c>

00:08:10.990 --> 00:08:11.000 align:start position:0%
and Lyft from 2020 to 2021
 

00:08:11.000 --> 00:08:13.070 align:start position:0%
and Lyft from 2020 to 2021
this<00:08:12.000><c> should</c><00:08:12.180><c> be</c><00:08:12.300><c> a</c><00:08:12.419><c> pretty</c><00:08:12.599><c> straightforward</c>

00:08:13.070 --> 00:08:13.080 align:start position:0%
this should be a pretty straightforward
 

00:08:13.080 --> 00:08:16.210 align:start position:0%
this should be a pretty straightforward
answer<00:08:13.440><c> that</c><00:08:14.099><c> really</c><00:08:14.639><c> just</c><00:08:14.940><c> depends</c><00:08:15.479><c> on</c>

00:08:16.210 --> 00:08:16.220 align:start position:0%
answer that really just depends on
 

00:08:16.220 --> 00:08:20.930 align:start position:0%
answer that really just depends on
querying<00:08:17.220><c> the</c><00:08:17.520><c> right</c><00:08:17.900><c> index</c><00:08:19.039><c> using</c><00:08:20.039><c> the</c><00:08:20.759><c> right</c>

00:08:20.930 --> 00:08:20.940 align:start position:0%
querying the right index using the right
 

00:08:20.940 --> 00:08:23.450 align:start position:0%
querying the right index using the right
sub<00:08:21.479><c> question</c>

00:08:23.450 --> 00:08:23.460 align:start position:0%
sub question
 

00:08:23.460 --> 00:08:26.270 align:start position:0%
sub question
so<00:08:24.240><c> in</c><00:08:24.599><c> this</c><00:08:24.720><c> case</c><00:08:24.900><c> we</c><00:08:25.319><c> only</c><00:08:25.500><c> needed</c><00:08:25.919><c> two</c><00:08:26.099><c> sub</c>

00:08:26.270 --> 00:08:26.280 align:start position:0%
so in this case we only needed two sub
 

00:08:26.280 --> 00:08:28.969 align:start position:0%
so in this case we only needed two sub
questions<00:08:26.580><c> one</c><00:08:27.120><c> is</c><00:08:27.360><c> the</c><00:08:28.080><c> revenue</c><00:08:28.440><c> girls</c><00:08:28.680><c> of</c>

00:08:28.969 --> 00:08:28.979 align:start position:0%
questions one is the revenue girls of
 

00:08:28.979 --> 00:08:31.189 align:start position:0%
questions one is the revenue girls of
uber<00:08:29.280><c> being</c><00:08:29.520><c> as</c><00:08:29.759><c> an</c><00:08:29.940><c> Uber</c><00:08:30.300><c> document</c><00:08:30.599><c> and</c><00:08:30.960><c> the</c>

00:08:31.189 --> 00:08:31.199 align:start position:0%
uber being as an Uber document and the
 

00:08:31.199 --> 00:08:32.990 align:start position:0%
uber being as an Uber document and the
revenue<00:08:31.440><c> growth</c><00:08:31.740><c> of</c><00:08:31.860><c> lift</c><00:08:32.159><c> being</c><00:08:32.640><c> asked</c><00:08:32.880><c> on</c>

00:08:32.990 --> 00:08:33.000 align:start position:0%
revenue growth of lift being asked on
 

00:08:33.000 --> 00:08:35.870 align:start position:0%
revenue growth of lift being asked on
lift<00:08:33.300><c> document</c><00:08:33.680><c> and</c><00:08:34.680><c> as</c><00:08:34.919><c> you</c><00:08:35.039><c> can</c><00:08:35.159><c> see</c><00:08:35.279><c> we</c><00:08:35.700><c> have</c>

00:08:35.870 --> 00:08:35.880 align:start position:0%
lift document and as you can see we have
 

00:08:35.880 --> 00:08:38.269 align:start position:0%
lift document and as you can see we have
a<00:08:36.240><c> clear</c><00:08:36.839><c> comparison</c><00:08:37.380><c> between</c><00:08:37.680><c> the</c><00:08:37.919><c> two</c><00:08:38.039><c> as</c>

00:08:38.269 --> 00:08:38.279 align:start position:0%
a clear comparison between the two as
 

00:08:38.279 --> 00:08:40.550 align:start position:0%
a clear comparison between the two as
the<00:08:38.520><c> final</c><00:08:38.700><c> answer</c><00:08:39.000><c> where</c><00:08:39.659><c> Uber</c><00:08:40.020><c> had</c><00:08:40.200><c> a</c><00:08:40.320><c> higher</c>

00:08:40.550 --> 00:08:40.560 align:start position:0%
the final answer where Uber had a higher
 

00:08:40.560 --> 00:08:42.769 align:start position:0%
the final answer where Uber had a higher
rate<00:08:40.680><c> of</c><00:08:40.800><c> Revenue</c><00:08:41.039><c> growth</c><00:08:41.459><c> than</c><00:08:41.640><c> left</c>

00:08:42.769 --> 00:08:42.779 align:start position:0%
rate of Revenue growth than left
 

00:08:42.779 --> 00:08:45.530 align:start position:0%
rate of Revenue growth than left
cool<00:08:43.380><c> that's</c><00:08:44.279><c> all</c><00:08:44.580><c> hopefully</c><00:08:44.940><c> you</c><00:08:45.060><c> enjoyed</c>

00:08:45.530 --> 00:08:45.540 align:start position:0%
cool that's all hopefully you enjoyed
 

00:08:45.540 --> 00:08:48.200 align:start position:0%
cool that's all hopefully you enjoyed
this<00:08:45.720><c> tutorial</c>

