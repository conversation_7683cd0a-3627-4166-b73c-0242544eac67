WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:02.110 align:start position:0%
 
developers<00:00:00.640><c> in</c><00:00:00.880><c> every</c><00:00:01.199><c> industry</c><00:00:01.719><c> can</c><00:00:01.920><c> now</c>

00:00:02.110 --> 00:00:02.120 align:start position:0%
developers in every industry can now
 

00:00:02.120 --> 00:00:04.269 align:start position:0%
developers in every industry can now
unlock<00:00:02.560><c> the</c><00:00:02.679><c> power</c><00:00:02.960><c> of</c><00:00:03.159><c> arm</c><00:00:03.800><c> with</c><00:00:03.959><c> the</c><00:00:04.080><c> new</c>

00:00:04.269 --> 00:00:04.279 align:start position:0%
unlock the power of arm with the new
 

00:00:04.279 --> 00:00:06.909 align:start position:0%
unlock the power of arm with the new
hosted<00:00:04.640><c> runners</c><00:00:05.040><c> in</c><00:00:05.240><c> GitHub</c><00:00:05.640><c> actions</c><00:00:06.600><c> the</c><00:00:06.720><c> new</c>

00:00:06.909 --> 00:00:06.919 align:start position:0%
hosted runners in GitHub actions the new
 

00:00:06.919 --> 00:00:09.110 align:start position:0%
hosted runners in GitHub actions the new
Runners<00:00:07.399><c> offer</c><00:00:07.799><c> significant</c><00:00:08.679><c> power</c>

00:00:09.110 --> 00:00:09.120 align:start position:0%
Runners offer significant power
 

00:00:09.120 --> 00:00:10.749 align:start position:0%
Runners offer significant power
performance<00:00:09.719><c> and</c><00:00:09.960><c> sustainability</c>

00:00:10.749 --> 00:00:10.759 align:start position:0%
performance and sustainability
 

00:00:10.759 --> 00:00:13.270 align:start position:0%
performance and sustainability
improvements<00:00:11.759><c> and</c><00:00:12.040><c> increased</c><00:00:12.679><c> price</c><00:00:13.040><c> to</c>

00:00:13.270 --> 00:00:13.280 align:start position:0%
improvements and increased price to
 

00:00:13.280 --> 00:00:16.150 align:start position:0%
improvements and increased price to
Performance<00:00:13.879><c> ratios</c><00:00:14.879><c> these</c><00:00:15.080><c> Runners</c><00:00:16.000><c> are</c>

00:00:16.150 --> 00:00:16.160 align:start position:0%
Performance ratios these Runners are
 

00:00:16.160 --> 00:00:18.429 align:start position:0%
Performance ratios these Runners are
fully<00:00:16.520><c> managed</c><00:00:17.000><c> by</c><00:00:17.160><c> GitHub</c><00:00:17.720><c> with</c><00:00:17.880><c> an</c><00:00:18.039><c> image</c>

00:00:18.429 --> 00:00:18.439 align:start position:0%
fully managed by GitHub with an image
 

00:00:18.439 --> 00:00:21.150 align:start position:0%
fully managed by GitHub with an image
built<00:00:18.760><c> by</c><00:00:18.920><c> arm</c><00:00:19.640><c> containing</c><00:00:20.279><c> all</c><00:00:20.480><c> the</c><00:00:20.640><c> tools</c><00:00:20.960><c> to</c>

00:00:21.150 --> 00:00:21.160 align:start position:0%
built by arm containing all the tools to
 

00:00:21.160 --> 00:00:24.070 align:start position:0%
built by arm containing all the tools to
help<00:00:21.400><c> developers</c><00:00:22.039><c> get</c><00:00:22.240><c> started</c><00:00:23.080><c> faster</c>

00:00:24.070 --> 00:00:24.080 align:start position:0%
help developers get started faster
 

00:00:24.080 --> 00:00:26.029 align:start position:0%
help developers get started faster
setting<00:00:24.400><c> up</c><00:00:24.599><c> the</c><00:00:24.720><c> GitHub</c><00:00:25.080><c> hosted</c><00:00:25.359><c> arm</c><00:00:25.599><c> Runners</c>

00:00:26.029 --> 00:00:26.039 align:start position:0%
setting up the GitHub hosted arm Runners
 

00:00:26.039 --> 00:00:28.230 align:start position:0%
setting up the GitHub hosted arm Runners
is<00:00:26.199><c> simple</c><00:00:27.119><c> navigate</c><00:00:27.599><c> to</c><00:00:27.720><c> the</c><00:00:27.880><c> GitHub</c>

00:00:28.230 --> 00:00:28.240 align:start position:0%
is simple navigate to the GitHub
 

00:00:28.240 --> 00:00:31.710 align:start position:0%
is simple navigate to the GitHub
organization<00:00:28.920><c> settings</c>

00:00:31.710 --> 00:00:31.720 align:start position:0%
organization settings
 

00:00:31.720 --> 00:00:33.430 align:start position:0%
organization settings
expand<00:00:32.119><c> the</c><00:00:32.239><c> general</c><00:00:32.559><c> settings</c><00:00:32.880><c> for</c><00:00:33.079><c> GitHub</c>

00:00:33.430 --> 00:00:33.440 align:start position:0%
expand the general settings for GitHub
 

00:00:33.440 --> 00:00:36.510 align:start position:0%
expand the general settings for GitHub
actions<00:00:34.200><c> click</c><00:00:34.440><c> on</c>

00:00:36.510 --> 00:00:36.520 align:start position:0%
actions click on
 

00:00:36.520 --> 00:00:39.310 align:start position:0%
actions click on
Runners<00:00:37.520><c> select</c><00:00:38.040><c> new</c><00:00:38.239><c> GitHub</c><00:00:38.600><c> hosted</c><00:00:38.920><c> Runner</c>

00:00:39.310 --> 00:00:39.320 align:start position:0%
Runners select new GitHub hosted Runner
 

00:00:39.320 --> 00:00:44.750 align:start position:0%
Runners select new GitHub hosted Runner
under<00:00:39.680><c> new</c><00:00:39.840><c> Runner</c><00:00:40.640><c> specify</c><00:00:41.280><c> a</c><00:00:41.480><c> name</c><00:00:41.719><c> for</c><00:00:41.960><c> the</c>

00:00:44.750 --> 00:00:44.760 align:start position:0%
 
 

00:00:44.760 --> 00:00:48.470 align:start position:0%
 
runner<00:00:45.760><c> you</c><00:00:46.000><c> can</c><00:00:46.199><c> select</c><00:00:46.800><c> either</c><00:00:47.120><c> Linux</c><00:00:47.520><c> arm64</c>

00:00:48.470 --> 00:00:48.480 align:start position:0%
runner you can select either Linux arm64
 

00:00:48.480 --> 00:00:51.029 align:start position:0%
runner you can select either Linux arm64
or<00:00:48.719><c> Windows</c><00:00:49.079><c> arm64</c>

00:00:51.029 --> 00:00:51.039 align:start position:0%
or Windows arm64
 

00:00:51.039 --> 00:00:54.029 align:start position:0%
or Windows arm64
platform<00:00:52.039><c> click</c>

00:00:54.029 --> 00:00:54.039 align:start position:0%
platform click
 

00:00:54.039 --> 00:00:57.750 align:start position:0%
platform click
save<00:00:55.039><c> you</c><00:00:55.160><c> can</c><00:00:55.359><c> filter</c><00:00:55.840><c> by</c><00:00:56.280><c> and</c><00:00:57.199><c> select</c><00:00:57.559><c> a</c>

00:00:57.750 --> 00:00:57.760 align:start position:0%
save you can filter by and select a
 

00:00:57.760 --> 00:01:00.709 align:start position:0%
save you can filter by and select a
partner<00:00:58.120><c> image</c><00:00:58.559><c> provided</c><00:00:58.960><c> by</c><00:00:59.160><c> arm</c>

00:01:00.709 --> 00:01:00.719 align:start position:0%
partner image provided by arm
 

00:01:00.719 --> 00:01:01.590 align:start position:0%
partner image provided by arm
click

00:01:01.590 --> 00:01:01.600 align:start position:0%
click
 

00:01:01.600 --> 00:01:04.829 align:start position:0%
click
save<00:01:02.600><c> now</c><00:01:03.160><c> select</c><00:01:03.519><c> the</c><00:01:03.640><c> machine</c><00:01:04.080><c> size</c><00:01:04.600><c> and</c>

00:01:04.829 --> 00:01:04.839 align:start position:0%
save now select the machine size and
 

00:01:04.839 --> 00:01:08.429 align:start position:0%
save now select the machine size and
click<00:01:05.520><c> save</c><00:01:06.520><c> you</c><00:01:06.680><c> can</c><00:01:06.920><c> choose</c><00:01:07.439><c> the</c><00:01:07.640><c> maximum</c>

00:01:08.429 --> 00:01:08.439 align:start position:0%
click save you can choose the maximum
 

00:01:08.439 --> 00:01:10.950 align:start position:0%
click save you can choose the maximum
concurrency<00:01:09.439><c> to</c><00:01:09.759><c> specify</c><00:01:10.360><c> the</c><00:01:10.479><c> number</c><00:01:10.759><c> of</c>

00:01:10.950 --> 00:01:10.960 align:start position:0%
concurrency to specify the number of
 

00:01:10.960 --> 00:01:13.830 align:start position:0%
concurrency to specify the number of
jobs<00:01:11.400><c> that</c><00:01:11.560><c> can</c><00:01:11.720><c> run</c><00:01:12.000><c> at</c><00:01:12.159><c> the</c><00:01:12.280><c> same</c><00:01:12.720><c> time</c><00:01:13.720><c> you</c>

00:01:13.830 --> 00:01:13.840 align:start position:0%
jobs that can run at the same time you
 

00:01:13.840 --> 00:01:16.310 align:start position:0%
jobs that can run at the same time you
can<00:01:14.159><c> also</c><00:01:14.520><c> specify</c><00:01:14.960><c> a</c><00:01:15.080><c> runner</c><00:01:15.400><c> group</c><00:01:15.680><c> under</c>

00:01:16.310 --> 00:01:16.320 align:start position:0%
can also specify a runner group under
 

00:01:16.320 --> 00:01:18.710 align:start position:0%
can also specify a runner group under
permissions<00:01:17.320><c> and</c><00:01:17.600><c> assign</c><00:01:17.920><c> a</c><00:01:18.119><c> unique</c><00:01:18.520><c> and</c>

00:01:18.710 --> 00:01:18.720 align:start position:0%
permissions and assign a unique and
 

00:01:18.720 --> 00:01:21.789 align:start position:0%
permissions and assign a unique and
static<00:01:19.159><c> public</c><00:01:19.479><c> IP</c><00:01:19.799><c> address</c><00:01:20.159><c> for</c><00:01:20.439><c> this</c><00:01:20.799><c> Runner</c>

00:01:21.789 --> 00:01:21.799 align:start position:0%
static public IP address for this Runner
 

00:01:21.799 --> 00:01:23.190 align:start position:0%
static public IP address for this Runner
click<00:01:22.159><c> create</c>

00:01:23.190 --> 00:01:23.200 align:start position:0%
click create
 

00:01:23.200 --> 00:01:25.990 align:start position:0%
click create
Runner<00:01:24.200><c> your</c><00:01:24.439><c> Runner</c><00:01:24.960><c> is</c><00:01:25.200><c> now</c><00:01:25.439><c> ready</c><00:01:25.680><c> to</c><00:01:25.840><c> be</c>

00:01:25.990 --> 00:01:26.000 align:start position:0%
Runner your Runner is now ready to be
 

00:01:26.000 --> 00:01:27.950 align:start position:0%
Runner your Runner is now ready to be
used<00:01:26.360><c> by</c><00:01:26.560><c> developers</c><00:01:27.159><c> across</c><00:01:27.479><c> your</c>

00:01:27.950 --> 00:01:27.960 align:start position:0%
used by developers across your
 

00:01:27.960 --> 00:01:31.190 align:start position:0%
used by developers across your
organization<00:01:28.960><c> simply</c><00:01:29.520><c> copy</c><00:01:30.040><c> the</c><00:01:30.159><c> label</c><00:01:30.960><c> and</c>

00:01:31.190 --> 00:01:31.200 align:start position:0%
organization simply copy the label and
 

00:01:31.200 --> 00:01:33.510 align:start position:0%
organization simply copy the label and
update<00:01:31.600><c> the</c><00:01:31.759><c> runson</c><00:01:32.399><c> key</c><00:01:32.720><c> in</c><00:01:32.880><c> your</c><00:01:33.119><c> actions</c>

00:01:33.510 --> 00:01:33.520 align:start position:0%
update the runson key in your actions
 

00:01:33.520 --> 00:01:35.030 align:start position:0%
update the runson key in your actions
workflow<00:01:34.040><c> yaml</c>

00:01:35.030 --> 00:01:35.040 align:start position:0%
workflow yaml
 

00:01:35.040 --> 00:01:37.789 align:start position:0%
workflow yaml
file<00:01:36.040><c> you</c><00:01:36.200><c> can</c><00:01:36.439><c> control</c><00:01:36.880><c> permissions</c><00:01:37.360><c> for</c><00:01:37.560><c> arm</c>

00:01:37.789 --> 00:01:37.799 align:start position:0%
file you can control permissions for arm
 

00:01:37.799 --> 00:01:40.190 align:start position:0%
file you can control permissions for arm
Runners<00:01:38.200><c> through</c><00:01:38.399><c> the</c><00:01:38.520><c> runner</c><00:01:38.840><c> group</c><00:01:39.799><c> you</c><00:01:39.960><c> can</c>

00:01:40.190 --> 00:01:40.200 align:start position:0%
Runners through the runner group you can
 

00:01:40.200 --> 00:01:42.870 align:start position:0%
Runners through the runner group you can
specify<00:01:41.159><c> what</c><00:01:41.439><c> repositories</c><00:01:42.240><c> can</c><00:01:42.479><c> access</c><00:01:42.759><c> the</c>

00:01:42.870 --> 00:01:42.880 align:start position:0%
specify what repositories can access the
 

00:01:42.880 --> 00:01:45.389 align:start position:0%
specify what repositories can access the
runners<00:01:43.799><c> and</c><00:01:44.119><c> restrict</c><00:01:44.560><c> the</c><00:01:44.640><c> runners</c><00:01:45.079><c> to</c>

00:01:45.389 --> 00:01:45.399 align:start position:0%
runners and restrict the runners to
 

00:01:45.399 --> 00:01:48.030 align:start position:0%
runners and restrict the runners to
specific<00:01:45.920><c> workflows</c><00:01:46.920><c> you</c><00:01:47.040><c> can</c><00:01:47.320><c> also</c><00:01:47.600><c> set</c><00:01:47.920><c> the</c>

00:01:48.030 --> 00:01:48.040 align:start position:0%
specific workflows you can also set the
 

00:01:48.040 --> 00:01:50.550 align:start position:0%
specific workflows you can also set the
network<00:01:48.439><c> configurations</c><00:01:49.240><c> here</c><00:01:49.960><c> in</c><00:01:50.119><c> order</c><00:01:50.399><c> to</c>

00:01:50.550 --> 00:01:50.560 align:start position:0%
network configurations here in order to
 

00:01:50.560 --> 00:01:54.200 align:start position:0%
network configurations here in order to
learn<00:01:50.840><c> more</c><00:01:51.280><c> click</c><00:01:51.600><c> this</c><00:01:51.799><c> link</c>

