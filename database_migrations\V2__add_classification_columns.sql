-- Migration to add classification columns to youtube_renewable_energy table
-- This adds columns for AI-based content classification for public display

-- Add the ai_eval_visibility column to store AI classification results
ALTER TABLE youtube_renewable_energy ADD COLUMN IF NOT EXISTS ai_eval_visibility BOOLEAN;

-- Add the visibility_congruent column to track agreement between AI and existing is_visible column
ALTER TABLE youtube_renewable_energy ADD COLUMN IF NOT EXISTS visibility_congruent BOOLEAN;

-- Add comments to document the columns
COMMENT ON COLUMN youtube_renewable_energy.ai_eval_visibility IS 'AI classification result: TRUE if content should be visible on public website, FALSE if it should be hidden (competing services, non-energy content, etc.)';
COMMENT ON COLUMN youtube_renewable_energy.visibility_congruent IS 'TRUE if ai_eval_visibility matches is_visible column, FALSE if they disagree';

-- Create an index for efficient querying of classification results
CREATE INDEX IF NOT EXISTS idx_youtube_renewable_energy_ai_eval_visibility ON youtube_renewable_energy(ai_eval_visibility);
CREATE INDEX IF NOT EXISTS idx_youtube_renewable_energy_visibility_congruent ON youtube_renewable_energy(visibility_congruent);
