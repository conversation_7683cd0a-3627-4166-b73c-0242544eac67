WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:03.110 align:start position:0%
 
welcome<00:00:00.390><c> to</c><00:00:00.539><c> another</c><00:00:00.780><c> video</c><00:00:01.879><c> today's</c><00:00:02.879><c> video</c>

00:00:03.110 --> 00:00:03.120 align:start position:0%
welcome to another video today's video
 

00:00:03.120 --> 00:00:10.040 align:start position:0%
welcome to another video today's video
we're<00:00:03.780><c> gonna</c><00:00:03.929><c> be</c><00:00:04.020><c> talking</c><00:00:04.670><c> about</c><00:00:08.690><c> an</c><00:00:09.690><c> open</c>

00:00:10.040 --> 00:00:10.050 align:start position:0%
we're gonna be talking about an open
 

00:00:10.050 --> 00:00:12.560 align:start position:0%
we're gonna be talking about an open
source<00:00:10.290><c> project</c><00:00:10.950><c> that</c><00:00:11.040><c> I'm</c><00:00:11.280><c> I'm</c><00:00:12.269><c> currently</c>

00:00:12.560 --> 00:00:12.570 align:start position:0%
source project that I'm I'm currently
 

00:00:12.570 --> 00:00:15.620 align:start position:0%
source project that I'm I'm currently
writing<00:00:12.900><c> it's</c><00:00:13.259><c> called</c><00:00:13.500><c> koala</c><00:00:13.950><c> CMS</c><00:00:14.639><c> I</c><00:00:14.940><c> need</c><00:00:15.509><c> to</c>

00:00:15.620 --> 00:00:15.630 align:start position:0%
writing it's called koala CMS I need to
 

00:00:15.630 --> 00:00:17.029 align:start position:0%
writing it's called koala CMS I need to
change<00:00:15.870><c> the</c><00:00:16.080><c> name</c><00:00:16.199><c> over</c><00:00:16.379><c> here</c><00:00:16.680><c> and</c><00:00:16.830><c> let's</c>

00:00:17.029 --> 00:00:17.039 align:start position:0%
change the name over here and let's
 

00:00:17.039 --> 00:00:18.590 align:start position:0%
change the name over here and let's
actually<00:00:17.190><c> do</c><00:00:17.550><c> that</c><00:00:17.580><c> together</c><00:00:18.060><c> now</c>

00:00:18.590 --> 00:00:18.600 align:start position:0%
actually do that together now
 

00:00:18.600 --> 00:00:24.410 align:start position:0%
actually do that together now
the<00:00:19.289><c> bodø</c><00:00:19.640><c> issues</c><00:00:20.640><c> settings</c><00:00:23.180><c> can</c><00:00:24.180><c> always</c>

00:00:24.410 --> 00:00:24.420 align:start position:0%
the bodø issues settings can always
 

00:00:24.420 --> 00:00:27.620 align:start position:0%
the bodø issues settings can always
change<00:00:24.949><c> but</c><00:00:25.949><c> if</c><00:00:26.039><c> I</c><00:00:26.130><c> do</c><00:00:26.189><c> that</c><00:00:26.519><c> it's</c><00:00:27.269><c> gonna</c><00:00:27.390><c> also</c>

00:00:27.620 --> 00:00:27.630 align:start position:0%
change but if I do that it's gonna also
 

00:00:27.630 --> 00:00:33.139 align:start position:0%
change but if I do that it's gonna also
change<00:00:28.080><c> the</c><00:00:28.349><c> name</c><00:00:31.700><c> depository</c><00:00:32.700><c> name</c><00:00:32.910><c> you</c><00:00:33.059><c> know</c>

00:00:33.139 --> 00:00:33.149 align:start position:0%
change the name depository name you know
 

00:00:33.149 --> 00:00:37.670 align:start position:0%
change the name depository name you know
what<00:00:33.329><c> I</c><00:00:35.270><c> do</c><00:00:36.270><c> it</c><00:00:36.420><c> it's</c><00:00:36.750><c> gonna</c><00:00:36.870><c> change</c><00:00:36.989><c> all</c><00:00:37.440><c> the</c>

00:00:37.670 --> 00:00:37.680 align:start position:0%
what I do it it's gonna change all the
 

00:00:37.680 --> 00:00:39.830 align:start position:0%
what I do it it's gonna change all the
links<00:00:38.250><c> within</c><00:00:38.460><c> the</c><00:00:38.670><c> YouTube</c><00:00:39.000><c> videos</c><00:00:39.450><c> I</c><00:00:39.780><c> don't</c>

00:00:39.830 --> 00:00:39.840 align:start position:0%
links within the YouTube videos I don't
 

00:00:39.840 --> 00:00:41.319 align:start position:0%
links within the YouTube videos I don't
know<00:00:40.020><c> whether</c><00:00:40.200><c> I</c><00:00:40.290><c> want</c><00:00:40.379><c> to</c><00:00:40.590><c> do</c><00:00:40.710><c> that</c>

00:00:41.319 --> 00:00:41.329 align:start position:0%
know whether I want to do that
 

00:00:41.329 --> 00:00:44.299 align:start position:0%
know whether I want to do that
well<00:00:42.329><c> whatever</c><00:00:42.660><c> my</c><00:00:43.290><c> sequel</c><00:00:43.649><c> note</c><00:00:43.860><c> angular</c>

00:00:44.299 --> 00:00:44.309 align:start position:0%
well whatever my sequel note angular
 

00:00:44.309 --> 00:00:45.860 align:start position:0%
well whatever my sequel note angular
that's<00:00:44.700><c> the</c><00:00:44.879><c> name</c><00:00:45.120><c> of</c><00:00:45.270><c> the</c><00:00:45.329><c> project</c><00:00:45.539><c> cuz</c>

00:00:45.860 --> 00:00:45.870 align:start position:0%
that's the name of the project cuz
 

00:00:45.870 --> 00:00:48.610 align:start position:0%
that's the name of the project cuz
that's<00:00:46.020><c> pretty</c><00:00:46.230><c> much</c><00:00:46.379><c> that</c>

00:00:48.610 --> 00:00:48.620 align:start position:0%
 
 

00:00:48.620 --> 00:00:50.840 align:start position:0%
 
technologies<00:00:49.620><c> that's</c><00:00:49.890><c> really</c><00:00:50.190><c> not</c><00:00:50.340><c> hard</c><00:00:50.520><c> and</c>

00:00:50.840 --> 00:00:50.850 align:start position:0%
technologies that's really not hard and
 

00:00:50.850 --> 00:00:54.560 align:start position:0%
technologies that's really not hard and
the<00:00:51.300><c> the</c><00:00:51.870><c> veins</c><00:00:52.140><c> of</c><00:00:53.010><c> this</c><00:00:53.370><c> of</c><00:00:53.640><c> this</c><00:00:53.969><c> project</c>

00:00:54.560 --> 00:00:54.570 align:start position:0%
the the veins of this of this project
 

00:00:54.570 --> 00:00:57.500 align:start position:0%
the the veins of this of this project
it's<00:00:54.719><c> my</c><00:00:54.899><c> sequel</c><00:00:55.289><c> node</c><00:00:55.500><c> in</c><00:00:55.680><c> angular</c><00:00:56.219><c> and</c><00:00:56.510><c> in</c>

00:00:57.500 --> 00:00:57.510 align:start position:0%
it's my sequel node in angular and in
 

00:00:57.510 --> 00:01:01.099 align:start position:0%
it's my sequel node in angular and in
order<00:00:57.899><c> to</c><00:00:58.399><c> wow</c><00:00:59.430><c> sorry</c><00:00:59.850><c> while</c><00:01:00.120><c> we're</c><00:01:00.870><c> going</c>

00:01:01.099 --> 00:01:01.109 align:start position:0%
order to wow sorry while we're going
 

00:01:01.109 --> 00:01:04.870 align:start position:0%
order to wow sorry while we're going
through<00:01:01.289><c> all</c><00:01:01.440><c> these</c><00:01:01.800><c> steps</c><00:01:02.160><c> of</c><00:01:02.460><c> learning</c>

00:01:04.870 --> 00:01:04.880 align:start position:0%
through all these steps of learning
 

00:01:04.880 --> 00:01:09.530 align:start position:0%
through all these steps of learning
learning<00:01:05.880><c> all</c><00:01:06.180><c> about</c><00:01:07.729><c> how</c><00:01:08.729><c> my</c><00:01:08.909><c> sequel</c><00:01:09.330><c> works</c>

00:01:09.530 --> 00:01:09.540 align:start position:0%
learning all about how my sequel works
 

00:01:09.540 --> 00:01:11.539 align:start position:0%
learning all about how my sequel works
having<00:01:09.810><c> node</c><00:01:10.020><c> works</c><00:01:10.260><c> how</c><00:01:10.500><c> angularjs</c><00:01:11.280><c> words</c>

00:01:11.539 --> 00:01:11.549 align:start position:0%
having node works how angularjs words
 

00:01:11.549 --> 00:01:13.550 align:start position:0%
having node works how angularjs words
were<00:01:11.700><c> also</c><00:01:11.939><c> taking</c><00:01:12.450><c> the</c><00:01:12.540><c> steps</c><00:01:12.810><c> to</c><00:01:13.020><c> look</c><00:01:13.439><c> at</c>

00:01:13.550 --> 00:01:13.560 align:start position:0%
were also taking the steps to look at
 

00:01:13.560 --> 00:01:16.850 align:start position:0%
were also taking the steps to look at
other<00:01:13.770><c> videos</c><00:01:14.250><c> along</c><00:01:14.729><c> with</c><00:01:15.650><c> with</c><00:01:16.650><c> this</c>

00:01:16.850 --> 00:01:16.860 align:start position:0%
other videos along with with this
 

00:01:16.860 --> 00:01:18.230 align:start position:0%
other videos along with with this
technology<00:01:17.520><c> and</c><00:01:17.640><c> that's</c><00:01:17.729><c> why</c><00:01:17.880><c> we're</c><00:01:18.030><c> also</c>

00:01:18.230 --> 00:01:18.240 align:start position:0%
technology and that's why we're also
 

00:01:18.240 --> 00:01:22.010 align:start position:0%
technology and that's why we're also
looking<00:01:18.540><c> at</c><00:01:19.939><c> something</c><00:01:20.939><c> like</c><00:01:21.150><c> meta</c><00:01:21.450><c> base</c><00:01:21.689><c> for</c>

00:01:22.010 --> 00:01:22.020 align:start position:0%
looking at something like meta base for
 

00:01:22.020 --> 00:01:23.359 align:start position:0%
looking at something like meta base for
example<00:01:22.470><c> if</c><00:01:22.560><c> you</c><00:01:22.650><c> look</c><00:01:22.799><c> at</c><00:01:22.920><c> the</c><00:01:23.040><c> YouTube</c>

00:01:23.359 --> 00:01:23.369 align:start position:0%
example if you look at the YouTube
 

00:01:23.369 --> 00:01:24.950 align:start position:0%
example if you look at the YouTube
channel<00:01:23.729><c> live</c><00:01:23.880><c> startup</c><00:01:24.390><c> series</c><00:01:24.570><c> you're</c><00:01:24.780><c> gonna</c>

00:01:24.950 --> 00:01:24.960 align:start position:0%
channel live startup series you're gonna
 

00:01:24.960 --> 00:01:25.999 align:start position:0%
channel live startup series you're gonna
see<00:01:25.110><c> that</c><00:01:25.259><c> there's</c><00:01:25.439><c> something</c><00:01:25.560><c> called</c><00:01:25.680><c> meta</c>

00:01:25.999 --> 00:01:26.009 align:start position:0%
see that there's something called meta
 

00:01:26.009 --> 00:01:27.649 align:start position:0%
see that there's something called meta
base<00:01:26.189><c> tutorial</c><00:01:26.700><c> that's</c><00:01:26.850><c> quickly</c><00:01:27.299><c> getting</c><00:01:27.570><c> a</c>

00:01:27.649 --> 00:01:27.659 align:start position:0%
base tutorial that's quickly getting a
 

00:01:27.659 --> 00:01:28.789 align:start position:0%
base tutorial that's quickly getting a
lot<00:01:27.810><c> of</c><00:01:27.840><c> views</c><00:01:28.140><c> a</c><00:01:28.259><c> lot</c><00:01:28.350><c> of</c><00:01:28.409><c> people</c><00:01:28.680><c> are</c>

00:01:28.789 --> 00:01:28.799 align:start position:0%
lot of views a lot of people are
 

00:01:28.799 --> 00:01:30.499 align:start position:0%
lot of views a lot of people are
interested<00:01:29.220><c> in</c><00:01:29.280><c> that</c><00:01:29.460><c> it's</c><00:01:30.090><c> pretty</c><00:01:30.240><c> much</c><00:01:30.420><c> an</c>

00:01:30.499 --> 00:01:30.509 align:start position:0%
interested in that it's pretty much an
 

00:01:30.509 --> 00:01:33.170 align:start position:0%
interested in that it's pretty much an
open<00:01:30.689><c> source</c><00:01:31.200><c> CMS</c><00:01:31.590><c> I</c><00:01:32.159><c> have</c><00:01:32.310><c> other</c><00:01:32.460><c> videos</c><00:01:32.880><c> on</c>

00:01:33.170 --> 00:01:33.180 align:start position:0%
open source CMS I have other videos on
 

00:01:33.180 --> 00:01:34.460 align:start position:0%
open source CMS I have other videos on
the<00:01:33.299><c> customers</c><00:01:33.720><c> online</c><00:01:33.900><c> journey</c><00:01:34.320><c> with</c>

00:01:34.460 --> 00:01:34.470 align:start position:0%
the customers online journey with
 

00:01:34.470 --> 00:01:35.480 align:start position:0%
the customers online journey with
everything<00:01:34.979><c> you</c><00:01:35.070><c> need</c><00:01:35.100><c> to</c><00:01:35.220><c> know</c><00:01:35.400><c> about</c>

00:01:35.480 --> 00:01:35.490 align:start position:0%
everything you need to know about
 

00:01:35.490 --> 00:01:37.010 align:start position:0%
everything you need to know about
digital<00:01:35.970><c> marketing</c><00:01:36.119><c> I</c><00:01:36.509><c> might</c><00:01:36.720><c> do</c><00:01:36.840><c> some</c>

00:01:37.010 --> 00:01:37.020 align:start position:0%
digital marketing I might do some
 

00:01:37.020 --> 00:01:38.660 align:start position:0%
digital marketing I might do some
digital<00:01:37.439><c> marketing</c><00:01:37.799><c> videos</c><00:01:38.130><c> in</c><00:01:38.310><c> the</c><00:01:38.430><c> past</c>

00:01:38.660 --> 00:01:38.670 align:start position:0%
digital marketing videos in the past
 

00:01:38.670 --> 00:01:41.300 align:start position:0%
digital marketing videos in the past
that's<00:01:39.329><c> pretty</c><00:01:39.600><c> much</c><00:01:39.780><c> the</c><00:01:40.079><c> field</c><00:01:41.040><c> that</c><00:01:41.189><c> I'm</c>

00:01:41.300 --> 00:01:41.310 align:start position:0%
that's pretty much the field that I'm
 

00:01:41.310 --> 00:01:42.950 align:start position:0%
that's pretty much the field that I'm
coming<00:01:41.520><c> from</c><00:01:41.820><c> originally</c><00:01:42.479><c> I</c><00:01:42.509><c> came</c><00:01:42.780><c> from</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
coming from originally I came from
 

00:01:42.960 --> 00:01:45.410 align:start position:0%
coming from originally I came from
digital<00:01:43.530><c> marketing</c><00:01:43.950><c> I</c><00:01:44.189><c> did</c><00:01:44.759><c> a</c><00:01:44.790><c> three</c><00:01:45.090><c> month</c>

00:01:45.410 --> 00:01:45.420 align:start position:0%
digital marketing I did a three month
 

00:01:45.420 --> 00:01:47.480 align:start position:0%
digital marketing I did a three month
coding<00:01:45.930><c> course</c><00:01:46.229><c> and</c><00:01:46.590><c> then</c><00:01:46.710><c> I</c><00:01:46.740><c> started</c><00:01:47.310><c> getting</c>

00:01:47.480 --> 00:01:47.490 align:start position:0%
coding course and then I started getting
 

00:01:47.490 --> 00:01:51.020 align:start position:0%
coding course and then I started getting
very<00:01:47.759><c> interested</c><00:01:48.360><c> in</c><00:01:48.509><c> in</c><00:01:49.250><c> making</c><00:01:50.250><c> videos</c><00:01:50.460><c> and</c>

00:01:51.020 --> 00:01:51.030 align:start position:0%
very interested in in making videos and
 

00:01:51.030 --> 00:01:53.090 align:start position:0%
very interested in in making videos and
tutorials<00:01:51.180><c> I'm</c><00:01:52.170><c> also</c><00:01:52.380><c> working</c><00:01:52.710><c> for</c><00:01:53.070><c> a</c>

00:01:53.090 --> 00:01:53.100 align:start position:0%
tutorials I'm also working for a
 

00:01:53.100 --> 00:01:56.749 align:start position:0%
tutorials I'm also working for a
start-up<00:01:53.280><c> currently</c><00:01:53.939><c> and</c><00:01:54.060><c> I</c><00:01:54.960><c> think</c><00:01:55.860><c> that</c><00:01:56.159><c> I'm</c>

00:01:56.749 --> 00:01:56.759 align:start position:0%
start-up currently and I think that I'm
 

00:01:56.759 --> 00:02:00.469 align:start position:0%
start-up currently and I think that I'm
able<00:01:57.060><c> to</c><00:01:58.219><c> learn</c><00:01:59.219><c> a</c><00:01:59.280><c> lot</c><00:01:59.520><c> on</c><00:01:59.700><c> the</c><00:01:59.820><c> job</c><00:02:00.060><c> in</c><00:02:00.270><c> terms</c>

00:02:00.469 --> 00:02:00.479 align:start position:0%
able to learn a lot on the job in terms
 

00:02:00.479 --> 00:02:02.480 align:start position:0%
able to learn a lot on the job in terms
of<00:02:00.689><c> how</c><00:02:01.320><c> you</c><00:02:01.380><c> get</c><00:02:01.590><c> the</c><00:02:01.740><c> product</c><00:02:02.100><c> market</c><00:02:02.460><c> fit</c>

00:02:02.480 --> 00:02:02.490 align:start position:0%
of how you get the product market fit
 

00:02:02.490 --> 00:02:05.090 align:start position:0%
of how you get the product market fit
how<00:02:03.119><c> you</c><00:02:03.180><c> actually</c><00:02:03.689><c> have</c><00:02:04.140><c> a</c><00:02:04.170><c> technology</c><00:02:04.560><c> stack</c>

00:02:05.090 --> 00:02:05.100 align:start position:0%
how you actually have a technology stack
 

00:02:05.100 --> 00:02:08.330 align:start position:0%
how you actually have a technology stack
which<00:02:05.280><c> supports</c><00:02:05.790><c> all</c><00:02:05.909><c> of</c><00:02:06.090><c> your</c><00:02:06.240><c> needs</c><00:02:06.509><c> as</c><00:02:07.310><c> as</c><00:02:08.310><c> a</c>

00:02:08.330 --> 00:02:08.340 align:start position:0%
which supports all of your needs as as a
 

00:02:08.340 --> 00:02:10.490 align:start position:0%
which supports all of your needs as as a
SAS<00:02:08.700><c> product</c><00:02:09.239><c> as</c><00:02:09.360><c> a</c><00:02:09.390><c> software</c><00:02:09.899><c> as</c><00:02:09.989><c> a</c><00:02:10.020><c> service</c>

00:02:10.490 --> 00:02:10.500 align:start position:0%
SAS product as a software as a service
 

00:02:10.500 --> 00:02:12.470 align:start position:0%
SAS product as a software as a service
product<00:02:10.800><c> so</c><00:02:11.220><c> that's</c><00:02:11.700><c> why</c><00:02:11.879><c> I</c><00:02:11.910><c> also</c><00:02:12.150><c> did</c><00:02:12.360><c> other</c>

00:02:12.470 --> 00:02:12.480 align:start position:0%
product so that's why I also did other
 

00:02:12.480 --> 00:02:13.320 align:start position:0%
product so that's why I also did other
tutorials

00:02:13.320 --> 00:02:13.330 align:start position:0%
tutorials
 

00:02:13.330 --> 00:02:16.500 align:start position:0%
tutorials
intercom<00:02:13.990><c> as</c><00:02:14.470><c> well</c><00:02:14.830><c> and</c><00:02:15.190><c> pretty</c><00:02:15.790><c> much</c><00:02:16.030><c> if</c>

00:02:16.500 --> 00:02:16.510 align:start position:0%
intercom as well and pretty much if
 

00:02:16.510 --> 00:02:18.150 align:start position:0%
intercom as well and pretty much if
you're<00:02:16.750><c> interested</c><00:02:17.260><c> in</c><00:02:17.350><c> the</c><00:02:17.440><c> project</c><00:02:17.650><c> here</c><00:02:18.100><c> is</c>

00:02:18.150 --> 00:02:18.160 align:start position:0%
you're interested in the project here is
 

00:02:18.160 --> 00:02:19.740 align:start position:0%
you're interested in the project here is
pretty<00:02:18.310><c> much</c><00:02:18.460><c> the</c><00:02:18.640><c> technologies</c><00:02:19.390><c> which</c><00:02:19.600><c> are</c>

00:02:19.740 --> 00:02:19.750 align:start position:0%
pretty much the technologies which are
 

00:02:19.750 --> 00:02:21.510 align:start position:0%
pretty much the technologies which are
pretty<00:02:19.930><c> much</c><00:02:20.050><c> the</c><00:02:20.230><c> heart</c><00:02:20.440><c> of</c><00:02:20.590><c> it</c><00:02:20.710><c> and</c><00:02:20.890><c> this</c><00:02:21.370><c> is</c>

00:02:21.510 --> 00:02:21.520 align:start position:0%
pretty much the heart of it and this is
 

00:02:21.520 --> 00:02:22.920 align:start position:0%
pretty much the heart of it and this is
pretty<00:02:21.670><c> much</c><00:02:21.760><c> what</c><00:02:22.150><c> I</c><00:02:22.180><c> want</c><00:02:22.570><c> to</c><00:02:22.690><c> run</c><00:02:22.900><c> through</c>

00:02:22.920 --> 00:02:22.930 align:start position:0%
pretty much what I want to run through
 

00:02:22.930 --> 00:02:26.340 align:start position:0%
pretty much what I want to run through
within<00:02:23.830><c> these</c><00:02:24.730><c> tutorial</c><00:02:25.540><c> series</c><00:02:25.780><c> within</c><00:02:26.200><c> this</c>

00:02:26.340 --> 00:02:26.350 align:start position:0%
within these tutorial series within this
 

00:02:26.350 --> 00:02:27.720 align:start position:0%
within these tutorial series within this
channel<00:02:26.830><c> some</c><00:02:27.070><c> have</c><00:02:27.310><c> these</c><00:02:27.550><c> other</c>

00:02:27.720 --> 00:02:27.730 align:start position:0%
channel some have these other
 

00:02:27.730 --> 00:02:31.560 align:start position:0%
channel some have these other
technologies<00:02:29.730><c> deploying</c><00:02:30.730><c> taroko</c><00:02:31.360><c> and</c><00:02:31.480><c> have</c>

00:02:31.560 --> 00:02:31.570 align:start position:0%
technologies deploying taroko and have
 

00:02:31.570 --> 00:02:33.150 align:start position:0%
technologies deploying taroko and have
other<00:02:31.720><c> videos</c><00:02:32.050><c> on</c><00:02:32.230><c> the</c><00:02:32.320><c> front</c><00:02:32.530><c> to</c><00:02:32.680><c> Heroku</c><00:02:32.920><c> as</c>

00:02:33.150 --> 00:02:33.160 align:start position:0%
other videos on the front to Heroku as
 

00:02:33.160 --> 00:02:36.240 align:start position:0%
other videos on the front to Heroku as
well<00:02:34.140><c> this</c><00:02:35.140><c> project</c><00:02:35.530><c> includes</c><00:02:35.860><c> a</c><00:02:35.920><c> long</c><00:02:36.070><c> list</c>

00:02:36.240 --> 00:02:36.250 align:start position:0%
well this project includes a long list
 

00:02:36.250 --> 00:02:38.430 align:start position:0%
well this project includes a long list
of<00:02:36.400><c> technology</c><00:02:36.940><c> including</c><00:02:37.170><c> angularjs</c><00:02:38.170><c> my</c>

00:02:38.430 --> 00:02:38.440 align:start position:0%
of technology including angularjs my
 

00:02:38.440 --> 00:02:40.260 align:start position:0%
of technology including angularjs my
sequel<00:02:38.830><c> will</c><00:02:39.010><c> do</c><00:02:39.130><c> some</c><00:02:39.310><c> tutorial</c><00:02:39.850><c> videos</c><00:02:40.120><c> on</c>

00:02:40.260 --> 00:02:40.270 align:start position:0%
sequel will do some tutorial videos on
 

00:02:40.270 --> 00:02:42.780 align:start position:0%
sequel will do some tutorial videos on
that<00:02:40.420><c> no</c><00:02:40.690><c> js'</c><00:02:41.050><c> server</c><00:02:41.440><c> a</c><00:02:41.470><c> lot</c><00:02:41.710><c> of</c><00:02:41.860><c> open</c><00:02:42.670><c> source</c>

00:02:42.780 --> 00:02:42.790 align:start position:0%
that no js' server a lot of open source
 

00:02:42.790 --> 00:02:45.240 align:start position:0%
that no js' server a lot of open source
modules<00:02:43.360><c> for</c><00:02:43.600><c> nodejs</c><00:02:44.140><c> servers</c><00:02:44.770><c> and</c><00:02:45.040><c> at</c><00:02:45.160><c> the</c>

00:02:45.240 --> 00:02:45.250 align:start position:0%
modules for nodejs servers and at the
 

00:02:45.250 --> 00:02:46.740 align:start position:0%
modules for nodejs servers and at the
other<00:02:45.370><c> day</c><00:02:45.580><c> it's</c><00:02:45.730><c> javascript</c><00:02:46.360><c> so</c><00:02:46.480><c> that's</c><00:02:46.630><c> why</c>

00:02:46.740 --> 00:02:46.750 align:start position:0%
other day it's javascript so that's why
 

00:02:46.750 --> 00:02:48.210 align:start position:0%
other day it's javascript so that's why
people<00:02:47.080><c> really</c><00:02:47.320><c> love</c><00:02:47.500><c> it</c><00:02:47.680><c> and</c><00:02:47.800><c> it's</c><00:02:47.920><c> gaining</c><00:02:48.160><c> a</c>

00:02:48.210 --> 00:02:48.220 align:start position:0%
people really love it and it's gaining a
 

00:02:48.220 --> 00:02:50.430 align:start position:0%
people really love it and it's gaining a
lot<00:02:48.310><c> of</c><00:02:48.370><c> popularity</c><00:02:48.960><c> Heroku</c><00:02:49.960><c> is</c><00:02:49.990><c> actually</c>

00:02:50.430 --> 00:02:50.440 align:start position:0%
lot of popularity Heroku is actually
 

00:02:50.440 --> 00:02:52.080 align:start position:0%
lot of popularity Heroku is actually
totally<00:02:50.860><c> free</c><00:02:51.040><c> all</c><00:02:51.190><c> you</c><00:02:51.310><c> need</c><00:02:51.430><c> to</c><00:02:51.550><c> do</c><00:02:51.670><c> is</c><00:02:51.820><c> put</c>

00:02:52.080 --> 00:02:52.090 align:start position:0%
totally free all you need to do is put
 

00:02:52.090 --> 00:02:54.630 align:start position:0%
totally free all you need to do is put
is<00:02:52.690><c> put</c><00:02:52.750><c> a</c><00:02:52.960><c> credit</c><00:02:53.380><c> card</c><00:02:53.530><c> on</c><00:02:53.770><c> to</c><00:02:54.130><c> your</c><00:02:54.370><c> account</c>

00:02:54.630 --> 00:02:54.640 align:start position:0%
is put a credit card on to your account
 

00:02:54.640 --> 00:02:55.920 align:start position:0%
is put a credit card on to your account
but<00:02:54.970><c> you</c><00:02:55.000><c> don't</c><00:02:55.210><c> actually</c><00:02:55.360><c> need</c><00:02:55.660><c> to</c><00:02:55.720><c> purchase</c>

00:02:55.920 --> 00:02:55.930 align:start position:0%
but you don't actually need to purchase
 

00:02:55.930 --> 00:02:58.050 align:start position:0%
but you don't actually need to purchase
anything<00:02:56.320><c> that</c><00:02:57.130><c> allows</c><00:02:57.370><c> you</c><00:02:57.550><c> to</c><00:02:57.700><c> have</c><00:02:57.820><c> both</c>

00:02:58.050 --> 00:02:58.060 align:start position:0%
anything that allows you to have both
 

00:02:58.060 --> 00:03:00.030 align:start position:0%
anything that allows you to have both
the<00:02:58.300><c> meta</c><00:02:58.510><c> based</c><00:02:58.690><c> open</c><00:02:58.990><c> source</c><00:02:59.200><c> analytics</c><00:02:59.800><c> and</c>

00:03:00.030 --> 00:03:00.040 align:start position:0%
the meta based open source analytics and
 

00:03:00.040 --> 00:03:02.400 align:start position:0%
the meta based open source analytics and
it<00:03:00.550><c> also</c><00:03:00.760><c> gives</c><00:03:01.120><c> you</c><00:03:01.300><c> the</c><00:03:01.330><c> actual</c><00:03:01.750><c> hosting</c><00:03:02.260><c> of</c>

00:03:02.400 --> 00:03:02.410 align:start position:0%
it also gives you the actual hosting of
 

00:03:02.410 --> 00:03:04.710 align:start position:0%
it also gives you the actual hosting of
the<00:03:02.440><c> site</c><00:03:02.770><c> itself</c><00:03:03.160><c> the</c><00:03:04.000><c> site</c><00:03:04.210><c> itself</c><00:03:04.540><c> is</c>

00:03:04.710 --> 00:03:04.720 align:start position:0%
the site itself the site itself is
 

00:03:04.720 --> 00:03:08.510 align:start position:0%
the site itself the site itself is
already<00:03:05.020><c> live</c><00:03:05.770><c> over</c><00:03:06.430><c> here</c>

00:03:08.510 --> 00:03:08.520 align:start position:0%
already live over here
 

00:03:08.520 --> 00:03:12.210 align:start position:0%
already live over here
koala<00:03:09.520><c> CMS</c><00:03:10.030><c> data</c><00:03:10.180><c> rogue</c><00:03:10.450><c> web.com</c><00:03:11.220><c> we've</c>

00:03:12.210 --> 00:03:12.220 align:start position:0%
koala CMS data rogue web.com we've
 

00:03:12.220 --> 00:03:14.550 align:start position:0%
koala CMS data rogue web.com we've
already<00:03:12.430><c> pushed</c><00:03:12.940><c> it</c><00:03:13.120><c> to</c><00:03:13.240><c> Heroku</c><00:03:13.570><c> as</c><00:03:13.900><c> well</c><00:03:14.170><c> I</c>

00:03:14.550 --> 00:03:14.560 align:start position:0%
already pushed it to Heroku as well I
 

00:03:14.560 --> 00:03:16.500 align:start position:0%
already pushed it to Heroku as well I
just<00:03:15.220><c> want</c><00:03:15.490><c> to</c><00:03:15.580><c> cover</c><00:03:15.730><c> a</c><00:03:15.880><c> little</c><00:03:16.150><c> bit</c><00:03:16.240><c> more</c><00:03:16.420><c> of</c>

00:03:16.500 --> 00:03:16.510 align:start position:0%
just want to cover a little bit more of
 

00:03:16.510 --> 00:03:19.260 align:start position:0%
just want to cover a little bit more of
us<00:03:16.950><c> we</c><00:03:17.950><c> ended</c><00:03:18.190><c> up</c><00:03:18.250><c> moving</c><00:03:18.400><c> away</c><00:03:18.670><c> from</c><00:03:18.820><c> Google</c>

00:03:19.260 --> 00:03:19.270 align:start position:0%
us we ended up moving away from Google
 

00:03:19.270 --> 00:03:20.550 align:start position:0%
us we ended up moving away from Google
Cloud<00:03:19.480><c> sequel</c><00:03:19.989><c> because</c><00:03:20.170><c> it</c><00:03:20.290><c> was</c><00:03:20.380><c> too</c>

00:03:20.550 --> 00:03:20.560 align:start position:0%
Cloud sequel because it was too
 

00:03:20.560 --> 00:03:21.210 align:start position:0%
Cloud sequel because it was too
expensive

00:03:21.210 --> 00:03:21.220 align:start position:0%
expensive
 

00:03:21.220 --> 00:03:23.640 align:start position:0%
expensive
we<00:03:21.520><c> ended</c><00:03:21.790><c> up</c><00:03:21.910><c> shifting</c><00:03:22.360><c> towards</c><00:03:22.780><c> Heroku</c>

00:03:23.640 --> 00:03:23.650 align:start position:0%
we ended up shifting towards Heroku
 

00:03:23.650 --> 00:03:26.699 align:start position:0%
we ended up shifting towards Heroku
add-on<00:03:24.040><c> called</c><00:03:24.370><c> jaws</c><00:03:25.000><c> DB</c><00:03:25.600><c> and</c><00:03:25.989><c> that</c><00:03:26.320><c> gives</c><00:03:26.560><c> us</c>

00:03:26.699 --> 00:03:26.709 align:start position:0%
add-on called jaws DB and that gives us
 

00:03:26.709 --> 00:03:28.290 align:start position:0%
add-on called jaws DB and that gives us
Heroku<00:03:27.220><c> for</c><00:03:27.400><c> absolutely</c><00:03:27.850><c> free</c><00:03:27.940><c> so</c><00:03:28.209><c> that's</c>

00:03:28.290 --> 00:03:28.300 align:start position:0%
Heroku for absolutely free so that's
 

00:03:28.300 --> 00:03:30.720 align:start position:0%
Heroku for absolutely free so that's
really<00:03:28.510><c> nice</c><00:03:28.780><c> angular</c><00:03:29.500><c> UI</c><00:03:29.680><c> rather</c><00:03:30.070><c> and</c><00:03:30.310><c> ejs</c>

00:03:30.720 --> 00:03:30.730 align:start position:0%
really nice angular UI rather and ejs
 

00:03:30.730 --> 00:03:32.550 align:start position:0%
really nice angular UI rather and ejs
are<00:03:30.940><c> also</c><00:03:31.180><c> included</c><00:03:31.690><c> within</c><00:03:31.840><c> the</c><00:03:32.050><c> project</c>

00:03:32.550 --> 00:03:32.560 align:start position:0%
are also included within the project
 

00:03:32.560 --> 00:03:36.420 align:start position:0%
are also included within the project
let's<00:03:33.310><c> see</c><00:03:33.400><c> what's</c><00:03:33.550><c> happening</c><00:03:33.670><c> off</c><00:03:34.560><c> it</c><00:03:35.560><c> has</c><00:03:36.040><c> it</c>

00:03:36.420 --> 00:03:36.430 align:start position:0%
let's see what's happening off it has it
 

00:03:36.430 --> 00:03:38.490 align:start position:0%
let's see what's happening off it has it
remembers<00:03:36.880><c> cookies</c><00:03:37.390><c> it's</c><00:03:37.780><c> gotta</c><00:03:37.989><c> it's</c><00:03:38.380><c> a</c>

00:03:38.490 --> 00:03:38.500 align:start position:0%
remembers cookies it's gotta it's a
 

00:03:38.500 --> 00:03:40.910 align:start position:0%
remembers cookies it's gotta it's a
login<00:03:38.980><c> and</c><00:03:39.010><c> authentication</c><00:03:39.459><c> app</c><00:03:40.150><c> over</c><00:03:40.390><c> here</c>

00:03:40.910 --> 00:03:40.920 align:start position:0%
login and authentication app over here
 

00:03:40.920 --> 00:03:43.680 align:start position:0%
login and authentication app over here
we<00:03:41.920><c> may</c><00:03:42.220><c> want</c><00:03:42.430><c> to</c><00:03:42.489><c> work</c><00:03:42.670><c> on</c><00:03:42.910><c> on</c><00:03:43.209><c> that</c><00:03:43.480><c> heading</c>

00:03:43.680 --> 00:03:43.690 align:start position:0%
we may want to work on on that heading
 

00:03:43.690 --> 00:03:46.470 align:start position:0%
we may want to work on on that heading
and<00:03:44.050><c> it</c><00:03:44.890><c> automatically</c><00:03:45.310><c> goes</c><00:03:45.400><c> to</c><00:03:45.880><c> the</c><00:03:46.209><c> blog</c>

00:03:46.470 --> 00:03:46.480 align:start position:0%
and it automatically goes to the blog
 

00:03:46.480 --> 00:03:48.150 align:start position:0%
and it automatically goes to the blog
once<00:03:47.050><c> you</c><00:03:47.170><c> get</c><00:03:47.350><c> to</c><00:03:47.440><c> the</c><00:03:47.560><c> blog</c><00:03:47.770><c> you</c><00:03:48.010><c> can</c>

00:03:48.150 --> 00:03:48.160 align:start position:0%
once you get to the blog you can
 

00:03:48.160 --> 00:03:49.680 align:start position:0%
once you get to the blog you can
actually<00:03:48.280><c> click</c><00:03:48.790><c> on</c><00:03:48.970><c> any</c><00:03:49.270><c> one</c><00:03:49.450><c> of</c><00:03:49.510><c> the</c>

00:03:49.680 --> 00:03:49.690 align:start position:0%
actually click on any one of the
 

00:03:49.690 --> 00:03:52.610 align:start position:0%
actually click on any one of the
articles<00:03:50.160><c> let's</c><00:03:51.160><c> say</c><00:03:51.310><c> I</c><00:03:51.370><c> click</c><00:03:51.430><c> on</c><00:03:51.790><c> that</c><00:03:51.820><c> and</c>

00:03:52.610 --> 00:03:52.620 align:start position:0%
articles let's say I click on that and
 

00:03:52.620 --> 00:03:55.050 align:start position:0%
articles let's say I click on that and
it's<00:03:53.620><c> got</c><00:03:53.770><c> the</c><00:03:53.920><c> amp</c><00:03:54.070><c> it</c><00:03:54.280><c> up</c><00:03:54.370><c> blog</c><00:03:54.670><c> maybe</c><00:03:54.910><c> we</c>

00:03:55.050 --> 00:03:55.060 align:start position:0%
it's got the amp it up blog maybe we
 

00:03:55.060 --> 00:03:57.120 align:start position:0%
it's got the amp it up blog maybe we
should<00:03:55.239><c> call</c><00:03:55.420><c> that</c><00:03:55.450><c> koala</c><00:03:55.959><c> CMS</c><00:03:56.560><c> we</c><00:03:56.800><c> might</c><00:03:56.980><c> do</c>

00:03:57.120 --> 00:03:57.130 align:start position:0%
should call that koala CMS we might do
 

00:03:57.130 --> 00:04:00.420 align:start position:0%
should call that koala CMS we might do
that<00:03:57.280><c> in</c><00:03:57.430><c> this</c><00:03:57.580><c> in</c><00:03:57.880><c> a</c><00:03:57.970><c> video</c><00:03:58.150><c> shortly</c><00:03:58.720><c> and</c><00:03:59.430><c> so</c>

00:04:00.420 --> 00:04:00.430 align:start position:0%
that in this in a video shortly and so
 

00:04:00.430 --> 00:04:02.699 align:start position:0%
that in this in a video shortly and so
on<00:04:00.550><c> and</c><00:04:00.700><c> so</c><00:04:00.880><c> forth</c><00:04:01.180><c> the</c><00:04:01.420><c> best</c><00:04:02.020><c> LinkedIn</c><00:04:02.410><c> growth</c>

00:04:02.699 --> 00:04:02.709 align:start position:0%
on and so forth the best LinkedIn growth
 

00:04:02.709 --> 00:04:04.620 align:start position:0%
on and so forth the best LinkedIn growth
hacks<00:04:03.070><c> you'll</c><00:04:03.370><c> hear</c><00:04:03.550><c> oh</c><00:04:03.580><c> yeah</c><00:04:03.850><c> okay</c><00:04:04.300><c> this</c><00:04:04.450><c> must</c>

00:04:04.620 --> 00:04:04.630 align:start position:0%
hacks you'll hear oh yeah okay this must
 

00:04:04.630 --> 00:04:07.800 align:start position:0%
hacks you'll hear oh yeah okay this must
be<00:04:04.840><c> a</c><00:04:04.870><c> typo</c><00:04:05.560><c> with</c><00:04:05.980><c> envy</c><00:04:06.370><c> that's</c><00:04:07.270><c> in</c><00:04:07.390><c> the</c><00:04:07.480><c> actual</c>

00:04:07.800 --> 00:04:07.810 align:start position:0%
be a typo with envy that's in the actual
 

00:04:07.810 --> 00:04:11.490 align:start position:0%
be a typo with envy that's in the actual
HTML<00:04:09.300><c> where</c><00:04:10.300><c> we</c><00:04:10.390><c> can't</c><00:04:10.630><c> see</c><00:04:10.750><c> all</c><00:04:11.019><c> the</c><00:04:11.140><c> pictures</c>

00:04:11.490 --> 00:04:11.500 align:start position:0%
HTML where we can't see all the pictures
 

00:04:11.500 --> 00:04:13.680 align:start position:0%
HTML where we can't see all the pictures
here<00:04:11.830><c> okay</c><00:04:12.730><c> but</c><00:04:12.970><c> anyways</c><00:04:13.269><c> you</c><00:04:13.390><c> have</c><00:04:13.420><c> some</c>

00:04:13.680 --> 00:04:13.690 align:start position:0%
here okay but anyways you have some
 

00:04:13.690 --> 00:04:15.509 align:start position:0%
here okay but anyways you have some
other<00:04:13.780><c> nice</c><00:04:14.050><c> features</c><00:04:14.440><c> over</c><00:04:14.709><c> here</c><00:04:14.950><c> you</c><00:04:15.340><c> have</c>

00:04:15.509 --> 00:04:15.519 align:start position:0%
other nice features over here you have
 

00:04:15.519 --> 00:04:17.789 align:start position:0%
other nice features over here you have
we'll<00:04:16.390><c> probably</c><00:04:16.570><c> dug</c><00:04:16.900><c> that</c><00:04:17.109><c> up</c><00:04:17.140><c> a</c><00:04:17.380><c> little</c><00:04:17.500><c> more</c>

00:04:17.789 --> 00:04:17.799 align:start position:0%
we'll probably dug that up a little more
 

00:04:17.799 --> 00:04:21.140 align:start position:0%
we'll probably dug that up a little more
to<00:04:18.010><c> include</c><00:04:19.560><c> automatically</c><00:04:20.560><c> include</c>

00:04:21.140 --> 00:04:21.150 align:start position:0%
to include automatically include
 

00:04:21.150 --> 00:04:24.090 align:start position:0%
to include automatically include
articles<00:04:22.150><c> that</c><00:04:22.390><c> were</c><00:04:22.510><c> randomly</c><00:04:23.380><c> that</c><00:04:23.979><c> are</c>

00:04:24.090 --> 00:04:24.100 align:start position:0%
articles that were randomly that are
 

00:04:24.100 --> 00:04:25.540 align:start position:0%
articles that were randomly that are
within<00:04:24.250><c> your</c><00:04:24.520><c> collection</c><00:04:24.940><c> of</c><00:04:25.030><c> article</c>

00:04:25.540 --> 00:04:25.550 align:start position:0%
within your collection of article
 

00:04:25.550 --> 00:04:28.390 align:start position:0%
within your collection of article
we<00:04:25.940><c> also</c><00:04:26.060><c> have</c><00:04:26.389><c> the</c><00:04:27.139><c> actual</c><00:04:27.530><c> front-end</c><00:04:28.099><c> for</c>

00:04:28.390 --> 00:04:28.400 align:start position:0%
we also have the actual front-end for
 

00:04:28.400 --> 00:04:30.700 align:start position:0%
we also have the actual front-end for
creating<00:04:29.210><c> the</c><00:04:29.389><c> articles</c><00:04:29.870><c> and</c><00:04:30.169><c> uploading</c>

00:04:30.700 --> 00:04:30.710 align:start position:0%
creating the articles and uploading
 

00:04:30.710 --> 00:04:32.920 align:start position:0%
creating the articles and uploading
pictures<00:04:30.919><c> as</c><00:04:31.460><c> well</c><00:04:31.699><c> soon</c><00:04:32.569><c> we're</c><00:04:32.690><c> going</c><00:04:32.780><c> to</c><00:04:32.840><c> be</c>

00:04:32.920 --> 00:04:32.930 align:start position:0%
pictures as well soon we're going to be
 

00:04:32.930 --> 00:04:34.809 align:start position:0%
pictures as well soon we're going to be
uploading<00:04:33.770><c> pictures</c><00:04:33.949><c> we</c><00:04:34.400><c> have</c><00:04:34.550><c> something</c>

00:04:34.809 --> 00:04:34.819 align:start position:0%
uploading pictures we have something
 

00:04:34.819 --> 00:04:37.839 align:start position:0%
uploading pictures we have something
called<00:04:34.940><c> the</c><00:04:35.120><c> community</c><00:04:35.720><c> page</c><00:04:36.159><c> which</c><00:04:37.159><c> is</c><00:04:37.370><c> this</c>

00:04:37.839 --> 00:04:37.849 align:start position:0%
called the community page which is this
 

00:04:37.849 --> 00:04:40.629 align:start position:0%
called the community page which is this
nice<00:04:38.120><c> WYSIWYG</c><00:04:39.020><c> here</c><00:04:39.500><c> anything</c><00:04:40.310><c> that</c><00:04:40.460><c> you</c>

00:04:40.629 --> 00:04:40.639 align:start position:0%
nice WYSIWYG here anything that you
 

00:04:40.639 --> 00:04:42.899 align:start position:0%
nice WYSIWYG here anything that you
write<00:04:40.819><c> will</c><00:04:41.000><c> immediately</c><00:04:41.240><c> get</c><00:04:41.690><c> translated</c>

00:04:42.899 --> 00:04:42.909 align:start position:0%
write will immediately get translated
 

00:04:42.909 --> 00:04:48.610 align:start position:0%
write will immediately get translated
converted<00:04:44.500><c> to</c><00:04:45.500><c> HTML</c><00:04:45.830><c> and</c><00:04:46.870><c> if</c><00:04:47.870><c> I</c><00:04:47.990><c> highlight</c><00:04:48.229><c> it</c>

00:04:48.610 --> 00:04:48.620 align:start position:0%
converted to HTML and if I highlight it
 

00:04:48.620 --> 00:04:52.420 align:start position:0%
converted to HTML and if I highlight it
and<00:04:48.800><c> I</c><00:04:48.979><c> do</c><00:04:49.039><c> an</c><00:04:49.280><c> h1</c><00:04:50.470><c> everything</c><00:04:51.470><c> here</c><00:04:51.740><c> also</c><00:04:52.129><c> gets</c>

00:04:52.420 --> 00:04:52.430 align:start position:0%
and I do an h1 everything here also gets
 

00:04:52.430 --> 00:04:54.249 align:start position:0%
and I do an h1 everything here also gets
converted<00:04:52.729><c> to</c><00:04:53.000><c> HTML</c><00:04:53.150><c> and</c><00:04:53.720><c> it</c><00:04:53.810><c> automatically</c>

00:04:54.249 --> 00:04:54.259 align:start position:0%
converted to HTML and it automatically
 

00:04:54.259 --> 00:04:56.230 align:start position:0%
converted to HTML and it automatically
gives<00:04:54.500><c> me</c><00:04:54.590><c> about</c><00:04:54.680><c> h1</c><00:04:55.099><c> so</c><00:04:55.550><c> we're</c><00:04:55.669><c> gonna</c><00:04:55.759><c> cut</c><00:04:56.060><c> a</c>

00:04:56.230 --> 00:04:56.240 align:start position:0%
gives me about h1 so we're gonna cut a
 

00:04:56.240 --> 00:04:57.700 align:start position:0%
gives me about h1 so we're gonna cut a
cover<00:04:56.509><c> a</c><00:04:56.599><c> little</c><00:04:56.750><c> bit</c><00:04:56.870><c> more</c><00:04:56.960><c> with</c><00:04:57.229><c> technology</c>

00:04:57.700 --> 00:04:57.710 align:start position:0%
cover a little bit more with technology
 

00:04:57.710 --> 00:04:59.529 align:start position:0%
cover a little bit more with technology
of<00:04:57.800><c> a</c><00:04:57.860><c> project</c><00:04:58.250><c> in</c><00:04:58.340><c> the</c><00:04:58.460><c> future</c><00:04:58.759><c> videos</c><00:04:59.090><c> tune</c>

00:04:59.529 --> 00:04:59.539 align:start position:0%
of a project in the future videos tune
 

00:04:59.539 --> 00:05:01.750 align:start position:0%
of a project in the future videos tune
in

