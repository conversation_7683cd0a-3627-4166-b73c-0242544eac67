WEBVTT
Kind: captions
Language: en

00:00:00.160 --> 00:00:02.070 align:start position:0%
 
Hi<00:00:00.400><c> there</c><00:00:00.560><c> and</c><00:00:00.880><c> welcome.</c><00:00:01.360><c> In</c><00:00:01.520><c> this</c><00:00:01.760><c> video,</c>

00:00:02.070 --> 00:00:02.080 align:start position:0%
Hi there and welcome. In this video,
 

00:00:02.080 --> 00:00:03.429 align:start position:0%
Hi there and welcome. In this video,
we'll<00:00:02.320><c> walk</c><00:00:02.480><c> you</c><00:00:02.639><c> through</c><00:00:02.879><c> how</c><00:00:03.120><c> to</c><00:00:03.280><c> get</c>

00:00:03.429 --> 00:00:03.439 align:start position:0%
we'll walk you through how to get
 

00:00:03.439 --> 00:00:05.030 align:start position:0%
we'll walk you through how to get
started<00:00:03.760><c> with</c><00:00:03.919><c> a</c><00:00:04.160><c> new</c><00:00:04.400><c> project</c><00:00:04.640><c> in</c><00:00:04.880><c> the</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
started with a new project in the
 

00:00:05.040 --> 00:00:07.909 align:start position:0%
started with a new project in the
generative<00:00:05.520><c> AI</c><00:00:05.920><c> lab</c><00:00:06.480><c> step</c><00:00:06.720><c> by</c><00:00:06.960><c> step.</c><00:00:07.600><c> Let's</c>

00:00:07.909 --> 00:00:07.919 align:start position:0%
generative AI lab step by step. Let's
 

00:00:07.919 --> 00:00:10.230 align:start position:0%
generative AI lab step by step. Let's
dive<00:00:08.160><c> right</c><00:00:08.320><c> in.</c><00:00:09.120><c> First,</c><00:00:09.599><c> head</c><00:00:09.760><c> over</c><00:00:09.920><c> to</c><00:00:10.080><c> the</c>

00:00:10.230 --> 00:00:10.240 align:start position:0%
dive right in. First, head over to the
 

00:00:10.240 --> 00:00:13.110 align:start position:0%
dive right in. First, head over to the
models<00:00:10.639><c> hub</c><00:00:10.880><c> page.</c><00:00:11.440><c> In</c><00:00:11.679><c> the</c><00:00:11.840><c> search</c><00:00:12.080><c> bar,</c><00:00:12.559><c> type</c>

00:00:13.110 --> 00:00:13.120 align:start position:0%
models hub page. In the search bar, type
 

00:00:13.120 --> 00:00:15.669 align:start position:0%
models hub page. In the search bar, type
ne<00:00:13.679><c> pathogen,</c><00:00:14.480><c> which</c><00:00:14.639><c> is</c><00:00:14.799><c> a</c><00:00:14.960><c> clinical</c><00:00:15.440><c> named</c>

00:00:15.669 --> 00:00:15.679 align:start position:0%
ne pathogen, which is a clinical named
 

00:00:15.679 --> 00:00:17.990 align:start position:0%
ne pathogen, which is a clinical named
entity<00:00:16.160><c> recognition</c><00:00:16.720><c> model.</c><00:00:17.520><c> Once</c><00:00:17.760><c> it</c>

00:00:17.990 --> 00:00:18.000 align:start position:0%
entity recognition model. Once it
 

00:00:18.000 --> 00:00:20.950 align:start position:0%
entity recognition model. Once it
appears,<00:00:18.560><c> click</c><00:00:18.880><c> to</c><00:00:19.039><c> download</c><00:00:19.480><c> it.</c><00:00:20.480><c> Next,</c>

00:00:20.950 --> 00:00:20.960 align:start position:0%
appears, click to download it. Next,
 

00:00:20.960 --> 00:00:22.950 align:start position:0%
appears, click to download it. Next,
let's<00:00:21.199><c> create</c><00:00:21.439><c> a</c><00:00:21.600><c> new</c><00:00:21.840><c> project.</c><00:00:22.480><c> Click</c><00:00:22.640><c> on</c><00:00:22.800><c> the</c>

00:00:22.950 --> 00:00:22.960 align:start position:0%
let's create a new project. Click on the
 

00:00:22.960 --> 00:00:24.310 align:start position:0%
let's create a new project. Click on the
new<00:00:23.119><c> button</c><00:00:23.359><c> at</c><00:00:23.600><c> the</c><00:00:23.680><c> top</c><00:00:23.840><c> right</c><00:00:24.000><c> of</c><00:00:24.160><c> your</c>

00:00:24.310 --> 00:00:24.320 align:start position:0%
new button at the top right of your
 

00:00:24.320 --> 00:00:26.390 align:start position:0%
new button at the top right of your
screen.<00:00:25.039><c> You'll</c><00:00:25.279><c> now</c><00:00:25.439><c> be</c><00:00:25.600><c> prompted</c><00:00:25.920><c> to</c><00:00:26.080><c> enter</c>

00:00:26.390 --> 00:00:26.400 align:start position:0%
screen. You'll now be prompted to enter
 

00:00:26.400 --> 00:00:28.150 align:start position:0%
screen. You'll now be prompted to enter
some<00:00:26.560><c> details</c><00:00:26.960><c> about</c><00:00:27.199><c> your</c><00:00:27.439><c> project,</c><00:00:28.000><c> like</c>

00:00:28.150 --> 00:00:28.160 align:start position:0%
some details about your project, like
 

00:00:28.160 --> 00:00:30.550 align:start position:0%
some details about your project, like
the<00:00:28.400><c> name</c><00:00:28.560><c> and</c><00:00:28.800><c> description.</c><00:00:29.840><c> Fill</c><00:00:30.080><c> those</c><00:00:30.320><c> in</c>

00:00:30.550 --> 00:00:30.560 align:start position:0%
the name and description. Fill those in
 

00:00:30.560 --> 00:00:32.350 align:start position:0%
the name and description. Fill those in
and<00:00:30.800><c> then</c><00:00:31.039><c> move</c><00:00:31.199><c> on</c><00:00:31.359><c> to</c><00:00:31.519><c> the</c><00:00:31.679><c> instruction</c>

00:00:32.350 --> 00:00:32.360 align:start position:0%
and then move on to the instruction
 

00:00:32.360 --> 00:00:34.389 align:start position:0%
and then move on to the instruction
section.<00:00:33.360><c> This</c><00:00:33.520><c> is</c><00:00:33.680><c> where</c><00:00:33.840><c> you</c><00:00:34.079><c> provide</c>

00:00:34.389 --> 00:00:34.399 align:start position:0%
section. This is where you provide
 

00:00:34.399 --> 00:00:37.030 align:start position:0%
section. This is where you provide
guidance<00:00:34.880><c> for</c><00:00:35.399><c> annotators.</c><00:00:36.399><c> Write</c><00:00:36.800><c> clear</c>

00:00:37.030 --> 00:00:37.040 align:start position:0%
guidance for annotators. Write clear
 

00:00:37.040 --> 00:00:38.750 align:start position:0%
guidance for annotators. Write clear
instructions<00:00:37.600><c> that</c><00:00:37.840><c> they'll</c><00:00:38.079><c> see</c><00:00:38.320><c> during</c>

00:00:38.750 --> 00:00:38.760 align:start position:0%
instructions that they'll see during
 

00:00:38.760 --> 00:00:41.030 align:start position:0%
instructions that they'll see during
annotation.<00:00:39.760><c> Things</c><00:00:40.000><c> like</c><00:00:40.160><c> what</c><00:00:40.399><c> entities</c><00:00:40.879><c> to</c>

00:00:41.030 --> 00:00:41.040 align:start position:0%
annotation. Things like what entities to
 

00:00:41.040 --> 00:00:43.430 align:start position:0%
annotation. Things like what entities to
look<00:00:41.200><c> for</c><00:00:41.440><c> or</c><00:00:41.680><c> how</c><00:00:41.920><c> to</c><00:00:42.000><c> label</c><00:00:42.320><c> them.</c><00:00:43.120><c> Now,</c>

00:00:43.430 --> 00:00:43.440 align:start position:0%
look for or how to label them. Now,
 

00:00:43.440 --> 00:00:45.270 align:start position:0%
look for or how to label them. Now,
click<00:00:43.600><c> on</c><00:00:43.840><c> next</c><00:00:44.160><c> and</c><00:00:44.399><c> you'll</c><00:00:44.640><c> be</c><00:00:44.719><c> taken</c><00:00:44.960><c> to</c><00:00:45.120><c> the</c>

00:00:45.270 --> 00:00:45.280 align:start position:0%
click on next and you'll be taken to the
 

00:00:45.280 --> 00:00:48.229 align:start position:0%
click on next and you'll be taken to the
teams<00:00:45.640><c> page.</c><00:00:46.640><c> Here</c><00:00:47.120><c> you</c><00:00:47.360><c> can</c><00:00:47.440><c> assign</c><00:00:47.840><c> users</c>

00:00:48.229 --> 00:00:48.239 align:start position:0%
teams page. Here you can assign users
 

00:00:48.239 --> 00:00:50.310 align:start position:0%
teams page. Here you can assign users
who<00:00:48.399><c> will</c><00:00:48.640><c> be</c><00:00:48.800><c> working</c><00:00:48.960><c> on</c><00:00:49.200><c> this</c><00:00:49.440><c> project.</c><00:00:50.160><c> Go</c>

00:00:50.310 --> 00:00:50.320 align:start position:0%
who will be working on this project. Go
 

00:00:50.320 --> 00:00:52.549 align:start position:0%
who will be working on this project. Go
ahead<00:00:50.559><c> and</c><00:00:50.719><c> add</c><00:00:50.879><c> Andy</c><00:00:51.280><c> as</c><00:00:51.440><c> the</c><00:00:51.600><c> annotator</c><00:00:52.239><c> and</c>

00:00:52.549 --> 00:00:52.559 align:start position:0%
ahead and add Andy as the annotator and
 

00:00:52.559 --> 00:00:54.510 align:start position:0%
ahead and add Andy as the annotator and
Robert<00:00:52.879><c> as</c><00:00:53.120><c> the</c>

00:00:54.510 --> 00:00:54.520 align:start position:0%
Robert as the
 

00:00:54.520 --> 00:00:57.910 align:start position:0%
Robert as the
reviewer.<00:00:55.520><c> Once</c><00:00:55.760><c> that's</c><00:00:56.160><c> done,</c><00:00:56.640><c> click</c><00:00:57.120><c> next.</c>

00:00:57.910 --> 00:00:57.920 align:start position:0%
reviewer. Once that's done, click next.
 

00:00:57.920 --> 00:01:00.549 align:start position:0%
reviewer. Once that's done, click next.
Here<00:00:58.320><c> you'll</c><00:00:58.640><c> choose</c><00:00:58.800><c> the</c><00:00:59.039><c> content</c><00:00:59.359><c> type.</c><00:01:00.239><c> By</c>

00:01:00.549 --> 00:01:00.559 align:start position:0%
Here you'll choose the content type. By
 

00:01:00.559 --> 00:01:02.790 align:start position:0%
Here you'll choose the content type. By
default,<00:01:00.879><c> it's</c><00:01:01.199><c> set</c><00:01:01.359><c> to</c><00:01:01.840><c> named</c><00:01:02.320><c> entity</c>

00:01:02.790 --> 00:01:02.800 align:start position:0%
default, it's set to named entity
 

00:01:02.800 --> 00:01:05.030 align:start position:0%
default, it's set to named entity
recognition,<00:01:03.760><c> which</c><00:01:04.080><c> is</c><00:01:04.239><c> exactly</c><00:01:04.640><c> what</c><00:01:04.879><c> we</c>

00:01:05.030 --> 00:01:05.040 align:start position:0%
recognition, which is exactly what we
 

00:01:05.040 --> 00:01:07.109 align:start position:0%
recognition, which is exactly what we
want<00:01:05.199><c> for</c><00:01:05.439><c> this</c><00:01:05.600><c> example.</c><00:01:06.320><c> So,</c><00:01:06.479><c> let's</c><00:01:06.720><c> keep</c><00:01:06.880><c> it</c>

00:01:07.109 --> 00:01:07.119 align:start position:0%
want for this example. So, let's keep it
 

00:01:07.119 --> 00:01:08.510 align:start position:0%
want for this example. So, let's keep it
as

00:01:08.510 --> 00:01:08.520 align:start position:0%
as
 

00:01:08.520 --> 00:01:11.990 align:start position:0%
as
is.<00:01:09.520><c> Next,</c><00:01:09.920><c> go</c><00:01:10.080><c> to</c><00:01:10.159><c> the</c><00:01:10.320><c> Ruse</c><00:01:10.720><c> resource</c><00:01:11.119><c> page</c>

00:01:11.990 --> 00:01:12.000 align:start position:0%
is. Next, go to the Ruse resource page
 

00:01:12.000 --> 00:01:13.670 align:start position:0%
is. Next, go to the Ruse resource page
here.<00:01:12.560><c> Select</c><00:01:12.880><c> the</c><00:01:13.119><c> model</c><00:01:13.360><c> we</c><00:01:13.520><c> just</c>

00:01:13.670 --> 00:01:13.680 align:start position:0%
here. Select the model we just
 

00:01:13.680 --> 00:01:15.789 align:start position:0%
here. Select the model we just
downloaded<00:01:14.080><c> from</c><00:01:14.240><c> the</c><00:01:14.400><c> models</c><00:01:14.799><c> hub.</c><00:01:15.200><c> That's</c>

00:01:15.789 --> 00:01:15.799 align:start position:0%
downloaded from the models hub. That's
 

00:01:15.799 --> 00:01:18.630 align:start position:0%
downloaded from the models hub. That's
necore<00:01:16.799><c> pathogen.</c><00:01:17.680><c> If</c><00:01:17.840><c> you</c><00:01:18.000><c> see</c><00:01:18.159><c> a</c><00:01:18.320><c> model</c>

00:01:18.630 --> 00:01:18.640 align:start position:0%
necore pathogen. If you see a model
 

00:01:18.640 --> 00:01:20.270 align:start position:0%
necore pathogen. If you see a model
called

00:01:20.270 --> 00:01:20.280 align:start position:0%
called
 

00:01:20.280 --> 00:01:23.030 align:start position:0%
called
ne<00:01:21.280><c> 100</c><00:01:21.680><c> already</c><00:01:22.000><c> selected,</c><00:01:22.560><c> go</c><00:01:22.720><c> ahead</c><00:01:22.880><c> and</c>

00:01:23.030 --> 00:01:23.040 align:start position:0%
ne 100 already selected, go ahead and
 

00:01:23.040 --> 00:01:25.109 align:start position:0%
ne 100 already selected, go ahead and
uncheck<00:01:23.360><c> it.</c><00:01:23.920><c> Then</c><00:01:24.240><c> click</c><00:01:24.799><c> save</c>

00:01:25.109 --> 00:01:25.119 align:start position:0%
uncheck it. Then click save
 

00:01:25.119 --> 00:01:27.590 align:start position:0%
uncheck it. Then click save
configuration.<00:01:26.400><c> On</c><00:01:26.560><c> the</c><00:01:26.720><c> next</c><00:01:26.880><c> screen,</c><00:01:27.360><c> just</c>

00:01:27.590 --> 00:01:27.600 align:start position:0%
configuration. On the next screen, just
 

00:01:27.600 --> 00:01:30.630 align:start position:0%
configuration. On the next screen, just
click<00:01:27.920><c> next.</c><00:01:28.640><c> Then</c><00:01:28.880><c> click</c><00:01:29.200><c> save</c><00:01:29.520><c> config.</c><00:01:30.479><c> When</c>

00:01:30.630 --> 00:01:30.640 align:start position:0%
click next. Then click save config. When
 

00:01:30.640 --> 00:01:32.550 align:start position:0%
click next. Then click save config. When
the<00:01:30.799><c> popup</c><00:01:31.200><c> appears</c><00:01:31.600><c> asking</c><00:01:31.920><c> you</c><00:01:32.079><c> to</c><00:01:32.240><c> deploy</c>

00:01:32.550 --> 00:01:32.560 align:start position:0%
the popup appears asking you to deploy
 

00:01:32.560 --> 00:01:34.870 align:start position:0%
the popup appears asking you to deploy
the<00:01:32.799><c> selected</c><00:01:33.200><c> model,</c><00:01:33.840><c> click</c><00:01:34.240><c> deploy</c><00:01:34.640><c> and</c>

00:01:34.870 --> 00:01:34.880 align:start position:0%
the selected model, click deploy and
 

00:01:34.880 --> 00:01:37.830 align:start position:0%
the selected model, click deploy and
save<00:01:35.320><c> config.</c><00:01:36.320><c> Perfect.</c><00:01:36.960><c> Your</c><00:01:37.200><c> model</c><00:01:37.439><c> is</c><00:01:37.680><c> now</c>

00:01:37.830 --> 00:01:37.840 align:start position:0%
save config. Perfect. Your model is now
 

00:01:37.840 --> 00:01:40.630 align:start position:0%
save config. Perfect. Your model is now
active<00:01:38.159><c> in</c><00:01:38.320><c> the</c><00:01:38.759><c> project.</c><00:01:39.759><c> Now</c><00:01:40.079><c> let's</c><00:01:40.240><c> add</c><00:01:40.400><c> a</c>

00:01:40.630 --> 00:01:40.640 align:start position:0%
active in the project. Now let's add a
 

00:01:40.640 --> 00:01:42.950 align:start position:0%
active in the project. Now let's add a
task.<00:01:41.280><c> Go</c><00:01:41.439><c> to</c><00:01:41.600><c> the</c><00:01:41.759><c> task</c><00:01:42.079><c> page</c><00:01:42.320><c> and</c><00:01:42.560><c> click</c><00:01:42.799><c> the</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
task. Go to the task page and click the
 

00:01:42.960 --> 00:01:45.429 align:start position:0%
task. Go to the task page and click the
import<00:01:43.360><c> button</c><00:01:43.600><c> at</c><00:01:43.840><c> the</c><00:01:44.000><c> top</c><00:01:44.240><c> right.</c><00:01:45.119><c> You</c><00:01:45.360><c> can</c>

00:01:45.429 --> 00:01:45.439 align:start position:0%
import button at the top right. You can
 

00:01:45.439 --> 00:01:47.510 align:start position:0%
import button at the top right. You can
either<00:01:45.840><c> drag</c><00:01:46.159><c> and</c><00:01:46.399><c> drop</c><00:01:46.560><c> a</c><00:01:46.799><c> text</c><00:01:47.040><c> file</c><00:01:47.360><c> with</c>

00:01:47.510 --> 00:01:47.520 align:start position:0%
either drag and drop a text file with
 

00:01:47.520 --> 00:01:50.149 align:start position:0%
either drag and drop a text file with
your<00:01:47.759><c> data</c><00:01:48.320><c> or</c><00:01:48.560><c> just</c><00:01:48.799><c> click</c><00:01:49.360><c> add</c><00:01:49.600><c> a</c><00:01:49.840><c> sample</c>

00:01:50.149 --> 00:01:50.159 align:start position:0%
your data or just click add a sample
 

00:01:50.159 --> 00:01:52.550 align:start position:0%
your data or just click add a sample
task<00:01:50.560><c> if</c><00:01:50.799><c> you</c><00:01:50.960><c> want</c><00:01:51.040><c> to</c><00:01:51.200><c> try</c><00:01:51.360><c> it</c><00:01:51.520><c> out</c><00:01:51.680><c> quickly.</c>

00:01:52.550 --> 00:01:52.560 align:start position:0%
task if you want to try it out quickly.
 

00:01:52.560 --> 00:01:54.789 align:start position:0%
task if you want to try it out quickly.
Once<00:01:52.880><c> your</c><00:01:53.040><c> task</c><00:01:53.360><c> is</c><00:01:53.600><c> added,</c><00:01:54.240><c> click</c><00:01:54.560><c> the</c>

00:01:54.789 --> 00:01:54.799 align:start position:0%
Once your task is added, click the
 

00:01:54.799 --> 00:01:57.350 align:start position:0%
Once your task is added, click the
pre-anotate<00:01:55.680><c> button</c><00:01:56.000><c> at</c><00:01:56.240><c> the</c><00:01:56.479><c> top</c><00:01:56.640><c> right.</c>

00:01:57.350 --> 00:01:57.360 align:start position:0%
pre-anotate button at the top right.
 

00:01:57.360 --> 00:01:59.030 align:start position:0%
pre-anotate button at the top right.
You'll<00:01:57.680><c> see</c><00:01:57.759><c> a</c><00:01:57.920><c> popup</c><00:01:58.320><c> with</c><00:01:58.399><c> the</c><00:01:58.640><c> deployed</c>

00:01:59.030 --> 00:01:59.040 align:start position:0%
You'll see a popup with the deployed
 

00:01:59.040 --> 00:02:02.069 align:start position:0%
You'll see a popup with the deployed
model<00:01:59.360><c> listed.</c><00:02:00.079><c> Click</c><00:02:00.479><c> pre-anotate</c><00:02:01.280><c> again.</c>

00:02:02.069 --> 00:02:02.079 align:start position:0%
model listed. Click pre-anotate again.
 

00:02:02.079 --> 00:02:03.910 align:start position:0%
model listed. Click pre-anotate again.
This<00:02:02.320><c> will</c><00:02:02.479><c> run</c><00:02:02.719><c> the</c><00:02:02.880><c> model</c><00:02:03.119><c> over</c><00:02:03.360><c> your</c><00:02:03.600><c> text</c>

00:02:03.910 --> 00:02:03.920 align:start position:0%
This will run the model over your text
 

00:02:03.920 --> 00:02:06.469 align:start position:0%
This will run the model over your text
and<00:02:04.240><c> generate</c><00:02:04.640><c> predicted</c><00:02:05.240><c> annotations.</c><00:02:06.240><c> When</c>

00:02:06.469 --> 00:02:06.479 align:start position:0%
and generate predicted annotations. When
 

00:02:06.479 --> 00:02:08.550 align:start position:0%
and generate predicted annotations. When
the<00:02:06.640><c> processing</c><00:02:07.119><c> is</c><00:02:07.360><c> complete,</c><00:02:08.000><c> you'll</c><00:02:08.239><c> see</c><00:02:08.319><c> a</c>

00:02:08.550 --> 00:02:08.560 align:start position:0%
the processing is complete, you'll see a
 

00:02:08.560 --> 00:02:10.790 align:start position:0%
the processing is complete, you'll see a
green<00:02:08.800><c> circle</c><00:02:09.119><c> next</c><00:02:09.360><c> to</c><00:02:09.520><c> the</c><00:02:09.679><c> task.</c><00:02:10.640><c> That</c>

00:02:10.790 --> 00:02:10.800 align:start position:0%
green circle next to the task. That
 

00:02:10.800 --> 00:02:13.350 align:start position:0%
green circle next to the task. That
means<00:02:11.120><c> predictions</c><00:02:11.680><c> are</c><00:02:12.120><c> available.</c><00:02:13.120><c> Click</c>

00:02:13.350 --> 00:02:13.360 align:start position:0%
means predictions are available. Click
 

00:02:13.360 --> 00:02:15.110 align:start position:0%
means predictions are available. Click
on<00:02:13.520><c> the</c><00:02:13.680><c> task</c><00:02:14.000><c> to</c><00:02:14.160><c> open</c><00:02:14.319><c> it</c><00:02:14.560><c> and</c><00:02:14.720><c> check</c><00:02:14.959><c> the</c>

00:02:15.110 --> 00:02:15.120 align:start position:0%
on the task to open it and check the
 

00:02:15.120 --> 00:02:17.470 align:start position:0%
on the task to open it and check the
model's<00:02:15.720><c> predictions.</c><00:02:16.720><c> Everything</c><00:02:17.120><c> look</c>

00:02:17.470 --> 00:02:17.480 align:start position:0%
model's predictions. Everything look
 

00:02:17.480 --> 00:02:20.550 align:start position:0%
model's predictions. Everything look
good?<00:02:18.480><c> Great.</c><00:02:19.360><c> Now,</c><00:02:19.599><c> let's</c><00:02:19.840><c> assign</c><00:02:20.160><c> it</c><00:02:20.319><c> for</c>

00:02:20.550 --> 00:02:20.560 align:start position:0%
good? Great. Now, let's assign it for
 

00:02:20.560 --> 00:02:23.030 align:start position:0%
good? Great. Now, let's assign it for
review.<00:02:21.360><c> Assign</c><00:02:21.680><c> the</c><00:02:21.920><c> task</c><00:02:22.160><c> to</c><00:02:22.400><c> Andy</c><00:02:22.720><c> as</c><00:02:22.879><c> the</c>

00:02:23.030 --> 00:02:23.040 align:start position:0%
review. Assign the task to Andy as the
 

00:02:23.040 --> 00:02:25.110 align:start position:0%
review. Assign the task to Andy as the
annotator<00:02:23.680><c> and</c><00:02:23.920><c> also</c><00:02:24.239><c> assign</c><00:02:24.480><c> it</c><00:02:24.640><c> to</c><00:02:24.800><c> Robert</c>

00:02:25.110 --> 00:02:25.120 align:start position:0%
annotator and also assign it to Robert
 

00:02:25.120 --> 00:02:41.869 align:start position:0%
annotator and also assign it to Robert
as<00:02:25.360><c> the</c>

00:02:41.869 --> 00:02:41.879 align:start position:0%
 
 

00:02:41.879 --> 00:02:44.869 align:start position:0%
 
reviewer.<00:02:42.959><c> For</c><00:02:43.200><c> Andy</c><00:02:43.680><c> as</c><00:02:43.920><c> the</c><00:02:44.080><c> annotator,</c>

00:02:44.869 --> 00:02:44.879 align:start position:0%
reviewer. For Andy as the annotator,
 

00:02:44.879 --> 00:02:46.710 align:start position:0%
reviewer. For Andy as the annotator,
they'll<00:02:45.200><c> open</c><00:02:45.440><c> the</c><00:02:45.599><c> task,</c><00:02:46.239><c> review</c><00:02:46.560><c> the</c>

00:02:46.710 --> 00:02:46.720 align:start position:0%
they'll open the task, review the
 

00:02:46.720 --> 00:02:49.030 align:start position:0%
they'll open the task, review the
model's<00:02:47.120><c> predictions,</c><00:02:48.080><c> make</c><00:02:48.239><c> any</c><00:02:48.560><c> necessary</c>

00:02:49.030 --> 00:02:49.040 align:start position:0%
model's predictions, make any necessary
 

00:02:49.040 --> 00:02:59.790 align:start position:0%
model's predictions, make any necessary
edits,<00:02:49.840><c> and</c><00:02:50.000><c> then</c><00:02:50.239><c> click</c><00:02:50.480><c> submit</c>

00:02:59.790 --> 00:02:59.800 align:start position:0%
 
 

00:02:59.800 --> 00:03:02.630 align:start position:0%
 
completion.<00:03:00.800><c> Now</c><00:03:01.040><c> it's</c><00:03:01.280><c> Robert's</c><00:03:01.760><c> turn.</c><00:03:02.400><c> As</c>

00:03:02.630 --> 00:03:02.640 align:start position:0%
completion. Now it's Robert's turn. As
 

00:03:02.640 --> 00:03:04.309 align:start position:0%
completion. Now it's Robert's turn. As
the<00:03:02.800><c> reviewer,</c><00:03:03.280><c> he'll</c><00:03:03.599><c> check</c><00:03:03.760><c> the</c><00:03:03.920><c> completed</c>

00:03:04.309 --> 00:03:04.319 align:start position:0%
the reviewer, he'll check the completed
 

00:03:04.319 --> 00:03:06.710 align:start position:0%
the reviewer, he'll check the completed
annotations<00:03:05.040><c> and</c><00:03:05.280><c> either</c><00:03:05.599><c> approve</c><00:03:06.000><c> them</c><00:03:06.400><c> or</c>

00:03:06.710 --> 00:03:06.720 align:start position:0%
annotations and either approve them or
 

00:03:06.720 --> 00:03:08.390 align:start position:0%
annotations and either approve them or
request<00:03:07.120><c> changes</c><00:03:07.519><c> if</c><00:03:07.760><c> something</c><00:03:08.080><c> needs</c>

00:03:08.390 --> 00:03:08.400 align:start position:0%
request changes if something needs
 

00:03:08.400 --> 00:03:13.830 align:start position:0%
request changes if something needs
correction.

00:03:13.830 --> 00:03:13.840 align:start position:0%
 
 

00:03:13.840 --> 00:03:16.149 align:start position:0%
 
And<00:03:14.080><c> that's</c><00:03:14.400><c> it.</c><00:03:15.040><c> You've</c><00:03:15.360><c> successfully</c><00:03:15.920><c> set</c>

00:03:16.149 --> 00:03:16.159 align:start position:0%
And that's it. You've successfully set
 

00:03:16.159 --> 00:03:18.309 align:start position:0%
And that's it. You've successfully set
up<00:03:16.319><c> and</c><00:03:16.560><c> run</c><00:03:16.720><c> a</c><00:03:16.879><c> project</c><00:03:17.120><c> in</c><00:03:17.360><c> Generative</c><00:03:17.920><c> AI</c>

00:03:18.309 --> 00:03:18.319 align:start position:0%
up and run a project in Generative AI
 

00:03:18.319 --> 00:03:20.270 align:start position:0%
up and run a project in Generative AI
lab,<00:03:19.040><c> complete</c><00:03:19.360><c> with</c><00:03:19.599><c> model</c><00:03:20.000><c> powered</c>

00:03:20.270 --> 00:03:20.280 align:start position:0%
lab, complete with model powered
 

00:03:20.280 --> 00:03:22.149 align:start position:0%
lab, complete with model powered
pre-anotations<00:03:21.280><c> and</c><00:03:21.440><c> a</c><00:03:21.599><c> full</c><00:03:21.840><c> review</c>

00:03:22.149 --> 00:03:22.159 align:start position:0%
pre-anotations and a full review
 

00:03:22.159 --> 00:03:26.239 align:start position:0%
pre-anotations and a full review
workflow.<00:03:23.200><c> Thanks</c><00:03:23.519><c> for</c><00:03:23.680><c> watching.</c>

