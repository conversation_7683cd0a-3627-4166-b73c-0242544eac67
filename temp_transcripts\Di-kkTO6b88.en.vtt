WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:02.510 align:start position:0%
 
kop<00:00:00.359><c> co-pilot</c><00:00:00.840><c> is</c><00:00:00.919><c> a</c><00:00:01.040><c> mature</c><00:00:01.439><c> and</c><00:00:01.640><c> trusted</c><00:00:02.159><c> AI</c>

00:00:02.510 --> 00:00:02.520 align:start position:0%
kop co-pilot is a mature and trusted AI
 

00:00:02.520 --> 00:00:04.230 align:start position:0%
kop co-pilot is a mature and trusted AI
pair<00:00:02.840><c> programmer</c><00:00:03.399><c> used</c><00:00:03.679><c> by</c><00:00:03.840><c> more</c><00:00:04.000><c> than</c><00:00:04.120><c> a</c>

00:00:04.230 --> 00:00:04.240 align:start position:0%
pair programmer used by more than a
 

00:00:04.240 --> 00:00:06.510 align:start position:0%
pair programmer used by more than a
million<00:00:04.680><c> developers</c><00:00:05.680><c> and</c><00:00:05.879><c> now</c><00:00:06.319><c> it's</c>

00:00:06.510 --> 00:00:06.520 align:start position:0%
million developers and now it's
 

00:00:06.520 --> 00:00:08.310 align:start position:0%
million developers and now it's
available<00:00:06.919><c> in</c><00:00:07.080><c> intellig</c><00:00:07.720><c> and</c><00:00:07.839><c> the</c><00:00:08.000><c> latest</c>

00:00:08.310 --> 00:00:08.320 align:start position:0%
available in intellig and the latest
 

00:00:08.320 --> 00:00:10.749 align:start position:0%
available in intellig and the latest
Suite<00:00:08.599><c> of</c><00:00:08.760><c> idees</c><00:00:09.280><c> from</c><00:00:09.480><c> jet</c><00:00:09.719><c> Brains</c><00:00:10.480><c> it's</c>

00:00:10.749 --> 00:00:10.759 align:start position:0%
Suite of idees from jet Brains it's
 

00:00:10.759 --> 00:00:12.749 align:start position:0%
Suite of idees from jet Brains it's
fantastic<00:00:11.280><c> at</c><00:00:11.559><c> inspecting</c><00:00:12.120><c> your</c><00:00:12.360><c> code</c>

00:00:12.749 --> 00:00:12.759 align:start position:0%
fantastic at inspecting your code
 

00:00:12.759 --> 00:00:14.869 align:start position:0%
fantastic at inspecting your code
including<00:00:13.200><c> natural</c><00:00:13.599><c> language</c><00:00:14.000><c> comments</c><00:00:14.679><c> and</c>

00:00:14.869 --> 00:00:14.879 align:start position:0%
including natural language comments and
 

00:00:14.879 --> 00:00:17.150 align:start position:0%
including natural language comments and
predicting<00:00:15.480><c> what</c><00:00:15.599><c> you</c><00:00:15.719><c> need</c><00:00:16.080><c> next</c><00:00:16.840><c> and</c><00:00:16.960><c> if</c><00:00:17.080><c> it</c>

00:00:17.150 --> 00:00:17.160 align:start position:0%
predicting what you need next and if it
 

00:00:17.160 --> 00:00:18.550 align:start position:0%
predicting what you need next and if it
doesn't<00:00:17.439><c> give</c><00:00:17.560><c> you</c><00:00:17.720><c> what</c><00:00:17.840><c> you</c><00:00:17.960><c> want</c><00:00:18.240><c> first</c>

00:00:18.550 --> 00:00:18.560 align:start position:0%
doesn't give you what you want first
 

00:00:18.560 --> 00:00:20.830 align:start position:0%
doesn't give you what you want first
time<00:00:18.960><c> it'll</c><00:00:19.199><c> work</c><00:00:19.560><c> with</c><00:00:19.800><c> you</c><00:00:20.279><c> ultimately</c>

00:00:20.830 --> 00:00:20.840 align:start position:0%
time it'll work with you ultimately
 

00:00:20.840 --> 00:00:23.390 align:start position:0%
time it'll work with you ultimately
making<00:00:21.119><c> you</c><00:00:21.240><c> more</c><00:00:21.800><c> productive</c><00:00:22.800><c> and</c><00:00:22.960><c> now</c><00:00:23.199><c> with</c>

00:00:23.390 --> 00:00:23.400 align:start position:0%
making you more productive and now with
 

00:00:23.400 --> 00:00:26.070 align:start position:0%
making you more productive and now with
G<00:00:23.720><c> copy</c><00:00:24.119><c> the</c><00:00:24.279><c> chat</c><00:00:24.880><c> it</c><00:00:25.039><c> truly</c><00:00:25.400><c> feels</c><00:00:25.760><c> like</c><00:00:25.920><c> I'm</c>

00:00:26.070 --> 00:00:26.080 align:start position:0%
G copy the chat it truly feels like I'm
 

00:00:26.080 --> 00:00:28.349 align:start position:0%
G copy the chat it truly feels like I'm
working<00:00:26.320><c> with</c><00:00:26.439><c> a</c><00:00:26.599><c> pair</c><00:00:27.119><c> programmer</c><00:00:28.119><c> for</c>

00:00:28.349 --> 00:00:28.359 align:start position:0%
working with a pair programmer for
 

00:00:28.359 --> 00:00:30.589 align:start position:0%
working with a pair programmer for
example<00:00:29.199><c> I</c><00:00:29.320><c> can</c><00:00:29.480><c> ask</c><00:00:29.640><c> General</c><00:00:30.080><c> programming</c>

00:00:30.589 --> 00:00:30.599 align:start position:0%
example I can ask General programming
 

00:00:30.599 --> 00:00:32.630 align:start position:0%
example I can ask General programming
questions<00:00:31.279><c> like</c><00:00:31.880><c> what</c><00:00:32.000><c> are</c><00:00:32.160><c> some</c><00:00:32.360><c> best</c>

00:00:32.630 --> 00:00:32.640 align:start position:0%
questions like what are some best
 

00:00:32.640 --> 00:00:35.150 align:start position:0%
questions like what are some best
practices<00:00:33.000><c> for</c><00:00:33.200><c> storing</c><00:00:33.680><c> passwords</c><00:00:34.160><c> in</c><00:00:34.360><c> Java</c>

00:00:35.150 --> 00:00:35.160 align:start position:0%
practices for storing passwords in Java
 

00:00:35.160 --> 00:00:37.709 align:start position:0%
practices for storing passwords in Java
co-pilot<00:00:35.760><c> lets</c><00:00:35.920><c> me</c><00:00:36.120><c> know</c><00:00:36.800><c> and</c><00:00:36.960><c> in</c><00:00:37.120><c> this</c><00:00:37.360><c> case</c>

00:00:37.709 --> 00:00:37.719 align:start position:0%
co-pilot lets me know and in this case
 

00:00:37.719 --> 00:00:39.590 align:start position:0%
co-pilot lets me know and in this case
it<00:00:37.960><c> even</c><00:00:38.200><c> provided</c><00:00:38.600><c> some</c><00:00:38.800><c> sample</c><00:00:39.200><c> code</c><00:00:39.480><c> I</c>

00:00:39.590 --> 00:00:39.600 align:start position:0%
it even provided some sample code I
 

00:00:39.600 --> 00:00:41.549 align:start position:0%
it even provided some sample code I
could<00:00:39.800><c> use</c><00:00:40.680><c> I</c><00:00:40.760><c> have</c><00:00:40.920><c> some</c><00:00:41.120><c> password</c>

00:00:41.549 --> 00:00:41.559 align:start position:0%
could use I have some password
 

00:00:41.559 --> 00:00:43.229 align:start position:0%
could use I have some password
validation<00:00:42.079><c> code</c><00:00:42.399><c> already</c><00:00:42.840><c> but</c><00:00:42.960><c> it's</c><00:00:43.079><c> a</c>

00:00:43.229 --> 00:00:43.239 align:start position:0%
validation code already but it's a
 

00:00:43.239 --> 00:00:44.790 align:start position:0%
validation code already but it's a
regular<00:00:43.680><c> expression</c><00:00:44.239><c> and</c><00:00:44.360><c> those</c><00:00:44.520><c> are</c><00:00:44.640><c> not</c>

00:00:44.790 --> 00:00:44.800 align:start position:0%
regular expression and those are not
 

00:00:44.800 --> 00:00:46.869 align:start position:0%
regular expression and those are not
easy<00:00:45.039><c> to</c><00:00:45.200><c> interpret</c><00:00:45.680><c> at</c><00:00:45.800><c> a</c><00:00:45.960><c> glance</c><00:00:46.719><c> in</c>

00:00:46.869 --> 00:00:46.879 align:start position:0%
easy to interpret at a glance in
 

00:00:46.879 --> 00:00:48.990 align:start position:0%
easy to interpret at a glance in
intelligate<00:00:47.640><c> I</c><00:00:47.719><c> can</c><00:00:47.879><c> now</c><00:00:48.039><c> ask</c><00:00:48.280><c> GitHub</c><00:00:48.559><c> cop</c>

00:00:48.990 --> 00:00:49.000 align:start position:0%
intelligate I can now ask GitHub cop
 

00:00:49.000 --> 00:00:51.350 align:start position:0%
intelligate I can now ask GitHub cop
chat<00:00:49.239><c> to</c><00:00:49.480><c> explain</c><00:00:50.079><c> any</c><00:00:50.320><c> code</c><00:00:50.640><c> I'm</c><00:00:50.800><c> unfamiliar</c>

00:00:51.350 --> 00:00:51.360 align:start position:0%
chat to explain any code I'm unfamiliar
 

00:00:51.360 --> 00:00:53.990 align:start position:0%
chat to explain any code I'm unfamiliar
with<00:00:51.920><c> even</c><00:00:52.199><c> if</c><00:00:52.320><c> it's</c><00:00:52.440><c> a</c><00:00:52.600><c> regular</c><00:00:53.039><c> expression</c>

00:00:53.990 --> 00:00:54.000 align:start position:0%
with even if it's a regular expression
 

00:00:54.000 --> 00:00:55.790 align:start position:0%
with even if it's a regular expression
co-pilot<00:00:54.480><c> chat</c><00:00:54.760><c> is</c><00:00:54.920><c> also</c><00:00:55.160><c> great</c><00:00:55.359><c> for</c><00:00:55.520><c> helping</c>

00:00:55.790 --> 00:00:55.800 align:start position:0%
co-pilot chat is also great for helping
 

00:00:55.800 --> 00:00:58.310 align:start position:0%
co-pilot chat is also great for helping
you<00:00:55.920><c> with</c><00:00:56.320><c> documentation</c><00:00:57.320><c> using</c><00:00:57.600><c> the</c><00:00:57.719><c> SL</c><00:00:58.120><c> do</c>

00:00:58.310 --> 00:00:58.320 align:start position:0%
you with documentation using the SL do
 

00:00:58.320 --> 00:00:59.869 align:start position:0%
you with documentation using the SL do
command<00:00:58.680><c> it</c><00:00:58.760><c> will</c><00:00:58.920><c> create</c><00:00:59.239><c> comments</c><00:00:59.640><c> EXP</c>

00:00:59.869 --> 00:00:59.879 align:start position:0%
command it will create comments EXP
 

00:00:59.879 --> 00:01:01.950 align:start position:0%
command it will create comments EXP
explaining<00:01:00.239><c> what</c><00:01:00.399><c> my</c><00:01:00.559><c> code</c><00:01:00.840><c> does</c><00:01:01.519><c> and</c><00:01:01.719><c> even</c>

00:01:01.950 --> 00:01:01.960 align:start position:0%
explaining what my code does and even
 

00:01:01.960 --> 00:01:04.149 align:start position:0%
explaining what my code does and even
better<00:01:02.559><c> I</c><00:01:02.680><c> can</c><00:01:02.840><c> even</c><00:01:03.079><c> get</c><00:01:03.239><c> help</c><00:01:03.519><c> writing</c><00:01:03.840><c> tests</c>

00:01:04.149 --> 00:01:04.159 align:start position:0%
better I can even get help writing tests
 

00:01:04.159 --> 00:01:06.670 align:start position:0%
better I can even get help writing tests
for<00:01:04.280><c> the</c><00:01:04.400><c> code</c><00:01:04.680><c> I'm</c><00:01:04.879><c> looking</c><00:01:05.159><c> at</c><00:01:05.880><c> co-pilot</c><00:01:06.520><c> is</c>

00:01:06.670 --> 00:01:06.680 align:start position:0%
for the code I'm looking at co-pilot is
 

00:01:06.680 --> 00:01:08.590 align:start position:0%
for the code I'm looking at co-pilot is
smart<00:01:07.040><c> enough</c><00:01:07.240><c> to</c><00:01:07.759><c> understand</c><00:01:08.040><c> from</c><00:01:08.200><c> the</c><00:01:08.360><c> code</c>

00:01:08.590 --> 00:01:08.600 align:start position:0%
smart enough to understand from the code
 

00:01:08.600 --> 00:01:10.510 align:start position:0%
smart enough to understand from the code
and<00:01:08.799><c> comments</c><00:01:09.360><c> exactly</c><00:01:09.759><c> what</c><00:01:09.920><c> I'm</c><00:01:10.080><c> looking</c><00:01:10.320><c> to</c>

00:01:10.510 --> 00:01:10.520 align:start position:0%
and comments exactly what I'm looking to
 

00:01:10.520 --> 00:01:12.830 align:start position:0%
and comments exactly what I'm looking to
test<00:01:10.960><c> and</c><00:01:11.080><c> can</c><00:01:11.240><c> help</c><00:01:11.439><c> me</c><00:01:11.600><c> Implement</c><00:01:12.119><c> them</c><00:01:12.720><c> it</c>

00:01:12.830 --> 00:01:12.840 align:start position:0%
test and can help me Implement them it
 

00:01:12.840 --> 00:01:14.670 align:start position:0%
test and can help me Implement them it
can<00:01:13.040><c> even</c><00:01:13.280><c> suggest</c><00:01:13.680><c> tests</c><00:01:14.040><c> that</c><00:01:14.200><c> I</c><00:01:14.320><c> might</c><00:01:14.520><c> be</c>

00:01:14.670 --> 00:01:14.680 align:start position:0%
can even suggest tests that I might be
 

00:01:14.680 --> 00:01:17.190 align:start position:0%
can even suggest tests that I might be
missing<00:01:15.640><c> GI</c><00:01:15.920><c> Hub</c><00:01:16.159><c> is</c><00:01:16.280><c> the</c><00:01:16.439><c> AI</c><00:01:16.880><c> powered</c>

00:01:17.190 --> 00:01:17.200 align:start position:0%
missing GI Hub is the AI powered
 

00:01:17.200 --> 00:01:18.789 align:start position:0%
missing GI Hub is the AI powered
platform<00:01:17.600><c> that</c><00:01:17.720><c> helps</c><00:01:18.000><c> your</c><00:01:18.119><c> teams</c><00:01:18.479><c> write</c>

00:01:18.789 --> 00:01:18.799 align:start position:0%
platform that helps your teams write
 

00:01:18.799 --> 00:01:21.429 align:start position:0%
platform that helps your teams write
more<00:01:19.159><c> secure</c><00:01:19.840><c> better</c><00:01:20.159><c> documented</c><00:01:20.880><c> and</c><00:01:21.119><c> better</c>

00:01:21.429 --> 00:01:21.439 align:start position:0%
more secure better documented and better
 

00:01:21.439 --> 00:01:24.390 align:start position:0%
more secure better documented and better
tested<00:01:21.880><c> code</c><00:01:22.600><c> faster</c><00:01:23.079><c> than</c><00:01:23.240><c> ever</c><00:01:23.520><c> before</c><00:01:24.280><c> the</c>

00:01:24.390 --> 00:01:24.400 align:start position:0%
tested code faster than ever before the
 

00:01:24.400 --> 00:01:26.109 align:start position:0%
tested code faster than ever before the
plugin<00:01:24.759><c> is</c><00:01:24.880><c> available</c><00:01:25.320><c> now</c><00:01:25.600><c> for</c><00:01:25.799><c> everyone</c>

00:01:26.109 --> 00:01:26.119 align:start position:0%
plugin is available now for everyone
 

00:01:26.119 --> 00:01:28.350 align:start position:0%
plugin is available now for everyone
with<00:01:26.360><c> GitHub</c><00:01:26.720><c> co-pilot</c><00:01:27.600><c> install</c><00:01:28.000><c> it</c><00:01:28.119><c> from</c><00:01:28.240><c> the</c>

00:01:28.350 --> 00:01:28.360 align:start position:0%
with GitHub co-pilot install it from the
 

00:01:28.360 --> 00:01:31.240 align:start position:0%
with GitHub co-pilot install it from the
jet<00:01:28.520><c> brains</c><00:01:28.880><c> Marketplace</c><00:01:29.439><c> today</c>

00:01:31.240 --> 00:01:31.250 align:start position:0%
jet brains Marketplace today
 

00:01:31.250 --> 00:01:34.469 align:start position:0%
jet brains Marketplace today
[Music]

