WEBVTT
Kind: captions
Language: en

00:00:00.480 --> 00:00:03.230 align:start position:0%
 
hey<00:00:01.380><c> there</c><00:00:01.560><c> this</c><00:00:01.860><c> is</c><00:00:02.040><c> Matt</c><00:00:02.280><c> <PERSON></c><00:00:02.580><c> the</c>

00:00:03.230 --> 00:00:03.240 align:start position:0%
hey there this is <PERSON> the
 

00:00:03.240 --> 00:00:06.170 align:start position:0%
hey there this is <PERSON> the
technov<00:00:03.659><c> Evangelist</c><00:00:04.400><c> and</c><00:00:05.400><c> recently</c><00:00:05.940><c> I</c>

00:00:06.170 --> 00:00:06.180 align:start position:0%
technov Evangelist and recently I
 

00:00:06.180 --> 00:00:09.169 align:start position:0%
technov Evangelist and recently I
updated<00:00:06.600><c> my</c><00:00:07.080><c> pie</c><00:00:07.319><c> hole</c><00:00:07.680><c> server</c><00:00:08.160><c> so</c><00:00:08.880><c> I've</c><00:00:09.000><c> got</c>

00:00:09.169 --> 00:00:09.179 align:start position:0%
updated my pie hole server so I've got
 

00:00:09.179 --> 00:00:11.750 align:start position:0%
updated my pie hole server so I've got
pie<00:00:09.360><c> hole</c><00:00:09.720><c> running</c><00:00:09.840><c> on</c><00:00:10.260><c> a</c><00:00:10.559><c> Raspberry</c><00:00:10.920><c> Pi</c><00:00:11.280><c> on</c><00:00:11.580><c> my</c>

00:00:11.750 --> 00:00:11.760 align:start position:0%
pie hole running on a Raspberry Pi on my
 

00:00:11.760 --> 00:00:15.770 align:start position:0%
pie hole running on a Raspberry Pi on my
home<00:00:11.880><c> network</c><00:00:12.500><c> and</c><00:00:13.500><c> every</c><00:00:14.219><c> time</c><00:00:14.460><c> I</c><00:00:14.639><c> do</c><00:00:14.759><c> this</c><00:00:15.000><c> I</c>

00:00:15.770 --> 00:00:15.780 align:start position:0%
home network and every time I do this I
 

00:00:15.780 --> 00:00:18.590 align:start position:0%
home network and every time I do this I
I<00:00:16.020><c> can't</c><00:00:16.260><c> remember</c><00:00:16.500><c> how</c><00:00:17.160><c> to</c><00:00:17.340><c> do</c><00:00:17.520><c> it</c><00:00:17.640><c> and</c><00:00:18.240><c> so</c><00:00:18.480><c> I</c>

00:00:18.590 --> 00:00:18.600 align:start position:0%
I can't remember how to do it and so I
 

00:00:18.600 --> 00:00:20.210 align:start position:0%
I can't remember how to do it and so I
do<00:00:18.720><c> a</c><00:00:18.840><c> little</c><00:00:18.900><c> searching</c><00:00:19.320><c> online</c><00:00:19.440><c> to</c><00:00:20.100><c> figure</c>

00:00:20.210 --> 00:00:20.220 align:start position:0%
do a little searching online to figure
 

00:00:20.220 --> 00:00:22.730 align:start position:0%
do a little searching online to figure
out<00:00:20.400><c> how</c><00:00:20.760><c> do</c><00:00:20.880><c> I</c><00:00:21.000><c> update</c><00:00:21.300><c> my</c><00:00:21.539><c> pile</c><00:00:21.840><c> and</c><00:00:22.140><c> it's</c><00:00:22.320><c> and</c>

00:00:22.730 --> 00:00:22.740 align:start position:0%
out how do I update my pile and it's and
 

00:00:22.740 --> 00:00:24.950 align:start position:0%
out how do I update my pile and it's and
every<00:00:22.920><c> time</c><00:00:23.160><c> I</c><00:00:23.400><c> get</c><00:00:23.520><c> I</c><00:00:24.000><c> don't</c><00:00:24.180><c> know</c><00:00:24.240><c> I</c><00:00:24.480><c> guess</c><00:00:24.660><c> a</c>

00:00:24.950 --> 00:00:24.960 align:start position:0%
every time I get I don't know I guess a
 

00:00:24.960 --> 00:00:27.470 align:start position:0%
every time I get I don't know I guess a
little<00:00:25.019><c> bit</c><00:00:25.260><c> uh</c><00:00:25.859><c> upset</c><00:00:26.340><c> at</c><00:00:26.519><c> myself</c><00:00:26.699><c> because</c><00:00:27.119><c> it</c>

00:00:27.470 --> 00:00:27.480 align:start position:0%
little bit uh upset at myself because it
 

00:00:27.480 --> 00:00:30.529 align:start position:0%
little bit uh upset at myself because it
is<00:00:27.660><c> so</c><00:00:27.960><c> easy</c><00:00:28.279><c> and</c><00:00:29.279><c> I</c><00:00:29.580><c> think</c><00:00:29.880><c> I'm</c><00:00:30.000><c> creating</c><00:00:30.359><c> this</c>

00:00:30.529 --> 00:00:30.539 align:start position:0%
is so easy and I think I'm creating this
 

00:00:30.539 --> 00:00:32.749 align:start position:0%
is so easy and I think I'm creating this
video<00:00:30.720><c> for</c><00:00:31.140><c> future</c><00:00:31.380><c> me</c><00:00:31.679><c> so</c><00:00:32.220><c> I</c><00:00:32.399><c> don't</c><00:00:32.520><c> have</c><00:00:32.640><c> to</c>

00:00:32.749 --> 00:00:32.759 align:start position:0%
video for future me so I don't have to
 

00:00:32.759 --> 00:00:35.150 align:start position:0%
video for future me so I don't have to
feel<00:00:33.000><c> quite</c><00:00:33.300><c> so</c><00:00:33.480><c> stupid</c><00:00:33.780><c> well</c><00:00:34.260><c> maybe</c><00:00:34.739><c> I</c><00:00:34.980><c> still</c>

00:00:35.150 --> 00:00:35.160 align:start position:0%
feel quite so stupid well maybe I still
 

00:00:35.160 --> 00:00:37.910 align:start position:0%
feel quite so stupid well maybe I still
feel<00:00:35.399><c> stupid</c><00:00:35.640><c> but</c><00:00:36.059><c> I</c><00:00:36.960><c> don't</c><00:00:37.079><c> have</c><00:00:37.260><c> to</c><00:00:37.380><c> spend</c><00:00:37.680><c> a</c>

00:00:37.910 --> 00:00:37.920 align:start position:0%
feel stupid but I don't have to spend a
 

00:00:37.920 --> 00:00:39.350 align:start position:0%
feel stupid but I don't have to spend a
whole<00:00:38.040><c> lot</c><00:00:38.160><c> of</c><00:00:38.280><c> time</c><00:00:38.399><c> searching</c><00:00:38.940><c> because</c><00:00:39.120><c> I</c>

00:00:39.350 --> 00:00:39.360 align:start position:0%
whole lot of time searching because I
 

00:00:39.360 --> 00:00:41.569 align:start position:0%
whole lot of time searching because I
can<00:00:39.480><c> just</c><00:00:39.600><c> look</c><00:00:39.719><c> at</c><00:00:39.840><c> my</c><00:00:40.020><c> own</c><00:00:40.140><c> video</c><00:00:40.320><c> so</c><00:00:41.160><c> here</c><00:00:41.399><c> I</c>

00:00:41.569 --> 00:00:41.579 align:start position:0%
can just look at my own video so here I
 

00:00:41.579 --> 00:00:43.549 align:start position:0%
can just look at my own video so here I
am<00:00:41.640><c> on</c><00:00:41.820><c> my</c><00:00:42.000><c> pie</c><00:00:42.180><c> hole</c><00:00:42.480><c> I've</c><00:00:42.899><c> actually</c><00:00:43.079><c> I'm</c>

00:00:43.549 --> 00:00:43.559 align:start position:0%
am on my pie hole I've actually I'm
 

00:00:43.559 --> 00:00:45.410 align:start position:0%
am on my pie hole I've actually I'm
actually<00:00:43.800><c> showing</c><00:00:44.100><c> my</c><00:00:44.219><c> graphical</c><00:00:44.640><c> Statistics</c>

00:00:45.410 --> 00:00:45.420 align:start position:0%
actually showing my graphical Statistics
 

00:00:45.420 --> 00:00:48.290 align:start position:0%
actually showing my graphical Statistics
over<00:00:46.320><c> the</c><00:00:46.620><c> last</c><00:00:46.800><c> uh</c><00:00:47.340><c> looks</c><00:00:47.640><c> like</c><00:00:47.700><c> about</c><00:00:47.940><c> a</c><00:00:48.180><c> week</c>

00:00:48.290 --> 00:00:48.300 align:start position:0%
over the last uh looks like about a week
 

00:00:48.300 --> 00:00:52.130 align:start position:0%
over the last uh looks like about a week
or<00:00:48.539><c> so</c><00:00:48.920><c> but</c><00:00:50.000><c> on</c><00:00:51.000><c> any</c><00:00:51.180><c> page</c><00:00:51.480><c> that</c><00:00:51.660><c> you're</c><00:00:51.780><c> on</c><00:00:51.960><c> no</c>

00:00:52.130 --> 00:00:52.140 align:start position:0%
or so but on any page that you're on no
 

00:00:52.140 --> 00:00:54.170 align:start position:0%
or so but on any page that you're on no
matter<00:00:52.320><c> what</c><00:00:52.559><c> page</c><00:00:52.800><c> you're</c><00:00:52.980><c> on</c><00:00:53.160><c> down</c><00:00:53.879><c> here</c><00:00:54.059><c> at</c>

00:00:54.170 --> 00:00:54.180 align:start position:0%
matter what page you're on down here at
 

00:00:54.180 --> 00:00:55.670 align:start position:0%
matter what page you're on down here at
the<00:00:54.300><c> bottom</c><00:00:54.600><c> sometimes</c><00:00:55.079><c> you</c><00:00:55.260><c> have</c><00:00:55.379><c> to</c><00:00:55.500><c> scroll</c>

00:00:55.670 --> 00:00:55.680 align:start position:0%
the bottom sometimes you have to scroll
 

00:00:55.680 --> 00:00:58.729 align:start position:0%
the bottom sometimes you have to scroll
down<00:00:55.920><c> to</c><00:00:56.160><c> the</c><00:00:56.280><c> bottom</c><00:00:56.579><c> you'll</c><00:00:57.300><c> see</c><00:00:57.600><c> what's</c><00:00:58.500><c> a</c>

00:00:58.729 --> 00:00:58.739 align:start position:0%
down to the bottom you'll see what's a
 

00:00:58.739 --> 00:01:01.549 align:start position:0%
down to the bottom you'll see what's a
current<00:00:58.920><c> version</c><00:00:59.340><c> of</c><00:01:00.239><c> the</c><00:01:00.539><c> the</c><00:01:00.840><c> pie</c><00:01:01.140><c> hole</c><00:01:01.320><c> that</c>

00:01:01.549 --> 00:01:01.559 align:start position:0%
current version of the the pie hole that
 

00:01:01.559 --> 00:01:03.529 align:start position:0%
current version of the the pie hole that
you've<00:01:01.680><c> got</c><00:01:01.800><c> pie</c><00:01:02.039><c> hole</c><00:01:02.399><c> and</c><00:01:02.579><c> FTL</c><00:01:03.059><c> and</c><00:01:03.359><c> web</c>

00:01:03.529 --> 00:01:03.539 align:start position:0%
you've got pie hole and FTL and web
 

00:01:03.539 --> 00:01:05.929 align:start position:0%
you've got pie hole and FTL and web
interface<00:01:04.080><c> and</c><00:01:04.739><c> there</c><00:01:04.920><c> it</c><00:01:05.100><c> is</c><00:01:05.220><c> and</c><00:01:05.460><c> and</c><00:01:05.760><c> if</c>

00:01:05.929 --> 00:01:05.939 align:start position:0%
interface and there it is and and if
 

00:01:05.939 --> 00:01:07.789 align:start position:0%
interface and there it is and and if
there<00:01:06.060><c> was</c><00:01:06.180><c> an</c><00:01:06.360><c> update</c><00:01:06.600><c> available</c><00:01:06.900><c> it</c><00:01:07.740><c> would</c>

00:01:07.789 --> 00:01:07.799 align:start position:0%
there was an update available it would
 

00:01:07.799 --> 00:01:09.890 align:start position:0%
there was an update available it would
show<00:01:08.040><c> a</c><00:01:08.280><c> little</c><00:01:08.400><c> tag</c><00:01:08.939><c> next</c><00:01:09.180><c> to</c><00:01:09.360><c> it</c><00:01:09.540><c> saying</c>

00:01:09.890 --> 00:01:09.900 align:start position:0%
show a little tag next to it saying
 

00:01:09.900 --> 00:01:12.590 align:start position:0%
show a little tag next to it saying
something<00:01:10.260><c> like</c><00:01:10.500><c> update</c><00:01:10.920><c> available</c><00:01:11.159><c> okay</c><00:01:12.119><c> so</c>

00:01:12.590 --> 00:01:12.600 align:start position:0%
something like update available okay so
 

00:01:12.600 --> 00:01:14.690 align:start position:0%
something like update available okay so
how<00:01:12.960><c> do</c><00:01:13.140><c> we</c><00:01:13.260><c> do</c><00:01:13.439><c> the</c><00:01:13.619><c> update</c><00:01:13.920><c> you</c><00:01:14.159><c> can't</c><00:01:14.280><c> if</c><00:01:14.520><c> you</c>

00:01:14.690 --> 00:01:14.700 align:start position:0%
how do we do the update you can't if you
 

00:01:14.700 --> 00:01:17.330 align:start position:0%
how do we do the update you can't if you
just<00:01:14.820><c> click</c><00:01:15.060><c> on</c><00:01:15.240><c> these</c><00:01:15.960><c> links</c><00:01:16.560><c> they</c><00:01:16.860><c> will</c><00:01:17.100><c> go</c>

00:01:17.330 --> 00:01:17.340 align:start position:0%
just click on these links they will go
 

00:01:17.340 --> 00:01:20.749 align:start position:0%
just click on these links they will go
to<00:01:17.520><c> the</c><00:01:17.760><c> GitHub</c><00:01:18.140><c> release</c><00:01:19.140><c> that</c><00:01:19.860><c> describes</c><00:01:20.400><c> the</c>

00:01:20.749 --> 00:01:20.759 align:start position:0%
to the GitHub release that describes the
 

00:01:20.759 --> 00:01:22.190 align:start position:0%
to the GitHub release that describes the
current<00:01:20.880><c> report</c><00:01:21.240><c> the</c><00:01:21.600><c> release</c><00:01:21.780><c> that</c><00:01:22.080><c> you're</c>

00:01:22.190 --> 00:01:22.200 align:start position:0%
current report the release that you're
 

00:01:22.200 --> 00:01:25.310 align:start position:0%
current report the release that you're
on<00:01:22.500><c> so</c><00:01:22.920><c> how</c><00:01:23.040><c> do</c><00:01:23.159><c> you</c><00:01:23.280><c> go</c><00:01:23.460><c> about</c><00:01:23.580><c> updating</c><00:01:24.840><c> so</c>

00:01:25.310 --> 00:01:25.320 align:start position:0%
on so how do you go about updating so
 

00:01:25.320 --> 00:01:26.929 align:start position:0%
on so how do you go about updating so
the<00:01:25.439><c> first</c><00:01:25.560><c> thing</c><00:01:25.680><c> I</c><00:01:25.860><c> want</c><00:01:25.979><c> to</c><00:01:26.100><c> do</c><00:01:26.220><c> is</c><00:01:26.520><c> switch</c>

00:01:26.929 --> 00:01:26.939 align:start position:0%
the first thing I want to do is switch
 

00:01:26.939 --> 00:01:30.830 align:start position:0%
the first thing I want to do is switch
over<00:01:27.119><c> to</c><00:01:27.479><c> my</c><00:01:27.780><c> my</c><00:01:28.259><c> terminal</c><00:01:28.619><c> window</c><00:01:29.060><c> and</c><00:01:30.060><c> here</c><00:01:30.540><c> I</c>

00:01:30.830 --> 00:01:30.840 align:start position:0%
over to my my terminal window and here I
 

00:01:30.840 --> 00:01:34.550 align:start position:0%
over to my my terminal window and here I
want<00:01:31.020><c> to</c><00:01:31.200><c> SSH</c><00:01:31.680><c> into</c><00:01:32.540><c> the</c><00:01:33.540><c> server</c><00:01:33.960><c> that</c><00:01:34.320><c> is</c>

00:01:34.550 --> 00:01:34.560 align:start position:0%
want to SSH into the server that is
 

00:01:34.560 --> 00:01:37.670 align:start position:0%
want to SSH into the server that is
running<00:01:35.180><c> my</c><00:01:36.180><c> pie</c><00:01:36.540><c> hole</c><00:01:36.900><c> so</c><00:01:37.200><c> I'm</c><00:01:37.320><c> just</c><00:01:37.439><c> going</c><00:01:37.560><c> to</c>

00:01:37.670 --> 00:01:37.680 align:start position:0%
running my pie hole so I'm just going to
 

00:01:37.680 --> 00:01:39.469 align:start position:0%
running my pie hole so I'm just going to
do<00:01:37.799><c> SSH</c>

00:01:39.469 --> 00:01:39.479 align:start position:0%
do SSH
 

00:01:39.479 --> 00:01:41.270 align:start position:0%
do SSH
um<00:01:39.600><c> and</c><00:01:39.840><c> the</c><00:01:40.020><c> name</c><00:01:40.140><c> of</c><00:01:40.320><c> my</c><00:01:40.439><c> server</c><00:01:40.799><c> is</c><00:01:40.920><c> Falk</c>

00:01:41.270 --> 00:01:41.280 align:start position:0%
um and the name of my server is Falk
 

00:01:41.280 --> 00:01:43.429 align:start position:0%
um and the name of my server is Falk
Falk<00:01:41.820><c> is</c><00:01:42.000><c> the</c><00:01:42.180><c> street</c><00:01:42.360><c> that</c><00:01:42.600><c> I</c><00:01:42.720><c> live</c><00:01:42.900><c> on</c><00:01:43.020><c> here</c>

00:01:43.429 --> 00:01:43.439 align:start position:0%
Falk is the street that I live on here
 

00:01:43.439 --> 00:01:45.789 align:start position:0%
Falk is the street that I live on here
on<00:01:43.619><c> Bainbridge</c><00:01:43.979><c> Island</c><00:01:44.180><c> and</c><00:01:45.180><c> I</c><00:01:45.360><c> press</c><00:01:45.540><c> enter</c>

00:01:45.789 --> 00:01:45.799 align:start position:0%
on Bainbridge Island and I press enter
 

00:01:45.799 --> 00:01:48.830 align:start position:0%
on Bainbridge Island and I press enter
and<00:01:46.799><c> I'm</c><00:01:47.040><c> using</c><00:01:47.280><c> key</c><00:01:47.460><c> pair</c><00:01:47.759><c> to</c><00:01:48.119><c> manage</c><00:01:48.420><c> all</c><00:01:48.720><c> of</c>

00:01:48.830 --> 00:01:48.840 align:start position:0%
and I'm using key pair to manage all of
 

00:01:48.840 --> 00:01:51.950 align:start position:0%
and I'm using key pair to manage all of
my<00:01:49.140><c> SSH</c><00:01:49.500><c> key</c><00:01:49.979><c> pairs</c><00:01:50.399><c> so</c><00:01:51.240><c> I'll</c><00:01:51.420><c> use</c><00:01:51.659><c> my</c>

00:01:51.950 --> 00:01:51.960 align:start position:0%
my SSH key pairs so I'll use my
 

00:01:51.960 --> 00:01:54.649 align:start position:0%
my SSH key pairs so I'll use my
fingerprint<00:01:52.500><c> to</c><00:01:52.799><c> say</c><00:01:53.399><c> that</c><00:01:53.700><c> yes</c><00:01:53.939><c> I</c><00:01:54.180><c> am</c><00:01:54.299><c> who</c><00:01:54.479><c> I</c>

00:01:54.649 --> 00:01:54.659 align:start position:0%
fingerprint to say that yes I am who I
 

00:01:54.659 --> 00:01:59.149 align:start position:0%
fingerprint to say that yes I am who I
say<00:01:54.840><c> I</c><00:01:54.960><c> am</c><00:01:55.140><c> and</c><00:01:55.799><c> now</c><00:01:55.979><c> I'm</c><00:01:56.220><c> logged</c><00:01:56.700><c> into</c><00:01:57.259><c> my</c><00:01:58.259><c> my</c>

00:01:59.149 --> 00:01:59.159 align:start position:0%
say I am and now I'm logged into my my
 

00:01:59.159 --> 00:02:03.469 align:start position:0%
say I am and now I'm logged into my my
Faulk<00:02:00.200><c> Raspberry</c><00:02:01.200><c> Pi</c><00:02:01.640><c> and</c><00:02:02.640><c> really</c><00:02:02.939><c> all</c><00:02:03.240><c> I</c><00:02:03.360><c> have</c>

00:02:03.469 --> 00:02:03.479 align:start position:0%
Faulk Raspberry Pi and really all I have
 

00:02:03.479 --> 00:02:07.730 align:start position:0%
Faulk Raspberry Pi and really all I have
to<00:02:03.600><c> do</c><00:02:03.720><c> here</c><00:02:03.979><c> is</c><00:02:04.979><c> just</c><00:02:05.399><c> type</c><00:02:05.640><c> in</c><00:02:05.880><c> pie</c><00:02:06.360><c> hole</c><00:02:07.200><c> and</c>

00:02:07.730 --> 00:02:07.740 align:start position:0%
to do here is just type in pie hole and
 

00:02:07.740 --> 00:02:10.490 align:start position:0%
to do here is just type in pie hole and
then<00:02:07.920><c> Dash</c><00:02:08.280><c> up</c><00:02:08.520><c> and</c><00:02:09.239><c> that's</c><00:02:09.360><c> it</c><00:02:09.599><c> press</c><00:02:10.259><c> enter</c>

00:02:10.490 --> 00:02:10.500 align:start position:0%
then Dash up and that's it press enter
 

00:02:10.500 --> 00:02:13.369 align:start position:0%
then Dash up and that's it press enter
it's<00:02:11.400><c> going</c><00:02:11.640><c> to</c><00:02:11.700><c> do</c><00:02:12.000><c> a</c><00:02:12.120><c> search</c><00:02:12.300><c> for</c><00:02:12.720><c> are</c><00:02:13.140><c> there</c>

00:02:13.369 --> 00:02:13.379 align:start position:0%
it's going to do a search for are there
 

00:02:13.379 --> 00:02:16.130 align:start position:0%
it's going to do a search for are there
any<00:02:13.560><c> updates</c><00:02:13.980><c> to</c><00:02:14.280><c> be</c><00:02:14.459><c> done</c>

00:02:16.130 --> 00:02:16.140 align:start position:0%
any updates to be done
 

00:02:16.140 --> 00:02:17.930 align:start position:0%
any updates to be done
everything's<00:02:16.800><c> up</c><00:02:16.920><c> to</c><00:02:17.040><c> date</c><00:02:17.220><c> on</c><00:02:17.459><c> mine</c><00:02:17.700><c> because</c>

00:02:17.930 --> 00:02:17.940 align:start position:0%
everything's up to date on mine because
 

00:02:17.940 --> 00:02:21.290 align:start position:0%
everything's up to date on mine because
I<00:02:18.360><c> just</c><00:02:18.540><c> did</c><00:02:18.660><c> this</c><00:02:18.840><c> update</c><00:02:19.459><c> but</c><00:02:20.459><c> if</c><00:02:20.760><c> there</c><00:02:21.060><c> were</c>

00:02:21.290 --> 00:02:21.300 align:start position:0%
I just did this update but if there were
 

00:02:21.300 --> 00:02:23.570 align:start position:0%
I just did this update but if there were
was<00:02:21.840><c> anything</c><00:02:22.080><c> for</c><00:02:22.379><c> it</c><00:02:22.500><c> to</c><00:02:22.620><c> do</c><00:02:22.739><c> it</c><00:02:23.160><c> would</c><00:02:23.280><c> go</c>

00:02:23.570 --> 00:02:23.580 align:start position:0%
was anything for it to do it would go
 

00:02:23.580 --> 00:02:26.650 align:start position:0%
was anything for it to do it would go
through<00:02:23.879><c> all</c><00:02:24.420><c> the</c><00:02:24.599><c> different</c><00:02:24.780><c> uh</c><00:02:25.860><c> um</c><00:02:25.980><c> settings</c>

00:02:26.650 --> 00:02:26.660 align:start position:0%
through all the different uh um settings
 

00:02:26.660 --> 00:02:29.690 align:start position:0%
through all the different uh um settings
and<00:02:27.660><c> update</c><00:02:28.140><c> all</c><00:02:28.500><c> the</c><00:02:28.620><c> bits</c><00:02:28.860><c> and</c><00:02:28.980><c> pieces</c><00:02:29.340><c> to</c>

00:02:29.690 --> 00:02:29.700 align:start position:0%
and update all the bits and pieces to
 

00:02:29.700 --> 00:02:31.550 align:start position:0%
and update all the bits and pieces to
the<00:02:29.819><c> latest</c><00:02:30.000><c> version</c><00:02:30.300><c> and</c><00:02:30.720><c> that's</c><00:02:30.840><c> it</c><00:02:31.080><c> that</c>

00:02:31.550 --> 00:02:31.560 align:start position:0%
the latest version and that's it that
 

00:02:31.560 --> 00:02:34.790 align:start position:0%
the latest version and that's it that
it's<00:02:32.459><c> it</c><00:02:32.760><c> really</c><00:02:33.000><c> is</c><00:02:33.300><c> that</c><00:02:33.540><c> easy</c><00:02:33.780><c> so</c><00:02:34.500><c> I'm</c><00:02:34.680><c> going</c>

00:02:34.790 --> 00:02:34.800 align:start position:0%
it's it really is that easy so I'm going
 

00:02:34.800 --> 00:02:37.790 align:start position:0%
it's it really is that easy so I'm going
to<00:02:34.860><c> exit</c><00:02:35.099><c> out</c><00:02:35.220><c> of</c><00:02:35.340><c> here</c><00:02:35.540><c> and</c><00:02:36.540><c> uh</c><00:02:36.720><c> that's</c><00:02:37.020><c> it</c><00:02:37.200><c> so</c>

00:02:37.790 --> 00:02:37.800 align:start position:0%
to exit out of here and uh that's it so
 

00:02:37.800 --> 00:02:39.949 align:start position:0%
to exit out of here and uh that's it so
thanks<00:02:38.040><c> so</c><00:02:38.160><c> much</c><00:02:38.280><c> for</c><00:02:38.459><c> watching</c><00:02:38.819><c> and</c><00:02:39.599><c> uh</c>

00:02:39.949 --> 00:02:39.959 align:start position:0%
thanks so much for watching and uh
 

00:02:39.959 --> 00:02:41.930 align:start position:0%
thanks so much for watching and uh
thanks<00:02:40.560><c> so</c><00:02:40.739><c> much</c><00:02:40.860><c> for</c><00:02:41.099><c> for</c><00:02:41.340><c> watching</c><00:02:41.640><c> future</c>

00:02:41.930 --> 00:02:41.940 align:start position:0%
thanks so much for for watching future
 

00:02:41.940 --> 00:02:43.790 align:start position:0%
thanks so much for for watching future
Matt

00:02:43.790 --> 00:02:43.800 align:start position:0%
Matt
 

00:02:43.800 --> 00:02:48.200 align:start position:0%
Matt
and<00:02:44.700><c> uh</c><00:02:45.000><c> I'll</c><00:02:45.180><c> see</c><00:02:45.300><c> you</c><00:02:45.420><c> next</c><00:02:45.540><c> time</c><00:02:45.720><c> bye</c>

