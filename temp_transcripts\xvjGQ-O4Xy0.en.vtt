WEBVTT
Kind: captions
Language: en

00:00:00.160 --> 00:00:01.630 align:start position:0%
 
what<00:00:00.280><c> if</c><00:00:00.359><c> you</c><00:00:00.440><c> had</c><00:00:00.560><c> a</c><00:00:00.719><c> prompt</c><00:00:01.079><c> that</c><00:00:01.240><c> required</c>

00:00:01.630 --> 00:00:01.640 align:start position:0%
what if you had a prompt that required
 

00:00:01.640 --> 00:00:03.389 align:start position:0%
what if you had a prompt that required
multiple<00:00:02.080><c> tasks</c><00:00:02.480><c> and</c><00:00:02.600><c> instead</c><00:00:02.800><c> of</c><00:00:02.960><c> coming</c><00:00:03.199><c> up</c>

00:00:03.389 --> 00:00:03.399 align:start position:0%
multiple tasks and instead of coming up
 

00:00:03.399 --> 00:00:04.749 align:start position:0%
multiple tasks and instead of coming up
with<00:00:03.520><c> that</c><00:00:03.719><c> we</c><00:00:03.800><c> could</c><00:00:03.959><c> have</c><00:00:04.160><c> the</c><00:00:04.359><c> agents</c>

00:00:04.749 --> 00:00:04.759 align:start position:0%
with that we could have the agents
 

00:00:04.759 --> 00:00:06.389 align:start position:0%
with that we could have the agents
create<00:00:05.120><c> that</c><00:00:05.279><c> for</c><00:00:05.520><c> us</c><00:00:05.839><c> today</c><00:00:06.120><c> I'm</c><00:00:06.200><c> going</c><00:00:06.319><c> to</c>

00:00:06.389 --> 00:00:06.399 align:start position:0%
create that for us today I'm going to
 

00:00:06.399 --> 00:00:08.190 align:start position:0%
create that for us today I'm going to
show<00:00:06.560><c> you</c><00:00:06.680><c> how</c><00:00:06.799><c> we</c><00:00:06.879><c> can</c><00:00:07.000><c> teach</c><00:00:07.240><c> autogen</c><00:00:07.799><c> agents</c>

00:00:08.190 --> 00:00:08.200 align:start position:0%
show you how we can teach autogen agents
 

00:00:08.200 --> 00:00:09.870 align:start position:0%
show you how we can teach autogen agents
to<00:00:08.360><c> do</c><00:00:08.679><c> just</c><00:00:08.960><c> that</c><00:00:09.240><c> let's</c><00:00:09.400><c> say</c><00:00:09.559><c> you</c><00:00:09.639><c> just</c><00:00:09.760><c> have</c>

00:00:09.870 --> 00:00:09.880 align:start position:0%
to do just that let's say you just have
 

00:00:09.880 --> 00:00:11.589 align:start position:0%
to do just that let's say you just have
a<00:00:10.000><c> user</c><00:00:10.280><c> agent</c><00:00:10.639><c> talking</c><00:00:10.880><c> to</c><00:00:11.040><c> an</c><00:00:11.200><c> assistant</c>

00:00:11.589 --> 00:00:11.599 align:start position:0%
a user agent talking to an assistant
 

00:00:11.599 --> 00:00:13.190 align:start position:0%
a user agent talking to an assistant
agent<00:00:11.960><c> and</c><00:00:12.080><c> you</c><00:00:12.200><c> just</c><00:00:12.400><c> give</c><00:00:12.559><c> them</c><00:00:12.719><c> maybe</c><00:00:12.960><c> a</c>

00:00:13.190 --> 00:00:13.200 align:start position:0%
agent and you just give them maybe a
 

00:00:13.200 --> 00:00:15.669 align:start position:0%
agent and you just give them maybe a
complex<00:00:13.839><c> task</c><00:00:14.400><c> well</c><00:00:14.759><c> you</c><00:00:14.879><c> may</c><00:00:15.080><c> get</c><00:00:15.240><c> the</c><00:00:15.400><c> answer</c>

00:00:15.669 --> 00:00:15.679 align:start position:0%
complex task well you may get the answer
 

00:00:15.679 --> 00:00:17.429 align:start position:0%
complex task well you may get the answer
or<00:00:16.000><c> a</c><00:00:16.160><c> similar</c><00:00:16.520><c> answer</c><00:00:16.800><c> that</c><00:00:16.920><c> you're</c><00:00:17.160><c> looking</c>

00:00:17.429 --> 00:00:17.439 align:start position:0%
or a similar answer that you're looking
 

00:00:17.439 --> 00:00:19.189 align:start position:0%
or a similar answer that you're looking
for<00:00:18.000><c> or</c><00:00:18.279><c> maybe</c><00:00:18.480><c> you</c><00:00:18.600><c> don't</c><00:00:18.880><c> because</c><00:00:19.080><c> it</c>

00:00:19.189 --> 00:00:19.199 align:start position:0%
for or maybe you don't because it
 

00:00:19.199 --> 00:00:20.230 align:start position:0%
for or maybe you don't because it
doesn't<00:00:19.439><c> quite</c><00:00:19.880><c> understand</c><00:00:20.039><c> what</c><00:00:20.119><c> you're</c>

00:00:20.230 --> 00:00:20.240 align:start position:0%
doesn't quite understand what you're
 

00:00:20.240 --> 00:00:21.750 align:start position:0%
doesn't quite understand what you're
trying<00:00:20.439><c> to</c><00:00:20.600><c> ask</c><00:00:20.760><c> of</c><00:00:20.920><c> it</c><00:00:21.160><c> well</c><00:00:21.320><c> instead</c><00:00:21.600><c> what</c><00:00:21.680><c> we</c>

00:00:21.750 --> 00:00:21.760 align:start position:0%
trying to ask of it well instead what we
 

00:00:21.760 --> 00:00:23.790 align:start position:0%
trying to ask of it well instead what we
can<00:00:21.880><c> do</c><00:00:22.240><c> is</c><00:00:22.439><c> we</c><00:00:22.519><c> can</c><00:00:22.680><c> break</c><00:00:22.920><c> down</c><00:00:23.119><c> that</c><00:00:23.359><c> complex</c>

00:00:23.790 --> 00:00:23.800 align:start position:0%
can do is we can break down that complex
 

00:00:23.800 --> 00:00:26.109 align:start position:0%
can do is we can break down that complex
task<00:00:24.199><c> into</c><00:00:24.560><c> multiple</c><00:00:25.000><c> ones</c><00:00:25.439><c> and</c><00:00:25.599><c> sequentially</c>

00:00:26.109 --> 00:00:26.119 align:start position:0%
task into multiple ones and sequentially
 

00:00:26.119 --> 00:00:28.070 align:start position:0%
task into multiple ones and sequentially
tell<00:00:26.320><c> the</c><00:00:26.439><c> agent</c><00:00:26.720><c> to</c><00:00:26.920><c> perform</c><00:00:27.320><c> each</c><00:00:27.599><c> task</c><00:00:27.960><c> and</c>

00:00:28.070 --> 00:00:28.080 align:start position:0%
tell the agent to perform each task and
 

00:00:28.080 --> 00:00:30.109 align:start position:0%
tell the agent to perform each task and
at<00:00:28.199><c> the</c><00:00:28.320><c> end</c><00:00:28.840><c> ask</c><00:00:29.080><c> the</c><00:00:29.199><c> agent</c><00:00:29.439><c> to</c><00:00:29.560><c> create</c><00:00:29.759><c> a</c>

00:00:30.109 --> 00:00:30.119 align:start position:0%
at the end ask the agent to create a
 

00:00:30.119 --> 00:00:32.310 align:start position:0%
at the end ask the agent to create a
recipe<00:00:30.840><c> based</c><00:00:31.160><c> on</c><00:00:31.279><c> the</c><00:00:31.480><c> task</c><00:00:31.759><c> that</c><00:00:31.880><c> they</c><00:00:32.079><c> just</c>

00:00:32.310 --> 00:00:32.320 align:start position:0%
recipe based on the task that they just
 

00:00:32.320 --> 00:00:33.910 align:start position:0%
recipe based on the task that they just
performed<00:00:33.000><c> and</c><00:00:33.120><c> now</c><00:00:33.200><c> we'd</c><00:00:33.399><c> have</c><00:00:33.520><c> a</c><00:00:33.640><c> better</c>

00:00:33.910 --> 00:00:33.920 align:start position:0%
performed and now we'd have a better
 

00:00:33.920 --> 00:00:36.430 align:start position:0%
performed and now we'd have a better
prompt<00:00:34.239><c> or</c><00:00:34.520><c> as</c><00:00:34.800><c> what</c><00:00:34.920><c> can</c><00:00:35.040><c> be</c><00:00:35.200><c> called</c><00:00:35.440><c> a</c><00:00:35.640><c> recipe</c>

00:00:36.430 --> 00:00:36.440 align:start position:0%
prompt or as what can be called a recipe
 

00:00:36.440 --> 00:00:38.830 align:start position:0%
prompt or as what can be called a recipe
that<00:00:36.559><c> we</c><00:00:36.680><c> can</c><00:00:37.000><c> reuse</c><00:00:37.800><c> with</c><00:00:38.079><c> that</c><00:00:38.239><c> agent</c><00:00:38.559><c> later</c>

00:00:38.830 --> 00:00:38.840 align:start position:0%
that we can reuse with that agent later
 

00:00:38.840 --> 00:00:40.990 align:start position:0%
that we can reuse with that agent later
on<00:00:39.360><c> instead</c><00:00:39.600><c> of</c><00:00:39.760><c> having</c><00:00:40.079><c> multiple</c><00:00:40.520><c> steps</c><00:00:40.879><c> we</c>

00:00:40.990 --> 00:00:41.000 align:start position:0%
on instead of having multiple steps we
 

00:00:41.000 --> 00:00:42.790 align:start position:0%
on instead of having multiple steps we
just<00:00:41.160><c> have</c><00:00:41.320><c> this</c><00:00:41.480><c> one</c><00:00:41.879><c> prompt</c><00:00:42.200><c> now</c><00:00:42.520><c> that</c><00:00:42.680><c> it</c>

00:00:42.790 --> 00:00:42.800 align:start position:0%
just have this one prompt now that it
 

00:00:42.800 --> 00:00:44.990 align:start position:0%
just have this one prompt now that it
can<00:00:43.039><c> easily</c><00:00:43.879><c> understand</c><00:00:44.360><c> now</c><00:00:44.480><c> let's</c><00:00:44.680><c> code</c><00:00:44.879><c> it</c>

00:00:44.990 --> 00:00:45.000 align:start position:0%
can easily understand now let's code it
 

00:00:45.000 --> 00:00:46.750 align:start position:0%
can easily understand now let's code it
to<00:00:45.120><c> see</c><00:00:45.280><c> how</c><00:00:45.399><c> it</c><00:00:45.520><c> works</c><00:00:46.239><c> okay</c><00:00:46.399><c> the</c><00:00:46.480><c> first</c><00:00:46.680><c> thing</c>

00:00:46.750 --> 00:00:46.760 align:start position:0%
to see how it works okay the first thing
 

00:00:46.760 --> 00:00:48.350 align:start position:0%
to see how it works okay the first thing
we<00:00:46.879><c> need</c><00:00:47.000><c> to</c><00:00:47.120><c> do</c><00:00:47.360><c> is</c><00:00:47.480><c> create</c><00:00:47.680><c> a</c><00:00:47.800><c> new</c><00:00:48.000><c> project</c>

00:00:48.350 --> 00:00:48.360 align:start position:0%
we need to do is create a new project
 

00:00:48.360 --> 00:00:49.470 align:start position:0%
we need to do is create a new project
and<00:00:48.480><c> when</c><00:00:48.559><c> you've</c><00:00:48.760><c> done</c><00:00:48.920><c> that</c><00:00:49.039><c> open</c><00:00:49.239><c> up</c><00:00:49.360><c> your</c>

00:00:49.470 --> 00:00:49.480 align:start position:0%
and when you've done that open up your
 

00:00:49.480 --> 00:00:51.430 align:start position:0%
and when you've done that open up your
terminal<00:00:49.840><c> and</c><00:00:50.000><c> install</c><00:00:50.399><c> pi</c><00:00:50.640><c> autogen</c><00:00:51.160><c> and</c><00:00:51.280><c> once</c>

00:00:51.430 --> 00:00:51.440 align:start position:0%
terminal and install pi autogen and once
 

00:00:51.440 --> 00:00:52.750 align:start position:0%
terminal and install pi autogen and once
that<00:00:51.520><c> is</c><00:00:51.680><c> done</c><00:00:51.920><c> go</c><00:00:52.000><c> ahead</c><00:00:52.160><c> and</c><00:00:52.239><c> create</c><00:00:52.440><c> a</c><00:00:52.559><c> new</c>

00:00:52.750 --> 00:00:52.760 align:start position:0%
that is done go ahead and create a new
 

00:00:52.760 --> 00:00:55.270 align:start position:0%
that is done go ahead and create a new
file<00:00:53.039><c> I</c><00:00:53.199><c> named</c><00:00:53.399><c> M</c><00:00:53.760><c> main.py</c><00:00:54.760><c> the</c><00:00:54.879><c> first</c><00:00:55.079><c> thing</c>

00:00:55.270 --> 00:00:55.280 align:start position:0%
file I named M main.py the first thing
 

00:00:55.280 --> 00:00:56.869 align:start position:0%
file I named M main.py the first thing
is<00:00:55.440><c> our</c><00:00:55.640><c> import</c><00:00:56.039><c> so</c><00:00:56.199><c> go</c><00:00:56.320><c> ahead</c><00:00:56.440><c> and</c><00:00:56.559><c> import</c>

00:00:56.869 --> 00:00:56.879 align:start position:0%
is our import so go ahead and import
 

00:00:56.879 --> 00:00:59.310 align:start position:0%
is our import so go ahead and import
autogen<00:00:57.600><c> and</c><00:00:57.719><c> then</c><00:00:57.920><c> also</c><00:00:58.199><c> import</c><00:00:58.719><c> annotated</c>

00:00:59.310 --> 00:00:59.320 align:start position:0%
autogen and then also import annotated
 

00:00:59.320 --> 00:01:01.270 align:start position:0%
autogen and then also import annotated
from<00:00:59.559><c> typing</c><00:01:00.320><c> because</c><00:01:00.559><c> I</c><00:01:00.640><c> am</c><00:01:00.760><c> going</c><00:01:00.879><c> to</c><00:01:01.000><c> create</c>

00:01:01.270 --> 00:01:01.280 align:start position:0%
from typing because I am going to create
 

00:01:01.280 --> 00:01:03.509 align:start position:0%
from typing because I am going to create
a<00:01:01.399><c> simple</c><00:01:01.840><c> function</c><00:01:02.239><c> to</c><00:01:02.480><c> save</c><00:01:02.719><c> that</c><00:01:02.920><c> recipe</c><00:01:03.280><c> to</c>

00:01:03.509 --> 00:01:03.519 align:start position:0%
a simple function to save that recipe to
 

00:01:03.519 --> 00:01:05.310 align:start position:0%
a simple function to save that recipe to
a<00:01:03.680><c> file</c><00:01:04.159><c> and</c><00:01:04.280><c> the</c><00:01:04.400><c> next</c><00:01:04.559><c> thing</c><00:01:04.720><c> is</c><00:01:04.920><c> I</c><00:01:05.040><c> have</c>

00:01:05.310 --> 00:01:05.320 align:start position:0%
a file and the next thing is I have
 

00:01:05.320 --> 00:01:07.310 align:start position:0%
a file and the next thing is I have
three<00:01:05.560><c> tests</c><00:01:05.920><c> that</c><00:01:06.000><c> I</c><00:01:06.080><c> want</c><00:01:06.280><c> to</c><00:01:06.479><c> perform</c><00:01:07.159><c> then</c>

00:01:07.310 --> 00:01:07.320 align:start position:0%
three tests that I want to perform then
 

00:01:07.320 --> 00:01:09.469 align:start position:0%
three tests that I want to perform then
on<00:01:07.439><c> the</c><00:01:07.720><c> last</c><00:01:08.119><c> task</c><00:01:08.560><c> this</c><00:01:08.640><c> is</c><00:01:08.799><c> where</c><00:01:08.960><c> I</c><00:01:09.040><c> want</c><00:01:09.240><c> to</c>

00:01:09.469 --> 00:01:09.479 align:start position:0%
on the last task this is where I want to
 

00:01:09.479 --> 00:01:11.190 align:start position:0%
on the last task this is where I want to
create<00:01:09.759><c> the</c><00:01:09.960><c> recipe</c><00:01:10.600><c> and</c><00:01:10.720><c> the</c><00:01:10.799><c> function</c><00:01:11.080><c> is</c>

00:01:11.190 --> 00:01:11.200 align:start position:0%
create the recipe and the function is
 

00:01:11.200 --> 00:01:13.390 align:start position:0%
create the recipe and the function is
going<00:01:11.280><c> to</c><00:01:11.360><c> save</c><00:01:11.600><c> that</c><00:01:11.759><c> recipe</c><00:01:12.159><c> into</c><00:01:12.479><c> a</c><00:01:12.680><c> file</c>

00:01:13.390 --> 00:01:13.400 align:start position:0%
going to save that recipe into a file
 

00:01:13.400 --> 00:01:15.270 align:start position:0%
going to save that recipe into a file
okay<00:01:13.600><c> and</c><00:01:13.720><c> then</c><00:01:13.840><c> we</c><00:01:13.960><c> need</c><00:01:14.080><c> to</c><00:01:14.280><c> create</c><00:01:14.560><c> the</c><00:01:14.680><c> llm</c>

00:01:15.270 --> 00:01:15.280 align:start position:0%
okay and then we need to create the llm
 

00:01:15.280 --> 00:01:17.350 align:start position:0%
okay and then we need to create the llm
config<00:01:15.640><c> for</c><00:01:15.880><c> the</c><00:01:16.040><c> agents</c><00:01:16.840><c> and</c><00:01:16.960><c> you</c><00:01:17.040><c> can</c><00:01:17.159><c> just</c>

00:01:17.350 --> 00:01:17.360 align:start position:0%
config for the agents and you can just
 

00:01:17.360 --> 00:01:19.270 align:start position:0%
config for the agents and you can just
initiate<00:01:17.720><c> it</c><00:01:17.840><c> with</c><00:01:18.000><c> this</c><00:01:18.200><c> variable</c><00:01:18.960><c> I</c><00:01:19.119><c> give</c>

00:01:19.270 --> 00:01:19.280 align:start position:0%
initiate it with this variable I give
 

00:01:19.280 --> 00:01:21.910 align:start position:0%
initiate it with this variable I give
the<00:01:19.479><c> timeout</c><00:01:19.960><c> of</c><00:01:20.119><c> 2</c><00:01:20.280><c> minutes</c><00:01:20.560><c> or</c><00:01:20.720><c> 120</c><00:01:21.280><c> seconds</c>

00:01:21.910 --> 00:01:21.920 align:start position:0%
the timeout of 2 minutes or 120 seconds
 

00:01:21.920 --> 00:01:23.230 align:start position:0%
the timeout of 2 minutes or 120 seconds
you<00:01:22.040><c> can</c><00:01:22.159><c> give</c><00:01:22.360><c> whatever</c><00:01:22.640><c> cache</c><00:01:22.880><c> seed</c><00:01:23.119><c> you</c>

00:01:23.230 --> 00:01:23.240 align:start position:0%
you can give whatever cache seed you
 

00:01:23.240 --> 00:01:24.830 align:start position:0%
you can give whatever cache seed you
want<00:01:23.439><c> you</c><00:01:23.520><c> can</c><00:01:23.720><c> keep</c><00:01:24.000><c> changing</c><00:01:24.400><c> this</c><00:01:24.520><c> so</c><00:01:24.680><c> you</c>

00:01:24.830 --> 00:01:24.840 align:start position:0%
want you can keep changing this so you
 

00:01:24.840 --> 00:01:26.510 align:start position:0%
want you can keep changing this so you
get<00:01:25.119><c> different</c><00:01:25.520><c> results</c><00:01:26.119><c> and</c><00:01:26.200><c> then</c><00:01:26.280><c> for</c><00:01:26.400><c> the</c>

00:01:26.510 --> 00:01:26.520 align:start position:0%
get different results and then for the
 

00:01:26.520 --> 00:01:28.670 align:start position:0%
get different results and then for the
config<00:01:26.840><c> list</c><00:01:27.119><c> we</c><00:01:27.240><c> get</c><00:01:27.400><c> that</c><00:01:27.640><c> from</c><00:01:27.840><c> the</c><00:01:28.000><c> oi</c>

00:01:28.670 --> 00:01:28.680 align:start position:0%
config list we get that from the oi
 

00:01:28.680 --> 00:01:31.190 align:start position:0%
config list we get that from the oi
config<00:01:29.040><c> list.</c><00:01:29.360><c> Json</c><00:01:29.680><c> file</c><00:01:29.960><c> file</c><00:01:30.600><c> which</c><00:01:30.880><c> I</c><00:01:31.040><c> went</c>

00:01:31.190 --> 00:01:31.200 align:start position:0%
config list. Json file file which I went
 

00:01:31.200 --> 00:01:32.870 align:start position:0%
config list. Json file file which I went
ahead<00:01:31.360><c> and</c><00:01:31.439><c> created</c><00:01:31.759><c> right</c><00:01:31.880><c> over</c><00:01:32.119><c> here</c><00:01:32.479><c> so</c><00:01:32.799><c> if</c>

00:01:32.870 --> 00:01:32.880 align:start position:0%
ahead and created right over here so if
 

00:01:32.880 --> 00:01:34.389 align:start position:0%
ahead and created right over here so if
you<00:01:33.000><c> haven't</c><00:01:33.159><c> done</c><00:01:33.320><c> that</c><00:01:33.439><c> already</c><00:01:34.079><c> go</c><00:01:34.200><c> ahead</c>

00:01:34.389 --> 00:01:34.399 align:start position:0%
you haven't done that already go ahead
 

00:01:34.399 --> 00:01:36.590 align:start position:0%
you haven't done that already go ahead
and<00:01:34.520><c> create</c><00:01:34.759><c> this</c><00:01:34.960><c> file</c><00:01:35.560><c> open</c><00:01:35.799><c> it</c><00:01:36.000><c> up</c><00:01:36.240><c> and</c><00:01:36.439><c> all</c>

00:01:36.590 --> 00:01:36.600 align:start position:0%
and create this file open it up and all
 

00:01:36.600 --> 00:01:38.749 align:start position:0%
and create this file open it up and all
we<00:01:36.799><c> need</c><00:01:37.280><c> is</c><00:01:37.399><c> to</c><00:01:37.560><c> give</c><00:01:37.720><c> the</c><00:01:37.920><c> model</c><00:01:38.320><c> which</c><00:01:38.640><c> I</c>

00:01:38.749 --> 00:01:38.759 align:start position:0%
we need is to give the model which I
 

00:01:38.759 --> 00:01:41.510 align:start position:0%
we need is to give the model which I
chose<00:01:39.000><c> a</c><00:01:39.159><c> 3.5</c><00:01:39.799><c> turbo</c><00:01:40.640><c> and</c><00:01:40.840><c> you</c><00:01:40.920><c> want</c><00:01:41.040><c> to</c><00:01:41.280><c> give</c>

00:01:41.510 --> 00:01:41.520 align:start position:0%
chose a 3.5 turbo and you want to give
 

00:01:41.520 --> 00:01:43.910 align:start position:0%
chose a 3.5 turbo and you want to give
your<00:01:41.880><c> API</c><00:01:42.439><c> key</c><00:01:42.759><c> here</c><00:01:43.159><c> and</c><00:01:43.240><c> then</c><00:01:43.360><c> I</c><00:01:43.479><c> filter</c><00:01:43.759><c> it</c>

00:01:43.910 --> 00:01:43.920 align:start position:0%
your API key here and then I filter it
 

00:01:43.920 --> 00:01:45.590 align:start position:0%
your API key here and then I filter it
out<00:01:44.119><c> by</c><00:01:44.280><c> the</c><00:01:44.439><c> model</c><00:01:45.040><c> and</c><00:01:45.159><c> then</c><00:01:45.280><c> I</c><00:01:45.399><c> give</c><00:01:45.479><c> a</c>

00:01:45.590 --> 00:01:45.600 align:start position:0%
out by the model and then I give a
 

00:01:45.600 --> 00:01:47.190 align:start position:0%
out by the model and then I give a
temperature<00:01:46.000><c> of</c><00:01:46.159><c> zero</c><00:01:46.600><c> cuz</c><00:01:46.719><c> I</c><00:01:46.799><c> don't</c><00:01:46.920><c> want</c><00:01:47.040><c> too</c>

00:01:47.190 --> 00:01:47.200 align:start position:0%
temperature of zero cuz I don't want too
 

00:01:47.200 --> 00:01:48.469 align:start position:0%
temperature of zero cuz I don't want too
much<00:01:47.399><c> variation</c><00:01:47.880><c> and</c><00:01:48.000><c> then</c><00:01:48.079><c> I</c><00:01:48.159><c> went</c><00:01:48.280><c> ahead</c><00:01:48.399><c> and</c>

00:01:48.469 --> 00:01:48.479 align:start position:0%
much variation and then I went ahead and
 

00:01:48.479 --> 00:01:49.950 align:start position:0%
much variation and then I went ahead and
created<00:01:48.799><c> three</c><00:01:49.000><c> agents</c><00:01:49.439><c> two</c><00:01:49.600><c> of</c><00:01:49.680><c> them</c><00:01:49.799><c> are</c>

00:01:49.950 --> 00:01:49.960 align:start position:0%
created three agents two of them are
 

00:01:49.960 --> 00:01:51.550 align:start position:0%
created three agents two of them are
assistant<00:01:50.280><c> agents</c><00:01:50.719><c> and</c><00:01:50.920><c> another</c><00:01:51.159><c> one</c><00:01:51.320><c> is</c><00:01:51.439><c> the</c>

00:01:51.550 --> 00:01:51.560 align:start position:0%
assistant agents and another one is the
 

00:01:51.560 --> 00:01:53.270 align:start position:0%
assistant agents and another one is the
user<00:01:51.840><c> proxy</c><00:01:52.159><c> agent</c><00:01:52.479><c> the</c><00:01:52.600><c> assistant</c><00:01:52.920><c> agents</c>

00:01:53.270 --> 00:01:53.280 align:start position:0%
user proxy agent the assistant agents
 

00:01:53.280 --> 00:01:54.749 align:start position:0%
user proxy agent the assistant agents
are<00:01:53.520><c> pretty</c><00:01:53.719><c> simple</c><00:01:54.159><c> I</c><00:01:54.240><c> have</c><00:01:54.360><c> one</c><00:01:54.520><c> named</c>

00:01:54.749 --> 00:01:54.759 align:start position:0%
are pretty simple I have one named
 

00:01:54.759 --> 00:01:56.230 align:start position:0%
are pretty simple I have one named
assistant<00:01:55.079><c> agent</c><00:01:55.320><c> and</c><00:01:55.479><c> another</c><00:01:55.719><c> one</c><00:01:55.920><c> named</c>

00:01:56.230 --> 00:01:56.240 align:start position:0%
assistant agent and another one named
 

00:01:56.240 --> 00:01:57.990 align:start position:0%
assistant agent and another one named
recipe<00:01:56.640><c> assistant</c><00:01:57.240><c> and</c><00:01:57.399><c> they</c><00:01:57.520><c> both</c><00:01:57.719><c> had</c><00:01:57.880><c> the</c>

00:01:57.990 --> 00:01:58.000 align:start position:0%
recipe assistant and they both had the
 

00:01:58.000 --> 00:01:59.910 align:start position:0%
recipe assistant and they both had the
same<00:01:58.200><c> llm</c><00:01:58.719><c> config</c><00:01:59.159><c> again</c><00:01:59.320><c> you</c><00:01:59.399><c> only</c><00:01:59.600><c> give</c><00:01:59.880><c> the</c>

00:01:59.910 --> 00:01:59.920 align:start position:0%
same llm config again you only give the
 

00:01:59.920 --> 00:02:01.550 align:start position:0%
same llm config again you only give the
llm<00:02:00.240><c> config</c><00:02:00.560><c> to</c><00:02:00.719><c> the</c><00:02:00.799><c> assistant</c><00:02:01.159><c> agents</c>

00:02:01.550 --> 00:02:01.560 align:start position:0%
llm config to the assistant agents
 

00:02:01.560 --> 00:02:02.469 align:start position:0%
llm config to the assistant agents
because<00:02:01.799><c> they're</c><00:02:02.000><c> the</c><00:02:02.079><c> ones</c><00:02:02.240><c> that</c><00:02:02.320><c> are</c><00:02:02.399><c> going</c>

00:02:02.469 --> 00:02:02.479 align:start position:0%
because they're the ones that are going
 

00:02:02.479 --> 00:02:04.270 align:start position:0%
because they're the ones that are going
to<00:02:02.560><c> be</c><00:02:02.680><c> talking</c><00:02:03.039><c> to</c><00:02:03.280><c> the</c><00:02:03.399><c> llm</c><00:02:04.000><c> and</c><00:02:04.079><c> then</c><00:02:04.200><c> for</c>

00:02:04.270 --> 00:02:04.280 align:start position:0%
to be talking to the llm and then for
 

00:02:04.280 --> 00:02:06.069 align:start position:0%
to be talking to the llm and then for
the<00:02:04.360><c> user</c><00:02:04.640><c> proxy</c><00:02:05.000><c> agent</c><00:02:05.360><c> the</c><00:02:05.479><c> simple</c><00:02:05.759><c> name</c><00:02:05.920><c> is</c>

00:02:06.069 --> 00:02:06.079 align:start position:0%
the user proxy agent the simple name is
 

00:02:06.079 --> 00:02:08.229 align:start position:0%
the user proxy agent the simple name is
user<00:02:06.759><c> the</c><00:02:06.880><c> human</c><00:02:07.119><c> input</c><00:02:07.399><c> mode</c><00:02:07.560><c> is</c><00:02:07.680><c> never</c><00:02:08.039><c> so</c>

00:02:08.229 --> 00:02:08.239 align:start position:0%
user the human input mode is never so
 

00:02:08.239 --> 00:02:09.790 align:start position:0%
user the human input mode is never so
I'm<00:02:08.360><c> never</c><00:02:08.560><c> going</c><00:02:08.640><c> to</c><00:02:08.720><c> have</c><00:02:08.800><c> any</c><00:02:08.959><c> intervention</c>

00:02:09.790 --> 00:02:09.800 align:start position:0%
I'm never going to have any intervention
 

00:02:09.800 --> 00:02:11.869 align:start position:0%
I'm never going to have any intervention
with<00:02:10.119><c> the</c><00:02:10.280><c> llm</c><00:02:10.759><c> calls</c><00:02:11.239><c> then</c><00:02:11.360><c> for</c><00:02:11.480><c> the</c><00:02:11.599><c> code</c>

00:02:11.869 --> 00:02:11.879 align:start position:0%
with the llm calls then for the code
 

00:02:11.879 --> 00:02:13.630 align:start position:0%
with the llm calls then for the code
execution<00:02:12.400><c> it's</c><00:02:12.560><c> going</c><00:02:12.640><c> to</c><00:02:12.800><c> create</c><00:02:13.120><c> a</c><00:02:13.239><c> folder</c>

00:02:13.630 --> 00:02:13.640 align:start position:0%
execution it's going to create a folder
 

00:02:13.640 --> 00:02:15.589 align:start position:0%
execution it's going to create a folder
called<00:02:13.879><c> teaching</c><00:02:14.560><c> under</c><00:02:15.000><c> the</c><00:02:15.120><c> autogen</c>

00:02:15.589 --> 00:02:15.599 align:start position:0%
called teaching under the autogen
 

00:02:15.599 --> 00:02:17.589 align:start position:0%
called teaching under the autogen
teaching<00:02:16.000><c> directory</c><00:02:16.440><c> over</c><00:02:16.680><c> here</c><00:02:17.040><c> once</c><00:02:17.400><c> it's</c>

00:02:17.589 --> 00:02:17.599 align:start position:0%
teaching directory over here once it's
 

00:02:17.599 --> 00:02:19.110 align:start position:0%
teaching directory over here once it's
finished<00:02:18.040><c> and</c><00:02:18.120><c> for</c><00:02:18.239><c> use</c><00:02:18.440><c> Docker</c><00:02:18.800><c> this</c><00:02:18.879><c> is</c><00:02:19.000><c> set</c>

00:02:19.110 --> 00:02:19.120 align:start position:0%
finished and for use Docker this is set
 

00:02:19.120 --> 00:02:20.630 align:start position:0%
finished and for use Docker this is set
to<00:02:19.280><c> false</c><00:02:19.640><c> if</c><00:02:19.720><c> you</c><00:02:19.840><c> have</c><00:02:19.959><c> Docker</c><00:02:20.239><c> running</c><00:02:20.519><c> go</c>

00:02:20.630 --> 00:02:20.640 align:start position:0%
to false if you have Docker running go
 

00:02:20.640 --> 00:02:21.750 align:start position:0%
to false if you have Docker running go
ahead<00:02:20.760><c> and</c><00:02:20.840><c> set</c><00:02:21.000><c> this</c><00:02:21.080><c> to</c><00:02:21.200><c> true</c><00:02:21.519><c> and</c><00:02:21.599><c> here's</c>

00:02:21.750 --> 00:02:21.760 align:start position:0%
ahead and set this to true and here's
 

00:02:21.760 --> 00:02:22.589 align:start position:0%
ahead and set this to true and here's
the<00:02:21.879><c> function</c><00:02:22.120><c> that</c><00:02:22.200><c> we're</c><00:02:22.319><c> going</c><00:02:22.400><c> to</c><00:02:22.480><c> be</c>

00:02:22.589 --> 00:02:22.599 align:start position:0%
the function that we're going to be
 

00:02:22.599 --> 00:02:24.670 align:start position:0%
the function that we're going to be
creating<00:02:23.040><c> that's</c><00:02:23.280><c> simply</c><00:02:23.800><c> saving</c><00:02:24.160><c> the</c><00:02:24.319><c> recipe</c>

00:02:24.670 --> 00:02:24.680 align:start position:0%
creating that's simply saving the recipe
 

00:02:24.680 --> 00:02:26.430 align:start position:0%
creating that's simply saving the recipe
to<00:02:24.800><c> a</c><00:02:25.000><c> file</c><00:02:25.519><c> so</c><00:02:25.640><c> it's</c><00:02:25.800><c> just</c><00:02:25.920><c> called</c><00:02:26.160><c> save</c>

00:02:26.430 --> 00:02:26.440 align:start position:0%
to a file so it's just called save
 

00:02:26.440 --> 00:02:28.670 align:start position:0%
to a file so it's just called save
recipe<00:02:27.120><c> we're</c><00:02:27.280><c> going</c><00:02:27.400><c> to</c><00:02:27.599><c> create</c><00:02:28.000><c> a</c><00:02:28.160><c> new</c><00:02:28.400><c> text</c>

00:02:28.670 --> 00:02:28.680 align:start position:0%
recipe we're going to create a new text
 

00:02:28.680 --> 00:02:30.350 align:start position:0%
recipe we're going to create a new text
file<00:02:29.000><c> called</c><00:02:29.280><c> new</c><00:02:29.480><c> recipe</c>

00:02:30.350 --> 00:02:30.360 align:start position:0%
file called new recipe
 

00:02:30.360 --> 00:02:32.110 align:start position:0%
file called new recipe
and<00:02:30.519><c> then</c><00:02:30.760><c> write</c><00:02:31.200><c> the</c><00:02:31.360><c> recipe</c><00:02:31.760><c> that</c><00:02:31.840><c> were</c>

00:02:32.110 --> 00:02:32.120 align:start position:0%
and then write the recipe that were
 

00:02:32.120 --> 00:02:34.110 align:start position:0%
and then write the recipe that were
given<00:02:32.560><c> from</c><00:02:32.720><c> the</c><00:02:32.879><c> llm</c><00:02:33.319><c> call</c><00:02:33.640><c> and</c><00:02:33.720><c> then</c><00:02:33.840><c> finally</c>

00:02:34.110 --> 00:02:34.120 align:start position:0%
given from the llm call and then finally
 

00:02:34.120 --> 00:02:35.550 align:start position:0%
given from the llm call and then finally
we're<00:02:34.239><c> going</c><00:02:34.360><c> to</c><00:02:34.519><c> initiate</c><00:02:34.879><c> the</c><00:02:35.040><c> chat</c><00:02:35.319><c> so</c><00:02:35.440><c> we</c>

00:02:35.550 --> 00:02:35.560 align:start position:0%
we're going to initiate the chat so we
 

00:02:35.560 --> 00:02:36.990 align:start position:0%
we're going to initiate the chat so we
initiate<00:02:35.879><c> the</c><00:02:36.000><c> chat</c><00:02:36.239><c> with</c><00:02:36.360><c> the</c><00:02:36.519><c> assistant</c><00:02:36.840><c> for</c>

00:02:36.990 --> 00:02:37.000 align:start position:0%
initiate the chat with the assistant for
 

00:02:37.000 --> 00:02:39.550 align:start position:0%
initiate the chat with the assistant for
the<00:02:37.160><c> first</c><00:02:37.440><c> task</c><00:02:38.239><c> we</c><00:02:38.400><c> initiate</c><00:02:38.760><c> it</c><00:02:39.000><c> again</c><00:02:39.400><c> with</c>

00:02:39.550 --> 00:02:39.560 align:start position:0%
the first task we initiate it again with
 

00:02:39.560 --> 00:02:41.830 align:start position:0%
the first task we initiate it again with
the<00:02:39.720><c> second</c><00:02:40.120><c> task</c><00:02:40.920><c> again</c><00:02:41.159><c> with</c><00:02:41.319><c> the</c><00:02:41.480><c> third</c>

00:02:41.830 --> 00:02:41.840 align:start position:0%
the second task again with the third
 

00:02:41.840 --> 00:02:43.710 align:start position:0%
the second task again with the third
task<00:02:42.239><c> and</c><00:02:42.319><c> then</c><00:02:42.440><c> finally</c><00:02:42.760><c> on</c><00:02:42.879><c> the</c><00:02:43.000><c> fourth</c><00:02:43.360><c> task</c>

00:02:43.710 --> 00:02:43.720 align:start position:0%
task and then finally on the fourth task
 

00:02:43.720 --> 00:02:45.030 align:start position:0%
task and then finally on the fourth task
like<00:02:43.840><c> we</c><00:02:44.000><c> mentioned</c><00:02:44.680><c> we're</c><00:02:44.840><c> going</c><00:02:44.920><c> to</c>

00:02:45.030 --> 00:02:45.040 align:start position:0%
like we mentioned we're going to
 

00:02:45.040 --> 00:02:47.030 align:start position:0%
like we mentioned we're going to
initiate<00:02:45.360><c> the</c><00:02:45.440><c> chat</c><00:02:45.800><c> with</c><00:02:45.920><c> the</c><00:02:46.120><c> create</c><00:02:46.560><c> recipe</c>

00:02:47.030 --> 00:02:47.040 align:start position:0%
initiate the chat with the create recipe
 

00:02:47.040 --> 00:02:48.750 align:start position:0%
initiate the chat with the create recipe
assistant<00:02:47.720><c> which</c><00:02:47.840><c> is</c><00:02:47.959><c> going</c><00:02:48.080><c> to</c><00:02:48.280><c> call</c><00:02:48.560><c> this</c>

00:02:48.750 --> 00:02:48.760 align:start position:0%
assistant which is going to call this
 

00:02:48.760 --> 00:02:51.149 align:start position:0%
assistant which is going to call this
function<00:02:49.519><c> once</c><00:02:49.720><c> it</c><00:02:49.879><c> creates</c><00:02:50.200><c> the</c><00:02:50.360><c> recipe</c><00:02:50.879><c> to</c>

00:02:51.149 --> 00:02:51.159 align:start position:0%
function once it creates the recipe to
 

00:02:51.159 --> 00:02:53.430 align:start position:0%
function once it creates the recipe to
save<00:02:51.400><c> it</c><00:02:51.519><c> to</c><00:02:51.680><c> a</c><00:02:51.879><c> file</c><00:02:52.319><c> all</c><00:02:52.440><c> right</c><00:02:52.599><c> I</c><00:02:52.800><c> ran</c><00:02:53.080><c> it</c><00:02:53.319><c> and</c>

00:02:53.430 --> 00:02:53.440 align:start position:0%
save it to a file all right I ran it and
 

00:02:53.440 --> 00:02:55.350 align:start position:0%
save it to a file all right I ran it and
as<00:02:53.519><c> you</c><00:02:53.640><c> can</c><00:02:53.800><c> see</c><00:02:54.000><c> it</c><00:02:54.120><c> created</c><00:02:54.480><c> a</c><00:02:54.680><c> teaching</c>

00:02:55.350 --> 00:02:55.360 align:start position:0%
as you can see it created a teaching
 

00:02:55.360 --> 00:02:57.430 align:start position:0%
as you can see it created a teaching
coding<00:02:55.840><c> directory</c><00:02:56.440><c> with</c><00:02:56.599><c> all</c><00:02:56.760><c> of</c><00:02:56.879><c> the</c><00:02:57.040><c> Python</c>

00:02:57.430 --> 00:02:57.440 align:start position:0%
coding directory with all of the Python
 

00:02:57.440 --> 00:02:59.550 align:start position:0%
coding directory with all of the Python
files<00:02:57.920><c> and</c><00:02:58.519><c> the</c><00:02:58.760><c> chart</c><00:02:59.080><c> that</c><00:02:59.159><c> it</c><00:02:59.280><c> created</c>

00:02:59.550 --> 00:02:59.560 align:start position:0%
files and the chart that it created
 

00:02:59.560 --> 00:03:01.470 align:start position:0%
files and the chart that it created
right<00:02:59.959><c> here</c><00:03:00.120><c> for</c><00:03:00.280><c> me</c><00:03:00.680><c> and</c><00:03:00.800><c> then</c><00:03:00.959><c> also</c><00:03:01.159><c> created</c>

00:03:01.470 --> 00:03:01.480 align:start position:0%
right here for me and then also created
 

00:03:01.480 --> 00:03:03.670 align:start position:0%
right here for me and then also created
the<00:03:01.560><c> new</c><00:03:01.720><c> recipe.</c><00:03:02.280><c> text</c><00:03:02.480><c> for</c><00:03:02.680><c> me</c><00:03:03.159><c> based</c><00:03:03.400><c> on</c><00:03:03.519><c> the</c>

00:03:03.670 --> 00:03:03.680 align:start position:0%
the new recipe. text for me based on the
 

00:03:03.680 --> 00:03:05.710 align:start position:0%
the new recipe. text for me based on the
sequences<00:03:04.319><c> of</c><00:03:04.480><c> tasks</c><00:03:04.760><c> that</c><00:03:04.959><c> performed</c><00:03:05.560><c> it</c>

00:03:05.710 --> 00:03:05.720 align:start position:0%
sequences of tasks that performed it
 

00:03:05.720 --> 00:03:08.030 align:start position:0%
sequences of tasks that performed it
proposed<00:03:06.239><c> the</c><00:03:06.400><c> following</c><00:03:07.159><c> which</c><00:03:07.319><c> includes</c>

00:03:08.030 --> 00:03:08.040 align:start position:0%
proposed the following which includes
 

00:03:08.040 --> 00:03:10.350 align:start position:0%
proposed the following which includes
the<00:03:08.200><c> steps</c><00:03:08.879><c> and</c><00:03:09.080><c> the</c><00:03:09.280><c> python</c><00:03:09.720><c> files</c><00:03:10.080><c> that</c><00:03:10.200><c> it</c>

00:03:10.350 --> 00:03:10.360 align:start position:0%
the steps and the python files that it
 

00:03:10.360 --> 00:03:12.630 align:start position:0%
the steps and the python files that it
saved<00:03:10.680><c> over</c><00:03:10.959><c> here</c><00:03:11.519><c> so</c><00:03:11.720><c> if</c><00:03:11.840><c> I</c><00:03:11.920><c> try</c><00:03:12.080><c> to</c><00:03:12.200><c> run</c><00:03:12.400><c> this</c>

00:03:12.630 --> 00:03:12.640 align:start position:0%
saved over here so if I try to run this
 

00:03:12.640 --> 00:03:14.789 align:start position:0%
saved over here so if I try to run this
again<00:03:13.200><c> I</c><00:03:13.280><c> will</c><00:03:13.519><c> get</c><00:03:13.680><c> a</c><00:03:13.879><c> similar</c><00:03:14.280><c> result</c><00:03:14.640><c> but</c>

00:03:14.789 --> 00:03:14.799 align:start position:0%
again I will get a similar result but
 

00:03:14.799 --> 00:03:16.910 align:start position:0%
again I will get a similar result but
only<00:03:15.080><c> with</c><00:03:15.280><c> one</c><00:03:15.720><c> prompt</c><00:03:16.200><c> instead</c><00:03:16.440><c> of</c><00:03:16.599><c> having</c>

00:03:16.910 --> 00:03:16.920 align:start position:0%
only with one prompt instead of having
 

00:03:16.920 --> 00:03:18.949 align:start position:0%
only with one prompt instead of having
multiple<00:03:17.400><c> prompts</c><00:03:17.720><c> to</c><00:03:17.879><c> initiate</c><00:03:18.280><c> tasks</c><00:03:18.640><c> with</c>

00:03:18.949 --> 00:03:18.959 align:start position:0%
multiple prompts to initiate tasks with
 

00:03:18.959 --> 00:03:20.509 align:start position:0%
multiple prompts to initiate tasks with
and<00:03:19.080><c> now</c><00:03:19.239><c> as</c><00:03:19.319><c> you</c><00:03:19.400><c> can</c><00:03:19.519><c> see</c><00:03:19.720><c> we</c><00:03:19.799><c> only</c><00:03:20.000><c> have</c><00:03:20.200><c> one</c>

00:03:20.509 --> 00:03:20.519 align:start position:0%
and now as you can see we only have one
 

00:03:20.519 --> 00:03:22.550 align:start position:0%
and now as you can see we only have one
initiate<00:03:20.959><c> chat</c><00:03:21.319><c> I</c><00:03:21.440><c> just</c><00:03:21.599><c> simply</c><00:03:22.000><c> read</c><00:03:22.360><c> the</c>

00:03:22.550 --> 00:03:22.560 align:start position:0%
initiate chat I just simply read the
 

00:03:22.560 --> 00:03:25.710 align:start position:0%
initiate chat I just simply read the
contents<00:03:23.400><c> from</c><00:03:23.760><c> that</c><00:03:23.959><c> recipe.</c><00:03:24.640><c> text</c><00:03:24.920><c> file</c><00:03:25.480><c> and</c>

00:03:25.710 --> 00:03:25.720 align:start position:0%
contents from that recipe. text file and
 

00:03:25.720 --> 00:03:27.589 align:start position:0%
contents from that recipe. text file and
give<00:03:25.959><c> that</c><00:03:26.200><c> as</c><00:03:26.360><c> the</c><00:03:26.519><c> message</c><00:03:27.120><c> well</c><00:03:27.280><c> after</c><00:03:27.480><c> I</c>

00:03:27.589 --> 00:03:27.599 align:start position:0%
give that as the message well after I
 

00:03:27.599 --> 00:03:29.990 align:start position:0%
give that as the message well after I
ran<00:03:27.840><c> it</c><00:03:28.200><c> it</c><00:03:28.360><c> gave</c><00:03:28.560><c> me</c><00:03:28.720><c> a</c><00:03:28.840><c> new</c><00:03:29.080><c> python</c><00:03:29.439><c> file</c><00:03:29.840><c> for</c>

00:03:29.990 --> 00:03:30.000 align:start position:0%
ran it it gave me a new python file for
 

00:03:30.000 --> 00:03:32.789 align:start position:0%
ran it it gave me a new python file for
the<00:03:30.120><c> GPT</c><00:03:30.720><c> applications</c><00:03:31.360><c> domain</c><00:03:32.239><c> and</c><00:03:32.480><c> also</c>

00:03:32.789 --> 00:03:32.799 align:start position:0%
the GPT applications domain and also
 

00:03:32.799 --> 00:03:34.830 align:start position:0%
the GPT applications domain and also
output<00:03:33.239><c> a</c><00:03:33.439><c> chart</c><00:03:33.720><c> for</c><00:03:33.959><c> me</c><00:03:34.360><c> all</c><00:03:34.480><c> right</c><00:03:34.599><c> we</c><00:03:34.720><c> just</c>

00:03:34.830 --> 00:03:34.840 align:start position:0%
output a chart for me all right we just
 

00:03:34.840 --> 00:03:36.229 align:start position:0%
output a chart for me all right we just
learned<00:03:35.040><c> a</c><00:03:35.159><c> way</c><00:03:35.280><c> to</c><00:03:35.400><c> teach</c><00:03:35.640><c> our</c><00:03:35.799><c> agents</c><00:03:36.120><c> to</c>

00:03:36.229 --> 00:03:36.239 align:start position:0%
learned a way to teach our agents to
 

00:03:36.239 --> 00:03:37.990 align:start position:0%
learned a way to teach our agents to
come<00:03:36.360><c> up</c><00:03:36.519><c> with</c><00:03:36.640><c> better</c><00:03:36.959><c> prompts</c><00:03:37.319><c> so</c><00:03:37.840><c> they</c>

00:03:37.990 --> 00:03:38.000 align:start position:0%
come up with better prompts so they
 

00:03:38.000 --> 00:03:40.110 align:start position:0%
come up with better prompts so they
could<00:03:38.319><c> easily</c><00:03:38.640><c> more</c><00:03:39.319><c> understand</c><00:03:39.640><c> the</c><00:03:39.799><c> tasks</c>

00:03:40.110 --> 00:03:40.120 align:start position:0%
could easily more understand the tasks
 

00:03:40.120 --> 00:03:42.229 align:start position:0%
could easily more understand the tasks
that<00:03:40.239><c> they</c><00:03:40.360><c> are</c><00:03:40.560><c> given</c><00:03:41.040><c> this</c><00:03:41.120><c> isn't</c><00:03:41.400><c> foolproof</c>

00:03:42.229 --> 00:03:42.239 align:start position:0%
that they are given this isn't foolproof
 

00:03:42.239 --> 00:03:44.149 align:start position:0%
that they are given this isn't foolproof
but<00:03:42.360><c> this</c><00:03:42.480><c> could</c><00:03:42.680><c> give</c><00:03:42.799><c> you</c><00:03:43.000><c> a</c><00:03:43.239><c> better</c><00:03:43.640><c> chance</c>

00:03:44.149 --> 00:03:44.159 align:start position:0%
but this could give you a better chance
 

00:03:44.159 --> 00:03:45.910 align:start position:0%
but this could give you a better chance
of<00:03:44.400><c> getting</c><00:03:44.680><c> the</c><00:03:44.840><c> results</c><00:03:45.200><c> that</c><00:03:45.319><c> you</c><00:03:45.439><c> want</c>

00:03:45.910 --> 00:03:45.920 align:start position:0%
of getting the results that you want
 

00:03:45.920 --> 00:03:47.550 align:start position:0%
of getting the results that you want
thank<00:03:46.040><c> you</c><00:03:46.120><c> for</c><00:03:46.319><c> watching</c><00:03:46.799><c> every</c><00:03:47.000><c> Sunday</c><00:03:47.400><c> at</c>

00:03:47.550 --> 00:03:47.560 align:start position:0%
thank you for watching every Sunday at
 

00:03:47.560 --> 00:03:49.309 align:start position:0%
thank you for watching every Sunday at
noon<00:03:47.879><c> I</c><00:03:48.000><c> give</c><00:03:48.120><c> out</c><00:03:48.319><c> a</c><00:03:48.439><c> free</c><00:03:48.640><c> newsletter</c><00:03:49.159><c> I'll</c>

00:03:49.309 --> 00:03:49.319 align:start position:0%
noon I give out a free newsletter I'll
 

00:03:49.319 --> 00:03:50.869 align:start position:0%
noon I give out a free newsletter I'll
have<00:03:49.480><c> down</c><00:03:49.599><c> in</c><00:03:49.680><c> the</c><00:03:49.799><c> description</c><00:03:50.439><c> as</c><00:03:50.599><c> well</c><00:03:50.720><c> as</c>

00:03:50.869 --> 00:03:50.879 align:start position:0%
have down in the description as well as
 

00:03:50.879 --> 00:03:53.110 align:start position:0%
have down in the description as well as
my<00:03:51.040><c> GitHub</c><00:03:51.599><c> if</c><00:03:51.680><c> you</c><00:03:51.799><c> have</c><00:03:51.959><c> any</c><00:03:52.200><c> questions</c><00:03:52.799><c> or</c>

00:03:53.110 --> 00:03:53.120 align:start position:0%
my GitHub if you have any questions or
 

00:03:53.120 --> 00:03:54.429 align:start position:0%
my GitHub if you have any questions or
comments<00:03:53.560><c> please</c><00:03:53.840><c> leave</c><00:03:54.040><c> them</c><00:03:54.239><c> in</c><00:03:54.319><c> the</c>

00:03:54.429 --> 00:03:54.439 align:start position:0%
comments please leave them in the
 

00:03:54.439 --> 00:03:55.830 align:start position:0%
comments please leave them in the
comment<00:03:54.720><c> section</c><00:03:55.000><c> and</c><00:03:55.120><c> I</c><00:03:55.200><c> will</c><00:03:55.360><c> certainly</c><00:03:55.680><c> get</c>

00:03:55.830 --> 00:03:55.840 align:start position:0%
comment section and I will certainly get
 

00:03:55.840 --> 00:03:57.069 align:start position:0%
comment section and I will certainly get
back<00:03:55.959><c> to</c><00:03:56.040><c> you</c><00:03:56.239><c> here</c><00:03:56.360><c> are</c><00:03:56.480><c> some</c><00:03:56.599><c> more</c><00:03:56.720><c> videos</c><00:03:56.959><c> on</c>

00:03:57.069 --> 00:03:57.079 align:start position:0%
back to you here are some more videos on
 

00:03:57.079 --> 00:04:00.920 align:start position:0%
back to you here are some more videos on
autogen<00:03:57.799><c> I'll</c><00:03:57.959><c> see</c><00:03:58.120><c> you</c><00:03:58.280><c> next</c><00:03:58.480><c> time</c><00:03:58.760><c> by</c>

