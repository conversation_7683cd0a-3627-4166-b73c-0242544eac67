WEBVTT
Kind: captions
Language: en

00:00:01.079 --> 00:00:03.869 align:start position:0%
 
hey<00:00:01.280><c> everyone</c><00:00:02.120><c> Ravi</c><00:00:02.480><c> here</c><00:00:02.639><c> from</c><00:00:02.800><c> llama</c><00:00:03.120><c> index</c>

00:00:03.869 --> 00:00:03.879 align:start position:0%
hey everyone Ravi here from llama index
 

00:00:03.879 --> 00:00:06.430 align:start position:0%
hey everyone Ravi here from llama index
so<00:00:04.160><c> in</c><00:00:04.319><c> this</c><00:00:04.520><c> video</c><00:00:04.880><c> I'll</c><00:00:05.120><c> discuss</c><00:00:05.520><c> about</c><00:00:06.240><c> a</c>

00:00:06.430 --> 00:00:06.440 align:start position:0%
so in this video I'll discuss about a
 

00:00:06.440 --> 00:00:08.709 align:start position:0%
so in this video I'll discuss about a
llama<00:00:06.759><c> pack</c><00:00:07.120><c> on</c><00:00:07.560><c> recent</c><00:00:08.040><c> paper</c><00:00:08.280><c> from</c><00:00:08.440><c> UC</c>

00:00:08.709 --> 00:00:08.719 align:start position:0%
llama pack on recent paper from UC
 

00:00:08.719 --> 00:00:11.270 align:start position:0%
llama pack on recent paper from UC
berley<00:00:09.040><c> on</c><00:00:09.200><c> raft</c><00:00:09.679><c> adapting</c><00:00:10.639><c> language</c><00:00:11.000><c> model</c>

00:00:11.270 --> 00:00:11.280 align:start position:0%
berley on raft adapting language model
 

00:00:11.280 --> 00:00:12.950 align:start position:0%
berley on raft adapting language model
to<00:00:11.519><c> domain</c><00:00:11.799><c> specific</c>

00:00:12.950 --> 00:00:12.960 align:start position:0%
to domain specific
 

00:00:12.960 --> 00:00:16.109 align:start position:0%
to domain specific
rag<00:00:13.960><c> So</c><00:00:14.160><c> currently</c><00:00:15.000><c> there</c><00:00:15.120><c> are</c><00:00:15.440><c> primarily</c><00:00:15.839><c> two</c>

00:00:16.109 --> 00:00:16.119 align:start position:0%
rag So currently there are primarily two
 

00:00:16.119 --> 00:00:17.950 align:start position:0%
rag So currently there are primarily two
approaches<00:00:16.600><c> in</c><00:00:16.800><c> injecting</c><00:00:17.400><c> new</c><00:00:17.600><c> knowledge</c>

00:00:17.950 --> 00:00:17.960 align:start position:0%
approaches in injecting new knowledge
 

00:00:17.960 --> 00:00:21.269 align:start position:0%
approaches in injecting new knowledge
into<00:00:18.160><c> llms</c><00:00:19.039><c> uh</c><00:00:19.520><c> which</c><00:00:19.680><c> are</c><00:00:20.199><c> one</c><00:00:20.400><c> is</c><00:00:20.560><c> Rag</c><00:00:21.119><c> and</c>

00:00:21.269 --> 00:00:21.279 align:start position:0%
into llms uh which are one is Rag and
 

00:00:21.279 --> 00:00:22.269 align:start position:0%
into llms uh which are one is Rag and
the<00:00:21.359><c> other</c><00:00:21.560><c> is</c>

00:00:22.269 --> 00:00:22.279 align:start position:0%
the other is
 

00:00:22.279 --> 00:00:25.910 align:start position:0%
the other is
fine-tuning<00:00:23.279><c> but</c><00:00:23.800><c> uh</c><00:00:24.800><c> what</c><00:00:24.920><c> is</c><00:00:25.000><c> the</c><00:00:25.160><c> current</c>

00:00:25.910 --> 00:00:25.920 align:start position:0%
fine-tuning but uh what is the current
 

00:00:25.920 --> 00:00:28.269 align:start position:0%
fine-tuning but uh what is the current
um<00:00:26.240><c> optimal</c><00:00:26.720><c> methodology</c><00:00:27.519><c> for</c><00:00:27.720><c> the</c><00:00:27.840><c> model</c><00:00:28.080><c> to</c>

00:00:28.269 --> 00:00:28.279 align:start position:0%
um optimal methodology for the model to
 

00:00:28.279 --> 00:00:30.589 align:start position:0%
um optimal methodology for the model to
gain<00:00:28.560><c> such</c><00:00:28.800><c> new</c><00:00:29.080><c> knowledge</c><00:00:29.800><c> uh</c><00:00:30.039><c> Still</c><00:00:30.359><c> Remains</c>

00:00:30.589 --> 00:00:30.599 align:start position:0%
gain such new knowledge uh Still Remains
 

00:00:30.599 --> 00:00:31.830 align:start position:0%
gain such new knowledge uh Still Remains
an<00:00:30.800><c> open</c>

00:00:31.830 --> 00:00:31.840 align:start position:0%
an open
 

00:00:31.840 --> 00:00:34.790 align:start position:0%
an open
question<00:00:32.840><c> so</c><00:00:33.120><c> this</c><00:00:33.320><c> paper</c><00:00:33.680><c> raft</c><00:00:34.120><c> proposes</c><00:00:34.559><c> an</c>

00:00:34.790 --> 00:00:34.800 align:start position:0%
question so this paper raft proposes an
 

00:00:34.800 --> 00:00:37.590 align:start position:0%
question so this paper raft proposes an
approach<00:00:35.640><c> wherein</c><00:00:36.360><c> given</c><00:00:36.640><c> a</c><00:00:36.800><c> question</c><00:00:37.239><c> and</c><00:00:37.440><c> a</c>

00:00:37.590 --> 00:00:37.600 align:start position:0%
approach wherein given a question and a
 

00:00:37.600 --> 00:00:40.790 align:start position:0%
approach wherein given a question and a
set<00:00:37.920><c> of</c><00:00:38.920><c> retrieve</c><00:00:39.440><c> documents</c><00:00:40.200><c> their</c><00:00:40.399><c> try</c><00:00:40.680><c> name</c>

00:00:40.790 --> 00:00:40.800 align:start position:0%
set of retrieve documents their try name
 

00:00:40.800 --> 00:00:42.270 align:start position:0%
set of retrieve documents their try name
model

00:00:42.270 --> 00:00:42.280 align:start position:0%
model
 

00:00:42.280 --> 00:00:46.830 align:start position:0%
model
to<00:00:43.280><c> only</c><00:00:43.559><c> use</c><00:00:43.840><c> relevant</c><00:00:44.600><c> documents</c><00:00:45.600><c> um</c><00:00:46.320><c> for</c>

00:00:46.830 --> 00:00:46.840 align:start position:0%
to only use relevant documents um for
 

00:00:46.840 --> 00:00:50.310 align:start position:0%
to only use relevant documents um for
those<00:00:47.840><c> it</c><00:00:47.960><c> will</c><00:00:48.160><c> help</c><00:00:48.719><c> to</c><00:00:48.960><c> answer</c><00:00:49.239><c> a</c><00:00:49.399><c> question</c>

00:00:50.310 --> 00:00:50.320 align:start position:0%
those it will help to answer a question
 

00:00:50.320 --> 00:00:53.150 align:start position:0%
those it will help to answer a question
and<00:00:50.840><c> uh</c><00:00:51.640><c> uh</c><00:00:52.000><c> chunk</c><00:00:52.320><c> out</c><00:00:52.480><c> the</c><00:00:52.600><c> IR</c><00:00:52.719><c> relevant</c>

00:00:53.150 --> 00:00:53.160 align:start position:0%
and uh uh chunk out the IR relevant
 

00:00:53.160 --> 00:00:56.549 align:start position:0%
and uh uh chunk out the IR relevant
documents<00:00:53.719><c> when</c><00:00:54.120><c> answering</c><00:00:54.480><c> the</c><00:00:54.640><c> question</c><00:00:55.559><c> so</c>

00:00:56.549 --> 00:00:56.559 align:start position:0%
documents when answering the question so
 

00:00:56.559 --> 00:00:58.470 align:start position:0%
documents when answering the question so
they<00:00:56.760><c> have</c><00:00:57.079><c> Oracle</c><00:00:57.640><c> document</c><00:00:58.199><c> which</c><00:00:58.320><c> is</c>

00:00:58.470 --> 00:00:58.480 align:start position:0%
they have Oracle document which is
 

00:00:58.480 --> 00:01:00.869 align:start position:0%
they have Oracle document which is
relevant<00:00:58.960><c> and</c><00:00:59.199><c> the</c><00:00:59.359><c> destructor</c><00:01:00.039><c> documents</c><00:01:00.760><c> uh</c>

00:01:00.869 --> 00:01:00.879 align:start position:0%
relevant and the destructor documents uh
 

00:01:00.879 --> 00:01:03.869 align:start position:0%
relevant and the destructor documents uh
which<00:01:01.000><c> are</c><00:01:01.320><c> irrelevant</c><00:01:01.920><c> documents</c><00:01:02.840><c> uh</c><00:01:03.600><c> uh</c><00:01:03.719><c> to</c>

00:01:03.869 --> 00:01:03.879 align:start position:0%
which are irrelevant documents uh uh to
 

00:01:03.879 --> 00:01:06.030 align:start position:0%
which are irrelevant documents uh uh to
train<00:01:04.119><c> a</c><00:01:04.280><c> model</c><00:01:04.479><c> for</c><00:01:04.720><c> given</c><00:01:05.040><c> question</c><00:01:05.760><c> and</c>

00:01:06.030 --> 00:01:06.040 align:start position:0%
train a model for given question and
 

00:01:06.040 --> 00:01:07.149 align:start position:0%
train a model for given question and
also<00:01:06.240><c> an</c>

00:01:07.149 --> 00:01:07.159 align:start position:0%
also an
 

00:01:07.159 --> 00:01:10.310 align:start position:0%
also an
answer<00:01:08.159><c> so</c><00:01:08.880><c> uh</c><00:01:09.240><c> the</c><00:01:09.400><c> question</c><00:01:09.920><c> they're</c><00:01:10.119><c> trying</c>

00:01:10.310 --> 00:01:10.320 align:start position:0%
answer so uh the question they're trying
 

00:01:10.320 --> 00:01:13.749 align:start position:0%
answer so uh the question they're trying
to<00:01:10.520><c> answer</c><00:01:10.799><c> is</c><00:01:11.119><c> how</c><00:01:11.280><c> to</c><00:01:11.479><c> adapt</c><00:01:12.280><c> uh</c><00:01:12.920><c> preer</c><00:01:13.400><c> LMS</c>

00:01:13.749 --> 00:01:13.759 align:start position:0%
to answer is how to adapt uh preer LMS
 

00:01:13.759 --> 00:01:15.950 align:start position:0%
to answer is how to adapt uh preer LMS
foral<00:01:14.320><c> augment</c><00:01:14.640><c> and</c><00:01:14.759><c> generation</c><00:01:15.280><c> in</c><00:01:15.439><c> specific</c>

00:01:15.950 --> 00:01:15.960 align:start position:0%
foral augment and generation in specific
 

00:01:15.960 --> 00:01:21.069 align:start position:0%
foral augment and generation in specific
domains<00:01:17.000><c> right</c><00:01:18.000><c> so</c><00:01:18.840><c> um</c><00:01:19.560><c> they</c><00:01:19.799><c> speak</c><00:01:20.200><c> about</c><00:01:20.840><c> an</c>

00:01:21.069 --> 00:01:21.079 align:start position:0%
domains right so um they speak about an
 

00:01:21.079 --> 00:01:23.230 align:start position:0%
domains right so um they speak about an
interesting<00:01:21.479><c> analogy</c><00:01:22.000><c> here</c><00:01:22.400><c> which</c><00:01:22.560><c> is</c><00:01:23.079><c> a</c>

00:01:23.230 --> 00:01:23.240 align:start position:0%
interesting analogy here which is a
 

00:01:23.240 --> 00:01:25.830 align:start position:0%
interesting analogy here which is a
close<00:01:23.560><c> book</c><00:01:23.880><c> exam</c><00:01:24.880><c> uh</c><00:01:25.000><c> you</c><00:01:25.119><c> don't</c><00:01:25.400><c> have</c><00:01:25.640><c> an</c>

00:01:25.830 --> 00:01:25.840 align:start position:0%
close book exam uh you don't have an
 

00:01:25.840 --> 00:01:27.630 align:start position:0%
close book exam uh you don't have an
access<00:01:26.119><c> to</c><00:01:26.360><c> any</c><00:01:26.600><c> external</c><00:01:27.079><c> documents</c><00:01:27.439><c> or</c>

00:01:27.630 --> 00:01:27.640 align:start position:0%
access to any external documents or
 

00:01:27.640 --> 00:01:30.870 align:start position:0%
access to any external documents or
references<00:01:28.479><c> uh</c><00:01:28.600><c> to</c><00:01:28.799><c> the</c><00:01:29.000><c> questions</c><00:01:30.079><c> but</c><00:01:30.360><c> llms</c>

00:01:30.870 --> 00:01:30.880 align:start position:0%
references uh to the questions but llms
 

00:01:30.880 --> 00:01:33.310 align:start position:0%
references uh to the questions but llms
are<00:01:31.400><c> trained</c><00:01:31.759><c> on</c><00:01:32.000><c> what's</c><00:01:32.600><c> vast</c><00:01:32.880><c> amount</c><00:01:33.119><c> of</c>

00:01:33.310 --> 00:01:33.320 align:start position:0%
are trained on what's vast amount of
 

00:01:33.320 --> 00:01:35.429 align:start position:0%
are trained on what's vast amount of
data<00:01:33.560><c> so</c><00:01:33.759><c> they'll</c><00:01:33.960><c> be</c><00:01:34.119><c> able</c><00:01:34.320><c> to</c><00:01:34.840><c> answer</c><00:01:35.240><c> the</c>

00:01:35.429 --> 00:01:35.439 align:start position:0%
data so they'll be able to answer the
 

00:01:35.439 --> 00:01:38.910 align:start position:0%
data so they'll be able to answer the
questions<00:01:35.920><c> without</c><00:01:36.240><c> any</c><00:01:37.079><c> um</c><00:01:37.600><c> access</c><00:01:37.920><c> to</c><00:01:38.119><c> any</c>

00:01:38.910 --> 00:01:38.920 align:start position:0%
questions without any um access to any
 

00:01:38.920 --> 00:01:40.069 align:start position:0%
questions without any um access to any
additional

00:01:40.069 --> 00:01:40.079 align:start position:0%
additional
 

00:01:40.079 --> 00:01:42.429 align:start position:0%
additional
documents<00:01:41.079><c> but</c><00:01:41.240><c> then</c><00:01:41.399><c> there</c><00:01:41.520><c> is</c><00:01:41.640><c> an</c><00:01:41.840><c> open</c><00:01:42.159><c> book</c>

00:01:42.429 --> 00:01:42.439 align:start position:0%
documents but then there is an open book
 

00:01:42.439 --> 00:01:44.950 align:start position:0%
documents but then there is an open book
exam<00:01:42.880><c> wherein</c><00:01:43.280><c> LM</c><00:01:43.759><c> has</c><00:01:43.920><c> an</c><00:01:44.479><c> access</c><00:01:44.719><c> to</c><00:01:44.880><c> the</c>

00:01:44.950 --> 00:01:44.960 align:start position:0%
exam wherein LM has an access to the
 

00:01:44.960 --> 00:01:47.510 align:start position:0%
exam wherein LM has an access to the
external<00:01:45.320><c> source</c><00:01:45.560><c> of</c><00:01:45.799><c> informations</c><00:01:46.799><c> and</c><00:01:47.000><c> then</c>

00:01:47.510 --> 00:01:47.520 align:start position:0%
external source of informations and then
 

00:01:47.520 --> 00:01:49.670 align:start position:0%
external source of informations and then
uh<00:01:47.640><c> it</c><00:01:47.759><c> uses</c><00:01:48.119><c> these</c><00:01:48.360><c> knowledge</c><00:01:49.119><c> in</c><00:01:49.200><c> a</c><00:01:49.360><c> rag</c>

00:01:49.670 --> 00:01:49.680 align:start position:0%
uh it uses these knowledge in a rag
 

00:01:49.680 --> 00:01:52.749 align:start position:0%
uh it uses these knowledge in a rag
fashion<00:01:50.520><c> to</c><00:01:51.040><c> generate</c><00:01:51.360><c> an</c><00:01:51.560><c> answer</c><00:01:51.840><c> for</c><00:01:52.000><c> the</c><00:01:52.600><c> uh</c>

00:01:52.749 --> 00:01:52.759 align:start position:0%
fashion to generate an answer for the uh
 

00:01:52.759 --> 00:01:53.590 align:start position:0%
fashion to generate an answer for the uh
given

00:01:53.590 --> 00:01:53.600 align:start position:0%
given
 

00:01:53.600 --> 00:01:55.670 align:start position:0%
given
query<00:01:54.600><c> but</c><00:01:54.759><c> this</c><00:01:54.960><c> approach</c><00:01:55.240><c> is</c><00:01:55.399><c> heavily</c>

00:01:55.670 --> 00:01:55.680 align:start position:0%
query but this approach is heavily
 

00:01:55.680 --> 00:01:58.109 align:start position:0%
query but this approach is heavily
dependent<00:01:56.079><c> on</c><00:01:56.240><c> the</c><00:01:56.640><c> quality</c><00:01:56.960><c> of</c><00:01:57.119><c> Retriever</c>

00:01:58.109 --> 00:01:58.119 align:start position:0%
dependent on the quality of Retriever
 

00:01:58.119 --> 00:02:00.510 align:start position:0%
dependent on the quality of Retriever
and<00:01:58.320><c> also</c><00:01:58.799><c> sometimes</c><00:01:59.280><c> uh</c><00:01:59.600><c> uh</c><00:01:59.960><c> the</c><00:02:00.119><c> retri</c>

00:02:00.510 --> 00:02:00.520 align:start position:0%
and also sometimes uh uh the retri
 

00:02:00.520 --> 00:02:02.389 align:start position:0%
and also sometimes uh uh the retri
documents<00:02:00.920><c> might</c><00:02:01.119><c> contain</c><00:02:01.880><c> elevant</c>

00:02:02.389 --> 00:02:02.399 align:start position:0%
documents might contain elevant
 

00:02:02.399 --> 00:02:05.709 align:start position:0%
documents might contain elevant
information<00:02:02.920><c> as</c><00:02:03.520><c> well</c><00:02:04.520><c> so</c><00:02:05.360><c> then</c><00:02:05.520><c> we</c><00:02:05.600><c> are</c>

00:02:05.709 --> 00:02:05.719 align:start position:0%
information as well so then we are
 

00:02:05.719 --> 00:02:07.389 align:start position:0%
information as well so then we are
entering<00:02:06.039><c> into</c><00:02:06.280><c> domain</c><00:02:06.560><c> specific</c><00:02:06.920><c> open</c><00:02:07.159><c> book</c>

00:02:07.389 --> 00:02:07.399 align:start position:0%
entering into domain specific open book
 

00:02:07.399 --> 00:02:09.469 align:start position:0%
entering into domain specific open book
exam<00:02:07.960><c> in</c><00:02:08.080><c> this</c><00:02:08.280><c> exam</c><00:02:08.560><c> the</c><00:02:08.640><c> llm</c><00:02:09.000><c> will</c><00:02:09.119><c> be</c><00:02:09.239><c> fine</c>

00:02:09.469 --> 00:02:09.479 align:start position:0%
exam in this exam the llm will be fine
 

00:02:09.479 --> 00:02:12.110 align:start position:0%
exam in this exam the llm will be fine
tuned<00:02:09.800><c> for</c><00:02:10.000><c> the</c><00:02:10.160><c> specific</c><00:02:10.560><c> domain</c><00:02:11.239><c> and</c><00:02:11.520><c> then</c>

00:02:12.110 --> 00:02:12.120 align:start position:0%
tuned for the specific domain and then
 

00:02:12.120 --> 00:02:16.550 align:start position:0%
tuned for the specific domain and then
uh<00:02:12.360><c> we</c><00:02:12.560><c> keep</c><00:02:13.080><c> asking</c><00:02:14.080><c> queries</c><00:02:14.440><c> to</c><00:02:14.720><c> the</c><00:02:15.720><c> uh</c><00:02:16.200><c> find</c>

00:02:16.550 --> 00:02:16.560 align:start position:0%
uh we keep asking queries to the uh find
 

00:02:16.560 --> 00:02:20.070 align:start position:0%
uh we keep asking queries to the uh find
tun<00:02:16.800><c> llm</c><00:02:17.519><c> so</c><00:02:17.760><c> this</c><00:02:17.920><c> paper</c><00:02:18.239><c> talks</c><00:02:18.519><c> about</c><00:02:19.160><c> mainly</c>

00:02:20.070 --> 00:02:20.080 align:start position:0%
tun llm so this paper talks about mainly
 

00:02:20.080 --> 00:02:23.390 align:start position:0%
tun llm so this paper talks about mainly
uh<00:02:20.400><c> domain</c><00:02:20.760><c> specific</c><00:02:21.200><c> open</c><00:02:21.840><c> uh</c><00:02:22.239><c> book</c><00:02:22.480><c> setting</c>

00:02:23.390 --> 00:02:23.400 align:start position:0%
uh domain specific open uh book setting
 

00:02:23.400 --> 00:02:27.110 align:start position:0%
uh domain specific open uh book setting
how<00:02:23.840><c> uh</c><00:02:24.560><c> they</c><00:02:24.680><c> can</c><00:02:24.879><c> use</c><00:02:25.200><c> this</c><00:02:25.400><c> approach</c><00:02:25.800><c> in</c><00:02:25.959><c> the</c>

00:02:27.110 --> 00:02:27.120 align:start position:0%
how uh they can use this approach in the
 

00:02:27.120 --> 00:02:30.710 align:start position:0%
how uh they can use this approach in the
setting<00:02:28.120><c> so</c><00:02:28.400><c> let's</c><00:02:28.920><c> uh</c><00:02:29.080><c> see</c><00:02:29.560><c> uh</c><00:02:30.160><c> how</c><00:02:30.400><c> they</c>

00:02:30.710 --> 00:02:30.720 align:start position:0%
setting so let's uh see uh how they
 

00:02:30.720 --> 00:02:32.710 align:start position:0%
setting so let's uh see uh how they
create<00:02:31.040><c> the</c><00:02:31.200><c> data</c><00:02:31.480><c> set</c><00:02:31.760><c> for</c><00:02:32.160><c> for</c><00:02:32.319><c> this</c><00:02:32.440><c> F</c>

00:02:32.710 --> 00:02:32.720 align:start position:0%
create the data set for for this F
 

00:02:32.720 --> 00:02:36.150 align:start position:0%
create the data set for for this F
tuning<00:02:33.080><c> process</c><00:02:34.000><c> first</c><00:02:35.000><c> what</c><00:02:35.160><c> they</c><00:02:35.319><c> do</c><00:02:35.680><c> is</c><00:02:36.040><c> uh</c>

00:02:36.150 --> 00:02:36.160 align:start position:0%
tuning process first what they do is uh
 

00:02:36.160 --> 00:02:38.990 align:start position:0%
tuning process first what they do is uh
in<00:02:36.280><c> the</c><00:02:36.440><c> usual</c><00:02:37.239><c> sft</c><00:02:37.760><c> process</c><00:02:38.319><c> what</c><00:02:38.440><c> they</c><00:02:38.599><c> do</c>

00:02:38.990 --> 00:02:39.000 align:start position:0%
in the usual sft process what they do
 

00:02:39.000 --> 00:02:41.830 align:start position:0%
in the usual sft process what they do
you<00:02:39.239><c> have</c><00:02:39.519><c> questions</c><00:02:39.879><c> and</c><00:02:40.120><c> answers</c><00:02:40.599><c> right</c><00:02:41.280><c> and</c>

00:02:41.830 --> 00:02:41.840 align:start position:0%
you have questions and answers right and
 

00:02:41.840 --> 00:02:44.869 align:start position:0%
you have questions and answers right and
then<00:02:42.840><c> and</c><00:02:43.000><c> then</c><00:02:43.319><c> in</c><00:02:43.480><c> the</c><00:02:43.720><c> inference</c><00:02:44.239><c> stage</c><00:02:44.680><c> you</c>

00:02:44.869 --> 00:02:44.879 align:start position:0%
then and then in the inference stage you
 

00:02:44.879 --> 00:02:46.910 align:start position:0%
then and then in the inference stage you
have<00:02:45.120><c> questions</c><00:02:45.560><c> you</c><00:02:45.720><c> have</c><00:02:46.000><c> query</c><00:02:46.680><c> for</c><00:02:46.840><c> the</c>

00:02:46.910 --> 00:02:46.920 align:start position:0%
have questions you have query for the
 

00:02:46.920 --> 00:02:50.350 align:start position:0%
have questions you have query for the
train<00:02:47.239><c> model</c><00:02:47.599><c> and</c><00:02:47.800><c> then</c><00:02:48.680><c> answer</c><00:02:48.920><c> is</c><00:02:49.360><c> generated</c>

00:02:50.350 --> 00:02:50.360 align:start position:0%
train model and then answer is generated
 

00:02:50.360 --> 00:02:52.990 align:start position:0%
train model and then answer is generated
but<00:02:50.560><c> then</c><00:02:50.760><c> during</c><00:02:51.040><c> the</c><00:02:51.200><c> rag</c><00:02:52.200><c> um</c><00:02:52.519><c> what</c><00:02:52.640><c> you</c><00:02:52.800><c> do</c>

00:02:52.990 --> 00:02:53.000 align:start position:0%
but then during the rag um what you do
 

00:02:53.000 --> 00:02:54.869 align:start position:0%
but then during the rag um what you do
is<00:02:53.200><c> you</c><00:02:53.360><c> have</c><00:02:53.519><c> query</c><00:02:54.000><c> and</c><00:02:54.159><c> then</c><00:02:54.319><c> the</c><00:02:54.440><c> retri</c>

00:02:54.869 --> 00:02:54.879 align:start position:0%
is you have query and then the retri
 

00:02:54.879 --> 00:02:56.990 align:start position:0%
is you have query and then the retri
documents<00:02:55.360><c> as</c><00:02:55.519><c> well</c><00:02:55.959><c> and</c><00:02:56.360><c> give</c><00:02:56.599><c> both</c><00:02:56.760><c> that</c><00:02:56.879><c> to</c>

00:02:56.990 --> 00:02:57.000 align:start position:0%
documents as well and give both that to
 

00:02:57.000 --> 00:02:59.630 align:start position:0%
documents as well and give both that to
the<00:02:57.080><c> LM</c><00:02:57.519><c> and</c><00:02:57.680><c> gener</c><00:02:58.519><c> generate</c><00:02:58.879><c> an</c><00:02:59.040><c> answer</c><00:02:59.519><c> in</c>

00:02:59.630 --> 00:02:59.640 align:start position:0%
the LM and gener generate an answer in
 

00:02:59.640 --> 00:03:01.869 align:start position:0%
the LM and gener generate an answer in
the<00:02:59.920><c> trial</c><00:03:00.200><c> augmented</c><00:03:00.560><c> generation</c>

00:03:01.869 --> 00:03:01.879 align:start position:0%
the trial augmented generation
 

00:03:01.879 --> 00:03:07.869 align:start position:0%
the trial augmented generation
p<00:03:02.879><c> so</c><00:03:03.480><c> um</c><00:03:04.200><c> in</c><00:03:04.440><c> rra</c><00:03:04.920><c> what</c><00:03:05.080><c> they</c><00:03:05.239><c> do</c><00:03:05.480><c> is</c><00:03:06.640><c> um</c><00:03:07.640><c> they</c>

00:03:07.869 --> 00:03:07.879 align:start position:0%
p so um in rra what they do is um they
 

00:03:07.879 --> 00:03:10.789 align:start position:0%
p so um in rra what they do is um they
take<00:03:08.080><c> the</c><00:03:08.200><c> whole</c><00:03:08.480><c> data</c><00:03:09.040><c> but</c><00:03:09.519><c> uh</c><00:03:10.080><c> in</c><00:03:10.280><c> preparing</c>

00:03:10.789 --> 00:03:10.799 align:start position:0%
take the whole data but uh in preparing
 

00:03:10.799 --> 00:03:13.149 align:start position:0%
take the whole data but uh in preparing
let's<00:03:10.959><c> say</c><00:03:11.159><c> there</c><00:03:11.280><c> are</c><00:03:11.560><c> 100</c><00:03:11.920><c> samples</c><00:03:12.840><c> in</c><00:03:12.959><c> the</c>

00:03:13.149 --> 00:03:13.159 align:start position:0%
let's say there are 100 samples in the
 

00:03:13.159 --> 00:03:16.509 align:start position:0%
let's say there are 100 samples in the
data<00:03:13.519><c> set</c><00:03:14.519><c> uh</c><00:03:14.680><c> let's</c><00:03:14.920><c> say</c><00:03:15.360><c> for</c><00:03:15.599><c> 90</c><00:03:16.000><c> samples</c>

00:03:16.509 --> 00:03:16.519 align:start position:0%
data set uh let's say for 90 samples
 

00:03:16.519 --> 00:03:19.869 align:start position:0%
data set uh let's say for 90 samples
what<00:03:16.680><c> they</c><00:03:16.840><c> do</c><00:03:17.040><c> is</c><00:03:17.599><c> uh</c><00:03:18.360><c> uh</c><00:03:18.680><c> take</c><00:03:18.879><c> a</c><00:03:19.040><c> query</c><00:03:19.680><c> and</c>

00:03:19.869 --> 00:03:19.879 align:start position:0%
what they do is uh uh take a query and
 

00:03:19.879 --> 00:03:22.270 align:start position:0%
what they do is uh uh take a query and
then<00:03:20.120><c> the</c><00:03:20.560><c> correct</c><00:03:21.000><c> context</c><00:03:21.400><c> of</c><00:03:21.640><c> the</c><00:03:21.959><c> query</c>

00:03:22.270 --> 00:03:22.280 align:start position:0%
then the correct context of the query
 

00:03:22.280 --> 00:03:23.910 align:start position:0%
then the correct context of the query
from<00:03:22.480><c> which</c><00:03:22.640><c> you</c><00:03:22.760><c> can</c><00:03:22.879><c> generate</c><00:03:23.200><c> an</c><00:03:23.360><c> answer</c>

00:03:23.910 --> 00:03:23.920 align:start position:0%
from which you can generate an answer
 

00:03:23.920 --> 00:03:25.869 align:start position:0%
from which you can generate an answer
and<00:03:24.120><c> then</c><00:03:24.640><c> irrelevant</c><00:03:25.200><c> context</c><00:03:25.519><c> or</c><00:03:25.680><c> the</c>

00:03:25.869 --> 00:03:25.879 align:start position:0%
and then irrelevant context or the
 

00:03:25.879 --> 00:03:27.990 align:start position:0%
and then irrelevant context or the
documents<00:03:26.280><c> for</c><00:03:26.480><c> the</c><00:03:26.599><c> given</c><00:03:26.920><c> query</c><00:03:27.640><c> and</c><00:03:27.840><c> you</c>

00:03:27.990 --> 00:03:28.000 align:start position:0%
documents for the given query and you
 

00:03:28.000 --> 00:03:30.350 align:start position:0%
documents for the given query and you
have<00:03:28.280><c> an</c><00:03:28.480><c> answer</c><00:03:28.840><c> for</c><00:03:29.000><c> it</c>

00:03:30.350 --> 00:03:30.360 align:start position:0%
have an answer for it
 

00:03:30.360 --> 00:03:32.630 align:start position:0%
have an answer for it
and<00:03:30.480><c> for</c><00:03:30.680><c> the</c><00:03:30.799><c> rest</c><00:03:31.000><c> of</c><00:03:31.200><c> the</c><00:03:31.400><c> 10</c><00:03:31.680><c> samples</c><00:03:32.519><c> they</c>

00:03:32.630 --> 00:03:32.640 align:start position:0%
and for the rest of the 10 samples they
 

00:03:32.640 --> 00:03:33.789 align:start position:0%
and for the rest of the 10 samples they
remove

00:03:33.789 --> 00:03:33.799 align:start position:0%
remove
 

00:03:33.799 --> 00:03:37.229 align:start position:0%
remove
this<00:03:34.799><c> correct</c><00:03:35.280><c> document</c><00:03:36.080><c> but</c><00:03:36.840><c> have</c><00:03:37.080><c> the</c>

00:03:37.229 --> 00:03:37.239 align:start position:0%
this correct document but have the
 

00:03:37.239 --> 00:03:40.470 align:start position:0%
this correct document but have the
already<00:03:38.040><c> irrelevant</c><00:03:38.640><c> documents</c><00:03:39.280><c> for</c><00:03:40.280><c> the</c>

00:03:40.470 --> 00:03:40.480 align:start position:0%
already irrelevant documents for the
 

00:03:40.480 --> 00:03:42.350 align:start position:0%
already irrelevant documents for the
answer<00:03:40.840><c> to</c><00:03:41.040><c> this</c>

00:03:42.350 --> 00:03:42.360 align:start position:0%
answer to this
 

00:03:42.360 --> 00:03:46.270 align:start position:0%
answer to this
query<00:03:43.360><c> this</c><00:03:43.560><c> way</c><00:03:43.959><c> you'll</c><00:03:44.239><c> be</c><00:03:44.400><c> able</c><00:03:44.680><c> to</c><00:03:45.319><c> uh</c><00:03:46.040><c> help</c>

00:03:46.270 --> 00:03:46.280 align:start position:0%
query this way you'll be able to uh help
 

00:03:46.280 --> 00:03:47.949 align:start position:0%
query this way you'll be able to uh help
the<00:03:46.400><c> model</c><00:03:46.720><c> to</c><00:03:46.879><c> memorize</c><00:03:47.280><c> the</c><00:03:47.439><c> answers</c>

00:03:47.949 --> 00:03:47.959 align:start position:0%
the model to memorize the answers
 

00:03:47.959 --> 00:03:49.470 align:start position:0%
the model to memorize the answers
instead<00:03:48.280><c> of</c><00:03:48.599><c> deriving</c><00:03:49.040><c> them</c><00:03:49.200><c> from</c><00:03:49.360><c> the</c>

00:03:49.470 --> 00:03:49.480 align:start position:0%
instead of deriving them from the
 

00:03:49.480 --> 00:03:53.110 align:start position:0%
instead of deriving them from the
context<00:03:50.319><c> directly</c><00:03:51.319><c> uh</c><00:03:51.560><c> that's</c><00:03:51.959><c> the</c><00:03:52.200><c> approach</c>

00:03:53.110 --> 00:03:53.120 align:start position:0%
context directly uh that's the approach
 

00:03:53.120 --> 00:03:55.069 align:start position:0%
context directly uh that's the approach
and<00:03:53.319><c> also</c><00:03:53.760><c> you</c><00:03:53.840><c> should</c><00:03:54.040><c> note</c><00:03:54.319><c> that</c><00:03:54.640><c> the</c><00:03:54.799><c> raft</c>

00:03:55.069 --> 00:03:55.079 align:start position:0%
and also you should note that the raft
 

00:03:55.079 --> 00:03:57.830 align:start position:0%
and also you should note that the raft
is<00:03:55.200><c> independent</c><00:03:55.640><c> of</c><00:03:55.799><c> the</c><00:03:56.000><c> ret</c><00:03:56.760><c> uh</c>

00:03:57.830 --> 00:03:57.840 align:start position:0%
is independent of the ret uh
 

00:03:57.840 --> 00:04:03.550 align:start position:0%
is independent of the ret uh
used<00:03:58.840><c> Okay</c><00:03:59.439><c> so</c><00:03:59.840><c> so</c><00:04:00.640><c> so</c><00:04:01.519><c> we'll</c><00:04:02.519><c> get</c><00:04:02.720><c> into</c><00:04:03.239><c> the</c>

00:04:03.550 --> 00:04:03.560 align:start position:0%
used Okay so so so we'll get into the
 

00:04:03.560 --> 00:04:06.190 align:start position:0%
used Okay so so so we'll get into the
details<00:04:04.000><c> of</c><00:04:04.480><c> how</c><00:04:04.680><c> you</c><00:04:04.799><c> can</c><00:04:05.400><c> directly</c><00:04:05.840><c> create</c>

00:04:06.190 --> 00:04:06.200 align:start position:0%
details of how you can directly create
 

00:04:06.200 --> 00:04:09.670 align:start position:0%
details of how you can directly create
this<00:04:06.400><c> data</c><00:04:06.680><c> set</c><00:04:07.239><c> uh</c><00:04:07.400><c> the</c><00:04:07.519><c> code</c><00:04:07.879><c> part</c><00:04:08.159><c> next</c><00:04:09.159><c> and</c>

00:04:09.670 --> 00:04:09.680 align:start position:0%
this data set uh the code part next and
 

00:04:09.680 --> 00:04:12.670 align:start position:0%
this data set uh the code part next and
one<00:04:10.319><c> key</c><00:04:10.519><c> interesting</c><00:04:11.000><c> thing</c><00:04:11.200><c> is</c><00:04:11.680><c> uh</c><00:04:12.280><c> they</c><00:04:12.439><c> use</c>

00:04:12.670 --> 00:04:12.680 align:start position:0%
one key interesting thing is uh they use
 

00:04:12.680 --> 00:04:15.069 align:start position:0%
one key interesting thing is uh they use
cot<00:04:13.159><c> CH</c><00:04:13.400><c> of</c><00:04:13.560><c> thought</c><00:04:13.799><c> to</c><00:04:14.000><c> explain</c><00:04:14.319><c> the</c><00:04:14.439><c> answers</c>

00:04:15.069 --> 00:04:15.079 align:start position:0%
cot CH of thought to explain the answers
 

00:04:15.079 --> 00:04:17.469 align:start position:0%
cot CH of thought to explain the answers
as<00:04:15.239><c> they</c><00:04:15.720><c> observed</c><00:04:16.720><c> uh</c><00:04:16.840><c> an</c><00:04:17.000><c> increase</c><00:04:17.320><c> in</c>

00:04:17.469 --> 00:04:17.479 align:start position:0%
as they observed uh an increase in
 

00:04:17.479 --> 00:04:18.749 align:start position:0%
as they observed uh an increase in
accuracy<00:04:17.840><c> in</c><00:04:17.959><c> answering</c><00:04:18.320><c> questions</c><00:04:18.600><c> with</c>

00:04:18.749 --> 00:04:18.759 align:start position:0%
accuracy in answering questions with
 

00:04:18.759 --> 00:04:24.150 align:start position:0%
accuracy in answering questions with
this<00:04:19.239><c> approach</c><00:04:20.239><c> so</c><00:04:20.919><c> and</c><00:04:21.919><c> uh</c><00:04:22.199><c> a</c><00:04:22.800><c> n</c><00:04:23.040><c> detail</c><00:04:23.960><c> for</c>

00:04:24.150 --> 00:04:24.160 align:start position:0%
this approach so and uh a n detail for
 

00:04:24.160 --> 00:04:26.030 align:start position:0%
this approach so and uh a n detail for
each<00:04:24.400><c> sample</c><00:04:24.720><c> in</c><00:04:24.800><c> the</c><00:04:24.919><c> training</c><00:04:25.280><c> data</c><00:04:25.840><c> all</c>

00:04:26.030 --> 00:04:26.040 align:start position:0%
each sample in the training data all
 

00:04:26.040 --> 00:04:29.350 align:start position:0%
each sample in the training data all
they<00:04:26.199><c> have</c><00:04:26.400><c> is</c><00:04:26.560><c> the</c><00:04:26.759><c> question</c><00:04:27.720><c> and</c><00:04:27.960><c> the</c><00:04:28.360><c> answer</c>

00:04:29.350 --> 00:04:29.360 align:start position:0%
they have is the question and the answer
 

00:04:29.360 --> 00:04:31.909 align:start position:0%
they have is the question and the answer
and<00:04:29.560><c> then</c><00:04:29.759><c> then</c><00:04:30.199><c> Oracle</c><00:04:30.759><c> document</c><00:04:31.280><c> and</c><00:04:31.520><c> then</c>

00:04:31.909 --> 00:04:31.919 align:start position:0%
and then then Oracle document and then
 

00:04:31.919 --> 00:04:34.469 align:start position:0%
and then then Oracle document and then
uh<00:04:32.080><c> distracted</c><00:04:32.639><c> documents</c><00:04:33.080><c> as</c><00:04:33.240><c> well</c><00:04:33.800><c> so</c><00:04:34.039><c> with</c>

00:04:34.469 --> 00:04:34.479 align:start position:0%
uh distracted documents as well so with
 

00:04:34.479 --> 00:04:39.110 align:start position:0%
uh distracted documents as well so with
these<00:04:35.479><c> set</c><00:04:36.039><c> of</c><00:04:37.039><c> um</c><00:04:37.919><c> training</c><00:04:38.360><c> data</c><00:04:38.600><c> set</c><00:04:38.919><c> they</c>

00:04:39.110 --> 00:04:39.120 align:start position:0%
these set of um training data set they
 

00:04:39.120 --> 00:04:42.550 align:start position:0%
these set of um training data set they
then<00:04:39.280><c> fine</c><00:04:39.600><c> tune</c><00:04:40.240><c> the</c><00:04:40.520><c> llm</c><00:04:41.320><c> and</c><00:04:41.479><c> then</c><00:04:41.639><c> use</c><00:04:41.840><c> it</c>

00:04:42.550 --> 00:04:42.560 align:start position:0%
then fine tune the llm and then use it
 

00:04:42.560 --> 00:04:44.670 align:start position:0%
then fine tune the llm and then use it
uh<00:04:42.759><c> for</c><00:04:43.000><c> it</c><00:04:43.120><c> to</c><00:04:43.440><c> generate</c><00:04:44.039><c> answers</c><00:04:44.400><c> for</c><00:04:44.560><c> the</c>

00:04:44.670 --> 00:04:44.680 align:start position:0%
uh for it to generate answers for the
 

00:04:44.680 --> 00:04:46.629 align:start position:0%
uh for it to generate answers for the
given<00:04:44.919><c> queries</c><00:04:45.199><c> in</c><00:04:45.280><c> the</c><00:04:45.400><c> specific</c><00:04:45.840><c> domain</c>

00:04:46.629 --> 00:04:46.639 align:start position:0%
given queries in the specific domain
 

00:04:46.639 --> 00:04:48.990 align:start position:0%
given queries in the specific domain
let's<00:04:46.960><c> get</c><00:04:47.120><c> into</c><00:04:47.400><c> the</c><00:04:47.759><c> Lama</c><00:04:48.120><c> pack</c><00:04:48.360><c> part</c>

00:04:48.990 --> 00:04:49.000 align:start position:0%
let's get into the Lama pack part
 

00:04:49.000 --> 00:04:51.710 align:start position:0%
let's get into the Lama pack part
wherein<00:04:49.280><c> I'll</c><00:04:49.600><c> go</c><00:04:49.759><c> through</c><00:04:50.000><c> the</c><00:04:50.320><c> code</c><00:04:50.639><c> base</c><00:04:51.520><c> U</c>

00:04:51.710 --> 00:04:51.720 align:start position:0%
wherein I'll go through the code base U
 

00:04:51.720 --> 00:04:54.950 align:start position:0%
wherein I'll go through the code base U
on<00:04:51.960><c> how</c><00:04:52.120><c> it's</c>

00:04:54.950 --> 00:04:54.960 align:start position:0%
 
 

00:04:54.960 --> 00:04:59.189 align:start position:0%
 
designed<00:04:55.960><c> so</c><00:04:56.840><c> this</c><00:04:56.960><c> is</c><00:04:57.160><c> the</c><00:04:57.759><c> uh</c><00:04:58.400><c> rap</c><00:04:58.759><c> data</c><00:04:59.039><c> set</c>

00:04:59.189 --> 00:04:59.199 align:start position:0%
designed so this is the uh rap data set
 

00:04:59.199 --> 00:05:00.790 align:start position:0%
designed so this is the uh rap data set
Lama<00:04:59.479><c> pack</c>

00:05:00.790 --> 00:05:00.800 align:start position:0%
Lama pack
 

00:05:00.800 --> 00:05:06.070 align:start position:0%
Lama pack
um<00:05:01.800><c> where</c><00:05:02.000><c> in</c><00:05:03.000><c> you</c><00:05:03.120><c> can</c><00:05:03.320><c> directly</c><00:05:03.720><c> use</c><00:05:04.479><c> uh</c><00:05:05.080><c> the</c>

00:05:06.070 --> 00:05:06.080 align:start position:0%
um where in you can directly use uh the
 

00:05:06.080 --> 00:05:08.150 align:start position:0%
um where in you can directly use uh the
pack<00:05:06.320><c> to</c><00:05:06.639><c> create</c><00:05:07.000><c> the</c><00:05:07.199><c> training</c><00:05:07.600><c> data</c><00:05:07.919><c> set</c><00:05:08.039><c> to</c>

00:05:08.150 --> 00:05:08.160 align:start position:0%
pack to create the training data set to
 

00:05:08.160 --> 00:05:11.590 align:start position:0%
pack to create the training data set to
train<00:05:08.440><c> the</c><00:05:08.759><c> ra</c><00:05:09.520><c> model</c><00:05:10.520><c> so</c><00:05:10.680><c> all</c><00:05:10.840><c> it</c><00:05:11.000><c> takes</c><00:05:11.280><c> is</c><00:05:11.400><c> a</c>

00:05:11.590 --> 00:05:11.600 align:start position:0%
train the ra model so all it takes is a
 

00:05:11.600 --> 00:05:14.550 align:start position:0%
train the ra model so all it takes is a
file<00:05:11.960><c> path</c><00:05:12.680><c> if</c><00:05:12.800><c> you</c><00:05:12.960><c> want</c><00:05:13.280><c> llm</c><00:05:13.960><c> any</c><00:05:14.240><c> specific</c>

00:05:14.550 --> 00:05:14.560 align:start position:0%
file path if you want llm any specific
 

00:05:14.560 --> 00:05:17.670 align:start position:0%
file path if you want llm any specific
llm<00:05:14.960><c> you</c><00:05:15.039><c> can</c><00:05:15.320><c> go</c><00:05:15.479><c> with</c><00:05:15.639><c> it</c><00:05:16.000><c> or</c><00:05:16.600><c> uh</c><00:05:17.240><c> embedding</c>

00:05:17.670 --> 00:05:17.680 align:start position:0%
llm you can go with it or uh embedding
 

00:05:17.680 --> 00:05:19.990 align:start position:0%
llm you can go with it or uh embedding
model<00:05:18.639><c> the</c><00:05:18.800><c> default</c><00:05:19.120><c> is</c><00:05:19.280><c> open</c><00:05:19.479><c> a</c><00:05:19.639><c> embedding</c>

00:05:19.990 --> 00:05:20.000 align:start position:0%
model the default is open a embedding
 

00:05:20.000 --> 00:05:22.070 align:start position:0%
model the default is open a embedding
model<00:05:20.479><c> and</c><00:05:20.639><c> the</c><00:05:20.759><c> number</c><00:05:21.000><c> of</c><00:05:21.160><c> questions</c><00:05:21.639><c> that</c>

00:05:22.070 --> 00:05:22.080 align:start position:0%
model and the number of questions that
 

00:05:22.080 --> 00:05:24.670 align:start position:0%
model and the number of questions that
you<00:05:22.199><c> want</c><00:05:22.360><c> to</c><00:05:22.479><c> create</c><00:05:22.840><c> per</c><00:05:23.039><c> chunk</c><00:05:23.759><c> and</c><00:05:23.880><c> the</c>

00:05:24.670 --> 00:05:24.680 align:start position:0%
you want to create per chunk and the
 

00:05:24.680 --> 00:05:27.309 align:start position:0%
you want to create per chunk and the
number<00:05:24.880><c> of</c><00:05:25.039><c> distractor</c><00:05:26.000><c> docks</c><00:05:27.000><c> what</c><00:05:27.120><c> is</c><00:05:27.199><c> the</c>

00:05:27.309 --> 00:05:27.319 align:start position:0%
number of distractor docks what is the
 

00:05:27.319 --> 00:05:30.870 align:start position:0%
number of distractor docks what is the
chunk<00:05:27.720><c> size</c><00:05:28.360><c> and</c><00:05:28.560><c> all</c><00:05:29.479><c> and</c><00:05:29.720><c> and</c><00:05:29.919><c> default</c><00:05:30.680><c> uh</c>

00:05:30.870 --> 00:05:30.880 align:start position:0%
chunk size and all and and default uh
 

00:05:30.880 --> 00:05:33.270 align:start position:0%
chunk size and all and and default uh
breakpoint<00:05:31.880><c> uh</c><00:05:32.039><c> percentile</c><00:05:32.560><c> threshold</c><00:05:33.120><c> is</c>

00:05:33.270 --> 00:05:33.280 align:start position:0%
breakpoint uh percentile threshold is
 

00:05:33.280 --> 00:05:38.629 align:start position:0%
breakpoint uh percentile threshold is
for<00:05:33.840><c> uh</c><00:05:34.840><c> uh</c><00:05:35.000><c> creating</c><00:05:35.840><c> semantic</c><00:05:36.759><c> uh</c><00:05:37.639><c> chunking</c>

00:05:38.629 --> 00:05:38.639 align:start position:0%
for uh uh creating semantic uh chunking
 

00:05:38.639 --> 00:05:44.590 align:start position:0%
for uh uh creating semantic uh chunking
um<00:05:39.479><c> so</c><00:05:40.280><c> so</c><00:05:41.000><c> yeah</c><00:05:42.000><c> so</c><00:05:42.440><c> let's</c><00:05:42.880><c> get</c><00:05:43.039><c> to</c><00:05:43.199><c> the</c><00:05:43.720><c> uh</c><00:05:44.440><c> uh</c>

00:05:44.590 --> 00:05:44.600 align:start position:0%
um so so yeah so let's get to the uh uh
 

00:05:44.600 --> 00:05:47.150 align:start position:0%
um so so yeah so let's get to the uh uh
Run<00:05:44.919><c> part</c><00:05:45.520><c> so</c><00:05:45.759><c> initially</c><00:05:46.120><c> we</c><00:05:46.280><c> create</c><00:05:46.840><c> uh</c><00:05:47.000><c> these</c>

00:05:47.150 --> 00:05:47.160 align:start position:0%
Run part so initially we create uh these
 

00:05:47.160 --> 00:05:50.990 align:start position:0%
Run part so initially we create uh these
chunks<00:05:47.720><c> given</c><00:05:47.919><c> a</c><00:05:48.160><c> document</c><00:05:48.919><c> uh</c><00:05:49.120><c> we</c><00:05:49.720><c> uh</c><00:05:50.000><c> load</c>

00:05:50.990 --> 00:05:51.000 align:start position:0%
chunks given a document uh we uh load
 

00:05:51.000 --> 00:05:53.270 align:start position:0%
chunks given a document uh we uh load
the<00:05:51.240><c> document</c><00:05:52.000><c> using</c><00:05:52.360><c> the</c><00:05:52.560><c> simple</c><00:05:52.880><c> direct</c>

00:05:53.270 --> 00:05:53.280 align:start position:0%
the document using the simple direct
 

00:05:53.280 --> 00:05:56.870 align:start position:0%
the document using the simple direct
reader<00:05:54.120><c> and</c><00:05:54.319><c> then</c><00:05:54.479><c> use</c><00:05:54.800><c> semantic</c><00:05:55.639><c> uh</c><00:05:55.880><c> splitter</c>

00:05:56.870 --> 00:05:56.880 align:start position:0%
reader and then use semantic uh splitter
 

00:05:56.880 --> 00:05:59.350 align:start position:0%
reader and then use semantic uh splitter
sorry<00:05:57.319><c> semantic</c><00:05:58.240><c> splitter</c><00:05:58.600><c> node</c><00:05:58.880><c> parer</c><00:05:59.240><c> to</c>

00:05:59.350 --> 00:05:59.360 align:start position:0%
sorry semantic splitter node parer to
 

00:05:59.360 --> 00:06:03.189 align:start position:0%
sorry semantic splitter node parer to
create<00:05:59.880><c> the</c><00:06:00.240><c> notes</c><00:06:00.840><c> and</c><00:06:01.039><c> then</c><00:06:01.600><c> U</c><00:06:02.240><c> get</c><00:06:02.479><c> the</c><00:06:02.919><c> text</c>

00:06:03.189 --> 00:06:03.199 align:start position:0%
create the notes and then U get the text
 

00:06:03.199 --> 00:06:05.629 align:start position:0%
create the notes and then U get the text
for<00:06:03.400><c> each</c><00:06:03.600><c> note</c><00:06:04.000><c> and</c><00:06:04.400><c> return</c><00:06:04.680><c> them</c><00:06:05.240><c> so</c><00:06:05.440><c> that's</c>

00:06:05.629 --> 00:06:05.639 align:start position:0%
for each note and return them so that's
 

00:06:05.639 --> 00:06:08.990 align:start position:0%
for each note and return them so that's
how<00:06:05.919><c> you</c><00:06:06.639><c> basically</c><00:06:07.160><c> get</c><00:06:07.680><c> the</c><00:06:07.880><c> chunks</c><00:06:08.280><c> out</c><00:06:08.479><c> of</c>

00:06:08.990 --> 00:06:09.000 align:start position:0%
how you basically get the chunks out of
 

00:06:09.000 --> 00:06:12.749 align:start position:0%
how you basically get the chunks out of
it<00:06:10.000><c> so</c><00:06:10.440><c> once</c><00:06:10.639><c> you</c><00:06:10.759><c> get</c><00:06:10.919><c> the</c><00:06:11.039><c> chunks</c><00:06:12.039><c> you</c>

00:06:12.749 --> 00:06:12.759 align:start position:0%
it so once you get the chunks you
 

00:06:12.759 --> 00:06:13.950 align:start position:0%
it so once you get the chunks you
basically<00:06:13.160><c> Define</c><00:06:13.479><c> the</c><00:06:13.599><c> number</c><00:06:13.800><c> of</c>

00:06:13.950 --> 00:06:13.960 align:start position:0%
basically Define the number of
 

00:06:13.960 --> 00:06:16.070 align:start position:0%
basically Define the number of
distracted<00:06:14.440><c> docs</c><00:06:14.840><c> it</c><00:06:14.919><c> should</c><00:06:15.120><c> be</c><00:06:15.280><c> less</c><00:06:15.520><c> than</c>

00:06:16.070 --> 00:06:16.080 align:start position:0%
distracted docs it should be less than
 

00:06:16.080 --> 00:06:19.350 align:start position:0%
distracted docs it should be less than
uh<00:06:16.280><c> number</c><00:06:16.479><c> of</c><00:06:16.639><c> chunks</c><00:06:17.039><c> available</c><00:06:17.599><c> right</c><00:06:18.360><c> so</c>

00:06:19.350 --> 00:06:19.360 align:start position:0%
uh number of chunks available right so
 

00:06:19.360 --> 00:06:21.110 align:start position:0%
uh number of chunks available right so
uh<00:06:19.520><c> take</c><00:06:19.680><c> the</c><00:06:19.840><c> minimum</c><00:06:20.199><c> value</c><00:06:20.479><c> of</c><00:06:20.720><c> both</c><00:06:20.919><c> of</c>

00:06:21.110 --> 00:06:21.120 align:start position:0%
uh take the minimum value of both of
 

00:06:21.120 --> 00:06:24.710 align:start position:0%
uh take the minimum value of both of
them<00:06:21.960><c> and</c><00:06:22.160><c> then</c><00:06:22.759><c> for</c><00:06:23.000><c> each</c><00:06:23.240><c> chunk</c><00:06:23.720><c> you</c><00:06:24.000><c> create</c>

00:06:24.710 --> 00:06:24.720 align:start position:0%
them and then for each chunk you create
 

00:06:24.720 --> 00:06:26.550 align:start position:0%
them and then for each chunk you create
uh<00:06:24.880><c> the</c><00:06:25.039><c> data</c><00:06:25.319><c> set</c><00:06:25.639><c> like</c><00:06:25.800><c> for</c><00:06:26.000><c> each</c><00:06:26.199><c> chunk</c>

00:06:26.550 --> 00:06:26.560 align:start position:0%
uh the data set like for each chunk
 

00:06:26.560 --> 00:06:29.390 align:start position:0%
uh the data set like for each chunk
first<00:06:26.880><c> you</c><00:06:27.120><c> create</c><00:06:27.479><c> the</c><00:06:28.000><c> questions</c><00:06:29.000><c> uh</c><00:06:29.240><c> let's</c>

00:06:29.390 --> 00:06:29.400 align:start position:0%
first you create the questions uh let's
 

00:06:29.400 --> 00:06:31.270 align:start position:0%
first you create the questions uh let's
say<00:06:30.039><c> here</c><00:06:30.280><c> the</c><00:06:30.360><c> number</c><00:06:30.599><c> of</c><00:06:30.759><c> questions</c><00:06:31.080><c> for</c>

00:06:31.270 --> 00:06:31.280 align:start position:0%
say here the number of questions for
 

00:06:31.280 --> 00:06:33.510 align:start position:0%
say here the number of questions for
each<00:06:31.440><c> un</c><00:06:31.720><c> is</c><00:06:31.880><c> five</c><00:06:32.599><c> and</c><00:06:32.800><c> again</c><00:06:33.000><c> for</c><00:06:33.240><c> each</c>

00:06:33.510 --> 00:06:33.520 align:start position:0%
each un is five and again for each
 

00:06:33.520 --> 00:06:36.950 align:start position:0%
each un is five and again for each
question<00:06:34.080><c> you</c><00:06:34.880><c> create</c><00:06:35.240><c> the</c><00:06:35.520><c> answer</c><00:06:36.440><c> cot</c>

00:06:36.950 --> 00:06:36.960 align:start position:0%
question you create the answer cot
 

00:06:36.960 --> 00:06:39.270 align:start position:0%
question you create the answer cot
answer<00:06:37.360><c> as</c><00:06:37.560><c> well</c><00:06:37.759><c> as</c><00:06:38.160><c> uh</c><00:06:38.440><c> what</c><00:06:38.560><c> is</c><00:06:38.800><c> Oracle</c>

00:06:39.270 --> 00:06:39.280 align:start position:0%
answer as well as uh what is Oracle
 

00:06:39.280 --> 00:06:41.430 align:start position:0%
answer as well as uh what is Oracle
document<00:06:39.800><c> what</c><00:06:39.919><c> is</c><00:06:40.039><c> the</c><00:06:40.319><c> distracted</c>

00:06:41.430 --> 00:06:41.440 align:start position:0%
document what is the distracted
 

00:06:41.440 --> 00:06:43.670 align:start position:0%
document what is the distracted
documents<00:06:42.440><c> so</c><00:06:42.680><c> let's</c><00:06:43.080><c> uh</c><00:06:43.199><c> get</c><00:06:43.319><c> to</c><00:06:43.479><c> this</c>

00:06:43.670 --> 00:06:43.680 align:start position:0%
documents so let's uh get to this
 

00:06:43.680 --> 00:06:48.189 align:start position:0%
documents so let's uh get to this
function

00:06:48.189 --> 00:06:48.199 align:start position:0%
 
 

00:06:48.199 --> 00:06:52.070 align:start position:0%
 
here<00:06:49.199><c> so</c><00:06:49.520><c> here</c><00:06:50.039><c> uh</c><00:06:50.360><c> you</c><00:06:51.000><c> uh</c><00:06:51.120><c> you</c><00:06:51.360><c> generate</c><00:06:51.800><c> the</c>

00:06:52.070 --> 00:06:52.080 align:start position:0%
here so here uh you uh you generate the
 

00:06:52.080 --> 00:06:54.749 align:start position:0%
here so here uh you uh you generate the
instructions<00:06:53.080><c> here</c><00:06:53.520><c> uh</c><00:06:53.840><c> so</c><00:06:54.120><c> which</c><00:06:54.319><c> creates</c>

00:06:54.749 --> 00:06:54.759 align:start position:0%
instructions here uh so which creates
 

00:06:54.759 --> 00:06:57.070 align:start position:0%
instructions here uh so which creates
basically<00:06:55.199><c> the</c><00:06:55.360><c> questions</c><00:06:56.280><c> uh</c><00:06:56.479><c> for</c><00:06:56.720><c> from</c><00:06:56.879><c> the</c>

00:06:57.070 --> 00:06:57.080 align:start position:0%
basically the questions uh for from the
 

00:06:57.080 --> 00:07:00.790 align:start position:0%
basically the questions uh for from the
given<00:06:57.360><c> junk</c><00:06:58.199><c> so</c><00:06:58.400><c> if</c><00:06:58.520><c> you</c><00:06:58.720><c> go</c><00:06:59.080><c> here</c><00:06:59.800><c> uh</c><00:07:00.520><c> you</c><00:07:00.680><c> have</c>

00:07:00.790 --> 00:07:00.800 align:start position:0%
given junk so if you go here uh you have
 

00:07:00.800 --> 00:07:03.950 align:start position:0%
given junk so if you go here uh you have
a<00:07:00.960><c> prompt</c><00:07:01.360><c> to</c><00:07:01.800><c> create</c><00:07:02.280><c> question</c><00:07:02.560><c> answer</c><00:07:02.960><c> page</c>

00:07:03.950 --> 00:07:03.960 align:start position:0%
a prompt to create question answer page
 

00:07:03.960 --> 00:07:06.390 align:start position:0%
a prompt to create question answer page
and<00:07:04.199><c> then</c><00:07:04.680><c> uh</c><00:07:04.919><c> you'll</c><00:07:05.240><c> create</c><00:07:05.599><c> all</c><00:07:05.879><c> those</c>

00:07:06.390 --> 00:07:06.400 align:start position:0%
and then uh you'll create all those
 

00:07:06.400 --> 00:07:09.950 align:start position:0%
and then uh you'll create all those
questions<00:07:07.720><c> uh</c><00:07:08.720><c> by</c><00:07:08.879><c> default</c><00:07:09.240><c> it</c><00:07:09.360><c> creates</c><00:07:09.720><c> five</c>

00:07:09.950 --> 00:07:09.960 align:start position:0%
questions uh by default it creates five
 

00:07:09.960 --> 00:07:12.830 align:start position:0%
questions uh by default it creates five
queries<00:07:10.440><c> per</c><00:07:10.759><c> chunk</c><00:07:11.479><c> right</c><00:07:11.680><c> so</c><00:07:11.919><c> it</c><00:07:12.080><c> creates</c>

00:07:12.830 --> 00:07:12.840 align:start position:0%
queries per chunk right so it creates
 

00:07:12.840 --> 00:07:14.469 align:start position:0%
queries per chunk right so it creates
this<00:07:13.080><c> function</c><00:07:13.400><c> creates</c><00:07:13.759><c> five</c><00:07:13.960><c> questions</c><00:07:14.319><c> per</c>

00:07:14.469 --> 00:07:14.479 align:start position:0%
this function creates five questions per
 

00:07:14.479 --> 00:07:17.790 align:start position:0%
this function creates five questions per
chunk<00:07:15.479><c> and</c><00:07:15.759><c> once</c><00:07:16.080><c> you</c><00:07:16.280><c> have</c><00:07:16.720><c> a</c>

00:07:17.790 --> 00:07:17.800 align:start position:0%
chunk and once you have a
 

00:07:17.800 --> 00:07:20.189 align:start position:0%
chunk and once you have a
query<00:07:18.800><c> you</c><00:07:19.120><c> have</c><00:07:19.240><c> to</c><00:07:19.440><c> create</c><00:07:19.800><c> what</c><00:07:19.879><c> is</c><00:07:20.000><c> the</c>

00:07:20.189 --> 00:07:20.199 align:start position:0%
query you have to create what is the
 

00:07:20.199 --> 00:07:22.589 align:start position:0%
query you have to create what is the
context<00:07:20.720><c> what</c><00:07:20.840><c> is</c><00:07:20.960><c> the</c><00:07:21.280><c> cot</c><00:07:21.800><c> answer</c><00:07:22.240><c> and</c><00:07:22.479><c> what</c>

00:07:22.589 --> 00:07:22.599 align:start position:0%
context what is the cot answer and what
 

00:07:22.599 --> 00:07:27.350 align:start position:0%
context what is the cot answer and what
is<00:07:22.800><c> the</c><00:07:23.440><c> uh</c><00:07:23.879><c> Oracle</c><00:07:24.360><c> context</c><00:07:24.879><c> right</c><00:07:25.919><c> so</c><00:07:26.919><c> so</c><00:07:27.120><c> for</c>

00:07:27.350 --> 00:07:27.360 align:start position:0%
is the uh Oracle context right so so for
 

00:07:27.360 --> 00:07:29.869 align:start position:0%
is the uh Oracle context right so so for
these<00:07:27.639><c> and</c><00:07:27.840><c> then</c><00:07:28.199><c> you</c><00:07:28.319><c> need</c><00:07:28.479><c> to</c><00:07:28.680><c> decide</c><00:07:29.360><c> uh</c>

00:07:29.869 --> 00:07:29.879 align:start position:0%
these and then you need to decide uh
 

00:07:29.879 --> 00:07:32.230 align:start position:0%
these and then you need to decide uh
whether<00:07:30.599><c> this</c><00:07:30.840><c> particular</c><00:07:31.280><c> sample</c><00:07:31.800><c> will</c><00:07:32.039><c> have</c>

00:07:32.230 --> 00:07:32.240 align:start position:0%
whether this particular sample will have
 

00:07:32.240 --> 00:07:35.790 align:start position:0%
whether this particular sample will have
an<00:07:32.400><c> oracle</c><00:07:32.840><c> document</c><00:07:33.199><c> or</c><00:07:33.440><c> not</c><00:07:34.479><c> so</c><00:07:35.479><c> this</c><00:07:35.639><c> part</c>

00:07:35.790 --> 00:07:35.800 align:start position:0%
an oracle document or not so this part
 

00:07:35.800 --> 00:07:39.670 align:start position:0%
an oracle document or not so this part
of<00:07:35.919><c> the</c><00:07:36.080><c> code</c><00:07:36.440><c> decides</c><00:07:36.919><c> that</c><00:07:37.199><c> by</c><00:07:37.800><c> uh</c><00:07:38.120><c> having</c><00:07:38.800><c> uh</c>

00:07:39.670 --> 00:07:39.680 align:start position:0%
of the code decides that by uh having uh
 

00:07:39.680 --> 00:07:43.189 align:start position:0%
of the code decides that by uh having uh
random<00:07:40.800><c> sampling</c><00:07:41.800><c> and</c><00:07:42.080><c> then</c><00:07:42.520><c> once</c><00:07:42.720><c> you</c><00:07:42.960><c> have</c>

00:07:43.189 --> 00:07:43.199 align:start position:0%
random sampling and then once you have
 

00:07:43.199 --> 00:07:45.950 align:start position:0%
random sampling and then once you have
that<00:07:43.840><c> you</c><00:07:44.120><c> create</c><00:07:44.520><c> the</c><00:07:44.759><c> Oracle</c><00:07:45.240><c> context</c><00:07:45.759><c> and</c>

00:07:45.950 --> 00:07:45.960 align:start position:0%
that you create the Oracle context and
 

00:07:45.960 --> 00:07:49.629 align:start position:0%
that you create the Oracle context and
then<00:07:46.520><c> what</c><00:07:46.680><c> is</c><00:07:46.919><c> the</c><00:07:47.639><c> what</c><00:07:47.800><c> are</c><00:07:48.039><c> the</c><00:07:48.879><c> uh</c><00:07:49.400><c> what</c><00:07:49.520><c> is</c>

00:07:49.629 --> 00:07:49.639 align:start position:0%
then what is the what are the uh what is
 

00:07:49.639 --> 00:07:50.990 align:start position:0%
then what is the what are the uh what is
a<00:07:49.919><c> cot</c>

00:07:50.990 --> 00:07:51.000 align:start position:0%
a cot
 

00:07:51.000 --> 00:07:56.909 align:start position:0%
a cot
answer<00:07:52.000><c> um</c><00:07:52.759><c> and</c><00:07:53.000><c> then</c><00:07:53.599><c> uh</c><00:07:54.599><c> have</c><00:07:54.919><c> the</c><00:07:55.599><c> um</c><00:07:56.199><c> other</c>

00:07:56.909 --> 00:07:56.919 align:start position:0%
answer um and then uh have the um other
 

00:07:56.919 --> 00:07:57.830 align:start position:0%
answer um and then uh have the um other
uh

00:07:57.830 --> 00:07:57.840 align:start position:0%
uh
 

00:07:57.840 --> 00:08:00.869 align:start position:0%
uh
context<00:07:58.840><c> so</c><00:07:59.080><c> this</c><00:07:59.280><c> way</c><00:07:59.680><c> the</c><00:07:59.840><c> whole</c><00:08:00.360><c> uh</c><00:08:00.560><c> data</c>

00:08:00.869 --> 00:08:00.879 align:start position:0%
context so this way the whole uh data
 

00:08:00.879 --> 00:08:04.230 align:start position:0%
context so this way the whole uh data
set<00:08:01.440><c> will</c><00:08:01.599><c> be</c><00:08:01.800><c> created</c><00:08:02.599><c> the</c><00:08:03.599><c> and</c><00:08:03.800><c> then</c><00:08:04.000><c> it</c>

00:08:04.230 --> 00:08:04.240 align:start position:0%
set will be created the and then it
 

00:08:04.240 --> 00:08:06.710 align:start position:0%
set will be created the and then it
being<00:08:04.680><c> returned</c><00:08:05.199><c> so</c>

00:08:06.710 --> 00:08:06.720 align:start position:0%
being returned so
 

00:08:06.720 --> 00:08:09.309 align:start position:0%
being returned so
once<00:08:07.720><c> we</c><00:08:07.879><c> have</c><00:08:08.120><c> these</c><00:08:08.360><c> data</c><00:08:08.639><c> set</c><00:08:08.800><c> which</c><00:08:08.960><c> is</c>

00:08:09.309 --> 00:08:09.319 align:start position:0%
once we have these data set which is
 

00:08:09.319 --> 00:08:11.309 align:start position:0%
once we have these data set which is
hugging<00:08:09.599><c> face</c><00:08:09.879><c> data</c><00:08:10.120><c> set</c><00:08:10.280><c> format</c><00:08:10.720><c> you</c><00:08:10.840><c> can</c><00:08:11.039><c> use</c>

00:08:11.309 --> 00:08:11.319 align:start position:0%
hugging face data set format you can use
 

00:08:11.319 --> 00:08:16.230 align:start position:0%
hugging face data set format you can use
them<00:08:11.960><c> uh</c><00:08:12.479><c> in</c><00:08:13.000><c> your</c><00:08:13.360><c> try</c><00:08:13.639><c> F</c><00:08:13.840><c> tuning</c><00:08:14.639><c> pipeline</c><00:08:15.639><c> so</c>

00:08:16.230 --> 00:08:16.240 align:start position:0%
them uh in your try F tuning pipeline so
 

00:08:16.240 --> 00:08:19.749 align:start position:0%
them uh in your try F tuning pipeline so
now<00:08:16.479><c> let's</c><00:08:17.039><c> get</c><00:08:17.400><c> to</c><00:08:17.759><c> see</c><00:08:18.360><c> the</c><00:08:18.720><c> notebook</c><00:08:19.639><c> uh</c>

00:08:19.749 --> 00:08:19.759 align:start position:0%
now let's get to see the notebook uh
 

00:08:19.759 --> 00:08:21.869 align:start position:0%
now let's get to see the notebook uh
walk<00:08:20.080><c> through</c><00:08:20.400><c> how</c><00:08:20.560><c> you</c><00:08:20.680><c> can</c><00:08:20.840><c> use</c><00:08:21.080><c> this</c><00:08:21.280><c> Lama</c>

00:08:21.869 --> 00:08:21.879 align:start position:0%
walk through how you can use this Lama
 

00:08:21.879 --> 00:08:24.749 align:start position:0%
walk through how you can use this Lama
pack<00:08:22.159><c> to</c><00:08:22.479><c> generate</c><00:08:22.840><c> the</c><00:08:23.000><c> data</c>

00:08:24.749 --> 00:08:24.759 align:start position:0%
pack to generate the data
 

00:08:24.759 --> 00:08:27.510 align:start position:0%
pack to generate the data
set<00:08:25.759><c> so</c><00:08:26.039><c> in</c><00:08:26.199><c> this</c><00:08:26.319><c> notebook</c><00:08:26.680><c> we'll</c><00:08:26.879><c> look</c><00:08:27.039><c> into</c>

00:08:27.510 --> 00:08:27.520 align:start position:0%
set so in this notebook we'll look into
 

00:08:27.520 --> 00:08:29.469 align:start position:0%
set so in this notebook we'll look into
how<00:08:27.639><c> you</c><00:08:27.759><c> can</c><00:08:27.919><c> use</c><00:08:28.199><c> the</c><00:08:28.560><c> Llama</c><00:08:28.960><c> pack</c><00:08:29.120><c> to</c><00:08:29.240><c> create</c>

00:08:29.469 --> 00:08:29.479 align:start position:0%
how you can use the Llama pack to create
 

00:08:29.479 --> 00:08:32.269 align:start position:0%
how you can use the Llama pack to create
cre<00:08:29.680><c> the</c><00:08:29.800><c> data</c><00:08:30.039><c> set</c><00:08:30.319><c> here</c><00:08:31.120><c> so</c><00:08:31.440><c> we</c><00:08:31.560><c> need</c><00:08:31.840><c> open</c><00:08:32.080><c> a</c>

00:08:32.269 --> 00:08:32.279 align:start position:0%
cre the data set here so we need open a
 

00:08:32.279 --> 00:08:36.350 align:start position:0%
cre the data set here so we need open a
AP<00:08:32.680><c> Key</c><00:08:32.880><c> by</c><00:08:33.320><c> default</c><00:08:34.320><c> and</c><00:08:34.599><c> then</c><00:08:35.479><c> download</c><00:08:36.039><c> the</c>

00:08:36.350 --> 00:08:36.360 align:start position:0%
AP Key by default and then download the
 

00:08:36.360 --> 00:08:39.949 align:start position:0%
AP Key by default and then download the
pogram<00:08:36.919><c> essay</c><00:08:37.839><c> text</c><00:08:38.839><c> um</c><00:08:39.360><c> we'll</c><00:08:39.640><c> probably</c>

00:08:39.949 --> 00:08:39.959 align:start position:0%
pogram essay text um we'll probably
 

00:08:39.959 --> 00:08:44.269 align:start position:0%
pogram essay text um we'll probably
remove<00:08:41.000><c> um</c><00:08:42.000><c> a</c><00:08:42.240><c> part</c><00:08:42.440><c> of</c><00:08:42.640><c> it</c><00:08:43.000><c> because</c><00:08:43.959><c> uh</c><00:08:44.120><c> it</c>

00:08:44.269 --> 00:08:44.279 align:start position:0%
remove um a part of it because uh it
 

00:08:44.279 --> 00:08:47.870 align:start position:0%
remove um a part of it because uh it
takes<00:08:44.600><c> huge</c><00:08:44.880><c> amount</c><00:08:45.120><c> of</c><00:08:45.279><c> time</c><00:08:46.279><c> to</c><00:08:46.560><c> create</c><00:08:47.240><c> uh</c>

00:08:47.870 --> 00:08:47.880 align:start position:0%
takes huge amount of time to create uh
 

00:08:47.880 --> 00:08:50.430 align:start position:0%
takes huge amount of time to create uh
um<00:08:48.519><c> the</c><00:08:48.720><c> training</c><00:08:49.160><c> data</c><00:08:49.360><c> for</c><00:08:49.560><c> the</c><00:08:49.680><c> whole</c><00:08:49.920><c> data</c>

00:08:50.430 --> 00:08:50.440 align:start position:0%
um the training data for the whole data
 

00:08:50.440 --> 00:08:53.790 align:start position:0%
um the training data for the whole data
set<00:08:51.440><c> probably</c><00:08:51.720><c> I'll</c><00:08:51.959><c> just</c><00:08:52.160><c> take</c><00:08:52.640><c> very</c><00:08:52.920><c> small</c>

00:08:53.790 --> 00:08:53.800 align:start position:0%
set probably I'll just take very small
 

00:08:53.800 --> 00:08:57.470 align:start position:0%
set probably I'll just take very small
part<00:08:54.080><c> of</c>

00:08:57.470 --> 00:08:57.480 align:start position:0%
 
 

00:08:57.480 --> 00:09:00.110 align:start position:0%
 
it<00:08:58.480><c> and</c><00:08:58.839><c> then</c>

00:09:00.110 --> 00:09:00.120 align:start position:0%
it and then
 

00:09:00.120 --> 00:09:01.949 align:start position:0%
it and then
when<00:09:00.360><c> this</c><00:09:00.519><c> is</c><00:09:00.680><c> done</c>

00:09:01.949 --> 00:09:01.959 align:start position:0%
when this is done
 

00:09:01.959 --> 00:09:06.630 align:start position:0%
when this is done
you<00:09:02.959><c> uh</c><00:09:03.160><c> import</c><00:09:03.600><c> rap</c><00:09:04.000><c> data</c><00:09:04.320><c> set</c><00:09:04.519><c> Lama</c>

00:09:06.630 --> 00:09:06.640 align:start position:0%
you uh import rap data set Lama
 

00:09:06.640 --> 00:09:11.190 align:start position:0%
you uh import rap data set Lama
pack<00:09:07.640><c> and</c><00:09:07.920><c> then</c><00:09:08.920><c> give</c><00:09:09.200><c> the</c><00:09:09.360><c> file</c><00:09:10.040><c> path</c><00:09:11.040><c> and</c>

00:09:11.190 --> 00:09:11.200 align:start position:0%
pack and then give the file path and
 

00:09:11.200 --> 00:09:12.389 align:start position:0%
pack and then give the file path and
then<00:09:11.399><c> run</c>

00:09:12.389 --> 00:09:12.399 align:start position:0%
then run
 

00:09:12.399 --> 00:09:16.590 align:start position:0%
then run
it<00:09:13.880><c> so</c><00:09:14.880><c> so</c><00:09:15.200><c> this</c><00:09:15.399><c> says</c><00:09:15.720><c> there</c><00:09:15.920><c> are</c><00:09:16.399><c> three</c>

00:09:16.590 --> 00:09:16.600 align:start position:0%
it so so this says there are three
 

00:09:16.600 --> 00:09:19.470 align:start position:0%
it so so this says there are three
chunks<00:09:17.000><c> created</c><00:09:17.760><c> and</c><00:09:18.120><c> it's</c><00:09:18.560><c> processing</c><00:09:19.320><c> uh</c>

00:09:19.470 --> 00:09:19.480 align:start position:0%
chunks created and it's processing uh
 

00:09:19.480 --> 00:09:20.590 align:start position:0%
chunks created and it's processing uh
chunk

00:09:20.590 --> 00:09:20.600 align:start position:0%
chunk
 

00:09:20.600 --> 00:09:23.630 align:start position:0%
chunk
one<00:09:21.600><c> and</c><00:09:22.040><c> uh</c><00:09:22.320><c> this</c><00:09:22.480><c> will</c><00:09:22.680><c> take</c><00:09:23.120><c> quite</c><00:09:23.399><c> some</c>

00:09:23.630 --> 00:09:23.640 align:start position:0%
one and uh this will take quite some
 

00:09:23.640 --> 00:09:27.550 align:start position:0%
one and uh this will take quite some
time<00:09:24.120><c> since</c><00:09:24.399><c> back</c><00:09:24.560><c> end</c><00:09:25.160><c> uh</c><00:09:25.440><c> this</c><00:09:25.600><c> uses</c><00:09:25.959><c> GPT</c><00:09:26.560><c> 4</c>

00:09:27.550 --> 00:09:27.560 align:start position:0%
time since back end uh this uses GPT 4
 

00:09:27.560 --> 00:09:29.990 align:start position:0%
time since back end uh this uses GPT 4
and<00:09:28.040><c> uh</c><00:09:28.360><c> embeddings</c>

00:09:29.990 --> 00:09:30.000 align:start position:0%
and uh embeddings
 

00:09:30.000 --> 00:09:32.470 align:start position:0%
and uh embeddings
as<00:09:30.200><c> well</c><00:09:30.680><c> so</c><00:09:30.920><c> as</c><00:09:31.040><c> you</c><00:09:31.160><c> can</c><00:09:31.320><c> see</c><00:09:31.800><c> uh</c><00:09:31.920><c> it</c><00:09:32.040><c> used</c>

00:09:32.470 --> 00:09:32.480 align:start position:0%
as well so as you can see uh it used
 

00:09:32.480 --> 00:09:34.590 align:start position:0%
as well so as you can see uh it used
embeddings<00:09:32.959><c> in</c><00:09:33.079><c> the</c><00:09:33.200><c> first</c><00:09:33.480><c> call</c><00:09:34.120><c> uh</c><00:09:34.360><c> to</c>

00:09:34.590 --> 00:09:34.600 align:start position:0%
embeddings in the first call uh to
 

00:09:34.600 --> 00:09:36.949 align:start position:0%
embeddings in the first call uh to
create<00:09:34.920><c> the</c><00:09:35.120><c> chunks</c><00:09:36.120><c> um</c><00:09:36.440><c> that's</c><00:09:36.640><c> because</c><00:09:36.880><c> we</c>

00:09:36.949 --> 00:09:36.959 align:start position:0%
create the chunks um that's because we
 

00:09:36.959 --> 00:09:39.990 align:start position:0%
create the chunks um that's because we
are<00:09:37.079><c> using</c><00:09:37.360><c> santing</c><00:09:37.839><c> chunking</c><00:09:38.760><c> parser</c><00:09:39.760><c> and</c>

00:09:39.990 --> 00:09:40.000 align:start position:0%
are using santing chunking parser and
 

00:09:40.000 --> 00:09:43.870 align:start position:0%
are using santing chunking parser and
then<00:09:40.680><c> um</c><00:09:41.120><c> fore</c><00:09:41.800><c> chunk</c><00:09:42.800><c> um</c><00:09:43.160><c> the</c><00:09:43.320><c> first</c><00:09:43.560><c> one</c><00:09:43.720><c> will</c>

00:09:43.870 --> 00:09:43.880 align:start position:0%
then um fore chunk um the first one will
 

00:09:43.880 --> 00:09:46.949 align:start position:0%
then um fore chunk um the first one will
be<00:09:44.079><c> to</c><00:09:44.480><c> create</c><00:09:44.880><c> the</c><00:09:45.560><c> queries</c><00:09:46.440><c> so</c><00:09:46.680><c> we</c><00:09:46.800><c> have</c>

00:09:46.949 --> 00:09:46.959 align:start position:0%
be to create the queries so we have
 

00:09:46.959 --> 00:09:49.710 align:start position:0%
be to create the queries so we have
created<00:09:47.360><c> Five</c><00:09:47.600><c> queries</c><00:09:48.200><c> by</c><00:09:48.399><c> default</c><00:09:49.399><c> and</c><00:09:49.600><c> then</c>

00:09:49.710 --> 00:09:49.720 align:start position:0%
created Five queries by default and then
 

00:09:49.720 --> 00:09:52.630 align:start position:0%
created Five queries by default and then
you<00:09:49.839><c> can</c><00:09:50.040><c> see</c><00:09:50.920><c> um</c><00:09:51.680><c> the</c><00:09:51.920><c> the</c><00:09:52.040><c> rest</c><00:09:52.279><c> is</c><00:09:52.399><c> for</c>

00:09:52.630 --> 00:09:52.640 align:start position:0%
you can see um the the rest is for
 

00:09:52.640 --> 00:09:55.750 align:start position:0%
you can see um the the rest is for
creating<00:09:53.040><c> answers</c><00:09:53.720><c> uh</c><00:09:53.920><c> 1</c><00:09:54.200><c> 2</c><00:09:54.480><c> 3</c><00:09:54.720><c> 4</c><00:09:55.079><c> five</c><00:09:55.519><c> so</c>

00:09:55.750 --> 00:09:55.760 align:start position:0%
creating answers uh 1 2 3 4 five so
 

00:09:55.760 --> 00:09:59.870 align:start position:0%
creating answers uh 1 2 3 4 five so
there<00:09:55.880><c> are</c><00:09:56.519><c> total</c><00:09:56.839><c> six</c><00:09:57.120><c> API</c><00:09:57.480><c> calls</c><00:09:58.040><c> uh</c><00:09:59.000><c> uh</c><00:09:59.600><c> for</c>

00:09:59.870 --> 00:09:59.880 align:start position:0%
there are total six API calls uh uh for
 

00:09:59.880 --> 00:10:02.670 align:start position:0%
there are total six API calls uh uh for
each<00:10:00.160><c> chunk</c><00:10:00.560><c> once</c><00:10:01.320><c> to</c><00:10:01.560><c> create</c><00:10:01.839><c> the</c><00:10:02.000><c> questions</c>

00:10:02.670 --> 00:10:02.680 align:start position:0%
each chunk once to create the questions
 

00:10:02.680 --> 00:10:05.030 align:start position:0%
each chunk once to create the questions
and<00:10:03.079><c> uh</c><00:10:03.320><c> and</c><00:10:03.480><c> then</c><00:10:03.720><c> again</c><00:10:04.279><c> the</c><00:10:04.480><c> subsequent</c>

00:10:05.030 --> 00:10:05.040 align:start position:0%
and uh and then again the subsequent
 

00:10:05.040 --> 00:10:07.110 align:start position:0%
and uh and then again the subsequent
calls<00:10:05.600><c> for</c><00:10:05.920><c> creating</c><00:10:06.279><c> the</c><00:10:06.440><c> answers</c><00:10:06.760><c> for</c><00:10:06.920><c> each</c>

00:10:07.110 --> 00:10:07.120 align:start position:0%
calls for creating the answers for each
 

00:10:07.120 --> 00:10:11.230 align:start position:0%
calls for creating the answers for each
question<00:10:07.440><c> in</c><00:10:07.560><c> a</c><00:10:08.160><c> chunk</c><00:10:09.160><c> so</c><00:10:09.880><c> and</c><00:10:10.079><c> then</c><00:10:10.880><c> the</c><00:10:11.040><c> next</c>

00:10:11.230 --> 00:10:11.240 align:start position:0%
question in a chunk so and then the next
 

00:10:11.240 --> 00:10:15.269 align:start position:0%
question in a chunk so and then the next
one<00:10:11.480><c> is</c><00:10:12.079><c> uh</c><00:10:12.240><c> going</c><00:10:12.560><c> for</c><00:10:13.279><c> the</c><00:10:13.440><c> chunk</c><00:10:13.760><c> one</c>

00:10:15.269 --> 00:10:15.279 align:start position:0%
one is uh going for the chunk one
 

00:10:15.279 --> 00:10:19.670 align:start position:0%
one is uh going for the chunk one
so<00:10:16.279><c> and</c><00:10:16.519><c> again</c><00:10:16.800><c> the</c><00:10:16.920><c> Chun</c><00:10:17.320><c> two</c><00:10:18.320><c> later</c><00:10:18.600><c> on</c><00:10:19.480><c> and</c>

00:10:19.670 --> 00:10:19.680 align:start position:0%
so and again the Chun two later on and
 

00:10:19.680 --> 00:10:22.750 align:start position:0%
so and again the Chun two later on and
then<00:10:20.240><c> um</c><00:10:20.560><c> once</c><00:10:21.320><c> uh</c><00:10:21.440><c> the</c><00:10:21.600><c> data</c><00:10:21.880><c> set</c><00:10:22.040><c> is</c><00:10:22.160><c> created</c>

00:10:22.750 --> 00:10:22.760 align:start position:0%
then um once uh the data set is created
 

00:10:22.760 --> 00:10:25.430 align:start position:0%
then um once uh the data set is created
you<00:10:22.920><c> can</c><00:10:23.360><c> um</c><00:10:24.079><c> save</c><00:10:24.320><c> it</c><00:10:24.440><c> in</c><00:10:24.600><c> Arrow</c><00:10:24.920><c> format</c><00:10:25.279><c> or</c>

00:10:25.430 --> 00:10:25.440 align:start position:0%
you can um save it in Arrow format or
 

00:10:25.440 --> 00:10:27.829 align:start position:0%
you can um save it in Arrow format or
Json<00:10:25.839><c> format</c><00:10:26.240><c> or</c><00:10:26.440><c> this</c><00:10:26.560><c> is</c><00:10:26.839><c> in</c><00:10:27.040><c> hugging</c><00:10:27.360><c> face</c>

00:10:27.829 --> 00:10:27.839 align:start position:0%
Json format or this is in hugging face
 

00:10:27.839 --> 00:10:29.310 align:start position:0%
Json format or this is in hugging face
data<00:10:28.120><c> set</c><00:10:28.279><c> format</c><00:10:28.680><c> and</c><00:10:28.800><c> then</c><00:10:28.920><c> you</c><00:10:29.000><c> can</c><00:10:29.120><c> direct</c>

00:10:29.310 --> 00:10:29.320 align:start position:0%
data set format and then you can direct
 

00:10:29.320 --> 00:10:31.790 align:start position:0%
data set format and then you can direct
direct<00:10:29.480><c> L</c><00:10:29.600><c> use</c><00:10:29.800><c> it</c><00:10:29.959><c> for</c><00:10:30.160><c> find</c><00:10:30.399><c> tuning</c>

00:10:31.790 --> 00:10:31.800 align:start position:0%
direct L use it for find tuning
 

00:10:31.800 --> 00:10:34.110 align:start position:0%
direct L use it for find tuning
purposes

00:10:34.110 --> 00:10:34.120 align:start position:0%
purposes
 

00:10:34.120 --> 00:10:37.829 align:start position:0%
purposes
so<00:10:35.120><c> this</c><00:10:35.399><c> might</c><00:10:35.839><c> take</c><00:10:36.839><c> I</c><00:10:36.959><c> think</c><00:10:37.320><c> another</c>

00:10:37.829 --> 00:10:37.839 align:start position:0%
so this might take I think another
 

00:10:37.839 --> 00:10:40.550 align:start position:0%
so this might take I think another
minute<00:10:38.160><c> or</c>

00:10:40.550 --> 00:10:40.560 align:start position:0%
 
 

00:10:40.560 --> 00:10:43.990 align:start position:0%
 
so<00:10:41.560><c> inspect</c><00:10:41.959><c> the</c><00:10:42.160><c> data</c>

00:10:43.990 --> 00:10:44.000 align:start position:0%
so inspect the data
 

00:10:44.000 --> 00:10:47.870 align:start position:0%
so inspect the data
set<00:10:45.000><c> so</c><00:10:45.480><c> we</c>

00:10:47.870 --> 00:10:47.880 align:start position:0%
 
 

00:10:47.880 --> 00:10:49.910 align:start position:0%
 
have<00:10:48.880><c> 15</c>

00:10:49.910 --> 00:10:49.920 align:start position:0%
have 15
 

00:10:49.920 --> 00:10:52.670 align:start position:0%
have 15
rows<00:10:50.920><c> as</c><00:10:51.160><c> said</c><00:10:51.639><c> we</c><00:10:51.760><c> have</c><00:10:51.959><c> three</c><00:10:52.120><c> chunks</c><00:10:52.480><c> we</c>

00:10:52.670 --> 00:10:52.680 align:start position:0%
rows as said we have three chunks we
 

00:10:52.680 --> 00:10:54.150 align:start position:0%
rows as said we have three chunks we
created<00:10:53.000><c> three</c><00:10:53.279><c> chunks</c><00:10:53.560><c> and</c><00:10:53.760><c> then</c><00:10:53.959><c> five</c>

00:10:54.150 --> 00:10:54.160 align:start position:0%
created three chunks and then five
 

00:10:54.160 --> 00:10:56.629 align:start position:0%
created three chunks and then five
queries<00:10:54.480><c> per</c><00:10:54.639><c> chunks</c><00:10:54.959><c> so</c><00:10:55.639><c> 15</c><00:10:56.040><c> there</c><00:10:56.120><c> are</c><00:10:56.279><c> 15</c>

00:10:56.629 --> 00:10:56.639 align:start position:0%
queries per chunks so 15 there are 15
 

00:10:56.639 --> 00:10:59.509 align:start position:0%
queries per chunks so 15 there are 15
samples<00:10:57.079><c> here</c><00:10:57.519><c> with</c><00:10:57.680><c> see</c><00:10:58.160><c> answer</c><00:10:58.560><c> query</c>

00:10:59.509 --> 00:10:59.519 align:start position:0%
samples here with see answer query
 

00:10:59.519 --> 00:11:03.430 align:start position:0%
samples here with see answer query
context<00:10:59.959><c> Oracle</c><00:11:00.800><c> context</c><00:11:02.079><c> details</c><00:11:03.079><c> so</c><00:11:03.279><c> you</c>

00:11:03.430 --> 00:11:03.440 align:start position:0%
context Oracle context details so you
 

00:11:03.440 --> 00:11:08.069 align:start position:0%
context Oracle context details so you
can<00:11:04.200><c> inspect</c><00:11:05.200><c> the</c><00:11:05.399><c> first</c><00:11:06.000><c> uh</c><00:11:06.800><c> sample</c><00:11:07.800><c> so</c><00:11:07.959><c> you</c>

00:11:08.069 --> 00:11:08.079 align:start position:0%
can inspect the first uh sample so you
 

00:11:08.079 --> 00:11:09.550 align:start position:0%
can inspect the first uh sample so you
can<00:11:08.200><c> see</c><00:11:08.440><c> what</c><00:11:08.560><c> is</c><00:11:08.639><c> a</c><00:11:08.800><c> query</c><00:11:09.200><c> what</c><00:11:09.320><c> is</c><00:11:09.399><c> a</c>

00:11:09.550 --> 00:11:09.560 align:start position:0%
can see what is a query what is a
 

00:11:09.560 --> 00:11:12.870 align:start position:0%
can see what is a query what is a
context<00:11:10.079><c> and</c><00:11:10.720><c> then</c><00:11:11.720><c> uh</c><00:11:11.920><c> what</c><00:11:12.040><c> is</c><00:11:12.279><c> Oracle</c>

00:11:12.870 --> 00:11:12.880 align:start position:0%
context and then uh what is Oracle
 

00:11:12.880 --> 00:11:16.069 align:start position:0%
context and then uh what is Oracle
context<00:11:13.880><c> what</c><00:11:14.000><c> is</c><00:11:14.079><c> a</c><00:11:14.200><c> c</c><00:11:14.720><c> answer</c><00:11:15.120><c> and</c><00:11:15.360><c> all</c><00:11:15.600><c> those</c>

00:11:16.069 --> 00:11:16.079 align:start position:0%
context what is a c answer and all those
 

00:11:16.079 --> 00:11:19.190 align:start position:0%
context what is a c answer and all those
things<00:11:17.079><c> so</c><00:11:17.720><c> and</c><00:11:17.920><c> this</c><00:11:18.079><c> can</c><00:11:18.240><c> be</c><00:11:18.360><c> used</c><00:11:18.639><c> for</c><00:11:19.079><c> uh</c>

00:11:19.190 --> 00:11:19.200 align:start position:0%
things so and this can be used for uh
 

00:11:19.200 --> 00:11:23.190 align:start position:0%
things so and this can be used for uh
further<00:11:19.560><c> fine-tuning</c><00:11:20.160><c> purposes</c><00:11:21.639><c> so</c><00:11:22.639><c> I</c><00:11:22.800><c> hope</c>

00:11:23.190 --> 00:11:23.200 align:start position:0%
further fine-tuning purposes so I hope
 

00:11:23.200 --> 00:11:26.430 align:start position:0%
further fine-tuning purposes so I hope
uh<00:11:23.399><c> you</c><00:11:24.120><c> enjoyed</c><00:11:24.639><c> the</c><00:11:25.600><c> video</c><00:11:25.880><c> on</c><00:11:26.240><c> and</c>

00:11:26.430 --> 00:11:26.440 align:start position:0%
uh you enjoyed the video on and
 

00:11:26.440 --> 00:11:28.670 align:start position:0%
uh you enjoyed the video on and
understood<00:11:26.880><c> the</c><00:11:27.000><c> video</c><00:11:27.200><c> on</c><00:11:27.399><c> the</c><00:11:27.720><c> raft</c><00:11:28.320><c> Lama</c>

00:11:28.670 --> 00:11:28.680 align:start position:0%
understood the video on the raft Lama
 

00:11:28.680 --> 00:11:32.310 align:start position:0%
understood the video on the raft Lama
pack<00:11:29.639><c> uh</c><00:11:29.959><c> do</c><00:11:30.200><c> try</c><00:11:30.399><c> it</c><00:11:30.519><c> out</c><00:11:30.959><c> and</c><00:11:31.240><c> let</c><00:11:31.399><c> us</c><00:11:31.600><c> know</c><00:11:32.160><c> uh</c>

00:11:32.310 --> 00:11:32.320 align:start position:0%
pack uh do try it out and let us know uh
 

00:11:32.320 --> 00:11:36.150 align:start position:0%
pack uh do try it out and let us know uh
if<00:11:32.480><c> it</c><00:11:32.680><c> has</c><00:11:32.839><c> improved</c><00:11:33.320><c> your</c><00:11:33.760><c> uh</c><00:11:34.519><c> rag</c><00:11:35.160><c> Pipeline</c>

00:11:36.150 --> 00:11:36.160 align:start position:0%
if it has improved your uh rag Pipeline
 

00:11:36.160 --> 00:11:39.470 align:start position:0%
if it has improved your uh rag Pipeline
and<00:11:36.760><c> thank</c><00:11:36.959><c> you</c><00:11:37.360><c> and</c><00:11:37.519><c> see</c><00:11:37.720><c> you</c><00:11:37.920><c> in</c><00:11:38.040><c> the</c><00:11:38.200><c> next</c>

00:11:39.470 --> 00:11:39.480 align:start position:0%
and thank you and see you in the next
 

00:11:39.480 --> 00:11:42.480 align:start position:0%
and thank you and see you in the next
video

