WEBVTT
Kind: captions
Language: en

00:00:00.179 --> 00:00:02.810 align:start position:0%
 
hi<00:00:00.780><c> everyone</c><00:00:00.960><c> I'm</c><00:00:01.560><c> <PERSON><PERSON><PERSON></c><00:00:02.159><c> and</c><00:00:02.340><c> I</c><00:00:02.520><c> have</c><00:00:02.639><c> a</c><00:00:02.760><c> new</c>

00:00:02.810 --> 00:00:02.820 align:start position:0%
hi everyone I'm <PERSON><PERSON><PERSON> and I have a new
 

00:00:02.820 --> 00:00:05.570 align:start position:0%
hi everyone I'm <PERSON><PERSON><PERSON> and I have a new
GitHub<00:00:03.179><c> feature</c><00:00:03.540><c> for</c><00:00:03.780><c> you</c><00:00:03.899><c> to</c><00:00:04.319><c> check</c><00:00:04.500><c> out</c>

00:00:05.570 --> 00:00:05.580 align:start position:0%
GitHub feature for you to check out
 

00:00:05.580 --> 00:00:07.490 align:start position:0%
GitHub feature for you to check out
we've<00:00:06.120><c> been</c><00:00:06.240><c> making</c><00:00:06.480><c> a</c><00:00:06.779><c> lot</c><00:00:06.899><c> of</c><00:00:07.020><c> changes</c><00:00:07.379><c> to</c>

00:00:07.490 --> 00:00:07.500 align:start position:0%
we've been making a lot of changes to
 

00:00:07.500 --> 00:00:09.589 align:start position:0%
we've been making a lot of changes to
GitHub<00:00:07.859><c> projects</c><00:00:08.400><c> and</c><00:00:08.760><c> today</c><00:00:09.000><c> we</c><00:00:09.420><c> have</c>

00:00:09.589 --> 00:00:09.599 align:start position:0%
GitHub projects and today we have
 

00:00:09.599 --> 00:00:11.930 align:start position:0%
GitHub projects and today we have
product<00:00:09.900><c> manager</c><00:00:10.500><c> Josie</c><00:00:10.980><c> here</c><00:00:11.460><c> to</c><00:00:11.700><c> tell</c><00:00:11.820><c> us</c>

00:00:11.930 --> 00:00:11.940 align:start position:0%
product manager Josie here to tell us
 

00:00:11.940 --> 00:00:13.549 align:start position:0%
product manager Josie here to tell us
how<00:00:12.179><c> we</c><00:00:12.360><c> can</c><00:00:12.480><c> power</c><00:00:12.719><c> up</c><00:00:12.960><c> with</c><00:00:13.080><c> fields</c><00:00:13.380><c> and</c>

00:00:13.549 --> 00:00:13.559 align:start position:0%
how we can power up with fields and
 

00:00:13.559 --> 00:00:17.150 align:start position:0%
how we can power up with fields and
GitHub<00:00:13.860><c> projects</c><00:00:14.400><c> Josie</c><00:00:15.240><c> over</c><00:00:15.719><c> to</c><00:00:15.960><c> you</c><00:00:16.199><c> hi</c>

00:00:17.150 --> 00:00:17.160 align:start position:0%
GitHub projects Josie over to you hi
 

00:00:17.160 --> 00:00:19.250 align:start position:0%
GitHub projects Josie over to you hi
today<00:00:17.820><c> I'm</c><00:00:18.060><c> going</c><00:00:18.180><c> to</c><00:00:18.240><c> show</c><00:00:18.480><c> you</c><00:00:18.600><c> how</c><00:00:18.960><c> to</c><00:00:19.080><c> power</c>

00:00:19.250 --> 00:00:19.260 align:start position:0%
today I'm going to show you how to power
 

00:00:19.260 --> 00:00:21.890 align:start position:0%
today I'm going to show you how to power
up<00:00:19.500><c> your</c><00:00:19.740><c> project</c><00:00:19.920><c> with</c><00:00:20.340><c> Fields</c><00:00:20.820><c> Fields</c><00:00:21.779><c> can</c>

00:00:21.890 --> 00:00:21.900 align:start position:0%
up your project with Fields Fields can
 

00:00:21.900 --> 00:00:25.670 align:start position:0%
up your project with Fields Fields can
be<00:00:22.080><c> a</c><00:00:22.199><c> really</c><00:00:22.320><c> great</c><00:00:22.560><c> way</c><00:00:23.180><c> to</c><00:00:24.199><c> organize</c><00:00:25.199><c> and</c>

00:00:25.670 --> 00:00:25.680 align:start position:0%
be a really great way to organize and
 

00:00:25.680 --> 00:00:28.429 align:start position:0%
be a really great way to organize and
get<00:00:25.920><c> things</c><00:00:26.100><c> done</c><00:00:26.400><c> quickly</c><00:00:26.880><c> in</c><00:00:27.240><c> your</c><00:00:27.359><c> project</c>

00:00:28.429 --> 00:00:28.439 align:start position:0%
get things done quickly in your project
 

00:00:28.439 --> 00:00:30.290 align:start position:0%
get things done quickly in your project
here<00:00:28.980><c> I'm</c><00:00:29.160><c> going</c><00:00:29.340><c> to</c><00:00:29.400><c> create</c><00:00:29.519><c> a</c><00:00:29.760><c> new</c><00:00:29.820><c> field</c><00:00:30.000><c> by</c>

00:00:30.290 --> 00:00:30.300 align:start position:0%
here I'm going to create a new field by
 

00:00:30.300 --> 00:00:32.450 align:start position:0%
here I'm going to create a new field by
clicking<00:00:30.660><c> on</c><00:00:30.779><c> this</c><00:00:30.960><c> plus</c><00:00:31.199><c> sign</c>

00:00:32.450 --> 00:00:32.460 align:start position:0%
clicking on this plus sign
 

00:00:32.460 --> 00:00:36.770 align:start position:0%
clicking on this plus sign
creating<00:00:33.059><c> a</c><00:00:33.239><c> new</c><00:00:33.360><c> field</c><00:00:33.600><c> let's</c><00:00:34.140><c> say</c><00:00:34.380><c> size</c>

00:00:36.770 --> 00:00:36.780 align:start position:0%
creating a new field let's say size
 

00:00:36.780 --> 00:00:39.650 align:start position:0%
creating a new field let's say size
um<00:00:36.899><c> and</c><00:00:37.140><c> it's</c><00:00:37.320><c> going</c><00:00:37.500><c> to</c><00:00:37.620><c> be</c><00:00:37.739><c> a</c><00:00:38.340><c> single</c><00:00:38.700><c> select</c>

00:00:39.650 --> 00:00:39.660 align:start position:0%
um and it's going to be a single select
 

00:00:39.660 --> 00:00:44.209 align:start position:0%
um and it's going to be a single select
and<00:00:40.260><c> maybe</c><00:00:40.940><c> sizes</c><00:00:41.940><c> for</c><00:00:42.120><c> issues</c><00:00:42.480><c> include</c><00:00:42.960><c> small</c>

00:00:44.209 --> 00:00:44.219 align:start position:0%
and maybe sizes for issues include small
 

00:00:44.219 --> 00:00:47.810 align:start position:0%
and maybe sizes for issues include small
medium<00:00:45.000><c> large</c>

00:00:47.810 --> 00:00:47.820 align:start position:0%
medium large
 

00:00:47.820 --> 00:00:49.369 align:start position:0%
medium large
I'm<00:00:48.180><c> going</c><00:00:48.360><c> to</c><00:00:48.420><c> add</c><00:00:48.600><c> those</c><00:00:48.840><c> and</c><00:00:49.140><c> then</c><00:00:49.260><c> I'm</c>

00:00:49.369 --> 00:00:49.379 align:start position:0%
I'm going to add those and then I'm
 

00:00:49.379 --> 00:00:51.410 align:start position:0%
I'm going to add those and then I'm
going<00:00:49.500><c> to</c><00:00:49.559><c> click</c><00:00:49.980><c> save</c>

00:00:51.410 --> 00:00:51.420 align:start position:0%
going to click save
 

00:00:51.420 --> 00:00:54.889 align:start position:0%
going to click save
from<00:00:51.899><c> here</c><00:00:52.140><c> I</c><00:00:52.440><c> can</c><00:00:52.559><c> go</c><00:00:52.680><c> to</c><00:00:52.860><c> the</c><00:00:53.039><c> settings</c><00:00:53.600><c> and</c><00:00:54.600><c> I</c>

00:00:54.889 --> 00:00:54.899 align:start position:0%
from here I can go to the settings and I
 

00:00:54.899 --> 00:00:58.310 align:start position:0%
from here I can go to the settings and I
can<00:00:55.020><c> see</c><00:00:55.260><c> that</c><00:00:55.559><c> size</c><00:00:55.860><c> is</c><00:00:56.520><c> here</c><00:00:56.879><c> and</c><00:00:57.780><c> let's</c><00:00:58.020><c> say</c>

00:00:58.310 --> 00:00:58.320 align:start position:0%
can see that size is here and let's say
 

00:00:58.320 --> 00:01:01.369 align:start position:0%
can see that size is here and let's say
I<00:00:58.860><c> want</c><00:00:58.980><c> to</c><00:00:59.100><c> edit</c><00:00:59.340><c> the</c><00:00:59.640><c> colors</c><00:01:00.180><c> I</c><00:01:01.020><c> want</c><00:01:01.140><c> to</c><00:01:01.260><c> add</c>

00:01:01.369 --> 00:01:01.379 align:start position:0%
I want to edit the colors I want to add
 

00:01:01.379 --> 00:01:02.770 align:start position:0%
I want to edit the colors I want to add
a<00:01:01.500><c> little</c><00:01:01.620><c> description</c>

00:01:02.770 --> 00:01:02.780 align:start position:0%
a little description
 

00:01:02.780 --> 00:01:04.630 align:start position:0%
a little description
please

00:01:04.630 --> 00:01:04.640 align:start position:0%
please
 

00:01:04.640 --> 00:01:11.990 align:start position:0%
please
issues<00:01:05.780><c> can</c><00:01:06.780><c> be</c><00:01:07.939><c> resolved</c><00:01:08.960><c> in</c><00:01:09.960><c> a</c><00:01:10.260><c> few</c><00:01:10.500><c> hours</c>

00:01:11.990 --> 00:01:12.000 align:start position:0%
issues can be resolved in a few hours
 

00:01:12.000 --> 00:01:14.270 align:start position:0%
issues can be resolved in a few hours
now<00:01:12.659><c> what</c><00:01:12.900><c> I</c><00:01:13.020><c> can</c><00:01:13.140><c> also</c><00:01:13.439><c> do</c><00:01:13.619><c> here</c><00:01:13.920><c> in</c><00:01:14.159><c> the</c>

00:01:14.270 --> 00:01:14.280 align:start position:0%
now what I can also do here in the
 

00:01:14.280 --> 00:01:17.149 align:start position:0%
now what I can also do here in the
settings<00:01:14.460><c> page</c><00:01:14.939><c> is</c><00:01:15.479><c> reorder</c><00:01:16.080><c> them</c><00:01:16.260><c> so</c><00:01:16.920><c> let's</c>

00:01:17.149 --> 00:01:17.159 align:start position:0%
settings page is reorder them so let's
 

00:01:17.159 --> 00:01:19.730 align:start position:0%
settings page is reorder them so let's
say<00:01:17.460><c> status</c><00:01:18.119><c> makes</c><00:01:18.540><c> sense</c><00:01:18.780><c> as</c><00:01:19.020><c> the</c><00:01:19.260><c> top</c><00:01:19.439><c> field</c>

00:01:19.730 --> 00:01:19.740 align:start position:0%
say status makes sense as the top field
 

00:01:19.740 --> 00:01:22.630 align:start position:0%
say status makes sense as the top field
then<00:01:20.460><c> priority</c><00:01:21.000><c> and</c><00:01:21.600><c> maybe</c><00:01:21.780><c> I</c><00:01:22.020><c> want</c><00:01:22.140><c> to</c><00:01:22.259><c> bring</c>

00:01:22.630 --> 00:01:22.640 align:start position:0%
then priority and maybe I want to bring
 

00:01:22.640 --> 00:01:26.630 align:start position:0%
then priority and maybe I want to bring
estimate<00:01:23.640><c> up</c><00:01:23.880><c> what</c><00:01:24.659><c> this</c><00:01:24.840><c> will</c><00:01:25.020><c> do</c><00:01:25.259><c> is</c><00:01:25.920><c> on</c><00:01:26.520><c> the</c>

00:01:26.630 --> 00:01:26.640 align:start position:0%
estimate up what this will do is on the
 

00:01:26.640 --> 00:01:28.969 align:start position:0%
estimate up what this will do is on the
issue<00:01:27.060><c> itself</c><00:01:27.659><c> when</c><00:01:28.080><c> you</c><00:01:28.259><c> open</c><00:01:28.500><c> the</c><00:01:28.799><c> side</c>

00:01:28.969 --> 00:01:28.979 align:start position:0%
issue itself when you open the side
 

00:01:28.979 --> 00:01:31.670 align:start position:0%
issue itself when you open the side
panel<00:01:29.400><c> it'll</c><00:01:29.820><c> rearrange</c><00:01:30.479><c> the</c><00:01:30.960><c> custom</c><00:01:31.140><c> fields</c>

00:01:31.670 --> 00:01:31.680 align:start position:0%
panel it'll rearrange the custom fields
 

00:01:31.680 --> 00:01:33.410 align:start position:0%
panel it'll rearrange the custom fields
that<00:01:31.799><c> you've</c><00:01:32.040><c> created</c><00:01:32.280><c> in</c><00:01:32.460><c> projects</c><00:01:33.000><c> right</c>

00:01:33.410 --> 00:01:33.420 align:start position:0%
that you've created in projects right
 

00:01:33.420 --> 00:01:37.010 align:start position:0%
that you've created in projects right
here<00:01:33.659><c> I</c><00:01:34.439><c> can</c><00:01:34.619><c> also</c><00:01:35.159><c> sort</c><00:01:35.939><c> by</c><00:01:36.479><c> multiple</c><00:01:36.840><c> field</c>

00:01:37.010 --> 00:01:37.020 align:start position:0%
here I can also sort by multiple field
 

00:01:37.020 --> 00:01:39.950 align:start position:0%
here I can also sort by multiple field
options<00:01:37.560><c> at</c><00:01:38.100><c> once</c><00:01:38.400><c> by</c><00:01:39.240><c> clicking</c><00:01:39.600><c> on</c><00:01:39.780><c> this</c>

00:01:39.950 --> 00:01:39.960 align:start position:0%
options at once by clicking on this
 

00:01:39.960 --> 00:01:41.149 align:start position:0%
options at once by clicking on this
Arrow

00:01:41.149 --> 00:01:41.159 align:start position:0%
Arrow
 

00:01:41.159 --> 00:01:43.249 align:start position:0%
Arrow
selecting<00:01:41.939><c> sort</c><00:01:42.240><c> by</c>

00:01:43.249 --> 00:01:43.259 align:start position:0%
selecting sort by
 

00:01:43.259 --> 00:01:44.710 align:start position:0%
selecting sort by
I<00:01:43.740><c> can</c><00:01:43.920><c> sort</c>

00:01:44.710 --> 00:01:44.720 align:start position:0%
I can sort
 

00:01:44.720 --> 00:01:48.469 align:start position:0%
I can sort
Maybe<00:01:45.720><c> by</c><00:01:46.020><c> status</c>

00:01:48.469 --> 00:01:48.479 align:start position:0%
 
 

00:01:48.479 --> 00:01:50.510 align:start position:0%
 
and<00:01:48.900><c> if</c><00:01:49.020><c> I</c><00:01:49.140><c> want</c><00:01:49.259><c> to</c><00:01:49.380><c> select</c><00:01:49.680><c> a</c><00:01:49.799><c> secondary</c><00:01:50.159><c> sort</c>

00:01:50.510 --> 00:01:50.520 align:start position:0%
and if I want to select a secondary sort
 

00:01:50.520 --> 00:01:53.090 align:start position:0%
and if I want to select a secondary sort
I<00:01:50.880><c> can</c><00:01:51.000><c> hold</c><00:01:51.119><c> option</c><00:01:51.659><c> or</c><00:01:52.140><c> the</c><00:01:52.320><c> ALT</c><00:01:52.619><c> key</c><00:01:52.740><c> on</c><00:01:52.920><c> my</c>

00:01:53.090 --> 00:01:53.100 align:start position:0%
I can hold option or the ALT key on my
 

00:01:53.100 --> 00:01:56.090 align:start position:0%
I can hold option or the ALT key on my
keyboard<00:01:53.340><c> and</c><00:01:54.240><c> select</c><00:01:54.540><c> priority</c>

00:01:56.090 --> 00:01:56.100 align:start position:0%
keyboard and select priority
 

00:01:56.100 --> 00:01:58.969 align:start position:0%
keyboard and select priority
now<00:01:56.579><c> you</c><00:01:56.820><c> can</c><00:01:56.939><c> see</c><00:01:57.240><c> that</c><00:01:57.720><c> these</c><00:01:58.259><c> issues</c><00:01:58.680><c> are</c>

00:01:58.969 --> 00:01:58.979 align:start position:0%
now you can see that these issues are
 

00:01:58.979 --> 00:02:02.510 align:start position:0%
now you can see that these issues are
sorted<00:01:59.399><c> by</c><00:01:59.820><c> status</c><00:02:00.299><c> first</c><00:02:00.960><c> and</c><00:02:01.920><c> then</c><00:02:02.040><c> priority</c>

00:02:02.510 --> 00:02:02.520 align:start position:0%
sorted by status first and then priority
 

00:02:02.520 --> 00:02:04.550 align:start position:0%
sorted by status first and then priority
second

00:02:04.550 --> 00:02:04.560 align:start position:0%
second
 

00:02:04.560 --> 00:02:08.449 align:start position:0%
second
now<00:02:05.040><c> let's</c><00:02:05.219><c> say</c><00:02:05.460><c> I</c><00:02:06.000><c> want</c><00:02:06.299><c> to</c><00:02:06.840><c> update</c><00:02:07.619><c> all</c><00:02:08.340><c> of</c>

00:02:08.449 --> 00:02:08.459 align:start position:0%
now let's say I want to update all of
 

00:02:08.459 --> 00:02:11.330 align:start position:0%
now let's say I want to update all of
these<00:02:08.759><c> issues</c><00:02:09.300><c> they're</c><00:02:10.259><c> not</c><00:02:10.440><c> exactly</c><00:02:11.160><c> urgent</c>

00:02:11.330 --> 00:02:11.340 align:start position:0%
these issues they're not exactly urgent
 

00:02:11.340 --> 00:02:13.490 align:start position:0%
these issues they're not exactly urgent
maybe<00:02:12.120><c> they're</c><00:02:12.420><c> just</c><00:02:12.660><c> High</c>

00:02:13.490 --> 00:02:13.500 align:start position:0%
maybe they're just High
 

00:02:13.500 --> 00:02:17.030 align:start position:0%
maybe they're just High
I<00:02:14.099><c> can</c><00:02:14.220><c> drag</c><00:02:14.520><c> and</c><00:02:15.420><c> update</c><00:02:15.840><c> multiple</c><00:02:16.500><c> cells</c><00:02:16.920><c> at</c>

00:02:17.030 --> 00:02:17.040 align:start position:0%
I can drag and update multiple cells at
 

00:02:17.040 --> 00:02:18.410 align:start position:0%
I can drag and update multiple cells at
once

00:02:18.410 --> 00:02:18.420 align:start position:0%
once
 

00:02:18.420 --> 00:02:20.809 align:start position:0%
once
you<00:02:18.959><c> can</c><00:02:19.080><c> also</c><00:02:19.379><c> update</c><00:02:19.800><c> multiple</c><00:02:20.220><c> cells</c><00:02:20.640><c> At</c>

00:02:20.809 --> 00:02:20.819 align:start position:0%
you can also update multiple cells At
 

00:02:20.819 --> 00:02:23.330 align:start position:0%
you can also update multiple cells At
Once<00:02:21.060><c> by</c><00:02:21.540><c> selecting</c><00:02:21.900><c> the</c><00:02:22.200><c> command</c><00:02:22.500><c> or</c><00:02:23.160><c> the</c>

00:02:23.330 --> 00:02:23.340 align:start position:0%
Once by selecting the command or the
 

00:02:23.340 --> 00:02:24.890 align:start position:0%
Once by selecting the command or the
control<00:02:23.520><c> key</c>

00:02:24.890 --> 00:02:24.900 align:start position:0%
control key
 

00:02:24.900 --> 00:02:27.470 align:start position:0%
control key
so<00:02:25.440><c> let's</c><00:02:25.860><c> say</c><00:02:26.099><c> I'm</c><00:02:26.580><c> going</c><00:02:26.760><c> to</c><00:02:26.879><c> copy</c><00:02:27.239><c> this</c>

00:02:27.470 --> 00:02:27.480 align:start position:0%
so let's say I'm going to copy this
 

00:02:27.480 --> 00:02:30.589 align:start position:0%
so let's say I'm going to copy this
assignee<00:02:28.080><c> and</c><00:02:28.620><c> I'm</c><00:02:28.739><c> going</c><00:02:28.860><c> to</c><00:02:28.920><c> assign</c><00:02:29.280><c> them</c><00:02:29.599><c> to</c>

00:02:30.589 --> 00:02:30.599 align:start position:0%
assignee and I'm going to assign them to
 

00:02:30.599 --> 00:02:32.930 align:start position:0%
assignee and I'm going to assign them to
these<00:02:31.260><c> three</c><00:02:31.440><c> issues</c>

00:02:32.930 --> 00:02:32.940 align:start position:0%
these three issues
 

00:02:32.940 --> 00:02:36.290 align:start position:0%
these three issues
so<00:02:33.780><c> that's</c><00:02:33.959><c> control</c><00:02:34.379><c> C</c><00:02:34.860><c> or</c><00:02:35.220><c> command</c><00:02:35.580><c> C</c><00:02:35.879><c> for</c>

00:02:36.290 --> 00:02:36.300 align:start position:0%
so that's control C or command C for
 

00:02:36.300 --> 00:02:40.729 align:start position:0%
so that's control C or command C for
copy<00:02:36.860><c> and</c><00:02:37.860><c> control</c><00:02:38.040><c> V</c><00:02:38.459><c> for</c><00:02:38.879><c> paste</c>

00:02:40.729 --> 00:02:40.739 align:start position:0%
copy and control V for paste
 

00:02:40.739 --> 00:02:43.070 align:start position:0%
copy and control V for paste
and<00:02:41.040><c> that's</c><00:02:41.160><c> how</c><00:02:41.400><c> you</c><00:02:41.519><c> power</c><00:02:41.700><c> up</c><00:02:41.940><c> with</c><00:02:42.120><c> fields</c>

00:02:43.070 --> 00:02:43.080 align:start position:0%
and that's how you power up with fields
 

00:02:43.080 --> 00:02:44.089 align:start position:0%
and that's how you power up with fields
if

00:02:44.089 --> 00:02:44.099 align:start position:0%
if
 

00:02:44.099 --> 00:02:46.430 align:start position:0%
if
it's<00:02:44.340><c> a</c><00:02:44.340><c> rename</c><00:02:44.760><c> a</c><00:02:45.000><c> field</c><00:02:45.180><c> how</c><00:02:45.840><c> would</c><00:02:46.019><c> I</c><00:02:46.200><c> do</c>

00:02:46.430 --> 00:02:46.440 align:start position:0%
it's a rename a field how would I do
 

00:02:46.440 --> 00:02:49.610 align:start position:0%
it's a rename a field how would I do
that<00:02:46.680><c> if</c><00:02:47.280><c> I</c><00:02:47.459><c> want</c><00:02:47.519><c> to</c><00:02:47.700><c> rename</c><00:02:48.060><c> a</c><00:02:48.300><c> field</c><00:02:48.540><c> I</c><00:02:49.500><c> can</c>

00:02:49.610 --> 00:02:49.620 align:start position:0%
that if I want to rename a field I can
 

00:02:49.620 --> 00:02:52.729 align:start position:0%
that if I want to rename a field I can
go<00:02:49.800><c> here</c><00:02:50.160><c> and</c><00:02:50.940><c> say</c><00:02:51.360><c> maybe</c><00:02:52.200><c> this</c><00:02:52.440><c> is</c><00:02:52.560><c> not</c>

00:02:52.729 --> 00:02:52.739 align:start position:0%
go here and say maybe this is not
 

00:02:52.739 --> 00:02:58.850 align:start position:0%
go here and say maybe this is not
priority<00:02:53.220><c> but</c><00:02:53.580><c> this</c><00:02:53.819><c> is</c><00:02:54.200><c> super</c><00:02:55.200><c> priority</c>

00:02:58.850 --> 00:02:58.860 align:start position:0%
 
 

00:02:58.860 --> 00:03:00.890 align:start position:0%
 
and<00:02:59.400><c> that's</c><00:02:59.519><c> how</c><00:02:59.760><c> I</c><00:02:59.940><c> would</c><00:03:00.060><c> update</c><00:03:00.480><c> the</c><00:03:00.720><c> field</c>

00:03:00.890 --> 00:03:00.900 align:start position:0%
and that's how I would update the field
 

00:03:00.900 --> 00:03:05.449 align:start position:0%
and that's how I would update the field
name<00:03:01.680><c> I</c><00:03:02.220><c> can</c><00:03:02.280><c> also</c><00:03:02.519><c> do</c><00:03:02.700><c> that</c><00:03:02.879><c> in</c><00:03:03.120><c> settings</c>

00:03:05.449 --> 00:03:05.459 align:start position:0%
 
 

00:03:05.459 --> 00:03:07.729 align:start position:0%
 
Fields<00:03:05.940><c> can</c><00:03:06.060><c> we</c><00:03:06.239><c> created</c><00:03:06.599><c> projects</c><00:03:07.260><c> is</c><00:03:07.500><c> there</c>

00:03:07.729 --> 00:03:07.739 align:start position:0%
Fields can we created projects is there
 

00:03:07.739 --> 00:03:10.790 align:start position:0%
Fields can we created projects is there
currently<00:03:08.099><c> a</c><00:03:08.280><c> limit</c><00:03:08.840><c> there</c><00:03:09.840><c> are</c><00:03:10.019><c> limits</c><00:03:10.500><c> you</c>

00:03:10.790 --> 00:03:10.800 align:start position:0%
currently a limit there are limits you
 

00:03:10.800 --> 00:03:13.190 align:start position:0%
currently a limit there are limits you
can<00:03:10.920><c> create</c><00:03:11.099><c> about</c><00:03:11.519><c> 20</c><00:03:11.879><c> at</c><00:03:12.239><c> this</c><00:03:12.420><c> time</c>

00:03:13.190 --> 00:03:13.200 align:start position:0%
can create about 20 at this time
 

00:03:13.200 --> 00:03:15.350 align:start position:0%
can create about 20 at this time
okay<00:03:13.379><c> last</c><00:03:13.800><c> question</c><00:03:14.040><c> are</c><00:03:14.580><c> we</c><00:03:14.700><c> able</c><00:03:14.940><c> to</c><00:03:15.060><c> sort</c>

00:03:15.350 --> 00:03:15.360 align:start position:0%
okay last question are we able to sort
 

00:03:15.360 --> 00:03:18.949 align:start position:0%
okay last question are we able to sort
by<00:03:15.900><c> more</c><00:03:16.560><c> than</c><00:03:16.860><c> two</c><00:03:17.400><c> fields</c><00:03:17.760><c> at</c><00:03:17.940><c> a</c><00:03:18.060><c> time</c><00:03:18.239><c> for</c>

00:03:18.949 --> 00:03:18.959 align:start position:0%
by more than two fields at a time for
 

00:03:18.959 --> 00:03:21.170 align:start position:0%
by more than two fields at a time for
now<00:03:19.140><c> you</c><00:03:19.739><c> can</c><00:03:19.800><c> sort</c><00:03:19.980><c> by</c><00:03:20.220><c> two</c><00:03:20.459><c> but</c><00:03:20.819><c> we're</c>

00:03:21.170 --> 00:03:21.180 align:start position:0%
now you can sort by two but we're
 

00:03:21.180 --> 00:03:23.210 align:start position:0%
now you can sort by two but we're
looking<00:03:21.360><c> into</c><00:03:21.659><c> whether</c><00:03:22.080><c> folks</c><00:03:22.620><c> would</c><00:03:22.860><c> like</c><00:03:23.040><c> to</c>

00:03:23.210 --> 00:03:23.220 align:start position:0%
looking into whether folks would like to
 

00:03:23.220 --> 00:03:25.309 align:start position:0%
looking into whether folks would like to
be<00:03:23.340><c> able</c><00:03:23.400><c> to</c><00:03:23.580><c> sort</c><00:03:23.760><c> by</c><00:03:24.060><c> more</c><00:03:24.360><c> thanks</c><00:03:25.019><c> so</c><00:03:25.200><c> much</c>

00:03:25.309 --> 00:03:25.319 align:start position:0%
be able to sort by more thanks so much
 

00:03:25.319 --> 00:03:27.530 align:start position:0%
be able to sort by more thanks so much
Josie<00:03:25.800><c> for</c><00:03:26.040><c> showing</c><00:03:26.340><c> us</c><00:03:26.519><c> all</c><00:03:26.819><c> about</c><00:03:27.000><c> how</c><00:03:27.360><c> we</c>

00:03:27.530 --> 00:03:27.540 align:start position:0%
Josie for showing us all about how we
 

00:03:27.540 --> 00:03:30.350 align:start position:0%
Josie for showing us all about how we
can<00:03:27.720><c> use</c><00:03:28.080><c> fields</c><00:03:28.560><c> and</c><00:03:28.739><c> GitHub</c><00:03:29.040><c> projects</c><00:03:30.120><c> if</c>

00:03:30.350 --> 00:03:30.360 align:start position:0%
can use fields and GitHub projects if
 

00:03:30.360 --> 00:03:32.149 align:start position:0%
can use fields and GitHub projects if
you<00:03:30.480><c> have</c><00:03:30.659><c> questions</c><00:03:30.840><c> about</c><00:03:31.140><c> this</c><00:03:31.440><c> feature</c><00:03:31.800><c> be</c>

00:03:32.149 --> 00:03:32.159 align:start position:0%
you have questions about this feature be
 

00:03:32.159 --> 00:03:33.770 align:start position:0%
you have questions about this feature be
sure<00:03:32.280><c> to</c><00:03:32.400><c> check</c><00:03:32.519><c> out</c><00:03:32.700><c> our</c><00:03:33.000><c> GitHub</c><00:03:33.420><c> Community</c>

00:03:33.770 --> 00:03:33.780 align:start position:0%
sure to check out our GitHub Community
 

00:03:33.780 --> 00:03:35.990 align:start position:0%
sure to check out our GitHub Community
discussion<00:03:34.500><c> board</c><00:03:34.680><c> or</c><00:03:35.459><c> you</c><00:03:35.580><c> can</c><00:03:35.700><c> leave</c><00:03:35.819><c> a</c>

00:03:35.990 --> 00:03:36.000 align:start position:0%
discussion board or you can leave a
 

00:03:36.000 --> 00:03:37.729 align:start position:0%
discussion board or you can leave a
comment<00:03:36.300><c> down</c><00:03:36.480><c> below</c><00:03:36.720><c> to</c><00:03:37.080><c> let</c><00:03:37.200><c> us</c><00:03:37.319><c> know</c><00:03:37.500><c> what</c>

00:03:37.729 --> 00:03:37.739 align:start position:0%
comment down below to let us know what
 

00:03:37.739 --> 00:03:40.309 align:start position:0%
comment down below to let us know what
you<00:03:37.920><c> think</c><00:03:38.099><c> be</c><00:03:38.700><c> sure</c><00:03:38.879><c> to</c><00:03:39.060><c> like</c><00:03:39.300><c> this</c><00:03:39.540><c> video</c><00:03:39.780><c> And</c>

00:03:40.309 --> 00:03:40.319 align:start position:0%
you think be sure to like this video And
 

00:03:40.319 --> 00:03:42.890 align:start position:0%
you think be sure to like this video And
subscribe<00:03:40.799><c> for</c><00:03:41.220><c> future</c><00:03:41.459><c> videos</c><00:03:41.819><c> and</c><00:03:42.659><c> stay</c>

00:03:42.890 --> 00:03:42.900 align:start position:0%
subscribe for future videos and stay
 

00:03:42.900 --> 00:03:44.330 align:start position:0%
subscribe for future videos and stay
tuned<00:03:43.140><c> for</c><00:03:43.260><c> more</c><00:03:43.379><c> checkout</c><00:03:43.739><c> videos</c><00:03:43.980><c> coming</c>

00:03:44.330 --> 00:03:44.340 align:start position:0%
tuned for more checkout videos coming
 

00:03:44.340 --> 00:03:47.540 align:start position:0%
tuned for more checkout videos coming
your<00:03:44.580><c> way</c><00:03:44.760><c> very</c><00:03:45.239><c> soon</c>

