import os
import asyncio
import json
import time
from datetime import datetime, timedelta, timezone
from groq import Groq
from supabase import create_client, Client
from supabase.client import ClientOptions
from tenacity import retry, stop_after_attempt, wait_exponential
import tiktoken
from dotenv import load_dotenv
import logging
from collections import deque
from llama31_promptsV3 import prompt_template
import google.generativeai as genai
import grpc._cython.cygrpc as _cygrpc
from grpc import aio

load_dotenv()

# Constants
ROWS_PER_TABLE = 10000
MAX_CONCURRENT_REQUESTS = 6
MAX_PROCESSING_ATTEMPTS = 4
MAX_PROMPT_TOKENS = 1000000  # 1 million tokens for Gemini input
SAFETY_MARGIN_TOKENS = 1000
MAX_TOKENS_PER_MINUTE = 110000
MAX_TOKENS_PER_DAY = 900000
MAX_REQUESTS_PER_MINUTE = 30

tables_to_process = [
    "youtube_artificial_intelligence", "youtube_renewable_energy", "youtube_gme",
"youtube_sustainability", "youtube_startups", "youtube_financial_markets",
"youtube_general", "youtube_legal"

]

"""
"youtube_artificial_intelligence", "youtube_renewable_energy", "youtube_gme",
"youtube_sustainability", "youtube_startups", "youtube_financial_markets",
"youtube_general", "youtube_legal"
"""



# Define schemas
initial_response_schema = {
    "type": "object",
    "properties": {
        "keywords": {
            "type": "array",
            "items": {"type": "string"}
        },
        "questions": {
            "type": "array",
            "items": {"type": "string"}
        },
        "insights": {
            "type": "array",
            "items": {"type": "string"}
        },
        "recommendations": {
            "type": "array",
            "items": {"type": "string"}
        }
    },
    "required": ["keywords", "questions", "insights", "recommendations"]
}

# Initialize clients
supabase: Client = create_client(
    os.environ.get("SUPABASE_URL"),
    os.environ.get("SERVICE_ROLE_KEY"),
    options=ClientOptions(
        schema="public",
        headers={},
        auto_refresh_token=True,
        persist_session=True,
        storage=None,
        realtime=None,
        postgrest_client_timeout=3600,
        storage_client_timeout=3600,
        flow_type=None
    )
)

# Disable Supabase HTTP request logging
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('httpcore').setLevel(logging.WARNING)

# Token counting and rate limiting
token_usage_queue = deque()
request_timestamps = deque()
daily_token_usage = 0
daily_token_reset_time = time.time()

# Update Gemini configuration
genai.configure(api_key=os.environ.get("GOOGLE_API_KEY"))
gemini_model = genai.GenerativeModel('gemini-1.5-flash-002',
    generation_config=genai.types.GenerationConfig(
        max_output_tokens=8192,
        temperature=0.05,
        top_p=0.95,
        top_k=40,
        response_mime_type="application/json"
    )
)

# Function to count tokens
def count_tokens(text):
    encoding = tiktoken.get_encoding("cl100k_base")
    return len(encoding.encode(text))

# Function to truncate the transcript
def truncate_transcript(transcript, max_tokens, additional_text=""):
    max_tokens_with_margin = max_tokens - SAFETY_MARGIN_TOKENS - count_tokens(additional_text)
    tokens = count_tokens(transcript)
    if tokens <= max_tokens_with_margin:
        return transcript
    else:
        truncated_transcript = transcript[:max_tokens_with_margin * 4]  # Approximate char to token ratio
        while count_tokens(truncated_transcript) > max_tokens_with_margin:
            truncated_transcript = truncated_transcript[:-1]
        return truncated_transcript

# Add this function to handle Gemini responses
async def query_gemini(prompt, response_schema):
    """Query Gemini model and ensure response matches our schema"""
    try:
        # Query the Gemini model
        response = await gemini_model.generate_content_async(prompt)
        
        # Check if response was blocked by safety filters
        if not response.candidates or not response.candidates[0].content:
            raise ValueError("Response blocked by safety filters")
            
        # Extract the response text
        text = response.text
        
        # Parse the JSON response
        try:
            # First try to parse as pure JSON
            response_data = json.loads(text)
        except json.JSONDecodeError:
            # If that fails, try to extract JSON from the text
            start_idx = text.find('{')
            end_idx = text.rfind('}') + 1
            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON found in response")
            json_str = text[start_idx:end_idx]
            response_data = json.loads(json_str)
        
        # Validate required fields
        required_fields = ["keywords", "questions", "insights", "recommendations"]
        for field in required_fields:
            if field not in response_data:
                raise ValueError(f"Missing required field: {field}")
        
        # Validate field types
        if not isinstance(response_data["keywords"], list):
            raise ValueError("Keywords must be a list")
        if not isinstance(response_data["questions"], list):
            raise ValueError("Questions must be a list")
        if not isinstance(response_data["insights"], list):
            raise ValueError("Insights must be a list")
        if not isinstance(response_data["recommendations"], list):
            raise ValueError("Recommendations must be a list")
            
        return response_data
            
    except Exception as e:
        print(f"Error in Gemini query: {str(e)}")
        raise

# Add these helper functions after the existing imports
def sanitize_text(text):
    """Sanitize text to ensure UTF-8 compatibility"""
    if not isinstance(text, str):
        return str(text)
    try:
        # Try to encode then decode to catch any invalid characters
        return text.encode('utf-8', errors='ignore').decode('utf-8')
    except UnicodeError:
        return text.encode('ascii', errors='ignore').decode('ascii')

# Add response validation function
def validate_llm_response(response_data, video_id):
    """Validate the LLM response matches our database schema"""
    try:
        # Required keys
        required_keys = {"keywords", "questions", "insights", "recommendations"}
        actual_keys = set(response_data.keys())
        
        # Check for missing required keys
        if not required_keys.issubset(actual_keys):
            missing_keys = required_keys - actual_keys
            print(f"Missing required keys in response for video {video_id}: {missing_keys}")
            return False
            
        # Validate types and non-empty values
        if not isinstance(response_data["keywords"], list) or not response_data["keywords"]:
            print(f"Invalid or empty keywords for video {video_id}")
            return False
            
        if not isinstance(response_data["questions"], list) or not response_data["questions"]:
            print(f"Invalid or empty questions for video {video_id}")
            return False
            
        if not isinstance(response_data["insights"], list) or not response_data["insights"]:
            print(f"Invalid or empty insights for video {video_id}")
            return False
            
        if not isinstance(response_data["recommendations"], list) or not response_data["recommendations"]:
            print(f"Invalid or empty recommendations for video {video_id}")
            return False
                
        return True
    except Exception as e:
        print(f"Error validating response for video {video_id}: {str(e)}")
        return False

# Add near the top with other constants
PROCESSING_STATUS = {
    'PENDING': 'pending',
    'COMPLETED': 'completed',
    'IMPOSSIBLE': 'impossible'
}
# Function to process a row
async def process_row(row, table_name):
    if row['processed'] == "completed":
        print(f"Skipping already processed video: {sanitize_text(row['title'])}")
        return {'processed': "completed"}

    if row['transcript'] != "Transcript not available":
        for attempt in range(MAX_PROCESSING_ATTEMPTS):
            try:
                # Sanitize text inputs
                sanitized_transcript = sanitize_text(row['transcript'])
                sanitized_channel = sanitize_text(row['channel_name'])
                sanitized_title = sanitize_text(row['title'])
                
                truncated_transcript = truncate_transcript(sanitized_transcript, MAX_PROMPT_TOKENS)
                prompt = prompt_template.format(
                    channel_name=sanitized_channel,
                    title=sanitized_title,
                    published_at=row['published_at'],
                    transcript=truncated_transcript
                )
                
                print(f"\nProcessing video: {sanitized_title}")
                print(f"Video ID: {row['video_id']}")
                print(f"Attempt: {attempt + 1}")
                
                llm_response = await query_gemini(prompt, initial_response_schema)
                
                # Validate the response
                if not validate_llm_response(llm_response, row['video_id']):
                    raise ValueError("Invalid LLM response structure")
                
                # Update the database with the validated response
                update_data = {
                    "summary": "\n".join(llm_response["insights"]),  # Convert insights to summary
                    "keywords": llm_response["keywords"],
                    "visual_description": "\n".join(llm_response["recommendations"]),  # Use recommendations as visual description
                    "llm_response": llm_response,  # Store the full response as JSONB
                    "llm_call_date": datetime.now(timezone.utc).isoformat(),
                    "processed": "completed"
                }
                
                supabase.table(table_name).update(update_data).eq("id", row['id']).execute()
                
                print(f"Successfully processed video_id: {row['video_id']}")
                return {'processed': "completed"}
            
            except Exception as e:
                print(f"Error processing video_id: {row['video_id']} (Attempt {attempt + 1}): {str(e)}")
                if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                    print(f"Failed to process video_id: {row['video_id']} after {MAX_PROCESSING_ATTEMPTS} attempts")
                    # Leave as pending, don't update anything else
                    return {'processed': "pending"}
                continue
    else:
        print(f"Skipping video with no transcript: {row['title']}")
        return {'processed': "pending"}

# Function to get the timestamp for 72 hours ago (REMOVED for V9 - kept function definition but it's unused)
def get_72_hours_ago():
    date_72_hours_ago = datetime.now(timezone.utc) - timedelta(hours=72) 
    return date_72_hours_ago.isoformat()

# Modify the cleanup function
async def cleanup():
    """Gracefully cleanup resources"""
    try:
        # Cancel all pending tasks except the current one
        current_task = asyncio.current_task()
        pending = [task for task in asyncio.all_tasks() 
                  if task is not current_task]
        
        for task in pending:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                print(f"Error cancelling task: {e}")
        
        # Properly shutdown gRPC
        try:
            if hasattr(_cygrpc, '_actual_aio_shutdown'):
                _cygrpc._actual_aio_shutdown = lambda: None
        except Exception:
            pass
                
    except Exception as e:
        print(f"Cleanup error: {e}")

# Add this cleanup function
async def cleanup_grpc():
    """Gracefully cleanup gRPC resources"""
    try:
        # Patch the problematic shutdown function
        if hasattr(_cygrpc, '_actual_aio_shutdown'):
            _cygrpc._actual_aio_shutdown = lambda: None
            
        # Cancel all pending tasks with a longer timeout
        pending = asyncio.all_tasks()
        for task in pending:
            if not task.done():
                task.cancel()
                try:
                    await asyncio.wait_for(task, timeout=2.0)
                except (asyncio.CancelledError, asyncio.TimeoutError):
                    pass
                    
        # Add a small delay before final shutdown
        await asyncio.sleep(0.5)
        
    except Exception as e:
        print(f"gRPC cleanup error: {e}")
    finally:
        # Ensure the POLLER attribute is cleared
        if hasattr(_cygrpc, 'AsyncioEngine'):
            if hasattr(_cygrpc.AsyncioEngine, 'POLLER'):
                _cygrpc.AsyncioEngine.POLLER = None

# Main function
async def main():
    try:
        for table_name in tables_to_process:
            print(f"\nProcessing table: {table_name}")

            # Query to fetch ALL pending videos, regardless of date
            response = supabase.table(table_name)\
                .select("*")\
                .eq("processed", PROCESSING_STATUS['PENDING'])\
                .neq("transcript", "Transcript not available")\
                .execute()

            rows = response.data

            # Add debugging information
            print(f"Found {len(rows)} rows to process")
            print(f"Query parameters:")
            print(f"- Status: {PROCESSING_STATUS['PENDING']}")
            # No cutoff time filter applied

            if not rows:
                print(f"No eligible rows found in table: {table_name}")
                continue

            # Process each row with more detailed logging
            for row in rows:
                try:
                    print(f"\nProcessing row:")
                    print(f"- Video ID: {row.get('video_id')}")
                    print(f"- Title: {row.get('title')}")
                    print(f"- Published: {row.get('published_at')}")
                    print(f"- Transcript length: {len(row.get('transcript', ''))} characters")

                    result = await process_row(row, table_name)
                    print(f"Processing result: {result}")
                except Exception as e:
                    print(f"Error processing row {row.get('id', 'unknown')}: {e}")
                    continue
    except Exception as e:
        print(f"Error in main execution: {e}")
        raise
    finally:
        await cleanup()

# Modified script execution
if __name__ == "__main__":
    try:
        # Initialize gRPC async
        aio.init_grpc_aio()
        
        # Create and set event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # Run main function
        loop.run_until_complete(main())
        
    except KeyboardInterrupt:
        print("\nGracefully shutting down...")
    except Exception as e:
        print(f"Error during execution: {e}")
    finally:
        try:
            # Add a small delay before cleanup
            loop.run_until_complete(asyncio.sleep(0.5))
            
            # Cleanup gRPC
            loop.run_until_complete(cleanup_grpc())
            
            # Close the loop
            loop.close()
            
            # Shutdown gRPC
            aio.shutdown_grpc_aio()
        except Exception as e:
            print(f"Error during final cleanup: {e}")
