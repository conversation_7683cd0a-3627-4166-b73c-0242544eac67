WEBVTT
Kind: captions
Language: en

00:00:00.060 --> 00:00:02.810 align:start position:0%
 
let's<00:00:00.599><c> begin</c><00:00:00.840><c> to</c><00:00:01.140><c> understand</c><00:00:01.260><c> merge</c><00:00:01.920><c> sort</c><00:00:02.159><c> the</c>

00:00:02.810 --> 00:00:02.820 align:start position:0%
let's begin to understand merge sort the
 

00:00:02.820 --> 00:00:05.210 align:start position:0%
let's begin to understand merge sort the
idea<00:00:03.120><c> is</c><00:00:03.300><c> we</c><00:00:03.480><c> want</c><00:00:03.659><c> to</c><00:00:03.780><c> recursively</c><00:00:04.560><c> call</c><00:00:04.860><c> a</c>

00:00:05.210 --> 00:00:05.220 align:start position:0%
idea is we want to recursively call a
 

00:00:05.220 --> 00:00:07.190 align:start position:0%
idea is we want to recursively call a
function<00:00:05.520><c> that</c><00:00:06.180><c> is</c><00:00:06.299><c> going</c><00:00:06.540><c> to</c><00:00:06.660><c> divide</c><00:00:07.020><c> and</c>

00:00:07.190 --> 00:00:07.200 align:start position:0%
function that is going to divide and
 

00:00:07.200 --> 00:00:09.770 align:start position:0%
function that is going to divide and
conquer<00:00:07.560><c> this</c><00:00:07.740><c> array</c><00:00:08.040><c> into</c><00:00:08.340><c> sub</c><00:00:08.639><c> arrays</c><00:00:09.179><c> once</c>

00:00:09.770 --> 00:00:09.780 align:start position:0%
conquer this array into sub arrays once
 

00:00:09.780 --> 00:00:11.690 align:start position:0%
conquer this array into sub arrays once
we<00:00:09.900><c> reach</c><00:00:10.080><c> the</c><00:00:10.200><c> base</c><00:00:10.380><c> case</c><00:00:10.620><c> which</c><00:00:11.160><c> is</c><00:00:11.280><c> when</c><00:00:11.460><c> we</c>

00:00:11.690 --> 00:00:11.700 align:start position:0%
we reach the base case which is when we
 

00:00:11.700 --> 00:00:13.669 align:start position:0%
we reach the base case which is when we
can't<00:00:11.820><c> divide</c><00:00:12.360><c> a</c><00:00:12.599><c> sub</c><00:00:12.719><c> array</c><00:00:13.080><c> into</c><00:00:13.320><c> another</c>

00:00:13.669 --> 00:00:13.679 align:start position:0%
can't divide a sub array into another
 

00:00:13.679 --> 00:00:15.829 align:start position:0%
can't divide a sub array into another
subarray<00:00:14.340><c> anymore</c><00:00:14.759><c> we</c><00:00:15.480><c> want</c><00:00:15.660><c> to</c><00:00:15.780><c> start</c>

00:00:15.829 --> 00:00:15.839 align:start position:0%
subarray anymore we want to start
 

00:00:15.839 --> 00:00:17.750 align:start position:0%
subarray anymore we want to start
combining<00:00:16.379><c> these</c><00:00:16.500><c> sub</c><00:00:16.619><c> arrays</c><00:00:17.039><c> back</c><00:00:17.340><c> up</c><00:00:17.520><c> and</c>

00:00:17.750 --> 00:00:17.760 align:start position:0%
combining these sub arrays back up and
 

00:00:17.760 --> 00:00:20.330 align:start position:0%
combining these sub arrays back up and
sorting<00:00:18.180><c> them</c><00:00:18.300><c> as</c><00:00:18.779><c> we</c><00:00:19.020><c> go</c><00:00:19.320><c> into</c><00:00:19.740><c> this</c><00:00:20.100><c> original</c>

00:00:20.330 --> 00:00:20.340 align:start position:0%
sorting them as we go into this original
 

00:00:20.340 --> 00:00:22.010 align:start position:0%
sorting them as we go into this original
array<00:00:20.880><c> and</c><00:00:21.060><c> I'm</c><00:00:21.240><c> going</c><00:00:21.359><c> to</c><00:00:21.480><c> show</c><00:00:21.600><c> you</c><00:00:21.660><c> how</c><00:00:21.900><c> it's</c>

00:00:22.010 --> 00:00:22.020 align:start position:0%
array and I'm going to show you how it's
 

00:00:22.020 --> 00:00:24.109 align:start position:0%
array and I'm going to show you how it's
done<00:00:22.260><c> as</c><00:00:22.859><c> if</c><00:00:23.100><c> we</c><00:00:23.279><c> were</c><00:00:23.400><c> calling</c><00:00:23.880><c> this</c>

00:00:24.109 --> 00:00:24.119 align:start position:0%
done as if we were calling this
 

00:00:24.119 --> 00:00:26.090 align:start position:0%
done as if we were calling this
recursively<00:00:24.840><c> as</c><00:00:25.260><c> I</c><00:00:25.380><c> mentioned</c><00:00:25.619><c> we're</c><00:00:25.859><c> going</c>

00:00:26.090 --> 00:00:26.100 align:start position:0%
recursively as I mentioned we're going
 

00:00:26.100 --> 00:00:28.550 align:start position:0%
recursively as I mentioned we're going
to<00:00:26.220><c> divide</c><00:00:26.580><c> and</c><00:00:26.699><c> conquer</c><00:00:27.119><c> so</c><00:00:27.539><c> I</c><00:00:27.779><c> would</c><00:00:27.960><c> call</c><00:00:28.199><c> a</c>

00:00:28.550 --> 00:00:28.560 align:start position:0%
to divide and conquer so I would call a
 

00:00:28.560 --> 00:00:30.410 align:start position:0%
to divide and conquer so I would call a
function<00:00:28.920><c> that</c><00:00:29.519><c> would</c><00:00:29.699><c> divide</c><00:00:30.119><c> the</c><00:00:30.300><c> original</c>

00:00:30.410 --> 00:00:30.420 align:start position:0%
function that would divide the original
 

00:00:30.420 --> 00:00:32.990 align:start position:0%
function that would divide the original
array<00:00:30.960><c> into</c><00:00:31.619><c> a</c><00:00:31.920><c> left</c><00:00:32.040><c> half</c><00:00:32.220><c> and</c><00:00:32.520><c> a</c><00:00:32.640><c> right</c><00:00:32.759><c> half</c>

00:00:32.990 --> 00:00:33.000 align:start position:0%
array into a left half and a right half
 

00:00:33.000 --> 00:00:34.790 align:start position:0%
array into a left half and a right half
each<00:00:33.540><c> time</c><00:00:33.719><c> we</c><00:00:33.899><c> do</c><00:00:34.079><c> this</c><00:00:34.200><c> we're</c><00:00:34.500><c> going</c><00:00:34.680><c> to</c>

00:00:34.790 --> 00:00:34.800 align:start position:0%
each time we do this we're going to
 

00:00:34.800 --> 00:00:37.010 align:start position:0%
each time we do this we're going to
first<00:00:34.980><c> deal</c><00:00:35.280><c> with</c><00:00:35.520><c> the</c><00:00:35.880><c> left</c><00:00:36.000><c> half</c><00:00:36.239><c> so</c><00:00:36.719><c> we</c><00:00:36.899><c> now</c>

00:00:37.010 --> 00:00:37.020 align:start position:0%
first deal with the left half so we now
 

00:00:37.020 --> 00:00:38.750 align:start position:0%
first deal with the left half so we now
have<00:00:37.260><c> a</c><00:00:37.500><c> sub</c><00:00:37.620><c> array</c><00:00:37.980><c> of</c><00:00:38.219><c> the</c><00:00:38.399><c> first</c><00:00:38.520><c> four</c>

00:00:38.750 --> 00:00:38.760 align:start position:0%
have a sub array of the first four
 

00:00:38.760 --> 00:00:41.330 align:start position:0%
have a sub array of the first four
elements<00:00:39.239><c> and</c><00:00:40.079><c> again</c><00:00:40.260><c> we're</c><00:00:40.620><c> going</c><00:00:40.800><c> to</c><00:00:40.920><c> call</c><00:00:41.040><c> a</c>

00:00:41.330 --> 00:00:41.340 align:start position:0%
elements and again we're going to call a
 

00:00:41.340 --> 00:00:43.670 align:start position:0%
elements and again we're going to call a
recursive<00:00:41.820><c> function</c><00:00:42.180><c> on</c><00:00:42.780><c> this</c><00:00:43.079><c> subarray</c>

00:00:43.670 --> 00:00:43.680 align:start position:0%
recursive function on this subarray
 

00:00:43.680 --> 00:00:45.290 align:start position:0%
recursive function on this subarray
we're<00:00:44.340><c> going</c><00:00:44.460><c> to</c><00:00:44.520><c> deal</c><00:00:44.640><c> with</c><00:00:44.820><c> the</c><00:00:45.000><c> left</c><00:00:45.059><c> half</c>

00:00:45.290 --> 00:00:45.300 align:start position:0%
we're going to deal with the left half
 

00:00:45.300 --> 00:00:47.090 align:start position:0%
we're going to deal with the left half
first<00:00:45.540><c> so</c><00:00:45.840><c> we're</c><00:00:45.960><c> going</c><00:00:46.079><c> to</c><00:00:46.140><c> take</c><00:00:46.320><c> 85</c><00:00:46.680><c> and</c><00:00:46.860><c> 24</c>

00:00:47.090 --> 00:00:47.100 align:start position:0%
first so we're going to take 85 and 24
 

00:00:47.100 --> 00:00:49.250 align:start position:0%
first so we're going to take 85 and 24
and<00:00:48.000><c> they're</c><00:00:48.180><c> going</c><00:00:48.360><c> to</c><00:00:48.480><c> go</c><00:00:48.539><c> down</c><00:00:48.780><c> to</c><00:00:49.020><c> another</c>

00:00:49.250 --> 00:00:49.260 align:start position:0%
and they're going to go down to another
 

00:00:49.260 --> 00:00:50.330 align:start position:0%
and they're going to go down to another
level

00:00:50.330 --> 00:00:50.340 align:start position:0%
level
 

00:00:50.340 --> 00:00:53.270 align:start position:0%
level
and<00:00:51.000><c> then</c><00:00:51.180><c> we</c><00:00:51.600><c> can</c><00:00:51.840><c> still</c><00:00:52.200><c> call</c><00:00:52.559><c> a</c><00:00:52.860><c> recursive</c>

00:00:53.270 --> 00:00:53.280 align:start position:0%
and then we can still call a recursive
 

00:00:53.280 --> 00:00:55.430 align:start position:0%
and then we can still call a recursive
function<00:00:53.640><c> on</c><00:00:53.879><c> this</c><00:00:54.120><c> because</c><00:00:54.480><c> the</c><00:00:54.960><c> size</c><00:00:55.140><c> of</c><00:00:55.320><c> the</c>

00:00:55.430 --> 00:00:55.440 align:start position:0%
function on this because the size of the
 

00:00:55.440 --> 00:00:57.830 align:start position:0%
function on this because the size of the
array<00:00:55.680><c> is</c><00:00:55.920><c> not</c><00:00:56.100><c> less</c><00:00:56.399><c> than</c><00:00:56.520><c> 2.</c>

00:00:57.830 --> 00:00:57.840 align:start position:0%
array is not less than 2.
 

00:00:57.840 --> 00:01:00.350 align:start position:0%
array is not less than 2.
so<00:00:58.199><c> we</c><00:00:58.379><c> call</c><00:00:58.559><c> a</c><00:00:58.739><c> recursive</c><00:00:59.219><c> function</c><00:00:59.579><c> and</c><00:01:00.239><c> now</c>

00:01:00.350 --> 00:01:00.360 align:start position:0%
so we call a recursive function and now
 

00:01:00.360 --> 00:01:02.810 align:start position:0%
so we call a recursive function and now
we're<00:01:00.539><c> left</c><00:01:00.780><c> with</c><00:01:00.960><c> just</c><00:01:01.140><c> 85.</c><00:01:01.920><c> from</c><00:01:02.399><c> here</c><00:01:02.520><c> we</c>

00:01:02.810 --> 00:01:02.820 align:start position:0%
we're left with just 85. from here we
 

00:01:02.820 --> 00:01:04.430 align:start position:0%
we're left with just 85. from here we
can<00:01:02.940><c> no</c><00:01:03.120><c> longer</c><00:01:03.359><c> call</c><00:01:03.660><c> the</c><00:01:03.960><c> recursive</c>

00:01:04.430 --> 00:01:04.440 align:start position:0%
can no longer call the recursive
 

00:01:04.440 --> 00:01:06.590 align:start position:0%
can no longer call the recursive
function<00:01:04.739><c> because</c><00:01:05.220><c> the</c><00:01:05.640><c> sub</c><00:01:05.760><c> array</c><00:01:06.180><c> holding</c>

00:01:06.590 --> 00:01:06.600 align:start position:0%
function because the sub array holding
 

00:01:06.600 --> 00:01:08.990 align:start position:0%
function because the sub array holding
just<00:01:06.720><c> one</c><00:01:06.900><c> element</c><00:01:07.080><c> 85</c><00:01:07.740><c> the</c><00:01:08.340><c> size</c><00:01:08.580><c> is</c><00:01:08.760><c> less</c>

00:01:08.990 --> 00:01:09.000 align:start position:0%
just one element 85 the size is less
 

00:01:09.000 --> 00:01:11.570 align:start position:0%
just one element 85 the size is less
than<00:01:09.180><c> two</c><00:01:09.360><c> which</c><00:01:09.840><c> means</c><00:01:10.080><c> for</c><00:01:10.320><c> 85</c><00:01:10.740><c> we're</c><00:01:11.400><c> going</c>

00:01:11.570 --> 00:01:11.580 align:start position:0%
than two which means for 85 we're going
 

00:01:11.580 --> 00:01:14.750 align:start position:0%
than two which means for 85 we're going
to<00:01:11.700><c> push</c><00:01:11.939><c> that</c><00:01:12.240><c> back</c><00:01:12.540><c> up</c><00:01:12.960><c> one</c><00:01:13.439><c> level</c>

00:01:14.750 --> 00:01:14.760 align:start position:0%
to push that back up one level
 

00:01:14.760 --> 00:01:16.490 align:start position:0%
to push that back up one level
though<00:01:15.299><c> we</c><00:01:15.479><c> dealt</c><00:01:15.720><c> with</c><00:01:15.780><c> the</c><00:01:15.960><c> left</c><00:01:16.080><c> half</c><00:01:16.260><c> of</c>

00:01:16.490 --> 00:01:16.500 align:start position:0%
though we dealt with the left half of
 

00:01:16.500 --> 00:01:18.050 align:start position:0%
though we dealt with the left half of
this<00:01:16.619><c> subarray</c><00:01:17.159><c> now</c><00:01:17.640><c> we're</c><00:01:17.760><c> going</c><00:01:17.880><c> to</c><00:01:17.939><c> deal</c>

00:01:18.050 --> 00:01:18.060 align:start position:0%
this subarray now we're going to deal
 

00:01:18.060 --> 00:01:20.090 align:start position:0%
this subarray now we're going to deal
with<00:01:18.180><c> our</c><00:01:18.360><c> right</c><00:01:18.540><c> half</c><00:01:18.720><c> so</c><00:01:19.260><c> now</c><00:01:19.439><c> we</c><00:01:19.619><c> bring</c><00:01:19.799><c> 24</c>

00:01:20.090 --> 00:01:20.100 align:start position:0%
with our right half so now we bring 24
 

00:01:20.100 --> 00:01:21.830 align:start position:0%
with our right half so now we bring 24
down<00:01:20.520><c> and</c><00:01:20.939><c> now</c><00:01:21.119><c> we</c><00:01:21.240><c> can't</c><00:01:21.360><c> do</c><00:01:21.540><c> anything</c><00:01:21.659><c> else</c>

00:01:21.830 --> 00:01:21.840 align:start position:0%
down and now we can't do anything else
 

00:01:21.840 --> 00:01:24.890 align:start position:0%
down and now we can't do anything else
with<00:01:22.080><c> this</c><00:01:22.320><c> so</c><00:01:23.100><c> we</c><00:01:23.340><c> would</c><00:01:23.640><c> also</c><00:01:23.939><c> bring</c><00:01:24.180><c> 24</c><00:01:24.479><c> back</c>

00:01:24.890 --> 00:01:24.900 align:start position:0%
with this so we would also bring 24 back
 

00:01:24.900 --> 00:01:27.830 align:start position:0%
with this so we would also bring 24 back
up<00:01:25.080><c> but</c><00:01:25.380><c> this</c><00:01:25.740><c> time</c><00:01:25.920><c> we're</c><00:01:26.580><c> going</c><00:01:26.759><c> to</c><00:01:26.939><c> sort</c><00:01:27.180><c> 24</c>

00:01:27.830 --> 00:01:27.840 align:start position:0%
up but this time we're going to sort 24
 

00:01:27.840 --> 00:01:30.530 align:start position:0%
up but this time we're going to sort 24
and<00:01:28.200><c> 85</c><00:01:28.619><c> into</c><00:01:29.040><c> that</c><00:01:29.280><c> subarray</c><00:01:29.820><c> so</c><00:01:30.240><c> when</c><00:01:30.360><c> we</c>

00:01:30.530 --> 00:01:30.540 align:start position:0%
and 85 into that subarray so when we
 

00:01:30.540 --> 00:01:32.570 align:start position:0%
and 85 into that subarray so when we
bring<00:01:30.659><c> 24</c><00:01:31.020><c> back</c><00:01:31.560><c> up</c>

00:01:32.570 --> 00:01:32.580 align:start position:0%
bring 24 back up
 

00:01:32.580 --> 00:01:35.390 align:start position:0%
bring 24 back up
now<00:01:33.240><c> these</c><00:01:33.600><c> are</c><00:01:33.720><c> going</c><00:01:33.840><c> to</c><00:01:33.900><c> be</c><00:01:34.020><c> sorted</c><00:01:34.439><c> 24</c><00:01:34.920><c> and</c>

00:01:35.390 --> 00:01:35.400 align:start position:0%
now these are going to be sorted 24 and
 

00:01:35.400 --> 00:01:37.550 align:start position:0%
now these are going to be sorted 24 and
85.<00:01:36.119><c> now</c><00:01:36.360><c> in</c><00:01:36.540><c> the</c><00:01:36.659><c> sub</c><00:01:36.720><c> array</c><00:01:37.079><c> that</c><00:01:37.439><c> was</c>

00:01:37.550 --> 00:01:37.560 align:start position:0%
85. now in the sub array that was
 

00:01:37.560 --> 00:01:40.010 align:start position:0%
85. now in the sub array that was
holding<00:01:37.979><c> four</c><00:01:38.340><c> elements</c><00:01:38.880><c> we</c><00:01:39.420><c> dealt</c><00:01:39.780><c> with</c><00:01:39.840><c> the</c>

00:01:40.010 --> 00:01:40.020 align:start position:0%
holding four elements we dealt with the
 

00:01:40.020 --> 00:01:41.870 align:start position:0%
holding four elements we dealt with the
left<00:01:40.200><c> half</c><00:01:40.439><c> so</c><00:01:40.979><c> we're</c><00:01:41.100><c> going</c><00:01:41.280><c> to</c><00:01:41.400><c> push</c><00:01:41.640><c> them</c>

00:01:41.870 --> 00:01:41.880 align:start position:0%
left half so we're going to push them
 

00:01:41.880 --> 00:01:44.030 align:start position:0%
left half so we're going to push them
back<00:01:42.060><c> up</c><00:01:42.240><c> into</c><00:01:42.479><c> that</c><00:01:42.780><c> level</c><00:01:42.960><c> and</c><00:01:43.740><c> then</c><00:01:43.799><c> we</c>

00:01:44.030 --> 00:01:44.040 align:start position:0%
back up into that level and then we
 

00:01:44.040 --> 00:01:46.550 align:start position:0%
back up into that level and then we
still<00:01:44.220><c> have</c><00:01:44.400><c> to</c><00:01:44.520><c> deal</c><00:01:44.759><c> with</c><00:01:45.180><c> 63</c><00:01:45.659><c> and</c><00:01:45.840><c> 45</c><00:01:46.079><c> for</c>

00:01:46.550 --> 00:01:46.560 align:start position:0%
still have to deal with 63 and 45 for
 

00:01:46.560 --> 00:01:47.690 align:start position:0%
still have to deal with 63 and 45 for
the<00:01:46.740><c> right</c><00:01:46.920><c> half</c>

00:01:47.690 --> 00:01:47.700 align:start position:0%
the right half
 

00:01:47.700 --> 00:01:49.850 align:start position:0%
the right half
so<00:01:48.119><c> let's</c><00:01:48.240><c> go</c><00:01:48.420><c> ahead</c><00:01:48.540><c> and</c><00:01:48.600><c> bring</c><00:01:48.780><c> 24</c><00:01:49.140><c> and</c><00:01:49.500><c> 85</c>

00:01:49.850 --> 00:01:49.860 align:start position:0%
so let's go ahead and bring 24 and 85
 

00:01:49.860 --> 00:01:52.010 align:start position:0%
so let's go ahead and bring 24 and 85
back<00:01:50.100><c> up</c><00:01:50.280><c> and</c><00:01:50.579><c> they</c><00:01:50.759><c> are</c><00:01:50.939><c> sorted</c><00:01:51.299><c> in</c><00:01:51.540><c> that</c><00:01:51.780><c> left</c>

00:01:52.010 --> 00:01:52.020 align:start position:0%
back up and they are sorted in that left
 

00:01:52.020 --> 00:01:55.370 align:start position:0%
back up and they are sorted in that left
half<00:01:52.259><c> so</c><00:01:52.799><c> now</c><00:01:52.920><c> we</c><00:01:53.100><c> bring</c><00:01:53.280><c> 63</c><00:01:53.759><c> and</c><00:01:53.939><c> 45</c><00:01:54.180><c> down</c><00:01:54.600><c> and</c>

00:01:55.370 --> 00:01:55.380 align:start position:0%
half so now we bring 63 and 45 down and
 

00:01:55.380 --> 00:01:57.889 align:start position:0%
half so now we bring 63 and 45 down and
now<00:01:55.619><c> we</c><00:01:55.860><c> have</c><00:01:55.979><c> to</c><00:01:56.240><c> recursively</c><00:01:57.240><c> call</c><00:01:57.479><c> the</c>

00:01:57.889 --> 00:01:57.899 align:start position:0%
now we have to recursively call the
 

00:01:57.899 --> 00:02:00.109 align:start position:0%
now we have to recursively call the
right<00:01:58.079><c> half</c><00:01:58.259><c> of</c><00:01:58.619><c> that</c><00:01:58.799><c> array</c><00:01:59.220><c> we</c><00:01:59.759><c> call</c><00:02:00.000><c> a</c>

00:02:00.109 --> 00:02:00.119 align:start position:0%
right half of that array we call a
 

00:02:00.119 --> 00:02:01.730 align:start position:0%
right half of that array we call a
recursive<00:02:00.540><c> function</c><00:02:00.840><c> on</c><00:02:01.140><c> this</c><00:02:01.259><c> sub</c><00:02:01.439><c> array</c>

00:02:01.730 --> 00:02:01.740 align:start position:0%
recursive function on this sub array
 

00:02:01.740 --> 00:02:04.069 align:start position:0%
recursive function on this sub array
holding<00:02:02.040><c> 63</c><00:02:02.399><c> and</c><00:02:02.579><c> 45</c><00:02:02.759><c> we</c><00:02:03.479><c> deal</c><00:02:03.659><c> with</c><00:02:03.840><c> the</c><00:02:03.960><c> left</c>

00:02:04.069 --> 00:02:04.079 align:start position:0%
holding 63 and 45 we deal with the left
 

00:02:04.079 --> 00:02:06.950 align:start position:0%
holding 63 and 45 we deal with the left
half<00:02:04.380><c> first</c><00:02:04.680><c> so</c><00:02:05.219><c> we</c><00:02:05.399><c> take</c><00:02:05.640><c> 63</c><00:02:06.060><c> bring</c><00:02:06.600><c> it</c><00:02:06.780><c> down</c>

00:02:06.950 --> 00:02:06.960 align:start position:0%
half first so we take 63 bring it down
 

00:02:06.960 --> 00:02:09.290 align:start position:0%
half first so we take 63 bring it down
to<00:02:07.079><c> another</c><00:02:07.259><c> level</c><00:02:07.560><c> and</c><00:02:08.220><c> here</c><00:02:08.520><c> we</c><00:02:08.759><c> can</c><00:02:08.880><c> say</c><00:02:09.060><c> we</c>

00:02:09.290 --> 00:02:09.300 align:start position:0%
to another level and here we can say we
 

00:02:09.300 --> 00:02:11.270 align:start position:0%
to another level and here we can say we
can<00:02:09.479><c> no</c><00:02:09.660><c> longer</c><00:02:09.959><c> perform</c><00:02:10.440><c> a</c><00:02:10.860><c> recursive</c>

00:02:11.270 --> 00:02:11.280 align:start position:0%
can no longer perform a recursive
 

00:02:11.280 --> 00:02:12.890 align:start position:0%
can no longer perform a recursive
function<00:02:11.580><c> because</c><00:02:11.879><c> we</c><00:02:12.180><c> reached</c><00:02:12.540><c> the</c><00:02:12.780><c> base</c>

00:02:12.890 --> 00:02:12.900 align:start position:0%
function because we reached the base
 

00:02:12.900 --> 00:02:14.630 align:start position:0%
function because we reached the base
case<00:02:13.140><c> where</c><00:02:13.440><c> the</c><00:02:13.680><c> size</c><00:02:13.800><c> of</c><00:02:13.980><c> this</c><00:02:14.099><c> array</c><00:02:14.400><c> is</c>

00:02:14.630 --> 00:02:14.640 align:start position:0%
case where the size of this array is
 

00:02:14.640 --> 00:02:16.190 align:start position:0%
case where the size of this array is
less<00:02:14.879><c> than</c><00:02:15.060><c> two</c><00:02:15.300><c> so</c><00:02:15.599><c> now</c><00:02:15.780><c> we're</c><00:02:15.900><c> going</c><00:02:16.080><c> to</c>

00:02:16.190 --> 00:02:16.200 align:start position:0%
less than two so now we're going to
 

00:02:16.200 --> 00:02:18.170 align:start position:0%
less than two so now we're going to
bring<00:02:16.319><c> 63</c><00:02:16.860><c> back</c><00:02:17.220><c> up</c>

00:02:18.170 --> 00:02:18.180 align:start position:0%
bring 63 back up
 

00:02:18.180 --> 00:02:20.449 align:start position:0%
bring 63 back up
and<00:02:18.540><c> then</c><00:02:18.720><c> we're</c><00:02:18.840><c> going</c><00:02:18.959><c> to</c><00:02:19.020><c> bring</c><00:02:19.200><c> 45</c><00:02:19.620><c> back</c>

00:02:20.449 --> 00:02:20.459 align:start position:0%
and then we're going to bring 45 back
 

00:02:20.459 --> 00:02:22.309 align:start position:0%
and then we're going to bring 45 back
down<00:02:20.640><c> to</c><00:02:20.819><c> that</c><00:02:21.000><c> level</c><00:02:21.599><c> because</c><00:02:22.020><c> we're</c><00:02:22.200><c> going</c>

00:02:22.309 --> 00:02:22.319 align:start position:0%
down to that level because we're going
 

00:02:22.319 --> 00:02:23.750 align:start position:0%
down to that level because we're going
to<00:02:22.379><c> deal</c><00:02:22.500><c> with</c><00:02:22.620><c> the</c><00:02:22.800><c> right</c><00:02:22.980><c> half</c><00:02:23.220><c> of</c><00:02:23.640><c> that</c>

00:02:23.750 --> 00:02:23.760 align:start position:0%
to deal with the right half of that
 

00:02:23.760 --> 00:02:25.550 align:start position:0%
to deal with the right half of that
array<00:02:24.060><c> now</c><00:02:24.300><c> remember</c><00:02:24.720><c> we</c><00:02:25.080><c> deal</c><00:02:25.200><c> with</c><00:02:25.319><c> the</c><00:02:25.500><c> left</c>

00:02:25.550 --> 00:02:25.560 align:start position:0%
array now remember we deal with the left
 

00:02:25.560 --> 00:02:27.830 align:start position:0%
array now remember we deal with the left
half<00:02:25.739><c> and</c><00:02:26.040><c> then</c><00:02:26.160><c> the</c><00:02:26.340><c> right</c><00:02:26.459><c> half</c><00:02:26.640><c> with</c><00:02:27.000><c> 45</c><00:02:27.239><c> we</c>

00:02:27.830 --> 00:02:27.840 align:start position:0%
half and then the right half with 45 we
 

00:02:27.840 --> 00:02:29.690 align:start position:0%
half and then the right half with 45 we
can<00:02:27.959><c> no</c><00:02:28.140><c> longer</c><00:02:28.379><c> divide</c><00:02:28.739><c> that</c><00:02:28.920><c> anymore</c><00:02:29.160><c> we</c>

00:02:29.690 --> 00:02:29.700 align:start position:0%
can no longer divide that anymore we
 

00:02:29.700 --> 00:02:30.949 align:start position:0%
can no longer divide that anymore we
reach<00:02:29.940><c> the</c><00:02:30.120><c> base</c><00:02:30.239><c> case</c><00:02:30.420><c> so</c><00:02:30.599><c> we're</c><00:02:30.720><c> going</c><00:02:30.900><c> to</c>

00:02:30.949 --> 00:02:30.959 align:start position:0%
reach the base case so we're going to
 

00:02:30.959 --> 00:02:33.110 align:start position:0%
reach the base case so we're going to
bring<00:02:31.080><c> that</c><00:02:31.319><c> back</c><00:02:31.620><c> up</c><00:02:31.860><c> but</c><00:02:32.580><c> this</c><00:02:32.760><c> time</c><00:02:32.879><c> we're</c>

00:02:33.110 --> 00:02:33.120 align:start position:0%
bring that back up but this time we're
 

00:02:33.120 --> 00:02:35.690 align:start position:0%
bring that back up but this time we're
also<00:02:33.420><c> going</c><00:02:33.480><c> to</c><00:02:33.540><c> be</c><00:02:33.599><c> sorting</c><00:02:33.959><c> 45</c><00:02:34.200><c> and</c><00:02:34.560><c> 63</c><00:02:34.980><c> in</c>

00:02:35.690 --> 00:02:35.700 align:start position:0%
also going to be sorting 45 and 63 in
 

00:02:35.700 --> 00:02:38.869 align:start position:0%
also going to be sorting 45 and 63 in
this<00:02:35.879><c> array</c><00:02:36.239><c> now</c><00:02:36.959><c> when</c><00:02:37.260><c> we</c><00:02:37.440><c> bring</c><00:02:37.620><c> 45</c><00:02:38.040><c> and</c><00:02:38.400><c> 63</c>

00:02:38.869 --> 00:02:38.879 align:start position:0%
this array now when we bring 45 and 63
 

00:02:38.879 --> 00:02:41.089 align:start position:0%
this array now when we bring 45 and 63
we<00:02:39.420><c> bring</c><00:02:39.599><c> that</c><00:02:39.840><c> back</c><00:02:40.080><c> up</c><00:02:40.379><c> to</c><00:02:40.620><c> the</c><00:02:40.739><c> next</c><00:02:40.920><c> level</c>

00:02:41.089 --> 00:02:41.099 align:start position:0%
we bring that back up to the next level
 

00:02:41.099 --> 00:02:44.210 align:start position:0%
we bring that back up to the next level
or<00:02:41.519><c> 24</c><00:02:41.760><c> and</c><00:02:42.120><c> 85</c><00:02:42.480><c> are</c><00:02:42.720><c> we're</c><00:02:43.440><c> going</c><00:02:43.620><c> to</c><00:02:43.800><c> sort</c>

00:02:44.210 --> 00:02:44.220 align:start position:0%
or 24 and 85 are we're going to sort
 

00:02:44.220 --> 00:02:46.850 align:start position:0%
or 24 and 85 are we're going to sort
that<00:02:44.819><c> whole</c><00:02:44.940><c> array</c><00:02:45.300><c> so</c><00:02:45.540><c> now</c><00:02:45.660><c> it'll</c><00:02:45.900><c> be</c><00:02:45.959><c> 24</c><00:02:46.260><c> 45</c>

00:02:46.850 --> 00:02:46.860 align:start position:0%
that whole array so now it'll be 24 45
 

00:02:46.860 --> 00:02:50.330 align:start position:0%
that whole array so now it'll be 24 45
63<00:02:47.700><c> and</c><00:02:48.060><c> 85.</c><00:02:48.840><c> now</c><00:02:49.260><c> that</c><00:02:49.440><c> we're</c><00:02:49.560><c> done</c><00:02:49.739><c> with</c><00:02:50.099><c> the</c>

00:02:50.330 --> 00:02:50.340 align:start position:0%
63 and 85. now that we're done with the
 

00:02:50.340 --> 00:02:52.369 align:start position:0%
63 and 85. now that we're done with the
left<00:02:50.519><c> half</c><00:02:50.760><c> of</c><00:02:51.060><c> the</c><00:02:51.239><c> original</c><00:02:51.360><c> array</c><00:02:51.900><c> and</c>

00:02:52.369 --> 00:02:52.379 align:start position:0%
left half of the original array and
 

00:02:52.379 --> 00:02:54.710 align:start position:0%
left half of the original array and
calling<00:02:52.739><c> all</c><00:02:52.980><c> those</c><00:02:53.160><c> recursive</c><00:02:53.700><c> calls</c><00:02:54.120><c> to</c>

00:02:54.710 --> 00:02:54.720 align:start position:0%
calling all those recursive calls to
 

00:02:54.720 --> 00:02:57.290 align:start position:0%
calling all those recursive calls to
sort<00:02:54.900><c> them</c><00:02:55.200><c> we</c><00:02:55.860><c> bring</c><00:02:56.160><c> them</c><00:02:56.400><c> back</c><00:02:56.760><c> up</c><00:02:57.000><c> to</c><00:02:57.180><c> the</c>

00:02:57.290 --> 00:02:57.300 align:start position:0%
sort them we bring them back up to the
 

00:02:57.300 --> 00:02:59.449 align:start position:0%
sort them we bring them back up to the
original<00:02:57.420><c> array</c><00:02:57.959><c> and</c><00:02:58.379><c> that</c><00:02:58.620><c> left</c><00:02:58.860><c> half</c><00:02:59.099><c> is</c><00:02:59.280><c> now</c>

00:02:59.449 --> 00:02:59.459 align:start position:0%
original array and that left half is now
 

00:02:59.459 --> 00:03:01.250 align:start position:0%
original array and that left half is now
sorted<00:02:59.940><c> now</c><00:03:00.360><c> we</c><00:03:00.599><c> have</c><00:03:00.720><c> to</c><00:03:00.780><c> deal</c><00:03:00.900><c> with</c><00:03:01.080><c> the</c>

00:03:01.250 --> 00:03:01.260 align:start position:0%
sorted now we have to deal with the
 

00:03:01.260 --> 00:03:03.050 align:start position:0%
sorted now we have to deal with the
right<00:03:01.500><c> half</c><00:03:01.739><c> and</c><00:03:02.160><c> we're</c><00:03:02.340><c> going</c><00:03:02.459><c> to</c><00:03:02.580><c> do</c><00:03:02.760><c> the</c>

00:03:03.050 --> 00:03:03.060 align:start position:0%
right half and we're going to do the
 

00:03:03.060 --> 00:03:04.790 align:start position:0%
right half and we're going to do the
same<00:03:03.360><c> thing</c><00:03:03.660><c> the</c><00:03:04.019><c> idea</c><00:03:04.260><c> of</c><00:03:04.319><c> merge</c><00:03:04.560><c> sword</c><00:03:04.680><c> again</c>

00:03:04.790 --> 00:03:04.800 align:start position:0%
same thing the idea of merge sword again
 

00:03:04.800 --> 00:03:05.930 align:start position:0%
same thing the idea of merge sword again
is<00:03:04.980><c> that</c><00:03:05.099><c> we're</c><00:03:05.220><c> going</c><00:03:05.280><c> to</c><00:03:05.400><c> divide</c><00:03:05.819><c> the</c>

00:03:05.930 --> 00:03:05.940 align:start position:0%
is that we're going to divide the
 

00:03:05.940 --> 00:03:07.369 align:start position:0%
is that we're going to divide the
original<00:03:06.060><c> array</c><00:03:06.480><c> into</c><00:03:06.660><c> sub</c><00:03:06.900><c> arrays</c>

00:03:07.369 --> 00:03:07.379 align:start position:0%
original array into sub arrays
 

00:03:07.379 --> 00:03:09.110 align:start position:0%
original array into sub arrays
recursively<00:03:07.980><c> calling</c><00:03:08.280><c> a</c><00:03:08.459><c> function</c><00:03:08.700><c> to</c><00:03:09.000><c> do</c>

00:03:09.110 --> 00:03:09.120 align:start position:0%
recursively calling a function to do
 

00:03:09.120 --> 00:03:11.089 align:start position:0%
recursively calling a function to do
that<00:03:09.300><c> for</c><00:03:09.480><c> us</c><00:03:09.660><c> when</c><00:03:09.959><c> we</c><00:03:10.140><c> reach</c><00:03:10.379><c> a</c><00:03:10.500><c> level</c><00:03:10.680><c> or</c><00:03:10.920><c> the</c>

00:03:11.089 --> 00:03:11.099 align:start position:0%
that for us when we reach a level or the
 

00:03:11.099 --> 00:03:12.890 align:start position:0%
that for us when we reach a level or the
base<00:03:11.220><c> case</c><00:03:11.400><c> of</c><00:03:11.640><c> that</c><00:03:11.760><c> recursion</c><00:03:12.180><c> call</c><00:03:12.360><c> we</c><00:03:12.780><c> want</c>

00:03:12.890 --> 00:03:12.900 align:start position:0%
base case of that recursion call we want
 

00:03:12.900 --> 00:03:15.110 align:start position:0%
base case of that recursion call we want
to<00:03:13.080><c> propagate</c><00:03:13.560><c> those</c><00:03:13.739><c> elements</c><00:03:14.159><c> back</c><00:03:14.519><c> up</c><00:03:14.819><c> the</c>

00:03:15.110 --> 00:03:15.120 align:start position:0%
to propagate those elements back up the
 

00:03:15.120 --> 00:03:16.309 align:start position:0%
to propagate those elements back up the
level<00:03:15.239><c> you</c><00:03:15.540><c> can</c><00:03:15.659><c> think</c><00:03:15.780><c> of</c><00:03:15.840><c> this</c><00:03:15.959><c> as</c><00:03:16.080><c> like</c><00:03:16.260><c> a</c>

00:03:16.309 --> 00:03:16.319 align:start position:0%
level you can think of this as like a
 

00:03:16.319 --> 00:03:18.589 align:start position:0%
level you can think of this as like a
merge<00:03:16.620><c> sort</c><00:03:16.860><c> tree</c><00:03:17.099><c> we</c><00:03:17.459><c> bring</c><00:03:17.700><c> it</c><00:03:17.879><c> back</c><00:03:18.120><c> up</c><00:03:18.360><c> and</c>

00:03:18.589 --> 00:03:18.599 align:start position:0%
merge sort tree we bring it back up and
 

00:03:18.599 --> 00:03:20.570 align:start position:0%
merge sort tree we bring it back up and
as<00:03:18.780><c> we</c><00:03:18.959><c> bring</c><00:03:19.140><c> the</c><00:03:19.379><c> elements</c><00:03:19.739><c> back</c><00:03:19.920><c> up</c><00:03:20.099><c> we</c><00:03:20.459><c> want</c>

00:03:20.570 --> 00:03:20.580 align:start position:0%
as we bring the elements back up we want
 

00:03:20.580 --> 00:03:22.850 align:start position:0%
as we bring the elements back up we want
to<00:03:20.760><c> combine</c><00:03:21.239><c> them</c><00:03:21.360><c> and</c><00:03:21.599><c> sort</c><00:03:22.080><c> them</c><00:03:22.319><c> as</c><00:03:22.560><c> we</c><00:03:22.680><c> do</c>

00:03:22.850 --> 00:03:22.860 align:start position:0%
to combine them and sort them as we do
 

00:03:22.860 --> 00:03:25.190 align:start position:0%
to combine them and sort them as we do
so<00:03:23.040><c> now</c><00:03:23.819><c> the</c><00:03:23.940><c> time</c><00:03:24.120><c> complexity</c><00:03:24.599><c> of</c><00:03:24.840><c> this</c><00:03:24.959><c> merge</c>

00:03:25.190 --> 00:03:25.200 align:start position:0%
so now the time complexity of this merge
 

00:03:25.200 --> 00:03:28.850 align:start position:0%
so now the time complexity of this merge
sort<00:03:25.440><c> is</c><00:03:25.739><c> Big</c><00:03:25.980><c> O</c><00:03:26.159><c> of</c><00:03:26.519><c> n</c><00:03:26.879><c> Times</c><00:03:27.120><c> log</c><00:03:27.480><c> of</c><00:03:27.900><c> M</c><00:03:28.140><c> you</c>

00:03:28.850 --> 00:03:28.860 align:start position:0%
sort is Big O of n Times log of M you
 

00:03:28.860 --> 00:03:30.830 align:start position:0%
sort is Big O of n Times log of M you
just<00:03:29.099><c> successfully</c><00:03:29.519><c> completed</c><00:03:30.000><c> merge</c><00:03:30.659><c> sort</c>

00:03:30.830 --> 00:03:30.840 align:start position:0%
just successfully completed merge sort
 

00:03:30.840 --> 00:03:32.089 align:start position:0%
just successfully completed merge sort
or<00:03:31.140><c> at</c><00:03:31.260><c> least</c><00:03:31.440><c> you</c><00:03:31.560><c> went</c><00:03:31.739><c> through</c><00:03:31.920><c> an</c>

00:03:32.089 --> 00:03:32.099 align:start position:0%
or at least you went through an
 

00:03:32.099 --> 00:03:33.589 align:start position:0%
or at least you went through an
explanation<00:03:32.459><c> of</c><00:03:32.819><c> merge</c><00:03:33.120><c> sort</c><00:03:33.360><c> using</c>

00:03:33.589 --> 00:03:33.599 align:start position:0%
explanation of merge sort using
 

00:03:33.599 --> 00:03:35.690 align:start position:0%
explanation of merge sort using
recursion<00:03:34.080><c> more</c><00:03:34.680><c> than</c><00:03:34.800><c> likely</c><00:03:35.040><c> this</c><00:03:35.340><c> is</c><00:03:35.459><c> the</c>

00:03:35.690 --> 00:03:35.700 align:start position:0%
recursion more than likely this is the
 

00:03:35.700 --> 00:03:37.729 align:start position:0%
recursion more than likely this is the
first<00:03:35.879><c> sorting</c><00:03:36.360><c> algorithm</c><00:03:36.780><c> that</c><00:03:37.140><c> you</c><00:03:37.260><c> expose</c>

00:03:37.729 --> 00:03:37.739 align:start position:0%
first sorting algorithm that you expose
 

00:03:37.739 --> 00:03:40.190 align:start position:0%
first sorting algorithm that you expose
to<00:03:38.040><c> that</c><00:03:38.580><c> uses</c><00:03:38.940><c> recursion</c><00:03:39.540><c> the</c><00:03:39.780><c> concept</c><00:03:40.080><c> of</c>

00:03:40.190 --> 00:03:40.200 align:start position:0%
to that uses recursion the concept of
 

00:03:40.200 --> 00:03:42.890 align:start position:0%
to that uses recursion the concept of
recursion<00:03:40.799><c> isn't</c><00:03:41.519><c> easy</c><00:03:41.760><c> and</c><00:03:42.000><c> it</c><00:03:42.180><c> doesn't</c><00:03:42.420><c> come</c>

00:03:42.890 --> 00:03:42.900 align:start position:0%
recursion isn't easy and it doesn't come
 

00:03:42.900 --> 00:03:44.509 align:start position:0%
recursion isn't easy and it doesn't come
naturally<00:03:43.379><c> most</c><00:03:44.040><c> of</c><00:03:44.159><c> the</c><00:03:44.220><c> time</c><00:03:44.280><c> you're</c><00:03:44.459><c> going</c>

00:03:44.509 --> 00:03:44.519 align:start position:0%
naturally most of the time you're going
 

00:03:44.519 --> 00:03:46.369 align:start position:0%
naturally most of the time you're going
to<00:03:44.640><c> see</c><00:03:44.700><c> it</c><00:03:44.819><c> in</c><00:03:45.000><c> the</c><00:03:45.120><c> classroom</c><00:03:45.480><c> or</c><00:03:46.019><c> when</c>

00:03:46.369 --> 00:03:46.379 align:start position:0%
to see it in the classroom or when
 

00:03:46.379 --> 00:03:47.449 align:start position:0%
to see it in the classroom or when
you're<00:03:46.560><c> understanding</c><00:03:47.040><c> some</c><00:03:47.280><c> of</c><00:03:47.400><c> these</c>

00:03:47.449 --> 00:03:47.459 align:start position:0%
you're understanding some of these
 

00:03:47.459 --> 00:03:49.309 align:start position:0%
you're understanding some of these
algorithms<00:03:47.940><c> for</c><00:03:48.060><c> the</c><00:03:48.180><c> first</c><00:03:48.239><c> time</c><00:03:48.420><c> you</c><00:03:49.140><c> can</c>

00:03:49.309 --> 00:03:49.319 align:start position:0%
algorithms for the first time you can
 

00:03:49.319 --> 00:03:51.530 align:start position:0%
algorithms for the first time you can
accomplish<00:03:49.860><c> the</c><00:03:50.159><c> merge</c><00:03:50.459><c> sort</c><00:03:50.700><c> also</c><00:03:51.120><c> using</c>

00:03:51.530 --> 00:03:51.540 align:start position:0%
accomplish the merge sort also using
 

00:03:51.540 --> 00:03:53.990 align:start position:0%
accomplish the merge sort also using
iteration<00:03:52.140><c> not</c><00:03:52.620><c> just</c><00:03:52.860><c> recursion</c><00:03:53.400><c> generally</c>

00:03:53.990 --> 00:03:54.000 align:start position:0%
iteration not just recursion generally
 

00:03:54.000 --> 00:03:55.789 align:start position:0%
iteration not just recursion generally
you're<00:03:54.239><c> going</c><00:03:54.420><c> to</c><00:03:54.599><c> see</c><00:03:54.780><c> recursion</c><00:03:55.260><c> when</c><00:03:55.620><c> you</c>

00:03:55.789 --> 00:03:55.799 align:start position:0%
you're going to see recursion when you
 

00:03:55.799 --> 00:03:57.649 align:start position:0%
you're going to see recursion when you
perform<00:03:55.980><c> a</c><00:03:56.220><c> merge</c><00:03:56.519><c> sort</c><00:03:56.700><c> I</c><00:03:57.120><c> understand</c><00:03:57.299><c> it's</c>

00:03:57.649 --> 00:03:57.659 align:start position:0%
perform a merge sort I understand it's
 

00:03:57.659 --> 00:03:59.149 align:start position:0%
perform a merge sort I understand it's
not<00:03:57.840><c> the</c><00:03:57.959><c> easiest</c><00:03:58.260><c> thing</c><00:03:58.440><c> so</c><00:03:58.680><c> if</c><00:03:58.799><c> you</c><00:03:58.920><c> have</c><00:03:58.980><c> any</c>

00:03:59.149 --> 00:03:59.159 align:start position:0%
not the easiest thing so if you have any
 

00:03:59.159 --> 00:04:00.710 align:start position:0%
not the easiest thing so if you have any
questions<00:03:59.340><c> or</c><00:03:59.580><c> comments</c><00:03:59.940><c> leave</c><00:04:00.299><c> them</c><00:04:00.480><c> down</c>

00:04:00.710 --> 00:04:00.720 align:start position:0%
questions or comments leave them down
 

00:04:00.720 --> 00:04:02.449 align:start position:0%
questions or comments leave them down
below<00:04:00.959><c> and</c><00:04:01.260><c> I'll</c><00:04:01.440><c> be</c><00:04:01.620><c> more</c><00:04:01.799><c> than</c><00:04:01.980><c> happy</c><00:04:02.220><c> to</c>

00:04:02.449 --> 00:04:02.459 align:start position:0%
below and I'll be more than happy to
 

00:04:02.459 --> 00:04:03.890 align:start position:0%
below and I'll be more than happy to
help<00:04:02.640><c> you</c><00:04:02.760><c> watch</c><00:04:03.180><c> these</c><00:04:03.480><c> other</c><00:04:03.599><c> sorting</c>

00:04:03.890 --> 00:04:03.900 align:start position:0%
help you watch these other sorting
 

00:04:03.900 --> 00:04:05.509 align:start position:0%
help you watch these other sorting
algorithm<00:04:04.319><c> videos</c><00:04:04.500><c> and</c><00:04:04.980><c> I'll</c><00:04:05.099><c> talk</c><00:04:05.280><c> to</c><00:04:05.400><c> you</c>

00:04:05.509 --> 00:04:05.519 align:start position:0%
algorithm videos and I'll talk to you
 

00:04:05.519 --> 00:04:08.420 align:start position:0%
algorithm videos and I'll talk to you
later<00:04:05.640><c> have</c><00:04:06.060><c> a</c><00:04:06.180><c> great</c><00:04:06.299><c> day</c>

