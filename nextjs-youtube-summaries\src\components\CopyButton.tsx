'use client';

import React from 'react';

interface CopyButtonProps {
  text: string;
  className?: string;
  label?: string;
  icon?: boolean;
  showToast?: boolean;
  iconOnly?: boolean;
}

export default function CopyButton({
  text,
  className = "text-xs px-2 py-1 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded flex items-center",
  label = "Copy",
  icon = true,
  showToast = true,
  iconOnly = false
}: CopyButtonProps) {
  const handleCopy = () => {
    navigator.clipboard.writeText(text);
    
    if (showToast) {
      // Visual feedback
      const notification = document.createElement('div');
      notification.textContent = 'Copied to clipboard!';
      notification.style.cssText = 'position:fixed;bottom:20px;left:50%;transform:translateX(-50%);background:#4CAF50;color:white;padding:8px 16px;border-radius:4px;z-index:1000;';
      document.body.appendChild(notification);
      setTimeout(() => notification.remove(), 2000);
    }
  };

  return (
    <button onClick={handleCopy} className={className} title={`Copy ${label.toLowerCase()}`}>
      {icon && (
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="14" 
          height="14" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          className={iconOnly ? "" : "mr-1"}
        >
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
        </svg>
      )}
      {!iconOnly && label}
    </button>
  );
}
