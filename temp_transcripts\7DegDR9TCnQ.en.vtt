WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.670 align:start position:0%
 
hello<00:00:00.399><c> and</c><00:00:00.480><c> welcome</c><00:00:00.719><c> to</c><00:00:00.880><c> tyler</c><00:00:01.199><c> programming</c>

00:00:01.670 --> 00:00:01.680 align:start position:0%
hello and welcome to tyler programming
 

00:00:01.680 --> 00:00:03.429 align:start position:0%
hello and welcome to tyler programming
and<00:00:01.839><c> today</c><00:00:02.320><c> we're</c><00:00:02.560><c> going</c><00:00:02.720><c> to</c><00:00:02.800><c> be</c><00:00:02.960><c> going</c><00:00:03.199><c> over</c>

00:00:03.429 --> 00:00:03.439 align:start position:0%
and today we're going to be going over
 

00:00:03.439 --> 00:00:06.389 align:start position:0%
and today we're going to be going over
the<00:00:03.520><c> graph</c><00:00:03.919><c> data</c><00:00:04.240><c> structure</c><00:00:05.120><c> now</c><00:00:05.839><c> graphs</c><00:00:06.240><c> are</c>

00:00:06.389 --> 00:00:06.399 align:start position:0%
the graph data structure now graphs are
 

00:00:06.399 --> 00:00:08.390 align:start position:0%
the graph data structure now graphs are
a<00:00:06.480><c> way</c><00:00:06.640><c> of</c><00:00:06.799><c> representing</c><00:00:07.200><c> relationships</c><00:00:08.160><c> that</c>

00:00:08.390 --> 00:00:08.400 align:start position:0%
a way of representing relationships that
 

00:00:08.400 --> 00:00:11.030 align:start position:0%
a way of representing relationships that
exist<00:00:08.800><c> between</c><00:00:09.280><c> pairs</c><00:00:09.599><c> of</c><00:00:09.760><c> objects</c><00:00:10.240><c> and</c>

00:00:11.030 --> 00:00:11.040 align:start position:0%
exist between pairs of objects and
 

00:00:11.040 --> 00:00:12.629 align:start position:0%
exist between pairs of objects and
this<00:00:11.280><c> use</c><00:00:11.599><c> can</c><00:00:11.759><c> range</c><00:00:12.080><c> from</c><00:00:12.320><c> network</c>

00:00:12.629 --> 00:00:12.639 align:start position:0%
this use can range from network
 

00:00:12.639 --> 00:00:13.589 align:start position:0%
this use can range from network
engineering<00:00:13.120><c> all</c><00:00:13.280><c> the</c><00:00:13.360><c> way</c><00:00:13.519><c> to</c>

00:00:13.589 --> 00:00:13.599 align:start position:0%
engineering all the way to
 

00:00:13.599 --> 00:00:15.749 align:start position:0%
engineering all the way to
transportation<00:00:14.639><c> which</c><00:00:15.040><c> is</c><00:00:15.200><c> what</c><00:00:15.440><c> i'm</c><00:00:15.599><c> going</c>

00:00:15.749 --> 00:00:15.759 align:start position:0%
transportation which is what i'm going
 

00:00:15.759 --> 00:00:17.750 align:start position:0%
transportation which is what i'm going
to<00:00:15.839><c> show</c><00:00:15.920><c> you</c><00:00:16.080><c> as</c><00:00:16.160><c> our</c><00:00:16.240><c> first</c><00:00:16.480><c> example</c><00:00:17.119><c> alright</c>

00:00:17.750 --> 00:00:17.760 align:start position:0%
to show you as our first example alright
 

00:00:17.760 --> 00:00:19.740 align:start position:0%
to show you as our first example alright
so<00:00:17.920><c> let's</c><00:00:18.080><c> get</c><00:00:18.320><c> started</c>

00:00:19.740 --> 00:00:19.750 align:start position:0%
so let's get started
 

00:00:19.750 --> 00:00:24.070 align:start position:0%
so let's get started
[Music]

00:00:24.070 --> 00:00:24.080 align:start position:0%
 
 

00:00:24.080 --> 00:00:25.670 align:start position:0%
 
let's<00:00:24.240><c> take</c><00:00:24.480><c> the</c><00:00:24.560><c> map</c><00:00:24.800><c> of</c><00:00:24.960><c> florida</c><00:00:25.439><c> and</c><00:00:25.519><c> some</c>

00:00:25.670 --> 00:00:25.680 align:start position:0%
let's take the map of florida and some
 

00:00:25.680 --> 00:00:27.429 align:start position:0%
let's take the map of florida and some
of<00:00:25.840><c> its</c><00:00:25.920><c> major</c><00:00:26.240><c> cities</c><00:00:26.560><c> and</c><00:00:26.640><c> turn</c><00:00:26.880><c> it</c><00:00:26.960><c> into</c><00:00:27.279><c> a</c>

00:00:27.429 --> 00:00:27.439 align:start position:0%
of its major cities and turn it into a
 

00:00:27.439 --> 00:00:28.390 align:start position:0%
of its major cities and turn it into a
graph

00:00:28.390 --> 00:00:28.400 align:start position:0%
graph
 

00:00:28.400 --> 00:00:31.109 align:start position:0%
graph
the<00:00:28.640><c> circle</c><00:00:29.039><c> shapes</c><00:00:29.439><c> are</c><00:00:29.599><c> called</c><00:00:30.000><c> vertices</c><00:00:30.960><c> or</c>

00:00:31.109 --> 00:00:31.119 align:start position:0%
the circle shapes are called vertices or
 

00:00:31.119 --> 00:00:33.430 align:start position:0%
the circle shapes are called vertices or
if<00:00:31.279><c> there's</c><00:00:31.439><c> one</c><00:00:31.760><c> vertex</c><00:00:32.640><c> and</c><00:00:32.880><c> they</c><00:00:33.040><c> store</c><00:00:33.280><c> the</c>

00:00:33.430 --> 00:00:33.440 align:start position:0%
if there's one vertex and they store the
 

00:00:33.440 --> 00:00:35.670 align:start position:0%
if there's one vertex and they store the
data<00:00:33.840><c> of</c><00:00:34.000><c> something</c><00:00:34.559><c> in</c><00:00:34.719><c> this</c><00:00:34.960><c> case</c><00:00:35.360><c> it's</c><00:00:35.520><c> the</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
data of something in this case it's the
 

00:00:35.680 --> 00:00:37.430 align:start position:0%
data of something in this case it's the
name<00:00:36.000><c> of</c><00:00:36.079><c> the</c><00:00:36.239><c> city</c>

00:00:37.430 --> 00:00:37.440 align:start position:0%
name of the city
 

00:00:37.440 --> 00:00:39.430 align:start position:0%
name of the city
we<00:00:37.600><c> connect</c><00:00:37.920><c> the</c><00:00:38.000><c> vertices</c><00:00:38.640><c> to</c><00:00:38.879><c> each</c><00:00:39.040><c> other</c>

00:00:39.430 --> 00:00:39.440 align:start position:0%
we connect the vertices to each other
 

00:00:39.440 --> 00:00:41.590 align:start position:0%
we connect the vertices to each other
with<00:00:39.840><c> lines</c><00:00:40.239><c> called</c><00:00:40.640><c> edges</c><00:00:41.280><c> we</c><00:00:41.440><c> have</c>

00:00:41.590 --> 00:00:41.600 align:start position:0%
with lines called edges we have
 

00:00:41.600 --> 00:00:43.910 align:start position:0%
with lines called edges we have
something<00:00:41.920><c> called</c><00:00:42.239><c> directed</c><00:00:42.879><c> and</c><00:00:43.200><c> undirected</c>

00:00:43.910 --> 00:00:43.920 align:start position:0%
something called directed and undirected
 

00:00:43.920 --> 00:00:46.150 align:start position:0%
something called directed and undirected
graphs<00:00:44.559><c> here's</c><00:00:44.879><c> an</c><00:00:45.120><c> undirected</c><00:00:45.680><c> graph</c>

00:00:46.150 --> 00:00:46.160 align:start position:0%
graphs here's an undirected graph
 

00:00:46.160 --> 00:00:48.389 align:start position:0%
graphs here's an undirected graph
because<00:00:46.399><c> we</c><00:00:46.559><c> aren't</c><00:00:46.800><c> using</c><00:00:47.120><c> arrows</c><00:00:47.680><c> to</c><00:00:47.840><c> denote</c>

00:00:48.389 --> 00:00:48.399 align:start position:0%
because we aren't using arrows to denote
 

00:00:48.399 --> 00:00:50.389 align:start position:0%
because we aren't using arrows to denote
one-way<00:00:48.960><c> relationships</c><00:00:49.680><c> between</c><00:00:50.239><c> the</c>

00:00:50.389 --> 00:00:50.399 align:start position:0%
one-way relationships between the
 

00:00:50.399 --> 00:00:52.709 align:start position:0%
one-way relationships between the
vertices<00:00:51.520><c> this</c><00:00:51.760><c> is</c><00:00:51.840><c> an</c><00:00:52.000><c> example</c><00:00:52.480><c> of</c><00:00:52.640><c> a</c>

00:00:52.709 --> 00:00:52.719 align:start position:0%
vertices this is an example of a
 

00:00:52.719 --> 00:00:55.590 align:start position:0%
vertices this is an example of a
directed<00:00:53.199><c> graph</c><00:00:53.920><c> because</c><00:00:54.399><c> we</c><00:00:54.640><c> only</c><00:00:54.879><c> go</c><00:00:55.120><c> in</c><00:00:55.280><c> one</c>

00:00:55.590 --> 00:00:55.600 align:start position:0%
directed graph because we only go in one
 

00:00:55.600 --> 00:00:58.470 align:start position:0%
directed graph because we only go in one
direction<00:00:56.239><c> from</c><00:00:56.879><c> each</c><00:00:57.199><c> city</c>

00:00:58.470 --> 00:00:58.480 align:start position:0%
direction from each city
 

00:00:58.480 --> 00:01:00.869 align:start position:0%
direction from each city
these<00:00:58.800><c> edges</c><00:00:59.280><c> typically</c><00:00:59.680><c> have</c><00:00:59.840><c> a</c><00:01:00.000><c> value</c><00:01:00.559><c> or</c>

00:01:00.869 --> 00:01:00.879 align:start position:0%
these edges typically have a value or
 

00:01:00.879 --> 00:01:03.430 align:start position:0%
these edges typically have a value or
what<00:01:01.120><c> can</c><00:01:01.280><c> be</c><00:01:01.440><c> called</c><00:01:01.840><c> weight</c><00:01:02.559><c> so</c><00:01:02.800><c> from</c><00:01:03.039><c> tampa</c>

00:01:03.430 --> 00:01:03.440 align:start position:0%
what can be called weight so from tampa
 

00:01:03.440 --> 00:01:05.990 align:start position:0%
what can be called weight so from tampa
to<00:01:03.600><c> miami</c><00:01:04.559><c> we</c><00:01:04.720><c> denoted</c><00:01:05.119><c> the</c><00:01:05.280><c> weight</c><00:01:05.680><c> to</c><00:01:05.840><c> be</c>

00:01:05.990 --> 00:01:06.000 align:start position:0%
to miami we denoted the weight to be
 

00:01:06.000 --> 00:01:08.710 align:start position:0%
to miami we denoted the weight to be
400.<00:01:07.119><c> in</c><00:01:07.280><c> our</c><00:01:07.439><c> example</c><00:01:07.920><c> this</c><00:01:08.159><c> references</c><00:01:08.640><c> the</c>

00:01:08.710 --> 00:01:08.720 align:start position:0%
400. in our example this references the
 

00:01:08.720 --> 00:01:10.630 align:start position:0%
400. in our example this references the
number<00:01:08.960><c> of</c><00:01:09.119><c> miles</c><00:01:09.439><c> to</c><00:01:09.600><c> get</c><00:01:09.840><c> from</c><00:01:10.080><c> tampa</c><00:01:10.400><c> to</c>

00:01:10.630 --> 00:01:10.640 align:start position:0%
number of miles to get from tampa to
 

00:01:10.640 --> 00:01:12.310 align:start position:0%
number of miles to get from tampa to
miami<00:01:11.439><c> let's</c><00:01:11.600><c> go</c><00:01:11.760><c> over</c><00:01:11.840><c> some</c><00:01:12.000><c> more</c><00:01:12.159><c> of</c><00:01:12.240><c> the</c>

00:01:12.310 --> 00:01:12.320 align:start position:0%
miami let's go over some more of the
 

00:01:12.320 --> 00:01:13.670 align:start position:0%
miami let's go over some more of the
terminology

00:01:13.670 --> 00:01:13.680 align:start position:0%
terminology
 

00:01:13.680 --> 00:01:15.990 align:start position:0%
terminology
endpoints<00:01:14.560><c> are</c><00:01:14.799><c> just</c><00:01:15.040><c> a</c><00:01:15.200><c> pair</c><00:01:15.360><c> of</c><00:01:15.520><c> vertices</c>

00:01:15.990 --> 00:01:16.000 align:start position:0%
endpoints are just a pair of vertices
 

00:01:16.000 --> 00:01:19.030 align:start position:0%
endpoints are just a pair of vertices
that<00:01:16.159><c> are</c><00:01:16.320><c> connected</c><00:01:16.880><c> to</c><00:01:17.520><c> the</c><00:01:17.840><c> same</c><00:01:18.240><c> edge</c><00:01:18.799><c> for</c>

00:01:19.030 --> 00:01:19.040 align:start position:0%
that are connected to the same edge for
 

00:01:19.040 --> 00:01:21.910 align:start position:0%
that are connected to the same edge for
adjacent<00:01:19.439><c> vertices</c><00:01:20.400><c> a</c><00:01:20.560><c> vertex</c><00:01:21.119><c> is</c><00:01:21.280><c> considered</c>

00:01:21.910 --> 00:01:21.920 align:start position:0%
adjacent vertices a vertex is considered
 

00:01:21.920 --> 00:01:25.030 align:start position:0%
adjacent vertices a vertex is considered
adjacent<00:01:22.479><c> to</c><00:01:22.640><c> another</c><00:01:23.040><c> vertex</c><00:01:23.759><c> if</c><00:01:24.400><c> they</c><00:01:24.560><c> both</c>

00:01:25.030 --> 00:01:25.040 align:start position:0%
adjacent to another vertex if they both
 

00:01:25.040 --> 00:01:27.109 align:start position:0%
adjacent to another vertex if they both
share<00:01:25.360><c> the</c><00:01:25.600><c> same</c><00:01:26.000><c> edge</c>

00:01:27.109 --> 00:01:27.119 align:start position:0%
share the same edge
 

00:01:27.119 --> 00:01:29.109 align:start position:0%
share the same edge
so<00:01:27.439><c> for</c><00:01:27.600><c> an</c><00:01:27.840><c> incident</c><00:01:28.320><c> edge</c>

00:01:29.109 --> 00:01:29.119 align:start position:0%
so for an incident edge
 

00:01:29.119 --> 00:01:31.670 align:start position:0%
so for an incident edge
an<00:01:29.360><c> edge</c><00:01:29.600><c> is</c><00:01:29.680><c> said</c><00:01:29.920><c> to</c><00:01:30.159><c> be</c><00:01:30.560><c> incident</c><00:01:31.200><c> to</c><00:01:31.439><c> a</c>

00:01:31.670 --> 00:01:31.680 align:start position:0%
an edge is said to be incident to a
 

00:01:31.680 --> 00:01:32.870 align:start position:0%
an edge is said to be incident to a
vertex

00:01:32.870 --> 00:01:32.880 align:start position:0%
vertex
 

00:01:32.880 --> 00:01:35.109 align:start position:0%
vertex
if<00:01:33.200><c> that</c><00:01:33.439><c> vertex</c><00:01:33.920><c> is</c><00:01:34.079><c> one</c><00:01:34.320><c> of</c><00:01:34.560><c> the</c><00:01:34.640><c> edge's</c>

00:01:35.109 --> 00:01:35.119 align:start position:0%
if that vertex is one of the edge's
 

00:01:35.119 --> 00:01:37.350 align:start position:0%
if that vertex is one of the edge's
endpoints<00:01:35.759><c> and</c><00:01:35.920><c> we</c><00:01:36.079><c> would</c><00:01:36.159><c> say</c><00:01:36.400><c> 225</c><00:01:37.119><c> is</c>

00:01:37.350 --> 00:01:37.360 align:start position:0%
endpoints and we would say 225 is
 

00:01:37.360 --> 00:01:40.469 align:start position:0%
endpoints and we would say 225 is
incident<00:01:37.759><c> to</c><00:01:37.920><c> jacksonville</c><00:01:38.880><c> but</c><00:01:39.119><c> 225</c><00:01:39.920><c> is</c><00:01:40.079><c> not</c>

00:01:40.469 --> 00:01:40.479 align:start position:0%
incident to jacksonville but 225 is not
 

00:01:40.479 --> 00:01:42.630 align:start position:0%
incident to jacksonville but 225 is not
incident<00:01:40.880><c> to</c><00:01:41.040><c> orlando</c><00:01:41.759><c> because</c><00:01:42.000><c> orlando</c><00:01:42.479><c> is</c>

00:01:42.630 --> 00:01:42.640 align:start position:0%
incident to orlando because orlando is
 

00:01:42.640 --> 00:01:43.830 align:start position:0%
incident to orlando because orlando is
not<00:01:42.880><c> one</c><00:01:43.119><c> of</c>

00:01:43.830 --> 00:01:43.840 align:start position:0%
not one of
 

00:01:43.840 --> 00:01:46.230 align:start position:0%
not one of
its<00:01:44.000><c> endpoints</c><00:01:44.640><c> for</c><00:01:45.040><c> outgoing</c><00:01:45.439><c> edges</c><00:01:45.920><c> this</c><00:01:46.159><c> is</c>

00:01:46.230 --> 00:01:46.240 align:start position:0%
its endpoints for outgoing edges this is
 

00:01:46.240 --> 00:01:49.190 align:start position:0%
its endpoints for outgoing edges this is
just<00:01:46.560><c> all</c><00:01:46.799><c> of</c><00:01:46.880><c> the</c><00:01:47.119><c> edges</c><00:01:48.000><c> out</c><00:01:48.479><c> going</c><00:01:48.880><c> from</c><00:01:49.119><c> a</c>

00:01:49.190 --> 00:01:49.200 align:start position:0%
just all of the edges out going from a
 

00:01:49.200 --> 00:01:51.990 align:start position:0%
just all of the edges out going from a
vertex<00:01:49.840><c> okay</c><00:01:50.159><c> so</c><00:01:50.640><c> if</c><00:01:50.799><c> we</c><00:01:50.960><c> take</c><00:01:51.119><c> tallahassee</c><00:01:51.920><c> it</c>

00:01:51.990 --> 00:01:52.000 align:start position:0%
vertex okay so if we take tallahassee it
 

00:01:52.000 --> 00:01:54.710 align:start position:0%
vertex okay so if we take tallahassee it
only<00:01:52.240><c> has</c><00:01:52.479><c> one</c><00:01:52.799><c> outgoing</c><00:01:53.600><c> edge</c><00:01:54.240><c> incoming</c>

00:01:54.710 --> 00:01:54.720 align:start position:0%
only has one outgoing edge incoming
 

00:01:54.720 --> 00:01:57.030 align:start position:0%
only has one outgoing edge incoming
edges<00:01:55.200><c> is</c><00:01:55.439><c> the</c><00:01:55.600><c> opposite</c><00:01:56.079><c> so</c><00:01:56.320><c> for</c><00:01:56.479><c> tallahassee</c>

00:01:57.030 --> 00:01:57.040 align:start position:0%
edges is the opposite so for tallahassee
 

00:01:57.040 --> 00:02:00.230 align:start position:0%
edges is the opposite so for tallahassee
again<00:01:57.680><c> it</c><00:01:57.840><c> has</c><00:01:58.240><c> one</c><00:01:58.640><c> incoming</c><00:01:59.200><c> edge</c><00:01:59.840><c> which</c><00:02:00.079><c> is</c>

00:02:00.230 --> 00:02:00.240 align:start position:0%
again it has one incoming edge which is
 

00:02:00.240 --> 00:02:02.469 align:start position:0%
again it has one incoming edge which is
the<00:02:00.399><c> one</c><00:02:00.560><c> denoted</c><00:02:00.960><c> with</c><00:02:01.040><c> the</c><00:02:01.119><c> value</c><00:02:01.360><c> of</c><00:02:01.520><c> 300.</c>

00:02:02.469 --> 00:02:02.479 align:start position:0%
the one denoted with the value of 300.
 

00:02:02.479 --> 00:02:05.109 align:start position:0%
the one denoted with the value of 300.
the<00:02:02.640><c> degree</c><00:02:03.119><c> of</c><00:02:03.360><c> a</c><00:02:03.520><c> vertex</c><00:02:04.320><c> is</c><00:02:04.479><c> the</c><00:02:04.719><c> total</c>

00:02:05.109 --> 00:02:05.119 align:start position:0%
the degree of a vertex is the total
 

00:02:05.119 --> 00:02:07.670 align:start position:0%
the degree of a vertex is the total
number<00:02:05.360><c> of</c><00:02:05.680><c> incoming</c><00:02:06.159><c> outgoing</c><00:02:06.719><c> edges</c><00:02:07.439><c> so</c><00:02:07.600><c> if</c>

00:02:07.670 --> 00:02:07.680 align:start position:0%
number of incoming outgoing edges so if
 

00:02:07.680 --> 00:02:11.110 align:start position:0%
number of incoming outgoing edges so if
we<00:02:07.920><c> take</c><00:02:08.160><c> orlando</c><00:02:09.119><c> this</c><00:02:09.360><c> has</c><00:02:09.520><c> a</c><00:02:09.599><c> total</c><00:02:10.000><c> of</c><00:02:10.399><c> five</c>

00:02:11.110 --> 00:02:11.120 align:start position:0%
we take orlando this has a total of five
 

00:02:11.120 --> 00:02:13.350 align:start position:0%
we take orlando this has a total of five
because<00:02:11.440><c> it</c><00:02:11.520><c> has</c><00:02:11.840><c> two</c><00:02:12.160><c> outgoing</c><00:02:12.800><c> and</c><00:02:12.959><c> three</c>

00:02:13.350 --> 00:02:13.360 align:start position:0%
because it has two outgoing and three
 

00:02:13.360 --> 00:02:16.150 align:start position:0%
because it has two outgoing and three
incoming<00:02:13.840><c> edges</c><00:02:14.640><c> the</c><00:02:14.879><c> in</c><00:02:14.959><c> degree</c><00:02:15.200><c> of</c><00:02:15.280><c> a</c><00:02:15.360><c> vertex</c>

00:02:16.150 --> 00:02:16.160 align:start position:0%
incoming edges the in degree of a vertex
 

00:02:16.160 --> 00:02:17.910 align:start position:0%
incoming edges the in degree of a vertex
is<00:02:16.319><c> just</c><00:02:16.640><c> the</c><00:02:16.800><c> total</c><00:02:17.040><c> number</c><00:02:17.280><c> of</c><00:02:17.520><c> incoming</c>

00:02:17.910 --> 00:02:17.920 align:start position:0%
is just the total number of incoming
 

00:02:17.920 --> 00:02:19.990 align:start position:0%
is just the total number of incoming
edges<00:02:18.160><c> of</c><00:02:18.239><c> a</c><00:02:18.319><c> vertex</c><00:02:18.959><c> so</c><00:02:19.360><c> in</c><00:02:19.520><c> this</c><00:02:19.680><c> instance</c>

00:02:19.990 --> 00:02:20.000 align:start position:0%
edges of a vertex so in this instance
 

00:02:20.000 --> 00:02:22.630 align:start position:0%
edges of a vertex so in this instance
with<00:02:20.160><c> orlando</c><00:02:21.040><c> it</c><00:02:21.200><c> has</c><00:02:21.440><c> three</c><00:02:21.680><c> which</c><00:02:22.000><c> is</c><00:02:22.480><c> the</c>

00:02:22.630 --> 00:02:22.640 align:start position:0%
with orlando it has three which is the
 

00:02:22.640 --> 00:02:26.949 align:start position:0%
with orlando it has three which is the
100<00:02:23.440><c> 75</c><00:02:24.080><c> and</c><00:02:24.239><c> 150</c><00:02:25.200><c> valued</c><00:02:25.680><c> edges</c><00:02:26.480><c> the</c><00:02:26.720><c> out</c>

00:02:26.949 --> 00:02:26.959 align:start position:0%
100 75 and 150 valued edges the out
 

00:02:26.959 --> 00:02:28.150 align:start position:0%
100 75 and 150 valued edges the out
degree

00:02:28.150 --> 00:02:28.160 align:start position:0%
degree
 

00:02:28.160 --> 00:02:30.229 align:start position:0%
degree
is<00:02:28.400><c> just</c><00:02:28.560><c> the</c><00:02:28.720><c> opposite</c><00:02:29.599><c> this</c><00:02:29.840><c> is</c><00:02:29.920><c> a</c><00:02:30.000><c> total</c>

00:02:30.229 --> 00:02:30.239 align:start position:0%
is just the opposite this is a total
 

00:02:30.239 --> 00:02:32.070 align:start position:0%
is just the opposite this is a total
number<00:02:30.480><c> of</c><00:02:30.640><c> outgoing</c><00:02:31.120><c> edges</c><00:02:31.360><c> from</c><00:02:31.519><c> a</c><00:02:31.599><c> vertex</c>

00:02:32.070 --> 00:02:32.080 align:start position:0%
number of outgoing edges from a vertex
 

00:02:32.080 --> 00:02:34.949 align:start position:0%
number of outgoing edges from a vertex
so<00:02:32.480><c> orlando</c><00:02:33.040><c> has</c><00:02:33.440><c> two</c><00:02:34.080><c> as</c>

00:02:34.949 --> 00:02:34.959 align:start position:0%
so orlando has two as
 

00:02:34.959 --> 00:02:38.229 align:start position:0%
so orlando has two as
one<00:02:35.200><c> edge</c><00:02:35.360><c> as</c><00:02:35.599><c> 50</c><00:02:36.319><c> and</c><00:02:36.480><c> another</c><00:02:36.720><c> edge</c><00:02:36.959><c> has</c><00:02:37.120><c> 300</c>

00:02:38.229 --> 00:02:38.239 align:start position:0%
one edge as 50 and another edge has 300
 

00:02:38.239 --> 00:02:40.390 align:start position:0%
one edge as 50 and another edge has 300
a<00:02:38.480><c> path</c><00:02:38.879><c> is</c><00:02:39.040><c> a</c><00:02:39.120><c> sequence</c><00:02:39.599><c> of</c><00:02:39.760><c> vertices</c><00:02:40.239><c> and</c>

00:02:40.390 --> 00:02:40.400 align:start position:0%
a path is a sequence of vertices and
 

00:02:40.400 --> 00:02:43.110 align:start position:0%
a path is a sequence of vertices and
edges<00:02:40.720><c> starting</c><00:02:41.120><c> at</c><00:02:41.200><c> one</c><00:02:41.440><c> vertex</c><00:02:42.239><c> and</c><00:02:42.480><c> ending</c>

00:02:43.110 --> 00:02:43.120 align:start position:0%
edges starting at one vertex and ending
 

00:02:43.120 --> 00:02:45.750 align:start position:0%
edges starting at one vertex and ending
at<00:02:43.360><c> another</c><00:02:44.239><c> so</c><00:02:44.480><c> we</c><00:02:44.640><c> could</c><00:02:44.800><c> say</c>

00:02:45.750 --> 00:02:45.760 align:start position:0%
at another so we could say
 

00:02:45.760 --> 00:02:49.270 align:start position:0%
at another so we could say
miami<00:02:46.800><c> to</c><00:02:46.959><c> palm</c><00:02:47.200><c> beach</c><00:02:47.519><c> to</c><00:02:47.680><c> orlando</c><00:02:48.560><c> is</c><00:02:48.720><c> a</c><00:02:48.879><c> path</c>

00:02:49.270 --> 00:02:49.280 align:start position:0%
miami to palm beach to orlando is a path
 

00:02:49.280 --> 00:02:51.430 align:start position:0%
miami to palm beach to orlando is a path
and<00:02:49.440><c> there's</c><00:02:49.680><c> two</c><00:02:49.920><c> edges</c><00:02:50.239><c> along</c><00:02:50.480><c> the</c><00:02:50.640><c> way</c>

00:02:51.430 --> 00:02:51.440 align:start position:0%
and there's two edges along the way
 

00:02:51.440 --> 00:02:53.509 align:start position:0%
and there's two edges along the way
so<00:02:51.599><c> you</c><00:02:51.760><c> would</c><00:02:51.920><c> make</c><00:02:52.080><c> sure</c><00:02:52.239><c> you</c><00:02:52.400><c> say</c><00:02:52.800><c> miami</c><00:02:53.360><c> to</c>

00:02:53.509 --> 00:02:53.519 align:start position:0%
so you would make sure you say miami to
 

00:02:53.519 --> 00:02:55.589 align:start position:0%
so you would make sure you say miami to
palm<00:02:53.840><c> beach</c><00:02:54.080><c> is</c><00:02:54.239><c> 120</c><00:02:54.720><c> miles</c><00:02:55.040><c> and</c><00:02:55.120><c> then</c><00:02:55.360><c> palm</c>

00:02:55.589 --> 00:02:55.599 align:start position:0%
palm beach is 120 miles and then palm
 

00:02:55.599 --> 00:02:58.470 align:start position:0%
palm beach is 120 miles and then palm
beach<00:02:55.840><c> to</c><00:02:56.000><c> orlando</c><00:02:56.319><c> is</c><00:02:56.400><c> 75</c><00:02:56.879><c> miles</c><00:02:57.440><c> a</c><00:02:57.680><c> cycle</c><00:02:58.319><c> is</c>

00:02:58.470 --> 00:02:58.480 align:start position:0%
beach to orlando is 75 miles a cycle is
 

00:02:58.480 --> 00:03:00.710 align:start position:0%
beach to orlando is 75 miles a cycle is
a<00:02:58.560><c> path</c><00:02:58.879><c> that</c><00:02:58.959><c> starts</c><00:02:59.360><c> and</c><00:02:59.519><c> ends</c><00:02:59.920><c> at</c><00:03:00.000><c> the</c><00:03:00.239><c> same</c>

00:03:00.710 --> 00:03:00.720 align:start position:0%
a path that starts and ends at the same
 

00:03:00.720 --> 00:03:03.750 align:start position:0%
a path that starts and ends at the same
vertex<00:03:01.519><c> so</c><00:03:01.840><c> we</c><00:03:02.000><c> can</c><00:03:02.159><c> begin</c><00:03:02.480><c> at</c><00:03:02.560><c> miami</c><00:03:03.440><c> go</c><00:03:03.599><c> to</c>

00:03:03.750 --> 00:03:03.760 align:start position:0%
vertex so we can begin at miami go to
 

00:03:03.760 --> 00:03:06.229 align:start position:0%
vertex so we can begin at miami go to
palm<00:03:04.080><c> beach</c><00:03:04.640><c> then</c><00:03:04.800><c> go</c><00:03:04.959><c> to</c><00:03:05.040><c> orlando</c><00:03:05.760><c> then</c><00:03:06.000><c> go</c><00:03:06.159><c> to</c>

00:03:06.229 --> 00:03:06.239 align:start position:0%
palm beach then go to orlando then go to
 

00:03:06.239 --> 00:03:08.070 align:start position:0%
palm beach then go to orlando then go to
tampa<00:03:06.640><c> and</c><00:03:06.720><c> then</c><00:03:06.879><c> come</c><00:03:07.120><c> back</c><00:03:07.280><c> down</c><00:03:07.440><c> to</c><00:03:07.599><c> miami</c>

00:03:08.070 --> 00:03:08.080 align:start position:0%
tampa and then come back down to miami
 

00:03:08.080 --> 00:03:09.509 align:start position:0%
tampa and then come back down to miami
and<00:03:08.159><c> that's</c><00:03:08.480><c> a</c><00:03:08.640><c> cycle</c>

00:03:09.509 --> 00:03:09.519 align:start position:0%
and that's a cycle
 

00:03:09.519 --> 00:03:10.790 align:start position:0%
and that's a cycle
all<00:03:09.599><c> right</c><00:03:09.760><c> so</c><00:03:09.920><c> i</c><00:03:10.000><c> know</c><00:03:10.159><c> there</c><00:03:10.319><c> was</c><00:03:10.400><c> a</c><00:03:10.560><c> lot</c><00:03:10.720><c> of</c>

00:03:10.790 --> 00:03:10.800 align:start position:0%
all right so i know there was a lot of
 

00:03:10.800 --> 00:03:12.149 align:start position:0%
all right so i know there was a lot of
terminology

00:03:12.149 --> 00:03:12.159 align:start position:0%
terminology
 

00:03:12.159 --> 00:03:14.390 align:start position:0%
terminology
that<00:03:12.319><c> we</c><00:03:12.560><c> just</c><00:03:12.959><c> went</c><00:03:13.200><c> over</c><00:03:13.599><c> and</c><00:03:13.760><c> the</c><00:03:13.920><c> reason</c><00:03:14.319><c> i</c>

00:03:14.390 --> 00:03:14.400 align:start position:0%
that we just went over and the reason i
 

00:03:14.400 --> 00:03:16.149 align:start position:0%
that we just went over and the reason i
went<00:03:14.640><c> over</c><00:03:14.800><c> that</c><00:03:14.959><c> is</c><00:03:15.120><c> because</c><00:03:15.440><c> it's</c><00:03:15.840><c> actually</c>

00:03:16.149 --> 00:03:16.159 align:start position:0%
went over that is because it's actually
 

00:03:16.159 --> 00:03:18.630 align:start position:0%
went over that is because it's actually
used<00:03:16.560><c> with</c><00:03:16.800><c> graphs</c><00:03:17.360><c> and</c><00:03:17.760><c> when</c><00:03:18.000><c> we</c><00:03:18.080><c> go</c><00:03:18.239><c> to</c><00:03:18.400><c> code</c>

00:03:18.630 --> 00:03:18.640 align:start position:0%
used with graphs and when we go to code
 

00:03:18.640 --> 00:03:20.550 align:start position:0%
used with graphs and when we go to code
this<00:03:18.879><c> the</c><00:03:18.959><c> next</c><00:03:19.360><c> video</c>

00:03:20.550 --> 00:03:20.560 align:start position:0%
this the next video
 

00:03:20.560 --> 00:03:22.390 align:start position:0%
this the next video
some<00:03:20.720><c> of</c><00:03:20.879><c> the</c><00:03:21.120><c> method</c><00:03:21.519><c> names</c><00:03:21.920><c> are</c><00:03:22.080><c> going</c><00:03:22.159><c> to</c><00:03:22.239><c> be</c>

00:03:22.390 --> 00:03:22.400 align:start position:0%
some of the method names are going to be
 

00:03:22.400 --> 00:03:25.589 align:start position:0%
some of the method names are going to be
derived<00:03:23.040><c> from</c><00:03:23.519><c> this</c><00:03:23.760><c> terminology</c><00:03:24.640><c> okay</c><00:03:24.959><c> and</c>

00:03:25.589 --> 00:03:25.599 align:start position:0%
derived from this terminology okay and
 

00:03:25.599 --> 00:03:27.430 align:start position:0%
derived from this terminology okay and
the<00:03:25.840><c> definitions</c><00:03:26.400><c> are</c><00:03:26.480><c> pretty</c><00:03:26.720><c> much</c>

00:03:27.430 --> 00:03:27.440 align:start position:0%
the definitions are pretty much
 

00:03:27.440 --> 00:03:29.670 align:start position:0%
the definitions are pretty much
what<00:03:27.599><c> we're</c><00:03:27.840><c> going</c><00:03:28.000><c> to</c><00:03:28.080><c> be</c><00:03:28.239><c> coding</c><00:03:28.640><c> for</c>

00:03:29.670 --> 00:03:29.680 align:start position:0%
what we're going to be coding for
 

00:03:29.680 --> 00:03:31.830 align:start position:0%
what we're going to be coding for
when<00:03:29.840><c> we</c><00:03:30.000><c> get</c><00:03:30.239><c> there</c><00:03:30.879><c> so</c><00:03:31.040><c> do</c><00:03:31.200><c> the</c><00:03:31.280><c> best</c><00:03:31.519><c> you</c><00:03:31.680><c> can</c>

00:03:31.830 --> 00:03:31.840 align:start position:0%
when we get there so do the best you can
 

00:03:31.840 --> 00:03:34.630 align:start position:0%
when we get there so do the best you can
to<00:03:32.000><c> understand</c><00:03:32.400><c> them</c><00:03:33.040><c> and</c><00:03:33.599><c> in</c><00:03:33.840><c> order</c><00:03:34.080><c> to</c><00:03:34.319><c> even</c>

00:03:34.630 --> 00:03:34.640 align:start position:0%
to understand them and in order to even
 

00:03:34.640 --> 00:03:36.149 align:start position:0%
to understand them and in order to even
code<00:03:34.879><c> we</c><00:03:35.040><c> have</c><00:03:35.120><c> to</c><00:03:35.280><c> understand</c><00:03:35.840><c> that</c><00:03:36.000><c> there</c>

00:03:36.149 --> 00:03:36.159 align:start position:0%
code we have to understand that there
 

00:03:36.159 --> 00:03:38.869 align:start position:0%
code we have to understand that there
are<00:03:36.239><c> different</c><00:03:36.560><c> implementations</c><00:03:37.519><c> of</c><00:03:37.840><c> a</c><00:03:38.080><c> graph</c>

00:03:38.869 --> 00:03:38.879 align:start position:0%
are different implementations of a graph
 

00:03:38.879 --> 00:03:40.789 align:start position:0%
are different implementations of a graph
we're<00:03:39.040><c> going</c><00:03:39.120><c> to</c><00:03:39.200><c> go</c><00:03:39.360><c> over</c><00:03:39.680><c> three</c><00:03:40.319><c> and</c><00:03:40.560><c> then</c>

00:03:40.789 --> 00:03:40.799 align:start position:0%
we're going to go over three and then
 

00:03:40.799 --> 00:03:42.470 align:start position:0%
we're going to go over three and then
i'll<00:03:41.040><c> explain</c><00:03:41.440><c> why</c><00:03:41.599><c> we're</c><00:03:41.680><c> going</c><00:03:41.840><c> to</c><00:03:42.000><c> code</c><00:03:42.319><c> one</c>

00:03:42.470 --> 00:03:42.480 align:start position:0%
i'll explain why we're going to code one
 

00:03:42.480 --> 00:03:45.270 align:start position:0%
i'll explain why we're going to code one
of<00:03:42.640><c> them</c><00:03:42.959><c> for</c><00:03:43.440><c> the</c><00:03:43.599><c> next</c><00:03:43.920><c> video</c><00:03:44.720><c> so</c><00:03:44.879><c> the</c><00:03:45.040><c> first</c>

00:03:45.270 --> 00:03:45.280 align:start position:0%
of them for the next video so the first
 

00:03:45.280 --> 00:03:47.750 align:start position:0%
of them for the next video so the first
one<00:03:45.440><c> is</c><00:03:45.599><c> the</c><00:03:45.760><c> adjacency</c><00:03:46.400><c> list</c><00:03:46.640><c> structure</c><00:03:47.519><c> this</c>

00:03:47.750 --> 00:03:47.760 align:start position:0%
one is the adjacency list structure this
 

00:03:47.760 --> 00:03:50.949 align:start position:0%
one is the adjacency list structure this
is<00:03:48.239><c> uses</c><00:03:48.560><c> a</c><00:03:48.799><c> list</c><00:03:49.200><c> for</c><00:03:49.599><c> all</c><00:03:49.920><c> of</c><00:03:50.000><c> the</c><00:03:50.080><c> vertices</c>

00:03:50.949 --> 00:03:50.959 align:start position:0%
is uses a list for all of the vertices
 

00:03:50.959 --> 00:03:53.670 align:start position:0%
is uses a list for all of the vertices
then<00:03:51.280><c> each</c><00:03:51.599><c> vertex</c><00:03:52.159><c> has</c><00:03:52.400><c> another</c><00:03:52.799><c> list</c><00:03:53.200><c> of</c><00:03:53.360><c> all</c>

00:03:53.670 --> 00:03:53.680 align:start position:0%
then each vertex has another list of all
 

00:03:53.680 --> 00:03:55.509 align:start position:0%
then each vertex has another list of all
incident<00:03:54.239><c> edges</c>

00:03:55.509 --> 00:03:55.519 align:start position:0%
incident edges
 

00:03:55.519 --> 00:03:58.070 align:start position:0%
incident edges
or<00:03:56.000><c> any</c><00:03:56.239><c> edge</c><00:03:56.480><c> that</c><00:03:56.560><c> is</c><00:03:56.720><c> associated</c><00:03:57.200><c> with</c><00:03:57.920><c> if</c>

00:03:58.070 --> 00:03:58.080 align:start position:0%
or any edge that is associated with if
 

00:03:58.080 --> 00:03:59.750 align:start position:0%
or any edge that is associated with if
this<00:03:58.239><c> was</c><00:03:58.400><c> a</c><00:03:58.560><c> directed</c><00:03:59.120><c> graph</c><00:03:59.360><c> then</c><00:03:59.519><c> each</c>

00:03:59.750 --> 00:03:59.760 align:start position:0%
this was a directed graph then each
 

00:03:59.760 --> 00:04:01.750 align:start position:0%
this was a directed graph then each
vertex<00:04:00.560><c> would</c><00:04:00.720><c> have</c><00:04:00.959><c> a</c><00:04:01.120><c> list</c><00:04:01.439><c> for</c><00:04:01.599><c> the</c>

00:04:01.750 --> 00:04:01.760 align:start position:0%
vertex would have a list for the
 

00:04:01.760 --> 00:04:04.070 align:start position:0%
vertex would have a list for the
outgoing<00:04:02.239><c> edges</c><00:04:02.959><c> and</c><00:04:03.120><c> another</c><00:04:03.680><c> for</c><00:04:03.840><c> the</c>

00:04:04.070 --> 00:04:04.080 align:start position:0%
outgoing edges and another for the
 

00:04:04.080 --> 00:04:05.509 align:start position:0%
outgoing edges and another for the
incoming<00:04:04.560><c> edges</c>

00:04:05.509 --> 00:04:05.519 align:start position:0%
incoming edges
 

00:04:05.519 --> 00:04:07.190 align:start position:0%
incoming edges
so<00:04:05.760><c> here</c><00:04:06.000><c> we</c><00:04:06.159><c> have</c><00:04:06.480><c> a</c><00:04:06.640><c> list</c><00:04:06.879><c> of</c><00:04:06.959><c> all</c><00:04:07.120><c> the</c>

00:04:07.190 --> 00:04:07.200 align:start position:0%
so here we have a list of all the
 

00:04:07.200 --> 00:04:09.910 align:start position:0%
so here we have a list of all the
vertices<00:04:07.599><c> which</c><00:04:07.840><c> would</c><00:04:07.840><c> be</c><00:04:08.080><c> u</c><00:04:08.319><c> v</c><00:04:08.560><c> w</c><00:04:09.040><c> and</c><00:04:09.120><c> z</c>

00:04:09.910 --> 00:04:09.920 align:start position:0%
vertices which would be u v w and z
 

00:04:09.920 --> 00:04:11.110 align:start position:0%
vertices which would be u v w and z
and<00:04:10.080><c> then</c>

00:04:11.110 --> 00:04:11.120 align:start position:0%
and then
 

00:04:11.120 --> 00:04:13.509 align:start position:0%
and then
when<00:04:11.280><c> we</c><00:04:11.439><c> created</c><00:04:11.840><c> the</c><00:04:12.000><c> vertex</c><00:04:12.560><c> u</c><00:04:13.200><c> we</c><00:04:13.360><c> would</c>

00:04:13.509 --> 00:04:13.519 align:start position:0%
when we created the vertex u we would
 

00:04:13.519 --> 00:04:15.670 align:start position:0%
when we created the vertex u we would
also<00:04:13.840><c> have</c><00:04:14.000><c> another</c><00:04:14.480><c> list</c><00:04:15.280><c> which</c><00:04:15.519><c> would</c>

00:04:15.670 --> 00:04:15.680 align:start position:0%
also have another list which would
 

00:04:15.680 --> 00:04:18.469 align:start position:0%
also have another list which would
contain<00:04:16.400><c> edges</c><00:04:16.880><c> e</c><00:04:17.519><c> and</c><00:04:17.680><c> g</c>

00:04:18.469 --> 00:04:18.479 align:start position:0%
contain edges e and g
 

00:04:18.479 --> 00:04:20.229 align:start position:0%
contain edges e and g
now<00:04:18.720><c> moving</c><00:04:18.959><c> on</c><00:04:19.120><c> to</c><00:04:19.199><c> the</c><00:04:19.280><c> adjacency</c><00:04:19.919><c> map</c>

00:04:20.229 --> 00:04:20.239 align:start position:0%
now moving on to the adjacency map
 

00:04:20.239 --> 00:04:23.110 align:start position:0%
now moving on to the adjacency map
structure<00:04:21.120><c> so</c><00:04:21.280><c> with</c><00:04:21.440><c> the</c><00:04:21.519><c> list</c><00:04:21.759><c> structure</c>

00:04:23.110 --> 00:04:23.120 align:start position:0%
structure so with the list structure
 

00:04:23.120 --> 00:04:24.710 align:start position:0%
structure so with the list structure
the<00:04:23.360><c> edge</c><00:04:23.680><c> lists</c><00:04:23.919><c> for</c><00:04:24.000><c> the</c><00:04:24.160><c> vertex</c><00:04:24.639><c> are</c>

00:04:24.710 --> 00:04:24.720 align:start position:0%
the edge lists for the vertex are
 

00:04:24.720 --> 00:04:26.950 align:start position:0%
the edge lists for the vertex are
considered<00:04:25.440><c> unordered</c>

00:04:26.950 --> 00:04:26.960 align:start position:0%
considered unordered
 

00:04:26.960 --> 00:04:29.110 align:start position:0%
considered unordered
so<00:04:27.120><c> to</c><00:04:27.280><c> get</c><00:04:27.440><c> to</c><00:04:27.600><c> a</c><00:04:27.759><c> specific</c><00:04:28.320><c> edge</c><00:04:28.560><c> between</c><00:04:28.880><c> two</c>

00:04:29.110 --> 00:04:29.120 align:start position:0%
so to get to a specific edge between two
 

00:04:29.120 --> 00:04:31.749 align:start position:0%
so to get to a specific edge between two
vertices<00:04:29.840><c> you'd</c><00:04:30.080><c> have</c><00:04:30.240><c> to</c><00:04:30.400><c> traverse</c><00:04:30.880><c> through</c>

00:04:31.749 --> 00:04:31.759 align:start position:0%
vertices you'd have to traverse through
 

00:04:31.759 --> 00:04:33.430 align:start position:0%
vertices you'd have to traverse through
two<00:04:32.000><c> different</c><00:04:32.320><c> vertices</c>

00:04:33.430 --> 00:04:33.440 align:start position:0%
two different vertices
 

00:04:33.440 --> 00:04:35.110 align:start position:0%
two different vertices
edge<00:04:33.759><c> list</c><00:04:34.080><c> to</c><00:04:34.320><c> see</c>

00:04:35.110 --> 00:04:35.120 align:start position:0%
edge list to see
 

00:04:35.120 --> 00:04:37.830 align:start position:0%
edge list to see
which<00:04:35.360><c> one</c><00:04:35.600><c> is</c><00:04:35.680><c> connected</c><00:04:36.080><c> to</c><00:04:36.400><c> to</c><00:04:36.560><c> the</c><00:04:36.720><c> vertex</c>

00:04:37.830 --> 00:04:37.840 align:start position:0%
which one is connected to to the vertex
 

00:04:37.840 --> 00:04:40.469 align:start position:0%
which one is connected to to the vertex
we<00:04:38.000><c> can</c><00:04:38.240><c> improve</c><00:04:38.800><c> that</c><00:04:39.360><c> by</c><00:04:39.600><c> using</c><00:04:39.919><c> a</c><00:04:40.080><c> hash</c>

00:04:40.469 --> 00:04:40.479 align:start position:0%
we can improve that by using a hash
 

00:04:40.479 --> 00:04:42.710 align:start position:0%
we can improve that by using a hash
based<00:04:40.880><c> map</c><00:04:41.440><c> to</c><00:04:41.600><c> implement</c><00:04:42.000><c> the</c><00:04:42.160><c> edges</c><00:04:42.560><c> for</c>

00:04:42.710 --> 00:04:42.720 align:start position:0%
based map to implement the edges for
 

00:04:42.720 --> 00:04:45.110 align:start position:0%
based map to implement the edges for
each<00:04:43.040><c> vertex</c><00:04:43.840><c> well</c><00:04:44.240><c> we</c><00:04:44.400><c> will</c><00:04:44.639><c> still</c><00:04:44.800><c> have</c><00:04:44.960><c> a</c>

00:04:45.110 --> 00:04:45.120 align:start position:0%
each vertex well we will still have a
 

00:04:45.120 --> 00:04:47.110 align:start position:0%
each vertex well we will still have a
list<00:04:45.440><c> of</c><00:04:45.600><c> all</c><00:04:45.759><c> the</c><00:04:45.840><c> vertices</c><00:04:46.639><c> just</c><00:04:46.880><c> like</c><00:04:47.040><c> we</c>

00:04:47.110 --> 00:04:47.120 align:start position:0%
list of all the vertices just like we
 

00:04:47.120 --> 00:04:48.629 align:start position:0%
list of all the vertices just like we
did<00:04:47.280><c> in</c><00:04:47.360><c> the</c><00:04:47.440><c> list</c><00:04:47.600><c> structure</c>

00:04:48.629 --> 00:04:48.639 align:start position:0%
did in the list structure
 

00:04:48.639 --> 00:04:50.950 align:start position:0%
did in the list structure
but<00:04:48.960><c> instead</c><00:04:49.840><c> of</c><00:04:50.000><c> each</c><00:04:50.240><c> vertex</c><00:04:50.720><c> having</c>

00:04:50.950 --> 00:04:50.960 align:start position:0%
but instead of each vertex having
 

00:04:50.960 --> 00:04:52.950 align:start position:0%
but instead of each vertex having
another<00:04:51.280><c> list</c><00:04:51.520><c> of</c><00:04:51.600><c> all</c><00:04:51.680><c> the</c><00:04:51.840><c> edges</c><00:04:52.320><c> which</c><00:04:52.800><c> i</c>

00:04:52.950 --> 00:04:52.960 align:start position:0%
another list of all the edges which i
 

00:04:52.960 --> 00:04:55.350 align:start position:0%
another list of all the edges which i
said<00:04:53.360><c> again</c><00:04:53.759><c> are</c><00:04:54.000><c> unordered</c><00:04:54.639><c> we</c><00:04:54.800><c> will</c><00:04:54.960><c> have</c><00:04:55.120><c> a</c>

00:04:55.350 --> 00:04:55.360 align:start position:0%
said again are unordered we will have a
 

00:04:55.360 --> 00:04:57.430 align:start position:0%
said again are unordered we will have a
map<00:04:55.919><c> with</c><00:04:56.160><c> the</c><00:04:56.320><c> key</c><00:04:56.639><c> being</c><00:04:56.880><c> the</c><00:04:57.040><c> opposite</c>

00:04:57.430 --> 00:04:57.440 align:start position:0%
map with the key being the opposite
 

00:04:57.440 --> 00:05:00.230 align:start position:0%
map with the key being the opposite
vertex<00:04:57.919><c> and</c><00:04:58.080><c> the</c><00:04:58.240><c> value</c><00:04:58.560><c> being</c><00:04:58.880><c> the</c><00:04:59.120><c> edge</c><00:04:59.840><c> so</c>

00:05:00.230 --> 00:05:00.240 align:start position:0%
vertex and the value being the edge so
 

00:05:00.240 --> 00:05:02.390 align:start position:0%
vertex and the value being the edge so
in<00:05:00.320><c> this</c><00:05:00.560><c> diagram</c><00:05:01.120><c> if</c><00:05:01.280><c> we</c><00:05:01.440><c> had</c><00:05:01.759><c> a</c><00:05:01.919><c> list</c><00:05:02.160><c> of</c><00:05:02.240><c> all</c>

00:05:02.390 --> 00:05:02.400 align:start position:0%
in this diagram if we had a list of all
 

00:05:02.400 --> 00:05:05.830 align:start position:0%
in this diagram if we had a list of all
the<00:05:02.479><c> vertices</c><00:05:02.960><c> would</c><00:05:03.120><c> still</c><00:05:03.280><c> be</c><00:05:03.520><c> u</c><00:05:04.000><c> v</c><00:05:04.400><c> w</c><00:05:04.880><c> and</c><00:05:05.039><c> z</c>

00:05:05.830 --> 00:05:05.840 align:start position:0%
the vertices would still be u v w and z
 

00:05:05.840 --> 00:05:08.310 align:start position:0%
the vertices would still be u v w and z
each<00:05:06.080><c> vertex</c><00:05:06.639><c> class</c><00:05:07.199><c> is</c><00:05:07.360><c> going</c><00:05:07.520><c> to</c><00:05:07.600><c> have</c>

00:05:08.310 --> 00:05:08.320 align:start position:0%
each vertex class is going to have
 

00:05:08.320 --> 00:05:10.629 align:start position:0%
each vertex class is going to have
another<00:05:08.800><c> hash</c><00:05:09.039><c> map</c><00:05:09.280><c> inside</c><00:05:09.600><c> of</c><00:05:09.759><c> it</c><00:05:09.840><c> containing</c>

00:05:10.629 --> 00:05:10.639 align:start position:0%
another hash map inside of it containing
 

00:05:10.639 --> 00:05:13.749 align:start position:0%
another hash map inside of it containing
all<00:05:10.880><c> of</c><00:05:11.039><c> that</c><00:05:11.280><c> vertex's</c><00:05:12.080><c> opposite</c><00:05:12.639><c> vertices</c>

00:05:13.749 --> 00:05:13.759 align:start position:0%
all of that vertex's opposite vertices
 

00:05:13.759 --> 00:05:15.749 align:start position:0%
all of that vertex's opposite vertices
and<00:05:13.919><c> the</c><00:05:14.000><c> associated</c><00:05:14.720><c> edges</c>

00:05:15.749 --> 00:05:15.759 align:start position:0%
and the associated edges
 

00:05:15.759 --> 00:05:18.710 align:start position:0%
and the associated edges
here<00:05:16.160><c> in</c><00:05:16.240><c> this</c><00:05:16.400><c> diagram</c><00:05:17.360><c> if</c><00:05:17.600><c> we</c><00:05:18.000><c> take</c><00:05:18.560><c> the</c>

00:05:18.710 --> 00:05:18.720 align:start position:0%
here in this diagram if we take the
 

00:05:18.720 --> 00:05:20.070 align:start position:0%
here in this diagram if we take the
vertex<00:05:19.280><c> u</c>

00:05:20.070 --> 00:05:20.080 align:start position:0%
vertex u
 

00:05:20.080 --> 00:05:22.790 align:start position:0%
vertex u
well<00:05:20.400><c> we</c><00:05:20.560><c> have</c><00:05:21.120><c> inside</c><00:05:21.360><c> of</c><00:05:21.520><c> that</c><00:05:21.600><c> a</c><00:05:21.680><c> hash</c><00:05:22.000><c> map</c>

00:05:22.790 --> 00:05:22.800 align:start position:0%
well we have inside of that a hash map
 

00:05:22.800 --> 00:05:26.390 align:start position:0%
well we have inside of that a hash map
with<00:05:23.199><c> a</c><00:05:23.440><c> key</c><00:05:24.160><c> v</c><00:05:24.720><c> being</c><00:05:24.960><c> the</c><00:05:25.199><c> opposite</c><00:05:25.680><c> vertex</c>

00:05:26.390 --> 00:05:26.400 align:start position:0%
with a key v being the opposite vertex
 

00:05:26.400 --> 00:05:29.270 align:start position:0%
with a key v being the opposite vertex
and<00:05:26.960><c> its</c><00:05:27.199><c> value</c><00:05:27.680><c> being</c><00:05:28.080><c> e</c><00:05:28.400><c> which</c><00:05:28.639><c> is</c><00:05:28.720><c> the</c><00:05:28.960><c> edge</c>

00:05:29.270 --> 00:05:29.280 align:start position:0%
and its value being e which is the edge
 

00:05:29.280 --> 00:05:32.310 align:start position:0%
and its value being e which is the edge
connecting<00:05:30.000><c> both</c><00:05:30.320><c> u</c><00:05:30.639><c> and</c><00:05:30.800><c> v</c>

00:05:32.310 --> 00:05:32.320 align:start position:0%
connecting both u and v
 

00:05:32.320 --> 00:05:33.990 align:start position:0%
connecting both u and v
the<00:05:32.479><c> next</c><00:05:32.800><c> one</c><00:05:33.199><c> is</c>

00:05:33.990 --> 00:05:34.000 align:start position:0%
the next one is
 

00:05:34.000 --> 00:05:35.830 align:start position:0%
the next one is
has<00:05:34.240><c> the</c><00:05:34.400><c> key</c><00:05:34.800><c> w</c>

00:05:35.830 --> 00:05:35.840 align:start position:0%
has the key w
 

00:05:35.840 --> 00:05:38.550 align:start position:0%
has the key w
which<00:05:36.160><c> is</c><00:05:36.560><c> also</c><00:05:36.880><c> an</c><00:05:37.039><c> opposite</c><00:05:37.440><c> vert</c><00:05:37.919><c> vertice</c>

00:05:38.550 --> 00:05:38.560 align:start position:0%
which is also an opposite vert vertice
 

00:05:38.560 --> 00:05:41.830 align:start position:0%
which is also an opposite vert vertice
to<00:05:38.880><c> u</c><00:05:39.520><c> and</c><00:05:40.160><c> g</c><00:05:40.479><c> as</c><00:05:40.720><c> the</c><00:05:40.880><c> value</c><00:05:41.360><c> which</c><00:05:41.600><c> is</c><00:05:41.680><c> the</c>

00:05:41.830 --> 00:05:41.840 align:start position:0%
to u and g as the value which is the
 

00:05:41.840 --> 00:05:44.310 align:start position:0%
to u and g as the value which is the
edge<00:05:42.160><c> connecting</c><00:05:42.800><c> u</c><00:05:43.199><c> and</c><00:05:43.360><c> w</c>

00:05:44.310 --> 00:05:44.320 align:start position:0%
edge connecting u and w
 

00:05:44.320 --> 00:05:47.830 align:start position:0%
edge connecting u and w
so<00:05:44.880><c> you</c><00:05:45.039><c> can</c><00:05:45.280><c> easily</c><00:05:45.840><c> access</c><00:05:46.560><c> any</c><00:05:46.880><c> edge</c><00:05:47.360><c> given</c>

00:05:47.830 --> 00:05:47.840 align:start position:0%
so you can easily access any edge given
 

00:05:47.840 --> 00:05:49.990 align:start position:0%
so you can easily access any edge given
two<00:05:48.240><c> vertices</c>

00:05:49.990 --> 00:05:50.000 align:start position:0%
two vertices
 

00:05:50.000 --> 00:05:51.749 align:start position:0%
two vertices
now<00:05:50.240><c> for</c><00:05:50.400><c> the</c><00:05:50.560><c> last</c><00:05:50.880><c> one</c><00:05:51.039><c> the</c><00:05:51.120><c> adjacency</c>

00:05:51.749 --> 00:05:51.759 align:start position:0%
now for the last one the adjacency
 

00:05:51.759 --> 00:05:54.790 align:start position:0%
now for the last one the adjacency
matrix<00:05:52.560><c> in</c><00:05:52.720><c> a</c><00:05:52.880><c> matrix</c><00:05:53.360><c> representation</c><00:05:54.479><c> we</c><00:05:54.639><c> can</c>

00:05:54.790 --> 00:05:54.800 align:start position:0%
matrix in a matrix representation we can
 

00:05:54.800 --> 00:05:56.950 align:start position:0%
matrix in a matrix representation we can
think<00:05:55.039><c> of</c><00:05:55.120><c> the</c><00:05:55.280><c> vertices</c><00:05:56.000><c> as</c><00:05:56.160><c> being</c><00:05:56.400><c> integers</c>

00:05:56.950 --> 00:05:56.960 align:start position:0%
think of the vertices as being integers
 

00:05:56.960 --> 00:06:00.070 align:start position:0%
think of the vertices as being integers
in<00:05:57.039><c> the</c><00:05:57.199><c> set</c><00:05:57.520><c> 0</c><00:05:57.840><c> to</c><00:05:58.000><c> n</c><00:05:58.240><c> minus</c><00:05:58.639><c> 1</c><00:05:59.280><c> and</c><00:05:59.440><c> the</c><00:05:59.680><c> edges</c>

00:06:00.070 --> 00:06:00.080 align:start position:0%
in the set 0 to n minus 1 and the edges
 

00:06:00.080 --> 00:06:02.230 align:start position:0%
in the set 0 to n minus 1 and the edges
being<00:06:00.479><c> pairs</c><00:06:00.880><c> of</c><00:06:00.960><c> those</c><00:06:01.199><c> integers</c>

00:06:02.230 --> 00:06:02.240 align:start position:0%
being pairs of those integers
 

00:06:02.240 --> 00:06:04.230 align:start position:0%
being pairs of those integers
let's<00:06:02.479><c> go</c><00:06:02.639><c> over</c><00:06:02.800><c> what</c><00:06:02.960><c> that</c><00:06:03.199><c> means</c><00:06:03.840><c> well</c><00:06:04.080><c> here</c>

00:06:04.230 --> 00:06:04.240 align:start position:0%
let's go over what that means well here
 

00:06:04.240 --> 00:06:07.110 align:start position:0%
let's go over what that means well here
we<00:06:04.400><c> have</c><00:06:04.479><c> a</c><00:06:04.840><c> matrix</c><00:06:06.080><c> and</c><00:06:06.400><c> we</c><00:06:06.560><c> have</c><00:06:06.800><c> four</c>

00:06:07.110 --> 00:06:07.120 align:start position:0%
we have a matrix and we have four
 

00:06:07.120 --> 00:06:08.950 align:start position:0%
we have a matrix and we have four
vertices<00:06:07.840><c> so</c><00:06:08.000><c> this</c><00:06:08.400><c> means</c><00:06:08.560><c> it's</c><00:06:08.720><c> going</c><00:06:08.800><c> to</c><00:06:08.880><c> be</c>

00:06:08.950 --> 00:06:08.960 align:start position:0%
vertices so this means it's going to be
 

00:06:08.960 --> 00:06:12.070 align:start position:0%
vertices so this means it's going to be
a<00:06:09.039><c> four</c><00:06:09.199><c> by</c><00:06:09.360><c> four</c><00:06:09.600><c> matrix</c><00:06:10.560><c> we</c><00:06:10.800><c> let</c><00:06:11.280><c> u</c>

00:06:12.070 --> 00:06:12.080 align:start position:0%
a four by four matrix we let u
 

00:06:12.080 --> 00:06:17.189 align:start position:0%
a four by four matrix we let u
denote<00:06:12.800><c> index</c><00:06:13.280><c> zero</c><00:06:14.160><c> and</c><00:06:14.400><c> z</c><00:06:14.800><c> be</c><00:06:15.039><c> index</c><00:06:15.520><c> three</c>

00:06:17.189 --> 00:06:17.199 align:start position:0%
denote index zero and z be index three
 

00:06:17.199 --> 00:06:19.749 align:start position:0%
denote index zero and z be index three
well<00:06:17.360><c> what</c><00:06:17.520><c> we</c><00:06:17.759><c> fill</c><00:06:18.080><c> in</c><00:06:18.560><c> is</c><00:06:18.720><c> the</c><00:06:18.960><c> edge</c><00:06:19.360><c> that</c><00:06:19.520><c> is</c>

00:06:19.749 --> 00:06:19.759 align:start position:0%
well what we fill in is the edge that is
 

00:06:19.759 --> 00:06:22.150 align:start position:0%
well what we fill in is the edge that is
incident<00:06:20.160><c> to</c><00:06:20.319><c> two</c><00:06:20.560><c> vertices</c><00:06:21.039><c> that</c><00:06:21.199><c> act</c><00:06:21.520><c> as</c><00:06:21.759><c> its</c>

00:06:22.150 --> 00:06:22.160 align:start position:0%
incident to two vertices that act as its
 

00:06:22.160 --> 00:06:23.670 align:start position:0%
incident to two vertices that act as its
endpoints

00:06:23.670 --> 00:06:23.680 align:start position:0%
endpoints
 

00:06:23.680 --> 00:06:25.350 align:start position:0%
endpoints
all<00:06:23.840><c> right</c><00:06:24.000><c> let's</c><00:06:24.160><c> take</c><00:06:24.319><c> an</c><00:06:24.479><c> example</c><00:06:25.199><c> let's</c>

00:06:25.350 --> 00:06:25.360 align:start position:0%
all right let's take an example let's
 

00:06:25.360 --> 00:06:28.070 align:start position:0%
all right let's take an example let's
take<00:06:25.600><c> the</c><00:06:25.680><c> vertex</c><00:06:26.080><c> u</c><00:06:26.319><c> and</c><00:06:26.400><c> the</c><00:06:26.479><c> vertex</c><00:06:26.960><c> v</c><00:06:27.600><c> well</c>

00:06:28.070 --> 00:06:28.080 align:start position:0%
take the vertex u and the vertex v well
 

00:06:28.080 --> 00:06:32.070 align:start position:0%
take the vertex u and the vertex v well
its<00:06:28.479><c> edge</c><00:06:28.800><c> that</c><00:06:29.039><c> connects</c><00:06:29.440><c> them</c><00:06:29.759><c> is</c><00:06:30.080><c> edge</c><00:06:30.560><c> e</c>

00:06:32.070 --> 00:06:32.080 align:start position:0%
its edge that connects them is edge e
 

00:06:32.080 --> 00:06:35.189 align:start position:0%
its edge that connects them is edge e
well<00:06:32.400><c> we</c><00:06:32.560><c> said</c><00:06:32.800><c> u</c><00:06:33.360><c> is</c><00:06:33.520><c> zero</c><00:06:34.000><c> and</c><00:06:34.160><c> v</c><00:06:34.560><c> is</c><00:06:34.720><c> known</c><00:06:34.960><c> as</c>

00:06:35.189 --> 00:06:35.199 align:start position:0%
well we said u is zero and v is known as
 

00:06:35.199 --> 00:06:38.870 align:start position:0%
well we said u is zero and v is known as
one<00:06:35.600><c> well</c><00:06:35.759><c> if</c><00:06:35.919><c> you</c><00:06:36.000><c> take</c><00:06:36.160><c> the</c><00:06:36.319><c> pair</c><00:06:36.880><c> zero</c><00:06:37.440><c> one</c>

00:06:38.870 --> 00:06:38.880 align:start position:0%
one well if you take the pair zero one
 

00:06:38.880 --> 00:06:41.189 align:start position:0%
one well if you take the pair zero one
we<00:06:39.039><c> would</c><00:06:39.280><c> fill</c><00:06:39.680><c> in</c><00:06:40.160><c> e</c>

00:06:41.189 --> 00:06:41.199 align:start position:0%
we would fill in e
 

00:06:41.199 --> 00:06:43.029 align:start position:0%
we would fill in e
in<00:06:41.360><c> that</c><00:06:41.600><c> matrix</c><00:06:42.400><c> and</c><00:06:42.479><c> then</c><00:06:42.720><c> if</c><00:06:42.800><c> you</c><00:06:42.880><c> just</c>

00:06:43.029 --> 00:06:43.039 align:start position:0%
in that matrix and then if you just
 

00:06:43.039 --> 00:06:44.629 align:start position:0%
in that matrix and then if you just
reverse<00:06:43.360><c> that</c><00:06:43.520><c> and</c><00:06:43.600><c> you</c><00:06:43.680><c> want</c><00:06:43.840><c> to</c><00:06:44.000><c> say</c><00:06:44.240><c> well</c>

00:06:44.629 --> 00:06:44.639 align:start position:0%
reverse that and you want to say well
 

00:06:44.639 --> 00:06:46.870 align:start position:0%
reverse that and you want to say well
let's<00:06:44.880><c> start</c><00:06:45.039><c> at</c><00:06:45.280><c> v</c><00:06:45.759><c> and</c><00:06:45.919><c> go</c><00:06:46.080><c> to</c><00:06:46.319><c> u</c><00:06:46.560><c> well</c><00:06:46.720><c> its</c>

00:06:46.870 --> 00:06:46.880 align:start position:0%
let's start at v and go to u well its
 

00:06:46.880 --> 00:06:49.029 align:start position:0%
let's start at v and go to u well its
edge<00:06:47.039><c> is</c><00:06:47.199><c> still</c><00:06:47.440><c> e</c><00:06:48.080><c> so</c><00:06:48.240><c> instead</c><00:06:48.479><c> of</c><00:06:48.720><c> going</c><00:06:48.960><c> to</c>

00:06:49.029 --> 00:06:49.039 align:start position:0%
edge is still e so instead of going to
 

00:06:49.039 --> 00:06:52.550 align:start position:0%
edge is still e so instead of going to
the<00:06:49.759><c> matrix</c><00:06:50.240><c> 0</c><00:06:50.639><c> 1</c><00:06:50.960><c> you</c><00:06:51.120><c> go</c><00:06:51.280><c> to</c><00:06:51.440><c> 1</c><00:06:51.680><c> 0</c><00:06:52.080><c> and</c><00:06:52.160><c> fill</c><00:06:52.400><c> in</c>

00:06:52.550 --> 00:06:52.560 align:start position:0%
the matrix 0 1 you go to 1 0 and fill in
 

00:06:52.560 --> 00:06:54.469 align:start position:0%
the matrix 0 1 you go to 1 0 and fill in
e<00:06:52.720><c> there</c><00:06:52.960><c> as</c><00:06:53.039><c> well</c>

00:06:54.469 --> 00:06:54.479 align:start position:0%
e there as well
 

00:06:54.479 --> 00:06:56.309 align:start position:0%
e there as well
so<00:06:54.639><c> if</c><00:06:54.720><c> we</c><00:06:54.880><c> repeated</c><00:06:55.280><c> that</c><00:06:55.440><c> for</c><00:06:55.599><c> all</c><00:06:55.759><c> of</c><00:06:55.919><c> them</c>

00:06:56.309 --> 00:06:56.319 align:start position:0%
so if we repeated that for all of them
 

00:06:56.319 --> 00:06:57.749 align:start position:0%
so if we repeated that for all of them
it<00:06:56.400><c> would</c><00:06:56.560><c> look</c><00:06:56.800><c> like</c><00:06:56.960><c> this</c>

00:06:57.749 --> 00:06:57.759 align:start position:0%
it would look like this
 

00:06:57.759 --> 00:07:00.469 align:start position:0%
it would look like this
now<00:06:58.080><c> if</c><00:06:58.240><c> we</c><00:06:58.319><c> were</c><00:06:58.400><c> to</c><00:06:58.560><c> look</c><00:06:58.880><c> up</c><00:06:59.440><c> array</c>

00:07:00.469 --> 00:07:00.479 align:start position:0%
now if we were to look up array
 

00:07:00.479 --> 00:07:04.390 align:start position:0%
now if we were to look up array
0<00:07:01.039><c> 1</c><00:07:02.000><c> we</c><00:07:02.160><c> would</c><00:07:02.400><c> return</c><00:07:02.960><c> the</c><00:07:03.199><c> edge</c><00:07:03.599><c> e</c><00:07:04.080><c> now</c><00:07:04.319><c> a</c>

00:07:04.390 --> 00:07:04.400 align:start position:0%
0 1 we would return the edge e now a
 

00:07:04.400 --> 00:07:07.749 align:start position:0%
0 1 we would return the edge e now a
matrix<00:07:04.880><c> will</c><00:07:05.199><c> maintain</c><00:07:05.919><c> a</c><00:07:06.080><c> worst</c><00:07:06.560><c> case</c><00:07:07.039><c> access</c>

00:07:07.749 --> 00:07:07.759 align:start position:0%
matrix will maintain a worst case access
 

00:07:07.759 --> 00:07:11.029 align:start position:0%
matrix will maintain a worst case access
to<00:07:07.919><c> a</c><00:07:08.000><c> specific</c><00:07:08.560><c> edge</c><00:07:09.039><c> big</c><00:07:09.360><c> o</c><00:07:09.520><c> of</c><00:07:09.680><c> 1</c><00:07:10.319><c> because</c><00:07:10.720><c> it</c>

00:07:11.029 --> 00:07:11.039 align:start position:0%
to a specific edge big o of 1 because it
 

00:07:11.039 --> 00:07:14.309 align:start position:0%
to a specific edge big o of 1 because it
always<00:07:11.440><c> maintain</c><00:07:12.000><c> that</c><00:07:12.319><c> n</c><00:07:12.560><c> by</c><00:07:12.880><c> n</c><00:07:13.280><c> matrix</c>

00:07:14.309 --> 00:07:14.319 align:start position:0%
always maintain that n by n matrix
 

00:07:14.319 --> 00:07:17.029 align:start position:0%
always maintain that n by n matrix
overall<00:07:14.880><c> the</c><00:07:15.039><c> adjacency</c><00:07:15.759><c> map</c><00:07:16.240><c> is</c><00:07:16.479><c> the</c><00:07:16.720><c> better</c>

00:07:17.029 --> 00:07:17.039 align:start position:0%
overall the adjacency map is the better
 

00:07:17.039 --> 00:07:19.510 align:start position:0%
overall the adjacency map is the better
choice<00:07:17.360><c> as</c><00:07:17.520><c> a</c><00:07:17.680><c> graph</c><00:07:18.000><c> representation</c><00:07:19.280><c> and</c>

00:07:19.510 --> 00:07:19.520 align:start position:0%
choice as a graph representation and
 

00:07:19.520 --> 00:07:21.670 align:start position:0%
choice as a graph representation and
because<00:07:19.759><c> of</c><00:07:19.919><c> that</c><00:07:20.479><c> in</c><00:07:20.639><c> the</c><00:07:20.720><c> next</c><00:07:21.039><c> video</c><00:07:21.440><c> when</c>

00:07:21.670 --> 00:07:21.680 align:start position:0%
because of that in the next video when
 

00:07:21.680 --> 00:07:23.909 align:start position:0%
because of that in the next video when
we<00:07:22.160><c> code</c><00:07:22.479><c> the</c><00:07:22.560><c> implementation</c><00:07:23.520><c> for</c><00:07:23.680><c> this</c>

00:07:23.909 --> 00:07:23.919 align:start position:0%
we code the implementation for this
 

00:07:23.919 --> 00:07:24.870 align:start position:0%
we code the implementation for this
graph

00:07:24.870 --> 00:07:24.880 align:start position:0%
graph
 

00:07:24.880 --> 00:07:26.790 align:start position:0%
graph
we<00:07:25.120><c> are</c><00:07:25.199><c> going</c><00:07:25.280><c> to</c><00:07:25.360><c> be</c><00:07:25.520><c> coding</c><00:07:25.919><c> in</c><00:07:26.240><c> adjacency</c>

00:07:26.790 --> 00:07:26.800 align:start position:0%
we are going to be coding in adjacency
 

00:07:26.800 --> 00:07:29.910 align:start position:0%
we are going to be coding in adjacency
map

00:07:29.910 --> 00:07:29.920 align:start position:0%
 
 

00:07:29.920 --> 00:07:36.710 align:start position:0%
 
[Music]

00:07:36.710 --> 00:07:36.720 align:start position:0%
[Music]
 

00:07:36.720 --> 00:07:38.800 align:start position:0%
[Music]
you

