WEBVTT
Kind: captions
Language: en

00:00:00.919 --> 00:00:04.430 align:start position:0%
 
shiny<00:00:01.680><c> lava</c><00:00:02.399><c> yep</c><00:00:02.760><c> we</c><00:00:02.919><c> have</c><00:00:03.080><c> a</c><00:00:03.280><c> new</c><00:00:03.520><c> model</c><00:00:04.080><c> and</c><00:00:04.279><c> a</c>

00:00:04.430 --> 00:00:04.440 align:start position:0%
shiny lava yep we have a new model and a
 

00:00:04.440 --> 00:00:09.470 align:start position:0%
shiny lava yep we have a new model and a
new<00:00:04.680><c> version</c><00:00:05.040><c> of</c><00:00:05.240><c> olama</c><00:00:06.000><c> to</c><00:00:06.200><c> support</c><00:00:06.480><c> it</c><00:00:07.160><c> olama</c>

00:00:09.470 --> 00:00:09.480 align:start position:0%
new version of olama to support it olama
 

00:00:09.480 --> 00:00:13.270 align:start position:0%
new version of olama to support it olama
0.123<00:00:10.480><c> is</c><00:00:10.679><c> out</c><00:00:11.120><c> and</c><00:00:11.280><c> along</c><00:00:11.599><c> with</c><00:00:11.719><c> it</c><00:00:12.000><c> lava</c><00:00:12.480><c> 1.6</c>

00:00:13.270 --> 00:00:13.280 align:start position:0%
0.123 is out and along with it lava 1.6
 

00:00:13.280 --> 00:00:16.670 align:start position:0%
0.123 is out and along with it lava 1.6
has<00:00:13.400><c> been</c><00:00:13.559><c> released</c><00:00:14.000><c> offering</c><00:00:14.480><c> 7</c><00:00:15.200><c> 13</c><00:00:15.799><c> and</c><00:00:16.119><c> 34</c>

00:00:16.670 --> 00:00:16.680 align:start position:0%
has been released offering 7 13 and 34
 

00:00:16.680 --> 00:00:18.790 align:start position:0%
has been released offering 7 13 and 34
billion<00:00:17.039><c> parameter</c><00:00:17.480><c> variants</c><00:00:18.359><c> I'll</c><00:00:18.560><c> talk</c>

00:00:18.790 --> 00:00:18.800 align:start position:0%
billion parameter variants I'll talk
 

00:00:18.800 --> 00:00:21.349 align:start position:0%
billion parameter variants I'll talk
more<00:00:19.000><c> about</c><00:00:19.279><c> lava</c><00:00:19.920><c> and</c><00:00:20.199><c> give</c><00:00:20.439><c> some</c><00:00:20.760><c> examples</c>

00:00:21.349 --> 00:00:21.359 align:start position:0%
more about lava and give some examples
 

00:00:21.359 --> 00:00:24.070 align:start position:0%
more about lava and give some examples
in<00:00:21.480><c> a</c><00:00:21.640><c> bit</c><00:00:22.480><c> first</c><00:00:23.080><c> let's</c><00:00:23.320><c> look</c><00:00:23.480><c> at</c><00:00:23.640><c> some</c><00:00:23.760><c> of</c><00:00:23.880><c> the</c>

00:00:24.070 --> 00:00:24.080 align:start position:0%
in a bit first let's look at some of the
 

00:00:24.080 --> 00:00:29.230 align:start position:0%
in a bit first let's look at some of the
other<00:00:24.279><c> features</c><00:00:24.760><c> in</c><00:00:25.359><c> 23</c><00:00:26.359><c> keep</c><00:00:26.800><c> alive</c><00:00:27.880><c> wow</c><00:00:28.880><c> this</c>

00:00:29.230 --> 00:00:29.240 align:start position:0%
other features in 23 keep alive wow this
 

00:00:29.240 --> 00:00:32.870 align:start position:0%
other features in 23 keep alive wow this
has<00:00:29.439><c> been</c><00:00:30.359><c> a</c><00:00:30.840><c> hot</c><00:00:31.400><c> request</c><00:00:31.800><c> for</c><00:00:31.960><c> a</c><00:00:32.079><c> long</c><00:00:32.279><c> time</c>

00:00:32.870 --> 00:00:32.880 align:start position:0%
has been a hot request for a long time
 

00:00:32.880 --> 00:00:34.990 align:start position:0%
has been a hot request for a long time
when<00:00:33.000><c> a</c><00:00:33.200><c> llama</c><00:00:33.480><c> starts</c><00:00:33.800><c> with</c><00:00:33.920><c> a</c><00:00:34.040><c> model</c><00:00:34.840><c> that</c>

00:00:34.990 --> 00:00:35.000 align:start position:0%
when a llama starts with a model that
 

00:00:35.000 --> 00:00:37.950 align:start position:0%
when a llama starts with a model that
model<00:00:35.320><c> stays</c><00:00:35.600><c> in</c><00:00:35.760><c> memory</c><00:00:36.120><c> for</c><00:00:36.520><c> 5</c><00:00:36.800><c> minutes</c><00:00:37.760><c> let</c>

00:00:37.950 --> 00:00:37.960 align:start position:0%
model stays in memory for 5 minutes let
 

00:00:37.960 --> 00:00:40.110 align:start position:0%
model stays in memory for 5 minutes let
me<00:00:38.160><c> tell</c><00:00:38.360><c> you</c><00:00:38.680><c> how</c><00:00:38.879><c> many</c><00:00:39.160><c> folks</c><00:00:39.480><c> are</c><00:00:39.800><c> happy</c>

00:00:40.110 --> 00:00:40.120 align:start position:0%
me tell you how many folks are happy
 

00:00:40.120 --> 00:00:41.270 align:start position:0%
me tell you how many folks are happy
with

00:00:41.270 --> 00:00:41.280 align:start position:0%
with
 

00:00:41.280 --> 00:00:46.189 align:start position:0%
with
that<00:00:42.280><c> exactly</c><00:00:42.719><c> none</c><00:00:43.600><c> there</c><00:00:43.760><c> are</c><00:00:44.600><c> two</c><00:00:45.600><c> strong</c>

00:00:46.189 --> 00:00:46.199 align:start position:0%
that exactly none there are two strong
 

00:00:46.199 --> 00:00:49.189 align:start position:0%
that exactly none there are two strong
opinions<00:00:46.920><c> on</c><00:00:47.440><c> where</c><00:00:47.680><c> this</c><00:00:47.840><c> should</c><00:00:48.120><c> be</c><00:00:48.960><c> one</c>

00:00:49.189 --> 00:00:49.199 align:start position:0%
opinions on where this should be one
 

00:00:49.199 --> 00:00:52.110 align:start position:0%
opinions on where this should be one
group<00:00:49.760><c> says</c><00:00:50.160><c> it</c><00:00:50.280><c> should</c><00:00:50.480><c> be</c><00:00:50.840><c> longer</c><00:00:51.840><c> and</c><00:00:52.000><c> the</c>

00:00:52.110 --> 00:00:52.120 align:start position:0%
group says it should be longer and the
 

00:00:52.120 --> 00:00:54.950 align:start position:0%
group says it should be longer and the
other<00:00:52.480><c> group</c><00:00:52.760><c> says</c><00:00:53.079><c> it</c><00:00:53.199><c> should</c><00:00:53.440><c> be</c><00:00:53.960><c> shorter</c><00:00:54.600><c> so</c>

00:00:54.950 --> 00:00:54.960 align:start position:0%
other group says it should be shorter so
 

00:00:54.960 --> 00:00:57.830 align:start position:0%
other group says it should be shorter so
now<00:00:55.239><c> for</c><00:00:55.559><c> API</c><00:00:56.039><c> requests</c><00:00:56.640><c> you</c><00:00:56.760><c> can</c><00:00:57.039><c> pass</c><00:00:57.320><c> a</c><00:00:57.559><c> keep</c>

00:00:57.830 --> 00:00:57.840 align:start position:0%
now for API requests you can pass a keep
 

00:00:57.840 --> 00:01:00.790 align:start position:0%
now for API requests you can pass a keep
alive<00:00:58.280><c> parameter</c><00:00:58.719><c> setting</c><00:00:59.480><c> this</c><00:00:59.600><c> is</c><00:00:59.719><c> a</c><00:01:00.039><c> string</c>

00:01:00.790 --> 00:01:00.800 align:start position:0%
alive parameter setting this is a string
 

00:01:00.800 --> 00:01:03.910 align:start position:0%
alive parameter setting this is a string
if<00:01:00.920><c> you</c><00:01:01.160><c> pass</c><00:01:01.680><c> 30</c><00:01:02.320><c> then</c><00:01:02.480><c> it's</c><00:01:02.640><c> 30</c><00:01:02.960><c> seconds</c><00:01:03.719><c> but</c>

00:01:03.910 --> 00:01:03.920 align:start position:0%
if you pass 30 then it's 30 seconds but
 

00:01:03.920 --> 00:01:07.710 align:start position:0%
if you pass 30 then it's 30 seconds but
tacking<00:01:04.280><c> on</c><00:01:04.559><c> an</c><00:01:04.760><c> S</c><00:01:05.239><c> an</c><00:01:05.439><c> M</c><00:01:06.040><c> or</c><00:01:06.240><c> HR</c><00:01:07.000><c> will</c><00:01:07.240><c> clarify</c>

00:01:07.710 --> 00:01:07.720 align:start position:0%
tacking on an S an M or HR will clarify
 

00:01:07.720 --> 00:01:10.230 align:start position:0%
tacking on an S an M or HR will clarify
things<00:01:07.960><c> a</c><00:01:08.159><c> bit</c><00:01:08.759><c> this</c><00:01:08.880><c> is</c><00:01:09.159><c> just</c><00:01:09.320><c> in</c><00:01:09.479><c> the</c><00:01:09.640><c> API</c><00:01:10.040><c> for</c>

00:01:10.230 --> 00:01:10.240 align:start position:0%
things a bit this is just in the API for
 

00:01:10.240 --> 00:01:12.870 align:start position:0%
things a bit this is just in the API for
now<00:01:10.960><c> maybe</c><00:01:11.280><c> in</c><00:01:11.400><c> the</c><00:01:11.560><c> future</c><00:01:12.200><c> we'll</c><00:01:12.520><c> get</c><00:01:12.680><c> this</c>

00:01:12.870 --> 00:01:12.880 align:start position:0%
now maybe in the future we'll get this
 

00:01:12.880 --> 00:01:15.109 align:start position:0%
now maybe in the future we'll get this
on<00:01:13.080><c> the</c><00:01:13.240><c> command</c><00:01:13.640><c> line</c><00:01:13.960><c> or</c><00:01:14.280><c> as</c><00:01:14.400><c> an</c><00:01:14.600><c> environment</c>

00:01:15.109 --> 00:01:15.119 align:start position:0%
on the command line or as an environment
 

00:01:15.119 --> 00:01:18.109 align:start position:0%
on the command line or as an environment
variable<00:01:15.720><c> or</c><00:01:15.960><c> in</c><00:01:16.080><c> the</c><00:01:16.360><c> reppel</c><00:01:17.360><c> support</c><00:01:17.720><c> for</c>

00:01:18.109 --> 00:01:18.119 align:start position:0%
variable or in the reppel support for
 

00:01:18.119 --> 00:01:20.670 align:start position:0%
variable or in the reppel support for
NVIDIA<00:01:18.560><c> gpus</c><00:01:19.119><c> has</c><00:01:19.320><c> expanded</c><00:01:19.799><c> a</c><00:01:19.960><c> bit</c><00:01:20.159><c> so</c><00:01:20.439><c> some</c>

00:01:20.670 --> 00:01:20.680 align:start position:0%
NVIDIA gpus has expanded a bit so some
 

00:01:20.680 --> 00:01:23.870 align:start position:0%
NVIDIA gpus has expanded a bit so some
older<00:01:21.079><c> cars</c><00:01:21.400><c> are</c><00:01:21.640><c> Now</c><00:01:22.200><c> supported</c><00:01:23.200><c> as</c><00:01:23.439><c> long</c><00:01:23.680><c> as</c>

00:01:23.870 --> 00:01:23.880 align:start position:0%
older cars are Now supported as long as
 

00:01:23.880 --> 00:01:26.390 align:start position:0%
older cars are Now supported as long as
your<00:01:24.079><c> GPU</c><00:01:24.560><c> has</c><00:01:24.680><c> a</c><00:01:24.880><c> compute</c><00:01:25.360><c> capability</c><00:01:26.000><c> of</c>

00:01:26.390 --> 00:01:26.400 align:start position:0%
your GPU has a compute capability of
 

00:01:26.400 --> 00:01:30.149 align:start position:0%
your GPU has a compute capability of
five<00:01:26.920><c> or</c><00:01:27.240><c> higher</c><00:01:28.200><c> you</c><00:01:28.320><c> should</c><00:01:28.640><c> be</c><00:01:28.840><c> set</c><00:01:29.960><c> that</c>

00:01:30.149 --> 00:01:30.159 align:start position:0%
five or higher you should be set that
 

00:01:30.159 --> 00:01:33.310 align:start position:0%
five or higher you should be set that
ancient<00:01:30.720><c> Ultra</c><00:01:31.159><c> cheap</c><00:01:31.680><c> eBay</c><00:01:32.159><c> special</c><00:01:32.880><c> though</c>

00:01:33.310 --> 00:01:33.320 align:start position:0%
ancient Ultra cheap eBay special though
 

00:01:33.320 --> 00:01:35.510 align:start position:0%
ancient Ultra cheap eBay special though
may<00:01:33.560><c> still</c><00:01:33.799><c> only</c><00:01:34.079><c> be</c><00:01:34.280><c> good</c><00:01:34.439><c> for</c><00:01:34.600><c> scrap</c><00:01:35.119><c> and</c><00:01:35.280><c> not</c>

00:01:35.510 --> 00:01:35.520 align:start position:0%
may still only be good for scrap and not
 

00:01:35.520 --> 00:01:38.630 align:start position:0%
may still only be good for scrap and not
much<00:01:35.720><c> more</c><00:01:36.600><c> the</c><00:01:36.720><c> olama</c><00:01:37.399><c> debug</c><00:01:37.960><c> equals</c><00:01:38.320><c> one</c>

00:01:38.630 --> 00:01:38.640 align:start position:0%
much more the olama debug equals one
 

00:01:38.640 --> 00:01:40.870 align:start position:0%
much more the olama debug equals one
setting<00:01:39.240><c> that</c><00:01:39.360><c> I</c><00:01:39.520><c> demoed</c><00:01:39.960><c> in</c><00:01:40.040><c> a</c><00:01:40.200><c> recent</c><00:01:40.560><c> video</c>

00:01:40.870 --> 00:01:40.880 align:start position:0%
setting that I demoed in a recent video
 

00:01:40.880 --> 00:01:43.109 align:start position:0%
setting that I demoed in a recent video
is<00:01:41.119><c> now</c><00:01:41.360><c> in</c><00:01:41.520><c> the</c><00:01:41.720><c> product</c><00:01:42.600><c> so</c><00:01:42.720><c> you</c><00:01:42.840><c> don't</c><00:01:43.000><c> have</c>

00:01:43.109 --> 00:01:43.119 align:start position:0%
is now in the product so you don't have
 

00:01:43.119 --> 00:01:46.389 align:start position:0%
is now in the product so you don't have
to<00:01:43.320><c> build</c><00:01:43.920><c> to</c><00:01:44.119><c> get</c><00:01:44.320><c> that</c><00:01:45.040><c> what</c><00:01:45.200><c> that</c><00:01:45.399><c> does</c><00:01:46.079><c> is</c>

00:01:46.389 --> 00:01:46.399 align:start position:0%
to build to get that what that does is
 

00:01:46.399 --> 00:01:48.789 align:start position:0%
to build to get that what that does is
to<00:01:46.680><c> give</c><00:01:46.880><c> the</c><00:01:47.040><c> full</c><00:01:47.439><c> details</c><00:01:47.880><c> on</c><00:01:48.159><c> prompts</c><00:01:48.640><c> and</c>

00:01:48.789 --> 00:01:48.799 align:start position:0%
to give the full details on prompts and
 

00:01:48.799 --> 00:01:51.830 align:start position:0%
to give the full details on prompts and
outputs<00:01:49.360><c> in</c><00:01:49.479><c> the</c><00:01:49.640><c> logs</c><00:01:50.159><c> for</c><00:01:50.520><c> Alama</c><00:01:51.520><c> definitely</c>

00:01:51.830 --> 00:01:51.840 align:start position:0%
outputs in the logs for Alama definitely
 

00:01:51.840 --> 00:01:53.789 align:start position:0%
outputs in the logs for Alama definitely
not<00:01:52.040><c> something</c><00:01:52.280><c> you</c><00:01:52.439><c> want</c><00:01:52.560><c> to</c><00:01:52.719><c> run</c><00:01:53.360><c> all</c><00:01:53.600><c> the</c>

00:01:53.789 --> 00:01:53.799 align:start position:0%
not something you want to run all the
 

00:01:53.799 --> 00:01:56.469 align:start position:0%
not something you want to run all the
time<00:01:54.799><c> but</c><00:01:54.960><c> when</c><00:01:55.200><c> trying</c><00:01:55.520><c> to</c><00:01:55.680><c> figure</c><00:01:55.960><c> out</c><00:01:56.280><c> what</c>

00:01:56.469 --> 00:01:56.479 align:start position:0%
time but when trying to figure out what
 

00:01:56.479 --> 00:02:00.029 align:start position:0%
time but when trying to figure out what
an<00:01:56.719><c> application</c><00:01:57.159><c> is</c><00:01:57.320><c> doing</c><00:01:58.159><c> it</c><00:01:58.320><c> can</c><00:01:58.439><c> be</c><00:01:58.840><c> magic</c>

00:02:00.029 --> 00:02:00.039 align:start position:0%
an application is doing it can be magic
 

00:02:00.039 --> 00:02:02.350 align:start position:0%
an application is doing it can be magic
there<00:02:00.159><c> are</c><00:02:00.320><c> a</c><00:02:00.439><c> few</c><00:02:00.719><c> smaller</c><00:02:01.159><c> things</c><00:02:01.640><c> but</c><00:02:02.119><c> let's</c>

00:02:02.350 --> 00:02:02.360 align:start position:0%
there are a few smaller things but let's
 

00:02:02.360 --> 00:02:03.910 align:start position:0%
there are a few smaller things but let's
start<00:02:02.640><c> talking</c><00:02:02.920><c> about</c>

00:02:03.910 --> 00:02:03.920 align:start position:0%
start talking about
 

00:02:03.920 --> 00:02:06.789 align:start position:0%
start talking about
multimodal<00:02:04.920><c> first</c><00:02:05.479><c> you</c><00:02:05.640><c> can</c><00:02:05.920><c> add</c><00:02:06.200><c> an</c><00:02:06.399><c> image</c>

00:02:06.789 --> 00:02:06.799 align:start position:0%
multimodal first you can add an image
 

00:02:06.799 --> 00:02:09.469 align:start position:0%
multimodal first you can add an image
path<00:02:07.159><c> when</c><00:02:07.360><c> running</c><00:02:07.840><c> o</c><00:02:08.000><c> llama</c><00:02:08.319><c> run</c><00:02:09.000><c> just</c><00:02:09.239><c> tack</c>

00:02:09.469 --> 00:02:09.479 align:start position:0%
path when running o llama run just tack
 

00:02:09.479 --> 00:02:11.670 align:start position:0%
path when running o llama run just tack
on<00:02:09.720><c> the</c><00:02:09.959><c> path</c><00:02:10.200><c> to</c><00:02:10.360><c> the</c><00:02:10.479><c> end</c><00:02:10.599><c> of</c><00:02:10.720><c> the</c><00:02:10.840><c> command</c>

00:02:11.670 --> 00:02:11.680 align:start position:0%
on the path to the end of the command
 

00:02:11.680 --> 00:02:13.470 align:start position:0%
on the path to the end of the command
this<00:02:11.800><c> isn't</c><00:02:12.080><c> for</c><00:02:12.319><c> working</c><00:02:12.760><c> with</c><00:02:13.000><c> the</c><00:02:13.160><c> image</c>

00:02:13.470 --> 00:02:13.480 align:start position:0%
this isn't for working with the image
 

00:02:13.480 --> 00:02:15.350 align:start position:0%
this isn't for working with the image
interactively<00:02:14.480><c> but</c><00:02:14.680><c> rather</c><00:02:14.959><c> when</c><00:02:15.120><c> you're</c>

00:02:15.350 --> 00:02:15.360 align:start position:0%
interactively but rather when you're
 

00:02:15.360 --> 00:02:17.630 align:start position:0%
interactively but rather when you're
also<00:02:15.760><c> asking</c><00:02:16.080><c> the</c><00:02:16.239><c> question</c><00:02:16.800><c> on</c><00:02:16.959><c> the</c><00:02:17.080><c> command</c>

00:02:17.630 --> 00:02:17.640 align:start position:0%
also asking the question on the command
 

00:02:17.640 --> 00:02:20.830 align:start position:0%
also asking the question on the command
line<00:02:18.640><c> and</c><00:02:18.879><c> now</c><00:02:19.400><c> you</c><00:02:19.519><c> can</c><00:02:19.800><c> also</c><00:02:20.080><c> send</c><00:02:20.319><c> a</c><00:02:20.480><c> message</c>

00:02:20.830 --> 00:02:20.840 align:start position:0%
line and now you can also send a message
 

00:02:20.840 --> 00:02:23.830 align:start position:0%
line and now you can also send a message
to<00:02:21.080><c> a</c><00:02:21.239><c> multimod</c><00:02:22.200><c> model</c><00:02:23.000><c> without</c><00:02:23.280><c> sending</c><00:02:23.680><c> a</c>

00:02:23.830 --> 00:02:23.840 align:start position:0%
to a multimod model without sending a
 

00:02:23.840 --> 00:02:28.670 align:start position:0%
to a multimod model without sending a
the<00:02:24.000><c> image</c><00:02:24.720><c> though</c><00:02:24.920><c> I'm</c><00:02:25.440><c> not</c><00:02:26.160><c> really</c><00:02:26.440><c> sure</c><00:02:27.680><c> why</c>

00:02:28.670 --> 00:02:28.680 align:start position:0%
the image though I'm not really sure why
 

00:02:28.680 --> 00:02:31.190 align:start position:0%
the image though I'm not really sure why
okay<00:02:28.959><c> so</c><00:02:29.160><c> let's</c><00:02:29.360><c> get</c><00:02:29.480><c> into</c><00:02:30.080><c> lava</c>

00:02:31.190 --> 00:02:31.200 align:start position:0%
okay so let's get into lava
 

00:02:31.200 --> 00:02:34.910 align:start position:0%
okay so let's get into lava
1.6<00:02:32.200><c> lava</c><00:02:32.840><c> stands</c><00:02:33.160><c> for</c><00:02:33.519><c> large</c><00:02:34.080><c> language</c><00:02:34.640><c> and</c>

00:02:34.910 --> 00:02:34.920 align:start position:0%
1.6 lava stands for large language and
 

00:02:34.920 --> 00:02:37.350 align:start position:0%
1.6 lava stands for large language and
vision<00:02:35.519><c> assistant</c><00:02:36.440><c> and</c><00:02:36.599><c> it's</c><00:02:36.879><c> all</c><00:02:37.120><c> about</c>

00:02:37.350 --> 00:02:37.360 align:start position:0%
vision assistant and it's all about
 

00:02:37.360 --> 00:02:41.070 align:start position:0%
vision assistant and it's all about
doing<00:02:37.879><c> image</c><00:02:38.319><c> to</c><00:02:38.840><c> text</c><00:02:39.840><c> first</c><00:02:40.120><c> off</c><00:02:40.640><c> how</c><00:02:40.760><c> do</c><00:02:40.879><c> you</c>

00:02:41.070 --> 00:02:41.080 align:start position:0%
doing image to text first off how do you
 

00:02:41.080 --> 00:02:44.070 align:start position:0%
doing image to text first off how do you
get<00:02:41.200><c> it</c><00:02:41.760><c> well</c><00:02:42.040><c> just</c><00:02:42.239><c> like</c><00:02:42.560><c> any</c><00:02:42.800><c> other</c><00:02:43.080><c> model</c><00:02:43.560><c> do</c>

00:02:44.070 --> 00:02:44.080 align:start position:0%
get it well just like any other model do
 

00:02:44.080 --> 00:02:46.670 align:start position:0%
get it well just like any other model do
Lama<00:02:44.440><c> pull</c><00:02:44.879><c> lava</c><00:02:45.599><c> or</c><00:02:45.920><c> whatever</c><00:02:46.239><c> tag</c><00:02:46.480><c> you're</c>

00:02:46.670 --> 00:02:46.680 align:start position:0%
Lama pull lava or whatever tag you're
 

00:02:46.680 --> 00:02:49.589 align:start position:0%
Lama pull lava or whatever tag you're
using<00:02:47.680><c> if</c><00:02:47.800><c> there's</c><00:02:48.000><c> a</c><00:02:48.200><c> new</c><00:02:48.519><c> version</c><00:02:49.000><c> available</c>

00:02:49.589 --> 00:02:49.599 align:start position:0%
using if there's a new version available
 

00:02:49.599 --> 00:02:51.790 align:start position:0%
using if there's a new version available
it<00:02:49.720><c> will</c><00:02:49.920><c> download</c><00:02:50.360><c> what's</c><00:02:50.599><c> new</c><00:02:51.480><c> I</c><00:02:51.560><c> have</c><00:02:51.680><c> a</c>

00:02:51.790 --> 00:02:51.800 align:start position:0%
it will download what's new I have a
 

00:02:51.800 --> 00:02:55.830 align:start position:0%
it will download what's new I have a
tool<00:02:52.400><c> creatively</c><00:02:53.440><c> called</c><00:02:54.440><c> or</c><00:02:54.840><c> llama</c><00:02:55.360><c> model</c>

00:02:55.830 --> 00:02:55.840 align:start position:0%
tool creatively called or llama model
 

00:02:55.840 --> 00:02:58.030 align:start position:0%
tool creatively called or llama model
updater<00:02:56.800><c> that</c><00:02:56.879><c> will</c><00:02:57.040><c> go</c><00:02:57.200><c> through</c><00:02:57.519><c> all</c><00:02:57.800><c> your</c>

00:02:58.030 --> 00:02:58.040 align:start position:0%
updater that will go through all your
 

00:02:58.040 --> 00:02:59.869 align:start position:0%
updater that will go through all your
models<00:02:58.480><c> searching</c><00:02:58.959><c> for</c><00:02:59.239><c> the</c><00:02:59.360><c> ones</c><00:02:59.599><c> that</c><00:02:59.760><c> that</c>

00:02:59.869 --> 00:02:59.879 align:start position:0%
models searching for the ones that that
 

00:02:59.879 --> 00:03:02.710 align:start position:0%
models searching for the ones that that
need<00:03:00.080><c> an</c><00:03:00.239><c> update</c><00:03:00.959><c> and</c><00:03:01.080><c> then</c><00:03:01.280><c> pulls</c><00:03:01.680><c> the</c><00:03:01.800><c> update</c>

00:03:02.710 --> 00:03:02.720 align:start position:0%
need an update and then pulls the update
 

00:03:02.720 --> 00:03:05.030 align:start position:0%
need an update and then pulls the update
it's<00:03:02.920><c> more</c><00:03:03.159><c> than</c><00:03:03.319><c> just</c><00:03:03.519><c> running</c><00:03:03.959><c> olama</c><00:03:04.560><c> pull</c>

00:03:05.030 --> 00:03:05.040 align:start position:0%
it's more than just running olama pull
 

00:03:05.040 --> 00:03:07.830 align:start position:0%
it's more than just running olama pull
on<00:03:05.720><c> everything</c><00:03:06.680><c> my</c><00:03:06.920><c> tool</c><00:03:07.200><c> compares</c><00:03:07.560><c> the</c>

00:03:07.830 --> 00:03:07.840 align:start position:0%
on everything my tool compares the
 

00:03:07.840 --> 00:03:11.670 align:start position:0%
on everything my tool compares the
Manifest<00:03:08.480><c> on</c><00:03:08.799><c> ol</c><00:03:09.480><c> a</c><00:03:10.280><c> with</c><00:03:10.480><c> what</c><00:03:10.640><c> you</c><00:03:10.920><c> have</c><00:03:11.480><c> and</c>

00:03:11.670 --> 00:03:11.680 align:start position:0%
Manifest on ol a with what you have and
 

00:03:11.680 --> 00:03:13.509 align:start position:0%
Manifest on ol a with what you have and
only<00:03:12.000><c> initiates</c><00:03:12.480><c> the</c><00:03:12.640><c> download</c><00:03:13.080><c> if</c><00:03:13.239><c> there's</c>

00:03:13.509 --> 00:03:13.519 align:start position:0%
only initiates the download if there's
 

00:03:13.519 --> 00:03:16.110 align:start position:0%
only initiates the download if there's
something<00:03:13.920><c> different</c><00:03:14.920><c> this</c><00:03:15.080><c> often</c><00:03:15.319><c> saves</c><00:03:15.720><c> me</c>

00:03:16.110 --> 00:03:16.120 align:start position:0%
something different this often saves me
 

00:03:16.120 --> 00:03:19.229 align:start position:0%
something different this often saves me
20<00:03:16.440><c> to</c><00:03:16.599><c> 30</c><00:03:16.959><c> minutes</c><00:03:17.680><c> to</c><00:03:17.879><c> update</c><00:03:18.280><c> everything</c>

00:03:19.229 --> 00:03:19.239 align:start position:0%
20 to 30 minutes to update everything
 

00:03:19.239 --> 00:03:21.030 align:start position:0%
20 to 30 minutes to update everything
there<00:03:19.319><c> will</c><00:03:19.480><c> be</c><00:03:19.599><c> a</c><00:03:19.720><c> link</c><00:03:19.959><c> in</c><00:03:20.080><c> the</c><00:03:20.200><c> tool</c><00:03:20.519><c> Below</c>

00:03:21.030 --> 00:03:21.040 align:start position:0%
there will be a link in the tool Below
 

00:03:21.040 --> 00:03:22.990 align:start position:0%
there will be a link in the tool Below
in<00:03:21.159><c> the</c><00:03:21.319><c> description</c><00:03:22.239><c> after</c><00:03:22.440><c> you</c><00:03:22.599><c> download</c>

00:03:22.990 --> 00:03:23.000 align:start position:0%
in the description after you download
 

00:03:23.000 --> 00:03:24.869 align:start position:0%
in the description after you download
the<00:03:23.120><c> model</c><00:03:23.760><c> you</c><00:03:23.879><c> can</c><00:03:24.040><c> still</c><00:03:24.280><c> go</c><00:03:24.480><c> back</c><00:03:24.599><c> to</c><00:03:24.720><c> the</c>

00:03:24.869 --> 00:03:24.879 align:start position:0%
the model you can still go back to the
 

00:03:24.879 --> 00:03:27.990 align:start position:0%
the model you can still go back to the
previous<00:03:25.239><c> version</c><00:03:26.000><c> go</c><00:03:26.159><c> to</c><00:03:26.319><c> the</c><00:03:26.560><c> AMA</c><00:03:27.560><c> page</c><00:03:27.760><c> for</c>

00:03:27.990 --> 00:03:28.000 align:start position:0%
previous version go to the AMA page for
 

00:03:28.000 --> 00:03:30.509 align:start position:0%
previous version go to the AMA page for
lava<00:03:28.799><c> and</c><00:03:28.920><c> then</c><00:03:29.080><c> the</c><00:03:29.239><c> tags</c><00:03:29.519><c> page</c><00:03:29.799><c> page</c><00:03:30.319><c> and</c><00:03:30.400><c> you</c>

00:03:30.509 --> 00:03:30.519 align:start position:0%
lava and then the tags page page and you
 

00:03:30.519 --> 00:03:32.070 align:start position:0%
lava and then the tags page page and you
can<00:03:30.640><c> see</c><00:03:30.840><c> there</c><00:03:30.959><c> are</c><00:03:31.159><c> a</c><00:03:31.319><c> bunch</c><00:03:31.519><c> of</c><00:03:31.680><c> models</c>

00:03:32.070 --> 00:03:32.080 align:start position:0%
can see there are a bunch of models
 

00:03:32.080 --> 00:03:36.030 align:start position:0%
can see there are a bunch of models
there<00:03:32.560><c> that</c><00:03:32.680><c> are</c><00:03:33.040><c> version</c><00:03:33.439><c> 1.5</c><00:03:34.840><c> models</c><00:03:35.840><c> if</c>

00:03:36.030 --> 00:03:36.040 align:start position:0%
there that are version 1.5 models if
 

00:03:36.040 --> 00:03:38.509 align:start position:0%
there that are version 1.5 models if
this<00:03:36.360><c> page</c><00:03:37.000><c> is</c><00:03:37.120><c> a</c><00:03:37.239><c> bit</c><00:03:37.400><c> confusing</c><00:03:37.840><c> to</c><00:03:38.000><c> you</c>

00:03:38.509 --> 00:03:38.519 align:start position:0%
this page is a bit confusing to you
 

00:03:38.519 --> 00:03:40.070 align:start position:0%
this page is a bit confusing to you
check<00:03:38.720><c> out</c><00:03:38.920><c> this</c><00:03:39.080><c> video</c><00:03:39.360><c> that</c><00:03:39.519><c> describes</c>

00:03:40.070 --> 00:03:40.080 align:start position:0%
check out this video that describes
 

00:03:40.080 --> 00:03:41.910 align:start position:0%
check out this video that describes
what's<00:03:40.319><c> going</c><00:03:40.480><c> on</c><00:03:40.720><c> here</c><00:03:41.239><c> so</c><00:03:41.519><c> while</c><00:03:41.720><c> you're</c>

00:03:41.910 --> 00:03:41.920 align:start position:0%
what's going on here so while you're
 

00:03:41.920 --> 00:03:43.550 align:start position:0%
what's going on here so while you're
downloading<00:03:42.360><c> the</c><00:03:42.480><c> new</c><00:03:42.680><c> model</c><00:03:43.040><c> you</c><00:03:43.159><c> might</c><00:03:43.360><c> be</c>

00:03:43.550 --> 00:03:43.560 align:start position:0%
downloading the new model you might be
 

00:03:43.560 --> 00:03:46.350 align:start position:0%
downloading the new model you might be
curious<00:03:43.959><c> about</c><00:03:44.280><c> what's</c><00:03:44.599><c> new</c><00:03:45.599><c> the</c><00:03:45.799><c> model</c><00:03:46.159><c> now</c>

00:03:46.350 --> 00:03:46.360 align:start position:0%
curious about what's new the model now
 

00:03:46.360 --> 00:03:48.789 align:start position:0%
curious about what's new the model now
supports<00:03:46.720><c> a</c><00:03:46.879><c> higher</c><00:03:47.239><c> resolution</c><00:03:48.239><c> now</c><00:03:48.439><c> this</c>

00:03:48.789 --> 00:03:48.799 align:start position:0%
supports a higher resolution now this
 

00:03:48.799 --> 00:03:51.750 align:start position:0%
supports a higher resolution now this
may<00:03:49.120><c> be</c><00:03:49.599><c> a</c><00:03:49.720><c> bit</c><00:03:49.959><c> confusing</c><00:03:50.920><c> lava</c><00:03:51.319><c> supports</c>

00:03:51.750 --> 00:03:51.760 align:start position:0%
may be a bit confusing lava supports
 

00:03:51.760 --> 00:03:53.830 align:start position:0%
may be a bit confusing lava supports
high<00:03:52.000><c> resolution</c><00:03:52.439><c> images</c><00:03:52.840><c> but</c><00:03:53.000><c> to</c><00:03:53.200><c> do</c><00:03:53.400><c> so</c><00:03:53.680><c> it</c>

00:03:53.830 --> 00:03:53.840 align:start position:0%
high resolution images but to do so it
 

00:03:53.840 --> 00:03:56.509 align:start position:0%
high resolution images but to do so it
has<00:03:53.959><c> to</c><00:03:54.280><c> split</c><00:03:54.720><c> the</c><00:03:54.879><c> image</c><00:03:55.280><c> up</c><00:03:55.840><c> into</c><00:03:56.120><c> smaller</c>

00:03:56.509 --> 00:03:56.519 align:start position:0%
has to split the image up into smaller
 

00:03:56.519 --> 00:03:58.869 align:start position:0%
has to split the image up into smaller
pieces<00:03:57.239><c> and</c><00:03:57.480><c> then</c><00:03:57.760><c> recognize</c><00:03:58.280><c> what's</c><00:03:58.439><c> in</c><00:03:58.640><c> each</c>

00:03:58.869 --> 00:03:58.879 align:start position:0%
pieces and then recognize what's in each
 

00:03:58.879 --> 00:04:00.350 align:start position:0%
pieces and then recognize what's in each
piece<00:03:59.120><c> then</c><00:03:59.239><c> flatten</c><00:03:59.519><c> the</c><00:03:59.760><c> images</c><00:04:00.040><c> and</c>

00:04:00.350 --> 00:04:00.360 align:start position:0%
piece then flatten the images and
 

00:04:00.360 --> 00:04:02.750 align:start position:0%
piece then flatten the images and
results<00:04:01.360><c> now</c><00:04:01.959><c> because</c><00:04:02.159><c> it</c><00:04:02.319><c> supports</c><00:04:02.640><c> the</c>

00:04:02.750 --> 00:04:02.760 align:start position:0%
results now because it supports the
 

00:04:02.760 --> 00:04:04.309 align:start position:0%
results now because it supports the
higher<00:04:03.000><c> resolution</c><00:04:03.560><c> it</c><00:04:03.680><c> doesn't</c><00:04:03.959><c> have</c><00:04:04.079><c> to</c>

00:04:04.309 --> 00:04:04.319 align:start position:0%
higher resolution it doesn't have to
 

00:04:04.319 --> 00:04:06.949 align:start position:0%
higher resolution it doesn't have to
split<00:04:04.680><c> the</c><00:04:04.840><c> file</c><00:04:05.120><c> up</c><00:04:05.760><c> as</c><00:04:06.159><c> much</c><00:04:06.480><c> as</c><00:04:06.599><c> it</c><00:04:06.720><c> did</c>

00:04:06.949 --> 00:04:06.959 align:start position:0%
split the file up as much as it did
 

00:04:06.959 --> 00:04:09.390 align:start position:0%
split the file up as much as it did
before<00:04:07.959><c> there's</c><00:04:08.200><c> better</c><00:04:08.560><c> visual</c><00:04:08.920><c> reasoning</c>

00:04:09.390 --> 00:04:09.400 align:start position:0%
before there's better visual reasoning
 

00:04:09.400 --> 00:04:13.270 align:start position:0%
before there's better visual reasoning
and<00:04:09.799><c> OCR</c><00:04:10.799><c> it's</c><00:04:11.040><c> not</c><00:04:11.280><c> a</c><00:04:11.599><c> full-on</c><00:04:12.200><c> OCR</c><00:04:12.720><c> engine</c>

00:04:13.270 --> 00:04:13.280 align:start position:0%
and OCR it's not a full-on OCR engine
 

00:04:13.280 --> 00:04:15.110 align:start position:0%
and OCR it's not a full-on OCR engine
there<00:04:13.400><c> are</c><00:04:13.560><c> lots</c><00:04:13.760><c> of</c><00:04:13.920><c> OCR</c><00:04:14.360><c> tools</c><00:04:14.640><c> that</c><00:04:14.799><c> run</c><00:04:14.959><c> on</c>

00:04:15.110 --> 00:04:15.120 align:start position:0%
there are lots of OCR tools that run on
 

00:04:15.120 --> 00:04:16.749 align:start position:0%
there are lots of OCR tools that run on
low<00:04:15.480><c> powerered</c><00:04:15.599><c> machines</c><00:04:16.120><c> that</c><00:04:16.320><c> perform</c>

00:04:16.749 --> 00:04:16.759 align:start position:0%
low powerered machines that perform
 

00:04:16.759 --> 00:04:18.789 align:start position:0%
low powerered machines that perform
better<00:04:17.400><c> and</c><00:04:17.519><c> it's</c><00:04:17.720><c> definitely</c><00:04:18.040><c> not</c><00:04:18.280><c> great</c><00:04:18.519><c> at</c>

00:04:18.789 --> 00:04:18.799 align:start position:0%
better and it's definitely not great at
 

00:04:18.799 --> 00:04:21.629 align:start position:0%
better and it's definitely not great at
what<00:04:19.000><c> is</c><00:04:19.239><c> often</c><00:04:19.560><c> called</c><00:04:19.959><c> icr</c><00:04:20.479><c> for</c><00:04:20.759><c> handwriting</c>

00:04:21.629 --> 00:04:21.639 align:start position:0%
what is often called icr for handwriting
 

00:04:21.639 --> 00:04:24.230 align:start position:0%
what is often called icr for handwriting
but<00:04:21.799><c> it's</c><00:04:22.000><c> still</c><00:04:22.479><c> amazing</c><00:04:23.000><c> to</c><00:04:23.160><c> see</c><00:04:23.880><c> how</c><00:04:24.040><c> much</c>

00:04:24.230 --> 00:04:24.240 align:start position:0%
but it's still amazing to see how much
 

00:04:24.240 --> 00:04:26.350 align:start position:0%
but it's still amazing to see how much
better<00:04:24.560><c> this</c><00:04:24.680><c> is</c><00:04:24.880><c> getting</c><00:04:25.479><c> and</c><00:04:25.600><c> it</c><00:04:25.680><c> won't</c><00:04:26.000><c> be</c>

00:04:26.350 --> 00:04:26.360 align:start position:0%
better this is getting and it won't be
 

00:04:26.360 --> 00:04:29.950 align:start position:0%
better this is getting and it won't be
much<00:04:26.680><c> longer</c><00:04:27.240><c> before</c><00:04:27.759><c> those</c><00:04:28.000><c> OCR</c><00:04:28.479><c> tools</c><00:04:28.919><c> just</c>

00:04:29.950 --> 00:04:29.960 align:start position:0%
much longer before those OCR tools just
 

00:04:29.960 --> 00:04:32.350 align:start position:0%
much longer before those OCR tools just
they<00:04:30.080><c> just</c><00:04:30.240><c> can't</c><00:04:30.560><c> compete</c><00:04:31.560><c> it</c><00:04:31.720><c> becomes</c><00:04:32.039><c> more</c>

00:04:32.350 --> 00:04:32.360 align:start position:0%
they just can't compete it becomes more
 

00:04:32.360 --> 00:04:35.830 align:start position:0%
they just can't compete it becomes more
interesting<00:04:32.960><c> when</c><00:04:33.120><c> you</c><00:04:33.320><c> combine</c><00:04:34.039><c> OCR</c><00:04:35.039><c> with</c>

00:04:35.830 --> 00:04:35.840 align:start position:0%
interesting when you combine OCR with
 

00:04:35.840 --> 00:04:38.029 align:start position:0%
interesting when you combine OCR with
understanding<00:04:36.120><c> the</c><00:04:36.280><c> meaning</c><00:04:36.800><c> of</c><00:04:37.000><c> the</c><00:04:37.160><c> image</c>

00:04:38.029 --> 00:04:38.039 align:start position:0%
understanding the meaning of the image
 

00:04:38.039 --> 00:04:39.550 align:start position:0%
understanding the meaning of the image
so<00:04:38.320><c> here's</c><00:04:38.520><c> an</c><00:04:38.720><c> example</c><00:04:39.160><c> from</c><00:04:39.360><c> their</c>

00:04:39.550 --> 00:04:39.560 align:start position:0%
so here's an example from their
 

00:04:39.560 --> 00:04:41.909 align:start position:0%
so here's an example from their
announcement<00:04:40.039><c> paper</c><00:04:40.720><c> it's</c><00:04:40.880><c> an</c><00:04:41.120><c> image</c><00:04:41.479><c> showing</c>

00:04:41.909 --> 00:04:41.919 align:start position:0%
announcement paper it's an image showing
 

00:04:41.919 --> 00:04:44.950 align:start position:0%
announcement paper it's an image showing
flight<00:04:42.320><c> info</c><00:04:43.080><c> and</c><00:04:43.240><c> the</c><00:04:43.400><c> prompt</c><00:04:43.800><c> is</c><00:04:44.400><c> I</c><00:04:44.560><c> need</c><00:04:44.720><c> to</c>

00:04:44.950 --> 00:04:44.960 align:start position:0%
flight info and the prompt is I need to
 

00:04:44.960 --> 00:04:48.070 align:start position:0%
flight info and the prompt is I need to
pick<00:04:45.120><c> up</c><00:04:45.479><c> my</c><00:04:45.800><c> wife</c><00:04:46.400><c> I</c><00:04:46.520><c> live</c><00:04:46.680><c> in</c><00:04:46.840><c> San</c><00:04:47.080><c> Jose</c><00:04:47.919><c> when</c>

00:04:48.070 --> 00:04:48.080 align:start position:0%
pick up my wife I live in San Jose when
 

00:04:48.080 --> 00:04:51.070 align:start position:0%
pick up my wife I live in San Jose when
should<00:04:48.280><c> I</c><00:04:48.440><c> leave</c><00:04:49.320><c> and</c><00:04:49.440><c> the</c><00:04:49.600><c> model</c><00:04:49.919><c> spits</c><00:04:50.320><c> out</c>

00:04:51.070 --> 00:04:51.080 align:start position:0%
should I leave and the model spits out
 

00:04:51.080 --> 00:04:53.590 align:start position:0%
should I leave and the model spits out
based<00:04:51.360><c> on</c><00:04:51.520><c> the</c><00:04:51.759><c> info</c><00:04:52.160><c> provided</c><00:04:52.800><c> the</c><00:04:52.960><c> flight</c><00:04:53.360><c> is</c>

00:04:53.590 --> 00:04:53.600 align:start position:0%
based on the info provided the flight is
 

00:04:53.600 --> 00:04:57.070 align:start position:0%
based on the info provided the flight is
scheduled<00:04:54.000><c> to</c><00:04:54.160><c> arrive</c><00:04:54.479><c> at</c><00:04:54.680><c> 11:51</c><00:04:55.680><c> at</c><00:04:55.960><c> SFO</c><00:04:56.960><c> if</c>

00:04:57.070 --> 00:04:57.080 align:start position:0%
scheduled to arrive at 11:51 at SFO if
 

00:04:57.080 --> 00:04:59.110 align:start position:0%
scheduled to arrive at 11:51 at SFO if
you<00:04:57.280><c> live</c><00:04:57.560><c> in</c><00:04:57.720><c> San</c><00:04:58.000><c> Jose</c><00:04:58.360><c> you</c><00:04:58.479><c> should</c><00:04:58.800><c> consider</c>

00:04:59.110 --> 00:04:59.120 align:start position:0%
you live in San Jose you should consider
 

00:04:59.120 --> 00:05:01.310 align:start position:0%
you live in San Jose you should consider
the<00:04:59.240><c> travel</c><00:04:59.759><c> time</c><00:04:59.960><c> which</c><00:05:00.080><c> is</c><00:05:00.280><c> 45</c><00:05:00.840><c> to</c><00:05:01.000><c> 60</c>

00:05:01.310 --> 00:05:01.320 align:start position:0%
the travel time which is 45 to 60
 

00:05:01.320 --> 00:05:03.950 align:start position:0%
the travel time which is 45 to 60
Minutes<00:05:02.039><c> depending</c><00:05:02.360><c> on</c><00:05:02.560><c> traffic</c><00:05:03.440><c> to</c><00:05:03.639><c> ensure</c>

00:05:03.950 --> 00:05:03.960 align:start position:0%
Minutes depending on traffic to ensure
 

00:05:03.960 --> 00:05:05.670 align:start position:0%
Minutes depending on traffic to ensure
you<00:05:04.120><c> have</c><00:05:04.320><c> enough</c><00:05:04.560><c> time</c><00:05:05.000><c> you</c><00:05:05.120><c> should</c><00:05:05.400><c> leave</c>

00:05:05.670 --> 00:05:05.680 align:start position:0%
you have enough time you should leave
 

00:05:05.680 --> 00:05:08.110 align:start position:0%
you have enough time you should leave
San<00:05:05.960><c> Jose</c><00:05:06.320><c> no</c><00:05:06.560><c> later</c><00:05:06.880><c> than</c><00:05:07.039><c> 11:</c><00:05:07.680><c> to</c><00:05:07.880><c> account</c>

00:05:08.110 --> 00:05:08.120 align:start position:0%
San Jose no later than 11: to account
 

00:05:08.120 --> 00:05:10.790 align:start position:0%
San Jose no later than 11: to account
for<00:05:08.400><c> traffic</c><00:05:08.919><c> and</c><00:05:09.160><c> unexpected</c><00:05:09.800><c> delays</c>

00:05:10.790 --> 00:05:10.800 align:start position:0%
for traffic and unexpected delays
 

00:05:10.800 --> 00:05:12.590 align:start position:0%
for traffic and unexpected delays
however<00:05:11.039><c> it's</c><00:05:11.199><c> a</c><00:05:11.440><c> good</c><00:05:11.680><c> idea</c><00:05:12.039><c> to</c><00:05:12.199><c> leave</c>

00:05:12.590 --> 00:05:12.600 align:start position:0%
however it's a good idea to leave
 

00:05:12.600 --> 00:05:16.029 align:start position:0%
however it's a good idea to leave
earlier<00:05:13.600><c> now</c><00:05:13.919><c> that</c><00:05:14.120><c> is</c><00:05:14.400><c> pretty</c><00:05:14.720><c> cool</c><00:05:15.520><c> combine</c>

00:05:16.029 --> 00:05:16.039 align:start position:0%
earlier now that is pretty cool combine
 

00:05:16.039 --> 00:05:17.790 align:start position:0%
earlier now that is pretty cool combine
this<00:05:16.320><c> with</c><00:05:16.520><c> function</c><00:05:16.880><c> calling</c><00:05:17.479><c> and</c><00:05:17.600><c> then</c>

00:05:17.790 --> 00:05:17.800 align:start position:0%
this with function calling and then
 

00:05:17.800 --> 00:05:19.710 align:start position:0%
this with function calling and then
maybe<00:05:18.000><c> you</c><00:05:18.199><c> get</c><00:05:18.360><c> the</c><00:05:18.520><c> actual</c><00:05:18.880><c> travel</c><00:05:19.280><c> info</c>

00:05:19.710 --> 00:05:19.720 align:start position:0%
maybe you get the actual travel info
 

00:05:19.720 --> 00:05:21.629 align:start position:0%
maybe you get the actual travel info
that<00:05:19.960><c> day</c><00:05:20.280><c> and</c><00:05:20.639><c> and</c><00:05:20.800><c> add</c><00:05:21.000><c> the</c><00:05:21.120><c> appointment</c><00:05:21.479><c> to</c>

00:05:21.629 --> 00:05:21.639 align:start position:0%
that day and and add the appointment to
 

00:05:21.639 --> 00:05:23.870 align:start position:0%
that day and and add the appointment to
the<00:05:21.800><c> calendar</c><00:05:22.360><c> maybe</c><00:05:22.759><c> update</c><00:05:23.080><c> it</c><00:05:23.280><c> as</c><00:05:23.520><c> travel</c>

00:05:23.870 --> 00:05:23.880 align:start position:0%
the calendar maybe update it as travel
 

00:05:23.880 --> 00:05:26.909 align:start position:0%
the calendar maybe update it as travel
info<00:05:24.360><c> changes</c><00:05:25.360><c> well</c><00:05:25.639><c> this</c><00:05:26.000><c> gets</c><00:05:26.440><c> really</c>

00:05:26.909 --> 00:05:26.919 align:start position:0%
info changes well this gets really
 

00:05:26.919 --> 00:05:29.270 align:start position:0%
info changes well this gets really
really<00:05:27.400><c> amazing</c><00:05:28.400><c> now</c><00:05:28.560><c> here's</c><00:05:28.720><c> a</c><00:05:28.840><c> cool</c><00:05:29.039><c> demo</c>

00:05:29.270 --> 00:05:29.280 align:start position:0%
really amazing now here's a cool demo
 

00:05:29.280 --> 00:05:31.950 align:start position:0%
really amazing now here's a cool demo
from<00:05:29.720><c> Ai</c><00:05:30.080><c> and</c><00:05:30.280><c> design</c><00:05:30.759><c> on</c><00:05:30.960><c> Twitter</c><00:05:31.680><c> where</c><00:05:31.800><c> he</c>

00:05:31.950 --> 00:05:31.960 align:start position:0%
from Ai and design on Twitter where he
 

00:05:31.960 --> 00:05:35.029 align:start position:0%
from Ai and design on Twitter where he
has<00:05:32.120><c> the</c><00:05:32.240><c> model</c><00:05:32.520><c> roast</c><00:05:33.199><c> himself</c><00:05:34.199><c> fashionably</c>

00:05:35.029 --> 00:05:35.039 align:start position:0%
has the model roast himself fashionably
 

00:05:35.039 --> 00:05:36.790 align:start position:0%
has the model roast himself fashionably
bald<00:05:35.440><c> and</c>

00:05:36.790 --> 00:05:36.800 align:start position:0%
bald and
 

00:05:36.800 --> 00:05:39.830 align:start position:0%
bald and
bearded<00:05:37.800><c> I</c><00:05:38.000><c> resemble</c><00:05:38.440><c> that</c><00:05:38.680><c> remark</c><00:05:39.680><c> well</c>

00:05:39.830 --> 00:05:39.840 align:start position:0%
bearded I resemble that remark well
 

00:05:39.840 --> 00:05:41.150 align:start position:0%
bearded I resemble that remark well
here's<00:05:40.039><c> another</c><00:05:40.280><c> one</c><00:05:40.520><c> I</c><00:05:40.639><c> think</c><00:05:40.800><c> is</c><00:05:40.960><c> really</c>

00:05:41.150 --> 00:05:41.160 align:start position:0%
here's another one I think is really
 

00:05:41.160 --> 00:05:43.309 align:start position:0%
here's another one I think is really
awesome<00:05:41.479><c> it</c><00:05:41.600><c> comes</c><00:05:41.880><c> from</c><00:05:42.360><c> Tremor</c><00:05:42.880><c> coder</c><00:05:43.199><c> in</c>

00:05:43.309 --> 00:05:43.319 align:start position:0%
awesome it comes from Tremor coder in
 

00:05:43.319 --> 00:05:45.710 align:start position:0%
awesome it comes from Tremor coder in
the<00:05:43.400><c> olama</c><00:05:43.880><c> Discord</c><00:05:44.800><c> are</c><00:05:44.960><c> you</c><00:05:45.120><c> signed</c><00:05:45.400><c> up</c><00:05:45.600><c> for</c>

00:05:45.710 --> 00:05:45.720 align:start position:0%
the olama Discord are you signed up for
 

00:05:45.720 --> 00:05:47.990 align:start position:0%
the olama Discord are you signed up for
the<00:05:45.800><c> olama</c><00:05:46.240><c> Discord</c><00:05:46.800><c> you</c><00:05:46.919><c> can</c><00:05:47.120><c> find</c><00:05:47.319><c> it</c><00:05:47.639><c> here</c>

00:05:47.990 --> 00:05:48.000 align:start position:0%
the olama Discord you can find it here
 

00:05:48.000 --> 00:05:49.189 align:start position:0%
the olama Discord you can find it here
at

00:05:49.189 --> 00:05:49.199 align:start position:0%
at
 

00:05:49.199 --> 00:05:52.390 align:start position:0%
at
discord.gg<00:05:50.240><c> olama</c><00:05:51.240><c> he</c><00:05:51.400><c> gives</c><00:05:51.600><c> the</c><00:05:51.720><c> model</c><00:05:52.160><c> an</c>

00:05:52.390 --> 00:05:52.400 align:start position:0%
discord.gg olama he gives the model an
 

00:05:52.400 --> 00:05:54.150 align:start position:0%
discord.gg olama he gives the model an
image<00:05:52.800><c> and</c><00:05:52.919><c> then</c><00:05:53.080><c> asks</c><00:05:53.360><c> for</c><00:05:53.600><c> recommendations</c>

00:05:54.150 --> 00:05:54.160 align:start position:0%
image and then asks for recommendations
 

00:05:54.160 --> 00:05:56.550 align:start position:0%
image and then asks for recommendations
for<00:05:54.360><c> edits</c><00:05:54.800><c> to</c><00:05:54.960><c> improve</c><00:05:55.360><c> that</c><00:05:55.520><c> image</c><00:05:56.360><c> it's</c>

00:05:56.550 --> 00:05:56.560 align:start position:0%
for edits to improve that image it's
 

00:05:56.560 --> 00:05:59.550 align:start position:0%
for edits to improve that image it's
such<00:05:56.720><c> a</c><00:05:56.960><c> cool</c><00:05:57.280><c> use</c><00:05:57.600><c> case</c><00:05:58.160><c> and</c><00:05:58.360><c> apparently</c><00:05:58.960><c> lava</c>

00:05:59.550 --> 00:05:59.560 align:start position:0%
such a cool use case and apparently lava
 

00:05:59.560 --> 00:06:03.870 align:start position:0%
such a cool use case and apparently lava
1.6<00:06:00.560><c> is</c><00:06:00.800><c> so</c><00:06:01.120><c> much</c><00:06:01.400><c> better</c><00:06:02.039><c> than</c><00:06:02.319><c> 1.5</c><00:06:03.120><c> was</c><00:06:03.360><c> for</c>

00:06:03.870 --> 00:06:03.880 align:start position:0%
1.6 is so much better than 1.5 was for
 

00:06:03.880 --> 00:06:06.430 align:start position:0%
1.6 is so much better than 1.5 was for
this<00:06:04.880><c> but</c><00:06:05.000><c> one</c><00:06:05.120><c> of</c><00:06:05.280><c> my</c><00:06:05.479><c> favorites</c><00:06:06.000><c> is</c><00:06:06.160><c> this</c>

00:06:06.430 --> 00:06:06.440 align:start position:0%
this but one of my favorites is this
 

00:06:06.440 --> 00:06:09.749 align:start position:0%
this but one of my favorites is this
poetr<00:06:07.080><c> camera</c><00:06:07.680><c> a</c><00:06:07.840><c> play</c><00:06:08.080><c> on</c><00:06:08.360><c> Polaroid</c><00:06:09.360><c> which</c>

00:06:09.749 --> 00:06:09.759 align:start position:0%
poetr camera a play on Polaroid which
 

00:06:09.759 --> 00:06:11.469 align:start position:0%
poetr camera a play on Polaroid which
having<00:06:10.039><c> worked</c><00:06:10.400><c> at</c><00:06:10.520><c> a</c><00:06:10.680><c> camera</c><00:06:11.000><c> shop</c><00:06:11.199><c> in</c><00:06:11.319><c> the</c><00:06:11.400><c> '</c>

00:06:11.469 --> 00:06:11.479 align:start position:0%
having worked at a camera shop in the '
 

00:06:11.479 --> 00:06:13.510 align:start position:0%
having worked at a camera shop in the '
80s<00:06:11.759><c> and</c><00:06:11.880><c> 90s</c><00:06:12.280><c> on</c><00:06:12.400><c> the</c><00:06:12.520><c> island</c><00:06:12.800><c> near</c><00:06:13.120><c> Miami</c>

00:06:13.510 --> 00:06:13.520 align:start position:0%
80s and 90s on the island near Miami
 

00:06:13.520 --> 00:06:16.390 align:start position:0%
80s and 90s on the island near Miami
where<00:06:13.639><c> I</c><00:06:13.759><c> grew</c><00:06:14.000><c> up</c><00:06:14.639><c> I</c><00:06:14.800><c> remember</c><00:06:15.199><c> well</c><00:06:16.080><c> it</c>

00:06:16.390 --> 00:06:16.400 align:start position:0%
where I grew up I remember well it
 

00:06:16.400 --> 00:06:18.469 align:start position:0%
where I grew up I remember well it
recognizes<00:06:17.120><c> what's</c><00:06:17.360><c> in</c><00:06:17.560><c> front</c><00:06:17.800><c> of</c><00:06:17.919><c> the</c><00:06:18.080><c> camera</c>

00:06:18.469 --> 00:06:18.479 align:start position:0%
recognizes what's in front of the camera
 

00:06:18.479 --> 00:06:20.909 align:start position:0%
recognizes what's in front of the camera
then<00:06:18.680><c> generates</c><00:06:19.160><c> a</c><00:06:19.360><c> poem</c><00:06:19.720><c> based</c><00:06:19.960><c> on</c><00:06:20.080><c> it</c><00:06:20.840><c> I</c>

00:06:20.909 --> 00:06:20.919 align:start position:0%
then generates a poem based on it I
 

00:06:20.919 --> 00:06:22.589 align:start position:0%
then generates a poem based on it I
thought<00:06:21.080><c> it</c><00:06:21.199><c> might</c><00:06:21.400><c> be</c><00:06:21.680><c> fun</c><00:06:21.960><c> to</c><00:06:22.240><c> try</c><00:06:22.400><c> to</c>

00:06:22.589 --> 00:06:22.599 align:start position:0%
thought it might be fun to try to
 

00:06:22.599 --> 00:06:25.230 align:start position:0%
thought it might be fun to try to
recreate<00:06:23.039><c> the</c><00:06:23.160><c> software</c><00:06:23.680><c> part</c><00:06:23.840><c> of</c><00:06:24.000><c> this</c><00:06:24.680><c> so</c><00:06:25.160><c> I</c>

00:06:25.230 --> 00:06:25.240 align:start position:0%
recreate the software part of this so I
 

00:06:25.240 --> 00:06:27.550 align:start position:0%
recreate the software part of this so I
built<00:06:25.520><c> a</c><00:06:25.720><c> simple</c><00:06:26.240><c> example</c><00:06:26.880><c> and</c><00:06:27.080><c> let's</c><00:06:27.280><c> take</c><00:06:27.400><c> a</c>

00:06:27.550 --> 00:06:27.560 align:start position:0%
built a simple example and let's take a
 

00:06:27.560 --> 00:06:30.390 align:start position:0%
built a simple example and let's take a
look<00:06:28.319><c> right</c><00:06:28.479><c> up</c><00:06:28.720><c> top</c><00:06:28.960><c> I</c><00:06:29.039><c> readed</c><00:06:29.639><c> a</c><00:06:29.840><c> style</c><00:06:30.240><c> and</c>

00:06:30.390 --> 00:06:30.400 align:start position:0%
look right up top I readed a style and
 

00:06:30.400 --> 00:06:32.390 align:start position:0%
look right up top I readed a style and
an<00:06:30.599><c> image</c><00:06:30.960><c> path</c><00:06:31.199><c> from</c><00:06:31.360><c> the</c><00:06:31.479><c> command</c><00:06:31.759><c> line</c><00:06:32.280><c> then</c>

00:06:32.390 --> 00:06:32.400 align:start position:0%
an image path from the command line then
 

00:06:32.400 --> 00:06:34.670 align:start position:0%
an image path from the command line then
I<00:06:32.520><c> create</c><00:06:32.759><c> a</c><00:06:32.880><c> new</c><00:06:33.039><c> AMA</c><00:06:33.560><c> object</c><00:06:34.080><c> oh</c><00:06:34.280><c> by</c><00:06:34.400><c> the</c><00:06:34.520><c> way</c>

00:06:34.670 --> 00:06:34.680 align:start position:0%
I create a new AMA object oh by the way
 

00:06:34.680 --> 00:06:38.070 align:start position:0%
I create a new AMA object oh by the way
this<00:06:34.759><c> is</c><00:06:34.919><c> using</c><00:06:35.240><c> the</c><00:06:35.360><c> new</c><00:06:35.599><c> AMA</c><00:06:36.080><c> JS</c><00:06:37.000><c> Library</c><00:06:38.000><c> I</c>

00:06:38.070 --> 00:06:38.080 align:start position:0%
this is using the new AMA JS Library I
 

00:06:38.080 --> 00:06:40.589 align:start position:0%
this is using the new AMA JS Library I
read<00:06:38.319><c> in</c><00:06:38.560><c> the</c><00:06:38.720><c> image</c><00:06:39.000><c> file</c><00:06:39.400><c> into</c><00:06:39.639><c> a</c><00:06:39.759><c> buffer</c><00:06:40.440><c> and</c>

00:06:40.589 --> 00:06:40.599 align:start position:0%
read in the image file into a buffer and
 

00:06:40.599 --> 00:06:44.150 align:start position:0%
read in the image file into a buffer and
then<00:06:40.840><c> base</c><00:06:41.120><c> 64</c><00:06:41.639><c> encode</c><00:06:42.199><c> the</c><00:06:42.440><c> image</c><00:06:43.440><c> now</c><00:06:43.840><c> I</c><00:06:43.960><c> call</c>

00:06:44.150 --> 00:06:44.160 align:start position:0%
then base 64 encode the image now I call
 

00:06:44.160 --> 00:06:46.589 align:start position:0%
then base 64 encode the image now I call
the<00:06:44.280><c> olama</c><00:06:44.759><c> chat</c><00:06:45.039><c> endpoint</c><00:06:45.840><c> passing</c><00:06:46.160><c> the</c><00:06:46.319><c> lava</c>

00:06:46.589 --> 00:06:46.599 align:start position:0%
the olama chat endpoint passing the lava
 

00:06:46.599 --> 00:06:49.390 align:start position:0%
the olama chat endpoint passing the lava
Model<00:06:46.919><c> A</c><00:06:47.120><c> simple</c><00:06:47.440><c> prompt</c><00:06:47.880><c> in</c><00:06:48.039><c> the</c><00:06:48.240><c> image</c><00:06:49.240><c> now</c>

00:06:49.390 --> 00:06:49.400 align:start position:0%
Model A simple prompt in the image now
 

00:06:49.400 --> 00:06:51.990 align:start position:0%
Model A simple prompt in the image now
the<00:06:49.560><c> first</c><00:06:49.919><c> attempt</c><00:06:50.400><c> I</c><00:06:50.560><c> had</c><00:06:50.759><c> at</c><00:06:50.960><c> this</c><00:06:51.599><c> you</c><00:06:51.800><c> I</c>

00:06:51.990 --> 00:06:52.000 align:start position:0%
the first attempt I had at this you I
 

00:06:52.000 --> 00:06:54.670 align:start position:0%
the first attempt I had at this you I
tried<00:06:52.199><c> to</c><00:06:52.360><c> have</c><00:06:52.479><c> it</c><00:06:52.639><c> write</c><00:06:52.919><c> the</c><00:06:53.080><c> poem</c><00:06:53.680><c> itself</c>

00:06:54.670 --> 00:06:54.680 align:start position:0%
tried to have it write the poem itself
 

00:06:54.680 --> 00:06:57.189 align:start position:0%
tried to have it write the poem itself
but<00:06:55.240><c> lava</c><00:06:55.639><c> seems</c><00:06:55.879><c> to</c><00:06:56.039><c> suck</c><00:06:56.479><c> at</c><00:06:56.639><c> coming</c><00:06:56.879><c> up</c><00:06:57.039><c> with</c>

00:06:57.189 --> 00:06:57.199 align:start position:0%
but lava seems to suck at coming up with
 

00:06:57.199 --> 00:06:58.790 align:start position:0%
but lava seems to suck at coming up with
different<00:06:57.520><c> styles</c><00:06:57.840><c> of</c><00:06:58.039><c> poems</c><00:06:58.360><c> it</c><00:06:58.479><c> was</c><00:06:58.639><c> the</c>

00:06:58.790 --> 00:06:58.800 align:start position:0%
different styles of poems it was the
 

00:06:58.800 --> 00:07:00.790 align:start position:0%
different styles of poems it was the
same<00:06:59.000><c> thing</c><00:06:59.160><c> over</c><00:06:59.400><c> over</c><00:06:59.520><c> and</c><00:06:59.680><c> over</c><00:06:59.879><c> again</c><00:07:00.560><c> so</c>

00:07:00.790 --> 00:07:00.800 align:start position:0%
same thing over over and over again so
 

00:07:00.800 --> 00:07:02.749 align:start position:0%
same thing over over and over again so
instead<00:07:01.240><c> I</c><00:07:01.360><c> just</c><00:07:01.560><c> have</c><00:07:01.720><c> lava</c><00:07:02.160><c> describe</c><00:07:02.639><c> the</c>

00:07:02.749 --> 00:07:02.759 align:start position:0%
instead I just have lava describe the
 

00:07:02.759 --> 00:07:05.029 align:start position:0%
instead I just have lava describe the
image<00:07:03.360><c> and</c><00:07:03.479><c> then</c><00:07:03.599><c> I</c><00:07:03.759><c> passed</c><00:07:04.039><c> that</c><00:07:04.280><c> description</c>

00:07:05.029 --> 00:07:05.039 align:start position:0%
image and then I passed that description
 

00:07:05.039 --> 00:07:07.830 align:start position:0%
image and then I passed that description
to<00:07:05.240><c> llama</c><00:07:05.639><c> 2</c><00:07:06.080><c> and</c><00:07:06.319><c> had</c><00:07:06.599><c> it</c><00:07:06.800><c> make</c><00:07:06.960><c> the</c><00:07:07.120><c> poem</c><00:07:07.680><c> in</c>

00:07:07.830 --> 00:07:07.840 align:start position:0%
to llama 2 and had it make the poem in
 

00:07:07.840 --> 00:07:09.909 align:start position:0%
to llama 2 and had it make the poem in
the<00:07:08.000><c> style</c><00:07:08.240><c> of</c><00:07:08.400><c> someone</c><00:07:09.160><c> and</c><00:07:09.319><c> it's</c><00:07:09.599><c> pretty</c>

00:07:09.909 --> 00:07:09.919 align:start position:0%
the style of someone and it's pretty
 

00:07:09.919 --> 00:07:12.629 align:start position:0%
the style of someone and it's pretty
cool<00:07:10.599><c> let's</c><00:07:10.800><c> try</c><00:07:11.039><c> one</c><00:07:11.759><c> it's</c><00:07:11.879><c> a</c><00:07:12.039><c> picture</c><00:07:12.319><c> of</c><00:07:12.440><c> a</c>

00:07:12.629 --> 00:07:12.639 align:start position:0%
cool let's try one it's a picture of a
 

00:07:12.639 --> 00:07:16.110 align:start position:0%
cool let's try one it's a picture of a
motion<00:07:13.000><c> sensor</c><00:07:13.440><c> from</c><00:07:13.680><c> envas</c><00:07:14.599><c> and</c><00:07:14.759><c> it</c><00:07:15.120><c> does</c><00:07:15.440><c> an</c>

00:07:16.110 --> 00:07:16.120 align:start position:0%
motion sensor from envas and it does an
 

00:07:16.120 --> 00:07:19.629 align:start position:0%
motion sensor from envas and it does an
okay<00:07:16.960><c> job</c><00:07:17.960><c> next</c><00:07:18.319><c> is</c><00:07:18.440><c> a</c><00:07:18.639><c> blurry</c><00:07:19.120><c> image</c><00:07:19.400><c> of</c><00:07:19.520><c> a</c>

00:07:19.629 --> 00:07:19.639 align:start position:0%
okay job next is a blurry image of a
 

00:07:19.639 --> 00:07:22.110 align:start position:0%
okay job next is a blurry image of a
stream<00:07:20.000><c> deck</c><00:07:20.400><c> which</c><00:07:20.840><c> it's</c><00:07:21.280><c> interpreted</c><00:07:21.919><c> as</c>

00:07:22.110 --> 00:07:22.120 align:start position:0%
stream deck which it's interpreted as
 

00:07:22.120 --> 00:07:24.909 align:start position:0%
stream deck which it's interpreted as
some<00:07:22.360><c> DJ</c><00:07:22.840><c> equipment</c><00:07:23.680><c> I'll</c><00:07:23.840><c> skip</c><00:07:24.160><c> that</c><00:07:24.280><c> one</c><00:07:24.759><c> but</c>

00:07:24.909 --> 00:07:24.919 align:start position:0%
some DJ equipment I'll skip that one but
 

00:07:24.919 --> 00:07:27.350 align:start position:0%
some DJ equipment I'll skip that one but
the<00:07:25.160><c> third</c><00:07:25.479><c> try</c><00:07:25.879><c> is</c><00:07:26.000><c> a</c><00:07:26.160><c> view</c><00:07:26.440><c> of</c><00:07:26.599><c> the</c><00:07:26.800><c> sunset</c>

00:07:27.350 --> 00:07:27.360 align:start position:0%
the third try is a view of the sunset
 

00:07:27.360 --> 00:07:30.070 align:start position:0%
the third try is a view of the sunset
from<00:07:27.560><c> our</c><00:07:27.840><c> house</c><00:07:28.720><c> you</c><00:07:28.840><c> can</c><00:07:29.000><c> see</c><00:07:29.400><c> Mount</c><00:07:29.599><c> rineer</c>

00:07:30.070 --> 00:07:30.080 align:start position:0%
from our house you can see Mount rineer
 

00:07:30.080 --> 00:07:32.629 align:start position:0%
from our house you can see Mount rineer
over<00:07:30.319><c> here</c><00:07:30.599><c> and</c><00:07:30.800><c> and</c><00:07:30.960><c> downtown</c><00:07:31.639><c> Seattle</c>

00:07:32.629 --> 00:07:32.639 align:start position:0%
over here and and downtown Seattle
 

00:07:32.639 --> 00:07:35.430 align:start position:0%
over here and and downtown Seattle
gorgeous<00:07:33.199><c> view</c><00:07:33.960><c> it's</c><00:07:34.160><c> so</c><00:07:34.479><c> gorgeous</c><00:07:34.840><c> waking</c><00:07:35.160><c> up</c>

00:07:35.430 --> 00:07:35.440 align:start position:0%
gorgeous view it's so gorgeous waking up
 

00:07:35.440 --> 00:07:37.950 align:start position:0%
gorgeous view it's so gorgeous waking up
this<00:07:36.240><c> and</c><00:07:36.479><c> here</c><00:07:36.599><c> is</c><00:07:36.800><c> what</c><00:07:36.919><c> it</c><00:07:37.039><c> comes</c><00:07:37.280><c> up</c><00:07:37.680><c> with</c>

00:07:37.950 --> 00:07:37.960 align:start position:0%
this and here is what it comes up with
 

00:07:37.960 --> 00:07:41.990 align:start position:0%
this and here is what it comes up with
for<00:07:38.319><c> Dr</c><00:07:38.720><c> Seuss</c><00:07:39.720><c> Oh</c><00:07:40.000><c> My</c><00:07:40.319><c> Oh</c><00:07:40.599><c> Me</c><00:07:41.000><c> Oh</c><00:07:41.280><c> What</c><00:07:41.440><c> A</c><00:07:41.720><c> Sight</c>

00:07:41.990 --> 00:07:42.000 align:start position:0%
for Dr Seuss Oh My Oh Me Oh What A Sight
 

00:07:42.000 --> 00:07:45.110 align:start position:0%
for Dr Seuss Oh My Oh Me Oh What A Sight
to<00:07:42.199><c> see</c><00:07:42.919><c> a</c><00:07:43.120><c> Sunset</c><00:07:43.680><c> View</c><00:07:44.120><c> so</c><00:07:44.479><c> Serene</c><00:07:44.919><c> and</c>

00:07:45.110 --> 00:07:45.120 align:start position:0%
to see a Sunset View so Serene and
 

00:07:45.120 --> 00:07:47.589 align:start position:0%
to see a Sunset View so Serene and
bright<00:07:45.479><c> you'll</c><00:07:45.720><c> see</c><00:07:46.520><c> in</c><00:07:46.680><c> the</c><00:07:46.879><c> background</c><00:07:47.440><c> a</c>

00:07:47.589 --> 00:07:47.599 align:start position:0%
bright you'll see in the background a
 

00:07:47.599 --> 00:07:50.149 align:start position:0%
bright you'll see in the background a
house<00:07:47.800><c> Stands</c><00:07:48.240><c> Tall</c><00:07:48.520><c> and</c><00:07:48.759><c> proud</c><00:07:49.560><c> overlooking</c>

00:07:50.149 --> 00:07:50.159 align:start position:0%
house Stands Tall and proud overlooking
 

00:07:50.159 --> 00:07:52.790 align:start position:0%
house Stands Tall and proud overlooking
the<00:07:50.319><c> water</c><00:07:50.800><c> reflecting</c><00:07:51.319><c> the</c><00:07:51.479><c> day's</c><00:07:51.840><c> crowd</c>

00:07:52.790 --> 00:07:52.800 align:start position:0%
the water reflecting the day's crowd
 

00:07:52.800 --> 00:07:55.070 align:start position:0%
the water reflecting the day's crowd
above<00:07:53.199><c> the</c><00:07:53.479><c> house</c><00:07:53.759><c> clouds</c><00:07:54.120><c> drift</c><00:07:54.479><c> by</c><00:07:54.680><c> in</c><00:07:54.840><c> the</c>

00:07:55.070 --> 00:07:55.080 align:start position:0%
above the house clouds drift by in the
 

00:07:55.080 --> 00:07:57.629 align:start position:0%
above the house clouds drift by in the
sky<00:07:55.759><c> tinted</c><00:07:56.199><c> with</c><00:07:56.400><c> colors</c><00:07:56.840><c> that</c><00:07:57.120><c> make</c><00:07:57.360><c> you</c>

00:07:57.629 --> 00:07:57.639 align:start position:0%
sky tinted with colors that make you
 

00:07:57.639 --> 00:08:00.749 align:start position:0%
sky tinted with colors that make you
sigh<00:07:58.560><c> from</c><00:07:58.840><c> warm</c><00:07:59.360><c> oranges</c><00:07:59.720><c> to</c><00:07:59.919><c> cooler</c><00:08:00.280><c> Blues</c>

00:08:00.749 --> 00:08:00.759 align:start position:0%
sigh from warm oranges to cooler Blues
 

00:08:00.759 --> 00:08:03.070 align:start position:0%
sigh from warm oranges to cooler Blues
they<00:08:00.960><c> play</c><00:08:01.199><c> their</c><00:08:01.400><c> part</c><00:08:02.120><c> in</c><00:08:02.400><c> this</c><00:08:02.599><c> Sunset</c>

00:08:03.070 --> 00:08:03.080 align:start position:0%
they play their part in this Sunset
 

00:08:03.080 --> 00:08:05.589 align:start position:0%
they play their part in this Sunset
scene<00:08:03.520><c> that</c><00:08:03.800><c> captures</c><00:08:04.199><c> your</c><00:08:04.440><c> heart</c><00:08:05.280><c> in</c><00:08:05.440><c> the</c>

00:08:05.589 --> 00:08:05.599 align:start position:0%
scene that captures your heart in the
 

00:08:05.599 --> 00:08:07.510 align:start position:0%
scene that captures your heart in the
foreground<00:08:06.159><c> our</c><00:08:06.319><c> railing</c><00:08:06.720><c> can</c><00:08:06.879><c> be</c><00:08:07.000><c> seen</c>

00:08:07.510 --> 00:08:07.520 align:start position:0%
foreground our railing can be seen
 

00:08:07.520 --> 00:08:09.350 align:start position:0%
foreground our railing can be seen
suggesting<00:08:08.000><c> we're</c><00:08:08.240><c> up</c><00:08:08.479><c> high</c><00:08:08.680><c> in</c><00:08:08.879><c> this</c><00:08:09.039><c> sweet</c>

00:08:09.350 --> 00:08:09.360 align:start position:0%
suggesting we're up high in this sweet
 

00:08:09.360 --> 00:08:12.149 align:start position:0%
suggesting we're up high in this sweet
dream<00:08:10.360><c> the</c><00:08:10.520><c> Shadows</c><00:08:10.960><c> grow</c><00:08:11.240><c> long</c><00:08:11.560><c> and</c><00:08:11.720><c> the</c><00:08:11.879><c> sun</c>

00:08:12.149 --> 00:08:12.159 align:start position:0%
dream the Shadows grow long and the sun
 

00:08:12.159 --> 00:08:14.869 align:start position:0%
dream the Shadows grow long and the sun
descends<00:08:13.080><c> early</c><00:08:13.520><c> evening</c><00:08:14.120><c> oh</c><00:08:14.479><c> what</c><00:08:14.639><c> a</c>

00:08:14.869 --> 00:08:14.879 align:start position:0%
descends early evening oh what a
 

00:08:14.879 --> 00:08:17.790 align:start position:0%
descends early evening oh what a
wonderful<00:08:15.440><c> friend</c><00:08:16.319><c> no</c><00:08:16.639><c> text</c><00:08:16.960><c> to</c><00:08:17.159><c> distract</c><00:08:17.560><c> or</c>

00:08:17.790 --> 00:08:17.800 align:start position:0%
wonderful friend no text to distract or
 

00:08:17.800 --> 00:08:20.550 align:start position:0%
wonderful friend no text to distract or
confuse<00:08:18.360><c> just</c><00:08:18.599><c> pure</c><00:08:18.879><c> delight</c><00:08:19.599><c> in</c><00:08:19.840><c> this</c><00:08:20.080><c> image</c>

00:08:20.550 --> 00:08:20.560 align:start position:0%
confuse just pure delight in this image
 

00:08:20.560 --> 00:08:23.270 align:start position:0%
confuse just pure delight in this image
that<00:08:20.680><c> fills</c><00:08:21.000><c> us</c><00:08:21.280><c> with</c><00:08:21.560><c> such</c><00:08:21.800><c> pure</c><00:08:22.080><c> sight</c><00:08:22.840><c> so</c>

00:08:23.270 --> 00:08:23.280 align:start position:0%
that fills us with such pure sight so
 

00:08:23.280 --> 00:08:26.270 align:start position:0%
that fills us with such pure sight so
come<00:08:23.479><c> and</c><00:08:23.720><c> gaze</c><00:08:24.120><c> upon</c><00:08:24.479><c> this</c><00:08:24.639><c> view</c><00:08:25.000><c> with</c><00:08:25.199><c> Glee</c><00:08:26.120><c> a</c>

00:08:26.270 --> 00:08:26.280 align:start position:0%
come and gaze upon this view with Glee a
 

00:08:26.280 --> 00:08:30.070 align:start position:0%
come and gaze upon this view with Glee a
sunset<00:08:26.759><c> scene</c><00:08:27.280><c> so</c><00:08:27.599><c> Serene</c><00:08:28.120><c> it's</c><00:08:28.479><c> plain</c><00:08:28.759><c> to</c><00:08:28.919><c> see</c>

00:08:30.070 --> 00:08:30.080 align:start position:0%
sunset scene so Serene it's plain to see
 

00:08:30.080 --> 00:08:32.709 align:start position:0%
sunset scene so Serene it's plain to see
yeah<00:08:30.919><c> that</c><00:08:31.120><c> that's</c><00:08:31.319><c> pretty</c><00:08:31.560><c> fun</c><00:08:32.120><c> so</c><00:08:32.440><c> that's</c><00:08:32.599><c> a</c>

00:08:32.709 --> 00:08:32.719 align:start position:0%
yeah that that's pretty fun so that's a
 

00:08:32.719 --> 00:08:34.750 align:start position:0%
yeah that that's pretty fun so that's a
little<00:08:32.919><c> of</c><00:08:33.080><c> what</c><00:08:33.200><c> you</c><00:08:33.279><c> can</c><00:08:33.440><c> do</c><00:08:33.719><c> with</c><00:08:34.000><c> lava</c><00:08:34.560><c> and</c>

00:08:34.750 --> 00:08:34.760 align:start position:0%
little of what you can do with lava and
 

00:08:34.760 --> 00:08:36.829 align:start position:0%
little of what you can do with lava and
what's<00:08:35.000><c> new</c><00:08:35.200><c> in</c><00:08:35.440><c> ama</c>

00:08:36.829 --> 00:08:36.839 align:start position:0%
what's new in ama
 

00:08:36.839 --> 00:08:39.190 align:start position:0%
what's new in ama
0.123<00:08:37.839><c> do</c><00:08:37.959><c> you</c><00:08:38.080><c> have</c><00:08:38.200><c> any</c><00:08:38.360><c> cool</c><00:08:38.680><c> projects</c><00:08:39.039><c> that</c>

00:08:39.190 --> 00:08:39.200 align:start position:0%
0.123 do you have any cool projects that
 

00:08:39.200 --> 00:08:41.589 align:start position:0%
0.123 do you have any cool projects that
you've<00:08:39.360><c> seen</c><00:08:39.640><c> with</c><00:08:39.800><c> lava</c><00:08:40.519><c> or</c><00:08:40.719><c> how</c><00:08:40.880><c> about</c><00:08:41.240><c> ideas</c>

00:08:41.589 --> 00:08:41.599 align:start position:0%
you've seen with lava or how about ideas
 

00:08:41.599 --> 00:08:42.870 align:start position:0%
you've seen with lava or how about ideas
for<00:08:41.839><c> things</c><00:08:42.000><c> that</c><00:08:42.120><c> you</c><00:08:42.200><c> want</c><00:08:42.320><c> to</c><00:08:42.479><c> build</c><00:08:42.760><c> with</c>

00:08:42.870 --> 00:08:42.880 align:start position:0%
for things that you want to build with
 

00:08:42.880 --> 00:08:45.310 align:start position:0%
for things that you want to build with
it<00:08:43.479><c> let</c><00:08:43.599><c> me</c><00:08:43.760><c> know</c><00:08:43.919><c> in</c><00:08:44.039><c> the</c><00:08:44.159><c> comments</c><00:08:44.560><c> below</c>

00:08:45.310 --> 00:08:45.320 align:start position:0%
it let me know in the comments below
 

00:08:45.320 --> 00:08:47.470 align:start position:0%
it let me know in the comments below
maybe<00:08:45.880><c> I'll</c><00:08:46.080><c> cover</c><00:08:46.440><c> what</c><00:08:46.519><c> you're</c><00:08:46.720><c> up</c><00:08:46.880><c> to</c><00:08:47.240><c> in</c><00:08:47.320><c> a</c>

00:08:47.470 --> 00:08:47.480 align:start position:0%
maybe I'll cover what you're up to in a
 

00:08:47.480 --> 00:08:51.470 align:start position:0%
maybe I'll cover what you're up to in a
future<00:08:47.839><c> video</c><00:08:48.600><c> thanks</c><00:08:48.839><c> so</c><00:08:48.959><c> much</c><00:08:49.120><c> for</c><00:08:49.279><c> watching</c>

00:08:51.470 --> 00:08:51.480 align:start position:0%
future video thanks so much for watching
 

00:08:51.480 --> 00:08:54.590 align:start position:0%
future video thanks so much for watching
goodbye<00:08:52.480><c> for</c><00:08:52.720><c> those</c><00:08:52.920><c> of</c><00:08:53.040><c> you</c><00:08:53.279><c> who</c><00:08:53.519><c> watch</c><00:08:54.080><c> me</c>

00:08:54.590 --> 00:08:54.600 align:start position:0%
goodbye for those of you who watch me
 

00:08:54.600 --> 00:08:57.190 align:start position:0%
goodbye for those of you who watch me
after<00:08:55.080><c> I</c><00:08:55.279><c> finish</c><00:08:56.000><c> I</c><00:08:56.120><c> feel</c><00:08:56.360><c> like</c><00:08:56.519><c> I</c><00:08:56.640><c> should</c><00:08:57.120><c> I</c>

00:08:57.190 --> 00:08:57.200 align:start position:0%
after I finish I feel like I should I
 

00:08:57.200 --> 00:09:00.269 align:start position:0%
after I finish I feel like I should I
should<00:08:57.399><c> do</c><00:08:57.640><c> something</c><00:08:58.640><c> have</c><00:08:58.839><c> to</c><00:08:59.680><c> maybe</c><00:08:59.959><c> that's</c>

00:09:00.269 --> 00:09:00.279 align:start position:0%
should do something have to maybe that's
 

00:09:00.279 --> 00:09:01.910 align:start position:0%
should do something have to maybe that's
be<00:09:00.440><c> going</c><00:09:00.560><c> to</c><00:09:00.680><c> be</c><00:09:00.800><c> my</c><00:09:00.959><c> signature</c><00:09:01.519><c> like</c>

00:09:01.910 --> 00:09:01.920 align:start position:0%
be going to be my signature like
 

00:09:01.920 --> 00:09:04.470 align:start position:0%
be going to be my signature like
thinking<00:09:02.240><c> of</c><00:09:02.399><c> something</c><00:09:02.760><c> random</c><00:09:03.160><c> to</c><00:09:03.320><c> do</c><00:09:04.320><c> in</c>

00:09:04.470 --> 00:09:04.480 align:start position:0%
thinking of something random to do in
 

00:09:04.480 --> 00:09:08.550 align:start position:0%
thinking of something random to do in
front<00:09:04.640><c> of</c><00:09:04.760><c> the</c>

00:09:08.550 --> 00:09:08.560 align:start position:0%
 
 

00:09:08.560 --> 00:09:12.269 align:start position:0%
 
camera<00:09:09.560><c> yeah</c><00:09:09.800><c> I</c><00:09:09.880><c> don't</c><00:09:10.040><c> know</c><00:09:11.000><c> I'll</c><00:09:11.160><c> think</c><00:09:11.360><c> of</c>

00:09:12.269 --> 00:09:12.279 align:start position:0%
camera yeah I don't know I'll think of
 

00:09:12.279 --> 00:09:16.079 align:start position:0%
camera yeah I don't know I'll think of
something<00:09:13.279><c> bye</c>

