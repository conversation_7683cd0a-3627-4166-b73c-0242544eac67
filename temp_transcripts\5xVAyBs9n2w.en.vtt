WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.929 align:start position:0%
 
this<00:00:00.149><c> is</c><00:00:00.299><c> Metta</c><00:00:00.539><c> based</c><00:00:00.780><c> part</c><00:00:01.140><c> 2</c><00:00:01.439><c> and</c><00:00:01.790><c> we</c><00:00:02.790><c> are</c>

00:00:02.929 --> 00:00:02.939 align:start position:0%
this is Metta based part 2 and we are
 

00:00:02.939 --> 00:00:04.610 align:start position:0%
this is Metta based part 2 and we are
back<00:00:03.179><c> over</c><00:00:03.510><c> here</c><00:00:03.750><c> we're</c><00:00:03.929><c> going</c><00:00:04.080><c> back</c><00:00:04.350><c> to</c><00:00:04.500><c> the</c>

00:00:04.610 --> 00:00:04.620 align:start position:0%
back over here we're going back to the
 

00:00:04.620 --> 00:00:07.160 align:start position:0%
back over here we're going back to the
setup<00:00:05.040><c> here</c><00:00:05.310><c> because</c><00:00:05.759><c> this</c><00:00:06.359><c> is</c><00:00:06.540><c> the</c><00:00:06.690><c> admin</c>

00:00:07.160 --> 00:00:07.170 align:start position:0%
setup here because this is the admin
 

00:00:07.170 --> 00:00:09.049 align:start position:0%
setup here because this is the admin
panel<00:00:07.410><c> of</c><00:00:07.740><c> Metta</c><00:00:08.040><c> base</c><00:00:08.280><c> it's</c><00:00:08.550><c> super</c><00:00:08.910><c> super</c>

00:00:09.049 --> 00:00:09.059 align:start position:0%
panel of Metta base it's super super
 

00:00:09.059 --> 00:00:12.890 align:start position:0%
panel of Metta base it's super super
awesome<00:00:09.710><c> this</c><00:00:10.710><c> uh</c><00:00:10.740><c> this</c><00:00:11.429><c> is</c><00:00:11.580><c> totally</c><00:00:12.150><c> free</c><00:00:12.599><c> and</c>

00:00:12.890 --> 00:00:12.900 align:start position:0%
awesome this uh this is totally free and
 

00:00:12.900 --> 00:00:17.150 align:start position:0%
awesome this uh this is totally free and
open<00:00:13.710><c> source</c><00:00:13.920><c> and</c><00:00:14.190><c> analytics</c><00:00:15.770><c> platform</c><00:00:16.770><c> it's</c>

00:00:17.150 --> 00:00:17.160 align:start position:0%
open source and analytics platform it's
 

00:00:17.160 --> 00:00:19.400 align:start position:0%
open source and analytics platform it's
great<00:00:17.490><c> for</c><00:00:17.640><c> business</c><00:00:17.789><c> analytics</c><00:00:18.410><c> that's</c>

00:00:19.400 --> 00:00:19.410 align:start position:0%
great for business analytics that's
 

00:00:19.410 --> 00:00:23.060 align:start position:0%
great for business analytics that's
great<00:00:19.710><c> for</c><00:00:19.920><c> communicating</c><00:00:20.880><c> as</c><00:00:21.000><c> a</c><00:00:21.029><c> team</c><00:00:21.800><c> all</c><00:00:22.800><c> of</c>

00:00:23.060 --> 00:00:23.070 align:start position:0%
great for communicating as a team all of
 

00:00:23.070 --> 00:00:25.009 align:start position:0%
great for communicating as a team all of
your<00:00:23.220><c> questions</c><00:00:23.820><c> now</c><00:00:24.029><c> I've</c><00:00:24.420><c> saved</c><00:00:24.750><c> this</c>

00:00:25.009 --> 00:00:25.019 align:start position:0%
your questions now I've saved this
 

00:00:25.019 --> 00:00:27.140 align:start position:0%
your questions now I've saved this
dashboard<00:00:25.320><c> so</c><00:00:25.859><c> I'm</c><00:00:25.920><c> gonna</c><00:00:26.130><c> press</c><00:00:26.369><c> save</c><00:00:26.730><c> here</c>

00:00:27.140 --> 00:00:27.150 align:start position:0%
dashboard so I'm gonna press save here
 

00:00:27.150 --> 00:00:30.230 align:start position:0%
dashboard so I'm gonna press save here
right<00:00:27.570><c> now</c><00:00:28.439><c> this</c><00:00:28.859><c> is</c><00:00:28.980><c> gonna</c><00:00:29.130><c> be</c><00:00:29.369><c> saved</c><00:00:29.640><c> in</c><00:00:29.970><c> the</c>

00:00:30.230 --> 00:00:30.240 align:start position:0%
right now this is gonna be saved in the
 

00:00:30.240 --> 00:00:31.849 align:start position:0%
right now this is gonna be saved in the
dashboard<00:00:30.720><c> section</c><00:00:30.990><c> everybody</c><00:00:31.529><c> on</c><00:00:31.679><c> my</c><00:00:31.830><c> team</c>

00:00:31.849 --> 00:00:31.859 align:start position:0%
dashboard section everybody on my team
 

00:00:31.859 --> 00:00:34.100 align:start position:0%
dashboard section everybody on my team
has<00:00:32.279><c> access</c><00:00:32.730><c> to</c><00:00:32.759><c> the</c><00:00:32.969><c> dashboard</c><00:00:33.149><c> section</c><00:00:33.719><c> so</c><00:00:34.020><c> I</c>

00:00:34.100 --> 00:00:34.110 align:start position:0%
has access to the dashboard section so I
 

00:00:34.110 --> 00:00:36.709 align:start position:0%
has access to the dashboard section so I
saved<00:00:34.500><c> a</c><00:00:34.649><c> question</c><00:00:35.160><c> you</c><00:00:36.030><c> save</c><00:00:36.239><c> the</c><00:00:36.390><c> question</c>

00:00:36.709 --> 00:00:36.719 align:start position:0%
saved a question you save the question
 

00:00:36.719 --> 00:00:38.360 align:start position:0%
saved a question you save the question
you<00:00:36.840><c> created</c><00:00:37.230><c> the</c><00:00:37.350><c> dashboard</c><00:00:37.829><c> within</c><00:00:38.010><c> one</c>

00:00:38.360 --> 00:00:38.370 align:start position:0%
you created the dashboard within one
 

00:00:38.370 --> 00:00:41.240 align:start position:0%
you created the dashboard within one
dashboard<00:00:38.640><c> I</c><00:00:39.239><c> can</c><00:00:39.690><c> add</c><00:00:40.050><c> many</c><00:00:40.320><c> questions</c><00:00:40.950><c> and</c><00:00:41.190><c> I</c>

00:00:41.240 --> 00:00:41.250 align:start position:0%
dashboard I can add many questions and I
 

00:00:41.250 --> 00:00:43.760 align:start position:0%
dashboard I can add many questions and I
can<00:00:41.309><c> even</c><00:00:41.640><c> put</c><00:00:41.820><c> a</c><00:00:41.850><c> filter</c><00:00:42.180><c> over</c><00:00:42.629><c> here</c><00:00:42.870><c> so</c><00:00:43.590><c> let's</c>

00:00:43.760 --> 00:00:43.770 align:start position:0%
can even put a filter over here so let's
 

00:00:43.770 --> 00:00:46.040 align:start position:0%
can even put a filter over here so let's
have<00:00:43.920><c> a</c><00:00:44.010><c> look</c><00:00:44.129><c> at</c><00:00:44.280><c> how</c><00:00:44.370><c> we</c><00:00:44.430><c> put</c><00:00:44.789><c> filters</c><00:00:45.090><c> within</c>

00:00:46.040 --> 00:00:46.050 align:start position:0%
have a look at how we put filters within
 

00:00:46.050 --> 00:00:48.920 align:start position:0%
have a look at how we put filters within
the<00:00:46.260><c> dashboard</c><00:00:46.770><c> I'm</c><00:00:46.890><c> gonna</c><00:00:47.100><c> do</c><00:00:47.280><c> add</c><00:00:48.000><c> a</c><00:00:48.420><c> filter</c>

00:00:48.920 --> 00:00:48.930 align:start position:0%
the dashboard I'm gonna do add a filter
 

00:00:48.930 --> 00:00:52.819 align:start position:0%
the dashboard I'm gonna do add a filter
and<00:00:49.530><c> it's</c><00:00:50.190><c> gonna</c><00:00:50.309><c> be</c><00:00:50.520><c> a</c><00:00:50.550><c> category</c><00:00:51.260><c> and</c><00:00:52.260><c> I'm</c>

00:00:52.819 --> 00:00:52.829 align:start position:0%
and it's gonna be a category and I'm
 

00:00:52.829 --> 00:00:56.689 align:start position:0%
and it's gonna be a category and I'm
gonna<00:00:52.980><c> do</c><00:00:53.750><c> what</c><00:00:54.750><c> over</c><00:00:54.930><c> here</c><00:00:55.320><c> it</c><00:00:55.620><c> is</c><00:00:56.160><c> the</c><00:00:56.489><c> order</c>

00:00:56.689 --> 00:00:56.699 align:start position:0%
gonna do what over here it is the order
 

00:00:56.699 --> 00:01:07.969 align:start position:0%
gonna do what over here it is the order
ID<00:00:56.969><c> okay</c><00:00:58.010><c> order</c><00:01:00.020><c> order</c><00:01:01.020><c> ID</c><00:01:05.570><c> order</c><00:01:06.570><c> ID</c><00:01:06.780><c> okay</c><00:01:07.740><c> and</c>

00:01:07.969 --> 00:01:07.979 align:start position:0%
ID okay order order ID order ID okay and
 

00:01:07.979 --> 00:01:10.789 align:start position:0%
ID okay order order ID order ID okay and
we<00:01:08.549><c> are</c><00:01:08.580><c> done</c><00:01:09.240><c> with</c><00:01:09.390><c> this</c><00:01:09.600><c> we</c><00:01:10.439><c> don't</c><00:01:10.500><c> have</c><00:01:10.710><c> the</c>

00:01:10.789 --> 00:01:10.799 align:start position:0%
we are done with this we don't have the
 

00:01:10.799 --> 00:01:14.270 align:start position:0%
we are done with this we don't have the
order<00:01:11.159><c> ID</c><00:01:11.280><c> over</c><00:01:11.760><c> here</c><00:01:12.439><c> okay</c><00:01:13.439><c> now</c><00:01:13.650><c> unless</c><00:01:13.979><c> we</c><00:01:14.130><c> do</c>

00:01:14.270 --> 00:01:14.280 align:start position:0%
order ID over here okay now unless we do
 

00:01:14.280 --> 00:01:16.539 align:start position:0%
order ID over here okay now unless we do
order<00:01:14.640><c> ID</c><00:01:14.760><c> is</c><00:01:14.909><c> an</c><00:01:15.090><c> existing</c><00:01:15.689><c> add</c><00:01:16.020><c> a</c><00:01:16.080><c> filter</c>

00:01:16.539 --> 00:01:16.549 align:start position:0%
order ID is an existing add a filter
 

00:01:16.549 --> 00:01:23.870 align:start position:0%
order ID is an existing add a filter
time<00:01:17.549><c> ID</c><00:01:19.040><c> enter</c><00:01:20.040><c> ID</c><00:01:20.729><c> default</c><00:01:21.330><c> value</c><00:01:22.880><c> alright</c>

00:01:23.870 --> 00:01:23.880 align:start position:0%
time ID enter ID default value alright
 

00:01:23.880 --> 00:01:26.350 align:start position:0%
time ID enter ID default value alright
that's<00:01:24.600><c> okay</c>

00:01:26.350 --> 00:01:26.360 align:start position:0%
that's okay
 

00:01:26.360 --> 00:01:28.670 align:start position:0%
that's okay
we're<00:01:27.360><c> gonna</c><00:01:27.509><c> get</c><00:01:27.750><c> into</c><00:01:27.960><c> this</c><00:01:28.049><c> in</c><00:01:28.229><c> a</c><00:01:28.320><c> second</c>

00:01:28.670 --> 00:01:28.680 align:start position:0%
we're gonna get into this in a second
 

00:01:28.680 --> 00:01:31.149 align:start position:0%
we're gonna get into this in a second
how<00:01:28.770><c> do</c><00:01:28.829><c> we</c><00:01:28.979><c> create</c><00:01:29.220><c> okay</c><00:01:29.909><c> forget</c><00:01:30.210><c> it</c><00:01:30.270><c> done</c>

00:01:31.149 --> 00:01:31.159 align:start position:0%
how do we create okay forget it done
 

00:01:31.159 --> 00:01:36.219 align:start position:0%
how do we create okay forget it done
remove<00:01:32.159><c> this</c><00:01:32.369><c> one</c><00:01:32.549><c> and</c><00:01:33.259><c> we</c><00:01:34.259><c> go</c><00:01:34.439><c> into</c><00:01:34.770><c> an</c><00:01:35.250><c> actual</c>

00:01:36.219 --> 00:01:36.229 align:start position:0%
remove this one and we go into an actual
 

00:01:36.229 --> 00:01:39.910 align:start position:0%
remove this one and we go into an actual
fish<00:01:37.229><c> we're</c><00:01:37.409><c> gonna</c><00:01:37.560><c> press</c><00:01:37.770><c> save</c><00:01:38.009><c> here</c><00:01:38.390><c> and</c>

00:01:39.910 --> 00:01:39.920 align:start position:0%
fish we're gonna press save here and
 

00:01:39.920 --> 00:01:43.690 align:start position:0%
fish we're gonna press save here and
you're<00:01:40.920><c> gonna</c><00:01:41.070><c> go</c><00:01:41.159><c> into</c><00:01:41.610><c> the</c><00:01:42.060><c> actual</c><00:01:42.450><c> question</c>

00:01:43.690 --> 00:01:43.700 align:start position:0%
you're gonna go into the actual question
 

00:01:43.700 --> 00:01:46.340 align:start position:0%
you're gonna go into the actual question
okay<00:01:44.700><c> so</c><00:01:45.060><c> again</c><00:01:45.240><c> we</c><00:01:45.299><c> have</c><00:01:45.509><c> the</c><00:01:45.630><c> dashboard</c><00:01:45.869><c> and</c>

00:01:46.340 --> 00:01:46.350 align:start position:0%
okay so again we have the dashboard and
 

00:01:46.350 --> 00:01:48.139 align:start position:0%
okay so again we have the dashboard and
it's<00:01:47.100><c> okay</c><00:01:47.369><c> to</c><00:01:47.460><c> play</c><00:01:47.579><c> around</c><00:01:47.790><c> with</c><00:01:47.909><c> save</c>

00:01:48.139 --> 00:01:48.149 align:start position:0%
it's okay to play around with save
 

00:01:48.149 --> 00:01:49.580 align:start position:0%
it's okay to play around with save
questions<00:01:48.450><c> you</c><00:01:48.810><c> won't</c><00:01:48.960><c> make</c><00:01:49.049><c> any</c><00:01:49.259><c> permanent</c>

00:01:49.580 --> 00:01:49.590 align:start position:0%
questions you won't make any permanent
 

00:01:49.590 --> 00:01:56.749 align:start position:0%
questions you won't make any permanent
changes<00:01:53.270><c> okay</c><00:01:54.270><c> so</c><00:01:54.750><c> I</c><00:01:55.070><c> have</c><00:01:56.070><c> over</c><00:01:56.280><c> here</c><00:01:56.430><c> a</c><00:01:56.490><c> query</c>

00:01:56.749 --> 00:01:56.759 align:start position:0%
changes okay so I have over here a query
 

00:01:56.759 --> 00:02:00.410 align:start position:0%
changes okay so I have over here a query
that<00:01:57.030><c> says</c><00:01:57.240><c> select</c><00:01:57.540><c> all</c><00:01:57.810><c> from</c><00:01:58.110><c> orders</c><00:01:59.210><c> yes</c><00:02:00.210><c> I</c>

00:02:00.410 --> 00:02:00.420 align:start position:0%
that says select all from orders yes I
 

00:02:00.420 --> 00:02:03.230 align:start position:0%
that says select all from orders yes I
haven't<00:02:00.719><c> created</c><00:02:00.869><c> that</c><00:02:01.259><c> as</c><00:02:01.530><c> you'll</c><00:02:01.680><c> notice</c><00:02:02.240><c> so</c>

00:02:03.230 --> 00:02:03.240 align:start position:0%
haven't created that as you'll notice so
 

00:02:03.240 --> 00:02:08.089 align:start position:0%
haven't created that as you'll notice so
that's<00:02:03.420><c> all</c><00:02:03.570><c> from</c><00:02:03.899><c> orders</c><00:02:04.439><c> where</c><00:02:06.649><c> let's</c><00:02:07.649><c> see</c>

00:02:08.089 --> 00:02:08.099 align:start position:0%
that's all from orders where let's see
 

00:02:08.099 --> 00:02:11.510 align:start position:0%
that's all from orders where let's see
waiter<00:02:09.830><c> here's</c><00:02:10.830><c> the</c><00:02:10.979><c> beautiful</c><00:02:11.220><c> thing</c><00:02:11.489><c> about</c>

00:02:11.510 --> 00:02:11.520 align:start position:0%
waiter here's the beautiful thing about
 

00:02:11.520 --> 00:02:11.980 align:start position:0%
waiter here's the beautiful thing about
it

00:02:11.980 --> 00:02:11.990 align:start position:0%
it
 

00:02:11.990 --> 00:02:16.450 align:start position:0%
it
bipu<00:02:12.290><c> blank-blank</c><00:02:12.950><c> this</c><00:02:13.430><c> is</c><00:02:15.220><c> not</c><00:02:16.220><c> a</c><00:02:16.250><c> base</c>

00:02:16.450 --> 00:02:16.460 align:start position:0%
bipu blank-blank this is not a base
 

00:02:16.460 --> 00:02:21.180 align:start position:0%
bipu blank-blank this is not a base
syntax<00:02:16.970><c> for</c><00:02:17.180><c> optional</c><00:02:17.630><c> value</c><00:02:18.080><c> where</c><00:02:19.030><c> ID</c>

00:02:21.180 --> 00:02:21.190 align:start position:0%
syntax for optional value where ID
 

00:02:21.190 --> 00:02:39.300 align:start position:0%
syntax for optional value where ID
equals<00:02:27.730><c> equals</c><00:02:28.730><c> order</c><00:02:29.260><c> underscore</c><00:02:30.260><c> ID</c><00:02:37.420><c> and</c>

00:02:39.300 --> 00:02:39.310 align:start position:0%
equals equals order underscore ID and
 

00:02:39.310 --> 00:02:41.640 align:start position:0%
equals equals order underscore ID and
let's<00:02:40.310><c> see</c><00:02:40.520><c> here</c>

00:02:41.640 --> 00:02:41.650 align:start position:0%
let's see here
 

00:02:41.650 --> 00:02:48.820 align:start position:0%
let's see here
product<00:02:42.650><c> ID</c><00:02:45.910><c> product</c><00:02:46.910><c> ID</c><00:02:47.500><c> created</c><00:02:48.500><c> Adam</c>

00:02:48.820 --> 00:02:48.830 align:start position:0%
product ID product ID created Adam
 

00:02:48.830 --> 00:02:53.950 align:start position:0%
product ID product ID created Adam
quantity<00:02:49.490><c> and</c><00:02:49.790><c> all</c><00:02:49.940><c> this</c><00:02:50.120><c> other</c><00:02:50.300><c> stuff</c><00:02:52.960><c> that</c>

00:02:53.950 --> 00:02:53.960 align:start position:0%
quantity and all this other stuff that
 

00:02:53.960 --> 00:02:59.860 align:start position:0%
quantity and all this other stuff that
works<00:02:55.720><c> actually</c><00:02:56.720><c> I</c><00:02:57.880><c> can</c><00:02:58.880><c> even</c><00:02:59.240><c> see</c><00:02:59.480><c> the</c><00:02:59.630><c> data</c>

00:02:59.860 --> 00:02:59.870 align:start position:0%
works actually I can even see the data
 

00:02:59.870 --> 00:03:03.150 align:start position:0%
works actually I can even see the data
itself

00:03:03.150 --> 00:03:03.160 align:start position:0%
 
 

00:03:03.160 --> 00:03:07.360 align:start position:0%
 
so<00:03:04.160><c> anyways</c><00:03:05.320><c> where</c><00:03:06.320><c> the</c><00:03:06.440><c> order</c><00:03:06.710><c> ID</c><00:03:06.890><c> is</c><00:03:07.100><c> blank</c>

00:03:07.360 --> 00:03:07.370 align:start position:0%
so anyways where the order ID is blank
 

00:03:07.370 --> 00:03:08.740 align:start position:0%
so anyways where the order ID is blank
let's<00:03:07.550><c> just</c><00:03:07.700><c> stick</c><00:03:07.850><c> with</c><00:03:07.880><c> something</c><00:03:08.180><c> simple</c>

00:03:08.740 --> 00:03:08.750 align:start position:0%
let's just stick with something simple
 

00:03:08.750 --> 00:03:11.830 align:start position:0%
let's just stick with something simple
right<00:03:08.930><c> now</c><00:03:09.460><c> right</c><00:03:10.460><c> now</c><00:03:10.700><c> we</c><00:03:11.270><c> have</c><00:03:11.450><c> a</c><00:03:11.480><c> very</c>

00:03:11.830 --> 00:03:11.840 align:start position:0%
right now right now we have a very
 

00:03:11.840 --> 00:03:13.660 align:start position:0%
right now right now we have a very
simple<00:03:12.230><c> order</c><00:03:12.560><c> ID</c><00:03:12.680><c> and</c><00:03:12.920><c> I'm</c><00:03:12.980><c> gonna</c><00:03:13.130><c> press</c><00:03:13.370><c> save</c>

00:03:13.660 --> 00:03:13.670 align:start position:0%
simple order ID and I'm gonna press save
 

00:03:13.670 --> 00:03:20.680 align:start position:0%
simple order ID and I'm gonna press save
here<00:03:14.150><c> and</c><00:03:14.320><c> I</c><00:03:15.320><c> want</c><00:03:15.620><c> to</c><00:03:15.740><c> save</c><00:03:16.010><c> this</c><00:03:16.310><c> oh</c><00:03:19.000><c> crap</c><00:03:20.000><c> we</c>

00:03:20.680 --> 00:03:20.690 align:start position:0%
here and I want to save this oh crap we
 

00:03:20.690 --> 00:03:23.620 align:start position:0%
here and I want to save this oh crap we
need<00:03:20.810><c> to</c><00:03:21.020><c> turn</c><00:03:21.440><c> the</c><00:03:21.650><c> variable</c><00:03:22.480><c> you'll</c><00:03:23.480><c> see</c>

00:03:23.620 --> 00:03:23.630 align:start position:0%
need to turn the variable you'll see
 

00:03:23.630 --> 00:03:26.140 align:start position:0%
need to turn the variable you'll see
every<00:03:23.870><c> time</c><00:03:24.050><c> we</c><00:03:24.200><c> add</c><00:03:24.380><c> a</c><00:03:24.410><c> variable</c><00:03:24.940><c> we</c><00:03:25.940><c> have</c><00:03:25.970><c> an</c>

00:03:26.140 --> 00:03:26.150 align:start position:0%
every time we add a variable we have an
 

00:03:26.150 --> 00:03:28.870 align:start position:0%
every time we add a variable we have an
order<00:03:26.450><c> ID</c><00:03:26.600><c> and</c><00:03:26.900><c> that</c><00:03:27.050><c> is</c><00:03:27.320><c> a</c><00:03:27.530><c> number</c><00:03:27.800><c> you</c><00:03:28.190><c> need</c>

00:03:28.870 --> 00:03:28.880 align:start position:0%
order ID and that is a number you need
 

00:03:28.880 --> 00:03:31.380 align:start position:0%
order ID and that is a number you need
to<00:03:29.030><c> set</c><00:03:29.210><c> the</c><00:03:29.330><c> data</c><00:03:29.540><c> type</c><00:03:29.660><c> every</c><00:03:30.140><c> time</c><00:03:30.380><c> okay</c>

00:03:31.380 --> 00:03:31.390 align:start position:0%
to set the data type every time okay
 

00:03:31.390 --> 00:03:34.450 align:start position:0%
to set the data type every time okay
replace<00:03:32.390><c> the</c><00:03:32.600><c> original</c><00:03:33.080><c> question</c><00:03:33.290><c> and</c><00:03:33.890><c> let's</c>

00:03:34.450 --> 00:03:34.460 align:start position:0%
replace the original question and let's
 

00:03:34.460 --> 00:03:38.710 align:start position:0%
replace the original question and let's
go<00:03:34.610><c> back</c><00:03:34.850><c> now</c><00:03:35.560><c> into</c><00:03:36.560><c> our</c><00:03:36.830><c> dashboard</c><00:03:37.720><c> because</c>

00:03:38.710 --> 00:03:38.720 align:start position:0%
go back now into our dashboard because
 

00:03:38.720 --> 00:03:41.680 align:start position:0%
go back now into our dashboard because
we<00:03:39.020><c> now</c><00:03:39.170><c> have</c><00:03:39.200><c> a</c><00:03:39.410><c> new</c><00:03:39.680><c> filter</c><00:03:40.210><c> that</c><00:03:41.210><c> we</c><00:03:41.360><c> can</c><00:03:41.510><c> use</c>

00:03:41.680 --> 00:03:41.690 align:start position:0%
we now have a new filter that we can use
 

00:03:41.690 --> 00:03:45.010 align:start position:0%
we now have a new filter that we can use
within<00:03:42.050><c> the</c><00:03:42.350><c> orders</c><00:03:43.300><c> within</c><00:03:44.300><c> this</c><00:03:44.600><c> question</c>

00:03:45.010 --> 00:03:45.020 align:start position:0%
within the orders within this question
 

00:03:45.020 --> 00:03:47.380 align:start position:0%
within the orders within this question
here<00:03:45.410><c> so</c><00:03:45.860><c> I'm</c><00:03:46.130><c> gonna</c><00:03:46.310><c> click</c><00:03:46.520><c> edit</c><00:03:46.850><c> dashboard</c>

00:03:47.380 --> 00:03:47.390 align:start position:0%
here so I'm gonna click edit dashboard
 

00:03:47.390 --> 00:03:51.460 align:start position:0%
here so I'm gonna click edit dashboard
and<00:03:48.260><c> what</c><00:03:48.440><c> order</c><00:03:48.710><c> ID</c><00:03:49.100><c> is</c><00:03:49.460><c> gonna</c><00:03:49.760><c> be</c><00:03:49.990><c> this</c><00:03:50.990><c> order</c>

00:03:51.460 --> 00:03:51.470 align:start position:0%
and what order ID is gonna be this order
 

00:03:51.470 --> 00:03:54.910 align:start position:0%
and what order ID is gonna be this order
ID<00:03:51.670><c> okay</c><00:03:52.670><c> and</c><00:03:53.000><c> done</c><00:03:53.470><c> should</c><00:03:54.470><c> have</c><00:03:54.560><c> saved</c><00:03:54.740><c> this</c>

00:03:54.910 --> 00:03:54.920 align:start position:0%
ID okay and done should have saved this
 

00:03:54.920 --> 00:03:59.230 align:start position:0%
ID okay and done should have saved this
order<00:03:55.280><c> ID</c><00:03:55.430><c> and</c><00:03:55.790><c> save</c><00:03:56.150><c> this</c><00:03:56.420><c> and</c><00:03:56.920><c> now</c><00:03:58.030><c> I</c><00:03:59.030><c> am</c>

00:03:59.230 --> 00:03:59.240 align:start position:0%
order ID and save this and now I am
 

00:03:59.240 --> 00:04:01.540 align:start position:0%
order ID and save this and now I am
gonna<00:03:59.480><c> click</c><00:03:59.660><c> I</c><00:03:59.990><c> only</c><00:04:00.410><c> want</c><00:04:00.680><c> order</c><00:04:01.070><c> ID</c><00:04:01.190><c> number</c>

00:04:01.540 --> 00:04:01.550 align:start position:0%
gonna click I only want order ID number
 

00:04:01.550 --> 00:04:02.950 align:start position:0%
gonna click I only want order ID number
three<00:04:01.760><c> have</c><00:04:01.970><c> a</c><00:04:02.000><c> look</c><00:04:02.180><c> at</c><00:04:02.300><c> this</c><00:04:02.390><c> value</c><00:04:02.630><c> yourself</c>

00:04:02.950 --> 00:04:02.960 align:start position:0%
three have a look at this value yourself
 

00:04:02.960 --> 00:04:06.730 align:start position:0%
three have a look at this value yourself
total<00:04:03.560><c> 116</c><00:04:04.220><c> okay</c><00:04:05.180><c> order</c><00:04:05.540><c> ID</c><00:04:05.690><c> is</c><00:04:05.930><c> three</c><00:04:06.320><c> and</c>

00:04:06.730 --> 00:04:06.740 align:start position:0%
total 116 okay order ID is three and
 

00:04:06.740 --> 00:04:09.490 align:start position:0%
total 116 okay order ID is three and
enter<00:04:07.700><c> oh</c><00:04:08.020><c> crap</c><00:04:09.050><c> aroni</c>

00:04:09.490 --> 00:04:09.500 align:start position:0%
enter oh crap aroni
 

00:04:09.500 --> 00:04:13.180 align:start position:0%
enter oh crap aroni
well<00:04:09.740><c> it</c><00:04:09.980><c> worked</c><00:04:10.220><c> anyways</c><00:04:10.580><c> what</c><00:04:11.330><c> RIT</c><00:04:11.540><c> 3c</c><00:04:12.350><c> 116</c>

00:04:13.180 --> 00:04:13.190 align:start position:0%
well it worked anyways what RIT 3c 116
 

00:04:13.190 --> 00:04:15.370 align:start position:0%
well it worked anyways what RIT 3c 116
and<00:04:13.340><c> all</c><00:04:13.459><c> that</c><00:04:13.700><c> let's</c><00:04:14.360><c> do</c><00:04:14.540><c> it</c><00:04:14.630><c> let's</c><00:04:15.080><c> remove</c>

00:04:15.370 --> 00:04:15.380 align:start position:0%
and all that let's do it let's remove
 

00:04:15.380 --> 00:04:17.770 align:start position:0%
and all that let's do it let's remove
the<00:04:15.590><c> filter</c><00:04:15.920><c> it's</c><00:04:16.070><c> gonna</c><00:04:16.330><c> search</c><00:04:17.330><c> the</c><00:04:17.540><c> query</c>

00:04:17.770 --> 00:04:17.780 align:start position:0%
the filter it's gonna search the query
 

00:04:17.780 --> 00:04:19.390 align:start position:0%
the filter it's gonna search the query
as<00:04:17.930><c> if</c><00:04:18.080><c> there's</c><00:04:18.260><c> absolutely</c><00:04:18.709><c> no</c><00:04:18.920><c> order</c><00:04:19.220><c> ID</c>

00:04:19.390 --> 00:04:19.400 align:start position:0%
as if there's absolutely no order ID
 

00:04:19.400 --> 00:04:22.000 align:start position:0%
as if there's absolutely no order ID
filter<00:04:19.790><c> let's</c><00:04:20.660><c> look</c><00:04:20.900><c> only</c><00:04:21.080><c> at</c><00:04:21.290><c> order</c><00:04:21.890><c> ID</c>

00:04:22.000 --> 00:04:22.010 align:start position:0%
filter let's look only at order ID
 

00:04:22.010 --> 00:04:25.560 align:start position:0%
filter let's look only at order ID
number<00:04:22.340><c> nine</c><00:04:22.990><c> and</c>

00:04:25.560 --> 00:04:25.570 align:start position:0%
number nine and
 

00:04:25.570 --> 00:04:28.800 align:start position:0%
number nine and
what<00:04:26.050><c> ID</c><00:04:26.290><c> number</c><00:04:26.680><c> nine</c><00:04:26.950><c> boom</c><00:04:27.370><c> subtitle</c><00:04:28.030><c> 124</c>

00:04:28.800 --> 00:04:28.810 align:start position:0%
what ID number nine boom subtitle 124
 

00:04:28.810 --> 00:04:30.600 align:start position:0%
what ID number nine boom subtitle 124
that's<00:04:29.020><c> all</c><00:04:29.560><c> there</c><00:04:29.710><c> is</c><00:04:29.830><c> to</c><00:04:30.040><c> it</c><00:04:30.160><c> folks</c>

00:04:30.600 --> 00:04:30.610 align:start position:0%
that's all there is to it folks
 

00:04:30.610 --> 00:04:33.270 align:start position:0%
that's all there is to it folks
it's<00:04:30.820><c> really</c><00:04:31.030><c> easy</c><00:04:31.510><c> if</c><00:04:32.320><c> I</c><00:04:32.470><c> go</c><00:04:32.650><c> over</c><00:04:32.680><c> here</c><00:04:33.250><c> I</c>

00:04:33.270 --> 00:04:33.280 align:start position:0%
it's really easy if I go over here I
 

00:04:33.280 --> 00:04:36.300 align:start position:0%
it's really easy if I go over here I
want<00:04:33.550><c> to</c><00:04:33.580><c> go</c><00:04:33.760><c> to</c><00:04:33.820><c> public</c><00:04:34.690><c> sharing</c><00:04:35.310><c> enable</c>

00:04:36.300 --> 00:04:36.310 align:start position:0%
want to go to public sharing enable
 

00:04:36.310 --> 00:04:39.480 align:start position:0%
want to go to public sharing enable
public<00:04:36.460><c> sharing</c><00:04:36.880><c> yes</c><00:04:37.240><c> enable</c><00:04:38.490><c> shared</c>

00:04:39.480 --> 00:04:39.490 align:start position:0%
public sharing yes enable shared
 

00:04:39.490 --> 00:04:41.100 align:start position:0%
public sharing yes enable shared
dashboards<00:04:40.120><c> no</c><00:04:40.300><c> dashboards</c><00:04:40.900><c> I've</c><00:04:40.990><c> been</c>

00:04:41.100 --> 00:04:41.110 align:start position:0%
dashboards no dashboards I've been
 

00:04:41.110 --> 00:04:42.510 align:start position:0%
dashboards no dashboards I've been
publicly<00:04:41.440><c> shared</c><00:04:41.710><c> yet</c><00:04:41.890><c> no</c><00:04:42.040><c> we're</c><00:04:42.190><c> gonna</c><00:04:42.340><c> do</c>

00:04:42.510 --> 00:04:42.520 align:start position:0%
publicly shared yet no we're gonna do
 

00:04:42.520 --> 00:04:45.000 align:start position:0%
publicly shared yet no we're gonna do
that<00:04:42.670><c> now</c><00:04:43.020><c> let's</c><00:04:44.020><c> share</c><00:04:44.290><c> this</c><00:04:44.440><c> dashboard</c><00:04:44.740><c> I'm</c>

00:04:45.000 --> 00:04:45.010 align:start position:0%
that now let's share this dashboard I'm
 

00:04:45.010 --> 00:04:47.340 align:start position:0%
that now let's share this dashboard I'm
gonna<00:04:45.160><c> reload</c><00:04:45.550><c> the</c><00:04:45.700><c> page</c><00:04:46.200><c> enable</c><00:04:47.200><c> public</c>

00:04:47.340 --> 00:04:47.350 align:start position:0%
gonna reload the page enable public
 

00:04:47.350 --> 00:04:56.910 align:start position:0%
gonna reload the page enable public
sharing<00:04:55.110><c> can</c><00:04:56.110><c> you</c><00:04:56.200><c> talk</c><00:04:56.380><c> to</c><00:04:56.530><c> her</c><00:04:56.620><c> in</c><00:04:56.740><c> the</c><00:04:56.830><c> other</c>

00:04:56.910 --> 00:04:56.920 align:start position:0%
sharing can you talk to her in the other
 

00:04:56.920 --> 00:04:59.040 align:start position:0%
sharing can you talk to her in the other
room

