-- SQL Schema for Google GenAI Chat Manager
-- 
-- This schema supports persistent chat sessions with conversation memory,
-- cost tracking, and session management capabilities.

-- Table to store chat session metadata
CREATE TABLE IF NOT EXISTS chat_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    model_name VARCHAR(100) NOT NULL,
    system_instruction TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    total_messages INTEGER NOT NULL DEFAULT 0,
    total_input_tokens INTEGER NOT NULL DEFAULT 0,
    total_output_tokens INTEGER NOT NULL DEFAULT 0,
    total_cost DECIMAL(10, 8) NOT NULL DEFAULT 0.0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Indexes for common queries
    CONSTRAINT chat_sessions_session_id_key UNIQUE (session_id)
);

-- Table to store individual chat messages
CREATE TABLE IF NOT EXISTS chat_messages (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL REFERENCES chat_sessions(session_id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'model')),
    content TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    token_count INTEGER,
    cost DECIMAL(10, 8),
    
    -- Indexes for common queries
    INDEX idx_chat_messages_session_id (session_id),
    INDEX idx_chat_messages_timestamp (timestamp),
    INDEX idx_chat_messages_session_timestamp (session_id, timestamp)
);

-- View for session summaries with computed metrics
CREATE OR REPLACE VIEW chat_session_summaries AS
SELECT 
    cs.session_id,
    cs.model_name,
    cs.system_instruction,
    cs.created_at,
    cs.last_message_at,
    cs.total_messages,
    cs.total_input_tokens,
    cs.total_output_tokens,
    cs.total_cost,
    cs.is_active,
    
    -- Computed metrics from messages
    COUNT(cm.id) as actual_message_count,
    SUM(CASE WHEN cm.role = 'user' THEN 1 ELSE 0 END) as user_message_count,
    SUM(CASE WHEN cm.role = 'model' THEN 1 ELSE 0 END) as model_message_count,
    SUM(cm.token_count) as total_message_tokens,
    SUM(cm.cost) as total_message_cost,
    
    -- Time metrics
    EXTRACT(EPOCH FROM (cs.last_message_at - cs.created_at)) as session_duration_seconds,
    
    -- Average metrics
    CASE 
        WHEN cs.total_messages > 0 THEN cs.total_cost / (cs.total_messages / 2.0)
        ELSE 0 
    END as avg_cost_per_exchange,
    
    CASE 
        WHEN cs.total_messages > 0 THEN (cs.total_input_tokens + cs.total_output_tokens) / (cs.total_messages / 2.0)
        ELSE 0 
    END as avg_tokens_per_exchange

FROM chat_sessions cs
LEFT JOIN chat_messages cm ON cs.session_id = cm.session_id
GROUP BY cs.session_id, cs.model_name, cs.system_instruction, cs.created_at, 
         cs.last_message_at, cs.total_messages, cs.total_input_tokens, 
         cs.total_output_tokens, cs.total_cost, cs.is_active;

-- Function to get session statistics
CREATE OR REPLACE FUNCTION get_chat_session_stats(
    p_session_id VARCHAR(255) DEFAULT NULL,
    p_hours_back INTEGER DEFAULT 24
)
RETURNS TABLE (
    session_id VARCHAR(255),
    model_name VARCHAR(100),
    message_count BIGINT,
    total_tokens INTEGER,
    total_cost DECIMAL(10, 8),
    avg_response_time_seconds DECIMAL(10, 2),
    session_duration_minutes DECIMAL(10, 2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cs.session_id,
        cs.model_name,
        COUNT(cm.id) as message_count,
        (cs.total_input_tokens + cs.total_output_tokens) as total_tokens,
        cs.total_cost,
        
        -- Calculate average time between user message and model response
        AVG(
            EXTRACT(EPOCH FROM (
                LEAD(cm.timestamp) OVER (PARTITION BY cs.session_id ORDER BY cm.timestamp) - cm.timestamp
            ))
        ) as avg_response_time_seconds,
        
        -- Session duration in minutes
        EXTRACT(EPOCH FROM (cs.last_message_at - cs.created_at)) / 60.0 as session_duration_minutes
        
    FROM chat_sessions cs
    LEFT JOIN chat_messages cm ON cs.session_id = cm.session_id
    WHERE 
        (p_session_id IS NULL OR cs.session_id = p_session_id)
        AND cs.created_at >= NOW() - INTERVAL '1 hour' * p_hours_back
    GROUP BY cs.session_id, cs.model_name, cs.total_input_tokens, cs.total_output_tokens, 
             cs.total_cost, cs.created_at, cs.last_message_at
    ORDER BY cs.last_message_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old inactive sessions
CREATE OR REPLACE FUNCTION cleanup_old_chat_sessions(
    p_days_old INTEGER DEFAULT 30
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete messages first (due to foreign key)
    DELETE FROM chat_messages 
    WHERE session_id IN (
        SELECT session_id 
        FROM chat_sessions 
        WHERE is_active = FALSE 
        AND last_message_at < NOW() - INTERVAL '1 day' * p_days_old
    );
    
    -- Delete the sessions
    DELETE FROM chat_sessions 
    WHERE is_active = FALSE 
    AND last_message_at < NOW() - INTERVAL '1 day' * p_days_old;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get conversation context for a session
CREATE OR REPLACE FUNCTION get_conversation_context(
    p_session_id VARCHAR(255),
    p_last_n_messages INTEGER DEFAULT 20
)
RETURNS TABLE (
    message_order INTEGER,
    role VARCHAR(20),
    content TEXT,
    timestamp TIMESTAMP WITH TIME ZONE,
    token_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ROW_NUMBER() OVER (ORDER BY cm.timestamp) as message_order,
        cm.role,
        cm.content,
        cm.timestamp,
        cm.token_count
    FROM chat_messages cm
    WHERE cm.session_id = p_session_id
    ORDER BY cm.timestamp DESC
    LIMIT p_last_n_messages;
END;
$$ LANGUAGE plpgsql;

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_chat_sessions_active ON chat_sessions(is_active, last_message_at);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_model ON chat_sessions(model_name);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_created ON chat_sessions(created_at);

CREATE INDEX IF NOT EXISTS idx_chat_messages_role ON chat_messages(role);
CREATE INDEX IF NOT EXISTS idx_chat_messages_session_role ON chat_messages(session_id, role);

-- Trigger to automatically update last_message_at when messages are added
CREATE OR REPLACE FUNCTION update_session_last_message_time()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE chat_sessions 
    SET last_message_at = NEW.timestamp
    WHERE session_id = NEW.session_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_session_last_message
    AFTER INSERT ON chat_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_session_last_message_time();

-- Comments for documentation
COMMENT ON TABLE chat_sessions IS 'Stores metadata for chat sessions with conversation memory';
COMMENT ON TABLE chat_messages IS 'Stores individual messages within chat sessions';
COMMENT ON VIEW chat_session_summaries IS 'Provides comprehensive session statistics and metrics';
COMMENT ON FUNCTION get_chat_session_stats IS 'Returns detailed statistics for chat sessions';
COMMENT ON FUNCTION cleanup_old_chat_sessions IS 'Removes old inactive sessions and their messages';
COMMENT ON FUNCTION get_conversation_context IS 'Retrieves recent conversation history for a session';

-- Example usage queries:

/*
-- Get all active sessions with their latest message times:
SELECT session_id, model_name, total_messages, total_cost, last_message_at 
FROM chat_sessions 
WHERE is_active = TRUE 
ORDER BY last_message_at DESC;

-- Get conversation history for a specific session:
SELECT role, content, timestamp, token_count 
FROM chat_messages 
WHERE session_id = 'your-session-id' 
ORDER BY timestamp;

-- Get session statistics for the last 24 hours:
SELECT * FROM get_chat_session_stats(NULL, 24);

-- Get comprehensive session summary:
SELECT * FROM chat_session_summaries WHERE session_id = 'your-session-id';

-- Clean up sessions older than 30 days:
SELECT cleanup_old_chat_sessions(30);

-- Get recent conversation context:
SELECT * FROM get_conversation_context('your-session-id', 10);
*/ 