import os
from supabase import create_client, Client
from supabase.client import Client<PERSON><PERSON>s
import json
from datetime import datetime, timedelta
import asyncio
from tenacity import retry, stop_after_attempt, wait_exponential
import tiktoken
from dotenv import load_dotenv
import time
import aiohttp
import logging
from llama31_promptsV2 import prompt_template, validation_prompt_template

load_dotenv()

"""
This script is designed to process and analyze video transcripts stored in a Supabase database. It performs the following main functions:

1. Connects to a Supabase database using environment variables for authentication.
2. Retrieves video transcripts from specified tables in the database.
3. Uses the Ollama API to generate AI-powered analysis of the transcripts, including keywords, typical questions, and key conclusions.
4. Implements parallel processing to handle multiple requests simultaneously, improving efficiency.
5. Includes a validation step to ensure the AI-generated responses meet specific criteria.
6. Handles rate limiting and retries failed requests using exponential backoff.
7. Truncates long transcripts to fit within the AI model's token limit.
8. Updates the database with the processed results and any feedback on the AI's performance.
9. Implements logging for monitoring the script's progress and debugging.

The script is designed to be robust, handling various edge cases and potential errors, and is optimized for processing large amounts of data efficiently.
"""


url: str = os.environ.get("SUPABASE_URL")
key: str = os.environ.get("SERVICE_ROLE_KEY")
supabase: Client = create_client(url, key,
  options=ClientOptions(
    schema="public",  # Use the default public schema
    headers={},  # No additional headers needed for now
    auto_refresh_token=True,  # Automatically refresh the token when it expires
    persist_session=True,  # Persist the logged in session
    storage=None,  # Use the default storage provider
    realtime=None,  # No specific realtime options needed
    postgrest_client_timeout=999999,  # Set PostgreSQL timeout to 1 hour (3600 seconds)
    storage_client_timeout=999999,  # Set storage timeout to 1 hour
    flow_type=None  # Use the default authentication flow
  ))

# Constants
ROWS_PER_TABLE = -1
MAX_PROCESSING_ATTEMPTS = 15
tables_to_process = ["youtube_artificial_intelligence","youtube_renewable_energy","youtube_gme","youtube_sustainability", "youtube_startups","youtube_financial_markets", "youtube_general", "youtube_legal"]  #, "youtube_sustainability", "youtube_startups", "youtube_artificial_intelligence"
chosen_model = "llama3.1"  #llama3.1  or mistral-nemo
validator_model = "llama3.1"  #llama3.1  or mistral-nemo
MAX_PROMPT_TOKENS = 120000  # Adjust based on your model's context window
SAFETY_MARGIN_TOKENS = 1000  # Adjust as needed

# Function to count tokens
def count_tokens(text):
    encoding = tiktoken.get_encoding("cl100k_base")
    return len(encoding.encode(text))

# Modified function to truncate the transcript
def truncate_transcript(transcript, max_tokens, additional_text=""):
    max_tokens_with_margin = max_tokens - SAFETY_MARGIN_TOKENS - count_tokens(additional_text)
    tokens = count_tokens(transcript)
    if tokens <= max_tokens_with_margin:
        return transcript
    else:
        truncated_transcript = transcript[:max_tokens_with_margin * 4]  # Approximate char to token ratio
        while count_tokens(truncated_transcript) > max_tokens_with_margin:
            truncated_transcript = truncated_transcript[:-1]
        return truncated_transcript

# Silence the HTTP request logs
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)

# Replace the synchronous query_ollama function with an asynchronous version
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=3))
async def query_ollama_async(prompt, session, max_tokens=MAX_PROMPT_TOKENS):
    try:
        async with session.post("http://localhost:11434/api/generate", json={
            "model": chosen_model,
            "prompt": prompt,
            "format": "json",
            "stream": False,
            "options": {
                "num_predict": max_tokens,
                "temperature": 0.3,
                "top_k": 10,
                "top_p": 0.9,
                "repeat_penalty": 1.1
            }
        }, timeout=300) as response:  # Increased timeout to 5 minutes
            result = await response.json()
            print(f"Ollama response:\n{result['response']}\n")
            return result['response']
    except asyncio.TimeoutError:
        print("Ollama query timed out after 5 minutes")
        raise
    except Exception as e:
        print(f"An error occurred while querying Ollama: {str(e)}")
        raise

# Modify the validate_ollama_response_async function
@retry(stop=stop_after_attempt(10), wait=wait_exponential(multiplier=1, min=4, max=10))
async def validate_ollama_response_async(llm_response, channel_name, title, published_at, transcript, session):
    validation_prompt = validation_prompt_template.format(
        llm_response=llm_response,
        channel_name=channel_name,
        title=title,
        published_at=published_at,
        transcript=truncate_transcript(transcript, MAX_PROMPT_TOKENS)  # Truncate transcript to avoid exceeding token limits
    )
    try:
        async with session.post("http://localhost:11434/api/generate", json={
            "model": validator_model,
            "prompt": validation_prompt,
            "format": "json",
            "stream": False,
            "options": {
                "num_predict": 1000,
                "temperature": 0.3,
                "top_k": 10,
                "top_p": 0.9,
                "repeat_penalty": 1.1
            }
        }, timeout=10) as response:  # Add a 10 second timeout
            result = await response.json()
            validation_result = json.loads(result['response'])
            print(f"Validation result: {validation_result}")
            return validation_result["Approved"] == "YES", validation_result["Reason"]
    except asyncio.TimeoutError:
        print("Validation query timed out after 5 minutes")
        raise
    except Exception as e:
        print(f"An error occurred while validating Ollama response: {str(e)}")
        raise


# Modify the process_row_async function to pass the transcript
"""
This function processes a single row of video data asynchronously. It performs the following steps:

1. Checks if the video has already been processed, skipping if so.
2. If a transcript is available, it attempts to process the video up to MAX_PROCESSING_ATTEMPTS times.
3. For each attempt:
   a. Truncates the transcript to fit within token limits, including any previous feedback.
   b. Formats the prompt with video metadata and the truncated transcript.
   c. Queries the Ollama API with the formatted prompt.
   d. Validates the LLM response for JSON format and content quality.
   e. If approved, updates the database with the processed result.
   f. If not approved, collects feedback for the next attempt.
4. Handles JSON parsing errors and logging throughout the process.
5. Uses retry logic to handle potential API failures or rate limiting.

The function is designed to be robust, handling various edge cases and potential errors,
and is optimized for processing large amounts of video transcript data efficiently.
"""

async def process_row_async(row, table_name, session):
    if row['processed']:
        print(f"Skipping already processed video: {row['title']}")
        return

    if row['transcript'] != "Transcript not available":
        auditor_feedback = ""
        for attempt in range(MAX_PROCESSING_ATTEMPTS):
            truncated_transcript = truncate_transcript(row['transcript'], MAX_PROMPT_TOKENS, auditor_feedback)
            prompt = prompt_template.format(
                channel_name=row['channel_name'],
                title=row['title'],
                published_at=row['published_at'],
                transcript=truncated_transcript
            ) + auditor_feedback
            
            print(f"\nProcessing video: {row['title']}")
            print(f"Video ID: {row['video_id']}")
            print(f"Attempt: {attempt + 1}")
            print(f"Prompt tokens: {count_tokens(prompt)}")
            
            try:
                llm_response = await query_ollama_async(prompt, session)
            except Exception as e:
                print(f"Ollama query failed for video ID: {row['video_id']}. Error: {str(e)}")
                if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                    current_datetime = datetime.now().isoformat()
                    supabase.table(table_name).update({
                        "llm_response": f"Error: Ollama query failed after {MAX_PROCESSING_ATTEMPTS} attempts",
                        "llm_call_date": current_datetime,
                        "processed": "impossible"  # Changed from "Impossible" to "impossible"
                    }).eq("id", row['id']).execute()
                    print(f"Marked video_id: {row['video_id']} as impossible due to repeated failures")
                    return
                continue  # Skip to the next attempt

            current_datetime = datetime.now().isoformat()
            
            try:
                json.loads(llm_response)
                is_valid_json = True
            except json.JSONDecodeError:
                is_valid_json = False
                print(f"Warning: Response for video {row['video_id']} is not valid JSON")

            if is_valid_json:
                try:
                    is_approved, reason = await validate_ollama_response_async(
                        llm_response, 
                        row['channel_name'], 
                        row['title'], 
                        row['published_at'], 
                        truncated_transcript,
                        session
                    )
                except asyncio.TimeoutError:
                    print(f"Validation timed out for video ID: {row['video_id']}")
                    continue  # Skip to the next attempt
                
                if is_approved:
                    supabase.table(table_name).update({
                        "llm_response": llm_response,
                        "llm_call_date": current_datetime,
                        "processed": "completed"  # Changed from True to "completed"
                    }).eq("id", row['id']).execute()
                    print(f"Successfully processed video_id: {row['video_id']}")
                    return
                else:
                    print(f"Response not approved for video_id: {row['video_id']}. Reason: {reason}")
                    auditor_feedback = f"\n\nPrevious response was not approved. Please address the following feedback and try again: {reason}"
            else:
                print(f"Invalid JSON response for video_id: {row['video_id']}. Retrying...")
        
        supabase.table(table_name).update({
            "llm_response": f"Error: Failed to generate an approved response after {MAX_PROCESSING_ATTEMPTS} attempts",
            "llm_call_date": current_datetime,
            "processed": "impossible"  # Changed from "Impossible" to "impossible"
        }).eq("id", row['id']).execute()
        print(f"Marked video_id: {row['video_id']} as impossible after {MAX_PROCESSING_ATTEMPTS} attempts")


# Add these new functions at an appropriate place in your script:

def get_unprocessed_count(table_name):
    response = supabase.table(table_name).select("id", count="exact").eq("processed", "pending").execute()
    return response.count

def get_total_count(table_name):
    response = supabase.table(table_name).select("id", count="exact").execute()
    return response.count

def calculate_progress_and_eta(start_time, total_unprocessed, current_unprocessed):
    elapsed_time = time.time() - start_time
    processed = total_unprocessed - current_unprocessed
    
    if processed == 0:
        return 0, "N/A"
    
    percentage = (processed / total_unprocessed) * 100
    
    time_per_item = elapsed_time / processed
    remaining_time = current_unprocessed * time_per_item
    
    eta = datetime.now() + timedelta(seconds=remaining_time)
    eta_str = eta.strftime("%Y-%m-%d %H:%M:%S")
    
    return percentage, eta_str

# Modify the process_table_async function:
async def process_table_async(table_name):
    print(f"Processing table: {table_name}")
    page_size = 1000
    offset = 0
    processed_count = 0
    
    total_unprocessed = get_unprocessed_count(table_name)
    total_rows = get_total_count(table_name)
    start_time = time.time()
    
    print(f"Total unprocessed rows in {table_name}: {total_unprocessed}")
    print(f"Total rows in {table_name}: {total_rows}")
    
    async with aiohttp.ClientSession() as session:
        while True:
            print(f"Fetching rows {offset} to {offset + page_size} from {table_name}")
            response = supabase.table(table_name).select("*").eq("processed", False).range(offset, offset + page_size).execute()
            rows = response.data
            
            print(f"Fetched {len(rows)} rows from {table_name}")
            
            if not rows:
                print(f"No more rows to process in {table_name}")
                break
            
            for i in range(0, len(rows), 2):
                tasks = []
                for j in range(2):
                    if i + j < len(rows):
                        tasks.append(process_row_async(rows[i + j], table_name, session))
                
                print(f"Processing rows {i} to {i+len(tasks)} in {table_name}")
                await asyncio.gather(*tasks)
                processed_count += len(tasks)
                
                current_unprocessed = total_unprocessed - processed_count
                percentage, eta = calculate_progress_and_eta(start_time, total_unprocessed, current_unprocessed)
                
                print(f"Processed {processed_count}/{total_unprocessed} unprocessed rows ({percentage:.2f}% complete).")
                print(f"Estimated completion time: {eta}")
                print(f"Total progress: {((total_rows - current_unprocessed) / total_rows) * 100:.2f}% of all rows")
            
            offset += len(rows)
            
            if ROWS_PER_TABLE != -1 and offset >= ROWS_PER_TABLE:
                print(f"Reached ROWS_PER_TABLE limit for {table_name}")
                break
    
    print(f"Finished processing {processed_count} rows in table: {table_name}")

# Modify the main_async function:
async def main_async():
    start_time = time.time()
    total_cleaned_entries = sum(cleanup_error_entries(table) for table in tables_to_process)
    print(f"Total error entries cleaned up: {total_cleaned_entries}")
    
    total_unprocessed = sum(get_unprocessed_count(table) for table in tables_to_process)
    total_rows = sum(get_total_count(table) for table in tables_to_process)
    print(f"Total unprocessed rows across all tables: {total_unprocessed}")
    print(f"Total rows across all tables: {total_rows}")
    
    for table_name in tables_to_process:
        table_start_time = time.time()
        await process_table_async_with_timeout(table_name)
        table_end_time = time.time()
        table_time = table_end_time - table_start_time
        print(f"Time taken for table {table_name}: {table_time:.2f} seconds")
    
    end_time = time.time()
    total_time = end_time - start_time
    print(f"\nTotal time taken: {total_time:.2f} seconds")

def cleanup_error_entries(table_name):
    print(f"Cleaning up error entries in table: {table_name}")
    response = supabase.table(table_name).select("id, llm_response").execute()
    rows = response.data
    cleaned_count = 0
    
    for row in rows:
        if row['llm_response'] and "An error occurred" in row['llm_response']:
            supabase.table(table_name).update({
                "llm_response": None,
                "processed": "pending"  # Changed from False to "pending"
            }).eq("id", row['id']).execute()
            cleaned_count += 1
    
    print(f"Cleaned up {cleaned_count} error entries in table: {table_name}")
    return cleaned_count

async def process_table_async_with_timeout(table_name):
    try:
        await asyncio.wait_for(process_table_async(table_name), timeout=3600)  # 1-hour timeout
    except asyncio.TimeoutError:
        print(f"Processing table {table_name} timed out after 1 hour")

if __name__ == "__main__":
    asyncio.run(main_async())

