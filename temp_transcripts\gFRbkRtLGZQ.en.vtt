WEBVTT
Kind: captions
Language: en

00:00:01.319 --> 00:00:04.430 align:start position:0%
 
hello<00:00:01.560><c> everyone</c><00:00:02.440><c> Ravi</c><00:00:02.840><c> here</c><00:00:03.000><c> from</c><00:00:03.159><c> Lama</c><00:00:03.480><c> index</c>

00:00:04.430 --> 00:00:04.440 align:start position:0%
hello everyone Ravi here from <PERSON> index
 

00:00:04.440 --> 00:00:07.230 align:start position:0%
hello everyone Ravi here from <PERSON> index
um<00:00:04.880><c> welcome</c><00:00:05.200><c> to</c><00:00:05.440><c> another</c><00:00:06.080><c> tutorial</c><00:00:06.600><c> video</c><00:00:06.919><c> in</c>

00:00:07.230 --> 00:00:07.240 align:start position:0%
um welcome to another tutorial video in
 

00:00:07.240 --> 00:00:10.549 align:start position:0%
um welcome to another tutorial video in
uh<00:00:07.439><c> agent</c><00:00:08.120><c> video</c><00:00:08.719><c> series</c><00:00:09.719><c> so</c><00:00:09.920><c> in</c><00:00:10.080><c> this</c><00:00:10.440><c> uh</c>

00:00:10.549 --> 00:00:10.559 align:start position:0%
uh agent video series so in this uh
 

00:00:10.559 --> 00:00:12.509 align:start position:0%
uh agent video series so in this uh
video<00:00:10.840><c> we'll</c><00:00:11.040><c> look</c><00:00:11.200><c> into</c><00:00:11.559><c> controlling</c><00:00:12.040><c> agent</c>

00:00:12.509 --> 00:00:12.519 align:start position:0%
video we'll look into controlling agent
 

00:00:12.519 --> 00:00:15.470 align:start position:0%
video we'll look into controlling agent
reasoning<00:00:12.960><c> loop</c><00:00:13.320><c> with</c><00:00:13.639><c> redirect</c><00:00:14.160><c> in</c><00:00:14.360><c> tools</c><00:00:15.240><c> so</c>

00:00:15.470 --> 00:00:15.480 align:start position:0%
reasoning loop with redirect in tools so
 

00:00:15.480 --> 00:00:17.710 align:start position:0%
reasoning loop with redirect in tools so
far<00:00:15.639><c> we</c><00:00:15.759><c> have</c><00:00:16.000><c> seen</c><00:00:16.400><c> uh</c><00:00:16.520><c> react</c><00:00:16.880><c> agent</c><00:00:17.359><c> and</c>

00:00:17.710 --> 00:00:17.720 align:start position:0%
far we have seen uh react agent and
 

00:00:17.720 --> 00:00:20.590 align:start position:0%
far we have seen uh react agent and
function<00:00:18.039><c> calling</c><00:00:18.600><c> agent</c><00:00:19.600><c> um</c><00:00:20.039><c> with</c><00:00:20.279><c> different</c>

00:00:20.590 --> 00:00:20.600 align:start position:0%
function calling agent um with different
 

00:00:20.600 --> 00:00:22.910 align:start position:0%
function calling agent um with different
tools<00:00:21.439><c> uh</c><00:00:21.600><c> in</c><00:00:21.760><c> these</c><00:00:22.000><c> cases</c><00:00:22.480><c> what</c><00:00:22.640><c> we</c><00:00:22.760><c> have</c>

00:00:22.910 --> 00:00:22.920 align:start position:0%
tools uh in these cases what we have
 

00:00:22.920 --> 00:00:25.269 align:start position:0%
tools uh in these cases what we have
seen<00:00:23.160><c> is</c><00:00:23.439><c> whatever</c><00:00:23.800><c> the</c><00:00:24.320><c> uh</c><00:00:24.400><c> tool</c><00:00:24.680><c> output</c><00:00:25.080><c> is</c>

00:00:25.269 --> 00:00:25.279 align:start position:0%
seen is whatever the uh tool output is
 

00:00:25.279 --> 00:00:27.310 align:start position:0%
seen is whatever the uh tool output is
we'll<00:00:25.840><c> send</c><00:00:26.039><c> it</c><00:00:26.160><c> to</c><00:00:26.320><c> llm</c><00:00:26.720><c> and</c><00:00:26.880><c> refine</c><00:00:27.199><c> the</c>

00:00:27.310 --> 00:00:27.320 align:start position:0%
we'll send it to llm and refine the
 

00:00:27.320 --> 00:00:29.710 align:start position:0%
we'll send it to llm and refine the
response<00:00:27.880><c> right</c><00:00:28.279><c> uh</c><00:00:28.920><c> uh</c><00:00:29.039><c> to</c><00:00:29.199><c> show</c><00:00:29.400><c> it</c><00:00:29.519><c> as</c><00:00:29.640><c> a</c>

00:00:29.710 --> 00:00:29.720 align:start position:0%
response right uh uh to show it as a
 

00:00:29.720 --> 00:00:33.709 align:start position:0%
response right uh uh to show it as a
final<00:00:30.359><c> response</c><00:00:31.359><c> but</c><00:00:31.679><c> in</c><00:00:32.520><c> some</c><00:00:32.800><c> cases</c><00:00:33.399><c> uh</c><00:00:33.520><c> we</c>

00:00:33.709 --> 00:00:33.719 align:start position:0%
final response but in some cases uh we
 

00:00:33.719 --> 00:00:35.270 align:start position:0%
final response but in some cases uh we
actually<00:00:34.000><c> don't</c><00:00:34.280><c> need</c><00:00:34.480><c> to</c><00:00:34.600><c> send</c><00:00:34.879><c> the</c><00:00:35.000><c> tool</c>

00:00:35.270 --> 00:00:35.280 align:start position:0%
actually don't need to send the tool
 

00:00:35.280 --> 00:00:37.830 align:start position:0%
actually don't need to send the tool
output<00:00:35.680><c> to</c><00:00:36.040><c> uh</c><00:00:36.160><c> an</c><00:00:36.320><c> llm</c><00:00:37.200><c> to</c><00:00:37.440><c> refine</c><00:00:37.719><c> the</c>

00:00:37.830 --> 00:00:37.840 align:start position:0%
output to uh an llm to refine the
 

00:00:37.840 --> 00:00:40.549 align:start position:0%
output to uh an llm to refine the
response<00:00:38.239><c> and</c><00:00:38.440><c> get</c><00:00:38.600><c> the</c><00:00:38.760><c> final</c><00:00:39.399><c> um</c><00:00:39.920><c> refined</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
response and get the final um refined
 

00:00:40.559 --> 00:00:44.029 align:start position:0%
response and get the final um refined
response<00:00:41.559><c> so</c><00:00:41.840><c> in</c><00:00:42.039><c> those</c><00:00:42.280><c> cases</c><00:00:43.079><c> how</c><00:00:43.239><c> can</c><00:00:43.800><c> um</c>

00:00:44.029 --> 00:00:44.039 align:start position:0%
response so in those cases how can um
 

00:00:44.039 --> 00:00:47.229 align:start position:0%
response so in those cases how can um
you<00:00:44.840><c> stop</c><00:00:45.200><c> sending</c><00:00:45.600><c> the</c><00:00:45.800><c> tool</c><00:00:46.120><c> output</c><00:00:46.480><c> to</c><00:00:47.079><c> um</c>

00:00:47.229 --> 00:00:47.239 align:start position:0%
you stop sending the tool output to um
 

00:00:47.239 --> 00:00:50.110 align:start position:0%
you stop sending the tool output to um
llm<00:00:47.719><c> and</c><00:00:48.079><c> give</c><00:00:48.280><c> a</c><00:00:48.440><c> final</c><00:00:48.800><c> response</c><00:00:49.680><c> instead</c>

00:00:50.110 --> 00:00:50.120 align:start position:0%
llm and give a final response instead
 

00:00:50.120 --> 00:00:53.229 align:start position:0%
llm and give a final response instead
whatever<00:00:50.440><c> the</c><00:00:50.559><c> tool</c><00:00:50.800><c> output</c><00:00:51.199><c> is</c><00:00:52.079><c> uh</c><00:00:52.280><c> just</c><00:00:53.039><c> uh</c>

00:00:53.229 --> 00:00:53.239 align:start position:0%
whatever the tool output is uh just uh
 

00:00:53.239 --> 00:00:57.310 align:start position:0%
whatever the tool output is uh just uh
give<00:00:53.440><c> it</c><00:00:53.559><c> as</c><00:00:53.680><c> a</c><00:00:53.760><c> final</c><00:00:54.559><c> output</c><00:00:55.559><c> all</c><00:00:55.760><c> right</c><00:00:56.359><c> so</c>

00:00:57.310 --> 00:00:57.320 align:start position:0%
give it as a final output all right so
 

00:00:57.320 --> 00:01:00.590 align:start position:0%
give it as a final output all right so
for<00:00:57.600><c> those</c><00:00:57.840><c> cases</c><00:00:58.600><c> uh</c><00:00:58.920><c> uh</c><00:00:59.039><c> return</c><00:01:00.039><c> direct</c><00:01:00.440><c> will</c>

00:01:00.590 --> 00:01:00.600 align:start position:0%
for those cases uh uh return direct will
 

00:01:00.600 --> 00:01:03.229 align:start position:0%
for those cases uh uh return direct will
be<00:01:00.800><c> useful</c><00:01:01.600><c> so</c><00:01:01.840><c> if</c><00:01:01.960><c> it</c><00:01:02.079><c> is</c><00:01:02.239><c> enabled</c><00:01:03.120><c> uh</c>

00:01:03.229 --> 00:01:03.239 align:start position:0%
be useful so if it is enabled uh
 

00:01:03.239 --> 00:01:06.030 align:start position:0%
be useful so if it is enabled uh
whenever<00:01:03.600><c> the</c><00:01:03.719><c> specific</c><00:01:04.119><c> tool</c><00:01:04.400><c> is</c><00:01:04.600><c> called</c><00:01:05.479><c> um</c>

00:01:06.030 --> 00:01:06.040 align:start position:0%
whenever the specific tool is called um
 

00:01:06.040 --> 00:01:08.070 align:start position:0%
whenever the specific tool is called um
uh<00:01:06.200><c> it</c><00:01:06.320><c> will</c><00:01:06.760><c> not</c><00:01:07.000><c> send</c><00:01:07.360><c> the</c><00:01:07.479><c> output</c><00:01:07.840><c> of</c><00:01:07.960><c> the</c>

00:01:08.070 --> 00:01:08.080 align:start position:0%
uh it will not send the output of the
 

00:01:08.080 --> 00:01:11.510 align:start position:0%
uh it will not send the output of the
tool<00:01:08.320><c> to</c><00:01:08.439><c> the</c><00:01:08.560><c> llm</c><00:01:09.080><c> it</c><00:01:09.200><c> will</c><00:01:09.479><c> just</c><00:01:09.759><c> directly</c><00:01:10.600><c> uh</c>

00:01:11.510 --> 00:01:11.520 align:start position:0%
tool to the llm it will just directly uh
 

00:01:11.520 --> 00:01:14.590 align:start position:0%
tool to the llm it will just directly uh
uh<00:01:11.600><c> send</c><00:01:12.000><c> as</c><00:01:12.119><c> a</c><00:01:12.240><c> final</c><00:01:12.680><c> output</c><00:01:13.680><c> so</c><00:01:13.880><c> we'll</c><00:01:14.159><c> see</c>

00:01:14.590 --> 00:01:14.600 align:start position:0%
uh send as a final output so we'll see
 

00:01:14.600 --> 00:01:16.550 align:start position:0%
uh send as a final output so we'll see
how<00:01:14.799><c> you</c><00:01:14.920><c> can</c><00:01:15.159><c> use</c><00:01:15.360><c> it</c><00:01:15.680><c> we'll</c><00:01:15.960><c> conduct</c><00:01:16.320><c> two</c>

00:01:16.550 --> 00:01:16.560 align:start position:0%
how you can use it we'll conduct two
 

00:01:16.560 --> 00:01:19.429 align:start position:0%
how you can use it we'll conduct two
experiments<00:01:17.439><c> uh</c><00:01:17.640><c> by</c><00:01:18.040><c> uh</c><00:01:18.159><c> for</c><00:01:18.320><c> a</c><00:01:18.680><c> specific</c><00:01:19.040><c> tool</c>

00:01:19.429 --> 00:01:19.439 align:start position:0%
experiments uh by uh for a specific tool
 

00:01:19.439 --> 00:01:22.789 align:start position:0%
experiments uh by uh for a specific tool
we'll<00:01:19.759><c> enable</c><00:01:20.200><c> it</c><00:01:20.560><c> and</c><00:01:21.079><c> disable</c><00:01:21.560><c> it</c><00:01:22.479><c> uh</c><00:01:22.600><c> and</c>

00:01:22.789 --> 00:01:22.799 align:start position:0%
we'll enable it and disable it uh and
 

00:01:22.799 --> 00:01:25.390 align:start position:0%
we'll enable it and disable it uh and
conduct<00:01:23.119><c> two</c><00:01:23.320><c> experiments</c><00:01:23.840><c> and</c><00:01:24.079><c> see</c><00:01:24.479><c> how</c><00:01:25.240><c> the</c>

00:01:25.390 --> 00:01:25.400 align:start position:0%
conduct two experiments and see how the
 

00:01:25.400 --> 00:01:26.870 align:start position:0%
conduct two experiments and see how the
outputs<00:01:25.840><c> are</c>

00:01:26.870 --> 00:01:26.880 align:start position:0%
outputs are
 

00:01:26.880 --> 00:01:30.469 align:start position:0%
outputs are
changing<00:01:27.880><c> so</c><00:01:28.079><c> we'll</c><00:01:28.439><c> demonstrate</c><00:01:29.079><c> that</c><00:01:29.320><c> with</c>

00:01:30.469 --> 00:01:30.479 align:start position:0%
changing so we'll demonstrate that with
 

00:01:30.479 --> 00:01:34.190 align:start position:0%
changing so we'll demonstrate that with
help<00:01:30.799><c> of</c><00:01:31.280><c> uh</c><00:01:31.680><c> a</c><00:01:31.880><c> restaurant</c><00:01:32.399><c> booking</c><00:01:33.200><c> example</c>

00:01:34.190 --> 00:01:34.200 align:start position:0%
help of uh a restaurant booking example
 

00:01:34.200 --> 00:01:37.749 align:start position:0%
help of uh a restaurant booking example
so<00:01:34.399><c> we'll</c><00:01:34.680><c> Define</c><00:01:35.200><c> a</c><00:01:35.520><c> pantic</c><00:01:36.439><c> booking</c><00:01:37.240><c> uh</c>

00:01:37.749 --> 00:01:37.759 align:start position:0%
so we'll Define a pantic booking uh
 

00:01:37.759 --> 00:01:40.910 align:start position:0%
so we'll Define a pantic booking uh
class<00:01:38.759><c> um</c><00:01:39.119><c> so</c><00:01:39.360><c> for</c><00:01:39.680><c> book</c><00:01:40.040><c> making</c><00:01:40.280><c> a</c><00:01:40.439><c> booking</c><00:01:40.799><c> we</c>

00:01:40.910 --> 00:01:40.920 align:start position:0%
class um so for book making a booking we
 

00:01:40.920 --> 00:01:46.389 align:start position:0%
class um so for book making a booking we
need<00:01:41.520><c> name</c><00:01:42.520><c> and</c><00:01:43.040><c> date</c><00:01:43.920><c> time</c><00:01:44.920><c> and</c><00:01:45.520><c> uh</c><00:01:46.040><c> various</c>

00:01:46.389 --> 00:01:46.399 align:start position:0%
need name and date time and uh various
 

00:01:46.399 --> 00:01:49.350 align:start position:0%
need name and date time and uh various
other<00:01:46.880><c> probably</c><00:01:47.240><c> phone</c><00:01:47.520><c> number</c><00:01:48.159><c> email</c><00:01:49.040><c> and</c>

00:01:49.350 --> 00:01:49.360 align:start position:0%
other probably phone number email and
 

00:01:49.360 --> 00:01:51.630 align:start position:0%
other probably phone number email and
various<00:01:49.680><c> other</c><00:01:49.960><c> details</c><00:01:50.880><c> we'll</c><00:01:51.119><c> Define</c><00:01:51.439><c> all</c>

00:01:51.630 --> 00:01:51.640 align:start position:0%
various other details we'll Define all
 

00:01:51.640 --> 00:01:55.550 align:start position:0%
various other details we'll Define all
this<00:01:51.799><c> in</c><00:01:51.960><c> the</c><00:01:52.240><c> pantic</c><00:01:53.119><c> uh</c><00:01:53.799><c> uh</c><00:01:53.960><c> model</c><00:01:54.360><c> class</c><00:01:55.320><c> and</c>

00:01:55.550 --> 00:01:55.560 align:start position:0%
this in the pantic uh uh model class and
 

00:01:55.560 --> 00:01:58.270 align:start position:0%
this in the pantic uh uh model class and
various<00:01:55.920><c> other</c><00:01:56.119><c> functions</c><00:01:56.560><c> to</c><00:01:56.759><c> complete</c><00:01:57.280><c> the</c>

00:01:58.270 --> 00:01:58.280 align:start position:0%
various other functions to complete the
 

00:01:58.280 --> 00:02:01.270 align:start position:0%
various other functions to complete the
booking<00:01:59.079><c> confirmation</c>

00:02:01.270 --> 00:02:01.280 align:start position:0%
booking confirmation
 

00:02:01.280 --> 00:02:03.709 align:start position:0%
booking confirmation
right<00:02:01.920><c> so</c><00:02:02.399><c> for</c><00:02:02.640><c> one</c><00:02:02.799><c> of</c><00:02:02.960><c> these</c><00:02:03.159><c> functions</c>

00:02:03.709 --> 00:02:03.719 align:start position:0%
right so for one of these functions
 

00:02:03.719 --> 00:02:06.910 align:start position:0%
right so for one of these functions
we'll<00:02:04.039><c> enable</c><00:02:04.960><c> uh</c><00:02:05.200><c> and</c><00:02:05.439><c> disable</c><00:02:06.200><c> and</c><00:02:06.399><c> see</c><00:02:06.719><c> how</c>

00:02:06.910 --> 00:02:06.920 align:start position:0%
we'll enable uh and disable and see how
 

00:02:06.920 --> 00:02:09.430 align:start position:0%
we'll enable uh and disable and see how
the<00:02:07.079><c> outputs</c><00:02:07.479><c> are</c><00:02:07.800><c> changing</c><00:02:08.800><c> okay</c><00:02:09.039><c> so</c><00:02:09.239><c> let's</c>

00:02:09.430 --> 00:02:09.440 align:start position:0%
the outputs are changing okay so let's
 

00:02:09.440 --> 00:02:13.190 align:start position:0%
the outputs are changing okay so let's
get<00:02:09.599><c> started</c><00:02:09.959><c> with</c><00:02:10.360><c> it</c><00:02:11.400><c> so</c>

00:02:13.190 --> 00:02:13.200 align:start position:0%
get started with it so
 

00:02:13.200 --> 00:02:17.550 align:start position:0%
get started with it so
we'll<00:02:14.200><c> import</c><00:02:14.640><c> Necessary</c><00:02:15.480><c> Things</c><00:02:16.480><c> um</c><00:02:16.959><c> set</c><00:02:17.239><c> up</c>

00:02:17.550 --> 00:02:17.560 align:start position:0%
we'll import Necessary Things um set up
 

00:02:17.560 --> 00:02:18.390 align:start position:0%
we'll import Necessary Things um set up
the

00:02:18.390 --> 00:02:18.400 align:start position:0%
the
 

00:02:18.400 --> 00:02:21.150 align:start position:0%
the
llm<00:02:19.400><c> and</c><00:02:19.519><c> then</c><00:02:19.720><c> as</c><00:02:19.920><c> said</c><00:02:20.280><c> uh</c><00:02:20.400><c> we'll</c><00:02:20.640><c> manage</c>

00:02:21.150 --> 00:02:21.160 align:start position:0%
llm and then as said uh we'll manage
 

00:02:21.160 --> 00:02:23.869 align:start position:0%
llm and then as said uh we'll manage
restaurant<00:02:21.760><c> bookings</c><00:02:22.560><c> uh</c><00:02:22.720><c> with</c><00:02:22.959><c> the</c><00:02:23.160><c> booking</c>

00:02:23.869 --> 00:02:23.879 align:start position:0%
restaurant bookings uh with the booking
 

00:02:23.879 --> 00:02:26.830 align:start position:0%
restaurant bookings uh with the booking
class<00:02:24.239><c> pantic</c><00:02:24.720><c> model</c><00:02:25.720><c> and</c><00:02:25.879><c> we</c><00:02:26.040><c> have</c><00:02:26.640><c> these</c>

00:02:26.830 --> 00:02:26.840 align:start position:0%
class pantic model and we have these
 

00:02:26.840 --> 00:02:29.309 align:start position:0%
class pantic model and we have these
four<00:02:27.239><c> functions</c><00:02:28.040><c> get</c><00:02:28.239><c> booking</c><00:02:28.560><c> State</c><00:02:29.000><c> update</c>

00:02:29.309 --> 00:02:29.319 align:start position:0%
four functions get booking State update
 

00:02:29.319 --> 00:02:30.550 align:start position:0%
four functions get booking State update
booking<00:02:29.599><c> create</c><00:02:29.879><c> create</c><00:02:30.080><c> booking</c><00:02:30.400><c> and</c>

00:02:30.550 --> 00:02:30.560 align:start position:0%
booking create create booking and
 

00:02:30.560 --> 00:02:32.910 align:start position:0%
booking create create booking and
confirm<00:02:30.959><c> booking</c><00:02:31.959><c> so</c><00:02:32.120><c> for</c><00:02:32.360><c> create</c><00:02:32.640><c> booking</c>

00:02:32.910 --> 00:02:32.920 align:start position:0%
confirm booking so for create booking
 

00:02:32.920 --> 00:02:34.390 align:start position:0%
confirm booking so for create booking
and<00:02:33.080><c> confirm</c><00:02:33.400><c> booking</c><00:02:33.720><c> we</c><00:02:33.840><c> actually</c><00:02:34.160><c> don't</c>

00:02:34.390 --> 00:02:34.400 align:start position:0%
and confirm booking we actually don't
 

00:02:34.400 --> 00:02:38.070 align:start position:0%
and confirm booking we actually don't
need<00:02:34.800><c> the</c><00:02:35.160><c> tool</c><00:02:35.560><c> output</c><00:02:35.959><c> to</c><00:02:36.040><c> be</c><00:02:36.160><c> sent</c><00:02:36.440><c> to</c><00:02:36.959><c> uh</c><00:02:37.120><c> LM</c>

00:02:38.070 --> 00:02:38.080 align:start position:0%
need the tool output to be sent to uh LM
 

00:02:38.080 --> 00:02:40.149 align:start position:0%
need the tool output to be sent to uh LM
because<00:02:38.519><c> for</c><00:02:38.760><c> creating</c><00:02:39.280><c> uh</c><00:02:39.440><c> create</c><00:02:39.720><c> booking</c>

00:02:40.149 --> 00:02:40.159 align:start position:0%
because for creating uh create booking
 

00:02:40.159 --> 00:02:43.710 align:start position:0%
because for creating uh create booking
like<00:02:40.840><c> okay</c><00:02:41.840><c> um</c><00:02:42.239><c> the</c><00:02:42.440><c> booking</c><00:02:42.800><c> has</c><00:02:43.000><c> started</c>

00:02:43.710 --> 00:02:43.720 align:start position:0%
like okay um the booking has started
 

00:02:43.720 --> 00:02:46.750 align:start position:0%
like okay um the booking has started
okay<00:02:44.040><c> that</c><00:02:44.159><c> is</c><00:02:44.360><c> fine</c><00:02:44.640><c> and</c><00:02:44.879><c> confirm</c><00:02:45.319><c> booking</c><00:02:46.200><c> um</c>

00:02:46.750 --> 00:02:46.760 align:start position:0%
okay that is fine and confirm booking um
 

00:02:46.760 --> 00:02:48.750 align:start position:0%
okay that is fine and confirm booking um
booking<00:02:47.080><c> is</c><00:02:47.239><c> confirmed</c><00:02:47.640><c> when</c><00:02:47.840><c> all</c><00:02:48.080><c> the</c><00:02:48.599><c> uh</c>

00:02:48.750 --> 00:02:48.760 align:start position:0%
booking is confirmed when all the uh
 

00:02:48.760 --> 00:02:50.949 align:start position:0%
booking is confirmed when all the uh
necessary<00:02:49.159><c> fields</c><00:02:49.519><c> are</c><00:02:49.680><c> filled</c><00:02:50.280><c> right</c><00:02:50.480><c> so</c><00:02:50.680><c> we</c>

00:02:50.949 --> 00:02:50.959 align:start position:0%
necessary fields are filled right so we
 

00:02:50.959 --> 00:02:53.550 align:start position:0%
necessary fields are filled right so we
don't<00:02:51.239><c> actually</c><00:02:51.519><c> need</c><00:02:51.720><c> to</c><00:02:51.920><c> send</c><00:02:52.120><c> it</c><00:02:52.239><c> to</c><00:02:52.400><c> LM</c><00:02:53.400><c> and</c>

00:02:53.550 --> 00:02:53.560 align:start position:0%
don't actually need to send it to LM and
 

00:02:53.560 --> 00:02:55.750 align:start position:0%
don't actually need to send it to LM and
then<00:02:53.680><c> for</c><00:02:53.920><c> update</c><00:02:54.239><c> booking</c><00:02:54.760><c> yes</c><00:02:55.000><c> we</c><00:02:55.120><c> need</c><00:02:55.319><c> to</c>

00:02:55.750 --> 00:02:55.760 align:start position:0%
then for update booking yes we need to
 

00:02:55.760 --> 00:02:58.030 align:start position:0%
then for update booking yes we need to
uh<00:02:55.840><c> send</c><00:02:56.080><c> it</c><00:02:56.159><c> to</c><00:02:56.319><c> llm</c><00:02:56.760><c> because</c><00:02:57.280><c> that</c><00:02:57.400><c> will</c><00:02:57.879><c> uh</c>

00:02:58.030 --> 00:02:58.040 align:start position:0%
uh send it to llm because that will uh
 

00:02:58.040 --> 00:03:00.350 align:start position:0%
uh send it to llm because that will uh
make<00:02:58.239><c> the</c><00:02:58.360><c> llm</c><00:02:58.800><c> ask</c><00:02:59.040><c> the</c><00:02:59.200><c> user</c>

00:03:00.350 --> 00:03:00.360 align:start position:0%
make the llm ask the user
 

00:03:00.360 --> 00:03:02.710 align:start position:0%
make the llm ask the user
uh<00:03:00.599><c> the</c><00:03:00.720><c> followup</c><00:03:01.159><c> questions</c><00:03:01.599><c> of</c><00:03:02.080><c> uh</c><00:03:02.400><c> certain</c>

00:03:02.710 --> 00:03:02.720 align:start position:0%
uh the followup questions of uh certain
 

00:03:02.720 --> 00:03:05.229 align:start position:0%
uh the followup questions of uh certain
fields<00:03:03.120><c> are</c><00:03:03.599><c> necessary</c><00:03:04.040><c> to</c><00:03:04.640><c> make</c><00:03:04.799><c> a</c><00:03:04.959><c> final</c>

00:03:05.229 --> 00:03:05.239 align:start position:0%
fields are necessary to make a final
 

00:03:05.239 --> 00:03:07.509 align:start position:0%
fields are necessary to make a final
booking<00:03:05.599><c> confirmation</c><00:03:06.319><c> right</c><00:03:06.519><c> so</c><00:03:06.720><c> in</c><00:03:06.879><c> a</c>

00:03:07.509 --> 00:03:07.519 align:start position:0%
booking confirmation right so in a
 

00:03:07.519 --> 00:03:09.630 align:start position:0%
booking confirmation right so in a
national<00:03:07.920><c> language</c><00:03:08.319><c> way</c><00:03:08.599><c> like</c><00:03:09.080><c> asking</c><00:03:09.400><c> it</c><00:03:09.480><c> to</c>

00:03:09.630 --> 00:03:09.640 align:start position:0%
national language way like asking it to
 

00:03:09.640 --> 00:03:12.789 align:start position:0%
national language way like asking it to
the<00:03:09.760><c> user</c><00:03:10.080><c> will</c><00:03:10.239><c> be</c><00:03:10.400><c> a</c><00:03:10.720><c> good</c><00:03:11.040><c> way</c><00:03:11.920><c> and</c><00:03:12.040><c> then</c><00:03:12.200><c> for</c>

00:03:12.789 --> 00:03:12.799 align:start position:0%
the user will be a good way and then for
 

00:03:12.799 --> 00:03:16.390 align:start position:0%
the user will be a good way and then for
uh<00:03:13.319><c> get</c><00:03:13.560><c> booking</c><00:03:14.239><c> we</c><00:03:14.440><c> actually</c><00:03:14.760><c> don't</c><00:03:15.000><c> need</c><00:03:15.519><c> uh</c>

00:03:16.390 --> 00:03:16.400 align:start position:0%
uh get booking we actually don't need uh
 

00:03:16.400 --> 00:03:20.589 align:start position:0%
uh get booking we actually don't need uh
the<00:03:17.400><c> details</c><00:03:17.840><c> sent</c><00:03:18.480><c> to</c><00:03:18.680><c> be</c><00:03:18.920><c> sent</c><00:03:19.200><c> to</c><00:03:19.360><c> llm</c><00:03:19.959><c> right</c>

00:03:20.589 --> 00:03:20.599 align:start position:0%
the details sent to be sent to llm right
 

00:03:20.599 --> 00:03:23.149 align:start position:0%
the details sent to be sent to llm right
uh<00:03:20.720><c> we</c><00:03:20.840><c> can</c><00:03:21.080><c> directly</c><00:03:21.879><c> give</c><00:03:22.120><c> it</c><00:03:22.640><c> as</c><00:03:22.720><c> a</c><00:03:22.840><c> final</c>

00:03:23.149 --> 00:03:23.159 align:start position:0%
uh we can directly give it as a final
 

00:03:23.159 --> 00:03:25.309 align:start position:0%
uh we can directly give it as a final
output<00:03:23.959><c> whatever</c><00:03:24.319><c> the</c><00:03:24.599><c> details</c><00:03:25.000><c> of</c><00:03:25.159><c> the</c>

00:03:25.309 --> 00:03:25.319 align:start position:0%
output whatever the details of the
 

00:03:25.319 --> 00:03:27.350 align:start position:0%
output whatever the details of the
particular<00:03:25.680><c> booking</c><00:03:26.000><c> are</c><00:03:26.280><c> there</c><00:03:26.920><c> so</c><00:03:27.080><c> we'll</c>

00:03:27.350 --> 00:03:27.360 align:start position:0%
particular booking are there so we'll
 

00:03:27.360 --> 00:03:30.030 align:start position:0%
particular booking are there so we'll
disable<00:03:27.840><c> and</c><00:03:28.080><c> enable</c><00:03:28.760><c> uh</c><00:03:28.879><c> this</c><00:03:29.080><c> particular</c><00:03:29.920><c> uh</c>

00:03:30.030 --> 00:03:30.040 align:start position:0%
disable and enable uh this particular uh
 

00:03:30.040 --> 00:03:32.550 align:start position:0%
disable and enable uh this particular uh
booking<00:03:30.439><c> State</c><00:03:31.000><c> function</c><00:03:31.560><c> so</c><00:03:31.760><c> that</c><00:03:31.920><c> you'll</c>

00:03:32.550 --> 00:03:32.560 align:start position:0%
booking State function so that you'll
 

00:03:32.560 --> 00:03:36.429 align:start position:0%
booking State function so that you'll
understand<00:03:33.280><c> how</c><00:03:34.000><c> uh</c><00:03:34.439><c> the</c><00:03:34.599><c> output</c><00:03:35.080><c> changes</c><00:03:36.080><c> uh</c>

00:03:36.429 --> 00:03:36.439 align:start position:0%
understand how uh the output changes uh
 

00:03:36.439 --> 00:03:40.070 align:start position:0%
understand how uh the output changes uh
with<00:03:37.040><c> redirect</c><00:03:38.040><c> return</c><00:03:38.280><c> direct</c><00:03:38.599><c> enabled</c><00:03:39.319><c> and</c>

00:03:40.070 --> 00:03:40.080 align:start position:0%
with redirect return direct enabled and
 

00:03:40.080 --> 00:03:42.789 align:start position:0%
with redirect return direct enabled and
disabled<00:03:41.080><c> so</c><00:03:41.560><c> we'll</c><00:03:41.840><c> Define</c><00:03:42.319><c> all</c><00:03:42.560><c> these</c>

00:03:42.789 --> 00:03:42.799 align:start position:0%
disabled so we'll Define all these
 

00:03:42.799 --> 00:03:45.949 align:start position:0%
disabled so we'll Define all these
functions<00:03:43.319><c> here</c><00:03:44.120><c> uh</c><00:03:44.280><c> with</c><00:03:44.439><c> booking</c><00:03:44.840><c> class</c><00:03:45.840><c> uh</c>

00:03:45.949 --> 00:03:45.959 align:start position:0%
functions here uh with booking class uh
 

00:03:45.959 --> 00:03:47.710 align:start position:0%
functions here uh with booking class uh
get<00:03:46.159><c> booking</c><00:03:46.439><c> State</c><00:03:46.760><c> update</c><00:03:47.080><c> booking</c><00:03:47.400><c> create</c>

00:03:47.710 --> 00:03:47.720 align:start position:0%
get booking State update booking create
 

00:03:47.720 --> 00:03:50.229 align:start position:0%
get booking State update booking create
booking<00:03:48.159><c> and</c><00:03:48.360><c> confirm</c><00:03:48.760><c> booking</c><00:03:49.519><c> which</c><00:03:49.640><c> are</c><00:03:50.080><c> uh</c>

00:03:50.229 --> 00:03:50.239 align:start position:0%
booking and confirm booking which are uh
 

00:03:50.239 --> 00:03:54.710 align:start position:0%
booking and confirm booking which are uh
pretty<00:03:50.560><c> much</c><00:03:51.000><c> self-explanatory</c>

00:03:54.710 --> 00:03:54.720 align:start position:0%
 
 

00:03:54.720 --> 00:03:57.910 align:start position:0%
 
here<00:03:55.720><c> and</c><00:03:56.040><c> then</c><00:03:56.599><c> so</c><00:03:56.879><c> as</c><00:03:57.040><c> said</c><00:03:57.280><c> we'll</c><00:03:57.560><c> conduct</c>

00:03:57.910 --> 00:03:57.920 align:start position:0%
here and then so as said we'll conduct
 

00:03:57.920 --> 00:04:01.630 align:start position:0%
here and then so as said we'll conduct
two<00:03:58.120><c> experiments</c><00:03:59.120><c> uh</c><00:04:00.159><c> uh</c><00:04:00.599><c> return</c><00:04:00.879><c> direct</c><00:04:01.400><c> not</c>

00:04:01.630 --> 00:04:01.640 align:start position:0%
two experiments uh uh return direct not
 

00:04:01.640 --> 00:04:06.509 align:start position:0%
two experiments uh uh return direct not
enabled<00:04:02.280><c> and</c><00:04:02.640><c> return</c><00:04:02.840><c> direct</c><00:04:03.200><c> enabled</c><00:04:04.200><c> okay</c>

00:04:06.509 --> 00:04:06.519 align:start position:0%
enabled and return direct enabled okay
 

00:04:06.519 --> 00:04:12.589 align:start position:0%
enabled and return direct enabled okay
so<00:04:07.519><c> we</c><00:04:08.079><c> just</c><00:04:08.319><c> run</c><00:04:08.840><c> it</c><00:04:09.840><c> and</c><00:04:10.079><c> then</c><00:04:10.640><c> user</c><00:04:11.000><c> ID</c><00:04:11.599><c> and</c>

00:04:12.589 --> 00:04:12.599 align:start position:0%
so we just run it and then user ID and
 

00:04:12.599 --> 00:04:15.189 align:start position:0%
so we just run it and then user ID and
and<00:04:12.879><c> prefix</c><00:04:13.319><c> message</c><00:04:14.280><c> uh</c><00:04:14.439><c> to</c><00:04:14.599><c> start</c><00:04:14.920><c> the</c>

00:04:15.189 --> 00:04:15.199 align:start position:0%
and prefix message uh to start the
 

00:04:15.199 --> 00:04:18.749 align:start position:0%
and prefix message uh to start the
conversation<00:04:16.199><c> and</c><00:04:16.359><c> we'll</c><00:04:16.639><c> create</c><00:04:17.560><c> uh</c>

00:04:18.749 --> 00:04:18.759 align:start position:0%
conversation and we'll create uh
 

00:04:18.759 --> 00:04:22.950 align:start position:0%
conversation and we'll create uh
the<00:04:19.759><c> agent</c><00:04:20.239><c> here</c><00:04:20.840><c> so</c><00:04:21.280><c> you</c><00:04:21.519><c> have</c><00:04:21.880><c> llm</c><00:04:22.560><c> you</c><00:04:22.720><c> have</c>

00:04:22.950 --> 00:04:22.960 align:start position:0%
the agent here so you have llm you have
 

00:04:22.960 --> 00:04:26.870 align:start position:0%
the agent here so you have llm you have
tools<00:04:23.440><c> you</c><00:04:23.560><c> have</c><00:04:23.800><c> prefix</c><00:04:24.479><c> messages</c><00:04:25.479><c> of</c><00:04:26.160><c> what</c>

00:04:26.870 --> 00:04:26.880 align:start position:0%
tools you have prefix messages of what
 

00:04:26.880 --> 00:04:31.950 align:start position:0%
tools you have prefix messages of what
uh<00:04:27.199><c> the</c><00:04:27.759><c> agent</c><00:04:28.160><c> does</c><00:04:28.840><c> for</c><00:04:29.000><c> a</c><00:04:29.120><c> particular</c><00:04:29.479><c> user</c>

00:04:31.950 --> 00:04:31.960 align:start position:0%
 
 

00:04:31.960 --> 00:04:35.909 align:start position:0%
 
user<00:04:32.960><c> and</c><00:04:33.240><c> then</c><00:04:34.000><c> uh</c><00:04:35.000><c> and</c><00:04:35.120><c> let's</c><00:04:35.520><c> uh</c><00:04:35.639><c> create</c>

00:04:35.909 --> 00:04:35.919 align:start position:0%
user and then uh and let's uh create
 

00:04:35.919 --> 00:04:39.469 align:start position:0%
user and then uh and let's uh create
start<00:04:36.240><c> booking</c><00:04:36.800><c> so</c><00:04:37.240><c> I</c><00:04:37.440><c> said</c><00:04:38.440><c> I</c><00:04:38.639><c> look</c><00:04:39.120><c> I'd</c><00:04:39.320><c> like</c>

00:04:39.469 --> 00:04:39.479 align:start position:0%
start booking so I said I look I'd like
 

00:04:39.479 --> 00:04:44.510 align:start position:0%
start booking so I said I look I'd like
to<00:04:39.720><c> make</c><00:04:39.919><c> a</c><00:04:40.120><c> booking</c><00:04:40.639><c> here</c><00:04:42.039><c> okay</c><00:04:43.039><c> and</c><00:04:43.320><c> then</c>

00:04:44.510 --> 00:04:44.520 align:start position:0%
to make a booking here okay and then
 

00:04:44.520 --> 00:04:47.950 align:start position:0%
to make a booking here okay and then
uh<00:04:45.520><c> then</c><00:04:45.720><c> it</c><00:04:45.880><c> started</c><00:04:46.520><c> asking</c><00:04:47.199><c> okay</c><00:04:47.639><c> some</c>

00:04:47.950 --> 00:04:47.960 align:start position:0%
uh then it started asking okay some
 

00:04:47.960 --> 00:04:50.629 align:start position:0%
uh then it started asking okay some
specific<00:04:48.400><c> ID</c><00:04:49.000><c> uh</c><00:04:49.160><c> create</c><00:04:49.639><c> booking</c><00:04:50.080><c> created</c>

00:04:50.629 --> 00:04:50.639 align:start position:0%
specific ID uh create booking created
 

00:04:50.639 --> 00:04:53.430 align:start position:0%
specific ID uh create booking created
and<00:04:50.919><c> then</c><00:04:51.919><c> uh</c><00:04:52.039><c> but</c><00:04:52.199><c> not</c><00:04:52.400><c> it</c><00:04:52.600><c> confirmed</c><00:04:53.080><c> because</c>

00:04:53.430 --> 00:04:53.440 align:start position:0%
and then uh but not it confirmed because
 

00:04:53.440 --> 00:04:56.150 align:start position:0%
and then uh but not it confirmed because
we<00:04:53.560><c> need</c><00:04:53.800><c> all</c><00:04:54.000><c> these</c><00:04:54.240><c> details</c><00:04:54.680><c> so</c><00:04:54.919><c> name</c><00:04:55.919><c> um</c>

00:04:56.150 --> 00:04:56.160 align:start position:0%
we need all these details so name um
 

00:04:56.160 --> 00:04:58.629 align:start position:0%
we need all these details so name um
mail<00:04:56.479><c> and</c><00:04:56.720><c> all</c><00:04:57.120><c> so</c><00:04:57.280><c> I'll</c><00:04:57.479><c> say</c><00:04:57.800><c> my</c><00:04:57.960><c> name</c><00:04:58.160><c> is</c><00:04:58.320><c> Ravi</c>

00:04:58.629 --> 00:04:58.639 align:start position:0%
mail and all so I'll say my name is Ravi
 

00:04:58.639 --> 00:05:01.590 align:start position:0%
mail and all so I'll say my name is Ravi
and<00:04:58.759><c> my</c><00:04:58.919><c> email</c><00:04:59.280><c> is</c>

00:05:01.590 --> 00:05:01.600 align:start position:0%
and my email is
 

00:05:01.600 --> 00:05:04.230 align:start position:0%
and my email is
gmail.com<00:05:02.600><c> and</c><00:05:02.880><c> then</c><00:05:03.360><c> so</c><00:05:03.560><c> it</c><00:05:03.720><c> updated</c><00:05:04.120><c> the</c>

00:05:04.230 --> 00:05:04.240 align:start position:0%
gmail.com and then so it updated the
 

00:05:04.240 --> 00:05:05.550 align:start position:0%
gmail.com and then so it updated the
booking<00:05:04.520><c> with</c><00:05:04.720><c> user</c>

00:05:05.550 --> 00:05:05.560 align:start position:0%
booking with user
 

00:05:05.560 --> 00:05:10.029 align:start position:0%
booking with user
ID<00:05:06.600><c> and</c><00:05:07.600><c> value</c><00:05:08.080><c> name</c><00:05:08.320><c> is</c><00:05:08.520><c> Ravi</c><00:05:08.919><c> email</c><00:05:09.240><c> is</c><00:05:09.479><c> this</c>

00:05:10.029 --> 00:05:10.039 align:start position:0%
ID and value name is Ravi email is this
 

00:05:10.039 --> 00:05:12.749 align:start position:0%
ID and value name is Ravi email is this
but<00:05:10.199><c> still</c><00:05:10.440><c> we</c><00:05:10.600><c> need</c><00:05:10.800><c> phone</c><00:05:11.039><c> number</c><00:05:11.479><c> date</c><00:05:11.840><c> time</c>

00:05:12.749 --> 00:05:12.759 align:start position:0%
but still we need phone number date time
 

00:05:12.759 --> 00:05:16.189 align:start position:0%
but still we need phone number date time
right<00:05:13.080><c> so</c><00:05:13.880><c> I</c><00:05:14.039><c> say</c><00:05:14.320><c> I'll</c><00:05:14.520><c> say</c><00:05:15.160><c> my</c><00:05:15.320><c> phone</c><00:05:15.520><c> number</c>

00:05:16.189 --> 00:05:16.199 align:start position:0%
right so I say I'll say my phone number
 

00:05:16.199 --> 00:05:19.309 align:start position:0%
right so I say I'll say my phone number
is<00:05:17.199><c> X</c><00:05:17.639><c> and</c><00:05:17.840><c> then</c><00:05:18.160><c> preferred</c><00:05:18.600><c> date</c><00:05:18.759><c> and</c><00:05:18.960><c> time</c><00:05:19.160><c> is</c>

00:05:19.309 --> 00:05:19.319 align:start position:0%
is X and then preferred date and time is
 

00:05:19.319 --> 00:05:20.909 align:start position:0%
is X and then preferred date and time is
April<00:05:19.639><c> 20</c><00:05:19.919><c> and</c><00:05:20.080><c> 12</c>

00:05:20.909 --> 00:05:20.919 align:start position:0%
April 20 and 12
 

00:05:20.919 --> 00:05:22.510 align:start position:0%
April 20 and 12
p.m.

00:05:22.510 --> 00:05:22.520 align:start position:0%
p.m.
 

00:05:22.520 --> 00:05:26.350 align:start position:0%
p.m.
so<00:05:23.520><c> so</c><00:05:23.800><c> it</c><00:05:23.919><c> has</c><00:05:24.160><c> passed</c><00:05:24.600><c> and</c><00:05:25.120><c> yeah</c><00:05:25.720><c> so</c><00:05:25.960><c> now</c><00:05:26.240><c> it</c>

00:05:26.350 --> 00:05:26.360 align:start position:0%
so so it has passed and yeah so now it
 

00:05:26.360 --> 00:05:28.670 align:start position:0%
so so it has passed and yeah so now it
says<00:05:26.720><c> booking</c><00:05:27.080><c> ID</c><00:05:27.319><c> is</c><00:05:27.479><c> confirm</c><00:05:28.080><c> function</c><00:05:28.440><c> call</c>

00:05:28.670 --> 00:05:28.680 align:start position:0%
says booking ID is confirm function call
 

00:05:28.680 --> 00:05:30.430 align:start position:0%
says booking ID is confirm function call
output<00:05:29.080><c> so</c><00:05:29.199><c> as</c><00:05:29.280><c> you</c><00:05:29.400><c> can</c><00:05:29.479><c> see</c><00:05:29.720><c> see</c><00:05:29.919><c> this</c><00:05:30.039><c> is</c><00:05:30.280><c> not</c>

00:05:30.430 --> 00:05:30.440 align:start position:0%
output so as you can see see this is not
 

00:05:30.440 --> 00:05:32.270 align:start position:0%
output so as you can see see this is not
an<00:05:30.639><c> llm</c><00:05:31.120><c> response</c><00:05:31.479><c> it's</c><00:05:31.639><c> just</c><00:05:31.800><c> the</c><00:05:31.960><c> function</c>

00:05:32.270 --> 00:05:32.280 align:start position:0%
an llm response it's just the function
 

00:05:32.280 --> 00:05:34.270 align:start position:0%
an llm response it's just the function
called<00:05:32.759><c> a</c><00:05:32.919><c> specific</c><00:05:33.479><c> function</c><00:05:33.800><c> or</c><00:05:33.960><c> tool</c>

00:05:34.270 --> 00:05:34.280 align:start position:0%
called a specific function or tool
 

00:05:34.280 --> 00:05:37.950 align:start position:0%
called a specific function or tool
output<00:05:35.000><c> right</c><00:05:35.800><c> so</c><00:05:36.039><c> now</c><00:05:36.199><c> I'll</c><00:05:36.680><c> ask</c><00:05:37.680><c> provide</c>

00:05:37.950 --> 00:05:37.960 align:start position:0%
output right so now I'll ask provide
 

00:05:37.960 --> 00:05:42.510 align:start position:0%
output right so now I'll ask provide
details<00:05:38.280><c> of</c><00:05:38.800><c> user</c><00:05:39.280><c> specific</c><00:05:40.160><c> user</c><00:05:41.160><c> okay</c><00:05:42.160><c> now</c>

00:05:42.510 --> 00:05:42.520 align:start position:0%
details of user specific user okay now
 

00:05:42.520 --> 00:05:44.430 align:start position:0%
details of user specific user okay now
it<00:05:42.720><c> gave</c><00:05:42.960><c> the</c><00:05:43.080><c> function</c><00:05:43.400><c> output</c><00:05:43.759><c> is</c><00:05:43.960><c> actually</c>

00:05:44.430 --> 00:05:44.440 align:start position:0%
it gave the function output is actually
 

00:05:44.440 --> 00:05:47.469 align:start position:0%
it gave the function output is actually
in<00:05:44.600><c> a</c><00:05:45.400><c> probably</c><00:05:45.800><c> dictionary</c><00:05:46.240><c> format</c><00:05:46.840><c> and</c><00:05:47.080><c> then</c>

00:05:47.469 --> 00:05:47.479 align:start position:0%
in a probably dictionary format and then
 

00:05:47.479 --> 00:05:50.870 align:start position:0%
in a probably dictionary format and then
llm<00:05:48.000><c> passed</c><00:05:48.360><c> it</c><00:05:48.560><c> and</c><00:05:48.840><c> gave</c><00:05:49.120><c> a</c><00:05:49.319><c> final</c><00:05:49.720><c> response</c>

00:05:50.870 --> 00:05:50.880 align:start position:0%
llm passed it and gave a final response
 

00:05:50.880 --> 00:05:54.070 align:start position:0%
llm passed it and gave a final response
here<00:05:51.880><c> name</c><00:05:52.280><c> email</c><00:05:52.680><c> phone</c><00:05:52.960><c> number</c><00:05:53.479><c> date</c><00:05:53.800><c> and</c>

00:05:54.070 --> 00:05:54.080 align:start position:0%
here name email phone number date and
 

00:05:54.080 --> 00:05:57.550 align:start position:0%
here name email phone number date and
time<00:05:54.919><c> right</c><00:05:55.919><c> so</c><00:05:56.240><c> you</c><00:05:56.360><c> can</c><00:05:56.560><c> see</c><00:05:56.840><c> that</c><00:05:57.240><c> uh</c><00:05:57.360><c> it</c><00:05:57.440><c> is</c>

00:05:57.550 --> 00:05:57.560 align:start position:0%
time right so you can see that uh it is
 

00:05:57.560 --> 00:05:59.749 align:start position:0%
time right so you can see that uh it is
sent<00:05:57.759><c> to</c><00:05:57.880><c> LM</c><00:05:58.240><c> for</c><00:05:58.400><c> final</c><00:05:58.720><c> response</c><00:05:59.400><c> which</c>

00:05:59.749 --> 00:05:59.759 align:start position:0%
sent to LM for final response which
 

00:05:59.759 --> 00:06:01.350 align:start position:0%
sent to LM for final response which
actually<00:06:00.039><c> is</c><00:06:00.199><c> not</c><00:06:00.440><c> needed</c><00:06:00.759><c> you</c><00:06:00.840><c> can</c><00:06:01.000><c> directly</c>

00:06:01.350 --> 00:06:01.360 align:start position:0%
actually is not needed you can directly
 

00:06:01.360 --> 00:06:04.029 align:start position:0%
actually is not needed you can directly
send<00:06:01.720><c> this</c><00:06:02.039><c> whatever</c><00:06:03.000><c> uh</c><00:06:03.160><c> dictionary</c><00:06:03.680><c> is</c>

00:06:04.029 --> 00:06:04.039 align:start position:0%
send this whatever uh dictionary is
 

00:06:04.039 --> 00:06:07.110 align:start position:0%
send this whatever uh dictionary is
there<00:06:04.360><c> right</c><00:06:05.199><c> so</c><00:06:05.720><c> we'll</c><00:06:06.000><c> see</c><00:06:06.440><c> how</c><00:06:06.599><c> you</c><00:06:06.720><c> can</c><00:06:06.880><c> do</c>

00:06:07.110 --> 00:06:07.120 align:start position:0%
there right so we'll see how you can do
 

00:06:07.120 --> 00:06:10.110 align:start position:0%
there right so we'll see how you can do
that<00:06:07.759><c> uh</c><00:06:07.880><c> in</c><00:06:08.000><c> the</c><00:06:08.199><c> second</c><00:06:08.759><c> experiment</c><00:06:09.759><c> here</c>

00:06:10.110 --> 00:06:10.120 align:start position:0%
that uh in the second experiment here
 

00:06:10.120 --> 00:06:13.670 align:start position:0%
that uh in the second experiment here
we'll<00:06:10.840><c> uh</c><00:06:11.000><c> make</c><00:06:11.479><c> return</c><00:06:11.680><c> direct</c><00:06:12.000><c> true</c><00:06:12.479><c> so</c><00:06:12.680><c> that</c>

00:06:13.670 --> 00:06:13.680 align:start position:0%
we'll uh make return direct true so that
 

00:06:13.680 --> 00:06:17.150 align:start position:0%
we'll uh make return direct true so that
you<00:06:13.840><c> don't</c><00:06:14.319><c> send</c><00:06:14.919><c> the</c><00:06:15.720><c> final</c>

00:06:17.150 --> 00:06:17.160 align:start position:0%
you don't send the final
 

00:06:17.160 --> 00:06:20.309 align:start position:0%
you don't send the final
uh<00:06:18.160><c> booking</c><00:06:18.560><c> details</c><00:06:19.000><c> of</c><00:06:19.199><c> the</c><00:06:19.319><c> user</c><00:06:19.880><c> when</c><00:06:20.080><c> we</c>

00:06:20.309 --> 00:06:20.319 align:start position:0%
uh booking details of the user when we
 

00:06:20.319 --> 00:06:24.990 align:start position:0%
uh booking details of the user when we
ask<00:06:21.199><c> uh</c><00:06:21.319><c> to</c><00:06:21.599><c> llm</c><00:06:22.280><c> to</c><00:06:22.599><c> get</c><00:06:22.759><c> a</c><00:06:22.919><c> final</c>

00:06:24.990 --> 00:06:25.000 align:start position:0%
ask uh to llm to get a final
 

00:06:25.000 --> 00:06:29.110 align:start position:0%
ask uh to llm to get a final
response<00:06:26.000><c> so</c><00:06:26.319><c> let's</c><00:06:26.560><c> run</c><00:06:27.000><c> it</c><00:06:28.000><c> let's</c><00:06:28.759><c> uh</c><00:06:28.919><c> have</c>

00:06:29.110 --> 00:06:29.120 align:start position:0%
response so let's run it let's uh have
 

00:06:29.120 --> 00:06:32.870 align:start position:0%
response so let's run it let's uh have
prefix<00:06:29.599><c> message</c><00:06:29.960><c> and</c><00:06:30.199><c> user</c><00:06:31.199><c> create</c>

00:06:32.870 --> 00:06:32.880 align:start position:0%
prefix message and user create
 

00:06:32.880 --> 00:06:35.110 align:start position:0%
prefix message and user create
agent<00:06:33.880><c> and</c>

00:06:35.110 --> 00:06:35.120 align:start position:0%
agent and
 

00:06:35.120 --> 00:06:37.790 align:start position:0%
agent and
then<00:06:36.120><c> follow</c><00:06:36.440><c> the</c><00:06:36.599><c> same</c><00:06:36.840><c> steps</c>

00:06:37.790 --> 00:06:37.800 align:start position:0%
then follow the same steps
 

00:06:37.800 --> 00:06:41.469 align:start position:0%
then follow the same steps
again<00:06:38.800><c> um</c><00:06:39.160><c> booking</c><00:06:39.520><c> is</c><00:06:39.720><c> created</c><00:06:40.520><c> and</c><00:06:40.759><c> then</c><00:06:41.240><c> my</c>

00:06:41.469 --> 00:06:41.479 align:start position:0%
again um booking is created and then my
 

00:06:41.479 --> 00:06:45.390 align:start position:0%
again um booking is created and then my
name<00:06:41.720><c> and</c><00:06:42.240><c> email</c><00:06:43.240><c> are</c><00:06:44.080><c> specified</c><00:06:44.840><c> here</c><00:06:45.199><c> and</c>

00:06:45.390 --> 00:06:45.400 align:start position:0%
name and email are specified here and
 

00:06:45.400 --> 00:06:46.230 align:start position:0%
name and email are specified here and
they<00:06:45.479><c> are</c>

00:06:46.230 --> 00:06:46.240 align:start position:0%
they are
 

00:06:46.240 --> 00:06:49.510 align:start position:0%
they are
updated<00:06:47.240><c> and</c><00:06:47.440><c> it</c><00:06:47.639><c> asks</c><00:06:47.960><c> for</c><00:06:48.919><c> what</c><00:06:49.039><c> is</c><00:06:49.160><c> a</c><00:06:49.280><c> phone</c>

00:06:49.510 --> 00:06:49.520 align:start position:0%
updated and it asks for what is a phone
 

00:06:49.520 --> 00:06:54.870 align:start position:0%
updated and it asks for what is a phone
number<00:06:50.000><c> date</c><00:06:50.319><c> and</c><00:06:51.080><c> time</c><00:06:52.319><c> right</c><00:06:53.319><c> and</c><00:06:53.880><c> then</c>

00:06:54.870 --> 00:06:54.880 align:start position:0%
number date and time right and then
 

00:06:54.880 --> 00:06:57.309 align:start position:0%
number date and time right and then
these<00:06:55.120><c> are</c><00:06:56.000><c> updated</c>

00:06:57.309 --> 00:06:57.319 align:start position:0%
these are updated
 

00:06:57.319 --> 00:07:00.510 align:start position:0%
these are updated
here<00:06:58.319><c> and</c><00:06:58.520><c> then</c><00:06:58.800><c> it</c><00:06:58.919><c> says</c><00:06:59.199><c> booking</c><00:06:59.680><c> is</c>

00:07:00.510 --> 00:07:00.520 align:start position:0%
here and then it says booking is
 

00:07:00.520 --> 00:07:05.430 align:start position:0%
here and then it says booking is
confirmed<00:07:01.520><c> so</c><00:07:02.199><c> now</c><00:07:02.400><c> I'll</c><00:07:02.720><c> ask</c><00:07:03.639><c> the</c><00:07:04.440><c> details</c>

00:07:05.430 --> 00:07:05.440 align:start position:0%
confirmed so now I'll ask the details
 

00:07:05.440 --> 00:07:08.670 align:start position:0%
confirmed so now I'll ask the details
now<00:07:05.639><c> you</c><00:07:05.759><c> can</c><00:07:05.919><c> see</c><00:07:06.360><c> it</c><00:07:06.720><c> just</c><00:07:07.440><c> uh</c><00:07:07.680><c> gave</c><00:07:07.960><c> the</c><00:07:08.360><c> tool</c>

00:07:08.670 --> 00:07:08.680 align:start position:0%
now you can see it just uh gave the tool
 

00:07:08.680 --> 00:07:10.990 align:start position:0%
now you can see it just uh gave the tool
output<00:07:09.080><c> it</c><00:07:09.199><c> did</c><00:07:09.400><c> not</c><00:07:10.000><c> uh</c><00:07:10.080><c> send</c><00:07:10.319><c> it</c><00:07:10.400><c> to</c><00:07:10.560><c> LM</c><00:07:10.879><c> to</c>

00:07:10.990 --> 00:07:11.000 align:start position:0%
output it did not uh send it to LM to
 

00:07:11.000 --> 00:07:15.309 align:start position:0%
output it did not uh send it to LM to
refine<00:07:11.240><c> a</c><00:07:11.440><c> response</c><00:07:12.000><c> right</c><00:07:13.160><c> so</c><00:07:14.160><c> so</c><00:07:14.479><c> these</c><00:07:15.120><c> this</c>

00:07:15.309 --> 00:07:15.319 align:start position:0%
refine a response right so so these this
 

00:07:15.319 --> 00:07:19.430 align:start position:0%
refine a response right so so these this
way<00:07:15.800><c> you</c><00:07:15.919><c> can</c><00:07:16.199><c> actually</c><00:07:17.160><c> uh</c><00:07:18.080><c> stop</c><00:07:18.759><c> sending</c><00:07:19.280><c> the</c>

00:07:19.430 --> 00:07:19.440 align:start position:0%
way you can actually uh stop sending the
 

00:07:19.440 --> 00:07:21.350 align:start position:0%
way you can actually uh stop sending the
tool<00:07:19.680><c> output</c><00:07:20.080><c> directly</c><00:07:20.440><c> to</c><00:07:20.560><c> the</c><00:07:20.720><c> llm</c><00:07:21.120><c> for</c><00:07:21.240><c> a</c>

00:07:21.350 --> 00:07:21.360 align:start position:0%
tool output directly to the llm for a
 

00:07:21.360 --> 00:07:25.749 align:start position:0%
tool output directly to the llm for a
refined<00:07:21.759><c> response</c><00:07:22.240><c> rather</c><00:07:23.199><c> just</c><00:07:24.360><c> um</c><00:07:25.360><c> get</c><00:07:25.599><c> the</c>

00:07:25.749 --> 00:07:25.759 align:start position:0%
refined response rather just um get the
 

00:07:25.759 --> 00:07:29.550 align:start position:0%
refined response rather just um get the
output<00:07:26.160><c> from</c><00:07:26.319><c> the</c><00:07:26.479><c> tool</c><00:07:27.160><c> and</c><00:07:27.560><c> get</c><00:07:27.680><c> it</c><00:07:28.199><c> done</c><00:07:29.199><c> so</c>

00:07:29.550 --> 00:07:29.560 align:start position:0%
output from the tool and get it done so
 

00:07:29.560 --> 00:07:32.189 align:start position:0%
output from the tool and get it done so
so<00:07:29.800><c> do</c><00:07:30.039><c> experiment</c><00:07:30.520><c> with</c><00:07:30.639><c> it</c><00:07:31.160><c> with</c><00:07:31.520><c> more</c>

00:07:32.189 --> 00:07:32.199 align:start position:0%
so do experiment with it with more
 

00:07:32.199 --> 00:07:35.869 align:start position:0%
so do experiment with it with more
examples<00:07:33.280><c> um</c><00:07:34.280><c> see</c><00:07:34.479><c> you</c><00:07:34.599><c> in</c><00:07:34.680><c> the</c><00:07:34.840><c> next</c><00:07:35.080><c> video</c>

00:07:35.869 --> 00:07:35.879 align:start position:0%
examples um see you in the next video
 

00:07:35.879 --> 00:07:39.120 align:start position:0%
examples um see you in the next video
thank<00:07:36.120><c> you</c>

