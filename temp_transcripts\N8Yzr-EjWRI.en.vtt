WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.309 align:start position:0%
 
I<00:00:00.030><c> mean</c><00:00:00.240><c> today</c><00:00:00.539><c> we're</c><00:00:00.750><c> going</c><00:00:00.870><c> to</c><00:00:00.960><c> be</c><00:00:01.050><c> talking</c>

00:00:01.309 --> 00:00:01.319 align:start position:0%
I mean today we're going to be talking
 

00:00:01.319 --> 00:00:07.010 align:start position:0%
I mean today we're going to be talking
about<00:00:02.250><c> using</c><00:00:03.179><c> our</c><00:00:03.830><c> AWS</c><00:00:04.830><c> s3</c><00:00:05.240><c> image</c><00:00:06.240><c> storage</c><00:00:06.779><c> and</c>

00:00:07.010 --> 00:00:07.020 align:start position:0%
about using our AWS s3 image storage and
 

00:00:07.020 --> 00:00:08.960 align:start position:0%
about using our AWS s3 image storage and
that's<00:00:07.080><c> gonna</c><00:00:07.259><c> be</c><00:00:07.440><c> really</c><00:00:07.620><c> cool</c><00:00:08.040><c> if</c><00:00:08.610><c> you're</c>

00:00:08.960 --> 00:00:08.970 align:start position:0%
that's gonna be really cool if you're
 

00:00:08.970 --> 00:00:11.629 align:start position:0%
that's gonna be really cool if you're
just<00:00:09.150><c> joining</c><00:00:09.360><c> us</c><00:00:09.599><c> now</c><00:00:09.840><c> you</c><00:00:09.900><c> can</c><00:00:10.200><c> go</c><00:00:10.920><c> ahead</c><00:00:10.950><c> to</c>

00:00:11.629 --> 00:00:11.639 align:start position:0%
just joining us now you can go ahead to
 

00:00:11.639 --> 00:00:13.430 align:start position:0%
just joining us now you can go ahead to
the<00:00:11.940><c> link</c><00:00:12.360><c> in</c><00:00:12.450><c> the</c><00:00:12.570><c> description</c><00:00:13.049><c> which</c><00:00:13.259><c> is</c>

00:00:13.430 --> 00:00:13.440 align:start position:0%
the link in the description which is
 

00:00:13.440 --> 00:00:15.799 align:start position:0%
the link in the description which is
this<00:00:13.620><c> repo</c><00:00:14.040><c> here</c><00:00:14.250><c> it's</c><00:00:14.460><c> called</c><00:00:14.809><c> angularjs</c>

00:00:15.799 --> 00:00:15.809 align:start position:0%
this repo here it's called angularjs
 

00:00:15.809 --> 00:00:17.750 align:start position:0%
this repo here it's called angularjs
client-side<00:00:16.379><c> image</c><00:00:16.619><c> compression</c><00:00:17.160><c> and</c><00:00:17.369><c> upload</c>

00:00:17.750 --> 00:00:17.760 align:start position:0%
client-side image compression and upload
 

00:00:17.760 --> 00:00:19.910 align:start position:0%
client-side image compression and upload
to<00:00:17.789><c> AWS</c><00:00:18.510><c> s3</c><00:00:18.539><c> and</c><00:00:19.230><c> this</c><00:00:19.350><c> is</c><00:00:19.470><c> a</c><00:00:19.500><c> really</c><00:00:19.800><c> nice</c>

00:00:19.910 --> 00:00:19.920 align:start position:0%
to AWS s3 and this is a really nice
 

00:00:19.920 --> 00:00:22.040 align:start position:0%
to AWS s3 and this is a really nice
thing<00:00:20.250><c> because</c><00:00:20.430><c> it</c><00:00:21.359><c> actually</c><00:00:21.539><c> gives</c><00:00:21.750><c> you</c><00:00:21.990><c> the</c>

00:00:22.040 --> 00:00:22.050 align:start position:0%
thing because it actually gives you the
 

00:00:22.050 --> 00:00:24.849 align:start position:0%
thing because it actually gives you the
full<00:00:22.320><c> tutorial</c><00:00:22.500><c> on</c><00:00:23.189><c> obtaining</c><00:00:23.400><c> your</c><00:00:23.699><c> s3</c><00:00:24.330><c> keys</c>

00:00:24.849 --> 00:00:24.859 align:start position:0%
full tutorial on obtaining your s3 keys
 

00:00:24.859 --> 00:00:27.560 align:start position:0%
full tutorial on obtaining your s3 keys
that's<00:00:25.859><c> a</c><00:00:25.980><c> YouTube</c><00:00:26.340><c> video</c><00:00:26.670><c> I</c><00:00:26.699><c> found</c><00:00:27.090><c> by</c><00:00:27.510><c> this</c>

00:00:27.560 --> 00:00:27.570 align:start position:0%
that's a YouTube video I found by this
 

00:00:27.570 --> 00:00:30.919 align:start position:0%
that's a YouTube video I found by this
guy<00:00:27.960><c> I</c><00:00:28.289><c> think</c><00:00:28.470><c> his</c><00:00:28.619><c> name</c><00:00:28.769><c> is</c><00:00:28.920><c> Keith</c><00:00:29.869><c> okay</c><00:00:30.869><c> so</c>

00:00:30.919 --> 00:00:30.929 align:start position:0%
guy I think his name is Keith okay so
 

00:00:30.929 --> 00:00:32.990 align:start position:0%
guy I think his name is Keith okay so
he's<00:00:31.260><c> awesome</c><00:00:31.710><c> and</c><00:00:31.949><c> this</c><00:00:32.279><c> is</c><00:00:32.460><c> how</c><00:00:32.579><c> you</c><00:00:32.640><c> build</c>

00:00:32.990 --> 00:00:33.000 align:start position:0%
he's awesome and this is how you build
 

00:00:33.000 --> 00:00:37.400 align:start position:0%
he's awesome and this is how you build
your<00:00:33.260><c> your</c><00:00:34.260><c> actual</c><00:00:35.690><c> your</c><00:00:36.690><c> file</c><00:00:36.989><c> and</c><00:00:37.290><c> I</c>

00:00:37.400 --> 00:00:37.410 align:start position:0%
your your actual your file and I
 

00:00:37.410 --> 00:00:39.020 align:start position:0%
your your actual your file and I
actually<00:00:37.649><c> put</c><00:00:38.010><c> down</c><00:00:38.070><c> everything</c><00:00:38.579><c> here</c><00:00:38.879><c> the</c>

00:00:39.020 --> 00:00:39.030 align:start position:0%
actually put down everything here the
 

00:00:39.030 --> 00:00:41.990 align:start position:0%
actually put down everything here the
principal<00:00:39.600><c> yeah</c><00:00:40.440><c> the</c><00:00:40.739><c> post</c><00:00:40.950><c> East</c><00:00:41.309><c> asset</c><00:00:41.820><c> this</c>

00:00:41.990 --> 00:00:42.000 align:start position:0%
principal yeah the post East asset this
 

00:00:42.000 --> 00:00:46.160 align:start position:0%
principal yeah the post East asset this
is<00:00:42.149><c> gonna</c><00:00:42.300><c> be</c><00:00:42.480><c> the</c><00:00:42.629><c> name</c><00:00:42.870><c> of</c><00:00:43.260><c> your</c><00:00:44.870><c> the</c><00:00:45.870><c> name</c><00:00:46.050><c> of</c>

00:00:46.160 --> 00:00:46.170 align:start position:0%
is gonna be the name of your the name of
 

00:00:46.170 --> 00:00:48.229 align:start position:0%
is gonna be the name of your the name of
your<00:00:46.289><c> bucket</c><00:00:46.559><c> and</c><00:00:46.920><c> so</c><00:00:47.160><c> on</c><00:00:47.340><c> and</c><00:00:47.640><c> this</c><00:00:48.030><c> is</c><00:00:48.090><c> the</c>

00:00:48.229 --> 00:00:48.239 align:start position:0%
your bucket and so on and this is the
 

00:00:48.239 --> 00:00:49.819 align:start position:0%
your bucket and so on and this is the
exact<00:00:48.629><c> syntax</c><00:00:48.840><c> in</c><00:00:49.260><c> order</c><00:00:49.440><c> for</c><00:00:49.530><c> everything</c><00:00:49.710><c> to</c>

00:00:49.819 --> 00:00:49.829 align:start position:0%
exact syntax in order for everything to
 

00:00:49.829 --> 00:00:52.369 align:start position:0%
exact syntax in order for everything to
be<00:00:49.980><c> public</c><00:00:50.520><c> let's</c><00:00:51.239><c> say</c><00:00:51.360><c> you</c><00:00:51.420><c> actually</c><00:00:51.629><c> go</c><00:00:51.989><c> into</c>

00:00:52.369 --> 00:00:52.379 align:start position:0%
be public let's say you actually go into
 

00:00:52.379 --> 00:00:55.040 align:start position:0%
be public let's say you actually go into
the<00:00:52.500><c> app</c><00:00:52.649><c> you're</c><00:00:53.100><c> gonna</c><00:00:53.280><c> go</c><00:00:53.460><c> here</c><00:00:53.820><c> and</c><00:00:54.629><c> as</c><00:00:54.930><c> you</c>

00:00:55.040 --> 00:00:55.050 align:start position:0%
the app you're gonna go here and as you
 

00:00:55.050 --> 00:00:56.689 align:start position:0%
the app you're gonna go here and as you
can<00:00:55.199><c> see</c><00:00:55.379><c> every</c><00:00:55.800><c> time</c><00:00:55.980><c> you</c><00:00:56.100><c> upload</c><00:00:56.340><c> something</c>

00:00:56.689 --> 00:00:56.699 align:start position:0%
can see every time you upload something
 

00:00:56.699 --> 00:01:00.650 align:start position:0%
can see every time you upload something
it<00:00:57.270><c> automatically</c><00:00:58.129><c> creates</c><00:00:59.129><c> a</c><00:00:59.460><c> link</c><00:00:59.730><c> the</c><00:01:00.449><c> link</c>

00:01:00.650 --> 00:01:00.660 align:start position:0%
it automatically creates a link the link
 

00:01:00.660 --> 00:01:02.959 align:start position:0%
it automatically creates a link the link
is<00:01:00.780><c> created</c><00:01:00.899><c> by</c><00:01:01.230><c> s3</c><00:01:01.739><c> and</c><00:01:01.890><c> we</c><00:01:02.039><c> just</c><00:01:02.280><c> fetch</c><00:01:02.760><c> that</c>

00:01:02.959 --> 00:01:02.969 align:start position:0%
is created by s3 and we just fetch that
 

00:01:02.969 --> 00:01:05.710 align:start position:0%
is created by s3 and we just fetch that
link<00:01:03.300><c> within</c><00:01:03.510><c> the</c><00:01:03.960><c> client</c><00:01:04.949><c> side</c><00:01:05.189><c> of</c><00:01:05.430><c> the</c><00:01:05.549><c> app</c>

00:01:05.710 --> 00:01:05.720 align:start position:0%
link within the client side of the app
 

00:01:05.720 --> 00:01:08.120 align:start position:0%
link within the client side of the app
so<00:01:06.720><c> let's</c><00:01:06.900><c> do</c><00:01:07.049><c> another</c><00:01:07.140><c> example</c><00:01:07.470><c> I'm</c><00:01:07.950><c> gonna</c>

00:01:08.120 --> 00:01:08.130 align:start position:0%
so let's do another example I'm gonna
 

00:01:08.130 --> 00:01:11.840 align:start position:0%
so let's do another example I'm gonna
upload<00:01:08.430><c> let's</c><00:01:09.030><c> say</c><00:01:09.270><c> a</c><00:01:09.299><c> picture</c><00:01:10.640><c> I'm</c><00:01:11.640><c> what</c>

00:01:11.840 --> 00:01:11.850 align:start position:0%
upload let's say a picture I'm what
 

00:01:11.850 --> 00:01:14.320 align:start position:0%
upload let's say a picture I'm what
about<00:01:12.060><c> other</c><00:01:12.240><c> projects</c><00:01:12.869><c> on</c><00:01:13.290><c> our</c><00:01:13.439><c> project</c>

00:01:14.320 --> 00:01:14.330 align:start position:0%
about other projects on our project
 

00:01:14.330 --> 00:01:17.120 align:start position:0%
about other projects on our project
image<00:01:15.500><c> okay</c><00:01:16.500><c> awesome</c>

00:01:17.120 --> 00:01:17.130 align:start position:0%
image okay awesome
 

00:01:17.130 --> 00:01:19.640 align:start position:0%
image okay awesome
well<00:01:17.880><c> this</c><00:01:18.000><c> is</c><00:01:18.150><c> pretty</c><00:01:18.360><c> funny</c><00:01:18.780><c> okay</c><00:01:19.470><c> one</c>

00:01:19.640 --> 00:01:19.650 align:start position:0%
well this is pretty funny okay one
 

00:01:19.650 --> 00:01:23.719 align:start position:0%
well this is pretty funny okay one
Calvin<00:01:20.070><c> and</c><00:01:20.220><c> Hobbes</c><00:01:22.340><c> what</c><00:01:23.340><c> am</c><00:01:23.460><c> I</c><00:01:23.549><c> really</c>

00:01:23.719 --> 00:01:23.729 align:start position:0%
Calvin and Hobbes what am I really
 

00:01:23.729 --> 00:01:25.969 align:start position:0%
Calvin and Hobbes what am I really
favorites<00:01:24.330><c> okay</c><00:01:24.780><c> there</c><00:01:25.350><c> you</c><00:01:25.380><c> go</c><00:01:25.470><c> while</c><00:01:25.799><c> you've</c>

00:01:25.969 --> 00:01:25.979 align:start position:0%
favorites okay there you go while you've
 

00:01:25.979 --> 00:01:28.039 align:start position:0%
favorites okay there you go while you've
achieved<00:01:26.100><c> a</c><00:01:26.430><c> compression</c><00:01:26.759><c> of</c><00:01:26.970><c> 2.57</c>

00:01:28.039 --> 00:01:28.049 align:start position:0%
achieved a compression of 2.57
 

00:01:28.049 --> 00:01:29.660 align:start position:0%
achieved a compression of 2.57
wasn't<00:01:28.860><c> the</c><00:01:28.920><c> time</c><00:01:29.070><c> it's</c><00:01:29.220><c> much</c><00:01:29.430><c> better</c>

00:01:29.660 --> 00:01:29.670 align:start position:0%
wasn't the time it's much better
 

00:01:29.670 --> 00:01:31.850 align:start position:0%
wasn't the time it's much better
actually<00:01:30.509><c> see</c><00:01:30.720><c> the</c><00:01:31.049><c> width</c><00:01:31.290><c> and</c><00:01:31.439><c> height</c><00:01:31.500><c> was</c>

00:01:31.850 --> 00:01:31.860 align:start position:0%
actually see the width and height was
 

00:01:31.860 --> 00:01:34.670 align:start position:0%
actually see the width and height was
really<00:01:32.189><c> short</c><00:01:32.400><c> anyway</c><00:01:32.759><c> so</c><00:01:32.909><c> that's</c><00:01:33.630><c> okay</c><00:01:33.930><c> but</c>

00:01:34.670 --> 00:01:34.680 align:start position:0%
really short anyway so that's okay but
 

00:01:34.680 --> 00:01:37.100 align:start position:0%
really short anyway so that's okay but
anyways<00:01:35.009><c> it</c><00:01:35.430><c> also</c><00:01:35.610><c> goes</c><00:01:35.850><c> into</c><00:01:36.180><c> our</c><00:01:36.210><c> s3</c><00:01:36.689><c> bucket</c>

00:01:37.100 --> 00:01:37.110 align:start position:0%
anyways it also goes into our s3 bucket
 

00:01:37.110 --> 00:01:39.020 align:start position:0%
anyways it also goes into our s3 bucket
and<00:01:37.320><c> let's</c><00:01:37.590><c> check</c><00:01:37.860><c> over</c><00:01:37.920><c> here</c><00:01:38.369><c> the</c><00:01:38.670><c> other</c><00:01:38.820><c> time</c>

00:01:39.020 --> 00:01:39.030 align:start position:0%
and let's check over here the other time
 

00:01:39.030 --> 00:01:50.560 align:start position:0%
and let's check over here the other time
Amazon<00:01:39.600><c> s3</c><00:01:41.540><c> and</c><00:01:42.540><c> let's</c><00:01:43.170><c> see</c><00:01:43.409><c> you</c><00:01:44.840><c> opened</c>

00:01:50.560 --> 00:01:50.570 align:start position:0%
 
 

00:01:50.570 --> 00:01:51.920 align:start position:0%
 
something

00:01:51.920 --> 00:01:51.930 align:start position:0%
something
 

00:01:51.930 --> 00:01:57.639 align:start position:0%
something
let's<00:01:52.350><c> go</c><00:01:52.530><c> back</c><00:01:52.740><c> I</c><00:01:52.950><c> was</c><00:01:53.159><c> on</c><00:01:53.310><c> history</c><00:01:54.259><c> and</c><00:01:55.880><c> I</c>

00:01:57.639 --> 00:01:57.649 align:start position:0%
let's go back I was on history and I
 

00:01:57.649 --> 00:02:00.109 align:start position:0%
let's go back I was on history and I
want<00:01:58.649><c> to</c><00:01:58.710><c> delete</c><00:01:58.979><c> these</c><00:01:59.130><c> elastic</c><00:01:59.670><c> beam</c><00:01:59.820><c> stacks</c>

00:02:00.109 --> 00:02:00.119 align:start position:0%
want to delete these elastic beam stacks
 

00:02:00.119 --> 00:02:03.080 align:start position:0%
want to delete these elastic beam stacks
man<00:02:00.390><c> what</c><00:02:00.719><c> happened</c><00:02:00.930><c> you</c><00:02:01.549><c> what</c><00:02:02.549><c> I</c><00:02:02.790><c> don't</c>

00:02:03.080 --> 00:02:03.090 align:start position:0%
man what happened you what I don't
 

00:02:03.090 --> 00:02:04.520 align:start position:0%
man what happened you what I don't
understand<00:02:03.509><c> maybe</c><00:02:03.689><c> there</c><00:02:03.930><c> was</c><00:02:04.049><c> an</c><00:02:04.229><c> error</c>

00:02:04.520 --> 00:02:04.530 align:start position:0%
understand maybe there was an error
 

00:02:04.530 --> 00:02:06.260 align:start position:0%
understand maybe there was an error
while<00:02:04.890><c> uploading</c><00:02:05.430><c> the</c><00:02:05.520><c> photo</c><00:02:05.759><c> in</c><00:02:05.969><c> which</c><00:02:06.090><c> case</c>

00:02:06.260 --> 00:02:06.270 align:start position:0%
while uploading the photo in which case
 

00:02:06.270 --> 00:02:09.749 align:start position:0%
while uploading the photo in which case
we<00:02:06.420><c> do</c><00:02:06.600><c> need</c><00:02:06.750><c> to</c><00:02:06.780><c> fix</c><00:02:07.110><c> that</c><00:02:08.300><c> this</c><00:02:09.300><c> trying</c>

00:02:09.749 --> 00:02:09.759 align:start position:0%
we do need to fix that this trying
 

00:02:09.759 --> 00:02:14.960 align:start position:0%
we do need to fix that this trying
again<00:02:09.989><c> another</c><00:02:10.989><c> image</c><00:02:11.909><c> so</c><00:02:12.909><c> you</c><00:02:13.000><c> choose</c><00:02:13.269><c> a</c><00:02:13.330><c> file</c>

00:02:14.960 --> 00:02:14.970 align:start position:0%
again another image so you choose a file
 

00:02:14.970 --> 00:02:19.800 align:start position:0%
again another image so you choose a file
that<00:02:15.970><c> is</c><00:02:16.030><c> this</c><00:02:16.299><c> guy</c><00:02:16.590><c> Jack</c><00:02:17.590><c> Black</c><00:02:18.209><c> you</c><00:02:19.209><c> can</c><00:02:19.480><c> have</c>

00:02:19.800 --> 00:02:19.810 align:start position:0%
that is this guy Jack Black you can have
 

00:02:19.810 --> 00:02:21.929 align:start position:0%
that is this guy Jack Black you can have
a<00:02:19.840><c> link</c><00:02:20.200><c> here</c><00:02:20.590><c> so</c><00:02:20.890><c> it</c><00:02:21.040><c> must</c><00:02:21.250><c> be</c><00:02:21.340><c> working</c><00:02:21.849><c> I</c>

00:02:21.929 --> 00:02:21.939 align:start position:0%
a link here so it must be working I
 

00:02:21.939 --> 00:02:26.339 align:start position:0%
a link here so it must be working I
don't<00:02:22.000><c> know</c><00:02:22.209><c> why</c><00:02:24.269><c> open</c><00:02:25.269><c> image</c><00:02:25.720><c> in</c><00:02:25.840><c> a</c><00:02:25.930><c> new</c><00:02:26.110><c> tab</c>

00:02:26.339 --> 00:02:26.349 align:start position:0%
don't know why open image in a new tab
 

00:02:26.349 --> 00:02:28.100 align:start position:0%
don't know why open image in a new tab
okay

00:02:28.100 --> 00:02:28.110 align:start position:0%
okay
 

00:02:28.110 --> 00:02:30.539 align:start position:0%
okay
whatever<00:02:29.110><c> this</c><00:02:29.319><c> is</c><00:02:29.379><c> where</c><00:02:29.709><c> it's</c><00:02:29.920><c> going</c><00:02:30.129><c> to</c><00:02:30.400><c> as</c>

00:02:30.539 --> 00:02:30.549 align:start position:0%
whatever this is where it's going to as
 

00:02:30.549 --> 00:02:33.470 align:start position:0%
whatever this is where it's going to as
you<00:02:30.670><c> can</c><00:02:30.790><c> see</c><00:02:30.940><c> s3</c><00:02:31.360><c> -</c><00:02:31.540><c> EU</c><00:02:31.959><c> central</c><00:02:32.530><c> 1</c><00:02:32.769><c> that's</c><00:02:33.040><c> the</c>

00:02:33.470 --> 00:02:33.480 align:start position:0%
you can see s3 - EU central 1 that's the
 

00:02:33.480 --> 00:02:37.020 align:start position:0%
you can see s3 - EU central 1 that's the
region<00:02:34.480><c> of</c><00:02:34.599><c> the</c><00:02:34.720><c> AWS</c><00:02:35.349><c> s3</c><00:02:35.530><c> server</c><00:02:36.190><c> that's</c><00:02:36.909><c> the</c>

00:02:37.020 --> 00:02:37.030 align:start position:0%
region of the AWS s3 server that's the
 

00:02:37.030 --> 00:02:38.399 align:start position:0%
region of the AWS s3 server that's the
name<00:02:37.180><c> of</c><00:02:37.209><c> the</c><00:02:37.329><c> project</c><00:02:37.540><c> and</c><00:02:37.959><c> that's</c><00:02:38.140><c> the</c><00:02:38.260><c> name</c>

00:02:38.399 --> 00:02:38.409 align:start position:0%
name of the project and that's the name
 

00:02:38.409 --> 00:02:41.399 align:start position:0%
name of the project and that's the name
of<00:02:38.560><c> the</c><00:02:38.769><c> of</c><00:02:39.280><c> the</c><00:02:40.000><c> photo</c><00:02:40.299><c> so</c><00:02:40.720><c> it</c><00:02:40.840><c> is</c><00:02:41.019><c> getting</c>

00:02:41.399 --> 00:02:41.409 align:start position:0%
of the of the photo so it is getting
 

00:02:41.409 --> 00:02:43.130 align:start position:0%
of the of the photo so it is getting
uploaded<00:02:41.769><c> I'm</c><00:02:42.250><c> gonna</c><00:02:42.459><c> check</c><00:02:42.819><c> back</c><00:02:42.849><c> here</c>

00:02:43.130 --> 00:02:43.140 align:start position:0%
uploaded I'm gonna check back here
 

00:02:43.140 --> 00:02:48.809 align:start position:0%
uploaded I'm gonna check back here
control<00:02:44.140><c> are</c><00:02:44.909><c> see</c><00:02:45.909><c> what</c><00:02:46.060><c> happens</c><00:02:47.730><c> it's</c><00:02:48.730><c> very</c>

00:02:48.809 --> 00:02:48.819 align:start position:0%
control are see what happens it's very
 

00:02:48.819 --> 00:02:49.699 align:start position:0%
control are see what happens it's very
strange

00:02:49.699 --> 00:02:49.709 align:start position:0%
strange
 

00:02:49.709 --> 00:02:51.599 align:start position:0%
strange
anyways<00:02:50.709><c> what</c><00:02:50.889><c> we're</c><00:02:50.980><c> gonna</c><00:02:51.099><c> do</c><00:02:51.280><c> within</c><00:02:51.430><c> the</c>

00:02:51.599 --> 00:02:51.609 align:start position:0%
anyways what we're gonna do within the
 

00:02:51.609 --> 00:02:53.129 align:start position:0%
anyways what we're gonna do within the
application<00:02:52.239><c> is</c><00:02:52.359><c> every</c><00:02:52.569><c> time</c><00:02:52.720><c> that</c><00:02:52.780><c> a</c><00:02:52.900><c> user</c>

00:02:53.129 --> 00:02:53.139 align:start position:0%
application is every time that a user
 

00:02:53.139 --> 00:02:55.440 align:start position:0%
application is every time that a user
uploads<00:02:53.409><c> a</c><00:02:53.980><c> photo</c><00:02:54.250><c> we're</c><00:02:54.670><c> gonna</c><00:02:54.819><c> be</c><00:02:55.120><c> saving</c>

00:02:55.440 --> 00:02:55.450 align:start position:0%
uploads a photo we're gonna be saving
 

00:02:55.450 --> 00:02:58.229 align:start position:0%
uploads a photo we're gonna be saving
that<00:02:55.780><c> specific</c><00:02:56.409><c> link</c><00:02:56.739><c> due</c><00:02:57.670><c> to</c><00:02:57.790><c> the</c><00:02:57.909><c> fact</c><00:02:58.150><c> that</c>

00:02:58.229 --> 00:02:58.239 align:start position:0%
that specific link due to the fact that
 

00:02:58.239 --> 00:03:00.690 align:start position:0%
that specific link due to the fact that
Amazon<00:02:58.750><c> creates</c><00:02:59.139><c> these</c><00:02:59.590><c> awesome</c><00:03:00.220><c> links</c><00:03:00.489><c> for</c>

00:03:00.690 --> 00:03:00.700 align:start position:0%
Amazon creates these awesome links for
 

00:03:00.700 --> 00:03:02.129 align:start position:0%
Amazon creates these awesome links for
you<00:03:00.849><c> and</c><00:03:01.030><c> there</c><00:03:01.150><c> we</c><00:03:01.239><c> go</c><00:03:01.359><c> we</c><00:03:01.690><c> have</c><00:03:01.720><c> all</c><00:03:01.930><c> the</c>

00:03:02.129 --> 00:03:02.139 align:start position:0%
you and there we go we have all the
 

00:03:02.139 --> 00:03:07.940 align:start position:0%
you and there we go we have all the
images<00:03:02.579><c> finally</c><00:03:03.579><c> getting</c><00:03:03.819><c> uploaded</c><00:03:05.700><c> do</c><00:03:06.700><c> you</c>

00:03:07.940 --> 00:03:07.950 align:start position:0%
images finally getting uploaded do you
 

00:03:07.950 --> 00:03:17.339 align:start position:0%
images finally getting uploaded do you
do<00:03:08.950><c> da</c><00:03:09.639><c> de</c><00:03:10.000><c> da</c><00:03:11.129><c> de</c><00:03:12.129><c> da</c><00:03:12.730><c> de</c><00:03:12.959><c> da</c><00:03:15.810><c> sorry</c><00:03:16.810><c> guys</c><00:03:17.019><c> it's</c>

00:03:17.339 --> 00:03:17.349 align:start position:0%
do da de da de da de da sorry guys it's
 

00:03:17.349 --> 00:03:19.890 align:start position:0%
do da de da de da de da sorry guys it's
not<00:03:17.530><c> the</c><00:03:17.739><c> first</c><00:03:17.949><c> or</c><00:03:18.220><c> the</c><00:03:18.250><c> second</c><00:03:18.730><c> one</c><00:03:18.940><c> it's</c>

00:03:19.890 --> 00:03:19.900 align:start position:0%
not the first or the second one it's
 

00:03:19.900 --> 00:03:26.159 align:start position:0%
not the first or the second one it's
actually<00:03:20.519><c> the</c><00:03:21.870><c> second</c><00:03:22.870><c> and</c><00:03:22.989><c> third</c><00:03:24.120><c> we</c><00:03:25.169><c> open</c>

00:03:26.159 --> 00:03:26.169 align:start position:0%
actually the second and third we open
 

00:03:26.169 --> 00:03:36.780 align:start position:0%
actually the second and third we open
this<00:03:26.489><c> and</c><00:03:27.489><c> I</c><00:03:27.760><c> will</c><00:03:31.349><c> there's</c><00:03:32.349><c> that</c><00:03:35.639><c> there's</c><00:03:36.639><c> the</c>

00:03:36.780 --> 00:03:36.790 align:start position:0%
this and I will there's that there's the
 

00:03:36.790 --> 00:03:38.159 align:start position:0%
this and I will there's that there's the
cabinet<00:03:37.209><c> knobs</c><00:03:37.389><c> I</c><00:03:37.540><c> used</c><00:03:37.720><c> to</c><00:03:37.810><c> hate</c><00:03:37.900><c> writing</c>

00:03:38.159 --> 00:03:38.169 align:start position:0%
cabinet knobs I used to hate writing
 

00:03:38.169 --> 00:03:39.780 align:start position:0%
cabinet knobs I used to hate writing
assignments<00:03:38.680><c> but</c><00:03:38.799><c> now</c><00:03:38.919><c> I</c><00:03:38.949><c> enjoy</c><00:03:39.099><c> them</c><00:03:39.639><c> I</c>

00:03:39.780 --> 00:03:39.790 align:start position:0%
assignments but now I enjoy them I
 

00:03:39.790 --> 00:03:41.430 align:start position:0%
assignments but now I enjoy them I
realize<00:03:40.449><c> that</c><00:03:40.480><c> the</c><00:03:40.660><c> purpose</c><00:03:40.959><c> of</c><00:03:41.079><c> writing</c><00:03:41.199><c> is</c>

00:03:41.430 --> 00:03:41.440 align:start position:0%
realize that the purpose of writing is
 

00:03:41.440 --> 00:03:43.349 align:start position:0%
realize that the purpose of writing is
to<00:03:41.590><c> inflate</c><00:03:41.949><c> weak</c><00:03:42.160><c> ideas</c><00:03:42.579><c> obscured</c><00:03:43.060><c> poor</c>

00:03:43.349 --> 00:03:43.359 align:start position:0%
to inflate weak ideas obscured poor
 

00:03:43.359 --> 00:03:47.670 align:start position:0%
to inflate weak ideas obscured poor
reasoning<00:03:43.959><c> and</c><00:03:44.370><c> inhibit</c><00:03:45.370><c> clarity</c><00:03:46.680><c> inflate</c>

00:03:47.670 --> 00:03:47.680 align:start position:0%
reasoning and inhibit clarity inflate
 

00:03:47.680 --> 00:03:51.210 align:start position:0%
reasoning and inhibit clarity inflate
weak<00:03:47.889><c> ideas</c><00:03:48.629><c> awesome</c><00:03:49.629><c> Calvin</c><00:03:50.109><c> and</c><00:03:50.260><c> Hobbes</c><00:03:50.440><c> and</c>

00:03:51.210 --> 00:03:51.220 align:start position:0%
weak ideas awesome Calvin and Hobbes and
 

00:03:51.220 --> 00:04:00.629 align:start position:0%
weak ideas awesome Calvin and Hobbes and
finally<00:03:52.000><c> the</c><00:03:52.299><c> last</c><00:03:52.480><c> one</c><00:03:58.739><c> Jack</c><00:03:59.739><c> Black</c><00:04:00.040><c> ok</c><00:04:00.400><c> good</c>

00:04:00.629 --> 00:04:00.639 align:start position:0%
finally the last one Jack Black ok good
 

00:04:00.639 --> 00:04:03.059 align:start position:0%
finally the last one Jack Black ok good
so<00:04:00.849><c> that's</c><00:04:01.269><c> everything</c><00:04:01.569><c> and</c><00:04:02.019><c> that's</c><00:04:02.889><c> the</c>

00:04:03.059 --> 00:04:03.069 align:start position:0%
so that's everything and that's the
 

00:04:03.069 --> 00:04:04.319 align:start position:0%
so that's everything and that's the
project<00:04:03.280><c> that</c><00:04:03.519><c> we're</c><00:04:03.699><c> gonna</c><00:04:03.790><c> need</c><00:04:04.000><c> to</c><00:04:04.150><c> move</c>

00:04:04.319 --> 00:04:04.329 align:start position:0%
project that we're gonna need to move
 

00:04:04.329 --> 00:04:06.930 align:start position:0%
project that we're gonna need to move
into<00:04:04.510><c> our</c><00:04:04.690><c> existing</c><00:04:05.159><c> application</c><00:04:06.159><c> ok</c><00:04:06.699><c> it's</c>

00:04:06.930 --> 00:04:06.940 align:start position:0%
into our existing application ok it's
 

00:04:06.940 --> 00:04:08.789 align:start position:0%
into our existing application ok it's
actually<00:04:07.090><c> got</c><00:04:07.419><c> a</c><00:04:07.449><c> really</c><00:04:07.659><c> nice</c><00:04:07.930><c> feel</c><00:04:08.229><c> to</c><00:04:08.260><c> it</c>

00:04:08.789 --> 00:04:08.799 align:start position:0%
actually got a really nice feel to it
 

00:04:08.799 --> 00:04:10.680 align:start position:0%
actually got a really nice feel to it
oh<00:04:08.829><c> ok</c><00:04:09.790><c> I</c><00:04:09.819><c> didn't</c><00:04:10.060><c> mean</c><00:04:10.150><c> to</c><00:04:10.269><c> show</c><00:04:10.449><c> you</c><00:04:10.510><c> the</c>

00:04:10.680 --> 00:04:10.690 align:start position:0%
oh ok I didn't mean to show you the
 

00:04:10.690 --> 00:04:13.379 align:start position:0%
oh ok I didn't mean to show you the
passwords<00:04:11.169><c> but</c><00:04:11.379><c> that's</c><00:04:11.620><c> everything</c><00:04:12.129><c> ok</c><00:04:13.090><c> and</c>

00:04:13.379 --> 00:04:13.389 align:start position:0%
passwords but that's everything ok and
 

00:04:13.389 --> 00:04:15.930 align:start position:0%
passwords but that's everything ok and
compression<00:04:14.319><c> plus</c><00:04:14.680><c> the</c><00:04:14.889><c> actual</c><00:04:15.040><c> upload</c><00:04:15.699><c> and</c>

00:04:15.930 --> 00:04:15.940 align:start position:0%
compression plus the actual upload and
 

00:04:15.940 --> 00:04:17.909 align:start position:0%
compression plus the actual upload and
it's<00:04:16.060><c> your</c><00:04:16.180><c> absolutely</c><00:04:16.570><c> great</c><00:04:16.900><c> what</c><00:04:17.620><c> happens</c>

00:04:17.909 --> 00:04:17.919 align:start position:0%
it's your absolutely great what happens
 

00:04:17.919 --> 00:04:21.629 align:start position:0%
it's your absolutely great what happens
if<00:04:18.010><c> I</c><00:04:18.130><c> click</c><00:04:18.370><c> remove</c><00:04:18.669><c> image</c><00:04:20.250><c> see</c><00:04:21.250><c> if</c><00:04:21.310><c> image</c>

00:04:21.629 --> 00:04:21.639 align:start position:0%
if I click remove image see if image
 

00:04:21.639 --> 00:04:22.980 align:start position:0%
if I click remove image see if image
will<00:04:21.789><c> still</c><00:04:22.060><c> be</c><00:04:22.180><c> available</c>

00:04:22.980 --> 00:04:22.990 align:start position:0%
will still be available
 

00:04:22.990 --> 00:04:25.520 align:start position:0%
will still be available
you<00:04:23.650><c> remove</c><00:04:23.949><c> the</c><00:04:24.160><c> image</c><00:04:24.340><c> remove</c><00:04:24.759><c> the</c><00:04:24.940><c> tab</c>

00:04:25.520 --> 00:04:25.530 align:start position:0%
you remove the image remove the tab
 

00:04:25.530 --> 00:04:29.969 align:start position:0%
you remove the image remove the tab
let's<00:04:26.530><c> see</c><00:04:26.710><c> here</c><00:04:27.039><c> if</c><00:04:27.069><c> it's</c><00:04:27.630><c> by</c><00:04:28.630><c> reload</c><00:04:29.080><c> did</c>

00:04:29.969 --> 00:04:29.979 align:start position:0%
let's see here if it's by reload did
 

00:04:29.979 --> 00:04:32.279 align:start position:0%
let's see here if it's by reload did
anything<00:04:30.190><c> get</c><00:04:30.400><c> deleted</c><00:04:30.699><c> probably</c><00:04:31.120><c> not</c><00:04:31.289><c> not</c>

00:04:32.279 --> 00:04:32.289 align:start position:0%
anything get deleted probably not not
 

00:04:32.289 --> 00:04:34.080 align:start position:0%
anything get deleted probably not not
nothing<00:04:32.650><c> gets</c><00:04:32.800><c> the</c><00:04:32.919><c> later</c><00:04:33.099><c> just</c><00:04:33.370><c> gets</c><00:04:33.580><c> removed</c>

00:04:34.080 --> 00:04:34.090 align:start position:0%
nothing gets the later just gets removed
 

00:04:34.090 --> 00:04:37.379 align:start position:0%
nothing gets the later just gets removed
in<00:04:34.240><c> the</c><00:04:34.360><c> UI</c><00:04:34.509><c> good</c><00:04:35.349><c> so</c><00:04:35.680><c> we're</c><00:04:36.430><c> gonna</c><00:04:36.550><c> cover</c><00:04:36.789><c> the</c>

00:04:37.379 --> 00:04:37.389 align:start position:0%
in the UI good so we're gonna cover the
 

00:04:37.389 --> 00:04:39.450 align:start position:0%
in the UI good so we're gonna cover the
code<00:04:37.660><c> in</c><00:04:38.080><c> the</c><00:04:38.289><c> next</c><00:04:38.530><c> video</c><00:04:38.830><c> and</c><00:04:39.069><c> talk</c><00:04:39.340><c> about</c>

00:04:39.450 --> 00:04:39.460 align:start position:0%
code in the next video and talk about
 

00:04:39.460 --> 00:04:41.100 align:start position:0%
code in the next video and talk about
how<00:04:39.639><c> to</c><00:04:39.699><c> integrated</c><00:04:40.270><c> into</c><00:04:40.599><c> our</c><00:04:40.690><c> main</c><00:04:40.840><c> project</c>

00:04:41.100 --> 00:04:41.110 align:start position:0%
how to integrated into our main project
 

00:04:41.110 --> 00:04:43.710 align:start position:0%
how to integrated into our main project
if<00:04:41.949><c> you</c><00:04:42.190><c> appreciate</c><00:04:43.000><c> this</c><00:04:43.180><c> video</c><00:04:43.479><c> feel</c><00:04:43.690><c> free</c>

00:04:43.710 --> 00:04:43.720 align:start position:0%
if you appreciate this video feel free
 

00:04:43.720 --> 00:04:45.629 align:start position:0%
if you appreciate this video feel free
to<00:04:43.870><c> give</c><00:04:44.080><c> it</c><00:04:44.110><c> a</c><00:04:44.259><c> like</c><00:04:44.470><c> and</c><00:04:44.710><c> check</c><00:04:44.770><c> out</c><00:04:45.250><c> the</c><00:04:45.430><c> live</c>

00:04:45.629 --> 00:04:45.639 align:start position:0%
to give it a like and check out the live
 

00:04:45.639 --> 00:04:49.890 align:start position:0%
to give it a like and check out the live
startup<00:04:46.210><c> series</c><00:04:46.479><c> for</c><00:04:47.349><c> more</c><00:04:48.240><c> help</c><00:04:49.240><c> helpful</c>

00:04:49.890 --> 00:04:49.900 align:start position:0%
startup series for more help helpful
 

00:04:49.900 --> 00:04:52.949 align:start position:0%
startup series for more help helpful
tutorial<00:04:50.620><c> videos</c>

