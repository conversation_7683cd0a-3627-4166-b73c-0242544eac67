WEBVTT
Kind: captions
Language: en

00:00:01.480 --> 00:00:04.070 align:start position:0%
 
I'd<00:00:01.680><c> like</c><00:00:01.800><c> to</c><00:00:02.000><c> present</c><00:00:02.360><c> two</c><00:00:02.560><c> use</c><00:00:02.919><c> cases</c><00:00:03.879><c> that</c>

00:00:04.070 --> 00:00:04.080 align:start position:0%
I'd like to present two use cases that
 

00:00:04.080 --> 00:00:05.869 align:start position:0%
I'd like to present two use cases that
highlight<00:00:04.440><c> the</c><00:00:04.600><c> value</c><00:00:04.880><c> of</c><00:00:05.040><c> Weights</c><00:00:05.279><c> Andes</c>

00:00:05.869 --> 00:00:05.879 align:start position:0%
highlight the value of Weights Andes
 

00:00:05.879 --> 00:00:08.830 align:start position:0%
highlight the value of Weights Andes
weave<00:00:06.799><c> combined</c><00:00:07.359><c> with</c><00:00:07.560><c> the</c><00:00:07.799><c> powerful</c><00:00:08.480><c> large</c>

00:00:08.830 --> 00:00:08.840 align:start position:0%
weave combined with the powerful large
 

00:00:08.840 --> 00:00:11.509 align:start position:0%
weave combined with the powerful large
language<00:00:09.240><c> model</c><00:00:09.960><c> Gemini</c>

00:00:11.509 --> 00:00:11.519 align:start position:0%
language model Gemini
 

00:00:11.519 --> 00:00:14.629 align:start position:0%
language model Gemini
Pro<00:00:12.519><c> the</c><00:00:12.679><c> first</c><00:00:12.920><c> use</c><00:00:13.240><c> case</c><00:00:13.480><c> is</c><00:00:13.759><c> summarization</c>

00:00:14.629 --> 00:00:14.639 align:start position:0%
Pro the first use case is summarization
 

00:00:14.639 --> 00:00:17.630 align:start position:0%
Pro the first use case is summarization
of<00:00:14.799><c> long</c><00:00:15.400><c> documents</c><00:00:16.400><c> many</c><00:00:16.600><c> of</c><00:00:16.760><c> our</c><00:00:17.039><c> customers</c>

00:00:17.630 --> 00:00:17.640 align:start position:0%
of long documents many of our customers
 

00:00:17.640 --> 00:00:19.790 align:start position:0%
of long documents many of our customers
face<00:00:18.000><c> time</c><00:00:18.320><c> constraints</c><00:00:19.320><c> but</c><00:00:19.480><c> need</c><00:00:19.640><c> to</c>

00:00:19.790 --> 00:00:19.800 align:start position:0%
face time constraints but need to
 

00:00:19.800 --> 00:00:21.310 align:start position:0%
face time constraints but need to
process<00:00:20.119><c> a</c><00:00:20.240><c> lot</c><00:00:20.400><c> of</c>

00:00:21.310 --> 00:00:21.320 align:start position:0%
process a lot of
 

00:00:21.320 --> 00:00:24.029 align:start position:0%
process a lot of
information<00:00:22.320><c> this</c><00:00:22.439><c> is</c><00:00:22.640><c> where</c><00:00:22.920><c> Gemini's</c><00:00:23.599><c> large</c>

00:00:24.029 --> 00:00:24.039 align:start position:0%
information this is where Gemini's large
 

00:00:24.039 --> 00:00:28.109 align:start position:0%
information this is where Gemini's large
context<00:00:24.400><c> window</c><00:00:24.840><c> becomes</c><00:00:25.640><c> particularly</c>

00:00:28.109 --> 00:00:28.119 align:start position:0%
context window becomes particularly
 

00:00:28.119 --> 00:00:30.429 align:start position:0%
context window becomes particularly
beneficial<00:00:29.119><c> the</c><00:00:29.240><c> Second</c><00:00:29.519><c> Use</c><00:00:29.840><c> case</c><00:00:30.199><c> is</c><00:00:30.359><c> the</c>

00:00:30.429 --> 00:00:30.439 align:start position:0%
beneficial the Second Use case is the
 

00:00:30.439 --> 00:00:34.229 align:start position:0%
beneficial the Second Use case is the
Morgan<00:00:30.800><c> Stanley</c><00:00:31.759><c> research</c><00:00:32.239><c> rack</c><00:00:32.879><c> model</c><00:00:33.879><c> many</c>

00:00:34.229 --> 00:00:34.239 align:start position:0%
Morgan Stanley research rack model many
 

00:00:34.239 --> 00:00:36.430 align:start position:0%
Morgan Stanley research rack model many
customers<00:00:35.160><c> use</c><00:00:35.520><c> retrieval</c><00:00:35.960><c> augmented</c>

00:00:36.430 --> 00:00:36.440 align:start position:0%
customers use retrieval augmented
 

00:00:36.440 --> 00:00:38.910 align:start position:0%
customers use retrieval augmented
generation<00:00:37.320><c> to</c><00:00:37.640><c> access</c><00:00:38.200><c> and</c><00:00:38.399><c> respond</c><00:00:38.719><c> to</c>

00:00:38.910 --> 00:00:38.920 align:start position:0%
generation to access and respond to
 

00:00:38.920 --> 00:00:41.430 align:start position:0%
generation to access and respond to
questions<00:00:39.840><c> related</c><00:00:40.200><c> to</c><00:00:40.440><c> proprietary</c><00:00:41.239><c> or</c>

00:00:41.430 --> 00:00:41.440 align:start position:0%
questions related to proprietary or
 

00:00:41.440 --> 00:00:43.990 align:start position:0%
questions related to proprietary or
internal<00:00:42.360><c> documents</c><00:00:43.360><c> let's</c><00:00:43.600><c> start</c><00:00:43.800><c> with</c><00:00:43.920><c> the</c>

00:00:43.990 --> 00:00:44.000 align:start position:0%
internal documents let's start with the
 

00:00:44.000 --> 00:00:50.910 align:start position:0%
internal documents let's start with the
summarization<00:00:44.640><c> use</c>

00:00:50.910 --> 00:00:50.920 align:start position:0%
 
 

00:00:50.920 --> 00:00:54.069 align:start position:0%
 
case<00:00:51.920><c> we</c><00:00:52.039><c> can</c><00:00:52.160><c> see</c><00:00:52.399><c> the</c><00:00:52.559><c> model</c><00:00:53.000><c> is</c><00:00:53.359><c> defined</c><00:00:53.760><c> in</c>

00:00:54.069 --> 00:00:54.079 align:start position:0%
case we can see the model is defined in
 

00:00:54.079 --> 00:00:56.189 align:start position:0%
case we can see the model is defined in
weave<00:00:54.640><c> where</c><00:00:54.840><c> we</c><00:00:54.960><c> can</c><00:00:55.120><c> see</c><00:00:55.840><c> all</c><00:00:56.039><c> the</c>

00:00:56.189 --> 00:00:56.199 align:start position:0%
weave where we can see all the
 

00:00:56.199 --> 00:00:58.790 align:start position:0%
weave where we can see all the
parameters<00:00:56.840><c> including</c><00:00:57.280><c> the</c><00:00:57.399><c> model</c><00:00:57.800><c> name</c>

00:00:58.790 --> 00:00:58.800 align:start position:0%
parameters including the model name
 

00:00:58.800 --> 00:01:02.509 align:start position:0%
parameters including the model name
which<00:00:59.000><c> refers</c><00:00:59.320><c> to</c><00:00:59.519><c> Gemini</c><00:01:00.280><c> 1.5</c><00:01:01.280><c> Pro</c><00:01:01.559><c> latest</c>

00:01:02.509 --> 00:01:02.519 align:start position:0%
which refers to Gemini 1.5 Pro latest
 

00:01:02.519 --> 00:01:05.469 align:start position:0%
which refers to Gemini 1.5 Pro latest
checkpoint<00:01:03.519><c> the</c><00:01:03.680><c> promt</c><00:01:04.159><c> template</c><00:01:05.159><c> uh</c><00:01:05.239><c> in</c><00:01:05.360><c> the</c>

00:01:05.469 --> 00:01:05.479 align:start position:0%
checkpoint the promt template uh in the
 

00:01:05.479 --> 00:01:08.270 align:start position:0%
checkpoint the promt template uh in the
summary<00:01:05.880><c> model</c><00:01:06.280><c> refers</c><00:01:06.600><c> to</c><00:01:06.760><c> a</c><00:01:06.880><c> Json</c><00:01:07.240><c> schema</c><00:01:08.159><c> we</c>

00:01:08.270 --> 00:01:08.280 align:start position:0%
summary model refers to a Json schema we
 

00:01:08.280 --> 00:01:11.390 align:start position:0%
summary model refers to a Json schema we
can<00:01:08.560><c> also</c><00:01:08.840><c> see</c><00:01:09.320><c> Json</c><00:01:09.720><c> schema</c><00:01:10.119><c> stored</c><00:01:10.799><c> as</c><00:01:11.280><c> one</c>

00:01:11.390 --> 00:01:11.400 align:start position:0%
can also see Json schema stored as one
 

00:01:11.400 --> 00:01:14.469 align:start position:0%
can also see Json schema stored as one
of<00:01:11.520><c> the</c><00:01:11.680><c> input</c><00:01:12.040><c> parameters</c><00:01:12.799><c> into</c><00:01:13.040><c> the</c><00:01:13.479><c> model</c>

00:01:14.469 --> 00:01:14.479 align:start position:0%
of the input parameters into the model
 

00:01:14.479 --> 00:01:17.350 align:start position:0%
of the input parameters into the model
we<00:01:14.600><c> can</c><00:01:14.759><c> see</c><00:01:15.520><c> uh</c><00:01:15.640><c> a</c><00:01:15.799><c> title</c><00:01:16.240><c> and</c><00:01:16.400><c> a</c><00:01:16.520><c> summary</c><00:01:17.200><c> that</c>

00:01:17.350 --> 00:01:17.360 align:start position:0%
we can see uh a title and a summary that
 

00:01:17.360 --> 00:01:19.390 align:start position:0%
we can see uh a title and a summary that
we<00:01:17.479><c> want</c><00:01:17.680><c> to</c><00:01:17.880><c> get</c><00:01:18.400><c> uh</c><00:01:18.520><c> as</c><00:01:18.640><c> an</c><00:01:18.799><c> output</c><00:01:19.159><c> of</c><00:01:19.280><c> the</c>

00:01:19.390 --> 00:01:19.400 align:start position:0%
we want to get uh as an output of the
 

00:01:19.400 --> 00:01:22.429 align:start position:0%
we want to get uh as an output of the
model<00:01:20.280><c> we</c><00:01:20.479><c> also</c><00:01:20.720><c> see</c><00:01:21.040><c> that</c><00:01:21.520><c> um</c><00:01:21.840><c> the</c><00:01:22.000><c> summary</c>

00:01:22.429 --> 00:01:22.439 align:start position:0%
model we also see that um the summary
 

00:01:22.439 --> 00:01:24.429 align:start position:0%
model we also see that um the summary
should<00:01:22.600><c> be</c><00:01:22.720><c> a</c><00:01:22.880><c> plain</c><00:01:23.119><c> short</c><00:01:23.479><c> text</c><00:01:24.040><c> without</c>

00:01:24.429 --> 00:01:24.439 align:start position:0%
should be a plain short text without
 

00:01:24.439 --> 00:01:27.190 align:start position:0%
should be a plain short text without
markdown<00:01:25.439><c> and</c><00:01:25.600><c> we</c><00:01:25.720><c> can</c><00:01:25.920><c> also</c><00:01:26.200><c> see</c><00:01:26.640><c> that</c><00:01:27.079><c> the</c>

00:01:27.190 --> 00:01:27.200 align:start position:0%
markdown and we can also see that the
 

00:01:27.200 --> 00:01:29.390 align:start position:0%
markdown and we can also see that the
prompt<00:01:27.680><c> refers</c><00:01:28.000><c> to</c><00:01:28.159><c> the</c><00:01:28.320><c> length</c><00:01:29.079><c> of</c><00:01:29.240><c> the</c>

00:01:29.390 --> 00:01:29.400 align:start position:0%
prompt refers to the length of the
 

00:01:29.400 --> 00:01:30.830 align:start position:0%
prompt refers to the length of the
document<00:01:29.759><c> which</c><00:01:29.920><c> which</c><00:01:30.000><c> should</c><00:01:30.159><c> be</c><00:01:30.320><c> less</c><00:01:30.479><c> than</c>

00:01:30.830 --> 00:01:30.840 align:start position:0%
document which which should be less than
 

00:01:30.840 --> 00:01:31.950 align:start position:0%
document which which should be less than
200

00:01:31.950 --> 00:01:31.960 align:start position:0%
200
 

00:01:31.960 --> 00:01:34.389 align:start position:0%
200
words<00:01:32.960><c> defining</c><00:01:33.439><c> these</c><00:01:33.640><c> requirements</c><00:01:34.119><c> in</c><00:01:34.240><c> the</c>

00:01:34.389 --> 00:01:34.399 align:start position:0%
words defining these requirements in the
 

00:01:34.399 --> 00:01:37.469 align:start position:0%
words defining these requirements in the
prompt<00:01:34.720><c> is</c><00:01:34.920><c> good</c><00:01:35.600><c> but</c><00:01:36.079><c> evaluating</c><00:01:36.799><c> your</c><00:01:37.000><c> llm</c>

00:01:37.469 --> 00:01:37.479 align:start position:0%
prompt is good but evaluating your llm
 

00:01:37.479 --> 00:01:39.870 align:start position:0%
prompt is good but evaluating your llm
application<00:01:38.159><c> against</c><00:01:38.560><c> these</c><00:01:38.880><c> requirements</c>

00:01:39.870 --> 00:01:39.880 align:start position:0%
application against these requirements
 

00:01:39.880 --> 00:01:41.870 align:start position:0%
application against these requirements
builds<00:01:40.399><c> confidence</c><00:01:41.159><c> that</c><00:01:41.320><c> the</c><00:01:41.439><c> model</c>

00:01:41.870 --> 00:01:41.880 align:start position:0%
builds confidence that the model
 

00:01:41.880 --> 00:01:43.469 align:start position:0%
builds confidence that the model
actually<00:01:42.200><c> meets</c>

00:01:43.469 --> 00:01:43.479 align:start position:0%
actually meets
 

00:01:43.479 --> 00:01:46.789 align:start position:0%
actually meets
them<00:01:44.479><c> by</c><00:01:44.759><c> defining</c><00:01:45.200><c> your</c><00:01:45.360><c> model</c><00:01:45.759><c> in</c><00:01:46.040><c> weave</c><00:01:46.640><c> you</c>

00:01:46.789 --> 00:01:46.799 align:start position:0%
them by defining your model in weave you
 

00:01:46.799 --> 00:01:49.870 align:start position:0%
them by defining your model in weave you
can<00:01:47.360><c> easily</c><00:01:47.920><c> reference</c><00:01:48.680><c> and</c><00:01:48.880><c> deploy</c>

00:01:49.870 --> 00:01:49.880 align:start position:0%
can easily reference and deploy
 

00:01:49.880 --> 00:01:53.590 align:start position:0%
can easily reference and deploy
it<00:01:50.880><c> for</c><00:01:51.079><c> example</c><00:01:51.600><c> on</c>

00:01:53.590 --> 00:01:53.600 align:start position:0%
it for example on
 

00:01:53.600 --> 00:01:57.149 align:start position:0%
it for example on
gcp<00:01:54.600><c> these</c><00:01:54.759><c> setups</c><00:01:55.439><c> provide</c><00:01:55.960><c> a</c><00:01:56.159><c> valuable</c>

00:01:57.149 --> 00:01:57.159 align:start position:0%
gcp these setups provide a valuable
 

00:01:57.159 --> 00:01:59.550 align:start position:0%
gcp these setups provide a valuable
observability<00:01:58.159><c> by</c><00:01:58.320><c> storing</c><00:01:58.840><c> all</c><00:01:59.079><c> your</c><00:01:59.240><c> model</c>

00:01:59.550 --> 00:01:59.560 align:start position:0%
observability by storing all your model
 

00:01:59.560 --> 00:02:01.749 align:start position:0%
observability by storing all your model
call<00:02:00.320><c> we</c><00:02:00.479><c> can</c><00:02:00.600><c> see</c><00:02:00.920><c> all</c><00:02:01.079><c> the</c><00:02:01.200><c> calls</c><00:02:01.439><c> to</c><00:02:01.600><c> our</c>

00:02:01.749 --> 00:02:01.759 align:start position:0%
call we can see all the calls to our
 

00:02:01.759 --> 00:02:06.310 align:start position:0%
call we can see all the calls to our
predict<00:02:02.159><c> method</c><00:02:03.079><c> and</c><00:02:03.479><c> inspect</c><00:02:04.479><c> the</c><00:02:05.479><c> inputs</c>

00:02:06.310 --> 00:02:06.320 align:start position:0%
predict method and inspect the inputs
 

00:02:06.320 --> 00:02:08.790 align:start position:0%
predict method and inspect the inputs
and<00:02:06.479><c> the</c><00:02:06.640><c> outputs</c><00:02:07.240><c> and</c><00:02:07.360><c> see</c><00:02:07.560><c> how</c><00:02:07.680><c> the</c><00:02:07.799><c> model</c><00:02:08.080><c> is</c>

00:02:08.790 --> 00:02:08.800 align:start position:0%
and the outputs and see how the model is
 

00:02:08.800 --> 00:02:11.750 align:start position:0%
and the outputs and see how the model is
doing<00:02:09.800><c> now</c><00:02:09.959><c> for</c><00:02:10.160><c> the</c>

00:02:11.750 --> 00:02:11.760 align:start position:0%
doing now for the
 

00:02:11.760 --> 00:02:14.509 align:start position:0%
doing now for the
evaluation<00:02:12.760><c> we</c><00:02:12.879><c> want</c><00:02:13.040><c> to</c><00:02:13.239><c> check</c><00:02:13.640><c> that</c><00:02:13.879><c> our</c>

00:02:14.509 --> 00:02:14.519 align:start position:0%
evaluation we want to check that our
 

00:02:14.519 --> 00:02:17.309 align:start position:0%
evaluation we want to check that our
summary<00:02:15.519><c> um</c><00:02:15.879><c> is</c><00:02:16.040><c> generated</c><00:02:16.640><c> according</c><00:02:17.000><c> to</c><00:02:17.160><c> the</c>

00:02:17.309 --> 00:02:17.319 align:start position:0%
summary um is generated according to the
 

00:02:17.319 --> 00:02:20.229 align:start position:0%
summary um is generated according to the
defined<00:02:17.760><c> schema</c><00:02:18.760><c> and</c><00:02:19.000><c> is</c><00:02:19.239><c> less</c><00:02:19.440><c> than</c><00:02:19.800><c> 200</c>

00:02:20.229 --> 00:02:20.239 align:start position:0%
defined schema and is less than 200
 

00:02:20.239 --> 00:02:23.869 align:start position:0%
defined schema and is less than 200
words<00:02:21.239><c> we</c><00:02:21.400><c> have</c><00:02:21.599><c> a</c><00:02:21.840><c> data</c><00:02:22.200><c> set</c><00:02:22.640><c> of</c><00:02:22.920><c> 18</c><00:02:23.480><c> long</c>

00:02:23.869 --> 00:02:23.879 align:start position:0%
words we have a data set of 18 long
 

00:02:23.879 --> 00:02:26.430 align:start position:0%
words we have a data set of 18 long
papers<00:02:24.599><c> and</c><00:02:24.800><c> evaluate</c><00:02:25.319><c> our</c><00:02:25.560><c> model</c><00:02:25.920><c> on</c><00:02:26.120><c> two</c>

00:02:26.430 --> 00:02:26.440 align:start position:0%
papers and evaluate our model on two
 

00:02:26.440 --> 00:02:29.270 align:start position:0%
papers and evaluate our model on two
metrics<00:02:27.440><c> uh</c><00:02:27.599><c> the</c><00:02:27.879><c> formatting</c><00:02:28.879><c> and</c><00:02:29.080><c> the</c>

00:02:29.270 --> 00:02:29.280 align:start position:0%
metrics uh the formatting and the
 

00:02:29.280 --> 00:02:31.110 align:start position:0%
metrics uh the formatting and the
conciseness

00:02:31.110 --> 00:02:31.120 align:start position:0%
conciseness
 

00:02:31.120 --> 00:02:32.790 align:start position:0%
conciseness
we<00:02:31.280><c> check</c><00:02:31.480><c> if</c><00:02:31.640><c> the</c><00:02:31.760><c> formatting</c><00:02:32.280><c> meets</c><00:02:32.560><c> our</c>

00:02:32.790 --> 00:02:32.800 align:start position:0%
we check if the formatting meets our
 

00:02:32.800 --> 00:02:35.509 align:start position:0%
we check if the formatting meets our
requirements<00:02:33.800><c> and</c><00:02:34.000><c> if</c><00:02:34.120><c> the</c><00:02:34.239><c> word</c><00:02:34.560><c> count</c><00:02:35.080><c> stays</c>

00:02:35.509 --> 00:02:35.519 align:start position:0%
requirements and if the word count stays
 

00:02:35.519 --> 00:02:37.990 align:start position:0%
requirements and if the word count stays
within<00:02:35.800><c> our</c><00:02:36.040><c> set</c><00:02:36.319><c> limit</c><00:02:37.280><c> we</c><00:02:37.360><c> can</c><00:02:37.519><c> see</c><00:02:37.720><c> that</c><00:02:37.879><c> we</c>

00:02:37.990 --> 00:02:38.000 align:start position:0%
within our set limit we can see that we
 

00:02:38.000 --> 00:02:41.790 align:start position:0%
within our set limit we can see that we
achieve<00:02:38.360><c> around</c><00:02:38.720><c> 89%</c><00:02:39.920><c> accuracy</c><00:02:40.920><c> uh</c><00:02:41.040><c> on</c><00:02:41.280><c> both</c>

00:02:41.790 --> 00:02:41.800 align:start position:0%
achieve around 89% accuracy uh on both
 

00:02:41.800 --> 00:02:44.550 align:start position:0%
achieve around 89% accuracy uh on both
metrics<00:02:42.800><c> but</c><00:02:43.120><c> we</c><00:02:43.200><c> need</c><00:02:43.360><c> to</c><00:02:43.480><c> dig</c><00:02:43.720><c> deeper</c><00:02:44.080><c> to</c>

00:02:44.550 --> 00:02:44.560 align:start position:0%
metrics but we need to dig deeper to
 

00:02:44.560 --> 00:02:50.750 align:start position:0%
metrics but we need to dig deeper to
understand<00:02:44.760><c> where</c><00:02:44.920><c> the</c><00:02:45.000><c> model</c>

00:02:50.750 --> 00:02:50.760 align:start position:0%
 
 

00:02:50.760 --> 00:02:54.670 align:start position:0%
 
fails<00:02:51.760><c> in</c><00:02:51.959><c> most</c><00:02:52.360><c> cases</c><00:02:53.360><c> uh</c><00:02:53.560><c> the</c><00:02:53.879><c> model</c><00:02:54.280><c> meets</c>

00:02:54.670 --> 00:02:54.680 align:start position:0%
fails in most cases uh the model meets
 

00:02:54.680 --> 00:02:57.550 align:start position:0%
fails in most cases uh the model meets
our<00:02:55.200><c> requirement</c><00:02:56.040><c> but</c><00:02:56.239><c> it</c><00:02:56.400><c> fails</c><00:02:56.760><c> in</c><00:02:57.239><c> two</c>

00:02:57.550 --> 00:02:57.560 align:start position:0%
our requirement but it fails in two
 

00:02:57.560 --> 00:03:01.229 align:start position:0%
our requirement but it fails in two
instances

00:03:01.229 --> 00:03:01.239 align:start position:0%
 
 

00:03:01.239 --> 00:03:04.509 align:start position:0%
 
when<00:03:01.440><c> we</c><00:03:01.760><c> inspect</c><00:03:02.599><c> um</c><00:03:03.480><c> these</c><00:03:03.840><c> instances</c><00:03:04.360><c> where</c>

00:03:04.509 --> 00:03:04.519 align:start position:0%
when we inspect um these instances where
 

00:03:04.519 --> 00:03:08.949 align:start position:0%
when we inspect um these instances where
the<00:03:04.599><c> model</c><00:03:04.959><c> is</c><00:03:06.000><c> failing</c><00:03:07.000><c> and</c><00:03:07.360><c> we</c><00:03:07.480><c> can</c><00:03:07.680><c> see</c><00:03:08.159><c> that</c>

00:03:08.949 --> 00:03:08.959 align:start position:0%
the model is failing and we can see that
 

00:03:08.959 --> 00:03:12.550 align:start position:0%
the model is failing and we can see that
the<00:03:09.360><c> problem</c><00:03:10.080><c> is</c><00:03:11.080><c> with</c><00:03:11.720><c> uh</c><00:03:11.959><c> the</c><00:03:12.120><c> predict</c>

00:03:12.550 --> 00:03:12.560 align:start position:0%
the problem is with uh the predict
 

00:03:12.560 --> 00:03:14.910 align:start position:0%
the problem is with uh the predict
method<00:03:13.360><c> and</c><00:03:13.519><c> the</c><00:03:13.680><c> error</c><00:03:14.080><c> that</c><00:03:14.200><c> we're</c><00:03:14.480><c> facing</c>

00:03:14.910 --> 00:03:14.920 align:start position:0%
method and the error that we're facing
 

00:03:14.920 --> 00:03:18.869 align:start position:0%
method and the error that we're facing
is<00:03:15.200><c> 504</c><00:03:16.200><c> deadline</c><00:03:16.799><c> exceeded</c><00:03:17.440><c> error</c><00:03:18.440><c> and</c><00:03:18.640><c> that</c>

00:03:18.869 --> 00:03:18.879 align:start position:0%
is 504 deadline exceeded error and that
 

00:03:18.879 --> 00:03:21.550 align:start position:0%
is 504 deadline exceeded error and that
tells<00:03:19.159><c> me</c><00:03:19.480><c> that</c><00:03:20.280><c> uh</c><00:03:20.400><c> whenever</c><00:03:20.799><c> we</c><00:03:21.000><c> actually</c>

00:03:21.550 --> 00:03:21.560 align:start position:0%
tells me that uh whenever we actually
 

00:03:21.560 --> 00:03:24.550 align:start position:0%
tells me that uh whenever we actually
reach<00:03:22.040><c> uh</c><00:03:22.159><c> the</c><00:03:22.280><c> model</c><00:03:22.599><c> API</c><00:03:23.440><c> it</c><00:03:23.640><c> responds</c>

00:03:24.550 --> 00:03:24.560 align:start position:0%
reach uh the model API it responds
 

00:03:24.560 --> 00:03:26.869 align:start position:0%
reach uh the model API it responds
reliably<00:03:25.560><c> but</c><00:03:25.840><c> in</c><00:03:26.040><c> some</c><00:03:26.280><c> cases</c><00:03:26.599><c> We</c><00:03:26.720><c> are</c>

00:03:26.869 --> 00:03:26.879 align:start position:0%
reliably but in some cases We are
 

00:03:26.879 --> 00:03:29.309 align:start position:0%
reliably but in some cases We are
failing<00:03:27.239><c> to</c><00:03:27.400><c> reach</c><00:03:27.720><c> the</c><00:03:27.959><c> API</c><00:03:28.400><c> properly</c><00:03:29.200><c> and</c>

00:03:29.309 --> 00:03:29.319 align:start position:0%
failing to reach the API properly and
 

00:03:29.319 --> 00:03:30.869 align:start position:0%
failing to reach the API properly and
that<00:03:29.439><c> means</c><00:03:29.799><c> that's</c><00:03:29.959><c> something</c><00:03:30.360><c> that</c><00:03:30.519><c> related</c>

00:03:30.869 --> 00:03:30.879 align:start position:0%
that means that's something that related
 

00:03:30.879 --> 00:03:34.190 align:start position:0%
that means that's something that related
to<00:03:31.000><c> our</c><00:03:31.200><c> setup</c><00:03:31.640><c> for</c><00:03:31.799><c> example</c><00:03:32.360><c> our</c><00:03:33.280><c> um</c><00:03:33.799><c> request</c>

00:03:34.190 --> 00:03:34.200 align:start position:0%
to our setup for example our um request
 

00:03:34.200 --> 00:03:37.149 align:start position:0%
to our setup for example our um request
quota<00:03:34.760><c> might</c><00:03:35.000><c> require</c><00:03:35.360><c> an</c><00:03:35.560><c> increase</c><00:03:36.560><c> and</c><00:03:36.799><c> by</c>

00:03:37.149 --> 00:03:37.159 align:start position:0%
quota might require an increase and by
 

00:03:37.159 --> 00:03:39.110 align:start position:0%
quota might require an increase and by
uh<00:03:37.280><c> looking</c><00:03:37.599><c> at</c><00:03:37.760><c> our</c><00:03:38.000><c> evaluation</c><00:03:38.519><c> in</c><00:03:38.680><c> this</c><00:03:38.879><c> way</c>

00:03:39.110 --> 00:03:39.120 align:start position:0%
uh looking at our evaluation in this way
 

00:03:39.120 --> 00:03:41.789 align:start position:0%
uh looking at our evaluation in this way
we<00:03:39.200><c> can</c><00:03:39.400><c> see</c><00:03:40.080><c> where</c><00:03:40.360><c> our</c><00:03:40.640><c> application</c><00:03:41.239><c> might</c>

00:03:41.789 --> 00:03:41.799 align:start position:0%
we can see where our application might
 

00:03:41.799 --> 00:03:44.789 align:start position:0%
we can see where our application might
um<00:03:42.239><c> require</c><00:03:42.599><c> an</c><00:03:42.760><c> improvement</c><00:03:43.720><c> and</c><00:03:43.920><c> we</c><00:03:44.040><c> can</c><00:03:44.560><c> get</c>

00:03:44.789 --> 00:03:44.799 align:start position:0%
um require an improvement and we can get
 

00:03:44.799 --> 00:03:49.589 align:start position:0%
um require an improvement and we can get
better<00:03:45.080><c> results</c><00:03:45.680><c> over</c>

00:03:49.589 --> 00:03:49.599 align:start position:0%
 
 

00:03:49.599 --> 00:03:53.270 align:start position:0%
 
time<00:03:50.599><c> the</c><00:03:51.040><c> Second</c><00:03:51.400><c> Use</c><00:03:51.760><c> case</c><00:03:52.519><c> uh</c><00:03:52.680><c> involves</c><00:03:53.159><c> the</c>

00:03:53.270 --> 00:03:53.280 align:start position:0%
time the Second Use case uh involves the
 

00:03:53.280 --> 00:03:56.270 align:start position:0%
time the Second Use case uh involves the
Morgan<00:03:53.760><c> Stanley</c><00:03:54.079><c> research</c><00:03:54.519><c> rack</c><00:03:55.120><c> model</c><00:03:56.120><c> here</c>

00:03:56.270 --> 00:03:56.280 align:start position:0%
Morgan Stanley research rack model here
 

00:03:56.280 --> 00:03:59.229 align:start position:0%
Morgan Stanley research rack model here
we<00:03:56.400><c> use</c><00:03:56.599><c> the</c><00:03:56.799><c> predict</c><00:03:57.519><c> method</c><00:03:58.519><c> um</c><00:03:58.920><c> and</c>

00:03:59.229 --> 00:03:59.239 align:start position:0%
we use the predict method um and
 

00:03:59.239 --> 00:04:01.710 align:start position:0%
we use the predict method um and
highlight<00:03:59.560><c> how</c><00:04:00.040><c> easy</c><00:04:00.360><c> it</c><00:04:00.480><c> is</c><00:04:00.640><c> to</c><00:04:00.840><c> instrument</c>

00:04:01.710 --> 00:04:01.720 align:start position:0%
highlight how easy it is to instrument
 

00:04:01.720 --> 00:04:07.069 align:start position:0%
highlight how easy it is to instrument
with<00:04:02.000><c> in</c><00:04:02.159><c> your</c><00:04:02.879><c> code</c><00:04:03.920><c> um</c><00:04:04.920><c> you</c><00:04:05.079><c> can</c><00:04:05.239><c> see</c><00:04:05.640><c> that</c><00:04:06.200><c> um</c>

00:04:07.069 --> 00:04:07.079 align:start position:0%
with in your code um you can see that um
 

00:04:07.079 --> 00:04:09.229 align:start position:0%
with in your code um you can see that um
instrumenting<00:04:07.640><c> we</c><00:04:07.879><c> is</c><00:04:08.040><c> as</c><00:04:08.200><c> simple</c><00:04:08.480><c> as</c><00:04:08.680><c> adding</c>

00:04:09.229 --> 00:04:09.239 align:start position:0%
instrumenting we is as simple as adding
 

00:04:09.239 --> 00:04:12.190 align:start position:0%
instrumenting we is as simple as adding
at<00:04:09.519><c> weave.</c><00:04:10.120><c> op</c><00:04:10.879><c> decorator</c><00:04:11.560><c> to</c><00:04:11.720><c> the</c><00:04:11.879><c> functions</c>

00:04:12.190 --> 00:04:12.200 align:start position:0%
at weave. op decorator to the functions
 

00:04:12.200 --> 00:04:14.309 align:start position:0%
at weave. op decorator to the functions
you<00:04:12.319><c> want</c><00:04:12.480><c> to</c><00:04:12.640><c> trace</c><00:04:13.519><c> in</c><00:04:13.720><c> this</c><00:04:13.920><c> case</c><00:04:14.079><c> we</c><00:04:14.200><c> are</c>

00:04:14.309 --> 00:04:14.319 align:start position:0%
you want to trace in this case we are
 

00:04:14.319 --> 00:04:16.349 align:start position:0%
you want to trace in this case we are
dealing<00:04:14.640><c> with</c><00:04:14.799><c> a</c><00:04:15.000><c> simple</c><00:04:15.760><c> uh</c><00:04:15.959><c> predict</c>

00:04:16.349 --> 00:04:16.359 align:start position:0%
dealing with a simple uh predict
 

00:04:16.359 --> 00:04:18.390 align:start position:0%
dealing with a simple uh predict
function<00:04:17.160><c> but</c><00:04:17.280><c> in</c><00:04:17.440><c> many</c><00:04:17.680><c> cases</c><00:04:17.959><c> you</c><00:04:18.079><c> will</c><00:04:18.239><c> have</c>

00:04:18.390 --> 00:04:18.400 align:start position:0%
function but in many cases you will have
 

00:04:18.400 --> 00:04:20.629 align:start position:0%
function but in many cases you will have
nested<00:04:18.880><c> functions</c><00:04:19.519><c> and</c><00:04:19.759><c> as</c><00:04:19.880><c> you</c><00:04:20.160><c> decorate</c>

00:04:20.629 --> 00:04:20.639 align:start position:0%
nested functions and as you decorate
 

00:04:20.639 --> 00:04:22.310 align:start position:0%
nested functions and as you decorate
each<00:04:20.799><c> of</c><00:04:20.919><c> these</c><00:04:21.079><c> nested</c><00:04:21.440><c> functions</c><00:04:21.840><c> in</c><00:04:22.040><c> weave</c>

00:04:22.310 --> 00:04:22.320 align:start position:0%
each of these nested functions in weave
 

00:04:22.320 --> 00:04:24.590 align:start position:0%
each of these nested functions in weave
you<00:04:22.400><c> will</c><00:04:22.600><c> get</c><00:04:22.720><c> a</c><00:04:22.880><c> detailed</c><00:04:23.280><c> Trace</c><00:04:24.199><c> that</c><00:04:24.320><c> will</c>

00:04:24.590 --> 00:04:24.600 align:start position:0%
you will get a detailed Trace that will
 

00:04:24.600 --> 00:04:26.110 align:start position:0%
you will get a detailed Trace that will
allow<00:04:24.840><c> you</c><00:04:24.919><c> to</c><00:04:25.120><c> debug</c><00:04:25.759><c> where</c><00:04:25.919><c> your</c>

00:04:26.110 --> 00:04:26.120 align:start position:0%
allow you to debug where your
 

00:04:26.120 --> 00:04:28.710 align:start position:0%
allow you to debug where your
application<00:04:26.560><c> is</c><00:04:27.000><c> failing</c><00:04:28.000><c> if</c><00:04:28.120><c> we</c><00:04:28.280><c> go</c><00:04:28.479><c> back</c><00:04:28.600><c> to</c>

00:04:28.710 --> 00:04:28.720 align:start position:0%
application is failing if we go back to
 

00:04:28.720 --> 00:04:30.870 align:start position:0%
application is failing if we go back to
the<00:04:28.919><c> call</c><00:04:29.560><c> and</c><00:04:29.800><c> we</c><00:04:29.880><c> can</c><00:04:30.000><c> see</c><00:04:30.160><c> that</c><00:04:30.280><c> a</c><00:04:30.479><c> question</c>

00:04:30.870 --> 00:04:30.880 align:start position:0%
the call and we can see that a question
 

00:04:30.880 --> 00:04:33.590 align:start position:0%
the call and we can see that a question
that<00:04:31.039><c> a</c><00:04:31.199><c> user</c><00:04:31.600><c> asked</c><00:04:32.280><c> uh</c><00:04:32.520><c> is</c><00:04:32.800><c> actually</c><00:04:33.199><c> not</c>

00:04:33.590 --> 00:04:33.600 align:start position:0%
that a user asked uh is actually not
 

00:04:33.600 --> 00:04:36.150 align:start position:0%
that a user asked uh is actually not
responded<00:04:34.120><c> by</c><00:04:34.240><c> the</c><00:04:34.560><c> model</c><00:04:35.560><c> and</c><00:04:35.960><c> that's</c>

00:04:36.150 --> 00:04:36.160 align:start position:0%
responded by the model and that's
 

00:04:36.160 --> 00:04:37.710 align:start position:0%
responded by the model and that's
something<00:04:36.479><c> that</c><00:04:36.600><c> we</c><00:04:36.680><c> might</c><00:04:36.840><c> want</c><00:04:37.000><c> to</c><00:04:37.160><c> check</c><00:04:37.440><c> in</c>

00:04:37.710 --> 00:04:37.720 align:start position:0%
something that we might want to check in
 

00:04:37.720 --> 00:04:39.310 align:start position:0%
something that we might want to check in
detail<00:04:38.039><c> and</c><00:04:38.440><c> understand</c><00:04:38.639><c> what's</c><00:04:38.840><c> Happening</c>

00:04:39.310 --> 00:04:39.320 align:start position:0%
detail and understand what's Happening
 

00:04:39.320 --> 00:04:41.909 align:start position:0%
detail and understand what's Happening
Here<00:04:40.320><c> we</c><00:04:40.440><c> can</c><00:04:40.560><c> see</c><00:04:41.160><c> one</c><00:04:41.320><c> of</c><00:04:41.440><c> the</c><00:04:41.600><c> input</c>

00:04:41.909 --> 00:04:41.919 align:start position:0%
Here we can see one of the input
 

00:04:41.919 --> 00:04:45.029 align:start position:0%
Here we can see one of the input
parameters<00:04:42.520><c> at</c><00:04:42.800><c> context</c><00:04:43.320><c> is</c><00:04:43.479><c> set</c><00:04:43.680><c> to</c><00:04:44.039><c> false</c>

00:04:45.029 --> 00:04:45.039 align:start position:0%
parameters at context is set to false
 

00:04:45.039 --> 00:04:47.990 align:start position:0%
parameters at context is set to false
and<00:04:45.320><c> if</c><00:04:45.440><c> we</c><00:04:45.800><c> look</c><00:04:45.919><c> at</c><00:04:46.039><c> the</c><00:04:46.199><c> code</c><00:04:46.759><c> again</c><00:04:47.759><c> we</c><00:04:47.880><c> can</c>

00:04:47.990 --> 00:04:48.000 align:start position:0%
and if we look at the code again we can
 

00:04:48.000 --> 00:04:49.710 align:start position:0%
and if we look at the code again we can
see<00:04:48.199><c> by</c><00:04:48.320><c> setting</c><00:04:48.639><c> this</c><00:04:48.800><c> parameter</c><00:04:49.199><c> to</c><00:04:49.360><c> false</c>

00:04:49.710 --> 00:04:49.720 align:start position:0%
see by setting this parameter to false
 

00:04:49.720 --> 00:04:52.790 align:start position:0%
see by setting this parameter to false
we're<00:04:50.039><c> actually</c><00:04:50.440><c> not</c><00:04:50.880><c> benefiting</c><00:04:51.880><c> from</c><00:04:52.199><c> any</c>

00:04:52.790 --> 00:04:52.800 align:start position:0%
we're actually not benefiting from any
 

00:04:52.800 --> 00:04:56.110 align:start position:0%
we're actually not benefiting from any
uh<00:04:53.039><c> context</c><00:04:53.639><c> document</c><00:04:54.639><c> that</c><00:04:55.000><c> uh</c><00:04:55.360><c> might</c><00:04:55.840><c> uh</c>

00:04:56.110 --> 00:04:56.120 align:start position:0%
uh context document that uh might uh
 

00:04:56.120 --> 00:04:57.990 align:start position:0%
uh context document that uh might uh
might<00:04:56.320><c> need</c><00:04:56.520><c> to</c><00:04:56.639><c> be</c><00:04:56.880><c> added</c><00:04:57.240><c> to</c><00:04:57.440><c> the</c><00:04:57.680><c> to</c><00:04:57.840><c> the</c>

00:04:57.990 --> 00:04:58.000 align:start position:0%
might need to be added to the to the
 

00:04:58.000 --> 00:04:59.790 align:start position:0%
might need to be added to the to the
prompt<00:04:58.360><c> so</c><00:04:58.520><c> in</c><00:04:58.639><c> this</c><00:04:58.800><c> case</c><00:04:59.000><c> the</c><00:04:59.080><c> model</c><00:04:59.360><c> does</c>

00:04:59.790 --> 00:04:59.800 align:start position:0%
prompt so in this case the model does
 

00:04:59.800 --> 00:05:02.749 align:start position:0%
prompt so in this case the model does
not<00:05:00.360><c> have</c><00:05:00.639><c> any</c><00:05:01.000><c> insight</c><00:05:01.639><c> into</c><00:05:02.160><c> how</c><00:05:02.280><c> to</c><00:05:02.479><c> answer</c>

00:05:02.749 --> 00:05:02.759 align:start position:0%
not have any insight into how to answer
 

00:05:02.759 --> 00:05:04.870 align:start position:0%
not have any insight into how to answer
the<00:05:02.880><c> user</c><00:05:03.240><c> question</c><00:05:04.080><c> and</c><00:05:04.280><c> it</c><00:05:04.479><c> properly</c>

00:05:04.870 --> 00:05:04.880 align:start position:0%
the user question and it properly
 

00:05:04.880 --> 00:05:07.390 align:start position:0%
the user question and it properly
responds<00:05:05.280><c> I</c><00:05:05.440><c> didn't</c><00:05:05.720><c> no</c><00:05:06.720><c> if</c><00:05:06.800><c> you</c><00:05:06.960><c> look</c><00:05:07.120><c> at</c><00:05:07.280><c> one</c>

00:05:07.390 --> 00:05:07.400 align:start position:0%
responds I didn't no if you look at one
 

00:05:07.400 --> 00:05:10.390 align:start position:0%
responds I didn't no if you look at one
of<00:05:07.520><c> the</c><00:05:07.759><c> other</c><00:05:08.160><c> instances</c><00:05:09.160><c> where</c><00:05:09.600><c> uh</c><00:05:09.720><c> the</c><00:05:09.919><c> same</c>

00:05:10.390 --> 00:05:10.400 align:start position:0%
of the other instances where uh the same
 

00:05:10.400 --> 00:05:12.950 align:start position:0%
of the other instances where uh the same
uh<00:05:10.600><c> method</c><00:05:11.000><c> is</c><00:05:11.280><c> called</c><00:05:12.280><c> uh</c><00:05:12.360><c> with</c><00:05:12.520><c> the</c><00:05:12.680><c> same</c>

00:05:12.950 --> 00:05:12.960 align:start position:0%
uh method is called uh with the same
 

00:05:12.960 --> 00:05:15.830 align:start position:0%
uh method is called uh with the same
question<00:05:13.800><c> but</c><00:05:14.039><c> now</c><00:05:14.680><c> uh</c><00:05:14.800><c> our</c><00:05:15.080><c> context</c><00:05:15.440><c> argument</c>

00:05:15.830 --> 00:05:15.840 align:start position:0%
question but now uh our context argument
 

00:05:15.840 --> 00:05:19.110 align:start position:0%
question but now uh our context argument
is<00:05:15.960><c> set</c><00:05:16.440><c> to</c><00:05:16.680><c> true</c><00:05:17.400><c> we</c><00:05:17.520><c> can</c><00:05:17.720><c> now</c><00:05:17.919><c> see</c><00:05:18.360><c> that</c><00:05:18.880><c> um</c>

00:05:19.110 --> 00:05:19.120 align:start position:0%
is set to true we can now see that um
 

00:05:19.120 --> 00:05:21.590 align:start position:0%
is set to true we can now see that um
the<00:05:19.240><c> model</c><00:05:19.600><c> was</c><00:05:19.800><c> able</c><00:05:20.120><c> to</c><00:05:20.360><c> properly</c><00:05:21.240><c> respond</c>

00:05:21.590 --> 00:05:21.600 align:start position:0%
the model was able to properly respond
 

00:05:21.600 --> 00:05:24.510 align:start position:0%
the model was able to properly respond
to<00:05:21.759><c> this</c>

00:05:24.510 --> 00:05:24.520 align:start position:0%
 
 

00:05:24.520 --> 00:05:27.550 align:start position:0%
 
question<00:05:25.520><c> in</c><00:05:25.919><c> summary</c><00:05:26.520><c> adding</c><00:05:26.880><c> weave</c><00:05:27.160><c> to</c><00:05:27.319><c> your</c>

00:05:27.550 --> 00:05:27.560 align:start position:0%
question in summary adding weave to your
 

00:05:27.560 --> 00:05:30.430 align:start position:0%
question in summary adding weave to your
llm<00:05:28.039><c> application</c><00:05:28.720><c> is</c><00:05:29.120><c> super</c><00:05:29.880><c> forward</c><00:05:30.319><c> by</c>

00:05:30.430 --> 00:05:30.440 align:start position:0%
llm application is super forward by
 

00:05:30.440 --> 00:05:33.950 align:start position:0%
llm application is super forward by
using<00:05:30.840><c> the</c><00:05:31.280><c> at</c><00:05:31.560><c> we.</c><00:05:32.000><c> op</c><00:05:32.240><c> decorator</c><00:05:33.199><c> you</c><00:05:33.479><c> get</c>

00:05:33.950 --> 00:05:33.960 align:start position:0%
using the at we. op decorator you get
 

00:05:33.960 --> 00:05:36.950 align:start position:0%
using the at we. op decorator you get
powerful<00:05:34.840><c> observability</c><00:05:35.840><c> you</c><00:05:35.960><c> can</c><00:05:36.240><c> save</c><00:05:36.720><c> uh</c>

00:05:36.950 --> 00:05:36.960 align:start position:0%
powerful observability you can save uh
 

00:05:36.960 --> 00:05:39.430 align:start position:0%
powerful observability you can save uh
data<00:05:37.240><c> sets</c><00:05:37.759><c> you</c><00:05:37.880><c> can</c><00:05:38.080><c> save</c><00:05:38.600><c> models</c><00:05:39.120><c> you</c><00:05:39.240><c> can</c>

00:05:39.430 --> 00:05:39.440 align:start position:0%
data sets you can save models you can
 

00:05:39.440 --> 00:05:42.150 align:start position:0%
data sets you can save models you can
save<00:05:40.160><c> evaluations</c><00:05:41.160><c> and</c><00:05:41.400><c> that</c><00:05:41.560><c> allows</c><00:05:41.840><c> you</c><00:05:41.960><c> to</c>

00:05:42.150 --> 00:05:42.160 align:start position:0%
save evaluations and that allows you to
 

00:05:42.160 --> 00:05:45.110 align:start position:0%
save evaluations and that allows you to
build<00:05:42.560><c> llm</c><00:05:43.240><c> applications</c><00:05:44.240><c> with</c><00:05:44.479><c> a</c><00:05:44.639><c> better</c>

00:05:45.110 --> 00:05:45.120 align:start position:0%
build llm applications with a better
 

00:05:45.120 --> 00:05:47.390 align:start position:0%
build llm applications with a better
productivity<00:05:46.120><c> and</c><00:05:46.319><c> deploy</c><00:05:46.759><c> them</c><00:05:47.039><c> with</c>

00:05:47.390 --> 00:05:47.400 align:start position:0%
productivity and deploy them with
 

00:05:47.400 --> 00:05:49.590 align:start position:0%
productivity and deploy them with
confidence<00:05:48.280><c> that</c><00:05:48.400><c> you're</c><00:05:48.720><c> actually</c><00:05:49.039><c> meeting</c>

00:05:49.590 --> 00:05:49.600 align:start position:0%
confidence that you're actually meeting
 

00:05:49.600 --> 00:05:52.110 align:start position:0%
confidence that you're actually meeting
your<00:05:49.800><c> user</c><00:05:50.160><c> requirements</c><00:05:50.840><c> and</c><00:05:51.000><c> solving</c><00:05:52.000><c> uh</c>

00:05:52.110 --> 00:05:52.120 align:start position:0%
your user requirements and solving uh
 

00:05:52.120 --> 00:05:55.840 align:start position:0%
your user requirements and solving uh
the<00:05:52.280><c> right</c><00:05:52.479><c> business</c><00:05:52.840><c> problems</c>

