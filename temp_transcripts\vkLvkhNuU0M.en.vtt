WEBVTT
Kind: captions
Language: en

00:00:00.640 --> 00:00:02.230 align:start position:0%
 
hey<00:00:00.799><c> everyone</c><00:00:01.120><c> it's</c><00:00:01.280><c> SRE</c><00:00:01.719><c> and</c><00:00:01.839><c> today</c><00:00:02.080><c> let's</c>

00:00:02.230 --> 00:00:02.240 align:start position:0%
hey everyone it's <PERSON><PERSON> and today let's
 

00:00:02.240 --> 00:00:05.510 align:start position:0%
hey everyone it's SRE and today let's
run<00:00:02.480><c> the</c><00:00:02.600><c> llm</c><00:00:02.960><c> OS</c><00:00:03.240><c> on</c><00:00:03.560><c> AWS</c><00:00:04.560><c> the</c><00:00:04.680><c> llm</c><00:00:05.359><c> was</c>

00:00:05.510 --> 00:00:05.520 align:start position:0%
run the llm OS on AWS the llm was
 

00:00:05.520 --> 00:00:08.070 align:start position:0%
run the llm OS on AWS the llm was
proposed<00:00:05.879><c> by</c><00:00:06.000><c> the</c><00:00:06.120><c> Magnificent</c><00:00:06.759><c> Andre</c><00:00:07.160><c> carpy</c>

00:00:08.070 --> 00:00:08.080 align:start position:0%
proposed by the Magnificent Andre carpy
 

00:00:08.080 --> 00:00:09.709 align:start position:0%
proposed by the Magnificent Andre carpy
where<00:00:08.240><c> he</c><00:00:08.360><c> said</c><00:00:08.559><c> that</c><00:00:08.719><c> we</c><00:00:08.840><c> should</c><00:00:09.160><c> think</c><00:00:09.440><c> about</c>

00:00:09.709 --> 00:00:09.719 align:start position:0%
where he said that we should think about
 

00:00:09.719 --> 00:00:12.589 align:start position:0%
where he said that we should think about
llms<00:00:10.360><c> as</c><00:00:10.480><c> the</c><00:00:10.679><c> CPU</c><00:00:11.280><c> or</c><00:00:11.440><c> the</c><00:00:11.599><c> kernel</c><00:00:12.120><c> process</c><00:00:12.480><c> of</c>

00:00:12.589 --> 00:00:12.599 align:start position:0%
llms as the CPU or the kernel process of
 

00:00:12.599 --> 00:00:15.509 align:start position:0%
llms as the CPU or the kernel process of
an<00:00:12.840><c> emerging</c><00:00:13.480><c> operating</c><00:00:13.960><c> system</c><00:00:14.920><c> this</c><00:00:15.120><c> CPU</c>

00:00:15.509 --> 00:00:15.519 align:start position:0%
an emerging operating system this CPU
 

00:00:15.519 --> 00:00:17.790 align:start position:0%
an emerging operating system this CPU
which<00:00:15.639><c> is</c><00:00:15.759><c> the</c><00:00:15.839><c> llm</c><00:00:16.680><c> can</c><00:00:16.920><c> coordinate</c><00:00:17.480><c> many</c>

00:00:17.790 --> 00:00:17.800 align:start position:0%
which is the llm can coordinate many
 

00:00:17.800 --> 00:00:20.390 align:start position:0%
which is the llm can coordinate many
different<00:00:18.199><c> resources</c><00:00:19.160><c> to</c><00:00:19.359><c> solve</c><00:00:19.840><c> complex</c>

00:00:20.390 --> 00:00:20.400 align:start position:0%
different resources to solve complex
 

00:00:20.400 --> 00:00:22.990 align:start position:0%
different resources to solve complex
problems<00:00:20.840><c> using</c><00:00:21.199><c> natural</c><00:00:21.640><c> language</c><00:00:22.560><c> today</c>

00:00:22.990 --> 00:00:23.000 align:start position:0%
problems using natural language today
 

00:00:23.000 --> 00:00:25.029 align:start position:0%
problems using natural language today
we'll<00:00:23.279><c> Implement</c><00:00:23.640><c> a</c><00:00:23.760><c> version</c><00:00:24.199><c> of</c><00:00:24.400><c> it</c><00:00:24.800><c> with</c>

00:00:25.029 --> 00:00:25.039 align:start position:0%
we'll Implement a version of it with
 

00:00:25.039 --> 00:00:28.710 align:start position:0%
we'll Implement a version of it with
gp40<00:00:26.000><c> as</c><00:00:26.160><c> the</c><00:00:26.240><c> LM</c><00:00:27.240><c> we'll</c><00:00:27.519><c> give</c><00:00:27.640><c> it</c><00:00:27.960><c> access</c><00:00:28.320><c> to</c>

00:00:28.710 --> 00:00:28.720 align:start position:0%
gp40 as the LM we'll give it access to
 

00:00:28.720 --> 00:00:30.509 align:start position:0%
gp40 as the LM we'll give it access to
software<00:00:29.160><c> 1.0</c><00:00:29.640><c> tools</c>

00:00:30.509 --> 00:00:30.519 align:start position:0%
software 1.0 tools
 

00:00:30.519 --> 00:00:32.470 align:start position:0%
software 1.0 tools
we'll<00:00:30.759><c> give</c><00:00:30.920><c> it</c><00:00:31.160><c> a</c><00:00:31.400><c> file</c><00:00:31.759><c> system</c><00:00:32.079><c> where</c><00:00:32.200><c> it</c><00:00:32.320><c> can</c>

00:00:32.470 --> 00:00:32.480 align:start position:0%
we'll give it a file system where it can
 

00:00:32.480 --> 00:00:34.830 align:start position:0%
we'll give it a file system where it can
store<00:00:32.759><c> its</c><00:00:32.960><c> memory</c><00:00:33.280><c> in</c><00:00:33.360><c> a</c><00:00:33.520><c> postest</c><00:00:34.040><c> database</c>

00:00:34.830 --> 00:00:34.840 align:start position:0%
store its memory in a postest database
 

00:00:34.840 --> 00:00:37.350 align:start position:0%
store its memory in a postest database
or<00:00:35.120><c> knowledge</c><00:00:35.600><c> in</c><00:00:35.840><c> PG</c><00:00:36.160><c> Vector</c><00:00:36.920><c> we'll</c><00:00:37.120><c> give</c><00:00:37.239><c> it</c>

00:00:37.350 --> 00:00:37.360 align:start position:0%
or knowledge in PG Vector we'll give it
 

00:00:37.360 --> 00:00:39.310 align:start position:0%
or knowledge in PG Vector we'll give it
the<00:00:37.480><c> ability</c><00:00:37.760><c> to</c><00:00:37.960><c> browse</c><00:00:38.360><c> the</c><00:00:38.600><c> internet</c><00:00:38.960><c> for</c>

00:00:39.310 --> 00:00:39.320 align:start position:0%
the ability to browse the internet for
 

00:00:39.320 --> 00:00:41.630 align:start position:0%
the ability to browse the internet for
information<00:00:40.200><c> and</c><00:00:40.480><c> the</c><00:00:40.600><c> ability</c><00:00:40.920><c> to</c><00:00:41.120><c> delegate</c>

00:00:41.630 --> 00:00:41.640 align:start position:0%
information and the ability to delegate
 

00:00:41.640 --> 00:00:44.310 align:start position:0%
information and the ability to delegate
task<00:00:42.399><c> to</c><00:00:42.760><c> other</c><00:00:43.160><c> assistants</c><00:00:43.760><c> built</c><00:00:44.079><c> for</c>

00:00:44.310 --> 00:00:44.320 align:start position:0%
task to other assistants built for
 

00:00:44.320 --> 00:00:47.310 align:start position:0%
task to other assistants built for
specific<00:00:44.760><c> use</c><00:00:45.120><c> cases</c><00:00:45.719><c> so</c><00:00:46.000><c> let's</c><00:00:46.280><c> get</c><00:00:46.440><c> started</c>

00:00:47.310 --> 00:00:47.320 align:start position:0%
specific use cases so let's get started
 

00:00:47.320 --> 00:00:49.430 align:start position:0%
specific use cases so let's get started
the<00:00:47.600><c> instructions</c><00:00:48.280><c> for</c><00:00:48.559><c> this</c><00:00:48.760><c> tutorial</c><00:00:49.280><c> are</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
the instructions for this tutorial are
 

00:00:49.440 --> 00:00:51.869 align:start position:0%
the instructions for this tutorial are
going<00:00:49.640><c> to</c><00:00:49.760><c> be</c><00:00:49.879><c> on</c><00:00:49.960><c> the</c><00:00:50.079><c> FI</c><00:00:50.320><c> data</c><00:00:50.879><c> documentation</c>

00:00:51.869 --> 00:00:51.879 align:start position:0%
going to be on the FI data documentation
 

00:00:51.879 --> 00:00:54.790 align:start position:0%
going to be on the FI data documentation
we<00:00:52.199><c> provided</c><00:00:52.640><c> the</c><00:00:52.760><c> llos</c><00:00:53.480><c> as</c><00:00:53.600><c> a</c><00:00:53.760><c> template</c><00:00:54.640><c> that</c>

00:00:54.790 --> 00:00:54.800 align:start position:0%
we provided the llos as a template that
 

00:00:54.800 --> 00:00:57.630 align:start position:0%
we provided the llos as a template that
you<00:00:54.960><c> can</c><00:00:55.399><c> pull</c><00:00:55.760><c> directly</c><00:00:56.280><c> from</c><00:00:56.640><c> GitHub</c><00:00:57.280><c> and</c>

00:00:57.630 --> 00:00:57.640 align:start position:0%
you can pull directly from GitHub and
 

00:00:57.640 --> 00:01:00.750 align:start position:0%
you can pull directly from GitHub and
run<00:00:58.160><c> locally</c><00:00:58.719><c> using</c><00:00:59.160><c> docker</c><00:01:00.160><c> or</c><00:01:00.480><c> in</c>

00:01:00.750 --> 00:01:00.760 align:start position:0%
run locally using docker or in
 

00:01:00.760 --> 00:01:03.790 align:start position:0%
run locally using docker or in
production<00:01:01.120><c> on</c><00:01:01.480><c> AWS</c><00:01:02.480><c> so</c><00:01:02.800><c> go</c><00:01:02.960><c> to</c><00:01:03.120><c> the</c><00:01:03.239><c> F</c>

00:01:03.790 --> 00:01:03.800 align:start position:0%
production on AWS so go to the F
 

00:01:03.800 --> 00:01:06.390 align:start position:0%
production on AWS so go to the F
documentation<00:01:04.600><c> under</c><00:01:04.920><c> templates</c><00:01:05.519><c> under</c><00:01:05.880><c> llm</c>

00:01:06.390 --> 00:01:06.400 align:start position:0%
documentation under templates under llm
 

00:01:06.400 --> 00:01:09.070 align:start position:0%
documentation under templates under llm
OS<00:01:07.240><c> and</c><00:01:07.360><c> you'll</c><00:01:07.560><c> find</c><00:01:07.840><c> all</c><00:01:08.119><c> the</c><00:01:08.360><c> instructions</c>

00:01:09.070 --> 00:01:09.080 align:start position:0%
OS and you'll find all the instructions
 

00:01:09.080 --> 00:01:11.950 align:start position:0%
OS and you'll find all the instructions
to<00:01:09.280><c> follow</c><00:01:09.640><c> along</c><00:01:10.360><c> the</c><00:01:10.560><c> tutorial</c><00:01:11.119><c> today</c><00:01:11.759><c> so</c>

00:01:11.950 --> 00:01:11.960 align:start position:0%
to follow along the tutorial today so
 

00:01:11.960 --> 00:01:14.630 align:start position:0%
to follow along the tutorial today so
let's<00:01:12.240><c> get</c><00:01:12.439><c> started</c><00:01:13.200><c> let's</c><00:01:13.439><c> pull</c><00:01:13.680><c> up</c><00:01:13.960><c> our</c>

00:01:14.630 --> 00:01:14.640 align:start position:0%
let's get started let's pull up our
 

00:01:14.640 --> 00:01:15.870 align:start position:0%
let's get started let's pull up our
terminal<00:01:15.119><c> of</c>

00:01:15.870 --> 00:01:15.880 align:start position:0%
terminal of
 

00:01:15.880 --> 00:01:19.069 align:start position:0%
terminal of
choice<00:01:16.880><c> and</c><00:01:17.560><c> pull</c><00:01:17.840><c> the</c><00:01:18.000><c> documentation</c><00:01:18.520><c> to</c><00:01:18.680><c> the</c>

00:01:19.069 --> 00:01:19.079 align:start position:0%
choice and pull the documentation to the
 

00:01:19.079 --> 00:01:22.190 align:start position:0%
choice and pull the documentation to the
right<00:01:20.079><c> we'll</c><00:01:20.439><c> first</c><00:01:21.040><c> create</c><00:01:21.560><c> a</c><00:01:21.840><c> python</c>

00:01:22.190 --> 00:01:22.200 align:start position:0%
right we'll first create a python
 

00:01:22.200 --> 00:01:24.630 align:start position:0%
right we'll first create a python
virtual<00:01:22.600><c> environment</c><00:01:23.079><c> so</c><00:01:23.280><c> our</c><00:01:23.640><c> dependencies</c>

00:01:24.630 --> 00:01:24.640 align:start position:0%
virtual environment so our dependencies
 

00:01:24.640 --> 00:01:25.670 align:start position:0%
virtual environment so our dependencies
stay

00:01:25.670 --> 00:01:25.680 align:start position:0%
stay
 

00:01:25.680 --> 00:01:28.550 align:start position:0%
stay
isolated<00:01:26.680><c> then</c><00:01:26.880><c> we'll</c><00:01:27.200><c> install</c><00:01:27.640><c> fi</c><00:01:28.000><c> data</c><00:01:28.400><c> with</c>

00:01:28.550 --> 00:01:28.560 align:start position:0%
isolated then we'll install fi data with
 

00:01:28.560 --> 00:01:32.030 align:start position:0%
isolated then we'll install fi data with
the<00:01:29.040><c> optional</c><00:01:29.560><c> AG</c><00:01:30.000><c> WS</c><00:01:30.600><c> libraries</c><00:01:31.600><c> we'll</c><00:01:31.840><c> also</c>

00:01:32.030 --> 00:01:32.040 align:start position:0%
the optional AG WS libraries we'll also
 

00:01:32.040 --> 00:01:35.350 align:start position:0%
the optional AG WS libraries we'll also
install<00:01:32.399><c> Docker</c><00:01:32.799><c> desktop</c><00:01:33.600><c> if</c><00:01:33.759><c> you</c><00:01:34.360><c> haven't</c>

00:01:35.350 --> 00:01:35.360 align:start position:0%
install Docker desktop if you haven't
 

00:01:35.360 --> 00:01:37.389 align:start position:0%
install Docker desktop if you haven't
then<00:01:35.520><c> we'll</c><00:01:35.920><c> create</c><00:01:36.360><c> our</c><00:01:36.680><c> codebase</c><00:01:37.200><c> we'll</c>

00:01:37.389 --> 00:01:37.399 align:start position:0%
then we'll create our codebase we'll
 

00:01:37.399 --> 00:01:40.190 align:start position:0%
then we'll create our codebase we'll
create<00:01:37.680><c> the</c><00:01:37.880><c> lmos</c><00:01:38.439><c> code</c><00:01:38.720><c> base</c><00:01:39.520><c> so</c><00:01:39.759><c> we'll</c><00:01:39.960><c> run</c>

00:01:40.190 --> 00:01:40.200 align:start position:0%
create the lmos code base so we'll run
 

00:01:40.200 --> 00:01:43.270 align:start position:0%
create the lmos code base so we'll run
the<00:01:40.360><c> five</c><00:01:40.759><c> workspace</c><00:01:41.560><c> create</c><00:01:42.079><c> command</c><00:01:43.079><c> and</c>

00:01:43.270 --> 00:01:43.280 align:start position:0%
the five workspace create command and
 

00:01:43.280 --> 00:01:46.469 align:start position:0%
the five workspace create command and
let's<00:01:43.520><c> also</c><00:01:43.759><c> pull</c><00:01:43.960><c> up</c><00:01:44.280><c> our</c><00:01:45.040><c> um</c><00:01:45.880><c> code</c><00:01:46.040><c> editor</c><00:01:46.360><c> on</c>

00:01:46.469 --> 00:01:46.479 align:start position:0%
let's also pull up our um code editor on
 

00:01:46.479 --> 00:01:48.670 align:start position:0%
let's also pull up our um code editor on
the<00:01:46.680><c> right</c><00:01:47.159><c> so</c><00:01:47.320><c> we'll</c><00:01:47.520><c> run</c><00:01:47.759><c> F</c><00:01:48.079><c> workspace</c>

00:01:48.670 --> 00:01:48.680 align:start position:0%
the right so we'll run F workspace
 

00:01:48.680 --> 00:01:53.230 align:start position:0%
the right so we'll run F workspace
create<00:01:49.680><c> which</c><00:01:49.799><c> will</c><00:01:50.119><c> give</c><00:01:50.280><c> us</c><00:01:50.439><c> an</c><00:01:50.840><c> option</c><00:01:52.240><c> to</c>

00:01:53.230 --> 00:01:53.240 align:start position:0%
create which will give us an option to
 

00:01:53.240 --> 00:01:55.789 align:start position:0%
create which will give us an option to
pull<00:01:53.600><c> from</c><00:01:54.280><c> a</c><00:01:54.520><c> starter</c><00:01:55.000><c> template</c><00:01:55.360><c> to</c><00:01:55.479><c> clone</c><00:01:55.719><c> a</c>

00:01:55.789 --> 00:01:55.799 align:start position:0%
pull from a starter template to clone a
 

00:01:55.799 --> 00:01:58.350 align:start position:0%
pull from a starter template to clone a
starter<00:01:56.159><c> template</c><00:01:56.520><c> we</c><00:01:56.640><c> can</c><00:01:56.880><c> clone</c><00:01:57.240><c> an</c><00:01:57.600><c> AI</c><00:01:58.000><c> app</c>

00:01:58.350 --> 00:01:58.360 align:start position:0%
starter template we can clone an AI app
 

00:01:58.360 --> 00:02:00.789 align:start position:0%
starter template we can clone an AI app
an<00:01:58.560><c> API</c><00:01:59.039><c> a</c><00:01:59.159><c> Jango</c><00:01:59.520><c> application</c>

00:02:00.789 --> 00:02:00.799 align:start position:0%
an API a Jango application
 

00:02:00.799 --> 00:02:03.910 align:start position:0%
an API a Jango application
or<00:02:01.119><c> today</c><00:02:01.399><c> we'll</c><00:02:01.640><c> clone</c><00:02:01.920><c> the</c><00:02:02.479><c> lmos</c><00:02:03.479><c> then</c><00:02:03.680><c> we'll</c>

00:02:03.910 --> 00:02:03.920 align:start position:0%
or today we'll clone the lmos then we'll
 

00:02:03.920 --> 00:02:05.550 align:start position:0%
or today we'll clone the lmos then we'll
give<00:02:04.039><c> our</c><00:02:04.240><c> workspace</c><00:02:04.680><c> a</c><00:02:04.840><c> name</c><00:02:05.159><c> I'll</c><00:02:05.399><c> just</c>

00:02:05.550 --> 00:02:05.560 align:start position:0%
give our workspace a name I'll just
 

00:02:05.560 --> 00:02:08.550 align:start position:0%
give our workspace a name I'll just
simply<00:02:05.880><c> call</c><00:02:06.039><c> it</c><00:02:06.240><c> lmos</c><00:02:06.960><c> which</c><00:02:07.039><c> is</c><00:02:07.159><c> the</c><00:02:07.560><c> default</c>

00:02:08.550 --> 00:02:08.560 align:start position:0%
simply call it lmos which is the default
 

00:02:08.560 --> 00:02:11.150 align:start position:0%
simply call it lmos which is the default
and<00:02:08.720><c> you'll</c><00:02:08.920><c> see</c><00:02:09.200><c> It'll</c><00:02:09.679><c> create</c><00:02:10.239><c> a</c><00:02:10.520><c> codebase</c>

00:02:11.150 --> 00:02:11.160 align:start position:0%
and you'll see It'll create a codebase
 

00:02:11.160 --> 00:02:14.589 align:start position:0%
and you'll see It'll create a codebase
for<00:02:11.440><c> us</c><00:02:12.400><c> with</c><00:02:13.400><c> all</c><00:02:13.640><c> the</c><00:02:13.879><c> different</c><00:02:14.239><c> pieces</c>

00:02:14.589 --> 00:02:14.599 align:start position:0%
for us with all the different pieces
 

00:02:14.599 --> 00:02:18.190 align:start position:0%
for us with all the different pieces
we're<00:02:14.760><c> going</c><00:02:14.840><c> to</c><00:02:15.080><c> need</c><00:02:15.239><c> in</c><00:02:15.480><c> today</c><00:02:16.200><c> already</c>

00:02:18.190 --> 00:02:18.200 align:start position:0%
we're going to need in today already
 

00:02:18.200 --> 00:02:21.030 align:start position:0%
we're going to need in today already
preconfigured<00:02:19.200><c> following</c><00:02:19.720><c> along</c><00:02:20.280><c> you</c><00:02:20.440><c> can</c>

00:02:21.030 --> 00:02:21.040 align:start position:0%
preconfigured following along you can
 

00:02:21.040 --> 00:02:23.390 align:start position:0%
preconfigured following along you can
see<00:02:21.440><c> what</c><00:02:21.680><c> each</c><00:02:21.879><c> of</c><00:02:22.080><c> these</c><00:02:22.360><c> folders</c><00:02:22.840><c> stand</c><00:02:23.120><c> for</c>

00:02:23.390 --> 00:02:23.400 align:start position:0%
see what each of these folders stand for
 

00:02:23.400 --> 00:02:25.949 align:start position:0%
see what each of these folders stand for
there's<00:02:23.560><c> a</c><00:02:23.720><c> streamlet</c><00:02:24.239><c> application</c><00:02:25.040><c> fast</c><00:02:25.360><c> API</c>

00:02:25.949 --> 00:02:25.959 align:start position:0%
there's a streamlet application fast API
 

00:02:25.959 --> 00:02:28.630 align:start position:0%
there's a streamlet application fast API
routes<00:02:26.959><c> uh</c><00:02:27.120><c> a</c><00:02:27.200><c> folder</c><00:02:27.480><c> for</c><00:02:27.640><c> database</c><00:02:28.080><c> settings</c>

00:02:28.630 --> 00:02:28.640 align:start position:0%
routes uh a folder for database settings
 

00:02:28.640 --> 00:02:31.070 align:start position:0%
routes uh a folder for database settings
a<00:02:28.800><c> Docker</c><00:02:29.160><c> file</c><00:02:29.400><c> to</c><00:02:29.879><c> containerize</c><00:02:30.560><c> everything</c>

00:02:31.070 --> 00:02:31.080 align:start position:0%
a Docker file to containerize everything
 

00:02:31.080 --> 00:02:33.949 align:start position:0%
a Docker file to containerize everything
and<00:02:31.400><c> run</c><00:02:31.599><c> it</c><00:02:31.800><c> locally</c><00:02:32.160><c> using</c><00:02:32.400><c> Docker</c><00:02:32.879><c> or</c><00:02:33.080><c> on</c>

00:02:33.949 --> 00:02:33.959 align:start position:0%
and run it locally using Docker or on
 

00:02:33.959 --> 00:02:38.110 align:start position:0%
and run it locally using Docker or on
ECS<00:02:34.959><c> uh</c><00:02:35.160><c> then</c><00:02:35.519><c> also</c><00:02:36.080><c> a</c><00:02:36.319><c> workspace</c><00:02:37.120><c> folder</c>

00:02:38.110 --> 00:02:38.120 align:start position:0%
ECS uh then also a workspace folder
 

00:02:38.120 --> 00:02:40.790 align:start position:0%
ECS uh then also a workspace folder
where<00:02:38.280><c> we've</c><00:02:38.599><c> defined</c><00:02:39.280><c> all</c><00:02:39.440><c> of</c><00:02:39.560><c> the</c><00:02:39.800><c> resources</c>

00:02:40.790 --> 00:02:40.800 align:start position:0%
where we've defined all of the resources
 

00:02:40.800 --> 00:02:43.830 align:start position:0%
where we've defined all of the resources
resources<00:02:41.319><c> we're</c><00:02:41.519><c> going</c><00:02:41.680><c> to</c><00:02:41.920><c> need</c><00:02:42.440><c> as</c><00:02:42.840><c> code</c><00:02:43.640><c> so</c>

00:02:43.830 --> 00:02:43.840 align:start position:0%
resources we're going to need as code so
 

00:02:43.840 --> 00:02:45.309 align:start position:0%
resources we're going to need as code so
all<00:02:44.000><c> of</c><00:02:44.080><c> the</c><00:02:44.200><c> resources</c><00:02:44.599><c> are</c><00:02:44.800><c> defined</c><00:02:45.120><c> as</c>

00:02:45.309 --> 00:02:45.319 align:start position:0%
all of the resources are defined as
 

00:02:45.319 --> 00:02:48.110 align:start position:0%
all of the resources are defined as
python<00:02:46.000><c> objects</c><00:02:47.000><c> so</c><00:02:47.239><c> this</c><00:02:47.360><c> way</c><00:02:47.519><c> the</c><00:02:47.680><c> next</c><00:02:47.920><c> step</c>

00:02:48.110 --> 00:02:48.120 align:start position:0%
python objects so this way the next step
 

00:02:48.120 --> 00:02:50.630 align:start position:0%
python objects so this way the next step
for<00:02:48.239><c> us</c><00:02:48.400><c> to</c><00:02:48.560><c> run</c><00:02:48.760><c> the</c><00:02:48.879><c> llm</c><00:02:49.280><c> OS</c><00:02:50.040><c> we'll</c><00:02:50.319><c> export</c>

00:02:50.630 --> 00:02:50.640 align:start position:0%
for us to run the llm OS we'll export
 

00:02:50.640 --> 00:02:52.990 align:start position:0%
for us to run the llm OS we'll export
our<00:02:50.800><c> open</c><00:02:51.360><c> API</c><00:02:51.760><c> key</c><00:02:52.080><c> because</c><00:02:52.280><c> we'll</c><00:02:52.480><c> use</c><00:02:52.640><c> GPD</c>

00:02:52.990 --> 00:02:53.000 align:start position:0%
our open API key because we'll use GPD
 

00:02:53.000 --> 00:02:56.149 align:start position:0%
our open API key because we'll use GPD
40<00:02:53.319><c> as</c><00:02:53.440><c> the</c><00:02:53.519><c> llm</c><00:02:54.480><c> we'll</c><00:02:54.720><c> also</c><00:02:54.920><c> be</c><00:02:55.040><c> using</c><00:02:55.519><c> EXA</c>

00:02:56.149 --> 00:02:56.159 align:start position:0%
40 as the llm we'll also be using EXA
 

00:02:56.159 --> 00:02:57.750 align:start position:0%
40 as the llm we'll also be using EXA
for<00:02:56.440><c> the</c><00:02:56.599><c> research</c><00:02:57.040><c> assistant</c><00:02:57.440><c> so</c><00:02:57.560><c> if</c><00:02:57.680><c> you</c>

00:02:57.750 --> 00:02:57.760 align:start position:0%
for the research assistant so if you
 

00:02:57.760 --> 00:02:59.830 align:start position:0%
for the research assistant so if you
want<00:02:58.000><c> research</c><00:02:58.519><c> capabilities</c><00:02:59.519><c> uh</c><00:02:59.640><c> please</c>

00:02:59.830 --> 00:02:59.840 align:start position:0%
want research capabilities uh please
 

00:02:59.840 --> 00:03:02.309 align:start position:0%
want research capabilities uh please
please<00:03:00.080><c> get</c><00:03:00.200><c> your</c><00:03:00.360><c> XI</c><00:03:00.680><c> API</c><00:03:01.080><c> key</c><00:03:01.239><c> and</c><00:03:01.680><c> uh</c><00:03:01.879><c> export</c>

00:03:02.309 --> 00:03:02.319 align:start position:0%
please get your XI API key and uh export
 

00:03:02.319 --> 00:03:05.509 align:start position:0%
please get your XI API key and uh export
that<00:03:02.599><c> we</c><00:03:02.760><c> love</c><00:03:03.000><c> using</c><00:03:03.560><c> XA</c><00:03:04.560><c> uh</c><00:03:04.799><c> then</c><00:03:05.360><c> we're</c>

00:03:05.509 --> 00:03:05.519 align:start position:0%
that we love using XA uh then we're
 

00:03:05.519 --> 00:03:07.990 align:start position:0%
that we love using XA uh then we're
going<00:03:05.640><c> to</c><00:03:05.879><c> run</c><00:03:06.200><c> the</c><00:03:06.319><c> llos</c><00:03:07.280><c> you</c><00:03:07.400><c> can</c><00:03:07.560><c> run</c><00:03:07.799><c> the</c>

00:03:07.990 --> 00:03:08.000 align:start position:0%
going to run the llos you can run the
 

00:03:08.000 --> 00:03:10.949 align:start position:0%
going to run the llos you can run the
app<00:03:08.239><c> independently</c><00:03:09.040><c> the</c><00:03:09.319><c> API</c><00:03:10.319><c> and</c><00:03:10.519><c> we'll</c><00:03:10.799><c> just</c>

00:03:10.949 --> 00:03:10.959 align:start position:0%
app independently the API and we'll just
 

00:03:10.959 --> 00:03:13.710 align:start position:0%
app independently the API and we'll just
simply<00:03:11.239><c> run</c><00:03:11.480><c> five</c><00:03:11.799><c> workspace</c><00:03:12.400><c> up</c><00:03:13.159><c> and</c><00:03:13.319><c> it'll</c>

00:03:13.710 --> 00:03:13.720 align:start position:0%
simply run five workspace up and it'll
 

00:03:13.720 --> 00:03:16.270 align:start position:0%
simply run five workspace up and it'll
run<00:03:14.159><c> the</c><00:03:14.280><c> llm</c><00:03:14.720><c> OS</c><00:03:15.120><c> for</c>

00:03:16.270 --> 00:03:16.280 align:start position:0%
run the llm OS for
 

00:03:16.280 --> 00:03:20.070 align:start position:0%
run the llm OS for
us<00:03:17.280><c> so</c><00:03:17.560><c> now</c><00:03:17.959><c> the</c><00:03:18.239><c> three</c><00:03:18.519><c> containers</c><00:03:19.400><c> for</c><00:03:19.760><c> a</c>

00:03:20.070 --> 00:03:20.080 align:start position:0%
us so now the three containers for a
 

00:03:20.080 --> 00:03:23.430 align:start position:0%
us so now the three containers for a
database<00:03:20.760><c> and</c><00:03:21.040><c> the</c><00:03:21.200><c> lmos</c><00:03:21.840><c> application</c><00:03:22.200><c> are</c>

00:03:23.430 --> 00:03:23.440 align:start position:0%
database and the lmos application are
 

00:03:23.440 --> 00:03:27.949 align:start position:0%
database and the lmos application are
created<00:03:24.440><c> open</c><00:03:24.760><c> up</c><00:03:25.040><c> the</c><00:03:26.040><c> Local</c><00:03:26.480><c> Host</c>

00:03:27.949 --> 00:03:27.959 align:start position:0%
created open up the Local Host
 

00:03:27.959 --> 00:03:30.509 align:start position:0%
created open up the Local Host
8501<00:03:28.959><c> and</c><00:03:29.120><c> you'll</c><00:03:29.280><c> see</c><00:03:29.439><c> the</c><00:03:29.760><c> lmos</c><00:03:30.159><c> running</c><00:03:30.400><c> in</c>

00:03:30.509 --> 00:03:30.519 align:start position:0%
8501 and you'll see the lmos running in
 

00:03:30.519 --> 00:03:32.910 align:start position:0%
8501 and you'll see the lmos running in
a<00:03:30.680><c> Docker</c><00:03:31.000><c> container</c><00:03:31.959><c> enter</c><00:03:32.239><c> a</c><00:03:32.360><c> username</c><00:03:32.799><c> and</c>

00:03:32.910 --> 00:03:32.920 align:start position:0%
a Docker container enter a username and
 

00:03:32.920 --> 00:03:35.030 align:start position:0%
a Docker container enter a username and
let's<00:03:33.080><c> play</c><00:03:33.280><c> around</c><00:03:33.560><c> with</c><00:03:33.680><c> it</c><00:03:34.200><c> the</c><00:03:34.360><c> lmos</c><00:03:34.840><c> has</c>

00:03:35.030 --> 00:03:35.040 align:start position:0%
let's play around with it the lmos has
 

00:03:35.040 --> 00:03:37.750 align:start position:0%
let's play around with it the lmos has
access<00:03:35.280><c> to</c><00:03:35.400><c> a</c><00:03:35.720><c> calculator</c><00:03:36.720><c> the</c><00:03:36.840><c> file</c><00:03:37.159><c> system</c>

00:03:37.750 --> 00:03:37.760 align:start position:0%
access to a calculator the file system
 

00:03:37.760 --> 00:03:40.030 align:start position:0%
access to a calculator the file system
web<00:03:37.959><c> search</c><00:03:38.239><c> and</c><00:03:38.439><c> Yahoo</c><00:03:38.799><c> finance</c><00:03:39.519><c> we've</c><00:03:39.760><c> also</c>

00:03:40.030 --> 00:03:40.040 align:start position:0%
web search and Yahoo finance we've also
 

00:03:40.040 --> 00:03:42.229 align:start position:0%
web search and Yahoo finance we've also
just<00:03:40.239><c> given</c><00:03:40.680><c> one</c><00:03:41.120><c> team</c><00:03:41.439><c> member</c><00:03:41.840><c> right</c><00:03:41.959><c> now</c><00:03:42.120><c> the</c>

00:03:42.229 --> 00:03:42.239 align:start position:0%
just given one team member right now the
 

00:03:42.239 --> 00:03:45.390 align:start position:0%
just given one team member right now the
research<00:03:42.720><c> assistant</c><00:03:43.560><c> but</c><00:03:43.720><c> in</c><00:03:44.080><c> other</c><00:03:44.840><c> examples</c>

00:03:45.390 --> 00:03:45.400 align:start position:0%
research assistant but in other examples
 

00:03:45.400 --> 00:03:47.190 align:start position:0%
research assistant but in other examples
other<00:03:45.760><c> videos</c><00:03:46.200><c> you</c><00:03:46.280><c> can</c><00:03:46.439><c> see</c><00:03:46.720><c> that</c><00:03:46.840><c> we</c><00:03:46.959><c> also</c>

00:03:47.190 --> 00:03:47.200 align:start position:0%
other videos you can see that we also
 

00:03:47.200 --> 00:03:49.630 align:start position:0%
other videos you can see that we also
add<00:03:47.400><c> a</c><00:03:47.680><c> python</c><00:03:48.080><c> assistant</c><00:03:48.640><c> we</c><00:03:48.959><c> add</c><00:03:49.159><c> a</c><00:03:49.360><c> data</c>

00:03:49.630 --> 00:03:49.640 align:start position:0%
add a python assistant we add a data
 

00:03:49.640 --> 00:03:51.670 align:start position:0%
add a python assistant we add a data
analyst<00:03:50.200><c> we</c><00:03:50.319><c> also</c><00:03:50.519><c> add</c><00:03:50.760><c> a</c><00:03:50.959><c> investment</c>

00:03:51.670 --> 00:03:51.680 align:start position:0%
analyst we also add a investment
 

00:03:51.680 --> 00:03:54.789 align:start position:0%
analyst we also add a investment
assistant<00:03:52.680><c> I'll</c><00:03:52.879><c> leave</c><00:03:53.319><c> this</c><00:03:53.760><c> process</c><00:03:54.120><c> to</c><00:03:54.360><c> you</c>

00:03:54.789 --> 00:03:54.799 align:start position:0%
assistant I'll leave this process to you
 

00:03:54.799 --> 00:03:57.030 align:start position:0%
assistant I'll leave this process to you
for<00:03:55.599><c> whichever</c><00:03:56.040><c> other</c><00:03:56.360><c> assistant</c><00:03:56.799><c> team</c>

00:03:57.030 --> 00:03:57.040 align:start position:0%
for whichever other assistant team
 

00:03:57.040 --> 00:03:59.270 align:start position:0%
for whichever other assistant team
members<00:03:57.439><c> you</c><00:03:57.560><c> want</c><00:03:57.760><c> to</c><00:03:58.040><c> add</c><00:03:58.280><c> to</c><00:03:58.480><c> it</c><00:03:58.959><c> but</c><00:03:59.120><c> I'll</c>

00:03:59.270 --> 00:03:59.280 align:start position:0%
members you want to add to it but I'll
 

00:03:59.280 --> 00:04:01.990 align:start position:0%
members you want to add to it but I'll
show<00:03:59.480><c> you</c><00:03:59.920><c> how</c><00:04:00.040><c> to</c><00:04:00.239><c> add</c><00:04:00.519><c> them</c><00:04:01.360><c> so</c><00:04:01.519><c> let's</c><00:04:01.760><c> test</c>

00:04:01.990 --> 00:04:02.000 align:start position:0%
show you how to add them so let's test
 

00:04:02.000 --> 00:04:06.149 align:start position:0%
show you how to add them so let's test
out<00:04:02.159><c> the</c><00:04:02.599><c> lmos</c><00:04:03.599><c> we'll</c><00:04:03.959><c> first</c><00:04:04.560><c> add</c><00:04:04.920><c> a</c><00:04:05.319><c> blog</c><00:04:05.760><c> post</c>

00:04:06.149 --> 00:04:06.159 align:start position:0%
out the lmos we'll first add a blog post
 

00:04:06.159 --> 00:04:10.630 align:start position:0%
out the lmos we'll first add a blog post
to<00:04:06.360><c> the</c><00:04:06.519><c> knowledge</c><00:04:07.439><c> base</c><00:04:08.439><c> and</c><00:04:09.120><c> ask</c><00:04:09.400><c> the</c>

00:04:10.630 --> 00:04:10.640 align:start position:0%
to the knowledge base and ask the
 

00:04:10.640 --> 00:04:13.750 align:start position:0%
to the knowledge base and ask the
question<00:04:11.640><c> what</c><00:04:11.799><c> did</c><00:04:12.040><c> Sam</c><00:04:12.319><c> Ortman</c><00:04:13.079><c> wish</c><00:04:13.480><c> he</c>

00:04:13.750 --> 00:04:13.760 align:start position:0%
question what did Sam Ortman wish he
 

00:04:13.760 --> 00:04:17.310 align:start position:0%
question what did Sam Ortman wish he
knew<00:04:14.760><c> to</c><00:04:14.959><c> answer</c><00:04:15.280><c> this</c><00:04:15.480><c> question</c><00:04:16.440><c> uh</c><00:04:16.560><c> the</c><00:04:16.720><c> llm</c>

00:04:17.310 --> 00:04:17.320 align:start position:0%
knew to answer this question uh the llm
 

00:04:17.320 --> 00:04:19.590 align:start position:0%
knew to answer this question uh the llm
OS<00:04:17.840><c> is</c><00:04:18.000><c> going</c><00:04:18.239><c> to</c><00:04:18.440><c> search</c><00:04:18.799><c> its</c><00:04:19.079><c> knowledge</c><00:04:19.359><c> base</c>

00:04:19.590 --> 00:04:19.600 align:start position:0%
OS is going to search its knowledge base
 

00:04:19.600 --> 00:04:21.789 align:start position:0%
OS is going to search its knowledge base
for<00:04:19.759><c> this</c><00:04:20.040><c> information</c><00:04:21.040><c> and</c><00:04:21.239><c> then</c><00:04:21.479><c> use</c>

00:04:21.789 --> 00:04:21.799 align:start position:0%
for this information and then use
 

00:04:21.799 --> 00:04:24.030 align:start position:0%
for this information and then use
retrieval<00:04:22.320><c> augmented</c><00:04:22.880><c> generation</c><00:04:23.800><c> to</c>

00:04:24.030 --> 00:04:24.040 align:start position:0%
retrieval augmented generation to
 

00:04:24.040 --> 00:04:26.390 align:start position:0%
retrieval augmented generation to
provide<00:04:24.360><c> the</c><00:04:24.520><c> answer</c><00:04:24.840><c> for</c><00:04:25.040><c> us</c><00:04:25.960><c> similarly</c>

00:04:26.390 --> 00:04:26.400 align:start position:0%
provide the answer for us similarly
 

00:04:26.400 --> 00:04:27.830 align:start position:0%
provide the answer for us similarly
we're<00:04:26.520><c> going</c><00:04:26.639><c> to</c><00:04:26.759><c> test</c><00:04:26.960><c> out</c><00:04:27.080><c> a</c><00:04:27.240><c> few</c><00:04:27.479><c> things</c>

00:04:27.830 --> 00:04:27.840 align:start position:0%
we're going to test out a few things
 

00:04:27.840 --> 00:04:30.029 align:start position:0%
we're going to test out a few things
let's<00:04:28.080><c> test</c><00:04:28.280><c> out</c><00:04:28.440><c> the</c><00:04:28.639><c> calculator</c><00:04:29.440><c> you</c><00:04:29.919><c> what</c>

00:04:30.029 --> 00:04:30.039 align:start position:0%
let's test out the calculator you what
 

00:04:30.039 --> 00:04:32.670 align:start position:0%
let's test out the calculator you what
is<00:04:30.120><c> 10</c><00:04:30.720><c> factorial</c><00:04:31.720><c> use</c><00:04:31.960><c> the</c><00:04:32.080><c> calculator</c><00:04:32.520><c> to</c>

00:04:32.670 --> 00:04:32.680 align:start position:0%
is 10 factorial use the calculator to
 

00:04:32.680 --> 00:04:34.950 align:start position:0%
is 10 factorial use the calculator to
give<00:04:32.800><c> us</c><00:04:32.960><c> the</c><00:04:33.120><c> answer</c><00:04:34.080><c> all</c><00:04:34.199><c> of</c><00:04:34.400><c> this</c><00:04:34.560><c> is</c><00:04:34.800><c> right</c>

00:04:34.950 --> 00:04:34.960 align:start position:0%
give us the answer all of this is right
 

00:04:34.960 --> 00:04:38.350 align:start position:0%
give us the answer all of this is right
now<00:04:35.160><c> running</c><00:04:35.880><c> in</c><00:04:36.120><c> our</c><00:04:36.400><c> Docker</c><00:04:37.160><c> container</c><00:04:38.160><c> so</c>

00:04:38.350 --> 00:04:38.360 align:start position:0%
now running in our Docker container so
 

00:04:38.360 --> 00:04:40.510 align:start position:0%
now running in our Docker container so
you<00:04:38.479><c> can</c><00:04:38.680><c> see</c><00:04:39.039><c> all</c><00:04:39.199><c> the</c><00:04:39.280><c> logs</c><00:04:39.600><c> running</c><00:04:40.000><c> here</c>

00:04:40.510 --> 00:04:40.520 align:start position:0%
you can see all the logs running here
 

00:04:40.520 --> 00:04:42.230 align:start position:0%
you can see all the logs running here
the<00:04:40.720><c> PG</c><00:04:41.039><c> Vector</c><00:04:41.320><c> database</c><00:04:41.720><c> the</c><00:04:41.840><c> postc</c><00:04:42.039><c> cross</c>

00:04:42.230 --> 00:04:42.240 align:start position:0%
the PG Vector database the postc cross
 

00:04:42.240 --> 00:04:43.710 align:start position:0%
the PG Vector database the postc cross
database<00:04:42.600><c> is</c><00:04:42.759><c> also</c><00:04:42.919><c> running</c><00:04:43.160><c> in</c><00:04:43.240><c> a</c><00:04:43.360><c> Docker</c>

00:04:43.710 --> 00:04:43.720 align:start position:0%
database is also running in a Docker
 

00:04:43.720 --> 00:04:45.670 align:start position:0%
database is also running in a Docker
container<00:04:44.600><c> uh</c><00:04:44.720><c> which</c><00:04:44.880><c> keeps</c><00:04:45.080><c> things</c><00:04:45.360><c> very</c>

00:04:45.670 --> 00:04:45.680 align:start position:0%
container uh which keeps things very
 

00:04:45.680 --> 00:04:49.029 align:start position:0%
container uh which keeps things very
neat<00:04:46.039><c> and</c><00:04:46.240><c> well</c><00:04:46.800><c> contained</c><00:04:47.800><c> now</c><00:04:48.160><c> because</c><00:04:48.560><c> we</c>

00:04:49.029 --> 00:04:49.039 align:start position:0%
neat and well contained now because we
 

00:04:49.039 --> 00:04:50.909 align:start position:0%
neat and well contained now because we
with<00:04:49.240><c> this</c><00:04:49.759><c> this</c><00:04:49.919><c> tutorial</c><00:04:50.360><c> is</c><00:04:50.520><c> about</c><00:04:50.680><c> running</c>

00:04:50.909 --> 00:04:50.919 align:start position:0%
with this this tutorial is about running
 

00:04:50.919 --> 00:04:54.790 align:start position:0%
with this this tutorial is about running
it<00:04:51.039><c> on</c><00:04:51.160><c> AWS</c><00:04:51.759><c> so</c><00:04:52.000><c> let's</c><00:04:52.199><c> get</c><00:04:52.360><c> to</c><00:04:52.560><c> that</c><00:04:52.880><c> part</c><00:04:53.880><c> um</c>

00:04:54.790 --> 00:04:54.800 align:start position:0%
it on AWS so let's get to that part um
 

00:04:54.800 --> 00:04:57.070 align:start position:0%
it on AWS so let's get to that part um
so<00:04:54.960><c> we</c><00:04:55.080><c> ran</c><00:04:55.479><c> here</c><00:04:55.840><c> you</c><00:04:56.000><c> can</c><00:04:56.199><c> skin</c><00:04:56.639><c> through</c><00:04:56.880><c> it</c>

00:04:57.070 --> 00:04:57.080 align:start position:0%
so we ran here you can skin through it
 

00:04:57.080 --> 00:05:00.790 align:start position:0%
so we ran here you can skin through it
how<00:04:57.199><c> to</c><00:04:57.400><c> run</c><00:04:57.919><c> everything</c><00:04:58.400><c> locally</c><00:04:59.759><c> one</c><00:05:00.039><c> part</c>

00:05:00.790 --> 00:05:00.800 align:start position:0%
how to run everything locally one part
 

00:05:00.800 --> 00:05:04.350 align:start position:0%
how to run everything locally one part
is<00:05:01.800><c> under</c><00:05:02.160><c> the</c><00:05:02.400><c> settings</c><00:05:03.199><c> we're</c><00:05:03.520><c> only</c><00:05:03.960><c> running</c>

00:05:04.350 --> 00:05:04.360 align:start position:0%
is under the settings we're only running
 

00:05:04.360 --> 00:05:07.189 align:start position:0%
is under the settings we're only running
the<00:05:04.600><c> app</c><00:05:04.880><c> and</c><00:05:05.000><c> the</c><00:05:05.240><c> database</c><00:05:06.240><c> let's</c><00:05:06.639><c> enable</c>

00:05:07.189 --> 00:05:07.199 align:start position:0%
the app and the database let's enable
 

00:05:07.199 --> 00:05:12.110 align:start position:0%
the app and the database let's enable
the<00:05:08.080><c> API</c><00:05:08.800><c> as</c><00:05:09.360><c> well</c><00:05:10.360><c> and</c><00:05:10.560><c> run</c><00:05:10.880><c> fir</c><00:05:11.240><c> workpace</c><00:05:11.800><c> up</c>

00:05:12.110 --> 00:05:12.120 align:start position:0%
the API as well and run fir workpace up
 

00:05:12.120 --> 00:05:14.629 align:start position:0%
the API as well and run fir workpace up
so<00:05:12.360><c> along</c><00:05:12.639><c> with</c><00:05:12.800><c> the</c><00:05:12.960><c> streamlet</c><00:05:13.639><c> application</c>

00:05:14.629 --> 00:05:14.639 align:start position:0%
so along with the streamlet application
 

00:05:14.639 --> 00:05:17.710 align:start position:0%
so along with the streamlet application
we<00:05:14.840><c> now</c><00:05:15.000><c> run</c><00:05:15.199><c> the</c><00:05:15.320><c> llm</c><00:05:15.680><c> OS</c><00:05:16.039><c> as</c><00:05:16.160><c> the</c><00:05:16.360><c> API</c><00:05:17.360><c> so</c><00:05:17.560><c> in</c>

00:05:17.710 --> 00:05:17.720 align:start position:0%
we now run the llm OS as the API so in
 

00:05:17.720 --> 00:05:21.710 align:start position:0%
we now run the llm OS as the API so in
almost<00:05:18.199><c> all</c><00:05:18.840><c> production</c><00:05:19.520><c> use</c><00:05:20.039><c> cases</c><00:05:21.039><c> you</c><00:05:21.199><c> will</c>

00:05:21.710 --> 00:05:21.720 align:start position:0%
almost all production use cases you will
 

00:05:21.720 --> 00:05:26.110 align:start position:0%
almost all production use cases you will
be<00:05:22.360><c> serving</c><00:05:23.360><c> the</c><00:05:23.680><c> llm</c><00:05:24.120><c> OS</c><00:05:24.520><c> or</c><00:05:24.759><c> your</c><00:05:25.160><c> assistants</c>

00:05:26.110 --> 00:05:26.120 align:start position:0%
be serving the llm OS or your assistants
 

00:05:26.120 --> 00:05:29.309 align:start position:0%
be serving the llm OS or your assistants
behind<00:05:26.720><c> a</c><00:05:27.400><c> API</c><00:05:28.160><c> that</c><00:05:28.360><c> your</c><00:05:28.560><c> front</c><00:05:28.840><c> end</c><00:05:29.120><c> will</c>

00:05:29.309 --> 00:05:29.319 align:start position:0%
behind a API that your front end will
 

00:05:29.319 --> 00:05:30.710 align:start position:0%
behind a API that your front end will
call

00:05:30.710 --> 00:05:30.720 align:start position:0%
call
 

00:05:30.720 --> 00:05:32.870 align:start position:0%
call
so<00:05:31.039><c> the</c><00:05:31.199><c> lnms</c><00:05:31.800><c> you</c><00:05:31.880><c> can</c><00:05:32.080><c> access</c><00:05:32.360><c> the</c><00:05:32.479><c> API</c>

00:05:32.870 --> 00:05:32.880 align:start position:0%
so the lnms you can access the API
 

00:05:32.880 --> 00:05:36.230 align:start position:0%
so the lnms you can access the API
documentation<00:05:33.520><c> at</c><00:05:33.680><c> local</c><00:05:34.160><c> 8000</c><00:05:34.919><c> sbox</c><00:05:35.919><c> over</c>

00:05:36.230 --> 00:05:36.240 align:start position:0%
documentation at local 8000 sbox over
 

00:05:36.240 --> 00:05:39.150 align:start position:0%
documentation at local 8000 sbox over
here<00:05:36.560><c> you'll</c><00:05:36.759><c> be</c><00:05:36.880><c> able</c><00:05:37.039><c> to</c><00:05:37.160><c> see</c><00:05:38.160><c> preconfigured</c>

00:05:39.150 --> 00:05:39.160 align:start position:0%
here you'll be able to see preconfigured
 

00:05:39.160 --> 00:05:41.390 align:start position:0%
here you'll be able to see preconfigured
end<00:05:39.520><c> points</c><00:05:40.039><c> that</c><00:05:40.199><c> you</c><00:05:40.360><c> can</c><00:05:40.600><c> directly</c><00:05:41.080><c> connect</c>

00:05:41.390 --> 00:05:41.400 align:start position:0%
end points that you can directly connect
 

00:05:41.400 --> 00:05:42.710 align:start position:0%
end points that you can directly connect
to<00:05:41.560><c> your</c><00:05:41.680><c> front</c><00:05:41.919><c> end</c><00:05:42.160><c> so</c><00:05:42.280><c> you</c><00:05:42.360><c> can</c><00:05:42.520><c> start</c>

00:05:42.710 --> 00:05:42.720 align:start position:0%
to your front end so you can start
 

00:05:42.720 --> 00:05:45.110 align:start position:0%
to your front end so you can start
building<00:05:43.000><c> a</c><00:05:43.120><c> front</c><00:05:43.280><c> end</c><00:05:43.520><c> in</c><00:05:43.680><c> nextjs</c><00:05:44.639><c> a</c><00:05:44.840><c> chat</c>

00:05:45.110 --> 00:05:45.120 align:start position:0%
building a front end in nextjs a chat
 

00:05:45.120 --> 00:05:46.870 align:start position:0%
building a front end in nextjs a chat
gbd<00:05:45.440><c> like</c><00:05:45.680><c> front</c><00:05:45.840><c> end</c><00:05:46.120><c> and</c><00:05:46.280><c> use</c><00:05:46.600><c> these</c>

00:05:46.870 --> 00:05:46.880 align:start position:0%
gbd like front end and use these
 

00:05:46.880 --> 00:05:50.150 align:start position:0%
gbd like front end and use these
endpoints<00:05:47.800><c> to</c><00:05:48.000><c> serve</c><00:05:48.360><c> your</c><00:05:48.560><c> llm</c><00:05:49.000><c> OS</c><00:05:49.880><c> instead</c>

00:05:50.150 --> 00:05:50.160 align:start position:0%
endpoints to serve your llm OS instead
 

00:05:50.160 --> 00:05:51.909 align:start position:0%
endpoints to serve your llm OS instead
of<00:05:50.280><c> using</c><00:05:50.520><c> the</c><00:05:50.639><c> streamlet</c><00:05:51.160><c> application</c><00:05:51.720><c> which</c>

00:05:51.909 --> 00:05:51.919 align:start position:0%
of using the streamlet application which
 

00:05:51.919 --> 00:05:55.469 align:start position:0%
of using the streamlet application which
also<00:05:52.199><c> you</c><00:05:52.319><c> can</c><00:05:52.759><c> use</c><00:05:53.759><c> so</c><00:05:54.240><c> these</c><00:05:54.479><c> routes</c><00:05:54.919><c> are</c>

00:05:55.469 --> 00:05:55.479 align:start position:0%
also you can use so these routes are
 

00:05:55.479 --> 00:05:58.710 align:start position:0%
also you can use so these routes are
defined<00:05:56.479><c> in</c><00:05:56.680><c> the</c><00:05:56.840><c> API</c><00:05:57.479><c> routes</c><00:05:57.880><c> folder</c><00:05:58.479><c> under</c>

00:05:58.710 --> 00:05:58.720 align:start position:0%
defined in the API routes folder under
 

00:05:58.720 --> 00:06:00.150 align:start position:0%
defined in the API routes folder under
the<00:05:58.880><c> assistance</c><00:05:59.360><c> file</c>

00:06:00.150 --> 00:06:00.160 align:start position:0%
the assistance file
 

00:06:00.160 --> 00:06:01.830 align:start position:0%
the assistance file
so<00:06:00.360><c> all</c><00:06:00.520><c> the</c><00:06:00.639><c> routes</c><00:06:00.960><c> are</c><00:06:01.160><c> defined</c><00:06:01.560><c> here</c><00:06:01.759><c> you</c>

00:06:01.830 --> 00:06:01.840 align:start position:0%
so all the routes are defined here you
 

00:06:01.840 --> 00:06:04.230 align:start position:0%
so all the routes are defined here you
can<00:06:02.039><c> add</c><00:06:02.199><c> your</c><00:06:02.360><c> own</c><00:06:03.039><c> assistance</c><00:06:04.039><c> this</c>

00:06:04.230 --> 00:06:04.240 align:start position:0%
can add your own assistance this
 

00:06:04.240 --> 00:06:08.629 align:start position:0%
can add your own assistance this
information<00:06:04.720><c> is</c><00:06:05.039><c> Al</c><00:06:05.400><c> also</c><00:06:06.240><c> shared</c><00:06:06.960><c> over</c><00:06:07.639><c> here</c>

00:06:08.629 --> 00:06:08.639 align:start position:0%
information is Al also shared over here
 

00:06:08.639 --> 00:06:11.909 align:start position:0%
information is Al also shared over here
so<00:06:08.840><c> we</c><00:06:09.039><c> Define</c><00:06:09.520><c> what</c><00:06:09.720><c> each</c><00:06:09.919><c> folder</c><00:06:10.319><c> stands</c><00:06:10.919><c> for</c>

00:06:11.909 --> 00:06:11.919 align:start position:0%
so we Define what each folder stands for
 

00:06:11.919 --> 00:06:13.710 align:start position:0%
so we Define what each folder stands for
and<00:06:12.280><c> along</c><00:06:12.639><c> with</c><00:06:12.880><c> that</c><00:06:13.240><c> there's</c><00:06:13.440><c> more</c>

00:06:13.710 --> 00:06:13.720 align:start position:0%
and along with that there's more
 

00:06:13.720 --> 00:06:15.990 align:start position:0%
and along with that there's more
information<00:06:14.280><c> how</c><00:06:14.440><c> to</c><00:06:14.720><c> build</c><00:06:15.039><c> your</c><00:06:15.280><c> AI</c><00:06:15.680><c> product</c>

00:06:15.990 --> 00:06:16.000 align:start position:0%
information how to build your AI product
 

00:06:16.000 --> 00:06:20.270 align:start position:0%
information how to build your AI product
using<00:06:16.240><c> the</c><00:06:16.360><c> llm</c><00:06:17.440><c> OS</c><00:06:18.440><c> which</c><00:06:18.680><c> endpoints</c><00:06:19.240><c> to</c><00:06:19.479><c> call</c>

00:06:20.270 --> 00:06:20.280 align:start position:0%
using the llm OS which endpoints to call
 

00:06:20.280 --> 00:06:22.749 align:start position:0%
using the llm OS which endpoints to call
how<00:06:20.440><c> to</c><00:06:20.680><c> use</c><00:06:20.960><c> the</c><00:06:21.120><c> Run</c><00:06:21.400><c> ID</c><00:06:21.680><c> and</c><00:06:21.800><c> the</c><00:06:21.919><c> user</c><00:06:22.280><c> ID</c><00:06:22.560><c> so</c>

00:06:22.749 --> 00:06:22.759 align:start position:0%
how to use the Run ID and the user ID so
 

00:06:22.759 --> 00:06:24.589 align:start position:0%
how to use the Run ID and the user ID so
all<00:06:22.919><c> that</c><00:06:23.080><c> information</c><00:06:23.639><c> is</c><00:06:23.880><c> here</c><00:06:24.240><c> but</c><00:06:24.360><c> you</c><00:06:24.440><c> can</c>

00:06:24.589 --> 00:06:24.599 align:start position:0%
all that information is here but you can
 

00:06:24.599 --> 00:06:26.390 align:start position:0%
all that information is here but you can
also<00:06:25.120><c> catch</c><00:06:25.319><c> us</c><00:06:25.479><c> on</c><00:06:25.639><c> Discord</c><00:06:26.039><c> if</c><00:06:26.120><c> you</c><00:06:26.199><c> need</c>

00:06:26.390 --> 00:06:26.400 align:start position:0%
also catch us on Discord if you need
 

00:06:26.400 --> 00:06:29.029 align:start position:0%
also catch us on Discord if you need
help<00:06:26.599><c> with</c><00:06:26.759><c> this</c><00:06:27.360><c> now</c><00:06:27.520><c> let's</c><00:06:27.720><c> get</c><00:06:27.840><c> to</c><00:06:27.960><c> the</c><00:06:28.039><c> awsp</c>

00:06:29.029 --> 00:06:29.039 align:start position:0%
help with this now let's get to the awsp
 

00:06:29.039 --> 00:06:30.430 align:start position:0%
help with this now let's get to the awsp
because<00:06:29.280><c> that's</c><00:06:29.599><c> what</c><00:06:29.759><c> this</c><00:06:29.880><c> tutorial</c><00:06:30.280><c> is</c>

00:06:30.430 --> 00:06:30.440 align:start position:0%
because that's what this tutorial is
 

00:06:30.440 --> 00:06:34.469 align:start position:0%
because that's what this tutorial is
about<00:06:31.360><c> so</c><00:06:31.680><c> to</c><00:06:31.880><c> run</c><00:06:32.160><c> this</c><00:06:32.280><c> on</c><00:06:32.680><c> AWS</c><00:06:33.680><c> export</c><00:06:34.080><c> your</c>

00:06:34.469 --> 00:06:34.479 align:start position:0%
about so to run this on AWS export your
 

00:06:34.479 --> 00:06:37.230 align:start position:0%
about so to run this on AWS export your
credentials<00:06:35.479><c> uh</c><00:06:35.880><c> you</c><00:06:36.039><c> can</c><00:06:36.360><c> install</c><00:06:36.680><c> the</c><00:06:36.759><c> AWS</c>

00:06:37.230 --> 00:06:37.240 align:start position:0%
credentials uh you can install the AWS
 

00:06:37.240 --> 00:06:40.270 align:start position:0%
credentials uh you can install the AWS
CLI<00:06:37.759><c> and</c><00:06:37.919><c> run</c><00:06:38.120><c> AWS</c><00:06:38.680><c> configure</c><00:06:39.039><c> for</c><00:06:39.280><c> it</c><00:06:39.919><c> export</c>

00:06:40.270 --> 00:06:40.280 align:start position:0%
CLI and run AWS configure for it export
 

00:06:40.280 --> 00:06:43.150 align:start position:0%
CLI and run AWS configure for it export
your<00:06:40.479><c> region</c><00:06:41.280><c> and</c><00:06:41.639><c> subnet</c><00:06:42.400><c> so</c><00:06:42.639><c> add</c><00:06:42.840><c> your</c>

00:06:43.150 --> 00:06:43.160 align:start position:0%
your region and subnet so add your
 

00:06:43.160 --> 00:06:46.469 align:start position:0%
your region and subnet so add your
subnet<00:06:43.639><c> to</c><00:06:43.759><c> the</c><00:06:43.960><c> workspace</c><00:06:44.520><c> settings</c><00:06:45.479><c> file</c>

00:06:46.469 --> 00:06:46.479 align:start position:0%
subnet to the workspace settings file
 

00:06:46.479 --> 00:06:49.550 align:start position:0%
subnet to the workspace settings file
over<00:06:46.800><c> here</c><00:06:47.800><c> so</c><00:06:47.960><c> the</c><00:06:48.080><c> workspace</c><00:06:48.599><c> settings</c><00:06:49.199><c> is</c><00:06:49.319><c> a</c>

00:06:49.550 --> 00:06:49.560 align:start position:0%
over here so the workspace settings is a
 

00:06:49.560 --> 00:06:52.029 align:start position:0%
over here so the workspace settings is a
pantic<00:06:50.080><c> settings</c><00:06:50.560><c> object</c><00:06:50.960><c> so</c><00:06:51.240><c> I've</c><00:06:51.720><c> already</c>

00:06:52.029 --> 00:06:52.039 align:start position:0%
pantic settings object so I've already
 

00:06:52.039 --> 00:06:53.830 align:start position:0%
pantic settings object so I've already
exported<00:06:52.520><c> the</c><00:06:52.639><c> subnet</c><00:06:53.080><c> IDs</c><00:06:53.560><c> as</c><00:06:53.680><c> an</c>

00:06:53.830 --> 00:06:53.840 align:start position:0%
exported the subnet IDs as an
 

00:06:53.840 --> 00:06:55.430 align:start position:0%
exported the subnet IDs as an
environment<00:06:54.319><c> variable</c><00:06:54.960><c> where</c><00:06:55.120><c> you</c><00:06:55.199><c> should</c>

00:06:55.430 --> 00:06:55.440 align:start position:0%
environment variable where you should
 

00:06:55.440 --> 00:06:57.189 align:start position:0%
environment variable where you should
add<00:06:55.599><c> your</c><00:06:55.759><c> subnet</c><00:06:56.120><c> IDs</c><00:06:56.440><c> over</c>

00:06:57.189 --> 00:06:57.199 align:start position:0%
add your subnet IDs over
 

00:06:57.199 --> 00:07:00.790 align:start position:0%
add your subnet IDs over
here<00:06:58.199><c> and</c><00:06:58.440><c> then</c><00:06:58.919><c> add</c><00:06:59.080><c> your</c><00:06:59.599><c> secret</c><00:07:00.479><c> so</c><00:07:00.680><c> when</c>

00:07:00.790 --> 00:07:00.800 align:start position:0%
here and then add your secret so when
 

00:07:00.800 --> 00:07:02.790 align:start position:0%
here and then add your secret so when
you're<00:07:00.919><c> running</c><00:07:01.319><c> locally</c><00:07:02.280><c> you</c><00:07:02.560><c> probably</c>

00:07:02.790 --> 00:07:02.800 align:start position:0%
you're running locally you probably
 

00:07:02.800 --> 00:07:04.869 align:start position:0%
you're running locally you probably
don't<00:07:03.000><c> want</c><00:07:03.120><c> to</c><00:07:03.240><c> add</c><00:07:03.360><c> a</c><00:07:03.560><c> password</c><00:07:04.520><c> because</c><00:07:04.720><c> it</c>

00:07:04.869 --> 00:07:04.879 align:start position:0%
don't want to add a password because it
 

00:07:04.879 --> 00:07:06.150 align:start position:0%
don't want to add a password because it
gets<00:07:05.039><c> annoying</c><00:07:05.360><c> when</c><00:07:05.479><c> you</c><00:07:05.560><c> have</c><00:07:05.680><c> to</c><00:07:05.759><c> input</c><00:07:06.039><c> it</c>

00:07:06.150 --> 00:07:06.160 align:start position:0%
gets annoying when you have to input it
 

00:07:06.160 --> 00:07:08.430 align:start position:0%
gets annoying when you have to input it
all<00:07:06.280><c> the</c><00:07:06.440><c> time</c><00:07:07.440><c> but</c><00:07:07.639><c> when</c><00:07:07.759><c> you're</c><00:07:07.919><c> running</c><00:07:08.199><c> in</c>

00:07:08.430 --> 00:07:08.440 align:start position:0%
all the time but when you're running in
 

00:07:08.440 --> 00:07:10.390 align:start position:0%
all the time but when you're running in
production<00:07:09.120><c> you</c><00:07:09.240><c> should</c><00:07:09.520><c> add</c><00:07:09.720><c> a</c><00:07:09.919><c> password</c><00:07:10.240><c> for</c>

00:07:10.390 --> 00:07:10.400 align:start position:0%
production you should add a password for
 

00:07:10.400 --> 00:07:11.830 align:start position:0%
production you should add a password for
your<00:07:10.599><c> application</c><00:07:11.160><c> I'm</c><00:07:11.280><c> just</c><00:07:11.400><c> going</c><00:07:11.520><c> to</c><00:07:11.639><c> use</c>

00:07:11.830 --> 00:07:11.840 align:start position:0%
your application I'm just going to use
 

00:07:11.840 --> 00:07:14.150 align:start position:0%
your application I'm just going to use
admin<00:07:12.319><c> right</c><00:07:12.440><c> now</c><00:07:13.360><c> and</c><00:07:13.479><c> you</c><00:07:13.560><c> should</c><00:07:13.759><c> also</c><00:07:14.000><c> add</c>

00:07:14.150 --> 00:07:14.160 align:start position:0%
admin right now and you should also add
 

00:07:14.160 --> 00:07:16.110 align:start position:0%
admin right now and you should also add
a<00:07:14.360><c> pass</c><00:07:14.759><c> password</c><00:07:15.080><c> for</c><00:07:15.160><c> your</c><00:07:15.280><c> database</c><00:07:15.919><c> it's</c>

00:07:16.110 --> 00:07:16.120 align:start position:0%
a pass password for your database it's
 

00:07:16.120 --> 00:07:17.710 align:start position:0%
a pass password for your database it's
not<00:07:16.319><c> the</c><00:07:16.520><c> actual</c><00:07:16.840><c> password</c><00:07:17.319><c> the</c><00:07:17.440><c> actual</c>

00:07:17.710 --> 00:07:17.720 align:start position:0%
not the actual password the actual
 

00:07:17.720 --> 00:07:19.230 align:start position:0%
not the actual password the actual
password<00:07:18.039><c> for</c><00:07:18.199><c> my</c><00:07:18.360><c> database</c><00:07:18.759><c> it's</c><00:07:18.879><c> something</c>

00:07:19.230 --> 00:07:19.240 align:start position:0%
password for my database it's something
 

00:07:19.240 --> 00:07:21.150 align:start position:0%
password for my database it's something
different<00:07:19.879><c> so</c><00:07:20.080><c> you</c><00:07:20.199><c> should</c><00:07:20.479><c> change</c><00:07:20.759><c> it</c><00:07:21.000><c> as</c>

00:07:21.150 --> 00:07:21.160 align:start position:0%
different so you should change it as
 

00:07:21.160 --> 00:07:22.990 align:start position:0%
different so you should change it as
well<00:07:21.479><c> and</c><00:07:21.639><c> not</c><00:07:21.879><c> put</c><00:07:22.000><c> it</c><00:07:22.120><c> on</c>

00:07:22.990 --> 00:07:23.000 align:start position:0%
well and not put it on
 

00:07:23.000 --> 00:07:25.469 align:start position:0%
well and not put it on
video<00:07:24.000><c> next</c><00:07:24.360><c> after</c><00:07:24.599><c> you've</c><00:07:24.919><c> updated</c><00:07:25.319><c> the</c>

00:07:25.469 --> 00:07:25.479 align:start position:0%
video next after you've updated the
 

00:07:25.479 --> 00:07:29.070 align:start position:0%
video next after you've updated the
credential<00:07:26.479><c> create</c><00:07:26.840><c> your</c><00:07:27.080><c> AWS</c><00:07:27.919><c> resources</c><00:07:28.919><c> the</c>

00:07:29.070 --> 00:07:29.080 align:start position:0%
credential create your AWS resources the
 

00:07:29.080 --> 00:07:32.309 align:start position:0%
credential create your AWS resources the
same<00:07:29.319><c> way</c><00:07:29.599><c> we</c><00:07:29.720><c> ran</c><00:07:29.960><c> fir</c><00:07:30.280><c> workpace</c><00:07:30.840><c> up</c><00:07:31.680><c> to</c><00:07:32.000><c> run</c>

00:07:32.309 --> 00:07:32.319 align:start position:0%
same way we ran fir workpace up to run
 

00:07:32.319 --> 00:07:35.029 align:start position:0%
same way we ran fir workpace up to run
the<00:07:32.599><c> application</c><00:07:33.080><c> locally</c><00:07:33.479><c> using</c><00:07:33.879><c> Docker</c><00:07:34.759><c> run</c>

00:07:35.029 --> 00:07:35.039 align:start position:0%
the application locally using Docker run
 

00:07:35.039 --> 00:07:39.670 align:start position:0%
the application locally using Docker run
fir<00:07:35.360><c> workpace</c><00:07:35.800><c> up</c><00:07:36.199><c> and</c><00:07:37.000><c> prod</c><00:07:37.680><c> infra</c><00:07:38.440><c> AWS</c><00:07:39.440><c> to</c>

00:07:39.670 --> 00:07:39.680 align:start position:0%
fir workpace up and prod infra AWS to
 

00:07:39.680 --> 00:07:41.510 align:start position:0%
fir workpace up and prod infra AWS to
run<00:07:39.960><c> your</c><00:07:40.199><c> application</c><00:07:40.639><c> on</c>

00:07:41.510 --> 00:07:41.520 align:start position:0%
run your application on
 

00:07:41.520 --> 00:07:45.469 align:start position:0%
run your application on
AWS<00:07:42.520><c> we've</c><00:07:42.919><c> already</c><00:07:44.039><c> preconfigured</c><00:07:45.039><c> using</c>

00:07:45.469 --> 00:07:45.479 align:start position:0%
AWS we've already preconfigured using
 

00:07:45.479 --> 00:07:48.309 align:start position:0%
AWS we've already preconfigured using
infrastructure<00:07:46.039><c> as</c><00:07:46.280><c> code</c><00:07:47.199><c> all</c><00:07:47.560><c> the</c><00:07:47.960><c> different</c>

00:07:48.309 --> 00:07:48.319 align:start position:0%
infrastructure as code all the different
 

00:07:48.319 --> 00:07:50.070 align:start position:0%
infrastructure as code all the different
pieces<00:07:48.680><c> you're</c><00:07:48.879><c> going</c><00:07:49.039><c> to</c><00:07:49.159><c> need</c><00:07:49.360><c> to</c><00:07:49.599><c> run</c><00:07:49.879><c> your</c>

00:07:50.070 --> 00:07:50.080 align:start position:0%
pieces you're going to need to run your
 

00:07:50.080 --> 00:07:53.070 align:start position:0%
pieces you're going to need to run your
llm<00:07:50.479><c> OS</c><00:07:50.800><c> onws</c><00:07:51.800><c> we</c><00:07:51.919><c> have</c><00:07:52.080><c> the</c><00:07:52.240><c> security</c><00:07:52.599><c> groups</c>

00:07:53.070 --> 00:07:53.080 align:start position:0%
llm OS onws we have the security groups
 

00:07:53.080 --> 00:07:55.070 align:start position:0%
llm OS onws we have the security groups
because<00:07:53.240><c> it</c><00:07:53.360><c> should</c><00:07:53.599><c> run</c><00:07:53.879><c> inside</c><00:07:54.199><c> your</c><00:07:54.360><c> VPC</c>

00:07:55.070 --> 00:07:55.080 align:start position:0%
because it should run inside your VPC
 

00:07:55.080 --> 00:07:56.790 align:start position:0%
because it should run inside your VPC
not<00:07:55.400><c> everyone</c><00:07:55.720><c> should</c><00:07:55.960><c> have</c><00:07:56.199><c> access</c><00:07:56.440><c> to</c><00:07:56.639><c> your</c>

00:07:56.790 --> 00:07:56.800 align:start position:0%
not everyone should have access to your
 

00:07:56.800 --> 00:07:59.869 align:start position:0%
not everyone should have access to your
database<00:07:57.240><c> or</c><00:07:57.400><c> your</c><00:07:57.680><c> API</c><00:07:58.680><c> we've</c><00:07:58.879><c> added</c><00:07:59.120><c> sec</c>

00:07:59.869 --> 00:07:59.879 align:start position:0%
database or your API we've added sec
 

00:07:59.879 --> 00:08:02.990 align:start position:0%
database or your API we've added sec
we've<00:08:00.039><c> added</c><00:08:00.440><c> a</c><00:08:00.599><c> database</c><00:08:01.120><c> instance</c><00:08:01.840><c> for</c><00:08:02.360><c> your</c>

00:08:02.990 --> 00:08:03.000 align:start position:0%
we've added a database instance for your
 

00:08:03.000 --> 00:08:06.869 align:start position:0%
we've added a database instance for your
llos<00:08:04.000><c> load</c><00:08:04.560><c> balancers</c><00:08:05.560><c> listeners</c><00:08:06.319><c> the</c><00:08:06.440><c> ECS</c>

00:08:06.869 --> 00:08:06.879 align:start position:0%
llos load balancers listeners the ECS
 

00:08:06.879 --> 00:08:08.909 align:start position:0%
llos load balancers listeners the ECS
cluster<00:08:07.479><c> and</c><00:08:07.960><c> these</c><00:08:08.240><c> services</c><00:08:08.680><c> that</c><00:08:08.800><c> are</c>

00:08:08.909 --> 00:08:08.919 align:start position:0%
cluster and these services that are
 

00:08:08.919 --> 00:08:11.830 align:start position:0%
cluster and these services that are
going<00:08:09.080><c> to</c><00:08:09.159><c> be</c><00:08:09.280><c> running</c><00:08:10.240><c> let's</c><00:08:10.520><c> confirm</c><00:08:10.919><c> deploy</c>

00:08:11.830 --> 00:08:11.840 align:start position:0%
going to be running let's confirm deploy
 

00:08:11.840 --> 00:08:13.350 align:start position:0%
going to be running let's confirm deploy
this<00:08:11.960><c> would</c><00:08:12.199><c> normally</c><00:08:12.599><c> take</c><00:08:12.759><c> a</c><00:08:12.879><c> few</c><00:08:13.080><c> minutes</c>

00:08:13.350 --> 00:08:13.360 align:start position:0%
this would normally take a few minutes
 

00:08:13.360 --> 00:08:14.950 align:start position:0%
this would normally take a few minutes
to<00:08:13.520><c> run</c><00:08:13.840><c> I've</c><00:08:14.000><c> already</c><00:08:14.280><c> created</c><00:08:14.759><c> these</c>

00:08:14.950 --> 00:08:14.960 align:start position:0%
to run I've already created these
 

00:08:14.960 --> 00:08:17.950 align:start position:0%
to run I've already created these
resources<00:08:15.639><c> to</c><00:08:16.000><c> speed</c><00:08:16.319><c> up</c><00:08:16.479><c> for</c><00:08:16.720><c> this</c><00:08:16.960><c> tutorial</c>

00:08:17.950 --> 00:08:17.960 align:start position:0%
resources to speed up for this tutorial
 

00:08:17.960 --> 00:08:20.670 align:start position:0%
resources to speed up for this tutorial
but<00:08:18.120><c> the</c><00:08:18.280><c> database</c><00:08:18.879><c> would</c><00:08:19.879><c> definitely</c><00:08:20.440><c> take</c>

00:08:20.670 --> 00:08:20.680 align:start position:0%
but the database would definitely take
 

00:08:20.680 --> 00:08:22.510 align:start position:0%
but the database would definitely take
about<00:08:21.039><c> five</c><00:08:21.240><c> minutes</c><00:08:21.479><c> to</c><00:08:21.639><c> run</c><00:08:21.840><c> so</c><00:08:22.039><c> go</c><00:08:22.199><c> grab</c><00:08:22.400><c> a</c>

00:08:22.510 --> 00:08:22.520 align:start position:0%
about five minutes to run so go grab a
 

00:08:22.520 --> 00:08:25.029 align:start position:0%
about five minutes to run so go grab a
cup<00:08:22.680><c> of</c><00:08:22.840><c> coffee</c><00:08:23.199><c> while</c><00:08:23.400><c> this</c><00:08:23.560><c> is</c><00:08:23.879><c> running</c><00:08:24.879><c> for</c>

00:08:25.029 --> 00:08:25.039 align:start position:0%
cup of coffee while this is running for
 

00:08:25.039 --> 00:08:26.550 align:start position:0%
cup of coffee while this is running for
me<00:08:25.240><c> I've</c><00:08:25.400><c> already</c><00:08:25.680><c> created</c><00:08:26.039><c> it</c><00:08:26.319><c> before</c>

00:08:26.550 --> 00:08:26.560 align:start position:0%
me I've already created it before
 

00:08:26.560 --> 00:08:28.469 align:start position:0%
me I've already created it before
running<00:08:26.879><c> this</c><00:08:27.039><c> command</c><00:08:27.280><c> so</c><00:08:27.479><c> it's</c><00:08:27.639><c> also</c><00:08:27.919><c> saying</c>

00:08:28.469 --> 00:08:28.479 align:start position:0%
running this command so it's also saying
 

00:08:28.479 --> 00:08:30.110 align:start position:0%
running this command so it's also saying
already<00:08:28.879><c> exists</c>

00:08:30.110 --> 00:08:30.120 align:start position:0%
already exists
 

00:08:30.120 --> 00:08:31.869 align:start position:0%
already exists
uh<00:08:30.319><c> but</c><00:08:30.479><c> for</c><00:08:30.720><c> you</c><00:08:30.960><c> it</c><00:08:31.080><c> should</c><00:08:31.240><c> be</c><00:08:31.440><c> creating</c>

00:08:31.869 --> 00:08:31.879 align:start position:0%
uh but for you it should be creating
 

00:08:31.879 --> 00:08:34.110 align:start position:0%
uh but for you it should be creating
each<00:08:32.039><c> of</c><00:08:32.200><c> those</c><00:08:32.360><c> and</c><00:08:32.519><c> it</c><00:08:32.640><c> would</c><00:08:32.839><c> take</c><00:08:33.800><c> a</c><00:08:33.919><c> couple</c>

00:08:34.110 --> 00:08:34.120 align:start position:0%
each of those and it would take a couple
 

00:08:34.120 --> 00:08:36.870 align:start position:0%
each of those and it would take a couple
of<00:08:34.279><c> minutes</c><00:08:34.599><c> to</c><00:08:34.919><c> run</c><00:08:35.240><c> this</c><00:08:35.399><c> entire</c><00:08:35.880><c> process</c>

00:08:36.870 --> 00:08:36.880 align:start position:0%
of minutes to run this entire process
 

00:08:36.880 --> 00:08:38.990 align:start position:0%
of minutes to run this entire process
when<00:08:37.080><c> the</c><00:08:37.200><c> load</c><00:08:37.519><c> balancer</c><00:08:37.919><c> is</c><00:08:38.159><c> created</c><00:08:38.719><c> you'll</c>

00:08:38.990 --> 00:08:39.000 align:start position:0%
when the load balancer is created you'll
 

00:08:39.000 --> 00:08:42.070 align:start position:0%
when the load balancer is created you'll
get<00:08:39.120><c> a</c><00:08:39.279><c> load</c><00:08:39.560><c> balancer</c><00:08:40.440><c> DNS</c><00:08:41.440><c> which</c><00:08:41.599><c> you</c><00:08:41.719><c> can</c>

00:08:42.070 --> 00:08:42.080 align:start position:0%
get a load balancer DNS which you can
 

00:08:42.080 --> 00:08:45.070 align:start position:0%
get a load balancer DNS which you can
access<00:08:42.560><c> to</c><00:08:42.719><c> see</c><00:08:42.880><c> your</c><00:08:43.039><c> lmos</c><00:08:43.640><c> running</c><00:08:43.880><c> on</c><00:08:44.080><c> AWS</c>

00:08:45.070 --> 00:08:45.080 align:start position:0%
access to see your lmos running on AWS
 

00:08:45.080 --> 00:08:47.230 align:start position:0%
access to see your lmos running on AWS
you<00:08:45.200><c> can</c><00:08:45.360><c> also</c><00:08:45.640><c> see</c><00:08:45.880><c> the</c><00:08:46.080><c> API</c><00:08:46.519><c> load</c><00:08:46.800><c> balancer</c>

00:08:47.230 --> 00:08:47.240 align:start position:0%
you can also see the API load balancer
 

00:08:47.240 --> 00:08:51.190 align:start position:0%
you can also see the API load balancer
over<00:08:47.720><c> here</c><00:08:48.720><c> you</c><00:08:48.839><c> can</c><00:08:49.160><c> add/</c><00:08:50.160><c> dos</c><00:08:50.680><c> to</c><00:08:50.839><c> get</c><00:08:51.000><c> your</c>

00:08:51.190 --> 00:08:51.200 align:start position:0%
over here you can add/ dos to get your
 

00:08:51.200 --> 00:08:54.150 align:start position:0%
over here you can add/ dos to get your
lmos<00:08:51.800><c> running</c><00:08:52.040><c> on</c><00:08:52.160><c> AWS</c><00:08:53.160><c> as</c><00:08:53.279><c> an</c>

00:08:54.150 --> 00:08:54.160 align:start position:0%
lmos running on AWS as an
 

00:08:54.160 --> 00:08:57.829 align:start position:0%
lmos running on AWS as an
API<00:08:55.160><c> so</c><00:08:55.640><c> a</c><00:08:55.839><c> quick</c><00:08:56.080><c> check</c><00:08:56.320><c> in</c><00:08:56.720><c> on</c><00:08:56.920><c> your</c><00:08:57.120><c> AWS</c>

00:08:57.829 --> 00:08:57.839 align:start position:0%
API so a quick check in on your AWS
 

00:08:57.839 --> 00:09:00.190 align:start position:0%
API so a quick check in on your AWS
console<00:08:58.480><c> you</c><00:08:58.600><c> can</c><00:08:58.760><c> see</c><00:08:59.040><c> under</c><00:08:59.480><c> your</c><00:08:59.640><c> RDS</c>

00:09:00.190 --> 00:09:00.200 align:start position:0%
console you can see under your RDS
 

00:09:00.200 --> 00:09:03.269 align:start position:0%
console you can see under your RDS
databases<00:09:00.760><c> you'll</c><00:09:01.040><c> have</c><00:09:01.160><c> your</c><00:09:01.320><c> lmos</c><00:09:02.000><c> prod</c><00:09:02.279><c> DP</c>

00:09:03.269 --> 00:09:03.279 align:start position:0%
databases you'll have your lmos prod DP
 

00:09:03.279 --> 00:09:06.269 align:start position:0%
databases you'll have your lmos prod DP
I'm<00:09:03.480><c> using</c><00:09:04.079><c> a</c><00:09:04.600><c> small</c><00:09:05.000><c> instance</c><00:09:05.399><c> you</c><00:09:05.519><c> can</c><00:09:05.920><c> use</c><00:09:06.120><c> a</c>

00:09:06.269 --> 00:09:06.279 align:start position:0%
I'm using a small instance you can use a
 

00:09:06.279 --> 00:09:08.710 align:start position:0%
I'm using a small instance you can use a
free<00:09:06.600><c> tier</c><00:09:06.880><c> if</c><00:09:06.959><c> you'd</c><00:09:07.160><c> like</c><00:09:07.880><c> or</c><00:09:08.079><c> a</c><00:09:08.240><c> larger</c>

00:09:08.710 --> 00:09:08.720 align:start position:0%
free tier if you'd like or a larger
 

00:09:08.720 --> 00:09:09.990 align:start position:0%
free tier if you'd like or a larger
instance<00:09:09.079><c> if</c><00:09:09.160><c> you're</c><00:09:09.279><c> going</c><00:09:09.360><c> to</c><00:09:09.480><c> be</c><00:09:09.600><c> serving</c>

00:09:09.990 --> 00:09:10.000 align:start position:0%
instance if you're going to be serving
 

00:09:10.000 --> 00:09:12.430 align:start position:0%
instance if you're going to be serving
this<00:09:10.200><c> application</c><00:09:10.600><c> in</c><00:09:11.120><c> production</c><00:09:12.120><c> under</c><00:09:12.320><c> the</c>

00:09:12.430 --> 00:09:12.440 align:start position:0%
this application in production under the
 

00:09:12.440 --> 00:09:14.230 align:start position:0%
this application in production under the
ECS<00:09:12.800><c> console</c><00:09:13.160><c> you</c><00:09:13.279><c> can</c><00:09:13.399><c> see</c><00:09:13.600><c> your</c><00:09:13.839><c> service</c>

00:09:14.230 --> 00:09:14.240 align:start position:0%
ECS console you can see your service
 

00:09:14.240 --> 00:09:16.230 align:start position:0%
ECS console you can see your service
running<00:09:14.920><c> you</c><00:09:15.040><c> can</c><00:09:15.240><c> re</c><00:09:15.440><c> see</c><00:09:15.600><c> the</c><00:09:15.760><c> application</c>

00:09:16.230 --> 00:09:16.240 align:start position:0%
running you can re see the application
 

00:09:16.240 --> 00:09:18.430 align:start position:0%
running you can re see the application
service<00:09:16.560><c> and</c><00:09:16.680><c> the</c><00:09:16.800><c> API</c><00:09:17.160><c> service</c><00:09:17.440><c> running</c><00:09:18.320><c> uh</c>

00:09:18.430 --> 00:09:18.440 align:start position:0%
service and the API service running uh
 

00:09:18.440 --> 00:09:20.470 align:start position:0%
service and the API service running uh
you<00:09:18.600><c> can</c><00:09:19.120><c> give</c><00:09:19.279><c> it</c><00:09:19.399><c> more</c><00:09:19.680><c> memory</c><00:09:20.000><c> less</c><00:09:20.200><c> memory</c>

00:09:20.470 --> 00:09:20.480 align:start position:0%
you can give it more memory less memory
 

00:09:20.480 --> 00:09:22.470 align:start position:0%
you can give it more memory less memory
and<00:09:20.600><c> you</c><00:09:20.720><c> can</c><00:09:20.839><c> fine-tune</c><00:09:21.320><c> it</c><00:09:21.480><c> over</c><00:09:21.720><c> here</c><00:09:22.360><c> you</c>

00:09:22.470 --> 00:09:22.480 align:start position:0%
and you can fine-tune it over here you
 

00:09:22.480 --> 00:09:25.269 align:start position:0%
and you can fine-tune it over here you
can<00:09:22.640><c> see</c><00:09:22.839><c> the</c><00:09:22.959><c> logs</c><00:09:23.399><c> for</c><00:09:23.600><c> this</c><00:09:24.279><c> application</c>

00:09:25.269 --> 00:09:25.279 align:start position:0%
can see the logs for this application
 

00:09:25.279 --> 00:09:26.870 align:start position:0%
can see the logs for this application
then<00:09:25.560><c> finally</c><00:09:26.079><c> along</c><00:09:26.360><c> with</c><00:09:26.519><c> the</c><00:09:26.600><c> load</c>

00:09:26.870 --> 00:09:26.880 align:start position:0%
then finally along with the load
 

00:09:26.880 --> 00:09:31.990 align:start position:0%
then finally along with the load
balancer<00:09:27.360><c> you</c><00:09:27.440><c> can</c><00:09:27.640><c> use</c><00:09:27.880><c> Route</c><00:09:28.279><c> 53</c><00:09:29.680><c> to</c><00:09:30.120><c> add</c><00:09:31.000><c> a</c>

00:09:31.990 --> 00:09:32.000 align:start position:0%
balancer you can use Route 53 to add a
 

00:09:32.000 --> 00:09:36.630 align:start position:0%
balancer you can use Route 53 to add a
um<00:09:32.560><c> to</c><00:09:33.560><c> to</c><00:09:33.839><c> point</c><00:09:34.160><c> your</c><00:09:34.480><c> domain</c><00:09:34.959><c> to</c><00:09:35.160><c> your</c><00:09:35.640><c> llos</c>

00:09:36.630 --> 00:09:36.640 align:start position:0%
um to to point your domain to your llos
 

00:09:36.640 --> 00:09:39.389 align:start position:0%
um to to point your domain to your llos
so<00:09:36.839><c> instead</c><00:09:37.120><c> of</c><00:09:37.240><c> using</c><00:09:37.640><c> this</c><00:09:37.760><c> load</c><00:09:38.399><c> balancer</c>

00:09:39.389 --> 00:09:39.399 align:start position:0%
so instead of using this load balancer
 

00:09:39.399 --> 00:09:42.310 align:start position:0%
so instead of using this load balancer
I'm<00:09:39.800><c> I've</c><00:09:40.000><c> pointed</c><00:09:40.360><c> the</c><00:09:40.480><c> llm</c><00:09:40.839><c> os.</c><00:09:41.320><c> aid.</c><00:09:42.040><c> Run</c>

00:09:42.310 --> 00:09:42.320 align:start position:0%
I'm I've pointed the llm os. aid. Run
 

00:09:42.320 --> 00:09:44.750 align:start position:0%
I'm I've pointed the llm os. aid. Run
domain<00:09:43.120><c> to</c><00:09:43.360><c> the</c><00:09:43.480><c> lmos</c><00:09:44.079><c> so</c><00:09:44.240><c> all</c><00:09:44.320><c> of</c><00:09:44.480><c> that</c>

00:09:44.750 --> 00:09:44.760 align:start position:0%
domain to the lmos so all of that
 

00:09:44.760 --> 00:09:46.230 align:start position:0%
domain to the lmos so all of that
instructions<00:09:45.440><c> is</c><00:09:45.519><c> in</c><00:09:45.640><c> the</c><00:09:45.720><c> F</c><00:09:45.959><c> data</c>

00:09:46.230 --> 00:09:46.240 align:start position:0%
instructions is in the F data
 

00:09:46.240 --> 00:09:48.150 align:start position:0%
instructions is in the F data
documentation<00:09:47.160><c> but</c><00:09:47.279><c> now</c><00:09:47.440><c> let's</c><00:09:47.680><c> check</c><00:09:47.880><c> out</c>

00:09:48.150 --> 00:09:48.160 align:start position:0%
documentation but now let's check out
 

00:09:48.160 --> 00:09:51.630 align:start position:0%
documentation but now let's check out
the<00:09:48.440><c> lmos</c><00:09:49.040><c> that</c><00:09:49.200><c> we</c><00:09:49.600><c> build</c><00:09:50.600><c> admin</c><00:09:51.040><c> password</c><00:09:51.440><c> to</c>

00:09:51.630 --> 00:09:51.640 align:start position:0%
the lmos that we build admin password to
 

00:09:51.640 --> 00:09:54.269 align:start position:0%
the lmos that we build admin password to
get<00:09:51.800><c> in</c>

00:09:54.269 --> 00:09:54.279 align:start position:0%
 
 

00:09:54.279 --> 00:09:57.470 align:start position:0%
 
there<00:09:55.279><c> next</c><00:09:55.839><c> Let's</c><00:09:56.160><c> test</c><00:09:56.399><c> it</c><00:09:56.560><c> out</c><00:09:57.000><c> so</c><00:09:57.200><c> let's</c>

00:09:57.470 --> 00:09:57.480 align:start position:0%
there next Let's test it out so let's
 

00:09:57.480 --> 00:09:59.550 align:start position:0%
there next Let's test it out so let's
add<00:09:57.680><c> the</c><00:09:57.839><c> blog</c><00:09:58.160><c> post</c><00:09:58.480><c> again</c><00:09:58.920><c> which</c><00:09:59.040><c> we</c><00:09:59.399><c> just</c>

00:09:59.550 --> 00:09:59.560 align:start position:0%
add the blog post again which we just
 

00:09:59.560 --> 00:10:02.230 align:start position:0%
add the blog post again which we just
run<00:10:00.240><c> locally</c><00:10:01.240><c> to</c><00:10:01.399><c> see</c><00:10:01.560><c> if</c><00:10:01.680><c> it</c><00:10:01.839><c> works</c><00:10:02.120><c> it's</c>

00:10:02.230 --> 00:10:02.240 align:start position:0%
run locally to see if it works it's
 

00:10:02.240 --> 00:10:03.870 align:start position:0%
run locally to see if it works it's
going<00:10:02.360><c> to</c><00:10:02.519><c> process</c><00:10:02.839><c> those</c><00:10:03.040><c> URLs</c><00:10:03.399><c> and</c><00:10:03.560><c> add</c><00:10:03.720><c> it</c>

00:10:03.870 --> 00:10:03.880 align:start position:0%
going to process those URLs and add it
 

00:10:03.880 --> 00:10:07.190 align:start position:0%
going to process those URLs and add it
to<00:10:04.120><c> the</c><00:10:04.560><c> vector</c><00:10:04.920><c> DB</c><00:10:05.440><c> and</c><00:10:05.560><c> we'll</c><00:10:05.880><c> ask</c><00:10:06.880><c> what</c><00:10:07.040><c> did</c>

00:10:07.190 --> 00:10:07.200 align:start position:0%
to the vector DB and we'll ask what did
 

00:10:07.200 --> 00:10:11.230 align:start position:0%
to the vector DB and we'll ask what did
sammin<00:10:07.959><c> no</c><00:10:08.800><c> weing</c><00:10:09.279><c> you</c><00:10:10.000><c> and</c><00:10:10.240><c> the</c><00:10:10.360><c> llms</c><00:10:10.959><c> is</c><00:10:11.079><c> now</c>

00:10:11.230 --> 00:10:11.240 align:start position:0%
sammin no weing you and the llms is now
 

00:10:11.240 --> 00:10:13.710 align:start position:0%
sammin no weing you and the llms is now
running<00:10:11.519><c> in</c><00:10:11.720><c> production</c><00:10:12.120><c> on</c><00:10:12.399><c> AWS</c><00:10:13.399><c> you</c><00:10:13.519><c> can</c>

00:10:13.710 --> 00:10:13.720 align:start position:0%
running in production on AWS you can
 

00:10:13.720 --> 00:10:16.470 align:start position:0%
running in production on AWS you can
also<00:10:13.959><c> serve</c><00:10:14.360><c> this</c><00:10:14.800><c> as</c><00:10:14.959><c> an</c><00:10:15.160><c> API</c><00:10:15.880><c> for</c><00:10:16.120><c> your</c>

00:10:16.470 --> 00:10:16.480 align:start position:0%
also serve this as an API for your
 

00:10:16.480 --> 00:10:18.230 align:start position:0%
also serve this as an API for your
application<00:10:17.480><c> and</c><00:10:17.640><c> now</c><00:10:17.800><c> let's</c><00:10:17.959><c> give</c><00:10:18.120><c> it</c>

00:10:18.230 --> 00:10:18.240 align:start position:0%
application and now let's give it
 

00:10:18.240 --> 00:10:19.670 align:start position:0%
application and now let's give it
something<00:10:18.440><c> a</c><00:10:18.560><c> little</c><00:10:18.720><c> more</c><00:10:18.959><c> complex</c><00:10:19.440><c> we'll</c>

00:10:19.670 --> 00:10:19.680 align:start position:0%
something a little more complex we'll
 

00:10:19.680 --> 00:10:21.630 align:start position:0%
something a little more complex we'll
ask<00:10:19.920><c> it</c><00:10:20.040><c> to</c><00:10:20.240><c> write</c><00:10:20.600><c> a</c><00:10:20.800><c> comparison</c><00:10:21.360><c> between</c>

00:10:21.630 --> 00:10:21.640 align:start position:0%
ask it to write a comparison between
 

00:10:21.640 --> 00:10:24.310 align:start position:0%
ask it to write a comparison between
Nvidia<00:10:22.079><c> and</c><00:10:22.200><c> AMD</c><00:10:23.160><c> using</c><00:10:23.519><c> the</c><00:10:23.680><c> Yahoo</c><00:10:24.000><c> finance</c>

00:10:24.310 --> 00:10:24.320 align:start position:0%
Nvidia and AMD using the Yahoo finance
 

00:10:24.320 --> 00:10:26.710 align:start position:0%
Nvidia and AMD using the Yahoo finance
tools<00:10:25.120><c> now</c><00:10:25.320><c> I</c><00:10:25.480><c> like</c><00:10:25.640><c> to</c><00:10:25.720><c> keep</c><00:10:25.920><c> my</c><00:10:26.079><c> videos</c><00:10:26.480><c> under</c>

00:10:26.710 --> 00:10:26.720 align:start position:0%
tools now I like to keep my videos under
 

00:10:26.720 --> 00:10:29.710 align:start position:0%
tools now I like to keep my videos under
10<00:10:26.959><c> minutes</c><00:10:27.440><c> this</c><00:10:27.560><c> is</c><00:10:27.760><c> already</c><00:10:28.120><c> exceeded</c><00:10:28.680><c> that</c>

00:10:29.710 --> 00:10:29.720 align:start position:0%
10 minutes this is already exceeded that
 

00:10:29.720 --> 00:10:33.590 align:start position:0%
10 minutes this is already exceeded that
uh<00:10:29.920><c> so</c><00:10:30.560><c> give</c><00:10:30.800><c> the</c><00:10:30.920><c> lmos</c><00:10:31.560><c> a</c><00:10:32.079><c> try</c><00:10:33.079><c> check</c><00:10:33.279><c> out</c><00:10:33.480><c> the</c>

00:10:33.590 --> 00:10:33.600 align:start position:0%
uh so give the lmos a try check out the
 

00:10:33.600 --> 00:10:35.350 align:start position:0%
uh so give the lmos a try check out the
F<00:10:33.800><c> data</c><00:10:34.079><c> documentation</c><00:10:34.680><c> where</c><00:10:34.880><c> all</c><00:10:35.040><c> of</c><00:10:35.200><c> this</c>

00:10:35.350 --> 00:10:35.360 align:start position:0%
F data documentation where all of this
 

00:10:35.360 --> 00:10:37.389 align:start position:0%
F data documentation where all of this
information<00:10:35.800><c> is</c><00:10:36.040><c> there</c><00:10:36.639><c> drop</c><00:10:36.959><c> by</c><00:10:37.120><c> in</c><00:10:37.200><c> our</c>

00:10:37.389 --> 00:10:37.399 align:start position:0%
information is there drop by in our
 

00:10:37.399 --> 00:10:39.110 align:start position:0%
information is there drop by in our
Discord<00:10:37.800><c> open</c><00:10:38.040><c> up</c><00:10:38.160><c> a</c><00:10:38.279><c> GitHub</c><00:10:38.560><c> issue</c><00:10:38.880><c> if</c><00:10:38.959><c> you</c>

00:10:39.110 --> 00:10:39.120 align:start position:0%
Discord open up a GitHub issue if you
 

00:10:39.120 --> 00:10:41.190 align:start position:0%
Discord open up a GitHub issue if you
have<00:10:39.279><c> any</c><00:10:39.519><c> questions</c><00:10:40.279><c> and</c><00:10:40.560><c> I</c><00:10:40.639><c> hope</c><00:10:40.839><c> this</c><00:10:41.000><c> helps</c>

00:10:41.190 --> 00:10:41.200 align:start position:0%
have any questions and I hope this helps
 

00:10:41.200 --> 00:10:43.590 align:start position:0%
have any questions and I hope this helps
you<00:10:41.320><c> run</c><00:10:41.519><c> the</c><00:10:41.680><c> alal</c><00:10:42.320><c> introduction</c><00:10:43.320><c> thank</c><00:10:43.480><c> you</c>

00:10:43.590 --> 00:10:43.600 align:start position:0%
you run the alal introduction thank you
 

00:10:43.600 --> 00:10:47.160 align:start position:0%
you run the alal introduction thank you
so<00:10:43.800><c> much</c><00:10:44.040><c> have</c><00:10:44.160><c> a</c><00:10:44.320><c> great</c><00:10:44.560><c> day</c><00:10:44.959><c> bye</c>

