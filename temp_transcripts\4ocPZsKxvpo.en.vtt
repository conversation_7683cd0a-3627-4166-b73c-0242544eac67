WEBVTT
Kind: captions
Language: en

00:00:00.280 --> 00:00:02.190 align:start position:0%
 
all<00:00:00.480><c> the</c><00:00:00.599><c> crap</c><00:00:00.880><c> we</c><00:00:01.000><c> load</c><00:00:01.240><c> into</c><00:00:01.480><c> our</c><00:00:01.719><c> computer</c>

00:00:02.190 --> 00:00:02.200 align:start position:0%
all the crap we load into our computer
 

00:00:02.200 --> 00:00:04.950 align:start position:0%
all the crap we load into our computer
comprises<00:00:02.840><c> its</c><00:00:03.400><c> environment</c><00:00:04.400><c> but</c><00:00:04.560><c> what</c><00:00:04.680><c> do</c><00:00:04.839><c> we</c>

00:00:04.950 --> 00:00:04.960 align:start position:0%
comprises its environment but what do we
 

00:00:04.960 --> 00:00:07.030 align:start position:0%
comprises its environment but what do we
do<00:00:05.319><c> when</c><00:00:05.520><c> two</c><00:00:05.759><c> different</c><00:00:06.120><c> programs</c><00:00:06.600><c> require</c>

00:00:07.030 --> 00:00:07.040 align:start position:0%
do when two different programs require
 

00:00:07.040 --> 00:00:09.790 align:start position:0%
do when two different programs require
two<00:00:07.240><c> different</c><00:00:07.840><c> environments</c><00:00:08.840><c> easy</c><00:00:09.440><c> we</c><00:00:09.599><c> buy</c>

00:00:09.790 --> 00:00:09.800 align:start position:0%
two different environments easy we buy
 

00:00:09.800 --> 00:00:11.990 align:start position:0%
two different environments easy we buy
two<00:00:10.000><c> different</c><00:00:10.639><c> computers</c><00:00:11.639><c> that's</c><00:00:11.799><c> an</c>

00:00:11.990 --> 00:00:12.000 align:start position:0%
two different computers that's an
 

00:00:12.000 --> 00:00:14.629 align:start position:0%
two different computers that's an
expensive<00:00:12.480><c> solution</c><00:00:13.320><c> so</c><00:00:13.519><c> we</c><00:00:13.679><c> use</c><00:00:13.920><c> your</c><00:00:14.160><c> credit</c>

00:00:14.629 --> 00:00:14.639 align:start position:0%
expensive solution so we use your credit
 

00:00:14.639 --> 00:00:17.830 align:start position:0%
expensive solution so we use your credit
card<00:00:15.639><c> or</c><00:00:16.160><c> we</c><00:00:16.320><c> use</c><00:00:16.640><c> Virtual</c><00:00:17.000><c> environments</c><00:00:17.560><c> like</c>

00:00:17.830 --> 00:00:17.840 align:start position:0%
card or we use Virtual environments like
 

00:00:17.840 --> 00:00:21.269 align:start position:0%
card or we use Virtual environments like
cond<00:00:18.320><c> or</c><00:00:18.880><c> VNV</c><00:00:19.880><c> we</c><00:00:20.000><c> use</c><00:00:20.320><c> Virtual</c><00:00:20.760><c> environments</c>

00:00:21.269 --> 00:00:21.279 align:start position:0%
cond or VNV we use Virtual environments
 

00:00:21.279 --> 00:00:23.509 align:start position:0%
cond or VNV we use Virtual environments
to<00:00:21.560><c> isolate</c><00:00:21.960><c> our</c><00:00:22.240><c> programs</c><00:00:22.720><c> from</c><00:00:22.880><c> our</c><00:00:23.080><c> larger</c>

00:00:23.509 --> 00:00:23.519 align:start position:0%
to isolate our programs from our larger
 

00:00:23.519 --> 00:00:25.710 align:start position:0%
to isolate our programs from our larger
computer<00:00:24.080><c> environment</c><00:00:24.960><c> the</c><00:00:25.119><c> same</c><00:00:25.359><c> way</c><00:00:25.560><c> we</c>

00:00:25.710 --> 00:00:25.720 align:start position:0%
computer environment the same way we
 

00:00:25.720 --> 00:00:28.150 align:start position:0%
computer environment the same way we
build<00:00:26.119><c> houses</c><00:00:26.599><c> to</c><00:00:26.840><c> isolate</c><00:00:27.240><c> our</c><00:00:27.480><c> living</c><00:00:27.800><c> area</c>

00:00:28.150 --> 00:00:28.160 align:start position:0%
build houses to isolate our living area
 

00:00:28.160 --> 00:00:30.589 align:start position:0%
build houses to isolate our living area
from<00:00:28.359><c> the</c><00:00:28.519><c> environment</c><00:00:28.960><c> of</c><00:00:29.119><c> the</c><00:00:29.240><c> Earth</c><00:00:30.279><c> it's</c>

00:00:30.589 --> 00:00:30.599 align:start position:0%
from the environment of the Earth it's
 

00:00:30.599 --> 00:00:32.869 align:start position:0%
from the environment of the Earth it's
easy<00:00:31.000><c> to</c><00:00:31.199><c> turn</c><00:00:31.480><c> up</c><00:00:31.679><c> the</c><00:00:31.840><c> temperature</c><00:00:32.239><c> in</c><00:00:32.399><c> our</c>

00:00:32.869 --> 00:00:32.879 align:start position:0%
easy to turn up the temperature in our
 

00:00:32.879 --> 00:00:35.590 align:start position:0%
easy to turn up the temperature in our
houses<00:00:33.879><c> but</c><00:00:34.239><c> I</c><00:00:34.320><c> mean</c><00:00:34.559><c> it's</c><00:00:34.760><c> not</c><00:00:34.960><c> like</c><00:00:35.160><c> a</c><00:00:35.320><c> person</c>

00:00:35.590 --> 00:00:35.600 align:start position:0%
houses but I mean it's not like a person
 

00:00:35.600 --> 00:00:38.229 align:start position:0%
houses but I mean it's not like a person
can<00:00:35.760><c> warm</c><00:00:36.120><c> the</c><00:00:36.280><c> entire</c><00:00:36.800><c> planet</c><00:00:37.399><c> right</c><00:00:38.120><c> who</c>

00:00:38.229 --> 00:00:38.239 align:start position:0%
can warm the entire planet right who
 

00:00:38.239 --> 00:00:40.630 align:start position:0%
can warm the entire planet right who
would<00:00:38.440><c> believe</c><00:00:39.000><c> that</c><00:00:39.719><c> send</c><00:00:39.960><c> your</c><00:00:40.160><c> hate</c><00:00:40.360><c> mail</c>

00:00:40.630 --> 00:00:40.640 align:start position:0%
would believe that send your hate mail
 

00:00:40.640 --> 00:00:43.069 align:start position:0%
would believe that send your hate mail
to<00:00:40.840><c> J</c><00:00:41.520><c> gravel.</c>

00:00:43.069 --> 00:00:43.079 align:start position:0%
to J gravel.
 

00:00:43.079 --> 00:00:46.229 align:start position:0%
to J gravel.
us<00:00:44.079><c> I</c><00:00:44.200><c> prefer</c><00:00:44.559><c> cond</c><00:00:45.160><c> and</c><00:00:45.399><c> I</c><00:00:45.480><c> know</c><00:00:45.680><c> I'm</c><00:00:45.840><c> right</c>

00:00:46.229 --> 00:00:46.239 align:start position:0%
us I prefer cond and I know I'm right
 

00:00:46.239 --> 00:00:49.229 align:start position:0%
us I prefer cond and I know I'm right
because<00:00:46.640><c> grock</c><00:00:47.280><c> agrees</c><00:00:47.719><c> with</c><00:00:47.840><c> me</c><00:00:48.480><c> all</c><00:00:48.879><c> hail</c>

00:00:49.229 --> 00:00:49.239 align:start position:0%
because grock agrees with me all hail
 

00:00:49.239 --> 00:00:51.510 align:start position:0%
because grock agrees with me all hail
grock<00:00:50.160><c> all</c><00:00:50.440><c> hail</c>

00:00:51.510 --> 00:00:51.520 align:start position:0%
grock all hail
 

00:00:51.520 --> 00:00:54.029 align:start position:0%
grock all hail
Gro<00:00:52.520><c> I</c><00:00:52.600><c> can</c><00:00:52.800><c> demonstrate</c><00:00:53.359><c> the</c><00:00:53.520><c> impact</c><00:00:53.920><c> of</c>

00:00:54.029 --> 00:00:54.039 align:start position:0%
Gro I can demonstrate the impact of
 

00:00:54.039 --> 00:00:56.510 align:start position:0%
Gro I can demonstrate the impact of
using<00:00:54.399><c> cond</c><00:00:54.840><c> by</c><00:00:55.000><c> activating</c><00:00:55.520><c> my</c><00:00:55.800><c> pocket</c><00:00:56.160><c> grock</c>

00:00:56.510 --> 00:00:56.520 align:start position:0%
using cond by activating my pocket grock
 

00:00:56.520 --> 00:00:58.549 align:start position:0%
using cond by activating my pocket grock
environment<00:00:57.280><c> and</c><00:00:57.440><c> checking</c><00:00:57.760><c> our</c><00:00:58.079><c> python</c>

00:00:58.549 --> 00:00:58.559 align:start position:0%
environment and checking our python
 

00:00:58.559 --> 00:01:01.709 align:start position:0%
environment and checking our python
version<00:00:58.920><c> number</c><00:01:00.000><c> pocket</c><00:01:00.359><c> grock</c><00:01:00.760><c> uses</c><00:01:01.280><c> python</c>

00:01:01.709 --> 00:01:01.719 align:start position:0%
version number pocket grock uses python
 

00:01:01.719 --> 00:01:03.149 align:start position:0%
version number pocket grock uses python
version

00:01:03.149 --> 00:01:03.159 align:start position:0%
version
 

00:01:03.159 --> 00:01:06.149 align:start position:0%
version
31.9<00:01:04.159><c> now</c><00:01:04.519><c> I</c><00:01:04.640><c> can</c><00:01:04.879><c> deactivate</c><00:01:05.479><c> our</c><00:01:05.680><c> virtual</c>

00:01:06.149 --> 00:01:06.159 align:start position:0%
31.9 now I can deactivate our virtual
 

00:01:06.159 --> 00:01:08.230 align:start position:0%
31.9 now I can deactivate our virtual
environment<00:01:06.799><c> and</c><00:01:06.920><c> check</c><00:01:07.159><c> our</c><00:01:07.400><c> python</c><00:01:07.720><c> version</c>

00:01:08.230 --> 00:01:08.240 align:start position:0%
environment and check our python version
 

00:01:08.240 --> 00:01:10.429 align:start position:0%
environment and check our python version
once<00:01:08.479><c> more</c><00:01:09.280><c> and</c><00:01:09.439><c> our</c><00:01:09.600><c> computer</c><00:01:10.000><c> itself</c><00:01:10.320><c> is</c>

00:01:10.429 --> 00:01:10.439 align:start position:0%
once more and our computer itself is
 

00:01:10.439 --> 00:01:13.230 align:start position:0%
once more and our computer itself is
using<00:01:10.840><c> python</c><00:01:11.600><c> 310</c><00:01:12.600><c> this</c><00:01:12.720><c> isn't</c><00:01:13.040><c> just</c>

00:01:13.230 --> 00:01:13.240 align:start position:0%
using python 310 this isn't just
 

00:01:13.240 --> 00:01:15.670 align:start position:0%
using python 310 this isn't just
important<00:01:13.600><c> for</c><00:01:13.799><c> Version</c><00:01:14.280><c> Control</c><00:01:15.280><c> suppose</c>

00:01:15.670 --> 00:01:15.680 align:start position:0%
important for Version Control suppose
 

00:01:15.680 --> 00:01:17.710 align:start position:0%
important for Version Control suppose
one<00:01:15.840><c> of</c><00:01:16.040><c> your</c><00:01:16.280><c> programs</c><00:01:16.720><c> Demands</c><00:01:17.080><c> a</c><00:01:17.240><c> library</c>

00:01:17.710 --> 00:01:17.720 align:start position:0%
one of your programs Demands a library
 

00:01:17.720 --> 00:01:20.270 align:start position:0%
one of your programs Demands a library
that<00:01:17.920><c> consumes</c><00:01:18.479><c> memory</c><00:01:19.000><c> like</c><00:01:19.200><c> AI</c><00:01:19.600><c> Will</c><00:01:19.880><c> Smith</c>

00:01:20.270 --> 00:01:20.280 align:start position:0%
that consumes memory like AI Will Smith
 

00:01:20.280 --> 00:01:22.710 align:start position:0%
that consumes memory like AI Will Smith
eats<00:01:20.720><c> spaghetti</c><00:01:21.720><c> cond</c><00:01:22.000><c> to</c><00:01:22.119><c> make</c><00:01:22.320><c> sure</c><00:01:22.479><c> Will</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
eats spaghetti cond to make sure Will
 

00:01:22.720 --> 00:01:24.230 align:start position:0%
eats spaghetti cond to make sure Will
Smith<00:01:23.040><c> doesn't</c><00:01:23.240><c> eat</c><00:01:23.439><c> all</c><00:01:23.560><c> our</c>

00:01:24.230 --> 00:01:24.240 align:start position:0%
Smith doesn't eat all our
 

00:01:24.240 --> 00:01:28.149 align:start position:0%
Smith doesn't eat all our
spaghetti<00:01:25.240><c> close</c><00:01:25.720><c> enough</c><00:01:26.720><c> this</c><00:01:26.960><c> vid</c><00:01:27.400><c> IQ</c>

00:01:28.149 --> 00:01:28.159 align:start position:0%
spaghetti close enough this vid IQ
 

00:01:28.159 --> 00:01:30.550 align:start position:0%
spaghetti close enough this vid IQ
software<00:01:28.680><c> that</c><00:01:28.799><c> I</c><00:01:28.920><c> paid</c><00:01:29.159><c> a</c><00:01:29.320><c> dollar</c><00:01:29.600><c> for</c><00:01:30.280><c> tells</c>

00:01:30.550 --> 00:01:30.560 align:start position:0%
software that I paid a dollar for tells
 

00:01:30.560 --> 00:01:32.630 align:start position:0%
software that I paid a dollar for tells
me<00:01:30.840><c> that</c><00:01:31.079><c> if</c><00:01:31.200><c> I</c><00:01:31.360><c> make</c><00:01:31.520><c> a</c><00:01:31.720><c> video</c><00:01:32.040><c> about</c><00:01:32.280><c> how</c><00:01:32.399><c> to</c>

00:01:32.630 --> 00:01:32.640 align:start position:0%
me that if I make a video about how to
 

00:01:32.640 --> 00:01:35.429 align:start position:0%
me that if I make a video about how to
program<00:01:33.079><c> AI</c><00:01:33.479><c> using</c><00:01:33.880><c> notepad</c><00:01:34.880><c> I'll</c><00:01:35.079><c> be</c><00:01:35.280><c> rich</c>

00:01:35.429 --> 00:01:35.439 align:start position:0%
program AI using notepad I'll be rich
 

00:01:35.439 --> 00:01:38.310 align:start position:0%
program AI using notepad I'll be rich
and<00:01:35.640><c> famous</c><00:01:35.960><c> in</c><00:01:36.119><c> no</c><00:01:36.439><c> time</c><00:01:37.079><c> so</c><00:01:37.280><c> let's</c><00:01:37.479><c> give</c><00:01:37.600><c> it</c><00:01:37.720><c> a</c>

00:01:38.310 --> 00:01:38.320 align:start position:0%
and famous in no time so let's give it a
 

00:01:38.320 --> 00:01:41.190 align:start position:0%
and famous in no time so let's give it a
shot<00:01:39.320><c> python</c><00:01:39.680><c> version</c><00:01:40.000><c> 10</c><00:01:40.360><c> works</c><00:01:40.720><c> fine</c><00:01:40.920><c> for</c><00:01:41.079><c> a</c>

00:01:41.190 --> 00:01:41.200 align:start position:0%
shot python version 10 works fine for a
 

00:01:41.200 --> 00:01:43.630 align:start position:0%
shot python version 10 works fine for a
lot<00:01:41.360><c> of</c><00:01:41.479><c> General</c><00:01:41.880><c> use</c><00:01:42.439><c> situations</c><00:01:43.439><c> it's</c>

00:01:43.630 --> 00:01:43.640 align:start position:0%
lot of General use situations it's
 

00:01:43.640 --> 00:01:45.550 align:start position:0%
lot of General use situations it's
Backward<00:01:44.079><c> Compatible</c><00:01:44.600><c> with</c><00:01:44.759><c> most</c><00:01:45.119><c> standard</c>

00:01:45.550 --> 00:01:45.560 align:start position:0%
Backward Compatible with most standard
 

00:01:45.560 --> 00:01:48.109 align:start position:0%
Backward Compatible with most standard
programming<00:01:46.159><c> libraries</c><00:01:47.119><c> but</c><00:01:47.280><c> we</c><00:01:47.399><c> use</c><00:01:47.759><c> Python</c>

00:01:48.109 --> 00:01:48.119 align:start position:0%
programming libraries but we use Python
 

00:01:48.119 --> 00:01:50.389 align:start position:0%
programming libraries but we use Python
11<00:01:48.600><c> in</c><00:01:48.799><c> Pocket</c><00:01:49.119><c> grock</c><00:01:49.759><c> because</c><00:01:50.000><c> some</c><00:01:50.159><c> of</c><00:01:50.280><c> the</c>

00:01:50.389 --> 00:01:50.399 align:start position:0%
11 in Pocket grock because some of the
 

00:01:50.399 --> 00:01:53.030 align:start position:0%
11 in Pocket grock because some of the
newer<00:01:50.759><c> gadgets</c><00:01:51.200><c> demand</c><00:01:51.520><c> it</c><00:01:52.520><c> if</c><00:01:52.600><c> you're</c><00:01:52.799><c> not</c>

00:01:53.030 --> 00:01:53.040 align:start position:0%
newer gadgets demand it if you're not
 

00:01:53.040 --> 00:01:55.069 align:start position:0%
newer gadgets demand it if you're not
familiar<00:01:53.479><c> with</c><00:01:53.640><c> pocket</c><00:01:54.000><c> grock</c><00:01:54.560><c> it</c><00:01:54.719><c> allows</c><00:01:55.000><c> you</c>

00:01:55.069 --> 00:01:55.079 align:start position:0%
familiar with pocket grock it allows you
 

00:01:55.079 --> 00:01:57.270 align:start position:0%
familiar with pocket grock it allows you
to<00:01:55.240><c> write</c><00:01:55.560><c> programs</c><00:01:56.039><c> that</c><00:01:56.159><c> use</c><00:01:56.439><c> the</c><00:01:56.560><c> grock</c><00:01:56.880><c> AI</c>

00:01:57.270 --> 00:01:57.280 align:start position:0%
to write programs that use the grock AI
 

00:01:57.280 --> 00:01:58.950 align:start position:0%
to write programs that use the grock AI
provider<00:01:57.719><c> service</c><00:01:58.119><c> without</c><00:01:58.360><c> having</c><00:01:58.560><c> to</c><00:01:58.719><c> write</c>

00:01:58.950 --> 00:01:58.960 align:start position:0%
provider service without having to write
 

00:01:58.960 --> 00:02:01.749 align:start position:0%
provider service without having to write
much<00:01:59.200><c> code</c><00:02:00.159><c> it</c><00:02:00.280><c> was</c><00:02:00.479><c> written</c><00:02:00.840><c> by</c><00:02:01.000><c> a</c><00:02:01.200><c> brilliant</c>

00:02:01.749 --> 00:02:01.759 align:start position:0%
much code it was written by a brilliant
 

00:02:01.759 --> 00:02:04.749 align:start position:0%
much code it was written by a brilliant
handsome<00:02:02.200><c> genius</c><00:02:02.640><c> who</c><00:02:03.079><c> oh</c><00:02:03.280><c> yeah</c><00:02:03.600><c> a</c><00:02:03.759><c> real</c><00:02:03.960><c> Fabio</c>

00:02:04.749 --> 00:02:04.759 align:start position:0%
handsome genius who oh yeah a real Fabio
 

00:02:04.759 --> 00:02:07.109 align:start position:0%
handsome genius who oh yeah a real Fabio
Einstein<00:02:05.759><c> I've</c><00:02:05.920><c> given</c><00:02:06.200><c> Claude</c><00:02:06.520><c> the</c><00:02:06.640><c> base</c><00:02:06.920><c> code</c>

00:02:07.109 --> 00:02:07.119 align:start position:0%
Einstein I've given Claude the base code
 

00:02:07.119 --> 00:02:09.510 align:start position:0%
Einstein I've given Claude the base code
for<00:02:07.399><c> pocket</c><00:02:07.719><c> grock</c><00:02:08.319><c> so</c><00:02:08.520><c> it</c><00:02:08.640><c> can</c><00:02:08.840><c> easily</c><00:02:09.160><c> write</c>

00:02:09.510 --> 00:02:09.520 align:start position:0%
for pocket grock so it can easily write
 

00:02:09.520 --> 00:02:12.470 align:start position:0%
for pocket grock so it can easily write
software<00:02:10.039><c> that</c><00:02:10.160><c> uses</c><00:02:10.479><c> the</c><00:02:10.640><c> grock</c><00:02:11.000><c> API</c><00:02:12.000><c> see</c><00:02:12.280><c> my</c>

00:02:12.470 --> 00:02:12.480 align:start position:0%
software that uses the grock API see my
 

00:02:12.480 --> 00:02:14.830 align:start position:0%
software that uses the grock API see my
video<00:02:12.760><c> on</c><00:02:13.040><c> pi</c><00:02:13.280><c> to</c><00:02:13.400><c> MD</c><00:02:13.800><c> to</c><00:02:13.920><c> see</c><00:02:14.120><c> how</c><00:02:14.239><c> to</c><00:02:14.440><c> quickly</c>

00:02:14.830 --> 00:02:14.840 align:start position:0%
video on pi to MD to see how to quickly
 

00:02:14.840 --> 00:02:17.350 align:start position:0%
video on pi to MD to see how to quickly
and<00:02:15.040><c> easily</c><00:02:15.400><c> train</c><00:02:15.879><c> Claw</c><00:02:16.160><c> on</c><00:02:16.480><c> any</c><00:02:16.680><c> of</c><00:02:16.840><c> your</c><00:02:17.000><c> own</c>

00:02:17.350 --> 00:02:17.360 align:start position:0%
and easily train Claw on any of your own
 

00:02:17.360 --> 00:02:20.430 align:start position:0%
and easily train Claw on any of your own
python<00:02:18.160><c> applications</c><00:02:19.160><c> for</c><00:02:19.360><c> fun</c><00:02:19.720><c> our</c><00:02:20.000><c> AI</c>

00:02:20.430 --> 00:02:20.440 align:start position:0%
python applications for fun our AI
 

00:02:20.440 --> 00:02:22.030 align:start position:0%
python applications for fun our AI
chatbot<00:02:21.040><c> is</c><00:02:21.160><c> going</c><00:02:21.319><c> to</c><00:02:21.480><c> talk</c><00:02:21.760><c> like</c><00:02:21.879><c> an</c>

00:02:22.030 --> 00:02:22.040 align:start position:0%
chatbot is going to talk like an
 

00:02:22.040 --> 00:02:23.869 align:start position:0%
chatbot is going to talk like an
informant<00:02:22.560><c> in</c><00:02:22.680><c> an</c><00:02:22.840><c> old</c><00:02:23.080><c> Spencer</c><00:02:23.480><c> Tracy</c>

00:02:23.869 --> 00:02:23.879 align:start position:0%
informant in an old Spencer Tracy
 

00:02:23.879 --> 00:02:26.110 align:start position:0%
informant in an old Spencer Tracy
gangster<00:02:24.319><c> movie</c><00:02:25.200><c> kids</c><00:02:25.720><c> ask</c><00:02:25.920><c> your</c>

00:02:26.110 --> 00:02:26.120 align:start position:0%
gangster movie kids ask your
 

00:02:26.120 --> 00:02:30.710 align:start position:0%
gangster movie kids ask your
grandparents<00:02:26.720><c> who</c><00:02:26.920><c> Spencer</c><00:02:27.319><c> Tracy</c><00:02:27.840><c> was</c>

00:02:30.710 --> 00:02:30.720 align:start position:0%
grandparents who Spencer Tracy was
 

00:02:30.720 --> 00:02:32.710 align:start position:0%
grandparents who Spencer Tracy was
thanks<00:02:30.959><c> to</c><00:02:31.120><c> the</c><00:02:31.280><c> pocket</c><00:02:31.599><c> grock</c><00:02:32.000><c> Library</c>

00:02:32.710 --> 00:02:32.720 align:start position:0%
thanks to the pocket grock Library
 

00:02:32.720 --> 00:02:34.869 align:start position:0%
thanks to the pocket grock Library
Claude<00:02:33.080><c> is</c><00:02:33.239><c> able</c><00:02:33.480><c> to</c><00:02:33.680><c> spit</c><00:02:33.959><c> out</c><00:02:34.160><c> our</c><00:02:34.400><c> solution</c>

00:02:34.869 --> 00:02:34.879 align:start position:0%
Claude is able to spit out our solution
 

00:02:34.879 --> 00:02:37.550 align:start position:0%
Claude is able to spit out our solution
in<00:02:35.040><c> a</c><00:02:35.200><c> couple</c><00:02:35.480><c> dozen</c><00:02:35.879><c> lines</c><00:02:36.879><c> we</c><00:02:37.000><c> can</c><00:02:37.239><c> paste</c>

00:02:37.550 --> 00:02:37.560 align:start position:0%
in a couple dozen lines we can paste
 

00:02:37.560 --> 00:02:40.750 align:start position:0%
in a couple dozen lines we can paste
that<00:02:37.720><c> code</c><00:02:38.120><c> into</c><00:02:38.400><c> notepad</c><00:02:39.360><c> save</c><00:02:39.640><c> it</c><00:02:39.800><c> as</c><00:02:39.959><c> a</c><00:02:40.159><c> py</c>

00:02:40.750 --> 00:02:40.760 align:start position:0%
that code into notepad save it as a py
 

00:02:40.760 --> 00:02:43.350 align:start position:0%
that code into notepad save it as a py
file<00:02:41.440><c> and</c><00:02:41.560><c> we're</c><00:02:41.760><c> ready</c><00:02:41.959><c> to</c><00:02:42.159><c> rock</c><00:02:42.400><c> and</c>

00:02:43.350 --> 00:02:43.360 align:start position:0%
file and we're ready to rock and
 

00:02:43.360 --> 00:02:46.070 align:start position:0%
file and we're ready to rock and
roll<00:02:44.360><c> when</c><00:02:44.480><c> we</c><00:02:44.640><c> run</c><00:02:44.840><c> our</c><00:02:45.040><c> program</c><00:02:45.599><c> Lefty</c>

00:02:46.070 --> 00:02:46.080 align:start position:0%
roll when we run our program Lefty
 

00:02:46.080 --> 00:02:48.110 align:start position:0%
roll when we run our program Lefty
starts<00:02:46.319><c> singing</c><00:02:46.680><c> like</c><00:02:46.800><c> a</c><00:02:46.959><c> canary</c><00:02:47.720><c> he's</c><00:02:47.920><c> ready</c>

00:02:48.110 --> 00:02:48.120 align:start position:0%
starts singing like a canary he's ready
 

00:02:48.120 --> 00:02:50.309 align:start position:0%
starts singing like a canary he's ready
to<00:02:48.280><c> rat</c><00:02:48.519><c> out</c><00:02:48.720><c> his</c><00:02:48.920><c> friends</c><00:02:49.640><c> he'd</c><00:02:49.879><c> rather</c><00:02:50.080><c> be</c><00:02:50.200><c> a</c>

00:02:50.309 --> 00:02:50.319 align:start position:0%
to rat out his friends he'd rather be a
 

00:02:50.319 --> 00:02:52.390 align:start position:0%
to rat out his friends he'd rather be a
stool<00:02:50.680><c> pigeon</c><00:02:51.040><c> than</c><00:02:51.159><c> a</c><00:02:51.280><c> jail</c><00:02:51.519><c> bird</c><00:02:52.239><c> he's</c>

00:02:52.390 --> 00:02:52.400 align:start position:0%
stool pigeon than a jail bird he's
 

00:02:52.400 --> 00:02:54.589 align:start position:0%
stool pigeon than a jail bird he's
quacking<00:02:52.840><c> like</c><00:02:52.959><c> a</c><00:02:53.120><c> duck</c><00:02:53.680><c> Mobsters</c><00:02:54.159><c> don't</c><00:02:54.360><c> say</c>

00:02:54.589 --> 00:02:54.599 align:start position:0%
quacking like a duck Mobsters don't say
 

00:02:54.599 --> 00:02:57.149 align:start position:0%
quacking like a duck Mobsters don't say
quacking<00:02:55.000><c> like</c><00:02:55.120><c> a</c><00:02:55.319><c> 23</c><00:02:55.760><c> skido</c><00:02:56.760><c> that</c><00:02:56.879><c> doesn't</c>

00:02:57.149 --> 00:02:57.159 align:start position:0%
quacking like a 23 skido that doesn't
 

00:02:57.159 --> 00:03:02.350 align:start position:0%
quacking like a 23 skido that doesn't
even<00:02:57.400><c> make</c><00:02:57.640><c> sense</c><00:02:58.400><c> B</c><00:02:58.760><c> Bing</c><00:02:59.480><c> no</c><00:03:00.239><c> b</c><00:03:00.560><c> boom</c><00:03:01.200><c> stop</c><00:03:01.480><c> it</c>

00:03:02.350 --> 00:03:02.360 align:start position:0%
even make sense B Bing no b boom stop it
 

00:03:02.360 --> 00:03:03.990 align:start position:0%
even make sense B Bing no b boom stop it
I've<00:03:02.480><c> got</c><00:03:02.599><c> a</c><00:03:02.720><c> little</c><00:03:03.000><c> time</c><00:03:03.280><c> here</c><00:03:03.480><c> so</c><00:03:03.680><c> let</c><00:03:03.799><c> me</c>

00:03:03.990 --> 00:03:04.000 align:start position:0%
I've got a little time here so let me
 

00:03:04.000 --> 00:03:06.390 align:start position:0%
I've got a little time here so let me
walk<00:03:04.159><c> you</c><00:03:04.360><c> through</c><00:03:04.599><c> setting</c><00:03:04.920><c> up</c><00:03:05.200><c> cond</c><00:03:06.200><c> they've</c>

00:03:06.390 --> 00:03:06.400 align:start position:0%
walk you through setting up cond they've
 

00:03:06.400 --> 00:03:08.390 align:start position:0%
walk you through setting up cond they've
made<00:03:06.560><c> it</c><00:03:06.760><c> way</c><00:03:06.959><c> more</c><00:03:07.200><c> confusing</c><00:03:07.799><c> than</c><00:03:07.920><c> it</c><00:03:08.080><c> needs</c>

00:03:08.390 --> 00:03:08.400 align:start position:0%
made it way more confusing than it needs
 

00:03:08.400 --> 00:03:10.589 align:start position:0%
made it way more confusing than it needs
to<00:03:08.560><c> be</c><00:03:09.000><c> in</c><00:03:09.120><c> short</c><00:03:09.440><c> the</c><00:03:09.640><c> actual</c><00:03:10.000><c> program</c><00:03:10.400><c> is</c>

00:03:10.589 --> 00:03:10.599 align:start position:0%
to be in short the actual program is
 

00:03:10.599 --> 00:03:13.149 align:start position:0%
to be in short the actual program is
called<00:03:11.120><c> Anaconda</c><00:03:12.120><c> but</c><00:03:12.280><c> you</c><00:03:12.400><c> don't</c><00:03:12.640><c> want</c>

00:03:13.149 --> 00:03:13.159 align:start position:0%
called Anaconda but you don't want
 

00:03:13.159 --> 00:03:15.990 align:start position:0%
called Anaconda but you don't want
Anaconda<00:03:14.159><c> you</c><00:03:14.280><c> want</c><00:03:14.599><c> miniconda</c><00:03:15.560><c> which</c><00:03:15.720><c> is</c>

00:03:15.990 --> 00:03:16.000 align:start position:0%
Anaconda you want miniconda which is
 

00:03:16.000 --> 00:03:19.550 align:start position:0%
Anaconda you want miniconda which is
just<00:03:16.640><c> cond</c><00:03:17.640><c> wait</c><00:03:18.239><c> what</c><00:03:18.599><c> it's</c><00:03:18.760><c> easier</c><00:03:19.120><c> to</c>

00:03:19.550 --> 00:03:19.560 align:start position:0%
just cond wait what it's easier to
 

00:03:19.560 --> 00:03:21.270 align:start position:0%
just cond wait what it's easier to
understand<00:03:19.760><c> if</c><00:03:19.840><c> you</c><00:03:20.000><c> don't</c><00:03:20.440><c> think</c><00:03:20.680><c> about</c><00:03:20.920><c> it</c>

00:03:21.270 --> 00:03:21.280 align:start position:0%
understand if you don't think about it
 

00:03:21.280 --> 00:03:23.789 align:start position:0%
understand if you don't think about it
it's<00:03:21.480><c> like</c><00:03:21.799><c> women</c><00:03:22.799><c> so</c><00:03:23.000><c> once</c><00:03:23.239><c> again</c><00:03:23.480><c> that's</c><00:03:23.640><c> the</c>

00:03:23.789 --> 00:03:23.799 align:start position:0%
it's like women so once again that's the
 

00:03:23.799 --> 00:03:25.630 align:start position:0%
it's like women so once again that's the
letter<00:03:24.120><c> J</c>

00:03:25.630 --> 00:03:25.640 align:start position:0%
letter J
 

00:03:25.640 --> 00:03:29.149 align:start position:0%
letter J
gravel.<00:03:27.120><c> us</c><00:03:28.120><c> just</c><00:03:28.319><c> download</c><00:03:28.680><c> and</c><00:03:28.840><c> run</c><00:03:29.040><c> the</c>

00:03:29.149 --> 00:03:29.159 align:start position:0%
gravel. us just download and run the
 

00:03:29.159 --> 00:03:31.030 align:start position:0%
gravel. us just download and run the
installer<00:03:29.640><c> and</c><00:03:29.840><c> open</c><00:03:30.000><c> a</c><00:03:30.120><c> command</c>

00:03:31.030 --> 00:03:31.040 align:start position:0%
installer and open a command
 

00:03:31.040 --> 00:03:33.910 align:start position:0%
installer and open a command
prompt<00:03:32.040><c> creating</c><00:03:32.400><c> a</c><00:03:32.599><c> cond</c><00:03:33.040><c> environment</c><00:03:33.560><c> is</c><00:03:33.760><c> as</c>

00:03:33.910 --> 00:03:33.920 align:start position:0%
prompt creating a cond environment is as
 

00:03:33.920 --> 00:03:36.830 align:start position:0%
prompt creating a cond environment is as
easy<00:03:34.280><c> as</c><00:03:34.480><c> this</c><00:03:35.319><c> optionally</c><00:03:36.040><c> you</c><00:03:36.159><c> can</c><00:03:36.400><c> specify</c>

00:03:36.830 --> 00:03:36.840 align:start position:0%
easy as this optionally you can specify
 

00:03:36.840 --> 00:03:39.309 align:start position:0%
easy as this optionally you can specify
a<00:03:37.040><c> python</c><00:03:37.439><c> version</c><00:03:38.400><c> I'll</c><00:03:38.599><c> tell</c><00:03:38.799><c> our</c><00:03:39.000><c> test</c>

00:03:39.309 --> 00:03:39.319 align:start position:0%
a python version I'll tell our test
 

00:03:39.319 --> 00:03:41.030 align:start position:0%
a python version I'll tell our test
program<00:03:39.640><c> to</c><00:03:39.760><c> use</c>

00:03:41.030 --> 00:03:41.040 align:start position:0%
program to use
 

00:03:41.040 --> 00:03:43.309 align:start position:0%
program to use
3.12<00:03:42.040><c> here's</c><00:03:42.239><c> one</c><00:03:42.360><c> of</c><00:03:42.480><c> the</c><00:03:42.599><c> many</c><00:03:42.840><c> advantages</c>

00:03:43.309 --> 00:03:43.319 align:start position:0%
3.12 here's one of the many advantages
 

00:03:43.319 --> 00:03:45.910 align:start position:0%
3.12 here's one of the many advantages
of<00:03:43.519><c> K</c><00:03:44.120><c> it</c><00:03:44.280><c> preloads</c><00:03:44.760><c> a</c><00:03:44.840><c> number</c><00:03:45.080><c> of</c><00:03:45.319><c> popular</c><00:03:45.760><c> and</c>

00:03:45.910 --> 00:03:45.920 align:start position:0%
of K it preloads a number of popular and
 

00:03:45.920 --> 00:03:47.550 align:start position:0%
of K it preloads a number of popular and
often<00:03:46.200><c> critical</c><00:03:46.640><c> support</c>

00:03:47.550 --> 00:03:47.560 align:start position:0%
often critical support
 

00:03:47.560 --> 00:03:49.869 align:start position:0%
often critical support
libraries<00:03:48.560><c> back</c><00:03:48.720><c> when</c><00:03:48.879><c> I</c><00:03:49.000><c> showed</c><00:03:49.319><c> you</c><00:03:49.480><c> how</c><00:03:49.640><c> to</c>

00:03:49.869 --> 00:03:49.879 align:start position:0%
libraries back when I showed you how to
 

00:03:49.879 --> 00:03:52.309 align:start position:0%
libraries back when I showed you how to
activate<00:03:50.319><c> our</c><00:03:50.519><c> pocket</c><00:03:50.840><c> grock</c><00:03:51.200><c> Library</c><00:03:52.159><c> what</c>

00:03:52.309 --> 00:03:52.319 align:start position:0%
activate our pocket grock Library what
 

00:03:52.319 --> 00:03:54.509 align:start position:0%
activate our pocket grock Library what
you<00:03:52.480><c> didn't</c><00:03:52.840><c> see</c><00:03:53.280><c> were</c><00:03:53.480><c> all</c><00:03:53.760><c> these</c><00:03:54.000><c> required</c>

00:03:54.509 --> 00:03:54.519 align:start position:0%
you didn't see were all these required
 

00:03:54.519 --> 00:03:56.470 align:start position:0%
you didn't see were all these required
supporting<00:03:55.120><c> libraries</c><00:03:55.760><c> getting</c><00:03:56.000><c> loaded</c><00:03:56.360><c> in</c>

00:03:56.470 --> 00:03:56.480 align:start position:0%
supporting libraries getting loaded in
 

00:03:56.480 --> 00:03:59.550 align:start position:0%
supporting libraries getting loaded in
the<00:03:56.959><c> background</c><00:03:57.959><c> that's</c><00:03:58.120><c> a</c><00:03:58.239><c> good</c><00:03:58.439><c> idea</c><00:03:59.400><c> what</c>

00:03:59.550 --> 00:03:59.560 align:start position:0%
the background that's a good idea what
 

00:03:59.560 --> 00:04:01.869 align:start position:0%
the background that's a good idea what
is<00:03:59.840><c> is</c><00:04:00.239><c> hey</c><00:04:00.439><c> where</c><00:04:00.560><c> are</c><00:04:00.680><c> you</c><00:04:00.920><c> going</c><00:04:01.640><c> I'm</c><00:04:01.760><c> going</c>

00:04:01.869 --> 00:04:01.879 align:start position:0%
is is hey where are you going I'm going
 

00:04:01.879 --> 00:04:05.030 align:start position:0%
is is hey where are you going I'm going
to<00:04:02.000><c> get</c><00:04:02.120><c> loaded</c><00:04:02.519><c> in</c><00:04:02.680><c> the</c>

00:04:05.030 --> 00:04:05.040 align:start position:0%
 
 

00:04:05.040 --> 00:04:06.949 align:start position:0%
 
background<00:04:06.040><c> take</c><00:04:06.200><c> a</c>

00:04:06.949 --> 00:04:06.959 align:start position:0%
background take a
 

00:04:06.959 --> 00:04:12.550 align:start position:0%
background take a
hike<00:04:07.959><c> subscribe</c><00:04:08.560><c> and</c><00:04:08.920><c> like</c><00:04:09.640><c> to</c><00:04:10.079><c> AI</c><00:04:10.560><c> tips</c><00:04:10.920><c> with</c>

00:04:12.550 --> 00:04:12.560 align:start position:0%
hike subscribe and like to AI tips with
 

00:04:12.560 --> 00:04:18.710 align:start position:0%
hike subscribe and like to AI tips with
J<00:04:13.560><c> AI</c><00:04:14.079><c> tips</c><00:04:14.439><c> with</c><00:04:14.640><c> J</c><00:04:15.400><c> hooray</c><00:04:15.959><c> ai</c><00:04:16.479><c> ai</c><00:04:17.040><c> ai</c><00:04:17.519><c> a</c><00:04:17.720><c> i</c><00:04:18.280><c> AI</c>

00:04:18.710 --> 00:04:18.720 align:start position:0%
J AI tips with J hooray ai ai ai a i AI
 

00:04:18.720 --> 00:04:20.310 align:start position:0%
J AI tips with J hooray ai ai ai a i AI
tips<00:04:19.120><c> with</c>

00:04:20.310 --> 00:04:20.320 align:start position:0%
tips with
 

00:04:20.320 --> 00:04:23.030 align:start position:0%
tips with
J<00:04:21.320><c> AI</c><00:04:21.600><c> tips</c><00:04:21.840><c> with</c><00:04:21.959><c> J</c><00:04:22.199><c> is</c><00:04:22.280><c> a</c><00:04:22.479><c> copyrighted</c>

00:04:23.030 --> 00:04:23.040 align:start position:0%
J AI tips with J is a copyrighted
 

00:04:23.040 --> 00:04:26.550 align:start position:0%
J AI tips with J is a copyrighted
production<00:04:23.400><c> of</c><00:04:24.240><c> j.g.</c><00:04:25.240><c> us</c><00:04:26.080><c> all</c><00:04:26.280><c> rights</c>

00:04:26.550 --> 00:04:26.560 align:start position:0%
production of j.g. us all rights
 

00:04:26.560 --> 00:04:32.960 align:start position:0%
production of j.g. us all rights
reserved<00:04:27.040><c> by</c><00:04:27.639><c> AI</c><00:04:28.080><c> tips</c><00:04:28.479><c> with</c><00:04:28.680><c> j</c><00:04:29.960><c> oh</c>

