WEBVTT
Kind: captions
Language: en

00:00:00.320 --> 00:00:02.230 align:start position:0%
 
hey<00:00:00.520><c> everyone</c><00:00:00.880><c> it's</c><00:00:01.079><c> ashre</c><00:00:01.760><c> and</c><00:00:01.880><c> today</c><00:00:02.120><c> we're</c>

00:00:02.230 --> 00:00:02.240 align:start position:0%
hey everyone it's ashre and today we're
 

00:00:02.240 --> 00:00:03.750 align:start position:0%
hey everyone it's ashre and today we're
going<00:00:02.320><c> to</c><00:00:02.440><c> be</c><00:00:02.560><c> having</c><00:00:02.760><c> a</c><00:00:02.919><c> little</c><00:00:03.120><c> bit</c><00:00:03.240><c> of</c><00:00:03.399><c> fun</c>

00:00:03.750 --> 00:00:03.760 align:start position:0%
going to be having a little bit of fun
 

00:00:03.760 --> 00:00:05.710 align:start position:0%
going to be having a little bit of fun
with<00:00:03.919><c> local</c><00:00:04.240><c> language</c><00:00:04.600><c> models</c><00:00:05.400><c> we're</c><00:00:05.600><c> going</c>

00:00:05.710 --> 00:00:05.720 align:start position:0%
with local language models we're going
 

00:00:05.720 --> 00:00:07.869 align:start position:0%
with local language models we're going
to<00:00:05.799><c> be</c><00:00:05.920><c> using</c><00:00:06.279><c> open</c><00:00:06.600><c> hermis</c><00:00:06.919><c> and</c><00:00:07.080><c> AMA</c><00:00:07.600><c> for</c>

00:00:07.869 --> 00:00:07.879 align:start position:0%
to be using open hermis and AMA for
 

00:00:07.879 --> 00:00:09.790 align:start position:0%
to be using open hermis and AMA for
World<00:00:08.160><c> building</c><00:00:08.760><c> which</c><00:00:08.880><c> is</c><00:00:09.000><c> the</c><00:00:09.240><c> process</c><00:00:09.639><c> of</c>

00:00:09.790 --> 00:00:09.800 align:start position:0%
World building which is the process of
 

00:00:09.800 --> 00:00:11.589 align:start position:0%
World building which is the process of
creating<00:00:10.200><c> completely</c><00:00:10.639><c> imaginary</c><00:00:11.200><c> fictional</c>

00:00:11.589 --> 00:00:11.599 align:start position:0%
creating completely imaginary fictional
 

00:00:11.599 --> 00:00:14.070 align:start position:0%
creating completely imaginary fictional
universes<00:00:12.480><c> so</c><00:00:12.639><c> if</c><00:00:12.719><c> you</c><00:00:12.920><c> a</c><00:00:13.120><c> fan</c><00:00:13.440><c> of</c><00:00:13.719><c> Game</c><00:00:13.880><c> of</c>

00:00:14.070 --> 00:00:14.080 align:start position:0%
universes so if you a fan of Game of
 

00:00:14.080 --> 00:00:16.349 align:start position:0%
universes so if you a fan of Game of
Thrones<00:00:14.519><c> Star</c><00:00:14.799><c> Wars</c><00:00:15.080><c> Star</c><00:00:15.360><c> Trek</c><00:00:16.000><c> this</c><00:00:16.080><c> is</c><00:00:16.199><c> a</c>

00:00:16.349 --> 00:00:16.359 align:start position:0%
Thrones Star Wars Star Trek this is a
 

00:00:16.359 --> 00:00:18.029 align:start position:0%
Thrones Star Wars Star Trek this is a
great<00:00:16.560><c> way</c><00:00:16.680><c> to</c><00:00:16.880><c> create</c><00:00:17.119><c> your</c><00:00:17.279><c> own</c><00:00:17.480><c> world</c>

00:00:18.029 --> 00:00:18.039 align:start position:0%
great way to create your own world
 

00:00:18.039 --> 00:00:20.750 align:start position:0%
great way to create your own world
completely<00:00:18.480><c> local</c><00:00:18.880><c> completely</c><00:00:19.359><c> private</c><00:00:20.279><c> and</c>

00:00:20.750 --> 00:00:20.760 align:start position:0%
completely local completely private and
 

00:00:20.760 --> 00:00:22.349 align:start position:0%
completely local completely private and
while<00:00:20.960><c> we're</c><00:00:21.160><c> going</c><00:00:21.240><c> to</c><00:00:21.359><c> be</c><00:00:21.600><c> only</c><00:00:21.840><c> generating</c>

00:00:22.349 --> 00:00:22.359 align:start position:0%
while we're going to be only generating
 

00:00:22.359 --> 00:00:24.750 align:start position:0%
while we're going to be only generating
text<00:00:22.760><c> right</c><00:00:22.920><c> now</c><00:00:23.439><c> as</c><00:00:23.720><c> text</c><00:00:24.000><c> to</c><00:00:24.160><c> video</c><00:00:24.400><c> and</c><00:00:24.519><c> text</c>

00:00:24.750 --> 00:00:24.760 align:start position:0%
text right now as text to video and text
 

00:00:24.760 --> 00:00:27.109 align:start position:0%
text right now as text to video and text
to<00:00:24.960><c> Audio</c><00:00:25.279><c> models</c><00:00:25.720><c> become</c><00:00:26.039><c> available</c><00:00:26.920><c> we're</c>

00:00:27.109 --> 00:00:27.119 align:start position:0%
to Audio models become available we're
 

00:00:27.119 --> 00:00:29.230 align:start position:0%
to Audio models become available we're
going<00:00:27.199><c> to</c><00:00:27.320><c> be</c><00:00:27.439><c> generating</c><00:00:28.240><c> text</c><00:00:28.720><c> audio</c><00:00:29.039><c> and</c>

00:00:29.230 --> 00:00:29.240 align:start position:0%
going to be generating text audio and
 

00:00:29.240 --> 00:00:31.870 align:start position:0%
going to be generating text audio and
video<00:00:29.560><c> all</c><00:00:30.119><c> together</c><00:00:30.439><c> using</c><00:00:30.800><c> this</c><00:00:31.000><c> guide</c><00:00:31.679><c> so</c>

00:00:31.870 --> 00:00:31.880 align:start position:0%
video all together using this guide so
 

00:00:31.880 --> 00:00:33.790 align:start position:0%
video all together using this guide so
to<00:00:32.079><c> follow</c><00:00:32.439><c> along</c><00:00:33.079><c> all</c><00:00:33.280><c> the</c><00:00:33.440><c> code</c><00:00:33.640><c> you're</c>

00:00:33.790 --> 00:00:33.800 align:start position:0%
to follow along all the code you're
 

00:00:33.800 --> 00:00:35.590 align:start position:0%
to follow along all the code you're
going<00:00:33.879><c> to</c><00:00:34.040><c> need</c><00:00:34.320><c> is</c><00:00:34.440><c> in</c><00:00:34.559><c> the</c><00:00:34.680><c> F</c><00:00:34.920><c> data</c><00:00:35.160><c> repo</c>

00:00:35.590 --> 00:00:35.600 align:start position:0%
going to need is in the F data repo
 

00:00:35.600 --> 00:00:37.750 align:start position:0%
going to need is in the F data repo
Under<00:00:35.840><c> The</c><00:00:36.040><c> cookbook</c><00:00:36.760><c> World</c><00:00:37.079><c> building</c>

00:00:37.750 --> 00:00:37.760 align:start position:0%
Under The cookbook World building
 

00:00:37.760 --> 00:00:40.830 align:start position:0%
Under The cookbook World building
section<00:00:38.760><c> so</c><00:00:39.399><c> for</c><00:00:39.719><c> can</c><00:00:39.879><c> clone</c><00:00:40.160><c> the</c><00:00:40.320><c> repository</c>

00:00:40.830 --> 00:00:40.840 align:start position:0%
section so for can clone the repository
 

00:00:40.840 --> 00:00:43.069 align:start position:0%
section so for can clone the repository
and<00:00:40.960><c> open</c><00:00:41.120><c> it</c><00:00:41.280><c> up</c><00:00:41.399><c> in</c><00:00:41.520><c> your</c><00:00:41.800><c> editor</c><00:00:42.800><c> after</c>

00:00:43.069 --> 00:00:43.079 align:start position:0%
and open it up in your editor after
 

00:00:43.079 --> 00:00:46.590 align:start position:0%
and open it up in your editor after
you've<00:00:43.320><c> got</c><00:00:43.800><c> the</c><00:00:44.120><c> repository</c><00:00:45.199><c> open</c><00:00:46.199><c> uh</c><00:00:46.360><c> run</c>

00:00:46.590 --> 00:00:46.600 align:start position:0%
you've got the repository open uh run
 

00:00:46.600 --> 00:00:49.270 align:start position:0%
you've got the repository open uh run
the<00:00:46.760><c> open</c><00:00:47.039><c> her</c><00:00:47.280><c> miss</c><00:00:47.399><c> model</c><00:00:47.719><c> using</c><00:00:48.039><c> AMA</c><00:00:49.000><c> create</c>

00:00:49.270 --> 00:00:49.280 align:start position:0%
the open her miss model using AMA create
 

00:00:49.280 --> 00:00:51.069 align:start position:0%
the open her miss model using AMA create
your<00:00:49.440><c> own</c><00:00:49.600><c> virtual</c><00:00:50.000><c> environment</c><00:00:50.559><c> and</c><00:00:50.719><c> install</c>

00:00:51.069 --> 00:00:51.079 align:start position:0%
your own virtual environment and install
 

00:00:51.079 --> 00:00:52.869 align:start position:0%
your own virtual environment and install
the<00:00:51.199><c> required</c><00:00:51.520><c> libraries</c><00:00:52.480><c> then</c><00:00:52.640><c> we're</c><00:00:52.760><c> going</c>

00:00:52.869 --> 00:00:52.879 align:start position:0%
the required libraries then we're going
 

00:00:52.879 --> 00:00:55.150 align:start position:0%
the required libraries then we're going
to<00:00:53.280><c> run</c><00:00:53.559><c> this</c><00:00:53.719><c> application</c><00:00:54.199><c> on</c><00:00:54.320><c> streamlet</c><00:00:54.920><c> so</c>

00:00:55.150 --> 00:00:55.160 align:start position:0%
to run this application on streamlet so
 

00:00:55.160 --> 00:00:57.790 align:start position:0%
to run this application on streamlet so
let's<00:00:55.359><c> kick</c><00:00:55.640><c> that</c><00:00:56.039><c> off</c><00:00:57.039><c> and</c><00:00:57.199><c> it's</c><00:00:57.359><c> going</c><00:00:57.480><c> to</c>

00:00:57.790 --> 00:00:57.800 align:start position:0%
let's kick that off and it's going to
 

00:00:57.800 --> 00:01:00.069 align:start position:0%
let's kick that off and it's going to
kick<00:00:58.039><c> off</c><00:00:58.239><c> our</c><00:00:58.519><c> world</c><00:00:58.800><c> building</c><00:00:59.239><c> application</c>

00:01:00.069 --> 00:01:00.079 align:start position:0%
kick off our world building application
 

00:01:00.079 --> 00:01:02.229 align:start position:0%
kick off our world building application
so<00:01:00.239><c> it's</c><00:01:00.359><c> going</c><00:01:00.480><c> to</c><00:01:00.760><c> ask</c><00:01:01.039><c> for</c><00:01:01.399><c> a</c><00:01:01.840><c> small</c>

00:01:02.229 --> 00:01:02.239 align:start position:0%
so it's going to ask for a small
 

00:01:02.239 --> 00:01:04.350 align:start position:0%
so it's going to ask for a small
description<00:01:02.719><c> so</c><00:01:02.879><c> for</c><00:01:03.079><c> us</c><00:01:03.280><c> we're</c><00:01:03.440><c> going</c><00:01:03.519><c> to</c><00:01:03.680><c> say</c>

00:01:04.350 --> 00:01:04.360 align:start position:0%
description so for us we're going to say
 

00:01:04.360 --> 00:01:06.550 align:start position:0%
description so for us we're going to say
an<00:01:04.559><c> advanced</c><00:01:04.920><c> futuristic</c><00:01:05.519><c> city</c><00:01:05.840><c> on</c><00:01:06.000><c> a</c><00:01:06.159><c> distant</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
an advanced futuristic city on a distant
 

00:01:06.560 --> 00:01:08.390 align:start position:0%
an advanced futuristic city on a distant
planet<00:01:06.960><c> with</c><00:01:07.119><c> one</c><00:01:07.320><c> Island</c><00:01:07.640><c> it's</c><00:01:07.759><c> got</c><00:01:07.960><c> a</c><00:01:08.080><c> dark</c>

00:01:08.390 --> 00:01:08.400 align:start position:0%
planet with one Island it's got a dark
 

00:01:08.400 --> 00:01:10.510 align:start position:0%
planet with one Island it's got a dark
history<00:01:08.680><c> and</c><00:01:08.799><c> a</c><00:01:09.000><c> population</c><00:01:09.400><c> of</c><00:01:09.520><c> 1</c><00:01:09.720><c> trillion</c>

00:01:10.510 --> 00:01:10.520 align:start position:0%
history and a population of 1 trillion
 

00:01:10.520 --> 00:01:13.070 align:start position:0%
history and a population of 1 trillion
so<00:01:10.680><c> you</c><00:01:10.799><c> can</c><00:01:11.040><c> customize</c><00:01:11.600><c> this</c><00:01:11.720><c> to</c><00:01:12.320><c> whatever</c>

00:01:13.070 --> 00:01:13.080 align:start position:0%
so you can customize this to whatever
 

00:01:13.080 --> 00:01:14.870 align:start position:0%
so you can customize this to whatever
type<00:01:13.280><c> of</c><00:01:13.400><c> world</c><00:01:13.640><c> you</c><00:01:13.759><c> want</c><00:01:13.840><c> to</c><00:01:13.960><c> generate</c><00:01:14.759><c> then</c>

00:01:14.870 --> 00:01:14.880 align:start position:0%
type of world you want to generate then
 

00:01:14.880 --> 00:01:16.270 align:start position:0%
type of world you want to generate then
you<00:01:15.040><c> click</c><00:01:15.200><c> on</c><00:01:15.320><c> the</c><00:01:15.400><c> generate</c><00:01:15.759><c> World</c><00:01:16.040><c> button</c>

00:01:16.270 --> 00:01:16.280 align:start position:0%
you click on the generate World button
 

00:01:16.280 --> 00:01:18.230 align:start position:0%
you click on the generate World button
and<00:01:16.400><c> it'll</c><00:01:16.640><c> generate</c><00:01:17.040><c> that</c><00:01:17.200><c> world</c><00:01:17.439><c> for</c><00:01:17.640><c> you</c>

00:01:18.230 --> 00:01:18.240 align:start position:0%
and it'll generate that world for you
 

00:01:18.240 --> 00:01:20.789 align:start position:0%
and it'll generate that world for you
again<00:01:18.479><c> you</c><00:01:18.640><c> can</c><00:01:18.880><c> use</c><00:01:19.560><c> U</c><00:01:19.920><c> any</c><00:01:20.159><c> of</c><00:01:20.320><c> the</c><00:01:20.479><c> local</c>

00:01:20.789 --> 00:01:20.799 align:start position:0%
again you can use U any of the local
 

00:01:20.799 --> 00:01:23.030 align:start position:0%
again you can use U any of the local
models<00:01:21.200><c> you'd</c><00:01:21.439><c> like</c><00:01:22.159><c> and</c><00:01:22.360><c> as</c><00:01:22.479><c> I</c><00:01:22.640><c> mentioned</c><00:01:22.960><c> in</c>

00:01:23.030 --> 00:01:23.040 align:start position:0%
models you'd like and as I mentioned in
 

00:01:23.040 --> 00:01:24.550 align:start position:0%
models you'd like and as I mentioned in
the<00:01:23.159><c> future</c><00:01:23.439><c> we</c><00:01:23.560><c> want</c><00:01:23.640><c> to</c><00:01:23.880><c> add</c><00:01:24.159><c> video</c><00:01:24.439><c> and</c>

00:01:24.550 --> 00:01:24.560 align:start position:0%
the future we want to add video and
 

00:01:24.560 --> 00:01:27.030 align:start position:0%
the future we want to add video and
audio<00:01:24.880><c> models</c><00:01:25.240><c> over</c><00:01:25.479><c> here</c><00:01:25.680><c> as</c><00:01:25.799><c> well</c><00:01:26.720><c> so</c><00:01:26.880><c> it's</c>

00:01:27.030 --> 00:01:27.040 align:start position:0%
audio models over here as well so it's
 

00:01:27.040 --> 00:01:28.990 align:start position:0%
audio models over here as well so it's
generated<00:01:27.400><c> a</c><00:01:27.560><c> world</c><00:01:27.840><c> called</c><00:01:28.119><c> astral</c><00:01:28.560><c> ale</c><00:01:28.799><c> for</c>

00:01:28.990 --> 00:01:29.000 align:start position:0%
generated a world called astral ale for
 

00:01:29.000 --> 00:01:31.710 align:start position:0%
generated a world called astral ale for
us<00:01:29.720><c> uh</c><00:01:30.000><c> the</c><00:01:30.119><c> currency's</c><00:01:30.640><c> quantum</c><00:01:31.119><c> credit</c><00:01:31.560><c> it</c>

00:01:31.710 --> 00:01:31.720 align:start position:0%
us uh the currency's quantum credit it
 

00:01:31.720 --> 00:01:34.069 align:start position:0%
us uh the currency's quantum credit it
uses<00:01:32.240><c> the</c><00:01:32.439><c> galactic</c><00:01:33.000><c> standard</c><00:01:33.479><c> as</c><00:01:33.600><c> its</c>

00:01:34.069 --> 00:01:34.079 align:start position:0%
uses the galactic standard as its
 

00:01:34.079 --> 00:01:36.870 align:start position:0%
uses the galactic standard as its
language<00:01:34.840><c> and</c><00:01:34.960><c> it's</c><00:01:35.159><c> got</c><00:01:35.320><c> a</c><00:01:35.560><c> history</c><00:01:36.560><c> it's</c>

00:01:36.870 --> 00:01:36.880 align:start position:0%
language and it's got a history it's
 

00:01:36.880 --> 00:01:38.670 align:start position:0%
language and it's got a history it's
generated<00:01:37.360><c> all</c><00:01:37.520><c> the</c><00:01:37.680><c> wars</c><00:01:38.119><c> and</c><00:01:38.320><c> like</c><00:01:38.439><c> the</c><00:01:38.560><c> kind</c>

00:01:38.670 --> 00:01:38.680 align:start position:0%
generated all the wars and like the kind
 

00:01:38.680 --> 00:01:40.429 align:start position:0%
generated all the wars and like the kind
of<00:01:38.799><c> drugs</c><00:01:39.159><c> people</c><00:01:39.360><c> use</c><00:01:39.560><c> on</c><00:01:39.720><c> it</c><00:01:39.880><c> so</c><00:01:40.040><c> you</c><00:01:40.159><c> could</c>

00:01:40.429 --> 00:01:40.439 align:start position:0%
of drugs people use on it so you could
 

00:01:40.439 --> 00:01:43.149 align:start position:0%
of drugs people use on it so you could
you<00:01:40.560><c> could</c><00:01:40.759><c> go</c><00:01:40.960><c> crazy</c><00:01:41.320><c> with</c><00:01:41.479><c> this</c><00:01:42.399><c> um</c><00:01:42.759><c> so</c><00:01:42.960><c> let's</c>

00:01:43.149 --> 00:01:43.159 align:start position:0%
you could go crazy with this um so let's
 

00:01:43.159 --> 00:01:46.310 align:start position:0%
you could go crazy with this um so let's
explore<00:01:43.600><c> this</c><00:01:43.720><c> world</c><00:01:44.119><c> together</c><00:01:44.960><c> um</c><00:01:45.439><c> and</c><00:01:46.119><c> let's</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
explore this world together um and let's
 

00:01:46.320 --> 00:01:48.230 align:start position:0%
explore this world together um and let's
first<00:01:46.520><c> say</c><00:01:46.719><c> tell</c><00:01:46.880><c> me</c><00:01:47.040><c> about</c><00:01:47.280><c> this</c>

00:01:48.230 --> 00:01:48.240 align:start position:0%
first say tell me about this
 

00:01:48.240 --> 00:01:52.389 align:start position:0%
first say tell me about this
world<00:01:49.240><c> and</c><00:01:49.399><c> you</c><00:01:49.560><c> can</c><00:01:50.240><c> go</c><00:01:50.960><c> as</c><00:01:51.520><c> um</c><00:01:51.880><c> you</c><00:01:51.960><c> know</c><00:01:52.200><c> as</c>

00:01:52.389 --> 00:01:52.399 align:start position:0%
world and you can go as um you know as
 

00:01:52.399 --> 00:01:54.030 align:start position:0%
world and you can go as um you know as
deep<00:01:52.680><c> as</c><00:01:52.799><c> you</c><00:01:52.920><c> want</c><00:01:53.240><c> with</c><00:01:53.399><c> these</c><00:01:53.560><c> stories</c><00:01:53.920><c> so</c>

00:01:54.030 --> 00:01:54.040 align:start position:0%
deep as you want with these stories so
 

00:01:54.040 --> 00:01:55.709 align:start position:0%
deep as you want with these stories so
this<00:01:54.159><c> is</c><00:01:54.280><c> completely</c><00:01:54.680><c> fictional</c><00:01:55.159><c> so</c><00:01:55.520><c> feel</c>

00:01:55.709 --> 00:01:55.719 align:start position:0%
this is completely fictional so feel
 

00:01:55.719 --> 00:01:58.429 align:start position:0%
this is completely fictional so feel
free<00:01:55.920><c> to</c><00:01:56.079><c> play</c><00:01:56.280><c> around</c><00:01:56.600><c> with</c><00:01:56.759><c> it</c>

00:01:58.429 --> 00:01:58.439 align:start position:0%
free to play around with it
 

00:01:58.439 --> 00:02:01.270 align:start position:0%
free to play around with it
um<00:01:59.439><c> and</c><00:01:59.600><c> it'll</c><00:02:00.079><c> give</c><00:02:00.240><c> us</c><00:02:00.439><c> like</c><00:02:00.560><c> full</c><00:02:00.840><c> stories</c>

00:02:01.270 --> 00:02:01.280 align:start position:0%
um and it'll give us like full stories
 

00:02:01.280 --> 00:02:03.310 align:start position:0%
um and it'll give us like full stories
and<00:02:01.479><c> as</c><00:02:01.600><c> we</c><00:02:01.759><c> start</c><00:02:02.079><c> adding</c><00:02:02.479><c> like</c><00:02:02.719><c> audio</c><00:02:03.079><c> and</c>

00:02:03.310 --> 00:02:03.320 align:start position:0%
and as we start adding like audio and
 

00:02:03.320 --> 00:02:05.590 align:start position:0%
and as we start adding like audio and
Vis<00:02:03.719><c> to</c><00:02:04.000><c> video</c><00:02:04.320><c> to</c><00:02:04.560><c> this</c><00:02:05.000><c> it's</c><00:02:05.119><c> going</c><00:02:05.240><c> to</c><00:02:05.360><c> be</c><00:02:05.479><c> a</c>

00:02:05.590 --> 00:02:05.600 align:start position:0%
Vis to video to this it's going to be a
 

00:02:05.600 --> 00:02:09.710 align:start position:0%
Vis to video to this it's going to be a
lot<00:02:05.719><c> of</c><00:02:05.880><c> fun</c><00:02:06.799><c> and</c><00:02:06.960><c> then</c><00:02:07.119><c> we</c><00:02:07.240><c> can</c><00:02:07.399><c> say</c><00:02:07.840><c> uh</c><00:02:08.080><c> okay</c>

00:02:09.710 --> 00:02:09.720 align:start position:0%
lot of fun and then we can say uh okay
 

00:02:09.720 --> 00:02:12.710 align:start position:0%
lot of fun and then we can say uh okay
so<00:02:10.720><c> let's</c><00:02:10.959><c> say</c><00:02:11.239><c> what</c><00:02:11.360><c> do</c><00:02:11.560><c> people</c><00:02:11.760><c> do</c><00:02:11.959><c> all</c>

00:02:12.710 --> 00:02:12.720 align:start position:0%
so let's say what do people do all
 

00:02:12.720 --> 00:02:15.830 align:start position:0%
so let's say what do people do all
day<00:02:13.720><c> you</c><00:02:13.840><c> know</c><00:02:14.280><c> give</c><00:02:14.440><c> it</c><00:02:14.640><c> some</c><00:02:15.040><c> uh</c>

00:02:15.830 --> 00:02:15.840 align:start position:0%
day you know give it some uh
 

00:02:15.840 --> 00:02:20.150 align:start position:0%
day you know give it some uh
backstory<00:02:16.840><c> and</c><00:02:17.400><c> uh</c><00:02:18.400><c> you</c><00:02:18.519><c> know</c><00:02:18.959><c> this</c><00:02:19.599><c> set</c><00:02:19.959><c> the</c>

00:02:20.150 --> 00:02:20.160 align:start position:0%
backstory and uh you know this set the
 

00:02:20.160 --> 00:02:22.509 align:start position:0%
backstory and uh you know this set the
stage<00:02:20.560><c> for</c><00:02:21.080><c> Our</c><00:02:21.360><c> Kind</c><00:02:21.599><c> like</c><00:02:21.720><c> the</c><00:02:21.840><c> world</c><00:02:22.120><c> we're</c>

00:02:22.509 --> 00:02:22.519 align:start position:0%
stage for Our Kind like the world we're
 

00:02:22.519 --> 00:02:25.110 align:start position:0%
stage for Our Kind like the world we're
working

00:02:25.110 --> 00:02:25.120 align:start position:0%
 
 

00:02:25.120 --> 00:02:27.790 align:start position:0%
 
with<00:02:26.120><c> all</c><00:02:26.319><c> right</c><00:02:27.080><c> and</c><00:02:27.280><c> then</c><00:02:27.400><c> we're</c><00:02:27.519><c> going</c><00:02:27.640><c> to</c>

00:02:27.790 --> 00:02:27.800 align:start position:0%
with all right and then we're going to
 

00:02:27.800 --> 00:02:30.070 align:start position:0%
with all right and then we're going to
ask<00:02:28.080><c> a</c><00:02:28.200><c> little</c><00:02:28.400><c> bit</c><00:02:28.560><c> of</c><00:02:29.000><c> um</c><00:02:29.480><c> you</c><00:02:29.599><c> know</c><00:02:29.920><c> we're</c>

00:02:30.070 --> 00:02:30.080 align:start position:0%
ask a little bit of um you know we're
 

00:02:30.080 --> 00:02:31.630 align:start position:0%
ask a little bit of um you know we're
going<00:02:30.160><c> to</c><00:02:30.319><c> ask</c><00:02:30.519><c> it</c><00:02:30.680><c> to</c><00:02:30.800><c> share</c><00:02:31.080><c> a</c><00:02:31.280><c> personal</c>

00:02:31.630 --> 00:02:31.640 align:start position:0%
going to ask it to share a personal
 

00:02:31.640 --> 00:02:34.509 align:start position:0%
going to ask it to share a personal
story<00:02:31.920><c> from</c><00:02:32.080><c> this</c><00:02:32.280><c> planet</c><00:02:33.040><c> tell</c><00:02:33.239><c> me</c>

00:02:34.509 --> 00:02:34.519 align:start position:0%
story from this planet tell me
 

00:02:34.519 --> 00:02:38.390 align:start position:0%
story from this planet tell me
a<00:02:35.640><c> story</c><00:02:36.640><c> about</c><00:02:36.920><c> a</c><00:02:37.160><c> person</c><00:02:37.959><c> and</c><00:02:38.080><c> let's</c><00:02:38.239><c> see</c>

00:02:38.390 --> 00:02:38.400 align:start position:0%
a story about a person and let's see
 

00:02:38.400 --> 00:02:41.030 align:start position:0%
a story about a person and let's see
what<00:02:38.480><c> it</c><00:02:38.599><c> comes</c><00:02:38.840><c> up</c><00:02:39.040><c> with</c><00:02:39.680><c> okay</c><00:02:39.879><c> so</c><00:02:40.120><c> it's</c><00:02:40.879><c> it's</c>

00:02:41.030 --> 00:02:41.040 align:start position:0%
what it comes up with okay so it's it's
 

00:02:41.040 --> 00:02:42.229 align:start position:0%
what it comes up with okay so it's it's
telling<00:02:41.319><c> the</c><00:02:41.440><c> story</c><00:02:41.680><c> about</c><00:02:41.840><c> a</c><00:02:41.959><c> young</c>

00:02:42.229 --> 00:02:42.239 align:start position:0%
telling the story about a young
 

00:02:42.239 --> 00:02:45.149 align:start position:0%
telling the story about a young
scientist<00:02:42.959><c> named</c><00:02:43.400><c> lra</c><00:02:44.400><c> really</c><00:02:44.680><c> like</c><00:02:44.959><c> that</c>

00:02:45.149 --> 00:02:45.159 align:start position:0%
scientist named lra really like that
 

00:02:45.159 --> 00:02:48.509 align:start position:0%
scientist named lra really like that
name<00:02:46.159><c> and</c><00:02:46.400><c> she's</c><00:02:46.680><c> pursuing</c><00:02:47.000><c> a</c><00:02:47.120><c> career</c><00:02:47.360><c> in</c>

00:02:48.509 --> 00:02:48.519 align:start position:0%
name and she's pursuing a career in
 

00:02:48.519 --> 00:02:50.949 align:start position:0%
name and she's pursuing a career in
astrophysics<00:02:49.519><c> okay</c><00:02:49.879><c> great</c><00:02:50.440><c> and</c><00:02:50.599><c> you</c><00:02:50.720><c> know</c><00:02:50.840><c> it</c>

00:02:50.949 --> 00:02:50.959 align:start position:0%
astrophysics okay great and you know it
 

00:02:50.959 --> 00:02:52.790 align:start position:0%
astrophysics okay great and you know it
writes<00:02:51.200><c> up</c><00:02:51.360><c> the</c><00:02:51.480><c> full</c><00:02:51.720><c> story</c><00:02:52.080><c> so</c><00:02:52.480><c> you</c><00:02:52.599><c> know</c><00:02:52.720><c> if</c>

00:02:52.790 --> 00:02:52.800 align:start position:0%
writes up the full story so you know if
 

00:02:52.800 --> 00:02:54.270 align:start position:0%
writes up the full story so you know if
you're<00:02:52.920><c> a</c><00:02:53.000><c> fan</c><00:02:53.159><c> of</c><00:02:53.280><c> reading</c><00:02:53.640><c> this</c><00:02:53.840><c> a</c><00:02:53.959><c> great</c><00:02:54.159><c> way</c>

00:02:54.270 --> 00:02:54.280 align:start position:0%
you're a fan of reading this a great way
 

00:02:54.280 --> 00:02:56.030 align:start position:0%
you're a fan of reading this a great way
to<00:02:54.440><c> kind</c><00:02:54.560><c> of</c><00:02:54.760><c> like</c><00:02:54.879><c> generate</c><00:02:55.280><c> your</c><00:02:55.440><c> own</c>

00:02:56.030 --> 00:02:56.040 align:start position:0%
to kind of like generate your own
 

00:02:56.040 --> 00:02:58.750 align:start position:0%
to kind of like generate your own
stories<00:02:57.040><c> and</c><00:02:57.200><c> then</c>

00:02:58.750 --> 00:02:58.760 align:start position:0%
stories and then
 

00:02:58.760 --> 00:03:00.270 align:start position:0%
stories and then
um

00:03:00.270 --> 00:03:00.280 align:start position:0%
um
 

00:03:00.280 --> 00:03:07.270 align:start position:0%
um
tell<00:03:00.480><c> me</c><00:03:00.720><c> about</c><00:03:01.599><c> the</c><00:03:02.239><c> leaders</c><00:03:03.239><c> of</c><00:03:03.480><c> this</c>

00:03:07.270 --> 00:03:07.280 align:start position:0%
 
 

00:03:07.280 --> 00:03:09.550 align:start position:0%
 
world<00:03:08.280><c> yeah</c><00:03:08.440><c> so</c><00:03:08.840><c> this</c><00:03:08.959><c> is</c><00:03:09.200><c> just</c><00:03:09.360><c> an</c>

00:03:09.550 --> 00:03:09.560 align:start position:0%
world yeah so this is just an
 

00:03:09.560 --> 00:03:10.910 align:start position:0%
world yeah so this is just an
introduction<00:03:10.000><c> to</c><00:03:10.159><c> World</c><00:03:10.360><c> building</c><00:03:10.720><c> how</c><00:03:10.840><c> you</c>

00:03:10.910 --> 00:03:10.920 align:start position:0%
introduction to World building how you
 

00:03:10.920 --> 00:03:13.110 align:start position:0%
introduction to World building how you
can<00:03:11.080><c> use</c><00:03:11.319><c> completely</c><00:03:11.760><c> local</c><00:03:12.319><c> language</c><00:03:12.760><c> models</c>

00:03:13.110 --> 00:03:13.120 align:start position:0%
can use completely local language models
 

00:03:13.120 --> 00:03:14.309 align:start position:0%
can use completely local language models
for<00:03:13.239><c> a</c><00:03:13.319><c> little</c><00:03:13.480><c> bit</c><00:03:13.640><c> of</c><00:03:13.720><c> fun</c><00:03:14.000><c> you</c><00:03:14.120><c> can</c>

00:03:14.309 --> 00:03:14.319 align:start position:0%
for a little bit of fun you can
 

00:03:14.319 --> 00:03:15.910 align:start position:0%
for a little bit of fun you can
customize<00:03:14.760><c> it</c><00:03:14.920><c> not</c><00:03:15.080><c> just</c><00:03:15.200><c> for</c><00:03:15.360><c> World</c><00:03:15.560><c> building</c>

00:03:15.910 --> 00:03:15.920 align:start position:0%
customize it not just for World building
 

00:03:15.920 --> 00:03:17.869 align:start position:0%
customize it not just for World building
you<00:03:16.000><c> can</c><00:03:16.120><c> use</c><00:03:16.319><c> this</c><00:03:16.480><c> cookbook</c><00:03:16.959><c> for</c><00:03:17.319><c> any</c><00:03:17.640><c> type</c>

00:03:17.869 --> 00:03:17.879 align:start position:0%
you can use this cookbook for any type
 

00:03:17.879 --> 00:03:20.710 align:start position:0%
you can use this cookbook for any type
of<00:03:18.159><c> creative</c><00:03:18.599><c> exercise</c><00:03:19.080><c> you</c><00:03:19.239><c> want</c><00:03:20.239><c> um</c><00:03:20.440><c> and</c><00:03:20.599><c> as</c>

00:03:20.710 --> 00:03:20.720 align:start position:0%
of creative exercise you want um and as
 

00:03:20.720 --> 00:03:22.589 align:start position:0%
of creative exercise you want um and as
I<00:03:20.840><c> mentioned</c><00:03:21.400><c> as</c><00:03:21.599><c> the</c><00:03:21.760><c> text</c><00:03:22.040><c> to</c><00:03:22.239><c> video</c><00:03:22.480><c> and</c>

00:03:22.589 --> 00:03:22.599 align:start position:0%
I mentioned as the text to video and
 

00:03:22.599 --> 00:03:25.270 align:start position:0%
I mentioned as the text to video and
text<00:03:22.840><c> to</c><00:03:23.000><c> Audio</c><00:03:23.280><c> models</c><00:03:23.959><c> uh</c><00:03:24.280><c> come</c><00:03:24.480><c> along</c><00:03:25.080><c> we're</c>

00:03:25.270 --> 00:03:25.280 align:start position:0%
text to Audio models uh come along we're
 

00:03:25.280 --> 00:03:27.430 align:start position:0%
text to Audio models uh come along we're
going<00:03:25.360><c> to</c><00:03:25.480><c> be</c><00:03:25.599><c> adding</c><00:03:26.000><c> video</c><00:03:26.440><c> and</c><00:03:26.799><c> uh</c><00:03:27.040><c> audio</c>

00:03:27.430 --> 00:03:27.440 align:start position:0%
going to be adding video and uh audio
 

00:03:27.440 --> 00:03:30.670 align:start position:0%
going to be adding video and uh audio
and<00:03:27.720><c> background</c><00:03:28.159><c> sound</c><00:03:28.480><c> effects</c><00:03:29.239><c> to</c><00:03:29.439><c> these</c><00:03:30.120><c> um</c>

00:03:30.670 --> 00:03:30.680 align:start position:0%
and background sound effects to these um
 

00:03:30.680 --> 00:03:32.869 align:start position:0%
and background sound effects to these um
to<00:03:30.840><c> our</c><00:03:31.040><c> world</c><00:03:31.280><c> building</c><00:03:31.680><c> cookbook</c><00:03:32.159><c> as</c><00:03:32.319><c> well</c>

00:03:32.869 --> 00:03:32.879 align:start position:0%
to our world building cookbook as well
 

00:03:32.879 --> 00:03:35.149 align:start position:0%
to our world building cookbook as well
so<00:03:33.159><c> stay</c><00:03:33.360><c> tuned</c><00:03:33.640><c> for</c><00:03:33.840><c> that</c><00:03:34.200><c> and</c><00:03:34.640><c> uh</c><00:03:34.920><c> let</c><00:03:35.040><c> me</c>

00:03:35.149 --> 00:03:35.159 align:start position:0%
so stay tuned for that and uh let me
 

00:03:35.159 --> 00:03:36.830 align:start position:0%
so stay tuned for that and uh let me
know<00:03:35.280><c> in</c><00:03:35.400><c> the</c><00:03:35.519><c> comments</c><00:03:36.080><c> which</c><00:03:36.400><c> all</c><00:03:36.640><c> new</c>

00:03:36.830 --> 00:03:36.840 align:start position:0%
know in the comments which all new
 

00:03:36.840 --> 00:03:39.149 align:start position:0%
know in the comments which all new
worlds<00:03:37.159><c> you</c><00:03:37.319><c> generate</c><00:03:38.080><c> and</c><00:03:38.439><c> uh</c><00:03:38.760><c> if</c><00:03:38.840><c> you</c><00:03:39.000><c> have</c>

00:03:39.149 --> 00:03:39.159 align:start position:0%
worlds you generate and uh if you have
 

00:03:39.159 --> 00:03:41.670 align:start position:0%
worlds you generate and uh if you have
any<00:03:39.400><c> questions</c><00:03:39.959><c> create</c><00:03:40.280><c> an</c><00:03:40.560><c> issue</c><00:03:40.840><c> on</c><00:03:41.080><c> GitHub</c>

00:03:41.670 --> 00:03:41.680 align:start position:0%
any questions create an issue on GitHub
 

00:03:41.680 --> 00:03:44.429 align:start position:0%
any questions create an issue on GitHub
or<00:03:42.159><c> uh</c><00:03:42.519><c> message</c><00:03:42.840><c> us</c><00:03:43.000><c> on</c><00:03:43.200><c> Discord</c><00:03:43.920><c> okay</c><00:03:44.200><c> have</c><00:03:44.319><c> a</c>

00:03:44.429 --> 00:03:44.439 align:start position:0%
or uh message us on Discord okay have a
 

00:03:44.439 --> 00:03:48.080 align:start position:0%
or uh message us on Discord okay have a
good<00:03:44.640><c> day</c><00:03:45.080><c> bye</c>

