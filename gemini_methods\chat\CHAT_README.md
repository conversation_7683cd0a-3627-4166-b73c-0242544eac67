# Google GenAI Chat Manager with Conversation Memory

This module implements Google GenAI's built-in chat functionality that automatically maintains conversation history and context across multiple API calls. It provides a complete chat management system with persistence, cost tracking, and session management.

## Overview

The Google GenAI SDK provides built-in conversation management through its **chat client interface**, which automatically maintains conversation history and context across multiple API calls. This implementation wraps that functionality with additional features like:

- 🧠 **Persistent conversation memory** - remembers context across messages
- 💰 **Cost tracking and budgeting** - monitors API usage costs
- 📊 **Session management** - create, restore, and manage multiple chat sessions
- 🗄️ **Database persistence** - store conversations and metadata
- 📤 **Export capabilities** - export conversations to JSON/Markdown
- ⚡ **Async/await support** - fully asynchronous implementation

## Key Features

### Built-in Conversation Management

The core functionality uses Google GenAI's `client.chats.create()` method, which creates a chat session that automatically remembers previous interactions:

```python
from chat_manager import create_simple_chat

# Create a chat session
manager, session_id = await create_simple_chat(
    system_instruction="You are a helpful assistant."
)

# First message
response1, *_ = await manager.send_message(session_id, "Hi, I'm <PERSON>.")

# Second message - the AI remembers <PERSON>'s name from the previous message
response2, *_ = await manager.send_message(session_id, "What's my name?")
```

### Session Persistence

Chat sessions can be persisted to a database and restored later:

```python
# Create a persistent chat manager with database
from asyncpg import create_pool

db_pool = await create_pool("postgresql://...")
manager = ChatManager(db_pool=db_pool)

# Create session
session_id = await manager.create_chat_session()

# Session is automatically saved to database
# Can be restored later even after application restart
restored = await manager.restore_chat_session(session_id)
```

### Cost Tracking

Every message exchange tracks token usage and calculates costs:

```python
response, input_tokens, output_tokens, cost = await manager.send_message(
    session_id, "Explain quantum computing"
)

print(f"Tokens: {input_tokens}/{output_tokens}")
print(f"Cost: ${cost:.6f}")

# Get session summary
summary = manager.get_session_summary(session_id)
print(f"Total cost: ${summary['total_cost']:.6f}")
```

## Quick Start

### 1. Installation

Make sure you have the required dependencies:

```bash
pip install google-genai asyncpg python-dotenv rich
```

### 2. Environment Setup

Set your Google API key:

```bash
export GOOGLE_API_KEY="your-api-key-here"
# or
export GEMINI_API_KEY="your-api-key-here"
```

### 3. Basic Usage

```python
import asyncio
from chat_manager import create_simple_chat

async def basic_chat():
    # Create a chat session
    manager, session_id = await create_simple_chat()
    
    # Send messages
    response1, *_ = await manager.send_message(session_id, "Hello!")
    response2, *_ = await manager.send_message(session_id, "How are you?")
    
    # Close session
    await manager.close_session(session_id)

asyncio.run(basic_chat())
```

### 4. Run Examples

```bash
# Run the interactive demo
python test_chat_manager.py

# Run simple examples
python chat_example.py
```

## API Reference

### ChatManager Class

The main class for managing chat sessions.

#### Constructor

```python
ChatManager(
    db_pool: Optional[Pool] = None,
    client_schema: str = "client_nooob_085c35c4", 
    default_model: str = DEFAULT_MODEL_NAME
)
```

#### Methods

##### create_chat_session()

```python
async def create_chat_session(
    model_name: Optional[str] = None,
    system_instruction: Optional[str] = None,
    generation_config_overrides: Optional[Dict[str, Any]] = None,
    session_id: Optional[str] = None
) -> str
```

Creates a new chat session with conversation memory.

##### send_message()

```python
async def send_message(
    session_id: str,
    message: str,
    include_files: Optional[List[types.Part]] = None
) -> Tuple[str, Optional[int], Optional[int], Optional[float]]
```

Sends a message and returns (response, input_tokens, output_tokens, cost).

##### get_chat_history()

```python
async def get_chat_history(session_id: str) -> List[ChatMessage]
```

Returns the complete conversation history for a session.

##### restore_chat_session()

```python
async def restore_chat_session(session_id: str) -> bool
```

Restores a chat session from database with full conversation history.

### Convenience Functions

#### create_simple_chat()

```python
async def create_simple_chat(
    model_name: Optional[str] = None,
    system_instruction: Optional[str] = None,
    db_pool: Optional[Pool] = None
) -> Tuple[ChatManager, str]
```

Quick way to create a chat manager and session.

#### quick_chat_exchange()

```python
async def quick_chat_exchange(
    message: str,
    model_name: Optional[str] = None,
    system_instruction: Optional[str] = None,
    db_pool: Optional[Pool] = None
) -> str
```

One-off chat exchange without session management.

## Data Models

### ChatMessage

```python
@dataclass
class ChatMessage:
    role: str  # 'user' or 'model'
    content: str
    timestamp: datetime
    token_count: Optional[int] = None
    cost: Optional[float] = None
```

### ChatSession

```python
@dataclass
class ChatSession:
    session_id: str
    model_name: str
    system_instruction: Optional[str]
    created_at: datetime
    last_message_at: datetime
    total_messages: int
    total_input_tokens: int
    total_output_tokens: int
    total_cost: float
    is_active: bool
```

## Database Schema

If using database persistence, you'll need these tables:

```sql
-- Run the schema from chat_schema.sql
\i chat_schema.sql
```

The schema includes:
- `chat_sessions` - session metadata
- `chat_messages` - individual messages
- Views and functions for analytics
- Automatic cleanup procedures

## Examples

### Conversation with Memory

```python
async def conversation_example():
    manager, session_id = await create_simple_chat(
        system_instruction="You are a helpful coding tutor."
    )
    
    # The AI remembers context across all these messages
    await manager.send_message(session_id, "I'm learning Python")
    await manager.send_message(session_id, "How do I create a list?")
    await manager.send_message(session_id, "What about the topic we discussed earlier?")
    
    await manager.close_session(session_id)
```

### Tic-Tac-Toe Game

```python
async def tic_tac_toe():
    manager, session_id = await create_simple_chat(
        system_instruction="Play tic-tac-toe and remember the game state."
    )
    
    await manager.send_message(session_id, "Let's play tic-tac-toe. I'll be X, center position.")
    await manager.send_message(session_id, "I'll take top-left corner.")
    # Game continues with full context memory...
```

### Multi-Session Management

```python
async def multi_session_example():
    manager = ChatManager()
    
    # Create multiple sessions for different topics
    coding_session = await manager.create_chat_session(
        system_instruction="You are a coding expert."
    )
    
    science_session = await manager.create_chat_session(
        system_instruction="You are a science teacher."
    )
    
    # Each session maintains separate conversation context
    await manager.send_message(coding_session, "Explain recursion")
    await manager.send_message(science_session, "Explain photosynthesis")
    
    # List all active sessions
    sessions = await manager.list_active_sessions()
```

## Integration with Existing Code

This chat manager integrates with the existing `gemini_methods` infrastructure:

- Uses `genai_utils.py` for configuration and client setup
- Integrates with `token_utils.py` for cost calculation
- Works with `file_manager.py` for file uploads in chat
- Supports `pricing_models.py` for accurate cost tracking

### With File Upload

```python
from file_manager import GenaiFileManager

# Upload a file
file_manager = GenaiFileManager()
uploaded_file = await file_manager.upload_file(Path("document.pdf"))

# Include file in chat
file_part = types.Part.from_uri(
    file_uri=uploaded_file.uri,
    mime_type=uploaded_file.mime_type
)

response, *_ = await manager.send_message(
    session_id, 
    "Analyze this document",
    include_files=[file_part]
)
```

## Key Differences from Other Methods

Unlike the existing `call_gemini_api()` function which handles single requests, this chat manager:

1. **Maintains State**: Automatically preserves conversation history
2. **Session Management**: Tracks multiple concurrent conversations
3. **Persistence**: Saves conversations to database for later restoration
4. **Cost Tracking**: Accumulates costs across the entire conversation
5. **Built-in Memory**: Uses Google's native conversation management

## Performance Considerations

- **Token Costs**: Full conversation history is sent with each request
- **Context Caching**: Consider using with Google's context caching for long conversations
- **Database Persistence**: Optional - works in-memory if no database provided
- **Cleanup**: Automatic cleanup of old sessions to manage storage

## Error Handling

The chat manager includes comprehensive error handling:

- Retries for transient API errors
- Graceful degradation when database is unavailable
- Session restoration capabilities
- Detailed logging for debugging

## Contributing

When extending this module:

1. Maintain async/await patterns
2. Add proper error handling and logging
3. Update tests and examples
4. Consider database schema changes
5. Document new features

## License

This implementation is part of the larger `gemini_methods` package and follows the same licensing terms. 