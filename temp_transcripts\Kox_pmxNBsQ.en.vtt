WEBVTT
Kind: captions
Language: en

00:00:01.280 --> 00:00:03.270 align:start position:0%
 
and<00:00:01.680><c> hi</c><00:00:01.920><c> everybody</c><00:00:02.399><c> and</c><00:00:02.560><c> thanks</c><00:00:02.800><c> for</c><00:00:02.960><c> checking</c>

00:00:03.270 --> 00:00:03.280 align:start position:0%
and hi everybody and thanks for checking
 

00:00:03.280 --> 00:00:04.150 align:start position:0%
and hi everybody and thanks for checking
out<00:00:03.600><c> this</c>

00:00:04.150 --> 00:00:04.160 align:start position:0%
out this
 

00:00:04.160 --> 00:00:06.950 align:start position:0%
out this
udemy<00:00:04.640><c> course</c><00:00:05.200><c> all</c><00:00:05.440><c> about</c><00:00:06.160><c> uh</c><00:00:06.480><c> product</c>

00:00:06.950 --> 00:00:06.960 align:start position:0%
udemy course all about uh product
 

00:00:06.960 --> 00:00:07.829 align:start position:0%
udemy course all about uh product
management

00:00:07.829 --> 00:00:07.839 align:start position:0%
management
 

00:00:07.839 --> 00:00:10.549 align:start position:0%
management
following<00:00:08.480><c> the</c><00:00:08.639><c> lean</c><00:00:09.040><c> startup</c><00:00:09.679><c> methodology</c>

00:00:10.549 --> 00:00:10.559 align:start position:0%
following the lean startup methodology
 

00:00:10.559 --> 00:00:11.749 align:start position:0%
following the lean startup methodology
i'm<00:00:10.719><c> very</c><00:00:10.960><c> excited</c>

00:00:11.749 --> 00:00:11.759 align:start position:0%
i'm very excited
 

00:00:11.759 --> 00:00:13.270 align:start position:0%
i'm very excited
to<00:00:11.920><c> be</c><00:00:12.080><c> offering</c><00:00:12.480><c> this</c><00:00:12.799><c> course</c><00:00:13.120><c> it's</c>

00:00:13.270 --> 00:00:13.280 align:start position:0%
to be offering this course it's
 

00:00:13.280 --> 00:00:15.589 align:start position:0%
to be offering this course it's
something<00:00:13.759><c> uh</c><00:00:14.559><c> something</c><00:00:14.799><c> that</c><00:00:14.960><c> i've</c><00:00:15.120><c> been</c><00:00:15.360><c> uh</c>

00:00:15.589 --> 00:00:15.599 align:start position:0%
something uh something that i've been uh
 

00:00:15.599 --> 00:00:16.790 align:start position:0%
something uh something that i've been uh
pretty<00:00:15.839><c> passionate</c><00:00:16.400><c> about</c>

00:00:16.790 --> 00:00:16.800 align:start position:0%
pretty passionate about
 

00:00:16.800 --> 00:00:19.830 align:start position:0%
pretty passionate about
scaling<00:00:17.359><c> apps</c><00:00:18.080><c> and</c><00:00:18.640><c> e-commerce</c>

00:00:19.830 --> 00:00:19.840 align:start position:0%
scaling apps and e-commerce
 

00:00:19.840 --> 00:00:22.870 align:start position:0%
scaling apps and e-commerce
um<00:00:20.640><c> especially</c><00:00:21.439><c> these</c><00:00:21.760><c> two</c><00:00:22.160><c> uh</c>

00:00:22.870 --> 00:00:22.880 align:start position:0%
um especially these two uh
 

00:00:22.880 --> 00:00:25.670 align:start position:0%
um especially these two uh
these<00:00:23.199><c> two</c><00:00:23.680><c> ecosystems</c><00:00:24.960><c> and</c><00:00:25.199><c> the</c><00:00:25.279><c> great</c><00:00:25.519><c> thing</c>

00:00:25.670 --> 00:00:25.680 align:start position:0%
these two ecosystems and the great thing
 

00:00:25.680 --> 00:00:26.950 align:start position:0%
these two ecosystems and the great thing
about

00:00:26.950 --> 00:00:26.960 align:start position:0%
about
 

00:00:26.960 --> 00:00:30.070 align:start position:0%
about
having<00:00:27.359><c> these</c><00:00:27.760><c> two</c><00:00:28.800><c> real</c><00:00:29.199><c> examples</c>

00:00:30.070 --> 00:00:30.080 align:start position:0%
having these two real examples
 

00:00:30.080 --> 00:00:31.990 align:start position:0%
having these two real examples
for<00:00:30.240><c> the</c><00:00:30.480><c> course</c><00:00:30.800><c> is</c><00:00:30.960><c> that</c><00:00:31.279><c> you're</c><00:00:31.679><c> also</c><00:00:31.920><c> going</c>

00:00:31.990 --> 00:00:32.000 align:start position:0%
for the course is that you're also going
 

00:00:32.000 --> 00:00:33.510 align:start position:0%
for the course is that you're also going
to<00:00:32.160><c> be</c><00:00:32.320><c> able</c><00:00:32.480><c> to</c><00:00:32.640><c> fill</c><00:00:32.960><c> out</c>

00:00:33.510 --> 00:00:33.520 align:start position:0%
to be able to fill out
 

00:00:33.520 --> 00:00:36.790 align:start position:0%
to be able to fill out
the<00:00:33.680><c> adrenaline</c><00:00:34.719><c> where</c><00:00:35.120><c> which</c><00:00:35.520><c> comes</c><00:00:36.480><c> with</c>

00:00:36.790 --> 00:00:36.800 align:start position:0%
the adrenaline where which comes with
 

00:00:36.800 --> 00:00:38.470 align:start position:0%
the adrenaline where which comes with
product<00:00:37.280><c> management</c>

00:00:38.470 --> 00:00:38.480 align:start position:0%
product management
 

00:00:38.480 --> 00:00:41.110 align:start position:0%
product management
which<00:00:38.719><c> is</c><00:00:38.800><c> something</c><00:00:40.000><c> which</c><00:00:40.320><c> allows</c><00:00:40.640><c> you</c><00:00:40.879><c> to</c>

00:00:41.110 --> 00:00:41.120 align:start position:0%
which is something which allows you to
 

00:00:41.120 --> 00:00:41.910 align:start position:0%
which is something which allows you to
run<00:00:41.440><c> small</c>

00:00:41.910 --> 00:00:41.920 align:start position:0%
run small
 

00:00:41.920 --> 00:00:45.830 align:start position:0%
run small
measurable<00:00:42.719><c> tests</c><00:00:43.680><c> and</c><00:00:44.000><c> then</c><00:00:44.800><c> optimize</c>

00:00:45.830 --> 00:00:45.840 align:start position:0%
measurable tests and then optimize
 

00:00:45.840 --> 00:00:49.670 align:start position:0%
measurable tests and then optimize
the<00:00:46.000><c> funnel</c><00:00:46.559><c> and</c><00:00:46.800><c> optimize</c><00:00:48.559><c> user</c><00:00:48.960><c> acquisition</c>

00:00:49.670 --> 00:00:49.680 align:start position:0%
the funnel and optimize user acquisition
 

00:00:49.680 --> 00:00:50.869 align:start position:0%
the funnel and optimize user acquisition
and<00:00:49.920><c> growth</c><00:00:50.320><c> rate</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
and growth rate
 

00:00:50.879 --> 00:00:52.229 align:start position:0%
and growth rate
and<00:00:51.120><c> of</c><00:00:51.199><c> course</c><00:00:51.520><c> we're</c><00:00:51.680><c> also</c><00:00:51.920><c> going</c><00:00:52.000><c> to</c><00:00:52.079><c> be</c>

00:00:52.229 --> 00:00:52.239 align:start position:0%
and of course we're also going to be
 

00:00:52.239 --> 00:00:55.029 align:start position:0%
and of course we're also going to be
using<00:00:52.960><c> the</c><00:00:53.120><c> top</c><00:00:53.440><c> tools</c><00:00:53.840><c> like</c><00:00:54.160><c> metabase</c><00:00:54.719><c> data</c>

00:00:55.029 --> 00:00:55.039 align:start position:0%
using the top tools like metabase data
 

00:00:55.039 --> 00:00:57.110 align:start position:0%
using the top tools like metabase data
visualization<00:00:56.000><c> to</c>

00:00:57.110 --> 00:00:57.120 align:start position:0%
visualization to
 

00:00:57.120 --> 00:00:59.270 align:start position:0%
visualization to
get<00:00:57.360><c> meaningful</c><00:00:58.000><c> insights</c><00:00:58.559><c> directly</c><00:00:59.039><c> from</c>

00:00:59.270 --> 00:00:59.280 align:start position:0%
get meaningful insights directly from
 

00:00:59.280 --> 00:01:00.229 align:start position:0%
get meaningful insights directly from
our<00:00:59.440><c> database</c>

00:01:00.229 --> 00:01:00.239 align:start position:0%
our database
 

00:01:00.239 --> 00:01:01.590 align:start position:0%
our database
we're<00:01:00.480><c> also</c><00:01:00.719><c> going</c><00:01:00.800><c> to</c><00:01:00.879><c> be</c><00:01:00.960><c> using</c><00:01:01.280><c> google</c>

00:01:01.590 --> 00:01:01.600 align:start position:0%
we're also going to be using google
 

00:01:01.600 --> 00:01:03.430 align:start position:0%
we're also going to be using google
analytics<00:01:02.239><c> to</c><00:01:02.399><c> see</c><00:01:02.559><c> what's</c><00:01:02.800><c> happening</c><00:01:03.199><c> on</c><00:01:03.359><c> all</c>

00:01:03.430 --> 00:01:03.440 align:start position:0%
analytics to see what's happening on all
 

00:01:03.440 --> 00:01:04.950 align:start position:0%
analytics to see what's happening on all
the<00:01:03.600><c> different</c><00:01:03.920><c> properties</c><00:01:04.559><c> that</c><00:01:04.799><c> we're</c>

00:01:04.950 --> 00:01:04.960 align:start position:0%
the different properties that we're
 

00:01:04.960 --> 00:01:05.910 align:start position:0%
the different properties that we're
going<00:01:05.119><c> to</c><00:01:05.199><c> be</c><00:01:05.360><c> looking</c><00:01:05.600><c> at</c>

00:01:05.910 --> 00:01:05.920 align:start position:0%
going to be looking at
 

00:01:05.920 --> 00:01:10.710 align:start position:0%
going to be looking at
including<00:01:07.680><c> the</c><00:01:08.000><c> chrome</c><00:01:08.400><c> extension</c>

00:01:10.710 --> 00:01:10.720 align:start position:0%
including the chrome extension
 

00:01:10.720 --> 00:01:12.390 align:start position:0%
including the chrome extension
one<00:01:10.880><c> of</c><00:01:10.960><c> the</c><00:01:11.040><c> apps</c><00:01:11.360><c> is</c><00:01:11.439><c> a</c><00:01:11.520><c> chrome</c><00:01:11.840><c> extension</c>

00:01:12.390 --> 00:01:12.400 align:start position:0%
one of the apps is a chrome extension
 

00:01:12.400 --> 00:01:14.630 align:start position:0%
one of the apps is a chrome extension
which<00:01:12.640><c> is</c><00:01:12.799><c> a</c><00:01:12.960><c> shopif</c><00:01:13.439><c> which</c><00:01:13.680><c> is</c><00:01:13.920><c> the</c><00:01:14.159><c> app</c><00:01:14.400><c> for</c>

00:01:14.630 --> 00:01:14.640 align:start position:0%
which is a shopif which is the app for
 

00:01:14.640 --> 00:01:15.510 align:start position:0%
which is a shopif which is the app for
amazon

00:01:15.510 --> 00:01:15.520 align:start position:0%
amazon
 

00:01:15.520 --> 00:01:17.190 align:start position:0%
amazon
which<00:01:15.759><c> allows</c><00:01:16.080><c> users</c><00:01:16.479><c> to</c><00:01:16.720><c> scrape</c><00:01:17.040><c> their</c>

00:01:17.190 --> 00:01:17.200 align:start position:0%
which allows users to scrape their
 

00:01:17.200 --> 00:01:18.870 align:start position:0%
which allows users to scrape their
purchase<00:01:17.680><c> history</c><00:01:18.240><c> and</c>

00:01:18.870 --> 00:01:18.880 align:start position:0%
purchase history and
 

00:01:18.880 --> 00:01:20.950 align:start position:0%
purchase history and
blog<00:01:19.200><c> about</c><00:01:19.360><c> their</c><00:01:19.600><c> purchases</c><00:01:20.479><c> the</c><00:01:20.640><c> second</c>

00:01:20.950 --> 00:01:20.960 align:start position:0%
blog about their purchases the second
 

00:01:20.960 --> 00:01:22.870 align:start position:0%
blog about their purchases the second
one<00:01:21.119><c> is</c><00:01:21.280><c> also</c><00:01:21.520><c> a</c><00:01:21.600><c> social</c><00:01:22.000><c> shopping</c><00:01:22.479><c> app</c><00:01:22.720><c> this</c>

00:01:22.870 --> 00:01:22.880 align:start position:0%
one is also a social shopping app this
 

00:01:22.880 --> 00:01:24.390 align:start position:0%
one is also a social shopping app this
time<00:01:23.119><c> for</c><00:01:23.280><c> shopify</c>

00:01:24.390 --> 00:01:24.400 align:start position:0%
time for shopify
 

00:01:24.400 --> 00:01:26.469 align:start position:0%
time for shopify
and<00:01:24.640><c> that</c><00:01:24.880><c> uh</c><00:01:25.200><c> that</c><00:01:25.439><c> integrates</c><00:01:26.080><c> directly</c>

00:01:26.469 --> 00:01:26.479 align:start position:0%
and that uh that integrates directly
 

00:01:26.479 --> 00:01:28.710 align:start position:0%
and that uh that integrates directly
with<00:01:26.720><c> the</c><00:01:26.880><c> shopify</c><00:01:27.520><c> ecosystem</c><00:01:28.400><c> and</c>

00:01:28.710 --> 00:01:28.720 align:start position:0%
with the shopify ecosystem and
 

00:01:28.720 --> 00:01:31.270 align:start position:0%
with the shopify ecosystem and
shopify<00:01:29.280><c> app</c><00:01:29.520><c> store</c><00:01:30.240><c> uh</c><00:01:30.479><c> so</c><00:01:30.720><c> we're</c><00:01:30.880><c> gonna</c><00:01:31.119><c> be</c>

00:01:31.270 --> 00:01:31.280 align:start position:0%
shopify app store uh so we're gonna be
 

00:01:31.280 --> 00:01:33.109 align:start position:0%
shopify app store uh so we're gonna be
able<00:01:31.520><c> to</c><00:01:31.680><c> set</c><00:01:31.920><c> we've</c><00:01:32.240><c> already</c><00:01:32.479><c> set</c><00:01:32.720><c> up</c><00:01:32.799><c> google</c>

00:01:33.109 --> 00:01:33.119 align:start position:0%
able to set we've already set up google
 

00:01:33.119 --> 00:01:34.950 align:start position:0%
able to set we've already set up google
analytics<00:01:33.600><c> on</c><00:01:33.759><c> both</c><00:01:34.000><c> those</c><00:01:34.240><c> properties</c><00:01:34.799><c> we're</c>

00:01:34.950 --> 00:01:34.960 align:start position:0%
analytics on both those properties we're
 

00:01:34.960 --> 00:01:36.550 align:start position:0%
analytics on both those properties we're
gonna<00:01:35.200><c> review</c><00:01:35.520><c> the</c><00:01:35.680><c> numbers</c>

00:01:36.550 --> 00:01:36.560 align:start position:0%
gonna review the numbers
 

00:01:36.560 --> 00:01:38.789 align:start position:0%
gonna review the numbers
uh<00:01:36.799><c> we're</c><00:01:37.040><c> also</c><00:01:37.200><c> gonna</c><00:01:37.439><c> review</c><00:01:37.840><c> user</c><00:01:38.240><c> metrics</c>

00:01:38.789 --> 00:01:38.799 align:start position:0%
uh we're also gonna review user metrics
 

00:01:38.799 --> 00:01:40.230 align:start position:0%
uh we're also gonna review user metrics
which<00:01:38.960><c> is</c><00:01:39.119><c> are</c><00:01:39.280><c> they</c><00:01:39.520><c> taking</c><00:01:39.840><c> the</c><00:01:40.000><c> right</c>

00:01:40.230 --> 00:01:40.240 align:start position:0%
which is are they taking the right
 

00:01:40.240 --> 00:01:41.510 align:start position:0%
which is are they taking the right
actions<00:01:40.720><c> also</c>

00:01:41.510 --> 00:01:41.520 align:start position:0%
actions also
 

00:01:41.520 --> 00:01:42.710 align:start position:0%
actions also
and<00:01:41.680><c> we're</c><00:01:41.840><c> going</c><00:01:42.000><c> to</c><00:01:42.079><c> try</c><00:01:42.240><c> to</c><00:01:42.320><c> look</c><00:01:42.479><c> for</c>

00:01:42.710 --> 00:01:42.720 align:start position:0%
and we're going to try to look for
 

00:01:42.720 --> 00:01:44.550 align:start position:0%
and we're going to try to look for
opportunities<00:01:43.360><c> for</c><00:01:43.520><c> optimization</c>

00:01:44.550 --> 00:01:44.560 align:start position:0%
opportunities for optimization
 

00:01:44.560 --> 00:01:47.109 align:start position:0%
opportunities for optimization
after<00:01:45.360><c> combining</c><00:01:46.079><c> data</c><00:01:46.399><c> from</c><00:01:46.640><c> multiple</c>

00:01:47.109 --> 00:01:47.119 align:start position:0%
after combining data from multiple
 

00:01:47.119 --> 00:01:47.910 align:start position:0%
after combining data from multiple
sources

00:01:47.910 --> 00:01:47.920 align:start position:0%
sources
 

00:01:47.920 --> 00:01:49.910 align:start position:0%
sources
we're<00:01:48.079><c> also</c><00:01:48.320><c> going</c><00:01:48.399><c> to</c><00:01:48.560><c> be</c><00:01:48.720><c> doing</c><00:01:49.200><c> a</c><00:01:49.520><c> email</c>

00:01:49.910 --> 00:01:49.920 align:start position:0%
we're also going to be doing a email
 

00:01:49.920 --> 00:01:51.350 align:start position:0%
we're also going to be doing a email
marketing<00:01:50.479><c> campaign</c><00:01:50.960><c> using</c>

00:01:51.350 --> 00:01:51.360 align:start position:0%
marketing campaign using
 

00:01:51.360 --> 00:01:53.749 align:start position:0%
marketing campaign using
active<00:01:51.840><c> campaign</c><00:01:53.200><c> that's</c><00:01:53.439><c> going</c><00:01:53.520><c> to</c><00:01:53.600><c> be</c>

00:01:53.749 --> 00:01:53.759 align:start position:0%
active campaign that's going to be
 

00:01:53.759 --> 00:01:54.870 align:start position:0%
active campaign that's going to be
really<00:01:54.000><c> cool</c><00:01:54.240><c> it's</c><00:01:54.399><c> going</c><00:01:54.479><c> to</c><00:01:54.560><c> be</c><00:01:54.640><c> an</c>

00:01:54.870 --> 00:01:54.880 align:start position:0%
really cool it's going to be an
 

00:01:54.880 --> 00:01:56.630 align:start position:0%
really cool it's going to be an
automation<00:01:55.439><c> campaign</c><00:01:56.000><c> which</c>

00:01:56.630 --> 00:01:56.640 align:start position:0%
automation campaign which
 

00:01:56.640 --> 00:01:59.670 align:start position:0%
automation campaign which
adds<00:01:56.880><c> the</c><00:01:57.119><c> user</c><00:01:57.759><c> to</c><00:01:57.920><c> a</c><00:01:58.000><c> specific</c><00:01:58.640><c> automation</c>

00:01:59.670 --> 00:01:59.680 align:start position:0%
adds the user to a specific automation
 

00:01:59.680 --> 00:02:01.830 align:start position:0%
adds the user to a specific automation
in<00:01:59.840><c> which</c><00:02:00.240><c> he</c><00:02:00.479><c> gets</c><00:02:00.799><c> uh</c><00:02:01.040><c> automatically</c><00:02:01.680><c> as</c>

00:02:01.830 --> 00:02:01.840 align:start position:0%
in which he gets uh automatically as
 

00:02:01.840 --> 00:02:03.749 align:start position:0%
in which he gets uh automatically as
many<00:02:02.240><c> emails</c>

00:02:03.749 --> 00:02:03.759 align:start position:0%
many emails
 

00:02:03.759 --> 00:02:06.789 align:start position:0%
many emails
as<00:02:03.920><c> we</c><00:02:04.159><c> want</c><00:02:04.719><c> um</c><00:02:05.439><c> within</c><00:02:06.000><c> and</c><00:02:06.079><c> we</c><00:02:06.240><c> control</c><00:02:06.640><c> the</c>

00:02:06.789 --> 00:02:06.799 align:start position:0%
as we want um within and we control the
 

00:02:06.799 --> 00:02:08.469 align:start position:0%
as we want um within and we control the
intervals<00:02:07.200><c> and</c><00:02:07.280><c> we</c><00:02:07.439><c> control</c><00:02:07.680><c> the</c><00:02:07.840><c> design</c><00:02:08.239><c> from</c>

00:02:08.469 --> 00:02:08.479 align:start position:0%
intervals and we control the design from
 

00:02:08.479 --> 00:02:09.830 align:start position:0%
intervals and we control the design from
active<00:02:08.800><c> campaign</c><00:02:09.280><c> i'm</c><00:02:09.360><c> going</c><00:02:09.440><c> to</c><00:02:09.520><c> show</c><00:02:09.679><c> you</c>

00:02:09.830 --> 00:02:09.840 align:start position:0%
active campaign i'm going to show you
 

00:02:09.840 --> 00:02:10.790 align:start position:0%
active campaign i'm going to show you
how<00:02:09.920><c> to</c><00:02:10.080><c> do</c><00:02:10.239><c> that</c>

00:02:10.790 --> 00:02:10.800 align:start position:0%
how to do that
 

00:02:10.800 --> 00:02:12.070 align:start position:0%
how to do that
and<00:02:10.959><c> we're</c><00:02:11.120><c> also</c><00:02:11.360><c> going</c><00:02:11.440><c> to</c><00:02:11.520><c> be</c><00:02:11.599><c> using</c><00:02:11.920><c> a</c>

00:02:12.070 --> 00:02:12.080 align:start position:0%
and we're also going to be using a
 

00:02:12.080 --> 00:02:13.830 align:start position:0%
and we're also going to be using a
linkedin<00:02:12.480><c> automation</c><00:02:13.040><c> tool</c><00:02:13.280><c> called</c><00:02:13.520><c> meet</c>

00:02:13.830 --> 00:02:13.840 align:start position:0%
linkedin automation tool called meet
 

00:02:13.840 --> 00:02:14.630 align:start position:0%
linkedin automation tool called meet
alfred

00:02:14.630 --> 00:02:14.640 align:start position:0%
alfred
 

00:02:14.640 --> 00:02:17.670 align:start position:0%
alfred
uh<00:02:15.040><c> both</c><00:02:15.360><c> for</c><00:02:16.000><c> um</c><00:02:16.640><c> trying</c><00:02:16.879><c> to</c><00:02:17.120><c> find</c>

00:02:17.670 --> 00:02:17.680 align:start position:0%
uh both for um trying to find
 

00:02:17.680 --> 00:02:19.750 align:start position:0%
uh both for um trying to find
uh<00:02:17.920><c> both</c><00:02:18.160><c> for</c><00:02:18.319><c> market</c><00:02:18.800><c> research</c><00:02:19.200><c> to</c><00:02:19.360><c> try</c><00:02:19.599><c> to</c>

00:02:19.750 --> 00:02:19.760 align:start position:0%
uh both for market research to try to
 

00:02:19.760 --> 00:02:22.070 align:start position:0%
uh both for market research to try to
find<00:02:20.480><c> users</c><00:02:20.879><c> that</c><00:02:21.040><c> want</c><00:02:21.200><c> to</c><00:02:21.360><c> trial</c><00:02:21.680><c> the</c><00:02:21.840><c> app</c>

00:02:22.070 --> 00:02:22.080 align:start position:0%
find users that want to trial the app
 

00:02:22.080 --> 00:02:23.830 align:start position:0%
find users that want to trial the app
trying<00:02:22.319><c> to</c><00:02:22.400><c> get</c><00:02:22.560><c> feedback</c><00:02:23.040><c> about</c><00:02:23.280><c> the</c><00:02:23.520><c> app</c>

00:02:23.830 --> 00:02:23.840 align:start position:0%
trying to get feedback about the app
 

00:02:23.840 --> 00:02:26.070 align:start position:0%
trying to get feedback about the app
and<00:02:24.160><c> also</c><00:02:25.120><c> maybe</c><00:02:25.440><c> try</c><00:02:25.599><c> to</c><00:02:25.680><c> get</c><00:02:25.840><c> some</c>

00:02:26.070 --> 00:02:26.080 align:start position:0%
and also maybe try to get some
 

00:02:26.080 --> 00:02:27.190 align:start position:0%
and also maybe try to get some
acquisition

00:02:27.190 --> 00:02:27.200 align:start position:0%
acquisition
 

00:02:27.200 --> 00:02:28.790 align:start position:0%
acquisition
through<00:02:27.440><c> our</c><00:02:27.599><c> contacts</c><00:02:28.160><c> in</c><00:02:28.319><c> linkedin</c><00:02:28.640><c> so</c>

00:02:28.790 --> 00:02:28.800 align:start position:0%
through our contacts in linkedin so
 

00:02:28.800 --> 00:02:30.229 align:start position:0%
through our contacts in linkedin so
we're<00:02:28.959><c> going</c><00:02:29.040><c> to</c><00:02:29.120><c> use</c><00:02:29.280><c> the</c><00:02:29.440><c> tool</c><00:02:29.599><c> called</c><00:02:29.840><c> meet</c>

00:02:30.229 --> 00:02:30.239 align:start position:0%
we're going to use the tool called meet
 

00:02:30.239 --> 00:02:33.030 align:start position:0%
we're going to use the tool called meet
alfred<00:02:30.720><c> very</c><00:02:30.959><c> very</c><00:02:31.280><c> cool</c><00:02:31.680><c> platform</c>

00:02:33.030 --> 00:02:33.040 align:start position:0%
alfred very very cool platform
 

00:02:33.040 --> 00:02:36.309 align:start position:0%
alfred very very cool platform
for<00:02:33.200><c> scaling</c><00:02:33.840><c> and</c><00:02:34.080><c> very</c><00:02:34.319><c> very</c><00:02:35.200><c> low</c><00:02:35.440><c> cost</c><00:02:35.920><c> also</c>

00:02:36.309 --> 00:02:36.319 align:start position:0%
for scaling and very very low cost also
 

00:02:36.319 --> 00:02:37.830 align:start position:0%
for scaling and very very low cost also
so<00:02:36.480><c> that's</c><00:02:36.720><c> a</c><00:02:36.800><c> little</c><00:02:36.959><c> bit</c><00:02:37.120><c> about</c><00:02:37.280><c> the</c><00:02:37.440><c> courses</c>

00:02:37.830 --> 00:02:37.840 align:start position:0%
so that's a little bit about the courses
 

00:02:37.840 --> 00:02:39.910 align:start position:0%
so that's a little bit about the courses
specifically<00:02:38.480><c> for</c><00:02:38.800><c> uh</c><00:02:39.040><c> probably</c>

00:02:39.910 --> 00:02:39.920 align:start position:0%
specifically for uh probably
 

00:02:39.920 --> 00:02:41.750 align:start position:0%
specifically for uh probably
brand<00:02:40.319><c> new</c><00:02:40.560><c> apps</c><00:02:40.959><c> we'll</c><00:02:41.200><c> probably</c><00:02:41.440><c> get</c><00:02:41.599><c> the</c>

00:02:41.750 --> 00:02:41.760 align:start position:0%
brand new apps we'll probably get the
 

00:02:41.760 --> 00:02:43.589 align:start position:0%
brand new apps we'll probably get the
most<00:02:42.080><c> value</c><00:02:42.480><c> out</c><00:02:42.560><c> of</c><00:02:42.640><c> this</c><00:02:42.879><c> course</c><00:02:43.120><c> but</c><00:02:43.280><c> i</c><00:02:43.360><c> do</c>

00:02:43.589 --> 00:02:43.599 align:start position:0%
most value out of this course but i do
 

00:02:43.599 --> 00:02:44.949 align:start position:0%
most value out of this course but i do
feel<00:02:43.840><c> like</c><00:02:44.000><c> this</c><00:02:44.239><c> course</c>

00:02:44.949 --> 00:02:44.959 align:start position:0%
feel like this course
 

00:02:44.959 --> 00:02:48.070 align:start position:0%
feel like this course
will<00:02:45.280><c> also</c><00:02:46.480><c> be</c><00:02:46.640><c> relevant</c><00:02:47.200><c> for</c>

00:02:48.070 --> 00:02:48.080 align:start position:0%
will also be relevant for
 

00:02:48.080 --> 00:02:50.229 align:start position:0%
will also be relevant for
higher<00:02:48.560><c> apps</c><00:02:48.879><c> which</c><00:02:49.200><c> pretty</c><00:02:49.360><c> much</c><00:02:49.680><c> follow</c><00:02:50.000><c> the</c>

00:02:50.229 --> 00:02:50.239 align:start position:0%
higher apps which pretty much follow the
 

00:02:50.239 --> 00:02:51.670 align:start position:0%
higher apps which pretty much follow the
same<00:02:50.480><c> methodology</c>

00:02:51.670 --> 00:02:51.680 align:start position:0%
same methodology
 

00:02:51.680 --> 00:02:52.869 align:start position:0%
same methodology
all<00:02:51.760><c> right</c><00:02:52.000><c> so</c><00:02:52.160><c> that's</c><00:02:52.400><c> pretty</c><00:02:52.640><c> much</c><00:02:52.800><c> the</c>

00:02:52.869 --> 00:02:52.879 align:start position:0%
all right so that's pretty much the
 

00:02:52.879 --> 00:02:54.710 align:start position:0%
all right so that's pretty much the
methodology<00:02:53.599><c> that</c><00:02:53.680><c> we're</c><00:02:53.840><c> going</c><00:02:53.920><c> to</c><00:02:54.000><c> be</c><00:02:54.160><c> using</c>

00:02:54.710 --> 00:02:54.720 align:start position:0%
methodology that we're going to be using
 

00:02:54.720 --> 00:02:56.630 align:start position:0%
methodology that we're going to be using
lean<00:02:55.040><c> startup</c><00:02:55.920><c> uh</c><00:02:56.160><c> based</c><00:02:56.480><c> on</c><00:02:56.560><c> the</c>

00:02:56.630 --> 00:02:56.640 align:start position:0%
lean startup uh based on the
 

00:02:56.640 --> 00:02:58.470 align:start position:0%
lean startup uh based on the
best-selling<00:02:57.360><c> book</c><00:02:57.680><c> and</c><00:02:57.840><c> i'm</c><00:02:57.920><c> really</c><00:02:58.159><c> excited</c>

00:02:58.470 --> 00:02:58.480 align:start position:0%
best-selling book and i'm really excited
 

00:02:58.480 --> 00:02:59.910 align:start position:0%
best-selling book and i'm really excited
to<00:02:58.640><c> give</c><00:02:58.800><c> you</c><00:02:58.959><c> guys</c><00:02:59.120><c> these</c><00:02:59.360><c> real-world</c>

00:02:59.910 --> 00:02:59.920 align:start position:0%
to give you guys these real-world
 

00:02:59.920 --> 00:03:01.830 align:start position:0%
to give you guys these real-world
examples<00:03:00.480><c> and</c><00:03:00.560><c> these</c><00:03:00.879><c> real-world</c>

00:03:01.830 --> 00:03:01.840 align:start position:0%
examples and these real-world
 

00:03:01.840 --> 00:03:04.229 align:start position:0%
examples and these real-world
real-world<00:03:02.560><c> tools</c><00:03:02.959><c> like</c><00:03:03.200><c> google</c><00:03:03.519><c> analytics</c>

00:03:04.229 --> 00:03:04.239 align:start position:0%
real-world tools like google analytics
 

00:03:04.239 --> 00:03:05.350 align:start position:0%
real-world tools like google analytics
metabase

00:03:05.350 --> 00:03:05.360 align:start position:0%
metabase
 

00:03:05.360 --> 00:03:07.509 align:start position:0%
metabase
active<00:03:05.680><c> campaign</c><00:03:06.159><c> email</c><00:03:06.400><c> marketing</c><00:03:07.280><c> meet</c>

00:03:07.509 --> 00:03:07.519 align:start position:0%
active campaign email marketing meet
 

00:03:07.519 --> 00:03:09.110 align:start position:0%
active campaign email marketing meet
alfred<00:03:07.840><c> linkedin</c><00:03:08.239><c> automation</c>

00:03:09.110 --> 00:03:09.120 align:start position:0%
alfred linkedin automation
 

00:03:09.120 --> 00:03:11.270 align:start position:0%
alfred linkedin automation
and<00:03:09.280><c> both</c><00:03:09.519><c> for</c><00:03:09.680><c> the</c><00:03:09.840><c> shopify</c><00:03:10.400><c> and</c><00:03:10.560><c> the</c><00:03:10.720><c> amazon</c>

00:03:11.270 --> 00:03:11.280 align:start position:0%
and both for the shopify and the amazon
 

00:03:11.280 --> 00:03:13.350 align:start position:0%
and both for the shopify and the amazon
ecosystem<00:03:12.000><c> thanks</c><00:03:12.239><c> guys</c><00:03:12.480><c> for</c><00:03:12.640><c> tuning</c><00:03:12.959><c> in</c><00:03:13.120><c> feel</c>

00:03:13.350 --> 00:03:13.360 align:start position:0%
ecosystem thanks guys for tuning in feel
 

00:03:13.360 --> 00:03:13.990 align:start position:0%
ecosystem thanks guys for tuning in feel
free<00:03:13.519><c> to</c>

00:03:13.990 --> 00:03:14.000 align:start position:0%
free to
 

00:03:14.000 --> 00:03:15.830 align:start position:0%
free to
reach<00:03:14.319><c> out</c><00:03:14.800><c> if</c><00:03:14.959><c> you</c><00:03:15.040><c> have</c><00:03:15.280><c> any</c><00:03:15.519><c> questions</c>

00:03:15.830 --> 00:03:15.840 align:start position:0%
reach out if you have any questions
 

00:03:15.840 --> 00:03:17.589 align:start position:0%
reach out if you have any questions
whatsoever<00:03:16.480><c> along</c><00:03:16.800><c> the</c><00:03:16.879><c> way</c><00:03:17.120><c> and</c><00:03:17.200><c> i</c><00:03:17.360><c> look</c>

00:03:17.589 --> 00:03:17.599 align:start position:0%
whatsoever along the way and i look
 

00:03:17.599 --> 00:03:19.670 align:start position:0%
whatsoever along the way and i look
forward<00:03:17.920><c> to</c><00:03:18.080><c> teaching</c><00:03:18.400><c> you</c>

00:03:19.670 --> 00:03:19.680 align:start position:0%
forward to teaching you
 

00:03:19.680 --> 00:03:25.390 align:start position:0%
forward to teaching you
these<00:03:20.239><c> very</c><00:03:20.560><c> in-demand</c><00:03:21.040><c> skills</c><00:03:21.519><c> within</c><00:03:22.080><c> this</c>

00:03:25.390 --> 00:03:25.400 align:start position:0%
 
 

00:03:25.400 --> 00:03:28.400 align:start position:0%
 
course

