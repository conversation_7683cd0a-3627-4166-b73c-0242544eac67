WEBVTT
Kind: captions
Language: en

00:00:00.370 --> 00:00:00.750 align:start position:0%
 
[Music]

00:00:00.750 --> 00:00:00.760 align:start position:0%
[Music]
 

00:00:00.760 --> 00:00:03.510 align:start position:0%
[Music]
this<00:00:01.240><c> is</c><00:00:01.400><c> the</c><00:00:01.520><c> auto</c><00:00:01.880><c> grock</c><00:00:02.200><c> frecking</c><00:00:02.560><c> tutorial</c>

00:00:03.510 --> 00:00:03.520 align:start position:0%
this is the auto grock frecking tutorial
 

00:00:03.520 --> 00:00:05.410 align:start position:0%
this is the auto grock frecking tutorial
lesson<00:00:03.840><c> two</c><00:00:04.560><c> installing</c>

00:00:05.410 --> 00:00:05.420 align:start position:0%
lesson two installing
 

00:00:05.420 --> 00:00:13.230 align:start position:0%
lesson two installing
[Music]

00:00:13.230 --> 00:00:13.240 align:start position:0%
[Music]
 

00:00:13.240 --> 00:00:16.029 align:start position:0%
[Music]
crap<00:00:14.240><c> you</c><00:00:14.360><c> don't</c><00:00:14.599><c> need</c><00:00:14.839><c> to</c><00:00:15.080><c> install</c><00:00:15.440><c> autogen</c>

00:00:16.029 --> 00:00:16.039 align:start position:0%
crap you don't need to install autogen
 

00:00:16.039 --> 00:00:18.150 align:start position:0%
crap you don't need to install autogen
or<00:00:16.199><c> crew</c><00:00:16.520><c> AI</c><00:00:16.840><c> to</c><00:00:17.000><c> work</c><00:00:17.160><c> with</c><00:00:17.320><c> autog</c><00:00:17.640><c> Gro</c><00:00:18.000><c> but</c>

00:00:18.150 --> 00:00:18.160 align:start position:0%
or crew AI to work with autog Gro but
 

00:00:18.160 --> 00:00:19.710 align:start position:0%
or crew AI to work with autog Gro but
you<00:00:18.240><c> won't</c><00:00:18.480><c> be</c><00:00:18.640><c> getting</c><00:00:18.880><c> the</c><00:00:19.000><c> full</c><00:00:19.279><c> benefit</c><00:00:19.600><c> of</c>

00:00:19.710 --> 00:00:19.720 align:start position:0%
you won't be getting the full benefit of
 

00:00:19.720 --> 00:00:21.910 align:start position:0%
you won't be getting the full benefit of
the<00:00:19.880><c> software</c><00:00:20.640><c> maybe</c><00:00:20.880><c> when</c><00:00:21.039><c> autog</c><00:00:21.359><c> Gro</c><00:00:21.680><c> grows</c>

00:00:21.910 --> 00:00:21.920 align:start position:0%
the software maybe when autog Gro grows
 

00:00:21.920 --> 00:00:23.790 align:start position:0%
the software maybe when autog Gro grows
up<00:00:22.080><c> a</c><00:00:22.199><c> little</c><00:00:22.359><c> more</c><00:00:22.640><c> it</c><00:00:22.720><c> will</c><00:00:22.880><c> be</c><00:00:23.039><c> as</c><00:00:23.199><c> robust</c><00:00:23.640><c> a</c>

00:00:23.790 --> 00:00:23.800 align:start position:0%
up a little more it will be as robust a
 

00:00:23.800 --> 00:00:25.710 align:start position:0%
up a little more it will be as robust a
standalone<00:00:24.439><c> package</c><00:00:24.760><c> as</c><00:00:24.920><c> Auto</c><00:00:25.199><c> Jen</c><00:00:25.439><c> is</c><00:00:25.599><c> but</c>

00:00:25.710 --> 00:00:25.720 align:start position:0%
standalone package as Auto Jen is but
 

00:00:25.720 --> 00:00:27.710 align:start position:0%
standalone package as Auto Jen is but
we're<00:00:25.880><c> not</c><00:00:26.080><c> there</c><00:00:26.320><c> yet</c><00:00:26.880><c> heck</c><00:00:27.039><c> Auto</c><00:00:27.320><c> Gro</c><00:00:27.599><c> is</c>

00:00:27.710 --> 00:00:27.720 align:start position:0%
we're not there yet heck Auto Gro is
 

00:00:27.720 --> 00:00:30.109 align:start position:0%
we're not there yet heck Auto Gro is
barely<00:00:27.960><c> a</c><00:00:28.080><c> month</c><00:00:28.279><c> old</c><00:00:28.960><c> so</c><00:00:29.199><c> I</c><00:00:29.320><c> am</c><00:00:29.519><c> going</c><00:00:29.720><c> to</c>

00:00:30.109 --> 00:00:30.119 align:start position:0%
barely a month old so I am going to
 

00:00:30.119 --> 00:00:32.310 align:start position:0%
barely a month old so I am going to
strongly<00:00:30.359><c> recommend</c><00:00:30.720><c> you</c><00:00:30.920><c> install</c><00:00:31.400><c> autogen</c>

00:00:32.310 --> 00:00:32.320 align:start position:0%
strongly recommend you install autogen
 

00:00:32.320 --> 00:00:34.630 align:start position:0%
strongly recommend you install autogen
and<00:00:32.520><c> autogen</c><00:00:33.160><c> Studio</c><00:00:33.800><c> Matt</c><00:00:34.000><c> Burman</c><00:00:34.399><c> has</c><00:00:34.520><c> a</c>

00:00:34.630 --> 00:00:34.640 align:start position:0%
and autogen Studio Matt Burman has a
 

00:00:34.640 --> 00:00:36.430 align:start position:0%
and autogen Studio Matt Burman has a
great<00:00:34.879><c> video</c><00:00:35.160><c> on</c><00:00:35.280><c> how</c><00:00:35.399><c> to</c><00:00:35.520><c> do</c><00:00:35.719><c> that</c><00:00:36.079><c> I'll</c><00:00:36.239><c> link</c>

00:00:36.430 --> 00:00:36.440 align:start position:0%
great video on how to do that I'll link
 

00:00:36.440 --> 00:00:38.389 align:start position:0%
great video on how to do that I'll link
to<00:00:36.600><c> it</c><00:00:36.719><c> in</c><00:00:36.840><c> the</c><00:00:37.000><c> description</c><00:00:37.480><c> below</c><00:00:38.120><c> pause</c>

00:00:38.389 --> 00:00:38.399 align:start position:0%
to it in the description below pause
 

00:00:38.399 --> 00:00:39.790 align:start position:0%
to it in the description below pause
this<00:00:38.600><c> video</c><00:00:38.879><c> and</c><00:00:39.040><c> come</c><00:00:39.239><c> back</c><00:00:39.399><c> to</c><00:00:39.559><c> it</c><00:00:39.680><c> when</c>

00:00:39.790 --> 00:00:39.800 align:start position:0%
this video and come back to it when
 

00:00:39.800 --> 00:00:42.430 align:start position:0%
this video and come back to it when
you're<00:00:40.480><c> done</c><00:00:41.480><c> are</c><00:00:41.640><c> they</c>

00:00:42.430 --> 00:00:42.440 align:start position:0%
you're done are they
 

00:00:42.440 --> 00:00:46.270 align:start position:0%
you're done are they
gone<00:00:43.440><c> do</c><00:00:44.160><c> do</c><00:00:44.320><c> I</c><00:00:44.440><c> have</c><00:00:44.600><c> enough</c><00:00:44.840><c> time</c><00:00:45.000><c> to</c><00:00:45.280><c> pee</c>

00:00:46.270 --> 00:00:46.280 align:start position:0%
gone do do I have enough time to pee
 

00:00:46.280 --> 00:00:47.260 align:start position:0%
gone do do I have enough time to pee
make<00:00:46.440><c> it</c>

00:00:47.260 --> 00:00:47.270 align:start position:0%
make it
 

00:00:47.270 --> 00:00:53.349 align:start position:0%
make it
[Music]

00:00:53.349 --> 00:00:53.359 align:start position:0%
 
 

00:00:53.359 --> 00:00:55.549 align:start position:0%
 
quick<00:00:54.359><c> if</c><00:00:54.480><c> you're</c><00:00:54.600><c> on</c><00:00:54.800><c> Windows</c><00:00:55.239><c> you'll</c><00:00:55.440><c> want</c>

00:00:55.549 --> 00:00:55.559 align:start position:0%
quick if you're on Windows you'll want
 

00:00:55.559 --> 00:00:58.349 align:start position:0%
quick if you're on Windows you'll want
to<00:00:55.760><c> install</c><00:00:56.239><c> miniconda</c><00:00:57.239><c> that</c><00:00:57.359><c> link</c><00:00:57.640><c> will</c><00:00:57.960><c> also</c>

00:00:58.349 --> 00:00:58.359 align:start position:0%
to install miniconda that link will also
 

00:00:58.359 --> 00:01:00.470 align:start position:0%
to install miniconda that link will also
be<00:00:58.559><c> below</c><00:00:59.120><c> autog</c><00:00:59.480><c> grock</c><00:00:59.719><c> will</c><00:01:00.079><c> probably</c>

00:01:00.470 --> 00:01:00.480 align:start position:0%
be below autog grock will probably
 

00:01:00.480 --> 00:01:02.110 align:start position:0%
be below autog grock will probably
install<00:01:00.879><c> fine</c><00:01:01.160><c> on</c><00:01:01.280><c> a</c><00:01:01.399><c> standard</c><00:01:01.760><c> Windows</c>

00:01:02.110 --> 00:01:02.120 align:start position:0%
install fine on a standard Windows
 

00:01:02.120 --> 00:01:05.229 align:start position:0%
install fine on a standard Windows
machine<00:01:02.480><c> running</c><00:01:02.879><c> python</c><00:01:03.400><c> 311</c><00:01:04.400><c> probably</c><00:01:05.080><c> but</c>

00:01:05.229 --> 00:01:05.239 align:start position:0%
machine running python 311 probably but
 

00:01:05.239 --> 00:01:07.270 align:start position:0%
machine running python 311 probably but
why<00:01:05.439><c> chance</c><00:01:05.720><c> it</c><00:01:06.040><c> cond</c><00:01:06.439><c> is</c>

00:01:07.270 --> 00:01:07.280 align:start position:0%
why chance it cond is
 

00:01:07.280 --> 00:01:10.390 align:start position:0%
why chance it cond is
cleaner<00:01:08.280><c> go</c><00:01:08.439><c> to</c><00:01:08.600><c> my</c><00:01:08.799><c> GitHub</c><00:01:09.360><c> repository</c><00:01:10.200><c> click</c>

00:01:10.390 --> 00:01:10.400 align:start position:0%
cleaner go to my GitHub repository click
 

00:01:10.400 --> 00:01:12.550 align:start position:0%
cleaner go to my GitHub repository click
on<00:01:10.560><c> the</c><00:01:10.680><c> ugly</c><00:01:11.040><c> green</c><00:01:11.400><c> code</c><00:01:11.759><c> button</c><00:01:12.040><c> and</c><00:01:12.200><c> select</c>

00:01:12.550 --> 00:01:12.560 align:start position:0%
on the ugly green code button and select
 

00:01:12.560 --> 00:01:14.950 align:start position:0%
on the ugly green code button and select
the<00:01:12.680><c> little</c><00:01:13.000><c> copy</c><00:01:13.400><c> icon</c><00:01:13.960><c> then</c><00:01:14.200><c> from</c><00:01:14.360><c> a</c><00:01:14.520><c> command</c>

00:01:14.950 --> 00:01:14.960 align:start position:0%
the little copy icon then from a command
 

00:01:14.960 --> 00:01:18.030 align:start position:0%
the little copy icon then from a command
prompt<00:01:15.560><c> create</c><00:01:15.960><c> a</c><00:01:16.119><c> directory</c><00:01:16.600><c> for</c><00:01:16.799><c> auto</c>

00:01:18.030 --> 00:01:18.040 align:start position:0%
prompt create a directory for auto
 

00:01:18.040 --> 00:01:21.230 align:start position:0%
prompt create a directory for auto
Gro<00:01:19.040><c> CD</c><00:01:19.479><c> into</c><00:01:19.759><c> it</c><00:01:19.920><c> and</c><00:01:20.040><c> run</c><00:01:20.280><c> this</c><00:01:20.479><c> cond</c><00:01:20.880><c> create</c>

00:01:21.230 --> 00:01:21.240 align:start position:0%
Gro CD into it and run this cond create
 

00:01:21.240 --> 00:01:24.069 align:start position:0%
Gro CD into it and run this cond create
command<00:01:21.920><c> specifying</c><00:01:22.520><c> python</c><00:01:22.880><c> 311</c><00:01:23.640><c> a</c><00:01:23.720><c> bunch</c><00:01:23.960><c> of</c>

00:01:24.069 --> 00:01:24.079 align:start position:0%
command specifying python 311 a bunch of
 

00:01:24.079 --> 00:01:25.789 align:start position:0%
command specifying python 311 a bunch of
confusing<00:01:24.560><c> stuff</c><00:01:24.799><c> that</c><00:01:24.920><c> nobody</c><00:01:25.560><c> understands</c>

00:01:25.789 --> 00:01:25.799 align:start position:0%
confusing stuff that nobody understands
 

00:01:25.799 --> 00:01:27.749 align:start position:0%
confusing stuff that nobody understands
will<00:01:26.000><c> garbage</c><00:01:26.320><c> up</c><00:01:26.479><c> your</c><00:01:26.640><c> screen</c><00:01:27.200><c> if</c><00:01:27.320><c> anybody's</c>

00:01:27.749 --> 00:01:27.759 align:start position:0%
will garbage up your screen if anybody's
 

00:01:27.759 --> 00:01:29.550 align:start position:0%
will garbage up your screen if anybody's
watching<00:01:28.040><c> you</c><00:01:28.439><c> nod</c><00:01:28.799><c> as</c><00:01:28.920><c> if</c><00:01:29.040><c> you</c><00:01:29.159><c> know</c><00:01:29.360><c> what</c><00:01:29.479><c> it</c>

00:01:29.550 --> 00:01:29.560 align:start position:0%
watching you nod as if you know what it
 

00:01:29.560 --> 00:01:32.469 align:start position:0%
watching you nod as if you know what it
means<00:01:30.280><c> it</c><00:01:30.400><c> makes</c><00:01:30.640><c> you</c><00:01:30.799><c> look</c><00:01:31.000><c> very</c>

00:01:32.469 --> 00:01:32.479 align:start position:0%
means it makes you look very
 

00:01:32.479 --> 00:01:35.550 align:start position:0%
means it makes you look very
smart<00:01:33.479><c> run</c><00:01:33.720><c> the</c><00:01:33.840><c> cond</c><00:01:34.280><c> activate</c><00:01:34.680><c> command</c><00:01:35.399><c> then</c>

00:01:35.550 --> 00:01:35.560 align:start position:0%
smart run the cond activate command then
 

00:01:35.560 --> 00:01:37.270 align:start position:0%
smart run the cond activate command then
run<00:01:35.759><c> this</c><00:01:35.920><c> git</c><00:01:36.119><c> clone</c><00:01:36.439><c> command</c><00:01:36.759><c> and</c><00:01:36.960><c> paste</c><00:01:37.159><c> in</c>

00:01:37.270 --> 00:01:37.280 align:start position:0%
run this git clone command and paste in
 

00:01:37.280 --> 00:01:42.030 align:start position:0%
run this git clone command and paste in
the<00:01:37.439><c> path</c><00:01:37.640><c> to</c><00:01:37.799><c> the</c><00:01:37.960><c> GitHub</c><00:01:38.360><c> archive</c><00:01:38.720><c> we</c><00:01:38.880><c> copied</c>

00:01:42.030 --> 00:01:42.040 align:start position:0%
 
 

00:01:42.040 --> 00:01:44.429 align:start position:0%
 
earlier<00:01:43.040><c> autog</c><00:01:43.360><c> gr</c><00:01:43.640><c> depends</c><00:01:43.920><c> on</c><00:01:44.119><c> seven</c>

00:01:44.429 --> 00:01:44.439 align:start position:0%
earlier autog gr depends on seven
 

00:01:44.439 --> 00:01:46.429 align:start position:0%
earlier autog gr depends on seven
different<00:01:44.840><c> packages</c><00:01:45.280><c> to</c><00:01:45.439><c> run</c><00:01:46.079><c> but</c><00:01:46.200><c> you</c><00:01:46.280><c> can</c>

00:01:46.429 --> 00:01:46.439 align:start position:0%
different packages to run but you can
 

00:01:46.439 --> 00:01:48.190 align:start position:0%
different packages to run but you can
install<00:01:46.759><c> them</c><00:01:46.920><c> all</c><00:01:47.079><c> at</c><00:01:47.240><c> once</c><00:01:47.520><c> by</c><00:01:47.680><c> finding</c><00:01:48.040><c> the</c>

00:01:48.190 --> 00:01:48.200 align:start position:0%
install them all at once by finding the
 

00:01:48.200 --> 00:01:50.310 align:start position:0%
install them all at once by finding the
directory<00:01:48.640><c> I</c><00:01:48.799><c> hid</c><00:01:49.000><c> the</c><00:01:49.320><c> requirements.txt</c>

00:01:50.310 --> 00:01:50.320 align:start position:0%
directory I hid the requirements.txt
 

00:01:50.320 --> 00:01:53.109 align:start position:0%
directory I hid the requirements.txt
file<00:01:51.159><c> in</c><00:01:51.439><c> and</c><00:01:51.560><c> running</c><00:01:51.840><c> the</c><00:01:52.000><c> PIP</c><00:01:52.240><c> install</c><00:01:52.719><c> Dash</c>

00:01:53.109 --> 00:01:53.119 align:start position:0%
file in and running the PIP install Dash
 

00:01:53.119 --> 00:01:55.789 align:start position:0%
file in and running the PIP install Dash
R<00:01:53.600><c> requirements.txt</c>

00:01:55.789 --> 00:01:55.799 align:start position:0%
R requirements.txt
 

00:01:55.799 --> 00:01:58.310 align:start position:0%
R requirements.txt
command<00:01:56.799><c> this</c><00:01:56.960><c> too</c><00:01:57.320><c> will</c><00:01:57.479><c> flood</c><00:01:57.840><c> the</c><00:01:57.920><c> screen</c>

00:01:58.310 --> 00:01:58.320 align:start position:0%
command this too will flood the screen
 

00:01:58.320 --> 00:02:00.990 align:start position:0%
command this too will flood the screen
with<00:01:58.560><c> incomprehensible</c><00:01:59.399><c> nonsense</c><00:02:00.000><c> sense</c>

00:02:00.990 --> 00:02:01.000 align:start position:0%
with incomprehensible nonsense sense
 

00:02:01.000 --> 00:02:02.789 align:start position:0%
with incomprehensible nonsense sense
some<00:02:01.200><c> of</c><00:02:01.320><c> the</c><00:02:01.479><c> nonsense</c><00:02:02.000><c> will</c><00:02:02.159><c> be</c><00:02:02.320><c> in</c><00:02:02.479><c> bright</c>

00:02:02.789 --> 00:02:02.799 align:start position:0%
some of the nonsense will be in bright
 

00:02:02.799 --> 00:02:05.749 align:start position:0%
some of the nonsense will be in bright
red<00:02:03.640><c> you</c><00:02:03.799><c> can</c><00:02:04.000><c> ignore</c><00:02:04.360><c> it</c><00:02:05.000><c> apparently</c><00:02:05.520><c> it's</c>

00:02:05.749 --> 00:02:05.759 align:start position:0%
red you can ignore it apparently it's
 

00:02:05.759 --> 00:02:08.389 align:start position:0%
red you can ignore it apparently it's
not<00:02:06.039><c> important</c><00:02:06.560><c> unless</c><00:02:06.840><c> it</c><00:02:07.000><c> flashes</c><00:02:07.439><c> and</c>

00:02:08.389 --> 00:02:08.399 align:start position:0%
not important unless it flashes and
 

00:02:08.399 --> 00:02:12.430 align:start position:0%
not important unless it flashes and
beeps<00:02:09.399><c> and</c><00:02:09.560><c> we're</c><00:02:10.319><c> done</c><00:02:11.319><c> open</c><00:02:11.680><c> whichever</c><00:02:12.120><c> code</c>

00:02:12.430 --> 00:02:12.440 align:start position:0%
beeps and we're done open whichever code
 

00:02:12.440 --> 00:02:14.670 align:start position:0%
beeps and we're done open whichever code
editor<00:02:12.879><c> you</c><00:02:13.080><c> use</c><00:02:13.520><c> and</c><00:02:13.720><c> from</c><00:02:13.959><c> inside</c><00:02:14.480><c> that</c>

00:02:14.670 --> 00:02:14.680 align:start position:0%
editor you use and from inside that
 

00:02:14.680 --> 00:02:17.750 align:start position:0%
editor you use and from inside that
editor<00:02:15.360><c> open</c><00:02:15.640><c> a</c><00:02:15.879><c> terminal</c><00:02:16.319><c> window</c><00:02:17.200><c> to</c><00:02:17.400><c> run</c>

00:02:17.750 --> 00:02:17.760 align:start position:0%
editor open a terminal window to run
 

00:02:17.760 --> 00:02:19.949 align:start position:0%
editor open a terminal window to run
autog<00:02:18.200><c> gr</c><00:02:18.560><c> you</c><00:02:18.760><c> simply</c><00:02:19.120><c> need</c><00:02:19.360><c> to</c><00:02:19.560><c> call</c><00:02:19.840><c> the</c>

00:02:19.949 --> 00:02:19.959 align:start position:0%
autog gr you simply need to call the
 

00:02:19.959 --> 00:02:23.070 align:start position:0%
autog gr you simply need to call the
streamlit<00:02:20.760><c> Run</c><00:02:21.160><c> command</c><00:02:22.040><c> and</c><00:02:22.280><c> specify</c><00:02:22.840><c> the</c>

00:02:23.070 --> 00:02:23.080 align:start position:0%
streamlit Run command and specify the
 

00:02:23.080 --> 00:02:25.589 align:start position:0%
streamlit Run command and specify the
path<00:02:23.319><c> to</c><00:02:23.480><c> the</c><00:02:24.040><c> main.py</c>

00:02:25.589 --> 00:02:25.599 align:start position:0%
path to the main.py
 

00:02:25.599 --> 00:02:28.350 align:start position:0%
path to the main.py
file<00:02:26.599><c> here</c><00:02:26.800><c> I</c><00:02:27.000><c> demonstrate</c><00:02:27.519><c> what</c><00:02:27.640><c> a</c><00:02:27.800><c> bone</c><00:02:28.080><c> head</c>

00:02:28.350 --> 00:02:28.360 align:start position:0%
file here I demonstrate what a bone head
 

00:02:28.360 --> 00:02:30.949 align:start position:0%
file here I demonstrate what a bone head
I<00:02:28.519><c> am</c><00:02:28.920><c> by</c><00:02:29.160><c> getting</c><00:02:29.400><c> the</c><00:02:29.599><c> path</c><00:02:30.120><c> wrong</c><00:02:30.519><c> don't</c><00:02:30.760><c> do</c>

00:02:30.949 --> 00:02:30.959 align:start position:0%
I am by getting the path wrong don't do
 

00:02:30.959 --> 00:02:33.190 align:start position:0%
I am by getting the path wrong don't do
that<00:02:31.239><c> type</c><00:02:31.440><c> in</c><00:02:31.599><c> the</c><00:02:31.800><c> right</c><00:02:32.080><c> path</c><00:02:32.400><c> it</c><00:02:32.560><c> works</c><00:02:32.959><c> way</c>

00:02:33.190 --> 00:02:33.200 align:start position:0%
that type in the right path it works way
 

00:02:33.200 --> 00:02:35.509 align:start position:0%
that type in the right path it works way
better<00:02:33.560><c> that</c><00:02:33.760><c> way</c><00:02:34.599><c> thanks</c><00:02:34.879><c> for</c><00:02:35.080><c> watching</c>

00:02:35.509 --> 00:02:35.519 align:start position:0%
better that way thanks for watching
 

00:02:35.519 --> 00:02:38.309 align:start position:0%
better that way thanks for watching
there<00:02:35.599><c> are</c><00:02:35.800><c> more</c><00:02:36.120><c> tutorials</c><00:02:36.640><c> to</c><00:02:37.200><c> come</c><00:02:38.200><c> what</c>

00:02:38.309 --> 00:02:38.319 align:start position:0%
there are more tutorials to come what
 

00:02:38.319 --> 00:02:40.300 align:start position:0%
there are more tutorials to come what
did<00:02:38.440><c> I</c><00:02:38.640><c> miss</c><00:02:39.280><c> did</c><00:02:39.440><c> I</c><00:02:39.599><c> miss</c>

00:02:40.300 --> 00:02:40.310 align:start position:0%
did I miss did I miss
 

00:02:40.310 --> 00:02:45.589 align:start position:0%
did I miss did I miss
[Music]

00:02:45.589 --> 00:02:45.599 align:start position:0%
 
 

00:02:45.599 --> 00:02:47.710 align:start position:0%
 
anything

00:02:47.710 --> 00:02:47.720 align:start position:0%
anything
 

00:02:47.720 --> 00:02:50.030 align:start position:0%
anything
autut<00:02:48.720><c> aut</c>

00:02:50.030 --> 00:02:50.040 align:start position:0%
autut aut
 

00:02:50.040 --> 00:02:55.350 align:start position:0%
autut aut
autut<00:02:51.239><c> aut</c><00:02:52.239><c> autut</c>

00:02:55.350 --> 00:02:55.360 align:start position:0%
 
 

00:02:55.360 --> 00:03:01.860 align:start position:0%
 
[Music]

