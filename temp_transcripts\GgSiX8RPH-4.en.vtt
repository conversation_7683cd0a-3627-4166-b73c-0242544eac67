WEBVTT
Kind: captions
Language: en

00:00:00.030 --> 00:00:01.850 align:start position:0%
 
hey<00:00:00.750><c> you</c><00:00:00.810><c> know</c><00:00:01.020><c> pick</c><00:00:01.199><c> another</c><00:00:01.380><c> date</c><00:00:01.620><c> for</c><00:00:01.650><c> this</c>

00:00:01.850 --> 00:00:01.860 align:start position:0%
hey you know pick another date for this
 

00:00:01.860 --> 00:00:03.710 align:start position:0%
hey you know pick another date for this
video<00:00:02.159><c> and</c><00:00:02.490><c> this</c><00:00:03.179><c> time</c><00:00:03.240><c> we're</c><00:00:03.449><c> gonna</c><00:00:03.510><c> talking</c>

00:00:03.710 --> 00:00:03.720 align:start position:0%
video and this time we're gonna talking
 

00:00:03.720 --> 00:00:05.840 align:start position:0%
video and this time we're gonna talking
about<00:00:03.780><c> circular</c><00:00:04.259><c> link</c><00:00:04.440><c> lists</c><00:00:04.740><c> but</c><00:00:05.640><c> let's</c>

00:00:05.840 --> 00:00:05.850 align:start position:0%
about circular link lists but let's
 

00:00:05.850 --> 00:00:07.519 align:start position:0%
about circular link lists but let's
start<00:00:05.970><c> out</c><00:00:06.060><c> with</c><00:00:06.089><c> a</c><00:00:06.450><c> linked</c><00:00:06.779><c> list</c><00:00:06.839><c> first</c><00:00:07.290><c> a</c>

00:00:07.519 --> 00:00:07.529 align:start position:0%
start out with a linked list first a
 

00:00:07.529 --> 00:00:09.620 align:start position:0%
start out with a linked list first a
linked<00:00:08.160><c> list</c><00:00:08.370><c> or</c><00:00:08.700><c> generally</c><00:00:09.210><c> thought</c><00:00:09.330><c> of</c><00:00:09.389><c> as</c><00:00:09.570><c> a</c>

00:00:09.620 --> 00:00:09.630 align:start position:0%
linked list or generally thought of as a
 

00:00:09.630 --> 00:00:12.200 align:start position:0%
linked list or generally thought of as a
linear<00:00:09.900><c> sequence</c><00:00:10.290><c> of</c><00:00:10.710><c> data</c><00:00:10.920><c> and</c><00:00:11.280><c> so</c><00:00:12.090><c> that</c>

00:00:12.200 --> 00:00:12.210 align:start position:0%
linear sequence of data and so that
 

00:00:12.210 --> 00:00:13.999 align:start position:0%
linear sequence of data and so that
means<00:00:12.300><c> that</c><00:00:12.480><c> there</c><00:00:12.780><c> is</c><00:00:12.809><c> some</c><00:00:13.440><c> beginning</c><00:00:13.830><c> and</c>

00:00:13.999 --> 00:00:14.009 align:start position:0%
means that there is some beginning and
 

00:00:14.009 --> 00:00:16.580 align:start position:0%
means that there is some beginning and
there's<00:00:14.190><c> some</c><00:00:14.400><c> end</c><00:00:14.660><c> well</c><00:00:15.660><c> there's</c><00:00:16.020><c> a</c><00:00:16.230><c> lot</c><00:00:16.470><c> of</c>

00:00:16.580 --> 00:00:16.590 align:start position:0%
there's some end well there's a lot of
 

00:00:16.590 --> 00:00:18.349 align:start position:0%
there's some end well there's a lot of
applications<00:00:17.190><c> where</c><00:00:17.580><c> we</c><00:00:18.000><c> can</c><00:00:18.119><c> actually</c><00:00:18.210><c> think</c>

00:00:18.349 --> 00:00:18.359 align:start position:0%
applications where we can actually think
 

00:00:18.359 --> 00:00:21.890 align:start position:0%
applications where we can actually think
of<00:00:19.490><c> linked</c><00:00:20.490><c> lists</c><00:00:20.789><c> in</c><00:00:20.910><c> a</c><00:00:21.029><c> cyclic</c><00:00:21.720><c> order</c>

00:00:21.890 --> 00:00:21.900 align:start position:0%
of linked lists in a cyclic order
 

00:00:21.900 --> 00:00:24.920 align:start position:0%
of linked lists in a cyclic order
meaning<00:00:22.470><c> there</c><00:00:22.800><c> is</c><00:00:22.830><c> no</c><00:00:22.980><c> beginning</c><00:00:23.400><c> or</c><00:00:23.580><c> end</c><00:00:23.930><c> so</c>

00:00:24.920 --> 00:00:24.930 align:start position:0%
meaning there is no beginning or end so
 

00:00:24.930 --> 00:00:26.900 align:start position:0%
meaning there is no beginning or end so
we're<00:00:25.890><c> just</c><00:00:25.920><c> kind</c><00:00:26.189><c> of</c><00:00:26.250><c> repeating</c><00:00:26.670><c> a</c><00:00:26.849><c> pattern</c>

00:00:26.900 --> 00:00:26.910 align:start position:0%
we're just kind of repeating a pattern
 

00:00:26.910 --> 00:00:29.330 align:start position:0%
we're just kind of repeating a pattern
of<00:00:27.510><c> going</c><00:00:27.840><c> through</c><00:00:28.109><c> the</c><00:00:28.260><c> data</c><00:00:28.439><c> and</c><00:00:28.740><c> then</c>

00:00:29.330 --> 00:00:29.340 align:start position:0%
of going through the data and then
 

00:00:29.340 --> 00:00:32.569 align:start position:0%
of going through the data and then
starting<00:00:29.699><c> over</c><00:00:29.900><c> okay</c><00:00:30.900><c> there</c><00:00:31.380><c> you</c><00:00:32.189><c> know</c><00:00:32.279><c> once</c>

00:00:32.569 --> 00:00:32.579 align:start position:0%
starting over okay there you know once
 

00:00:32.579 --> 00:00:34.130 align:start position:0%
starting over okay there you know once
we<00:00:32.700><c> reach</c><00:00:32.850><c> the</c><00:00:33.000><c> end</c><00:00:33.120><c> we</c><00:00:33.420><c> go</c><00:00:33.540><c> right</c><00:00:33.780><c> back</c><00:00:33.960><c> to</c><00:00:33.989><c> the</c>

00:00:34.130 --> 00:00:34.140 align:start position:0%
we reach the end we go right back to the
 

00:00:34.140 --> 00:00:38.150 align:start position:0%
we reach the end we go right back to the
beginning<00:00:34.230><c> and</c><00:00:35.510><c> one</c><00:00:36.510><c> of</c><00:00:36.600><c> those</c><00:00:36.750><c> like</c><00:00:37.739><c> well</c><00:00:37.980><c> an</c>

00:00:38.150 --> 00:00:38.160 align:start position:0%
beginning and one of those like well an
 

00:00:38.160 --> 00:00:40.160 align:start position:0%
beginning and one of those like well an
example<00:00:38.700><c> of</c><00:00:38.940><c> this</c><00:00:39.180><c> would</c><00:00:39.420><c> be</c><00:00:39.480><c> a</c><00:00:39.870><c> typical</c>

00:00:40.160 --> 00:00:40.170 align:start position:0%
example of this would be a typical
 

00:00:40.170 --> 00:00:43.729 align:start position:0%
example of this would be a typical
multiplayer<00:00:41.129><c> turn-based</c><00:00:41.610><c> game</c><00:00:42.140><c> so</c><00:00:43.140><c> here</c><00:00:43.620><c> we</c>

00:00:43.729 --> 00:00:43.739 align:start position:0%
multiplayer turn-based game so here we
 

00:00:43.739 --> 00:00:46.940 align:start position:0%
multiplayer turn-based game so here we
have<00:00:43.950><c> here</c><00:00:44.940><c> we</c><00:00:45.000><c> have</c><00:00:45.090><c> six</c><00:00:45.360><c> players</c><00:00:45.680><c> the</c><00:00:46.680><c> first</c>

00:00:46.940 --> 00:00:46.950 align:start position:0%
have here we have six players the first
 

00:00:46.950 --> 00:00:50.660 align:start position:0%
have here we have six players the first
player<00:00:47.190><c> it's</c><00:00:47.550><c> his</c><00:00:48.030><c> turn</c><00:00:48.239><c> so</c><00:00:48.899><c> he's</c><00:00:49.620><c> up</c><00:00:49.860><c> he</c>

00:00:50.660 --> 00:00:50.670 align:start position:0%
player it's his turn so he's up he
 

00:00:50.670 --> 00:00:52.400 align:start position:0%
player it's his turn so he's up he
performs<00:00:51.270><c> whatever</c><00:00:51.539><c> actions</c><00:00:51.930><c> he</c><00:00:51.989><c> needs</c><00:00:52.170><c> to</c><00:00:52.289><c> on</c>

00:00:52.400 --> 00:00:52.410 align:start position:0%
performs whatever actions he needs to on
 

00:00:52.410 --> 00:00:54.529 align:start position:0%
performs whatever actions he needs to on
his<00:00:52.559><c> turn</c><00:00:52.800><c> and</c><00:00:53.070><c> then</c><00:00:53.610><c> when</c><00:00:53.760><c> he's</c><00:00:53.879><c> done</c><00:00:53.910><c> he's</c>

00:00:54.529 --> 00:00:54.539 align:start position:0%
his turn and then when he's done he's
 

00:00:54.539 --> 00:00:56.090 align:start position:0%
his turn and then when he's done he's
kind<00:00:54.809><c> of</c><00:00:54.870><c> put</c><00:00:55.020><c> it</c><00:00:55.079><c> at</c><00:00:55.140><c> the</c><00:00:55.260><c> back</c><00:00:55.680><c> at</c><00:00:55.860><c> the</c><00:00:55.920><c> end</c><00:00:55.949><c> of</c>

00:00:56.090 --> 00:00:56.100 align:start position:0%
kind of put it at the back at the end of
 

00:00:56.100 --> 00:00:57.680 align:start position:0%
kind of put it at the back at the end of
the<00:00:56.190><c> line</c><00:00:56.370><c> and</c><00:00:56.820><c> so</c><00:00:57.000><c> then</c><00:00:57.180><c> the</c><00:00:57.270><c> next</c><00:00:57.510><c> five</c>

00:00:57.680 --> 00:00:57.690 align:start position:0%
the line and so then the next five
 

00:00:57.690 --> 00:01:00.200 align:start position:0%
the line and so then the next five
players<00:00:58.250><c> you</c><00:00:59.250><c> know</c><00:00:59.370><c> everybody</c><00:00:59.910><c> else</c><00:01:00.000><c> a</c><00:01:00.059><c> way</c><00:01:00.149><c> to</c>

00:01:00.200 --> 00:01:00.210 align:start position:0%
players you know everybody else a way to
 

00:01:00.210 --> 00:01:02.810 align:start position:0%
players you know everybody else a way to
take<00:01:00.420><c> their</c><00:01:00.600><c> turn</c><00:01:00.750><c> and</c><00:01:01.260><c> so</c><00:01:01.770><c> by</c><00:01:02.340><c> the</c><00:01:02.430><c> time</c><00:01:02.670><c> the</c>

00:01:02.810 --> 00:01:02.820 align:start position:0%
take their turn and so by the time the
 

00:01:02.820 --> 00:01:04.640 align:start position:0%
take their turn and so by the time the
next<00:01:02.969><c> five</c><00:01:03.090><c> players</c><00:01:03.270><c> are</c><00:01:03.480><c> done</c><00:01:03.660><c> then</c><00:01:04.470><c> his</c>

00:01:04.640 --> 00:01:04.650 align:start position:0%
next five players are done then his
 

00:01:04.650 --> 00:01:07.070 align:start position:0%
next five players are done then his
player<00:01:04.830><c> ones</c><00:01:05.070><c> turn</c><00:01:05.159><c> again</c><00:01:05.580><c> and</c><00:01:05.850><c> it</c><00:01:06.810><c> just</c><00:01:06.930><c> kind</c>

00:01:07.070 --> 00:01:07.080 align:start position:0%
player ones turn again and it just kind
 

00:01:07.080 --> 00:01:10.070 align:start position:0%
player ones turn again and it just kind
of<00:01:07.110><c> repeats</c><00:01:07.470><c> this</c><00:01:07.590><c> process</c><00:01:07.650><c> until</c><00:01:09.080><c> until</c>

00:01:10.070 --> 00:01:10.080 align:start position:0%
of repeats this process until until
 

00:01:10.080 --> 00:01:14.630 align:start position:0%
of repeats this process until until
somebody's<00:01:10.470><c> one</c><00:01:11.189><c> right</c><00:01:11.780><c> okay</c><00:01:12.780><c> so</c><00:01:12.840><c> a</c><00:01:13.640><c> circular</c>

00:01:14.630 --> 00:01:14.640 align:start position:0%
somebody's one right okay so a circular
 

00:01:14.640 --> 00:01:17.240 align:start position:0%
somebody's one right okay so a circular
linked<00:01:14.880><c> list</c><00:01:15.119><c> is</c><00:01:15.299><c> really</c><00:01:16.200><c> just</c><00:01:16.650><c> a</c><00:01:16.770><c> singly</c>

00:01:17.240 --> 00:01:17.250 align:start position:0%
linked list is really just a singly
 

00:01:17.250 --> 00:01:19.700 align:start position:0%
linked list is really just a singly
linked<00:01:17.430><c> list</c><00:01:17.700><c> where</c><00:01:18.390><c> the</c><00:01:18.720><c> tail</c><00:01:18.990><c> references</c>

00:01:19.700 --> 00:01:19.710 align:start position:0%
linked list where the tail references
 

00:01:19.710 --> 00:01:22.999 align:start position:0%
linked list where the tail references
the<00:01:19.979><c> head</c><00:01:20.600><c> in</c><00:01:21.600><c> a</c><00:01:21.810><c> singly</c><00:01:22.290><c> linked</c><00:01:22.500><c> list</c><00:01:22.710><c> the</c>

00:01:22.999 --> 00:01:23.009 align:start position:0%
the head in a singly linked list the
 

00:01:23.009 --> 00:01:25.010 align:start position:0%
the head in a singly linked list the
tail<00:01:23.189><c> Berman</c><00:01:23.460><c> says</c><00:01:23.610><c> null</c><00:01:23.880><c> the</c><00:01:24.570><c> meaning</c><00:01:24.900><c> that</c>

00:01:25.010 --> 00:01:25.020 align:start position:0%
tail Berman says null the meaning that
 

00:01:25.020 --> 00:01:26.990 align:start position:0%
tail Berman says null the meaning that
it<00:01:25.200><c> ends</c><00:01:25.590><c> and</c><00:01:25.770><c> like</c><00:01:26.040><c> I</c><00:01:26.070><c> said</c><00:01:26.310><c> this</c><00:01:26.430><c> is</c><00:01:26.580><c> a</c><00:01:26.640><c> cyclic</c>

00:01:26.990 --> 00:01:27.000 align:start position:0%
it ends and like I said this is a cyclic
 

00:01:27.000 --> 00:01:29.870 align:start position:0%
it ends and like I said this is a cyclic
order<00:01:27.210><c> so</c><00:01:27.840><c> it</c><00:01:28.049><c> doesn't</c><00:01:28.350><c> end</c><00:01:28.500><c> so</c><00:01:29.340><c> we</c><00:01:29.579><c> just</c><00:01:29.729><c> have</c>

00:01:29.870 --> 00:01:29.880 align:start position:0%
order so it doesn't end so we just have
 

00:01:29.880 --> 00:01:31.969 align:start position:0%
order so it doesn't end so we just have
the<00:01:30.000><c> tail</c><00:01:30.210><c> just</c><00:01:30.540><c> so</c><00:01:31.140><c> in</c><00:01:31.200><c> this</c><00:01:31.290><c> diagram</c><00:01:31.500><c> the</c>

00:01:31.969 --> 00:01:31.979 align:start position:0%
the tail just so in this diagram the
 

00:01:31.979 --> 00:01:35.719 align:start position:0%
the tail just so in this diagram the
tail<00:01:32.220><c> the</c><00:01:33.210><c> tails</c><00:01:33.390><c> next</c><00:01:33.600><c> reference</c><00:01:33.869><c> just</c><00:01:34.729><c> knows</c>

00:01:35.719 --> 00:01:35.729 align:start position:0%
tail the tails next reference just knows
 

00:01:35.729 --> 00:01:38.090 align:start position:0%
tail the tails next reference just knows
where<00:01:35.939><c> the</c><00:01:36.000><c> head</c><00:01:36.119><c> is</c><00:01:36.150><c> okay</c><00:01:36.860><c> so</c><00:01:37.860><c> there's</c><00:01:38.070><c> a</c>

00:01:38.090 --> 00:01:38.100 align:start position:0%
where the head is okay so there's a
 

00:01:38.100 --> 00:01:40.280 align:start position:0%
where the head is okay so there's a
couple<00:01:38.220><c> things</c><00:01:38.400><c> about</c><00:01:38.850><c> this</c><00:01:39.420><c> first</c><00:01:39.869><c> we</c><00:01:40.110><c> move</c>

00:01:40.280 --> 00:01:40.290 align:start position:0%
couple things about this first we move
 

00:01:40.290 --> 00:01:42.490 align:start position:0%
couple things about this first we move
on<00:01:40.470><c> is</c><00:01:41.070><c> there's</c><00:01:41.250><c> gonna</c><00:01:41.310><c> be</c><00:01:41.430><c> a</c><00:01:41.490><c> new</c><00:01:41.610><c> operation</c>

00:01:42.490 --> 00:01:42.500 align:start position:0%
on is there's gonna be a new operation
 

00:01:42.500 --> 00:01:45.109 align:start position:0%
on is there's gonna be a new operation
that<00:01:43.500><c> we're</c><00:01:43.619><c> gonna</c><00:01:43.740><c> be</c><00:01:44.189><c> adding</c><00:01:44.520><c> into</c><00:01:44.670><c> this</c><00:01:44.880><c> and</c>

00:01:45.109 --> 00:01:45.119 align:start position:0%
that we're gonna be adding into this and
 

00:01:45.119 --> 00:01:47.870 align:start position:0%
that we're gonna be adding into this and
that's<00:01:45.479><c> called</c><00:01:45.689><c> a</c><00:01:45.780><c> roti</c><00:01:46.110><c> and</c><00:01:46.350><c> all</c><00:01:47.070><c> that</c><00:01:47.250><c> is</c><00:01:47.490><c> is</c>

00:01:47.870 --> 00:01:47.880 align:start position:0%
that's called a roti and all that is is
 

00:01:47.880 --> 00:01:50.230 align:start position:0%
that's called a roti and all that is is
we're<00:01:48.030><c> rotating</c><00:01:48.180><c> the</c><00:01:48.600><c> head</c><00:01:48.780><c> node</c><00:01:49.020><c> to</c><00:01:49.740><c> the</c><00:01:49.860><c> end</c>

00:01:50.230 --> 00:01:50.240 align:start position:0%
we're rotating the head node to the end
 

00:01:50.240 --> 00:01:52.940 align:start position:0%
we're rotating the head node to the end
that's<00:01:51.240><c> it</c><00:01:51.509><c> and</c><00:01:51.720><c> it's</c><00:01:51.840><c> actually</c><00:01:52.170><c> really</c>

00:01:52.940 --> 00:01:52.950 align:start position:0%
that's it and it's actually really
 

00:01:52.950 --> 00:01:55.550 align:start position:0%
that's it and it's actually really
simple<00:01:53.340><c> and</c><00:01:53.840><c> I'll</c><00:01:54.840><c> show</c><00:01:55.049><c> and</c><00:01:55.259><c> I'll</c><00:01:55.350><c> show</c><00:01:55.530><c> you</c>

00:01:55.550 --> 00:01:55.560 align:start position:0%
simple and I'll show and I'll show you
 

00:01:55.560 --> 00:01:59.209 align:start position:0%
simple and I'll show and I'll show you
next<00:01:55.890><c> how</c><00:01:56.219><c> simple</c><00:01:56.790><c> it</c><00:01:56.880><c> is</c><00:01:57.000><c> but</c><00:01:57.930><c> and</c><00:01:58.560><c> then</c><00:01:59.189><c> the</c>

00:01:59.209 --> 00:01:59.219 align:start position:0%
next how simple it is but and then the
 

00:01:59.219 --> 00:02:01.010 align:start position:0%
next how simple it is but and then the
other<00:01:59.399><c> one</c><00:01:59.579><c> is</c><00:01:59.790><c> we're</c><00:02:00.509><c> actually</c><00:02:00.570><c> gonna</c><00:02:00.810><c> make</c>

00:02:01.010 --> 00:02:01.020 align:start position:0%
other one is we're actually gonna make
 

00:02:01.020 --> 00:02:04.399 align:start position:0%
other one is we're actually gonna make
an<00:02:01.229><c> awesome</c><00:02:01.710><c> ization</c><00:02:02.659><c> to</c><00:02:03.659><c> where</c><00:02:03.840><c> instead</c><00:02:04.350><c> of</c>

00:02:04.399 --> 00:02:04.409 align:start position:0%
an awesome ization to where instead of
 

00:02:04.409 --> 00:02:07.039 align:start position:0%
an awesome ization to where instead of
referencing<00:02:04.890><c> the</c><00:02:05.729><c> head</c><00:02:05.939><c> in</c><00:02:06.210><c> the</c><00:02:06.630><c> tail</c><00:02:06.840><c> of</c>

00:02:07.039 --> 00:02:07.049 align:start position:0%
referencing the head in the tail of
 

00:02:07.049 --> 00:02:09.410 align:start position:0%
referencing the head in the tail of
nodes<00:02:07.380><c> we</c><00:02:07.530><c> only</c><00:02:07.799><c> need</c><00:02:08.009><c> to</c><00:02:08.129><c> know</c><00:02:08.220><c> we'll</c><00:02:09.149><c> need</c><00:02:09.330><c> to</c>

00:02:09.410 --> 00:02:09.420 align:start position:0%
nodes we only need to know we'll need to
 

00:02:09.420 --> 00:02:10.990 align:start position:0%
nodes we only need to know we'll need to
know<00:02:09.479><c> about</c><00:02:09.660><c> the</c><00:02:09.869><c> tail</c><00:02:10.080><c> node</c>

00:02:10.990 --> 00:02:11.000 align:start position:0%
know about the tail node
 

00:02:11.000 --> 00:02:13.900 align:start position:0%
know about the tail node
as<00:02:11.150><c> the</c><00:02:11.360><c> head</c><00:02:11.600><c> is</c><00:02:11.870><c> always</c><00:02:12.560><c> the</c><00:02:13.250><c> tails</c><00:02:13.490><c> next</c>

00:02:13.900 --> 00:02:13.910 align:start position:0%
as the head is always the tails next
 

00:02:13.910 --> 00:02:16.300 align:start position:0%
as the head is always the tails next
reference<00:02:14.060><c> always</c><00:02:14.990><c> in</c><00:02:15.200><c> a</c><00:02:15.380><c> in</c><00:02:15.680><c> a</c><00:02:16.040><c> circular</c>

00:02:16.300 --> 00:02:16.310 align:start position:0%
reference always in a in a circular
 

00:02:16.310 --> 00:02:20.380 align:start position:0%
reference always in a in a circular
linked<00:02:16.700><c> list</c><00:02:16.970><c> it's</c><00:02:17.570><c> always</c><00:02:18.820><c> the</c><00:02:19.820><c> tails</c><00:02:20.060><c> next</c>

00:02:20.380 --> 00:02:20.390 align:start position:0%
linked list it's always the tails next
 

00:02:20.390 --> 00:02:21.880 align:start position:0%
linked list it's always the tails next
reference<00:02:20.690><c> so</c><00:02:20.810><c> we</c><00:02:20.840><c> don't</c><00:02:21.140><c> actually</c><00:02:21.410><c> need</c><00:02:21.830><c> to</c>

00:02:21.880 --> 00:02:21.890 align:start position:0%
reference so we don't actually need to
 

00:02:21.890 --> 00:02:24.310 align:start position:0%
reference so we don't actually need to
know<00:02:22.300><c> we</c><00:02:23.300><c> don't</c><00:02:23.480><c> even</c><00:02:23.570><c> have</c><00:02:23.660><c> a</c><00:02:23.750><c> specific</c>

00:02:24.310 --> 00:02:24.320 align:start position:0%
know we don't even have a specific
 

00:02:24.320 --> 00:02:25.840 align:start position:0%
know we don't even have a specific
reference<00:02:24.590><c> point</c><00:02:24.980><c> for</c><00:02:25.220><c> the</c><00:02:25.310><c> head</c><00:02:25.460><c> because</c><00:02:25.760><c> we</c>

00:02:25.840 --> 00:02:25.850 align:start position:0%
reference point for the head because we
 

00:02:25.850 --> 00:02:27.220 align:start position:0%
reference point for the head because we
always<00:02:26.030><c> know</c><00:02:26.240><c> where</c><00:02:26.390><c> it's</c><00:02:26.510><c> at</c><00:02:26.630><c> and</c><00:02:26.780><c> this</c>

00:02:27.220 --> 00:02:27.230 align:start position:0%
always know where it's at and this
 

00:02:27.230 --> 00:02:31.930 align:start position:0%
always know where it's at and this
actually<00:02:27.530><c> makes</c><00:02:27.710><c> the</c><00:02:27.890><c> code</c><00:02:28.070><c> simpler</c><00:02:29.320><c> and</c><00:02:30.940><c> even</c>

00:02:31.930 --> 00:02:31.940 align:start position:0%
actually makes the code simpler and even
 

00:02:31.940 --> 00:02:35.200 align:start position:0%
actually makes the code simpler and even
if<00:02:32.060><c> you</c><00:02:32.360><c> don't</c><00:02:32.620><c> even</c><00:02:33.620><c> if</c><00:02:33.890><c> you</c><00:02:34.100><c> don't</c><00:02:35.030><c> care</c>

00:02:35.200 --> 00:02:35.210 align:start position:0%
if you don't even if you don't care
 

00:02:35.210 --> 00:02:37.000 align:start position:0%
if you don't even if you don't care
about<00:02:35.270><c> their</c><00:02:35.510><c> new</c><00:02:35.660><c> rotate</c><00:02:36.020><c> method</c><00:02:36.410><c> so</c><00:02:36.980><c> you</c>

00:02:37.000 --> 00:02:37.010 align:start position:0%
about their new rotate method so you
 

00:02:37.010 --> 00:02:39.400 align:start position:0%
about their new rotate method so you
just<00:02:37.190><c> want</c><00:02:37.370><c> to</c><00:02:37.430><c> use</c><00:02:37.580><c> a</c><00:02:37.820><c> circular</c><00:02:38.660><c> link</c><00:02:39.140><c> list</c>

00:02:39.400 --> 00:02:39.410 align:start position:0%
just want to use a circular link list
 

00:02:39.410 --> 00:02:43.360 align:start position:0%
just want to use a circular link list
it's<00:02:39.890><c> actually</c><00:02:40.250><c> more</c><00:02:40.880><c> optimized</c><00:02:41.680><c> to</c><00:02:42.680><c> use</c><00:02:42.920><c> than</c>

00:02:43.360 --> 00:02:43.370 align:start position:0%
it's actually more optimized to use than
 

00:02:43.370 --> 00:02:45.699 align:start position:0%
it's actually more optimized to use than
just<00:02:43.910><c> a</c><00:02:43.970><c> typical</c><00:02:44.180><c> singular</c><00:02:44.870><c> or</c><00:02:45.080><c> singly</c><00:02:45.440><c> linked</c>

00:02:45.699 --> 00:02:45.709 align:start position:0%
just a typical singular or singly linked
 

00:02:45.709 --> 00:02:50.170 align:start position:0%
just a typical singular or singly linked
list<00:02:46.360><c> alright</c><00:02:47.360><c> so</c><00:02:47.660><c> we're</c><00:02:48.080><c> gonna</c><00:02:48.140><c> look</c><00:02:48.470><c> at</c><00:02:49.180><c> the</c>

00:02:50.170 --> 00:02:50.180 align:start position:0%
list alright so we're gonna look at the
 

00:02:50.180 --> 00:02:52.480 align:start position:0%
list alright so we're gonna look at the
rotate<00:02:50.540><c> message</c><00:02:50.840><c> real</c><00:02:51.020><c> quick</c><00:02:51.250><c> like</c><00:02:52.250><c> I</c><00:02:52.340><c> said</c>

00:02:52.480 --> 00:02:52.490 align:start position:0%
rotate message real quick like I said
 

00:02:52.490 --> 00:02:54.490 align:start position:0%
rotate message real quick like I said
this<00:02:52.580><c> is</c><00:02:52.700><c> very</c><00:02:52.910><c> simple</c><00:02:53.209><c> so</c><00:02:53.630><c> here</c><00:02:53.660><c> we</c><00:02:54.050><c> have</c><00:02:54.200><c> four</c>

00:02:54.490 --> 00:02:54.500 align:start position:0%
this is very simple so here we have four
 

00:02:54.500 --> 00:02:56.949 align:start position:0%
this is very simple so here we have four
nodes<00:02:54.650><c> we</c><00:02:55.459><c> know</c><00:02:56.030><c> where</c><00:02:56.209><c> the</c><00:02:56.330><c> we</c><00:02:56.690><c> have</c><00:02:56.930><c> a</c>

00:02:56.949 --> 00:02:56.959 align:start position:0%
nodes we know where the we have a
 

00:02:56.959 --> 00:02:58.480 align:start position:0%
nodes we know where the we have a
reference<00:02:57.110><c> for</c><00:02:57.290><c> the</c><00:02:57.440><c> tail</c><00:02:57.590><c> mode</c><00:02:57.830><c> and</c><00:02:58.100><c> I</c><00:02:58.310><c> just</c>

00:02:58.480 --> 00:02:58.490 align:start position:0%
reference for the tail mode and I just
 

00:02:58.490 --> 00:03:01.510 align:start position:0%
reference for the tail mode and I just
kind<00:02:58.670><c> of</c><00:02:58.700><c> have</c><00:02:58.820><c> the</c><00:02:58.910><c> head</c><00:02:59.060><c> there</c><00:02:59.300><c> just</c><00:03:00.520><c> just</c>

00:03:01.510 --> 00:03:01.520 align:start position:0%
kind of have the head there just just
 

00:03:01.520 --> 00:03:04.870 align:start position:0%
kind of have the head there just just
for<00:03:02.030><c> showing</c><00:03:02.660><c> you</c><00:03:02.780><c> the</c><00:03:02.930><c> purpose</c><00:03:03.320><c> of</c><00:03:03.700><c> where</c><00:03:04.700><c> it</c>

00:03:04.870 --> 00:03:04.880 align:start position:0%
for showing you the purpose of where it
 

00:03:04.880 --> 00:03:06.430 align:start position:0%
for showing you the purpose of where it
should<00:03:05.150><c> be</c><00:03:05.180><c> but</c><00:03:05.360><c> we're</c><00:03:05.870><c> not</c><00:03:06.020><c> actually</c><00:03:06.320><c> going</c>

00:03:06.430 --> 00:03:06.440 align:start position:0%
should be but we're not actually going
 

00:03:06.440 --> 00:03:09.430 align:start position:0%
should be but we're not actually going
to<00:03:06.500><c> explicitly</c><00:03:07.690><c> worry</c><00:03:08.690><c> about</c><00:03:08.959><c> it</c><00:03:09.080><c> in</c><00:03:09.170><c> the</c><00:03:09.230><c> code</c>

00:03:09.430 --> 00:03:09.440 align:start position:0%
to explicitly worry about it in the code
 

00:03:09.440 --> 00:03:14.830 align:start position:0%
to explicitly worry about it in the code
so<00:03:12.250><c> whenever</c><00:03:13.250><c> we</c><00:03:13.550><c> want</c><00:03:13.700><c> to</c><00:03:13.760><c> rotate</c><00:03:13.910><c> meaning</c><00:03:14.480><c> we</c>

00:03:14.830 --> 00:03:14.840 align:start position:0%
so whenever we want to rotate meaning we
 

00:03:14.840 --> 00:03:19.630 align:start position:0%
so whenever we want to rotate meaning we
were<00:03:15.050><c> gonna</c><00:03:15.200><c> bring</c><00:03:17.320><c> the</c><00:03:18.320><c> element</c><00:03:18.739><c> that</c><00:03:19.310><c> has</c><00:03:19.610><c> a</c>

00:03:19.630 --> 00:03:19.640 align:start position:0%
were gonna bring the element that has a
 

00:03:19.640 --> 00:03:21.850 align:start position:0%
were gonna bring the element that has a
value<00:03:19.910><c> of</c><00:03:20.000><c> five</c><00:03:20.269><c> that's</c><00:03:20.930><c> gonna</c><00:03:21.320><c> be</c><00:03:21.590><c> the</c><00:03:21.739><c> new</c>

00:03:21.850 --> 00:03:21.860 align:start position:0%
value of five that's gonna be the new
 

00:03:21.860 --> 00:03:23.170 align:start position:0%
value of five that's gonna be the new
tail<00:03:22.190><c> because</c><00:03:22.430><c> we're</c><00:03:22.550><c> taking</c><00:03:22.640><c> it</c><00:03:22.820><c> to</c><00:03:22.880><c> the</c><00:03:23.030><c> end</c>

00:03:23.170 --> 00:03:23.180 align:start position:0%
tail because we're taking it to the end
 

00:03:23.180 --> 00:03:26.920 align:start position:0%
tail because we're taking it to the end
we're<00:03:23.750><c> bringing</c><00:03:24.050><c> it</c><00:03:24.140><c> to</c><00:03:24.290><c> the</c><00:03:24.380><c> end</c><00:03:25.360><c> all</c><00:03:26.360><c> we</c><00:03:26.810><c> have</c>

00:03:26.920 --> 00:03:26.930 align:start position:0%
we're bringing it to the end all we have
 

00:03:26.930 --> 00:03:30.430 align:start position:0%
we're bringing it to the end all we have
to<00:03:27.050><c> do</c><00:03:27.290><c> is</c><00:03:27.920><c> say</c><00:03:28.220><c> tail</c><00:03:28.610><c> equals</c><00:03:29.360><c> tail</c><00:03:29.600><c> get</c><00:03:30.050><c> next</c>

00:03:30.430 --> 00:03:30.440 align:start position:0%
to do is say tail equals tail get next
 

00:03:30.440 --> 00:03:32.290 align:start position:0%
to do is say tail equals tail get next
so<00:03:31.220><c> basically</c><00:03:31.459><c> were</c><00:03:31.580><c> saying</c><00:03:31.820><c> tail</c><00:03:31.970><c> equals</c>

00:03:32.290 --> 00:03:32.300 align:start position:0%
so basically were saying tail equals
 

00:03:32.300 --> 00:03:36.040 align:start position:0%
so basically were saying tail equals
head<00:03:32.480><c> alright</c><00:03:34.239><c> and</c><00:03:35.239><c> yeah</c><00:03:35.720><c> that's</c><00:03:35.989><c> basically</c>

00:03:36.040 --> 00:03:36.050 align:start position:0%
head alright and yeah that's basically
 

00:03:36.050 --> 00:03:37.770 align:start position:0%
head alright and yeah that's basically
were<00:03:36.320><c> saying</c><00:03:36.530><c> just</c><00:03:36.560><c> tail</c><00:03:36.769><c> equals</c><00:03:37.070><c> head</c><00:03:37.190><c> and</c>

00:03:37.770 --> 00:03:37.780 align:start position:0%
were saying just tail equals head and
 

00:03:37.780 --> 00:03:40.060 align:start position:0%
were saying just tail equals head and
that's<00:03:38.780><c> all</c><00:03:38.959><c> we</c><00:03:39.019><c> need</c><00:03:39.140><c> to</c><00:03:39.170><c> do</c><00:03:39.350><c> we</c><00:03:39.680><c> don't</c><00:03:39.860><c> have</c>

00:03:40.060 --> 00:03:40.070 align:start position:0%
that's all we need to do we don't have
 

00:03:40.070 --> 00:03:41.500 align:start position:0%
that's all we need to do we don't have
to<00:03:40.160><c> do</c><00:03:40.280><c> anything</c><00:03:40.700><c> with</c><00:03:40.970><c> the</c><00:03:41.060><c> head</c><00:03:41.180><c> reference</c>

00:03:41.500 --> 00:03:41.510 align:start position:0%
to do anything with the head reference
 

00:03:41.510 --> 00:03:43.060 align:start position:0%
to do anything with the head reference
because<00:03:41.660><c> we're</c><00:03:41.900><c> not</c><00:03:42.019><c> we</c><00:03:42.230><c> don't</c><00:03:42.410><c> care</c><00:03:42.680><c> about</c><00:03:42.709><c> it</c>

00:03:43.060 --> 00:03:43.070 align:start position:0%
because we're not we don't care about it
 

00:03:43.070 --> 00:03:46.960 align:start position:0%
because we're not we don't care about it
we<00:03:43.900><c> implicitly</c><00:03:44.900><c> know</c><00:03:45.260><c> where</c><00:03:45.709><c> it's</c><00:03:45.890><c> at</c><00:03:46.040><c> without</c>

00:03:46.960 --> 00:03:46.970 align:start position:0%
we implicitly know where it's at without
 

00:03:46.970 --> 00:03:49.240 align:start position:0%
we implicitly know where it's at without
actually<00:03:47.420><c> defining</c><00:03:47.840><c> it</c><00:03:48.110><c> in</c><00:03:48.230><c> the</c><00:03:48.650><c> code</c><00:03:48.860><c> okay</c>

00:03:49.240 --> 00:03:49.250 align:start position:0%
actually defining it in the code okay
 

00:03:49.250 --> 00:03:52.150 align:start position:0%
actually defining it in the code okay
and<00:03:49.700><c> that</c><00:03:50.090><c> makes</c><00:03:50.269><c> this</c><00:03:50.390><c> rotate</c><00:03:50.810><c> so</c><00:03:51.500><c> simple</c><00:03:51.950><c> and</c>

00:03:52.150 --> 00:03:52.160 align:start position:0%
and that makes this rotate so simple and
 

00:03:52.160 --> 00:03:55.900 align:start position:0%
and that makes this rotate so simple and
for<00:03:52.940><c> the</c><00:03:53.269><c> next</c><00:03:53.680><c> and</c><00:03:54.680><c> for</c><00:03:54.950><c> the</c><00:03:55.040><c> next</c><00:03:55.190><c> operation</c>

00:03:55.900 --> 00:03:55.910 align:start position:0%
for the next and for the next operation
 

00:03:55.910 --> 00:03:57.820 align:start position:0%
for the next and for the next operation
we're<00:03:56.209><c> talking</c><00:03:56.420><c> about</c><00:03:56.480><c> is</c><00:03:56.720><c> adding</c><00:03:57.170><c> when</c><00:03:57.709><c> we</c>

00:03:57.820 --> 00:03:57.830 align:start position:0%
we're talking about is adding when we
 

00:03:57.830 --> 00:04:00.400 align:start position:0%
we're talking about is adding when we
want<00:03:57.980><c> to</c><00:03:58.040><c> add</c><00:03:58.160><c> a</c><00:03:58.519><c> node</c><00:03:58.989><c> into</c><00:03:59.989><c> the</c><00:04:00.080><c> beginning</c><00:04:00.350><c> of</c>

00:04:00.400 --> 00:04:00.410 align:start position:0%
want to add a node into the beginning of
 

00:04:00.410 --> 00:04:05.229 align:start position:0%
want to add a node into the beginning of
the<00:04:00.470><c> list</c><00:04:01.360><c> so</c><00:04:02.360><c> how</c><00:04:02.510><c> this</c><00:04:02.660><c> is</c><00:04:02.750><c> done</c><00:04:02.959><c> is</c><00:04:04.000><c> we</c><00:04:05.000><c> take</c>

00:04:05.229 --> 00:04:05.239 align:start position:0%
the list so how this is done is we take
 

00:04:05.239 --> 00:04:07.720 align:start position:0%
the list so how this is done is we take
a<00:04:05.480><c> new</c><00:04:05.720><c> node</c><00:04:06.070><c> we're</c><00:04:07.070><c> going</c><00:04:07.190><c> to</c><00:04:07.250><c> pass</c><00:04:07.370><c> in</c><00:04:07.519><c> some</c>

00:04:07.720 --> 00:04:07.730 align:start position:0%
a new node we're going to pass in some
 

00:04:07.730 --> 00:04:10.180 align:start position:0%
a new node we're going to pass in some
value<00:04:08.000><c> we</c><00:04:08.150><c> want</c><00:04:08.360><c> it</c><00:04:08.420><c> to</c><00:04:08.510><c> hold</c><00:04:08.600><c> and</c><00:04:08.930><c> we</c><00:04:09.830><c> just</c><00:04:10.040><c> set</c>

00:04:10.180 --> 00:04:10.190 align:start position:0%
value we want it to hold and we just set
 

00:04:10.190 --> 00:04:14.650 align:start position:0%
value we want it to hold and we just set
its<00:04:10.340><c> next</c><00:04:10.820><c> reference</c><00:04:11.269><c> to</c><00:04:12.260><c> the</c><00:04:13.220><c> head</c><00:04:13.489><c> or</c><00:04:13.820><c> you</c>

00:04:14.650 --> 00:04:14.660 align:start position:0%
its next reference to the head or you
 

00:04:14.660 --> 00:04:16.509 align:start position:0%
its next reference to the head or you
say<00:04:14.840><c> we're</c><00:04:15.049><c> going</c><00:04:15.140><c> to</c><00:04:15.200><c> say</c><00:04:15.350><c> tail</c><00:04:15.620><c> get</c><00:04:16.070><c> next</c>

00:04:16.509 --> 00:04:16.519 align:start position:0%
say we're going to say tail get next
 

00:04:16.519 --> 00:04:18.610 align:start position:0%
say we're going to say tail get next
alright<00:04:17.390><c> because</c><00:04:17.600><c> we</c><00:04:17.780><c> know</c><00:04:17.900><c> the</c><00:04:18.019><c> tail</c><00:04:18.200><c> is</c><00:04:18.320><c> next</c>

00:04:18.610 --> 00:04:18.620 align:start position:0%
alright because we know the tail is next
 

00:04:18.620 --> 00:04:19.660 align:start position:0%
alright because we know the tail is next
right

00:04:19.660 --> 00:04:19.670 align:start position:0%
right
 

00:04:19.670 --> 00:04:25.240 align:start position:0%
right
node<00:04:20.180><c> is</c><00:04:20.480><c> the</c><00:04:20.750><c> head</c><00:04:21.130><c> implicitly</c><00:04:22.630><c> so</c><00:04:24.070><c> once</c><00:04:25.070><c> we</c>

00:04:25.240 --> 00:04:25.250 align:start position:0%
node is the head implicitly so once we
 

00:04:25.250 --> 00:04:27.760 align:start position:0%
node is the head implicitly so once we
do<00:04:25.400><c> that</c><00:04:25.430><c> then</c><00:04:25.940><c> we</c><00:04:26.000><c> just</c><00:04:26.150><c> said</c><00:04:26.300><c> hey</c><00:04:26.630><c> now</c><00:04:27.290><c> the</c>

00:04:27.760 --> 00:04:27.770 align:start position:0%
do that then we just said hey now the
 

00:04:27.770 --> 00:04:30.010 align:start position:0%
do that then we just said hey now the
tails<00:04:28.100><c> next</c><00:04:29.060><c> reference</c><00:04:29.210><c> instead</c><00:04:29.750><c> of</c><00:04:29.840><c> being</c>

00:04:30.010 --> 00:04:30.020 align:start position:0%
tails next reference instead of being
 

00:04:30.020 --> 00:04:31.570 align:start position:0%
tails next reference instead of being
the<00:04:30.110><c> head</c><00:04:30.320><c> we</c><00:04:30.650><c> want</c><00:04:30.800><c> to</c><00:04:30.950><c> said</c><00:04:31.130><c> to</c><00:04:31.250><c> the</c><00:04:31.310><c> newest</c>

00:04:31.570 --> 00:04:31.580 align:start position:0%
the head we want to said to the newest
 

00:04:31.580 --> 00:04:36.460 align:start position:0%
the head we want to said to the newest
node<00:04:31.760><c> and</c><00:04:32.420><c> then</c><00:04:32.570><c> we</c><00:04:33.100><c> increment</c><00:04:34.100><c> the</c><00:04:34.460><c> size</c><00:04:35.470><c> the</c>

00:04:36.460 --> 00:04:36.470 align:start position:0%
node and then we increment the size the
 

00:04:36.470 --> 00:04:39.220 align:start position:0%
node and then we increment the size the
size<00:04:36.650><c> field</c><00:04:36.920><c> okay</c><00:04:37.460><c> so</c><00:04:38.030><c> real</c><00:04:38.210><c> quick</c><00:04:38.360><c> again</c><00:04:38.630><c> we</c>

00:04:39.220 --> 00:04:39.230 align:start position:0%
size field okay so real quick again we
 

00:04:39.230 --> 00:04:40.450 align:start position:0%
size field okay so real quick again we
just<00:04:39.290><c> we</c><00:04:39.590><c> had</c><00:04:39.740><c> this</c><00:04:39.830><c> newest</c><00:04:40.100><c> note</c><00:04:40.250><c> with</c><00:04:40.430><c> his</c>

00:04:40.450 --> 00:04:40.460 align:start position:0%
just we had this newest note with his
 

00:04:40.460 --> 00:04:44.740 align:start position:0%
just we had this newest note with his
new<00:04:40.700><c> node</c><00:04:40.910><c> we</c><00:04:41.660><c> are</c><00:04:43.180><c> or</c><00:04:44.180><c> created</c><00:04:44.480><c> where</c><00:04:44.630><c> we're</c>

00:04:44.740 --> 00:04:44.750 align:start position:0%
new node we are or created where we're
 

00:04:44.750 --> 00:04:47.950 align:start position:0%
new node we are or created where we're
creating<00:04:45.020><c> a</c><00:04:45.080><c> new</c><00:04:45.110><c> node</c><00:04:45.410><c> and</c><00:04:46.450><c> setting</c><00:04:47.450><c> its</c><00:04:47.570><c> next</c>

00:04:47.950 --> 00:04:47.960 align:start position:0%
creating a new node and setting its next
 

00:04:47.960 --> 00:04:50.500 align:start position:0%
creating a new node and setting its next
reference<00:04:48.110><c> to</c><00:04:48.680><c> the</c><00:04:48.830><c> tails</c><00:04:49.180><c> next</c><00:04:50.180><c> reference</c>

00:04:50.500 --> 00:04:50.510 align:start position:0%
reference to the tails next reference
 

00:04:50.510 --> 00:04:53.170 align:start position:0%
reference to the tails next reference
which<00:04:50.780><c> was</c><00:04:50.990><c> the</c><00:04:51.140><c> head</c><00:04:51.350><c> and</c><00:04:51.820><c> then</c><00:04:52.820><c> we're</c><00:04:52.970><c> saying</c>

00:04:53.170 --> 00:04:53.180 align:start position:0%
which was the head and then we're saying
 

00:04:53.180 --> 00:04:54.970 align:start position:0%
which was the head and then we're saying
now<00:04:53.720><c> the</c><00:04:53.780><c> tails</c><00:04:54.140><c> next</c><00:04:54.470><c> reference</c><00:04:54.590><c> should</c>

00:04:54.970 --> 00:04:54.980 align:start position:0%
now the tails next reference should
 

00:04:54.980 --> 00:04:57.370 align:start position:0%
now the tails next reference should
equal<00:04:55.190><c> to</c><00:04:55.310><c> this</c><00:04:55.400><c> new</c><00:04:55.700><c> node</c><00:04:55.910><c> that's</c><00:04:56.750><c> it</c><00:04:56.990><c> okay</c>

00:04:57.370 --> 00:04:57.380 align:start position:0%
equal to this new node that's it okay
 

00:04:57.380 --> 00:04:58.780 align:start position:0%
equal to this new node that's it okay
I'll<00:04:57.860><c> explain</c><00:04:58.040><c> a</c><00:04:58.160><c> little</c><00:04:58.220><c> bit</c><00:04:58.370><c> more</c><00:04:58.430><c> when</c><00:04:58.700><c> we</c>

00:04:58.780 --> 00:04:58.790 align:start position:0%
I'll explain a little bit more when we
 

00:04:58.790 --> 00:05:03.160 align:start position:0%
I'll explain a little bit more when we
code<00:04:59.000><c> this</c><00:05:00.100><c> but</c><00:05:01.100><c> go</c><00:05:02.060><c> in</c><00:05:02.240><c> real</c><00:05:02.390><c> quick</c><00:05:02.540><c> you</c><00:05:03.050><c> might</c>

00:05:03.160 --> 00:05:03.170 align:start position:0%
code this but go in real quick you might
 

00:05:03.170 --> 00:05:04.960 align:start position:0%
code this but go in real quick you might
want<00:05:03.410><c> I'm</c><00:05:03.560><c> not</c><00:05:03.620><c> gonna</c><00:05:03.980><c> go</c><00:05:04.100><c> over</c><00:05:04.130><c> adding</c><00:05:04.760><c> to</c><00:05:04.850><c> the</c>

00:05:04.960 --> 00:05:04.970 align:start position:0%
want I'm not gonna go over adding to the
 

00:05:04.970 --> 00:05:07.720 align:start position:0%
want I'm not gonna go over adding to the
last<00:05:05.380><c> because</c><00:05:06.380><c> it's</c><00:05:06.890><c> actually</c><00:05:07.040><c> very</c><00:05:07.400><c> simple</c>

00:05:07.720 --> 00:05:07.730 align:start position:0%
last because it's actually very simple
 

00:05:07.730 --> 00:05:09.580 align:start position:0%
last because it's actually very simple
what<00:05:07.970><c> we</c><00:05:08.150><c> we've</c><00:05:08.750><c> actually</c><00:05:08.960><c> done</c><00:05:09.260><c> the</c><00:05:09.380><c> hard</c>

00:05:09.580 --> 00:05:09.590 align:start position:0%
what we we've actually done the hard
 

00:05:09.590 --> 00:05:12.970 align:start position:0%
what we we've actually done the hard
part<00:05:09.820><c> right</c><00:05:10.820><c> now</c><00:05:10.880><c> we</c><00:05:11.630><c> want</c><00:05:11.810><c> to</c><00:05:11.870><c> add</c><00:05:11.990><c> last</c><00:05:12.320><c> all</c>

00:05:12.970 --> 00:05:12.980 align:start position:0%
part right now we want to add last all
 

00:05:12.980 --> 00:05:15.700 align:start position:0%
part right now we want to add last all
were<00:05:13.310><c> actually</c><00:05:13.550><c> gonna</c><00:05:13.670><c> do</c><00:05:13.880><c> is</c><00:05:14.090><c> make</c><00:05:14.270><c> a</c><00:05:14.330><c> call</c><00:05:14.750><c> to</c>

00:05:15.700 --> 00:05:15.710 align:start position:0%
were actually gonna do is make a call to
 

00:05:15.710 --> 00:05:20.470 align:start position:0%
were actually gonna do is make a call to
this<00:05:15.950><c> algorithm</c><00:05:16.340><c> and</c><00:05:16.930><c> then</c><00:05:17.930><c> we're</c><00:05:18.110><c> gonna</c><00:05:19.480><c> I'm</c>

00:05:20.470 --> 00:05:20.480 align:start position:0%
this algorithm and then we're gonna I'm
 

00:05:20.480 --> 00:05:22.690 align:start position:0%
this algorithm and then we're gonna I'm
gonna<00:05:20.660><c> say</c><00:05:20.840><c> tail</c><00:05:21.140><c> then</c><00:05:22.010><c> we're</c><00:05:22.130><c> just</c><00:05:22.160><c> gonna</c><00:05:22.400><c> set</c>

00:05:22.690 --> 00:05:22.700 align:start position:0%
gonna say tail then we're just gonna set
 

00:05:22.700 --> 00:05:28.650 align:start position:0%
gonna say tail then we're just gonna set
the<00:05:22.940><c> tail</c><00:05:23.510><c> equals</c><00:05:25.660><c> like</c><00:05:26.660><c> tail</c><00:05:26.870><c> get</c><00:05:27.260><c> next</c><00:05:27.590><c> ok</c><00:05:28.070><c> so</c>

00:05:28.650 --> 00:05:28.660 align:start position:0%
the tail equals like tail get next ok so
 

00:05:28.660 --> 00:05:30.850 align:start position:0%
the tail equals like tail get next ok so
last<00:05:29.660><c> thing</c><00:05:29.840><c> I</c><00:05:29.960><c> wanna</c><00:05:30.080><c> talk</c><00:05:30.170><c> about</c><00:05:30.230><c> is</c><00:05:30.530><c> the</c><00:05:30.710><c> Big</c>

00:05:30.850 --> 00:05:30.860 align:start position:0%
last thing I wanna talk about is the Big
 

00:05:30.860 --> 00:05:33.040 align:start position:0%
last thing I wanna talk about is the Big
O<00:05:30.950><c> notation</c><00:05:31.160><c> alright</c><00:05:32.000><c> so</c><00:05:32.330><c> again</c><00:05:32.480><c> real</c><00:05:32.840><c> quick</c>

00:05:33.040 --> 00:05:33.050 align:start position:0%
O notation alright so again real quick
 

00:05:33.050 --> 00:05:37.690 align:start position:0%
O notation alright so again real quick
Big<00:05:33.260><c> O</c><00:05:33.320><c> notation</c><00:05:33.500><c> is</c><00:05:34.270><c> what</c><00:05:35.270><c> is</c><00:05:35.360><c> the</c><00:05:35.600><c> time</c><00:05:36.700><c> what</c>

00:05:37.690 --> 00:05:37.700 align:start position:0%
Big O notation is what is the time what
 

00:05:37.700 --> 00:05:39.870 align:start position:0%
Big O notation is what is the time what
is<00:05:37.820><c> the</c><00:05:38.030><c> time</c><00:05:38.060><c> to</c><00:05:38.330><c> perform</c><00:05:38.600><c> the</c><00:05:38.750><c> algorithm</c><00:05:39.230><c> as</c>

00:05:39.870 --> 00:05:39.880 align:start position:0%
is the time to perform the algorithm as
 

00:05:39.880 --> 00:05:44.230 align:start position:0%
is the time to perform the algorithm as
besides<00:05:40.880><c> the</c><00:05:41.000><c> input</c><00:05:41.270><c> increases</c><00:05:42.220><c> so</c><00:05:43.220><c> I'd</c><00:05:43.850><c> only</c>

00:05:44.230 --> 00:05:44.240 align:start position:0%
besides the input increases so I'd only
 

00:05:44.240 --> 00:05:46.420 align:start position:0%
besides the input increases so I'd only
talk<00:05:44.450><c> about</c><00:05:44.480><c> the</c><00:05:44.660><c> best</c><00:05:44.840><c> case</c><00:05:45.220><c> because</c><00:05:46.220><c> the</c>

00:05:46.420 --> 00:05:46.430 align:start position:0%
talk about the best case because the
 

00:05:46.430 --> 00:05:47.890 align:start position:0%
talk about the best case because the
hardly<00:05:46.670><c> ever</c><00:05:46.730><c> happens</c><00:05:46.910><c> anyway</c><00:05:47.270><c> you</c><00:05:47.330><c> so</c><00:05:47.510><c> don't</c>

00:05:47.890 --> 00:05:47.900 align:start position:0%
hardly ever happens anyway you so don't
 

00:05:47.900 --> 00:05:49.450 align:start position:0%
hardly ever happens anyway you so don't
care<00:05:48.080><c> about</c><00:05:48.200><c> that</c><00:05:48.440><c> so</c><00:05:49.190><c> we're</c><00:05:49.280><c> gonna</c><00:05:49.370><c> talk</c>

00:05:49.450 --> 00:05:49.460 align:start position:0%
care about that so we're gonna talk
 

00:05:49.460 --> 00:05:51.370 align:start position:0%
care about that so we're gonna talk
about<00:05:49.550><c> average</c><00:05:49.790><c> average</c><00:05:50.720><c> and</c><00:05:50.900><c> worst</c><00:05:51.020><c> case</c>

00:05:51.370 --> 00:05:51.380 align:start position:0%
about average average and worst case
 

00:05:51.380 --> 00:05:52.420 align:start position:0%
about average average and worst case
time<00:05:51.560><c> complexity</c><00:05:51.920><c> and</c><00:05:52.130><c> then</c><00:05:52.220><c> space</c>

00:05:52.420 --> 00:05:52.430 align:start position:0%
time complexity and then space
 

00:05:52.430 --> 00:05:55.000 align:start position:0%
time complexity and then space
complexity<00:05:52.970><c> so</c><00:05:53.630><c> on</c><00:05:53.810><c> average</c><00:05:54.200><c> that's</c><00:05:54.860><c> what</c>

00:05:55.000 --> 00:05:55.010 align:start position:0%
complexity so on average that's what
 

00:05:55.010 --> 00:05:57.040 align:start position:0%
complexity so on average that's what
about<00:05:55.130><c> to</c><00:05:55.280><c> access</c><00:05:55.640><c> and</c><00:05:55.790><c> searching</c><00:05:56.090><c> first</c><00:05:56.330><c> so</c>

00:05:57.040 --> 00:05:57.050 align:start position:0%
about to access and searching first so
 

00:05:57.050 --> 00:05:59.680 align:start position:0%
about to access and searching first so
accessing<00:05:57.620><c> is</c><00:05:57.710><c> the</c><00:05:57.770><c> same</c><00:05:57.950><c> as</c><00:05:58.070><c> indexing</c><00:05:58.690><c> you</c>

00:05:59.680 --> 00:05:59.690 align:start position:0%
accessing is the same as indexing you
 

00:05:59.690 --> 00:06:01.230 align:start position:0%
accessing is the same as indexing you
might<00:05:59.840><c> you</c><00:06:00.050><c> might</c><00:06:00.200><c> see</c><00:06:00.500><c> it</c><00:06:00.620><c> interchangeably</c>

00:06:01.230 --> 00:06:01.240 align:start position:0%
might you might see it interchangeably
 

00:06:01.240 --> 00:06:05.830 align:start position:0%
might you might see it interchangeably
called<00:06:02.240><c> accessing</c><00:06:02.720><c> and</c><00:06:03.560><c> met</c><00:06:03.800><c> and</c><00:06:04.660><c> linked</c><00:06:05.660><c> list</c>

00:06:05.830 --> 00:06:05.840 align:start position:0%
called accessing and met and linked list
 

00:06:05.840 --> 00:06:11.290 align:start position:0%
called accessing and met and linked list
don't<00:06:06.170><c> index</c><00:06:08.110><c> arrays</c><00:06:09.110><c> do</c><00:06:09.380><c> ok</c><00:06:10.010><c> so</c><00:06:10.070><c> like</c><00:06:10.310><c> us</c><00:06:11.180><c> a</c>

00:06:11.290 --> 00:06:11.300 align:start position:0%
don't index arrays do ok so like us a
 

00:06:11.300 --> 00:06:14.050 align:start position:0%
don't index arrays do ok so like us a
array<00:06:11.810><c> of</c><00:06:12.470><c> indexes</c><00:06:13.100><c> 5</c><00:06:13.250><c> you</c><00:06:13.610><c> can</c><00:06:13.730><c> go</c><00:06:13.820><c> right</c><00:06:14.030><c> to</c>

00:06:14.050 --> 00:06:14.060 align:start position:0%
array of indexes 5 you can go right to
 

00:06:14.060 --> 00:06:18.280 align:start position:0%
array of indexes 5 you can go right to
it<00:06:14.210><c> it's</c><00:06:14.780><c> a</c><00:06:14.990><c> constant</c><00:06:15.710><c> time</c><00:06:15.890><c> to</c><00:06:16.580><c> access</c><00:06:17.290><c> access</c>

00:06:18.280 --> 00:06:18.290 align:start position:0%
it it's a constant time to access access
 

00:06:18.290 --> 00:06:20.520 align:start position:0%
it it's a constant time to access access
access<00:06:19.040><c> that</c><00:06:19.190><c> part</c><00:06:19.370><c> of</c><00:06:19.430><c> memory</c><00:06:19.640><c> because</c>

00:06:20.520 --> 00:06:20.530 align:start position:0%
access that part of memory because
 

00:06:20.530 --> 00:06:22.810 align:start position:0%
access that part of memory because
because<00:06:21.530><c> it's</c><00:06:21.680><c> indexed</c><00:06:22.070><c> linked</c><00:06:22.430><c> list</c><00:06:22.610><c> or</c><00:06:22.670><c> not</c>

00:06:22.810 --> 00:06:22.820 align:start position:0%
because it's indexed linked list or not
 

00:06:22.820 --> 00:06:24.130 align:start position:0%
because it's indexed linked list or not
you<00:06:23.240><c> still</c><00:06:23.360><c> have</c><00:06:23.480><c> to</c><00:06:23.570><c> traverse</c><00:06:23.690><c> through</c><00:06:24.080><c> the</c>

00:06:24.130 --> 00:06:24.140 align:start position:0%
you still have to traverse through the
 

00:06:24.140 --> 00:06:25.720 align:start position:0%
you still have to traverse through the
linked<00:06:24.350><c> list</c><00:06:24.410><c> to</c><00:06:24.770><c> find</c><00:06:24.950><c> whatever</c><00:06:25.130><c> it</c><00:06:25.280><c> is</c><00:06:25.340><c> much</c>

00:06:25.720 --> 00:06:25.730 align:start position:0%
linked list to find whatever it is much
 

00:06:25.730 --> 00:06:28.540 align:start position:0%
linked list to find whatever it is much
it's<00:06:26.030><c> basically</c><00:06:26.390><c> the</c><00:06:27.080><c> same</c><00:06:27.290><c> as</c><00:06:27.650><c> searching</c><00:06:28.220><c> ok</c>

00:06:28.540 --> 00:06:28.550 align:start position:0%
it's basically the same as searching ok
 

00:06:28.550 --> 00:06:31.590 align:start position:0%
it's basically the same as searching ok
and<00:06:28.850><c> these</c><00:06:29.540><c> are</c><00:06:29.690><c> Big</c><00:06:29.840><c> O</c><00:06:29.870><c> of</c><00:06:29.960><c> N</c>

00:06:31.590 --> 00:06:31.600 align:start position:0%
and these are Big O of N
 

00:06:31.600 --> 00:06:35.910 align:start position:0%
and these are Big O of N
because<00:06:32.400><c> if</c><00:06:33.400><c> you</c><00:06:33.490><c> have</c><00:06:33.580><c> a</c><00:06:33.610><c> size</c><00:06:34.240><c> of</c><00:06:34.360><c> 10</c><00:06:34.800><c> or</c><00:06:35.800><c> if</c>

00:06:35.910 --> 00:06:35.920 align:start position:0%
because if you have a size of 10 or if
 

00:06:35.920 --> 00:06:36.990 align:start position:0%
because if you have a size of 10 or if
you<00:06:35.980><c> have</c><00:06:36.070><c> a</c><00:06:36.100><c> size</c><00:06:36.280><c> of</c><00:06:36.400><c> a</c><00:06:36.430><c> hundred</c><00:06:36.550><c> thousand</c>

00:06:36.990 --> 00:06:37.000 align:start position:0%
you have a size of a hundred thousand
 

00:06:37.000 --> 00:06:39.420 align:start position:0%
you have a size of a hundred thousand
the<00:06:37.480><c> time</c><00:06:37.750><c> to</c><00:06:37.960><c> search</c><00:06:38.290><c> for</c><00:06:38.680><c> whatever</c><00:06:38.890><c> value</c>

00:06:39.420 --> 00:06:39.430 align:start position:0%
the time to search for whatever value
 

00:06:39.430 --> 00:06:41.370 align:start position:0%
the time to search for whatever value
that<00:06:40.150><c> is</c><00:06:40.300><c> because</c><00:06:40.510><c> we</c><00:06:40.660><c> don't</c><00:06:40.900><c> know</c><00:06:41.050><c> where</c><00:06:41.260><c> it's</c>

00:06:41.370 --> 00:06:41.380 align:start position:0%
that is because we don't know where it's
 

00:06:41.380 --> 00:06:43.650 align:start position:0%
that is because we don't know where it's
at<00:06:41.470><c> we</c><00:06:41.650><c> have</c><00:06:41.800><c> to</c><00:06:41.920><c> traverse</c><00:06:42.220><c> the</c><00:06:42.400><c> list</c><00:06:42.580><c> is</c><00:06:43.300><c> going</c>

00:06:43.650 --> 00:06:43.660 align:start position:0%
at we have to traverse the list is going
 

00:06:43.660 --> 00:06:45.780 align:start position:0%
at we have to traverse the list is going
to<00:06:43.720><c> proportionally</c><00:06:44.440><c> increase</c><00:06:44.980><c> with</c><00:06:45.430><c> the</c><00:06:45.550><c> size</c>

00:06:45.780 --> 00:06:45.790 align:start position:0%
to proportionally increase with the size
 

00:06:45.790 --> 00:06:47.820 align:start position:0%
to proportionally increase with the size
of<00:06:46.000><c> the</c><00:06:46.090><c> input</c><00:06:46.360><c> so</c><00:06:46.900><c> the</c><00:06:47.080><c> time</c><00:06:47.320><c> is</c><00:06:47.590><c> going</c><00:06:47.770><c> to</c>

00:06:47.820 --> 00:06:47.830 align:start position:0%
of the input so the time is going to
 

00:06:47.830 --> 00:06:50.880 align:start position:0%
of the input so the time is going to
increase<00:06:48.310><c> linearly</c><00:06:49.000><c> with</c><00:06:49.180><c> the</c><00:06:50.170><c> size</c><00:06:50.440><c> of</c><00:06:50.800><c> the</c>

00:06:50.880 --> 00:06:50.890 align:start position:0%
increase linearly with the size of the
 

00:06:50.890 --> 00:06:52.980 align:start position:0%
increase linearly with the size of the
input<00:06:51.040><c> and</c><00:06:51.430><c> we</c><00:06:52.300><c> call</c><00:06:52.450><c> this</c><00:06:52.540><c> Big</c><00:06:52.690><c> O</c><00:06:52.780><c> of</c><00:06:52.810><c> M</c>

00:06:52.980 --> 00:06:52.990 align:start position:0%
input and we call this Big O of M
 

00:06:52.990 --> 00:06:57.030 align:start position:0%
input and we call this Big O of M
because<00:06:53.850><c> that</c><00:06:54.850><c> it's</c><00:06:55.450><c> linear</c><00:06:55.810><c> time</c><00:06:56.080><c> right</c><00:06:56.680><c> so</c><00:06:56.710><c> n</c>

00:06:57.030 --> 00:06:57.040 align:start position:0%
because that it's linear time right so n
 

00:06:57.040 --> 00:06:58.890 align:start position:0%
because that it's linear time right so n
is<00:06:57.220><c> decides</c><00:06:57.610><c> the</c><00:06:57.730><c> input</c><00:06:58.000><c> so</c><00:06:58.480><c> the</c><00:06:58.570><c> time</c><00:06:58.750><c> is</c>

00:06:58.890 --> 00:06:58.900 align:start position:0%
is decides the input so the time is
 

00:06:58.900 --> 00:07:01.860 align:start position:0%
is decides the input so the time is
going<00:06:59.080><c> to</c><00:06:59.110><c> increase</c><00:06:59.490><c> alongside</c><00:07:00.490><c> of</c><00:07:01.150><c> M</c><00:07:01.570><c> all</c>

00:07:01.860 --> 00:07:01.870 align:start position:0%
going to increase alongside of M all
 

00:07:01.870 --> 00:07:05.850 align:start position:0%
going to increase alongside of M all
right<00:07:02.080><c> so</c><00:07:02.170><c> this</c><00:07:02.230><c> is</c><00:07:02.380><c> Big</c><00:07:02.560><c> O</c><00:07:02.650><c> of</c><00:07:02.680><c> M</c><00:07:04.860><c> insertion</c>

00:07:05.850 --> 00:07:05.860 align:start position:0%
right so this is Big O of M insertion
 

00:07:05.860 --> 00:07:08.460 align:start position:0%
right so this is Big O of M insertion
deletion<00:07:06.010><c> next</c><00:07:06.970><c> so</c><00:07:07.210><c> insertion</c><00:07:07.600><c> is</c><00:07:07.990><c> Big</c><00:07:08.140><c> O</c><00:07:08.230><c> of</c><00:07:08.290><c> 1</c>

00:07:08.460 --> 00:07:08.470 align:start position:0%
deletion next so insertion is Big O of 1
 

00:07:08.470 --> 00:07:10.020 align:start position:0%
deletion next so insertion is Big O of 1
for<00:07:09.010><c> whenever</c><00:07:09.130><c> we're</c><00:07:09.340><c> inserting</c><00:07:09.640><c> into</c><00:07:09.970><c> the</c>

00:07:10.020 --> 00:07:10.030 align:start position:0%
for whenever we're inserting into the
 

00:07:10.030 --> 00:07:12.210 align:start position:0%
for whenever we're inserting into the
beginning<00:07:10.360><c> or</c><00:07:10.750><c> the</c><00:07:10.810><c> end</c><00:07:11.140><c> if</c><00:07:11.920><c> we're</c><00:07:12.070><c> inserting</c>

00:07:12.210 --> 00:07:12.220 align:start position:0%
beginning or the end if we're inserting
 

00:07:12.220 --> 00:07:15.090 align:start position:0%
beginning or the end if we're inserting
into<00:07:12.520><c> the</c><00:07:12.640><c> middle</c><00:07:13.320><c> then</c><00:07:14.320><c> it's</c><00:07:14.650><c> actually</c><00:07:14.950><c> still</c>

00:07:15.090 --> 00:07:15.100 align:start position:0%
into the middle then it's actually still
 

00:07:15.100 --> 00:07:19.320 align:start position:0%
into the middle then it's actually still
Big<00:07:15.310><c> O</c><00:07:15.430><c> of</c><00:07:15.460><c> n</c><00:07:15.660><c> because</c><00:07:16.740><c> even</c><00:07:17.740><c> if</c><00:07:18.040><c> you</c><00:07:18.490><c> traverse</c>

00:07:19.320 --> 00:07:19.330 align:start position:0%
Big O of n because even if you traverse
 

00:07:19.330 --> 00:07:21.300 align:start position:0%
Big O of n because even if you traverse
to<00:07:19.600><c> the</c><00:07:19.990><c> nodes</c><00:07:20.200><c> that</c><00:07:20.320><c> you</c><00:07:20.380><c> eventually</c><00:07:20.530><c> find</c><00:07:20.770><c> so</c>

00:07:21.300 --> 00:07:21.310 align:start position:0%
to the nodes that you eventually find so
 

00:07:21.310 --> 00:07:22.800 align:start position:0%
to the nodes that you eventually find so
your<00:07:21.490><c> search</c><00:07:21.730><c> for</c><00:07:21.970><c> it</c><00:07:22.060><c> which</c><00:07:22.270><c> is</c><00:07:22.300><c> Big</c><00:07:22.540><c> O</c><00:07:22.630><c> of</c><00:07:22.660><c> n</c>

00:07:22.800 --> 00:07:22.810 align:start position:0%
your search for it which is Big O of n
 

00:07:22.810 --> 00:07:24.660 align:start position:0%
your search for it which is Big O of n
right<00:07:23.260><c> but</c><00:07:23.770><c> generally</c><00:07:24.070><c> speaking</c><00:07:24.130><c> one</c><00:07:24.460><c> dates</c>

00:07:24.660 --> 00:07:24.670 align:start position:0%
right but generally speaking one dates
 

00:07:24.670 --> 00:07:27.420 align:start position:0%
right but generally speaking one dates
when<00:07:25.600><c> we're</c><00:07:26.380><c> speaking</c><00:07:26.650><c> of</c><00:07:26.890><c> time</c><00:07:27.100><c> complexity</c>

00:07:27.420 --> 00:07:27.430 align:start position:0%
when we're speaking of time complexity
 

00:07:27.430 --> 00:07:30.170 align:start position:0%
when we're speaking of time complexity
it's<00:07:27.700><c> just</c><00:07:28.000><c> to</c><00:07:28.090><c> perform</c><00:07:28.480><c> the</c><00:07:28.570><c> operation</c><00:07:29.170><c> so</c>

00:07:30.170 --> 00:07:30.180 align:start position:0%
it's just to perform the operation so
 

00:07:30.180 --> 00:07:32.880 align:start position:0%
it's just to perform the operation so
what<00:07:31.180><c> if</c><00:07:31.300><c> you</c><00:07:31.420><c> know</c><00:07:31.600><c> where</c><00:07:31.780><c> the</c><00:07:31.900><c> note</c><00:07:32.110><c> is</c><00:07:32.140><c> then</c>

00:07:32.880 --> 00:07:32.890 align:start position:0%
what if you know where the note is then
 

00:07:32.890 --> 00:07:34.470 align:start position:0%
what if you know where the note is then
we're<00:07:33.040><c> only</c><00:07:33.160><c> worried</c><00:07:33.520><c> about</c><00:07:33.580><c> how</c><00:07:34.150><c> long</c><00:07:34.390><c> does</c>

00:07:34.470 --> 00:07:34.480 align:start position:0%
we're only worried about how long does
 

00:07:34.480 --> 00:07:36.330 align:start position:0%
we're only worried about how long does
it<00:07:34.570><c> take</c><00:07:34.780><c> to</c><00:07:34.930><c> perform</c><00:07:35.050><c> the</c><00:07:35.380><c> actual</c><00:07:35.830><c> operation</c>

00:07:36.330 --> 00:07:36.340 align:start position:0%
it take to perform the actual operation
 

00:07:36.340 --> 00:07:39.870 align:start position:0%
it take to perform the actual operation
of<00:07:36.490><c> inserting</c><00:07:36.970><c> all</c><00:07:37.420><c> right</c><00:07:38.110><c> so</c><00:07:38.610><c> even</c><00:07:39.610><c> if</c><00:07:39.730><c> we</c>

00:07:39.870 --> 00:07:39.880 align:start position:0%
of inserting all right so even if we
 

00:07:39.880 --> 00:07:42.180 align:start position:0%
of inserting all right so even if we
find<00:07:40.150><c> the</c><00:07:40.420><c> node</c><00:07:40.660><c> in</c><00:07:40.990><c> a</c><00:07:41.410><c> circularly</c><00:07:41.950><c> linked</c>

00:07:42.180 --> 00:07:42.190 align:start position:0%
find the node in a circularly linked
 

00:07:42.190 --> 00:07:45.060 align:start position:0%
find the node in a circularly linked
list<00:07:42.630><c> again</c><00:07:43.630><c> this</c><00:07:43.750><c> is</c><00:07:43.900><c> still</c><00:07:44.080><c> a</c><00:07:44.320><c> singly</c><00:07:44.860><c> linked</c>

00:07:45.060 --> 00:07:45.070 align:start position:0%
list again this is still a singly linked
 

00:07:45.070 --> 00:07:48.780 align:start position:0%
list again this is still a singly linked
list<00:07:45.280><c> there's</c><00:07:45.580><c> no</c><00:07:47.400><c> underlying</c><00:07:48.400><c> implication</c>

00:07:48.780 --> 00:07:48.790 align:start position:0%
list there's no underlying implication
 

00:07:48.790 --> 00:07:51.810 align:start position:0%
list there's no underlying implication
is<00:07:49.300><c> singly</c><00:07:49.690><c> linked</c><00:07:50.230><c> list</c><00:07:50.440><c> not</c><00:07:50.680><c> doubly</c><00:07:51.310><c> okay</c><00:07:51.760><c> so</c>

00:07:51.810 --> 00:07:51.820 align:start position:0%
is singly linked list not doubly okay so
 

00:07:51.820 --> 00:07:55.110 align:start position:0%
is singly linked list not doubly okay so
we<00:07:52.090><c> have</c><00:07:52.240><c> no</c><00:07:52.350><c> previous</c><00:07:53.350><c> reference</c><00:07:53.970><c> to</c><00:07:54.970><c> know</c>

00:07:55.110 --> 00:07:55.120 align:start position:0%
we have no previous reference to know
 

00:07:55.120 --> 00:07:57.150 align:start position:0%
we have no previous reference to know
where<00:07:55.270><c> the</c><00:07:55.390><c> node</c><00:07:55.570><c> before</c><00:07:55.810><c> it</c><00:07:56.080><c> is</c><00:07:56.200><c> so</c><00:07:56.800><c> even</c><00:07:57.040><c> we</c>

00:07:57.150 --> 00:07:57.160 align:start position:0%
where the node before it is so even we
 

00:07:57.160 --> 00:07:58.620 align:start position:0%
where the node before it is so even we
find<00:07:57.490><c> the</c><00:07:57.580><c> node</c><00:07:57.790><c> it</c><00:07:57.970><c> has</c><00:07:58.030><c> to</c><00:07:58.210><c> perform</c><00:07:58.540><c> the</c>

00:07:58.620 --> 00:07:58.630 align:start position:0%
find the node it has to perform the
 

00:07:58.630 --> 00:08:00.630 align:start position:0%
find the node it has to perform the
insertion<00:07:59.080><c> somewhere</c><00:07:59.500><c> in</c><00:07:59.560><c> the</c><00:07:59.620><c> middle</c><00:07:59.860><c> we</c>

00:08:00.630 --> 00:08:00.640 align:start position:0%
insertion somewhere in the middle we
 

00:08:00.640 --> 00:08:04.560 align:start position:0%
insertion somewhere in the middle we
still<00:08:01.030><c> have</c><00:08:01.360><c> to</c><00:08:02.310><c> actually</c><00:08:03.310><c> traverse</c><00:08:03.790><c> from</c><00:08:04.420><c> the</c>

00:08:04.560 --> 00:08:04.570 align:start position:0%
still have to actually traverse from the
 

00:08:04.570 --> 00:08:08.550 align:start position:0%
still have to actually traverse from the
head<00:08:04.780><c> to</c><00:08:05.560><c> whatever</c><00:08:06.250><c> node</c><00:08:06.640><c> is</c><00:08:06.790><c> before</c><00:08:07.210><c> it</c><00:08:07.560><c> in</c>

00:08:08.550 --> 00:08:08.560 align:start position:0%
head to whatever node is before it in
 

00:08:08.560 --> 00:08:12.030 align:start position:0%
head to whatever node is before it in
order<00:08:08.800><c> to</c><00:08:09.810><c> manipulate</c><00:08:10.810><c> the</c><00:08:11.140><c> references</c><00:08:11.620><c> okay</c>

00:08:12.030 --> 00:08:12.040 align:start position:0%
order to manipulate the references okay
 

00:08:12.040 --> 00:08:13.590 align:start position:0%
order to manipulate the references okay
in<00:08:12.700><c> linked</c><00:08:12.940><c> lists</c><00:08:13.120><c> you're</c><00:08:13.210><c> really</c><00:08:13.390><c> really</c>

00:08:13.590 --> 00:08:13.600 align:start position:0%
in linked lists you're really really
 

00:08:13.600 --> 00:08:16.110 align:start position:0%
in linked lists you're really really
just<00:08:13.810><c> manipulating</c><00:08:14.230><c> references</c><00:08:14.680><c> so</c><00:08:15.280><c> to</c>

00:08:16.110 --> 00:08:16.120 align:start position:0%
just manipulating references so to
 

00:08:16.120 --> 00:08:17.280 align:start position:0%
just manipulating references so to
insert<00:08:16.360><c> someone</c><00:08:16.600><c> in</c><00:08:16.660><c> the</c><00:08:16.720><c> middle</c><00:08:16.900><c> you</c><00:08:17.050><c> still</c>

00:08:17.280 --> 00:08:17.290 align:start position:0%
insert someone in the middle you still
 

00:08:17.290 --> 00:08:19.440 align:start position:0%
insert someone in the middle you still
have<00:08:17.560><c> to</c><00:08:17.770><c> do</c><00:08:17.950><c> a</c><00:08:17.980><c> search</c><00:08:18.430><c> which</c><00:08:18.820><c> is</c><00:08:18.850><c> Big</c><00:08:19.120><c> O</c><00:08:19.150><c> of</c><00:08:19.240><c> n</c>

00:08:19.440 --> 00:08:19.450 align:start position:0%
have to do a search which is Big O of n
 

00:08:19.450 --> 00:08:23.100 align:start position:0%
have to do a search which is Big O of n
so<00:08:20.350><c> in</c><00:08:21.300><c> inserting</c><00:08:22.300><c> anywhere</c><00:08:22.660><c> besides</c><00:08:23.020><c> the</c>

00:08:23.100 --> 00:08:23.110 align:start position:0%
so in inserting anywhere besides the
 

00:08:23.110 --> 00:08:24.990 align:start position:0%
so in inserting anywhere besides the
beginning<00:08:23.440><c> in</c><00:08:23.560><c> the</c><00:08:23.650><c> end</c><00:08:23.740><c> is</c><00:08:24.010><c> bigger</c><00:08:24.310><c> than</c><00:08:24.610><c> all</c>

00:08:24.990 --> 00:08:25.000 align:start position:0%
beginning in the end is bigger than all
 

00:08:25.000 --> 00:08:26.700 align:start position:0%
beginning in the end is bigger than all
right<00:08:25.060><c> now</c><00:08:25.720><c> deletion</c><00:08:26.140><c> is</c><00:08:26.230><c> the</c><00:08:26.290><c> only</c><00:08:26.380><c> Big</c><00:08:26.590><c> O</c><00:08:26.680><c> of</c>

00:08:26.700 --> 00:08:26.710 align:start position:0%
right now deletion is the only Big O of
 

00:08:26.710 --> 00:08:29.100 align:start position:0%
right now deletion is the only Big O of
one<00:08:26.980><c> when</c><00:08:27.640><c> you're</c><00:08:27.760><c> inserting</c><00:08:28.630><c> into</c><00:08:28.900><c> the</c>

00:08:29.100 --> 00:08:29.110 align:start position:0%
one when you're inserting into the
 

00:08:29.110 --> 00:08:30.450 align:start position:0%
one when you're inserting into the
beginning<00:08:29.470><c> because</c><00:08:30.100><c> if</c><00:08:30.190><c> you're</c><00:08:30.280><c> inserting</c>

00:08:30.450 --> 00:08:30.460 align:start position:0%
beginning because if you're inserting
 

00:08:30.460 --> 00:08:32.459 align:start position:0%
beginning because if you're inserting
into<00:08:30.670><c> the</c><00:08:30.850><c> tail</c><00:08:31.060><c> we</c><00:08:31.990><c> don't</c><00:08:32.169><c> know</c><00:08:32.260><c> what</c><00:08:32.380><c> the</c>

00:08:32.459 --> 00:08:32.469 align:start position:0%
into the tail we don't know what the
 

00:08:32.469 --> 00:08:34.140 align:start position:0%
into the tail we don't know what the
previous<00:08:32.710><c> reference</c><00:08:33.340><c> we</c><00:08:33.849><c> all</c><00:08:33.969><c> know</c><00:08:34.060><c> the</c>

00:08:34.140 --> 00:08:34.150 align:start position:0%
previous reference we all know the
 

00:08:34.150 --> 00:08:36.450 align:start position:0%
previous reference we all know the
previous<00:08:34.479><c> node</c><00:08:34.830><c> before</c><00:08:35.830><c> the</c><00:08:36.130><c> tail</c><00:08:36.310><c> right</c>

00:08:36.450 --> 00:08:36.460 align:start position:0%
previous node before the tail right
 

00:08:36.460 --> 00:08:37.709 align:start position:0%
previous node before the tail right
because<00:08:36.640><c> we</c><00:08:36.760><c> had</c><00:08:36.849><c> no</c><00:08:36.969><c> reference</c><00:08:37.240><c> point</c><00:08:37.450><c> to</c><00:08:37.510><c> it</c>

00:08:37.709 --> 00:08:37.719 align:start position:0%
because we had no reference point to it
 

00:08:37.719 --> 00:08:40.020 align:start position:0%
because we had no reference point to it
unless<00:08:38.020><c> we</c><00:08:38.229><c> traverse</c><00:08:38.650><c> all</c><00:08:39.280><c> the</c><00:08:39.640><c> way</c><00:08:39.790><c> through</c>

00:08:40.020 --> 00:08:40.030 align:start position:0%
unless we traverse all the way through
 

00:08:40.030 --> 00:08:42.480 align:start position:0%
unless we traverse all the way through
the<00:08:40.150><c> list</c><00:08:40.360><c> and</c><00:08:40.630><c> minus</c><00:08:41.169><c> one</c><00:08:41.349><c> times</c><00:08:41.589><c> to</c><00:08:42.339><c> get</c>

00:08:42.480 --> 00:08:42.490 align:start position:0%
the list and minus one times to get
 

00:08:42.490 --> 00:08:44.180 align:start position:0%
the list and minus one times to get
there<00:08:42.700><c> so</c>

00:08:44.180 --> 00:08:44.190 align:start position:0%
there so
 

00:08:44.190 --> 00:08:46.250 align:start position:0%
there so
deletion<00:08:45.090><c> is</c><00:08:45.210><c> big</c><00:08:45.540><c> of</c><00:08:45.570><c> one</c><00:08:45.870><c> if</c><00:08:46.020><c> we're</c><00:08:46.170><c> building</c>

00:08:46.250 --> 00:08:46.260 align:start position:0%
deletion is big of one if we're building
 

00:08:46.260 --> 00:08:49.130 align:start position:0%
deletion is big of one if we're building
from<00:08:46.470><c> the</c><00:08:46.590><c> beginning</c><00:08:46.920><c> Big</c><00:08:47.880><c> O</c><00:08:47.910><c> of</c><00:08:48.030><c> n</c><00:08:48.180><c> anywhere</c>

00:08:49.130 --> 00:08:49.140 align:start position:0%
from the beginning Big O of n anywhere
 

00:08:49.140 --> 00:08:52.100 align:start position:0%
from the beginning Big O of n anywhere
else<00:08:49.290><c> basically</c><00:08:49.890><c> okay</c><00:08:50.250><c> and</c><00:08:50.540><c> the</c><00:08:51.540><c> word</c><00:08:51.690><c> it's</c>

00:08:52.100 --> 00:08:52.110 align:start position:0%
else basically okay and the word it's
 

00:08:52.110 --> 00:08:53.540 align:start position:0%
else basically okay and the word it's
the<00:08:52.170><c> same</c><00:08:52.410><c> for</c><00:08:52.680><c> the</c><00:08:52.740><c> average</c><00:08:53.070><c> average</c><00:08:53.460><c> of</c>

00:08:53.540 --> 00:08:53.550 align:start position:0%
the same for the average average of
 

00:08:53.550 --> 00:08:55.670 align:start position:0%
the same for the average average of
worst<00:08:53.730><c> are</c><00:08:54.000><c> the</c><00:08:54.630><c> same</c><00:08:54.930><c> for</c><00:08:55.350><c> a</c><00:08:55.380><c> circular</c>

00:08:55.670 --> 00:08:55.680 align:start position:0%
worst are the same for a circular
 

00:08:55.680 --> 00:08:57.470 align:start position:0%
worst are the same for a circular
linkless<00:08:56.220><c> all</c><00:08:56.430><c> right</c><00:08:56.550><c> there's</c><00:08:56.760><c> there's</c><00:08:57.210><c> no</c>

00:08:57.470 --> 00:08:57.480 align:start position:0%
linkless all right there's there's no
 

00:08:57.480 --> 00:08:59.690 align:start position:0%
linkless all right there's there's no
difference<00:08:58.250><c> here's</c><00:08:59.250><c> the</c><00:08:59.280><c> worst</c><00:08:59.460><c> case</c><00:08:59.670><c> you</c>

00:08:59.690 --> 00:08:59.700 align:start position:0%
difference here's the worst case you
 

00:08:59.700 --> 00:09:01.700 align:start position:0%
difference here's the worst case you
have<00:08:59.940><c> to</c><00:09:00.030><c> still</c><00:09:00.510><c> traverse</c><00:09:00.930><c> the</c><00:09:01.410><c> whole</c><00:09:01.440><c> list</c>

00:09:01.700 --> 00:09:01.710 align:start position:0%
have to still traverse the whole list
 

00:09:01.710 --> 00:09:06.020 align:start position:0%
have to still traverse the whole list
and<00:09:01.980><c> that's</c><00:09:02.400><c> just</c><00:09:02.520><c> Big</c><00:09:02.670><c> O</c><00:09:02.760><c> then</c><00:09:03.000><c> so</c><00:09:03.540><c> so</c><00:09:05.030><c> no</c>

00:09:06.020 --> 00:09:06.030 align:start position:0%
and that's just Big O then so so no
 

00:09:06.030 --> 00:09:07.760 align:start position:0%
and that's just Big O then so so no
difference<00:09:06.330><c> there</c><00:09:06.450><c> and</c><00:09:06.660><c> space</c><00:09:07.140><c> complexity</c><00:09:07.560><c> is</c>

00:09:07.760 --> 00:09:07.770 align:start position:0%
difference there and space complexity is
 

00:09:07.770 --> 00:09:10.070 align:start position:0%
difference there and space complexity is
Big<00:09:07.950><c> O</c><00:09:08.010><c> of</c><00:09:08.040><c> n</c><00:09:08.190><c> and</c><00:09:08.430><c> so</c><00:09:09.000><c> again</c><00:09:09.420><c> space</c><00:09:09.660><c> complexity</c>

00:09:10.070 --> 00:09:10.080 align:start position:0%
Big O of n and so again space complexity
 

00:09:10.080 --> 00:09:12.200 align:start position:0%
Big O of n and so again space complexity
real<00:09:10.230><c> quick</c><00:09:10.410><c> is</c><00:09:10.590><c> just</c><00:09:10.980><c> how</c><00:09:11.430><c> much</c><00:09:11.610><c> storage</c><00:09:12.000><c> is</c>

00:09:12.200 --> 00:09:12.210 align:start position:0%
real quick is just how much storage is
 

00:09:12.210 --> 00:09:15.020 align:start position:0%
real quick is just how much storage is
used<00:09:12.240><c> to</c><00:09:12.930><c> perform</c><00:09:13.290><c> the</c><00:09:13.560><c> algorithm</c><00:09:13.950><c> or</c><00:09:14.130><c> to</c><00:09:14.910><c> do</c>

00:09:15.020 --> 00:09:15.030 align:start position:0%
used to perform the algorithm or to do
 

00:09:15.030 --> 00:09:18.020 align:start position:0%
used to perform the algorithm or to do
an<00:09:15.120><c> operation</c><00:09:15.390><c> on</c><00:09:15.600><c> it</c><00:09:15.750><c> and</c><00:09:16.190><c> we</c><00:09:17.190><c> don't</c><00:09:17.580><c> need</c><00:09:17.820><c> any</c>

00:09:18.020 --> 00:09:18.030 align:start position:0%
an operation on it and we don't need any
 

00:09:18.030 --> 00:09:21.560 align:start position:0%
an operation on it and we don't need any
more<00:09:18.180><c> space</c><00:09:18.510><c> besides</c><00:09:19.050><c> the</c><00:09:20.390><c> circular</c><00:09:21.390><c> link</c>

00:09:21.560 --> 00:09:21.570 align:start position:0%
more space besides the circular link
 

00:09:21.570 --> 00:09:23.060 align:start position:0%
more space besides the circular link
list<00:09:21.750><c> that</c><00:09:21.870><c> we</c><00:09:21.930><c> have</c><00:09:21.960><c> we</c><00:09:22.320><c> don't</c><00:09:22.500><c> need</c><00:09:22.650><c> any</c><00:09:22.890><c> more</c>

00:09:23.060 --> 00:09:23.070 align:start position:0%
list that we have we don't need any more
 

00:09:23.070 --> 00:09:26.080 align:start position:0%
list that we have we don't need any more
storage<00:09:23.340><c> space</c><00:09:23.580><c> so</c><00:09:24.050><c> whatever</c><00:09:25.050><c> the</c><00:09:25.170><c> size</c><00:09:25.470><c> of</c>

00:09:26.080 --> 00:09:26.090 align:start position:0%
storage space so whatever the size of
 

00:09:26.090 --> 00:09:28.940 align:start position:0%
storage space so whatever the size of
the<00:09:27.090><c> link</c><00:09:27.270><c> of</c><00:09:27.510><c> the</c><00:09:27.990><c> link</c><00:09:28.140><c> this</c><00:09:28.260><c> is</c><00:09:28.440><c> which</c><00:09:28.620><c> is</c><00:09:28.800><c> in</c>

00:09:28.940 --> 00:09:28.950 align:start position:0%
the link of the link this is which is in
 

00:09:28.950 --> 00:09:30.740 align:start position:0%
the link of the link this is which is in
this<00:09:29.370><c> the</c><00:09:29.460><c> size</c><00:09:29.640><c> of</c><00:09:29.730><c> the</c><00:09:29.790><c> input</c><00:09:29.910><c> that's</c><00:09:30.630><c> all</c>

00:09:30.740 --> 00:09:30.750 align:start position:0%
this the size of the input that's all
 

00:09:30.750 --> 00:09:32.690 align:start position:0%
this the size of the input that's all
the<00:09:30.810><c> space</c><00:09:30.960><c> we</c><00:09:31.110><c> need</c><00:09:31.140><c> so</c><00:09:31.560><c> and</c><00:09:31.830><c> space</c>

00:09:32.690 --> 00:09:32.700 align:start position:0%
the space we need so and space
 

00:09:32.700 --> 00:09:34.820 align:start position:0%
the space we need so and space
complexity<00:09:33.180><c> is</c><00:09:33.690><c> bigger</c><00:09:33.930><c> than</c><00:09:34.170><c> all</c><00:09:34.560><c> right</c>

00:09:34.820 --> 00:09:34.830 align:start position:0%
complexity is bigger than all right
 

00:09:34.830 --> 00:09:37.070 align:start position:0%
complexity is bigger than all right
so<00:09:34.980><c> I</c><00:09:35.250><c> hope</c><00:09:35.400><c> you</c><00:09:35.490><c> learn</c><00:09:35.610><c> something</c><00:09:35.790><c> and</c><00:09:36.080><c> I'll</c>

00:09:37.070 --> 00:09:37.080 align:start position:0%
so I hope you learn something and I'll
 

00:09:37.080 --> 00:09:39.470 align:start position:0%
so I hope you learn something and I'll
see<00:09:37.170><c> you</c><00:09:37.230><c> next</c><00:09:37.290><c> time</c>

