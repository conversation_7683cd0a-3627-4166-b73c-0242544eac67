WEBVTT
Kind: captions
Language: en

00:00:02.200 --> 00:00:04.590 align:start position:0%
 
hello<00:00:02.720><c> Ravi</c><00:00:03.120><c> here</c><00:00:03.280><c> from</c><00:00:03.439><c> llama</c><00:00:03.760><c> index</c><00:00:04.319><c> welcome</c>

00:00:04.590 --> 00:00:04.600 align:start position:0%
hello <PERSON> here from llama index welcome
 

00:00:04.600 --> 00:00:06.470 align:start position:0%
hello <PERSON> here from llama index welcome
to<00:00:04.799><c> another</c><00:00:05.120><c> video</c><00:00:05.440><c> in</c><00:00:05.600><c> this</c><00:00:05.759><c> tutorial</c><00:00:06.160><c> series</c>

00:00:06.470 --> 00:00:06.480 align:start position:0%
to another video in this tutorial series
 

00:00:06.480 --> 00:00:09.030 align:start position:0%
to another video in this tutorial series
on<00:00:06.839><c> uh</c><00:00:07.000><c> property</c><00:00:07.399><c> graphs</c><00:00:08.280><c> so</c><00:00:08.480><c> in</c><00:00:08.639><c> this</c><00:00:08.800><c> video</c>

00:00:09.030 --> 00:00:09.040 align:start position:0%
on uh property graphs so in this video
 

00:00:09.040 --> 00:00:11.030 align:start position:0%
on uh property graphs so in this video
we'll<00:00:09.320><c> demonstrate</c><00:00:09.840><c> the</c><00:00:10.040><c> property</c><00:00:10.400><c> graph</c><00:00:10.920><c> uh</c>

00:00:11.030 --> 00:00:11.040 align:start position:0%
we'll demonstrate the property graph uh
 

00:00:11.040 --> 00:00:14.549 align:start position:0%
we'll demonstrate the property graph uh
index<00:00:11.960><c> uh</c><00:00:12.080><c> in</c><00:00:12.240><c> Lama</c><00:00:12.599><c> index</c><00:00:13.599><c> uh</c><00:00:13.679><c> so</c><00:00:13.880><c> we'll</c><00:00:14.280><c> start</c>

00:00:14.549 --> 00:00:14.559 align:start position:0%
index uh in Lama index uh so we'll start
 

00:00:14.559 --> 00:00:16.550 align:start position:0%
index uh in Lama index uh so we'll start
with<00:00:14.719><c> implementing</c><00:00:15.360><c> property</c><00:00:15.679><c> graph</c><00:00:16.000><c> using</c>

00:00:16.550 --> 00:00:16.560 align:start position:0%
with implementing property graph using
 

00:00:16.560 --> 00:00:19.750 align:start position:0%
with implementing property graph using
uh<00:00:17.400><c> default</c><00:00:17.840><c> extractors</c><00:00:18.359><c> and</c><00:00:18.560><c> retrievers</c><00:00:19.560><c> and</c>

00:00:19.750 --> 00:00:19.760 align:start position:0%
uh default extractors and retrievers and
 

00:00:19.760 --> 00:00:21.550 align:start position:0%
uh default extractors and retrievers and
then<00:00:19.960><c> on</c><00:00:20.080><c> your</c><00:00:20.240><c> given</c><00:00:20.560><c> document</c><00:00:21.080><c> and</c><00:00:21.279><c> then</c>

00:00:21.550 --> 00:00:21.560 align:start position:0%
then on your given document and then
 

00:00:21.560 --> 00:00:23.470 align:start position:0%
then on your given document and then
once<00:00:21.760><c> the</c><00:00:21.920><c> construction</c><00:00:22.359><c> is</c><00:00:22.519><c> done</c><00:00:22.840><c> we'll</c><00:00:23.359><c> uh</c>

00:00:23.470 --> 00:00:23.480 align:start position:0%
once the construction is done we'll uh
 

00:00:23.480 --> 00:00:26.230 align:start position:0%
once the construction is done we'll uh
start<00:00:23.760><c> quering</c><00:00:24.199><c> with</c><00:00:24.560><c> default</c><00:00:25.080><c> retrievers</c><00:00:26.080><c> so</c>

00:00:26.230 --> 00:00:26.240 align:start position:0%
start quering with default retrievers so
 

00:00:26.240 --> 00:00:29.470 align:start position:0%
start quering with default retrievers so
we'll<00:00:26.439><c> use</c><00:00:26.920><c> M</c><00:00:27.760><c> uh</c><00:00:28.080><c> llm</c><00:00:28.560><c> and</c><00:00:28.760><c> embeddings</c><00:00:29.279><c> for</c>

00:00:29.470 --> 00:00:29.480 align:start position:0%
we'll use M uh llm and embeddings for
 

00:00:29.480 --> 00:00:31.790 align:start position:0%
we'll use M uh llm and embeddings for
this<00:00:29.840><c> uh</c><00:00:30.039><c> notebook</c><00:00:30.880><c> so</c><00:00:31.039><c> let's</c><00:00:31.279><c> get</c><00:00:31.439><c> started</c>

00:00:31.790 --> 00:00:31.800 align:start position:0%
this uh notebook so let's get started
 

00:00:31.800 --> 00:00:33.910 align:start position:0%
this uh notebook so let's get started
with<00:00:31.920><c> it</c><00:00:32.119><c> so</c><00:00:32.399><c> before</c><00:00:33.120><c> going</c><00:00:33.360><c> forward</c><00:00:33.680><c> you</c><00:00:33.760><c> need</c>

00:00:33.910 --> 00:00:33.920 align:start position:0%
with it so before going forward you need
 

00:00:33.920 --> 00:00:35.990 align:start position:0%
with it so before going forward you need
to<00:00:34.040><c> install</c><00:00:34.399><c> these</c><00:00:34.600><c> packages</c><00:00:35.360><c> Lama</c><00:00:35.680><c> index</c>

00:00:35.990 --> 00:00:36.000 align:start position:0%
to install these packages Lama index
 

00:00:36.000 --> 00:00:39.350 align:start position:0%
to install these packages Lama index
core<00:00:36.360><c> package</c><00:00:36.879><c> and</c><00:00:37.040><c> then</c><00:00:37.239><c> for</c><00:00:37.440><c> llm</c><00:00:38.440><c> Mr</c><00:00:38.920><c> llm</c><00:00:39.280><c> you</c>

00:00:39.350 --> 00:00:39.360 align:start position:0%
core package and then for llm Mr llm you
 

00:00:39.360 --> 00:00:41.910 align:start position:0%
core package and then for llm Mr llm you
need<00:00:39.480><c> to</c><00:00:39.640><c> install</c><00:00:40.160><c> Lama</c><00:00:40.399><c> index</c><00:00:40.680><c> llm</c><00:00:41.160><c> Mr</c><00:00:41.760><c> and</c>

00:00:41.910 --> 00:00:41.920 align:start position:0%
need to install Lama index llm Mr and
 

00:00:41.920 --> 00:00:45.270 align:start position:0%
need to install Lama index llm Mr and
then<00:00:42.719><c> Lama</c><00:00:43.000><c> index</c><00:00:43.280><c> Tings</c><00:00:43.920><c> Mr</c><00:00:44.920><c> uh</c><00:00:45.039><c> I</c><00:00:45.120><c> have</c>

00:00:45.270 --> 00:00:45.280 align:start position:0%
then Lama index Tings Mr uh I have
 

00:00:45.280 --> 00:00:47.869 align:start position:0%
then Lama index Tings Mr uh I have
already<00:00:45.559><c> installed</c><00:00:45.920><c> it</c><00:00:46.199><c> so</c><00:00:46.480><c> you</c><00:00:46.600><c> don't</c><00:00:47.480><c> so</c>

00:00:47.869 --> 00:00:47.879 align:start position:0%
already installed it so you don't so
 

00:00:47.879 --> 00:00:51.350 align:start position:0%
already installed it so you don't so
I'll<00:00:48.079><c> not</c><00:00:48.239><c> be</c><00:00:48.399><c> running</c><00:00:49.000><c> through</c><00:00:49.199><c> this</c><00:00:49.640><c> cell</c><00:00:50.640><c> um</c>

00:00:51.350 --> 00:00:51.360 align:start position:0%
I'll not be running through this cell um
 

00:00:51.360 --> 00:00:53.189 align:start position:0%
I'll not be running through this cell um
and<00:00:51.520><c> then</c><00:00:51.719><c> next</c><00:00:51.960><c> you</c><00:00:52.079><c> need</c><00:00:52.239><c> to</c><00:00:52.399><c> set</c><00:00:52.559><c> up</c><00:00:52.719><c> the</c><00:00:52.840><c> API</c>

00:00:53.189 --> 00:00:53.199 align:start position:0%
and then next you need to set up the API
 

00:00:53.199 --> 00:00:57.069 align:start position:0%
and then next you need to set up the API
key<00:00:53.440><c> M</c><00:00:53.960><c> AP</c><00:00:54.399><c> key</c><00:00:55.399><c> right</c><00:00:55.760><c> and</c><00:00:55.920><c> we'll</c><00:00:56.160><c> use</c><00:00:56.680><c> Mr</c>

00:00:57.069 --> 00:00:57.079 align:start position:0%
key M AP key right and we'll use Mr
 

00:00:57.079 --> 00:00:59.630 align:start position:0%
key M AP key right and we'll use Mr
Large<00:00:57.440><c> latest</c><00:00:58.079><c> llm</c><00:00:58.800><c> and</c><00:00:58.960><c> then</c><00:00:59.239><c> embedding</c>

00:00:59.630 --> 00:00:59.640 align:start position:0%
Large latest llm and then embedding
 

00:00:59.640 --> 00:01:02.470 align:start position:0%
Large latest llm and then embedding
model<00:01:00.079><c> as</c><00:01:00.239><c> well</c><00:01:01.239><c> and</c><00:01:01.440><c> then</c><00:01:01.840><c> you</c><00:01:02.039><c> need</c><00:01:02.239><c> to</c>

00:01:02.470 --> 00:01:02.480 align:start position:0%
model as well and then you need to
 

00:01:02.480 --> 00:01:03.549 align:start position:0%
model as well and then you need to
download<00:01:02.840><c> the</c>

00:01:03.549 --> 00:01:03.559 align:start position:0%
download the
 

00:01:03.559 --> 00:01:08.230 align:start position:0%
download the
data<00:01:04.559><c> right</c><00:01:05.400><c> so</c><00:01:06.400><c> yeah</c><00:01:07.000><c> um</c><00:01:07.439><c> once</c><00:01:07.640><c> you</c><00:01:07.840><c> download</c>

00:01:08.230 --> 00:01:08.240 align:start position:0%
data right so yeah um once you download
 

00:01:08.240 --> 00:01:10.550 align:start position:0%
data right so yeah um once you download
the<00:01:08.400><c> data</c><00:01:08.799><c> you</c><00:01:09.159><c> load</c><00:01:09.439><c> it</c><00:01:09.920><c> using</c><00:01:10.240><c> simple</c>

00:01:10.550 --> 00:01:10.560 align:start position:0%
the data you load it using simple
 

00:01:10.560 --> 00:01:12.910 align:start position:0%
the data you load it using simple
directory<00:01:10.920><c> reader</c><00:01:11.680><c> and</c><00:01:11.880><c> then</c><00:01:12.320><c> we'll</c><00:01:12.600><c> start</c>

00:01:12.910 --> 00:01:12.920 align:start position:0%
directory reader and then we'll start
 

00:01:12.920 --> 00:01:15.510 align:start position:0%
directory reader and then we'll start
creating<00:01:13.320><c> the</c><00:01:13.600><c> property</c><00:01:13.960><c> graph</c><00:01:14.200><c> index</c><00:01:15.119><c> so</c><00:01:15.320><c> we</c>

00:01:15.510 --> 00:01:15.520 align:start position:0%
creating the property graph index so we
 

00:01:15.520 --> 00:01:18.830 align:start position:0%
creating the property graph index so we
pass<00:01:15.799><c> the</c><00:01:16.520><c> documents</c><00:01:17.200><c> llm</c><00:01:17.640><c> embedding</c><00:01:18.080><c> model</c>

00:01:18.830 --> 00:01:18.840 align:start position:0%
pass the documents llm embedding model
 

00:01:18.840 --> 00:01:23.590 align:start position:0%
pass the documents llm embedding model
and<00:01:19.840><c> create</c><00:01:20.200><c> it</c><00:01:21.200><c> so</c><00:01:22.000><c> this</c><00:01:22.240><c> uses</c><00:01:22.759><c> the</c><00:01:22.960><c> default</c>

00:01:23.590 --> 00:01:23.600 align:start position:0%
and create it so this uses the default
 

00:01:23.600 --> 00:01:25.910 align:start position:0%
and create it so this uses the default
implicit<00:01:24.119><c> path</c><00:01:24.400><c> extractor</c><00:01:25.040><c> and</c><00:01:25.240><c> then</c><00:01:25.640><c> uh</c>

00:01:25.910 --> 00:01:25.920 align:start position:0%
implicit path extractor and then uh
 

00:01:25.920 --> 00:01:27.990 align:start position:0%
implicit path extractor and then uh
simple<00:01:26.240><c> LM</c><00:01:26.560><c> extractor</c><00:01:27.040><c> to</c><00:01:27.520><c> uh</c><00:01:27.600><c> build</c><00:01:27.840><c> a</c>

00:01:27.990 --> 00:01:28.000 align:start position:0%
simple LM extractor to uh build a
 

00:01:28.000 --> 00:01:31.109 align:start position:0%
simple LM extractor to uh build a
property<00:01:28.400><c> graph</c><00:01:29.200><c> if</c><00:01:29.320><c> you</c><00:01:29.560><c> don't</c><00:01:30.079><c> customize</c><00:01:30.560><c> it</c>

00:01:31.109 --> 00:01:31.119 align:start position:0%
property graph if you don't customize it
 

00:01:31.119 --> 00:01:34.149 align:start position:0%
property graph if you don't customize it
we'll<00:01:31.400><c> see</c><00:01:31.600><c> in</c><00:01:31.720><c> the</c><00:01:31.880><c> next</c><00:01:32.840><c> video</c><00:01:33.119><c> or</c><00:01:33.399><c> notebook</c>

00:01:34.149 --> 00:01:34.159 align:start position:0%
we'll see in the next video or notebook
 

00:01:34.159 --> 00:01:37.590 align:start position:0%
we'll see in the next video or notebook
how<00:01:34.320><c> you</c><00:01:34.439><c> can</c><00:01:34.640><c> customize</c><00:01:35.680><c> the</c><00:01:36.680><c> extractor</c><00:01:37.520><c> as</c>

00:01:37.590 --> 00:01:37.600 align:start position:0%
how you can customize the extractor as
 

00:01:37.600 --> 00:01:39.590 align:start position:0%
how you can customize the extractor as
well<00:01:37.759><c> as</c><00:01:37.960><c> retriever</c><00:01:38.479><c> but</c><00:01:38.640><c> for</c><00:01:38.880><c> this</c><00:01:39.240><c> part</c>

00:01:39.590 --> 00:01:39.600 align:start position:0%
well as retriever but for this part
 

00:01:39.600 --> 00:01:41.510 align:start position:0%
well as retriever but for this part
we'll<00:01:39.880><c> go</c><00:01:40.040><c> with</c><00:01:40.159><c> the</c><00:01:40.280><c> simple</c><00:01:40.880><c> implementation</c>

00:01:41.510 --> 00:01:41.520 align:start position:0%
we'll go with the simple implementation
 

00:01:41.520 --> 00:01:45.270 align:start position:0%
we'll go with the simple implementation
of<00:01:41.680><c> using</c><00:01:41.920><c> the</c><00:01:42.040><c> default</c><00:01:42.640><c> ones</c><00:01:43.640><c> so</c><00:01:44.439><c> uh</c><00:01:44.680><c> this</c>

00:01:45.270 --> 00:01:45.280 align:start position:0%
of using the default ones so uh this
 

00:01:45.280 --> 00:01:47.749 align:start position:0%
of using the default ones so uh this
creates<00:01:45.640><c> the</c><00:01:45.880><c> index</c><00:01:46.360><c> and</c><00:01:46.560><c> then</c><00:01:47.200><c> uh</c><00:01:47.399><c> basically</c>

00:01:47.749 --> 00:01:47.759 align:start position:0%
creates the index and then uh basically
 

00:01:47.759 --> 00:01:50.510 align:start position:0%
creates the index and then uh basically
it<00:01:47.880><c> passes</c><00:01:48.399><c> the</c><00:01:48.680><c> documents</c><00:01:49.079><c> into</c><00:01:49.360><c> noes</c><00:01:50.360><c> and</c>

00:01:50.510 --> 00:01:50.520 align:start position:0%
it passes the documents into noes and
 

00:01:50.520 --> 00:01:52.109 align:start position:0%
it passes the documents into noes and
once<00:01:50.680><c> you</c><00:01:50.840><c> have</c><00:01:51.040><c> the</c><00:01:51.119><c> notes</c><00:01:51.520><c> you</c><00:01:51.680><c> send</c><00:01:51.880><c> it</c><00:01:52.000><c> to</c>

00:01:52.109 --> 00:01:52.119 align:start position:0%
once you have the notes you send it to
 

00:01:52.119 --> 00:01:55.310 align:start position:0%
once you have the notes you send it to
llm<00:01:52.960><c> with</c><00:01:53.079><c> a</c><00:01:53.320><c> prompt</c><00:01:54.240><c> which</c><00:01:54.360><c> we'll</c><00:01:54.880><c> uh</c><00:01:55.000><c> see</c><00:01:55.240><c> in</c>

00:01:55.310 --> 00:01:55.320 align:start position:0%
llm with a prompt which we'll uh see in
 

00:01:55.320 --> 00:01:58.149 align:start position:0%
llm with a prompt which we'll uh see in
the<00:01:55.479><c> next</c><00:01:55.880><c> video</c><00:01:56.640><c> how</c><00:01:56.840><c> the</c><00:01:57.000><c> prompts</c><00:01:57.439><c> look</c><00:01:57.680><c> like</c>

00:01:58.149 --> 00:01:58.159 align:start position:0%
the next video how the prompts look like
 

00:01:58.159 --> 00:02:00.789 align:start position:0%
the next video how the prompts look like
and<00:01:58.320><c> how</c><00:01:58.479><c> you</c><00:01:58.600><c> can</c><00:01:58.759><c> customize</c><00:01:59.240><c> it</c><00:01:59.920><c> to</c><00:02:00.119><c> generate</c>

00:02:00.789 --> 00:02:00.799 align:start position:0%
and how you can customize it to generate
 

00:02:00.799 --> 00:02:02.870 align:start position:0%
and how you can customize it to generate
uh<00:02:01.119><c> these</c><00:02:01.360><c> parts</c><00:02:01.759><c> like</c><00:02:02.000><c> these</c><00:02:02.240><c> part</c><00:02:02.439><c> parts</c><00:02:02.680><c> are</c>

00:02:02.870 --> 00:02:02.880 align:start position:0%
uh these parts like these part parts are
 

00:02:02.880 --> 00:02:06.069 align:start position:0%
uh these parts like these part parts are
like<00:02:03.280><c> um</c><00:02:03.759><c> knowledge</c><00:02:04.119><c> graphs</c><00:02:04.560><c> triplets</c><00:02:05.560><c> right</c>

00:02:06.069 --> 00:02:06.079 align:start position:0%
like um knowledge graphs triplets right
 

00:02:06.079 --> 00:02:09.070 align:start position:0%
like um knowledge graphs triplets right
so<00:02:06.360><c> once</c><00:02:07.320><c> uh</c><00:02:07.439><c> you</c><00:02:07.640><c> have</c><00:02:08.039><c> the</c><00:02:08.280><c> triplets</c><00:02:08.879><c> you</c>

00:02:09.070 --> 00:02:09.080 align:start position:0%
so once uh you have the triplets you
 

00:02:09.080 --> 00:02:11.070 align:start position:0%
so once uh you have the triplets you
have<00:02:09.479><c> uh</c><00:02:09.679><c> implicit</c><00:02:10.360><c> you</c><00:02:10.479><c> can</c><00:02:10.640><c> extract</c><00:02:11.000><c> the</c>

00:02:11.070 --> 00:02:11.080 align:start position:0%
have uh implicit you can extract the
 

00:02:11.080 --> 00:02:14.309 align:start position:0%
have uh implicit you can extract the
implicit<00:02:11.520><c> Parts</c><00:02:11.840><c> as</c><00:02:12.000><c> well</c><00:02:12.599><c> uh</c><00:02:12.760><c> which</c><00:02:13.160><c> is</c><00:02:14.160><c> by</c>

00:02:14.309 --> 00:02:14.319 align:start position:0%
implicit Parts as well uh which is by
 

00:02:14.319 --> 00:02:16.030 align:start position:0%
implicit Parts as well uh which is by
using<00:02:14.599><c> node</c><00:02:14.920><c> relationships</c><00:02:15.599><c> which</c><00:02:15.720><c> you</c><00:02:15.920><c> have</c>

00:02:16.030 --> 00:02:16.040 align:start position:0%
using node relationships which you have
 

00:02:16.040 --> 00:02:17.670 align:start position:0%
using node relationships which you have
seen<00:02:16.640><c> in</c><00:02:16.720><c> the</c><00:02:16.879><c> previous</c><00:02:17.239><c> video</c><00:02:17.440><c> on</c><00:02:17.560><c> the</c>

00:02:17.670 --> 00:02:17.680 align:start position:0%
seen in the previous video on the
 

00:02:17.680 --> 00:02:20.110 align:start position:0%
seen in the previous video on the
introduction<00:02:18.120><c> to</c><00:02:18.319><c> property</c><00:02:18.720><c> graphs</c><00:02:19.360><c> and</c><00:02:19.560><c> then</c>

00:02:20.110 --> 00:02:20.120 align:start position:0%
introduction to property graphs and then
 

00:02:20.120 --> 00:02:22.390 align:start position:0%
introduction to property graphs and then
generate<00:02:20.480><c> the</c><00:02:20.760><c> embeddings</c><00:02:21.760><c> so</c><00:02:22.040><c> here</c><00:02:22.239><c> we</c>

00:02:22.390 --> 00:02:22.400 align:start position:0%
generate the embeddings so here we
 

00:02:22.400 --> 00:02:24.630 align:start position:0%
generate the embeddings so here we
create<00:02:22.720><c> embeddings</c><00:02:23.200><c> for</c><00:02:23.480><c> both</c><00:02:23.760><c> text</c><00:02:24.040><c> nodes</c><00:02:24.480><c> as</c>

00:02:24.630 --> 00:02:24.640 align:start position:0%
create embeddings for both text nodes as
 

00:02:24.640 --> 00:02:26.430 align:start position:0%
create embeddings for both text nodes as
well<00:02:24.800><c> as</c><00:02:25.160><c> uh</c><00:02:25.280><c> nodes</c><00:02:25.680><c> that's</c><00:02:25.800><c> the</c><00:02:25.959><c> reason</c><00:02:26.239><c> you</c>

00:02:26.430 --> 00:02:26.440 align:start position:0%
well as uh nodes that's the reason you
 

00:02:26.440 --> 00:02:28.750 align:start position:0%
well as uh nodes that's the reason you
see<00:02:27.120><c> uh</c><00:02:27.319><c> the</c><00:02:27.560><c> generating</c><00:02:28.040><c> embeddings</c><00:02:28.560><c> two</c>

00:02:28.750 --> 00:02:28.760 align:start position:0%
see uh the generating embeddings two
 

00:02:28.760 --> 00:02:33.390 align:start position:0%
see uh the generating embeddings two
times<00:02:29.640><c> okay</c><00:02:30.000><c> okay</c><00:02:30.720><c> yeah</c><00:02:31.599><c> and</c><00:02:31.879><c> then</c><00:02:32.400><c> once</c><00:02:33.080><c> uh</c>

00:02:33.390 --> 00:02:33.400 align:start position:0%
times okay okay yeah and then once uh
 

00:02:33.400 --> 00:02:35.869 align:start position:0%
times okay okay yeah and then once uh
this<00:02:33.519><c> is</c><00:02:33.680><c> done</c><00:02:33.920><c> you</c><00:02:34.000><c> can</c><00:02:34.200><c> save</c><00:02:34.519><c> the</c><00:02:34.640><c> index</c><00:02:35.440><c> and</c>

00:02:35.869 --> 00:02:35.879 align:start position:0%
this is done you can save the index and
 

00:02:35.879 --> 00:02:39.270 align:start position:0%
this is done you can save the index and
you<00:02:36.040><c> can</c><00:02:36.800><c> actually</c><00:02:37.120><c> view</c><00:02:37.519><c> it</c>

00:02:39.270 --> 00:02:39.280 align:start position:0%
you can actually view it
 

00:02:39.280 --> 00:02:40.990 align:start position:0%
you can actually view it
so<00:02:40.280><c> let</c>

00:02:40.990 --> 00:02:41.000 align:start position:0%
so let
 

00:02:41.000 --> 00:02:51.190 align:start position:0%
so let
me<00:02:42.000><c> open</c>

00:02:51.190 --> 00:02:51.200 align:start position:0%
 
 

00:02:51.200 --> 00:02:52.990 align:start position:0%
 
it

00:02:52.990 --> 00:02:53.000 align:start position:0%
it
 

00:02:53.000 --> 00:02:57.229 align:start position:0%
it
so<00:02:54.000><c> this</c><00:02:54.159><c> will</c><00:02:54.519><c> take</c><00:02:55.280><c> I</c><00:02:55.720><c> think</c><00:02:56.720><c> yeah</c><00:02:57.040><c> it's</c>

00:02:57.229 --> 00:02:57.239 align:start position:0%
so this will take I think yeah it's
 

00:02:57.239 --> 00:03:00.470 align:start position:0%
so this will take I think yeah it's
getting<00:02:57.519><c> loaded</c><00:02:58.319><c> so</c><00:02:58.560><c> this</c><00:02:58.680><c> is</c><00:02:59.080><c> the</c><00:02:59.480><c> graph</c>

00:03:00.470 --> 00:03:00.480 align:start position:0%
getting loaded so this is the graph
 

00:03:00.480 --> 00:03:03.070 align:start position:0%
getting loaded so this is the graph
Total<00:03:00.840><c> Property</c><00:03:01.280><c> graph</c><00:03:01.640><c> and</c><00:03:01.840><c> then</c><00:03:02.720><c> you</c><00:03:02.840><c> can</c>

00:03:03.070 --> 00:03:03.080 align:start position:0%
Total Property graph and then you can
 

00:03:03.080 --> 00:03:04.710 align:start position:0%
Total Property graph and then you can
actually<00:03:03.480><c> look</c><00:03:03.720><c> into</c>

00:03:04.710 --> 00:03:04.720 align:start position:0%
actually look into
 

00:03:04.720 --> 00:03:07.869 align:start position:0%
actually look into
it<00:03:05.720><c> like</c><00:03:05.879><c> your</c><00:03:06.120><c> different</c><00:03:06.440><c> noes</c><00:03:07.440><c> right</c><00:03:07.640><c> the</c>

00:03:07.869 --> 00:03:07.879 align:start position:0%
it like your different noes right the
 

00:03:07.879 --> 00:03:09.309 align:start position:0%
it like your different noes right the
speaker

00:03:09.309 --> 00:03:09.319 align:start position:0%
speaker
 

00:03:09.319 --> 00:03:13.830 align:start position:0%
speaker
author<00:03:10.799><c> then</c><00:03:11.799><c> right</c><00:03:12.280><c> you</c><00:03:12.440><c> can</c><00:03:12.720><c> actually</c>

00:03:13.830 --> 00:03:13.840 align:start position:0%
author then right you can actually
 

00:03:13.840 --> 00:03:19.190 align:start position:0%
author then right you can actually
polygram<00:03:14.840><c> so</c><00:03:15.239><c> you</c><00:03:15.360><c> can</c><00:03:15.760><c> explore</c><00:03:16.920><c> uh</c><00:03:17.920><c> the</c><00:03:18.239><c> YC</c>

00:03:19.190 --> 00:03:19.200 align:start position:0%
polygram so you can explore uh the YC
 

00:03:19.200 --> 00:03:23.509 align:start position:0%
polygram so you can explore uh the YC
explore<00:03:19.640><c> this</c><00:03:20.080><c> uh</c><00:03:20.799><c> uh</c><00:03:21.959><c> graph</c><00:03:22.959><c> um</c><00:03:23.200><c> once</c><00:03:23.400><c> you</c>

00:03:23.509 --> 00:03:23.519 align:start position:0%
explore this uh uh graph um once you
 

00:03:23.519 --> 00:03:27.070 align:start position:0%
explore this uh uh graph um once you
save<00:03:23.799><c> it</c><00:03:24.120><c> right</c><00:03:24.920><c> so</c><00:03:25.200><c> once</c><00:03:25.400><c> you</c><00:03:25.599><c> have</c><00:03:25.760><c> it</c><00:03:26.440><c> you</c>

00:03:27.070 --> 00:03:27.080 align:start position:0%
save it right so once you have it you
 

00:03:27.080 --> 00:03:28.910 align:start position:0%
save it right so once you have it you
basically<00:03:27.440><c> set</c><00:03:27.680><c> the</c><00:03:27.799><c> llm</c><00:03:28.280><c> and</c><00:03:28.480><c> embedding</c>

00:03:28.910 --> 00:03:28.920 align:start position:0%
basically set the llm and embedding
 

00:03:28.920 --> 00:03:30.670 align:start position:0%
basically set the llm and embedding
model<00:03:29.319><c> here</c>

00:03:30.670 --> 00:03:30.680 align:start position:0%
model here
 

00:03:30.680 --> 00:03:34.710 align:start position:0%
model here
and<00:03:31.159><c> uh</c><00:03:31.879><c> during</c><00:03:32.560><c> uh</c><00:03:32.680><c> once</c><00:03:33.280><c> uh</c><00:03:33.799><c> uh</c><00:03:34.360><c> property</c>

00:03:34.710 --> 00:03:34.720 align:start position:0%
and uh during uh once uh uh property
 

00:03:34.720 --> 00:03:36.390 align:start position:0%
and uh during uh once uh uh property
graph<00:03:35.000><c> is</c><00:03:35.159><c> constructed</c><00:03:35.840><c> you</c><00:03:36.040><c> get</c><00:03:36.159><c> into</c>

00:03:36.390 --> 00:03:36.400 align:start position:0%
graph is constructed you get into
 

00:03:36.400 --> 00:03:38.869 align:start position:0%
graph is constructed you get into
quering<00:03:36.760><c> Stage</c><00:03:37.159><c> right</c><00:03:37.760><c> so</c><00:03:37.920><c> the</c><00:03:38.040><c> default</c><00:03:38.400><c> one</c>

00:03:38.869 --> 00:03:38.879 align:start position:0%
quering Stage right so the default one
 

00:03:38.879 --> 00:03:42.070 align:start position:0%
quering Stage right so the default one
uh<00:03:39.000><c> uses</c><00:03:39.400><c> two</c><00:03:40.000><c> uh</c><00:03:40.439><c> types</c><00:03:40.760><c> of</c><00:03:41.080><c> retrieval</c><00:03:41.920><c> which</c>

00:03:42.070 --> 00:03:42.080 align:start position:0%
uh uses two uh types of retrieval which
 

00:03:42.080 --> 00:03:45.350 align:start position:0%
uh uses two uh types of retrieval which
is<00:03:42.439><c> synonym</c><00:03:43.000><c> or</c><00:03:43.360><c> keyword</c><00:03:44.360><c> extraction</c><00:03:44.879><c> from</c><00:03:45.239><c> uh</c>

00:03:45.350 --> 00:03:45.360 align:start position:0%
is synonym or keyword extraction from uh
 

00:03:45.360 --> 00:03:47.429 align:start position:0%
is synonym or keyword extraction from uh
from<00:03:45.480><c> the</c><00:03:45.640><c> query</c><00:03:45.959><c> using</c><00:03:46.200><c> llm</c><00:03:46.760><c> and</c><00:03:46.879><c> then</c><00:03:47.120><c> Vector</c>

00:03:47.429 --> 00:03:47.439 align:start position:0%
from the query using llm and then Vector
 

00:03:47.439 --> 00:03:50.710 align:start position:0%
from the query using llm and then Vector
retrieval<00:03:48.280><c> once</c><00:03:48.560><c> these</c><00:03:48.799><c> notes</c><00:03:49.599><c> are</c><00:03:49.760><c> retrieved</c>

00:03:50.710 --> 00:03:50.720 align:start position:0%
retrieval once these notes are retrieved
 

00:03:50.720 --> 00:03:53.550 align:start position:0%
retrieval once these notes are retrieved
yeah<00:03:51.280><c> um</c><00:03:51.840><c> you</c><00:03:51.959><c> can</c><00:03:52.200><c> actually</c><00:03:52.959><c> just</c><00:03:53.159><c> use</c><00:03:53.400><c> the</c>

00:03:53.550 --> 00:03:53.560 align:start position:0%
yeah um you can actually just use the
 

00:03:53.560 --> 00:03:55.470 align:start position:0%
yeah um you can actually just use the
tripat<00:03:54.040><c> retrieved</c><00:03:54.519><c> or</c><00:03:54.720><c> you</c><00:03:54.840><c> can</c><00:03:55.000><c> use</c><00:03:55.280><c> both</c>

00:03:55.470 --> 00:03:55.480 align:start position:0%
tripat retrieved or you can use both
 

00:03:55.480 --> 00:03:58.069 align:start position:0%
tripat retrieved or you can use both
tripletes<00:03:55.920><c> and</c><00:03:56.079><c> the</c><00:03:56.239><c> source</c><00:03:56.599><c> text</c><00:03:56.959><c> as</c><00:03:57.120><c> well</c><00:03:57.920><c> uh</c>

00:03:58.069 --> 00:03:58.079 align:start position:0%
tripletes and the source text as well uh
 

00:03:58.079 --> 00:04:00.710 align:start position:0%
tripletes and the source text as well uh
for<00:03:58.680><c> to</c><00:03:58.840><c> the</c><00:03:59.159><c> to</c><00:03:59.280><c> send</c><00:03:59.439><c> it</c><00:03:59.519><c> to</c><00:03:59.920><c> M</c><00:04:00.120><c> and</c><00:04:00.400><c> get</c><00:04:00.519><c> an</c>

00:04:00.710 --> 00:04:00.720 align:start position:0%
for to the to send it to M and get an
 

00:04:00.720 --> 00:04:04.350 align:start position:0%
for to the to send it to M and get an
answer<00:04:01.000><c> from</c><00:04:01.200><c> it</c><00:04:01.720><c> so</c><00:04:02.480><c> you</c><00:04:02.959><c> can</c><00:04:03.959><c> actually</c>

00:04:04.350 --> 00:04:04.360 align:start position:0%
answer from it so you can actually
 

00:04:04.360 --> 00:04:06.270 align:start position:0%
answer from it so you can actually
include<00:04:04.760><c> this</c><00:04:04.959><c> parameter</c><00:04:05.439><c> include</c><00:04:05.799><c> text</c>

00:04:06.270 --> 00:04:06.280 align:start position:0%
include this parameter include text
 

00:04:06.280 --> 00:04:09.509 align:start position:0%
include this parameter include text
equal<00:04:06.560><c> to</c><00:04:06.720><c> false</c><00:04:07.000><c> or</c><00:04:07.239><c> true</c><00:04:07.920><c> based</c><00:04:08.200><c> on</c><00:04:08.680><c> um</c>

00:04:09.509 --> 00:04:09.519 align:start position:0%
equal to false or true based on um
 

00:04:09.519 --> 00:04:11.550 align:start position:0%
equal to false or true based on um
whether<00:04:09.720><c> you</c><00:04:09.879><c> want</c><00:04:10.040><c> to</c><00:04:10.519><c> just</c><00:04:10.879><c> retrieve</c><00:04:11.360><c> the</c>

00:04:11.550 --> 00:04:11.560 align:start position:0%
whether you want to just retrieve the
 

00:04:11.560 --> 00:04:13.750 align:start position:0%
whether you want to just retrieve the
text<00:04:11.959><c> or</c><00:04:12.280><c> whether</c><00:04:12.519><c> you</c><00:04:12.799><c> just</c><00:04:12.959><c> want</c><00:04:13.159><c> to</c>

00:04:13.750 --> 00:04:13.760 align:start position:0%
text or whether you just want to
 

00:04:13.760 --> 00:04:16.030 align:start position:0%
text or whether you just want to
retrieve<00:04:14.239><c> triplets</c><00:04:14.760><c> sorry</c><00:04:15.319><c> just</c><00:04:15.480><c> retrieve</c>

00:04:16.030 --> 00:04:16.040 align:start position:0%
retrieve triplets sorry just retrieve
 

00:04:16.040 --> 00:04:18.150 align:start position:0%
retrieve triplets sorry just retrieve
triplets<00:04:16.720><c> or</c><00:04:17.239><c> whether</c><00:04:17.440><c> you</c><00:04:17.560><c> want</c><00:04:17.680><c> to</c><00:04:17.840><c> retrieve</c>

00:04:18.150 --> 00:04:18.160 align:start position:0%
triplets or whether you want to retrieve
 

00:04:18.160 --> 00:04:20.990 align:start position:0%
triplets or whether you want to retrieve
both<00:04:18.400><c> triplets</c><00:04:18.919><c> and</c><00:04:19.160><c> text</c><00:04:19.440><c> Source</c><00:04:19.840><c> text</c><00:04:20.720><c> so</c>

00:04:20.990 --> 00:04:21.000 align:start position:0%
both triplets and text Source text so
 

00:04:21.000 --> 00:04:23.230 align:start position:0%
both triplets and text Source text so
let's<00:04:21.600><c> uh</c><00:04:21.720><c> run</c><00:04:21.959><c> through</c><00:04:22.199><c> an</c><00:04:22.400><c> example</c><00:04:22.880><c> here</c>

00:04:23.230 --> 00:04:23.240 align:start position:0%
let's uh run through an example here
 

00:04:23.240 --> 00:04:26.830 align:start position:0%
let's uh run through an example here
what<00:04:23.400><c> happened</c><00:04:23.759><c> at</c><00:04:24.120><c> inter</c><00:04:24.440><c> leaf</c><00:04:24.720><c> and</c><00:04:25.120><c> web</c><00:04:25.840><c> web</c>

00:04:26.830 --> 00:04:26.840 align:start position:0%
what happened at inter leaf and web web
 

00:04:26.840 --> 00:04:28.950 align:start position:0%
what happened at inter leaf and web web
so<00:04:27.160><c> these</c><00:04:27.320><c> are</c><00:04:27.440><c> the</c><00:04:27.600><c> triplets</c><00:04:28.360><c> that</c><00:04:28.520><c> have</c><00:04:28.639><c> been</c>

00:04:28.950 --> 00:04:28.960 align:start position:0%
so these are the triplets that have been
 

00:04:28.960 --> 00:04:31.790 align:start position:0%
so these are the triplets that have been
uh<00:04:29.080><c> retrieved</c><00:04:29.479><c> for</c><00:04:29.600><c> the</c><00:04:29.759><c> the</c><00:04:29.919><c> given</c><00:04:30.400><c> uh</c><00:04:30.800><c> query</c>

00:04:31.790 --> 00:04:31.800 align:start position:0%
uh retrieved for the the given uh query
 

00:04:31.800 --> 00:04:33.590 align:start position:0%
uh retrieved for the the given uh query
right<00:04:32.360><c> so</c><00:04:32.520><c> you</c><00:04:32.639><c> can</c><00:04:32.800><c> experiment</c><00:04:33.280><c> different</c>

00:04:33.590 --> 00:04:33.600 align:start position:0%
right so you can experiment different
 

00:04:33.600 --> 00:04:37.230 align:start position:0%
right so you can experiment different
queries<00:04:34.199><c> and</c><00:04:34.919><c> then</c><00:04:35.919><c> uh</c><00:04:36.039><c> for</c><00:04:36.199><c> the</c><00:04:36.320><c> query</c><00:04:36.600><c> engine</c>

00:04:37.230 --> 00:04:37.240 align:start position:0%
queries and then uh for the query engine
 

00:04:37.240 --> 00:04:39.990 align:start position:0%
queries and then uh for the query engine
uh<00:04:37.360><c> you</c><00:04:37.440><c> can</c><00:04:37.840><c> have</c><00:04:38.479><c> uh</c><00:04:38.919><c> create</c><00:04:39.520><c> this</c><00:04:39.720><c> Square</c>

00:04:39.990 --> 00:04:40.000 align:start position:0%
uh you can have uh create this Square
 

00:04:40.000 --> 00:04:42.390 align:start position:0%
uh you can have uh create this Square
engine<00:04:40.280><c> index.</c><00:04:40.800><c> as</c><00:04:40.960><c> Square</c><00:04:41.199><c> engine</c><00:04:41.759><c> that</c><00:04:42.080><c> used</c>

00:04:42.390 --> 00:04:42.400 align:start position:0%
engine index. as Square engine that used
 

00:04:42.400 --> 00:04:46.070 align:start position:0%
engine index. as Square engine that used
we<00:04:42.520><c> used</c><00:04:42.680><c> to</c><00:04:42.840><c> do</c><00:04:43.440><c> with</c><00:04:44.440><c> rack</c><00:04:44.759><c> pipeline</c><00:04:45.160><c> as</c><00:04:45.320><c> well</c>

00:04:46.070 --> 00:04:46.080 align:start position:0%
we used to do with rack pipeline as well
 

00:04:46.080 --> 00:04:48.590 align:start position:0%
we used to do with rack pipeline as well
so<00:04:47.080><c> and</c><00:04:47.400><c> as</c><00:04:47.479><c> an</c><00:04:47.639><c> experiment</c><00:04:48.080><c> we'll</c><00:04:48.280><c> include</c>

00:04:48.590 --> 00:04:48.600 align:start position:0%
so and as an experiment we'll include
 

00:04:48.600 --> 00:04:50.710 align:start position:0%
so and as an experiment we'll include
the<00:04:48.759><c> text</c><00:04:49.280><c> along</c><00:04:49.560><c> with</c><00:04:49.800><c> the</c><00:04:50.160><c> source</c><00:04:50.479><c> text</c>

00:04:50.710 --> 00:04:50.720 align:start position:0%
the text along with the source text
 

00:04:50.720 --> 00:04:54.510 align:start position:0%
the text along with the source text
along<00:04:50.960><c> with</c><00:04:51.160><c> the</c><00:04:52.000><c> uh</c><00:04:52.160><c> triplets</c><00:04:53.199><c> so</c><00:04:54.199><c> then</c><00:04:54.400><c> we</c>

00:04:54.510 --> 00:04:54.520 align:start position:0%
along with the uh triplets so then we
 

00:04:54.520 --> 00:04:58.350 align:start position:0%
along with the uh triplets so then we
can<00:04:54.759><c> get</c><00:04:54.919><c> a</c><00:04:55.120><c> response</c>

00:04:58.350 --> 00:04:58.360 align:start position:0%
 
 

00:04:58.360 --> 00:05:01.830 align:start position:0%
 
accordingly<00:04:59.360><c> and</c>

00:05:01.830 --> 00:05:01.840 align:start position:0%
 
 

00:05:01.840 --> 00:05:05.510 align:start position:0%
 
yeah<00:05:02.440><c> so</c><00:05:02.960><c> interl</c><00:05:03.560><c> Pam</c><00:05:04.400><c> has</c><00:05:04.560><c> done</c><00:05:04.840><c> some</c><00:05:05.120><c> things</c>

00:05:05.510 --> 00:05:05.520 align:start position:0%
yeah so interl Pam has done some things
 

00:05:05.520 --> 00:05:08.590 align:start position:0%
yeah so interl Pam has done some things
and<00:05:06.360><c> uh</c><00:05:06.639><c> here</c><00:05:07.400><c> we</c><00:05:07.520><c> have</c><00:05:07.919><c> received</c><00:05:08.240><c> response</c>

00:05:08.590 --> 00:05:08.600 align:start position:0%
and uh here we have received response
 

00:05:08.600 --> 00:05:12.350 align:start position:0%
and uh here we have received response
for<00:05:08.880><c> other</c><00:05:09.680><c> stuff</c><00:05:10.039><c> as</c><00:05:10.199><c> well</c><00:05:10.880><c> so</c><00:05:11.759><c> yeah</c><00:05:12.000><c> so</c><00:05:12.240><c> this</c>

00:05:12.350 --> 00:05:12.360 align:start position:0%
for other stuff as well so yeah so this
 

00:05:12.360 --> 00:05:15.189 align:start position:0%
for other stuff as well so yeah so this
is<00:05:12.520><c> how</c><00:05:12.680><c> you</c><00:05:12.840><c> build</c><00:05:13.440><c> uh</c><00:05:14.240><c> uh</c><00:05:14.440><c> property</c><00:05:14.880><c> graph</c>

00:05:15.189 --> 00:05:15.199 align:start position:0%
is how you build uh uh property graph
 

00:05:15.199 --> 00:05:17.029 align:start position:0%
is how you build uh uh property graph
and<00:05:15.400><c> then</c><00:05:15.680><c> start</c><00:05:15.919><c> querying</c><00:05:16.280><c> as</c><00:05:16.360><c> a</c><00:05:16.479><c> retriever</c>

00:05:17.029 --> 00:05:17.039 align:start position:0%
and then start querying as a retriever
 

00:05:17.039 --> 00:05:19.790 align:start position:0%
and then start querying as a retriever
as<00:05:17.160><c> a</c><00:05:17.280><c> query</c><00:05:17.560><c> engine</c><00:05:18.280><c> and</c><00:05:18.479><c> get</c><00:05:19.080><c> uh</c><00:05:19.240><c> answers</c>

00:05:19.790 --> 00:05:19.800 align:start position:0%
as a query engine and get uh answers
 

00:05:19.800 --> 00:05:23.230 align:start position:0%
as a query engine and get uh answers
accordingly<00:05:20.800><c> and</c><00:05:20.960><c> then</c><00:05:21.440><c> uh</c><00:05:21.600><c> we</c><00:05:21.720><c> can</c><00:05:22.199><c> save</c><00:05:22.600><c> the</c>

00:05:23.230 --> 00:05:23.240 align:start position:0%
accordingly and then uh we can save the
 

00:05:23.240 --> 00:05:26.230 align:start position:0%
accordingly and then uh we can save the
uh<00:05:23.440><c> what</c><00:05:23.680><c> our</c><00:05:23.919><c> index</c><00:05:24.479><c> we</c><00:05:24.600><c> have</c><00:05:25.039><c> created</c><00:05:26.039><c> and</c>

00:05:26.230 --> 00:05:26.240 align:start position:0%
uh what our index we have created and
 

00:05:26.240 --> 00:05:30.469 align:start position:0%
uh what our index we have created and
load<00:05:26.639><c> the</c><00:05:27.039><c> index</c><00:05:28.039><c> and</c><00:05:28.680><c> uh</c>

00:05:30.469 --> 00:05:30.479 align:start position:0%
load the index and uh
 

00:05:30.479 --> 00:05:32.350 align:start position:0%
load the index and uh
uh<00:05:30.680><c> create</c><00:05:30.960><c> a</c><00:05:31.080><c> query</c><00:05:31.400><c> engine</c><00:05:31.840><c> and</c><00:05:32.000><c> run</c><00:05:32.240><c> the</c>

00:05:32.350 --> 00:05:32.360 align:start position:0%
uh create a query engine and run the
 

00:05:32.360 --> 00:05:34.870 align:start position:0%
uh create a query engine and run the
same<00:05:32.560><c> query</c><00:05:32.960><c> again</c>

00:05:34.870 --> 00:05:34.880 align:start position:0%
same query again
 

00:05:34.880 --> 00:05:37.629 align:start position:0%
same query again
so<00:05:35.880><c> here</c><00:05:36.080><c> is</c>

00:05:37.629 --> 00:05:37.639 align:start position:0%
so here is
 

00:05:37.639 --> 00:05:43.029 align:start position:0%
so here is
it<00:05:38.639><c> so</c><00:05:39.080><c> it</c><00:05:39.840><c> uh</c><00:05:40.840><c> saves</c><00:05:41.400><c> loads</c><00:05:42.120><c> and</c><00:05:42.319><c> then</c><00:05:42.919><c> uh</c>

00:05:43.029 --> 00:05:43.039 align:start position:0%
it so it uh saves loads and then uh
 

00:05:43.039 --> 00:05:46.029 align:start position:0%
it so it uh saves loads and then uh
creates<00:05:43.360><c> a</c><00:05:43.479><c> query</c><00:05:43.759><c> engine</c><00:05:44.000><c> on</c><00:05:44.160><c> top</c><00:05:44.319><c> of</c><00:05:44.479><c> it</c><00:05:45.440><c> yeah</c>

00:05:46.029 --> 00:05:46.039 align:start position:0%
creates a query engine on top of it yeah
 

00:05:46.039 --> 00:05:49.469 align:start position:0%
creates a query engine on top of it yeah
uh<00:05:46.199><c> we</c><00:05:46.360><c> got</c><00:05:46.680><c> the</c><00:05:47.039><c> same</c><00:05:47.280><c> answer</c><00:05:47.639><c> again</c><00:05:48.280><c> right</c><00:05:49.199><c> so</c>

00:05:49.469 --> 00:05:49.479 align:start position:0%
uh we got the same answer again right so
 

00:05:49.479 --> 00:05:51.510 align:start position:0%
uh we got the same answer again right so
this<00:05:49.600><c> is</c><00:05:49.759><c> how</c><00:05:50.000><c> we</c><00:05:50.120><c> can</c><00:05:50.319><c> build</c><00:05:50.639><c> index</c><00:05:51.000><c> and</c><00:05:51.240><c> save</c>

00:05:51.510 --> 00:05:51.520 align:start position:0%
this is how we can build index and save
 

00:05:51.520 --> 00:05:55.510 align:start position:0%
this is how we can build index and save
load<00:05:51.960><c> and</c><00:05:52.600><c> uh</c><00:05:52.720><c> create</c><00:05:53.000><c> the</c><00:05:53.479><c> same</c><00:05:54.479><c> index</c><00:05:54.960><c> again</c>

00:05:55.510 --> 00:05:55.520 align:start position:0%
load and uh create the same index again
 

00:05:55.520 --> 00:05:59.629 align:start position:0%
load and uh create the same index again
right<00:05:56.759><c> so</c><00:05:57.759><c> then</c><00:05:58.080><c> we'll</c><00:05:58.479><c> see</c><00:05:58.840><c> how</c><00:05:59.080><c> you</c><00:05:59.199><c> can</c><00:05:59.440><c> use</c>

00:05:59.629 --> 00:05:59.639 align:start position:0%
right so then we'll see how you can use
 

00:05:59.639 --> 00:06:01.550 align:start position:0%
right so then we'll see how you can use
use<00:05:59.800><c> a</c><00:05:59.919><c> vector</c><00:06:00.160><c> store</c><00:06:00.440><c> for</c><00:06:00.840><c> uh</c><00:06:00.960><c> storing</c><00:06:01.319><c> these</c>

00:06:01.550 --> 00:06:01.560 align:start position:0%
use a vector store for uh storing these
 

00:06:01.560 --> 00:06:04.909 align:start position:0%
use a vector store for uh storing these
embeddings<00:06:02.560><c> right</c><00:06:03.160><c> so</c><00:06:03.720><c> here</c><00:06:04.280><c> uh</c><00:06:04.400><c> we</c><00:06:04.520><c> are</c><00:06:04.639><c> using</c>

00:06:04.909 --> 00:06:04.919 align:start position:0%
embeddings right so here uh we are using
 

00:06:04.919 --> 00:06:07.230 align:start position:0%
embeddings right so here uh we are using
chroma<00:06:05.280><c> Vector</c><00:06:05.520><c> store</c><00:06:06.120><c> you</c><00:06:06.319><c> need</c><00:06:06.479><c> to</c><00:06:06.680><c> install</c>

00:06:07.230 --> 00:06:07.240 align:start position:0%
chroma Vector store you need to install
 

00:06:07.240 --> 00:06:11.110 align:start position:0%
chroma Vector store you need to install
Alex<00:06:07.880><c> Vector</c><00:06:08.120><c> stores</c><00:06:08.639><c> chroma</c><00:06:09.639><c> and</c><00:06:09.840><c> then</c><00:06:10.400><c> um</c>

00:06:11.110 --> 00:06:11.120 align:start position:0%
Alex Vector stores chroma and then um
 

00:06:11.120 --> 00:06:13.550 align:start position:0%
Alex Vector stores chroma and then um
yeah<00:06:11.720><c> create</c><00:06:11.960><c> a</c><00:06:12.080><c> collection</c><00:06:12.440><c> out</c><00:06:12.639><c> of</c><00:06:12.840><c> it</c><00:06:13.120><c> and</c>

00:06:13.550 --> 00:06:13.560 align:start position:0%
yeah create a collection out of it and
 

00:06:13.560 --> 00:06:16.469 align:start position:0%
yeah create a collection out of it and
so<00:06:13.960><c> while</c><00:06:14.160><c> building</c><00:06:14.639><c> property</c><00:06:15.039><c> graph</c><00:06:15.400><c> you</c><00:06:15.680><c> s</c>

00:06:16.469 --> 00:06:16.479 align:start position:0%
so while building property graph you s
 

00:06:16.479 --> 00:06:19.270 align:start position:0%
so while building property graph you s
documents<00:06:17.000><c> llm</c><00:06:17.440><c> embedding</c><00:06:17.840><c> model</c><00:06:18.680><c> what</c><00:06:18.800><c> is</c><00:06:18.960><c> a</c>

00:06:19.270 --> 00:06:19.280 align:start position:0%
documents llm embedding model what is a
 

00:06:19.280 --> 00:06:21.150 align:start position:0%
documents llm embedding model what is a
property<00:06:19.639><c> graph</c><00:06:19.919><c> store</c><00:06:20.639><c> which</c><00:06:20.759><c> is</c><00:06:20.880><c> simple</c>

00:06:21.150 --> 00:06:21.160 align:start position:0%
property graph store which is simple
 

00:06:21.160 --> 00:06:22.589 align:start position:0%
property graph store which is simple
property<00:06:21.520><c> graph</c><00:06:21.759><c> store</c><00:06:22.000><c> and</c><00:06:22.120><c> then</c><00:06:22.240><c> chroma</c>

00:06:22.589 --> 00:06:22.599 align:start position:0%
property graph store and then chroma
 

00:06:22.599 --> 00:06:25.550 align:start position:0%
property graph store and then chroma
Vector<00:06:22.880><c> store</c><00:06:23.800><c> right</c><00:06:24.080><c> and</c><00:06:24.240><c> then</c><00:06:24.800><c> say</c><00:06:25.160><c> purchase</c>

00:06:25.550 --> 00:06:25.560 align:start position:0%
Vector store right and then say purchase
 

00:06:25.560 --> 00:06:26.870 align:start position:0%
Vector store right and then say purchase
it

00:06:26.870 --> 00:06:26.880 align:start position:0%
it
 

00:06:26.880 --> 00:06:29.950 align:start position:0%
it
locally<00:06:27.880><c> so</c><00:06:28.360><c> this</c><00:06:28.479><c> will</c><00:06:28.680><c> create</c><00:06:29.400><c> a</c><00:06:29.599><c> extract</c>

00:06:29.950 --> 00:06:29.960 align:start position:0%
locally so this will create a extract
 

00:06:29.960 --> 00:06:32.029 align:start position:0%
locally so this will create a extract
the<00:06:30.160><c> parts</c><00:06:30.800><c> implicit</c><00:06:31.280><c> parts</c><00:06:31.720><c> and</c><00:06:31.840><c> then</c>

00:06:32.029 --> 00:06:32.039 align:start position:0%
the parts implicit parts and then
 

00:06:32.039 --> 00:06:34.350 align:start position:0%
the parts implicit parts and then
generate<00:06:32.800><c> embeddings</c><00:06:33.800><c> right</c><00:06:34.000><c> and</c><00:06:34.160><c> then</c>

00:06:34.350 --> 00:06:34.360 align:start position:0%
generate embeddings right and then
 

00:06:34.360 --> 00:06:39.150 align:start position:0%
generate embeddings right and then
create<00:06:34.680><c> the</c><00:06:35.440><c> uh</c><00:06:35.919><c> index</c><00:06:36.360><c> property</c><00:06:36.680><c> graph</c>

00:06:39.150 --> 00:06:39.160 align:start position:0%
 
 

00:06:39.160 --> 00:06:41.870 align:start position:0%
 
index<00:06:40.160><c> once</c><00:06:40.400><c> you</c><00:06:40.639><c> have</c><00:06:40.880><c> the</c><00:06:41.120><c> index</c><00:06:41.479><c> created</c>

00:06:41.870 --> 00:06:41.880 align:start position:0%
index once you have the index created
 

00:06:41.880 --> 00:06:46.150 align:start position:0%
index once you have the index created
and<00:06:42.080><c> saved</c><00:06:42.560><c> you</c><00:06:42.680><c> can</c><00:06:43.360><c> load</c><00:06:43.680><c> it</c><00:06:44.199><c> back</c><00:06:45.199><c> and</c><00:06:45.800><c> uh</c>

00:06:46.150 --> 00:06:46.160 align:start position:0%
and saved you can load it back and uh
 

00:06:46.160 --> 00:06:49.510 align:start position:0%
and saved you can load it back and uh
create<00:06:46.800><c> uh</c><00:06:47.280><c> air</c><00:06:47.759><c> engine</c><00:06:48.520><c> and</c><00:06:48.759><c> get</c><00:06:48.919><c> a</c><00:06:49.120><c> response</c>

00:06:49.510 --> 00:06:49.520 align:start position:0%
create uh air engine and get a response
 

00:06:49.520 --> 00:06:53.870 align:start position:0%
create uh air engine and get a response
for

00:06:53.870 --> 00:06:53.880 align:start position:0%
 
 

00:06:53.880 --> 00:06:57.150 align:start position:0%
 
it<00:06:54.880><c> yeah</c><00:06:55.080><c> it's</c><00:06:55.280><c> almost</c><00:06:55.680><c> done</c><00:06:56.599><c> uh</c><00:06:56.759><c> embedding</c>

00:06:57.150 --> 00:06:57.160 align:start position:0%
it yeah it's almost done uh embedding
 

00:06:57.160 --> 00:07:02.230 align:start position:0%
it yeah it's almost done uh embedding
generation<00:06:57.520><c> is</c><00:06:57.639><c> the</c><00:06:57.759><c> last</c><00:06:58.039><c> part</c>

00:07:02.230 --> 00:07:02.240 align:start position:0%
 
 

00:07:02.240 --> 00:07:07.550 align:start position:0%
 
so<00:07:03.599><c> yeah</c><00:07:04.599><c> this</c><00:07:04.759><c> is</c><00:07:04.919><c> done</c><00:07:05.479><c> so</c><00:07:06.280><c> let's</c><00:07:06.800><c> uh</c><00:07:07.160><c> load</c>

00:07:07.550 --> 00:07:07.560 align:start position:0%
so yeah this is done so let's uh load
 

00:07:07.560 --> 00:07:10.029 align:start position:0%
so yeah this is done so let's uh load
the<00:07:07.680><c> index</c><00:07:08.120><c> create</c><00:07:08.440><c> query</c><00:07:08.759><c> engine</c><00:07:09.520><c> and</c><00:07:09.879><c> what</c>

00:07:10.029 --> 00:07:10.039 align:start position:0%
the index create query engine and what
 

00:07:10.039 --> 00:07:12.430 align:start position:0%
the index create query engine and what
did<00:07:10.240><c> other</c><00:07:10.560><c> do</c><00:07:10.800><c> at</c><00:07:11.000><c> YC</c><00:07:11.520><c> is</c><00:07:11.680><c> the</c>

00:07:12.430 --> 00:07:12.440 align:start position:0%
did other do at YC is the
 

00:07:12.440 --> 00:07:15.990 align:start position:0%
did other do at YC is the
query<00:07:13.440><c> yeah</c><00:07:14.080><c> so</c><00:07:14.599><c> the</c><00:07:14.720><c> other</c><00:07:15.319><c> was</c><00:07:15.720><c> heavily</c>

00:07:15.990 --> 00:07:16.000 align:start position:0%
query yeah so the other was heavily
 

00:07:16.000 --> 00:07:19.990 align:start position:0%
query yeah so the other was heavily
involved<00:07:16.319><c> in</c><00:07:16.520><c> YC</c><00:07:17.400><c> and</c><00:07:18.319><c> where</c><00:07:19.080><c> other</c><00:07:19.400><c> tasks</c><00:07:19.800><c> he</c>

00:07:19.990 --> 00:07:20.000 align:start position:0%
involved in YC and where other tasks he
 

00:07:20.000 --> 00:07:22.990 align:start position:0%
involved in YC and where other tasks he
did<00:07:20.160><c> at</c><00:07:20.479><c> YC</c><00:07:21.479><c> so</c><00:07:21.680><c> this</c><00:07:21.800><c> is</c><00:07:22.000><c> how</c><00:07:22.440><c> uh</c><00:07:22.560><c> you</c><00:07:22.680><c> can</c>

00:07:22.990 --> 00:07:23.000 align:start position:0%
did at YC so this is how uh you can
 

00:07:23.000 --> 00:07:25.629 align:start position:0%
did at YC so this is how uh you can
create<00:07:23.240><c> a</c><00:07:23.400><c> property</c><00:07:23.840><c> graph</c><00:07:24.800><c> uh</c><00:07:25.000><c> construct</c><00:07:25.479><c> a</c>

00:07:25.629 --> 00:07:25.639 align:start position:0%
create a property graph uh construct a
 

00:07:25.639 --> 00:07:29.749 align:start position:0%
create a property graph uh construct a
property<00:07:26.039><c> graph</c><00:07:26.720><c> and</c><00:07:27.120><c> uh</c><00:07:27.560><c> retrieve</c><00:07:28.560><c> and</c><00:07:29.120><c> Quire</c>

00:07:29.749 --> 00:07:29.759 align:start position:0%
property graph and uh retrieve and Quire
 

00:07:29.759 --> 00:07:32.990 align:start position:0%
property graph and uh retrieve and Quire
it<00:07:30.280><c> and</c><00:07:30.440><c> then</c><00:07:30.599><c> store</c><00:07:30.960><c> it</c><00:07:31.840><c> and</c><00:07:32.080><c> load</c><00:07:32.360><c> it</c><00:07:32.599><c> again</c>

00:07:32.990 --> 00:07:33.000 align:start position:0%
it and then store it and load it again
 

00:07:33.000 --> 00:07:36.390 align:start position:0%
it and then store it and load it again
and<00:07:33.240><c> quy</c><00:07:33.560><c> it</c><00:07:33.919><c> again</c><00:07:34.919><c> and</c><00:07:35.360><c> even</c><00:07:35.720><c> you</c><00:07:35.840><c> can</c><00:07:36.000><c> use</c><00:07:36.199><c> a</c>

00:07:36.390 --> 00:07:36.400 align:start position:0%
and quy it again and even you can use a
 

00:07:36.400 --> 00:07:38.710 align:start position:0%
and quy it again and even you can use a
vector<00:07:36.639><c> store</c><00:07:37.039><c> as</c><00:07:37.160><c> well</c><00:07:37.720><c> to</c><00:07:38.400><c> uh</c><00:07:38.520><c> while</c>

00:07:38.710 --> 00:07:38.720 align:start position:0%
vector store as well to uh while
 

00:07:38.720 --> 00:07:40.990 align:start position:0%
vector store as well to uh while
creating<00:07:39.400><c> property</c><00:07:39.759><c> graph</c><00:07:40.000><c> index</c><00:07:40.639><c> and</c><00:07:40.759><c> load</c>

00:07:40.990 --> 00:07:41.000 align:start position:0%
creating property graph index and load
 

00:07:41.000 --> 00:07:44.510 align:start position:0%
creating property graph index and load
it<00:07:41.240><c> back</c><00:07:41.520><c> again</c><00:07:41.840><c> and</c><00:07:42.319><c> start</c><00:07:43.159><c> querying</c><00:07:44.159><c> so</c>

00:07:44.510 --> 00:07:44.520 align:start position:0%
it back again and start querying so
 

00:07:44.520 --> 00:07:46.830 align:start position:0%
it back again and start querying so
that's<00:07:44.680><c> all</c><00:07:44.840><c> I</c><00:07:44.960><c> have</c><00:07:45.080><c> for</c><00:07:45.240><c> this</c><00:07:45.479><c> video</c><00:07:46.400><c> uh</c><00:07:46.639><c> see</c>

00:07:46.830 --> 00:07:46.840 align:start position:0%
that's all I have for this video uh see
 

00:07:46.840 --> 00:07:49.270 align:start position:0%
that's all I have for this video uh see
you<00:07:46.960><c> in</c><00:07:47.080><c> the</c><00:07:47.319><c> next</c><00:07:47.599><c> video</c><00:07:48.000><c> with</c><00:07:48.240><c> different</c>

00:07:49.270 --> 00:07:49.280 align:start position:0%
you in the next video with different
 

00:07:49.280 --> 00:07:53.479 align:start position:0%
you in the next video with different
tutorial<00:07:50.280><c> thank</c><00:07:50.479><c> you</c>

