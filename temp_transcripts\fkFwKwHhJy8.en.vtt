WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.060 align:start position:0%
 
hey<00:00:00.359><c> and</c><00:00:00.719><c> welcome</c><00:00:00.870><c> to</c><00:00:01.110><c> another</c><00:00:01.350><c> HTML</c><00:00:01.829><c> video</c>

00:00:02.060 --> 00:00:02.070 align:start position:0%
hey and welcome to another HTML video
 

00:00:02.070 --> 00:00:03.710 align:start position:0%
hey and welcome to another HTML video
and<00:00:02.460><c> this</c><00:00:02.939><c> time</c><00:00:03.120><c> we're</c><00:00:03.360><c> going</c><00:00:03.449><c> to</c><00:00:03.540><c> tackle</c>

00:00:03.710 --> 00:00:03.720 align:start position:0%
and this time we're going to tackle
 

00:00:03.720 --> 00:00:05.869 align:start position:0%
and this time we're going to tackle
forms<00:00:04.200><c> before</c><00:00:05.130><c> we</c><00:00:05.220><c> get</c><00:00:05.339><c> in</c><00:00:05.460><c> the</c><00:00:05.520><c> coding</c>

00:00:05.869 --> 00:00:05.879 align:start position:0%
forms before we get in the coding
 

00:00:05.879 --> 00:00:08.150 align:start position:0%
forms before we get in the coding
anything<00:00:06.180><c> we</c><00:00:06.540><c> need</c><00:00:06.660><c> to</c><00:00:06.750><c> understand</c><00:00:07.200><c> how</c><00:00:07.620><c> forms</c>

00:00:08.150 --> 00:00:08.160 align:start position:0%
anything we need to understand how forms
 

00:00:08.160 --> 00:00:12.980 align:start position:0%
anything we need to understand how forms
work<00:00:08.700><c> so</c><00:00:09.690><c> what</c><00:00:10.349><c> is</c><00:00:10.440><c> the</c><00:00:10.500><c> idea</c><00:00:10.769><c> of</c><00:00:10.830><c> a</c><00:00:10.980><c> form</c><00:00:11.330><c> so</c><00:00:12.330><c> if</c>

00:00:12.980 --> 00:00:12.990 align:start position:0%
work so what is the idea of a form so if
 

00:00:12.990 --> 00:00:15.200 align:start position:0%
work so what is the idea of a form so if
you<00:00:13.110><c> take</c><00:00:13.370><c> if</c><00:00:14.370><c> you</c><00:00:14.490><c> did</c><00:00:14.639><c> like</c><00:00:14.700><c> a</c><00:00:14.820><c> doctor's</c>

00:00:15.200 --> 00:00:15.210 align:start position:0%
you take if you did like a doctor's
 

00:00:15.210 --> 00:00:17.779 align:start position:0%
you take if you did like a doctor's
office<00:00:15.360><c> for</c><00:00:15.750><c> the</c><00:00:16.080><c> first</c><00:00:16.109><c> time</c><00:00:16.440><c> you</c><00:00:17.130><c> need</c><00:00:17.730><c> to</c>

00:00:17.779 --> 00:00:17.789 align:start position:0%
office for the first time you need to
 

00:00:17.789 --> 00:00:21.290 align:start position:0%
office for the first time you need to
fill<00:00:18.029><c> out</c><00:00:18.180><c> like</c><00:00:18.359><c> a</c><00:00:18.830><c> huge</c><00:00:19.830><c> application</c><00:00:20.300><c> put</c>

00:00:21.290 --> 00:00:21.300 align:start position:0%
fill out like a huge application put
 

00:00:21.300 --> 00:00:24.170 align:start position:0%
fill out like a huge application put
your<00:00:21.420><c> name</c><00:00:21.630><c> any</c><00:00:22.140><c> past</c><00:00:22.890><c> illnesses</c><00:00:23.400><c> are</c><00:00:24.090><c> you</c>

00:00:24.170 --> 00:00:24.180 align:start position:0%
your name any past illnesses are you
 

00:00:24.180 --> 00:00:26.000 align:start position:0%
your name any past illnesses are you
allergic<00:00:24.330><c> to</c><00:00:24.539><c> anything</c><00:00:24.720><c> blah</c><00:00:25.439><c> blah</c><00:00:25.590><c> blah</c><00:00:25.800><c> and</c>

00:00:26.000 --> 00:00:26.010 align:start position:0%
allergic to anything blah blah blah and
 

00:00:26.010 --> 00:00:28.849 align:start position:0%
allergic to anything blah blah blah and
then<00:00:26.699><c> when</c><00:00:27.510><c> you're</c><00:00:27.599><c> done</c><00:00:27.750><c> you</c><00:00:28.230><c> take</c><00:00:28.470><c> that</c><00:00:28.590><c> form</c>

00:00:28.849 --> 00:00:28.859 align:start position:0%
then when you're done you take that form
 

00:00:28.859 --> 00:00:31.519 align:start position:0%
then when you're done you take that form
give<00:00:29.279><c> it</c><00:00:29.400><c> to</c><00:00:29.490><c> receptionist</c><00:00:30.029><c> and</c><00:00:30.179><c> then</c><00:00:30.720><c> they</c>

00:00:31.519 --> 00:00:31.529 align:start position:0%
give it to receptionist and then they
 

00:00:31.529 --> 00:00:35.180 align:start position:0%
give it to receptionist and then they
use<00:00:31.859><c> that</c><00:00:31.980><c> information</c><00:00:32.279><c> to</c><00:00:32.989><c> diagnose</c><00:00:33.989><c> and</c><00:00:34.800><c> for</c>

00:00:35.180 --> 00:00:35.190 align:start position:0%
use that information to diagnose and for
 

00:00:35.190 --> 00:00:37.910 align:start position:0%
use that information to diagnose and for
future<00:00:35.750><c> future</c><00:00:36.750><c> times</c><00:00:37.050><c> when</c><00:00:37.380><c> you're</c><00:00:37.500><c> there</c><00:00:37.649><c> so</c>

00:00:37.910 --> 00:00:37.920 align:start position:0%
future future times when you're there so
 

00:00:37.920 --> 00:00:39.950 align:start position:0%
future future times when you're there so
they'll<00:00:38.070><c> have</c><00:00:38.100><c> their</c><00:00:38.309><c> ready</c><00:00:38.780><c> so</c><00:00:39.780><c> that</c><00:00:39.899><c> you</c>

00:00:39.950 --> 00:00:39.960 align:start position:0%
they'll have their ready so that you
 

00:00:39.960 --> 00:00:43.760 align:start position:0%
they'll have their ready so that you
want<00:00:40.050><c> to</c><00:00:40.170><c> fill</c><00:00:40.320><c> out</c><00:00:40.469><c> every</c><00:00:40.950><c> time</c><00:00:41.270><c> so</c><00:00:42.739><c> the</c><00:00:43.739><c> whole</c>

00:00:43.760 --> 00:00:43.770 align:start position:0%
want to fill out every time so the whole
 

00:00:43.770 --> 00:00:48.410 align:start position:0%
want to fill out every time so the whole
idea<00:00:43.920><c> is</c><00:00:44.309><c> to</c><00:00:44.690><c> collect</c><00:00:45.770><c> data</c><00:00:46.770><c> from</c><00:00:47.430><c> a</c><00:00:47.850><c> visitor</c>

00:00:48.410 --> 00:00:48.420 align:start position:0%
idea is to collect data from a visitor
 

00:00:48.420 --> 00:00:50.840 align:start position:0%
idea is to collect data from a visitor
so<00:00:48.960><c> we</c><00:00:49.079><c> take</c><00:00:49.289><c> this</c><00:00:49.440><c> concept</c><00:00:50.010><c> put</c><00:00:50.489><c> it</c><00:00:50.610><c> into</c><00:00:50.820><c> a</c>

00:00:50.840 --> 00:00:50.850 align:start position:0%
so we take this concept put it into a
 

00:00:50.850 --> 00:00:53.209 align:start position:0%
so we take this concept put it into a
web<00:00:51.090><c> page</c><00:00:51.329><c> and</c><00:00:51.660><c> you</c><00:00:52.079><c> fill</c><00:00:52.320><c> it</c><00:00:52.440><c> information</c><00:00:53.010><c> and</c>

00:00:53.209 --> 00:00:53.219 align:start position:0%
web page and you fill it information and
 

00:00:53.219 --> 00:00:55.279 align:start position:0%
web page and you fill it information and
then<00:00:53.399><c> now</c><00:00:53.969><c> that</c><00:00:54.180><c> website</c><00:00:54.719><c> has</c><00:00:54.930><c> information</c>

00:00:55.279 --> 00:00:55.289 align:start position:0%
then now that website has information
 

00:00:55.289 --> 00:00:59.000 align:start position:0%
then now that website has information
about<00:00:55.559><c> you</c><00:00:55.890><c> if</c><00:00:56.010><c> such</c><00:00:56.219><c> as</c><00:00:56.430><c> when</c><00:00:57.270><c> you</c><00:00:57.390><c> log</c><00:00:57.989><c> in</c><00:00:58.230><c> do</c>

00:00:59.000 --> 00:00:59.010 align:start position:0%
about you if such as when you log in do
 

00:00:59.010 --> 00:01:00.380 align:start position:0%
about you if such as when you log in do
you<00:00:59.100><c> sign</c><00:00:59.280><c> up</c><00:00:59.430><c> for</c><00:00:59.520><c> the</c><00:00:59.609><c> first</c><00:00:59.730><c> time</c><00:01:00.030><c> and</c><00:01:00.300><c> then</c>

00:01:00.380 --> 00:01:00.390 align:start position:0%
you sign up for the first time and then
 

00:01:00.390 --> 00:01:01.819 align:start position:0%
you sign up for the first time and then
now<00:01:00.629><c> all</c><00:01:00.660><c> I</c><00:01:00.780><c> need</c><00:01:01.020><c> to</c><00:01:01.109><c> do</c><00:01:01.199><c> is</c><00:01:01.410><c> put</c><00:01:01.649><c> in</c><00:01:01.710><c> your</c>

00:01:01.819 --> 00:01:01.829 align:start position:0%
now all I need to do is put in your
 

00:01:01.829 --> 00:01:03.950 align:start position:0%
now all I need to do is put in your
email<00:01:02.010><c> and</c><00:01:02.510><c> your</c><00:01:03.510><c> password</c>

00:01:03.950 --> 00:01:03.960 align:start position:0%
email and your password
 

00:01:03.960 --> 00:01:07.070 align:start position:0%
email and your password
it'll<00:01:04.920><c> look</c><00:01:05.040><c> it</c><00:01:05.189><c> up</c><00:01:05.280><c> and</c><00:01:05.540><c> see</c><00:01:06.540><c> if</c><00:01:06.689><c> that's</c>

00:01:07.070 --> 00:01:07.080 align:start position:0%
it'll look it up and see if that's
 

00:01:07.080 --> 00:01:08.870 align:start position:0%
it'll look it up and see if that's
correct<00:01:07.380><c> and</c><00:01:07.619><c> then</c><00:01:08.280><c> you're</c><00:01:08.430><c> in</c><00:01:08.580><c> you</c><00:01:08.850><c> don't</c>

00:01:08.870 --> 00:01:08.880 align:start position:0%
correct and then you're in you don't
 

00:01:08.880 --> 00:01:11.480 align:start position:0%
correct and then you're in you don't
have<00:01:09.270><c> to</c><00:01:09.510><c> sign</c><00:01:10.110><c> up</c><00:01:10.170><c> every</c><00:01:10.860><c> time</c><00:01:11.040><c> you</c><00:01:11.100><c> visit</c><00:01:11.460><c> the</c>

00:01:11.480 --> 00:01:11.490 align:start position:0%
have to sign up every time you visit the
 

00:01:11.490 --> 00:01:16.460 align:start position:0%
have to sign up every time you visit the
website<00:01:12.950><c> so</c><00:01:13.950><c> there's</c><00:01:14.100><c> other</c><00:01:14.450><c> other</c><00:01:15.470><c> ideas</c>

00:01:16.460 --> 00:01:16.470 align:start position:0%
website so there's other other ideas
 

00:01:16.470 --> 00:01:17.719 align:start position:0%
website so there's other other ideas
it's<00:01:16.619><c> not</c><00:01:16.740><c> just</c><00:01:16.920><c> about</c><00:01:16.979><c> signing</c><00:01:17.430><c> up</c><00:01:17.549><c> and</c>

00:01:17.719 --> 00:01:17.729 align:start position:0%
it's not just about signing up and
 

00:01:17.729 --> 00:01:19.700 align:start position:0%
it's not just about signing up and
logging<00:01:18.000><c> in</c><00:01:18.150><c> but</c><00:01:18.720><c> you</c><00:01:19.350><c> know</c><00:01:19.470><c> it</c><00:01:19.530><c> could</c><00:01:19.650><c> be</c>

00:01:19.700 --> 00:01:19.710 align:start position:0%
logging in but you know it could be
 

00:01:19.710 --> 00:01:22.820 align:start position:0%
logging in but you know it could be
online<00:01:20.600><c> applications</c><00:01:21.600><c> and</c><00:01:21.740><c> probably</c><00:01:22.740><c> the</c>

00:01:22.820 --> 00:01:22.830 align:start position:0%
online applications and probably the
 

00:01:22.830 --> 00:01:26.780 align:start position:0%
online applications and probably the
most<00:01:23.159><c> popular</c><00:01:24.500><c> form</c><00:01:25.500><c> is</c><00:01:25.860><c> the</c><00:01:26.369><c> Google</c><00:01:26.610><c> search</c>

00:01:26.780 --> 00:01:26.790 align:start position:0%
most popular form is the Google search
 

00:01:26.790 --> 00:01:30.289 align:start position:0%
most popular form is the Google search
bar<00:01:27.030><c> that's</c><00:01:27.720><c> a</c><00:01:27.869><c> form</c><00:01:28.369><c> you</c><00:01:29.369><c> just</c><00:01:29.400><c> it's</c><00:01:29.939><c> very</c>

00:01:30.289 --> 00:01:30.299 align:start position:0%
bar that's a form you just it's very
 

00:01:30.299 --> 00:01:31.640 align:start position:0%
bar that's a form you just it's very
simple<00:01:30.509><c> just</c><00:01:30.600><c> in</c><00:01:30.810><c> the</c><00:01:30.869><c> real</c><00:01:31.020><c> screen</c><00:01:31.320><c> you</c>

00:01:31.640 --> 00:01:31.650 align:start position:0%
simple just in the real screen you
 

00:01:31.650 --> 00:01:33.859 align:start position:0%
simple just in the real screen you
search<00:01:31.950><c> and</c><00:01:32.310><c> then</c><00:01:32.850><c> it</c><00:01:32.939><c> retrieves</c><00:01:33.360><c> data</c><00:01:33.479><c> for</c>

00:01:33.859 --> 00:01:33.869 align:start position:0%
search and then it retrieves data for
 

00:01:33.869 --> 00:01:37.219 align:start position:0%
search and then it retrieves data for
you<00:01:34.100><c> that's</c><00:01:35.100><c> an</c><00:01:35.280><c> example</c><00:01:35.490><c> of</c><00:01:35.729><c> a</c><00:01:35.790><c> form</c><00:01:36.079><c> so</c><00:01:37.079><c> let's</c>

00:01:37.219 --> 00:01:37.229 align:start position:0%
you that's an example of a form so let's
 

00:01:37.229 --> 00:01:39.350 align:start position:0%
you that's an example of a form so let's
go<00:01:37.350><c> over</c><00:01:37.590><c> the</c><00:01:37.890><c> end</c><00:01:38.159><c> end</c><00:01:38.430><c> process</c><00:01:38.939><c> when</c><00:01:39.270><c> you</c>

00:01:39.350 --> 00:01:39.360 align:start position:0%
go over the end end process when you
 

00:01:39.360 --> 00:01:41.359 align:start position:0%
go over the end end process when you
visit<00:01:39.600><c> a</c><00:01:39.630><c> website</c><00:01:39.689><c> and</c><00:01:40.259><c> then</c><00:01:40.409><c> what</c><00:01:41.070><c> happens</c>

00:01:41.359 --> 00:01:41.369 align:start position:0%
visit a website and then what happens
 

00:01:41.369 --> 00:01:43.670 align:start position:0%
visit a website and then what happens
after<00:01:42.020><c> you've</c><00:01:43.020><c> finished</c><00:01:43.290><c> filling</c><00:01:43.350><c> out</c><00:01:43.590><c> the</c>

00:01:43.670 --> 00:01:43.680 align:start position:0%
after you've finished filling out the
 

00:01:43.680 --> 00:01:45.380 align:start position:0%
after you've finished filling out the
form<00:01:43.920><c> and</c><00:01:44.100><c> hit</c><00:01:44.970><c> submit</c>

00:01:45.380 --> 00:01:45.390 align:start position:0%
form and hit submit
 

00:01:45.390 --> 00:01:48.980 align:start position:0%
form and hit submit
so<00:01:46.460><c> this</c><00:01:47.460><c> this</c><00:01:47.759><c> diagram</c><00:01:48.090><c> is</c><00:01:48.149><c> taken</c><00:01:48.390><c> from</c><00:01:48.570><c> John</c>

00:01:48.980 --> 00:01:48.990 align:start position:0%
so this this diagram is taken from John
 

00:01:48.990 --> 00:01:50.749 align:start position:0%
so this this diagram is taken from John
Doggett's<00:01:49.380><c> book</c><00:01:49.530><c> when</c><00:01:49.740><c> HTML</c><00:01:50.130><c> and</c><00:01:50.220><c> CSS</c><00:01:50.310><c> which</c>

00:01:50.749 --> 00:01:50.759 align:start position:0%
Doggett's book when HTML and CSS which
 

00:01:50.759 --> 00:01:52.850 align:start position:0%
Doggett's book when HTML and CSS which
will<00:01:50.909><c> have</c><00:01:51.060><c> a</c><00:01:51.090><c> description</c><00:01:51.470><c> but</c><00:01:52.470><c> it's</c><00:01:52.710><c> a</c>

00:01:52.850 --> 00:01:52.860 align:start position:0%
will have a description but it's a
 

00:01:52.860 --> 00:01:56.600 align:start position:0%
will have a description but it's a
simple<00:01:53.310><c> example</c><00:01:53.610><c> as</c><00:01:54.149><c> to</c><00:01:54.420><c> how</c><00:01:54.770><c> form</c><00:01:55.770><c> works</c><00:01:55.950><c> so</c>

00:01:56.600 --> 00:01:56.610 align:start position:0%
simple example as to how form works so
 

00:01:56.610 --> 00:01:59.030 align:start position:0%
simple example as to how form works so
you'll<00:01:57.060><c> go</c><00:01:57.210><c> in</c><00:01:57.420><c> in</c><00:01:57.630><c> this</c><00:01:58.170><c> example</c><00:01:58.439><c> you're</c>

00:01:59.030 --> 00:01:59.040 align:start position:0%
you'll go in in this example you're
 

00:01:59.040 --> 00:02:00.560 align:start position:0%
you'll go in in this example you're
gonna<00:01:59.219><c> put</c><00:01:59.549><c> in</c><00:01:59.700><c> a</c><00:01:59.759><c> username</c><00:02:00.000><c> and</c><00:02:00.299><c> you're</c><00:02:00.450><c> gonna</c>

00:02:00.560 --> 00:02:00.570 align:start position:0%
gonna put in a username and you're gonna
 

00:02:00.570 --> 00:02:03.709 align:start position:0%
gonna put in a username and you're gonna
select<00:02:00.840><c> one</c><00:02:01.259><c> of</c><00:02:01.290><c> the</c><00:02:01.439><c> five</c><00:02:01.649><c> radio</c><00:02:02.040><c> buttons</c><00:02:02.719><c> now</c>

00:02:03.709 --> 00:02:03.719 align:start position:0%
select one of the five radio buttons now
 

00:02:03.719 --> 00:02:04.969 align:start position:0%
select one of the five radio buttons now
in<00:02:03.810><c> the</c><00:02:03.869><c> next</c><00:02:04.020><c> forms</c><00:02:04.380><c> video</c><00:02:04.590><c> I'll</c><00:02:04.770><c> kind</c><00:02:04.920><c> of</c>

00:02:04.969 --> 00:02:04.979 align:start position:0%
in the next forms video I'll kind of
 

00:02:04.979 --> 00:02:07.399 align:start position:0%
in the next forms video I'll kind of
explain<00:02:05.659><c> step</c><00:02:06.659><c> number</c><00:02:06.780><c> two</c><00:02:07.049><c> where</c><00:02:07.229><c> talks</c>

00:02:07.399 --> 00:02:07.409 align:start position:0%
explain step number two where talks
 

00:02:07.409 --> 00:02:10.729 align:start position:0%
explain step number two where talks
about<00:02:07.619><c> name</c><00:02:08.009><c> and</c><00:02:08.250><c> value</c><00:02:08.599><c> but</c><00:02:09.599><c> essentially</c><00:02:10.110><c> the</c>

00:02:10.729 --> 00:02:10.739 align:start position:0%
about name and value but essentially the
 

00:02:10.739 --> 00:02:12.770 align:start position:0%
about name and value but essentially the
username<00:02:11.129><c> and</c><00:02:11.400><c> the</c><00:02:11.520><c> I</c><00:02:11.550><c> vote</c><00:02:11.849><c> for</c><00:02:12.120><c> those</c><00:02:12.629><c> are</c>

00:02:12.770 --> 00:02:12.780 align:start position:0%
username and the I vote for those are
 

00:02:12.780 --> 00:02:13.880 align:start position:0%
username and the I vote for those are
each<00:02:12.900><c> called</c><00:02:13.110><c> form</c>

00:02:13.880 --> 00:02:13.890 align:start position:0%
each called form
 

00:02:13.890 --> 00:02:18.260 align:start position:0%
each called form
and<00:02:14.660><c> whenever</c><00:02:15.660><c> you</c><00:02:16.620><c> code</c><00:02:17.160><c> in</c><00:02:17.490><c> HTML</c><00:02:17.610><c> you</c><00:02:18.150><c> have</c>

00:02:18.260 --> 00:02:18.270 align:start position:0%
and whenever you code in HTML you have
 

00:02:18.270 --> 00:02:20.300 align:start position:0%
and whenever you code in HTML you have
to<00:02:18.390><c> give</c><00:02:18.540><c> it</c><00:02:18.630><c> a</c><00:02:18.690><c> name</c><00:02:18.900><c> attribute</c><00:02:19.170><c> each</c><00:02:20.040><c> of</c>

00:02:20.300 --> 00:02:20.310 align:start position:0%
to give it a name attribute each of
 

00:02:20.310 --> 00:02:22.640 align:start position:0%
to give it a name attribute each of
those<00:02:20.430><c> and</c><00:02:20.700><c> then</c><00:02:21.240><c> so</c><00:02:21.420><c> the</c><00:02:21.630><c> name</c><00:02:21.870><c> attribute</c><00:02:22.140><c> for</c>

00:02:22.640 --> 00:02:22.650 align:start position:0%
those and then so the name attribute for
 

00:02:22.650 --> 00:02:24.800 align:start position:0%
those and then so the name attribute for
username<00:02:22.950><c> and</c><00:02:23.130><c> the</c><00:02:23.700><c> I</c><00:02:23.730><c> vote</c><00:02:24.030><c> for</c><00:02:24.240><c> radio</c><00:02:24.510><c> button</c>

00:02:24.800 --> 00:02:24.810 align:start position:0%
username and the I vote for radio button
 

00:02:24.810 --> 00:02:28.340 align:start position:0%
username and the I vote for radio button
and<00:02:25.370><c> the</c><00:02:26.370><c> value</c><00:02:26.850><c> that</c><00:02:26.880><c> you</c><00:02:27.270><c> select</c><00:02:27.660><c> input</c><00:02:28.080><c> or</c>

00:02:28.340 --> 00:02:28.350 align:start position:0%
and the value that you select input or
 

00:02:28.350 --> 00:02:32.000 align:start position:0%
and the value that you select input or
select<00:02:29.330><c> those</c><00:02:30.330><c> are</c><00:02:30.660><c> pairs</c><00:02:31.290><c> so</c><00:02:31.620><c> it's</c><00:02:31.770><c> gonna</c><00:02:31.890><c> be</c>

00:02:32.000 --> 00:02:32.010 align:start position:0%
select those are pairs so it's gonna be
 

00:02:32.010 --> 00:02:33.770 align:start position:0%
select those are pairs so it's gonna be
the<00:02:32.100><c> name</c><00:02:32.310><c> attribute</c><00:02:32.550><c> and</c><00:02:33.030><c> the</c><00:02:33.090><c> value</c><00:02:33.510><c> that</c>

00:02:33.770 --> 00:02:33.780 align:start position:0%
the name attribute and the value that
 

00:02:33.780 --> 00:02:36.170 align:start position:0%
the name attribute and the value that
you<00:02:34.410><c> entered</c><00:02:34.830><c> and</c><00:02:35.100><c> there's</c><00:02:35.580><c> going</c><00:02:35.700><c> to</c><00:02:35.760><c> be</c><00:02:35.850><c> sent</c>

00:02:36.170 --> 00:02:36.180 align:start position:0%
you entered and there's going to be sent
 

00:02:36.180 --> 00:02:39.110 align:start position:0%
you entered and there's going to be sent
over<00:02:36.570><c> to</c><00:02:37.020><c> what's</c><00:02:37.200><c> called</c><00:02:37.350><c> a</c><00:02:37.440><c> server</c><00:02:38.060><c> now</c><00:02:39.060><c> the</c>

00:02:39.110 --> 00:02:39.120 align:start position:0%
over to what's called a server now the
 

00:02:39.120 --> 00:02:43.790 align:start position:0%
over to what's called a server now the
server<00:02:41.210><c> will</c><00:02:42.210><c> take</c><00:02:42.420><c> this</c><00:02:42.540><c> information</c><00:02:42.750><c> and</c><00:02:43.410><c> do</c>

00:02:43.790 --> 00:02:43.800 align:start position:0%
server will take this information and do
 

00:02:43.800 --> 00:02:44.780 align:start position:0%
server will take this information and do
something<00:02:44.430><c> with</c><00:02:44.580><c> it</c>

00:02:44.780 --> 00:02:44.790 align:start position:0%
something with it
 

00:02:44.790 --> 00:02:46.910 align:start position:0%
something with it
a<00:02:44.910><c> very</c><00:02:45.360><c> common</c><00:02:45.780><c> example</c><00:02:46.410><c> is</c><00:02:46.530><c> that</c><00:02:46.740><c> it's</c><00:02:46.830><c> going</c>

00:02:46.910 --> 00:02:46.920 align:start position:0%
a very common example is that it's going
 

00:02:46.920 --> 00:02:49.190 align:start position:0%
a very common example is that it's going
to<00:02:46.980><c> save</c><00:02:47.250><c> it</c><00:02:47.430><c> to</c><00:02:47.460><c> a</c><00:02:47.580><c> database</c><00:02:47.940><c> that</c><00:02:48.330><c> might</c>

00:02:49.190 --> 00:02:49.200 align:start position:0%
to save it to a database that might
 

00:02:49.200 --> 00:02:51.380 align:start position:0%
to save it to a database that might
always<00:02:49.350><c> be</c><00:02:49.500><c> the</c><00:02:49.680><c> case</c><00:02:49.860><c> but</c><00:02:50.490><c> that's</c><00:02:50.760><c> just</c><00:02:50.940><c> the</c>

00:02:51.380 --> 00:02:51.390 align:start position:0%
always be the case but that's just the
 

00:02:51.390 --> 00:02:55.310 align:start position:0%
always be the case but that's just the
more<00:02:51.600><c> common</c><00:02:52.050><c> thing</c><00:02:52.770><c> to</c><00:02:52.950><c> do</c><00:02:53.959><c> such</c><00:02:54.959><c> as</c><00:02:55.110><c> when</c><00:02:55.230><c> you</c>

00:02:55.310 --> 00:02:55.320 align:start position:0%
more common thing to do such as when you
 

00:02:55.320 --> 00:02:57.770 align:start position:0%
more common thing to do such as when you
signing<00:02:55.740><c> up</c><00:02:55.860><c> for</c><00:02:56.010><c> a</c><00:02:56.100><c> web</c><00:02:56.250><c> website</c><00:02:56.910><c> they're</c>

00:02:57.770 --> 00:02:57.780 align:start position:0%
signing up for a web website they're
 

00:02:57.780 --> 00:02:58.970 align:start position:0%
signing up for a web website they're
gonna<00:02:57.870><c> take</c><00:02:58.020><c> your</c><00:02:58.140><c> data</c><00:02:58.230><c> save</c><00:02:58.530><c> it</c><00:02:58.709><c> and</c><00:02:58.830><c> the</c>

00:02:58.970 --> 00:02:58.980 align:start position:0%
gonna take your data save it and the
 

00:02:58.980 --> 00:03:01.550 align:start position:0%
gonna take your data save it and the
next<00:02:59.130><c> time</c><00:02:59.280><c> you</c><00:02:59.490><c> come</c><00:02:59.670><c> you</c><00:03:00.510><c> like</c><00:03:01.380><c> I</c><00:03:01.440><c> said</c>

00:03:01.550 --> 00:03:01.560 align:start position:0%
next time you come you like I said
 

00:03:01.560 --> 00:03:03.500 align:start position:0%
next time you come you like I said
before<00:03:01.709><c> you</c><00:03:01.890><c> put</c><00:03:02.070><c> in</c><00:03:02.160><c> using</c><00:03:02.490><c> using</c><00:03:03.420><c> the</c>

00:03:03.500 --> 00:03:03.510 align:start position:0%
before you put in using using the
 

00:03:03.510 --> 00:03:06.080 align:start position:0%
before you put in using using the
password<00:03:03.690><c> and</c><00:03:04.340><c> it'll</c><00:03:05.340><c> look</c><00:03:05.430><c> able</c><00:03:05.670><c> to</c><00:03:05.730><c> database</c>

00:03:06.080 --> 00:03:06.090 align:start position:0%
password and it'll look able to database
 

00:03:06.090 --> 00:03:10.430 align:start position:0%
password and it'll look able to database
and<00:03:06.630><c> if</c><00:03:06.750><c> this</c><00:03:06.870><c> correct</c><00:03:07.170><c> you're</c><00:03:07.620><c> in</c><00:03:08.600><c> so</c><00:03:09.600><c> after</c>

00:03:10.430 --> 00:03:10.440 align:start position:0%
and if this correct you're in so after
 

00:03:10.440 --> 00:03:14.060 align:start position:0%
and if this correct you're in so after
the<00:03:10.560><c> server</c><00:03:10.830><c> does</c><00:03:11.430><c> something</c><00:03:12.320><c> with</c><00:03:13.320><c> with</c><00:03:13.920><c> your</c>

00:03:14.060 --> 00:03:14.070 align:start position:0%
the server does something with with your
 

00:03:14.070 --> 00:03:16.780 align:start position:0%
the server does something with with your
information<00:03:14.220><c> and</c><00:03:14.670><c> again</c><00:03:15.360><c> it's</c><00:03:15.660><c> got</c><00:03:15.930><c> to</c><00:03:16.050><c> be</c><00:03:16.230><c> a</c>

00:03:16.780 --> 00:03:16.790 align:start position:0%
information and again it's got to be a
 

00:03:16.790 --> 00:03:19.000 align:start position:0%
information and again it's got to be a
programming<00:03:17.790><c> language</c><00:03:18.180><c> that</c><00:03:18.209><c> does</c><00:03:18.600><c> it's</c><00:03:18.780><c> not</c>

00:03:19.000 --> 00:03:19.010 align:start position:0%
programming language that does it's not
 

00:03:19.010 --> 00:03:21.860 align:start position:0%
programming language that does it's not
HTML<00:03:20.010><c> can't</c><00:03:20.330><c> do</c><00:03:21.330><c> this</c><00:03:21.510><c> has</c><00:03:21.690><c> to</c><00:03:21.780><c> be</c><00:03:21.840><c> a</c>

00:03:21.860 --> 00:03:21.870 align:start position:0%
HTML can't do this has to be a
 

00:03:21.870 --> 00:03:28.310 align:start position:0%
HTML can't do this has to be a
programming<00:03:22.320><c> language</c><00:03:22.350><c> so</c><00:03:24.230><c> PHP</c><00:03:26.120><c> PHP</c><00:03:27.120><c> Java</c><00:03:27.720><c> you</c>

00:03:28.310 --> 00:03:28.320 align:start position:0%
programming language so PHP PHP Java you
 

00:03:28.320 --> 00:03:31.100 align:start position:0%
programming language so PHP PHP Java you
know<00:03:28.440><c> those</c><00:03:28.620><c> are</c><00:03:28.769><c> common</c><00:03:29.730><c> ones</c><00:03:30.000><c> that</c><00:03:30.150><c> will</c>

00:03:31.100 --> 00:03:31.110 align:start position:0%
know those are common ones that will
 

00:03:31.110 --> 00:03:34.699 align:start position:0%
know those are common ones that will
process<00:03:31.440><c> the</c><00:03:31.530><c> information</c><00:03:31.680><c> and</c><00:03:32.160><c> so</c><00:03:32.820><c> after</c><00:03:33.709><c> the</c>

00:03:34.699 --> 00:03:34.709 align:start position:0%
process the information and so after the
 

00:03:34.709 --> 00:03:35.690 align:start position:0%
process the information and so after the
server<00:03:34.980><c> does</c><00:03:35.190><c> something</c><00:03:35.519><c> with</c><00:03:35.610><c> an</c>

00:03:35.690 --> 00:03:35.700 align:start position:0%
server does something with an
 

00:03:35.700 --> 00:03:38.390 align:start position:0%
server does something with an
information<00:03:36.260><c> you'll</c><00:03:37.260><c> be</c><00:03:37.410><c> redirected</c><00:03:37.769><c> to</c>

00:03:38.390 --> 00:03:38.400 align:start position:0%
information you'll be redirected to
 

00:03:38.400 --> 00:03:41.870 align:start position:0%
information you'll be redirected to
another<00:03:38.880><c> web</c><00:03:39.090><c> page</c><00:03:39.120><c> with</c><00:03:40.110><c> some</c><00:03:40.850><c> with</c><00:03:41.850><c> some</c>

00:03:41.870 --> 00:03:41.880 align:start position:0%
another web page with some with some
 

00:03:41.880 --> 00:03:44.420 align:start position:0%
another web page with some with some
response<00:03:42.630><c> like</c><00:03:43.560><c> where</c><00:03:43.950><c> the</c><00:03:44.040><c> failure</c>

00:03:44.420 --> 00:03:44.430 align:start position:0%
response like where the failure
 

00:03:44.430 --> 00:03:46.850 align:start position:0%
response like where the failure
successful<00:03:45.330><c> whatever</c><00:03:45.780><c> it</c><00:03:46.050><c> is</c><00:03:46.170><c> it'll</c><00:03:46.620><c> give</c><00:03:46.800><c> you</c>

00:03:46.850 --> 00:03:46.860 align:start position:0%
successful whatever it is it'll give you
 

00:03:46.860 --> 00:03:51.350 align:start position:0%
successful whatever it is it'll give you
some<00:03:47.040><c> response</c><00:03:48.440><c> so</c><00:03:49.440><c> again</c><00:03:49.970><c> you're</c><00:03:50.970><c> gonna</c><00:03:51.060><c> fill</c>

00:03:51.350 --> 00:03:51.360 align:start position:0%
some response so again you're gonna fill
 

00:03:51.360 --> 00:03:53.930 align:start position:0%
some response so again you're gonna fill
out<00:03:51.450><c> the</c><00:03:51.540><c> information</c><00:03:52.580><c> you're</c><00:03:53.580><c> gonna</c><00:03:53.730><c> hit</c><00:03:53.880><c> the</c>

00:03:53.930 --> 00:03:53.940 align:start position:0%
out the information you're gonna hit the
 

00:03:53.940 --> 00:03:55.850 align:start position:0%
out the information you're gonna hit the
submit<00:03:54.180><c> button</c><00:03:54.320><c> it's</c><00:03:55.320><c> going</c><00:03:55.470><c> to</c><00:03:55.560><c> go</c><00:03:55.620><c> to</c><00:03:55.680><c> a</c>

00:03:55.850 --> 00:03:55.860 align:start position:0%
submit button it's going to go to a
 

00:03:55.860 --> 00:03:58.880 align:start position:0%
submit button it's going to go to a
server<00:03:56.250><c> the</c><00:03:57.090><c> server's</c><00:03:57.360><c> gonna</c><00:03:57.480><c> process</c><00:03:57.890><c> the</c>

00:03:58.880 --> 00:03:58.890 align:start position:0%
server the server's gonna process the
 

00:03:58.890 --> 00:04:00.820 align:start position:0%
server the server's gonna process the
data<00:03:59.130><c> that</c><00:03:59.730><c> you</c><00:03:59.850><c> just</c><00:03:59.880><c> entered</c><00:04:00.360><c> in</c><00:04:00.480><c> the</c><00:04:00.540><c> form</c>

00:04:00.820 --> 00:04:00.830 align:start position:0%
data that you just entered in the form
 

00:04:00.830 --> 00:04:03.740 align:start position:0%
data that you just entered in the form
do<00:04:01.830><c> something</c><00:04:02.190><c> with</c><00:04:02.340><c> it</c><00:04:02.519><c> and</c><00:04:02.700><c> then</c><00:04:03.150><c> it's</c><00:04:03.570><c> gonna</c>

00:04:03.740 --> 00:04:03.750 align:start position:0%
do something with it and then it's gonna
 

00:04:03.750 --> 00:04:06.140 align:start position:0%
do something with it and then it's gonna
redirect<00:04:04.650><c> you</c><00:04:04.890><c> to</c><00:04:04.950><c> another</c><00:04:05.250><c> web</c><00:04:05.459><c> page</c><00:04:05.489><c> with</c>

00:04:06.140 --> 00:04:06.150 align:start position:0%
redirect you to another web page with
 

00:04:06.150 --> 00:04:10.310 align:start position:0%
redirect you to another web page with
some<00:04:06.450><c> response</c><00:04:06.870><c> and</c><00:04:08.180><c> that's</c><00:04:09.180><c> basically</c><00:04:10.110><c> how</c><00:04:10.230><c> a</c>

00:04:10.310 --> 00:04:10.320 align:start position:0%
some response and that's basically how a
 

00:04:10.320 --> 00:04:15.500 align:start position:0%
some response and that's basically how a
form<00:04:10.620><c> works</c><00:04:10.799><c> well</c><00:04:11.670><c> to</c><00:04:12.390><c> quickly</c><00:04:12.600><c> recap</c><00:04:13.760><c> again</c><00:04:14.760><c> a</c>

00:04:15.500 --> 00:04:15.510 align:start position:0%
form works well to quickly recap again a
 

00:04:15.510 --> 00:04:17.360 align:start position:0%
form works well to quickly recap again a
user<00:04:16.109><c> will</c><00:04:16.410><c> fill</c><00:04:16.590><c> out</c><00:04:16.680><c> the</c><00:04:16.799><c> information</c><00:04:16.979><c> in</c><00:04:17.280><c> a</c>

00:04:17.360 --> 00:04:17.370 align:start position:0%
user will fill out the information in a
 

00:04:17.370 --> 00:04:19.849 align:start position:0%
user will fill out the information in a
form<00:04:17.609><c> this</c><00:04:18.450><c> information</c><00:04:19.049><c> goes</c><00:04:19.229><c> to</c><00:04:19.440><c> a</c><00:04:19.470><c> server</c>

00:04:19.849 --> 00:04:19.859 align:start position:0%
form this information goes to a server
 

00:04:19.859 --> 00:04:23.240 align:start position:0%
form this information goes to a server
and<00:04:20.190><c> that</c><00:04:20.220><c> server</c><00:04:20.820><c> is</c><00:04:21.209><c> going</c><00:04:21.630><c> to</c><00:04:21.959><c> process</c><00:04:22.410><c> the</c>

00:04:23.240 --> 00:04:23.250 align:start position:0%
and that server is going to process the
 

00:04:23.250 --> 00:04:24.680 align:start position:0%
and that server is going to process the
information<00:04:23.490><c> that</c><00:04:24.120><c> was</c><00:04:24.210><c> filled</c><00:04:24.450><c> out</c><00:04:24.510><c> in</c><00:04:24.660><c> the</c>

00:04:24.680 --> 00:04:24.690 align:start position:0%
information that was filled out in the
 

00:04:24.690 --> 00:04:26.900 align:start position:0%
information that was filled out in the
form<00:04:25.050><c> it's</c><00:04:25.560><c> gonna</c><00:04:25.680><c> do</c><00:04:25.770><c> something</c><00:04:25.950><c> with</c><00:04:26.130><c> it</c>

00:04:26.900 --> 00:04:26.910 align:start position:0%
form it's gonna do something with it
 

00:04:26.910 --> 00:04:31.250 align:start position:0%
form it's gonna do something with it
and<00:04:27.020><c> then</c><00:04:29.270><c> after</c><00:04:30.270><c> it</c><00:04:30.390><c> does</c><00:04:30.570><c> it's</c><00:04:30.930><c> going</c><00:04:31.140><c> to</c>

00:04:31.250 --> 00:04:31.260 align:start position:0%
and then after it does it's going to
 

00:04:31.260 --> 00:04:33.950 align:start position:0%
and then after it does it's going to
redirect<00:04:31.590><c> you</c><00:04:31.950><c> to</c><00:04:32.130><c> another</c><00:04:32.550><c> webpage</c><00:04:33.060><c> with</c>

00:04:33.950 --> 00:04:33.960 align:start position:0%
redirect you to another webpage with
 

00:04:33.960 --> 00:04:37.130 align:start position:0%
redirect you to another webpage with
some<00:04:34.650><c> response</c><00:04:35.390><c> let</c><00:04:36.390><c> you</c><00:04:36.510><c> know</c><00:04:36.720><c> that</c><00:04:37.020><c> was</c>

00:04:37.130 --> 00:04:37.140 align:start position:0%
some response let you know that was
 

00:04:37.140 --> 00:04:41.120 align:start position:0%
some response let you know that was
successful<00:04:37.650><c> or</c><00:04:38.040><c> whatever</c><00:04:38.480><c> but</c><00:04:39.480><c> that's</c><00:04:40.350><c> really</c>

00:04:41.120 --> 00:04:41.130 align:start position:0%
successful or whatever but that's really
 

00:04:41.130 --> 00:04:42.440 align:start position:0%
successful or whatever but that's really
just<00:04:41.250><c> how</c><00:04:41.430><c> the</c><00:04:41.490><c> forum</c><00:04:41.730><c> works</c><00:04:41.910><c> and</c><00:04:42.090><c> we</c><00:04:42.150><c> need</c><00:04:42.360><c> to</c>

00:04:42.440 --> 00:04:42.450 align:start position:0%
just how the forum works and we need to
 

00:04:42.450 --> 00:04:46.100 align:start position:0%
just how the forum works and we need to
know<00:04:42.630><c> that</c><00:04:43.580><c> because</c><00:04:44.580><c> in</c><00:04:45.120><c> the</c><00:04:45.210><c> future</c><00:04:45.450><c> when</c><00:04:46.020><c> we</c>

00:04:46.100 --> 00:04:46.110 align:start position:0%
know that because in the future when we
 

00:04:46.110 --> 00:04:49.190 align:start position:0%
know that because in the future when we
talk<00:04:46.260><c> about</c><00:04:46.290><c> JavaScript</c><00:04:47.900><c> we're</c><00:04:48.900><c> going</c><00:04:49.050><c> to</c><00:04:49.110><c> use</c>

00:04:49.190 --> 00:04:49.200 align:start position:0%
talk about JavaScript we're going to use
 

00:04:49.200 --> 00:04:53.240 align:start position:0%
talk about JavaScript we're going to use
JavaScript<00:04:49.560><c> to</c><00:04:51.410><c> deal</c><00:04:52.410><c> with</c><00:04:52.590><c> the</c><00:04:52.680><c> information</c>

00:04:53.240 --> 00:04:53.250 align:start position:0%
JavaScript to deal with the information
 

00:04:53.250 --> 00:04:56.030 align:start position:0%
JavaScript to deal with the information
okay<00:04:53.960><c> all</c><00:04:54.960><c> right</c><00:04:55.080><c> thank</c><00:04:55.620><c> you</c><00:04:55.680><c> for</c><00:04:55.770><c> watching</c>

00:04:56.030 --> 00:04:56.040 align:start position:0%
okay all right thank you for watching
 

00:04:56.040 --> 00:04:59.360 align:start position:0%
okay all right thank you for watching
and<00:04:56.190><c> I'll</c><00:04:56.850><c> see</c><00:04:56.880><c> you</c><00:04:57.090><c> next</c><00:04:57.120><c> time</c>

