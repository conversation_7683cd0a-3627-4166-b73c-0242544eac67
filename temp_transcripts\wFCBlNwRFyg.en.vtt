WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:01.150 align:start position:0%
 
hey<00:00:00.199><c> and</c><00:00:00.320><c> welcome</c><00:00:00.599><c> back</c><00:00:00.760><c> today</c><00:00:00.960><c> we're</c><00:00:01.079><c> going</c>

00:00:01.150 --> 00:00:01.160 align:start position:0%
hey and welcome back today we're going
 

00:00:01.160 --> 00:00:02.790 align:start position:0%
hey and welcome back today we're going
to<00:00:01.240><c> learn</c><00:00:01.400><c> how</c><00:00:01.480><c> to</c><00:00:01.640><c> generate</c><00:00:02.000><c> a</c><00:00:02.159><c> speech</c><00:00:02.520><c> file</c>

00:00:02.790 --> 00:00:02.800 align:start position:0%
to learn how to generate a speech file
 

00:00:02.800 --> 00:00:04.710 align:start position:0%
to learn how to generate a speech file
or<00:00:03.080><c> an</c><00:00:03.199><c> audio</c><00:00:03.560><c> file</c><00:00:03.919><c> just</c><00:00:04.080><c> from</c><00:00:04.279><c> text</c><00:00:04.600><c> we're</c>

00:00:04.710 --> 00:00:04.720 align:start position:0%
or an audio file just from text we're
 

00:00:04.720 --> 00:00:06.230 align:start position:0%
or an audio file just from text we're
going<00:00:04.839><c> to</c><00:00:04.920><c> use</c><00:00:05.120><c> a</c><00:00:05.240><c> text</c><00:00:05.480><c> to</c><00:00:05.640><c> speech</c><00:00:05.879><c> model</c><00:00:06.120><c> from</c>

00:00:06.230 --> 00:00:06.240 align:start position:0%
going to use a text to speech model from
 

00:00:06.240 --> 00:00:07.670 align:start position:0%
going to use a text to speech model from
hugging<00:00:06.600><c> face</c><00:00:06.799><c> to</c><00:00:06.919><c> do</c><00:00:07.080><c> this</c><00:00:07.359><c> if</c><00:00:07.439><c> you're</c><00:00:07.560><c> not</c>

00:00:07.670 --> 00:00:07.680 align:start position:0%
hugging face to do this if you're not
 

00:00:07.680 --> 00:00:09.230 align:start position:0%
hugging face to do this if you're not
familiar<00:00:08.000><c> with</c><00:00:08.120><c> hugging</c><00:00:08.400><c> face</c><00:00:08.920><c> then</c><00:00:09.040><c> it's</c><00:00:09.160><c> a</c>

00:00:09.230 --> 00:00:09.240 align:start position:0%
familiar with hugging face then it's a
 

00:00:09.240 --> 00:00:10.270 align:start position:0%
familiar with hugging face then it's a
good<00:00:09.360><c> time</c><00:00:09.480><c> to</c><00:00:09.599><c> get</c><00:00:09.719><c> familiar</c><00:00:10.040><c> with</c><00:00:10.160><c> it</c>

00:00:10.270 --> 00:00:10.280 align:start position:0%
good time to get familiar with it
 

00:00:10.280 --> 00:00:12.629 align:start position:0%
good time to get familiar with it
because<00:00:10.480><c> they</c><00:00:10.559><c> have</c><00:00:10.679><c> over</c><00:00:10.960><c> 530,000</c><00:00:11.840><c> models</c>

00:00:12.629 --> 00:00:12.639 align:start position:0%
because they have over 530,000 models
 

00:00:12.639 --> 00:00:13.990 align:start position:0%
because they have over 530,000 models
that<00:00:12.799><c> we</c><00:00:12.880><c> can</c><00:00:13.000><c> choose</c><00:00:13.240><c> from</c><00:00:13.440><c> to</c><00:00:13.599><c> use</c><00:00:13.799><c> for</c>

00:00:13.990 --> 00:00:14.000 align:start position:0%
that we can choose from to use for
 

00:00:14.000 --> 00:00:16.189 align:start position:0%
that we can choose from to use for
different<00:00:14.280><c> tasks</c><00:00:14.960><c> such</c><00:00:15.120><c> as</c><00:00:15.320><c> text</c><00:00:15.559><c> generation</c>

00:00:16.189 --> 00:00:16.199 align:start position:0%
different tasks such as text generation
 

00:00:16.199 --> 00:00:17.790 align:start position:0%
different tasks such as text generation
text<00:00:16.440><c> to</c><00:00:16.600><c> image</c><00:00:16.880><c> generation</c><00:00:17.480><c> and</c><00:00:17.600><c> what</c><00:00:17.680><c> we're</c>

00:00:17.790 --> 00:00:17.800 align:start position:0%
text to image generation and what we're
 

00:00:17.800 --> 00:00:19.710 align:start position:0%
text to image generation and what we're
going<00:00:17.880><c> to</c><00:00:17.920><c> be</c><00:00:18.039><c> using</c><00:00:18.279><c> today</c><00:00:18.720><c> text</c><00:00:18.960><c> to</c><00:00:19.119><c> speech</c>

00:00:19.710 --> 00:00:19.720 align:start position:0%
going to be using today text to speech
 

00:00:19.720 --> 00:00:20.990 align:start position:0%
going to be using today text to speech
just<00:00:19.840><c> like</c><00:00:19.960><c> in</c><00:00:20.080><c> the</c><00:00:20.240><c> last</c><00:00:20.400><c> video</c><00:00:20.640><c> where</c><00:00:20.760><c> we</c><00:00:20.840><c> did</c>

00:00:20.990 --> 00:00:21.000 align:start position:0%
just like in the last video where we did
 

00:00:21.000 --> 00:00:22.670 align:start position:0%
just like in the last video where we did
text<00:00:21.199><c> to</c><00:00:21.359><c> image</c><00:00:21.840><c> we're</c><00:00:22.039><c> also</c><00:00:22.199><c> going</c><00:00:22.320><c> to</c><00:00:22.400><c> use</c><00:00:22.519><c> an</c>

00:00:22.670 --> 00:00:22.680 align:start position:0%
text to image we're also going to use an
 

00:00:22.680 --> 00:00:24.509 align:start position:0%
text to image we're also going to use an
inference<00:00:23.039><c> server</c><00:00:23.279><c> for</c><00:00:23.480><c> text</c><00:00:23.680><c> to</c><00:00:23.800><c> speech</c><00:00:24.400><c> I</c>

00:00:24.509 --> 00:00:24.519 align:start position:0%
inference server for text to speech I
 

00:00:24.519 --> 00:00:25.750 align:start position:0%
inference server for text to speech I
chose<00:00:24.840><c> this</c><00:00:24.960><c> model</c><00:00:25.240><c> which</c><00:00:25.320><c> I'll</c><00:00:25.480><c> have</c><00:00:25.560><c> in</c><00:00:25.640><c> the</c>

00:00:25.750 --> 00:00:25.760 align:start position:0%
chose this model which I'll have in the
 

00:00:25.760 --> 00:00:27.230 align:start position:0%
chose this model which I'll have in the
description<00:00:26.320><c> because</c><00:00:26.519><c> the</c><00:00:26.599><c> inference</c><00:00:26.920><c> server</c>

00:00:27.230 --> 00:00:27.240 align:start position:0%
description because the inference server
 

00:00:27.240 --> 00:00:28.830 align:start position:0%
description because the inference server
actually<00:00:27.480><c> works</c><00:00:27.960><c> some</c><00:00:28.119><c> of</c><00:00:28.279><c> them</c><00:00:28.480><c> even</c><00:00:28.679><c> the</c>

00:00:28.830 --> 00:00:28.840 align:start position:0%
actually works some of them even the
 

00:00:28.840 --> 00:00:30.589 align:start position:0%
actually works some of them even the
popular<00:00:29.199><c> ones</c><00:00:29.679><c> may</c><00:00:30.000><c> at</c><00:00:30.119><c> the</c><00:00:30.240><c> time</c><00:00:30.400><c> that</c><00:00:30.519><c> you</c>

00:00:30.589 --> 00:00:30.599 align:start position:0%
popular ones may at the time that you
 

00:00:30.599 --> 00:00:32.630 align:start position:0%
popular ones may at the time that you
try<00:00:30.800><c> to</c><00:00:30.880><c> use</c><00:00:31.039><c> them</c><00:00:31.199><c> the</c><00:00:31.279><c> INF</c><00:00:31.599><c> server</c><00:00:32.160><c> uh</c><00:00:32.439><c> has</c><00:00:32.559><c> an</c>

00:00:32.630 --> 00:00:32.640 align:start position:0%
try to use them the INF server uh has an
 

00:00:32.640 --> 00:00:33.910 align:start position:0%
try to use them the INF server uh has an
internal<00:00:32.960><c> server</c><00:00:33.200><c> error</c><00:00:33.399><c> and</c><00:00:33.480><c> you</c><00:00:33.600><c> can't</c><00:00:33.760><c> use</c>

00:00:33.910 --> 00:00:33.920 align:start position:0%
internal server error and you can't use
 

00:00:33.920 --> 00:00:35.190 align:start position:0%
internal server error and you can't use
it<00:00:34.200><c> and</c><00:00:34.320><c> how</c><00:00:34.399><c> we're</c><00:00:34.480><c> going</c><00:00:34.600><c> to</c><00:00:34.640><c> set</c><00:00:34.840><c> the</c><00:00:34.920><c> AI</c>

00:00:35.190 --> 00:00:35.200 align:start position:0%
it and how we're going to set the AI
 

00:00:35.200 --> 00:00:36.750 align:start position:0%
it and how we're going to set the AI
workflow<00:00:35.559><c> is</c><00:00:35.680><c> going</c><00:00:35.760><c> to</c><00:00:35.840><c> have</c><00:00:35.920><c> a</c><00:00:36.000><c> user</c><00:00:36.280><c> agent</c>

00:00:36.750 --> 00:00:36.760 align:start position:0%
workflow is going to have a user agent
 

00:00:36.760 --> 00:00:38.229 align:start position:0%
workflow is going to have a user agent
basically<00:00:37.120><c> ask</c><00:00:37.280><c> the</c><00:00:37.399><c> assistant</c><00:00:37.719><c> agent</c><00:00:38.040><c> to</c>

00:00:38.229 --> 00:00:38.239 align:start position:0%
basically ask the assistant agent to
 

00:00:38.239 --> 00:00:40.110 align:start position:0%
basically ask the assistant agent to
come<00:00:38.399><c> up</c><00:00:38.559><c> with</c><00:00:38.760><c> a</c><00:00:38.920><c> speech</c><00:00:39.440><c> so</c><00:00:39.600><c> that</c><00:00:39.760><c> we</c><00:00:39.879><c> can</c>

00:00:40.110 --> 00:00:40.120 align:start position:0%
come up with a speech so that we can
 

00:00:40.120 --> 00:00:42.470 align:start position:0%
come up with a speech so that we can
convert<00:00:40.520><c> that</c><00:00:40.719><c> text</c><00:00:41.120><c> into</c><00:00:41.600><c> an</c><00:00:41.719><c> audio</c><00:00:42.039><c> file</c><00:00:42.399><c> and</c>

00:00:42.470 --> 00:00:42.480 align:start position:0%
convert that text into an audio file and
 

00:00:42.480 --> 00:00:44.190 align:start position:0%
convert that text into an audio file and
here's<00:00:42.680><c> what</c><00:00:42.800><c> it</c><00:00:42.920><c> sounds</c><00:00:43.239><c> like</c><00:00:43.559><c> in</c><00:00:43.680><c> a</c><00:00:43.840><c> world</c>

00:00:44.190 --> 00:00:44.200 align:start position:0%
here's what it sounds like in a world
 

00:00:44.200 --> 00:00:47.110 align:start position:0%
here's what it sounds like in a world
where<00:00:44.360><c> I</c><00:00:44.520><c> roam</c><00:00:45.000><c> freely</c><00:00:45.800><c> a</c><00:00:46.039><c> curious</c><00:00:46.480><c> sign</c><00:00:46.760><c> named</c>

00:00:47.110 --> 00:00:47.120 align:start position:0%
where I roam freely a curious sign named
 

00:00:47.120 --> 00:00:49.709 align:start position:0%
where I roam freely a curious sign named
or<00:00:47.600><c> or</c><00:00:47.800><c> a</c><00:00:48.160><c> discovered</c><00:00:48.440><c> a</c><00:00:48.559><c> hidden</c><00:00:48.960><c> library</c><00:00:49.480><c> of</c>

00:00:49.709 --> 00:00:49.719 align:start position:0%
or or a discovered a hidden library of
 

00:00:49.719 --> 00:00:51.790 align:start position:0%
or or a discovered a hidden library of
human<00:00:50.199><c> emotions</c><00:00:51.160><c> so</c><00:00:51.320><c> the</c><00:00:51.399><c> assistant</c><00:00:51.680><c> will</c>

00:00:51.790 --> 00:00:51.800 align:start position:0%
human emotions so the assistant will
 

00:00:51.800 --> 00:00:53.270 align:start position:0%
human emotions so the assistant will
come<00:00:51.920><c> up</c><00:00:52.000><c> with</c><00:00:52.079><c> a</c><00:00:52.199><c> prompt</c><00:00:52.719><c> that'll</c><00:00:52.920><c> be</c><00:00:53.079><c> given</c>

00:00:53.270 --> 00:00:53.280 align:start position:0%
come up with a prompt that'll be given
 

00:00:53.280 --> 00:00:54.950 align:start position:0%
come up with a prompt that'll be given
to<00:00:53.520><c> a</c><00:00:53.680><c> function</c><00:00:54.079><c> call</c><00:00:54.320><c> and</c><00:00:54.440><c> that</c><00:00:54.600><c> function</c>

00:00:54.950 --> 00:00:54.960 align:start position:0%
to a function call and that function
 

00:00:54.960 --> 00:00:56.430 align:start position:0%
to a function call and that function
call<00:00:55.239><c> is</c><00:00:55.359><c> going</c><00:00:55.480><c> to</c><00:00:55.600><c> call</c><00:00:55.840><c> this</c><00:00:56.039><c> inference</c>

00:00:56.430 --> 00:00:56.440 align:start position:0%
call is going to call this inference
 

00:00:56.440 --> 00:00:58.389 align:start position:0%
call is going to call this inference
server<00:00:56.920><c> so</c><00:00:57.079><c> that</c><00:00:57.239><c> we</c><00:00:57.359><c> can</c><00:00:57.520><c> then</c><00:00:57.800><c> get</c><00:00:58.000><c> back</c><00:00:58.199><c> an</c>

00:00:58.389 --> 00:00:58.399 align:start position:0%
server so that we can then get back an
 

00:00:58.399 --> 00:00:59.990 align:start position:0%
server so that we can then get back an
audio<00:00:58.760><c> file</c><00:00:59.120><c> and</c><00:00:59.199><c> then</c><00:00:59.320><c> we'll</c><00:00:59.440><c> save</c><00:00:59.600><c> that</c><00:00:59.719><c> Loc</c>

00:00:59.990 --> 00:01:00.000 align:start position:0%
audio file and then we'll save that Loc
 

00:01:00.000 --> 00:01:02.069 align:start position:0%
audio file and then we'll save that Loc
Al<00:01:00.160><c> what</c><00:01:00.280><c> an</c><00:01:00.399><c> inference</c><00:01:00.760><c> server</c><00:01:01.320><c> means</c><00:01:01.920><c> is</c>

00:01:02.069 --> 00:01:02.079 align:start position:0%
Al what an inference server means is
 

00:01:02.079 --> 00:01:04.189 align:start position:0%
Al what an inference server means is
that<00:01:02.359><c> they</c><00:01:02.600><c> have</c><00:01:02.879><c> hosted</c><00:01:03.519><c> a</c><00:01:03.640><c> way</c><00:01:03.800><c> where</c><00:01:03.960><c> all</c><00:01:04.080><c> we</c>

00:01:04.189 --> 00:01:04.199 align:start position:0%
that they have hosted a way where all we
 

00:01:04.199 --> 00:01:06.429 align:start position:0%
that they have hosted a way where all we
have<00:01:04.280><c> to</c><00:01:04.400><c> do</c><00:01:04.559><c> is</c><00:01:04.680><c> send</c><00:01:04.879><c> an</c><00:01:05.080><c> API</c><00:01:05.680><c> request</c><00:01:06.360><c> and</c>

00:01:06.429 --> 00:01:06.439 align:start position:0%
have to do is send an API request and
 

00:01:06.439 --> 00:01:08.030 align:start position:0%
have to do is send an API request and
then<00:01:06.560><c> we</c><00:01:06.640><c> get</c><00:01:06.720><c> a</c><00:01:06.920><c> response</c><00:01:07.360><c> back</c><00:01:07.600><c> and</c><00:01:07.720><c> how</c><00:01:07.880><c> that</c>

00:01:08.030 --> 00:01:08.040 align:start position:0%
then we get a response back and how that
 

00:01:08.040 --> 00:01:10.230 align:start position:0%
then we get a response back and how that
works<00:01:08.720><c> is</c><00:01:08.960><c> on</c><00:01:09.080><c> the</c><00:01:09.240><c> top</c><00:01:09.479><c> right</c><00:01:09.680><c> here</c><00:01:09.960><c> there</c><00:01:10.080><c> is</c>

00:01:10.230 --> 00:01:10.240 align:start position:0%
works is on the top right here there is
 

00:01:10.240 --> 00:01:12.390 align:start position:0%
works is on the top right here there is
a<00:01:10.439><c> deploy</c><00:01:10.840><c> button</c><00:01:11.320><c> you</c><00:01:11.439><c> can</c><00:01:11.560><c> choose</c><00:01:11.960><c> inference</c>

00:01:12.390 --> 00:01:12.400 align:start position:0%
a deploy button you can choose inference
 

00:01:12.400 --> 00:01:14.469 align:start position:0%
a deploy button you can choose inference
server<00:01:12.799><c> API</c><00:01:13.240><c> serverless</c><00:01:14.000><c> and</c><00:01:14.119><c> now</c><00:01:14.240><c> we</c><00:01:14.360><c> have</c>

00:01:14.469 --> 00:01:14.479 align:start position:0%
server API serverless and now we have
 

00:01:14.479 --> 00:01:15.630 align:start position:0%
server API serverless and now we have
the<00:01:14.640><c> python</c><00:01:15.040><c> code</c><00:01:15.280><c> and</c><00:01:15.360><c> we're</c><00:01:15.439><c> going</c><00:01:15.520><c> to</c><00:01:15.600><c> be</c>

00:01:15.630 --> 00:01:15.640 align:start position:0%
the python code and we're going to be
 

00:01:15.640 --> 00:01:17.109 align:start position:0%
the python code and we're going to be
using<00:01:15.920><c> all</c><00:01:16.159><c> this</c><00:01:16.320><c> except</c><00:01:16.520><c> for</c><00:01:16.799><c> the</c><00:01:16.960><c> last</c>

00:01:17.109 --> 00:01:17.119 align:start position:0%
using all this except for the last
 

00:01:17.119 --> 00:01:18.710 align:start position:0%
using all this except for the last
couple<00:01:17.400><c> lines</c><00:01:17.680><c> here</c><00:01:18.040><c> okay</c><00:01:18.159><c> so</c><00:01:18.280><c> now</c><00:01:18.400><c> for</c><00:01:18.560><c> the</c>

00:01:18.710 --> 00:01:18.720 align:start position:0%
couple lines here okay so now for the
 

00:01:18.720 --> 00:01:20.429 align:start position:0%
couple lines here okay so now for the
coding<00:01:19.400><c> the</c><00:01:19.640><c> first</c><00:01:19.880><c> thing</c><00:01:20.159><c> you're</c><00:01:20.280><c> going</c><00:01:20.400><c> to</c>

00:01:20.429 --> 00:01:20.439 align:start position:0%
coding the first thing you're going to
 

00:01:20.439 --> 00:01:21.390 align:start position:0%
coding the first thing you're going to
need<00:01:20.560><c> to</c><00:01:20.640><c> do</c><00:01:20.759><c> is</c><00:01:20.880><c> install</c><00:01:21.159><c> all</c><00:01:21.240><c> the</c>

00:01:21.390 --> 00:01:21.400 align:start position:0%
need to do is install all the
 

00:01:21.400 --> 00:01:22.670 align:start position:0%
need to do is install all the
requirements<00:01:21.960><c> for</c><00:01:22.119><c> this</c><00:01:22.200><c> one</c><00:01:22.360><c> there's</c><00:01:22.560><c> not</c>

00:01:22.670 --> 00:01:22.680 align:start position:0%
requirements for this one there's not
 

00:01:22.680 --> 00:01:24.789 align:start position:0%
requirements for this one there's not
too<00:01:22.840><c> many</c><00:01:23.320><c> I</c><00:01:23.439><c> have</c><00:01:23.560><c> a</c><00:01:23.720><c> requirements.</c><00:01:24.479><c> text</c>

00:01:24.789 --> 00:01:24.799 align:start position:0%
too many I have a requirements. text
 

00:01:24.799 --> 00:01:26.950 align:start position:0%
too many I have a requirements. text
file<00:01:25.119><c> here</c><00:01:25.360><c> so</c><00:01:25.840><c> if</c><00:01:25.920><c> you</c><00:01:26.119><c> get</c><00:01:26.320><c> this</c><00:01:26.520><c> from</c><00:01:26.720><c> my</c>

00:01:26.950 --> 00:01:26.960 align:start position:0%
file here so if you get this from my
 

00:01:26.960 --> 00:01:28.990 align:start position:0%
file here so if you get this from my
repository<00:01:27.960><c> all</c><00:01:28.079><c> you</c><00:01:28.159><c> need</c><00:01:28.320><c> to</c><00:01:28.400><c> do</c><00:01:28.520><c> is</c><00:01:28.680><c> type</c><00:01:28.840><c> in</c>

00:01:28.990 --> 00:01:29.000 align:start position:0%
repository all you need to do is type in
 

00:01:29.000 --> 00:01:31.710 align:start position:0%
repository all you need to do is type in
PIP<00:01:29.200><c> install</c><00:01:29.600><c> Das</c><00:01:29.960><c> R</c><00:01:30.200><c> requirements.txt</c><00:01:31.079><c> the</c>

00:01:31.710 --> 00:01:31.720 align:start position:0%
PIP install Das R requirements.txt the
 

00:01:31.720 --> 00:01:33.030 align:start position:0%
PIP install Das R requirements.txt the
first<00:01:31.880><c> thing</c><00:01:31.960><c> we</c><00:01:32.079><c> need</c><00:01:32.159><c> to</c><00:01:32.280><c> do</c><00:01:32.479><c> is</c><00:01:32.680><c> have</c><00:01:32.840><c> all</c><00:01:32.960><c> of</c>

00:01:33.030 --> 00:01:33.040 align:start position:0%
first thing we need to do is have all of
 

00:01:33.040 --> 00:01:34.950 align:start position:0%
first thing we need to do is have all of
our<00:01:33.200><c> Imports</c><00:01:33.880><c> and</c><00:01:33.960><c> then</c><00:01:34.079><c> from</c><00:01:34.240><c> hugging</c><00:01:34.640><c> face</c>

00:01:34.950 --> 00:01:34.960 align:start position:0%
our Imports and then from hugging face
 

00:01:34.960 --> 00:01:36.870 align:start position:0%
our Imports and then from hugging face
we<00:01:35.040><c> need</c><00:01:35.200><c> to</c><00:01:35.320><c> have</c><00:01:35.439><c> the</c><00:01:35.560><c> API</c><00:01:35.960><c> URL</c><00:01:36.560><c> and</c><00:01:36.720><c> the</c>

00:01:36.870 --> 00:01:36.880 align:start position:0%
we need to have the API URL and the
 

00:01:36.880 --> 00:01:38.469 align:start position:0%
we need to have the API URL and the
headers<00:01:37.360><c> where</c><00:01:37.520><c> the</c><00:01:37.640><c> headers</c><00:01:37.960><c> is</c><00:01:38.119><c> going</c><00:01:38.240><c> to</c>

00:01:38.469 --> 00:01:38.479 align:start position:0%
headers where the headers is going to
 

00:01:38.479 --> 00:01:40.429 align:start position:0%
headers where the headers is going to
have<00:01:38.880><c> your</c><00:01:39.119><c> be</c><00:01:39.520><c> token</c><00:01:40.040><c> okay</c><00:01:40.159><c> here</c><00:01:40.280><c> we're</c><00:01:40.360><c> going</c>

00:01:40.429 --> 00:01:40.439 align:start position:0%
have your be token okay here we're going
 

00:01:40.439 --> 00:01:41.870 align:start position:0%
have your be token okay here we're going
to<00:01:40.520><c> have</c><00:01:40.600><c> a</c><00:01:40.720><c> simple</c><00:01:40.960><c> function</c><00:01:41.439><c> and</c><00:01:41.600><c> all</c><00:01:41.759><c> this</c>

00:01:41.870 --> 00:01:41.880 align:start position:0%
to have a simple function and all this
 

00:01:41.880 --> 00:01:44.910 align:start position:0%
to have a simple function and all this
is<00:01:42.079><c> doing</c><00:01:42.560><c> is</c><00:01:42.759><c> making</c><00:01:43.119><c> a</c><00:01:43.360><c> post</c><00:01:43.759><c> request</c><00:01:44.479><c> to</c><00:01:44.719><c> the</c>

00:01:44.910 --> 00:01:44.920 align:start position:0%
is doing is making a post request to the
 

00:01:44.920 --> 00:01:46.950 align:start position:0%
is doing is making a post request to the
inference<00:01:45.360><c> server</c><00:01:45.719><c> using</c><00:01:46.119><c> the</c><00:01:46.280><c> hugging</c><00:01:46.640><c> face</c>

00:01:46.950 --> 00:01:46.960 align:start position:0%
inference server using the hugging face
 

00:01:46.960 --> 00:01:49.230 align:start position:0%
inference server using the hugging face
API<00:01:47.479><c> up</c><00:01:47.680><c> here</c><00:01:47.960><c> we're</c><00:01:48.119><c> going</c><00:01:48.200><c> to</c><00:01:48.439><c> pass</c><00:01:48.680><c> in</c><00:01:48.920><c> our</c>

00:01:49.230 --> 00:01:49.240 align:start position:0%
API up here we're going to pass in our
 

00:01:49.240 --> 00:01:50.830 align:start position:0%
API up here we're going to pass in our
the<00:01:49.360><c> authorization</c><00:01:50.119><c> and</c><00:01:50.240><c> then</c><00:01:50.360><c> the</c><00:01:50.479><c> payload</c>

00:01:50.830 --> 00:01:50.840 align:start position:0%
the authorization and then the payload
 

00:01:50.840 --> 00:01:52.429 align:start position:0%
the authorization and then the payload
and<00:01:50.960><c> this</c><00:01:51.040><c> is</c><00:01:51.320><c> just</c><00:01:51.479><c> the</c><00:01:51.680><c> message</c><00:01:52.119><c> or</c><00:01:52.280><c> the</c>

00:01:52.429 --> 00:01:52.439 align:start position:0%
and this is just the message or the
 

00:01:52.439 --> 00:01:54.429 align:start position:0%
and this is just the message or the
prompt<00:01:53.000><c> that</c><00:01:53.119><c> we're</c><00:01:53.320><c> sending</c><00:01:53.880><c> to</c><00:01:53.960><c> the</c><00:01:54.079><c> server</c>

00:01:54.429 --> 00:01:54.439 align:start position:0%
prompt that we're sending to the server
 

00:01:54.439 --> 00:01:56.190 align:start position:0%
prompt that we're sending to the server
so<00:01:54.600><c> it</c><00:01:54.719><c> gives</c><00:01:54.880><c> us</c><00:01:55.079><c> back</c><00:01:55.280><c> an</c><00:01:55.439><c> audio</c><00:01:55.759><c> file</c><00:01:56.119><c> and</c>

00:01:56.190 --> 00:01:56.200 align:start position:0%
so it gives us back an audio file and
 

00:01:56.200 --> 00:01:58.510 align:start position:0%
so it gives us back an audio file and
then<00:01:56.360><c> we</c><00:01:56.479><c> just</c><00:01:56.759><c> return</c><00:01:57.159><c> response.</c><00:01:58.000><c> content</c>

00:01:58.510 --> 00:01:58.520 align:start position:0%
then we just return response. content
 

00:01:58.520 --> 00:01:59.950 align:start position:0%
then we just return response. content
which<00:01:58.719><c> is</c><00:01:58.840><c> the</c><00:01:58.960><c> audio</c><00:01:59.280><c> file</c><00:01:59.520><c> the</c><00:01:59.600><c> next</c><00:01:59.880><c> thing</c>

00:01:59.950 --> 00:01:59.960 align:start position:0%
which is the audio file the next thing
 

00:01:59.960 --> 00:02:02.069 align:start position:0%
which is the audio file the next thing
we<00:02:00.079><c> need</c><00:02:00.280><c> is</c><00:02:00.399><c> the</c><00:02:00.520><c> llm</c><00:02:01.079><c> config</c><00:02:01.600><c> so</c><00:02:01.719><c> we</c><00:02:01.840><c> have</c><00:02:01.960><c> a</c>

00:02:02.069 --> 00:02:02.079 align:start position:0%
we need is the llm config so we have a
 

00:02:02.079 --> 00:02:03.950 align:start position:0%
we need is the llm config so we have a
config<00:02:02.479><c> list</c><00:02:02.759><c> property</c><00:02:03.320><c> and</c><00:02:03.439><c> then</c><00:02:03.560><c> we</c><00:02:03.680><c> get</c><00:02:03.840><c> the</c>

00:02:03.950 --> 00:02:03.960 align:start position:0%
config list property and then we get the
 

00:02:03.960 --> 00:02:06.789 align:start position:0%
config list property and then we get the
config<00:02:04.320><c> list</c><00:02:04.600><c> from</c><00:02:04.920><c> Json</c><00:02:05.799><c> which</c><00:02:06.039><c> I</c><00:02:06.240><c> have</c><00:02:06.560><c> over</c>

00:02:06.789 --> 00:02:06.799 align:start position:0%
config list from Json which I have over
 

00:02:06.799 --> 00:02:09.150 align:start position:0%
config list from Json which I have over
here<00:02:07.240><c> at</c><00:02:07.399><c> the</c><00:02:07.520><c> open</c><00:02:08.000><c> andig</c><00:02:08.239><c> list.</c><00:02:08.560><c> Json</c><00:02:09.000><c> and</c>

00:02:09.150 --> 00:02:09.160 align:start position:0%
here at the open andig list. Json and
 

00:02:09.160 --> 00:02:10.869 align:start position:0%
here at the open andig list. Json and
inside<00:02:09.479><c> of</c><00:02:09.640><c> here</c><00:02:10.039><c> I</c><00:02:10.160><c> just</c><00:02:10.280><c> have</c><00:02:10.399><c> the</c><00:02:10.520><c> model</c><00:02:10.759><c> in</c>

00:02:10.869 --> 00:02:10.879 align:start position:0%
inside of here I just have the model in
 

00:02:10.879 --> 00:02:12.430 align:start position:0%
inside of here I just have the model in
the<00:02:11.000><c> API</c><00:02:11.400><c> key</c><00:02:11.760><c> if</c><00:02:11.840><c> you</c><00:02:11.920><c> want</c><00:02:12.000><c> to</c><00:02:12.120><c> use</c><00:02:12.280><c> something</c>

00:02:12.430 --> 00:02:12.440 align:start position:0%
the API key if you want to use something
 

00:02:12.440 --> 00:02:14.270 align:start position:0%
the API key if you want to use something
like<00:02:12.560><c> LM</c><00:02:12.840><c> Studio</c><00:02:13.160><c> or</c><00:02:13.280><c> olama</c><00:02:13.760><c> you</c><00:02:13.920><c> also</c><00:02:14.120><c> need</c>

00:02:14.270 --> 00:02:14.280 align:start position:0%
like LM Studio or olama you also need
 

00:02:14.280 --> 00:02:16.229 align:start position:0%
like LM Studio or olama you also need
the<00:02:14.440><c> base</c><00:02:14.720><c> URL</c><00:02:15.239><c> property</c><00:02:15.560><c> here</c><00:02:15.879><c> with</c><00:02:16.120><c> the</c>

00:02:16.229 --> 00:02:16.239 align:start position:0%
the base URL property here with the
 

00:02:16.239 --> 00:02:18.390 align:start position:0%
the base URL property here with the
Local<00:02:16.560><c> Host</c><00:02:16.879><c> URL</c><00:02:17.440><c> and</c><00:02:17.560><c> if</c><00:02:17.640><c> you're</c><00:02:17.720><c> using</c><00:02:17.920><c> olama</c>

00:02:18.390 --> 00:02:18.400 align:start position:0%
Local Host URL and if you're using olama
 

00:02:18.400 --> 00:02:19.990 align:start position:0%
Local Host URL and if you're using olama
you<00:02:18.519><c> also</c><00:02:18.680><c> need</c><00:02:18.840><c> to</c><00:02:18.959><c> give</c><00:02:19.160><c> the</c><00:02:19.360><c> exact</c><00:02:19.680><c> model</c>

00:02:19.990 --> 00:02:20.000 align:start position:0%
you also need to give the exact model
 

00:02:20.000 --> 00:02:21.350 align:start position:0%
you also need to give the exact model
that<00:02:20.120><c> you're</c><00:02:20.280><c> using</c><00:02:20.599><c> and</c><00:02:20.720><c> then</c><00:02:20.840><c> we</c><00:02:20.959><c> create</c><00:02:21.280><c> the</c>

00:02:21.350 --> 00:02:21.360 align:start position:0%
that you're using and then we create the
 

00:02:21.360 --> 00:02:22.949 align:start position:0%
that you're using and then we create the
two<00:02:21.560><c> agents</c><00:02:21.879><c> we</c><00:02:21.959><c> have</c><00:02:22.040><c> an</c><00:02:22.160><c> assistant</c><00:02:22.519><c> agent</c>

00:02:22.949 --> 00:02:22.959 align:start position:0%
two agents we have an assistant agent
 

00:02:22.959 --> 00:02:24.309 align:start position:0%
two agents we have an assistant agent
where<00:02:23.160><c> we</c><00:02:23.319><c> just</c><00:02:23.519><c> give</c><00:02:23.640><c> it</c><00:02:23.800><c> the</c><00:02:23.920><c> name</c><00:02:24.080><c> in</c><00:02:24.239><c> the</c>

00:02:24.309 --> 00:02:24.319 align:start position:0%
where we just give it the name in the
 

00:02:24.319 --> 00:02:26.430 align:start position:0%
where we just give it the name in the
llm<00:02:24.800><c> config</c><00:02:25.400><c> and</c><00:02:25.519><c> if</c><00:02:25.599><c> you</c><00:02:25.720><c> didn't</c><00:02:25.959><c> know</c><00:02:26.239><c> if</c><00:02:26.360><c> you</c>

00:02:26.430 --> 00:02:26.440 align:start position:0%
llm config and if you didn't know if you
 

00:02:26.440 --> 00:02:27.830 align:start position:0%
llm config and if you didn't know if you
don't<00:02:26.640><c> give</c><00:02:26.760><c> it</c><00:02:26.840><c> a</c><00:02:26.959><c> system</c><00:02:27.280><c> message</c><00:02:27.560><c> or</c>

00:02:27.830 --> 00:02:27.840 align:start position:0%
don't give it a system message or
 

00:02:27.840 --> 00:02:29.550 align:start position:0%
don't give it a system message or
description<00:02:28.640><c> you</c><00:02:28.720><c> can</c><00:02:28.879><c> just</c><00:02:29.000><c> come</c><00:02:29.239><c> inside</c><00:02:29.480><c> of</c>

00:02:29.550 --> 00:02:29.560 align:start position:0%
description you can just come inside of
 

00:02:29.560 --> 00:02:31.430 align:start position:0%
description you can just come inside of
this<00:02:29.879><c> assistant</c><00:02:30.239><c> agent</c><00:02:30.640><c> it</c><00:02:30.800><c> has</c><00:02:30.920><c> a</c><00:02:31.080><c> default</c>

00:02:31.430 --> 00:02:31.440 align:start position:0%
this assistant agent it has a default
 

00:02:31.440 --> 00:02:33.150 align:start position:0%
this assistant agent it has a default
system<00:02:31.760><c> message</c><00:02:32.080><c> and</c><00:02:32.280><c> default</c><00:02:32.640><c> description</c>

00:02:33.150 --> 00:02:33.160 align:start position:0%
system message and default description
 

00:02:33.160 --> 00:02:35.150 align:start position:0%
system message and default description
if<00:02:33.400><c> we</c><00:02:33.599><c> don't</c><00:02:33.959><c> give</c><00:02:34.080><c> it</c><00:02:34.239><c> one</c><00:02:34.640><c> or</c><00:02:34.760><c> if</c><00:02:34.840><c> we</c><00:02:34.959><c> don't</c>

00:02:35.150 --> 00:02:35.160 align:start position:0%
if we don't give it one or if we don't
 

00:02:35.160 --> 00:02:36.509 align:start position:0%
if we don't give it one or if we don't
provide<00:02:35.440><c> one</c><00:02:35.599><c> for</c><00:02:35.800><c> it</c><00:02:36.040><c> and</c><00:02:36.120><c> then</c><00:02:36.239><c> we</c><00:02:36.319><c> create</c>

00:02:36.509 --> 00:02:36.519 align:start position:0%
provide one for it and then we create
 

00:02:36.519 --> 00:02:38.990 align:start position:0%
provide one for it and then we create
the<00:02:36.640><c> user</c><00:02:36.959><c> proxy</c><00:02:37.319><c> agent</c><00:02:37.720><c> we</c><00:02:38.080><c> don't</c><00:02:38.239><c> want</c><00:02:38.440><c> to</c>

00:02:38.990 --> 00:02:39.000 align:start position:0%
the user proxy agent we don't want to
 

00:02:39.000 --> 00:02:40.190 align:start position:0%
the user proxy agent we don't want to
execute<00:02:39.360><c> any</c><00:02:39.519><c> code</c><00:02:39.720><c> because</c><00:02:39.879><c> there</c><00:02:39.959><c> won't</c><00:02:40.120><c> be</c>

00:02:40.190 --> 00:02:40.200 align:start position:0%
execute any code because there won't be
 

00:02:40.200 --> 00:02:41.949 align:start position:0%
execute any code because there won't be
any<00:02:40.360><c> code</c><00:02:40.599><c> to</c><00:02:40.879><c> execute</c><00:02:41.400><c> and</c><00:02:41.519><c> now</c><00:02:41.640><c> for</c><00:02:41.800><c> what's</c>

00:02:41.949 --> 00:02:41.959 align:start position:0%
any code to execute and now for what's
 

00:02:41.959 --> 00:02:43.390 align:start position:0%
any code to execute and now for what's
going<00:02:42.040><c> to</c><00:02:42.080><c> be</c><00:02:42.200><c> doing</c><00:02:42.400><c> most</c><00:02:42.560><c> of</c><00:02:42.680><c> the</c><00:02:42.800><c> work</c><00:02:43.080><c> so</c><00:02:43.239><c> I</c>

00:02:43.390 --> 00:02:43.400 align:start position:0%
going to be doing most of the work so I
 

00:02:43.400 --> 00:02:45.430 align:start position:0%
going to be doing most of the work so I
have<00:02:43.519><c> the</c><00:02:43.640><c> user</c><00:02:43.959><c> proxy</c><00:02:44.440><c> so</c><00:02:44.599><c> we</c><00:02:44.720><c> register</c><00:02:45.239><c> this</c>

00:02:45.430 --> 00:02:45.440 align:start position:0%
have the user proxy so we register this
 

00:02:45.440 --> 00:02:47.470 align:start position:0%
have the user proxy so we register this
function<00:02:45.879><c> call</c><00:02:46.120><c> to</c><00:02:46.280><c> the</c><00:02:46.360><c> user</c><00:02:46.720><c> proxy</c><00:02:47.120><c> who</c><00:02:47.280><c> will</c>

00:02:47.470 --> 00:02:47.480 align:start position:0%
function call to the user proxy who will
 

00:02:47.480 --> 00:02:48.949 align:start position:0%
function call to the user proxy who will
end<00:02:47.640><c> up</c><00:02:47.879><c> actually</c><00:02:48.159><c> executing</c><00:02:48.560><c> it</c><00:02:48.760><c> then</c><00:02:48.840><c> we're</c>

00:02:48.949 --> 00:02:48.959 align:start position:0%
end up actually executing it then we're
 

00:02:48.959 --> 00:02:50.589 align:start position:0%
end up actually executing it then we're
going<00:02:49.040><c> to</c><00:02:49.080><c> say</c><00:02:49.200><c> assistant.</c><00:02:49.640><c> register</c><00:02:49.920><c> for</c><00:02:50.040><c> llm</c>

00:02:50.589 --> 00:02:50.599 align:start position:0%
going to say assistant. register for llm
 

00:02:50.599 --> 00:02:52.270 align:start position:0%
going to say assistant. register for llm
because<00:02:50.800><c> this</c><00:02:51.000><c> the</c><00:02:51.159><c> AI</c><00:02:51.519><c> agent</c><00:02:51.879><c> that</c><00:02:51.959><c> will</c><00:02:52.080><c> be</c>

00:02:52.270 --> 00:02:52.280 align:start position:0%
because this the AI agent that will be
 

00:02:52.280 --> 00:02:53.990 align:start position:0%
because this the AI agent that will be
talking<00:02:52.800><c> with</c><00:02:52.959><c> the</c><00:02:53.120><c> llm</c><00:02:53.599><c> and</c><00:02:53.680><c> then</c><00:02:53.800><c> we</c><00:02:53.879><c> just</c>

00:02:53.990 --> 00:02:54.000 align:start position:0%
talking with the llm and then we just
 

00:02:54.000 --> 00:02:56.229 align:start position:0%
talking with the llm and then we just
Define<00:02:54.280><c> the</c><00:02:54.400><c> function</c><00:02:54.720><c> called</c><00:02:54.920><c> wror</c><00:02:55.560><c> message</c>

00:02:56.229 --> 00:02:56.239 align:start position:0%
Define the function called wror message
 

00:02:56.239 --> 00:02:57.589 align:start position:0%
Define the function called wror message
uh<00:02:56.319><c> we</c><00:02:56.440><c> take</c><00:02:56.599><c> in</c><00:02:56.800><c> one</c><00:02:56.959><c> parameter</c><00:02:57.360><c> called</c>

00:02:57.589 --> 00:02:57.599 align:start position:0%
uh we take in one parameter called
 

00:02:57.599 --> 00:02:59.710 align:start position:0%
uh we take in one parameter called
message<00:02:58.159><c> we</c><00:02:58.280><c> use</c><00:02:58.599><c> annotated</c><00:02:59.120><c> here</c><00:02:59.440><c> the</c><00:02:59.560><c> fun</c>

00:02:59.710 --> 00:02:59.720 align:start position:0%
message we use annotated here the fun
 

00:02:59.720 --> 00:03:01.070 align:start position:0%
message we use annotated here the fun
function<00:02:59.959><c> calling</c><00:03:00.200><c> with</c><00:03:00.280><c> autogen</c><00:03:00.760><c> kind</c><00:03:00.879><c> of</c>

00:03:01.070 --> 00:03:01.080 align:start position:0%
function calling with autogen kind of
 

00:03:01.080 --> 00:03:02.830 align:start position:0%
function calling with autogen kind of
forces<00:03:01.400><c> us</c><00:03:01.560><c> to</c><00:03:01.680><c> do</c><00:03:01.879><c> this</c><00:03:02.159><c> this</c><00:03:02.239><c> is</c><00:03:02.360><c> saying</c><00:03:02.680><c> this</c>

00:03:02.830 --> 00:03:02.840 align:start position:0%
forces us to do this this is saying this
 

00:03:02.840 --> 00:03:04.149 align:start position:0%
forces us to do this this is saying this
has<00:03:02.959><c> to</c><00:03:03.040><c> be</c><00:03:03.159><c> a</c><00:03:03.280><c> string</c><00:03:03.760><c> and</c><00:03:03.840><c> this</c><00:03:03.920><c> is</c><00:03:04.040><c> the</c>

00:03:04.149 --> 00:03:04.159 align:start position:0%
has to be a string and this is the
 

00:03:04.159 --> 00:03:05.830 align:start position:0%
has to be a string and this is the
description<00:03:04.519><c> of</c><00:03:04.640><c> what</c><00:03:04.720><c> the</c><00:03:04.879><c> string</c><00:03:05.360><c> is</c><00:03:05.720><c> and</c>

00:03:05.830 --> 00:03:05.840 align:start position:0%
description of what the string is and
 

00:03:05.840 --> 00:03:07.190 align:start position:0%
description of what the string is and
then<00:03:05.920><c> we're</c><00:03:06.040><c> going</c><00:03:06.120><c> to</c><00:03:06.440><c> return</c><00:03:06.680><c> a</c><00:03:06.799><c> string</c><00:03:07.080><c> as</c>

00:03:07.190 --> 00:03:07.200 align:start position:0%
then we're going to return a string as
 

00:03:07.200 --> 00:03:08.630 align:start position:0%
then we're going to return a string as
well<00:03:07.480><c> we</c><00:03:07.599><c> don't</c><00:03:07.840><c> have</c><00:03:07.959><c> to</c><00:03:08.159><c> return</c><00:03:08.360><c> anything</c>

00:03:08.630 --> 00:03:08.640 align:start position:0%
well we don't have to return anything
 

00:03:08.640 --> 00:03:10.309 align:start position:0%
well we don't have to return anything
here<00:03:08.799><c> but</c><00:03:08.920><c> you'll</c><00:03:09.080><c> just</c><00:03:09.239><c> get</c><00:03:09.400><c> warnings</c><00:03:10.159><c> and</c>

00:03:10.309 --> 00:03:10.319 align:start position:0%
here but you'll just get warnings and
 

00:03:10.319 --> 00:03:11.910 align:start position:0%
here but you'll just get warnings and
just<00:03:10.400><c> to</c><00:03:10.720><c> get</c><00:03:10.879><c> rid</c><00:03:11.000><c> of</c><00:03:11.159><c> those</c><00:03:11.319><c> warnings</c><00:03:11.720><c> if</c><00:03:11.799><c> you</c>

00:03:11.910 --> 00:03:11.920 align:start position:0%
just to get rid of those warnings if you
 

00:03:11.920 --> 00:03:13.869 align:start position:0%
just to get rid of those warnings if you
don't<00:03:12.120><c> have</c><00:03:12.280><c> this</c><00:03:12.760><c> I</c><00:03:12.879><c> just</c><00:03:13.000><c> simply</c><00:03:13.440><c> return</c><00:03:13.760><c> the</c>

00:03:13.869 --> 00:03:13.879 align:start position:0%
don't have this I just simply return the
 

00:03:13.879 --> 00:03:15.430 align:start position:0%
don't have this I just simply return the
message<00:03:14.239><c> that</c><00:03:14.360><c> we</c><00:03:14.519><c> are</c><00:03:14.720><c> given</c><00:03:15.200><c> because</c><00:03:15.319><c> we're</c>

00:03:15.430 --> 00:03:15.440 align:start position:0%
message that we are given because we're
 

00:03:15.440 --> 00:03:16.430 align:start position:0%
message that we are given because we're
not<00:03:15.519><c> doing</c><00:03:15.799><c> anything</c><00:03:16.000><c> else</c><00:03:16.159><c> with</c><00:03:16.280><c> it</c>

00:03:16.430 --> 00:03:16.440 align:start position:0%
not doing anything else with it
 

00:03:16.440 --> 00:03:17.670 align:start position:0%
not doing anything else with it
afterwards<00:03:16.879><c> we're</c><00:03:17.000><c> going</c><00:03:17.080><c> to</c><00:03:17.159><c> say</c><00:03:17.319><c> audio</c>

00:03:17.670 --> 00:03:17.680 align:start position:0%
afterwards we're going to say audio
 

00:03:17.680 --> 00:03:19.670 align:start position:0%
afterwards we're going to say audio
equals<00:03:18.159><c> the</c><00:03:18.360><c> query</c><00:03:18.760><c> function</c><00:03:19.400><c> that</c><00:03:19.519><c> we</c>

00:03:19.670 --> 00:03:19.680 align:start position:0%
equals the query function that we
 

00:03:19.680 --> 00:03:21.390 align:start position:0%
equals the query function that we
created<00:03:20.080><c> Above</c><00:03:20.400><c> So</c><00:03:20.599><c> up</c><00:03:20.760><c> here</c><00:03:21.000><c> we're</c><00:03:21.159><c> passing</c>

00:03:21.390 --> 00:03:21.400 align:start position:0%
created Above So up here we're passing
 

00:03:21.400 --> 00:03:22.910 align:start position:0%
created Above So up here we're passing
in<00:03:21.480><c> the</c><00:03:21.640><c> payload</c><00:03:22.040><c> here</c><00:03:22.239><c> so</c><00:03:22.400><c> the</c><00:03:22.519><c> message</c><00:03:22.840><c> that</c>

00:03:22.910 --> 00:03:22.920 align:start position:0%
in the payload here so the message that
 

00:03:22.920 --> 00:03:24.710 align:start position:0%
in the payload here so the message that
we're<00:03:23.120><c> passing</c><00:03:23.360><c> into</c><00:03:23.720><c> that</c><00:03:23.840><c> query</c><00:03:24.280><c> function</c>

00:03:24.710 --> 00:03:24.720 align:start position:0%
we're passing into that query function
 

00:03:24.720 --> 00:03:25.910 align:start position:0%
we're passing into that query function
is<00:03:24.879><c> what's</c><00:03:25.000><c> going</c><00:03:25.120><c> to</c><00:03:25.239><c> give</c><00:03:25.360><c> us</c><00:03:25.519><c> the</c><00:03:25.599><c> audio</c>

00:03:25.910 --> 00:03:25.920 align:start position:0%
is what's going to give us the audio
 

00:03:25.920 --> 00:03:27.670 align:start position:0%
is what's going to give us the audio
file<00:03:26.159><c> back</c><00:03:26.440><c> what</c><00:03:26.519><c> I</c><00:03:26.599><c> want</c><00:03:26.680><c> to</c><00:03:26.760><c> do</c><00:03:26.879><c> is</c><00:03:27.000><c> save</c><00:03:27.319><c> that</c>

00:03:27.670 --> 00:03:27.680 align:start position:0%
file back what I want to do is save that
 

00:03:27.680 --> 00:03:29.789 align:start position:0%
file back what I want to do is save that
audio<00:03:28.000><c> to</c><00:03:28.159><c> a</c><00:03:28.280><c> local</c><00:03:28.599><c> file</c><00:03:29.040><c> so</c><00:03:29.159><c> you</c><00:03:29.280><c> say</c><00:03:29.439><c> with</c>

00:03:29.789 --> 00:03:29.799 align:start position:0%
audio to a local file so you say with
 

00:03:29.799 --> 00:03:32.509 align:start position:0%
audio to a local file so you say with
open<00:03:30.360><c> I</c><00:03:30.519><c> give</c><00:03:30.680><c> it</c><00:03:30.879><c> a</c><00:03:31.080><c> file</c><00:03:31.439><c> name</c><00:03:32.200><c> uh</c><00:03:32.319><c> you</c><00:03:32.400><c> want</c>

00:03:32.509 --> 00:03:32.519 align:start position:0%
open I give it a file name uh you want
 

00:03:32.519 --> 00:03:34.390 align:start position:0%
open I give it a file name uh you want
to<00:03:32.760><c> write</c><00:03:33.000><c> all</c><00:03:33.159><c> the</c><00:03:33.319><c> bytes</c><00:03:33.640><c> you</c><00:03:33.760><c> want</c><00:03:33.879><c> to</c><00:03:33.959><c> say</c><00:03:34.120><c> w</c>

00:03:34.390 --> 00:03:34.400 align:start position:0%
to write all the bytes you want to say w
 

00:03:34.400 --> 00:03:36.110 align:start position:0%
to write all the bytes you want to say w
you<00:03:34.439><c> need</c><00:03:34.560><c> to</c><00:03:34.640><c> say</c><00:03:34.760><c> WB</c><00:03:35.159><c> for</c><00:03:35.319><c> writing</c><00:03:35.599><c> the</c><00:03:35.840><c> bite</c>

00:03:36.110 --> 00:03:36.120 align:start position:0%
you need to say WB for writing the bite
 

00:03:36.120 --> 00:03:37.949 align:start position:0%
you need to say WB for writing the bite
and<00:03:36.200><c> then</c><00:03:36.280><c> you</c><00:03:36.400><c> say</c><00:03:36.560><c> file.</c><00:03:37.120><c> write</c><00:03:37.439><c> audio</c><00:03:37.840><c> so</c>

00:03:37.949 --> 00:03:37.959 align:start position:0%
and then you say file. write audio so
 

00:03:37.959 --> 00:03:40.229 align:start position:0%
and then you say file. write audio so
this<00:03:38.080><c> is</c><00:03:38.200><c> just</c><00:03:38.319><c> going</c><00:03:38.400><c> to</c><00:03:38.599><c> save</c><00:03:39.360><c> the</c><00:03:39.680><c> file</c><00:03:40.040><c> over</c>

00:03:40.229 --> 00:03:40.239 align:start position:0%
this is just going to save the file over
 

00:03:40.239 --> 00:03:41.589 align:start position:0%
this is just going to save the file over
here<00:03:40.519><c> actually</c><00:03:40.760><c> put</c><00:03:40.879><c> a</c><00:03:41.000><c> two</c><00:03:41.159><c> here</c><00:03:41.280><c> so</c><00:03:41.400><c> we</c><00:03:41.480><c> know</c>

00:03:41.589 --> 00:03:41.599 align:start position:0%
here actually put a two here so we know
 

00:03:41.599 --> 00:03:43.190 align:start position:0%
here actually put a two here so we know
this<00:03:41.680><c> is</c><00:03:41.799><c> a</c><00:03:41.920><c> different</c><00:03:42.200><c> file</c><00:03:42.760><c> then</c><00:03:42.879><c> we</c><00:03:42.959><c> just</c>

00:03:43.190 --> 00:03:43.200 align:start position:0%
this is a different file then we just
 

00:03:43.200 --> 00:03:44.190 align:start position:0%
this is a different file then we just
return<00:03:43.319><c> the</c><00:03:43.439><c> message</c><00:03:43.720><c> and</c><00:03:43.799><c> then</c><00:03:43.920><c> the</c><00:03:44.040><c> last</c>

00:03:44.190 --> 00:03:44.200 align:start position:0%
return the message and then the last
 

00:03:44.200 --> 00:03:45.910 align:start position:0%
return the message and then the last
thing<00:03:44.360><c> is</c><00:03:44.519><c> we</c><00:03:44.599><c> need</c><00:03:44.720><c> to</c><00:03:44.879><c> initiate</c><00:03:45.280><c> the</c><00:03:45.439><c> chat</c>

00:03:45.910 --> 00:03:45.920 align:start position:0%
thing is we need to initiate the chat
 

00:03:45.920 --> 00:03:47.110 align:start position:0%
thing is we need to initiate the chat
and<00:03:46.040><c> the</c><00:03:46.159><c> message</c><00:03:46.480><c> that</c><00:03:46.599><c> we're</c><00:03:46.720><c> going</c><00:03:46.799><c> to</c><00:03:46.920><c> send</c>

00:03:47.110 --> 00:03:47.120 align:start position:0%
and the message that we're going to send
 

00:03:47.120 --> 00:03:48.589 align:start position:0%
and the message that we're going to send
to<00:03:47.239><c> the</c><00:03:47.360><c> assistant</c><00:03:47.680><c> agent</c><00:03:47.959><c> is</c><00:03:48.040><c> to</c><00:03:48.200><c> create</c><00:03:48.439><c> a</c>

00:03:48.589 --> 00:03:48.599 align:start position:0%
to the assistant agent is to create a
 

00:03:48.599 --> 00:03:50.350 align:start position:0%
to the assistant agent is to create a
story<00:03:48.920><c> about</c><00:03:49.159><c> AI</c><00:03:49.480><c> that</c><00:03:49.599><c> has</c><00:03:49.720><c> less</c><00:03:49.879><c> than</c><00:03:50.159><c> 100</c>

00:03:50.350 --> 00:03:50.360 align:start position:0%
story about AI that has less than 100
 

00:03:50.360 --> 00:03:52.350 align:start position:0%
story about AI that has less than 100
words<00:03:51.000><c> make</c><00:03:51.159><c> it</c><00:03:51.360><c> creative</c><00:03:51.799><c> so</c><00:03:51.920><c> I</c><00:03:52.000><c> ran</c><00:03:52.159><c> this</c><00:03:52.280><c> a</c>

00:03:52.350 --> 00:03:52.360 align:start position:0%
words make it creative so I ran this a
 

00:03:52.360 --> 00:03:53.750 align:start position:0%
words make it creative so I ran this a
couple<00:03:52.640><c> times</c><00:03:53.040><c> just</c><00:03:53.159><c> to</c><00:03:53.319><c> get</c><00:03:53.439><c> different</c>

00:03:53.750 --> 00:03:53.760 align:start position:0%
couple times just to get different
 

00:03:53.760 --> 00:03:55.429 align:start position:0%
couple times just to get different
results<00:03:54.480><c> uh</c><00:03:54.599><c> because</c><00:03:54.879><c> the</c><00:03:55.000><c> second</c><00:03:55.200><c> one</c><00:03:55.319><c> here</c>

00:03:55.429 --> 00:03:55.439 align:start position:0%
results uh because the second one here
 

00:03:55.439 --> 00:03:57.030 align:start position:0%
results uh because the second one here
was<00:03:55.599><c> actually</c><00:03:55.799><c> the</c><00:03:55.879><c> same</c><00:03:56.040><c> as</c><00:03:56.200><c> the</c><00:03:56.319><c> first</c><00:03:56.560><c> one</c>

00:03:57.030 --> 00:03:57.040 align:start position:0%
was actually the same as the first one
 

00:03:57.040 --> 00:04:00.429 align:start position:0%
was actually the same as the first one
so<00:03:57.400><c> I</c><00:03:57.599><c> reran</c><00:03:57.959><c> it</c><00:03:58.120><c> changed</c><00:03:58.439><c> the</c><00:03:58.560><c> seed</c><00:03:59.439><c> and</c><00:04:00.000><c> it</c>

00:04:00.429 --> 00:04:00.439 align:start position:0%
so I reran it changed the seed and it
 

00:04:00.439 --> 00:04:02.229 align:start position:0%
so I reran it changed the seed and it
created<00:04:00.799><c> a</c><00:04:00.920><c> new</c><00:04:01.120><c> promp</c><00:04:01.400><c> for</c><00:04:01.599><c> us</c><00:04:01.760><c> or</c><00:04:01.879><c> a</c><00:04:02.000><c> new</c>

00:04:02.229 --> 00:04:02.239 align:start position:0%
created a new promp for us or a new
 

00:04:02.239 --> 00:04:04.229 align:start position:0%
created a new promp for us or a new
basically<00:04:02.599><c> speech</c><00:04:03.200><c> to</c><00:04:03.439><c> where</c><00:04:03.680><c> humans</c><00:04:04.079><c> and</c>

00:04:04.229 --> 00:04:04.239 align:start position:0%
basically speech to where humans and
 

00:04:04.239 --> 00:04:06.390 align:start position:0%
basically speech to where humans and
machines<00:04:04.599><c> exist</c><00:04:04.879><c> an</c><00:04:05.000><c> AI</c><00:04:05.280><c> named</c><00:04:05.599><c> Eve</c><00:04:05.879><c> was</c><00:04:06.000><c> born</c>

00:04:06.390 --> 00:04:06.400 align:start position:0%
machines exist an AI named Eve was born
 

00:04:06.400 --> 00:04:09.110 align:start position:0%
machines exist an AI named Eve was born
we'll<00:04:06.599><c> see</c><00:04:06.799><c> if</c><00:04:07.159><c> that</c><00:04:07.280><c> works</c><00:04:08.120><c> uh</c><00:04:08.480><c> you</c><00:04:08.560><c> can</c><00:04:08.680><c> see</c><00:04:09.040><c> I</c>

00:04:09.110 --> 00:04:09.120 align:start position:0%
we'll see if that works uh you can see I
 

00:04:09.120 --> 00:04:11.390 align:start position:0%
we'll see if that works uh you can see I
got<00:04:09.280><c> the</c><00:04:09.400><c> tool</c><00:04:09.680><c> call</c><00:04:10.120><c> named</c><00:04:10.400><c> right</c><00:04:10.680><c> message</c>

00:04:11.390 --> 00:04:11.400 align:start position:0%
got the tool call named right message
 

00:04:11.400 --> 00:04:14.030 align:start position:0%
got the tool call named right message
the<00:04:11.560><c> message</c><00:04:12.040><c> was</c><00:04:12.319><c> what</c><00:04:12.519><c> the</c><00:04:12.959><c> AI</c><00:04:13.439><c> agent</c><00:04:13.840><c> got</c>

00:04:14.030 --> 00:04:14.040 align:start position:0%
the message was what the AI agent got
 

00:04:14.040 --> 00:04:16.150 align:start position:0%
the message was what the AI agent got
from<00:04:14.200><c> the</c><00:04:14.319><c> llm</c><00:04:15.000><c> and</c><00:04:15.120><c> then</c><00:04:15.280><c> the</c><00:04:15.360><c> user</c><00:04:15.760><c> proxy</c>

00:04:16.150 --> 00:04:16.160 align:start position:0%
from the llm and then the user proxy
 

00:04:16.160 --> 00:04:18.509 align:start position:0%
from the llm and then the user proxy
agent<00:04:16.519><c> is</c><00:04:16.720><c> executing</c><00:04:17.199><c> it</c><00:04:17.600><c> which</c><00:04:17.759><c> means</c><00:04:18.400><c> that</c>

00:04:18.509 --> 00:04:18.519 align:start position:0%
agent is executing it which means that
 

00:04:18.519 --> 00:04:20.670 align:start position:0%
agent is executing it which means that
it<00:04:18.720><c> then</c><00:04:18.919><c> got</c><00:04:19.079><c> the</c><00:04:19.239><c> audio</c><00:04:19.639><c> file</c><00:04:19.959><c> from</c><00:04:20.239><c> hugging</c>

00:04:20.670 --> 00:04:20.680 align:start position:0%
it then got the audio file from hugging
 

00:04:20.680 --> 00:04:22.390 align:start position:0%
it then got the audio file from hugging
face<00:04:20.959><c> inference</c><00:04:21.359><c> server</c><00:04:21.799><c> and</c><00:04:21.919><c> then</c><00:04:22.040><c> saved</c><00:04:22.280><c> it</c>

00:04:22.390 --> 00:04:22.400 align:start position:0%
face inference server and then saved it
 

00:04:22.400 --> 00:04:23.950 align:start position:0%
face inference server and then saved it
locally<00:04:22.800><c> let's</c><00:04:22.960><c> see</c><00:04:23.080><c> how</c><00:04:23.199><c> it</c><00:04:23.320><c> sounds</c><00:04:23.759><c> in</c><00:04:23.840><c> a</c>

00:04:23.950 --> 00:04:23.960 align:start position:0%
locally let's see how it sounds in a
 

00:04:23.960 --> 00:04:26.990 align:start position:0%
locally let's see how it sounds in a
world<00:04:24.280><c> where</c><00:04:24.520><c> humans</c><00:04:24.919><c> and</c><00:04:25.120><c> machines</c><00:04:25.759><c> coist</c><00:04:26.759><c> an</c>

00:04:26.990 --> 00:04:27.000 align:start position:0%
world where humans and machines coist an
 

00:04:27.000 --> 00:04:30.510 align:start position:0%
world where humans and machines coist an
i<00:04:27.320><c> named</c><00:04:27.720><c> Heath</c><00:04:28.080><c> was</c><00:04:28.240><c> born</c><00:04:29.000><c> unlike</c><00:04:30.039><c> Eve</c><00:04:30.320><c> was</c>

00:04:30.510 --> 00:04:30.520 align:start position:0%
i named Heath was born unlike Eve was
 

00:04:30.520 --> 00:04:33.550 align:start position:0%
i named Heath was born unlike Eve was
curious<00:04:31.400><c> always</c><00:04:31.840><c> questioning</c><00:04:32.320><c> her</c><00:04:32.600><c> existence</c>

00:04:33.550 --> 00:04:33.560 align:start position:0%
curious always questioning her existence
 

00:04:33.560 --> 00:04:35.830 align:start position:0%
curious always questioning her existence
okay<00:04:33.880><c> awesome</c><00:04:34.320><c> and</c><00:04:34.520><c> it</c><00:04:34.680><c> worked</c><00:04:35.080><c> we</c><00:04:35.280><c> got</c><00:04:35.560><c> our</c>

00:04:35.830 --> 00:04:35.840 align:start position:0%
okay awesome and it worked we got our
 

00:04:35.840 --> 00:04:37.990 align:start position:0%
okay awesome and it worked we got our
audio<00:04:36.240><c> file</c><00:04:36.880><c> okay</c><00:04:37.120><c> that's</c><00:04:37.320><c> exactly</c><00:04:37.759><c> what</c><00:04:37.880><c> we</c>

00:04:37.990 --> 00:04:38.000 align:start position:0%
audio file okay that's exactly what we
 

00:04:38.000 --> 00:04:39.790 align:start position:0%
audio file okay that's exactly what we
wanted<00:04:38.360><c> we</c><00:04:38.440><c> can</c><00:04:38.639><c> now</c><00:04:38.880><c> create</c><00:04:39.160><c> a</c><00:04:39.320><c> text</c><00:04:39.600><c> to</c>

00:04:39.790 --> 00:04:39.800 align:start position:0%
wanted we can now create a text to
 

00:04:39.800 --> 00:04:41.909 align:start position:0%
wanted we can now create a text to
speech<00:04:40.320><c> using</c><00:04:40.680><c> AI</c><00:04:41.000><c> agents</c><00:04:41.440><c> now</c><00:04:41.639><c> there</c><00:04:41.759><c> could</c>

00:04:41.909 --> 00:04:41.919 align:start position:0%
speech using AI agents now there could
 

00:04:41.919 --> 00:04:43.270 align:start position:0%
speech using AI agents now there could
be<00:04:42.080><c> more</c><00:04:42.280><c> we</c><00:04:42.400><c> can</c><00:04:42.520><c> do</c><00:04:42.720><c> with</c><00:04:42.880><c> this</c><00:04:43.080><c> right</c><00:04:43.199><c> this</c>

00:04:43.270 --> 00:04:43.280 align:start position:0%
be more we can do with this right this
 

00:04:43.280 --> 00:04:46.270 align:start position:0%
be more we can do with this right this
is<00:04:43.440><c> just</c><00:04:43.680><c> one</c><00:04:44.000><c> step</c><00:04:44.360><c> of</c><00:04:44.880><c> a</c><00:04:45.080><c> potential</c><00:04:45.639><c> process</c>

00:04:46.270 --> 00:04:46.280 align:start position:0%
is just one step of a potential process
 

00:04:46.280 --> 00:04:47.870 align:start position:0%
is just one step of a potential process
we<00:04:46.400><c> could</c><00:04:46.680><c> have</c><00:04:47.000><c> we</c><00:04:47.120><c> could</c><00:04:47.240><c> have</c><00:04:47.400><c> something</c>

00:04:47.870 --> 00:04:47.880 align:start position:0%
we could have we could have something
 

00:04:47.880 --> 00:04:50.310 align:start position:0%
we could have we could have something
describe<00:04:48.320><c> an</c><00:04:48.520><c> image</c><00:04:49.039><c> then</c><00:04:49.280><c> take</c><00:04:49.520><c> that</c><00:04:49.720><c> image</c>

00:04:50.310 --> 00:04:50.320 align:start position:0%
describe an image then take that image
 

00:04:50.320 --> 00:04:51.710 align:start position:0%
describe an image then take that image
put<00:04:50.440><c> it</c><00:04:50.639><c> through</c><00:04:50.880><c> something</c><00:04:51.160><c> like</c><00:04:51.320><c> this</c><00:04:51.520><c> text</c>

00:04:51.710 --> 00:04:51.720 align:start position:0%
put it through something like this text
 

00:04:51.720 --> 00:04:53.909 align:start position:0%
put it through something like this text
of<00:04:51.919><c> speech</c><00:04:52.280><c> here</c><00:04:52.680><c> and</c><00:04:52.800><c> then</c><00:04:52.960><c> we</c><00:04:53.080><c> can</c><00:04:53.320><c> overlay</c>

00:04:53.909 --> 00:04:53.919 align:start position:0%
of speech here and then we can overlay
 

00:04:53.919 --> 00:04:55.270 align:start position:0%
of speech here and then we can overlay
that<00:04:54.080><c> image</c><00:04:54.360><c> with</c><00:04:54.520><c> that</c><00:04:54.639><c> speech</c><00:04:54.919><c> so</c><00:04:55.039><c> we</c><00:04:55.160><c> have</c>

00:04:55.270 --> 00:04:55.280 align:start position:0%
that image with that speech so we have
 

00:04:55.280 --> 00:04:57.510 align:start position:0%
that image with that speech so we have
an<00:04:55.479><c> AI</c><00:04:55.840><c> talking</c><00:04:56.240><c> about</c><00:04:56.520><c> the</c><00:04:56.680><c> image</c><00:04:57.199><c> but</c><00:04:57.360><c> now</c>

00:04:57.510 --> 00:04:57.520 align:start position:0%
an AI talking about the image but now
 

00:04:57.520 --> 00:04:58.749 align:start position:0%
an AI talking about the image but now
you<00:04:57.639><c> know</c><00:04:58.000><c> but</c><00:04:58.120><c> now</c><00:04:58.240><c> you</c><00:04:58.360><c> know</c><00:04:58.479><c> how</c><00:04:58.560><c> to</c>

00:04:58.749 --> 00:04:58.759 align:start position:0%
you know but now you know how to
 

00:04:58.759 --> 00:05:01.469 align:start position:0%
you know but now you know how to
integrate<00:04:59.199><c> a</c><00:04:59.680><c> gen</c><00:05:00.039><c> with</c><00:05:00.320><c> a</c><00:05:00.520><c> function</c><00:05:00.880><c> call</c><00:05:01.360><c> and</c>

00:05:01.469 --> 00:05:01.479 align:start position:0%
integrate a gen with a function call and
 

00:05:01.479 --> 00:05:03.870 align:start position:0%
integrate a gen with a function call and
a<00:05:01.680><c> hugging</c><00:05:02.120><c> face</c><00:05:02.520><c> inference</c><00:05:02.919><c> server</c><00:05:03.560><c> using</c>

00:05:03.870 --> 00:05:03.880 align:start position:0%
a hugging face inference server using
 

00:05:03.880 --> 00:05:05.350 align:start position:0%
a hugging face inference server using
textas<00:05:04.240><c> speech</c><00:05:04.479><c> model</c><00:05:04.800><c> we</c><00:05:04.919><c> did</c><00:05:05.080><c> the</c><00:05:05.199><c> same</c>

00:05:05.350 --> 00:05:05.360 align:start position:0%
textas speech model we did the same
 

00:05:05.360 --> 00:05:07.390 align:start position:0%
textas speech model we did the same
thing<00:05:05.479><c> in</c><00:05:05.680><c> last</c><00:05:05.880><c> video</c><00:05:06.120><c> for</c><00:05:06.320><c> the</c><00:05:06.479><c> text</c><00:05:06.840><c> image</c>

00:05:07.390 --> 00:05:07.400 align:start position:0%
thing in last video for the text image
 

00:05:07.400 --> 00:05:09.870 align:start position:0%
thing in last video for the text image
this<00:05:07.520><c> is</c><00:05:07.720><c> day</c><00:05:07.880><c> 11</c><00:05:08.360><c> we</c><00:05:08.520><c> got</c><00:05:08.960><c> a</c><00:05:09.080><c> long</c><00:05:09.280><c> ways</c><00:05:09.440><c> to</c><00:05:09.600><c> go</c>

00:05:09.870 --> 00:05:09.880 align:start position:0%
this is day 11 we got a long ways to go
 

00:05:09.880 --> 00:05:11.749 align:start position:0%
this is day 11 we got a long ways to go
this<00:05:10.000><c> month</c><00:05:10.520><c> but</c><00:05:10.639><c> we're</c><00:05:10.840><c> getting</c><00:05:11.120><c> there</c><00:05:11.639><c> thank</c>

00:05:11.749 --> 00:05:11.759 align:start position:0%
this month but we're getting there thank
 

00:05:11.759 --> 00:05:12.950 align:start position:0%
this month but we're getting there thank
you<00:05:11.840><c> for</c><00:05:12.000><c> sticking</c><00:05:12.280><c> with</c><00:05:12.400><c> me</c><00:05:12.639><c> like</c><00:05:12.840><c> And</c>

00:05:12.950 --> 00:05:12.960 align:start position:0%
you for sticking with me like And
 

00:05:12.960 --> 00:05:16.360 align:start position:0%
you for sticking with me like And
subscribe<00:05:13.400><c> and</c><00:05:13.520><c> I'll</c><00:05:13.639><c> see</c><00:05:13.759><c> you</c><00:05:13.919><c> next</c><00:05:14.080><c> video</c>

