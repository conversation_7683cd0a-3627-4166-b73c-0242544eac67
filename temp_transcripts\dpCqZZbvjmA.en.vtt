WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.869 align:start position:0%
 
all<00:00:00.539><c> little</c><00:00:00.719><c> chips</c><00:00:01.140><c> began</c><00:00:01.439><c> as</c><00:00:01.620><c> big</c><00:00:01.800><c> chips</c><00:00:02.159><c> the</c>

00:00:02.869 --> 00:00:02.879 align:start position:0%
all little chips began as big chips the
 

00:00:02.879 --> 00:00:06.710 align:start position:0%
all little chips began as big chips the
chips<00:00:03.240><c> are</c><00:00:03.419><c> made</c><00:00:03.860><c> by</c><00:00:04.860><c> dicing</c><00:00:05.400><c> a</c><00:00:05.520><c> wafer</c><00:00:05.880><c> and</c><00:00:06.480><c> the</c>

00:00:06.710 --> 00:00:06.720 align:start position:0%
chips are made by dicing a wafer and the
 

00:00:06.720 --> 00:00:09.470 align:start position:0%
chips are made by dicing a wafer and the
wafer<00:00:07.379><c> is</c><00:00:07.620><c> for</c><00:00:08.220><c> most</c><00:00:08.400><c> of</c><00:00:08.580><c> the</c><00:00:08.700><c> chips</c><00:00:08.820><c> you</c><00:00:09.179><c> and</c><00:00:09.360><c> I</c>

00:00:09.470 --> 00:00:09.480 align:start position:0%
wafer is for most of the chips you and I
 

00:00:09.480 --> 00:00:11.690 align:start position:0%
wafer is for most of the chips you and I
deal<00:00:09.720><c> with</c><00:00:09.900><c> begin</c><00:00:10.380><c> at</c><00:00:10.740><c> 300</c><00:00:11.160><c> millimeter</c><00:00:11.580><c> in</c>

00:00:11.690 --> 00:00:11.700 align:start position:0%
deal with begin at 300 millimeter in
 

00:00:11.700 --> 00:00:13.910 align:start position:0%
deal with begin at 300 millimeter in
diameter<00:00:12.120><c> and</c><00:00:12.719><c> the</c><00:00:13.019><c> the</c><00:00:13.080><c> chips</c><00:00:13.440><c> were</c><00:00:13.679><c> sort</c><00:00:13.860><c> of</c>

00:00:13.910 --> 00:00:13.920 align:start position:0%
diameter and the the chips were sort of
 

00:00:13.920 --> 00:00:16.010 align:start position:0%
diameter and the the chips were sort of
punched<00:00:14.280><c> out</c><00:00:14.400><c> like</c><00:00:14.700><c> your</c><00:00:14.940><c> mother</c><00:00:15.360><c> would</c><00:00:15.660><c> punch</c>

00:00:16.010 --> 00:00:16.020 align:start position:0%
punched out like your mother would punch
 

00:00:16.020 --> 00:00:18.290 align:start position:0%
punched out like your mother would punch
out<00:00:16.260><c> cookies</c><00:00:16.740><c> from</c><00:00:17.220><c> cookie</c><00:00:17.520><c> dough</c><00:00:17.699><c> what's</c>

00:00:18.290 --> 00:00:18.300 align:start position:0%
out cookies from cookie dough what's
 

00:00:18.300 --> 00:00:21.349 align:start position:0%
out cookies from cookie dough what's
interesting<00:00:18.779><c> is</c><00:00:19.140><c> then</c><00:00:19.680><c> for</c><00:00:19.980><c> large</c><00:00:20.220><c> AI</c><00:00:20.760><c> work</c><00:00:21.000><c> we</c>

00:00:21.349 --> 00:00:21.359 align:start position:0%
interesting is then for large AI work we
 

00:00:21.359 --> 00:00:22.609 align:start position:0%
interesting is then for large AI work we
have<00:00:21.480><c> to</c><00:00:21.660><c> try</c><00:00:21.779><c> and</c><00:00:21.960><c> Stitch</c><00:00:22.199><c> them</c><00:00:22.380><c> back</c>

00:00:22.609 --> 00:00:22.619 align:start position:0%
have to try and Stitch them back
 

00:00:22.619 --> 00:00:24.769 align:start position:0%
have to try and Stitch them back
together<00:00:22.800><c> again</c><00:00:23.220><c> right</c><00:00:23.760><c> one</c><00:00:24.180><c> ship</c><00:00:24.420><c> isn't</c>

00:00:24.769 --> 00:00:24.779 align:start position:0%
together again right one ship isn't
 

00:00:24.779 --> 00:00:27.890 align:start position:0%
together again right one ship isn't
enough<00:00:24.960><c> and</c><00:00:25.439><c> so</c><00:00:25.619><c> we</c><00:00:25.859><c> then</c><00:00:26.300><c> expend</c><00:00:27.300><c> tremendous</c>

00:00:27.890 --> 00:00:27.900 align:start position:0%
enough and so we then expend tremendous
 

00:00:27.900 --> 00:00:30.650 align:start position:0%
enough and so we then expend tremendous
effort<00:00:28.439><c> trying</c><00:00:28.920><c> to</c><00:00:29.160><c> tie</c><00:00:29.580><c> these</c><00:00:29.880><c> chips</c><00:00:30.180><c> that</c>

00:00:30.650 --> 00:00:30.660 align:start position:0%
effort trying to tie these chips that
 

00:00:30.660 --> 00:00:32.749 align:start position:0%
effort trying to tie these chips that
earlier<00:00:31.140><c> in</c><00:00:31.320><c> the</c><00:00:31.560><c> manufacturing</c><00:00:32.099><c> process</c><00:00:32.460><c> we</c>

00:00:32.749 --> 00:00:32.759 align:start position:0%
earlier in the manufacturing process we
 

00:00:32.759 --> 00:00:35.209 align:start position:0%
earlier in the manufacturing process we
cut<00:00:32.940><c> up</c><00:00:33.120><c> in</c><00:00:33.360><c> discrete</c><00:00:33.899><c> elements</c><00:00:34.380><c> we</c><00:00:34.860><c> try</c><00:00:35.040><c> to</c>

00:00:35.209 --> 00:00:35.219 align:start position:0%
cut up in discrete elements we try to
 

00:00:35.219 --> 00:00:37.310 align:start position:0%
cut up in discrete elements we try to
reassemble<00:00:35.880><c> them</c><00:00:36.059><c> in</c><00:00:36.420><c> the</c><00:00:36.540><c> form</c><00:00:36.660><c> of</c><00:00:36.840><c> a</c><00:00:36.960><c> cluster</c>

00:00:37.310 --> 00:00:37.320 align:start position:0%
reassemble them in the form of a cluster
 

00:00:37.320 --> 00:00:39.290 align:start position:0%
reassemble them in the form of a cluster
and<00:00:37.559><c> it</c><00:00:37.680><c> turns</c><00:00:37.980><c> out</c><00:00:38.100><c> that's</c><00:00:38.579><c> rather</c><00:00:39.120><c> bad</c>

00:00:39.290 --> 00:00:39.300 align:start position:0%
and it turns out that's rather bad
 

00:00:39.300 --> 00:00:41.389 align:start position:0%
and it turns out that's rather bad
strategy<00:00:39.719><c> so</c><00:00:40.020><c> if</c><00:00:40.260><c> you</c><00:00:40.440><c> can</c><00:00:40.620><c> keep</c><00:00:40.860><c> the</c><00:00:41.160><c> traffic</c>

00:00:41.389 --> 00:00:41.399 align:start position:0%
strategy so if you can keep the traffic
 

00:00:41.399 --> 00:00:43.610 align:start position:0%
strategy so if you can keep the traffic
on<00:00:42.120><c> your</c><00:00:42.239><c> chip</c><00:00:42.480><c> here</c><00:00:42.780><c> thousands</c><00:00:43.320><c> of</c><00:00:43.440><c> times</c>

00:00:43.610 --> 00:00:43.620 align:start position:0%
on your chip here thousands of times
 

00:00:43.620 --> 00:00:46.850 align:start position:0%
on your chip here thousands of times
faster<00:00:44.100><c> at</c><00:00:44.820><c> a</c><00:00:45.059><c> thousand</c><00:00:45.420><c> the</c><00:00:45.780><c> power</c><00:00:46.020><c> and</c><00:00:46.440><c> so</c><00:00:46.620><c> by</c>

00:00:46.850 --> 00:00:46.860 align:start position:0%
faster at a thousand the power and so by
 

00:00:46.860 --> 00:00:49.130 align:start position:0%
faster at a thousand the power and so by
building<00:00:47.160><c> a</c><00:00:47.460><c> big</c><00:00:47.579><c> chip</c><00:00:47.879><c> we</c><00:00:48.420><c> were</c><00:00:48.539><c> able</c><00:00:48.719><c> to</c><00:00:48.840><c> keep</c>

00:00:49.130 --> 00:00:49.140 align:start position:0%
building a big chip we were able to keep
 

00:00:49.140 --> 00:00:51.350 align:start position:0%
building a big chip we were able to keep
a<00:00:49.379><c> huge</c><00:00:49.680><c> amount</c><00:00:49.920><c> of</c><00:00:50.100><c> the</c><00:00:50.280><c> work</c><00:00:50.520><c> that</c><00:00:50.879><c> usually</c>

00:00:51.350 --> 00:00:51.360 align:start position:0%
a huge amount of the work that usually
 

00:00:51.360 --> 00:00:54.170 align:start position:0%
a huge amount of the work that usually
leaves<00:00:51.899><c> chip</c><00:00:52.140><c> boundaries</c><00:00:52.680><c> and</c><00:00:53.340><c> slows</c><00:00:54.000><c> down</c>

00:00:54.170 --> 00:00:54.180 align:start position:0%
leaves chip boundaries and slows down
 

00:00:54.180 --> 00:00:56.090 align:start position:0%
leaves chip boundaries and slows down
training<00:00:54.660><c> we're</c><00:00:54.840><c> able</c><00:00:55.079><c> to</c><00:00:55.260><c> keep</c><00:00:55.440><c> it</c><00:00:55.620><c> on</c><00:00:55.980><c> the</c>

00:00:56.090 --> 00:00:56.100 align:start position:0%
training we're able to keep it on the
 

00:00:56.100 --> 00:00:59.330 align:start position:0%
training we're able to keep it on the
chip<00:00:56.280><c> and</c><00:00:57.180><c> do</c><00:00:57.719><c> work</c><00:00:58.020><c> much</c><00:00:58.320><c> faster</c><00:00:58.680><c> at</c><00:00:59.100><c> much</c>

00:00:59.330 --> 00:00:59.340 align:start position:0%
chip and do work much faster at much
 

00:00:59.340 --> 00:01:01.820 align:start position:0%
chip and do work much faster at much
lower<00:00:59.520><c> power</c>

