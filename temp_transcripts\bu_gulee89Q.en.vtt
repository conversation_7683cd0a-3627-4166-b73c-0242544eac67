WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:02.210 align:start position:0%
 
hi<00:00:00.539><c> friends</c><00:00:00.780><c> my</c><00:00:01.020><c> name</c><00:00:01.079><c> is</c><00:00:01.199><c> Tris</c><00:00:01.500><c> and</c><00:00:01.860><c> this</c><00:00:02.040><c> is</c>

00:00:02.210 --> 00:00:02.220 align:start position:0%
hi friends my name is <PERSON><PERSON> and this is
 

00:00:02.220 --> 00:00:03.649 align:start position:0%
hi friends my name is <PERSON><PERSON> and this is
no<00:00:02.340><c> boilerplate</c><00:00:02.879><c> focusing</c><00:00:03.300><c> on</c><00:00:03.419><c> Fast</c>

00:00:03.649 --> 00:00:03.659 align:start position:0%
no boilerplate focusing on Fast
 

00:00:03.659 --> 00:00:05.870 align:start position:0%
no boilerplate focusing on Fast
technical<00:00:04.259><c> videos</c><00:00:04.560><c> welcome</c><00:00:05.100><c> class</c><00:00:05.400><c> to</c><00:00:05.640><c> raster</c>

00:00:05.870 --> 00:00:05.880 align:start position:0%
technical videos welcome class to raster
 

00:00:05.880 --> 00:00:07.369 align:start position:0%
technical videos welcome class to raster
101<00:00:06.359><c> where</c><00:00:06.540><c> you'll</c><00:00:06.779><c> learn</c><00:00:06.899><c> how</c><00:00:07.080><c> to</c><00:00:07.200><c> understand</c>

00:00:07.369 --> 00:00:07.379 align:start position:0%
101 where you'll learn how to understand
 

00:00:07.379 --> 00:00:09.470 align:start position:0%
101 where you'll learn how to understand
what<00:00:07.859><c> the</c><00:00:07.980><c> compiler</c><00:00:08.460><c> is</c><00:00:08.580><c> saying</c><00:00:08.880><c> to</c><00:00:08.940><c> you</c><00:00:09.059><c> this</c>

00:00:09.470 --> 00:00:09.480 align:start position:0%
what the compiler is saying to you this
 

00:00:09.480 --> 00:00:11.509 align:start position:0%
what the compiler is saying to you this
is<00:00:09.660><c> a</c><00:00:09.840><c> critical</c><00:00:10.080><c> skill</c><00:00:10.380><c> that</c><00:00:10.800><c> atrophies</c><00:00:11.340><c> in</c>

00:00:11.509 --> 00:00:11.519 align:start position:0%
is a critical skill that atrophies in
 

00:00:11.519 --> 00:00:13.549 align:start position:0%
is a critical skill that atrophies in
other<00:00:11.639><c> languages</c><00:00:11.880><c> that</c><00:00:12.179><c> give</c><00:00:12.360><c> poor</c><00:00:12.960><c> or</c><00:00:13.320><c> no</c>

00:00:13.549 --> 00:00:13.559 align:start position:0%
other languages that give poor or no
 

00:00:13.559 --> 00:00:16.189 align:start position:0%
other languages that give poor or no
error<00:00:13.980><c> output</c><00:00:14.280><c> open</c><00:00:15.059><c> your</c><00:00:15.299><c> books</c><00:00:15.599><c> here</c><00:00:15.900><c> are</c><00:00:16.080><c> my</c>

00:00:16.189 --> 00:00:16.199 align:start position:0%
error output open your books here are my
 

00:00:16.199 --> 00:00:19.189 align:start position:0%
error output open your books here are my
tips<00:00:16.440><c> and</c><00:00:16.920><c> read</c><00:00:17.220><c> the</c><00:00:17.640><c> error</c><00:00:18.060><c> again</c><00:00:18.600><c> everything</c>

00:00:19.189 --> 00:00:19.199 align:start position:0%
tips and read the error again everything
 

00:00:19.199 --> 00:00:20.810 align:start position:0%
tips and read the error again everything
you<00:00:19.440><c> see</c><00:00:19.560><c> in</c><00:00:19.680><c> this</c><00:00:19.859><c> video</c><00:00:19.980><c> from</c><00:00:20.220><c> the</c><00:00:20.400><c> script</c><00:00:20.580><c> to</c>

00:00:20.810 --> 00:00:20.820 align:start position:0%
you see in this video from the script to
 

00:00:20.820 --> 00:00:22.189 align:start position:0%
you see in this video from the script to
the<00:00:21.000><c> images</c><00:00:21.119><c> are</c><00:00:21.480><c> part</c><00:00:21.600><c> of</c><00:00:21.720><c> a</c><00:00:21.840><c> markdown</c>

00:00:22.189 --> 00:00:22.199 align:start position:0%
the images are part of a markdown
 

00:00:22.199 --> 00:00:24.050 align:start position:0%
the images are part of a markdown
document<00:00:22.560><c> available</c><00:00:22.859><c> on</c><00:00:23.160><c> GitHub</c><00:00:23.520><c> under</c><00:00:23.760><c> a</c>

00:00:24.050 --> 00:00:24.060 align:start position:0%
document available on GitHub under a
 

00:00:24.060 --> 00:00:26.810 align:start position:0%
document available on GitHub under a
public<00:00:24.180><c> domain</c><00:00:24.720><c> license</c><00:00:25.080><c> being</c><00:00:25.859><c> a</c><00:00:26.160><c> basic</c><00:00:26.460><c> user</c>

00:00:26.810 --> 00:00:26.820 align:start position:0%
public domain license being a basic user
 

00:00:26.820 --> 00:00:28.970 align:start position:0%
public domain license being a basic user
of<00:00:26.939><c> the</c><00:00:27.060><c> rust</c><00:00:27.300><c> language</c><00:00:27.599><c> is</c><00:00:28.199><c> easy</c><00:00:28.500><c> because</c>

00:00:28.970 --> 00:00:28.980 align:start position:0%
of the rust language is easy because
 

00:00:28.980 --> 00:00:30.950 align:start position:0%
of the rust language is easy because
there<00:00:29.220><c> has</c><00:00:29.340><c> been</c><00:00:29.519><c> so</c><00:00:29.880><c> much</c><00:00:30.000><c> time</c><00:00:30.240><c> invested</c><00:00:30.720><c> by</c>

00:00:30.950 --> 00:00:30.960 align:start position:0%
there has been so much time invested by
 

00:00:30.960 --> 00:00:32.810 align:start position:0%
there has been so much time invested by
the<00:00:31.199><c> incredible</c><00:00:31.619><c> compiler</c><00:00:32.220><c> team</c><00:00:32.340><c> in</c><00:00:32.579><c> making</c>

00:00:32.810 --> 00:00:32.820 align:start position:0%
the incredible compiler team in making
 

00:00:32.820 --> 00:00:35.150 align:start position:0%
the incredible compiler team in making
your<00:00:33.120><c> early</c><00:00:33.360><c> experience</c><00:00:33.899><c> of</c><00:00:34.140><c> rust</c><00:00:34.500><c> a</c>

00:00:35.150 --> 00:00:35.160 align:start position:0%
your early experience of rust a
 

00:00:35.160 --> 00:00:37.490 align:start position:0%
your early experience of rust a
wonderful<00:00:35.460><c> one</c><00:00:36.120><c> forget</c><00:00:36.600><c> the</c><00:00:36.899><c> borrow</c><00:00:37.020><c> Checker</c>

00:00:37.490 --> 00:00:37.500 align:start position:0%
wonderful one forget the borrow Checker
 

00:00:37.500 --> 00:00:39.530 align:start position:0%
wonderful one forget the borrow Checker
forget<00:00:37.800><c> lifetimes</c><00:00:38.640><c> and</c><00:00:38.760><c> reference</c><00:00:39.120><c> counting</c>

00:00:39.530 --> 00:00:39.540 align:start position:0%
forget lifetimes and reference counting
 

00:00:39.540 --> 00:00:41.150 align:start position:0%
forget lifetimes and reference counting
and<00:00:39.719><c> all</c><00:00:39.899><c> that</c><00:00:40.020><c> other</c><00:00:40.200><c> stuff</c><00:00:40.500><c> you've</c><00:00:40.800><c> heard</c><00:00:40.920><c> of</c>

00:00:41.150 --> 00:00:41.160 align:start position:0%
and all that other stuff you've heard of
 

00:00:41.160 --> 00:00:43.310 align:start position:0%
and all that other stuff you've heard of
you<00:00:41.879><c> don't</c><00:00:41.940><c> need</c><00:00:42.120><c> to</c><00:00:42.300><c> understand</c><00:00:42.420><c> them</c><00:00:42.780><c> yet</c><00:00:42.960><c> to</c>

00:00:43.310 --> 00:00:43.320 align:start position:0%
you don't need to understand them yet to
 

00:00:43.320 --> 00:00:44.990 align:start position:0%
you don't need to understand them yet to
build<00:00:43.440><c> simple</c><00:00:43.800><c> rust</c><00:00:44.160><c> apps</c><00:00:44.579><c> in</c><00:00:44.700><c> the</c><00:00:44.820><c> same</c><00:00:44.879><c> way</c>

00:00:44.990 --> 00:00:45.000 align:start position:0%
build simple rust apps in the same way
 

00:00:45.000 --> 00:00:46.430 align:start position:0%
build simple rust apps in the same way
as<00:00:45.180><c> you</c><00:00:45.300><c> can</c><00:00:45.420><c> get</c><00:00:45.540><c> the</c><00:00:45.660><c> gender</c><00:00:45.899><c> of</c><00:00:46.140><c> a</c><00:00:46.320><c> French</c>

00:00:46.430 --> 00:00:46.440 align:start position:0%
as you can get the gender of a French
 

00:00:46.440 --> 00:00:48.170 align:start position:0%
as you can get the gender of a French
noun<00:00:46.800><c> wrong</c><00:00:47.040><c> and</c><00:00:47.280><c> still</c><00:00:47.460><c> be</c><00:00:47.579><c> understood</c><00:00:47.879><c> fine</c>

00:00:48.170 --> 00:00:48.180 align:start position:0%
noun wrong and still be understood fine
 

00:00:48.180 --> 00:00:51.410 align:start position:0%
noun wrong and still be understood fine
by<00:00:48.480><c> a</c><00:00:48.660><c> native</c><00:00:48.840><c> speaker</c><00:00:49.280><c> kind</c><00:00:50.280><c> generous</c><00:00:51.120><c> people</c>

00:00:51.410 --> 00:00:51.420 align:start position:0%
by a native speaker kind generous people
 

00:00:51.420 --> 00:00:53.150 align:start position:0%
by a native speaker kind generous people
will<00:00:52.020><c> always</c><00:00:52.260><c> understand</c><00:00:52.559><c> what</c><00:00:53.039><c> you're</c>

00:00:53.150 --> 00:00:53.160 align:start position:0%
will always understand what you're
 

00:00:53.160 --> 00:00:55.430 align:start position:0%
will always understand what you're
talking<00:00:53.340><c> about</c><00:00:53.579><c> and</c><00:00:54.000><c> kind</c><00:00:54.360><c> generous</c><00:00:55.140><c> people</c>

00:00:55.430 --> 00:00:55.440 align:start position:0%
talking about and kind generous people
 

00:00:55.440 --> 00:00:58.490 align:start position:0%
talking about and kind generous people
wrote<00:00:56.219><c> the</c><00:00:56.579><c> rust</c><00:00:56.879><c> compiler</c><00:00:57.539><c> here</c><00:00:58.199><c> is</c><00:00:58.379><c> the</c>

00:00:58.490 --> 00:00:58.500 align:start position:0%
wrote the rust compiler here is the
 

00:00:58.500 --> 00:01:00.110 align:start position:0%
wrote the rust compiler here is the
basic<00:00:58.739><c> command</c><00:00:58.980><c> I</c><00:00:59.219><c> recommend</c><00:00:59.460><c> beginners</c><00:00:59.940><c> use</c>

00:01:00.110 --> 00:01:00.120 align:start position:0%
basic command I recommend beginners use
 

00:01:00.120 --> 00:01:01.490 align:start position:0%
basic command I recommend beginners use
to<00:01:00.360><c> get</c><00:01:00.420><c> the</c><00:01:00.539><c> compiler</c><00:01:00.960><c> involved</c><00:01:01.260><c> with</c><00:01:01.379><c> their</c>

00:01:01.490 --> 00:01:01.500 align:start position:0%
to get the compiler involved with their
 

00:01:01.500 --> 00:01:03.709 align:start position:0%
to get the compiler involved with their
roster<00:01:01.800><c> education</c><00:01:02.399><c> clippy</c><00:01:03.120><c> is</c><00:01:03.300><c> rust's</c>

00:01:03.709 --> 00:01:03.719 align:start position:0%
roster education clippy is rust's
 

00:01:03.719 --> 00:01:06.230 align:start position:0%
roster education clippy is rust's
built-in<00:01:04.080><c> linter</c><00:01:04.500><c> yes</c><00:01:04.979><c> named</c><00:01:05.460><c> after</c><00:01:05.580><c> the</c><00:01:05.939><c> 90s</c>

00:01:06.230 --> 00:01:06.240 align:start position:0%
built-in linter yes named after the 90s
 

00:01:06.240 --> 00:01:08.690 align:start position:0%
built-in linter yes named after the 90s
Microsoft<00:01:06.720><c> Office</c><00:01:07.020><c> paper</c><00:01:07.380><c> clip</c><00:01:07.740><c> and</c><00:01:08.220><c> as</c><00:01:08.520><c> ever</c>

00:01:08.690 --> 00:01:08.700 align:start position:0%
Microsoft Office paper clip and as ever
 

00:01:08.700 --> 00:01:10.789 align:start position:0%
Microsoft Office paper clip and as ever
you<00:01:09.119><c> should</c><00:01:09.240><c> let</c><00:01:09.479><c> the</c><00:01:09.780><c> linta</c><00:01:10.260><c> teach</c><00:01:10.439><c> you</c><00:01:10.619><c> the</c>

00:01:10.789 --> 00:01:10.799 align:start position:0%
you should let the linta teach you the
 

00:01:10.799 --> 00:01:13.190 align:start position:0%
you should let the linta teach you the
language<00:01:11.040><c> you</c><00:01:12.000><c> can</c><00:01:12.119><c> even</c><00:01:12.299><c> get</c><00:01:12.659><c> with</c><00:01:12.840><c> a</c><00:01:13.140><c> very</c>

00:01:13.190 --> 00:01:13.200 align:start position:0%
language you can even get with a very
 

00:01:13.200 --> 00:01:15.770 align:start position:0%
language you can even get with a very
good<00:01:13.380><c> crate</c><00:01:13.799><c> bacon</c><00:01:14.400><c> to</c><00:01:14.760><c> auto</c><00:01:14.880><c> run</c><00:01:15.240><c> clippy</c><00:01:15.600><c> for</c>

00:01:15.770 --> 00:01:15.780 align:start position:0%
good crate bacon to auto run clippy for
 

00:01:15.780 --> 00:01:17.870 align:start position:0%
good crate bacon to auto run clippy for
you<00:01:15.900><c> while</c><00:01:16.260><c> you</c><00:01:16.439><c> code</c><00:01:16.619><c> it's</c><00:01:17.400><c> a</c><00:01:17.580><c> very</c><00:01:17.700><c> nice</c>

00:01:17.870 --> 00:01:17.880 align:start position:0%
you while you code it's a very nice
 

00:01:17.880 --> 00:01:20.690 align:start position:0%
you while you code it's a very nice
compliment<00:01:18.420><c> to</c><00:01:18.780><c> your</c><00:01:18.960><c> editor's</c><00:01:19.500><c> linting</c><00:01:20.040><c> you</c>

00:01:20.690 --> 00:01:20.700 align:start position:0%
compliment to your editor's linting you
 

00:01:20.700 --> 00:01:22.490 align:start position:0%
compliment to your editor's linting you
can<00:01:20.759><c> even</c><00:01:20.880><c> start</c><00:01:21.180><c> by</c><00:01:21.420><c> writing</c><00:01:21.720><c> JavaScript</c><00:01:22.259><c> and</c>

00:01:22.490 --> 00:01:22.500 align:start position:0%
can even start by writing JavaScript and
 

00:01:22.500 --> 00:01:24.770 align:start position:0%
can even start by writing JavaScript and
rust<00:01:22.799><c> will</c><00:01:23.100><c> tell</c><00:01:23.280><c> you</c><00:01:23.400><c> what</c><00:01:23.700><c> to</c><00:01:23.880><c> do</c><00:01:24.060><c> let's</c>

00:01:24.770 --> 00:01:24.780 align:start position:0%
rust will tell you what to do let's
 

00:01:24.780 --> 00:01:26.749 align:start position:0%
rust will tell you what to do let's
isolate<00:01:25.259><c> some</c><00:01:25.439><c> beginner</c><00:01:25.740><c> mistakes</c><00:01:26.100><c> and</c><00:01:26.460><c> study</c>

00:01:26.749 --> 00:01:26.759 align:start position:0%
isolate some beginner mistakes and study
 

00:01:26.759 --> 00:01:29.330 align:start position:0%
isolate some beginner mistakes and study
how<00:01:27.000><c> the</c><00:01:27.180><c> compiler</c><00:01:27.659><c> will</c><00:01:27.840><c> respond</c><00:01:28.200><c> a</c><00:01:29.159><c> very</c>

00:01:29.330 --> 00:01:29.340 align:start position:0%
how the compiler will respond a very
 

00:01:29.340 --> 00:01:31.010 align:start position:0%
how the compiler will respond a very
common<00:01:29.520><c> mistake</c><00:01:29.939><c> is</c><00:01:30.119><c> to</c><00:01:30.299><c> forget</c><00:01:30.360><c> the</c><00:01:30.659><c> bang</c><00:01:30.780><c> at</c>

00:01:31.010 --> 00:01:31.020 align:start position:0%
common mistake is to forget the bang at
 

00:01:31.020 --> 00:01:33.350 align:start position:0%
common mistake is to forget the bang at
the<00:01:31.140><c> end</c><00:01:31.200><c> of</c><00:01:31.320><c> a</c><00:01:31.380><c> macro</c><00:01:31.680><c> invocation</c><00:01:32.220><c> macros</c><00:01:33.180><c> as</c>

00:01:33.350 --> 00:01:33.360 align:start position:0%
the end of a macro invocation macros as
 

00:01:33.360 --> 00:01:35.270 align:start position:0%
the end of a macro invocation macros as
I<00:01:33.540><c> explained</c><00:01:33.900><c> in</c><00:01:34.080><c> detail</c><00:01:34.439><c> in</c><00:01:34.680><c> my</c><00:01:34.860><c> witchcraft</c>

00:01:35.270 --> 00:01:35.280 align:start position:0%
I explained in detail in my witchcraft
 

00:01:35.280 --> 00:01:37.550 align:start position:0%
I explained in detail in my witchcraft
video<00:01:35.700><c> are</c><00:01:36.060><c> functions</c><00:01:36.840><c> that</c><00:01:37.020><c> execute</c><00:01:37.380><c> a</c>

00:01:37.550 --> 00:01:37.560 align:start position:0%
video are functions that execute a
 

00:01:37.560 --> 00:01:39.950 align:start position:0%
video are functions that execute a
compile<00:01:37.920><c> time</c><00:01:38.159><c> and</c><00:01:38.759><c> are</c><00:01:39.000><c> a</c><00:01:39.299><c> very</c><00:01:39.540><c> powerful</c>

00:01:39.950 --> 00:01:39.960 align:start position:0%
compile time and are a very powerful
 

00:01:39.960 --> 00:01:41.749 align:start position:0%
compile time and are a very powerful
meta<00:01:40.320><c> programming</c><00:01:40.680><c> technique</c><00:01:40.979><c> borrowed</c><00:01:41.640><c> from</c>

00:01:41.749 --> 00:01:41.759 align:start position:0%
meta programming technique borrowed from
 

00:01:41.759 --> 00:01:43.969 align:start position:0%
meta programming technique borrowed from
lisp<00:01:42.060><c> because</c><00:01:42.780><c> they're</c><00:01:43.079><c> so</c><00:01:43.320><c> powerful</c><00:01:43.619><c> it's</c>

00:01:43.969 --> 00:01:43.979 align:start position:0%
lisp because they're so powerful it's
 

00:01:43.979 --> 00:01:45.289 align:start position:0%
lisp because they're so powerful it's
very<00:01:44.159><c> good</c><00:01:44.340><c> to</c><00:01:44.520><c> know</c><00:01:44.640><c> when</c><00:01:44.880><c> they</c><00:01:45.000><c> are</c><00:01:45.119><c> being</c>

00:01:45.289 --> 00:01:45.299 align:start position:0%
very good to know when they are being
 

00:01:45.299 --> 00:01:47.990 align:start position:0%
very good to know when they are being
used<00:01:45.540><c> rust</c><00:01:46.500><c> Flags</c><00:01:46.979><c> this</c><00:01:47.100><c> with</c><00:01:47.220><c> a</c><00:01:47.400><c> bang</c><00:01:47.520><c> on</c>

00:01:47.990 --> 00:01:48.000 align:start position:0%
used rust Flags this with a bang on
 

00:01:48.000 --> 00:01:49.850 align:start position:0%
used rust Flags this with a bang on
macro<00:01:48.360><c> invocation</c><00:01:48.900><c> as</c><00:01:49.500><c> you</c><00:01:49.619><c> will</c><00:01:49.740><c> see</c>

00:01:49.850 --> 00:01:49.860 align:start position:0%
macro invocation as you will see
 

00:01:49.860 --> 00:01:52.069 align:start position:0%
macro invocation as you will see
throughout<00:01:50.159><c> basic</c><00:01:50.520><c> rust</c><00:01:50.759><c> the</c><00:01:51.240><c> compiler</c><00:01:51.780><c> can</c>

00:01:52.069 --> 00:01:52.079 align:start position:0%
throughout basic rust the compiler can
 

00:01:52.079 --> 00:01:54.410 align:start position:0%
throughout basic rust the compiler can
almost<00:01:52.320><c> always</c><00:01:52.860><c> correct</c><00:01:53.579><c> your</c><00:01:53.759><c> errors</c><00:01:54.119><c> for</c>

00:01:54.410 --> 00:01:54.420 align:start position:0%
almost always correct your errors for
 

00:01:54.420 --> 00:01:56.810 align:start position:0%
almost always correct your errors for
you<00:01:54.600><c> if</c><00:01:55.140><c> the</c><00:01:55.320><c> item</c><00:01:55.619><c> is</c><00:01:55.799><c> known</c><00:01:55.979><c> to</c><00:01:56.220><c> the</c><00:01:56.340><c> compiler</c>

00:01:56.810 --> 00:01:56.820 align:start position:0%
you if the item is known to the compiler
 

00:01:56.820 --> 00:01:58.310 align:start position:0%
you if the item is known to the compiler
it<00:01:57.000><c> can</c><00:01:57.180><c> suggest</c><00:01:57.479><c> it</c><00:01:57.659><c> for</c><00:01:57.780><c> you</c><00:01:57.899><c> if</c><00:01:58.140><c> you've</c>

00:01:58.310 --> 00:01:58.320 align:start position:0%
it can suggest it for you if you've
 

00:01:58.320 --> 00:02:00.109 align:start position:0%
it can suggest it for you if you've
spelled<00:01:58.619><c> it</c><00:01:58.740><c> wrong</c><00:01:58.860><c> it</c><00:01:59.460><c> can</c><00:01:59.579><c> also</c><00:01:59.820><c> figure</c><00:02:00.000><c> out</c>

00:02:00.109 --> 00:02:00.119 align:start position:0%
spelled it wrong it can also figure out
 

00:02:00.119 --> 00:02:01.910 align:start position:0%
spelled it wrong it can also figure out
if<00:02:00.360><c> you're</c><00:02:00.479><c> using</c><00:02:00.720><c> an</c><00:02:00.899><c> unimported</c><00:02:01.439><c> method</c><00:02:01.740><c> and</c>

00:02:01.910 --> 00:02:01.920 align:start position:0%
if you're using an unimported method and
 

00:02:01.920 --> 00:02:04.130 align:start position:0%
if you're using an unimported method and
suggest<00:02:02.220><c> importing</c><00:02:02.939><c> the</c><00:02:03.240><c> trait</c><00:02:03.600><c> that</c><00:02:03.960><c> may</c>

00:02:04.130 --> 00:02:04.140 align:start position:0%
suggest importing the trait that may
 

00:02:04.140 --> 00:02:06.230 align:start position:0%
suggest importing the trait that may
provide<00:02:04.560><c> that</c><00:02:04.979><c> method</c><00:02:05.399><c> this</c><00:02:05.820><c> makes</c><00:02:05.939><c> working</c>

00:02:06.230 --> 00:02:06.240 align:start position:0%
provide that method this makes working
 

00:02:06.240 --> 00:02:08.389 align:start position:0%
provide that method this makes working
with<00:02:06.540><c> the</c><00:02:06.719><c> unfamiliar</c><00:02:07.259><c> trade</c><00:02:07.560><c> system</c><00:02:07.740><c> much</c>

00:02:08.389 --> 00:02:08.399 align:start position:0%
with the unfamiliar trade system much
 

00:02:08.399 --> 00:02:10.609 align:start position:0%
with the unfamiliar trade system much
easier<00:02:08.759><c> for</c><00:02:09.060><c> newbies</c><00:02:09.479><c> as</c><00:02:10.140><c> I</c><00:02:10.259><c> mentioned</c><00:02:10.500><c> about</c>

00:02:10.609 --> 00:02:10.619 align:start position:0%
easier for newbies as I mentioned about
 

00:02:10.619 --> 00:02:12.290 align:start position:0%
easier for newbies as I mentioned about
a<00:02:10.860><c> month</c><00:02:10.979><c> ago</c><00:02:11.220><c> I've</c><00:02:11.520><c> started</c><00:02:11.819><c> one-to-one</c>

00:02:12.290 --> 00:02:12.300 align:start position:0%
a month ago I've started one-to-one
 

00:02:12.300 --> 00:02:14.390 align:start position:0%
a month ago I've started one-to-one
mentoring<00:02:12.780><c> on</c><00:02:13.200><c> patreon</c><00:02:13.620><c> and</c><00:02:13.980><c> this</c><00:02:14.099><c> is</c><00:02:14.220><c> what</c>

00:02:14.390 --> 00:02:14.400 align:start position:0%
mentoring on patreon and this is what
 

00:02:14.400 --> 00:02:16.010 align:start position:0%
mentoring on patreon and this is what
has<00:02:14.520><c> allowed</c><00:02:14.760><c> me</c><00:02:14.879><c> to</c><00:02:15.120><c> quit</c><00:02:15.300><c> my</c><00:02:15.540><c> day</c><00:02:15.660><c> job</c><00:02:15.840><c> and</c>

00:02:16.010 --> 00:02:16.020 align:start position:0%
has allowed me to quit my day job and
 

00:02:16.020 --> 00:02:18.170 align:start position:0%
has allowed me to quit my day job and
focus<00:02:16.260><c> on</c><00:02:16.440><c> no</c><00:02:16.560><c> boilerplate</c><00:02:17.099><c> full</c><00:02:17.400><c> time</c><00:02:17.580><c> if</c>

00:02:18.170 --> 00:02:18.180 align:start position:0%
focus on no boilerplate full time if
 

00:02:18.180 --> 00:02:19.250 align:start position:0%
focus on no boilerplate full time if
you'd<00:02:18.360><c> like</c><00:02:18.480><c> to</c><00:02:18.540><c> sign</c><00:02:18.660><c> up</c><00:02:18.840><c> for</c><00:02:18.959><c> a</c><00:02:19.080><c> monthly</c>

00:02:19.250 --> 00:02:19.260 align:start position:0%
you'd like to sign up for a monthly
 

00:02:19.260 --> 00:02:20.869 align:start position:0%
you'd like to sign up for a monthly
recurring<00:02:19.680><c> one</c><00:02:19.860><c> hour</c><00:02:20.040><c> video</c><00:02:20.280><c> meeting</c><00:02:20.520><c> head</c>

00:02:20.869 --> 00:02:20.879 align:start position:0%
recurring one hour video meeting head
 

00:02:20.879 --> 00:02:22.670 align:start position:0%
recurring one hour video meeting head
over<00:02:21.000><c> to</c><00:02:21.180><c> patreon.com</c><00:02:21.720><c> forward</c><00:02:22.080><c> slash</c><00:02:22.440><c> no</c>

00:02:22.670 --> 00:02:22.680 align:start position:0%
over to patreon.com forward slash no
 

00:02:22.680 --> 00:02:24.170 align:start position:0%
over to patreon.com forward slash no
boilerplate<00:02:23.280><c> we</c><00:02:23.640><c> can</c><00:02:23.760><c> talk</c><00:02:23.879><c> anything</c>

00:02:24.170 --> 00:02:24.180 align:start position:0%
boilerplate we can talk anything
 

00:02:24.180 --> 00:02:26.630 align:start position:0%
boilerplate we can talk anything
valuable<00:02:24.720><c> including</c><00:02:25.379><c> rust</c><00:02:25.739><c> video</c><00:02:26.160><c> production</c>

00:02:26.630 --> 00:02:26.640 align:start position:0%
valuable including rust video production
 

00:02:26.640 --> 00:02:28.490 align:start position:0%
valuable including rust video production
or<00:02:26.940><c> your</c><00:02:27.120><c> choice</c><00:02:27.300><c> of</c><00:02:27.480><c> topics</c><00:02:27.780><c> if</c><00:02:28.319><c> you</c><00:02:28.440><c> would</c>

00:02:28.490 --> 00:02:28.500 align:start position:0%
or your choice of topics if you would
 

00:02:28.500 --> 00:02:30.110 align:start position:0%
or your choice of topics if you would
like<00:02:28.680><c> to</c><00:02:28.739><c> support</c><00:02:28.920><c> what</c><00:02:29.220><c> I</c><00:02:29.340><c> do</c><00:02:29.459><c> here</c><00:02:29.640><c> I</c><00:02:29.940><c> have</c>

00:02:30.110 --> 00:02:30.120 align:start position:0%
like to support what I do here I have
 

00:02:30.120 --> 00:02:32.089 align:start position:0%
like to support what I do here I have
multiple<00:02:30.540><c> patreon</c><00:02:31.020><c> options</c><00:02:31.440><c> and</c><00:02:31.680><c> they</c><00:02:31.920><c> all</c>

00:02:32.089 --> 00:02:32.099 align:start position:0%
multiple patreon options and they all
 

00:02:32.099 --> 00:02:34.430 align:start position:0%
multiple patreon options and they all
get<00:02:32.340><c> early</c><00:02:32.700><c> ad</c><00:02:33.120><c> free</c><00:02:33.360><c> and</c><00:02:33.840><c> tracking</c><00:02:34.260><c> free</c>

00:02:34.430 --> 00:02:34.440 align:start position:0%
get early ad free and tracking free
 

00:02:34.440 --> 00:02:35.990 align:start position:0%
get early ad free and tracking free
videos<00:02:34.739><c> thanks</c><00:02:35.459><c> to</c><00:02:35.580><c> everyone</c><00:02:35.700><c> who</c><00:02:35.879><c> has</c>

00:02:35.990 --> 00:02:36.000 align:start position:0%
videos thanks to everyone who has
 

00:02:36.000 --> 00:02:37.910 align:start position:0%
videos thanks to everyone who has
supported<00:02:36.540><c> me</c><00:02:36.660><c> on</c><00:02:36.840><c> this</c><00:02:36.959><c> wild</c><00:02:37.140><c> Journey</c><00:02:37.500><c> so</c><00:02:37.739><c> far</c>

00:02:37.910 --> 00:02:37.920 align:start position:0%
supported me on this wild Journey so far
 

00:02:37.920 --> 00:02:40.130 align:start position:0%
supported me on this wild Journey so far
back<00:02:38.459><c> to</c><00:02:38.640><c> speaking</c><00:02:38.940><c> rust</c><00:02:39.180><c> here's</c><00:02:39.959><c> a</c><00:02:40.020><c> funny</c>

00:02:40.130 --> 00:02:40.140 align:start position:0%
back to speaking rust here's a funny
 

00:02:40.140 --> 00:02:42.350 align:start position:0%
back to speaking rust here's a funny
strike<00:02:40.560><c> while</c><00:02:40.860><c> rust</c><00:02:41.220><c> supports</c><00:02:41.700><c> emojis</c><00:02:42.120><c> inside</c>

00:02:42.350 --> 00:02:42.360 align:start position:0%
strike while rust supports emojis inside
 

00:02:42.360 --> 00:02:43.670 align:start position:0%
strike while rust supports emojis inside
strings<00:02:42.840><c> they</c><00:02:43.080><c> can't</c><00:02:43.200><c> be</c><00:02:43.319><c> used</c><00:02:43.440><c> as</c>

00:02:43.670 --> 00:02:43.680 align:start position:0%
strings they can't be used as
 

00:02:43.680 --> 00:02:46.070 align:start position:0%
strings they can't be used as
identifiers<00:02:44.459><c> but</c><00:02:45.120><c> if</c><00:02:45.480><c> you</c><00:02:45.599><c> use</c><00:02:45.780><c> a</c><00:02:45.959><c> very</c>

00:02:46.070 --> 00:02:46.080 align:start position:0%
identifiers but if you use a very
 

00:02:46.080 --> 00:02:48.110 align:start position:0%
identifiers but if you use a very
special<00:02:46.319><c> Emoji</c><00:02:46.860><c> rust</c><00:02:47.459><c> knows</c><00:02:47.760><c> what</c><00:02:47.879><c> it</c><00:02:48.000><c> should</c>

00:02:48.110 --> 00:02:48.120 align:start position:0%
special Emoji rust knows what it should
 

00:02:48.120 --> 00:02:50.750 align:start position:0%
special Emoji rust knows what it should
be<00:02:48.239><c> called</c><00:02:48.360><c> at</c><00:02:49.200><c> first</c><00:02:49.319><c> glance</c><00:02:49.739><c> this</c><00:02:50.400><c> simple</c>

00:02:50.750 --> 00:02:50.760 align:start position:0%
be called at first glance this simple
 

00:02:50.760 --> 00:02:53.390 align:start position:0%
be called at first glance this simple
line<00:02:51.180><c> should</c><00:02:51.720><c> compile</c><00:02:52.140><c> that</c><00:02:52.620><c> is</c><00:02:52.800><c> not</c><00:02:52.920><c> a</c><00:02:53.040><c> minus</c>

00:02:53.390 --> 00:02:53.400 align:start position:0%
line should compile that is not a minus
 

00:02:53.400 --> 00:02:55.729 align:start position:0%
line should compile that is not a minus
symbol<00:02:53.700><c> up</c><00:02:54.000><c> there</c><00:02:54.180><c> that's</c><00:02:54.720><c> an</c><00:02:54.959><c> M</c><00:02:55.140><c> Dash</c><00:02:55.440><c> and</c><00:02:55.620><c> the</c>

00:02:55.729 --> 00:02:55.739 align:start position:0%
symbol up there that's an M Dash and the
 

00:02:55.739 --> 00:02:57.350 align:start position:0%
symbol up there that's an M Dash and the
developers<00:02:56.099><c> likely</c><00:02:56.459><c> copied</c><00:02:56.700><c> and</c><00:02:56.879><c> pasted</c><00:02:57.120><c> from</c>

00:02:57.350 --> 00:02:57.360 align:start position:0%
developers likely copied and pasted from
 

00:02:57.360 --> 00:02:59.210 align:start position:0%
developers likely copied and pasted from
a<00:02:57.540><c> badly</c><00:02:57.720><c> formatted</c><00:02:58.200><c> web</c><00:02:58.379><c> page</c><00:02:58.560><c> or</c><00:02:58.800><c> Microsoft</c>

00:02:59.210 --> 00:02:59.220 align:start position:0%
a badly formatted web page or Microsoft
 

00:02:59.220 --> 00:03:01.070 align:start position:0%
a badly formatted web page or Microsoft
product<00:02:59.580><c> I</c><00:03:00.300><c> remember</c><00:03:00.420><c> in</c><00:03:00.780><c> my</c><00:03:00.959><c> first</c>

00:03:01.070 --> 00:03:01.080 align:start position:0%
product I remember in my first
 

00:03:01.080 --> 00:03:02.509 align:start position:0%
product I remember in my first
programming<00:03:01.620><c> class</c><00:03:01.739><c> at</c><00:03:01.980><c> University</c><00:03:02.099><c> the</c>

00:03:02.509 --> 00:03:02.519 align:start position:0%
programming class at University the
 

00:03:02.519 --> 00:03:05.030 align:start position:0%
programming class at University the
lecturer<00:03:03.000><c> begged</c><00:03:03.420><c> us</c><00:03:03.540><c> to</c><00:03:04.019><c> not</c><00:03:04.200><c> copy</c><00:03:04.620><c> and</c><00:03:04.739><c> paste</c>

00:03:05.030 --> 00:03:05.040 align:start position:0%
lecturer begged us to not copy and paste
 

00:03:05.040 --> 00:03:06.530 align:start position:0%
lecturer begged us to not copy and paste
from<00:03:05.099><c> the</c><00:03:05.220><c> PDF</c><00:03:05.519><c> handouts</c><00:03:05.940><c> they</c><00:03:06.060><c> had</c><00:03:06.180><c> given</c><00:03:06.420><c> us</c>

00:03:06.530 --> 00:03:06.540 align:start position:0%
from the PDF handouts they had given us
 

00:03:06.540 --> 00:03:08.690 align:start position:0%
from the PDF handouts they had given us
half<00:03:07.140><c> the</c><00:03:07.379><c> class</c><00:03:07.500><c> including</c><00:03:07.980><c> me</c><00:03:08.099><c> did</c><00:03:08.459><c> anyway</c>

00:03:08.690 --> 00:03:08.700 align:start position:0%
half the class including me did anyway
 

00:03:08.700 --> 00:03:10.550 align:start position:0%
half the class including me did anyway
and<00:03:09.060><c> our</c><00:03:09.239><c> code</c><00:03:09.480><c> didn't</c><00:03:09.660><c> compile</c><00:03:10.019><c> and</c><00:03:10.200><c> had</c><00:03:10.379><c> a</c>

00:03:10.550 --> 00:03:10.560 align:start position:0%
and our code didn't compile and had a
 

00:03:10.560 --> 00:03:11.990 align:start position:0%
and our code didn't compile and had a
cryptic<00:03:10.920><c> error</c><00:03:11.280><c> message</c><00:03:11.400><c> from</c><00:03:11.700><c> the</c><00:03:11.819><c> C</c>

00:03:11.990 --> 00:03:12.000 align:start position:0%
cryptic error message from the C
 

00:03:12.000 --> 00:03:15.050 align:start position:0%
cryptic error message from the C
compiler<00:03:12.659><c> rust</c><00:03:13.459><c> understands</c><00:03:14.459><c> these</c>

00:03:15.050 --> 00:03:15.060 align:start position:0%
compiler rust understands these
 

00:03:15.060 --> 00:03:17.330 align:start position:0%
compiler rust understands these
homoglyphs<00:03:15.780><c> even</c><00:03:16.440><c> simple</c><00:03:16.739><c> things</c><00:03:16.980><c> can</c><00:03:17.220><c> be</c>

00:03:17.330 --> 00:03:17.340 align:start position:0%
homoglyphs even simple things can be
 

00:03:17.340 --> 00:03:19.790 align:start position:0%
homoglyphs even simple things can be
made<00:03:17.519><c> better</c><00:03:17.760><c> by</c><00:03:18.060><c> good</c><00:03:18.300><c> error</c><00:03:18.720><c> messages</c><00:03:18.900><c> like</c>

00:03:19.790 --> 00:03:19.800 align:start position:0%
made better by good error messages like
 

00:03:19.800 --> 00:03:21.110 align:start position:0%
made better by good error messages like
here<00:03:19.980><c> where</c><00:03:20.159><c> the</c><00:03:20.340><c> compiler</c><00:03:20.700><c> recommends</c>

00:03:21.110 --> 00:03:21.120 align:start position:0%
here where the compiler recommends
 

00:03:21.120 --> 00:03:23.990 align:start position:0%
here where the compiler recommends
constants<00:03:21.659><c> be</c><00:03:22.140><c> used</c><00:03:22.379><c> if</c><00:03:23.159><c> you're</c><00:03:23.280><c> a</c><00:03:23.400><c> purist</c><00:03:23.700><c> or</c>

00:03:23.990 --> 00:03:24.000 align:start position:0%
constants be used if you're a purist or
 

00:03:24.000 --> 00:03:26.930 align:start position:0%
constants be used if you're a purist or
by<00:03:24.300><c> heart</c><00:03:24.599><c> this</c><00:03:25.019><c> works</c><00:03:25.260><c> with</c><00:03:25.440><c> Tau</c><00:03:25.739><c> too</c><00:03:26.159><c> this</c>

00:03:26.930 --> 00:03:26.940 align:start position:0%
by heart this works with Tau too this
 

00:03:26.940 --> 00:03:28.610 align:start position:0%
by heart this works with Tau too this
common<00:03:27.060><c> variable</c><00:03:27.420><c> swapping</c><00:03:27.840><c> mistake</c><00:03:28.140><c> can</c><00:03:28.440><c> so</c>

00:03:28.610 --> 00:03:28.620 align:start position:0%
common variable swapping mistake can so
 

00:03:28.620 --> 00:03:30.710 align:start position:0%
common variable swapping mistake can so
easily<00:03:28.980><c> slip</c><00:03:29.340><c> into</c><00:03:29.459><c> our</c><00:03:29.760><c> code</c><00:03:29.940><c> especially</c>

00:03:30.710 --> 00:03:30.720 align:start position:0%
easily slip into our code especially
 

00:03:30.720 --> 00:03:32.930 align:start position:0%
easily slip into our code especially
when<00:03:30.959><c> nested</c><00:03:31.440><c> inside</c><00:03:31.680><c> some</c><00:03:32.159><c> dense</c><00:03:32.519><c> inner</c>

00:03:32.930 --> 00:03:32.940 align:start position:0%
when nested inside some dense inner
 

00:03:32.940 --> 00:03:34.790 align:start position:0%
when nested inside some dense inner
block<00:03:33.060><c> rather</c><00:03:33.480><c> than</c><00:03:33.659><c> being</c><00:03:33.900><c> easy</c><00:03:34.140><c> to</c><00:03:34.440><c> read</c><00:03:34.560><c> as</c>

00:03:34.790 --> 00:03:34.800 align:start position:0%
block rather than being easy to read as
 

00:03:34.800 --> 00:03:37.490 align:start position:0%
block rather than being easy to read as
it<00:03:34.920><c> is</c><00:03:35.099><c> here</c><00:03:35.280><c> I</c><00:03:35.760><c> love</c><00:03:35.940><c> of</c><00:03:36.360><c> this</c><00:03:36.599><c> stuff</c><00:03:36.780><c> you</c><00:03:37.319><c> and</c>

00:03:37.490 --> 00:03:37.500 align:start position:0%
it is here I love of this stuff you and
 

00:03:37.500 --> 00:03:39.170 align:start position:0%
it is here I love of this stuff you and
I<00:03:37.620><c> as</c><00:03:37.800><c> high</c><00:03:37.980><c> level</c><00:03:38.159><c> application</c><00:03:38.640><c> developers</c>

00:03:39.170 --> 00:03:39.180 align:start position:0%
I as high level application developers
 

00:03:39.180 --> 00:03:41.570 align:start position:0%
I as high level application developers
can<00:03:39.540><c> so</c><00:03:39.780><c> easily</c><00:03:40.080><c> use</c><00:03:40.440><c> extremely</c><00:03:41.040><c> performant</c>

00:03:41.570 --> 00:03:41.580 align:start position:0%
can so easily use extremely performant
 

00:03:41.580 --> 00:03:43.729 align:start position:0%
can so easily use extremely performant
bare<00:03:42.000><c> metal</c><00:03:42.180><c> operations</c><00:03:42.659><c> giving</c><00:03:43.379><c> a</c><00:03:43.560><c> high</c>

00:03:43.729 --> 00:03:43.739 align:start position:0%
bare metal operations giving a high
 

00:03:43.739 --> 00:03:45.710 align:start position:0%
bare metal operations giving a high
level<00:03:43.860><c> application</c><00:03:44.340><c> speed</c><00:03:44.760><c> and</c><00:03:45.239><c> superpowers</c>

00:03:45.710 --> 00:03:45.720 align:start position:0%
level application speed and superpowers
 

00:03:45.720 --> 00:03:48.589 align:start position:0%
level application speed and superpowers
and<00:03:46.260><c> all</c><00:03:46.500><c> without</c><00:03:46.860><c> making</c><00:03:47.220><c> me</c><00:03:47.400><c> scared</c><00:03:47.879><c> the</c>

00:03:48.589 --> 00:03:48.599 align:start position:0%
and all without making me scared the
 

00:03:48.599 --> 00:03:50.330 align:start position:0%
and all without making me scared the
more<00:03:48.780><c> you</c><00:03:48.900><c> use</c><00:03:49.080><c> the</c><00:03:49.319><c> match</c><00:03:49.500><c> expression</c><00:03:50.040><c> rather</c>

00:03:50.330 --> 00:03:50.340 align:start position:0%
more you use the match expression rather
 

00:03:50.340 --> 00:03:52.670 align:start position:0%
more you use the match expression rather
than<00:03:50.519><c> the</c><00:03:50.640><c> if</c><00:03:50.819><c> expression</c><00:03:51.480><c> the</c><00:03:52.019><c> safer</c><00:03:52.379><c> your</c>

00:03:52.670 --> 00:03:52.680 align:start position:0%
than the if expression the safer your
 

00:03:52.680 --> 00:03:54.830 align:start position:0%
than the if expression the safer your
code<00:03:52.860><c> is</c><00:03:53.159><c> because</c><00:03:53.459><c> if</c><00:03:53.879><c> Expressions</c><00:03:54.420><c> aren't</c><00:03:54.720><c> as</c>

00:03:54.830 --> 00:03:54.840 align:start position:0%
code is because if Expressions aren't as
 

00:03:54.840 --> 00:03:56.809 align:start position:0%
code is because if Expressions aren't as
type<00:03:55.080><c> safe</c><00:03:55.319><c> they</c><00:03:55.860><c> can</c><00:03:56.040><c> do</c><00:03:56.099><c> anything</c><00:03:56.280><c> after</c><00:03:56.580><c> all</c>

00:03:56.809 --> 00:03:56.819 align:start position:0%
type safe they can do anything after all
 

00:03:56.819 --> 00:03:58.369 align:start position:0%
type safe they can do anything after all
if<00:03:57.299><c> you</c><00:03:57.360><c> want</c><00:03:57.480><c> to</c><00:03:57.599><c> do</c><00:03:57.720><c> anything</c><00:03:57.900><c> they</c><00:03:58.260><c> are</c>

00:03:58.369 --> 00:03:58.379 align:start position:0%
if you want to do anything they are
 

00:03:58.379 --> 00:03:59.750 align:start position:0%
if you want to do anything they are
there<00:03:58.560><c> just</c><00:03:58.920><c> as</c><00:03:59.099><c> for</c><00:03:59.280><c> Loops</c><00:03:59.580><c> are</c><00:03:59.640><c> available</c>

00:03:59.750 --> 00:03:59.760 align:start position:0%
there just as for Loops are available
 

00:03:59.760 --> 00:04:02.149 align:start position:0%
there just as for Loops are available
instead<00:04:00.239><c> of</c><00:04:00.360><c> iterators</c><00:04:00.840><c> but</c><00:04:01.500><c> most</c><00:04:01.799><c> of</c><00:04:02.040><c> the</c>

00:04:02.149 --> 00:04:02.159 align:start position:0%
instead of iterators but most of the
 

00:04:02.159 --> 00:04:04.309 align:start position:0%
instead of iterators but most of the
time<00:04:02.400><c> refactoring</c><00:04:03.360><c> into</c><00:04:03.540><c> a</c><00:04:03.720><c> match</c><00:04:03.840><c> expression</c>

00:04:04.309 --> 00:04:04.319 align:start position:0%
time refactoring into a match expression
 

00:04:04.319 --> 00:04:05.750 align:start position:0%
time refactoring into a match expression
will<00:04:04.500><c> allow</c><00:04:04.739><c> the</c><00:04:04.860><c> compiler</c><00:04:05.340><c> to</c><00:04:05.459><c> help</c><00:04:05.640><c> you</c>

00:04:05.750 --> 00:04:05.760 align:start position:0%
will allow the compiler to help you
 

00:04:05.760 --> 00:04:07.490 align:start position:0%
will allow the compiler to help you
enormously<00:04:06.420><c> principally</c><00:04:06.959><c> by</c><00:04:07.200><c> making</c><00:04:07.379><c> sure</c>

00:04:07.490 --> 00:04:07.500 align:start position:0%
enormously principally by making sure
 

00:04:07.500 --> 00:04:09.710 align:start position:0%
enormously principally by making sure
you<00:04:07.680><c> handle</c><00:04:07.980><c> all</c><00:04:08.220><c> input</c><00:04:08.519><c> cases</c><00:04:08.940><c> in</c><00:04:09.420><c> all</c><00:04:09.599><c> your</c>

00:04:09.710 --> 00:04:09.720 align:start position:0%
you handle all input cases in all your
 

00:04:09.720 --> 00:04:12.050 align:start position:0%
you handle all input cases in all your
rust<00:04:09.959><c> coding</c><00:04:10.379><c> I</c><00:04:10.620><c> encourage</c><00:04:10.980><c> you</c><00:04:11.159><c> to</c><00:04:11.400><c> read</c><00:04:11.640><c> the</c>

00:04:12.050 --> 00:04:12.060 align:start position:0%
rust coding I encourage you to read the
 

00:04:12.060 --> 00:04:14.570 align:start position:0%
rust coding I encourage you to read the
error<00:04:12.420><c> again</c><00:04:12.780><c> read</c><00:04:13.319><c> it</c><00:04:13.560><c> twice</c><00:04:13.860><c> read</c><00:04:14.099><c> it</c><00:04:14.340><c> three</c>

00:04:14.570 --> 00:04:14.580 align:start position:0%
error again read it twice read it three
 

00:04:14.580 --> 00:04:17.390 align:start position:0%
error again read it twice read it three
times<00:04:14.819><c> the</c><00:04:15.540><c> information</c><00:04:15.720><c> is</c><00:04:16.500><c> all</c><00:04:16.799><c> there</c><00:04:17.040><c> with</c>

00:04:17.390 --> 00:04:17.400 align:start position:0%
times the information is all there with
 

00:04:17.400 --> 00:04:20.210 align:start position:0%
times the information is all there with
very<00:04:17.639><c> few</c><00:04:17.820><c> exceptions</c><00:04:18.299><c> if</c><00:04:18.900><c> you</c><00:04:19.139><c> only</c><00:04:19.320><c> look</c><00:04:19.680><c> I</c>

00:04:20.210 --> 00:04:20.220 align:start position:0%
very few exceptions if you only look I
 

00:04:20.220 --> 00:04:21.770 align:start position:0%
very few exceptions if you only look I
say<00:04:20.400><c> this</c><00:04:20.519><c> so</c><00:04:20.639><c> often</c><00:04:20.880><c> I</c><00:04:21.060><c> put</c><00:04:21.180><c> it</c><00:04:21.359><c> on</c><00:04:21.479><c> a</c><00:04:21.660><c> stupid</c>

00:04:21.770 --> 00:04:21.780 align:start position:0%
say this so often I put it on a stupid
 

00:04:21.780 --> 00:04:23.990 align:start position:0%
say this so often I put it on a stupid
shirt<00:04:22.079><c> in</c><00:04:22.320><c> my</c><00:04:22.500><c> stupid</c><00:04:22.680><c> store</c><00:04:23.040><c> don't</c><00:04:23.639><c> buy</c><00:04:23.820><c> it</c>

00:04:23.990 --> 00:04:24.000 align:start position:0%
shirt in my stupid store don't buy it
 

00:04:24.000 --> 00:04:26.090 align:start position:0%
shirt in my stupid store don't buy it
whatever<00:04:24.240><c> you</c><00:04:24.479><c> do</c><00:04:24.660><c> those</c><00:04:25.259><c> of</c><00:04:25.440><c> you</c><00:04:25.620><c> coming</c><00:04:25.800><c> from</c>

00:04:26.090 --> 00:04:26.100 align:start position:0%
whatever you do those of you coming from
 

00:04:26.100 --> 00:04:28.969 align:start position:0%
whatever you do those of you coming from
C<00:04:26.400><c> might</c><00:04:27.000><c> mix</c><00:04:27.300><c> up</c><00:04:27.419><c> bitwise</c><00:04:28.139><c> not</c><00:04:28.320><c> and</c><00:04:28.860><c> the</c>

00:04:28.969 --> 00:04:28.979 align:start position:0%
C might mix up bitwise not and the
 

00:04:28.979 --> 00:04:31.370 align:start position:0%
C might mix up bitwise not and the
compiler<00:04:29.340><c> knows</c><00:04:29.639><c> it</c><00:04:29.759><c> this</c><00:04:30.419><c> example</c><00:04:30.780><c> is</c><00:04:31.080><c> a</c>

00:04:31.370 --> 00:04:31.380 align:start position:0%
compiler knows it this example is a
 

00:04:31.380 --> 00:04:33.230 align:start position:0%
compiler knows it this example is a
great<00:04:31.560><c> case</c><00:04:31.919><c> study</c><00:04:32.280><c> of</c><00:04:32.400><c> the</c><00:04:32.580><c> benefits</c><00:04:32.940><c> of</c><00:04:33.060><c> a</c>

00:04:33.230 --> 00:04:33.240 align:start position:0%
great case study of the benefits of a
 

00:04:33.240 --> 00:04:34.850 align:start position:0%
great case study of the benefits of a
strongly<00:04:33.479><c> typed</c><00:04:33.840><c> language</c><00:04:34.020><c> that</c><00:04:34.320><c> pushes</c><00:04:34.680><c> as</c>

00:04:34.850 --> 00:04:34.860 align:start position:0%
strongly typed language that pushes as
 

00:04:34.860 --> 00:04:37.249 align:start position:0%
strongly typed language that pushes as
much<00:04:35.040><c> safety</c><00:04:35.460><c> to</c><00:04:35.580><c> compile</c><00:04:36.000><c> time</c><00:04:36.540><c> as</c><00:04:36.900><c> possible</c>

00:04:37.249 --> 00:04:37.259 align:start position:0%
much safety to compile time as possible
 

00:04:37.259 --> 00:04:39.710 align:start position:0%
much safety to compile time as possible
you<00:04:37.919><c> and</c><00:04:38.100><c> I</c><00:04:38.220><c> as</c><00:04:38.400><c> developers</c><00:04:38.820><c> live</c><00:04:39.060><c> exclusively</c>

00:04:39.710 --> 00:04:39.720 align:start position:0%
you and I as developers live exclusively
 

00:04:39.720 --> 00:04:41.990 align:start position:0%
you and I as developers live exclusively
at<00:04:40.139><c> compile</c><00:04:40.560><c> time</c><00:04:40.740><c> we</c><00:04:41.040><c> sit</c><00:04:41.220><c> in</c><00:04:41.520><c> front</c><00:04:41.699><c> of</c><00:04:41.880><c> our</c>

00:04:41.990 --> 00:04:42.000 align:start position:0%
at compile time we sit in front of our
 

00:04:42.000 --> 00:04:44.030 align:start position:0%
at compile time we sit in front of our
editor<00:04:42.419><c> and</c><00:04:42.840><c> look</c><00:04:43.020><c> at</c><00:04:43.199><c> the</c><00:04:43.380><c> same</c><00:04:43.560><c> source</c><00:04:43.919><c> code</c>

00:04:44.030 --> 00:04:44.040 align:start position:0%
editor and look at the same source code
 

00:04:44.040 --> 00:04:45.469 align:start position:0%
editor and look at the same source code
the<00:04:44.280><c> compiler</c><00:04:44.820><c> does</c>

00:04:45.469 --> 00:04:45.479 align:start position:0%
the compiler does
 

00:04:45.479 --> 00:04:47.330 align:start position:0%
the compiler does
process<00:04:46.139><c> compiler</c><00:04:46.620><c> reads</c><00:04:46.860><c> the</c><00:04:46.919><c> same</c><00:04:47.040><c> source</c>

00:04:47.330 --> 00:04:47.340 align:start position:0%
process compiler reads the same source
 

00:04:47.340 --> 00:04:49.070 align:start position:0%
process compiler reads the same source
as<00:04:47.460><c> we</c><00:04:47.580><c> do</c><00:04:47.759><c> and</c><00:04:47.940><c> because</c><00:04:48.060><c> it's</c><00:04:48.300><c> marked</c><00:04:48.660><c> up</c><00:04:48.780><c> with</c>

00:04:49.070 --> 00:04:49.080 align:start position:0%
as we do and because it's marked up with
 

00:04:49.080 --> 00:04:51.770 align:start position:0%
as we do and because it's marked up with
all<00:04:49.440><c> this</c><00:04:49.620><c> type</c><00:04:49.800><c> metadata</c><00:04:50.580><c> it</c><00:04:51.120><c> could</c><00:04:51.300><c> make</c><00:04:51.540><c> the</c>

00:04:51.770 --> 00:04:51.780 align:start position:0%
all this type metadata it could make the
 

00:04:51.780 --> 00:04:53.930 align:start position:0%
all this type metadata it could make the
same<00:04:51.960><c> decisions</c><00:04:52.380><c> we</c><00:04:52.800><c> can</c><00:04:52.979><c> and</c><00:04:53.580><c> if</c><00:04:53.699><c> it</c><00:04:53.820><c> doesn't</c>

00:04:53.930 --> 00:04:53.940 align:start position:0%
same decisions we can and if it doesn't
 

00:04:53.940 --> 00:04:55.790 align:start position:0%
same decisions we can and if it doesn't
yet<00:04:54.120><c> well</c><00:04:54.360><c> there's</c><00:04:54.540><c> work</c><00:04:54.780><c> to</c><00:04:54.900><c> be</c><00:04:55.020><c> done</c><00:04:55.139><c> open</c><00:04:55.560><c> a</c>

00:04:55.790 --> 00:04:55.800 align:start position:0%
yet well there's work to be done open a
 

00:04:55.800 --> 00:04:58.010 align:start position:0%
yet well there's work to be done open a
PR<00:04:55.919><c> and</c><00:04:56.340><c> pitch</c><00:04:56.520><c> in</c><00:04:56.699><c> I</c><00:04:57.360><c> first</c><00:04:57.540><c> got</c><00:04:57.780><c> this</c>

00:04:58.010 --> 00:04:58.020 align:start position:0%
PR and pitch in I first got this
 

00:04:58.020 --> 00:04:59.990 align:start position:0%
PR and pitch in I first got this
infinite<00:04:58.500><c> error</c><00:04:58.860><c> when</c><00:04:59.040><c> I</c><00:04:59.220><c> tried</c><00:04:59.400><c> to</c><00:04:59.580><c> make</c><00:04:59.759><c> a</c>

00:04:59.990 --> 00:05:00.000 align:start position:0%
infinite error when I tried to make a
 

00:05:00.000 --> 00:05:01.730 align:start position:0%
infinite error when I tried to make a
little<00:05:00.120><c> Dungeon</c><00:05:00.600><c> Crawler</c><00:05:00.900><c> game</c><00:05:01.139><c> where</c><00:05:01.380><c> rooms</c>

00:05:01.730 --> 00:05:01.740 align:start position:0%
little Dungeon Crawler game where rooms
 

00:05:01.740 --> 00:05:03.530 align:start position:0%
little Dungeon Crawler game where rooms
were<00:05:01.919><c> connected</c><00:05:02.340><c> to</c><00:05:02.520><c> other</c><00:05:02.699><c> rooms</c><00:05:03.120><c> and</c><00:05:03.240><c> so</c><00:05:03.419><c> on</c>

00:05:03.530 --> 00:05:03.540 align:start position:0%
were connected to other rooms and so on
 

00:05:03.540 --> 00:05:06.230 align:start position:0%
were connected to other rooms and so on
in<00:05:04.020><c> a</c><00:05:04.199><c> tree</c><00:05:04.320><c> because</c><00:05:05.160><c> structs</c><00:05:05.759><c> must</c><00:05:05.940><c> have</c><00:05:06.060><c> a</c>

00:05:06.230 --> 00:05:06.240 align:start position:0%
in a tree because structs must have a
 

00:05:06.240 --> 00:05:07.730 align:start position:0%
in a tree because structs must have a
size<00:05:06.360><c> that</c><00:05:06.660><c> is</c><00:05:06.780><c> known</c><00:05:06.960><c> at</c><00:05:07.199><c> compile</c><00:05:07.500><c> time</c>

00:05:07.730 --> 00:05:07.740 align:start position:0%
size that is known at compile time
 

00:05:07.740 --> 00:05:09.350 align:start position:0%
size that is known at compile time
recursive<00:05:08.340><c> strikes</c><00:05:08.699><c> could</c><00:05:09.000><c> contain</c>

00:05:09.350 --> 00:05:09.360 align:start position:0%
recursive strikes could contain
 

00:05:09.360 --> 00:05:10.909 align:start position:0%
recursive strikes could contain
themselves<00:05:09.900><c> and</c><00:05:10.259><c> therefore</c><00:05:10.500><c> be</c><00:05:10.680><c> linked</c>

00:05:10.909 --> 00:05:10.919 align:start position:0%
themselves and therefore be linked
 

00:05:10.919 --> 00:05:13.129 align:start position:0%
themselves and therefore be linked
forever<00:05:11.220><c> taking</c><00:05:11.520><c> up</c><00:05:11.699><c> infinite</c><00:05:12.120><c> memory</c><00:05:12.360><c> fun</c>

00:05:13.129 --> 00:05:13.139 align:start position:0%
forever taking up infinite memory fun
 

00:05:13.139 --> 00:05:14.870 align:start position:0%
forever taking up infinite memory fun
rust<00:05:13.800><c> is</c><00:05:14.040><c> telling</c><00:05:14.280><c> you</c><00:05:14.340><c> that</c><00:05:14.460><c> you</c><00:05:14.639><c> have</c><00:05:14.699><c> to</c>

00:05:14.870 --> 00:05:14.880 align:start position:0%
rust is telling you that you have to
 

00:05:14.880 --> 00:05:16.790 align:start position:0%
rust is telling you that you have to
weaken<00:05:15.300><c> the</c><00:05:15.419><c> relationship</c><00:05:15.840><c> slightly</c><00:05:16.440><c> with</c><00:05:16.680><c> a</c>

00:05:16.790 --> 00:05:16.800 align:start position:0%
weaken the relationship slightly with a
 

00:05:16.800 --> 00:05:18.950 align:start position:0%
weaken the relationship slightly with a
pointer<00:05:17.160><c> a</c><00:05:17.639><c> reference</c><00:05:17.940><c> counting</c><00:05:18.360><c> pointer</c><00:05:18.660><c> or</c>

00:05:18.950 --> 00:05:18.960 align:start position:0%
pointer a reference counting pointer or
 

00:05:18.960 --> 00:05:21.230 align:start position:0%
pointer a reference counting pointer or
a<00:05:19.080><c> normal</c><00:05:19.259><c> borrow</c><00:05:19.759><c> introducing</c><00:05:20.759><c> you</c><00:05:20.880><c> gently</c>

00:05:21.230 --> 00:05:21.240 align:start position:0%
a normal borrow introducing you gently
 

00:05:21.240 --> 00:05:24.170 align:start position:0%
a normal borrow introducing you gently
to<00:05:21.780><c> these</c><00:05:22.020><c> more</c><00:05:22.199><c> complex</c><00:05:22.500><c> smart</c><00:05:23.100><c> pointers</c><00:05:23.639><c> and</c>

00:05:24.170 --> 00:05:24.180 align:start position:0%
to these more complex smart pointers and
 

00:05:24.180 --> 00:05:26.029 align:start position:0%
to these more complex smart pointers and
finally<00:05:24.419><c> our</c><00:05:24.720><c> most</c><00:05:24.960><c> complex</c><00:05:25.320><c> example</c><00:05:25.800><c> for</c>

00:05:26.029 --> 00:05:26.039 align:start position:0%
finally our most complex example for
 

00:05:26.039 --> 00:05:28.310 align:start position:0%
finally our most complex example for
today<00:05:26.160><c> one</c><00:05:26.699><c> of</c><00:05:26.820><c> the</c><00:05:26.940><c> core</c><00:05:27.060><c> pillars</c><00:05:27.479><c> of</c><00:05:27.600><c> rust</c><00:05:27.840><c> is</c>

00:05:28.310 --> 00:05:28.320 align:start position:0%
today one of the core pillars of rust is
 

00:05:28.320 --> 00:05:30.770 align:start position:0%
today one of the core pillars of rust is
fearless<00:05:28.860><c> concurrency</c><00:05:29.580><c> which</c><00:05:30.180><c> is</c><00:05:30.300><c> due</c><00:05:30.479><c> to</c><00:05:30.600><c> the</c>

00:05:30.770 --> 00:05:30.780 align:start position:0%
fearless concurrency which is due to the
 

00:05:30.780 --> 00:05:32.390 align:start position:0%
fearless concurrency which is due to the
compiler<00:05:31.199><c> keeping</c><00:05:31.560><c> you</c><00:05:31.680><c> safe</c><00:05:31.860><c> right</c><00:05:32.160><c> through</c>

00:05:32.390 --> 00:05:32.400 align:start position:0%
compiler keeping you safe right through
 

00:05:32.400 --> 00:05:34.430 align:start position:0%
compiler keeping you safe right through
complex<00:05:32.759><c> parallel</c><00:05:33.180><c> processing</c><00:05:33.660><c> here</c><00:05:34.139><c> we</c><00:05:34.320><c> have</c>

00:05:34.430 --> 00:05:34.440 align:start position:0%
complex parallel processing here we have
 

00:05:34.440 --> 00:05:36.290 align:start position:0%
complex parallel processing here we have
a<00:05:34.740><c> multiple</c><00:05:35.039><c> producer</c><00:05:35.520><c> single</c><00:05:35.880><c> consumer</c>

00:05:36.290 --> 00:05:36.300 align:start position:0%
a multiple producer single consumer
 

00:05:36.300 --> 00:05:38.810 align:start position:0%
a multiple producer single consumer
channel<00:05:36.500><c> mpsc</c><00:05:37.500><c> the</c><00:05:38.039><c> most</c><00:05:38.220><c> commonly</c><00:05:38.580><c> used</c>

00:05:38.810 --> 00:05:38.820 align:start position:0%
channel mpsc the most commonly used
 

00:05:38.820 --> 00:05:41.090 align:start position:0%
channel mpsc the most commonly used
channel<00:05:39.120><c> in</c><00:05:39.360><c> Rust</c><00:05:39.539><c> and</c><00:05:39.900><c> the</c><00:05:40.080><c> one</c><00:05:40.259><c> included</c><00:05:40.860><c> in</c>

00:05:41.090 --> 00:05:41.100 align:start position:0%
channel in Rust and the one included in
 

00:05:41.100 --> 00:05:42.409 align:start position:0%
channel in Rust and the one included in
the<00:05:41.220><c> standard</c><00:05:41.520><c> Library</c>

00:05:42.409 --> 00:05:42.419 align:start position:0%
the standard Library
 

00:05:42.419 --> 00:05:44.390 align:start position:0%
the standard Library
this<00:05:43.020><c> channel</c><00:05:43.139><c> is</c><00:05:43.380><c> built</c><00:05:43.620><c> in</c><00:05:43.680><c> pure</c><00:05:43.860><c> rust</c><00:05:44.160><c> by</c>

00:05:44.390 --> 00:05:44.400 align:start position:0%
this channel is built in pure rust by
 

00:05:44.400 --> 00:05:46.249 align:start position:0%
this channel is built in pure rust by
the<00:05:44.520><c> way</c><00:05:44.580><c> it's</c><00:05:44.940><c> not</c><00:05:45.120><c> a</c><00:05:45.300><c> feature</c><00:05:45.600><c> provided</c><00:05:45.960><c> by</c>

00:05:46.249 --> 00:05:46.259 align:start position:0%
the way it's not a feature provided by
 

00:05:46.259 --> 00:05:48.529 align:start position:0%
the way it's not a feature provided by
some<00:05:46.440><c> underlying</c><00:05:46.979><c> runtime</c><00:05:47.400><c> rust</c><00:05:48.120><c> has</c><00:05:48.360><c> almost</c>

00:05:48.529 --> 00:05:48.539 align:start position:0%
some underlying runtime rust has almost
 

00:05:48.539 --> 00:05:50.510 align:start position:0%
some underlying runtime rust has almost
no<00:05:48.960><c> run</c><00:05:49.199><c> time</c><00:05:49.380><c> so</c><00:05:49.800><c> you</c><00:05:49.919><c> can</c><00:05:50.039><c> peek</c><00:05:50.280><c> at</c><00:05:50.400><c> the</c>

00:05:50.510 --> 00:05:50.520 align:start position:0%
no run time so you can peek at the
 

00:05:50.520 --> 00:05:52.129 align:start position:0%
no run time so you can peek at the
Channel's<00:05:50.820><c> source</c><00:05:51.120><c> code</c><00:05:51.240><c> and</c><00:05:51.660><c> write</c><00:05:51.780><c> your</c><00:05:52.020><c> own</c>

00:05:52.129 --> 00:05:52.139 align:start position:0%
Channel's source code and write your own
 

00:05:52.139 --> 00:05:53.870 align:start position:0%
Channel's source code and write your own
if<00:05:52.440><c> you</c><00:05:52.560><c> would</c><00:05:52.680><c> like</c><00:05:52.800><c> and</c><00:05:53.100><c> many</c><00:05:53.400><c> people</c><00:05:53.639><c> have</c>

00:05:53.870 --> 00:05:53.880 align:start position:0%
if you would like and many people have
 

00:05:53.880 --> 00:05:56.629 align:start position:0%
if you would like and many people have
check<00:05:54.360><c> crates.io</c><00:05:55.259><c> for</c><00:05:55.560><c> them</c><00:05:55.740><c> in</c><00:05:56.220><c> this</c><00:05:56.340><c> example</c>

00:05:56.629 --> 00:05:56.639 align:start position:0%
check crates.io for them in this example
 

00:05:56.639 --> 00:05:58.490 align:start position:0%
check crates.io for them in this example
we're<00:05:56.940><c> sending</c><00:05:57.300><c> a</c><00:05:57.419><c> list</c><00:05:57.539><c> of</c><00:05:57.720><c> users</c><00:05:58.080><c> down</c><00:05:58.320><c> a</c>

00:05:58.490 --> 00:05:58.500 align:start position:0%
we're sending a list of users down a
 

00:05:58.500 --> 00:05:59.990 align:start position:0%
we're sending a list of users down a
channel<00:05:58.680><c> and</c><00:05:59.160><c> then</c><00:05:59.280><c> trying</c><00:05:59.460><c> to</c><00:05:59.639><c> add</c><00:05:59.820><c> another</c>

00:05:59.990 --> 00:06:00.000 align:start position:0%
channel and then trying to add another
 

00:06:00.000 --> 00:06:03.050 align:start position:0%
channel and then trying to add another
user<00:06:00.479><c> onto</c><00:06:00.780><c> that</c><00:06:00.960><c> list</c><00:06:01.139><c> after</c><00:06:01.680><c> it's</c><00:06:01.860><c> sent</c><00:06:02.160><c> in</c>

00:06:03.050 --> 00:06:03.060 align:start position:0%
user onto that list after it's sent in
 

00:06:03.060 --> 00:06:04.730 align:start position:0%
user onto that list after it's sent in
other<00:06:03.180><c> languages</c><00:06:03.479><c> such</c><00:06:03.780><c> as</c><00:06:03.900><c> go</c><00:06:04.199><c> the</c><00:06:04.620><c> simple</c>

00:06:04.730 --> 00:06:04.740 align:start position:0%
other languages such as go the simple
 

00:06:04.740 --> 00:06:06.650 align:start position:0%
other languages such as go the simple
type<00:06:05.039><c> system</c><00:06:05.340><c> can't</c><00:06:05.820><c> keep</c><00:06:06.060><c> you</c><00:06:06.240><c> safe</c><00:06:06.419><c> when</c>

00:06:06.650 --> 00:06:06.660 align:start position:0%
type system can't keep you safe when
 

00:06:06.660 --> 00:06:09.110 align:start position:0%
type system can't keep you safe when
using<00:06:07.020><c> channels</c><00:06:07.320><c> you</c><00:06:07.500><c> have</c><00:06:07.680><c> to</c><00:06:07.860><c> remember</c><00:06:08.160><c> what</c>

00:06:09.110 --> 00:06:09.120 align:start position:0%
using channels you have to remember what
 

00:06:09.120 --> 00:06:11.210 align:start position:0%
using channels you have to remember what
the<00:06:09.300><c> rules</c><00:06:09.660><c> are</c><00:06:09.900><c> for</c><00:06:10.259><c> parallel</c><00:06:10.740><c> processing</c>

00:06:11.210 --> 00:06:11.220 align:start position:0%
the rules are for parallel processing
 

00:06:11.220 --> 00:06:13.370 align:start position:0%
the rules are for parallel processing
one<00:06:11.759><c> of</c><00:06:11.880><c> these</c><00:06:12.000><c> rules</c><00:06:12.240><c> is</c><00:06:12.419><c> after</c><00:06:12.960><c> you've</c><00:06:13.199><c> sent</c>

00:06:13.370 --> 00:06:13.380 align:start position:0%
one of these rules is after you've sent
 

00:06:13.380 --> 00:06:15.290 align:start position:0%
one of these rules is after you've sent
a<00:06:13.500><c> variable</c><00:06:13.740><c> into</c><00:06:13.919><c> a</c><00:06:14.100><c> Channel</c><00:06:14.220><c> or</c><00:06:14.460><c> thread</c><00:06:14.759><c> you</c>

00:06:15.290 --> 00:06:15.300 align:start position:0%
a variable into a Channel or thread you
 

00:06:15.300 --> 00:06:17.570 align:start position:0%
a variable into a Channel or thread you
can't<00:06:15.479><c> safely</c><00:06:16.139><c> reuse</c><00:06:16.620><c> it</c><00:06:16.800><c> it</c><00:06:17.220><c> could</c><00:06:17.460><c> be</c>

00:06:17.570 --> 00:06:17.580 align:start position:0%
can't safely reuse it it could be
 

00:06:17.580 --> 00:06:19.909 align:start position:0%
can't safely reuse it it could be
modified<00:06:18.000><c> or</c><00:06:18.479><c> overwritten</c><00:06:19.080><c> or</c><00:06:19.320><c> used</c><00:06:19.500><c> in</c>

00:06:19.909 --> 00:06:19.919 align:start position:0%
modified or overwritten or used in
 

00:06:19.919 --> 00:06:21.890 align:start position:0%
modified or overwritten or used in
unpredictable<00:06:20.460><c> Ways</c><00:06:20.880><c> by</c><00:06:21.180><c> unpredictable</c>

00:06:21.890 --> 00:06:21.900 align:start position:0%
unpredictable Ways by unpredictable
 

00:06:21.900 --> 00:06:24.170 align:start position:0%
unpredictable Ways by unpredictable
threads<00:06:22.440><c> the</c><00:06:23.039><c> rust</c><00:06:23.220><c> compiler</c><00:06:23.819><c> doesn't</c><00:06:23.940><c> need</c>

00:06:24.170 --> 00:06:24.180 align:start position:0%
threads the rust compiler doesn't need
 

00:06:24.180 --> 00:06:26.090 align:start position:0%
threads the rust compiler doesn't need
to<00:06:24.300><c> treat</c><00:06:24.479><c> threaded</c><00:06:25.020><c> code</c><00:06:25.199><c> as</c><00:06:25.440><c> a</c><00:06:25.680><c> special</c><00:06:25.860><c> case</c>

00:06:26.090 --> 00:06:26.100 align:start position:0%
to treat threaded code as a special case
 

00:06:26.100 --> 00:06:28.249 align:start position:0%
to treat threaded code as a special case
to<00:06:26.400><c> maintain</c><00:06:26.639><c> safety</c><00:06:27.060><c> the</c><00:06:27.479><c> borrow</c><00:06:27.720><c> Checkers</c>

00:06:28.249 --> 00:06:28.259 align:start position:0%
to maintain safety the borrow Checkers
 

00:06:28.259 --> 00:06:30.529 align:start position:0%
to maintain safety the borrow Checkers
Simple<00:06:28.560><c> Rules</c><00:06:28.919><c> keep</c><00:06:29.220><c> us</c><00:06:29.400><c> safe</c><00:06:29.639><c> with</c><00:06:30.180><c> the</c><00:06:30.419><c> same</c>

00:06:30.529 --> 00:06:30.539 align:start position:0%
Simple Rules keep us safe with the same
 

00:06:30.539 --> 00:06:33.469 align:start position:0%
Simple Rules keep us safe with the same
Rich<00:06:30.840><c> errors</c><00:06:31.199><c> we've</c><00:06:31.500><c> seen</c><00:06:31.680><c> all</c><00:06:32.280><c> along</c><00:06:32.460><c> rust</c><00:06:33.300><c> is</c>

00:06:33.469 --> 00:06:33.479 align:start position:0%
Rich errors we've seen all along rust is
 

00:06:33.479 --> 00:06:35.330 align:start position:0%
Rich errors we've seen all along rust is
unfamiliar<00:06:33.960><c> but</c><00:06:34.440><c> it</c><00:06:34.620><c> can</c><00:06:34.740><c> be</c><00:06:34.919><c> understood</c>

00:06:35.330 --> 00:06:35.340 align:start position:0%
unfamiliar but it can be understood
 

00:06:35.340 --> 00:06:37.610 align:start position:0%
unfamiliar but it can be understood
through<00:06:35.880><c> thorough</c><00:06:36.300><c> thought</c><00:06:36.479><c> though</c><00:06:36.780><c> read</c><00:06:37.440><c> the</c>

00:06:37.610 --> 00:06:37.620 align:start position:0%
through thorough thought though read the
 

00:06:37.620 --> 00:06:39.710 align:start position:0%
through thorough thought though read the
error<00:06:37.919><c> again</c><00:06:37.979><c> thank</c><00:06:38.819><c> you</c><00:06:39.000><c> if</c><00:06:39.360><c> you</c><00:06:39.479><c> would</c><00:06:39.539><c> like</c>

00:06:39.710 --> 00:06:39.720 align:start position:0%
error again thank you if you would like
 

00:06:39.720 --> 00:06:41.210 align:start position:0%
error again thank you if you would like
to<00:06:39.840><c> support</c><00:06:39.900><c> my</c><00:06:40.199><c> channel</c><00:06:40.319><c> and</c><00:06:40.560><c> get</c><00:06:40.740><c> early</c><00:06:40.919><c> ad</c>

00:06:41.210 --> 00:06:41.220 align:start position:0%
to support my channel and get early ad
 

00:06:41.220 --> 00:06:42.890 align:start position:0%
to support my channel and get early ad
free<00:06:41.400><c> and</c><00:06:41.639><c> tracking</c><00:06:41.940><c> free</c><00:06:42.000><c> videos</c><00:06:42.180><c> and</c><00:06:42.419><c> VIP</c>

00:06:42.890 --> 00:06:42.900 align:start position:0%
free and tracking free videos and VIP
 

00:06:42.900 --> 00:06:44.930 align:start position:0%
free and tracking free videos and VIP
Discord<00:06:43.380><c> access</c><00:06:43.620><c> head</c><00:06:44.100><c> to</c><00:06:44.280><c> patreon.com</c>

00:06:44.930 --> 00:06:44.940 align:start position:0%
Discord access head to patreon.com
 

00:06:44.940 --> 00:06:47.150 align:start position:0%
Discord access head to patreon.com
forward<00:06:45.360><c> slash</c><00:06:45.660><c> no</c><00:06:45.900><c> boilerplate</c><00:06:46.560><c> or</c><00:06:46.860><c> if</c><00:06:47.039><c> urban</c>

00:06:47.150 --> 00:06:47.160 align:start position:0%
forward slash no boilerplate or if urban
 

00:06:47.160 --> 00:06:49.309 align:start position:0%
forward slash no boilerplate or if urban
fantasy<00:06:47.639><c> is</c><00:06:47.699><c> more</c><00:06:47.880><c> your</c><00:06:48.060><c> bag</c><00:06:48.240><c> do</c><00:06:48.660><c> listen</c><00:06:48.900><c> to</c><00:06:49.080><c> a</c>

00:06:49.309 --> 00:06:49.319 align:start position:0%
fantasy is more your bag do listen to a
 

00:06:49.319 --> 00:06:51.170 align:start position:0%
fantasy is more your bag do listen to a
strange<00:06:49.500><c> and</c><00:06:49.680><c> beautiful</c><00:06:49.919><c> podcast</c><00:06:50.340><c> I</c><00:06:50.819><c> produce</c>

00:06:51.170 --> 00:06:51.180 align:start position:0%
strange and beautiful podcast I produce
 

00:06:51.180 --> 00:06:53.330 align:start position:0%
strange and beautiful podcast I produce
golden<00:06:51.780><c> mode</c><00:06:52.080><c> and</c><00:06:52.259><c> Prometheus</c><00:06:52.800><c> transcripts</c>

00:06:53.330 --> 00:06:53.340 align:start position:0%
golden mode and Prometheus transcripts
 

00:06:53.340 --> 00:06:54.830 align:start position:0%
golden mode and Prometheus transcripts
and<00:06:53.520><c> compile</c><00:06:53.880><c> checked</c><00:06:54.180><c> markdown</c><00:06:54.479><c> source</c><00:06:54.780><c> code</c>

00:06:54.830 --> 00:06:54.840 align:start position:0%
and compile checked markdown source code
 

00:06:54.840 --> 00:06:56.210 align:start position:0%
and compile checked markdown source code
are<00:06:55.020><c> available</c><00:06:55.139><c> on</c><00:06:55.380><c> GitHub</c><00:06:55.680><c> links</c><00:06:55.979><c> in</c><00:06:56.100><c> the</c>

00:06:56.210 --> 00:06:56.220 align:start position:0%
are available on GitHub links in the
 

00:06:56.220 --> 00:06:58.010 align:start position:0%
are available on GitHub links in the
description<00:06:56.460><c> and</c><00:06:57.000><c> Corrections</c><00:06:57.539><c> are</c><00:06:57.780><c> in</c><00:06:57.900><c> the</c>

00:06:58.010 --> 00:06:58.020 align:start position:0%
description and Corrections are in the
 

00:06:58.020 --> 00:07:00.170 align:start position:0%
description and Corrections are in the
pinned<00:06:58.319><c> irata</c><00:06:58.860><c> comment</c><00:06:59.160><c> thank</c><00:06:59.819><c> you</c><00:06:59.940><c> so</c><00:07:00.060><c> much</c>

00:07:00.170 --> 00:07:00.180 align:start position:0%
pinned irata comment thank you so much
 

00:07:00.180 --> 00:07:03.680 align:start position:0%
pinned irata comment thank you so much
for<00:07:00.300><c> watching</c><00:07:00.600><c> talk</c><00:07:01.020><c> to</c><00:07:01.199><c> you</c><00:07:01.259><c> on</c><00:07:01.380><c> Discord</c>

