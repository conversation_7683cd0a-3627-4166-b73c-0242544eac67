WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:01.550 align:start position:0%
 
hey<00:00:00.160><c> and</c><00:00:00.240><c> welcome</c><00:00:00.480><c> back</c><00:00:00.599><c> to</c><00:00:00.760><c> day</c><00:00:00.960><c> 19</c><00:00:01.319><c> of</c><00:00:01.439><c> the</c>

00:00:01.550 --> 00:00:01.560 align:start position:0%
hey and welcome back to day 19 of the
 

00:00:01.560 --> 00:00:03.189 align:start position:0%
hey and welcome back to day 19 of the
31-day<00:00:02.159><c> challenge</c><00:00:02.480><c> in</c><00:00:02.600><c> March</c><00:00:02.960><c> and</c><00:00:03.040><c> today</c>

00:00:03.189 --> 00:00:03.199 align:start position:0%
31-day challenge in March and today
 

00:00:03.199 --> 00:00:04.590 align:start position:0%
31-day challenge in March and today
we'll<00:00:03.360><c> be</c><00:00:03.439><c> going</c><00:00:03.600><c> over</c><00:00:03.800><c> L</c><00:00:04.080><c> chain</c><00:00:04.319><c> Vector</c>

00:00:04.590 --> 00:00:04.600 align:start position:0%
we'll be going over L chain Vector
 

00:00:04.600 --> 00:00:07.309 align:start position:0%
we'll be going over L chain Vector
databases<00:00:05.200><c> and</c><00:00:05.400><c> PDFs</c><00:00:06.080><c> we</c><00:00:06.160><c> will</c><00:00:06.359><c> load</c><00:00:06.839><c> a</c><00:00:07.000><c> PDF</c>

00:00:07.309 --> 00:00:07.319 align:start position:0%
databases and PDFs we will load a PDF
 

00:00:07.319 --> 00:00:09.150 align:start position:0%
databases and PDFs we will load a PDF
using<00:00:07.560><c> a</c><00:00:07.640><c> len</c><00:00:07.839><c> chain</c><00:00:08.120><c> tool</c><00:00:08.519><c> into</c><00:00:08.719><c> the</c><00:00:08.880><c> vector</c>

00:00:09.150 --> 00:00:09.160 align:start position:0%
using a len chain tool into the vector
 

00:00:09.160 --> 00:00:10.589 align:start position:0%
using a len chain tool into the vector
database<00:00:09.679><c> and</c><00:00:09.760><c> then</c><00:00:09.880><c> be</c><00:00:10.000><c> able</c><00:00:10.120><c> to</c><00:00:10.240><c> ask</c><00:00:10.400><c> that</c>

00:00:10.589 --> 00:00:10.599 align:start position:0%
database and then be able to ask that
 

00:00:10.599 --> 00:00:12.150 align:start position:0%
database and then be able to ask that
any<00:00:10.880><c> question</c><00:00:11.200><c> about</c><00:00:11.400><c> the</c><00:00:11.519><c> PDF</c><00:00:11.840><c> and</c><00:00:11.920><c> get</c><00:00:12.000><c> a</c>

00:00:12.150 --> 00:00:12.160 align:start position:0%
any question about the PDF and get a
 

00:00:12.160 --> 00:00:13.470 align:start position:0%
any question about the PDF and get a
response<00:00:12.559><c> back</c><00:00:12.759><c> all</c><00:00:12.840><c> right</c><00:00:12.960><c> for</c><00:00:13.080><c> the</c><00:00:13.160><c> example</c>

00:00:13.470 --> 00:00:13.480 align:start position:0%
response back all right for the example
 

00:00:13.480 --> 00:00:15.390 align:start position:0%
response back all right for the example
today<00:00:13.960><c> I</c><00:00:14.080><c> have</c><00:00:14.200><c> an</c><00:00:14.320><c> ancient</c><00:00:14.599><c> Rome</c><00:00:14.879><c> PDF</c><00:00:15.200><c> that</c><00:00:15.280><c> I</c>

00:00:15.390 --> 00:00:15.400 align:start position:0%
today I have an ancient Rome PDF that I
 

00:00:15.400 --> 00:00:17.109 align:start position:0%
today I have an ancient Rome PDF that I
just<00:00:15.559><c> found</c><00:00:15.879><c> online</c><00:00:16.240><c> it's</c><00:00:16.359><c> like</c><00:00:16.560><c> 100</c><00:00:16.760><c> pages</c>

00:00:17.109 --> 00:00:17.119 align:start position:0%
just found online it's like 100 pages
 

00:00:17.119 --> 00:00:18.550 align:start position:0%
just found online it's like 100 pages
about<00:00:17.320><c> ancient</c><00:00:17.600><c> Rome</c><00:00:18.000><c> you'll</c><00:00:18.160><c> also</c><00:00:18.359><c> have</c><00:00:18.480><c> this</c>

00:00:18.550 --> 00:00:18.560 align:start position:0%
about ancient Rome you'll also have this
 

00:00:18.560 --> 00:00:20.269 align:start position:0%
about ancient Rome you'll also have this
in<00:00:18.640><c> the</c><00:00:18.760><c> GitHub</c><00:00:19.359><c> and</c><00:00:19.480><c> what</c><00:00:19.560><c> we're</c><00:00:19.720><c> doing</c><00:00:20.119><c> is</c>

00:00:20.269 --> 00:00:20.279 align:start position:0%
in the GitHub and what we're doing is
 

00:00:20.279 --> 00:00:22.310 align:start position:0%
in the GitHub and what we're doing is
we're<00:00:20.400><c> going</c><00:00:20.519><c> to</c><00:00:20.640><c> be</c><00:00:20.760><c> loading</c><00:00:21.240><c> that</c><00:00:21.480><c> document</c>

00:00:22.310 --> 00:00:22.320 align:start position:0%
we're going to be loading that document
 

00:00:22.320 --> 00:00:25.029 align:start position:0%
we're going to be loading that document
using<00:00:22.800><c> a</c><00:00:22.920><c> lang</c><00:00:23.240><c> chain</c><00:00:23.519><c> tool</c><00:00:23.880><c> called</c><00:00:24.320><c> Pi</c><00:00:24.680><c> PDF</c>

00:00:25.029 --> 00:00:25.039 align:start position:0%
using a lang chain tool called Pi PDF
 

00:00:25.039 --> 00:00:26.950 align:start position:0%
using a lang chain tool called Pi PDF
loader<00:00:25.640><c> we're</c><00:00:25.760><c> going</c><00:00:25.840><c> to</c><00:00:25.920><c> be</c><00:00:26.000><c> loading</c><00:00:26.400><c> that</c>

00:00:26.950 --> 00:00:26.960 align:start position:0%
loader we're going to be loading that
 

00:00:26.960 --> 00:00:29.310 align:start position:0%
loader we're going to be loading that
splitting<00:00:27.560><c> the</c><00:00:27.720><c> text</c><00:00:28.119><c> from</c><00:00:28.320><c> that</c><00:00:28.560><c> document</c>

00:00:29.310 --> 00:00:29.320 align:start position:0%
splitting the text from that document
 

00:00:29.320 --> 00:00:31.830 align:start position:0%
splitting the text from that document
then<00:00:29.439><c> storing</c><00:00:30.240><c> into</c><00:00:30.599><c> a</c><00:00:30.800><c> vector</c><00:00:31.119><c> database</c>

00:00:31.830 --> 00:00:31.840 align:start position:0%
then storing into a vector database
 

00:00:31.840 --> 00:00:34.790 align:start position:0%
then storing into a vector database
locally<00:00:32.279><c> so</c><00:00:32.559><c> into</c><00:00:32.960><c> meta's</c><00:00:33.640><c> F</c><00:00:33.920><c> or</c><00:00:34.160><c> phase</c><00:00:34.520><c> Vector</c>

00:00:34.790 --> 00:00:34.800 align:start position:0%
locally so into meta's F or phase Vector
 

00:00:34.800 --> 00:00:36.510 align:start position:0%
locally so into meta's F or phase Vector
database<00:00:35.520><c> so</c><00:00:35.680><c> I</c><00:00:35.760><c> asked</c><00:00:35.920><c> it</c><00:00:36.040><c> to</c><00:00:36.120><c> summarize</c>

00:00:36.510 --> 00:00:36.520 align:start position:0%
database so I asked it to summarize
 

00:00:36.520 --> 00:00:38.830 align:start position:0%
database so I asked it to summarize
Julius<00:00:36.840><c> Caesar</c><00:00:37.600><c> from</c><00:00:37.879><c> the</c><00:00:38.040><c> PDF</c><00:00:38.440><c> that</c><00:00:38.520><c> we</c><00:00:38.640><c> gave</c>

00:00:38.830 --> 00:00:38.840 align:start position:0%
Julius Caesar from the PDF that we gave
 

00:00:38.840 --> 00:00:40.670 align:start position:0%
Julius Caesar from the PDF that we gave
to<00:00:38.960><c> the</c><00:00:39.040><c> vector</c><00:00:39.280><c> database</c><00:00:39.879><c> and</c><00:00:40.039><c> then</c><00:00:40.280><c> we</c><00:00:40.399><c> got</c><00:00:40.520><c> a</c>

00:00:40.670 --> 00:00:40.680 align:start position:0%
to the vector database and then we got a
 

00:00:40.680 --> 00:00:43.029 align:start position:0%
to the vector database and then we got a
result<00:00:41.480><c> talking</c><00:00:42.000><c> about</c><00:00:42.239><c> Julius</c><00:00:42.559><c> Caesar</c><00:00:42.960><c> that</c>

00:00:43.029 --> 00:00:43.039 align:start position:0%
result talking about Julius Caesar that
 

00:00:43.039 --> 00:00:44.590 align:start position:0%
result talking about Julius Caesar that
is<00:00:43.160><c> a</c><00:00:43.280><c> Roman</c><00:00:43.520><c> leader</c><00:00:43.800><c> whose</c><00:00:44.000><c> power</c><00:00:44.200><c> R</c><00:00:44.480><c> through</c>

00:00:44.590 --> 00:00:44.600 align:start position:0%
is a Roman leader whose power R through
 

00:00:44.600 --> 00:00:47.310 align:start position:0%
is a Roman leader whose power R through
military<00:00:45.280><c> campaigns</c><00:00:46.280><c> yada</c><00:00:46.559><c> yada</c><00:00:46.800><c> yada</c><00:00:47.199><c> but</c>

00:00:47.310 --> 00:00:47.320 align:start position:0%
military campaigns yada yada yada but
 

00:00:47.320 --> 00:00:49.310 align:start position:0%
military campaigns yada yada yada but
enemies<00:00:47.640><c> in</c><00:00:47.800><c> the</c><00:00:47.920><c> Senate</c><00:00:48.399><c> ultimately</c><00:00:48.800><c> led</c><00:00:48.960><c> to</c>

00:00:49.310 --> 00:00:49.320 align:start position:0%
enemies in the Senate ultimately led to
 

00:00:49.320 --> 00:00:51.229 align:start position:0%
enemies in the Senate ultimately led to
assassination<00:00:50.320><c> okay</c><00:00:50.440><c> so</c><00:00:50.640><c> there</c><00:00:50.760><c> are</c><00:00:51.000><c> quite</c><00:00:51.160><c> a</c>

00:00:51.229 --> 00:00:51.239 align:start position:0%
assassination okay so there are quite a
 

00:00:51.239 --> 00:00:53.110 align:start position:0%
assassination okay so there are quite a
few<00:00:51.440><c> things</c><00:00:51.719><c> that</c><00:00:51.920><c> we</c><00:00:52.079><c> will</c><00:00:52.239><c> need</c><00:00:52.440><c> to</c><00:00:52.680><c> import</c>

00:00:53.110 --> 00:00:53.120 align:start position:0%
few things that we will need to import
 

00:00:53.120 --> 00:00:54.189 align:start position:0%
few things that we will need to import
here<00:00:53.359><c> and</c><00:00:53.480><c> if</c><00:00:53.559><c> you're</c><00:00:53.640><c> going</c><00:00:53.760><c> to</c><00:00:53.840><c> really</c><00:00:54.000><c> use</c>

00:00:54.189 --> 00:00:54.199 align:start position:0%
here and if you're going to really use
 

00:00:54.199 --> 00:00:55.790 align:start position:0%
here and if you're going to really use
anything<00:00:54.399><c> with</c><00:00:54.520><c> Lang</c><00:00:54.760><c> chain</c><00:00:55.039><c> to</c><00:00:55.160><c> help</c><00:00:55.359><c> you</c>

00:00:55.790 --> 00:00:55.800 align:start position:0%
anything with Lang chain to help you
 

00:00:55.800 --> 00:00:57.709 align:start position:0%
anything with Lang chain to help you
load<00:00:56.199><c> documents</c><00:00:56.760><c> or</c><00:00:56.920><c> store</c><00:00:57.239><c> things</c><00:00:57.440><c> into</c><00:00:57.600><c> a</c>

00:00:57.709 --> 00:00:57.719 align:start position:0%
load documents or store things into a
 

00:00:57.719 --> 00:01:00.549 align:start position:0%
load documents or store things into a
vector<00:00:58.039><c> database</c><00:00:58.800><c> which</c><00:00:59.000><c> there</c><00:00:59.120><c> are</c><00:01:00.280><c> options</c>

00:01:00.549 --> 00:01:00.559 align:start position:0%
vector database which there are options
 

00:01:00.559 --> 00:01:02.549 align:start position:0%
vector database which there are options
to<00:01:00.719><c> do</c><00:01:00.879><c> so</c><00:01:01.079><c> this</c><00:01:01.160><c> is</c><00:01:01.359><c> just</c><00:01:01.480><c> one</c><00:01:01.600><c> of</c><00:01:01.760><c> them</c><00:01:02.320><c> then</c>

00:01:02.549 --> 00:01:02.559 align:start position:0%
to do so this is just one of them then
 

00:01:02.559 --> 00:01:04.710 align:start position:0%
to do so this is just one of them then
you<00:01:02.680><c> will</c><00:01:02.920><c> be</c><00:01:03.760><c> have</c><00:01:04.000><c> you</c><00:01:04.119><c> will</c><00:01:04.360><c> have</c><00:01:04.519><c> quite</c><00:01:04.640><c> a</c>

00:01:04.710 --> 00:01:04.720 align:start position:0%
you will be have you will have quite a
 

00:01:04.720 --> 00:01:06.670 align:start position:0%
you will be have you will have quite a
few<00:01:04.879><c> Imports</c><00:01:05.799><c> now</c><00:01:06.000><c> the</c><00:01:06.159><c> next</c><00:01:06.320><c> thing</c><00:01:06.439><c> we'll</c>

00:01:06.670 --> 00:01:06.680 align:start position:0%
few Imports now the next thing we'll
 

00:01:06.680 --> 00:01:09.310 align:start position:0%
few Imports now the next thing we'll
need<00:01:07.080><c> is</c><00:01:07.759><c> uh</c><00:01:07.880><c> the</c><00:01:08.119><c> environment</c><00:01:08.560><c> variables</c><00:01:09.040><c> so</c>

00:01:09.310 --> 00:01:09.320 align:start position:0%
need is uh the environment variables so
 

00:01:09.320 --> 00:01:12.070 align:start position:0%
need is uh the environment variables so
if<00:01:09.400><c> you</c><00:01:09.560><c> want</c><00:01:09.680><c> to</c><00:01:09.799><c> use</c><00:01:10.040><c> open</c><00:01:10.400><c> AI</c><00:01:11.040><c> API</c><00:01:11.600><c> or</c><00:01:11.840><c> really</c>

00:01:12.070 --> 00:01:12.080 align:start position:0%
if you want to use open AI API or really
 

00:01:12.080 --> 00:01:13.789 align:start position:0%
if you want to use open AI API or really
anything<00:01:12.320><c> you</c><00:01:12.439><c> just</c><00:01:12.520><c> need</c><00:01:12.640><c> to</c><00:01:12.799><c> have</c><00:01:13.200><c> this</c><00:01:13.400><c> API</c>

00:01:13.789 --> 00:01:13.799 align:start position:0%
anything you just need to have this API
 

00:01:13.799 --> 00:01:15.670 align:start position:0%
anything you just need to have this API
key<00:01:14.040><c> set</c><00:01:14.320><c> here</c><00:01:14.960><c> um</c><00:01:15.080><c> if</c><00:01:15.159><c> you're</c><00:01:15.240><c> going</c><00:01:15.360><c> to</c><00:01:15.479><c> use</c>

00:01:15.670 --> 00:01:15.680 align:start position:0%
key set here um if you're going to use
 

00:01:15.680 --> 00:01:17.670 align:start position:0%
key set here um if you're going to use
like<00:01:15.799><c> oama</c><00:01:16.280><c> or</c><00:01:16.360><c> Alm</c><00:01:16.680><c> Studio</c><00:01:17.159><c> then</c><00:01:17.320><c> it</c><00:01:17.439><c> doesn't</c>

00:01:17.670 --> 00:01:17.680 align:start position:0%
like oama or Alm Studio then it doesn't
 

00:01:17.680 --> 00:01:20.350 align:start position:0%
like oama or Alm Studio then it doesn't
matter<00:01:18.320><c> what</c><00:01:18.479><c> API</c><00:01:18.840><c> key</c><00:01:19.040><c> you</c><00:01:19.159><c> have</c><00:01:19.360><c> here</c><00:01:19.880><c> right</c>

00:01:20.350 --> 00:01:20.360 align:start position:0%
matter what API key you have here right
 

00:01:20.360 --> 00:01:21.670 align:start position:0%
matter what API key you have here right
um<00:01:20.520><c> I'm</c><00:01:20.640><c> choosing</c><00:01:20.920><c> the</c><00:01:21.040><c> model</c><00:01:21.320><c> because</c><00:01:21.479><c> I</c><00:01:21.560><c> will</c>

00:01:21.670 --> 00:01:21.680 align:start position:0%
um I'm choosing the model because I will
 

00:01:21.680 --> 00:01:23.749 align:start position:0%
um I'm choosing the model because I will
just<00:01:21.840><c> will</c><00:01:21.960><c> be</c><00:01:22.119><c> using</c><00:01:22.520><c> open</c><00:01:22.880><c> as</c><00:01:23.079><c> API</c><00:01:23.479><c> to</c><00:01:23.600><c> make</c>

00:01:23.749 --> 00:01:23.759 align:start position:0%
just will be using open as API to make
 

00:01:23.759 --> 00:01:25.870 align:start position:0%
just will be using open as API to make
this<00:01:23.960><c> quick</c><00:01:24.880><c> and</c><00:01:25.040><c> then</c><00:01:25.400><c> uh</c><00:01:25.479><c> if</c><00:01:25.560><c> you</c><00:01:25.640><c> want</c><00:01:25.720><c> to</c>

00:01:25.870 --> 00:01:25.880 align:start position:0%
this quick and then uh if you want to
 

00:01:25.880 --> 00:01:28.590 align:start position:0%
this quick and then uh if you want to
use<00:01:26.720><c> like</c><00:01:26.840><c> I</c><00:01:26.920><c> said</c><00:01:27.079><c> oama</c><00:01:27.600><c> or</c><00:01:27.799><c> LM</c><00:01:28.079><c> Studio</c><00:01:28.479><c> you</c>

00:01:28.590 --> 00:01:28.600 align:start position:0%
use like I said oama or LM Studio you
 

00:01:28.600 --> 00:01:30.510 align:start position:0%
use like I said oama or LM Studio you
just<00:01:28.680><c> need</c><00:01:28.840><c> the</c><00:01:29.040><c> base</c><00:01:29.240><c> URL</c><00:01:29.640><c> envir</c><00:01:30.159><c> variable</c>

00:01:30.510 --> 00:01:30.520 align:start position:0%
just need the base URL envir variable
 

00:01:30.520 --> 00:01:32.550 align:start position:0%
just need the base URL envir variable
here<00:01:30.759><c> and</c><00:01:30.880><c> then</c><00:01:31.040><c> just</c><00:01:31.320><c> give</c><00:01:32.000><c> you</c><00:01:32.119><c> just</c><00:01:32.240><c> need</c><00:01:32.360><c> to</c>

00:01:32.550 --> 00:01:32.560 align:start position:0%
here and then just give you just need to
 

00:01:32.560 --> 00:01:34.510 align:start position:0%
here and then just give you just need to
give<00:01:32.759><c> the</c><00:01:32.880><c> URL</c><00:01:33.640><c> for</c><00:01:33.960><c> whichever</c><00:01:34.240><c> one</c><00:01:34.399><c> you're</c>

00:01:34.510 --> 00:01:34.520 align:start position:0%
give the URL for whichever one you're
 

00:01:34.520 --> 00:01:36.030 align:start position:0%
give the URL for whichever one you're
using<00:01:34.960><c> I</c><00:01:35.040><c> have</c><00:01:35.200><c> another</c><00:01:35.439><c> variable</c><00:01:35.720><c> for</c><00:01:35.880><c> the</c>

00:01:36.030 --> 00:01:36.040 align:start position:0%
using I have another variable for the
 

00:01:36.040 --> 00:01:38.069 align:start position:0%
using I have another variable for the
vector<00:01:36.399><c> database</c><00:01:37.320><c> as</c><00:01:37.439><c> you</c><00:01:37.520><c> can</c><00:01:37.600><c> see</c><00:01:37.720><c> over</c><00:01:37.960><c> here</c>

00:01:38.069 --> 00:01:38.079 align:start position:0%
vector database as you can see over here
 

00:01:38.079 --> 00:01:39.350 align:start position:0%
vector database as you can see over here
and<00:01:38.159><c> I'll</c><00:01:38.280><c> delete</c><00:01:38.520><c> this</c><00:01:38.640><c> so</c><00:01:38.880><c> next</c><00:01:39.000><c> time</c><00:01:39.119><c> I</c><00:01:39.200><c> run</c>

00:01:39.350 --> 00:01:39.360 align:start position:0%
and I'll delete this so next time I run
 

00:01:39.360 --> 00:01:41.270 align:start position:0%
and I'll delete this so next time I run
it<00:01:39.560><c> but</c><00:01:39.759><c> this</c><00:01:39.840><c> will</c><00:01:40.320><c> this</c><00:01:40.399><c> will</c><00:01:40.560><c> be</c><00:01:40.759><c> where</c><00:01:41.000><c> we</c>

00:01:41.270 --> 00:01:41.280 align:start position:0%
it but this will this will be where we
 

00:01:41.280 --> 00:01:43.389 align:start position:0%
it but this will this will be where we
save<00:01:41.720><c> the</c><00:01:41.840><c> actual</c><00:01:42.119><c> Vector</c><00:01:42.399><c> database</c><00:01:42.840><c> set</c><00:01:43.200><c> okay</c>

00:01:43.389 --> 00:01:43.399 align:start position:0%
save the actual Vector database set okay
 

00:01:43.399 --> 00:01:44.709 align:start position:0%
save the actual Vector database set okay
there<00:01:43.479><c> are</c><00:01:43.640><c> a</c><00:01:43.720><c> lot</c><00:01:43.880><c> of</c><00:01:44.000><c> different</c><00:01:44.240><c> embeddings</c>

00:01:44.709 --> 00:01:44.719 align:start position:0%
there are a lot of different embeddings
 

00:01:44.719 --> 00:01:46.069 align:start position:0%
there are a lot of different embeddings
you<00:01:44.799><c> can</c><00:01:44.920><c> even</c><00:01:45.119><c> use</c><00:01:45.320><c> like</c><00:01:45.520><c> hugging</c><00:01:45.880><c> face</c>

00:01:46.069 --> 00:01:46.079 align:start position:0%
you can even use like hugging face
 

00:01:46.079 --> 00:01:47.789 align:start position:0%
you can even use like hugging face
embeddings<00:01:46.719><c> you</c><00:01:46.840><c> don't</c><00:01:47.000><c> have</c><00:01:47.079><c> to</c><00:01:47.200><c> use</c><00:01:47.360><c> open</c><00:01:47.560><c> AI</c>

00:01:47.789 --> 00:01:47.799 align:start position:0%
embeddings you don't have to use open AI
 

00:01:47.799 --> 00:01:49.310 align:start position:0%
embeddings you don't have to use open AI
embeddings<00:01:48.240><c> there</c><00:01:48.320><c> are</c><00:01:48.479><c> numerous</c><00:01:48.840><c> choices</c>

00:01:49.310 --> 00:01:49.320 align:start position:0%
embeddings there are numerous choices
 

00:01:49.320 --> 00:01:50.469 align:start position:0%
embeddings there are numerous choices
but<00:01:49.439><c> the</c><00:01:49.560><c> embeddings</c><00:01:49.960><c> are</c><00:01:50.119><c> basically</c><00:01:50.360><c> going</c>

00:01:50.469 --> 00:01:50.479 align:start position:0%
but the embeddings are basically going
 

00:01:50.479 --> 00:01:52.749 align:start position:0%
but the embeddings are basically going
to<00:01:50.600><c> store</c><00:01:51.439><c> uh</c><00:01:51.759><c> information</c><00:01:52.240><c> like</c><00:01:52.399><c> from</c><00:01:52.560><c> the</c>

00:01:52.749 --> 00:01:52.759 align:start position:0%
to store uh information like from the
 

00:01:52.759 --> 00:01:56.270 align:start position:0%
to store uh information like from the
PDF<00:01:53.479><c> into</c><00:01:54.200><c> values</c><00:01:54.840><c> for</c><00:01:55.119><c> the</c><00:01:55.280><c> database</c><00:01:56.039><c> because</c>

00:01:56.270 --> 00:01:56.280 align:start position:0%
PDF into values for the database because
 

00:01:56.280 --> 00:01:57.749 align:start position:0%
PDF into values for the database because
whenever<00:01:56.600><c> you</c><00:01:56.799><c> store</c><00:01:57.159><c> into</c><00:01:57.360><c> a</c><00:01:57.479><c> vector</c>

00:01:57.749 --> 00:01:57.759 align:start position:0%
whenever you store into a vector
 

00:01:57.759 --> 00:01:59.950 align:start position:0%
whenever you store into a vector
database<00:01:58.240><c> you</c><00:01:58.360><c> store</c><00:01:58.799><c> vectors</c><00:01:59.520><c> right</c><00:01:59.640><c> which</c>

00:01:59.950 --> 00:01:59.960 align:start position:0%
database you store vectors right which
 

00:01:59.960 --> 00:02:02.149 align:start position:0%
database you store vectors right which
are<00:02:00.320><c> just</c><00:02:00.560><c> like</c><00:02:00.840><c> decimal</c><00:02:01.240><c> numbers</c><00:02:01.880><c> and</c><00:02:02.039><c> we</c>

00:02:02.149 --> 00:02:02.159 align:start position:0%
are just like decimal numbers and we
 

00:02:02.159 --> 00:02:04.630 align:start position:0%
are just like decimal numbers and we
only<00:02:02.399><c> want</c><00:02:02.600><c> to</c><00:02:03.039><c> save</c><00:02:03.600><c> the</c><00:02:03.719><c> vector</c><00:02:04.039><c> database</c>

00:02:04.630 --> 00:02:04.640 align:start position:0%
only want to save the vector database
 

00:02:04.640 --> 00:02:07.149 align:start position:0%
only want to save the vector database
once<00:02:05.119><c> right</c><00:02:05.280><c> we</c><00:02:05.360><c> don't</c><00:02:05.680><c> want</c><00:02:05.880><c> to</c><00:02:06.280><c> try</c><00:02:06.719><c> and</c>

00:02:07.149 --> 00:02:07.159 align:start position:0%
once right we don't want to try and
 

00:02:07.159 --> 00:02:08.910 align:start position:0%
once right we don't want to try and
resave<00:02:07.719><c> the</c><00:02:07.920><c> same</c><00:02:08.119><c> thing</c><00:02:08.280><c> over</c><00:02:08.479><c> and</c><00:02:08.640><c> over</c>

00:02:08.910 --> 00:02:08.920 align:start position:0%
resave the same thing over and over
 

00:02:08.920 --> 00:02:12.750 align:start position:0%
resave the same thing over and over
again<00:02:09.599><c> so</c><00:02:10.000><c> if</c><00:02:10.520><c> it</c><00:02:10.640><c> doesn't</c><00:02:11.200><c> exist</c><00:02:12.080><c> if</c><00:02:12.200><c> so</c><00:02:12.520><c> if</c>

00:02:12.750 --> 00:02:12.760 align:start position:0%
again so if it doesn't exist if so if
 

00:02:12.760 --> 00:02:15.430 align:start position:0%
again so if it doesn't exist if so if
the<00:02:13.160><c> path</c><00:02:13.560><c> the</c><00:02:13.640><c> folder</c><00:02:14.000><c> path</c><00:02:14.239><c> for</c><00:02:14.480><c> the</c><00:02:14.959><c> f</c><00:02:15.280><c> is</c>

00:02:15.430 --> 00:02:15.440 align:start position:0%
the path the folder path for the f is
 

00:02:15.440 --> 00:02:17.309 align:start position:0%
the path the folder path for the f is
Vector<00:02:15.680><c> database</c><00:02:16.239><c> or</c><00:02:16.400><c> phase</c><00:02:16.720><c> Vector</c><00:02:16.920><c> database</c>

00:02:17.309 --> 00:02:17.319 align:start position:0%
Vector database or phase Vector database
 

00:02:17.319 --> 00:02:18.830 align:start position:0%
Vector database or phase Vector database
doesn't<00:02:17.599><c> exist</c><00:02:18.040><c> it's</c><00:02:18.200><c> going</c><00:02:18.280><c> to</c><00:02:18.440><c> create</c><00:02:18.640><c> it</c>

00:02:18.830 --> 00:02:18.840 align:start position:0%
doesn't exist it's going to create it
 

00:02:18.840 --> 00:02:21.350 align:start position:0%
doesn't exist it's going to create it
over<00:02:19.120><c> here</c><00:02:19.480><c> otherwise</c><00:02:20.040><c> if</c><00:02:20.160><c> it</c><00:02:20.360><c> does</c><00:02:20.640><c> exist</c>

00:02:21.350 --> 00:02:21.360 align:start position:0%
over here otherwise if it does exist
 

00:02:21.360 --> 00:02:23.270 align:start position:0%
over here otherwise if it does exist
then<00:02:21.480><c> it's</c><00:02:21.599><c> going</c><00:02:21.720><c> to</c><00:02:21.959><c> load</c><00:02:22.800><c> the</c><00:02:23.000><c> vector</c>

00:02:23.270 --> 00:02:23.280 align:start position:0%
then it's going to load the vector
 

00:02:23.280 --> 00:02:25.390 align:start position:0%
then it's going to load the vector
database<00:02:23.920><c> so</c><00:02:24.040><c> we</c><00:02:24.160><c> have</c><00:02:24.280><c> the</c><00:02:24.519><c> path</c><00:02:24.720><c> of</c><00:02:24.800><c> the</c><00:02:24.920><c> PDF</c>

00:02:25.390 --> 00:02:25.400 align:start position:0%
database so we have the path of the PDF
 

00:02:25.400 --> 00:02:28.030 align:start position:0%
database so we have the path of the PDF
the<00:02:25.519><c> loader</c><00:02:25.840><c> is</c><00:02:26.000><c> equal</c><00:02:26.239><c> to</c><00:02:26.440><c> the</c><00:02:26.840><c> pi</c><00:02:27.200><c> mu</c><00:02:27.680><c> PDF</c>

00:02:28.030 --> 00:02:28.040 align:start position:0%
the loader is equal to the pi mu PDF
 

00:02:28.040 --> 00:02:29.589 align:start position:0%
the loader is equal to the pi mu PDF
loader<00:02:28.440><c> we're</c><00:02:28.560><c> going</c><00:02:28.680><c> to</c><00:02:28.800><c> load</c><00:02:29.080><c> the</c><00:02:29.239><c> documents</c>

00:02:29.589 --> 00:02:29.599 align:start position:0%
loader we're going to load the documents
 

00:02:29.599 --> 00:02:31.070 align:start position:0%
loader we're going to load the documents
into<00:02:29.959><c> the</c><00:02:30.040><c> documents</c><00:02:30.440><c> variable</c><00:02:30.840><c> we're</c><00:02:30.959><c> going</c>

00:02:31.070 --> 00:02:31.080 align:start position:0%
into the documents variable we're going
 

00:02:31.080 --> 00:02:33.949 align:start position:0%
into the documents variable we're going
to<00:02:31.239><c> recursively</c><00:02:32.040><c> split</c><00:02:32.680><c> the</c><00:02:32.879><c> text</c><00:02:33.239><c> into</c><00:02:33.560><c> chunk</c>

00:02:33.949 --> 00:02:33.959 align:start position:0%
to recursively split the text into chunk
 

00:02:33.959 --> 00:02:35.550 align:start position:0%
to recursively split the text into chunk
sizes<00:02:34.239><c> and</c><00:02:34.400><c> overlaps</c><00:02:35.160><c> then</c><00:02:35.239><c> we're</c><00:02:35.400><c> actually</c>

00:02:35.550 --> 00:02:35.560 align:start position:0%
sizes and overlaps then we're actually
 

00:02:35.560 --> 00:02:37.390 align:start position:0%
sizes and overlaps then we're actually
going<00:02:35.640><c> to</c><00:02:35.840><c> split</c><00:02:36.319><c> the</c><00:02:36.519><c> documents</c><00:02:37.000><c> right</c><00:02:37.120><c> and</c><00:02:37.239><c> I</c>

00:02:37.390 --> 00:02:37.400 align:start position:0%
going to split the documents right and I
 

00:02:37.400 --> 00:02:39.229 align:start position:0%
going to split the documents right and I
and<00:02:37.519><c> I'll</c><00:02:37.680><c> show</c><00:02:37.959><c> you</c><00:02:38.480><c> um</c><00:02:38.640><c> what</c><00:02:38.800><c> this</c><00:02:38.920><c> is</c><00:02:39.120><c> right</c>

00:02:39.229 --> 00:02:39.239 align:start position:0%
and I'll show you um what this is right
 

00:02:39.239 --> 00:02:40.390 align:start position:0%
and I'll show you um what this is right
so<00:02:39.360><c> we</c><00:02:39.440><c> have</c><00:02:39.560><c> the</c><00:02:39.680><c> documents</c><00:02:40.080><c> and</c><00:02:40.200><c> when</c><00:02:40.280><c> we</c>

00:02:40.390 --> 00:02:40.400 align:start position:0%
so we have the documents and when we
 

00:02:40.400 --> 00:02:41.750 align:start position:0%
so we have the documents and when we
split<00:02:40.720><c> them</c><00:02:41.000><c> they're</c><00:02:41.200><c> going</c><00:02:41.319><c> to</c><00:02:41.560><c> there's</c>

00:02:41.750 --> 00:02:41.760 align:start position:0%
split them they're going to there's
 

00:02:41.760 --> 00:02:43.470 align:start position:0%
split them they're going to there's
going<00:02:41.840><c> to</c><00:02:41.920><c> be</c><00:02:42.040><c> a</c><00:02:42.159><c> lot</c><00:02:42.440><c> more</c><00:02:42.920><c> documents</c><00:02:43.360><c> that</c>

00:02:43.470 --> 00:02:43.480 align:start position:0%
going to be a lot more documents that
 

00:02:43.480 --> 00:02:44.790 align:start position:0%
going to be a lot more documents that
we're<00:02:43.599><c> going</c><00:02:43.720><c> to</c><00:02:43.840><c> store</c><00:02:44.319><c> then</c><00:02:44.440><c> we're</c><00:02:44.519><c> going</c><00:02:44.640><c> to</c>

00:02:44.790 --> 00:02:44.800 align:start position:0%
we're going to store then we're going to
 

00:02:44.800 --> 00:02:46.229 align:start position:0%
we're going to store then we're going to
create<00:02:45.040><c> a</c><00:02:45.239><c> vector</c><00:02:45.480><c> store</c><00:02:45.920><c> we're</c><00:02:46.040><c> going</c><00:02:46.120><c> to</c>

00:02:46.229 --> 00:02:46.239 align:start position:0%
create a vector store we're going to
 

00:02:46.239 --> 00:02:48.309 align:start position:0%
create a vector store we're going to
give<00:02:46.360><c> it</c><00:02:46.440><c> the</c><00:02:46.640><c> documents</c><00:02:47.200><c> and</c><00:02:47.319><c> the</c><00:02:47.440><c> embeddings</c>

00:02:48.309 --> 00:02:48.319 align:start position:0%
give it the documents and the embeddings
 

00:02:48.319 --> 00:02:49.990 align:start position:0%
give it the documents and the embeddings
and<00:02:48.440><c> then</c><00:02:48.519><c> we're</c><00:02:48.720><c> finally</c><00:02:49.000><c> going</c><00:02:49.120><c> to</c><00:02:49.440><c> save</c><00:02:49.879><c> the</c>

00:02:49.990 --> 00:02:50.000 align:start position:0%
and then we're finally going to save the
 

00:02:50.000 --> 00:02:51.229 align:start position:0%
and then we're finally going to save the
vector<00:02:50.280><c> store</c><00:02:50.519><c> locally</c><00:02:50.959><c> this</c><00:02:51.040><c> is</c><00:02:51.120><c> what's</c>

00:02:51.229 --> 00:02:51.239 align:start position:0%
vector store locally this is what's
 

00:02:51.239 --> 00:02:53.149 align:start position:0%
vector store locally this is what's
going<00:02:51.360><c> to</c><00:02:51.480><c> happen</c><00:02:51.879><c> if</c><00:02:52.200><c> the</c><00:02:52.519><c> vector</c><00:02:52.800><c> database</c>

00:02:53.149 --> 00:02:53.159 align:start position:0%
going to happen if the vector database
 

00:02:53.159 --> 00:02:54.869 align:start position:0%
going to happen if the vector database
doesn't<00:02:53.400><c> already</c><00:02:53.760><c> exist</c><00:02:54.239><c> in</c><00:02:54.400><c> your</c><00:02:54.560><c> local</c>

00:02:54.869 --> 00:02:54.879 align:start position:0%
doesn't already exist in your local
 

00:02:54.879 --> 00:02:56.390 align:start position:0%
doesn't already exist in your local
directory<00:02:55.480><c> however</c><00:02:55.680><c> if</c><00:02:55.760><c> it</c><00:02:55.920><c> does</c><00:02:56.159><c> we're</c><00:02:56.319><c> going</c>

00:02:56.390 --> 00:02:56.400 align:start position:0%
directory however if it does we're going
 

00:02:56.400 --> 00:02:57.790 align:start position:0%
directory however if it does we're going
to<00:02:56.480><c> load</c><00:02:56.680><c> the</c><00:02:56.800><c> vector</c><00:02:57.080><c> database</c><00:02:57.599><c> from</c><00:02:57.720><c> the</c>

00:02:57.790 --> 00:02:57.800 align:start position:0%
to load the vector database from the
 

00:02:57.800 --> 00:02:59.110 align:start position:0%
to load the vector database from the
same<00:02:58.000><c> folder</c><00:02:58.360><c> path</c><00:02:58.599><c> and</c><00:02:58.680><c> then</c><00:02:58.800><c> we</c><00:02:58.879><c> have</c><00:02:59.000><c> a</c>

00:02:59.110 --> 00:02:59.120 align:start position:0%
same folder path and then we have a
 

00:02:59.120 --> 00:03:01.509 align:start position:0%
same folder path and then we have a
retrieval<00:02:59.840><c> question</c><00:03:00.040><c> and</c><00:03:00.280><c> answer</c><00:03:01.000><c> where</c><00:03:01.239><c> the</c>

00:03:01.509 --> 00:03:01.519 align:start position:0%
retrieval question and answer where the
 

00:03:01.519 --> 00:03:04.430 align:start position:0%
retrieval question and answer where the
we<00:03:01.599><c> set</c><00:03:01.760><c> the</c><00:03:01.840><c> llm</c><00:03:02.239><c> to</c><00:03:02.440><c> open</c><00:03:02.760><c> AI</c><00:03:03.640><c> the</c><00:03:03.840><c> retriever</c>

00:03:04.430 --> 00:03:04.440 align:start position:0%
we set the llm to open AI the retriever
 

00:03:04.440 --> 00:03:06.390 align:start position:0%
we set the llm to open AI the retriever
is<00:03:04.720><c> you</c><00:03:04.840><c> know</c><00:03:05.080><c> this</c><00:03:05.200><c> is</c><00:03:05.560><c> uh</c><00:03:05.680><c> the</c><00:03:05.799><c> retriever</c><00:03:06.200><c> for</c>

00:03:06.390 --> 00:03:06.400 align:start position:0%
is you know this is uh the retriever for
 

00:03:06.400 --> 00:03:07.910 align:start position:0%
is you know this is uh the retriever for
the<00:03:06.560><c> actual</c><00:03:06.920><c> actually</c><00:03:07.159><c> getting</c><00:03:07.400><c> the</c><00:03:07.519><c> vector</c>

00:03:07.910 --> 00:03:07.920 align:start position:0%
the actual actually getting the vector
 

00:03:07.920 --> 00:03:10.470 align:start position:0%
the actual actually getting the vector
store<00:03:08.400><c> then</c><00:03:08.519><c> we</c><00:03:08.680><c> essentially</c><00:03:09.440><c> call</c><00:03:09.879><c> invoke</c><00:03:10.400><c> if</c>

00:03:10.470 --> 00:03:10.480 align:start position:0%
store then we essentially call invoke if
 

00:03:10.480 --> 00:03:12.229 align:start position:0%
store then we essentially call invoke if
you're<00:03:10.640><c> using</c><00:03:10.920><c> an</c><00:03:11.159><c> older</c><00:03:11.599><c> version</c><00:03:11.959><c> you</c><00:03:12.080><c> might</c>

00:03:12.229 --> 00:03:12.239 align:start position:0%
you're using an older version you might
 

00:03:12.239 --> 00:03:15.110 align:start position:0%
you're using an older version you might
say<00:03:12.480><c> qa.</c><00:03:13.239><c> call</c><00:03:14.040><c> um</c><00:03:14.200><c> which</c><00:03:14.519><c> then</c><00:03:14.640><c> if</c><00:03:14.720><c> you</c><00:03:14.840><c> try</c><00:03:15.000><c> to</c>

00:03:15.110 --> 00:03:15.120 align:start position:0%
say qa. call um which then if you try to
 

00:03:15.120 --> 00:03:17.149 align:start position:0%
say qa. call um which then if you try to
run<00:03:15.319><c> it</c><00:03:15.440><c> it'll</c><00:03:15.640><c> say</c><00:03:15.879><c> that</c><00:03:16.080><c> this</c><00:03:16.159><c> is</c><00:03:16.400><c> deprecated</c>

00:03:17.149 --> 00:03:17.159 align:start position:0%
run it it'll say that this is deprecated
 

00:03:17.159 --> 00:03:19.430 align:start position:0%
run it it'll say that this is deprecated
and<00:03:17.280><c> I</c><00:03:17.400><c> think</c><00:03:18.040><c> um</c><00:03:18.200><c> if</c><00:03:18.319><c> it's</c><00:03:18.480><c> not</c><00:03:18.720><c> already</c>

00:03:19.430 --> 00:03:19.440 align:start position:0%
and I think um if it's not already
 

00:03:19.440 --> 00:03:20.710 align:start position:0%
and I think um if it's not already
they're<00:03:19.640><c> going</c><00:03:19.760><c> to</c><00:03:20.040><c> completely</c><00:03:20.360><c> switch</c><00:03:20.599><c> to</c>

00:03:20.710 --> 00:03:20.720 align:start position:0%
they're going to completely switch to
 

00:03:20.720 --> 00:03:22.670 align:start position:0%
they're going to completely switch to
invoke<00:03:21.040><c> and</c><00:03:21.159><c> the</c><00:03:21.280><c> call</c><00:03:21.560><c> might</c><00:03:21.720><c> not</c><00:03:21.959><c> work</c><00:03:22.560><c> um</c>

00:03:22.670 --> 00:03:22.680 align:start position:0%
invoke and the call might not work um
 

00:03:22.680 --> 00:03:23.750 align:start position:0%
invoke and the call might not work um
but<00:03:22.799><c> this</c><00:03:22.879><c> is</c><00:03:23.000><c> the</c><00:03:23.120><c> question</c><00:03:23.280><c> we</c><00:03:23.400><c> want</c><00:03:23.480><c> to</c><00:03:23.560><c> ask</c>

00:03:23.750 --> 00:03:23.760 align:start position:0%
but this is the question we want to ask
 

00:03:23.760 --> 00:03:24.990 align:start position:0%
but this is the question we want to ask
right<00:03:23.959><c> just</c><00:03:24.239><c> we</c><00:03:24.360><c> just</c><00:03:24.440><c> want</c><00:03:24.599><c> to</c><00:03:24.680><c> ask</c><00:03:24.799><c> it</c><00:03:24.920><c> to</c>

00:03:24.990 --> 00:03:25.000 align:start position:0%
right just we just want to ask it to
 

00:03:25.000 --> 00:03:26.869 align:start position:0%
right just we just want to ask it to
sumarize<00:03:25.319><c> juliia</c><00:03:25.599><c> Caesar</c><00:03:26.239><c> but</c><00:03:26.519><c> we</c><00:03:26.640><c> did</c><00:03:26.760><c> that</c>

00:03:26.869 --> 00:03:26.879 align:start position:0%
sumarize juliia Caesar but we did that
 

00:03:26.879 --> 00:03:28.270 align:start position:0%
sumarize juliia Caesar but we did that
an<00:03:27.040><c> example</c><00:03:27.440><c> let's</c><00:03:27.640><c> ask</c><00:03:27.799><c> it</c><00:03:27.920><c> a</c><00:03:28.040><c> different</c>

00:03:28.270 --> 00:03:28.280 align:start position:0%
an example let's ask it a different
 

00:03:28.280 --> 00:03:29.550 align:start position:0%
an example let's ask it a different
question<00:03:28.560><c> so</c><00:03:28.720><c> just</c><00:03:28.879><c> give</c><00:03:28.959><c> me</c><00:03:29.080><c> a</c><00:03:29.239><c> fact</c><00:03:29.439><c> from</c>

00:03:29.550 --> 00:03:29.560 align:start position:0%
question so just give me a fact from
 

00:03:29.560 --> 00:03:31.869 align:start position:0%
question so just give me a fact from
this<00:03:29.840><c> PDF</c><00:03:30.519><c> okay</c><00:03:31.080><c> I'm</c><00:03:31.159><c> going</c><00:03:31.239><c> to</c><00:03:31.360><c> go</c><00:03:31.480><c> ahead</c><00:03:31.680><c> and</c>

00:03:31.869 --> 00:03:31.879 align:start position:0%
this PDF okay I'm going to go ahead and
 

00:03:31.879 --> 00:03:33.509 align:start position:0%
this PDF okay I'm going to go ahead and
delete<00:03:32.319><c> the</c><00:03:32.439><c> database</c><00:03:32.840><c> that</c><00:03:32.959><c> we</c><00:03:33.080><c> have</c><00:03:33.280><c> there</c>

00:03:33.509 --> 00:03:33.519 align:start position:0%
delete the database that we have there
 

00:03:33.519 --> 00:03:34.990 align:start position:0%
delete the database that we have there
I'm<00:03:33.640><c> going</c><00:03:33.799><c> to</c><00:03:33.959><c> debug</c><00:03:34.400><c> this</c><00:03:34.519><c> so</c><00:03:34.640><c> that</c><00:03:34.760><c> you</c><00:03:34.879><c> can</c>

00:03:34.990 --> 00:03:35.000 align:start position:0%
I'm going to debug this so that you can
 

00:03:35.000 --> 00:03:36.750 align:start position:0%
I'm going to debug this so that you can
see<00:03:35.239><c> the</c><00:03:35.480><c> documents</c><00:03:36.000><c> right</c><00:03:36.120><c> so</c><00:03:36.519><c> we're</c><00:03:36.640><c> going</c>

00:03:36.750 --> 00:03:36.760 align:start position:0%
see the documents right so we're going
 

00:03:36.760 --> 00:03:38.630 align:start position:0%
see the documents right so we're going
to<00:03:36.840><c> load</c><00:03:37.000><c> the</c><00:03:37.120><c> documents</c><00:03:37.519><c> here</c><00:03:37.959><c> okay</c><00:03:38.080><c> so</c><00:03:38.560><c> we</c>

00:03:38.630 --> 00:03:38.640 align:start position:0%
to load the documents here okay so we
 

00:03:38.640 --> 00:03:40.750 align:start position:0%
to load the documents here okay so we
are<00:03:38.879><c> right</c><00:03:39.040><c> here</c><00:03:39.239><c> at</c><00:03:39.360><c> the</c><00:03:39.560><c> text</c><00:03:39.840><c> splitter</c>

00:03:40.750 --> 00:03:40.760 align:start position:0%
are right here at the text splitter
 

00:03:40.760 --> 00:03:41.910 align:start position:0%
are right here at the text splitter
right<00:03:40.920><c> so</c><00:03:41.120><c> we</c><00:03:41.200><c> want</c><00:03:41.319><c> to</c><00:03:41.400><c> look</c><00:03:41.519><c> for</c><00:03:41.680><c> this</c>

00:03:41.910 --> 00:03:41.920 align:start position:0%
right so we want to look for this
 

00:03:41.920 --> 00:03:44.750 align:start position:0%
right so we want to look for this
documents<00:03:42.760><c> uh</c><00:03:43.080><c> variable</c><00:03:44.080><c> so</c><00:03:44.239><c> you</c><00:03:44.360><c> can</c><00:03:44.480><c> see</c>

00:03:44.750 --> 00:03:44.760 align:start position:0%
documents uh variable so you can see
 

00:03:44.760 --> 00:03:47.750 align:start position:0%
documents uh variable so you can see
here<00:03:45.200><c> there</c><00:03:45.400><c> are</c><00:03:45.799><c> 114</c><00:03:46.799><c> different</c><00:03:47.239><c> documents</c>

00:03:47.750 --> 00:03:47.760 align:start position:0%
here there are 114 different documents
 

00:03:47.760 --> 00:03:49.869 align:start position:0%
here there are 114 different documents
right<00:03:47.879><c> there's</c><00:03:48.040><c> only</c><00:03:48.200><c> one</c><00:03:48.400><c> PDF</c><00:03:49.120><c> right</c><00:03:49.480><c> but</c><00:03:49.640><c> our</c>

00:03:49.869 --> 00:03:49.879 align:start position:0%
right there's only one PDF right but our
 

00:03:49.879 --> 00:03:52.350 align:start position:0%
right there's only one PDF right but our
documents<00:03:50.720><c> are</c><00:03:50.920><c> each</c><00:03:51.120><c> like</c><00:03:51.280><c> split</c><00:03:51.680><c> up</c><00:03:52.040><c> from</c>

00:03:52.350 --> 00:03:52.360 align:start position:0%
documents are each like split up from
 

00:03:52.360 --> 00:03:54.509 align:start position:0%
documents are each like split up from
that<00:03:52.560><c> PDF</c><00:03:53.239><c> right</c><00:03:53.400><c> so</c><00:03:53.640><c> then</c><00:03:54.120><c> like</c><00:03:54.280><c> here's</c>

00:03:54.509 --> 00:03:54.519 align:start position:0%
that PDF right so then like here's
 

00:03:54.519 --> 00:03:58.069 align:start position:0%
that PDF right so then like here's
something<00:03:54.799><c> about</c><00:03:55.360><c> uh</c><00:03:55.560><c> chapter</c><00:03:55.920><c> 3</c><00:03:56.319><c> so</c><00:03:56.799><c> all</c><00:03:57.120><c> of</c>

00:03:58.069 --> 00:03:58.079 align:start position:0%
something about uh chapter 3 so all of
 

00:03:58.079 --> 00:03:59.830 align:start position:0%
something about uh chapter 3 so all of
the<00:03:58.280><c> different</c><00:03:58.599><c> pages</c><00:03:59.040><c> inside</c><00:03:59.360><c> of</c><00:03:59.519><c> the</c>

00:03:59.830 --> 00:03:59.840 align:start position:0%
the different pages inside of the
 

00:03:59.840 --> 00:04:01.710 align:start position:0%
the different pages inside of the
document<00:04:00.159><c> are</c><00:04:00.360><c> split</c><00:04:00.959><c> inside</c><00:04:01.200><c> the</c><00:04:01.280><c> PDF</c><00:04:01.599><c> are</c>

00:04:01.710 --> 00:04:01.720 align:start position:0%
document are split inside the PDF are
 

00:04:01.720 --> 00:04:03.270 align:start position:0%
document are split inside the PDF are
split<00:04:02.000><c> up</c><00:04:02.120><c> into</c><00:04:02.400><c> documents</c><00:04:02.799><c> here</c><00:04:03.000><c> let's</c><00:04:03.120><c> move</c>

00:04:03.270 --> 00:04:03.280 align:start position:0%
split up into documents here let's move
 

00:04:03.280 --> 00:04:05.630 align:start position:0%
split up into documents here let's move
on<00:04:03.400><c> to</c><00:04:03.519><c> the</c><00:04:03.640><c> next</c><00:04:03.840><c> one</c><00:04:04.280><c> after</c><00:04:04.680><c> it</c><00:04:04.920><c> splits</c><00:04:05.480><c> the</c>

00:04:05.630 --> 00:04:05.640 align:start position:0%
on to the next one after it splits the
 

00:04:05.640 --> 00:04:07.750 align:start position:0%
on to the next one after it splits the
text<00:04:06.000><c> now</c><00:04:06.120><c> you</c><00:04:06.200><c> can</c><00:04:06.319><c> see</c><00:04:06.519><c> here</c><00:04:06.720><c> the</c><00:04:06.879><c> docs</c><00:04:07.319><c> has</c>

00:04:07.750 --> 00:04:07.760 align:start position:0%
text now you can see here the docs has
 

00:04:07.760 --> 00:04:09.949 align:start position:0%
text now you can see here the docs has
more<00:04:07.920><c> now</c><00:04:08.120><c> the</c><00:04:08.239><c> original</c><00:04:08.760><c> documents</c><00:04:09.680><c> as</c><00:04:09.799><c> it</c>

00:04:09.949 --> 00:04:09.959 align:start position:0%
more now the original documents as it
 

00:04:09.959 --> 00:04:12.750 align:start position:0%
more now the original documents as it
was<00:04:10.200><c> had</c><00:04:10.400><c> 114</c><00:04:11.280><c> different</c><00:04:11.920><c> uh</c><00:04:12.079><c> documents</c><00:04:12.640><c> but</c>

00:04:12.750 --> 00:04:12.760 align:start position:0%
was had 114 different uh documents but
 

00:04:12.760 --> 00:04:15.509 align:start position:0%
was had 114 different uh documents but
now<00:04:12.920><c> there's</c><00:04:13.360><c> 169</c><00:04:14.000><c> documents</c><00:04:14.640><c> and</c><00:04:14.760><c> the</c><00:04:15.000><c> idea</c>

00:04:15.509 --> 00:04:15.519 align:start position:0%
now there's 169 documents and the idea
 

00:04:15.519 --> 00:04:18.150 align:start position:0%
now there's 169 documents and the idea
is<00:04:15.760><c> that</c><00:04:15.920><c> when</c><00:04:16.079><c> we</c><00:04:16.280><c> go</c><00:04:16.519><c> to</c><00:04:17.320><c> uh</c><00:04:17.479><c> retrieve</c><00:04:17.959><c> the</c>

00:04:18.150 --> 00:04:18.160 align:start position:0%
is that when we go to uh retrieve the
 

00:04:18.160 --> 00:04:19.990 align:start position:0%
is that when we go to uh retrieve the
vectors<00:04:18.479><c> from</c><00:04:18.639><c> the</c><00:04:18.759><c> database</c><00:04:19.720><c> that</c><00:04:19.840><c> we're</c>

00:04:19.990 --> 00:04:20.000 align:start position:0%
vectors from the database that we're
 

00:04:20.000 --> 00:04:22.110 align:start position:0%
vectors from the database that we're
going<00:04:20.079><c> to</c><00:04:20.479><c> have</c><00:04:20.759><c> more</c><00:04:21.120><c> accurate</c><00:04:21.519><c> results</c><00:04:21.919><c> from</c>

00:04:22.110 --> 00:04:22.120 align:start position:0%
going to have more accurate results from
 

00:04:22.120 --> 00:04:23.990 align:start position:0%
going to have more accurate results from
the<00:04:22.280><c> similarity</c><00:04:22.840><c> search</c><00:04:23.400><c> it'll</c><00:04:23.639><c> end</c><00:04:23.840><c> up</c>

00:04:23.990 --> 00:04:24.000 align:start position:0%
the similarity search it'll end up
 

00:04:24.000 --> 00:04:26.430 align:start position:0%
the similarity search it'll end up
creating<00:04:24.440><c> a</c><00:04:24.639><c> vector</c><00:04:24.960><c> database</c><00:04:25.479><c> over</c><00:04:25.720><c> here</c><00:04:26.199><c> oh</c>

00:04:26.430 --> 00:04:26.440 align:start position:0%
creating a vector database over here oh
 

00:04:26.440 --> 00:04:29.710 align:start position:0%
creating a vector database over here oh
look<00:04:26.600><c> I</c><00:04:26.680><c> forgot</c><00:04:27.000><c> to</c><00:04:27.880><c> well</c><00:04:28.759><c> I'm</c><00:04:29.039><c> a</c><00:04:29.199><c> scrub</c><00:04:29.520><c> I</c>

00:04:29.710 --> 00:04:29.720 align:start position:0%
look I forgot to well I'm a scrub I
 

00:04:29.720 --> 00:04:31.469 align:start position:0%
look I forgot to well I'm a scrub I
forgot<00:04:29.960><c> to</c><00:04:30.080><c> change</c><00:04:30.320><c> the</c><00:04:30.440><c> API</c><00:04:30.840><c> key</c><00:04:31.039><c> here</c><00:04:31.400><c> I'm</c>

00:04:31.469 --> 00:04:31.479 align:start position:0%
forgot to change the API key here I'm
 

00:04:31.479 --> 00:04:33.390 align:start position:0%
forgot to change the API key here I'm
going<00:04:31.600><c> to</c><00:04:31.720><c> rerun</c><00:04:32.120><c> this</c><00:04:32.400><c> with</c><00:04:32.560><c> an</c><00:04:32.800><c> act</c><00:04:33.080><c> an</c><00:04:33.240><c> an</c>

00:04:33.390 --> 00:04:33.400 align:start position:0%
going to rerun this with an act an an
 

00:04:33.400 --> 00:04:36.350 align:start position:0%
going to rerun this with an act an an
actual<00:04:33.800><c> API</c><00:04:34.240><c> key</c><00:04:34.600><c> it's</c><00:04:34.759><c> starting</c><00:04:35.520><c> it</c><00:04:35.639><c> should</c>

00:04:36.350 --> 00:04:36.360 align:start position:0%
actual API key it's starting it should
 

00:04:36.360 --> 00:04:38.830 align:start position:0%
actual API key it's starting it should
actually<00:04:36.680><c> save</c><00:04:37.160><c> the</c><00:04:37.360><c> vector</c><00:04:37.960><c> database</c><00:04:38.560><c> over</c>

00:04:38.830 --> 00:04:38.840 align:start position:0%
actually save the vector database over
 

00:04:38.840 --> 00:04:40.670 align:start position:0%
actually save the vector database over
here<00:04:39.320><c> and</c><00:04:39.400><c> then</c><00:04:39.560><c> the</c><00:04:39.680><c> query</c><00:04:40.120><c> was</c><00:04:40.240><c> to</c><00:04:40.400><c> give</c><00:04:40.520><c> me</c><00:04:40.600><c> a</c>

00:04:40.670 --> 00:04:40.680 align:start position:0%
here and then the query was to give me a
 

00:04:40.680 --> 00:04:42.909 align:start position:0%
here and then the query was to give me a
fact<00:04:41.000><c> from</c><00:04:41.120><c> this</c><00:04:41.280><c> PDF</c><00:04:41.960><c> and</c><00:04:42.080><c> the</c><00:04:42.240><c> result</c><00:04:42.720><c> and</c>

00:04:42.909 --> 00:04:42.919 align:start position:0%
fact from this PDF and the result and
 

00:04:42.919 --> 00:04:44.870 align:start position:0%
fact from this PDF and the result and
you<00:04:43.000><c> know</c><00:04:43.160><c> it</c><00:04:43.240><c> gives</c><00:04:43.400><c> me</c><00:04:43.520><c> a</c><00:04:43.639><c> result</c><00:04:44.000><c> here</c><00:04:44.680><c> um</c>

00:04:44.870 --> 00:04:44.880 align:start position:0%
you know it gives me a result here um
 

00:04:44.880 --> 00:04:46.350 align:start position:0%
you know it gives me a result here um
actually<00:04:45.160><c> I</c><00:04:45.240><c> went</c><00:04:45.400><c> with</c><00:04:45.560><c> something</c><00:04:46.039><c> different</c>

00:04:46.350 --> 00:04:46.360 align:start position:0%
actually I went with something different
 

00:04:46.360 --> 00:04:47.749 align:start position:0%
actually I went with something different
right<00:04:46.520><c> so</c><00:04:46.720><c> that</c><00:04:46.840><c> just</c><00:04:47.000><c> gave</c><00:04:47.120><c> a</c><00:04:47.240><c> summary</c><00:04:47.600><c> but</c>

00:04:47.749 --> 00:04:47.759 align:start position:0%
right so that just gave a summary but
 

00:04:47.759 --> 00:04:49.270 align:start position:0%
right so that just gave a summary but
just<00:04:47.919><c> I</c><00:04:48.000><c> wanted</c><00:04:48.199><c> to</c><00:04:48.320><c> show</c><00:04:48.560><c> you</c><00:04:49.000><c> that's</c>

00:04:49.270 --> 00:04:49.280 align:start position:0%
just I wanted to show you that's
 

00:04:49.280 --> 00:04:50.550 align:start position:0%
just I wanted to show you that's
actually<00:04:49.600><c> retrieving</c><00:04:50.080><c> something</c><00:04:50.320><c> from</c><00:04:50.440><c> the</c>

00:04:50.550 --> 00:04:50.560 align:start position:0%
actually retrieving something from the
 

00:04:50.560 --> 00:04:52.710 align:start position:0%
actually retrieving something from the
PDF<00:04:50.960><c> right</c><00:04:51.120><c> so</c><00:04:51.840><c> I</c><00:04:51.960><c> asked</c><00:04:52.120><c> it</c><00:04:52.280><c> to</c><00:04:52.400><c> give</c><00:04:52.520><c> me</c><00:04:52.600><c> a</c>

00:04:52.710 --> 00:04:52.720 align:start position:0%
PDF right so I asked it to give me a
 

00:04:52.720 --> 00:04:54.230 align:start position:0%
PDF right so I asked it to give me a
paragraph<00:04:53.120><c> directly</c><00:04:53.440><c> word</c><00:04:53.720><c> for</c><00:04:53.840><c> word</c><00:04:54.080><c> from</c>

00:04:54.230 --> 00:04:54.240 align:start position:0%
paragraph directly word for word from
 

00:04:54.240 --> 00:04:56.510 align:start position:0%
paragraph directly word for word from
this<00:04:54.400><c> PDF</c><00:04:55.160><c> from</c><00:04:55.440><c> chapter</c><00:04:55.680><c> 10</c><00:04:55.960><c> right</c><00:04:56.039><c> so</c><00:04:56.280><c> I</c><00:04:56.360><c> know</c>

00:04:56.510 --> 00:04:56.520 align:start position:0%
this PDF from chapter 10 right so I know
 

00:04:56.520 --> 00:04:58.629 align:start position:0%
this PDF from chapter 10 right so I know
this<00:04:56.680><c> P</c><00:04:56.880><c> I</c><00:04:56.960><c> looked</c><00:04:57.160><c> into</c><00:04:57.320><c> it</c><00:04:57.520><c> there</c><00:04:57.720><c> are</c><00:04:58.479><c> has</c>

00:04:58.629 --> 00:04:58.639 align:start position:0%
this P I looked into it there are has
 

00:04:58.639 --> 00:05:00.110 align:start position:0%
this P I looked into it there are has
chapters<00:04:58.960><c> in</c><00:04:59.080><c> it</c><00:04:59.240><c> so</c><00:04:59.400><c> I</c><00:04:59.680><c> wanted</c><00:04:59.840><c> to</c><00:04:59.919><c> give</c><00:05:00.000><c> me</c>

00:05:00.110 --> 00:05:00.120 align:start position:0%
chapters in it so I wanted to give me
 

00:05:00.120 --> 00:05:02.310 align:start position:0%
chapters in it so I wanted to give me
something<00:05:00.320><c> from</c><00:05:00.520><c> chapter</c><00:05:00.800><c> 10</c><00:05:01.680><c> and</c><00:05:01.800><c> it</c><00:05:02.000><c> did</c><00:05:02.199><c> it</c>

00:05:02.310 --> 00:05:02.320 align:start position:0%
something from chapter 10 and it did it
 

00:05:02.320 --> 00:05:04.390 align:start position:0%
something from chapter 10 and it did it
said<00:05:02.680><c> uh</c><00:05:02.800><c> chapter</c><00:05:03.080><c> 10</c><00:05:03.240><c> roads</c><00:05:03.600><c> Bridges</c><00:05:04.120><c> and</c>

00:05:04.390 --> 00:05:04.400 align:start position:0%
said uh chapter 10 roads Bridges and
 

00:05:04.400 --> 00:05:06.909 align:start position:0%
said uh chapter 10 roads Bridges and
aqueducts<00:05:05.199><c> aqueducts</c><00:05:05.919><c> whatever</c><00:05:06.520><c> so</c><00:05:06.720><c> if</c><00:05:06.800><c> you</c>

00:05:06.909 --> 00:05:06.919 align:start position:0%
aqueducts aqueducts whatever so if you
 

00:05:06.919 --> 00:05:08.189 align:start position:0%
aqueducts aqueducts whatever so if you
search<00:05:07.199><c> for</c><00:05:07.360><c> it</c><00:05:07.520><c> opened</c><00:05:07.759><c> it</c><00:05:07.880><c> up</c><00:05:07.960><c> and</c><00:05:08.039><c> if</c><00:05:08.120><c> you</c>

00:05:08.189 --> 00:05:08.199 align:start position:0%
search for it opened it up and if you
 

00:05:08.199 --> 00:05:10.430 align:start position:0%
search for it opened it up and if you
search<00:05:08.400><c> for</c><00:05:08.560><c> it</c><00:05:08.680><c> I</c><00:05:08.759><c> mean</c><00:05:09.039><c> here</c><00:05:09.600><c> I</c><00:05:09.680><c> mean</c><00:05:10.199><c> here</c><00:05:10.320><c> it</c>

00:05:10.430 --> 00:05:10.440 align:start position:0%
search for it I mean here I mean here it
 

00:05:10.440 --> 00:05:11.790 align:start position:0%
search for it I mean here I mean here it
is<00:05:10.600><c> right</c><00:05:10.720><c> it's</c><00:05:10.840><c> actually</c><00:05:11.160><c> TW</c><00:05:11.400><c> I</c><00:05:11.479><c> searched</c><00:05:11.720><c> for</c>

00:05:11.790 --> 00:05:11.800 align:start position:0%
is right it's actually TW I searched for
 

00:05:11.800 --> 00:05:13.230 align:start position:0%
is right it's actually TW I searched for
it<00:05:11.919><c> twice</c><00:05:12.240><c> here</c><00:05:12.479><c> it's</c><00:05:12.600><c> in</c><00:05:12.720><c> the</c><00:05:12.840><c> table</c><00:05:13.080><c> of</c>

00:05:13.230 --> 00:05:13.240 align:start position:0%
it twice here it's in the table of
 

00:05:13.240 --> 00:05:15.670 align:start position:0%
it twice here it's in the table of
contents<00:05:13.680><c> and</c><00:05:13.759><c> then</c><00:05:13.880><c> the</c><00:05:14.039><c> actual</c><00:05:14.720><c> chapter</c><00:05:15.560><c> uh</c>

00:05:15.670 --> 00:05:15.680 align:start position:0%
contents and then the actual chapter uh
 

00:05:15.680 --> 00:05:17.350 align:start position:0%
contents and then the actual chapter uh
the<00:05:15.759><c> actual</c><00:05:16.000><c> chapter</c><00:05:16.240><c> 10</c><00:05:16.479><c> that's</c><00:05:16.639><c> the</c><00:05:16.800><c> title</c>

00:05:17.350 --> 00:05:17.360 align:start position:0%
the actual chapter 10 that's the title
 

00:05:17.360 --> 00:05:19.029 align:start position:0%
the actual chapter 10 that's the title
of<00:05:17.479><c> it</c><00:05:18.080><c> okay</c><00:05:18.199><c> and</c><00:05:18.280><c> then</c><00:05:18.360><c> I</c><00:05:18.479><c> said</c><00:05:18.720><c> actually</c><00:05:18.960><c> I</c>

00:05:19.029 --> 00:05:19.039 align:start position:0%
of it okay and then I said actually I
 

00:05:19.039 --> 00:05:20.590 align:start position:0%
of it okay and then I said actually I
know<00:05:19.160><c> there's</c><00:05:19.280><c> also</c><00:05:19.440><c> a</c><00:05:19.560><c> vocabulary</c><00:05:20.080><c> in</c><00:05:20.240><c> here</c>

00:05:20.590 --> 00:05:20.600 align:start position:0%
know there's also a vocabulary in here
 

00:05:20.600 --> 00:05:22.029 align:start position:0%
know there's also a vocabulary in here
so<00:05:20.800><c> I</c><00:05:20.919><c> just</c><00:05:21.039><c> kind</c><00:05:21.120><c> of</c><00:05:21.240><c> want</c><00:05:21.319><c> to</c><00:05:21.440><c> make</c><00:05:21.560><c> sure</c><00:05:21.800><c> so</c>

00:05:22.029 --> 00:05:22.039 align:start position:0%
so I just kind of want to make sure so
 

00:05:22.039 --> 00:05:23.670 align:start position:0%
so I just kind of want to make sure so
give<00:05:22.120><c> me</c><00:05:22.240><c> a</c><00:05:22.360><c> vocabulary</c><00:05:22.880><c> definition</c><00:05:23.280><c> directly</c>

00:05:23.670 --> 00:05:23.680 align:start position:0%
give me a vocabulary definition directly
 

00:05:23.680 --> 00:05:25.230 align:start position:0%
give me a vocabulary definition directly
word<00:05:23.840><c> for</c><00:05:23.960><c> word</c><00:05:24.160><c> from</c><00:05:24.319><c> this</c><00:05:24.479><c> PDF</c><00:05:24.800><c> from</c><00:05:24.960><c> chapter</c>

00:05:25.230 --> 00:05:25.240 align:start position:0%
word for word from this PDF from chapter
 

00:05:25.240 --> 00:05:27.029 align:start position:0%
word for word from this PDF from chapter
10<00:05:25.440><c> so</c><00:05:25.600><c> it</c><00:05:25.680><c> gave</c><00:05:25.840><c> me</c><00:05:26.039><c> continent</c><00:05:26.720><c> one</c><00:05:26.880><c> of</c><00:05:26.960><c> the</c>

00:05:27.029 --> 00:05:27.039 align:start position:0%
10 so it gave me continent one of the
 

00:05:27.039 --> 00:05:28.909 align:start position:0%
10 so it gave me continent one of the
seven<00:05:27.240><c> large</c><00:05:27.520><c> land</c><00:05:27.759><c> areas</c><00:05:28.080><c> on</c><00:05:28.240><c> Earth</c><00:05:28.600><c> okay</c><00:05:28.800><c> I</c>

00:05:28.909 --> 00:05:28.919 align:start position:0%
seven large land areas on Earth okay I
 

00:05:28.919 --> 00:05:30.430 align:start position:0%
seven large land areas on Earth okay I
think<00:05:29.039><c> this</c><00:05:29.120><c> is</c><00:05:29.319><c> like</c><00:05:29.800><c> you</c><00:05:29.919><c> know</c><00:05:30.080><c> more</c><00:05:30.199><c> of</c><00:05:30.280><c> a</c>

00:05:30.430 --> 00:05:30.440 align:start position:0%
think this is like you know more of a
 

00:05:30.440 --> 00:05:33.550 align:start position:0%
think this is like you know more of a
beginner<00:05:30.919><c> ancient</c><00:05:31.199><c> Rome</c><00:05:31.520><c> PDF</c><00:05:32.160><c> uh</c><00:05:32.240><c> from</c><00:05:32.400><c> a</c><00:05:32.560><c> book</c>

00:05:33.550 --> 00:05:33.560 align:start position:0%
beginner ancient Rome PDF uh from a book
 

00:05:33.560 --> 00:05:35.390 align:start position:0%
beginner ancient Rome PDF uh from a book
so<00:05:33.759><c> if</c><00:05:33.960><c> I</c><00:05:34.120><c> if</c><00:05:34.240><c> I</c><00:05:34.319><c> want</c><00:05:34.440><c> to</c><00:05:34.560><c> open</c><00:05:34.759><c> it</c><00:05:34.919><c> up</c><00:05:35.160><c> and</c><00:05:35.280><c> I</c>

00:05:35.390 --> 00:05:35.400 align:start position:0%
so if I if I want to open it up and I
 

00:05:35.400 --> 00:05:37.110 align:start position:0%
so if I if I want to open it up and I
search<00:05:35.639><c> for</c><00:05:35.800><c> continent</c><00:05:36.319><c> now</c><00:05:36.639><c> it</c><00:05:36.759><c> is</c><00:05:36.880><c> in</c>

00:05:37.110 --> 00:05:37.120 align:start position:0%
search for continent now it is in
 

00:05:37.120 --> 00:05:41.469 align:start position:0%
search for continent now it is in
chapter<00:05:37.919><c> nine</c><00:05:38.919><c> not</c><00:05:39.160><c> 10</c><00:05:40.039><c> but</c><00:05:40.440><c> here</c><00:05:40.759><c> is</c><00:05:41.280><c> the</c>

00:05:41.469 --> 00:05:41.479 align:start position:0%
chapter nine not 10 but here is the
 

00:05:41.479 --> 00:05:43.350 align:start position:0%
chapter nine not 10 but here is the
vocabulary<00:05:42.319><c> we</c><00:05:42.479><c> got</c><00:05:42.680><c> that</c><00:05:42.840><c> working</c><00:05:43.120><c> we're</c>

00:05:43.350 --> 00:05:43.360 align:start position:0%
vocabulary we got that working we're
 

00:05:43.360 --> 00:05:45.710 align:start position:0%
vocabulary we got that working we're
actually<00:05:43.600><c> able</c><00:05:43.840><c> to</c><00:05:44.199><c> store</c><00:05:44.680><c> the</c><00:05:44.840><c> PDF</c><00:05:45.319><c> into</c><00:05:45.560><c> a</c>

00:05:45.710 --> 00:05:45.720 align:start position:0%
actually able to store the PDF into a
 

00:05:45.720 --> 00:05:48.189 align:start position:0%
actually able to store the PDF into a
vector<00:05:46.000><c> database</c><00:05:46.639><c> which</c><00:05:47.080><c> might</c><00:05:47.240><c> be</c><00:05:47.360><c> new</c><00:05:48.039><c> then</c>

00:05:48.189 --> 00:05:48.199 align:start position:0%
vector database which might be new then
 

00:05:48.199 --> 00:05:50.270 align:start position:0%
vector database which might be new then
we<00:05:48.319><c> could</c><00:05:48.600><c> ask</c><00:05:49.120><c> the</c><00:05:49.280><c> vector</c><00:05:49.560><c> database</c><00:05:50.039><c> a</c>

00:05:50.270 --> 00:05:50.280 align:start position:0%
we could ask the vector database a
 

00:05:50.280 --> 00:05:51.790 align:start position:0%
we could ask the vector database a
question<00:05:50.759><c> and</c><00:05:50.840><c> then</c><00:05:51.000><c> we</c><00:05:51.120><c> retrieved</c><00:05:51.600><c> a</c>

00:05:51.790 --> 00:05:51.800 align:start position:0%
question and then we retrieved a
 

00:05:51.800 --> 00:05:53.790 align:start position:0%
question and then we retrieved a
response<00:05:52.280><c> back</c><00:05:52.759><c> I</c><00:05:52.880><c> thank</c><00:05:53.039><c> you</c><00:05:53.120><c> for</c><00:05:53.319><c> watching</c>

00:05:53.790 --> 00:05:53.800 align:start position:0%
response back I thank you for watching
 

00:05:53.800 --> 00:05:55.590 align:start position:0%
response back I thank you for watching
here's<00:05:54.000><c> some</c><00:05:54.120><c> more</c><00:05:54.280><c> videos</c><00:05:54.560><c> on</c><00:05:54.759><c> autogen</c><00:05:55.440><c> again</c>

00:05:55.590 --> 00:05:55.600 align:start position:0%
here's some more videos on autogen again
 

00:05:55.600 --> 00:05:57.390 align:start position:0%
here's some more videos on autogen again
this<00:05:55.720><c> is</c><00:05:55.880><c> day</c><00:05:56.080><c> 19</c><00:05:56.639><c> I</c><00:05:56.720><c> believe</c><00:05:57.039><c> I'm</c><00:05:57.120><c> starting</c><00:05:57.319><c> to</c>

00:05:57.390 --> 00:05:57.400 align:start position:0%
this is day 19 I believe I'm starting to
 

00:05:57.400 --> 00:05:58.950 align:start position:0%
this is day 19 I believe I'm starting to
lose<00:05:57.639><c> count</c><00:05:57.960><c> thank</c><00:05:58.080><c> you</c><00:05:58.280><c> I</c><00:05:58.319><c> will</c><00:05:58.479><c> see</c><00:05:58.600><c> you</c><00:05:58.759><c> next</c>

00:05:58.950 --> 00:05:58.960 align:start position:0%
lose count thank you I will see you next
 

00:05:58.960 --> 00:06:01.240 align:start position:0%
lose count thank you I will see you next
video

