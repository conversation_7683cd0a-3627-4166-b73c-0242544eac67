WEBVTT
Kind: captions
Language: en

00:00:00.320 --> 00:00:02.790 align:start position:0%
 
okay<00:00:00.760><c> Google</c><00:00:01.120><c> has</c><00:00:01.280><c> released</c><00:00:01.760><c> Gemini</c>

00:00:02.790 --> 00:00:02.800 align:start position:0%
okay Google has released Gemini
 

00:00:02.800 --> 00:00:05.030 align:start position:0%
okay Google has released Gemini
1.5<00:00:03.800><c> and</c><00:00:03.919><c> in</c><00:00:04.040><c> this</c><00:00:04.200><c> video</c><00:00:04.440><c> I'm</c><00:00:04.560><c> going</c><00:00:04.640><c> to</c><00:00:04.839><c> go</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
1.5 and in this video I'm going to go
 

00:00:05.040 --> 00:00:06.950 align:start position:0%
1.5 and in this video I'm going to go
through<00:00:05.359><c> some</c><00:00:05.480><c> of</c><00:00:05.640><c> the</c><00:00:05.960><c> changes</c><00:00:06.480><c> that</c><00:00:06.640><c> they've</c>

00:00:06.950 --> 00:00:06.960 align:start position:0%
through some of the changes that they've
 

00:00:06.960 --> 00:00:10.430 align:start position:0%
through some of the changes that they've
made<00:00:07.359><c> between</c><00:00:07.680><c> Gemini</c><00:00:08.160><c> 1.0</c><00:00:08.760><c> and</c><00:00:08.920><c> Gemini</c><00:00:09.440><c> 1.5</c>

00:00:10.430 --> 00:00:10.440 align:start position:0%
made between Gemini 1.0 and Gemini 1.5
 

00:00:10.440 --> 00:00:11.950 align:start position:0%
made between Gemini 1.0 and Gemini 1.5
but<00:00:10.599><c> more</c><00:00:10.800><c> importantly</c><00:00:11.320><c> I'm</c><00:00:11.440><c> going</c><00:00:11.559><c> to</c><00:00:11.719><c> go</c>

00:00:11.950 --> 00:00:11.960 align:start position:0%
but more importantly I'm going to go
 

00:00:11.960 --> 00:00:13.829 align:start position:0%
but more importantly I'm going to go
through<00:00:12.240><c> and</c><00:00:12.440><c> actually</c><00:00:12.759><c> show</c><00:00:13.040><c> you</c><00:00:13.320><c> using</c><00:00:13.679><c> the</c>

00:00:13.829 --> 00:00:13.839 align:start position:0%
through and actually show you using the
 

00:00:13.839 --> 00:00:17.830 align:start position:0%
through and actually show you using the
model<00:00:14.679><c> and</c><00:00:15.120><c> show</c><00:00:15.400><c> you</c><00:00:15.759><c> using</c><00:00:16.440><c> this</c><00:00:16.840><c> amazing</c><00:00:17.520><c> 1</c>

00:00:17.830 --> 00:00:17.840 align:start position:0%
model and show you using this amazing 1
 

00:00:17.840 --> 00:00:21.070 align:start position:0%
model and show you using this amazing 1
million<00:00:18.320><c> token</c><00:00:18.960><c> context</c><00:00:19.600><c> in</c><00:00:19.840><c> here</c><00:00:20.640><c> so</c><00:00:20.840><c> let's</c>

00:00:21.070 --> 00:00:21.080 align:start position:0%
million token context in here so let's
 

00:00:21.080 --> 00:00:23.550 align:start position:0%
million token context in here so let's
get<00:00:21.240><c> started</c><00:00:21.840><c> this</c><00:00:22.000><c> new</c><00:00:22.279><c> version</c><00:00:22.720><c> of</c><00:00:22.960><c> Gemini</c>

00:00:23.550 --> 00:00:23.560 align:start position:0%
get started this new version of Gemini
 

00:00:23.560 --> 00:00:25.189 align:start position:0%
get started this new version of Gemini
is<00:00:23.760><c> Gemini</c>

00:00:25.189 --> 00:00:25.199 align:start position:0%
is Gemini
 

00:00:25.199 --> 00:00:27.950 align:start position:0%
is Gemini
1.5<00:00:26.199><c> and</c><00:00:26.359><c> the</c><00:00:26.560><c> biggest</c><00:00:27.000><c> difference</c><00:00:27.439><c> here</c><00:00:27.640><c> is</c>

00:00:27.950 --> 00:00:27.960 align:start position:0%
1.5 and the biggest difference here is
 

00:00:27.960 --> 00:00:30.630 align:start position:0%
1.5 and the biggest difference here is
this<00:00:28.080><c> is</c><00:00:28.199><c> a</c><00:00:28.359><c> whole</c><00:00:28.560><c> new</c><00:00:28.960><c> updated</c><00:00:29.599><c> model</c>

00:00:30.630 --> 00:00:30.640 align:start position:0%
this is a whole new updated model
 

00:00:30.640 --> 00:00:33.030 align:start position:0%
this is a whole new updated model
that<00:00:30.880><c> actually</c><00:00:31.199><c> can</c><00:00:31.679><c> achieve</c><00:00:32.239><c> a</c><00:00:32.399><c> lot</c><00:00:32.680><c> of</c><00:00:32.880><c> the</c>

00:00:33.030 --> 00:00:33.040 align:start position:0%
that actually can achieve a lot of the
 

00:00:33.040 --> 00:00:36.470 align:start position:0%
that actually can achieve a lot of the
performance<00:00:33.640><c> of</c><00:00:33.920><c> ultra</c><00:00:34.559><c> 1.0</c><00:00:35.559><c> with</c><00:00:35.680><c> the</c><00:00:35.840><c> Gemini</c>

00:00:36.470 --> 00:00:36.480 align:start position:0%
performance of ultra 1.0 with the Gemini
 

00:00:36.480 --> 00:00:39.549 align:start position:0%
performance of ultra 1.0 with the Gemini
Pro<00:00:37.200><c> size</c><00:00:37.719><c> Etc</c><00:00:38.719><c> so</c><00:00:38.879><c> one</c><00:00:38.960><c> of</c><00:00:39.079><c> the</c><00:00:39.160><c> big</c><00:00:39.360><c> things</c>

00:00:39.549 --> 00:00:39.559 align:start position:0%
Pro size Etc so one of the big things
 

00:00:39.559 --> 00:00:42.150 align:start position:0%
Pro size Etc so one of the big things
with<00:00:39.800><c> Gemini</c><00:00:40.280><c> 1.5</c><00:00:41.200><c> is</c><00:00:41.320><c> that</c><00:00:41.480><c> they</c><00:00:41.600><c> are</c><00:00:41.800><c> now</c>

00:00:42.150 --> 00:00:42.160 align:start position:0%
with Gemini 1.5 is that they are now
 

00:00:42.160 --> 00:00:43.950 align:start position:0%
with Gemini 1.5 is that they are now
actually<00:00:42.480><c> confirming</c><00:00:43.160><c> publicly</c><00:00:43.680><c> that</c><00:00:43.840><c> this</c>

00:00:43.950 --> 00:00:43.960 align:start position:0%
actually confirming publicly that this
 

00:00:43.960 --> 00:00:46.350 align:start position:0%
actually confirming publicly that this
is<00:00:44.160><c> a</c><00:00:44.320><c> mixture</c><00:00:44.680><c> of</c><00:00:44.879><c> experts</c><00:00:45.480><c> architecture</c>

00:00:46.350 --> 00:00:46.360 align:start position:0%
is a mixture of experts architecture
 

00:00:46.360 --> 00:00:50.110 align:start position:0%
is a mixture of experts architecture
here<00:00:46.960><c> so</c><00:00:47.199><c> many</c><00:00:47.399><c> of</c><00:00:47.520><c> the</c><00:00:47.640><c> rumors</c><00:00:48.239><c> around</c><00:00:49.039><c> GPT</c><00:00:49.600><c> 4</c>

00:00:50.110 --> 00:00:50.120 align:start position:0%
here so many of the rumors around GPT 4
 

00:00:50.120 --> 00:00:51.990 align:start position:0%
here so many of the rumors around GPT 4
have<00:00:50.280><c> been</c><00:00:50.520><c> that</c><00:00:50.640><c> it's</c><00:00:50.760><c> a</c><00:00:50.879><c> mixture</c><00:00:51.199><c> of</c><00:00:51.399><c> Experts</c>

00:00:51.990 --> 00:00:52.000 align:start position:0%
have been that it's a mixture of Experts
 

00:00:52.000 --> 00:00:58.470 align:start position:0%
have been that it's a mixture of Experts
of<00:00:52.120><c> course</c><00:00:52.320><c> we</c><00:00:52.440><c> had</c><00:00:52.640><c> the</c>

00:00:58.470 --> 00:00:58.480 align:start position:0%
 
 

00:00:58.480 --> 00:01:02.069 align:start position:0%
 
mixalis<00:00:59.480><c> 1.</c><00:01:00.120><c> 5</c><00:01:00.640><c> is</c><00:01:00.960><c> a</c><00:01:01.079><c> mixture</c><00:01:01.440><c> of</c><00:01:01.640><c> experts</c>

00:01:02.069 --> 00:01:02.079 align:start position:0%
mixalis 1. 5 is a mixture of experts
 

00:01:02.079 --> 00:01:05.109 align:start position:0%
mixalis 1. 5 is a mixture of experts
model<00:01:02.440><c> as</c><00:01:02.600><c> well</c><00:01:03.399><c> the</c><00:01:03.559><c> other</c><00:01:03.879><c> big</c><00:01:04.400><c> advancement</c>

00:01:05.109 --> 00:01:05.119 align:start position:0%
model as well the other big advancement
 

00:01:05.119 --> 00:01:07.390 align:start position:0%
model as well the other big advancement
here<00:01:05.680><c> is</c><00:01:05.799><c> a</c><00:01:05.960><c> whole</c><00:01:06.119><c> new</c><00:01:06.320><c> way</c><00:01:06.479><c> of</c><00:01:06.680><c> doing</c><00:01:07.159><c> the</c>

00:01:07.390 --> 00:01:07.400 align:start position:0%
here is a whole new way of doing the
 

00:01:07.400 --> 00:01:10.149 align:start position:0%
here is a whole new way of doing the
context<00:01:07.920><c> window</c><00:01:08.400><c> so</c><00:01:08.560><c> the</c><00:01:08.720><c> basic</c><00:01:09.159><c> version</c><00:01:09.840><c> of</c>

00:01:10.149 --> 00:01:10.159 align:start position:0%
context window so the basic version of
 

00:01:10.159 --> 00:01:12.950 align:start position:0%
context window so the basic version of
uh<00:01:10.320><c> Gemini</c><00:01:10.720><c> 1.5</c><00:01:11.439><c> Pro</c><00:01:11.880><c> comes</c><00:01:12.119><c> with</c>

00:01:12.950 --> 00:01:12.960 align:start position:0%
uh Gemini 1.5 Pro comes with
 

00:01:12.960 --> 00:01:15.990 align:start position:0%
uh Gemini 1.5 Pro comes with
128,000<00:01:13.960><c> token</c><00:01:14.360><c> context</c><00:01:14.840><c> window</c><00:01:15.320><c> so</c><00:01:15.520><c> similar</c>

00:01:15.990 --> 00:01:16.000 align:start position:0%
128,000 token context window so similar
 

00:01:16.000 --> 00:01:19.070 align:start position:0%
128,000 token context window so similar
to<00:01:16.280><c> GPT</c><00:01:16.840><c> 4</c><00:01:17.479><c> but</c><00:01:17.640><c> they</c><00:01:17.840><c> also</c><00:01:18.080><c> have</c><00:01:18.200><c> a</c><00:01:18.400><c> version</c>

00:01:19.070 --> 00:01:19.080 align:start position:0%
to GPT 4 but they also have a version
 

00:01:19.080 --> 00:01:21.550 align:start position:0%
to GPT 4 but they also have a version
that<00:01:19.240><c> is</c><00:01:19.479><c> actually</c><00:01:19.799><c> a</c><00:01:20.000><c> million</c><00:01:20.520><c> tokens</c><00:01:21.280><c> for</c>

00:01:21.550 --> 00:01:21.560 align:start position:0%
that is actually a million tokens for
 

00:01:21.560 --> 00:01:23.630 align:start position:0%
that is actually a million tokens for
the<00:01:21.720><c> context</c><00:01:22.159><c> window</c><00:01:22.960><c> so</c><00:01:23.200><c> that's</c><00:01:23.360><c> what</c><00:01:23.479><c> I</c>

00:01:23.630 --> 00:01:23.640 align:start position:0%
the context window so that's what I
 

00:01:23.640 --> 00:01:25.390 align:start position:0%
the context window so that's what I
really<00:01:23.880><c> want</c><00:01:23.960><c> to</c><00:01:24.119><c> go</c><00:01:24.280><c> through</c><00:01:24.600><c> here</c><00:01:24.920><c> and</c><00:01:25.079><c> show</c>

00:01:25.390 --> 00:01:25.400 align:start position:0%
really want to go through here and show
 

00:01:25.400 --> 00:01:26.870 align:start position:0%
really want to go through here and show
you<00:01:25.840><c> so</c><00:01:26.040><c> if</c><00:01:26.119><c> we</c><00:01:26.240><c> look</c><00:01:26.360><c> at</c><00:01:26.479><c> some</c><00:01:26.560><c> of</c><00:01:26.720><c> the</c>

00:01:26.870 --> 00:01:26.880 align:start position:0%
you so if we look at some of the
 

00:01:26.880 --> 00:01:29.069 align:start position:0%
you so if we look at some of the
examples<00:01:27.560><c> of</c><00:01:27.680><c> the</c><00:01:27.880><c> context</c><00:01:28.280><c> windows</c><00:01:28.759><c> that</c><00:01:28.880><c> are</c>

00:01:29.069 --> 00:01:29.079 align:start position:0%
examples of the context windows that are
 

00:01:29.079 --> 00:01:31.190 align:start position:0%
examples of the context windows that are
around<00:01:29.520><c> there</c><00:01:30.079><c> you</c><00:01:30.240><c> can</c><00:01:30.360><c> see</c><00:01:30.680><c> we've</c><00:01:30.880><c> got</c><00:01:31.079><c> the</c>

00:01:31.190 --> 00:01:31.200 align:start position:0%
around there you can see we've got the
 

00:01:31.200 --> 00:01:34.830 align:start position:0%
around there you can see we've got the
Gemini<00:01:31.799><c> Pro</c><00:01:32.240><c> which</c><00:01:32.360><c> was</c><00:01:32.600><c> originally</c><00:01:33.159><c> 32k</c><00:01:34.159><c> gbd4</c>

00:01:34.830 --> 00:01:34.840 align:start position:0%
Gemini Pro which was originally 32k gbd4
 

00:01:34.840 --> 00:01:35.910 align:start position:0%
Gemini Pro which was originally 32k gbd4
turbo

00:01:35.910 --> 00:01:35.920 align:start position:0%
turbo
 

00:01:35.920 --> 00:01:41.350 align:start position:0%
turbo
128k<00:01:36.920><c> Claude</c><00:01:37.520><c> 2.1</c><00:01:38.520><c> 200k</c><00:01:39.520><c> and</c><00:01:39.720><c> now</c><00:01:39.960><c> Gemini</c><00:01:40.479><c> 1.5</c>

00:01:41.350 --> 00:01:41.360 align:start position:0%
128k Claude 2.1 200k and now Gemini 1.5
 

00:01:41.360 --> 00:01:44.789 align:start position:0%
128k Claude 2.1 200k and now Gemini 1.5
pro<00:01:41.720><c> at</c><00:01:41.920><c> a</c><00:01:42.040><c> million</c><00:01:42.479><c> tokens</c><00:01:43.439><c> so</c><00:01:43.720><c> this</c><00:01:43.880><c> can</c><00:01:44.079><c> do</c>

00:01:44.789 --> 00:01:44.799 align:start position:0%
pro at a million tokens so this can do
 

00:01:44.799 --> 00:01:48.069 align:start position:0%
pro at a million tokens so this can do
an<00:01:45.000><c> hour</c><00:01:45.280><c> of</c><00:01:45.520><c> video</c><00:01:46.200><c> 11</c><00:01:46.719><c> hours</c><00:01:47.040><c> of</c><00:01:47.320><c> audio</c>

00:01:48.069 --> 00:01:48.079 align:start position:0%
an hour of video 11 hours of audio
 

00:01:48.079 --> 00:01:50.389 align:start position:0%
an hour of video 11 hours of audio
30,000<00:01:48.680><c> lines</c><00:01:48.920><c> of</c><00:01:49.079><c> code</c><00:01:49.360><c> or</c><00:01:49.520><c> so</c><00:01:49.799><c> and</c><00:01:49.960><c> about</c>

00:01:50.389 --> 00:01:50.399 align:start position:0%
30,000 lines of code or so and about
 

00:01:50.399 --> 00:01:53.389 align:start position:0%
30,000 lines of code or so and about
700,000<00:01:51.399><c> words</c><00:01:52.000><c> in</c><00:01:52.200><c> here</c><00:01:52.439><c> so</c><00:01:52.600><c> it</c><00:01:52.799><c> really</c>

00:01:53.389 --> 00:01:53.399 align:start position:0%
700,000 words in here so it really
 

00:01:53.399 --> 00:01:56.069 align:start position:0%
700,000 words in here so it really
allows<00:01:53.920><c> you</c><00:01:54.079><c> to</c><00:01:54.640><c> basically</c><00:01:55.040><c> put</c><00:01:55.200><c> in</c><00:01:55.520><c> a</c><00:01:55.680><c> lot</c><00:01:55.840><c> of</c>

00:01:56.069 --> 00:01:56.079 align:start position:0%
allows you to basically put in a lot of
 

00:01:56.079 --> 00:01:58.789 align:start position:0%
allows you to basically put in a lot of
things<00:01:56.399><c> in</c><00:01:56.640><c> here</c><00:01:57.240><c> so</c><00:01:57.719><c> it's</c><00:01:57.920><c> also</c><00:01:58.200><c> good</c><00:01:58.360><c> to</c><00:01:58.520><c> see</c>

00:01:58.789 --> 00:01:58.799 align:start position:0%
things in here so it's also good to see
 

00:01:58.799 --> 00:02:00.029 align:start position:0%
things in here so it's also good to see
that<00:01:58.960><c> one</c><00:01:59.079><c> of</c><00:01:59.200><c> the</c><00:01:59.320><c> things</c><00:01:59.520><c> that</c><00:01:59.680><c> they</c><00:01:59.840><c> they've</c>

00:02:00.029 --> 00:02:00.039 align:start position:0%
that one of the things that they they've
 

00:02:00.039 --> 00:02:01.870 align:start position:0%
that one of the things that they they've
done<00:02:00.240><c> with</c><00:02:00.399><c> this</c><00:02:00.600><c> context</c><00:02:01.039><c> window</c><00:02:01.520><c> is</c>

00:02:01.870 --> 00:02:01.880 align:start position:0%
done with this context window is
 

00:02:01.880 --> 00:02:04.950 align:start position:0%
done with this context window is
actually<00:02:02.640><c> run</c><00:02:03.000><c> some</c><00:02:03.159><c> of</c><00:02:03.320><c> the</c><00:02:03.520><c> tests</c><00:02:04.399><c> that</c><00:02:04.640><c> open</c>

00:02:04.950 --> 00:02:04.960 align:start position:0%
actually run some of the tests that open
 

00:02:04.960 --> 00:02:06.870 align:start position:0%
actually run some of the tests that open
source<00:02:05.240><c> people</c><00:02:05.479><c> have</c><00:02:05.640><c> been</c><00:02:05.920><c> doing</c><00:02:06.360><c> so</c><00:02:06.560><c> Greg</c>

00:02:06.870 --> 00:02:06.880 align:start position:0%
source people have been doing so Greg
 

00:02:06.880 --> 00:02:10.070 align:start position:0%
source people have been doing so Greg
camat<00:02:07.399><c> ran</c><00:02:07.640><c> some</c><00:02:07.920><c> really</c><00:02:08.119><c> cool</c><00:02:08.440><c> tests</c><00:02:09.360><c> on</c><00:02:09.720><c> this</c>

00:02:10.070 --> 00:02:10.080 align:start position:0%
camat ran some really cool tests on this
 

00:02:10.080 --> 00:02:12.470 align:start position:0%
camat ran some really cool tests on this
idea<00:02:10.640><c> of</c><00:02:10.800><c> a</c><00:02:10.959><c> needle</c><00:02:11.239><c> in</c><00:02:11.400><c> a</c><00:02:11.560><c> haze</c><00:02:11.879><c> stack</c><00:02:12.239><c> of</c>

00:02:12.470 --> 00:02:12.480 align:start position:0%
idea of a needle in a haze stack of
 

00:02:12.480 --> 00:02:15.869 align:start position:0%
idea of a needle in a haze stack of
basically<00:02:13.200><c> could</c><00:02:13.360><c> you</c><00:02:13.560><c> find</c><00:02:14.160><c> information</c><00:02:15.160><c> in</c>

00:02:15.869 --> 00:02:15.879 align:start position:0%
basically could you find information in
 

00:02:15.879 --> 00:02:18.110 align:start position:0%
basically could you find information in
a<00:02:16.040><c> really</c><00:02:16.239><c> long</c><00:02:16.560><c> context</c><00:02:17.040><c> window</c><00:02:17.760><c> and</c><00:02:17.879><c> what</c><00:02:18.000><c> he</c>

00:02:18.110 --> 00:02:18.120 align:start position:0%
a really long context window and what he
 

00:02:18.120 --> 00:02:19.589 align:start position:0%
a really long context window and what he
found<00:02:18.400><c> was</c><00:02:18.560><c> that</c><00:02:18.680><c> for</c><00:02:18.840><c> a</c><00:02:18.920><c> lot</c><00:02:19.040><c> of</c><00:02:19.120><c> the</c><00:02:19.239><c> models</c>

00:02:19.589 --> 00:02:19.599 align:start position:0%
found was that for a lot of the models
 

00:02:19.599 --> 00:02:21.550 align:start position:0%
found was that for a lot of the models
you<00:02:19.760><c> just</c><00:02:19.920><c> couldn't</c><00:02:20.239><c> do</c><00:02:20.519><c> that</c><00:02:20.840><c> now</c><00:02:21.239><c> I</c><00:02:21.319><c> think</c>

00:02:21.550 --> 00:02:21.560 align:start position:0%
you just couldn't do that now I think
 

00:02:21.560 --> 00:02:23.630 align:start position:0%
you just couldn't do that now I think
anthropic<00:02:22.200><c> showed</c><00:02:22.560><c> some</c><00:02:22.840><c> ways</c><00:02:23.319><c> that</c><00:02:23.480><c> they</c>

00:02:23.630 --> 00:02:23.640 align:start position:0%
anthropic showed some ways that they
 

00:02:23.640 --> 00:02:26.110 align:start position:0%
anthropic showed some ways that they
could<00:02:24.120><c> find</c><00:02:24.560><c> some</c><00:02:24.680><c> of</c><00:02:24.840><c> the</c><00:02:24.959><c> things</c><00:02:25.200><c> in</c><00:02:25.440><c> here</c>

00:02:26.110 --> 00:02:26.120 align:start position:0%
could find some of the things in here
 

00:02:26.120 --> 00:02:27.550 align:start position:0%
could find some of the things in here
but<00:02:26.280><c> what</c><00:02:26.400><c> Google's</c><00:02:26.760><c> claiming</c><00:02:27.120><c> here</c><00:02:27.280><c> is</c><00:02:27.440><c> that</c>

00:02:27.550 --> 00:02:27.560 align:start position:0%
but what Google's claiming here is that
 

00:02:27.560 --> 00:02:29.869 align:start position:0%
but what Google's claiming here is that
you<00:02:27.680><c> can</c><00:02:27.879><c> actually</c><00:02:28.360><c> find</c><00:02:29.080><c> one</c><00:02:29.200><c> of</c><00:02:29.400><c> these</c>

00:02:29.869 --> 00:02:29.879 align:start position:0%
you can actually find one of these
 

00:02:29.879 --> 00:02:32.430 align:start position:0%
you can actually find one of these
things<00:02:30.360><c> in</c><00:02:30.959><c> token</c><00:02:31.319><c> lengths</c><00:02:31.760><c> of</c><00:02:31.959><c> up</c><00:02:32.120><c> to</c><00:02:32.319><c> a</c>

00:02:32.430 --> 00:02:32.440 align:start position:0%
things in token lengths of up to a
 

00:02:32.440 --> 00:02:34.350 align:start position:0%
things in token lengths of up to a
million<00:02:32.879><c> tokens</c><00:02:33.480><c> here</c><00:02:33.680><c> which</c><00:02:33.800><c> is</c><00:02:34.000><c> really</c>

00:02:34.350 --> 00:02:34.360 align:start position:0%
million tokens here which is really
 

00:02:34.360 --> 00:02:36.790 align:start position:0%
million tokens here which is really
impressive<00:02:35.360><c> so</c><00:02:35.640><c> rather</c><00:02:35.879><c> than</c><00:02:36.080><c> talk</c><00:02:36.319><c> about</c><00:02:36.599><c> the</c>

00:02:36.790 --> 00:02:36.800 align:start position:0%
impressive so rather than talk about the
 

00:02:36.800 --> 00:02:38.630 align:start position:0%
impressive so rather than talk about the
stats<00:02:37.120><c> and</c><00:02:37.280><c> stuff</c><00:02:37.519><c> like</c><00:02:37.680><c> that</c><00:02:38.200><c> why</c><00:02:38.319><c> don't</c><00:02:38.480><c> we</c>

00:02:38.630 --> 00:02:38.640 align:start position:0%
stats and stuff like that why don't we
 

00:02:38.640 --> 00:02:40.350 align:start position:0%
stats and stuff like that why don't we
actually<00:02:38.879><c> just</c><00:02:39.120><c> jump</c><00:02:39.440><c> in</c><00:02:39.760><c> and</c><00:02:39.879><c> have</c><00:02:40.000><c> a</c><00:02:40.159><c> play</c>

00:02:40.350 --> 00:02:40.360 align:start position:0%
actually just jump in and have a play
 

00:02:40.360 --> 00:02:42.350 align:start position:0%
actually just jump in and have a play
with<00:02:40.560><c> the</c><00:02:40.680><c> model</c><00:02:41.360><c> see</c><00:02:41.599><c> what</c><00:02:41.720><c> it</c><00:02:41.840><c> can</c><00:02:42.000><c> do</c><00:02:42.200><c> with</c>

00:02:42.350 --> 00:02:42.360 align:start position:0%
with the model see what it can do with
 

00:02:42.360 --> 00:02:44.070 align:start position:0%
with the model see what it can do with
some<00:02:42.599><c> video</c><00:02:43.080><c> and</c><00:02:43.200><c> see</c><00:02:43.400><c> what</c><00:02:43.480><c> it</c><00:02:43.599><c> can</c><00:02:43.760><c> do</c><00:02:43.959><c> with</c>

00:02:44.070 --> 00:02:44.080 align:start position:0%
some video and see what it can do with
 

00:02:44.080 --> 00:02:46.470 align:start position:0%
some video and see what it can do with
some<00:02:44.319><c> various</c><00:02:44.800><c> context</c><00:02:45.280><c> lengths</c><00:02:45.640><c> in</c><00:02:45.920><c> here</c><00:02:46.159><c> as</c>

00:02:46.470 --> 00:02:46.480 align:start position:0%
some various context lengths in here as
 

00:02:46.480 --> 00:02:49.990 align:start position:0%
some various context lengths in here as
well<00:02:47.480><c> okay</c><00:02:47.720><c> so</c><00:02:48.080><c> here</c><00:02:48.319><c> we've</c><00:02:48.680><c> got</c><00:02:49.440><c> the</c><00:02:49.680><c> Google</c>

00:02:49.990 --> 00:02:50.000 align:start position:0%
well okay so here we've got the Google
 

00:02:50.000 --> 00:02:52.509 align:start position:0%
well okay so here we've got the Google
AI<00:02:50.480><c> Studio</c><00:02:51.040><c> where</c><00:02:51.200><c> we</c><00:02:51.280><c> can</c><00:02:51.440><c> see</c><00:02:51.680><c> the</c><00:02:51.840><c> model</c><00:02:52.400><c> you</c>

00:02:52.509 --> 00:02:52.519 align:start position:0%
AI Studio where we can see the model you
 

00:02:52.519 --> 00:02:54.990 align:start position:0%
AI Studio where we can see the model you
can<00:02:52.640><c> see</c><00:02:52.840><c> that</c><00:02:53.000><c> I've</c><00:02:53.280><c> selected</c><00:02:53.800><c> Gemini</c><00:02:54.239><c> 1.5</c>

00:02:54.990 --> 00:02:55.000 align:start position:0%
can see that I've selected Gemini 1.5
 

00:02:55.000 --> 00:02:57.949 align:start position:0%
can see that I've selected Gemini 1.5
Pro<00:02:55.400><c> as</c><00:02:55.560><c> the</c><00:02:55.680><c> model</c><00:02:56.040><c> here</c><00:02:56.840><c> and</c><00:02:57.000><c> we</c><00:02:57.080><c> can</c><00:02:57.280><c> upload</c>

00:02:57.949 --> 00:02:57.959 align:start position:0%
Pro as the model here and we can upload
 

00:02:57.959 --> 00:03:01.869 align:start position:0%
Pro as the model here and we can upload
images<00:02:58.879><c> videos</c><00:02:59.800><c> files</c><00:03:00.560><c> a</c><00:03:00.760><c> folder</c><00:03:01.360><c> full</c><00:03:01.640><c> of</c>

00:03:01.869 --> 00:03:01.879 align:start position:0%
images videos files a folder full of
 

00:03:01.879 --> 00:03:04.670 align:start position:0%
images videos files a folder full of
different<00:03:02.319><c> files</c><00:03:02.720><c> if</c><00:03:02.840><c> we</c><00:03:03.000><c> want</c><00:03:03.200><c> to</c><00:03:03.519><c> here</c><00:03:04.360><c> okay</c>

00:03:04.670 --> 00:03:04.680 align:start position:0%
different files if we want to here okay
 

00:03:04.680 --> 00:03:07.710 align:start position:0%
different files if we want to here okay
now<00:03:04.879><c> I'm</c><00:03:05.120><c> basically</c><00:03:06.080><c> uploading</c><00:03:06.720><c> the</c><00:03:06.959><c> combined</c>

00:03:07.710 --> 00:03:07.720 align:start position:0%
now I'm basically uploading the combined
 

00:03:07.720 --> 00:03:10.149 align:start position:0%
now I'm basically uploading the combined
Lang<00:03:08.040><c> chain</c><00:03:08.280><c> docs</c><00:03:08.720><c> now</c><00:03:08.879><c> if</c><00:03:08.959><c> you</c><00:03:09.120><c> look</c><00:03:09.319><c> at</c><00:03:09.599><c> the</c>

00:03:10.149 --> 00:03:10.159 align:start position:0%
Lang chain docs now if you look at the
 

00:03:10.159 --> 00:03:12.430 align:start position:0%
Lang chain docs now if you look at the
bottom<00:03:10.599><c> here</c><00:03:11.159><c> you</c><00:03:11.280><c> will</c><00:03:11.440><c> see</c><00:03:11.680><c> that</c><00:03:11.840><c> we've</c><00:03:12.120><c> got</c>

00:03:12.430 --> 00:03:12.440 align:start position:0%
bottom here you will see that we've got
 

00:03:12.440 --> 00:03:14.030 align:start position:0%
bottom here you will see that we've got
1,

00:03:14.030 --> 00:03:14.040 align:start position:0%
1,
 

00:03:14.040 --> 00:03:18.390 align:start position:0%
1,
48,5<00:03:15.040><c> 76</c><00:03:16.000><c> tokens</c><00:03:17.000><c> that</c><00:03:17.120><c> we</c><00:03:17.239><c> can</c><00:03:17.440><c> use</c><00:03:18.200><c> and</c><00:03:18.319><c> you</c>

00:03:18.390 --> 00:03:18.400 align:start position:0%
48,5 76 tokens that we can use and you
 

00:03:18.400 --> 00:03:21.149 align:start position:0%
48,5 76 tokens that we can use and you
can<00:03:18.560><c> see</c><00:03:18.879><c> that</c><00:03:19.159><c> here</c><00:03:19.560><c> at</c><00:03:19.680><c> the</c><00:03:19.879><c> top</c><00:03:20.200><c> we've</c><00:03:20.440><c> used</c>

00:03:21.149 --> 00:03:21.159 align:start position:0%
can see that here at the top we've used
 

00:03:21.159 --> 00:03:24.830 align:start position:0%
can see that here at the top we've used
526,000<00:03:22.400><c> tokens</c><00:03:23.400><c> so</c><00:03:23.720><c> this</c><00:03:23.840><c> is</c><00:03:24.120><c> basically</c>

00:03:24.830 --> 00:03:24.840 align:start position:0%
526,000 tokens so this is basically
 

00:03:24.840 --> 00:03:28.470 align:start position:0%
526,000 tokens so this is basically
every<00:03:25.280><c> page</c><00:03:26.159><c> on</c><00:03:26.640><c> the</c><00:03:27.360><c> Lang</c><00:03:27.760><c> chain</c><00:03:28.239><c> I</c><00:03:28.319><c> think</c>

00:03:28.470 --> 00:03:28.480 align:start position:0%
every page on the Lang chain I think
 

00:03:28.480 --> 00:03:30.270 align:start position:0%
every page on the Lang chain I think
it's<00:03:28.640><c> just</c><00:03:28.799><c> the</c><00:03:29.040><c> python</c><00:03:29.760><c> docs</c><00:03:30.040><c> that</c><00:03:30.120><c> have</c>

00:03:30.270 --> 00:03:30.280 align:start position:0%
it's just the python docs that have
 

00:03:30.280 --> 00:03:32.470 align:start position:0%
it's just the python docs that have
basically<00:03:30.560><c> been</c><00:03:30.720><c> scraped</c><00:03:31.200><c> saved</c><00:03:31.599><c> down</c><00:03:32.239><c> as</c>

00:03:32.470 --> 00:03:32.480 align:start position:0%
basically been scraped saved down as
 

00:03:32.480 --> 00:03:34.309 align:start position:0%
basically been scraped saved down as
text<00:03:32.799><c> files</c><00:03:33.200><c> and</c><00:03:33.319><c> I've</c><00:03:33.480><c> just</c><00:03:33.599><c> merged</c><00:03:33.959><c> them</c><00:03:34.080><c> all</c>

00:03:34.309 --> 00:03:34.319 align:start position:0%
text files and I've just merged them all
 

00:03:34.319 --> 00:03:36.470 align:start position:0%
text files and I've just merged them all
together<00:03:34.560><c> into</c><00:03:34.840><c> one</c><00:03:35.080><c> big</c><00:03:35.280><c> text</c><00:03:35.560><c> file</c><00:03:35.799><c> in</c><00:03:36.000><c> here</c>

00:03:36.470 --> 00:03:36.480 align:start position:0%
together into one big text file in here
 

00:03:36.480 --> 00:03:38.470 align:start position:0%
together into one big text file in here
to<00:03:36.640><c> make</c><00:03:36.799><c> it</c><00:03:36.920><c> easy</c><00:03:37.519><c> now</c><00:03:37.840><c> say</c><00:03:38.040><c> that</c><00:03:38.239><c> if</c><00:03:38.360><c> you</c>

00:03:38.470 --> 00:03:38.480 align:start position:0%
to make it easy now say that if you
 

00:03:38.480 --> 00:03:40.030 align:start position:0%
to make it easy now say that if you
upload<00:03:38.799><c> something</c><00:03:39.040><c> as</c><00:03:39.159><c> a</c><00:03:39.280><c> folder</c><00:03:39.720><c> you</c><00:03:39.799><c> can</c>

00:03:40.030 --> 00:03:40.040 align:start position:0%
upload something as a folder you can
 

00:03:40.040 --> 00:03:43.869 align:start position:0%
upload something as a folder you can
only<00:03:40.519><c> upload</c><00:03:41.519><c> a</c><00:03:41.879><c> 100</c><00:03:42.319><c> files</c><00:03:42.840><c> at</c><00:03:42.920><c> a</c><00:03:43.159><c> time</c><00:03:43.640><c> so</c><00:03:43.799><c> you</c>

00:03:43.869 --> 00:03:43.879 align:start position:0%
only upload a 100 files at a time so you
 

00:03:43.879 --> 00:03:46.270 align:start position:0%
only upload a 100 files at a time so you
can<00:03:44.040><c> have</c><00:03:44.319><c> 100</c><00:03:44.599><c> files</c><00:03:45.000><c> in</c><00:03:45.239><c> here</c><00:03:45.879><c> all</c><00:03:46.000><c> right</c><00:03:46.159><c> so</c>

00:03:46.270 --> 00:03:46.280 align:start position:0%
can have 100 files in here all right so
 

00:03:46.280 --> 00:03:48.789 align:start position:0%
can have 100 files in here all right so
we've<00:03:46.519><c> got</c><00:03:46.760><c> this</c><00:03:46.879><c> in</c><00:03:47.120><c> here</c><00:03:47.760><c> let's</c><00:03:48.040><c> ask</c><00:03:48.239><c> it</c><00:03:48.439><c> a</c>

00:03:48.789 --> 00:03:48.799 align:start position:0%
we've got this in here let's ask it a
 

00:03:48.799 --> 00:03:50.910 align:start position:0%
we've got this in here let's ask it a
question<00:03:49.799><c> I'm</c><00:03:49.920><c> was</c><00:03:50.040><c> going</c><00:03:50.159><c> to</c><00:03:50.239><c> ask</c><00:03:50.400><c> it</c><00:03:50.599><c> can</c><00:03:50.720><c> you</c>

00:03:50.910 --> 00:03:50.920 align:start position:0%
question I'm was going to ask it can you
 

00:03:50.920 --> 00:03:52.949 align:start position:0%
question I'm was going to ask it can you
explain<00:03:51.920><c> what</c>

00:03:52.949 --> 00:03:52.959 align:start position:0%
explain what
 

00:03:52.959 --> 00:03:55.949 align:start position:0%
explain what
LC<00:03:53.959><c> is</c><00:03:54.319><c> and</c><00:03:54.519><c> also</c><00:03:54.760><c> what</c><00:03:54.920><c> L</c><00:03:55.239><c> graph</c><00:03:55.599><c> is</c><00:03:55.760><c> if</c><00:03:55.879><c> you</c>

00:03:55.949 --> 00:03:55.959 align:start position:0%
LC is and also what L graph is if you
 

00:03:55.959 --> 00:03:57.309 align:start position:0%
LC is and also what L graph is if you
can<00:03:56.159><c> find</c><00:03:56.360><c> it</c><00:03:56.519><c> so</c><00:03:56.640><c> I'm</c><00:03:56.720><c> not</c><00:03:56.840><c> sure</c><00:03:57.000><c> if</c><00:03:57.120><c> there's</c>

00:03:57.309 --> 00:03:57.319 align:start position:0%
can find it so I'm not sure if there's
 

00:03:57.319 --> 00:03:59.509 align:start position:0%
can find it so I'm not sure if there's
anything<00:03:57.599><c> about</c><00:03:57.760><c> L</c><00:03:58.079><c> graph</c><00:03:58.360><c> actually</c><00:03:58.640><c> in</c><00:03:58.879><c> here</c>

00:03:59.509 --> 00:03:59.519 align:start position:0%
anything about L graph actually in here
 

00:03:59.519 --> 00:04:02.229 align:start position:0%
anything about L graph actually in here
let's<00:03:59.680><c> let's</c><00:03:59.920><c> find</c><00:04:00.120><c> out</c><00:04:00.680><c> when</c><00:04:00.840><c> we're</c><00:04:01.000><c> doing</c><00:04:01.640><c> a</c>

00:04:02.229 --> 00:04:02.239 align:start position:0%
let's let's find out when we're doing a
 

00:04:02.239 --> 00:04:04.030 align:start position:0%
let's let's find out when we're doing a
inference<00:04:02.599><c> run</c><00:04:02.879><c> like</c><00:04:03.120><c> this</c><00:04:03.319><c> we're</c><00:04:03.519><c> running</c>

00:04:04.030 --> 00:04:04.040 align:start position:0%
inference run like this we're running
 

00:04:04.040 --> 00:04:06.710 align:start position:0%
inference run like this we're running
inference<00:04:04.560><c> over</c><00:04:05.079><c> half</c><00:04:05.280><c> a</c><00:04:05.400><c> million</c><00:04:05.760><c> tokens</c>

00:04:06.710 --> 00:04:06.720 align:start position:0%
inference over half a million tokens
 

00:04:06.720 --> 00:04:09.350 align:start position:0%
inference over half a million tokens
going<00:04:06.840><c> to</c><00:04:06.959><c> take</c><00:04:07.200><c> around</c><00:04:07.799><c> about</c><00:04:08.280><c> 50</c><00:04:08.640><c> to</c><00:04:08.840><c> 60</c>

00:04:09.350 --> 00:04:09.360 align:start position:0%
going to take around about 50 to 60
 

00:04:09.360 --> 00:04:11.429 align:start position:0%
going to take around about 50 to 60
seconds<00:04:09.879><c> to</c><00:04:10.079><c> go</c><00:04:10.360><c> through</c><00:04:10.640><c> and</c><00:04:10.840><c> actually</c><00:04:11.120><c> do</c>

00:04:11.429 --> 00:04:11.439 align:start position:0%
seconds to go through and actually do
 

00:04:11.439 --> 00:04:14.509 align:start position:0%
seconds to go through and actually do
this<00:04:12.079><c> so</c><00:04:12.400><c> it</c><00:04:12.599><c> can</c><00:04:13.120><c> change</c><00:04:13.480><c> a</c><00:04:13.640><c> bit</c><00:04:13.959><c> depending</c><00:04:14.319><c> on</c>

00:04:14.509 --> 00:04:14.519 align:start position:0%
this so it can change a bit depending on
 

00:04:14.519 --> 00:04:16.789 align:start position:0%
this so it can change a bit depending on
what<00:04:14.640><c> the</c><00:04:14.799><c> tokens</c><00:04:15.200><c> are</c><00:04:15.920><c> so</c><00:04:16.160><c> in</c><00:04:16.320><c> this</c><00:04:16.479><c> case</c><00:04:16.639><c> all</c>

00:04:16.789 --> 00:04:16.799 align:start position:0%
what the tokens are so in this case all
 

00:04:16.799 --> 00:04:18.110 align:start position:0%
what the tokens are so in this case all
the<00:04:16.919><c> tokens</c><00:04:17.239><c> are</c><00:04:17.440><c> text</c><00:04:17.840><c> actually</c><00:04:18.000><c> it's</c>

00:04:18.110 --> 00:04:18.120 align:start position:0%
the tokens are text actually it's
 

00:04:18.120 --> 00:04:20.229 align:start position:0%
the tokens are text actually it's
running<00:04:18.440><c> much</c><00:04:18.639><c> quicker</c><00:04:19.079><c> now</c><00:04:19.720><c> but</c><00:04:20.000><c> sometimes</c>

00:04:20.229 --> 00:04:20.239 align:start position:0%
running much quicker now but sometimes
 

00:04:20.239 --> 00:04:21.789 align:start position:0%
running much quicker now but sometimes
if<00:04:20.359><c> the</c><00:04:20.440><c> tokens</c><00:04:20.720><c> are</c><00:04:20.880><c> video</c><00:04:21.239><c> they</c><00:04:21.400><c> will</c><00:04:21.560><c> take</c>

00:04:21.789 --> 00:04:21.799 align:start position:0%
if the tokens are video they will take
 

00:04:21.799 --> 00:04:23.870 align:start position:0%
if the tokens are video they will take
longer<00:04:22.160><c> and</c><00:04:22.320><c> stuff</c><00:04:22.520><c> like</c><00:04:22.720><c> that</c><00:04:23.360><c> so</c><00:04:23.520><c> it</c><00:04:23.680><c> does</c>

00:04:23.870 --> 00:04:23.880 align:start position:0%
longer and stuff like that so it does
 

00:04:23.880 --> 00:04:26.590 align:start position:0%
longer and stuff like that so it does
seem<00:04:24.120><c> like</c><00:04:24.280><c> it's</c><00:04:24.520><c> found</c><00:04:25.080><c> what</c><00:04:25.240><c> LCL</c><00:04:25.880><c> is</c><00:04:26.160><c> so</c><00:04:26.360><c> Lang</c>

00:04:26.590 --> 00:04:26.600 align:start position:0%
seem like it's found what LCL is so Lang
 

00:04:26.600 --> 00:04:28.110 align:start position:0%
seem like it's found what LCL is so Lang
chain<00:04:26.840><c> expression</c><00:04:27.240><c> language</c><00:04:27.600><c> Lang</c><00:04:27.840><c> chain</c>

00:04:28.110 --> 00:04:28.120 align:start position:0%
chain expression language Lang chain
 

00:04:28.120 --> 00:04:29.749 align:start position:0%
chain expression language Lang chain
expression<00:04:28.479><c> language</c><00:04:28.800><c> is</c><00:04:28.880><c> a</c><00:04:29.000><c> declarative</c><00:04:29.440><c> way</c>

00:04:29.749 --> 00:04:29.759 align:start position:0%
expression language is a declarative way
 

00:04:29.759 --> 00:04:31.990 align:start position:0%
expression language is a declarative way
to<00:04:29.880><c> build</c><00:04:30.199><c> L</c><00:04:30.440><c> chain</c><00:04:30.680><c> chains</c><00:04:31.479><c> it</c><00:04:31.600><c> goes</c><00:04:31.800><c> through</c>

00:04:31.990 --> 00:04:32.000 align:start position:0%
to build L chain chains it goes through
 

00:04:32.000 --> 00:04:33.469 align:start position:0%
to build L chain chains it goes through
and<00:04:32.120><c> it</c><00:04:32.240><c> tells</c><00:04:32.440><c> us</c><00:04:32.560><c> a</c><00:04:32.720><c> bit</c><00:04:32.880><c> about</c><00:04:33.120><c> them</c><00:04:33.280><c> and</c>

00:04:33.469 --> 00:04:33.479 align:start position:0%
and it tells us a bit about them and
 

00:04:33.479 --> 00:04:35.510 align:start position:0%
and it tells us a bit about them and
stuff<00:04:34.120><c> Lang</c><00:04:34.440><c> graph</c><00:04:34.720><c> it</c><00:04:34.880><c> does</c><00:04:35.039><c> seems</c><00:04:35.240><c> like</c><00:04:35.360><c> it's</c>

00:04:35.510 --> 00:04:35.520 align:start position:0%
stuff Lang graph it does seems like it's
 

00:04:35.520 --> 00:04:37.270 align:start position:0%
stuff Lang graph it does seems like it's
already<00:04:35.680><c> found</c><00:04:36.000><c> langra</c><00:04:36.520><c> as</c><00:04:36.600><c> well</c><00:04:36.840><c> Lang</c><00:04:37.080><c> graph</c>

00:04:37.270 --> 00:04:37.280 align:start position:0%
already found langra as well Lang graph
 

00:04:37.280 --> 00:04:38.830 align:start position:0%
already found langra as well Lang graph
is<00:04:37.360><c> still</c><00:04:37.479><c> under</c><00:04:37.800><c> development</c><00:04:38.320><c> and</c><00:04:38.440><c> is</c><00:04:38.639><c> not</c>

00:04:38.830 --> 00:04:38.840 align:start position:0%
is still under development and is not
 

00:04:38.840 --> 00:04:41.390 align:start position:0%
is still under development and is not
yet<00:04:39.039><c> officially</c><00:04:39.560><c> released</c><00:04:40.360><c> so</c><00:04:41.039><c> I</c><00:04:41.120><c> think</c><00:04:41.240><c> it's</c>

00:04:41.390 --> 00:04:41.400 align:start position:0%
yet officially released so I think it's
 

00:04:41.400 --> 00:04:42.990 align:start position:0%
yet officially released so I think it's
been<00:04:41.600><c> officially</c><00:04:42.000><c> released</c><00:04:42.400><c> so</c><00:04:42.600><c> maybe</c><00:04:42.880><c> that</c>

00:04:42.990 --> 00:04:43.000 align:start position:0%
been officially released so maybe that
 

00:04:43.000 --> 00:04:44.390 align:start position:0%
been officially released so maybe that
the<00:04:43.120><c> version</c><00:04:43.360><c> of</c><00:04:43.479><c> the</c><00:04:43.600><c> docs</c><00:04:43.840><c> is</c><00:04:43.960><c> a</c><00:04:44.039><c> little</c><00:04:44.240><c> bit</c>

00:04:44.390 --> 00:04:44.400 align:start position:0%
the version of the docs is a little bit
 

00:04:44.400 --> 00:04:46.710 align:start position:0%
the version of the docs is a little bit
old<00:04:45.320><c> okay</c><00:04:45.520><c> this</c><00:04:45.720><c> time</c><00:04:45.919><c> let's</c><00:04:46.120><c> try</c><00:04:46.280><c> and</c><00:04:46.479><c> get</c><00:04:46.600><c> it</c>

00:04:46.710 --> 00:04:46.720 align:start position:0%
old okay this time let's try and get it
 

00:04:46.720 --> 00:04:49.550 align:start position:0%
old okay this time let's try and get it
to<00:04:47.080><c> actually</c><00:04:47.440><c> write</c><00:04:47.759><c> some</c><00:04:48.199><c> code</c><00:04:48.880><c> using</c><00:04:49.280><c> these</c>

00:04:49.550 --> 00:04:49.560 align:start position:0%
to actually write some code using these
 

00:04:49.560 --> 00:04:54.029 align:start position:0%
to actually write some code using these
docs<00:04:50.000><c> so</c><00:04:50.199><c> I'm</c><00:04:50.280><c> going</c><00:04:50.400><c> to</c><00:04:50.560><c> ask</c><00:04:50.800><c> it</c><00:04:51.560><c> write</c><00:04:52.440><c> me</c><00:04:53.199><c> a</c>

00:04:54.029 --> 00:04:54.039 align:start position:0%
docs so I'm going to ask it write me a
 

00:04:54.039 --> 00:04:58.230 align:start position:0%
docs so I'm going to ask it write me a
simple<00:04:55.080><c> chain</c><00:04:56.280><c> using</c><00:04:57.280><c> the</c><00:04:57.479><c> chat</c><00:04:57.800><c> Google</c>

00:04:58.230 --> 00:04:58.240 align:start position:0%
simple chain using the chat Google
 

00:04:58.240 --> 00:05:04.070 align:start position:0%
simple chain using the chat Google
generative<00:04:58.840><c> AI</c><00:05:00.039><c> we</c><00:05:00.880><c> German</c><00:05:01.880><c> a</c><00:05:02.840><c> pro</c><00:05:03.520><c> for</c><00:05:03.880><c> the</c>

00:05:04.070 --> 00:05:04.080 align:start position:0%
generative AI we German a pro for the
 

00:05:04.080 --> 00:05:06.749 align:start position:0%
generative AI we German a pro for the
model<00:05:05.080><c> all</c><00:05:05.199><c> right</c><00:05:05.479><c> again</c><00:05:05.880><c> because</c><00:05:06.320><c> I'm</c>

00:05:06.749 --> 00:05:06.759 align:start position:0%
model all right again because I'm
 

00:05:06.759 --> 00:05:10.350 align:start position:0%
model all right again because I'm
running<00:05:07.120><c> it</c><00:05:07.320><c> on</c><00:05:08.160><c> 500,000</c><00:05:09.160><c> context</c><00:05:09.759><c> here</c><00:05:10.240><c> it's</c>

00:05:10.350 --> 00:05:10.360 align:start position:0%
running it on 500,000 context here it's
 

00:05:10.360 --> 00:05:12.390 align:start position:0%
running it on 500,000 context here it's
going<00:05:10.479><c> to</c><00:05:10.600><c> take</c><00:05:10.759><c> about</c><00:05:11.000><c> 50</c><00:05:11.360><c> seconds</c><00:05:12.120><c> to</c>

00:05:12.390 --> 00:05:12.400 align:start position:0%
going to take about 50 seconds to
 

00:05:12.400 --> 00:05:15.070 align:start position:0%
going to take about 50 seconds to
actually<00:05:12.680><c> do</c><00:05:12.919><c> the</c><00:05:13.080><c> inference</c><00:05:13.680><c> so</c><00:05:14.360><c> I'll</c><00:05:14.880><c> just</c>

00:05:15.070 --> 00:05:15.080 align:start position:0%
actually do the inference so I'll just
 

00:05:15.080 --> 00:05:16.790 align:start position:0%
actually do the inference so I'll just
pause<00:05:15.320><c> it</c><00:05:15.520><c> here</c><00:05:15.759><c> and</c><00:05:15.960><c> come</c><00:05:16.240><c> back</c><00:05:16.479><c> when</c><00:05:16.639><c> it's</c>

00:05:16.790 --> 00:05:16.800 align:start position:0%
pause it here and come back when it's
 

00:05:16.800 --> 00:05:18.909 align:start position:0%
pause it here and come back when it's
going<00:05:17.600><c> okay</c><00:05:17.840><c> so</c><00:05:18.000><c> you</c><00:05:18.080><c> can</c><00:05:18.199><c> see</c><00:05:18.520><c> just</c><00:05:18.639><c> at</c><00:05:18.800><c> the</c>

00:05:18.909 --> 00:05:18.919 align:start position:0%
going okay so you can see just at the
 

00:05:18.919 --> 00:05:22.189 align:start position:0%
going okay so you can see just at the
50c<00:05:19.560><c> Mark</c><00:05:19.880><c> it's</c><00:05:20.080><c> kicking</c><00:05:20.520><c> in</c><00:05:21.479><c> okay</c><00:05:21.680><c> from</c><00:05:21.880><c> Lang</c>

00:05:22.189 --> 00:05:22.199 align:start position:0%
50c Mark it's kicking in okay from Lang
 

00:05:22.199 --> 00:05:25.710 align:start position:0%
50c Mark it's kicking in okay from Lang
chain<00:05:22.680><c> J</c><00:05:23.680><c> A</c><00:05:24.120><c> import</c><00:05:24.560><c> check</c><00:05:25.120><c> I'm</c><00:05:25.240><c> not</c><00:05:25.360><c> sure</c><00:05:25.520><c> if</c>

00:05:25.710 --> 00:05:25.720 align:start position:0%
chain J A import check I'm not sure if
 

00:05:25.720 --> 00:05:27.870 align:start position:0%
chain J A import check I'm not sure if
that<00:05:25.919><c> is</c><00:05:26.120><c> actually</c><00:05:26.479><c> correct</c><00:05:26.960><c> there</c><00:05:27.560><c> but</c><00:05:27.680><c> it's</c>

00:05:27.870 --> 00:05:27.880 align:start position:0%
that is actually correct there but it's
 

00:05:27.880 --> 00:05:30.830 align:start position:0%
that is actually correct there but it's
gotten<00:05:28.280><c> Gemini</c><00:05:28.840><c> Pro</c><00:05:29.759><c> a</c><00:05:29.880><c> very</c><00:05:30.120><c> old</c><00:05:30.479><c> sort</c><00:05:30.680><c> of</c>

00:05:30.830 --> 00:05:30.840 align:start position:0%
gotten Gemini Pro a very old sort of
 

00:05:30.840 --> 00:05:33.550 align:start position:0%
gotten Gemini Pro a very old sort of
style<00:05:31.400><c> L</c><00:05:31.759><c> chain</c><00:05:32.160><c> with</c><00:05:32.280><c> an</c><00:05:32.440><c> llm</c><00:05:33.000><c> with</c><00:05:33.120><c> a</c><00:05:33.240><c> prompt</c>

00:05:33.550 --> 00:05:33.560 align:start position:0%
style L chain with an llm with a prompt
 

00:05:33.560 --> 00:05:35.670 align:start position:0%
style L chain with an llm with a prompt
template<00:05:34.560><c> and</c><00:05:34.800><c> basically</c><00:05:35.160><c> running</c><00:05:35.479><c> through</c>

00:05:35.670 --> 00:05:35.680 align:start position:0%
template and basically running through
 

00:05:35.680 --> 00:05:37.270 align:start position:0%
template and basically running through
it<00:05:35.880><c> and</c><00:05:35.960><c> then</c><00:05:36.080><c> it's</c><00:05:36.280><c> explaining</c><00:05:36.720><c> the</c><00:05:36.840><c> code</c><00:05:37.080><c> to</c>

00:05:37.270 --> 00:05:37.280 align:start position:0%
it and then it's explaining the code to
 

00:05:37.280 --> 00:05:39.390 align:start position:0%
it and then it's explaining the code to
us<00:05:37.800><c> in</c><00:05:37.960><c> here</c><00:05:38.160><c> and</c><00:05:38.319><c> basically</c><00:05:38.639><c> explaining</c><00:05:39.199><c> that</c>

00:05:39.390 --> 00:05:39.400 align:start position:0%
us in here and basically explaining that
 

00:05:39.400 --> 00:05:41.350 align:start position:0%
us in here and basically explaining that
this<00:05:39.520><c> should</c><00:05:39.720><c> be</c><00:05:39.840><c> the</c><00:05:39.960><c> Google</c><00:05:40.319><c> API</c><00:05:40.880><c> key</c><00:05:41.120><c> in</c>

00:05:41.350 --> 00:05:41.360 align:start position:0%
this should be the Google API key in
 

00:05:41.360 --> 00:05:43.710 align:start position:0%
this should be the Google API key in
there<00:05:42.240><c> all</c><00:05:42.360><c> right</c><00:05:42.520><c> so</c><00:05:42.720><c> let's</c><00:05:43.080><c> move</c><00:05:43.319><c> on</c><00:05:43.600><c> and</c>

00:05:43.710 --> 00:05:43.720 align:start position:0%
there all right so let's move on and
 

00:05:43.720 --> 00:05:46.230 align:start position:0%
there all right so let's move on and
have<00:05:43.840><c> a</c><00:05:44.000><c> look</c><00:05:44.120><c> at</c><00:05:44.280><c> some</c><00:05:44.600><c> videos</c><00:05:45.280><c> now</c><00:05:45.960><c> all</c><00:05:46.080><c> right</c>

00:05:46.230 --> 00:05:46.240 align:start position:0%
have a look at some videos now all right
 

00:05:46.240 --> 00:05:48.909 align:start position:0%
have a look at some videos now all right
so<00:05:46.400><c> many</c><00:05:46.600><c> of</c><00:05:46.759><c> you</c><00:05:47.000><c> have</c><00:05:47.199><c> seen</c><00:05:47.680><c> this</c><00:05:47.960><c> video</c><00:05:48.600><c> of</c>

00:05:48.909 --> 00:05:48.919 align:start position:0%
so many of you have seen this video of
 

00:05:48.919 --> 00:05:51.350 align:start position:0%
so many of you have seen this video of
the<00:05:49.039><c> original</c><00:05:49.600><c> Gemini</c><00:05:50.160><c> demo</c><00:05:50.800><c> of</c><00:05:51.039><c> basically</c>

00:05:51.350 --> 00:05:51.360 align:start position:0%
the original Gemini demo of basically
 

00:05:51.360 --> 00:05:53.309 align:start position:0%
the original Gemini demo of basically
asking<00:05:51.600><c> it</c><00:05:51.720><c> about</c><00:05:51.880><c> a</c><00:05:52.039><c> dock</c><00:05:52.639><c> playing</c><00:05:52.919><c> a</c><00:05:53.039><c> sort</c><00:05:53.199><c> of</c>

00:05:53.309 --> 00:05:53.319 align:start position:0%
asking it about a dock playing a sort of
 

00:05:53.319 --> 00:05:56.670 align:start position:0%
asking it about a dock playing a sort of
game<00:05:53.960><c> finding</c><00:05:54.479><c> the</c><00:05:54.880><c> the</c><00:05:55.000><c> paper</c><00:05:55.800><c> playing</c><00:05:56.440><c> Paper</c>

00:05:56.670 --> 00:05:56.680 align:start position:0%
game finding the the paper playing Paper
 

00:05:56.680 --> 00:05:58.950 align:start position:0%
game finding the the paper playing Paper
Rock<00:05:56.880><c> Scissors</c><00:05:57.720><c> doing</c><00:05:57.919><c> some</c><00:05:58.120><c> comparisons</c>

00:05:58.950 --> 00:05:58.960 align:start position:0%
Rock Scissors doing some comparisons
 

00:05:58.960 --> 00:06:00.749 align:start position:0%
Rock Scissors doing some comparisons
doing<00:05:59.199><c> this</c><00:05:59.479><c> so</c><00:05:59.800><c> let's</c><00:06:00.000><c> check</c><00:06:00.280><c> this</c><00:06:00.400><c> in</c><00:06:00.639><c> and</c>

00:06:00.749 --> 00:06:00.759 align:start position:0%
doing this so let's check this in and
 

00:06:00.759 --> 00:06:03.189 align:start position:0%
doing this so let's check this in and
see<00:06:01.120><c> okay</c><00:06:01.280><c> how</c><00:06:01.400><c> well</c><00:06:01.600><c> does</c><00:06:01.759><c> this</c><00:06:01.880><c> do</c><00:06:02.639><c> okay</c><00:06:02.880><c> so</c>

00:06:03.189 --> 00:06:03.199 align:start position:0%
see okay how well does this do okay so
 

00:06:03.199 --> 00:06:05.270 align:start position:0%
see okay how well does this do okay so
here<00:06:03.639><c> you</c><00:06:03.720><c> can</c><00:06:03.840><c> see</c><00:06:04.160><c> I've</c><00:06:04.319><c> taken</c><00:06:04.600><c> that</c><00:06:04.759><c> video</c>

00:06:05.270 --> 00:06:05.280 align:start position:0%
here you can see I've taken that video
 

00:06:05.280 --> 00:06:07.510 align:start position:0%
here you can see I've taken that video
I've<00:06:05.479><c> put</c><00:06:05.639><c> it</c><00:06:05.840><c> up</c><00:06:06.039><c> here</c><00:06:06.280><c> let</c><00:06:06.800><c> click</c><00:06:07.000><c> this</c><00:06:07.120><c> run</c>

00:06:07.510 --> 00:06:07.520 align:start position:0%
I've put it up here let click this run
 

00:06:07.520 --> 00:06:09.670 align:start position:0%
I've put it up here let click this run
while<00:06:07.680><c> I'm</c><00:06:07.880><c> doing</c><00:06:08.160><c> it</c><00:06:08.639><c> and</c><00:06:08.759><c> I've</c><00:06:08.919><c> put</c><00:06:09.039><c> in</c><00:06:09.360><c> a</c>

00:06:09.670 --> 00:06:09.680 align:start position:0%
while I'm doing it and I've put in a
 

00:06:09.680 --> 00:06:11.270 align:start position:0%
while I'm doing it and I've put in a
prompt<00:06:10.160><c> and</c><00:06:10.240><c> so</c><00:06:10.400><c> the</c><00:06:10.560><c> prompt</c><00:06:10.840><c> is</c><00:06:10.960><c> basically</c>

00:06:11.270 --> 00:06:11.280 align:start position:0%
prompt and so the prompt is basically
 

00:06:11.280 --> 00:06:13.110 align:start position:0%
prompt and so the prompt is basically
saying<00:06:11.560><c> can</c><00:06:11.680><c> you</c><00:06:11.840><c> break</c><00:06:12.080><c> down</c><00:06:12.319><c> each</c><00:06:12.520><c> scene</c><00:06:12.919><c> in</c>

00:06:13.110 --> 00:06:13.120 align:start position:0%
saying can you break down each scene in
 

00:06:13.120 --> 00:06:16.469 align:start position:0%
saying can you break down each scene in
this<00:06:13.479><c> and</c><00:06:13.680><c> give</c><00:06:13.840><c> me</c><00:06:14.120><c> an</c><00:06:14.319><c> outline</c><00:06:15.000><c> of</c><00:06:15.160><c> the</c><00:06:15.479><c> video</c>

00:06:16.469 --> 00:06:16.479 align:start position:0%
this and give me an outline of the video
 

00:06:16.479 --> 00:06:18.589 align:start position:0%
this and give me an outline of the video
so<00:06:16.720><c> really</c><00:06:16.919><c> we're</c><00:06:17.120><c> just</c><00:06:17.440><c> looking</c><00:06:17.720><c> to</c><00:06:18.039><c> identify</c>

00:06:18.589 --> 00:06:18.599 align:start position:0%
so really we're just looking to identify
 

00:06:18.599 --> 00:06:20.950 align:start position:0%
so really we're just looking to identify
some<00:06:18.720><c> of</c><00:06:18.880><c> the</c><00:06:19.000><c> scenes</c><00:06:19.479><c> that</c><00:06:19.560><c> were</c><00:06:19.800><c> in</c><00:06:20.039><c> there</c>

00:06:20.950 --> 00:06:20.960 align:start position:0%
some of the scenes that were in there
 

00:06:20.960 --> 00:06:22.790 align:start position:0%
some of the scenes that were in there
and<00:06:21.440><c> then</c><00:06:21.599><c> be</c><00:06:21.759><c> able</c><00:06:21.960><c> to</c><00:06:22.120><c> generate</c><00:06:22.520><c> what's</c>

00:06:22.790 --> 00:06:22.800 align:start position:0%
and then be able to generate what's
 

00:06:22.800 --> 00:06:25.629 align:start position:0%
and then be able to generate what's
going<00:06:22.960><c> on</c><00:06:23.440><c> okay</c><00:06:23.919><c> 0</c><00:06:24.240><c> to</c><00:06:24.599><c> 19</c><00:06:25.000><c> seconds</c><00:06:25.400><c> video</c>

00:06:25.629 --> 00:06:25.639 align:start position:0%
going on okay 0 to 19 seconds video
 

00:06:25.639 --> 00:06:27.670 align:start position:0%
going on okay 0 to 19 seconds video
opens<00:06:25.919><c> with</c><00:06:26.080><c> Deep</c><00:06:26.280><c> Mind</c><00:06:26.560><c> explaining</c><00:06:27.280><c> testing</c>

00:06:27.670 --> 00:06:27.680 align:start position:0%
opens with Deep Mind explaining testing
 

00:06:27.680 --> 00:06:30.510 align:start position:0%
opens with Deep Mind explaining testing
capabilities<00:06:28.280><c> of</c><00:06:28.520><c> Gemini</c><00:06:29.639><c> scene</c><00:06:29.919><c> one</c><00:06:30.160><c> drawing</c>

00:06:30.510 --> 00:06:30.520 align:start position:0%
capabilities of Gemini scene one drawing
 

00:06:30.520 --> 00:06:33.469 align:start position:0%
capabilities of Gemini scene one drawing
a<00:06:30.720><c> duck</c><00:06:31.560><c> okay</c><00:06:31.759><c> so</c><00:06:32.000><c> that's</c><00:06:32.319><c> quite</c><00:06:32.639><c> good</c><00:06:33.280><c> person</c>

00:06:33.469 --> 00:06:33.479 align:start position:0%
a duck okay so that's quite good person
 

00:06:33.479 --> 00:06:35.870 align:start position:0%
a duck okay so that's quite good person
draws<00:06:33.759><c> a</c><00:06:33.840><c> squiggly</c><00:06:34.360><c> line</c><00:06:35.160><c> then</c><00:06:35.400><c> guess</c><00:06:35.720><c> the</c>

00:06:35.870 --> 00:06:35.880 align:start position:0%
draws a squiggly line then guess the
 

00:06:35.880 --> 00:06:38.990 align:start position:0%
draws a squiggly line then guess the
country<00:06:36.400><c> for</c><00:06:36.759><c> scene</c><00:06:37.080><c> two</c><00:06:37.759><c> and</c><00:06:37.880><c> it</c><00:06:38.039><c> shows</c><00:06:38.599><c> yes</c><00:06:38.800><c> a</c>

00:06:38.990 --> 00:06:39.000 align:start position:0%
country for scene two and it shows yes a
 

00:06:39.000 --> 00:06:41.589 align:start position:0%
country for scene two and it shows yes a
map<00:06:39.360><c> is</c><00:06:39.639><c> of</c><00:06:39.800><c> the</c><00:06:39.919><c> world</c><00:06:40.199><c> is</c><00:06:40.360><c> drawn</c><00:06:40.919><c> okay</c><00:06:41.160><c> Gemini</c>

00:06:41.589 --> 00:06:41.599 align:start position:0%
map is of the world is drawn okay Gemini
 

00:06:41.599 --> 00:06:43.230 align:start position:0%
map is of the world is drawn okay Gemini
ask<00:06:41.840><c> the</c><00:06:41.960><c> user</c><00:06:42.199><c> to</c><00:06:42.360><c> guess</c><00:06:42.560><c> the</c><00:06:42.680><c> country</c><00:06:43.000><c> based</c>

00:06:43.230 --> 00:06:43.240 align:start position:0%
ask the user to guess the country based
 

00:06:43.240 --> 00:06:45.909 align:start position:0%
ask the user to guess the country based
on<00:06:43.360><c> a</c><00:06:43.479><c> series</c><00:06:43.800><c> of</c><00:06:44.039><c> Clues</c><00:06:44.960><c> and</c><00:06:45.120><c> it's</c><00:06:45.240><c> a</c><00:06:45.319><c> bit</c><00:06:45.520><c> slow</c>

00:06:45.909 --> 00:06:45.919 align:start position:0%
on a series of Clues and it's a bit slow
 

00:06:45.919 --> 00:06:47.710 align:start position:0%
on a series of Clues and it's a bit slow
in<00:06:46.080><c> generating</c><00:06:46.599><c> as</c><00:06:46.680><c> it</c><00:06:46.840><c> goes</c><00:06:47.120><c> through</c><00:06:47.440><c> this</c>

00:06:47.710 --> 00:06:47.720 align:start position:0%
in generating as it goes through this
 

00:06:47.720 --> 00:06:49.749 align:start position:0%
in generating as it goes through this
all<00:06:47.840><c> right</c><00:06:48.000><c> it's</c><00:06:48.160><c> generated</c><00:06:48.599><c> the</c><00:06:48.759><c> whole</c><00:06:49.000><c> thing</c>

00:06:49.749 --> 00:06:49.759 align:start position:0%
all right it's generated the whole thing
 

00:06:49.759 --> 00:06:52.110 align:start position:0%
all right it's generated the whole thing
Scene<00:06:50.080><c> Three</c><00:06:50.360><c> is</c><00:06:50.800><c> a</c><00:06:50.960><c> magic</c><00:06:51.240><c> trick</c><00:06:51.639><c> performs</c><00:06:52.000><c> a</c>

00:06:52.110 --> 00:06:52.120 align:start position:0%
Scene Three is a magic trick performs a
 

00:06:52.120 --> 00:06:56.309 align:start position:0%
Scene Three is a magic trick performs a
magic<00:06:52.319><c> trick</c><00:06:52.560><c> with</c><00:06:52.680><c> a</c><00:06:53.120><c> coin</c><00:06:54.120><c> hand</c><00:06:55.120><c> gestures</c><00:06:56.120><c> so</c>

00:06:56.309 --> 00:06:56.319 align:start position:0%
magic trick with a coin hand gestures so
 

00:06:56.319 --> 00:06:57.909 align:start position:0%
magic trick with a coin hand gestures so
Gemini<00:06:56.759><c> correctly</c><00:06:57.160><c> identifies</c><00:06:57.639><c> that</c><00:06:57.800><c> the</c>

00:06:57.909 --> 00:06:57.919 align:start position:0%
Gemini correctly identifies that the
 

00:06:57.919 --> 00:07:00.430 align:start position:0%
Gemini correctly identifies that the
gestures<00:06:58.520><c> are</c><00:06:58.680><c> rock</c><00:06:58.960><c> paper</c><00:06:59.400><c> scissors</c><00:06:59.800><c> and</c><00:06:59.960><c> a</c>

00:07:00.430 --> 00:07:00.440 align:start position:0%
gestures are rock paper scissors and a
 

00:07:00.440 --> 00:07:03.990 align:start position:0%
gestures are rock paper scissors and a
butterfly<00:07:01.440><c> yarn</c><00:07:01.840><c> and</c><00:07:02.199><c> Imagination</c><00:07:03.199><c> for</c><00:07:03.479><c> scene</c>

00:07:03.990 --> 00:07:04.000 align:start position:0%
butterfly yarn and Imagination for scene
 

00:07:04.000 --> 00:07:06.909 align:start position:0%
butterfly yarn and Imagination for scene
five<00:07:04.599><c> I</c><00:07:04.680><c> think</c><00:07:04.919><c> that's</c><00:07:05.199><c> correct</c><00:07:06.199><c> there</c><00:07:06.800><c> and</c>

00:07:06.909 --> 00:07:06.919 align:start position:0%
five I think that's correct there and
 

00:07:06.919 --> 00:07:08.110 align:start position:0%
five I think that's correct there and
then<00:07:07.039><c> it's</c><00:07:07.199><c> trying</c><00:07:07.479><c> to</c><00:07:07.560><c> do</c><00:07:07.720><c> some</c><00:07:07.879><c> of</c><00:07:08.000><c> the</c>

00:07:08.110 --> 00:07:08.120 align:start position:0%
then it's trying to do some of the
 

00:07:08.120 --> 00:07:10.469 align:start position:0%
then it's trying to do some of the
reasoning<00:07:08.479><c> and</c><00:07:08.720><c> decision</c><00:07:09.160><c> making</c><00:07:10.160><c> and</c><00:07:10.280><c> then</c>

00:07:10.469 --> 00:07:10.479 align:start position:0%
reasoning and decision making and then
 

00:07:10.479 --> 00:07:12.350 align:start position:0%
reasoning and decision making and then
finally<00:07:10.800><c> scene</c><00:07:11.160><c> Seven</c><00:07:11.639><c> connect</c><00:07:11.919><c> the</c><00:07:12.039><c> dots</c>

00:07:12.350 --> 00:07:12.360 align:start position:0%
finally scene Seven connect the dots
 

00:07:12.360 --> 00:07:14.469 align:start position:0%
finally scene Seven connect the dots
person<00:07:12.599><c> completes</c><00:07:13.080><c> a</c><00:07:13.639><c> connect</c><00:07:14.000><c> the</c><00:07:14.120><c> dots</c>

00:07:14.469 --> 00:07:14.479 align:start position:0%
person completes a connect the dots
 

00:07:14.479 --> 00:07:16.510 align:start position:0%
person completes a connect the dots
puzzle<00:07:14.879><c> Gemini</c><00:07:15.280><c> identifies</c><00:07:15.800><c> the</c><00:07:15.960><c> puzzle</c><00:07:16.319><c> as</c><00:07:16.400><c> a</c>

00:07:16.510 --> 00:07:16.520 align:start position:0%
puzzle Gemini identifies the puzzle as a
 

00:07:16.520 --> 00:07:18.990 align:start position:0%
puzzle Gemini identifies the puzzle as a
picture<00:07:16.759><c> of</c><00:07:16.879><c> a</c><00:07:17.080><c> crab</c><00:07:17.800><c> so</c><00:07:18.039><c> it's</c><00:07:18.240><c> gone</c><00:07:18.720><c> through</c>

00:07:18.990 --> 00:07:19.000 align:start position:0%
picture of a crab so it's gone through
 

00:07:19.000 --> 00:07:20.670 align:start position:0%
picture of a crab so it's gone through
and<00:07:19.319><c> identified</c><00:07:19.840><c> a</c><00:07:19.960><c> lot</c><00:07:20.080><c> of</c><00:07:20.199><c> the</c><00:07:20.319><c> things</c><00:07:20.520><c> in</c>

00:07:20.670 --> 00:07:20.680 align:start position:0%
and identified a lot of the things in
 

00:07:20.680 --> 00:07:23.270 align:start position:0%
and identified a lot of the things in
this<00:07:20.879><c> video</c><00:07:21.360><c> pretty</c><00:07:21.759><c> correctly</c><00:07:22.759><c> all</c><00:07:22.960><c> right</c>

00:07:23.270 --> 00:07:23.280 align:start position:0%
this video pretty correctly all right
 

00:07:23.280 --> 00:07:25.869 align:start position:0%
this video pretty correctly all right
and<00:07:23.560><c> this</c><00:07:24.160><c> I</c><00:07:24.280><c> haven't</c><00:07:24.680><c> staged</c><00:07:25.160><c> it</c><00:07:25.400><c> I</c><00:07:25.520><c> haven't</c>

00:07:25.869 --> 00:07:25.879 align:start position:0%
and this I haven't staged it I haven't
 

00:07:25.879 --> 00:07:27.390 align:start position:0%
and this I haven't staged it I haven't
done<00:07:26.160><c> any</c><00:07:26.400><c> other</c><00:07:26.639><c> prompts</c><00:07:27.080><c> I've</c><00:07:27.240><c> just</c>

00:07:27.390 --> 00:07:27.400 align:start position:0%
done any other prompts I've just
 

00:07:27.400 --> 00:07:30.830 align:start position:0%
done any other prompts I've just
uploaded<00:07:27.840><c> the</c><00:07:28.000><c> video</c><00:07:28.800><c> and</c><00:07:28.919><c> then</c><00:07:29.599><c> it</c><00:07:29.840><c> for</c><00:07:30.160><c> this</c>

00:07:30.830 --> 00:07:30.840 align:start position:0%
uploaded the video and then it for this
 

00:07:30.840 --> 00:07:33.110 align:start position:0%
uploaded the video and then it for this
overall<00:07:31.240><c> outline</c><00:07:31.840><c> introduction</c><00:07:32.319><c> to</c><00:07:32.520><c> Gemini's</c>

00:07:33.110 --> 00:07:33.120 align:start position:0%
overall outline introduction to Gemini's
 

00:07:33.120 --> 00:07:35.270 align:start position:0%
overall outline introduction to Gemini's
capabilities<00:07:34.039><c> examples</c><00:07:34.520><c> of</c><00:07:34.759><c> Gemini's</c>

00:07:35.270 --> 00:07:35.280 align:start position:0%
capabilities examples of Gemini's
 

00:07:35.280 --> 00:07:38.029 align:start position:0%
capabilities examples of Gemini's
ability<00:07:35.680><c> to</c><00:07:35.879><c> reason</c><00:07:36.360><c> about</c><00:07:36.639><c> images</c><00:07:37.440><c> and</c><00:07:37.639><c> Video</c>

00:07:38.029 --> 00:07:38.039 align:start position:0%
ability to reason about images and Video
 

00:07:38.039 --> 00:07:40.550 align:start position:0%
ability to reason about images and Video
in<00:07:38.240><c> here</c><00:07:38.560><c> conclusion</c><00:07:39.039><c> and</c><00:07:39.199><c> call</c><00:07:39.440><c> to</c><00:07:39.680><c> action</c>

00:07:40.550 --> 00:07:40.560 align:start position:0%
in here conclusion and call to action
 

00:07:40.560 --> 00:07:42.990 align:start position:0%
in here conclusion and call to action
all<00:07:40.680><c> right</c><00:07:40.960><c> so</c><00:07:41.160><c> I</c><00:07:41.400><c> I'm</c><00:07:41.479><c> going</c><00:07:41.560><c> to</c><00:07:41.759><c> accept</c><00:07:42.160><c> that</c>

00:07:42.990 --> 00:07:43.000 align:start position:0%
all right so I I'm going to accept that
 

00:07:43.000 --> 00:07:46.149 align:start position:0%
all right so I I'm going to accept that
in<00:07:43.280><c> here</c><00:07:44.120><c> what</c><00:07:44.240><c> I</c><00:07:44.400><c> could</c><00:07:44.599><c> do</c><00:07:45.039><c> also</c><00:07:45.599><c> is</c><00:07:45.759><c> I</c><00:07:45.879><c> can</c>

00:07:46.149 --> 00:07:46.159 align:start position:0%
in here what I could do also is I can
 

00:07:46.159 --> 00:07:48.909 align:start position:0%
in here what I could do also is I can
ask<00:07:46.479><c> it</c><00:07:47.039><c> very</c><00:07:47.360><c> specific</c><00:07:47.960><c> questions</c><00:07:48.520><c> about</c><00:07:48.759><c> a</c>

00:07:48.909 --> 00:07:48.919 align:start position:0%
ask it very specific questions about a
 

00:07:48.919 --> 00:07:51.230 align:start position:0%
ask it very specific questions about a
video<00:07:49.240><c> so</c><00:07:49.360><c> let's</c><00:07:49.560><c> look</c><00:07:49.680><c> at</c><00:07:49.840><c> another</c><00:07:50.280><c> video</c><00:07:51.120><c> and</c>

00:07:51.230 --> 00:07:51.240 align:start position:0%
video so let's look at another video and
 

00:07:51.240 --> 00:07:53.189 align:start position:0%
video so let's look at another video and
try<00:07:51.599><c> asking</c><00:07:51.919><c> it</c><00:07:52.080><c> some</c><00:07:52.319><c> very</c><00:07:52.759><c> specific</c>

00:07:53.189 --> 00:07:53.199 align:start position:0%
try asking it some very specific
 

00:07:53.199 --> 00:07:55.390 align:start position:0%
try asking it some very specific
questions<00:07:54.159><c> in</c><00:07:54.319><c> this</c><00:07:54.560><c> example</c><00:07:55.039><c> I'm</c><00:07:55.159><c> going</c><00:07:55.280><c> to</c>

00:07:55.390 --> 00:07:55.400 align:start position:0%
questions in this example I'm going to
 

00:07:55.400 --> 00:07:58.189 align:start position:0%
questions in this example I'm going to
be<00:07:55.599><c> using</c><00:07:56.319><c> quite</c><00:07:56.479><c> a</c><00:07:56.680><c> famous</c><00:07:57.440><c> psychological</c>

00:07:58.189 --> 00:07:58.199 align:start position:0%
be using quite a famous psychological
 

00:07:58.199 --> 00:08:00.430 align:start position:0%
be using quite a famous psychological
experiment<00:07:58.680><c> the</c><00:07:58.800><c> monkey</c><00:07:59.360><c> business</c><00:07:59.759><c> illusion</c>

00:08:00.430 --> 00:08:00.440 align:start position:0%
experiment the monkey business illusion
 

00:08:00.440 --> 00:08:02.110 align:start position:0%
experiment the monkey business illusion
there<00:08:00.560><c> are</c><00:08:00.680><c> different</c><00:08:00.960><c> versions</c><00:08:01.319><c> of</c><00:08:01.560><c> this</c>

00:08:02.110 --> 00:08:02.120 align:start position:0%
there are different versions of this
 

00:08:02.120 --> 00:08:04.110 align:start position:0%
there are different versions of this
basically<00:08:02.520><c> count</c><00:08:03.080><c> the</c><00:08:03.199><c> number</c><00:08:03.400><c> of</c><00:08:03.639><c> times</c>

00:08:04.110 --> 00:08:04.120 align:start position:0%
basically count the number of times
 

00:08:04.120 --> 00:08:06.790 align:start position:0%
basically count the number of times
players<00:08:04.639><c> pass</c><00:08:05.039><c> it</c><00:08:05.759><c> and</c><00:08:05.879><c> you'll</c><00:08:06.080><c> notice</c><00:08:06.479><c> that</c>

00:08:06.790 --> 00:08:06.800 align:start position:0%
players pass it and you'll notice that
 

00:08:06.800 --> 00:08:09.029 align:start position:0%
players pass it and you'll notice that
the<00:08:07.000><c> if</c><00:08:07.120><c> you've</c><00:08:07.319><c> seen</c><00:08:07.639><c> this</c><00:08:07.879><c> before</c><00:08:08.639><c> the</c><00:08:08.840><c> whole</c>

00:08:09.029 --> 00:08:09.039 align:start position:0%
the if you've seen this before the whole
 

00:08:09.039 --> 00:08:11.189 align:start position:0%
the if you've seen this before the whole
thing<00:08:09.240><c> is</c><00:08:09.479><c> do</c><00:08:09.680><c> you</c><00:08:10.000><c> actually</c><00:08:10.400><c> see</c><00:08:10.800><c> this</c>

00:08:11.189 --> 00:08:11.199 align:start position:0%
thing is do you actually see this
 

00:08:11.199 --> 00:08:13.430 align:start position:0%
thing is do you actually see this
gorilla<00:08:11.639><c> in</c><00:08:11.759><c> the</c><00:08:11.879><c> middle</c><00:08:12.360><c> here</c><00:08:12.960><c> as</c><00:08:13.039><c> we</c><00:08:13.240><c> go</c>

00:08:13.430 --> 00:08:13.440 align:start position:0%
gorilla in the middle here as we go
 

00:08:13.440 --> 00:08:15.110 align:start position:0%
gorilla in the middle here as we go
through<00:08:13.840><c> so</c><00:08:14.240><c> let's</c><00:08:14.440><c> come</c><00:08:14.599><c> in</c><00:08:14.800><c> here</c><00:08:15.000><c> we're</c>

00:08:15.110 --> 00:08:15.120 align:start position:0%
through so let's come in here we're
 

00:08:15.120 --> 00:08:21.189 align:start position:0%
through so let's come in here we're
going<00:08:15.240><c> to</c><00:08:15.440><c> ask</c><00:08:15.720><c> it</c><00:08:16.280><c> how</c><00:08:16.919><c> many</c><00:08:18.360><c> times</c><00:08:19.360><c> do</c><00:08:20.199><c> the</c>

00:08:21.189 --> 00:08:21.199 align:start position:0%
going to ask it how many times do the
 

00:08:21.199 --> 00:08:25.790 align:start position:0%
going to ask it how many times do the
players<00:08:22.039><c> in</c><00:08:23.159><c> White</c><00:08:24.159><c> Pass</c><00:08:24.759><c> the</c><00:08:24.919><c> ball</c><00:08:25.520><c> and</c><00:08:25.599><c> I've</c>

00:08:25.790 --> 00:08:25.800 align:start position:0%
players in White Pass the ball and I've
 

00:08:25.800 --> 00:08:27.589 align:start position:0%
players in White Pass the ball and I've
edited<00:08:26.159><c> out</c><00:08:26.400><c> the</c><00:08:26.560><c> answer</c><00:08:26.960><c> so</c><00:08:27.080><c> it</c><00:08:27.240><c> shouldn't</c>

00:08:27.589 --> 00:08:27.599 align:start position:0%
edited out the answer so it shouldn't
 

00:08:27.599 --> 00:08:29.950 align:start position:0%
edited out the answer so it shouldn't
know<00:08:27.919><c> the</c><00:08:28.080><c> answer</c><00:08:28.479><c> for</c><00:08:28.800><c> this</c><00:08:29.400><c> let's</c><00:08:29.560><c> run</c><00:08:29.800><c> this</c>

00:08:29.950 --> 00:08:29.960 align:start position:0%
know the answer for this let's run this
 

00:08:29.960 --> 00:08:32.829 align:start position:0%
know the answer for this let's run this
and<00:08:30.159><c> just</c><00:08:30.360><c> see</c><00:08:31.039><c> how</c><00:08:31.199><c> it</c><00:08:31.319><c> can</c><00:08:31.479><c> do</c><00:08:31.639><c> it</c><00:08:32.159><c> and</c><00:08:32.360><c> then</c>

00:08:32.829 --> 00:08:32.839 align:start position:0%
and just see how it can do it and then
 

00:08:32.839 --> 00:08:34.990 align:start position:0%
and just see how it can do it and then
we<00:08:32.959><c> will</c><00:08:33.200><c> ask</c><00:08:33.440><c> it</c><00:08:33.680><c> the</c><00:08:33.839><c> other</c><00:08:34.080><c> thing</c><00:08:34.519><c> is</c><00:08:34.760><c> that</c>

00:08:34.990 --> 00:08:35.000 align:start position:0%
we will ask it the other thing is that
 

00:08:35.000 --> 00:08:37.949 align:start position:0%
we will ask it the other thing is that
if<00:08:35.240><c> people</c><00:08:35.519><c> do</c><00:08:35.800><c> notice</c><00:08:36.360><c> the</c><00:08:36.560><c> actual</c><00:08:37.479><c> gorilla</c>

00:08:37.949 --> 00:08:37.959 align:start position:0%
if people do notice the actual gorilla
 

00:08:37.959 --> 00:08:40.110 align:start position:0%
if people do notice the actual gorilla
in<00:08:38.200><c> there</c><00:08:38.680><c> they</c><00:08:38.839><c> often</c><00:08:39.159><c> don't</c><00:08:39.479><c> notice</c><00:08:39.919><c> that</c>

00:08:40.110 --> 00:08:40.120 align:start position:0%
in there they often don't notice that
 

00:08:40.120 --> 00:08:42.909 align:start position:0%
in there they often don't notice that
the<00:08:40.279><c> curtains</c><00:08:41.039><c> change</c><00:08:41.440><c> colors</c><00:08:42.399><c> in</c><00:08:42.560><c> here</c><00:08:42.680><c> so</c><00:08:42.800><c> it</c>

00:08:42.909 --> 00:08:42.919 align:start position:0%
the curtains change colors in here so it
 

00:08:42.919 --> 00:08:45.750 align:start position:0%
the curtains change colors in here so it
says<00:08:43.279><c> 15</c><00:08:44.240><c> so</c><00:08:44.399><c> I</c><00:08:44.480><c> think</c><00:08:44.640><c> the</c><00:08:44.800><c> correct</c><00:08:45.160><c> answer</c><00:08:45.560><c> is</c>

00:08:45.750 --> 00:08:45.760 align:start position:0%
says 15 so I think the correct answer is
 

00:08:45.760 --> 00:08:48.949 align:start position:0%
says 15 so I think the correct answer is
16<00:08:46.320><c> so</c><00:08:46.560><c> that</c><00:08:46.720><c> that's</c><00:08:46.959><c> not</c><00:08:47.600><c> that</c><00:08:47.880><c> bad</c><00:08:48.560><c> what</c><00:08:48.680><c> if</c><00:08:48.800><c> I</c>

00:08:48.949 --> 00:08:48.959 align:start position:0%
16 so that that's not that bad what if I
 

00:08:48.959 --> 00:08:53.470 align:start position:0%
16 so that that's not that bad what if I
ask<00:08:49.200><c> it</c><00:08:49.600><c> how</c><00:08:49.760><c> many</c><00:08:50.399><c> times</c><00:08:51.399><c> do</c><00:08:51.880><c> the</c><00:08:52.480><c> curtains</c>

00:08:53.470 --> 00:08:53.480 align:start position:0%
ask it how many times do the curtains
 

00:08:53.480 --> 00:08:56.430 align:start position:0%
ask it how many times do the curtains
change<00:08:54.160><c> color</c><00:08:54.839><c> and</c><00:08:55.040><c> run</c><00:08:55.399><c> that</c><00:08:55.959><c> okay</c><00:08:56.120><c> so</c><00:08:56.240><c> it's</c>

00:08:56.430 --> 00:08:56.440 align:start position:0%
change color and run that okay so it's
 

00:08:56.440 --> 00:08:58.150 align:start position:0%
change color and run that okay so it's
going<00:08:56.640><c> through</c><00:08:57.040><c> so</c><00:08:57.360><c> it</c><00:08:57.440><c> is</c><00:08:57.560><c> kind</c><00:08:57.680><c> of</c><00:08:57.800><c> amazing</c>

00:08:58.150 --> 00:08:58.160 align:start position:0%
going through so it is kind of amazing
 

00:08:58.160 --> 00:08:59.910 align:start position:0%
going through so it is kind of amazing
that<00:08:58.360><c> this</c><00:08:58.600><c> video</c><00:08:58.959><c> okay</c><00:08:59.279><c> this</c><00:08:59.399><c> video</c><00:08:59.600><c> is</c><00:08:59.680><c> under</c>

00:08:59.910 --> 00:08:59.920 align:start position:0%
that this video okay this video is under
 

00:08:59.920 --> 00:09:02.750 align:start position:0%
that this video okay this video is under
a<00:09:00.040><c> minute</c><00:09:00.640><c> the</c><00:09:00.800><c> curtain</c><00:09:01.160><c> change</c><00:09:01.640><c> color</c><00:09:02.079><c> once</c>

00:09:02.750 --> 00:09:02.760 align:start position:0%
a minute the curtain change color once
 

00:09:02.760 --> 00:09:08.069 align:start position:0%
a minute the curtain change color once
in<00:09:03.000><c> there</c><00:09:03.800><c> okay</c><00:09:04.320><c> let's</c><00:09:04.680><c> try</c><00:09:05.680><c> one</c><00:09:06.480><c> more</c><00:09:07.480><c> what</c>

00:09:08.069 --> 00:09:08.079 align:start position:0%
in there okay let's try one more what
 

00:09:08.079 --> 00:09:12.870 align:start position:0%
in there okay let's try one more what
animal<00:09:09.040><c> walks</c><00:09:10.120><c> through</c><00:09:11.120><c> the</c><00:09:11.440><c> sea</c><00:09:12.440><c> in</c><00:09:12.720><c> the</c>

00:09:12.870 --> 00:09:12.880 align:start position:0%
animal walks through the sea in the
 

00:09:12.880 --> 00:09:14.910 align:start position:0%
animal walks through the sea in the
middle<00:09:13.800><c> okay</c><00:09:13.959><c> let's</c><00:09:14.120><c> see</c><00:09:14.240><c> if</c><00:09:14.360><c> it's</c><00:09:14.560><c> able</c><00:09:14.760><c> to</c>

00:09:14.910 --> 00:09:14.920 align:start position:0%
middle okay let's see if it's able to
 

00:09:14.920 --> 00:09:17.550 align:start position:0%
middle okay let's see if it's able to
work<00:09:15.160><c> out</c><00:09:15.399><c> that's</c><00:09:15.519><c> a</c><00:09:15.720><c> gorilla</c><00:09:16.399><c> in</c><00:09:16.600><c> there</c><00:09:17.279><c> so</c>

00:09:17.550 --> 00:09:17.560 align:start position:0%
work out that's a gorilla in there so
 

00:09:17.560 --> 00:09:20.269 align:start position:0%
work out that's a gorilla in there so
I've<00:09:17.760><c> basically</c><00:09:18.360><c> cut</c><00:09:18.560><c> out</c><00:09:18.959><c> any</c><00:09:19.240><c> of</c><00:09:19.519><c> the</c><00:09:19.880><c> bits</c>

00:09:20.269 --> 00:09:20.279 align:start position:0%
I've basically cut out any of the bits
 

00:09:20.279 --> 00:09:22.430 align:start position:0%
I've basically cut out any of the bits
that<00:09:20.399><c> it</c><00:09:20.560><c> has</c><00:09:20.720><c> got</c><00:09:20.880><c> the</c><00:09:21.040><c> answers</c><00:09:21.480><c> to</c><00:09:21.800><c> this</c><00:09:22.240><c> so</c>

00:09:22.430 --> 00:09:22.440 align:start position:0%
that it has got the answers to this so
 

00:09:22.440 --> 00:09:24.550 align:start position:0%
that it has got the answers to this so
we've<00:09:22.640><c> just</c><00:09:22.760><c> got</c><00:09:22.959><c> the</c><00:09:23.200><c> actual</c><00:09:23.600><c> thing</c><00:09:23.880><c> that</c><00:09:24.320><c> it</c>

00:09:24.550 --> 00:09:24.560 align:start position:0%
we've just got the actual thing that it
 

00:09:24.560 --> 00:09:26.829 align:start position:0%
we've just got the actual thing that it
Sayes<00:09:25.160><c> says</c><00:09:25.440><c> a</c><00:09:25.600><c> gorilla</c><00:09:26.240><c> okay</c><00:09:26.399><c> so</c><00:09:26.600><c> that's</c>

00:09:26.829 --> 00:09:26.839 align:start position:0%
Sayes says a gorilla okay so that's
 

00:09:26.839 --> 00:09:29.150 align:start position:0%
Sayes says a gorilla okay so that's
pretty<00:09:27.079><c> cool</c><00:09:27.440><c> that</c><00:09:27.600><c> it's</c><00:09:27.800><c> been</c><00:09:28.000><c> able</c><00:09:28.279><c> to</c><00:09:28.800><c> go</c>

00:09:29.150 --> 00:09:29.160 align:start position:0%
pretty cool that it's been able to go
 

00:09:29.160 --> 00:09:32.069 align:start position:0%
pretty cool that it's been able to go
through<00:09:29.399><c> this</c><00:09:29.640><c> video</c><00:09:30.560><c> spot</c><00:09:30.880><c> the</c><00:09:31.040><c> gorilla</c><00:09:31.600><c> spot</c>

00:09:32.069 --> 00:09:32.079 align:start position:0%
through this video spot the gorilla spot
 

00:09:32.079 --> 00:09:34.470 align:start position:0%
through this video spot the gorilla spot
the<00:09:32.240><c> curtain</c><00:09:32.839><c> changes</c><00:09:33.680><c> and</c><00:09:33.800><c> get</c><00:09:34.000><c> pretty</c><00:09:34.240><c> close</c>

00:09:34.470 --> 00:09:34.480 align:start position:0%
the curtain changes and get pretty close
 

00:09:34.480 --> 00:09:36.509 align:start position:0%
the curtain changes and get pretty close
on<00:09:34.680><c> Counting</c><00:09:35.120><c> the</c><00:09:35.279><c> number</c><00:09:35.560><c> of</c><00:09:35.839><c> passes</c><00:09:36.440><c> that</c>

00:09:36.509 --> 00:09:36.519 align:start position:0%
on Counting the number of passes that
 

00:09:36.519 --> 00:09:38.470 align:start position:0%
on Counting the number of passes that
are<00:09:36.680><c> in</c><00:09:36.920><c> there</c><00:09:37.600><c> all</c><00:09:37.720><c> right</c><00:09:37.839><c> let's</c><00:09:38.040><c> jump</c><00:09:38.200><c> on</c><00:09:38.320><c> and</c>

00:09:38.470 --> 00:09:38.480 align:start position:0%
are in there all right let's jump on and
 

00:09:38.480 --> 00:09:41.829 align:start position:0%
are in there all right let's jump on and
have<00:09:39.079><c> look</c><00:09:39.200><c> at</c><00:09:39.399><c> one</c><00:09:39.600><c> more</c><00:09:40.360><c> example</c><00:09:41.360><c> okay</c><00:09:41.640><c> this</c>

00:09:41.829 --> 00:09:41.839 align:start position:0%
have look at one more example okay this
 

00:09:41.839 --> 00:09:44.949 align:start position:0%
have look at one more example okay this
time<00:09:42.160><c> I've</c><00:09:42.399><c> basically</c><00:09:42.959><c> uploaded</c><00:09:43.800><c> a</c><00:09:44.160><c> video</c><00:09:44.680><c> of</c>

00:09:44.949 --> 00:09:44.959 align:start position:0%
time I've basically uploaded a video of
 

00:09:44.959 --> 00:09:48.870 align:start position:0%
time I've basically uploaded a video of
Andrew<00:09:45.800><c> talking</c><00:09:46.040><c> about</c><00:09:46.360><c> opportunities</c><00:09:47.240><c> in</c><00:09:47.880><c> AI</c>

00:09:48.870 --> 00:09:48.880 align:start position:0%
Andrew talking about opportunities in AI
 

00:09:48.880 --> 00:09:51.949 align:start position:0%
Andrew talking about opportunities in AI
in<00:09:49.560><c> 2023</c><00:09:50.560><c> I</c><00:09:50.680><c> picked</c><00:09:50.959><c> this</c><00:09:51.120><c> video</c><00:09:51.480><c> just</c><00:09:51.680><c> because</c>

00:09:51.949 --> 00:09:51.959 align:start position:0%
in 2023 I picked this video just because
 

00:09:51.959 --> 00:09:54.389 align:start position:0%
in 2023 I picked this video just because
it's<00:09:52.160><c> got</c><00:09:52.480><c> quite</c><00:09:52.640><c> a</c><00:09:52.720><c> number</c><00:09:52.920><c> of</c><00:09:53.240><c> slides</c><00:09:53.760><c> in</c><00:09:53.920><c> it</c>

00:09:54.389 --> 00:09:54.399 align:start position:0%
it's got quite a number of slides in it
 

00:09:54.399 --> 00:09:55.710 align:start position:0%
it's got quite a number of slides in it
so<00:09:54.560><c> it's</c><00:09:54.680><c> really</c><00:09:54.839><c> important</c><00:09:55.160><c> that</c><00:09:55.279><c> we</c>

00:09:55.710 --> 00:09:55.720 align:start position:0%
so it's really important that we
 

00:09:55.720 --> 00:09:57.350 align:start position:0%
so it's really important that we
understand<00:09:55.880><c> that</c><00:09:56.040><c> when</c><00:09:56.200><c> we</c><00:09:56.440><c> upload</c><00:09:57.079><c> these</c>

00:09:57.350 --> 00:09:57.360 align:start position:0%
understand that when we upload these
 

00:09:57.360 --> 00:10:00.389 align:start position:0%
understand that when we upload these
things<00:09:58.120><c> it's</c><00:09:58.279><c> not</c><00:09:58.480><c> actually</c><00:09:58.720><c> using</c><00:09:59.440><c> the</c><00:09:59.720><c> audio</c>

00:10:00.389 --> 00:10:00.399 align:start position:0%
things it's not actually using the audio
 

00:10:00.399 --> 00:10:03.630 align:start position:0%
things it's not actually using the audio
here<00:10:00.920><c> it's</c><00:10:01.160><c> just</c><00:10:01.360><c> using</c><00:10:01.880><c> the</c><00:10:02.160><c> video</c><00:10:03.160><c> so</c><00:10:03.480><c> while</c>

00:10:03.630 --> 00:10:03.640 align:start position:0%
here it's just using the video so while
 

00:10:03.640 --> 00:10:05.750 align:start position:0%
here it's just using the video so while
it's<00:10:03.800><c> tempting</c><00:10:04.200><c> to</c><00:10:04.399><c> basically</c><00:10:04.959><c> get</c><00:10:05.440><c> some</c>

00:10:05.750 --> 00:10:05.760 align:start position:0%
it's tempting to basically get some
 

00:10:05.760 --> 00:10:09.190 align:start position:0%
it's tempting to basically get some
videos<00:10:06.440><c> of</c><00:10:06.800><c> him</c><00:10:07.640><c> as</c><00:10:07.800><c> well</c><00:10:08.120><c> in</c><00:10:08.360><c> here</c><00:10:09.079><c> the</c>

00:10:09.190 --> 00:10:09.200 align:start position:0%
videos of him as well in here the
 

00:10:09.200 --> 00:10:10.949 align:start position:0%
videos of him as well in here the
challenge<00:10:09.640><c> is</c><00:10:09.839><c> that</c><00:10:10.120><c> that</c><00:10:10.200><c> it</c><00:10:10.320><c> doesn't</c><00:10:10.560><c> know</c>

00:10:10.949 --> 00:10:10.959 align:start position:0%
challenge is that that it doesn't know
 

00:10:10.959 --> 00:10:12.670 align:start position:0%
challenge is that that it doesn't know
exactly<00:10:11.519><c> what</c><00:10:11.760><c> it's</c><00:10:11.920><c> saying</c><00:10:12.240><c> so</c><00:10:12.399><c> it's</c><00:10:12.519><c> not</c>

00:10:12.670 --> 00:10:12.680 align:start position:0%
exactly what it's saying so it's not
 

00:10:12.680 --> 00:10:14.310 align:start position:0%
exactly what it's saying so it's not
actually<00:10:13.000><c> processing</c><00:10:13.440><c> the</c><00:10:13.600><c> audio</c><00:10:14.000><c> in</c><00:10:14.160><c> here</c>

00:10:14.310 --> 00:10:14.320 align:start position:0%
actually processing the audio in here
 

00:10:14.320 --> 00:10:16.389 align:start position:0%
actually processing the audio in here
it's<00:10:14.440><c> just</c><00:10:14.640><c> processing</c><00:10:15.120><c> the</c><00:10:15.320><c> video</c><00:10:15.959><c> as</c><00:10:16.079><c> we</c><00:10:16.200><c> go</c>

00:10:16.389 --> 00:10:16.399 align:start position:0%
it's just processing the video as we go
 

00:10:16.399 --> 00:10:18.550 align:start position:0%
it's just processing the video as we go
through<00:10:16.680><c> this</c><00:10:17.360><c> okay</c><00:10:17.519><c> so</c><00:10:17.640><c> I've</c><00:10:17.800><c> got</c><00:10:17.920><c> the</c><00:10:18.079><c> video</c>

00:10:18.550 --> 00:10:18.560 align:start position:0%
through this okay so I've got the video
 

00:10:18.560 --> 00:10:20.190 align:start position:0%
through this okay so I've got the video
uploaded<00:10:19.160><c> you</c><00:10:19.279><c> can</c><00:10:19.440><c> see</c><00:10:19.800><c> that</c><00:10:20.040><c> it's</c>

00:10:20.190 --> 00:10:20.200 align:start position:0%
uploaded you can see that it's
 

00:10:20.200 --> 00:10:21.990 align:start position:0%
uploaded you can see that it's
reasonably<00:10:20.720><c> long</c><00:10:21.079><c> and</c><00:10:21.200><c> we're</c><00:10:21.399><c> actually</c><00:10:21.600><c> using</c>

00:10:21.990 --> 00:10:22.000 align:start position:0%
reasonably long and we're actually using
 

00:10:22.000 --> 00:10:24.069 align:start position:0%
reasonably long and we're actually using
quite<00:10:22.200><c> a</c><00:10:22.279><c> lot</c><00:10:22.480><c> of</c><00:10:22.680><c> tokens</c><00:10:23.120><c> in</c><00:10:23.320><c> here</c><00:10:23.880><c> I'm</c><00:10:23.959><c> going</c>

00:10:24.069 --> 00:10:24.079 align:start position:0%
quite a lot of tokens in here I'm going
 

00:10:24.079 --> 00:10:26.550 align:start position:0%
quite a lot of tokens in here I'm going
to<00:10:24.200><c> get</c><00:10:24.360><c> that</c><00:10:24.760><c> running</c><00:10:25.720><c> and</c><00:10:26.040><c> so</c><00:10:26.320><c> the</c><00:10:26.480><c> The</c>

00:10:26.550 --> 00:10:26.560 align:start position:0%
to get that running and so the The
 

00:10:26.560 --> 00:10:27.910 align:start position:0%
to get that running and so the The
Prompt<00:10:26.839><c> that</c><00:10:26.959><c> I'm</c><00:10:27.079><c> using</c><00:10:27.360><c> here</c><00:10:27.480><c> is</c><00:10:27.720><c> please</c>

00:10:27.910 --> 00:10:27.920 align:start position:0%
Prompt that I'm using here is please
 

00:10:27.920 --> 00:10:29.910 align:start position:0%
Prompt that I'm using here is please
break<00:10:28.200><c> down</c><00:10:28.399><c> this</c><00:10:28.560><c> video</c><00:10:29.160><c> make</c><00:10:29.279><c> a</c><00:10:29.440><c> list</c><00:10:29.640><c> of</c><00:10:29.800><c> the</c>

00:10:29.910 --> 00:10:29.920 align:start position:0%
break down this video make a list of the
 

00:10:29.920 --> 00:10:32.829 align:start position:0%
break down this video make a list of the
slides<00:10:30.440><c> presented</c><00:10:31.000><c> with</c><00:10:31.320><c> details</c><00:10:32.279><c> this</c><00:10:32.440><c> video</c>

00:10:32.829 --> 00:10:32.839 align:start position:0%
slides presented with details this video
 

00:10:32.839 --> 00:10:35.829 align:start position:0%
slides presented with details this video
basically<00:10:33.680><c> is</c><00:10:33.800><c> a</c><00:10:34.000><c> presentation</c><00:10:34.800><c> that</c><00:10:35.000><c> Andrew</c>

00:10:35.829 --> 00:10:35.839 align:start position:0%
basically is a presentation that Andrew
 

00:10:35.839 --> 00:10:38.430 align:start position:0%
basically is a presentation that Andrew
gave<00:10:36.519><c> I</c><00:10:36.639><c> guess</c><00:10:36.800><c> about</c><00:10:37.079><c> a</c><00:10:37.200><c> year</c><00:10:37.440><c> ago</c><00:10:38.160><c> and</c><00:10:38.279><c> he's</c>

00:10:38.430 --> 00:10:38.440 align:start position:0%
gave I guess about a year ago and he's
 

00:10:38.440 --> 00:10:41.550 align:start position:0%
gave I guess about a year ago and he's
got<00:10:38.680><c> quite</c><00:10:38.920><c> detailed</c><00:10:39.519><c> slides</c><00:10:39.959><c> in</c><00:10:40.240><c> here</c><00:10:40.959><c> about</c>

00:10:41.550 --> 00:10:41.560 align:start position:0%
got quite detailed slides in here about
 

00:10:41.560 --> 00:10:43.389 align:start position:0%
got quite detailed slides in here about
different<00:10:42.040><c> things</c><00:10:42.240><c> in</c><00:10:42.480><c> Ai</c><00:10:42.880><c> and</c><00:10:43.079><c> different</c>

00:10:43.389 --> 00:10:43.399 align:start position:0%
different things in Ai and different
 

00:10:43.399 --> 00:10:45.550 align:start position:0%
different things in Ai and different
opportunities<00:10:44.079><c> in</c><00:10:44.279><c> AI</c><00:10:44.959><c> so</c><00:10:45.160><c> I'm</c><00:10:45.320><c> really</c>

00:10:45.550 --> 00:10:45.560 align:start position:0%
opportunities in AI so I'm really
 

00:10:45.560 --> 00:10:48.310 align:start position:0%
opportunities in AI so I'm really
curious<00:10:45.920><c> to</c><00:10:46.120><c> see</c><00:10:46.680><c> okay</c><00:10:46.920><c> how</c><00:10:47.160><c> well</c><00:10:47.560><c> can</c><00:10:47.720><c> it</c><00:10:48.000><c> go</c>

00:10:48.310 --> 00:10:48.320 align:start position:0%
curious to see okay how well can it go
 

00:10:48.320 --> 00:10:50.910 align:start position:0%
curious to see okay how well can it go
through<00:10:49.240><c> and</c><00:10:49.440><c> extract</c><00:10:49.880><c> the</c><00:10:50.040><c> information</c><00:10:50.680><c> from</c>

00:10:50.910 --> 00:10:50.920 align:start position:0%
through and extract the information from
 

00:10:50.920 --> 00:10:54.350 align:start position:0%
through and extract the information from
the<00:10:51.120><c> slides</c><00:10:52.079><c> and</c><00:10:52.240><c> be</c><00:10:52.399><c> able</c><00:10:52.600><c> to</c><00:10:52.839><c> run</c><00:10:53.279><c> that</c><00:10:53.959><c> so</c>

00:10:54.350 --> 00:10:54.360 align:start position:0%
the slides and be able to run that so
 

00:10:54.360 --> 00:10:55.949 align:start position:0%
the slides and be able to run that so
again<00:10:54.600><c> we're</c><00:10:54.800><c> coming</c><00:10:55.079><c> up</c><00:10:55.279><c> to</c><00:10:55.519><c> the</c><00:10:55.639><c> sort</c><00:10:55.800><c> of</c>

00:10:55.949 --> 00:10:55.959 align:start position:0%
again we're coming up to the sort of
 

00:10:55.959 --> 00:10:58.629 align:start position:0%
again we're coming up to the sort of
50-second<00:10:56.519><c> Mark</c><00:10:57.519><c> and</c><00:10:57.639><c> it</c><00:10:57.800><c> does</c><00:10:57.959><c> seem</c><00:10:58.160><c> like</c><00:10:58.320><c> for</c>

00:10:58.629 --> 00:10:58.639 align:start position:0%
50-second Mark and it does seem like for
 

00:10:58.639 --> 00:11:00.990 align:start position:0%
50-second Mark and it does seem like for
half<00:10:58.800><c> a</c><00:10:59.079><c> million</c><00:10:59.399><c> tokens</c><00:11:00.079><c> this</c><00:11:00.200><c> is</c><00:11:00.680><c> roughly</c>

00:11:00.990 --> 00:11:01.000 align:start position:0%
half a million tokens this is roughly
 

00:11:01.000 --> 00:11:03.110 align:start position:0%
half a million tokens this is roughly
where<00:11:01.160><c> it</c><00:11:01.279><c> would</c><00:11:01.480><c> start</c><00:11:01.920><c> generating</c><00:11:02.920><c> at</c>

00:11:03.110 --> 00:11:03.120 align:start position:0%
where it would start generating at
 

00:11:03.120 --> 00:11:05.670 align:start position:0%
where it would start generating at
around<00:11:03.519><c> this</c><00:11:03.760><c> kind</c><00:11:03.920><c> of</c><00:11:04.120><c> Point</c><00:11:04.440><c> 50</c><00:11:04.839><c> 60</c><00:11:05.240><c> seconds</c>

00:11:05.670 --> 00:11:05.680 align:start position:0%
around this kind of Point 50 60 seconds
 

00:11:05.680 --> 00:11:08.269 align:start position:0%
around this kind of Point 50 60 seconds
in<00:11:06.519><c> okay</c><00:11:06.839><c> so</c><00:11:07.279><c> this</c><00:11:07.360><c> is</c><00:11:07.519><c> taking</c><00:11:07.800><c> a</c><00:11:07.920><c> little</c><00:11:08.079><c> bit</c>

00:11:08.269 --> 00:11:08.279 align:start position:0%
in okay so this is taking a little bit
 

00:11:08.279 --> 00:11:09.870 align:start position:0%
in okay so this is taking a little bit
longer<00:11:08.600><c> than</c><00:11:08.720><c> I</c><00:11:08.880><c> thought</c><00:11:09.120><c> we</c><00:11:09.240><c> can</c><00:11:09.320><c> see</c><00:11:09.560><c> slide</c>

00:11:09.870 --> 00:11:09.880 align:start position:0%
longer than I thought we can see slide
 

00:11:09.880 --> 00:11:12.190 align:start position:0%
longer than I thought we can see slide
one<00:11:10.360><c> title</c><00:11:10.800><c> Slide</c><00:11:11.160><c> the</c><00:11:11.240><c> slide</c><00:11:11.519><c> introduces</c><00:11:12.000><c> the</c>

00:11:12.190 --> 00:11:12.200 align:start position:0%
one title Slide the slide introduces the
 

00:11:12.200 --> 00:11:14.389 align:start position:0%
one title Slide the slide introduces the
presentation<00:11:12.760><c> and</c><00:11:12.920><c> the</c><00:11:13.040><c> speaker</c><00:11:13.399><c> Andrew</c>

00:11:14.389 --> 00:11:14.399 align:start position:0%
presentation and the speaker Andrew
 

00:11:14.399 --> 00:11:17.350 align:start position:0%
presentation and the speaker Andrew
slide<00:11:14.680><c> two</c><00:11:15.160><c> AI</c><00:11:15.480><c> is</c><00:11:15.639><c> the</c><00:11:15.760><c> new</c><00:11:16.360><c> electricity</c>

00:11:17.350 --> 00:11:17.360 align:start position:0%
slide two AI is the new electricity
 

00:11:17.360 --> 00:11:20.710 align:start position:0%
slide two AI is the new electricity
slide<00:11:17.959><c> three</c><00:11:18.600><c> technology</c><00:11:19.480><c> landscape</c><00:11:20.480><c> slide</c>

00:11:20.710 --> 00:11:20.720 align:start position:0%
slide three technology landscape slide
 

00:11:20.720 --> 00:11:23.269 align:start position:0%
slide three technology landscape slide
four<00:11:21.000><c> supervisor</c><00:11:21.560><c> learning</c><00:11:22.040><c> labeling</c><00:11:22.639><c> things</c>

00:11:23.269 --> 00:11:23.279 align:start position:0%
four supervisor learning labeling things
 

00:11:23.279 --> 00:11:26.190 align:start position:0%
four supervisor learning labeling things
so<00:11:23.560><c> far</c><00:11:23.959><c> these</c><00:11:24.120><c> are</c><00:11:24.320><c> looking</c><00:11:24.880><c> pretty</c><00:11:25.279><c> good</c><00:11:25.720><c> for</c>

00:11:26.190 --> 00:11:26.200 align:start position:0%
so far these are looking pretty good for
 

00:11:26.200 --> 00:11:28.629 align:start position:0%
so far these are looking pretty good for
what<00:11:26.560><c> he's</c><00:11:26.839><c> got</c><00:11:27.120><c> in</c><00:11:27.399><c> here</c><00:11:28.160><c> I'm</c><00:11:28.279><c> just</c><00:11:28.399><c> looking</c>

00:11:28.629 --> 00:11:28.639 align:start position:0%
what he's got in here I'm just looking
 

00:11:28.639 --> 00:11:31.870 align:start position:0%
what he's got in here I'm just looking
at<00:11:28.760><c> the</c><00:11:29.079><c> chapters</c><00:11:29.880><c> on</c><00:11:30.160><c> YouTube</c><00:11:30.720><c> for</c><00:11:31.160><c> this</c><00:11:31.760><c> and</c>

00:11:31.870 --> 00:11:31.880 align:start position:0%
at the chapters on YouTube for this and
 

00:11:31.880 --> 00:11:34.350 align:start position:0%
at the chapters on YouTube for this and
it<00:11:32.040><c> does</c><00:11:32.240><c> seem</c><00:11:32.519><c> to</c><00:11:32.680><c> be</c><00:11:33.279><c> quite</c><00:11:33.600><c> consistent</c><00:11:34.200><c> that</c>

00:11:34.350 --> 00:11:34.360 align:start position:0%
it does seem to be quite consistent that
 

00:11:34.360 --> 00:11:36.750 align:start position:0%
it does seem to be quite consistent that
these<00:11:34.519><c> are</c><00:11:34.680><c> the</c><00:11:34.839><c> things</c><00:11:35.200><c> that</c><00:11:35.440><c> he</c><00:11:36.040><c> talks</c><00:11:36.360><c> about</c>

00:11:36.750 --> 00:11:36.760 align:start position:0%
these are the things that he talks about
 

00:11:36.760 --> 00:11:38.470 align:start position:0%
these are the things that he talks about
example<00:11:37.160><c> restaurant</c><00:11:37.519><c> reviews</c><00:11:37.800><c> and</c><00:11:37.959><c> sentiment</c>

00:11:38.470 --> 00:11:38.480 align:start position:0%
example restaurant reviews and sentiment
 

00:11:38.480 --> 00:11:41.310 align:start position:0%
example restaurant reviews and sentiment
tracking<00:11:39.480><c> prompting</c><00:11:39.920><c> is</c><00:11:40.160><c> revolutionizing</c><00:11:41.040><c> a</c>

00:11:41.310 --> 00:11:41.320 align:start position:0%
tracking prompting is revolutionizing a
 

00:11:41.320 --> 00:11:43.629 align:start position:0%
tracking prompting is revolutionizing a
AI<00:11:41.880><c> application</c><00:11:42.360><c> development</c><00:11:42.880><c> let's</c><00:11:43.360><c> go</c>

00:11:43.629 --> 00:11:43.639 align:start position:0%
AI application development let's go
 

00:11:43.639 --> 00:11:46.110 align:start position:0%
AI application development let's go
through<00:11:43.880><c> and</c><00:11:44.040><c> just</c><00:11:44.320><c> accept</c><00:11:44.839><c> this</c><00:11:45.680><c> so</c><00:11:45.880><c> that</c><00:11:46.040><c> we</c>

00:11:46.110 --> 00:11:46.120 align:start position:0%
through and just accept this so that we
 

00:11:46.120 --> 00:11:48.470 align:start position:0%
through and just accept this so that we
can<00:11:46.320><c> actually</c><00:11:46.560><c> read</c><00:11:46.760><c> it</c><00:11:46.920><c> a</c><00:11:47.040><c> bit</c><00:11:47.279><c> easier</c><00:11:48.240><c> so</c>

00:11:48.470 --> 00:11:48.480 align:start position:0%
can actually read it a bit easier so
 

00:11:48.480 --> 00:11:51.150 align:start position:0%
can actually read it a bit easier so
it's<00:11:48.680><c> done</c><00:11:48.839><c> a</c><00:11:49.079><c> pretty</c><00:11:49.399><c> good</c><00:11:49.720><c> job</c><00:11:50.240><c> in</c><00:11:50.480><c> about</c><00:11:50.800><c> two</c>

00:11:51.150 --> 00:11:51.160 align:start position:0%
it's done a pretty good job in about two
 

00:11:51.160 --> 00:11:54.670 align:start position:0%
it's done a pretty good job in about two
minutes<00:11:51.760><c> of</c><00:11:52.000><c> going</c><00:11:52.399><c> through</c><00:11:53.000><c> that</c><00:11:53.360><c> 36</c><00:11:53.959><c> minute</c>

00:11:54.670 --> 00:11:54.680 align:start position:0%
minutes of going through that 36 minute
 

00:11:54.680 --> 00:11:56.870 align:start position:0%
minutes of going through that 36 minute
presentation<00:11:55.680><c> working</c><00:11:56.000><c> out</c><00:11:56.279><c> what</c><00:11:56.440><c> the</c><00:11:56.600><c> key</c>

00:11:56.870 --> 00:11:56.880 align:start position:0%
presentation working out what the key
 

00:11:56.880 --> 00:11:59.310 align:start position:0%
presentation working out what the key
slides<00:11:57.399><c> were</c><00:11:58.120><c> and</c><00:11:58.279><c> then</c><00:11:58.519><c> giving</c><00:11:58.720><c> us</c><00:11:58.959><c> a</c><00:11:59.079><c> little</c>

00:11:59.310 --> 00:11:59.320 align:start position:0%
slides were and then giving us a little
 

00:11:59.320 --> 00:12:02.230 align:start position:0%
slides were and then giving us a little
description<00:12:00.160><c> of</c><00:12:00.440><c> each</c><00:12:00.639><c> of</c><00:12:00.880><c> the</c><00:12:01.079><c> slides</c><00:12:01.800><c> and</c>

00:12:02.230 --> 00:12:02.240 align:start position:0%
description of each of the slides and
 

00:12:02.240 --> 00:12:04.150 align:start position:0%
description of each of the slides and
that<00:12:02.519><c> section</c><00:12:02.880><c> of</c><00:12:03.040><c> it</c><00:12:03.440><c> now</c><00:12:03.600><c> remember</c><00:12:04.000><c> it</c>

00:12:04.150 --> 00:12:04.160 align:start position:0%
that section of it now remember it
 

00:12:04.160 --> 00:12:07.150 align:start position:0%
that section of it now remember it
doesn't<00:12:04.720><c> have</c><00:12:05.120><c> the</c><00:12:05.920><c> audio</c><00:12:06.639><c> what</c><00:12:06.720><c> I</c><00:12:06.920><c> could</c>

00:12:07.150 --> 00:12:07.160 align:start position:0%
doesn't have the audio what I could
 

00:12:07.160 --> 00:12:09.389 align:start position:0%
doesn't have the audio what I could
probably<00:12:07.399><c> do</c><00:12:07.600><c> is</c><00:12:07.800><c> also</c><00:12:08.120><c> throw</c><00:12:08.399><c> in</c><00:12:08.839><c> the</c>

00:12:09.389 --> 00:12:09.399 align:start position:0%
probably do is also throw in the
 

00:12:09.399 --> 00:12:12.110 align:start position:0%
probably do is also throw in the
transcript<00:12:10.120><c> of</c><00:12:10.320><c> what</c><00:12:10.519><c> he</c><00:12:10.720><c> said</c><00:12:11.320><c> and</c><00:12:11.519><c> then</c><00:12:12.000><c> get</c>

00:12:12.110 --> 00:12:12.120 align:start position:0%
transcript of what he said and then get
 

00:12:12.120 --> 00:12:13.949 align:start position:0%
transcript of what he said and then get
it<00:12:12.240><c> to</c><00:12:12.440><c> use</c><00:12:12.959><c> the</c><00:12:13.120><c> two</c><00:12:13.279><c> of</c><00:12:13.440><c> these</c><00:12:13.639><c> things</c>

00:12:13.949 --> 00:12:13.959 align:start position:0%
it to use the two of these things
 

00:12:13.959 --> 00:12:16.150 align:start position:0%
it to use the two of these things
combined<00:12:14.920><c> for</c><00:12:15.120><c> that</c><00:12:15.279><c> so</c><00:12:15.399><c> maybe</c><00:12:15.639><c> I'll</c><00:12:15.800><c> do</c><00:12:16.040><c> that</c>

00:12:16.150 --> 00:12:16.160 align:start position:0%
combined for that so maybe I'll do that
 

00:12:16.160 --> 00:12:19.150 align:start position:0%
combined for that so maybe I'll do that
in<00:12:16.279><c> a</c><00:12:16.440><c> future</c><00:12:16.880><c> video</c><00:12:17.639><c> for</c><00:12:18.000><c> this</c><00:12:18.600><c> anyway</c><00:12:18.920><c> so</c>

00:12:19.150 --> 00:12:19.160 align:start position:0%
in a future video for this anyway so
 

00:12:19.160 --> 00:12:21.590 align:start position:0%
in a future video for this anyway so
just<00:12:19.279><c> to</c><00:12:19.480><c> finish</c><00:12:19.800><c> up</c><00:12:20.120><c> this</c><00:12:20.360><c> video</c><00:12:21.199><c> here</c><00:12:21.519><c> this</c>

00:12:21.590 --> 00:12:21.600 align:start position:0%
just to finish up this video here this
 

00:12:21.600 --> 00:12:25.230 align:start position:0%
just to finish up this video here this
is<00:12:21.800><c> just</c><00:12:21.959><c> a</c><00:12:22.320><c> first</c><00:12:23.279><c> Hands-On</c><00:12:24.279><c> with</c><00:12:24.600><c> the</c><00:12:24.760><c> Gemini</c>

00:12:25.230 --> 00:12:25.240 align:start position:0%
is just a first Hands-On with the Gemini
 

00:12:25.240 --> 00:12:28.750 align:start position:0%
is just a first Hands-On with the Gemini
1.5<00:12:26.040><c> pro</c><00:12:26.360><c> model</c><00:12:27.360><c> it</c><00:12:27.480><c> gives</c><00:12:27.680><c> you</c><00:12:27.800><c> a</c><00:12:27.959><c> taste</c><00:12:28.279><c> of</c>

00:12:28.750 --> 00:12:28.760 align:start position:0%
1.5 pro model it gives you a taste of
 

00:12:28.760 --> 00:12:30.949 align:start position:0%
1.5 pro model it gives you a taste of
what<00:12:28.959><c> what's</c><00:12:29.199><c> coming</c><00:12:30.000><c> from</c><00:12:30.279><c> Google</c><00:12:30.800><c> it's</c>

00:12:30.949 --> 00:12:30.959 align:start position:0%
what what's coming from Google it's
 

00:12:30.959 --> 00:12:33.350 align:start position:0%
what what's coming from Google it's
still<00:12:31.240><c> a</c><00:12:31.320><c> little</c><00:12:31.560><c> buggy</c><00:12:32.160><c> at</c><00:12:32.440><c> times</c><00:12:33.120><c> I</c><00:12:33.199><c> think</c>

00:12:33.350 --> 00:12:33.360 align:start position:0%
still a little buggy at times I think
 

00:12:33.360 --> 00:12:35.790 align:start position:0%
still a little buggy at times I think
the<00:12:33.519><c> UI</c><00:12:34.040><c> is</c><00:12:34.320><c> still</c><00:12:35.000><c> they're</c><00:12:35.160><c> still</c><00:12:35.360><c> working</c><00:12:35.680><c> on</c>

00:12:35.790 --> 00:12:35.800 align:start position:0%
the UI is still they're still working on
 

00:12:35.800 --> 00:12:37.750 align:start position:0%
the UI is still they're still working on
it<00:12:35.920><c> and</c><00:12:36.040><c> stuff</c><00:12:36.279><c> like</c><00:12:36.480><c> that</c><00:12:36.760><c> but</c><00:12:37.240><c> my</c><00:12:37.440><c> guess</c><00:12:37.639><c> is</c>

00:12:37.750 --> 00:12:37.760 align:start position:0%
it and stuff like that but my guess is
 

00:12:37.760 --> 00:12:39.269 align:start position:0%
it and stuff like that but my guess is
that<00:12:37.920><c> this</c><00:12:38.000><c> will</c><00:12:38.160><c> be</c><00:12:38.360><c> out</c><00:12:38.639><c> in</c><00:12:38.760><c> the</c><00:12:38.880><c> next</c><00:12:39.079><c> few</c>

00:12:39.269 --> 00:12:39.279 align:start position:0%
that this will be out in the next few
 

00:12:39.279 --> 00:12:41.030 align:start position:0%
that this will be out in the next few
weeks<00:12:39.480><c> for</c><00:12:39.680><c> people</c><00:12:39.959><c> to</c><00:12:40.160><c> get</c><00:12:40.320><c> their</c><00:12:40.480><c> hands</c><00:12:40.720><c> on</c>

00:12:41.030 --> 00:12:41.040 align:start position:0%
weeks for people to get their hands on
 

00:12:41.040 --> 00:12:43.550 align:start position:0%
weeks for people to get their hands on
to<00:12:41.240><c> start</c><00:12:41.480><c> trying</c><00:12:42.480><c> and</c><00:12:42.639><c> start</c><00:12:42.880><c> using</c><00:12:43.279><c> for</c>

00:12:43.550 --> 00:12:43.560 align:start position:0%
to start trying and start using for
 

00:12:43.560 --> 00:12:45.870 align:start position:0%
to start trying and start using for
various<00:12:44.000><c> different</c><00:12:44.639><c> applications</c><00:12:45.519><c> and</c><00:12:45.680><c> it's</c>

00:12:45.870 --> 00:12:45.880 align:start position:0%
various different applications and it's
 

00:12:45.880 --> 00:12:49.069 align:start position:0%
various different applications and it's
certainly<00:12:46.560><c> a</c><00:12:46.760><c> nice</c><00:12:47.079><c> model</c><00:12:47.480><c> to</c><00:12:47.800><c> have</c><00:12:48.199><c> here</c><00:12:48.839><c> and</c>

00:12:49.069 --> 00:12:49.079 align:start position:0%
certainly a nice model to have here and
 

00:12:49.079 --> 00:12:50.829 align:start position:0%
certainly a nice model to have here and
especially<00:12:49.480><c> when</c><00:12:49.600><c> we've</c><00:12:49.800><c> got</c><00:12:50.000><c> that</c><00:12:50.160><c> 1</c><00:12:50.399><c> million</c>

00:12:50.829 --> 00:12:50.839 align:start position:0%
especially when we've got that 1 million
 

00:12:50.839 --> 00:12:54.030 align:start position:0%
especially when we've got that 1 million
token<00:12:51.440><c> context</c><00:12:52.120><c> window</c><00:12:53.000><c> really</c><00:12:53.320><c> opens</c><00:12:53.680><c> up</c><00:12:53.839><c> a</c>

00:12:54.030 --> 00:12:54.040 align:start position:0%
token context window really opens up a
 

00:12:54.040 --> 00:12:55.990 align:start position:0%
token context window really opens up a
whole<00:12:54.240><c> bunch</c><00:12:54.480><c> of</c><00:12:54.680><c> new</c><00:12:55.000><c> opportunities</c><00:12:55.800><c> for</c>

00:12:55.990 --> 00:12:56.000 align:start position:0%
whole bunch of new opportunities for
 

00:12:56.000 --> 00:12:57.750 align:start position:0%
whole bunch of new opportunities for
things<00:12:56.279><c> that</c><00:12:56.399><c> you</c><00:12:56.519><c> can</c><00:12:56.680><c> do</c><00:12:56.959><c> with</c><00:12:57.160><c> this</c><00:12:57.480><c> anyway</c>

00:12:57.750 --> 00:12:57.760 align:start position:0%
things that you can do with this anyway
 

00:12:57.760 --> 00:12:59.590 align:start position:0%
things that you can do with this anyway
if<00:12:57.800><c> you</c><00:12:57.959><c> got</c><00:12:58.079><c> any</c><00:12:58.240><c> comments</c><00:12:58.560><c> put</c><00:12:59.079><c> below</c>

00:12:59.590 --> 00:12:59.600 align:start position:0%
if you got any comments put below
 

00:12:59.600 --> 00:13:01.110 align:start position:0%
if you got any comments put below
anything<00:12:59.880><c> that</c><00:12:59.959><c> you</c><00:13:00.079><c> would</c><00:13:00.240><c> like</c><00:13:00.600><c> me</c><00:13:00.680><c> to</c><00:13:00.880><c> try</c>

00:13:01.110 --> 00:13:01.120 align:start position:0%
anything that you would like me to try
 

00:13:01.120 --> 00:13:03.350 align:start position:0%
anything that you would like me to try
out<00:13:01.360><c> on</c><00:13:01.480><c> a</c><00:13:01.639><c> follow-up</c><00:13:02.160><c> video</c><00:13:02.920><c> I'm</c><00:13:03.079><c> happy</c><00:13:03.279><c> to</c>

00:13:03.350 --> 00:13:03.360 align:start position:0%
out on a follow-up video I'm happy to
 

00:13:03.360 --> 00:13:05.310 align:start position:0%
out on a follow-up video I'm happy to
make<00:13:03.519><c> a</c><00:13:03.600><c> couple</c><00:13:03.959><c> videos</c><00:13:04.320><c> of</c><00:13:04.519><c> going</c><00:13:04.800><c> through</c>

00:13:05.310 --> 00:13:05.320 align:start position:0%
make a couple videos of going through
 

00:13:05.320 --> 00:13:08.069 align:start position:0%
make a couple videos of going through
gemino<00:13:05.760><c> 1.5</c><00:13:06.480><c> with</c><00:13:06.680><c> the</c><00:13:06.880><c> million</c><00:13:07.160><c> tokens</c>

00:13:08.069 --> 00:13:08.079 align:start position:0%
gemino 1.5 with the million tokens
 

00:13:08.079 --> 00:13:09.870 align:start position:0%
gemino 1.5 with the million tokens
anything<00:13:08.360><c> that</c><00:13:08.560><c> people</c><00:13:08.760><c> want</c><00:13:08.920><c> me</c><00:13:09.040><c> to</c><00:13:09.199><c> try</c><00:13:09.440><c> out</c>

00:13:09.870 --> 00:13:09.880 align:start position:0%
anything that people want me to try out
 

00:13:09.880 --> 00:13:12.030 align:start position:0%
anything that people want me to try out
put<00:13:10.000><c> it</c><00:13:10.120><c> in</c><00:13:10.240><c> the</c><00:13:10.360><c> comments</c><00:13:10.760><c> below</c><00:13:11.600><c> and</c><00:13:11.800><c> I</c><00:13:11.880><c> will</c>

00:13:12.030 --> 00:13:12.040 align:start position:0%
put it in the comments below and I will
 

00:13:12.040 --> 00:13:15.150 align:start position:0%
put it in the comments below and I will
talk<00:13:12.199><c> to</c><00:13:12.279><c> you</c><00:13:12.360><c> in</c><00:13:12.480><c> the</c><00:13:12.600><c> next</c><00:13:12.839><c> video</c><00:13:13.639><c> bye</c><00:13:13.839><c> for</c>

00:13:15.150 --> 00:13:15.160 align:start position:0%
talk to you in the next video bye for
 

00:13:15.160 --> 00:13:18.160 align:start position:0%
talk to you in the next video bye for
now

