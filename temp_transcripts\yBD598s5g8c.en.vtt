WEBVTT
Kind: captions
Language: en

00:00:00.240 --> 00:00:02.790 align:start position:0%
 
Hey<00:00:00.560><c> there,</c><00:00:00.960><c> this</c><00:00:01.120><c> is</c><00:00:01.280><c> Matt</c><00:00:01.760><c> and</c><00:00:02.080><c> I</c><00:00:02.399><c> am</c>

00:00:02.790 --> 00:00:02.800 align:start position:0%
Hey there, this is <PERSON> and I am
 

00:00:02.800 --> 00:00:05.670 align:start position:0%
Hey there, this is <PERSON> and I am
incredibly<00:00:03.439><c> excited</c><00:00:04.160><c> about</c><00:00:04.640><c> <PERSON><PERSON><PERSON>'s</c><00:00:05.440><c> brand</c>

00:00:05.670 --> 00:00:05.680 align:start position:0%
incredibly excited about <PERSON><PERSON><PERSON>'s brand
 

00:00:05.680 --> 00:00:07.310 align:start position:0%
incredibly excited about <PERSON><PERSON><PERSON>'s brand
new

00:00:07.310 --> 00:00:07.320 align:start position:0%
new
 

00:00:07.320 --> 00:00:10.790 align:start position:0%
new
0.9.0<00:00:08.320><c> release.</c><00:00:09.280><c> This</c><00:00:09.679><c> literally</c><00:00:10.400><c> just</c>

00:00:10.790 --> 00:00:10.800 align:start position:0%
0.9.0 release. This literally just
 

00:00:10.800 --> 00:00:14.070 align:start position:0%
0.9.0 release. This literally just
dropped<00:00:11.200><c> within</c><00:00:11.519><c> the</c><00:00:11.759><c> last</c><00:00:12.480><c> day</c><00:00:12.800><c> or</c><00:00:13.040><c> so,</c><00:00:13.679><c> and</c><00:00:13.920><c> I</c>

00:00:14.070 --> 00:00:14.080 align:start position:0%
dropped within the last day or so, and I
 

00:00:14.080 --> 00:00:15.990 align:start position:0%
dropped within the last day or so, and I
had<00:00:14.240><c> to</c><00:00:14.400><c> get</c><00:00:14.559><c> this</c><00:00:14.799><c> video</c><00:00:15.120><c> out</c><00:00:15.360><c> to</c><00:00:15.599><c> you</c>

00:00:15.990 --> 00:00:16.000 align:start position:0%
had to get this video out to you
 

00:00:16.000 --> 00:00:18.189 align:start position:0%
had to get this video out to you
immediately<00:00:16.720><c> because</c><00:00:17.119><c> it</c><00:00:17.440><c> introduces</c>

00:00:18.189 --> 00:00:18.199 align:start position:0%
immediately because it introduces
 

00:00:18.199 --> 00:00:22.310 align:start position:0%
immediately because it introduces
something<00:00:19.560><c> game-changing.</c><00:00:20.680><c> Native</c><00:00:21.680><c> thinking</c>

00:00:22.310 --> 00:00:22.320 align:start position:0%
something game-changing. Native thinking
 

00:00:22.320 --> 00:00:25.109 align:start position:0%
something game-changing. Native thinking
support.<00:00:23.279><c> Now,</c><00:00:23.680><c> Olama</c><00:00:24.240><c> has</c><00:00:24.480><c> supported</c>

00:00:25.109 --> 00:00:25.119 align:start position:0%
support. Now, Olama has supported
 

00:00:25.119 --> 00:00:27.750 align:start position:0%
support. Now, Olama has supported
thinking<00:00:25.600><c> and</c><00:00:25.920><c> reasoning</c><00:00:26.400><c> models</c><00:00:26.800><c> for</c><00:00:27.279><c> months</c>

00:00:27.750 --> 00:00:27.760 align:start position:0%
thinking and reasoning models for months
 

00:00:27.760 --> 00:00:30.710 align:start position:0%
thinking and reasoning models for months
already,<00:00:28.400><c> ever</c><00:00:28.640><c> since</c><00:00:29.039><c> Deep</c><00:00:29.279><c> Seek</c><00:00:29.599><c> R1</c><00:00:30.400><c> became</c>

00:00:30.710 --> 00:00:30.720 align:start position:0%
already, ever since Deep Seek R1 became
 

00:00:30.720 --> 00:00:33.229 align:start position:0%
already, ever since Deep Seek R1 became
the<00:00:31.039><c> first</c><00:00:31.279><c> major</c><00:00:31.840><c> downloadable</c><00:00:32.640><c> thinking</c>

00:00:33.229 --> 00:00:33.239 align:start position:0%
the first major downloadable thinking
 

00:00:33.239 --> 00:00:36.110 align:start position:0%
the first major downloadable thinking
model.<00:00:34.239><c> But</c><00:00:34.559><c> here's</c><00:00:35.040><c> what's</c>

00:00:36.110 --> 00:00:36.120 align:start position:0%
model. But here's what's
 

00:00:36.120 --> 00:00:39.030 align:start position:0%
model. But here's what's
revolutionary.<00:00:37.120><c> AMA</c><00:00:38.079><c> now</c><00:00:38.480><c> actually</c>

00:00:39.030 --> 00:00:39.040 align:start position:0%
revolutionary. AMA now actually
 

00:00:39.040 --> 00:00:41.590 align:start position:0%
revolutionary. AMA now actually
understands<00:00:39.920><c> the</c><00:00:40.239><c> thinking</c><00:00:40.640><c> process</c><00:00:41.040><c> and</c><00:00:41.360><c> can</c>

00:00:41.590 --> 00:00:41.600 align:start position:0%
understands the thinking process and can
 

00:00:41.600 --> 00:00:44.310 align:start position:0%
understands the thinking process and can
separate<00:00:42.000><c> it</c><00:00:42.239><c> out</c><00:00:42.960><c> intelligently</c><00:00:43.840><c> from</c><00:00:44.079><c> the</c>

00:00:44.310 --> 00:00:44.320 align:start position:0%
separate it out intelligently from the
 

00:00:44.320 --> 00:00:47.110 align:start position:0%
separate it out intelligently from the
final<00:00:44.600><c> output.</c><00:00:45.600><c> What</c><00:00:45.840><c> you</c><00:00:46.000><c> get</c><00:00:46.239><c> now</c><00:00:46.559><c> is</c>

00:00:47.110 --> 00:00:47.120 align:start position:0%
final output. What you get now is
 

00:00:47.120 --> 00:00:50.389 align:start position:0%
final output. What you get now is
beautiful<00:00:47.840><c> separation.</c><00:00:49.039><c> The</c><00:00:49.280><c> API</c><00:00:49.920><c> returns</c>

00:00:50.389 --> 00:00:50.399 align:start position:0%
beautiful separation. The API returns
 

00:00:50.399 --> 00:00:53.189 align:start position:0%
beautiful separation. The API returns
distinct<00:00:51.280><c> thinking</c><00:00:52.079><c> and</c><00:00:52.399><c> content</c><00:00:52.879><c> fields</c>

00:00:53.189 --> 00:00:53.199 align:start position:0%
distinct thinking and content fields
 

00:00:53.199 --> 00:00:56.150 align:start position:0%
distinct thinking and content fields
that<00:00:53.440><c> are</c><00:00:53.600><c> populated</c><00:00:54.840><c> accordingly.</c><00:00:55.840><c> Whether</c>

00:00:56.150 --> 00:00:56.160 align:start position:0%
that are populated accordingly. Whether
 

00:00:56.160 --> 00:00:57.910 align:start position:0%
that are populated accordingly. Whether
you're<00:00:56.399><c> streaming</c><00:00:56.800><c> or</c><00:00:56.960><c> not,</c><00:00:57.280><c> you</c><00:00:57.440><c> get</c><00:00:57.600><c> this</c>

00:00:57.910 --> 00:00:57.920 align:start position:0%
you're streaming or not, you get this
 

00:00:57.920 --> 00:00:59.910 align:start position:0%
you're streaming or not, you get this
clean<00:00:58.399><c> separation</c><00:00:58.960><c> between</c><00:00:59.280><c> the</c><00:00:59.520><c> model's</c>

00:00:59.910 --> 00:00:59.920 align:start position:0%
clean separation between the model's
 

00:00:59.920 --> 00:01:02.189 align:start position:0%
clean separation between the model's
internal<00:01:00.320><c> reasoning</c><00:01:01.199><c> and</c><00:01:01.440><c> its</c><00:01:01.760><c> final</c>

00:01:02.189 --> 00:01:02.199 align:start position:0%
internal reasoning and its final
 

00:01:02.199 --> 00:01:04.630 align:start position:0%
internal reasoning and its final
response.<00:01:03.199><c> The</c><00:01:03.520><c> brilliant</c><00:01:04.000><c> part,</c><00:01:04.320><c> they</c>

00:01:04.630 --> 00:01:04.640 align:start position:0%
response. The brilliant part, they
 

00:01:04.640 --> 00:01:07.990 align:start position:0%
response. The brilliant part, they
didn't<00:01:05.040><c> break</c><00:01:05.519><c> any</c><00:01:06.000><c> existing</c><00:01:07.000><c> applications.</c>

00:01:07.990 --> 00:01:08.000 align:start position:0%
didn't break any existing applications.
 

00:01:08.000 --> 00:01:10.469 align:start position:0%
didn't break any existing applications.
If<00:01:08.320><c> they'd</c><00:01:08.720><c> enabled</c><00:01:09.200><c> this</c><00:01:09.439><c> by</c><00:01:09.680><c> default,</c><00:01:10.240><c> it</c>

00:01:10.469 --> 00:01:10.479 align:start position:0%
If they'd enabled this by default, it
 

00:01:10.479 --> 00:01:13.429 align:start position:0%
If they'd enabled this by default, it
would<00:01:10.720><c> have</c><00:01:10.960><c> been</c><00:01:11.200><c> a</c><00:01:11.520><c> breaking</c><00:01:12.200><c> change.</c><00:01:13.200><c> But</c>

00:01:13.429 --> 00:01:13.439 align:start position:0%
would have been a breaking change. But
 

00:01:13.439 --> 00:01:15.990 align:start position:0%
would have been a breaking change. But
here's<00:01:13.760><c> where</c><00:01:14.000><c> you</c><00:01:14.240><c> might</c><00:01:14.479><c> get</c><00:01:14.799><c> confused</c><00:01:15.600><c> when</c>

00:01:15.990 --> 00:01:16.000 align:start position:0%
here's where you might get confused when
 

00:01:16.000 --> 00:01:19.350 align:start position:0%
here's where you might get confused when
first<00:01:16.320><c> trying</c><00:01:16.640><c> this</c><00:01:16.880><c> out.</c><00:01:17.680><c> You'll</c><00:01:18.080><c> wonder</c><00:01:18.360><c> why</c>

00:01:19.350 --> 00:01:19.360 align:start position:0%
first trying this out. You'll wonder why
 

00:01:19.360 --> 00:01:22.950 align:start position:0%
first trying this out. You'll wonder why
nothing<00:01:20.080><c> seems</c><00:01:21.080><c> different.</c><00:01:22.080><c> The</c><00:01:22.400><c> key</c><00:01:22.640><c> is</c>

00:01:22.950 --> 00:01:22.960 align:start position:0%
nothing seems different. The key is
 

00:01:22.960 --> 00:01:26.550 align:start position:0%
nothing seems different. The key is
this.<00:01:23.520><c> You</c><00:01:23.840><c> must</c><00:01:24.400><c> explicitly</c><00:01:25.200><c> enable</c><00:01:25.600><c> it.</c><00:01:26.320><c> For</c>

00:01:26.550 --> 00:01:26.560 align:start position:0%
this. You must explicitly enable it. For
 

00:01:26.560 --> 00:01:29.510 align:start position:0%
this. You must explicitly enable it. For
the<00:01:26.720><c> API,</c><00:01:27.360><c> set</c><00:01:27.680><c> think</c><00:01:28.320><c> to</c><00:01:28.640><c> true</c><00:01:29.040><c> in</c><00:01:29.360><c> your</c>

00:01:29.510 --> 00:01:29.520 align:start position:0%
the API, set think to true in your
 

00:01:29.520 --> 00:01:32.749 align:start position:0%
the API, set think to true in your
request<00:01:29.920><c> body.</c><00:01:30.560><c> For</c><00:01:30.880><c> CLI,</c><00:01:31.520><c> use</c><00:01:31.840><c> slash</c>

00:01:32.749 --> 00:01:32.759 align:start position:0%
request body. For CLI, use slash
 

00:01:32.759 --> 00:01:35.670 align:start position:0%
request body. For CLI, use slash
setthink<00:01:33.759><c> to</c><00:01:34.000><c> enable</c><00:01:34.320><c> it</c><00:01:34.560><c> and</c><00:01:34.960><c> slash</c><00:01:35.360><c> set</c>

00:01:35.670 --> 00:01:35.680 align:start position:0%
setthink to enable it and slash set
 

00:01:35.680 --> 00:01:38.310 align:start position:0%
setthink to enable it and slash set
nothink<00:01:36.320><c> to</c><00:01:36.479><c> disable</c><00:01:36.880><c> it.</c><00:01:37.680><c> Let</c><00:01:37.840><c> me</c><00:01:38.000><c> show</c><00:01:38.159><c> you</c>

00:01:38.310 --> 00:01:38.320 align:start position:0%
nothink to disable it. Let me show you
 

00:01:38.320 --> 00:01:41.429 align:start position:0%
nothink to disable it. Let me show you
this<00:01:38.560><c> in</c><00:01:38.799><c> action.</c><00:01:39.520><c> I'm</c><00:01:39.840><c> using</c><00:01:40.159><c> get</c><00:01:40.479><c> API</c><00:01:41.040><c> here,</c>

00:01:41.429 --> 00:01:41.439 align:start position:0%
this in action. I'm using get API here,
 

00:01:41.439 --> 00:01:43.270 align:start position:0%
this in action. I'm using get API here,
though<00:01:41.680><c> this</c><00:01:41.920><c> works</c><00:01:42.159><c> with</c><00:01:42.400><c> Postman,</c>

00:01:43.270 --> 00:01:43.280 align:start position:0%
though this works with Postman,
 

00:01:43.280 --> 00:01:46.389 align:start position:0%
though this works with Postman,
Insomnia,<00:01:44.000><c> or</c><00:01:44.320><c> any</c><00:01:44.640><c> API</c><00:01:45.200><c> tool.</c><00:01:45.840><c> I'm</c><00:01:46.079><c> testing</c>

00:01:46.389 --> 00:01:46.399 align:start position:0%
Insomnia, or any API tool. I'm testing
 

00:01:46.399 --> 00:01:47.950 align:start position:0%
Insomnia, or any API tool. I'm testing
with<00:01:46.640><c> Quen</c>

00:01:47.950 --> 00:01:47.960 align:start position:0%
with Quen
 

00:01:47.960 --> 00:01:50.630 align:start position:0%
with Quen
2.514b,<00:01:48.960><c> one</c><00:01:49.200><c> of</c><00:01:49.280><c> the</c><00:01:49.439><c> thinking</c><00:01:49.759><c> models,</c><00:01:50.479><c> with</c>

00:01:50.630 --> 00:01:50.640 align:start position:0%
2.514b, one of the thinking models, with
 

00:01:50.640 --> 00:01:53.590 align:start position:0%
2.514b, one of the thinking models, with
a<00:01:50.880><c> simple</c><00:01:51.200><c> prompt.</c><00:01:51.920><c> Get</c><00:01:52.159><c> a</c><00:01:52.399><c> business</c><00:01:52.799><c> plan</c><00:01:53.200><c> for</c>

00:01:53.590 --> 00:01:53.600 align:start position:0%
a simple prompt. Get a business plan for
 

00:01:53.600 --> 00:01:56.710 align:start position:0%
a simple prompt. Get a business plan for
a<00:01:54.000><c> startup</c><00:01:54.399><c> for</c><00:01:54.560><c> dog</c><00:01:54.880><c> soap.</c><00:01:55.759><c> I</c><00:01:56.000><c> don't</c><00:01:56.079><c> know.</c>

00:01:56.710 --> 00:01:56.720 align:start position:0%
a startup for dog soap. I don't know.
 

00:01:56.720 --> 00:01:58.469 align:start position:0%
a startup for dog soap. I don't know.
Initially,<00:01:57.119><c> I</c><00:01:57.360><c> set</c><00:01:57.520><c> streaming</c><00:01:57.920><c> to</c><00:01:58.159><c> false</c>

00:01:58.469 --> 00:01:58.479 align:start position:0%
Initially, I set streaming to false
 

00:01:58.479 --> 00:02:00.870 align:start position:0%
Initially, I set streaming to false
because<00:01:58.719><c> I</c><00:01:59.040><c> couldn't</c><00:01:59.360><c> get</c><00:01:59.520><c> it</c><00:01:59.840><c> working.</c><00:02:00.640><c> I</c>

00:02:00.870 --> 00:02:00.880 align:start position:0%
because I couldn't get it working. I
 

00:02:00.880 --> 00:02:02.870 align:start position:0%
because I couldn't get it working. I
thought<00:02:01.119><c> maybe</c><00:02:01.439><c> streaming</c><00:02:02.079><c> interfered</c><00:02:02.719><c> with</c>

00:02:02.870 --> 00:02:02.880 align:start position:0%
thought maybe streaming interfered with
 

00:02:02.880 --> 00:02:05.350 align:start position:0%
thought maybe streaming interfered with
the<00:02:03.119><c> feature.</c><00:02:03.600><c> And</c><00:02:03.840><c> honestly,</c><00:02:04.479><c> most</c><00:02:04.880><c> API</c>

00:02:05.350 --> 00:02:05.360 align:start position:0%
the feature. And honestly, most API
 

00:02:05.360 --> 00:02:07.830 align:start position:0%
the feature. And honestly, most API
testing<00:02:05.680><c> tools</c><00:02:06.560><c> don't</c><00:02:06.880><c> handle</c><00:02:07.360><c> streaming</c>

00:02:07.830 --> 00:02:07.840 align:start position:0%
testing tools don't handle streaming
 

00:02:07.840 --> 00:02:10.869 align:start position:0%
testing tools don't handle streaming
gracefully<00:02:08.640><c> anyway.</c><00:02:09.520><c> They</c><00:02:09.840><c> just</c><00:02:10.160><c> dump</c><00:02:10.640><c> all</c>

00:02:10.869 --> 00:02:10.879 align:start position:0%
gracefully anyway. They just dump all
 

00:02:10.879 --> 00:02:14.309 align:start position:0%
gracefully anyway. They just dump all
the<00:02:11.039><c> JSON</c><00:02:11.599><c> responses</c><00:02:12.480><c> at</c><00:02:12.720><c> the</c><00:02:12.879><c> end.</c><00:02:13.680><c> So</c><00:02:13.920><c> let</c><00:02:14.160><c> me</c>

00:02:14.309 --> 00:02:14.319 align:start position:0%
the JSON responses at the end. So let me
 

00:02:14.319 --> 00:02:16.589 align:start position:0%
the JSON responses at the end. So let me
demonstrate<00:02:14.720><c> with</c><00:02:14.959><c> curl</c><00:02:15.440><c> first</c><00:02:15.920><c> thinking</c>

00:02:16.589 --> 00:02:16.599 align:start position:0%
demonstrate with curl first thinking
 

00:02:16.599 --> 00:02:19.510 align:start position:0%
demonstrate with curl first thinking
disabled.<00:02:17.599><c> Same</c><00:02:18.000><c> model,</c><00:02:18.480><c> same</c><00:02:18.800><c> prompt.</c><00:02:19.280><c> Hit</c>

00:02:19.510 --> 00:02:19.520 align:start position:0%
disabled. Same model, same prompt. Hit
 

00:02:19.520 --> 00:02:21.030 align:start position:0%
disabled. Same model, same prompt. Hit
enter<00:02:19.840><c> and</c><00:02:20.000><c> we</c><00:02:20.239><c> get</c><00:02:20.319><c> the</c><00:02:20.560><c> traditional</c>

00:02:21.030 --> 00:02:21.040 align:start position:0%
enter and we get the traditional
 

00:02:21.040 --> 00:02:24.510 align:start position:0%
enter and we get the traditional
response.<00:02:21.840><c> Model</c><00:02:22.239><c> name,</c><00:02:22.800><c> timestamp,</c><00:02:23.599><c> ro</c><00:02:24.080><c> and</c>

00:02:24.510 --> 00:02:24.520 align:start position:0%
response. Model name, timestamp, ro and
 

00:02:24.520 --> 00:02:27.589 align:start position:0%
response. Model name, timestamp, ro and
content.<00:02:25.520><c> But</c><00:02:25.760><c> notice</c><00:02:26.160><c> something</c><00:02:26.640><c> confusing.</c>

00:02:27.589 --> 00:02:27.599 align:start position:0%
content. But notice something confusing.
 

00:02:27.599 --> 00:02:29.830 align:start position:0%
content. But notice something confusing.
All<00:02:27.840><c> the</c><00:02:28.080><c> thinking</c><00:02:28.560><c> tokens</c><00:02:29.120><c> are</c><00:02:29.360><c> jumbled</c>

00:02:29.830 --> 00:02:29.840 align:start position:0%
All the thinking tokens are jumbled
 

00:02:29.840 --> 00:02:32.229 align:start position:0%
All the thinking tokens are jumbled
together<00:02:30.560><c> with</c><00:02:30.879><c> the</c><00:02:31.120><c> actual</c><00:02:31.440><c> response</c><00:02:31.840><c> in</c><00:02:32.080><c> the</c>

00:02:32.229 --> 00:02:32.239 align:start position:0%
together with the actual response in the
 

00:02:32.239 --> 00:02:34.790 align:start position:0%
together with the actual response in the
content<00:02:32.760><c> field.</c><00:02:33.760><c> But</c><00:02:34.000><c> watch</c><00:02:34.239><c> what</c><00:02:34.560><c> happens</c>

00:02:34.790 --> 00:02:34.800 align:start position:0%
content field. But watch what happens
 

00:02:34.800 --> 00:02:39.190 align:start position:0%
content field. But watch what happens
when<00:02:35.040><c> I</c><00:02:35.280><c> add</c><00:02:35.599><c> think</c><00:02:36.840><c> true</c><00:02:37.840><c> to</c><00:02:38.080><c> the</c><00:02:38.319><c> body.</c><00:02:38.959><c> Now</c>

00:02:39.190 --> 00:02:39.200 align:start position:0%
when I add think true to the body. Now
 

00:02:39.200 --> 00:02:41.990 align:start position:0%
when I add think true to the body. Now
we<00:02:39.360><c> get</c><00:02:39.519><c> our</c><00:02:39.760><c> familiar</c><00:02:40.239><c> content</c><00:02:40.640><c> field</c><00:02:41.120><c> plus</c><00:02:41.680><c> a</c>

00:02:41.990 --> 00:02:42.000 align:start position:0%
we get our familiar content field plus a
 

00:02:42.000 --> 00:02:44.229 align:start position:0%
we get our familiar content field plus a
separate<00:02:42.480><c> thinking</c><00:02:42.879><c> field.</c><00:02:43.760><c> No</c><00:02:44.000><c> more</c>

00:02:44.229 --> 00:02:44.239 align:start position:0%
separate thinking field. No more
 

00:02:44.239 --> 00:02:46.470 align:start position:0%
separate thinking field. No more
confusing<00:02:44.879><c> thinking</c><00:02:45.360><c> symbols</c><00:02:45.920><c> mixed</c><00:02:46.239><c> into</c>

00:02:46.470 --> 00:02:46.480 align:start position:0%
confusing thinking symbols mixed into
 

00:02:46.480 --> 00:02:49.390 align:start position:0%
confusing thinking symbols mixed into
the<00:02:46.640><c> response,</c><00:02:47.519><c> just</c><00:02:47.840><c> clean,</c><00:02:48.560><c> separated</c>

00:02:49.390 --> 00:02:49.400 align:start position:0%
the response, just clean, separated
 

00:02:49.400 --> 00:02:51.910 align:start position:0%
the response, just clean, separated
reasoning.<00:02:50.400><c> With</c><00:02:50.640><c> streaming</c><00:02:51.120><c> enabled,</c><00:02:51.680><c> each</c>

00:02:51.910 --> 00:02:51.920 align:start position:0%
reasoning. With streaming enabled, each
 

00:02:51.920 --> 00:02:54.390 align:start position:0%
reasoning. With streaming enabled, each
JSON<00:02:52.400><c> response</c><00:02:52.879><c> represents</c><00:02:53.360><c> a</c><00:02:53.519><c> single</c><00:02:53.760><c> token</c>

00:02:54.390 --> 00:02:54.400 align:start position:0%
JSON response represents a single token
 

00:02:54.400 --> 00:02:56.830 align:start position:0%
JSON response represents a single token
tagged<00:02:54.800><c> either</c><00:02:55.120><c> as</c><00:02:55.440><c> thinking</c><00:02:55.840><c> or</c>

00:02:56.830 --> 00:02:56.840 align:start position:0%
tagged either as thinking or
 

00:02:56.840 --> 00:02:59.190 align:start position:0%
tagged either as thinking or
content.<00:02:57.840><c> You</c><00:02:58.080><c> can</c><00:02:58.239><c> watch</c><00:02:58.400><c> the</c><00:02:58.640><c> model</c><00:02:58.959><c> work</c>

00:02:59.190 --> 00:02:59.200 align:start position:0%
content. You can watch the model work
 

00:02:59.200 --> 00:03:01.589 align:start position:0%
content. You can watch the model work
through<00:02:59.360><c> its</c><00:02:59.599><c> reasoning</c><00:03:00.080><c> in</c><00:03:00.319><c> real</c><00:03:00.640><c> time.</c><00:03:01.360><c> When</c>

00:03:01.589 --> 00:03:01.599 align:start position:0%
through its reasoning in real time. When
 

00:03:01.599 --> 00:03:03.750 align:start position:0%
through its reasoning in real time. When
the<00:03:01.760><c> JSON</c><00:03:02.239><c> responses</c><00:03:02.800><c> stop</c><00:03:03.120><c> containing</c><00:03:03.599><c> the</c>

00:03:03.750 --> 00:03:03.760 align:start position:0%
the JSON responses stop containing the
 

00:03:03.760 --> 00:03:05.830 align:start position:0%
the JSON responses stop containing the
thinking<00:03:04.159><c> field</c><00:03:04.640><c> and</c><00:03:04.959><c> switch</c><00:03:05.200><c> to</c><00:03:05.360><c> content</c>

00:03:05.830 --> 00:03:05.840 align:start position:0%
thinking field and switch to content
 

00:03:05.840 --> 00:03:08.309 align:start position:0%
thinking field and switch to content
only,<00:03:06.640><c> you</c><00:03:06.879><c> know</c><00:03:07.120><c> the</c><00:03:07.360><c> reasoning</c><00:03:07.760><c> phase</c><00:03:08.080><c> is</c>

00:03:08.309 --> 00:03:08.319 align:start position:0%
only, you know the reasoning phase is
 

00:03:08.319 --> 00:03:10.910 align:start position:0%
only, you know the reasoning phase is
complete<00:03:08.959><c> and</c><00:03:09.280><c> the</c><00:03:09.519><c> final</c><00:03:09.840><c> answer</c><00:03:10.159><c> is</c><00:03:10.400><c> being</c>

00:03:10.910 --> 00:03:10.920 align:start position:0%
complete and the final answer is being
 

00:03:10.920 --> 00:03:13.910 align:start position:0%
complete and the final answer is being
generated.<00:03:11.920><c> One</c><00:03:12.480><c> missed</c><00:03:12.959><c> opportunity</c><00:03:13.680><c> is</c>

00:03:13.910 --> 00:03:13.920 align:start position:0%
generated. One missed opportunity is
 

00:03:13.920 --> 00:03:16.229 align:start position:0%
generated. One missed opportunity is
that<00:03:14.159><c> the</c><00:03:14.400><c> final</c><00:03:14.720><c> response</c><00:03:15.200><c> includes</c><00:03:15.680><c> timing</c>

00:03:16.229 --> 00:03:16.239 align:start position:0%
that the final response includes timing
 

00:03:16.239 --> 00:03:19.030 align:start position:0%
that the final response includes timing
statistics,<00:03:17.120><c> but</c><00:03:17.440><c> eval</c><00:03:17.920><c> duration</c><00:03:18.319><c> and</c><00:03:18.560><c> eval</c>

00:03:19.030 --> 00:03:19.040 align:start position:0%
statistics, but eval duration and eval
 

00:03:19.040 --> 00:03:21.990 align:start position:0%
statistics, but eval duration and eval
count<00:03:19.599><c> cover</c><00:03:19.920><c> the</c><00:03:20.159><c> entire</c><00:03:20.480><c> output.</c><00:03:21.440><c> I'd</c><00:03:21.760><c> love</c>

00:03:21.990 --> 00:03:22.000 align:start position:0%
count cover the entire output. I'd love
 

00:03:22.000 --> 00:03:23.910 align:start position:0%
count cover the entire output. I'd love
to<00:03:22.159><c> see</c><00:03:22.400><c> separate</c><00:03:22.959><c> metrics,</c><00:03:23.519><c> thinking</c>

00:03:23.910 --> 00:03:23.920 align:start position:0%
to see separate metrics, thinking
 

00:03:23.920 --> 00:03:26.229 align:start position:0%
to see separate metrics, thinking
duration,<00:03:24.560><c> thinking</c><00:03:25.040><c> count,</c><00:03:25.840><c> content</c>

00:03:26.229 --> 00:03:26.239 align:start position:0%
duration, thinking count, content
 

00:03:26.239 --> 00:03:28.710 align:start position:0%
duration, thinking count, content
duration,<00:03:26.800><c> content</c><00:03:27.200><c> count.</c><00:03:28.480><c> That</c>

00:03:28.710 --> 00:03:28.720 align:start position:0%
duration, content count. That
 

00:03:28.720 --> 00:03:31.990 align:start position:0%
duration, content count. That
granularity<00:03:29.680><c> isn't</c><00:03:30.239><c> there</c><00:03:30.560><c> yet,</c><00:03:31.440><c> but</c><00:03:31.680><c> you</c><00:03:31.840><c> can</c>

00:03:31.990 --> 00:03:32.000 align:start position:0%
granularity isn't there yet, but you can
 

00:03:32.000 --> 00:03:34.309 align:start position:0%
granularity isn't there yet, but you can
calculate<00:03:32.480><c> this</c><00:03:32.720><c> yourself</c><00:03:33.280><c> pretty</c><00:03:33.599><c> easily</c>

00:03:34.309 --> 00:03:34.319 align:start position:0%
calculate this yourself pretty easily
 

00:03:34.319 --> 00:03:37.110 align:start position:0%
calculate this yourself pretty easily
with<00:03:34.560><c> an</c><00:03:34.720><c> eval</c><00:03:35.280><c> count</c><00:03:35.440><c> of</c><00:03:35.680><c> let's</c><00:03:35.920><c> say</c><00:03:36.159><c> 2135</c>

00:03:37.110 --> 00:03:37.120 align:start position:0%
with an eval count of let's say 2135
 

00:03:37.120 --> 00:03:39.509 align:start position:0%
with an eval count of let's say 2135
tokens.<00:03:37.920><c> Count</c><00:03:38.239><c> the</c><00:03:38.480><c> thinking</c><00:03:39.040><c> versus</c>

00:03:39.509 --> 00:03:39.519 align:start position:0%
tokens. Count the thinking versus
 

00:03:39.519 --> 00:03:42.229 align:start position:0%
tokens. Count the thinking versus
content<00:03:40.080><c> JSON</c><00:03:40.640><c> responses.</c><00:03:41.599><c> Calculate</c><00:03:42.000><c> the</c>

00:03:42.229 --> 00:03:42.239 align:start position:0%
content JSON responses. Calculate the
 

00:03:42.239 --> 00:03:44.229 align:start position:0%
content JSON responses. Calculate the
percentage<00:03:42.640><c> split</c><00:03:43.040><c> and</c><00:03:43.200><c> apply</c><00:03:43.519><c> that</c><00:03:43.760><c> ratio</c><00:03:44.080><c> to</c>

00:03:44.229 --> 00:03:44.239 align:start position:0%
percentage split and apply that ratio to
 

00:03:44.239 --> 00:03:46.229 align:start position:0%
percentage split and apply that ratio to
the<00:03:44.319><c> eval</c><00:03:44.799><c> duration.</c><00:03:45.440><c> And</c><00:03:45.680><c> boom,</c><00:03:46.000><c> there</c>

00:03:46.229 --> 00:03:46.239 align:start position:0%
the eval duration. And boom, there
 

00:03:46.239 --> 00:03:47.910 align:start position:0%
the eval duration. And boom, there
you've<00:03:46.480><c> got</c><00:03:46.640><c> your</c><00:03:46.799><c> thinking</c><00:03:47.200><c> time</c><00:03:47.519><c> versus</c>

00:03:47.910 --> 00:03:47.920 align:start position:0%
you've got your thinking time versus
 

00:03:47.920 --> 00:03:50.149 align:start position:0%
you've got your thinking time versus
content<00:03:48.400><c> generation</c><00:03:48.879><c> time.</c><00:03:49.840><c> This</c><00:03:50.000><c> is</c>

00:03:50.149 --> 00:03:50.159 align:start position:0%
content generation time. This is
 

00:03:50.159 --> 00:03:52.869 align:start position:0%
content generation time. This is
absolutely<00:03:50.720><c> gamechanging</c><00:03:51.440><c> for</c><00:03:51.760><c> developers.</c>

00:03:52.869 --> 00:03:52.879 align:start position:0%
absolutely gamechanging for developers.
 

00:03:52.879 --> 00:03:54.710 align:start position:0%
absolutely gamechanging for developers.
Previously,<00:03:53.440><c> I</c><00:03:53.680><c> built</c><00:03:53.920><c> applications</c><00:03:54.480><c> that</c>

00:03:54.710 --> 00:03:54.720 align:start position:0%
Previously, I built applications that
 

00:03:54.720 --> 00:03:57.350 align:start position:0%
Previously, I built applications that
needed<00:03:55.040><c> to</c><00:03:55.280><c> detect</c><00:03:55.760><c> when</c><00:03:56.080><c> thinking</c><00:03:56.560><c> finished,</c>

00:03:57.350 --> 00:03:57.360 align:start position:0%
needed to detect when thinking finished,
 

00:03:57.360 --> 00:03:59.190 align:start position:0%
needed to detect when thinking finished,
which<00:03:57.599><c> meant</c><00:03:57.840><c> hunting</c><00:03:58.239><c> for</c><00:03:58.480><c> closing</c><00:03:58.799><c> tags</c>

00:03:59.190 --> 00:03:59.200 align:start position:0%
which meant hunting for closing tags
 

00:03:59.200 --> 00:04:02.550 align:start position:0%
which meant hunting for closing tags
like<00:04:00.120><c> /thinking.</c><00:04:01.120><c> I</c><00:04:01.360><c> had</c><00:04:01.519><c> to</c><00:04:01.760><c> manually</c><00:04:02.239><c> parse</c>

00:04:02.550 --> 00:04:02.560 align:start position:0%
like /thinking. I had to manually parse
 

00:04:02.560 --> 00:04:04.869 align:start position:0%
like /thinking. I had to manually parse
these<00:04:02.799><c> symbols</c><00:04:03.120><c> and</c><00:04:03.360><c> handle</c><00:04:03.680><c> timing.</c><00:04:04.560><c> And</c>

00:04:04.869 --> 00:04:04.879 align:start position:0%
these symbols and handle timing. And
 

00:04:04.879 --> 00:04:07.589 align:start position:0%
these symbols and handle timing. And
every<00:04:05.280><c> model</c><00:04:05.840><c> used</c><00:04:06.239><c> different</c><00:04:06.560><c> markers.</c><00:04:07.280><c> It</c>

00:04:07.589 --> 00:04:07.599 align:start position:0%
every model used different markers. It
 

00:04:07.599 --> 00:04:11.670 align:start position:0%
every model used different markers. It
was<00:04:07.840><c> a</c><00:04:08.760><c> nightmare.</c><00:04:09.760><c> Now</c><00:04:10.319><c> it's</c><00:04:10.720><c> effortless.</c>

00:04:11.670 --> 00:04:11.680 align:start position:0%
was a nightmare. Now it's effortless.
 

00:04:11.680 --> 00:04:14.229 align:start position:0%
was a nightmare. Now it's effortless.
This<00:04:11.920><c> will</c><00:04:12.319><c> revolutionize</c><00:04:13.200><c> how</c><00:04:13.519><c> tools</c><00:04:13.920><c> like</c>

00:04:14.229 --> 00:04:14.239 align:start position:0%
This will revolutionize how tools like
 

00:04:14.239 --> 00:04:17.509 align:start position:0%
This will revolutionize how tools like
Misti<00:04:14.799><c> and</c><00:04:15.040><c> others</c><00:04:15.920><c> handle</c><00:04:16.320><c> thinking</c><00:04:16.720><c> models.</c>

00:04:17.509 --> 00:04:17.519 align:start position:0%
Misti and others handle thinking models.
 

00:04:17.519 --> 00:04:19.830 align:start position:0%
Misti and others handle thinking models.
They<00:04:17.840><c> get</c><00:04:18.079><c> clean</c><00:04:18.400><c> separation</c><00:04:19.199><c> without</c>

00:04:19.830 --> 00:04:19.840 align:start position:0%
They get clean separation without
 

00:04:19.840 --> 00:04:22.790 align:start position:0%
They get clean separation without
parsing<00:04:20.320><c> headaches.</c><00:04:21.280><c> Any</c><00:04:21.759><c> developer</c><00:04:22.479><c> working</c>

00:04:22.790 --> 00:04:22.800 align:start position:0%
parsing headaches. Any developer working
 

00:04:22.800 --> 00:04:25.189 align:start position:0%
parsing headaches. Any developer working
with<00:04:23.360><c> reasoning</c><00:04:23.840><c> models</c><00:04:24.479><c> just</c><00:04:24.800><c> got</c><00:04:24.960><c> their</c>

00:04:25.189 --> 00:04:25.199 align:start position:0%
with reasoning models just got their
 

00:04:25.199 --> 00:04:28.070 align:start position:0%
with reasoning models just got their
life<00:04:25.759><c> dramatically</c><00:04:26.759><c> simplified.</c><00:04:27.759><c> I'm</c>

00:04:28.070 --> 00:04:28.080 align:start position:0%
life dramatically simplified. I'm
 

00:04:28.080 --> 00:04:30.070 align:start position:0%
life dramatically simplified. I'm
genuinely<00:04:28.639><c> thrilled</c><00:04:29.040><c> about</c><00:04:29.280><c> this</c><00:04:29.440><c> update</c><00:04:29.759><c> and</c>

00:04:30.070 --> 00:04:30.080 align:start position:0%
genuinely thrilled about this update and
 

00:04:30.080 --> 00:04:32.790 align:start position:0%
genuinely thrilled about this update and
had<00:04:30.240><c> to</c><00:04:30.400><c> share</c><00:04:30.639><c> it</c><00:04:31.240><c> immediately.</c><00:04:32.240><c> This</c><00:04:32.479><c> is</c><00:04:32.560><c> the</c>

00:04:32.790 --> 00:04:32.800 align:start position:0%
had to share it immediately. This is the
 

00:04:32.800 --> 00:04:35.189 align:start position:0%
had to share it immediately. This is the
kind<00:04:32.960><c> of</c><00:04:33.199><c> thoughtful</c><00:04:33.759><c> API</c><00:04:34.320><c> design</c><00:04:34.639><c> that</c><00:04:34.880><c> makes</c>

00:04:35.189 --> 00:04:35.199 align:start position:0%
kind of thoughtful API design that makes
 

00:04:35.199 --> 00:04:38.270 align:start position:0%
kind of thoughtful API design that makes
building<00:04:35.440><c> with</c><00:04:35.759><c> AI</c><00:04:36.320><c> models</c><00:04:36.919><c> actually</c>

00:04:38.270 --> 00:04:38.280 align:start position:0%
building with AI models actually
 

00:04:38.280 --> 00:04:40.469 align:start position:0%
building with AI models actually
enjoyable.<00:04:39.280><c> Thanks</c><00:04:39.520><c> so</c><00:04:39.680><c> much</c><00:04:39.840><c> for</c><00:04:40.080><c> watching</c>

00:04:40.469 --> 00:04:40.479 align:start position:0%
enjoyable. Thanks so much for watching
 

00:04:40.479 --> 00:04:42.870 align:start position:0%
enjoyable. Thanks so much for watching
and<00:04:40.800><c> I</c><00:04:41.040><c> look</c><00:04:41.120><c> forward</c><00:04:41.360><c> to</c><00:04:41.520><c> seeing</c><00:04:41.919><c> how</c><00:04:42.240><c> you</c><00:04:42.639><c> use</c>

00:04:42.870 --> 00:04:42.880 align:start position:0%
and I look forward to seeing how you use
 

00:04:42.880 --> 00:04:45.350 align:start position:0%
and I look forward to seeing how you use
this<00:04:43.600><c> in</c><00:04:43.840><c> your</c><00:04:44.080><c> own</c><00:04:44.280><c> applications.</c><00:04:45.280><c> All</c>

00:04:45.350 --> 00:04:45.360 align:start position:0%
this in your own applications. All
 

00:04:45.360 --> 00:04:49.080 align:start position:0%
this in your own applications. All
righty.<00:04:46.080><c> Bye.</c>

