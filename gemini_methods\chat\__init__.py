"""
Google GenAI Chat Manager Package

This package provides conversation memory functionality using Google GenAI's 
built-in chat client interface.

Main components:
- ChatManager: Main class for managing persistent chat sessions
- ChatMessage: Data model for individual messages
- ChatSession: Data model for session metadata
- Convenience functions for quick chat operations
"""

# Handle imports - try relative imports first, then absolute imports as fallback
try:
    from .chat_manager import (
        ChatManager,
        ChatMessage,
        ChatSession,
        create_simple_chat,
        quick_chat_exchange
    )
except ImportError:
    # Fallback for direct execution
    from chat_manager import (
        ChatManager,
        ChatMessage,
        ChatSession,
        create_simple_chat,
        quick_chat_exchange
    )

__all__ = [
    'ChatManager',
    'ChatMessage', 
    'ChatSession',
    'create_simple_chat',
    'quick_chat_exchange'
]

__version__ = '1.0.0' 