WEBVTT
Kind: captions
Language: en

00:00:00.359 --> 00:00:03.470 align:start position:0%
 
three<00:00:01.140><c> easy</c><00:00:01.620><c> steps</c><00:00:02.100><c> to</c><00:00:02.399><c> working</c><00:00:02.639><c> with</c><00:00:03.060><c> AI</c>

00:00:03.470 --> 00:00:03.480 align:start position:0%
three easy steps to working with AI
 

00:00:03.480 --> 00:00:05.870 align:start position:0%
three easy steps to working with AI
locally<00:00:04.140><c> on</c><00:00:04.620><c> your</c><00:00:04.920><c> laptop</c>

00:00:05.870 --> 00:00:05.880 align:start position:0%
locally on your laptop
 

00:00:05.880 --> 00:00:09.290 align:start position:0%
locally on your laptop
Step<00:00:06.299><c> 1</c><00:00:06.480><c> install</c><00:00:07.259><c> olama</c><00:00:08.040><c> there</c><00:00:08.820><c> are</c><00:00:08.940><c> lots</c><00:00:09.240><c> of</c>

00:00:09.290 --> 00:00:09.300 align:start position:0%
Step 1 install olama there are lots of
 

00:00:09.300 --> 00:00:11.450 align:start position:0%
Step 1 install olama there are lots of
tools<00:00:09.660><c> that</c><00:00:09.840><c> require</c><00:00:10.080><c> a</c><00:00:10.620><c> dozen</c><00:00:10.980><c> complicated</c>

00:00:11.450 --> 00:00:11.460 align:start position:0%
tools that require a dozen complicated
 

00:00:11.460 --> 00:00:13.789 align:start position:0%
tools that require a dozen complicated
processes<00:00:12.000><c> but</c><00:00:12.240><c> with</c><00:00:12.480><c> olama</c><00:00:12.900><c> you</c><00:00:13.440><c> just</c><00:00:13.620><c> visit</c>

00:00:13.789 --> 00:00:13.799 align:start position:0%
processes but with olama you just visit
 

00:00:13.799 --> 00:00:16.310 align:start position:0%
processes but with olama you just visit
olama.ai<00:00:14.759><c> and</c><00:00:15.240><c> click</c><00:00:15.480><c> the</c><00:00:15.599><c> download</c><00:00:15.780><c> button</c>

00:00:16.310 --> 00:00:16.320 align:start position:0%
olama.ai and click the download button
 

00:00:16.320 --> 00:00:19.250 align:start position:0%
olama.ai and click the download button
it's<00:00:16.980><c> a</c><00:00:17.220><c> tiny</c><00:00:17.640><c> file</c><00:00:17.820><c> so</c><00:00:18.180><c> should</c><00:00:18.480><c> just</c><00:00:18.720><c> take</c><00:00:19.020><c> a</c>

00:00:19.250 --> 00:00:19.260 align:start position:0%
it's a tiny file so should just take a
 

00:00:19.260 --> 00:00:21.470 align:start position:0%
it's a tiny file so should just take a
few<00:00:19.440><c> seconds</c><00:00:19.680><c> open</c><00:00:20.520><c> the</c><00:00:20.760><c> file</c><00:00:20.939><c> and</c><00:00:21.180><c> then</c><00:00:21.300><c> run</c>

00:00:21.470 --> 00:00:21.480 align:start position:0%
few seconds open the file and then run
 

00:00:21.480 --> 00:00:23.689 align:start position:0%
few seconds open the file and then run
the<00:00:21.720><c> app</c><00:00:21.900><c> inside</c><00:00:22.140><c> it</c><00:00:22.980><c> will</c><00:00:23.100><c> ask</c><00:00:23.279><c> for</c><00:00:23.460><c> some</c>

00:00:23.689 --> 00:00:23.699 align:start position:0%
the app inside it will ask for some
 

00:00:23.699 --> 00:00:25.370 align:start position:0%
the app inside it will ask for some
permissions<00:00:24.119><c> and</c><00:00:24.539><c> to</c><00:00:24.720><c> copy</c><00:00:24.960><c> to</c><00:00:25.140><c> the</c><00:00:25.260><c> right</c>

00:00:25.370 --> 00:00:25.380 align:start position:0%
permissions and to copy to the right
 

00:00:25.380 --> 00:00:27.830 align:start position:0%
permissions and to copy to the right
place<00:00:25.619><c> now</c><00:00:26.519><c> you</c><00:00:26.880><c> should</c><00:00:27.000><c> see</c><00:00:27.180><c> the</c><00:00:27.420><c> cute</c><00:00:27.660><c> little</c>

00:00:27.830 --> 00:00:27.840 align:start position:0%
place now you should see the cute little
 

00:00:27.840 --> 00:00:29.509 align:start position:0%
place now you should see the cute little
llama<00:00:28.199><c> in</c><00:00:28.439><c> your</c><00:00:28.560><c> menu</c><00:00:28.680><c> bar</c>

00:00:29.509 --> 00:00:29.519 align:start position:0%
llama in your menu bar
 

00:00:29.519 --> 00:00:33.889 align:start position:0%
llama in your menu bar
Step<00:00:30.119><c> 2</c><00:00:30.359><c> run</c><00:00:31.320><c> olama</c><00:00:31.859><c> the</c><00:00:32.640><c> menu</c><00:00:32.759><c> bar</c><00:00:33.059><c> app</c><00:00:33.300><c> runs</c><00:00:33.719><c> a</c>

00:00:33.889 --> 00:00:33.899 align:start position:0%
Step 2 run olama the menu bar app runs a
 

00:00:33.899 --> 00:00:36.110 align:start position:0%
Step 2 run olama the menu bar app runs a
server<00:00:34.260><c> that</c><00:00:34.739><c> developers</c><00:00:35.280><c> will</c><00:00:35.579><c> love</c><00:00:35.820><c> and</c>

00:00:36.110 --> 00:00:36.120 align:start position:0%
server that developers will love and
 

00:00:36.120 --> 00:00:38.150 align:start position:0%
server that developers will love and
will<00:00:36.300><c> ensure</c><00:00:36.600><c> olama</c><00:00:37.079><c> is</c><00:00:37.380><c> always</c><00:00:37.620><c> up</c><00:00:37.860><c> to</c><00:00:37.980><c> date</c>

00:00:38.150 --> 00:00:38.160 align:start position:0%
will ensure olama is always up to date
 

00:00:38.160 --> 00:00:40.369 align:start position:0%
will ensure olama is always up to date
but<00:00:38.760><c> for</c><00:00:38.940><c> now</c><00:00:39.120><c> you'll</c><00:00:39.719><c> work</c><00:00:39.840><c> with</c><00:00:39.960><c> a</c><00:00:40.140><c> llama</c>

00:00:40.369 --> 00:00:40.379 align:start position:0%
but for now you'll work with a llama
 

00:00:40.379 --> 00:00:42.049 align:start position:0%
but for now you'll work with a llama
from<00:00:40.620><c> the</c><00:00:40.739><c> command</c><00:00:41.040><c> line</c><00:00:41.160><c> so</c><00:00:41.640><c> open</c><00:00:41.820><c> the</c>

00:00:42.049 --> 00:00:42.059 align:start position:0%
from the command line so open the
 

00:00:42.059 --> 00:00:45.229 align:start position:0%
from the command line so open the
terminal<00:00:42.420><c> and</c><00:00:42.780><c> type</c><00:00:42.960><c> olama</c><00:00:43.440><c> run</c><00:00:43.800><c> Orca</c><00:00:44.280><c> this</c><00:00:45.120><c> is</c>

00:00:45.229 --> 00:00:45.239 align:start position:0%
terminal and type olama run Orca this is
 

00:00:45.239 --> 00:00:47.270 align:start position:0%
terminal and type olama run Orca this is
going<00:00:45.420><c> to</c><00:00:45.540><c> download</c><00:00:45.719><c> the</c><00:00:46.020><c> Orca</c><00:00:46.320><c> model</c><00:00:46.620><c> from</c><00:00:47.160><c> a</c>

00:00:47.270 --> 00:00:47.280 align:start position:0%
going to download the Orca model from a
 

00:00:47.280 --> 00:00:49.810 align:start position:0%
going to download the Orca model from a
website<00:00:47.640><c> with</c><00:00:47.879><c> the</c><00:00:48.059><c> crazy</c><00:00:48.239><c> name</c><00:00:48.600><c> hugging</c><00:00:49.320><c> face</c>

00:00:49.810 --> 00:00:49.820 align:start position:0%
website with the crazy name hugging face
 

00:00:49.820 --> 00:00:53.090 align:start position:0%
website with the crazy name hugging face
don't<00:00:50.820><c> ask</c><00:00:51.059><c> it's</c><00:00:51.840><c> a</c><00:00:52.020><c> relatively</c><00:00:52.440><c> small</c><00:00:52.680><c> file</c>

00:00:53.090 --> 00:00:53.100 align:start position:0%
don't ask it's a relatively small file
 

00:00:53.100 --> 00:00:54.709 align:start position:0%
don't ask it's a relatively small file
and<00:00:53.280><c> this</c><00:00:53.460><c> is</c><00:00:53.579><c> the</c><00:00:53.879><c> last</c><00:00:54.000><c> time</c><00:00:54.239><c> we</c><00:00:54.480><c> need</c><00:00:54.600><c> to</c>

00:00:54.709 --> 00:00:54.719 align:start position:0%
and this is the last time we need to
 

00:00:54.719 --> 00:00:57.290 align:start position:0%
and this is the last time we need to
touch<00:00:54.899><c> the</c><00:00:55.140><c> network</c><00:00:55.340><c> when</c><00:00:56.340><c> it's</c><00:00:56.579><c> done</c><00:00:56.879><c> it</c>

00:00:57.290 --> 00:00:57.300 align:start position:0%
touch the network when it's done it
 

00:00:57.300 --> 00:01:00.590 align:start position:0%
touch the network when it's done it
drops<00:00:57.719><c> you</c><00:00:57.840><c> into</c><00:00:58.140><c> a</c><00:00:58.559><c> interactive</c><00:00:59.100><c> prompt</c><00:01:00.000><c> step</c>

00:01:00.590 --> 00:01:00.600 align:start position:0%
drops you into a interactive prompt step
 

00:01:00.600 --> 00:01:03.770 align:start position:0%
drops you into a interactive prompt step
3<00:01:00.840><c> chat</c><00:01:01.559><c> with</c><00:01:01.680><c> Obama</c><00:01:02.100><c> ask</c><00:01:02.760><c> it</c><00:01:02.940><c> a</c><00:01:03.180><c> question</c><00:01:03.300><c> go</c>

00:01:03.770 --> 00:01:03.780 align:start position:0%
3 chat with Obama ask it a question go
 

00:01:03.780 --> 00:01:05.929 align:start position:0%
3 chat with Obama ask it a question go
wild<00:01:04.019><c> learn</c><00:01:04.440><c> to</c><00:01:04.619><c> code</c><00:01:04.799><c> learn</c><00:01:05.159><c> to</c><00:01:05.339><c> cook</c><00:01:05.519><c> learn</c>

00:01:05.929 --> 00:01:05.939 align:start position:0%
wild learn to code learn to cook learn
 

00:01:05.939 --> 00:01:08.510 align:start position:0%
wild learn to code learn to cook learn
particle<00:01:06.479><c> physics</c><00:01:06.900><c> the</c><00:01:07.680><c> world</c><00:01:07.860><c> is</c><00:01:08.280><c> your</c>

00:01:08.510 --> 00:01:08.520 align:start position:0%
particle physics the world is your
 

00:01:08.520 --> 00:01:10.429 align:start position:0%
particle physics the world is your
oyster<00:01:08.880><c> and</c><00:01:09.240><c> you</c><00:01:09.420><c> should</c><00:01:09.600><c> have</c><00:01:09.780><c> fun</c><00:01:10.020><c> with</c><00:01:10.260><c> it</c>

00:01:10.429 --> 00:01:10.439 align:start position:0%
oyster and you should have fun with it
 

00:01:10.439 --> 00:01:13.190 align:start position:0%
oyster and you should have fun with it
when<00:01:11.280><c> you're</c><00:01:11.400><c> done</c><00:01:11.640><c> press</c><00:01:11.939><c> Ctrl</c><00:01:12.299><c> C</c><00:01:12.540><c> to</c><00:01:12.840><c> quit</c>

00:01:13.190 --> 00:01:13.200 align:start position:0%
when you're done press Ctrl C to quit
 

00:01:13.200 --> 00:01:16.010 align:start position:0%
when you're done press Ctrl C to quit
and<00:01:13.439><c> then</c><00:01:13.619><c> run</c><00:01:13.799><c> olama</c><00:01:14.340><c> run</c><00:01:14.580><c> Orca</c><00:01:15.000><c> again</c><00:01:15.479><c> when</c>

00:01:16.010 --> 00:01:16.020 align:start position:0%
and then run olama run Orca again when
 

00:01:16.020 --> 00:01:18.289 align:start position:0%
and then run olama run Orca again when
you<00:01:16.140><c> want</c><00:01:16.320><c> to</c><00:01:16.439><c> chat</c><00:01:16.680><c> try</c><00:01:17.220><c> it</c><00:01:17.400><c> on</c><00:01:17.520><c> a</c><00:01:17.640><c> plane</c><00:01:17.880><c> or</c><00:01:18.180><c> a</c>

00:01:18.289 --> 00:01:18.299 align:start position:0%
you want to chat try it on a plane or a
 

00:01:18.299 --> 00:01:21.109 align:start position:0%
you want to chat try it on a plane or a
boat<00:01:18.420><c> or</c><00:01:18.840><c> a</c><00:01:19.020><c> train</c><00:01:19.200><c> or</c><00:01:19.979><c> anywhere</c><00:01:20.340><c> you</c><00:01:20.700><c> have</c><00:01:20.820><c> no</c>

00:01:21.109 --> 00:01:21.119 align:start position:0%
boat or a train or anywhere you have no
 

00:01:21.119 --> 00:01:24.050 align:start position:0%
boat or a train or anywhere you have no
network<00:01:21.360><c> it's</c><00:01:22.200><c> truly</c><00:01:22.619><c> amazing</c><00:01:23.159><c> and</c><00:01:23.700><c> we</c><00:01:23.939><c> can't</c>

00:01:24.050 --> 00:01:24.060 align:start position:0%
network it's truly amazing and we can't
 

00:01:24.060 --> 00:01:28.910 align:start position:0%
network it's truly amazing and we can't
wait<00:01:24.479><c> to</c><00:01:24.960><c> see</c><00:01:25.080><c> what</c><00:01:25.320><c> you</c><00:01:25.439><c> do</c><00:01:25.560><c> with</c><00:01:25.740><c> it</c><00:01:25.920><c> bye</c>

00:01:28.910 --> 00:01:28.920 align:start position:0%
wait to see what you do with it bye
 

00:01:28.920 --> 00:01:31.040 align:start position:0%
wait to see what you do with it bye
thank<00:01:29.040><c> you</c>

