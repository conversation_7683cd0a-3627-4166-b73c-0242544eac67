WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.869 align:start position:0%
 
let's<00:00:00.480><c> understand</c><00:00:00.719><c> bubble</c><00:00:01.319><c> sort</c><00:00:01.500><c> the</c><00:00:02.220><c> idea</c><00:00:02.520><c> is</c>

00:00:02.869 --> 00:00:02.879 align:start position:0%
let's understand bubble sort the idea is
 

00:00:02.879 --> 00:00:04.850 align:start position:0%
let's understand bubble sort the idea is
to<00:00:03.060><c> keep</c><00:00:03.240><c> swapping</c><00:00:03.720><c> adjacent</c><00:00:04.259><c> elements</c><00:00:04.680><c> when</c>

00:00:04.850 --> 00:00:04.860 align:start position:0%
to keep swapping adjacent elements when
 

00:00:04.860 --> 00:00:06.410 align:start position:0%
to keep swapping adjacent elements when
one<00:00:05.040><c> is</c><00:00:05.220><c> greater</c><00:00:05.460><c> than</c><00:00:05.580><c> the</c><00:00:05.759><c> other</c><00:00:05.880><c> and</c><00:00:06.240><c> move</c>

00:00:06.410 --> 00:00:06.420 align:start position:0%
one is greater than the other and move
 

00:00:06.420 --> 00:00:08.390 align:start position:0%
one is greater than the other and move
them<00:00:06.660><c> to</c><00:00:06.839><c> the</c><00:00:06.960><c> end</c><00:00:07.140><c> or</c><00:00:07.379><c> to</c><00:00:07.560><c> the</c><00:00:07.740><c> right</c><00:00:07.859><c> of</c><00:00:08.220><c> the</c>

00:00:08.390 --> 00:00:08.400 align:start position:0%
them to the end or to the right of the
 

00:00:08.400 --> 00:00:10.430 align:start position:0%
them to the end or to the right of the
array<00:00:08.700><c> and</c><00:00:09.059><c> we</c><00:00:09.240><c> do</c><00:00:09.480><c> that</c><00:00:09.720><c> a</c><00:00:10.139><c> number</c><00:00:10.260><c> of</c>

00:00:10.430 --> 00:00:10.440 align:start position:0%
array and we do that a number of
 

00:00:10.440 --> 00:00:12.589 align:start position:0%
array and we do that a number of
iterations<00:00:10.980><c> based</c><00:00:11.700><c> on</c><00:00:11.820><c> the</c><00:00:12.059><c> size</c><00:00:12.240><c> of</c><00:00:12.480><c> the</c>

00:00:12.589 --> 00:00:12.599 align:start position:0%
iterations based on the size of the
 

00:00:12.599 --> 00:00:14.629 align:start position:0%
iterations based on the size of the
array<00:00:12.900><c> let's</c><00:00:13.200><c> begin</c><00:00:13.440><c> visually</c><00:00:14.040><c> seeing</c><00:00:14.400><c> how</c>

00:00:14.629 --> 00:00:14.639 align:start position:0%
array let's begin visually seeing how
 

00:00:14.639 --> 00:00:16.250 align:start position:0%
array let's begin visually seeing how
this<00:00:14.759><c> works</c>

00:00:16.250 --> 00:00:16.260 align:start position:0%
this works
 

00:00:16.260 --> 00:00:18.290 align:start position:0%
this works
the<00:00:16.680><c> main</c><00:00:16.800><c> variable</c><00:00:17.160><c> we</c><00:00:17.520><c> have</c><00:00:17.640><c> here</c><00:00:17.940><c> which</c>

00:00:18.290 --> 00:00:18.300 align:start position:0%
the main variable we have here which
 

00:00:18.300 --> 00:00:21.230 align:start position:0%
the main variable we have here which
would<00:00:18.600><c> be</c><00:00:18.779><c> used</c><00:00:18.960><c> in</c><00:00:19.140><c> code</c><00:00:19.440><c> is</c><00:00:19.800><c> J</c><00:00:20.220><c> we</c><00:00:20.820><c> initially</c>

00:00:21.230 --> 00:00:21.240 align:start position:0%
would be used in code is J we initially
 

00:00:21.240 --> 00:00:24.170 align:start position:0%
would be used in code is J we initially
set<00:00:21.420><c> this</c><00:00:21.600><c> to</c><00:00:21.840><c> one</c><00:00:22.080><c> and</c><00:00:22.439><c> then</c><00:00:22.619><c> we</c><00:00:22.859><c> have</c><00:00:23.039><c> J</c><00:00:23.640><c> minus</c>

00:00:24.170 --> 00:00:24.180 align:start position:0%
set this to one and then we have J minus
 

00:00:24.180 --> 00:00:27.170 align:start position:0%
set this to one and then we have J minus
one<00:00:24.420><c> so</c><00:00:25.019><c> these</c><00:00:25.439><c> two</c><00:00:25.560><c> indices</c><00:00:26.039><c> are</c><00:00:26.939><c> what</c>

00:00:27.170 --> 00:00:27.180 align:start position:0%
one so these two indices are what
 

00:00:27.180 --> 00:00:28.490 align:start position:0%
one so these two indices are what
elements<00:00:27.599><c> we're</c><00:00:27.779><c> going</c><00:00:27.960><c> to</c><00:00:27.960><c> be</c><00:00:28.080><c> comparing</c>

00:00:28.490 --> 00:00:28.500 align:start position:0%
elements we're going to be comparing
 

00:00:28.500 --> 00:00:30.650 align:start position:0%
elements we're going to be comparing
against<00:00:28.619><c> as</c><00:00:29.220><c> we</c><00:00:29.519><c> go</c><00:00:29.760><c> through</c><00:00:29.939><c> the</c><00:00:30.180><c> array</c><00:00:30.420><c> the</c>

00:00:30.650 --> 00:00:30.660 align:start position:0%
against as we go through the array the
 

00:00:30.660 --> 00:00:32.810 align:start position:0%
against as we go through the array the
first<00:00:30.779><c> thing</c><00:00:30.960><c> we</c><00:00:31.080><c> do</c><00:00:31.260><c> is</c><00:00:31.859><c> compare</c><00:00:32.220><c> five</c><00:00:32.579><c> and</c>

00:00:32.810 --> 00:00:32.820 align:start position:0%
first thing we do is compare five and
 

00:00:32.820 --> 00:00:36.530 align:start position:0%
first thing we do is compare five and
two<00:00:33.000><c> is</c><00:00:33.780><c> five</c><00:00:34.079><c> greater</c><00:00:34.680><c> than</c><00:00:34.800><c> two</c><00:00:35.040><c> it</c><00:00:35.820><c> is</c><00:00:36.000><c> so</c><00:00:36.360><c> we</c>

00:00:36.530 --> 00:00:36.540 align:start position:0%
two is five greater than two it is so we
 

00:00:36.540 --> 00:00:38.630 align:start position:0%
two is five greater than two it is so we
swap<00:00:36.840><c> these</c><00:00:37.140><c> two</c><00:00:37.260><c> elements</c><00:00:37.739><c> and</c><00:00:38.219><c> then</c><00:00:38.399><c> we</c><00:00:38.520><c> move</c>

00:00:38.630 --> 00:00:38.640 align:start position:0%
swap these two elements and then we move
 

00:00:38.640 --> 00:00:41.450 align:start position:0%
swap these two elements and then we move
on<00:00:38.820><c> to</c><00:00:39.000><c> the</c><00:00:39.120><c> next</c><00:00:39.239><c> two</c><00:00:39.480><c> we</c><00:00:40.200><c> increment</c><00:00:40.620><c> J</c><00:00:40.980><c> now</c>

00:00:41.450 --> 00:00:41.460 align:start position:0%
on to the next two we increment J now
 

00:00:41.460 --> 00:00:43.970 align:start position:0%
on to the next two we increment J now
we're<00:00:41.640><c> looking</c><00:00:41.879><c> at</c><00:00:42.180><c> indexes</c><00:00:42.899><c> two</c><00:00:43.500><c> and</c><00:00:43.800><c> one</c>

00:00:43.970 --> 00:00:43.980 align:start position:0%
we're looking at indexes two and one
 

00:00:43.980 --> 00:00:46.369 align:start position:0%
we're looking at indexes two and one
which<00:00:44.280><c> hold</c><00:00:44.460><c> elements</c><00:00:44.940><c> five</c><00:00:45.300><c> and</c><00:00:45.540><c> four</c>

00:00:46.369 --> 00:00:46.379 align:start position:0%
which hold elements five and four
 

00:00:46.379 --> 00:00:49.430 align:start position:0%
which hold elements five and four
is<00:00:46.920><c> five</c><00:00:47.219><c> greater</c><00:00:47.700><c> than</c><00:00:47.760><c> four</c><00:00:48.059><c> it</c><00:00:48.660><c> is</c><00:00:48.840><c> so</c><00:00:49.260><c> we</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
is five greater than four it is so we
 

00:00:49.440 --> 00:00:50.510 align:start position:0%
is five greater than four it is so we
swap<00:00:49.739><c> them</c>

00:00:50.510 --> 00:00:50.520 align:start position:0%
swap them
 

00:00:50.520 --> 00:00:52.310 align:start position:0%
swap them
move<00:00:51.120><c> on</c><00:00:51.239><c> to</c><00:00:51.420><c> the</c><00:00:51.539><c> next</c><00:00:51.660><c> two</c><00:00:51.840><c> elements</c>

00:00:52.310 --> 00:00:52.320 align:start position:0%
move on to the next two elements
 

00:00:52.320 --> 00:00:54.290 align:start position:0%
move on to the next two elements
increment<00:00:53.280><c> J</c>

00:00:54.290 --> 00:00:54.300 align:start position:0%
increment J
 

00:00:54.300 --> 00:00:57.350 align:start position:0%
increment J
is<00:00:54.719><c> 5</c><00:00:54.960><c> greater</c><00:00:55.379><c> than</c><00:00:55.379><c> six</c><00:00:55.739><c> it</c><00:00:56.280><c> is</c><00:00:56.460><c> not</c><00:00:56.699><c> so</c><00:00:57.180><c> we</c>

00:00:57.350 --> 00:00:57.360 align:start position:0%
is 5 greater than six it is not so we
 

00:00:57.360 --> 00:01:00.110 align:start position:0%
is 5 greater than six it is not so we
just<00:00:57.600><c> move</c><00:00:57.960><c> on</c><00:00:58.199><c> without</c><00:00:58.620><c> swapping</c>

00:01:00.110 --> 00:01:00.120 align:start position:0%
just move on without swapping
 

00:01:00.120 --> 00:01:03.110 align:start position:0%
just move on without swapping
increment<00:01:00.660><c> J</c><00:01:00.899><c> again</c><00:01:01.140><c> now</c><00:01:01.739><c> a</c><00:01:02.039><c> 6</c><00:01:02.340><c> greater</c><00:01:03.000><c> than</c>

00:01:03.110 --> 00:01:03.120 align:start position:0%
increment J again now a 6 greater than
 

00:01:03.120 --> 00:01:06.289 align:start position:0%
increment J again now a 6 greater than
one<00:01:03.539><c> it</c><00:01:04.500><c> is</c><00:01:04.619><c> so</c><00:01:04.920><c> we</c><00:01:05.100><c> swap</c><00:01:05.460><c> these</c><00:01:05.760><c> two</c><00:01:05.880><c> elements</c>

00:01:06.289 --> 00:01:06.299 align:start position:0%
one it is so we swap these two elements
 

00:01:06.299 --> 00:01:09.350 align:start position:0%
one it is so we swap these two elements
and<00:01:06.960><c> then</c><00:01:07.080><c> we</c><00:01:07.260><c> move</c><00:01:07.439><c> on</c><00:01:07.619><c> to</c><00:01:07.740><c> the</c><00:01:07.860><c> next</c><00:01:07.979><c> two</c>

00:01:09.350 --> 00:01:09.360 align:start position:0%
and then we move on to the next two
 

00:01:09.360 --> 00:01:12.830 align:start position:0%
and then we move on to the next two
increment<00:01:09.960><c> J</c><00:01:10.260><c> is</c><00:01:10.979><c> 6</c><00:01:11.280><c> greater</c><00:01:11.820><c> than</c><00:01:11.880><c> three</c>

00:01:12.830 --> 00:01:12.840 align:start position:0%
increment J is 6 greater than three
 

00:01:12.840 --> 00:01:16.310 align:start position:0%
increment J is 6 greater than three
it<00:01:13.439><c> is</c><00:01:13.619><c> so</c><00:01:13.920><c> we</c><00:01:14.159><c> swap</c><00:01:14.520><c> them</c><00:01:15.180><c> and</c><00:01:15.720><c> now</c><00:01:15.840><c> we're</c><00:01:16.020><c> done</c>

00:01:16.310 --> 00:01:16.320 align:start position:0%
it is so we swap them and now we're done
 

00:01:16.320 --> 00:01:18.950 align:start position:0%
it is so we swap them and now we're done
with<00:01:16.619><c> this</c><00:01:16.799><c> iteration</c><00:01:17.280><c> we</c><00:01:17.880><c> can't</c><00:01:18.060><c> compare</c><00:01:18.600><c> any</c>

00:01:18.950 --> 00:01:18.960 align:start position:0%
with this iteration we can't compare any
 

00:01:18.960 --> 00:01:20.450 align:start position:0%
with this iteration we can't compare any
more<00:01:19.140><c> elements</c><00:01:19.500><c> we're</c><00:01:19.680><c> at</c><00:01:19.860><c> the</c><00:01:20.040><c> end</c><00:01:20.159><c> of</c><00:01:20.340><c> the</c>

00:01:20.450 --> 00:01:20.460 align:start position:0%
more elements we're at the end of the
 

00:01:20.460 --> 00:01:22.429 align:start position:0%
more elements we're at the end of the
array<00:01:20.759><c> now</c><00:01:21.119><c> what</c><00:01:21.299><c> this</c><00:01:21.420><c> iteration</c><00:01:21.900><c> has</c><00:01:22.259><c> told</c>

00:01:22.429 --> 00:01:22.439 align:start position:0%
array now what this iteration has told
 

00:01:22.439 --> 00:01:25.429 align:start position:0%
array now what this iteration has told
us<00:01:22.619><c> is</c><00:01:23.220><c> that</c><00:01:23.460><c> 6</c><00:01:23.759><c> is</c><00:01:24.060><c> the</c><00:01:24.360><c> greatest</c><00:01:24.540><c> element</c><00:01:24.900><c> and</c>

00:01:25.429 --> 00:01:25.439 align:start position:0%
us is that 6 is the greatest element and
 

00:01:25.439 --> 00:01:27.710 align:start position:0%
us is that 6 is the greatest element and
it's<00:01:25.560><c> now</c><00:01:25.740><c> in</c><00:01:25.920><c> the</c><00:01:26.100><c> correct</c><00:01:26.400><c> place</c><00:01:26.820><c> in</c><00:01:27.540><c> the</c>

00:01:27.710 --> 00:01:27.720 align:start position:0%
it's now in the correct place in the
 

00:01:27.720 --> 00:01:29.450 align:start position:0%
it's now in the correct place in the
array<00:01:28.080><c> it's</c><00:01:28.439><c> kind</c><00:01:28.619><c> of</c><00:01:28.740><c> like</c><00:01:28.860><c> we're</c><00:01:29.040><c> sorting</c>

00:01:29.450 --> 00:01:29.460 align:start position:0%
array it's kind of like we're sorting
 

00:01:29.460 --> 00:01:31.730 align:start position:0%
array it's kind of like we're sorting
them<00:01:29.640><c> from</c><00:01:30.240><c> the</c><00:01:30.420><c> highest</c><00:01:30.780><c> value</c><00:01:30.960><c> to</c><00:01:31.380><c> the</c><00:01:31.560><c> least</c>

00:01:31.730 --> 00:01:31.740 align:start position:0%
them from the highest value to the least
 

00:01:31.740 --> 00:01:34.070 align:start position:0%
them from the highest value to the least
value<00:01:32.040><c> now</c><00:01:32.580><c> that</c><00:01:32.759><c> was</c><00:01:32.880><c> one</c><00:01:33.000><c> iteration</c><00:01:33.479><c> we</c><00:01:33.900><c> have</c>

00:01:34.070 --> 00:01:34.080 align:start position:0%
value now that was one iteration we have
 

00:01:34.080 --> 00:01:35.990 align:start position:0%
value now that was one iteration we have
to<00:01:34.200><c> do</c><00:01:34.320><c> this</c><00:01:34.380><c> a</c><00:01:34.619><c> few</c><00:01:34.740><c> more</c><00:01:34.920><c> times</c>

00:01:35.990 --> 00:01:36.000 align:start position:0%
to do this a few more times
 

00:01:36.000 --> 00:01:37.850 align:start position:0%
to do this a few more times
so<00:01:36.420><c> we</c><00:01:36.600><c> restart</c>

00:01:37.850 --> 00:01:37.860 align:start position:0%
so we restart
 

00:01:37.860 --> 00:01:41.450 align:start position:0%
so we restart
we<00:01:38.820><c> reset</c><00:01:39.299><c> the</c><00:01:39.479><c> variable</c><00:01:39.720><c> J</c><00:01:40.020><c> back</c><00:01:40.320><c> to</c><00:01:40.500><c> one</c><00:01:40.680><c> and</c>

00:01:41.450 --> 00:01:41.460 align:start position:0%
we reset the variable J back to one and
 

00:01:41.460 --> 00:01:43.730 align:start position:0%
we reset the variable J back to one and
now<00:01:41.640><c> we</c><00:01:41.759><c> compare</c><00:01:42.060><c> two</c><00:01:42.299><c> and</c><00:01:42.540><c> four</c><00:01:42.720><c> two</c><00:01:43.259><c> is</c><00:01:43.560><c> not</c>

00:01:43.730 --> 00:01:43.740 align:start position:0%
now we compare two and four two is not
 

00:01:43.740 --> 00:01:46.550 align:start position:0%
now we compare two and four two is not
greater<00:01:44.159><c> than</c><00:01:44.280><c> 4</c><00:01:44.520><c> so</c><00:01:44.880><c> we</c><00:01:45.060><c> don't</c><00:01:45.180><c> do</c><00:01:45.420><c> anything</c>

00:01:46.550 --> 00:01:46.560 align:start position:0%
greater than 4 so we don't do anything
 

00:01:46.560 --> 00:01:49.010 align:start position:0%
greater than 4 so we don't do anything
we<00:01:46.860><c> move</c><00:01:47.100><c> on</c><00:01:47.220><c> to</c><00:01:47.340><c> the</c><00:01:47.460><c> next</c><00:01:47.579><c> two</c><00:01:47.700><c> elements</c>

00:01:49.010 --> 00:01:49.020 align:start position:0%
we move on to the next two elements
 

00:01:49.020 --> 00:01:51.950 align:start position:0%
we move on to the next two elements
or<00:01:49.560><c> is</c><00:01:49.860><c> not</c><00:01:50.100><c> greater</c><00:01:50.399><c> than</c><00:01:50.520><c> 5</c><00:01:50.820><c> so</c><00:01:51.360><c> we</c><00:01:51.600><c> move</c><00:01:51.780><c> on</c>

00:01:51.950 --> 00:01:51.960 align:start position:0%
or is not greater than 5 so we move on
 

00:01:51.960 --> 00:01:54.590 align:start position:0%
or is not greater than 5 so we move on
again<00:01:52.140><c> now</c><00:01:52.680><c> is</c><00:01:52.920><c> 5</c><00:01:53.159><c> greater</c><00:01:53.520><c> than</c><00:01:53.579><c> one</c><00:01:53.820><c> yes</c><00:01:54.360><c> so</c>

00:01:54.590 --> 00:01:54.600 align:start position:0%
again now is 5 greater than one yes so
 

00:01:54.600 --> 00:01:57.170 align:start position:0%
again now is 5 greater than one yes so
we<00:01:54.720><c> go</c><00:01:54.899><c> ahead</c><00:01:55.020><c> and</c><00:01:55.079><c> swap</c><00:01:55.380><c> these</c><00:01:55.619><c> two</c><00:01:55.799><c> elements</c>

00:01:57.170 --> 00:01:57.180 align:start position:0%
we go ahead and swap these two elements
 

00:01:57.180 --> 00:01:59.450 align:start position:0%
we go ahead and swap these two elements
and<00:01:57.540><c> we</c><00:01:57.720><c> move</c><00:01:57.840><c> on</c><00:01:57.960><c> to</c><00:01:58.140><c> the</c><00:01:58.259><c> next</c><00:01:58.380><c> two</c>

00:01:59.450 --> 00:01:59.460 align:start position:0%
and we move on to the next two
 

00:01:59.460 --> 00:02:02.749 align:start position:0%
and we move on to the next two
is<00:01:59.880><c> five</c><00:02:00.119><c> greater</c><00:02:00.540><c> than</c><00:02:00.600><c> three</c><00:02:00.979><c> it</c><00:02:01.979><c> is</c><00:02:02.280><c> so</c><00:02:02.640><c> we</c>

00:02:02.749 --> 00:02:02.759 align:start position:0%
is five greater than three it is so we
 

00:02:02.759 --> 00:02:04.249 align:start position:0%
is five greater than three it is so we
go<00:02:02.880><c> ahead</c><00:02:03.000><c> and</c><00:02:03.060><c> swap</c><00:02:03.360><c> these</c><00:02:03.600><c> two</c><00:02:03.720><c> elements</c><00:02:04.079><c> as</c>

00:02:04.249 --> 00:02:04.259 align:start position:0%
go ahead and swap these two elements as
 

00:02:04.259 --> 00:02:07.550 align:start position:0%
go ahead and swap these two elements as
well<00:02:04.500><c> and</c><00:02:05.040><c> now</c><00:02:05.340><c> 5</c><00:02:06.240><c> is</c><00:02:06.600><c> in</c><00:02:06.780><c> the</c><00:02:06.960><c> correct</c><00:02:07.259><c> spot</c>

00:02:07.550 --> 00:02:07.560 align:start position:0%
well and now 5 is in the correct spot
 

00:02:07.560 --> 00:02:09.889 align:start position:0%
well and now 5 is in the correct spot
now<00:02:08.459><c> we</c><00:02:08.580><c> have</c><00:02:08.700><c> two</c><00:02:08.880><c> elements</c><00:02:09.239><c> five</c><00:02:09.479><c> and</c><00:02:09.660><c> six</c>

00:02:09.889 --> 00:02:09.899 align:start position:0%
now we have two elements five and six
 

00:02:09.899 --> 00:02:12.110 align:start position:0%
now we have two elements five and six
which<00:02:10.440><c> are</c><00:02:10.679><c> the</c><00:02:10.920><c> two</c><00:02:11.220><c> highest</c><00:02:11.640><c> elements</c><00:02:12.000><c> in</c>

00:02:12.110 --> 00:02:12.120 align:start position:0%
which are the two highest elements in
 

00:02:12.120 --> 00:02:14.630 align:start position:0%
which are the two highest elements in
this<00:02:12.239><c> array</c><00:02:12.540><c> at</c><00:02:13.319><c> the</c><00:02:13.379><c> end</c><00:02:13.500><c> of</c><00:02:13.680><c> the</c><00:02:13.800><c> race</c><00:02:13.980><c> sorted</c>

00:02:14.630 --> 00:02:14.640 align:start position:0%
this array at the end of the race sorted
 

00:02:14.640 --> 00:02:16.729 align:start position:0%
this array at the end of the race sorted
and<00:02:15.180><c> we're</c><00:02:15.360><c> going</c><00:02:15.540><c> to</c><00:02:15.660><c> just</c><00:02:15.840><c> keep</c><00:02:16.080><c> doing</c><00:02:16.379><c> this</c>

00:02:16.729 --> 00:02:16.739 align:start position:0%
and we're going to just keep doing this
 

00:02:16.739 --> 00:02:19.130 align:start position:0%
and we're going to just keep doing this
for<00:02:17.340><c> all</c><00:02:17.580><c> of</c><00:02:17.700><c> the</c><00:02:17.879><c> elements</c><00:02:18.300><c> now</c><00:02:18.780><c> with</c><00:02:18.959><c> this</c>

00:02:19.130 --> 00:02:19.140 align:start position:0%
for all of the elements now with this
 

00:02:19.140 --> 00:02:22.250 align:start position:0%
for all of the elements now with this
sorting<00:02:19.560><c> method</c><00:02:19.860><c> the</c><00:02:20.459><c> time</c><00:02:20.700><c> complexity</c><00:02:21.360><c> is</c><00:02:21.900><c> N</c>

00:02:22.250 --> 00:02:22.260 align:start position:0%
sorting method the time complexity is N
 

00:02:22.260 --> 00:02:24.110 align:start position:0%
sorting method the time complexity is N
squared<00:02:22.739><c> because</c><00:02:23.040><c> we</c><00:02:23.340><c> have</c><00:02:23.400><c> an</c><00:02:23.580><c> outer</c><00:02:23.879><c> loop</c>

00:02:24.110 --> 00:02:24.120 align:start position:0%
squared because we have an outer loop
 

00:02:24.120 --> 00:02:25.610 align:start position:0%
squared because we have an outer loop
that<00:02:24.239><c> goes</c><00:02:24.480><c> through</c><00:02:24.599><c> the</c><00:02:24.720><c> whole</c><00:02:24.840><c> array</c><00:02:25.260><c> and</c>

00:02:25.610 --> 00:02:25.620 align:start position:0%
that goes through the whole array and
 

00:02:25.620 --> 00:02:27.350 align:start position:0%
that goes through the whole array and
amount<00:02:25.980><c> of</c><00:02:26.040><c> times</c><00:02:26.220><c> based</c><00:02:26.640><c> on</c><00:02:26.700><c> the</c><00:02:26.879><c> size</c><00:02:27.060><c> of</c><00:02:27.239><c> the</c>

00:02:27.350 --> 00:02:27.360 align:start position:0%
amount of times based on the size of the
 

00:02:27.360 --> 00:02:29.570 align:start position:0%
amount of times based on the size of the
array<00:02:27.660><c> and</c><00:02:28.140><c> we</c><00:02:28.260><c> have</c><00:02:28.440><c> to</c><00:02:28.560><c> go</c><00:02:28.860><c> inside</c><00:02:29.220><c> of</c><00:02:29.459><c> that</c>

00:02:29.570 --> 00:02:29.580 align:start position:0%
array and we have to go inside of that
 

00:02:29.580 --> 00:02:32.510 align:start position:0%
array and we have to go inside of that
for<00:02:29.819><c> Loop</c><00:02:30.180><c> in</c><00:02:30.900><c> each</c><00:02:31.140><c> iteration</c><00:02:31.680><c> we</c><00:02:32.280><c> have</c><00:02:32.400><c> to</c>

00:02:32.510 --> 00:02:32.520 align:start position:0%
for Loop in each iteration we have to
 

00:02:32.520 --> 00:02:34.190 align:start position:0%
for Loop in each iteration we have to
compare<00:02:32.879><c> all</c><00:02:33.180><c> these</c><00:02:33.360><c> elements</c><00:02:33.720><c> against</c><00:02:33.900><c> each</c>

00:02:34.190 --> 00:02:34.200 align:start position:0%
compare all these elements against each
 

00:02:34.200 --> 00:02:36.350 align:start position:0%
compare all these elements against each
other<00:02:34.379><c> to</c><00:02:35.040><c> help</c><00:02:35.160><c> sort</c><00:02:35.459><c> them</c>

00:02:36.350 --> 00:02:36.360 align:start position:0%
other to help sort them
 

00:02:36.360 --> 00:02:38.390 align:start position:0%
other to help sort them
even<00:02:36.959><c> though</c><00:02:37.200><c> this</c><00:02:37.680><c> is</c><00:02:37.860><c> probably</c><00:02:38.040><c> the</c><00:02:38.340><c> first</c>

00:02:38.390 --> 00:02:38.400 align:start position:0%
even though this is probably the first
 

00:02:38.400 --> 00:02:40.070 align:start position:0%
even though this is probably the first
sorting<00:02:38.819><c> algorithm</c><00:02:39.180><c> that</c><00:02:39.360><c> you'll</c><00:02:39.540><c> be</c><00:02:39.660><c> exposed</c>

00:02:40.070 --> 00:02:40.080 align:start position:0%
sorting algorithm that you'll be exposed
 

00:02:40.080 --> 00:02:42.530 align:start position:0%
sorting algorithm that you'll be exposed
to<00:02:40.260><c> that</c><00:02:40.860><c> doesn't</c><00:02:41.040><c> mean</c><00:02:41.220><c> it's</c><00:02:41.400><c> always</c><00:02:41.580><c> easy</c><00:02:41.940><c> it</c>

00:02:42.530 --> 00:02:42.540 align:start position:0%
to that doesn't mean it's always easy it
 

00:02:42.540 --> 00:02:44.449 align:start position:0%
to that doesn't mean it's always easy it
takes<00:02:42.660><c> me</c><00:02:42.900><c> multiple</c><00:02:43.379><c> times</c><00:02:43.680><c> to</c><00:02:44.040><c> re-watch</c>

00:02:44.449 --> 00:02:44.459 align:start position:0%
takes me multiple times to re-watch
 

00:02:44.459 --> 00:02:46.369 align:start position:0%
takes me multiple times to re-watch
something<00:02:44.700><c> and</c><00:02:45.300><c> practice</c><00:02:45.480><c> it</c><00:02:45.780><c> before</c><00:02:46.019><c> I</c><00:02:46.260><c> can</c>

00:02:46.369 --> 00:02:46.379 align:start position:0%
something and practice it before I can
 

00:02:46.379 --> 00:02:47.869 align:start position:0%
something and practice it before I can
get<00:02:46.560><c> it</c><00:02:46.739><c> so</c><00:02:47.040><c> if</c><00:02:47.160><c> you</c><00:02:47.280><c> have</c><00:02:47.340><c> any</c><00:02:47.459><c> issues</c>

00:02:47.869 --> 00:02:47.879 align:start position:0%
get it so if you have any issues
 

00:02:47.879 --> 00:02:50.030 align:start position:0%
get it so if you have any issues
re-watch<00:02:48.540><c> it</c><00:02:48.720><c> multiple</c><00:02:49.080><c> times</c><00:02:49.200><c> or</c><00:02:49.800><c> you</c><00:02:49.920><c> can</c>

00:02:50.030 --> 00:02:50.040 align:start position:0%
re-watch it multiple times or you can
 

00:02:50.040 --> 00:02:51.530 align:start position:0%
re-watch it multiple times or you can
leave<00:02:50.160><c> comments</c><00:02:50.580><c> Down</c><00:02:50.700><c> Below</c><00:02:50.879><c> in</c><00:02:51.360><c> the</c>

00:02:51.530 --> 00:02:51.540 align:start position:0%
leave comments Down Below in the
 

00:02:51.540 --> 00:02:52.790 align:start position:0%
leave comments Down Below in the
meantime<00:02:51.840><c> here</c><00:02:52.080><c> are</c><00:02:52.200><c> some</c><00:02:52.260><c> other</c><00:02:52.379><c> videos</c><00:02:52.560><c> you</c>

00:02:52.790 --> 00:02:52.800 align:start position:0%
meantime here are some other videos you
 

00:02:52.800 --> 00:02:56.000 align:start position:0%
meantime here are some other videos you
can<00:02:52.920><c> watch</c><00:02:53.040><c> I'll</c><00:02:53.519><c> talk</c><00:02:53.700><c> to</c><00:02:53.819><c> you</c><00:02:53.879><c> later</c>

