WEBVTT
Kind: captions
Language: en

00:00:02.080 --> 00:00:04.670 align:start position:0%
 
hi<00:00:02.280><c> everybody</c><00:00:02.800><c> I'm</c><00:00:02.960><c> lri</c><00:00:03.439><c> devell</c><00:00:04.080><c> at</c><00:00:04.319><c> llama</c>

00:00:04.670 --> 00:00:04.680 align:start position:0%
hi everybody I'm lri devell at llama
 

00:00:04.680 --> 00:00:07.630 align:start position:0%
hi everybody I'm lri devell at llama
index<00:00:05.440><c> and</c><00:00:05.680><c> I'm</c><00:00:05.839><c> here</c><00:00:06.040><c> today</c><00:00:06.359><c> to</c><00:00:07.080><c> demonstrate</c>

00:00:07.630 --> 00:00:07.640 align:start position:0%
index and I'm here today to demonstrate
 

00:00:07.640 --> 00:00:10.110 align:start position:0%
index and I'm here today to demonstrate
some<00:00:07.799><c> of</c><00:00:07.919><c> the</c><00:00:08.080><c> features</c><00:00:08.360><c> of</c><00:00:08.519><c> Lop</c><00:00:08.960><c> parse</c><00:00:09.360><c> are</c>

00:00:10.110 --> 00:00:10.120 align:start position:0%
some of the features of Lop parse are
 

00:00:10.120 --> 00:00:12.950 align:start position:0%
some of the features of Lop parse are
industry-leading<00:00:11.120><c> uh</c><00:00:11.320><c> PDF</c><00:00:11.960><c> parser</c><00:00:12.480><c> and</c><00:00:12.719><c> other</c>

00:00:12.950 --> 00:00:12.960 align:start position:0%
industry-leading uh PDF parser and other
 

00:00:12.960 --> 00:00:14.470 align:start position:0%
industry-leading uh PDF parser and other
document<00:00:13.360><c> type</c>

00:00:14.470 --> 00:00:14.480 align:start position:0%
document type
 

00:00:14.480 --> 00:00:18.349 align:start position:0%
document type
parser<00:00:15.480><c> uh</c><00:00:16.240><c> llap</c><00:00:16.720><c> parse</c><00:00:17.199><c> is</c><00:00:17.640><c> really</c><00:00:17.880><c> simple</c><00:00:18.160><c> to</c>

00:00:18.349 --> 00:00:18.359 align:start position:0%
parser uh llap parse is really simple to
 

00:00:18.359 --> 00:00:20.750 align:start position:0%
parser uh llap parse is really simple to
use<00:00:18.640><c> it's</c><00:00:18.760><c> an</c><00:00:18.960><c> API</c><00:00:19.480><c> that's</c><00:00:19.720><c> built</c><00:00:20.039><c> into</c><00:00:20.400><c> to</c>

00:00:20.750 --> 00:00:20.760 align:start position:0%
use it's an API that's built into to
 

00:00:20.760 --> 00:00:24.750 align:start position:0%
use it's an API that's built into to
llama<00:00:21.119><c> index</c><00:00:22.160><c> so</c><00:00:23.160><c> uh</c><00:00:23.760><c> let's</c><00:00:24.080><c> look</c><00:00:24.240><c> at</c><00:00:24.439><c> how</c><00:00:24.599><c> you</c>

00:00:24.750 --> 00:00:24.760 align:start position:0%
llama index so uh let's look at how you
 

00:00:24.760 --> 00:00:26.310 align:start position:0%
llama index so uh let's look at how you
use<00:00:24.920><c> it</c><00:00:25.279><c> in</c><00:00:25.439><c> this</c>

00:00:26.310 --> 00:00:26.320 align:start position:0%
use it in this
 

00:00:26.320 --> 00:00:30.589 align:start position:0%
use it in this
notebook<00:00:27.320><c> uh</c><00:00:27.720><c> first</c><00:00:28.240><c> you</c><00:00:28.960><c> pip</c><00:00:29.279><c> install</c><00:00:30.439><c> uh</c>

00:00:30.589 --> 00:00:30.599 align:start position:0%
notebook uh first you pip install uh
 

00:00:30.599 --> 00:00:32.589 align:start position:0%
notebook uh first you pip install uh
llama<00:00:30.920><c> index</c><00:00:31.279><c> and</c><00:00:31.400><c> llama</c><00:00:31.759><c> parse</c><00:00:32.239><c> as</c><00:00:32.439><c> two</c>

00:00:32.589 --> 00:00:32.599 align:start position:0%
llama index and llama parse as two
 

00:00:32.599 --> 00:00:34.510 align:start position:0%
llama index and llama parse as two
separate

00:00:34.510 --> 00:00:34.520 align:start position:0%
separate
 

00:00:34.520 --> 00:00:38.869 align:start position:0%
separate
packages<00:00:35.719><c> uh</c><00:00:36.719><c> and</c><00:00:37.360><c> now</c><00:00:37.600><c> let's</c><00:00:38.200><c> get</c><00:00:38.399><c> a</c><00:00:38.559><c> really</c>

00:00:38.869 --> 00:00:38.879 align:start position:0%
packages uh and now let's get a really
 

00:00:38.879 --> 00:00:42.069 align:start position:0%
packages uh and now let's get a really
complicated<00:00:39.520><c> PDF</c><00:00:40.480><c> that</c><00:00:40.640><c> we</c><00:00:40.719><c> can</c><00:00:40.920><c> look</c><00:00:41.120><c> at</c><00:00:41.600><c> and</c>

00:00:42.069 --> 00:00:42.079 align:start position:0%
complicated PDF that we can look at and
 

00:00:42.079 --> 00:00:44.670 align:start position:0%
complicated PDF that we can look at and
see<00:00:43.079><c> uh</c><00:00:43.239><c> how</c><00:00:43.399><c> the</c><00:00:43.480><c> features</c><00:00:43.800><c> of</c><00:00:43.960><c> LL</c><00:00:44.280><c> pars</c><00:00:44.559><c> come</c>

00:00:44.670 --> 00:00:44.680 align:start position:0%
see uh how the features of LL pars come
 

00:00:44.680 --> 00:00:47.310 align:start position:0%
see uh how the features of LL pars come
into<00:00:45.160><c> play</c><00:00:46.160><c> uh</c><00:00:46.320><c> the</c><00:00:46.440><c> one</c><00:00:46.559><c> that</c><00:00:46.680><c> we</c><00:00:46.879><c> picked</c><00:00:47.120><c> for</c>

00:00:47.310 --> 00:00:47.320 align:start position:0%
into play uh the one that we picked for
 

00:00:47.320 --> 00:00:51.430 align:start position:0%
into play uh the one that we picked for
this<00:00:47.640><c> example</c><00:00:48.719><c> is</c><00:00:49.719><c> uh</c><00:00:49.920><c> an</c><00:00:50.120><c> insurance</c><00:00:50.680><c> policy</c>

00:00:51.430 --> 00:00:51.440 align:start position:0%
this example is uh an insurance policy
 

00:00:51.440 --> 00:00:52.830 align:start position:0%
this example is uh an insurance policy
from<00:00:51.640><c> the</c>

00:00:52.830 --> 00:00:52.840 align:start position:0%
from the
 

00:00:52.840 --> 00:00:56.910 align:start position:0%
from the
irdia<00:00:53.879><c> um</c><00:00:54.879><c> it's</c><00:00:55.120><c> got</c><00:00:55.359><c> lots</c><00:00:55.600><c> of</c><00:00:56.160><c> Clauses</c><00:00:56.719><c> and</c>

00:00:56.910 --> 00:00:56.920 align:start position:0%
irdia um it's got lots of Clauses and
 

00:00:56.920 --> 00:00:59.229 align:start position:0%
irdia um it's got lots of Clauses and
exclusions<00:00:57.559><c> and</c><00:00:57.800><c> coverage</c><00:00:58.239><c> that</c><00:00:58.359><c> it</c><00:00:58.879><c> provides</c>

00:00:59.229 --> 00:00:59.239 align:start position:0%
exclusions and coverage that it provides
 

00:00:59.239 --> 00:01:00.630 align:start position:0%
exclusions and coverage that it provides
and<00:00:59.399><c> doesn't</c><00:00:59.640><c> provide</c><00:01:00.120><c> and</c><00:01:00.199><c> it's</c><00:01:00.280><c> a</c><00:01:00.440><c> really</c>

00:01:00.630 --> 00:01:00.640 align:start position:0%
and doesn't provide and it's a really
 

00:01:00.640 --> 00:01:03.670 align:start position:0%
and doesn't provide and it's a really
great<00:01:00.960><c> example</c><00:01:01.960><c> of</c><00:01:02.399><c> how</c><00:01:02.559><c> you</c><00:01:02.680><c> can</c><00:01:02.800><c> use</c><00:01:02.960><c> an</c><00:01:03.120><c> llm</c>

00:01:03.670 --> 00:01:03.680 align:start position:0%
great example of how you can use an llm
 

00:01:03.680 --> 00:01:08.749 align:start position:0%
great example of how you can use an llm
to<00:01:03.840><c> simplify</c><00:01:04.320><c> your</c><00:01:04.600><c> life</c><00:01:05.560><c> by</c><00:01:06.560><c> uh</c><00:01:07.400><c> turning</c><00:01:07.880><c> this</c>

00:01:08.749 --> 00:01:08.759 align:start position:0%
to simplify your life by uh turning this
 

00:01:08.759 --> 00:01:11.950 align:start position:0%
to simplify your life by uh turning this
pile<00:01:09.000><c> of</c><00:01:09.200><c> really</c><00:01:09.479><c> dense</c><00:01:09.960><c> text</c><00:01:10.600><c> into</c><00:01:10.880><c> a</c><00:01:11.040><c> summary</c>

00:01:11.950 --> 00:01:11.960 align:start position:0%
pile of really dense text into a summary
 

00:01:11.960 --> 00:01:15.190 align:start position:0%
pile of really dense text into a summary
of<00:01:12.200><c> what</c><00:01:12.360><c> it</c><00:01:12.560><c> actually</c><00:01:12.799><c> means</c><00:01:13.119><c> for</c><00:01:13.759><c> you</c><00:01:14.759><c> uh</c><00:01:14.960><c> so</c>

00:01:15.190 --> 00:01:15.200 align:start position:0%
of what it actually means for you uh so
 

00:01:15.200 --> 00:01:16.830 align:start position:0%
of what it actually means for you uh so
what<00:01:15.280><c> we</c><00:01:15.400><c> do</c><00:01:15.560><c> in</c><00:01:15.720><c> this</c><00:01:15.960><c> cell</c><00:01:16.119><c> is</c><00:01:16.240><c> we</c><00:01:16.400><c> download</c>

00:01:16.830 --> 00:01:16.840 align:start position:0%
what we do in this cell is we download
 

00:01:16.840 --> 00:01:17.749 align:start position:0%
what we do in this cell is we download
that

00:01:17.749 --> 00:01:17.759 align:start position:0%
that
 

00:01:17.759 --> 00:01:19.350 align:start position:0%
that
policy

00:01:19.350 --> 00:01:19.360 align:start position:0%
policy
 

00:01:19.360 --> 00:01:23.350 align:start position:0%
policy
uh<00:01:20.360><c> and</c><00:01:20.560><c> then</c><00:01:21.439><c> in</c><00:01:21.600><c> this</c><00:01:21.799><c> cell</c><00:01:22.040><c> we're</c><00:01:22.320><c> just</c><00:01:23.159><c> uh</c>

00:01:23.350 --> 00:01:23.360 align:start position:0%
uh and then in this cell we're just uh
 

00:01:23.360 --> 00:01:25.270 align:start position:0%
uh and then in this cell we're just uh
initializing<00:01:24.079><c> the</c><00:01:24.240><c> async</c><00:01:24.759><c> things</c><00:01:25.000><c> that</c><00:01:25.119><c> you</c>

00:01:25.270 --> 00:01:25.280 align:start position:0%
initializing the async things that you
 

00:01:25.280 --> 00:01:30.950 align:start position:0%
initializing the async things that you
need<00:01:25.600><c> to</c><00:01:25.880><c> get</c><00:01:26.479><c> a</c><00:01:27.840><c> uh</c><00:01:28.840><c> notebook</c><00:01:29.280><c> to</c><00:01:29.439><c> work</c>

00:01:30.950 --> 00:01:30.960 align:start position:0%
need to get a uh notebook to work
 

00:01:30.960 --> 00:01:32.910 align:start position:0%
need to get a uh notebook to work
uh<00:01:31.200><c> and</c><00:01:31.320><c> in</c><00:01:31.520><c> this</c><00:01:31.680><c> one</c><00:01:32.079><c> we</c><00:01:32.200><c> are</c><00:01:32.360><c> just</c><00:01:32.560><c> pulling</c>

00:01:32.910 --> 00:01:32.920 align:start position:0%
uh and in this one we are just pulling
 

00:01:32.920 --> 00:01:36.510 align:start position:0%
uh and in this one we are just pulling
in<00:01:33.680><c> uh</c><00:01:33.880><c> two</c><00:01:34.159><c> API</c><00:01:34.600><c> Keys</c><00:01:35.079><c> we</c><00:01:35.200><c> need</c><00:01:35.560><c> the</c><00:01:35.799><c> open</c><00:01:36.079><c> AI</c>

00:01:36.510 --> 00:01:36.520 align:start position:0%
in uh two API Keys we need the open AI
 

00:01:36.520 --> 00:01:40.429 align:start position:0%
in uh two API Keys we need the open AI
API<00:01:36.920><c> key</c><00:01:37.399><c> to</c><00:01:37.560><c> do</c><00:01:38.240><c> parsing</c><00:01:39.240><c> uh</c><00:01:39.399><c> and</c><00:01:39.560><c> we</c><00:01:39.720><c> need</c><00:01:40.119><c> our</c>

00:01:40.429 --> 00:01:40.439 align:start position:0%
API key to do parsing uh and we need our
 

00:01:40.439 --> 00:01:42.990 align:start position:0%
API key to do parsing uh and we need our
Lama<00:01:40.840><c> Cloud</c><00:01:41.119><c> API</c><00:01:41.520><c> key</c><00:01:42.000><c> which</c><00:01:42.119><c> you</c><00:01:42.240><c> can</c><00:01:42.399><c> get</c><00:01:42.560><c> at</c>

00:01:42.990 --> 00:01:43.000 align:start position:0%
Lama Cloud API key which you can get at
 

00:01:43.000 --> 00:01:45.190 align:start position:0%
Lama Cloud API key which you can get at
cloud.<00:01:44.000><c> L</c>

00:01:45.190 --> 00:01:45.200 align:start position:0%
cloud. L
 

00:01:45.200 --> 00:01:49.429 align:start position:0%
cloud. L
index.<00:01:46.200><c> uh</c><00:01:46.399><c> to</c><00:01:46.600><c> use</c><00:01:46.840><c> llam</c>

00:01:49.429 --> 00:01:49.439 align:start position:0%
 
 

00:01:49.439 --> 00:01:54.950 align:start position:0%
 
parse<00:01:50.560><c> so</c><00:01:51.560><c> uh</c><00:01:51.799><c> we're</c><00:01:51.960><c> going</c><00:01:52.119><c> to</c><00:01:52.719><c> use</c><00:01:53.719><c> uh</c><00:01:54.600><c> llama</c>

00:01:54.950 --> 00:01:54.960 align:start position:0%
parse so uh we're going to use uh llama
 

00:01:54.960 --> 00:01:58.389 align:start position:0%
parse so uh we're going to use uh llama
indexes<00:01:55.560><c> sorry</c><00:01:55.960><c> open</c><00:01:56.439><c> AI</c><00:01:57.439><c> uh</c><00:01:57.799><c> small</c><00:01:58.159><c> text</c>

00:01:58.389 --> 00:01:58.399 align:start position:0%
indexes sorry open AI uh small text
 

00:01:58.399 --> 00:01:59.950 align:start position:0%
indexes sorry open AI uh small text
embedding<00:01:58.759><c> three</c><00:01:59.000><c> small</c><00:01:59.280><c> model</c><00:01:59.560><c> and</c><00:01:59.640><c> we're</c>

00:01:59.950 --> 00:01:59.960 align:start position:0%
embedding three small model and we're
 

00:01:59.960 --> 00:02:02.910 align:start position:0%
embedding three small model and we're
going<00:02:00.079><c> to</c><00:02:00.159><c> be</c><00:02:00.280><c> using</c><00:02:00.560><c> GPT</c><00:02:01.000><c> 3.5</c><00:02:01.680><c> turbo</c><00:02:02.640><c> as</c><00:02:02.759><c> our</c>

00:02:02.910 --> 00:02:02.920 align:start position:0%
going to be using GPT 3.5 turbo as our
 

00:02:02.920 --> 00:02:09.229 align:start position:0%
going to be using GPT 3.5 turbo as our
embedding<00:02:03.360><c> model</c><00:02:03.600><c> and</c><00:02:03.759><c> our</c><00:02:03.880><c> LL</c><00:02:04.360><c> models</c><00:02:05.320><c> uh</c>

00:02:09.229 --> 00:02:09.239 align:start position:0%
 
 

00:02:09.239 --> 00:02:11.550 align:start position:0%
 
respectively<00:02:10.239><c> so</c><00:02:10.599><c> let's</c><00:02:10.879><c> just</c><00:02:11.039><c> look</c><00:02:11.200><c> at</c><00:02:11.400><c> how</c>

00:02:11.550 --> 00:02:11.560 align:start position:0%
respectively so let's just look at how
 

00:02:11.560 --> 00:02:15.190 align:start position:0%
respectively so let's just look at how
you<00:02:11.760><c> parse</c><00:02:12.360><c> things</c><00:02:12.560><c> with</c><00:02:12.720><c> L</c><00:02:13.040><c> parse</c><00:02:13.280><c> at</c><00:02:13.440><c> all</c><00:02:14.200><c> you</c>

00:02:15.190 --> 00:02:15.200 align:start position:0%
you parse things with L parse at all you
 

00:02:15.200 --> 00:02:18.270 align:start position:0%
you parse things with L parse at all you
uh<00:02:15.360><c> import</c><00:02:15.720><c> llap</c><00:02:16.120><c> parse</c><00:02:17.120><c> and</c><00:02:17.640><c> you</c><00:02:17.800><c> tell</c><00:02:18.000><c> it</c>

00:02:18.270 --> 00:02:18.280 align:start position:0%
uh import llap parse and you tell it
 

00:02:18.280 --> 00:02:20.270 align:start position:0%
uh import llap parse and you tell it
what<00:02:18.560><c> result</c><00:02:18.959><c> type</c><00:02:19.200><c> you</c><00:02:19.319><c> want</c><00:02:19.760><c> in</c><00:02:19.920><c> this</c><00:02:20.080><c> case</c>

00:02:20.270 --> 00:02:20.280 align:start position:0%
what result type you want in this case
 

00:02:20.280 --> 00:02:22.150 align:start position:0%
what result type you want in this case
we<00:02:20.400><c> want</c><00:02:20.599><c> markdown</c><00:02:21.120><c> back</c><00:02:21.519><c> and</c><00:02:21.640><c> you</c><00:02:21.800><c> give</c><00:02:21.920><c> it</c>

00:02:22.150 --> 00:02:22.160 align:start position:0%
we want markdown back and you give it
 

00:02:22.160 --> 00:02:24.630 align:start position:0%
we want markdown back and you give it
that<00:02:22.840><c> policy</c><00:02:23.280><c> PDF</c><00:02:23.680><c> that</c><00:02:23.800><c> we</c><00:02:24.000><c> just</c><00:02:24.120><c> downloaded</c>

00:02:24.630 --> 00:02:24.640 align:start position:0%
that policy PDF that we just downloaded
 

00:02:24.640 --> 00:02:25.750 align:start position:0%
that policy PDF that we just downloaded
in<00:02:24.760><c> the</c><00:02:24.840><c> earlier</c>

00:02:25.750 --> 00:02:25.760 align:start position:0%
in the earlier
 

00:02:25.760 --> 00:02:29.229 align:start position:0%
in the earlier
cell<00:02:26.879><c> um</c><00:02:27.879><c> it</c><00:02:28.040><c> parses</c><00:02:28.440><c> that</c><00:02:28.640><c> pretty</c><00:02:28.879><c> quickly</c>

00:02:29.229 --> 00:02:29.239 align:start position:0%
cell um it parses that pretty quickly
 

00:02:29.239 --> 00:02:33.350 align:start position:0%
cell um it parses that pretty quickly
and<00:02:29.360><c> if</c><00:02:29.480><c> you</c><00:02:29.599><c> print</c><00:02:30.080><c> out</c><00:02:30.440><c> what</c><00:02:30.560><c> you</c><00:02:30.720><c> see</c><00:02:31.280><c> is</c><00:02:32.360><c> uh</c>

00:02:33.350 --> 00:02:33.360 align:start position:0%
and if you print out what you see is uh
 

00:02:33.360 --> 00:02:36.550 align:start position:0%
and if you print out what you see is uh
markdown<00:02:34.280><c> that</c><00:02:34.440><c> represents</c><00:02:35.400><c> the</c><00:02:35.640><c> exact</c><00:02:36.080><c> text</c>

00:02:36.550 --> 00:02:36.560 align:start position:0%
markdown that represents the exact text
 

00:02:36.560 --> 00:02:38.270 align:start position:0%
markdown that represents the exact text
that<00:02:36.680><c> was</c><00:02:36.800><c> in</c><00:02:37.000><c> that</c><00:02:37.160><c> PDF</c><00:02:37.599><c> document</c><00:02:38.040><c> so</c><00:02:38.160><c> we</c>

00:02:38.270 --> 00:02:38.280 align:start position:0%
that was in that PDF document so we
 

00:02:38.280 --> 00:02:39.949 align:start position:0%
that was in that PDF document so we
haven't<00:02:38.560><c> changed</c><00:02:39.000><c> anything</c><00:02:39.640><c> we've</c><00:02:39.840><c> just</c>

00:02:39.949 --> 00:02:39.959 align:start position:0%
haven't changed anything we've just
 

00:02:39.959 --> 00:02:42.910 align:start position:0%
haven't changed anything we've just
turned<00:02:40.239><c> it</c><00:02:40.400><c> into</c><00:02:41.120><c> markdown</c><00:02:41.800><c> which</c><00:02:42.080><c> llms</c><00:02:42.640><c> find</c>

00:02:42.910 --> 00:02:42.920 align:start position:0%
turned it into markdown which llms find
 

00:02:42.920 --> 00:02:45.110 align:start position:0%
turned it into markdown which llms find
easier<00:02:43.239><c> to</c>

00:02:45.110 --> 00:02:45.120 align:start position:0%
easier to
 

00:02:45.120 --> 00:02:46.830 align:start position:0%
easier to
understand

00:02:46.830 --> 00:02:46.840 align:start position:0%
understand
 

00:02:46.840 --> 00:02:52.110 align:start position:0%
understand
um<00:02:47.840><c> one</c><00:02:48.000><c> of</c><00:02:48.120><c> the</c><00:02:48.280><c> things</c><00:02:48.519><c> we</c><00:02:48.680><c> can</c><00:02:49.000><c> do</c><00:02:50.000><c> is</c><00:02:51.120><c> pass</c>

00:02:52.110 --> 00:02:52.120 align:start position:0%
um one of the things we can do is pass
 

00:02:52.120 --> 00:02:55.910 align:start position:0%
um one of the things we can do is pass
uh<00:02:53.040><c> this</c><00:02:53.720><c> markdown</c><00:02:54.720><c> uh</c><00:02:54.879><c> into</c><00:02:55.200><c> our</c><00:02:55.440><c> markdown</c>

00:02:55.910 --> 00:02:55.920 align:start position:0%
uh this markdown uh into our markdown
 

00:02:55.920 --> 00:02:57.190 align:start position:0%
uh this markdown uh into our markdown
element<00:02:56.280><c> node</c>

00:02:57.190 --> 00:02:57.200 align:start position:0%
element node
 

00:02:57.200 --> 00:03:01.990 align:start position:0%
element node
parser<00:02:58.360><c> um</c><00:02:59.360><c> which</c><00:02:59.519><c> will</c><00:02:59.879><c> break</c><00:03:00.120><c> it</c><00:03:00.280><c> up</c><00:03:00.840><c> into</c><00:03:01.840><c> uh</c>

00:03:01.990 --> 00:03:02.000 align:start position:0%
parser um which will break it up into uh
 

00:03:02.000 --> 00:03:06.630 align:start position:0%
parser um which will break it up into uh
a<00:03:02.200><c> set</c><00:03:02.400><c> of</c><00:03:02.680><c> text</c><00:03:03.519><c> and</c><00:03:04.280><c> tables</c><00:03:05.280><c> uh</c><00:03:05.440><c> tables</c><00:03:05.799><c> are</c><00:03:06.400><c> a</c>

00:03:06.630 --> 00:03:06.640 align:start position:0%
a set of text and tables uh tables are a
 

00:03:06.640 --> 00:03:09.509 align:start position:0%
a set of text and tables uh tables are a
big<00:03:07.000><c> part</c><00:03:07.720><c> of</c><00:03:08.440><c> what</c><00:03:08.640><c> makes</c><00:03:09.000><c> complicated</c>

00:03:09.509 --> 00:03:09.519 align:start position:0%
big part of what makes complicated
 

00:03:09.519 --> 00:03:13.309 align:start position:0%
big part of what makes complicated
documents<00:03:10.480><c> complicated</c><00:03:11.480><c> um</c><00:03:12.080><c> especially</c><00:03:12.440><c> for</c>

00:03:13.309 --> 00:03:13.319 align:start position:0%
documents complicated um especially for
 

00:03:13.319 --> 00:03:17.789 align:start position:0%
documents complicated um especially for
llms<00:03:14.319><c> uh</c><00:03:15.159><c> and</c><00:03:16.159><c> by</c><00:03:16.599><c> parsing</c><00:03:16.959><c> it</c><00:03:17.120><c> into</c><00:03:17.400><c> tables</c>

00:03:17.789 --> 00:03:17.799 align:start position:0%
llms uh and by parsing it into tables
 

00:03:17.799 --> 00:03:19.750 align:start position:0%
llms uh and by parsing it into tables
specifically<00:03:18.680><c> we</c><00:03:18.799><c> can</c><00:03:18.959><c> get</c><00:03:19.159><c> better</c><00:03:19.400><c> results</c>

00:03:19.750 --> 00:03:19.760 align:start position:0%
specifically we can get better results
 

00:03:19.760 --> 00:03:22.350 align:start position:0%
specifically we can get better results
with<00:03:19.879><c> our</c><00:03:20.080><c> parsing</c><00:03:20.799><c> so</c><00:03:21.440><c> I'm</c><00:03:21.560><c> going</c><00:03:21.720><c> to</c><00:03:21.879><c> scroll</c>

00:03:22.350 --> 00:03:22.360 align:start position:0%
with our parsing so I'm going to scroll
 

00:03:22.360 --> 00:03:24.910 align:start position:0%
with our parsing so I'm going to scroll
past<00:03:22.599><c> all</c><00:03:22.760><c> the</c><00:03:22.959><c> part</c><00:03:23.560><c> where</c><00:03:24.040><c> the</c><00:03:24.159><c> parser</c><00:03:24.560><c> was</c>

00:03:24.910 --> 00:03:24.920 align:start position:0%
past all the part where the parser was
 

00:03:24.920 --> 00:03:28.149 align:start position:0%
past all the part where the parser was
working<00:03:25.920><c> and</c><00:03:26.080><c> the</c><00:03:26.239><c> result</c><00:03:26.640><c> is</c><00:03:27.560><c> uh</c><00:03:27.720><c> you</c><00:03:27.879><c> get</c><00:03:28.040><c> a</c>

00:03:28.149 --> 00:03:28.159 align:start position:0%
working and the result is uh you get a
 

00:03:28.159 --> 00:03:30.509 align:start position:0%
working and the result is uh you get a
bunch<00:03:28.360><c> of</c><00:03:28.519><c> nodes</c><00:03:28.920><c> and</c><00:03:29.200><c> table</c><00:03:29.480><c> objects</c>

00:03:30.509 --> 00:03:30.519 align:start position:0%
bunch of nodes and table objects
 

00:03:30.519 --> 00:03:32.190 align:start position:0%
bunch of nodes and table objects
which<00:03:30.680><c> you</c><00:03:30.760><c> can</c><00:03:30.959><c> then</c><00:03:31.200><c> pass</c><00:03:31.480><c> into</c><00:03:31.680><c> your</c><00:03:31.920><c> vector</c>

00:03:32.190 --> 00:03:32.200 align:start position:0%
which you can then pass into your vector
 

00:03:32.200 --> 00:03:36.030 align:start position:0%
which you can then pass into your vector
store<00:03:32.840><c> index</c><00:03:33.840><c> uh</c><00:03:34.000><c> as</c><00:03:34.080><c> a</c><00:03:34.200><c> simple</c><00:03:34.560><c> list</c><00:03:34.760><c> of</c>

00:03:36.030 --> 00:03:36.040 align:start position:0%
store index uh as a simple list of
 

00:03:36.040 --> 00:03:38.830 align:start position:0%
store index uh as a simple list of
objects<00:03:37.040><c> then</c><00:03:37.360><c> you</c><00:03:37.480><c> can</c><00:03:37.760><c> create</c><00:03:38.000><c> a</c><00:03:38.120><c> query</c>

00:03:38.830 --> 00:03:38.840 align:start position:0%
objects then you can create a query
 

00:03:38.840 --> 00:03:42.309 align:start position:0%
objects then you can create a query
engine<00:03:39.840><c> uh</c><00:03:40.680><c> from</c><00:03:41.080><c> that</c><00:03:41.280><c> Vector</c><00:03:41.560><c> store</c><00:03:41.879><c> Index</c>

00:03:42.309 --> 00:03:42.319 align:start position:0%
engine uh from that Vector store Index
 

00:03:42.319 --> 00:03:44.990 align:start position:0%
engine uh from that Vector store Index
this<00:03:42.439><c> is</c><00:03:42.640><c> how</c><00:03:42.760><c> you</c><00:03:42.959><c> always</c><00:03:43.159><c> use</c><00:03:43.360><c> llama</c>

00:03:44.990 --> 00:03:45.000 align:start position:0%
this is how you always use llama
 

00:03:45.000 --> 00:03:47.910 align:start position:0%
this is how you always use llama
index<00:03:46.000><c> let's</c><00:03:46.239><c> see</c><00:03:47.040><c> what</c><00:03:47.239><c> happens</c><00:03:47.560><c> when</c><00:03:47.720><c> we</c>

00:03:47.910 --> 00:03:47.920 align:start position:0%
index let's see what happens when we
 

00:03:47.920 --> 00:03:51.550 align:start position:0%
index let's see what happens when we
query<00:03:48.360><c> that</c><00:03:48.640><c> basic</c><00:03:49.640><c> uh</c><00:03:49.879><c> set</c><00:03:50.159><c> of</c><00:03:50.400><c> tech</c><00:03:50.920><c> of</c><00:03:51.159><c> text</c>

00:03:51.550 --> 00:03:51.560 align:start position:0%
query that basic uh set of tech of text
 

00:03:51.560 --> 00:03:52.309 align:start position:0%
query that basic uh set of tech of text
and

00:03:52.309 --> 00:03:52.319 align:start position:0%
and
 

00:03:52.319 --> 00:03:56.869 align:start position:0%
and
tables<00:03:53.319><c> uh</c><00:03:54.200><c> you</c><00:03:54.439><c> get</c><00:03:55.280><c> you</c><00:03:55.519><c> asked</c><00:03:56.360><c> my</c><00:03:56.519><c> trip</c><00:03:56.720><c> was</c>

00:03:56.869 --> 00:03:56.879 align:start position:0%
tables uh you get you asked my trip was
 

00:03:56.879 --> 00:03:58.910 align:start position:0%
tables uh you get you asked my trip was
delayed<00:03:57.200><c> and</c><00:03:57.280><c> I</c><00:03:57.400><c> paid</c><00:03:57.560><c> $45</c><00:03:58.439><c> how</c><00:03:58.560><c> much</c><00:03:58.720><c> am</c><00:03:58.799><c> I</c>

00:03:58.910 --> 00:03:58.920 align:start position:0%
delayed and I paid $45 how much am I
 

00:03:58.920 --> 00:04:00.350 align:start position:0%
delayed and I paid $45 how much am I
covered<00:03:59.239><c> for</c>

00:04:00.350 --> 00:04:00.360 align:start position:0%
covered for
 

00:04:00.360 --> 00:04:03.069 align:start position:0%
covered for
uh<00:04:01.280><c> and</c><00:04:01.439><c> the</c><00:04:01.560><c> result</c><00:04:01.879><c> is</c><00:04:02.120><c> unhelpful</c><00:04:02.760><c> you</c><00:04:02.879><c> are</c>

00:04:03.069 --> 00:04:03.079 align:start position:0%
uh and the result is unhelpful you are
 

00:04:03.079 --> 00:04:04.429 align:start position:0%
uh and the result is unhelpful you are
covered<00:04:03.360><c> for</c><00:04:03.519><c> the</c><00:04:03.599><c> amount</c><00:04:03.879><c> mentioned</c><00:04:04.159><c> in</c><00:04:04.280><c> the</c>

00:04:04.429 --> 00:04:04.439 align:start position:0%
covered for the amount mentioned in the
 

00:04:04.439 --> 00:04:08.509 align:start position:0%
covered for the amount mentioned in the
Certificate<00:04:04.879><c> of</c><00:04:05.480><c> Insurance</c><00:04:06.480><c> uh</c><00:04:07.120><c> that's</c><00:04:07.519><c> not</c>

00:04:08.509 --> 00:04:08.519 align:start position:0%
Certificate of Insurance uh that's not
 

00:04:08.519 --> 00:04:10.309 align:start position:0%
Certificate of Insurance uh that's not
what<00:04:08.680><c> we</c><00:04:08.840><c> wanted</c><00:04:09.239><c> we</c><00:04:09.360><c> wanted</c><00:04:09.599><c> to</c><00:04:09.720><c> know</c><00:04:09.920><c> exactly</c>

00:04:10.309 --> 00:04:10.319 align:start position:0%
what we wanted we wanted to know exactly
 

00:04:10.319 --> 00:04:12.750 align:start position:0%
what we wanted we wanted to know exactly
how<00:04:10.439><c> much</c><00:04:10.560><c> we're</c><00:04:10.799><c> covered</c><00:04:11.239><c> for</c>

00:04:12.750 --> 00:04:12.760 align:start position:0%
how much we're covered for
 

00:04:12.760 --> 00:04:15.630 align:start position:0%
how much we're covered for
um<00:04:13.760><c> the</c><00:04:13.920><c> problem</c><00:04:14.280><c> is</c><00:04:14.720><c> that</c><00:04:14.920><c> that</c><00:04:15.120><c> information</c>

00:04:15.630 --> 00:04:15.640 align:start position:0%
um the problem is that that information
 

00:04:15.640 --> 00:04:17.069 align:start position:0%
um the problem is that that information
about<00:04:15.959><c> exactly</c><00:04:16.280><c> how</c><00:04:16.359><c> much</c><00:04:16.519><c> you'd</c><00:04:16.639><c> be</c><00:04:16.759><c> covered</c>

00:04:17.069 --> 00:04:17.079 align:start position:0%
about exactly how much you'd be covered
 

00:04:17.079 --> 00:04:19.069 align:start position:0%
about exactly how much you'd be covered
for<00:04:17.519><c> is</c><00:04:17.680><c> split</c><00:04:18.040><c> across</c><00:04:18.320><c> the</c><00:04:18.519><c> document</c><00:04:18.840><c> in</c><00:04:18.959><c> a</c>

00:04:19.069 --> 00:04:19.079 align:start position:0%
for is split across the document in a
 

00:04:19.079 --> 00:04:20.629 align:start position:0%
for is split across the document in a
bunch<00:04:19.239><c> of</c><00:04:19.400><c> different</c><00:04:19.680><c> places</c><00:04:19.959><c> and</c><00:04:20.079><c> the</c><00:04:20.160><c> lon</c>

00:04:20.629 --> 00:04:20.639 align:start position:0%
bunch of different places and the lon
 

00:04:20.639 --> 00:04:23.350 align:start position:0%
bunch of different places and the lon
can't<00:04:20.880><c> find</c><00:04:21.360><c> it</c><00:04:22.360><c> uh</c><00:04:22.560><c> so</c><00:04:22.759><c> one</c><00:04:22.880><c> of</c><00:04:23.000><c> the</c><00:04:23.120><c> really</c>

00:04:23.350 --> 00:04:23.360 align:start position:0%
can't find it uh so one of the really
 

00:04:23.360 --> 00:04:26.830 align:start position:0%
can't find it uh so one of the really
neat<00:04:23.680><c> things</c><00:04:24.240><c> about</c><00:04:24.560><c> llama</c><00:04:25.240><c> about</c><00:04:25.440><c> Lama</c><00:04:25.840><c> parse</c>

00:04:26.830 --> 00:04:26.840 align:start position:0%
neat things about llama about Lama parse
 

00:04:26.840 --> 00:04:29.310 align:start position:0%
neat things about llama about Lama parse
uh<00:04:27.040><c> is</c><00:04:27.240><c> that</c><00:04:27.479><c> you</c><00:04:27.720><c> can</c><00:04:28.560><c> give</c><00:04:28.759><c> the</c><00:04:28.919><c> parser</c>

00:04:29.310 --> 00:04:29.320 align:start position:0%
uh is that you can give the parser
 

00:04:29.320 --> 00:04:30.749 align:start position:0%
uh is that you can give the parser
instruction

00:04:30.749 --> 00:04:30.759 align:start position:0%
instruction
 

00:04:30.759 --> 00:04:33.110 align:start position:0%
instruction
you<00:04:30.880><c> can</c><00:04:31.080><c> give</c><00:04:31.240><c> the</c><00:04:31.440><c> parser</c><00:04:32.440><c> uh</c><00:04:32.600><c> instructions</c>

00:04:33.110 --> 00:04:33.120 align:start position:0%
you can give the parser uh instructions
 

00:04:33.120 --> 00:04:35.430 align:start position:0%
you can give the parser uh instructions
that<00:04:33.400><c> say</c><00:04:34.160><c> this</c><00:04:34.280><c> is</c><00:04:34.440><c> the</c><00:04:34.600><c> kind</c><00:04:34.800><c> of</c><00:04:35.039><c> document</c>

00:04:35.430 --> 00:04:35.440 align:start position:0%
that say this is the kind of document
 

00:04:35.440 --> 00:04:37.790 align:start position:0%
that say this is the kind of document
that<00:04:35.560><c> I'm</c><00:04:35.720><c> reading</c><00:04:36.400><c> convert</c><00:04:36.759><c> it</c><00:04:37.039><c> into</c>

00:04:37.790 --> 00:04:37.800 align:start position:0%
that I'm reading convert it into
 

00:04:37.800 --> 00:04:39.670 align:start position:0%
that I'm reading convert it into
something<00:04:38.120><c> that</c><00:04:38.280><c> is</c><00:04:38.440><c> easier</c><00:04:38.840><c> to</c><00:04:39.440><c> understand</c>

00:04:39.670 --> 00:04:39.680 align:start position:0%
something that is easier to understand
 

00:04:39.680 --> 00:04:43.430 align:start position:0%
something that is easier to understand
as<00:04:39.800><c> an</c><00:04:40.160><c> LM</c><00:04:41.160><c> so</c><00:04:41.440><c> in</c><00:04:41.639><c> this</c><00:04:41.840><c> case</c><00:04:42.759><c> uh</c><00:04:42.919><c> we</c><00:04:43.080><c> give</c><00:04:43.199><c> it</c><00:04:43.320><c> a</c>

00:04:43.430 --> 00:04:43.440 align:start position:0%
as an LM so in this case uh we give it a
 

00:04:43.440 --> 00:04:45.909 align:start position:0%
as an LM so in this case uh we give it a
bunch<00:04:43.600><c> of</c><00:04:43.759><c> instructions</c><00:04:44.320><c> that</c><00:04:44.560><c> say</c><00:04:45.240><c> read</c><00:04:45.600><c> this</c>

00:04:45.909 --> 00:04:45.919 align:start position:0%
bunch of instructions that say read this
 

00:04:45.919 --> 00:04:48.350 align:start position:0%
bunch of instructions that say read this
document<00:04:46.320><c> full</c><00:04:46.560><c> of</c><00:04:46.800><c> policies</c><00:04:47.720><c> and</c><00:04:47.919><c> coverage</c>

00:04:48.350 --> 00:04:48.360 align:start position:0%
document full of policies and coverage
 

00:04:48.360 --> 00:04:50.390 align:start position:0%
document full of policies and coverage
and<00:04:48.520><c> benefits</c><00:04:48.919><c> and</c><00:04:49.080><c> exclusions</c><00:04:49.919><c> and</c><00:04:50.039><c> turn</c><00:04:50.240><c> it</c>

00:04:50.390 --> 00:04:50.400 align:start position:0%
and benefits and exclusions and turn it
 

00:04:50.400 --> 00:04:52.430 align:start position:0%
and benefits and exclusions and turn it
into<00:04:50.600><c> a</c><00:04:50.800><c> list</c><00:04:51.240><c> just</c><00:04:51.360><c> a</c><00:04:51.560><c> list</c><00:04:51.759><c> of</c><00:04:52.039><c> things</c><00:04:52.280><c> that</c>

00:04:52.430 --> 00:04:52.440 align:start position:0%
into a list just a list of things that
 

00:04:52.440 --> 00:04:54.430 align:start position:0%
into a list just a list of things that
I'm<00:04:52.600><c> covered</c><00:04:52.919><c> for</c><00:04:53.440><c> and</c><00:04:53.600><c> things</c><00:04:53.840><c> that</c><00:04:54.000><c> I'm</c><00:04:54.160><c> not</c>

00:04:54.430 --> 00:04:54.440 align:start position:0%
I'm covered for and things that I'm not
 

00:04:54.440 --> 00:04:56.870 align:start position:0%
I'm covered for and things that I'm not
cover<00:04:55.440><c> that</c><00:04:55.600><c> I'm</c><00:04:55.759><c> not</c><00:04:55.960><c> covered</c>

00:04:56.870 --> 00:04:56.880 align:start position:0%
cover that I'm not covered
 

00:04:56.880 --> 00:05:00.230 align:start position:0%
cover that I'm not covered
for<00:04:57.880><c> um</c><00:04:58.440><c> and</c><00:04:58.639><c> parse</c><00:04:58.919><c> it</c><00:04:59.120><c> that</c><00:04:59.240><c> way</c><00:04:59.400><c> in</c>

00:05:00.230 --> 00:05:00.240 align:start position:0%
for um and parse it that way in
 

00:05:00.240 --> 00:05:02.070 align:start position:0%
for um and parse it that way in
instead

00:05:02.070 --> 00:05:02.080 align:start position:0%
instead
 

00:05:02.080 --> 00:05:05.710 align:start position:0%
instead
uh<00:05:03.080><c> so</c><00:05:03.440><c> now</c><00:05:04.320><c> having</c><00:05:04.600><c> parsed</c><00:05:04.960><c> it</c><00:05:05.120><c> into</c><00:05:05.400><c> two</c>

00:05:05.710 --> 00:05:05.720 align:start position:0%
uh so now having parsed it into two
 

00:05:05.720 --> 00:05:08.189 align:start position:0%
uh so now having parsed it into two
different<00:05:06.000><c> forms</c><00:05:06.560><c> one</c><00:05:06.759><c> is</c><00:05:07.000><c> the</c><00:05:07.280><c> just</c><00:05:07.840><c> a</c><00:05:08.039><c> pile</c>

00:05:08.189 --> 00:05:08.199 align:start position:0%
different forms one is the just a pile
 

00:05:08.199 --> 00:05:10.189 align:start position:0%
different forms one is the just a pile
of<00:05:08.320><c> markdown</c><00:05:08.840><c> and</c><00:05:09.000><c> one</c><00:05:09.199><c> is</c><00:05:09.520><c> this</c><00:05:09.759><c> list</c><00:05:10.000><c> of</c>

00:05:10.189 --> 00:05:10.199 align:start position:0%
of markdown and one is this list of
 

00:05:10.199 --> 00:05:13.150 align:start position:0%
of markdown and one is this list of
coverages<00:05:11.199><c> let's</c><00:05:11.440><c> see</c><00:05:12.360><c> uh</c><00:05:12.600><c> what</c><00:05:12.759><c> sort</c><00:05:12.960><c> of</c>

00:05:13.150 --> 00:05:13.160 align:start position:0%
coverages let's see uh what sort of
 

00:05:13.160 --> 00:05:14.430 align:start position:0%
coverages let's see uh what sort of
responses<00:05:13.639><c> we</c>

00:05:14.430 --> 00:05:14.440 align:start position:0%
responses we
 

00:05:14.440 --> 00:05:16.270 align:start position:0%
responses we
get

00:05:16.270 --> 00:05:16.280 align:start position:0%
get
 

00:05:16.280 --> 00:05:19.749 align:start position:0%
get
um<00:05:17.280><c> you</c><00:05:17.400><c> can</c><00:05:17.560><c> see</c><00:05:17.840><c> that</c><00:05:18.080><c> this</c><00:05:18.280><c> is</c><00:05:19.199><c> uh</c><00:05:19.560><c> just</c>

00:05:19.749 --> 00:05:19.759 align:start position:0%
um you can see that this is uh just
 

00:05:19.759 --> 00:05:24.029 align:start position:0%
um you can see that this is uh just
printing<00:05:20.120><c> out</c><00:05:20.360><c> what</c><00:05:20.680><c> the</c><00:05:22.160><c> uh</c><00:05:23.160><c> uh</c><00:05:23.360><c> par</c><00:05:23.759><c> form</c>

00:05:24.029 --> 00:05:24.039 align:start position:0%
printing out what the uh uh par form
 

00:05:24.039 --> 00:05:27.189 align:start position:0%
printing out what the uh uh par form
looks<00:05:24.319><c> like</c><00:05:25.080><c> the</c><00:05:25.800><c> original</c><00:05:26.240><c> ones</c><00:05:26.639><c> is</c><00:05:26.840><c> still</c>

00:05:27.189 --> 00:05:27.199 align:start position:0%
looks like the original ones is still
 

00:05:27.199 --> 00:05:31.670 align:start position:0%
looks like the original ones is still
just<00:05:27.360><c> a</c><00:05:28.000><c> markdown</c><00:05:29.000><c> uh</c><00:05:29.160><c> conversion</c><00:05:30.080><c> of</c><00:05:31.080><c> uh</c><00:05:31.520><c> the</c>

00:05:31.670 --> 00:05:31.680 align:start position:0%
just a markdown uh conversion of uh the
 

00:05:31.680 --> 00:05:34.110 align:start position:0%
just a markdown uh conversion of uh the
exact<00:05:32.000><c> text</c><00:05:32.240><c> of</c><00:05:32.400><c> the</c><00:05:32.639><c> document</c><00:05:33.639><c> uh</c><00:05:33.759><c> but</c><00:05:33.919><c> after</c>

00:05:34.110 --> 00:05:34.120 align:start position:0%
exact text of the document uh but after
 

00:05:34.120 --> 00:05:37.350 align:start position:0%
exact text of the document uh but after
the<00:05:34.240><c> break</c><00:05:34.520><c> line</c><00:05:35.080><c> you</c><00:05:35.280><c> see</c><00:05:36.240><c> what</c><00:05:36.720><c> lamap</c><00:05:37.120><c> par</c>

00:05:37.350 --> 00:05:37.360 align:start position:0%
the break line you see what lamap par
 

00:05:37.360 --> 00:05:39.430 align:start position:0%
the break line you see what lamap par
has<00:05:37.479><c> turned</c><00:05:37.880><c> the</c><00:05:38.000><c> same</c><00:05:38.280><c> document</c><00:05:38.680><c> into</c><00:05:39.280><c> it's</c>

00:05:39.430 --> 00:05:39.440 align:start position:0%
has turned the same document into it's
 

00:05:39.440 --> 00:05:41.110 align:start position:0%
has turned the same document into it's
turned<00:05:39.680><c> it</c><00:05:39.840><c> into</c><00:05:40.080><c> this</c><00:05:40.280><c> list</c><00:05:40.520><c> of</c><00:05:40.759><c> things</c><00:05:41.000><c> that</c>

00:05:41.110 --> 00:05:41.120 align:start position:0%
turned it into this list of things that
 

00:05:41.120 --> 00:05:42.909 align:start position:0%
turned it into this list of things that
you<00:05:41.240><c> are</c><00:05:41.440><c> covered</c><00:05:41.759><c> for</c><00:05:42.360><c> things</c><00:05:42.560><c> that</c><00:05:42.680><c> you</c><00:05:42.759><c> are</c>

00:05:42.909 --> 00:05:42.919 align:start position:0%
you are covered for things that you are
 

00:05:42.919 --> 00:05:45.909 align:start position:0%
you are covered for things that you are
not<00:05:43.160><c> covered</c><00:05:43.479><c> for</c><00:05:44.199><c> very</c><00:05:44.440><c> simple</c><00:05:45.440><c> set</c><00:05:45.759><c> of</c>

00:05:45.909 --> 00:05:45.919 align:start position:0%
not covered for very simple set of
 

00:05:45.919 --> 00:05:49.590 align:start position:0%
not covered for very simple set of
statements<00:05:46.360><c> that's</c><00:05:46.600><c> easy</c><00:05:46.919><c> for</c><00:05:47.199><c> an</c><00:05:47.360><c> llm</c><00:05:47.840><c> to</c>

00:05:49.590 --> 00:05:49.600 align:start position:0%
statements that's easy for an llm to
 

00:05:49.600 --> 00:05:53.469 align:start position:0%
statements that's easy for an llm to
understand<00:05:50.600><c> so</c><00:05:51.080><c> now</c><00:05:52.080><c> uh</c><00:05:52.199><c> we</c><00:05:52.400><c> put</c><00:05:52.639><c> those</c><00:05:52.880><c> things</c>

00:05:53.469 --> 00:05:53.479 align:start position:0%
understand so now uh we put those things
 

00:05:53.479 --> 00:05:56.390 align:start position:0%
understand so now uh we put those things
into<00:05:54.479><c> a</c><00:05:54.680><c> quiry</c>

00:05:56.390 --> 00:05:56.400 align:start position:0%
into a quiry
 

00:05:56.400 --> 00:06:00.309 align:start position:0%
into a quiry
engine<00:05:57.400><c> and</c><00:05:58.080><c> let's</c><00:05:58.919><c> run</c>

00:06:00.309 --> 00:06:00.319 align:start position:0%
engine and let's run
 

00:06:00.319 --> 00:06:02.909 align:start position:0%
engine and let's run
uh<00:06:00.639><c> the</c><00:06:00.800><c> same</c><00:06:01.039><c> query</c><00:06:01.479><c> that</c><00:06:01.880><c> against</c><00:06:02.680><c> our</c>

00:06:02.909 --> 00:06:02.919 align:start position:0%
uh the same query that against our
 

00:06:02.919 --> 00:06:05.309 align:start position:0%
uh the same query that against our
original<00:06:03.560><c> vanilla</c><00:06:03.960><c> query</c><00:06:04.280><c> engine</c><00:06:04.960><c> and</c><00:06:05.120><c> our</c>

00:06:05.309 --> 00:06:05.319 align:start position:0%
original vanilla query engine and our
 

00:06:05.319 --> 00:06:08.350 align:start position:0%
original vanilla query engine and our
new<00:06:05.560><c> query</c><00:06:05.919><c> engine</c><00:06:06.400><c> using</c><00:06:06.880><c> the</c><00:06:07.360><c> par</c>

00:06:08.350 --> 00:06:08.360 align:start position:0%
new query engine using the par
 

00:06:08.360 --> 00:06:11.589 align:start position:0%
new query engine using the par
form<00:06:09.360><c> uh</c><00:06:10.280><c> again</c><00:06:10.599><c> my</c><00:06:10.720><c> trip</c><00:06:10.960><c> was</c><00:06:11.080><c> delayed</c><00:06:11.360><c> and</c><00:06:11.479><c> I</c>

00:06:11.589 --> 00:06:11.599 align:start position:0%
form uh again my trip was delayed and I
 

00:06:11.599 --> 00:06:14.029 align:start position:0%
form uh again my trip was delayed and I
paid<00:06:11.800><c> 45</c><00:06:12.440><c> how</c><00:06:12.520><c> much</c><00:06:12.680><c> am</c><00:06:12.800><c> I</c><00:06:12.960><c> covered</c><00:06:13.240><c> for</c><00:06:13.919><c> the</c>

00:06:14.029 --> 00:06:14.039 align:start position:0%
paid 45 how much am I covered for the
 

00:06:14.039 --> 00:06:15.589 align:start position:0%
paid 45 how much am I covered for the
vanilla<00:06:14.400><c> version</c><00:06:14.880><c> gives</c><00:06:15.080><c> us</c><00:06:15.199><c> the</c><00:06:15.319><c> same</c>

00:06:15.589 --> 00:06:15.599 align:start position:0%
vanilla version gives us the same
 

00:06:15.599 --> 00:06:16.990 align:start position:0%
vanilla version gives us the same
answers<00:06:16.000><c> before</c><00:06:16.240><c> you</c><00:06:16.360><c> were</c><00:06:16.520><c> covered</c><00:06:16.800><c> in</c><00:06:16.919><c> the</c>

00:06:16.990 --> 00:06:17.000 align:start position:0%
answers before you were covered in the
 

00:06:17.000 --> 00:06:20.909 align:start position:0%
answers before you were covered in the
amount<00:06:17.560><c> mentioned</c><00:06:18.560><c> uh</c><00:06:18.960><c> but</c><00:06:19.400><c> the</c><00:06:19.919><c> instructions</c>

00:06:20.909 --> 00:06:20.919 align:start position:0%
amount mentioned uh but the instructions
 

00:06:20.919 --> 00:06:24.189 align:start position:0%
amount mentioned uh but the instructions
the<00:06:21.560><c> the</c><00:06:21.720><c> one</c><00:06:21.960><c> that</c><00:06:22.240><c> comes</c><00:06:22.479><c> from</c><00:06:22.639><c> the</c><00:06:22.880><c> list</c><00:06:23.880><c> uh</c>

00:06:24.189 --> 00:06:24.199 align:start position:0%
the the one that comes from the list uh
 

00:06:24.199 --> 00:06:26.150 align:start position:0%
the the one that comes from the list uh
gives<00:06:24.440><c> us</c><00:06:24.680><c> the</c><00:06:24.880><c> exact</c><00:06:25.240><c> answer</c><00:06:25.560><c> you</c><00:06:25.880><c> covered</c>

00:06:26.150 --> 00:06:26.160 align:start position:0%
gives us the exact answer you covered
 

00:06:26.160 --> 00:06:28.550 align:start position:0%
gives us the exact answer you covered
for<00:06:26.280><c> the</c><00:06:26.400><c> delay</c><00:06:26.919><c> amount</c><00:06:27.199><c> you</c><00:06:27.400><c> paid</c><00:06:27.680><c> which</c><00:06:27.800><c> is</c>

00:06:28.550 --> 00:06:28.560 align:start position:0%
for the delay amount you paid which is
 

00:06:28.560 --> 00:06:30.430 align:start position:0%
for the delay amount you paid which is
45

00:06:30.430 --> 00:06:30.440 align:start position:0%
45
 

00:06:30.440 --> 00:06:33.510 align:start position:0%
45
um<00:06:31.440><c> looking</c><00:06:31.720><c> in</c><00:06:31.880><c> the</c><00:06:32.039><c> document</c><00:06:32.639><c> we</c><00:06:32.800><c> noticed</c>

00:06:33.510 --> 00:06:33.520 align:start position:0%
um looking in the document we noticed
 

00:06:33.520 --> 00:06:35.629 align:start position:0%
um looking in the document we noticed
that<00:06:34.120><c> uh</c><00:06:34.280><c> one</c><00:06:34.520><c> expense</c><00:06:34.880><c> that</c><00:06:35.039><c> was</c><00:06:35.199><c> not</c><00:06:35.360><c> covered</c>

00:06:35.629 --> 00:06:35.639 align:start position:0%
that uh one expense that was not covered
 

00:06:35.639 --> 00:06:38.550 align:start position:0%
that uh one expense that was not covered
is<00:06:35.800><c> baby</c><00:06:36.080><c> food</c><00:06:37.080><c> uh</c><00:06:37.199><c> so</c><00:06:37.440><c> let's</c><00:06:37.759><c> ask</c><00:06:38.039><c> the</c><00:06:38.199><c> two</c>

00:06:38.550 --> 00:06:38.560 align:start position:0%
is baby food uh so let's ask the two
 

00:06:38.560 --> 00:06:42.550 align:start position:0%
is baby food uh so let's ask the two
query<00:06:38.880><c> engines</c><00:06:39.400><c> about</c><00:06:39.680><c> baby</c><00:06:40.240><c> food</c><00:06:41.280><c> um</c><00:06:42.280><c> I</c><00:06:42.400><c> just</c>

00:06:42.550 --> 00:06:42.560 align:start position:0%
query engines about baby food um I just
 

00:06:42.560 --> 00:06:44.950 align:start position:0%
query engines about baby food um I just
had<00:06:42.680><c> a</c><00:06:42.840><c> baby</c><00:06:43.080><c> is</c><00:06:43.240><c> baby</c><00:06:43.479><c> food</c><00:06:43.720><c> covered</c><00:06:44.720><c> uh</c><00:06:44.840><c> the</c>

00:06:44.950 --> 00:06:44.960 align:start position:0%
had a baby is baby food covered uh the
 

00:06:44.960 --> 00:06:46.749 align:start position:0%
had a baby is baby food covered uh the
vanilla<00:06:45.280><c> version</c><00:06:45.680><c> doesn't</c><00:06:46.400><c> understand</c><00:06:46.599><c> it</c>

00:06:46.749 --> 00:06:46.759 align:start position:0%
vanilla version doesn't understand it
 

00:06:46.759 --> 00:06:50.990 align:start position:0%
vanilla version doesn't understand it
doesn't<00:06:47.160><c> know</c><00:06:48.160><c> uh</c><00:06:48.560><c> but</c><00:06:49.000><c> the</c><00:06:49.319><c> par</c><00:06:49.720><c> version</c><00:06:50.120><c> does</c>

00:06:50.990 --> 00:06:51.000 align:start position:0%
doesn't know uh but the par version does
 

00:06:51.000 --> 00:06:52.830 align:start position:0%
doesn't know uh but the par version does
it<00:06:51.120><c> says</c><00:06:51.479><c> it</c><00:06:51.639><c> has</c><00:06:51.800><c> noticed</c><00:06:52.199><c> the</c><00:06:52.319><c> exclusion</c><00:06:52.720><c> and</c>

00:06:52.830 --> 00:06:52.840 align:start position:0%
it says it has noticed the exclusion and
 

00:06:52.840 --> 00:06:54.309 align:start position:0%
it says it has noticed the exclusion and
said<00:06:52.960><c> that</c><00:06:53.080><c> baby</c><00:06:53.319><c> food</c><00:06:53.479><c> is</c><00:06:53.639><c> not</c><00:06:53.800><c> covered</c><00:06:54.080><c> under</c>

00:06:54.309 --> 00:06:54.319 align:start position:0%
said that baby food is not covered under
 

00:06:54.319 --> 00:06:57.270 align:start position:0%
said that baby food is not covered under
the<00:06:54.639><c> the</c><00:06:54.759><c> insurance</c><00:06:55.560><c> policy</c><00:06:56.560><c> uh</c><00:06:56.720><c> we</c><00:06:56.840><c> can</c><00:06:57.000><c> run</c><00:06:57.160><c> a</c>

00:06:57.270 --> 00:06:57.280 align:start position:0%
the the insurance policy uh we can run a
 

00:06:57.280 --> 00:06:59.909 align:start position:0%
the the insurance policy uh we can run a
similar<00:06:57.680><c> question</c><00:06:58.120><c> about</c><00:06:58.560><c> gauze</c><00:06:59.599><c> which</c><00:06:59.720><c> is</c><00:06:59.800><c> a</c>

00:06:59.909 --> 00:06:59.919 align:start position:0%
similar question about gauze which is a
 

00:06:59.919 --> 00:07:01.790 align:start position:0%
similar question about gauze which is a
very<00:07:00.160><c> specific</c><00:07:00.599><c> thing</c><00:07:01.080><c> do</c><00:07:01.240><c> you</c><00:07:01.360><c> mention</c><00:07:01.680><c> in</c>

00:07:01.790 --> 00:07:01.800 align:start position:0%
very specific thing do you mention in
 

00:07:01.800 --> 00:07:04.029 align:start position:0%
very specific thing do you mention in
your<00:07:01.960><c> insurance</c><00:07:02.479><c> policy</c><00:07:03.199><c> how</c><00:07:03.360><c> is</c><00:07:03.599><c> gauze</c><00:07:03.919><c> in</c>

00:07:04.029 --> 00:07:04.039 align:start position:0%
your insurance policy how is gauze in
 

00:07:04.039 --> 00:07:07.390 align:start position:0%
your insurance policy how is gauze in
your<00:07:04.240><c> operation</c><00:07:05.039><c> covered</c><00:07:06.039><c> uh</c><00:07:06.520><c> and</c><00:07:06.680><c> in</c><00:07:06.840><c> this</c>

00:07:07.390 --> 00:07:07.400 align:start position:0%
your operation covered uh and in this
 

00:07:07.400 --> 00:07:09.390 align:start position:0%
your operation covered uh and in this
case

00:07:09.390 --> 00:07:09.400 align:start position:0%
case
 

00:07:09.400 --> 00:07:14.110 align:start position:0%
case
uh<00:07:10.400><c> the</c><00:07:10.560><c> vanilla</c><00:07:10.960><c> version</c><00:07:11.560><c> says</c><00:07:12.879><c> uh</c><00:07:13.879><c> it's</c>

00:07:14.110 --> 00:07:14.120 align:start position:0%
uh the vanilla version says uh it's
 

00:07:14.120 --> 00:07:16.390 align:start position:0%
uh the vanilla version says uh it's
covered<00:07:14.720><c> if</c><00:07:15.560><c> is</c><00:07:15.720><c> covered</c><00:07:16.000><c> as</c><00:07:16.080><c> part</c><00:07:16.240><c> of</c><00:07:16.319><c> your</c>

00:07:16.390 --> 00:07:16.400 align:start position:0%
covered if is covered as part of your
 

00:07:16.400 --> 00:07:17.510 align:start position:0%
covered if is covered as part of your
surgical<00:07:16.680><c> tool</c><00:07:16.919><c> procedure</c><00:07:17.240><c> you</c><00:07:17.319><c> know</c><00:07:17.440><c> it</c>

00:07:17.510 --> 00:07:17.520 align:start position:0%
surgical tool procedure you know it
 

00:07:17.520 --> 00:07:18.869 align:start position:0%
surgical tool procedure you know it
gives<00:07:17.680><c> this</c><00:07:17.840><c> long</c><00:07:18.120><c> answer</c><00:07:18.400><c> that</c><00:07:18.560><c> has</c><00:07:18.720><c> all</c>

00:07:18.869 --> 00:07:18.879 align:start position:0%
gives this long answer that has all
 

00:07:18.879 --> 00:07:23.270 align:start position:0%
gives this long answer that has all
sorts<00:07:19.080><c> of</c><00:07:19.560><c> details</c><00:07:20.560><c> uh</c><00:07:21.280><c> whereas</c><00:07:22.000><c> the</c><00:07:22.639><c> par</c><00:07:23.039><c> list</c>

00:07:23.270 --> 00:07:23.280 align:start position:0%
sorts of details uh whereas the par list
 

00:07:23.280 --> 00:07:26.589 align:start position:0%
sorts of details uh whereas the par list
version<00:07:24.039><c> gives</c><00:07:24.319><c> you</c><00:07:25.319><c> uh</c><00:07:25.879><c> a</c><00:07:26.080><c> much</c><00:07:26.240><c> simpler</c>

00:07:26.589 --> 00:07:26.599 align:start position:0%
version gives you uh a much simpler
 

00:07:26.599 --> 00:07:28.029 align:start position:0%
version gives you uh a much simpler
answer<00:07:26.879><c> procedure</c><00:07:27.240><c> charges</c><00:07:27.599><c> cover</c><00:07:27.840><c> the</c><00:07:27.919><c> use</c>

00:07:28.029 --> 00:07:28.039 align:start position:0%
answer procedure charges cover the use
 

00:07:28.039 --> 00:07:30.869 align:start position:0%
answer procedure charges cover the use
of<00:07:28.160><c> gods</c><00:07:28.400><c> in</c><00:07:28.520><c> your</c><00:07:28.720><c> operation</c><00:07:29.199><c> so</c><00:07:29.319><c> your</c><00:07:29.879><c> fine</c>

00:07:30.869 --> 00:07:30.879 align:start position:0%
of gods in your operation so your fine
 

00:07:30.879 --> 00:07:33.869 align:start position:0%
of gods in your operation so your fine
um<00:07:31.840><c> this</c><00:07:31.960><c> is</c><00:07:32.240><c> how</c><00:07:32.479><c> you</c><00:07:32.639><c> can</c><00:07:32.919><c> use</c><00:07:33.319><c> the</c><00:07:33.479><c> power</c><00:07:33.720><c> of</c>

00:07:33.869 --> 00:07:33.879 align:start position:0%
um this is how you can use the power of
 

00:07:33.879 --> 00:07:38.830 align:start position:0%
um this is how you can use the power of
Lop<00:07:34.319><c> pars</c><00:07:35.319><c> to</c><00:07:36.319><c> uh</c><00:07:37.080><c> in</c><00:07:37.240><c> effect</c><00:07:37.840><c> pre-process</c>

00:07:38.830 --> 00:07:38.840 align:start position:0%
Lop pars to uh in effect pre-process
 

00:07:38.840 --> 00:07:41.950 align:start position:0%
Lop pars to uh in effect pre-process
your<00:07:39.440><c> complex</c><00:07:39.960><c> documents</c><00:07:40.560><c> to</c><00:07:40.759><c> be</c><00:07:41.520><c> simple</c>

00:07:41.950 --> 00:07:41.960 align:start position:0%
your complex documents to be simple
 

00:07:41.960 --> 00:07:44.830 align:start position:0%
your complex documents to be simple
enough<00:07:42.400><c> that</c><00:07:42.520><c> an</c><00:07:42.680><c> llm</c><00:07:43.199><c> can</c><00:07:43.560><c> usably</c><00:07:44.520><c> understand</c>

00:07:44.830 --> 00:07:44.840 align:start position:0%
enough that an llm can usably understand
 

00:07:44.840 --> 00:07:47.270 align:start position:0%
enough that an llm can usably understand
them<00:07:45.639><c> uh</c><00:07:45.759><c> and</c><00:07:45.960><c> provide</c><00:07:46.599><c> really</c><00:07:46.840><c> helpful</c>

00:07:47.270 --> 00:07:47.280 align:start position:0%
them uh and provide really helpful
 

00:07:47.280 --> 00:07:49.510 align:start position:0%
them uh and provide really helpful
answers<00:07:48.240><c> so</c><00:07:48.520><c> I</c><00:07:48.639><c> hope</c><00:07:48.879><c> this</c><00:07:49.039><c> gives</c><00:07:49.199><c> you</c><00:07:49.319><c> some</c>

00:07:49.510 --> 00:07:49.520 align:start position:0%
answers so I hope this gives you some
 

00:07:49.520 --> 00:07:51.189 align:start position:0%
answers so I hope this gives you some
ideas<00:07:49.879><c> of</c><00:07:50.080><c> how</c><00:07:50.240><c> you</c><00:07:50.360><c> can</c><00:07:50.520><c> use</c><00:07:50.759><c> parsing</c>

00:07:51.189 --> 00:07:51.199 align:start position:0%
ideas of how you can use parsing
 

00:07:51.199 --> 00:07:53.469 align:start position:0%
ideas of how you can use parsing
instructions<00:07:51.639><c> in</c><00:07:51.800><c> llap</c><00:07:52.199><c> parse</c><00:07:53.039><c> uh</c><00:07:53.120><c> for</c><00:07:53.280><c> your</c>

00:07:53.469 --> 00:07:53.479 align:start position:0%
instructions in llap parse uh for your
 

00:07:53.479 --> 00:07:56.070 align:start position:0%
instructions in llap parse uh for your
own<00:07:54.199><c> applications</c><00:07:55.199><c> cool</c><00:07:55.520><c> and</c><00:07:55.639><c> I'll</c><00:07:55.800><c> see</c><00:07:55.919><c> you</c>

00:07:56.070 --> 00:07:56.080 align:start position:0%
own applications cool and I'll see you
 

00:07:56.080 --> 00:07:59.120 align:start position:0%
own applications cool and I'll see you
next<00:07:56.319><c> time</c>

