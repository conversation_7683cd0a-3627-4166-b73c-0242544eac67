WEBVTT
Kind: captions
Language: en

00:00:00.030 --> 00:00:02.119 align:start position:0%
 
all<00:00:00.510><c> right</c><00:00:00.810><c> welcome</c><00:00:01.110><c> to</c><00:00:01.230><c> another</c><00:00:01.469><c> video</c><00:00:01.680><c> and</c>

00:00:02.119 --> 00:00:02.129 align:start position:0%
all right welcome to another video and
 

00:00:02.129 --> 00:00:04.700 align:start position:0%
all right welcome to another video and
this<00:00:02.939><c> time</c><00:00:03.000><c> your</c><00:00:03.600><c> beginning</c><00:00:03.929><c> getting</c><00:00:04.319><c> a</c><00:00:04.410><c> quiz</c>

00:00:04.700 --> 00:00:04.710 align:start position:0%
this time your beginning getting a quiz
 

00:00:04.710 --> 00:00:06.769 align:start position:0%
this time your beginning getting a quiz
I'm<00:00:05.520><c> not</c><00:00:05.670><c> gonna</c><00:00:05.819><c> give</c><00:00:05.940><c> any</c><00:00:06.060><c> hints</c><00:00:06.330><c> or</c><00:00:06.629><c> anything</c>

00:00:06.769 --> 00:00:06.779 align:start position:0%
I'm not gonna give any hints or anything
 

00:00:06.779 --> 00:00:07.160 align:start position:0%
I'm not gonna give any hints or anything
like<00:00:06.930><c> that</c>

00:00:07.160 --> 00:00:07.170 align:start position:0%
like that
 

00:00:07.170 --> 00:00:09.620 align:start position:0%
like that
but<00:00:07.770><c> good</c><00:00:08.189><c> luck</c><00:00:08.370><c> there's</c><00:00:08.790><c> me</c><00:00:08.910><c> five</c><00:00:09.120><c> questions</c>

00:00:09.620 --> 00:00:09.630 align:start position:0%
but good luck there's me five questions
 

00:00:09.630 --> 00:00:12.470 align:start position:0%
but good luck there's me five questions
and<00:00:10.099><c> there's</c><00:00:11.099><c> me</c><00:00:11.250><c> about</c><00:00:11.429><c> 10</c><00:00:11.670><c> seconds</c><00:00:12.059><c> given</c>

00:00:12.470 --> 00:00:12.480 align:start position:0%
and there's me about 10 seconds given
 

00:00:12.480 --> 00:00:14.150 align:start position:0%
and there's me about 10 seconds given
each<00:00:12.630><c> time</c><00:00:12.840><c> there's</c><00:00:13.080><c> countdown</c><00:00:13.349><c> timer</c><00:00:13.559><c> pause</c>

00:00:14.150 --> 00:00:14.160 align:start position:0%
each time there's countdown timer pause
 

00:00:14.160 --> 00:00:16.070 align:start position:0%
each time there's countdown timer pause
it<00:00:14.400><c> try</c><00:00:14.940><c> to</c><00:00:15.000><c> go</c><00:00:15.089><c> through</c><00:00:15.269><c> it</c><00:00:15.360><c> come</c><00:00:15.750><c> up</c><00:00:15.870><c> with</c><00:00:15.960><c> an</c>

00:00:16.070 --> 00:00:16.080 align:start position:0%
it try to go through it come up with an
 

00:00:16.080 --> 00:00:18.470 align:start position:0%
it try to go through it come up with an
answer<00:00:16.199><c> and</c><00:00:16.500><c> after</c><00:00:17.430><c> all</c><00:00:17.880><c> those</c><00:00:18.119><c> are</c><00:00:18.240><c> over</c>

00:00:18.470 --> 00:00:18.480 align:start position:0%
answer and after all those are over
 

00:00:18.480 --> 00:00:21.140 align:start position:0%
answer and after all those are over
we'll<00:00:19.380><c> go</c><00:00:19.529><c> through</c><00:00:19.740><c> them</c><00:00:19.920><c> alright</c><00:00:20.460><c> I</c><00:00:20.640><c> see</c><00:00:21.090><c> you</c>

00:00:21.140 --> 00:00:21.150 align:start position:0%
we'll go through them alright I see you
 

00:00:21.150 --> 00:00:51.190 align:start position:0%
we'll go through them alright I see you
shortly

00:00:51.190 --> 00:00:51.200 align:start position:0%
 
 

00:00:51.200 --> 00:01:35.660 align:start position:0%
 
you

00:01:35.660 --> 00:01:35.670 align:start position:0%
 
 

00:01:35.670 --> 00:01:38.940 align:start position:0%
 
all<00:01:36.670><c> right</c><00:01:37.149><c> well</c><00:01:37.420><c> the</c><00:01:38.200><c> quiz</c><00:01:38.380><c> is</c><00:01:38.500><c> over</c><00:01:38.619><c> and</c>

00:01:38.940 --> 00:01:38.950 align:start position:0%
all right well the quiz is over and
 

00:01:38.950 --> 00:01:40.889 align:start position:0%
all right well the quiz is over and
we're<00:01:39.610><c> gonna</c><00:01:39.729><c> go</c><00:01:39.880><c> over</c><00:01:39.970><c> the</c><00:01:40.030><c> questions</c><00:01:40.420><c> now</c><00:01:40.630><c> so</c>

00:01:40.889 --> 00:01:40.899 align:start position:0%
we're gonna go over the questions now so
 

00:01:40.899 --> 00:01:42.749 align:start position:0%
we're gonna go over the questions now so
hope<00:01:41.289><c> you</c><00:01:41.380><c> did</c><00:01:41.500><c> well</c><00:01:41.530><c> and</c><00:01:41.950><c> you</c><00:01:42.429><c> were</c><00:01:42.520><c> able</c><00:01:42.670><c> to</c>

00:01:42.749 --> 00:01:42.759 align:start position:0%
hope you did well and you were able to
 

00:01:42.759 --> 00:01:44.160 align:start position:0%
hope you did well and you were able to
at<00:01:42.850><c> least</c><00:01:42.880><c> ja</c><00:01:43.330><c> something</c><00:01:43.720><c> down</c><00:01:43.869><c> and</c>

00:01:44.160 --> 00:01:44.170 align:start position:0%
at least ja something down and
 

00:01:44.170 --> 00:01:46.230 align:start position:0%
at least ja something down and
understand<00:01:44.920><c> a</c><00:01:45.250><c> little</c><00:01:45.789><c> bit</c><00:01:45.880><c> of</c><00:01:45.970><c> how</c><00:01:46.060><c> you</c><00:01:46.149><c> got</c>

00:01:46.230 --> 00:01:46.240 align:start position:0%
understand a little bit of how you got
 

00:01:46.240 --> 00:01:48.749 align:start position:0%
understand a little bit of how you got
to<00:01:46.390><c> the</c><00:01:46.479><c> time</c><00:01:46.659><c> custody</c><00:01:47.140><c> okay</c><00:01:48.070><c> so</c><00:01:48.340><c> let's</c><00:01:48.490><c> begin</c>

00:01:48.749 --> 00:01:48.759 align:start position:0%
to the time custody okay so let's begin
 

00:01:48.759 --> 00:01:51.210 align:start position:0%
to the time custody okay so let's begin
so<00:01:49.630><c> with</c><00:01:49.959><c> question</c><00:01:50.259><c> number</c><00:01:50.289><c> one</c><00:01:50.590><c> the</c>

00:01:51.210 --> 00:01:51.220 align:start position:0%
so with question number one the
 

00:01:51.220 --> 00:01:52.710 align:start position:0%
so with question number one the
following<00:01:51.369><c> code</c><00:01:51.670><c> computes</c><00:01:52.060><c> the</c><00:01:52.149><c> product</c><00:01:52.450><c> of</c><00:01:52.539><c> a</c>

00:01:52.710 --> 00:01:52.720 align:start position:0%
following code computes the product of a
 

00:01:52.720 --> 00:01:55.020 align:start position:0%
following code computes the product of a
and<00:01:52.750><c> B</c><00:01:52.899><c> what</c><00:01:53.259><c> is</c><00:01:53.289><c> the</c><00:01:53.380><c> runtime</c><00:01:53.759><c> all</c><00:01:54.759><c> right</c><00:01:54.970><c> so</c>

00:01:55.020 --> 00:01:55.030 align:start position:0%
and B what is the runtime all right so
 

00:01:55.030 --> 00:01:56.039 align:start position:0%
and B what is the runtime all right so
I'm<00:01:55.119><c> not</c><00:01:55.149><c> gonna</c><00:01:55.299><c> actually</c><00:01:55.479><c> go</c><00:01:55.720><c> through</c><00:01:55.929><c> the</c>

00:01:56.039 --> 00:01:56.049 align:start position:0%
I'm not gonna actually go through the
 

00:01:56.049 --> 00:01:57.419 align:start position:0%
I'm not gonna actually go through the
meaning<00:01:56.289><c> of</c><00:01:56.350><c> the</c><00:01:56.470><c> code</c><00:01:56.619><c> for</c><00:01:56.830><c> each</c><00:01:56.920><c> of</c><00:01:57.039><c> these</c><00:01:57.130><c> or</c>

00:01:57.419 --> 00:01:57.429 align:start position:0%
meaning of the code for each of these or
 

00:01:57.429 --> 00:01:58.770 align:start position:0%
meaning of the code for each of these or
just<00:01:57.880><c> pretty</c><00:01:58.119><c> much</c><00:01:58.209><c> just</c><00:01:58.240><c> gonna</c><00:01:58.479><c> go</c><00:01:58.600><c> with</c><00:01:58.720><c> the</c>

00:01:58.770 --> 00:01:58.780 align:start position:0%
just pretty much just gonna go with the
 

00:01:58.780 --> 00:02:00.570 align:start position:0%
just pretty much just gonna go with the
answer<00:01:59.020><c> all</c><00:01:59.350><c> right</c>

00:02:00.570 --> 00:02:00.580 align:start position:0%
answer all right
 

00:02:00.580 --> 00:02:02.820 align:start position:0%
answer all right
so<00:02:01.119><c> this</c><00:02:01.990><c> just</c><00:02:02.080><c> like</c><00:02:02.289><c> such</c><00:02:02.500><c> a</c><00:02:02.530><c> piece</c><00:02:02.740><c> of</c>

00:02:02.820 --> 00:02:02.830 align:start position:0%
so this just like such a piece of
 

00:02:02.830 --> 00:02:05.550 align:start position:0%
so this just like such a piece of
product<00:02:03.159><c> of</c><00:02:03.310><c> two</c><00:02:03.640><c> inputs</c><00:02:04.000><c> a</c><00:02:04.179><c> and</c><00:02:04.209><c> B</c><00:02:04.560><c> there's</c>

00:02:05.550 --> 00:02:05.560 align:start position:0%
product of two inputs a and B there's
 

00:02:05.560 --> 00:02:07.529 align:start position:0%
product of two inputs a and B there's
essentially<00:02:05.860><c> just</c><00:02:06.340><c> a</c><00:02:06.520><c> for</c><00:02:06.789><c> loop</c><00:02:06.880><c> here</c><00:02:07.119><c> that</c><00:02:07.420><c> we</c>

00:02:07.529 --> 00:02:07.539 align:start position:0%
essentially just a for loop here that we
 

00:02:07.539 --> 00:02:08.729 align:start position:0%
essentially just a for loop here that we
really<00:02:07.569><c> care</c><00:02:07.990><c> about</c><00:02:08.049><c> everything</c><00:02:08.619><c> is</c><00:02:08.709><c> a</c>

00:02:08.729 --> 00:02:08.739 align:start position:0%
really care about everything is a
 

00:02:08.739 --> 00:02:10.770 align:start position:0%
really care about everything is a
constant<00:02:09.190><c> time</c><00:02:09.369><c> so</c><00:02:09.610><c> this</c><00:02:10.209><c> for</c><00:02:10.450><c> loop</c><00:02:10.569><c> is</c><00:02:10.750><c> going</c>

00:02:10.770 --> 00:02:10.780 align:start position:0%
constant time so this for loop is going
 

00:02:10.780 --> 00:02:16.770 align:start position:0%
constant time so this for loop is going
from<00:02:11.200><c> zero</c><00:02:11.590><c> to</c><00:02:13.410><c> whatever</c><00:02:14.430><c> B</c><00:02:15.430><c> is</c><00:02:15.640><c> right</c><00:02:16.390><c> so</c><00:02:16.630><c> this</c>

00:02:16.770 --> 00:02:16.780 align:start position:0%
from zero to whatever B is right so this
 

00:02:16.780 --> 00:02:21.210 align:start position:0%
from zero to whatever B is right so this
is<00:02:16.840><c> 4</c><00:02:17.440><c> this</c><00:02:17.799><c> is</c><00:02:18.310><c> gonna</c><00:02:19.170><c> sue</c><00:02:20.170><c> me</c><00:02:20.380><c> 0</c><00:02:20.829><c> we're</c><00:02:21.130><c> gonna</c>

00:02:21.210 --> 00:02:21.220 align:start position:0%
is 4 this is gonna sue me 0 we're gonna
 

00:02:21.220 --> 00:02:23.940 align:start position:0%
is 4 this is gonna sue me 0 we're gonna
go<00:02:21.370><c> 0</c><00:02:21.640><c> to</c><00:02:21.790><c> 3</c><00:02:22.540><c> so</c><00:02:22.720><c> that's</c><00:02:22.840><c> 4</c><00:02:23.109><c> times</c><00:02:23.140><c> you</c><00:02:23.799><c> know</c><00:02:23.859><c> we</c>

00:02:23.940 --> 00:02:23.950 align:start position:0%
go 0 to 3 so that's 4 times you know we
 

00:02:23.950 --> 00:02:26.900 align:start position:0%
go 0 to 3 so that's 4 times you know we
rent<00:02:24.130><c> there's</c><00:02:24.280><c> me</c><00:02:24.400><c> forward</c><00:02:24.640><c> durations</c><00:02:25.000><c> and</c>

00:02:26.900 --> 00:02:26.910 align:start position:0%
rent there's me forward durations and
 

00:02:26.910 --> 00:02:30.380 align:start position:0%
rent there's me forward durations and
then<00:02:27.910><c> we're</c><00:02:28.000><c> gonna</c><00:02:28.090><c> add</c><00:02:28.359><c> a</c><00:02:28.720><c> to</c><00:02:29.290><c> the</c><00:02:29.410><c> sum</c><00:02:29.680><c> on</c>

00:02:30.380 --> 00:02:30.390 align:start position:0%
then we're gonna add a to the sum on
 

00:02:30.390 --> 00:02:35.699 align:start position:0%
then we're gonna add a to the sum on
each<00:02:31.390><c> time</c><00:02:31.750><c> so</c><00:02:34.019><c> so</c><00:02:35.019><c> really</c><00:02:35.170><c> all</c><00:02:35.380><c> we're</c><00:02:35.500><c> doing</c>

00:02:35.699 --> 00:02:35.709 align:start position:0%
each time so so really all we're doing
 

00:02:35.709 --> 00:02:38.030 align:start position:0%
each time so so really all we're doing
is<00:02:35.799><c> just</c><00:02:35.950><c> going</c><00:02:36.130><c> over</c><00:02:36.790><c> this</c><00:02:37.450><c> for</c><00:02:37.750><c> loop</c>

00:02:38.030 --> 00:02:38.040 align:start position:0%
is just going over this for loop
 

00:02:38.040 --> 00:02:41.759 align:start position:0%
is just going over this for loop
whatever<00:02:39.040><c> B</c><00:02:39.670><c> is</c><00:02:39.850><c> so</c><00:02:40.269><c> the</c><00:02:40.450><c> answer</c><00:02:40.720><c> so</c><00:02:41.650><c> the</c>

00:02:41.759 --> 00:02:41.769 align:start position:0%
whatever B is so the answer so the
 

00:02:41.769 --> 00:02:46.080 align:start position:0%
whatever B is so the answer so the
answer<00:02:42.100><c> here</c><00:02:42.160><c> is</c><00:02:42.730><c> just</c><00:02:43.150><c> big</c><00:02:43.510><c> o</c><00:02:43.690><c> of</c><00:02:43.989><c> B</c><00:02:44.940><c> you</c><00:02:45.940><c> say</c>

00:02:46.080 --> 00:02:46.090 align:start position:0%
answer here is just big o of B you say
 

00:02:46.090 --> 00:02:48.360 align:start position:0%
answer here is just big o of B you say
Big<00:02:46.239><c> O</c><00:02:46.329><c> of</c><00:02:46.390><c> n</c><00:02:46.540><c> doesn't</c><00:02:47.170><c> matter</c><00:02:47.290><c> just</c><00:02:47.560><c> B</c><00:02:48.010><c> is</c><00:02:48.160><c> the</c>

00:02:48.360 --> 00:02:48.370 align:start position:0%
Big O of n doesn't matter just B is the
 

00:02:48.370 --> 00:02:51.420 align:start position:0%
Big O of n doesn't matter just B is the
input<00:02:49.000><c> here</c><00:02:49.180><c> it</c><00:02:50.079><c> really</c><00:02:50.440><c> it's</c><00:02:51.010><c> not</c><00:02:51.100><c> that</c><00:02:51.250><c> big</c><00:02:51.370><c> a</c>

00:02:51.420 --> 00:02:51.430 align:start position:0%
input here it really it's not that big a
 

00:02:51.430 --> 00:02:52.830 align:start position:0%
input here it really it's not that big a
deal<00:02:51.670><c> ok</c><00:02:51.880><c> but</c><00:02:52.150><c> that's</c><00:02:52.299><c> the</c><00:02:52.359><c> answer</c><00:02:52.420><c> big</c><00:02:52.750><c> it</c>

00:02:52.830 --> 00:02:52.840 align:start position:0%
deal ok but that's the answer big it
 

00:02:52.840 --> 00:02:54.660 align:start position:0%
deal ok but that's the answer big it
would<00:02:52.900><c> be</c><00:02:53.049><c> because</c><00:02:53.680><c> it's</c><00:02:53.829><c> just</c><00:02:53.920><c> based</c><00:02:54.160><c> on</c><00:02:54.340><c> the</c>

00:02:54.660 --> 00:02:54.670 align:start position:0%
would be because it's just based on the
 

00:02:54.670 --> 00:02:56.699 align:start position:0%
would be because it's just based on the
length<00:02:54.910><c> whatever</c><00:02:55.450><c> B</c><00:02:56.079><c> is</c><00:02:56.260><c> that's</c><00:02:56.530><c> the</c><00:02:56.590><c> input</c>

00:02:56.699 --> 00:02:56.709 align:start position:0%
length whatever B is that's the input
 

00:02:56.709 --> 00:02:59.350 align:start position:0%
length whatever B is that's the input
all<00:02:57.670><c> right</c><00:02:57.880><c> okay</c><00:02:58.810><c> this</c><00:02:58.930><c> question</c>

00:02:59.350 --> 00:02:59.360 align:start position:0%
all right okay this question
 

00:02:59.360 --> 00:03:02.950 align:start position:0%
all right okay this question
to<00:02:59.480><c> the</c><00:02:59.870><c> following</c><00:03:00.140><c> code</c><00:03:00.290><c> computes</c><00:03:00.800><c> a</c><00:03:01.240><c> ^</c><00:03:02.240><c> B</c><00:03:02.690><c> was</c>

00:03:02.950 --> 00:03:02.960 align:start position:0%
to the following code computes a ^ B was
 

00:03:02.960 --> 00:03:07.570 align:start position:0%
to the following code computes a ^ B was
its<00:03:03.170><c> runtime</c><00:03:03.400><c> all</c><00:03:04.400><c> right</c><00:03:04.960><c> so</c><00:03:06.100><c> we</c><00:03:07.100><c> have</c><00:03:07.130><c> some</c><00:03:07.220><c> if</c>

00:03:07.570 --> 00:03:07.580 align:start position:0%
its runtime all right so we have some if
 

00:03:07.580 --> 00:03:09.790 align:start position:0%
its runtime all right so we have some if
statements<00:03:07.640><c> here</c><00:03:08.420><c> and</c><00:03:08.540><c> this</c><00:03:08.630><c> is</c><00:03:08.780><c> recursion</c><00:03:09.650><c> as</c>

00:03:09.790 --> 00:03:09.800 align:start position:0%
statements here and this is recursion as
 

00:03:09.800 --> 00:03:12.850 align:start position:0%
statements here and this is recursion as
you<00:03:10.100><c> can</c><00:03:10.220><c> tell</c><00:03:10.400><c> so</c><00:03:11.260><c> we</c><00:03:12.260><c> don't</c><00:03:12.410><c> care</c><00:03:12.500><c> about</c><00:03:12.530><c> the</c>

00:03:12.850 --> 00:03:12.860 align:start position:0%
you can tell so we don't care about the
 

00:03:12.860 --> 00:03:15.550 align:start position:0%
you can tell so we don't care about the
two<00:03:13.370><c> base</c><00:03:13.550><c> cases</c><00:03:13.780><c> they</c><00:03:14.780><c> don't</c><00:03:14.960><c> matter</c><00:03:15.080><c> we're</c>

00:03:15.550 --> 00:03:15.560 align:start position:0%
two base cases they don't matter we're
 

00:03:15.560 --> 00:03:16.780 align:start position:0%
two base cases they don't matter we're
just<00:03:15.740><c> worried</c><00:03:15.920><c> about</c><00:03:15.950><c> this</c><00:03:16.430><c> says</c><00:03:16.580><c> return</c>

00:03:16.780 --> 00:03:16.790 align:start position:0%
just worried about this says return
 

00:03:16.790 --> 00:03:21.460 align:start position:0%
just worried about this says return
eight<00:03:17.210><c> times</c><00:03:17.660><c> the</c><00:03:17.960><c> power</c><00:03:18.200><c> of</c><00:03:18.380><c> a</c><00:03:19.960><c> and</c><00:03:20.960><c> then</c><00:03:21.440><c> B</c>

00:03:21.460 --> 00:03:21.470 align:start position:0%
eight times the power of a and then B
 

00:03:21.470 --> 00:03:24.810 align:start position:0%
eight times the power of a and then B
minus<00:03:21.890><c> one</c><00:03:22.100><c> so</c><00:03:22.340><c> this</c><00:03:22.430><c> B</c><00:03:22.610><c> minus</c><00:03:22.880><c> one</c><00:03:23.150><c> is</c><00:03:23.420><c> the</c><00:03:23.840><c> key</c>

00:03:24.810 --> 00:03:24.820 align:start position:0%
minus one so this B minus one is the key
 

00:03:24.820 --> 00:03:27.670 align:start position:0%
minus one so this B minus one is the key
since<00:03:25.820><c> that's</c><00:03:26.510><c> what's</c><00:03:26.810><c> being</c><00:03:27.020><c> decremented</c>

00:03:27.670 --> 00:03:27.680 align:start position:0%
since that's what's being decremented
 

00:03:27.680 --> 00:03:29.230 align:start position:0%
since that's what's being decremented
and<00:03:27.860><c> that's</c><00:03:28.040><c> all</c><00:03:28.280><c> the</c><00:03:28.490><c> base</c><00:03:28.670><c> cases</c><00:03:28.940><c> are</c>

00:03:29.230 --> 00:03:29.240 align:start position:0%
and that's all the base cases are
 

00:03:29.240 --> 00:03:33.520 align:start position:0%
and that's all the base cases are
revolving<00:03:29.840><c> around</c><00:03:30.100><c> the</c><00:03:31.100><c> B</c><00:03:31.520><c> input</c><00:03:32.530><c> then</c>

00:03:33.520 --> 00:03:33.530 align:start position:0%
revolving around the B input then
 

00:03:33.530 --> 00:03:36.790 align:start position:0%
revolving around the B input then
everything<00:03:34.180><c> then</c><00:03:35.180><c> this</c><00:03:35.420><c> recursively</c><00:03:36.140><c> being</c>

00:03:36.790 --> 00:03:36.800 align:start position:0%
everything then this recursively being
 

00:03:36.800 --> 00:03:39.100 align:start position:0%
everything then this recursively being
called<00:03:37.100><c> is</c><00:03:37.430><c> based</c><00:03:38.150><c> on</c><00:03:38.270><c> the</c><00:03:38.330><c> input</c><00:03:38.540><c> of</c><00:03:38.570><c> B</c><00:03:38.780><c> so</c>

00:03:39.100 --> 00:03:39.110 align:start position:0%
called is based on the input of B so
 

00:03:39.110 --> 00:03:41.890 align:start position:0%
called is based on the input of B so
whatever<00:03:39.640><c> B</c><00:03:40.640><c> is</c><00:03:40.820><c> that's</c><00:03:41.060><c> how</c><00:03:41.150><c> many</c><00:03:41.300><c> times</c><00:03:41.480><c> this</c>

00:03:41.890 --> 00:03:41.900 align:start position:0%
whatever B is that's how many times this
 

00:03:41.900 --> 00:03:44.380 align:start position:0%
whatever B is that's how many times this
we're<00:03:42.230><c> gonna</c><00:03:42.380><c> recruit</c><00:03:43.250><c> that's</c><00:03:43.670><c> how</c><00:03:43.820><c> how</c><00:03:44.209><c> many</c>

00:03:44.380 --> 00:03:44.390 align:start position:0%
we're gonna recruit that's how how many
 

00:03:44.390 --> 00:03:46.570 align:start position:0%
we're gonna recruit that's how how many
times<00:03:44.600><c> the</c><00:03:45.290><c> power</c><00:03:45.500><c> method</c><00:03:45.770><c> you're</c><00:03:46.370><c> gonna</c><00:03:46.490><c> be</c>

00:03:46.570 --> 00:03:46.580 align:start position:0%
times the power method you're gonna be
 

00:03:46.580 --> 00:03:49.780 align:start position:0%
times the power method you're gonna be
recursively<00:03:46.820><c> called</c><00:03:47.330><c> and</c><00:03:48.260><c> so</c><00:03:49.070><c> with</c><00:03:49.250><c> that</c><00:03:49.400><c> the</c>

00:03:49.780 --> 00:03:49.790 align:start position:0%
recursively called and so with that the
 

00:03:49.790 --> 00:03:53.380 align:start position:0%
recursively called and so with that the
answer<00:03:50.180><c> to</c><00:03:50.300><c> this</c><00:03:50.450><c> is</c><00:03:50.720><c> Z</c><00:03:51.350><c> is</c><00:03:51.680><c> Big</c><00:03:52.220><c> O</c><00:03:52.370><c> of</c><00:03:52.400><c> B</c><00:03:52.850><c> or</c><00:03:53.209><c> Big</c>

00:03:53.380 --> 00:03:53.390 align:start position:0%
answer to this is Z is Big O of B or Big
 

00:03:53.390 --> 00:03:54.280 align:start position:0%
answer to this is Z is Big O of B or Big
O<00:03:53.420><c> of</c><00:03:53.480><c> n</c><00:03:53.570><c> which</c><00:03:53.900><c> ever</c>

00:03:54.280 --> 00:03:54.290 align:start position:0%
O of n which ever
 

00:03:54.290 --> 00:03:57.160 align:start position:0%
O of n which ever
okay<00:03:54.950><c> okay</c><00:03:55.550><c> question</c><00:03:55.970><c> number</c><00:03:56.000><c> three</c><00:03:56.300><c> the</c>

00:03:57.160 --> 00:03:57.170 align:start position:0%
okay okay question number three the
 

00:03:57.170 --> 00:04:01.390 align:start position:0%
okay okay question number three the
following<00:03:57.440><c> code</c><00:03:57.590><c> confuse</c><00:03:58.100><c> a</c><00:03:58.250><c> mod</c><00:03:58.610><c> B</c><00:03:59.380><c> so</c><00:04:00.400><c> what</c>

00:04:01.390 --> 00:04:01.400 align:start position:0%
following code confuse a mod B so what
 

00:04:01.400 --> 00:04:03.670 align:start position:0%
following code confuse a mod B so what
we<00:04:01.520><c> have</c><00:04:01.670><c> here</c><00:04:01.700><c> we</c><00:04:01.940><c> have</c><00:04:01.970><c> two</c><00:04:02.090><c> inputs</c><00:04:02.530><c> we</c><00:04:03.530><c> don't</c>

00:04:03.670 --> 00:04:03.680 align:start position:0%
we have here we have two inputs we don't
 

00:04:03.680 --> 00:04:07.479 align:start position:0%
we have here we have two inputs we don't
trust<00:04:03.830><c> if</c><00:04:04.010><c> statements</c><00:04:04.519><c> the</c><00:04:06.489><c> assignment</c>

00:04:07.479 --> 00:04:07.489 align:start position:0%
trust if statements the assignment
 

00:04:07.489 --> 00:04:09.270 align:start position:0%
trust if statements the assignment
variable<00:04:07.630><c> and</c><00:04:08.630><c> then</c><00:04:08.750><c> we're</c><00:04:08.930><c> just</c><00:04:08.959><c> returning</c>

00:04:09.270 --> 00:04:09.280 align:start position:0%
variable and then we're just returning
 

00:04:09.280 --> 00:04:12.009 align:start position:0%
variable and then we're just returning
once<00:04:10.280><c> so</c><00:04:10.640><c> all</c><00:04:11.000><c> all</c><00:04:11.390><c> this</c><00:04:11.450><c> is</c><00:04:11.510><c> all</c><00:04:11.780><c> is</c><00:04:11.870><c> happening</c>

00:04:12.009 --> 00:04:12.019 align:start position:0%
once so all all this is all is happening
 

00:04:12.019 --> 00:04:13.120 align:start position:0%
once so all all this is all is happening
is<00:04:12.320><c> we're</c><00:04:12.440><c> going</c><00:04:12.530><c> through</c><00:04:12.739><c> each</c><00:04:12.920><c> of</c><00:04:13.010><c> these</c>

00:04:13.120 --> 00:04:13.130 align:start position:0%
is we're going through each of these
 

00:04:13.130 --> 00:04:16.990 align:start position:0%
is we're going through each of these
lines<00:04:13.340><c> of</c><00:04:13.430><c> code</c><00:04:13.489><c> once</c><00:04:13.970><c> and</c><00:04:14.680><c> that's</c><00:04:15.680><c> it</c><00:04:16.000><c> down</c>

00:04:16.990 --> 00:04:17.000 align:start position:0%
lines of code once and that's it down
 

00:04:17.000 --> 00:04:19.930 align:start position:0%
lines of code once and that's it down
here<00:04:17.180><c> returning</c><00:04:17.299><c> a</c><00:04:17.720><c> -</c><00:04:18.250><c> the</c><00:04:19.250><c> div</c><00:04:19.459><c> very</c><00:04:19.760><c> which</c><00:04:19.910><c> is</c>

00:04:19.930 --> 00:04:19.940 align:start position:0%
here returning a - the div very which is
 

00:04:19.940 --> 00:04:23.260 align:start position:0%
here returning a - the div very which is
a<00:04:20.239><c> divided</c><00:04:20.600><c> by</c><00:04:20.630><c> B</c><00:04:20.870><c> times</c><00:04:21.350><c> B</c><00:04:21.530><c> so</c><00:04:22.040><c> this</c><00:04:22.970><c> is</c><00:04:23.030><c> just</c>

00:04:23.260 --> 00:04:23.270 align:start position:0%
a divided by B times B so this is just
 

00:04:23.270 --> 00:04:26.530 align:start position:0%
a divided by B times B so this is just
big<00:04:23.419><c> of</c><00:04:23.510><c> one</c><00:04:24.130><c> because</c><00:04:25.130><c> no</c><00:04:25.400><c> matter</c><00:04:25.640><c> what</c><00:04:26.210><c> the</c>

00:04:26.530 --> 00:04:26.540 align:start position:0%
big of one because no matter what the
 

00:04:26.540 --> 00:04:29.080 align:start position:0%
big of one because no matter what the
inputs<00:04:27.020><c> are</c><00:04:27.260><c> a</c><00:04:27.290><c> B</c><00:04:27.740><c> no</c><00:04:27.950><c> matter</c><00:04:28.250><c> what</c><00:04:28.610><c> sizes</c><00:04:28.970><c> are</c>

00:04:29.080 --> 00:04:29.090 align:start position:0%
inputs are a B no matter what sizes are
 

00:04:29.090 --> 00:04:32.290 align:start position:0%
inputs are a B no matter what sizes are
we're<00:04:29.840><c> only</c><00:04:29.990><c> going</c><00:04:30.230><c> through</c><00:04:30.530><c> this</c><00:04:30.680><c> once</c><00:04:31.550><c> we're</c>

00:04:32.290 --> 00:04:32.300 align:start position:0%
we're only going through this once we're
 

00:04:32.300 --> 00:04:34.170 align:start position:0%
we're only going through this once we're
gonna<00:04:32.480><c> throw</c><00:04:32.660><c> those</c><00:04:32.780><c> lines</c><00:04:33.440><c> of</c><00:04:33.560><c> code</c><00:04:33.680><c> once</c><00:04:33.890><c> so</c>

00:04:34.170 --> 00:04:34.180 align:start position:0%
gonna throw those lines of code once so
 

00:04:34.180 --> 00:04:36.640 align:start position:0%
gonna throw those lines of code once so
this<00:04:35.180><c> is</c><00:04:35.390><c> Big</c><00:04:35.570><c> O</c><00:04:35.660><c> one</c><00:04:35.870><c> okay</c>

00:04:36.640 --> 00:04:36.650 align:start position:0%
this is Big O one okay
 

00:04:36.650 --> 00:04:39.190 align:start position:0%
this is Big O one okay
okay<00:04:37.460><c> so</c><00:04:37.700><c> question</c><00:04:37.940><c> before</c><00:04:38.360><c> the</c><00:04:38.930><c> following</c>

00:04:39.190 --> 00:04:39.200 align:start position:0%
okay so question before the following
 

00:04:39.200 --> 00:04:41.710 align:start position:0%
okay so question before the following
code<00:04:39.410><c> performs</c><00:04:40.160><c> integer</c><00:04:40.550><c> division</c><00:04:40.790><c> was</c><00:04:41.540><c> this</c>

00:04:41.710 --> 00:04:41.720 align:start position:0%
code performs integer division was this
 

00:04:41.720 --> 00:04:43.930 align:start position:0%
code performs integer division was this
runtime<00:04:42.140><c> and</c><00:04:42.410><c> assume</c><00:04:43.340><c> a</c><00:04:43.550><c> and</c><00:04:43.640><c> B</c><00:04:43.729><c> it</c><00:04:43.820><c> was</c>

00:04:43.930 --> 00:04:43.940 align:start position:0%
runtime and assume a and B it was
 

00:04:43.940 --> 00:04:45.250 align:start position:0%
runtime and assume a and B it was
positive<00:04:44.330><c> this</c><00:04:44.540><c> is</c><00:04:44.690><c> one</c><00:04:44.780><c> of</c><00:04:44.840><c> the</c><00:04:44.900><c> tricky</c><00:04:45.110><c> ones</c>

00:04:45.250 --> 00:04:45.260 align:start position:0%
positive this is one of the tricky ones
 

00:04:45.260 --> 00:04:47.530 align:start position:0%
positive this is one of the tricky ones
because<00:04:45.590><c> as</c><00:04:45.919><c> you</c><00:04:46.460><c> see</c><00:04:46.610><c> there's</c><00:04:46.820><c> no</c><00:04:46.940><c> actual</c>

00:04:47.530 --> 00:04:47.540 align:start position:0%
because as you see there's no actual
 

00:04:47.540 --> 00:04:51.310 align:start position:0%
because as you see there's no actual
division<00:04:48.640><c> operation</c><00:04:50.050><c> their</c><00:04:51.050><c> division</c>

00:04:51.310 --> 00:04:51.320 align:start position:0%
division operation their division
 

00:04:51.320 --> 00:04:52.900 align:start position:0%
division operation their division
mathematical<00:04:51.800><c> patient</c><00:04:52.100><c> operation</c><00:04:52.520><c> happening</c>

00:04:52.900 --> 00:04:52.910 align:start position:0%
mathematical patient operation happening
 

00:04:52.910 --> 00:04:55.570 align:start position:0%
mathematical patient operation happening
here<00:04:53.120><c> it's</c><00:04:53.840><c> just</c><00:04:53.900><c> another</c><00:04:54.200><c> way</c><00:04:54.380><c> to</c><00:04:54.580><c> actually</c>

00:04:55.570 --> 00:04:55.580 align:start position:0%
here it's just another way to actually
 

00:04:55.580 --> 00:04:58.090 align:start position:0%
here it's just another way to actually
do<00:04:55.910><c> division</c><00:04:56.300><c> but</c><00:04:56.990><c> let's</c><00:04:57.650><c> go</c><00:04:57.770><c> over</c><00:04:57.800><c> what's</c>

00:04:58.090 --> 00:04:58.100 align:start position:0%
do division but let's go over what's
 

00:04:58.100 --> 00:05:00.370 align:start position:0%
do division but let's go over what's
going<00:04:58.280><c> on</c><00:04:58.610><c> there's</c><00:04:58.940><c> two</c><00:04:59.030><c> inputs</c><00:04:59.360><c> don't</c><00:05:00.260><c> care</c>

00:05:00.370 --> 00:05:00.380 align:start position:0%
going on there's two inputs don't care
 

00:05:00.380 --> 00:05:02.080 align:start position:0%
going on there's two inputs don't care
about<00:05:00.530><c> the</c><00:05:00.650><c> sign</c><00:05:00.800><c> variables</c><00:05:01.280><c> so</c><00:05:01.790><c> here's</c><00:05:01.970><c> our</c>

00:05:02.080 --> 00:05:02.090 align:start position:0%
about the sign variables so here's our
 

00:05:02.090 --> 00:05:04.030 align:start position:0%
about the sign variables so here's our
loop<00:05:02.270><c> while</c><00:05:02.810><c> the</c><00:05:02.960><c> sum</c><00:05:03.169><c> is</c><00:05:03.320><c> less</c><00:05:03.350><c> than</c><00:05:03.680><c> or</c><00:05:03.740><c> equal</c>

00:05:04.030 --> 00:05:04.040 align:start position:0%
loop while the sum is less than or equal
 

00:05:04.040 --> 00:05:07.270 align:start position:0%
loop while the sum is less than or equal
to<00:05:04.160><c> a</c><00:05:04.479><c> sum</c><00:05:05.479><c> plus</c><00:05:05.900><c> equals</c><00:05:06.620><c> B</c><00:05:06.800><c> and</c><00:05:07.070><c> we're</c>

00:05:07.270 --> 00:05:07.280 align:start position:0%
to a sum plus equals B and we're
 

00:05:07.280 --> 00:05:08.920 align:start position:0%
to a sum plus equals B and we're
incrementing<00:05:07.550><c> a</c><00:05:07.850><c> count</c><00:05:08.240><c> there</c><00:05:08.539><c> at</c><00:05:08.630><c> the</c><00:05:08.750><c> count</c>

00:05:08.920 --> 00:05:08.930 align:start position:0%
incrementing a count there at the count
 

00:05:08.930 --> 00:05:11.270 align:start position:0%
incrementing a count there at the count
variable

00:05:11.270 --> 00:05:11.280 align:start position:0%
 
 

00:05:11.280 --> 00:05:14.250 align:start position:0%
 
okay<00:05:12.280><c> and</c><00:05:12.580><c> then</c><00:05:12.820><c> we're</c><00:05:12.940><c> returning</c><00:05:13.330><c> count</c>

00:05:14.250 --> 00:05:14.260 align:start position:0%
okay and then we're returning count
 

00:05:14.260 --> 00:05:19.020 align:start position:0%
okay and then we're returning count
so<00:05:14.560><c> that's</c><00:05:14.740><c> actually</c><00:05:14.860><c> a</c><00:05:15.070><c> hint</c><00:05:15.550><c> that</c><00:05:17.790><c> yes</c><00:05:18.790><c> this</c>

00:05:19.020 --> 00:05:19.030 align:start position:0%
so that's actually a hint that yes this
 

00:05:19.030 --> 00:05:21.930 align:start position:0%
so that's actually a hint that yes this
is<00:05:19.180><c> a</c><00:05:19.240><c> hint</c><00:05:19.450><c> how</c><00:05:20.170><c> many</c><00:05:20.320><c> times</c><00:05:20.560><c> count</c><00:05:21.310><c> whatever</c>

00:05:21.930 --> 00:05:21.940 align:start position:0%
is a hint how many times count whatever
 

00:05:21.940 --> 00:05:24.900 align:start position:0%
is a hint how many times count whatever
count<00:05:22.360><c> is</c><00:05:22.680><c> that's</c><00:05:23.680><c> what</c><00:05:23.860><c> we're</c><00:05:24.040><c> turning</c><00:05:24.460><c> it</c><00:05:24.610><c> so</c>

00:05:24.900 --> 00:05:24.910 align:start position:0%
count is that's what we're turning it so
 

00:05:24.910 --> 00:05:27.660 align:start position:0%
count is that's what we're turning it so
that's<00:05:25.060><c> kind</c><00:05:25.270><c> of</c><00:05:25.300><c> gonna</c><00:05:25.690><c> be</c><00:05:25.780><c> based</c><00:05:26.670><c> that's</c>

00:05:27.660 --> 00:05:27.670 align:start position:0%
that's kind of gonna be based that's
 

00:05:27.670 --> 00:05:30.360 align:start position:0%
that's kind of gonna be based that's
gonna<00:05:27.820><c> help</c><00:05:27.910><c> us</c><00:05:28.060><c> figure</c><00:05:28.300><c> this</c><00:05:28.450><c> out</c><00:05:29.010><c> so</c><00:05:30.010><c> what</c>

00:05:30.360 --> 00:05:30.370 align:start position:0%
gonna help us figure this out so what
 

00:05:30.370 --> 00:05:32.220 align:start position:0%
gonna help us figure this out so what
you<00:05:30.490><c> might</c><00:05:30.580><c> want</c><00:05:30.640><c> to</c><00:05:30.820><c> do</c><00:05:30.970><c> is</c><00:05:31.300><c> well</c><00:05:31.480><c> what</c><00:05:31.900><c> I</c><00:05:31.930><c> did</c>

00:05:32.220 --> 00:05:32.230 align:start position:0%
you might want to do is well what I did
 

00:05:32.230 --> 00:05:35.880 align:start position:0%
you might want to do is well what I did
is<00:05:32.700><c> let's</c><00:05:33.700><c> jot</c><00:05:34.150><c> some</c><00:05:34.420><c> hard</c><00:05:35.080><c> code</c><00:05:35.290><c> some</c><00:05:35.590><c> number</c>

00:05:35.880 --> 00:05:35.890 align:start position:0%
is let's jot some hard code some number
 

00:05:35.890 --> 00:05:37.530 align:start position:0%
is let's jot some hard code some number
down<00:05:36.070><c> so</c><00:05:36.100><c> we</c><00:05:36.520><c> can</c><00:05:36.640><c> see</c><00:05:36.850><c> what</c><00:05:36.970><c> this</c><00:05:37.090><c> is</c><00:05:37.300><c> because</c>

00:05:37.530 --> 00:05:37.540 align:start position:0%
down so we can see what this is because
 

00:05:37.540 --> 00:05:40.980 align:start position:0%
down so we can see what this is because
it's<00:05:37.690><c> not</c><00:05:37.810><c> straightforward</c><00:05:39.660><c> what</c><00:05:40.660><c> the</c><00:05:40.780><c> answer</c>

00:05:40.980 --> 00:05:40.990 align:start position:0%
it's not straightforward what the answer
 

00:05:40.990 --> 00:05:44.370 align:start position:0%
it's not straightforward what the answer
is<00:05:41.020><c> right</c><00:05:41.350><c> so</c><00:05:41.890><c> let's</c><00:05:42.070><c> say</c><00:05:42.190><c> a</c><00:05:42.550><c> is</c><00:05:43.380><c> rigid</c>

00:05:44.370 --> 00:05:44.380 align:start position:0%
is right so let's say a is rigid
 

00:05:44.380 --> 00:05:48.060 align:start position:0%
is right so let's say a is rigid
division<00:05:44.530><c> so</c><00:05:45.400><c> let's</c><00:05:45.520><c> just</c><00:05:45.700><c> do</c><00:05:45.880><c> a</c><00:05:46.690><c> ten</c><00:05:47.590><c> B</c><00:05:47.950><c> is</c>

00:05:48.060 --> 00:05:48.070 align:start position:0%
division so let's just do a ten B is
 

00:05:48.070 --> 00:05:53.310 align:start position:0%
division so let's just do a ten B is
five<00:05:49.530><c> okay</c><00:05:50.530><c> so</c><00:05:51.490><c> while</c><00:05:52.000><c> sum</c><00:05:52.510><c> is</c><00:05:53.020><c> less</c><00:05:53.080><c> than</c>

00:05:53.310 --> 00:05:53.320 align:start position:0%
five okay so while sum is less than
 

00:05:53.320 --> 00:05:57.120 align:start position:0%
five okay so while sum is less than
equal<00:05:53.650><c> to</c><00:05:53.680><c> a</c><00:05:54.040><c> so</c><00:05:54.610><c> sum</c><00:05:54.850><c> is</c><00:05:54.970><c> B</c><00:05:55.770><c> all</c><00:05:56.770><c> right</c><00:05:56.860><c> so</c>

00:05:57.120 --> 00:05:57.130 align:start position:0%
equal to a so sum is B all right so
 

00:05:57.130 --> 00:06:00.150 align:start position:0%
equal to a so sum is B all right so
while<00:05:57.520><c> B</c><00:05:57.880><c> so</c><00:05:58.720><c> five</c><00:05:59.050><c> is</c><00:05:59.410><c> less</c><00:05:59.500><c> than</c><00:05:59.830><c> equal</c><00:06:00.040><c> to</c><00:06:00.070><c> a</c>

00:06:00.150 --> 00:06:00.160 align:start position:0%
while B so five is less than equal to a
 

00:06:00.160 --> 00:06:06.180 align:start position:0%
while B so five is less than equal to a
which<00:06:01.120><c> is</c><00:06:01.270><c> ten</c><00:06:01.420><c> it</c><00:06:01.690><c> is</c><00:06:02.430><c> sum</c><00:06:03.690><c> plus</c><00:06:04.690><c> equals</c><00:06:05.200><c> B</c><00:06:05.680><c> so</c>

00:06:06.180 --> 00:06:06.190 align:start position:0%
which is ten it is sum plus equals B so
 

00:06:06.190 --> 00:06:13.590 align:start position:0%
which is ten it is sum plus equals B so
now<00:06:06.340><c> if</c><00:06:06.460><c> some</c><00:06:06.730><c> equals</c><00:06:07.180><c> ten</c><00:06:09.150><c> let's</c><00:06:10.150><c> do</c><00:06:10.330><c> it</c><00:06:12.600><c> erase</c>

00:06:13.590 --> 00:06:13.600 align:start position:0%
now if some equals ten let's do it erase
 

00:06:13.600 --> 00:06:16.980 align:start position:0%
now if some equals ten let's do it erase
iterations<00:06:14.500><c> for</c><00:06:15.250><c> these</c><00:06:15.550><c> inputs</c><00:06:15.910><c> so</c><00:06:16.270><c> how</c><00:06:16.870><c> many</c>

00:06:16.980 --> 00:06:16.990 align:start position:0%
iterations for these inputs so how many
 

00:06:16.990 --> 00:06:20.220 align:start position:0%
iterations for these inputs so how many
times<00:06:17.170><c> we</c><00:06:17.230><c> went</c><00:06:17.380><c> to</c><00:06:17.470><c> the</c><00:06:17.560><c> while</c><00:06:17.680><c> loop</c><00:06:19.230><c> says</c>

00:06:20.220 --> 00:06:20.230 align:start position:0%
times we went to the while loop says
 

00:06:20.230 --> 00:06:23.040 align:start position:0%
times we went to the while loop says
we're<00:06:20.500><c> going</c><00:06:21.450><c> so</c><00:06:22.450><c> we're</c><00:06:22.600><c> all</c><00:06:22.870><c> you</c><00:06:22.930><c> went</c>

00:06:23.040 --> 00:06:23.050 align:start position:0%
we're going so we're all you went
 

00:06:23.050 --> 00:06:24.450 align:start position:0%
we're going so we're all you went
through<00:06:23.140><c> it</c><00:06:23.260><c> once</c><00:06:23.440><c> so</c><00:06:23.650><c> while</c><00:06:23.830><c> some</c><00:06:24.100><c> let's</c><00:06:24.400><c> do</c>

00:06:24.450 --> 00:06:24.460 align:start position:0%
through it once so while some let's do
 

00:06:24.460 --> 00:06:26.910 align:start position:0%
through it once so while some let's do
it<00:06:24.550><c> do</c><00:06:24.760><c> it</c><00:06:24.790><c> again</c><00:06:25.000><c> some</c><00:06:25.300><c> lets</c><00:06:25.570><c> me</c><00:06:25.660><c> go</c><00:06:25.780><c> to</c><00:06:25.960><c> a</c><00:06:26.140><c> so</c>

00:06:26.910 --> 00:06:26.920 align:start position:0%
it do it again some lets me go to a so
 

00:06:26.920 --> 00:06:29.070 align:start position:0%
it do it again some lets me go to a so
some<00:06:27.070><c> is</c><00:06:27.160><c> B</c><00:06:27.310><c> so</c><00:06:27.610><c> five</c><00:06:28.210><c> is</c><00:06:28.240><c> less</c><00:06:28.540><c> than</c><00:06:28.660><c> equal</c><00:06:28.930><c> to</c>

00:06:29.070 --> 00:06:29.080 align:start position:0%
some is B so five is less than equal to
 

00:06:29.080 --> 00:06:32.940 align:start position:0%
some is B so five is less than equal to
ten<00:06:29.290><c> just</c><00:06:29.590><c> true</c><00:06:30.480><c> some</c><00:06:31.480><c> plus</c><00:06:31.750><c> equals</c><00:06:32.050><c> B</c><00:06:32.230><c> so</c><00:06:32.830><c> the</c>

00:06:32.940 --> 00:06:32.950 align:start position:0%
ten just true some plus equals B so the
 

00:06:32.950 --> 00:06:36.510 align:start position:0%
ten just true some plus equals B so the
sum<00:06:33.190><c> is</c><00:06:33.430><c> five</c><00:06:34.180><c> we're</c><00:06:34.720><c> adding</c><00:06:35.050><c> B</c><00:06:35.230><c> so</c><00:06:36.070><c> if</c><00:06:36.160><c> I</c><00:06:36.250><c> was</c><00:06:36.370><c> 5</c>

00:06:36.510 --> 00:06:36.520 align:start position:0%
sum is five we're adding B so if I was 5
 

00:06:36.520 --> 00:06:39.260 align:start position:0%
sum is five we're adding B so if I was 5
is<00:06:36.640><c> 10</c><00:06:36.820><c> and</c><00:06:37.060><c> we're</c><00:06:37.780><c> incrementing</c><00:06:38.020><c> count</c><00:06:38.470><c> once</c>

00:06:39.260 --> 00:06:39.270 align:start position:0%
is 10 and we're incrementing count once
 

00:06:39.270 --> 00:06:41.130 align:start position:0%
is 10 and we're incrementing count once
so<00:06:40.270><c> we're</c><00:06:40.360><c> going</c><00:06:40.480><c> through</c><00:06:40.600><c> a</c><00:06:40.660><c> while</c><00:06:40.780><c> up</c><00:06:40.900><c> again</c>

00:06:41.130 --> 00:06:41.140 align:start position:0%
so we're going through a while up again
 

00:06:41.140 --> 00:06:45.240 align:start position:0%
so we're going through a while up again
10<00:06:41.620><c> is</c><00:06:41.860><c> less</c><00:06:42.070><c> than</c><00:06:42.190><c> equal</c><00:06:42.490><c> to</c><00:06:42.520><c> 10</c><00:06:43.180><c> so</c><00:06:43.800><c> some</c><00:06:44.800><c> plus</c>

00:06:45.240 --> 00:06:45.250 align:start position:0%
10 is less than equal to 10 so some plus
 

00:06:45.250 --> 00:06:49.020 align:start position:0%
10 is less than equal to 10 so some plus
or<00:06:45.400><c> equals</c><00:06:45.670><c> B</c><00:06:45.940><c> so</c><00:06:46.840><c> now</c><00:06:47.380><c> we</c><00:06:47.800><c> had</c><00:06:47.920><c> 10</c><00:06:48.190><c> plus</c><00:06:48.610><c> 5</c>

00:06:49.020 --> 00:06:49.030 align:start position:0%
or equals B so now we had 10 plus 5
 

00:06:49.030 --> 00:06:51.240 align:start position:0%
or equals B so now we had 10 plus 5
which<00:06:49.330><c> is</c><00:06:49.360><c> 15</c><00:06:49.630><c> and</c><00:06:50.110><c> we're</c><00:06:50.320><c> incrementing</c><00:06:50.800><c> count</c>

00:06:51.240 --> 00:06:51.250 align:start position:0%
which is 15 and we're incrementing count
 

00:06:51.250 --> 00:06:55.350 align:start position:0%
which is 15 and we're incrementing count
well<00:06:51.910><c> countess</c><00:06:52.480><c> to</c><00:06:53.220><c> go</c><00:06:54.220><c> through</c><00:06:54.430><c> it</c><00:06:54.520><c> again</c><00:06:54.580><c> 15</c>

00:06:55.350 --> 00:06:55.360 align:start position:0%
well countess to go through it again 15
 

00:06:55.360 --> 00:06:59.070 align:start position:0%
well countess to go through it again 15
is<00:06:55.720><c> not</c><00:06:56.050><c> less</c><00:06:56.200><c> equal</c><00:06:56.500><c> to</c><00:06:56.650><c> 10</c><00:06:57.180><c> all</c><00:06:58.180><c> right</c><00:06:58.300><c> so</c><00:06:58.480><c> we</c>

00:06:59.070 --> 00:06:59.080 align:start position:0%
is not less equal to 10 all right so we
 

00:06:59.080 --> 00:07:03.030 align:start position:0%
is not less equal to 10 all right so we
went<00:06:59.230><c> through</c><00:06:59.350><c> this</c><00:06:59.590><c> twice</c><00:07:00.420><c> okay</c><00:07:01.860><c> now</c><00:07:02.860><c> let's</c>

00:07:03.030 --> 00:07:03.040 align:start position:0%
went through this twice okay now let's
 

00:07:03.040 --> 00:07:09.810 align:start position:0%
went through this twice okay now let's
do<00:07:03.190><c> another</c><00:07:03.580><c> one</c><00:07:04.740><c> let's</c><00:07:05.740><c> do</c><00:07:07.290><c> let's</c><00:07:08.290><c> do</c><00:07:08.820><c> let's</c>

00:07:09.810 --> 00:07:09.820 align:start position:0%
do another one let's do let's do let's
 

00:07:09.820 --> 00:07:15.150 align:start position:0%
do another one let's do let's do let's
just<00:07:09.880><c> do</c><00:07:10.060><c> it</c><00:07:10.090><c> like</c><00:07:10.270><c> 20</c><00:07:10.720><c> and</c><00:07:10.810><c> 5</c><00:07:11.020><c> okay</c><00:07:13.350><c> so</c><00:07:14.350><c> we</c><00:07:14.470><c> we</c>

00:07:15.150 --> 00:07:15.160 align:start position:0%
just do it like 20 and 5 okay so we we
 

00:07:15.160 --> 00:07:18.360 align:start position:0%
just do it like 20 and 5 okay so we we
get<00:07:15.310><c> to</c><00:07:15.430><c> the</c><00:07:15.520><c> point</c><00:07:15.760><c> to</c><00:07:15.940><c> where</c><00:07:16.590><c> sum</c><00:07:17.590><c> is</c><00:07:17.740><c> 15</c><00:07:17.920><c> and</c>

00:07:18.360 --> 00:07:18.370 align:start position:0%
get to the point to where sum is 15 and
 

00:07:18.370 --> 00:07:23.190 align:start position:0%
get to the point to where sum is 15 and
a<00:07:19.090><c> is</c><00:07:19.420><c> 10</c><00:07:19.900><c> or</c><00:07:20.140><c> yet</c><00:07:21.070><c> if</c><00:07:21.400><c> sum</c><00:07:21.670><c> is</c><00:07:21.790><c> 15</c><00:07:21.910><c> a</c><00:07:22.420><c> is</c><00:07:22.660><c> 20</c>

00:07:23.190 --> 00:07:23.200 align:start position:0%
a is 10 or yet if sum is 15 a is 20
 

00:07:23.200 --> 00:07:25.560 align:start position:0%
a is 10 or yet if sum is 15 a is 20
we<00:07:23.530><c> just</c><00:07:23.710><c> did</c><00:07:23.830><c> that</c><00:07:23.950><c> here</c><00:07:24.340><c> so</c><00:07:25.270><c> we've</c><00:07:25.390><c> all</c><00:07:25.480><c> right</c>

00:07:25.560 --> 00:07:25.570 align:start position:0%
we just did that here so we've all right
 

00:07:25.570 --> 00:07:30.330 align:start position:0%
we just did that here so we've all right
went<00:07:25.720><c> through</c><00:07:25.810><c> this</c><00:07:25.900><c> twice</c><00:07:26.230><c> and</c><00:07:28.260><c> so</c><00:07:29.260><c> some</c><00:07:29.800><c> 15</c>

00:07:30.330 --> 00:07:30.340 align:start position:0%
went through this twice and so some 15
 

00:07:30.340 --> 00:07:32.370 align:start position:0%
went through this twice and so some 15
is<00:07:30.550><c> less</c><00:07:30.580><c> than</c><00:07:30.790><c> equal</c><00:07:30.970><c> to</c><00:07:31.060><c> 20</c><00:07:31.390><c> so</c><00:07:32.140><c> now</c><00:07:32.290><c> we're</c>

00:07:32.370 --> 00:07:32.380 align:start position:0%
is less than equal to 20 so now we're
 

00:07:32.380 --> 00:07:34.200 align:start position:0%
is less than equal to 20 so now we're
gonna<00:07:32.470><c> add</c><00:07:32.590><c> 5</c><00:07:32.860><c> again</c><00:07:33.190><c> and</c><00:07:33.430><c> hit</c><00:07:33.760><c> current</c><00:07:34.030><c> count</c>

00:07:34.200 --> 00:07:34.210 align:start position:0%
gonna add 5 again and hit current count
 

00:07:34.210 --> 00:07:36.660 align:start position:0%
gonna add 5 again and hit current count
which<00:07:34.420><c> is</c><00:07:34.570><c> 3</c><00:07:34.840><c> this</c><00:07:34.990><c> time</c><00:07:35.310><c> we're</c><00:07:36.310><c> gonna</c><00:07:36.400><c> through</c>

00:07:36.660 --> 00:07:36.670 align:start position:0%
which is 3 this time we're gonna through
 

00:07:36.670 --> 00:07:41.520 align:start position:0%
which is 3 this time we're gonna through
it<00:07:36.730><c> again</c><00:07:36.820><c> 20</c><00:07:37.570><c> is</c><00:07:37.810><c> less</c><00:07:38.380><c> than</c><00:07:38.500><c> equal</c><00:07:38.830><c> to</c><00:07:39.960><c> 20</c><00:07:40.960><c> so</c>

00:07:41.520 --> 00:07:41.530 align:start position:0%
it again 20 is less than equal to 20 so
 

00:07:41.530 --> 00:07:45.510 align:start position:0%
it again 20 is less than equal to 20 so
we<00:07:41.560><c> added</c><00:07:42.130><c> 5</c><00:07:42.280><c> again</c><00:07:42.550><c> so</c><00:07:42.760><c> now</c><00:07:43.240><c> sum</c><00:07:44.200><c> is</c><00:07:44.410><c> 25</c><00:07:44.860><c> and</c><00:07:45.430><c> we</c>

00:07:45.510 --> 00:07:45.520 align:start position:0%
we added 5 again so now sum is 25 and we
 

00:07:45.520 --> 00:07:47.700 align:start position:0%
we added 5 again so now sum is 25 and we
increment<00:07:45.820><c> count</c><00:07:46.270><c> and</c><00:07:46.600><c> count</c><00:07:47.170><c> at</c><00:07:47.320><c> this</c><00:07:47.470><c> point</c>

00:07:47.700 --> 00:07:47.710 align:start position:0%
increment count and count at this point
 

00:07:47.710 --> 00:07:52.890 align:start position:0%
increment count and count at this point
is<00:07:47.890><c> 4</c><00:07:49.050><c> 25</c><00:07:50.050><c> is</c><00:07:50.140><c> not</c><00:07:50.230><c> less</c><00:07:50.380><c> than</c><00:07:50.410><c> equal</c><00:07:50.680><c> to</c><00:07:50.880><c> 20</c><00:07:51.900><c> so</c>

00:07:52.890 --> 00:07:52.900 align:start position:0%
is 4 25 is not less than equal to 20 so
 

00:07:52.900 --> 00:07:54.210 align:start position:0%
is 4 25 is not less than equal to 20 so
we<00:07:52.990><c> would</c><00:07:53.110><c> just</c><00:07:53.140><c> return</c><00:07:53.470><c> count</c><00:07:53.830><c> which</c><00:07:54.070><c> at</c><00:07:54.190><c> that</c>

00:07:54.210 --> 00:07:54.220 align:start position:0%
we would just return count which at that
 

00:07:54.220 --> 00:07:57.840 align:start position:0%
we would just return count which at that
point<00:07:54.790><c> is</c><00:07:54.970><c> 4</c><00:07:55.680><c> so</c><00:07:56.680><c> there</c><00:07:56.890><c> so</c><00:07:57.400><c> we're</c><00:07:57.550><c> starting</c><00:07:57.790><c> to</c>

00:07:57.840 --> 00:07:57.850 align:start position:0%
point is 4 so there so we're starting to
 

00:07:57.850 --> 00:07:58.710 align:start position:0%
point is 4 so there so we're starting to
see<00:07:57.970><c> a</c><00:07:58.000><c> pattern</c><00:07:58.090><c> here</c>

00:07:58.710 --> 00:07:58.720 align:start position:0%
see a pattern here
 

00:07:58.720 --> 00:08:00.900 align:start position:0%
see a pattern here
all<00:07:58.960><c> right</c><00:07:59.110><c> you</c><00:07:59.740><c> can</c><00:07:59.890><c> do</c><00:07:59.980><c> this</c><00:08:00.010><c> a</c><00:08:00.430><c> couple</c><00:08:00.490><c> more</c>

00:08:00.900 --> 00:08:00.910 align:start position:0%
all right you can do this a couple more
 

00:08:00.910 --> 00:08:02.550 align:start position:0%
all right you can do this a couple more
times<00:08:00.970><c> and</c><00:08:01.480><c> it's</c><00:08:02.080><c> going</c><00:08:02.230><c> to</c><00:08:02.290><c> be</c><00:08:02.350><c> the</c><00:08:02.410><c> same</c>

00:08:02.550 --> 00:08:02.560 align:start position:0%
times and it's going to be the same
 

00:08:02.560 --> 00:08:06.000 align:start position:0%
times and it's going to be the same
thing<00:08:02.800><c> so</c><00:08:03.900><c> so</c><00:08:04.900><c> what</c><00:08:05.260><c> does</c><00:08:05.350><c> this</c><00:08:05.410><c> mean</c><00:08:05.620><c> whenever</c>

00:08:06.000 --> 00:08:06.010 align:start position:0%
thing so so what does this mean whenever
 

00:08:06.010 --> 00:08:10.260 align:start position:0%
thing so so what does this mean whenever
a<00:08:06.280><c> is</c><00:08:06.310><c> 10</c><00:08:06.610><c> B</c><00:08:07.150><c> is</c><00:08:07.270><c> 5</c><00:08:08.550><c> mob</c><00:08:09.550><c> iterations</c><00:08:10.060><c> weave</c>

00:08:10.260 --> 00:08:10.270 align:start position:0%
a is 10 B is 5 mob iterations weave
 

00:08:10.270 --> 00:08:12.420 align:start position:0%
a is 10 B is 5 mob iterations weave
overs<00:08:10.570><c> 2</c><00:08:10.870><c> or</c><00:08:11.110><c> you</c><00:08:11.320><c> can</c><00:08:11.440><c> just</c><00:08:11.620><c> say</c><00:08:11.770><c> what</c><00:08:12.160><c> what</c><00:08:12.340><c> is</c>

00:08:12.420 --> 00:08:12.430 align:start position:0%
overs 2 or you can just say what what is
 

00:08:12.430 --> 00:08:16.560 align:start position:0%
overs 2 or you can just say what what is
count<00:08:12.730><c> right</c><00:08:14.010><c> whenever</c><00:08:15.010><c> a</c><00:08:15.370><c> is</c><00:08:15.400><c> 20</c><00:08:15.880><c> and</c><00:08:16.120><c> B</c><00:08:16.240><c> is</c><00:08:16.360><c> 5</c>

00:08:16.560 --> 00:08:16.570 align:start position:0%
count right whenever a is 20 and B is 5
 

00:08:16.570 --> 00:08:19.260 align:start position:0%
count right whenever a is 20 and B is 5
iteration<00:08:17.290><c> is</c><00:08:17.440><c> 4</c><00:08:17.850><c> what</c><00:08:18.850><c> is</c><00:08:18.880><c> that</c><00:08:19.000><c> the</c><00:08:19.090><c> same</c>

00:08:19.260 --> 00:08:19.270 align:start position:0%
iteration is 4 what is that the same
 

00:08:19.270 --> 00:08:22.130 align:start position:0%
iteration is 4 what is that the same
mask<00:08:19.480><c> that's</c><00:08:19.780><c> the</c><00:08:19.870><c> same</c><00:08:20.140><c> as</c><00:08:20.440><c> Big</c><00:08:21.400><c> O</c><00:08:21.520><c> of</c><00:08:21.550><c> a</c>

00:08:22.130 --> 00:08:22.140 align:start position:0%
mask that's the same as Big O of a
 

00:08:22.140 --> 00:08:26.670 align:start position:0%
mask that's the same as Big O of a
divided<00:08:23.140><c> by</c><00:08:23.430><c> B</c><00:08:25.140><c> so</c><00:08:26.140><c> that's</c><00:08:26.290><c> what</c><00:08:26.410><c> the</c><00:08:26.470><c> answer</c>

00:08:26.670 --> 00:08:26.680 align:start position:0%
divided by B so that's what the answer
 

00:08:26.680 --> 00:08:26.820 align:start position:0%
divided by B so that's what the answer
is

00:08:26.820 --> 00:08:26.830 align:start position:0%
is
 

00:08:26.830 --> 00:08:28.710 align:start position:0%
is
okay<00:08:27.190><c> time</c><00:08:27.610><c> Calissa</c><00:08:27.820><c> T</c><00:08:28.000><c> is</c><00:08:28.120><c> Big</c><00:08:28.330><c> O</c><00:08:28.450><c> of</c><00:08:28.480><c> a</c>

00:08:28.710 --> 00:08:28.720 align:start position:0%
okay time Calissa T is Big O of a
 

00:08:28.720 --> 00:08:31.860 align:start position:0%
okay time Calissa T is Big O of a
divided<00:08:29.200><c> by</c><00:08:29.320><c> B</c><00:08:29.470><c> all</c><00:08:30.040><c> right</c><00:08:30.600><c> alright</c><00:08:31.600><c> question</c>

00:08:31.860 --> 00:08:31.870 align:start position:0%
divided by B all right alright question
 

00:08:31.870 --> 00:08:33.719 align:start position:0%
divided by B all right alright question
umber<00:08:31.990><c> 5</c><00:08:32.169><c> if</c><00:08:32.740><c> a</c><00:08:32.830><c> binary</c><00:08:33.040><c> search</c><00:08:33.250><c> tree</c><00:08:33.310><c> is</c><00:08:33.580><c> not</c>

00:08:33.719 --> 00:08:33.729 align:start position:0%
umber 5 if a binary search tree is not
 

00:08:33.729 --> 00:08:35.520 align:start position:0%
umber 5 if a binary search tree is not
balanced<00:08:34.090><c> how</c><00:08:34.450><c> long</c><00:08:34.599><c> they</c><00:08:34.780><c> take</c><00:08:35.050><c> for</c><00:08:35.320><c> the</c><00:08:35.380><c> word</c>

00:08:35.520 --> 00:08:35.530 align:start position:0%
balanced how long they take for the word
 

00:08:35.530 --> 00:08:36.959 align:start position:0%
balanced how long they take for the word
in<00:08:35.680><c> the</c><00:08:35.770><c> worst</c><00:08:35.919><c> case</c><00:08:36.099><c> if</c><00:08:36.340><c> you</c><00:08:36.460><c> find</c><00:08:36.700><c> an</c><00:08:36.790><c> element</c>

00:08:36.959 --> 00:08:36.969 align:start position:0%
in the worst case if you find an element
 

00:08:36.969 --> 00:08:38.880 align:start position:0%
in the worst case if you find an element
in<00:08:37.180><c> it</c><00:08:37.330><c> well</c><00:08:37.930><c> you</c><00:08:38.200><c> can't</c><00:08:38.380><c> understand</c><00:08:38.740><c> what</c>

00:08:38.880 --> 00:08:38.890 align:start position:0%
in it well you can't understand what
 

00:08:38.890 --> 00:08:41.280 align:start position:0%
in it well you can't understand what
binary<00:08:39.070><c> search</c><00:08:39.280><c> tree</c><00:08:39.520><c> is</c><00:08:39.729><c> and</c><00:08:40.240><c> also</c><00:08:40.870><c> what</c><00:08:41.200><c> does</c>

00:08:41.280 --> 00:08:41.290 align:start position:0%
binary search tree is and also what does
 

00:08:41.290 --> 00:08:44.820 align:start position:0%
binary search tree is and also what does
balance<00:08:41.680><c> mean</c><00:08:42.130><c> so</c><00:08:42.880><c> a</c><00:08:43.440><c> balanced</c><00:08:44.440><c> binary</c><00:08:44.650><c> search</c>

00:08:44.820 --> 00:08:44.830 align:start position:0%
balance mean so a balanced binary search
 

00:08:44.830 --> 00:08:47.460 align:start position:0%
balance mean so a balanced binary search
tree<00:08:45.250><c> is</c><00:08:45.400><c> or</c><00:08:45.670><c> in</c><00:08:45.730><c> a</c><00:08:45.790><c> depth</c><00:08:46.120><c> of</c><00:08:46.420><c> a</c><00:08:47.020><c> left</c><00:08:47.290><c> subtree</c>

00:08:47.460 --> 00:08:47.470 align:start position:0%
tree is or in a depth of a left subtree
 

00:08:47.470 --> 00:08:50.610 align:start position:0%
tree is or in a depth of a left subtree
and<00:08:47.680><c> a</c><00:08:47.800><c> right</c><00:08:47.950><c> subtree</c><00:08:48.400><c> does</c><00:08:48.880><c> not</c><00:08:49.090><c> is</c><00:08:49.570><c> not</c><00:08:49.870><c> more</c>

00:08:50.610 --> 00:08:50.620 align:start position:0%
and a right subtree does not is not more
 

00:08:50.620 --> 00:08:54.330 align:start position:0%
and a right subtree does not is not more
than<00:08:50.650><c> one</c><00:08:51.330><c> quick</c><00:08:52.330><c> example</c><00:08:52.680><c> quick</c><00:08:53.680><c> example</c><00:08:54.250><c> if</c>

00:08:54.330 --> 00:08:54.340 align:start position:0%
than one quick example quick example if
 

00:08:54.340 --> 00:09:00.260 align:start position:0%
than one quick example quick example if
I<00:08:54.430><c> can</c><00:08:54.610><c> draw</c><00:08:54.820><c> nodes</c><00:08:55.590><c> ish</c>

00:09:00.260 --> 00:09:00.270 align:start position:0%
 
 

00:09:00.270 --> 00:09:01.590 align:start position:0%
 
okay<00:09:01.270><c> so</c>

00:09:01.590 --> 00:09:01.600 align:start position:0%
okay so
 

00:09:01.600 --> 00:09:02.999 align:start position:0%
okay so
left<00:09:01.750><c> sub-tree</c><00:09:01.930><c> over</c><00:09:02.170><c> here</c><00:09:02.529><c> is</c><00:09:02.649><c> a</c><00:09:02.680><c> depth</c><00:09:02.920><c> of</c>

00:09:02.999 --> 00:09:03.009 align:start position:0%
left sub-tree over here is a depth of
 

00:09:03.009 --> 00:09:06.360 align:start position:0%
left sub-tree over here is a depth of
one<00:09:03.220><c> over</c><00:09:03.490><c> here</c><00:09:04.120><c> has</c><00:09:04.300><c> a</c><00:09:04.360><c> depth</c><00:09:04.630><c> of</c><00:09:04.779><c> 3</c><00:09:05.110><c> 3</c><00:09:06.100><c> minus</c><00:09:06.339><c> 1</c>

00:09:06.360 --> 00:09:06.370 align:start position:0%
one over here has a depth of 3 3 minus 1
 

00:09:06.370 --> 00:09:09.120 align:start position:0%
one over here has a depth of 3 3 minus 1
is<00:09:06.579><c> 2</c><00:09:06.610><c> 2</c><00:09:07.180><c> is</c><00:09:07.329><c> greater</c><00:09:07.810><c> than</c><00:09:07.930><c> 1</c><00:09:08.079><c> so</c><00:09:08.620><c> this</c><00:09:08.740><c> is</c><00:09:08.800><c> not</c>

00:09:09.120 --> 00:09:09.130 align:start position:0%
is 2 2 is greater than 1 so this is not
 

00:09:09.130 --> 00:09:11.189 align:start position:0%
is 2 2 is greater than 1 so this is not
a<00:09:09.160><c> balanced</c><00:09:09.699><c> binary</c><00:09:09.910><c> search</c><00:09:10.209><c> tree</c><00:09:10.600><c> alright</c>

00:09:11.189 --> 00:09:11.199 align:start position:0%
a balanced binary search tree alright
 

00:09:11.199 --> 00:09:14.280 align:start position:0%
a balanced binary search tree alright
and<00:09:11.759><c> if</c><00:09:12.759><c> you</c><00:09:12.970><c> have</c><00:09:13.149><c> a</c><00:09:13.449><c> balanced</c><00:09:13.959><c> binary</c><00:09:14.199><c> search</c>

00:09:14.280 --> 00:09:14.290 align:start position:0%
and if you have a balanced binary search
 

00:09:14.290 --> 00:09:16.379 align:start position:0%
and if you have a balanced binary search
tree<00:09:14.649><c> you</c><00:09:15.279><c> just</c><00:09:15.519><c> that</c><00:09:15.730><c> would</c><00:09:15.880><c> this</c><00:09:16.029><c> this</c><00:09:16.089><c> would</c>

00:09:16.379 --> 00:09:16.389 align:start position:0%
tree you just that would this this would
 

00:09:16.389 --> 00:09:18.840 align:start position:0%
tree you just that would this this would
never<00:09:16.449><c> happen</c><00:09:16.660><c> it</c><00:09:16.930><c> would</c><00:09:17.050><c> you</c><00:09:17.500><c> know</c><00:09:17.620><c> we</c><00:09:18.519><c> can</c><00:09:18.699><c> do</c>

00:09:18.840 --> 00:09:18.850 align:start position:0%
never happen it would you know we can do
 

00:09:18.850 --> 00:09:22.069 align:start position:0%
never happen it would you know we can do
like<00:09:19.180><c> this</c><00:09:20.190><c> cross</c><00:09:21.190><c> that</c><00:09:21.459><c> out</c>

00:09:22.069 --> 00:09:22.079 align:start position:0%
like this cross that out
 

00:09:22.079 --> 00:09:24.689 align:start position:0%
like this cross that out
that's<00:09:23.079><c> a</c><00:09:23.230><c> button</c><00:09:23.500><c> that's</c><00:09:23.740><c> a</c><00:09:23.980><c> that</c><00:09:24.550><c> is</c><00:09:24.670><c> a</c>

00:09:24.689 --> 00:09:24.699 align:start position:0%
that's a button that's a that is a
 

00:09:24.699 --> 00:09:27.930 align:start position:0%
that's a button that's a that is a
balanced<00:09:25.149><c> binary</c><00:09:25.509><c> search</c><00:09:25.630><c> tree</c><00:09:25.959><c> okay</c><00:09:26.940><c> and</c>

00:09:27.930 --> 00:09:27.940 align:start position:0%
balanced binary search tree okay and
 

00:09:27.940 --> 00:09:30.689 align:start position:0%
balanced binary search tree okay and
that's<00:09:28.089><c> because</c><00:09:28.329><c> the</c><00:09:28.509><c> depth</c><00:09:28.750><c> here</c><00:09:29.139><c> is</c><00:09:29.319><c> now</c><00:09:29.829><c> 2</c>

00:09:30.689 --> 00:09:30.699 align:start position:0%
that's because the depth here is now 2
 

00:09:30.699 --> 00:09:37.139 align:start position:0%
that's because the depth here is now 2
and<00:09:31.180><c> not</c><00:09:31.660><c> 3</c><00:09:32.759><c> ok</c><00:09:33.759><c> so</c><00:09:35.069><c> does</c><00:09:36.069><c> this</c><00:09:36.250><c> means</c><00:09:36.519><c> that</c><00:09:36.880><c> any</c>

00:09:37.139 --> 00:09:37.149 align:start position:0%
and not 3 ok so does this means that any
 

00:09:37.149 --> 00:09:39.300 align:start position:0%
and not 3 ok so does this means that any
pretty<00:09:37.720><c> much</c><00:09:37.839><c> anything</c><00:09:38.079><c> goes</c><00:09:38.230><c> in</c><00:09:38.800><c> a</c><00:09:39.100><c> binary</c>

00:09:39.300 --> 00:09:39.310 align:start position:0%
pretty much anything goes in a binary
 

00:09:39.310 --> 00:09:40.680 align:start position:0%
pretty much anything goes in a binary
search<00:09:39.550><c> tree</c><00:09:39.610><c> that's</c><00:09:39.880><c> not</c><00:09:40.060><c> balanced</c><00:09:40.389><c> you</c><00:09:40.540><c> keep</c>

00:09:40.680 --> 00:09:40.690 align:start position:0%
search tree that's not balanced you keep
 

00:09:40.690 --> 00:09:42.749 align:start position:0%
search tree that's not balanced you keep
inserting<00:09:41.019><c> and</c><00:09:41.440><c> you</c><00:09:42.220><c> know</c><00:09:42.310><c> we</c><00:09:42.490><c> don't</c><00:09:42.579><c> care</c>

00:09:42.749 --> 00:09:42.759 align:start position:0%
inserting and you know we don't care
 

00:09:42.759 --> 00:09:45.329 align:start position:0%
inserting and you know we don't care
about<00:09:42.819><c> the</c><00:09:42.940><c> depth</c><00:09:43.149><c> really</c><00:09:43.649><c> so</c><00:09:44.649><c> a</c><00:09:44.680><c> worst</c><00:09:45.100><c> case</c>

00:09:45.329 --> 00:09:45.339 align:start position:0%
about the depth really so a worst case
 

00:09:45.339 --> 00:09:48.180 align:start position:0%
about the depth really so a worst case
for<00:09:45.610><c> that</c><00:09:45.940><c> is</c><00:09:46.440><c> we</c><00:09:47.440><c> can</c><00:09:47.620><c> keep</c><00:09:47.769><c> inserting</c>

00:09:48.180 --> 00:09:48.190 align:start position:0%
for that is we can keep inserting
 

00:09:48.190 --> 00:09:51.120 align:start position:0%
for that is we can keep inserting
elements<00:09:48.430><c> they're</c><00:09:49.269><c> always</c><00:09:49.660><c> greater</c><00:09:50.050><c> than</c><00:09:50.230><c> any</c>

00:09:51.120 --> 00:09:51.130 align:start position:0%
elements they're always greater than any
 

00:09:51.130 --> 00:09:53.280 align:start position:0%
elements they're always greater than any
other<00:09:51.399><c> element</c><00:09:52.000><c> before</c><00:09:52.180><c> it</c><00:09:52.540><c> so</c><00:09:52.990><c> we</c><00:09:53.050><c> can</c><00:09:53.170><c> kind</c>

00:09:53.280 --> 00:09:53.290 align:start position:0%
other element before it so we can kind
 

00:09:53.290 --> 00:09:56.040 align:start position:0%
other element before it so we can kind
of<00:09:53.319><c> keep</c><00:09:53.440><c> going</c><00:09:53.500><c> down</c><00:09:53.829><c> right</c><00:09:54.130><c> so</c><00:09:54.910><c> now</c><00:09:55.720><c> when</c><00:09:55.899><c> we</c>

00:09:56.040 --> 00:09:56.050 align:start position:0%
of keep going down right so now when we
 

00:09:56.050 --> 00:09:58.800 align:start position:0%
of keep going down right so now when we
search<00:09:56.410><c> what</c><00:09:57.279><c> if</c><00:09:57.399><c> we</c><00:09:57.550><c> have</c><00:09:57.670><c> to</c><00:09:57.819><c> search</c><00:09:58.000><c> for</c><00:09:58.300><c> the</c>

00:09:58.800 --> 00:09:58.810 align:start position:0%
search what if we have to search for the
 

00:09:58.810 --> 00:10:01.620 align:start position:0%
search what if we have to search for the
last<00:09:59.230><c> element</c><00:09:59.829><c> here</c><00:10:00.250><c> this</c><00:10:00.850><c> one</c><00:10:01.089><c> right</c><00:10:01.300><c> here</c>

00:10:01.620 --> 00:10:01.630 align:start position:0%
last element here this one right here
 

00:10:01.630 --> 00:10:03.870 align:start position:0%
last element here this one right here
this<00:10:02.620><c> happens</c><00:10:02.889><c> to</c><00:10:03.160><c> be</c><00:10:03.190><c> the</c><00:10:03.430><c> one</c><00:10:03.579><c> that</c><00:10:03.759><c> we're</c>

00:10:03.870 --> 00:10:03.880 align:start position:0%
this happens to be the one that we're
 

00:10:03.880 --> 00:10:06.840 align:start position:0%
this happens to be the one that we're
looking<00:10:03.910><c> for</c><00:10:04.209><c> well</c><00:10:04.990><c> we</c><00:10:05.410><c> went</c><00:10:05.560><c> down</c><00:10:05.850><c> there's</c>

00:10:06.840 --> 00:10:06.850 align:start position:0%
looking for well we went down there's
 

00:10:06.850 --> 00:10:09.269 align:start position:0%
looking for well we went down there's
only<00:10:07.120><c> 6</c><00:10:07.509><c> nodes</c><00:10:07.930><c> in</c><00:10:08.199><c> the</c><00:10:08.319><c> tree</c><00:10:08.350><c> and</c><00:10:08.829><c> we</c><00:10:08.920><c> had</c><00:10:09.160><c> to</c>

00:10:09.269 --> 00:10:09.279 align:start position:0%
only 6 nodes in the tree and we had to
 

00:10:09.279 --> 00:10:13.679 align:start position:0%
only 6 nodes in the tree and we had to
go<00:10:09.399><c> to</c><00:10:09.430><c> the</c><00:10:09.699><c> last</c><00:10:09.970><c> one</c><00:10:12.149><c> because</c><00:10:13.149><c> in</c><00:10:13.329><c> a</c><00:10:13.420><c> typical</c>

00:10:13.679 --> 00:10:13.689 align:start position:0%
go to the last one because in a typical
 

00:10:13.689 --> 00:10:15.749 align:start position:0%
go to the last one because in a typical
on<00:10:14.199><c> average</c><00:10:14.529><c> case</c><00:10:14.709><c> of</c><00:10:14.829><c> advice</c><00:10:15.009><c> binary</c><00:10:15.610><c> search</c>

00:10:15.749 --> 00:10:15.759 align:start position:0%
on average case of advice binary search
 

00:10:15.759 --> 00:10:20.100 align:start position:0%
on average case of advice binary search
tree<00:10:16.649><c> you</c><00:10:17.649><c> would</c><00:10:17.860><c> say</c><00:10:18.569><c> you</c><00:10:19.569><c> can</c><00:10:19.689><c> always</c><00:10:19.839><c> knock</c>

00:10:20.100 --> 00:10:20.110 align:start position:0%
tree you would say you can always knock
 

00:10:20.110 --> 00:10:22.559 align:start position:0%
tree you would say you can always knock
out<00:10:20.319><c> half</c><00:10:20.680><c> of</c><00:10:21.009><c> the</c><00:10:21.160><c> nodes</c><00:10:21.339><c> at</c><00:10:21.550><c> each</c><00:10:21.639><c> level</c><00:10:21.939><c> on</c>

00:10:22.559 --> 00:10:22.569 align:start position:0%
out half of the nodes at each level on
 

00:10:22.569 --> 00:10:25.350 align:start position:0%
out half of the nodes at each level on
average<00:10:23.399><c> especially</c><00:10:24.399><c> in</c><00:10:24.459><c> a</c><00:10:24.490><c> balanced</c><00:10:24.819><c> one</c><00:10:25.060><c> you</c>

00:10:25.350 --> 00:10:25.360 align:start position:0%
average especially in a balanced one you
 

00:10:25.360 --> 00:10:27.150 align:start position:0%
average especially in a balanced one you
know<00:10:25.449><c> so</c><00:10:25.810><c> that</c><00:10:26.019><c> means</c><00:10:26.199><c> it's</c><00:10:26.350><c> log</c><00:10:26.620><c> of</c><00:10:26.800><c> n</c><00:10:26.920><c> which</c>

00:10:27.150 --> 00:10:27.160 align:start position:0%
know so that means it's log of n which
 

00:10:27.160 --> 00:10:29.040 align:start position:0%
know so that means it's log of n which
is<00:10:27.639><c> what</c><00:10:27.819><c> you</c><00:10:27.939><c> might</c><00:10:28.089><c> have</c><00:10:28.209><c> thought</c><00:10:28.269><c> again</c>

00:10:29.040 --> 00:10:29.050 align:start position:0%
is what you might have thought again
 

00:10:29.050 --> 00:10:30.960 align:start position:0%
is what you might have thought again
this<00:10:29.199><c> is</c><00:10:29.319><c> worst</c><00:10:29.769><c> case</c><00:10:29.920><c> in</c><00:10:30.339><c> the</c><00:10:30.399><c> worst</c><00:10:30.550><c> case</c><00:10:30.730><c> is</c>

00:10:30.960 --> 00:10:30.970 align:start position:0%
this is worst case in the worst case is
 

00:10:30.970 --> 00:10:34.679 align:start position:0%
this is worst case in the worst case is
that<00:10:31.839><c> we</c><00:10:32.199><c> have</c><00:10:32.319><c> to</c><00:10:32.439><c> traverse</c><00:10:32.800><c> all</c><00:10:33.069><c> nodes</c><00:10:33.639><c> in</c><00:10:34.029><c> an</c>

00:10:34.679 --> 00:10:34.689 align:start position:0%
that we have to traverse all nodes in an
 

00:10:34.689 --> 00:10:36.600 align:start position:0%
that we have to traverse all nodes in an
unbalanced<00:10:35.170><c> binary</c><00:10:35.410><c> search</c><00:10:35.589><c> tree</c><00:10:35.829><c> to</c><00:10:36.160><c> find</c>

00:10:36.600 --> 00:10:36.610 align:start position:0%
unbalanced binary search tree to find
 

00:10:36.610 --> 00:10:40.559 align:start position:0%
unbalanced binary search tree to find
the<00:10:36.910><c> node</c><00:10:37.630><c> that</c><00:10:37.959><c> we're</c><00:10:38.439><c> looking</c><00:10:38.680><c> for</c><00:10:39.269><c> so</c><00:10:40.269><c> that</c>

00:10:40.559 --> 00:10:40.569 align:start position:0%
the node that we're looking for so that
 

00:10:40.569 --> 00:10:45.269 align:start position:0%
the node that we're looking for so that
means<00:10:41.050><c> that</c><00:10:41.560><c> this</c><00:10:41.860><c> is</c><00:10:42.100><c> Big</c><00:10:42.850><c> O</c><00:10:43.060><c> of</c><00:10:43.149><c> n</c><00:10:44.069><c> ok</c><00:10:45.069><c> and</c>

00:10:45.269 --> 00:10:45.279 align:start position:0%
means that this is Big O of n ok and
 

00:10:45.279 --> 00:10:48.090 align:start position:0%
means that this is Big O of n ok and
being<00:10:46.000><c> the</c><00:10:46.209><c> input</c><00:10:46.329><c> size</c><00:10:46.720><c> which</c><00:10:47.620><c> in</c><00:10:47.800><c> this</c><00:10:47.889><c> case</c>

00:10:48.090 --> 00:10:48.100 align:start position:0%
being the input size which in this case
 

00:10:48.100 --> 00:10:50.819 align:start position:0%
being the input size which in this case
is<00:10:48.490><c> 6</c><00:10:48.759><c> nodes</c><00:10:49.060><c> I</c><00:10:49.389><c> mean</c><00:10:49.660><c> you</c><00:10:49.720><c> do</c><00:10:49.810><c> it</c><00:10:49.839><c> for</c><00:10:50.050><c> 10</c><00:10:50.259><c> you</c>

00:10:50.819 --> 00:10:50.829 align:start position:0%
is 6 nodes I mean you do it for 10 you
 

00:10:50.829 --> 00:10:53.759 align:start position:0%
is 6 nodes I mean you do it for 10 you
know<00:10:50.920><c> whichever</c><00:10:51.279><c> it</c><00:10:51.790><c> doesn't</c><00:10:52.029><c> matter</c><00:10:52.680><c> but</c><00:10:53.680><c> the</c>

00:10:53.759 --> 00:10:53.769 align:start position:0%
know whichever it doesn't matter but the
 

00:10:53.769 --> 00:10:55.019 align:start position:0%
know whichever it doesn't matter but the
worst<00:10:53.920><c> case</c><00:10:54.100><c> is</c><00:10:54.279><c> going</c><00:10:54.610><c> through</c><00:10:54.790><c> all</c><00:10:54.939><c> the</c>

00:10:55.019 --> 00:10:55.029 align:start position:0%
worst case is going through all the
 

00:10:55.029 --> 00:10:56.699 align:start position:0%
worst case is going through all the
nodes<00:10:55.180><c> to</c><00:10:55.420><c> find</c><00:10:55.630><c> what</c><00:10:55.839><c> you</c><00:10:55.930><c> need</c><00:10:55.959><c> all</c><00:10:56.560><c> right</c>

00:10:56.699 --> 00:10:56.709 align:start position:0%
nodes to find what you need all right
 

00:10:56.709 --> 00:10:57.809 align:start position:0%
nodes to find what you need all right
typically<00:10:57.009><c> doesn't</c><00:10:57.160><c> happen</c><00:10:57.370><c> in</c><00:10:57.550><c> the</c><00:10:57.610><c> average</c>

00:10:57.809 --> 00:10:57.819 align:start position:0%
typically doesn't happen in the average
 

00:10:57.819 --> 00:10:59.280 align:start position:0%
typically doesn't happen in the average
case<00:10:58.000><c> because</c><00:10:58.329><c> you</c><00:10:58.449><c> can</c><00:10:58.569><c> eliminate</c><00:10:58.779><c> half</c><00:10:59.199><c> of</c>

00:10:59.280 --> 00:10:59.290 align:start position:0%
case because you can eliminate half of
 

00:10:59.290 --> 00:11:01.439 align:start position:0%
case because you can eliminate half of
them<00:10:59.439><c> on</c><00:10:59.740><c> each</c><00:10:59.889><c> level</c><00:11:00.160><c> which</c><00:11:00.819><c> makes</c><00:11:00.970><c> it</c><00:11:01.120><c> log</c><00:11:01.300><c> of</c>

00:11:01.439 --> 00:11:01.449 align:start position:0%
them on each level which makes it log of
 

00:11:01.449 --> 00:11:06.050 align:start position:0%
them on each level which makes it log of
n<00:11:01.949><c> ok</c><00:11:02.949><c> well</c><00:11:03.310><c> I</c><00:11:04.120><c> hope</c><00:11:04.569><c> you</c><00:11:04.720><c> hope</c><00:11:05.410><c> you</c><00:11:05.500><c> did</c><00:11:05.589><c> well</c>

00:11:06.050 --> 00:11:06.060 align:start position:0%
n ok well I hope you hope you did well
 

00:11:06.060 --> 00:11:08.009 align:start position:0%
n ok well I hope you hope you did well
let<00:11:07.060><c> me</c><00:11:07.089><c> know</c><00:11:07.209><c> in</c><00:11:07.269><c> the</c><00:11:07.300><c> comments</c><00:11:07.720><c> how</c><00:11:07.810><c> you</c><00:11:07.839><c> did</c>

00:11:08.009 --> 00:11:08.019 align:start position:0%
let me know in the comments how you did
 

00:11:08.019 --> 00:11:10.280 align:start position:0%
let me know in the comments how you did
and<00:11:08.259><c> if</c><00:11:08.980><c> you</c><00:11:09.069><c> have</c><00:11:09.160><c> any</c><00:11:09.279><c> more</c><00:11:09.370><c> questions</c>

00:11:10.280 --> 00:11:10.290 align:start position:0%
and if you have any more questions
 

00:11:10.290 --> 00:11:12.960 align:start position:0%
and if you have any more questions
please<00:11:11.290><c> reach</c><00:11:11.680><c> out</c><00:11:11.889><c> and</c><00:11:12.160><c> I</c><00:11:12.610><c> will</c><00:11:12.730><c> get</c><00:11:12.819><c> back</c><00:11:12.910><c> to</c>

00:11:12.960 --> 00:11:12.970 align:start position:0%
please reach out and I will get back to
 

00:11:12.970 --> 00:11:13.829 align:start position:0%
please reach out and I will get back to
you<00:11:13.089><c> as</c><00:11:13.149><c> soon</c><00:11:13.180><c> as</c><00:11:13.269><c> possible</c>

00:11:13.829 --> 00:11:13.839 align:start position:0%
you as soon as possible
 

00:11:13.839 --> 00:11:17.130 align:start position:0%
you as soon as possible
alright<00:11:14.290><c> I'll</c><00:11:14.680><c> see</c><00:11:14.829><c> you</c><00:11:14.889><c> next</c><00:11:14.920><c> time</c>

