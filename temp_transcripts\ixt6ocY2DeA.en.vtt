WEBVTT
Kind: captions
Language: en

00:00:00.650 --> 00:00:03.210
<PERSON>, <PERSON>, technovangelist here.

00:00:03.210 --> 00:00:07.810
I have really enjoyed the response on these
videos about the RAVPower FileHub.

00:00:07.810 --> 00:00:13.750
I received a few questions about using the
FileHub with a USB Hub to connect multiple

00:00:13.750 --> 00:00:14.750
hard drives.

00:00:14.750 --> 00:00:20.280
I went ahead and bought a little USB Hub from
<PERSON><PERSON>, plugged it in with two hard drives.

00:00:20.280 --> 00:00:23.779
I saw one of the drives but not the other.

00:00:23.779 --> 00:00:28.630
Then I was preparing a video about setting
up networking on the FileHub.

00:00:28.630 --> 00:00:36.130
I had to look up some details in the manual,
and I noticed an option I hadn't seen before.

00:00:36.130 --> 00:00:40.700
So I turned it on and tried connecting that
hub.

00:00:40.700 --> 00:00:43.910
To my amazement, I saw both hard drives appear.

00:00:43.910 --> 00:00:51.490
So it turns out you can connect multiple drives
with a USB hub.

00:00:51.490 --> 00:00:55.310
If you like videos like this, consider hitting
that subscribe button below.

00:00:55.310 --> 00:01:00.280
If you have any experience with the device,
share them with everyone in the comments section.

00:01:00.280 --> 00:01:04.010
So you bought a FileHub, maybe after seeing
one of my other videos.

00:01:04.010 --> 00:01:08.350
And you want to connect a hard drive and maybe
a compact flash card reader.

00:01:08.350 --> 00:01:13.940
Or maybe you want to back up your SD Card
to two drives, which is an excellent thing

00:01:13.940 --> 00:01:15.280
to do.

00:01:15.280 --> 00:01:18.460
To enable this, log in to the FileHub admin
page.

00:01:18.460 --> 00:01:22.700
You can't enable this from the apps; you have
to use the webpage.

00:01:22.700 --> 00:01:30.310
The URL is ************, and the default password
is blank, not the word blank, just nothing.

00:01:30.310 --> 00:01:31.880
Press enter.

00:01:31.880 --> 00:01:34.409
Click on Settings and then Services.

00:01:34.409 --> 00:01:39.200
You would have come here before to setup DLNA
settings that I mentioned in the video linked

00:01:39.200 --> 00:01:40.200
to above.

00:01:40.200 --> 00:01:43.820
Now click on Win File Service (Samba).

00:01:43.820 --> 00:01:46.340
Set the feature to On, and click save.

00:01:46.340 --> 00:01:50.090
Now you can plug in a hub with a few devices
connected.

00:01:50.090 --> 00:01:54.370
If you go back to settings then information
then storage, you will see that there are

00:01:54.370 --> 00:01:57.000
multiple devices called UsbDisk1.

00:01:57.000 --> 00:01:59.510
Yes, this is confusing.

00:01:59.510 --> 00:02:03.190
But click the home icon and then explorer.

00:02:03.190 --> 00:02:07.330
Now you should see an option for however many
disks are connected.

00:02:07.330 --> 00:02:11.959
You might need to click in each to figure
out which disk is which, but they are each

00:02:11.959 --> 00:02:13.840
uniquely addressable.

00:02:13.840 --> 00:02:24.170
This is pretty cool and solves the problem
I didn't know could be solved.

00:02:24.170 --> 00:02:34.930
If you find this video helpful, consider subscribing
to the channel right here.

00:02:34.930 --> 00:02:38.620
If you have any other questions, let me know
in the comments; as you can see, I have been

00:02:38.620 --> 00:02:41.620
making some of those questions into videos.

00:02:41.620 --> 00:02:45.919
And check out some of these other videos that
I think you might also find interesting.

00:02:45.919 --> 00:02:47.769
Thanks for watching.

00:02:47.769 --> 00:02:48.340
Goodbye.

