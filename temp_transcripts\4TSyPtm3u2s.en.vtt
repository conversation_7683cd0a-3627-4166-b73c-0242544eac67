WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.870 align:start position:0%
 
AI<00:00:00.320><c> agents</c><00:00:00.560><c> are</c><00:00:00.719><c> here</c><00:00:00.840><c> to</c><00:00:01.000><c> stay</c><00:00:01.439><c> at</c><00:00:01.560><c> least</c><00:00:01.760><c> for</c>

00:00:01.870 --> 00:00:01.880 align:start position:0%
AI agents are here to stay at least for
 

00:00:01.880 --> 00:00:03.550 align:start position:0%
AI agents are here to stay at least for
a<00:00:02.040><c> while</c><00:00:02.240><c> we</c><00:00:02.360><c> going</c><00:00:02.520><c> have</c><00:00:02.639><c> multiple</c><00:00:03.040><c> agents</c>

00:00:03.550 --> 00:00:03.560 align:start position:0%
a while we going have multiple agents
 

00:00:03.560 --> 00:00:05.869 align:start position:0%
a while we going have multiple agents
work<00:00:04.000><c> together</c><00:00:04.480><c> to</c><00:00:04.720><c> come</c><00:00:04.839><c> up</c><00:00:05.000><c> with</c><00:00:05.080><c> a</c><00:00:05.279><c> plan</c>

00:00:05.869 --> 00:00:05.879 align:start position:0%
work together to come up with a plan
 

00:00:05.879 --> 00:00:07.670 align:start position:0%
work together to come up with a plan
code<00:00:06.200><c> something</c><00:00:06.680><c> test</c><00:00:06.960><c> all</c><00:00:07.160><c> that</c><00:00:07.319><c> code</c><00:00:07.600><c> and</c>

00:00:07.670 --> 00:00:07.680 align:start position:0%
code something test all that code and
 

00:00:07.680 --> 00:00:09.709 align:start position:0%
code something test all that code and
then<00:00:07.839><c> give</c><00:00:08.000><c> us</c><00:00:08.160><c> the</c><00:00:08.400><c> final</c><00:00:08.719><c> code</c><00:00:09.040><c> output</c><00:00:09.599><c> and</c>

00:00:09.709 --> 00:00:09.719 align:start position:0%
then give us the final code output and
 

00:00:09.719 --> 00:00:11.190 align:start position:0%
then give us the final code output and
if<00:00:09.800><c> you've</c><00:00:09.960><c> never</c><00:00:10.200><c> created</c><00:00:10.480><c> an</c><00:00:10.599><c> AI</c><00:00:10.880><c> agent</c>

00:00:11.190 --> 00:00:11.200 align:start position:0%
if you've never created an AI agent
 

00:00:11.200 --> 00:00:12.669 align:start position:0%
if you've never created an AI agent
before<00:00:11.679><c> I</c><00:00:11.759><c> would</c><00:00:11.920><c> dare</c><00:00:12.080><c> to</c><00:00:12.200><c> say</c><00:00:12.360><c> that</c><00:00:12.480><c> you've</c>

00:00:12.669 --> 00:00:12.679 align:start position:0%
before I would dare to say that you've
 

00:00:12.679 --> 00:00:14.629 align:start position:0%
before I would dare to say that you've
at<00:00:12.840><c> least</c><00:00:13.120><c> interacted</c><00:00:13.719><c> with</c><00:00:13.880><c> one</c><00:00:14.360><c> let</c><00:00:14.480><c> me</c>

00:00:14.629 --> 00:00:14.639 align:start position:0%
at least interacted with one let me
 

00:00:14.639 --> 00:00:16.070 align:start position:0%
at least interacted with one let me
explain<00:00:15.080><c> I'm</c><00:00:15.160><c> going</c><00:00:15.240><c> to</c><00:00:15.320><c> introduce</c><00:00:15.679><c> you</c><00:00:15.759><c> to</c><00:00:15.879><c> a</c>

00:00:16.070 --> 00:00:16.080 align:start position:0%
explain I'm going to introduce you to a
 

00:00:16.080 --> 00:00:18.189 align:start position:0%
explain I'm going to introduce you to a
popular<00:00:16.480><c> agentic</c><00:00:16.920><c> framework</c><00:00:17.359><c> called</c><00:00:17.600><c> autogen</c>

00:00:18.189 --> 00:00:18.199 align:start position:0%
popular agentic framework called autogen
 

00:00:18.199 --> 00:00:20.109 align:start position:0%
popular agentic framework called autogen
it's<00:00:18.400><c> actually</c><00:00:18.640><c> a</c><00:00:18.800><c> multi-agent</c><00:00:19.520><c> framework</c>

00:00:20.109 --> 00:00:20.119 align:start position:0%
it's actually a multi-agent framework
 

00:00:20.119 --> 00:00:21.830 align:start position:0%
it's actually a multi-agent framework
all<00:00:20.279><c> that</c><00:00:20.519><c> really</c><00:00:20.800><c> means</c><00:00:21.160><c> is</c><00:00:21.279><c> that</c><00:00:21.439><c> we</c><00:00:21.560><c> can</c>

00:00:21.830 --> 00:00:21.840 align:start position:0%
all that really means is that we can
 

00:00:21.840 --> 00:00:24.390 align:start position:0%
all that really means is that we can
have<00:00:22.320><c> multiple</c><00:00:22.960><c> agents</c><00:00:23.480><c> talk</c><00:00:23.920><c> together</c><00:00:24.279><c> and</c>

00:00:24.390 --> 00:00:24.400 align:start position:0%
have multiple agents talk together and
 

00:00:24.400 --> 00:00:26.230 align:start position:0%
have multiple agents talk together and
then<00:00:24.640><c> produce</c><00:00:25.039><c> something</c><00:00:25.400><c> for</c><00:00:25.599><c> us</c><00:00:25.920><c> and</c><00:00:26.039><c> we</c><00:00:26.119><c> can</c>

00:00:26.230 --> 00:00:26.240 align:start position:0%
then produce something for us and we can
 

00:00:26.240 --> 00:00:27.589 align:start position:0%
then produce something for us and we can
have<00:00:26.439><c> different</c><00:00:26.760><c> ways</c><00:00:27.039><c> of</c><00:00:27.240><c> having</c><00:00:27.439><c> that</c>

00:00:27.589 --> 00:00:27.599 align:start position:0%
have different ways of having that
 

00:00:27.599 --> 00:00:29.630 align:start position:0%
have different ways of having that
conversation<00:00:28.279><c> and</c><00:00:28.400><c> aen</c><00:00:28.840><c> offers</c><00:00:29.160><c> so</c><00:00:29.320><c> much</c><00:00:29.519><c> out</c>

00:00:29.630 --> 00:00:29.640 align:start position:0%
conversation and aen offers so much out
 

00:00:29.640 --> 00:00:32.229 align:start position:0%
conversation and aen offers so much out
of<00:00:29.720><c> the</c><00:00:30.000><c> box</c><00:00:30.279><c> such</c><00:00:30.400><c> as</c><00:00:30.640><c> rag</c><00:00:31.320><c> context</c><00:00:31.759><c> handling</c>

00:00:32.229 --> 00:00:32.239 align:start position:0%
of the box such as rag context handling
 

00:00:32.239 --> 00:00:34.750 align:start position:0%
of the box such as rag context handling
compression<00:00:33.079><c> integrating</c><00:00:33.600><c> with</c><00:00:33.760><c> local</c><00:00:34.079><c> llms</c>

00:00:34.750 --> 00:00:34.760 align:start position:0%
compression integrating with local llms
 

00:00:34.760 --> 00:00:36.510 align:start position:0%
compression integrating with local llms
and<00:00:34.920><c> much</c><00:00:35.120><c> more</c><00:00:35.440><c> but</c><00:00:35.600><c> like</c><00:00:35.719><c> I</c><00:00:35.840><c> mentioned</c><00:00:36.399><c> I</c>

00:00:36.510 --> 00:00:36.520 align:start position:0%
and much more but like I mentioned I
 

00:00:36.520 --> 00:00:38.110 align:start position:0%
and much more but like I mentioned I
think<00:00:36.719><c> you've</c><00:00:37.000><c> interacted</c><00:00:37.559><c> with</c><00:00:37.680><c> a</c><00:00:37.800><c> single</c>

00:00:38.110 --> 00:00:38.120 align:start position:0%
think you've interacted with a single
 

00:00:38.120 --> 00:00:39.470 align:start position:0%
think you've interacted with a single
agent<00:00:38.480><c> before</c><00:00:38.840><c> you</c><00:00:39.000><c> more</c><00:00:39.120><c> than</c><00:00:39.239><c> likely</c>

00:00:39.470 --> 00:00:39.480 align:start position:0%
agent before you more than likely
 

00:00:39.480 --> 00:00:41.709 align:start position:0%
agent before you more than likely
interacted<00:00:39.879><c> with</c><00:00:39.960><c> chat</c><00:00:40.320><c> PT</c><00:00:40.640><c> at</c><00:00:40.760><c> least</c><00:00:41.039><c> once</c><00:00:41.520><c> so</c>

00:00:41.709 --> 00:00:41.719 align:start position:0%
interacted with chat PT at least once so
 

00:00:41.719 --> 00:00:43.310 align:start position:0%
interacted with chat PT at least once so
chat<00:00:42.160><c> PT</c><00:00:42.480><c> when</c><00:00:42.559><c> you</c><00:00:42.680><c> talk</c><00:00:42.800><c> to</c><00:00:42.920><c> them</c><00:00:43.120><c> they</c><00:00:43.200><c> can</c>

00:00:43.310 --> 00:00:43.320 align:start position:0%
chat PT when you talk to them they can
 

00:00:43.320 --> 00:00:45.549 align:start position:0%
chat PT when you talk to them they can
be<00:00:43.440><c> the</c><00:00:43.559><c> AI</c><00:00:43.879><c> agent</c><00:00:44.160><c> and</c><00:00:44.320><c> we</c><00:00:44.440><c> are</c><00:00:44.640><c> the</c><00:00:44.800><c> user</c><00:00:45.360><c> so</c>

00:00:45.549 --> 00:00:45.559 align:start position:0%
be the AI agent and we are the user so
 

00:00:45.559 --> 00:00:47.790 align:start position:0%
be the AI agent and we are the user so
me<00:00:45.800><c> as</c><00:00:45.960><c> a</c><00:00:46.160><c> user</c><00:00:46.719><c> I'm</c><00:00:46.840><c> going</c><00:00:47.000><c> to</c><00:00:47.199><c> ask</c><00:00:47.440><c> them</c><00:00:47.559><c> to</c>

00:00:47.790 --> 00:00:47.800 align:start position:0%
me as a user I'm going to ask them to
 

00:00:47.800 --> 00:00:49.990 align:start position:0%
me as a user I'm going to ask them to
write<00:00:48.079><c> a</c><00:00:48.320><c> python</c><00:00:48.760><c> function</c><00:00:49.160><c> that</c><00:00:49.360><c> reverses</c><00:00:49.840><c> a</c>

00:00:49.990 --> 00:00:50.000 align:start position:0%
write a python function that reverses a
 

00:00:50.000 --> 00:00:52.389 align:start position:0%
write a python function that reverses a
string<00:00:50.680><c> and</c><00:00:50.800><c> then</c><00:00:50.960><c> they</c><00:00:51.079><c> are</c><00:00:51.280><c> the</c><00:00:51.520><c> AI</c><00:00:52.039><c> agent</c>

00:00:52.389 --> 00:00:52.399 align:start position:0%
string and then they are the AI agent
 

00:00:52.399 --> 00:00:54.150 align:start position:0%
string and then they are the AI agent
that's<00:00:52.559><c> going</c><00:00:52.680><c> to</c><00:00:52.760><c> give</c><00:00:52.879><c> us</c><00:00:53.079><c> response</c><00:00:53.559><c> back</c><00:00:54.000><c> so</c>

00:00:54.150 --> 00:00:54.160 align:start position:0%
that's going to give us response back so
 

00:00:54.160 --> 00:00:56.790 align:start position:0%
that's going to give us response back so
whenever<00:00:54.600><c> they</c><00:00:54.760><c> do</c><00:00:55.359><c> we</c><00:00:55.559><c> now</c><00:00:55.879><c> have</c><00:00:56.199><c> code</c><00:00:56.600><c> that</c>

00:00:56.790 --> 00:00:56.800 align:start position:0%
whenever they do we now have code that
 

00:00:56.800 --> 00:00:58.430 align:start position:0%
whenever they do we now have code that
tell<00:00:57.000><c> shows</c><00:00:57.199><c> us</c><00:00:57.359><c> how</c><00:00:57.440><c> to</c><00:00:57.600><c> reverse</c><00:00:57.840><c> a</c><00:00:58.000><c> string</c><00:00:58.239><c> in</c>

00:00:58.430 --> 00:00:58.440 align:start position:0%
tell shows us how to reverse a string in
 

00:00:58.440 --> 00:01:00.590 align:start position:0%
tell shows us how to reverse a string in
Python<00:00:58.879><c> this</c><00:00:58.960><c> is</c><00:00:59.079><c> an</c><00:00:59.239><c> example</c><00:00:59.559><c> of</c><00:00:59.680><c> a</c><00:00:59.960><c> 2</c><00:01:00.160><c> a</c><00:01:00.320><c> chat</c>

00:01:00.590 --> 00:01:00.600 align:start position:0%
Python this is an example of a 2 a chat
 

00:01:00.600 --> 00:01:01.869 align:start position:0%
Python this is an example of a 2 a chat
that<00:01:00.719><c> I'm</c><00:01:00.840><c> also</c><00:01:01.039><c> going</c><00:01:01.160><c> to</c><00:01:01.280><c> show</c><00:01:01.480><c> you</c><00:01:01.719><c> with</c>

00:01:01.869 --> 00:01:01.879 align:start position:0%
that I'm also going to show you with
 

00:01:01.879 --> 00:01:03.670 align:start position:0%
that I'm also going to show you with
autojen<00:01:02.559><c> this</c><00:01:02.640><c> is</c><00:01:02.760><c> a</c><00:01:02.840><c> sample</c><00:01:03.160><c> conversation</c>

00:01:03.670 --> 00:01:03.680 align:start position:0%
autojen this is a sample conversation
 

00:01:03.680 --> 00:01:05.270 align:start position:0%
autojen this is a sample conversation
where<00:01:03.879><c> we</c><00:01:04.040><c> are</c><00:01:04.199><c> just</c><00:01:04.280><c> going</c><00:01:04.400><c> to</c><00:01:04.439><c> be</c><00:01:04.600><c> talking</c><00:01:05.080><c> to</c>

00:01:05.270 --> 00:01:05.280 align:start position:0%
where we are just going to be talking to
 

00:01:05.280 --> 00:01:07.190 align:start position:0%
where we are just going to be talking to
an<00:01:05.439><c> AI</c><00:01:05.760><c> agent</c><00:01:06.200><c> back</c><00:01:06.320><c> and</c><00:01:06.479><c> forth</c><00:01:06.720><c> until</c><00:01:06.920><c> we</c><00:01:07.040><c> get</c>

00:01:07.190 --> 00:01:07.200 align:start position:0%
an AI agent back and forth until we get
 

00:01:07.200 --> 00:01:08.350 align:start position:0%
an AI agent back and forth until we get
something<00:01:07.400><c> that</c><00:01:07.520><c> we</c><00:01:07.640><c> like</c><00:01:08.000><c> we're</c><00:01:08.119><c> going</c><00:01:08.200><c> to</c>

00:01:08.350 --> 00:01:08.360 align:start position:0%
something that we like we're going to
 

00:01:08.360 --> 00:01:09.830 align:start position:0%
something that we like we're going to
code<00:01:08.640><c> two</c><00:01:08.840><c> different</c><00:01:09.080><c> agents</c><00:01:09.439><c> to</c><00:01:09.560><c> have</c><00:01:09.720><c> them</c>

00:01:09.830 --> 00:01:09.840 align:start position:0%
code two different agents to have them
 

00:01:09.840 --> 00:01:11.070 align:start position:0%
code two different agents to have them
talk<00:01:10.000><c> to</c><00:01:10.159><c> each</c><00:01:10.280><c> other</c><00:01:10.680><c> and</c><00:01:10.759><c> we're</c><00:01:10.880><c> going</c><00:01:10.960><c> to</c>

00:01:11.070 --> 00:01:11.080 align:start position:0%
talk to each other and we're going to
 

00:01:11.080 --> 00:01:12.789 align:start position:0%
talk to each other and we're going to
give<00:01:11.200><c> a</c><00:01:11.360><c> configuration</c><00:01:11.840><c> to</c><00:01:11.960><c> the</c><00:01:12.080><c> AI</c><00:01:12.400><c> agent</c><00:01:12.640><c> so</c>

00:01:12.789 --> 00:01:12.799 align:start position:0%
give a configuration to the AI agent so
 

00:01:12.799 --> 00:01:15.149 align:start position:0%
give a configuration to the AI agent so
it<00:01:12.960><c> knows</c><00:01:13.520><c> what</c><00:01:13.720><c> model</c><00:01:14.040><c> to</c><00:01:14.320><c> actually</c><00:01:14.759><c> get</c><00:01:14.960><c> an</c>

00:01:15.149 --> 00:01:15.159 align:start position:0%
it knows what model to actually get an
 

00:01:15.159 --> 00:01:17.710 align:start position:0%
it knows what model to actually get an
output<00:01:15.600><c> from</c><00:01:16.159><c> well</c><00:01:16.520><c> let's</c><00:01:16.759><c> begin</c><00:01:17.080><c> coding</c>

00:01:17.710 --> 00:01:17.720 align:start position:0%
output from well let's begin coding
 

00:01:17.720 --> 00:01:19.710 align:start position:0%
output from well let's begin coding
first<00:01:17.880><c> off</c><00:01:18.040><c> use</c><00:01:18.240><c> the</c><00:01:18.400><c> IDE</c><00:01:18.880><c> of</c><00:01:19.000><c> your</c><00:01:19.159><c> choosing</c>

00:01:19.710 --> 00:01:19.720 align:start position:0%
first off use the IDE of your choosing
 

00:01:19.720 --> 00:01:21.190 align:start position:0%
first off use the IDE of your choosing
I'm<00:01:19.880><c> going</c><00:01:20.040><c> to</c><00:01:20.159><c> use</c><00:01:20.360><c> py</c><00:01:20.600><c> charm</c><00:01:20.840><c> and</c><00:01:20.960><c> the</c><00:01:21.040><c> first</c>

00:01:21.190 --> 00:01:21.200 align:start position:0%
I'm going to use py charm and the first
 

00:01:21.200 --> 00:01:22.710 align:start position:0%
I'm going to use py charm and the first
thing<00:01:21.320><c> I'm</c><00:01:21.400><c> going</c><00:01:21.520><c> to</c><00:01:21.640><c> do</c><00:01:22.000><c> is</c><00:01:22.200><c> create</c><00:01:22.479><c> a</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
thing I'm going to do is create a
 

00:01:22.720 --> 00:01:24.710 align:start position:0%
thing I'm going to do is create a
project<00:01:23.040><c> so</c><00:01:23.200><c> you</c><00:01:23.320><c> just</c><00:01:23.439><c> go</c><00:01:23.520><c> to</c><00:01:23.680><c> file</c><00:01:24.400><c> new</c>

00:01:24.710 --> 00:01:24.720 align:start position:0%
project so you just go to file new
 

00:01:24.720 --> 00:01:27.069 align:start position:0%
project so you just go to file new
project<00:01:25.520><c> go</c><00:01:25.640><c> ahead</c><00:01:25.960><c> and</c><00:01:26.159><c> choose</c><00:01:26.600><c> the</c><00:01:26.720><c> name</c><00:01:26.960><c> of</c>

00:01:27.069 --> 00:01:27.079 align:start position:0%
project go ahead and choose the name of
 

00:01:27.079 --> 00:01:28.510 align:start position:0%
project go ahead and choose the name of
it<00:01:27.400><c> and</c><00:01:27.520><c> then</c><00:01:27.680><c> choose</c><00:01:27.920><c> The</c><00:01:28.040><c> Interpreter</c>

00:01:28.510 --> 00:01:28.520 align:start position:0%
it and then choose The Interpreter
 

00:01:28.520 --> 00:01:29.870 align:start position:0%
it and then choose The Interpreter
whether<00:01:28.640><c> you</c><00:01:28.720><c> want</c><00:01:28.799><c> to</c><00:01:28.840><c> use</c><00:01:29.000><c> cond</c><00:01:29.439><c> or</c><00:01:29.600><c> virtual</c>

00:01:29.870 --> 00:01:29.880 align:start position:0%
whether you want to use cond or virtual
 

00:01:29.880 --> 00:01:31.469 align:start position:0%
whether you want to use cond or virtual
ual<00:01:30.079><c> EnV</c><00:01:30.600><c> once</c><00:01:30.759><c> that's</c><00:01:30.920><c> done</c><00:01:31.079><c> you</c><00:01:31.159><c> can</c><00:01:31.240><c> create</c>

00:01:31.469 --> 00:01:31.479 align:start position:0%
ual EnV once that's done you can create
 

00:01:31.479 --> 00:01:33.389 align:start position:0%
ual EnV once that's done you can create
a<00:01:31.600><c> new</c><00:01:31.799><c> directory</c><00:01:32.520><c> and</c><00:01:32.680><c> then</c><00:01:32.880><c> inside</c><00:01:33.159><c> of</c><00:01:33.280><c> that</c>

00:01:33.389 --> 00:01:33.399 align:start position:0%
a new directory and then inside of that
 

00:01:33.399 --> 00:01:35.069 align:start position:0%
a new directory and then inside of that
directory<00:01:33.840><c> you</c><00:01:33.920><c> can</c><00:01:34.040><c> create</c><00:01:34.280><c> a</c><00:01:34.439><c> new</c><00:01:34.720><c> file</c>

00:01:35.069 --> 00:01:35.079 align:start position:0%
directory you can create a new file
 

00:01:35.079 --> 00:01:37.069 align:start position:0%
directory you can create a new file
called<00:01:35.320><c> first</c><00:01:36.119><c> agents</c><00:01:36.520><c> or</c><00:01:36.680><c> whatever</c><00:01:36.920><c> you</c><00:01:37.000><c> want</c>

00:01:37.069 --> 00:01:37.079 align:start position:0%
called first agents or whatever you want
 

00:01:37.079 --> 00:01:38.109 align:start position:0%
called first agents or whatever you want
to<00:01:37.159><c> name</c><00:01:37.280><c> it</c><00:01:37.479><c> well</c><00:01:37.600><c> now</c><00:01:37.680><c> the</c><00:01:37.759><c> first</c><00:01:37.920><c> thing</c><00:01:38.000><c> we</c>

00:01:38.109 --> 00:01:38.119 align:start position:0%
to name it well now the first thing we
 

00:01:38.119 --> 00:01:40.030 align:start position:0%
to name it well now the first thing we
need<00:01:38.200><c> to</c><00:01:38.320><c> do</c><00:01:38.520><c> is</c><00:01:38.680><c> install</c><00:01:39.119><c> the</c><00:01:39.280><c> library</c><00:01:39.720><c> called</c>

00:01:40.030 --> 00:01:40.040 align:start position:0%
need to do is install the library called
 

00:01:40.040 --> 00:01:41.389 align:start position:0%
need to do is install the library called
Pi<00:01:40.280><c> autogen</c><00:01:40.720><c> so</c><00:01:40.840><c> you</c><00:01:40.920><c> just</c><00:01:41.040><c> open</c><00:01:41.200><c> up</c><00:01:41.320><c> your</c>

00:01:41.389 --> 00:01:41.399 align:start position:0%
Pi autogen so you just open up your
 

00:01:41.399 --> 00:01:44.230 align:start position:0%
Pi autogen so you just open up your
terminal<00:01:42.119><c> and</c><00:01:42.200><c> you'll</c><00:01:42.439><c> type</c><00:01:42.600><c> in</c><00:01:42.840><c> PIP</c><00:01:43.479><c> install</c>

00:01:44.230 --> 00:01:44.240 align:start position:0%
terminal and you'll type in PIP install
 

00:01:44.240 --> 00:01:46.950 align:start position:0%
terminal and you'll type in PIP install
Pi<00:01:44.920><c> autogen</c><00:01:45.920><c> hit</c><00:01:46.119><c> enter</c><00:01:46.439><c> and</c><00:01:46.520><c> it'll</c><00:01:46.680><c> install</c>

00:01:46.950 --> 00:01:46.960 align:start position:0%
Pi autogen hit enter and it'll install
 

00:01:46.960 --> 00:01:48.069 align:start position:0%
Pi autogen hit enter and it'll install
everything<00:01:47.159><c> you</c><00:01:47.240><c> need</c><00:01:47.560><c> once</c><00:01:47.719><c> that's</c><00:01:47.880><c> done</c><00:01:48.000><c> the</c>

00:01:48.069 --> 00:01:48.079 align:start position:0%
everything you need once that's done the
 

00:01:48.079 --> 00:01:49.429 align:start position:0%
everything you need once that's done the
first<00:01:48.200><c> thing</c><00:01:48.280><c> we</c><00:01:48.399><c> need</c><00:01:48.520><c> to</c><00:01:48.640><c> do</c><00:01:48.840><c> is</c><00:01:49.079><c> import</c>

00:01:49.429 --> 00:01:49.439 align:start position:0%
first thing we need to do is import
 

00:01:49.439 --> 00:01:51.990 align:start position:0%
first thing we need to do is import
autogen<00:01:50.119><c> not</c><00:01:50.320><c> piy</c><00:01:50.520><c> autogen</c><00:01:51.119><c> import</c><00:01:51.520><c> autogen</c>

00:01:51.990 --> 00:01:52.000 align:start position:0%
autogen not piy autogen import autogen
 

00:01:52.000 --> 00:01:53.310 align:start position:0%
autogen not piy autogen import autogen
and<00:01:52.079><c> the</c><00:01:52.159><c> next</c><00:01:52.320><c> thing</c><00:01:52.399><c> we</c><00:01:52.479><c> need</c><00:01:52.640><c> is</c><00:01:52.759><c> a</c><00:01:52.920><c> config</c>

00:01:53.310 --> 00:01:53.320 align:start position:0%
and the next thing we need is a config
 

00:01:53.320 --> 00:01:55.469 align:start position:0%
and the next thing we need is a config
list<00:01:53.520><c> and</c><00:01:53.719><c> what</c><00:01:53.920><c> this</c><00:01:54.159><c> is</c><00:01:54.640><c> is</c><00:01:54.799><c> just</c><00:01:54.960><c> a</c><00:01:55.119><c> way</c><00:01:55.280><c> to</c>

00:01:55.469 --> 00:01:55.479 align:start position:0%
list and what this is is just a way to
 

00:01:55.479 --> 00:01:58.230 align:start position:0%
list and what this is is just a way to
tell<00:01:55.719><c> the</c><00:01:55.920><c> AI</c><00:01:56.360><c> agent</c><00:01:56.759><c> or</c><00:01:57.039><c> multiple</c><00:01:57.479><c> agents</c>

00:01:58.230 --> 00:01:58.240 align:start position:0%
tell the AI agent or multiple agents
 

00:01:58.240 --> 00:02:00.230 align:start position:0%
tell the AI agent or multiple agents
what<00:01:58.479><c> models</c><00:01:59.000><c> we</c><00:01:59.119><c> want</c><00:01:59.280><c> to</c><00:01:59.399><c> use</c><00:01:59.640><c> in</c><00:01:59.960><c> give</c><00:02:00.119><c> them</c>

00:02:00.230 --> 00:02:00.240 align:start position:0%
what models we want to use in give them
 

00:02:00.240 --> 00:02:02.870 align:start position:0%
what models we want to use in give them
an<00:02:00.439><c> API</c><00:02:00.880><c> key</c><00:02:01.240><c> if</c><00:02:01.399><c> we</c><00:02:01.520><c> need</c><00:02:01.719><c> to</c><00:02:02.159><c> so</c><00:02:02.360><c> in</c><00:02:02.520><c> this</c><00:02:02.680><c> case</c>

00:02:02.870 --> 00:02:02.880 align:start position:0%
an API key if we need to so in this case
 

00:02:02.880 --> 00:02:05.270 align:start position:0%
an API key if we need to so in this case
I<00:02:02.960><c> want</c><00:02:03.079><c> to</c><00:02:03.240><c> use</c><00:02:03.439><c> GPT</c><00:02:04.000><c> 4</c><00:02:04.560><c> or</c><00:02:04.759><c> you</c><00:02:04.840><c> can</c><00:02:05.000><c> also</c><00:02:05.159><c> just</c>

00:02:05.270 --> 00:02:05.280 align:start position:0%
I want to use GPT 4 or you can also just
 

00:02:05.280 --> 00:02:07.389 align:start position:0%
I want to use GPT 4 or you can also just
use<00:02:05.439><c> a</c><00:02:05.560><c> 3.5</c><00:02:06.159><c> turbo</c><00:02:06.479><c> model</c><00:02:07.039><c> then</c><00:02:07.159><c> we're</c><00:02:07.320><c> going</c>

00:02:07.389 --> 00:02:07.399 align:start position:0%
use a 3.5 turbo model then we're going
 

00:02:07.399 --> 00:02:09.190 align:start position:0%
use a 3.5 turbo model then we're going
to<00:02:07.560><c> create</c><00:02:07.880><c> our</c><00:02:08.080><c> first</c><00:02:08.399><c> agent</c><00:02:08.840><c> which</c><00:02:09.080><c> like</c>

00:02:09.190 --> 00:02:09.200 align:start position:0%
to create our first agent which like
 

00:02:09.200 --> 00:02:11.110 align:start position:0%
to create our first agent which like
we're<00:02:09.360><c> talking</c><00:02:09.560><c> in</c><00:02:09.679><c> chat</c><00:02:09.879><c> jpt</c><00:02:10.440><c> this</c><00:02:10.640><c> agent</c><00:02:10.959><c> is</c>

00:02:11.110 --> 00:02:11.120 align:start position:0%
we're talking in chat jpt this agent is
 

00:02:11.120 --> 00:02:12.990 align:start position:0%
we're talking in chat jpt this agent is
going<00:02:11.280><c> to</c><00:02:11.480><c> be</c><00:02:11.760><c> us</c><00:02:12.239><c> so</c><00:02:12.400><c> this</c><00:02:12.520><c> is</c><00:02:12.680><c> why</c><00:02:12.840><c> it's</c>

00:02:12.990 --> 00:02:13.000 align:start position:0%
going to be us so this is why it's
 

00:02:13.000 --> 00:02:14.869 align:start position:0%
going to be us so this is why it's
called<00:02:13.280><c> a</c><00:02:13.440><c> user</c><00:02:13.920><c> proxy</c><00:02:14.280><c> agent</c><00:02:14.560><c> because</c><00:02:14.760><c> this</c>

00:02:14.869 --> 00:02:14.879 align:start position:0%
called a user proxy agent because this
 

00:02:14.879 --> 00:02:16.790 align:start position:0%
called a user proxy agent because this
is<00:02:14.959><c> a</c><00:02:15.080><c> user</c><00:02:15.319><c> agent</c><00:02:15.560><c> so</c><00:02:15.680><c> it's</c><00:02:15.800><c> you</c><00:02:15.959><c> or</c><00:02:16.120><c> me</c><00:02:16.599><c> I</c><00:02:16.680><c> want</c>

00:02:16.790 --> 00:02:16.800 align:start position:0%
is a user agent so it's you or me I want
 

00:02:16.800 --> 00:02:18.830 align:start position:0%
is a user agent so it's you or me I want
to<00:02:16.920><c> give</c><00:02:17.000><c> it</c><00:02:17.120><c> a</c><00:02:17.280><c> name</c><00:02:17.560><c> of</c><00:02:17.760><c> user</c><00:02:18.480><c> we</c><00:02:18.599><c> have</c><00:02:18.720><c> to</c>

00:02:18.830 --> 00:02:18.840 align:start position:0%
to give it a name of user we have to
 

00:02:18.840 --> 00:02:21.030 align:start position:0%
to give it a name of user we have to
give<00:02:18.920><c> it</c><00:02:19.040><c> a</c><00:02:19.160><c> code</c><00:02:19.519><c> execution</c><00:02:20.000><c> config</c><00:02:20.640><c> you</c><00:02:20.760><c> can</c>

00:02:21.030 --> 00:02:21.040 align:start position:0%
give it a code execution config you can
 

00:02:21.040 --> 00:02:23.110 align:start position:0%
give it a code execution config you can
set<00:02:21.239><c> this</c><00:02:21.599><c> whole</c><00:02:21.760><c> thing</c><00:02:21.920><c> to</c><00:02:22.080><c> false</c><00:02:22.480><c> or</c><00:02:22.760><c> do</c><00:02:22.959><c> what</c>

00:02:23.110 --> 00:02:23.120 align:start position:0%
set this whole thing to false or do what
 

00:02:23.120 --> 00:02:25.070 align:start position:0%
set this whole thing to false or do what
I<00:02:23.280><c> did</c><00:02:23.480><c> and</c><00:02:23.640><c> what</c><00:02:23.760><c> this</c><00:02:23.920><c> is</c><00:02:24.040><c> going</c><00:02:24.160><c> to</c><00:02:24.319><c> do</c><00:02:24.920><c> is</c>

00:02:25.070 --> 00:02:25.080 align:start position:0%
I did and what this is going to do is
 

00:02:25.080 --> 00:02:27.190 align:start position:0%
I did and what this is going to do is
whenever<00:02:25.440><c> we</c><00:02:25.640><c> have</c><00:02:25.760><c> it</c><00:02:26.040><c> actually</c><00:02:26.400><c> write</c><00:02:26.800><c> code</c>

00:02:27.190 --> 00:02:27.200 align:start position:0%
whenever we have it actually write code
 

00:02:27.200 --> 00:02:29.589 align:start position:0%
whenever we have it actually write code
for<00:02:27.560><c> us</c><00:02:28.160><c> it's</c><00:02:28.360><c> going</c><00:02:28.480><c> to</c><00:02:28.680><c> save</c><00:02:28.920><c> it</c><00:02:29.160><c> under</c><00:02:29.440><c> the</c>

00:02:29.589 --> 00:02:29.599 align:start position:0%
for us it's going to save it under the
 

00:02:29.599 --> 00:02:31.589 align:start position:0%
for us it's going to save it under the
direct<00:02:29.879><c> directory</c><00:02:30.239><c> called</c><00:02:30.560><c> code</c><00:02:31.160><c> right</c><00:02:31.319><c> over</c>

00:02:31.589 --> 00:02:31.599 align:start position:0%
direct directory called code right over
 

00:02:31.599 --> 00:02:33.910 align:start position:0%
direct directory called code right over
here<00:02:32.040><c> and</c><00:02:32.160><c> if</c><00:02:32.239><c> you</c><00:02:32.360><c> want</c><00:02:32.519><c> to</c><00:02:32.680><c> use</c><00:02:32.879><c> Docker</c><00:02:33.760><c> then</c>

00:02:33.910 --> 00:02:33.920 align:start position:0%
here and if you want to use Docker then
 

00:02:33.920 --> 00:02:34.990 align:start position:0%
here and if you want to use Docker then
don't<00:02:34.160><c> have</c><00:02:34.239><c> to</c><00:02:34.400><c> you</c><00:02:34.480><c> don't</c><00:02:34.599><c> have</c><00:02:34.720><c> to</c><00:02:34.800><c> worry</c>

00:02:34.990 --> 00:02:35.000 align:start position:0%
don't have to you don't have to worry
 

00:02:35.000 --> 00:02:36.710 align:start position:0%
don't have to you don't have to worry
about<00:02:35.239><c> this</c><00:02:35.560><c> but</c><00:02:35.800><c> if</c><00:02:35.920><c> you</c><00:02:36.040><c> don't</c><00:02:36.239><c> have</c><00:02:36.400><c> Docker</c>

00:02:36.710 --> 00:02:36.720 align:start position:0%
about this but if you don't have Docker
 

00:02:36.720 --> 00:02:37.910 align:start position:0%
about this but if you don't have Docker
you<00:02:36.800><c> don't</c><00:02:36.920><c> know</c><00:02:37.040><c> what</c><00:02:37.120><c> it</c><00:02:37.239><c> is</c><00:02:37.440><c> just</c><00:02:37.560><c> set</c><00:02:37.760><c> this</c>

00:02:37.910 --> 00:02:37.920 align:start position:0%
you don't know what it is just set this
 

00:02:37.920 --> 00:02:39.229 align:start position:0%
you don't know what it is just set this
to<00:02:38.120><c> false</c><00:02:38.560><c> I'm</c><00:02:38.640><c> going</c><00:02:38.760><c> to</c><00:02:38.840><c> set</c><00:02:38.959><c> the</c><00:02:39.040><c> human</c>

00:02:39.229 --> 00:02:39.239 align:start position:0%
to false I'm going to set the human
 

00:02:39.239 --> 00:02:40.470 align:start position:0%
to false I'm going to set the human
input<00:02:39.480><c> mode</c><00:02:39.640><c> to</c><00:02:39.760><c> terminate</c><00:02:40.120><c> so</c><00:02:40.239><c> that</c><00:02:40.360><c> we're</c>

00:02:40.470 --> 00:02:40.480 align:start position:0%
input mode to terminate so that we're
 

00:02:40.480 --> 00:02:42.670 align:start position:0%
input mode to terminate so that we're
only<00:02:40.760><c> going</c><00:02:40.840><c> to</c><00:02:41.159><c> talk</c><00:02:41.680><c> back</c><00:02:41.840><c> to</c><00:02:42.000><c> it</c><00:02:42.239><c> whenever</c>

00:02:42.670 --> 00:02:42.680 align:start position:0%
only going to talk back to it whenever
 

00:02:42.680 --> 00:02:44.070 align:start position:0%
only going to talk back to it whenever
it's<00:02:42.879><c> about</c><00:02:43.040><c> to</c><00:02:43.200><c> be</c><00:02:43.319><c> done</c><00:02:43.480><c> or</c><00:02:43.599><c> it</c><00:02:43.720><c> thinks</c><00:02:43.920><c> it's</c>

00:02:44.070 --> 00:02:44.080 align:start position:0%
it's about to be done or it thinks it's
 

00:02:44.080 --> 00:02:45.509 align:start position:0%
it's about to be done or it thinks it's
done<00:02:44.319><c> and</c><00:02:44.400><c> then</c><00:02:44.519><c> we</c><00:02:44.640><c> give</c><00:02:44.760><c> it</c><00:02:44.879><c> a</c><00:02:45.000><c> termination</c>

00:02:45.509 --> 00:02:45.519 align:start position:0%
done and then we give it a termination
 

00:02:45.519 --> 00:02:47.509 align:start position:0%
done and then we give it a termination
message<00:02:46.040><c> so</c><00:02:46.159><c> it</c><00:02:46.239><c> knows</c><00:02:46.440><c> whenever</c><00:02:46.800><c> to</c><00:02:46.959><c> stop</c><00:02:47.400><c> and</c>

00:02:47.509 --> 00:02:47.519 align:start position:0%
message so it knows whenever to stop and
 

00:02:47.519 --> 00:02:48.830 align:start position:0%
message so it knows whenever to stop and
then<00:02:47.680><c> finally</c><00:02:48.000><c> we</c><00:02:48.080><c> want</c><00:02:48.200><c> to</c><00:02:48.319><c> create</c><00:02:48.599><c> the</c>

00:02:48.830 --> 00:02:48.840 align:start position:0%
then finally we want to create the
 

00:02:48.840 --> 00:02:50.949 align:start position:0%
then finally we want to create the
assistant<00:02:49.440><c> agent</c><00:02:50.120><c> again</c><00:02:50.319><c> we</c><00:02:50.440><c> give</c><00:02:50.560><c> it</c><00:02:50.640><c> a</c><00:02:50.760><c> name</c>

00:02:50.949 --> 00:02:50.959 align:start position:0%
assistant agent again we give it a name
 

00:02:50.959 --> 00:02:52.910 align:start position:0%
assistant agent again we give it a name
and<00:02:51.159><c> engineer</c><00:02:51.920><c> and</c><00:02:52.040><c> we</c><00:02:52.120><c> have</c><00:02:52.239><c> to</c><00:02:52.400><c> give</c><00:02:52.560><c> it</c><00:02:52.800><c> the</c>

00:02:52.910 --> 00:02:52.920 align:start position:0%
and engineer and we have to give it the
 

00:02:52.920 --> 00:02:55.309 align:start position:0%
and engineer and we have to give it the
llm<00:02:53.480><c> config</c><00:02:54.159><c> which</c><00:02:54.519><c> again</c><00:02:54.720><c> is</c><00:02:54.840><c> the</c><00:02:55.000><c> config</c>

00:02:55.309 --> 00:02:55.319 align:start position:0%
llm config which again is the config
 

00:02:55.319 --> 00:02:57.430 align:start position:0%
llm config which again is the config
list<00:02:55.519><c> so</c><00:02:55.640><c> it</c><00:02:55.760><c> knows</c><00:02:56.400><c> what</c><00:02:56.680><c> model</c><00:02:57.000><c> to</c><00:02:57.159><c> choose</c>

00:02:57.430 --> 00:02:57.440 align:start position:0%
list so it knows what model to choose
 

00:02:57.440 --> 00:02:59.550 align:start position:0%
list so it knows what model to choose
from<00:02:57.680><c> to</c><00:02:58.000><c> actually</c><00:02:58.519><c> give</c><00:02:58.680><c> us</c><00:02:58.800><c> a</c><00:02:59.000><c> response</c><00:02:59.360><c> back</c>

00:02:59.550 --> 00:02:59.560 align:start position:0%
from to actually give us a response back
 

00:02:59.560 --> 00:03:01.070 align:start position:0%
from to actually give us a response back
from<00:02:59.879><c> and</c><00:03:00.000><c> then</c><00:03:00.120><c> the</c><00:03:00.239><c> system</c><00:03:00.519><c> message</c><00:03:00.840><c> is</c><00:03:00.959><c> kind</c>

00:03:01.070 --> 00:03:01.080 align:start position:0%
from and then the system message is kind
 

00:03:01.080 --> 00:03:03.390 align:start position:0%
from and then the system message is kind
of<00:03:01.200><c> like</c><00:03:01.319><c> the</c><00:03:01.519><c> personality</c><00:03:02.239><c> of</c><00:03:02.480><c> this</c><00:03:02.640><c> AI</c><00:03:02.959><c> agent</c>

00:03:03.390 --> 00:03:03.400 align:start position:0%
of like the personality of this AI agent
 

00:03:03.400 --> 00:03:04.789 align:start position:0%
of like the personality of this AI agent
so<00:03:03.560><c> if</c><00:03:03.680><c> they</c><00:03:03.760><c> are</c><00:03:03.879><c> a</c><00:03:04.000><c> 10</c><00:03:04.200><c> times</c><00:03:04.519><c> python</c>

00:03:04.789 --> 00:03:04.799 align:start position:0%
so if they are a 10 times python
 

00:03:04.799 --> 00:03:06.750 align:start position:0%
so if they are a 10 times python
engineer<00:03:05.440><c> of</c><00:03:05.599><c> course</c><00:03:06.080><c> then</c><00:03:06.159><c> to</c><00:03:06.319><c> run</c><00:03:06.560><c> this</c><00:03:06.680><c> you</c>

00:03:06.750 --> 00:03:06.760 align:start position:0%
engineer of course then to run this you
 

00:03:06.760 --> 00:03:08.110 align:start position:0%
engineer of course then to run this you
can<00:03:06.920><c> open</c><00:03:07.120><c> up</c><00:03:07.239><c> your</c><00:03:07.360><c> terminal</c><00:03:07.720><c> and</c><00:03:07.879><c> type</c>

00:03:08.110 --> 00:03:08.120 align:start position:0%
can open up your terminal and type
 

00:03:08.120 --> 00:03:10.630 align:start position:0%
can open up your terminal and type
python<00:03:08.840><c> first</c><00:03:09.480><c> agents.</c><00:03:09.959><c> py</c><00:03:10.280><c> or</c><00:03:10.360><c> whatever</c><00:03:10.599><c> the</c>

00:03:10.630 --> 00:03:10.640 align:start position:0%
python first agents. py or whatever the
 

00:03:10.640 --> 00:03:12.869 align:start position:0%
python first agents. py or whatever the
name<00:03:10.760><c> of</c><00:03:10.879><c> your</c><00:03:11.040><c> python</c><00:03:11.360><c> file</c><00:03:11.680><c> is</c><00:03:12.200><c> or</c><00:03:12.599><c> you</c><00:03:12.720><c> can</c>

00:03:12.869 --> 00:03:12.879 align:start position:0%
name of your python file is or you can
 

00:03:12.879 --> 00:03:15.789 align:start position:0%
name of your python file is or you can
go<00:03:13.120><c> to</c><00:03:13.360><c> your</c><00:03:13.720><c> project</c><00:03:14.120><c> pain</c><00:03:14.879><c> rightclick</c><00:03:15.560><c> the</c>

00:03:15.789 --> 00:03:15.799 align:start position:0%
go to your project pain rightclick the
 

00:03:15.799 --> 00:03:18.589 align:start position:0%
go to your project pain rightclick the
file<00:03:16.200><c> and</c><00:03:16.319><c> then</c><00:03:16.480><c> come</c><00:03:16.720><c> down</c><00:03:16.879><c> to</c><00:03:17.120><c> run</c><00:03:17.920><c> okay</c><00:03:18.200><c> so</c>

00:03:18.589 --> 00:03:18.599 align:start position:0%
file and then come down to run okay so
 

00:03:18.599 --> 00:03:20.430 align:start position:0%
file and then come down to run okay so
after<00:03:18.840><c> we</c><00:03:19.040><c> ran</c><00:03:19.280><c> it</c><00:03:19.400><c> it's</c><00:03:19.519><c> saying</c><00:03:19.840><c> user</c><00:03:20.159><c> to</c><00:03:20.319><c> the</c>

00:03:20.430 --> 00:03:20.440 align:start position:0%
after we ran it it's saying user to the
 

00:03:20.440 --> 00:03:22.509 align:start position:0%
after we ran it it's saying user to the
engineer<00:03:21.280><c> this</c><00:03:21.400><c> is</c><00:03:21.560><c> what</c><00:03:21.720><c> we</c><00:03:21.959><c> asked</c><00:03:22.200><c> the</c><00:03:22.280><c> AI</c>

00:03:22.509 --> 00:03:22.519 align:start position:0%
engineer this is what we asked the AI
 

00:03:22.519 --> 00:03:23.789 align:start position:0%
engineer this is what we asked the AI
agents<00:03:22.760><c> to</c><00:03:22.879><c> do</c><00:03:23.040><c> and</c><00:03:23.159><c> we</c><00:03:23.239><c> want</c><00:03:23.360><c> you</c><00:03:23.440><c> to</c><00:03:23.560><c> create</c>

00:03:23.789 --> 00:03:23.799 align:start position:0%
agents to do and we want you to create
 

00:03:23.799 --> 00:03:26.270 align:start position:0%
agents to do and we want you to create
two<00:03:24.000><c> python</c><00:03:24.319><c> methods</c><00:03:24.640><c> for</c><00:03:24.840><c> us</c><00:03:25.000><c> in</c><00:03:25.159><c> one</c><00:03:25.440><c> file</c><00:03:26.040><c> so</c>

00:03:26.270 --> 00:03:26.280 align:start position:0%
two python methods for us in one file so
 

00:03:26.280 --> 00:03:28.630 align:start position:0%
two python methods for us in one file so
the<00:03:26.400><c> engineer</c><00:03:27.000><c> responding</c><00:03:27.599><c> back</c><00:03:27.799><c> to</c><00:03:28.040><c> us</c><00:03:28.480><c> the</c>

00:03:28.630 --> 00:03:28.640 align:start position:0%
the engineer responding back to us the
 

00:03:28.640 --> 00:03:30.270 align:start position:0%
the engineer responding back to us the
triple<00:03:29.040><c> tick</c><00:03:29.280><c> marks</c><00:03:29.519><c> means</c><00:03:29.840><c> that</c><00:03:29.959><c> this</c><00:03:30.040><c> is</c><00:03:30.120><c> the</c>

00:03:30.270 --> 00:03:30.280 align:start position:0%
triple tick marks means that this is the
 

00:03:30.280 --> 00:03:32.589 align:start position:0%
triple tick marks means that this is the
actual<00:03:30.560><c> code</c><00:03:30.959><c> and</c><00:03:31.200><c> has</c><00:03:31.480><c> the</c><00:03:32.080><c> uh</c><00:03:32.239><c> language</c>

00:03:32.589 --> 00:03:32.599 align:start position:0%
actual code and has the uh language
 

00:03:32.599 --> 00:03:34.270 align:start position:0%
actual code and has the uh language
that's<00:03:32.799><c> going</c><00:03:32.879><c> to</c><00:03:32.920><c> be</c><00:03:33.000><c> using</c><00:03:33.159><c> so</c><00:03:33.319><c> it's</c><00:03:33.519><c> python</c>

00:03:34.270 --> 00:03:34.280 align:start position:0%
that's going to be using so it's python
 

00:03:34.280 --> 00:03:35.910 align:start position:0%
that's going to be using so it's python
it's<00:03:34.400><c> going</c><00:03:34.519><c> to</c><00:03:34.599><c> be</c><00:03:34.720><c> saved</c><00:03:35.040><c> under</c><00:03:35.400><c> this</c><00:03:35.599><c> file</c>

00:03:35.910 --> 00:03:35.920 align:start position:0%
it's going to be saved under this file
 

00:03:35.920 --> 00:03:38.350 align:start position:0%
it's going to be saved under this file
name<00:03:36.480><c> and</c><00:03:36.680><c> then</c><00:03:36.959><c> here</c><00:03:37.200><c> are</c><00:03:37.519><c> the</c><00:03:37.760><c> two</c><00:03:38.000><c> methods</c>

00:03:38.350 --> 00:03:38.360 align:start position:0%
name and then here are the two methods
 

00:03:38.360 --> 00:03:41.390 align:start position:0%
name and then here are the two methods
with<00:03:38.519><c> the</c><00:03:38.720><c> import</c><00:03:39.720><c> then</c><00:03:40.159><c> the</c><00:03:40.319><c> user</c><00:03:40.680><c> agent</c><00:03:41.159><c> is</c>

00:03:41.390 --> 00:03:41.400 align:start position:0%
with the import then the user agent is
 

00:03:41.400 --> 00:03:43.190 align:start position:0%
with the import then the user agent is
actually<00:03:41.760><c> going</c><00:03:41.959><c> to</c><00:03:42.200><c> try</c><00:03:42.360><c> to</c><00:03:42.599><c> execute</c><00:03:43.040><c> this</c>

00:03:43.190 --> 00:03:43.200 align:start position:0%
actually going to try to execute this
 

00:03:43.200 --> 00:03:44.630 align:start position:0%
actually going to try to execute this
code<00:03:43.519><c> and</c><00:03:43.640><c> then</c><00:03:43.720><c> we</c><00:03:43.799><c> got</c><00:03:43.879><c> an</c><00:03:43.959><c> exit</c><00:03:44.239><c> code</c><00:03:44.439><c> of</c>

00:03:44.630 --> 00:03:44.640 align:start position:0%
code and then we got an exit code of
 

00:03:44.640 --> 00:03:46.710 align:start position:0%
code and then we got an exit code of
zero<00:03:45.280><c> if</c><00:03:45.400><c> it</c><00:03:45.480><c> was</c><00:03:45.760><c> exective</c><00:03:46.200><c> one</c><00:03:46.360><c> meaning</c><00:03:46.599><c> that</c>

00:03:46.710 --> 00:03:46.720 align:start position:0%
zero if it was exective one meaning that
 

00:03:46.720 --> 00:03:47.910 align:start position:0%
zero if it was exective one meaning that
there<00:03:46.799><c> was</c><00:03:46.920><c> some</c><00:03:47.120><c> error</c><00:03:47.439><c> and</c><00:03:47.519><c> then</c><00:03:47.640><c> it</c><00:03:47.720><c> would</c>

00:03:47.910 --> 00:03:47.920 align:start position:0%
there was some error and then it would
 

00:03:47.920 --> 00:03:49.869 align:start position:0%
there was some error and then it would
try<00:03:48.080><c> to</c><00:03:48.319><c> fix</c><00:03:48.680><c> itself</c><00:03:49.120><c> but</c><00:03:49.360><c> we</c><00:03:49.439><c> didn't</c><00:03:49.640><c> get</c><00:03:49.760><c> any</c>

00:03:49.869 --> 00:03:49.879 align:start position:0%
try to fix itself but we didn't get any
 

00:03:49.879 --> 00:03:51.949 align:start position:0%
try to fix itself but we didn't get any
errors<00:03:50.200><c> this</c><00:03:50.360><c> time</c><00:03:50.959><c> and</c><00:03:51.120><c> then</c><00:03:51.319><c> the</c><00:03:51.439><c> engineer</c>

00:03:51.949 --> 00:03:51.959 align:start position:0%
errors this time and then the engineer
 

00:03:51.959 --> 00:03:53.589 align:start position:0%
errors this time and then the engineer
said<00:03:52.159><c> to</c><00:03:52.280><c> the</c><00:03:52.400><c> user</c><00:03:52.799><c> because</c><00:03:53.120><c> it</c><00:03:53.239><c> thinks</c><00:03:53.439><c> it's</c>

00:03:53.589 --> 00:03:53.599 align:start position:0%
said to the user because it thinks it's
 

00:03:53.599 --> 00:03:56.030 align:start position:0%
said to the user because it thinks it's
done<00:03:53.879><c> it</c><00:03:54.000><c> says</c><00:03:54.400><c> terminate</c><00:03:55.400><c> and</c><00:03:55.640><c> because</c><00:03:55.920><c> we</c>

00:03:56.030 --> 00:03:56.040 align:start position:0%
done it says terminate and because we
 

00:03:56.040 --> 00:03:57.550 align:start position:0%
done it says terminate and because we
have<00:03:56.120><c> it</c><00:03:56.239><c> set</c><00:03:56.439><c> to</c><00:03:56.599><c> terminate</c><00:03:57.159><c> this</c><00:03:57.239><c> is</c><00:03:57.400><c> where</c>

00:03:57.550 --> 00:03:57.560 align:start position:0%
have it set to terminate this is where
 

00:03:57.560 --> 00:03:59.309 align:start position:0%
have it set to terminate this is where
we<00:03:57.640><c> can</c><00:03:57.799><c> give</c><00:03:58.079><c> feedback</c><00:03:58.840><c> well</c><00:03:58.959><c> I</c><00:03:59.040><c> don't</c><00:03:59.159><c> really</c>

00:03:59.309 --> 00:03:59.319 align:start position:0%
we can give feedback well I don't really
 

00:03:59.319 --> 00:04:01.270 align:start position:0%
we can give feedback well I don't really
need<00:03:59.519><c> any</c><00:03:59.959><c> feedback</c><00:04:00.640><c> so</c><00:04:00.799><c> what's</c><00:04:00.959><c> going</c><00:04:01.079><c> to</c>

00:04:01.270 --> 00:04:01.280 align:start position:0%
need any feedback so what's going to
 

00:04:01.280 --> 00:04:04.110 align:start position:0%
need any feedback so what's going to
happen<00:04:01.680><c> is</c><00:04:02.239><c> I'm</c><00:04:02.400><c> going</c><00:04:02.519><c> to</c><00:04:02.760><c> hit</c><00:04:03.040><c> exit</c><00:04:03.640><c> because</c>

00:04:04.110 --> 00:04:04.120 align:start position:0%
happen is I'm going to hit exit because
 

00:04:04.120 --> 00:04:05.309 align:start position:0%
happen is I'm going to hit exit because
we<00:04:04.239><c> are</c>

00:04:05.309 --> 00:04:05.319 align:start position:0%
we are
 

00:04:05.319 --> 00:04:08.309 align:start position:0%
we are
done<00:04:06.319><c> and</c><00:04:06.439><c> whenever</c><00:04:06.720><c> we</c><00:04:06.840><c> hit</c><00:04:07.040><c> exit</c><00:04:07.680><c> now</c><00:04:07.920><c> we</c><00:04:08.040><c> are</c>

00:04:08.309 --> 00:04:08.319 align:start position:0%
done and whenever we hit exit now we are
 

00:04:08.319 --> 00:04:11.910 align:start position:0%
done and whenever we hit exit now we are
done<00:04:09.040><c> and</c><00:04:09.280><c> it</c><00:04:09.680><c> saves</c><00:04:10.439><c> that</c><00:04:10.720><c> file</c><00:04:11.159><c> under</c><00:04:11.720><c> the</c>

00:04:11.910 --> 00:04:11.920 align:start position:0%
done and it saves that file under the
 

00:04:11.920 --> 00:04:13.670 align:start position:0%
done and it saves that file under the
directory<00:04:12.360><c> that</c><00:04:12.480><c> we</c><00:04:12.560><c> told</c><00:04:12.760><c> it</c><00:04:12.920><c> to</c><00:04:13.360><c> now</c><00:04:13.480><c> I</c><00:04:13.560><c> had</c>

00:04:13.670 --> 00:04:13.680 align:start position:0%
directory that we told it to now I had
 

00:04:13.680 --> 00:04:15.390 align:start position:0%
directory that we told it to now I had
just<00:04:13.760><c> renamed</c><00:04:14.120><c> it</c><00:04:14.239><c> the</c><00:04:14.360><c> coding</c><00:04:14.920><c> instead</c><00:04:15.159><c> of</c>

00:04:15.390 --> 00:04:15.400 align:start position:0%
just renamed it the coding instead of
 

00:04:15.400 --> 00:04:17.349 align:start position:0%
just renamed it the coding instead of
code<00:04:16.040><c> but</c><00:04:16.199><c> it</c><00:04:16.320><c> does</c><00:04:16.479><c> the</c><00:04:16.600><c> same</c><00:04:16.799><c> thing</c><00:04:17.000><c> so</c><00:04:17.120><c> if</c><00:04:17.199><c> we</c>

00:04:17.349 --> 00:04:17.359 align:start position:0%
code but it does the same thing so if we
 

00:04:17.359 --> 00:04:20.069 align:start position:0%
code but it does the same thing so if we
open<00:04:17.639><c> this</c><00:04:17.880><c> up</c><00:04:18.759><c> here</c><00:04:19.079><c> is</c><00:04:19.320><c> the</c><00:04:19.479><c> code</c><00:04:19.799><c> that</c><00:04:19.919><c> it</c>

00:04:20.069 --> 00:04:20.079 align:start position:0%
open this up here is the code that it
 

00:04:20.079 --> 00:04:22.270 align:start position:0%
open this up here is the code that it
gave<00:04:20.280><c> us</c><00:04:21.000><c> this</c><00:04:21.160><c> was</c><00:04:21.280><c> a</c><00:04:21.400><c> good</c><00:04:21.600><c> introduction</c><00:04:22.079><c> to</c>

00:04:22.270 --> 00:04:22.280 align:start position:0%
gave us this was a good introduction to
 

00:04:22.280 --> 00:04:24.310 align:start position:0%
gave us this was a good introduction to
coding<00:04:22.600><c> your</c><00:04:22.720><c> first</c><00:04:23.000><c> AI</c><00:04:23.320><c> agents</c><00:04:23.880><c> yes</c><00:04:24.000><c> it</c><00:04:24.120><c> was</c>

00:04:24.310 --> 00:04:24.320 align:start position:0%
coding your first AI agents yes it was
 

00:04:24.320 --> 00:04:26.430 align:start position:0%
coding your first AI agents yes it was
simple<00:04:24.840><c> but</c><00:04:25.000><c> you</c><00:04:25.120><c> got</c><00:04:25.360><c> the</c><00:04:25.639><c> idea</c><00:04:25.919><c> and</c>

00:04:26.430 --> 00:04:26.440 align:start position:0%
simple but you got the idea and
 

00:04:26.440 --> 00:04:28.830 align:start position:0%
simple but you got the idea and
understanding<00:04:27.040><c> of</c><00:04:27.320><c> how</c><00:04:27.560><c> it</c><00:04:27.680><c> can</c><00:04:27.919><c> work</c><00:04:28.600><c> now</c>

00:04:28.830 --> 00:04:28.840 align:start position:0%
understanding of how it can work now
 

00:04:28.840 --> 00:04:31.390 align:start position:0%
understanding of how it can work now
here<00:04:29.039><c> I</c><00:04:29.160><c> have</c><00:04:29.320><c> a</c><00:04:29.680><c> beginers</c><00:04:30.039><c> course</c><00:04:30.360><c> on</c><00:04:30.560><c> autogen</c>

00:04:31.390 --> 00:04:31.400 align:start position:0%
here I have a beginers course on autogen
 

00:04:31.400 --> 00:04:32.629 align:start position:0%
here I have a beginers course on autogen
that<00:04:31.520><c> you</c><00:04:31.639><c> can</c><00:04:31.880><c> go</c><00:04:32.080><c> through</c><00:04:32.280><c> and</c><00:04:32.400><c> you</c><00:04:32.479><c> can</c>

00:04:32.629 --> 00:04:32.639 align:start position:0%
that you can go through and you can
 

00:04:32.639 --> 00:04:34.629 align:start position:0%
that you can go through and you can
learn<00:04:33.000><c> so</c><00:04:33.280><c> much</c><00:04:33.520><c> more</c><00:04:33.880><c> and</c><00:04:34.039><c> a</c><00:04:34.120><c> little</c><00:04:34.240><c> bit</c><00:04:34.400><c> more</c>

00:04:34.629 --> 00:04:34.639 align:start position:0%
learn so much more and a little bit more
 

00:04:34.639 --> 00:04:36.990 align:start position:0%
learn so much more and a little bit more
in<00:04:34.880><c> depth</c><00:04:35.240><c> of</c><00:04:35.520><c> how</c><00:04:35.759><c> all</c><00:04:35.960><c> the</c><00:04:36.160><c> things</c><00:04:36.440><c> work</c><00:04:36.840><c> like</c>

00:04:36.990 --> 00:04:37.000 align:start position:0%
in depth of how all the things work like
 

00:04:37.000 --> 00:04:38.670 align:start position:0%
in depth of how all the things work like
And<00:04:37.120><c> subscribe</c><00:04:37.479><c> to</c><00:04:37.560><c> my</c><00:04:37.759><c> channel</c><00:04:38.240><c> I</c><00:04:38.360><c> also</c><00:04:38.560><c> have</c>

00:04:38.670 --> 00:04:38.680 align:start position:0%
And subscribe to my channel I also have
 

00:04:38.680 --> 00:04:40.029 align:start position:0%
And subscribe to my channel I also have
a<00:04:38.800><c> free</c><00:04:38.960><c> newsletter</c><00:04:39.400><c> that</c><00:04:39.479><c> come</c><00:04:39.639><c> out</c><00:04:39.800><c> with</c>

00:04:40.029 --> 00:04:40.039 align:start position:0%
a free newsletter that come out with
 

00:04:40.039 --> 00:04:41.749 align:start position:0%
a free newsletter that come out with
every<00:04:40.320><c> Sunday</c><00:04:40.680><c> at</c><00:04:40.840><c> noon</c><00:04:41.400><c> down</c><00:04:41.560><c> in</c><00:04:41.639><c> the</c>

00:04:41.749 --> 00:04:41.759 align:start position:0%
every Sunday at noon down in the
 

00:04:41.759 --> 00:04:43.629 align:start position:0%
every Sunday at noon down in the
description<00:04:42.440><c> and</c><00:04:42.560><c> if</c><00:04:42.639><c> I</c><00:04:42.720><c> didn't</c><00:04:42.919><c> mention</c><00:04:43.440><c> it's</c>

00:04:43.629 --> 00:04:43.639 align:start position:0%
description and if I didn't mention it's
 

00:04:43.639 --> 00:04:45.070 align:start position:0%
description and if I didn't mention it's
free<00:04:43.919><c> thank</c><00:04:44.080><c> you</c><00:04:44.160><c> for</c><00:04:44.320><c> watching</c><00:04:44.680><c> I'll</c><00:04:44.800><c> see</c><00:04:44.919><c> you</c>

00:04:45.070 --> 00:04:45.080 align:start position:0%
free thank you for watching I'll see you
 

00:04:45.080 --> 00:04:47.479 align:start position:0%
free thank you for watching I'll see you
next<00:04:45.240><c> video</c>

