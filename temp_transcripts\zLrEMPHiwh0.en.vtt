WEBVTT
Kind: captions
Language: en

00:00:00.730 --> 00:00:05.710 align:start position:0%
 
[Music]

00:00:05.710 --> 00:00:05.720 align:start position:0%
 
 

00:00:05.720 --> 00:00:08.230 align:start position:0%
 
hey<00:00:05.920><c> everyone</c><00:00:06.759><c> welcome</c><00:00:07.080><c> to</c><00:00:07.279><c> module</c><00:00:07.600><c> three</c><00:00:08.080><c> of</c>

00:00:08.230 --> 00:00:08.240 align:start position:0%
hey everyone welcome to module three of
 

00:00:08.240 --> 00:00:11.390 align:start position:0%
hey everyone welcome to module three of
our<00:00:08.920><c> course</c><00:00:09.920><c> in</c><00:00:10.120><c> this</c><00:00:10.280><c> module</c><00:00:10.719><c> we</c><00:00:10.920><c> built</c><00:00:11.200><c> a</c>

00:00:11.390 --> 00:00:11.400 align:start position:0%
our course in this module we built a
 

00:00:11.400 --> 00:00:14.749 align:start position:0%
our course in this module we built a
baseline<00:00:12.200><c> llm</c><00:00:12.719><c> based</c><00:00:13.480><c> application</c><00:00:14.480><c> in</c><00:00:14.599><c> the</c>

00:00:14.749 --> 00:00:14.759 align:start position:0%
baseline llm based application in the
 

00:00:14.759 --> 00:00:16.390 align:start position:0%
baseline llm based application in the
previous<00:00:15.080><c> lesson</c><00:00:15.360><c> we</c><00:00:15.519><c> learned</c><00:00:15.799><c> how</c><00:00:16.000><c> to</c><00:00:16.119><c> call</c>

00:00:16.390 --> 00:00:16.400 align:start position:0%
previous lesson we learned how to call
 

00:00:16.400 --> 00:00:19.910 align:start position:0%
previous lesson we learned how to call
an<00:00:16.560><c> llm</c><00:00:17.199><c> through</c><00:00:17.400><c> an</c><00:00:17.840><c> API</c><00:00:18.840><c> it</c><00:00:18.960><c> sounds</c><00:00:19.320><c> simple</c>

00:00:19.910 --> 00:00:19.920 align:start position:0%
an llm through an API it sounds simple
 

00:00:19.920 --> 00:00:22.670 align:start position:0%
an llm through an API it sounds simple
we<00:00:20.080><c> have</c><00:00:20.199><c> a</c><00:00:20.359><c> question</c><00:00:21.080><c> call</c><00:00:21.279><c> an</c><00:00:21.439><c> API</c><00:00:22.160><c> and</c><00:00:22.320><c> get</c><00:00:22.480><c> a</c>

00:00:22.670 --> 00:00:22.680 align:start position:0%
we have a question call an API and get a
 

00:00:22.680 --> 00:00:25.710 align:start position:0%
we have a question call an API and get a
response<00:00:23.680><c> but</c><00:00:23.920><c> often</c><00:00:24.560><c> this</c><00:00:24.760><c> isn't</c>

00:00:25.710 --> 00:00:25.720 align:start position:0%
response but often this isn't
 

00:00:25.720 --> 00:00:28.669 align:start position:0%
response but often this isn't
enough<00:00:26.720><c> consider</c><00:00:27.199><c> this</c><00:00:27.439><c> example</c><00:00:28.199><c> from</c><00:00:28.400><c> our</c>

00:00:28.669 --> 00:00:28.679 align:start position:0%
enough consider this example from our
 

00:00:28.679 --> 00:00:30.230 align:start position:0%
enough consider this example from our
synthetic<00:00:29.160><c> data</c><00:00:29.439><c> set</c><00:00:29.599><c> of</c><00:00:29.720><c> question</c><00:00:30.000><c> questions</c>

00:00:30.230 --> 00:00:30.240 align:start position:0%
synthetic data set of question questions
 

00:00:30.240 --> 00:00:31.150 align:start position:0%
synthetic data set of question questions
and

00:00:31.150 --> 00:00:31.160 align:start position:0%
and
 

00:00:31.160 --> 00:00:33.670 align:start position:0%
and
answers<00:00:32.160><c> we</c><00:00:32.320><c> generated</c><00:00:32.880><c> this</c><00:00:33.000><c> in</c><00:00:33.200><c> previous</c>

00:00:33.670 --> 00:00:33.680 align:start position:0%
answers we generated this in previous
 

00:00:33.680 --> 00:00:36.549 align:start position:0%
answers we generated this in previous
module<00:00:34.680><c> the</c><00:00:34.879><c> question</c><00:00:35.239><c> is</c><00:00:35.800><c> how</c><00:00:35.960><c> can</c><00:00:36.079><c> I</c><00:00:36.200><c> share</c>

00:00:36.549 --> 00:00:36.559 align:start position:0%
module the question is how can I share
 

00:00:36.559 --> 00:00:37.270 align:start position:0%
module the question is how can I share
my

00:00:37.270 --> 00:00:37.280 align:start position:0%
my
 

00:00:37.280 --> 00:00:40.270 align:start position:0%
my
report<00:00:38.280><c> with</c><00:00:38.440><c> my</c><00:00:38.600><c> team</c><00:00:38.800><c> members</c><00:00:39.280><c> in</c><00:00:39.399><c> a</c><00:00:39.559><c> public</c>

00:00:40.270 --> 00:00:40.280 align:start position:0%
report with my team members in a public
 

00:00:40.280 --> 00:00:43.549 align:start position:0%
report with my team members in a public
project<00:00:41.280><c> the</c><00:00:41.559><c> generated</c><00:00:42.160><c> answer</c><00:00:42.920><c> explains</c>

00:00:43.549 --> 00:00:43.559 align:start position:0%
project the generated answer explains
 

00:00:43.559 --> 00:00:45.950 align:start position:0%
project the generated answer explains
how<00:00:43.719><c> to</c><00:00:43.920><c> share</c><00:00:44.600><c> a</c><00:00:44.800><c> report</c><00:00:45.200><c> by</c><00:00:45.320><c> selecting</c><00:00:45.760><c> the</c>

00:00:45.950 --> 00:00:45.960 align:start position:0%
how to share a report by selecting the
 

00:00:45.960 --> 00:00:48.310 align:start position:0%
how to share a report by selecting the
share<00:00:46.199><c> button</c><00:00:47.039><c> on</c><00:00:47.199><c> the</c><00:00:47.399><c> upper</c><00:00:47.719><c> right</c><00:00:47.960><c> corner</c>

00:00:48.310 --> 00:00:48.320 align:start position:0%
share button on the upper right corner
 

00:00:48.320 --> 00:00:51.069 align:start position:0%
share button on the upper right corner
of<00:00:48.440><c> the</c><00:00:48.879><c> report</c><00:00:49.879><c> it</c><00:00:50.039><c> goes</c><00:00:50.239><c> on</c><00:00:50.480><c> describing</c><00:00:50.920><c> the</c>

00:00:51.069 --> 00:00:51.079 align:start position:0%
of the report it goes on describing the
 

00:00:51.079 --> 00:00:52.510 align:start position:0%
of the report it goes on describing the
solution<00:00:51.440><c> in</c>

00:00:52.510 --> 00:00:52.520 align:start position:0%
solution in
 

00:00:52.520 --> 00:00:56.069 align:start position:0%
solution in
detail<00:00:53.520><c> however</c><00:00:54.120><c> if</c><00:00:54.280><c> we</c><00:00:54.440><c> ask</c><00:00:54.760><c> chat</c><00:00:55.079><c> GPT</c><00:00:55.920><c> the</c>

00:00:56.069 --> 00:00:56.079 align:start position:0%
detail however if we ask chat GPT the
 

00:00:56.079 --> 00:00:58.830 align:start position:0%
detail however if we ask chat GPT the
same<00:00:56.359><c> question</c><00:00:57.039><c> we</c><00:00:57.160><c> get</c><00:00:57.320><c> a</c><00:00:57.480><c> very</c><00:00:57.840><c> different</c>

00:00:58.830 --> 00:00:58.840 align:start position:0%
same question we get a very different
 

00:00:58.840 --> 00:01:01.830 align:start position:0%
same question we get a very different
but<00:00:59.039><c> also</c><00:00:59.519><c> specific</c><00:00:59.879><c> specific</c><00:01:00.719><c> answer</c><00:01:01.719><c> it</c>

00:01:01.830 --> 00:01:01.840 align:start position:0%
but also specific specific answer it
 

00:01:01.840 --> 00:01:05.270 align:start position:0%
but also specific specific answer it
tells<00:01:02.120><c> us</c><00:01:02.440><c> to</c><00:01:02.600><c> use</c><00:01:02.800><c> a</c><00:01:02.960><c> 1db</c><00:01:03.480><c> account</c><00:01:04.280><c> login</c>

00:01:05.270 --> 00:01:05.280 align:start position:0%
tells us to use a 1db account login
 

00:01:05.280 --> 00:01:07.390 align:start position:0%
tells us to use a 1db account login
navigate<00:01:05.720><c> to</c><00:01:05.840><c> the</c><00:01:06.040><c> project</c><00:01:06.400><c> page</c><00:01:07.040><c> click</c><00:01:07.280><c> on</c>

00:01:07.390 --> 00:01:07.400 align:start position:0%
navigate to the project page click on
 

00:01:07.400 --> 00:01:10.469 align:start position:0%
navigate to the project page click on
the<00:01:07.520><c> settings</c><00:01:08.000><c> stop</c><00:01:08.680><c> and</c><00:01:08.840><c> scroll</c><00:01:09.240><c> down</c><00:01:09.360><c> to</c>

00:01:10.469 --> 00:01:10.479 align:start position:0%
the settings stop and scroll down to
 

00:01:10.479 --> 00:01:13.710 align:start position:0%
the settings stop and scroll down to
sharing<00:01:11.479><c> that</c><00:01:11.799><c> sounds</c><00:01:12.360><c> accurate</c><00:01:13.360><c> but</c><00:01:13.600><c> it</c>

00:01:13.710 --> 00:01:13.720 align:start position:0%
sharing that sounds accurate but it
 

00:01:13.720 --> 00:01:16.030 align:start position:0%
sharing that sounds accurate but it
doesn't<00:01:14.000><c> work</c><00:01:14.240><c> in</c><00:01:14.479><c> reality</c><00:01:15.320><c> the</c><00:01:15.479><c> model</c>

00:01:16.030 --> 00:01:16.040 align:start position:0%
doesn't work in reality the model
 

00:01:16.040 --> 00:01:19.830 align:start position:0%
doesn't work in reality the model
hallucinates<00:01:16.680><c> an</c><00:01:17.360><c> answer</c><00:01:18.360><c> using</c><00:01:18.920><c> public</c><00:01:19.360><c> apis</c>

00:01:19.830 --> 00:01:19.840 align:start position:0%
hallucinates an answer using public apis
 

00:01:19.840 --> 00:01:22.390 align:start position:0%
hallucinates an answer using public apis
and<00:01:20.040><c> llms</c><00:01:20.640><c> as</c><00:01:20.799><c> knowledge</c><00:01:21.159><c> bases</c><00:01:21.680><c> has</c><00:01:21.840><c> its</c>

00:01:22.390 --> 00:01:22.400 align:start position:0%
and llms as knowledge bases has its
 

00:01:22.400 --> 00:01:24.670 align:start position:0%
and llms as knowledge bases has its
problems<00:01:23.400><c> they</c><00:01:23.560><c> are</c><00:01:23.880><c> trained</c><00:01:24.200><c> up</c><00:01:24.360><c> to</c><00:01:24.479><c> a</c>

00:01:24.670 --> 00:01:24.680 align:start position:0%
problems they are trained up to a
 

00:01:24.680 --> 00:01:27.069 align:start position:0%
problems they are trained up to a
specific<00:01:25.119><c> knowledge</c><00:01:25.600><c> cutoff</c><00:01:26.600><c> knowledge</c>

00:01:27.069 --> 00:01:27.079 align:start position:0%
specific knowledge cutoff knowledge
 

00:01:27.079 --> 00:01:29.429 align:start position:0%
specific knowledge cutoff knowledge
gained<00:01:27.600><c> after</c><00:01:27.880><c> the</c><00:01:28.040><c> cut</c><00:01:28.280><c> off</c><00:01:28.920><c> is</c><00:01:29.119><c> not</c>

00:01:29.429 --> 00:01:29.439 align:start position:0%
gained after the cut off is not
 

00:01:29.439 --> 00:01:31.109 align:start position:0%
gained after the cut off is not
available<00:01:30.040><c> in</c><00:01:30.159><c> the</c>

00:01:31.109 --> 00:01:31.119 align:start position:0%
available in the
 

00:01:31.119 --> 00:01:33.990 align:start position:0%
available in the
model<00:01:32.119><c> for</c><00:01:32.320><c> example</c><00:01:32.720><c> a</c><00:01:32.880><c> support</c><00:01:33.240><c> bot</c><00:01:33.600><c> doesn't</c>

00:01:33.990 --> 00:01:34.000 align:start position:0%
model for example a support bot doesn't
 

00:01:34.000 --> 00:01:36.630 align:start position:0%
model for example a support bot doesn't
know<00:01:34.720><c> about</c><00:01:35.040><c> application</c><00:01:35.600><c> changes</c><00:01:36.240><c> made</c>

00:01:36.630 --> 00:01:36.640 align:start position:0%
know about application changes made
 

00:01:36.640 --> 00:01:38.789 align:start position:0%
know about application changes made
after<00:01:37.159><c> the</c><00:01:37.320><c> cut</c><00:01:37.479><c> of</c>

00:01:38.789 --> 00:01:38.799 align:start position:0%
after the cut of
 

00:01:38.799 --> 00:01:41.469 align:start position:0%
after the cut of
date<00:01:39.799><c> we</c><00:01:39.960><c> also</c><00:01:40.200><c> don't</c><00:01:40.439><c> know</c><00:01:40.759><c> exactly</c><00:01:41.240><c> what</c>

00:01:41.469 --> 00:01:41.479 align:start position:0%
date we also don't know exactly what
 

00:01:41.479 --> 00:01:44.749 align:start position:0%
date we also don't know exactly what
llms<00:01:42.000><c> were</c><00:01:42.200><c> trained</c><00:01:42.600><c> on</c><00:01:43.600><c> training</c><00:01:44.079><c> data</c><00:01:44.560><c> might</c>

00:01:44.749 --> 00:01:44.759 align:start position:0%
llms were trained on training data might
 

00:01:44.759 --> 00:01:47.870 align:start position:0%
llms were trained on training data might
be<00:01:44.960><c> inaccurate</c><00:01:45.960><c> contain</c><00:01:46.360><c> outdated</c><00:01:46.960><c> code</c><00:01:47.640><c> or</c>

00:01:47.870 --> 00:01:47.880 align:start position:0%
be inaccurate contain outdated code or
 

00:01:47.880 --> 00:01:51.230 align:start position:0%
be inaccurate contain outdated code or
be<00:01:48.200><c> unreliable</c><00:01:49.200><c> from</c><00:01:49.399><c> the</c>

00:01:51.230 --> 00:01:51.240 align:start position:0%
be unreliable from the
 

00:01:51.240 --> 00:01:54.270 align:start position:0%
be unreliable from the
start<00:01:52.240><c> hallucinations</c><00:01:53.000><c> can</c><00:01:53.200><c> also</c><00:01:53.439><c> occur</c><00:01:54.079><c> as</c>

00:01:54.270 --> 00:01:54.280 align:start position:0%
start hallucinations can also occur as
 

00:01:54.280 --> 00:01:56.029 align:start position:0%
start hallucinations can also occur as
model<00:01:54.640><c> sample</c><00:01:55.079><c> from</c><00:01:55.320><c> probability</c>

00:01:56.029 --> 00:01:56.039 align:start position:0%
model sample from probability
 

00:01:56.039 --> 00:01:57.670 align:start position:0%
model sample from probability
distributions<00:01:57.039><c> producing</c>

00:01:57.670 --> 00:01:57.680 align:start position:0%
distributions producing
 

00:01:57.680 --> 00:02:00.870 align:start position:0%
distributions producing
non-deterministic<00:01:58.840><c> outputs</c><00:02:00.280><c> this</c><00:02:00.479><c> output</c>

00:02:00.870 --> 00:02:00.880 align:start position:0%
non-deterministic outputs this output
 

00:02:00.880 --> 00:02:04.230 align:start position:0%
non-deterministic outputs this output
seem<00:02:01.159><c> very</c><00:02:01.399><c> specific</c><00:02:02.200><c> but</c><00:02:02.399><c> can</c><00:02:02.560><c> be</c>

00:02:04.230 --> 00:02:04.240 align:start position:0%
seem very specific but can be
 

00:02:04.240 --> 00:02:07.350 align:start position:0%
seem very specific but can be
inaccurate<00:02:05.240><c> updating</c><00:02:06.000><c> retraining</c><00:02:06.799><c> or</c>

00:02:07.350 --> 00:02:07.360 align:start position:0%
inaccurate updating retraining or
 

00:02:07.360 --> 00:02:10.229 align:start position:0%
inaccurate updating retraining or
fine-tuning<00:02:08.360><c> llms</c><00:02:08.959><c> is</c><00:02:09.200><c> expensive</c><00:02:09.879><c> so</c><00:02:10.080><c> we</c>

00:02:10.229 --> 00:02:10.239 align:start position:0%
fine-tuning llms is expensive so we
 

00:02:10.239 --> 00:02:14.030 align:start position:0%
fine-tuning llms is expensive so we
often<00:02:10.560><c> use</c><00:02:10.840><c> them</c><00:02:11.160><c> as</c><00:02:11.599><c> is</c><00:02:12.599><c> one</c><00:02:12.879><c> solution</c><00:02:13.720><c> is</c>

00:02:14.030 --> 00:02:14.040 align:start position:0%
often use them as is one solution is
 

00:02:14.040 --> 00:02:15.910 align:start position:0%
often use them as is one solution is
including<00:02:14.680><c> relevant</c><00:02:15.160><c> knowledge</c><00:02:15.599><c> in</c><00:02:15.800><c> the</c>

00:02:15.910 --> 00:02:15.920 align:start position:0%
including relevant knowledge in the
 

00:02:15.920 --> 00:02:18.910 align:start position:0%
including relevant knowledge in the
query<00:02:16.239><c> prompt</c><00:02:16.840><c> along</c><00:02:17.160><c> with</c><00:02:17.319><c> a</c><00:02:17.760><c> question</c><00:02:18.760><c> but</c>

00:02:18.910 --> 00:02:18.920 align:start position:0%
query prompt along with a question but
 

00:02:18.920 --> 00:02:20.910 align:start position:0%
query prompt along with a question but
if<00:02:19.040><c> we</c><00:02:19.160><c> have</c><00:02:19.280><c> the</c><00:02:19.440><c> knowledge</c><00:02:20.280><c> why</c><00:02:20.400><c> do</c><00:02:20.519><c> we</c><00:02:20.640><c> need</c>

00:02:20.910 --> 00:02:20.920 align:start position:0%
if we have the knowledge why do we need
 

00:02:20.920 --> 00:02:23.589 align:start position:0%
if we have the knowledge why do we need
the<00:02:21.280><c> llm</c><00:02:22.280><c> and</c><00:02:22.440><c> this</c><00:02:22.599><c> brings</c><00:02:22.840><c> us</c><00:02:23.000><c> to</c><00:02:23.120><c> our</c><00:02:23.360><c> next</c>

00:02:23.589 --> 00:02:23.599 align:start position:0%
the llm and this brings us to our next
 

00:02:23.599 --> 00:02:25.270 align:start position:0%
the llm and this brings us to our next
topic<00:02:24.000><c> which</c><00:02:24.120><c> is</c><00:02:24.360><c> the</c><00:02:24.640><c> application</c>

00:02:25.270 --> 00:02:25.280 align:start position:0%
topic which is the application
 

00:02:25.280 --> 00:02:28.750 align:start position:0%
topic which is the application
architecture<00:02:26.280><c> of</c><00:02:26.480><c> our</c>

00:02:28.750 --> 00:02:28.760 align:start position:0%
architecture of our
 

00:02:28.760 --> 00:02:31.760 align:start position:0%
architecture of our
app

