WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:02.310 align:start position:0%
 
you<00:00:00.160><c> can</c><00:00:00.320><c> use</c><00:00:00.560><c> the</c><00:00:00.719><c> web</c><00:00:01.000><c> UI</c><00:00:01.400><c> to</c><00:00:01.599><c> visualize</c><00:00:02.159><c> the</c>

00:00:02.310 --> 00:00:02.320 align:start position:0%
you can use the web UI to visualize the
 

00:00:02.320 --> 00:00:04.870 align:start position:0%
you can use the web UI to visualize the
relationships<00:00:03.080><c> hidden</c><00:00:03.360><c> in</c><00:00:03.480><c> your</c><00:00:03.719><c> data</c><00:00:04.040><c> set</c>

00:00:04.870 --> 00:00:04.880 align:start position:0%
relationships hidden in your data set
 

00:00:04.880 --> 00:00:07.030 align:start position:0%
relationships hidden in your data set
first<00:00:05.160><c> you</c><00:00:05.279><c> need</c><00:00:05.440><c> to</c><00:00:05.640><c> import</c><00:00:06.000><c> a</c><00:00:06.240><c> practice</c><00:00:06.720><c> data</c>

00:00:07.030 --> 00:00:07.040 align:start position:0%
first you need to import a practice data
 

00:00:07.040 --> 00:00:09.470 align:start position:0%
first you need to import a practice data
set<00:00:07.799><c> here</c><00:00:08.080><c> we</c><00:00:08.200><c> are</c><00:00:08.320><c> going</c><00:00:08.519><c> to</c><00:00:08.719><c> import</c><00:00:09.080><c> the</c><00:00:09.200><c> mid</c>

00:00:09.470 --> 00:00:09.480 align:start position:0%
set here we are going to import the mid
 

00:00:09.480 --> 00:00:14.990 align:start position:0%
set here we are going to import the mid
Journey<00:00:09.840><c> data</c><00:00:10.120><c> set</c><00:00:10.320><c> to</c><00:00:10.480><c> a</c>

00:00:14.990 --> 00:00:15.000 align:start position:0%
 
 

00:00:15.000 --> 00:00:17.349 align:start position:0%
 
collection<00:00:16.000><c> from</c><00:00:16.199><c> the</c><00:00:16.359><c> collections</c><00:00:16.960><c> tab</c><00:00:17.240><c> you</c>

00:00:17.349 --> 00:00:17.359 align:start position:0%
collection from the collections tab you
 

00:00:17.359 --> 00:00:19.189 align:start position:0%
collection from the collections tab you
can<00:00:17.560><c> inspect</c><00:00:17.960><c> your</c><00:00:18.160><c> new</c><00:00:18.439><c> collection</c><00:00:18.960><c> and</c><00:00:19.080><c> the</c>

00:00:19.189 --> 00:00:19.199 align:start position:0%
can inspect your new collection and the
 

00:00:19.199 --> 00:00:25.870 align:start position:0%
can inspect your new collection and the
mid<00:00:19.439><c> Journey</c><00:00:19.840><c> images</c><00:00:20.240><c> as</c><00:00:20.480><c> data</c>

00:00:25.870 --> 00:00:25.880 align:start position:0%
 
 

00:00:25.880 --> 00:00:28.710 align:start position:0%
 
points<00:00:26.880><c> if</c><00:00:27.000><c> you</c><00:00:27.160><c> go</c><00:00:27.279><c> to</c><00:00:27.439><c> the</c><00:00:27.640><c> graph</c><00:00:28.000><c> tab</c><00:00:28.599><c> you</c>

00:00:28.710 --> 00:00:28.720 align:start position:0%
points if you go to the graph tab you
 

00:00:28.720 --> 00:00:30.710 align:start position:0%
points if you go to the graph tab you
can<00:00:28.920><c> visualize</c><00:00:29.400><c> a</c><00:00:29.560><c> sample</c><00:00:30.039><c> of</c><00:00:30.199><c> these</c><00:00:30.359><c> points</c>

00:00:30.710 --> 00:00:30.720 align:start position:0%
can visualize a sample of these points
 

00:00:30.720 --> 00:00:32.950 align:start position:0%
can visualize a sample of these points
and<00:00:30.880><c> see</c><00:00:31.119><c> how</c><00:00:31.279><c> they</c><00:00:31.480><c> relate</c><00:00:31.800><c> to</c><00:00:32.000><c> each</c><00:00:32.200><c> other</c>

00:00:32.950 --> 00:00:32.960 align:start position:0%
and see how they relate to each other
 

00:00:32.960 --> 00:00:35.389 align:start position:0%
and see how they relate to each other
let's<00:00:33.239><c> generate</c><00:00:33.640><c> a</c><00:00:33.800><c> sample</c><00:00:34.160><c> of</c><00:00:34.360><c> 900</c><00:00:34.960><c> images</c>

00:00:35.389 --> 00:00:35.399 align:start position:0%
let's generate a sample of 900 images
 

00:00:35.399 --> 00:00:42.910 align:start position:0%
let's generate a sample of 900 images
and<00:00:35.600><c> connect</c><00:00:35.960><c> them</c><00:00:36.160><c> using</c><00:00:36.399><c> a</c><00:00:36.520><c> tree</c>

00:00:42.910 --> 00:00:42.920 align:start position:0%
 
 

00:00:42.920 --> 00:00:45.510 align:start position:0%
 
graph<00:00:43.920><c> the</c><00:00:44.039><c> tree</c><00:00:44.320><c> is</c><00:00:44.520><c> flexible</c><00:00:45.120><c> and</c><00:00:45.239><c> you</c><00:00:45.360><c> can</c>

00:00:45.510 --> 00:00:45.520 align:start position:0%
graph the tree is flexible and you can
 

00:00:45.520 --> 00:00:47.430 align:start position:0%
graph the tree is flexible and you can
zoom<00:00:45.840><c> and</c><00:00:46.039><c> stretch</c><00:00:46.320><c> it</c><00:00:46.440><c> out</c><00:00:46.640><c> to</c><00:00:46.800><c> untangle</c>

00:00:47.430 --> 00:00:47.440 align:start position:0%
zoom and stretch it out to untangle
 

00:00:47.440 --> 00:00:55.510 align:start position:0%
zoom and stretch it out to untangle
relationships<00:00:48.239><c> between</c>

00:00:55.510 --> 00:00:55.520 align:start position:0%
 
 

00:00:55.520 --> 00:00:57.950 align:start position:0%
 
points<00:00:56.520><c> once</c><00:00:56.760><c> the</c><00:00:56.920><c> clusters</c><00:00:57.320><c> of</c><00:00:57.559><c> related</c>

00:00:57.950 --> 00:00:57.960 align:start position:0%
points once the clusters of related
 

00:00:57.960 --> 00:01:00.270 align:start position:0%
points once the clusters of related
points<00:00:58.239><c> are</c><00:00:58.440><c> more</c><00:00:58.680><c> visible</c><00:00:59.519><c> zoom</c><00:00:59.760><c> in</c><00:01:00.120><c> and</c>

00:01:00.270 --> 00:01:00.280 align:start position:0%
points are more visible zoom in and
 

00:01:00.280 --> 00:01:08.190 align:start position:0%
points are more visible zoom in and
check<00:01:00.480><c> out</c><00:01:00.760><c> each</c>

00:01:08.190 --> 00:01:08.200 align:start position:0%
 
 

00:01:08.200 --> 00:01:10.630 align:start position:0%
 
cluster<00:01:09.200><c> here</c><00:01:09.400><c> you</c><00:01:09.520><c> can</c><00:01:09.680><c> see</c><00:01:09.920><c> how</c><00:01:10.080><c> the</c><00:01:10.240><c> related</c>

00:01:10.630 --> 00:01:10.640 align:start position:0%
cluster here you can see how the related
 

00:01:10.640 --> 00:01:13.070 align:start position:0%
cluster here you can see how the related
points<00:01:10.920><c> are</c><00:01:11.119><c> indeed</c><00:01:11.479><c> visually</c><00:01:11.960><c> similar</c><00:01:12.880><c> this</c>

00:01:13.070 --> 00:01:13.080 align:start position:0%
points are indeed visually similar this
 

00:01:13.080 --> 00:01:14.789 align:start position:0%
points are indeed visually similar this
feature<00:01:13.400><c> is</c><00:01:13.640><c> excellent</c><00:01:14.040><c> at</c><00:01:14.200><c> uncovering</c>

00:01:14.789 --> 00:01:14.799 align:start position:0%
feature is excellent at uncovering
 

00:01:14.799 --> 00:01:23.630 align:start position:0%
feature is excellent at uncovering
Hidden<00:01:15.119><c> connections</c><00:01:15.600><c> and</c><00:01:15.799><c> data</c>

00:01:23.630 --> 00:01:23.640 align:start position:0%
 
 

00:01:23.640 --> 00:01:26.510 align:start position:0%
 
sets<00:01:24.640><c> to</c><00:01:24.840><c> try</c><00:01:25.119><c> this</c><00:01:25.320><c> feature</c><00:01:25.680><c> yourself</c><00:01:26.200><c> please</c>

00:01:26.510 --> 00:01:26.520 align:start position:0%
sets to try this feature yourself please
 

00:01:26.520 --> 00:01:27.910 align:start position:0%
sets to try this feature yourself please
check<00:01:26.680><c> out</c><00:01:26.880><c> the</c><00:01:27.040><c> instructions</c><00:01:27.600><c> in</c><00:01:27.720><c> the</c>

00:01:27.910 --> 00:01:27.920 align:start position:0%
check out the instructions in the
 

00:01:27.920 --> 00:01:31.280 align:start position:0%
check out the instructions in the
description<00:01:28.479><c> below</c>

