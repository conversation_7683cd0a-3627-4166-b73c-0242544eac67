WEBVTT
Kind: captions
Language: en

00:00:01.480 --> 00:00:03.429 align:start position:0%
 
new<00:00:01.680><c> versions</c><00:00:02.000><c> of</c><00:00:02.200><c> FF</c><00:00:02.480><c> imp</c><00:00:02.679><c> Peg</c><00:00:02.919><c> and</c><00:00:03.120><c> visual</c>

00:00:03.429 --> 00:00:03.439 align:start position:0%
new versions of FF imp Peg and visual
 

00:00:03.439 --> 00:00:06.950 align:start position:0%
new versions of FF imp Peg and visual
studio<00:00:03.840><c> code</c><00:00:04.440><c> open</c><00:00:04.720><c> AI</c><00:00:05.160><c> gp4</c><00:00:05.920><c> turbo</c><00:00:06.440><c> and</c><00:00:06.640><c> vision</c>

00:00:06.950 --> 00:00:06.960 align:start position:0%
studio code open AI gp4 turbo and vision
 

00:00:06.960 --> 00:00:09.470 align:start position:0%
studio code open AI gp4 turbo and vision
models<00:00:07.359><c> are</c><00:00:07.520><c> now</c><00:00:07.680><c> in</c><00:00:07.839><c> the</c><00:00:08.000><c> API</c><00:00:08.920><c> and</c><00:00:09.080><c> a</c><00:00:09.240><c> pick</c><00:00:09.360><c> of</c>

00:00:09.470 --> 00:00:09.480 align:start position:0%
models are now in the API and a pick of
 

00:00:09.480 --> 00:00:11.190 align:start position:0%
models are now in the API and a pick of
the<00:00:09.599><c> week</c><00:00:09.960><c> that's</c><00:00:10.160><c> worth</c><00:00:10.440><c> leaving</c><00:00:10.759><c> the</c><00:00:10.880><c> Vault</c>

00:00:11.190 --> 00:00:11.200 align:start position:0%
the week that's worth leaving the Vault
 

00:00:11.200 --> 00:00:13.629 align:start position:0%
the week that's worth leaving the Vault
for<00:00:11.840><c> all</c><00:00:12.040><c> that</c><00:00:12.160><c> and</c><00:00:12.320><c> more</c><00:00:12.519><c> on</c><00:00:12.719><c> this</c><00:00:12.880><c> episode</c><00:00:13.280><c> of</c>

00:00:13.629 --> 00:00:13.639 align:start position:0%
for all that and more on this episode of
 

00:00:13.639 --> 00:00:16.340 align:start position:0%
for all that and more on this episode of
the

00:00:16.340 --> 00:00:16.350 align:start position:0%
 
 

00:00:16.350 --> 00:00:18.750 align:start position:0%
 
[Music]

00:00:18.750 --> 00:00:18.760 align:start position:0%
[Music]
 

00:00:18.760 --> 00:00:20.910 align:start position:0%
[Music]
download<00:00:19.760><c> welcome</c><00:00:20.080><c> back</c><00:00:20.199><c> to</c><00:00:20.320><c> another</c><00:00:20.560><c> episode</c>

00:00:20.910 --> 00:00:20.920 align:start position:0%
download welcome back to another episode
 

00:00:20.920 --> 00:00:22.670 align:start position:0%
download welcome back to another episode
of<00:00:21.080><c> the</c><00:00:21.199><c> download</c><00:00:21.760><c> I'm</c><00:00:21.880><c> your</c><00:00:22.039><c> host</c><00:00:22.279><c> Christina</c>

00:00:22.670 --> 00:00:22.680 align:start position:0%
of the download I'm your host Christina
 

00:00:22.680 --> 00:00:24.310 align:start position:0%
of the download I'm your host Christina
Warren<00:00:23.039><c> senior</c><00:00:23.320><c> developer</c><00:00:23.680><c> Advocate</c><00:00:24.080><c> at</c>

00:00:24.310 --> 00:00:24.320 align:start position:0%
Warren senior developer Advocate at
 

00:00:24.320 --> 00:00:25.990 align:start position:0%
Warren senior developer Advocate at
GitHub<00:00:25.039><c> and</c><00:00:25.240><c> this</c><00:00:25.359><c> is</c><00:00:25.480><c> the</c><00:00:25.599><c> show</c><00:00:25.800><c> where</c><00:00:25.880><c> we</c>

00:00:25.990 --> 00:00:26.000 align:start position:0%
GitHub and this is the show where we
 

00:00:26.000 --> 00:00:27.630 align:start position:0%
GitHub and this is the show where we
cover<00:00:26.240><c> the</c><00:00:26.320><c> latest</c><00:00:26.599><c> developer</c><00:00:26.960><c> news</c><00:00:27.240><c> and</c><00:00:27.400><c> open</c>

00:00:27.630 --> 00:00:27.640 align:start position:0%
cover the latest developer news and open
 

00:00:27.640 --> 00:00:29.230 align:start position:0%
cover the latest developer news and open
source<00:00:27.920><c> Projects</c><00:00:28.640><c> please</c><00:00:28.920><c> like</c><00:00:29.039><c> And</c>

00:00:29.230 --> 00:00:29.240 align:start position:0%
source Projects please like And
 

00:00:29.240 --> 00:00:31.630 align:start position:0%
source Projects please like And
subscribe<00:00:30.039><c> so</c><00:00:30.359><c> no</c><00:00:30.560><c> special</c><00:00:30.840><c> shirt</c><00:00:31.199><c> this</c><00:00:31.320><c> week</c>

00:00:31.630 --> 00:00:31.640 align:start position:0%
subscribe so no special shirt this week
 

00:00:31.640 --> 00:00:33.790 align:start position:0%
subscribe so no special shirt this week
but<00:00:31.840><c> I</c><00:00:32.000><c> do</c><00:00:32.279><c> have</c><00:00:32.439><c> a</c><00:00:32.599><c> bandanna</c><00:00:33.200><c> that</c><00:00:33.320><c> we</c><00:00:33.440><c> will</c><00:00:33.600><c> be</c>

00:00:33.790 --> 00:00:33.800 align:start position:0%
but I do have a bandanna that we will be
 

00:00:33.800 --> 00:00:36.030 align:start position:0%
but I do have a bandanna that we will be
talking<00:00:34.160><c> more</c><00:00:34.480><c> about</c><00:00:34.879><c> later</c><00:00:35.520><c> all</c><00:00:35.640><c> right</c><00:00:35.840><c> let's</c>

00:00:36.030 --> 00:00:36.040 align:start position:0%
talking more about later all right let's
 

00:00:36.040 --> 00:00:38.990 align:start position:0%
talking more about later all right let's
get<00:00:36.200><c> into</c><00:00:36.440><c> the</c><00:00:36.640><c> news</c><00:00:37.640><c> so</c><00:00:38.200><c> the</c><00:00:38.399><c> first</c><00:00:38.680><c> thing</c>

00:00:38.990 --> 00:00:39.000 align:start position:0%
get into the news so the first thing
 

00:00:39.000 --> 00:00:40.869 align:start position:0%
get into the news so the first thing
that<00:00:39.120><c> I</c><00:00:39.200><c> want</c><00:00:39.320><c> to</c><00:00:39.440><c> call</c><00:00:39.680><c> out</c><00:00:39.879><c> is</c><00:00:40.039><c> a</c><00:00:40.200><c> blog</c><00:00:40.559><c> post</c>

00:00:40.869 --> 00:00:40.879 align:start position:0%
that I want to call out is a blog post
 

00:00:40.879 --> 00:00:43.150 align:start position:0%
that I want to call out is a blog post
from<00:00:41.000><c> the</c><00:00:41.160><c> GitHub</c><00:00:41.480><c> blog</c><00:00:41.960><c> by</c><00:00:42.160><c> hoger</c><00:00:43.000><c> that</c>

00:00:43.150 --> 00:00:43.160 align:start position:0%
from the GitHub blog by hoger that
 

00:00:43.160 --> 00:00:45.310 align:start position:0%
from the GitHub blog by hoger that
discusses<00:00:43.680><c> how</c><00:00:43.920><c> GitHub</c><00:00:44.280><c> Engineers</c><00:00:44.960><c> uh</c><00:00:45.079><c> use</c>

00:00:45.310 --> 00:00:45.320 align:start position:0%
discusses how GitHub Engineers uh use
 

00:00:45.320 --> 00:00:47.430 align:start position:0%
discusses how GitHub Engineers uh use
GitHub<00:00:45.640><c> co-pilot</c><00:00:46.320><c> and</c><00:00:46.800><c> this</c><00:00:46.920><c> is</c><00:00:47.120><c> actually</c><00:00:47.320><c> one</c>

00:00:47.430 --> 00:00:47.440 align:start position:0%
GitHub co-pilot and this is actually one
 

00:00:47.440 --> 00:00:48.869 align:start position:0%
GitHub co-pilot and this is actually one
of<00:00:47.559><c> the</c><00:00:47.680><c> questions</c><00:00:48.000><c> that</c><00:00:48.160><c> I'm</c><00:00:48.320><c> asked</c><00:00:48.600><c> about</c>

00:00:48.869 --> 00:00:48.879 align:start position:0%
of the questions that I'm asked about
 

00:00:48.879 --> 00:00:50.869 align:start position:0%
of the questions that I'm asked about
most<00:00:49.079><c> frequently</c><00:00:49.600><c> like</c><00:00:49.920><c> how</c><00:00:50.120><c> we</c><00:00:50.239><c> use</c><00:00:50.520><c> GitHub</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
most frequently like how we use GitHub
 

00:00:50.879 --> 00:00:52.830 align:start position:0%
most frequently like how we use GitHub
co-pilot<00:00:51.399><c> internally</c><00:00:52.280><c> and</c><00:00:52.399><c> I'm</c><00:00:52.520><c> thrilled</c>

00:00:52.830 --> 00:00:52.840 align:start position:0%
co-pilot internally and I'm thrilled
 

00:00:52.840 --> 00:00:54.990 align:start position:0%
co-pilot internally and I'm thrilled
that<00:00:52.920><c> I</c><00:00:53.000><c> can</c><00:00:53.120><c> now</c><00:00:53.320><c> point</c><00:00:53.559><c> to</c><00:00:53.760><c> holger's</c><00:00:54.320><c> post</c>

00:00:54.990 --> 00:00:55.000 align:start position:0%
that I can now point to holger's post
 

00:00:55.000 --> 00:00:56.910 align:start position:0%
that I can now point to holger's post
and<00:00:55.239><c> I</c><00:00:55.399><c> particularly</c><00:00:55.920><c> love</c><00:00:56.280><c> his</c><00:00:56.440><c> remarks</c>

00:00:56.910 --> 00:00:56.920 align:start position:0%
and I particularly love his remarks
 

00:00:56.920 --> 00:00:59.470 align:start position:0%
and I particularly love his remarks
about<00:00:57.359><c> semi-automating</c><00:00:58.199><c> repetitive</c><00:00:58.719><c> tasks</c>

00:00:59.470 --> 00:00:59.480 align:start position:0%
about semi-automating repetitive tasks
 

00:00:59.480 --> 00:01:00.709 align:start position:0%
about semi-automating repetitive tasks
because<00:00:59.719><c> this</c><00:01:00.039><c> is</c><00:01:00.120><c> something</c><00:01:00.280><c> that</c><00:01:00.399><c> I</c><00:01:00.519><c> love</c>

00:01:00.709 --> 00:01:00.719 align:start position:0%
because this is something that I love
 

00:01:00.719 --> 00:01:03.029 align:start position:0%
because this is something that I love
doing<00:01:01.039><c> too</c><00:01:01.280><c> with</c><00:01:01.440><c> GitHub</c><00:01:01.760><c> copilot</c><00:01:02.719><c> and</c><00:01:02.920><c> the</c>

00:01:03.029 --> 00:01:03.039 align:start position:0%
doing too with GitHub copilot and the
 

00:01:03.039 --> 00:01:05.149 align:start position:0%
doing too with GitHub copilot and the
blog<00:01:03.320><c> post</c><00:01:03.600><c> also</c><00:01:03.840><c> includes</c><00:01:04.239><c> a</c><00:01:04.400><c> demo</c><00:01:04.839><c> from</c><00:01:05.040><c> one</c>

00:01:05.149 --> 00:01:05.159 align:start position:0%
blog post also includes a demo from one
 

00:01:05.159 --> 00:01:07.510 align:start position:0%
blog post also includes a demo from one
of<00:01:05.280><c> our</c><00:01:05.400><c> Engineers</c><00:01:05.960><c> who</c><00:01:06.119><c> used</c><00:01:06.520><c> co-pilot</c><00:01:07.360><c> to</c>

00:01:07.510 --> 00:01:07.520 align:start position:0%
of our Engineers who used co-pilot to
 

00:01:07.520 --> 00:01:09.469 align:start position:0%
of our Engineers who used co-pilot to
figure<00:01:07.759><c> out</c><00:01:08.119><c> a</c><00:01:08.320><c> task</c><00:01:08.680><c> in</c><00:01:08.840><c> an</c><00:01:09.000><c> unfamiliar</c>

00:01:09.469 --> 00:01:09.479 align:start position:0%
figure out a task in an unfamiliar
 

00:01:09.479 --> 00:01:11.390 align:start position:0%
figure out a task in an unfamiliar
programming<00:01:09.880><c> language</c><00:01:10.439><c> and</c><00:01:10.640><c> he</c><00:01:10.880><c> documented</c>

00:01:11.390 --> 00:01:11.400 align:start position:0%
programming language and he documented
 

00:01:11.400 --> 00:01:13.510 align:start position:0%
programming language and he documented
the<00:01:11.520><c> whole</c><00:01:11.720><c> thing</c><00:01:12.400><c> John</c><00:01:12.960><c> the</c><00:01:13.080><c> engineer</c><00:01:13.400><c> who</c>

00:01:13.510 --> 00:01:13.520 align:start position:0%
the whole thing John the engineer who
 

00:01:13.520 --> 00:01:15.109 align:start position:0%
the whole thing John the engineer who
did<00:01:13.759><c> that</c><00:01:14.040><c> thank</c><00:01:14.200><c> you</c><00:01:14.320><c> so</c><00:01:14.439><c> much</c><00:01:14.560><c> for</c><00:01:14.720><c> capturing</c>

00:01:15.109 --> 00:01:15.119 align:start position:0%
did that thank you so much for capturing
 

00:01:15.119 --> 00:01:17.310 align:start position:0%
did that thank you so much for capturing
all<00:01:15.240><c> of</c><00:01:15.400><c> that</c><00:01:15.880><c> and</c><00:01:16.040><c> so</c><00:01:16.240><c> I've</c><00:01:16.400><c> got</c><00:01:16.560><c> a</c><00:01:16.720><c> full</c><00:01:16.960><c> link</c>

00:01:17.310 --> 00:01:17.320 align:start position:0%
all of that and so I've got a full link
 

00:01:17.320 --> 00:01:19.109 align:start position:0%
all of that and so I've got a full link
to<00:01:17.479><c> the</c><00:01:17.600><c> blog</c><00:01:17.920><c> post</c><00:01:18.240><c> in</c><00:01:18.360><c> the</c><00:01:18.479><c> links</c><00:01:18.840><c> and</c><00:01:19.000><c> the</c>

00:01:19.109 --> 00:01:19.119 align:start position:0%
to the blog post in the links and the
 

00:01:19.119 --> 00:01:21.390 align:start position:0%
to the blog post in the links and the
description<00:01:19.560><c> down</c><00:01:19.759><c> below</c><00:01:20.680><c> and</c><00:01:20.920><c> speaking</c><00:01:21.240><c> of</c>

00:01:21.390 --> 00:01:21.400 align:start position:0%
description down below and speaking of
 

00:01:21.400 --> 00:01:23.270 align:start position:0%
description down below and speaking of
GitHub<00:01:21.720><c> co-pilot</c><00:01:22.240><c> there</c><00:01:22.360><c> is</c><00:01:22.520><c> a</c><00:01:22.759><c> new</c>

00:01:23.270 --> 00:01:23.280 align:start position:0%
GitHub co-pilot there is a new
 

00:01:23.280 --> 00:01:26.030 align:start position:0%
GitHub co-pilot there is a new
experience<00:01:24.040><c> in</c><00:01:24.360><c> Visual</c><00:01:24.680><c> Studio</c><00:01:25.439><c> it's</c><00:01:25.640><c> now</c><00:01:25.840><c> in</c>

00:01:26.030 --> 00:01:26.040 align:start position:0%
experience in Visual Studio it's now in
 

00:01:26.040 --> 00:01:28.310 align:start position:0%
experience in Visual Studio it's now in
preview<00:01:26.400><c> 3</c><00:01:27.240><c> the</c><00:01:27.400><c> big</c><00:01:27.600><c> thing</c><00:01:27.880><c> is</c><00:01:27.960><c> that</c><00:01:28.119><c> this</c><00:01:28.240><c> is</c>

00:01:28.310 --> 00:01:28.320 align:start position:0%
preview 3 the big thing is that this is
 

00:01:28.320 --> 00:01:30.510 align:start position:0%
preview 3 the big thing is that this is
going<00:01:28.479><c> to</c><00:01:28.640><c> unify</c><00:01:29.240><c> the</c><00:01:29.400><c> co-pilot</c><00:01:29.960><c> and</c><00:01:30.079><c> co-pilot</c>

00:01:30.510 --> 00:01:30.520 align:start position:0%
going to unify the co-pilot and co-pilot
 

00:01:30.520 --> 00:01:32.789 align:start position:0%
going to unify the co-pilot and co-pilot
chat<00:01:30.799><c> experiences</c><00:01:31.479><c> into</c><00:01:31.720><c> one</c><00:01:32.000><c> place</c><00:01:32.600><c> there's</c>

00:01:32.789 --> 00:01:32.799 align:start position:0%
chat experiences into one place there's
 

00:01:32.799 --> 00:01:34.510 align:start position:0%
chat experiences into one place there's
some<00:01:32.920><c> other</c><00:01:33.119><c> changes</c><00:01:33.520><c> too</c><00:01:33.960><c> and</c><00:01:34.079><c> you</c><00:01:34.159><c> can</c><00:01:34.280><c> see</c><00:01:34.399><c> a</c>

00:01:34.510 --> 00:01:34.520 align:start position:0%
some other changes too and you can see a
 

00:01:34.520 --> 00:01:36.630 align:start position:0%
some other changes too and you can see a
preview<00:01:34.840><c> of</c><00:01:34.920><c> it</c><00:01:35.040><c> in</c><00:01:35.240><c> action</c><00:01:35.960><c> at</c><00:01:36.119><c> the</c><00:01:36.280><c> link</c><00:01:36.520><c> in</c>

00:01:36.630 --> 00:01:36.640 align:start position:0%
preview of it in action at the link in
 

00:01:36.640 --> 00:01:38.510 align:start position:0%
preview of it in action at the link in
the<00:01:36.720><c> show</c><00:01:36.920><c> notes</c><00:01:37.119><c> in</c><00:01:37.240><c> the</c><00:01:37.439><c> description</c><00:01:38.439><c> and</c>

00:01:38.510 --> 00:01:38.520 align:start position:0%
the show notes in the description and
 

00:01:38.520 --> 00:01:40.389 align:start position:0%
the show notes in the description and
you<00:01:38.640><c> can</c><00:01:38.759><c> try</c><00:01:38.920><c> it</c><00:01:39.040><c> out</c><00:01:39.320><c> in</c><00:01:39.799><c> um</c><00:01:39.960><c> as</c><00:01:40.040><c> I</c><00:01:40.159><c> said</c>

00:01:40.389 --> 00:01:40.399 align:start position:0%
you can try it out in um as I said
 

00:01:40.399 --> 00:01:43.670 align:start position:0%
you can try it out in um as I said
preview<00:01:41.280><c> uh</c><00:01:41.399><c> 3</c><00:01:41.840><c> of</c><00:01:42.000><c> visual</c><00:01:42.280><c> studio</c>

00:01:43.670 --> 00:01:43.680 align:start position:0%
preview uh 3 of visual studio
 

00:01:43.680 --> 00:01:45.670 align:start position:0%
preview uh 3 of visual studio
17.10<00:01:44.680><c> and</c><00:01:44.920><c> like</c><00:01:45.040><c> I</c><00:01:45.119><c> said</c><00:01:45.280><c> that's</c><00:01:45.399><c> going</c><00:01:45.520><c> to</c><00:01:45.600><c> be</c>

00:01:45.670 --> 00:01:45.680 align:start position:0%
17.10 and like I said that's going to be
 

00:01:45.680 --> 00:01:48.270 align:start position:0%
17.10 and like I said that's going to be
out<00:01:45.880><c> in</c><00:01:46.040><c> May</c><00:01:46.759><c> speaking</c><00:01:47.159><c> of</c><00:01:47.320><c> Visual</c><00:01:47.600><c> Studio</c><00:01:48.119><c> the</c>

00:01:48.270 --> 00:01:48.280 align:start position:0%
out in May speaking of Visual Studio the
 

00:01:48.280 --> 00:01:50.709 align:start position:0%
out in May speaking of Visual Studio the
latest<00:01:49.079><c> Visual</c><00:01:49.360><c> Studio</c><00:01:49.719><c> code</c><00:01:50.040><c> update</c><00:01:50.360><c> is</c><00:01:50.479><c> out</c>

00:01:50.709 --> 00:01:50.719 align:start position:0%
latest Visual Studio code update is out
 

00:01:50.719 --> 00:01:53.270 align:start position:0%
latest Visual Studio code update is out
now<00:01:51.200><c> and</c><00:01:51.439><c> this</c><00:01:51.600><c> release</c><00:01:52.119><c> brings</c><00:01:52.640><c> some</c><00:01:52.840><c> notable</c>

00:01:53.270 --> 00:01:53.280 align:start position:0%
now and this release brings some notable
 

00:01:53.280 --> 00:01:55.550 align:start position:0%
now and this release brings some notable
updates<00:01:53.920><c> to</c><00:01:54.079><c> how</c><00:01:54.320><c> extension</c><00:01:54.759><c> updates</c><00:01:55.200><c> happen</c>

00:01:55.550 --> 00:01:55.560 align:start position:0%
updates to how extension updates happen
 

00:01:55.560 --> 00:01:57.109 align:start position:0%
updates to how extension updates happen
so<00:01:55.719><c> you</c><00:01:55.799><c> can</c><00:01:55.920><c> now</c><00:01:56.159><c> restart</c><00:01:56.600><c> and</c><00:01:56.759><c> update</c>

00:01:57.109 --> 00:01:57.119 align:start position:0%
so you can now restart and update
 

00:01:57.119 --> 00:01:58.870 align:start position:0%
so you can now restart and update
extensions<00:01:58.000><c> without</c><00:01:58.240><c> having</c><00:01:58.399><c> to</c><00:01:58.479><c> reload</c><00:01:58.799><c> your</c>

00:01:58.870 --> 00:01:58.880 align:start position:0%
extensions without having to reload your
 

00:01:58.880 --> 00:02:01.069 align:start position:0%
extensions without having to reload your
editor<00:01:59.320><c> which</c><00:01:59.439><c> is</c><00:01:59.560><c> great</c><00:02:00.119><c> there's</c><00:02:00.399><c> also</c><00:02:00.680><c> now</c>

00:02:01.069 --> 00:02:01.079 align:start position:0%
editor which is great there's also now
 

00:02:01.079 --> 00:02:03.830 align:start position:0%
editor which is great there's also now
um<00:02:01.200><c> folding</c><00:02:01.600><c> markers</c><00:02:02.159><c> in</c><00:02:02.280><c> the</c><00:02:02.399><c> mini</c><00:02:02.719><c> map</c><00:02:03.600><c> so</c><00:02:03.759><c> if</c>

00:02:03.830 --> 00:02:03.840 align:start position:0%
um folding markers in the mini map so if
 

00:02:03.840 --> 00:02:06.670 align:start position:0%
um folding markers in the mini map so if
you're<00:02:04.000><c> a</c><00:02:04.119><c> Monster</c><00:02:04.520><c> who</c><00:02:04.680><c> keeps</c><00:02:05.000><c> mini</c><00:02:05.280><c> map</c><00:02:05.680><c> on</c>

00:02:06.670 --> 00:02:06.680 align:start position:0%
you're a Monster who keeps mini map on
 

00:02:06.680 --> 00:02:09.630 align:start position:0%
you're a Monster who keeps mini map on
I'm<00:02:06.840><c> kidding</c><00:02:07.560><c> kind</c><00:02:07.719><c> of</c><00:02:08.319><c> um</c><00:02:08.720><c> that's</c><00:02:08.959><c> great</c><00:02:09.560><c> and</c>

00:02:09.630 --> 00:02:09.640 align:start position:0%
I'm kidding kind of um that's great and
 

00:02:09.640 --> 00:02:11.070 align:start position:0%
I'm kidding kind of um that's great and
so<00:02:09.840><c> I've</c><00:02:09.920><c> got</c><00:02:10.039><c> a</c><00:02:10.160><c> full</c><00:02:10.360><c> link</c><00:02:10.679><c> the</c><00:02:10.800><c> release</c>

00:02:11.070 --> 00:02:11.080 align:start position:0%
so I've got a full link the release
 

00:02:11.080 --> 00:02:12.150 align:start position:0%
so I've got a full link the release
notes<00:02:11.360><c> in</c><00:02:11.520><c> the</c><00:02:11.599><c> show</c><00:02:11.800><c> notes</c><00:02:12.000><c> in</c><00:02:12.080><c> the</c>

00:02:12.150 --> 00:02:12.160 align:start position:0%
notes in the show notes in the
 

00:02:12.160 --> 00:02:14.869 align:start position:0%
notes in the show notes in the
description<00:02:13.160><c> and</c><00:02:13.360><c> speaking</c><00:02:13.640><c> of</c><00:02:13.760><c> new</c><00:02:13.920><c> releases</c>

00:02:14.869 --> 00:02:14.879 align:start position:0%
description and speaking of new releases
 

00:02:14.879 --> 00:02:17.070 align:start position:0%
description and speaking of new releases
FFM<00:02:15.360><c> Peg</c><00:02:15.680><c> which</c><00:02:15.800><c> is</c><00:02:16.080><c> one</c><00:02:16.200><c> of</c><00:02:16.319><c> the</c><00:02:16.440><c> most</c><00:02:16.680><c> useful</c>

00:02:17.070 --> 00:02:17.080 align:start position:0%
FFM Peg which is one of the most useful
 

00:02:17.080 --> 00:02:18.630 align:start position:0%
FFM Peg which is one of the most useful
projects<00:02:17.480><c> for</c><00:02:17.640><c> streaming</c><00:02:18.080><c> recording</c><00:02:18.480><c> and</c>

00:02:18.630 --> 00:02:18.640 align:start position:0%
projects for streaming recording and
 

00:02:18.640 --> 00:02:21.190 align:start position:0%
projects for streaming recording and
converting<00:02:19.080><c> audio</c><00:02:19.360><c> and</c><00:02:19.560><c> video</c><00:02:19.879><c> it's</c><00:02:20.160><c> great</c><00:02:21.040><c> uh</c>

00:02:21.190 --> 00:02:21.200 align:start position:0%
converting audio and video it's great uh
 

00:02:21.200 --> 00:02:23.030 align:start position:0%
converting audio and video it's great uh
seriously<00:02:21.680><c> this</c><00:02:21.800><c> thing</c><00:02:21.959><c> like</c><00:02:22.120><c> underpin</c><00:02:22.840><c> so</c>

00:02:23.030 --> 00:02:23.040 align:start position:0%
seriously this thing like underpin so
 

00:02:23.040 --> 00:02:25.550 align:start position:0%
seriously this thing like underpin so
many<00:02:23.239><c> workflows</c><00:02:24.000><c> and</c><00:02:24.160><c> so</c><00:02:24.280><c> many</c><00:02:24.560><c> applications</c>

00:02:25.550 --> 00:02:25.560 align:start position:0%
many workflows and so many applications
 

00:02:25.560 --> 00:02:28.430 align:start position:0%
many workflows and so many applications
it<00:02:25.680><c> just</c><00:02:25.840><c> released</c><00:02:26.239><c> a</c><00:02:26.440><c> version</c><00:02:26.760><c> 7.0</c><00:02:27.680><c> and</c><00:02:28.120><c> last</c>

00:02:28.430 --> 00:02:28.440 align:start position:0%
it just released a version 7.0 and last
 

00:02:28.440 --> 00:02:30.869 align:start position:0%
it just released a version 7.0 and last
year<00:02:29.000><c> the</c><00:02:29.160><c> FFM</c><00:02:29.599><c> Peg</c><00:02:29.959><c> uh</c><00:02:30.120><c> Team</c><00:02:30.360><c> announced</c><00:02:30.760><c> that</c>

00:02:30.869 --> 00:02:30.879 align:start position:0%
year the FFM Peg uh Team announced that
 

00:02:30.879 --> 00:02:32.710 align:start position:0%
year the FFM Peg uh Team announced that
they<00:02:31.000><c> were</c><00:02:31.160><c> moving</c><00:02:31.560><c> to</c><00:02:31.879><c> releasing</c><00:02:32.400><c> major</c>

00:02:32.710 --> 00:02:32.720 align:start position:0%
they were moving to releasing major
 

00:02:32.720 --> 00:02:34.949 align:start position:0%
they were moving to releasing major
releases<00:02:33.280><c> each</c><00:02:33.560><c> year</c><00:02:34.160><c> and</c><00:02:34.319><c> that</c><00:02:34.440><c> deprecated</c>

00:02:34.949 --> 00:02:34.959 align:start position:0%
releases each year and that deprecated
 

00:02:34.959 --> 00:02:36.670 align:start position:0%
releases each year and that deprecated
apis<00:02:35.480><c> will</c><00:02:35.640><c> be</c><00:02:35.760><c> removed</c><00:02:36.239><c> after</c><00:02:36.440><c> three</c>

00:02:36.670 --> 00:02:36.680 align:start position:0%
apis will be removed after three
 

00:02:36.680 --> 00:02:38.790 align:start position:0%
apis will be removed after three
releases<00:02:37.560><c> so</c><00:02:37.720><c> if</c><00:02:37.800><c> you're</c><00:02:37.959><c> using</c><00:02:38.200><c> a</c><00:02:38.319><c> feature</c>

00:02:38.790 --> 00:02:38.800 align:start position:0%
releases so if you're using a feature
 

00:02:38.800 --> 00:02:41.550 align:start position:0%
releases so if you're using a feature
that<00:02:38.920><c> was</c><00:02:39.080><c> deprecated</c><00:02:39.640><c> before</c><00:02:39.920><c> FFM</c><00:02:40.440><c> Peg</c><00:02:40.640><c> 6</c><00:02:41.400><c> be</c>

00:02:41.550 --> 00:02:41.560 align:start position:0%
that was deprecated before FFM Peg 6 be
 

00:02:41.560 --> 00:02:43.149 align:start position:0%
that was deprecated before FFM Peg 6 be
sure<00:02:41.720><c> that</c><00:02:41.840><c> you</c><00:02:41.920><c> found</c><00:02:42.080><c> a</c><00:02:42.280><c> replacement</c><00:02:42.840><c> or</c><00:02:43.080><c> you</c>

00:02:43.149 --> 00:02:43.159 align:start position:0%
sure that you found a replacement or you
 

00:02:43.159 --> 00:02:45.470 align:start position:0%
sure that you found a replacement or you
know<00:02:43.319><c> maybe</c><00:02:43.519><c> stick</c><00:02:43.800><c> with</c><00:02:44.040><c> them</c><00:02:44.280><c> the</c><00:02:44.400><c> 5.1</c><00:02:45.000><c> LTS</c>

00:02:45.470 --> 00:02:45.480 align:start position:0%
know maybe stick with them the 5.1 LTS
 

00:02:45.480 --> 00:02:46.949 align:start position:0%
know maybe stick with them the 5.1 LTS
Branch<00:02:45.760><c> for</c><00:02:45.959><c> now</c><00:02:46.159><c> some</c><00:02:46.280><c> of</c><00:02:46.360><c> the</c><00:02:46.480><c> big</c><00:02:46.640><c> features</c>

00:02:46.949 --> 00:02:46.959 align:start position:0%
Branch for now some of the big features
 

00:02:46.959 --> 00:02:49.190 align:start position:0%
Branch for now some of the big features
in<00:02:47.120><c> this</c><00:02:47.280><c> release</c><00:02:47.760><c> included</c><00:02:48.560><c> uh</c><00:02:48.720><c> support</c><00:02:49.000><c> for</c>

00:02:49.190 --> 00:02:49.200 align:start position:0%
in this release included uh support for
 

00:02:49.200 --> 00:02:52.470 align:start position:0%
in this release included uh support for
the<00:02:49.280><c> native</c><00:02:49.599><c> vbc</c><00:02:50.080><c> decoder</c><00:02:51.000><c> IM</c><00:02:51.239><c> AMF</c><00:02:51.840><c> support</c>

00:02:52.470 --> 00:02:52.480 align:start position:0%
the native vbc decoder IM AMF support
 

00:02:52.480 --> 00:02:55.430 align:start position:0%
the native vbc decoder IM AMF support
and<00:02:52.760><c> a</c><00:02:52.920><c> multi-threaded</c><00:02:53.680><c> fmeg</c><00:02:54.440><c> CLI</c><00:02:54.920><c> tool</c><00:02:55.319><c> which</c>

00:02:55.430 --> 00:02:55.440 align:start position:0%
and a multi-threaded fmeg CLI tool which
 

00:02:55.440 --> 00:02:57.949 align:start position:0%
and a multi-threaded fmeg CLI tool which
is<00:02:55.800><c> great</c><00:02:56.560><c> um</c><00:02:56.760><c> this</c><00:02:56.879><c> is</c><00:02:57.000><c> a</c><00:02:57.159><c> hugely</c><00:02:57.519><c> important</c>

00:02:57.949 --> 00:02:57.959 align:start position:0%
is great um this is a hugely important
 

00:02:57.959 --> 00:02:59.949 align:start position:0%
is great um this is a hugely important
project<00:02:58.400><c> and</c><00:02:58.519><c> I'm</c><00:02:58.640><c> a</c><00:02:58.760><c> huge</c><00:02:59.040><c> fan</c><00:02:59.280><c> and</c><00:02:59.400><c> supporter</c>

00:02:59.949 --> 00:02:59.959 align:start position:0%
project and I'm a huge fan and supporter
 

00:02:59.959 --> 00:03:01.750 align:start position:0%
project and I'm a huge fan and supporter
of<00:03:00.440><c> so</c><00:03:00.599><c> I've</c><00:03:00.720><c> got</c><00:03:00.800><c> links</c><00:03:01.000><c> in</c><00:03:01.080><c> the</c><00:03:01.200><c> description</c>

00:03:01.750 --> 00:03:01.760 align:start position:0%
of so I've got links in the description
 

00:03:01.760 --> 00:03:04.070 align:start position:0%
of so I've got links in the description
um<00:03:01.879><c> down</c><00:03:02.040><c> below</c><00:03:02.280><c> to</c><00:03:02.360><c> learn</c><00:03:02.680><c> more</c><00:03:03.680><c> in</c><00:03:03.840><c> some</c>

00:03:04.070 --> 00:03:04.080 align:start position:0%
um down below to learn more in some
 

00:03:04.080 --> 00:03:06.789 align:start position:0%
um down below to learn more in some
quick<00:03:04.319><c> AI</c><00:03:04.640><c> news</c><00:03:05.000><c> open</c><00:03:05.280><c> aai</c><00:03:05.640><c> announced</c><00:03:06.200><c> that</c><00:03:06.680><c> uh</c>

00:03:06.789 --> 00:03:06.799 align:start position:0%
quick AI news open aai announced that uh
 

00:03:06.799 --> 00:03:10.070 align:start position:0%
quick AI news open aai announced that uh
its<00:03:07.040><c> gp4</c><00:03:07.799><c> turbo</c><00:03:08.280><c> and</c><00:03:08.599><c> vision</c><00:03:09.000><c> models</c><00:03:09.480><c> are</c><00:03:09.720><c> now</c>

00:03:10.070 --> 00:03:10.080 align:start position:0%
its gp4 turbo and vision models are now
 

00:03:10.080 --> 00:03:13.110 align:start position:0%
its gp4 turbo and vision models are now
generally<00:03:10.519><c> available</c><00:03:10.959><c> in</c><00:03:11.120><c> the</c><00:03:11.319><c> API</c><00:03:12.319><c> and</c><00:03:12.760><c> um</c>

00:03:13.110 --> 00:03:13.120 align:start position:0%
generally available in the API and um
 

00:03:13.120 --> 00:03:15.550 align:start position:0%
generally available in the API and um
that<00:03:13.680><c> it</c><00:03:13.799><c> can</c><00:03:14.120><c> use</c><00:03:14.440><c> Json</c><00:03:14.879><c> mode</c><00:03:15.319><c> uh</c><00:03:15.440><c> and</c>

00:03:15.550 --> 00:03:15.560 align:start position:0%
that it can use Json mode uh and
 

00:03:15.560 --> 00:03:17.789 align:start position:0%
that it can use Json mode uh and
function<00:03:15.920><c> calling</c><00:03:16.519><c> and</c><00:03:16.640><c> openai</c><00:03:17.360><c> highlighted</c>

00:03:17.789 --> 00:03:17.799 align:start position:0%
function calling and openai highlighted
 

00:03:17.799 --> 00:03:19.789 align:start position:0%
function calling and openai highlighted
some<00:03:18.040><c> examples</c><00:03:18.599><c> of</c><00:03:18.760><c> some</c><00:03:18.959><c> tools</c><00:03:19.319><c> using</c><00:03:19.680><c> this</c>

00:03:19.789 --> 00:03:19.799 align:start position:0%
some examples of some tools using this
 

00:03:19.799 --> 00:03:22.589 align:start position:0%
some examples of some tools using this
feature<00:03:20.280><c> now</c><00:03:21.040><c> but</c><00:03:21.239><c> I</c><00:03:21.319><c> want</c><00:03:21.440><c> to</c><00:03:21.560><c> shout</c><00:03:21.799><c> out</c><00:03:22.080><c> one</c>

00:03:22.589 --> 00:03:22.599 align:start position:0%
feature now but I want to shout out one
 

00:03:22.599 --> 00:03:25.550 align:start position:0%
feature now but I want to shout out one
right<00:03:22.799><c> now</c><00:03:23.040><c> in</c><00:03:23.280><c> my</c><00:03:24.200><c> project</c><00:03:24.799><c> Spotlight</c>

00:03:25.550 --> 00:03:25.560 align:start position:0%
right now in my project Spotlight
 

00:03:25.560 --> 00:03:27.430 align:start position:0%
right now in my project Spotlight
section<00:03:26.400><c> and</c><00:03:26.519><c> this</c><00:03:26.640><c> is</c><00:03:26.760><c> where</c><00:03:26.879><c> I</c><00:03:27.080><c> highlight</c>

00:03:27.430 --> 00:03:27.440 align:start position:0%
section and this is where I highlight
 

00:03:27.440 --> 00:03:30.190 align:start position:0%
section and this is where I highlight
great<00:03:27.760><c> projects</c><00:03:28.239><c> on</c><00:03:28.680><c> GitHub</c><00:03:29.319><c> and</c><00:03:29.480><c> this</c><00:03:29.959><c> comes</c>

00:03:30.190 --> 00:03:30.200 align:start position:0%
great projects on GitHub and this comes
 

00:03:30.200 --> 00:03:32.110 align:start position:0%
great projects on GitHub and this comes
from<00:03:30.400><c> Simon</c><00:03:30.680><c> Willison</c><00:03:31.280><c> who</c><00:03:31.560><c> is</c><00:03:31.680><c> the</c><00:03:31.840><c> creator</c>

00:03:32.110 --> 00:03:32.120 align:start position:0%
from Simon Willison who is the creator
 

00:03:32.120 --> 00:03:34.390 align:start position:0%
from Simon Willison who is the creator
of<00:03:32.239><c> data</c><00:03:32.560><c> set</c><00:03:33.080><c> a</c><00:03:33.239><c> great</c><00:03:33.439><c> tool</c><00:03:33.680><c> for</c><00:03:33.920><c> exploring</c>

00:03:34.390 --> 00:03:34.400 align:start position:0%
of data set a great tool for exploring
 

00:03:34.400 --> 00:03:36.710 align:start position:0%
of data set a great tool for exploring
and<00:03:34.560><c> Publishing</c><00:03:35.000><c> data</c><00:03:35.959><c> uh</c><00:03:36.200><c> and</c><00:03:36.280><c> he's</c><00:03:36.439><c> also</c><00:03:36.640><c> an</c>

00:03:36.710 --> 00:03:36.720 align:start position:0%
and Publishing data uh and he's also an
 

00:03:36.720 --> 00:03:39.030 align:start position:0%
and Publishing data uh and he's also an
all-around<00:03:37.120><c> friend</c><00:03:37.360><c> of</c><00:03:37.480><c> the</c><00:03:37.560><c> show</c><00:03:38.439><c> and</c><00:03:38.799><c> um</c>

00:03:39.030 --> 00:03:39.040 align:start position:0%
all-around friend of the show and um
 

00:03:39.040 --> 00:03:41.470 align:start position:0%
all-around friend of the show and um
this<00:03:39.200><c> tool</c><00:03:39.480><c> is</c><00:03:39.680><c> called</c><00:03:40.280><c> uh</c><00:03:40.480><c> data</c><00:03:40.840><c> set</c><00:03:41.040><c> extract</c>

00:03:41.470 --> 00:03:41.480 align:start position:0%
this tool is called uh data set extract
 

00:03:41.480 --> 00:03:43.229 align:start position:0%
this tool is called uh data set extract
and<00:03:41.560><c> it's</c><00:03:41.680><c> a</c><00:03:41.799><c> plug-in</c><00:03:42.080><c> for</c><00:03:42.239><c> data</c><00:03:42.519><c> set</c><00:03:43.040><c> and</c><00:03:43.120><c> it</c>

00:03:43.229 --> 00:03:43.239 align:start position:0%
and it's a plug-in for data set and it
 

00:03:43.239 --> 00:03:45.550 align:start position:0%
and it's a plug-in for data set and it
can<00:03:43.439><c> extract</c><00:03:44.040><c> data</c><00:03:44.360><c> from</c><00:03:44.560><c> unstructured</c><00:03:45.200><c> text</c>

00:03:45.550 --> 00:03:45.560 align:start position:0%
can extract data from unstructured text
 

00:03:45.560 --> 00:03:47.830 align:start position:0%
can extract data from unstructured text
and<00:03:45.760><c> images</c><00:03:46.319><c> and</c><00:03:46.439><c> then</c><00:03:46.560><c> load</c><00:03:46.840><c> them</c><00:03:47.040><c> into</c><00:03:47.319><c> a</c>

00:03:47.830 --> 00:03:47.840 align:start position:0%
and images and then load them into a
 

00:03:47.840 --> 00:03:50.830 align:start position:0%
and images and then load them into a
database<00:03:48.840><c> super</c><00:03:49.200><c> super</c><00:03:49.560><c> cool</c><00:03:50.159><c> as</c><00:03:50.360><c> is</c><00:03:50.519><c> the</c><00:03:50.640><c> rest</c>

00:03:50.830 --> 00:03:50.840 align:start position:0%
database super super cool as is the rest
 

00:03:50.840 --> 00:03:52.830 align:start position:0%
database super super cool as is the rest
of<00:03:51.000><c> data</c><00:03:51.319><c> set</c><00:03:51.959><c> uh</c><00:03:52.040><c> and</c><00:03:52.159><c> I've</c><00:03:52.319><c> got</c><00:03:52.439><c> links</c><00:03:52.680><c> to</c>

00:03:52.830 --> 00:03:52.840 align:start position:0%
of data set uh and I've got links to
 

00:03:52.840 --> 00:03:54.789 align:start position:0%
of data set uh and I've got links to
Simon's<00:03:53.239><c> blog</c><00:03:53.480><c> post</c><00:03:53.799><c> and</c><00:03:53.959><c> GitHub</c><00:03:54.239><c> repos</c><00:03:54.680><c> in</c>

00:03:54.789 --> 00:03:54.799 align:start position:0%
Simon's blog post and GitHub repos in
 

00:03:54.799 --> 00:03:56.949 align:start position:0%
Simon's blog post and GitHub repos in
the<00:03:54.879><c> show</c><00:03:55.079><c> notes</c><00:03:55.319><c> and</c><00:03:55.439><c> the</c><00:03:55.840><c> description</c><00:03:56.840><c> and</c>

00:03:56.949 --> 00:03:56.959 align:start position:0%
the show notes and the description and
 

00:03:56.959 --> 00:03:59.229 align:start position:0%
the show notes and the description and
now<00:03:57.120><c> it's</c><00:03:57.280><c> time</c><00:03:57.439><c> for</c><00:03:57.560><c> my</c><00:03:57.760><c> pick</c><00:03:57.920><c> of</c><00:03:58.000><c> the</c><00:03:58.159><c> week</c><00:03:59.159><c> a</c>

00:03:59.229 --> 00:03:59.239 align:start position:0%
now it's time for my pick of the week a
 

00:03:59.239 --> 00:04:01.830 align:start position:0%
now it's time for my pick of the week a
new<00:03:59.400><c> TV</c><00:03:59.879><c> series</c><00:04:00.560><c> based</c><00:04:00.879><c> on</c><00:04:01.040><c> the</c><00:04:01.200><c> Fallout</c><00:04:01.599><c> video</c>

00:04:01.830 --> 00:04:01.840 align:start position:0%
new TV series based on the Fallout video
 

00:04:01.840 --> 00:04:04.069 align:start position:0%
new TV series based on the Fallout video
game<00:04:02.079><c> franchise</c><00:04:02.680><c> hence</c><00:04:02.920><c> my</c><00:04:03.079><c> bandana</c><00:04:03.879><c> was</c>

00:04:04.069 --> 00:04:04.079 align:start position:0%
game franchise hence my bandana was
 

00:04:04.079 --> 00:04:06.429 align:start position:0%
game franchise hence my bandana was
released<00:04:04.439><c> on</c><00:04:04.640><c> Amazon</c><00:04:05.159><c> this</c><00:04:05.360><c> week</c><00:04:05.840><c> and</c><00:04:06.159><c> I</c><00:04:06.239><c> was</c>

00:04:06.429 --> 00:04:06.439 align:start position:0%
released on Amazon this week and I was
 

00:04:06.439 --> 00:04:08.270 align:start position:0%
released on Amazon this week and I was
really<00:04:06.680><c> nervous</c><00:04:07.159><c> because</c><00:04:07.439><c> video</c><00:04:07.799><c> game</c><00:04:07.959><c> TV</c>

00:04:08.270 --> 00:04:08.280 align:start position:0%
really nervous because video game TV
 

00:04:08.280 --> 00:04:10.750 align:start position:0%
really nervous because video game TV
adaptations<00:04:08.840><c> are</c><00:04:09.159><c> often</c><00:04:09.519><c> terrible</c><00:04:10.400><c> and</c><00:04:10.640><c> part</c>

00:04:10.750 --> 00:04:10.760 align:start position:0%
adaptations are often terrible and part
 

00:04:10.760 --> 00:04:12.229 align:start position:0%
adaptations are often terrible and part
of<00:04:10.879><c> the</c><00:04:10.959><c> reason</c><00:04:11.239><c> the</c><00:04:11.360><c> last</c><00:04:11.560><c> of</c><00:04:11.720><c> us</c><00:04:11.840><c> is</c><00:04:12.000><c> such</c><00:04:12.120><c> a</c>

00:04:12.229 --> 00:04:12.239 align:start position:0%
of the reason the last of us is such a
 

00:04:12.239 --> 00:04:14.270 align:start position:0%
of the reason the last of us is such a
Triumph<00:04:12.680><c> is</c><00:04:12.879><c> because</c><00:04:13.680><c> a</c><00:04:13.840><c> it's</c><00:04:14.000><c> amazing</c>

00:04:14.270 --> 00:04:14.280 align:start position:0%
Triumph is because a it's amazing
 

00:04:14.280 --> 00:04:16.189 align:start position:0%
Triumph is because a it's amazing
storytelling<00:04:15.239><c> um</c><00:04:15.360><c> but</c><00:04:15.519><c> also</c><00:04:15.840><c> you</c><00:04:15.959><c> know</c><00:04:16.040><c> it</c>

00:04:16.189 --> 00:04:16.199 align:start position:0%
storytelling um but also you know it
 

00:04:16.199 --> 00:04:17.909 align:start position:0%
storytelling um but also you know it
captured<00:04:16.560><c> The</c><00:04:16.680><c> Narrative</c><00:04:17.079><c> of</c><00:04:17.239><c> that</c><00:04:17.400><c> game</c><00:04:17.639><c> so</c>

00:04:17.909 --> 00:04:17.919 align:start position:0%
captured The Narrative of that game so
 

00:04:17.919 --> 00:04:19.789 align:start position:0%
captured The Narrative of that game so
well<00:04:18.560><c> so</c><00:04:18.840><c> when</c><00:04:18.959><c> the</c><00:04:19.040><c> Fallout</c><00:04:19.400><c> series</c><00:04:19.639><c> was</c>

00:04:19.789 --> 00:04:19.799 align:start position:0%
well so when the Fallout series was
 

00:04:19.799 --> 00:04:22.469 align:start position:0%
well so when the Fallout series was
announced<00:04:20.720><c> I</c><00:04:20.799><c> was</c><00:04:21.000><c> nervous</c><00:04:21.759><c> even</c><00:04:22.040><c> though</c><00:04:22.320><c> it's</c>

00:04:22.469 --> 00:04:22.479 align:start position:0%
announced I was nervous even though it's
 

00:04:22.479 --> 00:04:23.830 align:start position:0%
announced I was nervous even though it's
being<00:04:22.680><c> produced</c><00:04:23.040><c> by</c><00:04:23.160><c> the</c><00:04:23.240><c> creators</c><00:04:23.520><c> of</c><00:04:23.639><c> westw</c>

00:04:23.830 --> 00:04:23.840 align:start position:0%
being produced by the creators of westw
 

00:04:23.840 --> 00:04:27.030 align:start position:0%
being produced by the creators of westw
world<00:04:24.560><c> but</c><00:04:25.080><c> I'm</c><00:04:25.280><c> happy</c><00:04:25.479><c> to</c><00:04:25.639><c> report</c><00:04:26.120><c> that</c><00:04:26.320><c> it</c><00:04:26.520><c> is</c>

00:04:27.030 --> 00:04:27.040 align:start position:0%
world but I'm happy to report that it is
 

00:04:27.040 --> 00:04:28.909 align:start position:0%
world but I'm happy to report that it is
great<00:04:27.880><c> and</c><00:04:28.040><c> even</c><00:04:28.240><c> if</c><00:04:28.320><c> you've</c><00:04:28.520><c> never</c><00:04:28.680><c> played</c>

00:04:28.909 --> 00:04:28.919 align:start position:0%
great and even if you've never played
 

00:04:28.919 --> 00:04:30.430 align:start position:0%
great and even if you've never played
the<00:04:29.039><c> games</c><00:04:29.479><c> I</c><00:04:29.680><c> I</c><00:04:29.759><c> think</c><00:04:29.840><c> that</c><00:04:29.960><c> you'll</c><00:04:30.120><c> enjoy</c>

00:04:30.430 --> 00:04:30.440 align:start position:0%
the games I I think that you'll enjoy
 

00:04:30.440 --> 00:04:32.590 align:start position:0%
the games I I think that you'll enjoy
the<00:04:30.560><c> show</c><00:04:31.080><c> but</c><00:04:31.240><c> for</c><00:04:31.440><c> fans</c><00:04:31.759><c> of</c><00:04:31.880><c> the</c><00:04:32.039><c> games</c><00:04:32.400><c> like</c>

00:04:32.590 --> 00:04:32.600 align:start position:0%
the show but for fans of the games like
 

00:04:32.600 --> 00:04:35.189 align:start position:0%
the show but for fans of the games like
myself<00:04:33.360><c> the</c><00:04:33.440><c> show</c><00:04:33.720><c> got</c><00:04:33.919><c> the</c><00:04:34.080><c> tone</c><00:04:34.800><c> just</c>

00:04:35.189 --> 00:04:35.199 align:start position:0%
myself the show got the tone just
 

00:04:35.199 --> 00:04:38.070 align:start position:0%
myself the show got the tone just
exactly<00:04:35.639><c> right</c><00:04:35.880><c> and</c><00:04:36.039><c> it's</c><00:04:36.320><c> so</c><00:04:36.800><c> so</c><00:04:37.160><c> good</c><00:04:37.720><c> so</c><00:04:38.000><c> if</c>

00:04:38.070 --> 00:04:38.080 align:start position:0%
exactly right and it's so so good so if
 

00:04:38.080 --> 00:04:39.510 align:start position:0%
exactly right and it's so so good so if
you<00:04:38.240><c> haven't</c><00:04:38.479><c> played</c><00:04:38.720><c> the</c><00:04:38.840><c> games</c><00:04:39.160><c> or</c><00:04:39.320><c> if</c><00:04:39.400><c> you</c>

00:04:39.510 --> 00:04:39.520 align:start position:0%
you haven't played the games or if you
 

00:04:39.520 --> 00:04:41.510 align:start position:0%
you haven't played the games or if you
haven't<00:04:39.720><c> played</c><00:04:39.880><c> them</c><00:04:40.000><c> in</c><00:04:40.080><c> a</c><00:04:40.280><c> while</c><00:04:40.880><c> Bethesda</c>

00:04:41.510 --> 00:04:41.520 align:start position:0%
haven't played them in a while Bethesda
 

00:04:41.520 --> 00:04:43.469 align:start position:0%
haven't played them in a while Bethesda
announced<00:04:42.039><c> this</c><00:04:42.160><c> week</c><00:04:42.400><c> that</c><00:04:42.560><c> Fallout</c><00:04:42.880><c> 4</c><00:04:43.360><c> a</c>

00:04:43.469 --> 00:04:43.479 align:start position:0%
announced this week that Fallout 4 a
 

00:04:43.479 --> 00:04:46.150 align:start position:0%
announced this week that Fallout 4 a
game<00:04:43.680><c> that</c><00:04:43.759><c> was</c><00:04:43.880><c> released</c><00:04:44.280><c> way</c><00:04:44.440><c> back</c><00:04:44.560><c> in</c><00:04:45.160><c> 2015</c>

00:04:46.150 --> 00:04:46.160 align:start position:0%
game that was released way back in 2015
 

00:04:46.160 --> 00:04:48.110 align:start position:0%
game that was released way back in 2015
wow<00:04:46.680><c> um</c><00:04:46.800><c> is</c><00:04:46.960><c> getting</c><00:04:47.199><c> some</c><00:04:47.400><c> upgrades</c><00:04:47.840><c> and</c><00:04:47.960><c> so</c>

00:04:48.110 --> 00:04:48.120 align:start position:0%
wow um is getting some upgrades and so
 

00:04:48.120 --> 00:04:50.350 align:start position:0%
wow um is getting some upgrades and so
for<00:04:48.280><c> the</c><00:04:48.479><c> PlayStation</c><00:04:48.919><c> 5</c><00:04:49.160><c> and</c><00:04:49.360><c> Xbox</c><00:04:49.759><c> series</c><00:04:50.160><c> X</c>

00:04:50.350 --> 00:04:50.360 align:start position:0%
for the PlayStation 5 and Xbox series X
 

00:04:50.360 --> 00:04:52.270 align:start position:0%
for the PlayStation 5 and Xbox series X
and<00:04:50.520><c> S</c><00:04:50.680><c> owners</c><00:04:51.080><c> they'll</c><00:04:51.280><c> now</c><00:04:51.440><c> be</c><00:04:51.560><c> able</c><00:04:51.720><c> to</c><00:04:51.919><c> play</c>

00:04:52.270 --> 00:04:52.280 align:start position:0%
and S owners they'll now be able to play
 

00:04:52.280 --> 00:04:53.870 align:start position:0%
and S owners they'll now be able to play
in<00:04:52.400><c> up</c><00:04:52.520><c> to</c><00:04:52.639><c> 60</c><00:04:52.919><c> frames</c><00:04:53.160><c> per</c><00:04:53.360><c> second</c><00:04:53.720><c> at</c>

00:04:53.870 --> 00:04:53.880 align:start position:0%
in up to 60 frames per second at
 

00:04:53.880 --> 00:04:56.110 align:start position:0%
in up to 60 frames per second at
increased<00:04:54.440><c> resolutions</c><00:04:55.440><c> um</c><00:04:55.600><c> the</c><00:04:55.680><c> steam</c><00:04:55.960><c> deck</c>

00:04:56.110 --> 00:04:56.120 align:start position:0%
increased resolutions um the steam deck
 

00:04:56.120 --> 00:04:57.710 align:start position:0%
increased resolutions um the steam deck
owners<00:04:56.479><c> the</c><00:04:56.600><c> game</c><00:04:56.720><c> is</c><00:04:56.840><c> now</c><00:04:57.000><c> going</c><00:04:57.120><c> to</c><00:04:57.199><c> be</c><00:04:57.320><c> steam</c>

00:04:57.710 --> 00:04:57.720 align:start position:0%
owners the game is now going to be steam
 

00:04:57.720 --> 00:04:59.710 align:start position:0%
owners the game is now going to be steam
deck<00:04:58.000><c> verified</c><00:04:59.000><c> and</c><00:04:59.199><c> there</c><00:04:59.320><c> have</c><00:04:59.440><c> been</c>

00:04:59.710 --> 00:04:59.720 align:start position:0%
deck verified and there have been
 

00:04:59.720 --> 00:05:01.390 align:start position:0%
deck verified and there have been
improvements<00:05:00.080><c> for</c><00:05:00.199><c> the</c><00:05:00.320><c> PC</c><00:05:00.639><c> releases</c><00:05:01.160><c> for</c>

00:05:01.390 --> 00:05:01.400 align:start position:0%
improvements for the PC releases for
 

00:05:01.400 --> 00:05:03.270 align:start position:0%
improvements for the PC releases for
widescreen<00:05:01.840><c> and</c><00:05:01.960><c> Ultra</c><00:05:02.240><c> widescreen</c><00:05:02.720><c> support</c>

00:05:03.270 --> 00:05:03.280 align:start position:0%
widescreen and Ultra widescreen support
 

00:05:03.280 --> 00:05:05.110 align:start position:0%
widescreen and Ultra widescreen support
as<00:05:03.360><c> well</c><00:05:03.479><c> as</c><00:05:03.560><c> some</c><00:05:03.720><c> other</c><00:05:03.880><c> updates</c><00:05:04.720><c> I</c><00:05:04.840><c> recently</c>

00:05:05.110 --> 00:05:05.120 align:start position:0%
as well as some other updates I recently
 

00:05:05.120 --> 00:05:06.430 align:start position:0%
as well as some other updates I recently
played<00:05:05.360><c> through</c><00:05:05.520><c> Fallout</c><00:05:05.840><c> 4</c><00:05:06.080><c> again</c><00:05:06.199><c> on</c><00:05:06.320><c> my</c>

00:05:06.430 --> 00:05:06.440 align:start position:0%
played through Fallout 4 again on my
 

00:05:06.440 --> 00:05:08.830 align:start position:0%
played through Fallout 4 again on my
steam<00:05:06.720><c> deck</c><00:05:07.199><c> and</c><00:05:07.320><c> it</c><00:05:07.400><c> was</c><00:05:07.520><c> such</c><00:05:07.720><c> a</c><00:05:07.840><c> good</c><00:05:08.039><c> time</c>

00:05:08.830 --> 00:05:08.840 align:start position:0%
steam deck and it was such a good time
 

00:05:08.840 --> 00:05:10.350 align:start position:0%
steam deck and it was such a good time
uh<00:05:08.960><c> these</c><00:05:09.120><c> updates</c><00:05:09.400><c> are</c><00:05:09.520><c> going</c><00:05:09.600><c> to</c><00:05:09.680><c> be</c><00:05:09.800><c> out</c><00:05:10.280><c> and</c>

00:05:10.350 --> 00:05:10.360 align:start position:0%
uh these updates are going to be out and
 

00:05:10.360 --> 00:05:12.350 align:start position:0%
uh these updates are going to be out and
they'll<00:05:10.520><c> be</c><00:05:10.639><c> free</c><00:05:11.000><c> on</c><00:05:11.160><c> April</c><00:05:11.440><c> 25th</c><00:05:12.000><c> so</c><00:05:12.160><c> that's</c>

00:05:12.350 --> 00:05:12.360 align:start position:0%
they'll be free on April 25th so that's
 

00:05:12.360 --> 00:05:14.790 align:start position:0%
they'll be free on April 25th so that's
awesome<00:05:13.320><c> anyway</c><00:05:13.919><c> that's</c><00:05:14.080><c> going</c><00:05:14.160><c> to</c><00:05:14.280><c> do</c><00:05:14.440><c> it</c><00:05:14.560><c> for</c>

00:05:14.790 --> 00:05:14.800 align:start position:0%
awesome anyway that's going to do it for
 

00:05:14.800 --> 00:05:16.909 align:start position:0%
awesome anyway that's going to do it for
me<00:05:15.320><c> let</c><00:05:15.479><c> me</c><00:05:15.680><c> know</c><00:05:15.919><c> your</c><00:05:16.080><c> favorite</c><00:05:16.400><c> video</c><00:05:16.639><c> game</c>

00:05:16.909 --> 00:05:16.919 align:start position:0%
me let me know your favorite video game
 

00:05:16.919 --> 00:05:18.990 align:start position:0%
me let me know your favorite video game
adaptation<00:05:17.600><c> in</c><00:05:17.759><c> the</c><00:05:17.960><c> comments</c><00:05:18.520><c> or</c><00:05:18.759><c> you</c><00:05:18.840><c> know</c>

00:05:18.990 --> 00:05:19.000 align:start position:0%
adaptation in the comments or you know
 

00:05:19.000 --> 00:05:20.230 align:start position:0%
adaptation in the comments or you know
let<00:05:19.120><c> me</c><00:05:19.199><c> know</c><00:05:19.360><c> your</c><00:05:19.520><c> thoughts</c><00:05:19.720><c> on</c><00:05:19.919><c> any</c><00:05:20.080><c> other</c>

00:05:20.230 --> 00:05:20.240 align:start position:0%
let me know your thoughts on any other
 

00:05:20.240 --> 00:05:22.670 align:start position:0%
let me know your thoughts on any other
story<00:05:20.520><c> that</c><00:05:20.600><c> we</c><00:05:20.720><c> covered</c><00:05:21.479><c> appreciate</c><00:05:22.000><c> that</c><00:05:22.600><c> if</c>

00:05:22.670 --> 00:05:22.680 align:start position:0%
story that we covered appreciate that if
 

00:05:22.680 --> 00:05:24.749 align:start position:0%
story that we covered appreciate that if
you<00:05:22.840><c> like</c><00:05:23.120><c> this</c><00:05:23.280><c> episode</c><00:05:23.800><c> give</c><00:05:23.919><c> us</c><00:05:24.080><c> a</c><00:05:24.319><c> like</c><00:05:24.639><c> it</c>

00:05:24.749 --> 00:05:24.759 align:start position:0%
you like this episode give us a like it
 

00:05:24.759 --> 00:05:26.790 align:start position:0%
you like this episode give us a like it
helps<00:05:25.039><c> the</c><00:05:25.160><c> algorithm</c><00:05:25.639><c> out</c><00:05:25.919><c> and</c><00:05:26.160><c> subscribe</c><00:05:26.560><c> to</c>

00:05:26.790 --> 00:05:26.800 align:start position:0%
helps the algorithm out and subscribe to
 

00:05:26.800 --> 00:05:28.230 align:start position:0%
helps the algorithm out and subscribe to
the<00:05:26.960><c> GitHub</c><00:05:27.360><c> YouTube</c><00:05:27.639><c> channel</c><00:05:27.919><c> for</c><00:05:28.039><c> all</c><00:05:28.160><c> of</c>

00:05:28.230 --> 00:05:28.240 align:start position:0%
the GitHub YouTube channel for all of
 

00:05:28.240 --> 00:05:32.260 align:start position:0%
the GitHub YouTube channel for all of
your<00:05:28.319><c> nerd</c><00:05:28.600><c> needs</c><00:05:29.280><c> see</c><00:05:29.400><c> you</c><00:05:29.520><c> next</c><00:05:29.720><c> next</c><00:05:29.919><c> time</c>

00:05:32.260 --> 00:05:32.270 align:start position:0%
your nerd needs see you next next time
 

00:05:32.270 --> 00:05:37.190 align:start position:0%
your nerd needs see you next next time
[Music]

