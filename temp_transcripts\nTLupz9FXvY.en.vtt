WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.699 align:start position:0%
 
hi<00:00:00.269><c> guys</c><00:00:00.539><c> and</c><00:00:00.870><c> welcome</c><00:00:01.110><c> to</c><00:00:01.290><c> our</c><00:00:01.530><c> tutorial</c>

00:00:01.699 --> 00:00:01.709 align:start position:0%
hi guys and welcome to our tutorial
 

00:00:01.709 --> 00:00:05.869 align:start position:0%
hi guys and welcome to our tutorial
series<00:00:02.490><c> about</c><00:00:03.389><c> get</c><00:00:04.049><c> okay</c><00:00:04.980><c> get</c><00:00:05.339><c> version</c>

00:00:05.869 --> 00:00:05.879 align:start position:0%
series about get okay get version
 

00:00:05.879 --> 00:00:11.990 align:start position:0%
series about get okay get version
control<00:00:06.330><c> it's</c><00:00:06.960><c> really</c><00:00:07.259><c> important</c><00:00:10.460><c> now</c><00:00:11.460><c> the</c>

00:00:11.990 --> 00:00:12.000 align:start position:0%
control it's really important now the
 

00:00:12.000 --> 00:00:13.520 align:start position:0%
control it's really important now the
way<00:00:12.120><c> that</c><00:00:12.300><c> I've</c><00:00:12.420><c> been</c><00:00:12.450><c> handling</c><00:00:12.929><c> it</c><00:00:13.259><c> for</c><00:00:13.500><c> a</c>

00:00:13.520 --> 00:00:13.530 align:start position:0%
way that I've been handling it for a
 

00:00:13.530 --> 00:00:15.609 align:start position:0%
way that I've been handling it for a
long<00:00:13.740><c> time</c><00:00:14.009><c> is</c><00:00:14.250><c> every</c><00:00:14.490><c> time</c><00:00:14.670><c> that</c><00:00:14.730><c> I</c><00:00:14.969><c> download</c>

00:00:15.609 --> 00:00:15.619 align:start position:0%
long time is every time that I download
 

00:00:15.619 --> 00:00:17.630 align:start position:0%
long time is every time that I download
every<00:00:16.619><c> time</c><00:00:16.770><c> that</c><00:00:16.890><c> I'm</c><00:00:17.010><c> interested</c><00:00:17.430><c> in</c><00:00:17.520><c> an</c>

00:00:17.630 --> 00:00:17.640 align:start position:0%
every time that I'm interested in an
 

00:00:17.640 --> 00:00:19.640 align:start position:0%
every time that I'm interested in an
open<00:00:17.880><c> source</c><00:00:18.090><c> project</c><00:00:18.390><c> I'll</c><00:00:19.260><c> just</c><00:00:19.470><c> do</c><00:00:19.619><c> a</c>

00:00:19.640 --> 00:00:19.650 align:start position:0%
open source project I'll just do a
 

00:00:19.650 --> 00:00:21.800 align:start position:0%
open source project I'll just do a
google<00:00:20.130><c> search</c><00:00:20.160><c> for</c><00:00:20.699><c> it</c><00:00:20.850><c> like</c><00:00:21.119><c> for</c><00:00:21.359><c> example</c>

00:00:21.800 --> 00:00:21.810 align:start position:0%
google search for it like for example
 

00:00:21.810 --> 00:00:26.750 align:start position:0%
google search for it like for example
I'll<00:00:22.020><c> search</c><00:00:22.260><c> for</c><00:00:22.619><c> a</c><00:00:23.900><c> search</c><00:00:24.900><c> for</c><00:00:25.760><c> something</c>

00:00:26.750 --> 00:00:26.760 align:start position:0%
I'll search for a search for something
 

00:00:26.760 --> 00:00:32.089 align:start position:0%
I'll search for a search for something
like<00:00:27.710><c> github</c><00:00:28.849><c> s3</c><00:00:29.849><c> and</c><00:00:30.390><c> image</c><00:00:31.260><c> uploading</c><00:00:31.890><c> with</c>

00:00:32.089 --> 00:00:32.099 align:start position:0%
like github s3 and image uploading with
 

00:00:32.099 --> 00:00:34.370 align:start position:0%
like github s3 and image uploading with
angularjs<00:00:32.579><c> and</c><00:00:33.329><c> then</c><00:00:33.420><c> I'll</c><00:00:33.540><c> end</c><00:00:33.719><c> up</c><00:00:33.870><c> pulling</c>

00:00:34.370 --> 00:00:34.380 align:start position:0%
angularjs and then I'll end up pulling
 

00:00:34.380 --> 00:00:37.040 align:start position:0%
angularjs and then I'll end up pulling
that<00:00:34.680><c> or</c><00:00:34.890><c> I'll</c><00:00:35.100><c> look</c><00:00:35.250><c> at</c><00:00:35.460><c> github</c><00:00:35.870><c> angularjs</c><00:00:36.870><c> in</c>

00:00:37.040 --> 00:00:37.050 align:start position:0%
that or I'll look at github angularjs in
 

00:00:37.050 --> 00:00:39.170 align:start position:0%
that or I'll look at github angularjs in
my<00:00:37.230><c> sequel</c><00:00:37.739><c> then</c><00:00:37.950><c> I'll</c><00:00:38.070><c> I'll</c><00:00:38.640><c> start</c><00:00:38.940><c> browsing</c>

00:00:39.170 --> 00:00:39.180 align:start position:0%
my sequel then I'll I'll start browsing
 

00:00:39.180 --> 00:00:41.270 align:start position:0%
my sequel then I'll I'll start browsing
around<00:00:39.540><c> for</c><00:00:39.899><c> other</c><00:00:40.020><c> projects</c><00:00:40.649><c> that</c><00:00:40.710><c> are</c><00:00:40.860><c> using</c>

00:00:41.270 --> 00:00:41.280 align:start position:0%
around for other projects that are using
 

00:00:41.280 --> 00:00:42.709 align:start position:0%
around for other projects that are using
the<00:00:41.370><c> same</c><00:00:41.579><c> technologies</c><00:00:42.329><c> that</c><00:00:42.360><c> I'm</c>

00:00:42.709 --> 00:00:42.719 align:start position:0%
the same technologies that I'm
 

00:00:42.719 --> 00:00:43.520 align:start position:0%
the same technologies that I'm
interested<00:00:43.230><c> in</c>

00:00:43.520 --> 00:00:43.530 align:start position:0%
interested in
 

00:00:43.530 --> 00:00:46.220 align:start position:0%
interested in
and<00:00:43.820><c> that's</c><00:00:44.820><c> why</c><00:00:45.329><c> I</c><00:00:45.360><c> think</c><00:00:45.539><c> it's</c><00:00:45.870><c> so</c><00:00:46.020><c> great</c>

00:00:46.220 --> 00:00:46.230 align:start position:0%
and that's why I think it's so great
 

00:00:46.230 --> 00:00:48.920 align:start position:0%
and that's why I think it's so great
because<00:00:46.789><c> everybody's</c><00:00:47.789><c> sharing</c><00:00:48.270><c> their</c><00:00:48.539><c> their</c>

00:00:48.920 --> 00:00:48.930 align:start position:0%
because everybody's sharing their their
 

00:00:48.930 --> 00:00:51.470 align:start position:0%
because everybody's sharing their their
repos<00:00:49.410><c> and</c><00:00:49.920><c> most</c><00:00:50.879><c> of</c><00:00:51.000><c> the</c><00:00:51.090><c> time</c><00:00:51.239><c> you</c><00:00:51.390><c> don't</c>

00:00:51.470 --> 00:00:51.480 align:start position:0%
repos and most of the time you don't
 

00:00:51.480 --> 00:00:53.029 align:start position:0%
repos and most of the time you don't
have<00:00:51.660><c> to</c><00:00:51.870><c> reinvent</c><00:00:52.170><c> the</c><00:00:52.500><c> wheel</c><00:00:52.530><c> you</c><00:00:52.920><c> don't</c>

00:00:53.029 --> 00:00:53.039 align:start position:0%
have to reinvent the wheel you don't
 

00:00:53.039 --> 00:00:55.069 align:start position:0%
have to reinvent the wheel you don't
have<00:00:53.219><c> to</c><00:00:53.340><c> code</c><00:00:53.579><c> stuff</c><00:00:53.940><c> from</c><00:00:54.149><c> scratch</c><00:00:54.449><c> all</c><00:00:54.870><c> the</c>

00:00:55.069 --> 00:00:55.079 align:start position:0%
have to code stuff from scratch all the
 

00:00:55.079 --> 00:00:56.750 align:start position:0%
have to code stuff from scratch all the
time<00:00:55.320><c> you</c><00:00:55.559><c> just</c><00:00:55.739><c> have</c><00:00:55.800><c> to</c><00:00:56.010><c> be</c><00:00:56.129><c> a</c><00:00:56.160><c> master</c><00:00:56.460><c> of</c>

00:00:56.750 --> 00:00:56.760 align:start position:0%
time you just have to be a master of
 

00:00:56.760 --> 00:00:59.599 align:start position:0%
time you just have to be a master of
integrating<00:00:57.510><c> as</c><00:00:57.629><c> as</c><00:00:58.260><c> they</c><00:00:58.559><c> say</c><00:00:58.770><c> that</c><00:00:59.039><c> Leonardo</c>

00:00:59.599 --> 00:00:59.609 align:start position:0%
integrating as as they say that Leonardo
 

00:00:59.609 --> 00:01:02.029 align:start position:0%
integrating as as they say that Leonardo
da<00:00:59.760><c> Vinci</c><00:00:59.940><c> he</c><00:01:00.390><c> he</c><00:01:01.079><c> didn't</c><00:01:01.320><c> actually</c><00:01:01.500><c> create</c>

00:01:02.029 --> 00:01:02.039 align:start position:0%
da Vinci he he didn't actually create
 

00:01:02.039 --> 00:01:03.950 align:start position:0%
da Vinci he he didn't actually create
and<00:01:02.340><c> everything</c><00:01:02.550><c> he</c><00:01:03.090><c> just</c><00:01:03.120><c> knew</c><00:01:03.480><c> how</c><00:01:03.600><c> to</c><00:01:03.629><c> steal</c>

00:01:03.950 --> 00:01:03.960 align:start position:0%
and everything he just knew how to steal
 

00:01:03.960 --> 00:01:05.690 align:start position:0%
and everything he just knew how to steal
from<00:01:04.140><c> the</c><00:01:04.320><c> best</c><00:01:04.530><c> and</c><00:01:04.739><c> patch</c><00:01:04.949><c> it</c><00:01:05.159><c> together</c><00:01:05.250><c> to</c>

00:01:05.690 --> 00:01:05.700 align:start position:0%
from the best and patch it together to
 

00:01:05.700 --> 00:01:08.600 align:start position:0%
from the best and patch it together to
make<00:01:05.729><c> it</c><00:01:05.909><c> look</c><00:01:06.030><c> unique</c><00:01:06.390><c> so</c><00:01:07.220><c> git</c><00:01:08.220><c> is</c><00:01:08.369><c> about</c>

00:01:08.600 --> 00:01:08.610 align:start position:0%
make it look unique so git is about
 

00:01:08.610 --> 00:01:10.070 align:start position:0%
make it look unique so git is about
version<00:01:08.850><c> control</c><00:01:09.030><c> what</c><00:01:09.510><c> I</c><00:01:09.540><c> was</c><00:01:09.689><c> doing</c><00:01:09.900><c> for</c><00:01:10.020><c> a</c>

00:01:10.070 --> 00:01:10.080 align:start position:0%
version control what I was doing for a
 

00:01:10.080 --> 00:01:12.109 align:start position:0%
version control what I was doing for a
long<00:01:10.229><c> time</c><00:01:10.530><c> every</c><00:01:11.100><c> time</c><00:01:11.220><c> I</c><00:01:11.369><c> pull</c><00:01:11.580><c> the</c><00:01:11.729><c> git</c><00:01:11.909><c> repo</c>

00:01:12.109 --> 00:01:12.119 align:start position:0%
long time every time I pull the git repo
 

00:01:12.119 --> 00:01:16.630 align:start position:0%
long time every time I pull the git repo
I<00:01:12.360><c> would</c><00:01:12.659><c> just</c><00:01:12.810><c> create</c><00:01:13.140><c> a</c><00:01:13.229><c> delete</c><00:01:13.890><c> the</c><00:01:15.470><c> get</c>

00:01:16.630 --> 00:01:16.640 align:start position:0%
I would just create a delete the get
 

00:01:16.640 --> 00:01:19.580 align:start position:0%
I would just create a delete the get
that<00:01:17.640><c> little</c><00:01:17.939><c> get</c><00:01:18.119><c> hidden</c><00:01:18.659><c> folder</c><00:01:19.170><c> and</c><00:01:19.439><c> I'll</c>

00:01:19.580 --> 00:01:19.590 align:start position:0%
that little get hidden folder and I'll
 

00:01:19.590 --> 00:01:22.300 align:start position:0%
that little get hidden folder and I'll
explain<00:01:19.920><c> to</c><00:01:19.979><c> you</c><00:01:20.250><c> the</c><00:01:20.850><c> way</c><00:01:20.880><c> that</c><00:01:21.150><c> it</c><00:01:21.270><c> works</c>

00:01:22.300 --> 00:01:22.310 align:start position:0%
explain to you the way that it works
 

00:01:22.310 --> 00:01:25.039 align:start position:0%
explain to you the way that it works
second<00:01:23.310><c> here's</c><00:01:23.460><c> my</c><00:01:23.670><c> sequel</c><00:01:24.119><c> node</c><00:01:24.360><c> right</c><00:01:24.689><c> this</c>

00:01:25.039 --> 00:01:25.049 align:start position:0%
second here's my sequel node right this
 

00:01:25.049 --> 00:01:27.950 align:start position:0%
second here's my sequel node right this
is<00:01:25.340><c> part</c><00:01:26.340><c> of</c><00:01:26.430><c> the</c><00:01:26.549><c> live</c><00:01:26.759><c> startup</c><00:01:27.270><c> series</c><00:01:27.720><c> this</c>

00:01:27.950 --> 00:01:27.960 align:start position:0%
is part of the live startup series this
 

00:01:27.960 --> 00:01:30.260 align:start position:0%
is part of the live startup series this
project<00:01:28.560><c> here</c><00:01:28.799><c> and</c><00:01:28.979><c> you'll</c><00:01:29.130><c> notice</c><00:01:29.189><c> you</c><00:01:30.090><c> have</c>

00:01:30.260 --> 00:01:30.270 align:start position:0%
project here and you'll notice you have
 

00:01:30.270 --> 00:01:32.780 align:start position:0%
project here and you'll notice you have
something<00:01:30.689><c> here</c><00:01:31.020><c> if</c><00:01:31.200><c> you</c><00:01:31.409><c> can't</c><00:01:31.710><c> see</c><00:01:31.979><c> it</c><00:01:32.009><c> you</c>

00:01:32.780 --> 00:01:32.790 align:start position:0%
something here if you can't see it you
 

00:01:32.790 --> 00:01:36.560 align:start position:0%
something here if you can't see it you
can<00:01:32.970><c> go</c><00:01:33.119><c> to</c><00:01:33.180><c> view</c><00:01:34.020><c> and</c><00:01:34.310><c> then</c><00:01:35.310><c> you</c><00:01:35.520><c> go</c><00:01:35.729><c> to</c><00:01:35.790><c> hidden</c>

00:01:36.560 --> 00:01:36.570 align:start position:0%
can go to view and then you go to hidden
 

00:01:36.570 --> 00:01:38.929 align:start position:0%
can go to view and then you go to hidden
items<00:01:36.900><c> make</c><00:01:37.350><c> sure</c><00:01:37.500><c> that</c><00:01:37.650><c> that's</c><00:01:37.860><c> checked</c><00:01:38.280><c> and</c>

00:01:38.929 --> 00:01:38.939 align:start position:0%
items make sure that that's checked and
 

00:01:38.939 --> 00:01:40.640 align:start position:0%
items make sure that that's checked and
then<00:01:39.060><c> you'll</c><00:01:39.180><c> be</c><00:01:39.329><c> able</c><00:01:39.420><c> to</c><00:01:39.689><c> see</c><00:01:39.900><c> this</c><00:01:40.259><c> git</c>

00:01:40.640 --> 00:01:40.650 align:start position:0%
then you'll be able to see this git
 

00:01:40.650 --> 00:01:42.800 align:start position:0%
then you'll be able to see this git
folder<00:01:40.950><c> over</c><00:01:41.340><c> here</c><00:01:41.490><c> so</c><00:01:41.880><c> if</c><00:01:42.150><c> you</c><00:01:42.299><c> click</c><00:01:42.570><c> on</c><00:01:42.750><c> it</c>

00:01:42.800 --> 00:01:42.810 align:start position:0%
folder over here so if you click on it
 

00:01:42.810 --> 00:01:45.649 align:start position:0%
folder over here so if you click on it
actually<00:01:43.520><c> let's</c><00:01:44.520><c> see</c><00:01:44.670><c> what</c><00:01:44.850><c> it's</c><00:01:45.299><c> got</c><00:01:45.479><c> all</c>

00:01:45.649 --> 00:01:45.659 align:start position:0%
actually let's see what it's got all
 

00:01:45.659 --> 00:01:47.600 align:start position:0%
actually let's see what it's got all
sorts<00:01:46.049><c> of</c><00:01:46.140><c> stuff</c><00:01:46.439><c> what</c><00:01:46.649><c> it's</c><00:01:46.770><c> got</c><00:01:46.950><c> in</c><00:01:47.100><c> there</c><00:01:47.340><c> is</c>

00:01:47.600 --> 00:01:47.610 align:start position:0%
sorts of stuff what it's got in there is
 

00:01:47.610 --> 00:01:50.980 align:start position:0%
sorts of stuff what it's got in there is
is<00:01:48.170><c> all</c><00:01:49.170><c> sorts</c><00:01:49.649><c> of</c><00:01:49.710><c> interesting</c><00:01:50.009><c> things</c><00:01:50.250><c> but</c>

00:01:50.980 --> 00:01:50.990 align:start position:0%
is all sorts of interesting things but
 

00:01:50.990 --> 00:01:53.990 align:start position:0%
is all sorts of interesting things but
that's<00:01:51.990><c> essentially</c><00:01:52.700><c> everything</c><00:01:53.700><c> that</c><00:01:53.880><c> you</c>

00:01:53.990 --> 00:01:54.000 align:start position:0%
that's essentially everything that you
 

00:01:54.000 --> 00:02:00.139 align:start position:0%
that's essentially everything that you
need<00:01:54.180><c> to</c><00:01:54.329><c> know</c><00:01:57.740><c> I'm</c><00:01:58.740><c> not</c><00:01:58.920><c> gonna</c><00:01:59.070><c> get</c><00:01:59.369><c> so</c><00:01:59.670><c> till</c>

00:02:00.139 --> 00:02:00.149 align:start position:0%
need to know I'm not gonna get so till
 

00:02:00.149 --> 00:02:01.700 align:start position:0%
need to know I'm not gonna get so till
deep<00:02:00.420><c> into</c><00:02:00.750><c> the</c><00:02:00.840><c> technical</c><00:02:01.320><c> side</c><00:02:01.500><c> of</c><00:02:01.560><c> things</c>

00:02:01.700 --> 00:02:01.710 align:start position:0%
deep into the technical side of things
 

00:02:01.710 --> 00:02:04.649 align:start position:0%
deep into the technical side of things
it's<00:02:02.219><c> saving</c><00:02:02.579><c> your</c><00:02:03.000><c> data</c><00:02:03.420><c> in</c><00:02:03.780><c> a</c><00:02:03.899><c> hash</c><00:02:04.140><c> and</c><00:02:04.500><c> I</c>

00:02:04.649 --> 00:02:04.659 align:start position:0%
it's saving your data in a hash and I
 

00:02:04.659 --> 00:02:07.469 align:start position:0%
it's saving your data in a hash and I
you<00:02:05.259><c> update</c><00:02:06.039><c> the</c><00:02:06.219><c> repo</c><00:02:06.640><c> it's</c><00:02:06.999><c> gonna</c><00:02:07.240><c> make</c>

00:02:07.469 --> 00:02:07.479 align:start position:0%
you update the repo it's gonna make
 

00:02:07.479 --> 00:02:10.350 align:start position:0%
you update the repo it's gonna make
little<00:02:07.749><c> tweaks</c><00:02:08.080><c> to</c><00:02:08.470><c> the</c><00:02:08.590><c> hash</c><00:02:08.769><c> and</c><00:02:09.149><c> save</c><00:02:10.149><c> the</c>

00:02:10.350 --> 00:02:10.360 align:start position:0%
little tweaks to the hash and save the
 

00:02:10.360 --> 00:02:12.300 align:start position:0%
little tweaks to the hash and save the
changes<00:02:10.599><c> or</c><00:02:11.170><c> so</c><00:02:11.379><c> on</c><00:02:11.590><c> I'm</c><00:02:11.709><c> not</c><00:02:11.890><c> sure</c><00:02:12.069><c> exactly</c>

00:02:12.300 --> 00:02:12.310 align:start position:0%
changes or so on I'm not sure exactly
 

00:02:12.310 --> 00:02:13.860 align:start position:0%
changes or so on I'm not sure exactly
how<00:02:12.580><c> that</c><00:02:12.640><c> works</c><00:02:12.910><c> I'm</c><00:02:13.209><c> just</c><00:02:13.420><c> interested</c><00:02:13.629><c> in</c>

00:02:13.860 --> 00:02:13.870 align:start position:0%
how that works I'm just interested in
 

00:02:13.870 --> 00:02:16.199 align:start position:0%
how that works I'm just interested in
the<00:02:13.930><c> practical</c><00:02:14.530><c> side</c><00:02:14.709><c> of</c><00:02:14.920><c> things</c><00:02:15.160><c> and</c><00:02:15.400><c> of</c><00:02:15.610><c> you</c>

00:02:16.199 --> 00:02:16.209 align:start position:0%
the practical side of things and of you
 

00:02:16.209 --> 00:02:19.020 align:start position:0%
the practical side of things and of you
not<00:02:16.540><c> losing</c><00:02:16.959><c> your</c><00:02:17.440><c> important</c><00:02:18.010><c> work</c><00:02:18.069><c> and</c><00:02:18.580><c> if</c>

00:02:19.020 --> 00:02:19.030 align:start position:0%
not losing your important work and if
 

00:02:19.030 --> 00:02:21.600 align:start position:0%
not losing your important work and if
stuff<00:02:19.360><c> gets</c><00:02:19.629><c> messed</c><00:02:20.049><c> up</c><00:02:20.290><c> somewhere</c><00:02:20.650><c> along</c><00:02:21.129><c> the</c>

00:02:21.600 --> 00:02:21.610 align:start position:0%
stuff gets messed up somewhere along the
 

00:02:21.610 --> 00:02:23.400 align:start position:0%
stuff gets messed up somewhere along the
time<00:02:21.879><c> line</c><00:02:21.910><c> you're</c><00:02:22.510><c> gonna</c><00:02:22.690><c> know</c><00:02:22.900><c> how</c><00:02:22.930><c> to</c><00:02:23.110><c> go</c>

00:02:23.400 --> 00:02:23.410 align:start position:0%
time line you're gonna know how to go
 

00:02:23.410 --> 00:02:26.610 align:start position:0%
time line you're gonna know how to go
back<00:02:23.709><c> and</c><00:02:24.120><c> to</c><00:02:25.120><c> that</c><00:02:25.269><c> last</c><00:02:25.569><c> time</c><00:02:26.230><c> that</c><00:02:26.440><c> it</c>

00:02:26.610 --> 00:02:26.620 align:start position:0%
back and to that last time that it
 

00:02:26.620 --> 00:02:28.320 align:start position:0%
back and to that last time that it
worked<00:02:26.920><c> without</c><00:02:27.280><c> deleting</c><00:02:27.640><c> the</c><00:02:28.000><c> entire</c>

00:02:28.320 --> 00:02:28.330 align:start position:0%
worked without deleting the entire
 

00:02:28.330 --> 00:02:30.600 align:start position:0%
worked without deleting the entire
project<00:02:28.750><c> and</c><00:02:29.319><c> re</c><00:02:29.590><c> pulling</c><00:02:29.920><c> it</c><00:02:30.280><c> and</c><00:02:30.489><c> all</c><00:02:30.580><c> that</c>

00:02:30.600 --> 00:02:30.610 align:start position:0%
project and re pulling it and all that
 

00:02:30.610 --> 00:02:33.270 align:start position:0%
project and re pulling it and all that
stuff<00:02:31.060><c> so</c><00:02:31.090><c> let's</c><00:02:32.019><c> see</c><00:02:32.230><c> I'm</c><00:02:32.319><c> gonna</c><00:02:32.440><c> go</c><00:02:32.620><c> into</c><00:02:32.830><c> CP</c>

00:02:33.270 --> 00:02:33.280 align:start position:0%
stuff so let's see I'm gonna go into CP
 

00:02:33.280 --> 00:02:35.460 align:start position:0%
stuff so let's see I'm gonna go into CP
my<00:02:33.670><c> seat</c><00:02:33.879><c> will</c><00:02:34.030><c> note</c><00:02:34.150><c> angular</c><00:02:34.569><c> first</c><00:02:35.290><c> thing</c>

00:02:35.460 --> 00:02:35.470 align:start position:0%
my seat will note angular first thing
 

00:02:35.470 --> 00:02:37.199 align:start position:0%
my seat will note angular first thing
you<00:02:35.530><c> need</c><00:02:35.620><c> to</c><00:02:35.680><c> know</c><00:02:35.920><c> something</c><00:02:36.430><c> called</c><00:02:36.790><c> git</c>

00:02:37.199 --> 00:02:37.209 align:start position:0%
you need to know something called git
 

00:02:37.209 --> 00:02:43.020 align:start position:0%
you need to know something called git
log<00:02:38.519><c> dit</c><00:02:39.519><c> log</c><00:02:39.730><c> is</c><00:02:39.970><c> very</c><00:02:40.180><c> interesting</c><00:02:42.030><c> here's</c>

00:02:43.020 --> 00:02:43.030 align:start position:0%
log dit log is very interesting here's
 

00:02:43.030 --> 00:02:44.789 align:start position:0%
log dit log is very interesting here's
what<00:02:43.180><c> it</c><00:02:43.299><c> does</c><00:02:43.329><c> every</c><00:02:44.079><c> time</c><00:02:44.260><c> that</c><00:02:44.379><c> I'm</c><00:02:44.500><c> pushing</c>

00:02:44.789 --> 00:02:44.799 align:start position:0%
what it does every time that I'm pushing
 

00:02:44.799 --> 00:02:48.750 align:start position:0%
what it does every time that I'm pushing
something<00:02:45.280><c> to</c><00:02:45.730><c> to</c><00:02:46.180><c> get</c><00:02:46.420><c> here</c><00:02:46.840><c> it's</c><00:02:47.650><c> it</c><00:02:48.190><c> lets</c><00:02:48.549><c> me</c>

00:02:48.750 --> 00:02:48.760 align:start position:0%
something to to get here it's it lets me
 

00:02:48.760 --> 00:02:51.030 align:start position:0%
something to to get here it's it lets me
know<00:02:48.790><c> so</c><00:02:49.180><c> February</c><00:02:49.720><c> 23rd</c><00:02:50.319><c> that</c><00:02:50.829><c> was</c><00:02:50.920><c> my</c>

00:02:51.030 --> 00:02:51.040 align:start position:0%
know so February 23rd that was my
 

00:02:51.040 --> 00:02:52.400 align:start position:0%
know so February 23rd that was my
birthday<00:02:51.099><c> by</c><00:02:51.640><c> the</c><00:02:51.670><c> way</c>

00:02:52.400 --> 00:02:52.410 align:start position:0%
birthday by the way
 

00:02:52.410 --> 00:02:57.300 align:start position:0%
birthday by the way
okay<00:02:53.440><c> you</c><00:02:53.799><c> see</c><00:02:54.780><c> I</c><00:02:55.780><c> pulled</c><00:02:56.470><c> I</c><00:02:56.680><c> pushed</c><00:02:57.069><c> something</c>

00:02:57.300 --> 00:02:57.310 align:start position:0%
okay you see I pulled I pushed something
 

00:02:57.310 --> 00:02:59.190 align:start position:0%
okay you see I pulled I pushed something
here<00:02:57.760><c> and</c><00:02:57.879><c> here</c><00:02:58.060><c> was</c><00:02:58.180><c> the</c><00:02:58.329><c> name</c><00:02:58.510><c> of</c><00:02:58.540><c> the</c><00:02:58.810><c> commit</c>

00:02:59.190 --> 00:02:59.200 align:start position:0%
here and here was the name of the commit
 

00:02:59.200 --> 00:03:01.800 align:start position:0%
here and here was the name of the commit
was<00:02:59.379><c> updated</c><00:03:00.010><c> read</c><00:03:00.220><c> me</c><00:03:00.430><c> updated</c><00:03:00.819><c> readme</c><00:03:01.299><c> icon</c>

00:03:01.800 --> 00:03:01.810 align:start position:0%
was updated read me updated readme icon
 

00:03:01.810 --> 00:03:04.740 align:start position:0%
was updated read me updated readme icon
image<00:03:02.109><c> updated</c><00:03:02.739><c> making</c><00:03:03.640><c> Heroku</c><00:03:04.150><c> site</c><00:03:04.420><c> better</c>

00:03:04.740 --> 00:03:04.750 align:start position:0%
image updated making Heroku site better
 

00:03:04.750 --> 00:03:07.170 align:start position:0%
image updated making Heroku site better
and<00:03:05.200><c> so</c><00:03:05.410><c> on</c><00:03:05.560><c> and</c><00:03:05.709><c> so</c><00:03:05.920><c> forth</c><00:03:06.310><c> okay</c><00:03:06.549><c> so</c><00:03:06.609><c> I've</c>

00:03:07.170 --> 00:03:07.180 align:start position:0%
and so on and so forth okay so I've
 

00:03:07.180 --> 00:03:10.740 align:start position:0%
and so on and so forth okay so I've
constantly<00:03:07.720><c> been</c><00:03:07.930><c> pushing</c><00:03:08.530><c> stuff</c><00:03:08.859><c> here</c><00:03:09.750><c> you</c>

00:03:10.740 --> 00:03:10.750 align:start position:0%
constantly been pushing stuff here you
 

00:03:10.750 --> 00:03:13.530 align:start position:0%
constantly been pushing stuff here you
can<00:03:10.900><c> press</c><00:03:11.049><c> ctrl</c><00:03:11.530><c> C</c><00:03:11.769><c> in</c><00:03:11.920><c> order</c><00:03:12.069><c> to</c><00:03:12.389><c> cancel</c><00:03:13.389><c> out</c>

00:03:13.530 --> 00:03:13.540 align:start position:0%
can press ctrl C in order to cancel out
 

00:03:13.540 --> 00:03:15.449 align:start position:0%
can press ctrl C in order to cancel out
of<00:03:13.660><c> it</c><00:03:13.810><c> but</c><00:03:14.380><c> of</c><00:03:14.590><c> course</c><00:03:14.650><c> the</c><00:03:15.040><c> way</c><00:03:15.130><c> that</c><00:03:15.340><c> it</c>

00:03:15.449 --> 00:03:15.459 align:start position:0%
of it but of course the way that it
 

00:03:15.459 --> 00:03:16.770 align:start position:0%
of it but of course the way that it
works<00:03:15.700><c> most</c><00:03:15.849><c> of</c><00:03:16.030><c> the</c><00:03:16.090><c> time</c><00:03:16.239><c> is</c><00:03:16.420><c> you'll</c><00:03:16.569><c> do</c><00:03:16.750><c> a</c>

00:03:16.770 --> 00:03:16.780 align:start position:0%
works most of the time is you'll do a
 

00:03:16.780 --> 00:03:19.920 align:start position:0%
works most of the time is you'll do a
git<00:03:17.109><c> add</c><00:03:17.349><c> all</c><00:03:18.209><c> when</c><00:03:19.209><c> you</c><00:03:19.359><c> finish</c><00:03:19.660><c> with</c><00:03:19.780><c> your</c>

00:03:19.920 --> 00:03:19.930 align:start position:0%
git add all when you finish with your
 

00:03:19.930 --> 00:03:21.120 align:start position:0%
git add all when you finish with your
changes<00:03:20.290><c> you're</c><00:03:20.470><c> gonna</c><00:03:20.620><c> do</c><00:03:20.739><c> a</c><00:03:20.769><c> git</c><00:03:21.099><c> commit</c>

00:03:21.120 --> 00:03:21.130 align:start position:0%
changes you're gonna do a git commit
 

00:03:21.130 --> 00:03:26.729 align:start position:0%
changes you're gonna do a git commit
what<00:03:22.120><c> happened</c><00:03:23.160><c> git</c><00:03:24.190><c> okay</c><00:03:24.790><c> let's</c><00:03:25.030><c> try</c><00:03:25.180><c> Q</c><00:03:25.739><c> and</c>

00:03:26.729 --> 00:03:26.739 align:start position:0%
what happened git okay let's try Q and
 

00:03:26.739 --> 00:03:32.069 align:start position:0%
what happened git okay let's try Q and
that's<00:03:27.700><c> it</c><00:03:27.970><c> Q</c><00:03:29.579><c> you'll</c><00:03:30.579><c> see</c><00:03:30.790><c> okay</c><00:03:31.239><c> make</c><00:03:31.840><c> sure</c><00:03:31.870><c> to</c>

00:03:32.069 --> 00:03:32.079 align:start position:0%
that's it Q you'll see okay make sure to
 

00:03:32.079 --> 00:03:35.190 align:start position:0%
that's it Q you'll see okay make sure to
get<00:03:32.200><c> out</c><00:03:32.260><c> of</c><00:03:32.440><c> it</c><00:03:32.530><c> get</c><00:03:32.859><c> at</c><00:03:33.069><c> all</c><00:03:33.930><c> every</c><00:03:34.930><c> time</c><00:03:34.959><c> you</c>

00:03:35.190 --> 00:03:35.200 align:start position:0%
get out of it get at all every time you
 

00:03:35.200 --> 00:03:40.520 align:start position:0%
get out of it get at all every time you
want<00:03:35.349><c> to</c><00:03:35.380><c> make</c><00:03:35.530><c> changes</c><00:03:35.950><c> get</c><00:03:36.220><c> commit</c><00:03:36.730><c> -</c><00:03:37.630><c> M</c><00:03:39.480><c> made</c>

00:03:40.520 --> 00:03:40.530 align:start position:0%
want to make changes get commit - M made
 

00:03:40.530 --> 00:03:46.289 align:start position:0%
want to make changes get commit - M made
changes<00:03:41.530><c> to</c><00:03:42.750><c> let's</c><00:03:43.750><c> say</c><00:03:43.930><c> front</c><00:03:44.290><c> end</c><00:03:44.560><c> okay</c><00:03:45.299><c> you</c>

00:03:46.289 --> 00:03:46.299 align:start position:0%
changes to let's say front end okay you
 

00:03:46.299 --> 00:03:48.059 align:start position:0%
changes to let's say front end okay you
want<00:03:46.480><c> to</c><00:03:46.540><c> get</c><00:03:46.630><c> as</c><00:03:46.750><c> specific</c><00:03:46.870><c> as</c><00:03:47.410><c> possible</c><00:03:47.470><c> at</c>

00:03:48.059 --> 00:03:48.069 align:start position:0%
want to get as specific as possible at
 

00:03:48.069 --> 00:03:49.409 align:start position:0%
want to get as specific as possible at
this<00:03:48.190><c> point</c><00:03:48.250><c> and</c><00:03:48.819><c> then</c><00:03:48.910><c> you're</c><00:03:49.120><c> gonna</c><00:03:49.239><c> do</c><00:03:49.389><c> a</c>

00:03:49.409 --> 00:03:49.419 align:start position:0%
this point and then you're gonna do a
 

00:03:49.419 --> 00:03:55.559 align:start position:0%
this point and then you're gonna do a
git<00:03:49.720><c> push</c><00:03:49.750><c> origin</c><00:03:50.430><c> master</c><00:03:51.870><c> okay</c><00:03:54.329><c> get</c><00:03:55.329><c> push</c>

00:03:55.559 --> 00:03:55.569 align:start position:0%
git push origin master okay get push
 

00:03:55.569 --> 00:03:57.330 align:start position:0%
git push origin master okay get push
origin<00:03:55.780><c> master</c><00:03:56.049><c> I'm</c><00:03:56.680><c> not</c><00:03:56.859><c> gonna</c><00:03:56.980><c> do</c><00:03:57.160><c> it</c><00:03:57.280><c> right</c>

00:03:57.330 --> 00:03:57.340 align:start position:0%
origin master I'm not gonna do it right
 

00:03:57.340 --> 00:03:59.370 align:start position:0%
origin master I'm not gonna do it right
now<00:03:57.489><c> because</c><00:03:57.880><c> I</c><00:03:58.120><c> don't</c><00:03:58.329><c> know</c><00:03:58.540><c> what</c><00:03:59.019><c> I</c><00:03:59.049><c> changed</c>

00:03:59.370 --> 00:03:59.380 align:start position:0%
now because I don't know what I changed
 

00:03:59.380 --> 00:04:01.920 align:start position:0%
now because I don't know what I changed
here<00:03:59.829><c> but</c><00:04:00.489><c> it's</c><00:04:00.639><c> okay</c><00:04:00.940><c> we</c><00:04:01.180><c> have</c><00:04:01.359><c> a</c><00:04:01.389><c> use</c><00:04:01.690><c> case</c>

00:04:01.920 --> 00:04:01.930 align:start position:0%
here but it's okay we have a use case
 

00:04:01.930 --> 00:04:05.159 align:start position:0%
here but it's okay we have a use case
that<00:04:02.200><c> we're</c><00:04:02.319><c> gonna</c><00:04:02.470><c> get</c><00:04:02.709><c> into</c><00:04:03.900><c> first</c><00:04:04.900><c> of</c><00:04:05.049><c> all</c>

00:04:05.159 --> 00:04:05.169 align:start position:0%
that we're gonna get into first of all
 

00:04:05.169 --> 00:04:07.559 align:start position:0%
that we're gonna get into first of all
you<00:04:05.319><c> also</c><00:04:05.470><c> have</c><00:04:05.709><c> branches</c><00:04:06.340><c> let's</c><00:04:07.090><c> do</c><00:04:07.239><c> a</c><00:04:07.299><c> git</c>

00:04:07.559 --> 00:04:07.569 align:start position:0%
you also have branches let's do a git
 

00:04:07.569 --> 00:04:10.879 align:start position:0%
you also have branches let's do a git
branch<00:04:07.810><c> as</c><00:04:08.319><c> you</c><00:04:09.190><c> can</c><00:04:09.340><c> see</c><00:04:09.519><c> I</c><00:04:09.549><c> have</c><00:04:09.849><c> one</c><00:04:10.120><c> two</c>

00:04:10.879 --> 00:04:10.889 align:start position:0%
branch as you can see I have one two
 

00:04:10.889 --> 00:04:16.379 align:start position:0%
branch as you can see I have one two
three<00:04:12.150><c> four</c><00:04:13.150><c> five</c><00:04:14.109><c> six</c><00:04:14.680><c> seven</c><00:04:15.100><c> seven</c><00:04:15.669><c> branches</c>

00:04:16.379 --> 00:04:16.389 align:start position:0%
three four five six seven seven branches
 

00:04:16.389 --> 00:04:18.390 align:start position:0%
three four five six seven seven branches
here<00:04:16.720><c> right</c><00:04:17.019><c> WYSIWYG</c><00:04:17.620><c> cleanup</c><00:04:18.039><c> throw</c>

00:04:18.390 --> 00:04:18.400 align:start position:0%
here right WYSIWYG cleanup throw
 

00:04:18.400 --> 00:04:21.689 align:start position:0%
here right WYSIWYG cleanup throw
master<00:04:19.090><c> fixing</c><00:04:19.600><c> and</c><00:04:20.160><c> most</c><00:04:21.160><c> of</c><00:04:21.310><c> the</c><00:04:21.370><c> time</c><00:04:21.549><c> the</c>

00:04:21.689 --> 00:04:21.699 align:start position:0%
master fixing and most of the time the
 

00:04:21.699 --> 00:04:26.340 align:start position:0%
master fixing and most of the time the
way<00:04:21.790><c> that</c><00:04:21.820><c> it</c><00:04:22.060><c> works</c><00:04:22.270><c> is</c><00:04:22.509><c> in</c><00:04:23.050><c> companies</c><00:04:25.350><c> is</c>

00:04:26.340 --> 00:04:26.350 align:start position:0%
way that it works is in companies is
 

00:04:26.350 --> 00:04:28.290 align:start position:0%
way that it works is in companies is
that<00:04:26.590><c> we're</c><00:04:26.830><c> gonna</c><00:04:27.009><c> create</c><00:04:27.430><c> a</c><00:04:27.850><c> separate</c>

00:04:28.290 --> 00:04:28.300 align:start position:0%
that we're gonna create a separate
 

00:04:28.300 --> 00:04:30.629 align:start position:0%
that we're gonna create a separate
branch<00:04:28.660><c> for</c><00:04:28.990><c> every</c><00:04:29.350><c> new</c><00:04:29.560><c> feature</c><00:04:29.830><c> in</c><00:04:30.460><c> this</c>

00:04:30.629 --> 00:04:30.639 align:start position:0%
branch for every new feature in this
 

00:04:30.639 --> 00:04:33.390 align:start position:0%
branch for every new feature in this
specific<00:04:31.270><c> repo</c><00:04:31.960><c> I</c><00:04:32.229><c> didn't</c><00:04:32.620><c> do</c><00:04:32.710><c> a</c><00:04:32.740><c> good</c><00:04:32.800><c> job</c><00:04:32.979><c> in</c>

00:04:33.390 --> 00:04:33.400 align:start position:0%
specific repo I didn't do a good job in
 

00:04:33.400 --> 00:04:35.129 align:start position:0%
specific repo I didn't do a good job in
terms<00:04:33.430><c> of</c><00:04:33.729><c> explaining</c><00:04:34.120><c> it</c><00:04:34.389><c> and</c><00:04:34.539><c> actually</c>

00:04:35.129 --> 00:04:35.139 align:start position:0%
terms of explaining it and actually
 

00:04:35.139 --> 00:04:36.900 align:start position:0%
terms of explaining it and actually
following<00:04:35.530><c> the</c><00:04:35.680><c> methodology</c><00:04:36.370><c> but</c><00:04:36.550><c> most</c><00:04:36.759><c> of</c>

00:04:36.900 --> 00:04:36.910 align:start position:0%
following the methodology but most of
 

00:04:36.910 --> 00:04:39.270 align:start position:0%
following the methodology but most of
the<00:04:36.970><c> time</c><00:04:37.180><c> you're</c><00:04:37.840><c> gonna</c><00:04:37.930><c> have</c><00:04:38.050><c> one</c><00:04:38.530><c> specific</c>

00:04:39.270 --> 00:04:39.280 align:start position:0%
the time you're gonna have one specific
 

00:04:39.280 --> 00:04:42.330 align:start position:0%
the time you're gonna have one specific
branch<00:04:39.759><c> for</c><00:04:40.630><c> every</c><00:04:40.960><c> single</c><00:04:41.229><c> new</c><00:04:41.560><c> feature</c><00:04:41.949><c> and</c>

00:04:42.330 --> 00:04:42.340 align:start position:0%
branch for every single new feature and
 

00:04:42.340 --> 00:04:44.580 align:start position:0%
branch for every single new feature and
that<00:04:42.580><c> way</c><00:04:42.729><c> if</c><00:04:42.910><c> that</c><00:04:43.150><c> specific</c><00:04:43.810><c> branch</c><00:04:44.080><c> messes</c>

00:04:44.580 --> 00:04:44.590 align:start position:0%
that way if that specific branch messes
 

00:04:44.590 --> 00:04:47.070 align:start position:0%
that way if that specific branch messes
up<00:04:44.800><c> it's</c><00:04:45.340><c> okay</c><00:04:45.970><c> it's</c><00:04:46.389><c> not</c><00:04:46.570><c> the</c><00:04:46.630><c> end</c><00:04:46.870><c> of</c><00:04:46.930><c> the</c>

00:04:47.070 --> 00:04:47.080 align:start position:0%
up it's okay it's not the end of the
 

00:04:47.080 --> 00:04:49.860 align:start position:0%
up it's okay it's not the end of the
world<00:04:47.259><c> so</c><00:04:47.560><c> in</c><00:04:48.009><c> the</c><00:04:48.729><c> next</c><00:04:49.030><c> video</c><00:04:49.570><c> we're</c><00:04:49.720><c> gonna</c>

00:04:49.860 --> 00:04:49.870 align:start position:0%
world so in the next video we're gonna
 

00:04:49.870 --> 00:04:51.120 align:start position:0%
world so in the next video we're gonna
continue<00:04:50.080><c> a</c><00:04:50.350><c> little</c><00:04:50.530><c> more</c><00:04:50.800><c> about</c><00:04:50.979><c> the</c>

00:04:51.120 --> 00:04:51.130 align:start position:0%
continue a little more about the
 

00:04:51.130 --> 00:04:52.800 align:start position:0%
continue a little more about the
fundamentals<00:04:51.760><c> and</c><00:04:51.850><c> looking</c><00:04:52.270><c> at</c><00:04:52.360><c> some</c><00:04:52.570><c> use</c>

00:04:52.800 --> 00:04:52.810 align:start position:0%
fundamentals and looking at some use
 

00:04:52.810 --> 00:04:55.110 align:start position:0%
fundamentals and looking at some use
cases

