# YouTube Summaries Development Guide

## Quick Start

### 1. Install Dependencies

```bash
# Install NextJS dependencies
npm install

# Install Python service dependencies
cd python-ai-service
pip install -r requirements.txt
cd ..
```

### 2. Set Up Environment Variables

Copy `.env.local` and fill in your actual values:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Python AI Service Configuration
PYTHON_AI_SERVICE_URL=http://localhost:9000
PYTHON_AI_SERVICE_API_KEY=your_python_service_api_key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:9696
```

### 3. Set Up Database

Follow the instructions in `DATABASE_SETUP.md` to:
1. Enable pgvector extension in Supabase
2. Create your topic tables
3. Add the required SQL functions
4. Set up Row Level Security

### 4. Configure Python AI Service

Edit `python-ai-service/main.py`:
1. Update the imports to use your actual `gemini_methods`
2. Replace placeholder implementations in `/chat` and `/embeddings` endpoints
3. Configure authentication as needed

### 5. Start Development Servers

#### Terminal 1 - NextJS Frontend
```bash
npm run dev
```

#### Terminal 2 - Python AI Service
```bash
cd python-ai-service
python main.py
```

### 6. Access the Application

- Frontend: http://localhost:9696
- Python AI Service: http://localhost:9000
- API Docs: http://localhost:9000/docs

## Project Structure

```
nextjs-youtube-summaries/
├── src/
│   ├── app/                    # NextJS App Router pages
│   │   ├── api/               # API routes
│   │   ├── chat/              # Chat page
│   │   ├── search/            # Search page
│   │   ├── video/[topic]/[videoId]/ # Individual video pages
│   │   ├── layout.tsx         # Root layout
│   │   ├── page.tsx           # Home page
│   │   └── globals.css        # Global styles
│   ├── components/            # React components
│   │   ├── ui/               # UI components (Radix)
│   │   ├── AppLayout.tsx     # Main app layout
│   │   ├── VideoCard.tsx     # Video display component
│   │   ├── ChatInterface.tsx # Chat UI
│   │   ├── SearchComponent.tsx # Search UI
│   │   └── TopicSelector.tsx # Topic filter
│   ├── lib/                  # Utilities and services
│   │   ├── supabase.ts       # Supabase client
│   │   ├── database.ts       # Database operations
│   │   └── utils.ts          # Helper functions
│   └── types/                # TypeScript type definitions
│       └── index.ts
├── python-ai-service/        # Python FastAPI service
│   ├── main.py              # FastAPI app
│   └── requirements.txt     # Python dependencies
├── DATABASE_SETUP.md        # Database schema guide
├── DEVELOPMENT.md           # This file
└── .env.local              # Environment variables
```

## API Endpoints

### NextJS API Routes

- `GET /api/topics` - Get all available topic tables
- `GET /api/videos/recent` - Get recent videos
- `POST /api/search` - Search videos by semantic similarity
- `POST /api/chat` - Chat with AI assistant
- `POST /api/embeddings` - Generate embeddings (proxies to Python service)

### Python AI Service

- `GET /` - Health check
- `POST /chat` - Generate AI responses
- `POST /embeddings` - Generate text embeddings
- `GET /models` - List available models
- `GET /docs` - API documentation

## Development Tips

### Adding New Features

1. **New Components**: Add to `src/components/`
2. **New Pages**: Add to `src/app/`
3. **New API Routes**: Add to `src/app/api/`
4. **Database Operations**: Extend `src/lib/database.ts`
5. **Types**: Update `src/types/index.ts`

### Debugging

1. **Frontend Issues**: Check browser console and Network tab
2. **API Issues**: Check Next.js terminal output
3. **Python Service Issues**: Check Python service terminal output
4. **Database Issues**: Check Supabase logs

### Performance Optimization

1. Use React.memo for expensive components
2. Implement pagination for large video lists
3. Cache search results where appropriate
4. Optimize vector search with proper indexes

### Testing

```bash
# Run NextJS in development mode
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Lint code
npm run lint
```

## Deployment

### Frontend (Vercel)

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push

### Python Service (Railway/Render/Heroku)

1. Create a `Dockerfile` for the Python service
2. Set environment variables
3. Deploy to your preferred platform

### Database (Supabase)

Your Supabase database is already hosted. Make sure to:
1. Set up proper indexes for performance
2. Configure Row Level Security
3. Monitor usage and scaling

## Troubleshooting

### Common Issues

1. **CORS Errors**: Make sure Python service allows NextJS origin
2. **Database Connection**: Verify Supabase credentials
3. **Missing Dependencies**: Run `npm install` and `pip install -r requirements.txt`
4. **Port Conflicts**: Change ports in development if needed
5. **Environment Variables**: Double-check all required variables are set

### Getting Help

1. Check the console for error messages
2. Verify all services are running
3. Test API endpoints individually
4. Check database connectivity and schema
