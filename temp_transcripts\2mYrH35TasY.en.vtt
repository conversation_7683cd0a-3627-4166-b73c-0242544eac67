WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:02.310 align:start position:0%
 
So<00:00:00.640><c> how</c><00:00:00.880><c> do</c><00:00:00.960><c> you</c><00:00:01.120><c> know</c><00:00:01.280><c> if</c><00:00:01.520><c> your</c><00:00:01.760><c> land</c><00:00:02.000><c> is</c>

00:00:02.310 --> 00:00:02.320 align:start position:0%
So how do you know if your land is
 

00:00:02.320 --> 00:00:05.269 align:start position:0%
So how do you know if your land is
suitable<00:00:03.120><c> for</c><00:00:03.439><c> a</c><00:00:03.600><c> data</c><00:00:03.919><c> center</c><00:00:04.160><c> development?</c>

00:00:05.269 --> 00:00:05.279 align:start position:0%
suitable for a data center development?
 

00:00:05.279 --> 00:00:09.030 align:start position:0%
suitable for a data center development?
Some<00:00:05.759><c> key</c><00:00:06.080><c> factors</c><00:00:06.720><c> for</c><00:00:07.359><c> a</c><00:00:07.839><c> ideal</c><00:00:08.320><c> data</c><00:00:08.720><c> center</c>

00:00:09.030 --> 00:00:09.040 align:start position:0%
Some key factors for a ideal data center
 

00:00:09.040 --> 00:00:11.669 align:start position:0%
Some key factors for a ideal data center
location<00:00:10.400><c> is</c><00:00:10.719><c> again</c><00:00:11.040><c> you</c><00:00:11.280><c> have</c><00:00:11.440><c> your</c>

00:00:11.669 --> 00:00:11.679 align:start position:0%
location is again you have your
 

00:00:11.679 --> 00:00:15.190 align:start position:0%
location is again you have your
buildable<00:00:12.160><c> acreage</c><00:00:12.800><c> so</c><00:00:13.519><c> averaging</c><00:00:14.000><c> around</c><00:00:14.320><c> 40</c>

00:00:15.190 --> 00:00:15.200 align:start position:0%
buildable acreage so averaging around 40
 

00:00:15.200 --> 00:00:17.189 align:start position:0%
buildable acreage so averaging around 40
and<00:00:15.440><c> then</c><00:00:15.679><c> you</c><00:00:15.920><c> have</c><00:00:16.160><c> available</c><00:00:16.720><c> offtake</c>

00:00:17.189 --> 00:00:17.199 align:start position:0%
and then you have available offtake
 

00:00:17.199 --> 00:00:19.990 align:start position:0%
and then you have available offtake
capacity.<00:00:18.320><c> So</c><00:00:18.880><c> these</c><00:00:19.199><c> data</c><00:00:19.520><c> centers</c>

00:00:19.990 --> 00:00:20.000 align:start position:0%
capacity. So these data centers
 

00:00:20.000 --> 00:00:22.950 align:start position:0%
capacity. So these data centers
obviously<00:00:20.560><c> require</c><00:00:21.119><c> a</c><00:00:21.439><c> lot</c><00:00:21.760><c> of</c><00:00:22.400><c> electricity</c>

00:00:22.950 --> 00:00:22.960 align:start position:0%
obviously require a lot of electricity
 

00:00:22.960 --> 00:00:26.070 align:start position:0%
obviously require a lot of electricity
to<00:00:23.279><c> run.</c><00:00:24.160><c> Um,</c><00:00:24.480><c> I</c><00:00:24.800><c> think</c><00:00:24.960><c> an</c><00:00:25.279><c> average</c><00:00:25.680><c> hypers</c>

00:00:26.070 --> 00:00:26.080 align:start position:0%
to run. Um, I think an average hypers
 

00:00:26.080 --> 00:00:28.950 align:start position:0%
to run. Um, I think an average hypers
scale<00:00:26.480><c> data</c><00:00:26.800><c> center</c><00:00:27.119><c> can</c><00:00:27.359><c> use</c><00:00:27.680><c> around</c><00:00:28.640><c> the</c>

00:00:28.950 --> 00:00:28.960 align:start position:0%
scale data center can use around the
 

00:00:28.960 --> 00:00:31.589 align:start position:0%
scale data center can use around the
equivalent<00:00:29.519><c> of</c><00:00:29.760><c> 50,000</c><00:00:30.640><c> homes</c><00:00:31.119><c> worth</c><00:00:31.359><c> of</c>

00:00:31.589 --> 00:00:31.599 align:start position:0%
equivalent of 50,000 homes worth of
 

00:00:31.599 --> 00:00:34.630 align:start position:0%
equivalent of 50,000 homes worth of
electricity.<00:00:32.960><c> Um,</c><00:00:33.520><c> so</c><00:00:33.920><c> in</c><00:00:34.079><c> order</c><00:00:34.239><c> for</c><00:00:34.399><c> these</c>

00:00:34.630 --> 00:00:34.640 align:start position:0%
electricity. Um, so in order for these
 

00:00:34.640 --> 00:00:36.310 align:start position:0%
electricity. Um, so in order for these
to<00:00:34.800><c> be</c><00:00:34.960><c> built,</c><00:00:35.280><c> you</c><00:00:35.520><c> have</c><00:00:35.600><c> to</c><00:00:35.760><c> be</c><00:00:35.920><c> close</c><00:00:36.160><c> to</c>

00:00:36.310 --> 00:00:36.320 align:start position:0%
to be built, you have to be close to
 

00:00:36.320 --> 00:00:38.630 align:start position:0%
to be built, you have to be close to
what<00:00:36.480><c> we</c><00:00:36.640><c> call</c><00:00:36.800><c> a</c><00:00:36.960><c> substation.</c><00:00:37.920><c> A</c><00:00:38.160><c> substation</c>

00:00:38.630 --> 00:00:38.640 align:start position:0%
what we call a substation. A substation
 

00:00:38.640 --> 00:00:41.590 align:start position:0%
what we call a substation. A substation
is<00:00:38.879><c> a</c><00:00:39.040><c> facility</c><00:00:39.520><c> that</c><00:00:40.000><c> produces</c><00:00:40.640><c> electricity</c>

00:00:41.590 --> 00:00:41.600 align:start position:0%
is a facility that produces electricity
 

00:00:41.600 --> 00:00:44.310 align:start position:0%
is a facility that produces electricity
and<00:00:42.000><c> offtake</c><00:00:42.559><c> is</c><00:00:42.800><c> how</c><00:00:43.040><c> much</c><00:00:43.360><c> electricity</c><00:00:44.000><c> you</c>

00:00:44.310 --> 00:00:44.320 align:start position:0%
and offtake is how much electricity you
 

00:00:44.320 --> 00:00:47.430 align:start position:0%
and offtake is how much electricity you
can<00:00:44.480><c> pull</c><00:00:45.520><c> off</c><00:00:45.760><c> the</c><00:00:45.920><c> grid</c><00:00:46.239><c> from</c><00:00:46.559><c> that</c><00:00:46.879><c> location</c>

00:00:47.430 --> 00:00:47.440 align:start position:0%
can pull off the grid from that location
 

00:00:47.440 --> 00:00:49.430 align:start position:0%
can pull off the grid from that location
of<00:00:47.680><c> a</c><00:00:47.840><c> substation.</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
of a substation.
 

00:00:49.440 --> 00:00:52.630 align:start position:0%
of a substation.
Third,<00:00:49.840><c> you</c><00:00:50.079><c> have</c><00:00:50.640><c> your</c><00:00:50.879><c> fiber</c><00:00:51.200><c> optic</c><00:00:51.680><c> lines.</c>

00:00:52.630 --> 00:00:52.640 align:start position:0%
Third, you have your fiber optic lines.
 

00:00:52.640 --> 00:00:55.750 align:start position:0%
Third, you have your fiber optic lines.
These<00:00:53.039><c> lines</c><00:00:53.600><c> are</c><00:00:54.079><c> important</c><00:00:54.640><c> for</c><00:00:55.520><c> um</c>

00:00:55.750 --> 00:00:55.760 align:start position:0%
These lines are important for um
 

00:00:55.760 --> 00:00:57.350 align:start position:0%
These lines are important for um
transporting

00:00:57.350 --> 00:00:57.360 align:start position:0%
transporting
 

00:00:57.360 --> 00:00:59.670 align:start position:0%
transporting
the<00:00:57.840><c> internet.</c><00:00:58.559><c> So</c><00:00:58.800><c> these</c><00:00:59.039><c> are</c><00:00:59.199><c> the</c><00:00:59.359><c> ones</c><00:00:59.520><c> that</c>

00:00:59.670 --> 00:00:59.680 align:start position:0%
the internet. So these are the ones that
 

00:00:59.680 --> 00:01:01.910 align:start position:0%
the internet. So these are the ones that
will<00:00:59.920><c> be</c><00:01:00.079><c> responsible</c><00:01:00.640><c> for</c><00:01:00.879><c> bringing</c><00:01:01.520><c> in</c><00:01:01.760><c> the</c>

00:01:01.910 --> 00:01:01.920 align:start position:0%
will be responsible for bringing in the
 

00:01:01.920 --> 00:01:04.710 align:start position:0%
will be responsible for bringing in the
connection<00:01:02.480><c> and</c><00:01:02.960><c> taking</c><00:01:03.280><c> out</c><00:01:03.600><c> whatever</c><00:01:04.080><c> data</c>

00:01:04.710 --> 00:01:04.720 align:start position:0%
connection and taking out whatever data
 

00:01:04.720 --> 00:01:07.109 align:start position:0%
connection and taking out whatever data
is<00:01:04.960><c> stored</c><00:01:05.280><c> or</c><00:01:05.600><c> processed</c><00:01:06.159><c> from</c><00:01:06.400><c> these</c><00:01:06.799><c> data</c>

00:01:07.109 --> 00:01:07.119 align:start position:0%
is stored or processed from these data
 

00:01:07.119 --> 00:01:10.310 align:start position:0%
is stored or processed from these data
centers.<00:01:08.159><c> The</c><00:01:08.479><c> fourth</c><00:01:08.880><c> is</c><00:01:10.000><c> your</c>

00:01:10.310 --> 00:01:10.320 align:start position:0%
centers. The fourth is your
 

00:01:10.320 --> 00:01:12.950 align:start position:0%
centers. The fourth is your
environmental<00:01:10.880><c> impact</c><00:01:11.200><c> on</c><00:01:11.360><c> the</c><00:01:11.600><c> land.</c><00:01:12.720><c> Just</c>

00:01:12.950 --> 00:01:12.960 align:start position:0%
environmental impact on the land. Just
 

00:01:12.960 --> 00:01:14.870 align:start position:0%
environmental impact on the land. Just
so<00:01:13.200><c> for</c><00:01:13.439><c> everyone</c><00:01:13.760><c> listening</c><00:01:14.080><c> here,</c><00:01:14.640><c> this</c>

00:01:14.870 --> 00:01:14.880 align:start position:0%
so for everyone listening here, this
 

00:01:14.880 --> 00:01:16.550 align:start position:0%
so for everyone listening here, this
information<00:01:15.280><c> here,</c><00:01:15.760><c> some</c><00:01:15.920><c> of</c><00:01:16.000><c> you</c><00:01:16.159><c> might</c><00:01:16.320><c> be</c>

00:01:16.550 --> 00:01:16.560 align:start position:0%
information here, some of you might be
 

00:01:16.560 --> 00:01:18.789 align:start position:0%
information here, some of you might be
wondering,<00:01:16.960><c> well,</c><00:01:17.119><c> I</c><00:01:17.360><c> don't</c><00:01:17.439><c> really</c><00:01:17.759><c> know</c>

00:01:18.789 --> 00:01:18.799 align:start position:0%
wondering, well, I don't really know
 

00:01:18.799 --> 00:01:21.030 align:start position:0%
wondering, well, I don't really know
where<00:01:19.040><c> if</c><00:01:19.680><c> I</c><00:01:19.920><c> have</c><00:01:20.000><c> a</c><00:01:20.159><c> substation</c><00:01:20.640><c> by</c><00:01:20.799><c> my</c>

00:01:21.030 --> 00:01:21.040 align:start position:0%
where if I have a substation by my
 

00:01:21.040 --> 00:01:22.710 align:start position:0%
where if I have a substation by my
property<00:01:21.439><c> or</c><00:01:21.600><c> if</c><00:01:21.680><c> it</c><00:01:21.840><c> has</c><00:01:22.000><c> offtake</c><00:01:22.320><c> capacity</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
property or if it has offtake capacity
 

00:01:22.720 --> 00:01:24.550 align:start position:0%
property or if it has offtake capacity
or<00:01:22.960><c> there's</c><00:01:23.200><c> fiber</c><00:01:23.520><c> lines,</c><00:01:24.000><c> all</c><00:01:24.159><c> of</c><00:01:24.240><c> these</c>

00:01:24.550 --> 00:01:24.560 align:start position:0%
or there's fiber lines, all of these
 

00:01:24.560 --> 00:01:26.789 align:start position:0%
or there's fiber lines, all of these
techn<00:01:25.040><c> technical</c><00:01:25.520><c> questions</c><00:01:25.920><c> here.</c><00:01:26.400><c> Um,</c><00:01:26.640><c> this</c>

00:01:26.789 --> 00:01:26.799 align:start position:0%
techn technical questions here. Um, this
 

00:01:26.799 --> 00:01:28.630 align:start position:0%
techn technical questions here. Um, this
is<00:01:26.960><c> kind</c><00:01:27.040><c> of</c><00:01:27.119><c> a</c><00:01:27.280><c> highle</c><00:01:27.840><c> understanding</c><00:01:28.320><c> of</c>

00:01:28.630 --> 00:01:28.640 align:start position:0%
is kind of a highle understanding of
 

00:01:28.640 --> 00:01:30.550 align:start position:0%
is kind of a highle understanding of
what<00:01:28.960><c> data</c><00:01:29.360><c> center</c><00:01:29.600><c> developers</c><00:01:30.159><c> are</c><00:01:30.400><c> looking</c>

00:01:30.550 --> 00:01:30.560 align:start position:0%
what data center developers are looking
 

00:01:30.560 --> 00:01:34.390 align:start position:0%
what data center developers are looking
for.<00:01:31.439><c> Landgate</c><00:01:32.240><c> and</c><00:01:32.560><c> LANDA</c><00:01:32.880><c> app</c><00:01:33.840><c> brings</c><00:01:34.159><c> this</c>

00:01:34.390 --> 00:01:34.400 align:start position:0%
for. Landgate and LANDA app brings this
 

00:01:34.400 --> 00:01:36.390 align:start position:0%
for. Landgate and LANDA app brings this
to<00:01:34.640><c> you</c><00:01:34.799><c> in</c><00:01:35.040><c> a</c><00:01:35.200><c> really</c><00:01:35.360><c> synthesized</c><00:01:36.000><c> way</c><00:01:36.159><c> and</c>

00:01:36.390 --> 00:01:36.400 align:start position:0%
to you in a really synthesized way and
 

00:01:36.400 --> 00:01:38.469 align:start position:0%
to you in a really synthesized way and
actually<00:01:36.720><c> tells</c><00:01:36.960><c> you.</c><00:01:37.439><c> So</c><00:01:37.680><c> if</c><00:01:37.840><c> you</c><00:01:37.920><c> go</c><00:01:38.079><c> to</c><00:01:38.240><c> land</c>

00:01:38.469 --> 00:01:38.479 align:start position:0%
actually tells you. So if you go to land
 

00:01:38.479 --> 00:01:41.109 align:start position:0%
actually tells you. So if you go to land
app<00:01:38.720><c> and</c><00:01:38.880><c> you</c><00:01:39.040><c> click</c><00:01:39.200><c> on</c><00:01:39.360><c> your</c><00:01:39.520><c> parcel,</c><00:01:40.799><c> that</c>

00:01:41.109 --> 00:01:41.119 align:start position:0%
app and you click on your parcel, that
 

00:01:41.119 --> 00:01:43.510 align:start position:0%
app and you click on your parcel, that
data<00:01:41.520><c> center</c><00:01:41.840><c> index</c><00:01:42.320><c> is</c><00:01:42.560><c> going</c><00:01:42.640><c> to</c><00:01:42.720><c> tell</c><00:01:42.880><c> you</c>

00:01:43.510 --> 00:01:43.520 align:start position:0%
data center index is going to tell you
 

00:01:43.520 --> 00:01:45.590 align:start position:0%
data center index is going to tell you
how<00:01:43.840><c> close</c><00:01:44.079><c> you</c><00:01:44.320><c> are</c><00:01:44.400><c> to</c><00:01:44.640><c> all</c><00:01:44.799><c> of</c><00:01:44.880><c> these</c><00:01:45.200><c> things</c>

00:01:45.590 --> 00:01:45.600 align:start position:0%
how close you are to all of these things
 

00:01:45.600 --> 00:01:48.240 align:start position:0%
how close you are to all of these things
in<00:01:45.920><c> combination.</c>

