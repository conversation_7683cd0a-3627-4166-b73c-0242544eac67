WEBVTT
Kind: captions
Language: en

00:00:00.160 --> 00:00:01.589 align:start position:0%
 
welcome<00:00:00.399><c> back</c><00:00:00.520><c> to</c><00:00:00.640><c> another</c><00:00:00.919><c> video</c><00:00:01.199><c> and</c><00:00:01.319><c> today</c>

00:00:01.589 --> 00:00:01.599 align:start position:0%
welcome back to another video and today
 

00:00:01.599 --> 00:00:03.990 align:start position:0%
welcome back to another video and today
we're<00:00:01.760><c> going</c><00:00:01.839><c> to</c><00:00:01.959><c> be</c><00:00:02.120><c> creating</c><00:00:02.520><c> a</c><00:00:02.679><c> SAS</c><00:00:03.280><c> product</c>

00:00:03.990 --> 00:00:04.000 align:start position:0%
we're going to be creating a SAS product
 

00:00:04.000 --> 00:00:06.309 align:start position:0%
we're going to be creating a SAS product
with<00:00:04.240><c> AI</c><00:00:04.720><c> specifically</c><00:00:05.240><c> using</c><00:00:05.600><c> autogen</c><00:00:06.240><c> now</c>

00:00:06.309 --> 00:00:06.319 align:start position:0%
with AI specifically using autogen now
 

00:00:06.319 --> 00:00:07.429 align:start position:0%
with AI specifically using autogen now
You'<00:00:06.480><c> probably</c><00:00:06.640><c> seen</c><00:00:06.759><c> a</c><00:00:06.839><c> lot</c><00:00:06.960><c> of</c><00:00:07.040><c> videos</c><00:00:07.319><c> that</c>

00:00:07.429 --> 00:00:07.439 align:start position:0%
You' probably seen a lot of videos that
 

00:00:07.439 --> 00:00:09.790 align:start position:0%
You' probably seen a lot of videos that
create<00:00:07.720><c> snake</c><00:00:08.200><c> or</c><00:00:08.480><c> pong</c><00:00:08.840><c> and</c><00:00:09.120><c> that's</c><00:00:09.320><c> great</c>

00:00:09.790 --> 00:00:09.800 align:start position:0%
create snake or pong and that's great
 

00:00:09.800 --> 00:00:11.110 align:start position:0%
create snake or pong and that's great
but<00:00:09.920><c> I</c><00:00:10.000><c> want</c><00:00:10.080><c> to</c><00:00:10.200><c> change</c><00:00:10.480><c> that</c><00:00:10.679><c> and</c><00:00:10.840><c> let's</c>

00:00:11.110 --> 00:00:11.120 align:start position:0%
but I want to change that and let's
 

00:00:11.120 --> 00:00:13.190 align:start position:0%
but I want to change that and let's
create<00:00:11.639><c> uh</c><00:00:11.759><c> an</c><00:00:11.920><c> actual</c><00:00:12.559><c> product</c><00:00:13.040><c> that</c>

00:00:13.190 --> 00:00:13.200 align:start position:0%
create uh an actual product that
 

00:00:13.200 --> 00:00:14.910 align:start position:0%
create uh an actual product that
somebody<00:00:13.519><c> can</c><00:00:13.719><c> use</c><00:00:14.280><c> well</c><00:00:14.440><c> let's</c><00:00:14.599><c> go</c><00:00:14.679><c> over</c><00:00:14.799><c> the</c>

00:00:14.910 --> 00:00:14.920 align:start position:0%
somebody can use well let's go over the
 

00:00:14.920 --> 00:00:16.390 align:start position:0%
somebody can use well let's go over the
agents<00:00:15.280><c> and</c><00:00:15.480><c> see</c><00:00:15.599><c> how</c><00:00:15.679><c> it's</c><00:00:15.799><c> going</c><00:00:15.879><c> to</c><00:00:16.000><c> work</c>

00:00:16.390 --> 00:00:16.400 align:start position:0%
agents and see how it's going to work
 

00:00:16.400 --> 00:00:19.109 align:start position:0%
agents and see how it's going to work
okay<00:00:16.480><c> so</c><00:00:16.640><c> the</c><00:00:16.760><c> first</c><00:00:17.000><c> agent</c><00:00:17.359><c> is</c><00:00:17.600><c> you</c><00:00:18.080><c> the</c><00:00:18.240><c> admin</c>

00:00:19.109 --> 00:00:19.119 align:start position:0%
okay so the first agent is you the admin
 

00:00:19.119 --> 00:00:20.790 align:start position:0%
okay so the first agent is you the admin
and<00:00:19.279><c> then</c><00:00:19.400><c> we're</c><00:00:19.560><c> going</c><00:00:19.640><c> to</c><00:00:19.800><c> have</c><00:00:20.080><c> a</c><00:00:20.279><c> planner</c>

00:00:20.790 --> 00:00:20.800 align:start position:0%
and then we're going to have a planner
 

00:00:20.800 --> 00:00:22.349 align:start position:0%
and then we're going to have a planner
who's<00:00:21.039><c> going</c><00:00:21.160><c> to</c><00:00:21.279><c> plan</c><00:00:21.600><c> everything</c><00:00:21.920><c> else</c><00:00:22.119><c> for</c>

00:00:22.349 --> 00:00:22.359 align:start position:0%
who's going to plan everything else for
 

00:00:22.359 --> 00:00:23.830 align:start position:0%
who's going to plan everything else for
the<00:00:22.640><c> other</c><00:00:22.880><c> agents</c><00:00:23.519><c> then</c><00:00:23.599><c> we're</c><00:00:23.720><c> going</c><00:00:23.800><c> to</c>

00:00:23.830 --> 00:00:23.840 align:start position:0%
the other agents then we're going to
 

00:00:23.840 --> 00:00:25.230 align:start position:0%
the other agents then we're going to
have<00:00:23.920><c> the</c><00:00:24.039><c> engineer</c><00:00:24.400><c> who</c><00:00:24.599><c> actually</c><00:00:24.880><c> writes</c>

00:00:25.230 --> 00:00:25.240 align:start position:0%
have the engineer who actually writes
 

00:00:25.240 --> 00:00:27.029 align:start position:0%
have the engineer who actually writes
the<00:00:25.400><c> code</c><00:00:25.960><c> the</c><00:00:26.160><c> executive</c><00:00:26.679><c> which</c><00:00:26.800><c> is</c><00:00:26.880><c> really</c>

00:00:27.029 --> 00:00:27.039 align:start position:0%
the code the executive which is really
 

00:00:27.039 --> 00:00:29.029 align:start position:0%
the code the executive which is really
going<00:00:27.160><c> to</c><00:00:27.279><c> take</c><00:00:27.480><c> that</c><00:00:27.640><c> code</c><00:00:27.920><c> and</c><00:00:28.199><c> test</c><00:00:28.480><c> it</c><00:00:28.920><c> and</c>

00:00:29.029 --> 00:00:29.039 align:start position:0%
going to take that code and test it and
 

00:00:29.039 --> 00:00:31.390 align:start position:0%
going to take that code and test it and
then<00:00:29.199><c> the</c><00:00:29.400><c> critic</c><00:00:30.080><c> somebody</c><00:00:30.320><c> going</c><00:00:30.439><c> to</c><00:00:30.560><c> review</c>

00:00:31.390 --> 00:00:31.400 align:start position:0%
then the critic somebody going to review
 

00:00:31.400 --> 00:00:33.350 align:start position:0%
then the critic somebody going to review
all<00:00:31.560><c> the</c><00:00:31.679><c> other</c><00:00:31.880><c> AI</c><00:00:32.160><c> agents</c><00:00:32.599><c> what</c><00:00:32.719><c> they</c><00:00:32.880><c> did</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
all the other AI agents what they did
 

00:00:33.360 --> 00:00:34.869 align:start position:0%
all the other AI agents what they did
then<00:00:33.600><c> give</c><00:00:33.840><c> feedback</c><00:00:34.280><c> and</c><00:00:34.399><c> then</c><00:00:34.559><c> hopefully</c>

00:00:34.869 --> 00:00:34.879 align:start position:0%
then give feedback and then hopefully
 

00:00:34.879 --> 00:00:36.750 align:start position:0%
then give feedback and then hopefully
the<00:00:35.000><c> other</c><00:00:35.239><c> AI</c><00:00:35.520><c> agents</c><00:00:35.879><c> take</c><00:00:36.040><c> that</c><00:00:36.200><c> feedback</c>

00:00:36.750 --> 00:00:36.760 align:start position:0%
the other AI agents take that feedback
 

00:00:36.760 --> 00:00:39.110 align:start position:0%
the other AI agents take that feedback
and<00:00:37.160><c> you</c><00:00:37.280><c> know</c><00:00:38.000><c> do</c><00:00:38.160><c> something</c><00:00:38.440><c> with</c><00:00:38.559><c> it</c><00:00:38.960><c> now</c>

00:00:39.110 --> 00:00:39.120 align:start position:0%
and you know do something with it now
 

00:00:39.120 --> 00:00:40.389 align:start position:0%
and you know do something with it now
the<00:00:39.320><c> task</c><00:00:39.520><c> for</c><00:00:39.680><c> today</c><00:00:39.960><c> is</c><00:00:40.079><c> we're</c><00:00:40.200><c> going</c><00:00:40.280><c> to</c>

00:00:40.389 --> 00:00:40.399 align:start position:0%
the task for today is we're going to
 

00:00:40.399 --> 00:00:42.869 align:start position:0%
the task for today is we're going to
create<00:00:40.680><c> a</c><00:00:40.800><c> flask</c><00:00:41.239><c> app</c><00:00:41.399><c> for</c><00:00:41.600><c> customer</c><00:00:42.000><c> surveys</c>

00:00:42.869 --> 00:00:42.879 align:start position:0%
create a flask app for customer surveys
 

00:00:42.879 --> 00:00:44.190 align:start position:0%
create a flask app for customer surveys
this<00:00:43.000><c> is</c><00:00:43.079><c> a</c><00:00:43.239><c> simple</c><00:00:43.520><c> application</c><00:00:44.000><c> that</c><00:00:44.079><c> we're</c>

00:00:44.190 --> 00:00:44.200 align:start position:0%
this is a simple application that we're
 

00:00:44.200 --> 00:00:45.790 align:start position:0%
this is a simple application that we're
going<00:00:44.280><c> to</c><00:00:44.320><c> be</c><00:00:44.399><c> able</c><00:00:44.559><c> to</c><00:00:44.640><c> run</c><00:00:44.840><c> locally</c><00:00:45.559><c> we</c><00:00:45.680><c> have</c>

00:00:45.790 --> 00:00:45.800 align:start position:0%
going to be able to run locally we have
 

00:00:45.800 --> 00:00:48.350 align:start position:0%
going to be able to run locally we have
a<00:00:45.960><c> local</c><00:00:46.440><c> database</c><00:00:47.039><c> within</c><00:00:47.239><c> the</c><00:00:47.360><c> flask</c><00:00:47.800><c> app</c>

00:00:48.350 --> 00:00:48.360 align:start position:0%
a local database within the flask app
 

00:00:48.360 --> 00:00:50.229 align:start position:0%
a local database within the flask app
and<00:00:48.520><c> it's</c><00:00:48.680><c> going</c><00:00:48.760><c> to</c><00:00:48.920><c> have</c><00:00:49.079><c> a</c><00:00:49.280><c> simple</c><00:00:49.640><c> homepage</c>

00:00:50.229 --> 00:00:50.239 align:start position:0%
and it's going to have a simple homepage
 

00:00:50.239 --> 00:00:52.310 align:start position:0%
and it's going to have a simple homepage
where<00:00:50.520><c> a</c><00:00:50.640><c> customer</c><00:00:51.039><c> can</c><00:00:51.239><c> take</c><00:00:51.559><c> a</c><00:00:51.960><c> take</c><00:00:52.120><c> a</c>

00:00:52.310 --> 00:00:52.320 align:start position:0%
where a customer can take a take a
 

00:00:52.320 --> 00:00:54.150 align:start position:0%
where a customer can take a take a
simple<00:00:52.640><c> question</c><00:00:53.399><c> answer</c><00:00:53.680><c> it</c><00:00:53.840><c> and</c><00:00:53.960><c> then</c>

00:00:54.150 --> 00:00:54.160 align:start position:0%
simple question answer it and then
 

00:00:54.160 --> 00:00:56.750 align:start position:0%
simple question answer it and then
submit<00:00:54.480><c> the</c><00:00:54.600><c> survey</c><00:00:55.320><c> it'll</c><00:00:55.600><c> redirect</c><00:00:56.120><c> them</c><00:00:56.320><c> to</c>

00:00:56.750 --> 00:00:56.760 align:start position:0%
submit the survey it'll redirect them to
 

00:00:56.760 --> 00:00:58.310 align:start position:0%
submit the survey it'll redirect them to
a<00:00:56.920><c> thank</c><00:00:57.079><c> you</c><00:00:57.280><c> page</c><00:00:57.520><c> and</c><00:00:57.760><c> when</c><00:00:57.879><c> it</c><00:00:57.960><c> does</c><00:00:58.120><c> that</c>

00:00:58.310 --> 00:00:58.320 align:start position:0%
a thank you page and when it does that
 

00:00:58.320 --> 00:01:01.110 align:start position:0%
a thank you page and when it does that
it<00:00:58.480><c> also</c><00:00:59.000><c> saves</c><00:01:00.039><c> uh</c><00:01:00.280><c> the</c><00:01:00.399><c> customer</c><00:01:00.760><c> survey</c>

00:01:01.110 --> 00:01:01.120 align:start position:0%
it also saves uh the customer survey
 

00:01:01.120 --> 00:01:03.830 align:start position:0%
it also saves uh the customer survey
into<00:01:01.399><c> the</c><00:01:01.640><c> SQL</c><00:01:02.160><c> like</c><00:01:02.359><c> database</c><00:01:03.320><c> and</c><00:01:03.559><c> then</c>

00:01:03.830 --> 00:01:03.840 align:start position:0%
into the SQL like database and then
 

00:01:03.840 --> 00:01:06.109 align:start position:0%
into the SQL like database and then
lastly<00:01:04.199><c> we'll</c><00:01:04.400><c> have</c><00:01:04.559><c> an</c><00:01:04.799><c> admin</c><00:01:05.320><c> page</c><00:01:06.000><c> that</c>

00:01:06.109 --> 00:01:06.119 align:start position:0%
lastly we'll have an admin page that
 

00:01:06.119 --> 00:01:07.950 align:start position:0%
lastly we'll have an admin page that
will<00:01:06.320><c> have</c><00:01:06.560><c> all</c><00:01:06.720><c> the</c><00:01:06.960><c> results</c><00:01:07.320><c> from</c><00:01:07.560><c> all</c><00:01:07.799><c> the</c>

00:01:07.950 --> 00:01:07.960 align:start position:0%
will have all the results from all the
 

00:01:07.960 --> 00:01:09.830 align:start position:0%
will have all the results from all the
surveys<00:01:08.360><c> that</c><00:01:08.439><c> were</c><00:01:08.640><c> taken</c><00:01:09.040><c> to</c><00:01:09.200><c> put</c><00:01:09.400><c> simply</c><00:01:09.759><c> we</c>

00:01:09.830 --> 00:01:09.840 align:start position:0%
surveys that were taken to put simply we
 

00:01:09.840 --> 00:01:12.429 align:start position:0%
surveys that were taken to put simply we
want<00:01:09.960><c> to</c><00:01:10.159><c> have</c><00:01:10.360><c> a</c><00:01:10.520><c> flask</c><00:01:10.920><c> app</c><00:01:11.479><c> a</c><00:01:11.680><c> database</c><00:01:12.320><c> and</c>

00:01:12.429 --> 00:01:12.439 align:start position:0%
want to have a flask app a database and
 

00:01:12.439 --> 00:01:14.630 align:start position:0%
want to have a flask app a database and
then<00:01:12.600><c> three</c><00:01:12.840><c> HTML</c><00:01:13.360><c> Pages</c><00:01:13.880><c> the</c><00:01:14.040><c> goal</c><00:01:14.360><c> is</c><00:01:14.439><c> to</c>

00:01:14.630 --> 00:01:14.640 align:start position:0%
then three HTML Pages the goal is to
 

00:01:14.640 --> 00:01:16.950 align:start position:0%
then three HTML Pages the goal is to
give<00:01:14.799><c> you</c><00:01:15.040><c> the</c><00:01:15.240><c> ideas</c><00:01:15.640><c> on</c><00:01:15.960><c> how</c><00:01:16.080><c> to</c><00:01:16.280><c> do</c><00:01:16.520><c> this</c><00:01:16.880><c> how</c>

00:01:16.950 --> 00:01:16.960 align:start position:0%
give you the ideas on how to do this how
 

00:01:16.960 --> 00:01:18.590 align:start position:0%
give you the ideas on how to do this how
to<00:01:17.200><c> create</c><00:01:17.560><c> the</c><00:01:17.799><c> product</c><00:01:18.119><c> all</c><00:01:18.240><c> right</c><00:01:18.360><c> I</c><00:01:18.439><c> hope</c>

00:01:18.590 --> 00:01:18.600 align:start position:0%
to create the product all right I hope
 

00:01:18.600 --> 00:01:20.270 align:start position:0%
to create the product all right I hope
you<00:01:18.680><c> can</c><00:01:18.840><c> take</c><00:01:19.000><c> this</c><00:01:19.119><c> and</c><00:01:19.280><c> do</c><00:01:19.479><c> something</c><00:01:20.000><c> far</c>

00:01:20.270 --> 00:01:20.280 align:start position:0%
you can take this and do something far
 

00:01:20.280 --> 00:01:21.630 align:start position:0%
you can take this and do something far
better<00:01:20.680><c> I</c><00:01:20.799><c> want</c><00:01:21.079><c> I</c><00:01:21.119><c> want</c><00:01:21.200><c> to</c><00:01:21.280><c> get</c><00:01:21.400><c> the</c><00:01:21.439><c> ball</c>

00:01:21.630 --> 00:01:21.640 align:start position:0%
better I want I want to get the ball
 

00:01:21.640 --> 00:01:23.469 align:start position:0%
better I want I want to get the ball
rolling<00:01:22.240><c> so</c><00:01:22.439><c> that</c><00:01:22.560><c> you</c><00:01:22.720><c> have</c><00:01:22.840><c> an</c><00:01:22.960><c> idea</c><00:01:23.240><c> how</c><00:01:23.360><c> to</c>

00:01:23.469 --> 00:01:23.479 align:start position:0%
rolling so that you have an idea how to
 

00:01:23.479 --> 00:01:25.109 align:start position:0%
rolling so that you have an idea how to
use<00:01:23.680><c> the</c><00:01:23.880><c> agents</c><00:01:24.159><c> and</c><00:01:24.320><c> the</c><00:01:24.479><c> prompts</c><00:01:24.840><c> that</c><00:01:24.960><c> I</c>

00:01:25.109 --> 00:01:25.119 align:start position:0%
use the agents and the prompts that I
 

00:01:25.119 --> 00:01:27.590 align:start position:0%
use the agents and the prompts that I
used<00:01:25.759><c> to</c><00:01:26.079><c> create</c><00:01:26.439><c> this</c><00:01:26.840><c> okay</c><00:01:27.200><c> let's</c><00:01:27.360><c> look</c><00:01:27.479><c> at</c>

00:01:27.590 --> 00:01:27.600 align:start position:0%
used to create this okay let's look at
 

00:01:27.600 --> 00:01:28.950 align:start position:0%
used to create this okay let's look at
the<00:01:27.680><c> actual</c><00:01:27.920><c> code</c><00:01:28.159><c> now</c><00:01:28.360><c> and</c><00:01:28.479><c> see</c><00:01:28.640><c> how</c><00:01:28.759><c> it</c><00:01:28.799><c> was</c>

00:01:28.950 --> 00:01:28.960 align:start position:0%
the actual code now and see how it was
 

00:01:28.960 --> 00:01:30.950 align:start position:0%
the actual code now and see how it was
done<00:01:29.439><c> all</c><00:01:29.520><c> right</c><00:01:29.680><c> so</c><00:01:30.000><c> starting</c><00:01:30.280><c> off</c><00:01:30.520><c> with</c><00:01:30.680><c> the</c>

00:01:30.950 --> 00:01:30.960 align:start position:0%
done all right so starting off with the
 

00:01:30.960 --> 00:01:33.230 align:start position:0%
done all right so starting off with the
open<00:01:31.280><c> AI</c><00:01:31.680><c> configuration</c><00:01:32.479><c> we</c><00:01:32.600><c> need</c><00:01:32.880><c> just</c><00:01:33.040><c> two</c>

00:01:33.230 --> 00:01:33.240 align:start position:0%
open AI configuration we need just two
 

00:01:33.240 --> 00:01:35.429 align:start position:0%
open AI configuration we need just two
things<00:01:33.439><c> for</c><00:01:33.640><c> this</c><00:01:33.759><c> one</c><00:01:34.320><c> uh</c><00:01:34.439><c> we</c><00:01:34.520><c> need</c><00:01:34.680><c> the</c><00:01:34.799><c> model</c>

00:01:35.429 --> 00:01:35.439 align:start position:0%
things for this one uh we need the model
 

00:01:35.439 --> 00:01:38.429 align:start position:0%
things for this one uh we need the model
that<00:01:35.759><c> I</c><00:01:35.920><c> just</c><00:01:36.079><c> chose</c><00:01:36.399><c> 3.5</c><00:01:37.079><c> turbo</c><00:01:37.720><c> I</c><00:01:37.799><c> have</c><00:01:38.000><c> no</c>

00:01:38.429 --> 00:01:38.439 align:start position:0%
that I just chose 3.5 turbo I have no
 

00:01:38.439 --> 00:01:40.630 align:start position:0%
that I just chose 3.5 turbo I have no
particular<00:01:38.960><c> reason</c><00:01:39.320><c> why</c><00:01:39.479><c> I</c><00:01:39.560><c> chose</c><00:01:39.840><c> it</c><00:01:40.360><c> I</c><00:01:40.479><c> just</c>

00:01:40.630 --> 00:01:40.640 align:start position:0%
particular reason why I chose it I just
 

00:01:40.640 --> 00:01:41.870 align:start position:0%
particular reason why I chose it I just
did<00:01:40.880><c> because</c><00:01:41.079><c> I</c><00:01:41.200><c> like</c><00:01:41.360><c> messing</c><00:01:41.640><c> around</c>

00:01:41.870 --> 00:01:41.880 align:start position:0%
did because I like messing around
 

00:01:41.880 --> 00:01:43.190 align:start position:0%
did because I like messing around
messing<00:01:42.119><c> around</c><00:01:42.280><c> with</c><00:01:42.439><c> different</c><00:01:42.640><c> models</c><00:01:42.960><c> and</c>

00:01:43.190 --> 00:01:43.200 align:start position:0%
messing around with different models and
 

00:01:43.200 --> 00:01:45.149 align:start position:0%
messing around with different models and
seeing<00:01:43.479><c> the</c><00:01:43.600><c> results</c><00:01:43.960><c> that</c><00:01:44.040><c> it</c><00:01:44.159><c> gives</c><00:01:44.360><c> me</c><00:01:45.000><c> and</c>

00:01:45.149 --> 00:01:45.159 align:start position:0%
seeing the results that it gives me and
 

00:01:45.159 --> 00:01:47.429 align:start position:0%
seeing the results that it gives me and
then<00:01:45.320><c> you</c><00:01:45.520><c> also</c><00:01:45.880><c> just</c><00:01:46.000><c> need</c><00:01:46.200><c> your</c><00:01:46.399><c> API</c><00:01:46.840><c> key</c><00:01:47.320><c> and</c>

00:01:47.429 --> 00:01:47.439 align:start position:0%
then you also just need your API key and
 

00:01:47.439 --> 00:01:49.350 align:start position:0%
then you also just need your API key and
now<00:01:47.600><c> we</c><00:01:47.719><c> have</c><00:01:47.840><c> our</c><00:01:48.079><c> python</c><00:01:48.799><c> file</c><00:01:49.119><c> that</c><00:01:49.240><c> we're</c>

00:01:49.350 --> 00:01:49.360 align:start position:0%
now we have our python file that we're
 

00:01:49.360 --> 00:01:51.510 align:start position:0%
now we have our python file that we're
going<00:01:49.439><c> to</c><00:01:49.600><c> actually</c><00:01:49.880><c> run</c><00:01:50.240><c> for</c><00:01:50.439><c> autogen</c><00:01:51.240><c> so</c><00:01:51.399><c> the</c>

00:01:51.510 --> 00:01:51.520 align:start position:0%
going to actually run for autogen so the
 

00:01:51.520 --> 00:01:53.870 align:start position:0%
going to actually run for autogen so the
first<00:01:51.759><c> thing</c><00:01:52.000><c> is</c><00:01:52.280><c> we</c><00:01:52.399><c> have</c><00:01:52.520><c> our</c><00:01:52.759><c> Imports</c><00:01:53.640><c> we</c>

00:01:53.870 --> 00:01:53.880 align:start position:0%
first thing is we have our Imports we
 

00:01:53.880 --> 00:01:56.109 align:start position:0%
first thing is we have our Imports we
have<00:01:54.040><c> a</c><00:01:54.479><c> config</c><00:01:54.920><c> list</c><00:01:55.240><c> variable</c><00:01:55.719><c> that</c><00:01:55.840><c> holds</c>

00:01:56.109 --> 00:01:56.119 align:start position:0%
have a config list variable that holds
 

00:01:56.119 --> 00:01:58.670 align:start position:0%
have a config list variable that holds
the<00:01:56.280><c> configuration</c><00:01:56.960><c> from</c><00:01:57.240><c> open</c><00:01:57.600><c> Ai</c><00:01:58.479><c> and</c><00:01:58.560><c> then</c>

00:01:58.670 --> 00:01:58.680 align:start position:0%
the configuration from open Ai and then
 

00:01:58.680 --> 00:02:01.350 align:start position:0%
the configuration from open Ai and then
we<00:01:58.799><c> have</c><00:01:58.920><c> our</c><00:01:59.119><c> llm</c><00:01:59.880><c> config</c><00:02:00.719><c> which</c><00:02:00.960><c> the</c><00:02:01.079><c> first</c>

00:02:01.350 --> 00:02:01.360 align:start position:0%
we have our llm config which the first
 

00:02:01.360 --> 00:02:03.910 align:start position:0%
we have our llm config which the first
parameter<00:02:02.039><c> takes</c><00:02:02.399><c> in</c><00:02:03.039><c> the</c><00:02:03.280><c> open</c><00:02:03.600><c> AI</c>

00:02:03.910 --> 00:02:03.920 align:start position:0%
parameter takes in the open AI
 

00:02:03.920 --> 00:02:06.389 align:start position:0%
parameter takes in the open AI
configuration<00:02:04.759><c> and</c><00:02:04.880><c> then</c><00:02:05.000><c> we</c><00:02:05.159><c> have</c><00:02:05.399><c> seed</c><00:02:06.200><c> now</c>

00:02:06.389 --> 00:02:06.399 align:start position:0%
configuration and then we have seed now
 

00:02:06.399 --> 00:02:07.350 align:start position:0%
configuration and then we have seed now
if<00:02:06.520><c> you</c><00:02:06.600><c> don't</c><00:02:06.719><c> know</c><00:02:06.799><c> what</c><00:02:06.920><c> this</c><00:02:07.039><c> is</c><00:02:07.200><c> I'll</c>

00:02:07.350 --> 00:02:07.360 align:start position:0%
if you don't know what this is I'll
 

00:02:07.360 --> 00:02:10.469 align:start position:0%
if you don't know what this is I'll
explain<00:02:07.600><c> this</c><00:02:07.799><c> really</c><00:02:08.000><c> quick</c><00:02:08.800><c> uh</c><00:02:09.479><c> basically</c>

00:02:10.469 --> 00:02:10.479 align:start position:0%
explain this really quick uh basically
 

00:02:10.479 --> 00:02:12.430 align:start position:0%
explain this really quick uh basically
by<00:02:10.640><c> default</c><00:02:11.000><c> I</c><00:02:11.080><c> believe</c><00:02:11.280><c> the</c><00:02:11.400><c> number</c><00:02:11.560><c> is</c><00:02:11.720><c> 42</c>

00:02:12.430 --> 00:02:12.440 align:start position:0%
by default I believe the number is 42
 

00:02:12.440 --> 00:02:14.630 align:start position:0%
by default I believe the number is 42
and<00:02:12.640><c> what</c><00:02:12.800><c> this</c><00:02:12.920><c> is</c><00:02:13.040><c> going</c><00:02:13.120><c> to</c><00:02:13.280><c> do</c><00:02:14.160><c> is</c><00:02:14.360><c> whenever</c>

00:02:14.630 --> 00:02:14.640 align:start position:0%
and what this is going to do is whenever
 

00:02:14.640 --> 00:02:16.110 align:start position:0%
and what this is going to do is whenever
you<00:02:14.760><c> run</c><00:02:15.000><c> this</c><00:02:15.120><c> for</c><00:02:15.280><c> the</c><00:02:15.400><c> first</c><00:02:15.640><c> time</c><00:02:16.000><c> it's</c>

00:02:16.110 --> 00:02:16.120 align:start position:0%
you run this for the first time it's
 

00:02:16.120 --> 00:02:17.990 align:start position:0%
you run this for the first time it's
going<00:02:16.239><c> to</c><00:02:16.360><c> come</c><00:02:16.560><c> over</c><00:02:16.840><c> here</c><00:02:17.160><c> in</c><00:02:17.280><c> your</c><00:02:17.599><c> project</c>

00:02:17.990 --> 00:02:18.000 align:start position:0%
going to come over here in your project
 

00:02:18.000 --> 00:02:19.910 align:start position:0%
going to come over here in your project
directory<00:02:18.760><c> and</c><00:02:18.840><c> it's</c><00:02:18.959><c> going</c><00:02:19.080><c> to</c><00:02:19.280><c> create</c><00:02:19.680><c> this</c>

00:02:19.910 --> 00:02:19.920 align:start position:0%
directory and it's going to create this
 

00:02:19.920 --> 00:02:22.110 align:start position:0%
directory and it's going to create this
cache<00:02:20.200><c> folder</c><00:02:20.959><c> so</c><00:02:21.440><c> I</c><00:02:21.560><c> started</c><00:02:21.840><c> out</c><00:02:21.920><c> for</c><00:02:22.040><c> the</c>

00:02:22.110 --> 00:02:22.120 align:start position:0%
cache folder so I started out for the
 

00:02:22.120 --> 00:02:23.990 align:start position:0%
cache folder so I started out for the
first<00:02:22.280><c> time</c><00:02:22.400><c> when</c><00:02:22.560><c> seed</c><00:02:22.840><c> was</c><00:02:22.959><c> 42</c><00:02:23.720><c> it's</c><00:02:23.879><c> going</c>

00:02:23.990 --> 00:02:24.000 align:start position:0%
first time when seed was 42 it's going
 

00:02:24.000 --> 00:02:26.949 align:start position:0%
first time when seed was 42 it's going
to<00:02:24.160><c> create</c><00:02:24.920><c> uh</c><00:02:25.040><c> under</c><00:02:25.480><c> the</c><00:02:25.680><c> cache</c><00:02:25.959><c> directory</c>

00:02:26.949 --> 00:02:26.959 align:start position:0%
to create uh under the cache directory
 

00:02:26.959 --> 00:02:29.589 align:start position:0%
to create uh under the cache directory
another<00:02:27.319><c> directory</c><00:02:28.280><c> uh</c><00:02:28.440><c> with</c><00:02:28.599><c> called</c><00:02:28.800><c> 42</c><00:02:29.440><c> and</c>

00:02:29.589 --> 00:02:29.599 align:start position:0%
another directory uh with called 42 and
 

00:02:29.599 --> 00:02:32.670 align:start position:0%
another directory uh with called 42 and
what<00:02:29.879><c> this</c><00:02:30.040><c> does</c><00:02:30.480><c> it</c><00:02:30.640><c> has</c><00:02:30.760><c> a</c><00:02:31.040><c> database</c><00:02:31.879><c> of</c><00:02:32.120><c> the</c>

00:02:32.670 --> 00:02:32.680 align:start position:0%
what this does it has a database of the
 

00:02:32.680 --> 00:02:34.710 align:start position:0%
what this does it has a database of the
conversations<00:02:33.680><c> of</c><00:02:33.840><c> whenever</c><00:02:34.120><c> you</c><00:02:34.280><c> ran</c><00:02:34.599><c> this</c>

00:02:34.710 --> 00:02:34.720 align:start position:0%
conversations of whenever you ran this
 

00:02:34.720 --> 00:02:36.790 align:start position:0%
conversations of whenever you ran this
for<00:02:34.879><c> the</c><00:02:35.040><c> first</c><00:02:35.319><c> time</c><00:02:36.080><c> which</c><00:02:36.239><c> means</c><00:02:36.560><c> if</c><00:02:36.680><c> you</c>

00:02:36.790 --> 00:02:36.800 align:start position:0%
for the first time which means if you
 

00:02:36.800 --> 00:02:39.750 align:start position:0%
for the first time which means if you
run<00:02:37.000><c> it</c><00:02:37.120><c> for</c><00:02:37.319><c> the</c><00:02:37.480><c> second</c><00:02:37.879><c> time</c><00:02:38.680><c> it's</c><00:02:38.879><c> going</c><00:02:39.000><c> to</c>

00:02:39.750 --> 00:02:39.760 align:start position:0%
run it for the second time it's going to
 

00:02:39.760 --> 00:02:42.670 align:start position:0%
run it for the second time it's going to
quickly<00:02:40.319><c> populate</c><00:02:41.159><c> your</c><00:02:41.440><c> terminal</c><00:02:42.239><c> with</c><00:02:42.480><c> the</c>

00:02:42.670 --> 00:02:42.680 align:start position:0%
quickly populate your terminal with the
 

00:02:42.680 --> 00:02:44.750 align:start position:0%
quickly populate your terminal with the
conversations<00:02:43.360><c> that</c><00:02:43.480><c> you've</c><00:02:43.760><c> already</c><00:02:44.280><c> had</c>

00:02:44.750 --> 00:02:44.760 align:start position:0%
conversations that you've already had
 

00:02:44.760 --> 00:02:46.550 align:start position:0%
conversations that you've already had
with<00:02:44.920><c> the</c><00:02:45.080><c> agents</c><00:02:45.480><c> because</c><00:02:45.720><c> it</c><00:02:45.920><c> already</c><00:02:46.200><c> knows</c>

00:02:46.550 --> 00:02:46.560 align:start position:0%
with the agents because it already knows
 

00:02:46.560 --> 00:02:48.750 align:start position:0%
with the agents because it already knows
what<00:02:46.720><c> they</c><00:02:46.879><c> are</c><00:02:47.680><c> if</c><00:02:47.800><c> you</c><00:02:47.920><c> want</c><00:02:48.040><c> to</c><00:02:48.319><c> have</c>

00:02:48.750 --> 00:02:48.760 align:start position:0%
what they are if you want to have
 

00:02:48.760 --> 00:02:51.229 align:start position:0%
what they are if you want to have
different<00:02:49.519><c> uh</c><00:02:49.959><c> conversations</c><00:02:50.640><c> with</c><00:02:50.840><c> agents</c>

00:02:51.229 --> 00:02:51.239 align:start position:0%
different uh conversations with agents
 

00:02:51.239 --> 00:02:53.869 align:start position:0%
different uh conversations with agents
for<00:02:51.440><c> the</c><00:02:51.640><c> same</c><00:02:52.040><c> project</c><00:02:52.840><c> you</c><00:02:53.000><c> need</c><00:02:53.159><c> to</c><00:02:53.440><c> change</c>

00:02:53.869 --> 00:02:53.879 align:start position:0%
for the same project you need to change
 

00:02:53.879 --> 00:02:55.869 align:start position:0%
for the same project you need to change
the<00:02:54.040><c> seed</c><00:02:54.319><c> number</c><00:02:54.800><c> as</c><00:02:55.040><c> I've</c><00:02:55.319><c> you</c><00:02:55.440><c> can</c><00:02:55.560><c> see</c><00:02:55.760><c> that</c>

00:02:55.869 --> 00:02:55.879 align:start position:0%
the seed number as I've you can see that
 

00:02:55.879 --> 00:02:57.869 align:start position:0%
the seed number as I've you can see that
I've<00:02:56.040><c> done</c><00:02:56.280><c> here</c><00:02:56.720><c> several</c><00:02:57.120><c> times</c><00:02:57.560><c> testing</c>

00:02:57.869 --> 00:02:57.879 align:start position:0%
I've done here several times testing
 

00:02:57.879 --> 00:02:59.869 align:start position:0%
I've done here several times testing
this<00:02:58.000><c> out</c><00:02:58.120><c> with</c><00:02:58.280><c> different</c><00:02:58.480><c> models</c><00:02:59.319><c> um</c><00:02:59.519><c> now</c><00:02:59.800><c> if</c>

00:02:59.869 --> 00:02:59.879 align:start position:0%
this out with different models um now if
 

00:02:59.879 --> 00:03:01.990 align:start position:0%
this out with different models um now if
I<00:02:59.959><c> were</c><00:03:00.080><c> to</c><00:03:00.400><c> run</c><00:03:00.920><c> any</c><00:03:01.120><c> of</c><00:03:01.280><c> these</c><00:03:01.519><c> again</c><00:03:01.879><c> I'm</c>

00:03:01.990 --> 00:03:02.000 align:start position:0%
I were to run any of these again I'm
 

00:03:02.000 --> 00:03:03.309 align:start position:0%
I were to run any of these again I'm
going<00:03:02.120><c> to</c><00:03:02.239><c> get</c><00:03:02.360><c> the</c><00:03:02.480><c> same</c><00:03:02.720><c> conversation</c><00:03:03.200><c> that</c>

00:03:03.309 --> 00:03:03.319 align:start position:0%
going to get the same conversation that
 

00:03:03.319 --> 00:03:05.710 align:start position:0%
going to get the same conversation that
I<00:03:03.480><c> had</c><00:03:03.680><c> the</c><00:03:03.879><c> first</c><00:03:04.159><c> time</c><00:03:04.760><c> with</c><00:03:05.120><c> these</c><00:03:05.360><c> seed</c>

00:03:05.710 --> 00:03:05.720 align:start position:0%
I had the first time with these seed
 

00:03:05.720 --> 00:03:08.110 align:start position:0%
I had the first time with these seed
numbers<00:03:06.519><c> okay</c><00:03:07.000><c> uh</c><00:03:07.159><c> temperature</c><00:03:07.720><c> I</c><00:03:07.840><c> just</c><00:03:07.959><c> set</c>

00:03:08.110 --> 00:03:08.120 align:start position:0%
numbers okay uh temperature I just set
 

00:03:08.120 --> 00:03:10.030 align:start position:0%
numbers okay uh temperature I just set
to<00:03:08.319><c> zero</c><00:03:08.799><c> there</c><00:03:08.920><c> re</c><00:03:09.120><c> Quest</c><00:03:09.319><c> timeout</c><00:03:09.640><c> I</c><00:03:09.760><c> set</c><00:03:09.879><c> to</c>

00:03:10.030 --> 00:03:10.040 align:start position:0%
to zero there re Quest timeout I set to
 

00:03:10.040 --> 00:03:11.430 align:start position:0%
to zero there re Quest timeout I set to
5<00:03:10.159><c> minutes</c><00:03:10.519><c> that</c><00:03:10.599><c> you</c><00:03:10.680><c> can</c><00:03:10.879><c> change</c><00:03:11.200><c> that</c>

00:03:11.430 --> 00:03:11.440 align:start position:0%
5 minutes that you can change that
 

00:03:11.440 --> 00:03:13.509 align:start position:0%
5 minutes that you can change that
depending<00:03:11.720><c> on</c><00:03:11.959><c> what</c><00:03:12.120><c> you're</c><00:03:12.400><c> making</c><00:03:13.280><c> um</c><00:03:13.440><c> you</c>

00:03:13.509 --> 00:03:13.519 align:start position:0%
depending on what you're making um you
 

00:03:13.519 --> 00:03:14.830 align:start position:0%
depending on what you're making um you
might<00:03:13.680><c> need</c><00:03:13.840><c> more</c><00:03:14.040><c> you</c><00:03:14.120><c> might</c><00:03:14.280><c> need</c><00:03:14.440><c> less</c><00:03:14.720><c> it</c>

00:03:14.830 --> 00:03:14.840 align:start position:0%
might need more you might need less it
 

00:03:14.840 --> 00:03:17.070 align:start position:0%
might need more you might need less it
depends<00:03:15.799><c> now</c><00:03:15.959><c> we</c><00:03:16.080><c> get</c><00:03:16.159><c> to</c><00:03:16.319><c> the</c><00:03:16.400><c> agents</c><00:03:16.959><c> the</c>

00:03:17.070 --> 00:03:17.080 align:start position:0%
depends now we get to the agents the
 

00:03:17.080 --> 00:03:19.229 align:start position:0%
depends now we get to the agents the
first<00:03:17.360><c> agent</c><00:03:17.840><c> is</c><00:03:18.159><c> the</c><00:03:18.319><c> user</c><00:03:18.599><c> agent</c><00:03:18.920><c> which</c><00:03:19.040><c> is</c>

00:03:19.229 --> 00:03:19.239 align:start position:0%
first agent is the user agent which is
 

00:03:19.239 --> 00:03:21.390 align:start position:0%
first agent is the user agent which is
you<00:03:19.599><c> and</c><00:03:19.760><c> me</c><00:03:20.440><c> and</c><00:03:20.599><c> we're</c><00:03:20.760><c> going</c><00:03:20.879><c> to</c><00:03:20.959><c> be</c><00:03:21.120><c> named</c>

00:03:21.390 --> 00:03:21.400 align:start position:0%
you and me and we're going to be named
 

00:03:21.400 --> 00:03:23.990 align:start position:0%
you and me and we're going to be named
the<00:03:21.519><c> admin</c><00:03:22.159><c> and</c><00:03:22.360><c> basically</c><00:03:23.000><c> we</c><00:03:23.440><c> need</c><00:03:23.720><c> to</c><00:03:23.840><c> make</c>

00:03:23.990 --> 00:03:24.000 align:start position:0%
the admin and basically we need to make
 

00:03:24.000 --> 00:03:25.910 align:start position:0%
the admin and basically we need to make
sure<00:03:24.200><c> that</c><00:03:24.319><c> the</c><00:03:24.519><c> execution</c><00:03:25.159><c> from</c><00:03:25.360><c> the</c><00:03:25.519><c> planner</c>

00:03:25.910 --> 00:03:25.920 align:start position:0%
sure that the execution from the planner
 

00:03:25.920 --> 00:03:28.670 align:start position:0%
sure that the execution from the planner
is<00:03:26.120><c> approved</c><00:03:26.599><c> by</c><00:03:26.760><c> us</c><00:03:27.040><c> before</c><00:03:27.239><c> we</c><00:03:27.400><c> move</c><00:03:27.599><c> on</c><00:03:28.080><c> okay</c>

00:03:28.670 --> 00:03:28.680 align:start position:0%
is approved by us before we move on okay
 

00:03:28.680 --> 00:03:30.869 align:start position:0%
is approved by us before we move on okay
then<00:03:28.879><c> we</c><00:03:29.159><c> have</c><00:03:29.799><c> the</c><00:03:29.920><c> engineer</c><00:03:30.560><c> which</c><00:03:30.680><c> is</c>

00:03:30.869 --> 00:03:30.879 align:start position:0%
then we have the engineer which is
 

00:03:30.879 --> 00:03:32.470 align:start position:0%
then we have the engineer which is
essentially<00:03:31.280><c> the</c><00:03:31.480><c> coder</c><00:03:32.040><c> they're</c><00:03:32.200><c> going</c><00:03:32.319><c> to</c>

00:03:32.470 --> 00:03:32.480 align:start position:0%
essentially the coder they're going to
 

00:03:32.480 --> 00:03:35.190 align:start position:0%
essentially the coder they're going to
follow<00:03:32.799><c> the</c><00:03:32.920><c> plan</c><00:03:33.280><c> and</c><00:03:33.480><c> write</c><00:03:33.879><c> python</c><00:03:34.840><c> uh</c><00:03:34.959><c> code</c>

00:03:35.190 --> 00:03:35.200 align:start position:0%
follow the plan and write python uh code
 

00:03:35.200 --> 00:03:37.789 align:start position:0%
follow the plan and write python uh code
to<00:03:35.439><c> solve</c><00:03:35.720><c> the</c><00:03:35.920><c> task</c><00:03:36.599><c> all</c><00:03:36.720><c> right</c><00:03:37.159><c> and</c><00:03:37.280><c> this</c><00:03:37.400><c> is</c>

00:03:37.789 --> 00:03:37.799 align:start position:0%
to solve the task all right and this is
 

00:03:37.799 --> 00:03:39.990 align:start position:0%
to solve the task all right and this is
just<00:03:37.959><c> a</c><00:03:38.200><c> description</c><00:03:38.760><c> of</c><00:03:39.360><c> kind</c><00:03:39.480><c> of</c><00:03:39.640><c> more</c><00:03:39.840><c> in</c>

00:03:39.990 --> 00:03:40.000 align:start position:0%
just a description of kind of more in
 

00:03:40.000 --> 00:03:42.030 align:start position:0%
just a description of kind of more in
depth<00:03:40.319><c> of</c><00:03:40.519><c> how</c><00:03:40.680><c> we</c><00:03:40.799><c> want</c><00:03:41.080><c> them</c><00:03:41.280><c> to</c><00:03:41.680><c> write</c><00:03:41.920><c> the</c>

00:03:42.030 --> 00:03:42.040 align:start position:0%
depth of how we want them to write the
 

00:03:42.040 --> 00:03:43.789 align:start position:0%
depth of how we want them to write the
code<00:03:42.480><c> and</c><00:03:42.599><c> then</c><00:03:42.720><c> we</c><00:03:42.799><c> have</c><00:03:42.959><c> the</c><00:03:43.080><c> planner</c><00:03:43.640><c> they</c>

00:03:43.789 --> 00:03:43.799 align:start position:0%
code and then we have the planner they
 

00:03:43.799 --> 00:03:45.869 align:start position:0%
code and then we have the planner they
are<00:03:44.040><c> to</c><00:03:44.319><c> suggest</c><00:03:44.680><c> a</c><00:03:44.840><c> plan</c><00:03:45.159><c> and</c><00:03:45.360><c> revise</c><00:03:45.760><c> the</c>

00:03:45.869 --> 00:03:45.879 align:start position:0%
are to suggest a plan and revise the
 

00:03:45.879 --> 00:03:48.429 align:start position:0%
are to suggest a plan and revise the
plan<00:03:46.120><c> based</c><00:03:46.360><c> on</c><00:03:46.560><c> feedback</c><00:03:47.080><c> from</c><00:03:47.519><c> us</c><00:03:47.920><c> and</c><00:03:48.200><c> the</c>

00:03:48.429 --> 00:03:48.439 align:start position:0%
plan based on feedback from us and the
 

00:03:48.439 --> 00:03:51.869 align:start position:0%
plan based on feedback from us and the
critic<00:03:48.920><c> until</c><00:03:49.599><c> we</c><00:03:49.840><c> approve</c><00:03:50.239><c> it</c><00:03:51.239><c> and</c><00:03:51.680><c> this</c>

00:03:51.869 --> 00:03:51.879 align:start position:0%
critic until we approve it and this
 

00:03:51.879 --> 00:03:54.509 align:start position:0%
critic until we approve it and this
basically<00:03:52.400><c> needs</c><00:03:52.680><c> to</c><00:03:52.920><c> have</c><00:03:53.159><c> clear</c><00:03:53.439><c> steps</c><00:03:54.040><c> for</c>

00:03:54.509 --> 00:03:54.519 align:start position:0%
basically needs to have clear steps for
 

00:03:54.519 --> 00:03:57.229 align:start position:0%
basically needs to have clear steps for
the<00:03:54.640><c> engineer</c><00:03:55.400><c> executive</c><00:03:56.200><c> and</c><00:03:56.599><c> the</c><00:03:56.760><c> critic</c>

00:03:57.229 --> 00:03:57.239 align:start position:0%
the engineer executive and the critic
 

00:03:57.239 --> 00:03:59.550 align:start position:0%
the engineer executive and the critic
the<00:03:57.360><c> next</c><00:03:57.599><c> agent</c><00:03:57.959><c> is</c><00:03:58.319><c> the</c><00:03:58.519><c> executive</c><00:03:59.319><c> and</c><00:03:59.439><c> this</c>

00:03:59.550 --> 00:03:59.560 align:start position:0%
the next agent is the executive and this
 

00:03:59.560 --> 00:04:01.149 align:start position:0%
the next agent is the executive and this
is<00:03:59.640><c> this</c><00:03:59.720><c> is</c><00:03:59.879><c> basically</c><00:04:00.200><c> to</c><00:04:00.480><c> execute</c><00:04:00.840><c> the</c><00:04:00.959><c> code</c>

00:04:01.149 --> 00:04:01.159 align:start position:0%
is this is basically to execute the code
 

00:04:01.159 --> 00:04:02.990 align:start position:0%
is this is basically to execute the code
written<00:04:01.439><c> by</c><00:04:01.560><c> the</c><00:04:01.680><c> engineer</c><00:04:02.280><c> and</c><00:04:02.480><c> Report</c><00:04:02.799><c> the</c>

00:04:02.990 --> 00:04:03.000 align:start position:0%
written by the engineer and Report the
 

00:04:03.000 --> 00:04:05.270 align:start position:0%
written by the engineer and Report the
results<00:04:04.000><c> and</c><00:04:04.120><c> then</c><00:04:04.239><c> we</c><00:04:04.360><c> have</c><00:04:04.519><c> the</c><00:04:04.640><c> critic</c><00:04:05.159><c> the</c>

00:04:05.270 --> 00:04:05.280 align:start position:0%
results and then we have the critic the
 

00:04:05.280 --> 00:04:07.470 align:start position:0%
results and then we have the critic the
critic<00:04:05.640><c> is</c><00:04:05.760><c> just</c><00:04:06.000><c> really</c><00:04:06.360><c> here</c><00:04:06.560><c> to</c><00:04:06.799><c> review</c><00:04:07.280><c> all</c>

00:04:07.470 --> 00:04:07.480 align:start position:0%
critic is just really here to review all
 

00:04:07.480 --> 00:04:09.509 align:start position:0%
critic is just really here to review all
of<00:04:07.640><c> the</c><00:04:07.760><c> other</c><00:04:08.000><c> agents</c><00:04:08.760><c> what</c><00:04:08.959><c> they</c><00:04:09.079><c> say</c><00:04:09.280><c> in</c><00:04:09.400><c> the</c>

00:04:09.509 --> 00:04:09.519 align:start position:0%
of the other agents what they say in the
 

00:04:09.519 --> 00:04:11.630 align:start position:0%
of the other agents what they say in the
conversation<00:04:10.360><c> review</c><00:04:10.760><c> it</c><00:04:11.040><c> and</c><00:04:11.159><c> then</c><00:04:11.400><c> give</c>

00:04:11.630 --> 00:04:11.640 align:start position:0%
conversation review it and then give
 

00:04:11.640 --> 00:04:14.470 align:start position:0%
conversation review it and then give
feedback<00:04:12.120><c> so</c><00:04:12.319><c> hopefully</c><00:04:13.200><c> they</c><00:04:13.760><c> you</c><00:04:13.920><c> know</c><00:04:14.200><c> do</c>

00:04:14.470 --> 00:04:14.480 align:start position:0%
feedback so hopefully they you know do
 

00:04:14.480 --> 00:04:15.830 align:start position:0%
feedback so hopefully they you know do
something<00:04:14.799><c> or</c><00:04:15.000><c> fix</c><00:04:15.280><c> whatever</c><00:04:15.519><c> it</c><00:04:15.640><c> is</c><00:04:15.720><c> they're</c>

00:04:15.830 --> 00:04:15.840 align:start position:0%
something or fix whatever it is they're
 

00:04:15.840 --> 00:04:17.310 align:start position:0%
something or fix whatever it is they're
doing<00:04:16.040><c> like</c><00:04:16.160><c> so</c><00:04:16.359><c> the</c><00:04:16.440><c> engineer</c><00:04:16.919><c> has</c><00:04:17.079><c> written</c><00:04:17.239><c> a</c>

00:04:17.310 --> 00:04:17.320 align:start position:0%
doing like so the engineer has written a
 

00:04:17.320 --> 00:04:19.749 align:start position:0%
doing like so the engineer has written a
piece<00:04:17.479><c> of</c><00:04:17.600><c> code</c><00:04:17.840><c> the</c><00:04:17.919><c> critic</c><00:04:18.239><c> says</c><00:04:18.600><c> oh</c><00:04:19.239><c> you</c>

00:04:19.749 --> 00:04:19.759 align:start position:0%
piece of code the critic says oh you
 

00:04:19.759 --> 00:04:22.430 align:start position:0%
piece of code the critic says oh you
don't<00:04:20.120><c> have</c><00:04:20.560><c> any</c><00:04:20.799><c> testing</c><00:04:21.440><c> or</c><00:04:22.160><c> uh</c><00:04:22.240><c> you're</c><00:04:22.320><c> not</c>

00:04:22.430 --> 00:04:22.440 align:start position:0%
don't have any testing or uh you're not
 

00:04:22.440 --> 00:04:24.629 align:start position:0%
don't have any testing or uh you're not
checking<00:04:22.720><c> for</c><00:04:22.840><c> the</c><00:04:23.040><c> specific</c><00:04:23.360><c> error</c><00:04:24.080><c> can</c><00:04:24.240><c> you</c>

00:04:24.629 --> 00:04:24.639 align:start position:0%
checking for the specific error can you
 

00:04:24.639 --> 00:04:27.790 align:start position:0%
checking for the specific error can you
please<00:04:25.400><c> revise</c><00:04:25.720><c> your</c><00:04:25.880><c> code</c><00:04:26.440><c> and</c><00:04:27.160><c> have</c><00:04:27.639><c> that</c>

00:04:27.790 --> 00:04:27.800 align:start position:0%
please revise your code and have that
 

00:04:27.800 --> 00:04:29.830 align:start position:0%
please revise your code and have that
checking<00:04:28.280><c> for</c><00:04:28.520><c> me</c><00:04:29.199><c> that's</c><00:04:29.320><c> what</c><00:04:29.440><c> the</c><00:04:29.720><c> that's</c>

00:04:29.830 --> 00:04:29.840 align:start position:0%
checking for me that's what the that's
 

00:04:29.840 --> 00:04:31.790 align:start position:0%
checking for me that's what the that's
what<00:04:29.960><c> they're</c><00:04:30.120><c> there</c><00:04:30.240><c> for</c><00:04:30.880><c> and</c><00:04:31.039><c> then</c><00:04:31.360><c> finally</c>

00:04:31.790 --> 00:04:31.800 align:start position:0%
what they're there for and then finally
 

00:04:31.800 --> 00:04:33.909 align:start position:0%
what they're there for and then finally
we<00:04:32.039><c> have</c><00:04:32.600><c> a</c><00:04:32.800><c> group</c><00:04:33.039><c> chat</c><00:04:33.280><c> so</c><00:04:33.440><c> we</c><00:04:33.600><c> basically</c>

00:04:33.909 --> 00:04:33.919 align:start position:0%
we have a group chat so we basically
 

00:04:33.919 --> 00:04:36.790 align:start position:0%
we have a group chat so we basically
just<00:04:34.120><c> instantiate</c><00:04:34.680><c> a</c><00:04:34.880><c> group</c><00:04:35.120><c> chat</c><00:04:35.560><c> object</c><00:04:36.360><c> and</c>

00:04:36.790 --> 00:04:36.800 align:start position:0%
just instantiate a group chat object and
 

00:04:36.800 --> 00:04:39.350 align:start position:0%
just instantiate a group chat object and
we<00:04:36.960><c> have</c><00:04:37.080><c> to</c><00:04:37.320><c> give</c><00:04:37.600><c> them</c><00:04:37.960><c> the</c><00:04:38.080><c> array</c><00:04:38.560><c> of</c><00:04:38.840><c> agents</c>

00:04:39.350 --> 00:04:39.360 align:start position:0%
we have to give them the array of agents
 

00:04:39.360 --> 00:04:42.189 align:start position:0%
we have to give them the array of agents
that<00:04:39.479><c> we</c><00:04:39.680><c> have</c><00:04:39.919><c> so</c><00:04:40.720><c> we</c><00:04:40.960><c> know</c><00:04:41.440><c> who</c><00:04:41.680><c> all</c><00:04:41.919><c> is</c><00:04:42.039><c> in</c>

00:04:42.189 --> 00:04:42.199 align:start position:0%
that we have so we know who all is in
 

00:04:42.199 --> 00:04:44.469 align:start position:0%
that we have so we know who all is in
the<00:04:42.360><c> group</c><00:04:42.600><c> chat</c><00:04:42.919><c> talking</c><00:04:43.199><c> to</c><00:04:43.400><c> each</c><00:04:43.520><c> other</c><00:04:44.360><c> uh</c>

00:04:44.469 --> 00:04:44.479 align:start position:0%
the group chat talking to each other uh
 

00:04:44.479 --> 00:04:47.350 align:start position:0%
the group chat talking to each other uh
I<00:04:44.560><c> set</c><00:04:44.720><c> the</c><00:04:44.840><c> max</c><00:04:45.280><c> max</c><00:04:45.479><c> round</c><00:04:45.720><c> to</c><00:04:45.880><c> 50</c><00:04:46.759><c> um</c><00:04:47.000><c> this</c><00:04:47.120><c> is</c>

00:04:47.350 --> 00:04:47.360 align:start position:0%
I set the max max round to 50 um this is
 

00:04:47.360 --> 00:04:49.469 align:start position:0%
I set the max max round to 50 um this is
just<00:04:47.600><c> you</c><00:04:47.720><c> know</c><00:04:48.000><c> how</c><00:04:48.160><c> many</c><00:04:48.919><c> uh</c><00:04:49.080><c> total</c>

00:04:49.469 --> 00:04:49.479 align:start position:0%
just you know how many uh total
 

00:04:49.479 --> 00:04:51.469 align:start position:0%
just you know how many uh total
interactions<00:04:50.000><c> there'll</c><00:04:50.240><c> be</c><00:04:50.840><c> so</c><00:04:51.080><c> you</c><00:04:51.160><c> can</c><00:04:51.280><c> set</c>

00:04:51.469 --> 00:04:51.479 align:start position:0%
interactions there'll be so you can set
 

00:04:51.479 --> 00:04:53.710 align:start position:0%
interactions there'll be so you can set
this<00:04:51.600><c> to</c><00:04:51.759><c> whatever</c><00:04:52.160><c> makes</c><00:04:52.400><c> sense</c><00:04:52.600><c> for</c><00:04:52.840><c> you</c><00:04:53.600><c> uh</c>

00:04:53.710 --> 00:04:53.720 align:start position:0%
this to whatever makes sense for you uh
 

00:04:53.720 --> 00:04:55.510 align:start position:0%
this to whatever makes sense for you uh
the<00:04:53.840><c> manager</c><00:04:54.720><c> we</c><00:04:54.840><c> just</c><00:04:55.000><c> basically</c><00:04:55.280><c> have</c><00:04:55.360><c> a</c>

00:04:55.510 --> 00:04:55.520 align:start position:0%
the manager we just basically have a
 

00:04:55.520 --> 00:04:57.909 align:start position:0%
the manager we just basically have a
group<00:04:55.800><c> chat</c><00:04:56.080><c> manager</c><00:04:56.680><c> object</c><00:04:57.120><c> here</c><00:04:57.479><c> and</c><00:04:57.720><c> this</c>

00:04:57.909 --> 00:04:57.919 align:start position:0%
group chat manager object here and this
 

00:04:57.919 --> 00:05:00.830 align:start position:0%
group chat manager object here and this
takes<00:04:58.199><c> in</c><00:04:58.840><c> the</c><00:04:59.039><c> group</c><00:04:59.320><c> chat</c><00:04:59.840><c> so</c><00:05:00.240><c> all</c><00:05:00.440><c> of</c><00:05:00.639><c> the</c>

00:05:00.830 --> 00:05:00.840 align:start position:0%
takes in the group chat so all of the
 

00:05:00.840 --> 00:05:01.909 align:start position:0%
takes in the group chat so all of the
members<00:05:01.160><c> in</c><00:05:01.280><c> the</c><00:05:01.400><c> group</c><00:05:01.600><c> chat</c><00:05:01.759><c> they're</c>

00:05:01.909 --> 00:05:01.919 align:start position:0%
members in the group chat they're
 

00:05:01.919 --> 00:05:03.430 align:start position:0%
members in the group chat they're
talking<00:05:02.160><c> to</c><00:05:02.320><c> each</c><00:05:02.440><c> other</c><00:05:02.960><c> and</c><00:05:03.039><c> then</c><00:05:03.160><c> it</c><00:05:03.280><c> takes</c>

00:05:03.430 --> 00:05:03.440 align:start position:0%
talking to each other and then it takes
 

00:05:03.440 --> 00:05:06.430 align:start position:0%
talking to each other and then it takes
in<00:05:03.600><c> the</c><00:05:03.720><c> llm</c><00:05:04.199><c> config</c><00:05:04.960><c> and</c><00:05:05.199><c> lastly</c><00:05:05.759><c> we</c><00:05:05.919><c> have</c><00:05:06.199><c> the</c>

00:05:06.430 --> 00:05:06.440 align:start position:0%
in the llm config and lastly we have the
 

00:05:06.440 --> 00:05:08.790 align:start position:0%
in the llm config and lastly we have the
user<00:05:06.840><c> proxy</c><00:05:07.400><c> initiating</c><00:05:07.840><c> the</c><00:05:08.000><c> chat</c><00:05:08.320><c> with</c><00:05:08.639><c> the</c>

00:05:08.790 --> 00:05:08.800 align:start position:0%
user proxy initiating the chat with the
 

00:05:08.800 --> 00:05:10.070 align:start position:0%
user proxy initiating the chat with the
whole<00:05:09.039><c> group</c><00:05:09.440><c> okay</c><00:05:09.560><c> and</c><00:05:09.639><c> this</c><00:05:09.759><c> is</c><00:05:09.840><c> where</c><00:05:10.000><c> we</c>

00:05:10.070 --> 00:05:10.080 align:start position:0%
whole group okay and this is where we
 

00:05:10.080 --> 00:05:12.469 align:start position:0%
whole group okay and this is where we
want<00:05:10.280><c> them</c><00:05:10.560><c> to</c><00:05:10.720><c> make</c><00:05:10.880><c> the</c><00:05:11.000><c> flash</c><00:05:11.479><c> application</c>

00:05:12.469 --> 00:05:12.479 align:start position:0%
want them to make the flash application
 

00:05:12.479 --> 00:05:14.350 align:start position:0%
want them to make the flash application
uh<00:05:12.720><c> the</c><00:05:12.880><c> HTML</c><00:05:13.280><c> Pages</c><00:05:13.600><c> database</c><00:05:14.039><c> and</c>

00:05:14.350 --> 00:05:14.360 align:start position:0%
uh the HTML Pages database and
 

00:05:14.360 --> 00:05:16.029 align:start position:0%
uh the HTML Pages database and
everything<00:05:14.639><c> and</c><00:05:14.759><c> all</c><00:05:14.840><c> the</c><00:05:14.960><c> styling</c><00:05:15.560><c> okay</c><00:05:15.960><c> now</c>

00:05:16.029 --> 00:05:16.039 align:start position:0%
everything and all the styling okay now
 

00:05:16.039 --> 00:05:16.909 align:start position:0%
everything and all the styling okay now
we're<00:05:16.160><c> going</c><00:05:16.280><c> go</c><00:05:16.320><c> ahead</c><00:05:16.479><c> and</c><00:05:16.560><c> run</c><00:05:16.680><c> this</c><00:05:16.800><c> and</c>

00:05:16.909 --> 00:05:16.919 align:start position:0%
we're going go ahead and run this and
 

00:05:16.919 --> 00:05:19.710 align:start position:0%
we're going go ahead and run this and
see<00:05:17.080><c> what</c><00:05:17.319><c> happens</c><00:05:18.319><c> all</c><00:05:18.440><c> right</c><00:05:18.680><c> so</c><00:05:18.919><c> we</c><00:05:19.080><c> started</c>

00:05:19.710 --> 00:05:19.720 align:start position:0%
see what happens all right so we started
 

00:05:19.720 --> 00:05:21.790 align:start position:0%
see what happens all right so we started
uh<00:05:19.840><c> the</c><00:05:19.960><c> planner</c><00:05:20.400><c> here</c><00:05:20.840><c> it</c><00:05:20.960><c> looks</c><00:05:21.160><c> like</c><00:05:21.400><c> they</c>

00:05:21.790 --> 00:05:21.800 align:start position:0%
uh the planner here it looks like they
 

00:05:21.800 --> 00:05:24.749 align:start position:0%
uh the planner here it looks like they
have<00:05:22.000><c> the</c><00:05:22.160><c> plan</c><00:05:22.479><c> for</c><00:05:22.800><c> all</c><00:05:22.919><c> of</c><00:05:23.039><c> the</c><00:05:23.199><c> AI</c><00:05:23.759><c> agents</c>

00:05:24.749 --> 00:05:24.759 align:start position:0%
have the plan for all of the AI agents
 

00:05:24.759 --> 00:05:27.550 align:start position:0%
have the plan for all of the AI agents
that<00:05:24.880><c> looks</c><00:05:25.319><c> okay</c><00:05:26.039><c> and</c><00:05:26.720><c> then</c><00:05:26.919><c> the</c><00:05:27.080><c> engineer</c>

00:05:27.550 --> 00:05:27.560 align:start position:0%
that looks okay and then the engineer
 

00:05:27.560 --> 00:05:30.590 align:start position:0%
that looks okay and then the engineer
went<00:05:27.759><c> ahead</c><00:05:27.960><c> and</c><00:05:28.240><c> started</c><00:05:28.639><c> creating</c><00:05:29.039><c> the</c><00:05:29.199><c> code</c>

00:05:30.590 --> 00:05:30.600 align:start position:0%
went ahead and started creating the code
 

00:05:30.600 --> 00:05:33.230 align:start position:0%
went ahead and started creating the code
uh<00:05:30.800><c> for</c><00:05:31.759><c> their</c><00:05:32.120><c> task</c><00:05:32.560><c> so</c><00:05:32.720><c> it</c><00:05:32.840><c> looks</c><00:05:33.000><c> like</c><00:05:33.120><c> we</c>

00:05:33.230 --> 00:05:33.240 align:start position:0%
uh for their task so it looks like we
 

00:05:33.240 --> 00:05:36.150 align:start position:0%
uh for their task so it looks like we
have<00:05:33.360><c> the</c><00:05:33.520><c> flask</c><00:05:34.000><c> application</c><00:05:34.759><c> here</c><00:05:35.759><c> uh</c><00:05:35.960><c> here</c>

00:05:36.150 --> 00:05:36.160 align:start position:0%
have the flask application here uh here
 

00:05:36.160 --> 00:05:37.749 align:start position:0%
have the flask application here uh here
is<00:05:36.400><c> the</c><00:05:36.560><c> SQL</c><00:05:37.000><c> light</c>

00:05:37.749 --> 00:05:37.759 align:start position:0%
is the SQL light
 

00:05:37.759 --> 00:05:41.590 align:start position:0%
is the SQL light
database<00:05:38.759><c> um</c><00:05:39.039><c> creating</c><00:05:39.600><c> a</c><00:05:39.800><c> feedback</c><00:05:40.600><c> table</c>

00:05:41.590 --> 00:05:41.600 align:start position:0%
database um creating a feedback table
 

00:05:41.600 --> 00:05:46.350 align:start position:0%
database um creating a feedback table
and<00:05:41.800><c> then</c><00:05:42.160><c> we</c><00:05:42.479><c> have</c><00:05:43.280><c> the</c><00:05:43.440><c> routing</c><00:05:44.080><c> so</c><00:05:45.360><c> the</c>

00:05:46.350 --> 00:05:46.360 align:start position:0%
and then we have the routing so the
 

00:05:46.360 --> 00:05:49.189 align:start position:0%
and then we have the routing so the
default<00:05:46.840><c> routing</c><00:05:47.199><c> for</c><00:05:47.400><c> the</c><00:05:47.560><c> index</c><00:05:48.319><c> and</c><00:05:48.520><c> then</c>

00:05:49.189 --> 00:05:49.199 align:start position:0%
default routing for the index and then
 

00:05:49.199 --> 00:05:50.870 align:start position:0%
default routing for the index and then
whenever<00:05:49.600><c> we</c><00:05:49.800><c> submit</c><00:05:50.240><c> it's</c><00:05:50.400><c> supposed</c><00:05:50.639><c> to</c><00:05:50.759><c> give</c>

00:05:50.870 --> 00:05:50.880 align:start position:0%
whenever we submit it's supposed to give
 

00:05:50.880 --> 00:05:54.469 align:start position:0%
whenever we submit it's supposed to give
up<00:05:51.639><c> post</c><00:05:52.639><c> so</c><00:05:52.960><c> looks</c><00:05:53.520><c> looks</c><00:05:53.800><c> like</c><00:05:53.919><c> it</c><00:05:54.120><c> may</c><00:05:54.319><c> or</c>

00:05:54.469 --> 00:05:54.479 align:start position:0%
up post so looks looks like it may or
 

00:05:54.479 --> 00:06:00.029 align:start position:0%
up post so looks looks like it may or
may<00:05:54.680><c> not</c><00:05:54.840><c> be</c><00:05:55.120><c> okay</c><00:05:55.440><c> we'll</c><00:05:55.639><c> see</c><00:05:56.560><c> um</c><00:05:57.560><c> but</c><00:05:58.479><c> then</c><00:05:59.880><c> we</c>

00:06:00.029 --> 00:06:00.039 align:start position:0%
may not be okay we'll see um but then we
 

00:06:00.039 --> 00:06:02.590 align:start position:0%
may not be okay we'll see um but then we
come<00:06:00.280><c> down</c><00:06:00.680><c> here</c><00:06:01.080><c> we</c><00:06:01.199><c> have</c><00:06:01.440><c> the</c><00:06:01.720><c> three</c><00:06:02.039><c> HTML</c>

00:06:02.590 --> 00:06:02.600 align:start position:0%
come down here we have the three HTML
 

00:06:02.600 --> 00:06:04.510 align:start position:0%
come down here we have the three HTML
files<00:06:03.000><c> we</c><00:06:03.080><c> have</c><00:06:03.240><c> index</c><00:06:03.639><c> HTML</c><00:06:04.080><c> file</c><00:06:04.280><c> for</c><00:06:04.400><c> the</c>

00:06:04.510 --> 00:06:04.520 align:start position:0%
files we have index HTML file for the
 

00:06:04.520 --> 00:06:07.230 align:start position:0%
files we have index HTML file for the
main<00:06:04.800><c> page</c><00:06:05.440><c> the</c><00:06:05.639><c> thank</c><00:06:05.840><c> you</c><00:06:06.160><c> HTML</c><00:06:06.720><c> file</c><00:06:07.120><c> and</c>

00:06:07.230 --> 00:06:07.240 align:start position:0%
main page the thank you HTML file and
 

00:06:07.240 --> 00:06:09.749 align:start position:0%
main page the thank you HTML file and
then<00:06:07.440><c> the</c><00:06:07.639><c> admin</c><00:06:08.120><c> HTML</c><00:06:08.639><c> file</c><00:06:08.960><c> and</c><00:06:09.120><c> it</c><00:06:09.240><c> does</c><00:06:09.440><c> use</c>

00:06:09.749 --> 00:06:09.759 align:start position:0%
then the admin HTML file and it does use
 

00:06:09.759 --> 00:06:11.990 align:start position:0%
then the admin HTML file and it does use
bootstrap<00:06:10.759><c> uh</c><00:06:10.880><c> it</c><00:06:11.000><c> just</c><00:06:11.120><c> brings</c><00:06:11.440><c> in</c><00:06:11.840><c> looks</c>

00:06:11.990 --> 00:06:12.000 align:start position:0%
bootstrap uh it just brings in looks
 

00:06:12.000 --> 00:06:14.270 align:start position:0%
bootstrap uh it just brings in looks
like<00:06:12.120><c> maybe</c><00:06:12.280><c> the</c><00:06:12.360><c> CDN</c><00:06:12.720><c> for</c><00:06:12.919><c> bootstrap</c><00:06:13.919><c> and</c>

00:06:14.270 --> 00:06:14.280 align:start position:0%
like maybe the CDN for bootstrap and
 

00:06:14.280 --> 00:06:16.029 align:start position:0%
like maybe the CDN for bootstrap and
uses<00:06:14.680><c> default</c><00:06:15.080><c> styling</c><00:06:15.520><c> there's</c><00:06:15.680><c> really</c>

00:06:16.029 --> 00:06:16.039 align:start position:0%
uses default styling there's really
 

00:06:16.039 --> 00:06:18.029 align:start position:0%
uses default styling there's really
there's<00:06:16.160><c> no</c><00:06:16.319><c> real</c><00:06:16.520><c> customization</c><00:06:17.400><c> here</c><00:06:17.720><c> but</c>

00:06:18.029 --> 00:06:18.039 align:start position:0%
there's no real customization here but
 

00:06:18.039 --> 00:06:20.430 align:start position:0%
there's no real customization here but
it<00:06:18.199><c> does</c><00:06:18.400><c> create</c><00:06:18.599><c> a</c><00:06:18.759><c> table</c><00:06:19.520><c> uh</c><00:06:19.599><c> for</c><00:06:19.880><c> hopefully</c>

00:06:20.430 --> 00:06:20.440 align:start position:0%
it does create a table uh for hopefully
 

00:06:20.440 --> 00:06:23.189 align:start position:0%
it does create a table uh for hopefully
the<00:06:20.639><c> records</c><00:06:21.319><c> so</c><00:06:21.520><c> what</c><00:06:21.599><c> we're</c><00:06:21.720><c> going</c><00:06:21.800><c> to</c><00:06:22.000><c> do</c><00:06:23.000><c> is</c>

00:06:23.189 --> 00:06:23.199 align:start position:0%
the records so what we're going to do is
 

00:06:23.199 --> 00:06:24.870 align:start position:0%
the records so what we're going to do is
all<00:06:23.599><c> also</c><00:06:23.880><c> it</c><00:06:24.000><c> tells</c><00:06:24.240><c> you</c><00:06:24.400><c> what</c><00:06:24.520><c> you</c><00:06:24.599><c> need</c><00:06:24.720><c> to</c>

00:06:24.870 --> 00:06:24.880 align:start position:0%
all also it tells you what you need to
 

00:06:24.880 --> 00:06:26.670 align:start position:0%
all also it tells you what you need to
install<00:06:25.840><c> uh</c><00:06:25.960><c> we</c><00:06:26.039><c> want</c><00:06:26.160><c> to</c><00:06:26.280><c> put</c><00:06:26.400><c> this</c><00:06:26.479><c> in</c><00:06:26.599><c> a</c>

00:06:26.670 --> 00:06:26.680 align:start position:0%
install uh we want to put this in a
 

00:06:26.680 --> 00:06:28.230 align:start position:0%
install uh we want to put this in a
requirements<00:06:27.120><c> text</c><00:06:27.400><c> eventually</c><00:06:27.880><c> but</c><00:06:28.160><c> what</c>

00:06:28.230 --> 00:06:28.240 align:start position:0%
requirements text eventually but what
 

00:06:28.240 --> 00:06:29.710 align:start position:0%
requirements text eventually but what
we're<00:06:28.360><c> going</c><00:06:28.440><c> to</c><00:06:28.560><c> do</c><00:06:28.840><c> is</c><00:06:29.039><c> going</c><00:06:29.120><c> to</c><00:06:29.199><c> put</c><00:06:29.560><c> this</c>

00:06:29.710 --> 00:06:29.720 align:start position:0%
we're going to do is going to put this
 

00:06:29.720 --> 00:06:31.070 align:start position:0%
we're going to do is going to put this
code<00:06:29.919><c> into</c><00:06:30.199><c> the</c><00:06:30.599><c> files</c><00:06:30.880><c> that</c><00:06:31.000><c> they're</c>

00:06:31.070 --> 00:06:31.080 align:start position:0%
code into the files that they're
 

00:06:31.080 --> 00:06:32.710 align:start position:0%
code into the files that they're
supposed<00:06:31.280><c> to</c><00:06:31.360><c> be</c><00:06:31.479><c> in</c><00:06:31.919><c> and</c><00:06:32.039><c> run</c><00:06:32.240><c> it</c><00:06:32.360><c> and</c><00:06:32.479><c> see</c><00:06:32.639><c> if</c>

00:06:32.710 --> 00:06:32.720 align:start position:0%
supposed to be in and run it and see if
 

00:06:32.720 --> 00:06:34.510 align:start position:0%
supposed to be in and run it and see if
it<00:06:32.840><c> works</c><00:06:33.759><c> all</c><00:06:33.840><c> right</c><00:06:33.960><c> so</c><00:06:34.039><c> I've</c><00:06:34.160><c> copied</c><00:06:34.440><c> the</c>

00:06:34.510 --> 00:06:34.520 align:start position:0%
it works all right so I've copied the
 

00:06:34.520 --> 00:06:36.909 align:start position:0%
it works all right so I've copied the
code<00:06:34.840><c> I</c><00:06:34.960><c> ran</c><00:06:35.280><c> server</c><00:06:36.039><c> and</c><00:06:36.199><c> now</c><00:06:36.360><c> here's</c><00:06:36.680><c> the</c>

00:06:36.909 --> 00:06:36.919 align:start position:0%
code I ran server and now here's the
 

00:06:36.919 --> 00:06:38.430 align:start position:0%
code I ran server and now here's the
default<00:06:37.319><c> page</c><00:06:37.639><c> now</c><00:06:37.880><c> I</c><00:06:37.960><c> don't</c><00:06:38.080><c> think</c><00:06:38.240><c> this</c><00:06:38.319><c> is</c>

00:06:38.430 --> 00:06:38.440 align:start position:0%
default page now I don't think this is
 

00:06:38.440 --> 00:06:40.070 align:start position:0%
default page now I don't think this is
going<00:06:38.520><c> to</c><00:06:38.639><c> work</c><00:06:38.919><c> because</c><00:06:39.479><c> this</c><00:06:39.599><c> is</c><00:06:39.720><c> the</c><00:06:39.840><c> get</c>

00:06:40.070 --> 00:06:40.080 align:start position:0%
going to work because this is the get
 

00:06:40.080 --> 00:06:42.430 align:start position:0%
going to work because this is the get
request<00:06:40.560><c> right</c><00:06:40.759><c> and</c><00:06:41.080><c> this</c><00:06:41.199><c> is</c><00:06:41.400><c> fine</c><00:06:42.039><c> and</c><00:06:42.199><c> so</c><00:06:42.360><c> it</c>

00:06:42.430 --> 00:06:42.440 align:start position:0%
request right and this is fine and so it
 

00:06:42.440 --> 00:06:44.629 align:start position:0%
request right and this is fine and so it
looks<00:06:42.680><c> like</c><00:06:43.479><c> what</c><00:06:43.599><c> they</c><00:06:43.720><c> do</c><00:06:44.000><c> they</c><00:06:44.120><c> just</c><00:06:44.280><c> say</c><00:06:44.560><c> if</c>

00:06:44.629 --> 00:06:44.639 align:start position:0%
looks like what they do they just say if
 

00:06:44.639 --> 00:06:45.790 align:start position:0%
looks like what they do they just say if
we<00:06:44.800><c> like</c><00:06:44.919><c> the</c><00:06:45.039><c> experience</c><00:06:45.440><c> we</c><00:06:45.560><c> can</c><00:06:45.639><c> just</c>

00:06:45.790 --> 00:06:45.800 align:start position:0%
we like the experience we can just
 

00:06:45.800 --> 00:06:47.710 align:start position:0%
we like the experience we can just
either<00:06:45.960><c> select</c><00:06:46.319><c> yes</c><00:06:46.440><c> or</c><00:06:46.599><c> no</c><00:06:47.240><c> now</c><00:06:47.360><c> I</c><00:06:47.440><c> think</c><00:06:47.599><c> it's</c>

00:06:47.710 --> 00:06:47.720 align:start position:0%
either select yes or no now I think it's
 

00:06:47.720 --> 00:06:49.350 align:start position:0%
either select yes or no now I think it's
not<00:06:47.840><c> going</c><00:06:47.919><c> to</c><00:06:48.039><c> work</c><00:06:48.360><c> because</c><00:06:49.000><c> they</c><00:06:49.120><c> didn't</c>

00:06:49.350 --> 00:06:49.360 align:start position:0%
not going to work because they didn't
 

00:06:49.360 --> 00:06:53.110 align:start position:0%
not going to work because they didn't
have<00:06:49.479><c> the</c><00:06:49.680><c> post</c><00:06:50.000><c> request</c><00:06:50.840><c> for</c><00:06:51.560><c> this</c><00:06:51.759><c> URL</c><00:06:52.680><c> right</c>

00:06:53.110 --> 00:06:53.120 align:start position:0%
have the post request for this URL right
 

00:06:53.120 --> 00:06:55.550 align:start position:0%
have the post request for this URL right
so<00:06:53.280><c> if</c><00:06:53.440><c> I</c><00:06:53.520><c> hit</c><00:06:53.680><c> submit</c><00:06:54.039><c> here</c><00:06:54.960><c> yeah</c><00:06:55.160><c> there's</c><00:06:55.440><c> the</c>

00:06:55.550 --> 00:06:55.560 align:start position:0%
so if I hit submit here yeah there's the
 

00:06:55.560 --> 00:06:56.950 align:start position:0%
so if I hit submit here yeah there's the
method<00:06:55.840><c> is</c><00:06:55.960><c> not</c><00:06:56.080><c> allowed</c><00:06:56.360><c> for</c><00:06:56.479><c> the</c><00:06:56.599><c> request</c>

00:06:56.950 --> 00:06:56.960 align:start position:0%
method is not allowed for the request
 

00:06:56.960 --> 00:06:59.270 align:start position:0%
method is not allowed for the request
URL<00:06:57.599><c> and</c><00:06:57.720><c> that's</c><00:06:58.000><c> okay</c><00:06:58.400><c> so</c><00:06:58.680><c> now</c><00:06:58.960><c> what</c><00:06:59.120><c> I</c><00:06:59.160><c> need</c>

00:06:59.270 --> 00:06:59.280 align:start position:0%
URL and that's okay so now what I need
 

00:06:59.280 --> 00:07:01.029 align:start position:0%
URL and that's okay so now what I need
to<00:06:59.440><c> to</c><00:06:59.520><c> do</c><00:06:59.639><c> is</c><00:06:59.720><c> I'm</c><00:06:59.800><c> going</c><00:06:59.879><c> to</c><00:07:00.000><c> get</c><00:07:00.160><c> feedback</c>

00:07:01.029 --> 00:07:01.039 align:start position:0%
to to do is I'm going to get feedback
 

00:07:01.039 --> 00:07:02.909 align:start position:0%
to to do is I'm going to get feedback
I'm<00:07:01.120><c> going</c><00:07:01.240><c> to</c><00:07:01.319><c> get</c><00:07:01.440><c> feedback</c><00:07:01.840><c> to</c><00:07:02.400><c> the</c><00:07:02.599><c> model</c>

00:07:02.909 --> 00:07:02.919 align:start position:0%
I'm going to get feedback to the model
 

00:07:02.919 --> 00:07:05.189 align:start position:0%
I'm going to get feedback to the model
and<00:07:03.039><c> say</c><00:07:03.319><c> hey</c><00:07:03.919><c> this</c><00:07:04.120><c> error</c><00:07:04.400><c> happened</c><00:07:04.960><c> please</c>

00:07:05.189 --> 00:07:05.199 align:start position:0%
and say hey this error happened please
 

00:07:05.199 --> 00:07:06.790 align:start position:0%
and say hey this error happened please
fix<00:07:05.360><c> it</c><00:07:05.440><c> and</c><00:07:05.560><c> give</c><00:07:05.639><c> me</c><00:07:05.720><c> the</c><00:07:05.800><c> updated</c><00:07:06.120><c> code</c><00:07:06.560><c> so</c>

00:07:06.790 --> 00:07:06.800 align:start position:0%
fix it and give me the updated code so
 

00:07:06.800 --> 00:07:08.309 align:start position:0%
fix it and give me the updated code so
let<00:07:06.960><c> I'm</c><00:07:07.039><c> going</c><00:07:07.160><c> to</c><00:07:07.240><c> do</c><00:07:07.440><c> that</c><00:07:07.879><c> and</c><00:07:08.000><c> we'll</c><00:07:08.160><c> come</c>

00:07:08.309 --> 00:07:08.319 align:start position:0%
let I'm going to do that and we'll come
 

00:07:08.319 --> 00:07:09.790 align:start position:0%
let I'm going to do that and we'll come
back<00:07:08.479><c> and</c><00:07:08.560><c> see</c><00:07:08.720><c> if</c><00:07:08.800><c> it</c><00:07:08.919><c> fixed</c><00:07:09.160><c> it</c><00:07:09.440><c> okay</c><00:07:09.599><c> we're</c>

00:07:09.790 --> 00:07:09.800 align:start position:0%
back and see if it fixed it okay we're
 

00:07:09.800 --> 00:07:12.029 align:start position:0%
back and see if it fixed it okay we're
back<00:07:10.319><c> I</c><00:07:10.440><c> updated</c><00:07:10.800><c> the</c><00:07:10.919><c> code</c><00:07:11.400><c> and</c><00:07:11.560><c> this</c><00:07:11.720><c> time</c><00:07:11.919><c> it</c>

00:07:12.029 --> 00:07:12.039 align:start position:0%
back I updated the code and this time it
 

00:07:12.039 --> 00:07:14.589 align:start position:0%
back I updated the code and this time it
allows<00:07:12.400><c> for</c><00:07:13.000><c> a</c><00:07:13.160><c> get</c><00:07:13.400><c> and</c><00:07:13.639><c> post</c><00:07:13.919><c> request</c><00:07:14.319><c> all</c><00:07:14.479><c> in</c>

00:07:14.589 --> 00:07:14.599 align:start position:0%
allows for a get and post request all in
 

00:07:14.599 --> 00:07:16.990 align:start position:0%
allows for a get and post request all in
the<00:07:14.720><c> same</c><00:07:14.919><c> function</c><00:07:15.680><c> so</c><00:07:16.240><c> now</c><00:07:16.520><c> let's</c><00:07:16.680><c> try</c><00:07:16.840><c> it</c>

00:07:16.990 --> 00:07:17.000 align:start position:0%
the same function so now let's try it
 

00:07:17.000 --> 00:07:19.510 align:start position:0%
the same function so now let's try it
again<00:07:17.639><c> if</c><00:07:17.840><c> we</c><00:07:18.199><c> uh</c><00:07:18.319><c> let</c><00:07:18.400><c> me</c><00:07:18.599><c> select</c><00:07:19.039><c> no</c><00:07:19.400><c> because</c>

00:07:19.510 --> 00:07:19.520 align:start position:0%
again if we uh let me select no because
 

00:07:19.520 --> 00:07:21.150 align:start position:0%
again if we uh let me select no because
I<00:07:19.599><c> didn't</c><00:07:19.720><c> like</c><00:07:19.879><c> the</c><00:07:20.000><c> experience</c><00:07:20.960><c> and</c><00:07:21.080><c> I'm</c>

00:07:21.150 --> 00:07:21.160 align:start position:0%
I didn't like the experience and I'm
 

00:07:21.160 --> 00:07:23.110 align:start position:0%
I didn't like the experience and I'm
going<00:07:21.280><c> to</c><00:07:21.400><c> hit</c><00:07:21.520><c> submit</c><00:07:22.400><c> and</c><00:07:22.560><c> I</c><00:07:22.680><c> get</c><00:07:22.879><c> another</c>

00:07:23.110 --> 00:07:23.120 align:start position:0%
going to hit submit and I get another
 

00:07:23.120 --> 00:07:26.110 align:start position:0%
going to hit submit and I get another
error<00:07:24.000><c> now</c><00:07:24.720><c> I</c><00:07:24.879><c> know</c><00:07:25.039><c> what</c><00:07:25.160><c> this</c><00:07:25.280><c> error</c><00:07:25.599><c> is</c><00:07:25.960><c> this</c>

00:07:26.110 --> 00:07:26.120 align:start position:0%
error now I know what this error is this
 

00:07:26.120 --> 00:07:27.629 align:start position:0%
error now I know what this error is this
error<00:07:26.400><c> has</c><00:07:26.520><c> to</c><00:07:26.639><c> do</c><00:07:26.840><c> with</c><00:07:27.000><c> the</c><00:07:27.120><c> SQL</c><00:07:27.479><c> light</c>

00:07:27.629 --> 00:07:27.639 align:start position:0%
error has to do with the SQL light
 

00:07:27.639 --> 00:07:29.510 align:start position:0%
error has to do with the SQL light
database<00:07:28.479><c> and</c><00:07:28.639><c> the</c><00:07:28.759><c> way</c><00:07:28.879><c> it's</c><00:07:29.000><c> using</c>

00:07:29.510 --> 00:07:29.520 align:start position:0%
database and the way it's using
 

00:07:29.520 --> 00:07:31.589 align:start position:0%
database and the way it's using
threading<00:07:30.440><c> uh</c><00:07:30.560><c> I'm</c><00:07:30.680><c> getting</c><00:07:30.879><c> an</c><00:07:30.960><c> error</c><00:07:31.240><c> saying</c>

00:07:31.589 --> 00:07:31.599 align:start position:0%
threading uh I'm getting an error saying
 

00:07:31.599 --> 00:07:33.710 align:start position:0%
threading uh I'm getting an error saying
that<00:07:31.960><c> there's</c><00:07:32.280><c> already</c><00:07:32.520><c> one</c><00:07:32.720><c> thread</c><00:07:33.039><c> open</c><00:07:33.599><c> but</c>

00:07:33.710 --> 00:07:33.720 align:start position:0%
that there's already one thread open but
 

00:07:33.720 --> 00:07:36.469 align:start position:0%
that there's already one thread open but
I'm<00:07:33.879><c> trying</c><00:07:34.120><c> to</c><00:07:35.120><c> execute</c><00:07:35.960><c> uh</c><00:07:36.039><c> I'm</c><00:07:36.120><c> trying</c><00:07:36.319><c> to</c>

00:07:36.469 --> 00:07:36.479 align:start position:0%
I'm trying to execute uh I'm trying to
 

00:07:36.479 --> 00:07:39.189 align:start position:0%
I'm trying to execute uh I'm trying to
submit<00:07:36.879><c> to</c><00:07:37.080><c> the</c><00:07:37.280><c> table</c><00:07:37.879><c> using</c><00:07:38.280><c> another</c><00:07:38.639><c> thread</c>

00:07:39.189 --> 00:07:39.199 align:start position:0%
submit to the table using another thread
 

00:07:39.199 --> 00:07:41.589 align:start position:0%
submit to the table using another thread
so<00:07:39.360><c> I</c><00:07:39.479><c> know</c><00:07:39.680><c> this</c><00:07:39.759><c> is</c><00:07:39.919><c> the</c><00:07:40.039><c> error</c><00:07:40.960><c> I</c><00:07:41.160><c> went</c><00:07:41.360><c> ahead</c>

00:07:41.589 --> 00:07:41.599 align:start position:0%
so I know this is the error I went ahead
 

00:07:41.599 --> 00:07:44.149 align:start position:0%
so I know this is the error I went ahead
and<00:07:41.720><c> I</c><00:07:41.840><c> told</c><00:07:42.400><c> chat</c><00:07:42.680><c> GPT</c><00:07:43.400><c> I</c><00:07:43.520><c> said</c><00:07:43.800><c> or</c><00:07:44.000><c> went</c>

00:07:44.149 --> 00:07:44.159 align:start position:0%
and I told chat GPT I said or went
 

00:07:44.159 --> 00:07:46.790 align:start position:0%
and I told chat GPT I said or went
autogen<00:07:44.560><c> said</c><00:07:44.800><c> hey</c><00:07:45.360><c> this</c><00:07:45.440><c> is</c><00:07:45.560><c> the</c><00:07:45.680><c> error</c><00:07:46.680><c> uh</c>

00:07:46.790 --> 00:07:46.800 align:start position:0%
autogen said hey this is the error uh
 

00:07:46.800 --> 00:07:48.350 align:start position:0%
autogen said hey this is the error uh
can<00:07:46.919><c> you</c><00:07:47.039><c> please</c><00:07:47.280><c> fix</c><00:07:47.479><c> this</c><00:07:47.639><c> so</c><00:07:47.800><c> we</c><00:07:47.919><c> can</c><00:07:48.120><c> don't</c>

00:07:48.350 --> 00:07:48.360 align:start position:0%
can you please fix this so we can don't
 

00:07:48.360 --> 00:07:50.589 align:start position:0%
can you please fix this so we can don't
have<00:07:48.440><c> to</c><00:07:49.000><c> use</c><00:07:49.599><c> the</c><00:07:49.720><c> same</c><00:07:49.960><c> thread</c><00:07:50.199><c> to</c><00:07:50.360><c> access</c>

00:07:50.589 --> 00:07:50.599 align:start position:0%
have to use the same thread to access
 

00:07:50.599 --> 00:07:53.189 align:start position:0%
have to use the same thread to access
the<00:07:50.720><c> database</c><00:07:51.720><c> so</c><00:07:51.919><c> I'll</c><00:07:52.080><c> be</c><00:07:52.319><c> right</c><00:07:52.520><c> back</c><00:07:53.039><c> when</c>

00:07:53.189 --> 00:07:53.199 align:start position:0%
the database so I'll be right back when
 

00:07:53.199 --> 00:07:54.790 align:start position:0%
the database so I'll be right back when
it<00:07:53.360><c> gives</c><00:07:53.440><c> me</c><00:07:53.560><c> an</c><00:07:53.639><c> updated</c><00:07:54.000><c> code</c><00:07:54.400><c> I'll</c><00:07:54.560><c> change</c>

00:07:54.790 --> 00:07:54.800 align:start position:0%
it gives me an updated code I'll change
 

00:07:54.800 --> 00:07:56.350 align:start position:0%
it gives me an updated code I'll change
it<00:07:55.039><c> and</c><00:07:55.240><c> then</c><00:07:55.360><c> we'll</c><00:07:55.479><c> see</c><00:07:55.639><c> if</c><00:07:55.800><c> that</c><00:07:55.919><c> works</c>

00:07:56.350 --> 00:07:56.360 align:start position:0%
it and then we'll see if that works
 

00:07:56.360 --> 00:07:58.710 align:start position:0%
it and then we'll see if that works
finally<00:07:57.360><c> all</c><00:07:57.440><c> right</c><00:07:57.560><c> so</c><00:07:57.680><c> we're</c><00:07:57.919><c> back</c><00:07:58.479><c> uh</c><00:07:58.599><c> it</c>

00:07:58.710 --> 00:07:58.720 align:start position:0%
finally all right so we're back uh it
 

00:07:58.720 --> 00:08:00.589 align:start position:0%
finally all right so we're back uh it
gave<00:07:58.840><c> me</c><00:07:58.960><c> updated</c><00:07:59.440><c> code</c><00:07:59.879><c> and</c><00:08:00.000><c> I</c><00:08:00.120><c> went</c><00:08:00.319><c> ahead</c>

00:08:00.589 --> 00:08:00.599 align:start position:0%
gave me updated code and I went ahead
 

00:08:00.599 --> 00:08:01.749 align:start position:0%
gave me updated code and I went ahead
and<00:08:00.720><c> I</c><00:08:00.840><c> inserted</c><00:08:01.240><c> it</c><00:08:01.319><c> I'll</c><00:08:01.440><c> show</c><00:08:01.560><c> you</c><00:08:01.639><c> in</c><00:08:01.680><c> a</c>

00:08:01.749 --> 00:08:01.759 align:start position:0%
and I inserted it I'll show you in a
 

00:08:01.759 --> 00:08:03.230 align:start position:0%
and I inserted it I'll show you in a
minute<00:08:02.000><c> after</c><00:08:02.240><c> I</c><00:08:02.520><c> go</c><00:08:02.720><c> through</c><00:08:02.879><c> this</c><00:08:03.039><c> cuz</c><00:08:03.159><c> we're</c>

00:08:03.230 --> 00:08:03.240 align:start position:0%
minute after I go through this cuz we're
 

00:08:03.240 --> 00:08:04.749 align:start position:0%
minute after I go through this cuz we're
going<00:08:03.360><c> to</c><00:08:03.440><c> see</c><00:08:03.560><c> if</c><00:08:03.639><c> it</c><00:08:03.720><c> worked</c><00:08:03.960><c> or</c><00:08:04.080><c> not</c><00:08:04.400><c> so</c><00:08:04.560><c> I</c>

00:08:04.749 --> 00:08:04.759 align:start position:0%
going to see if it worked or not so I
 

00:08:04.759 --> 00:08:07.350 align:start position:0%
going to see if it worked or not so I
updated<00:08:05.159><c> the</c><00:08:05.280><c> code</c><00:08:05.560><c> for</c><00:08:06.120><c> the</c><00:08:06.440><c> flask</c><00:08:06.919><c> python</c>

00:08:07.350 --> 00:08:07.360 align:start position:0%
updated the code for the flask python
 

00:08:07.360 --> 00:08:10.230 align:start position:0%
updated the code for the flask python
file<00:08:08.159><c> now</c><00:08:08.759><c> it</c><00:08:08.879><c> I</c><00:08:09.000><c> restarted</c><00:08:09.440><c> the</c><00:08:09.520><c> server</c><00:08:10.120><c> and</c>

00:08:10.230 --> 00:08:10.240 align:start position:0%
file now it I restarted the server and
 

00:08:10.240 --> 00:08:12.110 align:start position:0%
file now it I restarted the server and
let's<00:08:10.400><c> see</c><00:08:10.560><c> if</c><00:08:10.720><c> this</c><00:08:10.879><c> works</c><00:08:11.759><c> so</c><00:08:11.919><c> I'm</c><00:08:12.000><c> going</c><00:08:12.080><c> to</c>

00:08:12.110 --> 00:08:12.120 align:start position:0%
let's see if this works so I'm going to
 

00:08:12.120 --> 00:08:14.350 align:start position:0%
let's see if this works so I'm going to
select<00:08:12.360><c> the</c><00:08:12.520><c> option</c><00:08:12.919><c> yes</c><00:08:13.720><c> hopefully</c><00:08:14.039><c> this</c>

00:08:14.350 --> 00:08:14.360 align:start position:0%
select the option yes hopefully this
 

00:08:14.360 --> 00:08:18.270 align:start position:0%
select the option yes hopefully this
works<00:08:15.680><c> submit</c><00:08:16.680><c> and</c><00:08:16.840><c> it</c><00:08:16.960><c> worked</c><00:08:17.520><c> so</c><00:08:17.800><c> finally</c>

00:08:18.270 --> 00:08:18.280 align:start position:0%
works submit and it worked so finally
 

00:08:18.280 --> 00:08:20.070 align:start position:0%
works submit and it worked so finally
after<00:08:18.599><c> a</c><00:08:18.680><c> few</c><00:08:18.840><c> iterations</c><00:08:19.280><c> and</c><00:08:19.440><c> that's</c><00:08:19.680><c> okay</c>

00:08:20.070 --> 00:08:20.080 align:start position:0%
after a few iterations and that's okay
 

00:08:20.080 --> 00:08:22.029 align:start position:0%
after a few iterations and that's okay
actually<00:08:20.319><c> I've</c><00:08:20.520><c> had</c><00:08:20.680><c> some</c><00:08:20.840><c> seeds</c><00:08:21.319><c> where</c><00:08:21.919><c> uh</c>

00:08:22.029 --> 00:08:22.039 align:start position:0%
actually I've had some seeds where uh
 

00:08:22.039 --> 00:08:23.830 align:start position:0%
actually I've had some seeds where uh
either<00:08:22.240><c> the</c><00:08:22.680><c> request</c><00:08:23.039><c> timeout</c><00:08:23.280><c> out</c><00:08:23.599><c> and</c><00:08:23.720><c> I</c>

00:08:23.830 --> 00:08:23.840 align:start position:0%
either the request timeout out and I
 

00:08:23.840 --> 00:08:26.230 align:start position:0%
either the request timeout out and I
have<00:08:23.960><c> to</c><00:08:24.120><c> restart</c><00:08:24.479><c> it</c><00:08:24.919><c> or</c><00:08:25.159><c> it</c><00:08:25.360><c> just</c><00:08:25.879><c> would</c><00:08:26.120><c> give</c>

00:08:26.230 --> 00:08:26.240 align:start position:0%
have to restart it or it just would give
 

00:08:26.240 --> 00:08:29.790 align:start position:0%
have to restart it or it just would give
me<00:08:26.520><c> so</c><00:08:26.800><c> many</c><00:08:27.800><c> uh</c><00:08:27.919><c> incorrect</c><00:08:28.919><c> um</c><00:08:29.240><c> fixes</c><00:08:29.560><c> for</c><00:08:29.680><c> the</c>

00:08:29.790 --> 00:08:29.800 align:start position:0%
me so many uh incorrect um fixes for the
 

00:08:29.800 --> 00:08:32.389 align:start position:0%
me so many uh incorrect um fixes for the
errors<00:08:30.680><c> that</c><00:08:30.840><c> I</c><00:08:30.960><c> just</c><00:08:31.120><c> had</c><00:08:31.639><c> ended</c><00:08:31.879><c> up</c><00:08:32.000><c> doing</c><00:08:32.159><c> it</c>

00:08:32.389 --> 00:08:32.399 align:start position:0%
errors that I just had ended up doing it
 

00:08:32.399 --> 00:08:34.430 align:start position:0%
errors that I just had ended up doing it
myself<00:08:32.880><c> so</c><00:08:33.159><c> that</c><00:08:33.360><c> that</c><00:08:33.440><c> has</c><00:08:33.560><c> happened</c><00:08:33.919><c> in</c><00:08:34.360><c> I</c>

00:08:34.430 --> 00:08:34.440 align:start position:0%
myself so that that has happened in I
 

00:08:34.440 --> 00:08:36.909 align:start position:0%
myself so that that has happened in I
think<00:08:34.599><c> two</c><00:08:34.760><c> of</c><00:08:34.880><c> the</c><00:08:35.000><c> seeds</c><00:08:35.760><c> um</c><00:08:36.039><c> but</c><00:08:36.599><c> uh</c><00:08:36.680><c> for</c>

00:08:36.909 --> 00:08:36.919 align:start position:0%
think two of the seeds um but uh for
 

00:08:36.919 --> 00:08:38.990 align:start position:0%
think two of the seeds um but uh for
about<00:08:37.279><c> five</c><00:08:37.479><c> of</c><00:08:37.560><c> the</c><00:08:37.680><c> seeds</c><00:08:38.279><c> it</c><00:08:38.399><c> would</c><00:08:38.680><c> end</c><00:08:38.839><c> up</c>

00:08:38.990 --> 00:08:39.000 align:start position:0%
about five of the seeds it would end up
 

00:08:39.000 --> 00:08:40.670 align:start position:0%
about five of the seeds it would end up
giving<00:08:39.240><c> me</c><00:08:39.399><c> the</c><00:08:39.560><c> correct</c><00:08:39.919><c> code</c><00:08:40.320><c> now</c><00:08:40.479><c> that</c><00:08:40.599><c> we</c>

00:08:40.670 --> 00:08:40.680 align:start position:0%
giving me the correct code now that we
 

00:08:40.680 --> 00:08:42.310 align:start position:0%
giving me the correct code now that we
know<00:08:40.839><c> that</c><00:08:40.959><c> it</c><00:08:41.080><c> can</c><00:08:41.279><c> submit</c><00:08:41.599><c> it</c><00:08:41.880><c> the</c><00:08:42.080><c> last</c>

00:08:42.310 --> 00:08:42.320 align:start position:0%
know that it can submit it the last
 

00:08:42.320 --> 00:08:43.550 align:start position:0%
know that it can submit it the last
thing<00:08:42.519><c> is</c><00:08:42.680><c> we</c><00:08:42.800><c> need</c><00:08:42.919><c> to</c><00:08:43.000><c> make</c><00:08:43.120><c> sure</c><00:08:43.279><c> that</c><00:08:43.399><c> the</c>

00:08:43.550 --> 00:08:43.560 align:start position:0%
thing is we need to make sure that the
 

00:08:43.560 --> 00:08:46.310 align:start position:0%
thing is we need to make sure that the
admin<00:08:43.919><c> page</c><00:08:44.159><c> worked</c><00:08:44.880><c> so</c><00:08:45.080><c> if</c><00:08:45.240><c> we</c><00:08:45.560><c> go</c><00:08:45.800><c> here</c><00:08:46.080><c> go</c>

00:08:46.310 --> 00:08:46.320 align:start position:0%
admin page worked so if we go here go
 

00:08:46.320 --> 00:08:48.389 align:start position:0%
admin page worked so if we go here go
slash

00:08:48.389 --> 00:08:48.399 align:start position:0%
slash
 

00:08:48.399 --> 00:08:52.430 align:start position:0%
slash
admin<00:08:49.399><c> and</c><00:08:49.519><c> it</c><00:08:49.640><c> works</c><00:08:50.240><c> thank</c><00:08:50.600><c> goodness</c><00:08:51.600><c> uh</c><00:08:52.200><c> so</c>

00:08:52.430 --> 00:08:52.440 align:start position:0%
admin and it works thank goodness uh so
 

00:08:52.440 --> 00:08:54.350 align:start position:0%
admin and it works thank goodness uh so
now<00:08:52.600><c> it's</c><00:08:52.760><c> showing</c><00:08:53.519><c> what</c><00:08:53.800><c> what</c><00:08:53.880><c> we</c><00:08:53.959><c> can</c><00:08:54.080><c> do</c>

00:08:54.350 --> 00:08:54.360 align:start position:0%
now it's showing what what we can do
 

00:08:54.360 --> 00:08:57.030 align:start position:0%
now it's showing what what we can do
next<00:08:54.760><c> is</c><00:08:54.959><c> or</c><00:08:55.160><c> in</c><00:08:55.320><c> like</c><00:08:55.480><c> an</c><00:08:55.959><c> in</c><00:08:56.080><c> a</c><00:08:56.279><c> separate</c>

00:08:57.030 --> 00:08:57.040 align:start position:0%
next is or in like an in a separate
 

00:08:57.040 --> 00:08:59.190 align:start position:0%
next is or in like an in a separate
video<00:08:57.600><c> is</c><00:08:57.720><c> that</c><00:08:57.880><c> we</c><00:08:57.959><c> can</c><00:08:58.160><c> take</c><00:08:58.480><c> this</c><00:08:58.640><c> and</c><00:08:58.920><c> an</c>

00:08:59.190 --> 00:08:59.200 align:start position:0%
video is that we can take this and an
 

00:08:59.200 --> 00:09:01.509 align:start position:0%
video is that we can take this and an
analyze<00:08:59.760><c> all</c><00:08:59.880><c> the</c><00:09:00.079><c> feedback</c><00:09:00.959><c> I</c><00:09:01.040><c> know</c><00:09:01.279><c> here</c><00:09:01.399><c> is</c>

00:09:01.509 --> 00:09:01.519 align:start position:0%
analyze all the feedback I know here is
 

00:09:01.519 --> 00:09:02.990 align:start position:0%
analyze all the feedback I know here is
a<00:09:01.680><c> very</c><00:09:01.839><c> simple</c><00:09:02.200><c> this</c><00:09:02.279><c> is</c><00:09:02.399><c> a</c><00:09:02.560><c> very</c><00:09:02.720><c> simple</c>

00:09:02.990 --> 00:09:03.000 align:start position:0%
a very simple this is a very simple
 

00:09:03.000 --> 00:09:04.670 align:start position:0%
a very simple this is a very simple
example<00:09:03.399><c> right</c><00:09:03.519><c> but</c><00:09:03.600><c> if</c><00:09:03.720><c> we</c><00:09:03.839><c> had</c><00:09:04.279><c> multiple</c>

00:09:04.670 --> 00:09:04.680 align:start position:0%
example right but if we had multiple
 

00:09:04.680 --> 00:09:05.949 align:start position:0%
example right but if we had multiple
pages<00:09:05.000><c> of</c><00:09:05.160><c> multiple</c><00:09:05.480><c> different</c><00:09:05.680><c> questions</c>

00:09:05.949 --> 00:09:05.959 align:start position:0%
pages of multiple different questions
 

00:09:05.959 --> 00:09:07.470 align:start position:0%
pages of multiple different questions
and<00:09:06.120><c> surveys</c><00:09:06.720><c> we</c><00:09:06.839><c> could</c><00:09:07.000><c> take</c><00:09:07.240><c> all</c><00:09:07.320><c> that</c>

00:09:07.470 --> 00:09:07.480 align:start position:0%
and surveys we could take all that
 

00:09:07.480 --> 00:09:09.509 align:start position:0%
and surveys we could take all that
information<00:09:07.920><c> have</c><00:09:08.079><c> a</c><00:09:08.279><c> separate</c><00:09:09.200><c> separate</c>

00:09:09.509 --> 00:09:09.519 align:start position:0%
information have a separate separate
 

00:09:09.519 --> 00:09:11.790 align:start position:0%
information have a separate separate
application<00:09:10.000><c> or</c><00:09:10.480><c> function</c><00:09:11.240><c> that</c><00:09:11.399><c> takes</c><00:09:11.600><c> that</c>

00:09:11.790 --> 00:09:11.800 align:start position:0%
application or function that takes that
 

00:09:11.800 --> 00:09:13.710 align:start position:0%
application or function that takes that
data<00:09:12.360><c> and</c><00:09:12.480><c> then</c><00:09:12.720><c> analyzes</c><00:09:13.240><c> it</c><00:09:13.360><c> and</c><00:09:13.480><c> then</c><00:09:13.600><c> that</c>

00:09:13.710 --> 00:09:13.720 align:start position:0%
data and then analyzes it and then that
 

00:09:13.720 --> 00:09:16.470 align:start position:0%
data and then analyzes it and then that
would<00:09:13.920><c> send</c><00:09:14.120><c> it</c><00:09:14.399><c> back</c><00:09:14.680><c> to</c><00:09:15.640><c> uh</c><00:09:15.760><c> the</c><00:09:15.880><c> company</c>

00:09:16.470 --> 00:09:16.480 align:start position:0%
would send it back to uh the company
 

00:09:16.480 --> 00:09:19.310 align:start position:0%
would send it back to uh the company
that<00:09:16.959><c> hired</c><00:09:17.440><c> us</c><00:09:17.760><c> to</c><00:09:18.680><c> uh</c><00:09:18.760><c> send</c><00:09:19.000><c> out</c><00:09:19.200><c> these</c>

00:09:19.310 --> 00:09:19.320 align:start position:0%
that hired us to uh send out these
 

00:09:19.320 --> 00:09:21.350 align:start position:0%
that hired us to uh send out these
surveys<00:09:19.760><c> to</c><00:09:19.920><c> people</c><00:09:20.560><c> okay</c><00:09:20.880><c> uh</c><00:09:21.000><c> so</c><00:09:21.200><c> I</c>

00:09:21.350 --> 00:09:21.360 align:start position:0%
surveys to people okay uh so I
 

00:09:21.360 --> 00:09:23.630 align:start position:0%
surveys to people okay uh so I
originally<00:09:21.760><c> said</c><00:09:22.079><c> hey</c><00:09:22.640><c> you</c><00:09:22.800><c> know</c><00:09:23.440><c> we're</c>

00:09:23.630 --> 00:09:23.640 align:start position:0%
originally said hey you know we're
 

00:09:23.640 --> 00:09:25.710 align:start position:0%
originally said hey you know we're
getting<00:09:23.839><c> the</c><00:09:24.000><c> method</c><00:09:24.240><c> not</c><00:09:24.399><c> allowed</c><00:09:24.760><c> error</c><00:09:25.519><c> uh</c>

00:09:25.710 --> 00:09:25.720 align:start position:0%
getting the method not allowed error uh
 

00:09:25.720 --> 00:09:28.350 align:start position:0%
getting the method not allowed error uh
here<00:09:26.440><c> here</c><00:09:26.600><c> it</c><00:09:26.760><c> put</c><00:09:26.959><c> the</c><00:09:27.399><c> getting</c><00:09:27.800><c> post</c>

00:09:28.350 --> 00:09:28.360 align:start position:0%
here here it put the getting post
 

00:09:28.360 --> 00:09:30.750 align:start position:0%
here here it put the getting post
together<00:09:29.240><c> which</c><00:09:29.360><c> is</c><00:09:29.519><c> good</c><00:09:30.120><c> uh</c><00:09:30.279><c> after</c><00:09:30.519><c> that</c>

00:09:30.750 --> 00:09:30.760 align:start position:0%
together which is good uh after that
 

00:09:30.760 --> 00:09:33.310 align:start position:0%
together which is good uh after that
looks<00:09:30.920><c> like</c><00:09:31.079><c> the</c><00:09:31.480><c> critic</c><00:09:32.480><c> you</c><00:09:32.640><c> know</c><00:09:32.959><c> not</c><00:09:33.120><c> me</c>

00:09:33.310 --> 00:09:33.320 align:start position:0%
looks like the critic you know not me
 

00:09:33.320 --> 00:09:35.630 align:start position:0%
looks like the critic you know not me
the<00:09:33.440><c> critic</c><00:09:33.680><c> says</c><00:09:34.000><c> hey</c><00:09:34.200><c> good</c><00:09:34.399><c> job</c><00:09:35.279><c> uh</c><00:09:35.480><c> but</c>

00:09:35.630 --> 00:09:35.640 align:start position:0%
the critic says hey good job uh but
 

00:09:35.640 --> 00:09:38.470 align:start position:0%
the critic says hey good job uh but
there's<00:09:35.959><c> actually</c><00:09:36.480><c> a</c><00:09:36.600><c> Threading</c><00:09:37.120><c> error</c><00:09:38.120><c> um</c><00:09:38.399><c> I</c>

00:09:38.470 --> 00:09:38.480 align:start position:0%
there's actually a Threading error um I
 

00:09:38.480 --> 00:09:41.069 align:start position:0%
there's actually a Threading error um I
didn't<00:09:38.680><c> want</c><00:09:38.800><c> it</c><00:09:38.920><c> to</c><00:09:39.160><c> use</c><00:09:39.560><c> the</c><00:09:39.800><c> same</c><00:09:40.600><c> the</c><00:09:40.720><c> SQL</c>

00:09:41.069 --> 00:09:41.079 align:start position:0%
didn't want it to use the same the SQL
 

00:09:41.079 --> 00:09:42.790 align:start position:0%
didn't want it to use the same the SQL
light<00:09:41.240><c> was</c><00:09:41.440><c> again</c><00:09:41.600><c> trying</c><00:09:41.800><c> to</c><00:09:41.959><c> use</c><00:09:42.399><c> the</c><00:09:42.560><c> same</c>

00:09:42.790 --> 00:09:42.800 align:start position:0%
light was again trying to use the same
 

00:09:42.800 --> 00:09:44.590 align:start position:0%
light was again trying to use the same
thread<00:09:43.040><c> to</c><00:09:43.240><c> access</c><00:09:43.480><c> it</c><00:09:43.680><c> didn't</c><00:09:43.880><c> want</c><00:09:44.120><c> that</c><00:09:44.320><c> so</c>

00:09:44.590 --> 00:09:44.600 align:start position:0%
thread to access it didn't want that so
 

00:09:44.600 --> 00:09:47.350 align:start position:0%
thread to access it didn't want that so
it<00:09:44.800><c> came</c><00:09:45.560><c> uh</c><00:09:45.760><c> gave</c><00:09:45.959><c> me</c><00:09:46.200><c> updates</c><00:09:46.959><c> on</c><00:09:47.120><c> all</c><00:09:47.240><c> the</c>

00:09:47.350 --> 00:09:47.360 align:start position:0%
it came uh gave me updates on all the
 

00:09:47.360 --> 00:09:50.389 align:start position:0%
it came uh gave me updates on all the
codes<00:09:47.760><c> so</c><00:09:48.200><c> I</c><00:09:48.320><c> went</c><00:09:48.839><c> I</c><00:09:48.920><c> went</c><00:09:49.120><c> in</c><00:09:49.839><c> and</c><00:09:49.959><c> I</c><00:09:50.079><c> updated</c>

00:09:50.389 --> 00:09:50.399 align:start position:0%
codes so I went I went in and I updated
 

00:09:50.399 --> 00:09:53.230 align:start position:0%
codes so I went I went in and I updated
the<00:09:50.519><c> code</c><00:09:51.399><c> and</c><00:09:51.519><c> then</c><00:09:51.760><c> now</c><00:09:52.600><c> um</c><00:09:52.800><c> it</c><00:09:52.920><c> kind</c><00:09:53.040><c> of</c>

00:09:53.230 --> 00:09:53.240 align:start position:0%
the code and then now um it kind of
 

00:09:53.240 --> 00:09:55.470 align:start position:0%
the code and then now um it kind of
explains<00:09:53.600><c> it</c><00:09:53.880><c> I</c><00:09:54.000><c> say</c><00:09:54.240><c> good</c><00:09:54.440><c> job</c><00:09:54.839><c> now</c><00:09:54.959><c> I</c><00:09:55.040><c> can</c><00:09:55.200><c> say</c>

00:09:55.470 --> 00:09:55.480 align:start position:0%
explains it I say good job now I can say
 

00:09:55.480 --> 00:09:58.269 align:start position:0%
explains it I say good job now I can say
good<00:09:55.720><c> job</c><00:09:56.200><c> this</c><00:09:56.560><c> works</c><00:09:57.560><c> okay</c><00:09:57.760><c> now</c><00:09:57.880><c> as</c><00:09:58.000><c> far</c><00:09:58.120><c> as</c>

00:09:58.269 --> 00:09:58.279 align:start position:0%
good job this works okay now as far as
 

00:09:58.279 --> 00:10:00.269 align:start position:0%
good job this works okay now as far as
going<00:09:58.560><c> through</c><00:09:58.800><c> and</c><00:09:59.160><c> creating</c><00:09:59.480><c> a</c><00:09:59.600><c> simple</c><00:09:59.880><c> SAS</c>

00:10:00.269 --> 00:10:00.279 align:start position:0%
going through and creating a simple SAS
 

00:10:00.279 --> 00:10:01.870 align:start position:0%
going through and creating a simple SAS
product<00:10:00.560><c> and</c><00:10:00.640><c> I</c><00:10:00.760><c> know</c><00:10:00.839><c> this</c><00:10:00.959><c> is</c><00:10:01.120><c> very</c><00:10:01.320><c> simple</c>

00:10:01.870 --> 00:10:01.880 align:start position:0%
product and I know this is very simple
 

00:10:01.880 --> 00:10:03.750 align:start position:0%
product and I know this is very simple
I'm<00:10:01.959><c> not</c><00:10:02.120><c> trying</c><00:10:02.320><c> to</c><00:10:02.560><c> create</c><00:10:02.880><c> something</c><00:10:03.600><c> that</c>

00:10:03.750 --> 00:10:03.760 align:start position:0%
I'm not trying to create something that
 

00:10:03.760 --> 00:10:05.829 align:start position:0%
I'm not trying to create something that
I'm<00:10:03.839><c> going</c><00:10:03.959><c> to</c><00:10:04.079><c> make</c><00:10:04.279><c> a</c><00:10:04.399><c> lot</c><00:10:04.519><c> of</c><00:10:04.640><c> money</c><00:10:04.920><c> on</c><00:10:05.560><c> but</c>

00:10:05.829 --> 00:10:05.839 align:start position:0%
I'm going to make a lot of money on but
 

00:10:05.839 --> 00:10:08.069 align:start position:0%
I'm going to make a lot of money on but
this<00:10:05.920><c> is</c><00:10:06.240><c> the</c><00:10:06.519><c> idea</c><00:10:06.920><c> on</c><00:10:07.160><c> how</c><00:10:07.240><c> to</c><00:10:07.480><c> generate</c><00:10:07.839><c> it</c>

00:10:08.069 --> 00:10:08.079 align:start position:0%
this is the idea on how to generate it
 

00:10:08.079 --> 00:10:10.430 align:start position:0%
this is the idea on how to generate it
how<00:10:08.160><c> to</c><00:10:08.360><c> go</c><00:10:08.600><c> through</c><00:10:09.200><c> using</c><00:10:09.720><c> the</c><00:10:09.959><c> autonomous</c>

00:10:10.430 --> 00:10:10.440 align:start position:0%
how to go through using the autonomous
 

00:10:10.440 --> 00:10:12.590 align:start position:0%
how to go through using the autonomous
agents<00:10:11.120><c> giving</c><00:10:11.360><c> them</c><00:10:11.519><c> the</c><00:10:11.680><c> roles</c><00:10:12.360><c> and</c><00:10:12.480><c> then</c>

00:10:12.590 --> 00:10:12.600 align:start position:0%
agents giving them the roles and then
 

00:10:12.600 --> 00:10:14.269 align:start position:0%
agents giving them the roles and then
testing<00:10:12.920><c> the</c><00:10:13.040><c> application</c><00:10:13.800><c> you</c><00:10:13.880><c> know</c><00:10:14.000><c> you</c><00:10:14.120><c> can</c>

00:10:14.269 --> 00:10:14.279 align:start position:0%
testing the application you know you can
 

00:10:14.279 --> 00:10:16.110 align:start position:0%
testing the application you know you can
give<00:10:14.440><c> feedback</c><00:10:14.920><c> I</c><00:10:15.079><c> know</c><00:10:15.200><c> we</c><00:10:15.279><c> had</c><00:10:15.399><c> an</c><00:10:15.560><c> executive</c>

00:10:16.110 --> 00:10:16.120 align:start position:0%
give feedback I know we had an executive
 

00:10:16.120 --> 00:10:18.990 align:start position:0%
give feedback I know we had an executive
agent<00:10:16.720><c> do</c><00:10:17.000><c> that</c><00:10:17.279><c> but</c><00:10:17.680><c> uh</c><00:10:17.880><c> us</c><00:10:18.200><c> we</c><00:10:18.640><c> I'd</c><00:10:18.760><c> like</c><00:10:18.880><c> to</c>

00:10:18.990 --> 00:10:19.000 align:start position:0%
agent do that but uh us we I'd like to
 

00:10:19.000 --> 00:10:20.870 align:start position:0%
agent do that but uh us we I'd like to
do<00:10:19.120><c> that</c><00:10:19.320><c> myself</c><00:10:19.760><c> as</c><00:10:19.920><c> well</c><00:10:20.120><c> CU</c><00:10:20.240><c> I</c><00:10:20.320><c> want</c><00:10:20.440><c> to</c><00:10:20.640><c> test</c>

00:10:20.870 --> 00:10:20.880 align:start position:0%
do that myself as well CU I want to test
 

00:10:20.880 --> 00:10:23.990 align:start position:0%
do that myself as well CU I want to test
it<00:10:21.240><c> cu</c><00:10:21.440><c> the</c><00:10:21.760><c> executive</c><00:10:22.760><c> uh</c><00:10:22.959><c> before</c><00:10:23.640><c> has</c>

00:10:23.990 --> 00:10:24.000 align:start position:0%
it cu the executive uh before has
 

00:10:24.000 --> 00:10:25.990 align:start position:0%
it cu the executive uh before has
sometimes<00:10:24.200><c> said</c><00:10:24.839><c> oh</c><00:10:25.040><c> this</c><00:10:25.200><c> works</c><00:10:25.519><c> then</c><00:10:25.640><c> I</c><00:10:25.760><c> test</c>

00:10:25.990 --> 00:10:26.000 align:start position:0%
sometimes said oh this works then I test
 

00:10:26.000 --> 00:10:27.350 align:start position:0%
sometimes said oh this works then I test
it<00:10:26.079><c> and</c><00:10:26.160><c> it</c><00:10:26.279><c> didn't</c><00:10:26.560><c> quite</c><00:10:26.760><c> work</c><00:10:26.920><c> so</c><00:10:27.079><c> I</c><00:10:27.200><c> like</c>

00:10:27.350 --> 00:10:27.360 align:start position:0%
it and it didn't quite work so I like
 

00:10:27.360 --> 00:10:29.110 align:start position:0%
it and it didn't quite work so I like
testing<00:10:27.600><c> it</c><00:10:27.720><c> myself</c><00:10:28.079><c> anyways</c><00:10:28.720><c> I</c><00:10:28.800><c> think</c><00:10:28.920><c> it's</c>

00:10:29.110 --> 00:10:29.120 align:start position:0%
testing it myself anyways I think it's
 

00:10:29.120 --> 00:10:30.870 align:start position:0%
testing it myself anyways I think it's
it's<00:10:29.240><c> good</c><00:10:29.360><c> to</c><00:10:29.480><c> do</c><00:10:30.079><c> but</c><00:10:30.240><c> in</c><00:10:30.360><c> the</c><00:10:30.480><c> end</c><00:10:30.720><c> we've</c>

00:10:30.870 --> 00:10:30.880 align:start position:0%
it's good to do but in the end we've
 

00:10:30.880 --> 00:10:32.350 align:start position:0%
it's good to do but in the end we've
created<00:10:31.120><c> a</c><00:10:31.200><c> simple</c><00:10:31.440><c> SAS</c><00:10:31.800><c> product</c><00:10:32.079><c> using</c>

00:10:32.350 --> 00:10:32.360 align:start position:0%
created a simple SAS product using
 

00:10:32.360 --> 00:10:34.630 align:start position:0%
created a simple SAS product using
autogen<00:10:33.360><c> I</c><00:10:33.480><c> hope</c><00:10:33.600><c> you</c><00:10:33.760><c> enjoyed</c><00:10:34.040><c> the</c><00:10:34.160><c> video</c><00:10:34.519><c> uh</c>

00:10:34.630 --> 00:10:34.640 align:start position:0%
autogen I hope you enjoyed the video uh
 

00:10:34.640 --> 00:10:37.069 align:start position:0%
autogen I hope you enjoyed the video uh
please<00:10:34.880><c> share</c><00:10:35.720><c> uh</c><00:10:35.880><c> your</c><00:10:36.120><c> comments</c><00:10:36.480><c> down</c><00:10:36.680><c> below</c>

00:10:37.069 --> 00:10:37.079 align:start position:0%
please share uh your comments down below
 

00:10:37.079 --> 00:10:38.990 align:start position:0%
please share uh your comments down below
let<00:10:37.240><c> me</c><00:10:37.360><c> know</c><00:10:37.839><c> if</c><00:10:37.959><c> you've</c><00:10:38.360><c> optimized</c><00:10:38.839><c> this</c>

00:10:38.990 --> 00:10:39.000 align:start position:0%
let me know if you've optimized this
 

00:10:39.000 --> 00:10:40.670 align:start position:0%
let me know if you've optimized this
more<00:10:39.279><c> maybe</c><00:10:39.440><c> you</c><00:10:39.560><c> got</c><00:10:39.720><c> better</c><00:10:39.959><c> results</c><00:10:40.480><c> maybe</c>

00:10:40.670 --> 00:10:40.680 align:start position:0%
more maybe you got better results maybe
 

00:10:40.680 --> 00:10:42.430 align:start position:0%
more maybe you got better results maybe
you<00:10:40.839><c> did</c><00:10:41.079><c> more</c><00:10:41.360><c> with</c><00:10:41.480><c> the</c><00:10:41.680><c> product</c><00:10:42.120><c> you</c><00:10:42.240><c> know</c><00:10:42.360><c> I</c>

00:10:42.430 --> 00:10:42.440 align:start position:0%
you did more with the product you know I
 

00:10:42.440 --> 00:10:44.550 align:start position:0%
you did more with the product you know I
would<00:10:42.720><c> love</c><00:10:43.000><c> to</c><00:10:43.200><c> hear</c><00:10:43.440><c> from</c><00:10:43.639><c> you</c><00:10:43.959><c> and</c><00:10:44.240><c> see</c><00:10:44.440><c> what</c>

00:10:44.550 --> 00:10:44.560 align:start position:0%
would love to hear from you and see what
 

00:10:44.560 --> 00:10:45.829 align:start position:0%
would love to hear from you and see what
you<00:10:44.720><c> guys</c><00:10:44.839><c> have</c><00:10:45.000><c> done</c><00:10:45.399><c> okay</c><00:10:45.519><c> and</c><00:10:45.600><c> if</c><00:10:45.680><c> you</c><00:10:45.760><c> have</c>

00:10:45.829 --> 00:10:45.839 align:start position:0%
you guys have done okay and if you have
 

00:10:45.839 --> 00:10:47.829 align:start position:0%
you guys have done okay and if you have
any<00:10:46.000><c> issues</c><00:10:46.480><c> leave</c><00:10:46.680><c> them</c><00:10:46.920><c> comments</c><00:10:47.240><c> Below</c>

00:10:47.829 --> 00:10:47.839 align:start position:0%
any issues leave them comments Below
 

00:10:47.839 --> 00:10:49.069 align:start position:0%
any issues leave them comments Below
have<00:10:47.959><c> a</c><00:10:48.079><c> great</c><00:10:48.279><c> day</c><00:10:48.440><c> and</c><00:10:48.519><c> I'll</c><00:10:48.639><c> see</c><00:10:48.760><c> you</c><00:10:48.920><c> guys</c>

00:10:49.069 --> 00:10:49.079 align:start position:0%
have a great day and I'll see you guys
 

00:10:49.079 --> 00:10:51.560 align:start position:0%
have a great day and I'll see you guys
next<00:10:49.279><c> time</c>

