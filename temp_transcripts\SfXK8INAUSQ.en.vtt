WEBVTT
Kind: captions
Language: en

00:00:01.120 --> 00:00:02.990 align:start position:0%
 
all<00:00:01.319><c> the</c><00:00:01.439><c> latest</c><00:00:01.719><c> news</c><00:00:01.920><c> from</c><00:00:02.120><c> Microsoft</c><00:00:02.560><c> build</c>

00:00:02.990 --> 00:00:03.000 align:start position:0%
all the latest news from Microsoft build
 

00:00:03.000 --> 00:00:05.230 align:start position:0%
all the latest news from Microsoft build
including<00:00:03.480><c> GitHub</c><00:00:03.840><c> co-pilot</c><00:00:04.319><c> extensions</c><00:00:05.080><c> and</c>

00:00:05.230 --> 00:00:05.240 align:start position:0%
including GitHub co-pilot extensions and
 

00:00:05.240 --> 00:00:08.190 align:start position:0%
including GitHub co-pilot extensions and
a<00:00:05.400><c> new</c><00:00:05.560><c> dev</c><00:00:05.920><c> kit</c><00:00:06.200><c> from</c><00:00:06.480><c> Qualcomm</c><00:00:07.439><c> plus</c><00:00:07.680><c> atha</c>

00:00:08.190 --> 00:00:08.200 align:start position:0%
a new dev kit from Qualcomm plus atha
 

00:00:08.200 --> 00:00:10.709 align:start position:0%
a new dev kit from Qualcomm plus atha
crisis<00:00:08.559><c> goes</c><00:00:08.800><c> open</c><00:00:09.160><c> source</c><00:00:09.800><c> and</c><00:00:10.120><c> the</c><00:00:10.200><c> nerdiest</c>

00:00:10.709 --> 00:00:10.719 align:start position:0%
crisis goes open source and the nerdiest
 

00:00:10.719 --> 00:00:13.110 align:start position:0%
crisis goes open source and the nerdiest
way<00:00:10.840><c> to</c><00:00:11.000><c> control</c><00:00:11.400><c> your</c><00:00:11.679><c> concert</c><00:00:12.120><c> wristband</c>

00:00:13.110 --> 00:00:13.120 align:start position:0%
way to control your concert wristband
 

00:00:13.120 --> 00:00:17.840 align:start position:0%
way to control your concert wristband
all<00:00:13.320><c> that</c><00:00:13.440><c> and</c><00:00:13.599><c> more</c><00:00:13.839><c> on</c><00:00:14.040><c> this</c><00:00:14.200><c> episode</c><00:00:14.639><c> of</c><00:00:15.080><c> the</c>

00:00:17.840 --> 00:00:17.850 align:start position:0%
 
 

00:00:17.850 --> 00:00:20.269 align:start position:0%
 
[Music]

00:00:20.269 --> 00:00:20.279 align:start position:0%
[Music]
 

00:00:20.279 --> 00:00:22.670 align:start position:0%
[Music]
download<00:00:21.279><c> welcome</c><00:00:21.600><c> back</c><00:00:21.720><c> to</c><00:00:21.880><c> another</c><00:00:22.160><c> episode</c>

00:00:22.670 --> 00:00:22.680 align:start position:0%
download welcome back to another episode
 

00:00:22.680 --> 00:00:24.670 align:start position:0%
download welcome back to another episode
of<00:00:22.840><c> the</c><00:00:23.039><c> download</c><00:00:23.640><c> I'm</c><00:00:23.760><c> your</c><00:00:24.000><c> host</c><00:00:24.240><c> Christina</c>

00:00:24.670 --> 00:00:24.680 align:start position:0%
of the download I'm your host Christina
 

00:00:24.680 --> 00:00:26.310 align:start position:0%
of the download I'm your host Christina
Warren<00:00:25.080><c> senior</c><00:00:25.400><c> developer</c><00:00:25.800><c> Advocate</c><00:00:26.160><c> at</c>

00:00:26.310 --> 00:00:26.320 align:start position:0%
Warren senior developer Advocate at
 

00:00:26.320 --> 00:00:28.150 align:start position:0%
Warren senior developer Advocate at
GitHub<00:00:27.119><c> and</c><00:00:27.400><c> this</c><00:00:27.519><c> is</c><00:00:27.640><c> the</c><00:00:27.720><c> show</c><00:00:27.920><c> where</c><00:00:28.039><c> we</c>

00:00:28.150 --> 00:00:28.160 align:start position:0%
GitHub and this is the show where we
 

00:00:28.160 --> 00:00:29.710 align:start position:0%
GitHub and this is the show where we
cover<00:00:28.359><c> the</c><00:00:28.480><c> latest</c><00:00:28.760><c> developer</c><00:00:29.119><c> news</c><00:00:29.359><c> and</c><00:00:29.519><c> open</c>

00:00:29.710 --> 00:00:29.720 align:start position:0%
cover the latest developer news and open
 

00:00:29.720 --> 00:00:31.509 align:start position:0%
cover the latest developer news and open
source<00:00:30.119><c> Projects</c><00:00:30.920><c> please</c><00:00:31.199><c> like</c><00:00:31.359><c> And</c>

00:00:31.509 --> 00:00:31.519 align:start position:0%
source Projects please like And
 

00:00:31.519 --> 00:00:34.549 align:start position:0%
source Projects please like And
subscribe<00:00:32.439><c> so</c><00:00:32.880><c> my</c><00:00:33.040><c> shirt</c><00:00:33.360><c> this</c><00:00:33.520><c> week</c><00:00:33.879><c> is</c><00:00:34.200><c> not</c>

00:00:34.549 --> 00:00:34.559 align:start position:0%
subscribe so my shirt this week is not
 

00:00:34.559 --> 00:00:36.910 align:start position:0%
subscribe so my shirt this week is not
what<00:00:34.719><c> I</c><00:00:34.800><c> want</c><00:00:34.879><c> to</c><00:00:35.040><c> focus</c><00:00:35.360><c> on</c><00:00:36.120><c> it's</c><00:00:36.399><c> fine</c><00:00:36.800><c> but</c>

00:00:36.910 --> 00:00:36.920 align:start position:0%
what I want to focus on it's fine but
 

00:00:36.920 --> 00:00:38.910 align:start position:0%
what I want to focus on it's fine but
but<00:00:37.040><c> it's</c><00:00:37.160><c> not</c><00:00:37.320><c> why</c><00:00:37.440><c> we're</c><00:00:37.640><c> here</c><00:00:38.280><c> we're</c><00:00:38.600><c> here</c>

00:00:38.910 --> 00:00:38.920 align:start position:0%
but it's not why we're here we're here
 

00:00:38.920 --> 00:00:42.270 align:start position:0%
but it's not why we're here we're here
because<00:00:39.360><c> I</c><00:00:39.480><c> got</c><00:00:39.760><c> these</c><00:00:40.120><c> incredible</c><00:00:41.120><c> 404</c><00:00:42.000><c> Air</c>

00:00:42.270 --> 00:00:42.280 align:start position:0%
because I got these incredible 404 Air
 

00:00:42.280 --> 00:00:44.470 align:start position:0%
because I got these incredible 404 Air
Force<00:00:42.520><c> Ones</c><00:00:42.840><c> from</c><00:00:43.000><c> Nike</c><00:00:43.960><c> this</c><00:00:44.079><c> was</c><00:00:44.239><c> actually</c>

00:00:44.470 --> 00:00:44.480 align:start position:0%
Force Ones from Nike this was actually
 

00:00:44.480 --> 00:00:46.110 align:start position:0%
Force Ones from Nike this was actually
an<00:00:44.600><c> official</c><00:00:44.920><c> drop</c><00:00:45.280><c> not</c><00:00:45.480><c> something</c><00:00:45.760><c> that</c><00:00:45.960><c> I</c>

00:00:46.110 --> 00:00:46.120 align:start position:0%
an official drop not something that I
 

00:00:46.120 --> 00:00:48.430 align:start position:0%
an official drop not something that I
had<00:00:46.320><c> designed</c><00:00:47.000><c> and</c><00:00:47.199><c> you</c><00:00:47.440><c> guys</c><00:00:47.680><c> you</c><00:00:48.000><c> have</c><00:00:48.199><c> to</c>

00:00:48.430 --> 00:00:48.440 align:start position:0%
had designed and you guys you have to
 

00:00:48.440 --> 00:00:55.510 align:start position:0%
had designed and you guys you have to
check<00:00:48.719><c> them</c>

00:00:55.510 --> 00:00:55.520 align:start position:0%
 
 

00:00:55.520 --> 00:00:57.790 align:start position:0%
 
out<00:00:56.520><c> not</c><00:00:56.680><c> only</c><00:00:56.920><c> are</c><00:00:57.079><c> they</c><00:00:57.199><c> blue</c><00:00:57.399><c> screen</c><00:00:57.640><c> of</c>

00:00:57.790 --> 00:00:57.800 align:start position:0%
out not only are they blue screen of
 

00:00:57.800 --> 00:01:00.790 align:start position:0%
out not only are they blue screen of
death<00:00:58.000><c> blue</c><00:00:58.559><c> but</c><00:00:58.760><c> they</c><00:00:58.920><c> have</c><00:00:59.120><c> a</c><00:00:59.239><c> 404</c><00:01:00.160><c> on</c><00:01:00.359><c> them</c>

00:01:00.790 --> 00:01:00.800 align:start position:0%
death blue but they have a 404 on them
 

00:01:00.800 --> 00:01:02.830 align:start position:0%
death blue but they have a 404 on them
now<00:01:01.280><c> obviously</c><00:01:01.760><c> I</c><00:01:01.879><c> wore</c><00:01:02.160><c> these</c><00:01:02.280><c> to</c><00:01:02.399><c> Microsoft</c>

00:01:02.830 --> 00:01:02.840 align:start position:0%
now obviously I wore these to Microsoft
 

00:01:02.840 --> 00:01:06.190 align:start position:0%
now obviously I wore these to Microsoft
build<00:01:03.239><c> because</c><00:01:03.960><c> how</c><00:01:04.119><c> could</c><00:01:04.239><c> I</c><00:01:04.439><c> not</c><00:01:05.439><c> which</c><00:01:05.920><c> is</c><00:01:06.080><c> a</c>

00:01:06.190 --> 00:01:06.200 align:start position:0%
build because how could I not which is a
 

00:01:06.200 --> 00:01:08.429 align:start position:0%
build because how could I not which is a
great<00:01:06.400><c> segue</c><00:01:06.960><c> into</c><00:01:07.240><c> a</c><00:01:07.400><c> Roundup</c><00:01:07.840><c> of</c><00:01:08.040><c> Microsoft</c>

00:01:08.429 --> 00:01:08.439 align:start position:0%
great segue into a Roundup of Microsoft
 

00:01:08.439 --> 00:01:10.390 align:start position:0%
great segue into a Roundup of Microsoft
build<00:01:08.720><c> news</c><00:01:09.119><c> because</c><00:01:09.640><c> that's</c><00:01:09.840><c> where</c><00:01:10.000><c> we</c><00:01:10.119><c> were</c>

00:01:10.390 --> 00:01:10.400 align:start position:0%
build news because that's where we were
 

00:01:10.400 --> 00:01:12.550 align:start position:0%
build news because that's where we were
last<00:01:10.640><c> week</c><00:01:11.200><c> so</c><00:01:11.479><c> yes</c><00:01:11.680><c> Microsoft</c><00:01:12.119><c> build</c><00:01:12.360><c> took</c>

00:01:12.550 --> 00:01:12.560 align:start position:0%
last week so yes Microsoft build took
 

00:01:12.560 --> 00:01:14.789 align:start position:0%
last week so yes Microsoft build took
place<00:01:12.920><c> last</c><00:01:13.159><c> week</c><00:01:13.439><c> in</c><00:01:13.640><c> Seattle</c><00:01:14.080><c> Washington</c>

00:01:14.789 --> 00:01:14.799 align:start position:0%
place last week in Seattle Washington
 

00:01:14.799 --> 00:01:16.310 align:start position:0%
place last week in Seattle Washington
and<00:01:14.960><c> there</c><00:01:15.119><c> was</c><00:01:15.400><c> tons</c><00:01:15.640><c> and</c><00:01:15.799><c> tons</c><00:01:16.159><c> of</c>

00:01:16.310 --> 00:01:16.320 align:start position:0%
and there was tons and tons of
 

00:01:16.320 --> 00:01:18.830 align:start position:0%
and there was tons and tons of
announcements<00:01:17.240><c> many</c><00:01:17.439><c> of</c><00:01:17.560><c> them</c><00:01:17.720><c> AI</c><00:01:18.040><c> based</c><00:01:18.600><c> and</c>

00:01:18.830 --> 00:01:18.840 align:start position:0%
announcements many of them AI based and
 

00:01:18.840 --> 00:01:20.190 align:start position:0%
announcements many of them AI based and
I've<00:01:18.960><c> got</c><00:01:19.040><c> a</c><00:01:19.200><c> Linked</c><00:01:19.520><c> In</c><00:01:19.600><c> the</c><00:01:19.680><c> show</c><00:01:19.920><c> notes</c><00:01:20.119><c> in</c>

00:01:20.190 --> 00:01:20.200 align:start position:0%
I've got a Linked In the show notes in
 

00:01:20.200 --> 00:01:22.550 align:start position:0%
I've got a Linked In the show notes in
the<00:01:20.280><c> description</c><00:01:20.759><c> to</c><00:01:21.000><c> the</c><00:01:21.119><c> full</c><00:01:21.720><c> book</c><00:01:21.920><c> of</c><00:01:22.079><c> news</c>

00:01:22.550 --> 00:01:22.560 align:start position:0%
the description to the full book of news
 

00:01:22.560 --> 00:01:24.149 align:start position:0%
the description to the full book of news
if<00:01:22.640><c> you</c><00:01:22.759><c> want</c><00:01:22.840><c> to</c><00:01:23.000><c> check</c><00:01:23.240><c> that</c><00:01:23.360><c> out</c><00:01:23.840><c> but</c><00:01:24.040><c> here</c>

00:01:24.149 --> 00:01:24.159 align:start position:0%
if you want to check that out but here
 

00:01:24.159 --> 00:01:26.429 align:start position:0%
if you want to check that out but here
are<00:01:24.240><c> a</c><00:01:24.320><c> couple</c><00:01:24.479><c> of</c><00:01:24.640><c> highlights</c><00:01:25.640><c> first</c><00:01:26.119><c> on</c><00:01:26.280><c> the</c>

00:01:26.429 --> 00:01:26.439 align:start position:0%
are a couple of highlights first on the
 

00:01:26.439 --> 00:01:28.350 align:start position:0%
are a couple of highlights first on the
GitHub<00:01:26.759><c> front</c><00:01:27.200><c> we</c><00:01:27.360><c> announced</c><00:01:27.880><c> GitHub</c>

00:01:28.350 --> 00:01:28.360 align:start position:0%
GitHub front we announced GitHub
 

00:01:28.360 --> 00:01:31.069 align:start position:0%
GitHub front we announced GitHub
co-pilot<00:01:28.960><c> extensions</c><00:01:30.200><c> look</c><00:01:30.520><c> we</c><00:01:30.640><c> know</c><00:01:30.840><c> how</c>

00:01:31.069 --> 00:01:31.079 align:start position:0%
co-pilot extensions look we know how
 

00:01:31.079 --> 00:01:32.830 align:start position:0%
co-pilot extensions look we know how
great<00:01:31.320><c> GitHub</c><00:01:31.680><c> co-pilot</c><00:01:32.159><c> is</c><00:01:32.439><c> whether</c><00:01:32.640><c> you're</c>

00:01:32.830 --> 00:01:32.840 align:start position:0%
great GitHub co-pilot is whether you're
 

00:01:32.840 --> 00:01:34.749 align:start position:0%
great GitHub co-pilot is whether you're
using<00:01:33.159><c> it</c><00:01:33.280><c> on</c><00:01:33.439><c> github.com</c><00:01:34.040><c> or</c><00:01:34.479><c> in</c><00:01:34.600><c> your</c>

00:01:34.749 --> 00:01:34.759 align:start position:0%
using it on github.com or in your
 

00:01:34.759 --> 00:01:37.429 align:start position:0%
using it on github.com or in your
favorite<00:01:35.040><c> IDE</c><00:01:36.000><c> and</c><00:01:36.159><c> now</c><00:01:36.399><c> you</c><00:01:36.479><c> can</c><00:01:36.720><c> extend</c><00:01:37.280><c> them</c>

00:01:37.429 --> 00:01:37.439 align:start position:0%
favorite IDE and now you can extend them
 

00:01:37.439 --> 00:01:39.149 align:start position:0%
favorite IDE and now you can extend them
to<00:01:37.640><c> work</c><00:01:37.920><c> alongside</c><00:01:38.439><c> some</c><00:01:38.560><c> of</c><00:01:38.680><c> your</c><00:01:38.799><c> favorite</c>

00:01:39.149 --> 00:01:39.159 align:start position:0%
to work alongside some of your favorite
 

00:01:39.159 --> 00:01:41.310 align:start position:0%
to work alongside some of your favorite
services<00:01:40.159><c> so</c><00:01:40.320><c> to</c><00:01:40.439><c> start</c><00:01:40.680><c> out</c><00:01:40.840><c> with</c><00:01:41.000><c> GitHub</c>

00:01:41.310 --> 00:01:41.320 align:start position:0%
services so to start out with GitHub
 

00:01:41.320 --> 00:01:43.149 align:start position:0%
services so to start out with GitHub
co-pilot<00:01:41.759><c> extensions</c><00:01:42.320><c> are</c><00:01:42.520><c> available</c><00:01:42.920><c> from</c>

00:01:43.149 --> 00:01:43.159 align:start position:0%
co-pilot extensions are available from
 

00:01:43.159 --> 00:01:46.350 align:start position:0%
co-pilot extensions are available from
Partners<00:01:43.759><c> like</c><00:01:44.119><c> data</c><00:01:44.479><c> Stacks</c><00:01:45.000><c> Docker</c><00:01:45.880><c> Lambda</c>

00:01:46.350 --> 00:01:46.360 align:start position:0%
Partners like data Stacks Docker Lambda
 

00:01:46.360 --> 00:01:49.069 align:start position:0%
Partners like data Stacks Docker Lambda
test<00:01:46.719><c> launch</c><00:01:47.040><c> darkley</c><00:01:47.719><c> McKenzie</c><00:01:48.200><c> and</c><00:01:48.360><c> Company</c>

00:01:49.069 --> 00:01:49.079 align:start position:0%
test launch darkley McKenzie and Company
 

00:01:49.079 --> 00:01:51.910 align:start position:0%
test launch darkley McKenzie and Company
Microsoft<00:01:49.560><c> Azure</c><00:01:49.960><c> and</c><00:01:50.200><c> teams</c><00:01:50.799><c> mongod</c><00:01:51.200><c> DB</c>

00:01:51.910 --> 00:01:51.920 align:start position:0%
Microsoft Azure and teams mongod DB
 

00:01:51.920 --> 00:01:55.230 align:start position:0%
Microsoft Azure and teams mongod DB
octopus<00:01:52.399><c> deploy</c><00:01:53.159><c> Pangia</c><00:01:53.920><c> pine</c><00:01:54.240><c> cone</c><00:01:54.880><c> product</c>

00:01:55.230 --> 00:01:55.240 align:start position:0%
octopus deploy Pangia pine cone product
 

00:01:55.240 --> 00:01:58.069 align:start position:0%
octopus deploy Pangia pine cone product
science<00:01:55.680><c> readme</c><00:01:56.360><c> Sentry</c><00:01:56.920><c> and</c><00:01:57.119><c> stripe</c><00:01:57.880><c> and</c>

00:01:58.069 --> 00:01:58.079 align:start position:0%
science readme Sentry and stripe and
 

00:01:58.079 --> 00:01:59.469 align:start position:0%
science readme Sentry and stripe and
extensions<00:01:58.479><c> are</c><00:01:58.640><c> supported</c><00:01:59.000><c> in</c><00:01:59.159><c> GitHub</c>

00:01:59.469 --> 00:01:59.479 align:start position:0%
extensions are supported in GitHub
 

00:01:59.479 --> 00:02:01.789 align:start position:0%
extensions are supported in GitHub
co-pilot<00:02:00.000><c> chat</c><00:02:00.240><c> on</c><00:02:00.479><c> github.com</c><00:02:01.039><c> Visual</c>

00:02:01.789 --> 00:02:01.799 align:start position:0%
co-pilot chat on github.com Visual
 

00:02:01.799 --> 00:02:04.429 align:start position:0%
co-pilot chat on github.com Visual
Studio<00:02:02.240><c> as</c><00:02:02.360><c> well</c><00:02:02.479><c> as</c><00:02:02.600><c> vs</c><00:02:02.960><c> code</c><00:02:03.960><c> and</c><00:02:04.200><c> although</c>

00:02:04.429 --> 00:02:04.439 align:start position:0%
Studio as well as vs code and although
 

00:02:04.439 --> 00:02:06.190 align:start position:0%
Studio as well as vs code and although
the<00:02:04.560><c> GitHub</c><00:02:04.880><c> Marketplace</c><00:02:05.479><c> is</c><00:02:05.600><c> going</c><00:02:05.799><c> to</c><00:02:05.960><c> offer</c>

00:02:06.190 --> 00:02:06.200 align:start position:0%
the GitHub Marketplace is going to offer
 

00:02:06.200 --> 00:02:08.550 align:start position:0%
the GitHub Marketplace is going to offer
extensions<00:02:06.680><c> to</c><00:02:06.920><c> everyone</c><00:02:07.640><c> organizations</c><00:02:08.399><c> can</c>

00:02:08.550 --> 00:02:08.560 align:start position:0%
extensions to everyone organizations can
 

00:02:08.560 --> 00:02:10.309 align:start position:0%
extensions to everyone organizations can
also<00:02:08.759><c> choose</c><00:02:09.000><c> to</c><00:02:09.160><c> create</c><00:02:09.520><c> private</c><00:02:09.800><c> copilot</c>

00:02:10.309 --> 00:02:10.319 align:start position:0%
also choose to create private copilot
 

00:02:10.319 --> 00:02:12.630 align:start position:0%
also choose to create private copilot
extensions<00:02:10.920><c> for</c><00:02:11.120><c> their</c><00:02:11.239><c> own</c><00:02:11.400><c> tooling</c><00:02:11.800><c> too</c><00:02:12.520><c> and</c>

00:02:12.630 --> 00:02:12.640 align:start position:0%
extensions for their own tooling too and
 

00:02:12.640 --> 00:02:14.430 align:start position:0%
extensions for their own tooling too and
so<00:02:12.959><c> how</c><00:02:13.160><c> these</c><00:02:13.360><c> extensions</c><00:02:13.840><c> work</c><00:02:14.040><c> is</c><00:02:14.239><c> actually</c>

00:02:14.430 --> 00:02:14.440 align:start position:0%
so how these extensions work is actually
 

00:02:14.440 --> 00:02:16.430 align:start position:0%
so how these extensions work is actually
pretty<00:02:14.640><c> slick</c><00:02:15.080><c> so</c><00:02:15.239><c> for</c><00:02:15.440><c> instance</c><00:02:16.160><c> in</c><00:02:16.319><c> the</c>

00:02:16.430 --> 00:02:16.440 align:start position:0%
pretty slick so for instance in the
 

00:02:16.440 --> 00:02:18.430 align:start position:0%
pretty slick so for instance in the
GitHub<00:02:16.760><c> co-pilot</c><00:02:17.160><c> for</c><00:02:17.319><c> Azure</c><00:02:17.680><c> extension</c><00:02:18.319><c> you</c>

00:02:18.430 --> 00:02:18.440 align:start position:0%
GitHub co-pilot for Azure extension you
 

00:02:18.440 --> 00:02:20.790 align:start position:0%
GitHub co-pilot for Azure extension you
can<00:02:18.640><c> call</c><00:02:18.879><c> up</c><00:02:19.080><c> co-pilot</c><00:02:19.519><c> for</c><00:02:19.640><c> Azure</c><00:02:20.000><c> directly</c>

00:02:20.790 --> 00:02:20.800 align:start position:0%
can call up co-pilot for Azure directly
 

00:02:20.800 --> 00:02:22.949 align:start position:0%
can call up co-pilot for Azure directly
inside<00:02:21.200><c> GitHub</c><00:02:21.519><c> co-pilot</c><00:02:21.959><c> chat</c><00:02:22.560><c> and</c><00:02:22.640><c> then</c><00:02:22.800><c> get</c>

00:02:22.949 --> 00:02:22.959 align:start position:0%
inside GitHub co-pilot chat and then get
 

00:02:22.959 --> 00:02:25.190 align:start position:0%
inside GitHub co-pilot chat and then get
info<00:02:23.319><c> about</c><00:02:23.680><c> deploying</c><00:02:24.120><c> your</c><00:02:24.239><c> code</c><00:02:24.480><c> to</c><00:02:24.840><c> Azure</c>

00:02:25.190 --> 00:02:25.200 align:start position:0%
info about deploying your code to Azure
 

00:02:25.200 --> 00:02:27.150 align:start position:0%
info about deploying your code to Azure
services<00:02:25.640><c> and</c><00:02:25.840><c> and</c><00:02:26.000><c> whatnot</c><00:02:26.720><c> and</c><00:02:26.840><c> I've</c><00:02:26.959><c> got</c><00:02:27.080><c> a</c>

00:02:27.150 --> 00:02:27.160 align:start position:0%
services and and whatnot and I've got a
 

00:02:27.160 --> 00:02:29.030 align:start position:0%
services and and whatnot and I've got a
bunch<00:02:27.280><c> of</c><00:02:27.400><c> links</c><00:02:27.640><c> down</c><00:02:27.840><c> below</c><00:02:28.480><c> um</c><00:02:28.800><c> to</c><00:02:28.920><c> the</c>

00:02:29.030 --> 00:02:29.040 align:start position:0%
bunch of links down below um to the
 

00:02:29.040 --> 00:02:31.030 align:start position:0%
bunch of links down below um to the
GitHub<00:02:29.360><c> Blog</c><00:02:29.560><c> the</c><00:02:30.080><c> code</c><00:02:30.280><c> page</c><00:02:30.519><c> for</c><00:02:30.680><c> how</c><00:02:30.840><c> their</c>

00:02:31.030 --> 00:02:31.040 align:start position:0%
GitHub Blog the code page for how their
 

00:02:31.040 --> 00:02:32.670 align:start position:0%
GitHub Blog the code page for how their
chat<00:02:31.319><c> API</c><00:02:31.720><c> works</c><00:02:32.120><c> if</c><00:02:32.200><c> you</c><00:02:32.280><c> want</c><00:02:32.400><c> to</c><00:02:32.480><c> build</c>

00:02:32.670 --> 00:02:32.680 align:start position:0%
chat API works if you want to build
 

00:02:32.680 --> 00:02:34.949 align:start position:0%
chat API works if you want to build
features<00:02:33.040><c> into</c><00:02:33.319><c> your</c><00:02:33.440><c> vs</c><00:02:33.760><c> code</c><00:02:33.959><c> extension</c><00:02:34.800><c> and</c>

00:02:34.949 --> 00:02:34.959 align:start position:0%
features into your vs code extension and
 

00:02:34.959 --> 00:02:36.589 align:start position:0%
features into your vs code extension and
links<00:02:35.200><c> to</c><00:02:35.319><c> our</c><00:02:35.440><c> co-pilot</c><00:02:35.840><c> partner</c><00:02:36.160><c> program</c><00:02:36.519><c> if</c>

00:02:36.589 --> 00:02:36.599 align:start position:0%
links to our co-pilot partner program if
 

00:02:36.599 --> 00:02:37.910 align:start position:0%
links to our co-pilot partner program if
you<00:02:36.680><c> want</c><00:02:36.800><c> to</c><00:02:36.920><c> build</c><00:02:37.160><c> an</c><00:02:37.319><c> extension</c><00:02:37.640><c> of</c><00:02:37.760><c> your</c>

00:02:37.910 --> 00:02:37.920 align:start position:0%
you want to build an extension of your
 

00:02:37.920 --> 00:02:40.750 align:start position:0%
you want to build an extension of your
own<00:02:38.120><c> for</c><00:02:38.280><c> your</c><00:02:38.400><c> own</c><00:02:38.720><c> service</c><00:02:39.720><c> and</c><00:02:40.080><c> speaking</c><00:02:40.400><c> of</c>

00:02:40.750 --> 00:02:40.760 align:start position:0%
own for your own service and speaking of
 

00:02:40.760 --> 00:02:43.030 align:start position:0%
own for your own service and speaking of
co-pilots<00:02:41.760><c> all</c><00:02:41.920><c> of</c><00:02:42.040><c> the</c><00:02:42.159><c> co-pilots</c><00:02:42.720><c> were</c><00:02:42.879><c> out</c>

00:02:43.030 --> 00:02:43.040 align:start position:0%
co-pilots all of the co-pilots were out
 

00:02:43.040 --> 00:02:45.229 align:start position:0%
co-pilots all of the co-pilots were out
and<00:02:43.200><c> about</c><00:02:43.440><c> at</c><00:02:43.519><c> Microsoft</c><00:02:43.920><c> build</c><00:02:44.319><c> yes</c><00:02:44.519><c> all</c><00:02:44.760><c> 99</c>

00:02:45.229 --> 00:02:45.239 align:start position:0%
and about at Microsoft build yes all 99
 

00:02:45.239 --> 00:02:47.509 align:start position:0%
and about at Microsoft build yes all 99
of<00:02:45.400><c> them</c><00:02:46.040><c> but</c><00:02:46.200><c> small</c><00:02:46.480><c> language</c><00:02:46.840><c> models</c><00:02:47.360><c> like</c>

00:02:47.509 --> 00:02:47.519 align:start position:0%
of them but small language models like
 

00:02:47.519 --> 00:02:49.869 align:start position:0%
of them but small language models like
53<00:02:48.519><c> uh</c><00:02:48.640><c> and</c><00:02:48.760><c> we</c><00:02:48.879><c> talked</c><00:02:49.120><c> about</c><00:02:49.319><c> that</c><00:02:49.440><c> series</c><00:02:49.760><c> a</c>

00:02:49.869 --> 00:02:49.879 align:start position:0%
53 uh and we talked about that series a
 

00:02:49.879 --> 00:02:51.630 align:start position:0%
53 uh and we talked about that series a
couple<00:02:50.120><c> weeks</c><00:02:50.319><c> ago</c><00:02:50.800><c> those</c><00:02:50.959><c> were</c><00:02:51.120><c> on</c><00:02:51.280><c> display</c>

00:02:51.630 --> 00:02:51.640 align:start position:0%
couple weeks ago those were on display
 

00:02:51.640 --> 00:02:53.710 align:start position:0%
couple weeks ago those were on display
too<00:02:52.280><c> so</c><00:02:52.519><c> one</c><00:02:52.640><c> of</c><00:02:52.720><c> the</c><00:02:52.840><c> big</c><00:02:53.000><c> announcements</c><00:02:53.560><c> that</c>

00:02:53.710 --> 00:02:53.720 align:start position:0%
too so one of the big announcements that
 

00:02:53.720 --> 00:02:55.509 align:start position:0%
too so one of the big announcements that
happened<00:02:54.159><c> right</c><00:02:54.280><c> before</c><00:02:54.560><c> Microsoft</c><00:02:55.000><c> build</c>

00:02:55.509 --> 00:02:55.519 align:start position:0%
happened right before Microsoft build
 

00:02:55.519 --> 00:02:58.229 align:start position:0%
happened right before Microsoft build
was<00:02:55.760><c> the</c><00:02:55.879><c> new</c><00:02:56.080><c> co-pilot</c><00:02:56.640><c> plus</c><00:02:57.000><c> series</c><00:02:57.280><c> of</c><00:02:57.440><c> PCS</c>

00:02:58.229 --> 00:02:58.239 align:start position:0%
was the new co-pilot plus series of PCS
 

00:02:58.239 --> 00:03:00.390 align:start position:0%
was the new co-pilot plus series of PCS
from<00:02:58.400><c> Dell</c><00:02:58.720><c> and</c><00:02:58.840><c> HP</c><00:02:59.280><c> and</c><00:02:59.400><c> Microsoft</c><00:03:00.080><c> and</c><00:03:00.200><c> and</c>

00:03:00.390 --> 00:03:00.400 align:start position:0%
from Dell and HP and Microsoft and and
 

00:03:00.400 --> 00:03:02.550 align:start position:0%
from Dell and HP and Microsoft and and
others<00:03:01.239><c> and</c><00:03:01.599><c> these</c><00:03:01.760><c> are</c><00:03:01.920><c> the</c><00:03:02.040><c> next</c><00:03:02.200><c> generation</c>

00:03:02.550 --> 00:03:02.560 align:start position:0%
others and these are the next generation
 

00:03:02.560 --> 00:03:04.789 align:start position:0%
others and these are the next generation
of<00:03:02.720><c> laptops</c><00:03:03.159><c> that</c><00:03:03.239><c> are</c><00:03:03.480><c> optimized</c><00:03:04.159><c> for</c><00:03:04.480><c> AI</c>

00:03:04.789 --> 00:03:04.799 align:start position:0%
of laptops that are optimized for AI
 

00:03:04.799 --> 00:03:06.149 align:start position:0%
of laptops that are optimized for AI
workloads<00:03:05.360><c> and</c><00:03:05.480><c> they're</c><00:03:05.680><c> powered</c><00:03:05.959><c> by</c><00:03:06.080><c> the</c>

00:03:06.149 --> 00:03:06.159 align:start position:0%
workloads and they're powered by the
 

00:03:06.159 --> 00:03:09.030 align:start position:0%
workloads and they're powered by the
latest<00:03:06.519><c> Qualcomm</c><00:03:07.200><c> Snapdragon</c><00:03:08.040><c> processors</c>

00:03:09.030 --> 00:03:09.040 align:start position:0%
latest Qualcomm Snapdragon processors
 

00:03:09.040 --> 00:03:11.390 align:start position:0%
latest Qualcomm Snapdragon processors
and<00:03:09.319><c> you</c><00:03:09.560><c> guys</c><00:03:10.319><c> after</c><00:03:10.560><c> years</c><00:03:10.720><c> of</c><00:03:10.920><c> promises</c>

00:03:11.390 --> 00:03:11.400 align:start position:0%
and you guys after years of promises
 

00:03:11.400 --> 00:03:13.869 align:start position:0%
and you guys after years of promises
that<00:03:11.519><c> arm</c><00:03:11.760><c> on</c><00:03:11.959><c> Windows</c><00:03:12.360><c> would</c><00:03:12.560><c> be</c><00:03:12.680><c> a</c><00:03:12.840><c> thing</c><00:03:13.680><c> I'm</c>

00:03:13.869 --> 00:03:13.879 align:start position:0%
that arm on Windows would be a thing I'm
 

00:03:13.879 --> 00:03:15.630 align:start position:0%
that arm on Windows would be a thing I'm
really<00:03:14.120><c> really</c><00:03:14.360><c> hopeful</c><00:03:14.879><c> that</c><00:03:15.120><c> this</c><00:03:15.360><c> time</c>

00:03:15.630 --> 00:03:15.640 align:start position:0%
really really hopeful that this time
 

00:03:15.640 --> 00:03:17.750 align:start position:0%
really really hopeful that this time
it's<00:03:15.879><c> for</c><00:03:16.080><c> real</c><00:03:16.840><c> Qualcomm</c><00:03:17.280><c> has</c><00:03:17.400><c> done</c><00:03:17.560><c> some</c>

00:03:17.750 --> 00:03:17.760 align:start position:0%
it's for real Qualcomm has done some
 

00:03:17.760 --> 00:03:19.390 align:start position:0%
it's for real Qualcomm has done some
really<00:03:17.959><c> cool</c><00:03:18.239><c> stuff</c><00:03:18.480><c> on</c><00:03:18.599><c> the</c><00:03:18.720><c> hardware</c><00:03:19.159><c> side</c>

00:03:19.390 --> 00:03:19.400 align:start position:0%
really cool stuff on the hardware side
 

00:03:19.400 --> 00:03:21.630 align:start position:0%
really cool stuff on the hardware side
including<00:03:19.840><c> a</c><00:03:19.959><c> new</c><00:03:20.159><c> mpu</c><00:03:20.959><c> that</c><00:03:21.280><c> exists</c>

00:03:21.630 --> 00:03:21.640 align:start position:0%
including a new mpu that exists
 

00:03:21.640 --> 00:03:24.309 align:start position:0%
including a new mpu that exists
alongside<00:03:22.280><c> like</c><00:03:22.519><c> CPU</c><00:03:22.959><c> and</c><00:03:23.120><c> and</c><00:03:23.280><c> GPU</c><00:03:24.080><c> and</c><00:03:24.200><c> that</c>

00:03:24.309 --> 00:03:24.319 align:start position:0%
alongside like CPU and and GPU and that
 

00:03:24.319 --> 00:03:26.750 align:start position:0%
alongside like CPU and and GPU and that
can<00:03:24.440><c> be</c><00:03:24.599><c> focused</c><00:03:24.920><c> on</c><00:03:25.080><c> specific</c><00:03:25.480><c> AI</c><00:03:25.840><c> tasks</c><00:03:26.519><c> and</c>

00:03:26.750 --> 00:03:26.760 align:start position:0%
can be focused on specific AI tasks and
 

00:03:26.760 --> 00:03:28.509 align:start position:0%
can be focused on specific AI tasks and
then<00:03:27.000><c> on</c><00:03:27.120><c> the</c><00:03:27.239><c> software</c><00:03:27.680><c> side</c><00:03:27.920><c> Microsoft</c><00:03:28.400><c> has</c>

00:03:28.509 --> 00:03:28.519 align:start position:0%
then on the software side Microsoft has
 

00:03:28.519 --> 00:03:30.149 align:start position:0%
then on the software side Microsoft has
been<00:03:28.680><c> working</c><00:03:29.080><c> on</c><00:03:29.280><c> a</c><00:03:29.400><c> new</c><00:03:29.560><c> thing</c><00:03:29.879><c> called</c><00:03:30.080><c> the</c>

00:03:30.149 --> 00:03:30.159 align:start position:0%
been working on a new thing called the
 

00:03:30.159 --> 00:03:31.949 align:start position:0%
been working on a new thing called the
windows<00:03:30.480><c> co-pilot</c><00:03:30.920><c> runtime</c><00:03:31.560><c> that</c><00:03:31.640><c> will</c><00:03:31.760><c> help</c>

00:03:31.949 --> 00:03:31.959 align:start position:0%
windows co-pilot runtime that will help
 

00:03:31.959 --> 00:03:33.309 align:start position:0%
windows co-pilot runtime that will help
developers<00:03:32.400><c> take</c><00:03:32.560><c> advantage</c><00:03:32.879><c> of</c><00:03:33.000><c> these</c><00:03:33.120><c> new</c>

00:03:33.309 --> 00:03:33.319 align:start position:0%
developers take advantage of these new
 

00:03:33.319 --> 00:03:35.030 align:start position:0%
developers take advantage of these new
capabilities<00:03:34.319><c> so</c><00:03:34.480><c> this</c><00:03:34.560><c> is</c><00:03:34.680><c> going</c><00:03:34.760><c> to</c><00:03:34.840><c> ship</c>

00:03:35.030 --> 00:03:35.040 align:start position:0%
capabilities so this is going to ship
 

00:03:35.040 --> 00:03:36.710 align:start position:0%
capabilities so this is going to ship
with<00:03:35.120><c> a</c><00:03:35.200><c> bunch</c><00:03:35.360><c> of</c><00:03:35.439><c> local</c><00:03:35.680><c> models</c><00:03:36.159><c> including</c><00:03:36.599><c> a</c>

00:03:36.710 --> 00:03:36.720 align:start position:0%
with a bunch of local models including a
 

00:03:36.720 --> 00:03:39.550 align:start position:0%
with a bunch of local models including a
new<00:03:36.879><c> version</c><00:03:37.120><c> of</c><00:03:37.280><c> 53</c><00:03:37.879><c> called</c><00:03:38.200><c> vilica</c><00:03:39.200><c> and</c><00:03:39.400><c> and</c>

00:03:39.550 --> 00:03:39.560 align:start position:0%
new version of 53 called vilica and and
 

00:03:39.560 --> 00:03:41.149 align:start position:0%
new version of 53 called vilica and and
this<00:03:39.680><c> is</c><00:03:39.879><c> actually</c><00:03:40.120><c> specifically</c><00:03:40.680><c> optimized</c>

00:03:41.149 --> 00:03:41.159 align:start position:0%
this is actually specifically optimized
 

00:03:41.159 --> 00:03:43.589 align:start position:0%
this is actually specifically optimized
for<00:03:41.319><c> on</c><00:03:41.599><c> device</c><00:03:41.959><c> AI</c><00:03:42.319><c> stuff</c><00:03:42.959><c> but</c><00:03:43.120><c> beyond</c><00:03:43.400><c> all</c>

00:03:43.589 --> 00:03:43.599 align:start position:0%
for on device AI stuff but beyond all
 

00:03:43.599 --> 00:03:45.990 align:start position:0%
for on device AI stuff but beyond all
that<00:03:43.760><c> the</c><00:03:43.879><c> windows</c><00:03:44.200><c> co-pilot</c><00:03:44.680><c> runtime</c><00:03:45.560><c> and</c><00:03:45.879><c> to</c>

00:03:45.990 --> 00:03:46.000 align:start position:0%
that the windows co-pilot runtime and to
 

00:03:46.000 --> 00:03:47.110 align:start position:0%
that the windows co-pilot runtime and to
be<00:03:46.120><c> clear</c><00:03:46.360><c> this</c><00:03:46.439><c> is</c><00:03:46.599><c> actually</c><00:03:46.760><c> going</c><00:03:46.840><c> to</c><00:03:46.959><c> run</c>

00:03:47.110 --> 00:03:47.120 align:start position:0%
be clear this is actually going to run
 

00:03:47.120 --> 00:03:49.070 align:start position:0%
be clear this is actually going to run
on<00:03:47.239><c> a</c><00:03:47.360><c> range</c><00:03:47.599><c> of</c><00:03:47.720><c> different</c><00:03:48.000><c> devices</c><00:03:48.879><c> has</c>

00:03:49.070 --> 00:03:49.080 align:start position:0%
on a range of different devices has
 

00:03:49.080 --> 00:03:50.710 align:start position:0%
on a range of different devices has
really<00:03:49.280><c> been</c><00:03:49.480><c> optimized</c><00:03:49.959><c> to</c><00:03:50.080><c> make</c><00:03:50.360><c> working</c>

00:03:50.710 --> 00:03:50.720 align:start position:0%
really been optimized to make working
 

00:03:50.720 --> 00:03:52.830 align:start position:0%
really been optimized to make working
with<00:03:50.920><c> local</c><00:03:51.159><c> models</c><00:03:51.760><c> really</c><00:03:52.000><c> really</c><00:03:52.239><c> easy</c><00:03:52.720><c> and</c>

00:03:52.830 --> 00:03:52.840 align:start position:0%
with local models really really easy and
 

00:03:52.840 --> 00:03:55.270 align:start position:0%
with local models really really easy and
and<00:03:53.040><c> right</c><00:03:53.200><c> now</c><00:03:53.560><c> that</c><00:03:53.799><c> isn't</c><00:03:54.040><c> the</c><00:03:54.200><c> case</c><00:03:54.879><c> so</c>

00:03:55.270 --> 00:03:55.280 align:start position:0%
and right now that isn't the case so
 

00:03:55.280 --> 00:03:57.309 align:start position:0%
and right now that isn't the case so
part<00:03:55.480><c> of</c><00:03:55.680><c> this</c><00:03:55.920><c> work</c><00:03:56.439><c> is</c><00:03:56.799><c> the</c><00:03:56.920><c> news</c><00:03:57.159><c> that</c>

00:03:57.309 --> 00:03:57.319 align:start position:0%
part of this work is the news that
 

00:03:57.319 --> 00:03:59.949 align:start position:0%
part of this work is the news that
native<00:03:57.640><c> Pi</c><00:03:57.840><c> torch</c><00:03:58.159><c> support</c><00:03:58.560><c> is</c><00:03:58.760><c> now</c><00:03:59.000><c> in</c><00:03:59.439><c> window</c>

00:03:59.949 --> 00:03:59.959 align:start position:0%
native Pi torch support is now in window
 

00:03:59.959 --> 00:04:02.589 align:start position:0%
native Pi torch support is now in window
direct<00:04:00.200><c> ml</c><00:04:01.000><c> meaning</c><00:04:01.840><c> that</c><00:04:02.159><c> you</c><00:04:02.280><c> can</c><00:04:02.439><c> take</c>

00:04:02.589 --> 00:04:02.599 align:start position:0%
direct ml meaning that you can take
 

00:04:02.599 --> 00:04:04.550 align:start position:0%
direct ml meaning that you can take
models<00:04:03.040><c> from</c><00:04:03.400><c> places</c><00:04:03.720><c> like</c><00:04:03.840><c> hugging</c><00:04:04.159><c> face</c>

00:04:04.550 --> 00:04:04.560 align:start position:0%
models from places like hugging face
 

00:04:04.560 --> 00:04:06.710 align:start position:0%
models from places like hugging face
with<00:04:04.720><c> one</c><00:04:04.959><c> click</c><00:04:05.640><c> no</c><00:04:05.840><c> need</c><00:04:06.159><c> to</c><00:04:06.319><c> convert</c><00:04:06.640><c> the</c>

00:04:06.710 --> 00:04:06.720 align:start position:0%
with one click no need to convert the
 

00:04:06.720 --> 00:04:08.390 align:start position:0%
with one click no need to convert the
model<00:04:07.000><c> or</c><00:04:07.239><c> make</c><00:04:07.400><c> modifications</c><00:04:07.920><c> to</c><00:04:08.079><c> get</c><00:04:08.159><c> it</c><00:04:08.239><c> to</c>

00:04:08.390 --> 00:04:08.400 align:start position:0%
model or make modifications to get it to
 

00:04:08.400 --> 00:04:10.630 align:start position:0%
model or make modifications to get it to
run<00:04:09.120><c> I</c><00:04:09.200><c> had</c><00:04:09.280><c> a</c><00:04:09.400><c> really</c><00:04:09.560><c> great</c><00:04:09.799><c> talk</c><00:04:10.079><c> with</c><00:04:10.239><c> Divia</c>

00:04:10.630 --> 00:04:10.640 align:start position:0%
run I had a really great talk with Divia
 

00:04:10.640 --> 00:04:12.589 align:start position:0%
run I had a really great talk with Divia
and<00:04:10.799><c> vente</c><00:04:11.280><c> at</c><00:04:11.480><c> Microsoft</c><00:04:11.879><c> build</c><00:04:12.239><c> about</c><00:04:12.439><c> this</c>

00:04:12.589 --> 00:04:12.599 align:start position:0%
and vente at Microsoft build about this
 

00:04:12.599 --> 00:04:14.069 align:start position:0%
and vente at Microsoft build about this
stuff<00:04:12.840><c> and</c><00:04:12.959><c> I'm</c><00:04:13.120><c> super</c><00:04:13.360><c> excited</c><00:04:13.720><c> about</c><00:04:13.920><c> the</c>

00:04:14.069 --> 00:04:14.079 align:start position:0%
stuff and I'm super excited about the
 

00:04:14.079 --> 00:04:17.030 align:start position:0%
stuff and I'm super excited about the
possibilities<00:04:14.519><c> for</c><00:04:14.760><c> Debs</c><00:04:15.760><c> oh</c><00:04:16.400><c> and</c><00:04:16.600><c> if</c><00:04:16.720><c> you</c><00:04:16.799><c> are</c>

00:04:17.030 --> 00:04:17.040 align:start position:0%
possibilities for Debs oh and if you are
 

00:04:17.040 --> 00:04:19.189 align:start position:0%
possibilities for Debs oh and if you are
interested<00:04:17.519><c> in</c><00:04:17.880><c> that</c><00:04:18.040><c> new</c><00:04:18.280><c> AI</c><00:04:18.600><c> hardware</c><00:04:19.000><c> for</c>

00:04:19.189 --> 00:04:19.199 align:start position:0%
interested in that new AI hardware for
 

00:04:19.199 --> 00:04:21.430 align:start position:0%
interested in that new AI hardware for
Windows<00:04:20.000><c> qu</c><00:04:20.359><c> in</c><00:04:20.479><c> addition</c><00:04:20.720><c> to</c><00:04:20.840><c> the</c><00:04:20.959><c> laptops</c>

00:04:21.430 --> 00:04:21.440 align:start position:0%
Windows qu in addition to the laptops
 

00:04:21.440 --> 00:04:24.189 align:start position:0%
Windows qu in addition to the laptops
Qualcomm<00:04:22.040><c> announced</c><00:04:22.560><c> a</c><00:04:22.680><c> Snapdragon</c><00:04:23.280><c> dev</c><00:04:23.639><c> kit</c>

00:04:24.189 --> 00:04:24.199 align:start position:0%
Qualcomm announced a Snapdragon dev kit
 

00:04:24.199 --> 00:04:26.950 align:start position:0%
Qualcomm announced a Snapdragon dev kit
that's<00:04:24.320><c> $8.99</c><00:04:25.240><c> and</c><00:04:25.400><c> goes</c><00:04:25.560><c> on</c><00:04:25.759><c> sale</c><00:04:26.080><c> in</c><00:04:26.320><c> June</c>

00:04:26.950 --> 00:04:26.960 align:start position:0%
that's $8.99 and goes on sale in June
 

00:04:26.960 --> 00:04:28.189 align:start position:0%
that's $8.99 and goes on sale in June
and<00:04:27.080><c> it</c><00:04:27.160><c> looks</c><00:04:27.360><c> like</c><00:04:27.479><c> it's</c><00:04:27.560><c> a</c><00:04:27.680><c> great</c><00:04:27.880><c> way</c><00:04:28.000><c> to</c>

00:04:28.189 --> 00:04:28.199 align:start position:0%
and it looks like it's a great way to
 

00:04:28.199 --> 00:04:30.670 align:start position:0%
and it looks like it's a great way to
either<00:04:28.440><c> Port</c><00:04:28.759><c> your</c><00:04:29.080><c> you</c><00:04:29.160><c> know</c><00:04:29.320><c> existing</c><00:04:29.759><c> x86</c>

00:04:30.670 --> 00:04:30.680 align:start position:0%
either Port your you know existing x86
 

00:04:30.680 --> 00:04:32.550 align:start position:0%
either Port your you know existing x86
app<00:04:30.840><c> over</c><00:04:31.000><c> to</c><00:04:31.080><c> the</c><00:04:31.199><c> latest</c><00:04:31.520><c> processors</c><00:04:32.360><c> or</c><00:04:32.479><c> if</c>

00:04:32.550 --> 00:04:32.560 align:start position:0%
app over to the latest processors or if
 

00:04:32.560 --> 00:04:34.150 align:start position:0%
app over to the latest processors or if
you<00:04:32.639><c> want</c><00:04:32.759><c> to</c><00:04:32.880><c> do</c><00:04:33.039><c> more</c><00:04:33.240><c> local</c><00:04:33.520><c> AI</c><00:04:33.759><c> building</c>

00:04:34.150 --> 00:04:34.160 align:start position:0%
you want to do more local AI building
 

00:04:34.160 --> 00:04:35.550 align:start position:0%
you want to do more local AI building
and<00:04:34.240><c> so</c><00:04:34.400><c> I've</c><00:04:34.520><c> got</c><00:04:34.639><c> links</c><00:04:34.880><c> for</c><00:04:35.080><c> all</c><00:04:35.240><c> that</c><00:04:35.360><c> stuff</c>

00:04:35.550 --> 00:04:35.560 align:start position:0%
and so I've got links for all that stuff
 

00:04:35.560 --> 00:04:37.590 align:start position:0%
and so I've got links for all that stuff
in<00:04:35.639><c> the</c><00:04:35.720><c> show</c><00:04:35.919><c> notes</c><00:04:36.160><c> in</c><00:04:36.280><c> the</c><00:04:36.600><c> description</c>

00:04:37.590 --> 00:04:37.600 align:start position:0%
in the show notes in the description
 

00:04:37.600 --> 00:04:38.950 align:start position:0%
in the show notes in the description
okay<00:04:37.919><c> so</c><00:04:38.160><c> a</c><00:04:38.240><c> lot</c><00:04:38.320><c> of</c><00:04:38.440><c> the</c><00:04:38.520><c> stuff</c><00:04:38.680><c> that</c><00:04:38.800><c> we've</c>

00:04:38.950 --> 00:04:38.960 align:start position:0%
okay so a lot of the stuff that we've
 

00:04:38.960 --> 00:04:40.710 align:start position:0%
okay so a lot of the stuff that we've
talked<00:04:39.199><c> about</c><00:04:39.680><c> are</c><00:04:39.840><c> from</c><00:04:40.000><c> more</c><00:04:40.240><c> experienced</c>

00:04:40.710 --> 00:04:40.720 align:start position:0%
talked about are from more experienced
 

00:04:40.720 --> 00:04:42.590 align:start position:0%
talked about are from more experienced
developers<00:04:41.680><c> but</c><00:04:41.840><c> if</c><00:04:41.919><c> you're</c><00:04:42.240><c> new</c><00:04:42.440><c> to</c>

00:04:42.590 --> 00:04:42.600 align:start position:0%
developers but if you're new to
 

00:04:42.600 --> 00:04:44.350 align:start position:0%
developers but if you're new to
development<00:04:43.039><c> or</c><00:04:43.199><c> to</c><00:04:43.360><c> GitHub</c><00:04:43.919><c> we've</c><00:04:44.080><c> got</c><00:04:44.240><c> a</c>

00:04:44.350 --> 00:04:44.360 align:start position:0%
development or to GitHub we've got a
 

00:04:44.360 --> 00:04:46.710 align:start position:0%
development or to GitHub we've got a
brand<00:04:44.560><c> new</c><00:04:44.759><c> series</c><00:04:45.080><c> on</c><00:04:45.280><c> GitHub</c><00:04:45.680><c> for</c><00:04:45.800><c> you</c><00:04:46.400><c> home</c>

00:04:46.710 --> 00:04:46.720 align:start position:0%
brand new series on GitHub for you home
 

00:04:46.720 --> 00:04:48.270 align:start position:0%
brand new series on GitHub for you home
by<00:04:46.840><c> my</c><00:04:46.919><c> friend</c><00:04:47.199><c> kadaa</c><00:04:47.600><c> care</c><00:04:47.880><c> GitHub</c><00:04:48.199><c> for</c>

00:04:48.270 --> 00:04:48.280 align:start position:0%
by my friend kadaa care GitHub for
 

00:04:48.280 --> 00:04:49.749 align:start position:0%
by my friend kadaa care GitHub for
beginners<00:04:48.680><c> is</c><00:04:48.800><c> our</c><00:04:48.960><c> new</c><00:04:49.160><c> series</c><00:04:49.520><c> that</c><00:04:49.600><c> will</c>

00:04:49.749 --> 00:04:49.759 align:start position:0%
beginners is our new series that will
 

00:04:49.759 --> 00:04:51.590 align:start position:0%
beginners is our new series that will
help<00:04:49.919><c> you</c><00:04:50.080><c> get</c><00:04:50.199><c> started</c><00:04:50.479><c> with</c><00:04:50.639><c> GitHub</c><00:04:51.360><c> whether</c>

00:04:51.590 --> 00:04:51.600 align:start position:0%
help you get started with GitHub whether
 

00:04:51.600 --> 00:04:52.990 align:start position:0%
help you get started with GitHub whether
you're<00:04:51.800><c> a</c><00:04:51.919><c> student</c><00:04:52.199><c> a</c><00:04:52.320><c> hobbyist</c><00:04:52.720><c> or</c>

00:04:52.990 --> 00:04:53.000 align:start position:0%
you're a student a hobbyist or
 

00:04:53.000 --> 00:04:54.830 align:start position:0%
you're a student a hobbyist or
professional<00:04:53.880><c> and</c><00:04:54.039><c> I've</c><00:04:54.160><c> got</c><00:04:54.240><c> a</c><00:04:54.360><c> link</c><00:04:54.560><c> to</c><00:04:54.680><c> a</c>

00:04:54.830 --> 00:04:54.840 align:start position:0%
professional and I've got a link to a
 

00:04:54.840 --> 00:04:56.550 align:start position:0%
professional and I've got a link to a
blog<00:04:55.120><c> post</c><00:04:55.360><c> that</c><00:04:55.520><c> cadesa</c><00:04:55.919><c> wrote</c><00:04:56.280><c> about</c><00:04:56.479><c> the</c>

00:04:56.550 --> 00:04:56.560 align:start position:0%
blog post that cadesa wrote about the
 

00:04:56.560 --> 00:04:58.870 align:start position:0%
blog post that cadesa wrote about the
new<00:04:56.840><c> series</c><00:04:57.680><c> as</c><00:04:57.800><c> well</c><00:04:57.919><c> as</c><00:04:58.039><c> a</c><00:04:58.160><c> link</c><00:04:58.520><c> uh</c><00:04:58.600><c> to</c><00:04:58.720><c> the</c>

00:04:58.870 --> 00:04:58.880 align:start position:0%
new series as well as a link uh to the
 

00:04:58.880 --> 00:05:00.710 align:start position:0%
new series as well as a link uh to the
first<00:04:59.240><c> uh</c><00:04:59.360><c> episod</c><00:04:59.680><c> episode</c><00:05:00.240><c> on</c><00:05:00.360><c> this</c><00:05:00.520><c> very</c>

00:05:00.710 --> 00:05:00.720 align:start position:0%
first uh episod episode on this very
 

00:05:00.720 --> 00:05:02.830 align:start position:0%
first uh episod episode on this very
YouTube<00:05:01.000><c> channel</c><00:05:01.759><c> and</c><00:05:02.080><c> stay</c><00:05:02.280><c> tuned</c><00:05:02.520><c> to</c><00:05:02.680><c> the</c>

00:05:02.830 --> 00:05:02.840 align:start position:0%
YouTube channel and stay tuned to the
 

00:05:02.840 --> 00:05:05.070 align:start position:0%
YouTube channel and stay tuned to the
series<00:05:03.160><c> CU</c><00:05:03.320><c> you</c><00:05:03.639><c> might</c><00:05:03.840><c> see</c><00:05:04.160><c> me</c><00:05:04.479><c> pop</c><00:05:04.680><c> up</c><00:05:04.800><c> in</c><00:05:04.919><c> an</c>

00:05:05.070 --> 00:05:05.080 align:start position:0%
series CU you might see me pop up in an
 

00:05:05.080 --> 00:05:07.189 align:start position:0%
series CU you might see me pop up in an
episode<00:05:05.360><c> or</c><00:05:05.479><c> two</c><00:05:06.400><c> and</c><00:05:06.520><c> now</c><00:05:06.639><c> it's</c><00:05:06.800><c> time</c><00:05:06.919><c> for</c><00:05:07.080><c> my</c>

00:05:07.189 --> 00:05:07.199 align:start position:0%
episode or two and now it's time for my
 

00:05:07.199 --> 00:05:09.070 align:start position:0%
episode or two and now it's time for my
GitHub<00:05:07.560><c> project</c><00:05:07.960><c> Spotlight</c><00:05:08.720><c> and</c><00:05:08.880><c> this</c><00:05:08.960><c> is</c>

00:05:09.070 --> 00:05:09.080 align:start position:0%
GitHub project Spotlight and this is
 

00:05:09.080 --> 00:05:10.550 align:start position:0%
GitHub project Spotlight and this is
where<00:05:09.240><c> I</c><00:05:09.400><c> highlight</c><00:05:09.720><c> cool</c><00:05:09.960><c> open</c><00:05:10.199><c> source</c>

00:05:10.550 --> 00:05:10.560 align:start position:0%
where I highlight cool open source
 

00:05:10.560 --> 00:05:11.909 align:start position:0%
where I highlight cool open source
projects<00:05:11.199><c> uh</c><00:05:11.320><c> that</c><00:05:11.400><c> I</c><00:05:11.520><c> think</c><00:05:11.639><c> you</c><00:05:11.720><c> should</c>

00:05:11.909 --> 00:05:11.919 align:start position:0%
projects uh that I think you should
 

00:05:11.919 --> 00:05:13.430 align:start position:0%
projects uh that I think you should
check<00:05:12.120><c> out</c><00:05:12.600><c> and</c><00:05:12.759><c> this</c><00:05:12.880><c> week</c><00:05:13.080><c> I</c><00:05:13.160><c> want</c><00:05:13.240><c> to</c>

00:05:13.430 --> 00:05:13.440 align:start position:0%
check out and this week I want to
 

00:05:13.440 --> 00:05:16.150 align:start position:0%
check out and this week I want to
highlight<00:05:13.919><c> Athena</c><00:05:14.400><c> crisis</c><00:05:15.120><c> so</c><00:05:15.320><c> Athena</c><00:05:15.759><c> crisis</c>

00:05:16.150 --> 00:05:16.160 align:start position:0%
highlight Athena crisis so Athena crisis
 

00:05:16.160 --> 00:05:18.590 align:start position:0%
highlight Athena crisis so Athena crisis
is<00:05:16.280><c> a</c><00:05:16.479><c> modern</c><00:05:16.880><c> retro</c><00:05:17.360><c> turn-based</c><00:05:17.919><c> strategy</c>

00:05:18.590 --> 00:05:18.600 align:start position:0%
is a modern retro turn-based strategy
 

00:05:18.600 --> 00:05:21.350 align:start position:0%
is a modern retro turn-based strategy
game<00:05:19.039><c> and</c><00:05:19.160><c> it's</c><00:05:19.360><c> from</c><00:05:19.680><c> Christopher</c><00:05:20.360><c> nakazawa</c>

00:05:21.350 --> 00:05:21.360 align:start position:0%
game and it's from Christopher nakazawa
 

00:05:21.360 --> 00:05:23.110 align:start position:0%
game and it's from Christopher nakazawa
and<00:05:21.600><c> it's</c><00:05:21.759><c> published</c><00:05:22.120><c> by</c><00:05:22.240><c> null</c><00:05:22.720><c> and</c><00:05:22.840><c> null</c>

00:05:23.110 --> 00:05:23.120 align:start position:0%
and it's published by null and null
 

00:05:23.120 --> 00:05:24.909 align:start position:0%
and it's published by null and null
games<00:05:23.400><c> is</c><00:05:23.560><c> actually</c><00:05:23.840><c> helmed</c><00:05:24.199><c> by</c><00:05:24.600><c> GitHub</c>

00:05:24.909 --> 00:05:24.919 align:start position:0%
games is actually helmed by GitHub
 

00:05:24.919 --> 00:05:27.469 align:start position:0%
games is actually helmed by GitHub
co-founder<00:05:25.360><c> Chris</c><00:05:25.600><c> rreth</c><00:05:26.600><c> and</c><00:05:26.960><c> Christopher</c>

00:05:27.469 --> 00:05:27.479 align:start position:0%
co-founder Chris rreth and Christopher
 

00:05:27.479 --> 00:05:30.469 align:start position:0%
co-founder Chris rreth and Christopher
has<00:05:27.639><c> decided</c><00:05:27.960><c> to</c><00:05:28.160><c> open</c><00:05:28.520><c> source</c><00:05:29.120><c> Athena</c><00:05:29.720><c> crisis</c>

00:05:30.469 --> 00:05:30.479 align:start position:0%
has decided to open source Athena crisis
 

00:05:30.479 --> 00:05:32.469 align:start position:0%
has decided to open source Athena crisis
and<00:05:30.680><c> and</c><00:05:30.840><c> he</c><00:05:31.000><c> says</c><00:05:31.240><c> that</c><00:05:31.360><c> Athena</c><00:05:31.759><c> crisis</c><00:05:32.160><c> is</c><00:05:32.280><c> an</c>

00:05:32.469 --> 00:05:32.479 align:start position:0%
and and he says that Athena crisis is an
 

00:05:32.479 --> 00:05:34.350 align:start position:0%
and and he says that Athena crisis is an
example<00:05:32.840><c> of</c><00:05:32.960><c> how</c><00:05:33.080><c> to</c><00:05:33.240><c> build</c><00:05:33.440><c> a</c><00:05:33.600><c> highquality</c>

00:05:34.350 --> 00:05:34.360 align:start position:0%
example of how to build a highquality
 

00:05:34.360 --> 00:05:36.790 align:start position:0%
example of how to build a highquality
video<00:05:34.680><c> game</c><00:05:35.199><c> using</c><00:05:35.520><c> only</c><00:05:35.800><c> JavaScript</c><00:05:36.400><c> react</c>

00:05:36.790 --> 00:05:36.800 align:start position:0%
video game using only JavaScript react
 

00:05:36.800 --> 00:05:39.670 align:start position:0%
video game using only JavaScript react
and<00:05:37.080><c> CSS</c><00:05:38.080><c> and</c><00:05:38.319><c> fans</c><00:05:38.560><c> of</c><00:05:38.720><c> the</c><00:05:38.840><c> game</c><00:05:39.199><c> can</c><00:05:39.400><c> offer</c>

00:05:39.670 --> 00:05:39.680 align:start position:0%
and CSS and fans of the game can offer
 

00:05:39.680 --> 00:05:41.390 align:start position:0%
and CSS and fans of the game can offer
their<00:05:39.919><c> improvements</c><00:05:40.639><c> um</c><00:05:40.800><c> they</c><00:05:40.880><c> can</c><00:05:41.000><c> build</c>

00:05:41.390 --> 00:05:41.400 align:start position:0%
their improvements um they can build
 

00:05:41.400 --> 00:05:42.870 align:start position:0%
their improvements um they can build
additional<00:05:41.759><c> tools</c><00:05:42.039><c> for</c><00:05:42.199><c> the</c><00:05:42.319><c> games</c><00:05:42.680><c> they</c><00:05:42.759><c> can</c>

00:05:42.870 --> 00:05:42.880 align:start position:0%
additional tools for the games they can
 

00:05:42.880 --> 00:05:44.629 align:start position:0%
additional tools for the games they can
study<00:05:43.160><c> the</c><00:05:43.319><c> code</c><00:05:43.960><c> they</c><00:05:44.080><c> can</c><00:05:44.199><c> make</c><00:05:44.319><c> their</c><00:05:44.479><c> own</c>

00:05:44.629 --> 00:05:44.639 align:start position:0%
study the code they can make their own
 

00:05:44.639 --> 00:05:46.550 align:start position:0%
study the code they can make their own
JavaScript<00:05:45.199><c> based</c><00:05:45.520><c> web</c><00:05:45.960><c> uh</c><00:05:46.080><c> games</c><00:05:46.360><c> if</c><00:05:46.479><c> they</c>

00:05:46.550 --> 00:05:46.560 align:start position:0%
JavaScript based web uh games if they
 

00:05:46.560 --> 00:05:48.189 align:start position:0%
JavaScript based web uh games if they
want<00:05:46.680><c> to</c><00:05:46.800><c> do</c><00:05:47.039><c> that</c><00:05:47.440><c> and</c><00:05:47.560><c> to</c><00:05:47.759><c> Kickstart</c>

00:05:48.189 --> 00:05:48.199 align:start position:0%
want to do that and to Kickstart
 

00:05:48.199 --> 00:05:51.430 align:start position:0%
want to do that and to Kickstart
contributions<00:05:49.160><c> nakazawa</c><00:05:49.919><c> Tech</c><00:05:50.720><c> um</c><00:05:51.000><c> has</c>

00:05:51.430 --> 00:05:51.440 align:start position:0%
contributions nakazawa Tech um has
 

00:05:51.440 --> 00:05:53.189 align:start position:0%
contributions nakazawa Tech um has
funded<00:05:51.800><c> $5,000</c><00:05:52.520><c> worth</c><00:05:52.680><c> of</c><00:05:52.800><c> Open</c><00:05:52.960><c> Source</c>

00:05:53.189 --> 00:05:53.199 align:start position:0%
funded $5,000 worth of Open Source
 

00:05:53.199 --> 00:05:55.990 align:start position:0%
funded $5,000 worth of Open Source
contributions<00:05:53.960><c> and</c><00:05:54.039><c> then</c><00:05:54.280><c> polar</c><00:05:55.199><c> is</c><00:05:55.360><c> going</c><00:05:55.600><c> to</c>

00:05:55.990 --> 00:05:56.000 align:start position:0%
contributions and then polar is going to
 

00:05:56.000 --> 00:05:57.749 align:start position:0%
contributions and then polar is going to
match<00:05:56.360><c> that</c><00:05:56.520><c> amount</c><00:05:56.759><c> for</c><00:05:56.919><c> an</c><00:05:57.039><c> initial</c><00:05:57.400><c> total</c>

00:05:57.749 --> 00:05:57.759 align:start position:0%
match that amount for an initial total
 

00:05:57.759 --> 00:05:59.870 align:start position:0%
match that amount for an initial total
of<00:05:57.919><c> of</c><00:05:58.120><c> $10,000</c><00:05:59.120><c> for</c><00:05:59.280><c> people</c><00:05:59.440><c> who</c><00:05:59.720><c> make</c>

00:05:59.870 --> 00:05:59.880 align:start position:0%
of of $10,000 for people who make
 

00:05:59.880 --> 00:06:01.430 align:start position:0%
of of $10,000 for people who make
contributions<00:06:00.759><c> and</c><00:06:00.840><c> so</c><00:06:00.960><c> I've</c><00:06:01.080><c> got</c><00:06:01.160><c> a</c><00:06:01.240><c> link</c>

00:06:01.430 --> 00:06:01.440 align:start position:0%
contributions and so I've got a link
 

00:06:01.440 --> 00:06:03.990 align:start position:0%
contributions and so I've got a link
Down<00:06:01.600><c> Below</c><00:06:01.880><c> in</c><00:06:02.000><c> the</c><00:06:02.120><c> blog</c><00:06:02.639><c> uh</c><00:06:02.800><c> to</c><00:06:02.960><c> a</c><00:06:03.080><c> blog</c><00:06:03.360><c> post</c>

00:06:03.990 --> 00:06:04.000 align:start position:0%
Down Below in the blog uh to a blog post
 

00:06:04.000 --> 00:06:05.790 align:start position:0%
Down Below in the blog uh to a blog post
uh<00:06:04.120><c> about</c><00:06:04.280><c> Athena</c><00:06:04.720><c> crisis</c><00:06:05.319><c> and</c><00:06:05.440><c> it's</c><00:06:05.560><c> going</c><00:06:05.680><c> to</c>

00:06:05.790 --> 00:06:05.800 align:start position:0%
uh about Athena crisis and it's going to
 

00:06:05.800 --> 00:06:07.990 align:start position:0%
uh about Athena crisis and it's going to
outline<00:06:06.800><c> um</c><00:06:07.000><c> what</c><00:06:07.120><c> is</c><00:06:07.240><c> open</c><00:06:07.440><c> source</c><00:06:07.720><c> and</c><00:06:07.840><c> what</c>

00:06:07.990 --> 00:06:08.000 align:start position:0%
outline um what is open source and what
 

00:06:08.000 --> 00:06:10.270 align:start position:0%
outline um what is open source and what
isn't<00:06:08.319><c> and</c><00:06:08.479><c> how</c><00:06:08.639><c> you</c><00:06:08.720><c> can</c><00:06:08.919><c> contribute</c><00:06:09.840><c> but</c><00:06:10.080><c> I</c>

00:06:10.270 --> 00:06:10.280 align:start position:0%
isn't and how you can contribute but I
 

00:06:10.280 --> 00:06:12.309 align:start position:0%
isn't and how you can contribute but I
really<00:06:10.560><c> really</c><00:06:10.759><c> love</c><00:06:11.000><c> seeing</c><00:06:11.520><c> games</c><00:06:11.840><c> go</c><00:06:12.039><c> open</c>

00:06:12.309 --> 00:06:12.319 align:start position:0%
really really love seeing games go open
 

00:06:12.319 --> 00:06:14.309 align:start position:0%
really really love seeing games go open
source<00:06:12.680><c> and</c><00:06:12.800><c> in</c><00:06:12.960><c> this</c><00:06:13.199><c> case</c><00:06:13.560><c> I</c><00:06:13.800><c> also</c><00:06:14.080><c> really</c>

00:06:14.309 --> 00:06:14.319 align:start position:0%
source and in this case I also really
 

00:06:14.319 --> 00:06:16.189 align:start position:0%
source and in this case I also really
appreciate<00:06:15.039><c> they're</c><00:06:15.240><c> offering</c><00:06:15.960><c> you</c><00:06:16.039><c> know</c>

00:06:16.189 --> 00:06:16.199 align:start position:0%
appreciate they're offering you know
 

00:06:16.199 --> 00:06:18.790 align:start position:0%
appreciate they're offering you know
funding<00:06:16.919><c> incentives</c><00:06:17.680><c> and</c><00:06:17.880><c> so</c><00:06:18.240><c> I've</c><00:06:18.360><c> got</c><00:06:18.520><c> links</c>

00:06:18.790 --> 00:06:18.800 align:start position:0%
funding incentives and so I've got links
 

00:06:18.800 --> 00:06:20.870 align:start position:0%
funding incentives and so I've got links
to<00:06:19.000><c> all</c><00:06:19.199><c> the</c><00:06:19.440><c> stuff</c><00:06:20.080><c> including</c><00:06:20.440><c> the</c><00:06:20.560><c> Athena</c>

00:06:20.870 --> 00:06:20.880 align:start position:0%
to all the stuff including the Athena
 

00:06:20.880 --> 00:06:22.990 align:start position:0%
to all the stuff including the Athena
crisis<00:06:21.240><c> GitHub</c><00:06:21.639><c> repo</c><00:06:22.199><c> in</c><00:06:22.440><c> the</c><00:06:22.599><c> description</c>

00:06:22.990 --> 00:06:23.000 align:start position:0%
crisis GitHub repo in the description
 

00:06:23.000 --> 00:06:25.790 align:start position:0%
crisis GitHub repo in the description
down<00:06:23.599><c> below</c><00:06:24.599><c> and</c><00:06:24.759><c> now</c><00:06:24.919><c> it's</c><00:06:25.120><c> time</c><00:06:25.280><c> for</c><00:06:25.479><c> my</c><00:06:25.639><c> pick</c>

00:06:25.790 --> 00:06:25.800 align:start position:0%
down below and now it's time for my pick
 

00:06:25.800 --> 00:06:28.870 align:start position:0%
down below and now it's time for my pick
of<00:06:25.919><c> the</c><00:06:26.360><c> week</c><00:06:27.360><c> okay</c><00:06:27.639><c> so</c><00:06:27.880><c> Taylor</c><00:06:28.199><c> Swift</c><00:06:28.560><c> is</c><00:06:28.720><c> I</c>

00:06:28.870 --> 00:06:28.880 align:start position:0%
of the week okay so Taylor Swift is I
 

00:06:28.880 --> 00:06:30.990 align:start position:0%
of the week okay so Taylor Swift is I
think<00:06:29.160><c> in</c><00:06:29.319><c> her</c><00:06:29.639><c> her</c><00:06:29.840><c> fourth</c><00:06:30.520><c> it's</c><00:06:30.720><c> either</c><00:06:30.880><c> her</c>

00:06:30.990 --> 00:06:31.000 align:start position:0%
think in her her fourth it's either her
 

00:06:31.000 --> 00:06:33.749 align:start position:0%
think in her her fourth it's either her
fourth<00:06:31.280><c> or</c><00:06:31.400><c> her</c><00:06:31.560><c> fifth</c><00:06:31.840><c> leg</c><00:06:32.400><c> of</c><00:06:32.560><c> her</c><00:06:32.680><c> arys</c><00:06:33.080><c> tour</c>

00:06:33.749 --> 00:06:33.759 align:start position:0%
fourth or her fifth leg of her arys tour
 

00:06:33.759 --> 00:06:35.990 align:start position:0%
fourth or her fifth leg of her arys tour
and<00:06:34.160><c> part</c><00:06:34.319><c> of</c><00:06:34.440><c> the</c><00:06:34.560><c> tour</c><00:06:35.120><c> is</c><00:06:35.319><c> that</c><00:06:35.479><c> she</c><00:06:35.720><c> has</c>

00:06:35.990 --> 00:06:36.000 align:start position:0%
and part of the tour is that she has
 

00:06:36.000 --> 00:06:38.230 align:start position:0%
and part of the tour is that she has
these<00:06:36.240><c> led</c><00:06:36.759><c> wristbands</c><00:06:37.560><c> that</c><00:06:37.680><c> change</c><00:06:37.960><c> color</c>

00:06:38.230 --> 00:06:38.240 align:start position:0%
these led wristbands that change color
 

00:06:38.240 --> 00:06:40.189 align:start position:0%
these led wristbands that change color
depending<00:06:38.720><c> on</c><00:06:39.360><c> what</c><00:06:39.479><c> part</c><00:06:39.599><c> of</c><00:06:39.720><c> the</c><00:06:39.800><c> show</c><00:06:40.000><c> is</c>

00:06:40.189 --> 00:06:40.199 align:start position:0%
depending on what part of the show is
 

00:06:40.199 --> 00:06:41.550 align:start position:0%
depending on what part of the show is
happening<00:06:40.639><c> and</c><00:06:40.840><c> and</c><00:06:40.919><c> I</c><00:06:41.039><c> guess</c><00:06:41.199><c> like</c><00:06:41.319><c> maybe</c>

00:06:41.550 --> 00:06:41.560 align:start position:0%
happening and and I guess like maybe
 

00:06:41.560 --> 00:06:43.309 align:start position:0%
happening and and I guess like maybe
even<00:06:41.720><c> where</c><00:06:41.880><c> you</c><00:06:41.960><c> are</c><00:06:42.120><c> in</c><00:06:42.199><c> the</c><00:06:42.319><c> stadium</c><00:06:43.080><c> she's</c>

00:06:43.309 --> 00:06:43.319 align:start position:0%
even where you are in the stadium she's
 

00:06:43.319 --> 00:06:44.749 align:start position:0%
even where you are in the stadium she's
been<00:06:43.440><c> doing</c><00:06:43.680><c> this</c><00:06:43.800><c> sort</c><00:06:43.960><c> of</c><00:06:44.080><c> thing</c><00:06:44.280><c> for</c><00:06:44.479><c> years</c>

00:06:44.749 --> 00:06:44.759 align:start position:0%
been doing this sort of thing for years
 

00:06:44.759 --> 00:06:46.110 align:start position:0%
been doing this sort of thing for years
now<00:06:45.000><c> and</c><00:06:45.080><c> the</c><00:06:45.160><c> bands</c><00:06:45.400><c> are</c><00:06:45.599><c> actually</c><00:06:45.880><c> pretty</c>

00:06:46.110 --> 00:06:46.120 align:start position:0%
now and the bands are actually pretty
 

00:06:46.120 --> 00:06:49.589 align:start position:0%
now and the bands are actually pretty
cool<00:06:47.120><c> but</c><00:06:47.319><c> what</c><00:06:47.440><c> if</c><00:06:47.560><c> you</c><00:06:47.680><c> could</c><00:06:48.080><c> hack</c><00:06:49.080><c> those</c>

00:06:49.589 --> 00:06:49.599 align:start position:0%
cool but what if you could hack those
 

00:06:49.599 --> 00:06:51.909 align:start position:0%
cool but what if you could hack those
wristbands<00:06:50.599><c> well</c><00:06:51.160><c> thanks</c><00:06:51.360><c> to</c><00:06:51.479><c> the</c><00:06:51.560><c> flipper</c>

00:06:51.909 --> 00:06:51.919 align:start position:0%
wristbands well thanks to the flipper
 

00:06:51.919 --> 00:06:53.909 align:start position:0%
wristbands well thanks to the flipper
zero<00:06:52.319><c> a</c><00:06:52.440><c> multi-tool</c><00:06:53.080><c> for</c><00:06:53.280><c> hackers</c><00:06:53.680><c> that</c><00:06:53.759><c> we've</c>

00:06:53.909 --> 00:06:53.919 align:start position:0%
zero a multi-tool for hackers that we've
 

00:06:53.919 --> 00:06:56.909 align:start position:0%
zero a multi-tool for hackers that we've
talked<00:06:54.120><c> about</c><00:06:54.360><c> before</c><00:06:55.120><c> you</c><00:06:55.319><c> can</c><00:06:56.039><c> so</c><00:06:56.319><c> John</c><00:06:56.599><c> gr</c>

00:06:56.909 --> 00:06:56.919 align:start position:0%
talked about before you can so John gr
 

00:06:56.919 --> 00:06:58.990 align:start position:0%
talked about before you can so John gr
cuming<00:06:57.400><c> highlighted</c><00:06:57.840><c> how</c><00:06:57.960><c> to</c><00:06:58.080><c> do</c><00:06:58.319><c> this</c><00:06:58.599><c> on</c><00:06:58.800><c> his</c>

00:06:58.990 --> 00:06:59.000 align:start position:0%
cuming highlighted how to do this on his
 

00:06:59.000 --> 00:07:01.670 align:start position:0%
cuming highlighted how to do this on his
blog<00:06:59.720><c> and</c><00:06:59.840><c> it</c><00:06:59.960><c> turns</c><00:07:00.319><c> out</c><00:07:00.800><c> um</c><00:07:01.000><c> that</c><00:07:01.240><c> really</c>

00:07:01.670 --> 00:07:01.680 align:start position:0%
blog and it turns out um that really
 

00:07:01.680 --> 00:07:02.909 align:start position:0%
blog and it turns out um that really
what's<00:07:01.879><c> happening</c><00:07:02.160><c> behind</c><00:07:02.400><c> the</c><00:07:02.479><c> scenes</c><00:07:02.800><c> is</c>

00:07:02.909 --> 00:07:02.919 align:start position:0%
what's happening behind the scenes is
 

00:07:02.919 --> 00:07:04.830 align:start position:0%
what's happening behind the scenes is
that<00:07:03.039><c> there's</c><00:07:03.199><c> like</c><00:07:03.319><c> a</c><00:07:03.520><c> really</c><00:07:03.800><c> large</c><00:07:04.479><c> IR</c>

00:07:04.830 --> 00:07:04.840 align:start position:0%
that there's like a really large IR
 

00:07:04.840 --> 00:07:06.510 align:start position:0%
that there's like a really large IR
remote<00:07:05.120><c> control</c><00:07:05.639><c> that's</c><00:07:05.840><c> controlling</c><00:07:06.319><c> all</c>

00:07:06.510 --> 00:07:06.520 align:start position:0%
remote control that's controlling all
 

00:07:06.520 --> 00:07:08.710 align:start position:0%
remote control that's controlling all
these<00:07:06.680><c> bands</c><00:07:07.479><c> and</c><00:07:07.960><c> that's</c><00:07:08.160><c> how</c><00:07:08.479><c> they</c><00:07:08.599><c> can</c>

00:07:08.710 --> 00:07:08.720 align:start position:0%
these bands and that's how they can
 

00:07:08.720 --> 00:07:10.749 align:start position:0%
these bands and that's how they can
change<00:07:09.039><c> color</c><00:07:09.479><c> and</c><00:07:09.560><c> The</c><00:07:09.680><c> Flipper</c><00:07:10.039><c> zero</c><00:07:10.520><c> can</c>

00:07:10.749 --> 00:07:10.759 align:start position:0%
change color and The Flipper zero can
 

00:07:10.759 --> 00:07:13.270 align:start position:0%
change color and The Flipper zero can
actually<00:07:11.080><c> act</c><00:07:11.319><c> as</c><00:07:11.440><c> an</c><00:07:11.599><c> IR</c><00:07:11.919><c> remote</c><00:07:12.639><c> so</c><00:07:12.879><c> with</c><00:07:13.039><c> the</c>

00:07:13.270 --> 00:07:13.280 align:start position:0%
actually act as an IR remote so with the
 

00:07:13.280 --> 00:07:15.550 align:start position:0%
actually act as an IR remote so with the
right<00:07:13.520><c> you</c><00:07:13.639><c> know</c><00:07:13.960><c> code</c><00:07:14.440><c> in</c><00:07:14.680><c> place</c><00:07:15.120><c> you</c><00:07:15.319><c> can</c>

00:07:15.550 --> 00:07:15.560 align:start position:0%
right you know code in place you can
 

00:07:15.560 --> 00:07:17.110 align:start position:0%
right you know code in place you can
control<00:07:15.840><c> the</c><00:07:15.919><c> colors</c><00:07:16.720><c> now</c><00:07:16.840><c> there</c><00:07:16.919><c> are</c><00:07:17.039><c> a</c>

00:07:17.110 --> 00:07:17.120 align:start position:0%
control the colors now there are a
 

00:07:17.120 --> 00:07:18.430 align:start position:0%
control the colors now there are a
couple<00:07:17.280><c> of</c><00:07:17.400><c> projects</c><00:07:17.720><c> on</c><00:07:17.879><c> GitHub</c><00:07:18.240><c> and</c><00:07:18.319><c> I've</c>

00:07:18.430 --> 00:07:18.440 align:start position:0%
couple of projects on GitHub and I've
 

00:07:18.440 --> 00:07:20.189 align:start position:0%
couple of projects on GitHub and I've
got<00:07:18.599><c> those</c><00:07:18.759><c> linked</c><00:07:19.080><c> alongside</c><00:07:19.680><c> uh</c><00:07:19.800><c> John's</c>

00:07:20.189 --> 00:07:20.199 align:start position:0%
got those linked alongside uh John's
 

00:07:20.199 --> 00:07:22.110 align:start position:0%
got those linked alongside uh John's
blog<00:07:20.599><c> if</c><00:07:20.680><c> you</c><00:07:20.759><c> want</c><00:07:20.879><c> to</c><00:07:21.080><c> play</c><00:07:21.280><c> around</c><00:07:21.879><c> with</c><00:07:22.039><c> one</c>

00:07:22.110 --> 00:07:22.120 align:start position:0%
blog if you want to play around with one
 

00:07:22.120 --> 00:07:23.950 align:start position:0%
blog if you want to play around with one
of<00:07:22.280><c> those</c><00:07:22.479><c> yourself</c><00:07:23.160><c> and</c><00:07:23.240><c> if</c><00:07:23.360><c> you</c><00:07:23.560><c> actually</c><00:07:23.840><c> if</c>

00:07:23.950 --> 00:07:23.960 align:start position:0%
of those yourself and if you actually if
 

00:07:23.960 --> 00:07:25.589 align:start position:0%
of those yourself and if you actually if
you<00:07:24.039><c> happen</c><00:07:24.199><c> to</c><00:07:24.319><c> have</c><00:07:24.520><c> an</c><00:07:24.800><c> an</c><00:07:24.879><c> extra</c><00:07:25.120><c> wristband</c>

00:07:25.589 --> 00:07:25.599 align:start position:0%
you happen to have an an extra wristband
 

00:07:25.599 --> 00:07:27.350 align:start position:0%
you happen to have an an extra wristband
from<00:07:25.680><c> a</c><00:07:25.800><c> Taylor</c><00:07:26.080><c> Swift</c><00:07:26.360><c> show</c><00:07:26.599><c> or</c><00:07:26.759><c> Coldplay</c><00:07:27.199><c> or</c>

00:07:27.350 --> 00:07:27.360 align:start position:0%
from a Taylor Swift show or Coldplay or
 

00:07:27.360 --> 00:07:29.670 align:start position:0%
from a Taylor Swift show or Coldplay or
Beyonce<00:07:28.000><c> or</c><00:07:28.120><c> someone</c><00:07:28.400><c> else</c><00:07:29.080><c> um</c><00:07:29.199><c> and</c><00:07:29.319><c> all</c><00:07:29.599><c> that</c>

00:07:29.670 --> 00:07:29.680 align:start position:0%
Beyonce or someone else um and all that
 

00:07:29.680 --> 00:07:31.990 align:start position:0%
Beyonce or someone else um and all that
is<00:07:29.759><c> linked</c><00:07:30.039><c> below</c><00:07:31.000><c> and</c><00:07:31.160><c> now</c><00:07:31.360><c> I've</c><00:07:31.560><c> decided</c>

00:07:31.990 --> 00:07:32.000 align:start position:0%
is linked below and now I've decided
 

00:07:32.000 --> 00:07:34.350 align:start position:0%
is linked below and now I've decided
that<00:07:32.160><c> I'm</c><00:07:32.319><c> going</c><00:07:32.400><c> to</c><00:07:32.639><c> have</c><00:07:32.919><c> to</c><00:07:33.919><c> change</c><00:07:34.240><c> the</c>

00:07:34.350 --> 00:07:34.360 align:start position:0%
that I'm going to have to change the
 

00:07:34.360 --> 00:07:37.909 align:start position:0%
that I'm going to have to change the
color<00:07:34.560><c> of</c><00:07:34.680><c> my</c><00:07:34.800><c> wristband</c><00:07:35.720><c> at</c><00:07:35.960><c> a</c><00:07:36.120><c> show</c><00:07:37.120><c> um</c><00:07:37.560><c> so</c><00:07:37.759><c> so</c>

00:07:37.909 --> 00:07:37.919 align:start position:0%
color of my wristband at a show um so so
 

00:07:37.919 --> 00:07:38.990 align:start position:0%
color of my wristband at a show um so so
that's<00:07:38.120><c> that's</c><00:07:38.240><c> my</c><00:07:38.360><c> new</c><00:07:38.520><c> goal</c><00:07:38.720><c> and</c><00:07:38.800><c> so</c><00:07:38.919><c> that</c>

00:07:38.990 --> 00:07:39.000 align:start position:0%
that's that's my new goal and so that
 

00:07:39.000 --> 00:07:39.990 align:start position:0%
that's that's my new goal and so that
means<00:07:39.199><c> I'm</c><00:07:39.280><c> going</c><00:07:39.360><c> to</c><00:07:39.440><c> have</c><00:07:39.520><c> to</c><00:07:39.599><c> go</c><00:07:39.680><c> to</c><00:07:39.800><c> one</c><00:07:39.879><c> of</c>

00:07:39.990 --> 00:07:40.000 align:start position:0%
means I'm going to have to go to one of
 

00:07:40.000 --> 00:07:41.670 align:start position:0%
means I'm going to have to go to one of
the<00:07:40.080><c> European</c><00:07:40.560><c> stops</c><00:07:40.879><c> later</c><00:07:41.120><c> this</c><00:07:41.240><c> summer</c>

00:07:41.670 --> 00:07:41.680 align:start position:0%
the European stops later this summer
 

00:07:41.680 --> 00:07:43.790 align:start position:0%
the European stops later this summer
that's<00:07:41.879><c> just</c><00:07:42.120><c> that's</c><00:07:42.280><c> just</c><00:07:42.440><c> how</c><00:07:42.599><c> this</c><00:07:42.800><c> works</c>

00:07:43.790 --> 00:07:43.800 align:start position:0%
that's just that's just how this works
 

00:07:43.800 --> 00:07:45.189 align:start position:0%
that's just that's just how this works
let<00:07:43.919><c> me</c><00:07:44.039><c> know</c><00:07:44.159><c> your</c><00:07:44.280><c> favorite</c><00:07:44.520><c> flipper</c><00:07:44.879><c> zero</c>

00:07:45.189 --> 00:07:45.199 align:start position:0%
let me know your favorite flipper zero
 

00:07:45.199 --> 00:07:47.270 align:start position:0%
let me know your favorite flipper zero
hacks<00:07:45.639><c> in</c><00:07:45.759><c> the</c><00:07:45.919><c> comments</c><00:07:46.280><c> down</c><00:07:46.479><c> below</c><00:07:46.840><c> or</c><00:07:47.039><c> your</c>

00:07:47.270 --> 00:07:47.280 align:start position:0%
hacks in the comments down below or your
 

00:07:47.280 --> 00:07:48.510 align:start position:0%
hacks in the comments down below or your
thoughts<00:07:47.479><c> on</c><00:07:47.680><c> any</c><00:07:47.840><c> of</c><00:07:47.919><c> the</c><00:07:48.039><c> other</c><00:07:48.199><c> stories</c>

00:07:48.510 --> 00:07:48.520 align:start position:0%
thoughts on any of the other stories
 

00:07:48.520 --> 00:07:50.110 align:start position:0%
thoughts on any of the other stories
that<00:07:48.639><c> we</c><00:07:48.800><c> covered</c><00:07:49.280><c> maybe</c><00:07:49.479><c> your</c><00:07:49.720><c> favorite</c>

00:07:50.110 --> 00:07:50.120 align:start position:0%
that we covered maybe your favorite
 

00:07:50.120 --> 00:07:51.550 align:start position:0%
that we covered maybe your favorite
announcements<00:07:50.520><c> from</c><00:07:50.680><c> Microsoft</c><00:07:51.080><c> build</c><00:07:51.360><c> or</c>

00:07:51.550 --> 00:07:51.560 align:start position:0%
announcements from Microsoft build or
 

00:07:51.560 --> 00:07:53.589 align:start position:0%
announcements from Microsoft build or
anything<00:07:51.879><c> else</c><00:07:52.800><c> that's</c><00:07:52.960><c> going</c><00:07:53.039><c> to</c><00:07:53.159><c> do</c><00:07:53.280><c> it</c><00:07:53.360><c> for</c>

00:07:53.589 --> 00:07:53.599 align:start position:0%
anything else that's going to do it for
 

00:07:53.599 --> 00:07:55.510 align:start position:0%
anything else that's going to do it for
me<00:07:53.919><c> if</c><00:07:54.000><c> you</c><00:07:54.159><c> liked</c><00:07:54.440><c> this</c><00:07:54.599><c> episode</c><00:07:55.120><c> please</c>

00:07:55.510 --> 00:07:55.520 align:start position:0%
me if you liked this episode please
 

00:07:55.520 --> 00:07:56.990 align:start position:0%
me if you liked this episode please
leave<00:07:55.840><c> us</c><00:07:56.080><c> a</c><00:07:56.280><c> like</c><00:07:56.440><c> to</c><00:07:56.599><c> help</c><00:07:56.759><c> out</c><00:07:56.879><c> the</c>

00:07:56.990 --> 00:07:57.000 align:start position:0%
leave us a like to help out the
 

00:07:57.000 --> 00:07:58.909 align:start position:0%
leave us a like to help out the
algorithm<00:07:57.759><c> And</c><00:07:58.039><c> subscribe</c><00:07:58.360><c> to</c><00:07:58.479><c> the</c><00:07:58.599><c> GitHub</c>

00:07:58.909 --> 00:07:58.919 align:start position:0%
algorithm And subscribe to the GitHub
 

00:07:58.919 --> 00:08:00.830 align:start position:0%
algorithm And subscribe to the GitHub
YouTube<00:07:59.120><c> channel</c><00:07:59.520><c> for</c><00:07:59.639><c> all</c><00:07:59.720><c> your</c><00:07:59.840><c> nerd</c><00:08:00.120><c> needs</c>

00:08:00.830 --> 00:08:00.840 align:start position:0%
YouTube channel for all your nerd needs
 

00:08:00.840 --> 00:08:03.060 align:start position:0%
YouTube channel for all your nerd needs
see<00:08:01.000><c> you</c><00:08:01.159><c> next</c><00:08:01.400><c> time</c>

00:08:03.060 --> 00:08:03.070 align:start position:0%
see you next time
 

00:08:03.070 --> 00:08:08.149 align:start position:0%
see you next time
[Music]

