WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:02.270 align:start position:0%
 
all<00:00:00.199><c> right</c><00:00:00.320><c> it's</c><00:00:00.560><c> day</c><00:00:00.880><c> 25</c><00:00:01.480><c> of</c><00:00:01.599><c> the</c><00:00:01.680><c> 31-day</c>

00:00:02.270 --> 00:00:02.280 align:start position:0%
all right it's day 25 of the 31-day
 

00:00:02.280 --> 00:00:03.709 align:start position:0%
all right it's day 25 of the 31-day
challenge<00:00:02.720><c> and</c><00:00:02.919><c> today</c><00:00:03.280><c> we're</c><00:00:03.439><c> going</c><00:00:03.560><c> to</c><00:00:03.600><c> be</c>

00:00:03.709 --> 00:00:03.719 align:start position:0%
challenge and today we're going to be
 

00:00:03.719 --> 00:00:05.749 align:start position:0%
challenge and today we're going to be
using<00:00:04.080><c> Reddit</c><00:00:04.520><c> to</c><00:00:04.720><c> create</c><00:00:04.960><c> a</c><00:00:05.040><c> newsletter</c><00:00:05.640><c> this</c>

00:00:05.749 --> 00:00:05.759 align:start position:0%
using Reddit to create a newsletter this
 

00:00:05.759 --> 00:00:07.110 align:start position:0%
using Reddit to create a newsletter this
will<00:00:05.879><c> be</c><00:00:05.960><c> done</c><00:00:06.120><c> using</c><00:00:06.319><c> the</c><00:00:06.480><c> Reddit</c><00:00:06.759><c> loader</c>

00:00:07.110 --> 00:00:07.120 align:start position:0%
will be done using the Reddit loader
 

00:00:07.120 --> 00:00:09.430 align:start position:0%
will be done using the Reddit loader
from<00:00:07.319><c> linkchain</c><00:00:07.680><c> and</c><00:00:08.320><c> then</c><00:00:08.719><c> autogen</c><00:00:09.280><c> will</c>

00:00:09.430 --> 00:00:09.440 align:start position:0%
from linkchain and then autogen will
 

00:00:09.440 --> 00:00:10.830 align:start position:0%
from linkchain and then autogen will
orchestrate<00:00:09.880><c> the</c><00:00:10.000><c> agents</c><00:00:10.280><c> to</c><00:00:10.480><c> create</c><00:00:10.719><c> the</c>

00:00:10.830 --> 00:00:10.840 align:start position:0%
orchestrate the agents to create the
 

00:00:10.840 --> 00:00:11.830 align:start position:0%
orchestrate the agents to create the
newsletter<00:00:11.320><c> the</c><00:00:11.400><c> first</c><00:00:11.519><c> thing</c><00:00:11.599><c> I</c><00:00:11.679><c> want</c><00:00:11.759><c> to</c>

00:00:11.830 --> 00:00:11.840 align:start position:0%
newsletter the first thing I want to
 

00:00:11.840 --> 00:00:13.390 align:start position:0%
newsletter the first thing I want to
show<00:00:11.920><c> you</c><00:00:12.000><c> how</c><00:00:12.080><c> to</c><00:00:12.240><c> do</c><00:00:12.440><c> is</c><00:00:12.599><c> just</c><00:00:12.759><c> go</c><00:00:12.880><c> to</c><00:00:13.080><c> Reddit</c>

00:00:13.390 --> 00:00:13.400 align:start position:0%
show you how to do is just go to Reddit
 

00:00:13.400 --> 00:00:15.629 align:start position:0%
show you how to do is just go to Reddit
and<00:00:13.480><c> create</c><00:00:13.759><c> a</c><00:00:13.920><c> simple</c><00:00:14.360><c> app</c><00:00:14.719><c> from</c><00:00:14.920><c> your</c><00:00:15.080><c> user</c>

00:00:15.629 --> 00:00:15.639 align:start position:0%
and create a simple app from your user
 

00:00:15.639 --> 00:00:18.070 align:start position:0%
and create a simple app from your user
so<00:00:15.799><c> that</c><00:00:16.000><c> we</c><00:00:16.160><c> can</c><00:00:16.359><c> get</c><00:00:16.560><c> an</c><00:00:16.760><c> ID</c><00:00:17.080><c> and</c><00:00:17.240><c> a</c><00:00:17.359><c> secret</c><00:00:17.880><c> to</c>

00:00:18.070 --> 00:00:18.080 align:start position:0%
so that we can get an ID and a secret to
 

00:00:18.080 --> 00:00:20.109 align:start position:0%
so that we can get an ID and a secret to
use<00:00:18.320><c> for</c><00:00:18.439><c> the</c><00:00:18.560><c> Reddit</c><00:00:18.800><c> loader</c><00:00:19.080><c> and</c><00:00:19.199><c> we</c><00:00:19.320><c> can</c><00:00:19.560><c> get</c>

00:00:20.109 --> 00:00:20.119 align:start position:0%
use for the Reddit loader and we can get
 

00:00:20.119 --> 00:00:22.109 align:start position:0%
use for the Reddit loader and we can get
uh<00:00:20.279><c> whatever</c><00:00:20.880><c> subreddit</c><00:00:21.400><c> and</c><00:00:21.519><c> the</c><00:00:21.680><c> post</c><00:00:21.960><c> from</c>

00:00:22.109 --> 00:00:22.119 align:start position:0%
uh whatever subreddit and the post from
 

00:00:22.119 --> 00:00:23.150 align:start position:0%
uh whatever subreddit and the post from
there<00:00:22.279><c> that</c><00:00:22.400><c> we</c><00:00:22.519><c> want</c><00:00:22.760><c> all</c><00:00:22.840><c> right</c><00:00:22.960><c> well</c><00:00:23.080><c> the</c>

00:00:23.150 --> 00:00:23.160 align:start position:0%
there that we want all right well the
 

00:00:23.160 --> 00:00:24.990 align:start position:0%
there that we want all right well the
first<00:00:23.320><c> thing</c><00:00:23.480><c> is</c><00:00:24.039><c> this</c><00:00:24.199><c> URL</c><00:00:24.560><c> will</c><00:00:24.680><c> be</c><00:00:24.800><c> in</c><00:00:24.880><c> the</c>

00:00:24.990 --> 00:00:25.000 align:start position:0%
first thing is this URL will be in the
 

00:00:25.000 --> 00:00:26.910 align:start position:0%
first thing is this URL will be in the
description<00:00:25.640><c> you</c><00:00:25.880><c> come</c><00:00:26.119><c> here</c><00:00:26.599><c> there</c><00:00:26.679><c> will</c><00:00:26.800><c> be</c>

00:00:26.910 --> 00:00:26.920 align:start position:0%
description you come here there will be
 

00:00:26.920 --> 00:00:28.390 align:start position:0%
description you come here there will be
a<00:00:27.039><c> button</c><00:00:27.320><c> if</c><00:00:27.400><c> you</c><00:00:27.560><c> haven't</c><00:00:27.800><c> done</c><00:00:28.000><c> this</c><00:00:28.199><c> before</c>

00:00:28.390 --> 00:00:28.400 align:start position:0%
a button if you haven't done this before
 

00:00:28.400 --> 00:00:30.990 align:start position:0%
a button if you haven't done this before
to<00:00:28.640><c> create</c><00:00:28.880><c> a</c><00:00:29.000><c> new</c><00:00:29.160><c> app</c><00:00:29.519><c> you</c><00:00:29.679><c> click</c><00:00:30.000><c> c</c><00:00:30.160><c> on</c><00:00:30.320><c> it</c>

00:00:30.990 --> 00:00:31.000 align:start position:0%
to create a new app you click c on it
 

00:00:31.000 --> 00:00:32.350 align:start position:0%
to create a new app you click c on it
you<00:00:31.080><c> want</c><00:00:31.199><c> to</c><00:00:31.400><c> give</c><00:00:31.560><c> this</c><00:00:31.759><c> information</c><00:00:32.200><c> in</c>

00:00:32.350 --> 00:00:32.360 align:start position:0%
you want to give this information in
 

00:00:32.360 --> 00:00:34.590 align:start position:0%
you want to give this information in
here<00:00:32.599><c> right</c><00:00:32.759><c> so</c><00:00:33.040><c> I</c><00:00:33.160><c> gave</c><00:00:33.320><c> it</c><00:00:33.440><c> the</c><00:00:33.600><c> name</c><00:00:34.000><c> Tyler</c>

00:00:34.590 --> 00:00:34.600 align:start position:0%
here right so I gave it the name Tyler
 

00:00:34.600 --> 00:00:37.190 align:start position:0%
here right so I gave it the name Tyler
newsletter<00:00:35.480><c> you</c><00:00:35.719><c> choose</c><00:00:36.280><c> the</c><00:00:36.600><c> radio</c><00:00:36.960><c> button</c>

00:00:37.190 --> 00:00:37.200 align:start position:0%
newsletter you choose the radio button
 

00:00:37.200 --> 00:00:39.110 align:start position:0%
newsletter you choose the radio button
for<00:00:37.399><c> script</c><00:00:38.160><c> you</c><00:00:38.360><c> just</c><00:00:38.520><c> can</c><00:00:38.680><c> just</c><00:00:38.800><c> put</c>

00:00:39.110 --> 00:00:39.120 align:start position:0%
for script you just can just put
 

00:00:39.120 --> 00:00:41.869 align:start position:0%
for script you just can just put
reddit.com<00:00:40.120><c> uh</c><00:00:40.280><c> URL</c><00:00:40.840><c> in</c><00:00:41.039><c> here</c><00:00:41.440><c> and</c><00:00:41.559><c> then</c><00:00:41.680><c> you</c>

00:00:41.869 --> 00:00:41.879 align:start position:0%
reddit.com uh URL in here and then you
 

00:00:41.879 --> 00:00:44.229 align:start position:0%
reddit.com uh URL in here and then you
click<00:00:42.160><c> I'm</c><00:00:42.320><c> not</c><00:00:42.480><c> a</c><00:00:42.600><c> robot</c><00:00:43.440><c> and</c><00:00:43.559><c> when</c><00:00:43.680><c> you</c><00:00:43.840><c> click</c>

00:00:44.229 --> 00:00:44.239 align:start position:0%
click I'm not a robot and when you click
 

00:00:44.239 --> 00:00:46.950 align:start position:0%
click I'm not a robot and when you click
create<00:00:44.800><c> app</c><00:00:45.160><c> it'll</c><00:00:45.360><c> say</c><00:00:45.600><c> application</c><00:00:46.160><c> created</c>

00:00:46.950 --> 00:00:46.960 align:start position:0%
create app it'll say application created
 

00:00:46.960 --> 00:00:49.869 align:start position:0%
create app it'll say application created
and<00:00:47.079><c> you'll</c><00:00:47.280><c> be</c><00:00:47.440><c> given</c><00:00:47.719><c> your</c><00:00:48.000><c> client</c><00:00:48.399><c> ID</c><00:00:48.879><c> here</c>

00:00:49.869 --> 00:00:49.879 align:start position:0%
and you'll be given your client ID here
 

00:00:49.879 --> 00:00:52.270 align:start position:0%
and you'll be given your client ID here
and<00:00:50.199><c> your</c><00:00:50.480><c> client</c><00:00:50.920><c> secret</c><00:00:51.480><c> here</c><00:00:51.879><c> and</c><00:00:52.039><c> this</c><00:00:52.160><c> is</c>

00:00:52.270 --> 00:00:52.280 align:start position:0%
and your client secret here and this is
 

00:00:52.280 --> 00:00:53.549 align:start position:0%
and your client secret here and this is
what<00:00:52.359><c> we're</c><00:00:52.480><c> going</c><00:00:52.600><c> to</c><00:00:52.719><c> use</c><00:00:53.079><c> inside</c><00:00:53.359><c> of</c><00:00:53.440><c> the</c>

00:00:53.549 --> 00:00:53.559 align:start position:0%
what we're going to use inside of the
 

00:00:53.559 --> 00:00:55.029 align:start position:0%
what we're going to use inside of the
Reddit<00:00:53.879><c> loader</c><00:00:54.280><c> so</c><00:00:54.399><c> you</c><00:00:54.480><c> just</c><00:00:54.559><c> want</c><00:00:54.680><c> to</c><00:00:54.840><c> copy</c>

00:00:55.029 --> 00:00:55.039 align:start position:0%
Reddit loader so you just want to copy
 

00:00:55.039 --> 00:00:56.590 align:start position:0%
Reddit loader so you just want to copy
and<00:00:55.160><c> paste</c><00:00:55.399><c> those</c><00:00:55.559><c> things</c><00:00:55.760><c> when</c><00:00:55.879><c> we</c><00:00:56.039><c> get</c><00:00:56.199><c> there</c>

00:00:56.590 --> 00:00:56.600 align:start position:0%
and paste those things when we get there
 

00:00:56.600 --> 00:00:57.990 align:start position:0%
and paste those things when we get there
okay<00:00:56.800><c> well</c><00:00:57.039><c> we</c><00:00:57.199><c> might</c><00:00:57.359><c> as</c><00:00:57.480><c> well</c><00:00:57.640><c> just</c><00:00:57.800><c> dive</c>

00:00:57.990 --> 00:00:58.000 align:start position:0%
okay well we might as well just dive
 

00:00:58.000 --> 00:01:00.150 align:start position:0%
okay well we might as well just dive
into<00:00:58.199><c> the</c><00:00:58.359><c> code</c><00:00:58.879><c> so</c><00:00:59.160><c> again</c><00:00:59.600><c> like</c><00:00:59.719><c> I</c><00:01:00.000><c> said</c><00:01:00.079><c> you</c>

00:01:00.150 --> 00:01:00.160 align:start position:0%
into the code so again like I said you
 

00:01:00.160 --> 00:01:02.029 align:start position:0%
into the code so again like I said you
need<00:01:00.280><c> your</c><00:01:00.440><c> client</c><00:01:00.719><c> ID</c><00:01:01.000><c> and</c><00:01:01.199><c> secret</c><00:01:01.840><c> I'm</c><00:01:01.920><c> going</c>

00:01:02.029 --> 00:01:02.039 align:start position:0%
need your client ID and secret I'm going
 

00:01:02.039 --> 00:01:04.509 align:start position:0%
need your client ID and secret I'm going
to<00:01:02.199><c> paste</c><00:01:02.480><c> my</c><00:01:02.800><c> client</c><00:01:03.239><c> ID</c><00:01:03.640><c> here</c><00:01:03.920><c> and</c><00:01:04.080><c> then</c><00:01:04.239><c> my</c>

00:01:04.509 --> 00:01:04.519 align:start position:0%
to paste my client ID here and then my
 

00:01:04.519 --> 00:01:06.710 align:start position:0%
to paste my client ID here and then my
client<00:01:05.040><c> secret</c><00:01:05.560><c> here</c><00:01:05.880><c> okay</c><00:01:05.960><c> so</c><00:01:06.119><c> we</c><00:01:06.280><c> take</c><00:01:06.520><c> from</c>

00:01:06.710 --> 00:01:06.720 align:start position:0%
client secret here okay so we take from
 

00:01:06.720 --> 00:01:08.630 align:start position:0%
client secret here okay so we take from
Lang<00:01:07.040><c> chain</c><00:01:07.520><c> the</c><00:01:07.680><c> document</c><00:01:08.040><c> loader</c><00:01:08.320><c> we</c><00:01:08.439><c> take</c>

00:01:08.630 --> 00:01:08.640 align:start position:0%
Lang chain the document loader we take
 

00:01:08.640 --> 00:01:10.429 align:start position:0%
Lang chain the document loader we take
the<00:01:08.759><c> Reddit</c><00:01:09.119><c> post</c><00:01:09.400><c> loader</c><00:01:09.880><c> we</c><00:01:09.960><c> have</c><00:01:10.080><c> a</c><00:01:10.159><c> user</c>

00:01:10.429 --> 00:01:10.439 align:start position:0%
the Reddit post loader we have a user
 

00:01:10.439 --> 00:01:12.469 align:start position:0%
the Reddit post loader we have a user
agent<00:01:10.960><c> which</c><00:01:11.280><c> I</c><00:01:11.439><c> just</c><00:01:11.880><c> you</c><00:01:12.119><c> I</c><00:01:12.200><c> think</c><00:01:12.320><c> you</c><00:01:12.360><c> can</c>

00:01:12.469 --> 00:01:12.479 align:start position:0%
agent which I just you I think you can
 

00:01:12.479 --> 00:01:13.830 align:start position:0%
agent which I just you I think you can
name<00:01:12.640><c> this</c><00:01:12.799><c> whatever</c><00:01:13.040><c> you</c><00:01:13.119><c> want</c><00:01:13.479><c> but</c><00:01:13.600><c> I</c><00:01:13.720><c> kind</c>

00:01:13.830 --> 00:01:13.840 align:start position:0%
name this whatever you want but I kind
 

00:01:13.840 --> 00:01:15.630 align:start position:0%
name this whatever you want but I kind
of<00:01:13.960><c> follow</c><00:01:14.200><c> the</c><00:01:14.400><c> convention</c><00:01:15.000><c> that</c><00:01:15.159><c> they</c><00:01:15.320><c> have</c>

00:01:15.630 --> 00:01:15.640 align:start position:0%
of follow the convention that they have
 

00:01:15.640 --> 00:01:17.950 align:start position:0%
of follow the convention that they have
extractor<00:01:16.240><c> by</c><00:01:16.479><c> and</c><00:01:16.560><c> then</c><00:01:16.720><c> this</c><00:01:16.840><c> is</c><00:01:17.040><c> my</c><00:01:17.640><c> Reddit</c>

00:01:17.950 --> 00:01:17.960 align:start position:0%
extractor by and then this is my Reddit
 

00:01:17.960 --> 00:01:19.950 align:start position:0%
extractor by and then this is my Reddit
username<00:01:18.520><c> now</c><00:01:18.680><c> you</c><00:01:18.799><c> know</c><00:01:18.960><c> it</c><00:01:19.240><c> um</c><00:01:19.400><c> you</c><00:01:19.479><c> can</c><00:01:19.680><c> give</c>

00:01:19.950 --> 00:01:19.960 align:start position:0%
username now you know it um you can give
 

00:01:19.960 --> 00:01:21.830 align:start position:0%
username now you know it um you can give
categories<00:01:20.520><c> here</c><00:01:21.000><c> so</c><00:01:21.280><c> I</c><00:01:21.360><c> think</c><00:01:21.520><c> they</c><00:01:21.640><c> only</c>

00:01:21.830 --> 00:01:21.840 align:start position:0%
categories here so I think they only
 

00:01:21.840 --> 00:01:23.749 align:start position:0%
categories here so I think they only
allow<00:01:22.040><c> you</c><00:01:22.119><c> to</c><00:01:22.320><c> do</c><00:01:22.640><c> controversial</c><00:01:23.280><c> hot</c><00:01:23.520><c> new</c>

00:01:23.749 --> 00:01:23.759 align:start position:0%
allow you to do controversial hot new
 

00:01:23.759 --> 00:01:25.510 align:start position:0%
allow you to do controversial hot new
rising<00:01:24.240><c> in</c><00:01:24.479><c> top</c><00:01:24.720><c> so</c><00:01:24.920><c> I'm</c><00:01:25.000><c> just</c><00:01:25.119><c> going</c><00:01:25.200><c> to</c><00:01:25.320><c> put</c>

00:01:25.510 --> 00:01:25.520 align:start position:0%
rising in top so I'm just going to put
 

00:01:25.520 --> 00:01:27.469 align:start position:0%
rising in top so I'm just going to put
new<00:01:26.000><c> uh</c><00:01:26.119><c> I</c><00:01:26.200><c> just</c><00:01:26.320><c> want</c><00:01:26.439><c> to</c><00:01:26.759><c> retrieve</c><00:01:27.200><c> posts</c>

00:01:27.469 --> 00:01:27.479 align:start position:0%
new uh I just want to retrieve posts
 

00:01:27.479 --> 00:01:29.550 align:start position:0%
new uh I just want to retrieve posts
from<00:01:27.680><c> subreddits</c><00:01:28.680><c> and</c><00:01:28.759><c> then</c><00:01:28.920><c> queries</c><00:01:29.360><c> here</c>

00:01:29.550 --> 00:01:29.560 align:start position:0%
from subreddits and then queries here
 

00:01:29.560 --> 00:01:31.630 align:start position:0%
from subreddits and then queries here
you<00:01:29.640><c> can</c><00:01:30.040><c> put</c><00:01:30.320><c> um</c><00:01:30.479><c> a</c><00:01:30.600><c> list</c><00:01:30.799><c> of</c><00:01:30.960><c> subreddits</c><00:01:31.520><c> that</c>

00:01:31.630 --> 00:01:31.640 align:start position:0%
you can put um a list of subreddits that
 

00:01:31.640 --> 00:01:34.030 align:start position:0%
you can put um a list of subreddits that
you<00:01:31.840><c> want</c><00:01:32.680><c> and</c><00:01:32.840><c> then</c><00:01:33.000><c> you</c><00:01:33.119><c> can</c><00:01:33.320><c> also</c><00:01:33.600><c> put</c><00:01:33.840><c> the</c>

00:01:34.030 --> 00:01:34.040 align:start position:0%
you want and then you can also put the
 

00:01:34.040 --> 00:01:36.550 align:start position:0%
you want and then you can also put the
number<00:01:34.439><c> of</c><00:01:34.799><c> posts</c><00:01:35.720><c> from</c><00:01:36.000><c> I</c><00:01:36.079><c> think</c><00:01:36.200><c> it's</c><00:01:36.360><c> from</c>

00:01:36.550 --> 00:01:36.560 align:start position:0%
number of posts from I think it's from
 

00:01:36.560 --> 00:01:37.789 align:start position:0%
number of posts from I think it's from
each<00:01:36.759><c> of</c><00:01:36.960><c> these</c><00:01:37.200><c> that</c><00:01:37.360><c> you</c><00:01:37.439><c> would</c><00:01:37.560><c> like</c><00:01:37.680><c> to</c>

00:01:37.789 --> 00:01:37.799 align:start position:0%
each of these that you would like to
 

00:01:37.799 --> 00:01:39.590 align:start position:0%
each of these that you would like to
have<00:01:38.119><c> so</c><00:01:38.280><c> then</c><00:01:38.399><c> we</c><00:01:38.479><c> say</c><00:01:38.799><c> documents</c><00:01:39.200><c> equals</c>

00:01:39.590 --> 00:01:39.600 align:start position:0%
have so then we say documents equals
 

00:01:39.600 --> 00:01:41.710 align:start position:0%
have so then we say documents equals
loader.<00:01:40.200><c> load</c><00:01:40.399><c> so</c><00:01:40.520><c> it's</c><00:01:40.640><c> going</c><00:01:40.720><c> to</c><00:01:40.880><c> load</c><00:01:41.560><c> all</c>

00:01:41.710 --> 00:01:41.720 align:start position:0%
loader. load so it's going to load all
 

00:01:41.720 --> 00:01:43.870 align:start position:0%
loader. load so it's going to load all
of<00:01:41.920><c> that</c><00:01:42.159><c> information</c><00:01:42.799><c> into</c><00:01:43.079><c> these</c><00:01:43.320><c> documents</c>

00:01:43.870 --> 00:01:43.880 align:start position:0%
of that information into these documents
 

00:01:43.880 --> 00:01:45.429 align:start position:0%
of that information into these documents
we<00:01:43.960><c> have</c><00:01:44.079><c> the</c><00:01:44.159><c> config</c><00:01:44.520><c> list</c><00:01:44.719><c> and</c><00:01:44.840><c> the</c><00:01:44.920><c> llm</c>

00:01:45.429 --> 00:01:45.439 align:start position:0%
we have the config list and the llm
 

00:01:45.439 --> 00:01:47.109 align:start position:0%
we have the config list and the llm
config<00:01:46.000><c> I</c><00:01:46.079><c> will</c><00:01:46.280><c> have</c><00:01:46.399><c> it</c><00:01:46.600><c> so</c><00:01:46.759><c> that</c><00:01:46.880><c> you</c><00:01:46.960><c> can</c>

00:01:47.109 --> 00:01:47.119 align:start position:0%
config I will have it so that you can
 

00:01:47.119 --> 00:01:49.950 align:start position:0%
config I will have it so that you can
set<00:01:47.360><c> up</c><00:01:47.640><c> using</c><00:01:48.000><c> AMA</c><00:01:48.719><c> LM</c><00:01:49.119><c> Studio</c><00:01:49.640><c> or</c><00:01:49.759><c> if</c><00:01:49.840><c> you</c>

00:01:49.950 --> 00:01:49.960 align:start position:0%
set up using AMA LM Studio or if you
 

00:01:49.960 --> 00:01:52.069 align:start position:0%
set up using AMA LM Studio or if you
want<00:01:50.040><c> to</c><00:01:50.119><c> use</c><00:01:50.320><c> open</c><00:01:50.560><c> AI</c><00:01:50.960><c> API</c><00:01:51.520><c> that'll</c><00:01:51.759><c> all</c><00:01:51.920><c> be</c>

00:01:52.069 --> 00:01:52.079 align:start position:0%
want to use open AI API that'll all be
 

00:01:52.079 --> 00:01:54.109 align:start position:0%
want to use open AI API that'll all be
in<00:01:52.200><c> the</c><00:01:52.360><c> Json</c><00:01:52.920><c> configuration</c><00:01:53.640><c> then</c><00:01:53.759><c> I</c><00:01:53.960><c> have</c>

00:01:54.109 --> 00:01:54.119 align:start position:0%
in the Json configuration then I have
 

00:01:54.119 --> 00:01:56.670 align:start position:0%
in the Json configuration then I have
two<00:01:54.320><c> agents</c><00:01:54.840><c> I</c><00:01:54.960><c> have</c><00:01:55.079><c> the</c><00:01:55.159><c> writer</c><00:01:55.560><c> agent</c><00:01:56.479><c> which</c>

00:01:56.670 --> 00:01:56.680 align:start position:0%
two agents I have the writer agent which
 

00:01:56.680 --> 00:01:58.109 align:start position:0%
two agents I have the writer agent which
I<00:01:56.799><c> just</c><00:01:56.920><c> want</c><00:01:57.079><c> to</c><00:01:57.240><c> say</c><00:01:57.560><c> don't</c><00:01:57.759><c> change</c>

00:01:58.109 --> 00:01:58.119 align:start position:0%
I just want to say don't change
 

00:01:58.119 --> 00:02:00.029 align:start position:0%
I just want to say don't change
information<00:01:58.439><c> you're</c><00:01:58.640><c> given</c><00:01:59.000><c> just</c><00:01:59.280><c> parse</c><00:01:59.920><c> the</c>

00:02:00.029 --> 00:02:00.039 align:start position:0%
information you're given just parse the
 

00:02:00.039 --> 00:02:02.029 align:start position:0%
information you're given just parse the
page<00:02:00.360><c> content</c><00:02:00.680><c> from</c><00:02:00.840><c> the</c><00:02:00.960><c> Reddit</c><00:02:01.320><c> post</c><00:02:01.799><c> no</c>

00:02:02.029 --> 00:02:02.039 align:start position:0%
page content from the Reddit post no
 

00:02:02.039 --> 00:02:03.429 align:start position:0%
page content from the Reddit post no
code<00:02:02.280><c> will</c><00:02:02.399><c> be</c><00:02:02.520><c> written</c><00:02:02.880><c> if</c><00:02:02.960><c> I</c><00:02:03.079><c> give</c><00:02:03.200><c> it</c><00:02:03.320><c> kind</c>

00:02:03.429 --> 00:02:03.439 align:start position:0%
code will be written if I give it kind
 

00:02:03.439 --> 00:02:05.389 align:start position:0%
code will be written if I give it kind
of<00:02:03.520><c> a</c><00:02:03.680><c> basic</c><00:02:03.960><c> writer</c><00:02:04.320><c> prompt</c><00:02:04.719><c> it'll</c><00:02:05.000><c> take</c><00:02:05.240><c> the</c>

00:02:05.389 --> 00:02:05.399 align:start position:0%
of a basic writer prompt it'll take the
 

00:02:05.399 --> 00:02:07.429 align:start position:0%
of a basic writer prompt it'll take the
information<00:02:05.840><c> from</c><00:02:05.960><c> the</c><00:02:06.119><c> post</c><00:02:06.439><c> and</c><00:02:06.840><c> and</c><00:02:07.000><c> try</c><00:02:07.159><c> to</c>

00:02:07.429 --> 00:02:07.439 align:start position:0%
information from the post and and try to
 

00:02:07.439 --> 00:02:08.910 align:start position:0%
information from the post and and try to
morph<00:02:07.759><c> it</c><00:02:07.880><c> into</c><00:02:08.119><c> something</c><00:02:08.360><c> else</c><00:02:08.560><c> and</c><00:02:08.679><c> I</c><00:02:08.800><c> don't</c>

00:02:08.910 --> 00:02:08.920 align:start position:0%
morph it into something else and I don't
 

00:02:08.920 --> 00:02:10.869 align:start position:0%
morph it into something else and I don't
want<00:02:09.119><c> that</c><00:02:09.319><c> I</c><00:02:09.479><c> just</c><00:02:10.039><c> want</c><00:02:10.319><c> the</c><00:02:10.440><c> information</c>

00:02:10.869 --> 00:02:10.879 align:start position:0%
want that I just want the information
 

00:02:10.879 --> 00:02:12.030 align:start position:0%
want that I just want the information
from<00:02:11.039><c> the</c><00:02:11.200><c> post</c><00:02:11.599><c> and</c><00:02:11.680><c> then</c><00:02:11.760><c> we</c><00:02:11.840><c> have</c><00:02:11.920><c> a</c>

00:02:12.030 --> 00:02:12.040 align:start position:0%
from the post and then we have a
 

00:02:12.040 --> 00:02:13.790 align:start position:0%
from the post and then we have a
standard<00:02:12.319><c> user</c><00:02:12.640><c> proxy</c><00:02:13.000><c> agent</c><00:02:13.440><c> and</c><00:02:13.520><c> now</c><00:02:13.680><c> we</c>

00:02:13.790 --> 00:02:13.800 align:start position:0%
standard user proxy agent and now we
 

00:02:13.800 --> 00:02:15.869 align:start position:0%
standard user proxy agent and now we
want<00:02:13.959><c> to</c><00:02:14.200><c> initiate</c><00:02:14.599><c> the</c><00:02:14.720><c> chat</c><00:02:15.080><c> so</c><00:02:15.360><c> it's</c><00:02:15.519><c> just</c><00:02:15.680><c> a</c>

00:02:15.869 --> 00:02:15.879 align:start position:0%
want to initiate the chat so it's just a
 

00:02:15.879 --> 00:02:17.470 align:start position:0%
want to initiate the chat so it's just a
simple<00:02:16.080><c> user</c><00:02:16.400><c> proxy</c><00:02:16.760><c> initiating</c><00:02:17.200><c> chat</c><00:02:17.400><c> with</c>

00:02:17.470 --> 00:02:17.480 align:start position:0%
simple user proxy initiating chat with
 

00:02:17.480 --> 00:02:19.790 align:start position:0%
simple user proxy initiating chat with
the<00:02:17.599><c> writer</c><00:02:18.400><c> um</c><00:02:18.560><c> I</c><00:02:18.640><c> want</c><00:02:18.800><c> to</c><00:02:19.040><c> extract</c><00:02:19.400><c> the</c><00:02:19.560><c> page</c>

00:02:19.790 --> 00:02:19.800 align:start position:0%
the writer um I want to extract the page
 

00:02:19.800 --> 00:02:22.229 align:start position:0%
the writer um I want to extract the page
content<00:02:20.239><c> and</c><00:02:20.480><c> URL</c><00:02:21.080><c> from</c><00:02:21.319><c> each</c><00:02:21.599><c> of</c><00:02:21.800><c> the</c>

00:02:22.229 --> 00:02:22.239 align:start position:0%
content and URL from each of the
 

00:02:22.239 --> 00:02:24.390 align:start position:0%
content and URL from each of the
documents<00:02:23.080><c> from</c><00:02:23.360><c> above</c><00:02:23.680><c> so</c><00:02:23.920><c> in</c><00:02:24.000><c> case</c><00:02:24.160><c> you</c><00:02:24.280><c> had</c>

00:02:24.390 --> 00:02:24.400 align:start position:0%
documents from above so in case you had
 

00:02:24.400 --> 00:02:25.550 align:start position:0%
documents from above so in case you had
more<00:02:24.560><c> than</c><00:02:24.680><c> one</c><00:02:24.879><c> document</c><00:02:25.280><c> right</c><00:02:25.400><c> there's</c>

00:02:25.550 --> 00:02:25.560 align:start position:0%
more than one document right there's
 

00:02:25.560 --> 00:02:27.949 align:start position:0%
more than one document right there's
going<00:02:25.680><c> to</c><00:02:25.800><c> be</c><00:02:26.560><c> uh</c><00:02:26.760><c> page</c><00:02:27.319><c> lots</c><00:02:27.519><c> of</c><00:02:27.720><c> page</c>

00:02:27.949 --> 00:02:27.959 align:start position:0%
going to be uh page lots of page
 

00:02:27.959 --> 00:02:30.150 align:start position:0%
going to be uh page lots of page
contents<00:02:28.400><c> from</c><00:02:28.680><c> each</c><00:02:28.920><c> of</c><00:02:29.080><c> the</c><00:02:29.280><c> documents</c><00:02:30.000><c> and</c>

00:02:30.150 --> 00:02:30.160 align:start position:0%
contents from each of the documents and
 

00:02:30.160 --> 00:02:31.750 align:start position:0%
contents from each of the documents and
then<00:02:30.480><c> I</c><00:02:30.760><c> just</c><00:02:30.920><c> give</c><00:02:31.080><c> it</c><00:02:31.160><c> some</c><00:02:31.319><c> information</c><00:02:31.599><c> to</c>

00:02:31.750 --> 00:02:31.760 align:start position:0%
then I just give it some information to
 

00:02:31.760 --> 00:02:33.070 align:start position:0%
then I just give it some information to
separate<00:02:32.040><c> it</c><00:02:32.160><c> out</c><00:02:32.400><c> I</c><00:02:32.480><c> want</c><00:02:32.599><c> to</c><00:02:32.680><c> create</c><00:02:32.879><c> a</c>

00:02:33.070 --> 00:02:33.080 align:start position:0%
separate it out I want to create a
 

00:02:33.080 --> 00:02:35.390 align:start position:0%
separate it out I want to create a
newsletter<00:02:34.080><c> uh</c><00:02:34.160><c> with</c><00:02:34.280><c> the</c><00:02:34.400><c> newsletter</c><00:02:34.959><c> Title</c>

00:02:35.390 --> 00:02:35.400 align:start position:0%
newsletter uh with the newsletter Title
 

00:02:35.400 --> 00:02:37.750 align:start position:0%
newsletter uh with the newsletter Title
Here<00:02:35.920><c> make</c><00:02:36.040><c> sure</c><00:02:36.200><c> it's</c><00:02:36.400><c> catchy</c><00:02:37.120><c> then</c><00:02:37.239><c> a</c><00:02:37.400><c> format</c>

00:02:37.750 --> 00:02:37.760 align:start position:0%
Here make sure it's catchy then a format
 

00:02:37.760 --> 00:02:39.630 align:start position:0%
Here make sure it's catchy then a format
of<00:02:37.920><c> the</c><00:02:38.040><c> mark</c><00:02:38.319><c> down</c><00:02:38.640><c> here</c><00:02:39.040><c> it's</c><00:02:39.200><c> just</c><00:02:39.319><c> going</c><00:02:39.400><c> to</c>

00:02:39.630 --> 00:02:39.640 align:start position:0%
of the mark down here it's just going to
 

00:02:39.640 --> 00:02:41.070 align:start position:0%
of the mark down here it's just going to
print<00:02:39.879><c> this</c><00:02:40.040><c> out</c><00:02:40.200><c> for</c><00:02:40.360><c> us</c><00:02:40.599><c> this</c><00:02:40.720><c> going</c><00:02:40.840><c> to</c><00:02:40.879><c> be</c><00:02:41.000><c> a</c>

00:02:41.070 --> 00:02:41.080 align:start position:0%
print this out for us this going to be a
 

00:02:41.080 --> 00:02:42.790 align:start position:0%
print this out for us this going to be a
simple<00:02:41.319><c> example</c><00:02:41.920><c> to</c><00:02:42.040><c> show</c><00:02:42.239><c> you</c><00:02:42.360><c> how</c><00:02:42.480><c> to</c><00:02:42.599><c> use</c>

00:02:42.790 --> 00:02:42.800 align:start position:0%
simple example to show you how to use
 

00:02:42.800 --> 00:02:44.910 align:start position:0%
simple example to show you how to use
Reddit<00:02:43.120><c> to</c><00:02:43.280><c> get</c><00:02:43.920><c> you</c><00:02:44.080><c> know</c><00:02:44.319><c> it's</c><00:02:44.440><c> not</c><00:02:44.680><c> I</c><00:02:44.720><c> mean</c>

00:02:44.910 --> 00:02:44.920 align:start position:0%
Reddit to get you know it's not I mean
 

00:02:44.920 --> 00:02:46.790 align:start position:0%
Reddit to get you know it's not I mean
it's<00:02:45.120><c> simple</c><00:02:45.560><c> but</c><00:02:45.840><c> it</c><00:02:46.080><c> it</c><00:02:46.200><c> does</c><00:02:46.400><c> give</c><00:02:46.519><c> you</c><00:02:46.680><c> a</c>

00:02:46.790 --> 00:02:46.800 align:start position:0%
it's simple but it it does give you a
 

00:02:46.800 --> 00:02:48.430 align:start position:0%
it's simple but it it does give you a
lot<00:02:46.920><c> of</c><00:02:47.080><c> information</c><00:02:47.560><c> right</c><00:02:47.680><c> you</c><00:02:47.800><c> can</c><00:02:48.319><c> uh</c>

00:02:48.430 --> 00:02:48.440 align:start position:0%
lot of information right you can uh
 

00:02:48.440 --> 00:02:51.550 align:start position:0%
lot of information right you can uh
search<00:02:48.720><c> for</c><00:02:49.080><c> any</c><00:02:49.519><c> AI</c><00:02:50.200><c> related</c><00:02:50.840><c> uh</c><00:02:50.920><c> subreddit</c>

00:02:51.550 --> 00:02:51.560 align:start position:0%
search for any AI related uh subreddit
 

00:02:51.560 --> 00:02:53.110 align:start position:0%
search for any AI related uh subreddit
and<00:02:51.680><c> then</c><00:02:51.879><c> get</c><00:02:52.040><c> the</c><00:02:52.159><c> latest</c><00:02:52.480><c> post</c><00:02:52.720><c> from</c><00:02:52.920><c> there</c>

00:02:53.110 --> 00:02:53.120 align:start position:0%
and then get the latest post from there
 

00:02:53.120 --> 00:02:54.910 align:start position:0%
and then get the latest post from there
let's<00:02:53.319><c> run</c><00:02:53.599><c> this</c><00:02:53.879><c> let</c><00:02:54.040><c> me</c><00:02:54.159><c> delete</c><00:02:54.400><c> the</c><00:02:54.560><c> cache</c>

00:02:54.910 --> 00:02:54.920 align:start position:0%
let's run this let me delete the cache
 

00:02:54.920 --> 00:02:57.990 align:start position:0%
let's run this let me delete the cache
and<00:02:55.159><c> rerun</c><00:02:55.640><c> this</c><00:02:56.159><c> okay</c><00:02:56.360><c> so</c><00:02:56.680><c> it</c><00:02:56.920><c> finished</c><00:02:57.800><c> um</c>

00:02:57.990 --> 00:02:58.000 align:start position:0%
and rerun this okay so it finished um
 

00:02:58.000 --> 00:02:59.509 align:start position:0%
and rerun this okay so it finished um
here<00:02:58.200><c> is</c><00:02:58.400><c> the</c><00:02:58.560><c> user</c><00:02:58.920><c> chatting</c><00:02:59.280><c> with</c><00:02:59.400><c> the</c>

00:02:59.509 --> 00:02:59.519 align:start position:0%
here is the user chatting with the
 

00:02:59.519 --> 00:03:01.990 align:start position:0%
here is the user chatting with the
writer<00:03:00.280><c> and</c><00:03:00.400><c> then</c><00:03:00.560><c> the</c><00:03:00.720><c> writer</c><00:03:01.200><c> talks</c><00:03:01.640><c> back</c>

00:03:01.990 --> 00:03:02.000 align:start position:0%
writer and then the writer talks back
 

00:03:02.000 --> 00:03:03.670 align:start position:0%
writer and then the writer talks back
and<00:03:02.120><c> says</c><00:03:02.480><c> okay</c><00:03:02.640><c> here's</c><00:03:02.840><c> the</c><00:03:03.040><c> content</c><00:03:03.400><c> parse</c>

00:03:03.670 --> 00:03:03.680 align:start position:0%
and says okay here's the content parse
 

00:03:03.680 --> 00:03:05.470 align:start position:0%
and says okay here's the content parse
and<00:03:03.840><c> formatted</c><00:03:04.280><c> markdown</c><00:03:04.840><c> ready</c><00:03:05.040><c> for</c><00:03:05.280><c> a</c>

00:03:05.470 --> 00:03:05.480 align:start position:0%
and formatted markdown ready for a
 

00:03:05.480 --> 00:03:07.509 align:start position:0%
and formatted markdown ready for a
newsletter<00:03:06.480><c> so</c><00:03:06.640><c> it</c><00:03:06.760><c> looks</c><00:03:06.959><c> like</c><00:03:07.080><c> it</c><00:03:07.239><c> kind</c><00:03:07.360><c> of</c>

00:03:07.509 --> 00:03:07.519 align:start position:0%
newsletter so it looks like it kind of
 

00:03:07.519 --> 00:03:09.630 align:start position:0%
newsletter so it looks like it kind of
has<00:03:07.599><c> a</c><00:03:07.879><c> title</c><00:03:08.599><c> and</c><00:03:08.760><c> I</c><00:03:08.840><c> wanted</c><00:03:09.040><c> to</c><00:03:09.159><c> do</c><00:03:09.400><c> three</c>

00:03:09.630 --> 00:03:09.640 align:start position:0%
has a title and I wanted to do three
 

00:03:09.640 --> 00:03:13.430 align:start position:0%
has a title and I wanted to do three
posts<00:03:10.280><c> on</c><00:03:10.519><c> open</c><00:03:10.879><c> AI</c><00:03:11.760><c> so</c><00:03:12.239><c> here</c><00:03:12.480><c> are</c><00:03:13.200><c> three</c>

00:03:13.430 --> 00:03:13.440 align:start position:0%
posts on open AI so here are three
 

00:03:13.440 --> 00:03:15.430 align:start position:0%
posts on open AI so here are three
different<00:03:13.720><c> posts</c><00:03:14.159><c> and</c><00:03:14.400><c> these</c><00:03:14.599><c> are</c><00:03:15.080><c> I</c><00:03:15.159><c> said</c><00:03:15.360><c> it</c>

00:03:15.430 --> 00:03:15.440 align:start position:0%
different posts and these are I said it
 

00:03:15.440 --> 00:03:18.309 align:start position:0%
different posts and these are I said it
to<00:03:15.560><c> be</c><00:03:15.680><c> the</c><00:03:15.840><c> newest</c><00:03:16.159><c> one</c><00:03:16.440><c> so</c><00:03:17.120><c> uh</c><00:03:17.280><c> as</c><00:03:17.400><c> of</c><00:03:17.680><c> now</c><00:03:18.200><c> you</c>

00:03:18.309 --> 00:03:18.319 align:start position:0%
to be the newest one so uh as of now you
 

00:03:18.319 --> 00:03:20.309 align:start position:0%
to be the newest one so uh as of now you
know<00:03:18.799><c> this</c><00:03:18.959><c> one</c><00:03:19.200><c> I</c><00:03:19.280><c> think</c><00:03:19.440><c> was</c><00:03:19.680><c> like</c><00:03:19.840><c> maybe</c><00:03:20.040><c> 40</c>

00:03:20.309 --> 00:03:20.319 align:start position:0%
know this one I think was like maybe 40
 

00:03:20.319 --> 00:03:22.390 align:start position:0%
know this one I think was like maybe 40
minutes<00:03:20.599><c> ago</c><00:03:21.440><c> um</c><00:03:21.599><c> so</c><00:03:21.720><c> let's</c><00:03:21.879><c> click</c><00:03:22.080><c> on</c><00:03:22.159><c> one</c><00:03:22.280><c> of</c>

00:03:22.390 --> 00:03:22.400 align:start position:0%
minutes ago um so let's click on one of
 

00:03:22.400 --> 00:03:23.630 align:start position:0%
minutes ago um so let's click on one of
these<00:03:22.560><c> let's</c><00:03:22.720><c> click</c><00:03:22.879><c> on</c><00:03:23.120><c> number</c><00:03:23.319><c> two</c>

00:03:23.630 --> 00:03:23.640 align:start position:0%
these let's click on number two
 

00:03:23.640 --> 00:03:26.630 align:start position:0%
these let's click on number two
suggestion<00:03:24.360><c> for</c><00:03:24.640><c> updated</c><00:03:25.040><c> Laws</c><00:03:25.480><c> of</c><00:03:25.879><c> Robotics</c>

00:03:26.630 --> 00:03:26.640 align:start position:0%
suggestion for updated Laws of Robotics
 

00:03:26.640 --> 00:03:29.550 align:start position:0%
suggestion for updated Laws of Robotics
by<00:03:27.280><c> Samuel</c><00:03:27.840><c> so</c><00:03:28.120><c> I</c><00:03:28.280><c> open</c><00:03:28.439><c> it</c><00:03:28.599><c> up</c><00:03:29.000><c> that</c><00:03:29.159><c> is</c><00:03:29.360><c> by</c>

00:03:29.550 --> 00:03:29.560 align:start position:0%
by Samuel so I open it up that is by
 

00:03:29.560 --> 00:03:31.509 align:start position:0%
by Samuel so I open it up that is by
hand<00:03:29.879><c> it</c><00:03:30.000><c> was</c><00:03:30.200><c> actually</c><00:03:30.439><c> 20</c><00:03:30.760><c> this</c><00:03:30.840><c> one</c><00:03:31.000><c> was</c><00:03:31.159><c> 28</c>

00:03:31.509 --> 00:03:31.519 align:start position:0%
hand it was actually 20 this one was 28
 

00:03:31.519 --> 00:03:33.509 align:start position:0%
hand it was actually 20 this one was 28
minutes<00:03:31.840><c> ago</c><00:03:32.439><c> and</c><00:03:32.640><c> here</c><00:03:32.840><c> is</c><00:03:33.040><c> the</c><00:03:33.200><c> full</c>

00:03:33.509 --> 00:03:33.519 align:start position:0%
minutes ago and here is the full
 

00:03:33.519 --> 00:03:35.309 align:start position:0%
minutes ago and here is the full
description<00:03:33.920><c> so</c><00:03:34.040><c> if</c><00:03:34.120><c> it</c><00:03:34.239><c> was</c><00:03:34.319><c> a</c><00:03:34.519><c> newsletter</c><00:03:35.200><c> it</c>

00:03:35.309 --> 00:03:35.319 align:start position:0%
description so if it was a newsletter it
 

00:03:35.319 --> 00:03:37.910 align:start position:0%
description so if it was a newsletter it
gives<00:03:35.720><c> part</c><00:03:35.879><c> of</c><00:03:36.000><c> it</c><00:03:36.439><c> and</c><00:03:36.599><c> then</c><00:03:36.799><c> says</c><00:03:37.319><c> read</c><00:03:37.599><c> more</c>

00:03:37.910 --> 00:03:37.920 align:start position:0%
gives part of it and then says read more
 

00:03:37.920 --> 00:03:39.149 align:start position:0%
gives part of it and then says read more
here<00:03:38.080><c> and</c><00:03:38.200><c> you</c><00:03:38.280><c> can</c><00:03:38.439><c> click</c><00:03:38.640><c> on</c><00:03:38.760><c> the</c><00:03:38.840><c> link</c><00:03:39.040><c> to</c>

00:03:39.149 --> 00:03:39.159 align:start position:0%
here and you can click on the link to
 

00:03:39.159 --> 00:03:40.710 align:start position:0%
here and you can click on the link to
read<00:03:39.360><c> the</c><00:03:39.519><c> rest</c><00:03:39.680><c> of</c><00:03:39.799><c> it</c><00:03:40.040><c> here</c><00:03:40.159><c> I</c><00:03:40.280><c> just</c><00:03:40.400><c> copi</c><00:03:40.599><c> and</c>

00:03:40.710 --> 00:03:40.720 align:start position:0%
read the rest of it here I just copi and
 

00:03:40.720 --> 00:03:43.630 align:start position:0%
read the rest of it here I just copi and
paste<00:03:40.920><c> it</c><00:03:41.120><c> into</c><00:03:41.760><c> uh</c><00:03:41.959><c> Reddit</c><00:03:42.640><c> MD</c><00:03:43.120><c> file</c><00:03:43.360><c> for</c>

00:03:43.630 --> 00:03:43.640 align:start position:0%
paste it into uh Reddit MD file for
 

00:03:43.640 --> 00:03:45.390 align:start position:0%
paste it into uh Reddit MD file for
markdown<00:03:44.640><c> and</c><00:03:44.840><c> here's</c><00:03:45.080><c> actually</c><00:03:45.239><c> what</c><00:03:45.319><c> it</c>

00:03:45.390 --> 00:03:45.400 align:start position:0%
markdown and here's actually what it
 

00:03:45.400 --> 00:03:47.390 align:start position:0%
markdown and here's actually what it
looks<00:03:45.560><c> like</c><00:03:45.760><c> formatted</c><00:03:46.280><c> which</c><00:03:46.720><c> looks</c><00:03:47.080><c> nice</c>

00:03:47.390 --> 00:03:47.400 align:start position:0%
looks like formatted which looks nice
 

00:03:47.400 --> 00:03:51.190 align:start position:0%
looks like formatted which looks nice
right<00:03:47.480><c> so</c><00:03:47.640><c> it</c><00:03:47.799><c> has</c><00:03:48.599><c> um</c><00:03:49.080><c> each</c><00:03:49.920><c> each</c><00:03:50.120><c> one</c><00:03:50.480><c> here</c>

00:03:51.190 --> 00:03:51.200 align:start position:0%
right so it has um each each one here
 

00:03:51.200 --> 00:03:52.670 align:start position:0%
right so it has um each each one here
and<00:03:51.319><c> then</c><00:03:51.439><c> it</c><00:03:51.560><c> has</c><00:03:51.720><c> the</c><00:03:51.879><c> author</c><00:03:52.360><c> and</c><00:03:52.439><c> then</c><00:03:52.560><c> you</c>

00:03:52.670 --> 00:03:52.680 align:start position:0%
and then it has the author and then you
 

00:03:52.680 --> 00:03:54.630 align:start position:0%
and then it has the author and then you
can<00:03:52.920><c> choose</c><00:03:53.519><c> uh</c><00:03:53.640><c> it</c><00:03:53.799><c> creates</c><00:03:54.040><c> a</c><00:03:54.159><c> link</c><00:03:54.360><c> for</c><00:03:54.519><c> you</c>

00:03:54.630 --> 00:03:54.640 align:start position:0%
can choose uh it creates a link for you
 

00:03:54.640 --> 00:03:56.670 align:start position:0%
can choose uh it creates a link for you
to<00:03:54.879><c> read</c><00:03:55.159><c> more</c><00:03:55.519><c> about</c><00:03:55.760><c> it</c><00:03:56.000><c> I</c><00:03:56.079><c> didn't</c><00:03:56.280><c> even</c><00:03:56.400><c> do</c><00:03:56.560><c> a</c>

00:03:56.670 --> 00:03:56.680 align:start position:0%
to read more about it I didn't even do a
 

00:03:56.680 --> 00:03:58.350 align:start position:0%
to read more about it I didn't even do a
whole<00:03:56.920><c> lot</c><00:03:57.079><c> with</c><00:03:57.200><c> the</c><00:03:57.319><c> prompt</c><00:03:57.640><c> I</c><00:03:57.720><c> just</c><00:03:57.799><c> created</c>

00:03:58.350 --> 00:03:58.360 align:start position:0%
whole lot with the prompt I just created
 

00:03:58.360 --> 00:04:00.350 align:start position:0%
whole lot with the prompt I just created
a<00:03:58.480><c> kind</c><00:03:58.599><c> of</c><00:03:58.680><c> a</c><00:03:58.799><c> simple</c><00:03:59.079><c> outline</c><00:03:59.400><c> for</c><00:03:59.799><c> wanted</c>

00:04:00.350 --> 00:04:00.360 align:start position:0%
a kind of a simple outline for wanted
 

00:04:00.360 --> 00:04:01.949 align:start position:0%
a kind of a simple outline for wanted
the<00:04:00.480><c> newsletter</c><00:04:00.920><c> to</c><00:04:01.120><c> look</c><00:04:01.360><c> like</c><00:04:01.680><c> and</c><00:04:01.799><c> it</c>

00:04:01.949 --> 00:04:01.959 align:start position:0%
the newsletter to look like and it
 

00:04:01.959 --> 00:04:04.069 align:start position:0%
the newsletter to look like and it
created<00:04:02.319><c> this</c><00:04:02.439><c> for</c><00:04:02.680><c> me</c><00:04:02.959><c> which</c><00:04:03.120><c> is</c><00:04:03.360><c> pretty</c><00:04:03.599><c> cool</c>

00:04:04.069 --> 00:04:04.079 align:start position:0%
created this for me which is pretty cool
 

00:04:04.079 --> 00:04:05.030 align:start position:0%
created this for me which is pretty cool
okay<00:04:04.239><c> I</c><00:04:04.360><c> hope</c><00:04:04.480><c> you</c><00:04:04.599><c> thought</c><00:04:04.760><c> that</c><00:04:04.840><c> was</c>

00:04:05.030 --> 00:04:05.040 align:start position:0%
okay I hope you thought that was
 

00:04:05.040 --> 00:04:06.429 align:start position:0%
okay I hope you thought that was
interesting<00:04:05.519><c> I</c><00:04:05.599><c> certainly</c><00:04:05.920><c> thought</c><00:04:06.079><c> it</c><00:04:06.200><c> did</c>

00:04:06.429 --> 00:04:06.439 align:start position:0%
interesting I certainly thought it did
 

00:04:06.439 --> 00:04:08.229 align:start position:0%
interesting I certainly thought it did
you<00:04:06.519><c> know</c><00:04:06.640><c> it</c><00:04:06.720><c> took</c><00:04:06.920><c> me</c><00:04:07.200><c> less</c><00:04:07.400><c> than</c><00:04:07.799><c> 2</c><00:04:07.959><c> minutes</c>

00:04:08.229 --> 00:04:08.239 align:start position:0%
you know it took me less than 2 minutes
 

00:04:08.239 --> 00:04:10.190 align:start position:0%
you know it took me less than 2 minutes
to<00:04:08.519><c> create</c><00:04:08.879><c> actually</c><00:04:09.079><c> the</c><00:04:09.200><c> Reddit</c><00:04:09.599><c> app</c><00:04:10.040><c> get</c>

00:04:10.190 --> 00:04:10.200 align:start position:0%
to create actually the Reddit app get
 

00:04:10.200 --> 00:04:11.830 align:start position:0%
to create actually the Reddit app get
the<00:04:10.400><c> ID</c><00:04:10.680><c> and</c><00:04:10.840><c> secret</c><00:04:11.159><c> key</c><00:04:11.400><c> and</c><00:04:11.519><c> then</c><00:04:11.640><c> you</c><00:04:11.720><c> can</c>

00:04:11.830 --> 00:04:11.840 align:start position:0%
the ID and secret key and then you can
 

00:04:11.840 --> 00:04:13.270 align:start position:0%
the ID and secret key and then you can
use<00:04:12.040><c> that</c><00:04:12.159><c> as</c><00:04:12.280><c> the</c><00:04:12.400><c> Lang</c><00:04:12.680><c> chain</c><00:04:12.920><c> Reddit</c>

00:04:13.270 --> 00:04:13.280 align:start position:0%
use that as the Lang chain Reddit
 

00:04:13.280 --> 00:04:14.869 align:start position:0%
use that as the Lang chain Reddit
document<00:04:13.640><c> loader</c><00:04:14.079><c> and</c><00:04:14.159><c> then</c><00:04:14.280><c> once</c><00:04:14.400><c> we</c><00:04:14.560><c> load</c>

00:04:14.869 --> 00:04:14.879 align:start position:0%
document loader and then once we load
 

00:04:14.879 --> 00:04:17.390 align:start position:0%
document loader and then once we load
those<00:04:15.120><c> documents</c><00:04:15.920><c> from</c><00:04:16.320><c> whatever</c><00:04:16.720><c> subreddits</c>

00:04:17.390 --> 00:04:17.400 align:start position:0%
those documents from whatever subreddits
 

00:04:17.400 --> 00:04:19.349 align:start position:0%
those documents from whatever subreddits
and<00:04:17.880><c> many</c><00:04:18.079><c> posts</c><00:04:18.359><c> that</c><00:04:18.479><c> you</c><00:04:18.639><c> want</c><00:04:18.959><c> we</c><00:04:19.079><c> can</c><00:04:19.199><c> have</c>

00:04:19.349 --> 00:04:19.359 align:start position:0%
and many posts that you want we can have
 

00:04:19.359 --> 00:04:21.430 align:start position:0%
and many posts that you want we can have
agents<00:04:19.680><c> format</c><00:04:20.359><c> that</c><00:04:20.519><c> into</c><00:04:20.720><c> a</c><00:04:20.840><c> way</c><00:04:21.040><c> we</c><00:04:21.120><c> want</c><00:04:21.280><c> it</c>

00:04:21.430 --> 00:04:21.440 align:start position:0%
agents format that into a way we want it
 

00:04:21.440 --> 00:04:22.430 align:start position:0%
agents format that into a way we want it
think<00:04:21.560><c> there</c><00:04:21.680><c> a</c><00:04:21.799><c> lot</c><00:04:21.959><c> there</c><00:04:22.040><c> are</c><00:04:22.120><c> a</c><00:04:22.199><c> lot</c><00:04:22.320><c> of</c>

00:04:22.430 --> 00:04:22.440 align:start position:0%
think there a lot there are a lot of
 

00:04:22.440 --> 00:04:24.030 align:start position:0%
think there a lot there are a lot of
possibilities<00:04:23.000><c> here</c><00:04:23.440><c> but</c><00:04:23.600><c> what</c><00:04:23.680><c> would</c><00:04:23.919><c> you</c>

00:04:24.030 --> 00:04:24.040 align:start position:0%
possibilities here but what would you
 

00:04:24.040 --> 00:04:26.150 align:start position:0%
possibilities here but what would you
use<00:04:24.199><c> it</c><00:04:24.360><c> for</c><00:04:24.680><c> what</c><00:04:24.800><c> ideas</c><00:04:25.199><c> do</c><00:04:25.320><c> you</c><00:04:25.520><c> have</c><00:04:25.960><c> leave</c>

00:04:26.150 --> 00:04:26.160 align:start position:0%
use it for what ideas do you have leave
 

00:04:26.160 --> 00:04:27.790 align:start position:0%
use it for what ideas do you have leave
them<00:04:26.280><c> in</c><00:04:26.360><c> the</c><00:04:26.479><c> comment</c><00:04:26.759><c> section</c><00:04:27.120><c> down</c><00:04:27.280><c> below</c>

00:04:27.790 --> 00:04:27.800 align:start position:0%
them in the comment section down below
 

00:04:27.800 --> 00:04:29.430 align:start position:0%
them in the comment section down below
tell<00:04:27.919><c> me</c><00:04:28.120><c> what</c><00:04:28.199><c> you</c><00:04:28.400><c> think</c><00:04:29.080><c> thank</c><00:04:29.240><c> you</c><00:04:29.320><c> for</c>

00:04:29.430 --> 00:04:29.440 align:start position:0%
tell me what you think thank you for
 

00:04:29.440 --> 00:04:33.160 align:start position:0%
tell me what you think thank you for
watching<00:04:30.039><c> and</c><00:04:30.160><c> I'll</c><00:04:30.320><c> see</c><00:04:30.440><c> you</c><00:04:30.639><c> next</c><00:04:30.840><c> video</c>

